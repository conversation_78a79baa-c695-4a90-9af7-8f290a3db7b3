SDK Documentation
View on GitHub

Lua
Libraries
Functions
Math
vec2
vec3
vec4
seg2
mathf
clipper
Libraries
hanbot
cb
chat
console
minimap
ping
navmesh
game
graphics
shadereffect
objManager
core
sound
shop
memory
input
keyboard
permashow
network
module
menu
md5
Objects
base.obj
hero.obj
minion.obj
turret.obj
inhib.obj
nexus.obj
missile.obj
particle.obj
spell.obj
spell_slot.obj
spell_info.obj
spell_static.obj
inventory_slot.obj
item_data.obj
path.obj
buff.obj
runemanager.obj
rune.obj
camp.obj
texture.obj
shadereffect.obj
skillshot.obj (Evade3)
spelldata.obj (Evade3)
cast_info_saved.obj (Evade3)
Modules
pred
orb
evade
damagelib
TS
Globals
Examples
Callbacks
Creating Shards
Avoiding Bugsplats
Performance Tips
Paid Script Conditions


Lua
Libraries
math
string
table
os
bit
The os library has been restricted to the following functions: clock, time, date

Functions
assert
print
tostring
tonumber
pairs
ipairs
unpack
type
setmetatable
loadfile
loadstring
delayaction(fn, delay, params)
delayaction(
    function(a1, a2, a3)
        print(a1, a2, a3)
    end, 
    0.5, 
    { 1, 2, 3 }
)
Math
vec2
vec2(number, number)
vec2(vec2)

local a = vec2(player.x, player.z)
local b = vec2(a)
a:print()
b:print()
v1:dot(v2)
Parameters
vec2 v1
vec2 v2
Return Value
number returns dot product

local a = player.pos2D:dot(mousePos2D)
print(a)
v1:cross(v2)
Parameters
vec2 v1
vec2 v2
Return Value
number returns cross product

local a = player.pos2D:cross(mousePos2D)
print(a)
v1:len()
Parameters
vec2 v1
Return Value
number returns length of v1

local a = mousePos2D - player.pos2D
local b = a:len()
print(b)
v1:lenSqr()
Parameters
vec2 v1
Return Value
number returns squared length of v1

local a = mousePos2D - player.pos2D
local b = a:lenSqr()
print(b)
v1:norm()
Parameters
vec2 v1
Return Value
vec2 returns normalized v1

local a = mousePos2D - player.pos2D
local b = a:norm()
b:print()
v1:extend(to, range)
Parameters
vec2 to
number range
Return Value
vec2 returns extended vec

local a = player.pos2D:extend(mousePos2D, 100)
a:print()
v1:dist(v2)
Parameters
vec2 v1
vec2 v2
Return Value
number returns distance between v1 and v2

local a = mousePos2D:dist(player.pos2D)
print(a)
v1:distSqr(v2)
Parameters
vec2 v1
vec2 v2
Return Value
number returns squared distance between v1 and v2

local a = mousePos2D:distSqr(player.pos2D)
print(a)
v1:distLine(A, B)
Parameters
vec2 v1
vec2 A
vec2 B
Return Value
number returns distance between v1 and line: A to B

v1:inRange(v2, range)
Parameters
vec2 v1
vec2 v2
number range
Return Value
number returns whether v2 in area from v1 to range

v1:isOnLineSegment(a, b)
Parameters
vec2 v1
vec2 a
vec2 b
number range
Return Value
number returns whether v1 in line segment

v1:projectOnLine(a, b)
Parameters
vec2 v1
vec2 a
vec2 b
number range
Return Value
number returns project result in line

v1:projectOnLineSegment(a, b)
Parameters
vec2 v1
vec2 a
vec2 b
number range
Return Value
number returns project result in line segment

v1:angle(v2)
Parameters
vec2 v1
vec2 v2
Return Value
number returns angle (radians) between v1 and v2

v1:angleDeg(v2)
Parameters
vec2 v1
vec2 v2
Return Value
number returns angle (degrees) between v1 and v2

v1:perp1()
Parameters
vec2 v1
Return Value
vec2 returns perpendicular (left) vec2 to v1

local a = mousePos2D-player.pos2D
local b = a:norm()
local c = b:perp1()
c:print()
v1:perp2()
Parameters
vec2 v1
Return Value
vec2 returns perpendicular (right) vec2 to v1

local a = mousePos2D-player.pos2D
local b = a:norm()
local c = b:perp2()
c:print()
v1:lerp(v2, s)
Parameters
vec2 v1
vec2 v2
number s
Return Value
vec2 returns interpolated vec2 between v1 and v2 by factor s

local a = player.pos2D:lerp(mousePos2D, 0.5)
a:print()
v1:clone()
Parameters
vec2 v1
Return Value
vec2 returns cloned v1

local a = player.pos2D:clone()
a:print()
v1:to3D(y)
Parameters
vec2 v1
number y
Return Value
vec3 returns vec3

local a = vec2(player.x, player.z)
local b = a:to3D(mousePos.y)
b:print()
v1:toGame3D()
Parameters
vec2 v1
Return Value
vec3 returns vec3, y is set to world height for exact position

v1:rotate(s)
Parameters
vec2 v1
number s
Return Value
vec2 returns vec2 rotated by s radians

local a = (mousePos2D-player.pos2D)
local b = a:norm()
local c = b:rotate(0.785398)
c:print()
v1:rotateDeg(angle)
Parameters
vec2 v1
number angle
Return Value
vec2 returns vec2 rotated by s degrees

v1:countAllies(range)
Parameters
vec2 v1
number range
Return Value
number

v1:countEnemies(range)
Parameters
vec2 v1
number range
Return Value
number

v1:countAllyLaneMinions(range)
Parameters
vec2 v1
number range
Return Value
number

v1:countEnemyLaneMinions(range)
Parameters
vec2 v1
number range
Return Value
number

v1:isUnderEnemyTurret(range)
Parameters
vec2 v1
number range, extra range, optional
Return Value
boolean

v1:isUnderAllyTurret(range)
Parameters
vec2 v1
number range, extra range, optional
Return Value
boolean

v1:print()
Parameters
vec2 v1
Return Value
void

local a = vec2(mousePos.x, mousePos.z)
a:print()
vec2.array(n)
Parameters
number n
Return Value
vec2[?] returns vec2 array of length n

local a = vec2.array(12)
a[0].x = 100
a[0].y = 200
a[0]:print()
vec3
vec3(number, number, number)
vec3(vec3)

local a = vec3(player.x, player.y, player.z)
local b = vec3(a)
a:print()
b:print()
v1:dot(v2)
Parameters
vec3 v1
vec3 v2
Return Value
number returns dot product

local a = player.pos:dot(mousePos)
print(a)
v1:cross(v2)
Parameters
vec3 v1
vec3 v2
Return Value
number returns cross product

local a = player.pos:cross(mousePos)
print(a)
v1:len()
Parameters
vec3 v1
Return Value
number returns length of v1

local a = mousePos - player.pos
local b = a:len()
print(b)
v1:lenSqr()
Parameters
vec3 v1
Return Value
number returns squared length of v1

local a = mousePos - player.pos
local b = a:lenSqr()
print(b)
v1:norm()
Parameters
vec3 v1
Return Value
vec3 returns normalized v1

local a = mousePos - player.pos
local b = a:norm()
b:print()
v1:extend(to, range)
Parameters
vec3 to
number range
Return Value
vec3 returns extended vec

local a = player.pos:extend(mousePos, 100)
a:print()
v1:dist(v2)
Parameters
vec3 v1
vec3 v2
Return Value
number returns distance between v1 and v2

local a = mousePos:dist(player.pos)
print(a)
v1:distSqr(v2)
Parameters
vec3 v1
vec3 v2
Return Value
number returns squared distance between v1 and v2

local a = mousePos:distSqr(player.pos)
print(a)
v1:inRange(v2, range)
Parameters
vec3 v1
vec3 v2
number range
Return Value
number returns whether v2 in area from v1 to range

v1:angle(v2)
Parameters
vec3 v1
vec3 v2
Return Value
number returns angle (deg) between v1 and v2

v1:perp1()
Parameters
vec3 v1
Return Value
vec3 returns perpendicular (left) vec3 to v1

local a = mousePos-player.pos
local b = a:norm()
local c = b:perp1()
c:print()
v1:perp2()
Parameters
vec3 v1
Return Value
vec3 returns perpendicular (right) vec3 to v1

local a = mousePos-player.pos
local b = a:norm()
local c = b:perp2()
c:print()
v1:lerp(v2, s)
Parameters
vec3 v1
vec3 v2
number s
Return Value
vec3 returns interpolated vec3 between v1 and v2 by factor s

local a = player.pos:lerp(mousePos, 0.5)
a:print()
v1:clone()
Parameters
vec3 v1
Return Value
vec3 returns cloned v1

local a = player.pos:clone()
a:print()
v1:to2D()
Parameters
vec3 v1
Return Value
vec2 returns vec2 from x and z properties

local a = vec3(player.x, player.y, player.z)
local b = a:to2D()
b:print()
v1:rotate(s)
Parameters
vec3 v1
number s
Return Value
vec3 returns vec3 rotated by s radians

local a = (mousePos-player.pos)
local b = a:norm()
local c = b:rotate(0.785398)
c:print()
v1:countAllies(range)
Parameters
vec3 v1
number range
Return Value
number

v1:countEnemies(range)
Parameters
vec3 v1
number range
Return Value
number

v1:countAllyLaneMinions(range)
Parameters
vec3 v1
number range
Return Value
number

v1:countEnemyLaneMinions(range)
Parameters
vec3 v1
number range
Return Value
number

v1:isUnderEnemyTurret(range)
Parameters
vec3 v1
number range, extra range, optional
Return Value
boolean

v1:isUnderAllyTurret(range)
Parameters
vec3 v1
number range, extra range, optional
Return Value
boolean

v1:print()
Parameters
vec3 v1
Return Value
void

local a = vec3(mousePos.x, mousePos.y, mousePos.z)
a:print()
vec3.array(n)
Parameters
number n
Return Value
vec3[?] returns vec3 array of length n

local a = vec3.array(12)
a[0].x = 100
a[0].y = 50
a[0].z = 200
a[0]:print()
vec4
vec4(number, number, number)
vec4(vec4)

v1:dot(v2)
Parameters
vec4 v1
vec4 v2
Return Value
number returns dot product

v1:cross(v2)
Parameters
vec4 v1
vec4 v2
Return Value
number returns cross product

v1:len()
Parameters
vec4 v1
Return Value
number returns length of v1

v1:lenSqr()
Parameters
vec4 v1
Return Value
number returns squared length of v1

v1:norm()
Parameters
vec4 v1
Return Value
vec4 returns normalized v1

v1:dist(v2)
Parameters
vec4 v1
vec4 v2
Return Value
number returns distance between v1 and v2

v1:distSqr(v2)
Parameters
vec4 v1
vec4 v2
Return Value
number returns squared distance between v1 and v2

v1:perp1()
Parameters
vec4 v1
Return Value
vec4 returns perpendicular (left) vec4 to v1

v1:perp2()
Parameters
vec4 v1
Return Value
vec4 returns perpendicular (right) vec4 to v1

v1:lerp(v2, s)
Parameters
vec4 v1
vec4 v2
number s
Return Value
vec4 returns interpolated vec4 between v1 and v2 by factor s

v1:clone()
Parameters
vec4 v1
Return Value
vec4 returns cloned v1

v1:to2D()
Parameters
vec4 v1
Return Value
vec2 returns vec2 from x and z properties

v1:rotate(s)
Parameters
vec4 v1
number s
Return Value
vec4 returns vec4 rotated by s radians

v1:print()
Parameters
vec4 v1
Return Value
void

vec4.array(n)
Parameters
number n
Return Value
vec4[?] returns vec4 array of length n

seg2
vec2 seg2.startPos
vec2 seg2.endPos

v1:len()
Parameters
seg2 v1
Return Value
number returns length of v1

v1:lenSqr()
Parameters
seg2 v1
Return Value
number returns squared length of v1

mathf
A library of commonly used 2D math functions.

mathf.PI
Return Value
number returns pi

print(mathf.PI)
mathf.cos(n)
Parameters
number n
Return Value
number returns the cosine of n

local a = mathf.cos(mathf.PI)
print(a)
mathf.sin(n)
Parameters
number n
Return Value
number returns the sine of n

local a = mathf.sin(mathf.PI)
print(a)
mathf.round(n, d)
Parameters
number n
number d
Return Value
number returns rounded n to precision d

local a = mathf.round(1.234567, 3)
print(a)
mathf.sqr(n)
Parameters
number n
Return Value
number returns squared value of n

local a = mathf.sqr(2)
print(a)
mathf.clamp(min, max, n)
Parameters
number min
number max
number n
Return Value
number returns clamped n between min and max

local a = mathf.clamp(0, 25, -1)
print(a)
mathf.sect_line_line(v1, v3, v3, v4)
Parameters
vec2 v1
vec2 v2
vec2 v3
vec2 v4
Return Value
vec2 returns intersection point of lines (v1, v2) and (v3, v4)

local v1 = vec2(0, 0)
local v2 = vec2(100,100)
local v3 = vec2(100, 0)
local v4 = vec2(0,100)
local a = mathf.sect_line_line(v1, v2, v3, v4)
a:print()
mathf.dist_line_vector(v1, v2, v3)
Parameters
vec2 v1
vec2 v2
vec2 v3
Return Value
number returns distance between v1 and line (v2, v3)

local v1 = vec2(0, 0)
local v2 = vec2(100, 0)
local v3 = vec2(0,100)
local a = mathf.dist_line_vector(v1, v2, v3)
print(a)
mathf.angle_between(v1, v2, v3)
Parameters
vec2 v1
vec2 v2
vec2 v3
Return Value
number returns the angle between v2 and v3 from origin v1 in radians

local v1 = vec2(0, 0)
local a = mathf.angle_between(v1, player.pos2D, mousePos2D)
print(a)
mathf.mec(pts, n)
Parameters
vec2[] pts
number n
Return Value
vec2 returns the center of the minimum enclosing circle
number returns the radius of the minimum enclosing circle

local pts = vec2.array(2)
pts[0].x, pts[0].y = 0, 0
pts[1].x, pts[1].y = mousePos.x, mousePos.z
pts[2].x, pts[2].y = player.x, player.z
local c, n = mathf.mec(pts, 2)
print(c.x, c.y, n)
mathf.project(v1, v2, v3, s1, s2)
Parameters
vec2 v1
vec2 v2
vec2 v3
number s1
number s2
Return Value
vec2 returns the collision point
number returns the time until collision occurs

--calculates collision position and time of a projectile from mousePos along the players path
local src = mousePos2D
local pos_s = player.path.serverPos2D
local pos_e = player.path.point2D[player.path.index]
local s1 = 2000
local s2 = player.moveSpeed

local res, res_t = mathf.project(src, pos_s, pos_e, s1, s2)
if res then
    print(res.x, res.y, res_t)
end
mathf.dist_seg_seg(v1, v2, v3, v4)
Parameters
vec2 v1
vec2 v2
vec2 v3
vec2 v4
Return Value
number returns the distance between line segments (v1, v2) and (v3, v4)

local v1 = vec2(0,0)
local v2 = vec2(1000,1000)
local v3 = mousePos2D
local v4 = player.pos2D
local res = mathf.dist_seg_seg(v1, v2, v3, v4)
print(res)
mathf.closest_vec_line(v1, v2, v3)
Parameters
vec2 v1
vec2 v2
vec2 v3
Return Value
vec2 returns a vec2 along the line (v2, v3) closest to v1

local v1 = player.pos2D
local v2 = vec2(0,0)
local v3 = mousePos2D
local res = mathf.closest_vec_line(v1, v2, v3)
res:print()
mathf.closest_vec_line_seg(v1, v2, v3)
Parameters
vec2 v1
vec2 v2
vec2 v3
Return Value
vec2 returns a vec2 along the line segment (v2, v3) closest to v1

local v1 = player.pos2D
local v2 = vec2(0,0)
local v3 = mousePos2D
local res = mathf.closest_vec_line_seg(v1, v2, v3)
if res then
    res:print()
end
mathf.col_vec_rect(v1, v2, v3, w1, w2)
Parameters
vec2 v1
vec2 v2
vec2 v3
number w1
number w2
Return Value
boolean returns true if v1 with bounding radius of w1 collides with line (v2, v3) of width w2

local v1 = player.pos2D
local v2 = vec2(0, 0)
local v3 = mousePos2D
local w1 = player.boundingRadius
local w2 = 100
local res = mathf.col_vec_rect(v1, v2, v3, w1, w2)
print(res)
mathf.sect_circle_circle(v1, r1, v2, r2)
Parameters
vec2 v1
number r1
vec2 v2
number r2
Return Value
vec2 returns first intersection of circles at v1 with radius of r1 and v2 with radius of r2
vec2 returns second intersection of circles at v1 with radius of r1 and v2 with radius of r2

local v1 = mousePos2D
local r1 = 500
local v2 = player.pos2D
local r2 = player.attackRange
local res1, res2 = mathf.sect_circle_circle(v1, r1, v2, r2)
if res1 then
    res1:print()
    res2:print()
end
mathf.sect_line_circle(v1, v2, v3, r)
Parameters
vec2 v1
vec2 v2
vec2 v3
number r
Return Value
vec2 returns first intersection of line (v1, v2) and circle v3 with radius of r
vec2 returns second intersection of line (v1, v2) and circle v3 with radius of r

local v1 = vec2(0, 0)
local v2 = mousePos2D
local v3 = player.pos2D
local r = player.attackRange
local res1, res2 = mathf.sect_line_circle(v1, v2, v3, r)
if res1 then
    res1:print()
end
if res2 then
    res2:print()
end
mathf.mat2()
Return Value
double[2][2] returns a 2x2 transformation matrix

mathf.mat3()
Return Value
double[3][3] returns a 3x3 transformation matrix

mathf.mat4()
Return Value
double[4][4] returns a 4x4 transformation matrix

clipper
Unlike the other math libraries, clipper must first be loaded.
Further documentation can be found here.

local clip = module.internal('clipper')
local polygon = clip.polygon
local polygons = clip.polygons
local clipper = clip.clipper
local clipper_enum = clip.enum
Enums:

clipper_enum.PolyFillType.EvenOdd
clipper_enum.PolyFillType.NonZero
clipper_enum.PolyFillType.Positive
clipper_enum.PolyFillType.Negative
clipper_enum.JoinType.Square
clipper_enum.JoinType.Round
clipper_enum.JoinType.Miter
clipper_enum.ClipType.Intersection
clipper_enum.ClipType.Union
clipper_enum.ClipType.Difference
clipper_enum.ClipType.Xor
clipper_enum.PolyType.Subject
clipper_enum.PolyType.Clip
polygon:Add(v1)
Parameters
vec2 v1
Return Value
void

local p = polygon()
local v = vec2(200, 200)
p:Add(v)
polygon:ChildCount()
Return Value
number returns number of vertices

local p = polygon(vec2(200, 200), vec2(200, 100), vec2(100, 200))

print(p:ChildCount())
polygon:Childs(i)
Parameters
number i
Return Value
vec2 returns vec2 at vertex i

local p = polygon(vec2(200, 200), vec2(200, 100), vec2(100, 200))

local v = p:Childs(1)
v:print()
polygon:Area()
Return Value
number returns the area of polygon

local p = polygon(vec2(200, 200), vec2(200, 100), vec2(100, 200))

print(p:Area())
polygon:Clean(dist)
Parameters
number dist
Return Value
void

local p = polygon(vec2(200, 200), vec2(200, 100), vec2(200, 101), vec2(100, 200))

print(p:ChildCount())
p:Clean(5)
print(p:ChildCount())
polygon:Simplify(PolyFillType)
Parameters
number PolyFillType
Return Value
polygons returns polygon set

local p1 = polygon(vec2(5,62), vec2(164,62), vec2(36,157), vec2(85, 4), vec2(134, 158))
local p = p1:Simplify(clipper_enum.PolyFillType.NonZero)
local p2 = p:Childs(0)

cb.add(cb.draw, function()
    p2:Draw2D(5, 0xFF00FFFF)
    p1:Draw2D(2, 0xFFFF00FF)
end)
polygon:Orientation()
Return Value
number returns 1 if polygon has clockwise orientation

polygon:Reverse()
Return Value
void reverses polygons orientation

polygon:Contains(v1)
Parameters
vec2\vec3 v1
Return Value
number returns 1 if v1 is inside of polygon, 0 if outside, -1 if v1 is on polygon edge

local p1 = polygon(vec2(200,200), vec2(200,300), vec2(300,300), vec2(300, 200))
cb.add(cb.draw, function()
    p1:Draw2D(2, p1:Contains(game.cursorPos)==1 and 0xFF00FF00 or 0xFFFF0000)
end)
polygon:Draw2D(width, color)
Parameters
number width
number color
Return Value
void

polygon:Draw3D(y, width, color)
Parameters
number y
number width
number color
Return Value
void

polygon:OnScreen2D()
Return Value
boolean returns true if polygon intersects the screen

polygon:OnScreen3D(y)
Parameters
number y
Return Value
boolean returns true if polygon intersects the screen

polygons:Add(polygon)
Parameters
polygon polygon
Return Value
void

polygons:ChildCount()
Return Value
number returns number of polygons contained polygon set

polygons:Childs(i)
Parameters
number i
Return Value
polygon returns polygon at index i

polygons:Reverse()
Return Value
void reverses the orientation of all containing polygons

polygons:Clean(dist)
Parameters
number dist
Return Value
void cleans all contained polygons

polygons:Simplify(PolyFillType)
Parameters
number PolyFillType
Return Value
polygons returns polygon set

polygons:Offset(delta, JoinType, limit)
Parameters
number delta
number JoinType
number limit
Return Value
polygons returns polygon set

clipper:AddPath(polygon, PolyType, closed)
Parameters
polygon polygon
number PolyType
boolean closed
Return Value
void

clipper:AddPaths(polygons, PolyType, closed)
Parameters
polygons polygons
number PolyType
boolean closed
Return Value
void

clipper:Clear()
Return Value
void

clipper:Execute(ClipType, PolyFillType, PolyFillType)
Parameters
number ClipType
number PolyFillType
number PolyFillType
Return Value
polygons returns polygon set

-- Example to calc DariusQ Outer Ring

local clip = module.internal('clipper')
local polygon = clip.polygon
local polygons = clip.polygons
local clipper = clip.clipper
local clipper_enum = clip.enum

local function create_ring(center, outer_radius, inner_radius)
    local outer_ring = polygon()
    local inner_ring = polygon()
    for i = 1, 64 do
        local angle = (i - 1) * (2 * math.pi / 64)
        local x_outer = center.pos.x + outer_radius * math.cos(angle)
        local y_outer = center.pos.z + outer_radius * math.sin(angle)
        local x_inner = center.pos.x + inner_radius * math.cos(angle)
        local y_inner = center.pos.z + inner_radius * math.sin(angle)
        outer_ring:Add(vec2(x_outer, y_outer))
        inner_ring:Add(vec2(x_inner, y_inner))
    end

    local clpr = clipper()
    clpr:AddPath(outer_ring, clipper_enum.PolyType.Subject, true)
    clpr:AddPath(inner_ring, clipper_enum.PolyType.Clip, true)
    local ring_area = clpr:Execute(clipper_enum.ClipType.Difference, clipper_enum.PolyFillType.NonZero, clipper_enum.PolyFillType.EvenOdd)
    return ring_area
end

-- Darius Outer ring Q, Find hit areas
local function on_draw()
    local centers = {}
    for i=0, objManager.enemies_n-1 do
        local obj = objManager.enemies[i]
        if obj and obj:isValidTarget(850) then
            table.insert(centers, obj)
        end
    end

    if #centers >= 2 then
        local clpr = clipper()
        local first_ring = create_ring(centers[1], 460, 250)
        local second_ring = create_ring(centers[2], 460, 250)

        clpr:AddPaths(first_ring, clipper_enum.PolyType.Subject, true)
        clpr:AddPaths(second_ring, clipper_enum.PolyType.Clip, true)

        local solution = clpr:Execute(clipper_enum.ClipType.Intersection, clipper_enum.PolyFillType.NonZero, clipper_enum.PolyFillType.EvenOdd)

        for i = 0, solution:ChildCount() - 1 do
            local poly = solution:Childs(i)
            poly:Draw3D(player.y, 2, 0xFF00FF00)
        end
    end
end

cb.add(cb.draw, on_draw)
Libraries
hanbot
hanbot.path
Return Value
string returns hanbots working directory

hanbot.luapath
Return Value
string returns directory of non-shard scripts

hanbot.libpath
Return Value
string returns directory of community libraries

hanbot.shardpath
Return Value
string returns directory of shards

hanbot.hwid
Return Value
string returns current users hwid

hanbot.user
Return Value
string returns current users id

hanbot.language
Return Value
number returns language id

cb
Enums:

cb.draw_first (cb.draw2)
cb.sprite (cb.draw_sprite)
cb.draw
cb.draw_world
cb.draw_under_hud
cb.tick
cb.pre_tick
cb.spell
cb.keyup
cb.keydown
cb.issueorder
cb.issue_order
cb.castspell
cb.cast_spell
cb.attack_cancel
cb.cast_finish
cb.play_animation
cb.delete_minion
cb.create_minion
cb.delete_particle
cb.create_particle
cb.delete_missile
cb.create_missile
cb.buff_gain
cb.buff_lose
cb.path
cb.set_cursorpos
cb.error
drawing order:
cb.draw_world (game pipeline) ->
cb.draw_mouse_overs (game pipeline) ->
cb.draw_under_hud (game pipeline) ->
cb.draw_first (hanbot pipeline) ->
cb.draw_sprite (hanbot pipeline) ->
cb.draw (hanbot pipeline)

ALWAYS prepare your drawings in cb.tick, DONT do too much calculations in drawing callback
The draw_world/draw_mouse_overs/draw_under_hud pipelines will be combined to hanbot pipeline if obs_bypass is enabled in core menu.
cb.add(t, f)
Parameters
number t
function f
Return Value
void

local tick_n = 0
local function on_tick()
    tick_n = tick_n + 1
    print(tick_n)
end

cb.add(cb.tick, on_tick)
cb.remove(f)
Parameters
function f
Return Value
void

local tick_n = 0
local function on_tick()
    tick_n = tick_n + 1
    if tick_n == 10 then
        print('removed')
        cb.remove(on_tick)
    end
end

cb.add(cb.tick, on_tick)
chat
chat.isOpened
Return Value
boolean returns true if chat is open

chat.size
Return Value
number returns number in chat history

chat.clear()
Clear the send buffer
Parameters
Return Value
void

chat.add(str, style)
Parameters
string str in UTF8
table style
Return Value
void

chat.send(str)
Parameters
string str in UTF8
Return Value
void

chat.clear()
chat.add("hello", {bold = false, italic = false, color = 'ffffffff'})
chat.add("world", {bold = true, italic = true, color = '99999999'})
chat.print()
chat.print(str)
Parameters
string str in UTF8
Return Value
void

chat.print("hello world")
chat.message(index)
Parameters
number index
Return Value
string

for i=0,chat.size-1 do
    print(chat.message(i))
end
console
console.set_color(c)
Parameters
number c
Return Value
void

console.printx(str, c)
Parameters
string str
number c
Return Value
void

minimap
minimap.x
Return Value
number returns the screen x pos of the upper left corner of the minimap

minimap.y
Return Value
number returns the screen y pos of the upper left corner of the minimap

minimap.width
Return Value
number returns the screen width of the minimap

minimap.height
Return Value
number returns the screen height of the minimap

minimap.bounds
Return Value
vec2 returns the maximum map boundaries

minimap.world_to_map(v1)
Parameters
vec2\vec3 v1
Return Value
vec2 returns the screen pos of v1 on the minimap

local v1 = player.pos
local a = minimap.world_to_map(v1)
a:print()
minimap.on_map(v1)
Parameters
vec2 v1
Return Value
boolean returns true if v1 is hovering the minimap

local v1 = game.cursorPos
local a = minimap.on_map(v1)
print(a)
minimap.on_map_xy(x, y)
Parameters
number x
number y
Return Value
boolean returns true if (x, y) is hovering the minimap

local x = game.cursorPos.x
local y = game.cursorPos.y
local a = minimap.on_map_xy(x, y)
print(a)
minimap.draw_circle(v1, r, w, c, n)
Parameters
vec3 v1
number r
number w
number c
number n
Return Value
void

local function on_draw()
    local v1 = player.pos
    local radius = 1000
    local line_width = 1
    local color = 0xFFFFFFFF
    local points_n = 16
    minimap.draw_circle(v1, radius, line_width, color, points_n)
end

cb.add(cb.draw, on_draw)
ping
Enums:

ping.ALERT
ping.DANGER
ping.MISSING_ENEMY
ping.ON_MY_WAY
ping.RETREAT
ping.ASSIST_ME
ping.AREA_IS_WARDED
ping.send(vec3 pos [, number ping_type [, obj target]])
Parameters
vec3 pos
number ping_type
game.obj obj
Return Value
void

ping.recv(vec3 pos [, number ping_type [, obj target]])
Parameters
vec3 pos
number ping_type
game.obj obj
Return Value
void

navmesh
navmesh.getTerrainHeight(x, z)
Parameters
number x coordinate
number z coordinate
Return Value
number returns terrain height at x,z

navmesh.isGrass(v1)
Parameters
vec2\vec3 v1
Return Value
boolean returns true if v1 is in grass

navmesh.isWater(v1)
Parameters
vec2\vec3 v1
Return Value
boolean returns true if v1 is in water

navmesh.isWall(v1)
Parameters
vec2\vec3 v1
Return Value
boolean returns true if v1 is a wall

navmesh.isStructure(v1)
Parameters
vec2\vec3 v1
Return Value
boolean returns true if v1 is a structure

navmesh.isInFoW(v1)
Parameters
vec2\vec3 v1
Return Value
boolean returns true if v1 is in fog of war

navmesh.getNearstPassable(v1)
deprecated, please use player:getPassablePos instead
Parameters
vec2\vec3 v1
Return Value
vec2 returns the nearst passable position (the cell start) vec2(x,z) to v1(x, z)
bool returns the nearst passable is grass or not

local drop_pos = navmesh.getNearstPassable(mousePos) -- return type is vec2
graphics.draw_circle(vec3(drop_pos.x + 25, 0, drop_pos.y + 25), 4, 2, 0xFFFFFF00, 3)
navmesh.calcPos(obj, targetPos, strips)
Parameters
obj hero/minion
vec3 destination
table strips (shape of obstacle area)
Return Value
vec3[] returns array of paths
number returns array length


local g_strips = {}

function check_spell_area()
    local collision_size = 4
    local collision_array = vec3.array(collision_size)

    -- push areas, and please note that the minimum unit for collision checking is cell.
    -- You need to round up points to cell boundings in real situations.
    collision_array[0] = vec3(1000, 0, 1000)
    collision_array[1] = vec3(1000, 0, 2000)
    collision_array[2] = vec3(2000, 0, 2000)
    collision_array[3] = vec3(2000, 0, 1000)

    table.insert(g_strips, {vec = collision_array, n = collision_size})
end

cb.add(cb.draw, function()

  g_strips = {}
  check_spell_area()

  -- draw a opening shape
  for i = 1, #g_strips do
    local strip = g_strips[i]
    local vec = strip.vec
    for j = 0, strip.n - 2 do
      graphics.draw_line(vec[j], vec[j + 1], 2, 0xffff0000)
    end
  end

  -- draw the path finding
  local paths,size = navmesh.calcPos(player, mousePos, g_strips)
  graphics.draw_line_strip(paths, 2, 0xFF00FF00, size)
end)
game
game.mousePos
Return Value
vec3 returns the current position of the mouse

game.mousePos2D
Return Value
vec2 returns the current position of the mouse

game.cameraPos
Return Value
vec3 returns the current position of the camera

game.cameraLocked
Return Value
boolean returns true if the camera is locked

game.setCameraLock(bool)
Parameters
boolean bool
Return Value
void

game.cameraY
Return Value
number returns the current camera zoom

game.cursorPos
Return Value
vec2 returns the current cursor position

game.time
Return Value
number returns the current game time

game.version
Return Value
string returns the current game version
You cant lock your shard to specific game version, this is a forbidden.

game.selectedTarget
Return Value
obj returns the current selected game object

game.hoveredTarget
Return Value
obj returns the current hovered game object

game.mapID
Return Value
number returns the current map ID

game.mode
Return Value
string returns the current game mode

game.type
Return Value
string returns the current game type

game.shopOpen
Return Value
boolean check if shop available

game.isWindowFocused
Return Value
boolean returns true if LoL is focused

game.getHoveredTarget(int, int)
Parameters
int mouse X
int mouse Y
Return Value
obj returns the current hovered game object by position


game.sendEmote(emoteId)
Parameters
int emoteId
Return Value
void

EMOTE_DANCE = 0
EMOTE_TAUNT = 1
EMOTE_LAUGH = 2
EMOTE_JOKE = 3
EMOTE_TOGGLE = 4
game.sendEmote(0) -- dance
game.sendRadialEmote(index)
Parameters
int index
Return Value
void

game.sendRadialEmote(8) -- centre emote
game.displayMasteryBadge()
Parameters
Return Value
void

game.fnvhash(str)
Parameters
string input string
Return Value
unsigned int returns the fnvhash of input string

game.spellhash(str)
Parameters
string input string
Return Value
unsigned int returns the spell hash of input string

game.hashSDBM(str)
Parameters
string input string
Return Value
unsigned int returns the SDBM hash of input string

graphics
graphics.width
Return Value
number returns screen width

graphics.height
Return Value
number returns screen height

graphics.res
Return Value
vec2 returns screen resolution

graphics.draw_text_2D(str, size, x, y, color)
Parameters
string str
number size
number x
number y
number color
Return Value
void

local function on_draw()
    graphics.draw_text_2D('foo', 14, game.cursorPos.x, game.cursorPos.y, 0xFFFFFFFF)
end

cb.add(cb.draw, on_draw)
graphics.draw_outlined_text_2D(str, size, x, y, color, outline_color)
Parameters
string str
number size
number x
number y
number color
number outline_color
Return Value
void

graphics.text_area(str, size, n)
Parameters
string str
number size
number n
Return Value
number returns str width
number returns str height

local str = 'foo'
local font_size = 14
local x, y = graphics.text_area(str, font_size)
print(x, y)
graphics.get_font()
Return Value
string returns current font name

graphics.argb(a, r, g, b)
Parameters
number a
number r
number g
number b
Return Value
number returns color

local color = graphics.argb(255, 25, 185, 90)
local function on_draw()
    graphics.draw_text_2D('foo', 14, game.cursorPos.x, game.cursorPos.y, color)
end

cb.add(cb.draw, on_draw)
graphics.world_to_screen(v1)
Parameters
vec3 v1
Return Value
vec2 returns screen position from world position

local function on_draw()
    local v = graphics.world_to_screen(player.pos)
    graphics.draw_text_2D('foo', 14, v.x, v.y, 0xFFFFFFFF)
end

cb.add(cb.draw, on_draw)
graphics.world_to_screen_xyz(x, y, z)
Parameters
number x
number y
number z
Return Value
vec2 returns screen position from world position

local function on_draw()
    local v = graphics.world_to_screen_xyz(player.x, player.y, player.z)
    graphics.draw_text_2D('foo', 14, v.x, v.y, 0xFFFFFFFF)
end

cb.add(cb.draw, on_draw)
graphics.draw_line_2D(x1, y1, x2, y2, width, color)
Parameters
number x1
number y1
number x2
number y2
number width
number color
Return Value
void

local function on_draw()
    graphics.draw_line_2D(0, 0, game.cursorPos.x, game.cursorPos.y, 2, 0xFFFFFFFF)
end

cb.add(cb.draw, on_draw)
graphics.draw_line(v1, v2, width, color)
Parameters
vec3 v1
vec3 v2
number width
number color
Return Value
void

local function on_draw()
    graphics.draw_line(mousePos, player.pos, 2, 0xFFFFFFFF)
end

cb.add(cb.draw, on_draw)
graphics.draw_triangle_2D(p1, p2, p3, width, color, is_filled, rounding)
Parameters
vec2 p1
vec2 p2
vec2 p3
number width
number color
boolean is_filled, default: false
number rounding, default: 0
Return Value
void

local function on_draw()
    graphics.draw_triangle_2D(vec2(10, 10),vec2(10, 50),vec2(50, 10), 2, 0xFFFFFFFF, true)
end

cb.add(cb.draw, on_draw)
graphics.draw_rectangle_2D(x, y, dx, dy, width, color, is_filled)
Parameters
number x
number y
number dx
number dy
number width
number color
boolean is_filled: default: false
number rounding: default: 0
Return Value
void

local function on_draw()
    graphics.draw_rectangle_2D(game.cursorPos.x, game.cursorPos.y, 90, 20, 2, 0xFFFFFFFF)
end

cb.add(cb.draw, on_draw)
graphics.draw_line_strip(pts, width, color, pts_n)
Parameters
vec3[] pts
number width
number color
number pts_n
Return Value
void

local function on_draw()
    if player.path.active then
        graphics.draw_line_strip(player.path.point, 2, 0xFFFFFFFF, player.path.count+1)
    end
end

cb.add(cb.draw, on_draw)
graphics.draw_line_strip_2D(pts, width, color, pts_n)
Parameters
vec2[] pts
number width
number color
number pts_n
Return Value
void

local v = vec2.array(4)
v[0].x = 200
v[0].y = 200
v[1].x = 400
v[1].y = 200
v[2].x = 400
v[2].y = 400
v[3].x = 200
v[3].y = 400
local function on_draw()
    graphics.draw_line_strip_2D(v, 2, 0xFFFFFFFF, 4)
end

cb.add(cb.draw, on_draw)
graphics.draw_circle(v1, radius, width, color, pts_n)
Parameters
vec3 v1
number radius
number width
number color
number pts_n
Return Value
void

local function on_draw()
    graphics.draw_circle(player.pos, player.attackRange, 2, 0xFFFFFFFF, 32)
end

cb.add(cb.draw, on_draw)
graphics.draw_circle_2D(x, y, radius, width, color, pts_n)
Parameters
number x
number y
number radius
number width
number color
number pts_n
Return Value
void

local function on_draw()
    graphics.draw_circle_2D(game.cursorPos.x, game.cursorPos.y, 120, 2, 0xFFFFFFFF, 32)
end

cb.add(cb.draw, on_draw)
graphics.draw_circle_xyz(x, y, z, radius, width, color, pts_n)
Parameters
number x
number y
number z
number radius
number width
number color
number pts_n
Return Value
void

local function on_draw()
    graphics.draw_circle_xyz(player.x, player.y, player.z, player.attackRange, 2, 0xFFFFFFFF, 32)
end

cb.add(cb.draw, on_draw)
graphics.draw_arc(v1, radius, width, color, pts_n, r1, r2)
Parameters
vec3 v1
number radius
number width
number color
number pts_n
number r1
number r2
Return Value
void

local function on_draw()
    graphics.draw_arc(player.pos, player.attackRange, 2, 0xFFFFFFFF, 32, 0, math.pi/2)
end

cb.add(cb.draw, on_draw)
graphics.draw_arc_2D(x, y, radius, width, color, pts_n, r1, r2)
Parameters
number x
number y
number radius
number width
number color
number pts_n
number r1
number r2
Return Value
void

local function on_draw()
    graphics.draw_arc(player.pos, player.attackRange, 2, 0xFFFFFFFF, 32, 0, math.pi/2)
end

cb.add(cb.draw, on_draw)
graphics.create_effect(type)
Create a shadereffect instance by type
Parameters
number type
Return Value
shadereffect.obj

-- create once, show always, with best performance
local circle_unchanged = graphics.create_effect(graphics.CIRCLE_RAINBOW)
circle_unchanged:update_circle(player.pos, 1200, 2, 0xFFFF0000)
circle_unchanged:attach(player)
circle_unchanged:show()
-- update effect in on_draw
local circle_aa = graphics.create_effect(graphics.CIRCLE_RAINBOW)
local function on_draw()
    circle_aa:update_circle(player.pos, player.attackRange, 2, 0xff123456)
end
cb.add(cb.draw, on_draw)
graphics.CIRCLE_GLOW
Return Value
number simple glow circle

graphics.CIRCLE_GLOW_RAINBOW
Return Value
number glow circle with rainbow

graphics.CIRCLE_GLOW_LIGHT
Return Value
number another glow circle

graphics.CIRCLE_GLOW_BOLD
Return Value
number another glow circle 2

graphics.CIRCLE_FIRE
Return Value
number fire circle

graphics.CIRCLE_RAINBOW
Return Value
number colorful rainbow circle

graphics.CIRCLE_RAINBOW_BOLD
Return Value
number colorful another rainbow circle

graphics.CIRCLE_FILL
Return Value
number filled circle

graphics.sprite(name)
Parameters
string name
vec2 the screen position
number scale
number color
Return Value
texture.obj

local myIcon = graphics.sprite('XXX/menu_icon.png')
myMenu:set('icon', myIcon)
graphics.game_sprite(name)
Parameters
string name
vec2 the screen position
number scale
number color
Return Value
texture.obj

-- example: https://raw.communitydragon.org/14.1/game/assets/characters/sru_blue/hud/bluesentinel_circle.png
local icon = graphics.game_sprite('ASSETS/Characters/SRU_Blue/HUD/BlueSentinel_Circle.dds')
local icon_ashe = graphics.game_sprite('ASSETS/Characters/Ashe/HUD/Ashe_Circle.dds')
graphics.draw_sprite(name, v1, scale, color)
Parameters
string|texture.obj name
vec2 the screen position
number scale
number color
Return Value
void

local function on_draw_sprite()
    --sprite files must be placed in your shard directory
    graphics.draw_sprite("sprite_name.png", vec2(p.x, p.y), 1.5, 0x66FFFFFF)
end

cb.add(cb.sprite, on_draw_sprite)
graphics.spawn_fake_click(color, v1)
Parameters
string color: “red” or “green”
vec3 v1: world_pos
Return Value
void

local function on_key_down(k)
    if k==49 then --1
        graphics.spawn_fake_click("green", mousePos)
    end
end

cb.add(cb.keydown, on_key_down)
graphics.set_draw(enabled)
Parameters
boolean enabled
Return Value
void

graphics.get_draw()
Parameters
Return Value
boolean enabled

graphics.set_draw_menu(enabled)
Parameters
boolean enabled
Return Value
void

shadereffect
shadereffect.construct(effect_description, is_3D)
Parameters
string effect_description
boolean is_3D
Return Value
shadereffect shadereffect

shadereffect.begin(shadereffect, height, is_3D)
Parameters
shadereffect shadereffect
number hieght
boolean is_3D
Return Value
void

shadereffect.set_float(shadereffect, varname, var)
Parameters
shadereffect shadereffect
string varname
number var
Return Value
void

shadereffect.set_vec2(shadereffect, varname, var)
Parameters
shadereffect shadereffect
string varname
vec2 var
Return Value
void

shadereffect.set_vec3(shadereffect, varname, var)
Parameters
shadereffect shadereffect
string varname
vec3 var
Return Value
void

shadereffect.set_vec4(shadereffect, varname, var)
Parameters
shadereffect shadereffect
string varname
vec4 var
Return Value
void

shadereffect.set_float_array(shadereffect, varname, var, size)
Parameters
shadereffect shadereffect
string varname
array var
number size
Return Value
void

shadereffect.set_color(shadereffect, varname, color)
Parameters
shadereffect shadereffect
string varname
color var
Return Value
void

objManager
objManager.player
Return Value
hero.obj returns player object

print(player.charName)
objManager.maxObjects
Return Value
number returns max object count

objManager.get(i)
Return Value
obj returns game object

for i=0, objManager.maxObjects-1 do
    local obj = objManager.get(i)
    if obj and obj.type==TYPE_HERO then
        print(obj.charName)
    end
end
objManager.enemies_n
Return Value
number returns enemy hero count

objManager.enemies
Return Value
hero.obj[] returns array of enemy heroes

for i=0, objManager.enemies_n-1 do
    local obj = objManager.enemies[i]
    print(obj.charName)
end
objManager.allies_n
Return Value
number returns ally hero count

objManager.allies
Return Value
hero.obj[] returns array of ally heroes

for i=0, objManager.allies_n-1 do
    local obj = objManager.allies[i]
    print(obj.charName)
end
objManager.minions.size[team]
Parameters
team can be any of these: TEAM_ALLY/TEAM_ENEMY/TEAM_NEUTRAL / “plants” / “others” / “farm” / “farm” / “lane_ally” / “lane_enemy” / “pets_ally” / “pets_enemy” / “barrels”
Return Value
number returns minion count of respective team

local enemyMinionCount = objManager.minions.size[TEAM_ENEMY]
objManager.minions[team][i]
Parameters
team can be any of these: TEAM_ALLY/TEAM_ENEMY/TEAM_NEUTRAL / “plants” / “others” / “farm” / “lane_ally” / “lane_enemy” / “pets_ally” / “pets_enemy” / “barrels”
Return Value
minion.obj returns minion object


local enemy_minion_size = objManager.minions.size[TEAM_ENEMY]
local enemy_minion_arr = objManager.minions[TEAM_ENEMY]
for i=0, enemy_minion_size-1 do
    local obj = enemy_minion_arr[i]
    print(obj.charName)
end

-- spell farm minions
local farm_minion_size = objManager.minions.size['farm']
local farm_minion_size = objManager.minions.size['farm']
local farm_minion_arr = objManager.minions['farm']
for i=0, farm_minion_size-1 do
    local obj = farm_minion_arr[i]
    print(obj.charName)
end
objManager.turrets.size[team]
Return Value
number returns turret count of respective team

objManager.turrets[team][i]
Return Value
turret.obj returns turret object

for i=0, objManager.turrets.size[TEAM_ALLY]-1 do
    local obj = objManager.turrets[TEAM_ALLY][i]
    print(obj.charName)
end
objManager.inhibs.size[team]
Return Value
number returns inhib count of respective team

objManager.inhibs[team][i]
Return Value
inhib.obj returns inhib object

for i=0, objManager.inhibs.size[TEAM_ALLY]-1 do
    local obj = objManager.inhibs[TEAM_ALLY][i]
    print(obj.health)
end
objManager.nexus[team]
Return Value
nexus.obj returns nexus object

print(objManager.nexus[TEAM_ENEMY].health)
objManager.allObjects[i]
Return Value
base.obj returns GameObject

objManager.allAttackableUnits[i]
Return Value
base.obj returns AttackableUnit

objManager.allHeros[i]
Return Value
hero.obj returns hero object

for i=0, objManager.allHeros.size-1 do
    local obj = objManager.allHeros[i]
    print(obj.handle)
end
objManager.allMinions[i]
Return Value
minion.obj returns minion object

objManager.minionsAlly[i]
Return Value
minion.obj returns minion object

objManager.minionsEnemy[i]
Return Value
minion.obj returns minion object

objManager.minionsNeutral[i]
Return Value
minion.obj returns minion object

objManager.minionsOther[i]
Return Value
minion.obj returns minion object

objManager.petsAlly[i]
Return Value
minion.obj returns minion object

objManager.petsEnemy[i]
Return Value
minion.obj returns minion object

objManager.barrels[i]
Return Value
minion.obj returns minion object

objManager.missiles[i]
Return Value
missile.obj returns missile object

objManager.particles[i]
Return Value
particle.obj returns particle object

objManager.wardsAlly[i]
Return Value
minion.obj returns ally ward object

objManager.loop(f)
Parameters
function f
Return Value
void

local function foo(obj)
    if obj.type==TYPE_HERO then
        print(obj.charName)
    end
end

objManager.loop(foo)
core
core.block_input()
Only works in cb.issueorder and cb.castspell, will block current operation.

Return Value
void

local function on_issue_order(order, pos, target_ptr)
    if order==3 then
        --blocks this attack
        --this only works for orders issued by hanbot
        core.block_input()
    end
end

cb.add(cb.issueorder, on_issue_order)
core.reload()
Return Value
void

sound
sound.play(name)
Parameters
string name

-- while resource is shared between all plugins, it is better to have a unique name (path)
sound.play('demo_aio_resources/load.wav')
sound.play_from_file(file_path)
Parameters
string file_path

sound.disable(is_disabled)
Parameters
boolean is_disabled

shop
shop.canShop
Return Value
bool

shop.isOpened
Return Value
bool

shop.augmentSelectionOpen
Return Value
bool

shop.augmentSelectionIds
Return Value
table See AugmentId

enum class AugmentId: std::uint32_t
{
    Oathsworn = 0x1F273AC,// buff_hash( "Oathsworn" )
    ShrinkRay = 0x302DB74,// buff_hash( "ShrinkRay" )
    UltimateRevolution = 0x4A9BDC5,// buff_hash( "UltimateRevolution" )
    StackosaurusRex = 0x55E5BD2,// buff_hash( "StackosaurusRex" )
    OrbitalLaser = 0x5E9FD41,// buff_hash( "OrbitalLaser" )
    ApexInventor = 0x93D7848,// buff_hash( "ApexInventor" )
    escAPADe = 0xB24AB25,// buff_hash( "escAPADe" )
    WarmupRoutine = 0xB731243,// buff_hash( "WarmupRoutine" )
    HeavyHitter = 0x109C1530,// buff_hash( "HeavyHitter" )
    SummonersRoulette = 0x13BDA632,// buff_hash( "SummonersRoulette" )
    EtherealWeapon = 0x13E8C4A7,// buff_hash( "EtherealWeapon" )
    TheBrutalizer = 0x16EA7954,// buff_hash( "TheBrutalizer" )
    ItsKillingTime = 0x17A308CA,// buff_hash( "ItsKillingTime" )
    GiantSlayer = 0x1950C668,// buff_hash( "GiantSlayer" )
    SearingDawn = 0x199AA2C8,// buff_hash( "SearingDawn" )
    CannonFodder = 0x1A9B0060,// buff_hash( "CannonFodder" )
    Quest_WoogletsWitchcap = 0x1BC0CAC1,// buff_hash( "Quest_WoogletsWitchcap" )
    SpinToWin = 0x209762E6,// buff_hash( "SpinToWin" )
    Executioner = 0x23F1EBC6,// buff_hash( "Executioner" )
    NowYouSeeMe = 0x26433BAB,// buff_hash( "NowYouSeeMe" )
    FeeltheBurn = 0x2D24DA51,// buff_hash( "FeeltheBurn" )
    ItsCritical = 0x2E4815CC,// buff_hash( "ItsCritical" )
    GambaAnvil = 0x2EFA5F6D,// buff_hash( "GambaAnvil" )
    MadScientist = 0x301C4AB9,// buff_hash( "MadScientist" )
    DontBlink = 0x361E81B4,// buff_hash( "DontBlink" )
    GuiltyPleasure = 0x38A48C10,// buff_hash( "GuiltyPleasure" )
    SkilledSniper = 0x3A3FB8AC,// buff_hash( "SkilledSniper" )
    OutlawsGrit = 0x3AFCE102,// buff_hash( "OutlawsGrit" )
    Clothesline = 0x3D187ED1,// buff_hash( "Clothesline" )
    BannerofCommand = 0x3F13BB13,// buff_hash( "BannerofCommand" )
    TwiceThrice = 0x42346B02,// buff_hash( "TwiceThrice" )
    SoulSiphon = 0x4293CD3B,// buff_hash( "SoulSiphon" )
    OmniSoul = 0x44031317,// buff_hash( "OmniSoul" )
    Chauffeur = 0x4474F5D0,// buff_hash( "Chauffeur" )
    ServeBeyondDeath = 0x4589990F,// buff_hash( "ServeBeyondDeath" )
    Quest_UrfsChampion = 0x4716448D,// buff_hash( "Quest_UrfsChampion" )
    CelestialBody = 0x47330DB1,// buff_hash( "CelestialBody" )
    IceCold = 0x48B5BDCA,// buff_hash( "IceCold" )
    FromBeginningToEnd = 0x48DC2006,// buff_hash( "FromBeginningToEnd" )
    DawnbringersResolve = 0x4B303285,// buff_hash( "DawnbringersResolve" )
    WillingSacrifice = 0x4B43BF6C,// buff_hash( "WillingSacrifice" )
    JuiceBox = 0x4D27E960,// buff_hash( "JuiceBox" )
    DontChase = 0x4DBF5E70,// buff_hash( "DontChase" )
    BreadAndCheese = 0x4E28F2C3,// buff_hash( "BreadAndCheese" )
    FrostWraith = 0x4F7D128E,// buff_hash( "FrostWraith" )
    FeyMagic = 0x52022D72,// buff_hash( "FeyMagic" )
    DrawYourSword = 0x522572E9,// buff_hash( "DrawYourSword" )
    InfernalSoul = 0x55780831,// buff_hash( "InfernalSoul" )
    QuantumComputing = 0x55EF3A40,// buff_hash( "QuantumComputing" )
    OkBoomerang = 0x58FEA611,// buff_hash( "OkBoomerang" )
    Eureka = 0x59B3AFC2,// buff_hash( "Eureka" )
    Dematerialize = 0x5C8D739F,// buff_hash( "Dematerialize" )
    BigBrain = 0x5F479857,// buff_hash( "BigBrain" )
    MysticPunch = 0x610EB7B4,// buff_hash( "MysticPunch" )
    HolyFire = 0x61BEDC9B,// buff_hash( "HolyFire" )
    SymphonyofWar = 0x64EB2265,// buff_hash( "SymphonyofWar" )
    Marksmage = 0x65A8CBEF,// buff_hash( "Marksmage" )
    ScopierWeapons = 0x662429D1,// buff_hash( "ScopierWeapons" )
    BluntForce = 0x6951004B,// buff_hash( "BluntForce" )
    MindtoMatter = 0x6AD13E9D,// buff_hash( "MindtoMatter" )
    Homeguard = 0x6CA0F337,// buff_hash( "Homeguard" )
    Earthwake = 0x6E25F1F7,// buff_hash( "Earthwake" )
    Quest_AngelofRetribution = 0x6F79D727,// buff_hash( "Quest_AngelofRetribution" )
    BuffBuddies = 0x71E56AB8,// buff_hash( "BuffBuddies" )
    NestingDoll = 0x73016C04,// buff_hash( "NestingDoll" )
    BreadAndJam = 0x75565D16,// buff_hash( "BreadAndJam" )
    Recursion = 0x76B60E67,// buff_hash( "Recursion" )
    TankItOrLeaveIt = 0x80B86F89,// buff_hash( "TankItOrLeaveIt" )
    FrozenFoundations = 0x819259C7,// buff_hash( "FrozenFoundations" )
    Vengeance = 0x886F1F8D,// buff_hash( "Vengeance" )
    WithHaste = 0x894E5EBC,// buff_hash( "WithHaste" )
    Erosion = 0x8C2F16FE,// buff_hash( "Erosion" )
    Perseverance = 0x8D3AE60E,// buff_hash( "Perseverance" )
    ChainLightning = 0x8E40C3DC,// buff_hash( "ChainLightning" )
    TrueshotProdigy = 0x93AB024B,// buff_hash( "TrueshotProdigy" )
    PhenomenalEvil = 0x94D656DE,// buff_hash( "PhenomenalEvil" )
    ContractKiller = 0x97C4EFA0,// buff_hash( "ContractKiller" )
    BacktoBasics = 0x984667B4,// buff_hash( "BacktoBasics" )
    CircleofDeath = 0x9A1D093E,// buff_hash( "CircleofDeath" )
    Castle = 0x9AC2A459,// buff_hash( "Castle" )
    JeweledGauntlet = 0x9CAAE31B,// buff_hash( "JeweledGauntlet" )
    SlapAround = 0x9DFB15EE,// buff_hash( "SlapAround" )
    RestlessRestoration = 0x9E1A180E,// buff_hash( "RestlessRestoration" )
    CriticalHealing = 0x9F40039E,// buff_hash( "CriticalHealing" )
    Flashy = 0x9F6FD4F0,// buff_hash( "Flashy" )
    MasterofDuality = 0x9F9005F8,// buff_hash( "MasterofDuality" )
    Quest_SteelYourHeart = 0xA173E08E,// buff_hash( "Quest_SteelYourHeart" )
    Impassable = 0xA1A3AF22,// buff_hash( "Impassable" )
    ScopiestWeapons = 0xA1D75DC2,// buff_hash( "ScopiestWeapons" )
    LightemUp = 0xA26FF4EC,// buff_hash( "LightemUp" )
    OceanSoul = 0xA3670B02,// buff_hash( "OceanSoul" )
    Deft = 0xA3E2E068,// buff_hash( "Deft" )
    LaserEyes = 0xA748902E,// buff_hash( "LaserEyes" )
    WisdomofAges = 0xA83CA207,// buff_hash( "WisdomofAges" )
    DemonsDance = 0xA8B08058,// buff_hash( "DemonsDance" )
    BloodBrother = 0xAB06A043,// buff_hash( "BloodBrother" )
    Minionmancer = 0xAB4CDD4D,// buff_hash( "Minionmancer" )
    Firebrand = 0xABA0CD52,// buff_hash( "Firebrand" )
    SpiritLink = 0xAC744FB8,// buff_hash( "SpiritLink" )
    MountainSoul = 0xAE76811D,// buff_hash( "MountainSoul" )
    Vulnerability = 0xAFE4CF71,// buff_hash( "Vulnerability" )
    RaidBoss = 0xB2EDEF40,// buff_hash( "RaidBoss" )
    MagicMissile = 0xB4545A74,// buff_hash( "MagicMissile" )
    WitchfulThinking = 0xB503AEA3,// buff_hash( "WitchfulThinking" )
    CantTouchThis = 0xB5D9645A,// buff_hash( "CantTouchThis" )
    ComboMaster = 0xBA23DC87,// buff_hash( "ComboMaster" )
    KeystoneConjurer = 0xBBE31DDB,// buff_hash( "KeystoneConjurer" )
    BreadAndButter = 0xBD47D568,// buff_hash( "BreadAndButter" )
    ThreadtheNeedle = 0xBE4AA497,// buff_hash( "ThreadtheNeedle" )
    SnowballFight = 0xBF2B1A73,// buff_hash( "SnowballFight" )
    Typhoon = 0xBF771C16,// buff_hash( "Typhoon" )
    MirrorImage = 0xC390B2C7,// buff_hash( "MirrorImage" )
    RabbleRousing = 0xC3A6E3C0,// buff_hash( "RabbleRousing" )
    SelfDestruct = 0xC451D495,// buff_hash( "SelfDestruct" )
    DefensiveManeuvers = 0xC488607E,// buff_hash( "DefensiveManeuvers" )
    SlowCooker = 0xCB705AEB,// buff_hash( "SlowCooker" )
    UltimateUnstoppable = 0xCDF2651B,// buff_hash( "UltimateUnstoppable" )
    ADAPt = 0xD18FEA7F,// buff_hash( "ADAPt" )
    TapDancer = 0xD1F84AA5,// buff_hash( "TapDancer" )
    Spellwake = 0xD3CBE301,// buff_hash( "Spellwake" )
    Flashbang = 0xD4DA8047,// buff_hash( "Flashbang" )
    ShadowRunner = 0xD5F12997,// buff_hash( "ShadowRunner" )
    CourageoftheColossus = 0xD68F7D08,// buff_hash( "CourageoftheColossus" )
    InfernalConduit = 0xD79FD9B0,// buff_hash( "InfernalConduit" )
    AcceleratingSorcery = 0xDA054EE4,// buff_hash( "AcceleratingSorcery" )
    Repulsor = 0xDABCBBC3,// buff_hash( "Repulsor" )
    ExtendoArm = 0xDE9A77E4,// buff_hash( "ExtendoArm" )
    ScopedWeapons = 0xE3E3DBBE,// buff_hash( "ScopedWeapons" )
    LightningStrikes = 0xE48980B4,// buff_hash( "LightningStrikes" )
    FallenAegis = 0xE67E996E,// buff_hash( "FallenAegis" )
    BladeWaltz = 0xE8A9A9F1,// buff_hash( "BladeWaltz" )
    FirstAidKit = 0xE9AFCCDF,// buff_hash( "FirstAidKit" )
    Dashing = 0xED80A0CB,// buff_hash( "Dashing" )
    FullyAutomated = 0xEDFA6D47,// buff_hash( "FullyAutomated" )
    HoldVeryStill = 0xF0544646,// buff_hash( "HoldVeryStill" )
    DieAnotherDay = 0xF0DBEF32,// buff_hash( "DieAnotherDay" )
    Goredrink = 0xF4EBEFD4,// buff_hash( "Goredrink" )
    Vanish = 0xF6A6FFEA,// buff_hash( "Vanish" )
    AllForYou = 0xFC06EEFA,// buff_hash( "AllForYou" )
    Goliath = 0xFCB87F9B,// buff_hash( "Goliath" )
    CenterOfTheUniverse = 0xFCCE03A7,// buff_hash( "CenterOfTheUniverse" )
    DiveBomber = 0xFD0ED9E4,// buff_hash( "DiveBomber" )
    ParasiticRelationship = 0xFE635A5B,// buff_hash( "ParasiticRelationship" )
    Restart = 0xFE9C11EC,// buff_hash( "Restart" )
    Tormentor = 0xFEFBCC4F,// buff_hash( "Tormentor" )
    SonicBoom = 0xFF509218,// buff_hash( "SonicBoom" )
};
shop.buyItem(itemID, preferredSlotID)
shop.sellItem(inventorySlotId)
shop.undo()
shop.swapItem(source, dest)
memory
Valid types:

char
unsigned char
short
unsigned short
int
unsigned int
long long
unsigned long long
float
memory.new(type, n)
Parameters
string type
number n
Return Value
mixed c-type array

input
Enums:

input.LOCK_MOVEMENT
input.LOCK_ABILITIES
input.LOCK_SUMMONERSPELLS
input.LOCK_SHOP
input.LOCK_CHAT
input.LOCK_MINIMAPMOVEMENT
input.LOCK_CAMERAMOVEMENT
input.lock(…)
Parameters
number input_type
Return Value
void

input.unlock(…)
Parameters
number input_type
Return Value
void

input.islocked(…)
Parameters
number input_type
Return Value
boolean

input.lock_slot(…)
Parameters
number slot
Return Value
void

input.unlock_slot(…)
Parameters
number slot
Return Value
void

input.islocked_slot(…)
Parameters
number slot
Return Value
boolean

keyboard
keyboard.isKeyDown(key_code)
Parameters
number key_code
Return Value
boolean returns true if key_code is down

local function on_tick()
    if keyboard.isKeyDown(0x20) then --spacebar
        print('spacebar pressed')
    end
end

cb.add(cb.tick, on_tick)
keyboard.getClipboardText()
Return Value
string returns text that was copied to clipboard
string returns error message on failure

keyboard.setClipboardText(text)
Parameters
string text
Return Value
void copies text to clipboard

keyboard.keyCodeToString(key_code)
Parameters
number key_code
Return Value
string returns corresponding character of key_code

print(keyboard.keyCodeToString(0x20))
keyboard.stringToKeyCode(key)
Parameters
string key
Return Value
number returns corresponding key_code of key

print(keyboard.stringToKeyCode('Space'))
permashow
permashow.enable(v1)
Parameters
boolean enabled

permashow.set_pos(x, y)
Parameters
number x
number y

permashow.set_drag_enabled(option)
Parameters
number option, 1 = enabled always, 2 = enabled when menu show

permashow.set_alpha(alpha)
Parameters
number alpha

permashow.set_theme(theme)
Parameters
table theme

-- simple, set the alpha
permashow.set_alpha(100)

-- advanced usage, set custom theme (alpha will be ignored)
permashow.set_theme({
    itemHeight = 20,
    textSize = 14,
    textColor = 0xFFFFF799,
    textColorDisabled = 0xFFA8A8A8,

    shadowColor = 0x90000000,
    backgroundUpLeft = 0x90797145,
    backgroundUpRight = 0x904a3f23,
    backgroundBottomLeft = 0x90797145,
    backgroundBottomRight = 0x904a3f23,
    areaUpLeft = 0x90091e18,
    areaUpRight = 0x90091e18,
    areaBottomLeft = 0x9005120c,
    areaBottomRight = 0x9005120c,

    itemBorder1 = 0x33f4f499,
    itemBorder2 = 0x11f4f499,
    itemBorder3 = 0x33f4f499,
})
permashow.enable(true)
network
network.latency
Return Value
number returns game network latency in seconds

network.download_file(url, dest)
Parameters
string url
string dest
Return Value
boolean returns true on success

local url = 'https://i.imgur.com/k6HB1gA.gif'
local dest = hanbot.luapath..'/ok.png'
local success = network.download_file(url, dest)
print(success)
network.easy_download(cb, uri, path)
Parameters
function cb
string uri
string dest
Return Value
void

network.easy_post(cb, uri, postfields)
Parameters
function cb
string uri
string postfields
Return Value
void

module
module.seek(mod)
Parameters
string mod
Return Value
module returns module if it is loaded

local function on_tick()
    --module.seek will only return the module if its loaded
    local evade = module.seek('evade')
    if evade and evade.core.is_active() then return end
end

cb.add(cb.tick, on_tick)
module.load(id, mod)
Parameters
string id
string mod
Return Value
module returns module, loads it if needed

local foo_bar = module.load('foo', 'bar')
module.internal(mod)
Parameters
string mod
Return Value
module returns module, loads it if needed

local orb = module.internal('orb')
local function on_tick()
    local evade = module.seek('evade')
    if evade and evade.core.is_active() then return end
end

cb.add(cb.tick, on_tick)
module.lib(id)
Parameters
string id
Return Value
library returns library, loads it if needed

module.path(id)
Parameters
string id
Return Value
string returns module path

module.is_shard(id)
Parameters
string id
Return Value
boolean returns true if shard id exists

module.is_libshard(id)
Parameters
string id
Return Value
boolean returns true if libshard id exists

module.is_anyshard(id)
Parameters
string id
Return Value
boolean returns true if shard id or libshard id exists

module.file_exists(path)
Parameters
string path
Return Value
boolean returns true file exists

module.directory_exists(path)
Parameters
string path
Return Value
boolean returns true file exists

module.create_directory(id, path)
Parameters
string id
string path
Return Value
boolean returns true on success

module.create_script_directory(path)
Parameters
string path
Return Value
boolean returns true on success

module.create_lib_directory(path)
Parameters
string path
Return Value
boolean returns true on success

module.create_shard_directory(path)
Parameters
string path
Return Value
boolean returns true on success

module.open_file(id, path, mode)
Parameters
string id
string path
string mod
Return Value
file returns file handle on success

module.open_script_file(path, mode)
Parameters
string path
string mod
Return Value
file returns file handle on success

module.open_lib_file(path, mode)
Parameters
string path
string mod
Return Value
file returns file handle on success

module.open_shard_file(path, mode)
Parameters
string path
string mod
Return Value
file returns file handle on success

module.delete_file(id, path)
Parameters
string id
string path
Return Value
boolean returns true on success

module.delete_script_file(path)
Parameters
string path
Return Value
boolean returns true on success

module.delete_lib_file(path)
Parameters
string path
Return Value
boolean returns true on success

module.delete_shard_file(path)
Parameters
string path
Return Value
boolean returns true on success

module.rename_file(id, old, new)
Parameters
string id
string old
string new
Return Value
boolean returns true on success

module.rename_script_file(old, new)
Parameters
string old
string new
Return Value
boolean returns true on success

module.rename_lib_file(old, new)
Parameters
string old
string new
Return Value
boolean returns true on success

module.rename_shard_file(old, new)
Parameters
string old
string new
Return Value
boolean returns true on success

module.generate_tree(id, hash, anyfile)
Parameters
string id
string hash
boolean anyfile
Return Value
table returns file tree

menu
menu(var, text)
Parameters
string var
string text
Return Value
object returns menu instance

local myMenu = menu('example_menu', 'Example Menu')
menu:header(var, text)
Parameters
string var
string text
Return Value
void

local myMenu = menu('example_menu', 'Example Menu')
myMenu:header('example_header', 'Example Header')
menu:boolean(var, text, value)
Parameters
string var
string text
boolean value
Return Value
void

local myMenu = menu('example_menu', 'Example Menu')
myMenu:boolean('example_boolean', 'Example Boolean')

local function on_tick()
    if myMenu.example_boolean:get() then
        print('example_boolean is true')
    end
end

cb.add(cb.tick, on_tick)
menu:slider(var, text, value, min, max, step)
Parameters
string var
string text
number value
number min
number max
number step
Return Value
void

local myMenu = menu('example_menu', 'Example Menu')
myMenu:slider('example_slider', 'Example Slider', 100, 0, 150, 5)

local function on_tick()
    print(myMenu.example_slider:get())
end

cb.add(cb.tick, on_tick)
menu:keybind(var, text, key, toggle, defaultToggleValue)
Parameters
string var
string text
string key
string toggle
bool defaultToggleValue
Return Value
void

local myMenu = menu('example_menu', 'Example Menu')
--this creates an on key down keybind for 'A'
myMenu:keybind('example_keybind_a', 'Example Keybind A', 'A', nil)
--this creates a toggle keybind for 'A'
myMenu:keybind('example_keybind_b', 'Example Keybind B', nil, 'A')
--this creates an on key down keybind for 'A' or toggle for 'B'
myMenu:keybind('example_keybind_c', 'Example Keybind C', 'A', 'B')
--this disable the permashow for Keybind C
myMenu.example_keybind_c:permashow(false)

local function on_tick()
    if myMenu.example_keybind_a:get() then
        print('example_keybind_a is active')
    end
end

cb.add(cb.tick, on_tick)
menu:dropdown(var, text, value, options)
Parameters
string var
string text
number value
table options
Return Value
void

local myMenu = menu('example_menu', 'Example Menu')
myMenu:dropdown('example_dropdown', 'Example Dropdown', 1, {'Option A', 'Option B', 'Option C',})

local function on_tick()
    print(myMenu.example_dropdown:get())
end

cb.add(cb.tick, on_tick)
menu:button(var, text, buttonText, callback)
Parameters
string var
string text
string buttonText
function callback
Return Value
void

local myMenu = menu('example_menu', 'Example Menu')
myMenu:button('example_button', 'Example Button', 'My Button', function() print('button was pressed') end)
menu:color(var, text, red, green, blue, alpha)
Parameters
string var
string text
number red
number green
number blue
number alpha
Return Value
void

local myMenu = menu('example_menu', 'Example Menu')
myMenu:color('example_color', 'Example Color', 255, 0, 0, 255)

local function on_draw()
    graphics.draw_text_2D('foo', 14, game.cursorPos.x, game.cursorPos.y, myMenu.example_color:get())
end

cb.add(cb.draw, on_draw)
menu:isopen()
Return Value
boolean returns true if the menu is currently open

local myMenu = menu('example_menu', 'Example Menu')

local function on_tick()
    if myMenu:isopen() then
        print('menu is open')
    end
end

cb.add(cb.tick, on_tick)
menu:set(property, value)
Parameters
string property
mixed value
Return Value
void

--[[
The following properties can be set on their respective instances:
    'tooltip'
    'callback'
    'value'
    'text'
    'visible'
    'buttonText'
    'red'
    'green'
    'blue'
    'alpha'
    'options'
    'toggleValue'
    'toggle'
    'key'
    'min'
    'max'
    'step',
    'icon',
]]

local myMenu = menu('example_menu', 'Example Menu')
myMenu:slider('example_slider', 'Example Slider', 100, 0, 150, 5)

myMenu.example_slider:set('tooltip', 'This text will appear when the mouse is hovering Example Slider')
myMenu.example_slider:set('callback', function(old, new)
    print(('example_slider changed from %u to %u'):format(old, new))
end)

local myIcon = graphics.sprite('XXX/menu_icon.png')
myMenu.example_slider:set('icon', myIcon)
md5
md5.file(path)
Parameters
string path
Return Values
string returns md5 hash

md5.sum(str)
Parameters
string str
Return Values
string returns integer md5 hash

md5.tohex(str, upper)
Parameters
string str
boolean upper
Return Values
string returns str in hex, in uppercase if upper is true

Objects
base.obj
Properties:

boolean base.valid
number base.type
number base.index
The index of object

number base.index32
The object_id of object

number base.team
string base.name
number base.networkID
number base.networkID32
The network_id of object

number base.x
number base.y
number base.z
vec3 base.pos
vec2 base.pos2D
boolean base.isOnScreen
number base.selectionHeight
number base.selectionRadius
vec3 base.minBoundingBox
vec3 base.maxBoundingBox
hero.obj
Properties:

string hero.name
string hero.charName
string hero.recallName
texture.obj hero.iconCircle
texture.obj hero.iconSquare
string hero.recallName
boolean hero.isOnScreen
boolean hero.inShopRange
boolean hero.isDead
boolean hero.isAlive
boolean hero.isVisible
boolean hero.isRecalling
boolean hero.isTargetable
boolean hero.parEnabled
boolean hero.sarEnabled
vec3 hero.pos
vec3 hero.direction
vec2 hero.pos2D
vec2 hero.direction2D
vec2 hero.barPos
path.obj hero.path
spell.obj hero.activeSpell
table hero.buff
-- use BUFF_XXX for buff type
if hero.buff[BUFF_BLIND] then
    print('player blind')
end

-- Use the name(in lowercase) to get the the specified name
if hero.buff['kaisaeevolved'] then
    print('has buff KaisaEEvolved')
end
runemanager.obj hero.rune
number hero.type
number hero.index
number hero.networkID
number hero.networkID32
number hero.team
number hero.x
number hero.y
number hero.z
number hero.selectionHeight
number hero.selectionRadius
number hero.boundingRadius
number hero.overrideCollisionRadius
number hero.pathfindingCollisionRadius
vec3 hero.minBoundingBox
vec3 hero.maxBoundingBox
number hero.deathTime
number hero.health
number hero.maxHealth
number hero.maxHealthPenalty
number hero.allShield
number hero.physicalShield
number hero.magicalShield
number hero.champSpecificHealth
number hero.stopShieldFade
number hero.isTargetableToTeamFlags
number hero.mana
number hero.maxMana
number hero.baseAttackDamage
number hero.baseAd
alias of baseAttackDamage

number hero.bonusAd
number hero.totalAd
The TotalAttackDamage

number hero.totalAp
The TotalAbilityPower

number hero.armor
number hero.spellBlock
number hero.attackSpeedMod
number hero.flatPhysicalDamageMod
number hero.percentPhysicalDamageMod
number hero.flatMagicDamageMod
number hero.percentMagicDamageMod
number hero.healthRegenRate
number hero.bonusArmor
number hero.bonusSpellBlock
number hero.flatBubbleRadiusMod
number hero.percentBubbleRadiusMod
number hero.moveSpeed
number hero.moveSpeedBaseIncrease
number hero.scaleSkinCoef
number hero.gold
number hero.goldTotal
number hero.minimumGold
number hero.evolvePoints
number hero.evolveFlag
number hero.inputLocks
number hero.skillUpLevelDeltaReplicate
number hero.manaCost0
number hero.manaCost1
number hero.manaCost2
number hero.manaCost3
number hero.baseAbilityDamage
number hero.dodge
number hero.crit
number hero.parRegenRate
number hero.attackRange
number hero.flatMagicReduction
number hero.percentMagicReduction
number hero.flatCastRangeMod
number hero.percentCooldownMod
number hero.passiveCooldownEndTime
number hero.passiveCooldownTotalTime
number hero.flatArmorPenetration
number hero.percentArmorPenetration
number hero.flatMagicPenetration
number hero.percentMagicPenetration
number hero.percentLifeStealMod
number hero.percentSpellVampMod
number hero.percentOmnivamp
number hero.percentPhysicalVamp
number hero.percentBonusArmorPenetration
number hero.percentCritBonusArmorPenetration
number hero.percentCritTotalArmorPenetration
number hero.percentBonusMagicPenetration
number hero.physicalLethality
number hero.magicLethality
number hero.baseHealthRegenRate
number hero.primaryARBaseRegenRateRep
number hero.secondaryARRegenRateRep
number hero.secondaryARBaseRegenRateRep
number hero.percentCooldownCapMod
number hero.percentEXPBonus
number hero.flatBaseAttackDamageMod
number hero.percentBaseAttackDamageMod
number hero.baseAttackDamageSansPercentScale
number hero.exp
number hero.par
number hero.maxPar
number hero.sar
number hero.maxSar
number hero.pathfindingRadiusMod
number hero.levelRef
number hero.numNeutralMinionsKilled
number hero.primaryArRegenRateRep
number hero.physicalDamagePercentageModifier
number hero.magicalDamagePercentageModifier
number hero.baseHealth
number hero.baseMana
number hero.baseManaPerLevel
number hero.combatType
number hero.critDamageMultiplier
number hero.abilityHasteMod
number hero.baseMoveSpeed
number hero.baseAttackRange
bool hero.isZombie
bool hero.isMelee
bool hero.isRanged
bool hero.isBot
bool hero.isMe
The following functions are limited to player only:

player:move(v1)
Parameters
hero.obj player
vec3 v1
Return Value
void

player:attackmove(v1)
Parameters
hero.obj player
vec3 v1
Return Value
void

player:altmove(v1)
Parameters
hero.obj player
vec3 v1
Return Value
void

player:attack(obj)
Parameters
hero.obj player
obj obj
Return Value
void

player:altattack(obj)
Parameters
hero.obj player
obj obj
Return Value
void

player:stop()
Parameters
hero.obj player
Return Value
void

player:select(n)
Parameters
hero.obj player
number index
Return Value
void

player:castSpell(type, slot, arg1, arg2, no_limit)
Parameters
hero.obj player
string type
number slot
mixed arg1
mixed arg2
boolean no_limit: ignore the limitation check, maybe kickout/banned, use careful
Return Value
void

--type 'pos', orianna q
player:castSpell('pos', 0, mousePos)
--type 'obj', teemo q
player:castSpell('obj', 0, game.selectedTarget)
--type 'self', teemo w
player:castSpell('self', 1)
--type 'line', rumble r
player:castSpell('line', 3, player.pos, mousePos)
--type 'release', varus q
player:castSpell('release', 0, player.pos, mousePos)
--type 'move', aurelion sol q
player:castSpell('move', 0, mousePos, nil, true)
--type 'switch', Hwei Q
player:castSpell('switch', 0)
player:levelSpell(slot)
Parameters
hero.obj player
number slot
Return Value
void

player:interact(obj)
Parameters
hero.obj player
obj obj
Return Value
void

player:buyItem(itemID)
Parameters
hero.obj player
number itemID
Return Value
void

The following functions are available for all hero.obj

hero:spellSlot(slot)
Parameters
hero.obj hero
number slot
Return Value
spell_slot.obj

hero:findSpellSlot(spellName)
Parameters
hero.obj hero
string spell name
Return Value
spell_slot.obj

hero:inventorySlot(slot)
Parameters
hero.obj hero
number slot
Return Value
inventory_slot.obj

hero:basicAttack(index)
Parameters
hero.obj hero
number index, -1 to get the default AA spell
Return Value
spell.obj

hero:itemID(slot)
Parameters
hero.obj hero
number slot
Return Value
number returns item ID for item slot i

hero:isPlayingAnimation(animationNameHash)
Parameters
hero.obj hero
number animationNameHash
Return Value
boolean

hero:attackDelay()
Parameters
hero.obj hero
Return Value
number

hero:attackCastDelay(slot)
Parameters
hero.obj hero
number spellSlot
Return Value
number

hero:getPassablePos(to)
Parameters
hero.obj hero
vec3 to pos
Return Value
vec3 The NearstPassable position (the cell center, and include hero collsion state)

hero:baseHealthForLevel(level)
Parameters
number level
Return Value
number returns base health for level i

hero:abilityResourceBase(slot)
Parameters
number slot, usually the slot is 0 for mana
Return Value
number returns ability resource value (with level factor calculated)

hero:abilityResourceForLevel(slot, level)
Parameters
number slot, usually the slot is 0 for mana
number level
Return Value
number returns ability resource value for level

hero:statForLevel(type, level)
Parameters
number type
number level
Return Value
number returns stat of type for level i

hero:getStat(name)
Parameters
string name
Return Value
number returns stat value

The available stats (Some of them are deprecated by riot and always zero):

EXP
GOLD_SPENT
GOLD_EARNED
MINIONS_KILLED
NEUTRAL_MINIONS_KILLED
NEUTRAL_MINIONS_KILLED_YOUR_JUNGLE
NEUTRAL_MINIONS_KILLED_ENEMY_JUNGLE
PLAYER_SCORE_0
PLAYER_SCORE_1
PLAYER_SCORE_2
PLAYER_SCORE_3
PLAYER_SCORE_4
PLAYER_SCORE_5
PLAYER_SCORE_6
PLAYER_SCORE_7
PLAYER_SCORE_8
PLAYER_SCORE_9
PLAYER_SCORE_10
PLAYER_SCORE_11
VICTORY_POINT_TOTAL
TOTAL_DAMAGE_DEALT
PHYSICAL_DAMAGE_DEALT_PLAYER
MAGIC_DAMAGE_DEALT_PLAYER
TRUE_DAMAGE_DEALT_PLAYER
TOTAL_DAMAGE_DEALT_TO_CHAMPIONS
PHYSICAL_DAMAGE_DEALT_TO_CHAMPIONS
MAGIC_DAMAGE_DEALT_TO_CHAMPIONS
TRUE_DAMAGE_DEALT_TO_CHAMPIONS
TOTAL_DAMAGE_TAKEN
PHYSICAL_DAMAGE_TAKEN
MAGIC_DAMAGE_TAKEN
TRUE_DAMAGE_TAKEN
TOTAL_DAMAGE_SELF_MITIGATED
TOTAL_DAMAGE_SHIELDED_ON_TEAMMATES
TOTAL_DAMAGE_DEALT_TO_BUILDINGS
TOTAL_DAMAGE_DEALT_TO_TURRETS
TOTAL_DAMAGE_DEALT_TO_OBJECTIVES
LARGEST_ATTACK_DAMAGE
LARGEST_ABILITY_DAMAGE
LARGEST_CRITICAL_STRIKE
TOTAL_TIME_CROWD_CONTROL_DEALT
TOTAL_TIME_CROWD_CONTROL_DEALT_TO_CHAMPIONS
TOTAL_HEAL_ON_TEAMMATES
TIME_PLAYED
LONGEST_TIME_SPENT_LIVING
TOTAL_TIME_SPENT_DEAD
TIME_OF_FROM_LAST_DISCONNECT
TIME_SPENT_DISCONNECTED
TIME_CCING_OTHERS
TEAM
LEVEL
CHAMPIONS_KILLED
NUM_DEATHS
ASSISTS
LARGEST_KILLING_SPREE
KILLING_SPREES
LARGEST_MULTI_KILL
BOUNTY_LEVEL
DOUBLE_KILLS
TRIPLE_KILLS
QUADRA_KILLS
PENTA_KILLS
UNREAL_KILLS
BARRACKS_KILLED
BARRACKS_TAKEDOWNS
TURRETS_KILLED
TURRET_TAKEDOWNS
HQ_KILLED
HQ_TAKEDOWNS
OBJECTIVES_STOLEN
OBJECTIVES_STOLEN_ASSISTS
FRIENDLY_DAMPEN_LOST
FRIENDLY_TURRET_LOST
FRIENDLY_HQ_LOST
BARON_KILLS
DRAGON_KILLS
RIFT_HERALD_KILLS
HORDE_KILLS
ATAKHAN_KILLS
NODE_CAPTURE
NODE_CAPTURE_ASSIST
NODE_NEUTRALIZE
NODE_NEUTRALIZE_ASSIST
TEAM_OBJECTIVE
ITEMS_PURCHASED
CONSUMABLES_PURCHASED
ITEM0
ITEM1
ITEM2
ITEM3
ITEM4
ITEM5
ITEM6
PERK_PRIMARY_STYLE
PERK_SUB_STYLE
STAT_PERK_0
STAT_PERK_1
STAT_PERK_2
SIGHT_WARDS_BOUGHT_IN_GAME
VISION_WARDS_BOUGHT_IN_GAME
WARD_PLACED
WARD_KILLED
WARD_PLACED_DETECTOR
VISION_SCORE
SPELL1_CAST
SPELL2_CAST
SPELL3_CAST
SPELL4_CAST
SUMMON_SPELL1_CAST
SUMMON_SPELL2_CAST
KEYSTONE_ID
TOTAL_HEAL
TOTAL_UNITS_HEALED
WAS_AFK
WAS_AFK_AFTER_FAILED_SURRENDER
WAS_EARLY_SURRENDER_ACCOMPLICE
TEAM_EARLY_SURRENDERED
GAME_ENDED_IN_EARLY_SURRENDER
GAME_ENDED_IN_SURRENDER
WAS_SURRENDER_DUE_TO_AFK
WAS_LEAVER
PLAYERS_I_MUTED
hero:isValidTarget(range)
Parameters
hero.obj hero
number range, optional
Return Value
bool Returns whether this is valid target to self or not

hero:findBuff(hash)
Equal to player.buff["some_name"] and player.buff["some_name"] or nil
Parameters
hero.obj hero
int fnv hash of buff name
Return Value
buff.obj

hero:getBuffStacks(hash)
Equal to player.buff["some_name"] and player.buff["some_name"].stacks or 0
Parameters
hero.obj hero
int fnv hash of buff name
Return Value
int

hero:getBuffCount(hash)
Equal to player.buff["some_name"] and player.buff["some_name"].stacks2 or 0
Parameters
hero.obj hero
int fnv hash of buff name
Return Value
int

local ezrealpassivestacks = game.fnvhash("ezrealpassivestacks")
cb.add(cb.tick, function()
    local stacks = player:getBuffStacks(ezrealpassivestacks)
    if stacks > 0 then
        -- do somethings
    end
end)
hero:getTeamBuffCount(team, buffType)
Parameters
hero.obj hero
int team
int buffType
Return Value
int

The available buffType: DragonFireStacks = 0x1, DragonAirStacks = 0x2, DragonWaterStacks = 0x3, DragonEarthStacks = 0x4, * DragonElderStacks = 0x5,

print(player:getTeamBuffCount(TEAM_ALLY, 0))
print(player:getTeamBuffCount(TEAM_ENEMY, 0))
minion.obj
Properties:

string minion.name
string minion.charName
boolean minion.isOnScreen
boolean minion.isDead
boolean minion.isAlive
boolean minion.isVisible
boolean minion.isTargetable
vec3 minion.pos
vec3 minion.direction
vec2 minion.pos2D
vec2 minion.direction2D
vec2 minion.barPos
path.obj minion.path
hero.obj minion.owner
spell.obj minion.activeSpell
table minion.buff
number minion.networkID
number minion.networkID32
number minion.team
number minion.x
number minion.y
number minion.z
number minion.selectionHeight
number minion.selectionRadius
number minion.boundingRadius
number minion.overrideCollisionRadius
number minion.pathfindingCollisionRadius
vec3 minion.minBoundingBox
vec3 minion.maxBoundingBox
number minion.deathTime
number minion.health
number minion.maxHealth
number minion.mana
number minion.maxMana
number minion.maxHealthPenalty
number minion.physicalShield
number minion.magicalShield
number minion.allShield
number minion.stopShieldFade
number minion.isTargetableToTeamFlags
number minion.actionState
number minion.baseAttackDamage
number minion.baseAd
alias of baseAttackDamage

number minion.bonusAd
number minion.totalAd
The TotalAttackDamage

number minion.totalAp
The TotalAbilityPower

number minion.armor
number minion.spellBlock
number minion.attackSpeedMod
number minion.flatPhysicalDamageMod
number minion.percentPhysicalDamageMod
number minion.flatMagicDamageMod
number minion.percentMagicDamageMod
number minion.healthRegenRate
number minion.bonusArmor
number minion.bonusSpellBlock
number minion.moveSpeed
number minion.moveSpeedBaseIncrease
number minion.baseAbilityDamage
number minion.attackRange
number minion.flatMagicReduction
number minion.percentMagicReduction
number minion.flatArmorPenetration
number minion.percentArmorPenetration
number minion.flatMagicPenetration
number minion.percentMagicPenetration
number minion.physicalLethality
number minion.magicLethality
number minion.flatBaseAttackDamageMod
number minion.percentBaseAttackDamageMod
number minion.baseAttackDamageSansPercentScale
number minion.exp
number minion.par
number minion.maxPar
number minion.parEnabled
number minion.percentDamageToBarracksMinionMod
number minion.flatDamageReductionFromBarracksMinionMod
bool minion.isMelee
bool minion.isRanged
bool minion.isClone
bool minion.isLaneMinion
bool minion.isEliteMinion
bool minion.isEpicMinion
bool minion.isJungleMonster
bool minion.isLaneMinion
bool minion.isSiegeMinion
bool minion.isSuperMinion
bool minion.isCasterdMinion
bool minion.isMeleeMinion
bool minion.isBarrel
bool minion.isPet
bool minion.isCollidablePet
bool minion.isTrap
bool minion.isWard
bool minion.isWardNoBlue
bool minion.isPlant
bool minion.isMonster
bool minion.isLargeMonster
bool minion.isBaron
bool minion.isDragon
bool minion.isEpicMonster
bool minion.isSmiteMonster
m:basicAttack(index)
Parameters
minion.obj m
number index, -1 to get the default AA spell
Return Value
spell.obj returns the minions basic attack

m:isPlayingAnimation(animationNameHash)
Parameters
minion.obj m
number animationNameHash
Return Value
boolean

m:attackDelay()
Parameters
minion.obj m
Return Value
number

m:attackCastDelay(slot)
Parameters
minion.obj m
number spellSlot
Return Value
number

m:isValidTarget(range)
Parameters
m.obj m
number range, optional
Return Value
bool Returns whether this is valid target to self or not

turret.obj
Properties:

string turret.name
string turret.charName
boolean turret.isOnScreen
boolean turret.isDead
boolean turret.isAlive
boolean turret.isVisible
boolean turret.isTargetable
vec3 turret.pos
vec3 turret.direction
vec2 turret.pos2D
vec2 turret.barPos
vec2 turret.direction2D
path.obj turret.path
spell.obj turret.activeSpell
table turret.buff
number turret.networkID
number turret.networkID32
number turret.team
number turret.x
number turret.y
number turret.z
number turret.selectionHeight
number turret.selectionRadius
number turret.boundingRadius
number turret.overrideCollisionRadius
number turret.pathfindingCollisionRadius
vec3 turret.minBoundingBox
vec3 turret.maxBoundingBox
number turret.deathTime
number turret.health
number turret.maxHealth
number turret.mana
number turret.maxMana
number turret.allShield
number turret.isTargetableToTeamFlags
number turret.baseAttackDamage
number turret.baseAd
alias of baseAttackDamage

number turret.bonusAd
number turret.totalAd
The TotalAttackDamage

number turret.totalAp
The TotalAbilityPower

number turret.armor
number turret.spellBlock
number turret.attackSpeedMod
number turret.flatPhysicalDamageMod
number turret.percentPhysicalDamageMod
number turret.flatMagicDamageMod
number turret.percentMagicDamageMod
number turret.healthRegenRate
number turret.bonusArmor
number turret.bonusSpellBlock
number turret.baseAbilityDamage
number turret.parRegenRate
number turret.attackRange
number turret.flatMagicReduction
number turret.percentMagicReduction
number turret.flatArmorPenetration
number turret.percentArmorPenetration
number turret.flatMagicPenetration
number turret.percentMagicPenetration
number turret.percentBonusArmorPenetration
number turret.percentBonusMagicPenetration
number turret.physicalLethality
number turret.magicLethality
number turret.baseHealthRegenRate
number turret.secondaryARBaseRegenRateRep
number turret.flatBaseAttackDamageMod
number turret.percentBaseAttackDamageMod
number turret.baseAttackDamageSansPercentScale
number turret.exp
number turret.par
number turret.maxPar
number turret.parEnabled
number turret.physicalDamagePercentageModifier
number turret.magicalDamagePercentageModifier
bool turret.isMelee
bool turret.isRanged
number turret.tier
Base=1, Inner=2, Outer=3, NexusRight=4, NexusLeft=5

number turret.lane
Bottom=0, Mid=1, Top=2

t:basicAttack(i)
Parameters
turret.obj t
number i
Return Value
spell.obj returns the turrets basic attack

t:isPlayingAnimation(animationNameHash)
Parameters
turret.obj t
number animationNameHash
Return Value
boolean

t:attackDelay()
Parameters
turret.obj t
Return Value
number

t:attackCastDelay(slot)
Parameters
turret.obj t
number spellSlot
Return Value
number

t:isValidTarget(range)
Parameters
turret.obj t
number range, optional
Return Value
bool Returns whether this is valid target to self or not

inhib.obj
Properties:

number inhib.type
number inhib.index
number inhib.team
number inhib.networkID
number inhib.networkID32
string inhib.name
number inhib.x
number inhib.y
number inhib.z
vec3 inhib.pos
vec2 inhib.pos2D
boolean inhib.isOnScreen
number inhib.selectionHeight
number inhib.selectionRadius
number inhib.boundingRadius
number inhib.overrideCollisionRadius
number inhib.pathfindingCollisionRadius
vec3 inhib.minBoundingBox
vec3 inhib.maxBoundingBox
boolean inhib.isDead
boolean inhib.isAlive
boolean inhib.isVisible
number inhib.deathTime
number inhib.health
number inhib.maxHealth
number inhib.allShield
boolean inhib.isTargetable
number inhib.isTargetableToTeamFlags
inhib:isValidTarget(range)
Parameters
inhib.obj inhib
number range, optional
Return Value
bool Returns whether this is valid target to self or not

nexus.obj
Properties:

string nexus.name
boolean nexus.isOnScreen
boolean nexus.isDead
boolean nexus.isAlive
boolean nexus.isVisible
boolean nexus.isTargetable
vec3 nexus.pos
vec2 nexus.pos2D
number nexus.team
number nexus.type
number nexus.x
number nexus.y
number nexus.z
number nexus.health
number nexus.maxHealth
number nexus.allShield
number nexus.isTargetableToTeamFlags
nexus:isValidTarget(range)
Parameters
nexus.obj nexus
number range, optional
Return Value
bool Returns whether this is valid target to self or not

missile.obj
Properties:

number missile.type
number missile.index
number missile.index32
number missile.team
string missile.name
number missile.networkID
number missile.networkID32
boolean missile.isOnScreen
number missile.selectionHeight
number missile.selectionRadius
vec3 missile.minBoundingBox
vec3 missile.maxBoundingBox
number missile.x
number missile.y
number missile.z
vec3 missile.pos
vec2 missile.pos2D
The x/y/z/pos/pos2D now means minBoundingBox of GameObject

spell.obj missile.spell
base.obj missile.caster
base.obj missile.target
number missile.width
number missile.velocity
missile_info.obj missile.missile_info
The MissileMovement of Missile, And follow properties is getting from this as a alias:

vec3 missile.startPos
vec2 missile.startPos2D
vec3 missile.endPos
vec2 missile.endPos2D
number missile.speed
particle.obj
Properties:

number particle.type
number particle.index
number particle.index32
number particle.team
string particle.name
number particle.networkID
number particle.networkID32
number particle.x
number particle.y
number particle.z
number particle.effectKey
The hash of effect resource name, only valid for effects from spell (invalid for missile/sound/etc)

vec3 particle.pos
vec2 particle.pos2D
vec3 particle.initPos
The initial position when effect created, only valid for effects from spell

vec3 particle.initTargetPos
The initial target position when effect created, only valid for effects from spell

vec3 particle.initOrientation
The initial orientation when effect created, only valid for effects from spell

vec3 particle.direction
The effect drection when rendering.

boolean particle.isOnScreen
number particle.selectionHeight
number particle.selectionRadius
vec3 particle.minBoundingBox
vec3 particle.maxBoundingBox
obj particle.caster
The initial caster/emitter when effect created, only valid for effects from spell, could be nil

obj particle.target
The initial target when effect created, only valid for effects from spell, could be nil

obj particle.attachmentObject
obj particle.targetAttachmentObject
spell.obj
known as SpellCastInfo

Properties:

number spell.identifier
The missileNetworkID of SpellCastInfo

number spell.slot
boolean spell.isBasicAttack
obj spell.owner
boolean spell.hasTarget
obj spell.target
vec3 spell.startPos
vec2 spell.startPos2D
LaunchPosition

vec3 spell.endPos
vec2 spell.endPos2D
TargetPosition

vec3 spell.endPosLine
vec2 spell.endPosLine2D
TargetEndPosition

boolean spell.useChargeChanneling
boolean spell.channelingFinished
boolean spell.spellCasted
number spell.windUpTime
DesignerCastTime + ExtraTimeForCast

number spell.animationTime
DesignerTotalTime

number spell.clientWindUpTime
CharacterAttackCastDelay

number spell.startTime
TimeTillPlayAnimation

number spell.castEndTime
TimeCast

number spell.endTime
TimeSpellEnd

spell_info.obj spell.spell_info
The SpellData of current SpellCastInfo

string spell.name
equal to spell.spell_info.name

string spell.hash
equal to spell.spell_info.hash

spell_static.obj spell.static
equal to spell.spell_info.static

spell_slot.obj
known as SpellDataInst

Properties:

number spell_slot.slot
boolean spell_slot.isBasicSpellSlot
boolean spell_slot.isSummonerSpellSlot
string spell_slot.name
number spell_slot.hash
texture.obj spell_slot.icon
number spell_slot.targetingType
number spell_slot.level
number spell_slot.cooldown
number spell_slot.totalCooldown
number spell_slot.iconUsed
For champion like Bel’Veth, the index of spell icon (Q Spell)

number spell_slot.startTimeForCurrentCast
number spell_slot.displayRange
number spell_slot.currentNumericalDisplay
number spell_slot.stacks
CurrentAmmo

number spell_slot.stacksCooldown
TimeForNextAmmoRecharge

number spell_slot.state
number spell_slot.toggleState
boolean spell_slot.isCharging
boolean spell_slot.isNotEmpty
Whether current spell_slot has a active spell_info

spell_info.obj spell_slot.spell_info
spell_static.obj spell_slot.static
spell_slot:getTooltip(type)
Parameters
spell_slot.obj
int tooltip type: 0
Return Value
string

spell_slot:getTooltipVar(index)
Return the numerical variable when mouseover the skill icons
Parameters
spell_slot.obj
int tooltip var index: [0,15]
Return Value
number

spell_slot:getEffectAmount(index, level)
Return the fixed class value of the target skill
Parameters
spell_slot.obj
int index: [1,11]
int level: [0,6]
Return Value
number

spell_slot:calculate(spellNameHash, calculationHash)
Parameters
spell_slot.obj current spell
unsigned int spell name hash, could be 0 if using default spell name
unsigned int calculation type hash
Return Value
void

-- Jhin Example: 
-- https://raw.communitydragon.org/13.15/game/data/characters/jhin/jhin.bin.json
-- JhinW: ["Characters/Jhin/Spells/JhinRAbility/JhinW"].mSpell.mSpellCalculations
-- JhinR: ["Characters/Jhin/Spells/JhinRAbility/JhinR"].mSpell.mSpellCalculations

local calcHashW = game.fnvhash('TotalDamage')
local rawDamageW = player:spellSlot(0):calculate(0, calcHashW)

local calcHashR = game.fnvhash('DamageCalc')
local rawDamageR = player:spellSlot(3):calculate(0, calcHashR)


-- AatroxQ Example:
-- The `spellSlot(0).name` could be "AatroxQ"/"AatroxQ2"/"AatroxQ3"
-- We need use "AatroxQ" to calculate the damage.

local QDamage = game.fnvhash('QDamage')
local AatroxQ = game.spellhash('AatroxQ')
local rawDamageQ1 = player:spellSlot(0):calculate(AatroxQ, QDamage)
local rawDamageQ2 = player:spellSlot(0):calculate(AatroxQ, QDamage) * 1.25
local rawDamageQ3 = player:spellSlot(0):calculate(AatroxQ, QDamage) * 1.25 * 1.25
spell_slot:getDamage(target, spellHash, stage)
Parameters
spell_slot.obj current spell
int spell name hash, could be 0 if using default spell name
int stage, 0 for default, 1/2/3 for custom usage
Return Value
void

-- AatroxQ
local spellHash_AatroxQ = game.spellhash('AatroxQ')
local spellHash_AatroxQ2 = game.spellhash('AatroxQ2')
local spell = player:spellSlot(_Q)
print(spell:getDamage(target, spellHash_AatroxQ, 0)) -- Q damage
print(spell:getDamage(target, spellHash_AatroxQ, 1)) -- Q edge damage
print(spell:getDamage(target, spellHash_AatroxQ2, 0)) -- Q2 damage (need pass AatroxQ2 as arg1)

-- AkaliP
local spellHash_AkaliP = game.spellhash('AkaliP')
local spell = player:spellSlot(63) -- 63 = Passive
print(spell:getDamage(target, spellHash_AkaliP, 3)) -- Akali passive damage for stage 3

-- AurelionSolR
local spellHash_AurelionSolR = game.spellhash('AurelionSolR')
local spell = player:spellSlot(_R)
print(spell:getDamage(target, spellHash_AurelionSolR, 0)) -- AurelionSol R damage
print(spell:getDamage(target, spellHash_AurelionSolR, 1)) -- AurelionSol R2 damage
spell_info.obj
known as SpellData

Properties:

string spell_info.name
number spell_info.hash
the fnvhash of name

spell_static.obj spell_info.static
spell_static.obj
known as SpellDataResource

Properties:

string spell_static.alternateName
string spell_static.imgIconName
number spell_static.castType
Flags

boolean spell_static.useMinimapTargeting
number spell_static.missileSpeed
number spell_static.lineWidth
number spell_static.castFrame
inventory_slot.obj
Properties:

number inventory_slot.stacks
number inventory_slot.purchaseTime
boolean inventory_slot.hasItem
item_data.obj inventory_slot.item
texture.obj inventory_slot.icon
number inventory_slot.spellStacks
number inventory_slot.id
number inventory_slot.maxStacks
number inventory_slot.cost
string inventory_slot.name
string inventory_slot.iconName
texture.obj inventory_slot.icon
inventory_slot:getTooltip(type)
Parameters
inventory_slot.obj current inventory
int tooltip type: 0
Return Value
string

inventory_slot:calculate(calculationHash)
Parameters
inventory_slot.obj current inventory
unsigned int calculation type hash
Return Value
void

item_data.obj
Properties:

number item_data.id
number item_data.maxStacks
number item_data.cost
string item_data.name
string item_data.iconName
texture.obj item_data.icon
path.obj
Properties:

obj path.owner
boolean path.isActive
boolean path.isMoving
Sometimes player is moving but path.isActive will be false (eg: no path for short move).

boolean path.isDashing
number path.dashSpeed
boolean path.unstoppable
vec3 path.endPos
vec2 path.endPos2D
vec3 path.serverPos
vec2 path.serverPos2D
vec3 path.serverVelocity
vec2 path.serverVelocity2D
vec3 path.startPoint
vec3 path.endPoint
vec3[] path.point
WaypointList array

vec2[] path.point2D
WaypointList array

number path.index
The index of next point

number path.count
The count of point/point2D

number path.update
UpdateNumber

cb.add(cb.path, function (obj)
  if obj.ptr == player.ptr then
    print("--------newpath")
    print("isActive: " .. tostring(obj.path.isActive))
    print("index: " .. obj.path.index)
    print("count: " .. obj.path.count)

    local pos = obj.path.serverPos
    print("serverPos: " .. pos.x .. "," .. pos.y .. "," .. pos.z)
    print("isDashing: " .. tostring(obj.path.isDashing))

    if obj.path.isDashing then
      print("dashSpeed: " .. obj.path.dashSpeed)
    end
  end
end)

cb.add(cb.draw, function()
    if player.path.active then
        graphics.draw_line_strip(player.path.point, 2, 0xFFFFFFFF, player.path.count+1)
    end
end)
p:calcPos(v1)
Parameters
path.obj p
vec3 v1
Return Value
vec3[] returns vec3 array containing path points
number returns array length

cb.add(cb.draw, function()
  local p, n = player.path:calcPos(mousePos)
  for i = 0, n - 1 do
    graphics.draw_circle(p[i], 15, 2, 0xffffffff, 3)
  end
end)
p:isDirectPath(v1, v2)
Parameters
path.obj p
vec3 v1
vec3 v2
Return Value
boolean returns result
number[] return lastReachedPos

local isDirect,lastReachedPos = player.path:isDirectPath(player.pos, mousePos)
if isDirect then
    print('lastReachedPos.x', lastReachedPos[0])
    print('lastReachedPos.y', lastReachedPos[1])
end
buff.obj
Warning:
buff.obj are temporary objects, their memory may change or become invalid at any time, please do not save buff.obj anywhere

Properties:

number buff.type
string buff.name
number buff.hash
The fnvhash of buff.name

boolean buff.valid
obj buff.owner
obj buff.source
number buff.startTime
number buff.endTime
number buff.stacks
Number of buff layers

number buff.stacks2
The buff Counter

number buff.count
same to stacks2

buff:hasSource(src)
Parameters
buff.obj
base.obj game object: hero.obj/minion.obj/turret.obj
Return Value
bool

buff:getTooltipVar(index)
Return the numerical variable when mouseover the skill icons
Parameters
buff.obj
int tooltip var index: [0,15]
Return Value
number

runemanager.obj
Properties:

number runemanager.size
runemanager:get(index)
Parameters
runemanager.obj camp
number index: the index to get
Return Value
rune.obj returns the rune of current index

rune.obj
Properties:

number rune.id
string rune.name
texture.obj rune.icon
rune:getTooltipVar(index)
Return the numerical variable when mouseover the icons
Parameters
rune.obj
int tooltip var index: [0,15]
Return Value
number

camp.obj
Properties:

number camp.type
number camp.index
number camp.index32
number camp.team
string camp.name
number camp.networkID
number camp.networkID32
number camp.x
number camp.y
number camp.z
vec3 camp.pos
vec2 camp.pos2D
boolean camp.isOnScreen
number camp.selectionHeight
number camp.selectionRadius
vec3 camp.minBoundingBox
vec3 camp.maxBoundingBox
boolean camp.active
number camp.spawnTime
number camp.count
camp:getMinion(index)
Parameters
camp.obj camp
number index: the index to get
Return Value
minion.obj returns the minion of current index

for i=0,camp.count-1 do
    local minion = camp:getMinion(i)
    if minion then
        print(minion.name, minion.pos.x, minion.pos.z)
    end
end
texture.obj
Properties:
number texture.width number texture.height * string texture.name

shadereffect.obj
effect:show()
Parameters
shadereffect.obj effect
Return Value
void

effect:hide()
Parameters
shadereffect.obj effect
Return Value
void

effect:attach(obj)
Parameters
shadereffect.obj effect
base.obj game object: hero.obj/minion.obj/turret.obj
Return Value
void

effect:update_circle(pos, radius, width, color)
Update the circle info of current shadereffect.
Parameters
shadereffect.obj effect
vec3 effect center pos
number radius
number width
number color, argb (rgb is ignored when effect type is CIRCLE_RAINBOW)
Return Value
void

skillshot.obj (Evade3)
Properties:

obj skillshot.Caster
string skillshot.SpellName
spelldata.obj skillshot.SpellData
number skillshot.DetectionType
cast_info_saved.obj skillshot.SpellCastInfoData
number skillshot.InitTick
number skillshot.StartTick
number skillshot.EndTick
Ticks are based on game.time

number skillshot.TargetHandle
The ObjID

boolean skillshot.IsDummy
boolean skillshot.IsGlobal
boolean skillshot.IsFoW
boolean skillshot.IsRenderEnabled
boolean skillshot.IsVisible
table skillshot.LuaData
vec2 skillshot.StartPosition
vec2 skillshot.OriginalStartPosition
vec2 skillshot.DirectionVector
vec2 skillshot.NormalVector
vec2 skillshot.EndPosition
vec2 skillshot.OriginalEndPosition
vec2 skillshot.CastPosition
number skillshot.StartObjectHandle
number skillshot.EndObjectHandle
The ObjID

boolean skillshot.IsIgnoredFromInside
Member Functions:

skillshot:IsValid()
skillshot:Invalidate()
skillshot:IsActive()
skillshot:Activate()
skillshot:Deactivate()
skillshot:IsEnabledInMenu()
skillshot:IsFoWEnabledInMenu()
skillshot:GetDangerLevelFromMenu()
skillshot:GetDangerLevel()
skillshot:CalculateTicks()
skillshot:IsIgnored()
skillshot:IsIgnoredTemporarily()
skillshot:IgnoreTemporarily(bool)
skillshot:IsValid()
skillshot:Contains(vec2)
skillshot:ContainsPlayer()
skillshot:GetHitTime()
skillshot:GetHitRemainingTime()
spelldata.obj (Evade3)
Properties:

string spelldata.Name
number spelldata.NameHash
table spelldata.SpellNames
table spelldata.MissileNames
number spelldata.Slot
number spelldata.CastDuration
number spelldata.StayDuration
number spelldata.Speed
number spelldata.Range
number spelldata.HitArea
number spelldata.ExtraHitArea
boolean spelldata.IsDynamicHitArea
boolean spelldata.IsGlobal
boolean spelldata.IsFixedRange
boolean spelldata.IsAreaIgnoringHitBox
boolean spelldata.IsRangeIgnoringHitBox
boolean spelldata.IsCrowdControl
cast_info_saved.obj (Evade3)
Properties:

string cast_info_saved.Name
number cast_info_saved.Level
boolean cast_info_saved.HasTarget
number cast_info_saved.SenderHandle
number cast_info_saved.TargetHandle
number cast_info_saved.CastSpeed
number cast_info_saved.Slot
number cast_info_saved.MissileHash
number cast_info_saved.MissileNetworkId
vec2 cast_info_saved.StartPos
vec2 cast_info_saved.EndPos
vec2 cast_info_saved.EndPos2
number cast_info_saved.WindUpTime
number cast_info_saved.AnimationTime
number cast_info_saved.StartTime
number cast_info_saved.CastEndTime
number cast_info_saved.EndTime
number cast_info_saved.Cooldown
boolean cast_info_saved.IsBasicAttack
boolean cast_info_saved.IsSpecialAttack
boolean cast_info_saved.IsInstantCast
boolean cast_info_saved.IsAutoAttack
boolean cast_info_saved.IsChanneling
boolean cast_info_saved.IsBasicAttackSlot
boolean cast_info_saved.IsCharging
boolean cast_info_saved.SpellWasCast
boolean cast_info_saved.IsStopped
boolean cast_info_saved.ChargeEndScheduled
Modules
pred
pred.trace.linear.hardlock(input, seg, obj)
Parameters
table input
seg seg
obj obj
Return Value
boolean returns true if obj is hard crowd controlled


pred.trace.linear.hardlockmove(input, seg, obj)
Parameters
table input
seg seg
obj obj
Return Value
boolean returns true if obj is hard crowd controlled by taunts, fears or charms


pred.trace.circular.hardlock(input, seg, obj)
Parameters
table input
seg seg
obj obj
Return Value
boolean returns true if obj is hard crowd controlled


pred.trace.circular.hardlockmove(input, seg, obj)
Parameters
table input
seg seg
obj obj
Return Value
boolean returns true if obj is hard crowd controlled by taunts, fears or charms


pred.trace.newpath(obj, t1, t2)
Parameters
obj obj
number t1
number t2
Return Value
boolean returns true if obj path has updated t2 seconds ago


pred.core.lerp(path, delay, speed)
Parameters
path.obj path
number delay
number speed
Return Value
vec2 returns interpolated position along path
number returns path index of vec2
boolean returns true if vec2 is the end of path

local pred = module.internal('pred')

local function on_tick()
    if player.path.active and player.path.count>0 then
        local res, res_i, res_b = pred.core.lerp(player.path, 0.5, player.moveSpeed)
        print(('%.2f-%.2f on index %u'):format(res.x, res.y, res_i), res_b)
    end
end

cb.add(cb.tick, on_tick)
pred.core.project(pos, path, delay, projectileSpeed, pathSpeed)
Parameters
vec2 pos
path.obj path
number delay
number projectileSpeed
number pathSpeed
Return Value
vec2 returns projected position along path
number returns path index of vec2
boolean returns true if vec2 is the end of path

local pred = module.internal('pred')

local function on_tick()
    if player.path.active and player.path.count>0 then
        local res, res_i, res_b = pred.core.project(mousePos2D, player.path, 0.5, 1200, player.moveSpeed)
        print(('%.2f-%.2f on index %u'):format(res.x, res.y, res_i), res_b)
    end
end

cb.add(cb.tick, on_tick)
pred.core.project_off(pos, path, delay, projectileSpeed, pathSpeed, offset)
Parameters
vec2 pos
path.obj path
number delay
number projectileSpeed
number pathSpeed
vec2 offset
Return Value
vec2 returns projected position along path
number returns path index of vec2
boolean returns true if vec2 is the end of path

local pred = module.internal('pred')

local function on_tick()
    if player.path.active and player.path.count>0 then
        --same as core.project, but the path is offset
        local res, res_i, res_b = pred.core.project_off(mousePos2D, player.path, 0.5, 1200, player.moveSpeed, vec2(0,100))
        print(('%.2f-%.2f on index %u'):format(res.x, res.y, res_i), res_b)
    end
end

cb.add(cb.tick, on_tick)
pred.core.get_pos_after_time(obj, time)
Parameters
obj obj
number time
Return Value
vec2 returns position of obj after time

local pred = module.internal('pred')

local function on_tick()
    local res = pred.core.get_pos_after_time(player, 0.5)
    print(('%.2f-%.2f'):format(res.x, res.y))
end

cb.add(cb.tick, on_tick)
pred.linear.get_prediction(input, tar, src)
Parameters
table input
obj tar
obj src [optional]
Return Value
seg returns a line segment

--this example is based on caitlyn q
local orb = module.internal('orb')
local ts = module.internal('TS')
local pred = module.internal('pred')

local pred_input = {
  delay = 0.625,
  speed = 2200,
  width = 60,
  range = 1150,
  boundingRadiusMod = 1,
  collision = {
    wall = true,
  },
}

local function trace_filter(seg, obj)
    if seg.startPos:dist(seg.endPos) > pred_input.range then return false end

  if pred.trace.linear.hardlock(pred_input, seg, obj) then
    return true
  end
  if pred.trace.linear.hardlockmove(pred_input, seg, obj) then
    return true
  end
  if pred.trace.newpath(obj, 0.033, 0.500) then
    return true
  end
end

local function target_filter(res, obj, dist)
    if dist > 1500 then return false end
    local seg = pred.linear.get_prediction(pred_input, obj)
    if not seg then return false end
    if not trace_filter(seg, obj) then return false end

    res.pos = seg.endPos
    return true
end

local function on_tick()
    if player:spellSlot(0).state~=0 then return end


    local res = ts.get_result(target_filter)
    if res.pos then
        player:castSpell('pos', 0, vec3(res.pos.x, mousePos.y, res.pos.y))
        orb.core.set_server_pause()
        return true
    end
end

cb.add(cb.tick, on_tick)
pred.circular.get_prediction(input, tar, src)
Parameters
table input
obj tar
obj src [optional]
Return Value
seg returns a line segment

--this example is based on xerath w
local orb = module.internal('orb')
local ts = module.internal('TS')
local pred = module.internal('pred')

local pred_input = {
    delay = 0.75,
    radius = 275,
    speed = math.huge,
    boundingRadiusMod = 0,
}

local function trace_filter(seg, obj)
    if seg.startPos:dist(seg.endPos) > 950 then return false end

  if pred.trace.circular.hardlock(pred_input, seg, obj) then
    return true
  end
  if pred.trace.circular.hardlockmove(pred_input, seg, obj) then
    return true
  end
  if pred.trace.newpath(obj, 0.033, 0.500) then
    return true
  end
end

local function target_filter(res, obj, dist)
    if dist > 1500 then return false end
    local seg = pred.circular.get_prediction(pred_input, obj)
    if not seg then return false end
    if not trace_filter(seg, obj) then return false end

    res.pos = seg.endPos
    return true
end

local function on_tick()
    if player:spellSlot(1).state~=0 then return end


    local res = ts.get_result(target_filter)
    if res.pos then
        player:castSpell('pos', 1, vec3(res.pos.x, mousePos.y, res.pos.y))
        orb.core.set_server_pause()
        return true
    end
end

cb.add(cb.tick, on_tick)
pred.present.get_source_pos(obj)
Parameters
obj obj
Return Value
vec2 returns server pos of obj

pred.present.get_prediction(input, tar, src)
Parameters
table input
obj tar
obj src [optional]
Return Values
vec2

--this example is based on kindred e
local orb = module.internal('orb')
local ts = module.internal('TS')
local pred = module.internal('pred')

local pred_input = {
  delay = 0,
  radius = player.attackRange,
  dashRadius = 0,
  boundingRadiusModSource = 1,
  boundingRadiusModTarget = 1,
}

local function target_filter(res, obj, dist)
    if dist>800 then return false end
    if not pred.present.get_prediction(pred_input, obj) then return false end

    res.obj = obj
    return true
end

local function invoke()
    if player:spellSlot(2).state~=0 then return end

    local res = ts.get_result(target_filter)
    if res.obj then
        player:castSpell('obj', 2, res.obj)
        orb.core.set_server_pause()
        return true
  end
end
pred.collision.get_prediction(input, pred_result, ign_obj)
Parameters
table input
seg pred_result
obj ign_obj [optional]
Return Values
table returns a table of objects that pred_result will collide with or nil if no collision is detected

--this example is based on morgana q
local orb = module.internal('orb')
local ts = module.internal('TS')
local pred = module.internal('pred')

local pred_input = {
  delay = 0.25,
  speed = 1200,
  width = 70,
  boundingRadiusMod = 1,
  range = 1100,
  collision = {
    minion = true, --checks collision for minions
    hero = false, --no need to check for hero collision

    -- checks all collisions: YasuoWall/SamiraWall/BraumWall/PantheonWall/MelWall
    wall = true, 

    -- or checks specific collisions
    -- wall = { yasuo = true, samira = true, braum = false, pantheon = false, mel = false },
  },
}

local function trace_filter(seg, obj, dist)
    if seg.startPos:dist(seg.endPos) > pred_input.range then
        return false
    end
  if pred.trace.newpath(obj, 0.033, 0.500) and dist < 600 then
        return true
  end
  if pred.trace.linear.hardlock(pred_input, seg, obj) then
    return true
  end
  if pred.trace.linear.hardlockmove(pred_input, seg, obj) then
    return true
  end
end

local function target_filter(res, obj, dist)
    if dist > pred_input.range then return false end
    local seg = pred.linear.get_prediction(pred_input, obj)
    if not seg then return false end
    if not trace_filter(seg, obj, dist) then return false end
    --pass obj as third arg, it will then be ignored from collision checks
    if pred.collision.get_prediction(pred_input, seg, obj) then return false end

    res.pos = seg.endPos
    return true
end

local function on_tick()
  if player:spellSlot(0).state~=0 then return end

  local res = ts.get_result(target_filter)
  if res.pos then
    player:castSpell('pos', 0, vec3(res.pos.x, mousePos.y, res.pos.y))
    orb.core.set_server_pause()
    return true
  end
end

cb.add(cb.tick, on_tick)
orb
orb.core.akshan_should_double_attack
Return Value
boolean

local orb = module.internal('orb')
if xxx then
    orb.core.akshan_should_double_attack = true  -- set it in your AIO logic
end
orb.core.cur_attack_target
Return Value
obj readonly, the current attack target

orb.core.cur_attack_name
Return Value
string readonly, the current AA spell name

orb.core.cur_attack_start_client
Return Value
number readonly, the time when current attack started, based on os.clock()

orb.core.cur_attack_start_server
Return Value
number readonly, the server time when current attack started, based on os.clock()

orb.core.cur_attack_speed_mod
Return Value
number readonly, the attack speed mod for current attack

orb.core.next_attack
Return Value
number readonly, time for next attack

orb.core.next_action
Return Value
number readonly, time for next action

orb.core.next_action
Return Value
number readonly, time for next action

orb.core.reset()
Return Value
void

local orb = module.internal('orb')
orb.core.reset() --resets the orbwalks attack cooldown
orb.core.can_action()
Return Value
boolean returns true if player can move or cast spells

local orb = module.internal('orb')

local function on_tick()
    print(orb.core.can_action() and 'can action' or 'can not action')
end

cb.add(cb.tick, on_tick)
orb.core.can_move()
Return Value
boolean returns true if player can move

orb.core.can_attack()
Return Value
boolean returns true if player can attack

local orb = module.internal('orb')

local function on_tick()
    print(orb.core.can_attack() and 'can attack' or 'can not attack')
end

cb.add(cb.tick, on_tick)
orb.core.can_cast_spell(spellSlot, ignore_can_action_check)
Return Value
boolean returns true if player can cast spell

orb.core.is_paused()
Return Value
boolean returns true if the orb is paused (will not issue any orders)

local orb = module.internal('orb')

local function on_tick()
    print(orb.core.is_paused() and 'is paused' or 'is not paused')
end

cb.add(cb.tick, on_tick)
orb.core.is_move_paused()
Return Value
boolean returns true if the orb movement is paused (will not issue move orders)

local orb = module.internal('orb')

local function on_tick()
    print(orb.core.is_move_paused() and 'move is paused' or 'move is not paused')
end

cb.add(cb.tick, on_tick)
orb.core.is_attack_paused()
Return Value
boolean returns true if the orb attacks are paused (will not issue attack orders)

local orb = module.internal('orb')

local function on_tick()
    print(orb.core.is_attack_paused() and 'attack is paused' or 'attack is not paused')
end

cb.add(cb.tick, on_tick)
orb.core.is_spell_locked()
Return Value
boolean returns true if spells are paused

orb.core.is_winding_up_attack()
Return Value
boolean returns true if winding up

orb.core.set_pause(t)
Parameters
number t
Return Value
void

local orb = module.internal('orb')

local function on_process_spell(spell)
    if spell.owner==player and spell.name=='AnnieQ' then
        orb.core.set_pause(0.25)
    end
end

cb.add(cb.spell, on_process_spell)
orb.core.set_pause_move(t)
Parameters
number t
Return Value
void

orb.core.set_pause_attack(t)
Parameters
number t
Return Value
void

local orb = module.internal('orb')

local function on_process_spell(spell)
    if spell.owner==player and spell.name=='XayahR' then
        orb.core.set_pause_attack(1.5)
    end
end

cb.add(cb.spell, on_process_spell)
orb.core.set_server_pause()
Return Value
void

local orb = module.internal('orb')

local function on_tick()
  if orb.core.is_paused() or not orb.core.can_action() or orb.core.is_move_paused() then return end

    if player:spellSlot(0).state~=0 then return end
    player:castSpell('pos', 0, mousePos)
    orb.core.set_server_pause()
end

cb.add(cb.tick, on_tick)
orb.core.set_server_pause_attack()
Return Value
void

orb.core.set_pause_spell_lock()
Return Value
void

orb.core.set_server_pause_spell_lock()
Return Value
void

orb.core.set_pause_strict_limitation(t)
The anti-disconnect feature of orbwalker in CN region is based on facing direction, while during some special spell casting like MelQ, facing direction will be changed very quickly. So we add this API to pause the strict anti-disconnect feature
But shits anti-cheat in CN will not fix this. You maybe got banned/kicked out from game by pause this feature, and which is also the reason why normal players are banned (like VarusQ).
Parameters
number t
Return Value
void

orb.core.on_after_attack(callback)
Parameters
function callabck
Register a callback, which will be triggered once after a attack windup.

orb.core.on_player_attack(callback)
Parameters
function callabck
Register a callback, which will be triggered when a attack is responsed by server

orb.core.on_advanced_after_attack(callback)
Parameters
function callabck
Register a callback, which will be triggered when last attack is finished and could action now (called continuously after windup until attack ready again).

orb.core.time_to_next_attack()
Return Value
number the left seconds to issue next attack

orb.core.is_waiting_for_server_response(spellSlot)
Return Value
boolean

orb.core.is_waitting_for_cooldown(spellSlot)
Return Value
boolean

orb.combat.is_active()
Return Value
boolean returns true if combat mode is active

local orb = module.internal('orb')

local function on_tick()
  if orb.combat.is_active() then
        print('combat active')
    end
end

cb.add(cb.tick, on_tick)
orb.combat.target
Return Value
hero.obj the current attack hero (valid only for current tick)

local orb = module.internal('orb')

local function on_tick()
  if orb.combat.is_active() and your_logic() then
    orb.combat.target = your_logic_target()
  end
end

cb.add(cb.tick, on_tick)
orb.combat.pos
Return Value
hero.obj the prefer position (valid only for current tick) will orbwalker to

orb.combat.get_target()
orb.combat.set_target(t)
orb.combat.get_pos()
orb.combat.set_pos(t)
orb.combat.get_active()
orb.combat.set_active(t)
orb.combat.register_f_pre_attack(func)
Parameters
function func
Return Value
void

local orb = module.internal('orb')

local function on_pre_attack()
  print('will attack', orb.combat.target)
  -- you can set to new target
  -- orb.combat.target = xxxx
end

orb.combat.register_f_pre_attack(on_pre_attack)
orb.combat.register_f_after_attack(func)
Parameters
function func
Return Value
void

local orb = module.internal('orb')

local function on_after_attack()
  print('attack is on cooldown')
  -- you can return true to block other callbacks (Like callbacks registered by other plugin/module)
  -- return true 

  -- [on_after_attack] is called continuously, set bool to false can stop subsequent calls for the current attack.
  -- orb.combat.set_invoke_after_attack(bool) 
end

orb.combat.register_f_after_attack(on_after_attack)
orb.combat.register_f_out_of_range(func)
Parameters
function func
Return Value
void

local orb = module.internal('orb')

local function out_of_range()
  print('there is no target in aa range')
  -- you can return true to block other callbacks
  -- return true 
end

orb.combat.register_f_out_of_range(out_of_range)
orb.combat.register_f_pre_tick(func)
Parameters
function func
Return Value
void

local orb = module.internal('orb')

local function on_tick() --this fucntion is triggered prior to the orbs tick
  -- you can return true to block other callbacks
  -- return true 
end

orb.combat.register_f_pre_tick(on_tick)
orb.combat.set_invoke_after_attack(val)
Parameters
boolean val
Return Value
void

set to false, then orb will stop triggering [register_f_after_attack] events

local orb = module.internal('orb')

local function after_attack()
  print('attack is on cooldown')
  orb.combat.set_invoke_after_attack(false)
  -- you can return true to block other callbacks
  -- return true 
end

orb.combat.register_f_after_attack(after_attack)
orb.farm.clear_target
Return Value
obj the orbs current lane clear target

local orb = module.internal('orb')

local function on_tick()
  if orb.farm.clear_target then
        print(orb.farm.clear_target.charName)
    end
end

cb.add(cb.tick, on_tick)
orb.farm.lane_clear_wait()
Return Value
boolean returns true if the orb is currently waiting to attack a minion that is soon to die (or waiting for siege)

local orb = module.internal('orb')

local function on_tick()
  if orb.farm.lane_clear_wait() then
        print('lane clear is waiting to attack a minion')
    end
end

cb.add(cb.tick, on_tick)
orb.farm.lane_clear_wait_target
Return Value
obj the orbs current lane clear wait target

orb.farm.predict_hp(obj, time)
Parameters
minion.obj obj
number time
Return Value
number returns the minions health after time (seconds)

local orb = module.internal('orb')

local function on_tick()
    for i=0, objManager.minions.size[TEAM_ENEMY]-1 do
        local obj = objManager.minions[TEAM_ENEMY][i]
        if obj.isVisible then
            local hp = orb.farm.predict_hp(obj, 0.25)
            print(obj.charName, obj.health, hp)
        end
    end
end

cb.add(cb.tick, on_tick)
orb.farm.set_ignore(obj, time)
Parameters
minion.obj obj
number time
Return Value
void

orb.farm.skill_farm_linear(input)
Parameters
table input
Return Value
seg returns a line segment
minion.obj returns the minion object

--based on mundo q
local orb = module.internal('orb')
local input = {
    delay = 0.25,
    speed = 2000,
    width = 60,
    boundingRadiusMod = 1,
    range = 1050,
    collision = {
        minion=true,
        walls=true,
        hero=true,
    },
    damage = function(m)
        local q_level = player:spellSlot(0).level
        return math.min(250 + 50*q_level, math.max(30 + 50*q_level, m.health*(.125 + .025*q_level)))
    end,
}

local function on_tick()
    local seg, obj = orb.farm.skill_farm_linear(input)
    if seg then
        player:castSpell('pos', 0, seg.endPos)
    end
end

cb.add(cb.tick, on_tick)
orb.farm.skill_clear_linear(input)
Parameters
table input
Return Value
seg returns a line segment
minion.obj returns the minion object

--based on mundo q
local orb = module.internal('orb')
local input = {
    delay = 0.25,
    speed = 2000,
    width = 60,
    boundingRadiusMod = 1,
    range = 1050,
    collision = {
        minion=true,
        walls=true,
        hero=true,
    },
    damage = function(m)
        local q_level = player:spellSlot(0).level
        return math.min(250 + 50*q_level, math.max(30 + 50*q_level, m.health*(.125 + .025*q_level)))
    end,
}

local function on_tick()
    local seg, obj = orb.farm.skill_clear_linear(input)
    if seg then
        player:castSpell('pos', 0, seg.endPos)
    end
end

cb.add(cb.tick, on_tick)
orb.farm.skill_farm_circular(input)
Parameters
table input
Return Value
seg returns a line segment
minion.obj returns the minion object

--based on karthus q
local orb = module.internal('orb')
local input = {
    delay = 0.75,
    speed = math.huge,
    radius = 200,
    boundingRadiusMod = 0,
    range = 874,
    damage = function(m)
        return 20*player:spellSlot(0).level + 30 + .3*(player.flatMagicDamageMod*player.percentMagicDamageMod)
    end,
}

local function on_tick()
    local seg, obj = orb.farm.skill_farm_circular(input)
    if seg then
        player:castSpell('pos', 0, seg.endPos)
    end
end

cb.add(cb.tick, on_tick)
orb.farm.skill_clear_circular(input)
Parameters
table input
Return Value
seg returns a line segment
minion.obj returns the minion object

--based on karthus q
local orb = module.internal('orb')
local input = {
    delay = 0.75,
    speed = math.huge,
    radius = 200,
    boundingRadiusMod = 0,
    range = 874,
    damage = function(m)
        return 20*player:spellSlot(0).level + 30 + .3*(player.flatMagicDamageMod*player.percentMagicDamageMod)
    end,
}

local function on_tick()
    local seg, obj = orb.farm.skill_clear_circular(input)
    if seg then
        player:castSpell('pos', 0, seg.endPos)
    end
end

cb.add(cb.tick, on_tick)
orb.farm.skill_farm_target(input)
Parameters
table input
Return Value
seg returns a line segment
minion.obj returns the minion object

--based on pantheon q (****2019 version****)
local orb = module.internal('orb')
local input = {
    delay = 0.25,
    speed = 1500,
    range = 468,
    damage = function(target)
        return 35 + 40*player:spellSlot(0).level + 1.4*((player.baseAttackDamage + player.flatPhysicalDamageMod)*player.percentPhysicalDamageMod - player.baseAttackDamage)
    end,
}

local function on_tick()
    local seg, obj = orb.farm.skill_farm_target(input)
    if obj then
        player:castSpell('obj', 0, obj)
    end
end

cb.add(cb.tick, on_tick)
orb.farm.skill_clear_target(input)
Parameters
table input
Return Value
seg returns a line segment
minion.obj returns the minion object

--based on pantheon q
local orb = module.internal('orb')
local input = {
    delay = 0.25,
    speed = 1500,
    range = 468,
    damage = function(target)
        return 35 + 40*player:spellSlot(0).level + 1.4*((player.baseAttackDamage + player.flatPhysicalDamageMod)*player.percentPhysicalDamageMod - player.baseAttackDamage)
    end,
}

local function on_tick()
    local seg, obj = orb.farm.skill_clear_target(input)
    if obj then
        player:castSpell('obj', 0, obj)
    end
end

cb.add(cb.tick, on_tick)
orb.farm.set_clear_target(obj)
Parameters
minion.obj obj
Return Value
void

orb.utility.get_missile_speed(obj)
Parameters
obj obj
Return Value
number returns the obj attack missile speed

orb.utility.get_wind_up_time(obj)
Parameters
obj obj
Return Value
number returns the obj attacks wind up time

orb.utility.get_damage_mod(obj)
Parameters
obj obj
Return Value
number returns the obj damage mod (wards ex)

orb.utility.get_damage(source, target, add_bonus)
Parameters
obj source
obj target
boolean add_bonus
Return Value
number returns attack damage done to target by source

orb.utility.get_hit_time(source, target)
Parameters
obj source
obj target
Return Value
number returns time in seconds for an attack from source to hit target

orb.ts
Return Value
TS returns the orbs target selector instance

orb.menu.combat.key:get()
Return Value
boolean returns true if combat mode is active

orb.menu.lane_clear.key:get()
Return Value
boolean returns true lane clear mode is active

orb.menu.last_hit.key:get()
Return Value
boolean returns true last hit mode is active

orb.menu.hybrid.key:get()
Return Value
boolean returns true if hybrid mode is active

evade
use os.clock()

evade.core.set_server_pause()
Return Value
void

this is deprecated, use evade.core.set_pause instead

evade.core.set_pause(t)
Parameters
number t
Return Value
void

pauses evade from issuing movement orders (will still update orbs path)

local evade = module.seek('evade')
if evade then
    evade.core.set_pause(3) --pauses the evade from taking action for 3 seconds
end
evade.core.is_paused()
Return Value
boolean returns true if evade is currently paused

evade.core.is_active()
Return Value
boolean returns true if evade is currently evading a spell

should be checked before casting any movement impairing spells

evade.core.is_action_safe(v1, speed, delay)
Parameters
vec2\vec3 v1
number speed
number delay
Return Value
boolean returns true if action is safe

--this example is based on vayne q
local evade = module.seek('evade')

local function on_tick()
    if player:spellSlot(0).state~=0 then return end

    local pos = player.path.serverPos2D + (mousePos2D - player.path.serverPos2D):norm()*300
    if evade and evade.core.is_action_safe(pos, 500 + player.moveSpeed, 0) then
        player:castSpell('pos', 0, vec3(pos.x, mousePos.y, pos.y))
    end
end

cb.add(cb.tick, on_tick)
evade.core.get_anchor()
Return Value
void returns the anchor for current evading direction

evade.core.get_pos()
Return Value
void returns the target position of current evading

evade.damage


local ad_damage, ap_damage, true_damage, buff_list = evade.damage.count(player)
--these are reduced/modified by armor/buffs
--buff_list contains an array of all incoming buff types
evade.core.skillshots

Not recommended, prefer evade.core.register_on_create_spell

for i=evade.core.skillshots.n, 1, -1 do
  local spell = evade.core.skillshots[i]
  --spell.name
  --spell.start_time -- based on os.clock()
  --spell.end_time -- based on os.clock()
  --spell.owner
  --spell.danger_level
  --spell.start_pos
  --spell.end_pos
  --spell.damage -- totalDamage
  --spell.data -- assorted static data
  --spell:contains(pos2D/obj)
  --spell:get_hit_time(pos2D)
  --spell:get_hit_remaining_time(pos2D)
  --spell:get_damage(target)

  local ad_damage,ap_damage,true_damage,buff_list = spell:get_damage(player)

  if spell:contains(game.mousePos2D) then
    --mouse is inside of 'spell'
  end

  if spell:contains(player) then
    --player is inside of 'spell', this accounts for obj boundingRadius
  end

  if spell:intersection(player.pos2D, game.mousePos2D) then
    --line seg player->mousePos intersects 'spell'
  end
end
evade.core.targeted

Not recommended, prefer evade.core.register_on_create_spell

for i=evade.core.targeted.n, 1, -1 do
  local spell = evade.core.targeted[i]
  --spell.name
  --spell.start_time
  --spell.end_time
  --spell.owner
  --spell.target
  --spell.missile
  --spell.data -- assorted static data
end
evade.core.register_on_create_spell


evade.core.register_on_create_spell(function (skillshot)

  if not skillshot:contains(player) then
    return
  end

  local ad_damage,ap_damage,true_damage,buff_list = skillshot:get_damage(player) -- show damage to self
  print('create skillshot: ', 
    skillshot.name, 
    skillshot.owner and skillshot.owner.charName or "nil", 
    "time: ",
    string.format("%.2f", skillshot.start_time), 
    string.format("%.2f", skillshot.end_time), -- This is only an approximate time
    "target: ",
    skillshot.target and skillshot.target.charName or "nil",
    "damage: ",
    ad_damage,ap_damage,true_damage,#buff_list
  )

  -- example for anti ViR:
  if skillshot.name == "ViR" then
    delay_action(skillshot.end_time - os.clock() - 0.1, function() use_zhonya() end)
  end

end)
evade.damage.count
local ad_damage, ap_damage, true_damage, buff_list = evade.damage.count(player)
damagelib
damagelib.handlers
Insert your own handlers if internal damagelib is not ok.

local damagelib = module.internal('damagelib')
local handlers = damagelib.handlers

local FlashFrost = game.spellhash('FlashFrost')
local TotalPassthroughDamage = game.fnvhash('TotalPassthroughDamage')
local TotalExplosionDamage = game.fnvhash('TotalExplosionDamage')

handlers[FlashFrost] = function (source, target, is_raw_damage, stage)
    print('FlashFrost: lua handlers called')
    local spell_slot = source:spellSlot(0)
    if not spell_slot then
        return damage_result(0, 0, 0)
    end
    local raw_damage = spell_slot:calculate(0, TotalPassthroughDamage) + spell_slot:calculate(0, TotalExplosionDamage)
    if is_raw_damage or not target or not target.valid then
        return damage_result(0, raw_damage, 0)
    end

    -- return damage: ad,ap,true
    return damage_result(0, damagelib.calc_magical_damage(source, target, raw_damage), 0)
end


-- print damage
local total_damage, ad_damage, ap_damage, true_damage = damagelib.get_spell_damage('FlashFrost', 0, player, g_target, true, 0)
print(total_damage, ad_damage, ap_damage, true_damage)
damagelib.get_spell_damage(spellName, spellSlot, source, target, isRawDamage, stage)
Parameters
string spellName
number spellSlot
obj source
obj target
boolean isRawDamage, true = only spell damage, false = include runes/items/buffs/armors/shieds
number stage
Return Value
number,number,number,number total,ad,ap,true

local damagelib = module.internal('damagelib')

-- Briar
print('Passive min', damagelib.get_spell_damage('BriarP', 63, player, g_target, false, 0))
print('Passive max', damagelib.get_spell_damage('BriarP', 63, player, g_target, false, 1))
print('Q', damagelib.get_spell_damage('BriarQ', 0, player, g_target, false, 0))
print('W', damagelib.get_spell_damage('BriarW', 1, player, g_target, false, 0))
print('W2', damagelib.get_spell_damage('BriarWAttackSpell', 1, player, g_target, false, 0)) -- available when W2 ready
print('E', damagelib.get_spell_damage('BriarE', 2, player, g_target, false, 0))
print('R', damagelib.get_spell_damage('BriarR', 3, player, g_target, false, 0))
damagelib.calc_aa_damage(source, target, includeOnHit)
Calc the real damage after shields, etc.
Parameters
obj source
obj target
boolean include calc_on_hit_damage or not
Return Value
number

damagelib.calc_physical_damage(source, target, rawDamage)
Calc the real damage after shields, etc.
Parameters
obj source
obj target
number rawDamage
Return Value
number

damagelib.calc_magical_damage(source, target, rawDamage)
Calc the real damage after shields, etc.
Parameters
obj source
obj target
number rawDamage
Return Value
number

damagelib.calc_on_hit_damage(source, target, isAutoAttack)
Calc the extra on hit damage by passive/buffs/items/perks.
Parameters
obj source
obj target
boolean isAutoAttack
Return Value
number,number,number,number total,ad,ap,true

local total_damage,ad_damage,ap_damage,true_damage = damagelib.calc_on_hit_damage(player, target, true)
TS
TS.get_result(func, filter, ign_sel, hard)
Parameters
function func
table filter[optional]
boolean ign_sel[optional]
boolean hard[optional]
Return Value
table

--this example is based on annie q
local ts = module.internal('TS')
local pred = module.internal('pred')
local input = {
  delay = 0,
  radius = 505,
  dashRadius = 0,
  boundingRadiusModSource = 1,
  boundingRadiusModTarget = 1,
}

local function ts_filter(res, obj, dist)
    --return false for any objects much too far away
    if dist > 800 then return false end

    --pred.present.get_prediction checks that obj is in range for q
    if pred.present.get_prediction(input, obj) then
        --res.obj is arbitrary and could be named anything
        --additionally, anything you like can be added to the res table
        res.obj = obj
        return true
    end
end

local function on_tick()
    --check that q is ready
    if player:spellSlot(0).state~=0 then return end

    --ts.get_result loops through all valid enemies
    --simple usage
    local res = ts.get_result(ts_filter)
    if res.obj then
        player:castSpell('obj', 0, res.obj)
    end

    --alternative usages
    local res = ts.get_result(ts_filter, ts.filter_set[2]) --uses filter LESS_CAST_AD_PRIO (overrides menu)
    local res = ts.get_result(ts_filter, nil, true) --ignores ts selected target
    local res = ts.get_result(ts_filter, nil, nil, true) --forces ts selected target to be returned (overrides menu)
end

cb.add(cb.tick, on_tick)
TS.get_active_filter()
Return Value
table

local ts = module.internal('TS')
print(ts.get_active_filter().name)
TS.loop(func, filter)
Parameters
function func
table filter[optional]
Return Value
table

local ts = module.internal('TS')

local function loop_filter(res, obj, dist)
    -- any condition can be set here
    if dist < 800 then
        table.insert(res, obj)
    end
end

local function on_tick()
    local res = ts.loop(loop_filter)
    --res will contain all valid enemies within 800 units
end

cb.add(cb.tick, on_tick)
TS.loop_allies(func, filter)
Parameters
function func
table filter[optional]
Return Value
table

local ts = module.internal('TS')

local function loop_filter(res, obj, dist)
    -- any condition can be set here
    if dist < 800 then
        table.insert(res, obj)
    end
end

local function on_tick()
    local res = ts.loop_allies(loop_filter)
    --res will contain all valid allies within 800 units
end

cb.add(cb.tick, on_tick)
TS.filter.new()
Return Value
table

local ts = module.internal('TS')

local my_filter = ts.filter.new()
my_filter.name = 'LEAST_MANA'
my_filter.rank = {0.33, 0.66, 1.0, 1.33, 1.66} --these correspond to character priorities set in the menu, the lower the ratio the higher priority will be given
my_filter.index = function(obj, rank_val)
    return obj.par
    --alternatively, this will return the target with the least mana adjusted by priority
    -- return obj.par * rank_value
end

local function ts_filter(res, obj, dist)
    if dist < 800 then
        res.obj = obj
        return true
    end
end

local res = ts.get_result(ts_filter, my_filter)
TS.filter_set
Return Value
table

local ts = module.internal('TS')
for Nebelwolfi=1, #ts.filter_set do
    print(ts.filter_set[Nebelwolfi].name)
end
TS.selected
Return Value
obj current selected obj

Globals
player
mousePos
mousePos2D
TYPE_TROY
TYPE_HERO
TYPE_MINION
TYPE_MISSILE
TYPE_TURRET
TYPE_INHIB
TYPE_NEXUS
TYPE_SPAWN
TYPE_SHOP
TYPE_CAMP
TEAM_ALLY
TEAM_ENEMY
TEAM_NEUTRAL
_Q
_W
_E
_R
SUMMONER_1
SUMMONER_2
COLOR_RED
COLOR_GREEN
COLOR_BLUE
COLOR_YELLOW
COLOR_AQUA
COLOR_PURPLE
COLOR_BLACK
COLOR_WHITE
BUFF_INTERNAL
BUFF_AURA
BUFF_COMBATENCHANCER
BUFF_COMBATDEHANCER
BUFF_SPELLSHIELD
BUFF_STUN
BUFF_INVISIBILITY
BUFF_SILENCE
BUFF_TAUNT
BUFF_BERSERK
BUFF_POLYMORPH
BUFF_SLOW
BUFF_SNARE
BUFF_DAMAGE
BUFF_HEAL
BUFF_HASTE
BUFF_SPELLIMMUNITY
BUFF_PHYSICALIMMUNITY
BUFF_INVULNERABILITY
BUFF_ATTACKSPEEDSLOW
BUFF_NEARSIGHT
BUFF_CURRENCY
BUFF_FEAR
BUFF_CHARM
BUFF_POISON
BUFF_SUPPRESSION
BUFF_BLIND
BUFF_COUNTER
BUFF_SHRED
BUFF_FLEE
BUFF_KNOCKUP
BUFF_KNOCKBACK
BUFF_DISARM
BUFF_GROUNDED
BUFF_DROWSY
BUFF_ASLEEP
BUFF_OBSCURED
BUFF_CLICKPROOFTOENEMIES
BUFF_UNKILLABLE
Examples
Callbacks
The following callbacks have no arguments:

cb.pre_tick
cb.tick
cb.draw_first
cb.draw
cb.sprite
cb.draw_first is triggered before cb.sprite, while cb.draw is triggered after cb.sprite.

cb.keydown and cb.keyup
Both have a single arg, key_code:

local function on_key_down(k)
    if k==49 then
        print('the 1 key is down')
    end
end

local function on_key_up(k)
    if k==49 then
        print('the 1 key is up')
    end
end

cb.add(cb.keydown, on_key_down)
cb.add(cb.keyup, on_key_up)
cb.spell
Has a single arg, spell.obj:

local function on_process_spell(spell)
    print(spell.name, spell.owner.charName)
end

cb.add(cb.spell, on_process_spell)
cb.issueorder
Has three args: order_type, pos, obj:

Note that cb.issueorder will only work for hanbot internal request, user’s manual movement/attack will not trigger this event.

local function on_issue_order(order, pos, obj)
    if order==2 then
        print(('move order issued at %.2f - %.2f'):format(pos.x, pos.z))
    end
    if order==3 then
        print(('attack order issued to %u'):format(obj))
    end
end

cb.add(cb.issueorder, on_issue_order)
cb.issue_order
just a better version of cb.issueorder
args: IssueOrderArgs:

boolean args.process
number args.order
vec3 args.pos
obj args.target
boolean args.isShiftPressed
boolean args.isAltPressed
boolean args.shouldPlayOrderAcknowledgementSound
boolean args.isFromUser
Note that cb.issue_order will only work for hanbot internal request, user’s manual movement/attack will not trigger this event.
Now cb.issue_order will be triggered for all requests (include manual click).

Warning:
If you want to change the target or pos of issue_order, please use orb.combat.register_f_pre_tick and TS.filter, use cb.issue_order is not recommended, it will cause lots logic problems.

local function on_issue_order(args)
    if args.order==2 then
        print(('move order issued at %.2f - %.2f'):format(args.pos.x, args.pos.z))
        return
    end

    if args.order==3 then
        print(('attack order issued to %u'):format(args.target.ptr))
        return
    end

    if SOME_CONDITION_1 then
        args.pos = cursorPos -- you can change any parameters
        return
    end

    if SOME_CONDITION_2 then
        args.process = false -- block this issue_order 
        return
    end
end

cb.add(cb.issue_order, on_issue_order)
cb.castspell
Has four args: slot, startpos, endpos, nid:

Note that cb.cast_spell will only work for hanbot internal request, user’s manual cast will not trigger this event.

local function on_cast_spell(slot, startpos, endpos, nid)
    print(('%u, %.2f-%.2f, %.2f-%.2f, %u'):format(slot, startpos.x, startpos.z, endpos.x, endpos.z, nid))
end

cb.add(cb.castspell, on_cast_spell)
cb.cast_spell
just a better version of cb.castspell
args: CastSpellArgs:

boolean args.process
number args.spellSlot
vec3 args.casterPosition
vec3 args.targetPosition
vec3 args.targetEndPosition
obj args.target
Note that cb.cast_spell will only work for hanbot internal request, user’s manual cast will not trigger this event.

local function on_cast_spell(args)
    print(('spellSlot: %u, target: %u'):format(args.spellSlot, args.target and args.target.ptr or 0))

    if SOME_CONDITION_2 then
        args.process = false -- block this cast_spell 
        return
    end
end

cb.add(cb.cast_spell, on_cast_spell)
cb.attack_cancel
Fired when AA canceled

local function on_cancel_attack(obj)
    print(('%s, cancel attack'):format(obj.charName))
end
cb.add(cb.attack_cancel, on_cancel_attack)
cb.cast_finish
Fired when a spell cast is finished.

local function on_cast_finish(spell)
    if spell.owner==player then
        print('on_cast_finish: ' .. spell.name)
    end
end
cb.add(cb.cast_finish, on_cast_finish)
cb.play_animation
Fired when a animation (from network only) is begin to play.

local function on_play_animation(obj, animation)
    print(('on_play_animation, %s, %s'):format(obj.charName, animation))
end
cb.add(cb.play_animation, on_play_animation)
cb.create_minion and cb.delete_minion
Both have a single arg, minion.obj

local function on_create_minion(obj)
    print(obj.name, obj.charName)
end

local function on_delete_minion(obj)
    --obj is invalid within the scope of this function, it is dangerous to check obj properties other than .ptr
    print(obj.ptr)
end

cb.add(cb.create_minion, on_create_minion)
cb.add(cb.delete_minion, on_delete_minion)
cb.create_missile and cb.delete_missile
Both have a single arg, missile.obj

local function on_create_missile(obj)
    print(obj.name, obj.speed, obj.spell.name)
end

local function on_delete_missile(obj)
    --obj is invalid within the scope of this function, it is dangerous to check obj properties other than .ptr
    print(obj.ptr)
end

cb.add(cb.create_missile, on_create_missile)
cb.add(cb.delete_missile, on_delete_missile)
cb.create_particle and cb.delete_particle
Both have a single arg, base.obj

local function on_create_particle(obj)
    print(obj.name, obj.x, obj.z)
end

local function on_delete_particle(obj)
    --obj is invalid within the scope of this function, it is dangerous to check obj properties other than .ptr
    print(obj.ptr)
end

cb.add(cb.create_particle, on_create_particle)
cb.add(cb.delete_particle, on_delete_particle)
cb.buff_gain and cb.buff_lose


local function on_buff_gain(obj, buff)
    print('[Buff gain]: ', obj.charName, buff.name)
end

local function on_buff_lose(obj, buff)
    print('[Buff lose]: ', obj.charName, buff.name)
end

cb.add(cb.buff_gain, on_buff_gain)
cb.add(cb.buff_lose, on_buff_lose)
cb.set_cursorpos
Don’t use this callback, please use player:castSpell('move')

-- Only works for VelkozR / AurelionSolQ / YuumiQ / NunuW / SionR
local function on_cursorpos_change(point)
    print('[on_cursorpos_change]: ', point[0], point[1])

    -- set x and y
    local v = graphics.world_to_screen(g_target.pos)
    point[0] = v.x
    point[1] = v.y
end

cb.add(cb.set_cursorpos, on_cursorpos_change)
Creating Shards
Introducing shards, a new way of binding and encrypting your folder into a single file.

To build shards, you have to add a shard table to your header.lua, which contains all the names of your files you would use in module.load(id, name).
If you build a libshard, lib = true has to be added additionally.


local isCN = hanbot and hanbot.language == 1
local supported = {
  Ashe = true,
  Lux = true,
}

return {
  id = "some_unique_name",
  name = isCN and "你好" or "Hello",
  author = "aaa",
  description = [[]],
  shard = {
    'main', 
    'spells/q',
  },

  -- menu will be loaded by this order: "Champion" / "Orbwalker" / "Evade" / "Utility" / "Other"
  type = "Champion",  -- if this shard is a champion plugin

  -- current shard will not be loaded when "load" return failed.
  load = function ()
    return supported[player.charName]
  end
}
Additionally you can bind sprites into a shard by adding it’s names to a resource table.
The resource is shared between ALL plugins, it is better to have a unique name.

return {
  ...
  ...
  shard = {
    'main', 'spells/q',
  },
  resources = {
    'SPRITE_NAME.png', —developer/SHARD_NAME/SPRITE_NAME.png
    'SUB_FOLDER/SPRITE_NAME.png', —developer/SHARD_NAME/SUB_FOLDER/SPRITE_NAME.png
  },
  hprotect = true, -- enable the enhanced protection to protect your source code
  -- lib = true, -- build a libshard
}
Note that the sprite name added to the resource folder is the same as when using it ingame.

cb.add(cb.sprite, function()
  graphics.draw_sprite('SPRITE_NAME.png', vec2)
  graphics.draw_sprite('SUB_FOLDER/SPRITE_NAME.png', vec2)
end)
Shard builder is available in the developer group.
Warning:
There is no error handling for the shard builder.
Make sure your script is working ingame, has a valid header and the folder name is being input correctly.
Do not build shards out of scripts that already use the crypt module.


Avoiding Bugsplats
Variables must be properly purged, attempting to access certain obj properties after the obj has been deleted by the LoL client will result in LoL bugsplatting

local ex_obj

local function on_tick()
    if ex_obj and ex_obj.isDead then
        ex_obj = nil
    end
    --continue rest of your code
end

local function on_create_minion(obj)
    if not ex_obj then
        ex_obj = obj
    end
end

local function on_delete_minion(obj)
    if ex_obj and ex_obj.ptr==obj.ptr then
        ex_obj = nil
    end
end

cb.add(cb.tick, on_tick)
cb.add(cb.create_minion, on_create_minion)
cb.add(cb.delete_minion, on_delete_minion)
Do not use spell.obj outside of the spell callback.

local wrong
local correct

local function on_process_spell(spell)
    if not wrong then
        wrong = spell
    end
    if not correct then
        correct = {
            windUpTime = spell.windUpTime,
            startPos = vec3(spell.startPos),
        }
    end
end

local function on_tick()
    if wrong then
        --DO NOT DO THIS, this is very likely to lead to bugsplats!
        print(wrong.windUpTime)
    end

    if correct then
        --Instead, do this
        print(correct.windUpTime)
    end
end

cb.add(cb.spell, on_process_spell)
cb.add(cb.tick, on_tick)
Do not attempt to access obj properties other than ptr in cb.delete_minion, cb.delete_particle or cb.delete_missile.

local function on_delete_minion(obj)
    --wrong
    if obj.charName=='dontdothis' then
        --this is likely to lead to bugsplats
    end

    --correct, only check ptrs here
    if obj.ptr==someobj.ptr then
        someobj = nil
    end
end

local function on_delete_particle(obj)
    --wrong
    if obj.charName=='dontdothis' then
        --this is likely to lead to bugsplats
    end

    --correct, only check ptrs here
    if obj.ptr==someobj.ptr then
        someobj = nil
    end
end

local function on_delete_missile(obj)
    --wrong
    if obj.charName=='dontdothis' then
        --this is likely to lead to bugsplats
    end

    --correct, only check ptrs here
    if obj.ptr==someobj.ptr then
        someobj = nil
    end
end

cb.add(cb.create_minion, on_create_minion)
cb.add(cb.delete_particlee, on_delete_particle)
cb.add(cb.delete_missile, on_delete_missile)
Do not use the buffManager, use obj.buff instead.

Performance Tips
The cb.on_draw will cause lots performance, Dont loop objManager or do lots calulation in it.
Use objManager.minions[‘xxx’] to get the type of minions needed.
Some other tips for lua: using local variable
DONT use any NYI: The JIT Compiler’s Drawback: Why Avoid NYI?
-- normal
for i=0, objManager.minions[TEAM_ENEMY].size-1 do
    local obj = objManager.minions[TEAM_ENEMY][i]
end

-- use, local variable, better
local enemyMinions = objManager.minions[TEAM_ENEMY]
local enemyMinionsSize = enemyMinions.size
for i=0, enemyMinionsSize-1 do
    local obj = enemyMinions[i]
end
Internal Profiler Tools
Start game and click “Open Performance Monitor” in “Hanbot Settings”
In “Settings” tab, Check “Enable LuaJIT Profiler”
Press [-] to start record performance data and press [+] to stop
Download this profiler_tool and start profiler_gui
Open “profile.prof” (in the same directory with core.dll) and analyze performance (It is recommended to display the “Stats” window as “Aggregate Stats”)
Paid Script Conditions
To reward developers, we pay all community developers starting from 2023.

1a. Only “Community Developers” qualify to get payment from their plugins.
1b. To obtain the “Community Developers” status:
User must display proficiency with the lua language
Communication with administration and users must be professional
Your plugin needs to have enough users
2a. The plugin must remain free, and any form of authentication or login is not allowed.
2b. Any form of date verification, network verification, or version check is not allowed.
3a. It is prohibited to add QQ group information in the plugin.
3b. If you want to get feedback from users, Email, Telegram and Discord are allowed.
4a. The payment amount contains many parts (base+bonus+usage+win_rate).
The usage part contains win_count * champion_weight
Champions with lower usage (T0-T5) will get more weight
4b. Developers who have switched to other platforms or inactive will only get the win_rate part.
5a. Conditions are subject to change at anytime without notice

local ove_0_10 = module.internal("pred")
local ove_0_11 = module.internal("TS")
local ove_0_12 = module.seek("evade")
local ove_0_13 = module.load("<PERSON>", "common/common")
local ove_0_14 = module.load("<PERSON>", "common/computer")
local ove_0_15 = module.load("<PERSON>", "common/SpellDatabase")
local ove_0_16 = module.load("Brian", "common/targetedSpells")
local ove_0_17 = module.load("<PERSON>", "KSante/menu")
local ove_0_18 = module.load("<PERSON>", "KSante/helper")
local ove_0_19 = {
	CastRangeGrowthEndTime = 2.5,
	CastRangeGrowthStartTime = 0,
	last = 0,
	range = 600,
	LastCastTime = 0,
	CastRangeGrowthMax = 600,
	CastRangeGrowthMin = 1.5,
	CastRangeGrowthDuration = 0.7,
	slot = player:spellSlot(_W),
	pred_input = {
		speed = 1500,
		range = 600,
		delay = 0.25,
		boundingRadiusMod = 0,
		width = 110,
		source = player.path.serverPos:to2D()
	},
	result = {},
	interrupt_data = {}
}

function ove_0_19.is_ready()
	return ove_0_19.slot.state == 0
end

function ove_0_19.invoke_action()
	if ove_0_19.result.castPos:dist(player.pos2D) < ove_0_19.range and not ove_0_19.result.obj.buff[BUFF_STUN] then
		player:castSpell("release", _W, ove_0_19.result.castPos:to3D())
	end
end

function ove_0_19.get_action_state()
	if ove_0_19.is_ready() then
		return ove_0_19.get_prediction()
	end
end

function ove_0_19.is_charging_with_w()
	if player:spellSlot(_W).isCharging or player.buff.ksantew or ove_0_19.LastCastTime > 0 and game.time < ove_0_19.LastCastTime then
		return true
	end

	return false
end

function ove_0_19.cast_w(arg_9_0)
	local slot_9_0 = ove_0_10.linear.get_prediction(ove_0_19.pred_input, arg_9_0)

	if slot_9_0 then
		local slot_9_1 = slot_9_0.endPos:to3D()

		if not ove_0_19.is_charging_with_w() then
			player:castSpell("pos", _W, slot_9_1)
		elseif slot_9_0.endPos:dist(player.pos2D) < ove_0_19.range and not ove_0_13.isUnderEnemyTurret(slot_9_1, 950) then
			ove_0_13.IntDelayAction(function()
				player:castSpell("release", _W, slot_9_1)
			end, ove_0_17.w_block.delay_cast:get() / 1000 - network.latency)
		end
	elseif not ove_0_19.is_charging_with_w() then
		player:castSpell("pos", _W, arg_9_0.path.serverPos)
	elseif slot_9_0.endPos:dist(player.pos2D) < ove_0_19.range and not ove_0_13.isUnderEnemyTurret(arg_9_0.path.serverPos, 950) then
		ove_0_13.IntDelayAction(function()
			player:castSpell("release", _W, arg_9_0.path.serverPos)
		end, ove_0_17.w_block.delay_cast:get() / 1000 - network.latency)
	end
end

function ove_0_19.invoke_interrupt_spells()
	if not ove_0_19.interrupt_data.owner then
		return
	end

	if game.time - ove_0_19.interrupt_data.channel >= ove_0_19.interrupt_data.start then
		ove_0_19.interrupt_data.owner = false

		return
	end

	if player.pos:dist(ove_0_19.interrupt_data.owner.pos) < ove_0_19.range + ove_0_19.interrupt_data.owner.boundingRadius and game.time >= ove_0_19.interrupt_data.start then
		local slot_12_0 = ove_0_10.linear.get_prediction(ove_0_19.pred_input, ove_0_19.interrupt_data.owner)

		if slot_12_0 and slot_12_0.startPos:distSqr(slot_12_0.endPos) < ove_0_19.range * ove_0_19.range then
			if not ove_0_19.is_charging_with_w() then
				player:castSpell("pos", _W, slot_12_0.endPos:to3D())
			elseif slot_12_0.endPos:dist(player.pos2D) < ove_0_19.range - 150 then
				player:castSpell("release", _W, slot_12_0.endPos:to3D())

				return true
			end
		end
	end
end

function ove_0_19.spell_on_enemy(arg_13_0)
	local slot_13_0 = arg_13_0.name
	local slot_13_1 = arg_13_0.owner

	if arg_13_0 and slot_13_1.type == TYPE_HERO and slot_13_1.team == TEAM_ENEMY and arg_13_0.target == player and not slot_13_0:find("BasicAttack") and (not slot_13_0:find("crit") or slot_13_1.charName == "Karthus" and slot_13_1.charName == "Diana") then
		local slot_13_2 = string.lower(slot_13_1.charName)

		if ove_0_16[slot_13_2] then
			for iter_13_0 = 1, #ove_0_16[slot_13_2] do
				local slot_13_3 = ove_0_16[slot_13_2][iter_13_0]

				if ove_0_17.w_block[slot_13_2][slot_13_1.charName .. slot_13_3.menuslot].Dodge and ove_0_17.w_block[slot_13_2][slot_13_1.charName .. slot_13_3.menuslot].Dodge:get() and ove_0_13.GetPercentHealth(player) <= ove_0_17.w_block[slot_13_2][slot_13_1.charName .. slot_13_3.menuslot]["min.Health"]:get() and arg_13_0.slot == slot_13_3.slot and slot_13_1.charName ~= "Vladimir" and slot_13_1.charName ~= "Karthus" and slot_13_1.charName ~= "Zed" and slot_13_1.charName ~= "Renekton" then
					ove_0_19.cast_w(slot_13_1)
				end

				if ove_0_17.w_block[slot_13_2][slot_13_1.charName .. slot_13_3.menuslot].Dodge and ove_0_17.w_block[slot_13_2][slot_13_1.charName .. slot_13_3.menuslot].Dodge:get() and ove_0_13.GetPercentHealth(player) <= ove_0_17.w_block[slot_13_2][slot_13_1.charName .. slot_13_3.menuslot]["min.Health"]:get() and slot_13_1.charName == "Renekton" and slot_13_0 == "RenektonExecute" then
					ove_0_19.cast_w(slot_13_1)
				end
			end
		end
	end
end

function ove_0_19.evade_block_spells()
	if not ove_0_13.isEvade().evade_0_2 then
		for iter_14_0 = 1, #ove_0_12.core.skillshots do
			local slot_14_0 = ove_0_12.core.skillshots[iter_14_0]

			if slot_14_0 and slot_14_0.polygon and slot_14_0.polygon:Contains(player.path.serverPos) ~= 0 and (not slot_14_0.data.collision or #slot_14_0.data.collision == 0) then
				local slot_14_1 = slot_14_0.owner

				for iter_14_1, iter_14_2 in pairs(ove_0_15) do
					if ove_0_17.w_block[iter_14_2.charName] and slot_14_1.charName ~= "Fizz" then
						if iter_14_2.charName == "Evelynn" and ove_0_13.CheckBuff(player, "EvelynnW") then
							if slot_14_0.name:find(iter_14_1:lower()) and ove_0_17.w_block[iter_14_2.charName][iter_14_1].Dodge:get() and ove_0_17.w_block[iter_14_2.charName][iter_14_1].hp:get() >= player.health / player.maxHealth * 100 then
								if slot_14_0.missile and slot_14_0.data.delay + player.pos:dist(slot_14_0.missile.pos) / slot_14_0.data.speed < network.latency + 0.25 then
									ove_0_19.cast_w(slot_14_1)
								end

								if iter_14_2.speed == math.huge or not ove_0_17.w_block.ignoreCircular:get() and slot_14_0.data.spell_type == "Circular" then
									ove_0_19.cast_w(slot_14_1)
								end
							end
						elseif slot_14_0.name:find(iter_14_1:lower()) and ove_0_17.w_block[iter_14_2.charName][iter_14_1].Dodge:get() and ove_0_17.w_block[iter_14_2.charName][iter_14_1].hp:get() >= player.health / player.maxHealth * 100 then
							if slot_14_0.missile then
								if slot_14_0.data.delay + player.pos:dist(slot_14_0.missile.pos) / slot_14_0.data.speed < network.latency + 0.25 then
									ove_0_19.cast_w(slot_14_1)
								end
							else
								ove_0_19.cast_w(slot_14_1)
							end

							if iter_14_2.speed == math.huge or not ove_0_17.w_block.ignoreCircular:get() and slot_14_0.data.spell_type == "Circular" then
								ove_0_19.cast_w(slot_14_1)
							end
						end
					end
				end
			end
		end

		for iter_14_3 = 1, #ove_0_12.core.targeted do
			local slot_14_2 = ove_0_12.core.targeted[iter_14_3]
			local slot_14_3 = slot_14_2.owner

			if slot_14_2.data.spell_type == "Target" and slot_14_2.target == player and slot_14_3.type == TYPE_HERO and slot_14_2 and not slot_14_2.name:lower():find("basicattack") and not slot_14_2.name:lower():find("crit") then
				for iter_14_4, iter_14_5 in pairs(ove_0_15) do
					if ove_0_17.w_block[iter_14_5.charName] and slot_14_3.charName ~= "Fizz" then
						for iter_14_6, iter_14_7 in pairs(ove_0_15) do
							if slot_14_2.name and (slot_14_2.name:find(iter_14_6) or slot_14_2.name:find(iter_14_6:lower())) and ove_0_17.w_block[iter_14_7.charName] and ove_0_17.w_block[iter_14_7.charName][iter_14_6].Dodge:get() and ove_0_17.w_block[iter_14_7.charName][iter_14_6].hp:get() >= player.health / player.maxHealth * 100 then
								if slot_14_2 and slot_14_2.target and slot_14_2.target == player and slot_14_3.type == TYPE_HERO then
									ove_0_19.cast_w(slot_14_3)
								elseif slot_14_2.missile and slot_14_2.missile.speed and player.pos:dist(slot_14_2.missile.pos) / slot_14_2.missile.speed < network.latency + 0.35 then
									ove_0_19.cast_w(slot_14_3)
								end
							end
						end
					end
				end
			end
		end
	else
		for iter_14_8 = ove_0_12.core.skillshots.n, 1, -1 do
			local slot_14_4 = ove_0_12.core.skillshots[iter_14_8]
			local slot_14_5 = slot_14_4:contains(player)
			local slot_14_6 = slot_14_4.name
			local slot_14_7 = slot_14_4.owner

			if slot_14_4 and not slot_14_6:lower():find("basicattack") and not slot_14_6:lower():find("crit") and slot_14_5 then
				for iter_14_9, iter_14_10 in pairs(ove_0_15) do
					if ove_0_17.w_block[iter_14_10.charName] then
						if iter_14_10.charName == "Evelynn" and ove_0_13.CheckBuff(player, "EvelynnW") then
							if slot_14_6 and (slot_14_6:find(iter_14_9) or slot_14_6:find(iter_14_9:lower())) and ove_0_17.w_block[iter_14_10.charName] and ove_0_17.w_block[iter_14_10.charName][iter_14_9].Dodge:get() and ove_0_17.w_block[iter_14_10.charName][iter_14_9].hp:get() >= player.health / player.maxHealth * 100 then
								if slot_14_4.missile and player.pos:dist(slot_14_4.missile.pos) / slot_14_4.data.speed < network.latency + 0.25 then
									ove_0_19.cast_w(slot_14_7)
								end

								if iter_14_10.speed == math.huge or not ove_0_17.w_block.ignoreCircular:get() and slot_14_4.data.spell_type == "Circular" then
									ove_0_19.cast_w(slot_14_7)
								end
							end
						elseif slot_14_6 and (slot_14_6:find(iter_14_9) or slot_14_6:find(iter_14_9:lower())) and ove_0_17.w_block[iter_14_10.charName] and ove_0_17.w_block[iter_14_10.charName][iter_14_9].Dodge:get() and ove_0_17.w_block[iter_14_10.charName][iter_14_9].hp:get() >= player.health / player.maxHealth * 100 then
							if slot_14_4.missile and player.pos:dist(slot_14_4.missile.pos) / slot_14_4.data.speed < network.latency + 0.25 then
								ove_0_19.cast_w(slot_14_7)
							end

							if not ove_0_17.w_block.ignoreCircular:get() and slot_14_4.data.spell_type == "Circular" then
								ove_0_19.cast_w(slot_14_7)
							end
						end
					end
				end
			end
		end

		for iter_14_11 = ove_0_12.core.targeted.n, 1, -1 do
			local slot_14_8 = ove_0_12.core.targeted[iter_14_11]
			local slot_14_9 = slot_14_8.name
			local slot_14_10 = slot_14_8.owner

			if slot_14_8 and not slot_14_9:lower():find("basicattack") and not slot_14_9:lower():find("crit") then
				for iter_14_12, iter_14_13 in pairs(ove_0_15) do
					if slot_14_9 and (slot_14_9:find(iter_14_12) or slot_14_9:find(iter_14_12:lower())) and ove_0_17.w_block[iter_14_13.charName] and ove_0_17.w_block[iter_14_13.charName][iter_14_12].Dodge:get() and ove_0_17.w_block[iter_14_13.charName][iter_14_12].hp:get() >= player.health / player.maxHealth * 100 then
						if slot_14_8 and slot_14_8.target and slot_14_8.target == player and slot_14_10.type == TYPE_HERO then
							ove_0_19.cast_w(slot_14_10)
						elseif slot_14_8.missile and slot_14_8.missile.speed and player.pos:dist(slot_14_8.missile.pos) / slot_14_8.missile.speed < network.latency + 0.24 then
							ove_0_19.cast_w(slot_14_10)
						end
					end
				end
			end
		end
	end
end

function ove_0_19.invoke_action_jungle()
	local slot_15_0 = {
		mode = "jungleclear",
		health = 0
	}
	local slot_15_1 = ove_0_19.range

	for iter_15_0 = 0, objManager.minions.size[TEAM_NEUTRAL] - 1 do
		local slot_15_2 = objManager.minions[TEAM_NEUTRAL][iter_15_0]

		if slot_15_1 >= player.pos:dist(slot_15_2.pos) and slot_15_2.maxHealth > slot_15_0.health and ove_0_13.valid_minion(slot_15_2) then
			slot_15_0.obj = slot_15_2
			slot_15_0.health = slot_15_2.maxHealth
		end
	end

	if slot_15_0.mode == "jungleclear" and slot_15_0.obj ~= nil and ove_0_13.isValidTarget(slot_15_0.obj) and not slot_15_0.obj.buff[BUFF_STUN] then
		local slot_15_3 = ove_0_10.linear.get_prediction(ove_0_19.pred_input, slot_15_0.obj, player.path.serverPos:to2D())

		if not ove_0_19.is_charging_with_w() then
			player:castSpell("pos", _W, slot_15_3.endPos:to3D())
		elseif ove_0_13.GetDistance(player.pos, slot_15_0.obj.pos) < ove_0_19.range + 250 then
			player:castSpell("release", _W, slot_15_3.endPos:to3D())
		end
	end
end

function ove_0_19.flee_w()
	if not ove_0_19.is_charging_with_w() then
		player:castSpell("pos", _W, game.mousePos)
	elseif ove_0_19.range == ove_0_19.CastRangeGrowthMax then
		player:castSpell("release", _W, game.mousePos)
	end
end

function ove_0_19.get_prediction()
	if ove_0_19.last == game.time then
		return ove_0_19.result.castPos
	end

	ove_0_19.last = game.time
	ove_0_19.result.obj = nil
	ove_0_19.result.seg = nil
	ove_0_19.result.castPos = nil
	ove_0_19.result = ove_0_11.get_result(function(arg_18_0, arg_18_1, arg_18_2)
		if arg_18_2 > ove_0_19.range + 250 + arg_18_1.boundingRadius then
			return
		end

		if not ove_0_19.is_charging_with_w() and ove_0_18.HaveQ3() then
			return
		end

		if arg_18_2 <= ove_0_13.GetAARange(arg_18_1) and ove_0_13.CalculateAADamage(arg_18_1) * 2 > ove_0_13.GetShieldedHealth("AD", arg_18_1) then
			return
		end

		local slot_18_0 = ove_0_10.linear.get_prediction(ove_0_19.pred_input, arg_18_1)

		if not slot_18_0 then
			return
		end

		local slot_18_1 = ove_0_14.Compute(ove_0_19.pred_input, slot_18_0, arg_18_1)

		if slot_18_1 < 0 then
			return false
		end

		local slot_18_2 = ove_0_19.range + 250 + arg_18_1.boundingRadius

		if slot_18_0 and slot_18_0.startPos:distSqr(slot_18_0.endPos) < slot_18_2 * slot_18_2 then
			arg_18_0.seg = slot_18_0
			arg_18_0.obj = arg_18_1
			arg_18_0.castPos = (ove_0_10.core.get_pos_after_time(arg_18_1, slot_18_1) + slot_18_0.endPos) / 2
			arg_18_0.time = slot_18_1

			return true
		end
	end)

	if ove_0_19.result.castPos then
		return ove_0_19.result
	end
end

return ove_0_19

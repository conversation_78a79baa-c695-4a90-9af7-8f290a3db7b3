﻿local var_0_0 = module.internal("pred")
local var_0_1 = module.internal("TS")
local var_0_2 = module.internal("orb")
local var_0_3 = module.load("<PERSON>", "Utility/common20")
local var_0_4 = module.load("<PERSON>", "Utility/lualinq")
local var_0_5 = module.seek("evade")
local var_0_6 = module.load("<PERSON>", "Utility/SpellDatabaseIrelia")
local var_0_7 = {
	range = 600
}
local var_0_8 = {
	speed = 2300,
	range = 825,
	delay = 0.25,
	boundingRadiusMod = 1,
	width = 120
}
local var_0_9 = {
	speed = 2000,
	range = 775,
	delay = 0,
	boundingRadiusMod = 1,
	width = 70,
	collision = {
		hero = true
	}
}
local var_0_10 = {
	speed = 2000,
	range = 850,
	delay = 0.4,
	boundingRadiusMod = 1,
	width = 160,
	collision = {
		wall = true
	}
}
local var_0_11 = {
	garen = {
		{
			slot = 3,
			menuslot = "R"
		}
	},
	darius = {
		{
			slot = 3,
			menuslot = "R"
		}
	},
	karthus = {
		{
			slot = 3,
			menuslot = "R"
		}
	},
	zed = {
		{
			slot = 3,
			menuslot = "R"
		}
	},
	vlad<PERSON><PERSON> = {
		{
			slot = 3,
			menuslot = "R"
		}
	},
	syndra = {
		{
			slot = 3,
			menuslot = "R"
		}
	},
	veigar = {
		{
			slot = 3,
			menuslot = "R"
		}
	},
	leesin = {
		{
			slot = 3,
			menuslot = "R"
		}
	},
	malzahar = {
		{
			slot = 3,
			menuslot = "R"
		}
	},
	tristana = {
		{
			slot = 3,
			menuslot = "R"
		}
	},
	chogath = {
		{
			slot = 3,
			menuslot = "R"
		}
	},
	lissandra = {
		{
			slot = 3,
			menuslot = "R"
		}
	},
	jarvaniv = {
		{
			slot = 3,
			menuslot = "R"
		}
	},
	skarner = {
		{
			slot = 3,
			menuslot = "R"
		}
	},
	kalista = {
		{
			slot = 2,
			menuslot = "E"
		}
	},
	brand = {
		{
			slot = 3,
			menuslot = "R"
		}
	},
	akali = {
		{
			slot = 3,
			menuslot = "R"
		}
	},
	diana = {
		{
			slot = 3,
			menuslot = "R"
		}
	},
	khazix = {
		{
			slot = 0,
			menuslot = "Q"
		}
	},
	nocturne = {
		{
			slot = 3,
			menuslot = "R"
		}
	},
	volibear = {
		{
			slot = 1,
			menuslot = "W"
		}
	},
	singed = {
		{
			slot = 2,
			menuslot = "E"
		}
	},
	renekton = {
		{
			slot = 1,
			menuslot = "W"
		}
	},
	diana = {
		{
			slot = -1,
			menuslot = "Third Auto Attack"
		}
	}
}
local var_0_12 = {
	[0] = "Q",
	"W",
	"E",
	[-1] = "P",
	[3] = "R"
}
local var_0_13 = menu("Brian" .. player.charName, "[Brian] " .. player.charName)

var_0_13:menu("combo", "Combo")
var_0_13.combo:menu("qset", "Q Settings")
var_0_13.combo.qset:boolean("qcombo", "Use Q", true)
var_0_13.combo.qset:boolean("gapq", "Use Q for Gapclose on Minion", true)
var_0_13.combo.qset:boolean("focusmarked", "Priority Marked Enemies", true)
var_0_13.combo.qset:header("uwu", " -- Jump Around -= ")
var_0_13.combo.qset:boolean("jumparound", "Use Q to Jump-Around Enemy", true)
var_0_13.combo.qset:boolean("passive", " ^- Only to stack passive", true)
var_0_13.combo.qset:slider("jumphp", " ^- Ignore if HP <= X", 30, 0, 100, 1)
var_0_13.combo.qset:slider("jumpmana", "Stop if Mana <= X", 50, 0, 100, 1)
var_0_13.combo.qset:header("--", " ---- ")
var_0_13.combo.qset:keybind("qmarked", "Only if Marked Toggle", nil, "G")
var_0_13.combo:menu("wset", "W Settings")
var_0_13.combo.wset:boolean("wcombo", "Use W", true)
var_0_13.combo.wset.wcombo:set("tooltip", "It doesn't effect W Dodging")
var_0_13.combo.wset:slider("chargew", "Automatically release after (ms)", 100, 1, 1500, 1)
var_0_13.combo:menu("eset", "E Settings")
var_0_13.combo.eset:boolean("ecombo", "Use E", true)
var_0_13.combo:menu("rset", "R Settings")
var_0_13.combo.rset:dropdown("rusage", "R Usage", 1, {
	"Killable",
	"At X Health",
	"Never"
})
var_0_13.combo.rset:slider("hpr", "If X% >= Health", 50, 0, 100, 5)
var_0_13.combo.rset:slider("saver", "Don't waste R if Enemy Health Percent <=", 10, 1, 100, 1)
var_0_13.combo.rset.hpr:set("visible", false)
var_0_13.combo.rset:slider("hitr", "Force R if Hits X", 2, 0, 5, 1)
var_0_13.combo.rset.hitr:set("tooltip", "0 - Disabled")
var_0_13.combo:header("--", " ---- ")
var_0_13.combo:keybind("semir", "Semi-R", "T", nil)
var_0_13:menu("harass", "Harass")
var_0_13.harass:boolean("qcombo", "Use Q", true)
var_0_13.harass:boolean("gapq", "Use Q for Gapclose on Minion", true)
var_0_13.harass:boolean("wcombo", "Use W", false)
var_0_13.harass:boolean("ecombo", "Use E", true)
var_0_13:menu("dodgew", "W Dodge")
var_0_13.dodgew:boolean("enablew", "Use W Automatically", true)
var_0_13.dodgew:header("hello", " -- Enemy Skillshots -- ")

if not var_0_5 then
	var_0_13.dodgew:header("uhh", "Enable 'Premium Evade' to block Skillshots")
end

if var_0_5 then
	for iter_0_0, iter_0_1 in pairs(var_0_6) do
		for iter_0_2, iter_0_3 in pairs(var_0_3.GetEnemyHeroes()) do
			if iter_0_1.charName == iter_0_3.charName then
				if iter_0_1.displayname == "" then
					iter_0_1.displayname = iter_0_0
				end

				if iter_0_1.danger == 0 then
					iter_0_1.danger = 1
				end

				if var_0_13.dodgew[iter_0_1.charName] == nil then
					var_0_13.dodgew:menu(iter_0_1.charName, iter_0_1.charName)
				end

				var_0_13.dodgew[iter_0_1.charName]:menu(iter_0_1.slot, "" .. iter_0_1.charName .. " | " .. (var_0_12[iter_0_1.slot] or "?"))
				var_0_13.dodgew[iter_0_1.charName][iter_0_1.slot]:boolean("Dodge", "Enable Block", false)
				var_0_13.dodgew[iter_0_1.charName][iter_0_1.slot]:slider("hp", "HP to Dodge", 100, 1, 100, 5)
			end
		end
	end
end

for iter_0_4 = 1, #var_0_3.GetEnemyHeroes() do
	local var_0_14 = var_0_3.GetEnemyHeroes()[iter_0_4]
	local var_0_15 = string.lower(var_0_14.charName)

	if var_0_14 and var_0_11[var_0_15] then
		for iter_0_5 = 1, #var_0_11[var_0_15] do
			local var_0_16 = var_0_11[var_0_15][iter_0_5]

			var_0_13.dodgew:boolean(string.format(tostring(var_0_14.charName) .. tostring(var_0_16.menuslot)), "Reduce Damage: " .. tostring(var_0_14.charName) .. " " .. tostring(var_0_16.menuslot), true)
		end
	end
end

var_0_13:menu("farming", "Farming")
var_0_13.farming:keybind("toggle", "Farm Toggle", nil, "A")
var_0_13.farming:header("~~~", " ~~~~ ")
var_0_13.farming:menu("laneclear", "Lane Clear")
var_0_13.farming.laneclear:boolean("farmq", "Use Q to Farm", true)
var_0_13.farming.laneclear:boolean("turret", " ^- Don't use Q Under the Turret", true)
var_0_13.farming.laneclear:boolean("qaa", "  ^- Don't use Q in AA Range", false)
var_0_13.farming.laneclear:slider("suicidalq", "Don't Q minion if Enemy is near it by X Range: ", 0, 0, 500, 5)
var_0_13.farming:menu("jungleclear", "Jungle Clear")
var_0_13.farming.jungleclear:boolean("useq", "Use Q in Jungle Clear", true)
var_0_13.farming.jungleclear:boolean("markedq", " ^- Only if Marked/Killable", true)
var_0_13.farming.jungleclear:boolean("usee", "Use E in Jungle Clear", true)
var_0_13.farming:menu("lasthit", "Last Hit")
var_0_13.farming.lasthit:boolean("farmq", "Use Q to Last Hit", true)
var_0_13.farming.lasthit:boolean("qaa", "  ^- Don't use Q in AA Range", false)
var_0_13.farming.lasthit:boolean("turret", " ^- Don't use Q Under the Turret", true)
var_0_13.farming.lasthit:slider("suicidalq", "Don't Q minion if Enemy is near it by X Range: ", 0, 0, 500, 5)
var_0_13:menu("killsteal", "Killsteal")
var_0_13.killsteal:boolean("ksq", "Use Q", true)
var_0_13.killsteal:boolean("ksqaa", " ^- Include AA", false)
var_0_13.killsteal:boolean("gapq", "Use Smart Q Gapclose", true)
var_0_13.killsteal:header("uhhh", "Q on Killable Minion > Enemy", true)
var_0_13:menu("flee", "Flee")
var_0_13.flee:keybind("fleekey", "Flee Key", "Z", nil)
var_0_13.flee:header("~~~", " ~~~~ ")
var_0_13.flee:boolean("fleeq", "Use Q to Flee", true)
var_0_13.flee:boolean("fleekill", " ^- Only if Minion is Killable/Marked", true)
var_0_13.flee:boolean("fleee", "Use E in Flee", true)
var_0_13:menu("draws", "Draw Settings")
var_0_13.draws:header("ranges", " -- Ranges -- ")
var_0_13.draws:boolean("drawq", "Draw Q Range", true)
var_0_13.draws:color("colorq", "  ^- Color", 128, 173, 238, 255)
var_0_13.draws:boolean("draww", "Draw W Range", false)
var_0_13.draws:color("colorw", "  ^- Color", 255, 255, 255, 255)
var_0_13.draws:boolean("drawe", "Draw E Range", true)
var_0_13.draws:color("colore", "  ^- Color", 255, 153, 153, 255)
var_0_13.draws:boolean("drawr", "Draw R Range", false)
var_0_13.draws:color("colorr", "  ^- Color", 255, 255, 255, 255)
var_0_13.draws:header("other", " -- Other -- ")
var_0_13.draws:boolean("drawkill", "Draw Minions Killable with Q", true)
var_0_13.draws:boolean("drawdamage", "Draw Damage", true)
var_0_13.draws:slider("transparency", "Damage Drawing Transparency", 155, 0, 255, 1)
var_0_13:header("qset", "~~~~~~~~~~~~~~")
var_0_13:boolean("mastery", "Include 'Coup de Grace' rune", false)
var_0_13.mastery:set("tooltip", "Rune, which deals more damage below 40%")
var_0_1.load_to_menu(var_0_13)

local function var_0_17(arg_1_0)
	for iter_1_0 = 0, objManager.turrets.size[TEAM_ENEMY] - 1 do
		local var_1_0 = objManager.turrets[TEAM_ENEMY][iter_1_0]

		if var_1_0 and not var_1_0.isDead and var_1_0.pos:dist(arg_1_0) < 910 then
			return true
		end
	end

	return false
end

local function var_0_18(arg_2_0, arg_2_1, arg_2_2)
	if arg_2_2 <= var_0_7.range then
		arg_2_0.obj = arg_2_1

		return true
	end
end

local function var_0_19()
	return var_0_1.get_result(var_0_18).obj
end

local function var_0_20(arg_4_0, arg_4_1, arg_4_2)
	if arg_4_2 <= var_0_9.range then
		arg_4_0.obj = arg_4_1

		return true
	end
end

local function var_0_21()
	return var_0_1.get_result(var_0_20).obj
end

local function var_0_22(arg_6_0, arg_6_1, arg_6_2)
	if arg_6_2 < var_0_7.range * 2 - 70 then
		arg_6_0.obj = arg_6_1

		return true
	end
end

local function var_0_23()
	return var_0_1.get_result(var_0_22).obj
end

local var_0_24 = 0
local var_0_25 = vec3(0, 0, 0)
local var_0_26 = false
local var_0_27 = false
local var_0_28 = 0
local var_0_29 = 0
local var_0_30 = 0
local var_0_31 = vec2(0, 0)
local var_0_32 = 0
local var_0_33 = 0
local var_0_34 = 0
local var_0_35 = 0

function toVec3(arg_8_0, arg_8_1)
	return vec3(arg_8_0.x, arg_8_1.pos.y, arg_8_0.y)
end

function toVec2(arg_9_0)
	return vec2(arg_9_0.x, arg_9_0.z)
end

function RaySetDist(arg_10_0, arg_10_1, arg_10_2, arg_10_3)
	local var_10_0 = arg_10_0.x - arg_10_2.x
	local var_10_1 = arg_10_0.y - arg_10_2.y
	local var_10_2 = arg_10_0.z - arg_10_2.z
	local var_10_3 = arg_10_1.x
	local var_10_4 = arg_10_1.y
	local var_10_5 = arg_10_1.z
	local var_10_6 = var_10_0 * var_10_3 + var_10_1 * var_10_4 + var_10_2 * var_10_5
	local var_10_7 = var_10_5^2 * arg_10_3^2 - var_10_0^2 * var_10_5^2 - var_10_1^2 * var_10_5^2 + 2 * var_10_0 * var_10_2 * var_10_3 * var_10_5 + 2 * var_10_1 * var_10_2 * var_10_4 * var_10_5 + 2 * var_10_0 * var_10_1 * var_10_3 * var_10_4 + arg_10_3^2 * var_10_3^2 + arg_10_3^2 * var_10_4^2 - var_10_0^2 * var_10_4^2 - var_10_1^2 * var_10_3^2 - var_10_2^2 * var_10_3^2 - var_10_2^2 * var_10_4^2
	local var_10_8 = var_10_3^2 + var_10_4^2 + var_10_5^2
	local var_10_9 = -(var_10_6 + math.sqrt(var_10_7)) / var_10_8
	local var_10_10 = -(var_10_6 - math.sqrt(var_10_7)) / var_10_8

	return arg_10_0 + math.max(var_10_9, var_10_10) * arg_10_1
end

local function var_0_36(arg_11_0, arg_11_1, arg_11_2)
	if var_0_0.trace.linear.hardlock(arg_11_0, arg_11_1, arg_11_2) then
		return true
	end

	if var_0_0.trace.linear.hardlockmove(arg_11_0, arg_11_1, arg_11_2) then
		return true
	end

	if arg_11_2 and var_0_3.IsValidTarget(arg_11_2) and player.pos:dist(arg_11_2) <= 500 then
		return true
	end

	if var_0_0.trace.newpath(arg_11_2, 0.033, 0.5) then
		return true
	end
end

local var_0_37 = false
local var_0_38

local function var_0_39(arg_12_0, arg_12_1)
	local var_12_0 = {}

	for iter_12_0 = 0, objManager.enemies_n - 1 do
		local var_12_1 = objManager.enemies[iter_12_0]

		if arg_12_1 > arg_12_0:dist(var_12_1.pos) and var_0_3.IsValidTarget(var_12_1) then
			var_12_0[#var_12_0 + 1] = var_12_1
		end
	end

	return var_12_0
end

local function var_0_40(arg_13_0, arg_13_1)
	local var_13_0 = {}

	for iter_13_0 = 0, objManager.minions.size[TEAM_ENEMY] - 1 do
		local var_13_1 = objManager.minions[TEAM_ENEMY][iter_13_0]

		if arg_13_1 > arg_13_0:dist(var_13_1.pos) and var_0_3.IsValidTarget(var_13_1) then
			var_13_0[#var_13_0 + 1] = var_13_1
		end
	end

	return var_13_0
end

function VectorExtend(arg_14_0, arg_14_1, arg_14_2)
	return arg_14_0 + arg_14_2 * (arg_14_1 - arg_14_0):norm()
end

local var_0_41 = {
	5,
	25,
	45,
	65,
	85
}
local var_0_42 = {
	55,
	75,
	95,
	115,
	135
}
local var_0_43 = {
	55,
	75,
	95,
	115,
	135
}
local var_0_44 = 0

local function var_0_45()
	var_0_26 = false
	var_0_27 = false

	for iter_15_0 = 0, 5 do
		if player:itemID(iter_15_0) == 3078 then
			if player:spellSlot(6 + iter_15_0).cooldown > 0 then
				var_0_44 = 0.2 + game.time
			end

			if var_0_44 - game.time < 0 then
				var_0_27 = true
			end
		end

		if player:itemID(iter_15_0) == 3057 then
			if player:spellSlot(6 + iter_15_0).cooldown > 0 then
				var_0_44 = 0.2 + game.time
			end

			if var_0_44 - game.time < 0 then
				var_0_26 = true
			end
		end
	end
end

function SheenDamage(arg_16_0)
	if var_0_37 == true and var_0_38 ~= nil and var_0_38 ~= arg_16_0 or player.buff.trinityforce or player.buff.sheen then
		return 0
	end

	if var_0_27 then
		return var_0_3.CalculatePhysicalDamage(arg_16_0, 2 * player.baseAttackDamage, player)
	end

	if var_0_26 and not var_0_27 then
		return var_0_3.CalculatePhysicalDamage(arg_16_0, player.baseAttackDamage, player)
	end

	return 0
end

function PassiveDamage(arg_17_0)
	if player.buff.ireliapassivestacksmax then
		local var_17_0 = 12 + 3 * math.min(var_0_3.GetLevel(player), 18) + 0.25 * var_0_3.GetBonusAD(player)

		return var_0_3.CalculateMagicDamage(arg_17_0, var_17_0, player)
	end

	return 0
end

local var_0_46 = {
	0.5,
	0.49,
	0.48,
	0.47,
	0.46,
	0.45,
	0.44,
	0.43,
	0.42,
	0.41,
	0.4,
	0.39,
	0.38,
	0.37,
	0.36,
	0.35,
	0.34,
	0.33,
	0.32,
	0.31,
	0.3
}

function QDamage(arg_18_0)
	local var_18_0 = 0

	if player:spellSlot(0).level > 0 then
		var_18_0 = var_0_41[player:spellSlot(0).level]

		if arg_18_0.type == TYPE_MINION and arg_18_0.team == TEAM_ENEMY then
			var_18_0 = var_18_0 + var_0_42[player:spellSlot(0).level]
		end

		var_18_0 = var_18_0 + 0.6 * var_0_3.GetTotalAD(player)
	end

	local var_18_1 = var_0_3.CalculatePhysicalDamage(arg_18_0, var_18_0, player) + SheenDamage(arg_18_0) + PassiveDamage(arg_18_0)

	if player.buff["assets/perks/styles/precision/conqueror/conquerorenrage.lua"] and arg_18_0.type == TYPE_HERO then
		var_18_1 = var_18_1 * 1.08
	end

	if arg_18_0.type == TYPE_HERO and var_0_13.mastery:get() and arg_18_0.health / arg_18_0.maxHealth * 100 <= 40 then
		var_18_1 = var_18_1 * 1.08
	end

	if arg_18_0.buff.exaltedwithbaronnashorminion and math.floor(game.time / 60) >= 20 then
		if math.floor(game.time / 60) < 40 then
			if arg_18_0.charName:find("Ranged") then
				var_18_1 = var_18_1 * var_0_46[math.floor(game.time / 60) - 20]
			end

			if arg_18_0.charName:find("Melee") then
				var_18_1 = var_18_1 * var_0_46[math.floor(game.time / 60) - 20]
			end
		else
			if arg_18_0.charName:find("Ranged") then
				var_18_1 = var_18_1 * 0.3
			end

			if arg_18_0.charName:find("Melee") then
				var_18_1 = var_18_1 * 0.3
			end
		end
	end

	return var_18_1 - 5
end

local var_0_47 = {
	125,
	225,
	325
}

function RDamage(arg_19_0)
	local var_19_0 = 0

	if player:spellSlot(3).level > 0 then
		var_19_0 = var_0_3.CalculateMagicDamage(arg_19_0, var_0_47[player:spellSlot(3).level] + var_0_3.GetTotalAP() * 0.7)
	end

	return var_19_0
end

local var_0_48 = {
	80,
	120,
	160,
	200,
	240
}

function EDamage(arg_20_0)
	local var_20_0 = 0

	if player:spellSlot(2).level > 0 then
		var_20_0 = var_0_3.CalculateMagicDamage(arg_20_0, var_0_48[player:spellSlot(2).level] + var_0_3.GetTotalAP() * 0.8)
	end

	return var_20_0
end

local function var_0_49(arg_21_0)
	local var_21_0 = arg_21_0

	if var_21_0.owner and var_21_0.owner == player and var_21_0.target and var_21_0.isBasicAttack then
		var_0_37 = true
		var_0_38 = var_21_0.target
	end

	if arg_21_0.owner == player and arg_21_0.name == "IreliaE" then
		var_0_25 = vec3(arg_21_0.endPos)

		if var_0_25:dist(player.pos) >= var_0_9.range then
			var_0_25 = VectorExtend(player.pos, var_0_25, var_0_9.range)
			var_0_32 = game.time + 0.3
		end
	end

	if arg_21_0.owner == player and arg_21_0.name == "IreliaE2" then
		var_0_25 = vec3(0, 0, 0)
	end

	if arg_21_0.owner == player and arg_21_0.name == "IreliaW" then
		var_0_33 = game.time
	end

	if arg_21_0.owner == player and arg_21_0.name == "IreliaR" then
		var_0_35 = game.time + 0.5
	end

	if var_0_13.dodgew.enablew:get() then
		if arg_21_0 and arg_21_0.owner.type == TYPE_HERO and arg_21_0.owner.team == TEAM_ENEMY and arg_21_0.target and arg_21_0.target == player and not arg_21_0.name:find("BasicAttack") and (not arg_21_0.name:find("crit") or arg_21_0.owner.charName == "Karthus" and arg_21_0.owner.charName == "Diana") and not player.buff.ireliawdefense then
			local var_21_1 = string.lower(arg_21_0.owner.charName)

			if var_0_11[var_21_1] then
				for iter_21_0 = 1, #var_0_11[var_21_1] do
					local var_21_2 = var_0_11[var_21_1][iter_21_0]

					if var_0_13.dodgew[arg_21_0.owner.charName .. var_21_2.menuslot]:get() and arg_21_0.slot == var_21_2.slot and arg_21_0.owner.charName ~= "Vladimir" and arg_21_0.owner.charName ~= "Karthus" and arg_21_0.owner.charName ~= "Zed" and arg_21_0.owner.charName ~= "Renekton" then
						player:castSpell("pos", 1, player.pos)

						var_0_34 = game.time + 0.25
					end

					if var_0_13.dodgew[arg_21_0.owner.charName .. var_21_2.menuslot]:get() and arg_21_0.owner.charName == "Renekton" and arg_21_0.name == "RenektonExecute" then
						player:castSpell("pos", 1, player.pos)

						var_0_34 = game.time + 0.25
					end
				end
			end
		end

		if arg_21_0 and arg_21_0.owner.type == TYPE_HERO and arg_21_0.owner.team == TEAM_ENEMY and arg_21_0.target == player and arg_21_0.name:find("BasicAttack3") and arg_21_0.owner.charName == "Diana" and not player.buff.ireliawdefense then
			local var_21_3 = string.lower(arg_21_0.owner.charName)

			if var_0_11[var_21_3] then
				for iter_21_1 = 1, #var_0_11[var_21_3] do
					local var_21_4 = var_0_11[var_21_3][iter_21_1]

					if var_0_13.dodgew[arg_21_0.owner.charName .. var_21_4.menuslot]:get() and arg_21_0.owner.charName == "Diana" then
						player:castSpell("pos", 1, player.pos)

						var_0_34 = game.time + 0.25
					end
				end
			end
		end
	end
end

local function var_0_50(arg_22_0)
	if not arg_22_0 or not var_0_3.IsValidTarget(arg_22_0) then
		return
	end

	local var_22_0 = {}
	local var_22_1 = false

	for iter_22_0 = 0, objManager.enemies_n - 1 do
		local var_22_2 = objManager.enemies[iter_22_0]

		if var_22_2 and var_0_3.IsValidTarget(var_22_2) and var_22_2 ~= arg_22_0 and var_22_2.team ~= TEAM_ALLY then
			local var_22_3 = var_0_0.linear.get_prediction(var_0_9, var_22_2)

			if var_22_3 and var_22_3.endPos and var_22_3.startPos:dist(var_22_3.endPos) < 900 then
				local var_22_4 = var_0_0.collision.get_prediction(var_0_9, var_22_3)

				if var_22_4 then
					if #var_22_4 >= 1 then
						var_22_1 = true
					end

					table.insert(var_22_0, {
						unit = var_22_2,
						count = #var_22_4
					})
				end
			end
		end
	end

	if var_22_1 then
		table.sort(var_22_0, function(arg_23_0, arg_23_1)
			if arg_23_0.count and arg_23_1.count then
				return arg_23_0.count > arg_23_1.count
			end
		end)
	else
		table.sort(var_22_0, function(arg_24_0, arg_24_1)
			if arg_24_0.unit.pos and arg_24_1.unit.pos then
				return arg_24_0.unit.pos:dist(player.pos) < arg_24_1.unit.pos:dist(player.pos)
			end
		end)
	end

	if var_22_0[1] then
		return var_22_0[1]
	end
end

local function var_0_51(arg_25_0)
	if player:spellSlot(2).state ~= 0 or not arg_25_0 or not var_0_3.IsValidTarget(arg_25_0) then
		return false
	end

	local var_25_0 = var_0_0.linear.get_prediction(var_0_9, arg_25_0)

	var_0_9.delay = 0.5 + arg_25_0.pos:dist(player.pos) / 2000

	if player:spellSlot(2).name == "IreliaE" and arg_25_0.pos:dist(player.pos) <= 900 and var_25_0 and var_25_0.endPos and (arg_25_0.type ~= TYPE_HERO or arg_25_0.type == TYPE_HERO and var_0_36(var_0_9, var_25_0, arg_25_0) and var_0_25 == vec3(0, 0, 0)) then
		local var_25_1 = var_0_50(arg_25_0)

		if var_25_1 and var_0_3.IsValidTarget(var_25_1.unit) then
			local var_25_2 = var_0_0.linear.get_prediction(var_0_9, var_25_1.unit)

			if var_25_2 and var_25_2.endPos and var_25_2.startPos:dist(var_25_2.endPos) < 900 then
				local var_25_3 = var_25_0.endPos + (var_25_2.endPos - var_25_0.endPos):norm() * (var_25_0.endPos:dist(var_25_2.endPos) + 275)

				if var_25_3 then
					player:castSpell("pos", 2, vec3(var_25_3.x, var_25_1.unit.y, var_25_3.y))

					var_0_31 = var_25_3
					var_0_30 = game.time + 1
				end
			end
		end
	end

	if var_25_0 and var_0_31 and var_0_31 ~= vec2(0, 0) and (arg_25_0.type ~= TYPE_HERO or arg_25_0.type == TYPE_HERO and var_0_36(var_0_9, var_25_0, arg_25_0)) and player:spellSlot(2).name == "IreliaE2" then
		local var_25_4 = var_0_31 + (var_25_0.endPos - var_0_31):norm() * (var_0_31:dist(var_25_0.endPos) + 275)

		if var_25_4 then
			player:castSpell("pos", 2, vec3(var_25_4.x, arg_25_0.y, var_25_4.y))

			var_0_31 = vec2(0, 0)
		end
	end
end

function CastE2(arg_26_0)
	if var_0_30 > game.time or var_0_35 > game.time then
		return
	end

	if player:spellSlot(2).state == 0 then
		local var_26_0 = 0

		if arg_26_0.path.isActive and arg_26_0.path.isDashing then
			local var_26_1 = var_0_0.core.project(player.path.serverPos2D, arg_26_0.path, network.latency + 0.25, 2000, arg_26_0.path.dashSpeed)

			if var_26_1 and player.pos2D:dist(var_26_1) <= var_0_9.range then
				player:castSpell("pos", 2, toVec3(var_26_1, arg_26_0))
			end
		elseif var_0_25 ~= vec3(0, 0, 0) then
			local var_26_2 = false
			local var_26_3 = false

			var_0_9.delay = 0.5 + player.pos:dist(arg_26_0.pos) / 2000

			local var_26_4 = var_0_0.linear.get_prediction(var_0_9, arg_26_0, vec2(var_0_25.x, var_0_25.z))

			if var_26_4 then
				local var_26_5 = toVec3(var_26_4.endPos, arg_26_0)

				if player.pos:dist(var_26_5) <= var_0_9.range then
					local var_26_6 = vec3(0, 0, 0)

					if mathf.closest_vec_line(player.pos2D, toVec2(var_0_25), toVec2(var_26_5)) then
						local var_26_7 = toVec3(mathf.closest_vec_line(player.pos2D, toVec2(var_0_25), toVec2(var_26_5)), arg_26_0)

						if var_26_7:dist(player.pos) > var_0_9.range or var_26_5:dist(var_0_25) > var_26_7:dist(var_0_25) or var_26_7:dist(var_0_25) < arg_26_0.moveSpeed * 0.625 * 1.5 then
							var_26_2 = true

							local var_26_8 = (var_26_5 - var_0_25):norm()
							local var_26_9 = var_0_25 + var_26_8 * (var_26_5:dist(var_0_25) + arg_26_0.moveSpeed * 0.625 * 1.5)

							if player.pos:dist(var_26_9) < var_0_9.range then
								var_26_6 = var_26_9
							else
								var_26_6 = RaySetDist(var_0_25, var_26_8, player.pos, var_0_9.range)
							end
						else
							var_26_6 = var_26_7
						end
					end

					if var_26_6 and var_26_6 ~= vec3(0, 0, 0) then
						var_0_9.delay = 0.5 + player.pos:dist(var_26_6) / 2000
						var_0_9.speed = math.huge

						local var_26_10 = var_0_0.linear.get_prediction(var_0_9, arg_26_0, vec2(var_0_25.x, var_0_25.z))

						if var_26_10 then
							local var_26_11 = toVec3(var_26_10.endPos, arg_26_0)

							if var_26_11 and mathf.closest_vec_line(player.pos2D, toVec2(var_0_25), toVec2(var_26_11)) then
								local var_26_12 = vec3(0, 0, 0)
								local var_26_13 = toVec3(mathf.closest_vec_line(player.pos2D, toVec2(var_0_25), toVec2(var_26_11)), arg_26_0)

								if var_26_13:dist(player.pos) > var_0_9.range or var_26_11:dist(var_0_25) > var_26_13:dist(var_0_25) or var_26_13:dist(var_0_25) < arg_26_0.moveSpeed * 0.625 * 1.5 then
									var_26_3 = true
									pathNorm = (var_26_11 - var_0_25):norm()
									extendPos = var_0_25 + pathNorm * (var_26_11:dist(var_0_25) + arg_26_0.moveSpeed * 0.625 * 1.5)

									if player.pos:dist(extendPos) < var_0_9.range then
										var_26_12 = extendPos
									else
										var_26_12 = RaySetDist(var_0_25, pathNorm, player.pos, var_0_9.range)
									end
								else
									var_26_12 = var_26_13
								end

								if var_26_2 == var_26_3 and var_26_12:dist(player.pos) <= var_0_9.range then
									player:castSpell("pos", 2, var_26_12)
								end
							end
						end
					end
				end
			end
		end
	end
end

var_0_2.combat.register_f_after_attack(function()
	var_0_37 = false
	var_0_38 = nil

	var_0_2.combat.set_invoke_after_attack(false)

	return "on_after_attack_hydra"
end)

local function var_0_52()
	local var_28_0 = var_0_19()
	local var_28_1 = var_0_21()

	if var_0_3.IsValidTarget(var_28_0) and player:spellSlot(0).state == 0 and var_0_13.combo.qset.qcombo:get() then
		if var_28_0.buff.ireliamark or var_28_0.health <= QDamage(var_28_0) or var_0_13.combo.qset.qmarked:get() ~= true then
			player:castSpell("obj", 0, var_28_0)
		end

		if var_0_13.combo.qset.qcombo:get() and var_0_13.combo.qset.focusmarked:get() then
			local var_28_2 = from(var_0_3.GetEnemyHeroes()):where(function(arg_29_0)
				return var_0_3.IsValidTarget(arg_29_0) and arg_29_0.pos:dist(player.pos) <= var_0_7.range and arg_29_0.buff.ireliamark
			end):first()

			if var_28_2 then
				player:castSpell("obj", 0, var_28_2)
			end
		end

		if var_0_13.combo.qset.jumparound:get() and var_0_13.combo.qset.jumpmana:get() <= player.mana / player.maxMana * 100 and var_0_25 == vec3(0, 0, 0) and (var_0_13.combo.qset.passive:get() ~= true or not player.buff.ireliapassivestacks or player.buff.ireliapassivestacks and player.buff.ireliapassivestacks.stacks2 < 5 or var_0_13.combo.qset.jumphp:get() >= player.health / player.maxHealth * 100) then
			local var_28_3 = from(var_0_3.GetMinionsInRange(var_0_7.range, TEAM_ENEMY)):where(function(arg_30_0)
				return var_0_3.can_target_minion(arg_30_0) and arg_30_0.pos:dist(player.pos) < var_0_7.range and arg_30_0.pos:dist(var_28_0.pos) < var_0_7.range - 150 and arg_30_0.health <= QDamage(arg_30_0) and var_0_17(arg_30_0.pos) == false
			end):first()

			if var_28_3 and game.time - var_0_24 > 0.1 then
				player:castSpell("obj", 0, var_28_3)

				var_0_24 = game.time
			end
		end
	end

	if var_0_3.IsValidTarget(var_28_1) and var_0_13.combo.eset.ecombo:get() and var_0_3.IsValidTarget(var_28_0) then
		var_0_51(var_28_0)
	end

	if var_0_3.IsValidTarget(var_28_1) and var_0_13.combo.eset.ecombo:get() and var_0_0.core.get_pos_after_time(var_28_1, var_28_1.pos:dist(player.pos) / 2000 + 0.5):dist(player.pos:to2D()) <= var_0_9.range and var_0_25 == vec3(0, 0, 0) and player:spellSlot(2).name == "IreliaE" and player:spellSlot(2).state == 0 and var_0_30 < game.time then
		local var_28_4 = var_28_1.path.point[0]
		local var_28_5 = (var_28_1.path.point[var_28_1.path.count] - var_28_4):norm()
		local var_28_6 = var_0_0.core.get_pos_after_time(var_28_1, var_28_1.pos:dist(player.pos) / 2000 + 0.3)

		if not var_28_1.path.isActive then
			if var_28_1.pos:dist(player.pos) <= var_0_9.range then
				local var_28_7 = player.pos + (var_28_1.pos - player.pos):norm() * 900

				player:castSpell("pos", 2, var_28_7)
			end
		elseif var_28_6 then
			local var_28_8 = player.pos:to2D():dist(var_28_6)

			if var_28_8 <= 900 then
				if var_28_8 < player.pos:dist(var_28_1.pos) then
					var_28_5 = var_28_5 * -1
				end

				local var_28_9 = RaySetDist(var_28_1.pos, var_28_5, player.pos, 900)

				player:castSpell("pos", 2, var_28_9)
			end
		end
	end

	if var_0_3.IsValidTarget(var_28_0) then
		if var_28_0.pos:dist(player) < var_0_10.range then
			local var_28_10 = var_0_0.linear.get_prediction(var_0_10, var_28_0)

			if var_28_10 and var_28_10.startPos:dist(var_28_10.endPos) < var_0_10.range and var_0_13.combo.rset.hitr:get() <= #var_0_39(var_28_0.pos, 400) and var_0_36(var_0_10, var_28_10, var_28_0) then
				player:castSpell("pos", 3, vec3(var_28_10.endPos.x, var_28_0.pos.y, var_28_10.endPos.y))
			end
		end

		if var_0_13.combo.rset.rusage:get() == 2 and var_28_0.pos:dist(player) < var_0_10.range then
			local var_28_11 = var_0_0.linear.get_prediction(var_0_10, var_28_0)

			if var_28_11 and var_28_11.startPos:dist(var_28_11.endPos) < var_0_10.range and var_28_0.health / var_28_0.maxHealth * 100 <= var_0_13.combo.rset.hpr:get() and var_0_36(var_0_10, var_28_11, var_28_0) then
				player:castSpell("pos", 3, vec3(var_28_11.endPos.x, var_28_0.pos.y, var_28_11.endPos.y))
			end
		end

		if var_0_13.combo.rset.rusage:get() == 1 and QDamage(var_28_0) + RDamage(var_28_0) * 2 + EDamage(var_28_0) >= var_28_0.health and var_28_0.pos:dist(player) < var_0_10.range then
			local var_28_12 = var_0_0.linear.get_prediction(var_0_10, var_28_0)

			if var_28_12 and var_28_12.startPos:dist(var_28_12.endPos) < var_0_10.range and var_28_0.health / var_28_0.maxHealth * 100 >= var_0_13.combo.rset.saver:get() and var_0_36(var_0_10, var_28_12, var_28_0) then
				player:castSpell("pos", 3, vec3(var_28_12.endPos.x, var_28_0.pos.y, var_28_12.endPos.y))
			end
		end
	end

	if var_0_3.IsValidTarget(var_28_1) and var_0_13.combo.eset.ecombo:get() and var_28_1.pos:dist(player) <= var_0_9.range and not var_28_1.buff.ireliamark and player:spellSlot(2).name == "IreliaE2" and var_0_32 < game.time then
		CastE2(var_28_1)
	end

	local var_28_13 = var_0_23()

	if var_0_13.combo.qset.gapq:get() and var_0_3.IsValidTarget(var_28_13) and player.mana > player.manaCost0 then
		local var_28_14 = from(var_0_3.GetMinionsInRange(var_0_7.range, TEAM_ENEMY)):where(function(arg_31_0)
			return var_0_3.can_target_minion(arg_31_0) and arg_31_0.pos:dist(player.pos) <= var_0_7.range and (arg_31_0.health <= QDamage(arg_31_0) or arg_31_0.buff.ireliamark) and arg_31_0.pos:dist(var_28_13.pos) <= var_0_7.range
		end):min(function(arg_32_0)
			return arg_32_0.pos:dist(var_28_13.pos)
		end)

		if var_28_14 and var_28_14.pos:dist(var_28_13.pos) < var_28_13.pos:dist(player.pos) and var_28_13.pos:dist(player.pos) >= 250 and game.time - var_0_24 > 0.1 then
			player:castSpell("obj", 0, var_28_14)

			var_0_24 = game.time
		end
	end

	if var_0_3.IsValidTarget(var_28_0) and player:spellSlot(0).state ~= 0 and var_0_13.combo.wset.wcombo:get() and var_28_0.pos:dist(player) <= var_0_8.range - 150 and not player.buff.ireliawdefense then
		local var_28_15 = var_0_0.linear.get_prediction(var_0_8, var_28_0)

		if var_28_15 and var_28_15.startPos:dist(var_28_15.endPos) < var_0_8.range then
			player:castSpell("pos", 1, player.pos)
		end
	end
end

local function var_0_53()
	local var_33_0 = var_0_19()

	if var_0_3.IsValidTarget(var_33_0) and player:spellSlot(0).state == 0 and var_0_13.harass.qcombo:get() then
		if var_33_0.buff.ireliamark or var_33_0.health <= QDamage(var_33_0) or var_0_13.combo.qset.qmarked:get() ~= true then
			player:castSpell("obj", 0, var_33_0)
		end

		if var_0_13.harass.qcombo:get() and var_0_13.combo.qset.focusmarked:get() then
			local var_33_1 = from(var_0_3.GetEnemyHeroes()):where(function(arg_34_0)
				return var_0_3.IsValidTarget(arg_34_0) and arg_34_0.pos:dist(player.pos) <= var_0_7.range and arg_34_0.buff.ireliamark
			end):first()

			if var_33_1 then
				player:castSpell("obj", 0, var_33_1)
			end
		end
	end

	if var_0_3.IsValidTarget(var_33_0) and var_0_13.harass.ecombo:get() and var_0_3.IsValidTarget(var_33_0) then
		var_0_51(var_33_0)
	end

	if var_0_3.IsValidTarget(var_33_0) and var_0_13.harass.ecombo:get() and var_0_0.core.get_pos_after_time(var_33_0, var_33_0.pos:dist(player.pos) / 2000 + 0.3):dist(player.pos:to2D()) <= var_0_9.range - 50 and var_0_25 == vec3(0, 0, 0) and player:spellSlot(2).name == "IreliaE" and player:spellSlot(2).state == 0 and var_0_30 < game.time then
		local var_33_2 = var_33_0.path.point[0]
		local var_33_3 = (var_33_0.path.point[var_33_0.path.count] - var_33_2):norm()
		local var_33_4 = var_0_0.core.get_pos_after_time(var_33_0, var_33_0.pos:dist(player.pos) / 2000 + 0.5)

		if not var_33_0.path.isActive then
			if var_33_0.pos:dist(player.pos) <= var_0_9.range then
				local var_33_5 = player.pos + (var_33_0.pos - player.pos):norm() * 900

				player:castSpell("pos", 2, var_33_5)
			end
		elseif var_33_4 then
			local var_33_6 = player.pos:to2D():dist(var_33_4)

			if var_33_6 <= 900 then
				if var_33_6 < player.pos:dist(var_33_0.pos) then
					var_33_3 = var_33_3 * -1
				end

				local var_33_7 = RaySetDist(var_33_0.pos, var_33_3, player.pos, 900)

				player:castSpell("pos", 2, var_33_7)
			end
		end
	end

	if var_0_3.IsValidTarget(var_33_0) and var_0_13.harass.ecombo:get() and var_33_0.pos:dist(player) <= var_0_9.range and not var_33_0.buff.ireliamark and player:spellSlot(2).name == "IreliaE2" and var_0_32 < game.time then
		CastE2(var_33_0)
	end

	local var_33_8 = var_0_23()

	if var_0_13.harass.gapq:get() and var_0_3.IsValidTarget(var_33_8) and player.mana > player.manaCost0 then
		local var_33_9 = from(var_0_3.GetMinionsInRange(var_0_7.range, TEAM_ENEMY)):where(function(arg_35_0)
			return var_0_3.can_target_minion(arg_35_0) and arg_35_0.pos:dist(player.pos) <= var_0_7.range and (arg_35_0.health <= QDamage(arg_35_0) or arg_35_0.buff.ireliamark) and arg_35_0.pos:dist(var_33_8.pos) <= var_0_7.range
		end):min(function(arg_36_0)
			return arg_36_0.pos:dist(var_33_8.pos)
		end)

		if var_33_9 and var_33_9.pos:dist(var_33_8.pos) < var_33_8.pos:dist(player.pos) and var_33_8.pos:dist(player.pos) >= 250 and game.time - var_0_24 > 0.1 then
			player:castSpell("obj", 0, var_33_9)

			var_0_24 = game.time
		end
	end

	if var_0_3.IsValidTarget(var_33_0) and player:spellSlot(0).state ~= 0 and var_0_13.harass.wcombo:get() and var_33_0.pos:dist(player) <= var_0_8.range - 150 and not player.buff.ireliawdefense then
		local var_33_10 = var_0_0.linear.get_prediction(var_0_8, var_33_0)

		if var_33_10 and var_33_10.startPos:dist(var_33_10.endPos) < var_0_8.range then
			player:castSpell("pos", 1, player.pos)
		end
	end
end

local function var_0_54()
	if var_0_13.farming.laneclear.farmq:get() and player:spellSlot(0).state == 0 and game.time - var_0_24 > 0.1 then
		local var_37_0 = from(var_0_3.GetMinionsInRange(var_0_7.range, TEAM_ENEMY)):where(function(arg_38_0)
			return var_0_3.can_target_minion(arg_38_0) and #var_0_39(arg_38_0.pos, var_0_13.farming.laneclear.suicidalq:get()) == 0 and arg_38_0.health <= QDamage(arg_38_0) and (var_0_13.farming.laneclear.turret:get() ~= true or var_0_17(arg_38_0.pos) == false) and (var_0_13.farming.laneclear.qaa:get() ~= true or arg_38_0.pos:dist(player.pos) > 200)
		end):first()

		if var_37_0 then
			player:castSpell("obj", 0, var_37_0)

			var_0_24 = game.time
		end
	end
end

local function var_0_55()
	if var_0_13.farming.lasthit.farmq:get() and player:spellSlot(0).state == 0 and game.time - var_0_24 > 0.1 then
		local var_39_0 = from(var_0_3.GetMinionsInRange(var_0_7.range, TEAM_ENEMY)):where(function(arg_40_0)
			return var_0_3.can_target_minion(arg_40_0) and #var_0_39(arg_40_0.pos, var_0_13.farming.lasthit.suicidalq:get()) == 0 and arg_40_0.health <= QDamage(arg_40_0) and (var_0_13.farming.lasthit.turret:get() ~= true or var_0_17(arg_40_0.pos) == false) and (var_0_13.farming.lasthit.qaa:get() ~= true or arg_40_0.pos:dist(player.pos) > 200)
		end):first()

		if var_39_0 then
			player:castSpell("obj", 0, var_39_0)

			var_0_24 = game.time
		end

		local var_39_1 = from(var_0_3.GetMinionsInRange(var_0_7.range, TEAM_NEUTRAL)):where(function(arg_41_0)
			return var_0_3.can_target_minion(arg_41_0) and #var_0_39(arg_41_0.pos, var_0_13.farming.lasthit.suicidalq:get()) == 0 and arg_41_0.health <= QDamage(arg_41_0) and (var_0_13.farming.lasthit.turret:get() ~= true or var_0_17(arg_41_0.pos) == false) and (var_0_13.farming.lasthit.qaa:get() ~= true or arg_41_0.pos:dist(player.pos) > 200)
		end):first()

		if var_39_1 then
			player:castSpell("obj", 0, var_39_1)

			var_0_24 = game.time
		end
	end
end

local function var_0_56()
	if var_0_13.farming.jungleclear.usee:get() then
		local var_42_0 = from(var_0_3.GetMinionsInRange(var_0_9.range, TEAM_NEUTRAL)):where(function(arg_43_0)
			return var_0_3.can_target_minion(arg_43_0)
		end):first()

		if var_42_0 and var_0_3.IsValidTarget(var_42_0) then
			if var_0_13.combo.eset.ecombo:get() and var_0_0.core.get_pos_after_time(var_42_0, var_42_0.pos:dist(player.pos) / 2000 + 0.3):dist(player.pos:to2D()) <= var_0_9.range and var_0_25 == vec3(0, 0, 0) and player:spellSlot(2).name == "IreliaE" and player:spellSlot(2).state == 0 and var_0_30 < game.time then
				local var_42_1 = var_42_0.path.point[0]
				local var_42_2 = (var_42_0.path.point[var_42_0.path.count] - var_42_1):norm()
				local var_42_3 = var_0_0.core.get_pos_after_time(var_42_0, var_42_0.pos:dist(player.pos) / 2000 + 0.5)

				if not var_42_0.path.isActive then
					if var_42_0.pos:dist(player.pos) <= var_0_9.range then
						local var_42_4 = player.pos + (var_42_0.pos - player.pos):norm() * 900

						player:castSpell("pos", 2, var_42_4)
					end
				elseif var_42_3 then
					local var_42_5 = player.pos:to2D():dist(var_42_3)

					if var_42_5 <= 900 then
						if var_42_5 < player.pos:dist(var_42_0.pos) then
							var_42_2 = var_42_2 * -1
						end

						local var_42_6 = RaySetDist(var_42_0.pos, var_42_2, player.pos, 900)

						player:castSpell("pos", 2, var_42_6)
					end
				end
			end

			if var_42_0.pos:dist(player) <= var_0_9.range and player:spellSlot(2).name == "IreliaE2" and var_0_32 < game.time then
				CastE2(var_42_0)
			end
		end
	end

	if var_0_13.farming.jungleclear.useq:get() then
		local var_42_7 = from(var_0_3.GetMinionsInRange(var_0_7.range, TEAM_NEUTRAL)):where(function(arg_44_0)
			return var_0_3.can_target_minion(arg_44_0) and (var_0_13.farming.jungleclear.markedq:get() ~= true or arg_44_0.buff.ireliamark or arg_44_0.health <= QDamage(arg_44_0))
		end):first()

		if var_42_7 then
			player:castSpell("obj", 0, var_42_7)
		end
	end
end

local function var_0_57()
	if var_0_13.killsteal.ksq:get() and player:spellSlot(0).state == 0 then
		local var_45_0 = from(var_0_3.GetEnemyHeroes()):where(function(arg_46_0)
			return var_0_3.IsValidTarget(arg_46_0) and arg_46_0.pos:dist(player.pos) <= var_0_7.range and (var_0_13.killsteal.ksqaa:get() and arg_46_0.health <= QDamage(arg_46_0) + var_0_3.CalculateAADamage(arg_46_0) or var_0_13.killsteal.ksqaa:get() ~= true and arg_46_0.health <= QDamage(arg_46_0))
		end):first()

		if var_45_0 then
			player:castSpell("obj", 0, var_45_0)
		end
	end

	if var_0_13.killsteal.gapq:get() and player:spellSlot(0).state == 0 then
		local var_45_1 = from(var_0_3.GetEnemyHeroes()):where(function(arg_47_0)
			return var_0_3.IsValidTarget(arg_47_0) and arg_47_0.pos:dist(player.pos) > var_0_7.range and arg_47_0.pos:dist(player.pos) < var_0_7.range * 1.8 and arg_47_0.health <= QDamage(arg_47_0)
		end):first()

		if var_45_1 then
			local var_45_2 = from(var_0_3.GetMinionsInRange(var_0_7.range, TEAM_ENEMY)):where(function(arg_48_0)
				return var_0_3.can_target_minion(arg_48_0) and arg_48_0.pos:dist(player.pos) <= var_0_7.range and (arg_48_0.health <= QDamage(arg_48_0) or arg_48_0.buff.ireliamark) and arg_48_0.pos:dist(var_45_1.pos) <= var_0_7.range
			end):min(function(arg_49_0)
				return arg_49_0.pos:dist(var_45_1.pos)
			end)

			if var_45_2 then
				player:castSpell("obj", 0, var_45_2)
			end
		end
	end
end

local function var_0_58()
	if player.isDead then
		return
	end

	if player.isOnScreen then
		if var_0_13.draws.drawq:get() then
			graphics.draw_circle(player.pos, var_0_7.range, 2, var_0_13.draws.colorq:get(), 80)
		end

		if var_0_13.draws.draww:get() then
			graphics.draw_circle(player.pos, var_0_8.range, 2, var_0_13.draws.colorw:get(), 80)
		end

		if var_0_13.draws.drawe:get() then
			graphics.draw_circle(player.pos, var_0_9.range, 2, var_0_13.draws.colore:get(), 80)
		end

		if var_0_13.draws.drawr:get() then
			graphics.draw_circle(player.pos, var_0_10.range, 2, var_0_13.draws.colorr:get(), 80)
		end

		local var_50_0 = graphics.world_to_screen(vec3(player.x, player.y, player.z))

		graphics.draw_text_2D("Only Marked: ", 15, var_50_0.x - 50, var_50_0.y + 30, graphics.argb(255, 255, 255, 255))

		if var_0_13.combo.qset.qmarked:get() then
			graphics.draw_text_2D("ON", 15, var_50_0.x + 35, var_50_0.y + 30, graphics.argb(255, 128, 255, 0))
		else
			graphics.draw_text_2D("OFF", 15, var_50_0.x + 35, var_50_0.y + 30, graphics.argb(255, 218, 34, 34))
		end

		graphics.draw_text_2D("Farm: ", 15, var_50_0.x - 50, var_50_0.y + 20, graphics.argb(255, 255, 255, 255))

		if var_0_13.farming.toggle:get() then
			graphics.draw_text_2D("ON", 15, var_50_0.x - 10, var_50_0.y + 20, graphics.argb(255, 128, 255, 0))
		else
			graphics.draw_text_2D("OFF", 15, var_50_0.x - 10, var_50_0.y + 20, graphics.argb(255, 218, 34, 34))
		end
	end

	if var_0_13.draws.drawkill:get() and player:spellSlot(0).state == 0 then
		for iter_50_0 = 0, objManager.minions.size[TEAM_ENEMY] - 1 do
			local var_50_1 = objManager.minions[TEAM_ENEMY][iter_50_0]

			if var_0_3.can_target_minion(var_50_1) and QDamage(var_50_1) >= var_50_1.health and var_50_1.pos:dist(player.pos) < var_0_7.range + 300 then
				graphics.draw_circle(var_50_1.pos, var_50_1.boundingRadius, 2, graphics.argb(180, 255, 255, 51), 10)
			end
		end

		for iter_50_1 = 0, objManager.minions.size[TEAM_NEUTRAL] - 1 do
			local var_50_2 = objManager.minions[TEAM_NEUTRAL][iter_50_1]

			if var_0_3.can_target_minion(var_50_2) and QDamage(var_50_2) >= var_50_2.health and var_50_2.pos:dist(player.pos) < var_0_7.range + 300 then
				graphics.draw_circle(var_50_2.pos, var_50_2.boundingRadius, 2, graphics.argb(180, 255, 255, 51), 10)
			end
		end
	end

	if var_0_13.draws.drawdamage:get() then
		for iter_50_2 = 0, objManager.enemies_n - 1 do
			local var_50_3 = objManager.enemies[iter_50_2]

			if var_50_3 and var_50_3.isVisible and var_50_3.team == TEAM_ENEMY and var_50_3.isOnScreen then
				local var_50_4 = var_50_3.barPos
				local var_50_5 = var_50_4.x + 164
				local var_50_6 = var_50_4.y + 122.5
				local var_50_7 = player:spellSlot(0).state == 0 and QDamage(var_50_3) or 0
				local var_50_8 = var_50_3.health - var_50_7
				local var_50_9 = var_50_5 + var_50_3.health / var_50_3.maxHealth * 102
				local var_50_10 = var_50_5 + (var_50_8 > 0 and var_50_8 or 0) / var_50_3.maxHealth * 102

				if var_50_8 > 0 then
					graphics.draw_line_2D(var_50_9, var_50_6, var_50_10, var_50_6, 10, graphics.argb(var_0_13.draws.transparency:get(), 255, 192, 200))
				else
					graphics.draw_line_2D(var_50_9, var_50_6, var_50_10, var_50_6, 10, graphics.argb(var_0_13.draws.transparency:get(), 0, 255, 0))
				end
			end
		end
	end
end

local function var_0_59()
	local var_51_0 = var_0_19()

	if #var_0_39(player.pos, var_0_8.range - 150) == 0 and var_0_34 < game.time and keyboard.isKeyDown(87) == false and player.buff.ireliawdefense then
		player:castSpell("release", 1, mousePos)
	end

	if var_0_3.IsValidTarget(var_51_0) and var_0_34 < game.time then
		if keyboard.isKeyDown(87) == false and var_51_0.pos:dist(player) <= var_0_8.range and player.buff.ireliawdefense and game.time - var_0_33 > var_0_13.combo.wset.chargew:get() / 1000 then
			local var_51_1 = var_0_0.linear.get_prediction(var_0_8, var_51_0)

			if var_51_1 and var_51_1.startPos:dist(var_51_1.endPos) <= var_0_8.range then
				player:castSpell("release", 1, vec3(var_51_1.endPos.x, var_51_0.pos.y, var_51_1.endPos.y))
			else
				player:castSpell("release", 1, var_51_0.pos)
			end
		end

		if keyboard.isKeyDown(87) == true and var_51_0.pos:dist(player) <= var_0_8.range and player.buff.ireliawdefense and os.clock() - var_0_33 > 1.4 then
			local var_51_2 = var_0_0.linear.get_prediction(var_0_8, var_51_0)

			if var_51_2 and var_51_2.startPos:dist(var_51_2.endPos) <= var_0_8.range then
				player:castSpell("release", 1, vec3(var_51_2.endPos.x, var_51_0.pos.y, var_51_2.endPos.y))
			end
		end
	end
end

local function var_0_60()
	if var_0_13.flee.fleekey:get() then
		local var_52_0 = var_0_19()

		player:move(vec3(mousePos.x, mousePos.y, mousePos.z))

		if var_0_13.flee.fleeq:get() and player:spellSlot(0).state == 0 and not var_0_13.flee.fleekill:get() then
			local var_52_1 = from(var_0_3.GetMinionsInRange(var_0_7.range, TEAM_ENEMY)):where(function(arg_53_0)
				return var_0_3.can_target_minion(arg_53_0) and mousePos:dist(player.pos) > arg_53_0.pos:dist(mousePos)
			end):max(function(arg_54_0)
				return arg_54_0.pos:dist(player.pos)
			end)

			if var_52_1 then
				player:castSpell("obj", 0, var_52_1)
			end

			local var_52_2 = from(var_0_3.GetMinionsInRange(var_0_7.range, TEAM_NEUTRAL)):where(function(arg_55_0)
				return var_0_3.can_target_minion(arg_55_0) and mousePos:dist(player.pos) > arg_55_0.pos:dist(mousePos)
			end):max(function(arg_56_0)
				return arg_56_0.pos:dist(player.pos)
			end)

			if var_52_2 then
				player:castSpell("obj", 0, var_52_2)
			end
		end

		if var_0_13.flee.fleeq:get() and player:spellSlot(0).state == 0 and var_0_13.flee.fleekill:get() then
			local var_52_3 = from(var_0_3.GetMinionsInRange(var_0_7.range, TEAM_ENEMY)):where(function(arg_57_0)
				return var_0_3.can_target_minion(arg_57_0) and (arg_57_0.health <= QDamage(arg_57_0) or arg_57_0.buff.ireliamark) and mousePos:dist(player.pos) > arg_57_0.pos:dist(mousePos)
			end):max(function(arg_58_0)
				return arg_58_0.pos:dist(player.pos)
			end)

			if var_52_3 then
				player:castSpell("obj", 0, var_52_3)
			end

			local var_52_4 = from(var_0_3.GetMinionsInRange(var_0_7.range, TEAM_NEUTRAL)):where(function(arg_59_0)
				return var_0_3.can_target_minion(arg_59_0) and (arg_59_0.health <= QDamage(arg_59_0) or arg_59_0.buff.ireliamark) and mousePos:dist(player.pos) > arg_59_0.pos:dist(mousePos)
			end):max(function(arg_60_0)
				return arg_60_0.pos:dist(player.pos)
			end)

			if var_52_4 then
				player:castSpell("obj", 0, var_52_4)
			end
		end

		if var_0_13.flee.fleee:get() and player:spellSlot(2).state == 0 and var_0_3.IsValidTarget(var_52_0) then
			if var_0_0.core.get_pos_after_time(var_52_0, var_52_0.pos:dist(player.pos) / 2000 + 0.3):dist(player.pos:to2D()) <= var_0_9.range and var_0_25 == vec3(0, 0, 0) and player:spellSlot(2).name == "IreliaE" and player:spellSlot(2).state == 0 and var_0_30 < game.time then
				local var_52_5 = var_52_0.path.point[0]
				local var_52_6 = (var_52_0.path.point[var_52_0.path.count] - var_52_5):norm()
				local var_52_7 = var_0_0.core.get_pos_after_time(var_52_0, var_52_0.pos:dist(player.pos) / 2000 + 0.5)

				if not var_52_0.path.isActive then
					if var_52_0.pos:dist(player.pos) <= var_0_9.range then
						local var_52_8 = player.pos + (var_52_0.pos - player.pos):norm() * 900

						player:castSpell("pos", 2, var_52_8)
					end
				elseif var_52_7 then
					local var_52_9 = player.pos:to2D():dist(var_52_7)

					if var_52_9 <= 900 then
						if var_52_9 < player.pos:dist(var_52_0.pos) then
							var_52_6 = var_52_6 * -1
						end

						local var_52_10 = RaySetDist(var_52_0.pos, var_52_6, player.pos, 900)

						player:castSpell("pos", 2, var_52_10)
					end
				end
			end

			if var_52_0.pos:dist(player) <= var_0_9.range and player:spellSlot(2).name == "IreliaE2" and var_0_32 < game.time then
				CastE2(var_52_0)
			end
		end
	end
end

local function var_0_61()
	if var_0_5 then
		if var_0_13.dodgew.enablew:get() then
			for iter_61_0 = 1, #var_0_5.core.active_spells do
				local var_61_0 = var_0_5.core.active_spells[iter_61_0]

				if var_61_0.polygon and var_61_0.polygon:Contains(player.path.serverPos) ~= 0 and (not var_61_0.data.collision or #var_61_0.data.collision == 0) then
					for iter_61_1, iter_61_2 in pairs(var_0_6) do
						if var_0_13.dodgew[iter_61_2.charName] and iter_61_2.charName == var_61_0.owner.charName then
							if iter_61_2.charName == "Evelynn" and var_0_3.CheckBuff(player, "EvelynnW") then
								if var_61_0.data.slot == iter_61_2.slot and var_0_13.dodgew[iter_61_2.charName][iter_61_2.slot].Dodge:get() and var_0_13.dodgew[iter_61_2.charName][iter_61_2.slot].hp:get() >= player.health / player.maxHealth * 100 then
									if var_61_0.missile and player.pos:dist(var_61_0.missile.pos) / var_61_0.data.speed < network.latency + 0.35 then
										player:castSpell("pos", 1, player.pos)

										var_0_34 = game.time + 0.25
									end

									if iter_61_2.speed == math.huge or var_61_0.data.spell_type == "Circular" then
										player:castSpell("pos", 1, player.pos)

										var_0_34 = game.time + 0.25
									end
								end
							elseif var_61_0.data.slot == iter_61_2.slot and var_0_13.dodgew[iter_61_2.charName][iter_61_2.slot].Dodge:get() and var_0_13.dodgew[iter_61_2.charName][iter_61_2.slot].hp:get() >= player.health / player.maxHealth * 100 then
								if var_61_0.missile and player.pos:dist(var_61_0.missile.pos) / var_61_0.data.speed < network.latency + 0.35 then
									player:castSpell("pos", 1, player.pos)

									var_0_34 = game.time + 0.25
								end

								if var_61_0.data.speed == math.huge or var_61_0.data.spell_type == "Circular" then
									player:castSpell("pos", 1, player.pos)

									var_0_34 = game.time + 0.25
								end
							end
						end
					end
				end
			end
		end

		if not player.buff.ireliawdefense then
			if var_0_13.dodgew["Karthus" .. "R"] and var_0_13.dodgew["Karthus" .. "R"]:get() and var_0_3.CheckBuff(player, "karthusfallenonetarget") and (var_0_3.EndTime(player, "karthusfallenonetarget") - game.time) * 1000 <= 300 then
				player:castSpell("pos", 1, player.pos)

				var_0_34 = game.time + 0.25
			end

			if var_0_13.dodgew["Zed" .. "R"] and var_0_13.dodgew["Zed" .. "R"]:get() and var_0_3.CheckBuff(player, "zedrdeathmark") and (var_0_3.EndTime(player, "zedrdeathmark") - game.time) * 1000 <= 300 then
				player:castSpell("pos", 1, player.pos)

				var_0_34 = game.time + 0.25
			end

			if var_0_13.dodgew["Vladimir" .. "R"] and var_0_13.dodgew["Vladimir" .. "R"]:get() and var_0_3.CheckBuff(player, "vladimirhemoplaguedebuff") and (var_0_3.EndTime(player, "vladimirhemoplaguedebuff") - game.time) * 1000 <= 300 then
				player:castSpell("pos", 1, player.pos)

				var_0_34 = game.time + 0.25
			end
		end
	end
end

local function var_0_62()
	var_0_45()

	if var_0_13.combo.rset.rusage:get() == 2 then
		var_0_13.combo.rset.hpr:set("visible", true)
	else
		var_0_13.combo.rset.hpr:set("visible", false)
	end

	if player.isDead then
		return
	end

	var_0_59()
	var_0_57()
	var_0_60()
	var_0_61()

	if var_0_13.combo.semir:get() then
		local var_62_0 = var_0_19()

		if var_0_3.IsValidTarget(var_62_0) and var_62_0 and var_62_0.pos:dist(player) < var_0_10.range then
			local var_62_1 = var_0_0.linear.get_prediction(var_0_10, var_62_0)

			if var_62_1 and var_62_1.startPos:dist(var_62_1.endPos) < var_0_10.range and var_0_36(var_0_10, var_62_1, var_62_0) then
				player:castSpell("pos", 3, vec3(var_62_1.endPos.x, var_62_0.pos.y, var_62_1.endPos.y))
			end
		end
	end

	if var_0_2.menu.combat.key:get() then
		var_0_52()
	end

	if var_0_2.menu.hybrid.key:get() then
		var_0_53()
	end

	if var_0_2.menu.lane_clear.key:get() and var_0_13.farming.toggle:get() then
		var_0_54()
		var_0_56()
	end

	if var_0_2.menu.last_hit.key:get() and var_0_13.farming.toggle:get() then
		var_0_55()
	end
end

cb.add(cb.draw, var_0_58)
cb.add(cb.spell, var_0_49)
var_0_2.combat.register_f_pre_tick(var_0_62)

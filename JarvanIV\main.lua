local slot_20_0 = module.load(header.id, "JarvanIV/menu")
local slot_20_1 = module.load(header.id, "JarvanIV/misc")
local slot_20_2 = module.load(header.id, "Libraries/common1")
local slot_20_3 = module.seek("evade")
local slot_20_4 = module.internal("TS")
local slot_20_5 = module.internal("orb")
local slot_20_6 = module.internal("pred")
local Curses = module.load("<PERSON>", "Curses");
local slot_20_7
local slot_20_8 = slot_20_2.GetFlashNum()
local slot_20_9
local slot_20_10
local slot_20_11 = {}
local slot_20_12 = {
	boundingRadiusMod = 0,
	radius = 75,
	speed = 3200,
	delay = 0.5,
	collision = {
		hero = false,
		minion = false
	}
}
local slot_20_13 = {
	width = 70,
	boundingRadiusMod = 0,
	speed = 2000,
	delay = 0.25,
	collision = {
		hero = false,
		minion = false
	}
}
local slot_20_14 = {
	width = 70,
	boundingRadiusMod = 0,
	speed = 2000,
	delay = 0.25,
	collision = {
		hero = true,
		minion = true
	}
}
local slot_20_15 = {
	width = 70,
	boundingRadiusMod = 0,
	speed = 2000,
	delay = 0.3,
	collision = {
		hero = false,
		minion = false
	}
}
local slot_20_16 = {
	width = 70,
	boundingRadiusMod = 0,
	speed = 2000,
	delay = 0.3,
	collision = {
		hero = true,
		minion = true
	}
}
local slot_20_17 = {
	width = 70,
	boundingRadiusMod = 0,
	speed = 2000,
	delay = 0.3,
	collision = {
		hero = true,
		minion = false
	}
}

local function slot_20_18(arg_21_0, arg_21_1, arg_21_2)
	if arg_21_1 and arg_21_1.isVisible and arg_21_2 then
		local slot_21_0 = slot_20_6.linear.get_prediction(slot_20_15, arg_21_1)
		local slot_21_1 = slot_20_6.circular.get_prediction(slot_20_12, arg_21_1)
		local slot_21_2 = slot_20_6.linear.get_prediction(slot_20_13, arg_21_1)

		if slot_21_0 and slot_21_0.endPos and slot_21_0.startPos:dist(slot_21_0.endPos) < 850 and player.mana >= 55 + slot_20_1.Qmana() and player:spellSlot(_E).state == 0 and player:spellSlot(_Q).state == 0 and arg_21_2 and arg_21_2 < 850 then
			arg_21_0.obj = arg_21_1

			return true
		elseif slot_21_2 and slot_21_2.endPos and slot_21_2.startPos:dist(slot_21_2.endPos) < 800 and player:spellSlot(_Q).state == 0 and arg_21_2 and arg_21_2 < 800 then
			arg_21_0.obj = arg_21_1

			return true
		elseif slot_21_1 and slot_21_1.endPos and slot_21_1.startPos:dist(slot_21_1.endPos) < 850 and player:spellSlot(_E).state == 0 then
			arg_21_0.obj = arg_21_1

			return true
		elseif slot_20_5.combat.target then
			arg_21_0.obj = slot_20_5.combat.target

			return true
		elseif player:spellSlot(_R).state == 0 and arg_21_2 and arg_21_2 < 650 then
			arg_21_0.obj = arg_21_1

			return true
		end
	end
end

local function slot_20_19()
	return slot_20_5.ts.get_result(slot_20_18).obj
end

local function slot_20_20(arg_23_0, arg_23_1, arg_23_2)
	if arg_23_1 and arg_23_1.isVisible and arg_23_2 then
		local slot_23_0 = slot_20_6.linear.get_prediction(slot_20_15, arg_23_1)

		if slot_20_8 and player:spellSlot(slot_20_8).state == 0 and player:spellSlot(_E).state == 0 and player:spellSlot(_Q).state == 0 and slot_23_0 and slot_23_0.endPos and slot_23_0.startPos:dist(slot_23_0.endPos) < 1275 and player.mana >= 55 + slot_20_1.Qmana() and arg_23_2 and arg_23_2 < 1275 then
			arg_23_0.obj = arg_23_1

			return true
		elseif (not slot_20_8 or slot_20_8 and player:spellSlot(slot_20_8).state ~= 0) and slot_23_0 and slot_23_0.endPos and slot_23_0.startPos:dist(slot_23_0.endPos) < 850 and player.mana >= 55 + slot_20_1.Qmana() and player:spellSlot(2).state == 0 and player:spellSlot(_Q).state == 0 and arg_23_2 and arg_23_2 < 850 then
			arg_23_0.obj = arg_23_1

			return true
		end
	end
end

local function slot_20_21()
	return slot_20_5.ts.get_result(slot_20_20).obj
end

local function slot_20_22(arg_25_0, arg_25_1, arg_25_2, arg_25_3)
	if player:spellSlot(arg_25_2).state ~= 0 or not arg_25_0 or not slot_20_2.isValid(arg_25_0) then
		return false
	end

	local slot_25_0 = slot_20_6.linear.get_prediction(arg_25_1, arg_25_0)

	if not slot_20_5.core.is_spell_locked() and slot_25_0 and slot_25_0.endPos and slot_25_0.startPos:distSqr(slot_25_0.endPos) < arg_25_1.range ^ 2 and slot_20_2.trace_filter(arg_25_1, slot_25_0, arg_25_0) then
		if arg_25_3 and arg_25_1.count then
			local slot_25_1 = slot_20_6.collision.get_prediction(arg_25_1, slot_25_0)

			if slot_25_1 and arg_25_1.count and #slot_25_1 >= arg_25_1.count then
				player:castSpell("pos", arg_25_2, vec3(slot_25_0.endPos.x, arg_25_0.pos.y, slot_25_0.endPos.y))
			end
		else
			player:castSpell("pos", arg_25_2, vec3(slot_25_0.endPos.x, arg_25_0.pos.y, slot_25_0.endPos.y))
		end
	end
end

local function slot_20_23(arg_26_0)
	if player:spellSlot(0).state ~= 0 or not arg_26_0 or not slot_20_2.isValid(arg_26_0) then
		return false
	end

	local slot_26_0 = slot_20_6.linear.get_prediction(slot_20_13, arg_26_0)

	if slot_26_0 and slot_26_0.endPos and slot_26_0.startPos:distSqr(slot_26_0.endPos) < 640000 and slot_20_2.trace_filter(slot_20_13, slot_26_0, arg_26_0) then
		player:castSpell("pos", 0, vec3(slot_26_0.endPos.x, arg_26_0.pos.y, slot_26_0.endPos.y))
	end
end

local function slot_20_24(arg_27_0)
	if player:spellSlot(0).state ~= 0 or not arg_27_0 or not slot_20_2.isValid(arg_27_0) then
		return false
	end

	local slot_27_0 = slot_20_6.linear.get_prediction(slot_20_14, arg_27_0)

	if slot_27_0 and slot_27_0.endPos and slot_27_0.startPos:distSqr(slot_27_0.endPos) < 640000 then
		if slot_20_0.laneclear.hit:get() > 1 then
			local slot_27_1 = slot_20_6.collision.get_prediction(slot_20_14, slot_27_0)

			if slot_27_1 and #slot_27_1 >= slot_20_0.laneclear.hit:get() then
				player:castSpell("pos", 0, vec3(slot_27_0.endPos.x, arg_27_0.pos.y, slot_27_0.endPos.y))
			end
		else
			player:castSpell("pos", 0, vec3(slot_27_0.endPos.x, arg_27_0.pos.y, slot_27_0.endPos.y))
		end
	end
end

local function slot_20_25(arg_28_0)
	if player:spellSlot(0).state ~= 0 or not arg_28_0 or not slot_20_2.isValid(arg_28_0) then
		return false
	end

	if arg_28_0.name and not arg_28_0.name:lower():find("beakmini") then
		local slot_28_0 = slot_20_6.linear.get_prediction(slot_20_13, arg_28_0)

		if slot_28_0 and slot_28_0.endPos and slot_28_0.startPos:distSqr(slot_28_0.endPos) < 640000 then
			player:castSpell("pos", 0, vec3(slot_28_0.endPos.x, arg_28_0.pos.y, slot_28_0.endPos.y))
		end
	end
end

local function slot_20_26(arg_29_0)
	if player:spellSlot(_E).state ~= 0 or not arg_29_0 or not slot_20_2.isValid(arg_29_0) then
		return false
	end

	local function slot_29_0(arg_30_0, arg_30_1, arg_30_2)
		if slot_20_6.trace.circular.hardlock(arg_30_0, arg_30_1, arg_30_2) then
			return true
		end

		if slot_20_6.trace.circular.hardlockmove(arg_30_0, arg_30_1, arg_30_2) then
			return true
		end

		if arg_30_1.startPos:dist(arg_30_1.endPos) < 650 then
			return true
		end

		if slot_20_6.trace.newpath(arg_30_2, 0.033, 0.55) then
			return true
		end
	end

	local slot_29_1 = slot_20_6.circular.get_prediction(slot_20_12, arg_29_0)

	if slot_29_1 and slot_29_1.endPos and slot_29_1.startPos:distSqr(slot_29_1.endPos) < 640000 and slot_29_0(slot_20_12, slot_29_1, arg_29_0) then
		player:castSpell("pos", 2, vec3(slot_29_1.endPos.x, arg_29_0.pos.y, slot_29_1.endPos.y))
	end
end

local function slot_20_27(arg_31_0)
	if player:spellSlot(2).state ~= 0 or not arg_31_0 or not slot_20_2.isValid(arg_31_0) then
		return false
	end

	if arg_31_0.name and not arg_31_0.name:lower():find("beakmini") then
		local slot_31_0 = slot_20_6.circular.get_prediction(slot_20_12, arg_31_0)

		if slot_31_0 and slot_31_0.endPos and slot_31_0.startPos:distSqr(slot_31_0.endPos) < 640000 then
			player:castSpell("pos", 2, vec3(slot_31_0.endPos.x, arg_31_0.pos.y, slot_31_0.endPos.y))
		end
	end
end

local function slot_20_28(arg_32_0)
	if player:spellSlot(_E).state ~= 0 or player:spellSlot(_Q).state ~= 0 or not arg_32_0 or not slot_20_2.isValid(arg_32_0) then
		return false
	end

	local slot_32_0 = slot_20_6.linear.get_prediction(slot_20_15, arg_32_0)

	if slot_32_0 and slot_32_0.endPos and slot_32_0.startPos:distSqr(slot_32_0.endPos) < 722500 and slot_20_2.trace_filter(slot_20_15, slot_32_0, arg_32_0) then
		local slot_32_1 = slot_32_0.startPos +
		(slot_32_0.endPos - slot_32_0.startPos):norm() *
		(slot_32_0.startPos:dist(slot_32_0.endPos) + arg_32_0.boundingRadius + player.boundingRadius)

		if slot_32_1 and player.path.serverPos2D:dist(slot_32_1) < 240 + arg_32_0.boundingRadius then
			slot_32_1 = player.path.serverPos2D +
			(240 + arg_32_0.boundingRadius) / player.path.serverPos2D:dist(slot_32_1) *
			(slot_32_1 - player.path.serverPos2D)
		end

		player:castSpell("pos", _E, vec3(slot_32_1.x, arg_32_0.pos.y, slot_32_1.y))
		player:castSpell("pos", _Q, vec3(slot_32_1.x, arg_32_0.pos.y, slot_32_1.y))
	end
end

local function slot_20_29(arg_33_0)
	if player:spellSlot(2).state ~= 0 or player:spellSlot(0).state ~= 0 or not arg_33_0 or not slot_20_2.isValid(arg_33_0) then
		return false
	end

	local slot_33_0 = slot_20_6.linear.get_prediction(slot_20_16, arg_33_0)

	if slot_33_0 and slot_33_0.endPos and slot_33_0.startPos:distSqr(slot_33_0.endPos) < 640000 then
		local slot_33_1 = slot_33_0.startPos +
		(slot_33_0.endPos - slot_33_0.startPos):norm() *
		(slot_33_0.startPos:dist(slot_33_0.endPos) + arg_33_0.boundingRadius + player.boundingRadius)

		if slot_33_1 and player.path.serverPos2D:dist(slot_33_1) < 240 + arg_33_0.boundingRadius then
			slot_33_1 = player.path.serverPos2D +
			(240 + arg_33_0.boundingRadius) / player.path.serverPos2D:dist(slot_33_1) *
			(slot_33_1 - player.path.serverPos2D)
		end

		if slot_20_0.laneclear.hit:get() > 1 then
			local slot_33_2 = slot_20_6.collision.get_prediction(slot_20_16, slot_33_0)

			if slot_33_2 and #slot_33_2 >= slot_20_0.laneclear.hit:get() then
				player:castSpell("pos", 2, vec3(slot_33_1.x, arg_33_0.pos.y, slot_33_1.y))
				player:castSpell("pos", 0, vec3(slot_33_1.x, arg_33_0.pos.y, slot_33_1.y))
			end
		else
			player:castSpell("pos", 2, vec3(slot_33_1.x, arg_33_0.pos.y, slot_33_1.y))
			player:castSpell("pos", 0, vec3(slot_33_1.x, arg_33_0.pos.y, slot_33_1.y))
		end
	end
end

local function slot_20_30(arg_34_0)
	if player:spellSlot(2).state ~= 0 or player:spellSlot(0).state ~= 0 or not arg_34_0 or not slot_20_2.isValid(arg_34_0) then
		return false
	end

	if arg_34_0.name and not arg_34_0.name:lower():find("beakmini") then
		local slot_34_0 = slot_20_6.linear.get_prediction(slot_20_15, arg_34_0)

		if slot_34_0 and slot_34_0.endPos and slot_34_0.startPos:distSqr(slot_34_0.endPos) < 722500 then
			local slot_34_1 = slot_34_0.startPos +
			(slot_34_0.endPos - slot_34_0.startPos):norm() *
			(slot_34_0.startPos:dist(slot_34_0.endPos) + arg_34_0.boundingRadius + player.boundingRadius)

			if slot_34_1 and player.path.serverPos2D:dist(slot_34_1) < 240 + arg_34_0.boundingRadius then
				slot_34_1 = player.path.serverPos2D +
				(240 + arg_34_0.boundingRadius) / player.path.serverPos2D:dist(slot_34_1) *
				(slot_34_1 - player.path.serverPos2D)
			end

			player:castSpell("pos", 2, vec3(slot_34_1.x, arg_34_0.pos.y, slot_34_1.y))
			player:castSpell("pos", 0, vec3(slot_34_1.x, arg_34_0.pos.y, slot_34_1.y))
		end
	end
end

local function slot_20_31(arg_35_0)
	if player:spellSlot(_E).state ~= 0 or player:spellSlot(_Q).state ~= 0 or not arg_35_0 or not slot_20_2.isValid(arg_35_0) then
		return false
	end

	local slot_35_0 = slot_20_6.linear.get_prediction(slot_20_17, arg_35_0)

	if slot_35_0 and slot_35_0.endPos and slot_35_0.startPos:distSqr(slot_35_0.endPos) < 722500 and slot_20_2.trace_filter(slot_20_17, slot_35_0, arg_35_0) then
		local slot_35_1 = slot_35_0.startPos +
		(slot_35_0.endPos - slot_35_0.startPos):norm() *
		(slot_35_0.startPos:dist(slot_35_0.endPos) + arg_35_0.boundingRadius + player.boundingRadius)

		if slot_35_1 and player.path.serverPos2D:dist(slot_35_1) < 240 + arg_35_0.boundingRadius then
			slot_35_1 = player.path.serverPos2D +
			(240 + arg_35_0.boundingRadius) / player.path.serverPos2D:dist(slot_35_1) *
			(slot_35_1 - player.path.serverPos2D)
		end

		for iter_35_0 = 0, objManager.enemies_n - 1 do
			local slot_35_2 = objManager.enemies[iter_35_0]

			if slot_35_2 and slot_20_2.isValid(slot_35_2) and slot_35_2.team ~= TEAM_ALLY and slot_35_2.path.serverPos:dist(player.path.serverPos) < 425 + slot_35_2.boundingRadius + player.boundingRadius then
				local slot_35_3 = {
					startPos = slot_35_2.path.serverPos2D,
					endPos = slot_35_1
				}
				local slot_35_4 = slot_20_6.collision.get_prediction(slot_20_17, slot_35_3)
				local slot_35_5 = slot_20_6.collision.get_prediction(slot_20_17, slot_35_0, arg_35_0)

				if slot_35_2.path.serverPos2D:dist(slot_35_1) < 850 and not slot_35_5 and slot_35_4 and #slot_35_4 >= slot_20_0.eqflash.hit:get() then
					player:castSpell("pos", _E, vec3(slot_35_1.x, arg_35_0.pos.y, slot_35_1.y))
					player:castSpell("pos", _Q, vec3(slot_35_1.x, arg_35_0.pos.y, slot_35_1.y))

					local slot_35_6 = vec2(slot_35_0.endPos)

					slot_20_2.DelayAction(function()
						if slot_35_2 and slot_20_2.isValid(slot_35_2) then
							local slot_36_0 = slot_35_6 +
							(slot_35_2.path.serverPos2D - slot_35_6):norm() *
							(slot_35_2.path.serverPos2D:dist(slot_35_6) + arg_35_0.boundingRadius + player.boundingRadius)

							if slot_36_0:dist(slot_35_0.endPos) < 850 then
								player:castSpell("pos", slot_20_8,
									vec3(slot_36_0.x, slot_35_2.path.serverPos.y, slot_36_0.y))
							end
						end
					end, 0.25 + network.latency)
				else
					player:castSpell("pos", _E, vec3(slot_35_1.x, arg_35_0.pos.y, slot_35_1.y))
					player:castSpell("pos", _Q, vec3(slot_35_1.x, arg_35_0.pos.y, slot_35_1.y))
				end
			else
				player:castSpell("pos", _E, vec3(slot_35_1.x, arg_35_0.pos.y, slot_35_1.y))
				player:castSpell("pos", _Q, vec3(slot_35_1.x, arg_35_0.pos.y, slot_35_1.y))
			end
		end
	end
end

local function slot_20_32()
	if slot_20_0.combo.wcombo:get() and slot_20_5.core.can_action() and player:spellSlot(_W).state == 0 and slot_20_2.CountEnemiesNear(player.pos, 400) > 0 then
		player:castSpell("self", 1)
	end

	if not slot_20_7 or not slot_20_2.isValid(slot_20_7) then
		return
	end

	if slot_20_0.combo.emode:get() == 3 and slot_20_5.core.can_action() and player:spellSlot(_Q).state == 0 and player:spellSlot(_E).state == 0 then
		if slot_20_0.eqflash.usecombo:get() and slot_20_8 and player:spellSlot(slot_20_8).state == 0 and slot_20_5.core.can_action() and player.mana >= 55 + slot_20_1.Qmana() and slot_20_2.CountEnemiesNear(slot_20_7.pos, 600) > 1 then
			slot_20_31(slot_20_7)
		elseif slot_20_0.combo.epos:get() then
			local slot_37_0 = false

			if slot_20_11 and slot_20_11[1] then
				for iter_37_0, iter_37_1 in pairs(slot_20_11) do
					if iter_37_1.pos:distSqr(slot_20_7.pos) < 90000 and iter_37_1.pos:distSqr(player.pos) < 640000 then
						local slot_37_1, slot_37_2 = mathf.sect_line_circle(player.pos2D, iter_37_1.pos2D,
							slot_20_7.pos2D, 200)

						if slot_37_1 or slot_37_2 then
							slot_37_0 = true
						end
					end
				end
			end

			if slot_37_0 then
				slot_20_23(slot_20_7)
			else
				slot_20_28(slot_20_7)
			end
		else
			slot_20_28(slot_20_7)
		end
	end

	if slot_20_0.combo.emode:get() == 1 then
		if player:spellSlot(_Q).state == 0 and slot_20_5.core.can_action() and player:spellSlot(_E).state == 0 then
			if slot_20_0.eqflash.usecombo:get() and slot_20_8 and player:spellSlot(slot_20_8).state == 0 and slot_20_5.core.can_action() and player.mana >= 55 + slot_20_1.Qmana() and slot_20_2.CountEnemiesNear(slot_20_7.pos, 600) > 1 then
				slot_20_31(slot_20_7)
			elseif slot_20_0.combo.epos:get() then
				local slot_37_3 = false

				if slot_20_11 and slot_20_11[1] then
					for iter_37_2, iter_37_3 in pairs(slot_20_11) do
						if iter_37_3.pos:distSqr(slot_20_7.pos) < 90000 and iter_37_3.pos:distSqr(player.pos) < 640000 then
							local slot_37_4, slot_37_5 = mathf.sect_line_circle(player.pos2D, iter_37_3.pos2D,
								slot_20_7.pos2D, 200)

							if slot_37_4 or slot_37_5 then
								slot_37_3 = true
							end
						end
					end
				end

				if slot_37_3 then
					slot_20_23(slot_20_7)
				else
					slot_20_28(slot_20_7)
				end
			else
				slot_20_28(slot_20_7)
			end
		end

		if player:spellSlot(_Q).state == 0 and slot_20_5.core.can_action() and (player:spellSlot(_E).cooldown > 3 or player:spellSlot(2).cooldown > 2 and player.path.serverPos:dist(slot_20_7.path.serverPos) <= player.attackRange + player.boundingRadius + slot_20_7.boundingRadius or player:spellSlot(2).level < 1 or player.mana < 55 + slot_20_1.Qmana()) then
			slot_20_23(slot_20_7)
		end

		if player:spellSlot(_E).state == 0 and slot_20_5.core.can_action() and (player:spellSlot(_Q).cooldown > 3 or player:spellSlot(0).cooldown > 2 and not player.buff.jarvanivdemacianstandard and player.path.serverPos:dist(slot_20_7.path.serverPos) <= player.attackRange + player.boundingRadius + slot_20_7.boundingRadius or player:spellSlot(_Q).level < 1) then
			slot_20_26(slot_20_7)
		end
	end

	if slot_20_0.combo.emode:get() == 2 then
		if player:spellSlot(_Q).state == 0 and slot_20_5.core.can_action() and player:spellSlot(_E).state == 0 then
			if slot_20_0.eqflash.usecombo:get() and slot_20_8 and player:spellSlot(slot_20_8).state == 0 and slot_20_5.core.can_action() and player.mana >= 55 + slot_20_1.Qmana() and slot_20_2.CountEnemiesNear(slot_20_7.pos, 600) > 1 then
				slot_20_31(slot_20_7)
			elseif slot_20_0.combo.epos:get() then
				local slot_37_6 = false

				if slot_20_11 and slot_20_11[1] then
					for iter_37_4, iter_37_5 in pairs(slot_20_11) do
						if iter_37_5.pos:distSqr(slot_20_7.pos) < 90000 and iter_37_5.pos:distSqr(player.pos) < 640000 then
							local slot_37_7, slot_37_8 = mathf.sect_line_circle(player.pos2D, iter_37_5.pos2D,
								slot_20_7.pos2D, 200)

							if slot_37_7 or slot_37_8 then
								slot_37_6 = true
							end
						end
					end
				end

				if slot_37_6 then
					slot_20_23(slot_20_7)
				else
					slot_20_28(slot_20_7)
				end
			else
				slot_20_28(slot_20_7)
			end
		end

		if player:spellSlot(_Q).state == 0 and slot_20_5.core.can_action() then
			slot_20_23(slot_20_7)
		end

		if player:spellSlot(_E).state == 0 and slot_20_5.core.can_action() then
			slot_20_26(slot_20_7)
		end
	end

	if not slot_20_10 and slot_20_0.combo.rhit:get() == 1 and slot_20_0.combo.rcombo:get() and player:spellSlot(_R).state == 0 and slot_20_5.core.can_action() and player.pos:dist(slot_20_7.path.serverPos) < 650 and (slot_20_0.misc.waiteqr:get() and not player.isDashing or not slot_20_0.misc.waiteqr:get()) and slot_20_7.health <= slot_20_1.RDmg(slot_20_7) + slot_20_1.AADmg(slot_20_7) + slot_20_1.AADmg2(slot_20_7) then
		player:castSpell("obj", _R, slot_20_7)
	end
end

local function slot_20_33()
	if slot_20_0.harass.wharass:get() and slot_20_5.core.can_action() and player:spellSlot(_W).state == 0 and slot_20_2.CountEnemiesNear(player.pos, 400) > 0 then
		player:castSpell("self", 1)
	end

	if not slot_20_7 or not slot_20_2.isValid(slot_20_7) or player.mana * 100 / player.maxMana < slot_20_0.harass.mana:get() then
		return
	end

	if slot_20_0.harass.mode:get() == 1 and slot_20_5.core.can_action() then
		if slot_20_0.harass.eharass:get() and slot_20_0.harass.qharass:get() and player:spellSlot(_Q).state == 0 and player:spellSlot(_E).state == 0 and not slot_20_2.UnderTurret(slot_20_7.pos) then
			slot_20_28(slot_20_7)
		elseif slot_20_0.harass.qharass:get() and player:spellSlot(_Q).state == 0 then
			slot_20_23(slot_20_7)
		end
	end

	if slot_20_0.harass.mode:get() == 2 and slot_20_5.core.can_action() then
		if player:spellSlot(_Q).state == 0 then
			if slot_20_0.harass.epos:get() then
				if slot_20_11 and slot_20_11[1] then
					for iter_38_0, iter_38_1 in pairs(slot_20_11) do
						local slot_38_0, slot_38_1 = mathf.sect_line_circle(player.pos2D, iter_38_1.pos2D,
							slot_20_7.pos2D, 250)
						local slot_38_2, slot_38_3 = mathf.sect_line_circle(player.pos2D, slot_20_7.pos2D,
							iter_38_1.pos2D, 250)

						if not slot_38_0 and not slot_38_1 and not slot_38_2 and not slot_38_3 then
							slot_20_23(slot_20_7)
						end
					end
				else
					slot_20_23(slot_20_7)
				end
			else
				slot_20_23(slot_20_7)
			end
		end

		if player:spellSlot(_E).state == 0 then
			slot_20_26(slot_20_7)
		end
	end
end

local function slot_20_34()
	local slot_39_0 = objManager.minions

	if player.mana * 100 / player.maxMana >= slot_20_0.jungclear.mana:get() then
		for iter_39_0 = 0, slot_39_0.size[TEAM_NEUTRAL] - 1 do
			local slot_39_1 = slot_39_0[TEAM_NEUTRAL][iter_39_0]

			if slot_39_1 and not slot_39_1.isDead and slot_39_1.isVisible and slot_39_1.isTargetable and slot_39_1.pos:distSqr(player.pos) < 640000 and not slot_20_2.WardName(slot_39_1) then
				if slot_20_0.jungclear.jungcleareq:get() and player:spellSlot(0).state == 0 and slot_20_5.core.can_action() and player:spellSlot(2).state == 0 then
					slot_20_30(slot_39_1)
				end

				if slot_20_0.jungclear.jungclearq:get() and player:spellSlot(0).state == 0 and slot_20_5.core.can_action() and (player:spellSlot(2).cooldown > 3 or player:spellSlot(2).cooldown > 2 and player.path.serverPos:dist(slot_39_1.path.serverPos) <= player.attackRange + player.boundingRadius + slot_39_1.boundingRadius or player:spellSlot(2).level < 1) then
					slot_20_25(slot_39_1)
				end

				if slot_20_0.jungclear.jungcleare:get() and player:spellSlot(2).state == 0 and slot_20_5.core.can_action() and (player:spellSlot(0).cooldown > 3 or player:spellSlot(0).cooldown > 2 and not player.buff.jarvanivdemacianstandard and player.path.serverPos:dist(slot_39_1.path.serverPos) <= player.attackRange + player.boundingRadius + slot_39_1.boundingRadius or player:spellSlot(0).level < 1) then
					slot_20_27(slot_39_1)
				end
			end
		end
	end
end

local function slot_20_35()
	local slot_40_0 = objManager.minions

	if slot_20_0.laneclear.notuse:get() and slot_20_2.CountEnemiesNear(player.pos, 1500) > 0 or player.mana * 100 / player.maxMana < slot_20_0.laneclear.mana:get() then
		return
	end

	for iter_40_0 = 0, slot_40_0.size[TEAM_ENEMY] - 1 do
		local slot_40_1 = slot_40_0[TEAM_ENEMY][iter_40_0]

		if slot_40_1 and not slot_40_1.isDead and slot_40_1.isVisible and slot_40_1.isTargetable and slot_40_1.pos:distSqr(player.pos) < 640000 and not slot_20_2.WardName(slot_40_1) then
			if slot_20_0.laneclear.laneclearq:get() and player:spellSlot(0).state == 0 and slot_20_5.core.can_action() then
				slot_20_24(slot_40_1)
			end

			if slot_20_0.laneclear.lanecleare:get() and player:spellSlot(2).state == 0 then
				local slot_40_2 = {
					range = 800,
					boundingRadiusMod = 0,
					radius = 75,
					speed = 3200,
					delay = 0.5,
					damage = function(arg_41_0)
						return slot_20_1.EDmg(arg_41_0)
					end
				}
				local slot_40_3, slot_40_4 = slot_20_5.farm.skill_clear_circular(slot_40_2)

				if slot_40_3 then
					local slot_40_5 = player.path.serverPos2D:dist(slot_40_4.path.serverPos2D) / slot_40_2.speed

					if slot_20_5.farm.predict_hp(slot_40_4, slot_40_5) < slot_20_1.EDmg(slot_40_4) then
						if slot_20_0.laneclear.emode:get() == 2 and slot_20_5.core.can_action() and not slot_20_5.core.is_paused() and (slot_20_5.core.next_attack - os.clock() > slot_40_2.delay + network.latency or slot_20_5.core.next_attack - os.clock() < 0) then
							slot_20_27(slot_40_4)
						end

						if slot_20_0.laneclear.emode:get() == 1 then
							slot_20_27(slot_40_4)
						end
					end
				end
			end
		end
	end
end

local function slot_20_36()
	if not slot_20_5.menu.combat.key:get() and not slot_20_5.menu.hybrid.key:get() then
		player:move(mousePos)
	end

	if slot_20_9 and slot_20_2.isValid(slot_20_9) and player.path.serverPos:dist(slot_20_9.path.serverPos) <= 425 + player.boundingRadius and player.path.serverPos:dist(slot_20_9.path.serverPos) > player.boundingRadius and player.path.isDashing then
		player:castSpell("pos", slot_20_8, slot_20_9.path.serverPos)
	end

	if not slot_20_7 or not slot_20_2.isValid(slot_20_7) then
		return
	end

	if player:spellSlot(_Q).state == 0 and player:spellSlot(_E).state == 0 then
		local slot_42_0 = slot_20_6.linear.get_prediction(slot_20_15, slot_20_7)
		local slot_42_1 = slot_20_6.linear.get_prediction(slot_20_15, slot_20_7)

		if slot_42_0 and slot_42_0.endPos and slot_42_0.startPos:distSqr(slot_42_0.endPos) < 722500 and slot_20_2.trace_filter(slot_20_15, slot_42_0, slot_20_7) then
			player:castSpell("pos", _E, vec3(slot_42_0.endPos.x, slot_20_7.pos.y, slot_42_0.endPos.y))
			player:castSpell("pos", _Q, vec3(slot_42_0.endPos.x, slot_20_7.pos.y, slot_42_0.endPos.y))
		elseif slot_20_8 and player:spellSlot(slot_20_8).state == 0 and slot_42_1 and slot_42_1.endPos and slot_42_1.startPos:distSqr(slot_42_1.endPos) < 1625625 and slot_20_2.trace_filter2(slot_20_15, slot_42_1, slot_20_7, 1100) then
			slot_20_9 = slot_20_7

			player:castSpell("pos", _E, vec3(slot_42_0.endPos.x, slot_20_7.pos.y, slot_42_0.endPos.y))
			player:castSpell("pos", _Q, vec3(slot_42_0.endPos.x, slot_20_7.pos.y, slot_42_0.endPos.y))
			slot_20_2.DelayAction(function()
				slot_20_9 = nil
			end, 1)
		end
	else
		slot_20_32()
	end
end

local function slot_20_37()
	local slot_44_0 = mousePos

	if player.path.serverPos:distSqr(slot_44_0) > 722500 then
		slot_44_0 = player.path.serverPos +
		850 / player.path.serverPos:dist(slot_44_0) * (slot_44_0 - player.path.serverPos)
	end

	if player:spellSlot(_Q).state == 0 and player:spellSlot(_E).state == 0 and not navmesh.isWall(vec3(slot_44_0.x, slot_44_0.y, slot_44_0.z)) then
		player:castSpell("pos", _E, vec3(slot_44_0.x, slot_44_0.y, slot_44_0.z))
		player:castSpell("pos", _Q, vec3(slot_44_0.x, slot_44_0.y, slot_44_0.z))
	end

	movepos = player.path.serverPos + 150 / player.path.serverPos:dist(mousePos) * (mousePos - player.path.serverPos)

	if not slot_20_5.menu.combat.key:get() and not slot_20_5.menu.hybrid.key:get() then
		player:move(movepos)
	end
end

local function slot_20_38()
	for iter_45_0 = 0, objManager.enemies_n - 1 do
		local slot_45_0 = objManager.enemies[iter_45_0]

		if slot_45_0 and slot_20_2.isValid(slot_45_0) and slot_45_0.team ~= TEAM_ALLY then
			if slot_20_5.menu.combat.key:get() and slot_20_0.combo.rcombo:get() and slot_20_0.combo.rhit:get() > 1 and slot_20_2.CountEnemiesNear(slot_45_0, 350) >= slot_20_0.combo.rhit:get() and player.pos:dist(slot_45_0) <= 650 and (slot_20_0.misc.waiteqr:get() and not player.isDashing or not slot_20_0.misc.waiteqr:get()) then
				player:castSpell("obj", 3, slot_45_0)
			end

			if not slot_45_0.buff.willrevive and not slot_45_0.buff.undyingrage and not slot_45_0.buff.sionpassivezombie then
				if slot_20_0.killsteal.killstealq:get() and slot_45_0.health <= slot_20_1.QDmg(slot_45_0) and player:spellSlot(1).state == 0 and player.pos:distSqr(slot_45_0) <= 640000 then
					slot_20_23(slot_45_0)
				elseif slot_20_0.killsteal.killsteale:get() and slot_45_0.health <= slot_20_1.EDmg(slot_45_0) and player:spellSlot(2).state == 0 and player.pos:distSqr(slot_45_0) <= 640000 then
					slot_20_26(slot_45_0)
				elseif slot_20_0.killsteal.killsteale:get() and slot_20_0.killsteal.killstealq:get() and slot_45_0.health <= slot_20_1.EDmg(slot_45_0) + slot_20_1.QDmg(slot_45_0) and player:spellSlot(2).state == 0 and player:spellSlot(0).state == 0 and player.pos:distSqr(slot_45_0) <= 722500 then
					slot_20_28(slot_45_0)
				elseif slot_20_0.killsteal.killstealr:get() and slot_45_0.health <= slot_20_1.RDmg(slot_45_0) + slot_20_1.AADmg(slot_45_0) + slot_20_1.AADmg2(slot_45_0) and player:spellSlot(3).state == 0 and player.pos:distSqr(slot_45_0) <= 422500 then
					player:castSpell("obj", 3, slot_45_0)
				end
			end
		end
	end
end

cb.add(cb.spell, function(arg_46_0)
	if arg_46_0 and arg_46_0.name then
		if arg_46_0.owner and arg_46_0.owner.team ~= TEAM_ALLY and arg_46_0.owner.type == TYPE_HERO and arg_46_0.target and arg_46_0.target == player and (slot_20_5.menu.combat.key:get() and slot_20_0.combo.wcombo:get() or slot_20_5.menu.hybrid.key:get() and slot_20_0.harass.wharass:get() or slot_20_0.flee:get()) and player:spellSlot(_W).state == 0 and slot_20_5.core.can_action() then
			player:castSpell("self", _W)
		end

		if arg_46_0.owner and arg_46_0.owner.team == TEAM_NEUTRAL and arg_46_0.target and arg_46_0.target == player and slot_20_5.menu.lane_clear.key:get() and slot_20_0.jungclear.jungclearw:get() and player.mana * 100 / player.maxMana >= slot_20_0.jungclear.mana:get() and player:spellSlot(1).state == 0 and slot_20_5.core.can_action() then
			player:castSpell("self", 1)
		end

		if arg_46_0.owner and arg_46_0.owner == player then
			if not slot_20_10 and arg_46_0.name == "JarvanIVCataclysm" then
				slot_20_10 = vec3(arg_46_0.endPos.x, arg_46_0.endPos.y, arg_46_0.endPos.z)
			end

			if arg_46_0.name == "ItemTitanicHydraCleave" then
				slot_20_5.core.reset()
			end
		end
	end
end)
cb.add(cb.tick, function()
	if player.isDead then
		return
	end

	if slot_20_0.misc.autorn:get() and slot_20_10 and player:spellSlot(_R).state == 0 and slot_20_5.core.can_action() and slot_20_2.CountEnemiesNear(slot_20_10, 350) == 0 then
		player:castSpell("self", 3)
	end

	slot_20_38()

	if slot_20_0.eqflashkey:get() then
		slot_20_7 = slot_20_21()
	else
		slot_20_7 = slot_20_19()
	end

	if slot_20_5.menu.combat.key:get() then
		slot_20_32()
	elseif slot_20_5.menu.hybrid.key:get() then
		slot_20_33()
	elseif slot_20_5.menu.lane_clear.key:get() then
		slot_20_34()
		slot_20_35()
	elseif slot_20_0.flee:get() then
		slot_20_37()
	elseif slot_20_0.eqflashkey:get() then
		slot_20_36()
	end
end)
cb.add(cb.create_particle, function(arg_48_0)
	if arg_48_0.ptr and arg_48_0 and arg_48_0.name == "JarvanIV_Base_E_buf_green" then
		table.insert(slot_20_11, arg_48_0)
	end
end)
cb.add(cb.delete_particle, function(arg_49_0)
	if arg_49_0.ptr and arg_49_0 and arg_49_0.name == "JarvanIV_Base_R_sound_01" then
		slot_20_10 = nil
	end

	for iter_49_0 = #slot_20_11, 1, -1 do
		if slot_20_11[iter_49_0] == arg_49_0 then
			table.remove(slot_20_11, iter_49_0)
		end
	end
end)
cb.add(cb.tick, function()
	if slot_20_3 and slot_20_3.menu.input_blocking == nil then
		for iter_50_0 = slot_20_3.core.skillshots.n, 1, -1 do
			local slot_50_0 = slot_20_3.core.skillshots[iter_50_0]

			if slot_50_0 and slot_50_0.owner and slot_50_0.owner.type == TYPE_HERO then
				local slot_50_1 = string.lower(slot_50_0.name)

				if slot_20_0.Interruptibles.enable:get() and slot_20_0.Interruptibles[slot_50_1] and slot_20_0.Interruptibles[slot_50_1]:get() and player:spellSlot(0).state == 0 and player:spellSlot(0).name == "YasuoQ3Wrapper" and slot_50_0.owner.pos:distSqr(player.pos) <= 1322500 then
					slot_20_28(slot_50_0.owner)
				end
			end
		end

		for iter_50_1 = slot_20_3.core.targeted.n, 1, -1 do
			local slot_50_2 = slot_20_3.core.targeted[iter_50_1]

			if slot_50_2 and slot_50_2.owner and slot_50_2.owner.type == TYPE_HERO and slot_50_2.target then
				local slot_50_3 = string.lower(slot_50_2.name)

				if slot_20_0.Interruptibles.enable:get() and slot_20_0.Interruptibles[slot_50_3] and slot_20_0.Interruptibles[slot_50_3]:get() and player:spellSlot(0).state == 0 and player:spellSlot(0).name == "YasuoQ3Wrapper" and slot_50_2.owner.pos:distSqr(player.pos) <= 1322500 then
					slot_20_28(slot_50_2.owner)
				end
			end
		end
	end
end)
local rainbowColors = {
    graphics.argb(255, 255, 0, 0), -- 红色
    graphics.argb(255, 255, 165, 0), -- 橙色
    graphics.argb(255, 255, 255, 0), -- 黄色
    graphics.argb(255, 0, 255, 0), -- 绿色
    graphics.argb(255, 0, 0, 255), -- 蓝色
    graphics.argb(255, 75, 0, 130), -- 靛色
    graphics.argb(255, 238, 130, 238) -- 紫色
}

local colorIndex = 0
local colorChangeCounter = 0
local colorChangeInterval = 100 -- 控制颜色变化的频率，数值越大，颜色变化越慢

cb.add(cb.draw, function()
    if player.isOnScreen then
        if player.isDead then
            return
        end

        if slot_20_0.draws.target:get() and slot_20_7 and slot_20_2.isValid(slot_20_7) and slot_20_7.isOnScreen then
            slot_20_2.drawCircle(slot_20_7.pos.xz, 0.05, 0.01, 1.5, 1.5)
        end

        if slot_20_0.draws.drawq:get() then
            if slot_20_0.draws.drawsmode:get() == 1 then
                graphics.draw_circle_xyz(player.x, player.y, player.z, 800, 1, rainbowColors[colorIndex + 1], 100)
            else
                graphics.draw_circle_xyz(player.x, player.y, player.z, 800, 1, rainbowColors[colorIndex + 1], 100)
            end
        end

        if slot_20_0.draws.drawe:get() and slot_20_11 then
            for iter_51_0, iter_51_1 in pairs(slot_20_11) do
                graphics.draw_circle_xyz(iter_51_1.pos.x, iter_51_1.pos.y, iter_51_1.pos.z, 100, 2, rainbowColors[colorIndex + 1], 100)
            end
        end

        if slot_20_0.draws.damage:get() then
            for iter_51_2 = 0, objManager.enemies_n - 1 do
                local slot_51_0 = objManager.enemies[iter_51_2]

                if slot_51_0 and slot_51_0.team and slot_51_0.type and slot_51_0.team == TEAM_ENEMY and slot_51_0.isVisible and slot_51_0.type == TYPE_HERO and slot_51_0.isOnScreen and slot_51_0.barPos then
                    local slot_51_1 = graphics.height > 1080 and graphics.height / 1080 * 0.905 or 1
                    local slot_51_2 = vec2(109 * slot_51_1, 111 * slot_51_1)
                    local slot_51_3 = vec2(54 * slot_51_1, 11 * slot_51_1)
                    local slot_51_4 = slot_51_0.barPos + slot_51_2 + slot_51_3
                    local slot_51_5 = slot_51_4.x
                    local slot_51_6 = slot_51_4.y
                    local slot_51_7 = player:spellSlot(0).state == 0 and slot_20_1.QDmg(slot_51_0) or 0
                    local slot_51_8 = player:spellSlot(1).state == 0 and slot_20_1.EDmg(slot_51_0) or 0
                    local slot_51_9 = player:spellSlot(3).state == 0 and slot_20_1.RDmg(slot_51_0) or 0
                    local slot_51_10 = slot_51_0.health -
                    (slot_51_7 + slot_51_8 + slot_51_9 + slot_20_1.AADmg(slot_51_0) + slot_20_1.AADmg(slot_51_0))
                    local slot_51_11 = slot_51_5 + slot_51_0.health / slot_51_0.maxHealth * 102 * slot_51_1
                    local slot_51_12 = slot_51_5 +
                    (slot_51_10 > 0 and slot_51_10 or 0) / slot_51_0.maxHealth * 102 * slot_51_1

                    graphics.draw_line_2D(slot_51_11, slot_51_6, slot_51_12, slot_51_6, 10.5, **********)
                end
            end
        end

        colorChangeCounter = colorChangeCounter + 1
        if colorChangeCounter >= colorChangeInterval then
            colorIndex = (colorIndex + 1) % #rainbowColors
            colorChangeCounter = 0
        end
    end
end)
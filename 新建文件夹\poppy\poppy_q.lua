
local ove_0_5 = require("TS/main")
local ove_0_6 = require("pred/main")
local ove_0_7 = require("orb/main")
local ove_0_8 = require("poppy/menu")
local ove_0_9 = player:spellSlot(0)
local ove_0_10 = {
	range = 450,
	delay = 0.332,
	boundingRadiusMod = 1,
	width = 75,
	speed = math.huge,
	damage = -- print(arg_1_0)
		-- -- print 1
		return 20 * ove_0_9.level + 20 + player.flatPhysicalDamageMod * 0.8 + arg_1_0.maxHealth * 0.08
	end
}

local -- print ove_0_11(arg_2_0, arg_2_1, arg_2_2)
	-- -- print 2
	if arg_2_2 > ove_0_10.range then
		return false
	end

	local slot_2_0 = ove_0_6.linear.get_prediction(ove_0_10, arg_2_1)

	if not slot_2_0 then
		return false
	end

	if slot_2_0.startPos:dist(slot_2_0.endPos) > ove_0_10.range - 100 then
		return false
	end

	arg_2_0.pos = slot_2_0.endPos

	return true
end

local -- print ove_0_12()
	-- -- print 3
	if ove_0_9.state ~= 0 then
		return
	end

	local slot_3_0 = ove_0_5.get_result(ove_0_11)

	if slot_3_0.pos and player:castSpell("pos", 0, vec3(slot_3_0.pos.x, mousePos.y, slot_3_0.pos.y)) then
		ove_0_7.core.set_server_pause()

		return true
	end
end

local -- print ove_0_13(arg_4_0)
	-- -- print 4
	if ove_0_9.state ~= 0 then
		return
	end

	local slot_4_0 = player.path.serverPos2D
	local slot_4_1 = {}
	local slot_4_2 = 0

	for iter_4_0 = 0, objManager.minions.size[arg_4_0] - 1 do
		local slot_4_3 = objManager.minions[arg_4_0][iter_4_0]

		if slot_4_3.isVisible and slot_4_3.pos2D:dist(ove_0_7.core.cur_attack_target.pos2D) < 400 and slot_4_3.path.serverPos2D:dist(slot_4_0) < ove_0_10.range then
			slot_4_2 = slot_4_2 + 1
			slot_4_1[slot_4_2] = slot_4_3
		end
	end

	local slot_4_4
	local slot_4_5 = 0

	for iter_4_1 = 1, slot_4_2 do
		local slot_4_6 = slot_4_0 + (slot_4_1[iter_4_1].path.serverPos2D - slot_4_0):norm() * ove_0_10.range
		local slot_4_7 = 1

		for iter_4_2 = 1, slot_4_2 do
			if iter_4_1 ~= iter_4_2 then
				local slot_4_8 = slot_4_1[iter_4_2]
				local slot_4_9 = mathf.closest_vec_line_seg(slot_4_8.path.serverPos2D, slot_4_0, slot_4_6)

				if slot_4_9 and slot_4_9:dist(slot_4_8.path.serverPos2D) < 125 then
					slot_4_7 = slot_4_7 + 1
				end
			end
		end

		if not slot_4_4 or slot_4_5 < slot_4_7 then
			slot_4_4 = slot_4_6
			slot_4_5 = slot_4_7
		end
	end

	if slot_4_4 and player:castSpell("pos", 0, vec3(slot_4_4.x, mousePos.y, slot_4_4.y)) then
		ove_0_7.core.set_server_pause()

		return true
	end
end

return {
	invoke = ove_0_12,
	invoke_clear = ove_0_13
}

local lvxbot = module.load(header.id, 'lvxbot/main')

local input = {
  prediction = {
    type = 'Linear',
    --
    range = 920,
    delay = 0.25,
    speed = 1800,
    width = 80,
	mov = 0.01,
	movtime = 0.35,
	movseep = 1000,
    boundingRadiusMod = 1,
	PredZs = 0,
    --
    collision = {
      hero = true, --allow to hit other heros :-)
      minion = true,
      wall = true,
    },
    --
    hitchance = 0,
  },

  target_selector = {
    type = 'STRICT_PRIO',
  },

  cast_spell = {
    type = 'pos',
    slot = _Q,
    arg1 = function(action)
      local seg = action.ts_result.seg
      local obj = action.ts_result.obj
      return seg.endPos:to3D(obj.y)
    end,
  },

  slot = _Q,
  ignore_obj_radius = 2000,

}

return lvxbot.expert.create(input)


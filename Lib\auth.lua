

local ove_0_10 = {
	isUserAuthed = "exception",
	Hwid = hanbot.hwid,
	user = hanbot.user,
	CheckFile = function()
		-- print 5
		if module.file_exists(hanbot.path .. "\\KPAIO.txt") then
			return true
		end

		return false
	end,
	WriteFile = function(arg_6_0)
		-- print 6
		file = io.open(hanbot.path .. "\\KPAIO.txt", "w")

		file:write(arg_6_0)
		file:close()
	end,
	ReadFile = function()
		-- print 7
		local slot_7_0 = hanbot.path .. "\\KPAIO.txt"
		local slot_7_1 = assert(io.open(slot_7_0, "rb"))
		local slot_7_2 = slot_7_1:read("*all")

		slot_7_1:close()

		return slot_7_2
	end
}

function ove_0_10.FileHandle()
	-- print 8
	if ove_0_10.CheckFile() then
		return true
	else
		ove_0_10.WriteFile("FREEVERSION")

		return true
	end

	return false
end

function ove_0_10.Need()
	-- print 9
	if ove_0_10.FileHandle() then
		ove_0_10.Connection()

		return true
	end

	print("Error accours while file creation")

	return false
end

function ove_0_10.Connection()
	-- print 10
	network.easy_get(function(arg_11_0, arg_11_1, arg_11_2)
		-- print 11
		ove_0_10.isUserAuthed = arg_11_1
	end, "http://krystra.com/hauth.php?hwid=" .. hanbot.hwid .. "&user=" .. hanbot.user .. "&sub=" .. ove_0_10.ReadFile())
end

function ove_0_10.isAuthed()
	-- print 12
	if ove_0_10.isUserAuthed == "exception" then
		return
	end

	if ove_0_10.isUserAuthed:find("true") then
		return true
	elseif ove_0_10.isUserAuthed:find("false") then
		return false
	end

	return ove_0_10.isUserAuthed
end

return ove_0_10

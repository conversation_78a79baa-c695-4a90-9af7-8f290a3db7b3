local var_0_0 = "1.0"
local var_0_1 = module.seek("evade")
local var_0_2 = module.internal("pred")
local var_0_3 = module.internal("TS")
local var_0_4 = module.internal("orb")



local var_0_5 = module.load("<PERSON>", "Utility/kcommon")
local var_0_6 = module.load("<PERSON>", "Utility/SpellDatabaseSupport")
local var_0_7 = {
	speed = 1500,
	range = 1150,
	delay = 0.25,
	boundingRadiusMod = 1,
	width = 60
}
local var_0_8 = {
	range = 400
}
local var_0_9 = {
	range = 425
}
local var_0_10 = {
	range = 2500
}
local var_0_11 = {
	[0] = "Q",
	"W",
	"E",
	[-1] = "P",
	[3] = "R"
}
local var_0_12 = menu("Brian" .. player.charName, "[<PERSON>] " .. player.charName)

var_0_12:menu("combo", "Combo")
var_0_12.combo:boolean("qcombo", "Use Q", true)
var_0_12.combo:boolean("ecombo", "Use E", true)
var_0_12.combo:boolean("rcombo", "Use R", true)
var_0_12.combo:slider("hpr", " ^- Min. R Range", 500, 0, 600, 5)
var_0_12.combo:slider("dontr", "Don't R in X Enemies", 3, 2, 5, 1)
var_0_12:menu("harass", "Harass")
var_0_12.harass:boolean("qcombo", "Use Q", true)
var_0_12.harass:boolean("ecombo", "Use E", true)
var_0_12:menu("blacklist", "E Blacklist")

local var_0_13 = var_0_5.GetEnemyHeroes()

for iter_0_0, iter_0_1 in ipairs(var_0_13) do
	var_0_12.blacklist:boolean(iter_0_1.charName, "Block: " .. iter_0_1.charName, false)
end

var_0_12:menu("SpellsMenu", "W Shielding")
var_0_12.SpellsMenu:boolean("enable", "Enable Shielding", true)
var_0_12.SpellsMenu:header("hello", " -- Enemy Skillshots -- ")

for iter_0_2, iter_0_3 in pairs(var_0_6) do
	for iter_0_4, iter_0_5 in pairs(var_0_5.GetEnemyHeroes()) do
		if iter_0_3.charName == iter_0_5.charName then
			if iter_0_3.displayname == "" then
				iter_0_3.displayname = iter_0_2
			end

			if iter_0_3.danger == 0 then
				iter_0_3.danger = 1
			end

			if var_0_12.SpellsMenu[iter_0_3.charName] == nil then
				var_0_12.SpellsMenu:menu(iter_0_3.charName, iter_0_3.charName)
			end

			var_0_12.SpellsMenu[iter_0_3.charName]:menu(iter_0_3.slot, "" .. iter_0_3.charName .. " | " .. (var_0_11[iter_0_3.slot] or "?"))
			var_0_12.SpellsMenu[iter_0_3.charName][iter_0_3.slot]:boolean("Dodge", "Enable Block", true)
			var_0_12.SpellsMenu[iter_0_3.charName][iter_0_3.slot]:slider("hp", "HP to Dodge", 100, 1, 100, 5)
		end
	end
end

var_0_12.SpellsMenu:header("hello", " -- Targeted Spells -- ")

for iter_0_6 = 1, #var_0_5.GetEnemyHeroes() do
	local var_0_14 = var_0_5.GetEnemyHeroes()[iter_0_6]
	local var_0_15 = string.lower(var_0_14.charName)

	if var_0_14 and var_0_5.TargetedSpells()[var_0_15] then
		for iter_0_7 = 1, #var_0_5.TargetedSpells()[var_0_15] do
			local var_0_16 = var_0_5.TargetedSpells()[var_0_15][iter_0_7]

			if var_0_12.SpellsMenu[tostring(var_0_14.charName) .. tostring(var_0_16.slot)] == nil then
				var_0_12.SpellsMenu:menu(tostring(var_0_14.charName) .. tostring(var_0_16.slot), "" .. var_0_14.charName .. " | " .. var_0_16.menuslot)
			end

			var_0_12.SpellsMenu[tostring(var_0_14.charName) .. tostring(var_0_16.slot)]:boolean("Dodge", "Dodge", true)
			var_0_12.SpellsMenu[tostring(var_0_14.charName) .. tostring(var_0_16.slot)]:slider("hp", "HP to Dodge", 80, 1, 100, 5)
		end
	end
end

var_0_12:menu("farming", "Farming")
var_0_12.farming:keybind("toggle", "Farm Toggle", nil, "A")
var_0_12.farming:header("uwu", " ~~~~ ")
var_0_12.farming:menu("laneclear", "Lane Clear")
var_0_12.farming.laneclear:boolean("farmq", "Use Q", true)
var_0_12.farming.laneclear:slider("hitsq", " ^- if Hits X Minions", 3, 1, 6, 1)
var_0_12.farming:menu("jungleclear", "Jungle Clear")
var_0_12.farming.jungleclear:boolean("useq", "Use Q", true)
var_0_12.farming.jungleclear:boolean("usee", "Use E", true)
var_0_12:menu("draws", "Draw Settings")
var_0_12.draws:header("ranges", " -- Ranges -- ")
var_0_12.draws:boolean("drawq", "Draw Q Range", true)
var_0_12.draws:color("colorq", "  ^- Color", 255, 255, 255, 255)
var_0_12.draws:boolean("drawe", "Draw E Range", true)
var_0_12.draws:color("colore", "  ^- Color", 255, 255, 255, 255)
var_0_12.draws:boolean("drawr", "Draw R Range", true)
var_0_12.draws:color("colorr", "  ^- Color", 255, 102, 51, 0)
var_0_12.draws:header("other", " -- Other -- ")
var_0_12.draws:boolean("drawdamage", "Draw Damage", true)
var_0_12.draws:slider("transparency", "Damage Drawing Transparency", 155, 0, 255, 1)
var_0_12:menu("misc", "Misc.")
var_0_12.misc:menu("Gap", "Gapcloser Settings")
var_0_12.misc.Gap:boolean("GapA", "Use E for Anti-Gapclose", true)
var_0_12.misc:menu("interrupt", "Interrupt Settings")
var_0_12.misc.interrupt:boolean("inte", "Use E", true)
var_0_12.misc.interrupt:menu("interruptmenu", "Interrupt Settings")

for iter_0_8 = 1, #var_0_5.GetEnemyHeroes() do
	local var_0_17 = var_0_5.GetEnemyHeroes()[iter_0_8]
	local var_0_18 = string.lower(var_0_17.charName)

	if var_0_17 and var_0_5.GetInterruptableSpells()[var_0_18] then
		for iter_0_9 = 1, #var_0_5.GetInterruptableSpells()[var_0_18] do
			local var_0_19 = var_0_5.GetInterruptableSpells()[var_0_18][iter_0_9]

			var_0_12.misc.interrupt.interruptmenu:boolean(string.format(tostring(var_0_17.charName) .. tostring(var_0_19.menuslot)), "Interrupt " .. tostring(var_0_17.charName) .. " " .. tostring(var_0_19.menuslot), true)
		end
	end
end

var_0_3.load_to_menu(var_0_12)

local var_0_20 = 0

function is_turret_near(arg_1_0)
	for iter_1_0 = 0, objManager.turrets.size[TEAM_ENEMY] - 1 do
		local var_1_0 = objManager.turrets[TEAM_ENEMY][iter_1_0]

		if var_1_0 and not var_1_0.isDead and var_1_0.pos:dist(arg_1_0) < 910 then
			return true
		end
	end

	return false
end

local function var_0_21(arg_2_0, arg_2_1, arg_2_2)
	if arg_2_2 <= var_0_7.range then
		arg_2_0.obj = arg_2_1

		return true
	end
end

local function var_0_22()
	return var_0_3.get_result(var_0_21).obj
end

local function var_0_23(arg_4_0, arg_4_1, arg_4_2)
	if arg_4_2 <= var_0_9.range then
		arg_4_0.obj = arg_4_1

		return true
	end
end

local function var_0_24()
	return var_0_3.get_result(var_0_23).obj
end

local function var_0_25(arg_6_0, arg_6_1, arg_6_2)
	if arg_6_2 <= var_0_10.range then
		arg_6_0.obj = arg_6_1

		return true
	end
end

local function var_0_26()
	return var_0_3.get_result(var_0_25).obj
end

local function var_0_27(arg_8_0, arg_8_1)
	local var_8_0 = {}

	for iter_8_0 = 0, objManager.enemies_n - 1 do
		local var_8_1 = objManager.enemies[iter_8_0]

		if arg_8_1 > arg_8_0:dist(var_8_1.pos) and var_0_5.IsValidTarget(var_8_1) then
			var_8_0[#var_8_0 + 1] = var_8_1
		end
	end

	return var_8_0
end

local function var_0_28(arg_9_0, arg_9_1)
	local var_9_0 = {}

	for iter_9_0 = 0, objManager.minions.size[TEAM_ENEMY] - 1 do
		local var_9_1 = objManager.minions[TEAM_ENEMY][iter_9_0]

		if arg_9_1 > arg_9_0:dist(var_9_1.pos) and var_0_5.IsValidTarget(var_9_1) then
			var_9_0[#var_9_0 + 1] = var_9_1
		end
	end

	return var_9_0
end

local var_0_29 = {
	150,
	275,
	400
}

function RDamage(arg_10_0)
	local var_10_0 = 0
	local var_10_1 = 0

	if player:spellSlot(3).level > 0 then
		var_10_0 = var_0_5.CalculatePhysicalDamage(arg_10_0, var_0_29[player:spellSlot(3).level] + var_0_5.GetBonusAD() * 1.2, player)
	end

	return var_10_0
end

local var_0_30 = {
	65,
	110,
	155,
	200,
	245
}

function QDamage(arg_11_0)
	local var_11_0 = 0

	if player:spellSlot(0).level > 0 then
		var_11_0 = var_0_5.CalculatePhysicalDamage(arg_11_0, var_0_30[player:spellSlot(0).level] + var_0_5.GetBonusAD() * 0.75, player)
	end

	return var_11_0
end

local var_0_31 = {
	80,
	125,
	170,
	215,
	260
}

function EDamage(arg_12_0)
	local var_12_0 = 0

	if player:spellSlot(2).level > 0 then
		var_12_0 = var_0_5.CalculatePhysicalDamage(arg_12_0, var_0_31[player:spellSlot(2).level] + var_0_5.GetTotalAP() * 1, player)
	end

	return var_12_0
end

local var_0_32 = {}

local function var_0_33(arg_13_0)
	if var_0_12.misc.interrupt.inte:get() and arg_13_0 and arg_13_0.owner and arg_13_0.owner.team == TEAM_ENEMY and player.pos:dist(arg_13_0.owner.pos) < var_0_9.range then
		local var_13_0 = string.lower(arg_13_0.owner.charName)

		if var_0_5.GetInterruptableSpells()[var_13_0] then
			for iter_13_0 = 1, #var_0_5.GetInterruptableSpells()[var_13_0] do
				local var_13_1 = var_0_5.GetInterruptableSpells()[var_13_0][iter_13_0]

				if var_0_12.misc.interrupt.interruptmenu[arg_13_0.owner.charName .. var_13_1.menuslot]:get() and var_13_1.spellname == arg_13_0.name:lower() then
					var_0_32.start = os.clock()
					var_0_32.channel = var_13_1.channelduration
					var_0_32.owner = arg_13_0.owner
				end
			end
		end
	end
end

local function var_0_34()
	if var_0_32.owner then
		if os.clock() - var_0_32.channel >= var_0_32.start then
			var_0_32.owner = false

			return
		end

		if player:spellSlot(2).state == 0 and player.pos:dist(var_0_32.owner) < var_0_9.range then
			player:castSpell("obj", 2, var_0_32.owner)

			var_0_32.owner = false
		end
	end
end

local function var_0_35(arg_15_0)
	local var_15_0 = arg_15_0

	var_0_33(var_15_0)

	if var_0_12.SpellsMenu.enable:get() and var_15_0 and var_15_0.owner.type == TYPE_HERO and var_15_0.owner.team == TEAM_ENEMY and var_15_0.target == player and not var_15_0.name:find("BasicAttack") and not var_15_0.name:find("crit") then
		local var_15_1 = string.lower(var_15_0.owner.charName)

		if var_0_5.TargetedSpells()[var_15_1] and var_0_12.SpellsMenu[var_15_0.owner.charName .. var_15_0.slot] and var_0_12.SpellsMenu[var_15_0.owner.charName .. var_15_0.slot].Dodge:get() and var_0_12.SpellsMenu[var_15_0.owner.charName .. var_15_0.slot].hp:get() >= player.health / player.maxHealth * 100 then
			player:castSpell("self", 1)
		end
	end
end

local function var_0_36()
	if var_0_12.misc.Gap.GapA:get() then
		local var_16_0 = var_0_3.get_result(function(arg_17_0, arg_17_1, arg_17_2)
			if arg_17_2 <= var_0_7.range and arg_17_1.path.isActive and arg_17_1.path.isDashing then
				arg_17_0.obj = arg_17_1

				return true
			end
		end).obj

		if var_16_0 then
			local var_16_1 = var_0_2.core.lerp(var_16_0.path, network.latency, var_16_0.path.dashSpeed)

			if var_16_1 and var_16_1:dist(player.path.serverPos2D) <= var_0_9.range then
				player:castSpell("obj", 2, var_16_0)
			end
		end
	end
end

local function var_0_37()
	local var_18_0 = var_0_22()
	local var_18_1 = var_0_24()
	local var_18_2 = var_0_26()

	if var_0_12.combo.ecombo:get() and player:spellSlot(2).state == 0 and var_0_5.IsValidTarget(var_18_1) and var_18_1 and var_18_1.pos:dist(player) <= var_0_9.range and var_0_12.blacklist[var_18_0.charName] and not var_0_12.blacklist[var_18_0.charName]:get() then
		player:castSpell("obj", 2, var_18_1)
	end

	if var_0_12.combo.qcombo:get() and var_0_5.IsValidTarget(var_18_0) and var_18_0 and var_18_0.pos:dist(player) <= var_0_7.range then
		local var_18_3 = var_0_2.linear.get_prediction(var_0_7, var_18_0)

		if var_18_3 and var_18_3.startPos:dist(var_18_3.endPos) <= var_0_7.range then
			player:castSpell("pos", 0, vec3(var_18_3.endPos.x, var_18_0.y, var_18_3.endPos.y))
		end
	end

	if var_0_12.combo.rcombo:get() and player:spellSlot(3).state == 0 and var_0_5.IsValidTarget(var_18_2) and var_18_2 and #var_0_27(var_18_2.pos, 900) < var_0_12.combo.dontr:get() and var_18_2.pos:dist(player.pos) >= var_0_12.combo.hpr:get() and var_18_2.pos:dist(player) <= var_0_10.range then
		player:castSpell("obj", 3, var_18_2)
	end
end

local function var_0_38()
	if var_0_12.farming.jungleclear.useq:get() and player:spellSlot(0).state == 0 then
		for iter_19_0 = 0, objManager.minions.size[TEAM_NEUTRAL] - 1 do
			local var_19_0 = objManager.minions[TEAM_NEUTRAL][iter_19_0]

			if var_19_0 and var_19_0.isVisible and var_19_0.moveSpeed > 0 and var_19_0.isTargetable and not var_19_0.isDead and var_19_0.pos:dist(player.pos) < var_0_7.range then
				local var_19_1 = var_0_2.linear.get_prediction(var_0_7, var_19_0)

				if var_19_1 and var_19_1.startPos:dist(var_19_1.endPos) < var_0_7.range then
					player:castSpell("pos", 0, vec3(var_19_1.endPos.x, var_19_0.y, var_19_1.endPos.y))
				end
			end
		end
	end

	if var_0_12.farming.jungleclear.usee:get() and player:spellSlot(2).state == 0 then
		for iter_19_1 = 0, objManager.minions.size[TEAM_NEUTRAL] - 1 do
			local var_19_2 = objManager.minions[TEAM_NEUTRAL][iter_19_1]

			if var_19_2 and var_19_2.isVisible and var_19_2.moveSpeed > 0 and var_19_2.isTargetable and not var_19_2.isDead and var_19_2.pos:dist(player.pos) < var_0_9.range then
				player:castSpell("obj", 2, var_19_2)
			end
		end
	end
end

function VectorPointProjectionOnLineSegment(arg_20_0, arg_20_1, arg_20_2)
	local var_20_0 = arg_20_2.x
	local var_20_1 = arg_20_2.z or arg_20_2.y
	local var_20_2 = arg_20_0.x
	local var_20_3 = arg_20_0.z or arg_20_0.y
	local var_20_4 = arg_20_1.x
	local var_20_5 = arg_20_1.z or arg_20_1.y
	local var_20_6 = ((var_20_0 - var_20_2) * (var_20_4 - var_20_2) + (var_20_1 - var_20_3) * (var_20_5 - var_20_3)) / ((var_20_4 - var_20_2)^2 + (var_20_5 - var_20_3)^2)
	local var_20_7 = {
		x = var_20_2 + var_20_6 * (var_20_4 - var_20_2),
		y = var_20_3 + var_20_6 * (var_20_5 - var_20_3)
	}
	local var_20_8 = var_20_6 < 0 and 0 or var_20_6 > 1 and 1 or var_20_6
	local var_20_9 = var_20_8 == var_20_6

	return var_20_9 and var_20_7 or {
		x = var_20_2 + var_20_8 * (var_20_4 - var_20_2),
		y = var_20_3 + var_20_8 * (var_20_5 - var_20_3)
	}, var_20_7, var_20_9
end

function GetNMinionsHitE(arg_21_0)
	local var_21_0 = 0
	local var_21_1
	local var_21_2 = vec3(arg_21_0.x, 0, arg_21_0.z)
	local var_21_3 = vec3(player.x, 0, player.z)

	for iter_21_0 = 0, objManager.minions.size[TEAM_ENEMY] - 1 do
		local var_21_4 = objManager.minions[TEAM_ENEMY][iter_21_0]

		if var_21_4 and var_21_4.isVisible and not var_21_4.isDead and var_21_4.moveSpeed > 0 and var_21_4.isTargetable then
			local var_21_5 = vec3(var_21_4.x, 0, var_21_4.z)
			local var_21_6 = VectorPointProjectionOnLineSegment(player.pos - var_0_7.range * (player.pos - arg_21_0.pos):norm(), var_21_3, var_21_5)

			if vec2(var_21_5.x, var_21_5.z):dist(vec2(var_21_6.x, var_21_6.y)) < var_0_7.width then
				var_21_0 = var_21_0 + 1
				var_21_1 = var_21_4
			end
		end
	end

	return var_21_0, var_21_1
end

local function var_0_39()
	local var_22_0 = var_0_22()

	if var_0_12.harass.qcombo:get() and var_0_5.IsValidTarget(var_22_0) and var_22_0 and var_22_0.pos:dist(player) <= var_0_7.range then
		local var_22_1 = var_0_2.linear.get_prediction(var_0_7, var_22_0)

		if var_22_1 and var_22_1.startPos:dist(var_22_1.endPos) <= var_0_7.range then
			player:castSpell("pos", 0, vec3(var_22_1.endPos.x, var_22_0.y, var_22_1.endPos.y))
		end
	end

	if var_0_12.harass.ecombo:get() and player:spellSlot(2).state == 0 and var_0_5.IsValidTarget(var_22_0) and var_22_0 and var_22_0.pos:dist(player) <= var_0_9.range and var_0_12.blacklist[var_22_0.charName] and not var_0_12.blacklist[var_22_0.charName]:get() then
		player:castSpell("obj", 2, var_22_0)
	end
end

local function var_0_40()
	if var_0_12.farming.laneclear.farmq:get() and player:spellSlot(0).state == 0 then
		for iter_23_0 = 0, objManager.minions.size[TEAM_ENEMY] - 1 do
			local var_23_0 = objManager.minions[TEAM_ENEMY][iter_23_0]

			if var_23_0 and var_23_0.isVisible and var_23_0.moveSpeed > 0 and var_23_0.isTargetable and not var_23_0.isDead and var_23_0.pos:dist(player.pos) <= var_0_7.range then
				local var_23_1, var_23_2 = GetNMinionsHitE(var_23_0)

				if var_23_1 >= var_0_12.farming.laneclear.hitsq:get() then
					local var_23_3 = var_0_2.linear.get_prediction(var_0_7, var_23_0)

					if var_23_3 and var_23_3.startPos:dist(var_23_3.endPos) < var_0_7.range then
						player:castSpell("pos", 0, vec3(var_23_3.endPos.x, var_23_0.y, var_23_3.endPos.y))
					end
				end
			end
		end
	end
end

local function var_0_41()
	if player.isDead then
		return
	end

	if player.isOnScreen then
		if var_0_12.draws.drawq:get() then
			graphics.draw_circle(player.pos, var_0_7.range, 2, var_0_12.draws.colorq:get(), 80)
		end

		if var_0_12.draws.drawr:get() then
			minimap.draw_circle(player.pos, var_0_10.range, 2, var_0_12.draws.colorr:get(), 30)
		end

		if var_0_12.draws.drawe:get() then
			graphics.draw_circle(player.pos, var_0_9.range, 2, var_0_12.draws.colore:get(), 80)
		end

		local var_24_0 = graphics.world_to_screen(vec3(player.x, player.y, player.z))

		graphics.draw_text_2D("Farm: ", 15, var_24_0.x - 50, var_24_0.y + 20, graphics.argb(255, 255, 255, 255))

		if var_0_12.farming.toggle:get() then
			graphics.draw_text_2D("ON", 15, var_24_0.x - 10, var_24_0.y + 20, graphics.argb(255, 128, 255, 0))
		else
			graphics.draw_text_2D("OFF", 15, var_24_0.x - 10, var_24_0.y + 20, graphics.argb(255, 218, 34, 34))
		end
	end

	if var_0_12.draws.drawdamage:get() then
		for iter_24_0 = 0, objManager.enemies_n - 1 do
			local var_24_1 = objManager.enemies[iter_24_0]

			if var_24_1 and var_24_1.isVisible and var_24_1.team == TEAM_ENEMY and var_24_1.isOnScreen then
				local var_24_2 = var_24_1.barPos
				local var_24_3 = var_24_2.x + 164
				local var_24_4 = var_24_2.y + 122.5
				local var_24_5 = player:spellSlot(0).state == 0 and QDamage(var_24_1) or 0
				local var_24_6 = player:spellSlot(2).state == 0 and EDamage(var_24_1) or 0
				local var_24_7 = player:spellSlot(3).state == 0 and RDamage(var_24_1) or 0
				local var_24_8 = var_24_1.health - (var_24_5 + var_24_7 + var_24_6)
				local var_24_9 = var_24_3 + var_24_1.health / var_24_1.maxHealth * 102
				local var_24_10 = var_24_3 + (var_24_8 > 0 and var_24_8 or 0) / var_24_1.maxHealth * 102

				if var_24_8 > 0 then
					graphics.draw_line_2D(var_24_9, var_24_4, var_24_10, var_24_4, 10, graphics.argb(var_0_12.draws.transparency:get(), 255, 192, 200))
				else
					graphics.draw_line_2D(var_24_9, var_24_4, var_24_10, var_24_4, 10, graphics.argb(var_0_12.draws.transparency:get(), 0, 255, 0))
				end
			end
		end
	end
end

local function var_0_42()
	if player.isDead then
		return
	end

	var_0_34()

	if var_0_12.SpellsMenu.enable:get() and var_0_1 then
		for iter_25_0 = 1, #var_0_1.core.active_spells do
			local var_25_0 = var_0_1.core.active_spells[iter_25_0]

			if var_25_0.data.spell_type == "Target" and var_25_0.target == player and var_25_0.owner.type == TYPE_HERO then
				local var_25_1 = string.lower(var_25_0.owner.charName)

				if var_0_5.TargetedSpells()[var_25_1] and var_0_12.SpellsMenu[var_25_0.owner.charName .. var_25_0.data.slot] and var_0_12.SpellsMenu[var_25_0.owner.charName .. var_25_0.data.slot].Dodge:get() and var_0_12.SpellsMenu[var_25_0.owner.charName .. var_25_0.data.slot].hp:get() >= player.health / player.maxHealth * 100 then
					player:castSpell("self", 1)
				end
			elseif var_25_0.polygon and var_25_0.polygon:Contains(player.path.serverPos) ~= 0 and (not var_25_0.data.collision or #var_25_0.data.collision == 0) then
				for iter_25_1, iter_25_2 in pairs(var_0_6) do
					if iter_25_2.charName == var_25_0.owner.charName and var_25_0.data.slot == iter_25_2.slot and var_0_12.SpellsMenu[iter_25_2.charName] and var_0_12.SpellsMenu[iter_25_2.charName][iter_25_2.slot].Dodge:get() and var_0_12.SpellsMenu[iter_25_2.charName][iter_25_2.slot].hp:get() >= player.health / player.maxHealth * 100 then
						if player.pos:dist(var_25_0.owner.pos) <= 300 then
							player:castSpell("self", 1)
						end

						if var_25_0.missile and player.pos:dist(var_25_0.missile.pos) / var_25_0.data.speed < network.latency + 0.25 then
							player:castSpell("self", 1)
						end

						if (var_25_0.data.speed == math.huge or var_25_0.data.spell_type == "Circular") and var_25_0.owner then
							player:castSpell("self", 1)
						end
					end
				end
			end
		end
	end

	if var_0_12.misc.Gap.GapA:get() then
		var_0_36()
	end

	if var_0_4.menu.combat.key:get() then
		var_0_37()
	end

	if var_0_4.menu.hybrid.key:get() then
		var_0_39()
	end

	if var_0_4.menu.lane_clear.key:get() and var_0_12.farming.toggle:get() then
		var_0_40()
		var_0_38()
	end
end

cb.add(cb.draw, var_0_41)
cb.add(cb.spell, var_0_35)
var_0_4.combat.register_f_pre_tick(var_0_42)

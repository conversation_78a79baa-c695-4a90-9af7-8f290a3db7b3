-- ========================================
-- 波比W技能检测测试文件
-- 用于验证波比检测功能是否正常工作
-- ========================================

local poppy_counter = module.load(header.id, 'Riven/poppy_counter')

local test = {}

-- 测试函数：检查波比检测功能
function test.check_poppy_detection()
    print("=== 波比W技能检测测试 ===")
    
    -- 检查是否有波比在游戏中
    local has_poppy = false
    for i = 0, objManager.enemies_n - 1 do
        local enemy = objManager.enemies[i]
        if enemy and enemy.charName == "Poppy" then
            has_poppy = true
            print("发现敌方波比: " .. enemy.charName)
            
            -- 检查波比W技能状态
            local should_avoid = poppy_counter.should_avoid_target(enemy)
            print("是否应该避免攻击波比: " .. tostring(should_avoid))
            
            -- 检查波比W技能是否激活
            local w_active = poppy_counter.is_poppy_w_active()
            print("波比W技能是否激活: " .. tostring(w_active))
            
            break
        end
    end
    
    if not has_poppy then
        print("当前游戏中没有敌方波比")
    end
    
    print("=== 测试完成 ===")
end

-- 测试函数：模拟技能释放检查
function test.check_spell_blocking()
    print("=== 技能阻止测试 ===")
    
    for i = 0, objManager.enemies_n - 1 do
        local enemy = objManager.enemies[i]
        if enemy and not enemy.isDead and enemy.isVisible then
            -- 测试E技能是否应该被阻止
            local should_block_e = poppy_counter.should_block_spell(2, enemy)  -- E技能是slot 2
            print("对 " .. enemy.charName .. " 使用E技能是否被阻止: " .. tostring(should_block_e))
            
            -- 测试闪现是否应该被阻止
            local should_block_flash = poppy_counter.should_block_spell(4, enemy)  -- 闪现通常是slot 4
            print("对 " .. enemy.charName .. " 使用闪现是否被阻止: " .. tostring(should_block_flash))
        end
    end
    
    print("=== 测试完成 ===")
end

-- 定期测试函数
local last_test_time = 0
function test.periodic_test()
    local current_time = game.time
    
    -- 每5秒测试一次
    if current_time - last_test_time >= 5 then
        last_test_time = current_time
        
        -- 只在开发模式下运行测试
        if false then  -- 设置为true来启用测试
            test.check_poppy_detection()
            test.check_spell_blocking()
        end
    end
end

return test


local ove_0_5 = module.load(header.id,"xerath/r")
local ove_0_6 = module.load(header.id,"xerath/r_buff")
local ove_0_7 = module.load(header.id,"xerath/menu")
local ove_0_8 = {}
local ove_0_9 = graphics.argb(255, 255, 215, 0)

function ove_0_8.on_draw()
    -- print 1
    if ove_0_6.isActive then
        graphics.draw_circle(mousePos, ove_0_7.r_radius:get(), 3, ove_0_9, 48)
    end
end


return ove_0_8

local ove_0_5 = module.internal("orb")
local ove_0_6 = module.internal("pred")
local ove_0_7 = module.load(header.id,"xerath/menu")
local ove_0_8 = module.load(header.id,"xerath/q")
local ove_0_9 = module.load(header.id,"xerath/q_buff")
local ove_0_10 = module.load(header.id,"xerath/w")

local clear = {}

-- 完全复制Champions/Xerath.lua的get_charged_range函数
local get_charged_range = function()
    if not ove_0_9.isActive or not ove_0_9.start then
        return 735
    end
    local t = os.clock() - ove_0_9.start - network.latency*2 - 0.1
    return math.min(1450, 735 + 510.71 * t)
end

-- 完全复制Champions/Xerath.lua的point_on_line_seg函数
local function point_on_line_seg(line_start, line_end, point)
    local ratio = ((point.x - line_start.x)*(line_end.x - line_start.x) + (point.y - line_start.y)*(line_end.y - line_start.y))/((line_end.x - line_start.x)^2 + (line_end.y - line_start.y)^2)
    if ratio>=0 and ratio<=1 then
        return vec2(line_start.x + ratio*(line_end.x - line_start.x), line_start.y + ratio*(line_end.y - line_start.y))
    end
    return nil
end

-- 检查法力值是否足够
function clear.mana_check()
    local manaPercent = player.mana / player.maxMana * 100
    return manaPercent >= ove_0_7.clear_mana:get()
end

-- 完全复制Champions/Xerath.lua的GetQMinis函数
local GetQMinis = function()
    local minions = objManager.minions
    local in_attack_range = false
    local rt = false
    local valid = {}
    for i = 0, minions.size[TEAM_ENEMY] - 1 do
        local minion = minions[TEAM_ENEMY][i]
        if minion and minion.isVisible and minion.moveSpeed > 0 and minion.isTargetable and not minion.isDead and player.path.serverPos:distSqr(minion.path.serverPos) <= (1450 * 1450) and minion.maxHealth > 10    then
            local dist = player.pos2D:dist(minion.pos2D)
            if dist < 1450 then
              table.insert(valid, minion)
            end
            if dist < player.attackRange + 65 then
              in_attack_range = true
            end
        end
    end
    if #valid == 0 then
        for i = 0, minions.size[TEAM_NEUTRAL] - 1 do
            local minion = minions[TEAM_NEUTRAL][i]
            if minion and minion.isVisible and minion.moveSpeed > 0 and minion.isTargetable and not minion.isDead and player.path.serverPos:distSqr(minion.path.serverPos) <= (1450 * 1450) and minion.maxHealth > 10    then
                local dist = player.pos2D:dist(minion.pos2D)
                if dist < 1450 then
                  table.insert(valid, minion)
                end
                if dist < player.attackRange + 65 then
                  in_attack_range = true
                end
                rt = true
            end
        end
    end
    local cast_pos, max_count = nil, 0
    for i, minion_a in ipairs(valid) do
        local distance = math.min(1450, math.max(365, minion_a.pos2D:dist(player.pos2D)+50))
        local current_pos, hit_count = player.pos2D + (minion_a.pos2D-player.pos2D):norm()*distance, 1
        for k, minion_b in ipairs(valid) do
            if i~=k then
                local closest_point = point_on_line_seg(player.pos2D, current_pos, minion_b.pos2D)
                if closest_point and closest_point:dist(minion_b.pos2D) < 72+minion_b.boundingRadius then
                    hit_count=hit_count+1
                end
            end
        end
        if not cast_pos or hit_count>max_count then
            cast_pos, max_count = current_pos, hit_count
        end
    end
    return cast_pos,max_count,in_attack_range,rt
end

-- 完全复制Champions/Xerath.lua的GetWMinis函数
local GetWMinis = function()
    local minions = objManager.minions
    local in_attack_range = false
    local rt = false
    local valid = {}
    for i = 0, minions.size[TEAM_ENEMY] - 1 do
        local minion = minions[TEAM_ENEMY][i]
        if minion and minion.isVisible and minion.moveSpeed > 0 and minion.isTargetable and not minion.isDead and player.path.serverPos:distSqr(minion.path.serverPos) <= (1450 * 1450) and minion.maxHealth > 10    then
            local dist = player.pos2D:dist(minion.pos2D)
            if dist < 1450 then
              table.insert(valid, minion)
            end
            if dist < player.attackRange + 65 then
              in_attack_range = true
            end
        end
    end
    if #valid == 0 then
        for i = 0, minions.size[TEAM_NEUTRAL] - 1 do
            local minion = minions[TEAM_NEUTRAL][i]
            if minion and minion.isVisible and minion.moveSpeed > 0 and minion.isTargetable and not minion.isDead and player.path.serverPos:distSqr(minion.path.serverPos) <= (1450 * 1450) and minion.maxHealth > 10    then
                local dist = player.pos2D:dist(minion.pos2D)
                if dist < 1450 then
                  table.insert(valid, minion)
                end
                if dist < player.attackRange + 65 then
                  in_attack_range = true
                end
                rt = true
            end
        end
    end
    local cast_pos, max_count = nil, 0
    for i, minion_a in ipairs(valid) do
        local distance = math.min(1450, math.max(365, minion_a.pos2D:dist(player.pos2D)+50))
        local current_pos, hit_count = player.pos2D + (minion_a.pos2D-player.pos2D):norm()*distance, 1

        for k, minion_b in ipairs(valid) do
            if i~=k and current_pos:dist(player.pos2D) <= 1000 then
                if minion_b.pos2D:dist(current_pos) <= 175 + minion_b.boundingRadius then
                    hit_count=hit_count+1
                end
            end
        end
        if not cast_pos or hit_count>max_count then
            cast_pos, max_count = current_pos, hit_count
        end
    end
    return cast_pos,max_count,in_attack_range,rt
end

-- 完全复制Champions/Xerath.lua的Farm函数
function clear.get_action()
    -- 检查清线总开关
    if not ove_0_7.clear_toggle:get() then
        return false
    end

    if not ove_0_7.clear_key:get() then
        return false
    end

    if ove_0_7.clear_check_hero:get() then
        local enemies_nearby = 0
        for i = 0, objManager.enemies_n - 1 do
            local enemy = objManager.enemies[i]
            if enemy and enemy.isVisible and not enemy.isDead and enemy.pos:dist(player.pos) <= 1600 then
                enemies_nearby = enemies_nearby + 1
            end
        end
        if enemies_nearby > 0 then
            return false
        end
    end

    ---- ~~ 使用Q ~~  --
    if ove_0_8.is_ready() and ove_0_7.q_clear:get() and ove_0_7.q_clear_mana:get() <= (player.mana / player.maxMana * 100) and ove_0_5.core.can_action() then
        local cast_pos,max_count,in_attack_range,rt = GetQMinis()

        if not ove_0_9.isActive and cast_pos and max_count and (not in_attack_range or not ove_0_5.core.can_attack()) and (max_count >= ove_0_7.q_clear_minions:get() or rt) then
            player:castSpell("pos", 0, mousePos)
            return
        end
        if ove_0_9.isActive and cast_pos and max_count then
            if vec3(cast_pos.x, game.mousePos.y, cast_pos.y):dist(player.pos) <= get_charged_range() then
                player:castSpell("release", 0, vec3(cast_pos.x, game.mousePos.y, cast_pos.y))
                return
            end
        end
    end
    ---- ~~ 使用W ~~  --
    if ove_0_10.is_ready() and ove_0_7.w_clear:get() and ove_0_7.w_clear_mana:get() <= (player.mana / player.maxMana * 100) then
        local cast_pos,max_count,in_attack_range,rt = GetWMinis()
        if not ove_0_9.isActive and cast_pos and max_count and (not in_attack_range or not ove_0_5.core.can_attack()) and (max_count >= ove_0_7.w_clear_minions:get() or rt) then
            if vec3(cast_pos.x, game.mousePos.y, cast_pos.y):dist(player.pos) <= 1000 then
                player:castSpell("pos", 1, vec3(cast_pos.x, game.mousePos.y, cast_pos.y))
                return
            end
        end
    end
end

return clear

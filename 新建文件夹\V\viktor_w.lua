local ove_0_5 = require("viktor/menu").w
local ove_0_6 = require("viktor/r")
local ove_0_7 = require("pred/main")
local ove_0_8 = require("orb/main")
local ove_0_9 = require("TS/main")
local ove_0_10 = player:spellSlot(1)
local ove_0_11
local ove_0_12
local ove_0_13 = {
	radius = 325,
	range = 799,
	delay = 0.65,
	offset = 150,
	speed = math.huge
}
local ove_0_14 = {
	Crowstorm = function(arg_1_0)
		-- print 1
		return arg_1_0.endPos2D:dist(arg_1_0.startPos2D) > 800 and arg_1_0.startPos2D + (arg_1_0.endPos2D - arg_1_0.startPos2D):norm() * 800 or vec2(arg_1_0.endPos.x, arg_1_0.endPos.z)
	end,
	PantheonRJump = function(arg_2_0)
		-- print 2
		return vec2(arg_2_0.endPos.x, arg_2_0.endPos.z)
	end,
	gate = function(arg_3_0)
		-- print 3
		return vec2(spell.endPos.x, spell.endPos.z)
	end
}
local ove_0_15 = {
	Ahri = function(arg_4_0, arg_4_1)
		-- print 4
		return arg_4_0:spellSlot(3).cooldown < 10
	end,
	AurelionSol = function(arg_5_0, arg_5_1)
		-- print 5
		return arg_5_1 == 600
	end,
	Azir = function(arg_6_0, arg_6_1)
		-- print 6
		return arg_6_1 == 1700
	end,
	Camille = function(arg_7_0, arg_7_1)
		-- print 7
		return arg_7_1 == 1050 + arg_7_0.moveSpeed
	end,
	Ekko = function(arg_8_0, arg_8_1)
		-- print 8
		return arg_8_1 > 1100 and arg_8_1 < 1200
	end,
	Fizz = function(arg_9_0, arg_9_1)
		-- print 9
		return true
	end,
	Galio = function(arg_10_0, arg_10_1)
		-- print 10
		return arg_10_1 == 1250
	end,
	Gnar = function(arg_11_0, arg_11_1)
		-- print 11
		return arg_11_1 > 850 and arg_11_1 < 1000
	end,
	Hecarim = function(arg_12_0, arg_12_1)
		-- print 12
		return true
	end,
	Kalista = function(arg_13_0, arg_13_1)
		-- print 13
		return arg_13_1 > 700 and arg_13_1 < 900
	end,
	Leblanc = function(arg_14_0, arg_14_1)
		-- print 14
		return true
	end,
	Quinn = function(arg_15_0, arg_15_1)
		-- print 15
		return arg_15_1 == 2500
	end,
	Riven = function(arg_16_0, arg_16_1)
		-- print 16
		return arg_16_1 < 1150
	end,
	Yasuo = function(arg_17_0, arg_17_1)
		-- print 17
		return true
	end
}
local ove_0_16 = {
	RiftWalk = function(arg_18_0)
		-- print 18
		return arg_18_0.endPos2D:dist(arg_18_0.startPos2D) > 500 and arg_18_0.startPos2D + (arg_18_0.endPos2D - arg_18_0.startPos2D):norm() * 500 or vec2(arg_18_0.endPos.x, arg_18_0.endPos.z)
	end,
	EzrealArcaneShift = function(arg_19_0)
		-- print 19
		return arg_19_0.endPos2D:dist(arg_19_0.startPos2D) > 475 and arg_19_0.startPos2D + (arg_19_0.endPos2D - arg_19_0.startPos2D):norm() * 475 or vec2(arg_19_0.endPos.x, arg_19_0.endPos.z)
	end,
	KatarinaE = function(arg_20_0)
		-- print 20
		if arg_20_0.endPos2D:dist(player.pos2D) < 350 then
			return vec2(arg_20_0.endPos.x, arg_20_0.endPos.z)
		end
	end,
	AlphaStrike = function(arg_21_0)
		-- print 21
		if arg_21_0.target and arg_21_0.target.type == TYPE_HERO then
			return vec2(arg_21_0.target.pos.x, arg_21_0.target.pos.z)
		end
	end,
	EkkoEAttack = function(arg_22_0)
		-- print 22
		if arg_22_0.target and arg_22_0.target.type == TYPE_HERO then
			return vec2(arg_22_0.target.pos.x, arg_22_0.target.pos.z)
		end
	end
}
local ove_0_17 = {
	[2] = {
		{
			1,
			2
		},
		{
			1,
			3
		},
		{
			1,
			4
		},
		{
			1,
			5
		},
		{
			2,
			3
		},
		{
			2,
			4
		},
		{
			2,
			5
		},
		{
			3,
			4
		},
		{
			3,
			5
		},
		{
			4,
			5
		}
	},
	[3] = {
		{
			1,
			2,
			3
		},
		{
			1,
			2,
			4
		},
		{
			1,
			2,
			5
		},
		{
			1,
			3,
			4
		},
		{
			1,
			3,
			5
		},
		{
			1,
			4,
			5
		},
		{
			2,
			3,
			4
		},
		{
			2,
			3,
			5
		},
		{
			2,
			4,
			5
		},
		{
			3,
			4,
			5
		}
	},
	[4] = {
		{
			1,
			2,
			3,
			4
		},
		{
			1,
			2,
			3,
			5
		},
		{
			1,
			2,
			4,
			5
		},
		{
			1,
			3,
			4,
			5
		},
		{
			2,
			3,
			4,
			5
		}
	},
	[5] = {
		{
			1,
			2,
			3,
			4,
			5
		}
	}
}
local ove_0_18 = vec2.array(6)

local function ove_0_19(arg_23_0)
	-- print 23
	if not arg_23_0.isVisible then
		return false
	end

	if arg_23_0.isDead then
		return false
	end

	if arg_23_0.path.active and arg_23_0.path.isDashing then
		return false
	end

	if arg_23_0.path.serverPos2D:dist(player.path.serverPos2D) > ove_0_13.range + ove_0_13.radius - ove_0_13.offset then
		return false
	end

	return true
end

local function ove_0_20(arg_24_0)
	-- print 24
	if not ove_0_19(arg_24_0) then
		return false
	end

	if not arg_24_0.isTargetable then
		return true
	end

	if not arg_24_0.activeSpell then
		return false
	end

	if arg_24_0.activeSpell.animationTime > 2.75 then
		return true
	end

	if arg_24_0.activeSpell.animationTime > 1.375 and not arg_24_0.activeSpell.isBasicAttack then
		return true
	end

	return false
end

local function ove_0_21(arg_25_0)
	-- print 25
	if not arg_25_0.isVisible then
		return false
	end

	if arg_25_0.isDead then
		return false
	end

	if not arg_25_0.isTargetable then
		return false
	end

	if not arg_25_0.path.active then
		return false
	end

	if not arg_25_0.path.isDashing then
		return false
	end

	if ove_0_15[arg_25_0.charName] and ove_0_15[arg_25_0.charName](arg_25_0, arg_25_0.path.dashSpeed) then
		return false
	end

	if arg_25_0.path.serverPos2D:dist(arg_25_0.path.point2D[arg_25_0.path.count]) < 125 then
		return false
	end

	return true
end

local function ove_0_22(arg_26_0)
	-- print 26
	if not arg_26_0.isVisible then
		return false
	end

	if arg_26_0.isDead then
		return false
	end

	if not arg_26_0.isTargetable then
		return false
	end

	if not arg_26_0.activeSpell then
		return false
	end

	if not ove_0_16[arg_26_0.activeSpell.name] then
		return false
	end

	return true
end

local function ove_0_23(arg_27_0, arg_27_1, arg_27_2)
	-- print 27
	if arg_27_2 > ove_0_13.range + ove_0_13.radius - ove_0_13.offset then
		return false
	end

	if not arg_27_1.path.active then
		return false
	end

	if arg_27_1.path.isDashing then
		return false
	end

	local slot_27_0 = ove_0_7.core.lerp(arg_27_1.path, 325 / arg_27_1.moveSpeed, arg_27_1.moveSpeed)

	if not slot_27_0 then
		return false
	end

	if player.path.serverPos2D:dist(slot_27_0) > ove_0_13.range then
		return false
	end

	local slot_27_1 = (slot_27_0 - player.path.serverPos2D):norm()
	local slot_27_2 = vec2(slot_27_1.y, -slot_27_1.x)
	local slot_27_3 = slot_27_0 + slot_27_2 * 250
	local slot_27_4 = slot_27_0 - slot_27_2 * 250
	local slot_27_5 = arg_27_1.path.serverPos2D:dist(arg_27_1.path.point2D[arg_27_1.path.count]) > 700

	if navmesh.isWall(slot_27_3) then
		if navmesh.isWall(slot_27_4) or slot_27_5 then
			arg_27_0.pos = slot_27_0

			return true
		end
	elseif navmesh.isWall(slot_27_4) and slot_27_5 then
		arg_27_0.pos = slot_27_0

		return true
	end
end

local function ove_0_24(arg_28_0, arg_28_1, arg_28_2)
	-- print 28
	if arg_28_2 > ove_0_13.range + ove_0_13.radius - ove_0_13.offset then
		return false
	end

	if ove_0_7.trace.circular.hardlock(ove_0_13, nil, arg_28_1) or ove_0_7.trace.circular.hardlockmove(ove_0_13, nil, arg_28_1) then
		local slot_28_0 = player.path.serverPos2D
		local slot_28_1 = arg_28_1.path.serverPos2D

		arg_28_0.pos = slot_28_0:dist(slot_28_1) < ove_0_13.range and slot_28_1 or slot_28_0 + (slot_28_1 - slot_28_0):norm() * ove_0_13.range

		return true
	end
end

local function ove_0_25()
	-- print 29
	if ove_0_10.state ~= 0 then
		return
	end

	local slot_29_0 = {}
	local slot_29_1 = 0

	for iter_29_0 = 0, objManager.enemies_n - 1 do
		local slot_29_2 = objManager.enemies[iter_29_0]

		if ove_0_19(slot_29_2) then
			slot_29_1 = slot_29_1 + 1
			slot_29_0[slot_29_1] = slot_29_2
		end
	end

	local slot_29_3 = ove_0_8.menu.combat.key:get() and ove_0_5.hit_count_combat:get() or ove_0_5.hit_count_all:get()

	if slot_29_1 < slot_29_3 then
		return
	end

	local slot_29_4 = player.path.serverPos2D

	for iter_29_1 = slot_29_1, slot_29_3, -1 do
		for iter_29_2 = 1, #ove_0_17[iter_29_1] do
			for iter_29_3, iter_29_4 in ipairs(ove_0_17[iter_29_1][iter_29_2]) do
				if slot_29_0[iter_29_4] then
					local slot_29_5 = slot_29_0[iter_29_4].path.serverPos

					ove_0_18[iter_29_4 - 1].x = slot_29_5.x
					ove_0_18[iter_29_4 - 1].y = slot_29_5.z
				end
			end

			local slot_29_6, slot_29_7 = mathf.mec(ove_0_18, #ove_0_17[iter_29_1][iter_29_2])

			if slot_29_7 < ove_0_13.radius - ove_0_13.offset * 0.5 and slot_29_6:dist(slot_29_4) < ove_0_13.range and player:castSpell("pos", 1, vec3(slot_29_6.x, mousePos.y, slot_29_6.y)) then
				ove_0_8.core.set_server_pause()

				return true
			end
		end
	end
end

local function ove_0_26()
	-- print 30
	if ove_0_10.state ~= 0 then
		return
	end

	local slot_30_0 = ove_0_9.get_result(ove_0_23)

	if slot_30_0.pos and player:castSpell("pos", 1, vec3(slot_30_0.pos.x, mousePos.y, slot_30_0.pos.y)) then
		ove_0_8.core.set_server_pause()

		return true
	end

	if not ove_0_6.storm_t() then
		return
	end

	local slot_30_1 = player.path.serverPos2D:dist(ove_0_6.storm_t().path.serverPos2D)

	if slot_30_1 > ove_0_13.range + ove_0_13.radius - ove_0_13.offset then
		return
	end

	local slot_30_2 = slot_30_1 < ove_0_13.range and ove_0_6.storm_t().path.serverPos2D or player.path.serverPos2D + (ove_0_6.storm_t().path.serverPos2D - player.path.serverPos2D):norm() * ove_0_13.range

	if player:castSpell("pos", 1, vec3(slot_30_2.x, mousePos.y, slot_30_2.y)) then
		ove_0_8.core.set_server_pause()

		return true
	end
end

local function ove_0_27()
	-- print 31
	if ove_0_10.state ~= 0 then
		return
	end

	local slot_31_0 = ove_0_9.get_result(ove_0_24)

	if slot_31_0.pos and player:castSpell("pos", 1, vec3(slot_31_0.pos.x, mousePos.y, slot_31_0.pos.y)) then
		ove_0_8.core.set_server_pause()

		return true
	end
end

local function ove_0_28()
	-- print 32
	if ove_0_10.state ~= 0 then
		return
	end

	if not ove_0_5.channels:get() then
		return
	end

	local slot_32_0 = player.path.serverPos2D

	for iter_32_0 = 0, objManager.enemies_n - 1 do
		local slot_32_1 = objManager.enemies[iter_32_0]

		if ove_0_20(slot_32_1) then
			local slot_32_2 = slot_32_1.path.serverPos2D
			local slot_32_3 = slot_32_0:dist(slot_32_2) < ove_0_13.range and slot_32_2 or slot_32_0 + (slot_32_2 - slot_32_0):norm() * ove_0_13.range

			if ove_0_14[slot_32_1.activeSpell.name] then
				local slot_32_4 = ove_0_14[slot_32_1.activeSpell.name](slot_32_1.activeSpell)

				if slot_32_0:dist(end_pos) < ove_0_13.range then
					slot_32_3 = slot_32_4
				end
			end

			if player:castSpell("pos", 1, vec3(slot_32_3.x, mousePos.y, slot_32_3.y)) then
				ove_0_8.core.set_server_pause()

				return true
			end
		end
	end
end

local function ove_0_29()
	-- print 33
	if ove_0_10.state ~= 0 then
		return
	end

	if not ove_0_12 then
		return
	end

	if ove_0_12.timeout > os.clock() then
		ove_0_12 = nil

		return
	end

	if ove_0_12.delay > os.clock() then
		return
	end

	if ove_0_12.pos:dist(player.path.serverPos2D) > ove_0_13.range then
		return
	end

	if player:castSpell("pos", 1, vec3(ove_0_12.pos.x, mousePos.y, ove_0_12.pos.y)) then
		ove_0_8.core.set_server_pause()

		return true
	end
end

local function ove_0_30()
	-- print 34
	if ove_0_10.state ~= 0 then
		return
	end

	local slot_34_0 = player.path.serverPos2D

	for iter_34_0 = 0, objManager.enemies_n - 1 do
		local slot_34_1 = objManager.enemies[iter_34_0]

		if ove_0_21(slot_34_1) then
			local slot_34_2 = slot_34_1.path.point2D[slot_34_1.path.count]
			local slot_34_3 = slot_34_2:dist(slot_34_0)

			if slot_34_3 < ove_0_13.range + ove_0_13.radius - ove_0_13.offset then
				local slot_34_4 = slot_34_3 < ove_0_13.range and slot_34_2 or slot_34_0 + (slot_34_2 - slot_34_0):norm() * ove_0_13.range

				if player:castSpell("pos", 1, vec3(slot_34_4.x, mousePos.y, slot_34_4.y)) then
					ove_0_8.core.set_server_pause()

					return true
				end
			end
		elseif ove_0_22(slot_34_1) then
			local slot_34_5 = ove_0_16[slot_34_1.activeSpell.name](slot_34_1.activeSpell)

			if slot_34_5 then
				local slot_34_6 = slot_34_5:dist(slot_34_0)

				if slot_34_6 < ove_0_13.range + ove_0_13.radius - ove_0_13.offset then
					local slot_34_7 = slot_34_6 < ove_0_13.range and slot_34_5 or slot_34_0 + (slot_34_5 - slot_34_0):norm() * ove_0_13.range

					if player:castSpell("pos", 1, vec3(slot_34_7.x, mousePos.y, slot_34_7.y)) then
						ove_0_8.core.set_server_pause()

						return true
					end
				end
			end
		end
	end
end

local function ove_0_31()
	-- print 35
	if ove_0_5.draw_range:get() then
		graphics.draw_circle(player.pos, ove_0_13.range, 1, ove_0_5.draw_color:get(), 48)
	end
end

local function ove_0_32(arg_36_0)
	-- print 36
	if not ove_0_5.teleport:get() then
		return
	end

	local slot_36_0 = arg_36_0.name

	if slot_36_0 == "global_ss_teleport_target_red.troy" then
		ove_0_12 = {
			pos = vec2(arg_36_0.pos.x, arg_36_0.pos.z),
			delay = os.clock() + 2,
			timeout = os.clock() + 4.5
		}
	elseif slot_36_0 == "global_ss_teleport_turret_red.troy" then
		ove_0_12 = {
			pos = ove_0_11 and arg_36_0.pos2D + (ove_0_11 - arg_36_0.pos2D):norm() * 150 or vec2(arg_36_0.pos.x, arg_36_0.pos.z),
			delay = os.clock() + 2,
			timeout = os.clock() + 4.5
		}
	elseif slot_36_0:find("TwistedFate") and slot_36_0:find("Gatemarker_Red") then
		ove_0_12 = {
			pos = vec2(arg_36_0.pos.x, arg_36_0.pos.z),
			delay = os.clock(),
			timeout = os.clock() + 1
		}
	end
end

for iter_0_0 = 0, objManager.maxObjects - 1 do
	local ove_0_33 = objManager.get(iter_0_0)

	if ove_0_33 and ove_0_33.type == TYPE_SPAWN and ove_0_33.team == TEAM_ENEMY then
		ove_0_11 = vec2(ove_0_33.pos.x, ove_0_33.pos.z)
	end
end

return {
	invoke_mec = ove_0_25,
	invoke_combat = ove_0_26,
	invoke_hardlock = ove_0_27,
	invoke_channel = ove_0_28,
	invoke_teleport = ove_0_29,
	invoke_on_dash = ove_0_30,
	on_draw = ove_0_31,
	on_create_particle = ove_0_32
}



local ove_0_6 = module.load(header.id, "Utility/commonF")
local ove_0_7 = module.load(header.id, "Utility/menuFA")
local ove_0_8 = module.internal("orb")
local ove_0_9 = module.internal("TS")
local ove_0_10 = module.internal("pred")
local ove_0_11 = module.internal("damagelib")
local ove_0_12 = module.seek("evade")
local ove_0_13 = {
	delay = 0.25,
	range = 600,
	boundingRadiusMod = 0
}
local ove_0_14 = {
	delay = 0,
	range = 1
}
local ove_0_15 = {
	delay = 0,
	range = 900
}
local ove_0_16 = {
	delay = 0,
	range = 1
}
local ove_0_17 = {
	delay = 0.25,
	damage = 600,
	range = 500 + player.boundingRadius
}
local ove_0_18 = {
	delay = 0,
	range = 1
}
local ove_0_19 = {
	delay = 0,
	range = 450
}
local ove_0_20 = {
	delay = 0,
	range = 450
}
local ove_0_21 = {
	delay = 0,
	range = 450
}
local ove_0_22 = {
	delay = 0,
	range = 450
}
local ove_0_23 = {
	delay = 0,
	range = 450
}
local ove_0_24 = {
	range = 1000,
	delay = 0.25,
	boundingRadiusMod = 0,
	width = 200,
	speed = math.huge,
	collision = {
		minion = true,
		hero = false,
		wall = true
	}
}
local ove_0_25 = {
	delay = 0,
	range = 0
}
local ove_0_26 = {
	delay = 0,
	range = 0
}
local ove_0_27 = {
	delay = 0,
	range = 0
}

local function ove_0_28(arg_5_0)
	local slot_5_0 = 0

	return ({
		70,
		90,
		110,
		130,
		150,
		170,
		190,
		210,
		230,
		250,
		270,
		290,
		310,
		330,
		350,
		370,
		390,
		410
	})[player.levelRef]
end

local function ove_0_29()
	local slot_6_0 = 0

	return ({
		105,
		123,
		141,
		159,
		177,
		195,
		213,
		231,
		249,
		267,
		285,
		303,
		321,
		339,
		357,
		375,
		393,
		411
	})[player.levelRef]
end

local function ove_0_30()
	local slot_7_0 = 0

	return ({
		80,
		94,
		108,
		122,
		136,
		150,
		164,
		178,
		192,
		206,
		220,
		234,
		248,
		262,
		276,
		290,
		304,
		318
	})[player.levelRef]
end

local function ove_0_31(arg_8_0, arg_8_1, arg_8_2)
	if arg_8_1 and not arg_8_1.isDead and arg_8_1.isVisible and arg_8_1.isTargetable and not arg_8_1.buff[17] then
		if arg_8_2 > ove_0_13.range or arg_8_1.buff.rocketgrab or arg_8_1.buff.bansheesveil or arg_8_1.buff.itemmagekillerveil or arg_8_1.buff.nocturneshroudofdarkness or arg_8_1.buff.sivire or arg_8_1.buff.fioraw or arg_8_1.buff.blackshield then
			return
		end

		arg_8_0.object = arg_8_1

		return true
	end
end

local function ove_0_32()
	return ove_0_9.get_result(ove_0_31).object
end

local function ove_0_33(arg_10_0, arg_10_1, arg_10_2)
	if arg_10_1 and not arg_10_1.isDead and arg_10_1.isVisible and arg_10_1.isTargetable and not arg_10_1.buff[17] then
		if arg_10_2 > ove_0_24.range or arg_10_1.buff.rocketgrab or arg_10_1.buff.bansheesveil or arg_10_1.buff.itemmagekillerveil or arg_10_1.buff.nocturneshroudofdarkness or arg_10_1.buff.sivire or arg_10_1.buff.fioraw or arg_10_1.buff.blackshield then
			return
		end

		arg_10_0.object = arg_10_1

		return true
	end
end

local function ove_0_34()
	return ove_0_9.get_result(ove_0_33).object
end

local function ove_0_35()
	if ove_0_17.slot == nil then
		return
	end

	if player:spellSlot(ove_0_17.slot).name == "S5_SummonerSmitePlayerGanker" then
		ove_0_17.damage = 900
	end

	if player:spellSlot(ove_0_17.slot).name == "SummonerSmiteAvatarUtility" or player:spellSlot(ove_0_17.slot).name == "SummonerSmiteAvatarOffensive" or player:spellSlot(ove_0_17.slot).name == "SummonerSmiteAvatarDefensive" then
		ove_0_17.damage = 1200
	end
end

local function ove_0_36()
	for iter_13_0 = 4, 5 do
		local slot_13_0 = player:spellSlot(iter_13_0)

		if slot_13_0.isNotEmpty and slot_13_0.name:lower():find("dot") or slot_13_0.name:lower():find("ignite") then
			ove_0_13.slot = iter_13_0
		end

		if slot_13_0.isNotEmpty and slot_13_0.name:lower():find("barrier") then
			ove_0_14.slot = iter_13_0
		end

		if slot_13_0.isNotEmpty and slot_13_0.name:lower():find("heal") then
			ove_0_15.slot = iter_13_0
		end

		if slot_13_0.isNotEmpty and slot_13_0.name:lower():find("cleanse") or slot_13_0.name:lower():find("boost") then
			ove_0_16.slot = iter_13_0
		end

		if slot_13_0.isNotEmpty and slot_13_0.name:lower():find("summonersmite") or slot_13_0.name:lower():find("smiteplayerganker") or slot_13_0.name:lower():find("avatarutility") then
			ove_0_17.slot = iter_13_0
		end
	end
end

local function ove_0_37()
	for iter_14_0 = 6, 11 do
		local slot_14_0 = player:spellSlot(iter_14_0).name

		if slot_14_0 == "ItemMercurial" or slot_14_0 == "QuicksilverSash" then
			ove_0_18.slot = iter_14_0
		end

		if slot_14_0 == "3077Active" then
			ove_0_19.slot = iter_14_0
		end

		if slot_14_0 == "3074Active" then
			ove_0_20.slot = iter_14_0
		end

		if slot_14_0 == "3748Active" then
			ove_0_21.slot = iter_14_0
		end

		if slot_14_0 == "6698Active" then
			ove_0_22.slot = iter_14_0
		end

		if slot_14_0 == "6631Active" then
			ove_0_23.slot = iter_14_0
		end

		if slot_14_0 == "3152Active" then
			ove_0_24.slot = iter_14_0
		end

		if slot_14_0 == "Item2003" then
			ove_0_25.slot = iter_14_0
		end

		if slot_14_0 == "ItemCrystalFlask" then
			ove_0_26.slot = iter_14_0
		end

		if slot_14_0 == "Item2010" then
			ove_0_27.slot = iter_14_0
		end
	end
end

local function ove_0_38()
	local slot_15_0 = ove_0_7.activatormenu.summoners.ignite.autoignite:get()
	local slot_15_1 = ove_0_7.activatormenu.summoners.ignite.onlycombo:get()
	local slot_15_2 = ove_0_32()

	if ove_0_13.slot == nil then
		return
	end

	if slot_15_0 and player:spellSlot(ove_0_13.slot).state == 0 and slot_15_2 and ove_0_6.IsValidTarget(slot_15_2) then
		if not slot_15_1 and slot_15_2.health <= ove_0_28(slot_15_2) then
			player:castSpell("obj", ove_0_13.slot, slot_15_2)
		end

		if slot_15_1 and ove_0_8.combat.is_active() and slot_15_2.health <= ove_0_28(slot_15_2) then
			player:castSpell("obj", ove_0_13.slot, slot_15_2)
		end
	end
end

local function ove_0_39()
	local slot_16_0 = ove_0_7.activatormenu.summoners.barrier.autobarrier:get()
	local slot_16_1 = ove_0_7.activatormenu.summoners.barrier.onlydead:get()
	local slot_16_2 = ove_0_7.activatormenu.summoners.barrier.incomdamage:get()

	if not ove_0_12 then
		return
	end

	if ove_0_14.slot == nil then
		return
	end

	if ove_0_12 and slot_16_0 and player:spellSlot(ove_0_14.slot).state == 0 then
		if slot_16_1 and ove_0_12.damage.count(player) >= player.health and ove_0_12.damage.count(player) < player.health + ove_0_29() then
			player:castSpell("self", ove_0_14.slot)
			print("[Fatality] Casting Barrier to Save")
		end

		if not slot_16_1 and slot_16_2 < ove_0_12.damage.count(player) then
			player:castSpell("self", ove_0_14.slot)
			print("[Fatality] Casting Barrier to Save")
		end
	end
end

local function ove_0_40()
	local slot_17_0 = ove_0_7.activatormenu.summoners.heal.autoheal:get()
	local slot_17_1 = ove_0_7.activatormenu.summoners.heal.onlydead:get()
	local slot_17_2 = ove_0_7.activatormenu.summoners.heal.incomdamage:get()

	if not ove_0_12 then
		return
	end

	if ove_0_15.slot == nil then
		return
	end

	if ove_0_12 and slot_17_0 and player:spellSlot(ove_0_15.slot).state == 0 then
		if slot_17_1 and ove_0_12.damage.count(player) >= player.health and ove_0_12.damage.count(player) < player.health + ove_0_30() then
			player:castSpell("self", ove_0_15.slot)
			print("[Fatality] Casting Heal to Save")
		end

		if not slot_17_1 and slot_17_2 < ove_0_12.damage.count(player) then
			player:castSpell("self", ove_0_15.slot)
			print("[Fatality] Casting Heal to Save")
		end
	end

	if ove_0_12 and player:spellSlot(ove_0_15.slot).state == 0 then
		for iter_17_0 = 0, objManager.allies_n - 1 do
			local slot_17_3 = objManager.allies[iter_17_0]

			if slot_17_3 and slot_17_3.charName ~= player.charName and slot_17_3.pos:dist(player.pos) <= ove_0_15.range and ove_0_7.activatormenu.summoners.heal[slot_17_3.charName]:get() and ove_0_12.damage.count(slot_17_3) >= 5 and ove_0_12.damage.count(slot_17_3) < slot_17_3.health + ove_0_30() then
				player:castSpell("obj", ove_0_15.slot, slot_17_3)
				print("[Fatality] Casting Heal To Save Allie: " .. slot_17_3.charName)
			end
		end
	end
end

local function ove_0_41()
	local slot_18_0 = ove_0_7.activatormenu.summoners.cleanse.autocleanse:get()
	local slot_18_1 = ove_0_7.activatormenu.summoners.cleanse.dontqss:get()
	local slot_18_2 = ove_0_7.activatormenu.summoners.cleanse.cleanseignite:get()
	local slot_18_3 = ove_0_7.activatormenu.summoners.cleanse.cleanseexhaust:get()
	local slot_18_4 = ove_0_7.activatormenu.summoners.cleanse.cleanseblind:get()
	local slot_18_5 = ove_0_7.activatormenu.summoners.cleanse.cleansesilence:get()

	if ove_0_16.slot == nil then
		return
	end

	if slot_18_1 and ove_0_18.slot ~= nil and player:spellSlot(ove_0_18.slot).state == 0 then
		return
	end

	if slot_18_0 then
		if player:spellSlot(ove_0_16.slot).state == 0 and (player.buff[BUFF_SNARE] or player.buff[BUFF_STUN] or player.buff[BUFF_TAUNT] or player.buff[BUFF_FEAR] or player.buff[BUFF_CHARM] or player.buff[BUFF_FLEE] or player.buff[BUFF_ASLEEP]) then
			player:castSpell("self", ove_0_16.slot)
		end

		if slot_18_4 and player:spellSlot(ove_0_16.slot).state == 0 and player.buff[BUFF_BLIND] then
			player:castSpell("self", ove_0_16.slot)
		end

		if slot_18_5 and player:spellSlot(ove_0_16.slot).state == 0 and player.buff[BUFF_SILENCE] then
			player:castSpell("self", 0)
		end

		if slot_18_2 and player:spellSlot(ove_0_16.slot).state == 0 and player.buff.summonerdot then
			player:castSpell("self", ove_0_16.slot)
		end

		if slot_18_3 and player:spellSlot(ove_0_16.slot).state == 0 and player.buff.summonerexhaust then
			player:castSpell("self", ove_0_16.slot)
		end
	end
end

local function ove_0_42()
	local slot_19_0 = ove_0_7.activatormenu.summoners.smite.smitered:get()
	local slot_19_1 = ove_0_7.activatormenu.summoners.smite.smiteblue:get()
	local slot_19_2 = ove_0_7.activatormenu.summoners.smite.smitecrab:get()
	local slot_19_3 = ove_0_7.activatormenu.summoners.smite.smitedrake:get()
	local slot_19_4 = ove_0_7.activatormenu.summoners.smite.smitevoidling:get()
	local slot_19_5 = ove_0_7.activatormenu.summoners.smite.smiteherald:get()
	local slot_19_6 = ove_0_7.activatormenu.summoners.smite.smitebaron:get()
	local slot_19_7 = ove_0_7.activatormenu.summoners.smite.smiteatakhan:get()

	if ove_0_17.slot == nil then
		return
	end

	for iter_19_0 = 0, objManager.minions.size[TEAM_NEUTRAL] - 1 do
		local slot_19_8 = objManager.minions[TEAM_NEUTRAL][iter_19_0]

		if slot_19_8.pos:dist(player.pos) <= ove_0_17.range + slot_19_8.boundingRadius and player:spellSlot(ove_0_17.slot).state == 0 then
			if slot_19_0 and slot_19_8.name:lower():find("red") and ove_0_17.damage >= slot_19_8.health then
				player:castSpell("obj", ove_0_17.slot, slot_19_8)
				print("[Fatality] Casted Smite on: " .. slot_19_8.name)
			end

			if slot_19_1 and slot_19_8.name:lower():find("blue") and ove_0_17.damage >= slot_19_8.health then
				player:castSpell("obj", ove_0_17.slot, slot_19_8)
				print("[Fatality] Casted Smite on: " .. slot_19_8.name)
			end

			if slot_19_2 and slot_19_8.name:lower():find("crab") and ove_0_17.damage >= slot_19_8.health then
				player:castSpell("obj", ove_0_17.slot, slot_19_8)
				print("[Fatality] Casted Smite on: " .. slot_19_8.name)
			end

			if slot_19_4 and slot_19_8.name:lower():find("horde") and not slot_19_8.name:lower():find("mini") and ove_0_17.damage >= slot_19_8.health then
				player:castSpell("obj", ove_0_17.slot, slot_19_8)
				print("[Fatality] Casted Smite on: " .. slot_19_8.name)
			end

			if slot_19_3 and slot_19_8.name:lower():find("dragon") and ove_0_17.damage >= slot_19_8.health then
				player:castSpell("obj", ove_0_17.slot, slot_19_8)
				print("[Fatality] Casted Smite on: " .. slot_19_8.name)
			end

			if slot_19_6 and slot_19_8.name:lower():find("baron") and ove_0_17.damage >= slot_19_8.health then
				player:castSpell("obj", ove_0_17.slot, slot_19_8)
				print("[Fatality] Casted Smite on: " .. slot_19_8.name)
			end

			if slot_19_5 and slot_19_8.name:lower():find("herald") and ove_0_17.damage >= slot_19_8.health then
				player:castSpell("obj", ove_0_17.slot, slot_19_8)
				print("[Fatality] Casted Smite on: " .. slot_19_8.name)
			end

			if slot_19_7 and slot_19_8.name:lower():find("atakhan") and ove_0_17.damage >= slot_19_8.health then
				player:castSpell("obj", ove_0_17.slot, slot_19_8)
				print("[Fatality] Casted Smite on: " .. slot_19_8.name)
			end
		end
	end
end

local function ove_0_43()
	local slot_20_0 = ove_0_7.activatormenu.items.defensive.qss.autoqss:get()
	local slot_20_1 = ove_0_7.activatormenu.items.defensive.qss.qssblind:get()
	local slot_20_2 = ove_0_7.activatormenu.items.defensive.qss.onlycombo:get()

	if ove_0_18.slot == nil then
		return
	end

	if slot_20_2 and not ove_0_8.combat.is_active() then
		return
	end

	if slot_20_0 and player:spellSlot(ove_0_18.slot).state == 0 and (player.buff[BUFF_SNARE] or player.buff[BUFF_STUN] or player.buff[BUFF_TAUNT] or player.buff[BUFF_FEAR] or player.buff[BUFF_CHARM] or player.buff[BUFF_FLEE] or player.buff[BUFF_ASLEEP]) then
		player:castSpell("self", ove_0_18.slot)
	end

	if slot_20_1 and player:spellSlot(ove_0_18.slot).state == 0 and player.buff[BUFF_BLIND] then
		player:castSpell("self", ove_0_18.slot)
	end
end

local function ove_0_44()
	local slot_21_0 = ove_0_7.activatormenu.items.offensive.belt.rocketbelt:get()
	local slot_21_1 = ove_0_7.activatormenu.items.offensive.belt.belthp:get()
	local slot_21_2 = ove_0_34()

	if slot_21_0 and ove_0_24.slot ~= nil and player:spellSlot(ove_0_24.slot).state == 0 and slot_21_2 and ove_0_6.IsValidTarget(slot_21_2) and slot_21_1 >= ove_0_6.HealthPercent(slot_21_2) then
		local slot_21_3 = ove_0_10.linear.get_prediction(ove_0_24, slot_21_2)

		if slot_21_3 and not ove_0_10.collision.get_prediction(ove_0_24, slot_21_3, slot_21_2) then
			player:castSpell("pos", ove_0_24.slot, slot_21_2.pos)
		end
	end
end

local function ove_0_45()
	local slot_22_0 = ove_0_7.activatormenu.items.defensive.pots.healthpot:get()
	local slot_22_1 = ove_0_7.activatormenu.items.defensive.pots.refillpot:get()
	local slot_22_2 = ove_0_7.activatormenu.items.defensive.pots.cookie:get()
	local slot_22_3 = ove_0_7.activatormenu.items.defensive.pots.health:get()
	local slot_22_4 = objManager.nexus[TEAM_ALLY]

	if slot_22_4 and slot_22_4.isOnScreen then
		return
	end

	if slot_22_0 and ove_0_25.slot ~= nil and player:spellSlot(ove_0_25.slot).state == 0 and not player.buff.item2003 and slot_22_3 >= ove_0_6.HealthPercent(player) then
		player:castSpell("self", ove_0_25.slot)
	end

	if slot_22_1 and ove_0_26.slot ~= nil and player:spellSlot(ove_0_26.slot).state == 0 and not player.buff.itemcrystalflask and slot_22_3 >= ove_0_6.HealthPercent(player) then
		player:castSpell("self", ove_0_26.slot)
	end

	if slot_22_2 and ove_0_27.slot ~= nil and player:spellSlot(ove_0_27.slot).state == 0 and not player.buff.item2010 and slot_22_3 >= ove_0_6.HealthPercent(player) then
		player:castSpell("self", ove_0_27.slot)
	end
end

ove_0_8.combat.register_f_after_attack(function()
	local slot_23_0 = ove_0_7.activatormenu.items.offensive.hydra.tiamat:get()
	local slot_23_1 = ove_0_7.activatormenu.items.offensive.hydra.ravenoushydra:get()
	local slot_23_2 = ove_0_7.activatormenu.items.offensive.hydra.titanichydra:get()
	local slot_23_3 = ove_0_7.activatormenu.items.offensive.hydra.profanehydra:get()
	local slot_23_4 = ove_0_7.activatormenu.items.offensive.stride.stridebreaker:get()

	if ove_0_8.combat.is_active() then
		if slot_23_0 and ove_0_19.slot ~= nil and player:spellSlot(ove_0_19.slot).state == 0 and player.pos:countEnemies(ove_0_19.range) > 0 then
			player:castSpell("self", ove_0_19.slot)
			ove_0_8.core.reset()
		end

		if slot_23_1 and ove_0_20.slot ~= nil and player:spellSlot(ove_0_20.slot).state == 0 and player.pos:countEnemies(ove_0_20.range) > 0 then
			player:castSpell("self", ove_0_20.slot)
			ove_0_8.core.reset()
		end

		if slot_23_2 and ove_0_21.slot ~= nil and player:spellSlot(ove_0_21.slot).state == 0 and player.pos:countEnemies(ove_0_21.range) > 0 then
			player:castSpell("self", ove_0_21.slot)
		end

		if slot_23_3 and ove_0_22.slot ~= nil and player:spellSlot(ove_0_22.slot).state == 0 and player.pos:countEnemies(ove_0_22.range) > 0 then
			player:castSpell("self", ove_0_22.slot)
		end

		if slot_23_4 and ove_0_23.slot ~= nil and player:spellSlot(ove_0_23.slot).state == 0 and player.pos:countEnemies(ove_0_23.range) > 0 then
			player:castSpell("self", ove_0_23.slot)
		end
	end
end)
cb.add(cb.tick, function()
	ove_0_36()
	ove_0_37()
	ove_0_35()
	ove_0_38()
	ove_0_39()
	ove_0_40()
	ove_0_41()
	ove_0_43()
	ove_0_45()

	if ove_0_7.activatormenu.summoners.smite.smitekey:get() then
		ove_0_42()
	end

	if ove_0_8.combat.is_active() then
		ove_0_44()
	end
end)
cb.add(cb.draw, function()
	local slot_25_0 = ove_0_7.activatormenu.summoners.smite.drawsmite:get()

	if player.isOnScreen and ove_0_7.activatormenu.summoners.smite.smitekey:get() and slot_25_0 then
		graphics.draw_circle(player.pos, ove_0_17.range, 2, graphics.argb(255, 255, 100, 0), 100)
	end
end)


local ove_0_10 = module.internal("TS")
local ove_0_11 = module.internal("pred")
local ove_0_12

if hanbot.language == 1 then
	print("Chinese Menu Loaded")

	ove_0_12 = module.load(header.id, "TahmKench/cnmenu")
else
	ove_0_12 = module.load(header.id, "TahmKench/menu")
end

local ove_0_13 = module.internal("orb")
local ove_0_14 = module.load(header.id, "TahmKench/dmg")
local ove_0_15 = player:spellSlot(1)
local ove_0_16 = player:spellSlot(0)
local ove_0_17 = player:spellSlot(2)
local ove_0_18 = {
	radius = 200,
	range = 1000,
	delay = 1.5,
	boundingRadiusMod = 1,
	speed = math.huge,
	collision = {
		minion = true,
		hero = true,
		wall = true
	},
	damage = function(arg_5_0)
		return 50
	end
}
local ove_0_19
local ove_0_20 = 0
local ove_0_21 = module.load(header.id, "common0")

local function ove_0_22(arg_6_0, arg_6_1, arg_6_2)
	if arg_6_0.startPos:dist(arg_6_0.endPos) > ove_0_18.range then
		return false
	end

	if ove_0_12.slowonlyw:get() and arg_6_1.buff[BUFF_SLOW] then
		return true
	end

	if ove_0_11.trace.circular.hardlock(ove_0_18, arg_6_0, arg_6_1) then
		return true
	end

	if ove_0_11.trace.circular.hardlockmove(ove_0_18, arg_6_0, arg_6_1) then
		return true
	end

	if not ove_0_12.slowonlyw:get() then
		return true
	end
end

local function ove_0_23(arg_7_0, arg_7_1, arg_7_2)
	if arg_7_2 > ove_0_18.range then
		return false
	end

	local slot_7_0 = ove_0_11.circular.get_prediction(ove_0_18, arg_7_1)

	if ove_0_12.bind.ultimate:get() then
		if arg_7_2 > ove_0_18.range then
			return false
		end

		if not slot_7_0 then
			return false
		end

		arg_7_0.pos = slot_7_0.endPos

		player:castSpell("pos", _W, vec3(arg_7_0.pos.x, arg_7_0.pos.y, arg_7_0.pos.y))

		return true
	end

	if ove_0_21.IsobjUnderTurretenemy(arg_7_1) and not ove_0_12.bind.dive:get() then
		return
	end

	local slot_7_1 = ove_0_11.circular.get_prediction(ove_0_18, arg_7_1)

	if not slot_7_1 then
		return false
	end

	if not ove_0_22(slot_7_1, arg_7_1, arg_7_2) then
		return false
	end

	arg_7_0.pos = slot_7_1.endPos

	return true
end

local function ove_0_24(arg_8_0, arg_8_1)
	local slot_8_0 = arg_8_1 or player
	local slot_8_1 = arg_8_0.x - slot_8_0.x
	local slot_8_2 = (arg_8_0.z or arg_8_0.y) - (slot_8_0.z or slot_8_0.y)

	return slot_8_1 * slot_8_1 + slot_8_2 * slot_8_2
end

local function ove_0_25(arg_9_0, arg_9_1)
	local slot_9_0 = ove_0_24(arg_9_0, arg_9_1)

	return math.sqrt(slot_9_0)
end

local function ove_0_26(arg_10_0, arg_10_1)
	return arg_10_0 and arg_10_0.isVisible and not arg_10_0.isDead and arg_10_0.isTargetable and (not arg_10_1 or arg_10_1 >= player.pos:dist(arg_10_0.pos))
end

local function ove_0_27()
	ove_0_19 = ove_0_10.get_result(ove_0_23)

	if ove_0_19.pos then
		return ove_0_19
	end
end

local function ove_0_28()
	if ove_0_19.pos then
		local slot_12_0 = vec3(ove_0_19.pos.x, ove_0_19.pos.y, ove_0_19.pos.y)

		player:castSpell("pos", _W, slot_12_0)

		q_pause = os.clock() + network.latency + 0.25

		local slot_12_1
	end
end

local function ove_0_29()
	return
end

local function ove_0_30()
	return
end

return {
	get_action_state = ove_0_27,
	invoke_action = ove_0_28,
	get_farm_state = ove_0_29,
	invoke_farm = ove_0_30,
	autoWTurret = autoWTurret
}

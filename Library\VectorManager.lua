local vectorManager = {}

function vectorManager.Extend(pos1, pos2, range)
    local direction = (pos2 - pos1):norm()
    local extendedPos = pos1 + (direction * range)
    return extendedPos
end

function vectorManager.IsZeroPoint(pos)
    if not pos or (pos.type ~= "vec3" and pos.type ~= "vec2") then
        return
    end

    if pos.type == "vec3" then
        if pos.x and pos.x == 0 and pos.y and pos.y == 0 and pos.z and pos.z == 0 then
            return true
        end
    elseif pos.type == "vec2" then
        if pos.x and pos.x == 0 and pos.y and pos.y == 0 then
            return true
        end
    end

    return false
end

function vectorManager.Polygon_Circle(pos, radius, quality)
    if not pos or not radius then
        return nil
    end
    quality = quality or 20
    local list = {}
    local cosZhi = 2 * mathf.PI / quality
    local cosQuality = math.cos(cosZhi)
    local outRadius = radius / cosQuality
    for i = 1, radius, 1 do
        local angel = i * 2 * mathf.PI / quality
        local cos = math.cos(angel)
        local sin = math.sin(angel)
        local pointX = pos.x + (outRadius * cos)
        local pointY = pos.y + (outRadius * sin)
        local pointZ = pos.z
        local point = vec3(pointX, pointY, pointZ)
        list[#list + 1] = point
    end
    return list
end

function vectorManager.GetCirclePoint(pos, range, quality)
    if not pos or not range then
        return nil
    end
    quality = quality or 20
    local list = {}
    local width = range / 5
    for i = 1, range, width do
        local updatePoint = vectorManager.Polygon_Circle(pos, i, quality)
        if updatePoint ~= nil and updatePoint then
            for _, v in ipairs(updatePoint) do
                list[#list + 1] = v
            end
        end
    end
    return list
end

function vectorManager.CrossProduct(pos1, pos2)
    return (pos2.y * pos1.x) - (pos2.x * pos1.y)
end

function vectorManager.Rotated2D(v, angle)
    local c = math.cos(angle)
    local s = math.sin(angle)
    return vec2((v.x * c - v.y * s), (v.y * c + v.x * s))
end

function vectorManager.RadianToDegree(angle)
    local result = angle * (180 / mathf.PI)
    return result
end

function vectorManager.Close(a, b, eps)
    local ab = a - b
    local abs = math.abs(ab)
    if abs <= 0 then
        return true
    end
    return false
end

function vectorManager.Polar(v1)
    if not v1 then
        return 0
    end
    if vectorManager.Close(v1.x, 0, 0) then
        if v1.y > 0 then
            return 90
        end
        if v1.y < 0 then
            return 270
        end
        return 0
    end
    local chu = v1.y / v1.x
    local atan = math.atan(chu)
    local theta = vectorManager.RadianToDegree(atan)
    if v1.x < 0 then
        theta = theta + 180
    end
    if theta < 0 then
        theta = theta + 360
    end
    return theta
end

function vectorManager.AngleBetween2D(p1, p2)
    if not p1 or not p2 then
        return 180
    end
    local theta = vectorManager.Polar(p1) - vectorManager.Polar(p2)
    if theta < 0 then
        theta = theta + 360
    end
    if theta > 180 then
        theta = 360 - theta
    end
    return theta
end

function vectorManager.Perpendicular2D(pos)
    return vec2((-pos.y), pos.x)
end

function vectorManager.IsFacing(source, target)
    if source == nil or not source or target == nil or not target then
        return false
    end
    if not source.pos or not target.pos then
        return false
    end
    local angle = 90
    if not source.direction then
        return false
    end
    local source2D = vec2(source.direction.x, source.direction.z)
    local source2DPer = vectorManager.Perpendicular2D(source2D)
    local st = target.pos:to2D() - source.pos:to2D()
    local source2DPerAB = vectorManager.AngleBetween2D(source2DPer, st)
    if source2DPerAB < angle then
        return true
    end
    return false
end

function vectorManager.CircleCircleIntersection(center1, center2, radius1, radius2)
    local d = center1:dist(center2)
    if d > (radius1 + radius2) then
        return {}
    elseif d <= (math.abs(radius1 - radius2)) then
        return {}
    end
    local a = ((radius1 * radius1) - (radius2 * radius2) + (d * d)) / (2 * d)
    local h = math.sqrt((radius1 * radius1) - (a * a))
    local direction = (center2 - center1):norm()
    local pa = center1 + (a * direction)
    local s1 = pa + (h * direction:perp())
    local s2 = pa - (h * direction:perp())
    local tbl = {}
    table.insert(tbl, s1)
    table.insert(tbl, s2)
    return tbl
end

function vectorManager.Closest(pos, tbl)
    local result = nil
    local distance = 10000
    for i, v in ipairs(tbl) do
        if v then
            if pos:dist(v) < distance then
                distance = pos:dist(v)
                result = v
            end
        end
    end
    return result
end

--return pointSegment, pointLine, isOnSegment
function vectorManager.VectorPointProjectionOnLineSegment(segmentStart, segmentEnd, point)
    local cx = segmentEnd.x
    local cy = segmentEnd.y
    local ax = point.x
    local ay = point.y
    local bx = segmentStart.x
    local by = segmentStart.y
    local rL = ((cx - ax) * (bx - ax) + (cy - ay) * (by - ay)) / ((bx - ax) ^ 2 + (by - ay) ^ 2)
    local pointLine = vec2(ax + rL * (bx - ax), ay + rL * (by - ay))
    local rS = rL < 0 and 0 or (rL > 1 and 1 or rL)
    local isOnSegment = rS == rL
    local pointSegment = isOnSegment and pointLine or vec2(ax + rS * (bx - ax), ay + rS * (by - ay))
    return pointSegment, pointLine, isOnSegment
end

function vectorManager.GetSegmentDistance(point, segmentStart, segmentEnd, onlyIfOnSegment, squared)
    local pointSegment, pointLine, isOnSegment = vectorManager.VectorPointProjectionOnLineSegment(point, segmentStart, segmentEnd)
    if isOnSegment or onlyIfOnSegment == false then
        local distance = point:dist(pointSegment)
        if squared then
            local dis = distance * distance
            return distance * distance
        else
            return distance
        end
    end
    return math.huge
end

return vectorManager

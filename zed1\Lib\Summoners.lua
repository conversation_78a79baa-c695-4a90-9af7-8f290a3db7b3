
local ove_0_10 = {
	Ignite = {
		range = 600,
		real = false
	},
	Flash = {
		range = 400,
		real = false
	},
	Smite = {
		range = 400,
		real = false
	}
}

function ove_0_10.CastI(arg_5_0)
	-- print 5
	if not ove_0_10.IReady() or not ove_0_10.Ignite.nslot then
		return
	end

	player:castSpell("obj", ove_0_10.Ignite.nslot, arg_5_0)
end

function ove_0_10.CastS(arg_6_0)
	-- print 6
	if not ove_0_10.SReady() or not ove_0_10.Smite.nslot then
		return
	end

	player:castSpell("obj", ove_0_10.Smite.nslot, arg_6_0)
end

function ove_0_10.CastF(arg_7_0)
	-- print 7
	if not ove_0_10.FReady() or not ove_0_10.Flash.nslot then
		return
	end

	player:castSpell("pos", ove_0_10.Flash.nslot, arg_7_0)
end

function ove_0_10.GetIgniteSlot()
	-- print 8
	if player:spellSlot(4).name == "SummonerDot" then
		ove_0_10.Ignite.slot = player:spellSlot(4)
		ove_0_10.Ignite.real = true
		ove_0_10.Ignite.nslot = 4
	elseif player:spellSlot(5).name == "SummonerDot" then
		ove_0_10.Ignite.slot = player:spellSlot(5)
		ove_0_10.Ignite.real = true
		ove_0_10.Ignite.nslot = 5
	end
end

function ove_0_10.GetFlashSlot()
	-- print 9
	if player:spellSlot(4).name == "SummonerFlash" then
		ove_0_10.Flash.slot = player:spellSlot(4)
		ove_0_10.Flash.real = true
		ove_0_10.Flash.nslot = 4
	elseif player:spellSlot(5).name == "SummonerFlash" then
		ove_0_10.Flash.slot = player:spellSlot(5)
		ove_0_10.Flash.real = true
		ove_0_10.Flash.nslot = 5
	end
end

function ove_0_10.GetSmiteSlot()
	-- print 10
	if player:spellSlot(4).name:find("Smite") then
		ove_0_10.Smite.slot = player:spellSlot(4)
		ove_0_10.Smite.real = true
		ove_0_10.Smite.nslot = 4
	elseif player:spellSlot(5).name:find("Smite") then
		ove_0_10.Smite.slot = player:spellSlot(5)
		ove_0_10.Smite.real = true
		ove_0_10.Smite.nslot = 5
	end
end

function ove_0_10.IReady()
	-- print 11
	if not ove_0_10.Ignite.slot then
		return
	end

	if ove_0_10.Ignite.real then
		return ove_0_10.Ignite.slot.state == 0
	end
end

function ove_0_10.FReady()
	-- print 12
	if not ove_0_10.Flash.slot then
		return
	end

	if ove_0_10.Flash.real then
		return ove_0_10.Flash.slot.state == 0
	end
end

function ove_0_10.SReady()
	-- print 13
	if not ove_0_10.Smite.slot then
		return
	end

	if ove_0_10.Smite.real then
		return ove_0_10.Smite.slot.state == 0
	end
end

function ove_0_10.SDamage()
	-- print 14
	local slot_14_0 = 370
	local slot_14_1 = player.levelRef
	local slot_14_2 = 0

	if slot_14_1 <= 4 then
		slot_14_2 = slot_14_0 + slot_14_1 * 20
	elseif slot_14_1 > 4 and slot_14_1 <= 9 then
		slot_14_2 = slot_14_0 + slot_14_1 * 30 - 40
	elseif slot_14_1 > 9 and slot_14_1 <= 14 then
		slot_14_2 = slot_14_0 + slot_14_1 * 40 - 130
	elseif slot_14_1 > 14 and slot_14_1 <= 18 then
		slot_14_2 = slot_14_0 + slot_14_1 * 50 - 270
	end

	return slot_14_2
end

return ove_0_10

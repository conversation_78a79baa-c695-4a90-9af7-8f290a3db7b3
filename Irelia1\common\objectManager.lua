
local ove_0_10 = {}
local ove_0_11 = false
local ove_0_12 = false
local ove_0_13 = false
local ove_0_14 = {
	fiora = true,
	ahri = true,
	teemo = true,
	rammus = true,
	tristana = true,
	akali = true,
	syndra = true,
	kaisa = true,
	elise = true,
	skarner = true,
	xinzhao = true,
	lulu = true,
	camille = true,
	pantheon = true,
	nunu = true,
	swain = true,
	mordeka<PERSON> = true,
	viktor = true,
	rengar = true,
	chogath = true,
	azir = true,
	fiddlesticks = true,
	missfortune = true,
	katarina = true,
	vladimir = true,
	malzahar = true,
	rumble = true,
	garen = true,
	aurelionsol = true,
	ornn = true,
	leesin = true,
	nidalee = true,
	aatrox = true,
	leblanc = true,
	maokai = true,
	monkeyking = true,
	vayne = true,
	morgana = true,
	kassadin = true,
	kayle = true,
	jax = true,
	janna = true,
	jhin = true,
	drmundo = true,
	kindred = true,
	sona = true,
	ziggs = true,
	volibear = true,
	xerath = true,
	corki = true,
	orianna = true,
	yasuo = true,
	yorick = true,
	cassiopeia = true,
	zilean = true,
	zoe = true,
	zyra = true,
	fizz = true,
	reksai = true,
	nautilus = true,
	neeko = true,
	brand = true,
	taric = true,
	lucian = true,
	tahmkench = true,
	practicetool_targetdummy = true,
	xayah = true,
	ezreal = true,
	galio = true,
	leona = true,
	draven = true,
	karma = true,
	kogmaw = true,
	ashe = true,
	jayce = true,
	graves = true,
	heimerdinger = true,
	amumu = true,
	evelynn = true,
	blitzcrank = true,
	vi = true,
	karthus = true,
	illaoi = true,
	tryndamere = true,
	warwick = true,
	masteryi = true,
	udyr = true,
	annie = true,
	diana = true,
	zed = true,
	gragas = true,
	lux = true,
	singed = true,
	kayn = true,
	trundle = true,
	malphite = true,
	twistedfate = true,
	quinn = true,
	ryze = true,
	taliyah = true,
	braum = true,
	nami = true,
	ivern = true,
	jinx = true,
	gnar = true,
	sion = true,
	darius = true,
	zac = true,
	nocturne = true,
	anivia = true,
	velkoz = true,
	shaco = true,
	kennen = true,
	irelia = true,
	sejuani = true,
	hecarim = true,
	poppy = true,
	jarvaniv = true,
	twitch = true,
	gangplank = true,
	pyke = true,
	kalista = true,
	shyvana = true,
	talon = true,
	khazix = true,
	caitlyn = true,
	veigar = true,
	sivir = true,
	soraka = true,
	nasus = true,
	varus = true,
	lissandra = true,
	shen = true,
	urgot = true,
	alistar = true,
	renekton = true,
	riven = true,
	rakan = true,
	kled = true,
	ekko = true,
	thresh = true,
	olaf = true,
	bard = true
}
local ove_0_15 = {
	annietibbers = true,
	gangplankbarrel = true,
	zyrathornplant = true,
	heimertyellow = true,
	jammerdevice = true,
	zacrebirthbloblet = true,
	yorickghoulmelee = true,
	bluetrinket = true,
	sightward = true,
	yellowtrinket = true,
	voidgate = true,
	malzaharvoidling = true,
	heimertblue = true,
	sru_plant_vision = true,
	illaoiminion = true,
	sru_plant_satchel = true,
	yorickbigghoul = true,
	zyragraspingplant = true,
	voidspawn = true,
	sru_plant_health = true
}
local ove_0_16 = {
	minikrugb = true,
	minikruga = true
}
local ove_0_17 = {
	shadow = true,
	shield = true,
	syndrasphere = true,
	jarvanivwall = true,
	thedoomball = true,
	threshlantern = true,
	yorickwinvisible = true,
	k = true,
	azirrsoldier = true,
	testcuberender10vision = true,
	azirsoldier = true,
	beacon = true
}
local ove_0_18 = {
	jarvanivstandard = true,
	caitlyntrap = true,
	zyraseed = true,
	seed = true,
	planthealthpack = true
}
local ove_0_19 = {
	sru_dragon_fire = true,
	sru_baron = true,
	sru_krugmini = true,
	sru_dragon_air = true,
	sru_krug = true,
	sru_blue = true,
	sru_gromp = true,
	sru_riftherald = true,
	sru_dragon_elder = true,
	sru_razorbeak = true,
	sru_murkwolfmini = true,
	sru_dragon_water = true,
	sru_crab = true,
	sru_krugminimini = true,
	sru_red = true,
	sru_razorbeakmini = true,
	sru_dragon_earth = true,
	sru_murkwolf = true
}

ove_0_10.AllHeroes = {}
ove_0_10.AllMinions = {}
ove_0_10.AllTurrets = {}
ove_0_10.AllBarracks = {}
ove_0_10.AllHQ = {}
ove_0_10.AllFountains = {}
ove_0_10.AllyHeroes = {}
ove_0_10.AllyMinions = {}
ove_0_10.AllyTurrets = {}
ove_0_10.AllyBarracks = {}
ove_0_10.AllyHQ = {}
ove_0_10.AllyFountains = {}
ove_0_10.EnemyHeroes = {}
ove_0_10.EnemyMinions = {}
ove_0_10.EnemyTurrets = {}
ove_0_10.EnemyBarracks = {}
ove_0_10.EnemyHQ = {}
ove_0_10.EnemyFountains = {}
ove_0_10.NeturalMinions = {}
ove_0_10.SpecialMinions = {}

function ove_0_10.Compare(arg_5_0, arg_5_1)
	if arg_5_0 and arg_5_0 ~= nil and arg_5_1 and arg_5_1 ~= nil and arg_5_0.type == arg_5_1.type and arg_5_0.ptr and arg_5_1.ptr and arg_5_0.ptr == arg_5_1.ptr then
		return true
	end
end

function ove_0_10.GangPlankInGame()
	return ove_0_12
end

function ove_0_10.GetAllHeroes()
	return ove_0_10.AllHeroes
end

function ove_0_10.GetAllMinions()
	local slot_8_0 = {}
	local slot_8_1 = objManager.minions

	if slot_8_1 and slot_8_1.size[TEAM_ALLY] > 0 then
		for iter_8_0 = 0, slot_8_1.size[TEAM_ALLY] - 1 do
			local slot_8_2 = slot_8_1[TEAM_ALLY][iter_8_0]

			if slot_8_2 and not slot_8_2.isDead and slot_8_2.pos then
				table.insert(slot_8_0, slot_8_2)
			end
		end
	end

	local slot_8_3 = objManager.minions

	if slot_8_3 and slot_8_3.size[TEAM_ENEMY] > 0 then
		for iter_8_1 = 0, slot_8_3.size[TEAM_ENEMY] - 1 do
			local slot_8_4 = slot_8_3[TEAM_ENEMY][iter_8_1]

			if slot_8_4 and not slot_8_4.isDead and slot_8_4.pos then
				table.insert(slot_8_0, slot_8_4)
			end
		end
	end

	return slot_8_0
end

function ove_0_10.GetAllTurrets()
	local slot_9_0 = {}

	if objManager.turrets then
		if objManager.turrets[TEAM_ENEMY] and objManager.turrets.size[TEAM_ENEMY] > 0 then
			for iter_9_0 = 0, objManager.turrets.size[TEAM_ENEMY] - 1 do
				local slot_9_1 = objManager.turrets[TEAM_ENEMY][iter_9_0]

				if slot_9_1 and not slot_9_1.isDead and slot_9_1.team ~= player.team then
					table.insert(slot_9_0, slot_9_1)
				end
			end
		end

		if objManager.turrets[TEAM_ALLY] and objManager.turrets.size[TEAM_ALLY] > 0 then
			for iter_9_1 = 0, objManager.turrets.size[TEAM_ALLY] - 1 do
				local slot_9_2 = objManager.turrets[TEAM_ALLY][iter_9_1]

				if slot_9_2 and not slot_9_2.isDead and slot_9_2.team == player.team then
					table.insert(slot_9_0, slot_9_2)
				end
			end
		end
	end

	return slot_9_0
end

function ove_0_10.GetAllBarracks()
	local slot_10_0 = {}

	if objManager.inhibs then
		if objManager.inhibs[TEAM_ENEMY] and objManager.inhibs.size[TEAM_ENEMY] > 0 then
			for iter_10_0 = 0, objManager.inhibs.size[TEAM_ENEMY] - 1 do
				local slot_10_1 = objManager.inhibs[TEAM_ENEMY][iter_10_0]

				if slot_10_1 and not slot_10_1.isDead and slot_10_1.team ~= player.team then
					table.insert(slot_10_0, slot_10_1)
				end
			end
		end

		if objManager.inhibs[TEAM_ALLY] and objManager.inhibs.size[TEAM_ALLY] > 0 then
			for iter_10_1 = 0, objManager.inhibs.size[TEAM_ALLY] - 1 do
				local slot_10_2 = objManager.inhibs[TEAM_ALLY][iter_10_1]

				if slot_10_2 and not slot_10_2.isDead and slot_10_2.team == player.team then
					table.insert(slot_10_0, slot_10_2)
				end
			end
		end
	end

	return slot_10_0
end

function ove_0_10.GetAllHQ()
	local slot_11_0 = {}

	if objManager.nexus then
		local slot_11_1 = objManager.nexus[TEAM_ENEMY]

		if slot_11_1 then
			table.insert(slot_11_0, slot_11_1)
		end

		local slot_11_2 = objManager.nexus[TEAM_ALLY]

		if slot_11_2 then
			table.insert(slot_11_0, slot_11_2)
		end
	end

	return slot_11_0
end

function ove_0_10.GetAllFountains()
	return ove_0_10.AllFountains
end

function ove_0_10.GetAllyHeroes()
	return ove_0_10.AllyHeroes
end

function ove_0_10.GetAllyMinions()
	local slot_14_0 = {}
	local slot_14_1 = objManager.minions

	if slot_14_1 and slot_14_1.size[TEAM_ALLY] > 0 then
		for iter_14_0 = 0, slot_14_1.size[TEAM_ALLY] - 1 do
			local slot_14_2 = slot_14_1[TEAM_ALLY][iter_14_0]

			if slot_14_2 and not slot_14_2.isDead and slot_14_2.pos then
				table.insert(slot_14_0, slot_14_2)
			end
		end
	end

	return slot_14_0
end

function ove_0_10.GetAllyTurrets()
	local slot_15_0 = {}

	if objManager.turrets and objManager.turrets[TEAM_ALLY] and objManager.turrets.size[TEAM_ALLY] > 0 then
		for iter_15_0 = 0, objManager.turrets.size[TEAM_ALLY] - 1 do
			local slot_15_1 = objManager.turrets[TEAM_ALLY][iter_15_0]

			if slot_15_1 and not slot_15_1.isDead and slot_15_1.team == player.team then
				table.insert(slot_15_0, slot_15_1)
			end
		end
	end

	return slot_15_0
end

function ove_0_10.GetAllyBarracks()
	local slot_16_0 = {}

	if objManager.inhibs and objManager.inhibs[TEAM_ALLY] and objManager.inhibs.size[TEAM_ALLY] > 0 then
		for iter_16_0 = 0, objManager.inhibs.size[TEAM_ALLY] - 1 do
			local slot_16_1 = objManager.inhibs[TEAM_ALLY][iter_16_0]

			if slot_16_1 and not slot_16_1.isDead and slot_16_1.team == player.team then
				table.insert(slot_16_0, slot_16_1)
			end
		end
	end

	return slot_16_0
end

function ove_0_10.GetAllyHQ()
	local slot_17_0 = {}
	local slot_17_1 = objManager.nexus[TEAM_ALLY]

	if slot_17_1 then
		table.insert(slot_17_0, slot_17_1)
	end

	return slot_17_0
end

function ove_0_10.GetAllyFountains()
	return ove_0_10.AllyFountains
end

function ove_0_10.GetEnemyHeroes()
	return ove_0_10.EnemyHeroes
end

function ove_0_10.GetEnemyMinions()
	local slot_20_0 = {}
	local slot_20_1 = objManager.minions

	if slot_20_1 and slot_20_1.size[TEAM_ENEMY] > 0 then
		for iter_20_0 = 0, slot_20_1.size[TEAM_ENEMY] - 1 do
			local slot_20_2 = slot_20_1[TEAM_ENEMY][iter_20_0]

			if slot_20_2 and not slot_20_2.isDead and slot_20_2.pos then
				table.insert(slot_20_0, slot_20_2)
			end
		end
	end

	return slot_20_0
end

function ove_0_10.GetEnemyTurrets()
	local slot_21_0 = {}

	if objManager.turrets and objManager.turrets[TEAM_ENEMY] and objManager.turrets.size[TEAM_ENEMY] > 0 then
		for iter_21_0 = 0, objManager.turrets.size[TEAM_ENEMY] - 1 do
			local slot_21_1 = objManager.turrets[TEAM_ENEMY][iter_21_0]

			if slot_21_1 and not slot_21_1.isDead and slot_21_1.team ~= player.team then
				table.insert(slot_21_0, slot_21_1)
			end
		end
	end

	return slot_21_0
end

function ove_0_10.GetEnemyBarracks()
	local slot_22_0 = {}

	if objManager.inhibs and objManager.inhibs[TEAM_ENEMY] and objManager.inhibs.size[TEAM_ENEMY] > 0 then
		for iter_22_0 = 0, objManager.inhibs.size[TEAM_ENEMY] - 1 do
			local slot_22_1 = objManager.inhibs[TEAM_ENEMY][iter_22_0]

			if slot_22_1 and not slot_22_1.isDead and slot_22_1.team ~= player.team then
				table.insert(slot_22_0, slot_22_1)
			end
		end
	end

	return slot_22_0
end

function ove_0_10.GetEnemyHQ()
	local slot_23_0 = {}
	local slot_23_1 = objManager.nexus[TEAM_ENEMY]

	if slot_23_1 then
		table.insert(slot_23_0, slot_23_1)
	end

	return slot_23_0
end

function ove_0_10.GetEnemyFountains()
	return ove_0_10.EnemyFountains
end

function ove_0_10.GetNeturalMinions()
	local slot_25_0 = {}
	local slot_25_1 = objManager.minions

	if slot_25_1 and slot_25_1.size[TEAM_NEUTRAL] > 0 then
		for iter_25_0 = 0, slot_25_1.size[TEAM_NEUTRAL] - 1 do
			local slot_25_2 = slot_25_1[TEAM_NEUTRAL][iter_25_0]

			if slot_25_2 and not slot_25_2.isDead and slot_25_2.pos then
				table.insert(slot_25_0, slot_25_2)
			end
		end
	end

	return slot_25_0
end

function ove_0_10.GetSpecialMinions()
	return ove_0_10.SpecialMinions
end

function ove_0_10.IsUnderAllyTurret(arg_27_0)
	if not arg_27_0 then
		return false
	end

	local slot_27_0 = vec2(0, 0)

	if arg_27_0 then
		if arg_27_0.type == "vec3" and arg_27_0.x and arg_27_0.y and arg_27_0.z then
			slot_27_0 = vec2(arg_27_0.x, arg_27_0.z)
		elseif arg_27_0.type == "vec2" and arg_27_0.x and arg_27_0.y then
			slot_27_0 = vec2(arg_27_0.x, arg_27_0.y)
		elseif arg_27_0.pos and arg_27_0.pos.type == "vec3" then
			slot_27_0 = vec2(arg_27_0.pos.x, arg_27_0.pos.z)
		else
			return false
		end
	end

	if slot_27_0.x == 0 and slot_27_0.y == 0 then
		return false
	end

	local slot_27_1 = ove_0_10.AllyTurrets

	if slot_27_1 and #slot_27_1 > 0 then
		for iter_27_0, iter_27_1 in ipairs(slot_27_1) do
			if iter_27_1 and not iter_27_1.isDead and iter_27_1.team == player.team and iter_27_1.pos and iter_27_1.pos2D and slot_27_0 and iter_27_1.pos2D:distSqr(slot_27_0) <= 864900 then
				return true
			end
		end
	end
end

function ove_0_10.IsUnderEnemyTurret(arg_28_0, arg_28_1)
	arg_28_1 = arg_28_1 or 0

	if not arg_28_0 then
		return false
	end

	local slot_28_0 = player.boundingRadius + arg_28_1
	local slot_28_1 = vec2(0, 0)

	if arg_28_0 then
		if arg_28_0.type == "vec3" and arg_28_0.x and arg_28_0.y and arg_28_0.z then
			slot_28_1 = vec2(arg_28_0.x, arg_28_0.z)
		elseif arg_28_0.type == "vec2" and arg_28_0.x and arg_28_0.y then
			slot_28_1 = vec2(arg_28_0.x, arg_28_0.y)
		elseif arg_28_0.pos and arg_28_0.pos.type == "vec3" then
			slot_28_1 = vec2(arg_28_0.pos.x, arg_28_0.pos.z)

			if arg_28_0.type == TYPE_HERO and arg_28_0.boundingRadius then
				slot_28_0 = arg_28_0.boundingRadius
			end
		else
			return false
		end
	else
		return false
	end

	if slot_28_1.x == 0 and slot_28_1.y == 0 then
		return false
	end

	if objManager.turrets and objManager.turrets[TEAM_ENEMY] and objManager.turrets.size[TEAM_ENEMY] > 0 then
		for iter_28_0 = 0, objManager.turrets.size[TEAM_ENEMY] - 1 do
			local slot_28_2 = objManager.turrets[TEAM_ENEMY][iter_28_0]

			if slot_28_2 and not slot_28_2.isDead and slot_28_2.team ~= player.team and slot_28_2.pos and slot_28_2.pos2D and slot_28_1 then
				local slot_28_3 = 930 + slot_28_0

				if slot_28_2.pos2D:distSqr(slot_28_1) <= slot_28_3^2 then
					return true
				end
			end
		end
	end
end

function ove_0_10.IsInFountainRange(arg_29_0, arg_29_1)
	if not arg_29_0 then
		return false
	end

	arg_29_1 = arg_29_1 or 0

	local slot_29_0 = vec2(0, 0)

	if arg_29_0.type == "vec3" and arg_29_0.x and arg_29_0.y and arg_29_0.z then
		slot_29_0 = vec2(arg_29_0.x, arg_29_0.z)
	elseif arg_29_0.type == "vec2" and arg_29_0.x and arg_29_0.y then
		slot_29_0 = vec2(arg_29_0.x, arg_29_0.y)
	elseif arg_29_0.pos and arg_29_0.pos.type == "vec3" then
		slot_29_0 = vec2(arg_29_0.pos.x, arg_29_0.pos.z)
	else
		return false
	end

	if slot_29_0.x == 0 and slot_29_0.y == 0 then
		return false
	end

	local slot_29_1 = 1250 + arg_29_1
	local slot_29_2 = ove_0_10.AllyFountains

	if slot_29_2 and #slot_29_2 > 0 then
		for iter_29_0, iter_29_1 in ipairs(slot_29_2) do
			if iter_29_1 and iter_29_1.team == player.team and iter_29_1.pos and iter_29_1.pos2D and slot_29_0 and iter_29_1.pos2D:distSqr(slot_29_0) <= slot_29_1^2 then
				return true
			end
		end
	end
end

function ove_0_10.IsInEnemyFountainRange(arg_30_0)
	if not arg_30_0 then
		return false
	end

	local slot_30_0 = vec2(0, 0)

	if arg_30_0.type == "vec3" and arg_30_0.x and arg_30_0.y and arg_30_0.z then
		slot_30_0 = vec2(arg_30_0.x, arg_30_0.z)
	elseif arg_30_0.type == "vec2" and arg_30_0.x and arg_30_0.y then
		slot_30_0 = vec2(arg_30_0.x, arg_30_0.y)
	elseif arg_30_0.pos and arg_30_0.pos.type == "vec3" then
		slot_30_0 = vec2(arg_30_0.pos.x, arg_30_0.pos.z)
	else
		return false
	end

	if slot_30_0.x == 0 and slot_30_0.y == 0 then
		return false
	end

	local slot_30_1 = ove_0_10.EnemyFountains

	if slot_30_1 and #slot_30_1 > 0 then
		for iter_30_0, iter_30_1 in ipairs(slot_30_1) do
			if iter_30_1 and iter_30_1.team ~= player.team and iter_30_1.pos and iter_30_1.pos2D and slot_30_0 and iter_30_1.pos2D:distSqr(slot_30_0) <= 1562500 then
				return true
			end
		end
	end
end

local function ove_0_20(arg_31_0)
	if arg_31_0 and arg_31_0 ~= nil and not arg_31_0.isDead and arg_31_0.type == TYPE_HERO and arg_31_0.health and arg_31_0.health > 0 and arg_31_0.isVisible and arg_31_0.isTargetable then
		return true
	end
end

local function ove_0_21(arg_32_0)
	if arg_32_0 and arg_32_0 ~= nil and not arg_32_0.isDead and arg_32_0.type == TYPE_MINION and arg_32_0.health and arg_32_0.health > 0 and arg_32_0.isVisible and arg_32_0.isTargetable and arg_32_0.charName then
		local slot_32_0 = string.lower(arg_32_0.charName)

		if not ove_0_18[slot_32_0] and not ove_0_17[slot_32_0] and not ove_0_15[slot_32_0] then
			return true
		end
	end
end

function ove_0_10.GetAllMinionsInRange(arg_33_0, arg_33_1)
	local slot_33_0 = vec2(0, 0)

	if arg_33_1 then
		if arg_33_1.type == "vec3" and arg_33_1.x and arg_33_1.y and arg_33_1.z then
			slot_33_0 = vec2(arg_33_1.x, arg_33_1.z)
		elseif arg_33_1.type == "vec2" and arg_33_1.x and arg_33_1.y then
			slot_33_0 = vec2(arg_33_1.x, arg_33_1.y)
		elseif arg_33_1.pos and arg_33_1.pos.type == "vec3" then
			slot_33_0 = vec2(arg_33_1.pos.x, arg_33_1.pos.z)
		end
	elseif not arg_33_1 then
		slot_33_0 = vec2(player.pos.x, player.pos.z)
	end

	arg_33_0 = arg_33_0 or math.huge

	local slot_33_1 = {}

	if slot_33_0.x == 0 and slot_33_0.y == 0 then
		return slot_33_1
	end

	local slot_33_2 = ove_0_10.GetAllyMinions()

	for iter_33_0, iter_33_1 in ipairs(slot_33_2) do
		if iter_33_1 and iter_33_1 ~= nil and ove_0_21(iter_33_1) and iter_33_1.pos and iter_33_1.pos2D and slot_33_0 and iter_33_1.pos2D:distSqr(slot_33_0) <= arg_33_0^2 then
			table.insert(slot_33_1, iter_33_1)
		end
	end

	local slot_33_3 = ove_0_10.GetEnemyMinions()

	for iter_33_2, iter_33_3 in ipairs(slot_33_3) do
		if iter_33_3 and iter_33_3 ~= nil and ove_0_21(iter_33_3) and iter_33_3.pos and iter_33_3.pos2D and slot_33_0 and iter_33_3.pos2D:distSqr(slot_33_0) <= arg_33_0^2 then
			table.insert(slot_33_1, iter_33_3)
		end
	end

	local slot_33_4 = ove_0_10.GetNeturalMinions()

	for iter_33_4, iter_33_5 in ipairs(slot_33_4) do
		if iter_33_5 and iter_33_5 ~= nil and ove_0_21(iter_33_5) and iter_33_5.pos and iter_33_5.pos2D and slot_33_0 and iter_33_5.pos2D:distSqr(slot_33_0) <= arg_33_0^2 then
			table.insert(slot_33_1, iter_33_5)
		end
	end

	table.sort(slot_33_1, function(arg_34_0, arg_34_1)
		return arg_34_0.maxHealth < arg_34_1.maxHealth
	end)

	return slot_33_1
end

function ove_0_10.GetNotAllyMinions(arg_35_0, arg_35_1)
	local slot_35_0 = vec2(0, 0)

	if arg_35_1 then
		if arg_35_1.type == "vec3" and arg_35_1.x and arg_35_1.y and arg_35_1.z then
			slot_35_0 = vec2(arg_35_1.x, arg_35_1.z)
		elseif arg_35_1.type == "vec2" and arg_35_1.x and arg_35_1.y then
			slot_35_0 = vec2(arg_35_1.x, arg_35_1.y)
		elseif arg_35_1.pos and arg_35_1.pos.type == "vec3" then
			slot_35_0 = vec2(arg_35_1.pos.x, arg_35_1.pos.z)
		end
	elseif not arg_35_1 then
		slot_35_0 = vec2(player.pos.x, player.pos.z)
	end

	arg_35_0 = arg_35_0 or math.huge

	local slot_35_1 = {}

	if slot_35_0.x == 0 and slot_35_0.y == 0 then
		return slot_35_1
	end

	local slot_35_2 = ove_0_10.GetEnemyMinions()

	for iter_35_0, iter_35_1 in ipairs(slot_35_2) do
		if iter_35_1 and iter_35_1 ~= nil and ove_0_21(iter_35_1) and iter_35_1.pos and iter_35_1.pos2D and slot_35_0 and iter_35_1.pos2D:distSqr(slot_35_0) <= arg_35_0^2 then
			table.insert(slot_35_1, iter_35_1)
		end
	end

	local slot_35_3 = ove_0_10.GetNeturalMinions()

	for iter_35_2, iter_35_3 in ipairs(slot_35_3) do
		if iter_35_3 and iter_35_3 ~= nil and ove_0_21(iter_35_3) and iter_35_3.pos and iter_35_3.pos2D and slot_35_0 and iter_35_3.pos2D:distSqr(slot_35_0) <= arg_35_0^2 then
			table.insert(slot_35_1, iter_35_3)
		end
	end

	table.sort(slot_35_1, function(arg_36_0, arg_36_1)
		return arg_36_0.maxHealth < arg_36_1.maxHealth
	end)

	return slot_35_1
end

function ove_0_10.GetNotEnemyMinions(arg_37_0, arg_37_1)
	local slot_37_0 = vec2(0, 0)

	if arg_37_1 then
		if arg_37_1.type == "vec3" and arg_37_1.x and arg_37_1.y and arg_37_1.z then
			slot_37_0 = vec2(arg_37_1.x, arg_37_1.z)
		elseif arg_37_1.type == "vec2" and arg_37_1.x and arg_37_1.y then
			slot_37_0 = vec2(arg_37_1.x, arg_37_1.y)
		elseif arg_37_1.pos and arg_37_1.pos.type == "vec3" then
			slot_37_0 = vec2(arg_37_1.pos.x, arg_37_1.pos.z)
		end
	elseif not arg_37_1 then
		slot_37_0 = vec2(player.pos.x, player.pos.z)
	end

	arg_37_0 = arg_37_0 or math.huge

	local slot_37_1 = {}

	if slot_37_0.x == 0 and slot_37_0.y == 0 then
		return slot_37_1
	end

	local slot_37_2 = ove_0_10.GetAllyMinions()

	for iter_37_0, iter_37_1 in ipairs(slot_37_2) do
		if iter_37_1 and iter_37_1 ~= nil and ove_0_21(iter_37_1) and iter_37_1.pos and iter_37_1.pos2D and slot_37_0 and iter_37_1.pos2D:distSqr(slot_37_0) <= arg_37_0^2 then
			table.insert(slot_37_1, iter_37_1)
		end
	end

	local slot_37_3 = ove_0_10.GetNeturalMinions()

	for iter_37_2, iter_37_3 in ipairs(slot_37_3) do
		if iter_37_3 and iter_37_3 ~= nil and ove_0_21(iter_37_3) and iter_37_3.pos and iter_37_3.pos2D and slot_37_0 and iter_37_3.pos2D:distSqr(slot_37_0) <= arg_37_0^2 then
			table.insert(slot_37_1, iter_37_3)
		end
	end

	table.sort(slot_37_1, function(arg_38_0, arg_38_1)
		return arg_38_0.maxHealth < arg_38_1.maxHealth
	end)

	return slot_37_1
end

function ove_0_10.GetMinions(arg_39_0, arg_39_1, arg_39_2)
	local slot_39_0 = vec2(0, 0)

	if arg_39_2 then
		if arg_39_2.type == "vec3" and arg_39_2.x and arg_39_2.y and arg_39_2.z then
			slot_39_0 = vec2(arg_39_2.x, arg_39_2.z)
		elseif arg_39_2.type == "vec2" and arg_39_2.x and arg_39_2.y then
			slot_39_0 = vec2(arg_39_2.x, arg_39_2.y)
		elseif arg_39_2.pos and arg_39_2.pos.type == "vec3" then
			slot_39_0 = vec2(arg_39_2.pos.x, arg_39_2.pos.z)
		end
	elseif not arg_39_2 then
		slot_39_0 = vec2(player.pos.x, player.pos.z)
	end

	arg_39_0 = arg_39_0 or math.huge
	arg_39_1 = arg_39_1 or TEAM_ENEMY

	local slot_39_1 = {}

	if slot_39_0.x == 0 and slot_39_0.y == 0 then
		return slot_39_1
	end

	if arg_39_1 == TEAM_ENEMY then
		local slot_39_2 = ove_0_10.GetEnemyMinions()

		for iter_39_0, iter_39_1 in ipairs(slot_39_2) do
			if iter_39_1 and iter_39_1 ~= nil and ove_0_21(iter_39_1) and iter_39_1.pos and iter_39_1.pos2D and slot_39_0 and iter_39_1.pos2D:distSqr(slot_39_0) <= arg_39_0^2 then
				table.insert(slot_39_1, iter_39_1)
			end
		end
	elseif arg_39_1 == TEAM_NEUTRAL then
		local slot_39_3 = ove_0_10.GetNeturalMinions()

		for iter_39_2, iter_39_3 in ipairs(slot_39_3) do
			if iter_39_3 and iter_39_3 ~= nil and ove_0_21(iter_39_3) and iter_39_3.pos and iter_39_3.pos2D and slot_39_0 and iter_39_3.pos2D:distSqr(slot_39_0) <= arg_39_0^2 then
				table.insert(slot_39_1, iter_39_3)
			end
		end
	elseif arg_39_1 == TEAM_ALLY then
		local slot_39_4 = ove_0_10.GetAllyMinions()

		for iter_39_4, iter_39_5 in ipairs(slot_39_4) do
			if iter_39_5 and iter_39_5 ~= nil and ove_0_21(iter_39_5) and iter_39_5.pos and iter_39_5.pos2D and slot_39_0 and iter_39_5.pos2D:distSqr(slot_39_0) <= arg_39_0^2 then
				table.insert(slot_39_1, iter_39_5)
			end
		end
	end

	table.sort(slot_39_1, function(arg_40_0, arg_40_1)
		return arg_40_0.maxHealth < arg_40_1.maxHealth
	end)

	return slot_39_1
end

function ove_0_10.GetAAMinions(arg_41_0, arg_41_1, arg_41_2)
	local slot_41_0 = vec2(0, 0)

	if arg_41_2 then
		if arg_41_2.type == "vec3" and arg_41_2.x and arg_41_2.y and arg_41_2.z then
			slot_41_0 = vec2(arg_41_2.x, arg_41_2.z)
		elseif arg_41_2.type == "vec2" and arg_41_2.x and arg_41_2.y then
			slot_41_0 = vec2(arg_41_2.x, arg_41_2.y)
		elseif arg_41_2.pos and arg_41_2.pos.type == "vec3" then
			slot_41_0 = vec2(arg_41_2.pos.x, arg_41_2.pos.z)
		end
	elseif not arg_41_2 then
		slot_41_0 = vec2(player.pos.x, player.pos.z)
	end

	arg_41_0 = arg_41_0 or player.attackRange + player.boundingRadius + 70
	arg_41_1 = arg_41_1 or TEAM_ENEMY

	local slot_41_1 = {}

	if slot_41_0.x == 0 and slot_41_0.y == 0 then
		return slot_41_1
	end

	if arg_41_1 == TEAM_ENEMY then
		local slot_41_2 = ove_0_10.GetEnemyMinions()

		for iter_41_0, iter_41_1 in ipairs(slot_41_2) do
			if iter_41_1 and iter_41_1 ~= nil and iter_41_1 and ove_0_21(iter_41_1) and iter_41_1.pos and iter_41_1.pos2D and slot_41_0 and iter_41_1.pos2D:distSqr(slot_41_0) <= arg_41_0^2 then
				table.insert(slot_41_1, iter_41_1)
			end
		end
	elseif arg_41_1 == TEAM_NEUTRAL then
		local slot_41_3 = ove_0_10.GetNeturalMinions()

		for iter_41_2, iter_41_3 in ipairs(slot_41_3) do
			if iter_41_3 and iter_41_3 ~= nil and ove_0_21(iter_41_3) and iter_41_3.pos and iter_41_3.pos2D and slot_41_0 then
				local slot_41_4 = 0

				if iter_41_3.charName == "SRU_Baron" then
					slot_41_4 = 125
				end

				if iter_41_3.pos2D:distSqr(slot_41_0) <= (arg_41_0 + slot_41_4)^2 then
					table.insert(slot_41_1, iter_41_3)
				end
			end
		end
	elseif arg_41_1 == TEAM_ALLY then
		local slot_41_5 = ove_0_10.GetAllyMinions()

		for iter_41_4, iter_41_5 in ipairs(slot_41_5) do
			if iter_41_5 and iter_41_5 ~= nil and iter_41_5 and ove_0_21(iter_41_5) and iter_41_5.pos and iter_41_5.pos2D and slot_41_0 and iter_41_5.pos2D:distSqr(slot_41_0) <= arg_41_0^2 then
				table.insert(slot_41_1, iter_41_5)
			end
		end
	end

	return slot_41_1
end

function ove_0_10.GetEnemiesInRange(arg_42_0, arg_42_1)
	local slot_42_0 = {}

	if not arg_42_0 then
		return slot_42_0
	end

	local slot_42_1 = vec2(0, 0)

	if arg_42_1 then
		if arg_42_1.type == "vec3" and arg_42_1.x and arg_42_1.y and arg_42_1.z then
			slot_42_1 = vec2(arg_42_1.x, arg_42_1.z)
		elseif arg_42_1.type == "vec2" and arg_42_1.x and arg_42_1.y then
			slot_42_1 = vec2(arg_42_1.x, arg_42_1.y)
		elseif arg_42_1.pos and arg_42_1.pos.type == "vec3" then
			slot_42_1 = vec2(arg_42_1.pos.x, arg_42_1.pos.z)
		end
	elseif not arg_42_1 then
		slot_42_1 = vec2(player.pos.x, player.pos.z)
	end

	if slot_42_1.x == 0 and slot_42_1.y == 0 then
		return slot_42_0
	end

	local slot_42_2 = ove_0_10.EnemyHeroes

	if slot_42_2 and #slot_42_2 > 0 then
		for iter_42_0, iter_42_1 in ipairs(slot_42_2) do
			if iter_42_1 and iter_42_1 ~= nil and ove_0_20(iter_42_1) and iter_42_1.pos and iter_42_1.pos2D and slot_42_1 and iter_42_1.pos2D:distSqr(slot_42_1) <= arg_42_0^2 then
				table.insert(slot_42_0, iter_42_1)
			end
		end
	end

	return slot_42_0
end

function ove_0_10.GetAlliesInRange(arg_43_0, arg_43_1, arg_43_2)
	arg_43_2 = arg_43_2 or true

	local slot_43_0 = {}

	if not arg_43_0 then
		return slot_43_0
	end

	local slot_43_1 = vec2(0, 0)

	if arg_43_1 then
		if arg_43_1.type == "vec3" and arg_43_1.x and arg_43_1.y and arg_43_1.z then
			slot_43_1 = vec2(arg_43_1.x, arg_43_1.z)
		elseif arg_43_1.type == "vec2" and arg_43_1.x and arg_43_1.y then
			slot_43_1 = vec2(arg_43_1.x, arg_43_1.y)
		elseif arg_43_1.pos and arg_43_1.pos.type == "vec3" then
			slot_43_1 = vec2(arg_43_1.pos.x, arg_43_1.pos.z)
		end
	elseif not arg_43_1 then
		slot_43_1 = vec2(player.pos.x, player.pos.z)
	end

	if slot_43_1.x == 0 and slot_43_1.y == 0 then
		return slot_43_0
	end

	local slot_43_2 = ove_0_10.AllyHeroes

	if slot_43_2 and #slot_43_2 > 0 then
		for iter_43_0, iter_43_1 in ipairs(slot_43_2) do
			if iter_43_1 and iter_43_1 ~= nil and ove_0_20(iter_43_1) and iter_43_1.ptr and (arg_43_2 or iter_43_1.ptr ~= player.ptr) and iter_43_1.pos and iter_43_1.pos2D and slot_43_1 and iter_43_1.pos2D:distSqr(slot_43_1) <= arg_43_0^2 then
				table.insert(slot_43_0, iter_43_1)
			end
		end
	end

	return slot_43_0
end

function ove_0_10.GetHeroesInRange(arg_44_0, arg_44_1, arg_44_2)
	arg_44_2 = arg_44_2 or false

	local slot_44_0 = {}

	if not arg_44_0 then
		return slot_44_0
	end

	local slot_44_1 = vec2(0, 0)

	if arg_44_1 then
		if arg_44_1.type == "vec3" and arg_44_1.x and arg_44_1.y and arg_44_1.z then
			slot_44_1 = vec2(arg_44_1.x, arg_44_1.z)
		elseif arg_44_1.type == "vec2" and arg_44_1.x and arg_44_1.y then
			slot_44_1 = vec2(arg_44_1.x, arg_44_1.y)
		elseif arg_44_1.pos and arg_44_1.pos.type == "vec3" then
			slot_44_1 = vec2(arg_44_1.pos.x, arg_44_1.pos.z)
		end
	elseif not arg_44_1 then
		slot_44_1 = vec2(player.pos.x, player.pos.z)
	end

	if slot_44_1.x == 0 and slot_44_1.y == 0 then
		return slot_44_0
	end

	local slot_44_2 = ove_0_10.EnemyHeroes

	if slot_44_2 and #slot_44_2 > 0 then
		for iter_44_0, iter_44_1 in ipairs(slot_44_2) do
			if iter_44_1 and iter_44_1 ~= nil and ove_0_20(iter_44_1) and iter_44_1.pos and iter_44_1.pos2D and slot_44_1 and iter_44_1.pos2D:distSqr(slot_44_1) <= arg_44_0^2 then
				table.insert(slot_44_0, iter_44_1)
			end
		end
	end

	local slot_44_3 = ove_0_10.AllyHeroes

	if slot_44_3 and #slot_44_3 > 0 then
		for iter_44_2, iter_44_3 in ipairs(slot_44_3) do
			if iter_44_3 and iter_44_3 ~= nil and ove_0_20(iter_44_3) and iter_44_3.ptr and (arg_44_2 or iter_44_3.ptr ~= player.ptr) and iter_44_3.pos and iter_44_3.pos2D and slot_44_1 and iter_44_3.pos2D:distSqr(slot_44_1) <= arg_44_0^2 then
				table.insert(slot_44_0, iter_44_3)
			end
		end
	end

	return slot_44_0
end

local function ove_0_22(arg_45_0, arg_45_1)
	if arg_45_0 and arg_45_0.ptr then
		for iter_45_0, iter_45_1 in pairs(arg_45_1) do
			if iter_45_1 and iter_45_1.ptr and iter_45_1.ptr == arg_45_0.ptr then
				table.remove(arg_45_1, iter_45_0)
			end
		end
	end
end

local function ove_0_23(arg_46_0)
	if arg_46_0 and arg_46_0.charName then
		local slot_46_0 = string.lower(arg_46_0.charName)

		if slot_46_0:find("orderminion") or slot_46_0:find("chaosminion") then
			return true
		end

		return false
	end

	return false
end

local function ove_0_24(arg_47_0)
	if arg_47_0 then
		if arg_47_0.type == TYPE_HERO then
			table.insert(ove_0_10.AllHeroes, arg_47_0)

			if arg_47_0.team == player.team then
				table.insert(ove_0_10.AllyHeroes, arg_47_0)
			else
				table.insert(ove_0_10.EnemyHeroes, arg_47_0)

				if arg_47_0.charName == "Gangplank" then
					ove_0_12 = true
				end

				if arg_47_0.charName == "Mordekaiser" then
					ove_0_13 = true
				end
			end

			return
		end

		if arg_47_0.type == TYPE_MINION and arg_47_0.charName and arg_47_0.ptr then
			local slot_47_0 = string.lower(arg_47_0.charName)

			if slot_47_0 and ove_0_15[slot_47_0] then
				ove_0_10.SpecialMinions[arg_47_0.ptr] = arg_47_0

				return
			end

			return
		end

		if arg_47_0.type == TYPE_SPAWN then
			table.insert(ove_0_10.AllFountains, arg_47_0)

			if arg_47_0.team == player.team then
				table.insert(ove_0_10.AllyFountains, arg_47_0)
			else
				table.insert(ove_0_10.EnemyFountains, arg_47_0)
			end
		end
	end
end

local function ove_0_25(arg_48_0)
	if arg_48_0 and arg_48_0.type == TYPE_MINION and arg_48_0.charName and arg_48_0.ptr then
		local slot_48_0 = string.lower(arg_48_0.charName)

		if slot_48_0 and not ove_0_17[slot_48_0] then
			if arg_48_0.team == TEAM_NEUTRAL then
				if ove_0_15[slot_48_0] then
					ove_0_10.SpecialMinions[arg_48_0.ptr] = arg_48_0

					return
				end
			elseif arg_48_0.team == TEAM_ENEMY then
				if ove_0_15[slot_48_0] then
					ove_0_10.SpecialMinions[arg_48_0.ptr] = arg_48_0

					return
				elseif ove_0_13 and ove_0_14[slot_48_0] then
					ove_0_10.SpecialMinions[arg_48_0.ptr] = arg_48_0

					return
				end
			end
		end

		return
	end
end

local function ove_0_26(arg_49_0)
	if arg_49_0 and ove_0_10.SpecialMinions[arg_49_0.ptr] then
		ove_0_10.SpecialMinions[arg_49_0.ptr] = nil

		return
	end
end

;(function()
	if ove_0_11 then
		return
	end

	ove_0_11 = true

	objManager.loop(function(arg_51_0)
		ove_0_24(arg_51_0)
	end)
	cb.add(cb.create_minion, ove_0_25)
	cb.add(cb.delete_minion, ove_0_26)
end)()

return ove_0_10

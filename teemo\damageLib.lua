local versionUpdated = "8.2" -- 2/4/2018

local common = module.load("Brian", "common")

local damageLib = {}

local function GetBuffStacks(unit, buffName)
    local bName = string.lower(buffName)
    if unit.buff[bName] and unit.buff[bName].endTime >= game.time then
        if unit.buff[bName].stacks2 > 0 then
            return unit.buff[bName].stacks2
        elseif unit.buff[bName].stacks > 0 then
            return unit.buff[bName].stacks
        else
            return 0
        end
    end
    return 0
end

local DamageLibTable = {
    ["aatrox"] = {
        {slot = 0, stage = 1, damagetype = 1, damage = function(source, target, level) return ({20,50,80,110,140})[level] + 1.1 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod)) end},
        {slot = 1, stage = 1, damagetype = 1, damage = function(source, target, level) return ({50,85,120,155,190})[level] + 0.75 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod) - source.baseAttackDamage) end},
        {slot = 2, stage = 1, damagetype = 1, damage = function(source, target, level) return ({80,120,160,200,240})[level] + 0.7 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod) - source.baseAttackDamage) end},
        {slot = 3, stage = 1, damagetype = 2, damage = function(source, target, level) return ({200,300,400})[level] + 1 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
    },

    ["ahri"] = {
        {slot = 0, stage = 1, damagetype = 2, damage = function(source, target, level) return ({40,65,90,115,140})[level] + 0.35 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 0, stage = 2, damagetype = 3, damage = function(source, target, level) return ({40,65,90,115,140})[level] + 0.35 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 1, stage = 1, damagetype = 2, damage = function(source, target, level) return ({40,65,90,115,140})[level] + 0.3 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 1, stage = 2, damagetype = 2, damage = function(source, target, level) return ({12,19.5,27,34.5,42})[level] + 0.09 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 2, stage = 1, damagetype = 2, damage = function(source, target, level) return ({60,95,130,165,200})[level] + 0.6 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 3, stage = 1, damagetype = 2, damage = function(source, target, level) return ({70,110,150})[level] + 0.25 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
    },

    ["akali"] = {
        {slot = 0, stage = 1, damagetype = 2, damage = function(source, target, level) return ({35,55,75,95,115})[level] + 0.4 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 0, stage = 2, damagetype = 2, damage = function(source, target, level) return ({45,70,95,120,145})[level] + 0.5 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 2, stage = 1, damagetype = 1, damage = function(source, target, level) return ({70,100,130,160,190})[level] + (0.6 * (source.flatMagicDamageMod * source.percentMagicDamageMod) + 0.8 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod) - source.baseAttackDamage)) end},
        {slot = 3, stage = 1, damagetype = 2, damage = function(source, target, level) return ({50,100,150})[level] + 0.35 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
    },

    ["alistar"] = {
        {slot = 0, stage = 1, damagetype = 2, damage = function(source, target, level) return ({60,105,150,195,240})[level] + 0.5 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 1, stage = 1, damagetype = 2, damage = function(source, target, level) return ({55,110,165,220,275})[level] + 0.7 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 2, stage = 1, damagetype = 2, damage = function(source, target, level) return ({10,12.5,15,17.5,20})[level] + 0.04 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
    },

    ["amumu"] = {
        {slot = 0, stage = 1, damagetype = 2, damage = function(source, target, level) return ({80,130,180,230,280})[level] + 0.7 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 1, stage = 1, damagetype = 2, damage = function(source, target, level) return ({5,7.5,10,12.5,15})[level] + ((({0.005,0.00625,0.0075,0.00875,0.01})[level] + (0.005 * ((source.flatMagicDamageMod * source.percentMagicDamageMod) / 100))) * target.maxHealth) end},
        {slot = 2, stage = 1, damagetype = 2, damage = function(source, target, level) return ({75,100,125,150,175})[level] + 0.5 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 3, stage = 1, damagetype = 2, damage = function(source, target, level) return ({150,250,350})[level] + 0.8 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
    },

    ["anivia"] = {
        {slot = 0, stage = 1, damagetype = 2, damage = function(source, target, level) return ({60,85,110,135,160})[level] + 0.4 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 2, stage = 1, damagetype = 2, damage = function(source, target, level) return ({50,75,100,125,150})[level] + 0.5 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 3, stage = 1, damagetype = 2, damage = function(source, target, level) return ({40,60,80})[level] + 0.125 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
    },

    ["annie"] = {
        {slot = 0, stage = 1, damagetype = 2, damage = function(source, target, level) return ({80,115,150,185,220})[level] + 0.8 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 1, stage = 1, damagetype = 2, damage = function(source, target, level) return ({70,115,160,205,250})[level] + 0.85 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 2, stage = 1, damagetype = 2, damage = function(source, target, level) return ({20,30,40,50,60})[level] + 0.2 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 3, stage = 1, damagetype = 2, damage = function(source, target, level) return ({150,275,400})[level] + 0.65 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
    },

    ["ashe"] = {
        {slot = 1, stage = 1, damagetype = 1, damage = function(source, target, level) return ({20,35,50,65,80})[level] + 1 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod)) end},
        {slot = 3, stage = 1, damagetype = 2, damage = function(source, target, level) return ({200,400,600})[level] + 1 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
    },

    ["aurelionsol"] = {
        {slot = 0, stage = 1, damagetype = 2, damage = function(source, target, level) return ({70,110,150,190,230})[level] + 0.65 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 3, stage = 1, damagetype = 2, damage = function(source, target, level) return ({150,250,350})[level] + 0.7 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
    },

    ["azir"] = {
        {slot = 0, stage = 1, damagetype = 2, damage = function(source, target, level) return ({70,95,120,145,170})[level] + 0.3 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 1, stage = 1, damagetype = 2, damage = function(source, target, level) return ({60,62,64,66,68,70,72,75,80,85,90,100,110,120,130,140,150,160})[source.levelRef] + 0.6 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 2, stage = 1, damagetype = 2, damage = function(source, target, level) return ({60,90,120,150,180})[level] + 0.4 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 3, stage = 1, damagetype = 2, damage = function(source, target, level) return ({150, 250, 450})[level] + 0.6 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
    },

    ["bard"] = {
        {slot = 0, stage = 1, damagetype = 2, damage = function(source, target, level) return ({80,125,170,215,260})[level] + 0.65 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
    },

    ["blitzcrank"] = {
        {slot = 0, stage = 1, damagetype = 2, damage = function(source, target, level) return ({80,135,190,245,300})[level] + 1 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 2, stage = 1, damagetype = 1, damage = function(source, target, level) return ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod)) end},
        {slot = 3, stage = 1, damagetype = 2, damage = function(source, target, level) return ({250,375,500})[level] + 1 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
    },

    ["brand"] = {
        {slot = 0, stage = 1, damagetype = 2, damage = function(source, target, level) return ({80,110,140,170,200})[level] + 0.55 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 1, stage = 1, damagetype = 2, damage = function(source, target, level) return ({75,120,165,210,255})[level] + 0.6 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 2, stage = 1, damagetype = 2, damage = function(source, target, level) return ({70,90,110,130,150})[level] + 0.35 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 3, stage = 1, damagetype = 2, damage = function(source, target, level) return ({100,200,300})[level] + 0.25 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
    },

    ["braum"] = {
        {slot = 0, stage = 1, damagetype = 2, damage = function(source, target, level) return ({60,105,150,195,240})[level] + 0.025 * source.maxHealth end},
        {slot = 3, stage = 1, damagetype = 2, damage = function(source, target, level) return ({150,250,350})[level] + 0.6 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
    },

    ["caitlyn"] = {
        {slot = 0, stage = 1, damagetype = 1, damage = function(source, target, level) return ({30,70,110,150,190})[level] + ({1.3, 1.4, 1.5, 1.6, 1.7})[level] * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod)) end},
        {slot = 2, stage = 1, damagetype = 2, damage = function(source, target, level) return ({70,110,150,190,230})[level] + 0.8 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 3, stage = 1, damagetype = 1, damage = function(source, target, level) return ({250,475,700})[level] + 2 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod)) end},
    },

    ["camille"] = {
        {slot = 0, stage = 1, damagetype = 1, damage = function(source, target, level) return ({0.20,0.25,0.30,0.35,0.40})[level] * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod)) end},
        {slot = 0, stage = 2, damagetype = 1, damage = function(source, target, level) return ({0.40,0.50,0.60,0.70,0.80})[level] * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod)) end},
        {slot = 1, stage = 1, damagetype = 1, damage = function(source, target, level) return ({70,100,130,160,190})[level] + 0.6 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod) - source.baseAttackDamage) end},
        {slot = 2, stage = 1, damagetype = 1, damage = function(source, target, level) return ({75,120,165,210,255})[level] + 0.75 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod) - source.baseAttackDamage) end},
        {slot = 3, stage = 1, damagetype = 2, damage = function(source, target, level) return ({5,10,15})[level] + ({0.04,0.06,0.08})[level] * target.health end},
    },

    ["cassiopeia"] = {
        {slot = 0, stage = 1, damagetype = 2, damage = function(source, target, level) return ({75,120,165,210,255})[level] + 0.7 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 1, stage = 1, damagetype = 2, damage = function(source, target, level) return ({20,35,50,65,80})[level] + 0.15 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 2, stage = 1, damagetype = 2, damage = function(source, target, level) return (48 + (4 * source.levelRef)) + 0.1 * (source.flatMagicDamageMod * source.percentMagicDamageMod) + ({10,30,50,70,90})[level] + 0.60 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 3, stage = 1, damagetype = 2, damage = function(source, target, level) return ({150,250,350})[level] + 0.5 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
    },

    ["chogath"] = {
        {slot = 0, stage = 1, damagetype = 2, damage = function(source, target, level) return ({80,135,190,245,305})[level] + (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 1, stage = 1, damagetype = 2, damage = function(source, target, level) return ({75,125,175,225,275})[level] + 0.7 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 2, stage = 1, damagetype = 2, damage = function(source, target, level) return ({20,30,40,50,60})[level] + 0.3 * (source.flatMagicDamageMod * source.percentMagicDamageMod) + 0.03 * target.maxHealth end},
        {slot = 3, stage = 1, damagetype = 3, damage = function(source, target, level) return ({300,475,650})[level] + 0.5 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
    },

    ["corki"] = {
        {slot = 0, stage = 1, damagetype = 2, damage = function(source, target, level) return ({75 ,115, 155, 195, 235})[level] + 0.5 * (source.flatMagicDamageMod * source.percentMagicDamageMod) + 0.5 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod) - source.baseAttackDamage) end},
        {slot = 1, stage = 1, damagetype = 2, damage = function(source, target, level) return ({60,90,120,150,180})[level] + 0.4 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 1, stage = 2, damagetype = 2, damage = function(source, target, level) return ({30,30,30,30,30,30,30,35,40,45,50,55,60,65,70,80,90,100})[source.levelRef] + (1.5 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod) - source.baseAttackDamage)) + 0.2 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 2, stage = 1, damagetype = 1, damage = function(source, target, level) return ({20,35,50,65,80})[level] + 0.1 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod) - source.baseAttackDamage) end},
        {slot = 3, stage = 1, damagetype = 2, damage = function(source, target, level) return ({75,100,125})[level] + 0.2 * (source.flatMagicDamageMod * source.percentMagicDamageMod) + ({0.15,0.45,0.75})[level] * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod)) end},
        {slot = 3, stage = 2, damagetype = 2, damage = function(source, target, level) return ({150, 200, 250})[level] + 0.4 * (source.flatMagicDamageMod * source.percentMagicDamageMod) + ({0.3,0.9,1.5})[level] * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod)) end},
    },

    ["darius"] = {
        {slot = 0, stage = 1, damagetype = 1, damage = function(source, target, level) return ({40, 70, 100, 130, 160})[level] + (({1, 1.1, 1.2, 1.3, 1.4})[level] * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod))) end},
        {slot = 1, stage = 1, damagetype = 1, damage = function(source, target, level) return 0.4 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod)) end},
        {slot = 3, stage = 1, damagetype = 3, damage = function(source, target, level) return ({100, 200, 300})[level] + 0.75 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod) - source.baseAttackDamage) end},
    },

    ["diana"] = {
        {slot = 0, stage = 1, damagetype = 2, damage = function(source, target, level) return ({60, 95, 130, 165, 200})[level] + 0.7 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 1, stage = 1, damagetype = 2, damage = function(source, target, level) return ({22, 34, 46, 58, 70})[level] + 0.2 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 3, stage = 1, damagetype = 2, damage = function(source, target, level) return ({100, 160, 220})[level] + 0.6 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
    },

    ["drmundo"] = {
        {slot = 0, stage = 1, damagetype = 2, damage = function(source, target, level) return ({0.15, 0.175, 0.2, 0.225, 0.25})[level] * target.health end},
        {slot = 1, stage = 1, damagetype = 2, damage = function(source, target, level) return ({40, 55, 70, 85, 100})[level] + 0.1 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
    },

    ["draven"] = {
        {slot = 0, stage = 1, damagetype = 1, damage = function(source, target, level) return ({35, 40, 45, 50, 55})[level] + ({0.65, 0.75, 0.85, 0.95, 1.05})[level] * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod) - source.baseAttackDamage) end},
        {slot = 2, stage = 1, damagetype = 1, damage = function(source, target, level) return ({75, 110, 145, 180, 215})[level] + 0.5 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod) - source.baseAttackDamage) end},
        {slot = 3, stage = 1, damagetype = 1, damage = function(source, target, level) return ({175, 275, 375})[level] + 1.1 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod) - source.baseAttackDamage) end},
    },

    ["ekko"] = {
        {slot = 0, stage = 1, damagetype = 2, damage = function(source, target, level) return ({60, 75, 90, 105, 120})[level] + 0.3 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 0, stage = 2, damagetype = 2, damage = function(source, target, level) return ({40, 65, 90, 115, 140})[level] + 0.6 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 2, stage = 1, damagetype = 2, damage = function(source, target, level) return ({40, 65, 90, 115, 140})[level] + 0.4 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 3, stage = 1, damagetype = 2, damage = function(source, target, level) return ({150, 300, 450})[level] + 1.5 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
    },

    ["elise"] = {
        {slot = 0, stage = 1, damagetype = 2, damage = function(source, target, level) return ({40, 75, 110, 145, 180})[level] + (0.04 + (0.03 * ((source.flatMagicDamageMod * source.percentMagicDamageMod) / 100))) * target.health end},
        {slot = 0, stage = 2, damagetype = 2, damage = function(source, target, level) return ({60, 100, 140, 180, 220})[level] + (0.08 + (0.03 * ((source.flatMagicDamageMod * source.percentMagicDamageMod) / 100))) * (target.maxHealth - target.health) end},
        {slot = 1, stage = 1, damagetype = 2, damage = function(source, target, level) return ({55, 95, 135, 175, 215})[level] + 0.95 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
    },

    ["evelynn"] = {
        {slot = 0, stage = 1, damagetype = 2, damage = function(source, target, level) return ({25,30,35,40,45})[level] + 0.3 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 2, stage = 1, damagetype = 2, damage = function(source, target, level) return ({30,45,60,75,90})[level] + (0.03 + (0.015 * ((source.flatMagicDamageMod * source.percentMagicDamageMod) / 100))) * target.maxHealth end},
        {slot = 2, stage = 2, damagetype = 2, damage = function(source, target, level) return ({60,80,100,120,140})[level] + (0.04 + (0.025 * ((source.flatMagicDamageMod * source.percentMagicDamageMod) / 100))) * target.maxHealth end},
        {slot = 3, stage = 1, damagetype = 2, damage = function(source, target, level) return ({150,275,400})[level] + 0.75 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
    },

    ["ezreal"] = {
        {slot = 0, stage = 1, damagetype = 1, damage = function(source, target, level) return ({15, 40, 65, 90, 115})[level] + 0.4 * (source.flatMagicDamageMod * source.percentMagicDamageMod) + 1.1 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod)) end},
        {slot = 1, stage = 1, damagetype = 2, damage = function(source, target, level) return ({70, 115, 160, 205, 250})[level] + 0.8 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 2, stage = 1, damagetype = 2, damage = function(source, target, level) return ({80, 130, 180, 230, 280})[level] + 0.75 * (source.flatMagicDamageMod * source.percentMagicDamageMod) + 0.5 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod) - source.baseAttackDamage) end},
        {slot = 3, stage = 1, damagetype = 2, damage = function(source, target, level) return ({350, 500, 650})[level] + 0.9 * (source.flatMagicDamageMod * source.percentMagicDamageMod) + 1 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod) - source.baseAttackDamage) end},
    },

    ["fiddlesticks"] = {
        {slot = 1, stage = 1, damagetype = 2, damage = function(source, target, level) return ({80, 105, 130, 155, 180})[level] + 0.45 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 2, stage = 1, damagetype = 2, damage = function(source, target, level) return ({65, 85, 105, 125, 145})[level] + 0.45 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 3, stage = 1, damagetype = 2, damage = function(source, target, level) return ({125, 225, 325})[level] + 0.45 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
    },

    ["fiora"] = {
        {slot = 0, stage = 1, damagetype = 1, damage = function(source, target, level) return ({70, 80, 90, 100, 110})[level] + ({0.95, 1, 1.05, 1.1, 1.15})[level] * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod) - source.baseAttackDamage) end},
        {slot = 1, stage = 1, damagetype = 2, damage = function(source, target, level) return ({90, 130, 170, 210, 250})[level] + (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
    },

    ["fizz"] = {
        {slot = 0, stage = 1, damagetype = 2, damage = function(source, target, level) return ({10, 25, 40, 55, 70})[level] + 0.55 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 1, stage = 1, damagetype = 2, damage = function(source, target, level) return ({20, 30, 40, 50, 60})[level] + 0.4 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 2, stage = 1, damagetype = 2, damage = function(source, target, level) return ({70, 120, 170, 220, 270})[level] + 0.75 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 3, stage = 1, damagetype = 2, damage = function(source, target, level) return ({150, 250, 350})[level] + 0.6 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 3, stage = 2, damagetype = 2, damage = function(source, target, level) return ({225, 325, 425})[level] + 0.8 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 3, stage = 3, damagetype = 2, damage = function(source, target, level) return ({300, 400, 500})[level] + 1.2 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
    },

    ["galio"] = {
        {slot = 0, stage = 1, damagetype = 2, damage = function(source, target, level) return ({50, 80, 110, 140, 170})[level] + 0.8 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 2, stage = 1, damagetype = 2, damage = function(source, target, level) return ({100, 130, 160, 190, 220})[level] + 0.9 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 3, stage = 1, damagetype = 2, damage = function(source, target, level) return ({150, 250, 350})[level] + 0.7 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
    },

    ["gangplank"] = {
        {slot = 0, stage = 1, damagetype = 1, damage = function(source, target, level) return ({20, 45, 70, 95, 120})[level] + ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod)) end},
        {slot = 3, stage = 1, damagetype = 2, damage = function(source, target, level) return ({35, 60, 85})[level] + 0.1 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
    },

    ["garen"] = {
        {slot = 0, stage = 1, damagetype = 1, damage = function(source, target, level) return ({30, 55, 80, 105, 130})[level] + 0.4 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod)) end},
        {slot = 2, stage = 1, damagetype = 1, damage = function(source, target, level) return ({14, 18, 22, 26, 30})[level] + ({0.36, 0.37, 0.38, 0.39, 0.4})[level] * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod)) end},
        {slot = 3, stage = 1, damagetype = 2, damage = function(source, target, level) return ({175, 350, 525})[level] + ({0.286, 0.333, 0.4})[level] * (target.maxHealth - target.health) end},
    },

    ["gnar"] = {
        {slot = 0, stage = 1, damagetype = 1, damage = function(source, target, level) return ({5, 45, 85, 125, 165})[level] + 1.15 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod)) end},
        {slot = 0, stage = 2, damagetype = 1, damage = function(source, target, level) return ({5, 45, 85, 125, 165})[level] + 1.2 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod)) end},
        {slot = 1, stage = 1, damagetype = 2, damage = function(source, target, level) return ({10, 20, 30, 40, 50})[level] + (source.flatMagicDamageMod * source.percentMagicDamageMod) + (({6, 8, 10, 12, 14})[level] / 100) * target.maxHealth end},
        {slot = 1, stage = 2, damagetype = 1, damage = function(source, target, level) return ({25, 45, 65, 85, 105})[level] + ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod)) end},
        {slot = 2, stage = 1, damagetype = 1, damage = function(source, target, level) return ({20, 60, 100, 140, 180})[level] + source.maxHealth * 0.06 end},
        {slot = 2, stage = 2, damagetype = 1, damage = function(source, target, level) return ({20, 60, 100, 140, 180})[level] + source.maxHealth * 0.06 end},
        {slot = 3, stage = 1, damagetype = 1, damage = function(source, target, level) return ({200, 300, 400})[level] + 0.5 * (source.flatMagicDamageMod * source.percentMagicDamageMod) + 0.2 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod) - source.baseAttackDamage) end},
    },

    ["gragas"] = {
        {slot = 0, stage = 1, damagetype = 2, damage = function(source, target, level) return ({80, 120, 160, 200, 240})[level] + 0.6 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 1, stage = 1, damagetype = 2, damage = function(source, target, level) return ({20, 50, 80, 110, 140})[level] + 0.08 * target.maxHealth + 0.3 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 2, stage = 1, damagetype = 2, damage = function(source, target, level) return ({80, 130, 180, 230, 280})[level] + 0.6 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 3, stage = 1, damagetype = 2, damage = function(source, target, level) return ({200, 300, 400})[level] + 0.7 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
    },

    ["graves"] = {
        {slot = 0, stage = 1, damagetype = 1, damage = function(source, target, level) return ({45, 60, 75, 90, 105})[level] + ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod) - source.baseAttackDamage) end},
        {slot = 0, stage = 2, damagetype = 1, damage = function(source, target, level) return ({85, 115, 145, 175, 205})[level] + ({0.4, 0.7, 1, 1.3, 1.6})[level] * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod) - source.baseAttackDamage) end},
        {slot = 1, stage = 1, damagetype = 2, damage = function(source, target, level) return ({60, 110, 160, 210, 260})[level] + 0.6 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 3, stage = 1, damagetype = 1, damage = function(source, target, level) return ({250, 400, 550})[level] + 1.5 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod) - source.baseAttackDamage) end},
    },

    ["hecarim"] = {
        {slot = 0, stage = 1, damagetype = 1, damage = function(source, target, level) return ({50, 90, 125, 160, 195})[level] + 0.6 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod) - source.baseAttackDamage) end},
        {slot = 1, stage = 1, damagetype = 2, damage = function(source, target, level) return ({20, 30, 40, 50, 60})[level] + 0.2 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 2, stage = 1, damagetype = 1, damage = function(source, target, level) return ({45, 80, 115, 150, 185})[level] + 0.5 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod) - source.baseAttackDamage) end},
        {slot = 3, stage = 1, damagetype = 2, damage = function(source, target, level) return ({150, 250, 350})[level] + (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
    },

    ["heimerdinger"] = {
        {slot = 0, stage = 1, damagetype = 2, damage = function(source, target, level) return ({6, 9, 12, 15, 18})[level] + 0.3 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end}, -- norm turret Basic Attack
        {slot = 0, stage = 2, damagetype = 2, damage = function(source, target, level) return ({40, 60, 80, 100, 120})[level] + 0.55 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end}, -- norm turret Beam Attack
        {slot = 0, stage = 3, damagetype = 2, damage = function(source, target, level) return ({80, 100, 120})[source:spellSlot(3).level] + 0.3 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end}, -- apex turret Basic Attack
        {slot = 0, stage = 4, damagetype = 2, damage = function(source, target, level) return ({100, 140, 180})[source:spellSlot(3).level] + 0.7 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end}, -- apex turret Beam Attack
        {slot = 1, stage = 1, damagetype = 2, damage = function(source, target, level) return ({60, 90, 120, 150, 180})[level] + 0.45 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 1, stage = 2, damagetype = 2, damage = function(source, target, level) return ({135, 180, 225})[source:spellSlot(3).level] + 0.45 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 2, stage = 1, damagetype = 2, damage = function(source, target, level) return ({60, 100, 140, 180, 220})[level] + 0.6 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 2, stage = 2, damagetype = 2, damage = function(source, target, level) return ({150, 250, 350})[source:spellSlot(3).level] + 0.75 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
    },

    ["illaoi"] = {
        {slot = 0, stage = 1, damagetype = 1, damage = function(source, target, level) return ((source.levelRef * 10) + 1.2 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod))) + ({0.1,0.15,0.2,0.25,0.30})[level] * ((source.levelRef * 10) + 1.2 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod))) end},
        {slot = 1, stage = 1, damagetype = 1, damage = function(source, target, level) return ({0.03, 0.035, 0.04, 0.045, 0.05})[level] + (0.02 * (((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod)) / 100)) * (target.maxHealth - target.health) end},
        {slot = 3, stage = 1, damagetype = 1, damage = function(source, target, level) return ({150, 250, 350})[level] + 0.5 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod) - source.baseAttackDamage) end},
    },

    ["irelia"] = {
        {slot = 0, stage = 1, damagetype = 1, damage = function(source, target, level) return ({20, 50, 80, 110, 140})[level] + 1.2 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod)) end},
        {slot = 1, stage = 1, damagetype = 3, damage = function(source, target, level) return ({15, 30, 45, 60, 75})[level] end},
        {slot = 2, stage = 1, damagetype = 2, damage = function(source, target, level) return ({80, 120, 160, 200, 240})[level] + 0.5 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 3, stage = 1, damagetype = 1, damage = function(source, target, level) return ({80, 120, 160})[level] + 0.5 * (source.flatMagicDamageMod * source.percentMagicDamageMod) + 0.7 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod) - source.baseAttackDamage) end},
    },

    ["ivern"] = {
        {slot = 0, stage = 1, damagetype = 2, damage = function(source, target, level) return ({80,125,170,215,260})[level] + 0.7 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 1, stage = 1, damagetype = 2, damage = function(source, target, level) return ({20,30,40,50,60})[level] + 0.3 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 2, stage = 1, damagetype = 2, damage = function(source, target, level) return ({60,80,100,120,140})[level] + 0.8 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 3, stage = 1, damagetype = 2, damage = function(source, target, level) return ({70,100,170})[level] + 0.3 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
    },

    ["janna"] = {
        {slot = 0, stage = 1, damagetype = 2, damage = function(source, target, level) return ({60, 85, 110, 135, 160})[level] + 0.35 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 1, stage = 1, damagetype = 2, damage = function(source, target, level) return ({60, 115, 170, 225, 280})[level] + 0.5 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
    },

    ["jarvaniv"] = {
        {slot = 0, stage = 1, damagetype = 1, damage = function(source, target, level) return ({80, 120, 160, 200, 240})[level] + 1.2 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod) - source.baseAttackDamage) end},
        {slot = 2, stage = 1, damagetype = 2, damage = function(source, target, level) return ({60, 105, 150, 195, 240})[level] + 0.8 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 3, stage = 1, damagetype = 1, damage = function(source, target, level) return ({200, 325, 450})[level] + 1.5 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod) - source.baseAttackDamage) end},
    },

    ["jax"] = {
        {slot = 0, stage = 1, damagetype = 1, damage = function(source, target, level) return ({80, 120, 160, 200, 240})[level] + ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod) - source.baseAttackDamage) + 0.6 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 1, stage = 1, damagetype = 2, damage = function(source, target, level) return ({40, 75, 110, 145, 180})[level] + 0.6 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 2, stage = 1, damagetype = 1, damage = function(source, target, level) return ({55, 80, 105, 130, 155})[level] + 0.5 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod) - source.baseAttackDamage) end},
        {slot = 3, stage = 1, damagetype = 2, damage = function(source, target, level) return ({100, 160, 220})[level] + 0.7 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
    },

    ["jayce"] = {
        {slot = 0, stage = 1, damagetype = 1, damage = function(source, target, level) return ({70, 120, 170, 220, 270, 320})[level] + 1.2 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod) - source.baseAttackDamage) end},
        {slot = 0, stage = 2, damagetype = 1, damage = function(source, target, level) return ({45, 80, 115, 150, 185, 220})[level] + 1.2 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod) - source.baseAttackDamage) end},
        {slot = 1, stage = 1, damagetype = 2, damage = function(source, target, level) return ({25, 40, 55, 70, 85, 100})[level] + 0.25 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 2, stage = 1, damagetype = 2, damage = function(source, target, level) return (({8, 10.4, 12.8, 15.2, 17.6, 20})[level] / 100) * target.maxHealth + ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod) - source.baseAttackDamage) end},
        {slot = 3, stage = 1, damagetype = 1, damage = function(source, target, level) return ({25, 65, 105, 145})[level] + 0.25 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod) - source.baseAttackDamage) end},
    },

    ["jhin"] = {
        {slot = 0, stage = 1, damagetype = 1, damage = function(source, target, level) return ({45, 70, 95, 120, 145})[level] + ({0.4, 0.45, 0.5, 0.55, 0.6})[level] * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod)) + 0.6 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 1, stage = 1, damagetype = 1, damage = function(source, target, level) return ({50, 85, 120, 155, 190})[level] + 0.5 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod)) end},
        {slot = 2, stage = 1, damagetype = 2, damage = function(source, target, level) return ({20, 80, 140, 200, 260})[level] + 1.2 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod)) + (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 3, stage = 1, damagetype = 1, damage = function(source, target, level) return ({50, 115, 180})[level] + (0.2 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod))) + (((1 - (target.health / target.maxHealth)) * 2.5) * ({50, 115, 180})[level] + (0.2 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod)))) end},
        {slot = 3, stage = 2, damagetype = 1, damage = function(source, target, level) return (({50, 115, 180})[level] + (0.2 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod))) + (((1 - (target.health / target.maxHealth)) * 2.5) * ({50, 115, 180})[level] + (0.2 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod))))) * 2 end}, -- GetCritdamage..
    },

    ["jinx"] = {
        {slot = 0, stage = 1, damagetype = 1, damage = function(source, target, level) return 0.1 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod)) end},
        {slot = 1, stage = 1, damagetype = 1, damage = function(source, target, level) return ({10, 60, 110, 160, 210})[level] + 1.4 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod)) end},
        {slot = 2, stage = 1, damagetype = 2, damage = function(source, target, level) return ({70, 120, 170, 220, 270})[level] + (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 3, stage = 1, damagetype = 1, damage = function(source, target, level) return ({250, 350, 450})[level] + ({0.25, 0.30, 0.35})[level] * (target.maxHealth - target.health) + 1.5 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod) - source.baseAttackDamage) end},
    },

    ["kalista"] = {
        {slot = 0, stage = 1, damagetype = 1, damage = function(source, target, level) return ({10, 70, 130, 190, 250})[level] + ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod)) end},
        {slot = 2, stage = 1, damagetype = 1, damage = function(source, target, level) return ({20,30,40,50,60})[level] + 0.6 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod)) end},
        {slot = 2, stage = 2, damagetype = 1, damage = function(source, target, level) return ({10,14,19,25,32})[level] + ({0.2,0.225,0.25,0.275,0.3})[level] * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod)) end},
    },

    ["karma"] = {
        {slot = 0, stage = 1, damagetype = 2, damage = function(source, target, level) return ({80, 125, 170, 215, 260})[level] + 0.6 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 0, stage = 2, damagetype = 2, damage = function(source, target, level) return ({80, 125, 170, 215, 260})[level] + ({25, 75, 125, 175})[source:spellSlot(3).level] + 0.9 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 1, stage = 1, damagetype = 2, damage = function(source, target, level) return ({60, 110, 160, 210, 260})[level] + 0.9 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 1, stage = 2, damagetype = 2, damage = function(source, target, level) return ({60, 110, 160, 210, 260})[level] + 0.9 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
    },

    ["karthus"] = {
        {slot = 0, stage = 1, damagetype = 2, damage = function(source, target, level) return (({40, 60, 80, 100, 120})[level] + 0.3 * (source.flatMagicDamageMod * source.percentMagicDamageMod)) * 2 end},
        {slot = 0, stage = 2, damagetype = 2, damage = function(source, target, level) return ({40, 60, 80, 100, 120})[level] + 0.3 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 2, stage = 1, damagetype = 2, damage = function(source, target, level) return ({30, 50, 70, 90, 110})[level] + 0.2 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 3, stage = 1, damagetype = 2, damage = function(source, target, level) return ({250, 400, 550})[level] + 0.75 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
    },

    ["kassadin"] = {
        {slot = 0, stage = 1, damagetype = 2, damage = function(source, target, level) return ({65, 95, 125, 155, 185})[level] + 0.7 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 1, stage = 1, damagetype = 2, damage = function(source, target, level) return ({40, 65, 90, 115, 140})[level] + 0.7 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 1, stage = 2, damagetype = 2, damage = function(source, target, level) return 20 + 0.1 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 2, stage = 1, damagetype = 2, damage = function(source, target, level) return ({80, 105, 130, 155, 180})[level] + 0.7 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 3, stage = 1, damagetype = 2, damage = function(source, target, level) return ({80, 100, 120})[level] + 0.3 * (source.flatMagicDamageMod * source.percentMagicDamageMod) + 0.02 * source.maxPar end},
        {slot = 3, stage = 2, damagetype = 2, damage = function(source, target, level) return ({40, 50, 60})[level] + 0.1 * (source.flatMagicDamageMod * source.percentMagicDamageMod) + 0.01 * source.maxPar end},
    },

    ["katarina"] = {
        {slot = 0, stage = 1, damagetype = 2, damage = function(source, target, level) return ({75, 105, 135, 165, 195})[level] + 0.3 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 2, stage = 1, damagetype = 2, damage = function(source, target, level) return ({15, 30, 45, 60, 75})[level] + 0.25 * (source.flatMagicDamageMod * source.percentMagicDamageMod) + 0.5 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod)) end},
        {slot = 3, stage = 1, damagetype = 2, damage = function(source, target, level) return ({25, 37.5, 50})[level] + 0.22 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod) - source.baseAttackDamage) + 0.19 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 3, stage = 2, damagetype = 2, damage = function(source, target, level) return ({375, 562.5, 750})[level] + 3.3 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod) - source.baseAttackDamage) + 2.85 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
    },

    ["kayle"] = {
        {slot = 0, stage = 1, damagetype = 2, damage = function(source, target, level) return ({60, 110, 160, 210, 260})[level] + ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod) - source.baseAttackDamage) + 0.6 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 2, stage = 1, damagetype = 2, damage = function(source, target, level) return source.attackRange > 500 and ({20, 30, 40, 50, 60})[level] + 0.30 * (source.flatMagicDamageMod * source.percentMagicDamageMod) or ({10, 15, 20, 25, 30})[level] + 0.15 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
    },

    ["kayn"] = {
        {slot = 0, stage = 1, damagetype = 1, damage = function(source, target, level) return ({60,80,100,120,140})[level] + 0.65 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod) - source.baseAttackDamage) end},
        {slot = 1, stage = 1, damagetype = 1, damage = function(source, target, level) return ({90, 135, 180, 225, 270})[level] + 1.2 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod) - source.baseAttackDamage) end},
        {slot = 3, stage = 1, damagetype = 1, damage = function(source, target, level) return ({150,250,350})[level] + 1.5 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod) - source.baseAttackDamage) end},
    },

    ["kennen"] = {
        {slot = 0, stage = 1, damagetype = 2, damage = function(source, target, level) return ({75, 115, 155, 195, 235})[level] + 0.75 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 1, stage = 1, damagetype = 2, damage = function(source, target, level) return ({15, 20, 25, 30, 35})[level] + 0.6 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod) - source.baseAttackDamage) + 0.3 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 1, stage = 2, damagetype = 2, damage = function(source, target, level) return ({65, 95, 125, 155, 185})[level] + 0.55 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 2, stage = 1, damagetype = 2, damage = function(source, target, level) return ({85, 125, 165, 205, 245})[level] + 0.8 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 3, stage = 1, damagetype = 2, damage = function(source, target, level) return ({40, 75, 110})[level] + 0.2 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
    },

    ["khazix"] = {
        {slot = 0, stage = 1, damagetype = 1, damage = function(source, target, level) return ({65, 90, 115, 140, 165})[level] + 1.2 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod) - source.baseAttackDamage) end},
        {slot = 0, stage = 2, damagetype = 1, damage = function(source, target, level) return ({107.25, 148.5, 189.75, 231, 272.25})[level] + 1.98 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod) - source.baseAttackDamage) end},
        {slot = 1, stage = 1, damagetype = 1, damage = function(source, target, level) return ({85, 115, 145, 175, 205})[level] + ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod) - source.baseAttackDamage) end},
        {slot = 2, stage = 1, damagetype = 1, damage = function(source, target, level) return ({65, 100, 135, 170, 205})[level] + 0.2 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod) - source.baseAttackDamage) end},
    },

    ["kindred"] = {
        {slot = 0, stage = 1, damagetype = 1, damage = function(source, target, level) return ({60, 80, 100, 120, 140})[level] + 0.65 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod) - source.baseAttackDamage) end},
        {slot = 1, stage = 1, damagetype = 2, damage = function(source, target, level) return ({25, 30, 35, 40, 45})[level] + 0.2 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod) - source.baseAttackDamage) + 0.015 * target.health end},
        {slot = 2, stage = 1, damagetype = 1, damage = function(source, target, level) return ({65, 85, 105, 125, 145})[level] + 0.8 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod) - source.baseAttackDamage) + 0.08 * (target.maxHealth - target.health) end},
    },

    ["kled"] = {
        {slot = 0, stage = 1, damagetype = 1, damage = function(source, target, level) return ({30, 55, 80, 105, 130})[level] + 0.6 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod) - source.baseAttackDamage) end},
        {slot = 0, stage = 2, damagetype = 1, damage = function(source, target, level) return ({60, 110, 160, 210, 260})[level] + 1.2 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod) - source.baseAttackDamage) end},
        {slot = 0, stage = 3, damagetype = 1, damage = function(source, target, level) return ({35, 50, 65, 80, 95})[level] + 0.8 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod) - source.baseAttackDamage) end},
        {slot = 1, stage = 1, damagetype = 1, damage = function(source, target, level) return ({20,30,40,50,60})[level] + (({0.045, 0.05, 0.055, 0.06, 0.065})[level] + (0.05 * (((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod) - source.baseAttackDamage) / 100))) * target.maxHealth end},
        {slot = 2, stage = 1, damagetype = 1, damage = function(source, target, level) return ({35, 60, 85, 110, 135})[level] + 0.6 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod) - source.baseAttackDamage) end},
        {slot = 3, stage = 1, damagetype = 1, damage = function(source, target, level) return (({0.12, 0.15, 0.18})[level] + (0.12 * (((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod) - source.baseAttackDamage) / 100))) * target.maxHealth end},
    },

    ["kogmaw"] = {
        {slot = 0, stage = 1, damagetype = 2, damage = function(source, target, level) return ({80, 130, 180, 230, 280})[level] + 0.5 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 1, stage = 1, damagetype = 2, damage = function(source, target, level) return (({0.03, 0.04, 0.05, 0.06, 0.07})[level] + (0.01 * ((source.flatMagicDamageMod * source.percentMagicDamageMod) / 100))) * target.maxHealth end},
        {slot = 2, stage = 1, damagetype = 2, damage = function(source, target, level) return ({60, 105, 150, 195, 240})[level] + 0.5 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 3, stage = 1, damagetype = 2, damage = function(source, target, level) local dmg = (({100, 140, 180})[level] + 0.65 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod) - source.baseAttackDamage) + 0.25 * (source.flatMagicDamageMod * source.percentMagicDamageMod)) ; local healthMod = (1 - (target.health / target.maxHealth)) * 0.833 ; healthMod = (healthMod * 100) > 50 and 0.5 or healthMod ; return dmg + (healthMod * dmg) end},
    },

    ["leblanc"] = {
        {slot = 0, stage = 1, damagetype = 2, damage = function(source, target, level) return ({55, 90, 125, 160, 195})[level] + 0.5 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 0, stage = 2, damagetype = 2, damage = function(source, target, level) return ({150,275,400})[source:spellSlot(3).level] + 0.6 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 1, stage = 1, damagetype = 2, damage = function(source, target, level) return ({40, 55, 70, 85, 100})[level] + 0.2 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 1, stage = 2, damagetype = 2, damage = function(source, target, level) return ({60,120,180})[source:spellSlot(3).level] + 0.3 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 2, stage = 1, damagetype = 2, damage = function(source, target, level) return ({40, 60, 80, 100, 120})[level] + 0.5 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 2, stage = 2, damagetype = 2, damage = function(source, target, level) return ({100,160,220})[source:spellSlot(3).level] + 0.4 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
    },

    ["leesin"] = {
        {slot = 0, stage = 1, damagetype = 1, damage = function(source, target, level) return ({55,85,115,145,175})[level] + 0.9 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod) - source.baseAttackDamage) end},
        {slot = 0, stage = 2, damagetype = 1, damage = function(source, target, level) return ({55, 85, 115, 145, 175})[level] + 0.9 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod) - source.baseAttackDamage) + 0.08 * (target.maxHealth - target.health) end},
        {slot = 2, stage = 1, damagetype = 2, damage = function(source, target, level) return ({70, 105, 140, 175, 210})[level] + ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod) - source.baseAttackDamage) end},
        {slot = 3, stage = 1, damagetype = 1, damage = function(source, target, level) return ({150, 300, 450})[level] + 2 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod) - source.baseAttackDamage) end},
    },

    ["leona"] = {
        {slot = 0, stage = 1, damagetype = 2, damage = function(source, target, level) return ({10, 35, 60, 85, 110})[level] + 0.3 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 1, stage = 1, damagetype = 2, damage = function(source, target, level) return ({40, 80, 120, 160, 200})[level] + 0.4 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 2, stage = 1, damagetype = 2, damage = function(source, target, level) return ({60, 100, 140, 180, 220})[level] + 0.4 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 3, stage = 1, damagetype = 2, damage = function(source, target, level) return ({100, 175, 250})[level] + 0.8 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
    },

    ["lissandra"] = {
        {slot = 0, stage = 1, damagetype = 2, damage = function(source, target, level) return ({70, 100, 130, 160, 190})[level] + 0.7 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 1, stage = 1, damagetype = 2, damage = function(source, target, level) return ({70, 110, 150, 190, 230})[level] + 0.4 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 2, stage = 1, damagetype = 2, damage = function(source, target, level) return ({70, 115, 160, 205, 250})[level] + 0.6 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 3, stage = 1, damagetype = 2, damage = function(source, target, level) return ({150, 250, 350})[level] + 0.7 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
    },

    ["lucian"] = {
        {slot = 0, stage = 1, damagetype = 1, damage = function(source, target, level) return ({80, 120, 155, 190, 225})[level] + ({0.6, 0.7, 0.8, 0.9, 1})[level] * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod) - source.baseAttackDamage) end},
        {slot = 1, stage = 1, damagetype = 2, damage = function(source, target, level) return ({60, 100, 140, 180, 220})[level] + 0.9 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 3, stage = 1, damagetype = 1, damage = function(source, target, level) return ({20, 35, 50})[level] + 0.1 * (source.flatMagicDamageMod * source.percentMagicDamageMod) + 0.2 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod)) end},
    },

    ["lulu"] = {
        {slot = 0, stage = 1, damagetype = 2, damage = function(source, target, level) return ({80, 125, 170, 215, 260})[level] + 0.5 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 2, stage = 1, damagetype = 2, damage = function(source, target, level) return ({80, 110, 140, 170, 200})[level] + 0.4 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
    },

    ["lux"] = {
        {slot = 0, stage = 1, damagetype = 2, damage = function(source, target, level) return ({50, 100, 150, 200, 250})[level] + 0.7 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 2, stage = 1, damagetype = 2, damage = function(source, target, level) return ({60, 105, 150, 195, 240})[level] + 0.6 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 3, stage = 1, damagetype = 2, damage = function(source, target, level) return ({300, 400, 500})[level] + 0.75 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
    },

    ["malphite"] = {
        {slot = 0, stage = 1, damagetype = 2, damage = function(source, target, level) return ({70, 120, 170, 220, 270})[level] + 0.6 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 1, stage = 1, damagetype = 1, damage = function(source, target, level) return ({15, 30, 45, 60, 75})[level] + 0.1 * (source.flatMagicDamageMod * source.percentMagicDamageMod) + 0.15 * source.armor end},
        {slot = 2, stage = 1, damagetype = 2, damage = function(source, target, level) return ({60, 95, 130, 165, 200})[level] + 0.4 * source.armor + 0.2 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 3, stage = 1, damagetype = 2, damage = function(source, target, level) return ({200, 300, 400})[level] + (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
    },

    ["malzahar"] = {
        {slot = 0, stage = 1, damagetype = 2, damage = function(source, target, level) return ({70, 105, 140, 175, 210})[level] + 0.65 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 1, stage = 1, damagetype = 2, damage = function(source, target, level) return ((3.5 * source.levelRef + 1.5) + ({12,14,16,18,20})[level]) + 0.4 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod) - source.baseAttackDamage) + 0.2 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 2, stage = 1, damagetype = 2, damage = function(source, target, level) return ({80, 115, 150, 185, 220})[level] + 0.8 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 3, stage = 1, damagetype = 2, damage = function(source, target, level) return ({125,200,275})[level] + 0.8 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
    },

    ["maokai"] = {
        {slot = 0, stage = 1, damagetype = 2, damage = function(source, target, level) return ({65,105,145,185,225})[level] + 0.4 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 1, stage = 1, damagetype = 2, damage = function(source, target, level) return ({50,75,100,125,150})[level] + 0.4 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 2, stage = 1, damagetype = 2, damage = function(source, target, level) return ({25, 50, 75, 100, 125})[level] + (({0.06,0.065,0.07,0.075,0.08})[level] + (0.01 * ((source.flatMagicDamageMod * source.percentMagicDamageMod) / 100))) * target.maxHealth end},
        {slot = 2, stage = 2, damagetype = 2, damage = function(source, target, level) return ({50, 100, 150, 200, 250})[level] + (({0.12,0.13,0.14,0.15,0.16})[level] + (0.02 * ((source.flatMagicDamageMod * source.percentMagicDamageMod) / 100))) * target.maxHealth end},
        {slot = 3, stage = 1, damagetype = 2, damage = function(source, target, level) return ({150, 225, 300})[level] + 0.75 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
    },

    ["masteryi"] = {
        {slot = 0, stage = 1, damagetype = 1, damage = function(source, target, level) return ({25, 60, 95, 130, 165})[level] + ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod)) end},
        {slot = 2, stage = 1, damagetype = 3, damage = function(source, target, level) return ({14, 23, 32, 41, 50})[level] + 0.25 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod) - source.baseAttackDamage) end},
    },

    ["missfortune"] = {
        {slot = 0, stage = 1, damagetype = 1, damage = function(source, target, level) return ({20, 40, 60, 80, 100})[level] + 0.35 * (source.flatMagicDamageMod * source.percentMagicDamageMod) + ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod)) end},
        {slot = 2, stage = 1, damagetype = 2, damage = function(source, target, level) return ({80, 115, 150, 185, 220})[level] + 0.8 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 3, stage = 1, damagetype = 1, damage = function(source, target, level) return 0.75 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod)) + 0.2 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
    },

    ["monkeyking"] = {
        {slot = 0, stage = 1, damagetype = 1, damage = function(source, target, level) return ({30, 60, 90, 120, 150})[level] + 0.1 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod)) end},
        {slot = 1, stage = 1, damagetype = 2, damage = function(source, target, level) return ({70, 115, 160, 205, 250})[level] + 0.6 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 2, stage = 1, damagetype = 1, damage = function(source, target, level) return ({65, 110, 155, 200, 245})[level] + 0.8 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod) - source.baseAttackDamage) end},
        {slot = 3, stage = 1, damagetype = 1, damage = function(source, target, level) return ({20, 110, 200})[level] + 1.1 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod)) end},
    },

    ["mordekaiser"] = {
        {slot = 0, stage = 1, damagetype = 2, damage = function(source, target, level) return ({40, 80, 120, 160, 200})[level] + ({2,2.4,2.8,3.2,3.6})[level] * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod)) + 2.4 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 1, stage = 1, damagetype = 2, damage = function(source, target, level) return ({50, 85, 120, 155, 190})[level] + 0.3 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 2, stage = 1, damagetype = 2, damage = function(source, target, level) return ({35, 65, 95, 125, 155})[level] + 0.6 * (source.flatMagicDamageMod * source.percentMagicDamageMod) + 0.6 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod)) end},
        {slot = 3, stage = 1, damagetype = 2, damage = function(source, target, level) return (({0.25, 0.3, 0.35})[level] + (0.04 * ((source.flatMagicDamageMod * source.percentMagicDamageMod) / 100))) * target.maxHealth end},
    },

    ["morgana"] = {
        {slot = 0, stage = 1, damagetype = 2, damage = function(source, target, level) return ({80, 135, 190, 245, 300})[level] + 0.9 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 1, stage = 1, damagetype = 2, damage = function(source, target, level) return ({80, 160, 240, 320, 400})[level] + 1.1 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 3, stage = 1, damagetype = 2, damage = function(source, target, level) return ({150, 225, 300})[level] + 0.7 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
    },

    ["nami"] = {
        {slot = 0, stage = 1, damagetype = 2, damage = function(source, target, level) return ({75, 130, 185, 240, 295})[level] + 0.5 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 1, stage = 1, damagetype = 2, damage = function(source, target, level) return ({70, 110, 150, 190, 230})[level] + 0.5 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 2, stage = 1, damagetype = 2, damage = function(source, target, level) return ({20, 40, 55, 70, 85})[level] + 0.2 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 3, stage = 1, damagetype = 2, damage = function(source, target, level) return ({150, 250, 350})[level] + 0.6 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
    },

    ["nasus"] = {
        {slot = 0, stage = 1, damagetype = 1, damage = function(source, target, level) return GetBuffStacks(source, "nasusqstacks") + ({30, 50, 70, 90, 110})[level] end},
        {slot = 2, stage = 1, damagetype = 2, damage = function(source, target, level) return ({55, 95, 135, 175, 215})[level] + 0.6 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 2, stage = 2, damagetype = 2, damage = function(source, target, level) return ({11, 19, 27, 35, 43})[level] + 0.12 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 3, stage = 1, damagetype = 2, damage = function(source, target, level) return (({0.03, 0.04, 0.05})[level] + (0.01 * ((source.flatMagicDamageMod * source.percentMagicDamageMod) / 100))) * target.maxHealth end},
    },

    ["nautilus"] = {
        {slot = 0, stage = 1, damagetype = 2, damage = function(source, target, level) return ({80, 120, 160, 200, 240})[level] + 0.75 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 1, stage = 1, damagetype = 2, damage = function(source, target, level) return ({30, 40, 50, 60, 70})[level] + 0.4 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 2, stage = 1, damagetype = 2, damage = function(source, target, level) return ({55, 85, 115, 145, 175})[level] + 0.3 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 3, stage = 1, damagetype = 2, damage = function(source, target, level) return ({200, 325, 450})[level] + 0.8 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 3, stage = 2, damagetype = 2, damage = function(source, target, level) return ({125, 175, 225})[level] + 0.4 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
    },

    ["nidalee"] = {
        {slot = 0, stage = 1, damagetype = 2, damage = function(source, target, level) return ({70, 85, 100, 115, 130})[level] + 0.4 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 0, stage = 2, damagetype = 2, damage = function(source, target, level) local dmg = (({5, 30, 55, 80})[source:spellSlot(3).level] + 0.4 * (source.flatMagicDamageMod * source.percentMagicDamageMod) + 0.75 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod))) * ((target.maxHealth - target.health) / target.maxHealth * 1.5 + 1) dmg = dmg * (GetBuffStacks(target, "nidaleepassivehunted") > 0 and 1.4 or 1) return dmg end},
        {slot = 1, stage = 1, damagetype = 2, damage = function(source, target, level) return ({40, 80, 120, 160, 200})[level] + 0.2 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 1, stage = 2, damagetype = 2, damage = function(source, target, level) return ({60, 110, 160, 210})[source:spellSlot(3).level] + 0.3 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 2, stage = 2, damagetype = 2, damage = function(source, target, level) return ({70, 130, 190, 250})[source:spellSlot(3).level] + 0.45 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
    },

    ["nocturne"] = {
        {slot = 0, stage = 1, damagetype = 1, damage = function(source, target, level) return ({65, 110, 155, 200, 245})[level] + 0.75 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod) - source.baseAttackDamage) end},
        {slot = 2, stage = 1, damagetype = 2, damage = function(source, target, level) return ({80, 125, 170, 215, 260})[level] + (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 3, stage = 1, damagetype = 1, damage = function(source, target, level) return ({150, 250, 350})[level] + 1.2 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod) - source.baseAttackDamage) end},
    },

    ["nunu"] = {
        {slot = 0, stage = 1, damagetype = 3, damage = function(source, target, level) return ({340, 500, 660, 820, 980})[level] end},
        {slot = 2, stage = 1, damagetype = 2, damage = function(source, target, level) return ({80, 120, 160, 200, 240})[level] + 0.9 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 3, stage = 1, damagetype = 2, damage = function(source, target, level) return ({625, 875, 1125})[level] + 2.5 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
    },

    ["olaf"] = {
        {slot = 0, stage = 1, damagetype = 1, damage = function(source, target, level) return ({80, 125, 170, 215, 260})[level] + ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod) - source.baseAttackDamage) end},
        {slot = 2, stage = 1, damagetype = 3, damage = function(source, target, level) return ({70, 115, 160, 205, 250})[level] + 0.4 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod)) end},
    },

    ["orianna"] = {
        {slot = 0, stage = 1, damagetype = 2, damage = function(source, target, level) return ({60, 90, 120, 150, 180})[level] + 0.5 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 1, stage = 1, damagetype = 2, damage = function(source, target, level) return ({60, 105, 150, 195, 240})[level] + 0.7 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 2, stage = 1, damagetype = 2, damage = function(source, target, level) return ({60, 90, 120, 150, 180})[level] + 0.3 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 3, stage = 1, damagetype = 2, damage = function(source, target, level) return ({150, 225, 300})[level] + 0.7 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
    },

    ["ornn"] = {
        {slot = 0, stage = 1, damagetype = 1, damage = function(source, target, level) return ({20,50,80,110,140})[level] + ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod)) end},
        {slot = 1, stage = 1, damagetype = 2, damage = function(source, target, level) return ({0.1,0.12,0.14,0.16,0.18})[level] * target.maxHealth end},
        {slot = 2, stage = 1, damagetype = 1, damage = function(source, target, level) return ({30,50,70,90,110})[level] + 0.3 * source.bonusArmor + 0.3 * source.bonusSpellBlock end},
        {slot = 2, stage = 2, damagetype = 1, damage = function(source, target, level) return ({50,90,130,170,210})[level] + 0.3 * source.bonusArmor + 0.3 * source.bonusSpellBlock end},
        {slot = 3, stage = 1, damagetype = 2, damage = function(source, target, level) return ({125,175,225})[level] + 0.2 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
    },

    ["pantheon"] = {
        {slot = 0, stage = 1, damagetype = 1, damage = function(source, target, level) return ({75,115,155,195,235})[level] + 1.4 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod) - source.baseAttackDamage) end},
        {slot = 1, stage = 1, damagetype = 2, damage = function(source, target, level) return ({50, 75, 100, 125, 150})[level] + (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 2, stage = 1, damagetype = 1, damage = function(source, target, level) return ({100,150,200,250,300})[level] + 3 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod) - source.baseAttackDamage) end},
        {slot = 3, stage = 1, damagetype = 2, damage = function(source, target, level) return ({400, 700, 1000})[level] + (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 3, stage = 2, damagetype = 2, damage = function(source, target, level) return (({400, 700, 1000})[level] + (source.flatMagicDamageMod * source.percentMagicDamageMod)) * 0.5 end},
    },

    ["poppy"] = {
        {slot = 0, stage = 1, damagetype = 1, damage = function(source, target, level) return ({40, 60, 80, 100, 120})[level] + 0.8 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod) - source.baseAttackDamage) + 0.08 * target.maxHealth end},
        {slot = 0, stage = 2, damagetype = 1, damage = function(source, target, level) return ({80, 120, 160, 200, 240})[level] + 1.6 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod) - source.baseAttackDamage) + 0.16 * target.maxHealth end},
        {slot = 1, stage = 1, damagetype = 2, damage = function(source, target, level) return ({70, 110, 150, 190, 230})[level] + 0.7 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 2, stage = 1, damagetype = 1, damage = function(source, target, level) return ({55, 75, 95, 115, 135})[level] + 0.5 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod) - source.baseAttackDamage) end},
        {slot = 2, stage = 2, damagetype = 1, damage = function(source, target, level) return ({110, 150, 190, 230, 270})[level] + ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod) - source.baseAttackDamage) end},
        {slot = 3, stage = 1, damagetype = 1, damage = function(source, target, level) return ({210, 310, 410})[level] + 0.9 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod) - source.baseAttackDamage) end},
    },

    ["quinn"] = {
        {slot = 0, stage = 1, damagetype = 1, damage = function(source, target, level) return ({20,45,70,95,120})[level] + ({0.8, 0.9, 1, 1.1, 1.2})[level] * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod)) + 0.5 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 2, stage = 1, damagetype = 1, damage = function(source, target, level) return ({40, 70, 100, 130, 160})[level] + 0.2 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod) - source.baseAttackDamage) end},
        {slot = 3, stage = 1, damagetype = 1, damage = function(source, target, level) return 0.4 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod)) end},
    },

    ["rakan"] = {
        {slot = 0, stage = 1, damagetype = 2, damage = function(source, target, level) return ({70,115,160,205,250})[level] + 0.5 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 1, stage = 1, damagetype = 2, damage = function(source, target, level) return ({70,115,160,205,250})[level] + 0.5 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 3, stage = 1, damagetype = 2, damage = function(source, target, level) return ({100,200,300})[level] + 0.5 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
    },

    ["rammus"] = {
        {slot = 0, stage = 1, damagetype = 2, damage = function(source, target, level) return ({100, 135, 170, 205, 240})[level] + (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 3, stage = 1, damagetype = 2, damage = function(source, target, level) return ({40, 80, 120})[level] + 0.2 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
    },

    ["reksai"] = {
        {slot = 0, stage = 1, damagetype = 1, damage = function(source, target, level) return ({20,25,30,35,40})[level] + 0.4 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod) - source.baseAttackDamage) end},
        {slot = 0, stage = 2, damagetype = 1, damage = function(source, target, level) return ({60,90,120,150,180})[level] + 0.4 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod) - source.baseAttackDamage) + 0.7 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 1, stage = 1, damagetype = 1, damage = function(source, target, level) return ({55,70,85,100,115})[level] + 0.8 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod) - source.baseAttackDamage) end},
        {slot = 2, stage = 1, damagetype = 1, damage = function(source, target, level) return ({55,65,75,85,95})[level] + 0.85 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod) - source.baseAttackDamage) end},
        {slot = 3, stage = 1, damagetype = 1, damage = function(source, target, level) return ({100,250,400})[level] + 1.85 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod) - source.baseAttackDamage) + ({0.2, 0.25, 0.3})[level] * (target.maxHealth - target.health) end},
    },

    ["renekton"] = {
        {slot = 0, stage = 1, damagetype = 1, damage = function(source, target, level) return ({65, 95, 125, 155, 185})[level] + 0.8 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod) - source.baseAttackDamage) end},
        {slot = 0, stage = 2, damagetype = 1, damage = function(source, target, level) return (({65, 95, 125, 155, 185})[level] + 0.8 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod) - source.baseAttackDamage)) * 1.5 end},
        {slot = 1, stage = 1, damagetype = 1, damage = function(source, target, level) return ({5, 15, 25, 35, 45})[level] + 0.75 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod)) end},
        {slot = 1, stage = 2, damagetype = 1, damage = function(source, target, level) return ({15, 45, 75, 105, 135})[level] + 2.25 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod)) end},
        {slot = 2, stage = 1, damagetype = 1, damage = function(source, target, level) return ({40, 70, 100, 130, 160})[level] + 0.9 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod) - source.baseAttackDamage) end},
        {slot = 2, stage = 2, damagetype = 1, damage = function(source, target, level) return ({55, 100, 145, 190, 235})[level] + 1.35 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod) - source.baseAttackDamage) end},
        {slot = 3, stage = 1, damagetype = 2, damage = function(source, target, level) return ({40, 80, 120})[level] + 0.2 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
    },

    ["rengar"] = {
        {slot = 0, stage = 1, damagetype = 1, damage = function(source, target, level) return ({30, 50, 70, 90, 110})[level] + ({0.2, 0.3, 0.4, 0.5, 0.6})[level] * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod) - source.baseAttackDamage) end},
        {slot = 0, stage = 2, damagetype = 1, damage = function(source, target, level) return (104 + (16 * source.levelRef)) + 2.2 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod) - source.baseAttackDamage) end},
        {slot = 1, stage = 1, damagetype = 2, damage = function(source, target, level) return ({50, 80, 110, 140, 170})[level] + 0.8 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 2, stage = 1, damagetype = 1, damage = function(source, target, level) return ({50, 100, 145, 190, 235})[level] + 0.7 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod) - source.baseAttackDamage) end},
        {slot = 2, stage = 2, damagetype = 1, damage = function(source, target, level) return (35 + (15 * source.levelRef)) + 0.7 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod) - source.baseAttackDamage) end},
    },

    ["riven"] = {
        {slot = 0, stage = 1, damagetype = 1, damage = function(source, target, level) return ({15,35,55,75,95})[level] + ({0.45, 0.5, 0.55, 0.6, 0.65})[level] * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod)) end},
        {slot = 1, stage = 1, damagetype = 1, damage = function(source, target, level) return ({55, 85, 115, 145, 175})[level] + ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod) - source.baseAttackDamage) end},
        {slot = 3, stage = 1, damagetype = 1, damage = function(source, target, level) return ({100, 150, 200})[level] + 0.6 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod) - source.baseAttackDamage) end},
    },

    ["rumble"] = {
        {slot = 0, stage = 1, damagetype = 2, damage = function(source, target, level) return ({135, 180, 225, 270, 315})[level] + 1.1 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 0, stage = 2, damagetype = 2, damage = function(source, target, level) return ({202.5, 270, 337.5, 405, 472.5})[level] + 1.65 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 2, stage = 1, damagetype = 2, damage = function(source, target, level) return ({60, 85, 110, 135, 160})[level] + 0.4 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 2, stage = 2, damagetype = 2, damage = function(source, target, level) return ({90, 127.5, 165, 202.5, 240})[level] + 0.6 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 3, stage = 1, damagetype = 2, damage = function(source, target, level) return ({130, 185, 240})[level] + 0.3 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 3, stage = 2, damagetype = 2, damage = function(source, target, level) return ({650, 925, 1200})[level] + 1.5 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
    },

    ["ryze"] = {
        {slot = 0, stage = 1, damagetype = 2, damage = function(source, target, level) return ({60, 85, 110, 135, 160, 185})[level] + 0.45 * (source.flatMagicDamageMod * source.percentMagicDamageMod) + 0.02 * source.maxPar end},
        {slot = 0, stage = 2, damagetype = 2, damage = function(source, target, level) return (({60, 85, 110, 135, 160, 185})[level] + 0.45 * (source.flatMagicDamageMod * source.percentMagicDamageMod) + 0.02 * source.maxPar) * ({1.4, 1.5, 1.6, 1.7, 1.8})[level] end},
        {slot = 1, stage = 1, damagetype = 2, damage = function(source, target, level) return ({80, 100, 120, 140, 160})[level] + 0.6 * (source.flatMagicDamageMod * source.percentMagicDamageMod) + 0.01 * source.maxPar end},
        {slot = 2, stage = 1, damagetype = 2, damage = function(source, target, level) return ({50, 75, 100, 125, 150})[level] + 0.3 * (source.flatMagicDamageMod * source.percentMagicDamageMod) + 0.02 * source.maxPar end},
    },

    ["sejuani"] = {
        {slot = 0, stage = 1, damagetype = 2, damage = function(source, target, level) return ({60, 90, 120, 150, 180})[level] + 0.4 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 1, stage = 1, damagetype = 1, damage = function(source, target, level) return ({50, 90, 130, 170, 210})[level] + 0.06 * source.maxHealth end},
        {slot = 2, stage = 1, damagetype = 2, damage = function(source, target, level) return ({20, 30, 40, 50, 60})[level] + 0.3 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 3, stage = 1, damagetype = 2, damage = function(source, target, level) return ({150, 250, 350})[level] + 0.8 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
    },

    ["shaco"] = {
        {slot = 1, stage = 1, damagetype = 2, damage = function(source, target, level) return ({35, 50, 65, 80, 95})[level] + 0.2 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 2, stage = 1, damagetype = 1, damage = function(source, target, level) return ({55, 80, 105, 130, 155})[level] + ({0.6, 0.75, 0.9, 1.05, 1.2})[level] * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod) - source.baseAttackDamage) + 0.75 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 3, stage = 1, damagetype = 2, damage = function(source, target, level) return ({200, 300, 400})[level] + (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
    },

    ["shen"] = {
        {slot = 0, stage = 1, damagetype = 2, damage = function(source, target, level) return ({15,15,15,30,30,30,45,45,45,60,60,60,75,75,75,90,90,90})[source.levelRef] + (({0.06,0.075,0.09,0.105,0.12})[level] + (0.045 * ((source.flatMagicDamageMod * source.percentMagicDamageMod) / 100))) * target.maxHealth end},
        {slot = 0, stage = 2, damagetype = 2, damage = function(source, target, level) return ({15,15,15,30,30,30,45,45,45,60,60,60,75,75,75,90,90,90})[source.levelRef] + (({0.12,0.135,0.15,0.165,0.18})[level] + (0.06 * ((source.flatMagicDamageMod * source.percentMagicDamageMod) / 100))) * target.maxHealth end},
        {slot = 2, stage = 1, damagetype = 1, damage = function(source, target, level) return ({50, 75, 100, 125, 150})[level] + 0.11 * source.maxHealth end},
    },

    ["shyvana"] = {
        {slot = 0, stage = 1, damagetype = 1, damage = function(source, target, level) return ({0.4, 0.55, 0.7, 0.85, 1})[level] * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod)) end},
        {slot = 1, stage = 1, damagetype = 2, damage = function(source, target, level) return ({20, 32, 45, 57, 70})[level] + 0.2 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod) - source.baseAttackDamage) + 0.1 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 2, stage = 1, damagetype = 2, damage = function(source, target, level) return ({60, 100, 140, 180, 220})[level] + 0.3 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod)) + 0.3 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 3, stage = 1, damagetype = 2, damage = function(source, target, level) return ({150, 250, 350})[level] + 0.7 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
    },

    ["singed"] = {
        {slot = 0, stage = 1, damagetype = 2, damage = function(source, target, level) return ({40, 60, 80, 100, 120})[level] + 0.8 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 2, stage = 1, damagetype = 2, damage = function(source, target, level) return ({50, 65, 80, 95, 110})[level] + 0.75 * (source.flatMagicDamageMod * source.percentMagicDamageMod) + ({0.06, 0.065, 0.07, 0.075, 0.08})[level] * target.maxHealth end},
    },

    ["sion"] = {
        {slot = 0, stage = 1, damagetype = 1, damage = function(source, target, level) return ({20, 40, 60, 80, 100})[level] + 0.65 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod)) end},
        {slot = 0, stage = 2, damagetype = 1, damage = function(source, target, level) return ({60, 120, 180, 240, 300})[level] + 1.95 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod)) end},
        {slot = 1, stage = 1, damagetype = 2, damage = function(source, target, level) return ({40, 65, 90, 115, 140})[level] + 0.4 * (source.flatMagicDamageMod * source.percentMagicDamageMod) + ({0.1, 0.11, 0.12, 0.13, 0.14})[level] * target.maxHealth end},
        {slot = 2, stage = 1, damagetype = 2, damage = function(source, target, level) return ({70, 105, 140, 175, 210})[level] + 0.4 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 2, stage = 2, damagetype = 2, damage = function(source, target, level) return (({70, 105, 140, 175, 210})[level] + 0.4 * (source.flatMagicDamageMod * source.percentMagicDamageMod)) * 1.3 end},
        {slot = 3, stage = 1, damagetype = 1, damage = function(source, target, level) return ({150, 300, 450})[level] + 0.4 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod) - source.baseAttackDamage) end},
        {slot = 3, stage = 2, damagetype = 1, damage = function(source, target, level) return ({400, 800, 1200})[level] + 0.8 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod) - source.baseAttackDamage) end},
    },

    ["sivir"] = {
        {slot = 0, stage = 1, damagetype = 1, damage = function(source, target, level) return ({35, 55, 75, 95, 115})[level] + ({0.7, 0.8, 0.9, 1, 1.1})[level] * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod)) + 0.5 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 1, stage = 1, damagetype = 1, damage = function(source, target, level) return ({0.5, 0.55, 0.6, 0.65, 0.7})[level] * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod)) end},
    },

    ["skarner"] = {
        {slot = 0, stage = 1, damagetype = 1, damage = function(source, target, level) return ({0.33, 0.36, 0.39, 0.42, 0.45})[level] * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod)) end},
        {slot = 0, stage = 2, damagetype = 2, damage = function(source, target, level) return ({0.33, 0.36, 0.39, 0.42, 0.45})[level] * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod)) + 0.3 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 2, stage = 1, damagetype = 2, damage = function(source, target, level) return ({40, 65, 90, 115, 140})[level] + 0.2 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 2, stage = 2, damagetype = 1, damage = function(source, target, level) return ({30, 50, 70, 90, 110})[level] end},
        {slot = 3, stage = 1, damagetype = 2, damage = function(source, target, level) return ({40,120,200})[level] + (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 3, stage = 2, damagetype = 1, damage = function(source, target, level) return 1.2 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod)) end},
    },

    ["sona"] = {
        {slot = 0, stage = 1, damagetype = 2, damage = function(source, target, level) return ({40, 70, 100, 130, 160})[level] + 0.5 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 3, stage = 1, damagetype = 2, damage = function(source, target, level) return ({150, 250, 350})[level] + 0.5 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
    },

    ["soraka"] = {
        {slot = 0, stage = 1, damagetype = 2, damage = function(source, target, level) return ({70, 110, 150, 190, 230})[level] + 0.35 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 2, stage = 1, damagetype = 2, damage = function(source, target, level) return ({70, 110, 150, 190, 230})[level] + 0.4 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
    },

    ["swain"] = {
        {slot = 0, stage = 1, damagetype = 2, damage = function(source, target, level) return ({30, 47.5, 65, 82.5, 100})[level] + 0.3 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 1, stage = 1, damagetype = 2, damage = function(source, target, level) return ({80, 120, 160, 200, 240})[level] + 0.7 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 2, stage = 1, damagetype = 2, damage = function(source, target, level) return ({60, 96, 132, 168, 204})[level] + 1.2 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 3, stage = 1, damagetype = 2, damage = function(source, target, level) return ({50, 70, 90})[level] + 0.2 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
    },

    ["syndra"] = {
        {slot = 0, stage = 1, damagetype = 2, damage = function(source, target, level) return ({50, 95, 140, 185, 230})[level] + 0.65 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 1, stage = 1, damagetype = 2, damage = function(source, target, level) return ({70, 110, 150, 190, 230})[level] + 0.7 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 2, stage = 1, damagetype = 2, damage = function(source, target, level) return ({70, 115, 160, 205, 250})[level] + 0.6 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 3, stage = 1, damagetype = 2, damage = function(source, target, level) return ({270, 405, 540})[level] + 0.6 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 3, stage = 2, damagetype = 2, damage = function(source, target, level) return ({90, 135, 180})[level] + 0.2 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
    },

    ["tahmkench"] = {
        {slot = 0, stage = 1, damagetype = 2, damage = function(source, target, level) return ({80, 130, 180, 230, 280})[level] + 0.7 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 1, stage = 1, damagetype = 2, damage = function(source, target, level) return (({0.20, 0.23, 0.26, 0.29, 0.32})[level] + (0.02 * ((source.flatMagicDamageMod * source.percentMagicDamageMod) / 100))) * target.maxHealth end},
        {slot = 1, stage = 2, damagetype = 2, damage = function(source, target, level) return ({100, 150, 200, 250, 300})[level] + 0.6 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
    },

    ["taliyah"] = {
        {slot = 0, stage = 1, damagetype = 2, damage = function(source, target, level) return ({70, 95, 120, 145, 170})[level] + 0.45 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 0, stage = 2, damagetype = 2, damage = function(source, target, level) return ({182, 247, 312, 377, 442})[level] + 1.17 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 1, stage = 1, damagetype = 2, damage = function(source, target, level) return ({60, 80, 100, 120, 140})[level] + 0.4 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 2, stage = 1, damagetype = 2, damage = function(source, target, level) return ({70, 90, 110, 130, 150})[level] + 0.4 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 2, stage = 2, damagetype = 2, damage = function(source, target, level) return ({140, 180, 220, 260, 300})[level] + 0.8 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
    },

    ["talon"] = {
        {slot = 0, stage = 1, damagetype = 1, damage = function(source, target, level) return ({65, 90, 115, 140, 165})[level] + 1.1 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod) - source.baseAttackDamage) end},
        {slot = 0, stage = 2, damagetype = 1, damage = function(source, target, level) return ({97.5, 135, 172.5, 210, 247.5})[level] + 1.65 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod) - source.baseAttackDamage) end},
        {slot = 1, stage = 1, damagetype = 1, damage = function(source, target, level) return ({50, 60, 70, 80, 90})[level] + 0.4 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod) - source.baseAttackDamage) end},
        {slot = 1, stage = 2, damagetype = 1, damage = function(source, target, level) return ({70, 95, 120, 145, 170})[level] + 0.6 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod) - source.baseAttackDamage) end},
        {slot = 3, stage = 1, damagetype = 1, damage = function(source, target, level) return ({80, 120, 160})[level] + 0.8 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod) - source.baseAttackDamage) end},
    },

    ["taric"] = {
        {slot = 2, stage = 1, damagetype = 2, damage = function(source, target, level) return ({105, 150, 195, 240, 285})[level] + 0.5 * (source.flatMagicDamageMod * source.percentMagicDamageMod) + 0.3 * source.bonusArmor end},
    },

    ["teemo"] = {
        {slot = 0, stage = 1, damagetype = 2, damage = function(source, target, level) return ({80, 125, 170, 215, 260})[level] + 0.8 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 2, stage = 1, damagetype = 2, damage = function(source, target, level) return ({34, 68, 102, 136, 170})[level] + 0.7 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 2, stage = 2, damagetype = 2, damage = function(source, target, level) return ({10, 20, 30, 40, 50})[level] + 0.3 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 3, stage = 1, damagetype = 2, damage = function(source, target, level) return ({200, 325, 450})[level] + 0.5 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
    },

    ["thresh"] = {
        {slot = 0, stage = 1, damagetype = 2, damage = function(source, target, level) return ({80, 120, 160, 200, 240})[level] + 0.5 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 2, stage = 1, damagetype = 2, damage = function(source, target, level) return ({65, 95, 125, 155, 185})[level] + 0.4 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 3, stage = 1, damagetype = 2, damage = function(source, target, level) return ({250, 400, 550})[level] + (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
    },

    ["tristana"] = {
        {slot = 1, stage = 1, damagetype = 2, damage = function(source, target, level) return ({60, 110, 160, 210, 260})[level] + 0.5 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 2, stage = 1, damagetype = 1, damage = function(source, target, level) return ({60, 70, 80, 90, 100})[level] + ({0.5, 0.6, 0.7, 0.8, 0.9})[level] * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod) - source.baseAttackDamage) + 0.5 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 3, stage = 1, damagetype = 2, damage = function(source, target, level) return ({300, 400, 500})[level] + (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
    },

    ["trundle"] = {
        {slot = 0, stage = 1, damagetype = 1, damage = function(source, target, level) return ({20, 40, 60, 80, 100})[level] + ({0, 0.5, 0.1, 0.15, 0.2})[level] * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod)) end},
        {slot = 3, stage = 1, damagetype = 2, damage = function(source, target, level) return (({0.2, 0.275, 0.35})[level] + (0.02 * ((source.flatMagicDamageMod * source.percentMagicDamageMod) / 100))) * target.maxHealth end},
    },

    ["tryndamere"] = {
        {slot = 2, stage = 1, damagetype = 1, damage = function(source, target, level) return ({80, 110, 140, 170, 200})[level] + 1.2 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod) - source.baseAttackDamage) + (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
    },

    ["twistedfate"] = {
        {slot = 0, stage = 1, damagetype = 2, damage = function(source, target, level) return ({60, 105, 150, 195, 240})[level] + 0.65 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 1, stage = 1, damagetype = 2, damage = function(source, target, level) return ({40, 60, 80, 100, 120})[level] + ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod)) + 0.5 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 1, stage = 2, damagetype = 2, damage = function(source, target, level) return ({30, 45, 60, 75, 90})[level] + ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod)) + 0.5 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 1, stage = 3, damagetype = 2, damage = function(source, target, level) return ({15, 22.5, 30, 37.5, 45})[level] + ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod)) + 0.5 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 2, stage = 1, damagetype = 2, damage = function(source, target, level) return ({55, 80, 105, 130, 155})[level] + 0.5 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
    },

    ["twitch"] = {
        {slot = 2, stage = 1, damagetype = 1, damage = function(source, target, level) return (GetBuffStacks(target, "twitchdeadlyvenom") * ({15, 20, 25, 30, 35})[level] + 0.2 * (source.flatMagicDamageMod * source.percentMagicDamageMod) + 0.25 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod) - source.baseAttackDamage)) + ({20, 35, 50, 65, 80})[level] end},
        {slot = 2, stage = 2, damagetype = 1, damage = function(source, target, level) return ({15, 20, 25, 30, 35})[level] + 0.2 * (source.flatMagicDamageMod * source.percentMagicDamageMod) + 0.25 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod)) + ({20, 35, 50, 65, 80})[level] end},
    },

    ["udyr"] = {
        {slot = 0, stage = 1, damagetype = 1, damage = function(source, target, level) return ({30, 60, 90, 120, 150})[level] + (({120, 135, 150, 165, 180})[level] / 100) * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod)) end},
        {slot = 3, stage = 1, damagetype = 2, damage = function(source, target, level) return ({10, 20, 30, 40, 50})[level] + 0.25 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 3, stage = 2, damagetype = 2, damage = function(source, target, level) return ({40, 80, 120, 160, 200})[level] + 0.6 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
    },

    ["urgot"] = {
        {slot = 0, stage = 1, damagetype = 1, damage = function(source, target, level) return ({25, 70, 115, 160, 205})[level] + 0.7 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod)) end},
        {slot = 2, stage = 1, damagetype = 1, damage = function(source, target, level) return ({60, 100, 140, 180, 220})[level] + 0.5 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod)) end},
        {slot = 3, stage = 1, damagetype = 1, damage = function(source, target, level) return ({50, 175, 300})[level] + 0.5 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod)) end},
    },

    ["varus"] = {
        {slot = 0, stage = 1, damagetype = 1, damage = function(source, target, level) return ({10, 46.7, 83.3, 120, 156.7})[level] + ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod)) end},
        {slot = 0, stage = 2, damagetype = 1, damage = function(source, target, level) return ({15, 70, 125, 180, 235})[level] + 1.5 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod)) end},
        {slot = 1, stage = 1, damagetype = 2, damage = function(source, target, level) return ({10, 14, 18, 22, 26})[level] + 0.25 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 1, stage = 2, damagetype = 2, damage = function(source, target, level) return (({0.02, 0.0275, 0.035, 0.0425, 0.05})[level] + (0.02 * ((source.flatMagicDamageMod * source.percentMagicDamageMod) / 100))) * target.maxHealth end},
        {slot = 2, stage = 1, damagetype = 1, damage = function(source, target, level) return ({70, 105, 140, 175, 210})[level] + 0.6 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod) - source.baseAttackDamage) end},
        {slot = 3, stage = 1, damagetype = 2, damage = function(source, target, level) return ({100, 175, 250})[level] + (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
    },

    ["vayne"] = {
        {slot = 0, stage = 1, damagetype = 1, damage = function(source, target, level) return ({0.5, 0.55, 0.6, 0.65, 0.7})[level] * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod)) end},
        {slot = 1, stage = 1, damagetype = 3, damage = function(source, target, level) return ({50,65,80,95,110})[level] + ({0.04, 0.06, 0.08, 0.1, 0.12})[level] * target.maxHealth end},
        {slot = 2, stage = 1, damagetype = 1, damage = function(source, target, level) return ({50, 90, 120, 155, 190})[level] + 0.5 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod) - source.baseAttackDamage) end},
        {slot = 2, stage = 2, damagetype = 1, damage = function(source, target, level) return ({100, 170, 240, 310, 380})[level] + ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod) - source.baseAttackDamage) end},
    },

    ["veigar"] = {
        {slot = 0, stage = 1, damagetype = 2, damage = function(source, target, level) return ({70, 110, 150, 190, 230})[level] + 0.6 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 1, stage = 1, damagetype = 2, damage = function(source, target, level) return ({100, 150, 200, 250, 300})[level] + (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 3, stage = 1, damagetype = 2, damage = function(source, target, level) local dmg = (100 - ((target.health / target.maxHealth) * 100)) > 66.67 and ({350, 500, 650})[level] + 1.5 * (source.flatMagicDamageMod * source.percentMagicDamageMod) or (({175, 250, 325})[level] + 0.75 * (source.flatMagicDamageMod * source.percentMagicDamageMod)) * ((0.015 * (100 - ((target.health / target.maxHealth) * 100))) + 1); return dmg end},
    },

    ["velkoz"] = {
        {slot = 0, stage = 1, damagetype = 2, damage = function(source, target, level) return ({80, 120, 160, 200, 240})[level] + 0.6 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 1, stage = 1, damagetype = 2, damage = function(source, target, level) return ({75, 125, 175, 225, 275})[level] + 0.4 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 2, stage = 1, damagetype = 2, damage = function(source, target, level) return ({70, 100, 130, 160, 190})[level] + 0.3 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 3, stage = 1, damagetype = 3, damage = function(source, target, level) return (GetBuffStacks(target, "velkozresearchedstack") > 0 and ({450, 625, 800})[level] + 1.25 * (source.flatMagicDamageMod * source.percentMagicDamageMod) or common.CalculateMagicDamage(target, ({450, 625, 800})[level] + 1.25 * (source.flatMagicDamageMod * source.percentMagicDamageMod), source)) end},
    },

    ["vi"] = {
        {slot = 0, stage = 1, damagetype = 1, damage = function(source, target, level) return ({55, 80, 105, 130, 155})[level] + 0.8 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod) - source.baseAttackDamage) end},
        {slot = 1, stage = 1, damagetype = 1, damage = function(source, target, level) return (({0.04, 0.055, 0.07, 0.085, 0.01}) + (0.01 * (((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod) - source.baseAttackDamage) / 35))) * target.maxHealth end},
        {slot = 2, stage = 1, damagetype = 1, damage = function(source, target, level) return ({10, 30, 50, 70, 90})[level] + 1.15 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod)) + 0.7 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 3, stage = 1, damagetype = 1, damage = function(source, target, level) return ({150, 300, 450})[level] + 1.4 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod) - source.baseAttackDamage) end},
    },

    ["viktor"] = {
        {slot = 0, stage = 1, damagetype = 2, damage = function(source, target, level) return ({60, 80, 100, 120, 140})[level] + 0.4 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 0, stage = 2, damagetype = 2, damage = function(source, target, level) return ({20,40,60,80,100})[level] + ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod)) + 0.5 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 2, stage = 1, damagetype = 2, damage = function(source, target, level) return ({70, 110, 150, 190, 230})[level] + 0.5 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 2, stage = 2, damagetype = 2, damage = function(source, target, level) return ({90, 170, 250, 330, 410})[level] + 1.2 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 3, stage = 1, damagetype = 2, damage = function(source, target, level) return ({100, 175, 250})[level] + 0.5 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 3, stage = 2, damagetype = 2, damage = function(source, target, level) return ({150, 250, 350})[level] + 0.6 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
    },

    ["vladimir"] = {
        {slot = 0, stage = 1, damagetype = 2, damage = function(source, target, level) return ({80, 100, 120, 140, 160})[level] + 0.6 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 1, stage = 1, damagetype = 2, damage = function(source, target, level) return ({80, 135, 190, 245, 300})[level] + 0.05 * source.maxHealth end},
        {slot = 2, stage = 1, damagetype = 2, damage = function(source, target, level) return ({30, 45, 60, 75, 90})[level] + 0.35 * (source.flatMagicDamageMod * source.percentMagicDamageMod) + 0.025 * source.maxHealth end},
        {slot = 2, stage = 2, damagetype = 2, damage = function(source, target, level) return ({60, 90, 120, 150, 180})[level] + (source.flatMagicDamageMod * source.percentMagicDamageMod) + 0.06 * source.maxHealth end},
        {slot = 3, stage = 1, damagetype = 2, damage = function(source, target, level) return ({150, 250, 350})[level] + 0.7 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
    },

    ["volibear"] = {
        {slot = 0, stage = 1, damagetype = 1, damage = function(source, target, level) return ({30, 60, 90, 120, 150})[level] end},
        {slot = 1, stage = 1, damagetype = 1, damage = function(source, target, level) return ({60, 110, 160, 210, 260})[level] + 0.1 * source.maxHealth end},
        {slot = 2, stage = 1, damagetype = 2, damage = function(source, target, level) return ({60, 105, 150, 195, 240})[level] + 0.6 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 3, stage = 1, damagetype = 2, damage = function(source, target, level) return ({75, 115, 155})[level] + 0.3 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
    },

    ["warwick"] = {
        {slot = 0, stage = 1, damagetype = 2, damage = function(source, target, level) return 1.2 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod)) + 0.9 * (source.flatMagicDamageMod * source.percentMagicDamageMod) + ({0.06,0.07,0.08,0.09,0.1})[level] * target.maxHealth end},
        {slot = 3, stage = 1, damagetype = 2, damage = function(source, target, level) return ({175, 350, 525})[level] + 1.67 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod) - source.baseAttackDamage) end},
    },

    ["xayah"] = {
        {slot = 0, stage = 1, damagetype = 1, damage = function(source, target, level) return ({45, 65, 85, 105, 125})[level] + 0.5 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod) - source.baseAttackDamage) end},
        {slot = 2, stage = 1, damagetype = 1, damage = function(source, target, level) return ({55, 65, 75, 85, 95})[level] + 0.6 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod) - source.baseAttackDamage) end},
        {slot = 3, stage = 1, damagetype = 1, damage = function(source, target, level) return ({100, 150, 200})[level] + ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod) - source.baseAttackDamage) end},
    },

    ["xerath"] = {
        {slot = 0, stage = 1, damagetype = 2, damage = function(source, target, level) return ({80, 120, 160, 200, 240})[level] + 0.75 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 1, stage = 1, damagetype = 2, damage = function(source, target, level) return ({60, 90, 120, 150, 180})[level] + 0.6 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 2, stage = 1, damagetype = 2, damage = function(source, target, level) return ({80, 110, 140, 170, 200})[level] + 0.45 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 3, stage = 1, damagetype = 2, damage = function(source, target, level) return ({200, 240, 280})[level] + 0.43 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 3, stage = 2, damagetype = 2, damage = function(source, target, level) return ({600, 960, 1400})[level] + ({1.29, 1.72, 2.15})[level] * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
    },

    ["xinzhao"] = {
        {slot = 0, stage = 1, damagetype = 1, damage = function(source, target, level) return ({60, 75, 90, 105, 120})[level] + 1.2 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod) - source.baseAttackDamage) end},
        {slot = 1, stage = 1, damagetype = 1, damage = function(source, target, level) return ({60, 105, 150, 195, 240})[level] + 1.05 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod) - source.baseAttackDamage) end},
        {slot = 2, stage = 1, damagetype = 2, damage = function(source, target, level) return ({50, 75, 100, 125, 150})[level] + 0.6 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 3, stage = 1, damagetype = 1, damage = function(source, target, level) return ({75, 175, 275})[level] + ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod) - source.baseAttackDamage) + 0.15 * target.health end},
    },

    ["yasuo"] = {
        {slot = 0, stage = 1, damagetype = 1, damage = function(source, target, level) return ({20, 45, 70, 95, 120})[level] + ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod)) end},
        {slot = 2, stage = 1, damagetype = 2, damage = function(source, target, level) return ({60, 70, 80, 90, 100})[level] + 0.2 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod) - source.baseAttackDamage) + 0.6 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 3, stage = 1, damagetype = 1, damage = function(source, target, level) return ({200, 300, 400})[level] + 1.5 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod) - source.baseAttackDamage) end},
    },

    ["yorick"] = {
        {slot = 0, stage = 1, damagetype = 1, damage = function(source, target, level) return ({30, 55, 80, 105, 130})[level] + 0.4 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod)) end},
        {slot = 0, stage = 2, damagetype = 1, damage = function(source, target, level) return ({2,5,8,11,14,17,20,25,30,35,40,45,50,60,70,80,90,100})[source.levelRef] + 0.3 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod)) end},
        {slot = 2, stage = 1, damagetype = 2, damage = function(source, target, level) return ({70, 105, 140, 175, 210})[level] + 0.7 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 3, stage = 1, damagetype = 2, damage = function(source, target, level) return ({10, 20, 40})[level] + 0.5 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod)) end},
    },

    ["zac"] = {
        {slot = 0, stage = 1, damagetype = 2, damage = function(source, target, level) return ({30, 40, 50, 60, 70})[level] + 0.3 * (source.flatMagicDamageMod * source.percentMagicDamageMod) + 0.025 * source.maxHealth end},
        {slot = 1, stage = 1, damagetype = 2, damage = function(source, target, level) return ({15, 30, 45, 60, 75})[level] + (({0.04,0.05,0.06,0.07,0.08})[level] + (0.02 * ((source.flatMagicDamageMod * source.percentMagicDamageMod) / 100))) * target.maxHealth end},
        {slot = 2, stage = 1, damagetype = 2, damage = function(source, target, level) return ({60, 110, 160, 210, 260})[level] + 0.7 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 3, stage = 1, damagetype = 2, damage = function(source, target, level) return ({150, 250, 350})[level] + 0.7 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
    },

    ["zed"] = {
        {slot = 0, stage = 1, damagetype = 1, damage = function(source, target, level) return ({80, 115, 150, 185, 220})[level] + 0.9 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod) - source.baseAttackDamage) end},
        {slot = 0, stage = 2, damagetype = 1, damage = function(source, target, level) return ({60, 86.25, 112.5, 138.75, 165})[level] + 0.675 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod) - source.baseAttackDamage) end},
        {slot = 2, stage = 1, damagetype = 1, damage = function(source, target, level) return ({70, 95, 120, 145, 170})[level] + 0.8 * ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod) - source.baseAttackDamage) end},
        {slot = 3, stage = 1, damagetype = 1, damage = function(source, target, level) return ((source.baseAttackDamage + source.flatPhysicalDamageMod)*(1 + source.percentPhysicalDamageMod)) end},
    },

    ["ziggs"] = {
        {slot = 0, stage = 1, damagetype = 2, damage = function(source, target, level) return ({75, 120, 165, 210, 255})[level] + 0.65 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 1, stage = 1, damagetype = 2, damage = function(source, target, level) return ({70, 105, 140, 175, 210})[level] + 0.35 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 2, stage = 1, damagetype = 2, damage = function(source, target, level) return ({40, 65, 90, 115, 140})[level] + 0.3 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 3, stage = 1, damagetype = 2, damage = function(source, target, level) return ({200, 300, 400})[level] + 0.733 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 3, stage = 2, damagetype = 2, damage = function(source, target, level) return ({300, 450, 600})[level] + 1.1 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
    },

    ["zilean"] = {
        {slot = 0, stage = 1, damagetype = 2, damage = function(source, target, level) return ({75, 115, 165, 230, 300})[level] + 0.9 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
    },

    ["zoe"] = {
        --zoe Q damage is mutiplied based on the distance the projectile travels (800 = 0% increase, 2550 = 150% increase)
        --i cant do this calculation within the library, so it must be done script side :c
        {slot = 0, stage = 1, damagetype = 2, damage = function(source, target, level) return ({58,60,63,67,72,76,82,88,95,102,110,118,127,136,147,157,168,180})[source.levelRef] + (({0,30,60,90,120})[level] + 0.66 * (source.flatMagicDamageMod * source.percentMagicDamageMod)) end},
        -- spell theifs orbs total damage
        {slot = 1, stage = 1, damagetype = 2, damage = function(source, target, level) return ({70, 115, 160, 205, 250})[level] + 0.4 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        --spell theifs damage per orb
        {slot = 1, stage = 2, damagetype = 2, damage = function(source, target, level) return ({23.3, 38.3, 53.3, 68.3, 83.3})[level] + 0.133 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        --initial sleep trouble bubble magic damage proc
        {slot = 2, stage = 1, damagetype = 2, damage = function(source, target, level) return ({60,100,140,180,220})[level] + 0.4 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        --true damage taken on sleeping target 
        {slot = 2, stage = 2, damagetype = 3, damage = function(source, target, level) return ({60,100,140,180,220})[level] + 0.4 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        --total damage to target from sleepy trouble bubble procs
        {slot = 2, stage = 3, damagetype = 3, damage = function(source, target, level) return (({60,100,140,180,220})[level] + 0.4 * (source.flatMagicDamageMod * source.percentMagicDamageMod)) + common.CalculateMagicDamage(target, (({60,100,140,180,220})[level] + 0.4 * (source.flatMagicDamageMod * source.percentMagicDamageMod)), source) end},
    },
    
    ["zyra"] = {
        {slot = 0, stage = 1, damagetype = 2, damage = function(source, target, level) return ({60, 95, 130, 165, 200})[level] + 0.6 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 0, stage = 2, damagetype = 2, damage = function(source, target, level) return (24 + (5 * source.levelRef)) + 0.15 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 2, stage = 1, damagetype = 2, damage = function(source, target, level) return ({60, 95, 130, 165, 200})[level] + 0.5 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 2, stage = 2, damagetype = 2, damage = function(source, target, level) return (24 + (5 * source.levelRef)) + 0.15 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
        {slot = 3, stage = 1, damagetype = 2, damage = function(source, target, level) return ({180, 265, 350})[level] + 0.7 * (source.flatMagicDamageMod * source.percentMagicDamageMod) end},
    }
}

function damageLib.GetSpellDamage(sSlot, target, spellStage, from) -- stage is optional
    assert(type(sSlot) == "number", "GetSpellDamage: Wrong argument types (<number> expected for <sSlot> arg)")

    local source = from or objManager.player
    local name = string.lower(source.charName)
    local stage = spellStage or 1
    local spellTable = {}
    if stage > 4 then stage = 4 end

    if (sSlot == 0 or sSlot == 1 or sSlot == 2 or sSlot == 3) and target then
        local level = source:spellSlot(sSlot).level
        if level <= 0 then return 0 end

        if DamageLibTable[name] then
            for i=1, #DamageLibTable[name] do
                local spell = DamageLibTable[name][i]
                if spell.slot == sSlot then
                    table.insert(spellTable, spell)
                end
            end
            if stage > #spellTable then stage = #spellTable end

            for v = #spellTable, 1, -1 do
                local spells = spellTable[v]
                if spells.stage == stage then
                    if spells.damagetype == 1 then
                        return common.CalculatePhysicalDamage(target, spells.damage(source, target, level), source)
                    elseif spells.damagetype == 2 then
                        return common.CalculateMagicDamage(target, spells.damage(source, target, level), source)
                    elseif spells.damagetype == 3 then
                        return spells.damage(source, target, level)
                    end
                end
            end
        end
    end
    return 0
end

return damageLib
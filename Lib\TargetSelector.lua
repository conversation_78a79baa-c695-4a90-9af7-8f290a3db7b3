

local ove_0_10 = module.load("<PERSON>", "<PERSON><PERSON>/<PERSON><PERSON><PERSON><PERSON>")
local ove_0_11 = module.load("<PERSON>", "Lib/SpellDmg")
local ove_0_12 = module.load("<PERSON>", "Lib/DelayAction")
local ove_0_13 = objmanager.player
local ove_0_14 = {}
local ove_0_15 = {}
local ove_0_16 = {}
local ove_0_17 = {}
local ove_0_18 = {}

ove_0_14.concat = assert(table.concat)
ove_0_14.insert = assert(table.insert)
ove_0_14.remove = assert(table.remove)
ove_0_14.sort = assert(table.sort)

local function ove_0_19(arg_5_0, arg_5_1, arg_5_2)
	-- print 5
	for iter_5_0, iter_5_1 in pairs(arg_5_0) do
		if arg_5_2 and iter_5_1[arg_5_2] == arg_5_1 or iter_5_1 == arg_5_1 then
			return iter_5_0, iter_5_1
		end
	end
end

local ove_0_20 = ove_0_10.Class()

function ove_0_20.__init(arg_6_0, arg_6_1, arg_6_2, arg_6_3, arg_6_4, arg_6_5, arg_6_6)
	-- print 6
	arg_6_0.range = arg_6_1 or -1
	arg_6_0.damageType = arg_6_2 or 1
	arg_6_0.from = arg_6_3
	arg_6_0.focusSelected = arg_6_4 or false
	arg_6_0.menu = arg_6_5

	function arg_6_0.CalcDamage(arg_7_0, arg_7_1, arg_7_2)
		-- print 7
		return arg_7_1 == 1 and ove_0_11.CPD(arg_7_0, arg_7_2) or ove_0_11.CMD(arg_7_0, arg_7_2)
	end

	arg_6_0.sorting = {
		function(arg_8_0, arg_8_1)
			-- print 8
			return arg_6_0.CalcDamage(arg_8_0, arg_6_0.damageType, 100) / (1 + arg_8_0.health) * arg_6_0:GetPriority(arg_8_0) > arg_6_0.CalcDamage(arg_8_1, arg_6_0.damageType, 100) / (1 + arg_8_1.health) * arg_6_0:GetPriority(arg_8_1)
		end,
		function(arg_9_0, arg_9_1)
			-- print 9
			return arg_6_0.CalcDamage(arg_9_0, 1, 100) / (1 + arg_9_0.health) * arg_6_0:GetPriority(arg_9_0) > arg_6_0.CalcDamage(arg_9_1, 1, 100) / (1 + arg_9_1.health) * arg_6_0:GetPriority(arg_9_1)
		end,
		function(arg_10_0, arg_10_1)
			-- print 10
			return arg_6_0.CalcDamage(arg_10_0, 2, 100) / (1 + arg_10_0.health) * arg_6_0:GetPriority(arg_10_0) > arg_6_0.CalcDamage(arg_10_1, 2, 100) / (1 + arg_10_1.health) * arg_6_0:GetPriority(arg_10_1)
		end,
		function(arg_11_0, arg_11_1)
			-- print 11
			return arg_11_0.health < arg_11_1.health
		end,
		function(arg_12_0, arg_12_1)
			-- print 12
			return arg_12_0.baseAD + arg_12_0.bonusAD > arg_12_1.baseAD + arg_12_1.bonusAD
		end,
		function(arg_13_0, arg_13_1)
			-- print 13
			return arg_13_0.ap > arg_13_1.ap
		end,
		function(arg_14_0, arg_14_1)
			-- print 14
			return ove_0_10.Dist(arg_14_0, arg_6_0.from and arg_6_0.from or ove_0_13) < ove_0_10.Dist(arg_14_1, arg_6_0.from and arg_6_0.from or ove_0_13)
		end,
		function(arg_15_0, arg_15_1)
			-- print 15
			return arg_15_0.path.serverPos:dist(game.mousePos) < arg_15_1.path.serverPos:dist(game.mousePos)
		end
	}
	arg_6_0.SelectedTarget = nil

	if arg_6_5 then
		arg_6_0.menu:menu("fts", "Focus Target Settings")
		arg_6_0.menu.fts:boolean("focus", "Focus Selected Target", false)
		arg_6_0.menu.fts:boolean("focusa", "Attack Only Selected Target", false)
		arg_6_0.menu.fts:boolean("drawselected", "Draw Selected Target", true)
		arg_6_0.menu.fts:color("selectcolor", "Select Color :", 255, 0, 0, 255)
		arg_6_0.menu:slider("dist", "Target Selector Distance", 1500, 100, 5000, 50)
		arg_6_0.menu:dropdown("Mode", "Select Mode :", 1, {
			"Auto Priority",
			"Less Attack",
			"Less Cast",
			"Lowest HP",
			"Most AD",
			"Most AP",
			"Closest",
			"Closest to Mouse"
		})

		arg_6_0.ts_prio = {}

		arg_6_0.menu:header("priority", "Priority Settings")

		for iter_6_0, iter_6_1 in pairs(ove_0_10.GetEnemyHeroes()) do
			table.insert(arg_6_0.ts_prio, {
				charName = iter_6_1.charName,
				menu = arg_6_0.menu:slider(iter_6_1.charName, "Priority- " .. iter_6_1.charName, arg_6_0:GetDBPriority(iter_6_1.charName), 1, 4, 1)
			})
		end
	end

	if arg_6_6 then
		callback.add(enum.callback.draw, function()
			-- print 16
			arg_6_0:OnDraw()
		end)
	end

	callback.add(enum.callback.wndmsg, function(arg_17_0, arg_17_1)
		-- print 17
		arg_6_0:OnWndMsg(arg_17_0, arg_17_1)
	end)
end

function ove_0_20.OnDraw(arg_18_0)
	-- print 18
	if (arg_18_0.menu and arg_18_0.menu.fts.focus:get() or arg_18_0.focusSelected) and ove_0_10.IsValidTarget(arg_18_0.SelectedTarget) and arg_18_0.menu.fts.drawselected:get() then
		local slot_18_0 = arg_18_0.SelectedTarget.pos

		graphics.world.circle(slot_18_0, 200, 1, arg_18_0.menu.fts.selectcolor:get(), 100)

		local slot_18_1 = graphics.world.toscreen(arg_18_0.SelectedTarget)

		graphics.screen.text("Target Focused", 15, slot_18_1.x, slot_18_1.y + 35, graphics.argb(222, 231, 254, 255))
	end
end

function ove_0_20.OnWndMsg(arg_19_0, arg_19_1, arg_19_2)
	-- print 19
	if arg_19_1 == 513 and (arg_19_0.menu and arg_19_0.menu.fts.focus:get() or arg_19_0.focusSelected) then
		local slot_19_0
		local slot_19_1 = math.huge

		for iter_19_0, iter_19_1 in pairs(ove_0_10.GetEnemyHeroes()) do
			if ove_0_10.IsValidTarget(iter_19_1) then
				local slot_19_2 = iter_19_1.path.serverPos:dist(game.mousePos)

				if slot_19_2 < slot_19_1 and slot_19_2 < iter_19_1.bRadius * 1.25 then
					slot_19_0 = iter_19_1
					slot_19_1 = slot_19_2
				else
					arg_19_0.SelectedTarget = nil
				end
			end
		end

		if slot_19_0 then
			arg_19_0.SelectedTarget = slot_19_0
		end
	end
end

function ove_0_20.GetPriority(arg_20_0, arg_20_1)
	-- print 20
	local slot_20_0 = 1

	if arg_20_0.menu == nil then
		return slot_20_0
	end

	for iter_20_0 = 1, #arg_20_0.ts_prio do
		local slot_20_1 = 0

		if arg_20_0.ts_prio[iter_20_0].charName == arg_20_1.charName then
			slot_20_1 = iter_20_0
		end

		if slot_20_1 ~= 0 then
			slot_20_0 = arg_20_0.menu[arg_20_0.ts_prio[slot_20_1].charName]:get()
		end
	end

	if slot_20_0 == 2 then
		return 1.5
	elseif slot_20_0 == 3 then
		return 1.75
	elseif slot_20_0 == 4 then
		return 2
	elseif slot_20_0 == 5 then
		return 2.5
	end

	return slot_20_0
end

function ove_0_20.GetDBPriority(arg_21_0, arg_21_1)
	-- print 21
	local slot_21_0 = {
		"Alistar",
		"Amumu",
		"Bard",
		"Blitzcrank",
		"Braum",
		"Cho'Gath",
		"Dr. Mundo",
		"Garen",
		"Gnar",
		"Hecarim",
		"Janna",
		"Jarvan IV",
		"Leona",
		"Lulu",
		"Malphite",
		"Nami",
		"Nasus",
		"Nautilus",
		"Nunu",
		"Olaf",
		"Rammus",
		"Renekton",
		"Sejuani",
		"Shen",
		"Shyvana",
		"Singed",
		"Sion",
		"Skarner",
		"Sona",
		"Taric",
		"TahmKench",
		"Thresh",
		"Volibear",
		"Warwick",
		"MonkeyKing",
		"Yorick",
		"Zac",
		"Zyra"
	}
	local slot_21_1 = {
		"Aatrox",
		"Darius",
		"Elise",
		"Evelynn",
		"Galio",
		"Gangplank",
		"Gragas",
		"Irelia",
		"Jax",
		"Lee Sin",
		"Maokai",
		"Morgana",
		"Nocturne",
		"Pantheon",
		"Poppy",
		"Rengar",
		"Rumble",
		"Ryze",
		"Swain",
		"Trundle",
		"Tryndamere",
		"Udyr",
		"Urgot",
		"Vi",
		"XinZhao",
		"RekSai",
		"Kayn"
	}
	local slot_21_2 = {
		"Akali",
		"Diana",
		"Ekko",
		"Fiddlesticks",
		"Fiora",
		"Fizz",
		"Heimerdinger",
		"Jayce",
		"Kassadin",
		"Kayle",
		"Kha'Zix",
		"Lissandra",
		"Mordekaiser",
		"Nidalee",
		"Riven",
		"Shaco",
		"Vladimir",
		"Yasuo",
		"Zilean"
	}
	local slot_21_3 = {
		"Ahri",
		"Anivia",
		"Annie",
		"Ashe",
		"Azir",
		"Brand",
		"Caitlyn",
		"Cassiopeia",
		"Corki",
		"Draven",
		"Ezreal",
		"Graves",
		"Jinx",
		"Kalista",
		"Karma",
		"Karthus",
		"Katarina",
		"Kennen",
		"KogMaw",
		"Kindred",
		"Leblanc",
		"Lucian",
		"Lux",
		"Malzahar",
		"MasterYi",
		"MissFortune",
		"Orianna",
		"Quinn",
		"Sivir",
		"Syndra",
		"Talon",
		"Teemo",
		"Tristana",
		"TwistedFate",
		"Twitch",
		"Varus",
		"Vayne",
		"Veigar",
		"Velkoz",
		"Viktor",
		"Xerath",
		"Zed",
		"Ziggs",
		"Jhin",
		"Soraka",
		"Xayah",
		"Zoe"
	}

	if ove_0_19(slot_21_0, arg_21_1) then
		return 1
	end

	if ove_0_19(slot_21_1, arg_21_1) then
		return 2
	end

	if ove_0_19(slot_21_2, arg_21_1) then
		return 3
	end

	return ove_0_19(slot_21_3, arg_21_1) and 4 or 1
end

function ove_0_20.GetTarget(arg_22_0, arg_22_1)
	-- print 22
	arg_22_1 = arg_22_1 or arg_22_0.menu.dist:get()

	if (arg_22_0.menu and arg_22_0.menu.fts.focus:get() or arg_22_0.focusSelected) and ove_0_10.IsValidTarget(arg_22_0.SelectedTarget) and ove_0_10.Dist(arg_22_0.SelectedTarget) < (arg_22_1 or arg_22_0.range) and selector.valid_target(arg_22_0.SelectedTarget) then
		return arg_22_0.SelectedTarget
	end

	local slot_22_0 = {}

	for iter_22_0, iter_22_1 in pairs(ove_0_10.GetEnemyHeroes()) do
		if ove_0_10.IsValidTarget(iter_22_1) and ove_0_10.Dist(iter_22_1) < (arg_22_1 or arg_22_0.range) and selector.valid_target(iter_22_1) then
			ove_0_14.insert(slot_22_0, iter_22_1)
		end
	end

	arg_22_0.SortMode = arg_22_0.menu and arg_22_0.menu.Mode:get() or 1

	ove_0_14.sort(slot_22_0, arg_22_0.sorting[arg_22_0.SortMode])

	return #slot_22_0 > 0 and slot_22_0[1] or nil
end

return ove_0_20

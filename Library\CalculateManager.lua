local calculateManager = {}
calculateManager.version = 8.19

local BuffManager = module.load("<PERSON>", "Library/BuffManager")
local ItemManager = module.load("<PERSON>", "Library/ItemManager")
local MyCommon = module.load("<PERSON>", "Library/ExtraManager")

function calculateManager.CalculateMagicDamage(target, damage, source)
    if not target or target == nil or target.isDead or not target.spellBlock then
        return 0
    end

    source = source or player
    local amount = damage or 0
    local value = 0
    if target.spellBlock < 0 then
        value = 2 - 100 / (100 - target.spellBlock)
    elseif target.spellBlock * source.percentMagicPenetration - source.flatMagicPenetration < 0 then
        value = 1
    else
        value = 100 / (100 + target.spellBlock * source.percentMagicPenetration - source.flatMagicPenetration)
    end

    if BuffManager.HasBuff(target, "cursedtouch") then
        amount = amount * 1.1
    end

    if target.type == TYPE_MINION then
        if
            target.charName and string.lower(target.charName):find("minionmelee") and
                BuffManager.HasBuff(target, "exaltedwithbaronnashorminion")
         then
            value = value * 0.25
        end
        if source.buff then
            if
                BuffManager.HasBuff(source, "barontarget") and target.charName and
                    string.lower(target.charName):find("sru_baron")
             then
                value = value * 0.5
            end
        end
    end

    local result = math.floor(amount * value)
    return math.max(result, 0)
end

function calculateManager.CalculatePhysicalDamage(target, damage, source)
    if target and target ~= nil and not target.isDead and target.bonusArmor and target.armor then
        source = source or player
        if
            source.percentBonusArmorPenetration and source.percentArmorPenetration and source.physicalLethality and
                source.levelRef
         then
            local armor =
                ((target.bonusArmor * source.percentBonusArmorPenetration) + (target.armor - target.bonusArmor)) *
                source.percentArmorPenetration
            local lethality =
                (source.physicalLethality * 0.4) + ((source.physicalLethality * 0.6) * (source.levelRef / 18))
            local physicalReduction =
                armor >= 0 and (100 / (100 + (armor - lethality))) or (2 - (100 / (100 + (armor - lethality))))
            return damage * physicalReduction
        end
    end
    return 0
end

function calculateManager.GetAutoAttackDamage(source, target)
    if not source or not target then
        return 0
    end
    local dmgPhysical = MyCommon.GetTotalAD(source)
    local dmgMagical = 0
    local dmgReduce = 1
    if source.type == TYPE_MINION or source.type == TYPE_TURRET then
        return calculateManager.CalculatePhysicalDamage(target, dmgPhysical, source)
    end
    if target.type == TYPE_HERO then
        if ItemManager.HasItem(target, 3047) then
            dmgPhysical = dmgPhysical * 0.9
        end
        if target.charName == "Fizz" then
            dmgPhysical = dmgPhysical - (4 + 2 * math.floor((target.levelRef - 1) / 3))
        end
    end
    local PhysicalDamage = calculateManager.CalculatePhysicalDamage(target, dmgPhysical, source)
    local MagicalDamage = calculateManager.CalculateMagicDamage(target, dmgMagical, source)
    if target.type == TYPE_HERO and target.charName == "Amumu" then
        if BuffManager.HasBuff(target, "Tantrum") then
            local level = target:spellSlot(2).level
            if level > 0 then
                PhysicalDamage = PhysicalDamage - (({2, 4, 6, 8, 10})[level])
            end
        end
    end
    if source.type == TYPE_HERO and source.charName == "Kalista" then
        PhysicalDamage = PhysicalDamage * 0.9
    end
    if source.type == TYPE_HERO and source.charName == "Corki" then
        PhysicalDamage = calculateManager.CalculatePhysicalDamage(target, (dmgPhysical * 0.2), source)
        MagicalDamage = calculateManager.CalculateMagicDamage(target, (dmgPhysical * 0.8), source)
    end
    local resFloor = math.floor(PhysicalDamage + MagicalDamage) * dmgReduce
    if source.type == TYPE_HERO then
        if source.crit and source.crit >= 0.8 then
            local resMaxCrit = math.max(resFloor * 2, 0)
            return resMaxCrit
        end
    end
    local resMax = math.max(resFloor, 0)
    return resMax
end

return calculateManager

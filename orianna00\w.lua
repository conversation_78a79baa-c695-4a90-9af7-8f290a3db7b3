local ove_0_5 = module.internal("TS")
local ove_0_6 = module.internal("pred")
local ove_0_7 = module.load(header.id,"orianna/ball")
local ove_0_8 = player:spellSlot(1)
local ove_0_9
local ove_0_10 = {
	radius = 225,
	dashRadius = 0,
	boundingRadiusModTarget = 0,
	delay = 0,
	boundingRadiusModSource = 0
}

local function ove_0_11(arg_1_0, arg_1_1, arg_1_2)
	-- print 1
	if arg_1_2 > 3000 then
		return
	end

	if ove_0_6.present.get_prediction(ove_0_10, arg_1_1, ove_0_9) then
		arg_1_0.obj = arg_1_1

		return true
	end
end

local function ove_0_12()
	-- print 2
	ove_0_9 = ove_0_7.source()

	return ove_0_5.get_result(ove_0_11)
end

local function ove_0_13()
	-- print 3
	if ove_0_8.state == 0 then
		local slot_3_0 = ove_0_12()

		if slot_3_0.obj then
			return slot_3_0.obj
		end
	end
end

local function ove_0_14()
	-- print 4
	player:castSpell("self", 1)
end

return {
	get_action_state = ove_0_13,
	invoke_action = ove_0_14
}

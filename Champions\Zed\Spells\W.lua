local ove_0_10 = module.load("<PERSON>", "Lib/DelayAction")
local ove_0_11 = player
local ove_0_12 = {
	speed = 1750,
	range = 700,
	LastCastTime = 0,
	width = 250,
	Slot = player:spellSlot(1),
	-- 最新版本W技能数据
	energyCost = 40,
	shadowDuration = 5.0,
	swapRange = 2000
}

function ove_0_12.Cast1(arg_5_0)
	if not ove_0_12.Ready() or ove_0_12.IsW2() then
		return false
	end

	if navmesh.isWall(arg_5_0) then
		return false
	end

	if game.time * 1000 - ove_0_12.LastCastTime > 175 then
		local targetPos = arg_5_0
		local castPos = player.pos + (targetPos - player.pos):norm() * math.min(ove_0_12.range, player.pos:dist(targetPos))

		-- 改进的位置验证，确保影子不会卡在墙里
		local checkPositions = {castPos}
		for i = 1, 8 do
			local angle = (i - 1) * 45 * math.pi / 180
			local offset = vec3(math.cos(angle) * 50, 0, math.sin(angle) * 50)
			table.insert(checkPositions, castPos + offset)
		end

		for _, pos in ipairs(checkPositions) do
			if not navmesh.isWall(pos) and player.pos:dist(pos) <= ove_0_12.range then
				player:castSpell("pos", 1, pos)
				ove_0_12.LastCastTime = game.time * 1000
				return true
			end
		end
	end

	return false
end

function ove_0_12.Cast2()
	if not ove_0_12.Ready() or ove_0_12.IsW1() then
		return false
	end

	player:castSpell("self", 1)
	return true
end

function ove_0_12.CastEscape()
	if not ove_0_12.Ready() or ove_0_12.IsW2() then
		return false
	end
	
	local escapePos = player.pos + (game.mousePos - player.pos):norm() * ove_0_12.range
	
	if not navmesh.isWall(escapePos) then
		return ove_0_12.Cast1(escapePos)
	end
	
	return false
end

function ove_0_12.CastGapclose(target)
	if not target or not ove_0_12.Ready() or ove_0_12.IsW2() then
		return false
	end
	
	local dist = player.pos:dist(target.pos)
	if dist > ove_0_12.range + 200 then
		return false
	end
	
	local gapClosePos = player.pos + (target.pos - player.pos):norm() * math.min(ove_0_12.range, dist - 100)
	
	if not navmesh.isWall(gapClosePos) then
		return ove_0_12.Cast1(gapClosePos)
	end
	
	return false
end

function ove_0_12.Ready()
	return ove_0_12.Slot.state == 0
end

function ove_0_12.IsW1()
	return ove_0_12.Slot.name == "ZedW"
end

function ove_0_12.IsW2()
	return ove_0_12.Slot.name == "ZedW2"
end

function ove_0_12.Cost()
	return ove_0_12.energyCost
end

-- 智能W位置计算
function ove_0_12.GetOptimalWPosition(target)
	if not target then return nil end

	local targetPos = target.pos
	local playerPos = player.pos

	-- 如果目标在移动，预测其位置
	if target.path and target.path.isActive then
		local moveTime = 0.5 -- W飞行时间
		local moveDir = (target.path.endPos - target.pos):norm()
		local moveSpeed = target.moveSpeed or 350
		targetPos = target.pos + moveDir * moveSpeed * moveTime
	end

	-- 计算最佳W位置：在目标和玩家之间，稍微偏向目标
	local direction = (targetPos - playerPos):norm()
	local distance = math.min(ove_0_12.range, playerPos:dist(targetPos) * 0.8)
	local wPos = playerPos + direction * distance

	-- 确保位置安全
	if not navmesh.isWall(wPos) then
		return wPos
	end

	-- 如果主位置不安全，尝试周围位置
	for i = 1, 8 do
		local angle = (i - 1) * 45 * math.pi / 180
		local offset = vec3(math.cos(angle) * 100, 0, math.sin(angle) * 100)
		local altPos = wPos + offset

		if not navmesh.isWall(altPos) and playerPos:dist(altPos) <= ove_0_12.range then
			return altPos
		end
	end

	return nil
end

function ove_0_12.Level()
	return player:spellSlot(1).level
end

return ove_0_12

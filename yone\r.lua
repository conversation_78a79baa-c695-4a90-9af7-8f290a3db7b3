local ove_0_10 = module.internal("TS")
local ove_0_11 = module.internal("orb")
local ove_0_12 = module.internal("pred")
local ove_0_13 = module.load(header.id, "yone/menu")
local ove_0_14 = player:spellSlot(2)
local ove_0_15 = player:spellSlot(3)
local ove_0_16 = 1500000
local ove_0_17 = {
	range = 1000,
	delay = 0.75,
	boundingRadiusMod = 1,
	width = 135,
	speed = math.huge,
	collision = {
		hero = true
	},
	damage = function(arg_5_0)
		-- print 5
		return 200 * ove_0_15.level + 0.8 * player.totalAd
	end
}

local function ove_0_18(arg_6_0)
	-- print 6
	local slot_6_0 = ove_0_13.r_block.range:get()
	local slot_6_1 = 0

	for iter_6_0 = 0, objManager.enemies_n - 1 do
		local slot_6_2 = objManager.enemies[iter_6_0]

		if slot_6_2 ~= arg_6_0 and slot_6_2.path.serverPos2D:distSqr(arg_6_0.path.serverPos2D) < slot_6_0 * slot_6_0 then
			slot_6_1 = slot_6_1 + 1
		end
	end

	return slot_6_1
end

local function ove_0_19(arg_7_0)
	-- print 7
	if ove_0_12.trace.linear.hardlock(ove_0_17, arg_7_0.seg, arg_7_0.obj) then
		return true
	end

	if ove_0_12.trace.linear.hardlockmove(ove_0_17, arg_7_0.seg, arg_7_0.obj) then
		return true
	end

	if ove_0_12.trace.newpath(arg_7_0.obj, 0.033, 2) then
		return true
	end
    
    -- 更宽松的判断，增加释放机会
    return true
end

local function ove_0_20(arg_8_0, arg_8_1, arg_8_2)
	-- print 8
	if arg_8_2 > 3000 then  -- 增大搜索范围，原为2000
		return
	end

	if not ove_0_13.r.whitelist[arg_8_1.charName]:get() then
		return
	end

	-- 检查敌人血量百分比，只有在血量低于设定值时才释放R
	local enemy_hp_percent = arg_8_1.health / arg_8_1.maxHealth * 100
	if enemy_hp_percent > ove_0_13.r.enemy_hp_percent:get() then
		return
	end

	local slot_8_0 = ove_0_12.linear.get_prediction(ove_0_17, arg_8_1)

	if slot_8_0 and slot_8_0.startPos:distSqr(slot_8_0.endPos) < ove_0_16 then
        -- 移除多余的封锁检查，简化判断条件
		local slot_8_1 = ove_0_13.r.num_hits:get()

		if slot_8_1 > 1 then
			local slot_8_2 = ove_0_12.collision.get_prediction(ove_0_17, slot_8_0, arg_8_1)

			-- 仅当明确不可能命中所需目标时才返回false
			if slot_8_2 and #slot_8_2 + 1 < slot_8_1 - 1 then
				return false
			end
		end

		arg_8_0.obj = arg_8_1
		arg_8_0.seg = slot_8_0
		arg_8_0.pos = slot_8_0.endPos

		return true
	end
end

local ove_0_21 = {}
local ove_0_22 = 0

local function ove_0_23()
	-- print 9
	if ove_0_15.state == 0 and os.clock() > ove_0_22 then
		ove_0_21 = ove_0_10.get_result(ove_0_20)

		if ove_0_21.obj and ove_0_19(ove_0_21) then
			return ove_0_21
		end
	end
end

local function ove_0_24(arg_10_0, arg_10_1, arg_10_2)
	-- print 10
	if arg_10_2 > 2000 then
		return
	end

	if not ove_0_13.r.whitelist[arg_10_1.charName]:get() then
		return
	end

	local slot_10_0 = ove_0_12.linear.get_prediction(ove_0_17, arg_10_1)

	if slot_10_0 and slot_10_0.startPos:distSqr(slot_10_0.endPos) < ove_0_16 then
		arg_10_0.obj = arg_10_1
		arg_10_0.seg = slot_10_0
		arg_10_0.pos = slot_10_0.endPos

		return true
	end
end

local function ove_0_25()
	-- print 11
	if ove_0_15.state == 0 and os.clock() > ove_0_22 then
		ove_0_21 = ove_0_10.get_result(ove_0_24)

		if ove_0_21.obj and ove_0_19(ove_0_21) then
			return ove_0_21
		end
	end
end

local ove_0_26 = 0

local function ove_0_27()
	-- print 12
	local slot_12_0 = vec3(ove_0_21.pos.x, ove_0_21.obj.y, ove_0_21.pos.y)

	player:castSpell("pos", 3, slot_12_0)

	ove_0_22 = os.clock() + network.latency + 0.25
	ove_0_26 = os.clock() + network.latency

	ove_0_11.core.set_server_pause()
end

local ove_0_28 = false
local ove_0_29
local ove_0_30

cb.add(cb.spell, function(arg_13_0)
	-- print 13
	if arg_13_0.owner == player and arg_13_0.name == "YoneR" and os.clock() - ove_0_26 < 0.1 then
		ove_0_29 = os.clock()
		ove_0_30 = 1
	end
end)
cb.add(cb.draw, function()
	-- print 14
	if ove_0_28 then
		return
	end

	if ove_0_14.state ~= 0 then
		-- block empty
	end

	if not ove_0_13.link.e_on_r:get() then
		return
	end

	if ove_0_30 and ove_0_29 then
		local slot_14_0 = ove_0_29 + ove_0_30

		if slot_14_0 > os.clock() and slot_14_0 <= os.clock() + network.latency and ove_0_13.combat:get() and ove_0_21.obj then
			player:castSpellAdmin("pos", 2, ove_0_21.obj.pos)

			ove_0_30 = false
			ove_0_29 = false
		end
	end
end)

local function ove_0_31(arg_15_0)
	-- print 15
	-- 检查自身血量条件
	if ove_0_13.r.ks_hp_yone:get() > 0 and player.health/player.maxHealth*100 <= ove_0_13.r.ks_hp_yone:get() then
		return false
	end
	
	-- 检查敌人是否在普通攻击范围外
	if ove_0_13.r.ks_outside:get() and player.pos:dist(arg_15_0.pos) <= player.attackRange + arg_15_0.boundingRadius then
		return false
	end
	
	-- 检查敌人是否可以逃脱
	if ove_0_13.r.ks_escape:get() then
		-- 检查敌人是否有位移技能或者移速够快
		local has_escape = false
		-- 常见的逃生技能和特性
		local escape_champs = {
			["Ezreal"] = true,
			["Leblanc"] = true,
			["Zed"] = true,
			["Ahri"] = true,
			["Kassadin"] = true,
			["Khazix"] = true,
			["Tristana"] = true,
			["Vayne"] = true
		}
		
		if escape_champs[arg_15_0.charName] then
			-- 检查CD和蓝量
			for i = 0, 3 do
				local spell = arg_15_0:spellSlot(i)
				if spell and spell.state == 0 and spell.level > 0 then
					has_escape = true
					break
				end
			end
		end
		
		if has_escape and arg_15_0.health/arg_15_0.maxHealth*100 > 10 then
			return false
		end
	end
	
	-- 计算伤害
	local slot_15_0 = arg_15_0.health + arg_15_0.healthRegenRate
	local slot_15_1 = ove_0_17.damage(arg_15_0)
	
	-- 考虑抵抗和护盾
	local slot_15_2 = arg_15_0.spellBlock * player.percentMagicPenetration - player.flatMagicPenetration
	local slot_15_3 = slot_15_2 > -1 and 100 / (100 + slot_15_2) or 1
	local slot_15_4 = slot_15_1 / 2 * slot_15_3
	
	local slot_15_5 = (arg_15_0.bonusArmor * player.percentBonusArmorPenetration + (arg_15_0.armor - arg_15_0.bonusArmor)) * player.percentArmorPenetration
	local slot_15_6 = player.physicalLethality * (0.6 + 0.4 * player.levelRef / 18)
	local slot_15_7 = mathf.clamp(0, slot_15_5, slot_15_5 - slot_15_6)
	local slot_15_8 = slot_15_5 >= 0 and 100 / (100 + slot_15_7) or 1
	
	-- 计算物理魔法混合伤害
	local total_damage = slot_15_4 + slot_15_1 / 2 * slot_15_8
	
	-- 考虑敌方护盾
	total_damage = total_damage - arg_15_0.allShield - arg_15_0.magicalShield - arg_15_0.physicalShield
	
	-- 检查是否可以击杀
	return total_damage > slot_15_0
end

local function ove_0_32(arg_16_0, arg_16_1, arg_16_2)
	-- print 16
	if arg_16_2 > 2000 then
		return
	end

	if not ove_0_13.r.whitelist[arg_16_1.charName]:get() then
		return
	end

	local slot_16_0 = ove_0_12.linear.get_prediction(ove_0_17, arg_16_1)

	if slot_16_0 and slot_16_0.startPos:distSqr(slot_16_0.endPos) < ove_0_16 and ove_0_31(arg_16_1) then
		arg_16_0.obj = arg_16_1
		arg_16_0.seg = slot_16_0
		arg_16_0.pos = slot_16_0.endPos

		return true
	end
end

local function ove_0_33()
	-- print 17
	if ove_0_15.state == 0 and os.clock() > ove_0_22 then
		ove_0_21 = ove_0_10.loop(ove_0_32)

		if ove_0_21.obj and ove_0_19(ove_0_21) then
			return ove_0_21
		end
	end
end

local function ove_0_34()
	-- print 18
	local slot_18_0 = vec3(ove_0_21.pos.x, ove_0_21.obj.y, ove_0_21.pos.y)

	player:castSpell("pos", 3, slot_18_0)

	ove_0_22 = os.clock() + network.latency + 0.25

	ove_0_11.core.set_server_pause()
end

cb.add(cb.create_particle, function(arg_19_0)
	-- print 19
	if arg_19_0.name:find("Yone") and arg_19_0.name:find("E_Demon_Avatar") then
		ove_0_28 = true
	end

	if arg_19_0.name:find("Yone") and arg_19_0.name:find("E_Dash_Head") then
		ove_0_28 = false
	end
end)

-- 添加被控制状态类型
local cc_buff_types = {
	5,  -- 眩晕
	8,  -- 恐惧
	11, -- 魅惑
	21, -- 击飞
	22, -- 禁锢
	24, -- 压制
	29, -- 睡眠
	30  -- 减速
}

-- 检查敌人是否被控制
local function is_cc_controlled(enemy)
	if not enemy or not enemy.buffCount then return false end
	
	local min_cc_time = ove_0_13.r.cc_enemy_time:get() / 1000
	
	for i = 0, enemy.buffCount - 1 do
		local buff = enemy:buffByIndex(i)
		if buff and buff.valid and buff.duration >= min_cc_time then
			for _, cc_type in ipairs(cc_buff_types) do
				if buff.type == cc_type then
					return true
				end
			end
		end
	end
	
	return false
end

-- 获取被控制敌人
local function get_cc_target()
	if not ove_0_13.r.cc_enemy:get() then
		return nil
	end

	local enemies = {}
	for i = 0, objManager.enemies_n - 1 do
		local enemy = objManager.enemies[i]
		if enemy and enemy.isVisible and not enemy.isDead and 
		   enemy.isTargetable and enemy.pos:dist(player.pos) <= ove_0_17.range then
			
			-- 检查是否被控制且血量低于设定值
			if is_cc_controlled(enemy) and 
			   enemy.health/enemy.maxHealth*100 <= ove_0_13.r.cc_enemy_hp:get() and 
			   ove_0_13.r.whitelist[enemy.charName]:get() then
				table.insert(enemies, enemy)
			end
		end
	end

	-- 找出最近的敌人
	if #enemies > 0 then
		table.sort(enemies, function(a, b)
			return a.pos:dist(player.pos) < b.pos:dist(player.pos)
		end)
		return enemies[1]
	end

	return nil
end

-- 获取被控制敌人R状态
local function get_cc_action()
	if ove_0_15.state == 0 and os.clock() > ove_0_22 then
		local target = get_cc_target()
		if target then
			local pred = ove_0_12.linear.get_prediction(ove_0_17, target)
			if pred and pred.startPos:distSqr(pred.endPos) < ove_0_16 then
				ove_0_21.obj = target
				ove_0_21.seg = pred
				ove_0_21.pos = pred.endPos
				return ove_0_21
			end
		end
	end
	return nil
end

-- 释放R到被控制敌人
local function invoke_cc_action()
	local slot_12_0 = vec3(ove_0_21.pos.x, ove_0_21.obj.y, ove_0_21.pos.y)
	player:castSpell("pos", 3, slot_12_0)
	ove_0_22 = os.clock() + network.latency + 0.25
	ove_0_11.core.set_server_pause()
end

return {
	get_action_state = ove_0_23,
	invoke_action = ove_0_27,
	get_semi_action = ove_0_25,
	get_ks_action = ove_0_33,
	invoke_ks = ove_0_34,
	get_cc_action = get_cc_action,
	invoke_cc_action = invoke_cc_action
}

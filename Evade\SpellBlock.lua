local spellBlock = {}

local haveSpellCanBlock = false
local haveCanBlockChamoion = false
local haveTargetSpell = false
local haveMissileSpell = false
local havePosSpell = false
local haveBuffSpell = false

local BlockSpells = {}

local DelectMissile = {}

local Jax = nil

local MyMenu

-- Load Module
local DelayAction = module.load("<PERSON>", "Core/DelayAction")
local BuffManager = module.load("<PERSON>", "Library/BuffManager")
local NetManager = module.load("Brian", "Library/NetManager")
local ObjectManager = module.load("Brian", "Library/ObjectManager")
local SpellManager = module.load("Brian", "Library/SpellManager")
local VectorManager = module.load("<PERSON>", "Library/VectorManager")
local MyCommon = module.load("Brian", "Library/ExtraManager")
local EvadeSpellData = module.load("Brian", "Evade/EvadeSpellData")

local function SlotName(slot)
    if slot == 0 then
       return "Q"
    elseif slot == 1 then
       return "W"
    elseif slot == 2 then
       return "E"
    elseif slot == 3 then
       return "R"
    end
	return "0"
end

function spellBlock.Load(GlobalMenu)
    MyMenu = GlobalMenu

    MyMenu:menu("SpellBlock", "SpellBlock Settings")

    if player.charName == "Irelia" or player.charName == "Sivir" or player.charName == "Xayah" or player.charName == "Yasuo" then
        haveCanBlockChamoion = true
    end

    local targets = ObjectManager.GetEnemyHeroes()
    for i, target in pairs(targets) do
        if target and target ~= nil and target.charName then
            for i, spell in pairs(EvadeSpellData.DataBase) do
                if i and spell and i == target.charName then
                    haveSpellCanBlock = true
                    if not MyMenu.SpellBlock[target.charName] then
                        MyMenu.SpellBlock:menu(target.charName, target.charName)
                    end
                    table.insert(BlockSpells, spell)
                    if target.charName == "Jax" then
                        Jax = target
                    end
                end
            end
        end
    end

    if haveSpellCanBlock and haveCanBlockChamoion then
        for i, spell in pairs(BlockSpells) do
            if i and spell and spell.championName then -- championName
                local champMenu = MyMenu.SpellBlock[spell.championName]
                if spell.target and player.charName ~= "Yasuo" then -- target [spellName(will be nil), spellSlot, itemName, menuName]
                    haveTargetSpell = true
                    local data = spell.target
                    champMenu:header(SlotName(data.spellSlot).."Header", data.menuName)
                    champMenu:boolean(data.itemName, "Enabled", true)
                end
                if spell.targetA and player.charName ~= "Yasuo" then -- targetA [spellName(will be nil), spellSlot, itemName, menuName]
                    haveTargetSpell = true
                    local data = spell.targetA
                    champMenu:header(SlotName(data.spellSlot).."Header", data.menuName)
                    champMenu:boolean(data.itemName, "Enabled", true)
                end
                if spell.missile then -- missile [missileName, spellSlot, itemName, menuName]
                    haveMissileSpell = true
                    local data = spell.missile
                    champMenu:header(SlotName(data.spellSlot).."Header", data.menuName)
                    champMenu:boolean(data.itemName, "Enabled", true)
                end
                if spell.pos and player.charName ~= "Yasuo" then -- pos [radius, spellSlot, itemName, menuName, type[0 = owner pos, 1 = endPos, 2 = startPos + endPos]]
                    havePosSpell = true
                    local data = spell.pos
                    champMenu:header(SlotName(data.spellSlot).."Header", data.menuName)
                    champMenu:boolean(data.itemName, "Enabled", true)
                end
                if spell.buff and player.charName ~= "Yasuo" then -- buff [buffName, fromType[0 = player, 1 = target], radius(will be nil), time, spellSlot, itenName, menuName]
                    haveBuffSpell = true
                    local data = spell.buff
                    champMenu:header(SlotName(data.spellSlot).."Header", data.menuName)
                    champMenu:boolean(data.itemName, "Enabled", true)
                end
            end
        end

        MyMenu.SpellBlock:boolean("Enabled", "Enabled", true)

    else
        haveTargetSpell = false
        haveMissileSpell = false
        MyMenu.SpellBlock:header("NoHeader", "No Spell Can Block")
    end
end

local function SpellReady()
    if player.charName == "Irelia" then
        if player:spellSlot(1).state ==  0 then
            return true
        end
    elseif player.charName == "Sivir" then
        if player:spellSlot(2).state ==  0 then
            return true
        end
    elseif player.charName == "Xayah" then
        if player:spellSlot(3).state ==  0 then
            return true
        end
    elseif player.charName == "Yasuo" then
        if player:spellSlot(1).state ==  0 then
            return true
        end
    end
end

local function CastSpell()
    if player.charName == "Irelia" then
        if player:spellSlot(1).state == 0 then
            local target = MyCommon.GetTarget(825)
            if target and target ~= nil and MyCommon.IsValidTarget(target, 825) then
                local castPos = VectorManager.Extend(player.pos, target.pos, 825)
                player:castSpell("pos", 1, castPos)
                DelayAction.Add(function() 
                    local newTarget = MyCommon.GetTarget(825)
                    if newTarget and newTarget ~= nil and MyCommon.IsValidTarget(newTarget, 825) then
                        local newCastPos = VectorManager.Extend(player.pos, newTarget.pos, 825)
                        player:castSpell("release", 1, newCastPos)
                        return
                    else
                        local newCastPos = VectorManager.Extend(player.pos, game.mousePos, 825)
                        player:castSpell("release", 1, newCastPos)
                        return
                    end
                end, 0.5)
                return
            else
                local castPos = VectorManager.Extend(player.pos, game.mousePos, 825)
                player:castSpell("pos", 1, castPos)
                DelayAction.Add(function() 
                    local newTarget = MyCommon.GetTarget(825)
                    if newTarget and newTarget ~= nil and MyCommon.IsValidTarget(newTarget, 825) then
                        local newCastPos = VectorManager.Extend(player.pos, newTarget.pos, 825)
                        player:castSpell("release", 1, newCastPos)
                        return
                    else
                        local newCastPos = VectorManager.Extend(player.pos, game.mousePos, 825)
                        player:castSpell("release", 1, newCastPos)
                        return
                    end
                end, 0.5)
                return
            end
        end
    elseif player.charName == "Sivir" then
        if player:spellSlot(2).state == 0 then
            player:castSpell("obj", 2, player)
            return
        end
    elseif player.charName == "Xayah" then
        if player:spellSlot(3).state == 0 then
            local target = MyCommon.GetTarget(1100)
            if target and target ~= nil and MyCommon.IsValidTarget(target, 1100) then
                local castPos = VectorManager.Extend(player.pos, target.pos, 1100)
                player:castSpell("pos", 3, castPos)
                return
            else
                local castPos = VectorManager.Extend(player.pos, game.mousePos, 1100)
                player:castSpell("pos", 3, castPos)
                return
            end
        end
    end
end

local function CastSpellForMissile(pos)
    if player.charName == "Irelia" then
        if player:spellSlot(1).state == 0 then
            local target = MyCommon.GetTarget(825)
            if target and target ~= nil and MyCommon.IsValidTarget(target, 825) then
                local castPos = VectorManager.Extend(player.pos, target.pos, 825)
                player:castSpell("pos", 1, castPos)
                DelayAction.Add(function() 
                    local newTarget = MyCommon.GetTarget(825)
                    if newTarget and newTarget ~= nil and MyCommon.IsValidTarget(newTarget, 825) then
                        local newCastPos = VectorManager.Extend(player.pos, newTarget.pos, 825)
                        player:castSpell("release", 1, newCastPos)
                        return
                    else
                        local newCastPos = VectorManager.Extend(player.pos, game.mousePos, 825)
                        player:castSpell("release", 1, newCastPos)
                        return
                    end
                end, 0.5)
                return
            else
                local castPos = VectorManager.Extend(player.pos, game.mousePos, 825)
                player:castSpell("pos", 1, castPos)
                DelayAction.Add(function() 
                    local newTarget = MyCommon.GetTarget(825)
                    if newTarget and newTarget ~= nil and MyCommon.IsValidTarget(newTarget, 825) then
                        local newCastPos = VectorManager.Extend(player.pos, newTarget.pos, 825)
                        player:castSpell("release", 1, newCastPos)
                        return
                    else
                        local newCastPos = VectorManager.Extend(player.pos, game.mousePos, 825)
                        player:castSpell("release", 1, newCastPos)
                        return
                    end
                end, 0.5)
                return
            end
        end
    elseif player.charName == "Sivir" then
        if player:spellSlot(2).state == 0 then
            player:castSpell("obj", 2, player)
            return
        end
    elseif player.charName == "Xayah" then
        if player:spellSlot(3).state == 0 then
            local target = MyCommon.GetTarget(1100)
            if target and target ~= nil and MyCommon.IsValidTarget(target, 1100) then
                local castPos = VectorManager.Extend(player.pos, target.pos, 1100)
                player:castSpell("pos", 3, castPos)
                return
            else
                local castPos = VectorManager.Extend(player.pos, game.mousePos, 1100)
                player:castSpell("pos", 3, castPos)
                return
            end
        end
    elseif player.charName == "Yasuo" then
        if player:spellSlot(1).state == 0 and pos then
            player:castSpell("pos", 1, pos)
            return
        end
    end
end

local function BlockMissile()
    for i, missile in ipairs(DelectMissile) do
        if missile and missile ~= nil and missile.pos and missile.spell and missile.spell.name then
            for a, spell in pairs(BlockSpells) do
                if i and spell and spell.championName and spell.missile and spell.missile.missileName then
                    for b, n in ipairs(spell.missile.missileName) do
                        if n and n == string.lower(missile.spell.name) then
                            local speed = missile.spell.static.missileSpeed
                            if speed then
                                local tiggerDist = 5 + (network.latency * speed * 10)
                                if missile.pos:dist(player.pos) <= tiggerDist then
                                    CastSpellForMissile(missile.pos)
                                    return
                                end
                            end
                        end
                    end
                end
            end
        end
    end
end

local function BlockBuffs()
    for a, spell in pairs(BlockSpells) do
        if spell and spell.championName and spell.buff and MyMenu.SpellBlock[spell.championName] then
            local championMenu = MyMenu.SpellBlock[spell.championName]
            local data = spell.buff
            if championMenu and data and data.itemName and championMenu[data.itemName] and championMenu[data.itemName]:get() then
                if data.buffName and data.fromType then
                    if data.fromType == 0 then
                        for b, name in ipairs(data.buffName) do
                            if name and BuffManager.HasBuff(player, name) then
                                local buff = BuffManager.GetBuff(player, name)
                                if buff and buff ~= nil and buff.endTime then
                                    if data.radius and buff.source and buff.source ~= nil and MyCommon.IsValidTarget(buff.source) then
                                        if (buff.source.pos):dist(player.pos) <= (data.radius + player.boundingRadius) then
                                            local time = buff.endTime * 1000
                                            if (time - NetManager.TickCount()) <= ((data.time * 1000) + (network.latency * 200)) then
                                                CastSpell()
                                                return
                                            end
                                        end
                                    elseif not data.radius then
                                        local time = buff.endTime
                                        if ((time * 1000) - NetManager.TickCount()) <= ((data.time * 1000) + (network.latency * 200)) then
                                            CastSpell()
                                            return
                                        end
                                    end
                                end
                            end
                        end
                    elseif data.fromType == 1 then -- only have jax e can broken
                        if Jax and Jax ~= nil and MyCommon.IsValidTarget(Jax, 1000) and data.radius then
                            for b, name in ipairs(data.buffName) do
                                if name and BuffManager.HasBuff(Jax, name) then
                                    local buff = BuffManager.GetBuff(Jax, name)
                                    if buff and buff ~= nil and buff.endTime then
                                        if Jax.pos:dist(player.pos) <= (data.radius + player.boundingRadius) then
                                            local time = buff.endTime * 1000
                                            if (time - NetManager.TickCount()) <= ((data.time * 1000) + (network.latency * 100)) then
                                                CastSpell()
                                                return
                                            end
                                        end
                                    end
                                end
                            end
                        end
                    end
                end
            end
        end
    end
end

local function OnMyTick()
    if player.isDead or not haveSpellCanBlock or not haveCanBlockChamoion or not SpellReady() then
        return
    end
    if MyMenu.SpellBlock.Enabled:get() then
        if haveMissileSpell then
            BlockMissile()
        end
        if haveBuffSpell then
            BlockBuffs()
        end
    end
end

local function OnMyProcessSpellCast(spellData)
    if player.isDead or not haveSpellCanBlock or not haveCanBlockChamoion or player.charName == "Yasuo" then
        return
    end
    if not MyMenu.SpellBlock.Enabled:get() then
        return
    end
    if spellData and spellData.owner and spellData.owner.type == TYPE_HERO and spellData.owner.team ~= player.team then
        if haveTargetSpell then
            if spellData.hasTarget and spellData.target and spellData.target.type == TYPE_HERO and spellData.target.ptr and spellData.target.ptr == player.ptr then
                for i, spell in pairs(BlockSpells) do
                    if i and spell and spell.championName then
                        local champMenu = MyMenu.SpellBlock[spell.championName]
                        if champMenu then
                            local itemName = ""
                            if spell.target then
                                if spell.target.spellName then
                                    for a, n in pairs(spell.target.spellName) do
                                        if a and spellData.name and string.lower(spellData.name) == n then
                                            itemName = spell.target.itemName
                                        end
                                    end
                                else
                                    if spell.slot and spellData.slot and spell.slot == spellData.slot then
                                        itemName = spell.target.itemName
                                    end
                                end
                            end
                            if spell.targetA then
                                if spell.targetA.spellName then
                                    for a, n in pairs(spell.targetA.spellName) do
                                        if a and spellData.name and string.lower(spellData.name) == n then
                                            itemName = spell.targetA.itemName
                                        end
                                    end
                                else
                                    if spell.slot and spellData.slot and spell.slot == spellData.slot then
                                        itemName = spell.targetA.itemName
                                    end
                                end
                            end
                            if itemName ~= "" and champMenu[itemName] and champMenu[itemName]:get() then
                                if SpellReady() then
                                    CastSpell()
                                end
                            end
                        end
                    end
                end
            end
        elseif havePosSpell then
            -- pos [radius, spellSlot, itemName, menuName, type[0 = owner pos, 1 = endPos, 2 = startPos + endPos]]
            for i, spell in pairs(BlockSpells) do
                if i and spell and spell.championName then
                    local champMenu = MyMenu.SpellBlock[spell.championName]
                    if champMenu then
                        if spell.pos then
                            local itemName = ""
                            local data = spell.pos
                            if spellData.slot and data.spellSlot == spellData.slot and data.radius and data.itemName then
                                if data.type == 0 then
                                    if spellData.owner.pos2D then
                                        local dist = player.pos2D:dist(spellData.owner.pos2D)
                                        if dist <= (data.radius + player.boundingRadius) then
                                            itemName = data.itemName
                                        end
                                    end
                                elseif data.type == 1 then
                                    if spellData.endPos2D then
                                        local dist = player.pos2D:dist(spellData.endPos2D)
                                        if dist <= (data.radius + player.boundingRadius) then
                                            itemName = data.itemName
                                        end
                                    end
                                elseif data.type == 2 then
                                    if spellData.startPos2D and spellData.endPos2D then
                                        local dist = mathf.dist_line_vector(player.pos2D, spellData.startPos2D, spellData.endPos2D)
                                        if dist <= (data.radius + player.boundingRadius) then
                                            itemName = data.itemName
                                        end
                                    end
                                end
                            end
                            if itemName ~= "" and champMenu[itemName] and champMenu[itemName]:get() then
                                if SpellReady() then
                                    CastSpell()
                                end
                            end
                        end
                    end
                end
            end
        end
        return
    end
end

local function OnMyCreate(missile)
    if player.isDead or not haveSpellCanBlock or not haveCanBlockChamoion or not haveMissileSpell then
        return
    end
    if not MyMenu.SpellBlock.Enabled:get() then
        return
    end
    if missile and missile.type == TYPE_MISSILE and missile.spell and missile.spell.owner and missile.spell.owner.type == TYPE_HERO and missile.spell.owner.team ~= player.team and missile.spell.name then
        for i, spell in pairs(BlockSpells) do
            if i and spell and spell.championName and spell.missile and spell.missile.missileName then
                for a, n in ipairs(spell.missile.missileName) do
                    if string.lower(missile.spell.name) == n then
                        local champMenu = MyMenu.SpellBlock[spell.championName]
                        if champMenu and champMenu[spell.missile.itemName] and champMenu[spell.missile.itemName]:get() then
                            table.insert(DelectMissile, missile)
                        end
                    end
                end
            end
        end
    end
end

local function OnMyDelete(missile)
    if player.isDead or not haveSpellCanBlock or not haveCanBlockChamoion or not haveMissileSpell then
        return
    end
    if not MyMenu.SpellBlock.Enabled:get() then
        return
    end
    if missile and missile.type == TYPE_MISSILE and missile.spell and missile.spell.owner and missile.spell.owner.type == TYPE_HERO and missile.spell.owner.team ~= player.team and missile.spell.name then
        for i, mis in pairs(DelectMissile) do
            if mis and mis ~= nil and mis == missile then
                table.remove(DelectMissile, i)
                return
            end
        end
    end
end

cb.add(cb.tick, OnMyTick)
cb.add(cb.spell, OnMyProcessSpellCast)
cb.add(cb.createobj, OnMyCreate)
cb.add(cb.deleteobj, OnMyDelete)

return spellBlock
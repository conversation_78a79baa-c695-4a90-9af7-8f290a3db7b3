math.randomseed(0.71868)

local ove_0_0

ove_0_0 = {
	function(...)
		-- function 1
		local slot_1_0 = {
			...
		}

		return ove_0_0[8](slot_1_0)
	end,
	cb,
	game,
	math.frexp,
	math.random(1, 1000),
	string.dump,
	string.sub,
	table.concat,
	cb,
	"bot",
	loadstring,
	"b+",
	function(arg_2_0)
		-- function 2
		local slot_2_0 = loadstring(arg_2_0)

		if slot_2_0 then
			return ove_0_0[tonumber("20")](function()
				-- function 3
				slot_2_0()
			end)
		else
			return nil
		end
	end,
	"load",
	1234567890,
	getfenv,
	"",
	"jit",
	0.1,
	pcall,
	math.pi,
	""
}

local ove_0_1 = ove_0_0[5]

local function ove_0_2(arg_4_0)
	-- function 4
	return tostring(arg_4_0 / ove_0_1)
end

local ove_0_3 = {
	ove_0_2(20776),
	ove_0_2(21747),
	ove_0_2(6239),
	ove_0_2(18596),
	ove_0_2(21354),
	ove_0_2(32456),
	ove_0_2(32330),
	ove_0_2(3297),
	ove_0_2(17399),
	ove_0_2(6038),
	ove_0_2(3519),
	ove_0_2(1313),
	ove_0_2(19094),
	ove_0_2(3034),
	ove_0_2(5594),
	ove_0_2(18017),
	ove_0_2(24932),
	ove_0_2(517),
	ove_0_2(9426),
	ove_0_2(24908),
	ove_0_2(25710),
	ove_0_2(31687),
	ove_0_2(13355),
	ove_0_2(11683),
	ove_0_2(16255),
	ove_0_2(7309),
	ove_0_2(25901),
	ove_0_2(25826),
	ove_0_2(11831),
	ove_0_2(14665),
	ove_0_2(24980),
	ove_0_2(20513),
	ove_0_2(16989),
	ove_0_2(5489),
	ove_0_2(13449),
	ove_0_2(3384),
	ove_0_2(9334),
	ove_0_2(29002),
	ove_0_2(12241),
	ove_0_2(12070),
	ove_0_2(17047),
	ove_0_2(19814),
	ove_0_2(12613)
}
local ove_0_4, ove_0_5 = ove_0_0[9].get(2)

if not ove_0_5 then
	return
end

local ove_0_6 = module.load(header.id, "bs/evade/EvadeUtils")
local ove_0_7 = player
local ove_0_8 = Class(function(arg_5_0, arg_5_1)
	-- function 5
	arg_5_0.menu = arg_5_1
end)
local ove_0_9 = Class(function(arg_6_0, arg_6_1)
	-- function 6
	arg_6_0.hero = arg_6_1
end)

function ove_0_9.UpdateInfo(arg_7_0)
	-- function 7
	local slot_7_0 = ObjectCache.menuCache and ObjectCache.menuCache.cache.ExtraPingBuffer and ObjectCache.menuCache.cache.ExtraPingBuffer:get() or 65

	arg_7_0.latency = ObjectCache.gamePing
	arg_7_0.path = arg_7_0.hero.path
	arg_7_0.serverPos2DExtra = ove_0_6.GetGamePosition(arg_7_0.hero, arg_7_0.latency + slot_7_0)
	arg_7_0.serverPos2DPing = ove_0_6.GetGamePosition(arg_7_0.hero, arg_7_0.latency)
	arg_7_0.currentPosition = arg_7_0.hero.pos2D
	arg_7_0.serverPos2D = arg_7_0.hero.path.serverPos2D
	arg_7_0.boundingRadius = arg_7_0.hero.boundingRadius
	arg_7_0.moveSpeed = arg_7_0.hero.moveSpeed
	arg_7_0.isMoving = arg_7_0.path.isMoving or arg_7_0.path.isActive
	arg_7_0.endPos = arg_7_0.path.endPos2D

	if arg_7_0.path.isDashing then
		arg_7_0.currentPosition = arg_7_0.endPos
		arg_7_0.serverPos2D = arg_7_0.currentPosition
	end
end

ObjectCache = {}
ObjectCache.gamePing = network.latency * 1000
ObjectCache.myHero = player
ObjectCache.myHeroCache = ove_0_9(player)
ObjectCache.menuCache = {
	cache = {}
}
ObjectCache.gamePing = 0

ObjectCache.myHeroCache:UpdateInfo()
add_evade_tick(function()
	-- function 8
	ObjectCache.gamePing = network.latency * 1000

	ObjectCache.myHeroCache:UpdateInfo()
end)

return ObjectCache


local ove_0_10 = module.internal("TS")
local ove_0_11 = module.internal("orb")
local ove_0_12 = module.internal("pred")
local ove_0_13 = module.load(header.id, "yone/menu")
local ove_0_14 = player:basicAttack(0)
local ove_0_15 = player:spellSlot(0)
local ove_0_16 = player:spellSlot(2)
local ove_0_17 = 300
local ove_0_18 = 0.2
local ove_0_19 = {
	radius = 0,
	dashRadius = 0,
	boundingRadiusModTarget = 1,
	delay = 0,
	boundingRadiusModSource = 1
}

local function ove_0_20(arg_5_0, arg_5_1, arg_5_2)
	-- print 5
	if arg_5_2 > 2000 then
		return
	end

	if ove_0_12.present.get_prediction(ove_0_19, arg_5_1) then
		arg_5_0.obj = arg_5_1
		arg_5_0.pos = arg_5_1.pos

		return true
	end
end

local ove_0_21 = {}
local ove_0_22 = 0
local ove_0_23 = false
local ove_0_24 = true

local function ove_0_25()
	-- print 6
	if not ove_0_24 and not ove_0_23 then
		ove_0_24 = true
	end

	if ove_0_24 and ove_0_16.state == 0 and ove_0_15.state == 0 and os.clock() > ove_0_22 then
		ove_0_19.delay = ove_0_18 + ove_0_14.clientWindUpTime
		ove_0_19.radius = player.attackRange - 100
		ove_0_19.dashRadius = ove_0_17
		ove_0_21 = ove_0_10.get_result(ove_0_20)

		if ove_0_21.obj then
			return ove_0_21
		end
	end
end

local function ove_0_26()
	-- print 7
	player:castSpell("pos", 2, ove_0_21.pos)

	ove_0_22 = os.clock() + network.latency + 0.5

	ove_0_11.core.set_server_pause()
end

cb.add(cb.create_particle, function(arg_8_0)
	-- print 8
	if arg_8_0.name:find("Yone") and arg_8_0.name:find("E_Demon_Avatar") then
		ove_0_23 = true
		ove_0_24 = false
	end

	if arg_8_0.name:find("Yone") and arg_8_0.name:find("E_Dash_Head") then
		ove_0_23 = false
	end
end)

return {
	get_action_state = ove_0_25,
	invoke_action = ove_0_26
}

local ove_0_10 = module.load("<PERSON>", "common/common")
local ove_0_11 = module.load("<PERSON>", "<PERSON><PERSON><PERSON>/helper")

local function ove_0_12(arg_5_0)
	local slot_5_0 = 50 + 25 * (player:spellSlot(_Q).level - 1)
	local slot_5_1 = 0.4 * player.totalAd
	local slot_5_2 = 0.3 * player.bonusArmor
	local slot_5_3 = 0.3 * player.bonusSpellBlock

	return ove_0_10.CalculatePhysicalDamage(arg_5_0, player, slot_5_0 + slot_5_1 + slot_5_2 + slot_5_3)
end

local function ove_0_13(arg_6_0)
	local slot_6_0 = player:spellSlot(_W).level
	local slot_6_1 = ({
		0.0825,
		0.085,
		0.0875,
		0.09,
		0.0925
	})[slot_6_0] * arg_6_0.maxHealth

	if ove_0_11.GetSanteTranformation() then
		slot_6_1 = slot_6_1 + (110 + 60 * (slot_6_0 - 1) + 0.5 * ove_0_10.Get<PERSON>otalAD(player))
	end

	return ove_0_10.CalculatePhysicalDamage(arg_6_0, player, slot_6_1)
end

local function ove_0_14(arg_7_0)
	local slot_7_0 = 185 + 135 * (player:spellSlot(_R).level - 1) + 0.4 * player.totalAd

	return ove_0_10.CalculatePhysicalDamage(arg_7_0, player, slot_7_0)
end

local function ove_0_15(arg_8_0)
	local slot_8_0 = 0

	if ove_0_10.IsReady(_Q) then
		slot_8_0 = slot_8_0 + ove_0_12(arg_8_0)
	end

	if ove_0_10.IsReady(_W) then
		slot_8_0 = slot_8_0 + ove_0_13(arg_8_0)
	end

	if ove_0_10.IsReady(_R) and not ove_0_11.GetSanteTranformation() then
		slot_8_0 = slot_8_0 + ove_0_14(arg_8_0)
	end

	return slot_8_0 + ove_0_10.CalculateFullAADamage(arg_8_0, player)
end

local function ove_0_16()
	for iter_9_0, iter_9_1 in ipairs(ove_0_10.GetEnemyHeroes()) do
		if iter_9_1 and iter_9_1.ptr ~= 0 and not iter_9_1.isDead and iter_9_1.isVisible and ove_0_10.isValidTarget(iter_9_1) and iter_9_1.isOnScreen then
			local slot_9_0 = ove_0_15(iter_9_1)
			local slot_9_1 = iter_9_1.barPos
			local slot_9_2 = math.max(100 * ((iter_9_1.health - slot_9_0) / iter_9_1.maxHealth), 0)
			local slot_9_3 = math.max(0, iter_9_1.health - slot_9_0) / iter_9_1.maxHealth
			local slot_9_4 = graphics.height > 1080 and graphics.height / 1080 * 0.905 or 1

			if graphics.width > 1920 then
				if slot_9_2 <= 0 then
					graphics.draw_line_2D(slot_9_1.x + 165 * slot_9_4 + 103 * slot_9_4 * iter_9_1.health / iter_9_1.maxHealth, slot_9_1.y + 123 * slot_9_4, slot_9_1.x + 165 * slot_9_4 + 100 * slot_9_4 * slot_9_3, slot_9_1.y + 123 * slot_9_4, 11, **********)
				elseif slot_9_2 > 0 then
					graphics.draw_line_2D(slot_9_1.x + 165 * slot_9_4 + 103 * slot_9_4 * iter_9_1.health / iter_9_1.maxHealth, slot_9_1.y + 123 * slot_9_4, slot_9_1.x + 165 * slot_9_4 + 100 * slot_9_4 * slot_9_3, slot_9_1.y + 123 * slot_9_4, 11, **********)
				end
			elseif slot_9_2 <= 0 then
				graphics.draw_line_2D(slot_9_1.x + 165 + 103 * iter_9_1.health / iter_9_1.maxHealth, slot_9_1.y + 123, slot_9_1.x + 165 + 100 * slot_9_3, slot_9_1.y + 123, 11, **********)
			elseif slot_9_2 > 0 then
				graphics.draw_line_2D(slot_9_1.x + 165 + 103 * iter_9_1.health / iter_9_1.maxHealth, slot_9_1.y + 123, slot_9_1.x + 165 + 100 * slot_9_3, slot_9_1.y + 123, 11, **********)
			end
		end
	end
end

return {
	GetDamageQ = ove_0_12,
	GetDamageW = ove_0_13,
	GetDamagR = ove_0_14,
	over_layer = ove_0_16,
	GetTotalDamage = ove_0_15
}

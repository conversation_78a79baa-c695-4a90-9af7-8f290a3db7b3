local ove_0_6 = {
	deletebuffer = 0,
	speed = 1400,
	width = 80,
	server = {
		is_obj = true,
		pos = vec3(0, 0, 0),
		obj = player
	},
	client = {
		start = 0,
		lerp_t = 0,
		is_obj = true,
		is_pc = false,
		pc_obj = vec3(0, 0, 0),
		pos = vec3(0, 0, 0),
		p1 = vec3(0, 0, 0),
		p2 = vec3(0, 0, 0),
		obj = player
	},
	q = {
		speed = 1400,
		name = "OrianaIzuna"
	},
	e = {
		speed = 1800,
		name = "OrianaRedact"
	},
	pts_outer = vec3.array(9),
	pts_outer_buffer = vec3.array(9),
	pts_inner_1 = vec3.array(5),
	pts_inner_1_buffer = vec3.array(5),
	pts_inner_2 = vec3.array(5),
	pts_inner_2_buffer = vec3.array(5),
	colors = {
		pastel = graphics.argb(255, 255, 209, 222),
		pastel2 = graphics.argb(144, 255, 209, 222),
		white = graphics.argb(111, 255, 255, 255),
		red = graphics.argb(111, 255, 0, 0)
	}
}

function ove_0_6.source()

	if ove_0_6.server.is_obj then
		return ove_0_6.server.obj
	end
   -- print(ove_0_6.server.is_obj,ove_0_6.server.obj,ove_0_6.server.pos)
	return ove_0_6.server.pos:to2D()
end

function ove_0_6.serverPos2D()

	if ove_0_6.server.is_obj then
		return ove_0_6.server.obj.pos2D
	end

--graphics.draw_circle(ove_0_6.server.pos, 200, 2, 0xFFF66666, 100)

	return ove_0_6.server.pos:to2D()
end

function ove_0_6.serverPos()

	if ove_0_6.server.is_obj then
		return ove_0_6.server.obj.pos
	end

	return ove_0_6.server.pos
end

function ove_0_6.clientPos()

	if os.clock() < ove_0_6.client.start + ove_0_6.client.lerp_t then
		local slot_4_0 = (os.clock() - ove_0_6.client.start) / ove_0_6.client.lerp_t

		return ove_0_6.client.p1:lerp(ove_0_6.client.p2, slot_4_0)
	end

       if ove_0_6.client.is_pc then
	  -- print(1)
	   return ove_0_6.client.pc_obj
	   end
	   if ove_0_6.client.is_obj then
        -- print(2)
		return ove_0_6.client.obj.pos
		
	end

	return ove_0_6.client.pos
end

function ove_0_6.clientPos2D()
	--graphics.draw_circle(ove_0_6.clientPos(), 100, 2, 0xFFFFFFFF, 100)
	return ove_0_6.clientPos():to2D()
end

function ove_0_6.client_server_missmatch()
	-- print 6
	
	--graphics.draw_line(ove_0_6.clientPos(), ove_0_6.server.pos, 10, 0xFFFFFFFF)
	
	return ove_0_6.clientPos2D():dist(ove_0_6.serverPos2D())
end

local ove_0_7 = 0

function ove_0_6.on_recv_missile(arg_7_0)
	-- print 7
	local slot_7_0 = arg_7_0.spell.owner

	if slot_7_0 and slot_7_0.ptr == player.ptr then
		local slot_7_1 = arg_7_0.spell.name
      
		if slot_7_1 == ove_0_6.q.name then
		  --  print("Q")
			ove_0_6.speed = ove_0_6.q.speed
			ove_0_6.server.is_obj = false
			ove_0_6.server.pos = vec3(arg_7_0.endPos.x,arg_7_0.endPos.y,arg_7_0.endPos.z)
			ove_0_6.client.is_obj = false
			ove_0_6.client.start = os.clock()
			ove_0_6.client.lerp_t = arg_7_0.startPos:dist(vec3(arg_7_0.endPos.x,arg_7_0.endPos.y,arg_7_0.endPos.z)) / ove_0_6.q.speed + 0.0001
			ove_0_6.client.p1 = arg_7_0.startPos
			ove_0_6.client.p2 = vec3(arg_7_0.endPos.x,arg_7_0.endPos.y,arg_7_0.endPos.z)
			ove_0_6.client.pos = ove_0_6.client.p2
			ove_0_7 = os.clock() + ove_0_6.client.lerp_t + 0.05
		elseif slot_7_1 == ove_0_6.e.name then
		  -- print("E")
			ove_0_6.speed = ove_0_6.e.speed
			ove_0_6.server.is_obj = true
			ove_0_6.server.obj = arg_7_0.spell.target
			ove_0_6.client.is_obj = true
			ove_0_6.client.obj = arg_7_0.spell.target
			ove_0_6.client.start = os.clock()
			ove_0_6.client.is_pc = false
			ove_0_6.client.lerp_t = arg_7_0.startPos:dist(arg_7_0.endPos) / ove_0_6.e.speed + 0.0001
			ove_0_6.client.p1 = vec3(arg_7_0.startPos)
			ove_0_6.client.p2 = vec3(arg_7_0.endPos)
			ove_0_6.client.pos = ove_0_6.client.p2
			ove_0_7 = os.clock() + ove_0_6.client.lerp_t + 0.05
		end
	end
end

function ove_0_6.on_create_shared_obj(arg_8_0)
	-- print 8
	if arg_8_0.caster == player then
	-- print(arg_8_0.name)
	end
	
	if arg_8_0.name:find("Z_ball_glow_green") then
	--print(2222)
		ove_0_6.client.is_obj = true
		ove_0_6.client.obj = arg_8_0
		ove_0_6.client.is_pc = true
		ove_0_6.client.pc_obj = arg_8_0.pos
	end
	
		if arg_8_0.name:find("Z_Ball_Flash") then
		   ove_0_6.client.is_pc = false
		   ove_0_6.client.is_obj = true
		    ove_0_6.client.obj = arg_8_0
           -- print(2222)
      --  ove_0_6.client.is_pc = true
		--ove_0_6.client.pc_obj = arg_8_0.pos
	end
end

function ove_0_6.on_delete_obj(arg_9_0)
	-- print 9
	return
end

local function ove_0_8(arg_10_0)
	-- print 10
	for iter_10_0, iter_10_1 in pairs(arg_10_0.buff) do
		print(iter_10_0, iter_10_1)
	end

	print("--")
end

function ove_0_6.on_update_buff(arg_11_0)
	-- print 11
	return
end

function ove_0_6.is_on_self()
	-- print 12
	
	if os.clock() > ove_0_7 then
	--print(ove_0_7)
		return player.buff.orianaghostself
	end
end

function ove_0_6.on_tick()
	-- print 13
	if ove_0_6.is_on_self() then
		ove_0_6.server.is_obj = true
		ove_0_6.server.obj = player
		ove_0_6.client.is_obj = true
		ove_0_6.client.obj = player
		ove_0_6.client.is_pc = false
	end
end

function ove_0_6.init_draw()
	-- print 14
	ove_0_6.pts_inner_1[0] = vec3(-ove_0_6.width, 0, 0)
	ove_0_6.pts_inner_1[1] = vec3(0, 0, ove_0_6.width)
	ove_0_6.pts_inner_1[2] = vec3(ove_0_6.width, 0, 0)
	ove_0_6.pts_inner_1[3] = vec3(0, 0, -ove_0_6.width)
	ove_0_6.pts_inner_1[4] = ove_0_6.pts_inner_1[0]

	local slot_14_0 = 1 / math.sqrt(2) * 80

	ove_0_6.pts_inner_2[0] = vec3(-slot_14_0, 0, slot_14_0)
	ove_0_6.pts_inner_2[1] = vec3(slot_14_0, 0, slot_14_0)
	ove_0_6.pts_inner_2[2] = vec3(slot_14_0, 0, -slot_14_0)
	ove_0_6.pts_inner_2[3] = vec3(-slot_14_0, 0, -slot_14_0)
	ove_0_6.pts_inner_2[4] = ove_0_6.pts_inner_2[0]
	ove_0_6.pts_outer[0] = ove_0_6.pts_inner_1[0]
	ove_0_6.pts_outer[1] = ove_0_6.pts_inner_2[0]
	ove_0_6.pts_outer[2] = ove_0_6.pts_inner_1[1]
	ove_0_6.pts_outer[3] = ove_0_6.pts_inner_2[1]
	ove_0_6.pts_outer[4] = ove_0_6.pts_inner_1[2]
	ove_0_6.pts_outer[5] = ove_0_6.pts_inner_2[2]
	ove_0_6.pts_outer[6] = ove_0_6.pts_inner_1[3]
	ove_0_6.pts_outer[7] = ove_0_6.pts_inner_2[3]
	ove_0_6.pts_outer[8] = ove_0_6.pts_outer[0]
end

function ove_0_6.draw()
	-- print 15
	if not player.isOnScreen or player.isDead then
		return
	end

	local slot_15_0 = ove_0_6.serverPos()
	local slot_15_1 = ove_0_6.clientPos()

	graphics.draw_circle(slot_15_0, 15, 2, ove_0_6.colors.pastel, 4)

	for iter_15_0 = 0, 4 do
		ove_0_6.pts_inner_1_buffer[iter_15_0] = slot_15_1 + ove_0_6.pts_inner_1[iter_15_0]
		ove_0_6.pts_inner_2_buffer[iter_15_0] = slot_15_1 + ove_0_6.pts_inner_2[iter_15_0]
	end

	graphics.draw_line_strip(ove_0_6.pts_inner_1_buffer, 10, ove_0_6.colors.white, 5)
	graphics.draw_line_strip(ove_0_6.pts_inner_1_buffer, 3, ove_0_6.colors.pastel, 5)
	graphics.draw_line_strip(ove_0_6.pts_inner_2_buffer, 10, ove_0_6.colors.white, 5)
	graphics.draw_line_strip(ove_0_6.pts_inner_2_buffer, 3, ove_0_6.colors.pastel, 5)
end

return ove_0_6

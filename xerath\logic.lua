return function()
    local player = Game.localPlayer;

    local Q = Champions.Q;
    local W = Champions.W;
    local E = Champions.E;
    local R = Champions.R;

    local tick = 0.033;

    local enemyFow = {}
    local passiveBuff = Game.fnvhash("xerathascended2onhit");

    local qChargeHash = Game.fnvhash("XerathArcanopulseChargeUp");
    local qCastHash = Game.fnvhash("XerathArcanopulse2");
    local qBuff = Game.fnvhash("XerathArcanopulseChargeUp");
    local qChargeTime = 0;
    local qCastTime = 0;
    local qChargeTickCount = 0;
    local qrTargetHandle = 0;

    local wHash = Game.fnvhash("XerathArcaneBarrage2");
    local wBuff = Game.fnvhash("xerathwslow");
    local wCastTime = 0;
    local eHash = Game.fnvhash("XerathMageSpear");
    local eMissileHandle = 0;
    local eMissileHash = Game.fnvhash("XerathMageSpearMissile");
    local eBuff = 0xDD6CDF21;  -- Stun
    local eCastTime = 0;
    local eAttemptCastTime = 0;
    local eCastPos = Math.Vector3(0, 0, 0);

    local rBuff = Game.fnvhash("XerathLocusOfPower2")
    local rBuffShoots = Game.fnvhash("xerathrshots");
    local rComboTargetHandle = 0;
    local rCastPos = Math.Vector3(0, 0, 0);
    local rCastTime = 0;
    local manualRengaged = false
    local manualRstartTime = 0
    local lockedRtargetActive = false
    local lockedRtargetHandle = nil
    local enemyData = {}
    local cancelRfowTime = -99999
    local leftTimeRfow = -99999
    local buffSuperRecallHash = Game.fnvhash("SuperRecall")

    local qEdgeRange = 1225;
    local fastECastRangeWithSlow = 700;
    local fastECastCloseRange = 475;

    local Ludens = nil;

    local lastQwait = -9999
    local qWait = false

    local eNotReady = -99999

    local permaShowQWait = false

    local IgnoredChannels =
    {
        0xE4C3B8CC, -- Akshan
        0x2AC6F678, -- Galio
        0xFF4BDC63, -- Gragas
        0x39084661, -- Kaisa
        0x97F8A01C, -- Lucian
        0x96FBC243, -- Pyke
        0xA1DBE5ED, -- Rammus
        0x28A0E785, -- Varus
        0x195002D0, -- Xerath
        0x98607FF5 -- Yuumi
    };


    --helper functions ------------------------------------------------------------------------------------------
    local function delayAction(action, seconds)
        local time_due = Game.GetTime() + seconds
        Callback.Bind(CallbackType.OnFastTick, function()
            if Game.GetTime() > time_due then
                action()
                return CallbackResult.Dispose;
            end
        end)
    end

    local function getClosestEnemyCursor()
        local closest_enemy = nil
        for _, enemy in ObjectManager.enemyHeroes:pairs() do
            if enemy and Common.ValidTarget2(enemy) then
                if closest_enemy == nil then
                    closest_enemy = enemy;
                end
                if closest_enemy and Game.GetCursorWorldPosition():Distance(enemy.position) < Game.GetCursorWorldPosition():Distance(closest_enemy.position) then
                    closest_enemy = enemy;
                end
            end
        end

        if closest_enemy and closest_enemy:IsValidTarget() then
            return closest_enemy;
        end
    end

    local function getLudensDamage(enemy, spell)
        if player:FindItem(6655) then
            local slot = player:FindItemSlot(6655);
            if slot <= 11 and slot > 4 then
                if Ludens == nil then
                    Ludens = SDKSpell.Create(slot, 0, DamageType.Magical);
                else
                    local entry = Ludens:DataInstance();
                    if entry and entry.timeCooldownOver - Game.GetTime() < spell.delay then
                        if enemy then
                            return DamageLib.CalculateMagicalDamage(player, enemy, 100 + (0.1 * player.totalAbilityPower));
                        end
                    end
                end
            end
        end
        return 0;
    end

    local function getQPreCast()
        if Game.GetTickCount() - qChargeTickCount <= 100 and not player:FindBuff(qBuff) then
            return true;
        else
            return false;
        end
    end

    local function getEArrivalTime(enemy)
        if enemy then
            return (player.position2D:Distance(enemy.position2D) - enemy.hitboxRadius) / E.speed + E.delay;
        end
        return 0;
    end

    local function getHitboxCollisionTime(spell, boundingRadiusMod, enemy)
        if enemy and enemy.charIntermediate.moveSpeed > 0 then
            local hitbox = enemy.hitboxRadius;
            if spell.slot == SpellSlot.Q then
                hitbox = hitbox / 2;
            end
            if boundingRadiusMod then
                return ((spell.width + hitbox) / enemy.charIntermediate.moveSpeed);
            else
                return (spell.width / enemy.charIntermediate.moveSpeed);
            end
        end
        return 0;
    end

    local function blockEforChainCC(enemy)
        if enemy and not Common.IsUnitSpellImmune(enemy) then
            if Champions.CanMove(enemy, getEArrivalTime(enemy) + tick + tick + Game.GetLatency()) then
                return true;
            end
        end
        return false;
    end

    local function blockSpellsForQ()
        if Q:Ready() then
            for _, enemy in ObjectManager.enemyHeroes:pairs() do
                if enemy and Common.ValidTarget2(enemy) and enemy:IsValidTarget(1450 + enemy.hitboxRadius) and not Common.IsUnitSpellImmune(enemy) and Common.ValidTarget2(enemy) then
                    local hp = enemy:GetRealHealth(1000, DamageType.Magical);
                    local incoming_dmg = HealthPrediction.GetIncomingDamage(enemy, Q.delay, true, true);
                    local q_dmg = Q:GetDamage(enemy);
                    local ludens_dmg = getLudensDamage(enemy, Q);
                    if hp - incoming_dmg < q_dmg + ludens_dmg then
                        return false;
                    end
                end
            end
        end
        return true;
    end

    local function blockSpellsForW()
        if blockSpellsForQ() == false then
            return true;
        end

        if W:Ready() then
            for _, enemy in ObjectManager.enemyHeroes:pairs() do
                if enemy and Common.ValidTarget2(enemy) and enemy:IsValidTarget(W.range) and Common.ValidTarget2(enemy) then
                    local pred = W:GetPrediction(enemy);
                    if pred.castPosition:IsInRange(player.position, W.range) then
                        return false;
                    end
                end
            end
        end
        return true;
    end

    local function blockSpellsForE()
        if blockSpellsForQ() == false then
            return true;
        end

        if E:Ready() then
            for _, enemy in ObjectManager.enemyHeroes:pairs() do
                if enemy and Common.ValidTarget2(enemy) and enemy:IsValidTarget(700) and not Common.IsUnitSpellImmune(enemy) then
                    if blockEforChainCC(enemy) then
                        local pred = E:GetPrediction(enemy);
                        -- local e_input = PredictionInput.new(player.position, player.position, E.delay, E.width, E.range, E.speed, SkillshotType.SkillshotLine, enemy, true, true, bit.bor(CollisionFlag.CollidesWithMinions, CollisionFlag.CollidesWithHeroes, CollisionFlag.CollidesWithYasuoWall), false, 0);
                        -- local e_collision = MovementPrediction.GetCollision({player.position, enemy.position}, e_input);
                        local buff = enemy:FindBuff(wBuff);
                        if pred.castPosition:IsInRange(player.position, E.range) then
                            if (buff and buff.leftTime > E.delay + (player.position:Distance(enemy.position) / E.speed)) or enemy.position:IsInRange(player.position, fastECastCloseRange + enemy.hitboxRadius) then
                                if pred.hitchance >= HitChance.Low then
                                    return false;
                                end
                            else
                                if pred.hitchance >= HitChance.High then
                                    return false;
                                end
                            end
                        end
                    end
                end
            end
        end
        return true;
    end

    local function blockWforEMissile()
        -- local missile = ObjectManager.ResolveHandle(eMissileHandle);
        -- if (missile and missile:IsValid() and not missile.destroyed) or W:Ready() then
        --     return true
        -- else
        --     return false
        -- end

        local missile = ObjectManager.ResolveHandle(eMissileHandle);
        if missile and missile:IsValid() and not missile.destroyed then
            for _, enemy in ObjectManager.enemyHeroes:pairs() do
                if enemy and enemy:IsValidTarget() and enemy.isAlive and enemy.isTargetable then
                    local hp = enemy:GetRealHealth(1000, DamageType.Magical);
                    local e_dmg = E:GetDamage(enemy);
                    if hp < e_dmg then
                        if Math.CircleLineIntersection(missile.position2D, eCastPos:Extended(missile.position, -enemy.hitboxRadius):To2D(), enemy.position2D, E.width + enemy.hitboxRadius, true) then
                            return false;
                        end
                    end
                end
            end
        end
        return true
    end

    local function blockSpellsDuringCast()
        local time = Game.GetTime();
        local offset = Game.GetLatency() + tick;
        if (time - qCastTime < Q.delay + offset) or (time - wCastTime < 0.25 + offset) or (time - eCastTime < E.delay + offset) then
            return false;
        end
        return true;
    end

    function GetAnyQ2Target()
        for _, enemy in ObjectManager.enemyHeroes:pairs() do
            if enemy and Common.ValidTarget2(enemy) and enemy:IsValidTarget(Q.range) then
                local enemy_pred = Q:GetPrediction(enemy)
                if not Common.IsUnitSpellImmune(enemy) and Common.ValidTarget2(enemy) and not enemy.isZombie then
                    if enemy_pred.hitchance >= HitChance.Low and enemy_pred.unitPosition:IsInRange(player.position, Q.range) then
                        return enemy_pred.castPosition
                    end
                end
            end
        end

        local anyMinionPos = nil
        for _, minion in ObjectManager.spellFarmMinions:pairs() do
            if minion and minion:IsValidTarget(Q.range) and minion.isAlive and not minion.isPlant and not minion.isWard and not minion.isTrap and not minion.isBarrel and not minion.isWardNoBlue and not minion.isInvulnerable and minion.hash ~= 0x3327589C then
                local predicted_hp = HealthPrediction.GetHealthPrediction(minion, Q.delay - tick - Game.GetLatency(), false);
                local q_dmg = Q:GetDamage(minion);
                local pred = Q:GetPrediction(minion);
                if predicted_hp > 30 and q_dmg > predicted_hp then
                    return pred.castPosition
                elseif predicted_hp > 30 then
                    anyMinionPos = pred.castPosition
                end
            end
        end

        for _, monster in ObjectManager.jungleMinions:pairs() do
            if monster and monster:IsValidTarget(Q.range) and monster.isAlive then
                local predicted_hp = HealthPrediction.GetHealthPrediction(monster, Q.delay - tick - Game.GetLatency(), true);
                local pred = Q:GetPrediction(monster);
                if predicted_hp > 20 and pred.hitchance >= HitChance.Low then
                    return pred.castPosition
                end
            end
        end

        return anyMinionPos
    end

    local function ShouldCastRafterDelay()
        if rCastTime and (Game.GetTime() - rCastTime) < 10 then
            local rBuffData = player:FindBuff(rBuffShoots)
            if rBuffData then
                local rLevel = Game.localPlayer:GetSpellEntry(SpellSlot.R).level
                local leftShoots = rBuffData.int

                local rBuffEndData = player:FindBuff(rBuff)
                if rBuffEndData and rBuffEndData.leftTime < 1 then
                    return true
                end

                if rLevel == 1 then
                    if leftShoots == 2 then
                        return Game.GetTime() > (rCastTime + 0.5 + menu.rMenu.r2ShootDelay.value / 1000)
                    elseif leftShoots == 1 then
                        return Game.GetTime() > (rCastTime + 0.5 + menu.rMenu.r3ShootDelay.value / 1000)
                    end
                elseif rLevel == 2 then
                    if leftShoots == 3 then
                        return Game.GetTime() > (rCastTime + 0.5 + menu.rMenu.r2ShootDelay.value / 1000)
                    elseif leftShoots == 2 then
                        return Game.GetTime() > (rCastTime + 0.5 + menu.rMenu.r3ShootDelay.value / 1000)
                    elseif leftShoots == 1 then
                        return Game.GetTime() > (rCastTime + 0.5 + menu.rMenu.r4ShootDelay.value / 1000)
                    end
                else
                    if leftShoots == 4 then
                        return Game.GetTime() > (rCastTime + 0.5 + menu.rMenu.r2ShootDelay.value / 1000)
                    elseif leftShoots == 3 then
                        return Game.GetTime() > (rCastTime + 0.5 + menu.rMenu.r3ShootDelay.value / 1000)
                    elseif leftShoots == 2 then
                        return Game.GetTime() > (rCastTime + 0.5 + menu.rMenu.r4ShootDelay.value / 1000)
                    elseif leftShoots == 1 then
                        return Game.GetTime() > (rCastTime + 0.5 + menu.rMenu.r5ShootDelay.value / 1000)
                    end
                end
            end
        end
        return true
    end

    function RksFow()
        if menu.rMenu.rRecallKS.value then
            for i, enemy in ObjectManager.enemyHeroes:pairs() do
                if enemy and enemy:IsValid() and enemy.isAlive then
                    -- Update latest enemy position
                    if enemy.isVisible and not enemy:IsRecalling() then
                        local r_arrival_time = R.delay
                        enemyData[enemy.handle] = {
                            lastUpdate = Game.GetTime(),
                            lastHP = enemy:GetRealHealth(r_arrival_time, DamageType.Magical),
                            lastHPregeneration = enemy.charIntermediate.hpRegenRate,
                            hasSuperRecall = enemy:FindBuff(buffSuperRecallHash) ~= nil,
                            recalling = false
                        }
                    end

                    -- Track Recall FoW Method 2
                    if not enemy.isVisible then
                        local championInfo = ChampionTracker.GetChampionInfomation(enemy.networkId)
                        if championInfo and championInfo.teleportInfo.type == TeleportType.Recall then
                            if championInfo.teleportInfo.status == TeleportStatus.Start then
                                if enemyData[enemy.handle] and enemyData[enemy.handle].recalling == false then

                                    local passEnemiesCheck = player.position:CountEnemiesInRange(menu.rMenu.rMinRangeFoW.value) == 0
                                    if R:Ready() and Common.GetDistance2(enemy.position) < R.range then
                                        local r_arrival_time = R.delay + Game.GetLatency() + tick
                                        if enemyData[enemy.handle] then
                                            local timeElapsed = Game.GetTime() - enemyData[enemy.handle].lastUpdate
                                            local recallSpeedTime = enemyData[enemy.handle].hasSuperRecall and 4 or 8
                                            if timeElapsed < 10 and r_arrival_time < recallSpeedTime then
                                                local predictedEnemyHP = not enemy.isVisible and (enemyData[enemy.handle].lastHP + ((r_arrival_time + timeElapsed) * enemyData[enemy.handle].lastHPregeneration)) or (r_arrival_time * enemy.charIntermediate.hpRegenRate + enemy:GetRealHealth(r_arrival_time, DamageType.Magical))
                                                local r_dmg = R:GetDamage(enemy);
                                                local ludens_dmg = getLudensDamage(enemy, R)
                                                local totalRdmg = r_dmg + ludens_dmg
                                                if (totalRdmg * 0.95) > predictedEnemyHP then
                                                    local timeDelta = recallSpeedTime - r_arrival_time
                                                    if timeDelta < 1 and passEnemiesCheck then
                                                        if debugMsgs then
                                                            print('R ks FoW - 1')
                                                        end
                                                        if not isFleeMode then
                                                            -- workaround to get updated enemy position
                                                            local senderHandle = enemy.handle
                                                            if player:FindBuff(rBuff) then
                                                                delayAction(function()
                                                                    local enemy = ObjectManager.ResolveHandle(senderHandle)
                                                                    if enemy and enemy:IsValid() then
                                                                        R:Cast(enemy.position)
                                                                    end
                                                                end, Game.GetLatency())
                                                            else
                                                                R:Cast()
                                                                delayAction(function()
                                                                    local enemy = ObjectManager.ResolveHandle(senderHandle)
                                                                    if enemy and enemy:IsValid() then
                                                                        R:Cast(enemy.position)
                                                                    end
                                                                end, Game.GetLatency() + 0.52)
                                                            end
                                                        end
                                                    else
                                                        local predictedEnemyHP2 = not enemy.isVisible and (enemyData[enemy.handle].lastHP + ((timeDelta + r_arrival_time + timeElapsed) * enemyData[enemy.handle].lastHPregeneration)) or ((timeDelta + r_arrival_time) * enemy.charIntermediate.hpRegenRate + enemy:GetRealHealth(r_arrival_time, DamageType.Magical))
                                                        if (totalRdmg * 0.95) > predictedEnemyHP2 then
                                                            enemyData[enemy.handle].recalling = true
                                                            enemyData[enemy.handle].recallStartTime = Game.GetTime()
                                                        else
                                                            if debugMsgs then
                                                                print('R ks FoW - 3')
                                                            end
                                                            if not isFleeMode and passEnemiesCheck then
                                                                -- workaround to get updated enemy position
                                                                local senderHandle = enemy.handle
                                                                if player:FindBuff(rBuff) then
                                                                    delayAction(function()
                                                                        local enemy = ObjectManager.ResolveHandle(senderHandle)
                                                                        if enemy and enemy:IsValid() then
                                                                            R:Cast(enemy.position)
                                                                        end
                                                                    end, Game.GetLatency())
                                                                else
                                                                    R:Cast()
                                                                    delayAction(function()
                                                                        local enemy = ObjectManager.ResolveHandle(senderHandle)
                                                                        if enemy and enemy:IsValid() then
                                                                            R:Cast(enemy.position)
                                                                        end
                                                                    end, Game.GetLatency() + 0.52)
                                                                end
                                                            end
                                                        end
                                                    end
                                                end
                                            end
                                        end
                                    end

                                end
                            else
                                if enemyData[enemy.handle] then
                                    enemyData[enemy.handle].recalling = false
                                end
                            end
                        else
                            if enemyData[enemy.handle] then
                                enemyData[enemy.handle].recalling = false
                            end
                        end
                    end


                    -- cast it
                    if R:Ready() and enemyData[enemy.handle] and enemyData[enemy.handle].recallStartTime and enemyData[enemy.handle].recalling and (Game.GetTime() - enemyData[enemy.handle].recallStartTime) < 10 then
                        if player.position:CountEnemiesInRange(menu.rMenu.rMinRangeFoW.value) == 0 then
                            local r_arrival_time = R.delay + Game.GetLatency() + tick
                            local recallSpeedTime = enemyData[enemy.handle].hasSuperRecall and 4 or 8
                            local left_recall_time = (enemyData[enemy.handle].recallStartTime + recallSpeedTime) - Game.GetTime()
                            local timeDelta = left_recall_time - r_arrival_time
                            if timeDelta > 0 and timeDelta < 1 then
                                if debugMsgs then
                                    print('R ks FoW - 2')
                                end
                                if not isFleeMode and Common.GetDistance2(enemy.position) < R.range then
                                    if player:FindBuff(rBuff) then
                                        R:Cast(enemy.position)
                                    else
                                        R:Cast()
                                        local senderHandle = enemy.handle
                                        delayAction(function()
                                            local enemy = ObjectManager.ResolveHandle(senderHandle)
                                            if enemy and enemy:IsValid() then
                                                R:Cast(enemy.position)
                                            end
                                        end, Game.GetLatency() + 0.52)
                                    end
                                end
                            else
                                cancelRfowTime = Game.GetTime() + 0.15 + Game.GetLatency()
                                local timeLeft = timeDelta - (timeDelta > 1 and 1 or 0)
                                leftTimeRfow = string.format("%.1f", timeLeft)
                                if menu.rMenu.cancelRfowHotkey.value then
                                    enemyData[enemy.handle].recalling = false
                                end
                            end
                        end
                    end
                end
            end
        end
    end

    -------------------------------------------------------------------------------------------------------------
    local function MissileFinder()
        for _, missile in ObjectManager.allMissileClients:pairs() do
            if missile and missile.handleMissileOwner == player.handle then
                if missile.spell.hash == Game.fnvhash("XerathMageSpearMissile") then
                    eMissileHandle = missile.handle;
                    eCastPos = missile.missileDestination;
                end
            end
        end
    end

    local function Automatic()
        if not player:IsRecalling() then
            if E:Ready() then
                if menu.eMenu.eAutomatic.eCastSpecialImmobileTarget.value then
                    E:CastSpecialImmobileTarget(player.position, false, false);
                end
            elseif W:Ready() then
                if menu.wMenu.wAutomatic.wCastSpecialImmobileTarget.value then
                    W:CastSpecialImmobileTarget(player.position, false, false);
                end
            end

            for _, enemy in ObjectManager.enemyHeroes:pairs() do
                if enemy and Common.ValidTarget2(enemy) and not Common.IsUnitSpellImmune(enemy) and not enemy.isZombie then
                    local w_pred = W:GetPrediction(enemy);
                    local e_pred = E:GetPrediction(enemy);
                    local e_input = PredictionInput.new(player.position, player.position, E.delay, E.width, E.range, E.speed, SkillshotType.SkillshotLine, enemy, true, true, bit.bor(CollisionFlag.CollidesWithMinions, CollisionFlag.CollidesWithHeroes, CollisionFlag.CollidesWithYasuoWall), false, 0);
                    local e_collision = MovementPrediction.GetCollision({ player.position, enemy.position }, e_input);
                    if not E:Ready() or e_collision == true then --or getEArrivalTime(enemy) > W.delay
                        if W:Ready() then
                            if enemy.position:IsInRange(player.position, W.range) then
                                if menu.wMenu.wAutomatic.wCC.value then
                                    if not Champions.CanMove(enemy, W.delay - getHitboxCollisionTime(W, false, enemy) + tick + Game.GetLatency()) then
                                        W:Cast(enemy.position);
                                        break
                                    end
                                end

                                if menu.wMenu.wAutomatic.wOnProcSpellcast.value then
                                    local active_spell = enemy.activeSpell;
                                    if active_spell and not enemy.isDashing then
                                        local time_remaining = active_spell.timeWindupEnd - Game.GetTime();
                                        if time_remaining >= W.delay - getHitboxCollisionTime(W, false, enemy) then
                                            W:Cast(enemy.position)
                                            break
                                        end
                                    end
                                end
                            end

                            if menu.wMenu.wAutomatic.wOnDash.value then
                                if w_pred.hitchance == HitChance.Dashing and w_pred.castPosition:IsInRange(player.position, W.range) then
                                    W:Cast(w_pred.castPosition)
                                    break
                                end
                            end
                        end
                    else
                        if E:Ready() then
                            if enemy.position:IsInRange(player.position, E.range) then
                                if e_collision == false then
                                    if menu.eMenu.eAutomatic.eCC.value then
                                        if blockEforChainCC(enemy) and not Champions.CanMove(enemy, getEArrivalTime(enemy) - 0.1 - tick - Game.GetLatency()) then
                                            E:Cast(enemy.position)
                                            break
                                        end
                                    end

                                    if menu.eMenu.eAutomatic.eOnProcSpellcast.value then
                                        local active_spell = enemy.activeSpell;
                                        if active_spell and not enemy.isDashing then
                                            local time_remaining = active_spell.timeWindupEnd - Game.GetTime();
                                            if time_remaining >= getEArrivalTime(enemy) then
                                                E:Cast(enemy.position)
                                                break
                                            end
                                        end
                                    end

                                    if menu.eMenu.eAutomatic.eInterrupt.value then
                                        if enemy:IsCastingInterruptibleSpell() >= 1 then
                                            local hash = enemy.hash;
                                            -- ignored enemy channels
                                            for i, _ in ipairs(IgnoredChannels) do
                                                if IgnoredChannels[i] == hash then
                                                    break
                                                end
                                            end
                                            E:Cast(enemy.position)
                                            break
                                        end
                                    end
                                end
                            end

                            if menu.eMenu.eAutomatic.eOnDash.value then
                                if e_pred.hitchance == HitChance.Dashing and e_pred.castPosition:IsInRange(player.position, E.range) then
                                    E:Cast(e_pred.castPosition);
                                    break
                                end
                            end

                            -- anti gapclose
                            if menu.eMenu.eAutomatic.eAntiGap.value then
                                if enemy and enemy.IsValid and enemy:IsValid() and enemy:IsValidTarget() and enemy.isAlive and enemy.isDashing then
                                    if (not menu.eMenu.eAutomatic.eAntiGapWhitelist["eAntiGapWhitelist" .. enemy.networkId] or menu.eMenu.eAutomatic.eAntiGapWhitelist["eAntiGapWhitelist" .. enemy.networkId].value) then
                                        local pred = e_pred
                                        if pred and pred.unitPosition and Common.GetDistance2(pred.unitPosition) < E.range and Common.GetDistance2(pred.unitPosition) < 500 then
                                            if pred.hitchance >= HitChance.High then
                                                E:Cast(pred.castPosition)
                                            end
                                        end
                                    end
                                end
                            end


                        end
                    end
                end
            end
        end
    end

    local function QLogic()

        if Q:Ready() and (not Evade.IsEvading() or menu.advancedMenu.ignoreEvadeQ.value) then

            -- requested Q wait Dash E first feature:
            if menu.qMenu.qWaitEnemyDash.value == false or EnemyDashReady(900) == false or E:Ready() == false then
                -- Q Combo
                if (menu.qMenu.qCombo.value and Champions.Combo) then
                    -- Q2
                    if Q:IsCharging() then
                        local q_range = 1450;
                        if menu.qMenu.qTargetSelection.value == 1 then
                            q_range = Q.range;
                        end
                        local target = TargetSelector.GetTarget(q_range, DamageType.Magical, player.position, true);
                        qrTargetHandle = target and target:IsValidTarget() and target.handle or 0;
                        local qAboutToExpire = (Game.GetTime() - qChargeTime > 2.6 and Game.GetTime() - qChargeTime < 9)

                        -- Q FOW casts
                        if not target and menu.qMenu.qFOW.value then
                            if isComboMode or isHarassMode or isLaneClearMode then
                                for i, enemy in ObjectManager.enemyHeroes:pairs() do
                                    if enemy and enemy.IsValid and enemy:IsValid() and enemy.isAlive then
                                        if Game.IsInFoW(enemy.position) or not enemy.isVisible then
                                            if enemyFow[enemy.handle] and Game.GetTime() - enemyFow[enemy.handle] < 1.1 then
                                                if Common.GetDistance2(enemy.position) < Q.range then
                                                    Q:ShootChargedSpell(enemy.position, true, 0, false);
                                                end
                                            end
                                        else
                                            enemyFow[enemy.handle] = Game.GetTime()
                                        end
                                    end
                                end
                            end
                        end

                        if target and target:IsValidTarget(Q.range + target.hitboxRadius) and Common.ValidTarget2(target) and not target.isZombie then
                            local pred = Q:GetPrediction(target);
                            local buff = target:FindBuff(wBuff);
                            local saveQrange = Q.range + target.hitboxRadius - 5 - (target.charIntermediate.moveSpeed * Q.delay)
                            local targetRange = qAboutToExpire and Q.range or saveQrange
                            if target.position:IsInRange(player.position, Q.range) and not Champions.CanMove(target, Q.delay + tick + Game.GetLatency() - (getHitboxCollisionTime(Q, true, target) / 2)) then
                                Q:ShootChargedSpell(target.position, true, 0, false);
                            elseif target.position:IsInRange(player.position, targetRange) then
                                local enemyTooClose = player.position:CountEnemiesInRange(400) > 0
                                if (buff and buff.leftTime > Q.delay) or Game.IsInFoW(pred.unitPosition) or qAboutToExpire or enemyTooClose or player.position:Distance(target.position) > qEdgeRange then
                                    if pred.hitchance >= HitChance.Low or (Q.range == 1450 and player.position:Distance(target.position) > qEdgeRange) then
                                        Q:ShootChargedSpell(pred.castPosition, true, 0, false);
                                        return;
                                    end
                                else
                                    if pred.hitchance >= HitChance.Low + menu.qMenu.hitchanceQ.value then
                                        Q:ShootChargedSpell(pred.castPosition, true, 0, false);
                                        return;
                                    elseif target.isDashing and E:Ready() then
                                        -- release Q2 asap to intercept enemy with E
                                        Q:ShootChargedSpell(pred.castPosition, true, 0, false);
                                        return;
                                    end
                                end
                            end
                        elseif qAboutToExpire then
                            -- if Q2 about to expire and no target in range:
                            if menu.qMenu.q2IfNoTarget.value == 1 and player.mpPercent < menu.qMenu.q2IfNoTargetMinMana.value then
                                -- hold for refund bc low mana
                            elseif menu.qMenu.q2IfNoTarget.value == 2 then
                                -- hold for refund always
                            else
                                local anyQ2targePosition = GetAnyQ2Target()
                                if anyQ2targePosition then
                                    Q:ShootChargedSpell(anyQ2targePosition, true, 0, false);
                                end
                            end
                        end
                    else
                        -- Q1
                        if (blockSpellsForW() or blockWforEMissile() == false) and blockSpellsForE() then
                            local target = TargetSelector.GetTarget(menu.qMenu.qChargeRange.value, DamageType.Magical, player.position, true);  -- check current range or max range for target?
                            if target and target:IsValidTarget(menu.qMenu.qChargeRange.value + target.hitboxRadius) and
                            Common.ValidTarget2(target) and
                                not target.isZombie and (not target.isDashing or not E:Ready()) and not target:IsInvulnerable(player) then
                                Q:StartCharging(target.position, false);
                                return;
                            end
                        end
                    end
                end

                -- Q Harass
                if (menu.qMenu.qHarass.value and (Champions.Harass or Champions.LaneClear) and menu.extra.harassToggle.value and not player.position2D:IsUnderEnemyTurret()) then
                    if not menu.qMenu.qHarassHold.value or not W:Ready() then
                        if Q:IsCharging() then
                            local target = TargetSelector.GetTarget(Q.range, DamageType.Magical, player.position, true);
                            local qAboutToExpire = (Game.GetTime() - qChargeTime > 2.6 and Game.GetTime() - qChargeTime < 9)
                            if target and target:IsValidTarget(Q.range + target.hitboxRadius) and Common.ValidTarget2(target) and not target.isZombie then
                                local pred = Q:GetPrediction(target);
                                local buff = target:FindBuff(wBuff);
                                local saveQrange = Q.range - 5 - (target.charIntermediate.moveSpeed * Q.delay)
                                local targetRange = qAboutToExpire and Q.range or saveQrange
                                if target.position:IsInRange(player.position, Q.range) and not Champions.CanMove(target, Q.delay + tick + Game.GetLatency()) then
                                    Q:ShootChargedSpell(target.position, true, 0, false);
                                elseif target.position:IsInRange(player.position, targetRange) then --  + target.hitboxRadius?
                                    if (buff and buff.leftTime > Q.delay) or Game.IsInFoW(pred.unitPosition) or qAboutToExpire or player.position:Distance(target.position) > qEdgeRange then
                                        if pred.hitchance >= HitChance.Low or (Q.range == 1450 and player.position:Distance(target.position) > qEdgeRange) then
                                            Q:ShootChargedSpell(pred.castPosition, true, 0, false);
                                            return;
                                        end
                                    else
                                        if pred.hitchance >= HitChance.Low + menu.qMenu.hitchanceQ.value then
                                            Q:ShootChargedSpell(pred.castPosition, true, 0, false);
                                            return;
                                        elseif (target.isDashing and E:Ready()) then
                                            -- release Q2 asap to intercept enemy with E
                                            Q:ShootChargedSpell(pred.castPosition, true, 0, false);
                                            return;
                                        end
                                    end
                                end
                            elseif qAboutToExpire then
                                -- if Q2 about to expire and no target in range:
                                if menu.qMenu.q2IfNoTarget.value == 1 and player.mpPercent < menu.qMenu.q2IfNoTargetMinMana.value then
                                    -- hold for refund
                                else
                                    local anyQ2targePosition = GetAnyQ2Target()
                                    if anyQ2targePosition then
                                        Q:ShootChargedSpell(anyQ2targePosition, true, 0, false);
                                    end
                                end
                            end
                        else
                            if blockSpellsForW() or menu.wMenu.wHarass.value == false then
                                local target = TargetSelector.GetTarget(1400, DamageType.Magical, player.position, true);  -- check current range or max range for target?
                                if target and target:IsValidTarget(Q.range + target.hitboxRadius) and
                                    Common.ValidTarget2(target) and
                                    not target.isZombie and (not target.isDashing or not E:Ready()) and not target:IsInvulnerable(player) then
                                    Q:StartCharging(target.position, false);
                                    return;
                                end
                            end
                        end
                    end
                end

                if menu.qMenu.qCastSpecialImmobileTarget.value and Q:IsCharging() then
                    Q:CastSpecialImmobileTarget(player.position, false, false);
                end
            end

        end
    end

    local function WLogic()
        if W:Ready() and not player:IsWindingUp() and not Q:IsCharging() and blockSpellsDuringCast() and blockSpellsForQ() and Game.GetTime() - eAttemptCastTime > E.delay and (not Evade.IsEvading() or menu.advancedMenu.ignoreEvadeW.value) then
            if (menu.wMenu.wCombo.value and Champions.Combo and blockSpellsForE()) or (menu.wMenu.wHarass.value and (Champions.Harass or Champions.LaneClear) and menu.extra.harassToggle.value and not player.position2D:IsUnderEnemyTurret()) then
                local target = TargetSelector.GetTarget(W.range, DamageType.Magical);
                if target and target:IsValidTarget(W.range) and
                    Common.ValidTarget2(target) and not target.isZombie and
                    (not target.isDashing or not E:Ready()) then
                    if blockWforEMissile() or not Champions.CanMove(target, W.delay + tick + Game.GetLatency()) then
                        local wDelay = (Game.GetTime() - eCastTime) < 0.5 and (W.delay - 0.25) or W.delay
                        local w_pred_input = PredictionInput.new(player.position, player.position, wDelay, W.width, W.range, W.speed, SkillshotType.SkillshotCircle, target, false, false, CollisionFlag.CollidesWithNothing, true, 0);
                        local w_pred = MovementPrediction.GetPrediction(w_pred_input, false, false);
                        local w2_pred_input = PredictionInput.new(player.position, player.position, wDelay, 225, W.range, W.speed, SkillshotType.SkillshotCircle, target, false, false, CollisionFlag.CollidesWithNothing, true, 0);
                        local w2_pred = MovementPrediction.GetPrediction(w2_pred_input, false, false);

                        if target.position:IsInRange(player.position, W.range) and not Champions.CanMove(target, W.delay - getHitboxCollisionTime(W, false, target) + tick + Game.GetLatency()) then
                            W:Cast(target.position);
                            return;
                        elseif w_pred.hitchance >= HitChance.Low + menu.wMenu.hitchanceW.value and player.position:Distance(w_pred.castPosition) < W.range then
                            W:Cast(w_pred.castPosition);
                            return;
                        elseif w2_pred.hitchance >= HitChance.Low + menu.wMenu.hitchanceW.value and player.position:Distance(w2_pred.castPosition) < W.range then
                            W:Cast(w2_pred.castPosition);
                            return;
                        end
                    end
                end
            end
        end
    end

    local function ELogic()
        if E:Ready() and menu.eMenu.eHotkey.value then
            local target = getClosestEnemyCursor();
            local selected_target = TargetSelector.GetSelectedTarget();
            if selected_target and selected_target.isHero and selected_target:IsValidTarget(E.range) then
                target = ObjectManager.ResolveHandle(selected_target.handle);
            end

            if target and target:IsValidTarget(E.range) and not Common.IsUnitSpellImmune(target) then
                local pred = E:GetPrediction(target);
                if pred.castPosition:IsInRange(player.position, E.range) then
                    if target.position:IsInRange(player.position, fastECastCloseRange + target.hitboxRadius) then
                        if pred.hitchance >= HitChance.Low then
                            E:Cast(pred.castPosition);
                            eAttemptCastTime = Game.GetTime();
                            return;
                        end
                    else
                        if pred.hitchance >= HitChance.Low + menu.eMenu.hitchanceE.value then
                            E:Cast(pred.castPosition);
                            eAttemptCastTime = Game.GetTime();
                            return;
                        end
                    end
                end
            end
        end

        if E:Ready() and not player:IsWindingUp() and not Q:IsCharging() and blockSpellsDuringCast() and blockSpellsForQ() and (not Evade.IsEvading() or menu.advancedMenu.ignoreEvadeE.value) then
            -- add selected target check, E only him in range
            local e_range = menu.eMenu.eRange.value;
            local target = TargetSelector.GetTarget(e_range, DamageType.Magical);
            if (menu.eMenu.eCombo.value and Champions.Combo) or (menu.eMenu.eHarass.value and (Champions.Harass or Champions.LaneClear) and menu.extra.harassToggle.value and not player.position2D:IsUnderEnemyTurret()) then
                if target and target:IsValidTarget(e_range) then
                    local input = PredictionInput.new(player.position, player.position, E.delay, E.width, E.range, E.speed, SkillshotType.SkillshotLine, target, true, true, bit.bor(CollisionFlag.CollidesWithMinions, CollisionFlag.CollidesWithHeroes, CollisionFlag.CollidesWithYasuoWall), false, 0);
                    local collision = MovementPrediction.GetCollision({ player.position, target.position }, input);
                    if collision == true or target.position:Distance(player.position) > 800 or Common.IsUnitSpellImmune(target) then
                        for _, enemy in ObjectManager.enemyHeroes:pairs() do
                            if enemy and Common.ValidTarget2(enemy) and enemy:IsValidTarget(e_range) and not Common.IsUnitSpellImmune(enemy) then
                                local enemy_pred = E:GetPrediction(enemy);
                                if enemy_pred.hitchance >= HitChance.Low then
                                    if enemy.position:Distance(player.position) < target.position:Distance(player.position) then
                                        target = enemy;
                                    end
                                end
                            end
                        end
                    end
                end

                if target and target:IsValidTarget(menu.eMenu.eRange.value) and not Common.IsUnitSpellImmune(target) and blockEforChainCC(target) then
                    if not menu.eMenu.eMoreHC.value or
                        (target.isMoving or target.activeSpell or target.charIntermediate.moveSpeed < 200 or target:HasBuffOfType(BuffType.Knockup)
                            or target:HasBuffOfType(BuffType.Stun) or target:HasBuffOfType(BuffType.Snare) or
                            target:HasBuffOfType(BuffType.Suppression) or target:HasBuffOfType(BuffType.Charm) or target:HasBuffOfType(BuffType.Fear)) then
                        local pred = E:GetPrediction(target);
                        local buff = target:FindBuff(wBuff);
                        if pred.castPosition:IsInRange(player.position, E.range) and (not target.isDashing or pred.hitchance >= HitChance.Dashing) then
                            if (buff and buff.leftTime > E.delay + (player.position:Distance(target.position) / E.speed)) or target.position:IsInRange(player.position, fastECastCloseRange + target.hitboxRadius) then
                                if pred.hitchance >= HitChance.Low then
                                    E:Cast(pred.castPosition);
                                    eAttemptCastTime = Game.GetTime();
                                    return;
                                end
                            else
                                if pred.hitchance >= HitChance.Low + menu.eMenu.hitchanceE.value then
                                    E:Cast(pred.castPosition);
                                    eAttemptCastTime = Game.GetTime();
                                    return;
                                end
                            end
                        end
                    end
                end
            end
        end
    end

    -- Evade.SetCustomShouldSpellBeIgnored(function(skillshot)
    --     if skillshot.SpellData.DangerLevel <= 4 then
    --         return true
    --     end
    -- end)

    local function RLogic()
        if player:FindBuff(rBuff) then
            local target = nil;

            if menu.rMenu.rMouseRange.value == 2500 then
                target = TargetSelector.GetTarget(5000, DamageType.Magical);
            else
                target = TargetSelector.GetTarget(menu.rMenu.rMouseRange.value, DamageType.Magical, Game.GetCursorWorldPosition(), false);
            end

            if menu.rMenu.rTargetMode.value == 1 then
                local closest_target = getClosestEnemyCursor();
                if closest_target and closest_target:IsValidTarget() and Common.ValidTarget2(closest_target) then
                    local mouse_range = menu.rMenu.rMouseRange.value;
                    if closest_target.position:Distance(Game.GetCursorWorldPosition()) < mouse_range or mouse_range == 2500 then
                        target = closest_target;
                    end
                end
            end

            local validLockedRtarget = false
            if menu.rMenu.lockRtarget.value and lockedRtargetActive and lockedRtargetHandle then
                local lockedRtar = ObjectManager.ResolveHandle(lockedRtargetHandle)
                if lockedRtar and lockedRtar:IsValid() and Common.GetDistance2(lockedRtar.position) < 5000 and lockedRtar.isAlive and lockedRtar.isVisible then
                    validLockedRtarget = true
                else
                    lockedRtargetActive = false
                    lockedRtargetHandle = nil
                end
            end

            local selected_target = TargetSelector.GetSelectedTarget();
            if selected_target and selected_target.isHero and selected_target:IsValidTarget(5000) and selected_target.isVisible then
                target = ObjectManager.ResolveHandle(selected_target.handle)
                if menu.rMenu.lockRtarget.value then
                    if lockedRtargetActive == false then
                        lockedRtargetActive = true
                        lockedRtargetHandle = target.handle
                        validLockedRtarget = true
                    else
                        -- if we click other enemy, swap to him
                        if target.handle ~= lockedRtargetHandle then
                            lockedRtargetActive = true
                            lockedRtargetHandle = target.handle
                            validLockedRtarget = true
                        end
                    end
                end
            end
            qrTargetHandle = target and target:IsValidTarget() and target.handle or 0


            if validLockedRtarget then
                local lockedRtar = ObjectManager.ResolveHandle(lockedRtargetHandle)
                local pred = R:GetPrediction(lockedRtar);
                local hp = lockedRtar:GetRealHealth(1000, DamageType.Magical);
                local r_dmg = R:GetDamage(lockedRtar);
                local ludens_dmg = getLudensDamage(lockedRtar, R);

                if not (Game.GetTime() - rCastTime < 0.6 + tick + Game.GetLatency() and lockedRtar.position:Distance(rCastPos) < 200 and hp < r_dmg + ludens_dmg) then
                    if pred.castPosition:IsInRange(player.position, 5000) then
                        if pred.hitchance >= HitChance.High or menu.rMenu.rFastCast.value then
                            if ShouldCastRafterDelay() then
                                if not lockedRtar:IsInvulnerable(player) and lockedRtar:IsTargetableByUnit(player) then
                                    R:Cast(pred.castPosition);

                                    if menu.rMenu.castBlueTrinket.value and Common.GetDistance2(lockedRtar.position) < 4000 then
                                        if player:FindItem(3363) and player:CanUseItem(3363) then -- Blue Trinket
                                            local itemSlot = player:FindItemSlot(3363)
                                            local itemSpell = SDKSpell.Create(itemSlot, 4000, DamageType.Physical)
                                            itemSpell:Cast(lockedRtar.position + Math.Vector3(math.random(-100, 100), 0, math.random(-100, 100)):FixHeight())
                                            itemSpell:Delete()
                                        end
                                    end
                                    return
                                end
                            end
                        end
                    end
                end
            else
                if (menu.rMenu.rHotkey.value or menu.rMenu.rCastMode.value == 0) and (manualRengaged == false or isComboMode or menu.rMenu.rHotkey.value) then
                    if target and target:IsValidTarget(5000) then
                        local pred = R:GetPrediction(target);
                        local hp = target:GetRealHealth(1000, DamageType.Magical);
                        local r_dmg = R:GetDamage(target);
                        local ludens_dmg = getLudensDamage(target, R);

                        if not (Game.GetTime() - rCastTime < 0.6 + tick + Game.GetLatency() and target.position:Distance(rCastPos) < 200 and hp < r_dmg + ludens_dmg) then
                            if pred.castPosition:IsInRange(player.position, 5000) then
                                if pred.hitchance >= HitChance.High or menu.rMenu.rFastCast.value then
                                    if ShouldCastRafterDelay() then
                                        if not target:IsInvulnerable(player) and target:IsTargetableByUnit(player) then
                                            R:Cast(pred.castPosition);

                                            if menu.rMenu.castBlueTrinket.value and Common.GetDistance2(target.position) < 4000 then
                                                if player:FindItem(3363) and player:CanUseItem(3363) then -- Blue Trinket
                                                    local itemSlot = player:FindItemSlot(3363)
                                                    local itemSpell = SDKSpell.Create(itemSlot, 4000, DamageType.Physical)
                                                    itemSpell:Cast(target.position + Math.Vector3(math.random(-100, 100), 0, math.random(-100, 100)):FixHeight())
                                                    itemSpell:Delete()
                                                end
                                            end
                                            return
                                        end
                                    end
                                end
                            end
                        end
                    end
                end
            end

            if menu.rMenu.rCastSpecialImmobileTarget.value and (manualRengaged == false or isComboMode or lockedRtargetActive) then
                if ShouldCastRafterDelay() then
                    R:CastSpecialImmobileTarget(player.position, false, false)
                end
            end

        elseif R:Ready() then
            if menu.rMenu.rHotkey.value and menu.rMenu.rInitiateWithHotkey.value and player.position:CountEnemiesInRange(5000 - 200) > 0 then
                R:Cast();
            end
            if menu.rMenu.rCombo.value and menu.rMenu.rCastMode.value == 0 and player.position:CountEnemiesInRange(900) == 0 and not player.position2D:IsUnderEnemyTurret() then
                for _, enemy in ObjectManager.enemyHeroes:pairs() do
                    if enemy and Common.ValidTarget2(enemy) and enemy:IsValidTarget(4500) and Champions.ValidUlt(enemy, R.delay + 0.1) then
                        if not Champions.CanMove(enemy, (R.delay * 2) - getHitboxCollisionTime(R, false, enemy)) then -- add extra 0.5 between shots?
                            local hp = enemy:GetRealHealth(2000, DamageType.Magical);
                            local q_dmg = (Q:Ready() and enemy.position:IsInRange(player.position, Q.range) and player.mp > R:ManaCost()) and Q:GetDamage(enemy) or 0;
                            local w_dmg = (W:Ready() and enemy.position:IsInRange(player.position, W.range) and player.mp > R:ManaCost()) and W:GetDamage(enemy) or 0;
                            local r_dmg = R:GetDamage(enemy);
                            local ludens_dmg = getLudensDamage(enemy, R);
                            if hp < r_dmg * 4 + ludens_dmg and hp > q_dmg + w_dmg + ludens_dmg then
                                if menu.rMenu.lockRtarget.value then
                                    lockedRtargetActive = true
                                    lockedRtargetHandle = enemy.handle
                                end
                                R:Cast();
                            end
                        end
                    end
                end
            end
        end
    end

    local function Jungleclear()
        if not player:IsWindingUp() then
            for _, monster in ObjectManager.jungleMinions:pairs() do
                if monster and monster:IsValidTarget() and not monster.isPlant then
                    if Champions.LaneClear and menu.extra.spellFarm.value then
                        if Q:Ready() and menu.qMenu.qJungleclear.value and monster.position:IsInRange(player.position, Q.range) then
                            if Q:IsCharging() then
                                local pred = Q:GetPrediction(monster);
                                if pred.hitchance >= HitChance.Low then
                                    Q:ShootChargedSpell(pred.castPosition, true, 0, false);
                                end
                            elseif monster.position:IsInRange(player.position, 1000) then
                                Q:StartCharging(monster.position, false);
                            end
                        end

                        if W:Ready() and menu.wMenu.wJungleclear.value and monster.position:IsInRange(player.position, W.range) then
                            local w_aoe_pos = W:GetCastOnBestFarmPosition(3, true);
                            if w_aoe_pos:IsValid() then
                                W:Cast(w_aoe_pos);
                            else
                                local pred = W:GetPrediction(monster);
                                if pred.hitchance >= HitChance.Low then
                                    W:Cast(pred.castPosition);
                                end
                            end

                        end

                        if E:Ready() and menu.eMenu.eJungleclear.value and monster.position:IsInRange(player.position, E.range) then
                            local pred = E:GetPrediction(monster);
                            if pred.hitchance >= HitChance.Low then
                                E:Cast(pred.castPosition);
                            end
                        end
                    end

                    if monster.isEpicMonster or monster.isLargeMonster or monster.isSmiteMonster then
                        if Champions.Combo or Champions.LaneClear or Champions.Harass and blockSpellsForQ() then
                            local hp = monster.totalHealth;
                            local ludens_dmg = getLudensDamage(monster, Q);
                            if Q:IsCharging() and menu.qMenu.qJunglestealer.value then
                                local incoming_dmg = HealthPrediction.GetIncomingDamage(monster, (Q.delay - (0.033 + Game.GetLatency())), false, true);
                                local q_dmg = Q:GetDamage(monster);
                                if hp < q_dmg + ludens_dmg + incoming_dmg then
                                    local pred = Q:GetPrediction(monster);
                                    if pred.hitchance >= HitChance.Low then
                                        Q:ShootChargedSpell(pred.castPosition, true, 0, false);
                                    end
                                end
                            end

                            if W:Ready() and menu.wMenu.wJunglestealer.value then
                                -- todo:  W2 damage
                                local incoming_dmg = HealthPrediction.GetIncomingDamage(monster, (W.delay - (0.033 + Game.GetLatency())), false, true);
                                local w_dmg = W:GetDamage(monster);
                                if hp < w_dmg + ludens_dmg + incoming_dmg then
                                    local pred = W:GetPrediction(monster);
                                    if pred.hitchance >= HitChance.Low then
                                        W:Cast(pred.castPosition);
                                    end
                                end
                            end
                        end
                    end
                end
            end
        end
    end

    local function Laneclear()
        if menu.extra.spellFarm.value and Orbwalker.CanUseSpell() and not player:IsWindingUp() and (player.mpPercent >= menu.extra.Mana.value or Q:IsCharging()) then
            for _, minion in ObjectManager.spellFarmMinions:pairs() do
                if minion and minion:IsValid() and minion.isAlive and not minion.isPlant and not minion.isWard and not minion.isTrap and not minion.isBarrel and not minion.isWardNoBlue and not minion.isInvulnerable and minion.hash ~= 0x3327589C then
                    local q_predicted_hp = HealthPrediction.GetHealthPrediction(minion, Q.delay - tick - tick - Game.GetLatency(), true);
                    local w_predicted_hp = HealthPrediction.GetHealthPrediction(minion, 0.9 - tick - tick - Game.GetLatency(), true);
                    local orb_NID = Orbwalker.lastTargetNID;
                    if Q:Ready() and minion:IsValidTarget(Q.range) and q_predicted_hp > 0 and minion.networkId ~= orb_NID then
                        local q_dmg = Q:GetDamage(minion);
                        local pred = Q:GetPrediction(minion);
                        if menu.qMenu.qLaneclear.value and Champions.LaneClear then
                            if q_predicted_hp < q_dmg then
                                if not Q:IsCharging() then
                                    Q:StartCharging(player.position, false);
                                else
                                    if pred.hitchance >= HitChance.Low then
                                        Q:Cast(pred.castPosition);
                                    end
                                end
                            end

                            if minion.position:CountEnemyLaneMinionsInRange(Q.width) >= menu.extra.LCminions.value and minion.position:IsInRange(player.position, Q.range - 150) then
                                if not Q:IsCharging() then
                                    Q:StartCharging(player.position, false);
                                else
                                    Q:Cast(minion.position);
                                end
                            end
                        end

                        if menu.qMenu.qLasthit.value and bit.band(Orbwalker.activeMode, OrbwalkerMode.LastHit) == OrbwalkerMode.LastHit then
                            if q_predicted_hp < q_dmg then
                                if not Q:IsCharging() then
                                    Q:StartCharging(player.position, false);
                                else
                                    if pred.hitchance >= HitChance.Low then
                                        Q:Cast(pred.castPosition);
                                    end
                                end
                            end
                        end
                    end

                    if W:Ready() and minion:IsValidTarget(W.range) and minion.networkId ~= orb_NID then
                        local w_dmg = W:GetDamage(minion);
                        local pred = W:GetPrediction(minion);
                        if menu.wMenu.wLaneclear.value and Champions.LaneClear then
                            if w_predicted_hp < w_dmg then
                                if pred.hitchance >= HitChance.Low then
                                    W:Cast(pred.castPosition);
                                end
                            end

                            if minion.position:CountEnemyLaneMinionsInRange(275) >= 3 then
                                W:Cast(minion.position);
                            end
                        end

                        if menu.wMenu.wLasthit.value and bit.band(Orbwalker.activeMode, OrbwalkerMode.LastHit) == OrbwalkerMode.LastHit then
                            if w_predicted_hp < w_dmg then -- todo:  use bonus W dmg?
                                W:Cast(pred.castPosition);
                            end
                        end
                    end
                end
            end
        end
    end

    -------------------------------------------------------------------------------------------------------------
    Callback.Bind(CallbackType.OnFastTick, function()
        isComboMode = bit.band(Orbwalker.activeMode, OrbwalkerMode.Combo) == OrbwalkerMode.Combo
        isFleeMode = bit.band(Orbwalker.activeMode, OrbwalkerMode.Flee) == OrbwalkerMode.Flee

        if manualRengaged and manualRstartTime ~= 0 and Game.GetTime() > manualRstartTime then
            if not player:FindBuff(rBuff) then
                manualRstartTime = 0
                manualRengaged = false
            end
        end

        if rCastTime ~= 0 and (Game.GetTime() - rCastTime) > 0.2 + Game.GetLatency() then
            if not player:FindBuff(rBuff) then
                rCastTime = 0
                lockedRtargetActive = false
                lockedRtargetHandle = nil
            end
        end
    end)

    local xerathRSpellHash = Game.fnvhash("XerathLocusOfPower2")

    Callback.Bind(CallbackType.OnCastHud, function(spellData, targetHandle)
        local validHash = spellData and spellData.hash == xerathRSpellHash
        if validHash then
            if menu.rMenu.allowManualR.value then
                manualRengaged = true
                manualRstartTime = Game.GetTime() + Game.GetLatency() + 0.6
            end
        end
    end)

    Callback.Bind(CallbackType.OnTick, function()

        ---------------------------------- Hotkey toogle for Q
        if menu.qMenu.qWaitEnemyDash.value then
            if permaShowQWait == false then
                if FH_QWaitEnemyDash then
                    FH_QWaitEnemyDash:PermaShow(true, true);
                    permaShowQWait = true
                end
            end
        end
        if menu.qMenu.qWaitEnemyDash.value and E:Ready() == false then
            eNotReady = Game.GetTickCount()
        end
        if menu.qMenu.qWaitEnemyDash.value then
            if Game.GetTickCount() - lastQwait < 10000 and E:Ready() then
                qWait = true
            else
                menu.qMenu.qWaitEnemyDash.value = false
                eNotReady = -99999
            end
        else
            lastQwait = Game.GetTickCount()
            qWait = false
        end


        if not player.isAlive then return end
        -- RksFow()  -- Patched for now by Riot..
        MissileFinder();
        Automatic();

        RLogic();
        ELogic();
        WLogic();
        QLogic();

        Jungleclear();
        Laneclear();

    end)

    Callback.Bind(CallbackType.OnBeforeAttack, function(beforeAttackOrbwalkerArgs)
        if menu.restrtictAA.value then
            local target = beforeAttackOrbwalkerArgs.target;
            if target and target.isHero then
                if bit.band(Orbwalker.activeMode, OrbwalkerMode.Combo) == OrbwalkerMode.Combo then
                    if Q:Ready() or W:Ready() or blockSpellsForE() == false then
                        return CallbackResult.Cancel;
                    end
                end
            end
        end
    end)

    Callback.Bind(CallbackType.OnIssueOrder, function(OnIssueOrderArgs)
        local r_buff = player:FindBuff(rBuff);
        if r_buff and Game.GetTime() - r_buff.startTime < 0.5 + tick + Game.GetLatency() then
            return CallbackResult.Cancel;
        end
    end)

    Callback.Bind(CallbackType.OnObjectRemove, function(object)
        if menu.wMenu.wAutomatic.wFowOnE.value and W:Ready() then
            if object.isMissile and Game.fnvhash(object:GetUniqueName()) == eMissileHash and Game.IsInFoW(object.position) then
                local pos = object.position:Extended(eCastPos, E.width);
                if pos:IsInRange(player.position, W.range) and pos:Distance(eCastPos) > E.width + 15 then
                    W:Cast(pos)
                end
            end
        end
    end)

    Callback.Bind(CallbackType.OnMinimapIconChange, function(sender, name, type)
        if menu.rMenu.rRecallKS.value and sender.isHero and sender.team ~= player.team then
            if type == 'recall' then
                local passEnemiesCheck = player.position:CountEnemiesInRange(menu.rMenu.rMinRangeFoW.value) == 0
                if R:Ready() and Common.GetDistance2(sender.position) < R.range then
                    local r_arrival_time = R.delay + Game.GetLatency() + tick
                    if enemyData[sender.handle] then
                        local timeElapsed = Game.GetTime() - enemyData[sender.handle].lastUpdate
                        local recallSpeedTime = enemyData[sender.handle].hasSuperRecall and 4 or 8
                        if timeElapsed < 10 and r_arrival_time < recallSpeedTime then
                            local predictedEnemyHP = not sender.isVisible and (enemyData[sender.handle].lastHP + ((r_arrival_time + timeElapsed) * enemyData[sender.handle].lastHPregeneration)) or (r_arrival_time * sender.charIntermediate.hpRegenRate + sender:GetRealHealth(r_arrival_time, DamageType.Magical))
                            local r_dmg = R:GetDamage(sender);
                            local ludens_dmg = getLudensDamage(sender, R)
                            local totalRdmg = r_dmg + ludens_dmg
                            if (totalRdmg * 0.95) > predictedEnemyHP then
                                local timeDelta = recallSpeedTime - r_arrival_time
                                if timeDelta < 1 and passEnemiesCheck then
                                    if debugMsgs then
                                        print('R ks FoW - 1')
                                    end
                                    if not isFleeMode then
                                        -- workaround to get updated enemy position
                                        local senderHandle = sender.handle
                                        if player:FindBuff(rBuff) then
                                            delayAction(function()
                                                local enemy = ObjectManager.ResolveHandle(senderHandle)
                                                if enemy and enemy:IsValid() then
                                                    R:Cast(enemy.position)
                                                end
                                            end, Game.GetLatency())
                                        else
                                            R:Cast()
                                            delayAction(function()
                                                local enemy = ObjectManager.ResolveHandle(senderHandle)
                                                if enemy and enemy:IsValid() then
                                                    R:Cast(enemy.position)
                                                end
                                            end, Game.GetLatency() + 0.52)
                                        end
                                    end
                                else
                                    local predictedEnemyHP2 = not sender.isVisible and (enemyData[sender.handle].lastHP + ((timeDelta + r_arrival_time + timeElapsed) * enemyData[sender.handle].lastHPregeneration)) or ((timeDelta + r_arrival_time) * sender.charIntermediate.hpRegenRate + sender:GetRealHealth(r_arrival_time, DamageType.Magical))
                                    if (totalRdmg * 0.95) > predictedEnemyHP2 then
                                        enemyData[sender.handle].recalling = true
                                        enemyData[sender.handle].recallStartTime = Game.GetTime()
                                    else
                                        if debugMsgs then
                                            print('R ks FoW - 3')
                                        end
                                        if not isFleeMode and passEnemiesCheck then
                                            -- workaround to get updated enemy position
                                            local senderHandle = sender.handle
                                            if player:FindBuff(rBuff) then
                                                delayAction(function()
                                                    local enemy = ObjectManager.ResolveHandle(senderHandle)
                                                    if enemy and enemy:IsValid() then
                                                        R:Cast(enemy.position)
                                                    end
                                                end, Game.GetLatency())
                                            else
                                                R:Cast()
                                                delayAction(function()
                                                    local enemy = ObjectManager.ResolveHandle(senderHandle)
                                                    if enemy and enemy:IsValid() then
                                                        R:Cast(enemy.position)
                                                    end
                                                end, Game.GetLatency() + 0.52)
                                            end
                                        end
                                    end
                                end
                            end
                        end
                    end
                end
            else
                if enemyData[sender.handle] then
                    enemyData[sender.handle].recalling = false
                end
            end
        end
    end)

    Callback.Bind(CallbackType.OnSpellAnimationStart, function(sender, castArgs)
        local spell = castArgs.spell;
        local hash = spell.hash;
        if sender.isMe then
            if hash == qCastHash then
                qCastTime = Game.GetTime();
            end

            if hash == qChargeHash then
                qChargeTime = Game.GetTime();
                qChargeTickCount = Game.GetTickCount();
            end

            if hash == wHash then
                wCastTime = Game.GetTime();
            end

            if hash == eHash then
                eCastTime = Game.GetTime();
            end

            if hash == Game.fnvhash("XerathLocusPulse") then
                rCastPos = castArgs.to;
                rCastTime = Game.GetTime();
            end
        end
    end)

    local qCircleColor = Renderer.ColorInfo.new(0x99FFFFFF, 0x997DAFFF, Renderer.GradientType.Linear)
    local wCircleColor = Renderer.ColorInfo.new(0xFFffafaf, 0xFFffafaf, Renderer.GradientType.Solid)
    local eCircleColor = Renderer.ColorInfo.new(0xFFff6464, 0xFFff6464, Renderer.GradientType.Solid)
    local rCircleColor = Renderer.ColorInfo.new(0x99FFFFFF, 0x99FFFFFF, Renderer.GradientType.Solid)

    Callback.Bind(CallbackType.OnDraw, function()
        if not (Q:IsCharging() or getQPreCast() or player:FindBuff(rBuff)) then
            if menu.drawingsMenu.rangesMenu.qRange.value and Q:Level() > 0 then
                Renderer.DrawEffectCircle(Game.fnvhash("qCircle"), player.position2D, Q.range, qCircleColor, Renderer.EffectType.GlowingCircle);
            end

            if menu.drawingsMenu.rangesMenu.wRange.value and W:Level() > 0 then
                Renderer.DrawEffectCircle(Game.fnvhash("wCircle"), player.position2D, W.range, wCircleColor);
            end

            if menu.drawingsMenu.rangesMenu.eRange.value and E:Level() > 0 then
                Renderer.DrawEffectCircle(Game.fnvhash("eCircle"), player.position2D, menu.eMenu.eRange.value, eCircleColor);
            end

            if menu.drawingsMenu.rangesMenu.rRange.value and R:Level() > 0 and R:Ready() then
                Renderer.DrawEffectCircle(Game.fnvhash("rCircle"), player.position2D, 5000, rCircleColor);
            end
        end

        if player:FindBuff(rBuff) and menu.rMenu.rMouseRange.value ~= 2500 then
            Renderer.DrawEffectCircle(Game.fnvhash("rCircleMouse"), Game.GetCursorWorldPosition():To2D(), menu.rMenu.rMouseRange.value, rCircleColor);
        end

        if menu.drawingsMenu.qrDrawTarget.value then
            local target = ObjectManager.ResolveHandle(qrTargetHandle);
            if target and target:IsValidTarget() then
                if Q:IsCharging() then
                    Renderer.DrawEffectCircle(Game.fnvhash("qrTargetCircle"), target.position2D, 85, 0xFFFFFFFF, Renderer.EffectType.MagicalCircle);
                end
                if player:FindBuff(rBuff) then
                    Renderer.DrawEffectCircle(Game.fnvhash("qrTargetCircle"), target.position2D, 100, 0xFFFFFFFF, Renderer.EffectType.MagicalCircle);
                end
            end
        end

        if menu.drawingsMenu.eDrawLine.value then
            local missile = ObjectManager.ResolveHandle(eMissileHandle);
            if missile and missile:IsValid() then
                Renderer.DrawVectorPoly({ missile.position2D:Extended(missile.missileDestination:To2D(), missile.width), missile.missileDestination:To2D() }, 2, 0xFFFFFF00);
                Renderer.DrawEffectCircle(Game.fnvhash("eMissileCircle"), missile.position2D, missile.width, 0xFF33DDFF);
            end
        end

        -- if menu.drawingsMenu.passiveDraw.value and player:FindBuff(passiveBuff) then
        --     Renderer.DrawEffectCircle(Game.fnvhash("passiveCircle"), player.position2D, 100, 0xFF33DDFF);
        -- end
    end)

    local loadCallTime = Game.GetTime()
    Callback.Bind(CallbackType.OnImguiDraw, function()

        if (Game.GetTime() - loadCallTime < 12) then
            local text = "Xerath v1.1"
            local tX, tY = Renderer.CalcTextSize(text, 20)
            Renderer.DrawWorldText(text, Game.localPlayer.position, Math.Vector2(-tX / 2, 52), 20, 0xFF89FF39)
        end

        if menu.drawingsMenu.drawKillable.value and R:Ready() then
            for _, enemy in ObjectManager.enemyHeroes:pairs() do
                if enemy and Common.ValidTarget2(enemy) and enemy:IsValidTarget() then
                    local hp = enemy:GetRealHealth(2000, DamageType.Magical);
                    local r_dmg = R:GetDamage(enemy);
                    local ludens_dmg = getLudensDamage(enemy, R);
                    local r_shots_to_kill = math.ceil((hp - ludens_dmg) / r_dmg);
                    local tX, tY = Renderer.CalcTextSize(tostring(r_shots_to_kill .. "x R"), 22);

                    if r_shots_to_kill < (3 + R:Level()) then
                        Renderer.DrawWorldText(tostring(r_shots_to_kill .. "x R"), enemy.position, Math.Vector2(-tX / 2, tY + 4), 22);
                    end
                end
            end
        end

        if qWait then
            if languageIsCN then
                local text = "如敌人有突进技的话，等待敌人突进才开始Q1，以便E反突进 | %.1f"
                local timer = string.format(text, 10 - (Game.GetTickCount() - lastQwait) / 1000)
                local sizeX, sizeY = Renderer.CalcTextSize(text, 20)
                Renderer.DrawWorldText(timer, player.position, Math.Vector2(sizeX * -0.5, 52), 20, 0xFFFFFF55)
            else
                local text = "Waiting for enemy dashes if any before casting Q1, to anti-gapclose with E | %.1f"
                local timer = string.format(text, 10 - (Game.GetTickCount() - lastQwait) / 1000)
                local sizeX, sizeY = Renderer.CalcTextSize(text, 20)
                Renderer.DrawWorldText(timer, player.position, Math.Vector2(sizeX * -0.5, 52), 20, 0xFFFFFF55)
            end
        end

        if Game.GetTickCount() - eNotReady < 1400 then
            if languageIsCN then
                local text = "E 冷却中"
                local sizeX, sizeY = Renderer.CalcTextSize(text, 20)
                Renderer.DrawWorldText(text, player.position, Math.Vector2(sizeX * -0.5, 30), 20, 0xFFFF2222)
            else
                local text = "E on CD"
                local sizeX, sizeY = Renderer.CalcTextSize(text, 20)
                Renderer.DrawWorldText(text, player.position, Math.Vector2(sizeX * -0.5, 30), 20, 0xFFFF2222)
            end
        end

        if menu.drawingsMenu.drawRksFoW.value and cancelRfowTime > Game.GetTime() and R:Ready() then
            local textRfowCancel = "Recall R in " .. leftTimeRfow .. "s"
            if languageIsCN then
                textRfowCancel = "在 " .. leftTimeRfow .. " 秒後回城R"
            end

            local tX, tY = Renderer.CalcTextSize(textRfowCancel, 30)
            Renderer.DrawWorldText(textRfowCancel, Game.localPlayer.position, Math.Vector2(-tX / 2, 83), 30, 0xFFFFFFFF)
        end

    end)

    function WEPriority()
        local wCD = player:GetSpellEntry(SpellSlot.W).timeCooldownOver - Game.GetTime()
        local eCD = player:GetSpellEntry(SpellSlot.E).timeCooldownOver - Game.GetTime()
        if (E:Ready() or eCD < 0.5) or (W:Ready() or wCD < 0.5) then
            for i, enemy in ObjectManager.enemyHeroes:pairs() do
                if enemy and Common.ValidTarget2(enemy) and enemy.isAlive and enemy.isDashing then
                    local posList = enemy:GetRemainingPath2D()
                    if posList and #posList > 0 and posList[#posList] then
                        local arrivePos = posList[#posList]:To3D()
                        if Common.GetDistance2(arrivePos) < 800 and Common.GetDistance2(arrivePos) < Common.GetDistance2(enemy.position) then
                            return true
                        end
                    end
                end
            end
        end
        return false
    end

    function QHitCount(maxPos) -- any enemy
        local hitCount = 0
        for _, enemy in ObjectManager.enemyHeroes:pairs() do
            if enemy and Common.ValidTarget2(enemy) then
                local enemyPos = MovementPrediction.GetPrediction(enemy, 0.5).unitPosition
                local hit = LineHitCheck2(player.position, maxPos, 70, enemyPos)
                if hit then
                    hitCount = hitCount + 1
                end
            end
        end
        return hitCount
    end

    function MaxQCast(includeTarget) -- returns pos, hitcount, required charge range
        if includeTarget then
            local requiredMinDist = 0
            local targetPos = MovementPrediction.GetPrediction(includeTarget, 0.5).unitPosition
            local maxHitCount = 0
            local maxPos = nil
            -- Can hit main target
            if Common.GetDistance2(targetPos) < 1450 then
                -- Try the sides:
                local count = 80
                for m = 1, count, 1 do
                    local minDist = 0
                    local hitCount = 0
                    local checkPos = player.position2D:Extended(player.position2D + Math.Vector2(100, 0):Rotate(math.pi * 2 * m / count), 1450):To3D()
                    local hitIncludeTarget = LineHitCheck2(player.position, checkPos, 70, targetPos)
                    if hitIncludeTarget then
                        minDist = Common.GetDistance2(targetPos)
                        hitCount = 1
                        for _, enemy in ObjectManager.enemyHeroes:pairs() do
                            if enemy and Common.ValidTarget2(enemy) and enemy.handle ~= includeTarget.handle then
                                local enemyPos = MovementPrediction.GetPrediction(enemy, 0.5).unitPosition
                                local hit = LineHitCheck2(player.position, checkPos, 70, enemyPos)
                                if hit then
                                    hitCount = hitCount + 1
                                    if Common.GetDistance2(enemyPos) > minDist then
                                        minDist = Common.GetDistance2(enemyPos)
                                    end
                                end
                            end
                        end
                    end
                    if hitCount > maxHitCount then
                        maxHitCount = hitCount
                        maxPos = checkPos
                        requiredMinDist = minDist
                    end
                end
            end
            return maxPos, maxHitCount, requiredMinDist
        else
            local requiredMinDist = 0
            local maxHitCount = 0
            local maxPos = nil

            -- Try the sides:
            local count = 80
            for m = 1, count, 1 do
                local minDist = 0
                local hitCount = 0
                local checkPos = player.position2D:Extended(player.position2D + Math.Vector2(100, 0):Rotate(math.pi * 2 * m / count), 1450):To3D()
                for _, enemy in ObjectManager.enemyHeroes:pairs() do
                    if enemy and Common.ValidTarget2(enemy) then
                        local enemyPos = MovementPrediction.GetPrediction(enemy, 0.5).unitPosition
                        local hit = LineHitCheck2(player.position, checkPos, 70, enemyPos)
                        if hit then
                            hitCount = hitCount + 1
                            if Common.GetDistance2(enemyPos) > minDist then
                                minDist = Common.GetDistance2(enemyPos)
                            end
                        end
                    end
                end
                if hitCount > maxHitCount then
                    maxHitCount = hitCount
                    maxPos = checkPos
                    requiredMinDist = minDist
                end
            end
            return maxPos, maxHitCount, requiredMinDist
        end
    end

    function LineHitCheck2(startPos, endPos, width, hitObject)
        local x1 = startPos.x
        local x2 = endPos.x
        local y1 = startPos.z
        local y2 = endPos.z
        local px = hitObject.x
        local py = hitObject.z
        local dx, dy = x2 - x1, y2 - y1
        local length = math.sqrt(dx * dx + dy * dy)
        dx, dy = dx / length, dy / length
        local posOnLine = dx * (px - x1) + dy * (py - y1)
        if posOnLine < 0 then
            -- first end point is closest
            -- dx, dy = px - x1, py - y1
            -- local t = math.sqrt(dx * dx + dy * dy)
            -- return t < width, t
        elseif posOnLine > length then
            -- second end point is closest
            -- dx, dy = px - x2, py - y2
            -- local t = math.sqrt(dx * dx + dy * dy)
            -- return t < width, t
        else
            -- point is closest to some part in the middle of the line
            local t = (math.abs(dy * (px - x1) - dx * (py - y1)))
            if t < width then
                return true, t
            end
        end
        return false
    end

    function EnemyDashReady(range)
        local targetList = { aatrox = { SpellSlot.E }, ahri = { SpellSlot.R }, akali = { SpellSlot.E, SpellSlot.R }, akshan = { SpellSlot.E }, alistar = { SpellSlot.W }, amumu = { SpellSlot.Q }, anivia = {}, annie = {}, aphelios = {}, ashe = {}, aurelionsol = { SpellSlot.E }, azir = { SpellSlot.E }, bard = {}, blitzcrank = {}, brand = {}, braum = {}, caitlyn = { SpellSlot.E }, camille = { SpellSlot.E, SpellSlot.R }, cassiopeia = {}, chogath = {}, corki = { SpellSlot.E }, darius = {}, diana = { SpellSlot.E }, drmundo = {}, draven = {}, ekko = { SpellSlot.E }, elise = { SpellSlot.E }, evelynn = {}, ezreal = { SpellSlot.E }, fiddlesticks = { SpellSlot.R }, fiora = { SpellSlot.Q }, fizz = { SpellSlot.Q, SpellSlot.E }, galio = { SpellSlot.E }, gangplank = {}, garen = {}, gnar = { SpellSlot.E }, gragas = { SpellSlot.E }, graves = { SpellSlot.E }, gwen = { SpellSlot.E }, hecarim = { SpellSlot.E, SpellSlot.R }, heimerdinger = {}, illaoi = {}, irelia = { SpellSlot.Q }, ivern = {}, janna = {}, jarvaniv = { SpellSlot.Q, SpellSlot.R }, jax = { SpellSlot.Q }, jayce = { SpellSlot.Q }, jhin = {}, jinx = {}, kaisa = {}, kalista = {}, karma = {}, karthus = {}, kassadin = { SpellSlot.R }, katarina = { SpellSlot.E }, kayle = {}, kayn = { SpellSlot.Q }, kennen = {}, khazix = { SpellSlot.E }, kindred = { SpellSlot.Q }, kled = { SpellSlot.E }, kogmaw = {}, leblanc = { SpellSlot.W }, leesin = { SpellSlot.W }, leona = { SpellSlot.E }, lillia = {}, lissandra = { SpellSlot.E }, lucian = { SpellSlot.E }, lulu = {}, lux = {}, malphite = { SpellSlot.R }, malzahar = {}, maokai = { SpellSlot.W }, masteryi = { SpellSlot.Q }, missfortune = {}, mordekaiser = {}, morgana = {}, nami = {}, nasus = {}, nautilus = { SpellSlot.Q }, neeko = {}, nidalee = {}, nocturne = { SpellSlot.R }, nunu = {}, olaf = {}, orianna = {}, ornn = { SpellSlot.E }, pantheon = { SpellSlot.W }, poppy = { SpellSlot.E }, pyke = { SpellSlot.E }, qiyana = { SpellSlot.E }, quinn = { SpellSlot.E }, rakan = { SpellSlot.W }, rammus = {}, reksai = { SpellSlot.E }, rell = { SpellSlot.W }, renata = { 0, 0, 0, 0 }, renekton = { SpellSlot.E }, rengar = {}, riven = { SpellSlot.E }, rumble = {}, ryze = {}, samira = { SpellSlot.E }, sejuani = { SpellSlot.E }, senna = {}, seraphine = {}, sett = { SpellSlot.R }, shaco = { SpellSlot.Q }, shen = { SpellSlot.E }, shyvana = { SpellSlot.R }, singed = {}, sion = {}, sivir = {}, skarner = {}, sona = {}, soraka = {}, swain = {}, sylas = { SpellSlot.E }, syndra = {}, tahmkench = { SpellSlot.W }, taliyah = {}, talon = {}, taric = {}, teemo = {}, thresh = { SpellSlot.Q }, tristana = { SpellSlot.E }, trundle = {}, tryndamere = { SpellSlot.E }, twistedfate = {}, twitch = {}, udyr = {}, urgot = { SpellSlot.E }, varus = {}, vayne = { SpellSlot.Q }, veigar = {}, velkoz = {}, vex = { SpellSlot.R }, vi = { SpellSlot.Q, SpellSlot.R }, viego = { SpellSlot.E }, viktor = {}, vladimir = {}, volibear = { SpellSlot.R }, warwick = { SpellSlot.R }, monkeyking = { SpellSlot.E }, xayah = {}, xerath = {}, xinzhao = { SpellSlot.E }, yasuo = {}, yone = { SpellSlot.R }, yorick = {}, yuumi = {}, zac = { SpellSlot.E }, zed = { SpellSlot.R }, zeri = { SpellSlot.E }, ziggs = {}, zilean = {}, zoe = {}, zyra = {}, }
        local ready = false
        for _, enemy in ObjectManager.enemyHeroes:pairs() do
            if enemy and Common.ValidTarget2(enemy) and Common.GetDistance2(enemy.position) < range then

                if (not menu.qMenu.qWaitEnemyDashWhitelist["qWaitEnemyDashWhitelist" .. enemy.networkId] or menu.qMenu.qWaitEnemyDashWhitelist["qWaitEnemyDashWhitelist" .. enemy.networkId].value) then

                    local spellList = targetList[enemy.charName:lower()]
                    if spellList then
                        for i = 1, #spellList, 1 do
                            if spellList[i] then
                                if enemy:GetSpellEntry(spellList[i]).timeCooldownOver < Game.GetTime() then
                                    ready = true
                                end
                            end
                        end
                    end
                    if enemy.isDashing then
                        ready = true
                    end
                end
            end
        end
        return ready
    end

    Callback.Bind(CallbackType.OnUnload, function()
        if Ludens then
            Ludens:Delete()
        end
        if itemSpell then
            itemSpell:Delete()
        end
        return CallbackResult.Dispose
    end)


end

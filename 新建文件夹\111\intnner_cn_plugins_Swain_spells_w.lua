

local ove_0_10 = module.internal("orb")
local ove_0_11 = module.internal("TS")
local ove_0_12 = module.internal("pred")
local ove_0_13 = module.load(header.id, "common/common")
local ove_0_14 = module.load(header.id, "common/Compute/computer")
local ove_0_15 = module.load(header.id, "plugins/Swain/menu")
local ove_0_16 = module.load(header.id, "plugins/Swain/damage")
local ove_0_17 = {
	last = 0,
	slot = player:spellSlot(_W),
	range = {
		5500,
		6000,
		6500,
		7000,
		7500
	},
	result = {},
	pred_input = {
		radius = 162.5,
		delay = 1.53,
		boundingRadiusMod = 0,
		width = 83,
		speed = math.huge
	},
	pred_input_kill = {
		radius = 162.5,
		delay = 0.25,
		boundingRadiusMod = 0,
		width = 83,
		speed = math.huge
	},
	CrowdControls = {
		[BUFF_CHARM] = true,
		[BUFF_FEAR] = true,
		[BUFF_FLEE] = true,
		[BUFF_KNOCKUP] = true,
		[BUFF_SNARE] = true,
		[BUFF_STUN] = true,
		[BUFF_SUPPRESSION] = true,
		[BUFF_TAUNT] = true,
		[BUFF_KNOCKBACK] = true
	}
}

function ove_0_17.is_ready()
	return ove_0_17.slot.state == 0
end

function ove_0_17.get_action_state()
	if ove_0_17.is_ready() then
		return ove_0_17.get_prediction()
	end
end

function ove_0_17.invoke_action()
	player:castSpell("pos", _W, ove_0_17.result.pos:to3D())
end

function ove_0_17.get_stun_duration(arg_8_0)
	if not arg_8_0 then
		return
	end

	for iter_8_0, iter_8_1 in pairs(arg_8_0.buff) do
		if iter_8_1 and iter_8_1.valid and game.time < iter_8_1.endTime and ove_0_17.CrowdControls[iter_8_1.type] then
			return math.max(0, iter_8_1.startTime, iter_8_1.endTime) - game.time
		end
	end

	return 0
end

function ove_0_17.invoke_auto_cced()
	local slot_9_0 = ove_0_11.get_result(function(arg_10_0, arg_10_1, arg_10_2)
		if arg_10_1.isDead or arg_10_2 > ove_0_17.range[ove_0_17.slot.level] then
			return
		end

		if ove_0_17.get_stun_duration(arg_10_1) ~= 0 and ove_0_17.get_stun_duration(arg_10_1) > 0.25 - network.latency then
			arg_10_0.obj = arg_10_1
			arg_10_0.seg = arg_10_1.path.serverPos

			return true
		end
	end)

	if slot_9_0.obj and slot_9_0.seg then
		player:castSpell("pos", _W, slot_9_0.seg)

		return true
	end
end

function ove_0_17.invoke_w_killsteal()
	local slot_11_0 = ove_0_11.get_result(function(arg_12_0, arg_12_1, arg_12_2)
		if arg_12_2 > ove_0_17.range[ove_0_17.slot.level] then
			return
		end

		if not ove_0_13.isValidTarget(arg_12_1) then
			return
		end

		local slot_12_0 = ove_0_12.circular.get_prediction(ove_0_17.pred_input_kill, arg_12_1, player.path.serverPos2D)

		if not slot_12_0 then
			return
		end

		local slot_12_1 = ove_0_14.Compute(ove_0_17.pred_input_kill, slot_12_0, arg_12_1)

		if slot_12_1 <= 0 or slot_12_1 == nil or not slot_12_1 then
			return false
		end

		if ove_0_12.trace.newpath(arg_12_1, 0.25, 1.53) then
			local slot_12_2 = arg_12_1.path.serverPos + (slot_12_0.endPos:to3D() - arg_12_1.path.serverPos):norm() * (arg_12_1.moveSpeed * 0.9 - 0.25)

			if slot_12_0 and slot_12_0.startPos:distSqr(slot_12_0.endPos) < ove_0_17.range[ove_0_17.slot.level] * ove_0_17.range[ove_0_17.slot.level] and ove_0_16.get_w_damage(arg_12_1) > ove_0_13.GetShieldedHealth("AP", arg_12_1) then
				arg_12_0.obj = arg_12_1
				arg_12_0.seg = slot_12_2

				return true
			end
		end
	end)

	if slot_11_0.obj and ove_0_13.isValidTarget(slot_11_0.obj) and ove_0_13.IsEnemyMortal(slot_11_0.obj) then
		player:castSpell("pos", _W, slot_11_0.seg)

		return true
	end
end

function ove_0_17.invoke_lane_hit()
	for iter_13_0, iter_13_1 in pairs(ove_0_13.GetEnemyMinionsInRange(5000, TEAM_ENEMY, player.pos)) do
		if iter_13_1 and iter_13_1.moveSpeed > 0 and iter_13_1.isTargetable and not iter_13_1.isDead and iter_13_1.isVisible and player.path.serverPos:distSqr(iter_13_1.path.serverPos) <= 25000000 then
			local slot_13_0 = 0

			for iter_13_2, iter_13_3 in pairs(ove_0_13.GetEnemyMinionsInRange(5000, TEAM_ENEMY, player.pos)) do
				if iter_13_3 and iter_13_3.moveSpeed > 0 and iter_13_3.isTargetable and iter_13_3 ~= iter_13_1 and not iter_13_3.isDead and iter_13_3.isVisible and iter_13_3.path.serverPos:distSqr(iter_13_1.path.serverPos) <= 105625 then
					slot_13_0 = slot_13_0 + 1
				end

				if slot_13_0 >= ove_0_15.farming.lane.w.min_minions:get() then
					local slot_13_1 = ove_0_12.circular.get_prediction(ove_0_17.pred_input, iter_13_1)

					if slot_13_1 and slot_13_1.startPos:dist(slot_13_1.endPos) < 5000 then
						player:castSpell("pos", _W, iter_13_1.pos)

						break
					end
				end
			end
		end
	end
end

function ove_0_17.trace_filter()
	if ove_0_15.combo.w.w_mode:get() == 2 and ove_0_17.result.seg.endPos:dist(ove_0_17.result.obj) > 100 then
		if player.pos:dist(ove_0_17.result.obj.path.serverPos) > player.pos:dist(ove_0_17.result.obj.pos) then
			if ove_0_17.result.obj.pos2D:dist(player.path.serverPos2D) < ove_0_17.result.obj.pos2D:dist(player.pos2D) then
				return true
			end
		elseif ove_0_17.result.obj.pos2D:dist(player.path.serverPos2D) > ove_0_17.result.obj.pos:dist(player.pos) then
			return true
		end
	end
end

function ove_0_17.get_prediction()
	ove_0_17.last = game.time
	ove_0_17.result.obj = nil
	ove_0_17.result.seg = nil
	ove_0_17.result.pos = nil
	ove_0_17.result = ove_0_11.get_result(function(arg_16_0, arg_16_1, arg_16_2)
		if arg_16_2 > ove_0_17.range[ove_0_17.slot.level] then
			return
		end

		if not ove_0_13.isValidTarget(arg_16_1) then
			return
		end

		local slot_16_0 = ove_0_12.circular.get_prediction(ove_0_17.pred_input, arg_16_1, player.path.serverPos2D)

		if not slot_16_0 then
			return
		end

		local slot_16_1 = ove_0_14.Compute(ove_0_17.pred_input, slot_16_0, arg_16_1)

		if slot_16_1 < 0 then
			return false
		end

		if slot_16_0 and slot_16_0.startPos:distSqr(slot_16_0.endPos) < ove_0_17.range[ove_0_17.slot.level] * ove_0_17.range[ove_0_17.slot.level] then
			arg_16_0.obj = arg_16_1
			arg_16_0.seg = slot_16_0
			arg_16_0.pos = (ove_0_12.core.get_pos_after_time(arg_16_1, slot_16_1) + slot_16_0.endPos) / 2
			arg_16_0.time = slot_16_1

			return true
		end
	end)

	if ove_0_17.result.seg and ove_0_17.result.pos and ove_0_17.trace_filter() then
		return ove_0_17.result
	end
end

function ove_0_17.on_draw()
	if ove_0_15.draws.usee:get() and ove_0_17.slot.level > 0 then
		minimap.draw_circle(player.pos, ove_0_17.range[ove_0_17.slot.level], 1, ove_0_15.draws.e:get(), 100)
	end
end

return ove_0_17



-- local ove_0_10 = os.time({
	-- hour = 23,
	-- month = 4,
	-- year = 2027,
	-- min = 59,
	-- sec = 59,
	-- day = 12
-- })

-- if os.time() - ove_0_10 > 0 then
	-- print("<PERSON> outdated.")

	-- return
-- end

-- if game.version:find("15.8") == nil then
	-- return
-- end

module.load("<PERSON>", "Irelia/orbwalker").Add()

-- if player.charName == "Yasuo" then
	-- module.load("RexAio", "champions/yasuo").Add()
-- end

--module.load(header.id, "hb/champions/" .. player.charName .. "/main")
module.load("<PERSON>", "Irelia/champions/irelia").Add()


print("BSAio Load.")

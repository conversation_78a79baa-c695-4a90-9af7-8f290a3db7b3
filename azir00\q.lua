
local ove_0_5 = module.internal("orb")
local ove_0_6 = module.internal("pred")
local ove_0_7 = module.internal("TS")
local ove_0_8 = module.load("<PERSON>","azir/w")
local ove_0_9 = module.load("<PERSON>","azir/menu")
local ove_0_10 = {
	speed = 1000,
	range = 1100,
	delay = 0,
	boundingRadiusMod = 1,
	width = 70
}
local ove_0_11 = {
	delay = 0.25,
	boundingRadiusMod = 1,
	width = 70,
	speed = 1000
}

local function ove_0_12(arg_1_0)
	-- print 1
	local slot_1_0
	local slot_1_1 = math.huge

	for iter_1_0 = 1, #ove_0_8.soldiers do
		local slot_1_2 = ove_0_8.soldiers[iter_1_0].path.serverPos2D
		local slot_1_3 = slot_1_2:distSqr(arg_1_0)

		if not slot_1_0 or slot_1_3 < slot_1_1 then
			slot_1_0, slot_1_1 = vec2(slot_1_2.x, slot_1_2.y), slot_1_3
		end
	end

	return slot_1_0
end

local function ove_0_13(arg_2_0, arg_2_1, arg_2_2)
	-- print 2
	if arg_2_2 < ove_0_10.range then
		local slot_2_0 = ove_0_12(arg_2_1.path.serverPos2D)

		if slot_2_0 and slot_2_0:dist(arg_2_1.path.serverPos2D) < ove_0_10.range then
			local slot_2_1 = ove_0_6.linear.get_prediction(ove_0_10, arg_2_1, slot_2_0)

			if slot_2_1 and slot_2_1.endPos then
				local slot_2_2 = #ove_0_8.soldiers
				local slot_2_3 = slot_2_1.endPos:dist(slot_2_1.startPos)

				if slot_2_2 == 1 or slot_2_2 > 2 then
					arg_2_0.start_pos = slot_2_0
					arg_2_0.end_pos = slot_2_1.startPos:lerp(slot_2_1.endPos, (slot_2_3 + arg_2_1.boundingRadius) / slot_2_3)
					arg_2_0.type = "cast_single"

					return true
				elseif slot_2_2 == 2 then
					arg_2_0.start_pos = slot_2_0
					arg_2_0.end_pos = slot_2_1.startPos:lerp(slot_2_1.endPos, (slot_2_3 + arg_2_1.boundingRadius) / slot_2_3)
					arg_2_0.type = "cast_double"

					return true
				end
			end
		elseif player:spellSlot(1).state == 0 and player.par > 110 then
			local slot_2_4 = player.path.serverPos2D + (arg_2_1.path.serverPos2D - player.path.serverPos2D):norm() * (arg_2_2 > 500 and 500 or arg_2_2 - arg_2_1.boundingRadius)
			local slot_2_5 = ove_0_6.linear.get_prediction(ove_0_11, arg_2_1, slot_2_4)

			if slot_2_5 and slot_2_5.endPos then
				local slot_2_6 = #ove_0_8.soldiers + 1
				local slot_2_7 = slot_2_5.endPos:dist(slot_2_5.startPos)

				if slot_2_6 == 1 or slot_2_6 > 2 then
					arg_2_0.start_pos = slot_2_4
					arg_2_0.end_pos = slot_2_5.startPos:lerp(slot_2_5.endPos, (slot_2_7 + arg_2_1.boundingRadius) / slot_2_7)
					arg_2_0.cast_w = true
					arg_2_0.type = "cast_single"

					return true
				elseif slot_2_6 == 2 then
					arg_2_0.start_pos = slot_2_4
					arg_2_0.end_pos = slot_2_5.startPos:lerp(slot_2_5.endPos, (slot_2_7 + arg_2_1.boundingRadius) / slot_2_7)
					arg_2_0.cast_w = true
					arg_2_0.type = "cast_double"

					return true
				end
			end
		end
	end

	return false
end

local function ove_0_14(arg_3_0, arg_3_1, arg_3_2)
	-- print 3
	local slot_3_0 = arg_3_1
	local slot_3_1 = player.path.serverPos2D
	local slot_3_2 = math.min(slot_3_1:dist(slot_3_0), 770)

	if slot_3_2 == 770 then
		local slot_3_3, slot_3_4 = mathf.sect_line_circle(arg_3_0, slot_3_0, slot_3_1, 770)

		if slot_3_4 then
			slot_3_0 = slot_3_4
		end
	end

	if arg_3_0:dist(arg_3_1) > arg_3_0:dist(slot_3_0) + 420 then
		return false
	end

	local slot_3_5 = slot_3_1:lerp(slot_3_0, (slot_3_2 - 50) / slot_3_2)

	if arg_3_2 then
		player:castSpell("pos", 1, vec3(arg_3_0.x, player.y, arg_3_0.y))
	end

	if player:castSpell("pos", 0, vec3(slot_3_5.x, player.y, slot_3_5.y)) then
		ove_0_5.core.set_server_pause()

		return true
	end
end

local function ove_0_15(arg_4_0, arg_4_1, arg_4_2)
	-- print 4
	local slot_4_0 = player.path.serverPos2D

	if arg_4_0:distSqr(arg_4_1) > slot_4_0:distSqr(arg_4_1) then
		return false
	end

	local slot_4_1 = vec2(arg_4_1.x, arg_4_1.y)

	if slot_4_0:dist(arg_4_1) > 770 then
		local slot_4_2, slot_4_3 = mathf.sect_line_circle(arg_4_0, slot_4_1, slot_4_0, 770)

		if slot_4_3 then
			slot_4_1 = slot_4_3
		end
	end

	local slot_4_4 = (slot_4_1 - slot_4_0):norm()
	local slot_4_5 = slot_4_4:perp1()
	local slot_4_6 = slot_4_0 + slot_4_4 * (slot_4_1:dist(slot_4_0) - 50) + slot_4_5 * 100

	if slot_4_0:dist(slot_4_6) > 770 then
		local slot_4_7, slot_4_8 = mathf.sect_line_circle(arg_4_0, slot_4_6, slot_4_0, 770)

		if slot_4_8 then
			slot_4_6 = slot_4_8
		end
	end

	if arg_4_0:dist(arg_4_1) > arg_4_0:dist(slot_4_6) + 420 then
		return false
	end

	if arg_4_2 then
		player:castSpell("pos", 1, vec3(arg_4_0.x, player.y, arg_4_0.y))
	end

	if player:castSpell("pos", 0, vec3(slot_4_6.x, player.y, slot_4_6.y)) then
		ove_0_5.core.set_server_pause()

		return true
	end
end

local function ove_0_16(arg_5_0, arg_5_1, arg_5_2)
	-- print 5
	if arg_5_0:dist(arg_5_2) < 770 then
		local slot_5_0 = (arg_5_1 - arg_5_0):norm()

		return arg_5_0 - slot_5_0 * 50, arg_5_0 + slot_5_0 * 370
	elseif arg_5_1:dist(arg_5_2) < 770 then
		local slot_5_1 = (arg_5_0 - arg_5_1):norm()

		return arg_5_1 - slot_5_1 * 50, arg_5_1 + slot_5_1 * 370
	end
end

local function ove_0_17()
	-- print 6
	if ove_0_9.q.combat:get() and player:spellSlot(0).state == 0 then
		local slot_6_0 = ove_0_7.get_result(ove_0_13)

		if slot_6_0.type == "cast_single" then
			return ove_0_14(slot_6_0.start_pos, slot_6_0.end_pos, slot_6_0.cast_w)
		elseif slot_6_0.type == "cast_double" then
			return ove_0_15(slot_6_0.start_pos, slot_6_0.end_pos, slot_6_0.cast_w)
		end
	end
end

local function ove_0_18(arg_7_0)
	-- print 7
	if ove_0_9.q.lane_clear:get() and player:spellSlot(0).state == 0 and #ove_0_8.soldiers > 0 then
		local slot_7_0 = {}

		for iter_7_0 = 0, objManager.minions.size[arg_7_0] - 1 do
			local slot_7_1 = objManager.minions[arg_7_0][iter_7_0]

			if slot_7_1.isVisible and slot_7_1.health > 100 and slot_7_1.pos2D:dist(player.pos2D) < 770 then
				table.insert(slot_7_0, slot_7_1.path.active and slot_7_1.pos2D + (slot_7_1.path.point2D[slot_7_1.path.count] - slot_7_1.pos2D):norm() * 50 or vec2(slot_7_1.x, slot_7_1.z))
			end
		end

		if #slot_7_0 > 1 then
			local slot_7_2 = ove_0_6.present.get_source_pos(player)
			local slot_7_3 = 0
			local slot_7_4

			for iter_7_1, iter_7_2 in ipairs(slot_7_0) do
				for iter_7_3, iter_7_4 in ipairs(slot_7_0) do
					local slot_7_5, slot_7_6 = ove_0_16(iter_7_2, iter_7_4, slot_7_2)

					if slot_7_5 and slot_7_6 then
						local slot_7_7 = 0

						for iter_7_5, iter_7_6 in ipairs(slot_7_0) do
							local slot_7_8 = ((iter_7_6.x - slot_7_5.x) * (slot_7_6.x - slot_7_5.x) + (iter_7_6.y - slot_7_5.y) * (slot_7_6.y - slot_7_5.y)) / ((slot_7_6.x - slot_7_5.x)^2 + (slot_7_6.y - slot_7_5.y)^2)

							if slot_7_8 >= 0 and slot_7_8 <= 1 and (slot_7_5 + (slot_7_6 - slot_7_5) * slot_7_8):dist(iter_7_6) < 128 then
								slot_7_7 = slot_7_7 + 1
							end
						end

						if not slot_7_4 or slot_7_3 < slot_7_7 then
							slot_7_3, slot_7_4 = slot_7_7, slot_7_5
						end
					end
				end
			end

			if slot_7_4 and (slot_7_3 > 2 or slot_7_3 >= math.max(math.min(3, #slot_7_0 * 0.55), 2) or #slot_7_0 == slot_7_3) and player:castSpell("pos", 0, vec3(slot_7_4.x, mousePos.y, slot_7_4.y)) then
				ove_0_5.core.set_server_pause()

				return true
			end
		elseif #slot_7_0 == 1 and not ove_0_5.core.can_attack() and player:castSpell("pos", 0, vec3(slot_7_0[1].x, mousePos.y, slot_7_0[1].y)) then
			ove_0_5.core.set_server_pause()

			return true
		end
	end
end

local function ove_0_19(arg_8_0)
	-- print 8
	if arg_8_0.name == "AzirQ" then
		ove_0_5.core.reset()
	end
end

local function ove_0_20()
	-- print 9
	if ove_0_9.q.draw_range:get() and not player.isDead and player.isOnScreen then
		graphics.draw_circle(player.pos, ove_0_10.range, 2, ove_0_9.q.draw_color:get(), 48)
	end
end

return {
	invoke = ove_0_17,
	invoke_lane_clear = ove_0_18,
	on_process_spell = ove_0_19,
	on_draw = ove_0_20
}

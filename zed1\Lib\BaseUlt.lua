
local ove_0_10 = module.load("Kloader", "<PERSON>b/MyCommon")
local ove_0_11 = module.load("Kloader", "Lib/SpellDmg")
local ove_0_12 = module.load("Kloader", "Lib/DelayAction")
local ove_0_13 = player
local ove_0_14 = {
	concat = assert(table.concat),
	insert = assert(table.insert),
	remove = assert(table.remove),
	sort = assert(table.sort)
}

local function ove_0_15(arg_5_0, arg_5_1, arg_5_2)
	-- print 5
	for iter_5_0, iter_5_1 in pairs(arg_5_0) do
		if arg_5_2 and iter_5_1[arg_5_2] == arg_5_1 or iter_5_1 == arg_5_1 then
			return iter_5_0, iter_5_1
		end
	end
end

local ove_0_16 = ove_0_10.Class()
local ove_0_17 = module.seek("evade")
local ove_0_18 = ove_0_10.GetEnemyHeroes()
local ove_0_19 = module.internal("orb")
local ove_0_20 = {}

for iter_0_0 = 1, #ove_0_18 do
	ove_0_20[#ove_0_20 + 1] = {
		recall = false,
		timer = 0,
		name = "",
		needTime = 0,
		delay = 0,
		cankill = false,
		hero = ove_0_18[iter_0_0]
	}
end

local ove_0_21

if player.team == 100 then
	ove_0_21 = vec3(14300, 172, 14386)
else
	ove_0_21 = vec3(396, 182, 462)
end

function ove_0_16.__init(arg_6_0, arg_6_1)
	-- print 6
	arg_6_0.menu = arg_6_1
	arg_6_0.ezrealCol = 0.7
	arg_6_0.SpellData = {
		Ashe = {
			Width = 130,
			MissileSpeed = 1600,
			Delay = 0.25,
			Damage = function(arg_7_0)
				-- print 7
				return arg_6_0:Collision(arg_7_0, arg_6_0.SpellData[ove_0_13.charName].Width) == 0 and ove_0_11.CMD(arg_7_0, player:spellSlot(3).level * 200 + ove_0_13.flatMagicDamageMod) or 0
			end
		},
		Ezreal = {
			MissileSpeed = 2000,
			Delay = 1,
			Damage = function(arg_8_0)
				-- print 8
				return ove_0_11.CMD(arg_8_0, (200 + player:spellSlot(3).level * 150 + ove_0_13.flatMagicDamageMod * 0.9 + ove_0_13.flatPhysicalDamageMod) * arg_6_0.ezrealCol)
			end
		}
	}
	arg_6_0.Delay = arg_6_0.SpellData[ove_0_13.charName].Delay
	arg_6_0.Speed = arg_6_0.SpellData[ove_0_13.charName].MissileSpeed
	arg_6_0.Damage = arg_6_0.SpellData[ove_0_13.charName].Damage
	arg_6_0.EnemyData = {}

	for iter_6_0 = 1, #ove_0_18 do
		local slot_6_0 = ove_0_18[iter_6_0]

		arg_6_0.EnemyData[slot_6_0.networkID] = 0
	end

	if arg_6_1 then
		arg_6_0.menu:boolean("use", "Use BaseUlt", false)
		arg_6_0.menu:boolean("inCombo", "Do not use in combo", true)
		arg_6_0.menu:boolean("useDraw", "Use Drawings", true)
	end

	cb.add(cb.tick, function()
		-- print 9
		arg_6_0:OnTick()
	end)
	cb.add(cb.draw, function()
		-- print 10
		arg_6_0:OnDraw()
	end)
end

function ove_0_16.OnTick(arg_11_0)
	-- print 11
	if ove_0_17 and ove_0_17.core.is_active() or not arg_11_0.menu.use:get() then
		return
	end

	for iter_11_0, iter_11_1 in pairs(ove_0_18) do
		if iter_11_1.isVisible then
			arg_11_0.EnemyData[iter_11_1.networkID] = game.time
		end
	end

	for iter_11_2 = 1, #ove_0_20 do
		local slot_11_0 = ove_0_20[iter_11_2]
		local slot_11_1 = slot_11_0.hero

		if slot_11_0.recall and slot_11_0.hero.isRecalling then
			if arg_11_0:GetPredictedHealth(slot_11_1, slot_11_0.delay) < arg_11_0.Damage(slot_11_1) and game.time + arg_11_0:GetTimeToBase() > slot_11_0.timer and game.time + arg_11_0:GetTimeToBase() < slot_11_0.timer + 0.25 and player:spellSlot(3).state == 0 and arg_11_0:checkCombo() then
				slot_11_0.cankill = true

				player:castSpell("pos", 3, ove_0_21)
			end
		else
			arg_11_0:GetData()
		end
	end
end

function ove_0_16.checkCombo(arg_12_0)
	-- print 12
	return not arg_12_0.menu.inCombo:get() or not ove_0_19.combat.is_active()
end

function ove_0_16.GetData(arg_13_0)
	-- print 13
	for iter_13_0 = 1, #ove_0_20 do
		local slot_13_0 = ove_0_20[iter_13_0]
		local slot_13_1 = slot_13_0.hero.recallName

		if slot_13_1 ~= "" then
			if not slot_13_0.recall then
				if slot_13_1 == "recall" then
					slot_13_0.recall = true
					slot_13_0.timer = game.time + 8
					slot_13_0.delay = 8
					slot_13_0.name = "Recall"
				elseif slot_13_1 == "SuperRecall" then
					slot_13_0.recall = true
					slot_13_0.timer = game.time + 4
					slot_13_0.delay = 4
					slot_13_0.name = "SuperRecall"
				end
			end
		else
			slot_13_0.recall = false
			slot_13_0.cankill = false
		end
	end
end

function ove_0_16.OnDraw(arg_14_0)
	-- print 14
	for iter_14_0 = 1, #ove_0_20 do
		local slot_14_0 = ove_0_20[iter_14_0]

		if slot_14_0.hero.isRecalling and slot_14_0.recall and arg_14_0.menu.useDraw:get() then
			local slot_14_1 = graphics.width * 42 / 100
			local slot_14_2 = graphics.height * 81 / 100 + (iter_14_0 - 2) * 30
			local slot_14_3 = slot_14_0.timer - game.time
			local slot_14_4 = slot_14_1 - 100
			local slot_14_5 = slot_14_0.cankill and graphics.argb(255, 205, 92, 92) or graphics.argb(255, 143, 188, 143)

			graphics.draw_line_2D(slot_14_4, slot_14_2, slot_14_1 + 250, slot_14_2, 15, graphics.argb(255, 255, 250, 240))
			graphics.draw_line_2D(slot_14_4, slot_14_2, slot_14_4 + 350 * slot_14_3 / slot_14_0.delay, slot_14_2, 15, slot_14_5)
			graphics.draw_text_2D(slot_14_0.hero.charName .. " is recalling: " .. tostring(tonumber(string.format("%.1f", slot_14_3))) .. "s", 12, slot_14_1, slot_14_2, graphics.argb(255, 0, 0, 0), "center", "middle")
		end
	end
end

function ove_0_16.GetTimeToBase(arg_15_0)
	-- print 15
	return ove_0_10.GetDistance(ove_0_13, ove_0_21) / arg_15_0.Speed + arg_15_0.Delay
end

function ove_0_16.GetPredictedHealth(arg_16_0, arg_16_1, arg_16_2)
	-- print 16
	local slot_16_0 = 0

	if arg_16_1.charName == "Yasuo" and arg_16_1.mana == arg_16_1.maxMana then
		slot_16_0 = ({
			100,
			105,
			110,
			115,
			120,
			130,
			140,
			150,
			165,
			180,
			200,
			225,
			255,
			290,
			330,
			380,
			440,
			510
		})[arg_16_1.levelRef]
	end

	if arg_16_1.isVisible then
		return arg_16_1.health + slot_16_0
	end

	return math.min(arg_16_1.maxHealth + slot_16_0, arg_16_1.health + slot_16_0 + arg_16_1.healthRegenRate * (game.time - arg_16_0.EnemyData[arg_16_1.networkID] + arg_16_2))
end

function ove_0_16.Collision(arg_17_0, arg_17_1, arg_17_2)
	-- print 17
	local slot_17_0 = 0

	for iter_17_0, iter_17_1 in pairs(ove_0_18) do
		if iter_17_1 and iter_17_1.isVisible and not iter_17_1.isDead and arg_17_1.networkID ~= iter_17_1.networkID then
			local slot_17_1, slot_17_2, slot_17_3 = ove_0_10.PointSector(ove_0_13.path.serverPos, ove_0_21, iter_17_1.path.serverPos)

			if slot_17_3 and ove_0_10.GetDistance(slot_17_1, iter_17_1) < arg_17_2 + iter_17_1.boundingRadius and ove_0_10.GetDistance(ove_0_13, ove_0_21) > ove_0_10.GetDistance(ove_0_13, iter_17_1) then
				slot_17_0 = slot_17_0 + 1
			end
		end
	end

	return slot_17_0
end

return ove_0_16

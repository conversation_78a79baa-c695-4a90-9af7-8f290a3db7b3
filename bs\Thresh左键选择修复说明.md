# Thresh 左键选择目标修复说明

## 问题描述
Thresh无法对左键选择的敌人使用Q技能，即使左键点击了敌人，Thresh也不会优先Q那个目标。

## 问题原因
BS框架的目标选择系统(TS)缺少鼠标事件处理功能，无法捕获左键点击事件来设置焦点目标。

## 修复内容

### 1. 添加鼠标事件处理 (`bs/ts/TS.lua`)
- 新增 `mouse_down` 函数来处理左键点击事件
- 当左键点击敌人时，自动设置为焦点目标 (`TS.focus`)
- 只在启用"焦点选择目标"选项时生效

### 2. 集成鼠标事件到主框架 (`bs/Lib/main.lua`)
- 在主框架的 `mouse_down` 函数中调用TS模块的鼠标事件处理
- 确保鼠标事件能正确传递到TS系统

### 3. 优化Thresh Q技能逻辑 (`bs/aio/champion/Thresh.lua`)
- 修复目标选择条件逻辑
- 优先检查左键选中的目标
- 改进黑名单逻辑，确保左键选择能覆盖黑名单设置

### 4. 添加视觉反馈
- 为左键选中的目标添加黄色圆圈标记
- 显示"焦点目标"文字提示
- 便于用户确认选择是否生效

## 使用方法

### 1. 启用功能
1. 打开TS菜单 (通常在脚本主菜单中)
2. 找到"焦点"或"Focus"设置
3. 启用"焦点选择目标"/"Focus Select Target"选项
4. 启用"绘制目标"/"Draw target"选项 (可选，用于显示选中标记)

### 2. 使用左键选择
1. 左键点击想要Q的敌人
2. 被选中的敌人会显示黄色圆圈和"焦点目标"文字
3. Thresh会优先对该目标使用Q技能
4. 再次左键点击其他位置可以取消选择

## 技术细节

### 鼠标事件处理逻辑
```lua
function ove_0_7.mouse_down(arg_26_1)
    if arg_26_1 == 1 and ove_0_6.focusmenu.focusselect:get() then -- 左键且启用焦点选择
        -- 寻找鼠标附近的敌人
        for iter_26_0, iter_26_1 in ipairs(common.GetEnemyHeroes()) do
            if iter_26_1 and common.isTarget(iter_26_1) then
                local distance = iter_26_1.pos:dist(game.mousePos)
                if distance < iter_26_1.boundingRadius * 1.5 then
                    ove_0_7.focus = iter_26_1 -- 设置为焦点目标
                    break
                end
            end
        end
    end
end
```

### Thresh Q技能优先级
1. **左键选中目标** - 最高优先级
2. 常规目标选择算法
3. 黑名单过滤

## 注意事项
- 需要在TS菜单中启用"焦点选择目标"功能
- 左键选择会覆盖黑名单设置
- 目标死亡或不可见时会自动清除选择
- 功能与其他英雄的脚本兼容

## 兼容性
- ✅ 与现有TS系统完全兼容
- ✅ 不影响其他英雄脚本
- ✅ 保持原有的目标选择逻辑
- ✅ 支持中英文界面

## 测试建议
1. 进入训练模式或自定义游戏
2. 启用Thresh脚本和TS焦点选择功能
3. 左键点击敌人，确认出现黄色标记
4. 使用连招，观察Q技能是否优先攻击选中目标
5. 测试黑名单功能是否被正确覆盖

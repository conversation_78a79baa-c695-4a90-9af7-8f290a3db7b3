local lvxbot = module.load(header.id, 'lvxbot/main')

local input = {
  prediction = {
    type = 'Circular',
    --
    range = 900,
    delay = 1.25,
    speed = math.huge,
    radius = 235,
    boundingRadiusMod = 0,
    --
      collision = {
      hero = false, --allow to hit other heros :-)
      minion = false,
      wall = true,
    },
    --
    hitchance = 0,
  },

  target_selector = {
    type = 'LESS_CAST_AD',
  },

  cast_spell = {
    type = 'pos',
    slot = _E,
    arg1 = function(action)
      local seg = action.ts_result.seg
      local obj = action.ts_result.obj
      return seg.endPos:to3D(obj.y)
    end,
  },

  slot = _E,
  ignore_obj_radius = 2000,

}

return lvxbot.Nidaleeexpert.create(input)

﻿--local var_0_0 = module.load("<PERSON>", "Utility/header")

--if var_0_0.riot == false then
	--console.set_color(79)
	--print("Only for NeverDie! c:")
--end

--if var_0_0.riot == false then
	--return
--end

local var_0_1 = "1.0"
local var_0_2 = module.load("<PERSON>", "Utility/kcommon")
local var_0_3 = module.load("<PERSON>", "Utility/SpellDatabaseSupport")
local var_0_4 = module.seek("evade")
local var_0_5 = module.internal("pred")
local var_0_6 = module.internal("TS")
local var_0_7 = module.internal("orb")
local var_0_8 = {
	speed = 1400,
	range = 825,
	delay = 0,
	boundingRadiusMod = 1,
	width = 80,
	collision = {
		minion = false,
		wall = true,
		hero = false
	}
}
local var_0_9 = {
	range = 230
}
local var_0_10 = {
	speed = 1850,
	range = 1100,
	delay = 0.25,
	boundingRadiusMod = 1,
	width = 80,
	collision = {
		minion = false,
		wall = true,
		hero = false
	}
}
local var_0_11 = {
	speed = 1700,
	range = 1100,
	delay = 0.25,
	boundingRadiusMod = 1,
	width = 80,
	collision = {
		minion = false,
		wall = true,
		hero = true
	}
}
local var_0_12 = {
	radius = 350,
	range = 400
}
local var_0_13 = {
	"ShenE",
	"JaxLeapStrike",
	"AatroxQ",
	"AkaliShadowDance",
	"HeadButt",
	"BandageToss",
	"DianaTeleport",
	"EkkoE",
	"EliseSpidereInitial",
	"CamilleE",
	"KledR",
	"KledE",
	"IvernQ",
	"IllaoiW",
	"Crowstorm",
	"FioraQ",
	"GnarE",
	"GnarBigE",
	"GragasE",
	"HecarimUlt",
	"IreliaGatotsu",
	"JarvanIVCataclysm",
	"JarvanIVDragonStrike",
	"KatarinaE",
	"KennenLightningRush",
	"KhazixE",
	"LeblancSlide",
	"LeblancSlideM",
	"LeonaZenithBlade",
	"MaokaiUnstableGrowth",
	"NocturneParanoia",
	"OlafRagnarok",
	"SionR",
	"RengarR",
	"ShyvanaTransformCast",
	"ShyvanaTransformLeap",
	"ThreshQLeap",
	"WarwickR",
	"GallioE",
	"ZacE",
	"LucianE",
	"MonkeyKingNimbus",
	"NautilusAnchorDrag",
	"Pantheon_LeapBash",
	"PoppyHeroicCharge",
	"QuinnE",
	"RenektonSliceAndDice",
	"RiftWalk",
	"RivenTriCleave",
	"RocketJump",
	"SejuaniArcticAssault",
	"TalonCutThroat",
	"UFSlash",
	"KatarinaE",
	"Valkyrie",
	"ViQ",
	"ViR",
	"VolibearQ",
	"XenZhaoSweep",
	"YasuoDashWrapper",
	"blindmonkqtwo",
	"khazixelong",
	"reksaieburrowed",
	"TryndamereE"
}
local var_0_14 = {
	anivia = {
		{
			spellname = "glacialstorm",
			menuslot = "R",
			slot = 3,
			channelduration = 6
		}
	},
	caitlyn = {
		{
			spellname = "caitlynaceinthehole",
			menuslot = "R",
			slot = 3,
			channelduration = 1
		}
	},
	ezreal = {
		{
			spellname = "ezrealtrueshotbarrage",
			menuslot = "R",
			slot = 3,
			channelduration = 1
		}
	},
	fiddlesticks = {
		{
			spellname = "crowstorm",
			menuslot = "R",
			slot = 3,
			channelduration = 1.5
		}
	},
	janna = {
		{
			spellname = "reapthewhirlwind",
			menuslot = "R",
			slot = 3,
			channelduration = 3
		}
	},
	karthus = {
		{
			spellname = "karthusfallenone",
			menuslot = "R",
			slot = 3,
			channelduration = 3
		}
	},
	katarina = {
		{
			spellname = "katarinar",
			menuslot = "R",
			slot = 3,
			channelduration = 2.5
		}
	},
	lucian = {
		{
			spellname = "lucianr",
			menuslot = "R",
			slot = 3,
			channelduration = 2
		}
	},
	lux = {
		{
			spellname = "luxmalicecannon",
			menuslot = "R",
			slot = 3,
			channelduration = 0.5
		}
	},
	malzahar = {
		{
			spellname = "malzaharr",
			menuslot = "R",
			slot = 3,
			channelduration = 2.5
		}
	},
	missfortune = {
		{
			spellname = "missfortunebullettime",
			menuslot = "R",
			slot = 3,
			channelduration = 3
		}
	},
	nunu = {
		{
			spellname = "absolutezero",
			menuslot = "R",
			slot = 3,
			channelduration = 3
		}
	},
	pantheon = {
		{
			spellname = "pantheonrjump",
			menuslot = "R",
			slot = 3,
			channelduration = 2
		}
	},
	shen = {
		{
			spellname = "shenr",
			menuslot = "R",
			slot = 3,
			channelduration = 3
		}
	},
	twistedfate = {
		{
			spellname = "gate",
			menuslot = "R",
			slot = 3,
			channelduration = 1.5
		}
	},
	warwick = {
		{
			spellname = "warwickr",
			menuslot = "R",
			slot = 3,
			channelduration = 1.5
		}
	},
	xerath = {
		{
			spellname = "xerathlocusofpower2",
			menuslot = "R",
			slot = 3,
			channelduration = 3
		}
	}
}
local var_0_15 = {
	[0] = "Q",
	"W",
	"E",
	[-1] = "P",
	[3] = "R"
}
local var_0_16 = menu("Brian" .. player.charName, "[Brian] " .. player.charName)

var_0_16:menu("combo", "Combo")
var_0_16.combo:header("qset", " -- Q Settings -- ")
var_0_16.combo:boolean("qcombo", "Use Q in Combo", true)
var_0_16.combo:boolean("autoq", "Auto Q on Dashes", true)
var_0_16.combo:boolean("slowq", "Slow Predictions", false)
var_0_16.combo:header("wset", " -- W Settings -- ")
var_0_16.combo:boolean("wcombo", "Use W in Combo", true)
var_0_16.combo:header("eset", " -- E Settings -- ")
var_0_16.combo:boolean("ecombo", "Use E in Combo", true)
var_0_16.combo:boolean("eint", " ^- Auto E on Initiating Ally", true)
var_0_16.combo:boolean("ecombo2", "Use E if can hit Enemy", true)
var_0_16.combo:keybind("magnete", "Magnet E Key", "A", nil)
var_0_16.combo:boolean("include", " ^- Include Self", false)
var_0_16.combo.magnete:set("tooltip", " Casts E to Closest Ally to Mouse")
var_0_16.combo.eint:set("tooltip", "Automatically uses E to Boost Ally. ( Rengar R and so on.. ) ")
var_0_16.combo:header("rset", " -- R Settings -- ")
var_0_16.combo:boolean("rcombo", "Use R in Combo", true)
var_0_16.combo:slider("hitr", " ^- if Hits X Enemies", 2, 1, 5, 1)
var_0_16.combo:boolean("forcer", "Force R if Killable", true)
var_0_16:menu("SpellsMenu", "Auto Shielding")
var_0_16.SpellsMenu:boolean("enable", "Enable Shielding", true)
var_0_16.SpellsMenu:slider("mana", "Mana Manager", 20, 0, 100, 5)
var_0_16.SpellsMenu:menu("blacklist", "Ally Shield Blacklist")

local var_0_17 = var_0_2.GetAllyHeroes()

for iter_0_0, iter_0_1 in ipairs(var_0_17) do
	var_0_16.SpellsMenu.blacklist:boolean(iter_0_1.charName, "Block: " .. iter_0_1.charName, false)
end

var_0_16.SpellsMenu:header("hello", " -- Enemy Skillshots -- ")

for iter_0_2, iter_0_3 in pairs(var_0_3) do
	for iter_0_4, iter_0_5 in pairs(var_0_2.GetEnemyHeroes()) do
		if iter_0_3.charName == iter_0_5.charName then
			if iter_0_3.displayname == "" then
				iter_0_3.displayname = iter_0_2
			end

			if iter_0_3.danger == 0 then
				iter_0_3.danger = 1
			end

			if var_0_16.SpellsMenu[iter_0_3.charName] == nil then
				var_0_16.SpellsMenu:menu(iter_0_3.charName, iter_0_3.charName)
			end

			var_0_16.SpellsMenu[iter_0_3.charName]:menu(iter_0_3.slot, "" .. iter_0_3.charName .. " | " .. (var_0_15[iter_0_3.slot] or "?"))
			var_0_16.SpellsMenu[iter_0_3.charName][iter_0_3.slot]:boolean("Dodge", "Enable Block", true)
			var_0_16.SpellsMenu[iter_0_3.charName][iter_0_3.slot]:slider("hp", "HP to Dodge", 80, 1, 100, 5)
		end
	end
end

var_0_16.SpellsMenu:header("hello", " -- Misc. -- ")
var_0_16.SpellsMenu:boolean("targeteteteteteed", "Shield on Targeted Spells", true)
var_0_16.SpellsMenu:slider("health", " ^- If Health Percent lower than X", 25, 0, 100, 5)
var_0_16:menu("harass", "Harass")
var_0_16.harass:slider("mana", "Mana Manager", 20, 0, 100, 5)
var_0_16.harass:header("~~", " ~~~~ ")
var_0_16.harass:boolean("qcombo", "Use Q in Harass", true)
var_0_16.harass:boolean("wcombo", "Use W in Harass", true)
var_0_16.harass:boolean("wbuffw", " ^- Only if player has Blue Buff", false)
var_0_16.harass:boolean("ecombo", "Use E in Harass", true)
var_0_16.harass:header("--", " -- Auto Harass --")
var_0_16.harass:keybind("autoharas", "Auto Harass", nil, "G")
var_0_16.harass:boolean("qauto", "Use Q for Auto Harass", true)
var_0_16.harass:boolean("wauto", "Use W for Auto Harass", true)
var_0_16.harass:boolean("wbuff", " ^- Only if player has Blue Buff", false)
var_0_16:menu("farming", "Farming")
var_0_16.farming:boolean("mousetoggle", "Toggle Farm on Middle Mouse Press", true)
var_0_16.farming.mousetoggle:set("callback", function(arg_1_0)
	var_0_16.farming.toggle:set("visible", arg_1_0)
end)
var_0_16.farming:keybind("toggle", "Farm Toggle", "Z", nil)

if var_0_16.farming.mousetoggle:get() then
	var_0_16.farming.toggle:set("visible", false)
end

var_0_16.farming:menu("laneclear", "Lane Clear")
var_0_16.farming.laneclear:boolean("farmq", "Use Q to Farm", true)
var_0_16.farming.laneclear:slider("hitq", " ^- if Hits X Minions", 2, 1, 6, 1)
var_0_16.farming.laneclear:boolean("farmw", "Use W to Farm", true)
var_0_16.farming.laneclear:slider("hitw", " ^- if Hits X Minions", 3, 1, 6, 1)
var_0_16.farming.laneclear:boolean("farme", "Use E to Farm", true)
var_0_16.farming:menu("jungleclear", "Jungle Clear")
var_0_16.farming.jungleclear:boolean("useq", "Use Q in Jungle Clear", true)
var_0_16.farming.jungleclear:boolean("usew", "Use W in Jungle Clear", true)
var_0_16:menu("killsteal", "Killsteal")
var_0_16.killsteal:boolean("ksq", "Killsteal with Q", true)
var_0_16.killsteal:boolean("ksw", "Killsteal with W", true)
var_0_16.killsteal:boolean("kse", "Killsteal with E", true)
var_0_16.killsteal:boolean("ksr", "Killsteal with R", false)
var_0_16.killsteal:boolean("usee", "Use E on Ally for W / R Killsteal", false)
var_0_16:menu("misc", "Misc.")
var_0_16.misc:keybind("fleeKey", "Flee Key", "T", nil)
var_0_16.misc:dropdown("fleeMode", "Flee Mode: ", 1, {
	"Logic",
	"Simple E->W"
})
var_0_16.misc:header("aaa", "~~~~")
var_0_16.misc:menu("interrupt", "Interrupt Settings")
var_0_16.misc.interrupt:boolean("intr", "Use R to Interrupt", true)
var_0_16.misc.interrupt:menu("interruptmenuq", "Interruptable Spells")

for iter_0_6 = 1, #var_0_2.GetEnemyHeroes() do
	local var_0_18 = var_0_2.GetEnemyHeroes()[iter_0_6]
	local var_0_19 = string.lower(var_0_18.charName)

	if var_0_18 and var_0_14[var_0_19] then
		for iter_0_7 = 1, #var_0_14[var_0_19] do
			local var_0_20 = var_0_14[var_0_19][iter_0_7]

			var_0_16.misc.interrupt.interruptmenuq:boolean(string.format(tostring(var_0_18.charName) .. tostring(var_0_20.menuslot)), "Interrupt " .. tostring(var_0_18.charName) .. " " .. tostring(var_0_20.menuslot), true)
		end
	end
end

var_0_16:menu("draws", "Draw Settings")
var_0_16.draws:header("balldraw", " -- Spell Drawings --")
var_0_16.draws:boolean("drawq", "Draw Q Range", true)
var_0_16.draws:boolean("draww", "Draw W Range", false)
var_0_16.draws:boolean("drawe", "Draw E Range", false)
var_0_16.draws:boolean("drawr", "Draw R Range", true)
var_0_16.draws:header("balldraw", " -- Misc. Drawings --")
var_0_16.draws:boolean("drawdamage", "Draw Damage", true)
var_0_16.draws:slider("transparency", "Damage Drawing Transparency", 155, 0, 255, 1)
var_0_16.draws:header("balldraw", " -- Ball Drawings --")
var_0_16.draws:dropdown("drawmode", "Draw Mode", 2, {
	"First",
	"Second",
	"Simple",
	"None"
})
var_0_16.draws.drawmode:set("tooltip", "Use Simple if you drop FPS")
var_0_16.draws:dropdown("drawcolors", "Colors: ", 2, {
	"Random",
	"Default"
})
var_0_16:header("hewoo", " You can change Ball Animations ")
var_0_16:header("hewoo2", " in Drawings Menu. :3 ")
var_0_6.load_to_menu(var_0_16)

local function var_0_21(arg_2_0, arg_2_1, arg_2_2)
	if arg_2_2 <= var_0_8.range then
		arg_2_0.obj = arg_2_1

		return true
	end
end

local function var_0_22(arg_3_0, arg_3_1, arg_3_2)
	if arg_3_2 <= 52000 then
		arg_3_0.obj = arg_3_1

		return true
	end
end

local function var_0_23(arg_4_0, arg_4_1, arg_4_2)
	if arg_4_2 <= var_0_8.range then
		arg_4_0.obj = arg_4_1

		return true
	end
end

local var_0_24 = {
	60,
	90,
	120,
	150,
	180
}

function QDamage(arg_5_0)
	local var_5_0 = 0

	if player:spellSlot(0).level > 0 then
		var_5_0 = var_0_2.CalculateMagicDamage(arg_5_0, var_0_24[player:spellSlot(0).level] + var_0_2.GetTotalAP() * 0.5, player)
	end

	return var_5_0
end

local var_0_25 = {
	60,
	90,
	120,
	150,
	180
}

function EDamage(arg_6_0)
	local var_6_0 = 0

	if player:spellSlot(2).level > 0 then
		var_6_0 = var_0_2.CalculateMagicDamage(arg_6_0, var_0_25[player:spellSlot(2).level] + var_0_2.GetTotalAP() * 0.3, player)
	end

	return var_6_0
end

local function var_0_26(arg_7_0, arg_7_1, arg_7_2)
	local var_7_0 = 0

	for iter_7_0 = 0, objManager.enemies_n - 1 do
		local var_7_1 = objManager.enemies[iter_7_0]

		if var_7_1 and not var_7_1.isDead and var_7_1.isVisible and var_7_1.isTargetable and player.pos:dist(var_7_1) < 2000 then
			local var_7_2 = var_0_2.GetPredictedPos(var_7_1, arg_7_2)

			if var_7_2 and var_7_2:distSqr(arg_7_0) <= arg_7_1 * arg_7_1 then
				var_7_0 = var_7_0 + 1
			end
		end
	end

	return var_7_0
end

local var_0_27 = false
local var_0_28 = vec3(0, 0, 0)
local var_0_29 = 0

local function var_0_30(arg_8_0, arg_8_1)
	local var_8_0 = {}

	for iter_8_0 = 0, objManager.allies_n - 1 do
		local var_8_1 = objManager.allies[iter_8_0]

		if arg_8_1 > arg_8_0:dist(var_8_1.pos) and var_0_2.IsValidTarget(var_8_1) then
			var_8_0[#var_8_0 + 1] = var_8_1
		end
	end

	return var_8_0
end

local function var_0_31(arg_9_0, arg_9_1)
	local var_9_0 = {}

	for iter_9_0 = 0, objManager.allies_n - 1 do
		local var_9_1 = objManager.allies[iter_9_0]

		if arg_9_1 > arg_9_0:dist(var_9_1.pos) and var_0_2.IsValidTarget(var_9_1) then
			var_9_0[#var_9_0 + 1] = var_9_1
		end
	end

	return var_9_0
end

local var_0_32 = {
	60,
	105,
	150,
	195,
	240
}

function WDamage(arg_10_0)
	local var_10_0 = 0

	if player:spellSlot(1).level > 0 then
		var_10_0 = var_0_2.CalculateMagicDamage(arg_10_0, var_0_32[player:spellSlot(1).level] + var_0_2.GetTotalAP() * 0.7, player)
	end

	return var_10_0
end

local var_0_33 = {
	200,
	275,
	350
}

function RDamage(arg_11_0)
	local var_11_0 = 0

	if player:spellSlot(3).level > 0 then
		if player:spellSlot(1).state == 0 then
			var_11_0 = var_0_2.CalculateMagicDamage(arg_11_0, var_0_33[player:spellSlot(3).level] + var_0_2.GetTotalAP() * 0.8, player)
		else
			var_11_0 = 0
		end
	end

	return var_11_0
end

local var_0_34
local var_0_35
local var_0_36
local var_0_37
local var_0_38
local var_0_39 = 0

local function var_0_40()
	return var_0_6.get_result(var_0_21).obj
end

local function var_0_41()
	return var_0_6.get_result(var_0_23).obj
end

local function var_0_42()
	return var_0_6.get_result(var_0_22).obj
end

local var_0_43 = false
local var_0_44 = 0
local var_0_45 = true
local var_0_46 = 0

local function var_0_47()
	if var_0_16.farming.mousetoggle:get() and keyboard.isKeyDown(4) then
		if var_0_43 == false and os.clock() > var_0_44 then
			var_0_43 = true
			var_0_44 = os.clock() + 0.3
		end

		if var_0_43 == true and os.clock() > var_0_44 then
			var_0_43 = false
			var_0_44 = os.clock() + 0.3
		end
	end

	if not var_0_16.farming.mousetoggle:get() and var_0_16.farming.toggle and var_0_16.farming.toggle:get() then
		if var_0_43 == false and os.clock() > var_0_44 then
			var_0_43 = true
			var_0_44 = os.clock() + 0.3
		end

		if var_0_43 == true and os.clock() > var_0_44 then
			var_0_43 = false
			var_0_44 = os.clock() + 0.3
		end
	end
end

local function var_0_48(arg_16_0, arg_16_1)
	local var_16_0 = {}

	for iter_16_0 = 0, objManager.enemies_n - 1 do
		local var_16_1 = objManager.enemies[iter_16_0]

		if arg_16_1 > arg_16_0:dist(var_16_1.pos) and var_0_2.IsValidTarget(var_16_1) then
			var_16_0[#var_16_0 + 1] = var_16_1
		end
	end

	return var_16_0
end

local function var_0_49(arg_17_0, arg_17_1, arg_17_2)
	local var_17_0 = arg_17_2.moveSpeed
	local var_17_1 = arg_17_2.boundingRadius
	local var_17_2 = (arg_17_2.path.point[1] - arg_17_2.path.point[0]):norm()
	local var_17_3 = network.latency / 1000 + arg_17_0.delay
	local var_17_4 = arg_17_2.pos
	local var_17_5 = arg_17_1
	local var_17_6 = var_17_4 + var_17_2 * (var_17_0 * var_17_3) - var_17_2 * var_17_1
	local var_17_7 = (var_17_6 - var_17_5):norm()
	local var_17_8 = var_17_2 * var_17_7
	local var_17_9 = var_17_6 + var_17_2 * var_17_7 * (var_17_8 * arg_17_0.width)
	local var_17_10 = var_17_5:dist(var_17_9)
	local var_17_11 = var_17_0 * var_17_0 - arg_17_0.speed * arg_17_0.speed
	local var_17_12 = 2 * var_17_0 * var_17_10
	local var_17_13 = var_17_10 * var_17_10
	local var_17_14 = var_17_12 * var_17_12 - 4 * var_17_11 * var_17_13

	if var_17_14 < 0 then
		return -1
	end

	local var_17_15 = 2 * var_17_13 / (math.sqrt(var_17_14) - var_17_12)

	if var_17_15 < 0 then
		return -1
	end

	return var_17_15
end

local function var_0_50(arg_18_0, arg_18_1, arg_18_2)
	local var_18_0 = vec3(0, 0, 0)
	local var_18_1 = arg_18_2.direction
	local var_18_2 = arg_18_2.pos
	local var_18_3

	if var_0_28 then
		var_18_3 = var_0_28
	end

	if var_0_37 then
		var_18_3 = vec3(var_0_37.x, var_0_37.y, var_0_37.z)
	end

	if var_18_3 then
		local var_18_4 = var_0_49(var_0_8, var_18_3, arg_18_2)

		if not var_18_4 then
			return (vec3(0, 0, 0))
		end

		var_18_0 = var_18_2 + var_18_1 * (arg_18_2.moveSpeed * var_18_4)

		if arg_18_1.pos:dist(var_18_0) > var_0_12.range then
			return vec3(0, 0, 0)
		end

		return var_18_0
	end

	return var_18_0
end

local function var_0_51(arg_19_0)
	local var_19_0 = {}
	local var_19_1
	local var_19_2 = 0

	if var_0_37 then
		local var_19_3 = var_0_5.linear.get_prediction(var_0_8, arg_19_0, vec2(var_0_37.x, var_0_37.z))

		if var_19_3 and player.pos:dist(vec3(var_19_3.endPos.x, arg_19_0.y, var_19_3.endPos.y)) <= var_0_8.range then
			var_19_0[var_19_2] = var_19_3.endPos
			var_19_2 = var_19_2 + 1
		end
	end

	if not var_0_37 then
		local var_19_4 = var_0_5.linear.get_prediction(var_0_8, arg_19_0)

		if var_19_4 and player.pos:dist(vec3(var_19_4.endPos.x, arg_19_0.y, var_19_4.endPos.y)) <= var_0_8.range then
			var_19_0[var_19_2] = var_19_4.endPos
			var_19_2 = var_19_2 + 1
		end
	end

	for iter_19_0 = 0, objManager.enemies_n - 1 do
		local var_19_5 = objManager.enemies[iter_19_0]

		if var_19_5 and not var_19_5.isDead and var_19_5.isVisible and var_19_5.isTargetable and player.pos:dist(var_19_5) < 2000 then
			local var_19_6 = var_0_5.linear.get_prediction(var_0_8, var_19_5)
			local var_19_7 = var_0_50(var_0_8, player, arg_19_0)

			if var_19_6 and player.pos:dist(vec3(var_19_6.endPos.x, arg_19_0.y, var_19_6.endPos.y)) <= var_0_8.range then
				var_19_0[var_19_2] = var_19_6.endPos
				var_19_2 = var_19_2 + 1
			end
		end
	end

	if table.getn(var_19_0) > 0 then
		for iter_19_1 = 0, 5 do
			local var_19_8, var_19_9 = mathf.mec(var_19_0, table.getn(var_19_0))

			if var_19_9 < var_0_9.range and table.getn(var_19_0) >= 2 and player:spellSlot(1).state == 0 and player:spellSlot(2).state == 0 then
				return var_19_8, 2
			end

			if table.getn(var_19_0) == 1 then
				return var_19_8, 1
			end

			if var_19_9 < var_0_8.width + 50 and table.getn(var_19_0) >= 1 then
				return var_19_8, 2
			end

			if var_19_9 < var_0_12.range and table.getn(var_19_0) >= 3 and player:spellSlot(3).state == 0 then
				return var_19_8, 3
			end

			local var_19_10 = -1
			local var_19_11 = 1

			for iter_19_2 = 0, table.getn(var_19_0) do
				local var_19_12 = var_19_0[iter_19_2]:dist(var_19_0[1])

				if var_19_10 < var_19_12 or var_19_10 == -1 then
					var_19_11 = iter_19_1
					var_19_10 = var_19_12
				end
			end

			table.remove(var_19_0, var_19_11)
		end
	end

	return var_19_0[0], 0
end

local var_0_52 = 0
local var_0_53 = 0

local function var_0_54(arg_20_0, arg_20_1, arg_20_2, arg_20_3, arg_20_4)
	if arg_20_0 == 3 and player:spellSlot(3).state == 0 and game.time > var_0_53 then
		var_0_52 = game.time + 0.1
		var_0_53 = game.time + 1
	end

	if arg_20_0 == 3 then
		local var_20_0, var_20_1 = CheckEnemiesHitByR()

		if var_20_0 == 0 then
			core.block_input()
		end
	end
end

function GetDistanceToClosestAlly(arg_21_0)
	local var_21_0 = player.pos:dist(arg_21_0)
	local var_21_1 = var_0_2.GetAllyHeroes()

	for iter_21_0, iter_21_1 in ipairs(var_21_1) do
		if iter_21_1 ~= player then
			local var_21_2 = arg_21_0:dist(iter_21_1.pos)

			if var_21_2 < var_21_0 then
				var_21_0 = var_21_2
			end
		end
	end

	return var_21_0
end

local function var_0_55(arg_22_0, arg_22_1)
	local var_22_0 = {}

	for iter_22_0 = 0, objManager.minions.size[TEAM_ENEMY] - 1 do
		local var_22_1 = objManager.minions[TEAM_ENEMY][iter_22_0]

		if arg_22_1 > arg_22_0:dist(var_22_1.pos) and var_0_2.IsValidTarget(var_22_1) then
			var_22_0[#var_22_0 + 1] = var_22_1
		end
	end

	return var_22_0
end

local var_0_56
local var_0_57
local var_0_58 = 0
local var_0_59 = 0
local var_0_60 = 0
local var_0_61 = 0
local var_0_62
local var_0_63
local var_0_64

local function var_0_65(arg_23_0)
	if arg_23_0 then
		if arg_23_0.name == "OrianaRedact" then
			var_0_34 = arg_23_0
			var_0_60 = os.clock()
			var_0_62 = arg_23_0
			var_0_57 = arg_23_0
			var_0_27 = false
			var_0_28 = vec3(0, 0, 0)
			var_0_61 = game.time + 0.2
			var_0_63 = arg_23_0
		end

		if arg_23_0.name == "OrianaIzuna" then
			var_0_34 = arg_23_0
			var_0_35 = nil
			var_0_36 = nil
			var_0_56 = arg_23_0.endPos
			var_0_27 = false
			var_0_37 = nil
			var_0_64 = arg_23_0
		end
	end
end

cb.add(cb.create_missile, var_0_65)

local function var_0_66(arg_24_0)
	if arg_24_0 then
		if arg_24_0 and var_0_63 and var_0_63.ptr == arg_24_0.ptr then
			var_0_34 = nil
			var_0_57 = nil
			var_0_62 = nil
			var_0_63 = nil
		end

		if var_0_64 and arg_24_0 and var_0_64.ptr == arg_24_0.ptr then
			var_0_34 = nil
			var_0_56 = nil
			var_0_64 = nil
		end
	end
end

cb.add(cb.delete_missile, var_0_66)
cb.add(cb.delete_particle, function(arg_25_0)
	if arg_25_0 then
		if var_0_35 and arg_25_0.ptr == var_0_35.ptr then
			var_0_35 = nil
		end

		if var_0_36 and arg_25_0.ptr == var_0_36.ptr then
			var_0_36 = nil
		end

		if var_0_37 and arg_25_0.ptr == var_0_37.ptr then
			var_0_37 = nil
			var_0_36 = nil
			var_0_35 = nil
			timerdraw = os.clock() + 0.1
		end
	end
end)
cb.add(cb.create_particle, function(arg_26_0)
	if arg_26_0 and arg_26_0.name:find("yomu_ring") then
		var_0_37 = arg_26_0
		var_0_27 = false
	end

	if arg_26_0 and (var_0_37 or var_0_35) and arg_26_0.name:find("low_green") and var_0_37.pos:dist(arg_26_0.pos) < 200 then
		var_0_36 = arg_26_0
		var_0_27 = false
	end

	if arg_26_0 and (var_0_37 or var_0_36) and arg_26_0.name:find("glow_center") then
		var_0_35 = arg_26_0
		var_0_27 = false
	end
end)

local var_0_67 = 0
local var_0_68 = {}

local function var_0_69(arg_27_0)
	if arg_27_0 and arg_27_0.owner.team ~= TEAM_ALLY and arg_27_0.name:find("BasicAttack") and player.mana / player.maxMana * 100 >= var_0_16.SpellsMenu.mana:get() and var_0_16.SpellsMenu.enable:get() then
		local var_27_0 = var_0_2.GetAllyHeroes()

		for iter_27_0, iter_27_1 in ipairs(var_27_0) do
			if iter_27_1 and iter_27_1.health <= var_0_2.CalculateAADamage(iter_27_1, arg_27_0.owner) + var_0_2.CalculateAADamage(iter_27_1, arg_27_0.owner) * 0.2 and arg_27_0.target == aly then
				player:castSpell("obj", 2, iter_27_1)
			end
		end
	end

	if player.mana / player.maxMana * 100 >= var_0_16.SpellsMenu.mana:get() and var_0_16.SpellsMenu.targeteteteteteed:get() then
		local var_27_1 = var_0_2.GetAllyHeroes()

		for iter_27_2, iter_27_3 in ipairs(var_27_1) do
			if iter_27_3 and not var_0_16.SpellsMenu.blacklist[iter_27_3.charName]:get() and arg_27_0 and arg_27_0.owner.type == TYPE_HERO and arg_27_0.owner.team == TEAM_ENEMY and arg_27_0.target == iter_27_3 and not arg_27_0.name:find("crit") and not arg_27_0.name:find("BasicAttack") and var_0_16.SpellsMenu.targeteteteteteed:get() and iter_27_3.pos:dist(player.pos) <= var_0_10.range and var_0_16.SpellsMenu.health:get() >= iter_27_3.health / iter_27_3.maxHealth * 100 then
				player:castSpell("obj", 2, iter_27_3)
			end
		end
	end

	if arg_27_0 and arg_27_0.owner.team == TEAM_ALLY and var_0_16.combo.eint:get() and arg_27_0.owner.pos:dist(player.pos) <= var_0_10.range then
		for iter_27_4, iter_27_5 in pairs(var_0_13) do
			if arg_27_0.name:lower() == iter_27_5:lower() then
				player:castSpell("obj", 2, arg_27_0.owner)
			end
		end
	end

	if var_0_16.misc.interrupt.intr:get() and arg_27_0 and arg_27_0.owner and arg_27_0.owner.team == TEAM_ENEMY and player.pos:dist(arg_27_0.owner.pos) < var_0_8.range then
		local var_27_2 = string.lower(arg_27_0.owner.charName)

		if var_0_17 and var_0_14[var_27_2] then
			for iter_27_6 = 1, #var_0_14[var_27_2] do
				local var_27_3 = var_0_14[var_27_2][iter_27_6]

				if var_0_16.misc.interrupt.interruptmenuq[arg_27_0.owner.charName .. var_27_3.menuslot]:get() and var_27_3.spellname == arg_27_0.name:lower() then
					var_0_68.start = os.clock()
					var_0_68.channel = var_27_3.channelduration
					var_0_68.owner = arg_27_0.owner
				end
			end
		end
	end
end

local function var_0_70()
	if var_0_68.owner then
		if os.clock() - var_0_68.channel >= var_0_68.start then
			var_0_68.owner = false

			return
		end

		if player:spellSlot(3).state == 0 then
			if player.pos:dist(var_0_68.owner.pos) < var_0_8.range then
				player:castSpell("pos", 0, var_0_68.owner.pos)
			end

			if var_0_37 and var_0_37.pos:dist(var_0_68.owner.pos) < var_0_12.range then
				player:castSpell("self", 3)

				var_0_68.owner = false
			end

			if not var_0_37 and player.pos:dist(var_0_68.owner.pos) < var_0_12.range then
				player:castSpell("self", 3)

				var_0_68.owner = false
			end
		end
	end
end

local function var_0_71()
	return
end

local function var_0_72()
	local var_30_0 = 0

	if var_0_37 then
		local var_30_1 = var_0_2.GetEnemyHeroes()

		for iter_30_0, iter_30_1 in ipairs(var_30_1) do
			if iter_30_1 then
				local var_30_2 = var_0_5.linear.get_prediction(var_0_10, iter_30_1)

				if var_30_2 then
					local var_30_3 = (player.pos - var_0_37.pos):norm()
					local var_30_4 = var_0_37.pos - var_30_3
					local var_30_5, var_30_6, var_30_7 = VectorPointProjectionOnLineSegment(vec3(var_30_4.x, 0, var_30_4.z), vec3(player.pos.x, 0, player.pos.z), vec3(var_30_2.endPos.x, iter_30_1.pos.y, var_30_2.endPos.y))

					if var_30_7 and vec2(var_30_6.x, var_30_6.y):dist(var_30_2.endPos) <= 60 + iter_30_1.boundingRadius then
						var_30_0 = var_30_0 + 1
					end
				end
			end
		end
	end

	return var_30_0
end

function CheckEnemiesHitByR()
	local var_31_0 = {}
	local var_31_1 = var_0_2.GetEnemyHeroes()

	if var_0_37 then
		for iter_31_0, iter_31_1 in ipairs(var_31_1) do
			if iter_31_1 then
				local var_31_2 = var_0_5.core.get_pos_after_time(iter_31_1, 0.5)

				if vec3(var_31_2.x, iter_31_1.y, var_31_2.y):dist(var_0_37.pos) <= 380 and iter_31_1.pos:dist(var_0_37.pos) <= 475 then
					table.insert(var_31_0, iter_31_1)
				end
			end
		end
	end

	if var_0_28 then
		for iter_31_2, iter_31_3 in ipairs(var_31_1) do
			if iter_31_3 then
				local var_31_3 = var_0_5.core.get_pos_after_time(iter_31_3, 0.5)

				if vec3(var_31_3.x, iter_31_3.y, var_31_3.y):dist(var_0_28) <= 380 and iter_31_3.pos:dist(var_0_28) <= 475 then
					table.insert(var_31_0, iter_31_3)
				end
			end
		end
	end

	return #var_31_0, var_31_0
end

local function var_0_73(arg_32_0, arg_32_1, arg_32_2)
	if var_0_5.trace.linear.hardlock(arg_32_0, arg_32_1, arg_32_2) then
		return true
	end

	if var_0_5.trace.linear.hardlockmove(arg_32_0, arg_32_1, arg_32_2) then
		return true
	end

	if arg_32_2 and var_0_2.IsValidTarget(arg_32_2) and arg_32_1.startPos:dist(arg_32_1.endPos) <= 500 then
		return true
	end

	if var_0_5.trace.newpath(arg_32_2, 0.033, 0.2) then
		return true
	end
end

local function var_0_74()
	if var_0_16.combo.hitr:get() == 1 then
		local var_33_0 = var_0_40()

		if var_0_2.IsValidTarget(var_33_0) and var_33_0 and player:spellSlot(3).state == 0 and var_0_37 then
			local var_33_1 = var_0_5.core.get_pos_after_time(var_33_0, 0.5)

			if vec3(var_33_1.x, var_33_0.y, var_33_1.y):dist(var_0_37.pos) <= var_0_12.range and var_33_0.pos:dist(var_0_37.pos) <= var_0_12.range then
				player:castSpell("self", 3)
			end
		end
	end

	if #var_0_48(player.pos, var_0_8.range + var_0_12.range) == 1 then
		if not var_0_16.combo.forcer:get() and var_0_16.combo.rcombo:get() then
			local var_33_2 = var_0_40()

			if var_0_2.IsValidTarget(var_33_2) and var_33_2 and player:spellSlot(3).state == 0 and #var_0_48(var_33_2.pos, 1000) >= #var_0_31(var_33_2.pos, 1000) and var_0_37 and QDamage(var_33_2) + WDamage(var_33_2) + EDamage(var_33_2) + RDamage(var_33_2) > var_33_2.health then
				local var_33_3, var_33_4 = CheckEnemiesHitByR()

				if var_33_3 >= var_0_16.combo.hitr:get() then
					local var_33_5 = var_0_5.core.get_pos_after_time(var_33_2, 0.5)

					if vec3(var_33_5.x, var_33_2.y, var_33_5.y):dist(var_0_37.pos) <= var_0_12.range and var_33_2.pos:dist(var_0_37.pos) <= var_0_12.range then
						player:castSpell("self", 3)
					end
				end
			end
		end

		if var_0_16.combo.forcer:get() and var_0_16.combo.rcombo:get() then
			local var_33_6 = var_0_40()

			if var_0_2.IsValidTarget(var_33_6) and var_33_6 and player:spellSlot(3).state == 0 and #var_0_48(var_33_6.pos, 1000) >= #var_0_31(var_33_6.pos, 1000) and var_0_37 and QDamage(var_33_6) + WDamage(var_33_6) + EDamage(var_33_6) + RDamage(var_33_6) > var_33_6.health then
				local var_33_7, var_33_8 = CheckEnemiesHitByR()

				if var_33_7 >= 1 then
					local var_33_9 = var_0_5.core.get_pos_after_time(var_33_6, 0.5)

					if vec3(var_33_9.x, var_33_6.y, var_33_9.y):dist(var_0_37.pos) <= var_0_12.range and var_33_6.pos:dist(var_0_37.pos) <= var_0_12.range then
						player:castSpell("self", 3)
					end
				end
			end
		end

		if var_0_16.combo.wcombo:get() and player:spellSlot(1).state == 0 then
			if var_0_28 and #var_0_48(var_0_28, var_0_9.range) > 0 then
				player:castSpell("self", 1)
			end

			if var_0_37 and #var_0_48(var_0_37.pos, var_0_9.range) > 0 then
				player:castSpell("self", 1)
			end
		end

		if var_0_16.combo.ecombo:get() and game.time >= var_0_52 and player:spellSlot(0).state == 0 then
			local var_33_10 = var_0_40()

			if var_0_2.IsValidTarget(var_33_10) and var_33_10 and player:spellSlot(2).state == 0 then
				local var_33_11 = var_0_5.linear.get_prediction(var_0_8, var_33_10)

				if var_33_11 and player.pos:dist(vec3(var_33_11.endPos.x, var_33_10.y, var_33_11.endPos.y)) <= var_0_8.range and var_0_37 then
					local var_33_12 = var_0_37.pos:dist(vec3(var_33_11.endPos.x, var_33_10.y, var_33_11.endPos.y)) / var_0_8.speed
					local var_33_13 = player.pos:dist(vec3(var_33_11.endPos.x, var_33_10.y, var_33_11.endPos.y)) / var_0_8.speed + player.pos:dist(var_0_37.pos) / 1850
					local var_33_14 = player
					local var_33_15 = var_0_2.GetAllyHeroes()

					for iter_33_0, iter_33_1 in ipairs(var_33_15) do
						if iter_33_1 and iter_33_1 ~= player and iter_33_1.pos:dist(player.pos) <= var_0_10.range then
							local var_33_16 = iter_33_1.pos:dist(vec3(var_33_11.endPos.x, var_33_10.y, var_33_11.endPos.y)) / var_0_8.speed + iter_33_1.pos:dist(var_0_37.pos) / 1850

							if var_33_16 < var_33_13 then
								var_33_13 = var_33_16
								var_33_14 = iter_33_1
							end
						end
					end

					if var_33_13 < var_33_12 and (not var_33_14 == player or player.pos:dist(var_0_37.pos) > 100) and var_33_14.pos:dist(player.pos) < player.pos:dist(vec3(var_33_11.endPos.x, var_33_10.y, var_33_11.endPos.y)) then
						player:castSpell("obj", 2, var_33_14)

						return
					end
				end
			end
		end

		local var_33_17 = var_0_40()

		if var_0_16.combo.qcombo:get() and player:spellSlot(0).state == 0 and var_0_2.IsValidTarget(var_33_17) and var_33_17 then
			if var_0_37 then
				local var_33_18 = var_0_5.linear.get_prediction(var_0_8, var_33_17, vec2(var_0_37.x, var_0_37.z))

				if var_33_18 and player.pos:dist(vec3(var_33_18.endPos.x, var_33_17.y, var_33_18.endPos.y)) <= var_0_8.range then
					if not var_0_16.combo.slowq:get() then
						player:castSpell("pos", 0, vec3(var_33_18.endPos.x, var_33_17.y, var_33_18.endPos.y))
					end

					if var_0_16.combo.slowq:get() and var_0_73(var_0_8, var_33_18, var_33_17) then
						player:castSpell("pos", 0, vec3(var_33_18.endPos.x, var_33_17.y, var_33_18.endPos.y))
					end
				end
			end

			if var_0_28 then
				local var_33_19 = var_0_5.linear.get_prediction(var_0_8, var_33_17, vec2(var_0_28.x, var_0_28.z))

				if var_33_19 and player.pos:dist(vec3(var_33_19.endPos.x, var_33_17.y, var_33_19.endPos.y)) <= var_0_8.range then
					if not var_0_16.combo.slowq:get() then
						player:castSpell("pos", 0, vec3(var_33_19.endPos.x, var_33_17.y, var_33_19.endPos.y))
					end

					if var_0_16.combo.slowq:get() and var_0_73(var_0_8, var_33_19, var_33_17) then
						player:castSpell("pos", 0, vec3(var_33_19.endPos.x, var_33_17.y, var_33_19.endPos.y))
					end
				end
			end
		end

		if var_0_16.combo.ecombo:get() and player:spellSlot(2).state == 0 and var_0_16.combo.ecombo2:get() and game.time >= var_0_52 then
			local var_33_20 = var_0_40()

			if var_0_2.IsValidTarget(var_33_20) and var_33_20 and var_0_37 then
				local var_33_21 = var_0_5.linear.get_prediction(var_0_10, var_33_20)

				if var_33_21 then
					local var_33_22 = var_0_5.collision.get_prediction(var_0_10, var_33_21, var_33_20)
					local var_33_23 = var_0_5.collision.get_prediction(var_0_11, var_33_21, var_33_20)

					if var_0_72() >= 1 then
						player:castSpell("self", 2)
					end
				end
			end
		end
	end

	if #var_0_48(player.pos, var_0_8.range + var_0_12.range) > 1 then
		local var_33_24 = var_0_40()

		if var_0_2.IsValidTarget(var_33_24) and var_33_24 then
			if var_0_16.combo.rcombo:get() and player:spellSlot(3).state == 0 and var_0_37 then
				if #var_0_48(var_0_37.pos, 800) > 1 then
					local var_33_25, var_33_26 = CheckEnemiesHitByR()
					local var_33_27 = 0
					local var_33_28 = 0

					if var_33_25 >= 2 then
						for iter_33_2, iter_33_3 in ipairs(var_33_26) do
							if iter_33_3.health - QDamage(iter_33_3) + RDamage(iter_33_3) + EDamage(iter_33_3) + WDamage(iter_33_3) < 0.4 * iter_33_3.maxHealth or QDamage(iter_33_3) + RDamage(iter_33_3) + EDamage(iter_33_3) + WDamage(iter_33_3) >= 0.4 * iter_33_3.maxHealth then
								var_33_27 = var_33_27 + 1
							end

							if iter_33_3.health - QDamage(iter_33_3) + RDamage(iter_33_3) + EDamage(iter_33_3) + WDamage(iter_33_3) < 0 then
								var_33_28 = var_33_28 + 1
							end
						end
					end

					if (GetDistanceToClosestAlly(var_0_37.pos) < var_0_8.range * 1.3 and var_33_25 >= #var_0_48(var_0_37.pos, 800) or var_33_27 >= 2 or var_33_28 >= 1) and var_33_25 >= var_0_16.combo.hitr:get() then
						player:castSpell("self", 3)
					end
				end

				if var_0_16.combo.hitr:get() == 1 and QDamage(var_33_24) + WDamage(var_33_24) + RDamage(var_33_24) >= var_33_24.health and GetDistanceToClosestAlly(var_0_37.pos) < var_0_8.range * 1.3 then
					local var_33_29 = var_0_5.core.get_pos_after_time(var_33_24, 0.5)

					if vec3(var_33_29.x, var_33_24.y, var_33_29.y):dist(var_0_37.pos) <= var_0_12.range and var_33_24.pos:dist(var_0_37.pos) <= var_0_12.range then
						player:castSpell("self", 3)
					end
				end
			end

			if var_0_16.combo.wcombo:get() then
				if var_0_28 and #var_0_48(var_0_28, var_0_9.range) > 0 then
					player:castSpell("self", 1)
				end

				if var_0_37 and #var_0_48(var_0_37.pos, var_0_9.range) > 0 then
					player:castSpell("self", 1)
				end
			end

			if var_0_16.combo.qcombo:get() then
				local var_33_30, var_33_31 = var_0_51(var_33_24)

				if var_33_30 and var_33_31 > 1 then
					player:castSpell("pos", 0, vec3(var_33_30.x, var_33_24.pos.y, var_33_30.y))
				else
					if player:spellSlot(0).state == 0 then
						local var_33_32 = var_0_40()

						if var_0_2.IsValidTarget(var_33_32) and var_33_32 and player:spellSlot(2).state == 0 then
							local var_33_33 = var_0_5.linear.get_prediction(var_0_8, var_33_32)

							if var_33_33 and player.pos:dist(vec3(var_33_33.endPos.x, var_33_32.y, var_33_33.endPos.y)) <= var_0_8.range and var_0_37 then
								local var_33_34 = var_0_37.pos:dist(vec3(var_33_33.endPos.x, var_33_32.y, var_33_33.endPos.y)) / var_0_8.speed
								local var_33_35 = player.pos:dist(vec3(var_33_33.endPos.x, var_33_32.y, var_33_33.endPos.y)) / var_0_8.speed + player.pos:dist(var_0_37.pos) / 1850
								local var_33_36 = player
								local var_33_37 = var_0_2.GetAllyHeroes()

								for iter_33_4, iter_33_5 in ipairs(var_33_37) do
									if iter_33_5 and iter_33_5 ~= player and iter_33_5.pos:dist(player.pos) <= var_0_10.range then
										local var_33_38 = iter_33_5.pos:dist(vec3(var_33_33.endPos.x, var_33_32.y, var_33_33.endPos.y)) / var_0_8.speed + iter_33_5.pos:dist(var_0_37.pos) / 1850

										if var_33_38 < var_33_35 then
											var_33_35 = var_33_38
											var_33_36 = iter_33_5
										end
									end
								end

								if var_33_35 < var_33_34 and (not var_33_36 == player or player.pos:dist(var_0_37.pos) > 100) and var_33_36.pos:dist(player.pos) < player.pos:dist(vec3(var_33_33.endPos.x, var_33_32.y, var_33_33.endPos.y)) then
									player:castSpell("obj", 2, var_33_36)

									return
								end
							end
						end
					end

					local var_33_39 = var_0_40()

					if var_0_2.IsValidTarget(var_33_39) and var_33_39 then
						local var_33_40 = var_0_5.linear.get_prediction(var_0_8, var_33_39)

						if var_33_40 and player.pos:dist(vec3(var_33_40.endPos.x, var_33_39.y, var_33_40.endPos.y)) <= var_0_8.range then
							if not var_0_16.combo.slowq:get() then
								player:castSpell("pos", 0, vec3(var_33_40.endPos.x, var_33_39.y, var_33_40.endPos.y))
							end

							if var_0_16.combo.slowq:get() and var_0_73(var_0_8, var_33_40, var_33_39) then
								player:castSpell("pos", 0, vec3(var_33_40.endPos.x, var_33_39.y, var_33_40.endPos.y))
							end
						end
					end
				end
			end

			if var_0_37 and var_0_16.combo.ecombo:get() and player:spellSlot(2).state == 0 and var_0_16.combo.ecombo2:get() and game.time >= var_0_52 and #var_0_48(var_0_37.pos, 800) <= 2 then
				-- block empty
			end

			if var_0_16.combo.ecombo:get() and player:spellSlot(2).state == 0 and game.time >= var_0_52 and var_0_37 then
				if #var_0_48(var_0_37.pos, 800) <= 2 then
					local var_33_41 = var_0_40()

					if var_0_2.IsValidTarget(var_33_41) and var_33_41 and var_0_37 then
						local var_33_42 = var_0_5.linear.get_prediction(var_0_10, var_33_41)

						if var_33_42 then
							local var_33_43 = var_0_5.collision.get_prediction(var_0_10, var_33_42, var_33_41)
							local var_33_44 = var_0_5.collision.get_prediction(var_0_11, var_33_42, var_33_41)

							if var_0_72() >= 1 then
								player:castSpell("self", 2)
							end
						end
					end
				end

				if #var_0_48(var_0_37.pos, 800) > 2 then
					local var_33_45 = var_0_40()

					if var_0_2.IsValidTarget(var_33_45) and var_33_45 and var_0_37 then
						local var_33_46 = var_0_5.linear.get_prediction(var_0_10, var_33_45)

						if var_33_46 then
							local var_33_47 = var_0_5.collision.get_prediction(var_0_10, var_33_46, var_33_45)
							local var_33_48 = var_0_5.collision.get_prediction(var_0_11, var_33_46, var_33_45)

							if var_0_72() >= 2 then
								player:castSpell("self", 2)
							end
						end
					end
				end
			end
		end
	end
end

local function var_0_75()
	if var_0_43 then
		if var_0_16.farming.jungleclear.useq:get() then
			for iter_34_0 = 0, objManager.minions.size[TEAM_NEUTRAL] - 1 do
				local var_34_0 = objManager.minions[TEAM_NEUTRAL][iter_34_0]

				if var_34_0 and not var_34_0.isDead and var_34_0.moveSpeed > 0 and var_34_0.isTargetable and var_34_0.isVisible and var_34_0.type == TYPE_MINION and var_34_0.pos:dist(player.pos) <= var_0_8.range then
					if var_0_37 then
						local var_34_1 = var_0_5.linear.get_prediction(var_0_8, var_34_0, vec2(var_0_37.x, var_0_37.z))

						if var_34_1 and player.pos:dist(vec3(var_34_1.endPos.x, var_34_0.y, var_34_1.endPos.y)) < var_0_8.range then
							player:castSpell("pos", 0, vec3(var_34_1.endPos.x, mousePos.y, var_34_1.endPos.y))
						end
					end

					if var_0_28 then
						local var_34_2 = var_0_5.linear.get_prediction(var_0_8, var_34_0, vec2(var_0_28.x, var_0_28.z))

						if var_34_2 and player.pos:dist(vec3(var_34_2.endPos.x, var_34_0.y, var_34_2.endPos.y)) < var_0_8.range then
							player:castSpell("pos", 0, vec3(var_34_2.endPos.x, mousePos.y, var_34_2.endPos.y))
						end
					end
				end
			end
		end

		if var_0_16.farming.jungleclear.usew:get() then
			for iter_34_1 = 0, objManager.minions.size[TEAM_NEUTRAL] - 1 do
				local var_34_3 = objManager.minions[TEAM_NEUTRAL][iter_34_1]

				if var_34_3 and not var_34_3.isDead and var_34_3.moveSpeed > 0 and var_34_3.isTargetable and var_34_3.isVisible and var_34_3.type == TYPE_MINION and var_0_37 and var_34_3.pos:dist(var_0_37.pos) <= var_0_9.range and var_0_37 then
					player:castSpell("self", 1)
				end
			end
		end
	end
end

local var_0_76

local function var_0_77()
	if player.mana / player.maxMana * 100 >= var_0_16.harass.mana:get() then
		if var_0_16.harass.ecombo:get() and player:spellSlot(0).state == 0 then
			local var_35_0 = var_0_40()

			if var_0_2.IsValidTarget(var_35_0) and var_35_0 and player:spellSlot(2).state == 0 then
				local var_35_1 = var_0_5.linear.get_prediction(var_0_8, var_35_0)

				if var_35_1 and player.pos:dist(vec3(var_35_1.endPos.x, var_35_0.y, var_35_1.endPos.y)) <= var_0_8.range and var_0_37 then
					local var_35_2 = var_0_37.pos:dist(vec3(var_35_1.endPos.x, var_35_0.y, var_35_1.endPos.y)) / var_0_8.speed
					local var_35_3 = player.pos:dist(vec3(var_35_1.endPos.x, var_35_0.y, var_35_1.endPos.y)) / var_0_8.speed + player.pos:dist(var_0_37.pos) / 1850
					local var_35_4 = player
					local var_35_5 = var_0_2.GetAllyHeroes()

					for iter_35_0, iter_35_1 in ipairs(var_35_5) do
						if iter_35_1 and iter_35_1 ~= player and iter_35_1.pos:dist(player.pos) <= var_0_10.range then
							local var_35_6 = iter_35_1.pos:dist(vec3(var_35_1.endPos.x, var_35_0.y, var_35_1.endPos.y)) / var_0_8.speed + iter_35_1.pos:dist(var_0_37.pos) / 1850

							if var_35_6 < var_35_3 then
								var_35_3 = var_35_6
								var_35_4 = iter_35_1
							end
						end
					end

					if var_35_3 < var_35_2 and (not var_35_4 == player or player.pos:dist(var_0_37.pos) > 100) and var_35_4.pos:dist(player.pos) < player.pos:dist(vec3(var_35_1.endPos.x, var_35_0.y, var_35_1.endPos.y)) then
						player:castSpell("obj", 2, var_35_4)

						return
					end
				end
			end
		end

		local var_35_7 = var_0_40()

		if var_0_16.harass.qcombo:get() and var_0_2.IsValidTarget(var_35_7) and var_35_7 then
			if var_0_37 then
				local var_35_8 = var_0_5.linear.get_prediction(var_0_8, var_35_7, vec2(var_0_37.x, var_0_37.z))

				if var_35_8 and player.pos:dist(vec3(var_35_8.endPos.x, var_35_7.y, var_35_8.endPos.y)) <= var_0_8.range then
					player:castSpell("pos", 0, vec3(var_35_8.endPos.x, var_35_7.y, var_35_8.endPos.y))
				end
			end

			if var_0_28 then
				local var_35_9 = var_0_5.linear.get_prediction(var_0_8, var_35_7, vec2(var_0_28.x, var_0_28.z))

				if var_35_9 and player.pos:dist(vec3(var_35_9.endPos.x, var_35_7.y, var_35_9.endPos.y)) <= var_0_8.range then
					player:castSpell("pos", 0, vec3(var_35_9.endPos.x, var_35_7.y, var_35_9.endPos.y))
				end
			end
		end

		if var_0_16.harass.ecombo:get() then
			local var_35_10 = var_0_40()

			if var_0_2.IsValidTarget(var_35_10) and var_35_10 and var_0_37 then
				local var_35_11 = var_0_5.linear.get_prediction(var_0_10, var_35_10)

				if var_35_11 then
					local var_35_12 = var_0_5.collision.get_prediction(var_0_10, var_35_11, var_35_10)
					local var_35_13 = var_0_5.collision.get_prediction(var_0_11, var_35_11, var_35_10)

					if var_0_72() >= 1 then
						player:castSpell("self", 2)
					end
				end
			end
		end

		if var_0_16.harass.wcombo:get() then
			if var_0_28 and #var_0_48(var_0_28, var_0_9.range) > 0 then
				if not var_0_16.harass.wbuffw:get() then
					player:castSpell("self", 1)
				end

				if var_0_16.harass.wbuffw:get() and player.buff.crestoftheancientgolem then
					player:castSpell("self", 1)
				end
			end

			if var_0_37 and #var_0_48(var_0_37.pos, var_0_9.range) > 0 then
				if not var_0_16.harass.wbuffw:get() then
					player:castSpell("self", 1)
				end

				if var_0_16.harass.wbuffw:get() and player.buff.crestoftheancientgolem then
					player:castSpell("self", 1)
				end
			end
		end
	end
end

local function var_0_78()
	local var_36_0 = var_0_2.GetEnemyHeroes()

	for iter_36_0, iter_36_1 in ipairs(var_36_0) do
		if iter_36_1 and var_0_2.IsValidTarget(iter_36_1) and not var_0_2.CheckBuffType(iter_36_1, 17) then
			local var_36_1 = var_0_2.GetShieldedHealth("AP", iter_36_1)

			if player:spellSlot(0).state == 0 and QDamage(iter_36_1) > iter_36_1.health and player:spellSlot(2).state == 0 then
				local var_36_2 = var_0_5.linear.get_prediction(var_0_8, iter_36_1)

				if var_36_2 and player.pos:dist(vec3(var_36_2.endPos.x, iter_36_1.y, var_36_2.endPos.y)) <= var_0_8.range and var_0_37 then
					local var_36_3 = var_0_37.pos:dist(vec3(var_36_2.endPos.x, iter_36_1.y, var_36_2.endPos.y)) / var_0_8.speed
					local var_36_4 = player.pos:dist(vec3(var_36_2.endPos.x, iter_36_1.y, var_36_2.endPos.y)) / var_0_8.speed + player.pos:dist(var_0_37.pos) / 1850
					local var_36_5 = player
					local var_36_6 = var_0_2.GetAllyHeroes()

					for iter_36_2, iter_36_3 in ipairs(var_36_6) do
						if iter_36_3 and iter_36_3 ~= player and iter_36_3.pos:dist(player.pos) <= var_0_10.range then
							local var_36_7 = iter_36_3.pos:dist(vec3(var_36_2.endPos.x, iter_36_1.y, var_36_2.endPos.y)) / var_0_8.speed + iter_36_3.pos:dist(var_0_37.pos) / 1850

							if var_36_7 < var_36_4 then
								var_36_4 = var_36_7
								var_36_5 = iter_36_3
							end
						end
					end

					if var_36_4 < var_36_3 and (not var_36_5 == player or player.pos:dist(var_0_37.pos) > 100) and var_36_5.pos:dist(player.pos) < player.pos:dist(vec3(var_36_2.endPos.x, iter_36_1.y, var_36_2.endPos.y)) then
						player:castSpell("obj", 2, var_36_5)

						return
					end
				end
			end

			if var_0_16.killsteal.ksq:get() and player:spellSlot(0).state == 0 and vec3(iter_36_1.x, iter_36_1.y, iter_36_1.z):dist(player) < var_0_8.range and var_36_1 <= QDamage(iter_36_1) then
				if var_0_28 then
					local var_36_8 = var_0_5.linear.get_prediction(var_0_8, iter_36_1, vec2(var_0_28.x, var_0_28.z))

					if var_36_8 and player.pos:dist(vec3(var_36_8.endPos.x, iter_36_1.y, var_36_8.endPos.y)) <= var_0_8.range then
						if not var_0_16.combo.slowq:get() then
							player:castSpell("pos", 0, vec3(var_36_8.endPos.x, iter_36_1.y, var_36_8.endPos.y))
						end

						if var_0_16.combo.slowq:get() and var_0_73(var_0_8, var_36_8, iter_36_1) then
							player:castSpell("pos", 0, vec3(var_36_8.endPos.x, iter_36_1.y, var_36_8.endPos.y))
						end
					end
				end

				if var_0_37 then
					local var_36_9 = var_0_5.linear.get_prediction(var_0_8, iter_36_1, vec2(var_0_37.x, var_0_37.z))

					if var_36_9 and player.pos:dist(vec3(var_36_9.endPos.x, iter_36_1.y, var_36_9.endPos.y)) <= var_0_8.range then
						var_0_76 = vec3(var_36_9.startPos.x, iter_36_1.y, var_36_9.startPos.y)

						if not var_0_16.combo.slowq:get() then
							player:castSpell("pos", 0, vec3(var_36_9.endPos.x, iter_36_1.y, var_36_9.endPos.y))
						end

						if var_0_16.combo.slowq:get() and var_0_73(var_0_8, var_36_9, iter_36_1) then
							player:castSpell("pos", 0, vec3(var_36_9.endPos.x, iter_36_1.y, var_36_9.endPos.y))
						end
					end
				end
			end

			if var_0_16.killsteal.ksw:get() then
				if var_0_28 and player:spellSlot(1).state == 0 and vec3(iter_36_1.x, iter_36_1.y, iter_36_1.z):dist(var_0_28) < var_0_9.range and var_36_1 <= WDamage(iter_36_1) then
					player:castSpell("self", 1)
				end

				if var_0_37 and player:spellSlot(1).state == 0 and vec3(iter_36_1.x, iter_36_1.y, iter_36_1.z):dist(var_0_37.pos) < var_0_9.range and var_36_1 <= WDamage(iter_36_1) then
					player:castSpell("self", 1)
				end
			end

			if var_0_16.killsteal.kse:get() and var_0_37 and EDamage(iter_36_1) > iter_36_1.health then
				local var_36_10 = var_0_5.linear.get_prediction(var_0_10, iter_36_1)

				if var_36_10 then
					local var_36_11, var_36_12, var_36_13 = VectorPointProjectionOnLineSegment(vec3(var_0_37.pos.x, 0, var_0_37.pos.z), vec3(player.pos.x, 0, player.pos.z), vec3(var_36_10.endPos.x, iter_36_1.pos.y, var_36_10.endPos.y))

					if var_36_13 and vec2(var_36_12.x, var_36_12.y):dist(var_36_10.endPos) <= 60 + iter_36_1.boundingRadius then
						player:castSpell("self", 2)
					end
				end
			end

			if var_0_16.killsteal.ksr:get() and iter_36_1.health <= RDamage(iter_36_1) and player:spellSlot(3).state == 0 then
				if var_0_37 then
					local var_36_14, var_36_15 = CheckEnemiesHitByR()

					if var_36_14 >= 1 then
						local var_36_16 = var_0_5.core.get_pos_after_time(iter_36_1, 0.5)

						if vec3(var_36_16.x, iter_36_1.y, var_36_16.y):dist(var_0_37.pos) <= var_0_12.range and iter_36_1.pos:dist(var_0_37.pos) <= var_0_12.range then
							player:castSpell("self", 3)
						end
					end
				end

				if var_0_28 then
					local var_36_17, var_36_18 = CheckEnemiesHitByR()

					if var_36_17 >= 1 then
						local var_36_19 = var_0_5.core.get_pos_after_time(iter_36_1, 0.5)

						if vec3(var_36_19.x, iter_36_1.y, var_36_19.y):dist(var_0_28) <= var_0_12.range and iter_36_1.pos:dist(var_0_28) <= var_0_12.range then
							player:castSpell("self", 3)
						end
					end
				end
			end

			if var_0_16.killsteal.usee:get() then
				local var_36_20 = var_0_2.GetAllyHeroes()

				for iter_36_4, iter_36_5 in ipairs(var_36_20) do
					if iter_36_5 and iter_36_5 ~= player and iter_36_5.pos:dist(player.pos) <= var_0_10.range then
						if player:spellSlot(3).state == 0 then
							if var_0_37 and RDamage(iter_36_1) >= iter_36_1.health then
								local var_36_21 = var_0_5.core.get_pos_after_time(iter_36_1, 0.5)

								if vec3(var_36_21.x, iter_36_1.y, var_36_21.y):dist(var_0_37.pos) >= var_0_12.range and iter_36_1.pos:dist(var_0_37.pos) >= var_0_12.range and iter_36_5.pos:dist(iter_36_1.pos) <= var_0_12.range then
									player:castSpell("obj", 2, iter_36_5)
								end
							end

							if var_0_28 and RDamage(iter_36_1) >= iter_36_1.health then
								local var_36_22 = var_0_5.core.get_pos_after_time(iter_36_1, 0.5)

								if vec3(var_36_22.x, iter_36_1.y, var_36_22.y):dist(var_0_28) >= var_0_12.range and iter_36_1.pos:dist(var_0_28) >= var_0_12.range and iter_36_5.pos:dist(iter_36_1.pos) <= var_0_12.range then
									player:castSpell("obj", 2, iter_36_5)
								end
							end
						end

						if player:spellSlot(2).state == 0 and var_0_16.killsteal.ksw:get() then
							if not var_0_37 and player:spellSlot(1).state == 0 and vec3(iter_36_1.x, iter_36_1.y, iter_36_1.z):dist(player) > var_0_9.range and var_36_1 <= WDamage(iter_36_1) and iter_36_5.pos:dist(iter_36_1.pos) <= var_0_9.range then
								player:castSpell("obj", 2, iter_36_5)
							end

							if var_0_37 and player:spellSlot(1).state == 0 and vec3(iter_36_1.x, iter_36_1.y, iter_36_1.z):dist(var_0_37.pos) > var_0_9.range and var_36_1 <= WDamage(iter_36_1) and iter_36_5.pos:dist(iter_36_1.pos) <= var_0_9.range then
								player:castSpell("obj", 2, iter_36_5)
							end
						end
					end
				end
			end
		end
	end
end

function GetNMinionsHit(arg_37_0, arg_37_1)
	local var_37_0 = 0

	for iter_37_0 = 0, objManager.minions.size[TEAM_ENEMY] - 1 do
		local var_37_1 = objManager.minions[TEAM_ENEMY][iter_37_0]

		if var_37_1 and not var_37_1.isDead and var_37_1.moveSpeed > 0 and var_37_1.isTargetable and var_37_1.isVisible and var_37_1.type == TYPE_MINION and var_37_1.pos:dist(player.pos) <= var_0_8.range and arg_37_1 >= var_37_1.pos:dist(arg_37_0) then
			var_37_0 = var_37_0 + 1
		end
	end

	return var_37_0
end

function GetNMinionsHitE(arg_38_0)
	local var_38_0 = 0
	local var_38_1 = vec3(arg_38_0.x, 0, arg_38_0.z)
	local var_38_2 = vec3(player.x, 0, player.z)

	for iter_38_0 = 0, objManager.minions.size[TEAM_ENEMY] - 1 do
		local var_38_3 = objManager.minions[TEAM_ENEMY][iter_38_0]

		if var_38_3 and var_38_3.isVisible and not var_38_3.isDead and var_38_3.moveSpeed > 0 and var_38_3.isTargetable then
			local var_38_4 = vec3(var_38_3.x, 0, var_38_3.z)
			local var_38_5 = VectorPointProjectionOnLineSegment(var_38_1, var_38_2, var_38_4)

			if vec2(var_38_4.x, var_38_4.z):dist(vec2(var_38_5.x, var_38_5.y)) < 80 + var_38_3.boundingRadius then
				var_38_0 = var_38_0 + 1
			end
		end
	end

	return var_38_0
end

local function var_0_79()
	if var_0_43 then
		local var_39_0 = 0
		local var_39_1

		if var_0_16.farming.laneclear.farmq:get() and player:spellSlot(0).state == 0 then
			for iter_39_0 = 0, objManager.minions.size[TEAM_ENEMY] - 1 do
				local var_39_2 = objManager.minions[TEAM_ENEMY][iter_39_0]

				if var_39_2 and not var_39_2.isDead and var_39_2.moveSpeed > 0 and var_39_2.isTargetable and var_39_2.isVisible and var_39_2.pos:dist(player.pos) < var_0_8.range and var_39_2.path.count == 0 and var_39_2.type == TYPE_MINION then
					if var_0_37 then
						local var_39_3 = var_0_5.linear.get_prediction(var_0_8, var_39_2, vec2(var_0_37.x, var_0_37.z))

						if var_39_3 and player.pos:dist(vec3(var_39_3.endPos.x, var_39_2.y, var_39_3.endPos.y)) <= var_0_8.range then
							local var_39_4 = vec3(var_39_3.endPos.x, var_39_2.y, var_39_3.endPos.y)
							local var_39_5 = GetNMinionsHit(var_39_2.pos, var_0_9.range)

							if var_39_0 <= var_39_5 then
								var_39_0 = var_39_5
								var_39_1 = var_39_4
							end
						end
					end

					if var_0_28 then
						local var_39_6 = var_0_5.linear.get_prediction(var_0_8, var_39_2, vec2(var_0_28.x, var_0_28.z))

						if var_39_6 and player.pos:dist(vec3(var_39_6.endPos.x, var_39_2.y, var_39_6.endPos.y)) <= var_0_8.range then
							local var_39_7 = vec3(var_39_6.endPos.x, var_39_2.y, var_39_6.endPos.y)
							local var_39_8 = GetNMinionsHit(var_39_2.pos, var_0_9.range)

							if var_39_0 <= var_39_8 then
								var_39_0 = var_39_8
								var_39_1 = var_39_7
							end
						end
					end
				end
			end

			if var_39_0 >= var_0_16.farming.laneclear.hitq:get() and var_39_1 then
				player:castSpell("pos", 0, var_39_1)
			end
		end

		if var_0_16.farming.laneclear.farmw:get() and player:spellSlot(1).state == 0 and var_0_37 and GetNMinionsHit(var_0_37.pos, var_0_9.range) >= var_0_16.farming.laneclear.hitw:get() then
			player:castSpell("self", 1)
		end

		if var_0_16.farming.laneclear.farme:get() and player:spellSlot(2).state == 0 and var_0_37 and GetNMinionsHitE(var_0_37.pos) >= 3 and (player:spellSlot(1).state ~= 0 or not var_0_16.farming.laneclear.farmw:get()) then
			player:castSpell("self", 2)
		end
	end
end

function Cross(arg_40_0, arg_40_1)
	return vec3(arg_40_0.y * arg_40_1.z - arg_40_0.z * arg_40_1.y, arg_40_0.z * arg_40_1.x - arg_40_0.x * arg_40_1.z, arg_40_0.x * arg_40_1.y - arg_40_0.y * arg_40_1.x)
end

local var_0_80 = 0
local var_0_81 = 0
local var_0_82 = math.sin
local var_0_83 = math.cos

function RotateZ(arg_41_0, arg_41_1, arg_41_2)
	local var_41_0 = (arg_41_0.x - arg_41_1.x) * var_0_83(arg_41_2) - (arg_41_1.z - arg_41_0.z) * var_0_82(arg_41_2)
	local var_41_1 = (arg_41_1.z - arg_41_0.z) * var_0_83(arg_41_2) - (arg_41_0.x - arg_41_1.x) * var_0_82(arg_41_2)

	return vec3(var_41_0, arg_41_0.y, var_41_1 + arg_41_1.z)
end

local var_0_84 = graphics.argb(255, 255, 255, 255)
local var_0_85 = graphics.argb(255, 255, 255, 255)
local var_0_86 = graphics.argb(255, 46, 190, 244)
local var_0_87 = graphics.argb(255, 46, 190, 244)
local var_0_88 = graphics.argb(100, 255, 255, 244)
local var_0_89 = 0

function BallDraw(arg_42_0)
	if var_0_38 then
		arg_42_0 = vec3(arg_42_0.x, arg_42_0.y - 120, arg_42_0.z)
	end

	if var_0_16.draws.drawcolors:get() == 2 then
		var_0_84 = graphics.argb(255, 255, 255, 255)
		var_0_85 = graphics.argb(255, 255, 255, 255)
		var_0_86 = graphics.argb(255, 46, 190, 244)
		var_0_87 = graphics.argb(255, 46, 190, 244)
	end

	if var_0_16.draws.drawcolors:get() == 1 and os.clock() - var_0_80 > 0.3 then
		var_0_80 = os.clock()
		var_0_84 = graphics.argb(255, math.random(55, 255), math.random(55, 255), math.random(55, 255))
		var_0_85 = var_0_84
		var_0_86 = graphics.argb(255, math.random(55, 255), math.random(55, 255), math.random(55, 255))
		var_0_87 = var_0_86
	end

	local var_42_0 = Cross(arg_42_0, vec3(0, 1, 0)):norm() * 75
	local var_42_1 = vec3(0, 0, 0) - vec3(var_42_0.x, var_42_0.y, var_42_0.z)
	local var_42_2 = arg_42_0:norm() * 75
	local var_42_3 = arg_42_0
	local var_42_4 = var_42_3
	local var_42_5 = var_42_3
	local var_42_6 = var_42_3
	local var_42_7 = var_42_3
	local var_42_8 = var_42_3
	local var_42_9 = var_42_3
	local var_42_10 = var_42_3
	local var_42_11 = var_42_3
	local var_42_12 = Cross(var_42_0, vec3(0, 1, 0)):norm() * 75
	local var_42_13 = Cross(var_42_1, vec3(0, 1, 0)):norm() * 75
	local var_42_14 = vec3(var_42_3.x, 0, var_42_3.y)

	if var_0_81 > 360 then
		var_0_81 = 0
	end

	var_0_81 = var_0_81 + math.rad(0.5)

	local var_42_15 = 0.05

	for iter_42_0 = 0, 5, 2 do
		local var_42_16 = var_42_14 + RotateZ(var_42_0 + arg_42_0, arg_42_0, var_0_81)
		local var_42_17 = var_42_14 + RotateZ(var_42_1 + arg_42_0, arg_42_0, var_0_81)
		local var_42_18 = var_42_14 + RotateZ(var_42_1 + arg_42_0, arg_42_0, var_0_81)
		local var_42_19 = var_42_14 + RotateZ(arg_42_0 - var_42_2, arg_42_0, var_0_81)
		local var_42_20 = graphics.world_to_screen(var_42_16)
		local var_42_21 = graphics.world_to_screen(var_42_17)
		local var_42_22 = graphics.world_to_screen(var_42_18)
		local var_42_23 = graphics.world_to_screen(var_42_19)
		local var_42_24 = var_42_20 - vec2(iter_42_0 * var_42_15, iter_42_0 * var_42_15)
		local var_42_25 = var_42_22 + vec2(iter_42_0 * var_42_15, -(iter_42_0 * var_42_15))
		local var_42_26 = var_42_23 + vec2(-iter_42_0 * var_42_15, iter_42_0 * var_42_15)
		local var_42_27 = var_42_21 + vec2(iter_42_0 * var_42_15, iter_42_0 * var_42_15)

		graphics.draw_line_2D(var_42_24.x, var_42_24.y, var_42_25.x, var_42_25.y, 4, var_0_84)
		graphics.draw_line_2D(var_42_24.x, var_42_24.y, var_42_26.x, var_42_26.y, 4, var_0_86)
		graphics.draw_line_2D(var_42_25.x, var_42_25.y, var_42_27.x, var_42_27.y, 4, var_0_87)
		graphics.draw_line_2D(var_42_26.x, var_42_26.y, var_42_27.x, var_42_27.y, 4, var_0_85)
	end

	local var_42_28 = 0.1

	for iter_42_1 = 0, 6, 2 do
		local var_42_29 = var_42_14 + RotateZ(var_42_0 * 0.73 + var_42_12 * 0.8 + arg_42_0, arg_42_0, -var_0_81)
		local var_42_30 = var_42_14 + RotateZ(var_42_0 * 0.73 - var_42_12 * 0.8 + arg_42_0, arg_42_0, -var_0_81)
		local var_42_31 = var_42_14 + RotateZ(var_42_1 * 0.73 - var_42_13 * 0.8 + arg_42_0, arg_42_0, -var_0_81)
		local var_42_32 = var_42_14 + RotateZ(var_42_1 * 0.73 + var_42_13 * 0.8 + arg_42_0, arg_42_0, -var_0_81)
		local var_42_33 = graphics.world_to_screen(var_42_29)
		local var_42_34 = graphics.world_to_screen(var_42_32)
		local var_42_35 = graphics.world_to_screen(var_42_30)
		local var_42_36 = graphics.world_to_screen(var_42_31)

		var_42_33.x = var_42_33.x - iter_42_1 * var_42_28
		var_42_34.x = var_42_34.x + iter_42_1 * var_42_28
		var_42_35.x = var_42_35.x - iter_42_1 * var_42_28
		var_42_36.x = var_42_36.x + iter_42_1 * var_42_28

		graphics.draw_line_2D(var_42_33.x, var_42_33.y, var_42_35.x, var_42_35.y, 4, var_0_84)
		graphics.draw_line_2D(var_42_33.x, var_42_33.y, var_42_36.x, var_42_36.y, 4, var_0_86)
		graphics.draw_line_2D(var_42_35.x, var_42_35.y, var_42_34.x, var_42_34.y, 4, var_0_87)
		graphics.draw_line_2D(var_42_36.x, var_42_36.y, var_42_34.x, var_42_34.y, 4, var_0_85)
	end

	if var_0_37 then
		local var_42_37 = 100
		local var_42_38 = 50
		local var_42_39 = vec3(-1, -1, -1)
		local var_42_40 = vec2(-1, -1)
		local var_42_41 = vec2(-1, -1)

		for iter_42_2 = 0, var_42_37 do
			local var_42_42 = 31.415926 * iter_42_2 / var_42_37
			local var_42_43 = var_42_38 / var_42_37 * iter_42_2 * var_0_83(var_42_42)
			local var_42_44 = var_42_38 / var_42_37 * iter_42_2 * var_0_82(var_42_42)
			local var_42_45 = vec3(var_42_43 + var_0_37.x, var_0_37.y, var_42_44 + var_0_37.z)

			if var_42_39 == vec3(-1, -1, -1) then
				var_42_39 = var_0_37
			end

			local var_42_46 = var_42_45
			local var_42_47 = var_42_39
			local var_42_48 = graphics.world_to_screen(var_42_46)
			local var_42_49 = graphics.world_to_screen(var_42_47)

			graphics.draw_line_2D(var_42_48.x, var_42_48.y, var_42_49.x, var_42_49.y, 2, var_0_88)
		end
	end
end

function BallDraw2(arg_43_0)
	if var_0_38 then
		arg_43_0 = vec3(arg_43_0.x, arg_43_0.y, arg_43_0.z)
	end

	if var_0_16.draws.drawcolors:get() == 2 then
		var_0_84 = graphics.argb(255, 46, 190, 244)
		var_0_85 = graphics.argb(255, 46, 190, 244)
		var_0_86 = graphics.argb(255, 46, 190, 244)
		var_0_87 = graphics.argb(255, 46, 190, 244)
	end

	if var_0_34 then
		var_0_84 = graphics.argb(255, 217, 83, 79)
		var_0_85 = var_0_84
		var_0_86 = var_0_84
		var_0_87 = var_0_84
	end

	if var_0_16.draws.drawcolors:get() == 1 and var_0_37 and os.clock() - var_0_80 > 0.3 then
		var_0_80 = os.clock()
		var_0_84 = graphics.argb(255, math.random(55, 255), math.random(55, 255), math.random(55, 255))
		var_0_85 = var_0_84
		var_0_86 = graphics.argb(255, math.random(55, 255), math.random(55, 255), math.random(55, 255))
		var_0_87 = var_0_86
	end

	local var_43_0 = Cross(arg_43_0, vec3(0, 1, 0)):norm() * 75
	local var_43_1 = vec3(0, 0, 0) - vec3(var_43_0.x, var_43_0.y, var_43_0.z)
	local var_43_2 = arg_43_0:norm() * 75
	local var_43_3 = arg_43_0
	local var_43_4 = var_43_3
	local var_43_5 = var_43_3
	local var_43_6 = var_43_3
	local var_43_7 = var_43_3
	local var_43_8 = var_43_3
	local var_43_9 = var_43_3
	local var_43_10 = var_43_3
	local var_43_11 = var_43_3
	local var_43_12 = Cross(var_43_0, vec3(0, 1, 0)):norm() * 75
	local var_43_13 = Cross(var_43_1, vec3(0, 1, 0)):norm() * 75
	local var_43_14 = vec3(var_43_3.x, 0, var_43_3.y)
	local var_43_15 = 0.1

	for iter_43_0 = 0, 5, 2 do
		local var_43_16 = var_43_0 + arg_43_0
		local var_43_17 = var_43_1 + arg_43_0
		local var_43_18 = arg_43_0 + var_43_2
		local var_43_19 = arg_43_0 - var_43_2
		local var_43_20 = graphics.world_to_screen(var_43_16)
		local var_43_21 = graphics.world_to_screen(var_43_17)
		local var_43_22 = graphics.world_to_screen(var_43_18)
		local var_43_23 = graphics.world_to_screen(var_43_19)
		local var_43_24 = var_43_20 - vec2(iter_43_0 * var_43_15, iter_43_0 * var_43_15)
		local var_43_25 = var_43_22 + vec2(iter_43_0 * var_43_15, -(iter_43_0 * var_43_15))
		local var_43_26 = var_43_23 + vec2(-iter_43_0 * var_43_15, iter_43_0 * var_43_15)
		local var_43_27 = var_43_21 + vec2(iter_43_0 * var_43_15, iter_43_0 * var_43_15)

		graphics.draw_line_2D(var_43_24.x, var_43_24.y, var_43_25.x, var_43_25.y, 4, var_0_84)
		graphics.draw_line_2D(var_43_24.x, var_43_24.y, var_43_26.x, var_43_26.y, 4, var_0_86)
		graphics.draw_line_2D(var_43_25.x, var_43_25.y, var_43_27.x, var_43_27.y, 4, var_0_87)
		graphics.draw_line_2D(var_43_26.x, var_43_26.y, var_43_27.x, var_43_27.y, 4, var_0_85)
	end

	local var_43_28 = 0.1

	for iter_43_1 = 0, 6, 2 do
		local var_43_29 = arg_43_0 + var_43_0 * 0.73 + var_43_12 * 0.8
		local var_43_30 = arg_43_0 + var_43_0 * 0.73 - var_43_12 * 0.8
		local var_43_31 = arg_43_0 + var_43_1 * 0.73 - var_43_13 * 0.8
		local var_43_32 = arg_43_0 + var_43_1 * 0.73 + var_43_13 * 0.8
		local var_43_33 = graphics.world_to_screen(var_43_29)
		local var_43_34 = graphics.world_to_screen(var_43_32)
		local var_43_35 = graphics.world_to_screen(var_43_30)
		local var_43_36 = graphics.world_to_screen(var_43_31)

		var_43_33.x = var_43_33.x - iter_43_1 * var_43_28
		var_43_34.x = var_43_34.x + iter_43_1 * var_43_28
		var_43_35.x = var_43_35.x - iter_43_1 * var_43_28
		var_43_36.x = var_43_36.x + iter_43_1 * var_43_28

		graphics.draw_line_2D(var_43_33.x, var_43_33.y, var_43_35.x, var_43_35.y, 4, var_0_84)
		graphics.draw_line_2D(var_43_33.x, var_43_33.y, var_43_36.x, var_43_36.y, 4, var_0_86)
		graphics.draw_line_2D(var_43_35.x, var_43_35.y, var_43_34.x, var_43_34.y, 4, var_0_87)
		graphics.draw_line_2D(var_43_36.x, var_43_36.y, var_43_34.x, var_43_34.y, 4, var_0_85)
	end

	local var_43_37 = 0.1

	for iter_43_2 = 0, 6, 3 do
		local var_43_38 = arg_43_0 + var_43_0 * 0.73 - var_43_12 * 0.8
		local var_43_39 = arg_43_0 + var_43_0 * 0.73 + var_43_12 * 0.8
		local var_43_40 = arg_43_0 + var_43_1 * 0.73 - var_43_13 * 0.8
		local var_43_41 = arg_43_0 + var_43_1 * 0.73 + var_43_13 * 0.8
		local var_43_42 = graphics.world_to_screen(var_43_38)
		local var_43_43 = graphics.world_to_screen(var_43_41)
		local var_43_44 = graphics.world_to_screen(var_43_39)
		local var_43_45 = graphics.world_to_screen(var_43_40)

		var_43_42.x = var_43_42.x - iter_43_2 * var_43_37
		var_43_43.x = var_43_43.x + iter_43_2 * var_43_37
		var_43_44.x = var_43_44.x - iter_43_2 * var_43_37
		var_43_45.x = var_43_45.x + iter_43_2 * var_43_37

		graphics.draw_line_2D(var_43_42.x, var_43_42.y, var_43_44.x, var_43_44.y, 2, var_0_84)
		graphics.draw_line_2D(var_43_42.x, var_43_42.y, var_43_45.x, var_43_45.y, 2, var_0_86)
		graphics.draw_line_2D(var_43_44.x, var_43_44.y, var_43_43.x, var_43_43.y, 2, var_0_87)
		graphics.draw_line_2D(var_43_45.x, var_43_45.y, var_43_43.x, var_43_43.y, 2, var_0_85)
	end
end

local var_0_90 = graphics.argb(255, 255, 255, 255)
local var_0_91 = 0

function VectorPointProjectionOnLineSegment(arg_44_0, arg_44_1, arg_44_2)
	local var_44_0 = arg_44_2.x
	local var_44_1 = arg_44_2.z or arg_44_2.y
	local var_44_2 = arg_44_0.x
	local var_44_3 = arg_44_0.z or arg_44_0.y
	local var_44_4 = arg_44_1.x
	local var_44_5 = arg_44_1.z or arg_44_1.y
	local var_44_6 = ((var_44_0 - var_44_2) * (var_44_4 - var_44_2) + (var_44_1 - var_44_3) * (var_44_5 - var_44_3)) / ((var_44_4 - var_44_2)^2 + (var_44_5 - var_44_3)^2)
	local var_44_7 = {
		x = var_44_2 + var_44_6 * (var_44_4 - var_44_2),
		y = var_44_3 + var_44_6 * (var_44_5 - var_44_3)
	}
	local var_44_8 = var_44_6 < 0 and 0 or var_44_6 > 1 and 1 or var_44_6
	local var_44_9 = var_44_8 == var_44_6

	return var_44_9 and var_44_7 or {
		x = var_44_2 + var_44_8 * (var_44_4 - var_44_2),
		y = var_44_3 + var_44_8 * (var_44_5 - var_44_3)
	}, var_44_7, var_44_9
end

function DrawRectangleOutline(arg_45_0, arg_45_1, arg_45_2)
	local var_45_0 = arg_45_0 + (arg_45_1 - arg_45_0):perp1():norm() * arg_45_2
	local var_45_1 = arg_45_0 + (arg_45_1 - arg_45_0):perp2():norm() * arg_45_2
	local var_45_2 = arg_45_1 + (arg_45_0 - arg_45_1):perp1():norm() * arg_45_2
	local var_45_3 = arg_45_1 + (arg_45_0 - arg_45_1):perp2():norm() * arg_45_2
	local var_45_4 = graphics.world_to_screen(var_45_0)
	local var_45_5 = graphics.world_to_screen(var_45_1)
	local var_45_6 = graphics.world_to_screen(var_45_2)
	local var_45_7 = graphics.world_to_screen(var_45_3)

	graphics.draw_line_2D(var_45_4.x, var_45_4.y, var_45_5.x, var_45_5.y, math.ceil(arg_45_2 / 100), graphics.argb(255, 255, 255, 255))
	graphics.draw_line_2D(var_45_5.x, var_45_5.y, var_45_6.x, var_45_6.y, math.ceil(arg_45_2 / 100), graphics.argb(255, 255, 255, 255))
	graphics.draw_line_2D(var_45_6.x, var_45_6.y, var_45_7.x, var_45_7.y, math.ceil(arg_45_2 / 100), graphics.argb(255, 255, 255, 255))
	graphics.draw_line_2D(var_45_4.x, var_45_4.y, var_45_7.x, var_45_7.y, math.ceil(arg_45_2 / 100), graphics.argb(255, 255, 255, 255))
end

local var_0_92 = false
local var_0_93 = false
local var_0_94 = false
local var_0_95 = vec3(0, 0, 0)
local var_0_96 = os.clock() + 0.1

local function var_0_97()
	local var_46_0
	local var_46_1 = 9999
	local var_46_2 = var_0_2.GetAllyHeroes()

	for iter_46_0, iter_46_1 in ipairs(var_46_2) do
		if iter_46_1 and (iter_46_1 ~= player or var_0_16.combo.include:get()) then
			local var_46_3 = vec3(iter_46_1.x, iter_46_1.y, iter_46_1.z)

			if var_46_3:dist(player.pos) < var_0_10.range + 300 then
				if mousePos:dist(player.pos) <= var_0_10.range then
					local var_46_4 = var_46_3:dist(mousePos)

					if var_46_4 < var_46_1 then
						var_46_0 = iter_46_1
						var_46_1 = var_46_4
					end
				end

				if mousePos:dist(player.pos) >= var_0_10.range and iter_46_1 ~= player then
					local var_46_5 = var_46_3:dist(mousePos)

					if var_46_5 < var_46_1 then
						var_46_0 = iter_46_1
						var_46_1 = var_46_5
					end
				end
			end
		end
	end

	return var_46_0
end

local function var_0_98()
	if var_0_16.misc.fleeKey:get() then
		player:move(mousePos)

		if var_0_16.misc.fleeMode:get() == 1 then
			local var_47_0 = var_0_41()

			if var_0_2.IsValidTarget(var_47_0) and var_47_0.path.isActive and player.pos:dist(var_47_0.path.point[1]) > player.pos:dist(var_47_0.path.point[0]) and var_47_0.pos:dist(player.pos) > 350 then
				if var_0_37 then
					local var_47_1 = var_0_5.linear.get_prediction(var_0_8, var_47_0, vec2(var_0_37.x, var_0_37.z))

					if var_47_1 and player.pos:dist(vec3(var_47_1.endPos.x, var_47_0.y, var_47_1.endPos.y)) <= var_0_8.range then
						player:castSpell("pos", 0, vec3(var_47_1.endPos.x, var_47_0.y, var_47_1.endPos.y))
					end
				end

				if var_0_28 then
					local var_47_2 = var_0_5.linear.get_prediction(var_0_8, var_47_0, vec2(var_0_28.x, var_0_28.z))

					if var_47_2 and player.pos:dist(vec3(var_47_2.endPos.x, var_47_0.y, var_47_2.endPos.y)) <= var_0_8.range then
						player:castSpell("pos", 0, vec3(var_47_2.endPos.x, var_47_0.y, var_47_2.endPos.y))
					end
				end

				if var_0_28 and #var_0_48(var_0_28, var_0_9.range) > 0 then
					player:castSpell("self", 1)
				end

				if var_0_37 and #var_0_48(var_0_37.pos, var_0_9.range) > 0 then
					player:castSpell("self", 1)
				end
			end

			if #var_0_48(player.pos, 700) == 0 then
				if var_0_37 then
					if var_0_37.pos:dist(player.pos) <= var_0_9.range then
						player:castSpell("self", 1)
					else
						player:castSpell("self", 2)
						player:castSpell("self", 1)
					end
				end

				if var_0_28 then
					if var_0_28:dist(player.pos) <= var_0_9.range then
						player:castSpell("self", 1)
					else
						player:castSpell("self", 2)
						player:castSpell("self", 1)
					end
				end
			end

			if #var_0_48(player.pos, 350) == 1 then
				if var_0_37 then
					if var_0_37.pos:dist(player.pos) <= var_0_9.range then
						player:castSpell("self", 1)
					else
						player:castSpell("self", 2)
						player:castSpell("self", 1)
					end
				end

				if var_0_28 then
					if var_0_28:dist(player.pos) <= var_0_9.range then
						player:castSpell("self", 1)
					else
						player:castSpell("self", 2)
						player:castSpell("self", 1)
					end
				end
			end
		end

		if var_0_16.misc.fleeMode:get() == 2 then
			if var_0_37 then
				if var_0_37.pos:dist(player.pos) <= var_0_9.range then
					player:castSpell("self", 1)
				else
					player:castSpell("self", 2)
					player:castSpell("self", 1)
				end
			end

			if var_0_28 then
				if var_0_28:dist(player.pos) <= var_0_9.range then
					player:castSpell("self", 1)
				else
					player:castSpell("self", 2)
					player:castSpell("self", 1)
				end
			end
		end
	end
end

local function var_0_99()
	if player.isDead then
		return
	end

	var_0_98()

	if player:spellSlot(0).state == 0 then
		local var_48_0 = var_0_40()

		if var_0_2.IsValidTarget(var_48_0) and var_48_0 and player:spellSlot(2).state == 0 then
			local var_48_1 = var_0_5.linear.get_prediction(var_0_8, var_48_0)

			if var_48_1 and player.pos:dist(vec3(var_48_1.endPos.x, var_48_0.y, var_48_1.endPos.y)) <= var_0_8.range and var_0_37 then
				local var_48_2 = var_0_37.pos:dist(vec3(var_48_1.endPos.x, var_48_0.y, var_48_1.endPos.y)) / var_0_8.speed
				local var_48_3 = player.pos:dist(vec3(var_48_1.endPos.x, var_48_0.y, var_48_1.endPos.y)) / var_0_8.speed + player.pos:dist(var_0_37.pos) / 1850
				local var_48_4 = player
				local var_48_5 = var_0_2.GetAllyHeroes()

				for iter_48_0, iter_48_1 in ipairs(var_48_5) do
					if iter_48_1 and iter_48_1 ~= player and iter_48_1.pos:dist(player.pos) <= var_0_10.range then
						local var_48_6 = iter_48_1.pos:dist(vec3(var_48_1.endPos.x, var_48_0.y, var_48_1.endPos.y)) / var_0_8.speed + iter_48_1.pos:dist(var_0_37.pos) / 1850

						if var_48_6 < var_48_3 then
							var_48_3 = var_48_6
							var_48_4 = iter_48_1
						end
					end
				end

				if var_48_3 < var_48_2 and (not var_48_4 == player or player.pos:dist(var_0_37.pos) > 100) and var_48_4.pos:dist(player.pos) < player.pos:dist(vec3(var_48_1.endPos.x, var_48_0.y, var_48_1.endPos.y)) then
					-- block empty
				end
			end
		end
	end

	if var_0_16.combo.magnete:get() then
		local var_48_7 = var_0_97()

		if var_48_7 then
			player:castSpell("obj", 2, var_48_7)
		end
	end

	if var_0_16.harass.autoharas:get() and player.mana / player.maxMana * 100 >= var_0_16.harass.mana:get() then
		local var_48_8 = var_0_40()

		if var_0_2.IsValidTarget(var_48_8) and var_48_8 and var_0_16.harass.qauto:get() then
			if var_0_37 then
				local var_48_9 = var_0_5.linear.get_prediction(var_0_8, var_48_8, vec2(var_0_37.x, var_0_37.z))

				if var_48_9 and player.pos:dist(vec3(var_48_9.endPos.x, var_48_8.y, var_48_9.endPos.y)) <= var_0_8.range then
					player:castSpell("pos", 0, vec3(var_48_9.endPos.x, var_48_8.y, var_48_9.endPos.y))
				end
			end

			if var_0_28 then
				local var_48_10 = var_0_5.linear.get_prediction(var_0_8, var_48_8, vec2(var_0_28.x, var_0_28.z))

				if var_48_10 and player.pos:dist(vec3(var_48_10.endPos.x, var_48_8.y, var_48_10.endPos.y)) <= var_0_8.range then
					player:castSpell("pos", 0, vec3(var_48_10.endPos.x, var_48_8.y, var_48_10.endPos.y))
				end
			end
		end

		if player:spellSlot(1).state == 0 and var_0_16.harass.wcombo:get() and var_0_16.harass.wauto:get() then
			if var_0_28 and #var_0_48(var_0_28, var_0_9.range) > 0 then
				if not var_0_16.harass.wbuff:get() then
					player:castSpell("self", 1)
				end

				if var_0_16.harass.wbuff:get() and player.buff.crestoftheancientgolem then
					player:castSpell("self", 1)
				end
			end

			if var_0_37 and #var_0_48(var_0_37.pos, var_0_9.range) > 0 then
				if not var_0_16.harass.wbuff:get() then
					player:castSpell("self", 1)
				end

				if var_0_16.harass.wbuff:get() and player.buff.crestoftheancientgolem then
					player:castSpell("self", 1)
				end
			end
		end
	end

	var_0_47()
	var_0_70()

	if var_0_2.CheckBuff2(player, "orianaghostself") and game.time > var_0_61 then
		var_0_28 = player.pos
	end

	local var_48_11 = var_0_2.GetAllyHeroes()

	for iter_48_2, iter_48_3 in ipairs(var_48_11) do
		if iter_48_3 and iter_48_3 ~= player and var_0_2.CheckBuff2(iter_48_3, "orianaghost") and game.time > var_0_61 then
			var_0_28 = iter_48_3.pos
		end
	end

	if var_0_34 or var_0_38 or var_0_37 or var_0_95 then
		var_0_27 = false
	end

	if not var_0_34 and not var_0_38 and not var_0_37 and not var_0_95 and var_0_67 < os.clock() then
		var_0_27 = true
	end

	if var_0_38 == nil and var_0_37 == nil and var_0_34 == nil then
		var_0_95 = nil
	end

	if var_0_37 == nil and var_0_56 then
		var_0_95 = var_0_56
	end

	if var_0_37 or var_0_38 then
		var_0_28 = vec3(0, 0, 0)
	end

	if var_0_96 > os.clock() then
		if var_0_93 == false and var_0_2.CheckBuff2(player, "orianaghostself") then
			var_0_28 = player.pos
			var_0_29 = os.clock() + 0.2
			var_0_93 = true
		end

		if var_0_94 == false then
			objManager.loop(function(arg_49_0)
				if arg_49_0 and arg_49_0.pos:dist(player.pos) < 2000 and (arg_49_0.name:find("glow_center") or arg_49_0.name:find("yomu_ring") or arg_49_0.name:find("low_green")) then
					var_0_94 = true
				end
			end)
		end

		if var_0_92 == false then
			objManager.loop(function(arg_50_0)
				if arg_50_0 and arg_50_0.owner ~= player and arg_50_0.name:find("_bind") then
					local var_50_0 = var_0_2.GetAllyHeroes()

					for iter_50_0, iter_50_1 in ipairs(var_50_0) do
						if iter_50_1 then
							var_0_28 = iter_50_1.pos
						end
					end

					var_0_92 = true
				end
			end)
		end
	end

	if var_0_35 then
		var_0_38 = var_0_35
	end

	if var_0_36 then
		var_0_38 = var_0_36
	end

	if var_0_35 == nil and var_0_36 == nil then
		var_0_38 = nil
	end

	if not var_0_34 then
		var_0_56 = nil
	end

	if var_0_56 and var_0_28 then
		var_0_28 = vec3(0, 0, 0)
	end

	if var_0_16.combo.autoq:get() and player:spellSlot(0).state == 0 then
		local var_48_12 = {}
		local var_48_13 = var_0_6.get_result(function(arg_51_0, arg_51_1, arg_51_2)
			if arg_51_2 <= var_0_8.range and arg_51_1.path.isActive and arg_51_1.path.isDashing then
				arg_51_0.obj = arg_51_1

				return true
			end
		end).obj

		if var_48_13 then
			local var_48_14 = var_0_5.core.lerp(var_48_13.path, network.latency + var_0_8.delay, var_48_13.path.dashSpeed)

			if var_48_14 and var_48_14:dist(player.path.serverPos2D) <= var_0_8.range then
				var_48_12.startPos = player.path.serverPos2D
				var_48_12.endPos = vec2(var_48_14.x, var_48_14.y)

				player:castSpell("pos", 0, vec3(var_48_14.x, var_48_13.y, var_48_14.y))
			end
		end
	end

	if var_0_7.menu.combat.key:get() then
		var_0_74()
	end

	if var_0_7.menu.hybrid.key:get() then
		var_0_77()
	end

	if var_0_7.menu.lane_clear.key:get() then
		var_0_79()
		var_0_75()
	end

	var_0_78()

	if player.mana / player.maxMana * 100 >= var_0_16.SpellsMenu.mana:get() and var_0_16.SpellsMenu.enable:get() then
		local var_48_15 = var_0_2.GetAllyHeroes()

		for iter_48_4, iter_48_5 in ipairs(var_48_15) do
			if iter_48_5 and var_0_16.SpellsMenu.blacklist[iter_48_5.charName] and not var_0_16.SpellsMenu.blacklist[iter_48_5.charName]:get() and (var_0_2.CheckBuffType(iter_48_5, 23) or var_0_2.CheckBuffType(iter_48_5, 22) or var_0_2.CheckBuffType(iter_48_5, 12) or var_0_2.CheckBuffType(iter_48_5, 21)) and iter_48_5.pos:dist(player.pos) <= var_0_10.range and var_0_16.SpellsMenu.health:get() >= iter_48_5.health / iter_48_5.maxHealth * 100 then
				player:castSpell("obj", 2, iter_48_5)
			end
		end
	end

	if var_0_4 and game.time >= var_0_52 and player.mana / player.maxMana * 100 >= var_0_16.SpellsMenu.mana:get() and var_0_16.SpellsMenu.enable:get() then
		for iter_48_6 = 1, #var_0_4.core.active_spells do
			local var_48_16 = var_0_4.core.active_spells[iter_48_6]
			local var_48_17 = var_0_2.GetAllyHeroes()

			for iter_48_7, iter_48_8 in ipairs(var_48_17) do
				if iter_48_8 and not var_0_16.SpellsMenu.blacklist[iter_48_8.charName]:get() then
					if var_48_16.data.spell_type == "Target" and var_48_16.target == iter_48_8 and var_48_16.owner.type == TYPE_HERO then
						if not var_48_16.name:find("crit") and not var_48_16.name:find("basicattack") and var_0_16.SpellsMenu.targeteteteteteed:get() and iter_48_8.pos:dist(player.pos) <= var_0_10.range and var_0_16.SpellsMenu.health:get() >= iter_48_8.health / iter_48_8.maxHealth * 100 then
							player:castSpell("obj", 2, iter_48_8)
						end
					elseif var_48_16.polygon and var_48_16.polygon:Contains(iter_48_8.path.serverPos) ~= 0 and (not var_48_16.data.collision or #var_48_16.data.collision == 0) then
						for iter_48_9, iter_48_10 in pairs(var_0_3) do
							if iter_48_10.charName == var_48_16.owner.charName and var_48_16.data.slot == iter_48_10.slot and var_0_16.SpellsMenu[iter_48_10.charName] and var_0_16.SpellsMenu[iter_48_10.charName][iter_48_10.slot].Dodge:get() and var_0_16.SpellsMenu[iter_48_10.charName][iter_48_10.slot].hp:get() >= iter_48_8.health / iter_48_8.maxHealth * 100 and iter_48_8.pos:dist(player.pos) <= var_0_10.range then
								if var_48_16.missile and iter_48_8.pos:dist(var_48_16.missile.pos) / var_48_16.data.speed < network.latency + 0.35 and iter_48_8.pos:dist(player.pos) <= var_0_10.range then
									player:castSpell("obj", 2, iter_48_8)
								end

								if (var_48_16.data.speed == math.huge or var_48_16.data.spell_type == "Circular") and iter_48_8.pos:dist(player.pos) <= var_0_10.range then
									player:castSpell("obj", 2, iter_48_8)
								end
							end
						end
					end
				end
			end
		end
	end
end

local function var_0_100()
	if player.isDead then
		return
	end

	if var_0_36 and var_0_36.isOnScreen then
		graphics.draw_circle(var_0_36, 55, 2, graphics.argb(255, 52, 152, 219), 40)
	end

	if var_0_16.draws.drawcolors:get() == 1 and os.clock() - var_0_91 > 0.4 then
		var_0_91 = os.clock()
		var_0_90 = graphics.argb(255, math.random(55, 255), math.random(55, 255), math.random(55, 255))
	end

	if var_0_28 ~= vec3(0, 0, 0) and player.isOnScreen then
		if var_0_16.draws.drawcolors:get() == 2 then
			var_0_84 = graphics.argb(255, 255, 255, 255)
			var_0_85 = graphics.argb(255, 255, 255, 255)
			var_0_86 = graphics.argb(255, 46, 190, 244)
			var_0_87 = graphics.argb(255, 46, 190, 244)
		end

		if var_0_16.draws.drawcolors:get() == 1 and os.clock() - var_0_80 > 0.3 then
			var_0_80 = os.clock()
			var_0_84 = graphics.argb(255, math.random(55, 255), math.random(55, 255), math.random(55, 255))
			var_0_85 = var_0_84
			var_0_86 = graphics.argb(255, math.random(55, 255), math.random(55, 255), math.random(55, 255))
			var_0_87 = var_0_86
		end

		if var_0_16.draws.draww:get() then
			graphics.draw_circle(var_0_28, var_0_9.range, 2, graphics.argb(255, 100, 200, 219), 40)
		end

		if var_0_16.draws.drawr:get() then
			graphics.draw_circle(var_0_28, var_0_12.range, 2, graphics.argb(255, 52, 152, 219), 40)
		end

		if var_0_16.draws.drawmode:get() == 1 then
			BallDraw(var_0_28)
		end

		if var_0_16.draws.drawmode:get() == 2 then
			BallDraw2(var_0_28)
			graphics.draw_circle(var_0_28, 90, 5, graphics.argb(200, 52, 152, 219), 80)
		end

		if var_0_16.draws.drawmode:get() == 3 then
			if var_0_16.draws.drawcolors:get() == 2 then
				graphics.draw_circle(var_0_28, 90, 5, graphics.argb(255, 46, 190, 244), 20)
			end

			if var_0_16.draws.drawcolors:get() == 1 then
				graphics.draw_circle(var_0_28, 90, 5, var_0_90, 20)
			end
		end
	end

	local var_52_0 = graphics.world_to_screen(vec3(player.x, player.y, player.z))

	if var_0_56 then
		graphics.draw_circle(var_0_56, 15, 2, graphics.argb(255, 249, 229, 233), 100)
	end

	graphics.draw_text_2D("Auto Harass: ", 15, var_52_0.x - 50, var_52_0.y + 30, graphics.argb(255, 255, 255, 255))

	if var_0_16.harass.autoharas:get() then
		graphics.draw_text_2D("ON", 15, var_52_0.x + 55, var_52_0.y + 30, graphics.argb(255, 128, 255, 0))
	else
		graphics.draw_text_2D("OFF", 15, var_52_0.x + 55, var_52_0.y + 30, graphics.argb(255, 218, 34, 34))
	end

	graphics.draw_text_2D("Farm: ", 15, var_52_0.x - 50, var_52_0.y + 20, graphics.argb(255, 255, 255, 255))

	if var_0_43 == true then
		graphics.draw_text_2D("ON", 15, var_52_0.x - 10, var_52_0.y + 20, graphics.argb(255, 128, 255, 0))
	else
		graphics.draw_text_2D("OFF", 15, var_52_0.x - 10, var_52_0.y + 20, graphics.argb(255, 218, 34, 34))
	end

	if var_0_16.draws.drawmode:get() == 3 then
		if var_0_34 and var_0_62 == nil then
			graphics.draw_circle(vec3(var_0_34.pos.x, var_0_34.pos.y + 110, var_0_34.pos.z), 90, 5, graphics.argb(255, 217, 83, 79), 40)
		end

		if var_0_62 then
			graphics.draw_circle(vec3(var_0_62.pos.x, var_0_62.pos.y, var_0_62.pos.z), 90, 5, graphics.argb(255, 217, 83, 79), 40)
		end

		if var_0_38 and var_0_38.isOnScreen then
			if var_0_16.draws.drawcolors:get() == 2 then
				graphics.draw_circle(vec3(var_0_38.pos.x, var_0_38.pos.y, var_0_38.pos.z), 90, 5, graphics.argb(255, 46, 190, 244), 20)
			end

			if var_0_16.draws.drawcolors:get() == 1 then
				graphics.draw_circle(vec3(var_0_38.pos.x, var_0_38.pos.y, var_0_38.pos.z), 90, 5, var_0_90, 20)
			end
		end
	end

	if var_0_16.draws.drawdamage:get() then
		for iter_52_0 = 0, objManager.enemies_n - 1 do
			local var_52_1 = objManager.enemies[iter_52_0]

			if var_52_1 and var_52_1.isVisible and var_52_1.team == TEAM_ENEMY and var_52_1.isOnScreen then
				local var_52_2 = var_52_1.barPos
				local var_52_3 = var_52_2.x + 164
				local var_52_4 = var_52_2.y + 122.5
				local var_52_5 = player:spellSlot(2).state == 0 and QDamage(var_52_1) or 0
				local var_52_6 = player:spellSlot(0).state == 0 and EDamage(var_52_1) or 0
				local var_52_7 = WDamage(var_52_1)
				local var_52_8 = player:spellSlot(3).state == 0 and RDamage(var_52_1) or 0
				local var_52_9 = var_52_1.health - (var_52_5 + var_52_6 + var_52_8 + var_52_7)
				local var_52_10 = var_52_3 + var_52_1.health / var_52_1.maxHealth * 102
				local var_52_11 = var_52_3 + (var_52_9 > 0 and var_52_9 or 0) / var_52_1.maxHealth * 102

				if var_52_9 > 0 then
					graphics.draw_line_2D(var_52_10, var_52_4, var_52_11, var_52_4, 10, graphics.argb(var_0_16.draws.transparency:get(), 255, 192, 200))
				else
					graphics.draw_line_2D(var_52_10, var_52_4, var_52_11, var_52_4, 10, graphics.argb(var_0_16.draws.transparency:get(), 0, 255, 0))
				end
			end
		end
	end

	if player.isOnScreen then
		if var_0_16.draws.drawq:get() then
			graphics.draw_circle(player.pos, var_0_8.range, 2, graphics.argb(255, 52, 152, 219), 40)
		end

		if var_0_16.draws.drawe:get() then
			graphics.draw_circle(player.pos, var_0_10.range, 2, graphics.argb(255, 52, 152, 219), 40)
		end

		if not var_0_37 and not var_0_34 and not var_0_38 and var_0_67 < os.clock() and var_0_27 and var_0_28 == player.pos then
			if var_0_16.draws.draww:get() then
				graphics.draw_circle(player.pos, var_0_9.range, 2, graphics.argb(255, 100, 200, 219), 40)
			end

			if var_0_16.draws.drawr:get() then
				graphics.draw_circle(player.pos, var_0_12.range, 2, graphics.argb(255, 52, 152, 219), 40)
			end
		end

		if var_0_37 and var_0_37.isOnScreen then
			if var_0_16.draws.draww:get() then
				graphics.draw_circle(var_0_37.pos, var_0_9.range, 2, graphics.argb(255, 100, 200, 219), 40)
			end

			if var_0_16.draws.drawr:get() then
				graphics.draw_circle(var_0_37.pos, var_0_12.range, 2, graphics.argb(255, 52, 152, 219), 40)
			end
		end

		if var_0_34 then
			if var_0_16.draws.draww:get() then
				graphics.draw_circle(vec3(var_0_34.pos.x, var_0_34.pos.y, var_0_34.pos.z), var_0_9.range, 2, graphics.argb(255, 100, 200, 219), 40)
			end

			if var_0_16.draws.drawr:get() then
				graphics.draw_circle(vec3(var_0_34.pos.x, var_0_34.pos.y, var_0_34.pos.z), var_0_12.range, 2, graphics.argb(255, 52, 152, 219), 40)
			end
		end

		if var_0_16.draws.drawmode:get() == 1 then
			if var_0_34 and var_0_62 == nil then
				BallDraw(vec3(var_0_34.pos.x, var_0_34.pos.y + 30, var_0_34.pos.z))
			end

			if var_0_62 then
				BallDraw(vec3(var_0_62.pos.x, var_0_62.pos.y - 30, var_0_62.pos.z))
			end

			if var_0_38 and var_0_38.isOnScreen then
				BallDraw(var_0_38.pos)
			end
		end

		if var_0_16.draws.drawmode:get() == 2 then
			if var_0_34 and var_0_62 == nil then
				BallDraw2(vec3(var_0_34.pos.x, var_0_34.pos.y + 110, var_0_34.pos.z))
				graphics.draw_circle(vec3(var_0_34.pos.x, var_0_34.pos.y + 110, var_0_34.pos.z), 90, 5, graphics.argb(255, 217, 83, 79), 30)
			end

			if var_0_62 then
				BallDraw2(vec3(var_0_62.pos.x, var_0_62.pos.y, var_0_62.pos.z))
				graphics.draw_circle(vec3(var_0_34.pos.x, var_0_34.pos.y, var_0_34.pos.z), 90, 5, graphics.argb(255, 217, 83, 79), 30)
			end

			if var_0_38 and var_0_38.isOnScreen then
				BallDraw2(var_0_38.pos)
				graphics.draw_circle(vec3(var_0_38.pos.x, var_0_38.pos.y, var_0_38.pos.z), 90, 5, graphics.argb(200, 52, 152, 219), 30)
			end
		end
	end
end

cb.add(cb.draw, var_0_100)
cb.add(cb.spell, var_0_69)
var_0_7.combat.register_f_pre_tick(var_0_99)
cb.add(cb.castspell, var_0_54)

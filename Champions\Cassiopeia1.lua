local CassiopeiaPlugin = {}
local hello = 0
local interruptableSpells = {
    ["anivia"] = {
      {menuslot = "R", slot = 3, spellname = "glacialstorm", channelduration = 6}
    },
    ["caitlyn"] = {
      {menuslot = "R", slot = 3, spellname = "caitlynaceinthehole", channelduration = 1}
    },
    ["ezreal"] = {
      {menuslot = "R", slot = 3, spellname = "ezrealtrueshotbarrage", channelduration = 1}
    },
    ["fiddlesticks"] = {
      {menuslot = "W", slot = 1, spellname = "drain", channelduration = 5},
      {menuslot = "R", slot = 3, spellname = "crowstorm", channelduration = 1.5}
    },
    ["gragas"] = {
      {menuslot = "W", slot = 1, spellname = "gragasw", channelduration = 0.75}
    },
    ["janna"] = {
      {menuslot = "R", slot = 3, spellname = "reapthewhirlwind", channelduration = 3}
    },
    ["karthus"] = {
      {menuslot = "R", slot = 3, spellname = "karthusfallenone", channelduration = 3}
    }, --common.IsValidTargetTarget will prevent from casting @ karthus while he's zombie
    ["katarina"] = {
      {menuslot = "R", slot = 3, spellname = "katarinar", channelduration = 2.5}
    },
    ["lucian"] = {
      {menuslot = "R", slot = 3, spellname = "lucianr", channelduration = 2}
    },
    ["lux"] = {
      {menuslot = "R", slot = 3, spellname = "luxmalicecannon", channelduration = 0.5}
    },
    ["malzahar"] = {
      {menuslot = "R", slot = 3, spellname = "malzaharr", channelduration = 2.5}
    },
    ["masteryi"] = {
      {menuslot = "W", slot = 1, spellname = "meditate", channelduration = 4}
    },
    ["missfortune"] = {
      {menuslot = "R", slot = 3, spellname = "missfortunebullettime", channelduration = 3}
    },
    ["nunu"] = {
      {menuslot = "R", slot = 3, spellname = "absolutezero", channelduration = 3}
    },
    --excluding Orn's Forge Channel since it can be cancelled just by attacking him
    ["pantheon"] = {
      {menuslot = "R", slot = 3, spellname = "pantheonrjump", channelduration = 2}
    },
    ["shen"] = {
      {menuslot = "R", slot = 3, spellname = "shenr", channelduration = 3}
    },
    ["twistedfate"] = {
      {menuslot = "R", slot = 3, spellname = "gate", channelduration = 1.5}
    },
    ["varus"] = {
      {menuslot = "Q", slot = 0, spellname = "varusq", channelduration = 4}
    },
    ["warwick"] = {
      {menuslot = "R", slot = 3, spellname = "warwickr", channelduration = 1.5}
    },
    ["xerath"] = {
      {menuslot = "R", slot = 3, spellname = "xerathlocusofpower2", channelduration = 3}
    }
  }
  --local ui = module.load("Brian", "ui");
  local DelayAction = module.load("Brian", "Core/DelayAction")
  local DelayTick = module.load("Brian", "Core/DelayTick")
  local Prediction = module.load("Brian", "Core/Prediction")
  local BuffManager = module.load("Brian", "Library/BuffManager")
  local CalculateManager = module.load("Brian", "Library/CalculateManager")
  local FarmManager = module.load("Brian", "Library/FarmManager")
  local ItemManager = module.load("Brian", "Library/ItemManager")
  local NetManager = module.load("Brian", "Library/NetManager")
  local ObjectManager = module.load("Brian", "Library/ObjectManager")
  local OrbManager = module.load("Brian", "Library/OrbManager")
  local SpellManager = module.load("Brian", "Library/SpellManager")
  local VectorManager = module.load("Brian", "Library/VectorManager")
  local MyCommon = module.load("Brian", "Library/ExtraManager")
   local preds = module.internal("pred")
  local TS = module.internal("TS")
  local orb = module.internal("orb")
  local Curses = module.load("Brian", "Curses");
   local common = module.load("Brian","Utility/common")
   local spellQ = {
    range = 850,
    delay = 0.75,
    speed = math.huge,
    radius = 170,
    boundingRadiusMod = 0
  }
   local spellW = {
    range = 850,
    radius = 150,
    speed = 3000,
    delay = 0.7,
    boundingRadiusMod = 0
  }
   local spellE = {
    range = 750
  }
   local spellR = {
    range = 825,
    delay = 0.5,
    width = 100,
    speed = math.huge,
    boundingRadiusMod = 0
  }
   local FlashSlot = nil
  if player:spellSlot(4).name == "SummonerFlash" then
    FlashSlot = 4
  elseif player:spellSlot(5).name == "SummonerFlash" then
    FlashSlot = 5
  end


   local MyMenu

   function CassiopeiaPlugin.Load(GlobalMenu)
    MyMenu = GlobalMenu
    MyMenu.Key:keybind("rflash", "R-Flash Key", "G", nil)
    MyMenu.Key:keybind("semir", "Semi-R Key", "T", nil)

    MyMenu:menu("combo", "Combo")
    MyMenu.combo:header("12", "Q Settings")
    MyMenu.combo:menu("qset", "Q Settings")
    MyMenu.combo.qset:boolean("qcombo", "Use Q in Combo", true)
    MyMenu.combo.qset:boolean("qpoison", " ^-Only if NOT POISONED", false)
    MyMenu.combo.qset:boolean("autoq", "Auto Q on Dash", true)
    MyMenu.combo.qset:boolean("turret", " ^-Don't Under the Turret", true)

    MyMenu.combo:header("22", "W Settings")
    MyMenu.combo:menu("wset", "W Settings")
    MyMenu.combo.wset:boolean("wcombo", "Use W in Combo", true)
    MyMenu.combo.wset:boolean("startw", "Start Combo with W", true)
    MyMenu.combo.wset:slider("rangew", "W Max Range", 780, 400, 850, 1)

    MyMenu.combo:header("123", "E Settings")
    MyMenu.combo:menu("eset", "E Settings")
    MyMenu.combo.eset:boolean("ecombo", "Use E in Combo", true)
    MyMenu.combo.eset:boolean("epoison", " ^-Only if POISONED", false)

    MyMenu.combo:header("1122", "R Settings")
    MyMenu.combo:menu("rset", "R Settings")
    MyMenu.combo.rset:slider("range", "R Range", 750, 100, 825, 1)
    MyMenu.combo.rset:header("uhh", "-- 1 v 1 Settings --")
    MyMenu.combo.rset:dropdown("rusage", "R Usage", 1, {"At X Health", "Only if Killable", "Never"})
    MyMenu.combo.rset:slider("waster", "Don't waste R if Enemy Health < X", 100, 0, 500, 1)
    MyMenu.combo.rset:slider("hpr", "R if Target has X Health Percent", 60, 0, 100, 1)
    MyMenu.combo.rset:boolean("face", "Use R only if Facing", true)
    MyMenu.combo.rset:header("uhhh", "-- Teamfight Settings --")
    MyMenu.combo.rset:slider("hitr", "Min. Enemies to Hit", 2, 2, 5, 1)
    MyMenu.combo.rset:boolean("facer", " ^-Only count if Facing", true)
    --MyMenu.combo:boolean("rylais", "Rylais Combo ( Starts with E )", false)
    MyMenu.combo:boolean("flashrface", " ^- Only if Facing", true)


    MyMenu:menu("blacklist", "R Blacklist")
    local enemy = common.GetEnemyHeroes()
    for i, allies in ipairs(enemy) do
      MyMenu.blacklist:boolean(allies.charName, "Block: " .. allies.charName, false)
    end

    MyMenu:menu("Combo", "Combo Settings")
    MyMenu.Combo:header("SpellHeader", "Spell Core")
    MyMenu.Combo:boolean("qCombo", "Use Q to Combo", true)
    MyMenu.Combo:boolean("eCombo", "Use E to Combo", true)
    MyMenu.Combo:boolean("epoison", " ^-Only use if POISONED", false)
    MyMenu.Combo:boolean("laste", "Last Hit with E", true)
    MyMenu.Combo:header("ManaHeader", "Mana Manager")
    MyMenu.Combo:slider("mana", "Player ManaPercent >= x%", 60, 1, 100, 1)
    MyMenu.Combo:header("ProHeader", "PRO Mode")
    MyMenu.Combo:boolean("ProAllow", "Enabled PRO Combo Mode", true)
    MyMenu.Combo.ProAllow:set("tooltip", "PRO Mode => Allow Use Spell Combo Enemy on LaneClear Mode")
    MyMenu.Combo:boolean("ProTurret", "Allow Under Turret Combo", false)


    MyMenu:menu("laneclear", "LaneClear")
    MyMenu.laneclear:slider("mana", "Mana Manager", 30, 0, 100, 1)
    MyMenu.laneclear:boolean("useq", "Use Q to Farm", true)
    MyMenu.laneclear:slider("hitq", " ^-If Hits", 2, 0, 6, 1)
    MyMenu.laneclear:boolean("farme", "Use E to Farm", true)
    MyMenu.laneclear:boolean("epoison", " ^-Only if POISONED", true)
    MyMenu.laneclear:boolean("disable", "Disable AA", true)


    --MyMenu.laneclear:menu("passive", "Freeze")
    --MyMenu.laneclearpassive:boolean("farme", "Use E to Last Hit", true)

    MyMenu:menu("jungle", "Jungle Clear")
    MyMenu.jungle:slider("mana", "Mana Manager", 30, 0, 100, 1)
    MyMenu.jungle:boolean("useq", "Use Q in Jungle", true)
    MyMenu.jungle:boolean("usee", "Use E in Jungle", true)

    MyMenu:menu("lasthit", "Last Hit")
    MyMenu.lasthit:boolean("qlasthit", "Use E", true)

    FarmManager.Load(MyMenu)

    MyMenu:menu("killsteal", "Killsteal")
    MyMenu.killsteal:boolean("ksq", "Killsteal with Q", true)
    MyMenu.killsteal:boolean("kse", "Killsteal with E", true)
    MyMenu.killsteal:boolean("ksr", "Killsteal with R", true)
    MyMenu.killsteal:slider("saver", "Don't waste R if Enemy Health < X", 100, 0, 500, 1)

    MyMenu:menu("misc", "Misc.")
    MyMenu.misc:slider("qpred", "Q Radius: ", 170, 130, 200, 1)
    MyMenu.misc.qpred:set(
      "tooltip",
      "Lower - Will try to cast more behind enemy, Higher - Will try to cast more further of target."
    )
    MyMenu.misc:slider("lasthittimer", "Last Hit E Delay", 70, 10, 300, 1)
    MyMenu.misc.lasthittimer:set("tooltip", "Lower - Casts later, Higher - Casts earlier // Default is 70")
    MyMenu.misc:boolean("disable", "Disable Auto Attack", true)
    MyMenu.misc:slider("level", "Disable AA at X Level", 6, 1, 18, 1)
    MyMenu.misc:boolean("GapA", "Use R for Anti-Gapclose", true)
    MyMenu.misc:slider("health", " ^-Only if my Health Percent < X", 50, 1, 100, 1)
    --MyMenu.misc:menu("interrupt", "Interrupt Settings")
    --MyMenu.misc:boolean("inte", "Use R to Interrupt", true)
    --MyMenu.misc:menu("interruptmenu", "Interrupt Settings")
   
    --[[for i = 1, #common.GetEnemyHeroes() do
      local enemy = common.GetEnemyHeroes()[i]
      local name = string.lower(enemy.charName)
      if enemy and interruptableSpells[name] then
        for v = 1, #interruptableSpells[name] do
          local spell = interruptableSpells[name][v]
          MyMenu.misc.interruptmenu:boolean(
            string.format(tostring(enemy.charName) .. tostring(spell.menuslot)),
            "Interrupt " .. tostring(enemy.charName) .. " " .. tostring(spell.menuslot),
            true
          )
        end
      end
    end
    local delay = 0
    local hello = 0
    local mana = 0
    function count_enemies_in_something_range(pos, range)
      local enemies_in_range = {}
      for i = 0, objManager.enemies_n - 1 do
        local enemy = objManager.enemies[i]
        if
          pos:dist(enemy.pos) < range and common.IsValidTarget(enemy) and player.pos:dist(enemy.pos) < spellR.range and
            player.pos:dist(enemy.pos) > 370
        then
          enemies_in_range[#enemies_in_range + 1] = enemy
        end
      end
      return enemies_in_range
    end]]
    -- Thanks to Avada's Cassiopeia. <3
    MyMenu:menu("draws", "Draw Settings")
    MyMenu.draws:boolean("drawq", "Draw Q Range", true)
    MyMenu.draws:color("colorq", "  ^- Color", 255, 233, 121, 121)
    MyMenu.draws:boolean("draww", "Draw W Range", false)
    MyMenu.draws:color("colorw", "  ^- Color", 255, 233, 121, 121)
    MyMenu.draws:boolean("drawwmin", "Draw Min. W Range", false)
    MyMenu.draws:color("colorwmin", "  ^- Color", 255, 233, 121, 121)
    MyMenu.draws:boolean("drawe", "Draw E Range", true)
    MyMenu.draws:color("colore", "  ^- Color", 255, 233, 121, 121)
    MyMenu.draws:boolean("drawr", "Draw R Range", false)
    MyMenu.draws:color("colorr", "  ^- Color", 255, 233, 121, 121)
    MyMenu.draws:boolean("drawrf", "Draw R-Flash Range", false)
    MyMenu.draws:color("colorrf", "  ^- Color", 255, 233, 121, 121)
    --MyMenu.draws:boolean("drawtoggle", "Draw Farm Toggle", true)
    
    MyMenu.draws:header("DamageHeader", "Damage Indicator")
    MyMenu.draws:boolean("drawkill", "Draw Killable Minions with E", true)
    MyMenu.draws:boolean("DIEnabled", "Enabled", true)


  end
  TS.load_to_menu(MyMenu)
   local TargetSelection = function(res, obj, dist)
    if dist < spellQ.range then
      res.obj = obj
      return true
    end
  end
  local GetTarget = function()
    return TS.get_result(TargetSelection).obj
  end
  function count_enemies_in_range(pos, range)
    local enemies_in_range = {}
    for i = 0, objManager.enemies_n - 1 do
      local enemy = objManager.enemies[i]
      if pos:dist(enemy.pos) < range and common.IsValidTarget(enemy) then
        enemies_in_range[#enemies_in_range + 1] = enemy
      end
    end
    return enemies_in_range
  end
   local uhh = false
  local something = 0
   --[[local function Toggle()
    if MyMenu.lanecleartoggle:get() then
      if (uhh == false and os.clock() > something) then
        uhh = true
        something = os.clock() + 0.3
      end
      if (uhh == true and os.clock() > something) then
        uhh = false
        something = os.clock() + 0.3
      end
    end
  end]]
  local function IsFacing(target)
    return player.path.serverPos:distSqr(target.path.serverPos) >
      player.path.serverPos:distSqr(target.path.serverPos + target.direction)
  end
  local aaaaaaaaaasdfsaf = 0
  function is_turret_near(position)
    local hewwo = false
    if aaaaaaaaaasdfsaf < os.clock() then
      aaaaaaaaaasdfsaf = os.clock() + 0.1
      objManager.loop(
        function(obj)
          if obj and obj.pos:dist(position) < 900 and obj.team == TEAM_ENEMY and obj.type == TYPE_TURRET then
            hewwo = true
          end
        end
      )
      return hewwo
    end
  end



local function WDamage(target)
    if not target or target == nil then
        return 0
    end
    local level = player:spellSlot(1).level
    if level == 0 then
        return 0
    end
    local dmg = ({75, 125, 175, 225, 275})[level] + (1.5 * MyCommon.GetTotalAP())
    return CalculateManager.CalculateMagicDamage(target, dmg)
end



  local ElvlDmgBonus = {10, 30, 50, 70, 90}
  local ElvlDamage = 4
  function EDamage(target)
    local damage = 0
    if player:spellSlot(2).level > 0 then
      if
        (common.CheckBuff(target, "poisontrailtarget") or common.CheckBuff(target, "TwitchDeadlyVenom") or
          common.CheckBuff(target, "cassiopeiawpoison") or
          common.CheckBuff(target, "cassiopeiaqdebuff") or
          common.CheckBuff(target, "ToxicShotParticle") or
          common.CheckBuff(target, "bantamtraptarget"))
       then
        damage =
          CalcMagicDmg(
          target,
          (((52 + ElvlDamage * (player.levelRef - 1)) + (common.GetTotalAP() * .1)) + ElvlDmgBonus[player:spellSlot(2).level] +
            (common.GetTotalAP() * .6))
        )
      else
        damage = CalcMagicDmg(target, ((52 + ElvlDamage * (player.levelRef - 1)) + (common.GetTotalAP() * .1)))
      end
    end
    return damage - 5
  end


  
  function QDamage(target)
    local QLevelDamage = {75, 125, 175, 225, 275}
    local damage = 0
    if player:spellSlot(0).level > 0 then

         damage = common.CalculateMagicDamage(target, (QLevelDamage[player:spellSlot(0).level] + (common.GetTotalAP() * .8)), player)   
    end
    return damage
  end



  local RLevelDamage = {150, 250, 350}
  function RDamage(target)
    local damage = 0
    if player:spellSlot(3).level > 0 then
      damage =
        common.CalculateMagicDamage(target, (RLevelDamage[player:spellSlot(3).level] + (common.GetTotalAP() * .5)), player)
    end
    return damage
  end
  function CalcMagicDmg(target, amount, from)
    local from = from or player
    local target = target or orb.combat.target
    local amount = amount or 0
    local targetMR = target.spellBlock * math.ceil(from.percentMagicPenetration) - from.flatMagicPenetration
    local dmgMul = 100 / (100 + targetMR)
    if dmgMul < 0 then
      dmgMul = 2 - (100 / (100 - magicResist))
    end
    amount = amount * dmgMul
    return math.floor(amount)
  end
   local function AutoDash()
    local target =
      TS.get_result(
      function(res, obj, dist)
        if dist <= spellQ.range and obj.path.isActive and obj.path.isDashing then --add invulnverabilty check
          res.obj = obj
          return true
        end
      end
    ).obj
    if target then
      local pred_pos = preds.core.lerp(target.path, network.latency + spellQ.delay, target.path.dashSpeed)
      if pred_pos and pred_pos:dist(player.path.serverPos2D) <= spellQ.range then
        if MyMenu.combo.qset.turret:get() then
          if is_turret_near(vec3(pred_pos.x, target.y, pred_pos.y)) == false then
            player:castSpell("pos", 0, vec3(pred_pos.x, target.y, pred_pos.y))
          end
        else
          --orb.core.set_server_pause()
          player:castSpell("pos", 0, vec3(pred_pos.x, target.y, pred_pos.y))
        end
      end
    end
  end
   local function WGapcloser()
    if player:spellSlot(3).state == 0 and MyMenu.misc.GapA:get() then
      for i = 0, objManager.enemies_n - 1 do
        local dasher = objManager.enemies[i]
        if dasher.type == TYPE_HERO and dasher.team == TEAM_ENEMY then
          if
            dasher and common.IsValidTarget(dasher) and dasher.path.isActive and dasher.path.isDashing and
              player.pos:dist(dasher.path.point[1]) < 850
           then
            if player.pos2D:dist(dasher.path.point2D[1]) < player.pos2D:dist(dasher.path.point2D[0]) then
              if ((player.health / player.maxHealth) * 100 <= MyMenu.misc.health:get()) then
                --player:castSpell("pos", 3, dasher.path.point2D[1])
              end
            end
          end
        end
      end
    end
  end
   local TargetSelectionFR = function(res, obj, dist)
    if dist < spellR.range + 410 then
      res.obj = obj
      return true
    end
  end
  local GetTargetFR = function()
    return TS.get_result(TargetSelectionFR).obj
  end
  local function FlashR()
    if MyMenu.Key.rflash:get() then
      player:move(vec3(mousePos.x, mousePos.y, mousePos.z))
      local target = GetTargetFR()
      if target and target.isVisible then
        if common.IsValidTarget(target) then
          if (target.pos:dist(player.pos) <= spellR.range + 410) then
            if (FlashSlot and player:spellSlot(FlashSlot).state) then
              if (target.pos:dist(player.pos) > spellR.range) then
                if (MyMenu.combo.flashrface:get()) and IsFacing(target) then
                  local pos = preds.linear.get_prediction(spellR, target)
                  if pos and pos.startPos:dist(pos.endPos) < spellR.range + 410 then
                    player:castSpell("pos", 3, vec3(pos.endPos.x, mousePos.y, pos.endPos.y))
                    common.DelayAction(
                      function()
                        player:castSpell("pos", FlashSlot, target.pos)
                      end,
                      0.25 + network.latency
                    )
                  end
                end
                if not (MyMenu.combo.flashrface:get()) then
                  local pos = preds.linear.get_prediction(spellR, target)
                  if pos and pos.startPos:dist(pos.endPos) < spellR.range + 410 then
                    player:castSpell("pos", 3, vec3(pos.endPos.x, mousePos.y, pos.endPos.y))
                    common.DelayAction(
                      function()
                        player:castSpell("pos", FlashSlot, target.pos)
                      end,
                      0.25 + network.latency
                    )
                  end
                end
              end
            end
          end
        end
      end
    end
  end
  function KillSteal()
    local enemy = common.GetEnemyHeroes()
    for i, enemies in ipairs(enemy) do
      if enemies and enemies.isVisible and common.IsValidTarget(enemies) and not common.CheckBuffType(enemies, 17) then
        local hp = common.GetShieldedHealth("ap", enemies)
        if MyMenu.killsteal.ksq:get() then
          if
            player:spellSlot(0).state == 0 and vec3(enemies.x, enemies.y, enemies.z):dist(player) < spellQ.range and
              QDamage(enemies) > hp
           then
            local pos = preds.circular.get_prediction(spellQ, enemies)
            if pos and pos.startPos:dist(pos.endPos) < spellQ.range then
              player:castSpell("pos", 0, vec3(pos.endPos.x, mousePos.y, pos.endPos.y))
            end
          end
        end
        if MyMenu.killsteal.kse:get() then
          if
            player:spellSlot(2).state == 0 and vec3(enemies.x, enemies.y, enemies.z):dist(player) < spellE.range and
              EDamage(enemies) > hp
           then
            player:castSpell("obj", 2, enemies)
          end
        end
        if MyMenu.killsteal.ksr:get() then
          if
            player:spellSlot(3).state == 0 and vec3(enemies.x, enemies.y, enemies.z):dist(player) < spellR.range and
              hp < RDamage(enemies) and
              hp > MyMenu.killsteal.saver:get()
           then
            local pos = preds.linear.get_prediction(spellR, enemies)
            if pos and pos.startPos:dist(pos.endPos) < spellR.range then
              player:castSpell("pos", 3, vec3(pos.endPos.x, mousePos.y, pos.endPos.y))
            end
          end
        end
      end
    end
  end
   local function LastHit()
    if MyMenu.lasthit.qlasthit:get() then
      for i = 0, objManager.minions.size[TEAM_ENEMY] - 1 do
        local minion = objManager.minions[TEAM_ENEMY][i]
        if
          minion and minion.isVisible and minion.moveSpeed > 0 and minion.isTargetable and not minion.isDead and
            minion.pos:dist(player.pos) < spellE.range
         then
          local minionPos = vec3(minion.x, minion.y, minion.z)
          --delay = player.pos:dist(minion.pos) / 3500 + 0.2
          delay = MyMenu.misc.lasthittimer:get() / 1000 + player.pos:dist(minion.pos) / 840
          if (EDamage(minion) >= orb.farm.predict_hp(minion, delay / 2, true) - 150 and mana > player.manaCost2) then
            orb.core.set_pause_attack(1)
          end
          if (EDamage(minion) >= orb.farm.predict_hp(minion, delay / 2, true)) then
            player:castSpell("obj", 2, minion)
          end
        end
      end
    end
  end
  function JungleClear()
    if (player.mana / player.maxMana) * 100 >= MyMenu.jungle.mana:get() then
      if MyMenu.jungle.useq:get() then
        for i = 0, objManager.minions.size[TEAM_NEUTRAL] - 1 do
          local minion = objManager.minions[TEAM_NEUTRAL][i]
          if
            minion and not minion.isDead and minion.moveSpeed > 0 and minion.isTargetable and minion.isVisible and
              minion.type == TYPE_MINION
           then
            if minion.pos:dist(player.pos) <= spellQ.range then
              local pos = preds.circular.get_prediction(spellQ, minion)
              if pos and pos.startPos:dist(pos.endPos) < spellQ.range then
                player:castSpell("pos", 0, vec3(pos.endPos.x, mousePos.y, pos.endPos.y))
              end
            end
          end
        end
      end
      if MyMenu.jungle.usee:get() then
        for i = 0, objManager.minions.size[TEAM_NEUTRAL] - 1 do
          local minion = objManager.minions[TEAM_NEUTRAL][i]
          if
            minion and not minion.isDead and minion.moveSpeed > 0 and minion.isTargetable and minion.isVisible and
              minion.type == TYPE_MINION
           then
            if minion.pos:dist(player.pos) <= spellE.range then
              player:castSpell("obj", 2, minion)
            end
          end
        end
      end
    end
  end
  local function Combo()
    if not MyMenu.Combo.ProTurret:get() and ObjectManager.IsUnderEnemyTurret(player) then
      return
  end
    if MyMenu.Combo.laste:get() then
      for i = 0, objManager.minions.size[TEAM_ENEMY] - 1 do
        local minion = objManager.minions[TEAM_ENEMY][i]
        if minion and minion.isVisible and not minion.isDead and minion.pos:dist(player.pos) < spellE.range then
          local minionPos = vec3(minion.x, minion.y, minion.z)
          --delay = player.pos:dist(minion.pos) / 3500 + 0.2
          delay = MyMenu.misc.lasthittimer:get() / 1000 + player.pos:dist(minion.pos) / 840
          if (EDamage(minion) >= orb.farm.predict_hp(minion, delay / 2, true) - 150 and mana > player.manaCost2) then
            orb.core.set_pause_attack(1)
          end
          if (EDamage(minion) >= orb.farm.predict_hp(minion, delay / 2, true)) then
            player:castSpell("obj", 2, minion)
          end
        end
      end
    end
    if (player.mana / player.maxMana) * 100 >= MyMenu.Combo.mana:get() then
      local target = GetTarget()
      if not common.IsValidTarget(target) then
        return
      end
      if target and target.isVisible then
        if MyMenu.Combo.qCombo:get() then
          if (target.pos:dist(player) < spellQ.range) then
            local pos = preds.circular.get_prediction(spellQ, target)
            if pos and pos.startPos:dist(pos.endPos) < spellQ.range then
              player:castSpell("pos", 0, vec3(pos.endPos.x, mousePos.y, pos.endPos.y))
            end
          end
        end
        if MyMenu.Combo.eCombo:get() then
          if (target.pos:dist(player) < spellE.range) then
            if MyMenu.Combo.epoison:get() then
              if
                (common.CheckBuff(target, "poisontrailtarget") or common.CheckBuff(target, "TwitchDeadlyVenom") or
                  common.CheckBuff(target, "cassiopeiawpoison") or
                  common.CheckBuff(target, "cassiopeiaqdebuff") or
                  common.CheckBuff(target, "ToxicShotParticle") or
                  common.CheckBuff(target, "bantamtraptarget"))
               then
                player:castSpell("obj", 2, target)
              end
            end
            if not MyMenu.Combo.epoison:get() then
              player:castSpell("obj", 2, target)
            end
          end
        end
      end
    end
  end
  local function count_minions_in_range(pos, range)
    local enemies_in_range = {}
    for i = 0, objManager.minions.size[TEAM_ENEMY] - 1 do
      local enemy = objManager.minions[TEAM_ENEMY][i]
      if pos:dist(enemy.pos) < range and common.IsValidTarget(enemy) then
        enemies_in_range[#enemies_in_range + 1] = enemy
      end
    end
    return enemies_in_range
  end
   local function LaneClear()
    --[[if uhh == false then
      if MyMenu.laneclearpassive.farme:get() then
        for i = 0, objManager.minions.size[TEAM_ENEMY] - 1 do
          local minion = objManager.minions[TEAM_ENEMY][i]
          if
            minion and minion.isVisible and minion.moveSpeed > 0 and minion.isTargetable and not minion.isDead and
              minion.pos:dist(player.pos) < spellE.range
           then
            local minionPos = vec3(minion.x, minion.y, minion.z)
            --delay = player.pos:dist(minion.pos) / 3500 + 0.2
            delay = MyMenu.misc.lasthittimer:get() / 1000 + player.pos:dist(minion.pos) / 840
            if (EDamage(minion) >= orb.farm.predict_hp(minion, delay / 2, true) - 150 and mana > player.manaCost2) then
              orb.core.set_pause_attack(1)
            end
            if (EDamage(minion) >= orb.farm.predict_hp(minion, delay / 2, true)) then
              player:castSpell("obj", 2, minion)
            end
          end
        end
      end
    end]]
    --if uhh == true then
      if (player.mana / player.maxMana) * 100 >= MyMenu.laneclear.mana:get() then
        -- Thanks to Avada's Cassiopeia. <3
        if MyMenu.laneclear.useq:get() then
          local minions = objManager.minions
          for a = 0, minions.size[TEAM_ENEMY] - 1 do
            local minion1 = minions[TEAM_ENEMY][a]
            if
              minion1 and minion1.moveSpeed > 0 and minion1.isTargetable and not minion1.isDead and minion1.isVisible and
                player.path.serverPos:distSqr(minion1.path.serverPos) <= (spellQ.range * spellQ.range)
             then
              local count = 0
              for b = 0, minions.size[TEAM_ENEMY] - 1 do
                local minion2 = minions[TEAM_ENEMY][b]
                if
                  minion2 and minion2.moveSpeed > 0 and minion2.isTargetable and minion2 ~= minion1 and not minion2.isDead and
                    minion2.isVisible and
                    minion2.path.serverPos:distSqr(minion1.path.serverPos) <= (spellQ.radius * spellQ.radius)
                 then
                  count = count + 1
                end
                if count >= MyMenu.laneclear.hitq:get() then
                  local seg = preds.circular.get_prediction(spellQ, minion1)
                  if seg and seg.startPos:dist(seg.endPos) < spellQ.range then
                    player:castSpell("pos", 0, vec3(seg.endPos.x, minion1.y, seg.endPos.y))
                    --orb.core.set_server_pause()
                    break
                  end
                end
              end
            end
          end
          for i = 0, objManager.minions.size[TEAM_ENEMY] - 1 do
            local minion = objManager.minions[TEAM_ENEMY][i]
            if
              minion and minion.moveSpeed > 0 and minion.isTargetable and minion.pos:dist(player.pos) <= spellQ.range and
                minion.path.count == 0 and
                not minion.isDead and
                common.IsValidTarget(minion)
             then
              local minionPos = vec3(minion.x, minion.y, minion.z)
              if minionPos then
                if #count_minions_in_range(minionPos, 150) >= MyMenu.laneclear.hitq:get() then
                  local seg = preds.circular.get_prediction(spellQ, minion)
                  if seg and seg.startPos:dist(seg.endPos) < spellQ.range then
                    player:castSpell("pos", 0, vec3(seg.endPos.x, minionPos.y, seg.endPos.y))
                  end
                end
              end
            end
          end
        end
        if MyMenu.laneclear.farme:get() then
          for i = 0, objManager.minions.size[TEAM_ENEMY] - 1 do
            local minion = objManager.minions[TEAM_ENEMY][i]
            if
              minion and minion.isVisible and minion.moveSpeed > 0 and minion.isTargetable and not minion.isDead and
                minion.pos:dist(player.pos) < spellE.range
             then
              local minionPos = vec3(minion.x, minion.y, minion.z)
              --delay = player.pos:dist(minion.pos) / 3500 + 0.2
              delay = MyMenu.misc.lasthittimer:get() / 1000 + player.pos:dist(minion.pos) / 840
              if (EDamage(minion) >= orb.farm.predict_hp(minion, delay / 2, true) - 150 and mana > player.manaCost2) then
                orb.core.set_pause_attack(1)
              end
              if (EDamage(minion) >= orb.farm.predict_hp(minion, delay / 2, true)) then
                player:castSpell("obj", 2, minion)
              end
            end
          end
          for i = 0, objManager.minions.size[TEAM_ENEMY] - 1 do
            local minion = objManager.minions[TEAM_ENEMY][i]
            if minion and minion.moveSpeed > 0 and minion.isTargetable and not minion.isDead and common.IsValidTarget(minion) then
              local minionPos = vec3(minion.x, minion.y, minion.z)
              if minionPos:dist(player.pos) <= spellE.range then
                if (MyMenu.laneclear.epoison:get()) then
                  if
                    (common.CheckBuff(minion, "poisontrailtarget") or common.CheckBuff(minion, "TwitchDeadlyVenom") or
                      common.CheckBuff(minion, "cassiopeiawpoison") or
                      common.CheckBuff(minion, "cassiopeiaqdebuff") or
                      common.CheckBuff(minion, "ToxicShotParticle") or
                      common.CheckBuff(minion, "bantamtraptarget"))
                   then
                    player:castSpell("obj", 2, minion)
                  end
                end
              end
            end
          end
          for i = 0, objManager.minions.size[TEAM_ENEMY] - 1 do
            local minion = objManager.minions[TEAM_ENEMY][i]
            if
              minion and minion.moveSpeed > 0 and minion.isTargetable and
                (common.CheckBuff(minion, "poisontrailtarget") or common.CheckBuff(minion, "TwitchDeadlyVenom") or
                  common.CheckBuff(minion, "cassiopeiawpoison") or
                  common.CheckBuff(minion, "cassiopeiaqdebuff") or
                  common.CheckBuff(minion, "ToxicShotParticle") or
                  common.CheckBuff(minion, "bantamtraptarget")) and
                not minion.isDead and
                common.IsValidTarget(minion)
             then
              local minionPos = vec3(minion.x, minion.y, minion.z)
              if minionPos:dist(player.pos) <= spellE.range then
                if not MyMenu.laneclear.epoison:get() then
                  player:castSpell("obj", 2, minion)
                end
              end
            end
          end
          for i = 0, objManager.minions.size[TEAM_ENEMY] - 1 do
            local minion = objManager.minions[TEAM_ENEMY][i]
            if minion and minion.moveSpeed > 0 and minion.isTargetable and not minion.isDead and common.IsValidTarget(minion) then
              local minionPos = vec3(minion.x, minion.y, minion.z)
              if minionPos:dist(player.pos) <= spellE.range then
                if not MyMenu.laneclear.epoison:get() then
                  player:castSpell("obj", 2, minion)
                end
              end
            end
          end
        end
      end
    
  end
   local GetNumberOfHits = function(res, obj, dist)
    if dist > spellR.range then
      return
    end
    local target = GetTarget()
    local aaa = preds.linear.get_prediction(spellR, obj)
    if MyMenu.combo.rset.facer:get() then
      if
        obj and IsFacing(obj) and target and target.pos:dist(obj.pos) < 350 and
          obj.pos:dist(vec3(aaa.endPos.x, mousePos.y, aaa.endPos.y)) < 350 and
          obj.pos:dist(player.pos) > 350
       then
        res.num_hits = res.num_hits and res.num_hits + 1 or 1
      end
    end
    if not MyMenu.combo.rset.facer:get() then
      if
        obj and target and target.pos:dist(obj.pos) < 350 and
          obj.pos:dist(vec3(aaa.endPos.x, mousePos.y, aaa.endPos.y)) < 350 and
          obj.pos:dist(player.pos) > 350
       then
        res.num_hits = res.num_hits and res.num_hits + 1 or 1
      end
    end
  end
   local GetPred = function()
    local res = TS.loop(GetNumberOfHits)
    if res.num_hits and res.num_hits > 1 then
      return res.num_hits
    end
  end
   local function Combo()
    local mode = MyMenu.combo.rset.rusage:get()
    local target = GetTarget()
    if not common.IsValidTarget(target) then
      return
    end
    if target and target.isVisible then
      if mode == 1 then
        if
          target and (target.health / target.maxHealth) * 100 <= MyMenu.combo.rset.hpr:get() and
            target.health >= MyMenu.combo.rset.waster:get()
         then
          if MyMenu.blacklist[target.charName] and not MyMenu.blacklist[target.charName]:get() then
            local pos = preds.linear.get_prediction(spellR, target)
            if pos and pos.startPos:dist(pos.endPos) < spellR.range and #count_enemies_in_range(player.pos, 900) == 1 then
              if (MyMenu.combo.rset.face:get()) then
                if (IsFacing(target)) then
                  player:castSpell("pos", 3, vec3(pos.endPos.x, mousePos.y, pos.endPos.y))
                end
              else
                player:castSpell("pos", 3, vec3(pos.endPos.x, mousePos.y, pos.endPos.y))
              end
            end
          end
        end
      end
      if mode == 2 then
        if
          target and target.health < EDamage(target) * 3 + RDamage(target) + QDamage(target) and
            target.health >= MyMenu.combo.rset.waster:get()
         then
          if MyMenu.blacklist[target.charName] and not MyMenu.blacklist[target.charName]:get() then
            local pos = preds.linear.get_prediction(spellR, target)
            if pos and pos.startPos:dist(pos.endPos) < spellR.range and #count_enemies_in_range(player.pos, 900) == 1 then
              if (MyMenu.combo.rset.face:get()) then
                if (IsFacing(target)) then
                  player:castSpell("pos", 3, vec3(pos.endPos.x, mousePos.y, pos.endPos.y))
                end
              else
                player:castSpell("pos", 3, vec3(pos.endPos.x, mousePos.y, pos.endPos.y))
              end
            end
          end
        end
      end
      if mode == 2 or mode == 1 then
        if GetPred() and GetPred() >= MyMenu.combo.rset.hitr:get() then
          local pos = preds.linear.get_prediction(spellR, target)
          if pos and pos.startPos:dist(pos.endPos) < spellR.range then
            player:castSpell("pos", 3, vec3(pos.endPos.x, mousePos.y, pos.endPos.y))
          end
        end
      end
      if not MyMenu.combo.wset.startw:get() and not MyMenu.combo.rylais:get() then
        if MyMenu.combo.qset.qcombo:get() then
          if (target.pos:dist(player) < spellQ.range) then
            local pos = preds.circular.get_prediction(spellQ, target)
            if pos and pos.startPos:dist(pos.endPos) < spellQ.range then
              if not MyMenu.combo.qset.qpoison:get() then
                player:castSpell("pos", 0, vec3(pos.endPos.x, mousePos.y, pos.endPos.y))
              end
              if MyMenu.combo.qset.qpoison:get() then
                if
                  (common.CheckBuff(target, "poisontrailtarget") or common.CheckBuff(target, "TwitchDeadlyVenom") or
                    common.CheckBuff(target, "cassiopeiawpoison") or
                    common.CheckBuff(target, "cassiopeiaqdebuff") or
                    common.CheckBuff(target, "ToxicShotParticle") or
                    common.CheckBuff(target, "bantamtraptarget"))
                 then
                  player:castSpell("pos", 0, vec3(pos.endPos.x, mousePos.y, pos.endPos.y))
                end
              end
            end
          end
        end
        if MyMenu.combo.eset.ecombo:get() then
          if (MyMenu.combo.eset.epoison:get()) then
            if
              (common.CheckBuff(target, "poisontrailtarget") or common.CheckBuff(target, "TwitchDeadlyVenom") or
                common.CheckBuff(target, "cassiopeiawpoison") or
                common.CheckBuff(target, "cassiopeiaqdebuff") or
                common.CheckBuff(target, "ToxicShotParticle") or
                common.CheckBuff(target, "bantamtraptarget"))
             then
              player:castSpell("obj", 2, target)
            end
          else
            player:castSpell("obj", 2, target)
          end
        end
        if MyMenu.combo.wset.wcombo:get() then
          local pos = preds.circular.get_prediction(spellW, target)
          if pos and pos.startPos:dist(pos.endPos) < spellW.range and player.pos:dist(target.pos) then
            player:castSpell("pos", 1, vec3(pos.endPos.x, mousePos.y, pos.endPos.y))
          end
        end
      end
      if MyMenu.combo.wset.startw:get() then
        if MyMenu.combo.wset.wcombo:get() then
          local pos = preds.circular.get_prediction(spellW, target)
          if pos and pos.startPos:dist(pos.endPos) < spellW.range and player.pos:dist(target.pos) then
            player:castSpell("pos", 1, vec3(pos.endPos.x, mousePos.y, pos.endPos.y))
          end
        end
        if (os.clock() > hello) then
          if MyMenu.combo.qset.qcombo:get() then
            if (target.pos:dist(player) < spellQ.range) then
              local pos = preds.circular.get_prediction(spellQ, target)
              if pos and pos.startPos:dist(pos.endPos) < spellQ.range then
                if not MyMenu.combo.qset.qpoison:get() then
                  player:castSpell("pos", 0, vec3(pos.endPos.x, mousePos.y, pos.endPos.y))
                end
                if MyMenu.combo.qset.qpoison:get() then
                  if
                    not common.CheckBuff(target, "poisontrailtarget") and not common.CheckBuff(target, "TwitchDeadlyVenom") and
                      not common.CheckBuff(target, "cassiopeiawpoison") and
                      not common.CheckBuff(target, "cassiopeiaqdebuff") and
                      not common.CheckBuff(target, "ToxicShotParticle") and
                      not common.CheckBuff(target, "bantamtraptarget")
                   then
                    player:castSpell("pos", 0, vec3(pos.endPos.x, mousePos.y, pos.endPos.y))
                  end
                end
              end
            end
          end
        end
        if MyMenu.combo.eset.ecombo:get() then
          if (MyMenu.combo.eset.epoison:get()) then
            if
              (common.CheckBuff(target, "poisontrailtarget") or common.CheckBuff(target, "TwitchDeadlyVenom") or
                common.CheckBuff(target, "cassiopeiawpoison") or
                common.CheckBuff(target, "cassiopeiaqdebuff") or
                common.CheckBuff(target, "ToxicShotParticle") or
                common.CheckBuff(target, "bantamtraptarget"))
             then
              player:castSpell("obj", 2, target)
            end
          else
            player:castSpell("obj", 2, target)
          end
        end
      end
      --[[if MyMenu.combo.rylais:get() then
        if MyMenu.combo.eset.ecombo:get() then
          if (MyMenu.combo.eset.epoison:get()) then
            if
              (common.CheckBuff(target, "poisontrailtarget") or common.CheckBuff(target, "TwitchDeadlyVenom") or
                common.CheckBuff(target, "cassiopeiawpoison") or
                common.CheckBuff(target, "cassiopeiaqdebuff") or
                common.CheckBuff(target, "ToxicShotParticle") or
                common.CheckBuff(target, "bantamtraptarget"))
             then
              player:castSpell("obj", 2, target)
            end
          else
            player:castSpell("obj", 2, target)
          end
        end
        if MyMenu.combo.qset.qcombo:get() then
          if (target.pos:dist(player) < spellQ.range) then
            local pos = preds.circular.get_prediction(spellQ, target)
            if pos and pos.startPos:dist(pos.endPos) < spellQ.range then
              if not MyMenu.combo.qset.qpoison:get() then
                player:castSpell("pos", 0, vec3(pos.endPos.x, mousePos.y, pos.endPos.y))
              end
              if MyMenu.combo.qset.qpoison:get() then
                if
                  not common.CheckBuff(target, "poisontrailtarget") and not common.CheckBuff(target, "TwitchDeadlyVenom") and
                    not common.CheckBuff(target, "cassiopeiawpoison") and
                    not common.CheckBuff(target, "cassiopeiaqdebuff") and
                    not common.CheckBuff(target, "ToxicShotParticle") and
                    not common.CheckBuff(target, "bantamtraptarget")
                 then
                  player:castSpell("pos", 0, vec3(pos.endPos.x, mousePos.y, pos.endPos.y))
                end
              end
            end
          end
        end
        if MyMenu.combo.wset.wcombo:get() then
          local pos = preds.circular.get_prediction(spellW, target)
          if pos and pos.startPos:dist(pos.endPos) < spellW.range and player.pos:dist(target.pos) >= 500 then
            player:castSpell("pos", 1, vec3(pos.endPos.x, mousePos.y, pos.endPos.y))
          end
        end
      end]]
    end
  end
  local function OnTick()
    -- Potato Api Stuff
    if player.mana > player.maxMana then
      mana = player.maxMana
    else
      mana = player.mana
    end
    spellQ.radius = MyMenu.misc.qpred:get()
    if MyMenu.Key.semir:get() then
      local target = GetTarget()
      if common.IsValidTarget(target) then
        if target and target.isVisible then
          local pos = preds.linear.get_prediction(spellR, target)
          if pos and pos.startPos:dist(pos.endPos) < spellR.range then
            player:castSpell("pos", 3, vec3(pos.endPos.x, mousePos.y, pos.endPos.y))
          end
        end
      end
    end
    FlashR()
    --Toggle()
    KillSteal()
   
    if MyMenu.misc.GapA:get() then
      WGapcloser()
    end
    if MyMenu.combo.qset.autoq:get() then
      AutoDash()
    end
    if MyMenu.Key.LastHit:get() then
      LastHit()
    end
    if MyMenu.Key.Combo:get() then
      Combo()
    end
    if DelayTick.CanTickEvent() then
      if MyMenu.Key.LaneClear:get() then
          if MyMenu.Combo.ProAllow:get() then
              Combo()
          end
          if FarmManager.Enabled then
            LaneClear()
            JungleClear()
          end
      end
    end
    if MyMenu.Key.Combo:get() then
      Combo()
    end
  end
   --[[local function AutoInterrupt(spell)
    if orb.combat.is_active and MyMenu.combo.wset.startw:get() then
      if
        spell.owner.type == TYPE_HERO and spell.owner.team == TEAM_ALLY and spell.owner.charName == "Cassiopeia" and
          spell.name == "CassiopeiaW"
       then
        hello = os.clock() + 0.3
      end
      if
        spell.owner.type == TYPE_HERO and spell.owner.team == TEAM_ALLY and spell.owner.charName == "Cassiopeia" and
          spell.name == "CassiopeiaQ"
       then
        hello = 0
      end
    end
    if MyMenu.misc.inte:get() and player:spellSlot(3).state == 0 then
      if spell.owner.type == TYPE_HERO and spell.owner.team == TEAM_ENEMY then
        local enemyName = string.lower(spell.owner.charName)
        if interruptableSpells[enemyName] then
          for i = 1, #interruptableSpells[enemyName] do
            local spellCheck = interruptableSpells[enemyName][i]
            if
              MyMenu.misc.interruptmenu[spell.owner.charName .. spellCheck.menuslot]:get() and
                string.lower(spell.name) == spellCheck.spellname
             then
              if
                player.pos2D:dist(spell.owner.pos2D) < spellR.range and common.IsValidTarget(spell.owner) and
                  player:spellSlot(3).state == 0
               then
                player:castSpell("obj", 3, spell.owner)
              end
            end
          end
        end
      end
    end
  end]]
   orb.combat.register_f_pre_tick(OnTick)
   -- Credits to Avada's Kalista. <3
  function DrawDamagesE(target)
    if target.isVisible and not target.isDead then
      local pos = graphics.world_to_screen(target.pos)
      if (math.floor((EDamage(target) * 3 + QDamage(target) + RDamage(target)) / target.health * 100) < 100) then
        graphics.draw_line_2D(pos.x, pos.y - 30, pos.x + 30, pos.y - 80, 1, graphics.argb(255, 255, 153, 51))
        graphics.draw_line_2D(pos.x + 30, pos.y - 80, pos.x + 50, pos.y - 80, 1, graphics.argb(255, 255, 153, 51))
        graphics.draw_line_2D(pos.x + 50, pos.y - 85, pos.x + 50, pos.y - 75, 1, graphics.argb(255, 255, 153, 51))
        graphics.draw_text_2D(
          tostring(math.floor(EDamage(target) * 3 + QDamage(target) + RDamage(target))) ..
            " (" ..
              tostring(math.floor((EDamage(target) * 3 + QDamage(target) + RDamage(target)) / target.health * 100)) ..
                "%)" .. "Not Killable",
          20,
          pos.x + 55,
          pos.y - 80,
          graphics.argb(255, 255, 153, 51)
        )
      end
      if (math.floor((EDamage(target) * 3 + RDamage(target) + QDamage(target)) / target.health * 100) >= 100) then
        graphics.draw_line_2D(pos.x, pos.y - 30, pos.x + 30, pos.y - 80, 1, graphics.argb(255, 150, 255, 200))
        graphics.draw_line_2D(pos.x + 30, pos.y - 80, pos.x + 50, pos.y - 80, 1, graphics.argb(255, 150, 255, 200))
        graphics.draw_line_2D(pos.x + 50, pos.y - 85, pos.x + 50, pos.y - 75, 1, graphics.argb(255, 150, 255, 200))
        graphics.draw_text_2D(
          tostring(math.floor(EDamage(target) * 3 + RDamage(target) + QDamage(target))) ..
            " (" ..
              tostring(math.floor((EDamage(target) * 3 + QDamage(target) + RDamage(target)) / target.health * 100)) ..
                "%)" .. "Kilable",
          20,
          pos.x + 55,
          pos.y - 80,
          graphics.argb(255, 150, 255, 200)
        )
      end
    end
  end
   local function OnDraw()
    if player.isOnScreen then
      if MyMenu.draws.drawq:get() then
        graphics.draw_circle(player.pos, spellQ.range, 2, MyMenu.draws.colorq:get(), 100)
      end
      if MyMenu.draws.drawe:get() then
        graphics.draw_circle(player.pos, spellE.range, 2, MyMenu.draws.colore:get(), 100)
      end
      if MyMenu.draws.draww:get() then
        graphics.draw_circle(player.pos, spellW.range, 2, MyMenu.draws.colorw:get(), 100)
      end
      if MyMenu.draws.drawwmin:get() then
        graphics.draw_circle(player.pos, 500, 2, MyMenu.draws.colorwmin:get(), 100)
      end
      if MyMenu.draws.drawr:get() then
        graphics.draw_circle(player.pos, spellR.range, 2, MyMenu.draws.colorr:get(), 100)
      end
      if MyMenu.draws.drawrf:get() then
        graphics.draw_circle(player.pos, spellR.range + 410, 2, MyMenu.draws.colorrf:get(), 100)
      end
    end
    if MyMenu.draws.drawkill:get() and player:spellSlot(2).state == 0 then
      for i = 0, objManager.minions.size[TEAM_ENEMY] - 1 do
        local minion = objManager.minions[TEAM_ENEMY][i]
        if
          minion and minion.isVisible and not minion.isDead and minion.moveSpeed > 0 and minion.isTargetable and
            minion.pos:dist(player.pos) < spellE.range
         then
          local minionPos = vec3(minion.x, minion.y, minion.z)
          --delay = player.pos:dist(minion.pos) / 3500 + 0.2
          delay = (MyMenu.misc.lasthittimer:get() / 1000) + (player.pos:dist(minion.pos) / 840)
          if (EDamage(minion) >= orb.farm.predict_hp(minion, delay / 2, true)) then
            if minion.isOnScreen then
              graphics.draw_circle(minionPos, 100, 2, graphics.argb(255, 255, 255, 0), 100)
            end
          end
        end
      end
    end
    --[[if MyMenu.draws.drawdamage:get() then
      local enemy = common.GetEnemyHeroes()
      for i, enemies in ipairs(enemy) do
        if
          enemies and enemies.isVisible and common.IsValidTarget(enemies) and player.pos:dist(enemies) < 1000 and
            not common.CheckBuffType(enemies, 17)
         then
          DrawDamagesE(enemies)
        end
      end
    end]]
    if MyMenu.draws.DIEnabled:get() then
      local targets = ObjectManager.GetEnemyHeroes()
      if targets and #targets > 0 then
          for i, target in ipairs(targets) do
              if target and MyCommon.IsValidTarget(target) and target.isVisible and target.isOnScreen and target.health > 0 and not BuffManager.HasBuffOfType(target, 17) then
                  local damage = (SpellManager.CanCastSpell(0) and QDamage(target) or 0)+ (SpellManager.CanCastSpell(1) and WDamage(target) or 0) + (SpellManager.CanCastSpell(2) and EDamage(target) or 0) + (SpellManager.CanCastSpell(3) and RDamage(target) or 0)
                  if damage > 0 then
                      local hp_bar_pos = target.barPos
                      local xPos = hp_bar_pos.x + 165
                      local yPos = hp_bar_pos.y + 122.5
                      if target.charName and target.charName == "Annie" then
                          yPos = yPos + 2
                      end
                      local remainHealth = target.health - damage
                      local x1 = xPos + ((target.health / target.maxHealth) * 104)
                      local x2 = xPos + (((remainHealth > 0 and remainHealth or 0) / target.maxHealth) * 103.4)
                      graphics.draw_line_2D(x1, yPos, x2, yPos, 11, (remainHealth > 0 and 0xFFF2781E or 0xFFFF0000))
                  end
              end
          end
      end
  end
    --[[if MyMenu.draws.drawtoggle:get() then
      local pos = graphics.world_to_screen(vec3(player.x, player.y, player.z))
      if uhh == true then
        graphics.draw_text_2D("Push", 16, pos.x - 20, pos.y + 30, MyMenu.draws.colordrawtoggle:get())
      else
        graphics.draw_text_2D("Freez", 16, pos.x - 20, pos.y + 30, MyMenu.draws.colordrawtoggle:get())
      end]]
  end
  cb.add(cb.draw, OnDraw)
  cb.add(cb.tick, OnTick)
  cb.add(cb.spell, AutoInterrupt)
  return CassiopeiaPlugin
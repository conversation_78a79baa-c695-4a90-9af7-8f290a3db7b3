local lvxbot = module.load(header.id, 'lvxbot/main')

local input = {
  prediction = {
    type = 'Linear',
    --
    range = 1500,
    delay = 0.6;
    speed = 3300,
    width =65,
    boundingRadiusMod = 1,
	predslot = 1,
	
	  collision = {
      hero = false, --allow to hit other heros :-)
      minion = true,
      wall = true,
  },

 
 },
  
  
  target_selector = {
    type = 'LESS_CAST_AD',
  },

  cast_spell = {
    type = 'pos',
    slot = _W,
    arg1 = function(action)
      local seg = action.ts_result.seg
      local obj = action.ts_result.obj
      return seg.endPos:to3D(obj.y)
    end,
  },

  slot = _W,
  ignore_obj_radius = 2000,
}

local module = lvxbot.jinxexpert.create(input)



return module

-- Answer.lua - Authorization module for Kornis AIO
-- This is a simplified version to bypass authorization checks

local Answer = {}

-- Authorization status
Answer.i = true  -- HWID match status
Answer.e = false -- Subscription expired status
Answer.h = true  -- General authorization status
Answer.resets = 0 -- HWID resets remaining
Answer.dLeft = "∞ days" -- Days left in subscription

-- Spell data - basic configuration (LOL 15.12版本数值)
Answer.data = [[{
  "Q": 825,
  "W": 0,
  "E": 800,
  "R": 575,
  "RL": "NO:)asfasfasf"
}]]

return Answer

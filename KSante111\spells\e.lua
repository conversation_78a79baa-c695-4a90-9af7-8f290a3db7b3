
local ove_0_10 = module.load("Brian", "common/common")
local ove_0_11 = {
	last = 0,
	range = 250,
	slot = player:spellSlot(_E)
}

function ove_0_11.is_ready()
	return ove_0_11.slot.state == 0
end

function ove_0_11.getClosesMouseMinion()
	local slot_6_0
	local slot_6_1 = math.huge

	for iter_6_0, iter_6_1 in pairs(ove_0_10.GetAllyMinionsInRange(500, player.pos)) do
		if iter_6_1 ~= nil and ove_0_10.IsValidTarget(iter_6_1) then
			local slot_6_2 = ove_0_10.GetDistance(iter_6_1.pos, game.mousePos)

			if slot_6_2 < slot_6_1 then
				slot_6_0 = iter_6_1
				slot_6_1 = slot_6_2
			end
		end
	end

	return slot_6_0
end

function ove_0_11.getClosesMouseAlly()
	local slot_7_0
	local slot_7_1 = math.huge

	for iter_7_0, iter_7_1 in pairs(ove_0_10.GetAllyHeroesInRange(500, player.pos)) do
		if iter_7_1 ~= nil and iter_7_1.ptr ~= player.ptr and ove_0_10.IsValidTarget(iter_7_1) then
			local slot_7_2 = ove_0_10.GetDistance(iter_7_1.pos, game.mousePos)

			if slot_7_2 < slot_7_1 then
				slot_7_0 = iter_7_1
				slot_7_1 = slot_7_2
			end
		end
	end

	return slot_7_0
end

function ove_0_11.invoke_action_flee()
	local slot_8_0 = ove_0_11.getClosesMouseMinion()

	if slot_8_0 and slot_8_0.pos:dist(player.pos) <= 500 then
		player:castSpell("obj", _E, slot_8_0)
	else
		player:castSpell("pos", _E, game.mousePos)
	end

	local slot_8_1 = ove_0_11.getClosesMouseAlly()

	if slot_8_1 and slot_8_1.pos:dist(player.pos) <= 500 then
		player:castSpell("obj", _E, slot_8_1)
	else
		player:castSpell("pos", _E, game.mousePos)
	end
end

function ove_0_11.invoke_action_jungle()
	for iter_9_0 = 0, objManager.minions.size[TEAM_NEUTRAL] - 1 do
		local slot_9_0 = objManager.minions[TEAM_NEUTRAL][iter_9_0]

		if slot_9_0 and not slot_9_0.isDead and slot_9_0.isVisible and slot_9_0.maxHealth > 5 and ove_0_10.valid_minion(slot_9_0) and slot_9_0.charName ~= "Sru_Crab" and ove_0_10.GetDistance(slot_9_0, player) < ove_0_10.GetAARange() then
			player:castSpell("pos", _E, slot_9_0.path.serverPos)

			break
		end
	end
end

return ove_0_11

local module = {}

-- 距离检查工具
function module.CheckDistance(pos1, pos2, range)
    if not pos1 or not pos2 then return false end
    return pos1:dist(pos2) <= range
end

-- 敌人范围检查
function module.InPlayerRange(obj, range)
    if not obj or not player or not range then return false end
    return player.pos:dist(obj.pos) <= range
end

-- 检查目标是否有死亡印记
function module.HasDeathMark(target)
    if not target or target.isDead then return false end
    return target:getBuff("ZedDeathMark") ~= nil or target:getBuff("zedrshadowclone") ~= nil
end

-- 位置安全检查
function module.IsLocationSafe(pos)
    if not pos then return false end
    local turretCount = 0
    for i = 0, objManager.enemies_n - 1 do
        local turret = objManager.enemies[i]
        if turret and turret.type == TYPE_TURRET and turret.pos:dist(pos) < 950 then
            turretCount = turretCount + 1
        end
    end
    return turretCount == 0
end

-- 计算附近敌人数量
function module.CountEnemiesNearby(pos, range)
    if not pos or not range then return 0 end
    local count = 0
    for i = 0, objManager.enemies_n - 1 do
        local enemy = objManager.enemies[i]
        if enemy and enemy.type == TYPE_HERO and not enemy.isDead and enemy.isVisible 
           and enemy.pos:dist(pos) <= range then
            count = count + 1
        end
    end
    return count
end

-- 获取逃生方向
function module.GetEscapeDirection()
    local direction = vec3(0, 0, 0)
    local enemyCount = 0
    
    for i = 0, objManager.enemies_n - 1 do
        local enemy = objManager.enemies[i]
        if enemy and enemy.type == TYPE_HERO and not enemy.isDead and enemy.isVisible 
           and enemy.pos:dist(player.pos) <= 1000 then
            local enemyDir = (player.pos - enemy.pos):norm()
            direction = direction + enemyDir
            enemyCount = enemyCount + 1
        end
    end
    
    if enemyCount > 0 then
        return direction:norm()
    end
    return nil
end

-- 精美绘图系统
function module.DrawPremiumCircle(pos, radius, color, style)
    if not pos or not radius then return end
    
    style = style or "glow"
    local effect
    
    if style == "rainbow" then
        effect = graphics.create_effect(graphics.CIRCLE_RAINBOW)
    elseif style == "fire" then
        effect = graphics.create_effect(graphics.CIRCLE_FIRE)
    elseif style == "glow_rainbow" then
        effect = graphics.create_effect(graphics.CIRCLE_GLOW_RAINBOW)
    elseif style == "glow_bold" then
        effect = graphics.create_effect(graphics.CIRCLE_GLOW_BOLD)
    else
        effect = graphics.create_effect(graphics.CIRCLE_GLOW)
    end
    
    if effect then
        effect:set_pos(pos)
        effect:set_scale(radius / 100)
        effect:set_color(color or 0xFF00FF00)
        effect:draw()
    else
        -- 备用绘制方法
        graphics.draw_circle(pos, radius, 3, color or 0xFF00FF00, 64)
    end
end

-- 阴影圆圈特效
function module.DrawShadowCircle(pos, radius, color)
    if not pos or not radius then return end
    
    -- 主圆圈 - 使用炫光彩虹效果
    local mainEffect = graphics.create_effect(graphics.CIRCLE_GLOW_RAINBOW)
    if mainEffect then
        mainEffect:set_pos(pos)
        mainEffect:set_scale(radius / 100)
        mainEffect:set_color(color or 0xFF8A2BE2)
        mainEffect:draw()
    end
    
    -- 外圈发光效果
    local glowEffect = graphics.create_effect(graphics.CIRCLE_GLOW)
    if glowEffect then
        glowEffect:set_pos(pos)
        glowEffect:set_scale((radius + 30) / 100)
        glowEffect:set_color(0x40FFFFFF)
        glowEffect:draw()
    end
end

-- 技能范围显示
function module.DrawSkillRange(pos, radius, skill_name, color)
    if not pos or not radius then return end
    
    local style = "glow"
    if skill_name == "Q" then
        style = "fire"
        color = 0xFF4169E1
    elseif skill_name == "W" then
        style = "glow_rainbow"
        color = 0xFF8A2BE2
    elseif skill_name == "E" then
        style = "rainbow"
        color = 0xFF32CD32
    elseif skill_name == "R" then
        style = "glow_bold"
        color = 0xFFDC143C
    end
    
    module.DrawPremiumCircle(pos, radius, color, style)
end

-- 目标高亮显示
function module.DrawTargetHighlight(target)
    if not target or target.isDead then return end
    
    -- 目标主体高亮
    local targetEffect = graphics.create_effect(graphics.CIRCLE_FIRE)
    if targetEffect then
        targetEffect:set_pos(target.pos)
        targetEffect:set_scale(target.boundingRadius / 50)
        targetEffect:set_color(0xFFFF4500)
        targetEffect:draw()
    end
    
    -- 外圈指示器
    module.DrawPremiumCircle(target.pos, target.boundingRadius + 80, 0xFFFF6347, "rainbow")
end

-- 伤害预测显示
function module.DrawDamagePrediction(target, damage, totalHP)
    if not target or target.isDead then return end
    
    local healthPercent = damage / totalHP
    local color = 0xFF00FF00  -- 绿色
    
    if healthPercent >= 1.0 then
        color = 0xFFFF0000  -- 红色 - 可击杀
    elseif healthPercent >= 0.7 then
        color = 0xFFFF8C00  -- 橙色
    elseif healthPercent >= 0.4 then
        color = 0xFFFFD700  -- 金色
    end
    
    -- 伤害指示圆圈
    module.DrawPremiumCircle(target.pos, target.boundingRadius + 50, color, "glow_bold")
    
    -- 击杀指示器
    if healthPercent >= 1.0 then
        local killEffect = graphics.create_effect(graphics.CIRCLE_RAINBOW_BOLD)
        if killEffect then
            killEffect:set_pos(target.pos)
            killEffect:set_scale((target.boundingRadius + 100) / 100)
            killEffect:set_color(0xFFFF0000)
            killEffect:draw()
        end
    end
end

-- 原有的伤害计算函数
local function slot_15_54(arg_22_0)
	if arg_22_0 and arg_22_0.health / arg_22_0.maxHealth <= 0.5 and not arg_22_0.buff.zedpassivecd then
		return arg_22_0.maxHealth * 0.1 * (100 / (100 + arg_22_0.spellBlock))
	else
		return 0
	end
end

local function slot_15_55(arg_23_0)
	return (player.baseAttackDamage + player.flatPhysicalDamageMod) * (100 / (100 + arg_23_0.armor))
end

local function slot_15_56(arg_24_0)
	if player:spellSlot(0).level == 0 then
		return 0
	end

	local slot_24_0 = {
		80,
		115,
		150,
		185,
		220
	}
	local slot_24_1 = player:spellSlot(0).level
	local slot_24_2 = player.flatPhysicalDamageMod
	local slot_24_3 = 100 / (100 + arg_24_0.armor)

	return (slot_24_0[slot_24_1] + slot_24_2 * 0.9) * slot_24_3
end

local function slot_15_57(arg_25_0)
	if player:spellSlot(0).level == 0 then
		return 0
	end

	local slot_25_0 = {
		80,
		115,
		150,
		185,
		220
	}
	local slot_25_1 = player:spellSlot(0).level
	local slot_25_2 = player.flatPhysicalDamageMod
	local slot_25_3 = 100 / (100 + arg_25_0.armor)

	return (slot_25_0[slot_25_1] + slot_25_2 * 0.9) * slot_25_3 * 0.6
end

local function slot_15_58(arg_26_0)
	if player:spellSlot(2).level == 0 then
		return 0
	end

	local slot_26_0 = {
		70,
		95,
		120,
		145,
		170
	}
	local slot_26_1 = player:spellSlot(2).level
	local slot_26_2 = player.flatPhysicalDamageMod
	local slot_26_3 = 100 / (100 + arg_26_0.armor)

	return (slot_26_0[slot_26_1] + slot_26_2 * 0.8) * slot_26_3
end

local function slot_15_59(arg_27_0)
	if player:spellSlot(3).level == 0 then
		return 0
	end

	local slot_27_0 = {
		0.25,
		0.35,
		0.45
	}
	local slot_27_1 = player:spellSlot(3).level

	return (slot_15_56(arg_27_0) * 2 + slot_15_58(arg_27_0) + slot_15_55(arg_27_0) + slot_15_54(arg_27_0) + player.baseAttackDamage + player.flatPhysicalDamageMod) * slot_27_0[slot_27_1]
end

local function slot_15_60(arg_28_0, arg_28_1)
	if player:spellSlot(3).level == 0 or not arg_28_0 or not arg_28_1 then
		return 0
	end

	local slot_28_0 = {
		0.25,
		0.35,
		0.45
	}
	local slot_28_1 = player:spellSlot(3).level

	return (arg_28_1 - arg_28_0.health) * slot_28_0[slot_28_1]
end

local function slot_15_61()
	if player:spellSlot(3).name == "ZedR" then
		return true
	else
		return false
	end
end

local function slot_15_62()
	if player:spellSlot(1).name == "ZedW" then
		return true
	else
		return false
	end
end

-- 将原有的函数添加到module中，保持兼容性
module.QDmg = slot_15_56
module.Q2Dmg = slot_15_57
module.AADmg = slot_15_55
module.EDmg = slot_15_58
module.RDmg = slot_15_59
module.R2Dmg = slot_15_60
module.APDmg = slot_15_54
module.Rstate = slot_15_61
module.Wstate = slot_15_62

return module


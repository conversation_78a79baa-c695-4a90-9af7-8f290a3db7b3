
	local slot_15_48 = {}

	_Q, _<PERSON>, _<PERSON>, _<PERSON> = 0, 1, 2, 3

	local slot_15_49 = {
		0.49,
		0.48,
		0.47,
		0.46,
		0.45,
		0.44,
		0.43,
		0.42,
		0.41,
		0.4,
		0.39,
		0.38,
		0.37,
		0.36,
		0.35,
		0.34,
		0.33,
		0.32,
		0.31
	}
	local slot_15_50 = {
		0.55,
		0.65,
		0.75
	}
	local slot_15_51 = {
		0.1,
		0.12,
		0.14,
		0.16,
		0.18
	}
	local slot_15_52 = {
		0.45,
		0.475,
		0.5,
		0.525,
		0.55
	}
	local slot_15_53 = {
		0.35,
		0.4,
		0.45,
		0.5,
		0.55
	}
	local slot_15_54 = {
		0.25,
		0.3,
		0.35,
		0.4,
		0.45
	}
	local slot_15_55 = {
		0.28,
		0.33,
		0.38
	}
	local slot_15_56 = {
		0.35,
		0.4,
		0.45,
		0.5,
		0.55
	}

	function slot_15_48.get_damage(arg_22_0, arg_22_1, arg_22_2, arg_22_3)
		-- function 22
		if arg_22_3 then
			local slot_22_0 = arg_22_1.charName

			if arg_22_2.type == TYPE_HERO and arg_22_1.type == TYPE_HERO and arg_22_2.buff["assets/perks/styles/domination/darkharvest/darkharvest.lua"] and not arg_22_2.buff["assets/perks/styles/domination/darkharvest/darkharvestcooldown.lua"] and common.GetHp(arg_22_1) <= 49 then
				local slot_22_1 = 20 + 2.3529411764705883 * (arg_22_2.level - 1) + common.GetTotalAP(arg_22_2) * 0.15 + common.GetBonusAD(arg_22_2) * 0.25 + arg_22_2.buff["assets/perks/styles/domination/darkharvest/darkharvest.lua"].stacks2 * 9

				arg_22_0 = arg_22_0 + common.CAP(arg_22_1, slot_22_1, arg_22_2)
			end

			if common.findRune(arg_22_2, 8014) and common.GetHp(arg_22_1) < 40 then
				arg_22_0 = arg_22_0 + arg_22_0 * 0.08
			end

			if arg_22_1.type == TYPE_HERO and arg_22_1.buff.meditate then
				arg_22_0 = arg_22_0 * (1 - slot_15_52[arg_22_1:spellSlot(1).level])
			end

			if arg_22_3 == 1 then
				if arg_22_1.allShield > 0 then
					arg_22_0 = arg_22_0 - arg_22_1.allShield
				else
					arg_22_0 = arg_22_0 - arg_22_1.magicalShield
				end

				if arg_22_1.buff.galiow then
					arg_22_0 = arg_22_0 * (1 - slot_15_54[arg_22_1:spellSlot(1).level])
				end

				if arg_22_1.buff.galiorallybuff then
					arg_22_0 = arg_22_0 * (1 - slot_15_55[arg_22_1:spellSlot(3).level])
				end
			elseif arg_22_3 == 2 then
				if arg_22_1.allShield > 0 then
					arg_22_0 = arg_22_0 - arg_22_1.allShield
				else
					arg_22_0 = arg_22_0 - arg_22_1.physicalShield
				end

				if slot_22_0 == "Amumu" and arg_22_1.buff.tantrum then
					arg_22_0 = arg_22_0 - 2 * arg_22_1:spellSlot(2).level
				end

				if slot_22_0 == "Blitzcrank" and arg_22_1.buff.manabarriericon and not arg_22_1.buff.manabarriercooldown then
					arg_22_0 = arg_22_0 - arg_22_1.mana * 0.3
				end

				if arg_22_1.team == TEAM_ENEMY and arg_22_1.type ~= TYPE_HERO and arg_22_1.buff.exaltedwithbaronnashorminion then
					if math.floor(game.time / 60) <= 20 then
						if slot_22_0:find("Ranged") then
							arg_22_0 = arg_22_0 * 0.5
						end

						if slot_22_0:find("Melee") then
							arg_22_0 = arg_22_0 * 0.5
						end
					end

					if math.floor(game.time / 60) >= 21 and not (math.floor(game.time / 60) >= 40) then
						if slot_22_0:find("Ranged") then
							arg_22_0 = arg_22_0 * (slot_15_49[math.floor(game.time / 60) - 20] or 0.5)
						end

						if slot_22_0:find("Melee") then
							arg_22_0 = arg_22_0 * (slot_15_49[math.floor(game.time / 60) - 20] or 0.5)
						end
					end

					if math.floor(game.time / 60) >= 40 then
						if slot_22_0:find("Ranged") then
							arg_22_0 = arg_22_0 * 0.3
						end

						if slot_22_0:find("Melee") then
							arg_22_0 = arg_22_0 * 0.3
						end
					end
				end
			end

			if slot_22_0 == "Pantheon" and arg_22_1.buff.pantheone then
				arg_22_0 = 0
			end

			if slot_22_0 == "Briar" and arg_22_1.buff.briare then
				arg_22_0 = arg_22_0 * 0.6 - arg_22_1.maxHealth * 0.025
			end

			if arg_22_1.buff.garenw then
				arg_22_0 = arg_22_0 * 0.7
			end

			if arg_22_2.buff.summonerexhaust then
				arg_22_0 = arg_22_0 * 0.6
			end

			if arg_22_1.buff.gragaswself then
				arg_22_0 = arg_22_0 * (1 - (slot_15_51[arg_22_1:spellSlot(1).level] + common.GetTotalAP(arg_22_1) / 100 * 0.04))
			end

			if arg_22_1.buff.ferocioushowl then
				arg_22_0 = arg_22_0 * (1 - slot_15_50[arg_22_1:spellSlot(3).level])
			end

			if arg_22_1.buff.warwicke then
				arg_22_0 = arg_22_0 * (1 - slot_15_53[arg_22_1:spellSlot(2).level])
			end

			if arg_22_1.buff.braumeshieldbuff then
				arg_22_0 = arg_22_0 * (1 - slot_15_56[arg_22_1:spellSlot(2).level])
			end

			if arg_22_1.buff.sivire or arg_22_1.buff.fioraw or arg_22_1.buff.kindredrnodeathbuff then
				return 0
			end
		end

		if arg_22_0 > 0 and Activator and Activator.item[6676] and arg_22_1 and arg_22_1.type == TYPE_HERO and arg_22_0 + arg_22_1.maxHealth * 0.05 > arg_22_1.health + arg_22_1.physicalShield then
			arg_22_0 = 99999
		end

		return arg_22_0 - common.jshfhp(arg_22_1, 1), arg_22_3
	end

	slot_15_48.Ignite = {
		damage = function(arg_23_0, arg_23_1)
			-- function 23
			if not arg_23_0 then
				return 0
			end

			local slot_23_0 = arg_23_1 or player
			local slot_23_1

			if slot_23_0:spellSlot(4).name == "SummonerDot" then
				slot_23_1 = 4
			elseif slot_23_0:spellSlot(5).name == "SummonerDot" then
				slot_23_1 = 5
			end

			if slot_23_1 and slot_23_0:spellSlot(slot_23_1).state == 0 then
				local slot_23_2 = 50 + 20 * (slot_23_0.level > 18 and 18 or slot_23_0.level)

				if arg_23_0 then
					slot_23_2 = slot_23_2 - arg_23_0.physicalShield
				end

				return slot_23_2, slot_23_1
			end

			return 0
		end
	}
	slot_15_48.Electrocute = {
		damage = function(arg_24_0, arg_24_1)
			-- function 24
			if not arg_24_0 then
				return 0
			end

			local slot_24_0 = arg_24_1 or player

			if slot_24_0.buff["assets/perks/styles/domination/electrocute/electrocute.lua"] then
				local slot_24_1 = 50 + 8.235294117647058 * ((slot_24_0.level > 18 and 18 or slot_24_0.level) - 1)
				local slot_24_2 = common.GetTotalAP(slot_24_0)
				local slot_24_3 = common.GetBonusAD(slot_24_0)
				local slot_24_4 = slot_24_1 + slot_24_2 * 0.05 + slot_24_3 * 0.1
				local slot_24_5 = common.CAP(arg_24_0, slot_24_4, slot_24_0)

				return slot_15_48.get_damage(slot_24_5, arg_24_0, slot_24_0, 2)
			end

			return 0
		end
	}
	slot_15_48.Perks_ArcaneComet_Mis_Arc = {
		damage = function(arg_25_0, arg_25_1)
			-- function 25
			if not arg_25_0 then
				return 0
			end

			local slot_25_0 = arg_25_1 or player

			spell_Dmg = 30 + 4.117647058823529 * (slot_25_0.level - 1)
			totalDmg = spell_Dmg + common.GetTotalAP(slot_25_0) * 0.2 + common.GetBonusAD(slot_25_0) * 0.35
			returndmg = common.CAP(arg_25_0, totalDmg, slot_25_0) - common.jshfhp(arg_25_0, 1)

			return slot_15_48.get_damage(returndmg, arg_25_0, slot_25_0, 1)
		end
	}
	slot_15_48["3152Active"] = {
		damage = function(arg_26_0, arg_26_1)
			-- function 26
			if not arg_26_0 then
				return 0
			end

			local slot_26_0 = arg_26_1 or player

			spell_Dmg = 125
			totalDmg = spell_Dmg + common.GetTotalAP(slot_26_0) * 0.15
			returndmg = common.CAP(arg_26_0, totalDmg, slot_26_0) - common.jshfhp(arg_26_0, 1)

			return slot_15_48.get_damage(returndmg, arg_26_0, slot_26_0, 1)
		end
	}
	slot_15_48["6656Cast"] = {
		damage = function(arg_27_0, arg_27_1)
			-- function 27
			if not arg_27_0 then
				return 0
			end

			local slot_27_0 = arg_27_1 or player

			spell_Dmg = 100
			totalDmg = spell_Dmg + common.GetTotalAP(slot_27_0) * 0.3
			returndmg = common.CAP(arg_27_0, totalDmg, slot_27_0) - common.jshfhp(arg_27_0, 1)

			return slot_15_48.get_damage(returndmg, arg_27_0, slot_27_0, 1)
		end
	}
	slot_15_48["6671Cast"] = {
		damage = function(arg_28_0, arg_28_1)
			-- function 28
			if not arg_28_0 then
				return 0
			end

			local slot_28_0 = arg_28_1 or player
			local slot_28_1 = slot_28_0.level

			if slot_28_1 > 18 then
				slot_28_1 = 18
			end

			local slot_28_2 = {
				56,
				62,
				68,
				74,
				80,
				86,
				92,
				98,
				104,
				110,
				116
			}
			local slot_28_3 = 0

			if slot_28_1 > 7 then
				slot_28_3 = slot_28_2[slot_28_1 - 7]
			else
				slot_28_3 = 50
			end

			local slot_28_4 = slot_28_3 + common.GetBonusAD(slot_28_0) * 0.15
			local slot_28_5 = common.CAD(arg_28_0, slot_28_4, slot_28_0) * 3

			return slot_15_48.get_damage(slot_28_5, arg_28_0, slot_28_0, 1)
		end
	}
	slot_15_48["6693Active"] = {
		damage = function(arg_29_0, arg_29_1)
			-- function 29
			if not arg_29_0 then
				return 0
			end

			local slot_29_0 = arg_29_1 or player
			local slot_29_1 = 75 + common.GetBonusAD(slot_29_0) * 0.3
			local slot_29_2 = common.CAD(arg_29_0, slot_29_1, slot_29_0)

			return slot_15_48.get_damage(slot_29_2, arg_29_0, slot_29_0, 1)
		end
	}
	slot_15_48["3077Active"] = {
		damage = function(arg_30_0, arg_30_1)
			-- function 30
			if not arg_30_0 or not common.Activator.item["3077Active"] then
				return 0
			end

			local slot_30_0 = arg_30_1 or player
			local slot_30_1 = common.GetTotalAD(slot_30_0) * 0.75

			return (common.CAD(arg_30_0, slot_30_1, slot_30_0))
		end
	}
	slot_15_48["6698Active"] = {
		damage = function(arg_31_0, arg_31_1)
			-- function 31
			if not arg_31_0 or not common.Activator.item["6698Active"] then
				return 0
			end

			local slot_31_0 = arg_31_1 or player
			local slot_31_1 = common.GetTotalAD(slot_31_0) * 1

			if common.GetHp(arg_31_0) < 50 then
				slot_31_1 = slot_31_1 + common.GetTotalAD(slot_31_0) * 0.3
			end

			return (common.CAD(arg_31_0, slot_31_1, slot_31_0))
		end
	}
	slot_15_48["3748Active"] = {
		damage = function(arg_32_0, arg_32_1)
			-- function 32
			if not arg_32_0 or not common.Activator.item["3748Active"] then
				return 0
			end

			local slot_32_0 = arg_32_1 or player
			local slot_32_1 = common.GetTotalAD(slot_32_0) * 1

			return (common.CAD(arg_32_0, slot_32_1, slot_32_0))
		end
	}
	slot_15_48["3074Active"] = {
		damage = function(arg_33_0, arg_33_1)
			-- function 33
			if not arg_33_0 or not common.Activator.item["3074Active"] then
				return 0
			end

			local slot_33_0 = arg_33_1 or player
			local slot_33_1 = common.GetTotalAD(slot_33_0) * 1

			return (common.CAD(arg_33_0, slot_33_1, slot_33_0))
		end
	}
	slot_15_48.Lissandra = {
		Q = {
			ignore_obj_radius = 1200,
			target_selector = 8,
			prediction = {
				hitchance = 0,
				new_path = 0.5,
				boundingRadiusMod = 1,
				width = 75,
				speed = 2200,
				delay = 0.2509999871254,
				range = 725,
				type = "Linear",
				out_range = 450,
				collision = {
					wall = true,
					minion = true,
					hero = true
				}
			},
			cast_spell = {
				type = "pos",
				slot = _Q,
				arg1 = function(arg_34_0)
					-- function 34
					local slot_34_0 = arg_34_0.ts.seg
					local slot_34_1 = arg_34_0.ts.obj

					return slot_34_0.endPos
				end
			},
			slot = player:spellSlot(_Q),
			damage = function(arg_35_0, arg_35_1)
				-- function 35
				if not arg_35_0 then
					return 0
				end

				local slot_35_0 = arg_35_1 or player

				spell_tb = {
					80,
					115,
					150,
					185,
					220
				}
				spell_level = slot_35_0:spellSlot(_Q).level
				spell_Dmg = spell_tb[spell_level] or 0
				totalDmg = spell_Dmg + common.GetTotalAP(slot_35_0) * 0.75
				returndmg = common.CAP(arg_35_0, totalDmg, slot_35_0)

				return slot_15_48.get_damage(returndmg, arg_35_0, slot_35_0, 1)
			end
		},
		W = {
			ignore_obj_radius = 600,
			target_selector = 8,
			prediction = {
				delay = 0,
				range = 425,
				dashRadius = 0,
				boundingRadiusModTarget = 0,
				boundingRadiusModSource = 0,
				type = "InRange",
				radius = 425
			},
			cast_spell = {
				type = "self",
				slot = _W
			},
			slot = player:spellSlot(_W),
			damage = function(arg_36_0, arg_36_1)
				-- function 36
				if not arg_36_0 then
					return 0
				end

				local slot_36_0 = arg_36_1 or player

				spell_tb = {
					70,
					105,
					140,
					175,
					210
				}
				spell_level = slot_36_0:spellSlot(_W).level
				spell_Dmg = spell_tb[spell_level] or 0
				totalDmg = spell_Dmg + common.GetTotalAP(slot_36_0) * 0.7
				returndmg = common.CAP(arg_36_0, totalDmg, slot_36_0) - common.jshfhp(arg_36_0, 1)

				return slot_15_48.get_damage(returndmg, arg_36_0, slot_36_0, 1)
			end
		},
		E = {
			ignore_obj_radius = 1200,
			target_selector = 8,
			prediction = {
				delay = 0.25,
				range = 1025,
				width = 125,
				hitchance = 0,
				boundingRadiusMod = 1,
				type = "Linear",
				speed = 850,
				collision = {
					wall = true,
					minion = true,
					hero = false
				}
			},
			cast_spell = {
				type = "pos",
				slot = _E,
				arg1 = function(arg_37_0)
					-- function 37
					local slot_37_0 = arg_37_0.ts.seg
					local slot_37_1 = arg_37_0.ts.obj

					return slot_37_0.endPos
				end
			},
			slot = player:spellSlot(_E),
			damage = function(arg_38_0, arg_38_1)
				-- function 38
				if not arg_38_0 then
					return 0
				end

				spell_tb = {
					70,
					105,
					140,
					175,
					210
				}

				local slot_38_0 = arg_38_1 or player

				spell_level = slot_38_0:spellSlot(_E).level
				spell_Dmg = spell_tb[spell_level] or 0
				totalDmg = spell_Dmg + common.GetTotalAP(slot_38_0) * 0.6
				returndmg = common.CAP(arg_38_0, totalDmg, slot_38_0) - common.jshfhp(arg_38_0, 2)

				return slot_15_48.get_damage(returndmg, arg_38_0, slot_38_0, 1)
			end
		},
		R = {
			ignore_obj_radius = 650,
			target_selector = 8,
			prediction = {
				delay = 0.375,
				range = 550,
				dashRadius = 0,
				boundingRadiusModTarget = 0,
				boundingRadiusModSource = 0,
				type = "InRange",
				radius = 550
			},
			cast_spell = {
				type = "obj",
				slot = _R,
				arg1 = function(arg_39_0)
					-- function 39
					return arg_39_0.ts.obj
				end
			},
			slot = player:spellSlot(_R),
			damage = function(arg_40_0, arg_40_1)
				-- function 40
				if not arg_40_0 then
					return 0
				end

				spell_tb = {
					150,
					250,
					350
				}

				local slot_40_0 = arg_40_1 or player

				spell_level = slot_40_0:spellSlot(_R).level
				spell_Dmg = spell_tb[spell_level] or 0
				totalDmg = spell_Dmg + common.GetTotalAP(slot_40_0) * 0.75
				returndmg = common.CAP(arg_40_0, totalDmg, slot_40_0) - common.jshfhp(arg_40_0, 1)

				return slot_15_48.get_damage(returndmg, arg_40_0, slot_40_0, 1)
			end
		}
	}
	slot_15_48.Viego = {
		Q = {
			ignore_obj_radius = 800,
			target_selector = 8,
			prediction = {
				hitchance = 0,
				new_path = 0.5,
				boundingRadiusMod = 1,
				width = 62.5,
				speed = 2200,
				delay = 0.35,
				range = 600,
				type = "Linear",
				out_range = 750,
				collision = {
					wall = true,
					minion = false,
					hero = false
				}
			},
			cast_spell = {
				type = "pos",
				slot = _Q,
				arg1 = function(arg_41_0)
					-- function 41
					local slot_41_0 = arg_41_0.ts.seg
					local slot_41_1 = arg_41_0.ts.obj

					return slot_41_0.endPos
				end
			},
			slot = player:spellSlot(_Q),
			damage = function(arg_42_0, arg_42_1)
				-- function 42
				if not arg_42_0 then
					return 0
				end

				local slot_42_0 = arg_42_1 or player

				spell_tb = {
					15,
					30,
					45,
					60,
					75
				}
				spell_level = slot_42_0:spellSlot(_Q).level
				spell_Dmg = spell_tb[spell_level] or 0
				totalDmg = spell_Dmg + common.GetTotalAD(slot_42_0) * 0.7
				returndmg = common.CAD(arg_42_0, totalDmg, slot_42_0) - common.jshfhp(arg_42_0, 1)

				return slot_15_48.get_damage(returndmg, arg_42_0, slot_42_0, 2)
			end
		},
		W = {
			ignore_obj_radius = 1100,
			target_selector = 8,
			prediction = {
				hitchance = 0,
				new_path = 0.5,
				boundingRadiusMod = 1,
				width = 40,
				speed = 1300,
				delay = 0,
				range = 900,
				type = "Linear",
				out_range = 1000,
				collision = {
					wall = true,
					minion = true,
					hero = false
				}
			},
			cast_spell = {
				type = "pos",
				slot = _W,
				arg1 = function(arg_43_0)
					-- function 43
					local slot_43_0 = arg_43_0.ts.seg
					local slot_43_1 = arg_43_0.ts.obj

					return slot_43_0.endPos
				end
			},
			slot = player:spellSlot(_W),
			damage = function(arg_44_0, arg_44_1)
				-- function 44
				if not arg_44_0 then
					return 0
				end

				local slot_44_0 = arg_44_1 or player

				spell_tb = {
					80,
					135,
					190,
					245,
					300
				}
				spell_level = slot_44_0:spellSlot(_W).level
				spell_Dmg = spell_tb[spell_level] or 0
				totalDmg = spell_Dmg + common.GetTotalAP(slot_44_0) * 1
				returndmg = common.CAP(arg_44_0, totalDmg, slot_44_0) - common.jshfhp(arg_44_0, 1)

				return slot_15_48.get_damage(returndmg, arg_44_0, slot_44_0, 1)
			end
		},
		W_Flash = {
			ignore_obj_radius = 1100,
			target_selector = 8,
			prediction = {
				hitchance = 0,
				new_path = 0.5,
				boundingRadiusMod = 0,
				width = 40,
				speed = 1300,
				delay = 0,
				range = 1300,
				type = "Linear",
				out_range = 1000,
				collision = {
					wall = true,
					minion = true,
					hero = false
				}
			},
			cast_spell = {
				type = "pos",
				slot = _W,
				arg1 = function(arg_45_0)
					-- function 45
					local slot_45_0 = arg_45_0.ts.seg
					local slot_45_1 = arg_45_0.ts.obj

					return slot_45_0.endPos
				end
			},
			slot = player:spellSlot(_W),
			damage = function(arg_46_0, arg_46_1)
				-- function 46
				if not arg_46_0 then
					return 0
				end

				local slot_46_0 = arg_46_1 or player

				spell_tb = {
					80,
					135,
					190,
					245,
					300
				}
				spell_level = slot_46_0:spellSlot(_W).level
				spell_Dmg = spell_tb[spell_level] or 0
				totalDmg = spell_Dmg + common.GetTotalAP(slot_46_0) * 1
				returndmg = common.CAP(arg_46_0, totalDmg, slot_46_0) - common.jshfhp(arg_46_0, 1)

				return slot_15_48.get_damage(returndmg, arg_46_0, slot_46_0, 1)
			end
		},
		W_collision = {
			ignore_obj_radius = 1100,
			target_selector = 8,
			prediction = {
				hitchance = 0,
				new_path = 0.5,
				boundingRadiusMod = 1,
				width = 80,
				speed = 1400,
				delay = 0,
				range = 900,
				type = "Linear",
				out_range = 1000,
				collision = {
					wall = true,
					minion = true,
					hero = false
				}
			},
			cast_spell = {
				type = "pos",
				slot = _W,
				arg1 = function(arg_47_0)
					-- function 47
					local slot_47_0 = arg_47_0.ts.seg
					local slot_47_1 = arg_47_0.ts.obj

					return slot_47_0.endPos
				end
			},
			slot = player:spellSlot(_W),
			damage = function(arg_48_0, arg_48_1)
				-- function 48
				if not arg_48_0 then
					return 0
				end

				local slot_48_0 = arg_48_1 or player

				spell_tb = {
					80,
					135,
					190,
					245,
					300
				}
				spell_level = slot_48_0:spellSlot(_W).level
				spell_Dmg = spell_tb[spell_level] or 0
				totalDmg = spell_Dmg + common.GetTotalAP(slot_48_0) * 1
				returndmg = common.CAP(arg_48_0, totalDmg, slot_48_0) - common.jshfhp(arg_48_0, 1)

				return slot_15_48.get_damage(returndmg, arg_48_0, slot_48_0, 1)
			end
		},
		E = {
			ignore_obj_radius = 1200,
			target_selector = 8,
			prediction = {
				delay = 0,
				range = 400,
				type = "self",
				hitchance = 0
			},
			cast_spell = {
				type = "pos",
				slot = _E,
				arg1 = function(arg_49_0)
					-- function 49
					local slot_49_0 = arg_49_0.ts.seg
					local slot_49_1 = arg_49_0.ts.obj

					return slot_49_0.endPos
				end
			},
			slot = player:spellSlot(_E),
			damage = function(arg_50_0, arg_50_1)
				-- function 50
				return 0
			end
		},
		R = {
			ignore_obj_radius = 800,
			target_selector = 8,
			prediction = {
				hitchance = 0,
				out_range = 1000,
				new_path = 0.5,
				boundingRadiusMod = 0,
				delay = 0.5,
				range = 500,
				type = "Circular",
				radius = 200,
				speed = math.huge,
				collision = {
					wall = false,
					minion = false,
					hero = false
				}
			},
			cast_spell = {
				type = "pos",
				slot = _R,
				arg1 = function(arg_51_0)
					-- function 51
					local slot_51_0 = arg_51_0.ts.seg
					local slot_51_1 = arg_51_0.ts.obj

					return slot_51_0.endPos
				end
			},
			slot = player:spellSlot(_R),
			damage = function(arg_52_0, arg_52_1)
				-- function 52
				if not arg_52_0 then
					return 0
				end

				spell_tb = {
					0.12,
					0.16,
					0.2
				}

				local slot_52_0 = arg_52_1 or player

				spell_level = slot_52_0:spellSlot(_R).level
				spell_Dmg = spell_tb[spell_level] or 0
				AD = common.GetTotalAD(slot_52_0)
				BonusAD = common.GetBonusAD(slot_52_0)
				spell_Dmg = spell_Dmg + 0.05 * BonusAD / 100
				BonusHP = arg_52_0.maxHealth - arg_52_0.health
				THISAD = 1.2 + 1 * slot_52_0.crit
				totalDmg = BonusHP * spell_Dmg + AD * THISAD
				returndmg = common.CAD(arg_52_0, totalDmg, slot_52_0) - common.jshfhp(arg_52_0, 1)

				return slot_15_48.get_damage(returndmg, arg_52_0, slot_52_0, 1)
			end
		}
	}
	slot_15_48.Gangplank = {
		Q = {
			ignore_obj_radius = 800,
			target_selector = 8,
			prediction = {
				delay = 0.25,
				range = 625,
				hitchance = 0,
				out_range = 625,
				new_path = 0.5,
				type = "InRange",
				speed = 2200
			},
			cast_spell = {
				type = "obj",
				slot = _Q,
				arg1 = function(arg_53_0)
					-- function 53
					return arg_53_0.ts.obj
				end
			},
			slot = player:spellSlot(_Q),
			damage = function(arg_54_0, arg_54_1)
				-- function 54
				if not arg_54_0 then
					return 0
				end

				local slot_54_0 = arg_54_1 or player

				spell_tb = {
					20,
					45,
					70,
					95,
					120
				}
				spell_level = slot_54_0:spellSlot(_Q).level
				spell_Dmg = spell_tb[spell_level] or 0
				totalDmg = spell_Dmg + common.GetTotalAD(slot_54_0) * 1
				returndmg = common.CAD(arg_54_0, totalDmg, slot_54_0) - common.jshfhp(arg_54_0, 1)

				return slot_15_48.get_damage(returndmg, arg_54_0, slot_54_0, 2)
			end
		},
		W = {
			ignore_obj_radius = 0,
			target_selector = 8,
			prediction = {
				hitchance = 0,
				range = 0,
				delay = 0.25,
				new_path = 0.5,
				type = "self",
				out_range = 1000
			},
			cast_spell = {
				type = "self",
				slot = _W
			},
			slot = player:spellSlot(_W),
			damage = function(arg_55_0, arg_55_1)
				-- function 55
				return 0
			end
		},
		E = {
			ignore_obj_radius = 1200,
			target_selector = 8,
			prediction = {
				hitchance = 0,
				out_range = 1000,
				new_path = 0.5,
				boundingRadiusMod = 0,
				delay = 0.8,
				range = 1000,
				type = "InRange",
				radius = 345,
				speed = math.huge,
				collision = {
					wall = false,
					minion = false,
					hero = false
				}
			},
			cast_spell = {
				type = "pos",
				slot = _E,
				arg1 = function(arg_56_0)
					-- function 56
					local slot_56_0 = arg_56_0.ts.seg
					local slot_56_1 = arg_56_0.ts.obj

					return slot_56_0.endPos
				end
			},
			slot = player:spellSlot(_E),
			damage = function(arg_57_0, arg_57_1)
				-- function 57
				return 0
			end
		},
		R = {
			ignore_obj_radius = 99999,
			target_selector = 8,
			prediction = {
				hitchance = 0,
				out_range = 99999,
				new_path = 0.5,
				boundingRadiusMod = 0,
				delay = 1,
				range = 9999,
				type = "Circular",
				radius = 100,
				speed = math.huge,
				collision = {
					wall = false,
					minion = false,
					hero = false
				}
			},
			cast_spell = {
				type = "pos",
				slot = _R,
				arg1 = function(arg_58_0)
					-- function 58
					local slot_58_0 = arg_58_0.ts.seg
					local slot_58_1 = arg_58_0.ts.obj

					return slot_58_0.endPos
				end
			},
			slot = player:spellSlot(_R),
			damage = function(arg_59_0, arg_59_1)
				-- function 59
				if not arg_59_0 then
					return 0
				end

				local slot_59_0 = arg_59_1 or player

				spell_tb = {
					40,
					70,
					100
				}
				spell_level = slot_59_0:spellSlot(_R).level
				spell_Dmg = spell_tb[spell_level] or 0
				totalDmg = spell_Dmg + common.GetTotalAP(slot_59_0) * 0.1
				returndmg = common.CAP(arg_59_0, totalDmg, slot_59_0) - common.jshfhp(arg_59_0, 1)

				return slot_15_48.get_damage(returndmg, arg_59_0, slot_59_0, 1)
			end
		}
	}
	slot_15_48.Aatrox = {
		Q = {
			ignore_obj_radius = 625,
			target_selector = 8,
			prediction = {
				hitchance = 0,
				ig = true,
				out_range = 625,
				new_path = 0.5,
				boundingRadiusMod = 1,
				width = 40,
				delay = 0.6,
				range = 625,
				type = "Linear",
				checkRange = true,
				speed = math.huge,
				collision = {
					wall = false,
					minion = false,
					hero = false
				}
			},
			cast_spell = {
				type = "pos",
				slot = _Q,
				arg1 = function(arg_60_0)
					-- function 60
					local slot_60_0 = arg_60_0.ts.seg
					local slot_60_1 = arg_60_0.ts.obj

					return slot_60_0.endPos
				end
			},
			slot = player:spellSlot(_Q),
			damage = function(arg_61_0, arg_61_1)
				-- function 61
				if not arg_61_0 then
					return 0
				end

				local slot_61_0 = arg_61_1 or player

				spell_tb = {
					10,
					25,
					40,
					55,
					70
				}
				spell_tb1 = {
					0.6,
					0.675,
					0.75,
					0.825,
					0.9
				}
				spell_level = slot_61_0:spellSlot(_Q).level
				spell_Dmg = spell_tb[spell_level] or 0
				spell_Dmg1 = spell_tb1[spell_level] or 0
				totalDmg = (spell_Dmg + common.GetTotalAD(slot_61_0) * spell_Dmg1) * 1.6
				returndmg = common.CAD(arg_61_0, totalDmg, slot_61_0) - common.jshfhp(arg_61_0, 1)

				return slot_15_48.get_damage(returndmg, arg_61_0, slot_61_0, 2)
			end
		},
		Q2 = {
			ignore_obj_radius = 475,
			target_selector = 8,
			prediction = {
				hitchance = 0,
				ig = true,
				out_range = 475,
				new_path = 0.5,
				boundingRadiusMod = 1,
				width = 40,
				delay = 0.6,
				range = 475,
				type = "Linear",
				checkRange = true,
				speed = math.huge,
				collision = {
					wall = false,
					minion = false,
					hero = false
				}
			},
			cast_spell = {
				type = "pos",
				slot = _Q,
				arg1 = function(arg_62_0)
					-- function 62
					local slot_62_0 = arg_62_0.ts.seg
					local slot_62_1 = arg_62_0.ts.obj

					return slot_62_0.endPos
				end
			},
			slot = player:spellSlot(_Q),
			damage = function(arg_63_0, arg_63_1)
				-- function 63
				if not arg_63_0 then
					return 0
				end

				local slot_63_0 = arg_63_1 or player

				spell_tb = {
					12.5,
					31.25,
					50,
					68.75,
					87.5
				}
				spell_tb1 = {
					0.75,
					0.84375,
					0.9375,
					1.03125,
					1.125
				}
				spell_level = slot_63_0:spellSlot(_Q).level
				spell_Dmg = spell_tb[spell_level] or 0
				spell_Dmg1 = spell_tb1[spell_level] or 0
				totalDmg = (spell_Dmg + common.GetTotalAD(slot_63_0) * spell_Dmg1) * 1.6
				returndmg = common.CAD(arg_63_0, totalDmg, slot_63_0) - common.jshfhp(arg_63_0, 1)

				return slot_15_48.get_damage(returndmg, arg_63_0, slot_63_0, 2)
			end
		},
		Q3 = {
			ignore_obj_radius = 475,
			target_selector = 8,
			prediction = {
				hitchance = 0,
				checkRange = true,
				ig = true,
				out_range = 475,
				boundingRadiusMod = 1,
				new_path = 0.5,
				delay = 0.6,
				range = 360,
				type = "Circular",
				radius = 80,
				speed = math.huge,
				collision = {
					wall = false,
					minion = false,
					hero = false
				}
			},
			cast_spell = {
				type = "pos",
				slot = _Q,
				arg1 = function(arg_64_0)
					-- function 64
					local slot_64_0 = arg_64_0.ts.seg
					local slot_64_1 = arg_64_0.ts.obj

					return slot_64_0.endPos
				end
			},
			slot = player:spellSlot(_Q),
			damage = function(arg_65_0, arg_65_1)
				-- function 65
				if not arg_65_0 then
					return 0
				end

				local slot_65_0 = arg_65_1 or player

				spell_tb = {
					15,
					37.5,
					60,
					82.5,
					105
				}
				spell_tb1 = {
					0.9,
					1.01,
					1.12,
					1.23,
					1.35
				}
				spell_level = slot_65_0:spellSlot(_Q).level
				spell_Dmg = spell_tb[spell_level] or 0
				spell_Dmg1 = spell_tb1[spell_level] or 0
				totalDmg = (spell_Dmg + common.GetTotalAD(slot_65_0) * spell_Dmg1) * 1.6
				returndmg = common.CAD(arg_65_0, totalDmg, slot_65_0) - common.jshfhp(arg_65_0, 1)

				return slot_15_48.get_damage(returndmg, arg_65_0, slot_65_0, 2)
			end
		},
		W = {
			ignore_obj_radius = 0,
			target_selector = 8,
			prediction = {
				hitchance = 0,
				new_path = 0.5,
				boundingRadiusMod = 0,
				width = 80,
				speed = 1800,
				delay = 0.25,
				range = 825,
				type = "Linear",
				out_range = 1000,
				collision = {
					wall = true,
					minion = true,
					hero = true
				}
			},
			cast_spell = {
				type = "pos",
				slot = _W,
				arg1 = function(arg_66_0)
					-- function 66
					local slot_66_0 = arg_66_0.ts.seg
					local slot_66_1 = arg_66_0.ts.obj

					return slot_66_0.endPos
				end
			},
			slot = player:spellSlot(_W),
			damage = function(arg_67_0, arg_67_1)
				-- function 67
				if not arg_67_0 then
					return 0
				end

				local slot_67_0 = arg_67_1 or player

				spell_tb = {
					30,
					40,
					50,
					60,
					70
				}
				spell_level = slot_67_0:spellSlot(_Q).level
				spell_Dmg = spell_tb[spell_level] or 0
				totalDmg = spell_Dmg + common.GetTotalAD(slot_67_0) * 0.4
				returndmg = common.CAD(arg_67_0, totalDmg, slot_67_0) - common.jshfhp(arg_67_0, 1)

				return slot_15_48.get_damage(returndmg, arg_67_0, slot_67_0, 2)
			end
		},
		E = {
			ignore_obj_radius = 0,
			target_selector = 8,
			prediction = {
				hitchance = 0,
				ig = true,
				new_path = 0.5,
				boundingRadiusMod = 0,
				width = 1,
				delay = 0.33,
				range = 300,
				type = "Linear",
				out_range = 300,
				speed = math.huge,
				collision = {
					wall = false,
					minion = false,
					hero = false
				}
			},
			cast_spell = {
				type = "pos",
				slot = _E,
				arg1 = function(arg_68_0)
					-- function 68
					local slot_68_0 = arg_68_0.ts.seg
					local slot_68_1 = arg_68_0.ts.obj

					return slot_68_0.endPos
				end
			},
			slot = player:spellSlot(_E),
			damage = function(arg_69_0, arg_69_1)
				-- function 69
				return 0
			end
		},
		R = {
			ignore_obj_radius = 99999,
			target_selector = 8,
			prediction = {
				delay = 0,
				range = 0,
				type = "self"
			},
			cast_spell = {
				type = "self",
				slot = _R
			},
			slot = player:spellSlot(_R),
			damage = function(arg_70_0, arg_70_1)
				-- function 70
				return 0
			end
		}
	}
	slot_15_48.LeeSin = {
		Q = {
			ignore_obj_radius = 625,
			target_selector = 8,
			prediction = {
				hitchance = 0,
				delay = 0.25,
				new_path = 0.5,
				boundingRadiusMod = 1,
				width = 60,
				speed = 1800,
				width2 = 77,
				range = 1100,
				type = "Linear",
				out_range = 1200,
				collision = {
					wall = true,
					minion = true,
					hero = true
				}
			},
			cast_spell = {
				type = "pos",
				slot = _Q,
				arg1 = function(arg_71_0)
					-- function 71
					local slot_71_0 = arg_71_0.ts.seg
					local slot_71_1 = arg_71_0.ts.obj

					return slot_71_0.endPos
				end
			},
			slot = player:spellSlot(_Q),
			damage = function(arg_72_0, arg_72_1)
				-- function 72
				if not arg_72_0 then
					return 0
				end

				local slot_72_0 = arg_72_1 or player

				spell_tb = {
					55,
					80,
					105,
					130,
					155
				}
				spell_level = slot_72_0:spellSlot(_Q).level
				spell_Dmg = spell_tb[spell_level] or 0
				totalDmg = spell_Dmg + common.GetBonusAD(slot_72_0) * 1.15
				returndmg = common.CAD(arg_72_0, totalDmg, slot_72_0) - common.jshfhp(arg_72_0, 1)

				return slot_15_48.get_damage(returndmg, arg_72_0, slot_72_0, 2)
			end
		},
		Q2 = {
			ignore_obj_radius = 1300,
			target_selector = 8,
			prediction = {
				delay = 0,
				range = 1300,
				type = "self"
			},
			cast_spell = {
				type = "self",
				slot = _Q
			},
			slot = player:spellSlot(_Q),
			damage = function(arg_73_0, arg_73_1, arg_73_2)
				-- function 73
				if not arg_73_0 then
					return 0
				end

				local slot_73_0 = arg_73_1 or player

				spell_tb = {
					55,
					80,
					105,
					130,
					155
				}
				spell_level = slot_73_0:spellSlot(_Q).level
				spell_Dmg = spell_tb[spell_level] or 0
				totalDmg = spell_Dmg + common.GetBonusAD(slot_73_0) * 1.1

				local slot_73_1 = preds.predict_hp(arg_73_0, 1)

				if arg_73_2 then
					slot_73_1 = slot_73_1 - arg_73_2
				end

				local slot_73_2 = (100 - slot_73_1 / arg_73_0.maxHealth * 100) / 100

				totalDmg = totalDmg + totalDmg * slot_73_2
				returndmg = common.CAD(arg_73_0, totalDmg, slot_73_0) - common.jshfhp(arg_73_0, 1)

				return slot_15_48.get_damage(returndmg, arg_73_0, slot_73_0, 2)
			end
		},
		W = {
			ignore_obj_radius = 0,
			target_selector = 8,
			prediction = {
				hitchance = 0,
				new_path = 0.5,
				boundingRadiusMod = 0,
				width = 80,
				speed = 1800,
				delay = 0.25,
				range = 700,
				type = "obj",
				out_range = 1000,
				collision = {
					wall = true,
					minion = true,
					hero = true
				}
			},
			cast_spell = {
				type = "obj",
				slot = _W,
				arg1 = function(arg_74_0)
					-- function 74
					return arg_74_0.ts.obj
				end
			},
			slot = player:spellSlot(_W),
			damage = function(arg_75_0, arg_75_1)
				-- function 75
				return 0
			end
		},
		E = {
			ignore_obj_radius = 0,
			target_selector = 8,
			prediction = {
				delay = 0.25,
				range = 350,
				dashRadius = 1,
				boundingRadiusModTarget = 0,
				boundingRadiusModSource = 0,
				type = "InRange",
				radius = 350
			},
			cast_spell = {
				type = "self",
				slot = _E
			},
			slot = player:spellSlot(_E),
			damage = function(arg_76_0, arg_76_1)
				-- function 76
				if not arg_76_0 then
					return 0
				end

				local slot_76_0 = arg_76_1 or player

				spell_tb = {
					35,
					60,
					85,
					110,
					135
				}
				spell_level = slot_76_0:spellSlot(_E).level
				spell_Dmg = spell_tb[spell_level] or 0
				totalDmg = spell_Dmg + common.GetBonusAD(slot_76_0) * 1
				returndmg = common.CAP(arg_76_0, totalDmg, slot_76_0) - common.jshfhp(arg_76_0, 0.5)

				return slot_15_48.get_damage(returndmg, arg_76_0, slot_76_0, 1)
			end
		},
		R = {
			ignore_obj_radius = 99999,
			target_selector = 8,
			prediction = {
				delay = 0.25,
				range = 375,
				type = "InRange"
			},
			cast_spell = {
				type = "self",
				slot = _R
			},
			slot = player:spellSlot(_R),
			damage = function(arg_77_0, arg_77_1)
				-- function 77
				if not arg_77_0 then
					return 0
				end

				local slot_77_0 = arg_77_1 or player

				spell_tb = {
					175,
					400,
					625
				}
				spell_level = slot_77_0:spellSlot(_R).level
				spell_Dmg = spell_tb[spell_level] or 0
				totalDmg = spell_Dmg + common.GetBonusAD(slot_77_0) * 2
				returndmg = common.CAP(arg_77_0, totalDmg, slot_77_0) - common.jshfhp(arg_77_0, 0.5)

				return slot_15_48.get_damage(returndmg, arg_77_0, slot_77_0, 2)
			end
		}
	}
	slot_15_48.Caitlyn = {
		Q = {
			target_selector = 8,
			ignore_obj_radius = 625,
			prediction = {
				hitchance = 0,
				new_path = 0.5,
				boundingRadiusMod = 1,
				width = 60,
				speed = 2200,
				delay = 0.625,
				range = 1200,
				type = "Linear",
				out_range = 1300,
				collision = {
					wall = true,
					minion = false,
					hero = false
				}
			},
			prediction_ig = {
				hitchance = 0,
				ig = true,
				new_path = 0.5,
				boundingRadiusMod = 1,
				width = 60,
				speed = 2200,
				delay = 0.625,
				range = 1200,
				type = "Linear",
				out_range = 1300,
				collision = {
					wall = true,
					minion = false,
					hero = false
				}
			},
			cast_spell = {
				type = "pos",
				slot = _Q,
				arg1 = function(arg_78_0)
					-- function 78
					local slot_78_0 = arg_78_0.ts.seg
					local slot_78_1 = arg_78_0.ts.obj

					return slot_78_0.endPos
				end
			},
			slot = player:spellSlot(_Q),
			damage = function(arg_79_0, arg_79_1)
				-- function 79
				if not arg_79_0 then
					return 0
				end

				local slot_79_0 = arg_79_1 or player

				spell_tb = {
					50,
					90,
					130,
					170,
					210
				}
				spell_tb_b = {
					1.25,
					1.45,
					1.65,
					1.85,
					2.05
				}
				spell_level = slot_79_0:spellSlot(_Q).level
				spell_Dmg = spell_tb[spell_level] or 0
				spell_Dmg_b = spell_tb_b[spell_level] or 0
				totalDmg = spell_Dmg + common.GetTotalAD(slot_79_0) * spell_Dmg_b
				returndmg = common.CAD(arg_79_0, totalDmg, slot_79_0) - common.jshfhp(arg_79_0, 1)

				return slot_15_48.get_damage(returndmg, arg_79_0, slot_79_0, 2)
			end
		},
		W = {
			target_selector = 8,
			ignore_obj_radius = 0,
			prediction = {
				hitchance = 0,
				out_range = 800,
				new_path = 0.5,
				boundingRadiusMod = 1,
				delay = 1,
				range = 800,
				type = "Circular",
				radius = 65,
				speed = math.huge,
				collision = {
					wall = false,
					minion = false,
					hero = false
				}
			},
			prediction_ig = {
				hitchance = 0,
				ig = true,
				out_range = 800,
				new_path = 0.5,
				boundingRadiusMod = 1,
				delay = 1,
				range = 800,
				type = "Circular",
				radius = 65,
				speed = math.huge,
				collision = {
					wall = false,
					minion = false,
					hero = false
				}
			},
			cast_spell = {
				type = "pos",
				slot = _W,
				arg1 = function(arg_80_0)
					-- function 80
					local slot_80_0 = arg_80_0.ts.seg
					local slot_80_1 = arg_80_0.ts.obj

					return slot_80_0.endPos
				end
			},
			slot = player:spellSlot(_W),
			damage = function(arg_81_0, arg_81_1)
				-- function 81
				return 0
			end
		},
		E = {
			ignore_obj_radius = 0,
			target_selector = 8,
			prediction = {
				hitchance = 0,
				new_path = 0.5,
				boundingRadiusMod = 1,
				width = 70,
				speed = 1600,
				delay = 0.15,
				range = 740,
				type = "Linear",
				out_range = 740,
				collision = {
					wall = true,
					minion = true,
					hero = true
				}
			},
			cast_spell = {
				type = "pos",
				slot = _E,
				arg1 = function(arg_82_0)
					-- function 82
					local slot_82_0 = arg_82_0.ts.seg
					local slot_82_1 = arg_82_0.ts.obj

					return slot_82_0.endPos
				end
			},
			slot = player:spellSlot(_E),
			damage = function(arg_83_0, arg_83_1)
				-- function 83
				if not arg_83_0 then
					return 0
				end

				local slot_83_0 = arg_83_1 or player

				spell_tb = {
					80,
					130,
					180,
					230,
					280
				}
				spell_level = slot_83_0:spellSlot(_E).level
				spell_Dmg = spell_tb[spell_level] or 0
				totalDmg = spell_Dmg + common.GetTotalAP(slot_83_0) * 0.8
				returndmg = common.CAP(arg_83_0, totalDmg, slot_83_0) - common.jshfhp(arg_83_0, 0.5)

				return slot_15_48.get_damage(returndmg, arg_83_0, slot_83_0, 1)
			end
		},
		R = {
			ignore_obj_radius = 99999,
			target_selector = 8,
			prediction = {
				delay = 1,
				range = 3500,
				type = "InRange",
				boundingRadiusMod = 1,
				width = 400,
				speed = 3200,
				collision = {
					wall = true,
					minion = false,
					hero = true
				}
			},
			cast_spell = {
				type = "self",
				slot = _R
			},
			slot = player:spellSlot(_R),
			damage = function(arg_84_0, arg_84_1)
				-- function 84
				if not arg_84_0 then
					return 0
				end

				local slot_84_0 = arg_84_1 or player

				spell_tb = {
					300,
					500,
					700
				}
				spell_level = slot_84_0:spellSlot(_R).level
				spell_Dmg = spell_tb[spell_level] or 0
				totalDmg = spell_Dmg + common.GetBonusAD(slot_84_0) * 1
				returndmg = common.CAP(arg_84_0, totalDmg, slot_84_0) - common.jshfhp(arg_84_0, 0.5)

				return slot_15_48.get_damage(returndmg, arg_84_0, slot_84_0, 2)
			end
		}
	}
	slot_15_48.Lulu_old = {
		Q = {
			ignore_obj_radius = 625,
			target_selector = 8,
			prediction = {
				hitchance = 0,
				new_path = 0.5,
				boundingRadiusMod = 1,
				width = 60,
				speed = 1500,
				delay = 0.25,
				range = 925,
				type = "Linear",
				out_range = 925,
				collision = {
					wall = true,
					minion = false,
					hero = false
				}
			},
			cast_spell = {
				type = "pos",
				slot = _Q,
				arg1 = function(arg_85_0)
					-- function 85
					local slot_85_0 = arg_85_0.ts.seg
					local slot_85_1 = arg_85_0.ts.obj

					return slot_85_0.endPos
				end
			},
			slot = player:spellSlot(_Q),
			damage = function(arg_86_0, arg_86_1)
				-- function 86
				if not arg_86_0 then
					return 0
				end

				local slot_86_0 = arg_86_1 or player

				spell_tb = {
					70,
					105,
					140,
					175,
					210
				}
				spell_level = slot_86_0:spellSlot(_Q).level
				spell_Dmg = spell_tb[spell_level] or 0
				totalDmg = spell_Dmg + common.GetTotalAP(slot_86_0) * 0.4
				returndmg = common.CAP(arg_86_0, totalDmg, slot_86_0) - common.jshfhp(arg_86_0, 1)

				return slot_15_48.get_damage(returndmg, arg_86_0, slot_86_0, 1)
			end
		},
		W = {
			ignore_obj_radius = 0,
			target_selector = 8,
			prediction = {
				hitchance = 0,
				range = 650,
				delay = 0.2419,
				new_path = 0.5,
				type = "InRange",
				out_range = 800
			},
			cast_spell = {
				type = "obj",
				slot = _W,
				arg1 = function(arg_87_0)
					-- function 87
					return arg_87_0.ts.obj
				end
			},
			slot = player:spellSlot(_W),
			damage = function(arg_88_0, arg_88_1)
				-- function 88
				return 0
			end
		},
		E = {
			ignore_obj_radius = 0,
			target_selector = 8,
			prediction = {
				hitchance = 0,
				range = 650,
				delay = 0,
				new_path = 0.5,
				type = "InRange",
				out_range = 650
			},
			cast_spell = {
				type = "obj",
				slot = _E,
				arg1 = function(arg_89_0)
					-- function 89
					return arg_89_0.ts.obj
				end
			},
			slot = player:spellSlot(_E),
			damage = function(arg_90_0, arg_90_1)
				-- function 90
				if not arg_90_0 then
					return 0
				end

				local slot_90_0 = arg_90_1 or player

				spell_tb = {
					80,
					120,
					160,
					200,
					240
				}
				spell_level = slot_90_0:spellSlot(_E).level
				spell_Dmg = spell_tb[spell_level] or 0
				totalDmg = spell_Dmg + common.GetTotalAP(slot_90_0) * 0.4
				returndmg = common.CAP(arg_90_0, totalDmg, slot_90_0) - common.jshfhp(arg_90_0, 0.5)

				return slot_15_48.get_damage(returndmg, arg_90_0, slot_90_0, 1)
			end
		},
		R = {
			ignore_obj_radius = 99999,
			target_selector = 8,
			prediction = {
				delay = 0,
				range = 900,
				type = "InRange"
			},
			cast_spell = {
				type = "obj",
				slot = _R,
				arg1 = function(arg_91_0)
					-- function 91
					return arg_91_0.ts.obj
				end
			},
			slot = player:spellSlot(_R),
			damage = function(arg_92_0, arg_92_1)
				-- function 92
				return 0
			end
		}
	}
	slot_15_48.Gwen = {
		Q = {
			ignore_obj_radius = 500,
			target_selector = 8,
			prediction = {
				hitchance = 0,
				new_path = 0.5,
				boundingRadiusMod = 1,
				width = 30,
				delay = 0.5,
				range = 450,
				type = "Linear",
				out_range = 500,
				speed = math.huge,
				collision = {
					wall = false,
					minion = false,
					hero = false
				}
			},
			cast_spell = {
				type = "pos",
				slot = _Q,
				arg1 = function(arg_93_0)
					-- function 93
					local slot_93_0 = arg_93_0.ts.seg
					local slot_93_1 = arg_93_0.ts.obj

					return slot_93_0.endPos
				end
			},
			slot = player:spellSlot(_Q),
			damage = function(arg_94_0, arg_94_1)
				-- function 94
				if not arg_94_0 then
					return 0
				end

				local slot_94_0 = arg_94_1 or player

				spell_tb = {
					8,
					10.75,
					13.5,
					16.25,
					19
				}
				spell_tb_b = {
					40,
					53.75,
					67.5,
					81.25,
					95
				}
				spell_level = slot_94_0:spellSlot(_Q).level
				spell_Dmg = spell_tb[spell_level] or 0
				spell_Dmg_b = spell_tb_b[spell_level] or 0
				totalDmg = spell_Dmg + common.GetTotalAP(slot_94_0) * 0.05
				totalDmg_b = spell_Dmg_b + common.GetTotalAP(slot_94_0) * 0.25
				returndmga = common.CAP(arg_94_0, totalDmg, slot_94_0) + totalDmg
				returndmgb = common.CAP(arg_94_0, totalDmg_b, slot_94_0)

				local slot_94_1 = 1

				if player.buff.gwenq then
					local slot_94_2 = player.buff.gwenq.stacks2 + 1

					returndmga = returndmga * slot_94_2
				end

				return slot_15_48.get_damage(returndmga + returndmgb, arg_94_0, slot_94_0, 1)
			end
		},
		W = {
			ignore_obj_radius = 0,
			target_selector = 8,
			prediction = {
				hitchance = 0,
				range = 480,
				delay = 0,
				new_path = 0.5,
				type = "InRange",
				out_range = 480
			},
			cast_spell = {
				type = "pos",
				slot = _W,
				arg1 = function(arg_95_0)
					-- function 95
					return player.pos
				end
			},
			slot = player:spellSlot(_W),
			damage = function(arg_96_0, arg_96_1)
				-- function 96
				return 0
			end
		},
		E = {
			ignore_obj_radius = 0,
			target_selector = 8,
			prediction = {
				hitchance = 0,
				range = 350,
				delay = 0,
				new_path = 0.5,
				type = "InRange",
				out_range = 350
			},
			cast_spell = {
				type = "pos",
				slot = _E,
				arg1 = function(arg_97_0)
					-- function 97
					return mousePos
				end
			},
			slot = player:spellSlot(_E),
			damage = function(arg_98_0, arg_98_1)
				-- function 98
				if not arg_98_0 then
					return 0
				end

				local slot_98_0 = arg_98_1 or player

				spell_tb = {
					10,
					15,
					20,
					25,
					30
				}
				spell_level = slot_98_0:spellSlot(_E).level
				spell_Dmg = spell_tb[spell_level] or 0
				totalDmg = spell_Dmg + common.GetTotalAP(slot_98_0) * 0.08
				returndmg = common.CAP(arg_98_0, totalDmg, slot_98_0)

				return slot_15_48.get_damage(returndmg, arg_98_0, slot_98_0, 1)
			end
		},
		R = {
			ignore_obj_radius = 99999,
			target_selector = 8,
			prediction = {
				hitchance = 0,
				new_path = 0.5,
				boundingRadiusMod = 1,
				width = 60,
				speed = 1800,
				delay = 0.5,
				range = 1200,
				type = "Linear",
				out_range = 500,
				collision = {
					wall = false,
					minion = false,
					hero = false
				}
			},
			cast_spell = {
				type = "pos",
				slot = _R,
				arg1 = function(arg_99_0)
					-- function 99
					local slot_99_0 = arg_99_0.ts.seg
					local slot_99_1 = arg_99_0.ts.obj

					return slot_99_0.endPos
				end
			},
			slot = player:spellSlot(_R),
			damage = function(arg_100_0, arg_100_1)
				-- function 100
				if not arg_100_0 then
					return 0
				end

				local slot_100_0 = arg_100_1 or player

				spell_tb = {
					30,
					55,
					80
				}
				spell_level = slot_100_0:spellSlot(_R).level
				spell_Dmg = spell_tb[spell_level] or 0
				totalDmg = spell_Dmg + common.GetTotalAP(slot_100_0) * 0.08
				returndmg = common.CAP(arg_100_0, totalDmg, slot_100_0)

				return slot_15_48.get_damage(returndmg, arg_100_0, slot_100_0, 1)
			end
		}
	}
	slot_15_48.Morgana = {
		Q = {
			ignore_obj_radius = 11300,
			target_selector = 8,
			prediction = {
				hitchance = 0,
				new_path = 0.5,
				boundingRadiusMod = 1,
				width = 70,
				speed = 1200,
				delay = 0.25,
				range = 1250,
				type = "Linear",
				out_range = 1250,
				collision = {
					wall = true,
					minion = true,
					hero = true
				}
			},
			cast_spell = {
				type = "pos",
				slot = _Q,
				arg1 = function(arg_101_0)
					-- function 101
					local slot_101_0 = arg_101_0.ts.seg
					local slot_101_1 = arg_101_0.ts.obj

					return slot_101_0.endPos
				end
			},
			slot = player:spellSlot(_Q),
			damage = function(arg_102_0, arg_102_1)
				-- function 102
				if not arg_102_0 then
					return 0
				end

				local slot_102_0 = arg_102_1 or player

				spell_tb = {
					80,
					135,
					190,
					245,
					300
				}
				spell_level = slot_102_0:spellSlot(_Q).level
				spell_Dmg = spell_tb[spell_level] or 0
				totalDmg = spell_Dmg + common.GetTotalAP(slot_102_0) * 0.9
				returndmg = common.CAP(arg_102_0, totalDmg, slot_102_0) + totalDmg

				return slot_15_48.get_damage(returndmg, arg_102_0, slot_102_0, 1)
			end
		},
		Q_collision = {
			ignore_obj_radius = 11300,
			target_selector = 8,
			prediction = {
				hitchance = 0,
				new_path = 0.5,
				boundingRadiusMod = 0,
				width = 50,
				speed = 950,
				delay = 0.25,
				range = 1250,
				type = "Linear",
				out_range = 1250,
				collision = {
					wall = true,
					minion = true,
					hero = true
				}
			},
			cast_spell = {
				type = "pos",
				slot = _Q,
				arg1 = function(arg_103_0)
					-- function 103
					local slot_103_0 = arg_103_0.ts.seg
					local slot_103_1 = arg_103_0.ts.obj

					return slot_103_0.endPos
				end
			},
			slot = player:spellSlot(_Q),
			damage = function(arg_104_0, arg_104_1)
				-- function 104
				if not arg_104_0 then
					return 0
				end

				local slot_104_0 = arg_104_1 or player

				spell_tb = {
					80,
					135,
					190,
					245,
					300
				}
				spell_level = slot_104_0:spellSlot(_Q).level
				spell_Dmg = spell_tb[spell_level] or 0
				totalDmg = spell_Dmg + common.GetTotalAP(slot_104_0) * 0.9
				returndmg = common.CAP(arg_104_0, totalDmg, slot_104_0) + totalDmg

				return slot_15_48.get_damage(returndmg, arg_104_0, slot_104_0, 1)
			end
		},
		W = {
			ignore_obj_radius = 0,
			target_selector = 8,
			prediction = {
				hitchance = 0,
				radius = 270,
				new_path = 0.5,
				boundingRadiusMod = 1,
				speed = 1200,
				delay = 0.21,
				range = 900,
				type = "Circular",
				out_range = 900
			},
			cast_spell = {
				type = "pos",
				slot = _W,
				arg1 = function(arg_105_0)
					-- function 105
					local slot_105_0 = arg_105_0.ts.seg
					local slot_105_1 = arg_105_0.ts.obj

					return slot_105_0.endPos
				end
			},
			slot = player:spellSlot(_W),
			damage = function(arg_106_0, arg_106_1)
				-- function 106
				if not arg_106_0 then
					return 0
				end

				local slot_106_0 = arg_106_1 or player

				spell_tb = {
					6,
					11,
					16,
					21,
					26
				}
				spell_level = slot_106_0:spellSlot(_Q).level
				spell_Dmg = spell_tb[spell_level] or 0
				totalDmg = spell_Dmg + common.GetTotalAP(slot_106_0) * 0.07
				returndmg = common.CAP(arg_106_0, totalDmg, slot_106_0) + totalDmg

				return slot_15_48.get_damage(returndmg, arg_106_0, slot_106_0, 1)
			end
		},
		E = {
			ignore_obj_radius = 0,
			target_selector = 8,
			prediction = {
				hitchance = 0,
				range = 800,
				delay = 0,
				new_path = 0.5,
				type = "obj",
				out_range = 800
			},
			cast_spell = {
				type = "obj",
				slot = _E,
				arg1 = function(arg_107_0)
					-- function 107
					return arg_107_0.ts.obj
				end
			},
			slot = player:spellSlot(_E),
			damage = function(arg_108_0, arg_108_1)
				-- function 108
				if not arg_108_0 then
					return 0
				end

				return 0
			end
		},
		R = {
			ignore_obj_radius = 575,
			target_selector = 8,
			prediction = {
				delay = 0.35,
				range = 625,
				dashRadius = 0,
				boundingRadiusModTarget = 0,
				boundingRadiusModSource = 0,
				type = "InRange",
				radius = 625
			},
			cast_spell = {
				type = "obj",
				slot = _R,
				arg1 = function(arg_109_0)
					-- function 109
					local slot_109_0 = arg_109_0.ts.seg

					return arg_109_0.ts.obj
				end
			},
			slot = player:spellSlot(_R),
			damage = function(arg_110_0, arg_110_1)
				-- function 110
				if not arg_110_0 then
					return 0
				end

				local slot_110_0 = arg_110_1 or player

				spell_tb = {
					150,
					225,
					300
				}
				spell_level = slot_110_0:spellSlot(_R).level
				spell_Dmg = spell_tb[spell_level] or 0
				totalDmg = spell_Dmg + common.GetTotalAP(slot_110_0) * 0.7
				returndmg = common.CAP(arg_110_0, totalDmg, slot_110_0)

				return slot_15_48.get_damage(returndmg, arg_110_0, slot_110_0, 1)
			end
		}
	}
	slot_15_48.Aphelios = {
		Q = {
			ignore_obj_radius = 1450,
			target_selector = 8,
			prediction = {
				hitchance = 0,
				new_path = 0.5,
				boundingRadiusMod = 1,
				width = 60,
				speed = 1850,
				delay = 0.4,
				range = 1450,
				type = "Linear",
				out_range = 1450,
				collision = {
					wall = true,
					minion = true,
					hero = true
				}
			},
			cast_spell = {
				type = "pos",
				slot = _Q,
				arg1 = function(arg_111_0)
					-- function 111
					local slot_111_0 = arg_111_0.ts.seg
					local slot_111_1 = arg_111_0.ts.obj

					return slot_111_0.endPos
				end
			},
			slot = player:spellSlot(_Q),
			damage = function(arg_112_0, arg_112_1)
				-- function 112
				if not arg_112_0 then
					return 0
				end

				local slot_112_0 = arg_112_1 or player
				local slot_112_1 = math.min(7, math.max(1, math.ceil(player.level / 2))) - 1
				local slot_112_2 = 60 + 16.6666 * slot_112_1 + common.GetBonusAD(slot_112_0) * (0.42 + 0.03 * slot_112_1) + common.GetTotalAP(slot_112_0)
				local slot_112_3 = 15 + common.GetBonusAD(slot_112_0) * 0.2

				returndmg = common.CAD(arg_112_0, slot_112_2 + slot_112_3, slot_112_0) + common.CalculateAADamage(arg_112_0)

				local slot_112_4 = (slot_112_0.attackSpeedMod - 1) * 100
				local slot_112_5 = 6 + math.floor(slot_112_4 / 100 / 3 * 10 + 0.1)
				local slot_112_6 = 10 + 3.3333 * slot_112_1 + common.GetBonusAD(slot_112_0) * (0.21 + 0.015 * slot_112_1) * slot_112_5

				returndmg_red = common.CAD(arg_112_0, slot_112_6, slot_112_0)

				local slot_112_7 = 50 + 10 * slot_112_1 + common.GetBonusAD(slot_112_0) * (0.26 + 0.15 * slot_112_1) + common.GetTotalAP(slot_112_0) * 0.7

				returndmg_z = common.CAP(arg_112_0, slot_112_7, slot_112_0)

				local slot_112_8 = 25 + 6.6666 * slot_112_1 + common.GetBonusAD(slot_112_0) * (0.56 + 0.04 * slot_112_1) + common.GetTotalAP(slot_112_0) * 0.7

				returndmg_p = common.CAD(arg_112_0, slot_112_8, slot_112_0)

				local slot_112_9 = 25 + 10 * slot_112_1 + common.GetBonusAD(slot_112_0) * (0.35 + 0.025 * slot_112_1) + common.GetTotalAP(slot_112_0) * 0.5

				returndmg_p = common.CAD(arg_112_0, slot_112_9, slot_112_0)

				return slot_15_48.get_damage(returndmg, arg_112_0, slot_112_0, 2)
			end
		},
		W = {
			ignore_obj_radius = 0,
			target_selector = 8,
			prediction = {
				delay = 0,
				range = 0
			},
			slot = player:spellSlot(_W),
			damage = function(arg_113_0, arg_113_1)
				-- function 113
				if not arg_113_0 then
					return 0
				end

				return 0
			end
		},
		E = {
			ignore_obj_radius = 0,
			target_selector = 8,
			prediction = {},
			cast_spell = {},
			slot = player:spellSlot(_E),
			damage = function(arg_114_0, arg_114_1)
				-- function 114
				if not arg_114_0 then
					return 0
				end

				return 0
			end
		},
		R = {
			ignore_obj_radius = 575,
			target_selector = 8,
			prediction = {
				hitchance = 0,
				new_path = 0.5,
				boundingRadiusMod = 0,
				width = 50,
				speed = 1000,
				delay = 0.6,
				range = 1300,
				type = "Linear",
				out_range = 1450,
				collision = {
					wall = true,
					minion = true,
					hero = true
				}
			},
			cast_spell = {
				type = "obj",
				slot = _R,
				arg1 = function(arg_115_0)
					-- function 115
					local slot_115_0 = arg_115_0.ts.seg

					return arg_115_0.ts.obj
				end
			},
			slot = player:spellSlot(_R),
			damage = function(arg_116_0, arg_116_1)
				-- function 116
				if not arg_116_0 then
					return 0
				end

				local slot_116_0 = arg_116_1 or player

				spell_tb = {
					150,
					225,
					300
				}
				spell_level = slot_116_0:spellSlot(_R).level
				spell_Dmg = spell_tb[spell_level] or 0
				totalDmg = spell_Dmg + common.GetTotalAP(slot_116_0) * 0.7
				returndmg = common.CAP(arg_116_0, totalDmg, slot_116_0)

				return slot_15_48.get_damage(returndmg, arg_116_0, slot_116_0, 1)
			end
		}
	}
	slot_15_48.Sett = {
		Q = {
			ignore_obj_radius = 1450,
			target_selector = 8,
			prediction = {
				type = "InRange",
				range = player.attackRange
			},
			cast_spell = {},
			slot = player:spellSlot(_Q),
			damage = function(arg_117_0, arg_117_1)
				-- function 117
				if not arg_117_0 then
					return 0
				end

				local slot_117_0 = arg_117_1 or player
				local slot_117_1 = {
					10,
					20,
					30,
					40,
					50
				}
				local slot_117_2 = slot_117_0:spellSlot(0).level
				local slot_117_3 = slot_117_1[slot_117_2] or 0
				local slot_117_4 = common.GetTotalAD(slot_117_0)
				local slot_117_5 = math.floor(slot_117_4 / 100)
				local slot_117_6 = slot_117_3 + arg_117_0.maxHealth * ((slot_117_2 + slot_117_5) / 100)
				local slot_117_7 = common.CAD(arg_117_0, slot_117_6, slot_117_0)

				return slot_15_48.get_damage(slot_117_7, arg_117_0, slot_117_0, 2)
			end
		},
		W = {
			ignore_obj_radius = 0,
			target_selector = 8,
			prediction = {
				hitchance = 0,
				new_path = 0.5,
				boundingRadiusMod = 1,
				width = 90,
				delay = 0.75,
				range = 790,
				type = "Linear",
				out_range = 790,
				speed = math.huge,
				collision = {
					wall = false,
					minion = false,
					hero = false
				}
			},
			slot = player:spellSlot(_W),
			damage = function(arg_118_0, arg_118_1)
				-- function 118
				if not arg_118_0 then
					return 0
				end

				local slot_118_0 = arg_118_1 or player
				local slot_118_1 = ({
					80,
					100,
					120,
					140,
					160
				})[slot_118_0:spellSlot(1).level] or 0
				local slot_118_2 = common.GetBonusAD(slot_118_0) / 100 * 0.01

				return slot_118_1 + slot_118_0.mana * (0.25 + slot_118_2)
			end
		},
		E = {
			ignore_obj_radius = 0,
			target_selector = 8,
			prediction = {
				hitchance = 0,
				new_path = 0.5,
				boundingRadiusMod = 1,
				width = 90,
				delay = 0.3,
				range = 490,
				type = "Linear",
				out_range = 490,
				speed = math.huge,
				collision = {
					wall = false,
					minion = true,
					hero = true
				}
			},
			cast_spell = {},
			slot = player:spellSlot(_E),
			damage = function(arg_119_0, arg_119_1)
				-- function 119
				if not arg_119_0 then
					return 0
				end

				local slot_119_0 = arg_119_1 or player
				local slot_119_1 = (({
					50,
					70,
					90,
					110,
					130
				})[slot_119_0:spellSlot(2).level] or 0) + common.GetTotalAD(slot_119_0) * 0.6
				local slot_119_2 = common.CAD(arg_119_0, slot_119_1, slot_119_0)

				return slot_15_48.get_damage(slot_119_2, arg_119_0, slot_119_0, 2)
			end
		},
		R = {
			ignore_obj_radius = 575,
			target_selector = 8,
			prediction = {
				delay = 0,
				range = 400,
				type = "InRange"
			},
			cast_spell = {
				type = "obj",
				slot = _R,
				arg1 = function(arg_120_0)
					-- function 120
					local slot_120_0 = arg_120_0.ts.seg

					return arg_120_0.ts.obj
				end
			},
			slot = player:spellSlot(_R),
			damage = function(arg_121_0, arg_121_1)
				-- function 121
				if not arg_121_0 then
					return 0
				end

				local slot_121_0 = arg_121_1 or player
				local slot_121_1 = {
					200,
					300,
					400
				}
				local slot_121_2 = slot_121_0:spellSlot(3).level
				local slot_121_3 = slot_121_1[slot_121_2] or 0
				local slot_121_4 = arg_121_0.maxHealth - 650
				local slot_121_5 = (3 + slot_121_2) * 0.1
				local slot_121_6 = common.GetBonusAD(slot_121_0) * slot_121_5
				local slot_121_7 = slot_121_3 + (common.GetBonusAD(slot_121_0) + slot_121_6)
				local slot_121_8 = common.CAD(arg_121_0, slot_121_7, slot_121_0)

				return slot_15_48.get_damage(slot_121_8, arg_121_0, slot_121_0, 2)
			end
		}
	}
	slot_15_48.Gnar = {
		Q = {
			ignore_obj_radius = 1450,
			target_selector = 8,
			prediction = {
				hitchance = 0,
				new_path = 0.5,
				boundingRadiusMod = 1,
				width = 30,
				speed = 2500,
				delay = 0.25,
				range = 1125,
				type = "Linear",
				out_range = 1125,
				collision = {
					wall = true,
					minion = true,
					hero = true
				}
			},
			cast_spell = {},
			slot = player:spellSlot(_Q),
			damage = function(arg_122_0, arg_122_1)
				-- function 122
				if not arg_122_0 then
					return 0
				end

				local slot_122_0 = arg_122_1 or player
				local slot_122_1 = (({
					5,
					45,
					85,
					125,
					165
				})[slot_122_0:spellSlot(0).level] or 0) + common.GetTotalAD(slot_122_0) * 1.15
				local slot_122_2 = common.CAD(arg_122_0, slot_122_1, slot_122_0)

				return slot_15_48.get_damage(slot_122_2, arg_122_0, slot_122_0, 2)
			end
		},
		Q1 = {
			ignore_obj_radius = 1450,
			target_selector = 8,
			prediction = {
				hitchance = 0,
				new_path = 0.5,
				boundingRadiusMod = 1,
				width = 90,
				speed = 2100,
				delay = 0.5,
				range = 1150,
				type = "Linear",
				out_range = 1150,
				collision = {
					wall = true,
					minion = true,
					hero = true
				}
			},
			cast_spell = {},
			slot = player:spellSlot(_Q),
			damage = function(arg_123_0, arg_123_1)
				-- function 123
				if not arg_123_0 then
					return 0
				end

				local slot_123_0 = arg_123_1 or player
				local slot_123_1 = (({
					5,
					45,
					85,
					125,
					165
				})[slot_123_0:spellSlot(0).level] or 0) + common.GetTotalAD(slot_123_0)
				local slot_123_2 = common.CAD(arg_123_0, slot_123_1, slot_123_0)

				return slot_15_48.get_damage(slot_123_2, arg_123_0, slot_123_0, 2)
			end
		},
		W = {
			ignore_obj_radius = 0,
			target_selector = 8,
			prediction = {
				range = 0
			},
			slot = player:spellSlot(_W),
			damage = function(arg_124_0, arg_124_1)
				-- function 124
				if not arg_124_0 then
					return 0
				end

				local slot_124_0 = arg_124_1 or player
				local slot_124_1 = {
					0,
					10,
					20,
					30,
					40
				}
				local slot_124_2 = {
					0.06,
					0.08,
					0.1,
					0.12,
					0.14
				}
				local slot_124_3 = slot_124_0:spellSlot(1).level
				local slot_124_4 = slot_124_1[slot_124_3] or 0
				local slot_124_5 = slot_124_2[slot_124_3] or 0
				local slot_124_6 = common.GetTotalAP(slot_124_0)
				local slot_124_7 = slot_124_4 + arg_124_0.maxHealth * slot_124_5 + slot_124_6
				local slot_124_8 = common.CAP(arg_124_0, slot_124_7, slot_124_0)

				return slot_15_48.get_damage(slot_124_8, arg_124_0, slot_124_0, 1)
			end
		},
		W1 = {
			ignore_obj_radius = 0,
			target_selector = 8,
			prediction = {
				hitchance = 0,
				new_path = 0.5,
				boundingRadiusMod = 1,
				width = 100,
				delay = 0.6,
				range = 550,
				type = "Linear",
				out_range = 550,
				speed = math.huge,
				collision = {
					wall = true,
					minion = true,
					hero = true
				}
			},
			slot = player:spellSlot(_W),
			damage = function(arg_125_0, arg_125_1)
				-- function 125
				if not arg_125_0 then
					return 0
				end

				local slot_125_0 = arg_125_1 or player
				local slot_125_1 = (({
					25,
					55,
					85,
					115,
					145
				})[slot_125_0:spellSlot(1).level] or 0) + common.GetTotalAD(slot_125_0)
				local slot_125_2 = common.CAD(arg_125_0, slot_125_1, slot_125_0)

				return slot_15_48.get_damage(slot_125_2, arg_125_0, slot_125_0, 2)
			end
		},
		E = {
			ignore_obj_radius = 0,
			target_selector = 8,
			prediction = {
				hitchance = 0,
				new_path = 0.5,
				boundingRadiusMod = 1,
				width = 75,
				delay = 0,
				range = 475,
				type = "Linear",
				out_range = 475,
				speed = math.huge,
				collision = {
					wall = false,
					minion = true,
					hero = true
				}
			},
			cast_spell = {},
			slot = player:spellSlot(_E),
			damage = function(arg_126_0, arg_126_1)
				-- function 126
				if not arg_126_0 then
					return 0
				end

				local slot_126_0 = arg_126_1 or player
				local slot_126_1 = (({
					50,
					85,
					120,
					155,
					190
				})[slot_126_0:spellSlot(2).level] or 0) + arg_126_0.maxHealth * 0.06
				local slot_126_2 = common.CAD(arg_126_0, slot_126_1, slot_126_0)

				return slot_15_48.get_damage(slot_126_2, arg_126_0, slot_126_0, 2)
			end
		},
		E1 = {
			ignore_obj_radius = 0,
			target_selector = 8,
			prediction = {
				hitchance = 0,
				out_range = 675,
				new_path = 0.5,
				boundingRadiusMod = 1,
				delay = 0.25,
				range = 675,
				type = "Circular",
				radius = 375,
				speed = math.huge,
				collision = {
					wall = false,
					minion = false,
					hero = false
				}
			},
			cast_spell = {},
			slot = player:spellSlot(_E),
			damage = function(arg_127_0, arg_127_1)
				-- function 127
				if not arg_127_0 then
					return 0
				end

				local slot_127_0 = arg_127_1 or player
				local slot_127_1 = (({
					80,
					115,
					150,
					185,
					220
				})[slot_127_0:spellSlot(2).level] or 0) + arg_127_0.maxHealth * 0.06
				local slot_127_2 = common.CAD(arg_127_0, slot_127_1, slot_127_0)

				return slot_15_48.get_damage(slot_127_2, arg_127_0, slot_127_0, 2)
			end
		},
		R = {
			ignore_obj_radius = 465,
			target_selector = 8,
			prediction = {
				hitchance = 0,
				ig = true,
				out_range = 465,
				new_path = 0.5,
				boundingRadiusMod = 1,
				delay = 0.25,
				range = 465,
				type = "Circular",
				radius = 465,
				speed = math.huge,
				collision = {
					wall = false,
					minion = false,
					hero = false
				}
			},
			cast_spell = {
				type = "pos",
				slot = _R,
				arg1 = function(arg_128_0)
					-- function 128
					local slot_128_0 = arg_128_0.ts.seg

					return arg_128_0.ts.obj
				end
			},
			slot = player:spellSlot(_R),
			damage = function(arg_129_0, arg_129_1)
				-- function 129
				if not arg_129_0 then
					return 0
				end

				local slot_129_0 = arg_129_1 or player
				local slot_129_1 = (({
					200,
					300,
					400
				})[slot_129_0:spellSlot(3).level] or 0) + common.GetBonusAD(slot_129_0) * 0.5 + common.GetTotalAP(slot_129_0)
				local slot_129_2 = common.CAD(arg_129_0, slot_129_1, slot_129_0)

				return slot_15_48.get_damage(slot_129_2, arg_129_0, slot_129_0, 2)
			end
		}
	}
	slot_15_48.Xayah = {
		Q = {
			ignore_obj_radius = 1450,
			target_selector = 8,
			prediction = {
				hitchance = 0,
				new_path = 0.5,
				boundingRadiusMod = 1,
				width = 50,
				speed = 1700,
				delay = 0.25,
				range = 1099,
				type = "Linear",
				out_range = 1099,
				collision = {
					wall = true,
					minion = false,
					hero = false
				}
			},
			cast_spell = {},
			slot = player:spellSlot(_Q),
			damage = function(arg_130_0, arg_130_1)
				-- function 130
				if not arg_130_0 then
					return 0
				end

				local slot_130_0 = arg_130_1 or player
				local slot_130_1 = (({
					45,
					60,
					75,
					90,
					105
				})[slot_130_0:spellSlot(0).level] or 0) + common.GetBonusAD(slot_130_0) * 0.5
				local slot_130_2 = common.CAD(arg_130_0, slot_130_1, slot_130_0)

				return slot_15_48.get_damage(slot_130_2, arg_130_0, slot_130_0, 2)
			end
		},
		W = {
			ignore_obj_radius = 0,
			target_selector = 8,
			prediction = {
				speed = 3000,
				range = 0
			},
			slot = player:spellSlot(_W),
			damage = function(arg_131_0, arg_131_1)
				-- function 131
				if not arg_131_0 then
					return 0
				end

				local slot_131_0 = arg_131_1 or player

				return common.CalculateAADamage(arg_131_0, slot_131_0)
			end
		},
		E = {
			ignore_obj_radius = 0,
			target_selector = 8,
			prediction = {
				hitchance = 0,
				ig = true,
				new_path = 0.5,
				boundingRadiusMod = 0,
				width = 65,
				speed = 4000,
				delay = 0,
				range = 9999,
				type = "Linear",
				out_range = 9999,
				collision = {
					wall = false,
					minion = false,
					hero = true
				}
			},
			cast_spell = {},
			slot = player:spellSlot(_E),
			damage = function(arg_132_0, arg_132_1)
				-- function 132
				if not arg_132_0 then
					return 0
				end

				local slot_132_0 = arg_132_1 or player
				local slot_132_1 = (({
					55,
					65,
					75,
					85,
					95
				})[slot_132_0:spellSlot(2).level] or 0) + common.GetBonusAD(slot_132_0) * 0.6
				local slot_132_2 = common.CAD(arg_132_0, slot_132_1, slot_132_0)

				return slot_15_48.get_damage(slot_132_2, arg_132_0, slot_132_0, 2)
			end
		},
		R = {
			ignore_obj_radius = 1060,
			target_selector = 8,
			prediction = {
				hitchance = 0,
				new_path = 0.5,
				boundingRadiusMod = 1,
				width = 1,
				delay = 0,
				range = 1060,
				type = "Linear",
				out_range = 1060,
				speed = math.huge,
				collision = {
					wall = false,
					minion = false,
					hero = false
				}
			},
			cast_spell = {},
			slot = player:spellSlot(_R),
			damage = function(arg_133_0, arg_133_1)
				-- function 133
				if not arg_133_0 then
					return 0
				end

				local slot_133_0 = arg_133_1 or player
				local slot_133_1 = (({
					200,
					300,
					400
				})[slot_133_0:spellSlot(3).level] or 0) + common.GetBonusAD(slot_133_0) * 1
				local slot_133_2 = common.CAD(arg_133_0, slot_133_1, slot_133_0)

				return slot_15_48.get_damage(slot_133_2, arg_133_0, slot_133_0, 2)
			end
		}
	}
	slot_15_48.Syndra = {
		Q = {
			ignore_obj_radius = 1450,
			target_selector = 8,
			prediction = {
				hitchance = 0,
				radius = 50,
				new_path = 0.5,
				boundingRadiusMod = 0,
				delay = 0.6,
				range = 790,
				type = "Circular",
				out_range = 1099,
				speed = math.huge
			},
			cast_spell = {},
			slot = player:spellSlot(_Q),
			damage = function(arg_134_0, arg_134_1)
				-- function 134
				if not arg_134_0 then
					return 0
				end

				local slot_134_0 = arg_134_1 or player
				local slot_134_1 = (({
					75,
					110,
					145,
					180,
					215
				})[slot_134_0:spellSlot(0).level] or 0) + common.GetTotalAP(slot_134_0) * 0.7
				local slot_134_2 = common.CAP(arg_134_0, slot_134_1, slot_134_0)

				return slot_15_48.get_damage(slot_134_2, arg_134_0, slot_134_0, 1)
			end
		},
		W = {
			ignore_obj_radius = 0,
			target_selector = 8,
			prediction = {
				delay = 0.45,
				range = 925,
				radius = 50,
				boundingRadiusMod = 0,
				type = "Circular",
				speed = math.huge
			},
			slot = player:spellSlot(_W),
			damage = function(arg_135_0, arg_135_1)
				-- function 135
				if not arg_135_0 then
					return 0
				end

				local slot_135_0 = arg_135_1 or player
				local slot_135_1 = {
					70,
					105,
					140,
					175,
					210
				}
				local slot_135_2 = 0
				local slot_135_3 = slot_135_0.buff.syndrapassivestacks

				if slot_135_3 then
					slot_135_2 = slot_135_3.stacks2
				end

				local slot_135_4 = (slot_135_1[slot_135_0:spellSlot(1).level] or 0) + common.GetTotalAP(slot_135_0) * 0.65
				local slot_135_5 = common.CAP(arg_135_0, slot_135_4, slot_135_0)
				local slot_135_6 = 0

				if slot_135_2 >= 60 then
					slot_135_6 = slot_135_5 * (0.12 + common.GetTotalAP(slot_135_0) * 0.0002)
				end

				return slot_15_48.get_damage(slot_135_5, arg_135_0, slot_135_0, 1) + slot_135_6
			end
		},
		W1 = {
			ignore_obj_radius = 0,
			target_selector = 8,
			prediction = {
				delay = 0.25,
				range = 925,
				radius = 50,
				boundingRadiusMod = 0,
				type = "Circular",
				speed = 1450
			},
			slot = player:spellSlot(_W),
			damage = function(arg_136_0, arg_136_1)
				-- function 136
				if not arg_136_0 then
					return 0
				end

				local slot_136_0 = arg_136_1 or player
				local slot_136_1 = (({
					70,
					110,
					150,
					190,
					230
				})[slot_136_0:spellSlot(1).level] or 0) + common.GetTotalAP(slot_136_0) * 0.7
				local slot_136_2 = common.CAP(arg_136_0, slot_136_1, slot_136_0)

				return slot_15_48.get_damage(slot_136_2, arg_136_0, slot_136_0, 1)
			end
		},
		E = {
			ignore_obj_radius = 0,
			target_selector = 8,
			prediction = {
				hitchance = 0,
				new_path = 0.5,
				boundingRadiusMod = 0,
				width = 60,
				speed = 4000,
				delay = 0,
				range = 9999,
				type = "Linear",
				out_range = 9999,
				collision = {
					wall = false,
					minion = false,
					hero = true
				}
			},
			cast_spell = {},
			slot = player:spellSlot(_E),
			damage = function(arg_137_0, arg_137_1)
				-- function 137
				if not arg_137_0 then
					return 0
				end

				local slot_137_0 = arg_137_1 or player
				local slot_137_1 = (({
					55,
					65,
					75,
					85,
					95
				})[slot_137_0:spellSlot(2).level] or 0) + common.GetBonusAD(slot_137_0) * 0.6
				local slot_137_2 = common.CAD(arg_137_0, slot_137_1, slot_137_0)

				return slot_15_48.get_damage(slot_137_2, arg_137_0, slot_137_0, 1)
			end
		},
		R = {
			ignore_obj_radius = 1060,
			target_selector = 8,
			prediction = {
				dashRadius = 0,
				width = 75,
				speed = 1100,
				delay = 0,
				range = 675,
				boundingRadiusModTarget = 0,
				boundingRadiusModSource = 0,
				type = "InRange",
				radius = 675,
				collision = {
					wall = true
				}
			},
			cast_spell = {},
			slot = player:spellSlot(_R),
			damage = function(arg_138_0, arg_138_1)
				-- function 138
				if not arg_138_0 then
					return 0
				end

				local slot_138_0 = arg_138_1 or player
				local slot_138_1 = 0
				local slot_138_2 = slot_138_0.buff.syndrapassivestacks

				if slot_138_2 then
					slot_138_1 = slot_138_2.stacks2
				end

				local slot_138_3 = {
					100,
					140,
					180
				}
				local slot_138_4 = slot_138_0:spellSlot(3).level
				local slot_138_5 = common.GetTotalAP(slot_138_0) * 0.17
				local slot_138_6 = (slot_138_3[slot_138_4] or 0) + slot_138_5
				local slot_138_7 = common.CAP(arg_138_0, slot_138_6, slot_138_0)
				local slot_138_8 = slot_15_48.get_damage(slot_138_7, arg_138_0, slot_138_0, 1)

				if slot_138_1 >= 100 and arg_138_0.health - slot_138_8 * 3 < arg_138_0.maxHealth * 0.15 then
					return 99999
				end

				return slot_138_8 * 3
			end
		}
	}
	slot_15_48.Akshan = {
		Q = {
			ignore_obj_radius = 850,
			target_selector = 8,
			prediction = {
				hitchance = 0,
				new_path = 0.5,
				boundingRadiusMod = 1,
				width = 60,
				speed = 1500,
				delay = 0.25,
				range = 800,
				type = "Linear",
				out_range = 850,
				collision = {
					wall = false,
					minion = true,
					hero = true
				}
			},
			cast_spell = {},
			slot = player:spellSlot(_Q),
			damage = function(arg_139_0, arg_139_1)
				-- function 139
				if not arg_139_0 then
					return 0
				end

				local slot_139_0 = arg_139_1 or player
				local slot_139_1 = (({
					5,
					25,
					45,
					65,
					85
				})[slot_139_0:spellSlot(0).level] or 0) + common.GetTotalAD(slot_139_0) * 0.8
				local slot_139_2 = common.CAD(arg_139_0, slot_139_1, slot_139_0)

				return slot_15_48.get_damage(slot_139_2, arg_139_0, slot_139_0, 2)
			end
		},
		W = {
			ignore_obj_radius = 0,
			target_selector = 8,
			prediction = {
				delay = 0.5,
				range = 800,
				dashRadius = 0,
				boundingRadiusModTarget = 0,
				boundingRadiusModSource = 0,
				type = "InRange",
				radius = 800
			},
			slot = player:spellSlot(_W),
			damage = function(arg_140_0, arg_140_1)
				-- function 140
				if not arg_140_0 then
					return 0
				end

				return 0
			end
		},
		E = {
			ignore_obj_radius = 0,
			target_selector = 8,
			prediction = {
				hitchance = 0,
				new_path = 0.5,
				boundingRadiusMod = 0,
				width = 60,
				speed = 2500,
				delay = 0,
				range = 800,
				type = "InRange",
				out_range = 800,
				collision = {
					wall = false,
					minion = false,
					hero = true
				}
			},
			cast_spell = {},
			slot = player:spellSlot(_E),
			damage = function(arg_141_0, arg_141_1)
				-- function 141
				if not arg_141_0 then
					return 0
				end

				local slot_141_0 = arg_141_1 or player
				local slot_141_1 = (({
					30,
					55,
					80,
					105,
					130
				})[slot_141_0:spellSlot(2).level] or 0) + common.GetBonusAD(slot_141_0) * 0.1
				local slot_141_2 = common.CAD(arg_141_0, slot_141_1, slot_141_0)

				return slot_15_48.get_damage(slot_141_2, arg_141_0, slot_141_0, 2)
			end
		},
		R = {
			ignore_obj_radius = 1060,
			target_selector = 8,
			prediction = {
				hitchance = 0,
				new_path = 0.5,
				boundingRadiusMod = 1,
				width = 60,
				speed = 3200,
				delay = 2.5,
				range = 2500,
				type = "Linear",
				out_range = 2500,
				collision = {
					wall = true,
					minion = true,
					hero = true
				}
			},
			cast_spell = {},
			slot = player:spellSlot(_R),
			damage = function(arg_142_0, arg_142_1)
				-- function 142
				if not arg_142_0 then
					return 0
				end

				local slot_142_0 = arg_142_1 or player
				local slot_142_1 = {
					20,
					25,
					30
				}
				local slot_142_2 = slot_142_0:spellSlot(3).level
				local slot_142_3 = common.GetTotalAD(slot_142_0) * 0.1
				local slot_142_4 = (slot_142_1[slot_142_2] or 0) + slot_142_3
				local slot_142_5 = common.CAD(arg_142_0, slot_142_4, slot_142_0)

				return slot_15_48.get_damage(slot_142_5, arg_142_0, slot_142_0, 2)
			end
		}
	}
	slot_15_48.XinZhao = {
		Q = {
			ignore_obj_radius = 850,
			target_selector = 8,
			prediction = {
				hitchance = 0,
				new_path = 0.5,
				boundingRadiusMod = 1,
				width = 120,
				speed = 1500,
				delay = 0.25,
				range = 850,
				type = "Linear",
				out_range = 850,
				collision = {
					wall = false,
					minion = true,
					hero = true
				}
			},
			cast_spell = {},
			slot = player:spellSlot(_Q),
			damage = function(arg_143_0, arg_143_1)
				-- function 143
				if not arg_143_0 then
					return 0
				end

				local slot_143_0 = arg_143_1 or player
				local slot_143_1 = (({
					5,
					25,
					45,
					65,
					85
				})[slot_143_0:spellSlot(0).level] or 0) + common.GetTotalAD(slot_143_0) * 0.8
				local slot_143_2 = common.CAD(arg_143_0, slot_143_1, slot_143_0)

				return slot_15_48.get_damage(slot_143_2, arg_143_0, slot_143_0, 2)
			end
		},
		W = {
			ignore_obj_radius = 0,
			target_selector = 8,
			prediction = {
				hitchance = 0,
				new_path = 0.5,
				boundingRadiusMod = 1,
				width = 30,
				speed = 6250,
				delay = 0.5,
				range = 940,
				type = "Linear",
				out_range = 940,
				collision = {
					wall = false,
					minion = true,
					hero = true
				}
			},
			slot = player:spellSlot(_W),
			damage = function(arg_144_0, arg_144_1)
				-- function 144
				if not arg_144_0 then
					return 0
				end

				return 0
			end
		},
		E = {
			ignore_obj_radius = 0,
			target_selector = 8,
			prediction = {
				hitchance = 0,
				new_path = 0.5,
				boundingRadiusMod = 0,
				width = 60,
				speed = 2500,
				delay = 0,
				range = 800,
				type = "Linear",
				out_range = 800,
				collision = {
					wall = false,
					minion = false,
					hero = true
				}
			},
			cast_spell = {},
			slot = player:spellSlot(_E),
			damage = function(arg_145_0, arg_145_1)
				-- function 145
				if not arg_145_0 then
					return 0
				end

				local slot_145_0 = arg_145_1 or player
				local slot_145_1 = (({
					30,
					55,
					80,
					105,
					130
				})[slot_145_0:spellSlot(2).level] or 0) + common.GetBonusAD(slot_145_0) * 0.1
				local slot_145_2 = common.CAD(arg_145_0, slot_145_1, slot_145_0)

				return slot_15_48.get_damage(slot_145_2, arg_145_0, slot_145_0, 2)
			end
		},
		R = {
			ignore_obj_radius = 1060,
			target_selector = 8,
			prediction = {
				hitchance = 0,
				new_path = 0.5,
				boundingRadiusMod = 1,
				width = 60,
				speed = 3200,
				delay = 2.5,
				range = 2500,
				type = "Linear",
				out_range = 2500,
				collision = {
					wall = true,
					minion = true,
					hero = true
				}
			},
			cast_spell = {},
			slot = player:spellSlot(_R),
			damage = function(arg_146_0, arg_146_1)
				-- function 146
				if not arg_146_0 then
					return 0
				end

				local slot_146_0 = arg_146_1 or player
				local slot_146_1 = {
					20,
					25,
					30
				}
				local slot_146_2 = slot_146_0:spellSlot(3).level
				local slot_146_3 = common.GetTotalAD(slot_146_0) * 0.1
				local slot_146_4 = (slot_146_1[slot_146_2] or 0) + slot_146_3
				local slot_146_5 = common.CAD(arg_146_0, slot_146_4, slot_146_0)

				return slot_15_48.get_damage(slot_146_5, arg_146_0, slot_146_0, 2)
			end
		}
	}
	slot_15_48.Vex = {
		Q = {
			ignore_obj_radius = 1200,
			target_selector = 8,
			prediction = {
				hitchance = 0,
				new_path = 0.5,
				boundingRadiusMod = 0,
				width = 50,
				speed = 1500,
				delay = 0.45,
				range = 1100,
				type = "Linear",
				out_range = 1100,
				collision = {
					wall = true,
					minion = false,
					hero = false
				}
			},
			cast_spell = {},
			slot = player:spellSlot(_Q),
			damage = function(arg_147_0, arg_147_1)
				-- function 147
				if not arg_147_0 then
					return 0
				end

				local slot_147_0 = arg_147_1 or player
				local slot_147_1 = (({
					70,
					115,
					160,
					205,
					250
				})[slot_147_0:spellSlot(0).level] or 0) + common.GetTotalAP(slot_147_0) * 0.7
				local slot_147_2 = common.CAP(arg_147_0, slot_147_1, slot_147_0)

				return slot_15_48.get_damage(slot_147_2, arg_147_0, slot_147_0, 1)
			end
		},
		W = {
			ignore_obj_radius = 0,
			target_selector = 8,
			prediction = {
				delay = 0,
				range = 475,
				dashRadius = 0,
				boundingRadiusModTarget = 0,
				boundingRadiusModSource = 0,
				type = "InRange",
				radius = 475
			},
			slot = player:spellSlot(_W),
			damage = function(arg_148_0, arg_148_1)
				-- function 148
				if not arg_148_0 then
					return 0
				end

				local slot_148_0 = arg_148_1 or player
				local slot_148_1 = (({
					80,
					120,
					160,
					200,
					240
				})[slot_148_0:spellSlot(1).level] or 0) + common.GetTotalAP(slot_148_0) * 0.3
				local slot_148_2 = common.CAP(arg_148_0, slot_148_1, slot_148_0)

				return slot_15_48.get_damage(slot_148_2, arg_148_0, slot_148_0, 1)
			end
		},
		E = {
			ignore_obj_radius = 0,
			target_selector = 8,
			prediction = {
				hitchance = 0,
				radius = 50,
				new_path = 0.5,
				boundingRadiusMod = 0,
				speed = 1300,
				delay = 0.25,
				range = 750,
				type = "Circular",
				out_range = 800
			},
			cast_spell = {},
			slot = player:spellSlot(_E),
			damage = function(arg_149_0, arg_149_1)
				-- function 149
				if not arg_149_0 then
					return 0
				end

				local slot_149_0 = arg_149_1 or player
				local slot_149_1 = {
					50,
					70,
					90,
					110,
					130
				}
				local slot_149_2 = {
					0.4,
					0.45,
					0.5,
					0.55,
					0.6
				}
				local slot_149_3 = slot_149_0:spellSlot(2).level
				local slot_149_4 = slot_149_1[slot_149_3] or 0
				local slot_149_5 = slot_149_2[slot_149_3] or 0
				local slot_149_6 = slot_149_4 + common.GetTotalAP(slot_149_0) * slot_149_5
				local slot_149_7 = common.CAP(arg_149_0, slot_149_6, slot_149_0)

				return slot_15_48.get_damage(slot_149_7, arg_149_0, slot_149_0, 1)
			end
		},
		R = {
			ignore_obj_radius = 2000,
			target_selector = 8,
			prediction = {
				hitchance = 0,
				new_path = 0.5,
				boundingRadiusMod = 1,
				width = 60,
				speed = 1600,
				delay = 0.25,
				range = 2000,
				type = "Linear",
				out_range = 2000,
				collision = {
					wall = true,
					minion = true,
					hero = true
				}
			},
			cast_spell = {},
			slot = player:spellSlot(_R),
			damage = function(arg_150_0, arg_150_1, arg_150_2)
				-- function 150
				if not arg_150_0 then
					return 0
				end

				local slot_150_0 = arg_150_1 or player
				local slot_150_1 = {
					75,
					125,
					175
				}
				local slot_150_2 = slot_150_0:spellSlot(3).level
				local slot_150_3 = (slot_150_1[slot_150_2] or 0) + common.GetTotalAP(slot_150_0) * 0.2
				local slot_150_4 = common.CAP(arg_150_0, slot_150_3, slot_150_0)

				if not arg_150_2 then
					local slot_150_5 = (({
						150,
						250,
						350
					})[slot_150_2] or 0) + common.GetTotalAP(slot_150_0) * 0.5

					slot_150_4 = slot_150_4 + common.CAP(arg_150_0, slot_150_5, slot_150_0)
				end

				return slot_15_48.get_damage(slot_150_4, arg_150_0, slot_150_0, 1)
			end
		}
	}
	slot_15_48.Leona = {
		Q = {
			ignore_obj_radius = 1200,
			target_selector = 8,
			prediction = {
				delay = 0,
				range = 125,
				dashRadius = 0,
				boundingRadiusModTarget = 1,
				boundingRadiusModSource = 1,
				type = "InRange",
				radius = 125
			},
			cast_spell = {},
			slot = player:spellSlot(_Q),
			damage = function(arg_151_0, arg_151_1)
				-- function 151
				if not arg_151_0 then
					return 0
				end

				local slot_151_0 = arg_151_1 or player
				local slot_151_1 = (({
					10,
					35,
					60,
					85,
					110
				})[slot_151_0:spellSlot(0).level] or 0) + common.GetTotalAP(slot_151_0) * 0.3
				local slot_151_2 = common.CAP(arg_151_0, slot_151_1, slot_151_0)

				return slot_15_48.get_damage(slot_151_2, arg_151_0, slot_151_0, 1)
			end
		},
		W = {
			ignore_obj_radius = 0,
			target_selector = 8,
			prediction = {
				delay = 0,
				range = 450,
				dashRadius = 0,
				boundingRadiusModTarget = 0,
				boundingRadiusModSource = 0,
				type = "InRange",
				radius = 450
			},
			slot = player:spellSlot(_W),
			damage = function(arg_152_0, arg_152_1)
				-- function 152
				if not arg_152_0 then
					return 0
				end

				local slot_152_0 = arg_152_1 or player
				local slot_152_1 = (({
					50,
					90,
					130,
					170,
					210
				})[slot_152_0:spellSlot(1).level] or 0) + common.GetTotalAP(slot_152_0) * 0.4
				local slot_152_2 = common.CAP(arg_152_0, slot_152_1, slot_152_0)

				return slot_15_48.get_damage(slot_152_2, arg_152_0, slot_152_0, 1)
			end
		},
		E = {
			ignore_obj_radius = 0,
			target_selector = 8,
			prediction = {
				hitchance = 0,
				new_path = 0.5,
				boundingRadiusMod = 1,
				width = 70,
				speed = 2000,
				delay = 0.25,
				range = 900,
				type = "Linear",
				out_range = 900,
				collision = {
					wall = false,
					minion = false,
					hero = false
				}
			},
			cast_spell = {},
			slot = player:spellSlot(_E),
			damage = function(arg_153_0, arg_153_1)
				-- function 153
				if not arg_153_0 then
					return 0
				end

				local slot_153_0 = arg_153_1 or player
				local slot_153_1 = (({
					50,
					90,
					130,
					170,
					210
				})[slot_153_0:spellSlot(2).level] or 0) + common.GetTotalAP(slot_153_0) * 0.4
				local slot_153_2 = common.CAP(arg_153_0, slot_153_1, slot_153_0)

				return slot_15_48.get_damage(slot_153_2, arg_153_0, slot_153_0, 1)
			end
		},
		E2 = {
			ignore_obj_radius = 0,
			target_selector = 8,
			prediction = {
				hitchance = 0,
				new_path = 0.5,
				boundingRadiusMod = 1,
				width = 70,
				speed = 2000,
				delay = 0.25,
				range = 1200,
				type = "Linear",
				out_range = 900,
				collision = {
					wall = false,
					minion = false,
					hero = false
				}
			},
			cast_spell = {},
			slot = player:spellSlot(_E),
			damage = function(arg_154_0, arg_154_1)
				-- function 154
				if not arg_154_0 then
					return 0
				end

				local slot_154_0 = arg_154_1 or player
				local slot_154_1 = (({
					50,
					90,
					130,
					170,
					210
				})[slot_154_0:spellSlot(2).level] or 0) + common.GetTotalAP(slot_154_0) * 0.4
				local slot_154_2 = common.CAP(arg_154_0, slot_154_1, slot_154_0)

				return slot_15_48.get_damage(slot_154_2, arg_154_0, slot_154_0, 1)
			end
		},
		R = {
			ignore_obj_radius = 1200,
			target_selector = 8,
			prediction = {
				hitchance = 0,
				out_range = 1200,
				new_path = 0.5,
				boundingRadiusMod = 0,
				delay = 1,
				range = 1200,
				type = "Circular",
				radius = 50,
				speed = math.huge,
				collision = {
					wall = true,
					minion = true,
					hero = true
				}
			},
			cast_spell = {},
			slot = player:spellSlot(_R),
			damage = function(arg_155_0, arg_155_1, arg_155_2)
				-- function 155
				if not arg_155_0 then
					return 0
				end

				local slot_155_0 = arg_155_1 or player
				local slot_155_1 = (({
					150,
					225,
					300
				})[slot_155_0:spellSlot(3).level] or 0) + common.GetTotalAP(slot_155_0) * 0.8
				local slot_155_2 = common.CAP(arg_155_0, slot_155_1, slot_155_0)

				return slot_15_48.get_damage(slot_155_2, arg_155_0, slot_155_0, 1)
			end
		}
	}
	slot_15_48.Nami = {
		Q = {
			ignore_obj_radius = 1200,
			target_selector = 8,
			prediction = {
				hitchance = 0,
				out_range = 900,
				new_path = 0.5,
				boundingRadiusMod = 0,
				delay = 0.976,
				range = 850,
				type = "Circular",
				radius = 100,
				speed = math.huge,
				collision = {
					wall = false,
					minion = false,
					hero = false
				}
			},
			cast_spell = {},
			slot = player:spellSlot(_Q),
			damage = function(arg_156_0, arg_156_1)
				-- function 156
				if not arg_156_0 then
					return 0
				end

				local slot_156_0 = arg_156_1 or player
				local slot_156_1 = (({
					90,
					145,
					200,
					255,
					310
				})[slot_156_0:spellSlot(0).level] or 0) + common.GetTotalAP(slot_156_0) * 0.5
				local slot_156_2 = common.CAP(arg_156_0, slot_156_1, slot_156_0)

				return slot_15_48.get_damage(slot_156_2, arg_156_0, slot_156_0, 1)
			end
		},
		W = {
			ignore_obj_radius = 0,
			target_selector = 8,
			prediction = {
				delay = 0,
				range = 705,
				dashRadius = 0,
				boundingRadiusModTarget = 0,
				boundingRadiusModSource = 1,
				type = "InRange",
				radius = 705
			},
			slot = player:spellSlot(_W),
			damage = function(arg_157_0, arg_157_1)
				-- function 157
				if not arg_157_0 then
					return 0
				end

				local slot_157_0 = arg_157_1 or player
				local slot_157_1 = (({
					60,
					100,
					140,
					180,
					220
				})[slot_157_0:spellSlot(1).level] or 0) + common.GetTotalAP(slot_157_0) * 0.5
				local slot_157_2 = common.CAP(arg_157_0, slot_157_1, slot_157_0)

				return slot_15_48.get_damage(slot_157_2, arg_157_0, slot_157_0, 1)
			end
		},
		E = {
			ignore_obj_radius = 0,
			target_selector = 8,
			prediction = {
				delay = 0,
				range = 800,
				dashRadius = 0,
				boundingRadiusModTarget = 0,
				boundingRadiusModSource = 0,
				type = "InRange",
				radius = 800
			},
			cast_spell = {},
			slot = player:spellSlot(_E),
			damage = function(arg_158_0, arg_158_1)
				-- function 158
				if not arg_158_0 then
					return 0
				end

				local slot_158_0 = arg_158_1 or player
				local slot_158_1 = (({
					20,
					30,
					40,
					50,
					60
				})[slot_158_0:spellSlot(2).level] or 0) + common.GetTotalAP(slot_158_0) * 0.2
				local slot_158_2 = common.CAP(arg_158_0, slot_158_1, slot_158_0)

				return slot_15_48.get_damage(slot_158_2, arg_158_0, slot_158_0, 1)
			end
		},
		R = {
			ignore_obj_radius = 1200,
			target_selector = 8,
			prediction = {
				hitchance = 0,
				new_path = 0.5,
				boundingRadiusMod = 0,
				width = 250,
				speed = 850,
				delay = 0.5,
				range = 2500,
				type = "Linear",
				out_range = 1200,
				collision = {
					wall = true,
					minion = true,
					hero = true
				}
			},
			cast_spell = {},
			slot = player:spellSlot(_R),
			damage = function(arg_159_0, arg_159_1, arg_159_2)
				-- function 159
				if not arg_159_0 then
					return 0
				end

				local slot_159_0 = arg_159_1 or player
				local slot_159_1 = (({
					150,
					250,
					350
				})[slot_159_0:spellSlot(3).level] or 0) + common.GetTotalAP(slot_159_0) * 0.6
				local slot_159_2 = common.CAP(arg_159_0, slot_159_1, slot_159_0)

				return slot_15_48.get_damage(slot_159_2, arg_159_0, slot_159_0, 1)
			end
		}
	}
	slot_15_48.Teemo = {
		Q = {
			ignore_obj_radius = 1200,
			target_selector = 8,
			prediction = {
				delay = 0,
				range = 680,
				dashRadius = 0,
				boundingRadiusModTarget = 0,
				boundingRadiusModSource = 0,
				type = "InRange",
				radius = 680
			},
			cast_spell = {},
			slot = player:spellSlot(_Q),
			damage = function(arg_160_0, arg_160_1)
				-- function 160
				if not arg_160_0 then
					return 0
				end

				local slot_160_0 = arg_160_1 or player
				local slot_160_1 = (({
					80,
					125,
					170,
					215,
					260
				})[slot_160_0:spellSlot(0).level] or 0) + common.GetTotalAP(slot_160_0) * 0.8
				local slot_160_2 = common.CAP(arg_160_0, slot_160_1, slot_160_0)

				return slot_15_48.get_damage(slot_160_2, arg_160_0, slot_160_0, 1)
			end
		},
		W = {
			ignore_obj_radius = 0,
			target_selector = 8,
			prediction = {
				delay = 0,
				range = 0,
				dashRadius = 0,
				boundingRadiusModTarget = 0,
				boundingRadiusModSource = 0,
				type = "InRange",
				radius = 0
			},
			slot = player:spellSlot(_W),
			damage = function(arg_161_0, arg_161_1)
				-- function 161
				if not arg_161_0 then
					return 0
				end

				return 0
			end
		},
		E = {
			ignore_obj_radius = 0,
			target_selector = 8,
			prediction = {
				delay = 0,
				range = 0,
				dashRadius = 0,
				boundingRadiusModTarget = 0,
				boundingRadiusModSource = 0,
				type = "InRange",
				radius = 0
			},
			cast_spell = {},
			slot = player:spellSlot(_E),
			damage = function(arg_162_0, arg_162_1)
				-- function 162
				if not arg_162_0 then
					return 0
				end

				return 0
			end
		},
		R = {
			ignore_obj_radius = 400,
			target_selector = 8,
			prediction = {
				hitchance = 0,
				out_range = 900,
				new_path = 0.5,
				boundingRadiusMod = 1,
				delay = 0.5,
				range = 400,
				type = "Circular",
				radius = 75,
				speed = math.huge,
				collision = {
					wall = true,
					minion = true,
					hero = true
				}
			},
			cast_spell = {},
			slot = player:spellSlot(_R),
			damage = function(arg_163_0, arg_163_1, arg_163_2)
				-- function 163
				if not arg_163_0 then
					return 0
				end

				local slot_163_0 = arg_163_1 or player
				local slot_163_1 = (({
					200,
					325,
					450
				})[slot_163_0:spellSlot(3).level] or 0) + common.GetTotalAP(slot_163_0) * 0.55
				local slot_163_2 = common.CAP(arg_163_0, slot_163_1, slot_163_0)

				return slot_15_48.get_damage(slot_163_2, arg_163_0, slot_163_0, 1)
			end
		}
	}
	slot_15_48.Ezreal = {
		Q = {
			ignore_obj_radius = 1150,
			target_selector = 8,
			prediction = {
				delay = 0.25,
				range = 1100,
				type = "Linear",
				width2 = 40,
				boundingRadiusMod = 1,
				width = 60,
				speed = 2000,
				collision = {
					wall = true,
					minion = true,
					hero = false
				}
			},
			cast_spell = {},
			slot = player:spellSlot(_Q),
			damage = function(arg_164_0, arg_164_1)
				-- function 164
				if not arg_164_0 then
					return 0
				end

				local slot_164_0 = arg_164_1 or player
				local slot_164_1 = ({
					20,
					45,
					70,
					95,
					120
				})[slot_164_0:spellSlot(0).level] or 0
				local slot_164_2 = common.GetTotalAP(slot_164_0) * 0.15
				local slot_164_3 = common.GetTotalAD(slot_164_0) * 1.3
				local slot_164_4 = slot_164_1 + slot_164_2 + slot_164_3
				local slot_164_5 = common.CAD(arg_164_0, slot_164_4, slot_164_0)

				return slot_15_48.get_damage(slot_164_5, arg_164_0, slot_164_0, 2)
			end
		},
		W = {
			ignore_obj_radius = 0,
			target_selector = 8,
			prediction = {
				delay = 0.25,
				range = 1150,
				type = "Linear",
				boundingRadiusMod = 1,
				width = 80,
				speed = 1600
			},
			slot = player:spellSlot(_W),
			damage = function(arg_165_0, arg_165_1)
				-- function 165
				if not arg_165_0 then
					return 0
				end

				return 0
			end
		},
		E = {
			ignore_obj_radius = 0,
			target_selector = 8,
			prediction = {
				dashRadius = 475,
				delay = 0,
				range = 475,
				fixedRange = true,
				boundingRadiusModTarget = 0,
				boundingRadiusModSource = 0,
				type = "InRange",
				radius = 475,
				speed = math.huge
			},
			cast_spell = {},
			slot = player:spellSlot(_E),
			damage = function(arg_166_0, arg_166_1)
				-- function 166
				if not arg_166_0 then
					return 0
				end

				return 0
			end
		},
		R = {
			ignore_obj_radius = 9999,
			target_selector = 8,
			prediction = {
				hitchance = 0,
				delay = 1,
				range = 9999,
				new_path = 0.5,
				boundingRadiusMod = 0,
				width = 80,
				speed = 2000,
				width2 = 160,
				slow = true,
				type = "Linear",
				out_range = 9999,
				collision = {
					wall = true,
					minion = false,
					hero = false
				}
			},
			cast_spell = {},
			slot = player:spellSlot(_R),
			damage = function(arg_167_0, arg_167_1, arg_167_2)
				-- function 167
				if not arg_167_0 then
					return 0
				end

				local slot_167_0 = arg_167_1 or player
				local slot_167_1 = ({
					350,
					550,
					750
				})[slot_167_0:spellSlot(3).level] or 0
				local slot_167_2 = common.GetBonusAD(slot_167_0) * 1
				local slot_167_3 = slot_167_1 + common.GetTotalAD(slot_167_0) * 0.9 + slot_167_2
				local slot_167_4 = common.CAD(arg_167_0, slot_167_3, slot_167_0)

				return slot_15_48.get_damage(slot_167_4, arg_167_0, slot_167_0, 2)
			end
		}
	}
	slot_15_48.Ahri = {
		Q = {
			ignore_obj_radius = 900,
			target_selector = 8,
			prediction = {
				delay = 0.25,
				range = 900,
				type = "Linear",
				boundingRadiusMod = 0,
				width = 100,
				speed = 1550,
				collision = {
					minion = false,
					hero = false
				}
			},
			cast_spell = {},
			slot = player:spellSlot(_Q),
			damage = function(arg_168_0, arg_168_1)
				-- function 168
				if not arg_168_0 then
					return 0
				end

				local slot_168_0 = arg_168_1 or player
				local slot_168_1 = (({
					40,
					65,
					90,
					115,
					140
				})[slot_168_0:spellSlot(0).level] or 0) + common.GetTotalAP(slot_168_0) * 0.45
				local slot_168_2 = common.CAP(arg_168_0, slot_168_1, slot_168_0)

				return slot_15_48.get_damage(slot_168_2, arg_168_0, slot_168_0, 1)
			end
		},
		W = {
			ignore_obj_radius = 550,
			target_selector = 8,
			prediction = {
				delay = 0,
				range = 550,
				dashRadius = 0,
				boundingRadiusModTarget = 0,
				boundingRadiusModSource = 0,
				type = "InRange",
				radius = 550
			},
			cast_spell = {},
			slot = player:spellSlot(_W),
			damage = function(arg_169_0, arg_169_1)
				-- function 169
				if not arg_169_0 then
					return 0
				end

				local slot_169_0 = arg_169_1 or player
				local slot_169_1 = (({
					40,
					65,
					90,
					115,
					140
				})[slot_169_0:spellSlot(0).level] or 0) + common.GetTotalAP(slot_169_0) * 0.3
				local slot_169_2 = common.CAP(arg_169_0, slot_169_1, slot_169_0)

				return slot_15_48.get_damage(slot_169_2, arg_169_0, slot_169_0, 1)
			end
		},
		E = {
			ignore_obj_radius = 550,
			target_selector = 8,
			prediction = {
				delay = 0.25,
				range = 1000,
				type = "Linear",
				boundingRadiusMod = 0,
				width = 100,
				speed = 1550,
				collision = {
					minion = false,
					hero = false
				}
			},
			cast_spell = {},
			slot = player:spellSlot(_E),
			damage = function(arg_170_0, arg_170_1)
				-- function 170
				if not arg_170_0 then
					return 0
				end

				local slot_170_0 = arg_170_1 or player
				local slot_170_1 = (({
					80,
					120,
					160,
					200,
					240
				})[slot_170_0:spellSlot(2).level] or 0) + common.GetTotalAP(slot_170_0) * 0.75
				local slot_170_2 = common.CAP(arg_170_0, slot_170_1, slot_170_0)

				return slot_15_48.get_damage(slot_170_2, arg_170_0, slot_170_0, 1)
			end
		},
		R = {
			ignore_obj_radius = 550,
			target_selector = 8,
			prediction = {
				delay = 0,
				range = 550,
				dashRadius = 0,
				boundingRadiusModTarget = 0,
				boundingRadiusModSource = 0,
				type = "InRange",
				radius = 550
			},
			cast_spell = {},
			slot = player:spellSlot(_R),
			damage = function(arg_171_0, arg_171_1)
				-- function 171
				if not arg_171_0 then
					return 0
				end

				return 0
			end
		}
	}
	slot_15_48.Qiyana = {
		Q = {
			ignore_obj_radius = 525,
			target_selector = 8,
			prediction = {
				delay = 0.25,
				range = 525,
				type = "Linear",
				boundingRadiusMod = 1,
				width = 70,
				speed = 1600,
				collision = {
					minion = true,
					hero = true
				}
			},
			cast_spell = {},
			slot = player:spellSlot(_Q),
			damage = function(arg_172_0, arg_172_1, arg_172_2)
				-- function 172
				if not arg_172_0 then
					return 0
				end

				local slot_172_0 = arg_172_1 or player
				local slot_172_1 = (({
					60,
					90,
					120,
					150,
					180
				})[slot_172_0:spellSlot(0).level] or 0) + common.GetBonusAD(slot_172_0) * 0.9

				if (slot_172_0:spellSlot(_Q).name:find("QiyanaQ_Rock") or arg_172_2) and common.GetHp(arg_172_0) < 50 then
					slot_172_1 = slot_172_1 * 1.6
				end

				local slot_172_2 = common.CAD(arg_172_0, slot_172_1, slot_172_0)

				return slot_15_48.get_damage(slot_172_2, arg_172_0, slot_172_0, 2)
			end
		},
		W = {
			ignore_obj_radius = 1100,
			target_selector = 8,
			prediction = {
				delay = 0,
				range = 1100,
				dashRadius = 0,
				boundingRadiusModTarget = 0,
				boundingRadiusModSource = 0,
				type = "InRange",
				radius = 1100
			},
			cast_spell = {},
			slot = player:spellSlot(_W),
			damage = function(arg_173_0, arg_173_1)
				-- function 173
				if not arg_173_0 then
					return 0
				end

				local slot_173_0 = arg_173_1 or player
				local slot_173_1 = ({
					8,
					16,
					24,
					32,
					40
				})[slot_173_0:spellSlot(1).level] or 0
				local slot_173_2 = common.GetBonusAD(slot_173_0) * 0.2
				local slot_173_3 = slot_173_1 + common.GetTotalAP(slot_173_0) * 0.45 + slot_173_2
				local slot_173_4 = common.CAP(arg_173_0, slot_173_3, slot_173_0)

				return slot_15_48.get_damage(slot_173_4, arg_173_0, slot_173_0, 1)
			end
		},
		E = {
			ignore_obj_radius = 650,
			target_selector = 8,
			prediction = {
				delay = 0,
				range = 650,
				dashRadius = 0,
				boundingRadiusModTarget = 0,
				boundingRadiusModSource = 0,
				type = "InRange",
				radius = 650
			},
			cast_spell = {},
			slot = player:spellSlot(_E),
			damage = function(arg_174_0, arg_174_1)
				-- function 174
				if not arg_174_0 then
					return 0
				end

				local slot_174_0 = arg_174_1 or player
				local slot_174_1 = (({
					50,
					90,
					130,
					170,
					210
				})[slot_174_0:spellSlot(2).level] or 0) + common.GetBonusAD(slot_174_0) * 0.5
				local slot_174_2 = common.CAD(arg_174_0, slot_174_1, slot_174_0)

				return slot_15_48.get_damage(slot_174_2, arg_174_0, slot_174_0, 2)
			end
		},
		R = {
			ignore_obj_radius = 875,
			target_selector = 8,
			prediction = {
				delay = 0.25,
				range = 875,
				type = "Linear",
				boundingRadiusMod = 0,
				width = 140,
				speed = 2000,
				collision = {
					minion = false,
					hero = false
				}
			},
			cast_spell = {},
			slot = player:spellSlot(_R),
			damage = function(arg_175_0, arg_175_1)
				-- function 175
				if not arg_175_0 then
					return 0
				end

				local slot_175_0 = arg_175_1 or player
				local slot_175_1 = {
					100,
					200,
					300
				}
				local slot_175_2 = {
					500,
					750,
					1000
				}
				local slot_175_3 = slot_175_0:spellSlot(3).level
				local slot_175_4 = slot_175_1[slot_175_3] or 0
				local slot_175_5 = slot_175_2[slot_175_3] or 0
				local slot_175_6 = common.GetBonusAD(slot_175_0) * 1.25
				local slot_175_7 = math.min(slot_175_5, arg_175_0.maxHealth * 0.1)
				local slot_175_8 = slot_175_4 + slot_175_6 + slot_175_7
				local slot_175_9 = common.CAD(arg_175_0, slot_175_8, slot_175_0)

				return slot_15_48.get_damage(slot_175_9, arg_175_0, slot_175_0, 2)
			end
		}
	}
	slot_15_48.Zeri = {
		Q = {
			ignore_obj_radius = 825,
			target_selector = 8,
			prediction = {
				delay = 0,
				range = 750,
				type = "Linear",
				ig = true,
				boundingRadiusMod = 1,
				width = 40,
				speed = 2600,
				collision = {
					minion = true,
					hero = true
				}
			},
			cast_spell = {},
			slot = player:spellSlot(_Q),
			damage = function(arg_176_0, arg_176_1, arg_176_2)
				-- function 176
				if not arg_176_0 then
					return 0
				end

				local slot_176_0 = arg_176_1 or player
				local slot_176_1 = {
					15,
					17,
					19,
					21,
					23
				}
				local slot_176_2 = {
					1.04,
					1.08,
					1.12,
					1.16,
					1.2
				}
				local slot_176_3 = slot_176_0:spellSlot(0).level
				local slot_176_4 = slot_176_1[slot_176_3] or 0
				local slot_176_5 = slot_176_2[slot_176_3] or 0
				local slot_176_6 = slot_176_4 + common.GetTotalAD(slot_176_0) * slot_176_5
				local slot_176_7 = common.CAD(arg_176_0, slot_176_6, slot_176_0)

				return slot_15_48.get_damage(slot_176_7, arg_176_0, slot_176_0, 2)
			end
		},
		W = {
			ignore_obj_radius = 1100,
			target_selector = 8,
			prediction = {
				delay = 0.51562863588333,
				range = 1150,
				type = "Linear",
				boundingRadiusMod = 1,
				width = 40,
				speed = 2200,
				collision = {
					minion = true,
					hero = true
				}
			},
			cast_spell = {},
			slot = player:spellSlot(_W),
			damage = function(arg_177_0, arg_177_1)
				-- function 177
				if not arg_177_0 then
					return 0
				end

				local slot_177_0 = arg_177_1 or player
				local slot_177_1 = ({
					30,
					70,
					110,
					150,
					190
				})[slot_177_0:spellSlot(1).level] or 0
				local slot_177_2 = common.GetTotalAD(slot_177_0) * 1.3
				local slot_177_3 = slot_177_1 + common.GetTotalAP(slot_177_0) * 0.25 + slot_177_2
				local slot_177_4 = common.CAP(arg_177_0, slot_177_3, slot_177_0)

				return slot_15_48.get_damage(slot_177_4, arg_177_0, slot_177_0, 1)
			end
		},
		E = {
			ignore_obj_radius = 650,
			target_selector = 8,
			prediction = {
				delay = 0,
				range = 650,
				speed = 1000,
				dashRadius = 0,
				boundingRadiusModTarget = 0,
				boundingRadiusModSource = 0,
				type = "InRange",
				radius = 650
			},
			cast_spell = {},
			slot = player:spellSlot(_E),
			damage = function(arg_178_0, arg_178_1)
				-- function 178
				if not arg_178_0 then
					return 0
				end

				local slot_178_0 = arg_178_1 or player
				local slot_178_1 = (({
					50,
					90,
					130,
					170,
					210
				})[slot_178_0:spellSlot(2).level] or 0) + common.GetBonusAD(slot_178_0) * 0.5
				local slot_178_2 = common.CAD(arg_178_0, slot_178_1, slot_178_0)

				return slot_15_48.get_damage(slot_178_2, arg_178_0, slot_178_0, 2)
			end
		},
		R = {
			ignore_obj_radius = 825,
			target_selector = 8,
			prediction = {
				delay = 0,
				range = 780,
				dashRadius = 0,
				boundingRadiusModTarget = 0,
				boundingRadiusModSource = 0,
				type = "InRange",
				radius = 780
			},
			cast_spell = {},
			slot = player:spellSlot(_R),
			damage = function(arg_179_0, arg_179_1)
				-- function 179
				if not arg_179_0 then
					return 0
				end

				local slot_179_0 = arg_179_1 or player
				local slot_179_1 = ({
					175,
					275,
					375
				})[slot_179_0:spellSlot(3).level] or 0
				local slot_179_2 = common.GetBonusAD(slot_179_0) * 1
				local slot_179_3 = common.GetTotalAP(slot_179_0) * 1.1
				local slot_179_4 = slot_179_1 + slot_179_2 + slot_179_3
				local slot_179_5 = common.CAP(arg_179_0, slot_179_4, slot_179_0)

				return slot_15_48.get_damage(slot_179_5, arg_179_0, slot_179_0, 1)
			end
		}
	}
	slot_15_48.Renata = {
		Q = {
			ignore_obj_radius = 825,
			target_selector = 8,
			prediction = {
				delay = 0.25,
				range = 830,
				type = "Linear",
				width2 = 70,
				boundingRadiusMod = 1,
				width = 70,
				speed = 1450,
				collision = {
					wall = true,
					minion = true,
					hero = true
				}
			},
			prediction2 = {
				delay = 0.25,
				range = 1500,
				type = "Linear",
				boundingRadiusMod = 1,
				width = 70,
				speed = 1450,
				collision = {
					wall = true,
					minion = true,
					hero = true
				}
			},
			cast_spell = {},
			slot = player:spellSlot(_Q),
			damage = function(arg_180_0, arg_180_1, arg_180_2)
				-- function 180
				if not arg_180_0 then
					return 0
				end

				local slot_180_0 = arg_180_1 or player
				local slot_180_1 = (({
					80,
					125,
					170,
					215,
					260
				})[slot_180_0:spellSlot(0).level] or 0) + common.GetTotalAP(slot_180_0) * 0.8
				local slot_180_2 = common.CAP(arg_180_0, slot_180_1, slot_180_0)

				return slot_15_48.get_damage(slot_180_2, arg_180_0, slot_180_0, 1)
			end
		},
		W = {
			ignore_obj_radius = 800,
			target_selector = 8,
			prediction = {
				delay = 0,
				range = 800,
				dashRadius = 0,
				boundingRadiusModTarget = 0,
				boundingRadiusModSource = 0,
				type = "InRange",
				radius = 800
			},
			cast_spell = {},
			slot = player:spellSlot(_W),
			damage = function(arg_181_0, arg_181_1)
				-- function 181
				return 0
			end
		},
		E = {
			ignore_obj_radius = 650,
			target_selector = 8,
			prediction = {
				delay = 0.679,
				range = 800,
				boundingRadiusMod = 1,
				type = "Circular",
				radius = 30,
				speed = math.huge
			},
			cast_spell = {},
			slot = player:spellSlot(_E),
			damage = function(arg_182_0, arg_182_1)
				-- function 182
				if not arg_182_0 then
					return 0
				end

				local slot_182_0 = arg_182_1 or player
				local slot_182_1 = (({
					65,
					95,
					125,
					155,
					185
				})[slot_182_0:spellSlot(2).level] or 0) + common.GetTotalAP(slot_182_0) * 0.55
				local slot_182_2 = common.CAP(arg_182_0, slot_182_1, slot_182_0)

				return slot_15_48.get_damage(slot_182_2, arg_182_0, slot_182_0, 1)
			end
		},
		R = {
			ignore_obj_radius = 2000,
			target_selector = 8,
			prediction = {
				delay = 0.75,
				range = 2000,
				type = "Linear",
				boundingRadiusMod = 0,
				width = 250,
				speed = 1000
			},
			cast_spell = {},
			slot = player:spellSlot(_R),
			damage = function(arg_183_0, arg_183_1)
				-- function 183
				return 0
			end
		}
	}
	slot_15_48.Karma = {
		Q = {
			ignore_obj_radius = 950,
			target_selector = 8,
			prediction = {
				delay = 0.25,
				range = 950,
				type = "Linear",
				width2 = 80,
				boundingRadiusMod = 1,
				width = 60,
				speed = 1700,
				collision = {
					wall = true,
					minion = true,
					hero = false
				}
			},
			cast_spell = {},
			slot = player:spellSlot(_Q),
			damage = function(arg_184_0, arg_184_1, arg_184_2)
				-- function 184
				if not arg_184_0 then
					return 0
				end

				local slot_184_0 = arg_184_1 or player
				local slot_184_1 = (({
					70,
					120,
					170,
					220,
					270
				})[slot_184_0:spellSlot(0).level] or 0) + common.GetTotalAP(slot_184_0) * 0.7
				local slot_184_2 = common.CAP(arg_184_0, slot_184_1, slot_184_0)

				return slot_15_48.get_damage(slot_184_2, arg_184_0, slot_184_0, 1)
			end
		},
		Q_collision = {
			ignore_obj_radius = 950,
			target_selector = 8,
			prediction = {
				delay = 0.25,
				range = 950,
				type = "Linear",
				boundingRadiusMod = 0,
				width = 60,
				speed = 1700,
				collision = {
					wall = true,
					minion = true,
					hero = false
				}
			},
			cast_spell = {},
			slot = player:spellSlot(_Q),
			damage = function(arg_185_0, arg_185_1, arg_185_2)
				-- function 185
				if not arg_185_0 then
					return 0
				end

				local slot_185_0 = arg_185_1 or player
				local slot_185_1 = (({
					70,
					120,
					170,
					220,
					270
				})[slot_185_0:spellSlot(0).level] or 0) + common.GetTotalAP(slot_185_0) * 0.4
				local slot_185_2 = common.CAP(arg_185_0, slot_185_1, slot_185_0)

				return slot_15_48.get_damage(slot_185_2, arg_185_0, slot_185_0, 1)
			end
		},
		W = {
			ignore_obj_radius = 675,
			target_selector = 8,
			prediction = {
				delay = 0,
				range = 675,
				dashRadius = 0,
				boundingRadiusModTarget = 0,
				boundingRadiusModSource = 0,
				type = "InRange",
				radius = 675
			},
			cast_spell = {},
			slot = player:spellSlot(_W),
			damage = function(arg_186_0, arg_186_1)
				-- function 186
				if not arg_186_0 then
					return 0
				end

				local slot_186_0 = arg_186_1 or player
				local slot_186_1 = (({
					40,
					65,
					90,
					115,
					140
				})[slot_186_0:spellSlot(0).level] or 0) + common.GetTotalAP(slot_186_0) * 0.45
				local slot_186_2 = common.CAP(arg_186_0, slot_186_1, slot_186_0)

				return slot_15_48.get_damage(slot_186_2, arg_186_0, slot_186_0, 1)
			end
		},
		E = {
			ignore_obj_radius = 650,
			target_selector = 8,
			prediction = {
				delay = 0,
				range = 800,
				dashRadius = 0,
				boundingRadiusModTarget = 0,
				boundingRadiusModSource = 0,
				type = "InRange",
				radius = 800
			},
			cast_spell = {},
			slot = player:spellSlot(_E),
			damage = function(arg_187_0, arg_187_1)
				-- function 187
				return 0
			end
		},
		R = {
			ignore_obj_radius = 2000,
			target_selector = 8,
			prediction = {
				delay = 0,
				range = 0,
				dashRadius = 0,
				boundingRadiusModTarget = 0,
				boundingRadiusModSource = 0,
				type = "InRange",
				radius = 0
			},
			cast_spell = {},
			slot = player:spellSlot(_R),
			damage = function(arg_188_0, arg_188_1)
				-- function 188
				return 0
			end
		}
	}
	slot_15_48.Cassiopeia = {
		Q = {
			ignore_obj_radius = 850,
			target_selector = 8,
			prediction = {
				delay = 0.75,
				range = 850,
				boundingRadiusMod = 0,
				type = "Circular",
				radius = 50,
				speed = math.huge
			},
			cast_spell = {},
			slot = player:spellSlot(_Q),
			damage = function(arg_189_0, arg_189_1)
				-- function 189
				if not arg_189_0 then
					return 0
				end

				local slot_189_0 = arg_189_1 or player
				local slot_189_1 = (({
					75,
					110,
					145,
					180,
					215
				})[slot_189_0:spellSlot(0).level] or 0) + common.GetTotalAP(slot_189_0) * 0.9
				local slot_189_2 = common.CAP(arg_189_0, slot_189_1, slot_189_0)

				return slot_15_48.get_damage(slot_189_2, arg_189_0, slot_189_0, 1)
			end
		},
		W = {
			ignore_obj_radius = 675,
			target_selector = 8,
			prediction = {
				delay = 0.7,
				range = 700,
				speed = 3000,
				boundingRadiusMod = 0,
				type = "Circular",
				radius = 50
			},
			cast_spell = {},
			slot = player:spellSlot(_W),
			damage = function(arg_190_0, arg_190_1)
				-- function 190
				if not arg_190_0 then
					return 0
				end

				local slot_190_0 = arg_190_1 or player
				local slot_190_1 = (({
					20,
					25,
					30,
					35,
					40
				})[slot_190_0:spellSlot(1).level] or 0) + common.GetTotalAP(slot_190_0) * 0.15
				local slot_190_2 = common.CAP(arg_190_0, slot_190_1, slot_190_0)

				return slot_15_48.get_damage(slot_190_2, arg_190_0, slot_190_0, 1)
			end
		},
		E = {
			ignore_obj_radius = 650,
			target_selector = 8,
			prediction = {
				delay = 0.125,
				range = 700,
				speed = 2500,
				dashRadius = 0,
				boundingRadiusModTarget = 0,
				boundingRadiusModSource = 0,
				type = "InRange",
				radius = 700
			},
			cast_spell = {},
			slot = player:spellSlot(_E),
			damage = function(arg_191_0, arg_191_1)
				-- function 191
				if not arg_191_0 then
					return 0
				end

				local slot_191_0 = arg_191_1 or player
				local slot_191_1 = slot_191_0.level
				local slot_191_2 = slot_191_0:spellSlot(2).level

				if slot_191_1 > 18 then
					slot_191_1 = 18
				end

				local slot_191_3 = 48 + 4 * slot_191_1 + common.GetTotalAP(slot_191_0) * 0.1
				local slot_191_4 = 0

				function is_zd(arg_192_0)
					-- function 192
					if arg_192_0.buff.poisontrailtarget or arg_192_0.buff.twitchdeadlyvenom or arg_192_0.buff.cassiopeiawpoison or arg_192_0.buff.cassiopeiaqdebuff or arg_192_0.buff.toxicshotparticle or arg_192_0.buff.bantamtraptarget then
						return true
					end

					return false
				end

				if is_zd(arg_191_0) then
					local slot_191_5 = (({
						20,
						40,
						60,
						80,
						100
					})[slot_191_2] or 0) + common.GetTotalAP(slot_191_0) * 0.6

					slot_191_4 = common.CAP(arg_191_0, slot_191_5, slot_191_0)
				end

				local slot_191_6 = common.CAP(arg_191_0, slot_191_3, slot_191_0) + slot_191_4

				return slot_15_48.get_damage(slot_191_6, arg_191_0, slot_191_0, 1)
			end
		},
		R = {
			ignore_obj_radius = 2000,
			target_selector = 8,
			prediction = {
				delay = 0.5,
				range = 850,
				type = "Linear",
				boundingRadiusMod = 0,
				width = 40,
				speed = math.huge,
				collision = {
					wall = true,
					minion = true,
					hero = true
				}
			},
			cast_spell = {},
			slot = player:spellSlot(_R),
			damage = function(arg_193_0, arg_193_1)
				-- function 193
				if not arg_193_0 then
					return 0
				end

				local slot_193_0 = arg_193_1 or player
				local slot_193_1 = (({
					150,
					250,
					350
				})[slot_193_0:spellSlot(3).level] or 0) + common.GetTotalAP(slot_193_0) * 0.5
				local slot_193_2 = common.CAP(arg_193_0, slot_193_1, slot_193_0)

				return slot_15_48.get_damage(slot_193_2, arg_193_0, slot_193_0, 1)
			end
		}
	}
	slot_15_48.Kalista = {
		Q = {
			target_selector = 8,
			ignore_obj_radius = 1200,
			prediction = {
				delay = 0.25,
				range = 1150,
				type = "Linear",
				boundingRadiusMod = 1,
				width = 40,
				speed = 2400,
				collision = {
					wall = true,
					minion = true,
					hero = false
				}
			},
			prediction_ig = {
				delay = 0.25,
				range = 1150,
				type = "Linear",
				ig = true,
				boundingRadiusMod = 1,
				width = 40,
				speed = 2400,
				collision = {
					wall = true,
					minion = true,
					hero = false
				}
			},
			cast_spell = {},
			slot = player:spellSlot(_Q),
			damage = function(arg_194_0, arg_194_1)
				-- function 194
				if not arg_194_0 then
					return 0
				end

				local slot_194_0 = arg_194_1 or player
				local slot_194_1 = (({
					10,
					75,
					140,
					205,
					270
				})[slot_194_0:spellSlot(0).level] or 0) + common.GetTotalAD(slot_194_0) * 1.05
				local slot_194_2 = common.CAD(arg_194_0, slot_194_1, slot_194_0)

				return slot_15_48.get_damage(slot_194_2, arg_194_0, slot_194_0, 2)
			end
		},
		W = {
			ignore_obj_radius = 1400,
			target_selector = 8,
			prediction = {
				delay = 0.5,
				range = 1400,
				dashRadius = 0,
				boundingRadiusModTarget = 1,
				boundingRadiusModSource = 1,
				type = "InRange",
				radius = 1400,
				speed = math.huge
			},
			cast_spell = {},
			slot = player:spellSlot(_W),
			damage = function(arg_195_0, arg_195_1)
				-- function 195
				if not arg_195_0 then
					return 0
				end

				return 0
			end
		},
		E = {
			ignore_obj_radius = 1100,
			target_selector = 8,
			prediction = {
				delay = 0.25,
				range = 1100,
				dashRadius = 0,
				boundingRadiusModTarget = 0,
				boundingRadiusModSource = 0,
				type = "InRange",
				radius = 1100,
				speed = math.huge
			},
			cast_spell = {},
			slot = player:spellSlot(_E),
			damage = function(arg_196_0, arg_196_1, arg_196_2)
				-- function 196
				if not arg_196_0 then
					return 0
				end

				local slot_196_0 = arg_196_1 or player
				local slot_196_1 = slot_196_0.level
				local slot_196_2 = slot_196_0:spellSlot(2).level

				if slot_196_2 == 0 then
					return 0
				end

				local slot_196_3 = arg_196_2 or 0
				local slot_196_4 = arg_196_0.buff.kalistaexpungemarker

				if slot_196_4 then
					slot_196_3 = slot_196_4.stacks
				elseif not arg_196_2 and preds.damage then
					for iter_196_0, iter_196_1 in pairs(preds.damage) do
						if iter_196_0 == slot_196_0.ptr and iter_196_1.true_aa and iter_196_1.target and iter_196_1.target.ptr == arg_196_0.ptr then
							arg_196_2 = 1

							break
						end
					end
				end

				if arg_196_2 then
					slot_196_3 = arg_196_2
				end

				if slot_196_3 == 0 then
					return 0
				end

				local slot_196_5 = common.GetTotalAD(slot_196_0)
				local slot_196_6 = common.GetTotalAP(slot_196_0)
				local slot_196_7 = {
					10,
					20,
					30,
					40,
					50
				}
				local slot_196_8 = {
					7,
					14,
					21,
					28,
					35
				}
				local slot_196_9 = {
					0.2,
					0.25,
					0.3,
					0.35,
					0.4
				}
				local slot_196_10 = (slot_196_7[slot_196_2] or 0) + slot_196_5 * 0.7 + slot_196_6 * 0.2
				local slot_196_11 = (slot_196_8[slot_196_2] + slot_196_5 * slot_196_9[slot_196_2] + slot_196_6 * 0.2) * (slot_196_3 - 1) + slot_196_10

				if arg_196_0.type == TYPE_HERO and common.findRune(player, 8014) and common.GetHp(arg_196_0) < 40 then
					slot_196_11 = slot_196_11 + slot_196_11 * 0.08
				end

				if arg_196_0.charName:find("Dragon") then
					slot_196_11 = slot_196_11 * 0.49
				elseif arg_196_0.charName:find("SRU_RiftHerald") or arg_196_0.charName:find("SRU_Baron") then
					slot_196_11 = slot_196_11 * 0.49
				end

				local slot_196_12 = common.CAD(arg_196_0, slot_196_11, slot_196_0)

				return slot_15_48.get_damage(slot_196_12, arg_196_0, slot_196_0, 2)
			end
		},
		R = {
			ignore_obj_radius = 1200,
			target_selector = 8,
			prediction = {
				delay = 0,
				range = 1200,
				dashRadius = 0,
				boundingRadiusModTarget = 0,
				boundingRadiusModSource = 0,
				type = "InRange",
				radius = 1100,
				speed = math.huge
			},
			cast_spell = {},
			slot = player:spellSlot(_R),
			damage = function(arg_197_0, arg_197_1)
				-- function 197
				if not arg_197_0 then
					return 0
				end

				return 0
			end
		}
	}
	slot_15_48.Ryze = {
		Q = {
			target_selector = 8,
			ignore_obj_radius = 1200,
			prediction = {
				delay = 0.25,
				range = 1000,
				type = "Linear",
				boundingRadiusMod = 1,
				width = 55,
				speed = 1700,
				collision = {
					wall = true,
					minion = true,
					hero = true
				}
			},
			prediction_ig = {
				delay = 0.25,
				range = 1000,
				type = "Linear",
				ig = true,
				boundingRadiusMod = 1,
				width = 110,
				speed = 1700,
				collision = {
					wall = true,
					minion = true,
					hero = true
				}
			},
			cast_spell = {},
			slot = player:spellSlot(_Q),
			damage = function(arg_198_0, arg_198_1)
				-- function 198
				if not arg_198_0 then
					return 0
				end

				local slot_198_0 = arg_198_1 or player
				local slot_198_1 = ({
					75,
					95,
					115,
					135,
					155
				})[slot_198_0:spellSlot(0).level] or 0
				local slot_198_2 = common.GetTotalAP(slot_198_0) * 0.55
				local slot_198_3 = slot_198_0.maxMana - (slot_198_0.baseMana + slot_198_0.baseManaPerLevel * slot_198_0.level)

				if slot_198_3 < 0 then
					slot_198_3 = 0
				end

				local slot_198_4 = slot_198_3 * 0.02
				local slot_198_5 = slot_198_1 + slot_198_2 + slot_198_4

				if arg_198_0.buff.ryzee and arg_198_0.buff.ryzee.endTime - game.time > 0.25 + slot_198_0.pos:dist(arg_198_0.pos) / 1700 then
					slot_198_5 = slot_198_5 + slot_198_5 * (0.1 + 0.3 * slot_198_0:spellSlot(_R).level)
				end

				local slot_198_6 = common.CAP(arg_198_0, slot_198_5, slot_198_0)

				return slot_15_48.get_damage(slot_198_6, arg_198_0, slot_198_0, 1)
			end
		},
		W = {
			ignore_obj_radius = 1400,
			target_selector = 8,
			prediction = {
				delay = 0.25,
				range = 550,
				dashRadius = 0,
				boundingRadiusModTarget = 1,
				boundingRadiusModSource = 1,
				type = "InRange",
				radius = 550,
				speed = math.huge
			},
			cast_spell = {},
			slot = player:spellSlot(_W),
			damage = function(arg_199_0, arg_199_1)
				-- function 199
				if not arg_199_0 then
					return 0
				end

				local slot_199_0 = arg_199_1 or player
				local slot_199_1 = ({
					80,
					110,
					140,
					170,
					200
				})[slot_199_0:spellSlot(1).level] or 0
				local slot_199_2 = common.GetTotalAP(slot_199_0) * 0.7
				local slot_199_3 = slot_199_0.maxMana - (slot_199_0.baseMana + slot_199_0.baseManaPerLevel * slot_199_0.level)

				if slot_199_3 < 0 then
					slot_199_3 = 0
				end

				local slot_199_4 = slot_199_3 * 0.04
				local slot_199_5 = slot_199_1 + slot_199_2 + slot_199_4
				local slot_199_6 = common.CAP(arg_199_0, slot_199_5, slot_199_0)

				return slot_15_48.get_damage(slot_199_6, arg_199_0, slot_199_0, 1)
			end
		},
		E = {
			ignore_obj_radius = 1100,
			target_selector = 8,
			prediction = {
				delay = 0.25,
				range = 550,
				dashRadius = 0,
				boundingRadiusModTarget = 0,
				boundingRadiusModSource = 0,
				type = "InRange",
				radius = 550,
				speed = math.huge
			},
			cast_spell = {},
			slot = player:spellSlot(_E),
			damage = function(arg_200_0, arg_200_1)
				-- function 200
				if not arg_200_0 then
					return 0
				end

				local slot_200_0 = arg_200_1 or player
				local slot_200_1 = ({
					60,
					90,
					120,
					150,
					180
				})[slot_200_0:spellSlot(2).level] or 0
				local slot_200_2 = common.GetTotalAP(slot_200_0) * 0.45
				local slot_200_3 = slot_200_0.maxMana - (slot_200_0.baseMana + slot_200_0.baseManaPerLevel * slot_200_0.level)

				if slot_200_3 < 0 then
					slot_200_3 = 0
				end

				local slot_200_4 = slot_200_3 * 0.02
				local slot_200_5 = slot_200_1 + slot_200_2 + slot_200_4
				local slot_200_6 = common.CAP(arg_200_0, slot_200_5, slot_200_0)

				return slot_15_48.get_damage(slot_200_6, arg_200_0, slot_200_0, 1)
			end
		},
		R = {
			ignore_obj_radius = 3000,
			target_selector = 8,
			prediction = {
				delay = 0,
				range = 1000,
				dashRadius = 0,
				boundingRadiusModTarget = 0,
				boundingRadiusModSource = 0,
				type = "InRange",
				radius = 1000,
				speed = math.huge
			},
			cast_spell = {},
			slot = player:spellSlot(_R),
			damage = function(arg_201_0, arg_201_1)
				-- function 201
				if not arg_201_0 then
					return 0
				end

				return 0
			end
		}
	}
	slot_15_48.Blitzcrank = {
		Q = {
			ignore_obj_radius = 1115,
			target_selector = 8,
			prediction = {
				delay = 0.25,
				range = 1020,
				type = "Linear",
				width2 = 80,
				boundingRadiusMod = 1,
				width = 70,
				speed = 1800,
				collision = {
					wall = true,
					minion = true,
					hero = true
				}
			},
			cast_spell = {},
			slot = player:spellSlot(_Q),
			damage = function(arg_202_0, arg_202_1)
				-- function 202
				if not arg_202_0 then
					return 0
				end

				local slot_202_0 = arg_202_1 or player
				local slot_202_1 = (({
					105,
					150,
					195,
					240,
					285
				})[slot_202_0:spellSlot(0).level] or 0) + common.GetTotalAP(slot_202_0) * 1.2
				local slot_202_2 = common.CAP(arg_202_0, slot_202_1, slot_202_0)

				return slot_15_48.get_damage(slot_202_2, arg_202_0, slot_202_0, 1)
			end
		},
		W = {
			ignore_obj_radius = 0,
			target_selector = 8,
			prediction = {
				delay = 0,
				range = 0,
				dashRadius = 0,
				boundingRadiusModTarget = 0,
				boundingRadiusModSource = 0,
				type = "InRange",
				radius = 0,
				speed = math.huge
			},
			cast_spell = {},
			slot = player:spellSlot(_W),
			damage = function(arg_203_0, arg_203_1)
				-- function 203
				if not arg_203_0 then
					return 0
				end

				return 0
			end
		},
		E = {
			ignore_obj_radius = 1100,
			target_selector = 8,
			prediction = {
				delay = 0,
				range = 125,
				dashRadius = 0,
				boundingRadiusModTarget = 1,
				boundingRadiusModSource = 1,
				type = "InRange",
				radius = 125,
				speed = math.huge
			},
			cast_spell = {},
			slot = player:spellSlot(_E),
			damage = function(arg_204_0, arg_204_1)
				-- function 204
				if not arg_204_0 then
					return 0
				end

				local slot_204_0 = arg_204_1 or player
				local slot_204_1 = common.GetTotalAP(slot_204_0) * 0.25 + common.GetTotalAD(slot_204_0) * 2
				local slot_204_2 = common.CAD(arg_204_0, slot_204_1, slot_204_0)

				return slot_15_48.get_damage(slot_204_2, arg_204_0, slot_204_0, 2)
			end
		},
		R = {
			ignore_obj_radius = 3000,
			target_selector = 8,
			prediction = {
				delay = 0,
				range = 600,
				dashRadius = 0,
				boundingRadiusModTarget = 0,
				boundingRadiusModSource = 0,
				type = "InRange",
				radius = 600,
				speed = math.huge
			},
			cast_spell = {},
			slot = player:spellSlot(_R),
			damage = function(arg_205_0, arg_205_1)
				-- function 205
				if not arg_205_0 then
					return 0
				end

				local slot_205_0 = arg_205_1 or player
				local slot_205_1 = (({
					275,
					400,
					525
				})[slot_205_0:spellSlot(3).level] or 0) + common.GetTotalAP(slot_205_0) * 1
				local slot_205_2 = common.CAP(arg_205_0, slot_205_1, slot_205_0)

				return slot_15_48.get_damage(slot_205_2, arg_205_0, slot_205_0, 1)
			end
		}
	}
	slot_15_48.Belveth = {
		Q = {
			ignore_obj_radius = 1115,
			target_selector = 8,
			prediction = {
				delay = 0,
				range = 400,
				type = "Linear",
				boundingRadiusMod = 1,
				width = 100,
				speed = 1800
			},
			cast_spell = {},
			slot = player:spellSlot(_Q),
			damage = function(arg_206_0, arg_206_1)
				-- function 206
				if not arg_206_0 then
					return 0
				end

				local slot_206_0 = arg_206_1 or player
				local slot_206_1 = (({
					10,
					15,
					20,
					25,
					30
				})[slot_206_0:spellSlot(0).level] or 0) + common.GetTotalAD(slot_206_0) * 1.1
				local slot_206_2 = common.CAD(arg_206_0, slot_206_1, slot_206_0)

				return slot_15_48.get_damage(slot_206_2, arg_206_0, slot_206_0, 2)
			end
		},
		W = {
			ignore_obj_radius = 0,
			target_selector = 8,
			prediction = {
				delay = 0.5,
				range = 600,
				type = "Linear",
				boundingRadiusMod = 1,
				width = 100,
				speed = math.huge
			},
			cast_spell = {},
			slot = player:spellSlot(_W),
			damage = function(arg_207_0, arg_207_1)
				-- function 207
				if not arg_207_0 then
					return 0
				end

				local slot_207_0 = arg_207_1 or player
				local slot_207_1 = ({
					70,
					110,
					150,
					190,
					230
				})[slot_207_0:spellSlot(1).level] or 0
				local slot_207_2 = common.GetBonusAD(slot_207_0) * 1
				local slot_207_3 = common.GetTotalAP(slot_207_0) * 1.25
				local slot_207_4 = slot_207_1 + slot_207_2 + slot_207_3
				local slot_207_5 = common.CAP(arg_207_0, slot_207_4, slot_207_0)

				return slot_15_48.get_damage(slot_207_5, arg_207_0, slot_207_0, 1)
			end
		},
		E = {
			ignore_obj_radius = 1100,
			target_selector = 8,
			prediction = {
				delay = 0,
				range = 500,
				dashRadius = 0,
				boundingRadiusModTarget = 1,
				boundingRadiusModSource = 1,
				type = "InRange",
				radius = 500,
				speed = math.huge
			},
			cast_spell = {},
			slot = player:spellSlot(_E),
			damage = function(arg_208_0, arg_208_1)
				-- function 208
				if not arg_208_0 then
					return 0
				end

				local slot_208_0 = arg_208_1 or player
				local slot_208_1 = ({
					8,
					10,
					12,
					14,
					16
				})[slot_208_0:spellSlot(2).level] or 0
				local slot_208_2 = common.GetTotalAD(slot_208_0) * 0.06
				local slot_208_3 = math.min(0, 100 - common.GetHp(arg_208_0))
				local slot_208_4 = slot_208_1 + slot_208_2
				local slot_208_5 = slot_208_4 + slot_208_4 * (slot_208_3 * 0.03)
				local slot_208_6 = common.CAD(arg_208_0, slot_208_5, slot_208_0)

				return slot_15_48.get_damage(slot_208_6, arg_208_0, slot_208_0, 2)
			end
		},
		R = {
			ignore_obj_radius = 3000,
			target_selector = 8,
			prediction = {
				delay = 0,
				range = 500,
				dashRadius = 0,
				boundingRadiusModTarget = 0,
				boundingRadiusModSource = 0,
				type = "InRange",
				radius = 500,
				speed = math.huge
			},
			cast_spell = {},
			slot = player:spellSlot(_R),
			damage = function(arg_209_0, arg_209_1)
				-- function 209
				if not arg_209_0 then
					return 0
				end

				return 0
			end
		}
	}
	slot_15_48.Kaisa = {
		Q = {
			ignore_obj_radius = 1115,
			target_selector = 8,
			prediction = {
				delay = 0,
				range = 665,
				dashRadius = 0,
				boundingRadiusModTarget = 0,
				boundingRadiusModSource = 0,
				type = "InRange",
				radius = 665,
				speed = math.huge
			},
			cast_spell = {},
			slot = player:spellSlot(_Q),
			damage = function(arg_210_0, arg_210_1)
				-- function 210
				if not arg_210_0 then
					return 0
				end

				local slot_210_0 = arg_210_1 or player
				local slot_210_1 = ({
					40,
					55,
					70,
					85,
					100
				})[slot_210_0:spellSlot(0).level] or 0
				local slot_210_2 = common.GetBonusAD(slot_210_0) * 0.55
				local slot_210_3 = common.GetTotalAP(slot_210_0) * 0.2
				local slot_210_4 = slot_210_1 + slot_210_2 + slot_210_3
				local slot_210_5 = common.CAD(arg_210_0, slot_210_4, slot_210_0)

				return slot_15_48.get_damage(slot_210_5, arg_210_0, slot_210_0, 2)
			end
		},
		W = {
			target_selector = 8,
			ignore_obj_radius = 0,
			prediction = {
				delay = 0.4,
				range = 3000,
				type = "Linear",
				width2 = 140,
				boundingRadiusMod = 1,
				width = 100,
				speed = 1750,
				collision = {
					wall = true,
					minion = true,
					hero = true
				}
			},
			prediction_ig = {
				type = "Linear",
				delay = 0.4,
				boundingRadiusMod = 1,
				width = 100,
				speed = 1750,
				width2 = 140,
				range = 3000,
				ig = true,
				collision = {
					wall = true,
					minion = true,
					hero = true
				}
			},
			cast_spell = {},
			slot = player:spellSlot(_W),
			damage = function(arg_211_0, arg_211_1)
				-- function 211
				if not arg_211_0 then
					return 0
				end

				local slot_211_0 = arg_211_1 or player
				local slot_211_1 = ({
					30,
					55,
					80,
					105,
					130
				})[slot_211_0:spellSlot(1).level] or 0
				local slot_211_2 = common.GetTotalAD(slot_211_0) * 1.3
				local slot_211_3 = common.GetTotalAP(slot_211_0) * 0.45
				local slot_211_4 = slot_211_1 + slot_211_2 + slot_211_3
				local slot_211_5 = common.CAD(arg_211_0, slot_211_4, slot_211_0)

				return slot_15_48.get_damage(slot_211_5, arg_211_0, slot_211_0, 2)
			end
		},
		E = {
			ignore_obj_radius = 1100,
			target_selector = 8,
			prediction = {
				delay = 0,
				range = 500,
				dashRadius = 0,
				boundingRadiusModTarget = 1,
				boundingRadiusModSource = 1,
				type = "InRange",
				radius = 500,
				speed = math.huge
			},
			cast_spell = {},
			slot = player:spellSlot(_E),
			damage = function(arg_212_0, arg_212_1)
				-- function 212
				if not arg_212_0 then
					return 0
				end

				return 0
			end
		},
		R = {
			ignore_obj_radius = 3000,
			target_selector = 8,
			prediction = {
				delay = 0,
				range = 500,
				dashRadius = 0,
				boundingRadiusModTarget = 0,
				boundingRadiusModSource = 0,
				type = "InRange",
				radius = 500,
				speed = math.huge
			},
			cast_spell = {},
			slot = player:spellSlot(_R),
			damage = function(arg_213_0, arg_213_1)
				-- function 213
				if not arg_213_0 then
					return 0
				end

				return 0
			end
		}
	}
	slot_15_48.Nilah = {
		Q = {
			ignore_obj_radius = 600,
			target_selector = 8,
			prediction = {
				delay = 0.25,
				range = 600,
				type = "Linear",
				boundingRadiusMod = 0,
				width = 75,
				speed = math.huge,
				collision = {
					wall = false,
					minion = false,
					hero = false
				}
			},
			cast_spell = {},
			slot = player:spellSlot(_Q),
			damage = function(arg_214_0, arg_214_1)
				-- function 214
				if not arg_214_0 then
					return 0
				end

				local slot_214_0 = arg_214_1 or player
				local slot_214_1 = {
					5,
					10,
					15,
					20,
					25
				}
				local slot_214_2 = {
					0.9,
					1,
					1.1,
					1.2,
					1.3
				}
				local slot_214_3 = slot_214_0:spellSlot(0).level
				local slot_214_4 = slot_214_1[slot_214_3] or 0
				local slot_214_5 = slot_214_2[slot_214_3] or 0
				local slot_214_6 = slot_214_4 + common.GetTotalAD(slot_214_0) * slot_214_5

				if slot_214_0.crit > 0 then
					slot_214_6 = slot_214_6 + slot_214_6 * slot_214_0.crit
				end

				local slot_214_7 = common.CAD(arg_214_0, slot_214_6, slot_214_0)

				return slot_15_48.get_damage(slot_214_7, arg_214_0, slot_214_0, 2)
			end
		},
		W = {
			ignore_obj_radius = 0,
			target_selector = 8,
			prediction = {
				delay = 0,
				range = 225,
				dashRadius = 0,
				boundingRadiusModTarget = 0,
				boundingRadiusModSource = 0,
				type = "InRange",
				radius = 225,
				speed = math.huge
			},
			cast_spell = {},
			slot = player:spellSlot(_W),
			damage = function(arg_215_0, arg_215_1)
				-- function 215
				if not arg_215_0 then
					return 0
				end

				return 0
			end
		},
		E = {
			ignore_obj_radius = 1100,
			target_selector = 8,
			prediction = {
				delay = 0,
				range = 550,
				speed = 2200,
				dashRadius = 0,
				boundingRadiusModTarget = 1,
				boundingRadiusModSource = 1,
				type = "InRange",
				radius = 550
			},
			cast_spell = {},
			slot = player:spellSlot(_E),
			damage = function(arg_216_0, arg_216_1)
				-- function 216
				if not arg_216_0 then
					return 0
				end

				local slot_216_0 = arg_216_1 or player
				local slot_216_1 = (({
					65,
					90,
					115,
					140,
					165
				})[slot_216_0:spellSlot(2).level] or 0) + common.GetTotalAD(slot_216_0) * 0.2
				local slot_216_2 = common.CAD(arg_216_0, slot_216_1, slot_216_0)

				return slot_15_48.get_damage(slot_216_2, arg_216_0, slot_216_0, 2)
			end
		},
		R = {
			ignore_obj_radius = 3000,
			target_selector = 8,
			prediction = {
				delay = 0,
				range = 450,
				dashRadius = 0,
				boundingRadiusModTarget = 0,
				boundingRadiusModSource = 0,
				type = "InRange",
				radius = 450,
				speed = math.huge
			},
			cast_spell = {},
			slot = player:spellSlot(_R),
			damage = function(arg_217_0, arg_217_1)
				-- function 217
				if not arg_217_0 then
					return 0
				end

				local slot_217_0 = arg_217_1 or player
				local slot_217_1 = {
					15,
					30,
					45
				}
				local slot_217_2 = {
					125,
					225,
					325
				}
				local slot_217_3 = slot_217_0:spellSlot(3).level
				local slot_217_4 = slot_217_1[slot_217_3] or 0
				local slot_217_5 = slot_217_2[slot_217_3] or 0
				local slot_217_6 = common.GetTotalAD(slot_217_0)
				local slot_217_7 = (slot_217_4 + slot_217_6 * 0.28) * 4
				local slot_217_8 = slot_217_5 + slot_217_6 * 1.2
				local slot_217_9 = common.CAD(arg_217_0, slot_217_7 + slot_217_8, slot_217_0)

				return slot_15_48.get_damage(slot_217_9, arg_217_0, slot_217_0, 2)
			end
		}
	}
	slot_15_48.Yasuo = {
		Q = {
			ignore_obj_radius = 450,
			target_selector = 8,
			prediction = {
				delay = 0.25,
				range = 450,
				type = "Linear",
				boundingRadiusMod = 1,
				width = 40,
				speed = 1700,
				collision = {
					wall = false,
					minion = false,
					hero = false
				}
			},
			cast_spell = {},
			slot = player:spellSlot(_Q),
			damage = function(arg_218_0, arg_218_1)
				-- function 218
				if not arg_218_0 then
					return 0
				end

				local slot_218_0 = arg_218_1 or player
				local slot_218_1 = ({
					20,
					45,
					70,
					95,
					120
				})[slot_218_0:spellSlot(0).level] or 0
				local slot_218_2 = 1.05
				local slot_218_3 = slot_218_0.crit > 0.875
				local slot_218_4 = {}

				if slot_218_0.type == TYPE_HERO then
					for iter_218_0 = 0, 6 do
						local slot_218_5 = slot_218_0:itemID(iter_218_0)

						if slot_218_5 > 0 then
							slot_218_4[slot_218_5] = true
						end
					end
				end

				if slot_218_3 then
					slot_218_2 = 1.46995
				end

				if slot_218_4[3031] and slot_218_3 then
					slot_218_2 = slot_218_2 + 0.29399
				end

				local slot_218_6 = slot_218_1 + common.GetTotalAD(slot_218_0) * slot_218_2
				local slot_218_7 = common.CAD(arg_218_0, slot_218_6, slot_218_0)

				return slot_15_48.get_damage(slot_218_7, arg_218_0, slot_218_0, 2)
			end
		},
		Q3 = {
			ignore_obj_radius = 450,
			target_selector = 8,
			prediction = {
				delay = 0.25,
				range = 1150,
				type = "Linear",
				boundingRadiusMod = 1,
				width = 90,
				speed = 1200,
				collision = {
					wall = false,
					minion = false,
					hero = false
				}
			},
			cast_spell = {},
			slot = player:spellSlot(_Q),
			damage = function(arg_219_0, arg_219_1)
				-- function 219
				if not arg_219_0 then
					return 0
				end

				local slot_219_0 = arg_219_1 or player
				local slot_219_1 = ({
					20,
					45,
					70,
					95,
					120
				})[slot_219_0:spellSlot(0).level] or 0
				local slot_219_2 = 1.05
				local slot_219_3 = slot_219_0.crit > 0.875
				local slot_219_4 = {}

				if slot_219_0.type == TYPE_HERO then
					for iter_219_0 = 0, 6 do
						local slot_219_5 = slot_219_0:itemID(iter_219_0)

						if slot_219_5 > 0 then
							slot_219_4[slot_219_5] = true
						end
					end
				end

				if slot_219_3 then
					slot_219_2 = 1.46995
				end

				if slot_219_4[3031] and slot_219_3 then
					slot_219_2 = slot_219_2 + 0.29399
				end

				local slot_219_6 = slot_219_1 + common.GetTotalAD(slot_219_0) * slot_219_2
				local slot_219_7 = common.CAD(arg_219_0, slot_219_6, slot_219_0)

				return slot_15_48.get_damage(slot_219_7, arg_219_0, slot_219_0, 2)
			end
		},
		EQ = {
			ignore_obj_radius = 450,
			target_selector = 8,
			prediction = {
				delay = 0.58,
				range = 475,
				dashRadius = 0,
				boundingRadiusModTarget = 0,
				boundingRadiusModSource = 0,
				type = "InRange",
				radius = 215,
				speed = math.huge
			},
			cast_spell = {},
			slot = player:spellSlot(_Q),
			damage = function(arg_220_0, arg_220_1)
				-- function 220
				if not arg_220_0 then
					return 0
				end

				local slot_220_0 = arg_220_1 or player
				local slot_220_1 = ({
					20,
					45,
					70,
					95,
					120
				})[slot_220_0:spellSlot(0).level] or 0
				local slot_220_2 = 1.05
				local slot_220_3 = slot_220_0.crit > 0.875
				local slot_220_4 = {}

				if slot_220_0.type == TYPE_HERO then
					for iter_220_0 = 0, 6 do
						local slot_220_5 = slot_220_0:itemID(iter_220_0)

						if slot_220_5 > 0 then
							slot_220_4[slot_220_5] = true
						end
					end
				end

				if slot_220_3 then
					slot_220_2 = 1.46995
				end

				if slot_220_4[3031] and slot_220_3 then
					slot_220_2 = slot_220_2 + 0.29399
				end

				local slot_220_6 = slot_220_1 + common.GetTotalAD(slot_220_0) * slot_220_2
				local slot_220_7 = common.CAD(arg_220_0, slot_220_6, slot_220_0)

				return slot_15_48.get_damage(slot_220_7, arg_220_0, slot_220_0, 2)
			end
		},
		W = {
			ignore_obj_radius = 0,
			target_selector = 8,
			prediction = {
				delay = 0.013,
				range = 350,
				dashRadius = 0,
				boundingRadiusModTarget = 0,
				boundingRadiusModSource = 0,
				type = "InRange",
				radius = 350,
				speed = math.huge
			},
			cast_spell = {},
			slot = player:spellSlot(_W),
			damage = function(arg_221_0, arg_221_1)
				-- function 221
				if not arg_221_0 then
					return 0
				end

				return 0
			end
		},
		E = {
			ignore_obj_radius = 1100,
			target_selector = 8,
			prediction = {
				delay = 0,
				range = 475,
				dashRadius = 0,
				boundingRadiusModTarget = 1,
				boundingRadiusModSource = 1,
				type = "InRange",
				radius = 475,
				speed = 750 + player.moveSpeed * 0.6
			},
			cast_spell = {},
			slot = player:spellSlot(_E),
			damage = function(arg_222_0, arg_222_1)
				-- function 222
				if not arg_222_0 then
					return 0
				end

				local slot_222_0 = arg_222_1 or player
				local slot_222_1 = ({
					70,
					85,
					100,
					115,
					130
				})[slot_222_0:spellSlot(2).level] or 0
				local slot_222_2 = common.GetBonusAD(slot_222_0) * 0.2
				local slot_222_3 = common.GetTotalAP(slot_222_0) * 0.6
				local slot_222_4 = slot_222_1 + slot_222_2 + slot_222_3
				local slot_222_5 = common.CAP(arg_222_0, slot_222_4, slot_222_0)

				return slot_15_48.get_damage(slot_222_5, arg_222_0, slot_222_0, 1)
			end
		},
		R = {
			ignore_obj_radius = 1400,
			target_selector = 8,
			prediction = {
				delay = 0,
				range = 1400,
				dashRadius = 0,
				boundingRadiusModTarget = 0,
				boundingRadiusModSource = 0,
				type = "InRange",
				radius = 1400,
				speed = math.huge
			},
			cast_spell = {},
			slot = player:spellSlot(_R),
			damage = function(arg_223_0, arg_223_1)
				-- function 223
				if not arg_223_0 then
					return 0
				end

				local slot_223_0 = arg_223_1 or player
				local slot_223_1 = (({
					200,
					350,
					500
				})[slot_223_0:spellSlot(3).level] or 0) + common.GetBonusAD(slot_223_0) * 1.5
				local slot_223_2 = common.CAD(arg_223_0, slot_223_1, slot_223_0)

				return slot_15_48.get_damage(slot_223_2, arg_223_0, slot_223_0, 2)
			end
		}
	}
	slot_15_48.Zed = {
		Q = {
			ignore_obj_radius = 450,
			target_selector = 8,
			prediction = {
				delay = 0.25,
				range = 925,
				type = "Linear",
				boundingRadiusMod = 0,
				width = 50,
				speed = 1700,
				collision = {
					wall = true,
					minion = false,
					hero = false
				}
			},
			cast_spell = {},
			slot = player:spellSlot(_Q),
			damage = function(arg_224_0, arg_224_1)
				-- function 224
				if not arg_224_0 then
					return 0
				end

				local slot_224_0 = arg_224_1 or player
				local slot_224_1 = ({
					80,
					115,
					150,
					185,
					220
				})[slot_224_0:spellSlot(0).level] or 0
				local slot_224_2 = 1.1
				local slot_224_3 = slot_224_1 + common.GetBonusAD(slot_224_0) * slot_224_2
				local slot_224_4 = common.CAD(arg_224_0, slot_224_3, slot_224_0)

				return slot_15_48.get_damage(slot_224_4, arg_224_0, slot_224_0, 2)
			end
		},
		Q_IG = {
			ignore_obj_radius = 450,
			target_selector = 8,
			prediction = {
				delay = 0.25,
				range = 925,
				type = "Linear",
				ig = true,
				boundingRadiusMod = 0,
				width = 50,
				speed = 1700,
				collision = {
					wall = true,
					minion = false,
					hero = false
				}
			},
			cast_spell = {},
			slot = player:spellSlot(_Q),
			damage = function(arg_225_0, arg_225_1)
				-- function 225
				if not arg_225_0 then
					return 0
				end

				local slot_225_0 = arg_225_1 or player
				local slot_225_1 = ({
					80,
					115,
					150,
					185,
					220
				})[slot_225_0:spellSlot(0).level] or 0
				local slot_225_2 = 1.1
				local slot_225_3 = slot_225_1 + common.GetBonusAD(slot_225_0) * slot_225_2
				local slot_225_4 = common.CAD(arg_225_0, slot_225_3, slot_225_0)

				return slot_15_48.get_damage(slot_225_4, arg_225_0, slot_225_0, 2)
			end
		},
		W = {
			ignore_obj_radius = 0,
			target_selector = 8,
			prediction = {
				delay = 0,
				range = 650,
				speed = 2500,
				dashRadius = 0,
				boundingRadiusModTarget = 0,
				boundingRadiusModSource = 0,
				type = "InRange",
				radius = 650
			},
			cast_spell = {},
			slot = player:spellSlot(_W),
			damage = function(arg_226_0, arg_226_1)
				-- function 226
				if not arg_226_0 then
					return 0
				end

				return 0
			end
		},
		E = {
			ignore_obj_radius = 1100,
			target_selector = 8,
			prediction = {
				delay = 0,
				range = 290,
				dashRadius = 0,
				boundingRadiusModTarget = 1,
				boundingRadiusModSource = 1,
				type = "InRange",
				radius = 290,
				speed = math.huge
			},
			cast_spell = {},
			slot = player:spellSlot(_E),
			damage = function(arg_227_0, arg_227_1)
				-- function 227
				if not arg_227_0 then
					return 0
				end

				local slot_227_0 = arg_227_1 or player
				local slot_227_1 = (({
					70,
					90,
					110,
					130,
					150
				})[slot_227_0:spellSlot(2).level] or 0) + common.GetBonusAD(slot_227_0) * 0.65
				local slot_227_2 = common.CAD(arg_227_0, slot_227_1, slot_227_0)

				return slot_15_48.get_damage(slot_227_2, arg_227_0, slot_227_0, 2)
			end
		},
		R = {
			ignore_obj_radius = 1400,
			target_selector = 8,
			prediction = {
				delay = 0,
				range = 625,
				dashRadius = 0,
				boundingRadiusModTarget = 0,
				boundingRadiusModSource = 0,
				type = "InRange",
				radius = 625,
				speed = math.huge
			},
			cast_spell = {},
			slot = player:spellSlot(_R),
			damage = function(arg_228_0, arg_228_1)
				-- function 228
				if not arg_228_0 then
					return 0
				end

				local slot_228_0 = arg_228_1 or player
				local slot_228_1 = common.GetTotalAD(slot_228_0) * 0.65
				local slot_228_2 = common.CAD(arg_228_0, slot_228_1, slot_228_0)

				return slot_15_48.get_damage(slot_228_2, arg_228_0, slot_228_0, 2)
			end
		}
	}
	slot_15_48.MasterYi = {
		Q = {
			ignore_obj_radius = 600,
			target_selector = 8,
			prediction = {
				delay = 0,
				range = 600,
				dashRadius = 0,
				boundingRadiusModTarget = 0,
				boundingRadiusModSource = 0,
				type = "InRange",
				radius = 600,
				speed = math.huge
			},
			cast_spell = {},
			slot = player:spellSlot(_Q),
			damage = function(arg_229_0, arg_229_1)
				-- function 229
				if not arg_229_0 then
					return 0
				end

				local slot_229_0 = arg_229_1 or player
				local slot_229_1 = (({
					30,
					60,
					90,
					120,
					150
				})[slot_229_0:spellSlot(0).level] or 0) + common.GetTotalAD(slot_229_0) * 0.5
				local slot_229_2 = common.CAD(arg_229_0, slot_229_1, slot_229_0)

				return slot_15_48.get_damage(slot_229_2, arg_229_0, slot_229_0, 2)
			end
		},
		W = {
			ignore_obj_radius = 0,
			target_selector = 8,
			prediction = {
				delay = 0,
				range = 0,
				speed = 0,
				dashRadius = 0,
				boundingRadiusModTarget = 0,
				boundingRadiusModSource = 0,
				type = "InRange",
				radius = 0
			},
			cast_spell = {},
			slot = player:spellSlot(_W),
			damage = function(arg_230_0, arg_230_1)
				-- function 230
				if not arg_230_0 then
					return 0
				end

				return 0
			end
		},
		E = {
			ignore_obj_radius = 1100,
			target_selector = 8,
			prediction = {
				delay = 0,
				range = 0,
				dashRadius = 0,
				boundingRadiusModTarget = 1,
				boundingRadiusModSource = 1,
				type = "InRange",
				radius = 0,
				speed = math.huge
			},
			cast_spell = {},
			slot = player:spellSlot(_E),
			damage = function(arg_231_0, arg_231_1)
				-- function 231
				if not arg_231_0 then
					return 0
				end

				local slot_231_0 = arg_231_1 or player

				return (({
					20,
					25,
					30,
					35,
					40
				})[slot_231_0:spellSlot(2).level] or 0) + common.GetBonusAD(slot_231_0) * 0.35
			end
		},
		R = {
			ignore_obj_radius = 1400,
			target_selector = 8,
			prediction = {
				delay = 0,
				range = 0,
				dashRadius = 0,
				boundingRadiusModTarget = 0,
				boundingRadiusModSource = 0,
				type = "InRange",
				radius = 0,
				speed = math.huge
			},
			cast_spell = {},
			slot = player:spellSlot(_R),
			damage = function(arg_232_0, arg_232_1)
				-- function 232
				if not arg_232_0 then
					return 0
				end

				return 0
			end
		}
	}
	slot_15_48.Udyr = {
		Q = {
			ignore_obj_radius = 600,
			target_selector = 8,
			prediction = {
				delay = 0,
				dashRadius = 0,
				boundingRadiusModTarget = 0,
				boundingRadiusModSource = 0,
				type = "InRange",
				range = player.attackRange,
				radius = player.attackRange,
				speed = math.huge
			},
			cast_spell = {},
			slot = player:spellSlot(_Q),
			damage = function(arg_233_0, arg_233_1)
				-- function 233
				if not arg_233_0 then
					return 0
				end

				return 0
			end
		},
		W = {
			ignore_obj_radius = 600,
			target_selector = 8,
			prediction = {
				delay = 0,
				dashRadius = 0,
				boundingRadiusModTarget = 0,
				boundingRadiusModSource = 0,
				type = "InRange",
				range = player.attackRange,
				radius = player.attackRange,
				speed = math.huge
			},
			cast_spell = {},
			slot = player:spellSlot(_W),
			damage = function(arg_234_0, arg_234_1)
				-- function 234
				if not arg_234_0 then
					return 0
				end

				return 0
			end
		},
		E = {
			ignore_obj_radius = 600,
			target_selector = 8,
			prediction = {
				delay = 0,
				dashRadius = 0,
				boundingRadiusModTarget = 0,
				boundingRadiusModSource = 0,
				type = "InRange",
				range = player.attackRange,
				radius = player.attackRange,
				speed = math.huge
			},
			cast_spell = {},
			slot = player:spellSlot(_E),
			damage = function(arg_235_0, arg_235_1)
				-- function 235
				if not arg_235_0 then
					return 0
				end

				return 0
			end
		},
		R = {
			ignore_obj_radius = 600,
			target_selector = 8,
			prediction = {
				delay = 0,
				dashRadius = 0,
				boundingRadiusModTarget = 0,
				boundingRadiusModSource = 0,
				type = "InRange",
				range = player.attackRange,
				radius = player.attackRange,
				speed = math.huge
			},
			cast_spell = {},
			slot = player:spellSlot(_R),
			damage = function(arg_236_0, arg_236_1)
				-- function 236
				if not arg_236_0 then
					return 0
				end

				return 0
			end
		}
	}
	slot_15_48.Heimerdinger = {
		Q = {
			ignore_obj_radius = 600,
			target_selector = 8,
			prediction = {
				delay = 0,
				range = 350,
				dashRadius = 0,
				boundingRadiusModTarget = 0,
				boundingRadiusModSource = 0,
				type = "InRange",
				radius = 550,
				speed = math.huge
			},
			cast_spell = {},
			slot = player:spellSlot(_Q),
			damage = function(arg_237_0, arg_237_1)
				-- function 237
				if not arg_237_0 then
					return 0
				end

				return 0
			end
		},
		W = {
			ignore_obj_radius = 600,
			target_selector = 8,
			prediction = {
				delay = 0.25,
				range = 1300,
				type = "Linear",
				boundingRadiusMod = 0,
				width = 40,
				speed = 750,
				collision = {
					wall = true,
					minion = true,
					hero = false
				}
			},
			cast_spell = {},
			slot = player:spellSlot(_W),
			damage = function(arg_238_0, arg_238_1)
				-- function 238
				if not arg_238_0 then
					return 0
				end

				local slot_238_0 = arg_238_1 or player
				local slot_238_1 = common.GetTotalAP(slot_238_0) * 0.45
				local slot_238_2 = common.GetTotalAP(slot_238_0) * 0.12
				local slot_238_3 = common.GetTotalAP(slot_238_0) * 0.3575
				local slot_238_4 = {
					50,
					75,
					100,
					125,
					150
				}
				local slot_238_5 = {
					10,
					15,
					20,
					25,
					30
				}
				local slot_238_6 = {
					30,
					45,
					60,
					75,
					90
				}
				local slot_238_7 = slot_238_0:spellSlot(_W).level
				local slot_238_8 = slot_238_4[slot_238_7] or 0
				local slot_238_9 = slot_238_5[slot_238_7] or 0
				local slot_238_10 = slot_238_6[slot_238_7] or 0
				local slot_238_11 = 0

				if arg_238_0.type == TYPE_HERO then
					slot_238_11 = slot_238_8 + slot_238_1 + (slot_238_9 + slot_238_2) * 4
				elseif arg_238_0.type == TYPE_MINION then
					slot_238_11 = slot_238_8 + slot_238_1 + (slot_238_10 + slot_238_3) * 4
				end

				local slot_238_12 = common.CAP(arg_238_0, slot_238_11, slot_238_0)

				return slot_15_48.get_damage(slot_238_12, arg_238_0, slot_238_0, 1)
			end
		},
		E = {
			ignore_obj_radius = 600,
			target_selector = 8,
			prediction = {
				delay = 0.25,
				range = 925,
				speed = 1700,
				ig = true,
				boundingRadiusMod = 0,
				type = "Circular",
				radius = 135,
				collision = {
					wall = true,
					minion = true,
					hero = false
				}
			},
			cast_spell = {},
			slot = player:spellSlot(_E),
			damage = function(arg_239_0, arg_239_1)
				-- function 239
				if not arg_239_0 then
					return 0
				end

				local slot_239_0 = arg_239_1 or player
				local slot_239_1 = common.GetTotalAP(slot_239_0) * 0.6
				local slot_239_2 = (({
					60,
					100,
					140,
					180,
					220
				})[slot_239_0:spellSlot(_E).level] or 0) + slot_239_1
				local slot_239_3 = common.CAP(arg_239_0, slot_239_2, slot_239_0)

				return slot_15_48.get_damage(slot_239_3, arg_239_0, slot_239_0, 1)
			end
		},
		R = {
			ignore_obj_radius = 600,
			target_selector = 8,
			prediction = {
				delay = 0,
				dashRadius = 0,
				boundingRadiusModTarget = 0,
				boundingRadiusModSource = 0,
				type = "InRange",
				range = player.attackRange,
				radius = player.attackRange,
				speed = math.huge
			},
			cast_spell = {},
			slot = player:spellSlot(_R),
			damage = function(arg_240_0, arg_240_1)
				-- function 240
				if not arg_240_0 then
					return 0
				end

				return 0
			end
		}
	}
	slot_15_48.KSante = {
		Q = {
			ignore_obj_radius = 465,
			target_selector = 8,
			prediction = {
				delay = 0.25,
				range = 450,
				type = "Linear",
				boundingRadiusMod = 0,
				width = 40,
				speed = math.huge,
				collision = {
					wall = true,
					minion = false,
					hero = false
				}
			},
			cast_spell = {},
			slot = player:spellSlot(_Q),
			damage = function(arg_241_0, arg_241_1)
				-- function 241
				if not arg_241_0 then
					return 0
				end

				local slot_241_0 = arg_241_1 or player
				local slot_241_1 = common.GetTotalAD(slot_241_0) * 0.4
				local slot_241_2 = {
					50,
					75,
					100,
					125,
					150
				}
				local slot_241_3 = slot_241_0.bonusArmor * 0.3
				local slot_241_4 = slot_241_0.bonusSpellBlock * 0.3
				local slot_241_5 = (slot_241_2[slot_241_0:spellSlot(_Q).level] or 0) + slot_241_1 + slot_241_3 + slot_241_4
				local slot_241_6 = common.CAD(arg_241_0, slot_241_5, slot_241_0)

				return slot_15_48.get_damage(slot_241_6, arg_241_0, slot_241_0, 2)
			end
		},
		W = {
			ignore_obj_radius = 600,
			target_selector = 8,
			prediction = {
				delay = 0,
				range = 450,
				type = "Linear",
				boundingRadiusMod = 0,
				width = 50,
				speed = 1000,
				collision = {
					wall = true,
					minion = true,
					hero = false
				}
			},
			cast_spell = {},
			slot = player:spellSlot(_W),
			damage = function(arg_242_0, arg_242_1, arg_242_2)
				-- function 242
				if not arg_242_0 then
					return 0
				end

				local slot_242_0 = arg_242_1 or player
				local slot_242_1 = common.GetTotalAD(slot_242_0) * 0.5
				local slot_242_2 = {
					0.0425,
					0.045,
					0.0475,
					0.05,
					0.0525
				}
				local slot_242_3 = 0.25

				if slot_242_0.buff.ksantew or slot_242_0.buff.ksantew_allout then
					local slot_242_4 = slot_242_0.buff.ksantew or slot_242_0.buff.ksantew_allout

					slot_242_3 = math.min(1, 0.25 + (game.time - slot_242_4.startTime))
				end

				local slot_242_5 = {
					25,
					35,
					45,
					55,
					65
				}
				local slot_242_6 = slot_242_0:spellSlot(_W).level
				local slot_242_7 = (slot_242_2[slot_242_6] or 0) + slot_242_3 * 0.04
				local slot_242_8 = 0

				if arg_242_2 or slot_242_0.buff.ksantew_allout then
					slot_242_8 = slot_242_5[slot_242_6] or 0 + slot_242_1
				end

				local slot_242_9 = arg_242_0.maxHealth * slot_242_7 + slot_242_8
				local slot_242_10 = common.CAD(arg_242_0, slot_242_9, slot_242_0)

				return slot_15_48.get_damage(slot_242_10, arg_242_0, slot_242_0, 2)
			end
		},
		E = {
			ignore_obj_radius = 600,
			target_selector = 8,
			prediction = {
				delay = 0.25,
				range = 925,
				speed = 1700,
				ig = true,
				boundingRadiusMod = 0,
				type = "Circular",
				radius = 135
			},
			cast_spell = {},
			slot = player:spellSlot(_E),
			damage = function(arg_243_0, arg_243_1)
				-- function 243
				if not arg_243_0 then
					return 0
				end

				return 0
			end
		},
		R = {
			ignore_obj_radius = 600,
			target_selector = 8,
			prediction = {
				delay = 0.4,
				range = 350,
				dashRadius = 0,
				boundingRadiusModTarget = 0,
				boundingRadiusModSource = 0,
				type = "InRange",
				radius = 350,
				speed = math.huge
			},
			cast_spell = {},
			slot = player:spellSlot(_R),
			damage = function(arg_244_0, arg_244_1, arg_244_2)
				-- function 244
				if not arg_244_0 then
					return 0
				end

				local slot_244_0 = arg_244_1 or player
				local slot_244_1 = common.GetTotalAD(slot_244_0) * 0.2
				local slot_244_2 = {
					35,
					70,
					105
				}
				local slot_244_3 = slot_244_0:spellSlot(_R).level
				local slot_244_4 = slot_244_2[slot_244_3] or 0
				local slot_244_5 = 0

				if arg_244_2 then
					slot_244_5 = ({
						150,
						250,
						350
					})[slot_244_3] + slot_244_1
				end

				local slot_244_6 = slot_244_4 + slot_244_1 + slot_244_5
				local slot_244_7 = common.CAD(arg_244_0, slot_244_6, slot_244_0)

				return slot_15_48.get_damage(slot_244_7, arg_244_0, slot_244_0, 2)
			end
		}
	}
	slot_15_48.Milio = {
		Q = {
			ignore_obj_radius = 1000,
			target_selector = 8,
			prediction = {
				delay = 0.25,
				range = 965,
				type = "Linear",
				boundingRadiusMod = 0,
				width = 30,
				speed = 1200,
				collision = {
					wall = true,
					minion = true,
					hero = true
				}
			},
			prediction2 = {
				delay = 1,
				range = 1500,
				boundingRadiusMod = 0,
				type = "Circular",
				radius = 100,
				speed = math.huge
			},
			cast_spell = {},
			slot = player:spellSlot(_Q),
			damage = function(arg_245_0, arg_245_1)
				-- function 245
				if not arg_245_0 then
					return 0
				end

				local slot_245_0 = arg_245_1 or player
				local slot_245_1 = common.GetTotalAP(slot_245_0)
				local slot_245_2 = (({
					80,
					145,
					210,
					275,
					340
				})[slot_245_0:spellSlot(_Q).level] or 0) + slot_245_1 * 1.2
				local slot_245_3 = common.CAP(arg_245_0, slot_245_2, slot_245_0)

				return slot_15_48.get_damage(slot_245_3, arg_245_0, slot_245_0, 1)
			end
		},
		W = {
			ignore_obj_radius = 650,
			target_selector = 8,
			prediction = {
				delay = 0.25,
				range = 650,
				dashRadius = 0,
				boundingRadiusModTarget = 0,
				boundingRadiusModSource = 0,
				type = "InRange",
				radius = 650,
				speed = math.huge
			},
			cast_spell = {},
			slot = player:spellSlot(_W),
			damage = function(arg_246_0, arg_246_1, arg_246_2)
				-- function 246
				return 0
			end
		},
		E = {
			ignore_obj_radius = 650,
			target_selector = 8,
			prediction = {
				delay = 0,
				range = 650,
				dashRadius = 0,
				boundingRadiusModTarget = 0,
				boundingRadiusModSource = 0,
				type = "InRange",
				radius = 650,
				speed = math.huge
			},
			cast_spell = {},
			slot = player:spellSlot(_E),
			damage = function(arg_247_0, arg_247_1)
				-- function 247
				return 0
			end
		},
		R = {
			ignore_obj_radius = 600,
			target_selector = 8,
			prediction = {
				delay = 0,
				range = 700,
				dashRadius = 0,
				boundingRadiusModTarget = 0,
				boundingRadiusModSource = 0,
				type = "InRange",
				radius = 700,
				speed = math.huge
			},
			cast_spell = {},
			slot = player:spellSlot(_R),
			damage = function(arg_248_0, arg_248_1, arg_248_2)
				-- function 248
				return 0
			end
		}
	}
	slot_15_48.Nautilus = {
		Q = {
			prediction = {
				delay = 0.25,
				range = 1100,
				type = "Linear",
				boundingRadiusMod = 1,
				width = 90,
				speed = 2000,
				collision = {
					wall = true,
					minion = true,
					hero = true
				}
			},
			slot = player:spellSlot(_Q),
			damage = function(arg_249_0, arg_249_1)
				-- function 249
				if not arg_249_0 then
					return 0
				end

				local slot_249_0 = arg_249_1 or player
				local slot_249_1 = common.GetTotalAP(slot_249_0) * 0.9
				local slot_249_2 = (({
					80,
					130,
					180,
					230,
					280
				})[slot_249_0:spellSlot(_Q).level] or 0) + slot_249_1
				local slot_249_3 = common.CAP(arg_249_0, slot_249_2, slot_249_0)

				return slot_15_48.get_damage(slot_249_3, arg_249_0, slot_249_0, 1)
			end
		},
		W = {
			ignore_obj_radius = 175,
			prediction = {
				delay = 0,
				range = 175,
				type = "InRange",
				radius = 175,
				speed = math.huge
			},
			slot = player:spellSlot(_W),
			damage = function(arg_250_0, arg_250_1, arg_250_2)
				-- function 250
				return 0
			end
		},
		E = {
			ignore_obj_radius = 600,
			prediction = {
				delay = 0.811,
				range = 600,
				boundingRadiusMod = 0,
				type = "Circular",
				radius = 100,
				speed = math.huge
			},
			slot = player:spellSlot(_E),
			damage = function(arg_251_0, arg_251_1)
				-- function 251
				if not arg_251_0 then
					return 0
				end

				local slot_251_0 = arg_251_1 or player
				local slot_251_1 = common.GetTotalAP(slot_251_0) * 0.5
				local slot_251_2 = (({
					55,
					90,
					125,
					160,
					195
				})[slot_251_0:spellSlot(_E).level] or 0) + slot_251_1
				local slot_251_3 = common.CAP(arg_251_0, slot_251_2, slot_251_0)

				return slot_15_48.get_damage(slot_251_3, arg_251_0, slot_251_0, 1)
			end
		},
		R = {
			ignore_obj_radius = 825,
			prediction = {
				delay = 0.46,
				range = 825,
				speed = 2000,
				dashRadius = 0,
				type = "InRange",
				radius = 825,
				collision = {
					minion = false,
					hero = true
				}
			},
			slot = player:spellSlot(_R),
			damage = function(arg_252_0, arg_252_1, arg_252_2)
				-- function 252
				if not arg_252_0 then
					return 0
				end

				local slot_252_0 = arg_252_1 or player
				local slot_252_1 = common.GetTotalAP(slot_252_0) * 0.8
				local slot_252_2 = (({
					150,
					275,
					400
				})[slot_252_0:spellSlot(_R).level] or 0) + slot_252_1
				local slot_252_3 = common.CAP(arg_252_0, slot_252_2, slot_252_0)

				return slot_15_48.get_damage(slot_252_3, arg_252_0, slot_252_0, 1)
			end
		}
	}
	slot_15_48.Varus = {
		Q = {
			prediction = {
				delay = 0.528,
				range = 1595,
				type = "Linear",
				boundingRadiusMod = 0,
				width = 70,
				speed = 1900,
				collision = {
					wall = true,
					minion = false,
					hero = false
				}
			},
			prediction2 = {
				delay = 0,
				range = 1595,
				type = "Linear",
				boundingRadiusMod = 1,
				width = 70,
				speed = 1900,
				collision = {
					wall = true,
					minion = false,
					hero = false
				}
			},
			slot = player:spellSlot(_Q),
			damage = function(arg_253_0, arg_253_1)
				-- function 253
				if not arg_253_0 then
					return 0
				end

				local slot_253_0 = arg_253_1 or player
				local slot_253_1 = {
					0.833,
					0.866,
					0.9,
					0.933,
					0.966
				}
				local slot_253_2 = common.GetTotalAD(slot_253_0)
				local slot_253_3 = {
					10,
					46.67,
					83.33,
					120,
					156.67
				}
				local slot_253_4 = slot_253_0:spellSlot(_Q).level
				local slot_253_5 = (slot_253_3[slot_253_4] or 0) + slot_253_2 * slot_253_1[slot_253_4]
				local slot_253_6 = common.CAD(arg_253_0, slot_253_5, slot_253_0)

				return slot_15_48.get_damage(slot_253_6, arg_253_0, slot_253_0, 2)
			end
		},
		W = {
			ignore_obj_radius = 0,
			prediction = {
				delay = 0,
				range = 0,
				type = "InRange",
				radius = 0,
				speed = math.huge
			},
			slot = player:spellSlot(_W),
			damage = function(arg_254_0, arg_254_1, arg_254_2)
				-- function 254
				return 0
			end
		},
		E = {
			ignore_obj_radius = 925,
			prediction = {
				delay = 0.7419,
				range = 1000,
				speed = 2000,
				boundingRadiusMod = 0,
				type = "Circular",
				radius = 50
			},
			slot = player:spellSlot(_E),
			damage = function(arg_255_0, arg_255_1)
				-- function 255
				if not arg_255_0 then
					return 0
				end

				local slot_255_0 = arg_255_1 or player
				local slot_255_1 = common.GetBonusAD(slot_255_0)
				local slot_255_2 = (({
					60,
					100,
					140,
					180,
					220
				})[slot_255_0:spellSlot(_E).level] or 0) + slot_255_1 * 1.1
				local slot_255_3 = common.CAD(arg_255_0, slot_255_2, slot_255_0)

				return slot_15_48.get_damage(slot_255_3, arg_255_0, slot_255_0, 2)
			end
		},
		R = {
			ignore_obj_radius = 1050,
			prediction = {
				delay = 0.2419,
				range = 1050,
				type = "Linear",
				slow = true,
				boundingRadiusMod = 1,
				width = 60,
				speed = 1500,
				collision = {
					wall = true,
					minion = false,
					hero = false
				}
			},
			slot = player:spellSlot(_R),
			damage = function(arg_256_0, arg_256_1, arg_256_2)
				-- function 256
				if not arg_256_0 then
					return 0
				end

				local slot_256_0 = arg_256_1 or player
				local slot_256_1 = common.GetTotalAP(slot_256_0) * 1
				local slot_256_2 = (({
					150,
					250,
					350
				})[slot_256_0:spellSlot(_R).level] or 0) + slot_256_1
				local slot_256_3 = common.CAP(arg_256_0, slot_256_2, slot_256_0)

				return slot_15_48.get_damage(slot_256_3, arg_256_0, slot_256_0, 1)
			end
		}
	}
	slot_15_48.Sylas = {
		Q = {
			prediction = {
				delay = 0.4,
				range = 775,
				type = "Circular",
				boundingRadiusMod = 0,
				width = 70,
				speed = math.huge,
				collision = {
					wall = true,
					minion = false,
					hero = false
				}
			},
			prediction_ig = {
				delay = 1,
				range = 775,
				type = "Circular",
				ig = true,
				boundingRadiusMod = 0,
				width = 70,
				speed = math.huge,
				collision = {
					wall = true,
					minion = false,
					hero = false
				}
			},
			slot = player:spellSlot(_Q),
			damage = function(arg_257_0, arg_257_1)
				-- function 257
				if not arg_257_0 then
					return 0
				end

				local slot_257_0 = arg_257_1 or player
				local slot_257_1 = common.GetTotalAP(slot_257_0)
				local slot_257_2 = (({
					60,
					115,
					170,
					225,
					280
				})[slot_257_0:spellSlot(_Q).level] or 0) + slot_257_1 * 0.8
				local slot_257_3 = common.CAP(arg_257_0, slot_257_2, slot_257_0)

				return slot_15_48.get_damage(slot_257_3, arg_257_0, slot_257_0, 1)
			end
		},
		W = {
			ignore_obj_radius = 400,
			prediction = {
				delay = 0,
				range = 400,
				type = "InRange",
				radius = 400,
				speed = math.huge
			},
			slot = player:spellSlot(_W),
			damage = function(arg_258_0, arg_258_1, arg_258_2)
				-- function 258
				local slot_258_0 = arg_258_1 or player
				local slot_258_1 = common.GetTotalAP(slot_258_0)
				local slot_258_2 = (({
					75,
					110,
					145,
					180,
					215
				})[slot_258_0:spellSlot(_W).level] or 0) + slot_258_1 * 0.6
				local slot_258_3 = common.CAP(arg_258_0, slot_258_2, slot_258_0)

				return slot_15_48.get_damage(slot_258_3, arg_258_0, slot_258_0, 1)
			end
		},
		E = {
			ignore_obj_radius = 790,
			prediction = {
				delay = 0.25,
				range = 790,
				type = "Linear",
				boundingRadiusMod = 0,
				width = 60,
				speed = 2500,
				collision = {
					wall = true,
					minion = true,
					hero = true
				}
			},
			prediction_ig = {
				delay = 0.25,
				range = 790,
				type = "Linear",
				ig = true,
				boundingRadiusMod = 0,
				width = 60,
				speed = 2500,
				collision = {
					wall = true,
					minion = true,
					hero = true
				}
			},
			slot = player:spellSlot(_E),
			damage = function(arg_259_0, arg_259_1)
				-- function 259
				local slot_259_0 = arg_259_1 or player
				local slot_259_1 = common.GetTotalAP(slot_259_0)
				local slot_259_2 = (({
					80,
					130,
					180,
					230,
					280
				})[slot_259_0:spellSlot(_E).level] or 0) + slot_259_1 * 0.8
				local slot_259_3 = common.CAP(arg_259_0, slot_259_2, slot_259_0)

				return slot_15_48.get_damage(slot_259_3, arg_259_0, slot_259_0, 1)
			end
		},
		R = {
			ignore_obj_radius = 1050,
			prediction = {
				delay = 0.25,
				range = 950,
				speed = 2200,
				type = "InRange",
				radius = 950
			},
			slot = player:spellSlot(_R),
			damage = function(arg_260_0, arg_260_1, arg_260_2)
				-- function 260
				return 0
			end
		}
	}
	slot_15_48.Jinx = {
		Q = {
			prediction = {
				delay = 0,
				range = 0,
				type = "InRange",
				radius = 0,
				speed = math.huge
			},
			slot = player:spellSlot(_Q),
			damage = function(arg_261_0, arg_261_1)
				-- function 261
				return 0
			end
		},
		W = {
			ignore_obj_radius = 1400,
			prediction = {
				delay = 0.6,
				range = 1400,
				type = "Linear",
				width2 = 65,
				boundingRadiusMod = 1,
				width = 60,
				speed = 3300,
				collision = {
					wall = true,
					minion = true,
					hero = true
				}
			},
			slot = player:spellSlot(_W),
			damage = function(arg_262_0, arg_262_1, arg_262_2)
				-- function 262
				local slot_262_0 = arg_262_1 or player
				local slot_262_1 = common.GetTotalAD(slot_262_0)
				local slot_262_2 = (({
					10,
					60,
					110,
					160,
					210
				})[slot_262_0:spellSlot(_W).level] or 0) + slot_262_1 * 1.4
				local slot_262_3 = common.CAD(arg_262_0, slot_262_2, slot_262_0)

				return slot_15_48.get_damage(slot_262_3, arg_262_0, slot_262_0, 2)
			end
		},
		E = {
			ignore_obj_radius = 800,
			prediction = {
				delay = 1,
				range = 925,
				boundingRadiusMod = 0,
				type = "Circular",
				radius = 115,
				speed = math.huge
			},
			slot = player:spellSlot(_E),
			damage = function(arg_263_0, arg_263_1)
				-- function 263
				local slot_263_0 = arg_263_1 or player
				local slot_263_1 = common.GetTotalAP(slot_263_0)
				local slot_263_2 = (({
					70,
					120,
					170,
					220,
					270
				})[slot_263_0:spellSlot(_E).level] or 0) + slot_263_1 * 1
				local slot_263_3 = common.CAP(arg_263_0, slot_263_2, slot_263_0)

				return slot_15_48.get_damage(slot_263_3, arg_263_0, slot_263_0, 1)
			end
		},
		R = {
			ignore_obj_radius = 1050,
			prediction = {
				delay = 0.6,
				range = 5000,
				type = "Linear",
				width2 = 180,
				boundingRadiusMod = 1,
				width = 140,
				speed = 1700,
				collision = {
					wall = true,
					minion = false,
					hero = true
				}
			},
			slot = player:spellSlot(_R),
			damage = function(arg_264_0, arg_264_1)
				-- function 264
				local slot_264_0 = arg_264_1 or player
				local slot_264_1 = common.GetBonusAD(slot_264_0)
				local slot_264_2 = {
					0.25,
					0.3,
					0.35
				}
				local slot_264_3 = {
					325,
					475,
					625
				}
				local slot_264_4 = slot_264_0:spellSlot(_R).level
				local slot_264_5 = slot_264_3[slot_264_4] or 0
				local slot_264_6 = slot_264_2[slot_264_4] or 0
				local slot_264_7 = (arg_264_0.maxHealth - arg_264_0.health) * slot_264_6
				local slot_264_8 = slot_264_5 + slot_264_1 * 1.5 + slot_264_7
				local slot_264_9 = math.modf(arg_264_0.pos:dist(slot_264_0.pos) / 100)
				local slot_264_10 = slot_264_8 * ((10 + math.min(15, slot_264_9) * 6) / 100) - 10
				local slot_264_11 = common.CAD(arg_264_0, slot_264_10, slot_264_0)

				return slot_15_48.get_damage(slot_264_11, arg_264_0, slot_264_0, 2)
			end
		}
	}
	slot_15_48.KogMaw = {
		Q = {
			prediction = {
				delay = 0.25,
				range = 1200,
				type = "Linear",
				width2 = 70,
				boundingRadiusMod = 1,
				width = 70,
				speed = 1650,
				collision = {
					wall = true,
					minion = true,
					hero = true
				}
			},
			slot = player:spellSlot(_Q),
			damage = function(arg_265_0, arg_265_1)
				-- function 265
				local slot_265_0 = arg_265_1 or player
				local slot_265_1 = common.GetTotalAP(slot_265_0)
				local slot_265_2 = (({
					90,
					140,
					190,
					240,
					290
				})[slot_265_0:spellSlot(_Q).level] or 0) + slot_265_1 * 0.7
				local slot_265_3 = common.CAP(arg_265_0, slot_265_2, slot_265_0)

				return slot_15_48.get_damage(slot_265_3, arg_265_0, slot_265_0, 1)
			end
		},
		W = {
			ignore_obj_radius = 1400,
			prediction = {
				delay = 0.25,
				range = 950,
				speed = 2200,
				type = "InRange",
				radius = 950,
				range1 = {
					130,
					150,
					170,
					190,
					210
				}
			},
			slot = player:spellSlot(_W),
			damage = function(arg_266_0, arg_266_1)
				-- function 266
				return 0
			end
		},
		E = {
			ignore_obj_radius = 800,
			prediction = {
				delay = 0.25,
				range = 1350,
				type = "Linear",
				width2 = 120,
				boundingRadiusMod = 1,
				width = 120,
				speed = 1400,
				collision = {
					wall = true,
					minion = false,
					hero = false
				}
			},
			slot = player:spellSlot(_E),
			damage = function(arg_267_0, arg_267_1)
				-- function 267
				local slot_267_0 = arg_267_1 or player
				local slot_267_1 = common.GetTotalAP(slot_267_0)
				local slot_267_2 = (({
					70,
					110,
					150,
					190,
					230
				})[slot_267_0:spellSlot(_E).level] or 0) + slot_267_1 * 0.65
				local slot_267_3 = common.CAP(arg_267_0, slot_267_2, slot_267_0)

				return slot_15_48.get_damage(slot_267_3, arg_267_0, slot_267_0, 1)
			end
		},
		R = {
			ignore_obj_radius = 1050,
			prediction = {
				delay = 1,
				range = 1300,
				radius2 = 120,
				boundingRadiusMod = 0,
				type = "Circular",
				radius = 120,
				range1 = {
					1300,
					1550,
					1800
				},
				speed = math.huge
			},
			slot = player:spellSlot(_R),
			damage = function(arg_268_0, arg_268_1)
				-- function 268
				local slot_268_0 = arg_268_1 or player
				local slot_268_1 = common.GetBonusAD(slot_268_0)
				local slot_268_2 = common.GetTotalAP(slot_268_0)
				local slot_268_3 = (({
					100,
					140,
					180
				})[slot_268_0:spellSlot(_R).level] or 0) + slot_268_2 * 0.35 + slot_268_1 * 0.75
				local slot_268_4 = common.CAP(arg_268_0, slot_268_3, slot_268_0)

				return slot_15_48.get_damage(slot_268_4, arg_268_0, slot_268_0, 1)
			end
		}
	}
	slot_15_48.Samira = {
		Q = {
			prediction = {
				delay = 0.25,
				range = 850,
				type = "Linear",
				boundingRadiusMod = 0,
				width = 70,
				speed = 2600,
				collision = {
					wall = true,
					minion = true,
					hero = true
				}
			},
			slot = player:spellSlot(_Q),
			damage = function(arg_269_0, arg_269_1)
				-- function 269
				local slot_269_0 = arg_269_1 or player
				local slot_269_1 = common.GetTotalAD(slot_269_0)
				local slot_269_2 = {
					0,
					5,
					10,
					15,
					20
				}
				local slot_269_3 = {
					0.95,
					1.025,
					1.1,
					1.175,
					1.25
				}
				local slot_269_4 = slot_269_0:spellSlot(_Q).level
				local slot_269_5 = (slot_269_2[slot_269_4] or 0) + slot_269_1 * (slot_269_3[slot_269_4] or 0)
				local slot_269_6 = common.CAD(arg_269_0, slot_269_5, slot_269_0)

				return slot_15_48.get_damage(slot_269_6, arg_269_0, slot_269_0, 2)
			end
		},
		W = {
			ignore_obj_radius = 0,
			prediction = {
				delay = 0,
				range = 325,
				type = "InRange",
				radius = 325,
				speed = math.huge
			},
			slot = player:spellSlot(_W),
			damage = function(arg_270_0, arg_270_1)
				-- function 270
				local slot_270_0 = arg_270_1 or player
				local slot_270_1 = common.GetBonusAD(slot_270_0)
				local slot_270_2 = (({
					20,
					35,
					50,
					65,
					80
				})[slot_270_0:spellSlot(_W).level] or 0) + slot_270_1 * 0.6
				local slot_270_3 = common.CAD(arg_270_0, slot_270_2, slot_270_0)

				return slot_15_48.get_damage(slot_270_3, arg_270_0, slot_270_0, 2)
			end
		},
		E = {
			ignore_obj_radius = 800,
			prediction = {
				delay = 0,
				range = 665,
				speed = 1600,
				type = "InRange",
				radius = 665
			},
			slot = player:spellSlot(_E),
			damage = function(arg_271_0, arg_271_1)
				-- function 271
				local slot_271_0 = arg_271_1 or player
				local slot_271_1 = common.GetBonusAD(slot_271_0)
				local slot_271_2 = (({
					50,
					60,
					70,
					80,
					90
				})[slot_271_0:spellSlot(_E).level] or 0) + slot_271_1 * 0.2
				local slot_271_3 = common.CAP(arg_271_0, slot_271_2, slot_271_0)

				return slot_15_48.get_damage(slot_271_3, arg_271_0, slot_271_0, 1)
			end
		},
		R = {
			ignore_obj_radius = 1050,
			prediction = {
				delay = 0,
				range = 600,
				type = "InRange",
				radius = 600,
				speed = math.huge
			},
			slot = player:spellSlot(_R),
			damage = function(arg_272_0, arg_272_1)
				-- function 272
				local slot_272_0 = arg_272_1 or player
				local slot_272_1 = common.GetTotalAD(slot_272_0)
				local slot_272_2 = (({
					5,
					15,
					25
				})[slot_272_0:spellSlot(_R).level] or 0) + slot_272_1 * 0.45
				local slot_272_3 = common.CAD(arg_272_0, slot_272_2, slot_272_0)

				return slot_15_48.get_damage(slot_272_3, arg_272_0, slot_272_0, 2)
			end
		}
	}
	slot_15_48.Khazix = {
		Q = {
			prediction = {
				delay = 0,
				range = 325,
				type = "InRange",
				radius = 325,
				speed = math.huge
			},
			slot = player:spellSlot(_Q),
			damage = function(arg_273_0, arg_273_1)
				-- function 273
				local slot_273_0 = arg_273_1 or player
				local slot_273_1 = common.GetBonusAD(slot_273_0)
				local slot_273_2 = (({
					80,
					105,
					130,
					155,
					180
				})[slot_273_0:spellSlot(_Q).level] or 0) + slot_273_1 * 1.1
				local slot_273_3 = common.CAD(arg_273_0, slot_273_2, slot_273_0)

				return slot_15_48.get_damage(slot_273_3, arg_273_0, slot_273_0, 2)
			end
		},
		W = {
			ignore_obj_radius = 0,
			prediction = {
				delay = 0.25,
				range = 1025,
				type = "Linear",
				boundingRadiusMod = 0,
				width = 70,
				speed = 1700,
				collision = {
					wall = true,
					minion = true,
					hero = true
				}
			},
			slot = player:spellSlot(_W),
			damage = function(arg_274_0, arg_274_1)
				-- function 274
				local slot_274_0 = arg_274_1 or player
				local slot_274_1 = common.GetBonusAD(slot_274_0)
				local slot_274_2 = (({
					85,
					115,
					145,
					175,
					205
				})[slot_274_0:spellSlot(_W).level] or 0) + slot_274_1 * 1
				local slot_274_3 = common.CAD(arg_274_0, slot_274_2, slot_274_0)

				return slot_15_48.get_damage(slot_274_3, arg_274_0, slot_274_0, 2)
			end
		},
		E = {
			ignore_obj_radius = 900,
			prediction = {
				delay = 0,
				range = 700,
				speed = 2000,
				boundingRadiusMod = 0,
				type = "Circular",
				radius = 50
			},
			slot = player:spellSlot(_E),
			damage = function(arg_275_0, arg_275_1)
				-- function 275
				local slot_275_0 = arg_275_1 or player
				local slot_275_1 = common.GetBonusAD(slot_275_0)
				local slot_275_2 = (({
					65,
					100,
					135,
					170,
					205
				})[slot_275_0:spellSlot(_E).level] or 0) + slot_275_1 * 0.2
				local slot_275_3 = common.CAD(arg_275_0, slot_275_2, slot_275_0)

				return slot_15_48.get_damage(slot_275_3, arg_275_0, slot_275_0, 2)
			end
		},
		R = {
			ignore_obj_radius = 0,
			prediction = {
				delay = 0,
				range = 0,
				type = "InRange",
				radius = 0,
				speed = math.huge
			},
			slot = player:spellSlot(_R),
			damage = function(arg_276_0, arg_276_1)
				-- function 276
				return 0
			end
		}
	}
	slot_15_48.Tristana = {
		Q = {
			prediction = {
				delay = 0,
				range = 325,
				type = "InRange",
				radius = 325,
				speed = math.huge
			},
			slot = player:spellSlot(_Q),
			damage = function(arg_277_0, arg_277_1)
				-- function 277
				local slot_277_0

				slot_277_0 = arg_277_1 or player

				return 0
			end
		},
		W = {
			ignore_obj_radius = 0,
			prediction = {
				delay = 0.25,
				range = 900,
				fixedRange = true,
				speed = 1100,
				boundingRadiusMod = 0,
				type = "Circular",
				radius = 175
			},
			slot = player:spellSlot(_W),
			damage = function(arg_278_0, arg_278_1)
				-- function 278
				local slot_278_0 = arg_278_1 or player
				local slot_278_1 = common.GetTotalAP(slot_278_0)
				local slot_278_2 = common.GetBonusAD(slot_278_0)
				local slot_278_3 = (({
					70,
					105,
					140,
					175,
					210
				})[slot_278_0:spellSlot(_W).level] or 0) + slot_278_1 * 0.5 + slot_278_2 * 0.75
				local slot_278_4 = common.CAP(arg_278_0, slot_278_3, slot_278_0)

				return slot_15_48.get_damage(slot_278_4, arg_278_0, slot_278_0, 1)
			end
		},
		E = {
			ignore_obj_radius = 525,
			prediction = {
				delay = 0.226,
				range = 525,
				speed = 2400,
				type = "InRange",
				radius = 525
			},
			slot = player:spellSlot(_E),
			damage = function(arg_279_0, arg_279_1, arg_279_2)
				-- function 279
				local slot_279_0 = arg_279_1 or player
				local slot_279_1 = common.GetBonusAD(slot_279_0)
				local slot_279_2 = common.GetTotalAP(slot_279_0)
				local slot_279_3 = {
					60,
					70,
					80,
					90,
					100
				}
				local slot_279_4 = {
					1,
					1.1,
					1.2,
					1.3,
					1.4
				}
				local slot_279_5 = slot_279_0:spellSlot(_E).level
				local slot_279_6 = slot_279_3[slot_279_5] or 0
				local slot_279_7 = slot_279_1 * (slot_279_4[slot_279_5] or 0)
				local slot_279_8 = slot_279_6 + slot_279_2 * 0.5 + slot_279_7

				if arg_279_0.buff.tristanaecharge or arg_279_2 then
					arg_279_2 = arg_279_2 or arg_279_0.buff.tristanaecharge.stacks
					arg_279_2 = math.min(arg_279_2, 4)

					local slot_279_9 = 0.3 * arg_279_2

					if slot_279_9 > 0 then
						slot_279_8 = slot_279_8 + slot_279_8 * slot_279_9
					end
				end

				local slot_279_10 = common.CAP(arg_279_0, slot_279_8, slot_279_0)

				return slot_15_48.get_damage(slot_279_10, arg_279_0, slot_279_0, 1)
			end
		},
		R = {
			ignore_obj_radius = 0,
			prediction = {
				delay = 0.25,
				range = 525,
				type = "InRange",
				radius = 525,
				speed = math.huge
			},
			slot = player:spellSlot(_R),
			damage = function(arg_280_0, arg_280_1)
				-- function 280
				local slot_280_0 = arg_280_1 or player
				local slot_280_1 = common.GetTotalAP(slot_280_0)
				local slot_280_2 = common.GetBonusAD(slot_280_0)
				local slot_280_3 = (({
					275,
					325,
					375
				})[slot_280_0:spellSlot(_R).level] or 0) + slot_280_1 * 1 + slot_280_2 * 0.7
				local slot_280_4 = common.CAP(arg_280_0, slot_280_3, slot_280_0)

				return slot_15_48.get_damage(slot_280_4, arg_280_0, slot_280_0, 1)
			end
		}
	}
	slot_15_48.Ashe = {
		Q = {
			prediction = {
				delay = 0,
				range = 0,
				type = "InRange",
				radius = 0,
				speed = math.huge
			},
			slot = player:spellSlot(_Q),
			damage = function(arg_281_0, arg_281_1)
				-- function 281
				local slot_281_0

				slot_281_0 = arg_281_1 or player

				return 0
			end
		},
		W = {
			ignore_obj_radius = 0,
			prediction = {
				delay = 0.25,
				range = 1200,
				type = "Linear",
				width2 = 40,
				boundingRadiusMod = 0,
				width = 20,
				speed = 2000,
				collision = {
					wall = true,
					minion = true,
					hero = true
				}
			},
			slot = player:spellSlot(_W),
			damage = function(arg_282_0, arg_282_1)
				-- function 282
				local slot_282_0 = arg_282_1 or player
				local slot_282_1 = common.GetBonusAD(slot_282_0)
				local slot_282_2 = (({
					60,
					95,
					130,
					165,
					200
				})[slot_282_0:spellSlot(_W).level] or 0) + slot_282_1 * 1
				local slot_282_3 = common.CAD(arg_282_0, slot_282_2, slot_282_0)

				return slot_15_48.get_damage(slot_282_3, arg_282_0, slot_282_0, 2)
			end
		},
		E = {
			ignore_obj_radius = 0,
			prediction = {
				delay = 0,
				range = 0,
				speed = 0,
				type = "InRange",
				radius = 0
			},
			slot = player:spellSlot(_E),
			damage = function(arg_283_0, arg_283_1, arg_283_2)
				-- function 283
				local slot_283_0

				slot_283_0 = arg_283_1 or player

				return 0
			end
		},
		R = {
			ignore_obj_radius = 0,
			prediction = {
				delay = 0.25,
				range = 9999,
				type = "Linear",
				width2 = 140,
				boundingRadiusMod = 0,
				width = 50,
				speed = 1600,
				collision = {
					wall = true,
					minion = false,
					hero = true
				}
			},
			slot = player:spellSlot(_R),
			damage = function(arg_284_0, arg_284_1)
				-- function 284
				local slot_284_0 = arg_284_1 or player
				local slot_284_1 = common.GetTotalAP(slot_284_0)
				local slot_284_2 = (({
					200,
					400,
					600
				})[slot_284_0:spellSlot(_R).level] or 0) + slot_284_1 * 1.2
				local slot_284_3 = common.CAP(arg_284_0, slot_284_2, slot_284_0)

				return slot_15_48.get_damage(slot_284_3, arg_284_0, slot_284_0, 1)
			end
		}
	}
	slot_15_48.Xerath = {
		Q = {
			prediction = {
				delay = 0,
				range = 1450,
				type = "Linear",
				ig = true,
				boundingRadiusMod = 0,
				width = 123,
				speed = math.huge,
				collision = {
					wall = false,
					minion = false,
					hero = false
				}
			},
			slot = player:spellSlot(_Q),
			damage = function(arg_285_0, arg_285_1)
				-- function 285
				local slot_285_0 = arg_285_1 or player
				local slot_285_1 = common.GetTotalAP(slot_285_0)
				local slot_285_2 = (({
					70,
					110,
					150,
					190,
					230
				})[slot_285_0:spellSlot(_Q).level] or 0) + slot_285_1 * 0.85
				local slot_285_3 = common.CAP(arg_285_0, slot_285_2, slot_285_0)

				return slot_15_48.get_damage(slot_285_3, arg_285_0, slot_285_0, 1)
			end
		},
		Q2 = {
			prediction = {
				delay = 0.528,
				range = 1450,
				type = "Linear",
				boundingRadiusMod = 1,
				width = 70,
				speed = math.huge,
				collision = {
					wall = false,
					minion = false,
					hero = false
				}
			},
			slot = player:spellSlot(_Q),
			damage = function(arg_286_0, arg_286_1)
				-- function 286
				local slot_286_0 = arg_286_1 or player
				local slot_286_1 = common.GetTotalAP(slot_286_0)
				local slot_286_2 = (({
					70,
					110,
					150,
					190,
					230
				})[slot_286_0:spellSlot(_Q).level] or 0) + slot_286_1 * 0.85
				local slot_286_3 = common.CAP(arg_286_0, slot_286_2, slot_286_0)

				return slot_15_48.get_damage(slot_286_3, arg_286_0, slot_286_0, 1)
			end
		},
		W = {
			ignore_obj_radius = 0,
			prediction = {
				delay = 0.75,
				range = 990,
				boundingRadiusMod = 0,
				type = "Circular",
				radius = 200,
				speed = math.huge
			},
			slot = player:spellSlot(_W),
			damage = function(arg_287_0, arg_287_1)
				-- function 287
				local slot_287_0 = arg_287_1 or player
				local slot_287_1 = common.GetTotalAP(slot_287_0)
				local slot_287_2 = (({
					60,
					95,
					130,
					165,
					200
				})[slot_287_0:spellSlot(_W).level] or 0) + slot_287_1 * 0.6
				local slot_287_3 = common.CAP(arg_287_0, slot_287_2, slot_287_0)

				return slot_15_48.get_damage(slot_287_3, arg_287_0, slot_287_0, 1)
			end
		},
		E = {
			ignore_obj_radius = 0,
			prediction = {
				delay = 0.25,
				range = 1025,
				type = "Linear",
				width2 = 80,
				boundingRadiusMod = 1,
				width = 60,
				speed = 1400,
				collision = {
					wall = true,
					minion = true,
					hero = true
				}
			},
			slot = player:spellSlot(_E),
			damage = function(arg_288_0, arg_288_1, arg_288_2)
				-- function 288
				local slot_288_0 = arg_288_1 or player
				local slot_288_1 = common.GetTotalAP(slot_288_0)
				local slot_288_2 = (({
					80,
					110,
					140,
					170,
					200
				})[slot_288_0:spellSlot(_E).level] or 0) + slot_288_1 * 0.45
				local slot_288_3 = common.CAP(arg_288_0, slot_288_2, slot_288_0)

				return slot_15_48.get_damage(slot_288_3, arg_288_0, slot_288_0, 1)
			end
		},
		R = {
			ignore_obj_radius = 0,
			prediction = {
				delay = 0.5,
				range = 5000,
				boundingRadiusMod = 0,
				type = "Circular",
				radius = 200,
				speed = math.huge
			},
			slot = player:spellSlot(_R),
			damage = function(arg_289_0, arg_289_1)
				-- function 289
				local slot_289_0 = arg_289_1 or player
				local slot_289_1 = common.GetTotalAP(slot_289_0)
				local slot_289_2 = (({
					180,
					230,
					280
				})[slot_289_0:spellSlot(_R).level] or 0) + slot_289_1 * 0.45
				local slot_289_3 = common.CAP(arg_289_0, slot_289_2, slot_289_0)

				return (slot_15_48.get_damage(slot_289_3, arg_289_0, slot_289_0, 1))
			end
		}
	}
	slot_15_48.Naafiri = {
		Q = {
			prediction = {
				delay = 0.25,
				range = 880,
				type = "Linear",
				boundingRadiusMod = 0,
				width = 40,
				speed = 1700,
				collision = {
					wall = true,
					minion = false,
					hero = false
				}
			},
			slot = player:spellSlot(_Q),
			damage = function(arg_290_0, arg_290_1, arg_290_2)
				-- function 290
				local slot_290_0 = arg_290_1 or player
				local slot_290_1 = common.GetBonusAD(slot_290_0)
				local slot_290_2 = {
					30,
					45,
					60,
					75,
					90
				}
				local slot_290_3 = slot_290_0:spellSlot(_Q).level
				local slot_290_4 = (slot_290_2[slot_290_3] or 0) + slot_290_1 * 0.4

				if arg_290_0.buff.NaafiriQBleed and slot_290_0:spellSlot(_Q).name == "NaafiriQRecast" or arg_290_2 then
					slot_290_4 = (({
						30,
						45,
						60,
						75,
						90
					})[slot_290_3] or 0) + slot_290_1 * 0.4
				end

				local slot_290_5 = common.CAD(arg_290_0, slot_290_4, slot_290_0)

				return slot_15_48.get_damage(slot_290_5, arg_290_0, slot_290_0, 2)
			end
		},
		W = {
			ignore_obj_radius = 0,
			prediction = {
				delay = 0,
				range = 700,
				type = "InRange",
				radius = 700,
				speed = math.huge
			},
			slot = player:spellSlot(_W),
			damage = function(arg_291_0, arg_291_1)
				-- function 291
				local slot_291_0 = arg_291_1 or player
				local slot_291_1 = common.GetBonusAD(slot_291_0)
				local slot_291_2 = {
					30,
					70,
					110,
					150,
					190
				}
				local slot_291_3 = {
					3,
					7,
					11,
					15,
					19
				}
				local slot_291_4 = slot_291_0:spellSlot(_W).level
				local slot_291_5 = slot_291_2[slot_291_4] or 0
				local slot_291_6 = slot_291_3[slot_291_4] or 0
				local slot_291_7 = slot_291_5 + slot_291_1 * 0.8
				local slot_291_8 = slot_291_6 + slot_291_1 * 0.08
				local slot_291_9 = common.CAD(arg_291_0, slot_291_7, slot_291_0)
				local slot_291_10 = 0

				for iter_291_0, iter_291_1 in ipairs(common.GetAlliedMinions()) do
					if iter_291_1.name == "Packmate" and iter_291_1.owner and iter_291_1.owner.networkID == slot_291_0.networkID then
						slot_291_10 = slot_291_10 + 1
					end
				end

				if slot_291_10 > 0 then
					slot_291_9 = slot_291_9 + common.CAD(arg_291_0, slot_291_8, slot_291_0) * (slot_291_10 * 2)
				end

				return slot_15_48.get_damage(slot_291_9, arg_291_0, slot_291_0, 2)
			end
		},
		E = {
			ignore_obj_radius = 0,
			prediction = {
				delay = 0,
				range = 350,
				speed = 950,
				boundingRadiusMod = 0,
				type = "Circular",
				radius = 50
			},
			slot = player:spellSlot(_E),
			damage = function(arg_292_0, arg_292_1, arg_292_2)
				-- function 292
				local slot_292_0 = arg_292_1 or player
				local slot_292_1 = common.GetBonusAD(slot_292_0)
				local slot_292_2 = (({
					100,
					160,
					220,
					280,
					340
				})[slot_292_0:spellSlot(_E).level] or 0) + slot_292_1 * 1.3
				local slot_292_3 = common.CAD(arg_292_0, slot_292_2, slot_292_0)

				return slot_15_48.get_damage(slot_292_3, arg_292_0, slot_292_0, 1)
			end
		},
		R = {
			ignore_obj_radius = 0,
			prediction = {
				delay = 0,
				range = 700,
				type = "InRange",
				radius = 700,
				speed = math.huge
			},
			slot = player:spellSlot(_R),
			damage = function(arg_293_0, arg_293_1)
				-- function 293
				return 0
			end
		}
	}
	slot_15_48.Viktor = {
		Q = {
			prediction = {
				delay = 0.25,
				range = 600,
				type = "InRange",
				speed = 2000
			},
			slot = player:spellSlot(_Q),
			damage = function(arg_294_0, arg_294_1, arg_294_2)
				-- function 294
				local slot_294_0 = arg_294_1 or player
				local slot_294_1 = common.GetTotalAP(slot_294_0)
				local slot_294_2 = (({
					60,
					75,
					90,
					105,
					120
				})[slot_294_0:spellSlot(_Q).level] or 0) + slot_294_1 * 0.4
				local slot_294_3 = common.CAP(arg_294_0, slot_294_2, slot_294_0)

				return slot_15_48.get_damage(slot_294_3, arg_294_0, slot_294_0, 1)
			end
		},
		W = {
			ignore_obj_radius = 0,
			prediction = {
				delay = 1,
				range = 800,
				boundingRadiusMod = 0,
				type = "Circular",
				radius = 50,
				speed = math.huge
			},
			slot = player:spellSlot(_W),
			damage = function(arg_295_0, arg_295_1)
				-- function 295
				return 0
			end
		},
		E = {
			ignore_obj_radius = 0,
			prediction = {
				delay = 0,
				range = 1100,
				type = "Linear",
				boundingRadiusMod = 1,
				width = 45,
				speed = 1050
			},
			slot = player:spellSlot(_E),
			damage = function(arg_296_0, arg_296_1, arg_296_2)
				-- function 296
				local slot_296_0 = arg_296_1 or player
				local slot_296_1 = common.GetTotalAP(slot_296_0)
				local slot_296_2 = (({
					70,
					110,
					150,
					190,
					230
				})[slot_296_0:spellSlot(_E).level] or 0) + slot_296_1 * 0.5
				local slot_296_3 = common.CAP(arg_296_0, slot_296_2, slot_296_0)

				return slot_15_48.get_damage(slot_296_3, arg_296_0, slot_296_0, 1)
			end
		},
		R = {
			ignore_obj_radius = 0,
			prediction = {
				delay = 0.25,
				range = 800,
				boundingRadiusMod = 0,
				type = "Circular",
				radius = 50,
				speed = math.huge
			},
			slot = player:spellSlot(_R),
			damage = function(arg_297_0, arg_297_1)
				-- function 297
				local slot_297_0 = arg_297_1 or player
				local slot_297_1 = common.GetTotalAP(slot_297_0)
				local slot_297_2 = (({
					100,
					175,
					250
				})[slot_297_0:spellSlot(_R).level] or 0) + slot_297_1 * 0.5
				local slot_297_3 = common.CAP(arg_297_0, slot_297_2, slot_297_0)

				return slot_15_48.get_damage(slot_297_3, arg_297_0, slot_297_0, 1)
			end
		}
	}
	slot_15_48.Graves = {
		Q = {
			prediction = {
				fast = true,
				type = "Linear",
				boundingRadiusMod = 0,
				width = 40,
				speed = 3000,
				delay = 0.25,
				range = 800,
				ig = true,
				collision = {
					wall = true,
					minion = false,
					hero = false
				}
			},
			slot = player:spellSlot(_Q),
			damage = function(arg_298_0, arg_298_1, arg_298_2)
				-- function 298
				local slot_298_0 = arg_298_1 or player
				local slot_298_1 = common.GetBonusAD(slot_298_0)
				local slot_298_2 = {
					45,
					65,
					85,
					105,
					125
				}
				local slot_298_3 = slot_298_0:spellSlot(_Q).level
				local slot_298_4 = slot_298_2[slot_298_3] or 0
				local slot_298_5 = slot_298_4 + slot_298_1 * 0.8
				local slot_298_6 = common.CAD(arg_298_0, slot_298_5, slot_298_0)

				if arg_298_2 then
					local slot_298_7 = 0.15 + 0.25 * slot_298_3
					local slot_298_8 = ({
						85,
						120,
						155,
						190,
						225
					})[slot_298_3]
					local slot_298_9 = slot_298_4 + slot_298_1 * slot_298_7

					slot_298_6 = slot_298_6 + common.CAD(arg_298_0, slot_298_9, slot_298_0)
				end

				return slot_15_48.get_damage(slot_298_6, arg_298_0, slot_298_0, 2)
			end
		},
		W = {
			ignore_obj_radius = 0,
			prediction = {
				delay = 0.25,
				range = 800,
				speed = 1500,
				boundingRadiusMod = 0,
				type = "Circular",
				radius = 50
			},
			slot = player:spellSlot(_W),
			damage = function(arg_299_0, arg_299_1)
				-- function 299
				local slot_299_0 = arg_299_1 or player
				local slot_299_1 = common.GetTotalAP(slot_299_0)
				local slot_299_2 = (({
					45,
					60,
					75,
					90,
					105
				})[slot_299_0:spellSlot(_W).level] or 0) + slot_299_1 * 0.6
				local slot_299_3 = common.CAP(arg_299_0, slot_299_2, slot_299_0)

				return slot_15_48.get_damage(slot_299_3, arg_299_0, slot_299_0, 1)
			end
		},
		E = {
			ignore_obj_radius = 0,
			prediction = {
				delay = 0,
				range = 425,
				type = "InRange",
				speed = math.huge
			},
			slot = player:spellSlot(_E),
			damage = function(arg_300_0, arg_300_1, arg_300_2)
				-- function 300
				return 0
			end
		},
		R = {
			ignore_obj_radius = 0,
			prediction = {
				delay = 0.25,
				range = 1690,
				type = "Linear",
				slow = true,
				boundingRadiusMod = 0,
				width = 1,
				speed = 2100,
				collision = {
					wall = true,
					minion = false,
					hero = false
				}
			},
			slot = player:spellSlot(_R),
			damage = function(arg_301_0, arg_301_1)
				-- function 301
				local slot_301_0 = arg_301_1 or player
				local slot_301_1 = common.GetBonusAD(slot_301_0)
				local slot_301_2 = (({
					275,
					425,
					575
				})[slot_301_0:spellSlot(_R).level] or 0) + slot_301_1 * 1.5
				local slot_301_3 = common.CAD(arg_301_0, slot_301_2, slot_301_0)

				if arg_301_0.pos:dist(player.pos) > 925 then
					slot_301_3 = slot_301_3 * 0.8
				end

				return slot_15_48.get_damage(slot_301_3, arg_301_0, slot_301_0, 2)
			end
		}
	}
	slot_15_48.Ryze = {
		Q = {
			prediction = {
				delay = 0.25,
				range = 950,
				type = "Linear",
				width2 = 40,
				boundingRadiusMod = 0,
				width = 30,
				speed = 1700,
				collision = {
					wall = true,
					minion = true,
					hero = true
				}
			},
			slot = player:spellSlot(_Q),
			damage = function(arg_302_0, arg_302_1)
				-- function 302
				if not arg_302_0 then
					return 0
				end

				local slot_302_0 = arg_302_1 or player
				local slot_302_1 = ({
					70,
					90,
					110,
					130,
					150
				})[slot_302_0:spellSlot(_Q).level] or 0
				local slot_302_2 = common.GetTotalAP(slot_302_0) * 0.55
				local slot_302_3 = slot_302_0.maxMana - (slot_302_0.baseMana + slot_302_0.baseManaPerLevel * slot_302_0.level)

				if slot_302_3 < 0 then
					slot_302_3 = 0
				end

				local slot_302_4 = slot_302_3 * 0.02
				local slot_302_5 = slot_302_1 + slot_302_2 + slot_302_4

				if arg_302_0.buff.ryzee and arg_302_0.buff.ryzee.endTime - game.time > 0.25 + slot_302_0.pos:dist(arg_302_0.pos) / 1700 and slot_302_0:spellSlot(_R).level > 0 then
					slot_302_5 = slot_302_5 + slot_302_5 * (0.1 + 0.3 * slot_302_0:spellSlot(_R).level)
				end

				local slot_302_6 = common.CAP(arg_302_0, slot_302_5, slot_302_0)

				return slot_15_48.get_damage(slot_302_6, arg_302_0, slot_302_0, 1)
			end
		},
		W = {
			ignore_obj_radius = 0,
			prediction = {
				delay = 0,
				range = 550,
				type = "InRange",
				speed = math.huge
			},
			slot = player:spellSlot(_W),
			damage = function(arg_303_0, arg_303_1)
				-- function 303
				local slot_303_0 = arg_303_1 or player
				local slot_303_1 = common.GetTotalAP(slot_303_0)
				local slot_303_2 = ({
					80,
					110,
					140,
					170,
					200
				})[slot_303_0:spellSlot(_W).level] or 0
				local slot_303_3 = slot_303_0.maxMana - (slot_303_0.baseMana + slot_303_0.baseManaPerLevel * slot_303_0.level)

				if slot_303_3 < 0 then
					slot_303_3 = 0
				end

				local slot_303_4 = slot_303_3 * 0.04
				local slot_303_5 = slot_303_2 + slot_303_1 * 0.7 + slot_303_4
				local slot_303_6 = common.CAP(arg_303_0, slot_303_5, slot_303_0)

				return slot_15_48.get_damage(slot_303_6, arg_303_0, slot_303_0, 1)
			end
		},
		E = {
			ignore_obj_radius = 0,
			prediction = {
				delay = 0,
				range = 550,
				type = "InRange",
				speed = math.huge
			},
			slot = player:spellSlot(_E),
			damage = function(arg_304_0, arg_304_1, arg_304_2)
				-- function 304
				local slot_304_0 = arg_304_1 or player
				local slot_304_1 = common.GetTotalAP(slot_304_0)
				local slot_304_2 = ({
					60,
					90,
					120,
					150,
					180
				})[slot_304_0:spellSlot(_E).level] or 0
				local slot_304_3 = slot_304_0.maxMana - (slot_304_0.baseMana + slot_304_0.baseManaPerLevel * slot_304_0.level)

				if slot_304_3 < 0 then
					slot_304_3 = 0
				end

				local slot_304_4 = slot_304_3 * 0.02
				local slot_304_5 = slot_304_2 + slot_304_1 * 0.5 + slot_304_4
				local slot_304_6 = common.CAP(arg_304_0, slot_304_5, slot_304_0)

				return slot_15_48.get_damage(slot_304_6, arg_304_0, slot_304_0, 1)
			end
		},
		R = {
			ignore_obj_radius = 0,
			prediction = {
				delay = 0,
				range = 550,
				type = "InRange",
				speed = math.huge
			},
			slot = player:spellSlot(_R),
			damage = function(arg_305_0, arg_305_1)
				-- function 305
				return 0
			end
		}
	}
	slot_15_48.Briar = {
		Q = {
			prediction = {
				delay = 0.25,
				range = 450,
				type = "InRange",
				speed = math.huge
			},
			slot = player:spellSlot(_Q),
			damage = function(arg_306_0, arg_306_1)
				-- function 306
				if not arg_306_0 then
					return 0
				end

				local slot_306_0 = arg_306_1 or player
				local slot_306_1 = (({
					60,
					90,
					120,
					150,
					180
				})[slot_306_0:spellSlot(_Q).level] or 0) + common.GetBonusAD(slot_306_0) * 0.8
				local slot_306_2 = common.CAD(arg_306_0, slot_306_1, slot_306_0)

				return slot_15_48.get_damage(slot_306_2, arg_306_0, slot_306_0, 2)
			end
		},
		W = {
			ignore_obj_radius = 0,
			prediction = {
				delay = 0,
				range = 300,
				type = "InRange",
				speed = math.huge
			},
			slot = player:spellSlot(_W),
			damage = function(arg_307_0, arg_307_1)
				-- function 307
				return 0
			end
		},
		E = {
			ignore_obj_radius = 0,
			prediction = {
				delay = 0,
				range = 600,
				type = "Linear",
				width2 = 140,
				boundingRadiusMod = 0,
				width = 120,
				speed = 1900,
				collision = {
					wall = true,
					minion = false,
					hero = true
				}
			},
			slot = player:spellSlot(_E),
			damage = function(arg_308_0, arg_308_1, arg_308_2)
				-- function 308
				local slot_308_0 = arg_308_1 or player
				local slot_308_1 = common.GetTotalAP(slot_308_0)
				local slot_308_2 = common.GetBonusAD(slot_308_0)
				local slot_308_3 = (({
					80,
					115,
					150,
					185,
					220
				})[slot_308_0:spellSlot(_E).level] or 0) + slot_308_1 * 1 + slot_308_2 * 1
				local slot_308_4 = common.CAP(arg_308_0, slot_308_3, slot_308_0)

				return slot_15_48.get_damage(slot_308_4, arg_308_0, slot_308_0, 1)
			end
		},
		R = {
			ignore_obj_radius = 0,
			prediction = {
				delay = 1,
				range = 10000,
				type = "Linear",
				width2 = 160,
				boundingRadiusMod = 0,
				width = 70,
				speed = 2000,
				collision = {
					wall = true,
					minion = false,
					hero = true
				}
			},
			slot = player:spellSlot(_R),
			damage = function(arg_309_0, arg_309_1)
				-- function 309
				local slot_309_0 = arg_309_1 or player
				local slot_309_1 = common.GetTotalAP(slot_309_0)
				local slot_309_2 = common.GetBonusAD(slot_309_0)
				local slot_309_3 = (({
					150,
					300,
					450
				})[slot_309_0:spellSlot(_R).level] or 0) + slot_309_1 * 1.2 + slot_309_2 * 0.5
				local slot_309_4 = common.CAP(arg_309_0, slot_309_3, slot_309_0)

				return slot_15_48.get_damage(slot_309_4, arg_309_0, slot_309_0, 1)
			end
		}
	}
	slot_15_48.Sivir = {
		Q = {
			prediction = {
				delay = 0.25,
				range = 1150,
				type = "Linear",
				width2 = 90,
				boundingRadiusMod = 1,
				width = 90,
				speed = 1450,
				collision = {
					wall = true,
					minion = false,
					hero = false
				}
			},
			slot = player:spellSlot(_Q),
			damage = function(arg_310_0, arg_310_1)
				-- function 310
				if not arg_310_0 then
					return 0
				end

				local slot_310_0 = arg_310_1 or player
				local slot_310_1 = ({
					60,
					85,
					110,
					135,
					160
				})[slot_310_0:spellSlot(_Q).level] or 0
				local slot_310_2 = common.GetBonusAD(slot_310_0)
				local slot_310_3 = common.GetTotalAP(slot_310_0)
				local slot_310_4 = slot_310_1 + slot_310_2 * 1 + slot_310_3 * 0.6
				local slot_310_5 = common.CAD(arg_310_0, slot_310_4, slot_310_0)

				return slot_15_48.get_damage(slot_310_5, arg_310_0, slot_310_0, 2)
			end
		},
		W = {
			ignore_obj_radius = 0,
			prediction = {
				delay = 0,
				type = "InRange",
				range = player.attackRange,
				speed = math.huge
			},
			slot = player:spellSlot(_W),
			damage = function(arg_311_0, arg_311_1)
				-- function 311
				return 0
			end
		},
		E = {
			ignore_obj_radius = 0,
			prediction = {
				delay = 0,
				range = 0,
				type = "InRange",
				speed = math.huge
			},
			slot = player:spellSlot(_E),
			damage = function(arg_312_0, arg_312_1, arg_312_2)
				-- function 312
				return 0
			end
		},
		R = {
			ignore_obj_radius = 0,
			prediction = {
				delay = 0,
				range = 0,
				type = "InRange",
				speed = math.huge
			},
			slot = player:spellSlot(_R),
			damage = function(arg_313_0, arg_313_1)
				-- function 313
				return 0
			end
		}
	}
	slot_15_48.Twitch = {
		Q = {
			prediction = {
				delay = 0,
				type = "InRange",
				range = player.attackRange,
				speed = math.huge
			},
			slot = player:spellSlot(_Q),
			damage = function(arg_314_0, arg_314_1)
				-- function 314
				return 0
			end
		},
		W = {
			ignore_obj_radius = 0,
			prediction = {
				delay = 0.25,
				range = 950,
				speed = 1400,
				boundingRadiusMod = 0,
				type = "Circular",
				radius = 50
			},
			slot = player:spellSlot(_W),
			damage = function(arg_315_0, arg_315_1)
				-- function 315
				return 0
			end
		},
		E = {
			ignore_obj_radius = 0,
			prediction = {
				delay = 0,
				range = 1200,
				type = "InRange",
				speed = math.huge
			},
			slot = player:spellSlot(_E),
			damage = function(arg_316_0, arg_316_1, arg_316_2)
				-- function 316
				local slot_316_0 = arg_316_1 or player
				local slot_316_1 = arg_316_0.buff.twitchdeadlyvenom

				if not slot_316_1 then
					return 0
				end

				local slot_316_2 = common.GetTotalAP(slot_316_0)
				local slot_316_3 = common.GetBonusAD(slot_316_0)
				local slot_316_4 = {
					20,
					30,
					40,
					50,
					60
				}
				local slot_316_5 = {
					15,
					20,
					25,
					30,
					35
				}
				local slot_316_6 = slot_316_0:spellSlot(_E).level
				local slot_316_7, slot_316_8 = slot_316_4[slot_316_6] or 0, slot_316_5[slot_316_6] or 0
				local slot_316_9 = slot_316_8 + slot_316_3 * 0.35
				local slot_316_10 = 0
				local slot_316_11 = 0

				if slot_316_1 then
					slot_316_10 = slot_316_1.stacks2
				end

				if preds.damage then
					for iter_316_0, iter_316_1 in pairs(preds.damage) do
						if iter_316_0 == slot_316_0.ptr and iter_316_1.true_aa and iter_316_1.target and iter_316_1.target.ptr == arg_316_0.ptr then
							slot_316_11 = 1

							break
						end
					end

					slot_316_10 = math.min(6, slot_316_10 + slot_316_11)
				end

				local slot_316_12 = slot_316_9 * slot_316_10
				local slot_316_13 = slot_316_2 * 0.3 * slot_316_10
				local slot_316_14 = slot_316_7 + slot_316_12
				local slot_316_15 = common.CAD(arg_316_0, slot_316_14, slot_316_0)
				local slot_316_16 = common.CAP(arg_316_0, slot_316_13, slot_316_0)

				return slot_15_48.get_damage(slot_316_16, arg_316_0, slot_316_0, 1) + slot_15_48.get_damage(slot_316_15, arg_316_0, slot_316_0, 2)
			end
		},
		R = {
			ignore_obj_radius = 0,
			prediction = {
				delay = 0,
				range = 0,
				type = "InRange",
				speed = math.huge
			},
			slot = player:spellSlot(_R),
			damage = function(arg_317_0, arg_317_1)
				-- function 317
				return 0
			end
		}
	}
	slot_15_48.Nidalee = {
		Q = {
			prediction = {
				delay = 0.25,
				range = 1500,
				type = "Linear",
				width2 = 80,
				boundingRadiusMod = 0,
				width = 20,
				speed = 1300,
				collision = {
					wall = true,
					minion = true,
					hero = true
				}
			},
			slot = player:spellSlot(_Q),
			damage = function(arg_318_0, arg_318_1, arg_318_2, arg_318_3)
				-- function 318
				local slot_318_0 = arg_318_1 or player
				local slot_318_1 = common.GetTotalAP(slot_318_0)
				local slot_318_2 = common.GetTotalAD(slot_318_0)

				if (arg_318_3 or slot_318_0:spellSlot(_Q).name) == "Takedown" then
					local slot_318_3 = (({
						5,
						30,
						55,
						80
					})[slot_318_0:spellSlot(_R).level] or 0) + slot_318_1 * 0.4 + slot_318_2 * 0.75

					if arg_318_0.buff.nidaleepassivehunted then
						slot_318_3 = slot_318_3 * 1.3
					end

					local slot_318_4 = common.CAP(arg_318_0, slot_318_3, slot_318_0)

					return slot_15_48.get_damage(slot_318_4, arg_318_0, slot_318_0, 1)
				else
					local slot_318_5 = ({
						70,
						90,
						110,
						130,
						150
					})[slot_318_0:spellSlot(_Q).level] or 0

					if arg_318_2 then
						slot_318_5 = slot_318_5 + slot_318_5 * math.max(0, math.min(2, math.floor((arg_318_2 - 525) / 96.875) * 0.25))
					else
						slot_318_5 = slot_318_5 + slot_318_5 * math.max(0, math.min(2, math.floor((arg_318_0.pos:dist(slot_318_0.pos) - 525) / 96.875) * 0.25))
					end

					local slot_318_6 = slot_318_5 + slot_318_1 * 0.5
					local slot_318_7 = common.CAP(arg_318_0, slot_318_6, slot_318_0)

					return slot_15_48.get_damage(slot_318_7, arg_318_0, slot_318_0, 1)
				end

				return 0
			end
		},
		W = {
			ignore_obj_radius = 0,
			prediction = {
				delay = 1,
				range = 900,
				boundingRadiusMod = 0,
				type = "Circular",
				radius = 1,
				speed = math.huge
			},
			prediction2 = {
				delay = 0,
				range = 375,
				range2 = 750,
				type = "InRange",
				speed = math.huge
			},
			slot = player:spellSlot(_W),
			damage = function(arg_319_0, arg_319_1, arg_319_2)
				-- function 319
				local slot_319_0 = arg_319_1 or player
				local slot_319_1 = common.GetTotalAP(slot_319_0)
				local slot_319_2 = common.GetBonusAD(slot_319_0)

				if (arg_319_2 or slot_319_0:spellSlot(_Q).name) == "Takedown" then
					local slot_319_3 = (({
						60,
						110,
						160,
						210
					})[slot_319_0:spellSlot(_R).level] or 0) + slot_319_1 * 0.3
					local slot_319_4 = common.CAP(arg_319_0, slot_319_3, slot_319_0)

					return slot_15_48.get_damage(slot_319_4, arg_319_0, slot_319_0, 1)
				else
					local slot_319_5 = (({
						10,
						20,
						30,
						40,
						50
					})[slot_319_0:spellSlot(_W).level] or 0) + slot_319_1 * 0.05
					local slot_319_6 = common.CAP(arg_319_0, slot_319_5, slot_319_0)

					return slot_15_48.get_damage(slot_319_6, arg_319_0, slot_319_0, 1)
				end

				return 0
			end
		},
		E = {
			ignore_obj_radius = 0,
			prediction = {
				delay = 0,
				range = 900,
				type = "InRange",
				speed = math.huge
			},
			prediction2 = {
				delay = 0.25,
				range = 300,
				type = "Circular",
				boundingRadiusMod = 0,
				width = 80,
				speed = 2000
			},
			slot = player:spellSlot(_E),
			damage = function(arg_320_0, arg_320_1, arg_320_2)
				-- function 320
				local slot_320_0 = arg_320_1 or player
				local slot_320_1 = common.GetTotalAP(slot_320_0)
				local slot_320_2 = common.GetBonusAD(slot_320_0)

				if slot_320_0:spellSlot(_Q).name == "Takedown" then
					local slot_320_3 = (({
						80,
						140,
						200,
						260
					})[slot_320_0:spellSlot(_R).level] or 0) + slot_320_1 * 0.45 + slot_320_2 * 0.4
					local slot_320_4 = common.CAP(arg_320_0, slot_320_3, slot_320_0)

					return slot_15_48.get_damage(slot_320_4, arg_320_0, slot_320_0, 1)
				end

				return 0
			end
		},
		R = {
			ignore_obj_radius = 0,
			prediction = {
				delay = 0,
				range = 0,
				type = "InRange",
				speed = math.huge
			},
			slot = player:spellSlot(_R),
			damage = function(arg_321_0, arg_321_1)
				-- function 321
				return 0
			end
		}
	}
	slot_15_48.Lulu = {
		Q = {
			prediction = {
				delay = 0.25,
				range = 900,
				type = "Linear",
				boundingRadiusMod = 1,
				width = 60,
				speed = 1450,
				collision = {
					wall = true,
					minion = true,
					hero = true
				}
			},
			slot = player:spellSlot(_Q),
			damage = function(arg_322_0, arg_322_1)
				-- function 322
				local slot_322_0 = arg_322_1 or player
				local slot_322_1 = common.GetTotalAP(slot_322_0)
				local slot_322_2 = common.GetBonusAD(slot_322_0)
				local slot_322_3 = (({
					70,
					105,
					140,
					175,
					210
				})[slot_322_0:spellSlot(_Q).level] or 0) + slot_322_1 * 0.5
				local slot_322_4 = common.CAP(arg_322_0, slot_322_3, slot_322_0)

				return slot_15_48.get_damage(slot_322_4, arg_322_0, slot_322_0, 1)
			end
		},
		W = {
			ignore_obj_radius = 0,
			prediction = {
				delay = 0.2419,
				range = 650,
				type = "InRange",
				speed = 2250
			},
			slot = player:spellSlot(_W),
			damage = function(arg_323_0, arg_323_1)
				-- function 323
				return 0
			end
		},
		E = {
			ignore_obj_radius = 0,
			prediction = {
				delay = 0,
				range = 650,
				type = "InRange",
				speed = math.huge
			},
			slot = player:spellSlot(_E),
			damage = function(arg_324_0, arg_324_1)
				-- function 324
				local slot_324_0 = arg_324_1 or player
				local slot_324_1 = common.GetTotalAP(slot_324_0)
				local slot_324_2 = common.GetBonusAD(slot_324_0)
				local slot_324_3 = (({
					80,
					125,
					170,
					215,
					260
				})[slot_324_0:spellSlot(_E).level] or 0) + slot_324_1 * 0.5
				local slot_324_4 = common.CAP(arg_324_0, slot_324_3, slot_324_0)

				return slot_15_48.get_damage(slot_324_4, arg_324_0, slot_324_0, 1)
			end
		},
		R = {
			ignore_obj_radius = 0,
			prediction = {
				delay = 0,
				range = 900,
				type = "InRange",
				speed = math.huge
			},
			slot = player:spellSlot(_R),
			damage = function(arg_325_0, arg_325_1)
				-- function 325
				return 0
			end
		}
	}
	slot_15_48.Thresh = {
		Q = {
			prediction = {
				delay = 0.5,
				range = 1040,
				type = "Linear",
				width2 = 120,
				boundingRadiusMod = 1,
				width = 70,
				speed = 1900,
				collision = {
					wall = true,
					minion = true,
					hero = false
				}
			},
			prediction_ig = {
				type = "Linear",
				delay = 0.5,
				boundingRadiusMod = 1,
				width = 70,
				speed = 1900,
				width2 = 120,
				range = 1040,
				ig = true,
				collision = {
					wall = true,
					minion = true,
					hero = false
				}
			},
			slot = player:spellSlot(_Q),
			damage = function(arg_326_0, arg_326_1)
				-- function 326
				local slot_326_0 = arg_326_1 or player
				local slot_326_1 = common.GetTotalAP(slot_326_0)
				local slot_326_2 = common.GetBonusAD(slot_326_0)
				local slot_326_3 = (({
					100,
					150,
					200,
					250,
					300
				})[slot_326_0:spellSlot(_Q).level] or 0) + slot_326_1 * 0.9
				local slot_326_4 = common.CAP(arg_326_0, slot_326_3, slot_326_0)

				return slot_15_48.get_damage(slot_326_4, arg_326_0, slot_326_0, 1)
			end
		},
		W = {
			ignore_obj_radius = 0,
			prediction = {
				delay = 0,
				range = 950,
				type = "InRange",
				speed = math.huge
			},
			slot = player:spellSlot(_W),
			damage = function(arg_327_0, arg_327_1)
				-- function 327
				return 0
			end
		},
		E = {
			ignore_obj_radius = 0,
			prediction = {
				delay = 0.3889,
				range = 500,
				type = "Linear",
				boundingRadiusMod = 1,
				width = 110,
				speed = math.huge,
				collision = {
					wall = true,
					minion = true,
					hero = true
				}
			},
			prediction2 = {
				delay = 0.3889,
				range = 500,
				type = "Linear",
				ig = true,
				boundingRadiusMod = 1,
				width = 110,
				speed = math.huge,
				collision = {
					wall = true,
					minion = true,
					hero = true
				}
			},
			slot = player:spellSlot(_E),
			damage = function(arg_328_0, arg_328_1)
				-- function 328
				local slot_328_0 = arg_328_1 or player
				local slot_328_1 = common.GetTotalAP(slot_328_0)
				local slot_328_2 = common.GetBonusAD(slot_328_0)
				local slot_328_3 = (({
					75,
					120,
					165,
					210,
					255
				})[slot_328_0:spellSlot(_E).level] or 0) + slot_328_1 * 0.7
				local slot_328_4 = common.CAP(arg_328_0, slot_328_3, slot_328_0)

				return slot_15_48.get_damage(slot_328_4, arg_328_0, slot_328_0, 1)
			end
		},
		R = {
			ignore_obj_radius = 0,
			prediction = {
				delay = 0,
				range = 470,
				type = "InRange",
				speed = math.huge
			},
			slot = player:spellSlot(_R),
			damage = function(arg_329_0, arg_329_1)
				-- function 329
				local slot_329_0 = arg_329_1 or player
				local slot_329_1 = common.GetTotalAP(slot_329_0)
				local slot_329_2 = common.GetBonusAD(slot_329_0)
				local slot_329_3 = (({
					250,
					400,
					550
				})[slot_329_0:spellSlot(_R).level] or 0) + slot_329_1 * 1
				local slot_329_4 = common.CAP(arg_329_0, slot_329_3, slot_329_0)

				return slot_15_48.get_damage(slot_329_4, arg_329_0, slot_329_0, 1)
			end
		}
	}
	slot_15_48.Yuumi = {
		Q = {
			prediction = {
				delay = 0,
				speed2 = 850,
				boundingRadiusMod = 0,
				width = 30,
				speed = 1950,
				width2 = 60,
				range = 850,
				type = "Linear",
				collision = {
					wall = true,
					minion = true,
					hero = true
				}
			},
			prediction2 = {
				type = "Linear",
				delay = 0,
				boundingRadiusMod = 0,
				width = 30,
				speed = 1200,
				width2 = 120,
				range = 1800,
				ig = true,
				collision = {
					wall = true,
					minion = true,
					hero = true
				}
			},
			slot = player:spellSlot(_Q),
			damage = function(arg_330_0, arg_330_1)
				-- function 330
				local slot_330_0 = arg_330_1 or player
				local slot_330_1 = common.GetTotalAP(slot_330_0)
				local slot_330_2 = common.GetBonusAD(slot_330_0)
				local slot_330_3 = (({
					60,
					90,
					120,
					150,
					180,
					210
				})[slot_330_0:spellSlot(_Q).level] or 0) + slot_330_1 * 0.2
				local slot_330_4 = common.CAP(arg_330_0, slot_330_3, slot_330_0)

				return slot_15_48.get_damage(slot_330_4, arg_330_0, slot_330_0, 1)
			end
		},
		W = {
			ignore_obj_radius = 0,
			prediction = {
				delay = 0,
				range = 700,
				type = "InRange",
				speed = math.huge
			},
			slot = player:spellSlot(_W),
			damage = function(arg_331_0, arg_331_1)
				-- function 331
				return 0
			end
		},
		E = {
			ignore_obj_radius = 0,
			prediction = {
				delay = 0,
				range = 0,
				type = "InRange",
				speed = math.huge
			},
			slot = player:spellSlot(_E),
			damage = function(arg_332_0, arg_332_1)
				-- function 332
				return 0
			end
		},
		R = {
			ignore_obj_radius = 0,
			prediction = {
				delay = 0,
				range = 1000,
				type = "Linear",
				ig = true,
				boundingRadiusMod = 0,
				width = 225,
				speed = 3000,
				collision = {
					wall = true,
					minion = true,
					hero = true
				}
			},
			slot = player:spellSlot(_R),
			damage = function(arg_333_0, arg_333_1)
				-- function 333
				local slot_333_0 = arg_333_1 or player
				local slot_333_1 = common.GetTotalAP(slot_333_0)
				local slot_333_2 = common.GetBonusAD(slot_333_0)
				local slot_333_3 = (({
					75,
					100,
					125
				})[slot_333_0:spellSlot(_R).level] or 0) + slot_333_1 * 0.2
				local slot_333_4 = common.CAP(arg_333_0, slot_333_3, slot_333_0)

				return slot_15_48.get_damage(slot_333_4, arg_333_0, slot_333_0, 1)
			end
		}
	}
	slot_15_48.Lucian = {
		Q = {
			prediction = {
				boundingRadiusMod = 0,
				delay = 0.4,
				range2 = 500,
				width = 1,
				width2 = 60,
				range = 990,
				type = "Linear",
				speed = math.huge,
				collision = {
					wall = true,
					minion = false,
					hero = false
				}
			},
			slot = player:spellSlot(_Q),
			damage = function(arg_334_0, arg_334_1)
				-- function 334
				local slot_334_0 = arg_334_1 or player
				local slot_334_1 = common.GetTotalAP(slot_334_0)
				local slot_334_2 = common.GetBonusAD(slot_334_0)
				local slot_334_3 = {
					85,
					115,
					145,
					175,
					205
				}
				local slot_334_4 = {
					0.6,
					0.75,
					0.9,
					1.05,
					1.2
				}
				local slot_334_5 = slot_334_0:spellSlot(_Q).level
				local slot_334_6 = (slot_334_3[slot_334_5] or 0) + slot_334_2 * (slot_334_4[slot_334_5] or 0)
				local slot_334_7 = common.CAD(arg_334_0, slot_334_6, slot_334_0)

				return slot_15_48.get_damage(slot_334_7, arg_334_0, slot_334_0, 2)
			end
		},
		W = {
			ignore_obj_radius = 0,
			prediction = {
				delay = 0.25,
				range = 900,
				type = "Linear",
				boundingRadiusMod = 0,
				width = 55,
				speed = 1600,
				collision = {
					wall = true,
					minion = true,
					hero = true
				}
			},
			slot = player:spellSlot(_W),
			damage = function(arg_335_0, arg_335_1)
				-- function 335
				local slot_335_0 = arg_335_1 or player
				local slot_335_1 = common.GetTotalAP(slot_335_0)
				local slot_335_2 = common.GetBonusAD(slot_335_0)
				local slot_335_3 = (({
					75,
					110,
					145,
					180,
					215
				})[slot_335_0:spellSlot(_W).level] or 0) + slot_335_1 * 0.9
				local slot_335_4 = common.CAP(arg_335_0, slot_335_3, slot_335_0)

				return slot_15_48.get_damage(slot_335_4, arg_335_0, slot_335_0, 1)
			end
		},
		EW = {
			ignore_obj_radius = 0,
			prediction = {
				delay = 0.25,
				range = 1600,
				type = "Linear",
				boundingRadiusMod = 0,
				width = 55,
				speed = 1600,
				collision = {
					wall = true,
					minion = true,
					hero = true
				}
			},
			slot = player:spellSlot(_W),
			damage = function(arg_336_0, arg_336_1)
				-- function 336
				local slot_336_0 = arg_336_1 or player
				local slot_336_1 = common.GetTotalAP(slot_336_0)
				local slot_336_2 = common.GetBonusAD(slot_336_0)
				local slot_336_3 = (({
					75,
					110,
					145,
					180,
					215
				})[slot_336_0:spellSlot(_W).level] or 0) + slot_336_1 * 0.9
				local slot_336_4 = common.CAP(arg_336_0, slot_336_3, slot_336_0)

				return slot_15_48.get_damage(slot_336_4, arg_336_0, slot_336_0, 1)
			end
		},
		E = {
			ignore_obj_radius = 0,
			prediction = {
				delay = 0,
				range = 425,
				type = "Linear",
				ig = true,
				boundingRadiusMod = 0,
				width = 1,
				speed = 1350
			},
			slot = player:spellSlot(_E),
			damage = function(arg_337_0, arg_337_1)
				-- function 337
				return 0
			end
		},
		R = {
			ignore_obj_radius = 0,
			prediction = {
				delay = 0,
				range = 1200,
				type = "Linear",
				ig = true,
				boundingRadiusMod = 0,
				width = 110,
				speed = math.huge,
				collision = {
					wall = true,
					minion = true,
					hero = true
				}
			},
			slot = player:spellSlot(_R),
			damage = function(arg_338_0, arg_338_1)
				-- function 338
				local slot_338_0 = arg_338_1 or player
				local slot_338_1 = common.GetTotalAP(slot_338_0)
				local slot_338_2 = common.GetTotalAD(slot_338_0)
				local slot_338_3 = ({
					15,
					30,
					45
				})[slot_338_0:spellSlot(_R).level] or 0
				local slot_338_4 = 1
				local slot_338_5 = slot_338_3 + slot_338_2 * 0.25 + slot_338_1 * 0.15
				local slot_338_6 = common.CAD(arg_338_0, slot_338_5, slot_338_0)

				return slot_15_48.get_damage(slot_338_6, arg_338_0, slot_338_0, 2) * slot_338_4
			end
		}
	}
	slot_15_48.Draven = {
		Q = {
			prediction = {
				delay = 0,
				range = 0,
				type = "InRange",
				speed = math.huge
			},
			slot = player:spellSlot(_Q),
			damage = function(arg_339_0, arg_339_1)
				-- function 339
				return 0
			end
		},
		W = {
			ignore_obj_radius = 0,
			prediction = {
				delay = 0,
				range = 0,
				type = "InRange",
				speed = math.huge
			},
			slot = player:spellSlot(_W),
			damage = function(arg_340_0, arg_340_1)
				-- function 340
				return 0
			end
		},
		E = {
			ignore_obj_radius = 0,
			prediction = {
				delay = 0.25,
				range = 1100,
				type = "Linear",
				boundingRadiusMod = 1,
				width = 130,
				speed = 1400,
				collision = {
					wall = true,
					minion = false,
					hero = false
				}
			},
			slot = player:spellSlot(_E),
			damage = function(arg_341_0, arg_341_1)
				-- function 341
				local slot_341_0 = arg_341_1 or player
				local slot_341_1 = common.GetTotalAP(slot_341_0)
				local slot_341_2 = common.GetBonusAD(slot_341_0)
				local slot_341_3 = (({
					75,
					110,
					145,
					180,
					215
				})[slot_341_0:spellSlot(_E).level] or 0) + slot_341_2 * 0.5
				local slot_341_4 = common.CAD(arg_341_0, slot_341_3, slot_341_0)

				return slot_15_48.get_damage(slot_341_4, arg_341_0, slot_341_0, 2)
			end
		},
		R = {
			ignore_obj_radius = 0,
			prediction = {
				delay = 0.5,
				range = 20000,
				type = "Linear",
				boundingRadiusMod = 1,
				width = 160,
				speed = 2000,
				collision = {
					wall = true,
					minion = false,
					hero = true
				}
			},
			slot = player:spellSlot(_R),
			damage = function(arg_342_0, arg_342_1)
				-- function 342
				local slot_342_0 = arg_342_1 or player
				local slot_342_1 = common.GetTotalAP(slot_342_0)
				local slot_342_2 = common.GetBonusAD(slot_342_0)
				local slot_342_3 = {
					175,
					275,
					375
				}
				local slot_342_4 = {
					1.1,
					1.3,
					1.5
				}
				local slot_342_5 = slot_342_0:spellSlot(_R).level
				local slot_342_6 = (slot_342_3[slot_342_5] or 0) + slot_342_2 * (slot_342_4[slot_342_5] or 0)
				local slot_342_7 = common.CAD(arg_342_0, slot_342_6, slot_342_0)

				return slot_15_48.get_damage(slot_342_7, arg_342_0, slot_342_0, 2)
			end
		}
	}
	slot_15_48.Orianna = {
		Q = {
			prediction = {
				delay = 0,
				range = 1825,
				width = 80,
				boundingRadiusMod = 1,
				range2 = 825,
				type = "Linear",
				speed = 1400,
				collision = {
					wall = true,
					minion = false,
					hero = false
				}
			},
			slot = player:spellSlot(_Q),
			damage = function(arg_343_0, arg_343_1)
				-- function 343
				local slot_343_0 = arg_343_1 or player
				local slot_343_1 = common.GetTotalAP(slot_343_0)
				local slot_343_2 = (({
					60,
					90,
					120,
					150,
					180
				})[slot_343_0:spellSlot(_Q).level] or 0) + slot_343_1 * 0.55
				local slot_343_3 = common.CAP(arg_343_0, slot_343_2, slot_343_0)

				return slot_15_48.get_damage(slot_343_3, arg_343_0, slot_343_0, 2)
			end
		},
		W = {
			ignore_obj_radius = 0,
			prediction = {
				delay = 0,
				range = 0,
				bonusRadius = 0,
				boundingRadiusModTarget = 0,
				boundingRadiusModSource = 0,
				type = "InRange",
				radius = 225,
				speed = math.huge
			},
			slot = player:spellSlot(_W),
			damage = function(arg_344_0, arg_344_1)
				-- function 344
				local slot_344_0 = arg_344_1 or player
				local slot_344_1 = common.GetTotalAP(slot_344_0)
				local slot_344_2 = (({
					70,
					120,
					170,
					220,
					270
				})[slot_344_0:spellSlot(_W).level] or 0) + slot_344_1 * 0.7
				local slot_344_3 = common.CAP(arg_344_0, slot_344_2, slot_344_0)

				return slot_15_48.get_damage(slot_344_3, arg_344_0, slot_344_0, 2)
			end
		},
		E = {
			ignore_obj_radius = 0,
			prediction = {
				delay = 0,
				range = 1120,
				type = "Linear",
				boundingRadiusMod = 0,
				width = 80,
				speed = 1850,
				collision = {
					wall = true,
					minion = false,
					hero = false
				}
			},
			slot = player:spellSlot(_E),
			damage = function(arg_345_0, arg_345_1)
				-- function 345
				local slot_345_0 = arg_345_1 or player
				local slot_345_1 = common.GetTotalAP(slot_345_0)
				local slot_345_2 = (({
					60,
					90,
					120,
					150,
					180
				})[slot_345_0:spellSlot(_E).level] or 0) + slot_345_1 * 0.3
				local slot_345_3 = common.CAP(arg_345_0, slot_345_2, slot_345_0)

				return slot_15_48.get_damage(slot_345_3, arg_345_0, slot_345_0, 2)
			end
		},
		R = {
			ignore_obj_radius = 0,
			prediction = {
				delay = 0.5,
				range = 400,
				bonusRadius = 0,
				boundingRadiusModTarget = 0,
				boundingRadiusModSource = 0,
				type = "InRange",
				radius = 400,
				speed = math.huge
			},
			slot = player:spellSlot(_R),
			damage = function(arg_346_0, arg_346_1)
				-- function 346
				local slot_346_0 = arg_346_1 or player
				local slot_346_1 = common.GetTotalAP(slot_346_0)
				local slot_346_2 = (({
					250,
					400,
					550
				})[slot_346_0:spellSlot(_R).level] or 0) + slot_346_1 * 0.95
				local slot_346_3 = common.CAP(arg_346_0, slot_346_2, slot_346_0)

				return slot_15_48.get_damage(slot_346_3, arg_346_0, slot_346_0, 2)
			end
		}
	}
	slot_15_48.Vayne = {
		Q = {
			prediction = {
				delay = 0,
				width = 1,
				boundingRadiusMod = 0,
				range2 = 300,
				type = "Linear",
				range = 300 + player.attackRange + 100,
				speed = player.moveSpeed + 500
			},
			slot = player:spellSlot(_Q),
			damage = function(arg_347_0, arg_347_1)
				-- function 347
				return 0
			end
		},
		W = {
			ignore_obj_radius = 0,
			prediction = {
				delay = 0,
				range = 0,
				bonusRadius = 0,
				boundingRadiusModTarget = 0,
				boundingRadiusModSource = 0,
				type = "InRange",
				radius = 0,
				speed = math.huge
			},
			slot = player:spellSlot(_W),
			damage = function(arg_348_0, arg_348_1)
				-- function 348
				return 0
			end
		},
		E = {
			ignore_obj_radius = 0,
			prediction = {
				delay = 0.25,
				range = 550,
				width = 1,
				boundingRadiusMod = 0,
				range2 = 450,
				type = "Linear",
				speed = 2200,
				collision = {
					wall = true,
					minion = false,
					hero = false
				}
			},
			prediction2 = {
				delay = 0.25,
				range = 550,
				type = "Linear",
				boundingRadiusMod = 0,
				width = 1,
				speed = math.huge,
				collision = {
					wall = true,
					minion = false,
					hero = false
				}
			},
			slot = player:spellSlot(_E),
			damage = function(arg_349_0, arg_349_1)
				-- function 349
				local slot_349_0 = arg_349_1 or player
				local slot_349_1 = common.GetBonusAD(slot_349_0)
				local slot_349_2 = (({
					50,
					85,
					120,
					155,
					190
				})[slot_349_0:spellSlot(_E).level] or 0) + slot_349_1 * 0.5
				local slot_349_3 = common.CAD(arg_349_0, slot_349_2, slot_349_0)

				return slot_15_48.get_damage(slot_349_3, arg_349_0, slot_349_0, 1)
			end
		},
		R = {
			ignore_obj_radius = 0,
			prediction = {
				delay = 0,
				range = 0,
				bonusRadius = 0,
				boundingRadiusModTarget = 0,
				boundingRadiusModSource = 0,
				type = "InRange",
				radius = 0,
				speed = math.huge
			},
			slot = player:spellSlot(_R),
			damage = function(arg_350_0, arg_350_1)
				-- function 350
				return 0
			end
		}
	}
	slot_15_48.Senna = {
		Q = {
			prediction = {
				boundingRadiusMod = 1,
				delay = 0.4,
				range2 = 600,
				width = 40,
				width2 = 40,
				range = 1300,
				type = "Linear",
				speed = math.huge,
				collision = {
					wall = true,
					minion = false,
					hero = false
				}
			},
			slot = player:spellSlot(_Q),
			damage = function(arg_351_0, arg_351_1)
				-- function 351
				local slot_351_0 = arg_351_1 or player
				local slot_351_1 = common.GetBonusAD(slot_351_0)
				local slot_351_2 = (({
					30,
					60,
					90,
					120,
					150
				})[slot_351_0:spellSlot(_Q).level] or 0) + slot_351_1 * 0.4
				local slot_351_3 = common.CAD(arg_351_0, slot_351_2, slot_351_0)

				return slot_15_48.get_damage(slot_351_3, arg_351_0, slot_351_0, 2)
			end
		},
		W = {
			ignore_obj_radius = 0,
			prediction = {
				delay = 0.25,
				range = 1200,
				type = "Linear",
				boundingRadiusMod = 1,
				width = 70,
				speed = 1200,
				collision = {
					wall = true,
					minion = true,
					hero = true
				}
			},
			slot = player:spellSlot(_W),
			damage = function(arg_352_0, arg_352_1)
				-- function 352
				local slot_352_0 = arg_352_1 or player
				local slot_352_1 = common.GetBonusAD(slot_352_0)
				local slot_352_2 = (({
					70,
					115,
					160,
					205,
					250
				})[slot_352_0:spellSlot(_W).level] or 0) + slot_352_1 * 0.7
				local slot_352_3 = common.CAD(arg_352_0, slot_352_2, slot_352_0)

				return slot_15_48.get_damage(slot_352_3, arg_352_0, slot_352_0, 2)
			end
		},
		E = {
			ignore_obj_radius = 0,
			prediction = {
				delay = 0,
				range = 0,
				bonusRadius = 0,
				boundingRadiusModTarget = 0,
				boundingRadiusModSource = 0,
				type = "InRange",
				radius = 0,
				speed = math.huge
			},
			slot = player:spellSlot(_E),
			damage = function(arg_353_0, arg_353_1)
				-- function 353
				return 0
			end
		},
		R = {
			ignore_obj_radius = 0,
			prediction = {
				delay = 1,
				range = 20000,
				type = "Linear",
				slow = true,
				boundingRadiusMod = 0,
				width = 70,
				speed = 20000,
				collision = {
					wall = true,
					minion = false,
					hero = false
				}
			},
			slot = player:spellSlot(_R),
			damage = function(arg_354_0, arg_354_1)
				-- function 354
				local slot_354_0 = arg_354_1 or player
				local slot_354_1 = common.GetTotalAP(slot_354_0)
				local slot_354_2 = common.GetBonusAD(slot_354_0)
				local slot_354_3 = (({
					250,
					400,
					550
				})[slot_354_0:spellSlot(_R).level] or 0) + slot_354_1 * 0.7 + slot_354_2 * 1.15
				local slot_354_4 = common.CAD(arg_354_0, slot_354_3, slot_354_0)

				return slot_15_48.get_damage(slot_354_4, arg_354_0, slot_354_0, 2)
			end
		}
	}
	slot_15_48.MissFortune = {
		Q = {
			prediction = {
				delay = 0,
				range = 0,
				bonusRadius = 0,
				boundingRadiusModTarget = 0,
				boundingRadiusModSource = 0,
				type = "InRange",
				radius = 0,
				speed = math.huge
			},
			slot = player:spellSlot(_Q),
			damage = function(arg_355_0, arg_355_1)
				-- function 355
				local slot_355_0 = arg_355_1 or player
				local slot_355_1 = common.GetTotalAD(slot_355_0)
				local slot_355_2 = common.GetTotalAP(slot_355_0)
				local slot_355_3 = (({
					20,
					45,
					70,
					95,
					120
				})[slot_355_0:spellSlot(_Q).level] or 0) + slot_355_1 * 1 + slot_355_2 * 0.35
				local slot_355_4 = common.CAD(arg_355_0, slot_355_3, slot_355_0)

				return slot_15_48.get_damage(slot_355_4, arg_355_0, slot_355_0, 2)
			end
		},
		W = {
			ignore_obj_radius = 0,
			prediction = {
				delay = 0,
				range = 0,
				bonusRadius = 0,
				boundingRadiusModTarget = 0,
				boundingRadiusModSource = 0,
				type = "InRange",
				radius = 0,
				speed = math.huge
			},
			slot = player:spellSlot(_W),
			damage = function(arg_356_0, arg_356_1)
				-- function 356
				return 0
			end
		},
		E = {
			ignore_obj_radius = 0,
			prediction = {
				delay = 1,
				range = 1000,
				boundingRadiusMod = 0,
				type = "Circular",
				radius = 100,
				speed = math.huge,
				collision = {
					wall = false,
					minion = false,
					hero = false
				}
			},
			slot = player:spellSlot(_E),
			damage = function(arg_357_0, arg_357_1)
				-- function 357
				local slot_357_0 = arg_357_1 or player
				local slot_357_1 = common.GetTotalAP(slot_357_0)
				local slot_357_2 = (({
					8.75,
					12.5,
					16.25,
					20,
					23.75
				})[slot_357_0:spellSlot(_E).level] or 0) + slot_357_1 * 0.15
				local slot_357_3 = common.CAP(arg_357_0, slot_357_2, slot_357_0)

				return slot_15_48.get_damage(slot_357_3, arg_357_0, slot_357_0, 1)
			end
		},
		R = {
			ignore_obj_radius = 0,
			prediction = {
				delay = 0,
				range = 1450,
				type = "Linear",
				boundingRadiusMod = 0,
				width = 20,
				speed = 2000,
				collision = {
					wall = true,
					minion = false,
					hero = false
				}
			},
			slot = player:spellSlot(_R),
			damage = function(arg_358_0, arg_358_1)
				-- function 358
				local slot_358_0 = arg_358_1 or player
				local slot_358_1 = common.GetTotalAD(slot_358_0)
				local slot_358_2 = common.GetTotalAP(slot_358_0)
				local slot_358_3 = slot_358_1 * 0.75 + slot_358_2 * 0.25
				local slot_358_4 = common.CAD(arg_358_0, slot_358_3, slot_358_0)

				return slot_15_48.get_damage(slot_358_4, arg_358_0, slot_358_0, 2)
			end
		}
	}
	slot_15_48.Lux = {
		Q = {
			prediction = {
				delay = 0.25,
				range = 1200,
				type = "Linear",
				width2 = 80,
				boundingRadiusMod = 1,
				width = 50,
				speed = 1200,
				collision = {
					wall = true,
					minion = true,
					hero = true
				}
			},
			slot = player:spellSlot(_Q),
			damage = function(arg_359_0, arg_359_1)
				-- function 359
				local slot_359_0 = arg_359_1 or player
				local slot_359_1 = common.GetTotalAP(slot_359_0)
				local slot_359_2 = (({
					80,
					120,
					160,
					200,
					240
				})[slot_359_0:spellSlot(_Q).level] or 0) + slot_359_1 * 0.65
				local slot_359_3 = common.CAP(arg_359_0, slot_359_2, slot_359_0)

				return slot_15_48.get_damage(slot_359_3, arg_359_0, slot_359_0, 1)
			end
		},
		W = {
			ignore_obj_radius = 0,
			prediction = {
				delay = 0.25,
				range = 1175,
				type = "Linear",
				boundingRadiusMod = 0,
				width = 110,
				speed = 2400,
				collision = {
					wall = false,
					minion = false,
					hero = false
				}
			},
			slot = player:spellSlot(_W),
			damage = function(arg_360_0, arg_360_1)
				-- function 360
				return 0
			end
		},
		E = {
			ignore_obj_radius = 0,
			prediction = {
				delay = 0.25,
				range = 1100,
				speed = 1200,
				boundingRadiusMod = 0,
				type = "Circular",
				radius = 1,
				collision = {
					wall = true,
					minion = false,
					hero = false
				}
			},
			slot = player:spellSlot(_E),
			damage = function(arg_361_0, arg_361_1)
				-- function 361
				local slot_361_0 = arg_361_1 or player
				local slot_361_1 = common.GetTotalAP(slot_361_0)
				local slot_361_2 = (({
					65,
					115,
					165,
					215,
					265
				})[slot_361_0:spellSlot(_E).level] or 0) + slot_361_1 * 0.8
				local slot_361_3 = common.CAP(arg_361_0, slot_361_2, slot_361_0)

				return slot_15_48.get_damage(slot_361_3, arg_361_0, slot_361_0, 1)
			end
		},
		R = {
			ignore_obj_radius = 0,
			prediction = {
				delay = 1,
				range = 3400,
				type = "Linear",
				boundingRadiusMod = 0,
				width = 1000,
				speed = math.huge,
				collision = {
					wall = true,
					minion = false,
					hero = false
				}
			},
			slot = player:spellSlot(_R),
			damage = function(arg_362_0, arg_362_1)
				-- function 362
				local slot_362_0 = arg_362_1 or player
				local slot_362_1 = common.GetTotalAP(slot_362_0)
				local slot_362_2 = (({
					300,
					400,
					500
				})[slot_362_0:spellSlot(_R).level] or 0) + slot_362_1 * 1.2
				local slot_362_3 = common.CAP(arg_362_0, slot_362_2, slot_362_0)

				return slot_15_48.get_damage(slot_362_3, arg_362_0, slot_362_0, 1)
			end
		}
	}
	slot_15_48.Poppy = {
		Q = {
			prediction = {
				delay = 0.375,
				range = 425,
				type = "Linear",
				boundingRadiusMod = 0,
				width = 80,
				speed = math.huge
			},
			slot = player:spellSlot(_Q),
			damage = function(arg_363_0, arg_363_1)
				-- function 363
				local slot_363_0 = arg_363_1 or player
				local slot_363_1 = common.GetBonusAD(slot_363_0)
				local slot_363_2 = ({
					30,
					55,
					80,
					105,
					130
				})[slot_363_0:spellSlot(_Q).level] or 0
				local slot_363_3 = arg_363_0.maxHealth * 0.09
				local slot_363_4 = slot_363_2 + slot_363_1 * 1 + slot_363_3
				local slot_363_5 = common.CAD(arg_363_0, slot_363_4, slot_363_0)

				return slot_15_48.get_damage(slot_363_5, arg_363_0, slot_363_0, 2)
			end
		},
		W = {
			ignore_obj_radius = 0,
			prediction = {
				delay = 0,
				range = 0,
				bonusRadius = 0,
				boundingRadiusModTarget = 0,
				boundingRadiusModSource = 0,
				type = "InRange",
				radius = 400,
				speed = math.huge
			},
			slot = player:spellSlot(_W),
			damage = function(arg_364_0, arg_364_1)
				-- function 364
				return 0
			end
		},
		E = {
			ignore_obj_radius = 0,
			prediction = {
				delay = 0,
				range = 475,
				type = "Linear",
				boundingRadiusMod = 0,
				width = 65,
				speed = 1500
			},
			slot = player:spellSlot(_E),
			damage = function(arg_365_0, arg_365_1)
				-- function 365
				local slot_365_0 = arg_365_1 or player
				local slot_365_1 = common.GetBonusAD(slot_365_0)
				local slot_365_2 = (({
					50,
					70,
					90,
					110,
					130
				})[slot_365_0:spellSlot(_E).level] or 0) + slot_365_1 * 0.5
				local slot_365_3 = common.CAD(arg_365_0, slot_365_2, slot_365_0)

				return slot_15_48.get_damage(slot_365_3, arg_365_0, slot_365_0, 2)
			end
		},
		R = {
			ignore_obj_radius = 0,
			prediction = {
				delay = 0.45,
				range = 550,
				type = "Linear",
				boundingRadiusMod = 0,
				width = 137.5,
				speed = math.huge,
				collision = {
					wall = true,
					minion = false,
					hero = false
				}
			},
			prediction2 = {
				delay = 0.35,
				range = 1200,
				type = "Linear",
				boundingRadiusMod = 0,
				width = 90,
				speed = 2500,
				collision = {
					wall = true,
					minion = false,
					hero = false
				}
			},
			slot = player:spellSlot(_R),
			damage = function(arg_366_0, arg_366_1)
				-- function 366
				local slot_366_0 = arg_366_1 or player
				local slot_366_1 = common.GetBonusAD(slot_366_0)
				local slot_366_2 = (({
					100,
					150,
					200
				})[slot_366_0:spellSlot(_R).level] or 0) + slot_366_1 * 0.45
				local slot_366_3 = common.CAD(arg_366_0, slot_366_2, slot_366_0)

				return slot_15_48.get_damage(slot_366_3, arg_366_0, slot_366_0, 2)
			end
		}
	}
	slot_15_48.JarvanIV = {
		Q = {
			prediction = {
				delay = 0.4,
				range = 850,
				type = "Linear",
				boundingRadiusMod = 0,
				width = 68,
				speed = math.huge
			},
			slot = player:spellSlot(_Q),
			damage = function(arg_367_0, arg_367_1)
				-- function 367
				local slot_367_0 = arg_367_1 or player
				local slot_367_1 = common.GetBonusAD(slot_367_0)
				local slot_367_2 = (({
					80,
					120,
					160,
					200,
					240
				})[slot_367_0:spellSlot(_Q).level] or 0) + slot_367_1 * 1.4
				local slot_367_3 = common.CAD(arg_367_0, slot_367_2, slot_367_0)

				return slot_15_48.get_damage(slot_367_3, arg_367_0, slot_367_0, 2)
			end
		},
		EQ = {
			prediction = {
				delay = 0.75,
				range = 850,
				type = "Linear",
				boundingRadiusMod = 0,
				width = 90,
				speed = math.huge
			},
			prediction3 = {
				delay = 0.25,
				range = 850,
				type = "Linear",
				boundingRadiusMod = 0,
				width = 90,
				speed = 1450
			},
			prediction2 = {
				delay = 0.85,
				range = 1200,
				type = "Linear",
				slow = true,
				boundingRadiusMod = 0,
				width = 68,
				speed = math.huge
			},
			slot = player:spellSlot(_Q),
			damage = function(arg_368_0, arg_368_1)
				-- function 368
				local slot_368_0 = arg_368_1 or player
				local slot_368_1 = common.GetBonusAD(slot_368_0)
				local slot_368_2 = (({
					80,
					120,
					160,
					200,
					240
				})[slot_368_0:spellSlot(_Q).level] or 0) + slot_368_1 * 1.4
				local slot_368_3 = common.CAD(arg_368_0, slot_368_2, slot_368_0)

				return slot_15_48.get_damage(slot_368_3, arg_368_0, slot_368_0, 2)
			end
		},
		W = {
			ignore_obj_radius = 0,
			prediction = {
				delay = 0,
				range = 0,
				bonusRadius = 0,
				boundingRadiusModTarget = 0,
				boundingRadiusModSource = 0,
				type = "InRange",
				radius = 600,
				speed = math.huge
			},
			slot = player:spellSlot(_W),
			damage = function(arg_369_0, arg_369_1)
				-- function 369
				return 0
			end
		},
		E = {
			ignore_obj_radius = 0,
			prediction = {
				delay = 0.5,
				range = 860,
				boundingRadiusMod = 0,
				type = "Circular",
				radius = 200,
				speed = math.huge
			},
			slot = player:spellSlot(_E),
			damage = function(arg_370_0, arg_370_1)
				-- function 370
				local slot_370_0 = arg_370_1 or player
				local slot_370_1 = common.GetTotalAP(slot_370_0)
				local slot_370_2 = (({
					80,
					120,
					160,
					200,
					240
				})[slot_370_0:spellSlot(_E).level] or 0) + slot_370_1 * 0.8
				local slot_370_3 = common.CAP(arg_370_0, slot_370_2, slot_370_0)

				return slot_15_48.get_damage(slot_370_3, arg_370_0, slot_370_0, 1)
			end
		},
		R = {
			ignore_obj_radius = 0,
			prediction = {
				delay = 0,
				range = 650,
				bonusRadius = 0,
				boundingRadiusModTarget = 0,
				boundingRadiusModSource = 0,
				type = "InRange",
				radius = 350,
				speed = math.huge
			},
			slot = player:spellSlot(_R),
			damage = function(arg_371_0, arg_371_1)
				-- function 371
				local slot_371_0 = arg_371_1 or player
				local slot_371_1 = common.GetBonusAD(slot_371_0)
				local slot_371_2 = (({
					200,
					325,
					450
				})[slot_371_0:spellSlot(_R).level] or 0) + slot_371_1 * 1.8
				local slot_371_3 = common.CAD(arg_371_0, slot_371_2, slot_371_0)

				return slot_15_48.get_damage(slot_371_3, arg_371_0, slot_371_0, 2)
			end
		}
	}
	slot_15_48.Kindred = {
		Q = {
			prediction = {
				delay = 0.4,
				range = 300,
				type = "Linear",
				ig = true,
				boundingRadiusMod = 0,
				width = 65,
				speed = 900
			},
			slot = player:spellSlot(_Q),
			damage = function(arg_372_0, arg_372_1)
				-- function 372
				local slot_372_0 = arg_372_1 or player
				local slot_372_1 = common.GetBonusAD(slot_372_0)
				local slot_372_2 = (({
					40,
					65,
					90,
					115,
					140
				})[slot_372_0:spellSlot(_Q).level] or 0) + slot_372_1 * 0.7
				local slot_372_3 = common.CAD(arg_372_0, slot_372_2, slot_372_0)

				return slot_15_48.get_damage(slot_372_3, arg_372_0, slot_372_0, 2)
			end
		},
		W = {
			ignore_obj_radius = 0,
			prediction = {
				delay = 0.25,
				range = 750,
				speed = 1400,
				boundingRadiusMod = 0,
				type = "Circular",
				radius = 75
			},
			slot = player:spellSlot(_W),
			damage = function(arg_373_0, arg_373_1)
				-- function 373
				local slot_373_0 = arg_373_1 or player
				local slot_373_1 = common.GetBonusAD(slot_373_0)
				local slot_373_2 = common.GetTotalAP(slot_373_0)
				local slot_373_3 = (({
					25,
					30,
					35,
					40,
					45
				})[slot_373_0:spellSlot(_W).level] or 0) + slot_373_1 * 0.2 + slot_373_2 * 0.2
				local slot_373_4 = common.CAP(arg_373_0, slot_373_3, slot_373_0)

				return slot_15_48.get_damage(slot_373_4, arg_373_0, slot_373_0, 1)
			end
		},
		E = {
			ignore_obj_radius = 0,
			prediction = {
				delay = 0.25,
				range = 750,
				boundingRadiusMod = 0,
				type = "InRange",
				radius = 200,
				speed = math.huge
			},
			slot = player:spellSlot(_E),
			damage = function(arg_374_0, arg_374_1)
				-- function 374
				local slot_374_0 = arg_374_1 or player
				local slot_374_1 = common.GetBonusAD(slot_374_0)
				local slot_374_2 = common.GetTotalAP(slot_374_0)
				local slot_374_3 = (({
					80,
					100,
					120,
					140,
					160
				})[slot_374_0:spellSlot(_E).level] or 0) + slot_374_1 * 0.8
				local slot_374_4 = common.CAD(arg_374_0, slot_374_3, slot_374_0)

				return slot_15_48.get_damage(slot_374_4, arg_374_0, slot_374_0, 2)
			end
		},
		R = {
			ignore_obj_radius = 0,
			prediction = {
				delay = 0,
				range = 650,
				bonusRadius = 0,
				boundingRadiusModTarget = 0,
				boundingRadiusModSource = 0,
				type = "InRange",
				radius = 350,
				speed = math.huge
			},
			slot = player:spellSlot(_R),
			damage = function(arg_375_0, arg_375_1)
				-- function 375
				return 0
			end
		}
	}
	slot_15_48.Corki = {
		Q = {
			prediction = {
				delay = 0.25,
				range = 825,
				speed = 1100,
				boundingRadiusMod = 0,
				type = "Circular",
				radius = 275,
				collision = {
					wall = true,
					minion = false,
					hero = false
				}
			},
			slot = player:spellSlot(_Q),
			damage = function(arg_376_0, arg_376_1)
				-- function 376
				local slot_376_0 = arg_376_1 or player
				local slot_376_1 = common.GetBonusAD(slot_376_0)
				local slot_376_2 = common.GetTotalAP(slot_376_0)
				local slot_376_3 = (({
					70,
					115,
					160,
					205,
					250
				})[slot_376_0:spellSlot(_Q).level] or 0) + slot_376_1 * 1.2 + slot_376_2 * 1
				local slot_376_4 = common.CAP(arg_376_0, slot_376_3, slot_376_0)

				return slot_15_48.get_damage(slot_376_4, arg_376_0, slot_376_0, 1)
			end
		},
		W = {
			ignore_obj_radius = 0,
			prediction = {
				delay = 0.25,
				range = 600,
				width = 100,
				boundingRadiusMod = 0,
				type = "Linear",
				speed = 1050
			},
			slot = player:spellSlot(_W),
			damage = function(arg_377_0, arg_377_1)
				-- function 377
				local slot_377_0 = arg_377_1 or player
				local slot_377_1 = common.GetTotalAP(slot_377_0)
				local slot_377_2 = common.GetBonusAD(slot_377_0)
				local slot_377_3 = ((({
					200,
					275,
					350,
					425,
					500
				})[slot_377_0:spellSlot(_W).level] or 0) + slot_377_1 * 1.5 + slot_377_2 * 2) / 5
				local slot_377_4 = common.CAP(arg_377_0, slot_377_3, slot_377_0)

				return slot_15_48.get_damage(slot_377_4, arg_377_0, slot_377_0, 1)
			end
		},
		E = {
			ignore_obj_radius = 0,
			prediction = {
				delay = 0.25,
				range = 600,
				width = 100,
				boundingRadiusMod = 0,
				type = "Linear",
				speed = math.huge
			},
			slot = player:spellSlot(_E),
			damage = function(arg_378_0, arg_378_1)
				-- function 378
				local slot_378_0 = arg_378_1 or player
				local slot_378_1 = common.GetBonusAD(slot_378_0)
				local slot_378_2 = (({
					80,
					130,
					180,
					230,
					280
				})[slot_378_0:spellSlot(_E).level] or 0) + slot_378_1 * 2.4
				local slot_378_3 = common.CAD(arg_378_0, slot_378_2, slot_378_0) / 4

				return slot_15_48.get_damage(slot_378_3, arg_378_0, slot_378_0, 2)
			end
		},
		R = {
			ignore_obj_radius = 0,
			prediction = {
				delay = 0.175,
				range = 1500,
				boundingRadiusMod = 1,
				type = "Linear",
				range2 = 1300,
				width = 75,
				speed = 2000,
				collision = {
					wall = true,
					minion = true,
					hero = true
				}
			},
			slot = player:spellSlot(_R),
			damage = function(arg_379_0, arg_379_1)
				-- function 379
				local slot_379_0 = arg_379_1 or player
				local slot_379_1 = common.GetBonusAD(slot_379_0)
				local slot_379_2 = common.GetTotalAP(slot_379_0)
				local slot_379_3 = (({
					90,
					170,
					250
				})[slot_379_0:spellSlot(_R).level] or 0) + slot_379_1 * 0.8
				local slot_379_4 = common.CAD(arg_379_0, slot_379_3, slot_379_0)

				if slot_379_0.buff.mbcheck2 then
					slot_379_4 = slot_379_4 * 2
				end

				return slot_15_48.get_damage(slot_379_4, arg_379_0, slot_379_0, 2)
			end
		}
	}
	slot_15_48.Jayce = {
		Q = {
			prediction = {
				delay = 0.2143,
				range = 1050,
				type = "Linear",
				boundingRadiusMod = 1,
				width = 70,
				speed = 1450,
				collision = {
					wall = true,
					minion = true,
					hero = false
				}
			},
			slot = player:spellSlot(_Q),
			damage = function(arg_380_0, arg_380_1)
				-- function 380
				local slot_380_0 = arg_380_1 or player
				local slot_380_1 = common.GetBonusAD(slot_380_0)
				local slot_380_2 = (({
					55,
					110,
					165,
					220,
					275,
					330
				})[slot_380_0:spellSlot(_Q).level] or 0) + slot_380_1 * 1.2
				local slot_380_3 = common.CAD(arg_380_0, slot_380_2, slot_380_0)

				return slot_15_48.get_damage(slot_380_3, arg_380_0, slot_380_0, 2)
			end
		},
		Q1 = {
			prediction = {
				delay = 0,
				range = 600,
				boundingRadiusMod = 0,
				type = "InRange",
				speed = math.huge
			},
			slot = player:spellSlot(_Q),
			damage = function(arg_381_0, arg_381_1)
				-- function 381
				local slot_381_0 = arg_381_1 or player
				local slot_381_1 = common.GetBonusAD(slot_381_0)
				local slot_381_2 = (({
					60,
					110,
					160,
					210,
					260,
					310
				})[slot_381_0:spellSlot(_Q).level] or 0) + slot_381_1 * 1.2
				local slot_381_3 = common.CAD(arg_381_0, slot_381_2, slot_381_0)

				return slot_15_48.get_damage(slot_381_3, arg_381_0, slot_381_0, 2)
			end
		},
		EQ = {
			prediction = {
				delay = 0.2143,
				range = 1600,
				type = "Linear",
				boundingRadiusMod = 1,
				width = 100,
				speed = 2350,
				collision = {
					wall = true,
					minion = true,
					hero = false
				}
			},
			slot = player:spellSlot(_Q),
			damage = function(arg_382_0, arg_382_1)
				-- function 382
				local slot_382_0 = arg_382_1 or player
				local slot_382_1 = common.GetBonusAD(slot_382_0)
				local slot_382_2 = (({
					55,
					110,
					165,
					220,
					275,
					330
				})[slot_382_0:spellSlot(_Q).level] or 0) + slot_382_1 * 1.2
				local slot_382_3 = common.CAD(arg_382_0, slot_382_2 * 1.4, slot_382_0)

				return slot_15_48.get_damage(slot_382_3, arg_382_0, slot_382_0, 2)
			end
		},
		W = {
			ignore_obj_radius = 0,
			prediction = {
				range = 0
			},
			slot = player:spellSlot(_W),
			damage = function(arg_383_0, arg_383_1)
				-- function 383
				return 0
			end
		},
		E = {
			ignore_obj_radius = 0,
			prediction = {
				delay = 0,
				range = 650,
				width = 375,
				boundingRadiusMod = 0,
				type = "InRange",
				speed = math.huge
			},
			prediction2 = {
				delay = 0,
				range = 240,
				width = 375,
				boundingRadiusMod = 0,
				type = "InRange",
				speed = math.huge
			},
			slot = player:spellSlot(_E),
			damage = function(arg_384_0, arg_384_1)
				-- function 384
				local slot_384_0 = arg_384_1 or player
				local slot_384_1 = common.GetBonusAD(slot_384_0)
				local slot_384_2 = ({
					0.08,
					0.108,
					0.136,
					0.164,
					0.192,
					0.22
				})[slot_384_0:spellSlot(_E).level] or 0
				local slot_384_3 = slot_384_2 + arg_384_0.maxHealth * slot_384_2 + slot_384_1
				local slot_384_4 = common.CAP(arg_384_0, slot_384_3, slot_384_0)

				return slot_15_48.get_damage(slot_384_4, arg_384_0, slot_384_0, 1)
			end
		},
		R = {
			ignore_obj_radius = 0,
			prediction = {
				type = "InRange",
				range = 0
			},
			slot = player:spellSlot(_R),
			damage = function(arg_385_0, arg_385_1)
				-- function 385
				return 0
			end
		}
	}
	slot_15_48.Hwei = {
		QQ = {
			prediction = {
				delay = 0.25,
				range = 988,
				type = "Linear",
				width2 = 60,
				boundingRadiusMod = 1,
				width = 50,
				speed = 2000,
				collision = {
					wall = true,
					minion = true,
					hero = true
				}
			},
			slot = player:spellSlot(_Q),
			damage = function(arg_386_0, arg_386_1)
				-- function 386
				local slot_386_0 = arg_386_1 or player
				local slot_386_1 = common.GetTotalAP(slot_386_0)
				local slot_386_2 = {
					50,
					80,
					110,
					140,
					170
				}
				local slot_386_3 = {
					0.03,
					0.04,
					0.05,
					0.06,
					0.07
				}
				local slot_386_4 = slot_386_0:spellSlot(_Q).level
				local slot_386_5 = slot_386_2[slot_386_4] or 0
				local slot_386_6 = slot_386_3[slot_386_4] or 0
				local slot_386_7 = slot_386_5 + slot_386_1 * 0.7 + arg_386_0.maxHealth * slot_386_6
				local slot_386_8 = common.CAP(arg_386_0, slot_386_7, slot_386_0)

				return slot_15_48.get_damage(slot_386_8, arg_386_0, slot_386_0, 1)
			end
		},
		QW = {
			prediction = {
				delay = 1,
				range = 1900,
				boundingRadiusMod = 0,
				type = "Circular",
				radius = 50,
				speed = math.huge
			},
			slot = player:spellSlot(_Q),
			damage = function(arg_387_0, arg_387_1)
				-- function 387
				local slot_387_0 = arg_387_1 or player
				local slot_387_1 = common.GetTotalAP(slot_387_0)
				local slot_387_2 = (({
					80,
					100,
					120,
					140,
					160
				})[slot_387_0:spellSlot(_Q).level] or 0) + slot_387_1 * 0.25
				local slot_387_3 = common.CAP(arg_387_0, slot_387_2, slot_387_0)

				return slot_15_48.get_damage(slot_387_3, arg_387_0, slot_387_0, 1)
			end
		},
		QE = {
			prediction = {
				delay = 0.6,
				range = 1200,
				type = "Linear",
				boundingRadiusMod = 0,
				width = 112.5,
				speed = 800,
				collision = {
					wall = true,
					minion = false,
					hero = false
				}
			},
			prediction_ig = {
				delay = 0.6,
				range = 1200,
				type = "Linear",
				ig = true,
				boundingRadiusMod = 0,
				width = 110,
				speed = 800,
				collision = {
					wall = true,
					minion = false,
					hero = false
				}
			},
			slot = player:spellSlot(_Q),
			damage = function(arg_388_0, arg_388_1)
				-- function 388
				local slot_388_0 = arg_388_1 or player
				local slot_388_1 = common.GetTotalAP(slot_388_0)
				local slot_388_2 = (({
					20,
					40,
					60,
					80,
					100
				})[slot_388_0:spellSlot(_Q).level] or 0) + slot_388_1 * 0.2
				local slot_388_3 = common.CAP(arg_388_0, slot_388_2, slot_388_0)

				return slot_15_48.get_damage(slot_388_3, arg_388_0, slot_388_0, 1)
			end
		},
		W = {
			ignore_obj_radius = 0,
			prediction = {
				range = 0
			},
			slot = player:spellSlot(_W),
			damage = function(arg_389_0, arg_389_1)
				-- function 389
				return 0
			end
		},
		EQ = {
			ignore_obj_radius = 0,
			prediction = {
				delay = 0.25,
				range = 1000,
				type = "Linear",
				width2 = 70,
				boundingRadiusMod = 1,
				width = 35,
				speed = 1300,
				collision = {
					wall = true,
					minion = true,
					hero = true
				}
			},
			slot = player:spellSlot(_E),
			damage = function(arg_390_0, arg_390_1)
				-- function 390
				local slot_390_0 = arg_390_1 or player
				local slot_390_1 = common.GetTotalAP(slot_390_0)
				local slot_390_2 = (({
					60,
					90,
					120,
					150,
					180
				})[slot_390_0:spellSlot(_E).level] or 0) + slot_390_1 * 0.6
				local slot_390_3 = common.CAP(arg_390_0, slot_390_2, slot_390_0)

				return slot_15_48.get_damage(slot_390_3, arg_390_0, slot_390_0, 1)
			end
		},
		EW = {
			ignore_obj_radius = 0,
			prediction = {
				delay = 1.25,
				range = 900,
				boundingRadiusMod = 0,
				type = "Circular",
				radius = 50,
				speed = math.huge,
				collision = {
					wall = false,
					minion = false,
					hero = false
				}
			},
			slot = player:spellSlot(_E),
			damage = function(arg_391_0, arg_391_1)
				-- function 391
				local slot_391_0 = arg_391_1 or player
				local slot_391_1 = common.GetTotalAP(slot_391_0)
				local slot_391_2 = (({
					60,
					90,
					120,
					150,
					180
				})[slot_391_0:spellSlot(_E).level] or 0) + slot_391_1 * 0.6
				local slot_391_3 = common.CAP(arg_391_0, slot_391_2, slot_391_0)

				return slot_15_48.get_damage(slot_391_3, arg_391_0, slot_391_0, 1)
			end
		},
		EE = {
			ignore_obj_radius = 0,
			prediction = {
				delay = 0.6,
				range = 900,
				width = 175,
				boundingRadiusMod = 1,
				range2 = 800,
				type = "Linear",
				speed = math.huge,
				collision = {
					wall = true,
					minion = false,
					hero = false
				}
			},
			slot = player:spellSlot(_E),
			damage = function(arg_392_0, arg_392_1)
				-- function 392
				local slot_392_0 = arg_392_1 or player
				local slot_392_1 = common.GetTotalAP(slot_392_0)
				local slot_392_2 = (({
					60,
					90,
					120,
					150,
					180
				})[slot_392_0:spellSlot(_E).level] or 0) + slot_392_1 * 0.6
				local slot_392_3 = common.CAP(arg_392_0, slot_392_2, slot_392_0)

				return slot_15_48.get_damage(slot_392_3, arg_392_0, slot_392_0, 1)
			end
		},
		R = {
			ignore_obj_radius = 0,
			prediction = {
				delay = 0.25,
				range = 1200,
				type = "Linear",
				width2 = 90,
				boundingRadiusMod = 1,
				width = 90,
				speed = 1400,
				collision = {
					wall = true,
					minion = false,
					hero = false
				}
			},
			slot = player:spellSlot(_R),
			damage = function(arg_393_0, arg_393_1)
				-- function 393
				local slot_393_0 = arg_393_1 or player
				local slot_393_1 = common.GetTotalAP(slot_393_0)
				local slot_393_2 = {
					200,
					300,
					400
				}
				local slot_393_3 = {
					30,
					60,
					90
				}
				local slot_393_4 = slot_393_0:spellSlot(_R).level
				local slot_393_5 = slot_393_2[slot_393_4] or 0
				local slot_393_6 = slot_393_3[slot_393_4] or 0
				local slot_393_7 = slot_393_5 + slot_393_1 * 0.8
				local slot_393_8 = slot_393_6 + slot_393_1 * 0.15
				local slot_393_9 = common.CAP(arg_393_0, slot_393_7, slot_393_0)
				local slot_393_10 = common.CAP(arg_393_0, slot_393_8, slot_393_0)

				return slot_15_48.get_damage(slot_393_9 + slot_393_10, arg_393_0, slot_393_0, 1)
			end
		}
	}
	slot_15_48.Karthus = {
		Q = {
			prediction = {
				delay = 1,
				range = 875,
				black = true,
				boundingRadiusMod = 0,
				range2 = 875,
				type = "Circular",
				radius = 160,
				speed = math.huge
			},
			slot = player:spellSlot(_Q),
			damage = function(arg_394_0, arg_394_1)
				-- function 394
				local slot_394_0 = arg_394_1 or player
				local slot_394_1 = common.GetTotalAP(slot_394_0)
				local slot_394_2 = (({
					40,
					59,
					78,
					97,
					116
				})[slot_394_0:spellSlot(_Q).level] or 0) + slot_394_1 * 0.35
				local slot_394_3 = common.CAP(arg_394_0, slot_394_2, slot_394_0)

				return slot_15_48.get_damage(slot_394_3, arg_394_0, slot_394_0, 1)
			end
		},
		W = {
			ignore_obj_radius = 0,
			prediction = {
				delay = 0.528,
				range = 1000,
				boundingRadiusMod = 0,
				type = "Circular",
				radius = 50,
				speed = math.huge
			},
			slot = player:spellSlot(_W),
			damage = function(arg_395_0, arg_395_1)
				-- function 395
				return 0
			end
		},
		E = {
			ignore_obj_radius = 0,
			prediction = {
				delay = 0,
				range = 500,
				width = 500,
				boundingRadiusMod = 0,
				type = "InRange",
				speed = math.huge
			},
			slot = player:spellSlot(_E),
			damage = function(arg_396_0, arg_396_1)
				-- function 396
				local slot_396_0 = arg_396_1 or player
				local slot_396_1 = common.GetTotalAP(slot_396_0)
				local slot_396_2 = (({
					7.5,
					12.5,
					17.5,
					22.5,
					27.5
				})[slot_396_0:spellSlot(_E).level] or 0) + slot_396_1 * 0.05
				local slot_396_3 = common.CAP(arg_396_0, slot_396_2, slot_396_0)

				return slot_15_48.get_damage(slot_396_3, arg_396_0, slot_396_0, 1)
			end
		},
		R = {
			ignore_obj_radius = 0,
			prediction = {
				type = "InRange",
				range = 20000
			},
			slot = player:spellSlot(_R),
			damage = function(arg_397_0, arg_397_1)
				-- function 397
				local slot_397_0 = arg_397_1 or player
				local slot_397_1 = common.GetTotalAP(slot_397_0)
				local slot_397_2 = (({
					200,
					350,
					500
				})[slot_397_0:spellSlot(_R).level] or 0) + slot_397_1 * 0.75
				local slot_397_3 = common.CAP(arg_397_0, slot_397_2, slot_397_0)

				return slot_15_48.get_damage(slot_397_3, arg_397_0, slot_397_0, 1)
			end
		}
	}
	slot_15_48.Brand = {
		Q = {
			prediction = {
				boundingRadiusMod = 1,
				delay = 0.25,
				black = true,
				width = 30,
				speed = 1600,
				width2 = 60,
				range = 1040,
				type = "Linear",
				collision = {
					wall = true,
					minion = true,
					hero = true
				}
			},
			slot = player:spellSlot(_Q),
			damage = function(arg_398_0, arg_398_1)
				-- function 398
				local slot_398_0 = arg_398_1 or player
				local slot_398_1 = common.GetTotalAP(slot_398_0)
				local slot_398_2 = (({
					80,
					110,
					140,
					170,
					200
				})[slot_398_0:spellSlot(_Q).level] or 0) + slot_398_1 * 0.65
				local slot_398_3 = common.CAP(arg_398_0, slot_398_2, slot_398_0)

				return slot_15_48.get_damage(slot_398_3, arg_398_0, slot_398_0, 1)
			end
		},
		W = {
			ignore_obj_radius = 0,
			prediction = {
				delay = 0.877,
				range = 900,
				boundingRadiusMod = 0,
				type = "Circular",
				radius = 80,
				speed = math.huge
			},
			slot = player:spellSlot(_W),
			damage = function(arg_399_0, arg_399_1)
				-- function 399
				local slot_399_0 = arg_399_1 or player
				local slot_399_1 = common.GetTotalAP(slot_399_0)
				local slot_399_2 = (({
					75,
					120,
					165,
					210,
					255
				})[slot_399_0:spellSlot(_W).level] or 0) + slot_399_1 * 0.6
				local slot_399_3 = common.CAP(arg_399_0, slot_399_2, slot_399_0)

				return slot_15_48.get_damage(slot_399_3, arg_399_0, slot_399_0, 1)
			end
		},
		E = {
			ignore_obj_radius = 0,
			prediction = {
				delay = 0,
				range = 675,
				width = 675,
				boundingRadiusMod = 0,
				type = "InRange",
				speed = math.huge
			},
			slot = player:spellSlot(_E),
			damage = function(arg_400_0, arg_400_1)
				-- function 400
				local slot_400_0 = arg_400_1 or player
				local slot_400_1 = common.GetTotalAP(slot_400_0)
				local slot_400_2 = (({
					60,
					85,
					110,
					135,
					160
				})[slot_400_0:spellSlot(_E).level] or 0) + slot_400_1 * 0.6
				local slot_400_3 = common.CAP(arg_400_0, slot_400_2, slot_400_0)

				return slot_15_48.get_damage(slot_400_3, arg_400_0, slot_400_0, 1)
			end
		},
		R = {
			ignore_obj_radius = 0,
			prediction = {
				range = 750,
				type = "InRange",
				radius = 600
			},
			slot = player:spellSlot(_R),
			damage = function(arg_401_0, arg_401_1)
				-- function 401
				local slot_401_0 = arg_401_1 or player
				local slot_401_1 = common.GetTotalAP(slot_401_0)
				local slot_401_2 = (({
					100,
					175,
					250
				})[slot_401_0:spellSlot(_R).level] or 0) + slot_401_1 * 0.25
				local slot_401_3 = common.CAP(arg_401_0, slot_401_2, slot_401_0)

				return slot_15_48.get_damage(slot_401_3, arg_401_0, slot_401_0, 1)
			end
		}
	}
	slot_15_48.Yone = {
		Q = {
			ignore_obj_radius = 450,
			target_selector = 8,
			prediction = {
				delay = 0.25,
				range = 450,
				type = "Linear",
				boundingRadiusMod = 1,
				width = 40,
				speed = 1500,
				collision = {
					wall = false,
					minion = false,
					hero = false
				}
			},
			cast_spell = {},
			slot = player:spellSlot(_Q),
			damage = function(arg_402_0, arg_402_1)
				-- function 402
				if not arg_402_0 then
					return 0
				end

				local slot_402_0 = arg_402_1 or player
				local slot_402_1 = ({
					20,
					45,
					70,
					95,
					120
				})[slot_402_0:spellSlot(0).level] or 0
				local slot_402_2 = 1.05
				local slot_402_3 = slot_402_0.crit > 0.875
				local slot_402_4 = {}

				if slot_402_0.type == TYPE_HERO then
					for iter_402_0 = 0, 6 do
						local slot_402_5 = slot_402_0:itemID(iter_402_0)

						if slot_402_5 > 0 then
							slot_402_4[slot_402_5] = true
						end
					end
				end

				if slot_402_3 then
					slot_402_2 = 1.46995
				end

				if slot_402_4[3031] and slot_402_3 then
					slot_402_2 = slot_402_2 + 0.29399
				end

				local slot_402_6 = slot_402_1 + common.GetTotalAD(slot_402_0) * slot_402_2
				local slot_402_7 = common.CAD(arg_402_0, slot_402_6, slot_402_0)

				return slot_15_48.get_damage(slot_402_7, arg_402_0, slot_402_0, 2)
			end
		},
		Q3 = {
			ignore_obj_radius = 450,
			target_selector = 8,
			prediction = {
				delay = 0.25,
				range = 1050,
				type = "Linear",
				boundingRadiusMod = 1,
				width = 50,
				speed = 1500,
				collision = {
					wall = true,
					minion = false,
					hero = false
				}
			},
			cast_spell = {},
			slot = player:spellSlot(_Q),
			damage = function(arg_403_0, arg_403_1)
				-- function 403
				if not arg_403_0 then
					return 0
				end

				local slot_403_0 = arg_403_1 or player
				local slot_403_1 = ({
					20,
					40,
					60,
					80,
					100
				})[slot_403_0:spellSlot(0).level] or 0
				local slot_403_2 = 1.05
				local slot_403_3 = slot_403_0.crit > 0.875
				local slot_403_4 = {}

				if slot_403_0.type == TYPE_HERO then
					for iter_403_0 = 0, 6 do
						local slot_403_5 = slot_403_0:itemID(iter_403_0)

						if slot_403_5 > 0 then
							slot_403_4[slot_403_5] = true
						end
					end
				end

				if slot_403_3 then
					slot_403_2 = 1.46995
				end

				if slot_403_4[3031] and slot_403_3 then
					slot_403_2 = slot_403_2 + 0.29399
				end

				local slot_403_6 = slot_403_1 + common.GetTotalAD(slot_403_0) * slot_403_2
				local slot_403_7 = common.CAD(arg_403_0, slot_403_6, slot_403_0)

				return slot_15_48.get_damage(slot_403_7, arg_403_0, slot_403_0, 2)
			end
		},
		W = {
			ignore_obj_radius = 0,
			target_selector = 8,
			prediction = {
				delay = 0.5,
				range = 600,
				type = "Linear",
				boundingRadiusMod = 0,
				width = 50,
				speed = 1800,
				collision = {
					wall = true,
					minion = false,
					hero = false
				}
			},
			cast_spell = {},
			slot = player:spellSlot(_W),
			damage = function(arg_404_0, arg_404_1)
				-- function 404
				if not arg_404_0 then
					return 0
				end

				local slot_404_0 = arg_404_1 or player
				local slot_404_1 = {
					10,
					20,
					30,
					40,
					50
				}
				local slot_404_2 = {
					0.1,
					0.11,
					0.12,
					0.13,
					0.14
				}
				local slot_404_3 = slot_404_0:spellSlot(1).level
				local slot_404_4 = slot_404_1[slot_404_3] or 0
				local slot_404_5 = slot_404_2[slot_404_3] or 0
				local slot_404_6 = slot_404_4 + arg_404_0.maxHealth * slot_404_5
				local slot_404_7 = common.CAP(arg_404_0, slot_404_6 / 2, slot_404_0)
				local slot_404_8 = common.CAD(arg_404_0, slot_404_6 / 2, slot_404_0)

				return slot_15_48.get_damage(slot_404_7 + slot_404_8, arg_404_0, slot_404_0, 1)
			end
		},
		E = {
			ignore_obj_radius = 1100,
			target_selector = 8,
			prediction = {
				delay = 0,
				range = 475,
				dashRadius = 0,
				boundingRadiusModTarget = 1,
				boundingRadiusModSource = 1,
				type = "InRange",
				radius = 475,
				speed = 750 + player.moveSpeed * 0.6
			},
			cast_spell = {},
			slot = player:spellSlot(_E),
			damage = function(arg_405_0, arg_405_1)
				-- function 405
				if not arg_405_0 then
					return 0
				end

				return 0
			end
		},
		R = {
			ignore_obj_radius = 1000,
			target_selector = 8,
			prediction = {
				delay = 0.75,
				range = 1000,
				type = "Linear",
				boundingRadiusMod = 1,
				width = 100,
				speed = math.huge,
				collision = {
					wall = false,
					minion = false,
					hero = false
				}
			},
			cast_spell = {},
			slot = player:spellSlot(_R),
			damage = function(arg_406_0, arg_406_1)
				-- function 406
				if not arg_406_0 then
					return 0
				end

				local slot_406_0 = arg_406_1 or player
				local slot_406_1 = (({
					200,
					400,
					600
				})[slot_406_0:spellSlot(3).level] or 0) + common.GetBonusAD(slot_406_0) * 0.8
				local slot_406_2 = common.CAD(arg_406_0, slot_406_1, slot_406_0)

				return slot_15_48.get_damage(slot_406_2, arg_406_0, slot_406_0, 2)
			end
		}
	}
	slot_15_48.TwistedFate = {
		Q = {
			ignore_obj_radius = 450,
			target_selector = 8,
			prediction = {
				delay = 0.25,
				range = 1450,
				type = "Linear",
				boundingRadiusMod = 0,
				width = 40,
				speed = 1000,
				collision = {
					wall = true,
					minion = false,
					hero = false
				}
			},
			cast_spell = {},
			slot = player:spellSlot(_Q),
			damage = function(arg_407_0, arg_407_1)
				-- function 407
				local slot_407_0 = arg_407_1 or player
				local slot_407_1 = common.GetTotalAP(slot_407_0)
				local slot_407_2 = common.GetBonusAD(slot_407_0)
				local slot_407_3 = (({
					60,
					105,
					150,
					195,
					240
				})[slot_407_0:spellSlot(_Q).level] or 0) + slot_407_1 * 0.9 + slot_407_2 * 0.5
				local slot_407_4 = common.CAP(arg_407_0, slot_407_3, slot_407_0)

				return slot_15_48.get_damage(slot_407_4, arg_407_0, slot_407_0, 1)
			end
		},
		W = {
			ignore_obj_radius = 0,
			target_selector = 8,
			prediction = {
				delay = 0.5,
				range = 600,
				type = "Linear",
				boundingRadiusMod = 0,
				width = 50,
				speed = 1800,
				collision = {
					wall = true,
					minion = false,
					hero = false
				}
			},
			cast_spell = {},
			slot = player:spellSlot(_W),
			damage = function(arg_408_0, arg_408_1, arg_408_2)
				-- function 408
				local slot_408_0 = arg_408_1 or player
				local slot_408_1 = common.GetTotalAP(slot_408_0)
				local slot_408_2 = common.GetTotalAD(slot_408_0)
				local slot_408_3 = {
					40,
					60,
					80,
					100,
					120
				}
				local slot_408_4 = {
					30,
					45,
					60,
					75,
					90
				}
				local slot_408_5 = {
					15,
					22.5,
					30,
					37.5,
					45
				}
				local slot_408_6 = slot_408_3[slot_408_0:spellSlot(_W).level] or 0
				local slot_408_7 = slot_408_6 + slot_408_1 * 1 + slot_408_2 * 1
				local slot_408_8 = slot_408_6 + slot_408_1 * 0.7 + slot_408_2 * 1
				local slot_408_9 = slot_408_6 + slot_408_1 * 0.5 + slot_408_2 * 1
				local slot_408_10 = common.CAP(arg_408_0, slot_408_7, slot_408_0)

				return slot_15_48.get_damage(slot_408_10, arg_408_0, slot_408_0, 1)
			end
		},
		E = {
			ignore_obj_radius = 1100,
			target_selector = 8,
			prediction = {
				delay = 0,
				range = 475,
				dashRadius = 0,
				boundingRadiusModTarget = 1,
				boundingRadiusModSource = 1,
				type = "InRange",
				radius = 475
			},
			cast_spell = {},
			slot = player:spellSlot(_E),
			damage = function(arg_409_0, arg_409_1)
				-- function 409
				if not arg_409_0 then
					return 0
				end

				return 0
			end
		},
		R = {
			ignore_obj_radius = 1000,
			target_selector = 8,
			prediction = {
				delay = 0,
				range = 475,
				dashRadius = 0,
				boundingRadiusModTarget = 1,
				boundingRadiusModSource = 1,
				type = "InRange",
				radius = 475
			},
			cast_spell = {},
			slot = player:spellSlot(_R),
			damage = function(arg_410_0, arg_410_1)
				-- function 410
				return 0
			end
		}
	}
	slot_15_48.Smolder = {
		Q = {
			ignore_obj_radius = 450,
			target_selector = 8,
			prediction = {
				delay = 0,
				range = 550,
				dashRadius = 0,
				boundingRadiusModTarget = 1,
				boundingRadiusModSource = 1,
				type = "InRange",
				radius = 550
			},
			cast_spell = {},
			slot = player:spellSlot(_Q),
			damage = function(arg_411_0, arg_411_1)
				-- function 411
				local slot_411_0 = arg_411_1 or player
				local slot_411_1 = common.GetTotalAP(slot_411_0)
				local slot_411_2 = common.GetBonusAD(slot_411_0)
				local slot_411_3 = (({
					65,
					80,
					95,
					110,
					125
				})[slot_411_0:spellSlot(_Q).level] or 0) + slot_411_2 * 1.3
				local slot_411_4 = common.CAD(arg_411_0, slot_411_3, slot_411_0)
				local slot_411_5 = 0

				if slot_411_0.buff.smolderqpassive then
					local slot_411_6 = slot_411_0.buff.smolderqpassive.stacks2

					if slot_411_6 > 0 then
						local slot_411_7 = common.CAP(arg_411_0, slot_411_6 * 0.3, slot_411_0)

						slot_411_5 = slot_15_48.get_damage(slot_411_7, arg_411_0, slot_411_0, 1)
					end
				end

				return slot_15_48.get_damage(slot_411_4, arg_411_0, slot_411_0, 2) + slot_411_5
			end
		},
		W = {
			ignore_obj_radius = 0,
			target_selector = 8,
			prediction = {
				delay = 0.35,
				range = 1200,
				slot = true,
				type = "Linear",
				boundingRadiusMod = 0,
				width = 40,
				speed = 2000,
				collision = {
					wall = true,
					minion = false,
					hero = false
				}
			},
			cast_spell = {},
			slot = player:spellSlot(_W),
			damage = function(arg_412_0, arg_412_1, arg_412_2)
				-- function 412
				local slot_412_0 = arg_412_1 or player
				local slot_412_1 = common.GetTotalAP(slot_412_0)
				local slot_412_2 = common.GetBonusAD(slot_412_0)
				local slot_412_3 = {
					30,
					50,
					70,
					90,
					110
				}
				local slot_412_4 = slot_412_0:spellSlot(_W).level
				local slot_412_5 = (slot_412_3[slot_412_4] or 0) + slot_412_2 * 0.6
				local slot_412_6 = 0

				if slot_412_0.type == TYPE_HERO and arg_412_0.type == TYPE_HERO and slot_412_0.buff.smolderqpassive then
					local slot_412_7 = ({
						30,
						50,
						70,
						90,
						110
					})[slot_412_4] or 0
					local slot_412_8 = slot_412_0.buff.smolderqpassive.stacks2

					slot_412_5 = slot_412_7 + slot_412_1 * 0.8 + slot_412_2 * 0.6 + slot_412_5

					if slot_412_8 > 0 then
						local slot_412_9 = common.CAP(arg_412_0, slot_412_8 * 0.55, slot_412_0)

						slot_412_6 = slot_15_48.get_damage(slot_412_9, arg_412_0, slot_412_0, 2)
					end
				end

				local slot_412_10 = common.CAD(arg_412_0, slot_412_5, slot_412_0)

				return slot_15_48.get_damage(slot_412_10, arg_412_0, slot_412_0, 2) + slot_412_6
			end
		},
		E = {
			ignore_obj_radius = 1100,
			target_selector = 8,
			prediction = {
				delay = 0,
				range = 475,
				dashRadius = 0,
				boundingRadiusModTarget = 1,
				boundingRadiusModSource = 1,
				type = "InRange",
				radius = 475
			},
			cast_spell = {},
			slot = player:spellSlot(_E),
			damage = function(arg_413_0, arg_413_1)
				-- function 413
				local slot_413_0 = arg_413_1 or player
				local slot_413_1 = common.GetTotalAP(slot_413_0)
				local slot_413_2 = common.GetTotalAD(slot_413_0)
				local slot_413_3 = (({
					5,
					10,
					15,
					20,
					25
				})[slot_413_0:spellSlot(_E).level] or 0) + slot_413_2 * 0.25
				local slot_413_4 = 0

				if slot_413_0.type == TYPE_HERO and slot_413_0.buff.smolderqpassive then
					local slot_413_5 = slot_413_0.buff.smolderqpassive.stacks2

					if slot_413_5 > 0 then
						local slot_413_6 = common.CAP(arg_413_0, slot_413_5 * 0.1, slot_413_0)

						slot_413_4 = slot_15_48.get_damage(slot_413_6, arg_413_0, slot_413_0, 2)
					end
				end

				local slot_413_7 = common.CAD(arg_413_0, slot_413_3, slot_413_0)

				return slot_15_48.get_damage(slot_413_7, arg_413_0, slot_413_0, 2) + slot_413_4
			end
		},
		R = {
			ignore_obj_radius = 4250,
			target_selector = 8,
			prediction = {
				delay = 0.75,
				range = 4250,
				slot = true,
				type = "Linear",
				boundingRadiusMod = 0,
				width = 2,
				speed = 1700,
				collision = {
					wall = true,
					minion = false,
					hero = false
				}
			},
			cast_spell = {},
			slot = player:spellSlot(_R),
			damage = function(arg_414_0, arg_414_1)
				-- function 414
				local slot_414_0 = arg_414_1 or player
				local slot_414_1 = common.GetTotalAP(slot_414_0)
				local slot_414_2 = common.GetBonusAD(slot_414_0)
				local slot_414_3 = (({
					200,
					300,
					400
				})[slot_414_0:spellSlot(_R).level] or 0) + slot_414_2 * 1.1 + slot_414_1 * 1
				local slot_414_4 = 0
				local slot_414_5 = common.CAD(arg_414_0, slot_414_3, slot_414_0)

				return slot_15_48.get_damage(slot_414_5, arg_414_0, slot_414_0, 2) + slot_414_4
			end
		}
	}
	slot_15_48.Diana = {
		Q = {
			ignore_obj_radius = 450,
			target_selector = 8,
			prediction = {
				delay = 0.25,
				range = 900,
				type = "Linear",
				boundingRadiusMod = 0,
				width = 60,
				speed = 2100,
				collision = {
					wall = true,
					minion = false,
					hero = false
				}
			},
			cast_spell = {},
			slot = player:spellSlot(_Q),
			damage = function(arg_415_0, arg_415_1)
				-- function 415
				local slot_415_0 = arg_415_1 or player
				local slot_415_1 = common.GetTotalAP(slot_415_0)
				local slot_415_2 = common.GetTotalAD(slot_415_0)
				local slot_415_3 = (({
					60,
					95,
					130,
					165,
					200
				})[slot_415_0:spellSlot(_Q).level] or 0) + slot_415_1 * 0.7
				local slot_415_4 = common.CAP(arg_415_0, slot_415_3, slot_415_0)

				return slot_15_48.get_damage(slot_415_4, arg_415_0, slot_415_0, 1)
			end
		},
		W = {
			ignore_obj_radius = 0,
			target_selector = 8,
			prediction = {
				delay = 0,
				range = 200,
				dashRadius = 0,
				boundingRadiusModTarget = 1,
				boundingRadiusModSource = 1,
				type = "InRange",
				radius = 200
			},
			cast_spell = {},
			slot = player:spellSlot(_W),
			damage = function(arg_416_0, arg_416_1, arg_416_2)
				-- function 416
				local slot_416_0 = arg_416_1 or player
				local slot_416_1 = common.GetTotalAP(slot_416_0)
				local slot_416_2 = common.GetTotalAD(slot_416_0)
				local slot_416_3 = (({
					18,
					30,
					42,
					54,
					66
				})[slot_416_0:spellSlot(_W).level] or 0) + slot_416_1 * 0.18
				local slot_416_4 = common.CAP(arg_416_0, slot_416_3, slot_416_0)

				return slot_15_48.get_damage(slot_416_4, arg_416_0, slot_416_0, 1)
			end
		},
		E = {
			ignore_obj_radius = 825,
			target_selector = 8,
			prediction = {
				delay = 0,
				range = 825,
				dashRadius = 0,
				boundingRadiusModTarget = 1,
				boundingRadiusModSource = 1,
				type = "InRange",
				radius = 825
			},
			cast_spell = {},
			slot = player:spellSlot(_E),
			damage = function(arg_417_0, arg_417_1)
				-- function 417
				local slot_417_0 = arg_417_1 or player
				local slot_417_1 = common.GetTotalAP(slot_417_0)
				local slot_417_2 = common.GetTotalAD(slot_417_0)
				local slot_417_3 = (({
					50,
					70,
					90,
					110,
					130
				})[slot_417_0:spellSlot(_E).level] or 0) + slot_417_1 * 0.6
				local slot_417_4 = common.CAP(arg_417_0, slot_417_3, slot_417_0)

				return slot_15_48.get_damage(slot_417_4, arg_417_0, slot_417_0, 1)
			end
		},
		R = {
			ignore_obj_radius = 4250,
			target_selector = 8,
			prediction = {
				delay = 0,
				range = 450,
				dashRadius = 0,
				boundingRadiusModTarget = 0,
				boundingRadiusModSource = 0,
				type = "InRange",
				radius = 450
			},
			cast_spell = {},
			slot = player:spellSlot(_R),
			damage = function(arg_418_0, arg_418_1)
				-- function 418
				local slot_418_0 = arg_418_1 or player
				local slot_418_1 = common.GetTotalAP(slot_418_0)
				local slot_418_2 = common.GetBonusAD(slot_418_0)
				local slot_418_3 = (({
					200,
					300,
					400
				})[slot_418_0:spellSlot(_R).level] or 0) + slot_418_1 * 0.6
				local slot_418_4 = common.CAP(arg_418_0, slot_418_3, slot_418_0)

				return slot_15_48.get_damage(slot_418_4, arg_418_0, slot_418_0, 1)
			end
		}
	}
	slot_15_48.Swain = {
		Q = {
			ignore_obj_radius = 450,
			target_selector = 8,
			prediction = {
				delay = 0.25,
				range = 725,
				type = "Linear",
				boundingRadiusMod = 0,
				width = 60,
				speed = math.huge,
				collision = {
					wall = true,
					minion = false,
					hero = false
				}
			},
			cast_spell = {},
			slot = player:spellSlot(_Q),
			damage = function(arg_419_0, arg_419_1)
				-- function 419
				local slot_419_0 = arg_419_1 or player
				local slot_419_1 = common.GetTotalAP(slot_419_0)
				local slot_419_2 = common.GetTotalAD(slot_419_0)
				local slot_419_3 = (({
					60,
					90,
					120,
					150,
					180
				})[slot_419_0:spellSlot(_Q).level] or 0) + slot_419_1 * 0.45
				local slot_419_4 = common.CAP(arg_419_0, slot_419_3, slot_419_0)

				return slot_15_48.get_damage(slot_419_4, arg_419_0, slot_419_0, 1)
			end
		},
		W = {
			ignore_obj_radius = 0,
			target_selector = 8,
			prediction = {
				delay = 1.5,
				range = 5500,
				radius = 162.5,
				boundingRadiusMod = 0,
				type = "Circular",
				range1 = {
					5500,
					6000,
					6500,
					7000,
					7500
				},
				speed = math.huge,
				collision = {
					wall = false,
					minion = false,
					hero = false
				}
			},
			cast_spell = {},
			slot = player:spellSlot(_W),
			damage = function(arg_420_0, arg_420_1, arg_420_2)
				-- function 420
				local slot_420_0 = arg_420_1 or player
				local slot_420_1 = common.GetTotalAP(slot_420_0)
				local slot_420_2 = common.GetTotalAD(slot_420_0)
				local slot_420_3 = (({
					70,
					105,
					140,
					175,
					210
				})[slot_420_0:spellSlot(_W).level] or 0) + slot_420_1 * 0.6
				local slot_420_4 = common.CAP(arg_420_0, slot_420_3, slot_420_0)

				return slot_15_48.get_damage(slot_420_4, arg_420_0, slot_420_0, 1)
			end
		},
		E = {
			ignore_obj_radius = 825,
			target_selector = 8,
			prediction = {
				delay = 0.25,
				range = 960,
				type = "Linear",
				boundingRadiusMod = 0,
				width = 85,
				speed = 1200,
				collision = {
					wall = true,
					minion = true,
					hero = false
				}
			},
			cast_spell = {},
			slot = player:spellSlot(_E),
			damage = function(arg_421_0, arg_421_1)
				-- function 421
				local slot_421_0 = arg_421_1 or player
				local slot_421_1 = common.GetTotalAP(slot_421_0)
				local slot_421_2 = common.GetTotalAD(slot_421_0)
				local slot_421_3 = (({
					80,
					120,
					160,
					200,
					240
				})[slot_421_0:spellSlot(_E).level] or 0) + slot_421_1 * 0.6
				local slot_421_4 = common.CAP(arg_421_0, slot_421_3, slot_421_0)

				return slot_15_48.get_damage(slot_421_4, arg_421_0, slot_421_0, 1)
			end
		},
		R = {
			ignore_obj_radius = 650,
			target_selector = 8,
			prediction = {
				delay = 0,
				range = 650,
				dashRadius = 0,
				boundingRadiusModTarget = 0,
				boundingRadiusModSource = 0,
				type = "InRange",
				radius = 650
			},
			cast_spell = {},
			slot = player:spellSlot(_R),
			damage = function(arg_422_0, arg_422_1)
				-- function 422
				local slot_422_0 = arg_422_1 or player
				local slot_422_1 = common.GetTotalAP(slot_422_0)
				local slot_422_2 = common.GetBonusAD(slot_422_0)
				local slot_422_3 = (({
					150,
					250,
					350
				})[slot_422_0:spellSlot(_R).level] or 0) + slot_422_1 * 0.5
				local slot_422_4 = common.CAP(arg_422_0, slot_422_3, slot_422_0)

				return slot_15_48.get_damage(slot_422_4, arg_422_0, slot_422_0, 1)
			end
		}
	}
	slot_15_48.Volibear = {
		Q = {
			ignore_obj_radius = 450,
			target_selector = 8,
			prediction = {
				delay = 0,
				range = 0,
				dashRadius = 0,
				boundingRadiusModTarget = 1,
				boundingRadiusModSource = 1,
				type = "InRange",
				radius = 0
			},
			cast_spell = {},
			slot = player:spellSlot(_Q),
			damage = function(arg_423_0, arg_423_1)
				-- function 423
				local slot_423_0 = arg_423_1 or player
				local slot_423_1 = common.GetTotalAP(slot_423_0)
				local slot_423_2 = common.GetBonusAD(slot_423_0)
				local slot_423_3 = (({
					10,
					30,
					50,
					70,
					90
				})[slot_423_0:spellSlot(_Q).level] or 0) + slot_423_2 * 1.2
				local slot_423_4 = common.CAD(arg_423_0, slot_423_3, slot_423_0)

				return slot_15_48.get_damage(slot_423_4, arg_423_0, slot_423_0, 2)
			end
		},
		W = {
			ignore_obj_radius = 0,
			target_selector = 8,
			prediction = {
				delay = 0,
				range = 350,
				dashRadius = 0,
				boundingRadiusModTarget = 1,
				boundingRadiusModSource = 1,
				type = "InRange",
				radius = 350
			},
			cast_spell = {},
			slot = player:spellSlot(_W),
			damage = function(arg_424_0, arg_424_1, arg_424_2)
				-- function 424
				local slot_424_0 = arg_424_1 or player
				local slot_424_1 = common.GetTotalAP(slot_424_0)
				local slot_424_2 = common.GetTotalAD(slot_424_0)
				local slot_424_3 = {
					5,
					30,
					55,
					80,
					105
				}
				local slot_424_4 = (arg_424_0.maxHealth - arg_424_0.baseHealth) * 0.06
				local slot_424_5 = (slot_424_3[slot_424_0:spellSlot(_W).level] or 0) + slot_424_2 * 1 + slot_424_4
				local slot_424_6 = common.CAD(arg_424_0, slot_424_5, slot_424_0)

				return slot_15_48.get_damage(slot_424_6, arg_424_0, slot_424_0, 2)
			end
		},
		E = {
			ignore_obj_radius = 825,
			target_selector = 8,
			prediction = {
				delay = 2,
				range = 1200,
				boundingRadiusMod = 0,
				type = "Circular",
				radius = 325,
				speed = math.huge
			},
			cast_spell = {},
			slot = player:spellSlot(_E),
			damage = function(arg_425_0, arg_425_1)
				-- function 425
				local slot_425_0 = arg_425_1 or player
				local slot_425_1 = common.GetTotalAP(slot_425_0)
				local slot_425_2 = common.GetTotalAD(slot_425_0)
				local slot_425_3 = {
					80,
					110,
					140,
					170,
					200
				}
				local slot_425_4 = {
					0.11,
					0.12,
					0.13,
					0.14,
					0.15
				}
				local slot_425_5 = slot_425_0:spellSlot(_E).level
				local slot_425_6 = slot_425_3[slot_425_5] or 0
				local slot_425_7 = slot_425_4[slot_425_5] or 0
				local slot_425_8 = arg_425_0.maxHealth * slot_425_7
				local slot_425_9 = slot_425_6 + slot_425_1 * 0.8 + slot_425_8
				local slot_425_10 = common.CAP(arg_425_0, slot_425_9, slot_425_0)

				return slot_15_48.get_damage(slot_425_10, arg_425_0, slot_425_0, 1)
			end
		},
		R = {
			ignore_obj_radius = 4250,
			target_selector = 8,
			prediction = {
				delay = 0,
				range = 700,
				speed = 750,
				boundingRadiusMod = 0,
				type = "Circular",
				radius = 300
			},
			cast_spell = {},
			slot = player:spellSlot(_R),
			damage = function(arg_426_0, arg_426_1)
				-- function 426
				local slot_426_0 = arg_426_1 or player
				local slot_426_1 = common.GetTotalAP(slot_426_0)
				local slot_426_2 = common.GetBonusAD(slot_426_0)
				local slot_426_3 = (({
					300,
					500,
					700
				})[slot_426_0:spellSlot(_R).level] or 0) + slot_426_1 * 1.25 + slot_426_2 * 2.5
				local slot_426_4 = common.CAD(arg_426_0, slot_426_3, slot_426_0)

				return slot_15_48.get_damage(slot_426_4, arg_426_0, slot_426_0, 2)
			end
		}
	}
	slot_15_48.Olaf = {
		Q = {
			ignore_obj_radius = 450,
			target_selector = 8,
			prediction = {
				delay = 0.25,
				range = 1000,
				type = "Linear",
				boundingRadiusMod = 0,
				width = 90,
				speed = 1600,
				collision = {
					wall = true,
					minion = false,
					hero = false
				}
			},
			cast_spell = {},
			slot = player:spellSlot(_Q),
			damage = function(arg_427_0, arg_427_1)
				-- function 427
				local slot_427_0 = arg_427_1 or player
				local slot_427_1 = common.GetTotalAP(slot_427_0)
				local slot_427_2 = common.GetBonusAD(slot_427_0)
				local slot_427_3 = {
					65,
					115,
					165,
					215,
					265
				}
				local slot_427_4 = slot_427_0:spellSlot(_Q).level
				local slot_427_5 = (slot_427_3[slot_427_4] or 0) + slot_427_2 * 1

				if arg_427_0.type == TYPE_MINION and targe.team == TEAM_NEUTRAL then
					slot_427_5 = slot_427_5 + (({
						5,
						15,
						25,
						35,
						45
					})[slot_427_4] or 0)
				end

				local slot_427_6 = common.CAD(arg_427_0, slot_427_5, slot_427_0)

				return slot_15_48.get_damage(slot_427_6, arg_427_0, slot_427_0, 2)
			end
		},
		W = {
			ignore_obj_radius = 0,
			target_selector = 8,
			prediction = {
				delay = 0,
				range = 0,
				dashRadius = 0,
				boundingRadiusModTarget = 1,
				boundingRadiusModSource = 1,
				type = "InRange",
				radius = 0
			},
			cast_spell = {},
			slot = player:spellSlot(_W),
			damage = function(arg_428_0, arg_428_1)
				-- function 428
				return 0
			end
		},
		E = {
			ignore_obj_radius = 825,
			target_selector = 8,
			prediction = {
				delay = 0,
				range = 325,
				dashRadius = 0,
				boundingRadiusModTarget = 1,
				boundingRadiusModSource = 1,
				type = "InRange",
				radius = 0
			},
			cast_spell = {},
			slot = player:spellSlot(_E),
			damage = function(arg_429_0, arg_429_1)
				-- function 429
				local slot_429_0 = arg_429_1 or player
				local slot_429_1 = common.GetTotalAP(slot_429_0)
				local slot_429_2 = common.GetTotalAD(slot_429_0)

				return (({
					70,
					115,
					160,
					205,
					250
				})[slot_429_0:spellSlot(_E).level] or 0) + slot_429_2 * 0.5
			end
		},
		R = {
			ignore_obj_radius = 4250,
			target_selector = 8,
			prediction = {
				delay = 0,
				range = 0,
				dashRadius = 0,
				boundingRadiusModTarget = 1,
				boundingRadiusModSource = 1,
				type = "InRange",
				radius = 0
			},
			cast_spell = {},
			slot = player:spellSlot(_R),
			damage = function(arg_430_0, arg_430_1)
				-- function 430
				return 0
			end
		}
	}
	slot_15_48.Lillia = {
		Q = {
			ignore_obj_radius = 450,
			target_selector = 8,
			prediction = {
				delay = 0.25,
				range = 485,
				dashRadius = 0,
				boundingRadiusModTarget = 1,
				boundingRadiusModSource = 1,
				type = "InRange",
				radius = 485
			},
			cast_spell = {},
			slot = player:spellSlot(_Q),
			damage = function(arg_431_0, arg_431_1)
				-- function 431
				local slot_431_0 = arg_431_1 or player
				local slot_431_1 = common.GetTotalAP(slot_431_0)
				local slot_431_2 = common.GetTotalAD(slot_431_0)
				local slot_431_3 = (({
					35,
					45,
					55,
					65,
					75
				})[slot_431_0:spellSlot(_Q).level] or 0) + slot_431_1 * 0.35
				local slot_431_4 = common.CAP(arg_431_0, slot_431_3, slot_431_0)

				return slot_15_48.get_damage(slot_431_4, arg_431_0, slot_431_0, 1)
			end
		},
		W = {
			ignore_obj_radius = 0,
			target_selector = 8,
			prediction = {
				delay = 0.759,
				range = 350,
				speed = 1400,
				boundingRadiusMod = 0,
				type = "Circular",
				radius = 65
			},
			cast_spell = {},
			slot = player:spellSlot(_W),
			damage = function(arg_432_0, arg_432_1)
				-- function 432
				local slot_432_0 = arg_432_1 or player
				local slot_432_1 = common.GetTotalAP(slot_432_0)
				local slot_432_2 = common.GetTotalAD(slot_432_0)
				local slot_432_3 = (({
					80,
					100,
					120,
					140,
					160
				})[slot_432_0:spellSlot(_W).level] or 0) + slot_432_1 * 0.35
				local slot_432_4 = common.CAP(arg_432_0, slot_432_3, slot_432_0)

				return slot_15_48.get_damage(slot_432_4, arg_432_0, slot_432_0, 1)
			end
		},
		E = {
			ignore_obj_radius = 825,
			target_selector = 8,
			prediction = {
				delay = 0.4,
				range = 3000,
				type = "Linear",
				boundingRadiusMod = 0,
				width = 60,
				speed = 1400,
				collision = {
					wall = true,
					minion = true,
					hero = true
				}
			},
			cast_spell = {},
			slot = player:spellSlot(_E),
			damage = function(arg_433_0, arg_433_1)
				-- function 433
				local slot_433_0 = arg_433_1 or player
				local slot_433_1 = common.GetTotalAP(slot_433_0)
				local slot_433_2 = common.GetTotalAD(slot_433_0)
				local slot_433_3 = (({
					70,
					95,
					120,
					145,
					170
				})[slot_433_0:spellSlot(_E).level] or 0) + slot_433_1 * 0.6
				local slot_433_4 = common.CAP(arg_433_0, slot_433_3, slot_433_0)

				return slot_15_48.get_damage(slot_433_4, arg_433_0, slot_433_0, 1)
			end
		},
		R = {
			ignore_obj_radius = 4250,
			target_selector = 8,
			prediction = {
				delay = 0,
				range = 0,
				dashRadius = 0,
				boundingRadiusModTarget = 1,
				boundingRadiusModSource = 1,
				type = "InRange",
				radius = 0
			},
			cast_spell = {},
			slot = player:spellSlot(_R),
			damage = function(arg_434_0, arg_434_1)
				-- function 434
				return 0
			end
		}
	}
	slot_15_48.Nocturne = {
		Q = {
			ignore_obj_radius = 450,
			target_selector = 8,
			prediction = {
				delay = 0.25,
				range = 1200,
				type = "Linear",
				boundingRadiusMod = 0,
				width = 120,
				speed = 1600,
				collision = {
					wall = true,
					minion = false,
					hero = false
				}
			},
			cast_spell = {},
			slot = player:spellSlot(_Q),
			damage = function(arg_435_0, arg_435_1)
				-- function 435
				local slot_435_0 = arg_435_1 or player
				local slot_435_1 = common.GetTotalAP(slot_435_0)
				local slot_435_2 = common.GetBonusAD(slot_435_0)
				local slot_435_3 = (({
					65,
					110,
					155,
					200,
					245
				})[slot_435_0:spellSlot(_Q).level] or 0) + slot_435_2 * 0.85
				local slot_435_4 = common.CAD(arg_435_0, slot_435_3, slot_435_0)

				return slot_15_48.get_damage(slot_435_4, arg_435_0, slot_435_0, 2)
			end
		},
		W = {
			ignore_obj_radius = 0,
			target_selector = 8,
			prediction = {
				delay = 0,
				range = 0,
				dashRadius = 0,
				boundingRadiusModTarget = 1,
				boundingRadiusModSource = 1,
				type = "InRange",
				radius = 0
			},
			cast_spell = {},
			slot = player:spellSlot(_W),
			damage = function(arg_436_0, arg_436_1)
				-- function 436
				return 0
			end
		},
		E = {
			ignore_obj_radius = 825,
			target_selector = 8,
			prediction = {
				delay = 0,
				range = 465,
				dashRadius = 0,
				boundingRadiusModTarget = 1,
				boundingRadiusModSource = 1,
				type = "InRange",
				radius = 465
			},
			cast_spell = {},
			slot = player:spellSlot(_E),
			damage = function(arg_437_0, arg_437_1)
				-- function 437
				local slot_437_0 = arg_437_1 or player
				local slot_437_1 = common.GetTotalAP(slot_437_0)
				local slot_437_2 = common.GetTotalAD(slot_437_0)
				local slot_437_3 = (({
					80,
					125,
					170,
					215,
					260
				})[slot_437_0:spellSlot(_E).level] or 0) + slot_437_1 * 1
				local slot_437_4 = common.CAP(arg_437_0, slot_437_3, slot_437_0)

				return slot_15_48.get_damage(slot_437_4, arg_437_0, slot_437_0, 1)
			end
		},
		R = {
			ignore_obj_radius = 4250,
			target_selector = 8,
			prediction = {
				delay = 0,
				range = 2500,
				dashRadius = 1800,
				boundingRadiusModSource = 1,
				boundingRadiusModTarget = 1,
				type = "InRange",
				radius = 0,
				range2 = {
					2500,
					3250,
					4000
				}
			},
			cast_spell = {},
			slot = player:spellSlot(_R),
			damage = function(arg_438_0, arg_438_1)
				-- function 438
				local slot_438_0 = arg_438_1 or player
				local slot_438_1 = common.GetTotalAP(slot_438_0)
				local slot_438_2 = common.GetBonusAD(slot_438_0)
				local slot_438_3 = (({
					150,
					275,
					400
				})[slot_438_0:spellSlot(_R).level] or 0) + slot_438_2 * 1.2
				local slot_438_4 = common.CAD(arg_438_0, slot_438_3, slot_438_0)

				return slot_15_48.get_damage(slot_438_4, arg_438_0, slot_438_0, 2)
			end
		}
	}
	slot_15_48.DrMundo = {
		Q = {
			ignore_obj_radius = 450,
			target_selector = 8,
			prediction = {
				delay = 0.25,
				range = 990,
				type = "Linear",
				boundingRadiusMod = 1,
				width = 60,
				speed = 2000,
				collision = {
					wall = true,
					minion = true,
					hero = true
				}
			},
			cast_spell = {},
			slot = player:spellSlot(_Q),
			damage = function(arg_439_0, arg_439_1)
				-- function 439
				local slot_439_0 = arg_439_1 or player
				local slot_439_1 = common.GetTotalAP(slot_439_0)
				local slot_439_2 = common.GetBonusAD(slot_439_0)
				local slot_439_3 = slot_439_0:spellSlot(_Q).level

				if arg_439_0.type == TYPE_MINION then
					return ({
						80,
						130,
						180,
						230,
						280
					})[slot_439_3] or 0
				end

				local slot_439_4 = ({
					0.2,
					0.225,
					0.25,
					0.275,
					0.3
				})[slot_439_3] or 0
				local slot_439_5 = arg_439_0.health * slot_439_4
				local slot_439_6 = common.CAP(arg_439_0, slot_439_5, slot_439_0)

				return slot_15_48.get_damage(slot_439_6, arg_439_0, slot_439_0, 1)
			end
		},
		W = {
			ignore_obj_radius = 0,
			target_selector = 8,
			prediction = {
				delay = 0,
				range = 325,
				dashRadius = 0,
				boundingRadiusModTarget = 1,
				boundingRadiusModSource = 1,
				type = "InRange",
				radius = 325
			},
			cast_spell = {},
			slot = player:spellSlot(_W),
			damage = function(arg_440_0, arg_440_1)
				-- function 440
				local slot_440_0 = arg_440_1 or player
				local slot_440_1 = common.GetTotalAP(slot_440_0)
				local slot_440_2 = common.GetTotalAD(slot_440_0)
				local slot_440_3 = ({
					5,
					8.75,
					12.5,
					16.25,
					20
				})[slot_440_0:spellSlot(_W).level] or 0
				local slot_440_4 = common.CAP(arg_440_0, slot_440_3, slot_440_0)

				return slot_15_48.get_damage(slot_440_4, arg_440_0, slot_440_0, 1)
			end
		},
		E = {
			ignore_obj_radius = 825,
			target_selector = 8,
			prediction = {
				delay = 0.25,
				range = 800,
				type = "Linear",
				boundingRadiusMod = 0,
				width = 65,
				speed = 1800,
				collision = {
					wall = true,
					minion = false,
					hero = false
				}
			},
			cast_spell = {},
			slot = player:spellSlot(_E),
			damage = function(arg_441_0, arg_441_1)
				-- function 441
				local slot_441_0 = arg_441_1 or player
				local slot_441_1 = common.GetTotalAP(slot_441_0)
				local slot_441_2 = common.GetTotalAD(slot_441_0)
				local slot_441_3 = {
					5,
					15,
					25,
					35,
					45
				}
				local slot_441_4 = slot_441_0:spellSlot(_E).level
				local slot_441_5 = (player.maxHealth - player.baseHealth) * 0.07
				local slot_441_6 = (slot_441_3[slot_441_4] or 0) + slot_441_5 * 0.07
				local slot_441_7 = common.CAP(arg_441_0, slot_441_6, slot_441_0) * 1.4 + common.CAA(arg_441_0, slot_441_0)

				return slot_15_48.get_damage(slot_441_7, arg_441_0, slot_441_0, 1)
			end
		},
		R = {
			ignore_obj_radius = 4250,
			target_selector = 8,
			prediction = {
				delay = 0,
				range = 0,
				dashRadius = 0,
				boundingRadiusModTarget = 1,
				boundingRadiusModSource = 1,
				type = "InRange",
				radius = 0
			},
			cast_spell = {},
			slot = player:spellSlot(_R),
			damage = function(arg_442_0, arg_442_1)
				-- function 442
				return 0
			end
		}
	}
	slot_15_48.Zilean = {
		Q = {
			target_selector = 8,
			ignore_obj_radius = 450,
			prediction = {
				delay = 0.25,
				range = 900,
				speed = 1400,
				boundingRadiusMod = 1,
				type = "Circular",
				radius = 140,
				collision = {
					wall = true,
					minion = true,
					hero = true
				}
			},
			prediction_ig = {
				delay = 0.25,
				range = 900,
				speed = 1400,
				ig = true,
				boundingRadiusMod = 1,
				type = "Circular",
				radius = 140,
				collision = {
					wall = true,
					minion = true,
					hero = true
				}
			},
			cast_spell = {},
			slot = player:spellSlot(_Q),
			damage = function(arg_443_0, arg_443_1)
				-- function 443
				local slot_443_0 = arg_443_1 or player
				local slot_443_1 = common.GetTotalAP(slot_443_0)
				local slot_443_2 = common.GetBonusAD(slot_443_0)
				local slot_443_3 = slot_443_0:spellSlot(_Q).level
				local slot_443_4 = (({
					75,
					115,
					165,
					230,
					300
				})[slot_443_3] or 0) + slot_443_1 * 0.9
				local slot_443_5 = common.CAP(arg_443_0, slot_443_4, slot_443_0)

				return slot_15_48.get_damage(slot_443_5, arg_443_0, slot_443_0, 1)
			end
		},
		W = {
			ignore_obj_radius = 0,
			target_selector = 8,
			prediction = {
				delay = 0,
				range = 0,
				dashRadius = 0,
				boundingRadiusModTarget = 1,
				boundingRadiusModSource = 1,
				type = "InRange",
				radius = 0
			},
			cast_spell = {},
			slot = player:spellSlot(_W),
			damage = function(arg_444_0, arg_444_1)
				-- function 444
				return 0
			end
		},
		E = {
			ignore_obj_radius = 825,
			target_selector = 8,
			prediction = {
				delay = 0,
				range = 550,
				dashRadius = 0,
				boundingRadiusModTarget = 1,
				boundingRadiusModSource = 1,
				type = "InRange",
				radius = 550
			},
			cast_spell = {},
			slot = player:spellSlot(_E),
			damage = function(arg_445_0, arg_445_1)
				-- function 445
				return 0
			end
		},
		R = {
			ignore_obj_radius = 4250,
			target_selector = 8,
			prediction = {
				delay = 0,
				range = 900,
				dashRadius = 0,
				boundingRadiusModTarget = 1,
				boundingRadiusModSource = 1,
				type = "InRange",
				radius = 900
			},
			cast_spell = {},
			slot = player:spellSlot(_R),
			damage = function(arg_446_0, arg_446_1)
				-- function 446
				return 0
			end
		}
	}
	slot_15_48.Kayle = {
		Q = {
			ignore_obj_radius = 450,
			target_selector = 8,
			prediction = {
				delay = 0.25,
				range = 900,
				type = "Linear",
				boundingRadiusMod = 1,
				width = 75,
				speed = 1600,
				collision = {
					wall = true,
					minion = false,
					hero = false
				}
			},
			cast_spell = {},
			slot = player:spellSlot(_Q),
			damage = function(arg_447_0, arg_447_1)
				-- function 447
				local slot_447_0 = arg_447_1 or player
				local slot_447_1 = common.GetTotalAP(slot_447_0)
				local slot_447_2 = common.GetBonusAD(slot_447_0)
				local slot_447_3 = slot_447_0:spellSlot(_Q).level
				local slot_447_4 = (({
					60,
					100,
					140,
					180,
					220
				})[slot_447_3] or 0) + slot_447_1 * 0.5 + slot_447_2 * 0.6
				local slot_447_5 = common.CAP(arg_447_0, slot_447_4, slot_447_0)

				return slot_15_48.get_damage(slot_447_5, arg_447_0, slot_447_0, 1)
			end
		},
		W = {
			ignore_obj_radius = 0,
			target_selector = 8,
			prediction = {
				delay = 0,
				range = 0,
				dashRadius = 0,
				boundingRadiusModTarget = 1,
				boundingRadiusModSource = 1,
				type = "InRange",
				radius = 0
			},
			cast_spell = {},
			slot = player:spellSlot(_W),
			damage = function(arg_448_0, arg_448_1)
				-- function 448
				return 0
			end
		},
		E = {
			ignore_obj_radius = 825,
			target_selector = 8,
			prediction = {
				delay = 0,
				range = 590,
				dashRadius = 0,
				boundingRadiusModTarget = 1,
				boundingRadiusModSource = 1,
				type = "InRange",
				radius = 350
			},
			cast_spell = {},
			slot = player:spellSlot(_E),
			damage = function(arg_449_0, arg_449_1)
				-- function 449
				local slot_449_0 = arg_449_1 or player
				local slot_449_1 = common.GetTotalAP(slot_449_0)
				local slot_449_2 = common.GetBonusAD(slot_449_0)
				local slot_449_3 = slot_449_0:spellSlot(_E).level
				local slot_449_4 = (({
					15,
					20,
					25,
					30,
					35
				})[slot_449_3] or 0) + slot_449_1 * 0.2 + slot_449_2 * 0.1
				local slot_449_5 = common.CAP(arg_449_0, slot_449_4, slot_449_0) + common.CAA(arg_449_0, slot_449_0)

				return slot_15_48.get_damage(slot_449_5, arg_449_0, slot_449_0, 1)
			end
		},
		R = {
			ignore_obj_radius = 4250,
			target_selector = 8,
			prediction = {
				delay = 0,
				range = 900,
				dashRadius = 0,
				boundingRadiusModTarget = 1,
				boundingRadiusModSource = 1,
				type = "InRange",
				radius = 900
			},
			cast_spell = {},
			slot = player:spellSlot(_R),
			damage = function(arg_450_0, arg_450_1)
				-- function 450
				local slot_450_0 = arg_450_1 or player
				local slot_450_1 = common.GetTotalAP(slot_450_0)
				local slot_450_2 = common.GetBonusAD(slot_450_0)
				local slot_450_3 = slot_450_0:spellSlot(_R).level
				local slot_450_4 = (({
					200,
					300,
					400
				})[slot_450_3] or 0) + slot_450_1 * 0.7 + slot_450_2 * 1
				local slot_450_5 = common.CAP(arg_450_0, slot_450_4, slot_450_0)

				return slot_15_48.get_damage(slot_450_5, arg_450_0, slot_450_0, 1)
			end
		}
	}
	slot_15_48.Darius = {
		Q = {
			ignore_obj_radius = 450,
			target_selector = 8,
			prediction = {
				delay = 0,
				range = 460,
				dashRadius = 0,
				boundingRadiusModTarget = 1,
				boundingRadiusModSource = 1,
				type = "InRange",
				radius = 240
			},
			cast_spell = {},
			slot = player:spellSlot(_Q),
			damage = function(arg_451_0, arg_451_1)
				-- function 451
				local slot_451_0 = arg_451_1 or player
				local slot_451_1 = common.GetTotalAP(slot_451_0)
				local slot_451_2 = common.GetTotalAD(slot_451_0)
				local slot_451_3 = slot_451_0:spellSlot(_Q).level
				local slot_451_4 = {
					50,
					80,
					110,
					140,
					170
				}
				local slot_451_5 = {
					1,
					1.1,
					1.2,
					1.3,
					1.4
				}
				local slot_451_6 = (slot_451_4[slot_451_3] or 0) + slot_451_2 * (slot_451_5[slot_451_3] or 0)
				local slot_451_7 = common.CAD(arg_451_0, slot_451_6, slot_451_0)

				return slot_15_48.get_damage(slot_451_7, arg_451_0, slot_451_0, 2)
			end
		},
		W = {
			ignore_obj_radius = 0,
			target_selector = 8,
			prediction = {
				delay = 0,
				range = 165,
				dashRadius = 0,
				boundingRadiusModTarget = 1,
				boundingRadiusModSource = 1,
				type = "InRange",
				radius = 165
			},
			cast_spell = {},
			slot = player:spellSlot(_W),
			damage = function(arg_452_0, arg_452_1)
				-- function 452
				local slot_452_0 = arg_452_1 or player
				local slot_452_1 = common.GetTotalAP(slot_452_0)
				local slot_452_2 = common.GetTotalAD(slot_452_0)
				local slot_452_3 = slot_452_0:spellSlot(_W).level
				local slot_452_4 = ({
					1.4,
					1.45,
					1.5,
					1.55,
					1.6
				})[slot_452_3] or 0
				local slot_452_5 = common.CAA(arg_452_0, slot_452_0) * slot_452_4

				return slot_15_48.get_damage(slot_452_5, arg_452_0, slot_452_0, 2)
			end
		},
		E = {
			ignore_obj_radius = 825,
			target_selector = 8,
			prediction = {
				delay = 0.4,
				range = 515,
				type = "Linear",
				boundingRadiusMod = 0,
				width = 65,
				speed = math.huge,
				collision = {
					wall = true,
					minion = false,
					hero = false
				}
			},
			cast_spell = {},
			slot = player:spellSlot(_E),
			damage = function(arg_453_0, arg_453_1)
				-- function 453
				return 0
			end
		},
		R = {
			ignore_obj_radius = 4250,
			target_selector = 8,
			prediction = {
				delay = 0.3667,
				range = 475,
				dashRadius = 0,
				boundingRadiusModTarget = 1,
				boundingRadiusModSource = 1,
				type = "InRange",
				radius = 475
			},
			cast_spell = {},
			slot = player:spellSlot(_R),
			damage = function(arg_454_0, arg_454_1, arg_454_2)
				-- function 454
				local slot_454_0 = arg_454_1 or player
				local slot_454_1 = common.GetTotalAP(slot_454_0)
				local slot_454_2 = common.GetBonusAD(slot_454_0)
				local slot_454_3 = slot_454_0:spellSlot(_R).level
				local slot_454_4 = (({
					125,
					250,
					375
				})[slot_454_3] or 0) + slot_454_2 * 0.75
				local slot_454_5 = arg_454_2 or 0

				if arg_454_0.buff.dariushemo then
					slot_454_5 = slot_454_5 + arg_454_0.buff.dariushemo.stacks
				end

				if preds.damage then
					for iter_454_0, iter_454_1 in pairs(preds.damage) do
						if iter_454_0 == slot_454_0.ptr and iter_454_1.true_aa and iter_454_1.target and iter_454_1.target.ptr == arg_454_0.ptr then
							slot_454_5 = slot_454_5 + 1

							break
						end
					end
				end

				return slot_454_4 + slot_454_4 * (0.2 * math.min(5, slot_454_5))
			end
		}
	}
	slot_15_48.Ziggs = {
		Q = {
			ignore_obj_radius = 450,
			target_selector = 8,
			prediction = {
				delay = 0.25,
				range = 1400,
				radius2 = 275,
				speed = 1700,
				boundingRadiusMod = 0,
				type = "Circular",
				radius = 40,
				collision = {
					wall = true,
					minion = true,
					hero = true
				}
			},
			prediction2 = {
				delay = 0.25,
				range = 1400,
				speed = 1700,
				boundingRadiusMod = 0,
				type = "Circular",
				radius = 170,
				collision = {
					wall = true,
					minion = true,
					hero = true
				}
			},
			cast_spell = {},
			slot = player:spellSlot(_Q),
			damage = function(arg_455_0, arg_455_1)
				-- function 455
				local slot_455_0 = arg_455_1 or player
				local slot_455_1 = common.GetTotalAP(slot_455_0)
				local slot_455_2 = common.GetTotalAD(slot_455_0)
				local slot_455_3 = slot_455_0:spellSlot(_Q).level
				local slot_455_4 = (({
					85,
					135,
					185,
					235,
					285
				})[slot_455_3] or 0) + slot_455_1 * 0.65
				local slot_455_5 = common.CAP(arg_455_0, slot_455_4, slot_455_0)

				return slot_15_48.get_damage(slot_455_5, arg_455_0, slot_455_0, 1)
			end
		},
		W = {
			ignore_obj_radius = 0,
			target_selector = 8,
			prediction = {
				delay = 0.25,
				range = 1000,
				speed = 2000,
				boundingRadiusMod = 0,
				type = "Circular",
				radius = 2,
				collision = {
					wall = true,
					minion = false,
					hero = false
				}
			},
			cast_spell = {},
			slot = player:spellSlot(_W),
			damage = function(arg_456_0, arg_456_1)
				-- function 456
				local slot_456_0 = arg_456_1 or player
				local slot_456_1 = common.GetTotalAP(slot_456_0)
				local slot_456_2 = common.GetTotalAD(slot_456_0)
				local slot_456_3 = slot_456_0:spellSlot(_W).level
				local slot_456_4 = (({
					70,
					105,
					140,
					175,
					210
				})[slot_456_3] or 0) + slot_456_1 * 0.5
				local slot_456_5 = common.CAP(arg_456_0, slot_456_4, slot_456_0)

				return slot_15_48.get_damage(slot_456_5, arg_456_0, slot_456_0, 1)
			end
		},
		E = {
			ignore_obj_radius = 825,
			target_selector = 8,
			prediction = {
				delay = 0.25,
				range = 900,
				radius2 = 162.5,
				speed = 1500,
				boundingRadiusMod = 0,
				type = "Circular",
				radius = 50,
				collision = {
					wall = true,
					minion = false,
					hero = false
				}
			},
			cast_spell = {},
			slot = player:spellSlot(_E),
			damage = function(arg_457_0, arg_457_1)
				-- function 457
				local slot_457_0 = arg_457_1 or player
				local slot_457_1 = common.GetTotalAP(slot_457_0)
				local slot_457_2 = common.GetTotalAD(slot_457_0)
				local slot_457_3 = slot_457_0:spellSlot(_E).level
				local slot_457_4 = (({
					30,
					70,
					110,
					150,
					190
				})[slot_457_3] or 0) + slot_457_1 * 0.3
				local slot_457_5 = common.CAP(arg_457_0, slot_457_4, slot_457_0)

				return slot_15_48.get_damage(slot_457_5, arg_457_0, slot_457_0, 1)
			end
		},
		R = {
			ignore_obj_radius = 4250,
			target_selector = 8,
			prediction = {
				delay = 0.375,
				range = 5000,
				speed = 2250,
				boundingRadiusMod = 0,
				type = "Circular",
				radius = 100,
				collision = {
					wall = false,
					minion = false,
					hero = false
				}
			},
			cast_spell = {},
			slot = player:spellSlot(_R),
			damage = function(arg_458_0, arg_458_1)
				-- function 458
				local slot_458_0 = arg_458_1 or player
				local slot_458_1 = common.GetTotalAP(slot_458_0)
				local slot_458_2 = common.GetTotalAD(slot_458_0)
				local slot_458_3 = slot_458_0:spellSlot(_R).level
				local slot_458_4 = (({
					200,
					300,
					400
				})[slot_458_3] or 0) + slot_458_1 * 0.733
				local slot_458_5 = common.CAP(arg_458_0, slot_458_4, slot_458_0)

				return slot_15_48.get_damage(slot_458_5, arg_458_0, slot_458_0, 1)
			end
		}
	}
	slot_15_48.Zyra = {
		Q = {
			ignore_obj_radius = 450,
			target_selector = 8,
			prediction = {
				delay = 0.875,
				range = 800,
				radius2 = 120,
				boundingRadiusMod = 0,
				type = "Circular",
				radius = 40,
				speed = math.huge
			},
			cast_spell = {},
			slot = player:spellSlot(_Q),
			damage = function(arg_459_0, arg_459_1)
				-- function 459
				local slot_459_0 = arg_459_1 or player
				local slot_459_1 = common.GetTotalAP(slot_459_0)
				local slot_459_2 = common.GetTotalAD(slot_459_0)
				local slot_459_3 = slot_459_0:spellSlot(_Q).level
				local slot_459_4 = (({
					60,
					100,
					140,
					180,
					220
				})[slot_459_3] or 0) + slot_459_1 * 0.65
				local slot_459_5 = common.CAP(arg_459_0, slot_459_4, slot_459_0)

				return slot_15_48.get_damage(slot_459_5, arg_459_0, slot_459_0, 1)
			end
		},
		W = {
			ignore_obj_radius = 0,
			target_selector = 8,
			prediction = {
				delay = 0,
				range = 850,
				dashRadius = 0,
				boundingRadiusModTarget = 1,
				boundingRadiusModSource = 1,
				type = "InRange",
				radius = 0
			},
			cast_spell = {},
			slot = player:spellSlot(_W),
			damage = function(arg_460_0, arg_460_1)
				-- function 460
				return 0
			end
		},
		E = {
			ignore_obj_radius = 825,
			target_selector = 8,
			prediction = {
				delay = 0.25,
				range = 1040,
				type = "Linear",
				boundingRadiusMod = 0,
				width = 10,
				speed = 1150,
				collision = {
					wall = true,
					minion = false,
					hero = false
				}
			},
			cast_spell = {},
			slot = player:spellSlot(_E),
			damage = function(arg_461_0, arg_461_1)
				-- function 461
				local slot_461_0 = arg_461_1 or player
				local slot_461_1 = common.GetTotalAP(slot_461_0)
				local slot_461_2 = common.GetTotalAD(slot_461_0)
				local slot_461_3 = slot_461_0:spellSlot(_E).level
				local slot_461_4 = (({
					60,
					95,
					130,
					165,
					200
				})[slot_461_3] or 0) + slot_461_1 * 0.6
				local slot_461_5 = common.CAP(arg_461_0, slot_461_4, slot_461_0)

				return slot_15_48.get_damage(slot_461_5, arg_461_0, slot_461_0, 1)
			end
		},
		R = {
			ignore_obj_radius = 4250,
			target_selector = 8,
			prediction = {
				delay = 1,
				range = 700,
				boundingRadiusMod = 0,
				type = "Circular",
				radius = 50,
				speed = math.huge,
				collision = {
					wall = false,
					minion = false,
					hero = false
				}
			},
			cast_spell = {},
			slot = player:spellSlot(_R),
			damage = function(arg_462_0, arg_462_1)
				-- function 462
				local slot_462_0 = arg_462_1 or player
				local slot_462_1 = common.GetTotalAP(slot_462_0)
				local slot_462_2 = common.GetTotalAD(slot_462_0)
				local slot_462_3 = slot_462_0:spellSlot(_R).level
				local slot_462_4 = (({
					180,
					265,
					350
				})[slot_462_3] or 0) + slot_462_1 * 0.7
				local slot_462_5 = common.CAP(arg_462_0, slot_462_4, slot_462_0)

				return slot_15_48.get_damage(slot_462_5, arg_462_0, slot_462_0, 1)
			end
		}
	}
	slot_15_48.Leblanc = {
		Q = {
			ignore_obj_radius = 700,
			target_selector = 8,
			prediction = {
				type = "InRange",
				range = 700
			},
			cast_spell = {},
			slot = player:spellSlot(_Q),
			damage = function(arg_463_0, arg_463_1)
				-- function 463
				local slot_463_0 = arg_463_1 or player
				local slot_463_1 = common.GetTotalAP(slot_463_0)
				local slot_463_2 = common.GetTotalAD(slot_463_0)
				local slot_463_3 = slot_463_0:spellSlot(_Q).level
				local slot_463_4 = (({
					65,
					90,
					115,
					140,
					165
				})[slot_463_3] or 0) + slot_463_1 * 0.4
				local slot_463_5 = common.CAP(arg_463_0, slot_463_4, slot_463_0)

				return slot_15_48.get_damage(slot_463_5, arg_463_0, slot_463_0, 1)
			end
		},
		W = {
			ignore_obj_radius = 0,
			target_selector = 8,
			prediction = {
				delay = 0,
				range = 850,
				speed = 1450,
				boundingRadiusMod = 0,
				type = "circular",
				radius = 200
			},
			cast_spell = {},
			slot = player:spellSlot(_W),
			damage = function(arg_464_0, arg_464_1)
				-- function 464
				local slot_464_0 = arg_464_1 or player
				local slot_464_1 = common.GetTotalAP(slot_464_0)
				local slot_464_2 = common.GetTotalAD(slot_464_0)
				local slot_464_3 = slot_464_0:spellSlot(_W).level
				local slot_464_4 = (({
					75,
					115,
					155,
					195,
					235
				})[slot_464_3] or 0) + slot_464_1 * 0.7
				local slot_464_5 = common.CAP(arg_464_0, slot_464_4, slot_464_0)

				return slot_15_48.get_damage(slot_464_5, arg_464_0, slot_464_0, 1)
			end
		},
		E = {
			ignore_obj_radius = 825,
			target_selector = 8,
			prediction = {
				delay = 0.25,
				range = 950,
				type = "Linear",
				boundingRadiusMod = 1,
				width = 55,
				speed = 1750,
				collision = {
					wall = true,
					minion = true,
					hero = true
				}
			},
			cast_spell = {},
			slot = player:spellSlot(_E),
			damage = function(arg_465_0, arg_465_1)
				-- function 465
				local slot_465_0 = arg_465_1 or player
				local slot_465_1 = common.GetTotalAP(slot_465_0)
				local slot_465_2 = common.GetTotalAD(slot_465_0)
				local slot_465_3 = slot_465_0:spellSlot(_E).level
				local slot_465_4 = (({
					50,
					70,
					90,
					110,
					130
				})[slot_465_3] or 0) + slot_465_1 * 0.4
				local slot_465_5 = common.CAP(arg_465_0, slot_465_4, slot_465_0)

				return slot_15_48.get_damage(slot_465_5, arg_465_0, slot_465_0, 1)
			end
		},
		R = {
			ignore_obj_radius = 4250,
			target_selector = 8,
			prediction = {
				delay = 1,
				range = 700,
				boundingRadiusMod = 0,
				type = "Circular",
				radius = 50,
				speed = math.huge,
				collision = {
					wall = false,
					minion = false,
					hero = false
				}
			},
			cast_spell = {},
			slot = player:spellSlot(_R),
			damage = function(arg_466_0, arg_466_1)
				-- function 466
				local slot_466_0 = arg_466_1 or player
				local slot_466_1 = common.GetTotalAP(slot_466_0)
				local slot_466_2 = common.GetTotalAD(slot_466_0)
				local slot_466_3 = slot_466_0:spellSlot(_R).level
				local slot_466_4 = (({
					70,
					140,
					210
				})[slot_466_3] or 0) + slot_466_1 * 0.4
				local slot_466_5 = common.CAP(arg_466_0, slot_466_4, slot_466_0)

				return slot_15_48.get_damage(slot_466_5, arg_466_0, slot_466_0, 1)
			end
		}
	}
	slot_15_48.Ekko = {
		Q = {
			ignore_obj_radius = 700,
			target_selector = 8,
			prediction = {
				delay = 0.25,
				range = 1100,
				type = "Linear",
				boundingRadiusMod = 1,
				width = 80,
				speed = 1650
			},
			cast_spell = {},
			slot = player:spellSlot(_Q),
			damage = function(arg_467_0, arg_467_1)
				-- function 467
				local slot_467_0 = arg_467_1 or player
				local slot_467_1 = common.GetTotalAP(slot_467_0)
				local slot_467_2 = common.GetTotalAD(slot_467_0)
				local slot_467_3 = slot_467_0:spellSlot(_Q).level
				local slot_467_4 = (({
					70,
					85,
					100,
					115,
					130
				})[slot_467_3] or 0) + slot_467_1 * 0.3
				local slot_467_5 = common.CAP(arg_467_0, slot_467_4, slot_467_0)

				return slot_15_48.get_damage(slot_467_5, arg_467_0, slot_467_0, 1)
			end
		},
		W = {
			ignore_obj_radius = 0,
			target_selector = 8,
			prediction = {
				delay = 2,
				range = 850,
				boundingRadiusMod = 0,
				type = "circular",
				radius = 0,
				speed = math.huge
			},
			cast_spell = {},
			slot = player:spellSlot(_W),
			damage = function(arg_468_0, arg_468_1)
				-- function 468
				return 0
			end
		},
		E = {
			ignore_obj_radius = 825,
			target_selector = 8,
			prediction = {
				delay = 0.25,
				range = 375,
				type = "InRange",
				width = 0,
				speed = math.huge
			},
			cast_spell = {},
			slot = player:spellSlot(_E),
			damage = function(arg_469_0, arg_469_1)
				-- function 469
				local slot_469_0 = arg_469_1 or player
				local slot_469_1 = common.GetTotalAP(slot_469_0)
				local slot_469_2 = common.GetTotalAD(slot_469_0)
				local slot_469_3 = slot_469_0:spellSlot(_E).level
				local slot_469_4 = (({
					50,
					75,
					100,
					125,
					150
				})[slot_469_3] or 0) + slot_469_1 * 0.4
				local slot_469_5 = common.CAP(arg_469_0, slot_469_4, slot_469_0)

				return slot_15_48.get_damage(slot_469_5, arg_469_0, slot_469_0, 1)
			end
		},
		R = {
			ignore_obj_radius = 4250,
			target_selector = 8,
			prediction = {
				delay = 1,
				range = 700,
				boundingRadiusMod = 0,
				type = "Circular",
				radius = 50,
				speed = math.huge,
				collision = {
					wall = false,
					minion = false,
					hero = false
				}
			},
			cast_spell = {},
			slot = player:spellSlot(_R),
			damage = function(arg_470_0, arg_470_1)
				-- function 470
				local slot_470_0 = arg_470_1 or player
				local slot_470_1 = common.GetTotalAP(slot_470_0)
				local slot_470_2 = common.GetTotalAD(slot_470_0)
				local slot_470_3 = slot_470_0:spellSlot(_R).level
				local slot_470_4 = (({
					200,
					350,
					500
				})[slot_470_3] or 0) + slot_470_1 * 1.75
				local slot_470_5 = common.CAP(arg_470_0, slot_470_4, slot_470_0)

				return slot_15_48.get_damage(slot_470_5, arg_470_0, slot_470_0, 1)
			end
		}
	}
	slot_15_48.Aurora = {
		Q = {
			ignore_obj_radius = 700,
			target_selector = 8,
			prediction = {
				delay = 0.25,
				range = 900,
				type = "Linear",
				boundingRadiusMod = 0,
				width = 105,
				speed = 1600,
				collision = {
					wall = true,
					minion = false,
					hero = false
				}
			},
			cast_spell = {},
			slot = player:spellSlot(_Q),
			damage = function(arg_471_0, arg_471_1)
				-- function 471
				local slot_471_0 = arg_471_1 or player
				local slot_471_1 = common.GetTotalAP(slot_471_0)
				local slot_471_2 = common.GetTotalAD(slot_471_0)
				local slot_471_3 = slot_471_0:spellSlot(_Q).level
				local slot_471_4 = (({
					45,
					70,
					95,
					120,
					145
				})[slot_471_3] or 0) + slot_471_1 * 0.4
				local slot_471_5 = common.CAP(arg_471_0, slot_471_4, slot_471_0)

				return slot_15_48.get_damage(slot_471_5, arg_471_0, slot_471_0, 1)
			end
		},
		W = {
			ignore_obj_radius = 0,
			target_selector = 8,
			prediction = {
				dashRadius = 300,
				delay = 0,
				range = 300,
				fixedRange = true,
				boundingRadiusModTarget = 0,
				boundingRadiusModSource = 0,
				type = "InRange",
				radius = 300,
				speed = math.huge
			},
			cast_spell = {},
			slot = player:spellSlot(_W),
			damage = function(arg_472_0, arg_472_1)
				-- function 472
				return 0
			end
		},
		E = {
			ignore_obj_radius = 825,
			target_selector = 8,
			prediction = {
				delay = 0.35,
				range = 825,
				type = "Linear",
				boundingRadiusMod = 0,
				width = 120,
				speed = math.huge
			},
			cast_spell = {},
			slot = player:spellSlot(_E),
			damage = function(arg_473_0, arg_473_1)
				-- function 473
				local slot_473_0 = arg_473_1 or player
				local slot_473_1 = common.GetTotalAP(slot_473_0)
				local slot_473_2 = common.GetTotalAD(slot_473_0)
				local slot_473_3 = slot_473_0:spellSlot(_E).level
				local slot_473_4 = (({
					70,
					110,
					150,
					190,
					230
				})[slot_473_3] or 0) + slot_473_1 * 0.8
				local slot_473_5 = common.CAP(arg_473_0, slot_473_4, slot_473_0)

				return slot_15_48.get_damage(slot_473_5, arg_473_0, slot_473_0, 1)
			end
		},
		R = {
			ignore_obj_radius = 4250,
			target_selector = 8,
			prediction = {
				delay = 0,
				range = 750,
				speed = 700,
				boundingRadiusMod = 0,
				type = "Circular",
				radius = 50,
				collision = {
					wall = false,
					minion = false,
					hero = false
				}
			},
			cast_spell = {},
			slot = player:spellSlot(_R),
			damage = function(arg_474_0, arg_474_1)
				-- function 474
				local slot_474_0 = arg_474_1 or player
				local slot_474_1 = common.GetTotalAP(slot_474_0)
				local slot_474_2 = common.GetTotalAD(slot_474_0)
				local slot_474_3 = slot_474_0:spellSlot(_R).level
				local slot_474_4 = (({
					175,
					275,
					375
				})[slot_474_3] or 0) + slot_474_1 * 0.6
				local slot_474_5 = common.CAP(arg_474_0, slot_474_4, slot_474_0)

				return slot_15_48.get_damage(slot_474_5, arg_474_0, slot_474_0, 1)
			end
		}
	}
	slot_15_48.Soraka = {
		Q = {
			ignore_obj_radius = 700,
			target_selector = 8,
			prediction = {
				delay = 0.75,
				range = 800,
				boundingRadiusMod = 0,
				type = "Circular",
				radius = 65,
				speed = math.huge
			},
			cast_spell = {},
			slot = player:spellSlot(_Q),
			damage = function(arg_475_0, arg_475_1)
				-- function 475
				local slot_475_0 = arg_475_1 or player
				local slot_475_1 = common.GetTotalAP(slot_475_0)
				local slot_475_2 = common.GetTotalAD(slot_475_0)
				local slot_475_3 = slot_475_0:spellSlot(_Q).level
				local slot_475_4 = (({
					85,
					120,
					155,
					190,
					225
				})[slot_475_3] or 0) + slot_475_1 * 0.35
				local slot_475_5 = common.CAP(arg_475_0, slot_475_4, slot_475_0)

				return slot_15_48.get_damage(slot_475_5, arg_475_0, slot_475_0, 1)
			end
		},
		W = {
			ignore_obj_radius = 0,
			target_selector = 8,
			prediction = {
				dashRadius = 0,
				delay = 0.25,
				range = 550,
				fixedRange = true,
				boundingRadiusModTarget = 0,
				boundingRadiusModSource = 0,
				type = "InRange",
				radius = 550,
				speed = math.huge
			},
			cast_spell = {},
			slot = player:spellSlot(_W),
			damage = function(arg_476_0, arg_476_1)
				-- function 476
				return 0
			end
		},
		E = {
			ignore_obj_radius = 825,
			target_selector = 8,
			prediction = {
				delay = 0.25,
				range = 925,
				boundingRadiusMod = 0,
				type = "Circular",
				radius = 65,
				speed = math.huge
			},
			cast_spell = {},
			slot = player:spellSlot(_E),
			damage = function(arg_477_0, arg_477_1)
				-- function 477
				local slot_477_0 = arg_477_1 or player
				local slot_477_1 = common.GetTotalAP(slot_477_0)
				local slot_477_2 = common.GetTotalAD(slot_477_0)
				local slot_477_3 = slot_477_0:spellSlot(_E).level
				local slot_477_4 = (({
					70,
					95,
					120,
					145,
					170
				})[slot_477_3] or 0) + slot_477_1 * 0.4
				local slot_477_5 = common.CAP(arg_477_0, slot_477_4, slot_477_0)

				return slot_15_48.get_damage(slot_477_5, arg_477_0, slot_477_0, 1)
			end
		},
		R = {
			ignore_obj_radius = 4250,
			target_selector = 8,
			prediction = {
				dashRadius = 0,
				delay = 0.25,
				range = 550,
				fixedRange = true,
				boundingRadiusModTarget = 0,
				boundingRadiusModSource = 0,
				type = "InRange",
				radius = 550,
				speed = math.huge
			},
			cast_spell = {},
			slot = player:spellSlot(_R),
			damage = function(arg_478_0, arg_478_1)
				-- function 478
				return 0
			end
		}
	}
	slot_15_48.Elise = {
		Q = {
			ignore_obj_radius = 700,
			target_selector = 8,
			prediction = {
				delay = 0.75,
				range = 575,
				boundingRadiusMod = 0,
				type = "InRange",
				radius = 65,
				speed = math.huge
			},
			cast_spell = {},
			slot = player:spellSlot(_Q),
			damage = function(arg_479_0, arg_479_1)
				-- function 479
				local slot_479_0 = arg_479_1 or player
				local slot_479_1 = common.GetTotalAP(slot_479_0)
				local slot_479_2 = common.GetTotalAD(slot_479_0)
				local slot_479_3 = slot_479_0:spellSlot(_Q).level
				local slot_479_4 = (({
					40,
					75,
					110,
					145,
					180
				})[slot_479_3] or 0) + slot_479_1 * 0
				local slot_479_5 = common.CAP(arg_479_0, slot_479_4, slot_479_0)

				return slot_15_48.get_damage(slot_479_5, arg_479_0, slot_479_0, 1)
			end
		},
		Q1 = {
			ignore_obj_radius = 700,
			target_selector = 8,
			prediction = {
				delay = 0.75,
				range = 475,
				boundingRadiusMod = 0,
				type = "InRange",
				radius = 65,
				speed = math.huge
			},
			cast_spell = {},
			slot = player:spellSlot(_Q),
			damage = function(arg_480_0, arg_480_1)
				-- function 480
				local slot_480_0 = arg_480_1 or player
				local slot_480_1 = common.GetTotalAP(slot_480_0)
				local slot_480_2 = common.GetTotalAD(slot_480_0)
				local slot_480_3 = slot_480_0:spellSlot(_Q).level
				local slot_480_4 = (({
					60,
					90,
					120,
					150,
					180
				})[slot_480_3] or 0) + slot_480_1 * 0
				local slot_480_5 = common.CAP(arg_480_0, slot_480_4, slot_480_0)

				return slot_15_48.get_damage(slot_480_5, arg_480_0, slot_480_0, 1)
			end
		},
		W = {
			ignore_obj_radius = 0,
			target_selector = 8,
			prediction = {
				delay = 0.5,
				range = 950,
				boundingRadiusMod = 0,
				type = "Circular",
				radius = 65,
				speed = math.huge
			},
			cast_spell = {},
			slot = player:spellSlot(_W),
			damage = function(arg_481_0, arg_481_1)
				-- function 481
				local slot_481_0 = arg_481_1 or player
				local slot_481_1 = common.GetTotalAP(slot_481_0)
				local slot_481_2 = common.GetTotalAD(slot_481_0)
				local slot_481_3 = slot_481_0:spellSlot(_W).level
				local slot_481_4 = (({
					60,
					105,
					150,
					195,
					240
				})[slot_481_3] or 0) + slot_481_1 * 0.95
				local slot_481_5 = common.CAP(arg_481_0, slot_481_4, slot_481_0)

				return slot_15_48.get_damage(slot_481_5, arg_481_0, slot_481_0, 1)
			end
		},
		W = {
			ignore_obj_radius = 0,
			target_selector = 8,
			prediction = {
				delay = 0.5,
				range = 950,
				boundingRadiusMod = 0,
				type = "Circular",
				radius = 65,
				speed = math.huge
			},
			cast_spell = {},
			slot = player:spellSlot(_W),
			damage = function(arg_482_0, arg_482_1)
				-- function 482
				return 0
			end
		},
		E = {
			ignore_obj_radius = 825,
			target_selector = 8,
			prediction = {
				delay = 0.25,
				range = 925,
				type = "Linear",
				boundingRadiusMod = 1,
				width = 55,
				speed = 1600
			},
			cast_spell = {},
			slot = player:spellSlot(_E),
			damage = function(arg_483_0, arg_483_1)
				-- function 483
				return 0
			end
		},
		E = {
			ignore_obj_radius = 825,
			target_selector = 8,
			prediction = {
				delay = 0.25,
				range = 925,
				type = "InRange",
				boundingRadiusMod = 1,
				width = 55,
				speed = 1600
			},
			cast_spell = {},
			slot = player:spellSlot(_E),
			damage = function(arg_484_0, arg_484_1)
				-- function 484
				return 0
			end
		},
		R = {
			ignore_obj_radius = 4250,
			target_selector = 8,
			prediction = {
				dashRadius = 0,
				delay = 0.25,
				range = 550,
				fixedRange = true,
				boundingRadiusModTarget = 0,
				boundingRadiusModSource = 0,
				type = "InRange",
				radius = 550,
				speed = math.huge
			},
			cast_spell = {},
			slot = player:spellSlot(_R),
			damage = function(arg_485_0, arg_485_1)
				-- function 485
				return 0
			end
		}
	}
	slot_15_48.Seraphine = {
		Q = {
			ignore_obj_radius = 700,
			target_selector = 8,
			prediction = {
				delay = 0.25,
				range = 900,
				speed = 1300,
				boundingRadiusMod = 0,
				type = "Circular",
				radius = 65
			},
			cast_spell = {},
			slot = player:spellSlot(_Q),
			damage = function(arg_486_0, arg_486_1)
				-- function 486
				local slot_486_0 = arg_486_1 or player
				local slot_486_1 = common.GetTotalAP(slot_486_0)
				local slot_486_2 = common.GetTotalAD(slot_486_0)
				local slot_486_3 = slot_486_0:spellSlot(_Q).level
				local slot_486_4 = (({
					60,
					85,
					110,
					135,
					160
				})[slot_486_3] or 0) + slot_486_1 * 0.5
				local slot_486_5 = common.CAP(arg_486_0, slot_486_4, slot_486_0)

				return slot_15_48.get_damage(slot_486_5, arg_486_0, slot_486_0, 1)
			end
		},
		W = {
			ignore_obj_radius = 0,
			target_selector = 8,
			prediction = {
				dashRadius = 0,
				delay = 0.25,
				range = 800,
				fixedRange = true,
				boundingRadiusModTarget = 0,
				boundingRadiusModSource = 0,
				type = "InRange",
				radius = 800,
				speed = math.huge
			},
			cast_spell = {},
			slot = player:spellSlot(_W),
			damage = function(arg_487_0, arg_487_1)
				-- function 487
				return 0
			end
		},
		E = {
			ignore_obj_radius = 825,
			target_selector = 8,
			prediction = {
				delay = 0.25,
				range = 1250,
				type = "Linear",
				boundingRadiusMod = 1,
				width = 70,
				speed = 1200
			},
			cast_spell = {},
			slot = player:spellSlot(_E),
			damage = function(arg_488_0, arg_488_1)
				-- function 488
				local slot_488_0 = arg_488_1 or player
				local slot_488_1 = common.GetTotalAP(slot_488_0)
				local slot_488_2 = common.GetTotalAD(slot_488_0)
				local slot_488_3 = slot_488_0:spellSlot(_E).level
				local slot_488_4 = (({
					70,
					100,
					130,
					160,
					190
				})[slot_488_3] or 0) + slot_488_1 * 0.5
				local slot_488_5 = common.CAP(arg_488_0, slot_488_4, slot_488_0)

				return slot_15_48.get_damage(slot_488_5, arg_488_0, slot_488_0, 1)
			end
		},
		R = {
			ignore_obj_radius = 4250,
			target_selector = 8,
			prediction = {
				delay = 0.5,
				range = 1150,
				type = "Linear",
				boundingRadiusMod = 1,
				width = 80,
				speed = 1200
			},
			cast_spell = {},
			slot = player:spellSlot(_R),
			damage = function(arg_489_0, arg_489_1)
				-- function 489
				local slot_489_0 = arg_489_1 or player
				local slot_489_1 = common.GetTotalAP(slot_489_0)
				local slot_489_2 = common.GetTotalAD(slot_489_0)
				local slot_489_3 = slot_489_0:spellSlot(_R).level
				local slot_489_4 = (({
					150,
					200,
					250
				})[slot_489_3] or 0) + slot_489_1 * 0.4
				local slot_489_5 = common.CAP(arg_489_0, slot_489_4, slot_489_0)

				return slot_15_48.get_damage(slot_489_5, arg_489_0, slot_489_0, 1)
			end
		}
	}
	slot_15_48.Talon = {
		Q = {
			ignore_obj_radius = 700,
			target_selector = 8,
			prediction = {
				delay = 0.25,
				range = 575,
				speed = 1400,
				boundingRadiusMod = 0,
				type = "InRange",
				radius = 65
			},
			cast_spell = {},
			slot = player:spellSlot(_Q),
			damage = function(arg_490_0, arg_490_1)
				-- function 490
				local slot_490_0 = arg_490_1 or player
				local slot_490_1 = common.GetTotalAP(slot_490_0)
				local slot_490_2 = common.GetBonusAD(slot_490_0)
				local slot_490_3 = slot_490_0:spellSlot(_Q).level
				local slot_490_4 = (({
					65,
					85,
					105,
					125,
					145
				})[slot_490_3] or 0) + slot_490_2 * 1
				local slot_490_5 = common.CAD(arg_490_0, slot_490_4, slot_490_0)

				if arg_490_0.pos:dist(player.pos) < 225 then
					slot_490_5 = slot_490_5 * 1.5
				end

				return slot_15_48.get_damage(slot_490_5, arg_490_0, slot_490_0, 2)
			end
		},
		W = {
			ignore_obj_radius = 0,
			target_selector = 8,
			prediction = {
				delay = 0.25,
				range = 850,
				type = "Linear",
				boundingRadiusMod = 1,
				width = 75,
				speed = 2500
			},
			cast_spell = {},
			slot = player:spellSlot(_W),
			damage = function(arg_491_0, arg_491_1, arg_491_2)
				-- function 491
				local slot_491_0 = arg_491_1 or player
				local slot_491_1 = common.GetTotalAP(slot_491_0)
				local slot_491_2 = common.GetBonusAD(slot_491_0)
				local slot_491_3 = slot_491_0:spellSlot(_W).level
				local slot_491_4 = {
					50,
					60,
					70,
					80,
					90
				}
				local slot_491_5 = {
					60,
					90,
					120,
					150,
					180
				}
				local slot_491_6 = slot_491_4[slot_491_3] or 0
				local slot_491_7 = slot_491_5[slot_491_3] or 0
				local slot_491_8 = slot_491_6 + slot_491_2 * 0.4
				local slot_491_9 = slot_491_7 + slot_491_2 * 0.9
				local slot_491_10 = common.CAD(arg_491_0, slot_491_8 + slot_491_9, slot_491_0)

				return slot_15_48.get_damage(slot_491_10, arg_491_0, slot_491_0, 2)
			end
		},
		E = {
			ignore_obj_radius = 825,
			target_selector = 8,
			prediction = {
				delay = 0.25,
				range = 1250,
				type = "Linear",
				boundingRadiusMod = 1,
				width = 70,
				speed = 1200
			},
			cast_spell = {},
			slot = player:spellSlot(_E),
			damage = function(arg_492_0, arg_492_1)
				-- function 492
				return 0
			end
		},
		R = {
			ignore_obj_radius = 4250,
			target_selector = 8,
			prediction = {
				delay = 0,
				range = 550,
				type = "Linear",
				boundingRadiusMod = 1,
				width = 80,
				speed = 2400
			},
			cast_spell = {},
			slot = player:spellSlot(_R),
			damage = function(arg_493_0, arg_493_1)
				-- function 493
				local slot_493_0 = arg_493_1 or player
				local slot_493_1 = common.GetTotalAP(slot_493_0)
				local slot_493_2 = common.GetBonusAD(slot_493_0)
				local slot_493_3 = slot_493_0:spellSlot(_R).level
				local slot_493_4 = (({
					90,
					135,
					180
				})[slot_493_3] or 0) + slot_493_2 * 1
				local slot_493_5 = common.CAD(arg_493_0, slot_493_4, slot_493_0)

				return slot_15_48.get_damage(slot_493_5, arg_493_0, slot_493_0, 2)
			end
		}
	}
	slot_15_48.Hecarim = {
		Q = {
			ignore_obj_radius = 375,
			target_selector = 8,
			prediction = {
				delay = 0,
				range = 350,
				boundingRadiusMod = 1,
				type = "InRange",
				radius = 350
			},
			cast_spell = {},
			slot = player:spellSlot(_Q),
			damage = function(arg_494_0, arg_494_1)
				-- function 494
				local slot_494_0 = arg_494_1 or player
				local slot_494_1 = common.GetTotalAP(slot_494_0)
				local slot_494_2 = common.GetBonusAD(slot_494_0)
				local slot_494_3 = slot_494_0:spellSlot(_Q).level
				local slot_494_4 = (({
					60,
					85,
					110,
					135,
					160
				})[slot_494_3] or 0) + slot_494_2 * 0.9
				local slot_494_5 = common.CAD(arg_494_0, slot_494_4, slot_494_0)

				return slot_15_48.get_damage(slot_494_5, arg_494_0, slot_494_0, 2)
			end
		},
		W = {
			ignore_obj_radius = 0,
			target_selector = 8,
			prediction = {
				delay = 0,
				range = 525,
				boundingRadiusMod = 1,
				type = "InRange",
				radius = 525
			},
			cast_spell = {},
			slot = player:spellSlot(_W),
			damage = function(arg_495_0, arg_495_1, arg_495_2)
				-- function 495
				local slot_495_0 = arg_495_1 or player
				local slot_495_1 = common.GetTotalAP(slot_495_0)
				local slot_495_2 = common.GetBonusAD(slot_495_0)
				local slot_495_3 = slot_495_0:spellSlot(_W).level
				local slot_495_4 = (({
					20,
					30,
					40,
					50,
					60
				})[slot_495_3] or 0) + slot_495_1 * 0.2
				local slot_495_5 = common.CAP(arg_495_0, slot_495_4, slot_495_0)

				return slot_15_48.get_damage(slot_495_5, arg_495_0, slot_495_0, 1)
			end
		},
		E = {
			ignore_obj_radius = 825,
			target_selector = 8,
			prediction = {
				delay = 0.25,
				range = 1250,
				type = "Linear",
				boundingRadiusMod = 1,
				width = 70,
				speed = 1200
			},
			cast_spell = {},
			slot = player:spellSlot(_E),
			damage = function(arg_496_0, arg_496_1)
				-- function 496
				local slot_496_0 = arg_496_1 or player
				local slot_496_1 = common.GetTotalAP(slot_496_0)
				local slot_496_2 = common.GetBonusAD(slot_496_0)
				local slot_496_3 = slot_496_0:spellSlot(_E).level
				local slot_496_4 = (({
					30,
					45,
					60,
					75,
					90
				})[slot_496_3] or 0) + slot_496_2 * 0.5
				local slot_496_5 = common.CAP(arg_496_0, slot_496_4, slot_496_0)

				return slot_15_48.get_damage(slot_496_5, arg_496_0, slot_496_0, 2)
			end
		},
		R = {
			ignore_obj_radius = 4250,
			target_selector = 8,
			prediction = {
				delay = 0,
				range = 1000,
				type = "Linear",
				boundingRadiusMod = 1,
				width = 80,
				speed = 1100,
				collision = {
					wall = true,
					minion = false,
					hero = false
				}
			},
			prediction2 = {
				delay = 0,
				range = 1000,
				type = "Linear",
				ig = true,
				boundingRadiusMod = 1,
				width = 80,
				speed = 1100,
				collision = {
					wall = true,
					minion = false,
					hero = false
				}
			},
			cast_spell = {},
			slot = player:spellSlot(_R),
			damage = function(arg_497_0, arg_497_1)
				-- function 497
				local slot_497_0 = arg_497_1 or player
				local slot_497_1 = common.GetTotalAP(slot_497_0)
				local slot_497_2 = common.GetBonusAD(slot_497_0)
				local slot_497_3 = slot_497_0:spellSlot(_R).level
				local slot_497_4 = (({
					150,
					250,
					350
				})[slot_497_3] or 0) + slot_497_1 * 1
				local slot_497_5 = common.CAP(arg_497_0, slot_497_4, slot_497_0)

				return slot_15_48.get_damage(slot_497_5, arg_497_0, slot_497_0, 1)
			end
		}
	}
	slot_15_48.Annie = {
		Q = {
			ignore_obj_radius = 375,
			target_selector = 8,
			prediction = {
				delay = 0,
				range = 625,
				boundingRadiusMod = 1,
				type = "InRange",
				radius = 625
			},
			cast_spell = {},
			slot = player:spellSlot(_Q),
			damage = function(arg_498_0, arg_498_1)
				-- function 498
				local slot_498_0 = arg_498_1 or player
				local slot_498_1 = common.GetTotalAP(slot_498_0)
				local slot_498_2 = common.GetBonusAD(slot_498_0)
				local slot_498_3 = slot_498_0:spellSlot(_Q).level
				local slot_498_4 = (({
					70,
					115,
					160,
					205,
					250
				})[slot_498_3] or 0) + slot_498_1 * 0.85
				local slot_498_5 = common.CAP(arg_498_0, slot_498_4, slot_498_0)

				return slot_15_48.get_damage(slot_498_5, arg_498_0, slot_498_0, 1)
			end
		},
		W = {
			ignore_obj_radius = 0,
			target_selector = 8,
			prediction = {
				delay = 0.25,
				range = 600,
				type = "Linear",
				boundingRadiusMod = 1,
				width = 40,
				speed = math.huge
			},
			cast_spell = {},
			slot = player:spellSlot(_W),
			damage = function(arg_499_0, arg_499_1, arg_499_2)
				-- function 499
				local slot_499_0 = arg_499_1 or player
				local slot_499_1 = common.GetTotalAP(slot_499_0)
				local slot_499_2 = common.GetBonusAD(slot_499_0)
				local slot_499_3 = slot_499_0:spellSlot(_W).level
				local slot_499_4 = (({
					70,
					115,
					160,
					205,
					250
				})[slot_499_3] or 0) + slot_499_1 * 0.85
				local slot_499_5 = common.CAP(arg_499_0, slot_499_4, slot_499_0)

				return slot_15_48.get_damage(slot_499_5, arg_499_0, slot_499_0, 1)
			end
		},
		E = {
			ignore_obj_radius = 825,
			target_selector = 8,
			prediction = {
				delay = 0,
				range = 0,
				boundingRadiusMod = 0,
				type = "InRange",
				radius = 0
			},
			cast_spell = {},
			slot = player:spellSlot(_E),
			damage = function(arg_500_0, arg_500_1)
				-- function 500
				local slot_500_0 = arg_500_1 or player
				local slot_500_1 = common.GetTotalAP(slot_500_0)
				local slot_500_2 = common.GetBonusAD(slot_500_0)
				local slot_500_3 = slot_500_0:spellSlot(_E).level
				local slot_500_4 = (({
					25,
					35,
					45,
					55,
					65
				})[slot_500_3] or 0) + slot_500_1 * 0.4
				local slot_500_5 = common.CAP(arg_500_0, slot_500_4, slot_500_0)

				return slot_15_48.get_damage(slot_500_5, arg_500_0, slot_500_0, 1)
			end
		},
		R = {
			ignore_obj_radius = 4250,
			target_selector = 8,
			prediction = {
				delay = 0.25,
				range = 600,
				boundingRadiusMod = 1,
				type = "Circular",
				radius = 50,
				speed = math.huge
			},
			cast_spell = {},
			slot = player:spellSlot(_R),
			damage = function(arg_501_0, arg_501_1)
				-- function 501
				local slot_501_0 = arg_501_1 or player
				local slot_501_1 = common.GetTotalAP(slot_501_0)
				local slot_501_2 = common.GetBonusAD(slot_501_0)
				local slot_501_3 = slot_501_0:spellSlot(_R).level
				local slot_501_4 = (({
					150,
					275,
					400
				})[slot_501_3] or 0) + slot_501_1 * 0.75
				local slot_501_5 = common.CAP(arg_501_0, slot_501_4, slot_501_0)

				return slot_15_48.get_damage(slot_501_5, arg_501_0, slot_501_0, 1)
			end
		}
	}
	slot_15_48.RekSai = {
		Q = {
			ignore_obj_radius = 375,
			target_selector = 8,
			prediction = {
				delay = 0,
				range = 625,
				boundingRadiusMod = 1,
				type = "InRange",
				radius = 625
			},
			cast_spell = {},
			slot = player:spellSlot(_Q),
			damage = function(arg_502_0, arg_502_1)
				-- function 502
				local slot_502_0 = arg_502_1 or player
				local slot_502_1 = slot_502_0:spellSlot(_Q).level
				local slot_502_2 = ({
					0.3,
					0.35,
					0.4,
					0.45,
					0.5
				})[slot_502_1] or 0
				local slot_502_3 = common.CAA(arg_502_0, slot_502_0) * (1 + slot_502_2)

				return slot_15_48.get_damage(slot_502_3, arg_502_0, slot_502_0, 1)
			end
		},
		Q1 = {
			ignore_obj_radius = 375,
			target_selector = 8,
			prediction = {
				delay = 0.1,
				range = 1500,
				type = "Linear",
				boundingRadiusMod = 1,
				width = 65,
				speed = 1950,
				collision = {
					wall = true,
					minion = true,
					hero = true
				}
			},
			cast_spell = {},
			slot = player:spellSlot(_Q),
			damage = function(arg_503_0, arg_503_1)
				-- function 503
				local slot_503_0 = arg_503_1 or player
				local slot_503_1 = common.GetTotalAP(slot_503_0)
				local slot_503_2 = common.GetBonusAD(slot_503_0)
				local slot_503_3 = slot_503_0:spellSlot(_Q).level
				local slot_503_4 = (({
					50,
					80,
					110,
					140,
					170
				})[slot_503_3] or 0) + slot_503_2 * 0.25 + slot_503_1 * 0.7
				local slot_503_5 = common.CAP(arg_503_0, slot_503_4, slot_503_0)

				return slot_15_48.get_damage(slot_503_5, arg_503_0, slot_503_0, 1)
			end
		},
		W = {
			ignore_obj_radius = 0,
			target_selector = 8,
			prediction = {
				delay = 0,
				range = 225,
				boundingRadiusMod = 1,
				type = "InRange",
				radius = 225
			},
			cast_spell = {},
			slot = player:spellSlot(_W),
			damage = function(arg_504_0, arg_504_1, arg_504_2)
				-- function 504
				local slot_504_0 = arg_504_1 or player
				local slot_504_1 = common.GetTotalAP(slot_504_0)
				local slot_504_2 = common.GetBonusAD(slot_504_0)
				local slot_504_3 = slot_504_0:spellSlot(_W).level
				local slot_504_4 = (({
					30,
					55,
					80,
					105,
					130
				})[slot_504_3] or 0) + slot_504_1 * 0.8
				local slot_504_5 = common.CAP(arg_504_0, slot_504_4, slot_504_0)

				return slot_15_48.get_damage(slot_504_5, arg_504_0, slot_504_0, 1)
			end
		},
		E = {
			ignore_obj_radius = 825,
			target_selector = 8,
			prediction = {
				delay = 0,
				range = 225,
				boundingRadiusMod = 0,
				type = "InRange",
				radius = 255
			},
			cast_spell = {},
			slot = player:spellSlot(_E),
			damage = function(arg_505_0, arg_505_1, arg_505_2)
				-- function 505
				local slot_505_0 = arg_505_1 or player
				local slot_505_1 = common.GetTotalAP(slot_505_0)
				local slot_505_2 = common.GetTotalAD(slot_505_0)
				local slot_505_3 = slot_505_0:spellSlot(_E).level
				local slot_505_4 = (({
					0.08,
					0.095,
					0.11,
					0.125,
					0.14
				})[slot_505_3] or 0) * arg_505_0.maxHealth

				if slot_505_0.mana >= slot_505_0.maxMana or arg_505_2 then
					slot_505_4 = slot_505_4 + slot_505_2 * 1
				else
					slot_505_4 = slot_505_2
				end

				local slot_505_5 = common.CAD(arg_505_0, slot_505_4, slot_505_0)

				return slot_15_48.get_damage(slot_505_5, arg_505_0, slot_505_0, 2)
			end
		},
		R = {
			ignore_obj_radius = 4250,
			target_selector = 8,
			prediction = {
				delay = 0.25,
				range = 1500,
				speed = 1400,
				boundingRadiusMod = 0,
				type = "InRange",
				radius = 0
			},
			cast_spell = {},
			slot = player:spellSlot(_R),
			damage = function(arg_506_0, arg_506_1)
				-- function 506
				local slot_506_0 = arg_506_1 or player
				local slot_506_1 = common.GetTotalAP(slot_506_0)
				local slot_506_2 = common.GetBonusAD(slot_506_0)
				local slot_506_3 = slot_506_0:spellSlot(_R).level
				local slot_506_4 = {
					150,
					300,
					450
				}
				local slot_506_5 = {
					0.25,
					0.3,
					0.35
				}
				local slot_506_6 = slot_506_4[slot_506_3] or 0
				local slot_506_7 = slot_506_5[slot_506_3] or 0
				local slot_506_8 = slot_506_6 + slot_506_2 * 1 + (arg_506_0.maxHealth - arg_506_0.health) * slot_506_7
				local slot_506_9 = common.CAD(arg_506_0, slot_506_8, slot_506_0)

				return slot_15_48.get_damage(slot_506_9, arg_506_0, slot_506_0, 2)
			end
		}
	}
	slot_15_48.Ambessa = {
		Q = {
			ignore_obj_radius = 380,
			target_selector = 8,
			prediction = {
				delay = 0.25,
				range = 380,
				boundingRadiusMod = 0,
				type = "Circular",
				radius = 380,
				speed = math.huge
			},
			cast_spell = {},
			slot = player:spellSlot(_Q),
			damage = function(arg_507_0, arg_507_1)
				-- function 507
				local slot_507_0 = arg_507_1 or player
				local slot_507_1 = common.GetTotalAP(slot_507_0)
				local slot_507_2 = common.GetBonusAD(slot_507_0)
				local slot_507_3 = slot_507_0:spellSlot(_Q).level
				local slot_507_4 = {
					40,
					60,
					80,
					100,
					120
				}
				local slot_507_5 = {
					0.02,
					0.03,
					0.04,
					0.05,
					0.06
				}
				local slot_507_6 = slot_507_4[slot_507_3] or 0
				local slot_507_7 = (slot_507_5[slot_507_3] or 0) + slot_507_2 * 0.03 / 100
				local slot_507_8 = slot_507_6 + slot_507_2 * 0.6 + arg_507_0.maxHealth * slot_507_7
				local slot_507_9 = common.CAD(arg_507_0, slot_507_8, slot_507_0)

				return slot_15_48.get_damage(slot_507_9, arg_507_0, slot_507_0, 1)
			end
		},
		Q1 = {
			ignore_obj_radius = 375,
			target_selector = 8,
			prediction = {
				delay = 0.25,
				range = 650,
				type = "Linear",
				boundingRadiusMod = 0,
				width = 50,
				speed = math.huge,
				collision = {
					wall = true,
					minion = false,
					hero = false
				}
			},
			cast_spell = {},
			slot = player:spellSlot(_Q),
			damage = function(arg_508_0, arg_508_1)
				-- function 508
				local slot_508_0 = arg_508_1 or player
				local slot_508_1 = common.GetTotalAP(slot_508_0)
				local slot_508_2 = common.GetBonusAD(slot_508_0)
				local slot_508_3 = slot_508_0:spellSlot(_Q).level
				local slot_508_4 = {
					50,
					75,
					100,
					125,
					150
				}
				local slot_508_5 = {
					0.02,
					0.03,
					0.04,
					0.05,
					0.06
				}
				local slot_508_6 = slot_508_4[slot_508_3] or 0
				local slot_508_7 = (slot_508_5[slot_508_3] or 0) + slot_508_2 * 0.04 / 100
				local slot_508_8 = slot_508_6 + slot_508_2 * 0.6 + arg_508_0.maxHealth * slot_508_7
				local slot_508_9 = common.CAD(arg_508_0, slot_508_8, slot_508_0)

				return slot_15_48.get_damage(slot_508_9, arg_508_0, slot_508_0, 2)
			end
		},
		W = {
			ignore_obj_radius = 0,
			target_selector = 8,
			prediction = {
				delay = 0,
				range = 225,
				boundingRadiusMod = 1,
				type = "InRange",
				radius = 225
			},
			cast_spell = {},
			slot = player:spellSlot(_W),
			damage = function(arg_509_0, arg_509_1, arg_509_2)
				-- function 509
				local slot_509_0 = arg_509_1 or player
				local slot_509_1 = common.GetTotalAP(slot_509_0)
				local slot_509_2 = common.GetBonusAD(slot_509_0)
				local slot_509_3 = slot_509_0:spellSlot(_W).level
				local slot_509_4 = (({
					50,
					75,
					100,
					125,
					150
				})[slot_509_3] or 0) + slot_509_2 * 0.5
				local slot_509_5 = common.CAD(arg_509_0, slot_509_4, slot_509_0)

				return slot_15_48.get_damage(slot_509_5, arg_509_0, slot_509_0, 2)
			end
		},
		E = {
			ignore_obj_radius = 825,
			target_selector = 8,
			prediction = {
				delay = 0.25,
				range = 350,
				boundingRadiusMod = 0,
				type = "Circular",
				radius = 350,
				speed = math.huge
			},
			prediction2 = {
				delay = 0.25,
				range = 650,
				speed = 1105,
				boundingRadiusMod = 0,
				type = "Circular",
				radius = 300
			},
			cast_spell = {},
			slot = player:spellSlot(_E),
			damage = function(arg_510_0, arg_510_1, arg_510_2)
				-- function 510
				local slot_510_0 = arg_510_1 or player
				local slot_510_1 = common.GetTotalAP(slot_510_0)
				local slot_510_2 = common.GetBonusAD(slot_510_0)
				local slot_510_3 = slot_510_0:spellSlot(_E).level
				local slot_510_4 = {
					40,
					65,
					90,
					115,
					140
				}
				local slot_510_5 = {
					0.4,
					0.45,
					0.5,
					0.55,
					0.6
				}
				local slot_510_6 = (slot_510_4[slot_510_3] or 0) + slot_510_2 * (slot_510_5[slot_510_3] or 0)
				local slot_510_7 = common.CAD(arg_510_0, slot_510_6, slot_510_0)

				return slot_15_48.get_damage(slot_510_7, arg_510_0, slot_510_0, 2)
			end
		},
		R = {
			ignore_obj_radius = 4250,
			target_selector = 8,
			prediction = {
				delay = 0.55,
				range = 1200,
				type = "Linear",
				boundingRadiusMod = 1,
				width = 80,
				speed = math.huge,
				collision = {
					wall = true,
					minion = false,
					hero = false
				}
			},
			cast_spell = {},
			slot = player:spellSlot(_R),
			damage = function(arg_511_0, arg_511_1)
				-- function 511
				local slot_511_0 = arg_511_1 or player
				local slot_511_1 = common.GetTotalAP(slot_511_0)
				local slot_511_2 = common.GetBonusAD(slot_511_0)
				local slot_511_3 = slot_511_0:spellSlot(_R).level
				local slot_511_4 = (({
					150,
					250,
					350
				})[slot_511_3] or 0) + slot_511_2 * 0.8
				local slot_511_5 = common.CAD(arg_511_0, slot_511_4, slot_511_0)

				return slot_15_48.get_damage(slot_511_5, arg_511_0, slot_511_0, 2)
			end
		}
	}
	slot_15_48.Anivia = {
		Q = {
			ignore_obj_radius = 380,
			target_selector = 8,
			prediction = {
				delay = 0.25,
				range = 1100,
				type = "Linear",
				boundingRadiusMod = 1,
				width = 110,
				speed = 950,
				collision = {
					wall = true,
					minion = false,
					hero = false
				}
			},
			cast_spell = {},
			slot = player:spellSlot(_Q),
			damage = function(arg_512_0, arg_512_1)
				-- function 512
				local slot_512_0 = arg_512_1 or player
				local slot_512_1 = common.GetTotalAP(slot_512_0)
				local slot_512_2 = common.GetBonusAD(slot_512_0)
				local slot_512_3 = slot_512_0:spellSlot(_Q).level
				local slot_512_4 = {
					50,
					70,
					90,
					110,
					130
				}
				local slot_512_5 = {
					60,
					95,
					130,
					165,
					200
				}
				local slot_512_6 = slot_512_4[slot_512_3] or 0
				local slot_512_7 = slot_512_5[slot_512_3] or 0
				local slot_512_8 = slot_512_6 + slot_512_1 * 0.25
				local slot_512_9 = slot_512_7 + slot_512_1 * 0.45
				local slot_512_10 = common.CAP(arg_512_0, slot_512_8, slot_512_0)
				local slot_512_11 = common.CAP(arg_512_0, slot_512_9, slot_512_0)

				return slot_15_48.get_damage(slot_512_10 + slot_512_11, arg_512_0, slot_512_0, 1)
			end
		},
		W = {
			ignore_obj_radius = 0,
			target_selector = 8,
			prediction = {
				delay = 0.25,
				range = 1000,
				type = "Linear",
				boundingRadiusMod = 0,
				width = 1,
				speed = math.huge,
				collision = {
					wall = false,
					minion = false,
					hero = false
				}
			},
			cast_spell = {},
			slot = player:spellSlot(_W),
			damage = function(arg_513_0, arg_513_1)
				-- function 513
				return 0
			end
		},
		E = {
			ignore_obj_radius = 825,
			target_selector = 8,
			prediction = {
				delay = 0.25,
				range = 600,
				speed = 1600,
				boundingRadiusMod = 0,
				type = "InRange",
				radius = 600
			},
			cast_spell = {},
			slot = player:spellSlot(_E),
			damage = function(arg_514_0, arg_514_1, arg_514_2)
				-- function 514
				local slot_514_0 = arg_514_1 or player
				local slot_514_1 = common.GetTotalAP(slot_514_0)
				local slot_514_2 = common.GetBonusAD(slot_514_0)
				local slot_514_3 = slot_514_0:spellSlot(_E).level
				local slot_514_4 = (({
					50,
					75,
					100,
					125,
					150
				})[slot_514_3] or 0) + slot_514_1 * 0.55
				local slot_514_5 = common.CAP(arg_514_0, slot_514_4, slot_514_0) * 2

				return slot_15_48.get_damage(slot_514_5, arg_514_0, slot_514_0, 1)
			end
		},
		R = {
			ignore_obj_radius = 4250,
			target_selector = 8,
			prediction = {
				delay = 0,
				range = 750,
				boundingRadiusMod = 1,
				type = "Circular",
				radius = 50,
				speed = math.huge
			},
			cast_spell = {},
			slot = player:spellSlot(_R),
			damage = function(arg_515_0, arg_515_1)
				-- function 515
				local slot_515_0 = arg_515_1 or player
				local slot_515_1 = common.GetTotalAP(slot_515_0)
				local slot_515_2 = common.GetBonusAD(slot_515_0)
				local slot_515_3 = slot_515_0:spellSlot(_R).level
				local slot_515_4 = (({
					30,
					45,
					60
				})[slot_515_3] or 0) + slot_515_1 * 0.125
				local slot_515_5 = common.CAP(arg_515_0, slot_515_4, slot_515_0) * 3

				return slot_15_48.get_damage(slot_515_5, arg_515_0, slot_515_0, 1)
			end
		}
	}
	slot_15_48.Tryndamere = {
		Q = {
			ignore_obj_radius = 380,
			target_selector = 8,
			prediction = {
				delay = 0,
				range = 0,
				boundingRadiusMod = 1,
				type = "InRange",
				radius = 0
			},
			cast_spell = {},
			slot = player:spellSlot(_Q),
			damage = function(arg_516_0, arg_516_1)
				-- function 516
				return 0
			end
		},
		W = {
			ignore_obj_radius = 0,
			target_selector = 8,
			prediction = {
				delay = 0,
				range = 850,
				boundingRadiusMod = 1,
				type = "InRange",
				radius = 850
			},
			cast_spell = {},
			slot = player:spellSlot(_W),
			damage = function(arg_517_0, arg_517_1)
				-- function 517
				return 0
			end
		},
		E = {
			ignore_obj_radius = 825,
			target_selector = 8,
			prediction = {
				delay = 0,
				range = 650,
				type = "Linear",
				boundingRadiusMod = 1,
				width = 110,
				speed = 950,
				collision = {
					wall = true,
					minion = false,
					hero = false
				}
			},
			cast_spell = {},
			slot = player:spellSlot(_E),
			damage = function(arg_518_0, arg_518_1, arg_518_2)
				-- function 518
				local slot_518_0 = arg_518_1 or player
				local slot_518_1 = common.GetTotalAP(slot_518_0)
				local slot_518_2 = common.GetBonusAD(slot_518_0)
				local slot_518_3 = slot_518_0:spellSlot(_E).level
				local slot_518_4 = (({
					75,
					105,
					135,
					165,
					195
				})[slot_518_3] or 0) + slot_518_1 * 0.8 + slot_518_2 * 1.3
				local slot_518_5 = common.CAD(arg_518_0, slot_518_4, slot_518_0)

				return slot_15_48.get_damage(slot_518_5, arg_518_0, slot_518_0, 2)
			end
		},
		R = {
			ignore_obj_radius = 4250,
			target_selector = 8,
			prediction = {
				delay = 0,
				range = 225,
				boundingRadiusMod = 1,
				type = "InRange",
				radius = 225
			},
			cast_spell = {},
			slot = player:spellSlot(_R),
			damage = function(arg_519_0, arg_519_1)
				-- function 519
				return 0
			end
		}
	}
	slot_15_48.Mel = {
		P = {
			damage = function(arg_520_0, arg_520_1)
				-- function 520
				local slot_520_0 = arg_520_1 or player
				local slot_520_1 = common.getBuffStacks(arg_520_0, "melpassiveoverwhelm")
				local slot_520_2 = 65 + common.GetTotalAP(slot_520_0) * 0.25 + (3 + common.GetTotalAP(slot_520_0) * 0.0075) * slot_520_1

				return common.CAP(arg_520_0, slot_520_2, slot_520_0)
			end
		},
		Q = {
			ignore_obj_radius = 380,
			target_selector = 8,
			prediction = {
				delay = 0.25,
				range = 1065,
				speed = 5000,
				boundingRadiusMod = 0,
				type = "Circular",
				radius = 80
			},
			cast_spell = {},
			slot = player:spellSlot(_Q),
			damage = function(arg_521_0, arg_521_1)
				-- function 521
				local slot_521_0 = arg_521_1 or player
				local slot_521_1 = common.GetTotalAP(slot_521_0)
				local slot_521_2 = common.GetBonusAD(slot_521_0)
				local slot_521_3 = slot_521_0:spellSlot(_E).level
				local slot_521_4 = {
					13,
					16,
					19,
					22,
					25
				}
				local slot_521_5 = {
					6,
					7,
					8,
					9,
					10
				}
				local slot_521_6 = slot_521_4[slot_521_3] or 0
				local slot_521_7 = slot_521_5[slot_521_3] or 0
				local slot_521_8 = slot_521_6 + slot_521_1 * 0.085
				local slot_521_9 = common.CAP(arg_521_0, slot_521_8, slot_521_0) * slot_521_7

				if slot_521_9 + slot_15_48.Mel.P.damage(arg_521_0, slot_521_0) > arg_521_0.health then
					slot_521_9 = 99999
				end

				return slot_15_48.get_damage(slot_521_9, arg_521_0, slot_521_0, 1)
			end
		},
		W = {
			ignore_obj_radius = 0,
			target_selector = 8,
			prediction = {
				delay = 0,
				range = 850,
				boundingRadiusMod = 1,
				type = "InRange",
				radius = 850
			},
			cast_spell = {},
			slot = player:spellSlot(_W),
			damage = function(arg_522_0, arg_522_1)
				-- function 522
				return 0
			end
		},
		E = {
			ignore_obj_radius = 825,
			target_selector = 8,
			prediction = {
				delay = 0,
				range = 1200,
				type = "Linear",
				boundingRadiusMod = 1,
				width = 100,
				speed = 950,
				collision = {
					wall = true,
					minion = false,
					hero = false
				}
			},
			cast_spell = {},
			slot = player:spellSlot(_E),
			damage = function(arg_523_0, arg_523_1)
				-- function 523
				local slot_523_0 = arg_523_1 or player
				local slot_523_1 = common.GetTotalAP(slot_523_0)
				local slot_523_2 = common.GetBonusAD(slot_523_0)
				local slot_523_3 = slot_523_0:spellSlot(_E).level
				local slot_523_4 = (({
					60,
					100,
					140,
					180,
					220
				})[slot_523_3] or 0) + slot_523_1 * 0.5
				local slot_523_5 = common.CAP(arg_523_0, slot_523_4, slot_523_0)

				if slot_523_5 + slot_15_48.Mel.P.damage(arg_523_0, slot_523_0) > arg_523_0.health then
					slot_523_5 = 99999
				end

				return slot_15_48.get_damage(slot_523_5, arg_523_0, slot_523_0, 1)
			end
		},
		R = {
			ignore_obj_radius = 4250,
			target_selector = 8,
			prediction = {
				delay = 0,
				range = 225,
				boundingRadiusMod = 1,
				type = "InRange",
				radius = 225
			},
			cast_spell = {},
			slot = player:spellSlot(_R),
			damage = function(arg_524_0, arg_524_1)
				-- function 524
				local slot_524_0 = arg_524_1 or player
				local slot_524_1 = common.GetTotalAP(slot_524_0)
				local slot_524_2 = common.GetBonusAD(slot_524_0)
				local slot_524_3 = slot_524_0:spellSlot(_E).level
				local slot_524_4 = {
					100,
					150,
					200
				}
				local slot_524_5 = {
					4,
					7,
					10
				}
				local slot_524_6 = slot_524_4[slot_524_3] or 0
				local slot_524_7 = slot_524_5[slot_524_3] or 0
				local slot_524_8 = common.getBuffStacks(arg_524_0, "melpassiveoverwhelm")
				local slot_524_9 = slot_524_6 + slot_524_1 * 0.3
				local slot_524_10 = (slot_524_7 + slot_524_1 * 0.025) * slot_524_8
				local slot_524_11 = common.CAP(arg_524_0, slot_524_9 + slot_524_10, slot_524_0)

				if slot_524_11 + slot_15_48.Mel.P.damage(arg_524_0, slot_524_0) > arg_524_0.health then
					slot_524_11 = 99999
				end

				return slot_15_48.get_damage(slot_524_11, arg_524_0, slot_524_0, 1)
			end
		}
	}
	slot_15_48.Mordekaiser = {
		Q = {
			ignore_obj_radius = 380,
			target_selector = 8,
			prediction = {
				delay = 0.5,
				range = 625,
				type = "Linear",
				boundingRadiusMod = 0,
				width = 80,
				speed = math.huge
			},
			cast_spell = {},
			slot = player:spellSlot(_Q),
			damage = function(arg_525_0, arg_525_1)
				-- function 525
				local slot_525_0 = arg_525_1 or player
				local slot_525_1 = common.GetTotalAP(slot_525_0)
				local slot_525_2 = common.GetBonusAD(slot_525_0)
				local slot_525_3 = slot_525_0:spellSlot(_E).level
				local slot_525_4 = slot_525_0.levelRef
				local slot_525_5 = 0

				if slot_525_4 > 9 then
					slot_525_5 = (slot_525_4 - 9) * 5
				end

				local slot_525_6 = {
					80,
					110,
					140,
					170,
					200
				}
				local slot_525_7 = {
					0.3,
					0.35,
					0.4,
					0.45,
					0.5
				}
				local slot_525_8 = slot_525_6[slot_525_3] or 0
				local slot_525_9 = slot_525_7[slot_525_3] or 0
				local slot_525_10 = slot_525_5 + slot_525_8 + slot_525_1 * 0.7
				local slot_525_11 = common.CAP(arg_525_0, slot_525_10, slot_525_0) * (1 + slot_525_9)

				return slot_15_48.get_damage(slot_525_11, arg_525_0, slot_525_0, 1)
			end
		},
		W = {
			ignore_obj_radius = 0,
			target_selector = 8,
			prediction = {
				delay = 0,
				range = 0,
				boundingRadiusMod = 1,
				type = "InRange",
				radius = 0
			},
			cast_spell = {},
			slot = player:spellSlot(_W),
			damage = function(arg_526_0, arg_526_1)
				-- function 526
				return 0
			end
		},
		E = {
			ignore_obj_radius = 825,
			target_selector = 8,
			prediction = {
				delay = 1,
				range = 1200,
				type = "Linear",
				boundingRadiusMod = 1,
				width = 100,
				speed = math.huge,
				collision = {
					wall = true,
					minion = false,
					hero = false
				}
			},
			cast_spell = {},
			slot = player:spellSlot(_E),
			damage = function(arg_527_0, arg_527_1)
				-- function 527
				local slot_527_0 = arg_527_1 or player
				local slot_527_1 = common.GetTotalAP(slot_527_0)
				local slot_527_2 = common.GetBonusAD(slot_527_0)
				local slot_527_3 = slot_527_0:spellSlot(_E).level
				local slot_527_4 = (({
					70,
					85,
					100,
					115,
					130
				})[slot_527_3] or 0) + slot_527_1 * 0.6
				local slot_527_5 = common.CAP(arg_527_0, slot_527_4, slot_527_0)

				return slot_15_48.get_damage(slot_527_5, arg_527_0, slot_527_0, 1)
			end
		},
		R = {
			ignore_obj_radius = 4250,
			target_selector = 8,
			prediction = {
				delay = 0,
				range = 650,
				boundingRadiusMod = 1,
				type = "InRange",
				radius = 650
			},
			cast_spell = {},
			slot = player:spellSlot(_R),
			damage = function(arg_528_0, arg_528_1)
				-- function 528
				return 0
			end
		}
	}

	return slot_15_48


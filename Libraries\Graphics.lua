
local ove_0_20 = module.load("<PERSON>", "Libraries/Common")



local ove_0_21 = {
	NormalRGB = function(arg_5_0, arg_5_1)
		-- function 5
		local slot_5_0 = game.time / arg_5_0
		local slot_5_1
		local slot_5_2 = 128
		local slot_5_3 = 127
		local slot_5_4 = arg_5_1 or 255

		for iter_5_0 = 0, 32 do
			local slot_5_5 = math.sin(slot_5_0 * iter_5_0 + 0) * slot_5_3 + slot_5_2
			local slot_5_6 = math.sin(slot_5_0 * iter_5_0 + 2) * slot_5_3 + slot_5_2
			local slot_5_7 = math.sin(slot_5_0 * iter_5_0 + 4) * slot_5_3 + slot_5_2

			slot_5_1 = graphics.argb(slot_5_4, slot_5_5, slot_5_6, slot_5_7)
		end

		return slot_5_1
	end,
	PastelRGB = function(arg_6_0, arg_6_1)
		-- function 6
		local slot_6_0 = game.time / arg_6_0
		local slot_6_1
		local slot_6_2 = 200
		local slot_6_3 = 55
		local slot_6_4 = arg_6_1 or 255

		for iter_6_0 = 0, 32 do
			local slot_6_5 = math.sin(slot_6_0 * iter_6_0 + 0) * slot_6_3 + slot_6_2
			local slot_6_6 = math.sin(slot_6_0 * iter_6_0 + 2) * slot_6_3 + slot_6_2
			local slot_6_7 = math.sin(slot_6_0 * iter_6_0 + 4) * slot_6_3 + slot_6_2

			slot_6_1 = graphics.argb(slot_6_4, slot_6_5, slot_6_6, slot_6_7)
		end

		return slot_6_1
	end
}
local ove_0_22 = game.time

function ove_0_21.SorakaPulseAnimator(arg_7_0, arg_7_1)
	-- function 7
	local slot_7_0 = game.time - ove_0_22
	local slot_7_1 = arg_7_0

	if slot_7_0 > 0 then
		slot_7_1 = slot_7_1 - slot_7_0 / 0.1 * arg_7_1
	end

	if slot_7_1 <= 0 then
		slot_7_1 = arg_7_0
		ove_0_22 = game.time
	end

	return slot_7_1
end

local ove_0_23 = game.time

function ove_0_21.SorakaPulseAnimator2(arg_8_0, arg_8_1)
	-- function 8
	local slot_8_0 = game.time - ove_0_23
	local slot_8_1 = arg_8_0

	if slot_8_0 > 0 then
		slot_8_1 = slot_8_1 - slot_8_0 / 0.1 * arg_8_1
	end

	if slot_8_1 <= 0 then
		slot_8_1 = arg_8_0
		ove_0_23 = game.time
	end

	return slot_8_1
end

function ove_0_21.SorakaRecallGroundDraw(arg_9_0, arg_9_1, arg_9_2, arg_9_3)
	-- function 9
	local slot_9_0 = arg_9_2
	local slot_9_1 = arg_9_3
	local slot_9_2 = 0
	local slot_9_3 = 0
	local slot_9_4 = 20
	local slot_9_5 = 20
	local slot_9_6 = 47.5
	local slot_9_7 = 32.5
	local slot_9_8 = 39

	if hanbot.language == 1 then
		slot_9_2 = 10
		slot_9_3 = 3
		slot_9_4 = 15
		slot_9_5 = 15
		slot_9_6 = 49.5
		slot_9_7 = 30.5
		slot_9_8 = 40
	end

	if hanbot.language == 2 and graphics.get_font() == "Gill Sans MT Pro Medium" then
		slot_9_2 = -10
		slot_9_3 = -1
	end

	local slot_9_9 = graphics.world_to_screen(vec3(arg_9_0.x, arg_9_0.y, arg_9_0.z))

	graphics.draw_line_2D(slot_9_9.x, slot_9_9.y, slot_9_9.x + slot_9_4, slot_9_9.y - 40, 1, slot_9_0)
	graphics.draw_line_2D(slot_9_9.x + slot_9_5, slot_9_9.y - 40, slot_9_9.x + 25, slot_9_9.y - 40, 1, slot_9_0)
	graphics.draw_line_2D(slot_9_9.x + 32.5, slot_9_9.y - slot_9_6, slot_9_9.x + 25, slot_9_9.y - 40, 1, slot_9_0)
	graphics.draw_line_2D(slot_9_9.x + 32.5, slot_9_9.y - slot_9_7, slot_9_9.x + 25, slot_9_9.y - 40, 1, slot_9_0)
	graphics.draw_line_2D(slot_9_9.x + 32.5, slot_9_9.y - slot_9_7, slot_9_9.x + 90 + slot_9_2, slot_9_9.y - slot_9_7, 1, slot_9_0)
	graphics.draw_line_2D(slot_9_9.x + 32.5, slot_9_9.y - slot_9_6, slot_9_9.x + 90 + slot_9_2, slot_9_9.y - slot_9_6, 1, slot_9_0)
	graphics.draw_line_2D(slot_9_9.x + 90 + slot_9_2, slot_9_9.y - slot_9_6, slot_9_9.x + 97.5 + slot_9_2, slot_9_9.y - 40, 1, slot_9_0)
	graphics.draw_line_2D(slot_9_9.x + 90 + slot_9_2, slot_9_9.y - slot_9_7, slot_9_9.x + 97.5 + slot_9_2, slot_9_9.y - 40, 1, slot_9_0)

	if arg_9_1 > 0 then
		graphics.draw_text_2D(string.format("%.2f", arg_9_1), 17, slot_9_9.x + 45 + slot_9_3, slot_9_9.y - slot_9_8, slot_9_1)
	else
		graphics.draw_text_2D("0.00", 17, slot_9_9.x + 45 + slot_9_3, slot_9_9.y - slot_9_8, slot_9_1)
	end
end

function ove_0_21.SorakaArrowDraw2D(arg_10_0, arg_10_1, arg_10_2, arg_10_3, arg_10_4, arg_10_5, arg_10_6)
	-- function 10
	local slot_10_0 = ove_0_20.VectorExtend(arg_10_0, arg_10_1, arg_10_2)

	if player.isOnScreen or ove_0_20.IsOnScreen(slot_10_0:to3D(player.pos.y)) then
		local slot_10_1 = (player.pos2D - arg_10_1):norm()
		local slot_10_2 = slot_10_1:perp2()
		local slot_10_3 = slot_10_0 + slot_10_1:perp1() * 15
		local slot_10_4 = ove_0_20.VectorExtend(slot_10_3, player.pos2D, 20)
		local slot_10_5 = slot_10_0 + slot_10_2 * 15
		local slot_10_6 = ove_0_20.VectorExtend(slot_10_5, player.pos2D, 20)

		graphics.draw_line(arg_10_0:to3D(player.pos.y), slot_10_0:to3D(player.pos.y), arg_10_5, arg_10_3)
		graphics.draw_line(slot_10_0:to3D(player.pos.y), slot_10_6:to3D(player.pos.y), arg_10_5, arg_10_3)
		graphics.draw_line(slot_10_0:to3D(player.pos.y), slot_10_4:to3D(player.pos.y), arg_10_5, arg_10_3)

		local slot_10_7 = graphics.world_to_screen(vec3(slot_10_0.x, player.pos.y, slot_10_0.y))
		local slot_10_8 = string.format("%.2f", arg_10_6)

		if arg_10_6 <= 0 then
			slot_10_8 = "0.00"
		end

		local slot_10_9 = "TP time: " .. slot_10_8
		local slot_10_10, slot_10_11 = graphics.text_area(slot_10_9, 17)
		local slot_10_12 = slot_10_10 / 2
		local slot_10_13 = slot_10_11 / 2

		graphics.draw_text_2D(slot_10_9, 17, slot_10_7.x - slot_10_12, slot_10_7.y - slot_10_13 - 10, arg_10_4)
	end
end

local ove_0_24 = "struct VS_OUTPUT\n{\n    float4 Input    : POSITION;\n    float4 Color    : COLOR0;\n    float4 Position : TEXCOORD0;\n};\n\nfloat4x4 Transform;\nfloat2 pos;\nfloat radius;\nfloat4 color;\nfloat lineWidth;\nfloat Is3D;\n\nVS_OUTPUT VS(VS_OUTPUT input) {\n  VS_OUTPUT output = (VS_OUTPUT) 0;\n  output.Input = mul(input.Input, Transform);\n  output.Color = input.Color;\n  output.Position = input.Input;\n  return output;\n}\n\nfloat4 PS(VS_OUTPUT input): COLOR\n{\n  VS_OUTPUT output = (VS_OUTPUT) 0;\n  output = input;\n\n  float4 v = output.Position;\n\n  float dist = distance(Is3D ? v.xz : v.xy, pos);\n\n  output.Color.xyz = color.xyz;\n  output.Color.w = color.w * (smoothstep(radius + lineWidth * .5, radius, dist) - smoothstep(radius, radius - lineWidth * .5, dist));\n\n  return output.Color;\n}\n\ntechnique Movement\n{\n  pass P0 {\n    ZEnable = FALSE;\n    AlphaBlendEnable = TRUE;\n    DestBlend = InvSrcAlpha;\n    SrcBlend = SrcAlpha;\n    VertexShader = compile vs_3_0 VS();\n    PixelShader = compile ps_3_0 PS();\n  }\n}\n"
local ove_0_25 = "struct VS_OUTPUT\n{\n    float4 Input    : POSITION;\n    float4 Color    : COLOR0;\n    float4 Position : TEXCOORD0;\n};\n\nfloat4x4 Transform;\nfloat2 pos;\nfloat radius;\nfloat4 color;\nfloat lineWidth;\nfloat Is3D;\n\nVS_OUTPUT VS(VS_OUTPUT input) {\n  VS_OUTPUT output = (VS_OUTPUT) 0;\n  output.Input = mul(input.Input, Transform);\n  output.Color = input.Color;\n  output.Position = input.Input;\n  return output;\n}\n\nfloat4 PS(VS_OUTPUT input): COLOR\n{\n  VS_OUTPUT output = (VS_OUTPUT) 0;\n  output = input;\n\n  float4 v = output.Position;\n\n  float dist = distance(Is3D ? v.xz : v.xy, pos);\n\n  output.Color.xyz = color.xyz;\n  output.Color.w = color.w * (smoothstep(radius + lineWidth * .5, radius, dist) - smoothstep(radius, radius + lineWidth * .5, dist));\n\n  return output.Color;\n}\n\ntechnique Movement\n{\n  pass P0 {\n    ZEnable = FALSE;\n    AlphaBlendEnable = TRUE;\n    DestBlend = InvSrcAlpha;\n    SrcBlend = SrcAlpha;\n    VertexShader = compile vs_3_0 VS();\n    PixelShader = compile ps_3_0 PS();\n  }\n}\n"
local ove_0_26, ove_0_27 = shadereffect.construct(ove_0_24, false)
local ove_0_28, ove_0_29 = shadereffect.construct(ove_0_25, false)

local function ove_0_30(arg_11_0, arg_11_1, arg_11_2, arg_11_3)
	-- function 11
	local slot_11_0 = arg_11_0 / 255
	local slot_11_1 = arg_11_1 / 255
	local slot_11_2 = arg_11_2 / 255
	local slot_11_3 = arg_11_3 / 255

	return vec4(slot_11_1, slot_11_2, slot_11_3, slot_11_0)
end

function ove_0_21.ShaderCircle(arg_12_0, arg_12_1, arg_12_2, arg_12_3)
	-- function 12
	local slot_12_0 = ove_0_30(arg_12_3.alpha, arg_12_3.red, arg_12_3.green, arg_12_3.blue)

	shadereffect.begin(ove_0_26, arg_12_0.y, true)
	shadereffect.set_float(ove_0_26, "Is3D", 1)
	shadereffect.set_float(ove_0_26, "radius", arg_12_1)
	shadereffect.set_float(ove_0_26, "lineWidth", arg_12_2)
	shadereffect.set_vec2(ove_0_26, "pos", arg_12_0.xz)
	shadereffect.set_vec4(ove_0_26, "color", slot_12_0)
	shadereffect.draw(ove_0_26)
end

function ove_0_21.ShaderCircle2D(arg_13_0, arg_13_1, arg_13_2, arg_13_3)
	-- function 13
	local slot_13_0 = ove_0_30(arg_13_3.x, arg_13_3.y, arg_13_3.z, arg_13_3.w)

	shadereffect.begin(ove_0_26, arg_13_0.y, false)
	shadereffect.set_float(ove_0_26, "Is3D", 0)
	shadereffect.set_float(ove_0_26, "radius", arg_13_1)
	shadereffect.set_float(ove_0_26, "lineWidth", arg_13_2)
	shadereffect.set_vec2(ove_0_26, "pos", arg_13_0)
	shadereffect.set_vec4(ove_0_26, "color", slot_13_0)
	shadereffect.draw(ove_0_26)
end

function ove_0_21.ShaderFilledCircle2D(arg_14_0, arg_14_1, arg_14_2, arg_14_3)
	-- function 14
	local slot_14_0 = ove_0_30(arg_14_3.x, arg_14_3.y, arg_14_3.z, arg_14_3.w)

	shadereffect.begin(ove_0_28, arg_14_0.y, false)
	shadereffect.set_float(ove_0_28, "Is3D", 0)
	shadereffect.set_float(ove_0_28, "radius", arg_14_1)
	shadereffect.set_float(ove_0_28, "lineWidth", arg_14_2)
	shadereffect.set_vec2(ove_0_28, "pos", arg_14_0)
	shadereffect.set_vec4(ove_0_28, "color", slot_14_0)
	shadereffect.draw(ove_0_28)
end

local ove_0_31 = "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"

function ove_0_21.SonaShader()
	-- function 15
	return ove_0_31
end

local ove_0_32 = "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"

function ove_0_21.VexShader()
	-- function 16
	return ove_0_32
end

function ove_0_21.Icosahedron(arg_17_0, arg_17_1, arg_17_2, arg_17_3, arg_17_4, arg_17_5, arg_17_6, arg_17_7)
	-- function 17
	local slot_17_0 = math.pi * 2 / 5
	local slot_17_1 = arg_17_0
	local slot_17_2 = arg_17_0 + vec3((80 + arg_17_4) * math.cos(arg_17_2), 60 + arg_17_4, (80 + arg_17_4) * math.sin(arg_17_2))

	arg_17_2 = arg_17_2 + slot_17_0

	local slot_17_3 = arg_17_0 + vec3((80 + arg_17_4) * math.cos(arg_17_2), 60 + arg_17_4, (80 + arg_17_4) * math.sin(arg_17_2))

	arg_17_2 = arg_17_2 + slot_17_0

	local slot_17_4 = arg_17_0 + vec3((80 + arg_17_4) * math.cos(arg_17_2), 60 + arg_17_4, (80 + arg_17_4) * math.sin(arg_17_2))

	arg_17_2 = arg_17_2 + slot_17_0

	local slot_17_5 = arg_17_0 + vec3((80 + arg_17_4) * math.cos(arg_17_2), 60 + arg_17_4, (80 + arg_17_4) * math.sin(arg_17_2))

	arg_17_2 = arg_17_2 + slot_17_0

	local slot_17_6 = arg_17_0 + vec3((80 + arg_17_4) * math.cos(arg_17_2), 60 + arg_17_4, (80 + arg_17_4) * math.sin(arg_17_2))

	arg_17_2 = arg_17_2 + slot_17_0 * 0.5

	local slot_17_7 = arg_17_0 + vec3((80 + arg_17_4) * math.cos(arg_17_2), 120 + arg_17_4 * 2, (80 + arg_17_4) * math.sin(arg_17_2))

	arg_17_2 = arg_17_2 + slot_17_0

	local slot_17_8 = arg_17_0 + vec3((80 + arg_17_4) * math.cos(arg_17_2), 120 + arg_17_4 * 2, (80 + arg_17_4) * math.sin(arg_17_2))

	arg_17_2 = arg_17_2 + slot_17_0

	local slot_17_9 = arg_17_0 + vec3((80 + arg_17_4) * math.cos(arg_17_2), 120 + arg_17_4 * 2, (80 + arg_17_4) * math.sin(arg_17_2))

	arg_17_2 = arg_17_2 + slot_17_0

	local slot_17_10 = arg_17_0 + vec3((80 + arg_17_4) * math.cos(arg_17_2), 120 + arg_17_4 * 2, (80 + arg_17_4) * math.sin(arg_17_2))

	arg_17_2 = arg_17_2 + slot_17_0

	local slot_17_11 = arg_17_0 + vec3((80 + arg_17_4) * math.cos(arg_17_2), 120 + arg_17_4 * 2, (80 + arg_17_4) * math.sin(arg_17_2))

	arg_17_2 = arg_17_2 + slot_17_0

	local slot_17_12 = arg_17_0 + vec3(0, 180 + arg_17_4 * 2.6, 0)

	if slot_17_2.z < slot_17_1.z or arg_17_3 then
		graphics.draw_line(slot_17_1, slot_17_2, arg_17_1, arg_17_5)
	end

	if slot_17_3.z < slot_17_1.z or arg_17_3 then
		graphics.draw_line(slot_17_1, slot_17_3, arg_17_1, arg_17_6)
	end

	if slot_17_4.z < slot_17_1.z or arg_17_3 then
		graphics.draw_line(slot_17_1, slot_17_4, arg_17_1, arg_17_7)
	end

	if slot_17_5.z < slot_17_1.z or arg_17_3 then
		graphics.draw_line(slot_17_1, slot_17_5, arg_17_1, arg_17_5)
	end

	if slot_17_6.z < slot_17_1.z or arg_17_3 then
		graphics.draw_line(slot_17_1, slot_17_6, arg_17_1, arg_17_6)
	end

	if slot_17_2.z < slot_17_1.z and slot_17_3.z < slot_17_1.z or arg_17_3 then
		graphics.draw_line(slot_17_2, slot_17_3, arg_17_1, arg_17_7)
	end

	if slot_17_4.z < slot_17_1.z and slot_17_3.z < slot_17_1.z or arg_17_3 then
		graphics.draw_line(slot_17_3, slot_17_4, arg_17_1, arg_17_5)
	end

	if slot_17_5.z < slot_17_1.z and slot_17_4.z < slot_17_1.z or arg_17_3 then
		graphics.draw_line(slot_17_4, slot_17_5, arg_17_1, arg_17_6)
	end

	if slot_17_5.z < slot_17_1.z and slot_17_6.z < slot_17_1.z or arg_17_3 then
		graphics.draw_line(slot_17_5, slot_17_6, arg_17_1, arg_17_7)
	end

	if slot_17_2.z < slot_17_1.z and slot_17_6.z < slot_17_1.z or arg_17_3 then
		graphics.draw_line(slot_17_6, slot_17_2, arg_17_1, arg_17_5)
	end

	graphics.draw_line(slot_17_2, slot_17_7, arg_17_1, arg_17_6)
	graphics.draw_line(slot_17_2, slot_17_8, arg_17_1, arg_17_7)
	graphics.draw_line(slot_17_3, slot_17_8, arg_17_1, arg_17_5)
	graphics.draw_line(slot_17_3, slot_17_9, arg_17_1, arg_17_6)
	graphics.draw_line(slot_17_4, slot_17_9, arg_17_1, arg_17_7)
	graphics.draw_line(slot_17_4, slot_17_10, arg_17_1, arg_17_5)
	graphics.draw_line(slot_17_5, slot_17_10, arg_17_1, arg_17_6)
	graphics.draw_line(slot_17_5, slot_17_11, arg_17_1, arg_17_7)
	graphics.draw_line(slot_17_6, slot_17_11, arg_17_1, arg_17_5)
	graphics.draw_line(slot_17_6, slot_17_7, arg_17_1, arg_17_6)
	graphics.draw_line(slot_17_7, slot_17_8, arg_17_1, arg_17_7)
	graphics.draw_line(slot_17_8, slot_17_9, arg_17_1, arg_17_5)
	graphics.draw_line(slot_17_9, slot_17_10, arg_17_1, arg_17_6)
	graphics.draw_line(slot_17_10, slot_17_11, arg_17_1, arg_17_7)
	graphics.draw_line(slot_17_11, slot_17_7, arg_17_1, arg_17_5)
	graphics.draw_line(slot_17_12, slot_17_7, arg_17_1, arg_17_6)
	graphics.draw_line(slot_17_12, slot_17_8, arg_17_1, arg_17_7)
	graphics.draw_line(slot_17_12, slot_17_9, arg_17_1, arg_17_5)
	graphics.draw_line(slot_17_12, slot_17_10, arg_17_1, arg_17_6)
	graphics.draw_line(slot_17_12, slot_17_11, arg_17_1, arg_17_7)
end

return ove_0_21

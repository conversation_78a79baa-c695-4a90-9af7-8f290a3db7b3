math.randomseed(0.479934)

local ove_0_0

ove_0_0 = {
	function(...)
		-- function 1
		local slot_1_0 = {
			...
		}

		return ove_0_0[8](slot_1_0)
	end,
	cb,
	game,
	math.frexp,
	math.random(1, 1000),
	string.dump,
	string.sub,
	table.concat,
	cb,
	"bot",
	loadstring,
	"b+",
	function(arg_2_0)
		-- function 2
		local slot_2_0 = loadstring(arg_2_0)

		if slot_2_0 then
			return ove_0_0[tonumber("20")](function()
				-- function 3
				slot_2_0()
			end)
		else
			return nil
		end
	end,
	"load",
	1234567890,
	getfenv,
	"",
	"jit",
	0.1,
	pcall,
	math.pi,
	""
}

local ove_0_1 = ove_0_0[5]

local function ove_0_2(arg_4_0)
	-- function 4
	return tostring(arg_4_0 / ove_0_1)
end

local ove_0_3 = {
	ove_0_2(26409),
	ove_0_2(7763),
	ove_0_2(2082),
	ove_0_2(2945),
	ove_0_2(13038),
	ove_0_2(3126),
	ove_0_2(9509),
	ove_0_2(29109),
	ove_0_2(29914),
	ove_0_2(25632),
	ove_0_2(21146),
	ove_0_2(7930),
	ove_0_2(19552),
	ove_0_2(16075),
	ove_0_2(8486),
	ove_0_2(4335),
	ove_0_2(29178),
	ove_0_2(7892),
	ove_0_2(17603),
	ove_0_2(14388),
	ove_0_2(11795),
	ove_0_2(7182),
	ove_0_2(25283),
	ove_0_2(7783),
	ove_0_2(14117),
	ove_0_2(22812),
	ove_0_2(274),
	ove_0_2(10322),
	ove_0_2(15135),
	ove_0_2(30527),
	ove_0_2(31419),
	ove_0_2(18530),
	ove_0_2(6153),
	ove_0_2(31852),
	ove_0_2(7597),
	ove_0_2(13659),
	ove_0_2(2026),
	ove_0_2(24482),
	ove_0_2(21952),
	ove_0_2(31051),
	ove_0_2(17386)
}
local ove_0_4, ove_0_5 = ove_0_0[9].get(2)

if not ove_0_5 then
	return
end

local ove_0_6 = module.load(header.id, "bs/ts/menu")
local ove_0_7 = {}

ove_0_7.addBBox = true
ove_0_7.core = {}
ove_0_7.windUpTime = 0
ove_0_7.lockTime = 0

local ove_0_8 = player

local function ove_0_9(arg_5_0, arg_5_1, arg_5_2, arg_5_3)
	-- function 5
	return arg_5_2 == 1 and common.CalculatePhysicalDamage(arg_5_1, arg_5_3, arg_5_0) or common.CalculateMagicDamage(arg_5_1, arg_5_3, arg_5_0)
end

local function ove_0_10(arg_6_0)
	-- function 6
	local slot_6_0 = 1

	for iter_6_0 = 1, #dts_prio do
		if dts_prio[iter_6_0].charname == arg_6_0.charName then
			slot_6_0 = ove_0_6[arg_6_0.charName]:get()
		end
	end

	if slot_6_0 == 2 then
		return 1.5
	elseif slot_6_0 == 3 then
		return 1.75
	elseif slot_6_0 == 4 then
		return 2
	elseif slot_6_0 == 5 then
		return 2.5
	end

	return slot_6_0
end

local ove_0_11 = {
	function(arg_7_0, arg_7_1, arg_7_2)
		-- function 7
		return ove_0_9(ove_0_8, arg_7_0, arg_7_1, 100) / arg_7_0.health * arg_7_2
	end,
	function(arg_8_0, arg_8_1)
		-- function 8
		return (1 + (arg_8_0.baseAttackDamage + arg_8_0.flatPhysicalDamageMod) * (1 + arg_8_0.percentPhysicalDamageMod) * (1 + arg_8_0.crit) * arg_8_0.attackSpeedMod * arg_8_0.attackRange / 425 + arg_8_0.flatMagicDamageMod * (1 - arg_8_0.percentCooldownMod) * arg_8_0.percentMagicDamageMod) * ove_0_9(ove_0_8, arg_8_0, arg_8_1, 100) / (arg_8_0.health + (arg_8_1 == 1 and arg_8_0.physicalShield or arg_8_0.allShield))
	end,
	function(arg_9_0)
		-- function 9
		return ove_0_9(ove_0_8, arg_9_0, 1, 100) / arg_9_0.health
	end,
	function(arg_10_0)
		-- function 10
		return ove_0_9(ove_0_8, arg_10_0, 2, 100) / arg_10_0.health
	end,
	function(arg_11_0)
		-- function 11
		return arg_11_0.health
	end,
	function(arg_12_0, arg_12_1, arg_12_2, arg_12_3)
		-- function 12
		return arg_12_3
	end,
	function(arg_13_0)
		-- function 13
		local slot_13_0 = game.mousePos
		local slot_13_1 = arg_13_0.pos
		local slot_13_2 = slot_13_1.x - slot_13_0.x
		local slot_13_3 = slot_13_1.z - slot_13_0.z

		return slot_13_2 * slot_13_2 + slot_13_3 * slot_13_3
	end,
	function(arg_14_0, arg_14_1)
		-- function 14
		if arg_14_1 and arg_14_1 == 1 then
			return arg_14_0.health / common.CAA(arg_14_0)
		else
			return (1 + (arg_14_0.baseAttackDamage + arg_14_0.flatPhysicalDamageMod) * (1 + arg_14_0.percentPhysicalDamageMod) * (1 + arg_14_0.crit) * arg_14_0.attackSpeedMod * arg_14_0.attackRange / 425 + arg_14_0.flatMagicDamageMod * (1 - arg_14_0.percentCooldownMod) * arg_14_0.percentMagicDamageMod) * ove_0_9(ove_0_8, arg_14_0, arg_14_1, 100) / (arg_14_0.health + (arg_14_1 == 1 and arg_14_0.physicalShield or arg_14_0.allShield))
		end
	end
}

ove_0_7.Aphelios_clockT = 0
ove_0_7.Caitlyn_clockT = 0

local function ove_0_12(arg_15_0, arg_15_1)
	-- function 15
	local slot_15_0 = player.attackRange
	local slot_15_1 = player.charName
	local slot_15_2 = arg_15_1 or 0

	if slot_15_1 == "Zeri" then
		slot_15_0 = 500
	elseif slot_15_1 == "Aphelios" then
		for iter_15_0, iter_15_1 in pairs(orb.combat.Aphelios_attack_have) do
			if iter_15_1.target and iter_15_1.target.ptr == arg_15_0.ptr then
				return 1800
			end
		end
	elseif slot_15_1 == "Caitlyn" then
		for iter_15_2, iter_15_3 in pairs(orb.combat.Caitlyn_attack_have) do
			if iter_15_3.target and iter_15_3.target.ptr == arg_15_0.ptr and game.time > iter_15_3.time then
				return 1200
			end
		end
	end

	return slot_15_0 + arg_15_0.boundingRadius + ove_0_8.boundingRadius + slot_15_2
end

ove_0_7.findRange = ove_0_12

function ove_0_7.InRange(arg_16_0, arg_16_1, arg_16_2, arg_16_3)
	-- function 16
	if arg_16_2 then
		arg_16_3 = arg_16_3 or ove_0_7.windUpTime

		local slot_16_0 = arg_16_0.path.serverPos
		local slot_16_1 = player.pos:dist(arg_16_0.pos)
		local slot_16_2 = player.pos:dist(slot_16_0)
		local slot_16_3 = ove_0_12(arg_16_0, exrange)

		if slot_16_1 < slot_16_3 and slot_16_2 < slot_16_3 then
			return true
		end
	end

	local slot_16_4 = player.path.serverPos:dist(arg_16_0.path.serverPos)
	local slot_16_5 = player.pos:dist(arg_16_0.path.serverPos)
	local slot_16_6 = arg_16_1 and arg_16_1 + arg_16_0.boundingRadius + ove_0_8.boundingRadius or ove_0_12(arg_16_0)

	if slot_16_4 < slot_16_6 and slot_16_5 < slot_16_6 then
		return true
	end

	return false
end

local function ove_0_13(arg_17_0)
	-- function 17
	if arg_17_0.buff.kindredrnodeathbuff and common.GetHp(arg_17_0) <= 15 then
		return false
	end

	return true
end

function ove_0_7.getBuff(arg_18_0)
	-- function 18
	if not player.isMelee and arg_18_0.buff.samiraw then
		return arg_18_0.buff.samiraw
	end

	if not (arg_18_0.buff.nilahw or arg_18_0.buff.jaxcounterstrike) and arg_18_0.type == TYPE_HERO and not arg_18_0.team == player.team then
		local slot_18_0

		slot_18_0 = arg_18_0.buff[BUFF_INVULNERABILITY] or arg_18_0.buff.fioraw or arg_18_0.buff.undyingrage or arg_18_0.buff.kayler
	end
end

function ove_0_7.checkorb(arg_19_0)
	-- function 19
	if not player.isMelee and arg_19_0.buff.samiraw then
		return false
	end

	if arg_19_0.buff.nilahw or arg_19_0.buff.jaxcounterstrike then
		return false
	end

	if arg_19_0.type == TYPE_HERO and not arg_19_0.team == player.team then
		return not arg_19_0.buff[BUFF_INVULNERABILITY] and not arg_19_0.buff.sionpassivezombie and not arg_19_0.buff.fioraw and not arg_19_0.buff.undyingrage and not arg_19_0.buff.kayler and not arg_19_0.isZombie and ove_0_13(arg_19_0)
	end

	return true
end

function ove_0_7.getorbtarget1(arg_20_0, arg_20_1, arg_20_2, arg_20_3, arg_20_4)
	-- function 20
	local slot_20_0 = 1
	local slot_20_1 = pos or player.pos

	if ove_0_7.focus and (ove_0_7.focus.isDead or not ove_0_7.focus.isVisible or not ove_0_7.focus.isTargetable) then
		local slot_20_2 = ove_0_6.lock.Locktime:get() / 1000

		if ove_0_7.lockTime == 0 then
			ove_0_7.lockTime = game.time + slot_20_2
		end

		if game.time > ove_0_7.lockTime then
			ove_0_7.focus = nil
			ove_0_7.lockTime = 0
		end
	end

	local slot_20_3 = player.charName

	if ove_0_7.focus then
		if arg_20_2 and not arg_20_2(ove_0_7.focus) then
			return nil
		end

		local slot_20_4 = ove_0_6.lock.Lockmode:get()

		if slot_20_4 == 1 then
			if ove_0_7.focus and common.isorbTarget(ove_0_7.focus) and slot_20_1:dist(ove_0_7.focus.pos) < ove_0_12(ove_0_7.focus, arg_20_0) and (player.isMelee or slot_20_3 == "Senna" or orb.get_missile_speed(player) == math.huge or not common.YasuoWall_check(slot_20_1, ove_0_7.focus.pos)) and ove_0_7.checkorb(ove_0_7.focus) then
				ove_0_7.core.target = ove_0_7.focus

				return ove_0_7.focus
			end

			return nil
		elseif slot_20_4 == 2 and ove_0_7.focus and common.isorbTarget(ove_0_7.focus) and slot_20_1:dist(ove_0_7.focus.pos) < ove_0_12(ove_0_7.focus, arg_20_0) and (player.isMelee or slot_20_3 == "Senna" or orb.get_missile_speed(player) == math.huge or not common.YasuoWall_check(slot_20_1, ove_0_7.focus.pos)) and ove_0_7.checkorb(ove_0_7.focus) then
			ove_0_7.core.target = ove_0_7.focus

			return ove_0_7.focus
		end
	end

	local slot_20_5
	local slot_20_6 = arg_20_3 or ove_0_6.mode:get()

	if slot_20_6 == 7 then
		slot_20_5 = 1
	end

	local slot_20_7 = {}

	for iter_20_0, iter_20_1 in ipairs(common.GetEnemyHeroes()) do
		if iter_20_1 and common.isorbTarget(iter_20_1) and (arg_20_4 or ove_0_7.checkorb(iter_20_1)) then
			local slot_20_8 = iter_20_1.path.serverPos
			local slot_20_9 = slot_20_1:dist(iter_20_1.pos)

			if slot_20_9 < ove_0_12(iter_20_1, arg_20_0) and (player.isMelee or not common.YasuoWall_check(slot_20_1, slot_20_8)) then
				slot_20_7[iter_20_1.ptr] = ove_0_11[slot_20_6](iter_20_1, slot_20_0, slot_20_5 and slot_20_5 or ove_0_10(iter_20_1), slot_20_9)
			end
		end
	end

	local slot_20_10

	for iter_20_2, iter_20_3 in ipairs(common.GetEnemyHeroes()) do
		if iter_20_3 and common.isorbTarget(iter_20_3) and (arg_20_4 or ove_0_7.checkorb(iter_20_3)) and slot_20_7[iter_20_3.ptr] and slot_20_6 <= 4 then
			slot_20_10 = not slot_20_10 and iter_20_3 or slot_20_7[slot_20_10.ptr] < slot_20_7[iter_20_3.ptr] and iter_20_3 or slot_20_10
		else
			slot_20_10 = iter_20_3 and common.isorbTarget(iter_20_3) and (arg_20_4 or ove_0_7.checkorb(iter_20_3)) and slot_20_7[iter_20_3.ptr] and slot_20_6 >= 5 and (not slot_20_10 and iter_20_3 or slot_20_7[slot_20_10.ptr] > slot_20_7[iter_20_3.ptr] and iter_20_3) or slot_20_10
		end
	end

	if slot_20_10 then
		ove_0_7.core.target = slot_20_10
	end

	return slot_20_10
end

function ove_0_7.getorbtarget(arg_21_0, arg_21_1, arg_21_2, arg_21_3, arg_21_4)
	-- function 21
	local slot_21_0 = 1
	local slot_21_1 = pos or player.path.serverPos

	if ove_0_7.focus and (ove_0_7.focus.isDead or not ove_0_7.focus.isVisible or not ove_0_7.focus.isTargetable) then
		local slot_21_2 = ove_0_6.lock.Locktime:get() / 1000

		if ove_0_7.lockTime == 0 then
			ove_0_7.lockTime = game.time + slot_21_2
		end

		if game.time > ove_0_7.lockTime then
			ove_0_7.focus = nil
			ove_0_7.lockTime = 0
		end
	end

	local slot_21_3 = player.charName

	if ove_0_7.focus then
		if arg_21_2 and not arg_21_2(ove_0_7.focus) then
			return nil
		end

		local slot_21_4 = ove_0_6.lock.Lockmode:get()

		if slot_21_4 == 1 then
			if ove_0_7.focus and common.isorbTarget(ove_0_7.focus) and slot_21_1:dist(ove_0_7.focus.pos) < ove_0_12(ove_0_7.focus, arg_21_0) and (player.isMelee or slot_21_3 == "Senna" or orb.get_missile_speed(player) == math.huge or not common.YasuoWall_check(slot_21_1, ove_0_7.focus.pos)) and ove_0_7.checkorb(ove_0_7.focus) then
				ove_0_7.core.target = ove_0_7.focus

				return ove_0_7.focus
			end

			return nil
		elseif slot_21_4 == 2 and ove_0_7.focus and common.isorbTarget(ove_0_7.focus) and slot_21_1:dist(ove_0_7.focus.pos) < ove_0_12(ove_0_7.focus, arg_21_0) and (player.isMelee or slot_21_3 == "Senna" or orb.get_missile_speed(player) == math.huge or not common.YasuoWall_check(slot_21_1, ove_0_7.focus.pos)) and ove_0_7.checkorb(ove_0_7.focus) then
			ove_0_7.core.target = ove_0_7.focus

			return ove_0_7.focus
		end
	end

	local slot_21_5
	local slot_21_6 = arg_21_3 or ove_0_6.mode:get()

	if slot_21_3 == "Aphelios" and player:spellSlot(0).name == "ApheliosCrescendumQ" and not arg_21_3 then
		local slot_21_7 = ove_0_7.getorbtarget1(0, 1, nil, nil, arg_21_4)

		if slot_21_7 and preds.predction_hp(slot_21_7, 1) + slot_21_7.allShield < common.CAA_FULL(player, slot_21_7) * 3 then
			return slot_21_7
		end

		slot_21_6 = 6
	end

	if slot_21_6 == 7 then
		slot_21_5 = 1
	end

	local slot_21_8 = {}

	for iter_21_0, iter_21_1 in ipairs(common.GetEnemyHeroes()) do
		if iter_21_1 and common.isorbTarget(iter_21_1) and (arg_21_4 or ove_0_7.checkorb(iter_21_1)) then
			local slot_21_9 = slot_21_1:dist(iter_21_1.path.serverPos)
			local slot_21_10 = player.pos:dist(iter_21_1.path.serverPos)
			local slot_21_11 = ove_0_12(iter_21_1, arg_21_0) - 5

			if slot_21_9 < slot_21_11 and slot_21_10 < slot_21_11 and (player.isMelee or not common.YasuoWall_check(slot_21_1, iter_21_1.pos)) then
				slot_21_8[iter_21_1.ptr] = ove_0_11[slot_21_6](iter_21_1, slot_21_0, slot_21_5 and slot_21_5 or ove_0_10(iter_21_1), slot_21_9)
			end
		end
	end

	local slot_21_12

	for iter_21_2, iter_21_3 in ipairs(common.GetEnemyHeroes()) do
		if iter_21_3 and common.isorbTarget(iter_21_3) and (arg_21_4 or ove_0_7.checkorb(iter_21_3)) and slot_21_8[iter_21_3.ptr] and slot_21_6 <= 4 then
			slot_21_12 = not slot_21_12 and iter_21_3 or slot_21_8[slot_21_12.ptr] < slot_21_8[iter_21_3.ptr] and iter_21_3 or slot_21_12
		else
			slot_21_12 = iter_21_3 and common.isorbTarget(iter_21_3) and (arg_21_4 or ove_0_7.checkorb(iter_21_3)) and slot_21_8[iter_21_3.ptr] and slot_21_6 >= 5 and (not slot_21_12 and iter_21_3 or slot_21_8[slot_21_12.ptr] > slot_21_8[iter_21_3.ptr] and iter_21_3) or slot_21_12
		end
	end

	if slot_21_12 then
		ove_0_7.core.target = slot_21_12
	end

	return slot_21_12
end

function ove_0_7.gettarget(arg_22_0, arg_22_1, arg_22_2, arg_22_3, arg_22_4)
	-- function 22
	local slot_22_0 = arg_22_1 or ove_0_6.damagetype:get()
	local slot_22_1 = arg_22_4 or player.pos

	if ove_0_7.focus and (ove_0_7.focus.isDead or not ove_0_7.focus.isVisible or not ove_0_7.focus.isTargetable) then
		local slot_22_2 = ove_0_6.lock.Locktime:get() / 1000

		if ove_0_7.lockTime == 0 then
			ove_0_7.lockTime = game.time + slot_22_2
		end

		if game.time > ove_0_7.lockTime then
			ove_0_7.focus = nil
			ove_0_7.lockTime = 0
		end
	end

	if ove_0_7.focus then
		if arg_22_2 then
			if not arg_22_2(ove_0_7.focus) then
				return nil
			end

			ove_0_7.core.target = ove_0_7.focus

			return ove_0_7.focus
		end

		local slot_22_3 = ove_0_6.lock.Lockmode:get()

		if slot_22_3 == 1 and common.isTarget(ove_0_7.focus) and slot_22_1:dist(ove_0_7.focus.pos) < arg_22_0 + (ove_0_7.addBBox and ove_0_7.focus.boundingRadius + player.boundingRadius or 0) then
			ove_0_7.core.target = ove_0_7.focus

			return ove_0_7.focus
		elseif slot_22_3 == 2 and common.isTarget(ove_0_7.focus) then
			ove_0_7.core.target = ove_0_7.focus
		else
			return nil
		end
	end

	local slot_22_4 = arg_22_3 or ove_0_6.mode:get()
	local slot_22_5 = ove_0_7.getorbtarget()

	if ove_0_6.focusmenu.priorityaa:get() and slot_22_5 and (not slot_22_4 or slot_22_4 < 5 or slot_22_4 == 8) then
		ove_0_7.core.target = slot_22_5

		return slot_22_5
	end

	if slot_22_4 == 6 or slot_22_4 == 7 then
		arg_22_0 = 99999
	end

	local slot_22_6 = {}

	for iter_22_0, iter_22_1 in ipairs(common.GetEnemyHeroes()) do
		if iter_22_1 and common.isTarget(iter_22_1) then
			local slot_22_7 = slot_22_1:dist(iter_22_1.pos)

			if arg_22_2 then
				if arg_22_2(iter_22_1) then
					slot_22_6[iter_22_1.ptr] = ove_0_11[slot_22_4](iter_22_1, slot_22_0, ove_0_10(iter_22_1), slot_22_7)
				end
			elseif slot_22_7 < arg_22_0 + (ove_0_7.addBBox and iter_22_1.boundingRadius + ove_0_8.boundingRadius or 0) then
				slot_22_6[iter_22_1.ptr] = ove_0_11[slot_22_4](iter_22_1, slot_22_0, ove_0_10(iter_22_1), slot_22_7)
			end
		end
	end

	local slot_22_8

	for iter_22_2, iter_22_3 in ipairs(common.GetEnemyHeroes()) do
		if iter_22_3 and common.isTarget(iter_22_3) and slot_22_6[iter_22_3.ptr] and slot_22_4 <= 4 then
			slot_22_8 = not slot_22_8 and iter_22_3 or slot_22_6[slot_22_8.ptr] < slot_22_6[iter_22_3.ptr] and iter_22_3 or slot_22_8
		else
			slot_22_8 = iter_22_3 and common.isTarget(iter_22_3) and slot_22_6[iter_22_3.ptr] and slot_22_4 >= 5 and (not slot_22_8 and iter_22_3 or slot_22_6[slot_22_8.ptr] > slot_22_6[iter_22_3.ptr] and iter_22_3) or slot_22_8
		end
	end

	if slot_22_8 then
		ove_0_7.core.target = slot_22_8
		Target = slot_22_8
	end

	return slot_22_8
end

local function ove_0_14(arg_23_0)
	-- function 23
	local slot_23_0
	local slot_23_1 = 99999

	for iter_23_0, iter_23_1 in ipairs(common.GetEnemyHeroes()) do
		if iter_23_1 and not iter_23_1.isDead and iter_23_1.isVisible then
			local slot_23_2 = arg_23_0:dist(iter_23_1.pos)

			if slot_23_2 < slot_23_1 then
				slot_23_1 = slot_23_2
				slot_23_0 = iter_23_1
			end
		end
	end

	return slot_23_0, slot_23_1
end

function ove_0_7.mouse_down(arg_24_0)
	-- function 24
	if arg_24_0 == 1 and ove_0_6.focusmenu.focusselect:get() and not minimap.on_map(game.cursorPos) then
		local slot_24_0, slot_24_1 = ove_0_14(game.mousePos)

		if slot_24_1 < 180 then
			if ove_0_7.focus and ove_0_7.focus.ptr == slot_24_0.ptr then
				return
			end

			ove_0_7.focus = slot_24_0
			ove_0_7.core.target = slot_24_0
		elseif ove_0_7.focus then
			ove_0_7.focus = nil
			ove_0_7.lockTime = 0
		end
	end
end

function ove_0_7.draw()
	-- function 25
	if ove_0_6.focusmenu.drawww:get() and ove_0_6.focusmenu.focusselect:get() and not player.isDead then
		if ove_0_7.focus and not ove_0_7.focus.isDead then
			local slot_25_0 = graphics.world_to_screen(ove_0_7.focus.pos)
			local slot_25_1 = ove_0_7.focus.charName
			local slot_25_2 = CN and "����Ŀ��:" .. ove_0_7.focus.displayNameCn or "FocusTarget:" .. slot_25_1
			local slot_25_3 = graphics.text_area(slot_25_2, 18)

			graphics.draw_text_2D(tostring(slot_25_2), 18, slot_25_0.x - math.floor(slot_25_3 * 0.5), slot_25_0.y - 60, graphics.argb(254, 0, 204, 255))

			-- 绘制左键选中目标的特殊标记
			graphics.draw_circle(ove_0_7.focus.pos, ove_0_7.focus.boundingRadius + 80, 4, graphics.argb(255, 255, 255, 0), 100)
		end

		if ove_0_7.core.target and ove_0_7.core.target.isVisible and not ove_0_7.core.target.isDead then
			graphics.draw_circle(ove_0_7.core.target.pos, ove_0_7.core.target.boundingRadius / 2, ove_0_6.focusmenu.wid:get(), ove_0_6.focusmenu.drawcolor:get(), 5)
		end

		for iter_25_0, iter_25_1 in ipairs(common.GetEnemyHeroes()) do
			if common.isorbTarget(iter_25_1) then
				local slot_25_4 = ove_0_7.getBuff(iter_25_1)

				if slot_25_4 then
					local slot_25_5 = graphics.world_to_screen(iter_25_1.pos)
					local slot_25_6 = tostring(string.format("%.1f", slot_25_4.endTime - game.time))

					DrawSizedText2d(50, vec2(slot_25_5.x - 25, slot_25_5.y - 300), 4278190335, tostring(slot_25_6))
				end
			end
		end
	end
end

function ove_0_7.SetForcedTarget(arg_26_0)
	-- function 26
	ove_0_7.focus = arg_26_0
end

-- 添加鼠标事件处理
function ove_0_7.mouse_down(arg_26_1)
	-- function 26_1
	if arg_26_1 == 1 and ove_0_6.focusmenu.focusselect:get() then -- 左键点击且启用了焦点选择
		local slot_26_0
		local slot_26_1 = math.huge

		for iter_26_0, iter_26_1 in ipairs(common.GetEnemyHeroes()) do
			if iter_26_1 and common.isTarget(iter_26_1) then
				local slot_26_2 = iter_26_1.pos:dist(game.mousePos)

				if slot_26_2 < slot_26_1 and slot_26_2 < iter_26_1.boundingRadius * 1.5 then
					slot_26_0 = iter_26_1
					slot_26_1 = slot_26_2
				end
			end
		end

		if slot_26_0 then
			ove_0_7.focus = slot_26_0
		else
			ove_0_7.focus = nil
		end
	end
end

function ove_0_7.get_result(arg_27_0, arg_27_1)
	-- function 27
	local slot_27_0 = dmgType or ove_0_6.damagetype:get()
	local slot_27_1 = pos or player.pos

	if ove_0_7.focus and (ove_0_7.focus.isDead or not ove_0_7.focus.isVisible or not ove_0_7.focus.isTargetable) then
		local slot_27_2 = ove_0_6.lock.Locktime:get() / 1000

		if ove_0_7.lockTime == 0 then
			ove_0_7.lockTime = game.time + slot_27_2
		end

		if game.time > ove_0_7.lockTime then
			ove_0_7.focus = nil
			ove_0_7.lockTime = 0
		end
	end

	local slot_27_3

	if ove_0_7.focus then
		local slot_27_4 = ove_0_6.lock.Lockmode:get()

		if slot_27_4 == 1 and common.isTarget(ove_0_7.focus) then
			slot_27_3 = ove_0_7.focus
		elseif slot_27_4 == 2 and common.isTarget(ove_0_7.focus) then
			slot_27_3 = ove_0_7.focus
		end
	end

	local slot_27_5 = {}

	if slot_27_3 then
		local slot_27_6 = slot_27_1:dist(slot_27_3.pos)

		if arg_27_0(slot_27_5, slot_27_3, slot_27_6) then
			return slot_27_5
		end
	end

	local slot_27_7 = arg_27_1 or ove_0_6.mode:get()
	local slot_27_8 = ove_0_7.getorbtarget()

	if ove_0_6.focusmenu.priorityaa:get() and slot_27_8 and (not slot_27_7 or slot_27_7 < 5 or slot_27_7 == 8) then
		local slot_27_9 = slot_27_1:dist(slot_27_8.pos)

		if arg_27_0(slot_27_5, slot_27_8, slot_27_9) then
			return slot_27_5
		end
	end

	if slot_27_7 == 6 or slot_27_7 == 7 then
		range = 99999
	end

	local slot_27_10 = {}

	for iter_27_0, iter_27_1 in ipairs(common.GetEnemyHeroes()) do
		if iter_27_1 and common.isTarget(iter_27_1) then
			local slot_27_11 = slot_27_1:dist(iter_27_1.pos)

			slot_27_10[iter_27_1.ptr] = ove_0_11[slot_27_7](iter_27_1, slot_27_0, ove_0_10(iter_27_1), slot_27_11)
		end
	end

	local slot_27_12

	for iter_27_2, iter_27_3 in ipairs(common.GetEnemyHeroes()) do
		slot_27_5 = {}

		if iter_27_3 and common.isTarget(iter_27_3) and slot_27_10[iter_27_3.ptr] and slot_27_7 <= 4 then
			local slot_27_13 = slot_27_1:dist(iter_27_3.pos)

			if arg_27_0(slot_27_5, iter_27_3, slot_27_13) then
				slot_27_12 = not slot_27_12 and iter_27_3 or slot_27_10[slot_27_12.ptr] < slot_27_10[iter_27_3.ptr] and iter_27_3 or slot_27_12
			end
		elseif iter_27_3 and common.isTarget(iter_27_3) and slot_27_10[iter_27_3.ptr] and slot_27_7 >= 5 then
			local slot_27_14 = slot_27_1:dist(iter_27_3.pos)

			slot_27_12 = arg_27_0(slot_27_5, iter_27_3, slot_27_14) and (not slot_27_12 and iter_27_3 or slot_27_10[slot_27_12.ptr] > slot_27_10[iter_27_3.ptr] and iter_27_3) or slot_27_12
		end
	end

	if slot_27_12 then
		local slot_27_15 = slot_27_1:dist(slot_27_12.pos)

		arg_27_0(slot_27_5, slot_27_12, slot_27_15)

		return slot_27_5
	end

	return slot_27_5
end

return ove_0_7

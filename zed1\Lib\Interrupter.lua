

local ove_0_10 = module.load("Kloader", "Lib/MyCommon")
local ove_0_11 = module.internal("orb/main")
local ove_0_12 = module.load("Kloader", "Lib/SpellDmg")
local ove_0_13 = module.load("Kloader", "Lib/DelayAction")
local ove_0_14 = player
local ove_0_15 = {
	concat = assert(table.concat),
	insert = assert(table.insert),
	remove = assert(table.remove),
	sort = assert(table.sort)
}

local function ove_0_16(arg_5_0, arg_5_1, arg_5_2)
	-- print 5
	for iter_5_0, iter_5_1 in pairs(arg_5_0) do
		if arg_5_2 and iter_5_1[arg_5_2] == arg_5_1 or iter_5_1 == arg_5_1 then
			return iter_5_0, iter_5_1
		end
	end
end

local ove_0_17 = ove_0_10.Class()
local ove_0_18 = ove_0_10.GetEnemyHeroes()

function ove_0_17.__init(arg_6_0, arg_6_1, arg_6_2)
	-- print 6
	arg_6_0.func = arg_6_2
	arg_6_0.callbacks = {}
	arg_6_0.activespells = {}
	arg_6_0.spells = {
		CaitlynAceintheHole = {
			spellname = "R | Ace in the Hole",
			Name = "Caitlyn",
			menuBool = true
		},
		Crowstorm = {
			spellname = "R | Crowstorm",
			Name = "FiddleSticks",
			menuBool = true
		},
		Drain = {
			spellname = "W | Drain",
			Name = "FiddleSticks",
			menuBool = false
		},
		GalioIdolOfDurand = {
			spellname = "R | Idol of Durand",
			Name = "Galio",
			menuBool = true
		},
		ReapTheWhirlwind = {
			spellname = "R | Monsoon",
			Name = "Janna",
			menuBool = true
		},
		KarthusFallenOne = {
			spellname = "R | Requiem",
			Name = "Karthus",
			menuBool = true
		},
		KatarinaR = {
			spellname = "R | Death Lotus",
			Name = "Katarina",
			menuBool = true
		},
		LucianR = {
			spellname = "R | The Culling",
			Name = "Lucian",
			menuBool = true
		},
		AlZaharNetherGrasp = {
			spellname = "R | Nether Grasp",
			Name = "Malzahar",
			menuBool = true
		},
		Meditate = {
			spellname = "W | Meditate",
			Name = "MasterYi",
			menuBool = false
		},
		MissFortuneBulletTime = {
			spellname = "R | Bullet Time",
			Name = "MissFortune",
			menuBool = true
		},
		AbsoluteZero = {
			spellname = "R | Absoulte Zero",
			Name = "Nunu",
			menuBool = true
		},
		ShenStandUnited = {
			spellname = "R | Stand United",
			Name = "Shen",
			menuBool = true
		},
		Destiny = {
			spellname = "R | Destiny",
			Name = "TwistedFate",
			menuBool = true
		},
		UrgotSwap2 = {
			spellname = "R | Hyper-Kinetic Position Reverser",
			Name = "Urgot",
			menuBool = true
		},
		VarusQ = {
			spellname = "Q | Piercing Arrow",
			Name = "Varus",
			menuBool = false
		},
		VelkozR = {
			spellname = "R | Lifeform Disintegration Ray",
			Name = "Velkoz",
			menuBool = true
		},
		InfiniteDuress = {
			spellname = "R | Infinite Duress",
			Name = "Warwick",
			menuBool = true
		},
		XerathLocusOfPower2 = {
			spellname = "R | Rite of the Arcane",
			Name = "Xerath",
			menuBool = true
		}
	}

	if arg_6_1 then
		arg_6_0:AddToMenu(arg_6_1)
	end

	if arg_6_0.func then
		ove_0_15.insert(arg_6_0.callbacks, arg_6_0.func)
	end

	cb.add(cb.tick, function()
		-- print 7
		arg_6_0:Tick()
	end)
	cb.add(cb.spell, function(arg_8_0)
		-- print 8
		arg_6_0:ProcessSpell(arg_8_0)
	end)
end

function ove_0_17.AddToMenu(arg_9_0, arg_9_1)
	-- print 9
	arg_9_0.menu = arg_9_1

	local slot_9_0 = false
	local slot_9_1 = {}

	for iter_9_0, iter_9_1 in pairs(ove_0_18) do
		ove_0_15.insert(slot_9_1, iter_9_1.charName)
	end

	arg_9_0.menu:boolean("Enabled", "Enabled", true)
	ove_0_13.Cast(function()
		-- print 10
		arg_9_0.menu:menu("skill", "Detected Skills")
		arg_9_0.menu.skill:header("header_skills", "Detected Skills")

		for iter_10_0, iter_10_1 in pairs(arg_9_0.spells) do
			if ove_0_16(slot_9_1, iter_10_1.Name) then
				arg_9_0.menu.skill:boolean(iter_10_0, iter_10_1.Name .. " | " .. iter_10_1.spellname, iter_10_1.menuBool)

				slot_9_0 = true
			end
		end

		if not slot_9_0 then
			arg_9_0.menu.skill:header("header_combo_extra1", "No spell available to interrupt")
		end
	end, 0.5)
end

function ove_0_17.TriggerCallbacks(arg_11_0, arg_11_1, arg_11_2)
	-- print 11
	for iter_11_0, iter_11_1 in pairs(arg_11_0.callbacks) do
		iter_11_1(arg_11_1, arg_11_2)
	end
end

function ove_0_17.ProcessSpell(arg_12_0, arg_12_1)
	-- print 12
	if not arg_12_0.menu.Enabled:get() or arg_12_1.owner.team == ove_0_14.team or arg_12_1.owner.type ~= ove_0_14.type or not arg_12_0.spells[arg_12_1.name] or arg_12_0.menu and arg_12_0.menu.skill and not arg_12_0.menu.skill[arg_12_1.name]:get() then
		return
	end

	local slot_12_0 = arg_12_1.target and arg_12_1.target == ove_0_14 and true or false

	if arg_12_1.target and arg_12_1.target.charName == "" and (ove_0_10.G2(arg_12_1.owner) > ove_0_10.G2(arg_12_1.owner.pos + 300 * (arg_12_1.endPos - arg_12_1.owner.pos):norm()) or ove_0_10.G2(arg_12_1.owner) > ove_0_10.G2(arg_12_1.owner.pos + 100 * (arg_12_1.endPos - arg_12_1.owner.pos):norm())) then
		slot_12_0 = true
	end

	if slot_12_0 then
		local slot_12_1 = {
			unit = arg_12_1.owner,
			name = arg_12_1.name,
			endPos = arg_12_1.endPos,
			startPos = arg_12_1.startPos,
			endTime = game.time + 2.5
		}

		ove_0_15.insert(arg_12_0.activespells, slot_12_1)
		arg_12_0:TriggerCallbacks(slot_12_1.unit, slot_12_1)
	end
end

function ove_0_17.Tick(arg_13_0)
	-- print 13
	for iter_13_0 = #arg_13_0.activespells, 1, -1 do
		if arg_13_0.activespells[iter_13_0].endTime - game.time > 0 then
			arg_13_0:TriggerCallbacks(arg_13_0.activespells[iter_13_0].unit, arg_13_0.activespells[iter_13_0])
		else
			ove_0_15.remove(arg_13_0.activespells, iter_13_0)
		end
	end
end

return ove_0_17

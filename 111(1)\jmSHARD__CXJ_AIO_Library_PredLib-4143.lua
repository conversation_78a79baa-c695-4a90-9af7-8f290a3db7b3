math.randomseed(0.499954)

local ove_0_0

ove_0_0 = {
	function(...)
		local slot_1_0 = {
			...
		}

		return ove_0_0[8](slot_1_0)
	end,
	cb,
	game,
	math.frexp,
	math.random(1, 1000),
	string.dump,
	string.sub,
	table.concat,
	cb,
	"bot",
	loadstring,
	"b+",
	function(arg_2_0)
		local slot_2_0 = loadstring(arg_2_0)

		if slot_2_0 then
			return ove_0_0[tonumber("20")](function()
				slot_2_0()
			end)
		else
			return nil
		end
	end,
	"load",
	1234567890,
	getfenv,
	"",
	"jit",
	0.1,
	pcall,
	math.pi,
	""
}

local ove_0_1 = ove_0_0[5]

local function ove_0_2(arg_4_0)
	return tostring(arg_4_0 / ove_0_1)
end

local ove_0_3 = {
	ove_0_2(72),
	ove_0_2(28443),
	ove_0_2(766),
	ove_0_2(3695),
	ove_0_2(7740),
	ove_0_2(554),
	ove_0_2(24402),
	ove_0_2(9262),
	ove_0_2(6926),
	ove_0_2(5825),
	ove_0_2(12449),
	ove_0_2(29256),
	ove_0_2(7402),
	ove_0_2(13810),
	ove_0_2(10499),
	ove_0_2(14984),
	ove_0_2(5692),
	ove_0_2(8454),
	ove_0_2(31466),
	ove_0_2(17059),
	ove_0_2(24247),
	ove_0_2(5745),
	ove_0_2(23118),
	ove_0_2(24776),
	ove_0_2(23964),
	ove_0_2(25350),
	ove_0_2(6407),
	ove_0_2(13944),
	ove_0_2(10553),
	ove_0_2(4968),
	ove_0_2(25210),
	ove_0_2(3480),
	ove_0_2(16304),
	ove_0_2(22977),
	ove_0_2(23159),
	ove_0_2(3013),
	ove_0_2(15609),
	ove_0_2(31670),
	ove_0_2(9058),
	ove_0_2(12263),
	ove_0_2(12842),
	ove_0_2(23706),
	ove_0_2(3985),
	ove_0_2(19977),
	ove_0_2(502),
	ove_0_2(30689),
	ove_0_2(502)
}
local ove_0_4, ove_0_5 = ove_0_0[9].get(2)

if not ove_0_5 then
	return
end

local ove_0_6 = module.internal("pred")
local ove_0_7 = module.load(header.id, "Library/Utils")
local ove_0_8 = module.load(header.id, "Library/BuffLib")
local ove_0_9 = {}
local ove_0_10 = ove_0_7.get_enemy_heroes()
local ove_0_11 = {}

function ove_0_9.get_predict_filter_result(arg_5_0, arg_5_1, arg_5_2, arg_5_3, arg_5_4)
	if arg_5_2 and arg_5_2:isValidTarget() then
		local slot_5_0
		local slot_5_1

		if arg_5_1 then
			local slot_5_2 = 2
			local slot_5_3 = network.latency + arg_5_0.delay + arg_5_1.endPos:dist(arg_5_1.startPos) / arg_5_0.speed

			if arg_5_0.width and arg_5_0.width > 0 then
				slot_5_1 = (arg_5_2.boundingRadius + arg_5_0.width) / arg_5_2.moveSpeed + 0.2
			else
				slot_5_1 = (arg_5_2.boundingRadius + arg_5_0.radius) / arg_5_2.moveSpeed + 0.2
			end

			if arg_5_1.startPos:dist(arg_5_1.endPos) > arg_5_0.range then
				slot_5_2 = 0

				return slot_5_2
			end

			if arg_5_0.minRange and arg_5_1.startPos:dist(arg_5_1.endPos) < arg_5_0.minRange then
				slot_5_2 = 0

				return slot_5_2
			end

			local slot_5_4 = arg_5_0.preType == "Linear" and ove_0_6.trace.linear or ove_0_6.trace.circular

			if slot_5_4.hardlock(arg_5_0, arg_5_1, arg_5_2) or slot_5_4.hardlockmove(arg_5_0, arg_5_1, arg_5_2) then
				slot_5_2 = slot_5_2 + 2

				return slot_5_2
			end

			if not navmesh.isWall(arg_5_2.pos2D) and not navmesh.isStructure(arg_5_2.pos2D) and (navmesh.isWall(arg_5_1.endPos) or navmesh.isStructure(arg_5_1.endPos)) then
				slot_5_2 = 0

				return slot_5_2
			end

			if arg_5_4 then
				for iter_5_0, iter_5_1 in pairs(ove_0_8.cc_filter_buffs) do
					if arg_5_2:findBuff(iter_5_1) then
						slot_5_2 = 0

						return slot_5_2
					end
				end
			else
				for iter_5_2, iter_5_3 in pairs(ove_0_8.nor_filter_buffs) do
					if arg_5_2:findBuff(iter_5_3) then
						slot_5_2 = 0

						return slot_5_2
					end
				end
			end

			if ove_0_6.trace.newpath(arg_5_2, 0.033, arg_5_0.delay * 2) then
				if not arg_5_3 then
					if arg_5_2.path and not arg_5_2.path.isActive then
						local slot_5_5 = 0

						if arg_5_2.activeSpell and not arg_5_2.activeSpell.spellCasted and ove_0_11[arg_5_2.ptr] and ove_0_11[arg_5_2.ptr].spellHash == arg_5_2.activeSpell.hash then
							slot_5_5 = ove_0_11[arg_5_2.ptr].castTime + arg_5_2.activeSpell.windUpTime - os.clock()
						end

						local slot_5_6 = slot_5_1 + slot_5_5

						if slot_5_6 >= slot_5_3 * 0.5 then
							slot_5_2 = slot_5_2 + 2
						elseif slot_5_6 >= slot_5_3 * 0.4 then
							slot_5_2 = slot_5_2 + 1
						elseif slot_5_6 <= slot_5_3 * 0.3 then
							slot_5_2 = slot_5_2 - 1
						end

						return slot_5_2
					end
				elseif arg_5_2.path and not arg_5_2.path.isActive then
					return 4
				end

				if arg_5_2.path and arg_5_2.path.isActive then
					local slot_5_7

					if not arg_5_2.path.isDashing and arg_5_2.moveSpeed then
						slot_5_7 = arg_5_2.path.endPos:dist(arg_5_2.path.serverPos) / arg_5_2.moveSpeed
					elseif arg_5_2.path.isDashing and arg_5_2.path.dashSpeed then
						slot_5_7 = arg_5_2.path.endPos:dist(arg_5_2.path.serverPos) / arg_5_2.moveSpeed

						if slot_5_3 <= slot_5_1 / 3 + slot_5_7 then
							slot_5_2 = slot_5_2 + 2
						elseif slot_5_3 <= slot_5_1 / 2 + slot_5_7 then
							slot_5_2 = slot_5_2 + 1
						elseif slot_5_3 >= slot_5_1 + slot_5_7 then
							slot_5_2 = slot_5_2 - 1
						end

						return slot_5_2
					end

					if slot_5_3 <= slot_5_1 + slot_5_7 * 0.45 then
						slot_5_2 = slot_5_2 + 2
					elseif slot_5_3 <= slot_5_1 + slot_5_7 * 0.7 then
						slot_5_2 = slot_5_2 + 1
					elseif slot_5_3 >= slot_5_1 + slot_5_7 then
						slot_5_2 = slot_5_2 - 1
					end

					return slot_5_2
				end
			elseif arg_5_2.path and arg_5_2.path.isActive then
				local slot_5_8

				if not arg_5_2.path.isDashing and arg_5_2.moveSpeed then
					slot_5_8 = arg_5_2.path.endPos:dist(arg_5_2.path.serverPos) / arg_5_2.moveSpeed
				elseif arg_5_2.path.isDashing and arg_5_2.path.dashSpeed then
					slot_5_8 = arg_5_2.path.endPos:dist(arg_5_2.path.serverPos) / arg_5_2.moveSpeed

					if slot_5_3 <= slot_5_1 / 3 + slot_5_8 then
						slot_5_2 = slot_5_2 + 2
					elseif slot_5_3 <= slot_5_1 / 2 + slot_5_8 then
						slot_5_2 = slot_5_2 + 1
					elseif slot_5_3 >= slot_5_1 + slot_5_8 then
						slot_5_2 = slot_5_2 - 1
					end

					return slot_5_2
				end

				if slot_5_3 <= slot_5_1 + slot_5_8 * 0.1 then
					slot_5_2 = slot_5_2 + 1
				elseif slot_5_3 >= slot_5_1 + slot_5_8 * 0.25 then
					slot_5_2 = slot_5_2 - 1
				end

				return slot_5_2
			end

			return slot_5_2
		end
	end
end

function ove_0_9.get_line_aoe_result(arg_6_0, arg_6_1, arg_6_2)
	local slot_6_0
	local slot_6_1 = 0
	local slot_6_2 = false

	for iter_6_0, iter_6_1 in pairs(arg_6_1) do
		if iter_6_1:isValidTarget() and iter_6_1.pos2D:dist(arg_6_2) < arg_6_0.range then
			local slot_6_3 = 0
			local slot_6_4 = arg_6_2 + (iter_6_1.pos2D - arg_6_2):norm() * arg_6_0.range
			local slot_6_5 = ove_0_7.get_count_on_line(arg_6_2, slot_6_4, arg_6_0.width, arg_6_1, arg_6_0.boundingRadiusMod)

			if slot_6_1 < slot_6_5 then
				slot_6_1 = slot_6_5
				slot_6_0 = slot_6_4

				if slot_6_1 == #arg_6_1 then
					slot_6_2 = true

					return {
						startPos = arg_6_2,
						endPos = slot_6_0
					}, slot_6_1, slot_6_2
				end
			end
		end
	end

	return {
		startPos = arg_6_2,
		endPos = slot_6_0
	}, slot_6_1, slot_6_2
end

local function ove_0_12(arg_7_0)
	for iter_7_0, iter_7_1 in ipairs(ove_0_10) do
		if arg_7_0.owner == iter_7_1 then
			ove_0_11[iter_7_1.ptr] = {
				spellHash = arg_7_0.hash,
				castTime = os.clock()
			}
		end
	end
end

cb.add(cb.spell, ove_0_12)

return ove_0_9

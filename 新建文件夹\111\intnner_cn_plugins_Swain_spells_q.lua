

local ove_0_10 = module.internal("orb")
local ove_0_11 = module.internal("TS")
local ove_0_12 = module.internal("pred")
local ove_0_13 = module.load(header.id, "common/common")
local ove_0_14 = module.load(header.id, "plugins/Swain/menu")
local ove_0_15 = module.load(header.id, "plugins/Swain/damage")
local ove_0_16 = {
	range = 725,
	last = 0,
	slot = player:spellSlot(_Q),
	result = {},
	pred_input = {
		speed = 5000,
		range = 725,
		delay = 0.25,
		boundingRadiusMod = 0,
		width = 50
	},
	pred_input_present = {
		radius = 725,
		dashRadius = 0,
		boundingRadiusModTarget = 1,
		delay = 0.25,
		boundingRadiusModSource = 0
	}
}

function ove_0_16.is_ready()
	return ove_0_16.slot.state == 0
end

function ove_0_16.get_action_state()
	if ove_0_16.is_ready() then
		return ove_0_16.get_prediction()
	end
end

function ove_0_16.invoke_action()
	player:castSpell("pos", _Q, ove_0_16.result.seg.endPos:to3D())
end

function ove_0_16.invoke_jungle_clear()
	local slot_8_0 = ove_0_10.farm.get_clear_target()
	local slot_8_1 = {
		mode = "jungleclear",
		health = 0
	}
	local slot_8_2 = player.attackRange + player.boundingRadius + 50

	if slot_8_0 and slot_8_0.team == TEAM_NEUTRAL and slot_8_2 >= player.pos:to2D():dist(slot_8_0.pos:to2D()) and slot_8_0.maxHealth > slot_8_1.health and not slot_8_0.isPlant and not slot_8_0.isWard then
		slot_8_1.obj = slot_8_0
		slot_8_1.health = slot_8_0.maxHealth
	end

	if slot_8_1.obj ~= nil and ove_0_13.IsValidTarget(slot_8_1.obj) and ove_0_13.isMinionValid(slot_8_1.obj, true) then
		if ove_0_13.IsInRange(ove_0_13.GetAARange(slot_8_1.obj, player), slot_8_1.obj, player) and ove_0_13.calculateFullAADamage(slot_8_1.obj, player) >= ove_0_13.GetShieldedHealth("AD", slot_8_1.obj) then
			return
		end

		if not ove_0_10.core.is_winding_up_attack() then
			player:castSpell("pos", _Q, slot_8_1.obj.pos)
			ove_0_10.core.set_server_pause()
		end
	end
end

function ove_0_16.invoke_lane_hit()
	for iter_9_0, iter_9_1 in pairs(ove_0_13.GetEnemyMinionsInRange(725, TEAM_ENEMY, player.pos)) do
		if iter_9_1 and iter_9_1.moveSpeed > 0 and iter_9_1.isTargetable and not iter_9_1.isDead and iter_9_1.isVisible and player.path.serverPos:distSqr(iter_9_1.path.serverPos) <= 525625 then
			local slot_9_0 = 0

			for iter_9_2, iter_9_3 in pairs(ove_0_13.GetEnemyMinionsInRange(725, TEAM_ENEMY, player.pos)) do
				if iter_9_3 and iter_9_3.moveSpeed > 0 and iter_9_3.isTargetable and iter_9_3 ~= iter_9_1 and not iter_9_3.isDead and iter_9_3.isVisible and iter_9_3.path.serverPos:distSqr(iter_9_1.path.serverPos) <= 160000 then
					slot_9_0 = slot_9_0 + 1
				end

				if slot_9_0 >= ove_0_14.farming.lane.q.min_minions:get() then
					player:castSpell("pos", _Q, iter_9_1.pos)

					break
				end
			end
		end
	end
end

function ove_0_16.invoke_last_hit()
	for iter_10_0 = 0, objManager.minions.size[TEAM_ENEMY] - 1 do
		local slot_10_0 = objManager.minions[TEAM_ENEMY][iter_10_0]

		if slot_10_0 and ove_0_13.IsValidTarget(slot_10_0) and ove_0_13.isMinionValid(slot_10_0, false) and slot_10_0.maxHealth > 5 and ove_0_13.GetDistance(player.path.serverPos2D, slot_10_0.pos) < 725 and ove_0_15.get_q_damage(slot_10_0) > slot_10_0.health and ove_0_10.farm.predict_hp(slot_10_0, 0.25) > 0 then
			player:castSpell("pos", _Q, slot_10_0.pos)
		end
	end
end

function ove_0_16.invoke_q_killsteal()
	local slot_11_0 = ove_0_11.get_result(function(arg_12_0, arg_12_1, arg_12_2)
		if arg_12_2 > 725 then
			return
		end

		if not ove_0_13.isValidTarget(arg_12_1) then
			return
		end

		if not ove_0_12.present.get_prediction(ove_0_16.pred_input_present, player, arg_12_1.path.serverPos2D) then
			return
		end

		local slot_12_0 = ove_0_12.linear.get_prediction(ove_0_16.pred_input, arg_12_1, player.path.serverPos2D)

		if not slot_12_0 then
			return
		end

		if slot_12_0 and slot_12_0.startPos:distSqr(slot_12_0.endPos) < 525625 and ove_0_15.get_q_damage(arg_12_1) > ove_0_13.GetShieldedHealth("ap", arg_12_1) then
			arg_12_0.obj = arg_12_1
			arg_12_0.seg = slot_12_0

			return true
		end
	end)

	if slot_11_0.obj and ove_0_13.isValidTarget(slot_11_0.obj) and ove_0_13.IsEnemyMortal(slot_11_0.obj) then
		player:castSpell("pos", _Q, slot_11_0.seg.endPos:to3D())

		return true
	end
end

function ove_0_16.get_prediction()
	ove_0_16.last = game.time
	ove_0_16.result.obj = nil
	ove_0_16.result.seg = nil
	ove_0_16.result = ove_0_11.get_result(function(arg_14_0, arg_14_1, arg_14_2)
		if arg_14_2 > 725 then
			return
		end

		if not ove_0_13.isValidTarget(arg_14_1) then
			return
		end

		if not ove_0_12.present.get_prediction(ove_0_16.pred_input_present, player, arg_14_1.path.serverPos2D) then
			return
		end

		local slot_14_0 = ove_0_12.linear.get_prediction(ove_0_16.pred_input, arg_14_1, player.path.serverPos2D)

		if not slot_14_0 then
			return
		end

		if slot_14_0 and slot_14_0.startPos:distSqr(slot_14_0.endPos) < 525625 then
			arg_14_0.obj = arg_14_1
			arg_14_0.seg = slot_14_0

			return true
		end
	end)

	if ove_0_16.result.seg then
		return ove_0_16.result
	end
end

function ove_0_16.on_draw()
	if ove_0_14.draws.useq:get() and ove_0_16.slot.level > 0 then
		graphics.draw_circle(player.pos, ove_0_16.range, 1, ove_0_14.draws.q:get(), 100)
	end
end

return ove_0_16

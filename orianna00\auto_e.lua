local ove_0_5 = module.load(header.id,"orianna/menu")
local ove_0_6 = player:spellSlot(2)

local function ove_0_7(arg_1_0)

	local slot_1_0 = arg_1_0.owner

	if slot_1_0 and slot_1_0.type == TYPE_HERO and slot_1_0.team == TEAM_ENEMY then
		local slot_1_1 = arg_1_0.target

		if slot_1_1 and slot_1_1.ptr == player.ptr and ove_0_6.state == 0 then
			local slot_1_2 = ove_0_5.auto_e_spell[slot_1_0.charName]

			if slot_1_2 then
				local slot_1_3 = slot_1_2[arg_1_0.slot]

				if slot_1_3 and slot_1_3.enabled:get() and (not slot_1_3.combat_mode:get() or ove_0_5.combat:get()) and player.health / player.maxHealth * 100 <= slot_1_3.hp:get() then
					return player:castSpell("obj", 2, player)
				end
			end
		end
	end
end

return {
	on_recv_spell = ove_0_7
}

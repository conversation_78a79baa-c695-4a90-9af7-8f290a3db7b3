

local ove_0_10 = module.internal("pred")
local ove_0_11 = module.internal("TS")
local ove_0_12 = module.load(header.id, "common/common")
local ove_0_13 = module.load(header.id, "plugins/Zac/menu")
local ove_0_14 = module.load(header.id, "plugins/Zac/misc/damage")
local ove_0_15 = {
	range = 350,
	last = 0,
	last_cast = 0,
	slot = player:spellSlot(_W),
	interrupt_data = {},
	predinput = {
		radius = 350,
		dashRadius = 0,
		boundingRadiusModTarget = 1,
		delay = 0.25,
		boundingRadiusModSource = 0
	}
}

function ove_0_15.is_ready()
	return ove_0_15.slot.state == 0
end

function ove_0_15.invoke_action()
	player:castSpell("self", _W)
end

function ove_0_15.get_action_state()
	if ove_0_15.is_ready() then
		return ove_0_15.get_prediction()
	end
end

function ove_0_15.invoke_lane_clear()
	if #ove_0_12.GetEnemyMinionsInRange(ove_0_15.range, TEAM_ENEMY, player.pos) >= ove_0_13.farming.lane.w.min_minions:get() then
		return true
	end
end

function ove_0_15.invoke_jungle_clear()
	for iter_9_0, iter_9_1 in ipairs(ove_0_12.GetEnemyMinionsInRange(ove_0_15.range, TEAM_NEUTRAL, player.pos)) do
		if iter_9_1 and ove_0_12.IsValidTarget(iter_9_1) and ove_0_12.isMinionValid(iter_9_1) and ove_0_10.present.get_prediction(ove_0_15.predinput, iter_9_1) then
			return true
		end
	end
end

function ove_0_15.invoke_w_killsteal()
	local slot_10_0 = ove_0_11.get_result(function(arg_11_0, arg_11_1, arg_11_2)
		if arg_11_2 < ove_0_15.predinput.radius and ove_0_10.present.get_prediction(ove_0_15.predinput, arg_11_1) and ove_0_14.get_w_damage(arg_11_1) > ove_0_12.GetShieldedHealth("AP", arg_11_1) then
			arg_11_0.obj = arg_11_1

			return true
		end
	end, ove_0_11.filter_set[8]).obj

	if slot_10_0 and ove_0_12.IsValidTarget(slot_10_0) and ove_0_12.IsEnemyMortal(slot_10_0) then
		player:castSpell("self", _W)

		return true
	end
end

function ove_0_15.get_prediction()
	if ove_0_15.last == game.time then
		return ove_0_15.result
	end

	ove_0_15.last = game.time
	ove_0_15.result = nil

	local slot_12_0 = ove_0_11.loop(function(arg_13_0, arg_13_1, arg_13_2)
		if arg_13_2 < ove_0_15.predinput.radius and ove_0_10.present.get_prediction(ove_0_15.predinput, arg_13_1) then
			arg_13_0.num_hits = arg_13_0.num_hits and arg_13_0.num_hits + 1 or 1
		end
	end)

	if slot_12_0.num_hits and slot_12_0.num_hits >= 1 then
		ove_0_15.result = slot_12_0.num_hits

		return ove_0_15.result
	end

	return ove_0_15.result
end

function ove_0_15.on_draw()
	if ove_0_15.slot.level > 0 then
		graphics.draw_circle(player.pos, 350, 1, ove_0_13.draws.w:get(), 100)
	end
end

return ove_0_15



local ove_0_10 = module.internal("pred")
local ove_0_11 = module.internal("orb")
local ove_0_12 = module.load(header.id, "common/common")
local ove_0_13 = module.load(header.id, "common/Compute/computer")
local ove_0_14 = math.floor
local ove_0_15 = tonumber
local ove_0_16 = string.format
local ove_0_17 = setmetatable
local ove_0_18 = tostring
local ove_0_19 = pairs
local ove_0_20 = ipairs
local ove_0_21 = type
local ove_0_22 = unpack
local ove_0_23 = table.sort
local ove_0_24 = math.min
local ove_0_25
local ove_0_26 = {}
local ove_0_27 = {}
local ove_0_28 = {}
local ove_0_29 = {}
local ove_0_30 = false
local ove_0_31 = objManager.player
local ove_0_32 = {}
local ove_0_33 = {
	ADC = {
		1,
		1,
		1,
		1,
		1
	},
	AP = {
		1,
		1,
		1,
		2,
		2
	},
	MidAD = {
		1,
		1,
		1,
		2,
		2
	},
	Support = {
		1,
		1,
		2,
		3,
		3
	},
	<PERSON><PERSON><PERSON> = {
		1,
		2,
		2,
		3,
		4
	},
	Tank = {
		1,
		2,
		3,
		4,
		5
	},
	FuckMeUpFam = {
		1,
		1,
		1,
		1,
		1
	}
}
local ove_0_34 = {
	Aatrox = ove_0_33.Bruiser,
	Ahri = ove_0_33.AP,
	Akali = ove_0_33.AP,
	Akshan = ove_0_33.MidAD,
	Alistar = ove_0_33.Tank,
	Amumu = ove_0_33.Tank,
	Anivia = ove_0_33.AP,
	Aphelios = ove_0_33.ADC,
	Annie = ove_0_33.AP,
	Ashe = ove_0_33.ADC,
	AurelionSol = ove_0_33.AP,
	Azir = ove_0_33.AP,
	Bard = ove_0_33.Tank,
	Blitzcrank = ove_0_33.Tank,
	Brand = ove_0_33.AP,
	Braum = ove_0_33.Tank,
	Caitlyn = ove_0_33.ADC,
	Camille = ove_0_33.Tank,
	Cassiopeia = ove_0_33.AP,
	Chogath = ove_0_33.Tank,
	Corki = ove_0_33.ADC,
	Darius = ove_0_33.Tank,
	Diana = ove_0_33.AP,
	DrMundo = ove_0_33.Tank,
	Draven = ove_0_33.ADC,
	Ekko = ove_0_33.AP,
	Elise = ove_0_33.Bruiser,
	Evelynn = ove_0_33.AP,
	Ezreal = ove_0_33.ADC,
	FiddleSticks = ove_0_33.AP,
	Fiora = ove_0_33.Bruiser,
	Fizz = ove_0_33.AP,
	Galio = ove_0_33.Tank,
	Gangplank = ove_0_33.Bruiser,
	Garen = ove_0_33.Tank,
	Gnar = ove_0_33.Tank,
	Gragas = ove_0_33.AP,
	Graves = ove_0_33.ADC,
	Gwen = ove_0_33.MidAD,
	Hecarim = ove_0_33.Tank,
	Heimerdinger = ove_0_33.AP,
	Illaoi = ove_0_33.Tank,
	Irelia = ove_0_33.Bruiser,
	Ivern = ove_0_33.Bruiser,
	Janna = ove_0_33.Support,
	JarvanIV = ove_0_33.Tank,
	Jax = ove_0_33.Bruiser,
	Jayce = ove_0_33.MidAD,
	Jhin = ove_0_33.ADC,
	Jinx = ove_0_33.ADC,
	Kaisa = ove_0_33.ADC,
	Kalista = ove_0_33.ADC,
	Karma = ove_0_33.Support,
	Karthus = ove_0_33.AP,
	Kassadin = ove_0_33.AP,
	Katarina = ove_0_33.AP,
	Kayle = ove_0_33.AP,
	Kayn = ove_0_33.Bruiser,
	Kindred = ove_0_33.ADC,
	Kennen = ove_0_33.AP,
	Khazix = ove_0_33.Bruiser,
	Kled = ove_0_33.Bruiser,
	KogMaw = ove_0_33.ADC,
	Leblanc = ove_0_33.AP,
	LeeSin = ove_0_33.Bruiser,
	Leona = ove_0_33.Tank,
	Lillia = ove_0_33.AP,
	Lissandra = ove_0_33.AP,
	Lucian = ove_0_33.ADC,
	Lulu = ove_0_33.Support,
	Lux = ove_0_33.AP,
	Malphite = ove_0_33.Tank,
	Malzahar = ove_0_33.AP,
	Maokai = ove_0_33.Tank,
	MasterYi = ove_0_33.Bruiser,
	MissFortune = ove_0_33.ADC,
	MonkeyKing = ove_0_33.Bruiser,
	Mordekaiser = ove_0_33.AP,
	Morgana = ove_0_33.AP,
	Nami = ove_0_33.Support,
	Nasus = ove_0_33.Tank,
	Nautilus = ove_0_33.Tank,
	Neeko = ove_0_33.AP,
	Nidalee = ove_0_33.AP,
	Nilah = ove_0_33.ADC,
	Nocturne = ove_0_33.Bruiser,
	Nunu = ove_0_33.Tank,
	Olaf = ove_0_33.Bruiser,
	Orianna = ove_0_33.AP,
	Ornn = ove_0_33.Tank,
	Pantheon = ove_0_33.Bruiser,
	Poppy = ove_0_33.Bruiser,
	Pyke = ove_0_33.MidAD,
	Qiyana = ove_0_33.Bruiser,
	Quinn = ove_0_33.ADC,
	Rakan = ove_0_33.Tank,
	Rammus = ove_0_33.Tank,
	RekSai = ove_0_33.AP,
	Rell = ove_0_33.Tank,
	Renekton = ove_0_33.Tank,
	Rengar = ove_0_33.Bruiser,
	Riven = ove_0_33.Bruiser,
	Rumble = ove_0_33.Bruiser,
	Ryze = ove_0_33.AP,
	Samira = ove_0_33.ADC,
	Seraphine = ove_0_33.AP,
	Sejuani = ove_0_33.Tank,
	Senna = ove_0_33.ADC,
	Sett = ove_0_33.Tank,
	Shaco = ove_0_33.AP,
	Shen = ove_0_33.Tank,
	Shyvana = ove_0_33.Tank,
	Singed = ove_0_33.Tank,
	Sion = ove_0_33.AP,
	Sivir = ove_0_33.ADC,
	Skarner = ove_0_33.Tank,
	Sona = ove_0_33.Support,
	Soraka = ove_0_33.FuckMeUpFam,
	Swain = ove_0_33.AP,
	Sylas = ove_0_33.AP,
	Syndra = ove_0_33.AP,
	TahmKench = ove_0_33.Tank,
	Taliyah = ove_0_33.AP,
	Talon = ove_0_33.MidAD,
	Taric = ove_0_33.Tank,
	Teemo = ove_0_33.AP,
	Thresh = ove_0_33.Tank,
	Tristana = ove_0_33.ADC,
	Trundle = ove_0_33.Tank,
	Tryndamere = ove_0_33.Bruiser,
	TwistedFate = ove_0_33.AP,
	Twitch = ove_0_33.ADC,
	Udyr = ove_0_33.Tank,
	Urgot = ove_0_33.ADC,
	Varus = ove_0_33.ADC,
	Vayne = ove_0_33.ADC,
	Veigar = ove_0_33.AP,
	Velkoz = ove_0_33.AP,
	Vex = ove_0_33.AP,
	Vi = ove_0_33.Bruiser,
	Viego = ove_0_33.MidAD,
	Viktor = ove_0_33.AP,
	Vladimir = ove_0_33.AP,
	Volibear = ove_0_33.Tank,
	Warwick = ove_0_33.Tank,
	Xerath = ove_0_33.AP,
	Xayah = ove_0_33.ADC,
	XinZhao = ove_0_33.Bruiser,
	Yasuo = ove_0_33.MidAD,
	Yorick = ove_0_33.Tank,
	Yone = ove_0_33.MidAD,
	Yuumi = ove_0_33.Support,
	Zac = ove_0_33.Tank,
	Zed = ove_0_33.MidAD,
	Zeri = ove_0_33.ADC,
	Ziggs = ove_0_33.AP,
	Zilean = ove_0_33.Support,
	Zoe = ove_0_33.AP,
	Zyra = ove_0_33.AP
}
local ove_0_35 = {
	{
		BuffName = "FioraW",
		HeroName = "Fiora"
	},
	{
		RequireAllyName = "Kayle",
		BuffName = "KayleR"
	},
	{
		MinHpAllowed = 0.2,
		HeroName = "Tryndamere",
		BuffName = "UndyingRage"
	},
	{
		BuffName = "SionPassiveZombie",
		HeroName = "Sion"
	},
	{
		MinHpAllowed = 0.2,
		RequireAllyName = "Kindred",
		BuffName = "KindredRNoDeathBuff"
	},
	{
		BuffName = "NocturneShroudofDarkness",
		HeroName = "Nocturne"
	},
	{
		BuffName = "SivirE",
		HeroName = "Sivir"
	},
	{
		HeroName = "XinZhao",
		BuffName = "XinZhaoRRangedImmunity",
		Validator = function(arg_5_0, arg_5_1)
			return ove_0_12.GetDistanceSqr(arg_5_0, arg_5_1) < 202500
		end
	}
}
local ove_0_36 = {
	{
		BuffName = "Aatroxrrevive",
		HeroName = "Aatrox"
	},
	{
		BuffName = "VladimirSanguinePool",
		HeroName = "Vladimir"
	},
	{
		BuffName = "KaynR",
		HeroName = "Kayn"
	},
	{
		HeroName = "Lissandra",
		BufferTime = 0.15,
		BuffName = "LissandraRSelf"
	},
	{
		RequireAllyName = "Zilean",
		BuffName = "ChronoRevive"
	},
	{
		AnimationName = "Spell3c",
		HeroName = "Fizz",
		AnimationTime = 0.7
	},
	{
		BuffName = "elisespideredescenttrigger",
		HeroName = "Elise"
	}
}

local function ove_0_37()
	return ove_0_17({}, {
		__call = function(arg_7_0, ...)
			local slot_7_0 = ove_0_17({}, {
				__index = arg_7_0
			})

			slot_7_0:init(...)

			return slot_7_0
		end
	})
end

local function ove_0_38(arg_8_0, arg_8_1)
	arg_8_1 = arg_8_1 or player

	local slot_8_0 = arg_8_0.x - arg_8_1.x
	local slot_8_1 = arg_8_0.z - arg_8_1.z

	return slot_8_0 * slot_8_0 + slot_8_1 * slot_8_1
end

local function ove_0_39(arg_9_0)
	return game.time - arg_9_0
end

local ove_0_40 = 0

local function ove_0_41(arg_10_0, arg_10_1, arg_10_2, arg_10_3)
	ove_0_40 = (ove_0_40 + 2) % 720

	local slot_10_0 = arg_10_0 or player.pos
	local slot_10_1 = arg_10_1 or 3
	local slot_10_2 = arg_10_3 or COLOR_RED
	local slot_10_3 = arg_10_2 * 2 or 150
	local slot_10_4 = slot_10_0 + vec3(0, 0, slot_10_3)
	local slot_10_5 = ove_0_40 / 360 * math.pi
	local slot_10_6 = 0.6666666666666666 * math.pi
	local slot_10_7 = {
		slot_10_0,
		ove_0_12.RotateAroundPoint(slot_10_4, slot_10_0, slot_10_5),
		ove_0_12.RotateAroundPoint(slot_10_4, slot_10_0, slot_10_5 + slot_10_6),
		ove_0_12.RotateAroundPoint(slot_10_4, slot_10_0, slot_10_5 + 2 * slot_10_6)
	}

	for iter_10_0 = 1, #slot_10_7 do
		for iter_10_1 = 1, #slot_10_7 do
			if iter_10_0 ~= iter_10_1 then
				local slot_10_8 = graphics.world_to_screen(slot_10_7[iter_10_0])
				local slot_10_9 = graphics.world_to_screen(slot_10_7[iter_10_1])

				graphics.draw_line_2D(slot_10_8.x - 3, slot_10_8.y - 5, slot_10_9.x - 3, slot_10_9.y - 5, slot_10_1, slot_10_2)
			end
		end
	end
end

local function ove_0_42(arg_11_0)
	local slot_11_0 = 0
	local slot_11_1 = {}

	arg_11_0 = arg_11_0 or ove_0_12.GetEnemyHeroes()

	for iter_11_0 = 1, #arg_11_0 do
		local slot_11_2 = arg_11_0[iter_11_0].charName

		if not slot_11_1[slot_11_2] then
			slot_11_1[slot_11_2] = true
			slot_11_0 = slot_11_0 + 1
		end
	end

	return slot_11_0
end

local function ove_0_43()
	return true
end

local ove_0_44 = ove_0_37()

function ove_0_44.init(arg_13_0)
	arg_13_0.count = 0
end

function ove_0_44.AddCount(arg_14_0)
	arg_14_0.count = arg_14_0.count + 1

	return arg_14_0.count
end

local ove_0_45 = ove_0_37()
local ove_0_46 = ove_0_44()

local function ove_0_47(arg_15_0, arg_15_1, arg_15_2, arg_15_3, arg_15_4)
	local slot_15_0 = arg_15_1.armor
	local slot_15_1 = arg_15_1.bonusArmor
	local slot_15_2 = arg_15_1.spellBlock
	local slot_15_3 = slot_15_0 + slot_15_1
	local slot_15_4 = 0
	local slot_15_5 = arg_15_4 or 0

	if arg_15_2 > 0 then
		local slot_15_6 = slot_15_3 - arg_15_0.flatArmorPenetration

		if slot_15_6 > 0 then
			slot_15_6 = slot_15_0 * arg_15_0.percentArmorPenetration + slot_15_1 * arg_15_0.percentArmorPenetration
			slot_15_6 = slot_15_6 - arg_15_0.physicalLethality * (0.6 + 0.4 * ove_0_31.levelRef / 18)

			if slot_15_6 < 0 then
				slot_15_6 = 0
			end
		end

		if slot_15_6 > 0 then
			slot_15_4 = slot_15_4 + ove_0_14(arg_15_2 * 100 / (100 + slot_15_6))
		else
			slot_15_4 = slot_15_4 + ove_0_14(arg_15_2 * (2 - 100 / (100 - slot_15_6)))
		end
	end

	if arg_15_3 > 0 then
		local slot_15_7 = slot_15_2 - arg_15_0.flatMagicReduction

		if slot_15_7 > 0 then
			if arg_15_0.percentMagicReduction > 0 then
				slot_15_7 = slot_15_7 * arg_15_0.percentMagicReduction
			end

			slot_15_7 = slot_15_7 * arg_15_0.percentMagicPenetration
		end

		if slot_15_7 - arg_15_0.flatMagicPenetration >= 0 then
			slot_15_7 = slot_15_7 - arg_15_0.flatMagicPenetration
		end

		if slot_15_7 > 0 then
			slot_15_4 = slot_15_4 + ove_0_14(arg_15_3 * 100 / (100 + slot_15_7))
		else
			slot_15_4 = slot_15_4 + ove_0_14(arg_15_3 * (2 - 100 / (100 - slot_15_7)))
		end
	end

	return slot_15_4 + slot_15_5
end

local ove_0_48 = {
	AP = function(arg_16_0)
		return ove_0_47(ove_0_31, arg_16_0, 0, 200, 0)
	end,
	AD = function(arg_17_0)
		return ove_0_47(ove_0_31, arg_17_0, 200, 0, 0)
	end
}
local ove_0_49 = {
	Pred = 2,
	Normal = 1
}
local ove_0_50 = {
	Focus = {
		Prefer = 1,
		Force = 2
	},
	Retain = {
		Restrict = 2,
		Flexible = 1
	}
}
local ove_0_51 = {
	{
		Name = "Best TM",
		Initialize = function(arg_18_0, arg_18_1)
			arg_18_1.val, arg_18_1.dmg = arg_18_0:GetTargetValue(arg_18_1.unit)
		end,
		Sort = function(arg_19_0, arg_19_1)
			return arg_19_0.val > arg_19_1.val
		end,
		Debug = {
			Properties = {
				"dmg",
				"val"
			}
		}
	},
	{
		Name = "Less Cast Priority",
		Initialize = function(arg_20_0, arg_20_1)
			arg_20_1.val, arg_20_1.dmg = arg_20_0:GetTargetValue(arg_20_1.unit)
		end,
		Sort = function(arg_21_0, arg_21_1)
			return arg_21_0.val > arg_21_1.val
		end,
		Debug = {
			Properties = {
				"dmg",
				"val"
			}
		}
	},
	{
		Name = "Less Cast",
		Initialize = function(arg_22_0, arg_22_1)
			local slot_22_0 = arg_22_0.Damage(arg_22_1.unit)

			arg_22_1.dmg = slot_22_0
			arg_22_1.val = slot_22_0 / arg_22_1.unit.health
		end,
		Sort = function(arg_23_0, arg_23_1)
			return arg_23_0.val > arg_23_1.val
		end,
		Debug = {
			Properties = {
				"dmg",
				"val"
			}
		}
	},
	{
		Name = "Highest HP",
		Initialize = function(arg_24_0, arg_24_1)
			arg_24_1.val = arg_24_1.unit.health
		end,
		Sort = function(arg_25_0, arg_25_1)
			return arg_25_0.val > arg_25_1.val
		end
	},
	{
		Name = "Lowest HP",
		Initialize = function(arg_26_0, arg_26_1)
			arg_26_1.val = arg_26_1.unit.health
		end,
		Sort = function(arg_27_0, arg_27_1)
			return arg_27_0.val < arg_27_1.val
		end
	},
	{
		Name = "Closest To Hero",
		Initialize = function(arg_28_0, arg_28_1)
			arg_28_1.val = ove_0_38(arg_28_1.unit)
		end,
		Sort = function(arg_29_0, arg_29_1)
			return arg_29_0.val < arg_29_1.val
		end
	},
	{
		Name = "Closest To Mouse",
		Initialize = function(arg_30_0, arg_30_1)
			arg_30_1.val = ove_0_38(arg_30_1.unit, game.mousePos)
		end,
		Sort = function(arg_31_0, arg_31_1)
			return arg_31_0.val < arg_31_1.val
		end
	},
	{
		Name = "Highest Priority",
		Initialize = function(arg_32_0, arg_32_1)
			arg_32_1.val = arg_32_0:GetPriority(arg_32_1.unit)
		end,
		Sort = function(arg_33_0, arg_33_1)
			return arg_33_0.val < arg_33_1.val
		end,
		Debug = {
			Properties = {
				"val"
			}
		}
	},
	{
		Name = "Lowest Priority",
		Initialize = function(arg_34_0, arg_34_1)
			arg_34_1.val = arg_34_0:GetPriority(arg_34_1.unit)
		end,
		Sort = function(arg_35_0, arg_35_1)
			return arg_35_0.val > arg_35_1.val
		end,
		Debug = {
			Properties = {
				"val"
			}
		}
	},
	{
		Name = "Hybrid",
		Initialize = function(arg_36_0, arg_36_1)
			local slot_36_0 = arg_36_1.unit

			arg_36_1.dmg = arg_36_0:GetTargetValue(slot_36_0)
			arg_36_1.inRange = ove_0_38(slot_36_0) < 90000
		end,
		Sort = function(arg_37_0, arg_37_1)
			if arg_37_0.inRange and arg_37_1.inRange then
				return arg_37_0.dmg > arg_37_1.dmg
			elseif arg_37_0.inRange then
				return true
			elseif arg_37_1.inRange then
				return false
			end

			return arg_37_0.dmg > arg_37_1.dmg
		end,
		Debug = {
			Properties = {
				"dmg",
				"inRange"
			}
		}
	}
}
local ove_0_52 = {
	Initialize = function(arg_38_0, arg_38_1)
		arg_38_0.TempMode.Initialize(arg_38_0, arg_38_1)

		local slot_38_0 = arg_38_1.unit
		local slot_38_1, slot_38_2 = arg_38_0:GetImmuneTargetInfo(slot_38_0, 0)

		if slot_38_1 then
			arg_38_1.is_immune_or_zombie = not slot_38_2
		else
			arg_38_1.is_immune_or_zombie = slot_38_0.isZombie
		end

		arg_38_1.TempMode = arg_38_0.TempMode
	end,
	Sort = function(arg_39_0, arg_39_1)
		if arg_39_0.is_immune_or_zombie and arg_39_1.is_immune_or_zombie then
			return arg_39_0.TempMode.Sort(arg_39_0, arg_39_1)
		elseif arg_39_0.is_immune_or_zombie then
			return false
		elseif arg_39_1.is_immune_or_zombie then
			return true
		end

		return arg_39_0.TempMode.Sort(arg_39_0, arg_39_1)
	end
}

local function ove_0_53()
	local slot_40_0 = {}

	for iter_40_0, iter_40_1 in ove_0_20(ove_0_51) do
		slot_40_0[#slot_40_0 + 1] = iter_40_1.Name
	end

	return slot_40_0
end

for iter_0_0, iter_0_1 in ove_0_20(ove_0_51) do
	ove_0_51[iter_0_1.Name] = iter_0_1
end

local function ove_0_54(arg_41_0, arg_41_1, arg_41_2)
	arg_41_0.HerosAddedToMenu = arg_41_0.HerosAddedToMenu or {}

	if not arg_41_0.HerosAddedToMenu[arg_41_1.charName] then
		arg_41_2 = ove_0_24(5, arg_41_2 or ove_0_42())

		local slot_41_0 = ove_0_34[arg_41_1.charName] and ove_0_34[arg_41_1.charName][arg_41_2] or arg_41_2

		arg_41_0.Menu.priorities:dropdown(arg_41_1.charName, arg_41_1.charName, slot_41_0, {
			"Very High",
			"High",
			"Medium",
			"Low",
			"Very Low"
		})

		arg_41_0.HerosAddedToMenu[arg_41_1.charName] = true
	end
end

local function ove_0_55(arg_42_0, arg_42_1)
	arg_42_0.Menu = arg_42_1

	arg_42_0.Menu:set("icon", graphics.sprite("resources/target.png"))
	arg_42_0.Menu:dropdown("Mode", "Default Mode", 1, ove_0_53())
	arg_42_0.Menu:header("labeln0", "~~ Priorities ~~")
	arg_42_0.Menu:menu("priorities", "Priorities")
	arg_42_0.Menu:boolean("clickTarget", "Clickable Target", true)
	arg_42_0.Menu:dropdown("clickTargetFocus", "Focus Mode", 1, {
		"Prefer",
		"Force"
	})
	arg_42_0.Menu:dropdown("clickTargetRetain", "Retain Mode", 1, {
		"Flexible",
		"Restrict"
	})
	arg_42_0.Menu:header("labeln1", "~~ Humanizer ~~")
	arg_42_0.Menu:slider("minVisible", "Minimum Time Visible (ms)", 250, 5, 500, 5)
	arg_42_0.Menu:slider("recentTimer", "Recently Seen Timer (s)", 200, 5, 500, 5)
	arg_42_0.Menu:header("labeln2", "~~ Prediction ~~")
	arg_42_0.Menu:menu("Prediction", "Prediction")
	arg_42_0.Menu.Prediction:boolean("FastTargetEnabled", "Fast Targetting", true)
	arg_42_0.Menu.Prediction:boolean("FOWEnabled", "FOW Prediction", true)
	arg_42_0.Menu.Prediction:menu("FOW", "FoW - Settings")
	arg_42_0.Menu.Prediction.FOW:slider("moveMax", "[Moving target] Max timer", 150, 1, 200, 5)
	arg_42_0.Menu.Prediction.FOW:slider("standMax", "[Non-moving target] Max timer", 100, 1, 200, 5)
	arg_42_0.Menu:header("labeln3", "~~ Draws ~~")
	arg_42_0.Menu:boolean("draw_target", "Draw Target", true)
	arg_42_0.Menu:dropdown("mode_draw", "-> Draw switch: ", 1, {
		"Mark",
		"Circle"
	})
end

local function ove_0_56(arg_43_0, arg_43_1, arg_43_2)
	arg_43_2 = arg_43_2 or 0

	local slot_43_0 = ove_0_12.GetEnemyHeroes()
	local slot_43_1 = {}

	for iter_43_0, iter_43_1 in ove_0_20(slot_43_0) do
		slot_43_1[iter_43_1.charName] = true
	end

	local slot_43_2 = false
	local slot_43_3 = false
	local slot_43_4 = 0

	for iter_43_2, iter_43_3 in ove_0_20(arg_43_0) do
		if iter_43_3.HeroName and arg_43_1.charName ~= iter_43_3.HeroName then
			-- block empty
		elseif iter_43_3.RequireAllyName and not slot_43_1[iter_43_3.RequireAllyName] then
			-- block empty
		elseif iter_43_3.MinHpAllowed and ove_0_12.GetPercentHealth(arg_43_1) > iter_43_3.MinHpAllowed then
			-- block empty
		elseif not iter_43_3.BuffName or iter_43_3.Validator and not iter_43_3.Validator(ove_0_31, arg_43_1) then
			-- block empty
		else
			local slot_43_5 = string.lower(iter_43_3.BuffName)
			local slot_43_6 = arg_43_1.buff[slot_43_5]

			if slot_43_6 then
				slot_43_3 = true

				local slot_43_7 = slot_43_6.endTime - game.time + 0.02 + (iter_43_3.BufferTime or 0)

				if slot_43_4 < slot_43_7 then
					slot_43_4 = slot_43_7

					if slot_43_7 < arg_43_2 then
						slot_43_2 = true
					end
				end
			end
		end
	end

	return slot_43_3, slot_43_2
end

local function ove_0_57()
	local slot_44_0 = math.huge
	local slot_44_1

	for iter_44_0, iter_44_1 in ove_0_20(ove_0_12.GetEnemyHeroes()) do
		if not iter_44_1.isDead and iter_44_1.health > 0 then
			local slot_44_2 = ove_0_38(game.mousePos, iter_44_1.pos)

			if slot_44_2 < 40000 and slot_44_2 < slot_44_0 then
				slot_44_0 = slot_44_2
				slot_44_1 = iter_44_1
			end
		end
	end

	return slot_44_1
end

local ove_0_58 = ove_0_31.isDead

local function ove_0_59()
	if not ove_0_58 and ove_0_31.isDead then
		ove_0_25 = nil

		return false
	end

	ove_0_58 = ove_0_31.isDead

	if ove_0_25 then
		if ove_0_25.isDead then
			ove_0_25 = nil

			return false
		elseif not ove_0_25.isVisible and ove_0_39(ove_0_28[ove_0_25.networkID].LastVisibleTime) > 2 then
			ove_0_25 = nil

			return false
		elseif not ove_0_25.visibleOnScreen and ove_0_39(ove_0_29[ove_0_25.networkID].LastVisibleTime) > 2 then
			ove_0_25 = nil

			return false
		end
	end

	return true
end

local function ove_0_60()
	if ove_0_25 ~= nil and not ove_0_59() then
		for iter_46_0 = 1, #ove_0_27 do
			ove_0_27[iter_46_0](true)
		end
	end

	if ove_0_30 and ove_0_25 ~= nil then
		local slot_46_0 = game.selectedTarget

		if slot_46_0 ~= nil and slot_46_0.networkID ~= ove_0_25.networkID or not slot_46_0 then
			ove_0_25 = nil

			for iter_46_1 = 1, #ove_0_27 do
				ove_0_27[iter_46_1]()
			end

			ove_0_30 = false
		end
	end

	local slot_46_1 = ove_0_12.GetEnemyHeroes()

	for iter_46_2 = 1, #slot_46_1 do
		local slot_46_2 = slot_46_1[iter_46_2]
		local slot_46_3 = ove_0_28[slot_46_2.networkID]

		if slot_46_3 then
			if slot_46_2.isVisible then
				if not slot_46_3.LastStateIsVisible then
					slot_46_3.StartVisibleTime = game.time
					slot_46_3.PrevVisibleTime = slot_46_3.LastVisibleTime
				end

				slot_46_3.LastVisibleTime = game.time
			end

			slot_46_3.LastStateIsVisible = slot_46_2.isVisible
		else
			local slot_46_4 = slot_46_2.isVisible and game.time or 0

			ove_0_28[slot_46_2.networkID] = {
				LastStateIsVisible = slot_46_2.isVisible,
				StartVisibleTime = slot_46_4,
				LastVisibleTime = slot_46_4,
				PrevVisibleTime = slot_46_4
			}
		end

		local slot_46_5 = ove_0_29[slot_46_2.networkID]

		if slot_46_5 then
			if slot_46_2.isOnScreen then
				if not slot_46_5.LastStateIsVisible then
					slot_46_5.StartVisibleTime = game.time
					slot_46_5.PrevVisibleTime = slot_46_5.LastVisibleTime
				end

				slot_46_5.LastVisibleTime = game.time
			end

			slot_46_5.LastStateIsVisible = slot_46_2.isOnScreen
		else
			local slot_46_6 = slot_46_2.isOnScreen and game.time or 0

			ove_0_29[slot_46_2.networkID] = {
				LastStateIsVisible = slot_46_2.isOnScreen,
				StartVisibleTime = slot_46_6,
				LastVisibleTime = slot_46_6,
				PrevVisibleTime = slot_46_6
			}
		end
	end
end

local function ove_0_61(arg_47_0)
	if arg_47_0 == 1 then
		local slot_47_0 = ove_0_25

		ove_0_25 = ove_0_57()

		if ove_0_25 then
			for iter_47_0 = 1, #ove_0_26 do
				ove_0_26[iter_47_0](ove_0_25)
			end
		else
			if slot_47_0 ~= nil then
				local slot_47_1 = game.selectedTarget

				if slot_47_1 ~= nil and slot_47_1.networkID == slot_47_0.networkID then
					ove_0_25 = slot_47_0
					ove_0_30 = true

					return
				end
			end

			for iter_47_1 = 1, #ove_0_27 do
				ove_0_27[iter_47_1]()
			end
		end
	end
end

local function ove_0_62(arg_48_0)
	ove_0_26[#ove_0_26 + 1] = arg_48_0
end

local function ove_0_63(arg_49_0)
	ove_0_27[#ove_0_27 + 1] = arg_49_0
end

ove_0_45.Modes = ove_0_51

function ove_0_45.init(arg_50_0, arg_50_1, arg_50_2, arg_50_3)
	assert(arg_50_1, "Menu needed to use target selector")

	arg_50_2 = arg_50_2 or {}
	arg_50_0.TSIndex = ove_0_46:AddCount()
	arg_50_0.ValidTarget = arg_50_2.ValidTarget
	arg_50_0.Value = arg_50_3
	arg_50_0.LastTargetType = ove_0_49.Normal

	ove_0_55(arg_50_0, arg_50_1)
	arg_50_0.Menu:header("version", "Version: " .. ove_0_18(0.01))

	arg_50_0.LastTargetNormalCalcIsValid = {}
	arg_50_0.LastTargetPredCalcIsValid = {}

	arg_50_0:LoadEnemies()
	arg_50_0:LoadAllies()
	arg_50_0:InitEvents()
end

function ove_0_45.LoadEnemies(arg_51_0, arg_51_1)
	arg_51_1 = arg_51_1 or ove_0_12.GetEnemyHeroes()

	local slot_51_0 = ove_0_42(arg_51_1)

	for iter_51_0, iter_51_1 in ove_0_20(arg_51_1) do
		ove_0_54(arg_51_0, iter_51_1, slot_51_0)

		arg_51_0.LastTargetNormalCalcIsValid[iter_51_1.networkID] = false
		arg_51_0.LastTargetPredCalcIsValid[iter_51_1.networkID] = false

		if iter_51_1.charName == "Mordekaiser" then
			arg_51_0.IsMordeInGame = true
		end
	end
end

function ove_0_45.LoadAllies(arg_52_0, arg_52_1)
	arg_52_1 = arg_52_1 or ove_0_12.GetAllyHeroes()

	for iter_52_0, iter_52_1 in ove_0_20(arg_52_1) do
		if iter_52_1.charName == "Mordekaiser" then
			arg_52_0.IsMordeInGame = true

			break
		end
	end
end

function ove_0_45.InitEvents(arg_53_0)
	ove_0_62(function(...)
		arg_53_0:OnSelectTarget(...)
	end)
	ove_0_63(function(...)
		arg_53_0:OnLoseSelectedTarget(...)
	end)
	cb.add(cb.draw, function()
		arg_53_0:OnDraw()
	end)
end

function ove_0_45.OnSelectTarget(arg_57_0, arg_57_1)
	arg_57_0.SelectedTarget = arg_57_1
end

function ove_0_45.OnLoseSelectedTarget(arg_58_0, arg_58_1)
	arg_58_0.SelectedTarget = nil
end

function ove_0_45.OnDraw(arg_59_0)
	if not arg_59_0.Menu.draw_target:get() then
		return
	end

	local slot_59_0 = arg_59_0.target

	if slot_59_0 and slot_59_0.ptr ~= 0 and slot_59_0.isOnScreen and not slot_59_0.isDead and slot_59_0.health > 0 then
		if arg_59_0.Menu.mode_draw:get() == 1 then
			ove_0_41(slot_59_0.pos, 3, slot_59_0.boundingRadius)
		elseif arg_59_0.Menu.mode_draw:get() == 2 then
			graphics.draw_circle(slot_59_0.pos, slot_59_0.boundingRadius, 3, graphics.argb(255, 0, 255, 255), 20)
		end
	end

	if arg_59_0:ShouldUseSelectedTarget() then
		graphics.draw_circle(arg_59_0.SelectedTarget.pos, arg_59_0.SelectedTarget.boundingRadius, 3, graphics.argb(255, 0, 255, 255), 20)
	end
end

function ove_0_45.GetSelectedMode(arg_60_0)
	local slot_60_0 = ove_0_51[arg_60_0.Menu.Mode:get()]

	if not slot_60_0 then
		arg_60_0.Menu.Mode:set("value", 1)
	end

	return slot_60_0 or ove_0_51[arg_60_0.Menu.Mode:get()]
end

function ove_0_45.SetModeName(arg_61_0)
	return
end

function ove_0_45.ShouldUseSelectedTarget(arg_62_0)
	return arg_62_0.Menu.clickTarget:get() and arg_62_0.SelectedTarget and arg_62_0.SelectedTarget.type == ove_0_31.type and arg_62_0.SelectedTarget.team ~= ove_0_31.team
end

function ove_0_45.GetClickTargetFocusMode(arg_63_0, arg_63_1)
	return arg_63_1.ClickTargetMode and arg_63_1.ClickTargetMode.Focus or arg_63_0.Menu.clickTargetFocus:get()
end

function ove_0_45.GetClickTargetRetainMode(arg_64_0, arg_64_1)
	return arg_64_1.ClickTargetMode and arg_64_1.ClickTargetMode.Retain or arg_64_0.Menu.clickTargetRetain:get()
end

function ove_0_45.IsFowPredEnabled(arg_65_0)
	return arg_65_0.Menu.Prediction.FOWEnabled:get()
end

function ove_0_45.GetPriority(arg_66_0, arg_66_1)
	return 1 + ((arg_66_0.Menu.priorities[arg_66_1.charName] and arg_66_0.Menu.priorities[arg_66_1.charName]:get() or 3) - 1) * 0.2
end

function ove_0_45.GetSelectedTarget(arg_67_0)
	return arg_67_0.SelectedTarget
end

function ove_0_45.CanReactToTarget(arg_68_0, arg_68_1)
	if not arg_68_1.isVisible then
		return false
	end

	if not ove_0_28[arg_68_1.networkID] then
		return true
	end

	local slot_68_0 = ove_0_28[arg_68_1.networkID]
	local slot_68_1 = arg_68_0.Menu.minVisible:get() / 1000
	local slot_68_2 = arg_68_0.Menu.recentTimer:get() / 1000

	return slot_68_1 <= ove_0_39(slot_68_0.StartVisibleTime) or slot_68_2 >= ove_0_39(slot_68_0.PrevVisibleTime) and slot_68_1 <= ove_0_39(slot_68_0.PrevVisibleTime)
end

function ove_0_45.GetTargetValue(arg_69_0, arg_69_1)
	local slot_69_0 = arg_69_1.health

	if arg_69_0.Value == 1 then
		dmg = ove_0_12.CalculatePhysicalDamage(arg_69_1, player)
	else
		dmg = ove_0_12.CalculateMagicDamage(arg_69_1, player)
	end

	return dmg / slot_69_0 / arg_69_0:GetPriority(arg_69_1), dmg
end

function ove_0_45.GetImmuneTargetInfo(arg_70_0, arg_70_1, arg_70_2)
	return ove_0_56(ove_0_35, arg_70_1, arg_70_2)
end

function ove_0_45.GetInvulnTargetInfo(arg_71_0, arg_71_1, arg_71_2)
	return ove_0_56(ove_0_36, arg_71_1, arg_71_2)
end

function ove_0_45.IsTargetHiddenMorde(arg_72_0, arg_72_1)
	if not arg_72_0.IsMordeInGame then
		return false
	end

	local slot_72_0 = arg_72_1.buff.mordekaiserr_statstealenemy

	if slot_72_0 and slot_72_0.source and slot_72_0.endTime > 0 then
		return slot_72_0.source.networkID ~= ove_0_31.networkID
	end

	local slot_72_1 = arg_72_1.buff.mordekaiserr_statstealenemy

	if slot_72_1 and slot_72_1.endTime > 0 then
		local slot_72_2 = player.buff.mordekaiserr_statstealenemy

		if not slot_72_2 or not (slot_72_2.endTime > 0) or not slot_72_2.source or slot_72_2.source.networkID ~= arg_72_1.networkID then
			return true
		end
	end

	return false
end

function ove_0_45.GetGwenWUnit(arg_73_0)
	for iter_73_0, iter_73_1 in ove_0_20(ove_0_12.GetEnemyMinionsInRange(math.huge, player.pos)) do
		if iter_73_1 and iter_73_1.isVisible and iter_73_1.buff[BUFF_INVULNERABILITY] and iter_73_1.mana == 500 and iter_73_1.name == "TestCube" then
			return iter_73_1
		end
	end
end

function ove_0_45.IsUntargetableGwen(arg_74_0, arg_74_1, arg_74_2)
	if arg_74_1.charName == "Gwen" and arg_74_1.buff.gwenwuntargetabilitymanager then
		local slot_74_0 = arg_74_0:GetGwenWUnit()

		if slot_74_0 and ove_0_38(arg_74_2, slot_74_0) > 216225 then
			return true
		end
	end

	return false
end

local ove_0_64 = {
	Yuumi = {
		"YuumiWAttach"
	}
}

function ove_0_45.HasBadBuff(arg_75_0, arg_75_1, arg_75_2)
	arg_75_2 = arg_75_2 or ove_0_31

	local slot_75_0 = ove_0_64[arg_75_1.charName]

	if slot_75_0 then
		for iter_75_0 = 1, #slot_75_0 do
			if arg_75_1.buff[slot_75_0[iter_75_0]] then
				return true
			end
		end
	end

	if arg_75_0:IsTargetHiddenMorde(arg_75_1) then
		return true
	end

	if arg_75_0:IsUntargetableGwen(arg_75_1, arg_75_2) then
		return true
	end

	return false
end

function ove_0_45.IsTargetInvalid(arg_76_0, arg_76_1, arg_76_2)
	return not arg_76_1 or arg_76_1.isDead or arg_76_1.health == 0 or arg_76_0:HasBadBuff(arg_76_1, arg_76_2)
end

function ove_0_45.IsTargetInvulnOrZombie(arg_77_0, arg_77_1)
	return arg_77_1.buff[BUFF_INVULNERABILITY] or arg_77_1.isZombie
end

function ove_0_45.IsTargetValidNonSkillshot(arg_78_0, arg_78_1)
	return arg_78_1 and arg_78_1.isVisible and not arg_78_1.isDead and arg_78_1.isTargetable and not arg_78_0:IsUntargetableGwen(arg_78_1, ove_0_31)
end

function ove_0_45.GetOptions(arg_79_0, arg_79_1, arg_79_2)
	return ove_0_17(arg_79_2, arg_79_1)
end

function ove_0_45.SetTag(arg_80_0, arg_80_1, arg_80_2)
	if arg_80_1 then
		ove_0_32[arg_80_0] = ove_0_32[arg_80_0] or {}
		ove_0_32[arg_80_0][arg_80_1] = {
			arg_80_2 and arg_80_2.networkID,
			RiotClock.time + 3
		}
	end
end

function ove_0_45.AddMode(arg_81_0, arg_81_1, arg_81_2)
	assert(arg_81_1, "Mode is nil")
	assert(ove_0_21(arg_81_1) == "table", "Mode must be table")
	assert(arg_81_1.Name, "Mode must have a name")
	assert(arg_81_1.Initialize, "Mode must have an initialize")
	assert(arg_81_1.Sort, "Mode must have a sort")

	local slot_81_0 = #ove_0_51 + 1

	ove_0_51[slot_81_0] = arg_81_1
	ove_0_51[arg_81_1.Name] = arg_81_1
	arg_81_0.Menu.mode.list = ove_0_53()

	if arg_81_2 then
		arg_81_0.Menu.Mode:set("value", slot_81_0)
	end
end

function ove_0_45.IsValidPredResult(arg_82_0, arg_82_1, arg_82_2, arg_82_3)
	if arg_82_2.endPos then
		if arg_82_1.path.isDashing then
			return true
		end

		arg_82_3 = arg_82_3 or 0.25

		if arg_82_0:CanReactToTarget(arg_82_1) then
			local slot_82_0, slot_82_1 = arg_82_0:GetImmuneTargetInfo(arg_82_1, arg_82_3)

			if slot_82_0 and not slot_82_1 then
				return false
			end

			local slot_82_2, slot_82_3 = arg_82_0:GetInvulnTargetInfo(arg_82_1, arg_82_3)

			return ove_0_12.IsValidTarget(arg_82_1) or slot_82_2 and slot_82_3
		end
	end

	return false
end

local ove_0_65 = ove_0_50.Retain.Flexible
local ove_0_66 = ove_0_50.Retain.Restrict

function ove_0_45.IsValidPredTarget(arg_83_0, arg_83_1, arg_83_2, arg_83_3, arg_83_4)
	local slot_83_0 = arg_83_4.ValidTarget or ove_0_43
	local slot_83_1 = arg_83_4.ValidPred or ove_0_43

	if arg_83_0:IsTargetInvalid(arg_83_1) then
		return false, {
			[ove_0_65] = true,
			[ove_0_66] = true
		}
	end

	if not slot_83_0(arg_83_1, arg_83_2) then
		return false, {
			[ove_0_65] = true,
			[ove_0_66] = false
		}
	end

	local slot_83_2 = false
	local slot_83_3 = false
	local slot_83_4

	if arg_83_4.Spell.type == "Circular" then
		slot_83_4 = ove_0_10.circular.get_prediction(arg_83_4.Spell, arg_83_1, arg_83_4.Spell.source or player.path.serverPos:to2D())
	end

	if arg_83_4.Spell.type == "Linear" then
		slot_83_4 = ove_0_10.linear.get_prediction(arg_83_4.Spell, arg_83_1, arg_83_4.Spell.source or player.path.serverPos:to2D())
	end

	if slot_83_4 then
		slot_83_2 = slot_83_4.startPos:dist(slot_83_4.endPos) > arg_83_4.Spell.range and true or false

		if arg_83_4.Spell.collision and ove_0_21(arg_83_4.Spell.collision) == "table" then
			slot_83_3 = ove_0_10.collision.get_prediction(arg_83_4.Spell, slot_83_4, arg_83_1) and true or false
		end
	end

	local slot_83_5 = ove_0_13.Compute(arg_83_4.Spell, slot_83_4, arg_83_1)

	arg_83_3[arg_83_1.networkID] = {
		result = slot_83_4,
		castPos = (ove_0_10.core.get_pos_after_time(arg_83_1, slot_83_5) + slot_83_4.endPos) / 2,
		time = slot_83_5
	}
	arg_83_2.pred_result = slot_83_4
	arg_83_2.was_out_of_range = slot_83_2
	arg_83_2.had_collision = slot_83_3

	if slot_83_4 then
		if not arg_83_0:IsValidPredResult(arg_83_1, slot_83_4) then
			arg_83_3[arg_83_1.networkID] = nil

			return false, {
				[ove_0_65] = false,
				[ove_0_66] = false
			}
		end

		if not slot_83_1(arg_83_1, slot_83_4, arg_83_2) then
			return false, {
				[ove_0_65] = false,
				[ove_0_66] = false
			}
		end
	elseif slot_83_2 or not arg_83_1.isVisible then
		return false, {
			[ove_0_65] = true,
			[ove_0_66] = false
		}
	elseif slot_83_3 then
		return not (arg_83_0.Menu.Prediction.FastTargetEnabled:get() and not arg_83_2.is_selected_target), {
			[ove_0_65] = false,
			[ove_0_66] = false
		}
	end

	return true, {
		[ove_0_65] = false,
		[ove_0_66] = false
	}
end

function ove_0_45.IsValidNormalTarget(arg_84_0, arg_84_1, arg_84_2, arg_84_3)
	local slot_84_0 = arg_84_3.ValidTarget or arg_84_0.ValidTarget or ove_0_43
	local slot_84_1 = {
		[ove_0_65] = true,
		[ove_0_66] = arg_84_0:IsTargetInvalid(arg_84_1)
	}

	if arg_84_3.IsRawValidTargetFunction then
		return arg_84_0:CanReactToTarget(arg_84_1) and slot_84_0(arg_84_1, arg_84_2), slot_84_1
	end

	return arg_84_0:CanReactToTarget(arg_84_1) and arg_84_0:IsTargetValidNonSkillshot(arg_84_1) and slot_84_0(arg_84_1, arg_84_2), slot_84_1
end

function ove_0_45.GetPredValidTargetFunction(arg_85_0, arg_85_1)
	local slot_85_0 = {}

	return function(arg_86_0, arg_86_1)
		local slot_86_0, slot_86_1 = arg_85_0:IsValidPredTarget(arg_86_0, arg_86_1, slot_85_0, arg_85_1)

		arg_85_0.LastTargetPredCalcIsValid[arg_86_0.networkID] = slot_86_0

		return slot_86_0, slot_86_1
	end, slot_85_0
end

function ove_0_45.GetNormalValidTargetFunction(arg_87_0, arg_87_1)
	return function(arg_88_0, arg_88_1)
		local slot_88_0, slot_88_1 = arg_87_0:IsValidNormalTarget(arg_88_0, arg_88_1, arg_87_1)

		arg_87_0.LastTargetNormalCalcIsValid[arg_88_0.networkID] = slot_88_0

		return slot_88_0, slot_88_1
	end
end

function ove_0_45.GetNormalTargetMode(arg_89_0, arg_89_1)
	arg_89_0.TempMode = arg_89_1.TargetMode or arg_89_0:GetSelectedMode()

	return ove_0_52
end

function ove_0_45.SetLastTarget(arg_90_0, arg_90_1, arg_90_2, arg_90_3)
	arg_90_0.LastTargetType = arg_90_1
	arg_90_0.target = arg_90_2

	arg_90_0:SetTag(arg_90_3.Tag, arg_90_2)
end

function ove_0_45.GetTarget(arg_91_0, arg_91_1, arg_91_2, arg_91_3, arg_91_4, arg_91_5)
	local slot_91_0 = arg_91_0:Evaluate({
		Spell = arg_91_1,
		Source = arg_91_2,
		ValidTarget = arg_91_3,
		ValidPred = arg_91_4,
		TargetMode = arg_91_5
	})
	local slot_91_1 = slot_91_0.Target

	if slot_91_1 then
		return slot_91_1, slot_91_0.PredData[slot_91_1.networkID], slot_91_0.ModeData[slot_91_1.networkID]
	end
end

function ove_0_45.GetTargets(arg_92_0, arg_92_1, arg_92_2, arg_92_3, arg_92_4, arg_92_5)
	local slot_92_0 = arg_92_0:Evaluate({
		Spell = arg_92_1,
		Source = arg_92_2,
		ValidTarget = arg_92_3,
		ValidPred = arg_92_4,
		TargetMode = arg_92_5
	})

	return slot_92_0.Targets, slot_92_0.PredData, slot_92_0.ModeData
end

function ove_0_45.update(arg_93_0, arg_93_1, arg_93_2)
	local slot_93_0 = arg_93_0:Evaluate({
		ValidTarget = arg_93_1,
		TargetMode = arg_93_2
	})

	return slot_93_0.Targets, slot_93_0.ModeData
end

function ove_0_45.Evaluate(arg_94_0, arg_94_1)
	arg_94_1 = arg_94_1 or {}

	if arg_94_1.Spell then
		local slot_94_0, slot_94_1 = arg_94_0:GetPredValidTargetFunction(arg_94_1)
		local slot_94_2 = {
			ValidTarget = slot_94_0,
			TargetMode = arg_94_0:GetNormalTargetMode(arg_94_1)
		}
		local slot_94_3 = arg_94_0:Calc(arg_94_0:GetOptions(arg_94_1, slot_94_2))

		slot_94_3.PredData = slot_94_1
		slot_94_3.TargetPred = slot_94_3.Target and slot_94_3.PredData[slot_94_3.Target.networkID]

		arg_94_0:SetLastTarget(ove_0_49.Pred, slot_94_3.Target, arg_94_1)

		return slot_94_3
	end

	local slot_94_4 = {
		ValidTarget = arg_94_0:GetNormalValidTargetFunction(arg_94_1),
		TargetMode = arg_94_0:GetNormalTargetMode(arg_94_1)
	}
	local slot_94_5 = arg_94_0:Calc(arg_94_0:GetOptions(arg_94_1, slot_94_4))

	arg_94_0:SetLastTarget(ove_0_49.Normal, slot_94_5.Target, arg_94_1)

	return slot_94_5
end

function ove_0_45.Calc(arg_95_0, arg_95_1)
	local slot_95_0 = arg_95_1.TargetMode or arg_95_0:GetSelectedMode()
	local slot_95_1 = {}

	local function slot_95_2(arg_96_0, arg_96_1)
		if slot_95_1[arg_96_0.networkID] then
			return
		end

		if arg_96_0.type == TYPE_HERO then
			ove_0_54(arg_95_0, arg_96_0)
		end

		local slot_96_0 = {
			unit = arg_96_0,
			is_selected_target = arg_96_1
		}
		local slot_96_1, slot_96_2 = arg_95_1.ValidTarget(arg_96_0, slot_96_0)

		if slot_96_1 then
			slot_95_0.Initialize(arg_95_0, slot_96_0)

			slot_95_1[arg_96_0.networkID] = slot_96_0
		end

		return slot_96_1, slot_96_2
	end

	if arg_95_0:ShouldUseSelectedTarget() then
		local slot_95_3 = arg_95_0.SelectedTarget
		local slot_95_4, slot_95_5 = slot_95_2(slot_95_3, true)

		if slot_95_4 then
			return {
				Target = slot_95_3,
				Targets = {
					slot_95_3
				},
				ModeData = slot_95_1
			}
		end

		if slot_95_5[arg_95_0:GetClickTargetRetainMode(arg_95_1)] then
			arg_95_0:OnLoseSelectedTarget()
		elseif arg_95_0:GetClickTargetFocusMode(arg_95_1) ~= ove_0_50.Focus.Prefer then
			return {
				Targets = {},
				ModeData = {}
			}
		end
	end

	local slot_95_6 = {}

	for iter_95_0, iter_95_1 in ove_0_20(arg_95_1.Enemies or ove_0_12.GetEnemyHeroes()) do
		if slot_95_2(iter_95_1) then
			slot_95_6[#slot_95_6 + 1] = iter_95_1
		end
	end

	local slot_95_7 = slot_95_0.Sort

	ove_0_23(slot_95_6, function(arg_97_0, arg_97_1)
		return slot_95_7(slot_95_1[arg_97_0.networkID], slot_95_1[arg_97_1.networkID])
	end)

	return {
		Target = slot_95_6[1],
		Targets = slot_95_6,
		ModeData = slot_95_1
	}
end

cb.add(cb.tick, ove_0_60)
cb.add(cb.keydown, ove_0_61)

return ove_0_45

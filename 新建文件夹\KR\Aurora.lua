

local ove_0_6 = module.load(header.id, "common")
local ove_0_7 = module.load(header.id, "menu")
local ove_0_8 = module.internal("orb")
local ove_0_9 = module.internal("TS")
local ove_0_10 = module.internal("pred")
local ove_0_11 = module.internal("damagelib")
local ove_0_12 = module.seek("evade")
local ove_0_13 = graphics.create_effect(graphics.CIRCLE_RAINBOW_BOLD)
local ove_0_14 = graphics.create_effect(graphics.CIRCLE_RAINBOW_BOLD)
local ove_0_15 = graphics.create_effect(graphics.CIRCLE_RAINBOW_BOLD)
local ove_0_16 = graphics.create_effect(graphics.CIRCLE_RAINBOW_BOLD)
local ove_0_17 = false
local ove_0_18 = {
	speed = 1600,
	range = 850,
	delay = 0.25,
	boundingRadiusMod = 1,
	width = 105,
	slot = player:spellSlot(0),
	collision = {
		minion = false,
		wall = true,
		hero = false
	}
}
local ove_0_19 = {
	speed = 350,
	range = 300,
	delay = 0.25,
	boundingRadiusMod = 0,
	width = 0,
	slot = player:spellSlot(1),
	collision = {
		minion = false,
		wall = false,
		hero = false
	}
}
local ove_0_20 = {
	range = 800,
	delay = 0.35,
	boundingRadiusMod = 1,
	width = 85,
	slot = player:spellSlot(2),
	speed = math.huge,
	collision = {
		minion = false,
		wall = false,
		hero = false
	}
}
local ove_0_21 = {
	radius = 700,
	range = 1400,
	delay = 0,
	boundingRadiusMod = 0,
	width = 700,
	slot = player:spellSlot(3),
	speed = math.huge,
	collision = {
		minion = false,
		wall = false,
		hero = false
	}
}

local function ove_0_22(arg_5_0)
	local slot_5_0 = 0
	local slot_5_1 = {
		60,
		97.5,
		135,
		172.5,
		210
	}

	if ove_0_18.slot.level > 0 then
		local slot_5_2 = (arg_5_0.maxHealth - arg_5_0.health) / arg_5_0.maxHealth * 0.5 * slot_5_1[ove_0_18.slot.level]
		local slot_5_3 = slot_5_1[ove_0_18.slot.level] + 0.6 * player.totalAp + slot_5_2

		slot_5_0 = ove_0_11.calc_magical_damage(player, arg_5_0, slot_5_3)
	end

	return slot_5_0
end

local function ove_0_23(arg_6_0)
	local slot_6_0 = 0
	local slot_6_1 = {
		70,
		110,
		150,
		190,
		230
	}

	if ove_0_20.slot.level > 0 then
		local slot_6_2 = slot_6_1[ove_0_20.slot.level] + 0.8 * player.totalAp

		slot_6_0 = ove_0_11.calc_magical_damage(player, arg_6_0, slot_6_2)
	end

	return slot_6_0
end

local function ove_0_24(arg_7_0)
	local slot_7_0 = 0
	local slot_7_1 = {
		200,
		325,
		450
	}

	if ove_0_21.slot.level > 0 then
		local slot_7_2 = slot_7_1[ove_0_21.slot.level] + 0.6 * player.totalAp

		slot_7_0 = ove_0_11.calc_magical_damage(player, arg_7_0, slot_7_2)
	end

	return slot_7_0
end

local function ove_0_25(arg_8_0, arg_8_1, arg_8_2)
	if arg_8_1 and not arg_8_1.isDead and arg_8_1.isVisible and arg_8_1.isTargetable and not arg_8_1.buff[17] then
		if arg_8_2 > ove_0_18.range or arg_8_1.buff.rocketgrab or arg_8_1.buff.bansheesveil or arg_8_1.buff.itemmagekillerveil or arg_8_1.buff.nocturneshroudofdarkness or arg_8_1.buff.sivire or arg_8_1.buff.fioraw or arg_8_1.buff.blackshield then
			return
		end

		arg_8_0.object = arg_8_1

		return true
	end
end

local function ove_0_26()
	return ove_0_9.get_result(ove_0_25).object
end

local function ove_0_27(arg_10_0, arg_10_1, arg_10_2)
	if arg_10_1 and not arg_10_1.isDead and arg_10_1.isVisible and arg_10_1.isTargetable and not arg_10_1.buff[17] then
		if arg_10_2 > ove_0_20.range or arg_10_1.buff.rocketgrab or arg_10_1.buff.bansheesveil or arg_10_1.buff.itemmagekillerveil or arg_10_1.buff.nocturneshroudofdarkness or arg_10_1.buff.sivire or arg_10_1.buff.fioraw or arg_10_1.buff.blackshield then
			return
		end

		arg_10_0.object = arg_10_1

		return true
	end
end

local function ove_0_28()
	return ove_0_9.get_result(ove_0_27).object
end

local function ove_0_29(arg_12_0, arg_12_1, arg_12_2)
	if arg_12_1 and not arg_12_1.isDead and arg_12_1.isVisible and arg_12_1.isTargetable and not arg_12_1.buff[17] then
		if arg_12_2 > ove_0_21.range or arg_12_1.buff.rocketgrab or arg_12_1.buff.bansheesveil or arg_12_1.buff.itemmagekillerveil or arg_12_1.buff.nocturneshroudofdarkness or arg_12_1.buff.sivire or arg_12_1.buff.fioraw or arg_12_1.buff.blackshield then
			return
		end

		arg_12_0.object = arg_12_1

		return true
	end
end

local function ove_0_30()
	return ove_0_9.get_result(ove_0_29).object
end

local function ove_0_31()
	local slot_14_0 = ove_0_26()
	local slot_14_1 = ove_0_7.auroramenu.Combo.qsettings.useq:get()
	local slot_14_2 = ove_0_7.auroramenu.Combo.qsettings.slowpredq:get()

	if ove_0_8.core.is_spell_locked() then
		return
	end

	if slot_14_1 and ove_0_18.slot.state == 0 then
		if ove_0_18.slot.name == "AuroraQ" and slot_14_0 and ove_0_6.IsValidTarget(slot_14_0) then
			local slot_14_3 = ove_0_10.linear.get_prediction(ove_0_18, slot_14_0)

			if slot_14_2 and ove_0_6.SlowPredLinear(ove_0_18, slot_14_3, slot_14_0, ove_0_18.range) and slot_14_3 and slot_14_3.startPos:dist(slot_14_3.endPos) < ove_0_18.range then
				player:castSpell("pos", 0, vec3(slot_14_3.endPos.x, slot_14_0.y, slot_14_3.endPos.y))
			end

			if not slot_14_2 and slot_14_3 and slot_14_3.startPos:dist(slot_14_3.endPos) < ove_0_18.range then
				player:castSpell("pos", 0, vec3(slot_14_3.endPos.x, slot_14_0.y, slot_14_3.endPos.y))
			end
		end

		if ove_0_18.slot.name == "AuroraQRecast" then
			player:castSpell("self", 0)
		end
	end
end

local function ove_0_32()
	local slot_15_0 = ove_0_28()
	local slot_15_1 = ove_0_7.auroramenu.Combo.esettings.usee:get()
	local slot_15_2 = ove_0_7.auroramenu.Combo.esettings.slowprede:get()

	if ove_0_8.core.is_spell_locked() then
		return
	end

	if slot_15_1 and ove_0_20.slot.state == 0 and slot_15_0 and ove_0_6.IsValidTarget(slot_15_0) then
		local slot_15_3 = ove_0_10.linear.get_prediction(ove_0_20, slot_15_0)

		if slot_15_2 and ove_0_6.SlowPredLinear(ove_0_20, slot_15_3, slot_15_0, ove_0_20.range) and slot_15_3 and slot_15_3.startPos:dist(slot_15_3.endPos) < ove_0_20.range then
			player:castSpell("pos", 2, vec3(slot_15_3.endPos.x, slot_15_0.y, slot_15_3.endPos.y))
		end

		if not slot_15_2 and slot_15_3 and slot_15_3.startPos:dist(slot_15_3.endPos) < ove_0_20.range then
			player:castSpell("pos", 2, vec3(slot_15_3.endPos.x, slot_15_0.y, slot_15_3.endPos.y))
		end
	end
end

local function ove_0_33()
	local slot_16_0 = ove_0_30()
	local slot_16_1 = ove_0_7.auroramenu.Combo.rsettings.user:get()
	local slot_16_2 = ove_0_7.auroramenu.Combo.rsettings.minene:get()

	if ove_0_7.auroramenu.Combo.rsettings.healthpercent:get() > ove_0_6.HealthPercent(player) then
		return
	end

	if ove_0_21.slot.name == "AuroraRRecast" then
		return
	end

	if slot_16_1 and ove_0_21.slot.state == 0 then
		local slot_16_3 = {}

		for iter_16_0 = 0, objManager.enemies_n - 1 do
			local slot_16_4 = objManager.enemies[iter_16_0]

			if slot_16_4 and slot_16_4.isVisible and slot_16_4.isTargetable and slot_16_4.isAlive and slot_16_4.pos:dist(player.pos) < ove_0_21.range then
				table.insert(slot_16_3, slot_16_4)
			end
		end

		if slot_16_2 <= #slot_16_3 and slot_16_0 then
			local slot_16_5 = ove_0_10.circular.get_prediction(ove_0_21, slot_16_0)

			if slot_16_5 and slot_16_5.startPos:dist(slot_16_5.endPos) < ove_0_21.range and slot_16_2 <= ove_0_6.countEnemiesInRange(slot_16_5.endPos:toGame3D(), ove_0_21.radius) then
				player:castSpell("pos", 3, slot_16_0.pos)
			end
		end
	end
end

local function ove_0_34()
	local slot_17_0 = ove_0_30()

	if ove_0_21.slot.name == "AuroraRRecast" then
		return
	end

	if ove_0_21.slot.state == 0 and slot_17_0 then
		player:castSpell("pos", 3, slot_17_0.pos)
	end
end

local function ove_0_35()
	if ove_0_7.auroramenu.Killsteal.ksQ:get() then
		if ove_0_18.slot.state == 0 and ove_0_18.slot.name == "AuroraQ" then
			local slot_18_0 = ove_0_26()

			if slot_18_0 and ove_0_6.IsValidTarget(slot_18_0) and slot_18_0.health <= ove_0_22(slot_18_0) then
				local slot_18_1 = ove_0_10.linear.get_prediction(ove_0_18, slot_18_0)

				if slot_18_1 and slot_18_1.startPos:dist(slot_18_1.endPos) <= ove_0_18.range then
					player:castSpell("pos", 0, vec3(slot_18_1.endPos.x, slot_18_0.y, slot_18_1.endPos.y))
				end
			end
		end

		if ove_0_18.slot.name == "AuroraQRecast" then
			for iter_18_0 = 0, objManager.enemies_n - 1 do
				local slot_18_2 = objManager.enemies[iter_18_0]

				if slot_18_2 and (slot_18_2.pos:dist(player.pos) <= ove_0_18.range or slot_18_2.pos:dist(player.pos) >= ove_0_18.range) and slot_18_2.health <= ove_0_22(slot_18_2) and slot_18_2.buff.auroraqdebufftracker then
					player:castSpell("self", 0)
				end
			end
		end
	end
end

local function ove_0_36()
	local slot_19_0 = ove_0_7.auroramenu.Killsteal.ksE:get()
	local slot_19_1 = ove_0_28()

	if slot_19_0 and ove_0_20.slot.state == 0 and slot_19_1 and ove_0_6.IsValidTarget(slot_19_1) and slot_19_1.health <= ove_0_23(slot_19_1) then
		local slot_19_2 = ove_0_10.linear.get_prediction(ove_0_20, slot_19_1)

		if slot_19_2 and slot_19_2.startPos:dist(slot_19_2.endPos) <= ove_0_20.range then
			player:castSpell("pos", 2, vec3(slot_19_2.endPos.x, slot_19_1.y, slot_19_2.endPos.y))
		end
	end
end

local function ove_0_37()
	local slot_20_0 = ove_0_7.auroramenu.Killsteal.ksR:get()
	local slot_20_1 = ove_0_30()

	if ove_0_21.slot.name == "AuroraRRecast" then
		return
	end

	if slot_20_0 and ove_0_21.slot.state == 0 and slot_20_1 and ove_0_6.IsValidTarget(slot_20_1) and slot_20_1.health <= ove_0_24(slot_20_1) then
		local slot_20_2 = ove_0_10.circular.get_prediction(ove_0_21, slot_20_1)

		if slot_20_2 and slot_20_2.startPos:dist(slot_20_2.endPos) <= ove_0_21.range then
			player:castSpell("pos", 3, slot_20_1.pos)
		end
	end
end

local function ove_0_38()
	if not ove_0_7.auroramenu.Keys.farmtoggle:get() then
		return
	end

	for iter_21_0 = 0, objManager.minions.size[TEAM_ENEMY] - 1 do
		local slot_21_0 = objManager.minions[TEAM_ENEMY][iter_21_0]
		local slot_21_1 = ove_0_7.auroramenu.Clear.LcQ:get()
		local slot_21_2 = ove_0_7.auroramenu.Clear.LcE:get()

		if slot_21_0 and ove_0_6.IsValidTarget(slot_21_0) and not slot_21_0.name:find("Ward") then
			if slot_21_1 and slot_21_0.pos:dist(player.pos) <= ove_0_18.range then
				if ove_0_18.slot.name == "AuroraQ" then
					player:castSpell("pos", 0, slot_21_0.pos)
				end

				if ove_0_18.slot.name == "AuroraQRecast" then
					player:castSpell("self", 0)
				end
			end

			if slot_21_2 and slot_21_0.pos:dist(player.pos) <= ove_0_20.range then
				player:castSpell("pos", 2, slot_21_0.pos)
			end
		end
	end
end

local function ove_0_39()
	if not ove_0_7.auroramenu.Keys.farmtoggle:get() then
		return
	end

	if player.pos:countEnemies(ove_0_18.range) > 0 then
		return
	end

	for iter_22_0 = 0, objManager.minions.size[TEAM_NEUTRAL] - 1 do
		local slot_22_0 = objManager.minions[TEAM_NEUTRAL][iter_22_0]
		local slot_22_1 = ove_0_7.auroramenu.Clear.JcQ:get()
		local slot_22_2 = ove_0_7.auroramenu.Clear.JcE:get()

		if slot_22_0 and ove_0_6.IsValidTarget(slot_22_0) and not slot_22_0.name:find("Ward") then
			if slot_22_1 and slot_22_0.pos:dist(player.pos) <= ove_0_18.range then
				if ove_0_18.slot.name == "AuroraQ" then
					player:castSpell("pos", 0, slot_22_0.pos)
				end

				if ove_0_18.slot.name == "AuroraQRecast" then
					player:castSpell("self", 0)
				end
			end

			if slot_22_2 and slot_22_0.pos:dist(player.pos) <= ove_0_20.range then
				player:castSpell("pos", 2, slot_22_0.pos)
			end
		end
	end
end

local function ove_0_40()
	if ove_0_12 then
		if ove_0_12.core.is_action_safe(game.mousePos2D, 350, 0) then
			ove_0_17 = true
		end

		if not ove_0_12.core.is_action_safe(game.mousePos2D, 350, 0) then
			ove_0_17 = false
		end
	end
end

cb.add(cb.tick, function()
	if ove_0_7.auroramenu.Keys.stogglebar:get() then
		ove_0_33()
		ove_0_31()
		ove_0_32()
	end

	if ove_0_7.auroramenu.Keys.clearkey:get() then
		ove_0_38()
		ove_0_39()
	end

	if ove_0_7.auroramenu.Keys.semir:get() then
		player:move(mousePos)
		ove_0_34()
	end

	ove_0_35()
	ove_0_36()
	ove_0_37()
	ove_0_40()
end)
ove_0_12.core.register_on_create_spell(function(arg_25_0)
	if ove_0_19.slot.state ~= 0 then
		return
	end

	if not ove_0_7.auroramenu.Combo.wsettings.usew:get() then
		return
	end

	if not arg_25_0:contains(player) then
		return
	end

	if arg_25_0:IsEnabledInMenu() and arg_25_0:GetDangerLevelFromMenu() >= ove_0_7.auroramenu.Combo.wsettings.evadedanger:get() and not arg_25_0:contains(game.mousePos2D) and ove_0_17 then
		player:castSpell("pos", 1, game.mousePos)
	end
end)
cb.add(cb.draw, function()
	local slot_26_0 = ove_0_7.auroramenu.Draws.drawQ:get()
	local slot_26_1 = ove_0_7.auroramenu.Draws.drawW:get()
	local slot_26_2 = ove_0_7.auroramenu.Draws.drawE:get()
	local slot_26_3 = ove_0_7.auroramenu.Draws.drawR:get()
	local slot_26_4 = ove_0_7.auroramenu.Draws.drawAA:get()
	local slot_26_5 = ove_0_7.auroramenu.Draws.circlemode:get()

	if player.isOnScreen then
		if slot_26_0 and ove_0_18.slot.level > 0 then
			if slot_26_5 == 1 then
				ove_0_13:hide()
				graphics.draw_circle(player.pos, ove_0_18.range, 2, graphics.argb(255, 255, 255, 255), 100)
			end

			if slot_26_5 == 2 then
				ove_0_13:show()
				ove_0_13:update_circle(player.pos, ove_0_18.range, 2, graphics.argb(255, 255, 255, 255))
			end
		end

		if slot_26_1 and ove_0_19.slot.level > 0 then
			if slot_26_5 == 1 then
				ove_0_14:hide()
				graphics.draw_circle(player.pos, ove_0_19.range, 2, graphics.argb(255, 0, 0, 255), 100)
			end

			if slot_26_5 == 2 then
				ove_0_14:show()
				ove_0_14:update_circle(player.pos, ove_0_19.range, 2, graphics.argb(255, 255, 255, 255))
			end
		end

		if slot_26_2 and ove_0_20.slot.level > 0 then
			if slot_26_5 == 1 then
				ove_0_15:hide()
				graphics.draw_circle(player.pos, ove_0_20.range, 2, graphics.argb(255, 0, 255, 0), 100)
			end

			if slot_26_5 == 2 then
				ove_0_15:show()
				ove_0_15:update_circle(player.pos, ove_0_20.range, 2, graphics.argb(255, 255, 255, 255))
			end
		end

		if slot_26_3 and ove_0_21.slot.level > 0 then
			if slot_26_5 == 1 then
				ove_0_16:hide()
				graphics.draw_circle(player.pos, ove_0_21.range, 2, graphics.argb(255, 255, 0, 0), 100)
			end

			if slot_26_5 == 2 then
				ove_0_16:show()
				ove_0_16:update_circle(player.pos, ove_0_21.range, 2, graphics.argb(255, 255, 255, 255))
			end
		end

		if slot_26_4 then
			ove_0_6.AATracker()
		end
	end

	if slot_26_5 == 2 then
		if not slot_26_0 then
			ove_0_13:hide()
		end

		if not slot_26_1 then
			ove_0_14:hide()
		end

		if not slot_26_2 then
			ove_0_15:hide()
		end

		if not slot_26_3 then
			ove_0_16:hide()
		end
	end
end)

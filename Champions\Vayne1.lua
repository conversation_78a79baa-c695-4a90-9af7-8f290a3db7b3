local VaynePlugin = {}
local ts = module.internal("TS")
local orb = module.internal("orb")
local preds = module.internal("pred")
local DelayAction = module.load("NickAIO", "Core/DelayAction")
local DelayTick = module.load("NickAIO", "Core/DelayTick")
local Prediction = module.load("NickAIO", "Core/Prediction")
local BuffManager = module.load("NickAIO", "Library/BuffManager")
local CalculateManager = module.load("NickAIO", "Library/CalculateManager")
--local FarmManager = module.load("NickAIO", "Library/FarmManager")
local ItemManager = module.load("NickAIO", "Library/ItemManager")
local NetManager = module.load("NickAIO", "Library/NetManager")
local ObjectManager = module.load("NickAIO", "Library/ObjectManager")
local OrbManager = module.load("NickAIO", "Library/OrbManager")
local SpellManager = module.load("NickAIO", "Library/SpellManager")
local VectorManager = module.load("NickAIO", "Library/VectorManager")
local MyCommon = module.load("NickAIO", "Library/ExtraManager")
local common = module.load("NickAIO", "Utility/common")
local ui = module.load("Brian", "ui");

local GAPCLOSER_SPELLS = {
    'AatroxQ',
	'RenektonE',
    'AkaliR',
    'AkaliRb',
	'YasuoDashWrapper',
	"YasuoE",
    'Headbutt',
    'FioraQ',
	'FizzQ',
	"KaynQ",
	"AhriR",
    'DianaTeleport',
    'EliseSpiderQCast',
    'FizzPiercingStrike',
    'GragasE',
    "EkkoE",
    "Pounce",
    "GalioE",
    "GnarE",
    'HecarimUlt',
	'HecarimRamp',
    'IreliaQ',
    'JaxLeapStrike',
	'XinZhaoE',
	'XenZhaoE',
    'LeblancW',
    'LeblancRW',
    "CamilleE",
    "OrnnE",
    'BlindMonkQOne',
	'JayceShockBlast',
    'LeonaZenithBlade',
    'Pantheon_LeapBash',
    'PoppyHeroicCharge',
    'PoppyE',
    'RenektonSliceAndDice',
    'RivenTriCleave',
    'SejuaniQ',
    'slashCast',
    'ViQ',
    "ShenE",
    "TalonQ",
    "TristanaW",
    "TryndamereE",
    "UrgotE",
    "ZacE",
    'MonkeyKingNimbus',
    "WarwickR",
    "PykeE",
    "RiftWalk",
    'XenZhaoSweep',
    'YasuoDashWrapper'
	}
	
local Contain=function(table, value)
	for _, v in pairs(table) do
		if (v == value) then
			return true
		end
	end
	return false
end
-------------------
-- Menu creation --
-------------------


local MyMenu

function VaynePlugin.Load(GlobalMenu)
    MyMenu = GlobalMenu

    MyMenu.Key:keybind("aa", "Condemn on next AA", nil, "U")
    MyMenu.Key:keybind("poke", "Proc Silver Bolts", nil, "L")
    --MyMenu.Key:keybind("flash", "Flash-Condemn", "T", nil)
    MyMenu.Key:keybind("run", "Flee", "S", false)

    MyMenu:menu("combo", "Combo Settings")
    MyMenu.combo:header("xd", "Combo Settings")
    MyMenu.combo:header("xd", "Q Settings")
    MyMenu.combo:boolean("q", "Use Q", true)
    MyMenu.combo:boolean("range", "GapClose With Q", false)
    MyMenu.combo:boolean("stacks", "Q at 2 Stacks", true);
    MyMenu.combo:dropdown("mode", "Choose Mode: ", 1, {"To Mouse", "To Target"})
    MyMenu.combo:dropdown("qmode", "Choose Style: ", 1, {"Safe", "Aggresive"})

    MyMenu.combo:header("xd", "E Settings")
    MyMenu.combo:boolean("e", "Use E", true)
    MyMenu.combo:boolean("ward", "Auto-Trinket Bush", true)
    MyMenu.combo:slider('erange', "Max Condemn Distance", 380, 0, 450, 5);

    MyMenu.combo:header("xd", "R Settings")
    MyMenu.combo:boolean("r", "Use R", true)
    MyMenu.combo:keybind("aax", "Disable AA", nil, "A")
    MyMenu.combo:slider("rhp", "What HP% to Ult", 10, 0, 100, 5)
    MyMenu.combo:slider("rxe", "R if X Enemys in Range", 1, 0, 5, 1)
    --MyMenu.combo:slider("stealth", "Disable AA for X Seconds", 1, 0, 2, 1)

    MyMenu:menu("ks", "Killsteal Settings")
    MyMenu.ks:header("xd", "KillSteal Settings")
    MyMenu.ks:boolean("uks", "Use Killsteal", true)
    MyMenu.ks:boolean("ukse", "Use E on Killsteal", true)

    MyMenu:menu("auto", "Automatic Settings")
    MyMenu.auto:header("xd", "E Settings")
    MyMenu.auto:boolean("interrupt", "Auto E on Channeling Spells", true)
    MyMenu.auto:menu("gap", "Gapclose Settings")
    MyMenu.auto.gap:boolean('Enable', 'Enable Anti Gapclose', true)
    for i = 0, objManager.enemies_n - 1 do
        local obj = objManager.enemies[i]
        local Qname = obj:spellSlot(0).name
        local Wname = obj:spellSlot(1).name
        local Ename = obj:spellSlot(2).name
        local Rname = obj:spellSlot(3).name
        
        if (Contain(GAPCLOSER_SPELLS, Qname)) then
            MyMenu.auto.gap:boolean(Qname, obj.charName..'(Q)', true)
            elseif Qname=='JayceShockBlast' then
            MyMenu.auto.gap:boolean('JayceToTheSkies', obj.charName..'(Q2)', true)
        end
        if (Contain(GAPCLOSER_SPELLS, Wname)) then
            MyMenu.auto.gap:boolean(Wname, obj.charName..'(W)', true)
            
        end
        if (Contain(GAPCLOSER_SPELLS, Ename)) and Ename~='HecarimRamp' then
            MyMenu.auto.gap:boolean(Ename, obj.charName..'(E)', true)
        elseif Ename=='HecarimRamp' then
            MyMenu.auto.gap:boolean('HecarimRampAttack', obj.charName..'(E)', true)
        elseif Ename=='KhazixELong' or Ename=='KhazixE' then
            MyMenu.auto.gap:boolean('KhazixE', obj.charName..'(E)', true)
            MyMenu.auto.gap:boolean('KhazixELong', obj.charName..'(E)', true)
        end
        if (Contain(GAPCLOSER_SPELLS, Rname)) then
            MyMenu.auto.gap:boolean(Rname, obj.charName..'(R)', true)
        end
    end


    --[[
    MyMenu.auto:boolean("gapclose", "Use E For GapClosers", true)
    MyMenu.auto:menu("blacklist1", "Gapcloser Blacklist")
    local enemy = common.GetEnemyHeroes()
    for i, allies in ipairs(enemy) do
        MyMenu.auto.blacklist1:boolean(allies.charName, "Dont Use On: " .. allies.charName, false)
    end]]
    MyMenu.auto:boolean("auto", "Auto-Condemn Enemys", true)
    MyMenu.auto:menu("blacklist", "Auto-Condemn Blacklist")
    local enemy = common.GetEnemyHeroes()
    for i, allies in ipairs(enemy) do
        MyMenu.auto.blacklist:boolean(allies.charName, "Dont Use On: " .. allies.charName, false)
    end

    MyMenu:menu("draws", "Draw Settings")
    MyMenu.draws:header("xd", "Drawing Options")
    MyMenu.draws:boolean("q", "Draw Q + AA Range", true)
    MyMenu.draws:color("colorq", "^ color", 255, 233, 121, 121)
    MyMenu.draws:boolean("e", "Draw E Rnage", true)
    MyMenu.draws:color("colore", "^ color", 255, 233, 121, 121)
    MyMenu.draws:boolean("state", "Draw Condemn Point", true)
    MyMenu.draws:boolean("toggle", "Draw Toggle State", true)
end

----------------
-- Spell data --
----------------

local spells = {};

-- q data for preds input

spells.q = {
	delay = 0.25;
	radius = player.attackRange;
	speed = math.huge;
	dashRadius = 300;
	boundingRadiusModSource = 1;
    boundingRadiusModTarget = 1;
}

-- e data for preds input

spells.e = { 
	delay = 0.25; 
	radius = player.attackRange;
	speed = 2200; 
	dashRadius = 0;
	boundingRadiusModSource = 1;
    boundingRadiusModTarget = 1;
}

--------------------
-- Auto interrupt --
--------------------

spells.interrupt = {}

spells.interrupt.names = { -- names of dangerous spells
	"glacialstorm";
	"caitlynaceinthehole";
	"ezrealtrueshotbarrage";
	"drain";
	"crowstorm";
	"gragasw";
	"reapthewhirlwind";
	"karthusfallenone";
	"katarinar";
	"lucianr";
	"luxmalicecannon";
	"malzaharr";
	"meditate";
	"missfortunebullettime";
	"absolutezero";
	"pantheonrjump";
	"shenr";
	"gate";
	"varusq";
	"warwickr";
	"xerathlocusofpower2";
}

spells.interrupt.times = {6, 1, 1, 5, 1.5, 0.75, 3, 3, 2.5, 2, 0.5, 2.5, 4, 3, 3, 2, 3, 1.5, 4, 1.5, 3}; -- channel times of dangerous spells

-- On spell hook, used for interrupting


local interrupt_data = {};
local function on_spell(spell)
	if player:spellSlot(2).state == 0 then
		if not MyMenu.auto.interrupt:get() then return end
		if not spell or not spell.name or not spell.owner then return end
		if spell.owner.isDead then return end
		if spell.owner.team == player.team then return end
		if player.pos:dist(spell.owner.pos) > player.attackRange + (player.boundingRadius + spell.owner.boundingRadius) then return end	

		for i = 0, #spells.interrupt.names do
			if (spells.interrupt.names[i] == string.lower(spell.name)) then
				interrupt_data.start = os.clock();
				interrupt_data.channel = spells.interrupt.times[i];
				interrupt_data.owner = spell.owner;
			end
		end
	end
	if not MyMenu.auto.gap.Enable:get() or (spell and spell.owner and (spell.owner.team == player.team or spell.owner.type~=player.type)) then return end
	if player:spellSlot(2).state == 0 and (spell.target and spell.target==player) or (spell.endPos and spell.endPos:distSqr(player.path.serverPos)<=300^2) then		
		if MyMenu.auto.gap[spell.name] and MyMenu.auto.gap[spell.name]:get() then
			player:castSpell("obj", 2, spell.owner)
		end
	end
end



-- Interrupt stored dangerous spells w/ delay

local function interrupt()
	if not MyMenu.auto.interrupt:get() then return end
	if not interrupt_data.owner then return end
	if player.pos:dist(interrupt_data.owner.pos) > player.attackRange + (player.boundingRadius + interrupt_data.owner.boundingRadius) then return end
	
	if os.clock() - interrupt_data.channel >= interrupt_data.start then
		interrupt_data.owner = false;
		return
	end

	if os.clock() - 0.35 >= interrupt_data.start then
		player:castSpell("obj", 2, interrupt_data.owner);
		interrupt_data.owner = false;
	end
end

-------------------------------
-- Target selector functions --
-------------------------------
local function HasSionBuff(e)
	for i = 0, e.buffManager.count - 1 do
		local buff = e.buffManager:get(i)
		if buff and buff.valid and buff.name == 'sionpassivezombie' then
			return true
		end
	end
end

local function has_buff(unit, name)
	for i = 0, unit.buffManager.count - 1 do
    	local buff = unit.buffManager:get(i)
    	if buff and buff.valid and string.lower(buff.name) == name then
    		if game.time <= buff.endTime then
	      		return true, buff.stacks
    		end
    	end
  	end
  	return false, 0
end

local function get_stacks(unit)
	local buff, stacks = has_buff(unit, "vaynesilvereddebuff")
	if buff then
		return stacks;
	end
	return 0;
end

-- Used by target selector, without preds

local function select_target(res, obj, dist)
	if dist > 1000 then return end	
	res.obj = obj
	return true
end

-- Used by target selector, with q data preds

local function q_pred(res, obj, dist)
	if dist > 1000 then return end
	if preds.present.get_prediction(spells.q, obj) then
      	res.obj = obj
      	return true
    end
end

-- Used by target selector, with e data preds

local function e_pred(res, obj, dist)
	if dist > 1000 and obj.pos:dist(game.mousePos) <= 640 then return end
	if preds.present.get_prediction(spells.e, obj) then
      	res.obj = obj
      	return true
    end
end

-- Get target selector result

local function get_target()
	return ts.get_result(select_target).obj
end

local stackw = {50, 85, 120, 155, 195}
local pct = {0.04, 0.06, 0.09, 0.11, 0.013}
local function eDmg(target)
	if player.path.serverPos:dist(target.path.serverPos) < 800 then 
		local base_damage = (15 + (35 * player:spellSlot(0).level)) + (common.GetBonusAD() * 0.5)
		local stack = (30 + (70 * player:spellSlot(0).level)) + (common.GetBonusAD() *0.75)
		local total = base_damage
		return common.CalculatePhysicalDamage(target, total)
	end
end

local function wDmg(target)
	local stack = ((target.maxHealth * pct[player:spellSlot(1).level]) + stackw[player:spellSlot(1).level])
	return stack
end

local ShootRengar = false
local function LoadRengar()
	for i = 0, objManager.enemies_n - 1 do
		local enemy = objManager.enemies[i]
		if enemy and enemy.charName == "Rengar" then
			RengarHero = enemy
		end
	end
end

local function RengarObject(obj)
	if obj.type and RengarHero ~= nil and obj.name:lower():find("rengar_leapsound") and player.path.serverPos:dist(RengarHero.path.serverPos) < 1000 then
		ShootRengar = true
	end
end

local function GapCloserRengar()
	if player:spellSlot(2).state == 0 then
		if ShootRengar and not RengarHero.dead and RengarHero.health > 0 and player.path.serverPos:dist(RengarHero.path.serverPos) < 1000 then
			player:castSpell("obj", 2, RengarHero)
		else
			ShootRengar = false
		end
	end
end
---------------------
-- Combo functions --
---------------------

local checksPos = nil -- E position store, gets updated once condemn is called
local e_target = nil -- E target store, used to check if player is still visible
local last_e = os.clock(); -- Last E time store, updated every time E is casted

-- Check if player has buff, and if buff is valid


-- Return W stacks

local function get_stacks(unit)
	local buff, stacks = has_buff(unit, "vaynesilvereddebuff")
	if buff then
		return stacks;
	end
	return 0;
end

-- Condemn cast and logic

local function condemn(unit)
	--local obj = ts.get_result(e_pred).obj;
	local obj = unit
	local range = player.attackRange + (player.boundingRadius + unit.boundingRadius)
	if not obj then return end
	e_target = obj
	local p = preds.present.get_source_pos(obj)
	local unitPos = vec3(p.x, obj.y, p.y);
	local checks = math.ceil((MyMenu.combo.erange:get()) / 65)
	local checkDistance = (MyMenu.combo.erange:get()) / checks
	local InsideTheWall = false
	if player.pos:dist(unitPos) <= range and not unit.buff["blackshield"] and not unit.buff["sivire"] then
		for k=1, checks, 1 do
			checksPos = unitPos + (unitPos - player.pos):norm() * (checkDistance * k)
			last_e = os.clock()
			local WallContainsPosition = navmesh.isWall(checksPos)
			if WallContainsPosition then
	            InsideTheWall = true
	            break
	        end
		end
		if InsideTheWall then
			player:castSpell("obj", 2, unit)
			if checksPos and MyMenu.combo.ward:get() then
				local bushPos = navmesh.isGrass(checksPos)
				if bushPos then
					for i = 6, 12 do
						local item = player:spellSlot(i).name
						if item and item == "TrinketTotemLvl1" or item == "ItemGhostWard" or item == "JammerDevice" or item == "TrinketOrbLvl3" and player:spellSlot(i).state == 0 then
							player:castSpell("pos", i, checksPos)
							break
						end
					end	
				end
			end
		end
	else
		checksPos = nil
	end
end


-- Condemn on next AA toggle

local function condemn_next_aa(unit)
	if not MyMenu.Key.aa:get() then return end 
	if player:spellSlot(2).state ~= 0 then return end

	if orb.combat.target and orb.core.can_attack() then
		player:castSpell("obj", 2, unit)
  	end
end

-- Condemn on enemy gapclose

--[[local function gapclose()
	if not MyMenu.auto.gapclose:get() then return end
	if player:spellSlot(2).state ~= 0 then return end

	local obj = ts.get_result(e_pred).obj;
	if not obj or not obj.path.isActive or not obj.path.isDashing then return end

	local range = player.attackRange + (player.boundingRadius + obj.boundingRadius)
	if player.pos:dist(obj.pos) > range then return end
	
	local pred_pos = preds.core.lerp(obj.path, network.latency + spells.e.delay, obj.path.dashSpeed)
	if not pred_pos then return end

	if pred_pos:dist(player.pos2D) <= range then
		player:castSpell("obj", 2, obj)
	end
end]]

local function gapclose()
	if player:spellSlot(2).state == 0 and MyMenu.auto.gap.Enable:get() then
		for i = 0, objManager.enemies_n - 1 do
			local dasher = objManager.enemies[i]
			if dasher.type == TYPE_HERO and dasher.team == TEAM_ENEMY then
				if dasher and dasher.charName == "Rengar" and common.IsValidTarget(dasher) and dasher.path.isActive and dasher.path.isDashing and player.pos:dist(dasher.path.point[1]) < 550 then
					--[[
						if MyMenu.auto.blacklist1[dasher.charName] and not MyMenu.auto.blacklist1[dasher.charName]:get() then
							if player.pos2D:dist(dasher.path.point2D[1]) < player.pos2D:dist(dasher.path.point2D[0]) and not dasher.buff["threshq"] then
								player:castSpell("obj", 2, dasher)
							end
						end]]
					if dasher.path.dashSpeed == 1450 and player.path.serverPos:dist(dasher.path.serverPos) < 550 then
						player:castSpell("obj", 2, dasher)
					end
				end
			end
		end
	end
end

-- Roll cast with range & stack check

local function roll()
	if MyMenu.combo.q:get() and (MyMenu.Key.Combo:get() or MyMenu.Key.Harass:get()) then
		if MyMenu.combo.mode:get() == 1 then
			local target = get_target();
			if not target then return end
			local p = preds.present.get_source_pos(target)
			local unitPos = vec3(p.x, target.y, p.y);
			local range = player.attackRange + (player.boundingRadius + target.boundingRadius)
			local AfterTumblePos = player.pos + (game.mousePos - player.pos):norm() * 300
			local DistanceAfterTumble = AfterTumblePos:dist(target.pos)

			if orb.combat.target then
					
				if MyMenu.Key.poke:get() and get_stacks(target) == 2 and player:spellSlot(0).state ~= 0 then
					if player:spellSlot(2).state ~= 0 then return end
					player:castSpell("obj", 2, target)
				end

				if player:spellSlot(0).state ~= 0 then return end

				if MyMenu.combo.qmode:get() == 1 then
					if MyMenu.combo.stacks:get() and (get_stacks(target) ~= 2 and not MyMenu.Key.poke:get()) then return end
					if MyMenu.Key.poke:get() and get_stacks(target) ~= 1 then return end
					if DistanceAfterTumble < 650 and DistanceAfterTumble > 100 then
						player:castSpell("pos", 0, game.mousePos)
						orb.core.reset()
						orb.combat.set_invoke_after_attack(false)
					end
					if player.pos:dist(target.pos) > 650 and DistanceAfterTumble < 650 then
						player:castSpell("pos", 0, game.mousePos)
						orb.core.reset()
						orb.combat.set_invoke_after_attack(false)
					end
				end
				if MyMenu.combo.qmode:get() == 2 then
					if MyMenu.combo.stacks:get() and (get_stacks(target) ~= 2 and not MyMenu.Key.poke:get()) then return end
					if MyMenu.Key.poke:get() and get_stacks(target) ~= 1 then return end
					player:castSpell("pos", 0, game.mousePos)
					orb.core.reset()
					orb.combat.set_invoke_after_attack(false)
				end
			end
		end
	end
end

-- Roll cast when target steps out of aa range

local function out_of_aa()
	if not MyMenu.Key.Combo:get() then return end
	if not MyMenu.combo.range:get() then return end

	local obj = ts.get_result(q_pred).obj
    if obj then
    	local range = 300 + player.attackRange + (player.boundingRadius + obj.boundingRadius)
		if obj.pos:dist(player.pos) <= range then

			if MyMenu.Key.poke:get() and get_stacks(obj) ~= 1 then return end

			local p = preds.present.get_source_pos(obj)
			local dashpos = game.mousePos; 
			--if evade.core.is_action_safe(dashpos, math.huge, 0.25) then
				--player:castSpell("pos", 0, dashpos)
			--end
		end
	end
end

local function CastQ(target)
	if player:spellSlot(0).state == 0 and vec3(target.x, target.y, target.z):dist(player) < player.attackRange + (player.boundingRadius + target.boundingRadius) and target.pos:dist(player.pos) > 300 and not navmesh.isWall(target.pos) then
		player:castSpell("pos", 0, target.pos)
	end
end

local function CastR()
	if MyMenu.combo.r:get() and player:spellSlot(3).state == 0 then
		if #common.GetEnemyHeroesInRange(500) >= MyMenu.combo.rxe:get() and common.GetPercentHealth() <=  MyMenu.combo.rhp:get() then
			player:castSpell("self", 3)
		end
	end
end

-- Combo function to call each cast

local function combo()
	local target = get_target(select_target)
	if target and common.IsValidTarget(target) and not target.buff["sionpassivezombie"] then
		if MyMenu.combo.mode:get() == 2 then
			CastQ(target)
		end
		if MyMenu.combo.e:get() and player:spellSlot(2).state == 0 then
			condemn(target)
		end
		if MyMenu.Key.aa:get() then
			condemn_next_aa(target)
		end
	end
end

local function KillSteal()
	for i = 0, objManager.enemies_n - 1 do
		local enemy = objManager.enemies[i]
		local d = player.path.serverPos:dist(enemy.path.serverPos)
 		if enemy and common.IsValidTarget(enemy) and MyMenu.ks.uks:get() and not HasSionBuff(enemy) and d < 800 then
	  		if MyMenu.ks.ukse:get() and player:spellSlot(2).state == 0 and d < 800 and #common.GetEnemyHeroesInRange(800) <= 2 and enemy.health < eDmg(enemy) then
	  			player:castSpell("obj", 2, enemy)
	  		elseif MyMenu.ks.ukse:get() and player:spellSlot(2).state == 0 and player:spellSlot(1).level > 0 and get_stacks(enemy) == 2 and d < 800 and #common.GetEnemyHeroesInRange(800) <= 2 and enemy.health < eDmg(enemy) + wDmg(enemy) then
	  			player:castSpell("obj", 2, enemy)
	  		end
  		end
 	end
end


local function AutoCondemn()
	if player:spellSlot(2).state == 0 then
		for i = 0, objManager.enemies_n - 1 do
			local enemy = objManager.enemies[i]
			local d = player.path.serverPos:dist(enemy.path.serverPos)
			if enemy and common.IsValidTarget(enemy) and d < 800 then
				if MyMenu.auto.blacklist[enemy.charName] and not MyMenu.auto.blacklist[enemy.charName]:get() then
					condemn(enemy)
				end
			end
		end
	end
end



local function Run()
	if MyMenu.Key.run:get() then
		player:move((game.mousePos))
		if player:spellSlot(0).state == 0 and not navmesh.isWall(game.mousePos) then
			player:castSpell("pos", 0, (game.mousePos))
		end
	end
end 


-----------
-- Hooks --
-----------

-- Called pre tick

local function ontick()
	if MyMenu.Key.run:get() then Run() end
	--if MyMenu.Key.flash:get() then FlashE() end
	if MyMenu.Key.Combo:get() then combo() end
	if MyMenu.ks.uks:get() then KillSteal() end
	if MyMenu.auto.auto:get() then AutoCondemn() end
	if MyMenu.Key.Combo:get() and MyMenu.combo.r:get() then CastR() end
	interrupt();
	gapclose();
	LoadRengar()
	GapCloserRengar()
	if MyMenu.combo.aax:get() and (MyMenu.Key.Combo:get()) and orb.combat.target then
		if player.buff["vaynetumblefade"] then
			orb.core.set_pause_attack(1)
		end
	end
	if MyMenu.Key.Combo:get() and orb.combat.target then
		if not player.buff["vaynetumblefade"] then
			orb.core.set_pause_attack(0)
		end
	end
	if not MyMenu.Key.Combo:get() and not MyMenu.Key.LastHit:get() and not MyMenu.Key.LaneClear:get() then
		if orb.core.is_attack_paused() then
			orb.core.set_pause_attack(0)
		end
	end
	if MyMenu.Key.Combo:get() and not player.buff["vayneinquisition"] then
		orb.core.set_pause_attack(0)
	end
end

-- Draw hook, only used to draw predicted e position

local function ondraw()
	if MyMenu.draws.toggle:get() then
		if player.isOnScreen then
			local pos = graphics.world_to_screen(vec3(player.x-70, player.y, player.z-150))
	        local pos2 = graphics.world_to_screen(vec3(player.x-70, player.y-25, player.z-150))
			if MyMenu.Key.aa:get() then
				graphics.draw_text_2D("Condemn Next AA", 14, pos.x, pos.y, graphics.argb(255,255,255,255))
			elseif MyMenu.Key.poke:get() then
				graphics.draw_text_2D("Proc Silver Bolts", 14, pos.x, pos.y, graphics.argb(255,255,255,255))
			end
			if MyMenu.combo.aax:get() then
				graphics.draw_text_2D("Stealth AA: On", 14, pos2.x, pos2.y, graphics.argb(255,255,255,255))
			else
				graphics.draw_text_2D("Stealth AA: Off", 14, pos2.x, pos2.y, graphics.argb(255,255,255,255))
			end
		end
	end
	if MyMenu.draws.state:get() then
		if player:spellSlot(2).state == 0 and checksPos and last_e > os.clock() - 3 and player.isOnScreen then
			if e_target and player.pos:dist(e_target) < 600 and not e_target.isOnScreen then return end
			graphics.draw_circle(checksPos, 30, 3, graphics.argb(255,255,255,255), 100)
			graphics.draw_circle(e_target, 30, 3, graphics.argb(255,255,255,255), 100)
		end
	end
	if MyMenu.draws.q:get() and player.isOnScreen and player:spellSlot(0).state == 0 then
		graphics.draw_circle(player.pos, 300 + player.attackRange + (player.boundingRadius), 2, MyMenu.draws.colorq:get(), 50)
	end
	if MyMenu.draws.e:get() and player.isOnScreen and player:spellSlot(2).state == 0 then
		graphics.draw_circle(player.pos, 610, 2, MyMenu.draws.colore:get(), 50)
	end

end

-- Cast spell hook, for toggling poke combo off

local function cast_spell(slot, vec3, vec3, networkID)
	if slot == 2 then
		if MyMenu.Key.poke:get() then
			MyMenu.Key.poke:set("toggleValue", false)
		end

		if MyMenu.Key.aa:get() then
			MyMenu.Key.aa:set("toggleValue", false)
		end
	end
end

cb.add(cb.draw, ondraw)
cb.add(cb.spell, on_spell)
cb.add(cb.castspell, cast_spell)
cb.add(cb.create_particle, RengarObject)

orb.combat.register_f_pre_tick(ontick)
orb.combat.register_f_after_attack(roll)
orb.combat.register_f_out_of_range(out_of_aa)

return VaynePlugin

local ove_0_10 = module.load(header.id, "common/Notification/table")
local ove_0_11 = {
	w = 265,
	p = 10,
	h = 70,
	fs = 24
}

ove_0_11.sx = graphics.width - ove_0_11.w - ove_0_11.p
ove_0_11.sy = 0.25 * graphics.height - ove_0_11.h
ove_0_11.my = graphics.height * 0.75 + ove_0_11.h

local ove_0_12

ove_0_12 = {
	__c = 4280229663,
	__n = 0,
	__t = {},
	__bounds = ove_0_11,
	__init = function()
		if not ove_0_12.__addedCb then
			for iter_5_0, iter_5_1 in pairs(ove_0_12.__cb) do
				cb.add(cb[iter_5_0], iter_5_1)
			end
		end
	end,
	__cb = {
		draw = function()
			for iter_6_0 = 1, ove_0_12.__n do
				local slot_6_0 = ove_0_12.__t[iter_6_0]
				local slot_6_1

				if slot_6_0.__timeDiv < 0.75 then
					slot_6_1 = math.cos(slot_6_0.__timeDiv * math.pi)
				elseif slot_6_0.__timeDiv > 1.25 then
					slot_6_1 = math.cos((2 - slot_6_0.__timeDiv) * math.pi)
				else
					slot_6_1 = math.cos(0.75 * math.pi)
				end

				local slot_6_2 = ove_0_11.sx + (ove_0_11.w + ove_0_11.p) * slot_6_1
				local slot_6_3 = ove_0_11.sy + ove_0_11.h * 0.5 + (ove_0_11.h + ove_0_11.p) * iter_6_0

				graphics.draw_line_2D(slot_6_2, slot_6_3, slot_6_2 + ove_0_11.w, slot_6_3, ove_0_11.h, slot_6_0.__c)

				local slot_6_4 = graphics.text_area(slot_6_0.__text, ove_0_11.fs)

				graphics.draw_sprite(slot_6_0.__object.iconCircle, vec3(slot_6_2 + 10, slot_6_3 - 30, 0), 0.5, 4294967295)
				graphics.draw_text_2D(slot_6_0.__text, ove_0_11.fs, slot_6_2 + ove_0_11.w * 0.5 - slot_6_4 * 0.5 + 40, slot_6_3, 4293260551)
			end
		end,
		tick = function()
			local slot_7_0 = game.time
			local slot_7_1 = {}
			local slot_7_2 = 0

			for iter_7_0 = 1, ove_0_12.__n do
				local slot_7_3 = ove_0_12.__t[iter_7_0]

				if game.time - slot_7_3.__startTime > slot_7_3.__totalTime then
					slot_7_1[#slot_7_1 + 1] = iter_7_0

					if slot_7_3.__done then
						slot_7_3.__done()
					end
				else
					slot_7_3.__timeDiv = 2 * (slot_7_0 - slot_7_3.__startTime) / slot_7_3.__totalTime
				end
			end

			for iter_7_1 = 1, #slot_7_1 do
				ove_0_10.remove(ove_0_12.__t, slot_7_1[iter_7_1] - slot_7_2)

				slot_7_2 = slot_7_2 + 1
			end

			ove_0_12.__n = #ove_0_12.__t
		end
	},
	add = function(arg_8_0, arg_8_1, arg_8_2, arg_8_3, arg_8_4)
		ove_0_12.__init()

		ove_0_12.__n = ove_0_12.__n + 1
		ove_0_12.__t[ove_0_12.__n] = {
			__timeDiv = 0,
			__text = arg_8_0,
			__object = arg_8_1,
			__startTime = game.time,
			__totalTime = arg_8_2 or 3,
			__done = arg_8_4,
			__c = arg_8_3 or ove_0_12.__c
		}
	end
}

return ove_0_12

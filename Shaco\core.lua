local ove_0_10 = module.load(header.id, "draw/info")
local ove_0_11 = module.load(header.id, "draw/circle")
local ove_0_12 = module.load(header.id, "Shaco/common")
local ove_0_13 = module.internal("orb")
local ove_0_14 = module.internal("TS")
local ove_0_15 = false
local ove_0_16 = false
local ove_0_17
local ove_0_18 = module.load(header.id, "Shaco/dmg")

if hanbot.language == 1 then
	print("Chinese Menu Loaded")

	ove_0_17 = module.load(header.id, "Shaco/cnmenu")
else
	ove_0_17 = module.load(header.id, "Shaco/menu")
end

local ove_0_19 = player:spellSlot(0)
local ove_0_20 = player:spellSlot(1)
local ove_0_21 = player:spellSlot(2)
local ove_0_22 = player:spellSlot(3)
local ove_0_23 = module.load(header.id, "Shaco/q")
local ove_0_24 = module.load(header.id, "Shaco/w")
local ove_0_25 = module.load(header.id, "Shaco/e")
local ove_0_26 = module.load(header.id, "Shaco/r")
local ove_0_27 = module.seek("evade")

local function ove_0_28(arg_5_0, arg_5_1, arg_5_2)
	if player.isDead and arg_5_2 < 2000 then
		arg_5_0.obj = arg_5_1

		return true
	end
end

local function ove_0_29()
	return ove_0_14.get_result(ove_0_28, ove_0_14.filter_set[7]).obj
end

local function ove_0_30()
	if player:spellSlot(0).name ~= "HallucinateFull" then
		local slot_7_0 = ove_0_29()

		if slot_7_0 and slot_7_0.isVisible and ove_0_12.IsValidTarget(slot_7_0) then
			player:castSpell("pos", _R, slot_7_0.pos)
			print("Clone found enemy grrr")
		end
	end
end

local function ove_0_31()
	ove_0_30()

	local slot_8_0 = ove_0_23.get_action_state()
	local slot_8_1 = ove_0_24.get_action_state()
	local slot_8_2 = ove_0_25.get_action_state()
	local slot_8_3 = ove_0_26.get_action_state()

	if not ove_0_15 then
		--print("checking evade.", ove_0_27)

		ove_0_15 = true

		if ove_0_27 == nil then
			--print("NO EVADE MODULE FOUND PLEASE ENABLE EVADE ")
			-- chat.add("[Alexis AIO] ", {
				-- color = "#fff600",
				-- bold = false,
				-- italic = false
			-- })
			-- chat.add("NO EVADE FOUND PLEASE LOAD EVADE V2", {
				-- bold = true,
				-- color = COLOR_RED
			-- })
			-- chat.print()

			totalDmg = 100
		elseif ove_0_27.menu.text == "Hanbot Evade 2" then
			ad_damage, ap_damage, true_damage, buff_list = ove_0_27.damage.count(player)
			totalDmg = ad_damage + ap_damage + true_damage
			ove_0_16 = true
		else
			-- chat.add("[Alexis AIO] ", {
				-- color = "#CC5544CC",
				-- bold = false,
				-- italic = false
			-- })
			-- --chat.add("Enabled Viktor Evade or Evade 2.0 for advanced W usage", {
				-- color = "#CC5544CC",
				-- bold = true
			-- })
			-- chat.print()

			totalDmg = 0
			ove_0_16 = false
		end
	end

	if ove_0_17.combat:get() then
		if ove_0_17.combatv2:get() and player.buff.deceive then
			print("lul")

			return
		end

		if slot_8_2 and ove_0_17.enable_e:get() then
			ove_0_25.invoke_action()
		end

		if slot_8_0 and ove_0_17.enable_q:get() then
			ove_0_23.invoke_action()
		end

		if slot_8_1 and ove_0_17.enable_w:get() then
			ove_0_24.invoke_action()
		end
	end

	if ove_0_17.farm:get() then
		if ove_0_20.state == 0 and ove_0_24.get_farm_state() and ove_0_17.enable_wf:get() then
			ove_0_24.invoke_farm()
		end

		if ove_0_21.state == 0 and ove_0_25.get_farm_state() and ove_0_17.enable_ef:get() then
			ove_0_25.invoke_farm()
		end
	end

	if ove_0_16 then
		if ove_0_27.core.skillshots.n ~= nil then
			for iter_8_0 = ove_0_27.core.skillshots.n, 1, -1 do
				local slot_8_4 = ove_0_27.core.skillshots[iter_8_0]

				if slot_8_4:contains(player) then
					for iter_8_1, iter_8_2 in pairs(ove_0_12.CCSpells) do
						if ove_0_17.blockr[iter_8_2.charName] and slot_8_4.name:find(iter_8_1) and ove_0_17.blockr[iter_8_2.charName][iter_8_1].wblock:get() and ove_0_17.blockr[iter_8_2.charName][iter_8_1].hp:get() >= player.health / player.maxHealth * 100 then
							ove_0_26.invoke_action()
						end
					end
				end
			end
		elseif not ove_0_15 then
			ove_0_15 = true

			-- chat.add("[Alexis AIO] ", {
				-- color = "#CC5544CC",
				-- bold = false,
				-- italic = false
			-- })
			-- chat.add("Enabled Viktor Evade or Evade 2.0 for advanced W usage", {
				-- color = "#CC5544CC",
				-- bold = true
			-- })
			-- chat.print()
		end
	end

	if ove_0_17.combat:get() then
		return
	end
end

function DrawDamagesE(arg_9_0)
	if player.levelRef >= 6 and ove_0_18.r(player) ~= 0 and arg_9_0.isVisible and not arg_9_0.isDead and arg_9_0.type == player.type and arg_9_0.team ~= player.team and arg_9_0.isOnScreen then
		local slot_9_0 = arg_9_0.barPos + vec2(109, 111)
		local slot_9_1 = slot_9_0.x + 55
		local slot_9_2 = slot_9_0.y + 11.5

		if arg_9_0.charName == "Annie" then
			local slot_9_3 = slot_9_2 + 2
		end

		Qdmg = 0
		Wdmg = 0

		if ove_0_18.r(arg_9_0) then
			Edmg = ove_0_18.r(arg_9_0)
		else
			Edmg = 0
		end

		local slot_9_4 = arg_9_0.health - (Qdmg + Wdmg + Edmg)
		local slot_9_5 = slot_9_1 + arg_9_0.health / arg_9_0.maxHealth * 102
		local slot_9_6 = slot_9_1 + (slot_9_4 > 0 and slot_9_4 or 0) / arg_9_0.maxHealth * 102
	end
end

local ove_0_32 = "Auto Q Enabled"

local function ove_0_33()
	if not player.isOnScreen or player.isDead then
		return
	end

	if ove_0_17.range.rdamage:get() then
		local slot_10_0 = ove_0_12.GetEnemyHeroes()

		for iter_10_0, iter_10_1 in ipairs(slot_10_0) do
			if iter_10_1 and ove_0_12.IsValidTarget(iter_10_1) and iter_10_1.isOnScreen then
				DrawDamagesE(iter_10_1)
			end
		end
	end
end

local function ove_0_34()
	if not player.isOnScreen or player.isDead then
		return
	end

	local slot_11_0 = ove_0_17.range.c1:get()
	local slot_11_1 = ove_0_17.range.c2:get()
	local slot_11_2 = ove_0_17.range.c3:get()
	local slot_11_3 = ove_0_17.range.c4:get()

	if ove_0_17.range.q:get() then
		ove_0_11.on_draw(400, 10, player.pos.xzz, slot_11_0)
	end

	if ove_0_17.range.w:get() then
		ove_0_11.on_draw(500, 10, player.pos.xzz, slot_11_1)
	end

	if ove_0_17.range.e:get() then
		ove_0_11.on_draw(350, 10, player.pos.xzz, slot_11_2)
	end

	if ove_0_17.range.r:get() then
		ove_0_11.on_draw(500, 10, player.pos.xzz, slot_11_3)
	end
end

-- 添加获取菜单函数
function get_menu()
	return ove_0_17
end

function get_action()
	ove_0_31()
end

function on_draw_info()
	ove_0_33()
end

function on_draw_range()
	ove_0_34()
end

-- 导出函数
return {
	get_menu = get_menu,
	get_action = get_action,
	on_draw_info = on_draw_info,
	on_draw_range = on_draw_range
}



local ove_0_10 = module.internal("orb")
local ove_0_11 = module.seek("evade")
local ove_0_12 = module.load(header.id, "plugins/Swain/spells/q")
local ove_0_13 = module.load(header.id, "plugins/Swain/spells/w")
local ove_0_14 = module.load(header.id, "plugins/Swain/spells/e")
local ove_0_15 = module.load(header.id, "plugins/Swain/spells/e_recast")
local ove_0_16 = module.load(header.id, "plugins/Swain/spells/r")
local ove_0_17 = module.load(header.id, "plugins/Swain/spells/demonflare")
local ove_0_18 = module.load(header.id, "plugins/Swain/menu")
local ove_0_19 = module.load(header.id, "plugins/Swain/damage")
local ove_0_20 = module.load(header.id, "plugins/Swain/lock")
local ove_0_21 = module.load(header.id, "plugins/Swain/helper")
local ove_0_22 = module.load(header.id, "common/common")
local ove_0_23 = {
	on_end_time = 0,
	f_spell_map = {}
}

function ove_0_23.on_end_q()
	ove_0_23.on_end_func = nil

	ove_0_10.core.set_pause(0)
end

function ove_0_23.on_end_w()
	ove_0_23.on_end_func = nil

	ove_0_10.core.set_pause(0)
end

function ove_0_23.on_end_e()
	ove_0_23.on_end_func = nil

	ove_0_10.core.set_pause(0)
end

function ove_0_23.on_end_e2()
	ove_0_23.on_end_func = nil

	ove_0_10.core.set_pause(0)
end

function ove_0_23.on_cast_q(arg_9_0)
	if os.clock() + arg_9_0.windUpTime > ove_0_23.on_end_time then
		ove_0_23.on_end_func = ove_0_23.on_end_q
		ove_0_23.on_end_time = os.clock() + arg_9_0.windUpTime

		ove_0_10.core.set_pause(math.huge)
	end
end

function ove_0_23.on_cast_w(arg_10_0)
	if os.clock() + arg_10_0.windUpTime > ove_0_23.on_end_time then
		ove_0_23.on_end_func = ove_0_23.on_end_w
		ove_0_23.on_end_time = os.clock() + arg_10_0.windUpTime

		ove_0_10.core.set_pause(math.huge)
	end
end

function ove_0_23.on_cast_e(arg_11_0)
	if os.clock() + arg_11_0.windUpTime > ove_0_23.on_end_time then
		ove_0_23.on_end_func = ove_0_23.on_end_e
		ove_0_23.on_end_time = os.clock() + arg_11_0.windUpTime

		ove_0_10.core.set_pause(math.huge)
	end
end

function ove_0_23.on_cast_e2(arg_12_0)
	if os.clock() + arg_12_0.windUpTime > ove_0_23.on_end_time then
		ove_0_23.on_end_func = ove_0_23.on_end_e2
		ove_0_23.on_end_time = os.clock() + arg_12_0.windUpTime

		ove_0_10.core.set_pause(math.huge)
	end
end

function ove_0_23.get_action()
	if ove_0_23.on_end_func and os.clock() + network.latency > ove_0_23.on_end_time then
		ove_0_23.on_end_func()
	end

	if ove_0_18.misc.evade.disable_evade_with_r:get() and player.buff.swainr and ove_0_11 then
		ove_0_11.core.set_pause(math.huge)
	end

	if not player.buff.swainr and not ove_0_20.isMovement and ove_0_11 then
		ove_0_11.core.set_pause(0)
	end

	if ove_0_18.misc.mov.use_magnet:get() and ove_0_20.MovementeLock() then
		return
	end

	if ove_0_20.isMovement then
		if ove_0_11 then
			ove_0_11.core.set_pause(math.huge)
		end

		ove_0_10.core.set_pause_attack(math.huge)
	end

	if ove_0_18.flee.flee_key:get() then
		player:move(game.mousePos)
	end

	if ove_0_18.misc.auto.auto_w:get() and ove_0_13.is_ready() and ove_0_13.invoke_auto_cced() then
		return
	end

	if ove_0_18.misc.auto.auto_e:get() and ove_0_14.is_ready() and ove_0_14.invoke_auto_cced() then
		return
	end

	if ove_0_18.misc.interrupt.use_e:get() and ove_0_14.is_ready() and ove_0_14.invoke_interrupt_spells() then
		return
	end

	if ove_0_18.misc.interrupt.use_e:get() and ove_0_15.is_ready() and ove_0_15.interrupt_now() then
		ove_0_21.interrupt_data.owner = false

		return
	end

	if ove_0_18.misc.kill.use_w:get() and ove_0_13.is_ready() and ove_0_13.invoke_w_killsteal() then
		return
	end

	if ove_0_18.misc.kill.use_e:get() and ove_0_14.is_ready() and ove_0_14.invoke_e_killsteal() then
		return
	end

	if ove_0_18.misc.kill.use_q:get() and ove_0_12.is_ready() and ove_0_12.invoke_q_killsteal() then
		return
	end

	if ove_0_17.is_ready() and ove_0_17.invoke_r_killsteal() then
		return
	end

	if ove_0_10.menu.combat.key:get() then
		if ove_0_18.combo.w.w_mode:get() == 2 and ove_0_13.invoke_action() and ove_0_13.is_ready() then
			return
		end

		if ove_0_18.combo.e.use_e:get() and ove_0_22.GetPercentMana(player) >= ove_0_18.combo.e.min_mana:get() and ove_0_14.get_action_state() then
			ove_0_14.invoke_action()

			return
		end

		if ove_0_18.combo.e.use_e:get() and ove_0_22.GetPercentHealth(player) >= ove_0_18.combo.e.pull:get() and ove_0_15.get_action_state() then
			ove_0_15.invoke_action()

			return
		end

		if ove_0_18.combo.w.w_mode:get() ~= 3 and ove_0_22.GetPercentHealth(player) >= ove_0_18.combo.w.min_mana:get() and ove_0_13.get_action_state() and ove_0_18.combo.w.w_mode:get() == 1 and ove_0_13.invoke_auto_cced() then
			return
		end

		if ove_0_18.combo.q.use_q:get() and ove_0_22.GetPercentHealth(player) >= ove_0_18.combo.q.min_mana:get() and ove_0_12.get_action_state() then
			ove_0_12.invoke_action()

			return
		end

		if ove_0_18.combo.r.use_r:get() and ove_0_22.GetPercentHealth(player) >= ove_0_18.combo.r.min_mana:get() and ove_0_16.get_action_state() then
			ove_0_16.invoke_action()

			return
		end
	end

	if ove_0_10.menu.hybrid.key:get() then
		if ove_0_18.harass.q.use_q:get() and ove_0_22.GetPercentHealth(player) >= ove_0_18.harass.q.min_mana:get() and ove_0_12.get_action_state() then
			ove_0_12.invoke_action()

			return
		end

		if ove_0_18.harass.e.use_e:get() and ove_0_22.GetPercentMana(player) >= ove_0_18.harass.e.min_mana:get() and ove_0_14.get_action_state() then
			ove_0_14.invoke_action()

			return
		end

		if ove_0_18.combo.e.use_e:get() and ove_0_22.GetPercentHealth(player) >= ove_0_18.combo.e.pull:get() and ove_0_15.get_action_state() then
			ove_0_15.invoke_action()

			return
		end
	end

	if ove_0_18.farming.toggleFarm:get() then
		if ove_0_22.GetPercentMana(player) < ove_0_18.farming["mana.percent"]:get() and (not player.buff.crestoftheancientgolem or player.buff.crestoftheancientgolem ~= nil and not ove_0_18.farming.Ignoreharassmana:get()) then
			return
		end

		if ove_0_10.menu.last_hit.key:get() and ove_0_18.farming.last.q.use_q:get() and ove_0_12.is_ready() and ove_0_12.invoke_last_hit() then
			return
		end

		if ove_0_10.menu.lane_clear.key:get() then
			if ove_0_18.farming.lane.w.use_w:get() and ove_0_13.is_ready() and ove_0_13.invoke_lane_hit() then
				return
			end

			if ove_0_18.farming.lane.q.use_q:get() and ove_0_12.is_ready() and ove_0_12.invoke_lane_hit() then
				return
			end

			if ove_0_18.farming.jungle.q.use_q:get() and ove_0_12.is_ready() and ove_0_12.invoke_jungle_clear() then
				return
			end
		end
	end
end

function ove_0_23.on_recv_spells(arg_14_0)
	if ove_0_23.f_spell_map[arg_14_0.name] then
		ove_0_23.f_spell_map[arg_14_0.name](arg_14_0)
	end
end

ove_0_23.f_spell_map.SwainQ = ove_0_23.on_cast_q
ove_0_23.f_spell_map.SwainW = ove_0_23.on_cast_w
ove_0_23.f_spell_map.SwainE = ove_0_23.on_cast_e
ove_0_23.f_spell_map.SwainE2 = ove_0_23.on_cast_e2

return ove_0_23

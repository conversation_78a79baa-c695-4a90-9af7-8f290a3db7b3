local ove_0_10 = module.internal("orb")
local ove_0_11 = module.load(header.id, "common/Draw/draw")
local ove_0_12 = module.load(header.id, "common/common")
local ove_0_13 = module.load(header.id, "plugins/KSante/menu")
local ove_0_14 = module.load(header.id, "plugins/KSante/damage")
local ove_0_15 = module.load(header.id, "plugins/KSante/lock")
local ove_0_16 = module.load(header.id, "plugins/KSante/core")
local ove_0_17 = module.load(header.id, "plugins/KSante/spells/q")
local ove_0_18 = module.load(header.id, "plugins/KSante/spells/q3")
local ove_0_19 = module.load(header.id, "plugins/KSante/spells/w")
local ove_0_20 = module.load(header.id, "plugins/KSante/spells/e")
local ove_0_21 = module.load(header.id, "plugins/KSante/spells/r")
local ove_0_22 = module.load(header.id, "plugins/KSante/helper")

local function ove_0_23()
	ove_0_16.get_action_tick()
end

local function ove_0_24(arg_6_0)
	local slot_6_0 = arg_6_0.name
	local slot_6_1 = arg_6_0.owner

	if slot_6_0 == "KSanteW" then
		ove_0_19.LastCastTime = game.time
	end

	if not ove_0_22.GetSanteTranformation() then
		if arg_6_0 and slot_6_1.type == TYPE_HERO and slot_6_1.team == TEAM_ENEMY then
			local slot_6_2 = string.lower(slot_6_1.charName)

			if ove_0_12.interrupt_spells[slot_6_2] then
				for iter_6_0 = 1, #ove_0_12.interrupt_spells[slot_6_2] do
					local slot_6_3 = ove_0_12.interrupt_spells[slot_6_2][iter_6_0]

					if slot_6_3 and slot_6_3.spellname == string.lower(slot_6_0) and slot_6_3.slot == arg_6_0.slot and not slot_6_1.buff.karthusdeathdefiedbuff and ove_0_13.misc.interrupt.interruptmenu_q[slot_6_1.charName .. slot_6_3.menuslot]:get() then
						ove_0_19.interrupt_data.start = game.time - network.latency
						ove_0_19.interrupt_data.channel = slot_6_3.channelduration
						ove_0_19.interrupt_data.owner = slot_6_1
					end
				end
			end
		end

		if slot_6_1.team == TEAM_ENEMY and ove_0_13.w_block.auto_w:get() then
			ove_0_19.spell_on_enemy(arg_6_0)
		end
	end
end

local function ove_0_25()
	if player.isDead then
		return
	end

	if ove_0_13.draws.drawqDamage:get() then
		ove_0_14.over_layer()
	end

	if ove_0_13.draws.drawM:get() and (ove_0_10.combat.is_active() or ove_0_10.menu.hybrid.key:get()) then
		graphics.draw_circle(game.mousePos, ove_0_13.misc.move.magR:get(), 1, graphics.argb(255, 255, 255, 0), 40)
	end

	if ove_0_13.draws.useq:get() and player:spellSlot(_Q).state == 0 then
		graphics.draw_circle(player.pos, ove_0_17.range, 1, ove_0_13.draws.q:get(), 100)
	end

	if ove_0_13.draws.usew:get() and player:spellSlot(_W).state == 0 then
		graphics.draw_circle(player.pos, ove_0_19.range, 1, ove_0_13.draws.w:get(), 100)
	end

	local slot_7_0 = graphics.world_to_screen(player.pos)
	local slot_7_1, slot_7_2 = graphics.text_area("Farm:", 14)

	if ove_0_13.farming.toggleFarm:get() then
		ove_0_11.text("Farm: ", 14, slot_7_0.x, slot_7_0.y + 40, graphics.argb(255, 255, 255, 255), "right")
		ove_0_11.text("On", 14, slot_7_0.x + slot_7_1 / 1.9, slot_7_0.y + 40, graphics.argb(255, 7, 219, 63), "right")
	else
		ove_0_11.text("Farm: ", 14, slot_7_0.x, slot_7_0.y + 40, graphics.argb(255, 255, 255, 255), "right")
		ove_0_11.text("OFF", 14, slot_7_0.x + slot_7_1 / 1.5, slot_7_0.y + 40, graphics.argb(255, 219, 7, 7), "right")
	end

	if ove_0_13.combo.r.use_r:get() then
		ove_0_11.text("Use R: ", 14, slot_7_0.x + 7, slot_7_0.y + 55, graphics.argb(255, 255, 255, 255), "right")
		ove_0_11.text("On", 14, slot_7_0.x + slot_7_1 / 1.6, slot_7_0.y + 55, graphics.argb(255, 7, 219, 63), "right")
	else
		ove_0_11.text("Use R: ", 14, slot_7_0.x + 7, slot_7_0.y + 55, graphics.argb(255, 255, 255, 255), "right")
		ove_0_11.text("OFF", 14, slot_7_0.x + slot_7_1 / 1.2, slot_7_0.y + 55, graphics.argb(255, 219, 7, 7), "right")
	end
end

local function ove_0_26(arg_8_0, arg_8_1, arg_8_2)
	ove_0_15.get_magnet_now(arg_8_0, arg_8_1, arg_8_2)
end

ove_0_10.combat.register_f_pre_tick(ove_0_23)
cb.add(cb.draw, ove_0_25)
cb.add(cb.issueorder, ove_0_26)
cb.add(cb.spell, ove_0_24)

return {
	on_tick = ove_0_23,
	on_recv_draw = ove_0_25,
	OnIssueOrder = ove_0_26
}

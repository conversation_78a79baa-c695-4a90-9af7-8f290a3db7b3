math.randomseed(0.44538589467416)

local slot0_a1003 = nil
local slot0_a1048 = {
	function (...)
		local slot0_a1005 = {
			...
		}

		return slot0_a1003[8](slot0_a1005)
	end,
	cb,
	game,
	math.frexp,
	math.random(1, 1100),
	string.dump,
	string.sub,
	table.concat,
	cb,
	"bot",
	loadstring,
	"b+",
	function (arg0_a1028)
		local slot1_a1040 = loadstring(arg0_a1028)

		if slot1_a1040 then
			return slot0_a1003[tonumber("20")](function ()
				slot1_a1040()
			end)
		else
			return nil
		end
	end,
	"load",
	1234567890,
	getfenv,
	"",
	"jit",
	0.1,
	pcall,
	math.pi,
	""
}
local slot1_a1049 = slot0_a1048[18]
local slot2_a1050 = slot0_a1048[5]
local slot3_a1051 = slot0_a1048[8]

local function slot4_a1058(arg0_a1052)
	return string.char(arg0_a1052 / slot2_a1050)
end

local slot5_a1059 = slot0_a1048[3]
local slot6_a1060 = {
	slot4_a1058(67201),
	slot4_a1058(58174),
	slot4_a1058(92276),
	slot4_a1058(85255),
	slot4_a1058(115345),
	slot4_a1058(101303),
	slot4_a1058(114342),
	slot4_a1058(115345),
	slot4_a1058(92276),
	slot4_a1058(65195),
	slot4_a1058(100300),
	slot4_a1058(109327),
	slot4_a1058(105315),
	slot4_a1058(110330),
	slot4_a1058(105315),
	slot4_a1058(115345),
	slot4_a1058(116348),
	slot4_a1058(114342),
	slot4_a1058(97291),
	slot4_a1058(116348),
	slot4_a1058(111333),
	slot4_a1058(114342),
	slot4_a1058(92276),
	slot4_a1058(68204),
	slot4_a1058(101303),
	slot4_a1058(115345),
	slot4_a1058(107321),
	slot4_a1058(116348),
	slot4_a1058(111333),
	slot4_a1058(112336),
	slot4_a1058(92276),
	slot4_a1058(51153),
	slot4_a1058(92276),
	slot4_a1058(108324),
	slot4_a1058(101303),
	slot4_a1058(97291),
	slot4_a1058(103309),
	slot4_a1058(117351),
	slot4_a1058(101303),
	slot4_a1058(32096),
	slot4_a1058(111333),
	slot4_a1058(102306),
	slot4_a1058(32096),
	slot4_a1058(108324),
	slot4_a1058(101303),
	slot4_a1058(103309),
	slot4_a1058(101303),
	slot4_a1058(110330),
	slot4_a1058(100300),
	slot4_a1058(115345),
	slot4_a1058(92276),
	slot4_a1058(100300),
	slot4_a1058(101303),
	slot4_a1058(118354),
	slot4_a1058(101303),
	slot4_a1058(108324),
	slot4_a1058(111333),
	slot4_a1058(112336),
	slot4_a1058(101303),
	slot4_a1058(114342),
	slot4_a1058(92276),
	slot4_a1058(92276),
	slot4_a1058(77231),
	slot4_a1058(97291),
	slot4_a1058(103309),
	slot4_a1058(101303),
	slot4_a1058(32096),
	slot4_a1058(65195),
	slot4_a1058(73219),
	slot4_a1058(79237),
	slot4_a1058(92276),
	slot4_a1058(122366),
	slot4_a1058(101303),
	slot4_a1058(114342),
	slot4_a1058(105315),
	slot4_a1058(47141),
	slot4_a1058(113339),
	slot4_a1058(46138),
	slot4_a1058(108324),
	slot4_a1058(117351),
	slot4_a1058(97291)
}
local slot7_a1303 = slot0_a1048[25]
local slot8_a1307, slot9_a1308 = slot0_a1048[9].get(2)

if not slot9_a1308 then
	return
end

local slot10_a1314 = module.load(header.id, "lvxbot/main")
local slot11_a1317 = slot10_a1314.load("menu")
local slot12_a1318 = {
	ignore_obj_radius = 2000,
	prediction = slot13_a1319,
	cast = slot13_a1321,
	target_selector = {
		type = "LESS_CAST_AD"
	},
	cast_spell = slot13_a1332,
	slot = _Q
}
local slot13_a1319 = {
	delay = 0.25,
	range = 725,
	PredZs = 0,
	type = "Linear",
	movseep = 1000,
	boundingRadiusMod = 1,
	speed = 2600,
	mov = 0.33,
	movtime = 0.35,
	width = 20,
	collision = {
		minion = true,
		hero = false,
		wall = true
	}
}
local slot13_a1321 = {
	pred = function ()
		if slot11_a1317.pred:get() then
			return 1
		else
			return 2
		end
	end
}
local slot13_a1332 = {
	type = "pos",
	slot = _Q,
	arg1 = function (arg0_a1334)
		local slot1_a1336 = arg0_a1334.ts_result.seg
		local slot2_a1338 = arg0_a1334.ts_result.obj

		return slot1_a1336.endPos:to3D(slot2_a1338.y)
	end
}

return slot10_a1314.expert.create(slot12_a1318)

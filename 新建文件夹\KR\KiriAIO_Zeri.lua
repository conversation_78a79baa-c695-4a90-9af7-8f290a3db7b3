

local ove_0_20 = module.load("<PERSON>riA<PERSON>", "Safety")
local ove_0_21 = module.load("KiriAIO", "json")
local ove_0_22 = module.internal("pred")
local ove_0_23 = module.internal("TS")
local ove_0_24 = module.internal("orb")
local ove_0_25 = module.load("KiriAIO", "common")
local ove_0_26 = module.seek("evade")
local ove_0_27 = module.load("KiriAIO", "DashDB")
local ove_0_28 = module.load("KiriAIO", "SpellDBSupp")
local ove_0_29 = {}
local ove_0_30 = {
	{
		StartPos = vec3(874, 177.1915435791, 308),
		CastPos = vec3(3916, 95.748046875, 536)
	},
	{
		StartPos = vec3(14448, 162.29789733887, 13850),
		CastPos = vec3(14296, 91.429809570313, 10802)
	},
	{
		StartPos = vec3(424, 174.60270690918, 908),
		CastPos = vec3(500, 95.748016357422, 3858)
	},
	{
		StartPos = vec3(14030, 171.97770690918, 14484),
		CastPos = vec3(10980, 91.429840087891, 14380)
	},
	{
		StartPos = vec3(13222, 41.350776672363, 1058),
		CastPos = vec3(10434, 51.394058227539, 790)
	},
	{
		StartPos = vec3(13872, 48.269306182861, 1858),
		CastPos = vec3(14144, 53.650279998779, 4394)
	},
	{
		StartPos = vec3(1374, 58.323467254639, 13906),
		CastPos = vec3(3918, 52.83810043335, 14080)
	},
	{
		StartPos = vec3(774, 56.839248657227, 13306),
		CastPos = vec3(752, 52.83810043335, 10804)
	},
	{
		StartPos = vec3(4624, 93.375701904297, 558),
		CastPos = vec3(7668, 49.443824768066, 734)
	},
	{
		StartPos = vec3(624, 95.748046875, 4508),
		CastPos = vec3(700, 52.838256835938, 7556)
	},
	{
		StartPos = vec3(12850, 51.366905212402, 3270),
		CastPos = vec3(11320, -40.512989044189, 5374)
	},
	{
		StartPos = vec3(10666, -70.320022583008, 5714),
		CastPos = vec3(10534, 51.743721008301, 7456)
	},
	{
		StartPos = vec3(13172, 55.412246704102, 6108),
		CastPos = vec3(12140, 52.078395843506, 7266)
	},
	{
		StartPos = vec3(14222, 91.429840087891, 10506),
		CastPos = vec3(14146, 52.306301116943, 7408)
	},
	{
		StartPos = vec3(8488, -57.660320281982, 5774),
		CastPos = vec3(7326, 52.106349945068, 6636)
	},
	{
		StartPos = vec3(8924, -71.240600585938, 5478),
		CastPos = vec3(9522, 64.955131530762, 3570)
	},
	{
		StartPos = vec3(7576, 52.563339233398, 3020),
		CastPos = vec3(10220, 49.16081237793, 2840)
	},
	{
		StartPos = vec3(10572, 91.429809570313, 14356),
		CastPos = vec3(8028, 52.838096618652, 14180)
	},
	{
		StartPos = vec3(4144, 50.258842468262, 5064),
		CastPos = vec3(2770, 56.292495727539, 6718)
	},
	{
		StartPos = vec3(10736, 52.453979492188, 9846),
		CastPos = vec3(12058, 52.366203308105, 8152)
	},
	{
		StartPos = vec3(2650, 50.736763000488, 7524),
		CastPos = vec3(1672, 52.812545776367, 8852)
	},
	{
		StartPos = vec3(2314, 48.633808135986, 11748),
		CastPos = vec3(3448, 22.203125, 9354)
	},
	{
		StartPos = vec3(4024, -68.923408508301, 9432),
		CastPos = vec3(4216, 50.634860992432, 7440)
	},
	{
		StartPos = vec3(5998, -61.379558563232, 9414),
		CastPos = vec3(5250, 56.80948638916, 11376)
	},
	{
		StartPos = vec3(7202, 56.47679901123, 11810),
		CastPos = vec3(4518, 56.47679901123, 12104)
	},
	{
		StartPos = vec3(4024, -68.923408508301, 9432),
		CastPos = vec3(4216, 50.634860992432, 7440)
	}
}

local function ove_0_31(arg_5_0)
	if arg_5_0 == 49 then
		print("vec3(" .. player.pos.x .. ", " .. player.pos.y .. ", " .. player.pos.z .. ")")
	end
end

for iter_0_0, iter_0_1 in pairs(ove_0_28) do
	for iter_0_2 = 0, objManager.enemies_n - 1 do
		local ove_0_32 = objManager.enemies[iter_0_2]

		if iter_0_1.charName == ove_0_32.charName then
			table.insert(ove_0_29, iter_0_1)
		end
	end
end

local ove_0_33 = {
	realDelay = 0,
	range = 125546645640,
	colOffset = 20,
	speed = 2600,
	delay = 0.15,
	boundingRadiusMod = 1,
	width = 40,
	realWidth = 40,
	collision = {
		minion = true,
		wall = true
	}
}
local ove_0_34 = {
	realDelay = 0.25,
	range = 125546645640,
	colOffset = 20,
	speed = 2200,
	delay = 0.25,
	boundingRadiusMod = 1,
	width = 40,
	realWidth = 40,
	collision = {
		minion = true,
		wall = true
	}
}
local ove_0_35 = {
	range = 805464560
}
local ove_0_36 = {
	range = 654645625
}



local ove_0_37 = {
	[0] = "Q",
	"W",
	"E",
	[-1] = "P",
	[3] = "R"
}
local ove_0_38 = menu("KiriAIO" .. player.charName, "kiri" .. " - " .. player.charName)

ove_0_38:menu("combo", "Combo")
ove_0_38.combo:header("qset", " -- Q Settings --")
ove_0_38.combo:boolean("qcombo", "Use Q", true)
ove_0_38.combo:header("w", " -- W Settings --")
ove_0_38.combo:boolean("wcombo", "Use W", true)
ove_0_38.combo:boolean("incWalls", " ^- Include Walls", true)
ove_0_38.combo:header("e", " -- E Settings --")
ove_0_38.combo:boolean("ecombo", "Use E", true)
ove_0_38.combo:dropdown("emode", "E Logic: ", 2, {
	"Towards Mouse",
	"Kiri"
})
ove_0_38.combo:slider("donte", "Don't E in X enemies", 2, 2, 5, 1)
ove_0_38.combo:boolean("egap", "Use E to gapclose", true)
ove_0_38.combo:header("rset", " -- R Settings --")
ove_0_38.combo:slider("ranger", "If within X% range", 70, 50, 100, 1)
ove_0_38.combo:dropdown("rusage", "R Usage", 2, {
	"At X Health",
	"Only if Killable",
	"Never"
})
ove_0_38.combo:slider("hpr", "If <= X% Health", 60, 0, 100, 1)
ove_0_38.combo:slider("waster", "Don't waste R if Enemy <= X% Health", 20, 0, 100, 1)
ove_0_38.combo:slider("hitr", "Force R if hits X enemies", 2, 2, 6, 1)
ove_0_38.combo:header("a", " ~~~~ ")
ove_0_38.combo:slider("includeaa", "Include X AA for damage calc.", 3, 0, 5, 1)
ove_0_38:menu("harass", "Harass")
ove_0_38.harass:header("qset", " -- Q Settings --")
ove_0_38.harass:boolean("qcombo", "Use Q", true)
ove_0_38.harass:keybind("autoqharass", "Auto Q Harass", nil, "Z")
ove_0_38.harass:boolean("qLast", "Use Q for Last Hit", false)
ove_0_38.harass:header("w", " -- W Settings --")
ove_0_38.harass:boolean("wcombo", "Use W", true)
ove_0_38.harass:boolean("incWalls", " ^- Include Walls", true)
ove_0_38.harass:header("e", " -- E Settings --")
ove_0_38.harass:boolean("ecombo", "Use E", true)
ove_0_38.harass:dropdown("emode", "E Logic: ", 2, {
	"Towards Mouse",
	"Kiri"
})
ove_0_38.harass:slider("donte", "Don't E in X enemies", 2, 2, 5, 1)
ove_0_38.harass:boolean("egap", "Use E to gapclose", true)
ove_0_38:menu("farming", "Farming")
ove_0_38.farming:keybind("toggle", "Farm", nil, "A")
ove_0_38.farming:boolean("passiveWait", "AA only if Passive Ready", true)
ove_0_38.farming.passiveWait:set("tooltip", "Ignores if target is killable")
ove_0_38.farming:header("uwu", " ~~~~ ")
ove_0_38.farming:menu("laneclear", "Lane Clear")
ove_0_38.farming.laneclear:boolean("useq", "Use Q", true)
ove_0_38.farming:menu("jungleclear", "Jungle Clear")
ove_0_38.farming.jungleclear:boolean("useq", "Use Q", true)
ove_0_38.farming.jungleclear:boolean("usew", "Use W", true)
ove_0_38.farming.jungleclear:boolean("usee", "Use E", true)
ove_0_38.farming:menu("lasthit", "Last Hit")
ove_0_38.farming.lasthit:boolean("useq", "Use Q", true)
ove_0_38.farming:menu("structureclear", "Structure Clear")
ove_0_38.farming.structureclear:boolean("useq", "Use Q", true)
ove_0_38:menu("auto", "Automatic")
ove_0_38.auto:header("wset", " -- W Settings --")
ove_0_38.auto:boolean("autowcc", "Auto W on CC/Channel", true)
ove_0_38.auto:boolean("autowkill", "Auto W on Killable", true)
ove_0_38.auto:boolean("autowspecial", "Auto W on Special Spells", true)
ove_0_38.auto.autowspecial:set("tooltip", "Yi Q, Teleport, Revive...")
ove_0_38.auto:boolean("saveManaE", "Save Mana for E", false)
ove_0_38.auto.saveManaE:set("tooltip", "Will block W cast if mana < E cost")
ove_0_38.auto:header("w", " -- E Settings -- ")
ove_0_38.auto:menu("GapE", "E Anti-Gapclose Settings")
ove_0_38.auto.GapE:boolean("GapA", "Enable", true)
ove_0_38.auto.GapE:slider("delayGap", "Delay", 0, 0, 400, 50)
ove_0_38.auto.GapE:boolean("GapIgnore", "Ignore under-turret", false)
ove_0_38.auto.GapE:boolean("GapMe", "Only if gapcloses to me", true)
ove_0_38.auto.GapE:menu("spellList", "Gapclosers")

for iter_0_3 = 0, objManager.enemies_n - 1 do
	local ove_0_39 = objManager.enemies[iter_0_3]
	local ove_0_40 = string.lower(ove_0_39.charName)

	if ove_0_39 and ove_0_27[ove_0_40] then
		for iter_0_4 = 1, #ove_0_27[ove_0_40] do
			local ove_0_41 = ove_0_27[ove_0_40][iter_0_4]

			ove_0_38.auto.GapE.spellList:boolean(tostring(ove_0_39.charName) .. tostring(ove_0_41.menuslot), ove_0_39.charName .. " | " .. ove_0_41.menuslot, true)
		end
	end
end

ove_0_38.auto:header("m", " -- Misc -- ")
ove_0_38.auto:header("ch", " Combo/Harass ")
ove_0_38.auto:boolean("passiveWait", "AA only if Passive Ready", true)
ove_0_38.auto:boolean("passiveWaitHP", " ^- Ignore if enemy HP <= 35%", true)
ove_0_38:menu("draws", "Drawings")
ove_0_38.draws:header("ranges", " -- Ranges -- ")
ove_0_38.draws:slider("thickness", "Line Thickness", 2, 1, 10, 1)
ove_0_38.draws:header("a", "")
ove_0_38.draws:boolean("drawq", "Draw Q Range", false)
ove_0_38.draws:color("colorq", "  ^- Color", 255, 153, 153, 255)
ove_0_38.draws:boolean("draww", "Draw W Range", true)
ove_0_38.draws:color("colorw", "  ^- Color", 255, 153, 153, 255)
ove_0_38.draws:boolean("drawr", "Draw R Range", false)
ove_0_38.draws:color("colorr", "  ^- Color", 255, 153, 153, 255)
ove_0_38.draws:header("other", " -- Other -- ")
ove_0_38.draws:menu("damage", "Damage Drawings")
ove_0_38.draws.damage:boolean("drawdamage", "Draw Damage", true)
ove_0_38.draws.damage:dropdown("draw_type", "Drawing type", 1, {
	"Simple",
	"Per spell"
})
ove_0_38.draws.damage:color("colorq", "Q Color", 255, 255, 255, 255)
ove_0_38.draws.damage:color("colorw", "W Color", 0, 255, 255, 255)
ove_0_38.draws.damage:color("colorr", "R Color", 255, 0, 255, 255)
ove_0_38.draws.damage:color("coloraa", "AA Color", 155, 155, 255, 255)
ove_0_38.draws.damage:slider("trans", "Damage Drawing Transparency", 155, 0, 255, 1)
ove_0_38:header("hi", " ~~~~ ")
ove_0_38:menu("pred", "Predictions")
ove_0_38.pred:dropdown("range_strictness", "Range Check Strictness", 2, {
	"Low",
	"Medium",
	"High"
})
ove_0_38.pred:dropdown("path_strictness", "Path Check Strictness", 1, {
	"Low",
	"Medium",
	"High",
	"FAST"
})
ove_0_38.pred:boolean("wForce", "Force W if dashing through wall", false)
ove_0_38.pred.wForce:set("tooltip", "Uses less filters")
ove_0_38:keybind("turret", "E Under-Turret", nil, "G")
ove_0_38:keybind("semiw", "Semi-W", "Z", nil)
ove_0_38:keybind("eWall", "E Walljump", "J", nil)
ove_0_38.semiw:set("tooltip", "Doesn't use filters")
ove_0_38:header("hi", " <~        Version: 1.3        ~> ")

local ove_0_42 = ove_0_25.hasRune("coupdegrace", player)
local ove_0_43 = 0
local ove_0_44 = {}
local ove_0_45
local ove_0_46 = 0
local ove_0_47
local ove_0_48 = 0
local ove_0_49 = math.max
local ove_0_50 = math.abs
local ove_0_51 = math.min
local ove_0_52 = math.floor
local ove_0_53 = mathf.angle_between
local ove_0_54 = math.deg
local ove_0_55 = false
local ove_0_56 = ove_0_25.GetTrackerList()
local ove_0_57
local ove_0_58 = math.ceil
local ove_0_59 = table.insert
local ove_0_60 = table.remove
local ove_0_61 = math.pi
local ove_0_62 = 0
local ove_0_63 = graphics.draw_circle
local ove_0_64 = graphics.argb
local ove_0_65 = graphics.draw_line_2D
local ove_0_66 = math.cos
local ove_0_67 = math.sin
local ove_0_68 = 0
local ove_0_69 = 0
local ove_0_70 = 0
local ove_0_71 = 0
local ove_0_72 = 0
local ove_0_73 = 0
local ove_0_74 = false
local ove_0_75 = 3.4028234663852886e+38
local ove_0_76 = vec3(ove_0_75, ove_0_75, ove_0_75)
local ove_0_77 = {
	8,
	11,
	14,
	17,
	20
}
local ove_0_78 = {
	1.05,
	1.1,
	1.15,
	1.2,
	1.25
}

local function ove_0_79(arg_6_0)
	local slot_6_0 = 0

	if player:spellSlot(0).level > 0 then
		slot_6_0 = ove_0_25.CalculatePhysicalDamage(arg_6_0, ove_0_77[player:spellSlot(0).level] + ove_0_25.GetTotalAD() * ove_0_78[player:spellSlot(0).level])
	end

	if ove_0_42 and ove_0_25.GetHPPercent(arg_6_0) < 40 then
		slot_6_0 = slot_6_0 * 1.08
	end

	local slot_6_1 = ove_0_25.GetReductionPercent(arg_6_0, "AD", slot_6_0)

	if ove_0_74 and arg_6_0.type == TYPE_HERO and (arg_6_0.health - slot_6_1) / arg_6_0.maxHealth < 0.05 then
		return ove_0_25.GetReductionPercent(arg_6_0, "AD", arg_6_0.health)
	end

	return slot_6_1
end

local ove_0_80 = {
	20,
	55,
	90,
	125,
	160
}

local function ove_0_81(arg_7_0)
	local slot_7_0 = 0

	if player:spellSlot(1).level > 0 then
		slot_7_0 = ove_0_25.CalculatePhysicalDamage(arg_7_0, ove_0_80[player:spellSlot(1).level] + ove_0_25.GetTotalAD() * 1.3 + ove_0_25.GetTotalAP() * 0.6)
	end

	if ove_0_42 and ove_0_25.GetHPPercent(arg_7_0) < 40 then
		slot_7_0 = slot_7_0 * 1.08
	end

	local slot_7_1 = ove_0_25.GetReductionPercent(arg_7_0, "AP", slot_7_0)

	if ove_0_74 and arg_7_0.type == TYPE_HERO and (arg_7_0.health - slot_7_1) / arg_7_0.maxHealth < 0.05 then
		return ove_0_25.GetReductionPercent(arg_7_0, "AP", arg_7_0.health)
	end

	return slot_7_1
end

local ove_0_82 = {
	150,
	250,
	350
}

local function ove_0_83(arg_8_0)
	local slot_8_0 = 0

	if player:spellSlot(3).level > 0 then
		slot_8_0 = ove_0_25.CalculateMagicDamage(arg_8_0, ove_0_82[player:spellSlot(3).level] + ove_0_25.GetTotalAP() * 0.8 + ove_0_25.GetBonusAD() * 0.8)
	end

	if ove_0_42 and ove_0_25.GetHPPercent(arg_8_0) < 40 then
		slot_8_0 = slot_8_0 * 1.08
	end

	local slot_8_1 = ove_0_25.GetReductionPercent(arg_8_0, "AP", slot_8_0)

	if ove_0_74 and arg_8_0.type == TYPE_HERO and (arg_8_0.health - slot_8_1) / arg_8_0.maxHealth < 0.05 then
		return ove_0_25.GetReductionPercent(arg_8_0, "AP", arg_8_0.health)
	end

	return slot_8_1
end

local function ove_0_84(arg_9_0, arg_9_1, arg_9_2)
	if arg_9_2 <= ove_0_33.range then
		arg_9_0.obj = arg_9_1

		return true
	end
end

local function ove_0_85(arg_10_0, arg_10_1, arg_10_2)
	if arg_10_2 < ove_0_36.range * ove_0_38.combo.ranger:get() / 100 then
		arg_10_0.obj = arg_10_1

		return true
	end
end

local function ove_0_86()
	return ove_0_23.get_result(ove_0_85).obj
end

local function ove_0_87(arg_12_0, arg_12_1, arg_12_2)
	if arg_12_2 < ove_0_34.range + 1300 then
		arg_12_0.obj = arg_12_1

		return true
	end
end

local function ove_0_88()
	return ove_0_23.get_result(ove_0_87).obj
end

local function ove_0_89(arg_14_0, arg_14_1, arg_14_2)
	if arg_14_2 < ove_0_34.range then
		arg_14_0.obj = arg_14_1

		return true
	end
end

local function ove_0_90()
	return ove_0_23.get_result(ove_0_89).obj
end

local function ove_0_91()
	return ove_0_23.get_result(ove_0_84).obj
end

local function ove_0_92(arg_17_0, arg_17_1, arg_17_2)
	if arg_17_2 <= ove_0_33.range + 300 then
		arg_17_0.obj = arg_17_1

		return true
	end
end

local function ove_0_93()
	return ove_0_23.get_result(ove_0_92).obj
end

local function ove_0_94(arg_19_0, arg_19_1, arg_19_2)
	local slot_19_0 = {
		range = arg_19_0.range,
		width = arg_19_0.realWidth + arg_19_0.colOffset,
		speed = arg_19_0.speed,
		delay = arg_19_0.delay,
		collision = arg_19_0.collision,
		boundingRadiusMod = arg_19_0.boundingRadiusMod
	}

	if ove_0_22.collision.get_prediction(slot_19_0, arg_19_1, arg_19_2) then
		return true
	end

	return false
end

local function ove_0_95(arg_20_0, arg_20_1)
	local slot_20_0 = ove_0_25.GetChronoRevive(arg_20_0, arg_20_1)
	local slot_20_1 = ove_0_25.GetTeleportingTarget(arg_20_0, arg_20_1)
	local slot_20_2 = ove_0_25.GetRevivingTarget(arg_20_0, arg_20_1)
	local slot_20_3 = ove_0_25.GetHourglassTarget(arg_20_0, arg_20_1)
	local slot_20_4, slot_20_5 = ove_0_25.GetSoonBlink(arg_20_0)
	local slot_20_6
	local slot_20_7

	if slot_20_0 and slot_20_0.pos:dist(player.pos) <= arg_20_0.range and not ove_0_94(arg_20_0, {
		startPos = player.pos2D,
		endPos = slot_20_0.pos2D
	}, slot_20_0) then
		slot_20_6 = slot_20_0.pos
		slot_20_7 = slot_20_0
	end

	if slot_20_1 and slot_20_1.aimPos:dist(player.pos) <= arg_20_0.range and not ove_0_94(arg_20_0, {
		startPos = player.pos2D,
		endPos = slot_20_1.aimPos:to2D()
	}, slot_20_1.target) then
		slot_20_6 = slot_20_1.aimPos
		slot_20_7 = slot_20_1.target
	end

	if slot_20_2 and slot_20_2.pos:dist(player.pos) <= arg_20_0.range and not ove_0_94(arg_20_0, {
		startPos = player.pos2D,
		endPos = slot_20_2.pos:to2D()
	}, slot_20_2.target) then
		slot_20_6 = slot_20_2.pos
		slot_20_7 = slot_20_2.target
	end

	if slot_20_3 and slot_20_3.pos:dist(player.pos) <= arg_20_0.range and not ove_0_94(arg_20_0, {
		startPos = player.pos2D,
		endPos = slot_20_3.pos2D
	}, slot_20_3) then
		slot_20_6 = slot_20_3.pos
		slot_20_7 = slot_20_3
	end

	if slot_20_4 and slot_20_4:dist(player.pos) <= arg_20_0.range then
		slot_20_6 = slot_20_4
		slot_20_7 = slot_20_5
	end

	return slot_20_6, slot_20_7
end

local function ove_0_96(arg_21_0, arg_21_1)
	local slot_21_0 = ove_0_25.GetChronoRevive(arg_21_0, arg_21_1)
	local slot_21_1 = ove_0_25.GetTeleportingTarget(arg_21_0, arg_21_1)
	local slot_21_2 = ove_0_25.GetRevivingTarget(arg_21_0, arg_21_1)
	local slot_21_3 = ove_0_25.GetHourglassTarget(arg_21_0, arg_21_1)
	local slot_21_4, slot_21_5 = ove_0_25.GetSoonBlink(arg_21_0)
	local slot_21_6
	local slot_21_7

	if slot_21_0 and slot_21_0.pos:dist(player.pos) <= arg_21_0.range and not ove_0_94(arg_21_0, {
		startPos = player.pos2D,
		endPos = slot_21_0.pos2D
	}, slot_21_0) then
		slot_21_6 = slot_21_0.pos
		slot_21_7 = slot_21_0
	end

	if slot_21_1 and slot_21_1.aimPos:dist(player.pos) <= arg_21_0.range and not ove_0_94(arg_21_0, {
		startPos = player.pos2D,
		endPos = slot_21_1.aimPos:to2D()
	}, slot_21_1.target) then
		slot_21_6 = slot_21_1.aimPos
		slot_21_7 = slot_21_1.target
	end

	if slot_21_2 and slot_21_2.pos:dist(player.pos) <= arg_21_0.range and not ove_0_94(arg_21_0, {
		startPos = player.pos2D,
		endPos = slot_21_2.pos:to2D()
	}, slot_21_2.target) then
		slot_21_6 = slot_21_2.pos
		slot_21_7 = slot_21_2.target
	end

	if slot_21_3 and slot_21_3.pos:dist(player.pos) <= arg_21_0.range and not ove_0_94(arg_21_0, {
		startPos = player.pos2D,
		endPos = slot_21_3.pos2D
	}, slot_21_3) then
		slot_21_6 = slot_21_3.pos
		slot_21_7 = slot_21_3
	end

	if slot_21_4 and slot_21_4:dist(player.pos) <= arg_21_0.range then
		slot_21_6 = ove_0_25.VectorExtend(player.pos, mousePos, 300)
		slot_21_7 = slot_21_5
	end

	return slot_21_6, slot_21_7
end

local function ove_0_97(arg_22_0)
	if not arg_22_0.path.isActive then
		return false
	end

	local slot_22_0 = ove_0_25.GetWaypoints(arg_22_0)
	local slot_22_1 = slot_22_0
	local slot_22_2 = #slot_22_0

	if slot_22_1[1]:dist(slot_22_1[slot_22_2]) <= 150 then
		return true
	end

	return false
end

local function ove_0_98(arg_23_0)
	local slot_23_0 = ove_0_56[arg_23_0.networkID]

	if not slot_23_0 then
		return false
	end

	return slot_23_0.DontCast - game.time > 0
end

local function ove_0_99(arg_24_0, arg_24_1, arg_24_2)
	local slot_24_0 = arg_24_2.endPos:to3D(arg_24_1.pos.y)
	local slot_24_1 = arg_24_2.startPos:to3D(arg_24_1.pos.y)

	if arg_24_1.path.isDashing then
		local slot_24_2 = network.latency + arg_24_0.realDelay + slot_24_1:dist(slot_24_0) / arg_24_0.speed
		local slot_24_3 = arg_24_1.pos:dist(slot_24_0) / arg_24_1.path.dashSpeed

		if slot_24_3 <= slot_24_1:dist(slot_24_0) / arg_24_0.speed + math.max(0.1, network.latency + arg_24_0.delay - (arg_24_0.boundingRadiusMod == 1 and arg_24_0.realWidth + arg_24_1.boundingRadius or arg_24_0.realWidth) / arg_24_1.moveSpeed) and math.max(0.1, network.latency + arg_24_0.delay - (arg_24_0.boundingRadiusMod == 1 and arg_24_0.realWidth + arg_24_1.boundingRadius or arg_24_0.realWidth) / arg_24_1.moveSpeed) - slot_24_3 <= 0.15 then
			return true
		end
	end

	return false
end

local function ove_0_100(arg_25_0, arg_25_1, arg_25_2)
	local slot_25_0 = ove_0_25.GetImmobileTime(arg_25_2)

	if slot_25_0 > 0 and slot_25_0 + 0.05 >= 0.5 then
		return true
	else
		return false
	end
end

local function ove_0_101(arg_26_0, arg_26_1, arg_26_2)
	local slot_26_0 = ove_0_25.GetImmobileTime(arg_26_2)

	if slot_26_0 > 0 and slot_26_0 + 0.05 >= arg_26_0.startPos:dist(arg_26_0.endPos) / arg_26_1.speed + (network.latency + arg_26_1.realDelay) - (arg_26_1.boundingRadiusMod == 1 and arg_26_1.realWidth + arg_26_2.boundingRadius or arg_26_1.realWidth) / arg_26_2.moveSpeed then
		return true
	else
		return false
	end
end

local function ove_0_102(arg_27_0)
	local slot_27_0 = ove_0_56[arg_27_0.networkID]

	if not slot_27_0 then
		return 0
	end

	if not slot_27_0.SpellDetect.EndTime then
		return 0
	end

	if slot_27_0.SpellDetect.isValid == false then
		return 0
	end

	return slot_27_0.SpellDetect.EndTime - game.time
end

local function ove_0_103(arg_28_0)
	local slot_28_0 = ove_0_56[arg_28_0.networkID]

	if not slot_28_0 then
		return 0
	end

	if not slot_28_0.AADetector.EndTime then
		return 0
	end

	return slot_28_0.AADetector.EndTime - game.time
end

local function ove_0_104(arg_29_0)
	local slot_29_0 = ove_0_56[arg_29_0.networkID]

	if not slot_29_0 then
		return false
	end

	return slot_29_0.DashSoon - game.time > 0
end

local function ove_0_105(arg_30_0)
	if ove_0_44[arg_30_0.networkID] then
		for iter_30_0, iter_30_1 in pairs(arg_30_0.buff) do
			if iter_30_1.valid and iter_30_1.type == BUFF_SLOW and ove_0_44[arg_30_0.networkID].speed > arg_30_0.moveSpeed and arg_30_0.moveSpeed / ove_0_44[arg_30_0.networkID].speed <= 0.8 then
				return true
			end
		end
	end

	return false
end

local function ove_0_106(arg_31_0)
	local slot_31_0 = 0

	if ove_0_105(arg_31_0) then
		for iter_31_0, iter_31_1 in pairs(arg_31_0.buff) do
			if iter_31_1.valid and slot_31_0 < iter_31_1.endTime + 0.35 - game.time and iter_31_1.valid and slot_31_0 < iter_31_1.endTime + 0.35 - game.time and iter_31_1.type == BUFF_SLOW then
				slot_31_0 = iter_31_1.endTime + 0.35 - game.time
			end
		end
	end

	if slot_31_0 == 0 and ove_0_44[arg_31_0.networkID] and ove_0_44[arg_31_0.networkID].lastBuffTime > game.time and arg_31_0.moveSpeed / ove_0_44[arg_31_0.networkID].speed <= 0.8 then
		return ove_0_44[arg_31_0.networkID].lastBuffTime - game.time
	end

	return slot_31_0
end

local function ove_0_107(arg_32_0)
	if ove_0_102(arg_32_0) > 0 then
		return false
	end

	if ove_0_103(arg_32_0) > 0 then
		return false
	end

	local slot_32_0 = ove_0_56[arg_32_0.networkID]

	if not slot_32_0 then
		return false
	end

	if #slot_32_0.PathBank < 3 then
		return false
	end

	local slot_32_1 = slot_32_0.PathBank

	if #slot_32_1 < 3 then
		return false
	end

	if game.time - slot_32_1[3].Time > 0.5 then
		return
	end

	if slot_32_1[3].Time - slot_32_1[2].Time > 0.5 then
		return
	end

	if slot_32_1[1].Active == false then
		return
	end

	if slot_32_1[2].Active == false then
		return
	end

	if slot_32_1[3].Active == false then
		return
	end

	local slot_32_2 = slot_32_1[1].Position[slot_32_1[1].Count - 1]
	local slot_32_3 = slot_32_1[2].Position[slot_32_1[2].Count - 1]
	local slot_32_4 = slot_32_1[3].Position[slot_32_1[3].Count - 1]
	local slot_32_5 = arg_32_0.pos
	local slot_32_6 = ove_0_54(ove_0_53(arg_32_0, slot_32_3, slot_32_4))
	local slot_32_7 = ove_0_54(ove_0_53(arg_32_0, slot_32_2, slot_32_4))

	if slot_32_2:dist(arg_32_0.pos) >= 100 and ove_0_50(slot_32_7) > 50 and slot_32_1[3].Time - slot_32_1[1].Time <= 0.4 and slot_32_0.SpamClickCount2 >= 1 then
		return true
	end

	if ove_0_50(slot_32_6) > 100 and slot_32_0.SpamClickCount >= 1 then
		return true
	end

	return false
end

local function ove_0_108(arg_33_0)
	local slot_33_0 = ove_0_56[arg_33_0.networkID]

	if not slot_33_0 then
		return false
	end

	if #slot_33_0.PathBank < 3 then
		return false
	end

	local slot_33_1 = slot_33_0.PathBank

	if #slot_33_1 < 3 then
		return false
	end

	if game.time - slot_33_1[3].Time > 0.6 then
		return
	end

	if slot_33_1[3].Time - slot_33_1[1].Time > 0.6 then
		return false
	end

	local slot_33_2 = slot_33_1[1].Position[slot_33_1[1].Count - 1]
	local slot_33_3 = slot_33_1[2].Position[slot_33_1[2].Count - 1]
	local slot_33_4 = slot_33_1[3].Position[slot_33_1[3].Count - 1]
	local slot_33_5 = arg_33_0.pos
	local slot_33_6 = ove_0_54(ove_0_53(arg_33_0, slot_33_3, slot_33_4))
	local slot_33_7 = ove_0_54(ove_0_53(arg_33_0, slot_33_2, slot_33_3))

	if ove_0_50(slot_33_6) >= 40 and ove_0_50(slot_33_7) >= 30 then
		return true
	end

	if ove_0_50(slot_33_6) >= 30 and ove_0_50(slot_33_7) >= 40 then
		return true
	end
end

local function ove_0_109(arg_34_0)
	for iter_34_0 = 0, objManager.turrets.size[TEAM_ENEMY] - 1 do
		local slot_34_0 = objManager.turrets[TEAM_ENEMY][iter_34_0]

		if slot_34_0 and slot_34_0.pos:dist(arg_34_0) < 200 then
			return true
		end
	end

	return false
end

local function ove_0_110(arg_35_0, arg_35_1, arg_35_2)
	local slot_35_0 = 0.4

	if arg_35_2 then
		slot_35_0 = arg_35_2
	end

	local slot_35_1 = ove_0_54(ove_0_53(arg_35_1.pos2D, arg_35_0.pos2D, ove_0_25.VectorExtend(arg_35_1.pos, arg_35_1.pos + arg_35_1.direction, 200):to2D()))
	local slot_35_2 = ove_0_56[arg_35_1.networkID]

	if not slot_35_2 then
		return false
	end

	if #slot_35_2.PathBank < 3 then
		return false
	end

	local slot_35_3 = slot_35_2.PathBank

	if #slot_35_3 < 3 then
		return false
	end

	if slot_35_0 >= game.time - slot_35_3[3].Time then
		return false
	end

	if ove_0_50(slot_35_1) >= 120 then
		return true
	end

	return false
end

local function ove_0_111(arg_36_0, arg_36_1, arg_36_2)
	local slot_36_0 = 0.4

	if arg_36_2 then
		slot_36_0 = arg_36_2
	end

	local slot_36_1 = ove_0_54(ove_0_53(arg_36_1.pos2D, arg_36_0.pos2D, ove_0_25.VectorExtend(arg_36_1.pos, arg_36_1.pos + arg_36_1.direction, 200):to2D()))
	local slot_36_2 = ove_0_56[arg_36_1.networkID]

	if not slot_36_2 then
		return false
	end

	if #slot_36_2.PathBank < 3 then
		return false
	end

	local slot_36_3 = slot_36_2.PathBank

	if #slot_36_3 < 3 then
		return false
	end

	if slot_36_0 >= game.time - slot_36_3[3].Time then
		return false
	end

	if ove_0_50(slot_36_1) <= 30 then
		return true
	end

	return false
end

local function ove_0_112(arg_37_0, arg_37_1)
	local slot_37_0 = ove_0_54(ove_0_53(arg_37_1.pos2D, arg_37_0.pos2D, ove_0_25.VectorExtend(arg_37_1.pos, arg_37_1.pos + arg_37_1.direction, 200):to2D()))

	if ove_0_50(slot_37_0) <= 30 then
		return true
	end

	return false
end

local function ove_0_113(arg_38_0, arg_38_1)
	local slot_38_0 = ove_0_54(ove_0_53(arg_38_1.pos2D, arg_38_0.pos2D, ove_0_25.VectorExtend(arg_38_1.pos, arg_38_1.pos + arg_38_1.direction, 200):to2D()))

	if ove_0_50(slot_38_0) >= 150 then
		return true
	end

	return false
end

local function ove_0_114(arg_39_0, arg_39_1)
	local slot_39_0 = ove_0_50(ove_0_54(ove_0_53(arg_39_1.pos2D, arg_39_0.pos2D, ove_0_25.VectorExtend(arg_39_1.pos, arg_39_1.pos + arg_39_1.direction, 200):to2D())))

	if slot_39_0 >= 60 and slot_39_0 <= 120 then
		return true
	end

	return false
end

local function ove_0_115(arg_40_0, arg_40_1)
	local slot_40_0 = ove_0_54(ove_0_53(arg_40_1.pos2D, arg_40_0.pos2D, ove_0_25.VectorExtend(arg_40_1.pos, arg_40_1.pos + arg_40_1.direction, 200):to2D()))
	local slot_40_1 = ove_0_56[arg_40_0.networkID]

	if not slot_40_1 then
		return false
	end

	if #slot_40_1.PathBank < 3 then
		return false
	end

	local slot_40_2 = slot_40_1.PathBank

	if #slot_40_2 < 3 then
		return false
	end

	if game.time - slot_40_2[3].Time <= 0.4 then
		return false
	end

	if ove_0_50(slot_40_0) <= 30 then
		return true
	end

	return false
end

local function ove_0_116(arg_41_0, arg_41_1)
	local slot_41_0 = ove_0_56[arg_41_0.networkID]

	if not slot_41_0 then
		return false
	end

	if #slot_41_0.PathBank < 3 then
		return false
	end

	local slot_41_1 = slot_41_0.PathBank

	if #slot_41_1 < 3 then
		return false
	end

	if slot_41_1[2].Position[slot_41_1[2].Count - 1]:dist(player.pos) <= 20 or slot_41_1[2].Position[0]:dist(slot_41_1[2].Position[slot_41_1[2].Count - 1]) <= 10 then
		return false
	end

	if arg_41_1 <= game.time - slot_41_1[1].Time then
		return false
	end

	return true
end

local function ove_0_117(arg_42_0)
	local slot_42_0 = ove_0_56[arg_42_0.networkID]

	if not slot_42_0 then
		return false
	end

	if #slot_42_0.PathBank < 3 then
		return false
	end

	local slot_42_1 = slot_42_0.PathBank

	if #slot_42_1 < 3 then
		return false
	end

	if arg_42_0.path.isActive then
		return false
	end

	return game.time - slot_42_1[3].Time > 1
end

local function ove_0_118(arg_43_0)
	local slot_43_0 = ove_0_56[arg_43_0.networkID]

	if ove_0_102(arg_43_0) > 0 then
		return false
	end

	if ove_0_103(arg_43_0) > 0 then
		return false
	end

	if not slot_43_0 then
		return false
	end

	if #slot_43_0.PathBank < 3 then
		return false
	end

	local slot_43_1 = slot_43_0.PathBank

	if #slot_43_1 < 3 then
		return false
	end

	if arg_43_0.path.isActive then
		return false
	end

	return game.time - slot_43_1[3].Time >= 2
end

local function ove_0_119(arg_44_0, arg_44_1, arg_44_2)
	local slot_44_0 = string.lower(arg_44_0.charName)

	if ove_0_25.GetChannelNotMove()[slot_44_0] then
		for iter_44_0 = 1, #ove_0_25.GetChannelNotMove()[slot_44_0] do
			local slot_44_1 = ove_0_25.GetChannelNotMove()[slot_44_0][iter_44_0]

			if arg_44_0.buff[slot_44_1.buffname] and arg_44_1.startPos:dist(arg_44_1.endPos) / arg_44_2.speed + (network.latency + arg_44_2.realDelay) - (arg_44_2.boundingRadiusMod == 1 and arg_44_2.realWidth + arg_44_0.boundingRadius or arg_44_2.realWidth) / arg_44_0.moveSpeed < arg_44_0.buff[slot_44_1.buffname].endTime - game.time then
				return true
			end
		end
	end

	return false
end

local ove_0_120 = string.sub
local ove_0_121 = string.len

local function ove_0_122()
	if not player.path.isDashing then
		return false
	end

	return navmesh.isWall(player.pos)
end

local function ove_0_123(arg_46_0, arg_46_1, arg_46_2)
	if ove_0_94(arg_46_0, arg_46_1, arg_46_2) then
		return false
	end

	if ove_0_98(arg_46_2) then
		return
	end

	if ove_0_25.IsCollidingWindwall(arg_46_1.startPos, arg_46_1.endPos, ove_0_34) then
		return false
	end

	if ove_0_25.IsCollidingWindwall(arg_46_1.startPos, arg_46_2.pos2D, ove_0_34) then
		return false
	end

	if arg_46_1.startPos:dist(arg_46_1.endPos) > arg_46_0.range then
		return false
	end

	if arg_46_2.path.isDashing and not arg_46_2.charName == "Warwick" then
		if ove_0_99(arg_46_0, arg_46_2, arg_46_1) then
			if #ove_0_25.CountMinions(arg_46_1.endPos:to3D(arg_46_2.pos.y), 100, "both") > 0 then
				return false
			end

			if #ove_0_25.CountMinions(arg_46_1.startPos:to3D(arg_46_2.pos.y), 100, "both") > 0 then
				return false
			end

			return true
		end

		return false
	end

	if ove_0_101(arg_46_1, arg_46_0, arg_46_2) then
		return true
	end

	if ove_0_119(arg_46_2, arg_46_1, arg_46_0) then
		return true
	end

	if ove_0_97(arg_46_2) then
		return false
	end

	local slot_46_0 = arg_46_2.moveSpeed > 0 and arg_46_2.moveSpeed or 1

	if ove_0_102(arg_46_2) > -0.1 and ove_0_102(arg_46_2) ~= 0 then
		if ove_0_102(arg_46_2) >= arg_46_1.startPos:dist(arg_46_1.endPos) / arg_46_0.speed + (network.latency + arg_46_0.realDelay) - (arg_46_0.boundingRadiusMod == 1 and arg_46_0.realWidth + arg_46_2.boundingRadius or arg_46_0.realWidth) / slot_46_0 then
			return true
		else
			return false
		end
	end

	if ove_0_104(arg_46_2) then
		return false
	end

	if arg_46_2.path.isDashing then
		if ove_0_99(arg_46_0, arg_46_2, arg_46_1) then
			if #ove_0_25.CountMinions(arg_46_1.endPos:to3D(arg_46_2.pos.y), 100, "both") > 0 then
				return false
			end

			if #ove_0_25.CountMinions(arg_46_1.startPos:to3D(arg_46_2.pos.y), 100, "both") > 0 then
				return false
			end

			return true
		end

		return false
	end

	if ove_0_106(arg_46_2) > 0 and ove_0_106(arg_46_2) < arg_46_1.startPos:dist(arg_46_1.endPos) / arg_46_0.speed + (network.latency + arg_46_0.realDelay) - 0.1 then
		return false
	end

	if ove_0_103(arg_46_2) > -0.1 and ove_0_103(arg_46_2) ~= 0 then
		if ove_0_103(arg_46_2) + 0.02 >= arg_46_1.startPos:dist(arg_46_1.endPos) / arg_46_0.speed + (network.latency + arg_46_0.realDelay) - (arg_46_0.boundingRadiusMod == 1 and arg_46_0.realWidth + arg_46_2.boundingRadius or arg_46_0.realWidth) / slot_46_0 then
			return true
		elseif ove_0_117(arg_46_2) == false then
			return false
		end
	end

	if arg_46_1.startPos:dist(arg_46_1.endPos) > arg_46_0.range * 0.98 then
		return false
	end

	if #ove_0_25.CountMinions(arg_46_1.endPos:to3D(arg_46_2.pos.y), 200, "both") > 0 and arg_46_2.pos:dist(player.pos) > 80 then
		return false
	end

	if #ove_0_25.CountMinions(arg_46_1.startPos:to3D(arg_46_2.pos.y), 100, "both") > 0 and arg_46_2.pos:dist(player.pos) > 80 then
		return false
	end

	if #ove_0_25.CountMinions(arg_46_2.pos, 100, "both") > 0 and arg_46_2.pos:dist(player.pos) > 80 then
		return false
	end

	if ove_0_107(arg_46_2) then
		return false
	end

	if ove_0_108(arg_46_2) then
		return false
	end

	if arg_46_2.moveSpeed >= 500 then
		if player.pos:dist(arg_46_2) <= 350 then
			return true
		end

		if arg_46_2.path.isActive and ove_0_111(player, arg_46_2) and not ove_0_112(arg_46_2, player) and arg_46_2.pos:dist(player.pos) <= 600 then
			return true
		end

		return
	end

	local slot_46_1 = ove_0_38.pred.range_strictness:get() == 1 and 0.99 or ove_0_38.pred.range_strictness:get() == 2 and 0.9 or 0.85

	if not arg_46_2.path.isActive and arg_46_1.startPos:dist(arg_46_1.endPos) > arg_46_0.range * slot_46_1 then
		return false
	end

	if arg_46_2.path.isActive and ove_0_115(arg_46_2, player) and ove_0_113(player, arg_46_2) and arg_46_2.pos:dist(player.pos) <= 600 then
		return true
	end

	if arg_46_2.path.isActive and ove_0_111(player, arg_46_2) and arg_46_2.pos:dist(player.pos) <= 800 then
		return true
	end

	if arg_46_2.path.isActive and ove_0_112(player, arg_46_2) and ove_0_112(arg_46_2, player) and arg_46_1.startPos:dist(arg_46_2.pos2D) > arg_46_0.range * slot_46_1 then
		return false
	end

	if arg_46_2.path.isActive and ove_0_114(player, arg_46_2) and arg_46_1.startPos:dist(arg_46_2.pos2D) > arg_46_0.range * slot_46_1 then
		return false
	end

	if arg_46_2.path.isActive and ove_0_110(player, arg_46_2, 0.7) then
		return true
	end

	if arg_46_2.path.isActive and ove_0_111(player, arg_46_2) and not ove_0_112(arg_46_2, player) then
		return true
	end

	if arg_46_2.path.isActive and player.pos:dist(arg_46_2) <= 430 then
		return true
	end

	if (arg_46_0.boundingRadiusMod == 1 and arg_46_0.realWidth + arg_46_2.boundingRadius or arg_46_0.realWidth) / slot_46_0 >= arg_46_1.startPos:dist(arg_46_1.endPos) / arg_46_0.speed + (arg_46_0.realDelay - 0.05) then
		return true
	end

	if arg_46_2.path.isActive and navmesh.isGrass(player.pos) and arg_46_2.pos:dist(player.pos) <= 800 and not navmesh.isGrass(arg_46_2.pos) then
		return true
	end

	local slot_46_2 = ove_0_38.pred.path_strictness:get() == 1 and 1.4 or ove_0_38.pred.path_strictness:get() == 2 and 1 or 0.6

	if ove_0_22.trace.newpath(arg_46_2, 0.033, 3) and (ove_0_38.pred.path_strictness:get() == 4 and true or ove_0_116(arg_46_2, slot_46_2)) then
		return true
	end

	if ove_0_118(arg_46_2) and ove_0_25.isCCDelay(arg_46_2) == false then
		return true
	end

	if ove_0_38.pred.wForce:get() and ove_0_122() then
		return true
	end
end

local function ove_0_124(arg_47_0, arg_47_1, arg_47_2, arg_47_3)
	if ove_0_98(arg_47_2) then
		return
	end

	if ove_0_25.IsCollidingWindwall(arg_47_1.startPos, arg_47_1.endPos, ove_0_34) then
		return false
	end

	if ove_0_25.IsCollidingWindwall(arg_47_1.startPos, arg_47_2.pos2D, ove_0_34) then
		return false
	end

	if arg_47_1.startPos:dist(arg_47_1.endPos) > arg_47_3 + 1300 then
		return false
	end

	if arg_47_2.path.isDashing and not arg_47_2.charName == "Warwick" then
		if ove_0_99(arg_47_0, arg_47_2, arg_47_1) then
			if #ove_0_25.CountMinions(arg_47_1.endPos:to3D(arg_47_2.pos.y), 100, "both") > 0 then
				return false
			end

			if #ove_0_25.CountMinions(arg_47_1.startPos:to3D(arg_47_2.pos.y), 100, "both") > 0 then
				return false
			end

			return true
		end

		return false
	end

	if ove_0_101(arg_47_1, arg_47_0, arg_47_2) then
		return true
	end

	if ove_0_119(arg_47_2, arg_47_1, arg_47_0) then
		return true
	end

	if ove_0_97(arg_47_2) then
		return false
	end

	local slot_47_0 = arg_47_2.moveSpeed > 0 and arg_47_2.moveSpeed or 1

	if ove_0_102(arg_47_2) > -0.1 and ove_0_102(arg_47_2) ~= 0 then
		if ove_0_102(arg_47_2) >= arg_47_1.startPos:dist(arg_47_1.endPos) / arg_47_0.speed + (network.latency + arg_47_0.realDelay) - (arg_47_0.boundingRadiusMod == 1 and arg_47_0.realWidth + arg_47_2.boundingRadius or arg_47_0.realWidth) / slot_47_0 then
			return true
		else
			return false
		end
	end

	if ove_0_104(arg_47_2) then
		return false
	end

	if arg_47_2.path.isDashing then
		if ove_0_99(arg_47_0, arg_47_2, arg_47_1) then
			if #ove_0_25.CountMinions(arg_47_1.endPos:to3D(arg_47_2.pos.y), 100, "both") > 0 then
				return false
			end

			if #ove_0_25.CountMinions(arg_47_1.startPos:to3D(arg_47_2.pos.y), 100, "both") > 0 then
				return false
			end

			return true
		end

		return false
	end

	if ove_0_106(arg_47_2) > 0 and ove_0_106(arg_47_2) < arg_47_1.startPos:dist(arg_47_1.endPos) / arg_47_0.speed + (network.latency + arg_47_0.realDelay) - 0.1 then
		return false
	end

	if ove_0_103(arg_47_2) > -0.1 and ove_0_103(arg_47_2) ~= 0 then
		if ove_0_103(arg_47_2) + 0.02 >= arg_47_1.startPos:dist(arg_47_1.endPos) / arg_47_0.speed + (network.latency + arg_47_0.realDelay) - (arg_47_0.boundingRadiusMod == 1 and arg_47_0.realWidth + arg_47_2.boundingRadius or arg_47_0.realWidth) / slot_47_0 then
			return true
		elseif ove_0_117(arg_47_2) == false then
			return false
		end
	end

	if arg_47_1.startPos:dist(arg_47_1.endPos) > (arg_47_3 + 1300) * 0.98 then
		return false
	end

	if #ove_0_25.CountMinions(arg_47_1.endPos:to3D(arg_47_2.pos.y), 200, "both") > 0 and arg_47_2.pos:dist(player.pos) > 80 then
		return false
	end

	if #ove_0_25.CountMinions(arg_47_1.startPos:to3D(arg_47_2.pos.y), 100, "both") > 0 and arg_47_2.pos:dist(player.pos) > 80 then
		return false
	end

	if #ove_0_25.CountMinions(arg_47_2.pos, 100, "both") > 0 and arg_47_2.pos:dist(player.pos) > 80 then
		return false
	end

	if ove_0_107(arg_47_2) then
		return false
	end

	if ove_0_108(arg_47_2) then
		return false
	end

	if arg_47_2.moveSpeed >= 500 then
		if player.pos:dist(arg_47_2) <= 350 then
			return true
		end

		if arg_47_2.path.isActive and ove_0_111(player, arg_47_2) and not ove_0_112(arg_47_2, player) and arg_47_2.pos:dist(player.pos) <= 600 then
			return true
		end

		return
	end

	local slot_47_1 = ove_0_38.pred.range_strictness:get() == 1 and 0.99 or ove_0_38.pred.range_strictness:get() == 2 and 0.9 or 0.85

	if not arg_47_2.path.isActive and arg_47_1.startPos:dist(arg_47_1.endPos) > (arg_47_3 + 1300) * slot_47_1 then
		return false
	end

	if arg_47_2.path.isActive and ove_0_115(arg_47_2, player) and ove_0_113(player, arg_47_2) and arg_47_2.pos:dist(player.pos) <= 600 then
		return true
	end

	if arg_47_2.path.isActive and ove_0_111(player, arg_47_2) and arg_47_2.pos:dist(player.pos) <= 800 then
		return true
	end

	if arg_47_2.path.isActive and ove_0_112(player, arg_47_2) and ove_0_112(arg_47_2, player) and arg_47_1.startPos:dist(arg_47_2.pos2D) > (arg_47_3 + 1300) * slot_47_1 then
		return false
	end

	if arg_47_2.path.isActive and ove_0_114(player, arg_47_2) and arg_47_1.startPos:dist(arg_47_2.pos2D) > (arg_47_3 + 1300) * slot_47_1 then
		return false
	end

	if arg_47_2.path.isActive and ove_0_110(player, arg_47_2, 0.7) then
		return true
	end

	if arg_47_2.path.isActive and ove_0_111(player, arg_47_2) and not ove_0_112(arg_47_2, player) then
		return true
	end

	if arg_47_2.path.isActive and player.pos:dist(arg_47_2) <= 430 then
		return true
	end

	if (arg_47_0.boundingRadiusMod == 1 and arg_47_0.realWidth + arg_47_2.boundingRadius or arg_47_0.realWidth) / slot_47_0 >= arg_47_1.startPos:dist(arg_47_1.endPos) / arg_47_0.speed + (arg_47_0.realDelay - 0.05) then
		return true
	end

	if arg_47_2.path.isActive and navmesh.isGrass(player.pos) and arg_47_2.pos:dist(player.pos) <= 800 and not navmesh.isGrass(arg_47_2.pos) then
		return true
	end

	local slot_47_2 = ove_0_38.pred.path_strictness:get() == 1 and 1.4 or ove_0_38.pred.path_strictness:get() == 2 and 1 or 0.6

	if ove_0_22.trace.newpath(arg_47_2, 0.033, 3) and (ove_0_38.pred.path_strictness:get() == 4 and true or ove_0_116(arg_47_2, slot_47_2)) then
		return true
	end

	if ove_0_118(arg_47_2) and ove_0_25.isCCDelay(arg_47_2) == false then
		return true
	end
end

local function ove_0_125(arg_48_0)
	for iter_48_0 = 1, #ove_0_26.core.skillshots do
		local slot_48_0 = ove_0_26.core.skillshots[iter_48_0]

		for iter_48_1 = 20, 350, 40 do
			if ove_0_25.InsideSpellPos(slot_48_0, ove_0_25.VectorExtend(player.pos, arg_48_0, iter_48_1):to2D()) == true and ove_0_25.InsideSpellPos(slot_48_0, player.pos2D) == false then
				return false
			end
		end
	end

	return true
end

local function ove_0_126(arg_49_0)
	if ove_0_25.CollidesWithPoppy(arg_49_0) then
		return false
	end

	if ove_0_125(arg_49_0) == false then
		return false
	end

	for iter_49_0 = 1, 320, 60 do
		if navmesh.isWall(ove_0_25.VectorExtend(player.pos, arg_49_0, iter_49_0)) or ove_0_25.isWallValid(ove_0_25.VectorExtend(player.pos, arg_49_0, iter_49_0)) then
			return false
		end
	end

	return true
end

local function ove_0_127(arg_50_0, arg_50_1, arg_50_2, arg_50_3)
	local slot_50_0 = 350

	if not ove_0_25.IsFacing(arg_50_0, player) and arg_50_0.path.isActive then
		slot_50_0 = 200
	end

	if arg_50_3 then
		slot_50_0 = 100
	end

	if ove_0_25.CollidesWithPoppy(arg_50_1) then
		return false
	end

	if ove_0_125(arg_50_1) == false then
		return false
	end

	if arg_50_2 == true then
		for iter_50_0 = 1, 320, 60 do
			if navmesh.isWall(ove_0_25.VectorExtend(player.pos, arg_50_1, iter_50_0)) or ove_0_25.isWallValid(ove_0_25.VectorExtend(player.pos, arg_50_1, iter_50_0)) then
				return false
			end
		end
	end

	if #ove_0_25.CountEnemiesInv(arg_50_1, slot_50_0, 1) >= 1 then
		return false
	end

	if ove_0_38.turret:get() == false and ove_0_25.getturretenemy(arg_50_1) and arg_50_0.type ~= TYPE_TURRET then
		return false
	end

	if ove_0_25.getfountain(arg_50_1) then
		return false
	end

	if ove_0_24.menu.combat.key:get() and #ove_0_25.CountEnemiesInv(arg_50_1, 600, 1) >= ove_0_38.combo.donte:get() then
		return false
	end

	if ove_0_24.menu.hybrid.key:get() and #ove_0_25.CountEnemiesInv(arg_50_1, 600, 1) >= ove_0_38.harass.donte:get() then
		return false
	end

	return true
end

local function ove_0_128(arg_51_0, arg_51_1, arg_51_2)
	local slot_51_0 = {}

	for iter_51_0 = 0, (arg_51_2 or 16) - 1 do
		local slot_51_1 = 2 * ove_0_61 / arg_51_2 * (iter_51_0 + 0.5)
		local slot_51_2 = arg_51_0.x + arg_51_1 * ove_0_66(slot_51_1)
		local slot_51_3 = arg_51_0.z + arg_51_1 * ove_0_67(slot_51_1)

		ove_0_59(slot_51_0, vec2(slot_51_2, slot_51_3))
	end

	return slot_51_0
end

local function ove_0_129(arg_52_0, arg_52_1, arg_52_2)
	local slot_52_0 = {}

	for iter_52_0 = 0, (arg_52_2 or 16) - 1 do
		local slot_52_1 = 2 * ove_0_61 / arg_52_2 * (iter_52_0 + 0.5)
		local slot_52_2 = arg_52_0.x + arg_52_1 * ove_0_66(slot_52_1)
		local slot_52_3 = arg_52_0.z + arg_52_1 * ove_0_67(slot_52_1)

		ove_0_59(slot_52_0, vec2(slot_52_2, slot_52_3))
	end

	return slot_52_0
end

local function ove_0_130(arg_53_0)
	local slot_53_0 = ove_0_93()

	if ove_0_25.IsValidTarget(slot_53_0, false, true, true, false) and ove_0_22.core.get_pos_after_time(slot_53_0, 300 / (player.moveSpeed + 600)):dist(player.pos2D) <= ove_0_33.range + 300 and slot_53_0.pos:dist(player.pos) >= player.attackRange + player.boundingRadius + slot_53_0.boundingRadius + 100 then
		local slot_53_1 = ove_0_54(ove_0_53(slot_53_0.pos2D, player.pos2D, player.pos:dist(mousePos) < 1500 and ove_0_25.VectorExtend(player.pos, mousePos, 1500):to2D() or mousePos2D))

		if math.abs(slot_53_1) > 80 then
			if arg_53_0 == 1 then
				local slot_53_2 = ove_0_25.VectorExtend(player.pos, game.mousePos, 300 + player.boundingRadius)
				local slot_53_3 = ove_0_25.VectorExtend(player.pos, game.mousePos, 300)

				if ove_0_127(slot_53_0, slot_53_2, true) and #ove_0_25.CountEnemies(slot_53_2, 450) == 0 then
					if slot_53_0.pos:dist(slot_53_3) < ove_0_33.range then
						player:castSpell("pos", 2, slot_53_2)
					end
				elseif ove_0_69 < game.time then
					local slot_53_4 = ove_0_25.VectorExtend(player.pos, game.mousePos, 300)
					local slot_53_5 = ove_0_128(player.pos, 300, 16)
					local slot_53_6 = {}

					if ove_0_127(slot_53_0, slot_53_4, true) and slot_53_0.pos:dist(slot_53_4) < ove_0_33.range and #ove_0_25.CountEnemies(slot_53_2, 500) == 0 then
						player:castSpell("pos", 2, mousePos)
					end

					for iter_53_0, iter_53_1 in pairs(slot_53_5) do
						if ove_0_127(slot_53_0, iter_53_1:to3D(player.pos.y), true) and slot_53_0.pos:dist(iter_53_1:to3D(player.pos.y)) < ove_0_33.range and #ove_0_25.CountEnemies(slot_53_2, 450) == 0 then
							ove_0_59(slot_53_6, iter_53_1:to3D(player.pos.y))
						end
					end

					local slot_53_7
					local slot_53_8 = 9999

					for iter_53_2, iter_53_3 in pairs(slot_53_6) do
						if iter_53_3 and slot_53_8 > iter_53_3:dist(mousePos) then
							slot_53_7 = iter_53_3
							slot_53_8 = iter_53_3:dist(mousePos)
						end
					end

					if slot_53_7 then
						player:castSpell("pos", 2, slot_53_7)
					end

					ove_0_69 = game.time + 0.1
				end
			end

			if arg_53_0 == 2 then
				local slot_53_9 = ove_0_33.range
				local slot_53_10 = ove_0_22.core.get_pos_after_time(slot_53_0, 300 / (player.moveSpeed + 600))

				if slot_53_10 then
					local slot_53_11, slot_53_12 = mathf.sect_circle_circle(slot_53_10, 400, player.pos2D, ove_0_33.range)
					local slot_53_13 = false

					if slot_53_11 and slot_53_12 then
						local slot_53_14 = slot_53_11:to3D()
						local slot_53_15 = slot_53_12:to3D()

						if slot_53_14:dist(game.mousePos) <= slot_53_15:dist(game.mousePos) and slot_53_14:dist(slot_53_0) < ove_0_33.range - 50 then
							if ove_0_127(slot_53_0, slot_53_14, true) then
								player:castSpell("pos", 2, slot_53_14)
							else
								slot_53_13 = true
							end
						elseif ove_0_127(slot_53_0, slot_53_15, true) and slot_53_15:dist(slot_53_0) < ove_0_33.range - 50 then
							player:castSpell("pos", 2, slot_53_15)
						else
							slot_53_13 = true
						end
					else
						slot_53_13 = true
					end

					if slot_53_13 and ove_0_70 < game.time then
						local slot_53_16 = ove_0_25.VectorExtend(player.pos, game.mousePos, 300)
						local slot_53_17 = ove_0_129(player.pos, 300, 16)
						local slot_53_18 = {}

						if ove_0_127(slot_53_0, slot_53_16, true) and slot_53_0.pos:dist(slot_53_16) < ove_0_33.range and #ove_0_25.CountEnemies(slot_53_16, 450) == 0 then
							player:castSpell("pos", 2, mousePos)
						end

						local slot_53_19
						local slot_53_20 = 9999

						for iter_53_4, iter_53_5 in pairs(slot_53_17) do
							local slot_53_21 = iter_53_5:to3D(player.pos.y)

							if ove_0_127(slot_53_0, slot_53_21, true) and slot_53_0.pos:dist(slot_53_21) < ove_0_33.range and #ove_0_25.CountEnemies(slot_53_16, 450) == 0 and slot_53_10:to3D(player.pos.y):dist(player.pos) <= ove_0_33.range and slot_53_21 and slot_53_20 > slot_53_21:dist(mousePos) then
								slot_53_19 = slot_53_21
								slot_53_20 = slot_53_21:dist(mousePos)
							end
						end

						if slot_53_19 then
							player:castSpell("pos", 2, slot_53_19)
						end

						ove_0_70 = game.time + 0.1
					end
				end
			end
		end
	end
end

local function ove_0_131(arg_54_0, arg_54_1)
	local slot_54_0 = ove_0_24.combat.target

	if arg_54_1 then
		slot_54_0 = arg_54_1
	end

	if arg_54_0 == 1 then
		local slot_54_1 = ove_0_25.VectorExtend(player.pos, game.mousePos, 300 + player.boundingRadius)
		local slot_54_2 = ove_0_25.VectorExtend(player.pos, game.mousePos, 300)

		if ove_0_127(slot_54_0, slot_54_1, true) then
			if slot_54_0.pos:dist(slot_54_2) < player.attackRange + player.boundingRadius + slot_54_0.boundingRadius then
				player:castSpell("pos", 2, slot_54_1)
			end
		elseif ove_0_71 < game.time then
			local slot_54_3 = ove_0_25.VectorExtend(player.pos, game.mousePos, 300)
			local slot_54_4 = ove_0_128(player.pos, 300, 16)
			local slot_54_5 = {}

			if ove_0_127(slot_54_0, slot_54_3, true) and slot_54_0.pos:dist(slot_54_3) < player.attackRange + player.boundingRadius + slot_54_0.boundingRadius then
				player:castSpell("pos", 2, mousePos)
			end

			for iter_54_0, iter_54_1 in pairs(slot_54_4) do
				if ove_0_127(slot_54_0, iter_54_1:to3D(player.pos.y), true) and slot_54_0.pos:dist(iter_54_1:to3D(player.pos.y)) < player.attackRange then
					ove_0_59(slot_54_5, iter_54_1:to3D(player.pos.y))
				end
			end

			local slot_54_6
			local slot_54_7 = 9999

			for iter_54_2, iter_54_3 in pairs(slot_54_5) do
				if iter_54_3 and slot_54_7 > iter_54_3:dist(mousePos) then
					slot_54_6 = iter_54_3
					slot_54_7 = iter_54_3:dist(mousePos)
				end
			end

			if slot_54_6 then
				player:castSpell("pos", 2, slot_54_6)
			end

			ove_0_71 = game.time + 0.1
		end
	end

	if arg_54_0 == 2 then
		local slot_54_8 = ove_0_25.IsFacing(slot_54_0, player) and 500 or 200
		local slot_54_9 = ove_0_22.core.get_pos_after_time(slot_54_0, 300 / (player.moveSpeed + 600))

		if ove_0_25.IsFacing(slot_54_0, player) and ove_0_72 < game.time then
			local slot_54_10 = ove_0_25.VectorExtend(player.pos, game.mousePos, 300)
			local slot_54_11 = ove_0_129(player.pos, 300, 16)
			local slot_54_12 = {}

			if ove_0_127(slot_54_0, slot_54_10, true) and slot_54_0.pos:dist(slot_54_10) < player.attackRange + player.boundingRadius + slot_54_0.boundingRadius then
				player:castSpell("pos", 2, mousePos)
			end

			local slot_54_13
			local slot_54_14 = 9999
			local slot_54_15 = 1

			for iter_54_4, iter_54_5 in pairs(slot_54_11) do
				local slot_54_16 = iter_54_5:to3D(player.pos.y)

				if ove_0_127(slot_54_0, slot_54_16, true) and slot_54_0.pos:dist(slot_54_16) < player.attackRange and slot_54_9:to3D(player.pos.y):dist(player.pos) <= player.attackRange + player.boundingRadius + slot_54_0.boundingRadius and slot_54_16 and slot_54_14 > slot_54_16:dist(mousePos) then
					slot_54_13 = slot_54_16
					slot_54_14 = slot_54_16:dist(mousePos)
				end
			end

			if slot_54_13 then
				player:castSpell("pos", 2, slot_54_13)
			end

			ove_0_72 = game.time + 0.1
		end

		if slot_54_9 then
			local slot_54_17, slot_54_18 = mathf.sect_circle_circle(slot_54_9, slot_54_8, player.pos2D, 300)
			local slot_54_19 = false

			if slot_54_17 and slot_54_18 then
				local slot_54_20 = slot_54_17:to3D()
				local slot_54_21 = slot_54_18:to3D()

				if slot_54_20:dist(game.mousePos) <= slot_54_21:dist(game.mousePos) then
					if ove_0_127(slot_54_0, slot_54_20, true) and slot_54_0.pos:dist(slot_54_20) < player.attackRange + player.boundingRadius + slot_54_0.boundingRadius then
						player:castSpell("pos", 2, slot_54_20)
					else
						slot_54_19 = true
					end
				elseif ove_0_127(slot_54_0, slot_54_21, true) and slot_54_0.pos:dist(slot_54_21) < player.attackRange + player.boundingRadius + slot_54_0.boundingRadius then
					player:castSpell("pos", 2, slot_54_21)
				else
					slot_54_19 = true
				end
			else
				slot_54_19 = true
			end

			if slot_54_19 and ove_0_73 < game.time then
				local slot_54_22 = ove_0_25.VectorExtend(player.pos, game.mousePos, 300)
				local slot_54_23 = ove_0_129(player.pos, 300, 16)
				local slot_54_24 = {}

				if ove_0_127(slot_54_0, slot_54_22, true) and slot_54_0.pos:dist(slot_54_22) < player.attackRange + player.boundingRadius + slot_54_0.boundingRadius then
					player:castSpell("pos", 2, mousePos)
				end

				local slot_54_25
				local slot_54_26 = 9999

				for iter_54_6, iter_54_7 in pairs(slot_54_23) do
					local slot_54_27 = iter_54_7:to3D(player.pos.y)

					if ove_0_127(slot_54_0, slot_54_27, true) and slot_54_0.pos:dist(slot_54_27) < player.attackRange and slot_54_9:to3D(player.pos.y):dist(player.pos) <= player.attackRange + player.boundingRadius + slot_54_0.boundingRadius and slot_54_27 and slot_54_26 >= slot_54_27:dist(mousePos) then
						slot_54_25 = slot_54_27
						slot_54_26 = slot_54_27:dist(mousePos)
					end
				end

				if slot_54_25 then
					player:castSpell("pos", 2, slot_54_25)
				end

				ove_0_73 = game.time + 0.1
			end
		end
	end
end

local function ove_0_132()
	local slot_55_0 = ove_0_88()

	if ove_0_25.IsValidTarget(slot_55_0) then
		local slot_55_1 = 0
		local slot_55_2 = ove_0_22.core.get_pos_after_time(slot_55_0, 0.75 + ove_0_34.delay + slot_55_0.pos:dist(player.pos) / ove_0_34.speed)

		if slot_55_2 then
			local slot_55_3 = ove_0_51(ove_0_34.range - 50, slot_55_0.pos:dist(player.pos))

			for iter_55_0 = 0, slot_55_3, 50 do
				local slot_55_4 = ove_0_25.VectorExtend(player.pos, slot_55_2:to3D(player.pos.y), iter_55_0)

				if navmesh.isWall(slot_55_4) or ove_0_25.isWallValid(slot_55_4) then
					slot_55_1 = iter_55_0

					break
				end
			end

			if slot_55_1 ~= 0 and slot_55_1 <= player.pos:dist(slot_55_0.pos) then
				ove_0_34.delay = 0.75 + ove_0_34.delay + slot_55_1 / ove_0_34.speed
				ove_0_34.speed = math.huge
				ove_0_34.width = 80
				ove_0_34.realDelay = 0.75 + ove_0_34.delay + slot_55_1 / ove_0_34.speed
				ove_0_34.realWidth = 80

				local slot_55_5 = ove_0_22.linear.get_prediction(ove_0_34, slot_55_0)

				if slot_55_5 and ove_0_124(ove_0_34, slot_55_5, slot_55_0, slot_55_1) and not ove_0_25.IsCollidingWindwall(slot_55_5.startPos, slot_55_5.endPos, ove_0_33) and not ove_0_94(ove_0_34, {
					startPos = player.pos2D,
					endPos = ove_0_25.VectorExtend(player.pos, slot_55_5.endPos:to3D(), slot_55_1):to2D()
				}, slot_55_0) then
					player:castSpell("pos", 1, vec3(slot_55_5.endPos.x, slot_55_0.pos.y, slot_55_5.endPos.y))
				end
			end
		end
	end
end

local function ove_0_133()
	if ove_0_38.combo.ecombo:get() and player:spellSlot(2).state == 0 and ove_0_38.combo.egap:get() then
		ove_0_130(ove_0_38.combo.emode:get())
	end

	if ove_0_38.combo.ecombo:get() and player:spellSlot(2).state == 0 and not ove_0_24.core.is_winding_up_attack() and ove_0_24.combat.target then
		ove_0_131(ove_0_38.combo.emode:get())
	end

	if ove_0_38.combo.qcombo:get() and player:spellSlot(0).state == 0 and not ove_0_24.core.is_winding_up_attack() then
		local slot_56_0 = ove_0_91()

		if ove_0_25.IsValidTarget(slot_56_0) then
			local slot_56_1 = ove_0_22.linear.get_prediction(ove_0_33, slot_56_0)

			if slot_56_1 and slot_56_1.startPos:dist(slot_56_1.endPos) < ove_0_33.range and not ove_0_25.IsCollidingWindwall(slot_56_1.startPos, slot_56_1.endPos, ove_0_33) and (not ove_0_94(ove_0_33, slot_56_1, slot_56_0) or player.buff.zeriespecialrounds or player.buff.zerir) then
				player:castSpell("pos", 0, vec3(slot_56_1.endPos.x, slot_56_0.pos.y, slot_56_1.endPos.y))
			end
		end
	end

	if ove_0_38.combo.wcombo:get() and ove_0_38.combo.incWalls:get() and player:spellSlot(1).state == 0 and not ove_0_24.core.is_winding_up_attack() then
		ove_0_132()
	end

	if ove_0_38.combo.wcombo:get() and player:spellSlot(1).state == 0 and not ove_0_24.core.is_winding_up_attack() and (ove_0_38.auto.saveManaE:get() == false or player.mana - player.manaCost1 >= player.manaCost2 or player:spellSlot(2).level == 0 or player:spellSlot(2).state ~= 0 and player:spellSlot(2).cooldown > 10) then
		local slot_56_2 = ove_0_90()

		if ove_0_25.IsValidTarget(slot_56_2, false, true, true) then
			local slot_56_3 = ove_0_22.linear.get_prediction(ove_0_34, slot_56_2)

			if slot_56_3 and ove_0_123(ove_0_34, slot_56_3, slot_56_2) then
				player:castSpell("pos", 1, vec3(slot_56_3.endPos.x, slot_56_2.pos.y, slot_56_3.endPos.y))
			end
		end
	end

	if ove_0_38.combo.rusage:get() ~= 3 and player:spellSlot(3).state == 0 then
		local slot_56_4 = ove_0_86()

		if ove_0_25.IsValidTarget(slot_56_4, false, true, true) and ove_0_22.core.get_pos_after_time(slot_56_4, 0.25):dist(player.pos2D) <= ove_0_36.range then
			if ove_0_38.combo.rusage:get() == 1 and ove_0_25.GetHPPercent(slot_56_4) <= ove_0_38.combo.hpr:get() and ove_0_25.GetHPPercent(slot_56_4) > ove_0_38.combo.waster:get() then
				player:castSpell("self", 3)
			end

			if ove_0_38.combo.rusage:get() == 2 and slot_56_4.health <= ove_0_79(slot_56_4) + ove_0_25.CalculateAADamage(slot_56_4, player, ove_0_38.combo.includeaa:get()) + ove_0_83(slot_56_4) + ove_0_81(slot_56_4) and ove_0_25.GetHPPercent(slot_56_4) > ove_0_38.combo.waster:get() then
				player:castSpell("self", 3)
			end
		end

		local slot_56_5 = ove_0_86()

		if ove_0_25.IsValidTarget(slot_56_5, false, true, false) and ove_0_22.core.get_pos_after_time(slot_56_5, 0.25):dist(player.pos2D) <= ove_0_36.range and ove_0_38.combo.hitr:get() > 1 and #ove_0_25.CountEnemiesAfterX(player.pos, ove_0_36.range, 0.25) >= ove_0_38.combo.hitr:get() then
			player:castSpell("self", 3)
		end
	end
end

local ove_0_134 = {
	speed = 2600,
	boundingRadiusMod = 1,
	delay = 0.1,
	range = ove_0_21.parse(ove_0_20.data).Q,
	damage = function(arg_57_0)
		return ove_0_79(arg_57_0)
	end
}

local function ove_0_135()
	if ove_0_38.harass.qLast:get() and player:spellSlot(0).state == 0 and not ove_0_24.core.is_winding_up_attack() then
		local slot_58_0 = ove_0_25.CountMinions(player.pos, ove_0_33.range, "enemy")

		for iter_58_0, iter_58_1 in pairs(slot_58_0) do
			if ove_0_24.farm.predict_hp(iter_58_1, iter_58_1.pos2D:dist(player.pos2D) / ove_0_134.speed + ove_0_134.delay) <= ove_0_79(iter_58_1) and iter_58_1 ~= ove_0_57 then
				local slot_58_1 = ove_0_22.linear.get_prediction(ove_0_33, iter_58_1)

				if slot_58_1.startPos:dist(slot_58_1.endPos) <= ove_0_33.range and not ove_0_94(ove_0_33, slot_58_1, iter_58_1) then
					player:castSpell("pos", 0, vec3(slot_58_1.endPos.x, iter_58_1.pos.y, slot_58_1.endPos.y))
				end
			end
		end
	end

	if ove_0_38.harass.ecombo:get() and player:spellSlot(2).state == 0 and ove_0_38.harass.egap:get() then
		ove_0_130(ove_0_38.harass.emode:get())
	end

	if ove_0_38.harass.ecombo:get() and player:spellSlot(2).state == 0 and not ove_0_24.core.is_winding_up_attack() and ove_0_24.combat.target then
		ove_0_131(ove_0_38.harass.emode:get())
	end

	if ove_0_38.harass.qcombo:get() and player:spellSlot(0).state == 0 and not ove_0_24.core.is_winding_up_attack() then
		local slot_58_2 = ove_0_91()

		if ove_0_25.IsValidTarget(slot_58_2) then
			local slot_58_3 = ove_0_22.linear.get_prediction(ove_0_33, slot_58_2)

			if slot_58_3 and slot_58_3.startPos:dist(slot_58_3.endPos) < ove_0_33.range and not ove_0_25.IsCollidingWindwall(slot_58_3.startPos, slot_58_3.endPos, ove_0_33) and (not ove_0_94(ove_0_33, slot_58_3, slot_58_2) or player.buff.zeriespecialrounds or player.buff.zerir) then
				player:castSpell("pos", 0, vec3(slot_58_3.endPos.x, slot_58_2.pos.y, slot_58_3.endPos.y))
			end
		end
	end

	if ove_0_38.harass.wcombo:get() and ove_0_38.harass.incWalls:get() and player:spellSlot(1).state == 0 and not ove_0_24.core.is_winding_up_attack() then
		ove_0_132()
	end

	if ove_0_38.harass.wcombo:get() and player:spellSlot(1).state == 0 and not ove_0_24.core.is_winding_up_attack() and (ove_0_38.auto.saveManaE:get() == false or player.mana - player.manaCost1 >= player.manaCost2 or player:spellSlot(2).level == 0 or player:spellSlot(2).state ~= 0 and player:spellSlot(2).cooldown > 10) then
		local slot_58_4 = ove_0_90()

		if ove_0_25.IsValidTarget(slot_58_4, false, true, true) then
			local slot_58_5 = ove_0_22.linear.get_prediction(ove_0_34, slot_58_4)

			if slot_58_5 and ove_0_123(ove_0_34, slot_58_5, slot_58_4) then
				player:castSpell("pos", 1, vec3(slot_58_5.endPos.x, slot_58_4.pos.y, slot_58_5.endPos.y))
			end
		end
	end
end

ove_0_25.AddPermashow("Farm", {
	"ON",
	"OFF"
}, ove_0_38.farming.toggle)
ove_0_25.AddPermashow("E Under-Turret", {
	"ON",
	"OFF"
}, ove_0_38.turret)
ove_0_25.AddPermashow("Auto Q", {
	"ON",
	"OFF"
}, ove_0_38.harass.autoqharass)
ove_0_25.AddPermashowCustom("E Walljump", {
	"ON",
	"OFF"
}, ove_0_38.eWall)
ove_0_25.AddPermashowCustom("Semi-W", {
	"ON",
	"OFF"
}, ove_0_38.semiw)

local function ove_0_136()
	if ove_0_43 > game.time then
		return
	end

	ove_0_74 = false

	for iter_59_0 = 0, 5 do
		if player:itemID(iter_59_0) == 6676 then
			ove_0_74 = true
		end
	end

	ove_0_43 = game.time + 0.1
end

local function ove_0_137(arg_60_0, arg_60_1, arg_60_2)
	local slot_60_0 = {}
	local slot_60_1 = 0

	for iter_60_0 = 1, arg_60_0 do
		local slot_60_2 = iter_60_0 * 2 * (3.14 / arg_60_0)

		slot_60_0[slot_60_1] = vec3(arg_60_2.x + arg_60_1 * ove_0_66(slot_60_2), arg_60_2.y, arg_60_2.z + arg_60_1 * ove_0_67(slot_60_2))
		slot_60_1 = slot_60_1 + 1
	end

	return slot_60_0
end

local function ove_0_138(arg_61_0)
	for iter_61_0 = 0, objManager.turrets.size[TEAM_ALLY] - 1 do
		local slot_61_0 = objManager.turrets[TEAM_ALLY][iter_61_0]

		if slot_61_0 and not slot_61_0.isDead and slot_61_0.pos:dist(arg_61_0) < 900 then
			return true
		end
	end

	return false
end

local function ove_0_139(arg_62_0)
	for iter_62_0 = 0, objManager.turrets.size[TEAM_ENEMY] - 1 do
		local slot_62_0 = objManager.turrets[TEAM_ENEMY][iter_62_0]

		if slot_62_0 and not slot_62_0.isDead and slot_62_0.pos:dist(arg_62_0) < 900 then
			return slot_62_0.pos:dist(arg_62_0)
		end
	end

	return 2000
end

local function ove_0_140(arg_63_0)
	local slot_63_0 = ove_0_137(15, ove_0_35.range, player.pos)
	local slot_63_1 = ove_0_25.VectorExtend(player.pos, arg_63_0, -ove_0_35.range)
	local slot_63_2 = #ove_0_25.CountEnemiesInv(slot_63_1, 400, 1)
	local slot_63_3 = ove_0_139(slot_63_1)

	for iter_63_0, iter_63_1 in pairs(slot_63_0) do
		local slot_63_4 = #ove_0_25.CountEnemiesInv(iter_63_1, 400, 1)

		if ove_0_138(iter_63_1) == true and ove_0_126(iter_63_1) then
			return iter_63_1
		elseif (#ove_0_25.CountEnemiesInv(slot_63_1, 400, 1) > 0 or ove_0_25.getturretenemy(slot_63_1) == true) and ove_0_126(iter_63_1) then
			if slot_63_4 < slot_63_2 and (ove_0_25.getturretenemy(iter_63_1) == false or slot_63_3 < ove_0_139(iter_63_1)) and navmesh.isWall(iter_63_1) == false then
				slot_63_2 = slot_63_4
				slot_63_1 = iter_63_1
				slot_63_3 = ove_0_139(iter_63_1)
			end

			if (slot_63_4 == slot_63_2 or slot_63_4 == 0) and (ove_0_25.getturretenemy(iter_63_1) == false or slot_63_3 < ove_0_139(iter_63_1)) and navmesh.isWall(iter_63_1) == false then
				slot_63_2 = slot_63_4
				slot_63_1 = iter_63_1
				slot_63_3 = ove_0_139(iter_63_1)
			end
		end
	end

	return slot_63_1
end

local function ove_0_141(arg_64_0)
	if player.pos:dist(arg_64_0.endPos) < player.pos:dist(arg_64_0.startPos) and (not arg_64_0.isBlink or arg_64_0.endPos:dist(player.pos) <= 400) then
		local slot_64_0 = arg_64_0.endPos

		if player.pos:dist(arg_64_0.endPos) <= 40 then
			slot_64_0 = ove_0_25.VectorExtend(player.pos, arg_64_0.startPos, -100)
		end

		if ove_0_38.auto.GapE.GapMe:get() and arg_64_0.endPos:dist(player.pos) > 300 then
			return
		end

		if (arg_64_0.owner.charName ~= "Riven" or arg_64_0.spellInfo.menuslot == "E") and ove_0_38.auto.GapE.spellList[arg_64_0.owner.charName .. arg_64_0.spellInfo.menuslot]:get() then
			local slot_64_1 = ove_0_140(slot_64_0)

			if slot_64_1 then
				player:castSpell("pos", 2, slot_64_1)
			end
		end

		if arg_64_0.owner.charName == "Riven" and arg_64_0.spellInfo.slot == 0 and ove_0_38.auto.GapE.spellList[arg_64_0.owner.charName .. arg_64_0.QType]:get() then
			local slot_64_2 = ove_0_140(slot_64_0)

			if slot_64_2 then
				player:castSpell("pos", 2, slot_64_2)
			end
		end
	end
end

local function ove_0_142()
	if ove_0_25.RecallActive(player) then
		return
	end

	if ove_0_38.auto.GapE.GapIgnore:get() and ove_0_25.getturretenemy(player.pos) then
		return
	end

	if ove_0_38.auto.GapE.GapA:get() and player:spellSlot(2).state == 0 then
		for iter_65_0, iter_65_1 in pairs(ove_0_25.GetDashers()) do
			if iter_65_1.owner and iter_65_1.startPos and iter_65_1.endPos and iter_65_1.begin < game.time and iter_65_1.owner.isTargetable and player.pos:dist(iter_65_1.endPos) < ove_0_35.range then
				if ove_0_38.auto.GapE.delayGap:get() == 0 then
					ove_0_141(iter_65_1)
				elseif ove_0_55 == false then
					ove_0_55 = true

					ove_0_25.DelayAction(function()
						ove_0_141(iter_65_1)

						ove_0_55 = false
					end, ove_0_38.auto.GapE.delayGap:get() / 1000)
				end
			end
		end
	end
end

local function ove_0_143()
	if ove_0_25.RecallActive(player) then
		return
	end

	for iter_67_0 = 0, objManager.enemies_n - 1 do
		local slot_67_0 = objManager.enemies[iter_67_0]

		if ove_0_38.auto.saveManaE:get() == false or player.mana - player.manaCost1 >= player.manaCost2 or player:spellSlot(2).level == 0 or player:spellSlot(2).state ~= 0 and player:spellSlot(2).cooldown > 10 then
			if ove_0_38.auto.autowkill:get() and ove_0_25.IsValidTarget(slot_67_0, false, true, true) and slot_67_0.health <= ove_0_81(slot_67_0) and player:spellSlot(1).state == 0 and not ove_0_24.core.is_winding_up_attack() and not ove_0_24.core.is_spell_locked() and slot_67_0.pos:dist(player.pos) <= ove_0_34.range then
				local slot_67_1 = ove_0_22.linear.get_prediction(ove_0_34, slot_67_0)

				if slot_67_1 and ove_0_123(ove_0_34, slot_67_1, slot_67_0) then
					player:castSpell("pos", 1, vec3(slot_67_1.endPos.x, slot_67_0.pos.y, slot_67_1.endPos.y))

					return
				end
			end

			if ove_0_38.auto.autowcc:get() and player:spellSlot(1).state == 0 and ove_0_25.IsValidTarget(slot_67_0, false, true, true) and slot_67_0.pos:dist(player.pos) <= ove_0_34.range - 50 then
				local slot_67_2 = ove_0_22.linear.get_prediction(ove_0_34, slot_67_0)
				local slot_67_3 = string.lower(slot_67_0.charName)

				if ove_0_25.GetChannelNotMove()[slot_67_3] then
					for iter_67_1 = 1, #ove_0_25.GetChannelNotMove()[slot_67_3] do
						local slot_67_4 = ove_0_25.GetChannelNotMove()[slot_67_3][iter_67_1]

						if slot_67_0.buff[slot_67_4.buffname] and slot_67_2 and player.pos:dist(vec3(slot_67_2.endPos.x, slot_67_0.y, slot_67_2.endPos.y)) < ove_0_34.range and not ove_0_94(ove_0_34, slot_67_2, slot_67_0) and ove_0_34.realDelay + slot_67_2.startPos:dist(slot_67_2.endPos) / ove_0_34.speed < slot_67_0.buff[slot_67_4.buffname].endTime - game.time then
							player:castSpell("pos", 1, vec3(slot_67_2.endPos.x, slot_67_0.pos.y, slot_67_2.endPos.y))
						end
					end
				end

				if slot_67_2 and player.pos:dist(vec3(slot_67_2.endPos.x, slot_67_0.pos.y, slot_67_2.endPos.y)) < ove_0_34.range and ove_0_101(slot_67_2, ove_0_34, slot_67_0) and not ove_0_94(ove_0_34, slot_67_2, slot_67_0) then
					player:castSpell("pos", 1, vec3(slot_67_2.endPos.x, slot_67_0.pos.y, slot_67_2.endPos.y))
				end
			end
		end

		if slot_67_0 and (ove_0_25.RecallActive(player) or true) then
			if not slot_67_0.buff[BUFF_SLOW] then
				if ove_0_44[slot_67_0.networkID] and slot_67_0.moveSpeed / ove_0_44[slot_67_0.networkID].speed <= 0.8 and (ove_0_44[slot_67_0.networkID].lastCheck > game.time or ove_0_44[slot_67_0.networkID].lastBuffTime > game.time) then
					return
				end

				ove_0_44[slot_67_0.networkID] = {
					lastBuffTime = 0,
					speed = slot_67_0.moveSpeed,
					lastCheck = game.time + 0.2
				}
			elseif ove_0_44[slot_67_0.networkID] then
				ove_0_44[slot_67_0.networkID].lastBuffTime = game.time + 0.35
			end
		end
	end
end

local function ove_0_144()
	if player:spellSlot(1).state == 0 then
		local slot_68_0 = ove_0_88()

		if ove_0_25.IsValidTarget(slot_68_0) then
			local slot_68_1 = 0
			local slot_68_2 = ove_0_22.core.get_pos_after_time(slot_68_0, 0.75 + ove_0_34.delay + slot_68_0.pos:dist(player.pos) / ove_0_34.speed)

			if slot_68_2 then
				for iter_68_0 = 0, slot_68_0.pos:dist(player.pos) + 50, 50 do
					local slot_68_3 = ove_0_25.VectorExtend(player.pos, slot_68_2:to3D(player.pos.y), iter_68_0)

					if navmesh.isWall(slot_68_3) or ove_0_25.isWallValid(slot_68_3) then
						slot_68_1 = iter_68_0

						break
					end
				end

				if slot_68_1 ~= 0 and slot_68_1 <= player.pos:dist(slot_68_0.pos) then
					ove_0_34.delay = 0.75 + ove_0_34.delay + slot_68_1 / ove_0_34.speed
					ove_0_34.speed = math.huge
					ove_0_34.width = 80
					ove_0_34.realDelay = 0.75 + ove_0_34.delay + slot_68_1 / ove_0_34.speed
					ove_0_34.realWidth = 80

					local slot_68_4 = ove_0_22.linear.get_prediction(ove_0_34, slot_68_0)

					if slot_68_4 and slot_68_4.startPos:dist(slot_68_4.endPos) <= slot_68_1 + 1300 and not ove_0_94(ove_0_34, {
						startPos = player.pos2D,
						endPos = ove_0_25.VectorExtend(player.pos, slot_68_4.endPos:to3D(), slot_68_1):to2D()
					}, slot_68_0) then
						player:castSpell("pos", 1, vec3(slot_68_4.endPos.x, slot_68_0.pos.y, slot_68_4.endPos.y))
					end
				end
			end
		end
	end

	if player:spellSlot(1).state == 0 then
		local slot_68_5 = ove_0_90()

		if ove_0_25.IsValidTarget(slot_68_5, false, true, true) then
			local slot_68_6 = ove_0_22.linear.get_prediction(ove_0_34, slot_68_5)

			if slot_68_6 and slot_68_6.startPos:dist(slot_68_6.endPos) <= ove_0_34.range then
				player:castSpell("pos", 1, vec3(slot_68_6.endPos.x, slot_68_5.pos.y, slot_68_6.endPos.y))
			end
		end
	end
end

local function ove_0_145(arg_69_0)
	local slot_69_0

	for iter_69_0, iter_69_1 in pairs(ove_0_25.GetJungleMinion(arg_69_0)) do
		if iter_69_1.health >= ove_0_25.CalculateAADamage(iter_69_1, player, 1) or iter_69_1.maxHealth > 4000 then
			slot_69_0 = slot_69_0 or iter_69_1

			if iter_69_1.pos:dist(player.pos) <= slot_69_0.pos:dist(player.pos) then
				slot_69_0 = iter_69_1
			end
		end
	end

	return slot_69_0
end

local function ove_0_146(arg_70_0)
	local slot_70_0

	for iter_70_0, iter_70_1 in pairs(ove_0_25.GetJungleMinion(arg_70_0)) do
		slot_70_0 = slot_70_0 or iter_70_1

		if iter_70_1.pos:dist(player.pos) <= slot_70_0.pos:dist(player.pos) then
			slot_70_0 = iter_70_1
		end
	end

	return slot_70_0
end

local function ove_0_147()
	if ove_0_38.farming.lasthit.useq:get() and player:spellSlot(0).state == 0 and not ove_0_24.core.is_winding_up_attack() and ove_0_48 < game.time then
		local slot_71_0 = ove_0_25.CountMinions(player.pos, ove_0_33.range, "enemy")

		for iter_71_0, iter_71_1 in pairs(slot_71_0) do
			if ove_0_24.farm.predict_hp(iter_71_1, iter_71_1.pos2D:dist(player.pos2D) / ove_0_134.speed + ove_0_134.delay) <= ove_0_79(iter_71_1) and iter_71_1 ~= ove_0_57 then
				local slot_71_1 = ove_0_22.linear.get_prediction(ove_0_33, iter_71_1)

				if slot_71_1.startPos:dist(slot_71_1.endPos) <= ove_0_33.range and not ove_0_94(ove_0_33, slot_71_1, iter_71_1) then
					player:castSpell("pos", 0, vec3(slot_71_1.endPos.x, iter_71_1.pos.y, slot_71_1.endPos.y))
				end
			end
		end
	end
end

local function ove_0_148()
	if ove_0_38.farming.laneclear.useq:get() and player:spellSlot(0).state == 0 and not ove_0_24.core.is_winding_up_attack() and ove_0_48 < game.time then
		local slot_72_0 = ove_0_25.CountMinions(player.pos, ove_0_33.range, "enemy")

		for iter_72_0, iter_72_1 in pairs(slot_72_0) do
			if ove_0_24.farm.predict_hp(iter_72_1, iter_72_1.pos2D:dist(player.pos2D) / ove_0_134.speed + ove_0_134.delay) <= ove_0_79(iter_72_1) and iter_72_1 ~= ove_0_57 then
				local slot_72_1 = ove_0_22.linear.get_prediction(ove_0_33, iter_72_1)

				if slot_72_1.startPos:dist(slot_72_1.endPos) <= ove_0_33.range and not ove_0_94(ove_0_33, slot_72_1, iter_72_1) then
					player:castSpell("pos", 0, vec3(slot_72_1.endPos.x, iter_72_1.pos.y, slot_72_1.endPos.y))
				end
			end
		end

		local slot_72_2 = 0
		local slot_72_3 = ove_0_25.CountMinions(player.pos, ove_0_33.range, "enemy")

		for iter_72_2, iter_72_3 in pairs(slot_72_3) do
			local slot_72_4 = ove_0_22.linear.get_prediction(ove_0_33, iter_72_3)

			if slot_72_4 and player.pos:dist(vec3(slot_72_4.endPos.x, iter_72_3.y, slot_72_4.endPos.y)) < ove_0_33.range then
				player:castSpell("pos", 0, vec3(slot_72_4.endPos.x, iter_72_3.pos.y, slot_72_4.endPos.y))
			end
		end

		ove_0_48 = game.time + 0.1
	end
end

local function ove_0_149()
	if ove_0_25.ShouldAttackJungle(ove_0_34.range) == false then
		return
	end

	if ove_0_38.farming.jungleclear.usee:get() and player:spellSlot(2).state == 0 and not ove_0_24.core.is_winding_up_attack() then
		local slot_73_0 = ove_0_145(ove_0_34.range)

		if slot_73_0 then
			ove_0_131(2, slot_73_0)
		end
	end

	if ove_0_38.farming.jungleclear.useq:get() and player:spellSlot(0).state == 0 and not ove_0_24.core.is_winding_up_attack() then
		local slot_73_1 = ove_0_146(ove_0_34.range)

		if slot_73_1 then
			local slot_73_2 = ove_0_22.linear.get_prediction(ove_0_33, slot_73_1)

			if slot_73_2 and slot_73_2.startPos:dist(slot_73_2.endPos) < ove_0_33.range then
				player:castSpell("pos", 0, vec3(slot_73_2.endPos.x, slot_73_1.pos.y, slot_73_2.endPos.y))
			end
		end
	end

	if ove_0_38.farming.jungleclear.usew:get() and player:spellSlot(1).state == 0 and not ove_0_24.core.is_winding_up_attack() then
		local slot_73_3 = ove_0_145(ove_0_34.range)

		if slot_73_3 then
			local slot_73_4 = ove_0_22.linear.get_prediction(ove_0_34, slot_73_3)

			if slot_73_4 and slot_73_4.startPos:dist(slot_73_4.endPos) < ove_0_34.range then
				player:castSpell("pos", 1, vec3(slot_73_4.endPos.x, slot_73_3.pos.y, slot_73_4.endPos.y))
			end
		end
	end
end

local function ove_0_150()
	local slot_74_0
	local slot_74_1 = objManager.nexus[TEAM_ENEMY]

	if ove_0_25.IsValidTarget(slot_74_1) and slot_74_1.pos:dist(player.pos) <= ove_0_33.range + slot_74_1.boundingRadius / 2 then
		slot_74_0 = slot_74_1
	end

	for iter_74_0 = 0, objManager.inhibs.size[TEAM_ENEMY] - 1 do
		local slot_74_2 = objManager.inhibs[TEAM_ENEMY][iter_74_0]

		if ove_0_25.IsValidTarget(slot_74_2) and slot_74_2.pos:dist(player.pos) <= ove_0_33.range + slot_74_2.boundingRadius then
			slot_74_0 = slot_74_2
		end
	end

	for iter_74_1 = 0, objManager.turrets.size[TEAM_ENEMY] - 1 do
		local slot_74_3 = objManager.turrets[TEAM_ENEMY][iter_74_1]

		if ove_0_25.IsValidTarget(slot_74_3) and slot_74_3.pos:dist(player.pos) <= ove_0_33.range + slot_74_3.boundingRadius / 2 then
			slot_74_0 = slot_74_3
		end
	end

	if ove_0_25.IsValidTarget(slot_74_0) and slot_74_0.pos:dist(player.pos) <= ove_0_33.range + slot_74_0.boundingRadius - 20 and not ove_0_24.core.is_winding_up_attack() and ove_0_38.farming.toggle:get() and ove_0_24.menu.lane_clear.key:get() and player:spellSlot(0).state == 0 and ove_0_38.farming.structureclear.useq:get() then
		player:castSpell("pos", 0, slot_74_0.pos)

		return true
	end
end

local function ove_0_151()
	if ove_0_25.getturretenemy(player.pos) then
		return
	end

	if player:spellSlot(0).state == 0 and not ove_0_24.core.is_spell_locked() and not ove_0_25.RecallActive(player) and not ove_0_24.core.is_winding_up_attack() then
		local slot_75_0 = ove_0_91()

		if ove_0_25.IsValidTarget(slot_75_0) then
			local slot_75_1 = ove_0_22.linear.get_prediction(ove_0_33, slot_75_0)

			if slot_75_1 and slot_75_1.startPos:dist(slot_75_1.endPos) < ove_0_33.range and not ove_0_25.IsCollidingWindwall(slot_75_1.startPos, slot_75_1.endPos, ove_0_33) and (not ove_0_94(ove_0_33, slot_75_1, slot_75_0) or player.buff.zeriespecialrounds) then
				player:castSpell("pos", 0, vec3(slot_75_1.endPos.x, slot_75_0.pos.y, slot_75_1.endPos.y))
			end
		end
	end
end

local function ove_0_152()
	local slot_76_0 = ove_0_25.CountMinions(player.pos, player.attackRange + player.boundingRadius + 30, "enemy")

	for iter_76_0, iter_76_1 in pairs(slot_76_0) do
		if ove_0_24.farm.predict_hp(iter_76_1, ove_0_24.utility.get_hit_time(player, iter_76_1) - os.clock()) <= ove_0_24.utility.get_damage(player, iter_76_1) * 1.2 then
			return true
		end
	end

	return false
end

local function ove_0_153()
	if player.isDead then
		return
	end

	if ove_0_38.eWall:get() then
		for iter_77_0 = 1, #ove_0_30 do
			if ove_0_30[iter_77_0].StartPos:dist(mousePos) < 200 and ove_0_30[iter_77_0].StartPos:to2D():dist(player.pos2D) > 5 then
				player:move(ove_0_30[iter_77_0].StartPos)
			end

			if ove_0_30[iter_77_0].StartPos:dist(mousePos) < 200 and ove_0_30[iter_77_0].StartPos:to2D():dist(player.pos2D) < 5 then
				player:castSpell("pos", 2, ove_0_30[iter_77_0].CastPos)
			end

			if ove_0_30[iter_77_0].CastPos:dist(mousePos) < 200 and ove_0_30[iter_77_0].CastPos:to2D():dist(player.pos2D) > 5 then
				player:move(ove_0_30[iter_77_0].CastPos)
			end

			if ove_0_30[iter_77_0].CastPos:dist(mousePos) < 200 and ove_0_30[iter_77_0].CastPos:to2D():dist(player.pos2D) < 5 then
				player:castSpell("pos", 2, ove_0_30[iter_77_0].StartPos)
			end
		end
	end

	if not ove_0_24.menu.combat.key:get() and not ove_0_24.menu.hybrid.key:get() and ove_0_38.harass.autoqharass:get() then
		ove_0_151()
	end

	if ove_0_38.combo.rusage:get() == 1 then
		ove_0_38.combo.hpr:set("visible", true)
	else
		ove_0_38.combo.hpr:set("visible", false)
	end

	ove_0_136()

	if ove_0_38.semiw:get() then
		ove_0_144()
	end

	if ove_0_24.menu.lane_clear.key:get() then
		if ove_0_24.farm.get_clear_target() and ove_0_38.farming.passiveWait:get() and ove_0_152() == false and not player.buff.zeriqpassiveready and (not ove_0_94(ove_0_33, {
			startPos = player.pos2D,
			endPos = ove_0_24.farm.get_clear_target().pos2D
		}, ove_0_24.farm.get_clear_target()) or not player.buff.zeriespecialrounds) then
			ove_0_24.core.set_pause_attack(0.1)
		end

		if ove_0_38.farming.toggle:get() then
			ove_0_150()
			ove_0_149()
			ove_0_148()
		end
	end

	if ove_0_24.menu.last_hit.key:get() and ove_0_38.farming.toggle:get() then
		ove_0_147()
	end

	if ove_0_38.draws.damage.draw_type:get() == 2 then
		ove_0_38.draws.damage.colorq:set("visible", true)
		ove_0_38.draws.damage.colorw:set("visible", true)
		ove_0_38.draws.damage.colorr:set("visible", true)
		ove_0_38.draws.damage.coloraa:set("visible", true)
	else
		ove_0_38.draws.damage.colorq:set("visible", false)
		ove_0_38.draws.damage.colorw:set("visible", false)
		ove_0_38.draws.damage.colorr:set("visible", false)
		ove_0_38.draws.damage.coloraa:set("visible", false)
	end

	if ove_0_24.menu.combat.key:get() then
		if ove_0_24.combat.target and ove_0_38.auto.passiveWait:get() and (ove_0_38.auto.passiveWaitHP:get() == false or ove_0_25.GetHPPercent(ove_0_24.combat.target) > 35) and not player.buff.zeriqpassiveready and (not ove_0_94(ove_0_33, {
			startPos = player.pos2D,
			endPos = ove_0_24.combat.target.pos2D
		}, ove_0_24.combat.target) or not player.buff.zeriespecialrounds) then
			ove_0_24.core.set_pause_attack(0.1)
		end

		ove_0_133()
	end

	if ove_0_24.menu.hybrid.key:get() then
		if ove_0_24.combat.target and ove_0_38.auto.passiveWait:get() and (ove_0_38.auto.passiveWaitHP:get() == false or ove_0_25.GetHPPercent(ove_0_24.combat.target) > 35) and not player.buff.zeriqpassiveready and not player.buff.zeriespecialrounds and (not ove_0_94(ove_0_33, {
			startPos = player.pos2D,
			endPos = ove_0_24.combat.target.pos2D
		}, ove_0_24.combat.target) or not player.buff.zeriespecialrounds) then
			ove_0_24.core.set_pause_attack(0.1)
		end

		ove_0_135()
	end

	ove_0_143()
	ove_0_142()

	ove_0_34.delay = 0.4 / (player.buff.zerir and 0.62 * player.attackSpeedMod or ove_0_51(1.5, 0.62 * player.attackSpeedMod))
	ove_0_34.speed = 2200
	ove_0_34.width = 40
	ove_0_34.realDelay = 0.4 / (player.buff.zerir and 0.62 * player.attackSpeedMod or ove_0_51(1.5, 0.62 * player.attackSpeedMod))
	ove_0_34.realWidth = 40

	if player.buff["assets/perks/styles/precision/lethaltempo/lethaltempo.lua"] and player.buff["assets/perks/styles/precision/lethaltempo/lethaltempo.lua"].stacks2 == 6 then
		ove_0_33.range = 875
	else
		ove_0_33.range = 800
	end
end

ove_0_33.range = ove_0_21.parse(ove_0_20.data).Q
ove_0_34.range = ove_0_21.parse(ove_0_20.data).W
ove_0_35.range = ove_0_21.parse(ove_0_20.data).E
ove_0_36.range = ove_0_21.parse(ove_0_20.data).R

local function ove_0_154(arg_78_0, arg_78_1, arg_78_2, arg_78_3)
	local slot_78_0 = ove_0_25.VectorExtend(arg_78_0, arg_78_1, 600)
	local slot_78_1 = (arg_78_0 - slot_78_0):norm()
	local slot_78_2 = (slot_78_0 - arg_78_0):norm()
	local slot_78_3 = 20 * slot_78_2:perp1()
	local slot_78_4 = 20 * slot_78_2:perp2()
	local slot_78_5 = slot_78_0 - ((arg_78_0 - slot_78_0):norm() * 50):perp1() + (arg_78_0 - slot_78_0):norm() * 40
	local slot_78_6 = slot_78_0 - ((arg_78_0 - slot_78_0):norm() * 50):perp2() + (arg_78_0 - slot_78_0):norm() * 40
	local slot_78_7 = arg_78_0 - ((slot_78_0 - arg_78_0):norm() * 360):perp2() + (slot_78_0 - arg_78_0):norm() * 40

	graphics.draw_line(slot_78_5, slot_78_0, arg_78_2 + 1, graphics.argb(155, 204, 255, 153))
	graphics.draw_line(slot_78_6, slot_78_0, arg_78_2 + 1, graphics.argb(155, 204, 255, 153))
	graphics.draw_line(slot_78_0, arg_78_0, 2, graphics.argb(255, 255, 255, 255))
end

local function ove_0_155()
	if player.isDead then
		return
	end

	for iter_79_0 = 1, #ove_0_30 do
		if ove_0_30[iter_79_0].StartPos:dist(game.cameraPos) <= 4000 then
			if ove_0_30[iter_79_0].StartPos:dist(mousePos) <= 200 then
				graphics.draw_circle(ove_0_30[iter_79_0].StartPos, 30, 5, graphics.argb(255, 204, 255, 153), 10)
				ove_0_154(ove_0_30[iter_79_0].StartPos, ove_0_30[iter_79_0].CastPos, 4, COLOR_WHITE)
			else
				graphics.draw_circle(ove_0_30[iter_79_0].StartPos, 30, 1, COLOR_WHITE, 10)
			end
		end

		if ove_0_30[iter_79_0].CastPos:dist(game.cameraPos) <= 4000 then
			if ove_0_30[iter_79_0].CastPos:dist(mousePos) <= 200 then
				graphics.draw_circle(ove_0_30[iter_79_0].CastPos, 30, 5, graphics.argb(255, 204, 255, 153), 10)
				ove_0_154(ove_0_30[iter_79_0].CastPos, ove_0_30[iter_79_0].StartPos, 4, COLOR_WHITE)
			else
				graphics.draw_circle(ove_0_30[iter_79_0].CastPos, 30, 1, COLOR_WHITE, 10)
			end
		end
	end

	if ove_0_38.draws.drawq:get() then
		graphics.draw_circle(player.pos, ove_0_33.range, ove_0_38.draws.thickness:get(), ove_0_38.draws.colorq:get(), 80)
	end

	if ove_0_38.draws.draww:get() then
		graphics.draw_circle(player.pos, ove_0_34.range, ove_0_38.draws.thickness:get(), ove_0_38.draws.colorw:get(), 80)
	end

	if ove_0_38.draws.drawr:get() then
		graphics.draw_circle(player.pos, ove_0_36.range, ove_0_38.draws.thickness:get(), ove_0_38.draws.colorr:get(), 80)
	end

	for iter_79_1 = 0, objManager.enemies_n - 1 do
		local slot_79_0 = objManager.enemies[iter_79_1]

		if ove_0_38.draws.damage.drawdamage:get() and ove_0_25.ValidForDraw(slot_79_0) then
			local slot_79_1 = graphics.height > 1080 and graphics.height / 1080 * 0.905 or 1
			local slot_79_2 = slot_79_0.barPos
			local slot_79_3 = slot_79_2.x + 164 * slot_79_1
			local slot_79_4 = slot_79_2.y + 123 * slot_79_1
			local slot_79_5 = (ove_0_38.draws.damage.draw_type:get() == 1 or ove_0_38.draws.damage.colorr.alpha > 60) and player:spellSlot(3).state == 0 and ove_0_83(slot_79_0) or 0
			local slot_79_6 = (ove_0_38.draws.damage.draw_type:get() == 1 or ove_0_38.draws.damage.colorq.alpha > 60) and player:spellSlot(0).state == 0 and ove_0_79(slot_79_0) or 0
			local slot_79_7 = (ove_0_38.draws.damage.draw_type:get() == 1 or ove_0_38.draws.damage.colorw.alpha > 60) and player:spellSlot(1).state == 0 and ove_0_81(slot_79_0) or 0
			local slot_79_8 = (ove_0_38.draws.damage.draw_type:get() == 1 or ove_0_38.draws.damage.coloraa.alpha > 60) and ove_0_25.CalculateAADamage(slot_79_0, player, ove_0_38.combo.includeaa:get()) or 0
			local slot_79_9 = slot_79_0.health - (slot_79_6 + slot_79_5 + slot_79_7 + slot_79_8)
			local slot_79_10 = slot_79_3 + slot_79_0.health / slot_79_0.maxHealth * (102 * slot_79_1)
			local slot_79_11 = slot_79_3 + (slot_79_9 > 0 and slot_79_9 or 0) / slot_79_0.maxHealth * (102 * slot_79_1)

			if ove_0_38.draws.damage.draw_type:get() == 1 then
				if slot_79_9 > 0 then
					ove_0_65(slot_79_10, slot_79_4, slot_79_11, slot_79_4, 10 * slot_79_1, ove_0_64(ove_0_38.draws.damage.trans:get(), 255, 192, 200))
				else
					ove_0_65(slot_79_10, slot_79_4, slot_79_11, slot_79_4, 10 * slot_79_1, ove_0_64(ove_0_38.draws.damage.trans:get(), 0, 255, 0))
				end
			else
				local slot_79_12 = slot_79_6 + slot_79_5 + slot_79_7 + slot_79_8
				local slot_79_13 = player:spellSlot(0).state == 0 and slot_79_6 / slot_79_12 or 0
				local slot_79_14 = player:spellSlot(1).state == 0 and slot_79_7 / slot_79_12 or 0
				local slot_79_15 = player:spellSlot(3).state == 0 and slot_79_5 / slot_79_12 or 0
				local slot_79_16 = slot_79_8 / slot_79_12 or 0
				local slot_79_17 = ove_0_49(0, slot_79_0.health - slot_79_12) / slot_79_0.maxHealth
				local slot_79_18 = slot_79_2.x + 10 + 102 * slot_79_1 * slot_79_17
				local slot_79_19 = slot_79_2.x + 10 + 102 * slot_79_1 * slot_79_0.health / slot_79_0.maxHealth - slot_79_18
				local slot_79_20 = slot_79_2.x + 164 * slot_79_1 + 102 * slot_79_17

				if player:spellSlot(0).state == 0 then
					ove_0_65(slot_79_10 - (slot_79_15 + slot_79_14 + slot_79_16) * slot_79_19, slot_79_4, slot_79_11, slot_79_4, 10 * slot_79_1, ove_0_64(ove_0_38.draws.damage.trans:get(), ove_0_38.draws.damage.colorq.red, ove_0_38.draws.damage.colorq.green, ove_0_38.draws.damage.colorq.blue))
				end

				if player:spellSlot(1).state == 0 then
					ove_0_65(slot_79_10 - (slot_79_15 + slot_79_16) * slot_79_19, slot_79_4, slot_79_11 + slot_79_13 * slot_79_19, slot_79_4, 10 * slot_79_1, ove_0_64(ove_0_38.draws.damage.trans:get(), ove_0_38.draws.damage.colorw.red, ove_0_38.draws.damage.colorw.green, ove_0_38.draws.damage.colorw.blue))
				end

				if player:spellSlot(3).state == 0 then
					ove_0_65(slot_79_10 - slot_79_16 * slot_79_19, slot_79_4, slot_79_11 + (slot_79_13 + slot_79_14) * slot_79_19, slot_79_4, 10 * slot_79_1, ove_0_64(ove_0_38.draws.damage.trans:get(), ove_0_38.draws.damage.colorr.red, ove_0_38.draws.damage.colorr.green, ove_0_38.draws.damage.colorr.blue))
				end

				ove_0_65(slot_79_10, slot_79_4, slot_79_11 + (slot_79_13 + slot_79_14 + slot_79_15) * slot_79_19, slot_79_4, 10 * slot_79_1, ove_0_64(ove_0_38.draws.damage.trans:get(), ove_0_38.draws.damage.coloraa.red, ove_0_38.draws.damage.coloraa.green, ove_0_38.draws.damage.coloraa.blue))
			end
		end
	end
end

local function ove_0_156(arg_80_0)
	return
end

local function ove_0_157(arg_81_0)
	if arg_81_0 and arg_81_0.isBasicAttack and arg_81_0.owner and arg_81_0.owner == player and arg_81_0.target and (arg_81_0.target.type == TYPE_TURRET or arg_81_0.target.type == TYPE_INHIB or arg_81_0.target.type == TYPE_NEXUS) then
		ove_0_47 = arg_81_0.target
	end

	if arg_81_0.owner == player and arg_81_0.isBasicAttack and arg_81_0.target.type == TYPE_MINION then
		ove_0_57 = arg_81_0.target
	end
end

local function ove_0_158(arg_82_0)
	if arg_82_0 and ove_0_47 and arg_82_0.ptr == ove_0_47.ptr then
		ove_0_47 = nil
	end
end

local function ove_0_159(arg_83_0)
	if ove_0_57 and arg_83_0 and arg_83_0.ptr == ove_0_57.ptr then
		ove_0_57 = nil
	end
end

cb.add(cb.delete_minion, ove_0_159)
cb.add(cb.delete_obj, ove_0_158)
cb.add(cb.create_missile, ove_0_156)
cb.add(cb.draw, ove_0_155)
ove_0_24.combat.register_f_pre_tick(ove_0_153)
cb.add(cb.spell, ove_0_157)
cb.add(cb.keydown, ove_0_31)

local AniviaPlugin = {}
--local orb = module.internal("orb")
local preds = module.internal("pred")
--local evade = module.seek("evade")
local ui = module.load("<PERSON>", "ui");
local DelayAction = module.load("<PERSON>", "Core/DelayAction")
local DelayTick = module.load("<PERSON>", "Core/DelayTick")
local Prediction = module.load("<PERSON>", "Core/Prediction")
local BuffManager = module.load("<PERSON>", "Library/BuffManager")
local CalculateManager = module.load("<PERSON>", "Library/CalculateManager")
--local FarmManager = module.load("<PERSON>", "Library/FarmManager")
local ItemManager = module.load("<PERSON>", "Library/ItemManager")
local NetManager = module.load("<PERSON>", "Library/NetManager")
local ObjectManager = module.load("Brian", "Library/ObjectManager")
local OrbManager = module.load("<PERSON>", "Library/OrbManager")
local SpellManager = module.load("<PERSON>", "Library/SpellManager")
local VectorManager = module.load("<PERSON>", "Library/VectorManager")
local MyCommon = module.load("<PERSON>", "Library/ExtraManager")
local common = module.load( "Brian","Utility/common")

local enemies = common.GetEnemyHeroes()

local ts = module.internal("TS")
local orb = module.internal("orb")
local gpred = module.internal("pred")

local QMissile, RMissile = nil, nil
local Qobj, Robj = false, false
local Rfull = false

local QlvlDmg = {60, 85, 110, 135, 160}
local ElvlDmg = {50, 75, 100, 125, 150}
local qPred = {
	delay = 0.4,
	width = 90,
	speed = 850,
	boundingRadiusMod = 1,
	collision = {hero = false, minion = false, wall = true}
}
local wPred = {
	delay = 0.6,
	width = 100,
	speed = math.huge,
	boundingRadiusMod = 1,
	collision = {hero = false, minion = false}
}
local rPred = {
	delay = 0.25,
	radius = 200,
	speed = 1600,
	boundingRadiusMod = 0,
	collision = {hero = false, minion = false}
}


local MyMenu
local icon = graphics.sprite('mao.png')
function AniviaPlugin.Load(GlobalMenu)
    MyMenu = GlobalMenu
    MyMenu:set('icon',  player.iconSquare)

    MyMenu.Key:keybind("manual", "Manual W Aim [Behind]", "T", false)
    MyMenu.Key:keybind("manual2", "Manual W Aim [Infront]", "U", false)

    MyMenu:menu("combo", "Combo Settings")
    MyMenu.combo:header("xd", "Combo Settings")
    MyMenu.combo:header("xd", "Q Settings")
    MyMenu.combo:boolean("q", "Use Q", true)
    MyMenu.combo:boolean("sp", "Use Slow Pred", true)

    MyMenu.combo:header("xd", "W Settings")
    MyMenu.combo:boolean("w", "W when Enemy Stuned", true)

    MyMenu.combo:header("xd", "E Settings")
    MyMenu.combo:boolean("e", "Use E", true)
    MyMenu.combo:dropdown("modee", "Choose Mode: ", 1, {"Chilled", "Always"})

    MyMenu.combo:header("xd", "R Settings")
    MyMenu.combo:boolean("r", "Use R", true)
    MyMenu.combo:boolean("sr", "Auto Disable R", true)

    MyMenu.combo:header("xd", "Misc")
    MyMenu.combo:boolean("disable", "Smart Disable Auto Attack", true)
    MyMenu.combo:slider("level", "Disable AA at X Level", 6, 1, 18, 1)

    MyMenu:menu("harass", "Harass Settings")
    MyMenu.harass:header("xd", "Harass Settings")
    MyMenu.harass:boolean("q", "Use Q", true)
    MyMenu.harass:boolean("e", "Use E", true)
    MyMenu.harass:dropdown("modee", "Choose Mode: ", 1, {"Chilled", "Always"})
    MyMenu.harass:slider("Mana", "Min. Mana Percent: ", 10, 0, 100, 10)

    --FarmManager.load(MyMenu)
    MyMenu:menu("LaneClear", "LaneClear Settings")
    MyMenu.LaneClear:header("SpellHeader", "Spell Core")
    MyMenu.LaneClear:boolean("E", "Use E", true)
    MyMenu.LaneClear:boolean("R", "Use R", true)
    MyMenu.LaneClear:slider("rslider", "If hit X minions", 3, 1, 10, 1)
    MyMenu.LaneClear:header("ManaHeader", "Mana Manager")
    MyMenu.LaneClear:slider("ManaMin", "Player ManaPercent >= x%", 60, 1, 100, 1)

    MyMenu:menu("JungleClear", "JungleClear Settings")
    MyMenu.JungleClear:header("SpellHeader", "Spell Core")
    MyMenu.JungleClear:boolean("E", "Use E", true)
    MyMenu.JungleClear:boolean("R", "Use R", true)
    MyMenu.JungleClear:slider("rslider", "If hit X minions", 3, 1, 5, 1)
    MyMenu.JungleClear:header("ManaHeader", "Mana Manager")
    MyMenu.JungleClear:slider("ManaMin", "Player ManaPercent >= x%", 60, 1, 100, 1)


    MyMenu:menu("gc", "Anti Gapcloser")
    MyMenu.gc:menu("blacklist", "Anti-Gapclose Blacklist")
    local enemy = common.GetEnemyHeroes()
    for i, allies in ipairs(enemy) do
        MyMenu.gc.blacklist:boolean(allies.charName, "Dont Use On: " .. allies.charName, false)
    end
    MyMenu.gc:boolean("GapA", "Use Q for Anti-Gapclose", true)
    MyMenu.gc:boolean("GapAS", "Use W for Anti-Gapclose", true)
    MyMenu.gc:slider("health", " ^-Only if my Health Percent < X", 50, 1, 100, 1)

    MyMenu:menu("auto", "Killsteal Settings")
    MyMenu.auto:header("xd", "KillSteal Settings")
    MyMenu.auto:boolean("uks", "Use Killsteal", true)
    MyMenu.auto:boolean("ksq", "Use Q in Killsteal", true)
    MyMenu.auto:boolean("kse", "Use E in Killsteal", true)

    MyMenu:menu("draws", "Draw Settings")
    MyMenu.draws:header("xd", "Drawing Options")
    MyMenu.draws:boolean("q", "Draw Q Range", true)
    MyMenu.draws:color("colorq", "^ color", 255, 233, 121, 121)
    MyMenu.draws:boolean("e", "Draw E Range", true)
    MyMenu.draws:color("colore", "^ color", 255, 233, 121, 121)
end


local function select_target(res, obj, dist)
	if dist > 1100 then
		return
	end
	res.obj = obj
	return true
end

local function get_target(func)
	return ts.get_result(func).obj
end


local trace_filter = function(input, segment, target)
	if gpred.trace.linear.hardlock(input, segment, target) then
		return true
	end
	if gpred.trace.linear.hardlockmove(input, segment, target) then
		return true
	end
	if segment.startPos:dist(segment.endPos) <= 1070 then
		return true
	end
	if gpred.trace.newpath(target, 0.033, 0.5) then
		return true
	end
end



local function oncreateobj(obj)
	if obj.type then
	 if obj.name:find("Q_AOE_Mis") then
	   QMissile = obj
	   QPtr = obj.ptr
	   Qobj = true
	 end
	end
	if obj.type then
		if obj.name:find("R_AOE_Green") then
		 RCircle = obj
		 RPtr = obj.ptr
		 Robj = true
	 end
	end
	if obj.type then
		if obj.name:find("R_Full") then
		  RFPtr = obj.ptr
		  Rfull = true
		end
	   end
end

local function ondeleteobj(obj)
	if QPtr == obj.ptr then
	  QMissile = nil
	  QPtr = nil
	  Qobj = false
	end
	if RPtr == obj.ptr then
		RCircle = nil
		RPtr = nil
		Robj = false
	end
	if RFPtr == obj.ptr then
		RFPtr = nil
		RFobj = false
	  end	
end



local function smartW2(target)
	if player.path.serverPos:distSqr(target.path.serverPos) < (1000 * 1000) then
		local seg = gpred.linear.get_prediction(wPred, target)
		if seg then
			local x = seg.endPos.x
			local y = seg.endPos.y

			if x < player.x then
				x = x + 90
			elseif x > player.x then
				x = x - 90
			end

			if y < player.z then
				y = y + 90
			elseif y > player.z then
				y = y - 90
			end

			player:castSpell("pos", 1, vec3(x, target.y, y))
		end
	end
end

local function WGapcloser()
	--[[if player:spellSlot(0).state == 0 and MyMenu.gc.GapA:get() then
		for i = 0, objManager.enemies_n - 1 do
			local dasher = objManager.enemies[i]
			if dasher.type == TYPE_HERO and dasher.team == TEAM_ENEMY then
				if
					dasher and common.IsValidTarget(dasher) and dasher.path.isActive and dasher.path.isDashing and
						player.pos:dist(dasher.path.point[1]) < 900
				 then
					if MyMenu.gc.blacklist[dasher.charName] and not MyMenu.gc.blacklist[dasher.charName]:get() then
						if player.pos2D:dist(dasher.path.point2D[1]) < player.pos2D:dist(dasher.path.point2D[0]) then
							if ((player.health / player.maxHealth) * 100 <= MyMenu.gc.health:get()) then
								local pos = preds.linear.get_prediction(qPred, dasher)
								if pos and pos.startPos:dist(pos.endPos) < 1000 then
									player:castSpell("pos", 0, vec3(pos.endPos.x, mousePos.y, pos.endPos.y))
								end
							end
						end
					end
				end
			end
		end
	end]]
	if player:spellSlot(1).state == 0 and MyMenu.gc.GapAS:get() then
		for i = 0, objManager.enemies_n - 1 do
			local dasher = objManager.enemies[i]
			if dasher.type == TYPE_HERO and dasher.team == TEAM_ENEMY then
				if
					dasher and common.IsValidTarget(dasher) and dasher.path.isActive and dasher.path.isDashing and
						player.pos:dist(dasher.path.point[1]) < 1000
				 then
					if MyMenu.gc.blacklist[dasher.charName] and not MyMenu.gc.blacklist[dasher.charName]:get() then
						if player.pos2D:dist(dasher.path.point2D[1]) < player.pos2D:dist(dasher.path.point2D[0]) then
							if ((player.health / player.maxHealth) * 100 <= MyMenu.gc.health:get()) then
								smartW2(dasher)
							end
						end
					end
				end
			end
		end
	end
end

local aaaaaaaaaa = 0
local function Q(spell)
	if spell.owner.charName == "Anivia" then
		if spell.name == "FlashFrost" then
			aaaaaaaaaa = game.time + 0.5
		end
	end
end

local function qDmg(target)
	local damage = QlvlDmg[player:spellSlot(0).level] + (common.GetTotalAP() * .4)
	return common.CalculateMagicDamage(target, damage)
end

local function eDmg(target)
	local damage = ElvlDmg[player:spellSlot(2).level] + (common.GetTotalAP() * .5)
	return common.CalculateMagicDamage(target, damage)
end

local function DetectQ()
	for i = 0, objManager.enemies_n - 1 do
		local enemy = objManager.enemies[i]
		if enemy and common.IsValidTarget(enemy) and Qobj and not enemy.buff["sionpassivezombie"] and QMissile ~= nil then
			--if aaaaaaaaaa < os.clock() and player:spellSlot(0).name == "FlashFrost" then
				if enemy.pos:dist(QMissile.pos) <= 200 then
					player:castSpell("self", 0)
				end
			--end
		end
	end
end

local function CastQ(target)
	if player:spellSlot(0).state == 0 and player.path.serverPos:dist(target.path.serverPos) < 1070 then
		local seg = gpred.linear.get_prediction(qPred, target)
		if seg and seg.startPos:dist(seg.endPos) < 1070 then
			if not gpred.collision.get_prediction(qPred, seg, target) then
				if MyMenu.combo.sp:get() and trace_filter(qPred, seg, target) then
					player:castSpell("pos", 0, vec3(seg.endPos.x, game.mousePos.y, seg.endPos.y))
				end
				if not MyMenu.combo.sp:get() then
					player:castSpell("pos", 0, vec3(seg.endPos.x, game.mousePos.y, seg.endPos.y))
				end
			end
		end
	end
end

local function CastR(target)
	if player:spellSlot(3).state == 0 and player.path.serverPos:distSqr(target.path.serverPos) < (750 * 750) then
		local res = gpred.circular.get_prediction(rPred, target)
		if res and res.startPos:dist(res.endPos) < 750 then
			player:castSpell("pos", 3, vec3(res.endPos.x, game.mousePos.y, res.endPos.y))
		end
	end
end

local function GetBuff(target, buffname)
	local bname = string.lower(buffname)
	if target.buff[bname] then
		return target.buff[bname]
	end
	return false
end

local function smartW(target)
	if player.path.serverPos:distSqr(target.path.serverPos) < (1000 * 1000) then
		local seg = gpred.linear.get_prediction(wPred, target)
		if seg then
			local x = seg.endPos.x
			local y = seg.endPos.y

			if x < player.x then
				x = x - 103
			elseif x > player.x then
				x = x + 103
			end

			if y < player.z then
				y = y - 103
			elseif y > player.z then
				y = y + 103
			end

			player:castSpell("pos", 1, vec3(x, target.y, y))
		end
	end
end


local function CancelR()
	if MyMenu.combo.sr:get() and orb.combat.is_active() then
		if RCircle then
			local rcount = 0
			for i = 0, objManager.enemies_n - 1 do
				local enemy = objManager.enemies[i]
				if enemy and enemy.pos:dist(RCircle.pos) < 400 and common.IsValidTarget(enemy) then
					rcount = rcount + 1
				end
			end
			if rcount == 0 then
				player:castSpell("self", 3)
			end
		end
	end
end

local function Combo()
	local target = get_target(select_target)
	if target and common.IsValidTarget(target) and not target.buff["sionpassivezombie"] then
		if MyMenu.combo.r:get() and target.pos:dist(player.pos) <= 750 and not Robj then
			if RCircle ~= nil then
				return
			end
			CastR(target)
		end
		if MyMenu.combo.e:get() and player:spellSlot(2).state == 0 and player.path.serverPos:dist(target.path.serverPos) <= 650 then
			if MyMenu.combo.modee:get() == 1 and target.buff["aniviaiced"] then
				player:castSpell("obj", 2, target)
			elseif MyMenu.combo.modee:get() == 2 then
				player:castSpell("obj", 2, target)
			end
		end
		if MyMenu.combo.q:get() and not Qobj and QMissile == nil and target.pos:dist(player.pos) <= 1070 and player:spellSlot(0).state == 0 then
			if aaaaaaaaaa < game.time and player:spellSlot(0).name == "FlashFrost" then
				if QMissile == nil then
					CastQ(target)
				end
			end
		end
		if
			MyMenu.combo.w:get() and player:spellSlot(1).state == 0 and target.pos:dist(player.pos) < 1000 and target.buff[5] and
				Robj
		 then
			smartW(target)
		end
	end
end

local function Harass()
	if player.par / player.maxPar * 100 >= MyMenu.harass.Mana:get() then
		local target = get_target(select_target)
		if target and common.IsValidTarget(target) and not target.buff["sionpassivezombie"] then
			if
				MyMenu.harass.e:get() and player:spellSlot(2).state == 0 and player.path.serverPos:dist(target.path.serverPos) <= 650
			 then
				if MyMenu.harass.modee:get() == 1 and target.buff["aniviaiced"] then
					player:castSpell("obj", 2, target)
				elseif MyMenu.harass.modee:get() == 2 then
					player:castSpell("obj", 2, target)
				end
			end
			if MyMenu.harass.q:get() and target.pos:dist(player.pos) <= 1070 and player:spellSlot(0).state == 0 and not Qobj then
				--if aaaaaaaaaa < os.clock() and player:spellSlot(0).name == "FlashFrost" then
				if QMissile ~= nil then
					return
				end
				CastQ(target)
			--end
			end
		end
	end
end

local function KillSteal()
	for i = 0, objManager.enemies_n - 1 do
		local enemy = objManager.enemies[i]
		if enemy and common.IsValidTarget(enemy) and MyMenu.auto.uks:get() and not enemy.buff["sionpassivezombie"] then
			if
				MyMenu.auto.ksq:get() and player:spellSlot(0).state == 0 and enemy.health < qDmg(enemy) and not Qobj and
					player.path.serverPos:dist(enemy.path.serverPos) <= 1070
			 then
				if aaaaaaaaaa < game.time and player:spellSlot(0).name == "FlashFrost" then
					if QMissile == nil then
						CastQ(enemy)
					end
				end
			end
			if 
				MyMenu.auto.kse:get() and player:spellSlot(2).state == 0 and enemy.buff["aniviaiced"] and enemy.health < 2 * eDmg(enemy) and
					player.path.serverPos:dist(enemy.path.serverPos) <= 650
			 then
				player:castSpell("obj", 2, enemy)
			elseif
				MyMenu.auto.kse:get() and player:spellSlot(2).state == 0 and enemy.health < eDmg(enemy) and
					player.path.serverPos:dist(enemy.path.serverPos) <= 650
			 then
				player:castSpell("obj", 2, enemy)
			end
		end
	end
end

local function AimW()
	if MyMenu.keys.manual:get() then
		local target = get_target(select_target)
		player:move((game.mousePos))
		if target and target.pos:dist(player.pos) < 1000 then
			smartW(target)
		--PullWithR(target)
		end
	end
end

local function AimW2()
	if MyMenu.keys.manual2:get() then
		local target = get_target(select_target)
		player:move((game.mousePos))
		if target and target.pos:dist(player.pos) < 1000 then
			smartW2(target)
		--PullWithR(target)
		end
	end
end

local function OnTick()
	if QMissile ~= nil and Qobj then
		DetectQ()
	end
	if orb.combat.is_active() then
		Combo()
	end
	WGapcloser()
	--if orb.MyMenu.lane_clear:get() then Clear() end
	if MyMenu.auto.uks:get() then
		KillSteal()
	end
	if MyMenu.Key.manual:get() then
		AimW()
	end
	if MyMenu.Key.manual2:get() then
		AimW2()
	end
	if MyMenu.combo.sr:get() then
		CancelR()
	end
	if MyMenu.Key.Harass:get() then
		Harass()
	end
	if (orb.combat.is_active()) then
		if (MyMenu.combo.disable:get() and MyMenu.combo.level:get() <= player.levelRef) and player:spellSlot(2).state == 0 then
			orb.core.set_pause_attack(math.huge)
		end
	end
	if orb.combat.is_active() and player:spellSlot(2).state ~= 0 then
		orb.core.set_pause_attack(0)
	end
	if not orb.combat.is_active() and not orb.menu.last_hit.key:get() and not orb.menu.lane_clear.key:get() then
		if orb.core.is_attack_paused() then
			orb.core.set_pause_attack(0)
		end
		if orb.combat.is_active() and player:spellSlot(2).state ~= 0 then
			orb.core.set_pause_attack(0)
		end
	end
end

local function OnDraw()
	if MyMenu.draws.q:get() and player:spellSlot(0).state == 0 and player.isOnScreen then
		graphics.draw_circle(player.pos, 1075, 2, MyMenu.draws.colorq:get(), 50)
    end
	if MyMenu.draws.e:get() and player:spellSlot(2).state == 0 and player.isOnScreen then
		graphics.draw_circle(player.pos, 650, 2, MyMenu.draws.colorq:get(), 50)
	end
	if QMissile then
		graphics.draw_circle(QMissile.pos, 200, 2, graphics.argb(255, 248, 131, 121), 50)
	end
end

orb.combat.register_f_pre_tick(OnTick)
cb.add(cb.draw, OnDraw)
cb.add(cb.create_particle, oncreateobj)
cb.add(cb.create_missile, oncreateobj)
cb.add(cb.delete_particle, ondeleteobj)
cb.add(cb.delete_missile, ondeleteobj)
cb.add(cb.spell, Q)


return AniviaPlugin
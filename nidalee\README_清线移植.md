# Nidalee 清线功能移植说明

## 移植内容

已成功将 `Champions\Nidalee.lua` 中的清线功能移植到 `nidalee/` 目录中。

## 新增文件

### 1. `nidalee/clear_simple.lua` (简化版)
- **Farm()** - 清线功能
- **JungleFarm()** - 打野功能
- **SmartFarm()** - 智能清线功能（新增）
- **GetClosestJungleMob()** - 获取最近野怪
- **辅助函数** - isHuman(), isHunted(), IsValidTarget(), GetPercentPar(), GetWHuman()

## 修改文件

### 1. `nidalee/main.lua`
- 添加了清线模块导入：`local clear = module.load(header.id, 'nidalee/clear_simple')`
- 在菜单中添加了清线设置（中英文版本）
- 在 OnTick() 函数中集成了清线功能

## 功能特性

### 清线设置菜单
**中文版本：**
- 清线设置
  - 人形态设置
    - 使用Q清线
    - 使用W清线
  - 豹形态设置
    - 使用Q清线
    - 使用W清线
    - 使用E清线
  - 自动切换形态
  - 最低法力值百分比

**英文版本：**
- Farm Settings
  - Human Settings
    - Use Q
    - Use W
  - Cougar Settings
    - Use Q
    - Use W
    - Use E
  - Auto Swap Forms
  - Min. Mana Percent

### 智能清线功能
1. **优先级系统** - 按小兵血量排序，优先击杀低血量小兵
2. **预判系统** - Q技能使用预判提高命中率
3. **形态切换** - 智能自动切换人形态和豹形态
4. **法力值管理** - 根据设置的法力值阈值决定是否使用技能

### 技能优先级
**豹形态：** Q（撕咬） > E（横扫） > W（扑击）
**人形态：** Q（标枪投掷）优先，带预判功能

## 使用方法

1. 按下清线键（默认V键）激活清线模式
2. 脚本会自动：
   - 选择最佳目标（低血量小兵优先）
   - 使用合适的技能清线
   - 自动切换形态以提高效率
   - 管理法力值使用

## 技术改进

1. **预判系统** - Q技能使用线性预判，提高远程命中率
2. **碰撞检测** - 避免技能被小兵阻挡
3. **智能排序** - 按血量优先级选择目标
4. **形态优化** - 根据技能冷却和法力值智能切换形态

## 兼容性

- 完全兼容原有的 `nidalee/main.lua` 结构
- 保持了原有的菜单系统和按键绑定
- 不影响其他功能（连招、骚扰、治疗等）

## 扑击问题修复

### 🐛 **原问题**
- 豹子形态W技能往空地扑
- 没有距离限制和安全检查
- 缺少预判导致扑空

### ✅ **修复方案**
1. **正确的距离判断**
   - 普通目标：375范围
   - 被标记目标：750范围

2. **预判系统**
   - 检测小兵移动方向
   - 0.25秒预判时间
   - 确保扑击位置准确

3. **安全检查**
   - 避开敌方英雄600范围
   - 避开敌方防御塔915范围
   - 可通过菜单开关控制

4. **目标验证**
   - 检查目标是否仍然有效
   - 确保目标在扑击范围内

## 注意事项

- 清线功能只在按下清线键时激活
- 法力值不足时会自动停止使用技能
- 支持中英文界面切换
- 与原有的打野功能并行运行，不会冲突
- 安全扑击功能可通过菜单控制开关

## 错误修复 (v2.0)

### 🐛 **修复的错误**
- ✅ 修复了 "attempt to compare number with nil" 错误
- ✅ 修复了 minion.path.speed 可能为 nil 的问题
- ✅ 修复了菜单调用可能失败的问题
- ✅ 修复了 "attempt to call global 'pcall'" 错误（pcall 不可用）
- ✅ 简化了复杂的预判逻辑，提高稳定性

### ✅ **改进措施**
1. **安全检查** - 所有菜单调用都使用安全检查函数
2. **简化逻辑** - 移除了复杂的预判，使用简单直接的释放
3. **错误处理** - 添加了完整的 nil 值检查
4. **性能优化** - 减少了不必要的计算和检查
5. **兼容性** - 遵循 HANBOT3 开发者文档的 API 限制

### 📁 **文件更新**
- 创建了 `nidalee/clear_simple.lua` 替代原有的复杂版本
- 更新了 `nidalee/main.lua` 中的模块引用
- 删除了有问题的 `nidalee/clear.lua` 文件

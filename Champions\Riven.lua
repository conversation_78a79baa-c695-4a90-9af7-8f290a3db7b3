local rivenPlugin = {}

-- Load Spell

local spellQ = {
    range = 260,
    slot = "Q",
    count = 1,
    lastCastTime = 0,
    cast = false,
    target = nil,
    name = "RivenTriCleave",
    animation = {
        ["Riven_Base_Q_01_detonate"] = true,
        ["Riven_Base_Q_02_detonate"] = true,
        ["Riven_Base_Q_03_detonate"] = true,
        ["Riven_Base_Q_01_detonate_ult"] = true,
        ["Riven_Base_Q_02_detonate_ult"] = true,
        ["Riven_Base_Q_03_detonate_ult"] = true,
        ["Riven_Skin03_Q_01_detonate"] = true,
        ["Riven_Skin03_Q_02_detonate"] = true,
        ["Riven_Skin03_Q_03_detonate"] = true,
        ["Riven_Skin03_Q_01_detonate_ult"] = true,
        ["Riven_Skin03_Q_02_detonate_ult"] = true,
        ["Riven_Skin03_Q_03_detonate_ult"] = true,
        ["Riven_Skin04_Q_01_detonate"] = true,
        ["Riven_Skin04_Q_02_detonate"] = true,
        ["Riven_Skin04_Q_03_detonate"] = true,
        ["Riven_Skin04_Q_01_detonate_ult"] = true,
        ["Riven_Skin04_Q_02_detonate_ult"] = true,
        ["Riven_Skin04_Q_03_detonate_ult"] = true,
        ["Riven_Skin05_Q_01_detonate"] = true,
        ["Riven_Skin05_Q_02_detonate"] = true,
        ["Riven_Skin05_Q_03_detonate"] = true,
        ["Riven_Skin05_Q_01_detonate_ult"] = true,
        ["Riven_Skin05_Q_02_detonate_ult"] = true,
        ["Riven_Skin05_Q_03_detonate_ult"] = true,
        ["Riven_Skin06_Q_01_detonate"] = true,
        ["Riven_Skin06_Q_02_detonate"] = true,
        ["Riven_Skin06_Q_03_detonate"] = true,
        ["Riven_Skin06_Q_01_detonate_ult"] = true,
        ["Riven_Skin06_Q_02_detonate_ult"] = true,
        ["Riven_Skin06_Q_03_detonate_ult"] = true,
        ["Riven_Skin16_Q_01_detonate"] = true,
        ["Riven_Skin16_Q_02_detonate"] = true,
        ["Riven_Skin16_Q_03_detonate"] = true,
        ["Riven_Skin16_Q_01_detonate_ult"] = true,
        ["Riven_Skin16_Q_02_detonate_ult"] = true,
        ["Riven_Skin16_Q_03_detonate_ult"] = true,
        ["Riven_Skin18_Q_01_detonate"] = true,
        ["Riven_Skin18_Q_02_detonate"] = true,
        ["Riven_Skin18_Q_03_detonate"] = true,
        ["Riven_Skin18_Q_01_detonate_ult"] = true,
        ["Riven_Skin18_Q_02_detonate_ult"] = true,
        ["Riven_Skin18_Q_03_detonate_ult"] = true,
    }
}

local spellW = {
    range = 235,
    slot = "W",
    name = "RivenMartyr",
}

local spellE = {
    range = 325,
    slot = "E",
    name = "RivenFeint",
}

local spellR = {
    range = 600,
    slot = "R",
    name = "RivenFengShuiEngine",
}

local spellR2 = {
    range = 900,
    delay = 0.25,
    width = 60,
    speed = 1600,
    boundingRadiusMod = 0,
    collision = {hero = false, minion = false, wall = false},
    slot = "R",
    name = "RivenIzunaBlade",
}

local spellTiamat = {
    cast = false,
    name = "ItemTiamatCleave",
}

local spellIgnite = {
    slot = 666,
    range = 600,
}

local spellFlash = {
    slot = 666,
    range = 425,
}

local targetSpells = {
    ["Alistar"] = {
        championName = "Alistar",
        target = { spellSlot = 1, itemName = "AlistarW", menuName = "Headbutt [W]" }
    },
    ["Blitzcrank"] = {
        championName = "Blitzcrank",
        target = { spellName = {"powerfistattack"}, spellSlot = 2, itemName = "BlitzcrankE", menuName = "Power Fist [E]" }
    },
    ["Brand"] = {
        championName = "Brand",
        target = { spellSlot = 2, itemName = "BrandE", menuName = "Conflagration [E]" },
    },
    ["Chogath"] = {
        championName = "Chogath",
        target = { spellSlot = 3, itemName = "ChogathR", menuName = "Feast [R]" }
    },

    ["Darius"] = {
        championName = "Darius",
        target = { spellSlot = 3, itemName = "DariusR", menuName = "Noxian Guillotine [R]" }
    },
    ["Ekko"] = {
        championName = "Ekko",
        target = { spellName = {"ekkoeattack"}, spellSlot = 2, itemName = "EkkoE", menuName = "Phase Dive [E]" }
    },
    ["FiddleSticks"] = {
        championName = "FiddleSticks",
        target = { spellName = {"terrify"}, spellSlot = 0, itemName = "FiddleSticksQ", menuName = "Terrify [Q]" },
    },
    ["Fizz"] = {
        championName = "Fizz",
        target = { spellSlot = 0, itemName = "FizzQ", menuName = "Urchin Strike [Q]" }
    },
    ["Garen"] = {
        championName = "Garen",
        target = { spellName = {"garenqattack"}, spellSlot = 0, itemName = "GarenQ", menuName = "Decisive Strike [Q]" },
        targetA = { spellSlot = 3, itemName = "GarenR", menuName = "Demacian Justice [R]" },
    },
    ["Hecarim"] = {
        championName = "Hecarim",
        target = { spellName = {"hecarimrampattack"}, spellSlot = 2, itemName = "HecarimE", menuName = "Devastating Charge [E]" },
    },
    ["JarvanIV"] = {
        championName = "JarvanIV",
        target = { spellSlot = 3, itemName = "JarvanIVR", menuName = "Cataclysm [R]" }
    },
    ["Jayce"] = {
        championName = "Jayce",
        target = { spellSlot = 2, itemName = "JayceE", menuName = "Thundering Blow [E]" }
    },
    ["Kalista"] = {
        championName = "Kalista",
        target = { spellSlot = 2, itemName = "KalistaE", menuName = "Rend [E]" }
    },
    ["Kennen"] = {
        championName = "Kennen",
        target = { spellSlot = 1, itemName = "KennenW", menuName = "Electrical Surge [W]" }
    },
    ["LeeSin"] = {
        championName = "LeeSin",
        target = { spellSlot = 3, itemName = "LeeSinR", menuName = "Dragon's Rage [R]" }
    },
    ["Leona"] = {
        championName = "Leona",
        target = { spellName = {"leonashieldofdaybreakattack"}, spellSlot = 0, itemName = "LeonaQ", menuName = "Shield of Daybreak [Q]" }
    },
    ["Malzahar"] = {
        championName = "Malzahar",
        target = { spellSlot = 2, itemName = "MalzaharE", menuName = "Malefic Visions [E]" },
    },
    ["Maokai"] = {
        championName = "Maokai",
        target = { spellSlot = 1, itemName = "MaokaiW", menuName = "Twisted Advance [W]" }
    },
    ["MonkeyKing"] = {
        championName = "MonkeyKing",
        target = { spellName = {"monkeykingqattack"}, spellSlot = 0, itemName = "MonkeyKingQ", menuName = "Crushing Blow [Q]" }
    },
    ["Mordekaiser"] = {
        championName = "Mordekaiser",
        target = { spellName = {"mordekaiserqattack", "mordekaiserqattack1", "mordekaiserqattack2"}, spellSlot = 0, itemName = "MordekaiserQ", menuName = "Mace of Spades [Q]" },
        targetA = { spellSlot = 3, itemName = "MordekaiserR", menuName = "Children of the Grave [R]" }
    },
    ["Nasus"] = {
        championName = "Nasus",
        target = { spellName = {"nasusqattack"}, spellSlot = 0, itemName = "NasusQ", menuName = "Siphoning Strike [Q]" },
        targetA = { spellSlot = 1, itemName = "NasusW", menuName = "Wither [W]" }
    },
    ["Nidalee"] = {
        championName = "Nidalee",
        target = { spellName = {"nidaleetakedownattack", "nidalee_cougartakedownattack"}, spellSlot = 0, itemName = "NidaleeQ", menuName = "Takedown [Q]" }
    },
    ["Olaf"] = {
        championName = "Olaf",
        target = { spellSlot = 2, itemName = "OlafE", menuName = "Reckless Swing [E]" }
    },
    ["Pantheon"] = {
        championName = "Pantheon",
        target = { spellSlot = 1, itemName = "PantheonW", menuName = "Aegis of Zeonia [W]" }
    },
    ["Poppy"] = {       
        championName = "Poppy",
        target = { spellSlot = 2, itemName = "PoppyE", menuName = "Heroic Charge [E]" }
    },
    ["Quinn"] = {
        championName = "Quinn",
        target = { spellSlot = 2, itemName = "QuinnE", menuName = "Vault [E]" }
    },
    ["Rammus"] = {
        championName = "Rammus",
        target = { spellSlot = 2, itemName = "RammusE", menuName = "Frenzying Taunt [E]" }
    },
    ["RekSai"] = {
        championName = "RekSai",
        target = { spellSlot = 2, itemName = "RekSaiE", menuName = "Burrow [E]" }
    },
    ["Renekton"] = {
        championName = "Renekton",
        target = { spellName = {"renektonexecute", "renektonsuperexecute"}, spellSlot = 1, itemName = "RenektonW", menuName = "Ruthless Predator [W]" }
    },
    ["Ryze"] = {
        championName = "Ryze",
        target = { spellSlot = 1, itemName = "RyzeW", menuName = "Rune Prison [W]" },
    },
    ["Singed"] = {
        championName = "Singed",
        target = { spellSlot= 2, itemName = "SingedE", menuName = "Fling [E]" }
    },  
    ["Trundle"] = {
        championName = "Trundle",
        target = { spellSlot = 3, itemName = "TrundleR", menuName = "Subjugate [R]" }
    },
    ["Tristana"] = {
        championName = "Tristana",
        target = { spellSlot = 3, itemName = "TristanaR", menuName = "Buster Shot [R]" },
    },
    ["Twitch"] = {
        championName = "Twitch",
        target = { spellSlot = 2, itemName = "TwitchE", menuName = "Contaminate [E]" }
    },
    ["Udyr"] = {
        championName = "Udyr",
        target = { spellName = {"udyrbearattack", "udyrbearattackult"}, spellSlot = 2, itemName = "UdyrE", menuName = "Bear Stance [E]" }
    },
    ["Veigar"] = {
        championName = "Veigar",
        target = { spellSlot = 3, itemName = "VeigarR", menuName = "Primordial Burst [R]" }
    },
    ["Vladimir"] = {
        championName = "Vladimir",
        target = { spellSlot = 0, itemName = "VladimirQ", menuName = "Transfusion [Q]" },
    },
    ["Volibear"] = {
        championName = "Volibear",
        target = { spellName = {"volibearqattack"}, spellSlot = 0, itemName = "VolibearQ", menuName = "Rolling Thunder [Q]" },
        targetA = { spellSlot = 1, itemName = "VolibearW", menuName = "Frenzy [W]" },
    },
    ["XinZhao"] = {
        championName = "XinZhao",
        target = { spellName = {"xinzhaothrust3"}, spellSlot = 0, itemName = "XinZhaoQ", menuName = "Three Talon Strike [Q]" }
    },
    ["Yorick"] = {
        championName = "Yorick",
        target = { spellName = {"yorickqattack"}, spellSlot = 0, itemName = "YorickQ", menuName = "Last Rites [Q]" }
    },
    
    ["Zilean"] = {
        championName = "Zilean",
        target = { spellSlot = 2, itemName = "ZileanE", menuName = "Time Warp [E]" }
    },
}

local SelectTarget = nil
local SwitchTime = 0
local haveSpellCanBlock = false

local BlockSpells = {}

local on_end_func = nil
local on_end_time = 0

-- Load Module
local ui = module.load("Brian", "ui");
local DelayAction = module.load("Brian", "Core/DelayAction")
local DelayTick = module.load("Brian", "Core/DelayTick")
local Prediction = module.load("Brian", "Core/Prediction")
local BuffManager = module.load("Brian", "Library/BuffManager")
local CalculateManager = module.load("Brian", "Library/CalculateManager")
local ItemManager = module.load("Brian", "Library/ItemManager")
local FarmManager = module.load("Brian", "Library/FarmManager")
local NetManager = module.load("Brian", "Library/NetManager")
local ObjectManager = module.load("Brian", "Library/ObjectManager")
local OrbManager = module.load("Brian", "Library/OrbManager")
local SpellManager = module.load("Brian", "Library/SpellManager")
local VectorManager = module.load("Brian", "Library/VectorManager")
local MyCommon = module.load("Brian", "Library/ExtraManager")

local isRiot = module.load("Brian", "header").riot

local MyMenu

local function SlotName(slot)
    if slot == 0 then
       return "Q"
    elseif slot == 1 then
       return "W"
    elseif slot == 2 then
       return "E"
    elseif slot == 3 then
       return "R"
    end
	return "0"
end

function rivenPlugin.Load(GlobalMenu)
    MyMenu = GlobalMenu

    MyMenu.Key:keybind("Flee", "Flee Key", "Z", false)

    MyMenu:menu("Combo", "Combo Settings")
    MyMenu.Combo:header("SpellHeader", "Spell Core")
    MyMenu.Combo:boolean("Q", "Use Q Dash to Target", true)
    MyMenu.Combo:boolean("E", "Use E Dash to Target", true)
    MyMenu.Combo:boolean("EQ", "Use E + Q Dash to Target", false)
    MyMenu.Combo:boolean("R", "Use R")
    MyMenu.Combo:dropdown("R2", "Use R2", 2, {"Only KillAble", "Logic", "Always Cast", "Off"})
    MyMenu.Combo:keybind("R2Key", "Switch R2 Mode Key", "G", false)
    MyMenu.Combo:header("BurstHeader", "Burst Combo")
    MyMenu.Combo:boolean("S", "Start Burst Combo", false)
    MyMenu.Combo:boolean("F", "Use Flash", true)
    MyMenu.Combo:header("Tips1", "It need you manual to ")
    MyMenu.Combo:header("Tips2", "left click select target")
    MyMenu.Combo:header("Tips3", "and then press combo key")

    MyMenu:menu("Harass", "Harass Settings")
    MyMenu.Harass:header("SpellHeader", "Spell Core")
    MyMenu.Harass:boolean("W", "Use W", true)
    MyMenu.Harass:boolean("E", "Use E", true)
    MyMenu.Harass:dropdown("Mode", "Mode: ", 1, {"Fast Combo", "One Shot"})
    MyMenu.Harass:header("ProHeader", "PRO Mode")
    MyMenu.Harass:boolean("ProTurret", "Allow Under Turret Harass", false)

    MyMenu:menu("LaneClear", "LaneClear Settings")
    MyMenu.LaneClear:header("SpellHeader", "Spell Core")
    MyMenu.LaneClear:boolean("Q", "Use Q", true)
    MyMenu.LaneClear:boolean("QT", "Use Q Reset Attack(Turret)", true)
    MyMenu.LaneClear:boolean("W", "Use W", true)
    MyMenu.LaneClear:boolean("WK", "^ Minion Can KillAble", true)
    MyMenu.LaneClear:slider("WM", "^ Min Hit Count >= x", 3, 1, 6, 1)

    MyMenu:menu("JungleClear", "JungleClear Settings")
    MyMenu.JungleClear:header("SpellHeader", "Spell Core")
    MyMenu.JungleClear:boolean("W", "Use W", true)
    MyMenu.JungleClear:boolean("E", "Use E", true)

    FarmManager.Load(MyMenu)

    MyMenu:menu("Flee", "Flee Settings")
    MyMenu.Flee:header("SpellHeader", "Spell Core")
    MyMenu.Flee:boolean("Q", "Use Q", true)
    MyMenu.Flee:boolean("W", "Use W", true)
    MyMenu.Flee:boolean("E", "Use E", true)

    MyMenu:menu("KillSteal", "KillSteal Settings")
    MyMenu.KillSteal:header("SpellHeader", "Spell Core")
    MyMenu.KillSteal:boolean("R", "Use R", true)

    MyMenu:menu("Misc", "Misc Settings")
    MyMenu.Misc:header("SpellHeader", "Spell Core")
    --MyMenu.Misc:boolean("Q", "Keep Q Active", true)
    MyMenu.Misc:boolean("E", "Use E Block Spell", true)
    local targets = ObjectManager.GetEnemyHeroes()
    for i, target in pairs(targets) do
        if target and target ~= nil and target.charName then
            for i, spell in pairs(targetSpells) do
                if i and spell and i == target.charName then
                    haveSpellCanBlock = true
                    if not MyMenu.Misc[target.charName] then
                        MyMenu.Misc:menu(target.charName, target.charName)
                    end
                    table.insert(BlockSpells, spell)
                end
            end
        end
    end
    if haveSpellCanBlock then
        for i, spell in pairs(BlockSpells) do
            if i and spell and spell.championName then
                local champMenu = MyMenu.Misc[spell.championName]
                if spell.target and player.charName ~= "Yasuo" then
                    local data = spell.target
                    champMenu:header(SlotName(data.spellSlot).."Header", data.menuName)
                    champMenu:boolean(data.itemName, "Enabled", true)
                end
                if spell.targetA then
                    local data = spell.targetA
                    champMenu:header(SlotName(data.spellSlot).."Header", data.menuName)
                    champMenu:boolean(data.itemName, "Enabled", true)
                end
            end
        end
    end

    MyMenu:menu("Draw", "Draw Settings")
    MyMenu.Draw:header("RangeHeader", "Spell Range")
    MyMenu.Draw:boolean("R", "Draw R Range", true)
    MyMenu.Draw:color("colorr", "^ Color", 255, 233, 121, 121)
    MyMenu.Draw:boolean("B", "Draw Burst Range", true)
    MyMenu.Draw:color("colorb", "^ Color", 255, 233, 121, 121)
    MyMenu.Draw:boolean("Ready", "Only Spell Ready", false)
    MyMenu.Draw:header("StatusHeader", "Spell Status")
   -- MyMenu.Draw:boolean("CR", "Draw Combo R Status", true)
    MyMenu.Draw:header("DamageHeader", "Damage Indicator")
    MyMenu.Draw:boolean("DIEnabled", "Enabled", true)

    MyMenu.Combo.R2Key:set("callback", function()
        if player.isDead or player.isRecalling or chat.isOpened then
            return
        end
        if game.time - SwitchTime > 0.2 then
            if MyMenu.Combo.R2:get() == 1 then
                MyMenu.Combo.R2:set("value", 2)
                SwitchTime = game.time
                return
            end
            if MyMenu.Combo.R2:get() == 2 then
                MyMenu.Combo.R2:set("value", 3)
                SwitchTime = game.time
                return
            end
            if MyMenu.Combo.R2:get() == 3 then
                MyMenu.Combo.R2:set("value", 4)
                SwitchTime = game.time
                return
            end
            if MyMenu.Combo.R2:get() == 4 then
                MyMenu.Combo.R2:set("value", 1)
                SwitchTime = game.time
                return
            end
        end
    end)

end

local function GetAttackDamage(target)
    if not target or target == nil then
        return 0
    end
    local level = player.levelRef
    if level == 0 then
        return 0
    end
    local perc = 0.25
    if level >= 6 then
        perc = 0.30
    elseif level >= 9 then
        perc = 0.35
    elseif level >= 12 then
        perc = 0.40
    elseif level >= 15 then
        perc = 0.45 
    elseif level >= 18 then
        perc = 0.50
    end
    local attackDamage = CalculateManager.GetAutoAttackDamage(player, target)
    return attackDamage * (1 + perc)
end

local function GetQDamage(target)
    if not target or target == nil then
        return 0
    end
    local level = player:spellSlot(0).level
    if level == 0 then
        return 0
    end
    local dmg = ({15, 35, 55, 75, 95})[level] + ({0.45, 0.50, 0.55, 0.60, 0.65})[level] * MyCommon.GetTotalAD()
    local stack = 4 - spellQ.count
    return stack * (CalculateManager.CalculatePhysicalDamage(target, dmg) + GetAttackDamage(target))
end

local function GetWDamage(target, calcAA)
    calcAA = calcAA or true
    if not target or target == nil then
        return 0
    end
    local level = player:spellSlot(1).level
    if level == 0 then
        return 0
    end
    local dmg = ({55, 85, 115, 145, 175})[level] + MyCommon.GetTotalAD()
    if calcAA then
        return CalculateManager.CalculatePhysicalDamage(target, dmg) + GetAttackDamage(target)
    end
    return CalculateManager.CalculatePhysicalDamage(target, dmg)
end

local function GetEDamage(target)
    if not target or target == nil then
        return 0
    end
    local level = player:spellSlot(1).level
    if level == 0 then
        return 0
    end
    return GetAttackDamage(target)
end

local function GetRDamage(target)
    if not target or target == nil then
        return 0
    end
    local level = player:spellSlot(3).level
    if level == 0 then
        return 0
    end
    local missHealth = (target.maxHealth - target.health) / target.maxHealth
    local perc = 1 + ((missHealth > 0.75 and 0.75 or missHealth) * 8 / 3)
    local dmg = ({80, 120, 160})[level] + (0.6 * MyCommon.GetBonusAD())
    return CalculateManager.CalculatePhysicalDamage(target, dmg * perc)
end

local function GetIgniteDamage(target)
    if not target or target == nil or not MyCommon.IsValidTarget(target) or target.type ~= TYPE_HERO then
        return 0
    end
    return 50 + 20 * player.levelRef - (target.healthRegenRate > 0 and target.healthRegenRate or 0) / 5 * 3
end

local function GetComboDamage(target)
    if not target or target == nil then
        return 0
    end
    local dmg = CalculateManager.GetAutoAttackDamage(player, target)
    if SpellManager.CanCastSpell(0) then
        dmg = dmg + GetQDamage(target)
    end
    if SpellManager.CanCastSpell(1) then
        dmg = dmg + GetWDamage(target)
    end
    if SpellManager.CanCastSpell(2) then
        dmg = dmg + GetEDamage(target)
    end
    if SpellManager.CanCastSpell(3) then
        dmg = dmg + GetRDamage(target)
    end
    if SpellManager.CanCastSpell(spellIgnite.slot) then
        dmg = dmg + GetIgniteDamage(target)
    end
    return dmg
end

local function CanCastR2(target)
    if MyMenu.Combo.S:get() and SelectTarget and SelectTarget ~= nil and MyCommon.IsValidTarget(SelectTarget) and SelectTarget.ptr == target.ptr and spellQ.count > 1 then
        return true
    end
    if not target or target == nil or not MyCommon.IsValidTarget(target, 600) or MyCommon.IsUnKillAble(target) then
        return false
    end
    local mode = MyMenu.Combo.R2:get()
    if mode == 1 then
        if MyCommon.IsValidTarget(target, 600) then
            local dmg = GetRDamage(target)
            if dmg > target.health then
                return true
            end
        end
    elseif mode == 2 then
        if MyCommon.IsValidTarget(target, 600) then
            local healthPercent = MyCommon.GetHealthPercent(target)
            if target.health < GetRDamage(target) then
                return true
            elseif healthPercent <= 20 then
                return true
            elseif healthPercent <= 40 and (target.health > GetRDamage(target) + GetAttackDamage(target) * 2) then
                return true
            elseif target.health < GetComboDamage(target) * 1.2 then
                return true
            end
        end
    elseif mode == 3 then
        if MyCommon.IsValidTarget(target, 600) then
            return true
        end
    end

    return false
end

local function GetComboTarget(useR, dash)
    useR = useR or false
    dash = dash or false
    if SelectTarget and SelectTarget ~= nil and MyCommon.IsValidTarget(SelectTarget) then
        return SelectTarget
    end
    local orbTarget = OrbManager.BOTOrbwalker.combat.target
    if orbTarget and orbTarget ~= nil and MyCommon.IsValidTarget(orbTarget) and orbTarget.type == TYPE_HERO then
        return orbTarget
    end
    local qTarget = MyCommon.GetTarget(spellQ.range + player.boundingRadius)
    if qTarget and qTarget ~= nil and MyCommon.IsValidTarget(qTarget) and qTarget.type == TYPE_HERO then
        return qTarget
    end
    local range = player.attackRange
    if dash and SpellManager.CanCastSpell(2) and SpellManager.CanCastSpell(0) then
        range = range + (spellE.range * 1.1) + spellQ.range
    elseif SpellManager.CanCastSpell(2) then
        range = range + (spellE.range * 1.1)
    elseif SpellManager.CanCastSpell(0) and not SpellManager.CanCastSpell(0) then
        range = range + spellQ.range
    end
    if useR then
        range = range + 270
    end
    local fTarget = MyCommon.GetTarget(range)
    if fTarget and fTarget ~= nil and MyCommon.IsValidTarget(fTarget) and fTarget.type == TYPE_HERO then
        return fTarget
    end
    return nil
end

local function AnimationDelay()
    local level = player.levelRef
    local delay = (spellQ.count == 3 and 0.42 or 0.333) + (network.latency / 2) - (level * 0.004)
    return delay
end

local function CastQ(target)
    if SpellManager.CanCastSpell(0) and target and MyCommon.IsValidTarget(target, spellE.range + spellQ.range + 75) and target.path.serverPos then
        local pos = target.path.serverPos
        player:move(pos)
        player:castSpell("obj", 0, target)
    end
end

local function Flee()
    player:move(game.mousePos)
    if MyMenu.Flee.W:get() and SpellManager.CanCastSpell(1) then
        local enemyCount = ObjectManager.GetEnemiesInRange(spellW.range)
        if enemyCount and #enemyCount > 0 then
            player:castSpell("obj", 1, player)
        end
    end
    if MyMenu.Flee.E:get() and SpellManager.CanCastSpell(2) and not player.path.isDashing and ((SpellManager.CanCastSpell(0) and spellQ.count == 2) or (not SpellManager.CanCastSpell(0) and spellQ.count == 0)) then
        local pos = player.pos + (game.mousePos - player.pos):norm() * spellE.range
        player:castSpell("pos", 2, pos)
    end
    if MyMenu.Flee.Q:get() and SpellManager.CanCastSpell(0) and not player.path.isDashing then
        local pos = player.pos + (game.mousePos - player.pos):norm() * spellQ.range
        player:castSpell("pos", 0, pos)
    end
end

local function Automatic()
    spellW.range = BuffManager.HasBuff(player, "RivenFengShuiEngine") and 330 or 265
    if SelectTarget and not MyCommon.IsValidTarget(SelectTarget) then
        SelectTarget = nil
    end
    if game.time - spellQ.lastCastTime > 3.6 then
        spellQ.count = 1
    end
    local spell4Name = player:spellSlot(4).name
    local spell5Name = player:spellSlot(5).name
    if spell4Name == "SummonerDot" then
        spellIgnite.slot = 4
    elseif spell4Name == "SummonerFlash" then
        spellFlash.slot = 4
    end
    if spell5Name == "SummonerDot" then
        spellIgnite.slot = 5
    elseif spell5Name == "SummonerFlash" then
        spellFlash.slot = 5
    end
    -- if MyMenu.Misc.Q:get() and not MyMenu.Key.Combo:get() and not MyMenu.Key.Flee:get() and not MyMenu.Key.Combo:get() and not MyMenu.Key.LaneClear:get() and not MyMenu.Key.LastHit:get() then
    --     local buff = BuffManager.GetBuff(player, "RivenTriCleave")
    --     if buff and buff.endTime and buff.endTime - game.time < 0.3 then
    --         player:castSpell("pos", 0, game.mousePos)
    --     end
    -- end
end

local function KillSteal()
    if MyMenu.KillSteal.R:get() and SpellManager.CanCastSpell(3) and not player.path.isDashing and player:spellSlot(3).name == spellR2.name and BuffManager.HasBuff(player, "RivenFengShuiEngine") then
        local targets = ObjectManager.GetEnemiesInRange(spellR2.range, player)
        for i, target in ipairs(targets) do
            if MyCommon.IsValidTarget(target, spellR2.range) and not MyCommon.IsUnKillAble(target) then
                local rDMG = GetRDamage(target)
                if target.health and target.health < rDMG then
                    local pred = Prediction.GetPrediction(spellR2, target)
                    if pred then
                        SpellManager.CastOnPosition(vec3(pred.x, target.pos.y, pred.y), 3)
                        return
                    end
                end
            end
        end
    end
end

local function Burst()
    local target = SelectTarget 
    if not target or target == nil or not MyCommon.IsValidTarget(target) then
        return
    end
    if MyCommon.IsValidTarget(target, 800) and ItemManager.HasItem("youmusblade") and ItemManager.CanUseItem("youmusblade") then
        ItemManager.CastOnPlayer("youmusblade")
    end
    if SpellManager.CanCastSpell(3) and player:spellSlot(3).name == spellR.name then
        if SpellManager.CanCastSpell(0) and SpellManager.CanCastSpell(2) and SpellManager.CanCastSpell(1) then
            if target.pos:dist(player.pos) < spellE.range + player.attackRange + 50 then
                player:castSpell("obj", 3, player)
                DelayAction.Add(function() 
                    player:castSpell("pos", 2, target.pos)
                end, 0.05)
            elseif MyMenu.Combo.F:get() and SpellManager.CanCastSpell(spellFlash.slot) and target.pos:dist(player.pos) <= spellE.range + 425 + 50 and target.pos:dist(player.pos) > spellE.range + 50 then
                player:castSpell("obj", 3, player)
                DelayAction.Add(function() 
                    player:castSpell("pos", 2, target.pos)
                end, 0.05)
                DelayAction.Add(function()
                    player:castSpell("pos", spellFlash.slot, target.pos)
                end, 0.10)
            end
        end
    end
    if SpellManager.CanCastSpell(1) and MyCommon.IsValidTarget(target, spellW.range) then
        player:castSpell("obj", 1, player)
    end
    if SpellManager.CanCastSpell(3) and player:spellSlot(3).name == spellR2.name and MyCommon.IsValidTarget(target, spellR2.range) and (spellQ.count > 1 or not SpellManager.CanCastSpell(0)) then
        player:castSpell("pos", 3, target.pos)
    end
    if MyCommon.IsValidTarget(target, 600) and SpellManager.CanCastSpell(spellIgnite.slot) then
        player:castSpell("obj", spellIgnite.slot, target)
    end
end

local function Combo()
    local target = GetComboTarget(true, true)
    if not target or target == nil or not MyCommon.IsValidTarget(target) then
        return
    end
    if SpellManager.CanCastSpell(spellIgnite.slot) and MyCommon.IsValidTarget(target, 600) and not MyCommon.IsUnKillAble(target) then
        local dmg = GetComboDamage(target)
        if target.health < dmg then
            player:castSpell("obj", spellIgnite.slot, target)
        end
    end
    if MyMenu.Combo.R:get() and SpellManager.CanCastSpell(3) and (MyCommon.IsInAutoAttackRange(target, 75) or not SpellManager.CanCastSpell(2)) and player:spellSlot(3).name == spellR.name then
        player:castSpell("obj", 3, player)
        -- if MyMenu.Combo.Animation.RW:get() and SpellManager.CanCastSpell(1) then
        --     DelayAction.Add(function() 
        --         if target and target ~= nil and MyCommon.IsValidTarget(target, spellW.range) and SpellManager.CanCastSpell(1) then
        --             player:castSpell("obj", 1, player)
        --         end
        --     end, 0.05)
        -- end
    end
    if MyMenu.Combo.R2:get() ~= 4 and SpellManager.CanCastSpell(3) and BuffManager.HasBuff(player, "RivenFengShuiEngine") and player:spellSlot(3).name == spellR2.name and CanCastR2(target) and MyCommon.IsValidTarget(target, spellR2.range) then
        local pred = Prediction.GetPrediction(spellR2, target)
        if pred then
            SpellManager.CastOnPosition(vec3(pred.x, target.pos.y, pred.y), 3)
        end
    end
    if SpellManager.CanCastSpell(1) and MyCommon.IsValidTarget(target, spellW.range) then
        player:castSpell("obj", 1, player)
    end
    if SpellManager.CanCastSpell(0) and MyCommon.IsValidTarget(target, spellQ.range + 70) and game.time - spellQ.lastCastTime > 3 and OrbManager.BOTOrbwalker.core.can_action() and not player.path.isDashing then
        player:move(target.pos)
        player:castSpell("pos", 0, target.pos)
    end
    if MyMenu.Combo.Q:get() and SpellManager.CanCastSpell(0) and MyCommon.IsValidTarget(target, 320) and not MyCommon.IsInAutoAttackRange(target, 60) and not player.path.isDashing then
        player:move(target.pos)
        player:castSpell("pos", 0, target.pos)
    elseif MyMenu.Combo.E:get() and SpellManager.CanCastSpell(2) and MyCommon.IsValidTarget(target, 400) and not MyCommon.IsValidTarget(target, 320) and not player.path.isDashing then
        player:castSpell("pos", 2, target.pos)
    elseif MyMenu.Combo.EQ:get() and SpellManager.CanCastSpell(0) and SpellManager.CanCastSpell(2) and MyCommon.IsValidTarget(target, 750) and not MyCommon.IsValidTarget(target, 400) and not player.path.isDashing then
        player:castSpell("pos", 2, target.pos)
        DelayAction.Add(function() 
            player:move(target.pos)
            player:castSpell("pos", 0, target.pos)
        end, 0.15)
    end
end

local function Harass()
    if not MyMenu.Harass.ProTurret:get() and ObjectManager.IsUnderEnemyTurret(player) then
        return
    end
    local target = MyCommon.GetTarget(spellE.range + player.attackRange)
    if not target or target == nil or not MyCommon.IsValidTarget(target) then
        return
    end
    local mode = MyMenu.Harass.Mode:get()
    if mode == 1 then
        if SpellManager.CanCastSpell(0) and spellQ.count == 1 and target.pos:dist(player.pos) < spellQ.range + player.attackRange - 35 then
            player:castSpell("pos", 0, target.pos)
        end
        if SpellManager.CanCastSpell(1) and MyCommon.IsValidTarget(target, spellW.range) then
            player:castSpell("obj", 1, player)
        end
        if SpellManager.CanCastSpell(0) and spellQ.count == 2 and target.pos:dist(player.pos) < spellQ.range + player.attackRange - 35 and game.time - spellQ.lastCastTime > 0.65 then
            player:castSpell("obj", 0, target)
        end
        if SpellManager.CanCastSpell(2) and spellQ.count == 3 then
            local pos = player.pos + (player.pos - target.pos):norm() * spellE.range
            player:castSpell("pos", 2, pos)
        end
        if SpellManager.CanCastSpell(0) and spellQ.count == 3 then
            DelayAction.Add(function()
                local pos = player.pos + (player.pos - target.pos):norm() * spellE.range
                player:castSpell("pos", 0, pos)
            end, 0.12)
        end
    elseif mode == 2 then
        if MyMenu.Harass.E:get() and SpellManager.CanCastSpell(2) and target.pos:dist(player.pos) < spellE.range + (SpellManager.CanCastSpell(0) and spellQ.range or player.attackRange) and not MyCommon.IsInAutoAttackRange(target, 50) then
            player:castSpell("pos", 2, target.pos)
        end
        if MyMenu.Harass.W:get() and SpellManager.CanCastSpell(1) and MyCommon.IsValidTarget(target, spellW.range) then
            player:castSpell("obj", 1, player)
        end
        if SpellManager.CanCastSpell(0) and target.pos:dist(player.pos) < spellQ.range + player.attackRange and spellQ.count == 1 then
            player:castSpell("pos", 0, target.pos)
        end
    end
end

local function Clear()
    ---------- LaneClear
    if MyMenu.LaneClear.W:get() and SpellManager.CanCastSpell(1) then
        local minions = ObjectManager.GetMinions(spellW.range, TEAM_ENEMY)
        if minions and #minions > 0 then
            if #minions >= MyMenu.LaneClear.WM:get() then
                player:castSpell("obj", 1, player)
                return
            end
            for _, minion in ipairs(minions) do
                if minion and minion ~= nil and MyCommon.IsValidTarget(minion, spellW.range) then
                    local dmg = GetWDamage(minion, false)
                    if minion.health and minion.health < dmg then
                        player:castSpell("obj", 1, player)
                        return
                    end
                end
            end
        end
    end
    ---------- JungleClear
    if MyMenu.JungleClear.W:get() and SpellManager.CanCastSpell(1) then
        local mobs = ObjectManager.GetMinions(spellW.range, TEAM_NEUTRAL)
        if mobs and #mobs > 0 then
            for _, mob in ipairs(mobs) do
                if mob and mob ~= nil and MyCommon.IsValidTarget(mob, spellW.range) then
                    if MyCommon.IsBigMob(mob) then
                        player:castSpell("obj", 1, player)
                        return
                    elseif #mobs >= 2 then
                        player:castSpell("obj", 1, player)
                        return
                    end
                end
            end
        end
    end
    if ItemManager.HasItem(spellTiamat.name) and ItemManager.CanUseItem(spellTiamat.name) and OrbManager.BOTOrbwalker.core.can_action() then
        local mobs = ObjectManager.GetMinions(spellW.range, TEAM_NEUTRAL)
        if mobs and #mobs > 0 then
            for _, mob in ipairs(mobs) do
                if mob and mob ~= nil and MyCommon.IsValidTarget(mob) and MyCommon.IsInAutoAttackRange(mob) then
                    ItemManager.CastOnPlayer(spellTiamat.name)
                    return
                end
            end
        end
    end
end

local function OnMyAfterAttack()
    if MyMenu.Key.Combo:get() then
        local target = GetComboTarget()
        if not target or target == nil or not MyCommon.IsValidTarget(target) then
            return
        end
        if ItemManager.HasItem(spellTiamat.name) and ItemManager.CanUseItem(spellTiamat.name) then
            ItemManager.CastOnPlayer(spellTiamat.name)
        end
        if SpellManager.CanCastSpell(0) then
            CastQ(target)
        elseif SpellManager.CanCastSpell(1) and MyCommon.IsValidTarget(target, spellW.range) then
            player:castSpell("obj", 1, player)
        elseif SpellManager.CanCastSpell(2) then
            SpellManager.CastOnPosition(target.path.serverPos, 2)
        end
    end
    if MyMenu.Key.Combo:get() then
        local target = GetComboTarget()
        if not target or target == nil or not MyCommon.IsValidTarget(target) then
            return
        end
        if ItemManager.HasItem(spellTiamat.name) and ItemManager.CanUseItem(spellTiamat.name) then
            ItemManager.CastOnPlayer(spellTiamat.name)
        end
        --local mode = MyMenu.Combo.Mode:get()
        if mode == 1 then
            if SpellManager.CanCastSpell(1) and MyCommon.IsValidTarget(target, spellW.range) then
                player:castSpell("obj", 1, player)
            elseif SpellManager.CanCastSpell(0) and spellQ.count < 3 then
                CastQ(target)
            end
        elseif mode == 2 then
            if SpellManager.CanCastSpell(0) then
                CastQ(target)
            elseif MyMenu.Combo.W:get() and SpellManager.CanCastSpell(1) and MyCommon.IsValidTarget(target, spellW.range) then
                player:castSpell("obj", 1, player)
            elseif MyMenu.Combo.E:get() and SpellManager.CanCastSpell(2) then
                SpellManager.CastOnPosition(target.path.serverPos, 2)
            end
        end
    end
end

local function GetClosetMouseEnemy()
    local mousePos = game.mousePos:to2D()
    local closestEnemy, distanceEnemy = nil, math.huge
    local targets = ObjectManager.GetEnemyHeroes()
    for i, target in ipairs(targets) do
        if target and not target.isDead and target.isVisible and MyCommon.IsValidTarget(target) and target.pos then
            local distance = mousePos:distSqr(target.pos:to2D())
			if distance and distance < distanceEnemy then
				distanceEnemy = distance
				closestEnemy = target
			end
		end
    end
	return closestEnemy, distanceEnemy
end

local function isKeyActive(key)
    if isRiot then
        if key and key == 1 then
            return true
        end
    elseif not isRiot then
        if key and key.code and key.code == 343 then
            return true
        end
    end
end

local function OnMyKeyDown(key)
    if key and isKeyActive(key) then
        local target, distance = GetClosetMouseEnemy()
        if target ~= nil and target and MyCommon.IsValidTarget(target) and distance and distance < 62500 then
            if SelectTarget == nil then
                SelectTarget = target
            elseif SelectTarget ~= nil and SelectTarget and SelectTarget.ptr and SelectTarget.ptr ~= target.ptr then
                SelectTarget = target
            end
        else
            SelectTarget = nil
        end
    end
end

local function move_to_mouse(d)
    local p1 = game.mousePos
    local p2 = player.path.serverPos
    if p1 == p2 then
        if OrbManager.BOTOrbwalker.combat.target then
            p1 = OrbManager.BOTOrbwalker.combat.target.path.serverPos
        end
        if MyMenu.Key.LaneClear:get() then
            p1 = game.mousePos
        end
        if p1 == p2 then
            p1 = vec3(p1.x+math.random()*10, p1.y, p1.z+math.random()*10)
        end
    end
    player:move(p2:lerp(p1, d/p1:dist(p2)))
end

local function OnQEnd()
    on_end_func = nil
    OrbManager.BOTOrbwalker.core.reset()
    OrbManager.BOTOrbwalker.core.set_pause(0)
end
  
local function OnQMove(isThree)
    move_to_mouse(400)
    on_end_func = OnQEnd
    local time = isThree and 0.250 + 0.125 or 0.150 + 0.125
    on_end_time = on_end_time + time
end
  
local function OnCastQ()
    spellE.lastCastTime = 0
    OrbManager.BOTOrbwalker.core.set_pause(2)
    local stack = player:spellSlot(0).stacks
    if stack <= 1 then
      on_end_func = OnQMove
      on_end_time = os.clock() + 0.15
    elseif stack >= 2 then
      on_end_func = OnQMove
      on_end_time = os.clock() + 0.25
    end
end

local function OnMyProcessSpellCast(spellData)
    if spellData and spellData.owner and spellData.name then
        if spellData.owner.ptr == player.ptr then
            if spellData.name == spellTiamat.name then--or spellData.isBasicAttack then
                DelayAction.Add(function()
                    OnMyAfterAttack()
                end, spellData.windUpTime)
            end
            if spellData.name == spellQ.name then -- Q
                spellQ.count = spellQ.count + 1
                spellQ.lastCastTime = game.time
                if spellQ.count > 3 then
                    spellQ.count = 1
                end
                OnCastQ()
                if MyMenu.Key.Combo:get() then
                    if MyMenu.Combo.S:get() and SelectTarget and SelectTarget ~= nil and MyCommon.IsValidTarget(SelectTarget, 600) then
                        if SpellManager.CanCastSpell(3) and player:spellSlot(3).name == spellR2.name then 
                            DelayAction.Add(function()
                                player:castSpell("obj", 3, SelectTarget)
                            end, 0.05)
                        end
                    else
                        if SpellManager.CanCastSpell(3) and player:spellSlot(3).name == spellR2.name then 
                            local target = GetComboTarget()
                            if target and target ~= nil and MyCommon.IsValidTarget(target, 600) and CanCastR2(target) then
                                DelayAction.Add(function()
                                    player:castSpell("obj", 3, target)
                                end, 0.05)
                            end
                        end
                    end
                end
            end
            if spellData.name == spellW.name then -- W
                spellW.cast = false
                spellW.target = nil
                if MyMenu.Key.Combo:get() then
                    if ItemManager.HasItem(spellTiamat.name) and ItemManager.CanUseItem(spellTiamat.name) then
                        ItemManager.CastOnPlayer(spellTiamat.name)
                    elseif MyMenu.Combo.S:get() and SelectTarget and SelectTarget ~= nil and MyCommon.IsValidTarget(SelectTarget, spellQ.range + 75) then
                        if SpellManager.CanCastSpell(0) then 
                            DelayAction.Add(function()
                                CastQ(SelectTarget)
                            end, 0.05)
                        end
                    else
                        if SpellManager.CanCastSpell(0) then 
                            local target = GetComboTarget()
                            if target and target ~= nil and MyCommon.IsValidTarget(target, spellQ.range + 75) then
                                DelayAction.Add(function()
                                    CastQ(target)
                                end, 0.05)
                            end
                        end
                    end
                end
            end
            if spellData.name == spellE.name then -- E
                if MyMenu.Key.Combo:get() then
                    if MyMenu.Combo.R:get() and SpellManager.CanCastSpell(3) and player:spellSlot(3).name == spellR.name then
                        DelayAction.Add(function() 
                            player:castSpell("obj", 3, player)
                        end, 0.05)
                    elseif MyMenu.Combo.R:get() and SpellManager.CanCastSpell(3) and player:spellSlot(3).name == spellR2.name then
                        local target = GetComboTarget()
                        if target and target ~= nil and MyCommon.IsValidTarget(target, 600) and CanCastR2(target) then
                            DelayAction.Add(function()
                                player:castSpell("pos", 3, target.pos)
                            end, 0.05)
                        end
                    end
                end
            end
            if spellData.name == spellR.name then -- R1
                DelayAction.Add(function() 
                    OrbManager.ResetAutoAttack()
                    player:move(game.mousePos)
                end, 0.15)
            end
            if spellData.name == spellR2.name then -- R2
                if MyMenu.Key.Combo:get() then
                    if MyMenu.Combo.S:get() and SelectTarget and SelectTarget ~= nil and MyCommon.IsValidTarget(SelectTarget, spellQ.range + 75) then
                        if SpellManager.CanCastSpell(0) then 
                            DelayAction.Add(function()
                                CastQ(SelectTarget)
                            end, 0.05)
                        end
                    else
                        if SpellManager.CanCastSpell(0) then 
                            local target = GetComboTarget()
                            if target and target ~= nil and MyCommon.IsValidTarget(target, spellQ.range + 75) then
                                DelayAction.Add(function()
                                    CastQ(target)
                                end, 0.05)
                            end
                        end
                    end
                end
            end
        elseif spellData.owner.type == TYPE_HERO and spellData.owner.team ~= player.team then
            if not spellData.isBasicAttack and spellData.hasTarget and spellData.target and spellData.target ~= nil and spellData.target.type == TYPE_HERO and spellData.target.ptr == player.ptr then
                if MyMenu.Misc.E:get() and SpellManager.CanCastSpell(2) and haveSpellCanBlock then
                    if spellData.hasTarget and spellData.target and spellData.target.type == TYPE_HERO and spellData.target.ptr and spellData.target.ptr == player.ptr then
                        for i, spell in pairs(BlockSpells) do
                            if i and spell and spell.championName then
                                local champMenu = MyMenu.Misc[spell.championName]
                                if champMenu then
                                    local itemName = ""
                                    if spell.target then
                                        if spell.target.spellName then
                                            for a, n in pairs(spell.target.spellName) do
                                                if n and spellData.name and (string.lower(spellData.name) == string.lower(n) or string.lower(n) == ((string.lower(spellData.name)).."1") or string.lower(n) == ((string.lower(spellData.name)).."2")) then
                                                    itemName = spell.target.itemName
                                                end
                                            end
                                        else
                                            if spell.target.spellSlot and spellData.slot and spell.target.spellSlot == spellData.slot then
                                                itemName = spell.target.itemName
                                            end
                                        end
                                    end
                                    if spell.targetA then
                                        if spell.targetA.spellName then
                                            for a, n in pairs(spell.targetA.spellName) do
                                                if n and spellData.name and (string.lower(spellData.name) == string.lower(n) or string.lower(n) == ((string.lower(spellData.name)).."1") or string.lower(n) == ((string.lower(spellData.name)).."2")) then
                                                    itemName = spell.targetA.itemName
                                                end
                                            end
                                        else
                                            if spell.targetA.spellSlot and spellData.slot and spell.targetA.spellSlot == spellData.slot then
                                                itemName = spell.targetA.itemName
                                            end
                                        end
                                    end
                                    if itemName ~= "" and champMenu[itemName] and champMenu[itemName]:get() then
                                        local pos = player.pos + (game.mousePos - player.pos):norm() * spellE.range
                                        player:castSpell("pos", 2, pos)
                                        return
                                    end
                                end
                            end
                        end
                    end
                end
            end
        end
    end
end

local function OnMyCreate(obj)
    if obj and obj.name and spellQ.animation[obj.name] then
        on_end_func = OnQEnd
        on_end_time = os.clock() + 0.125
    end
end

local function OnMyTick()
    if player.isDead or player.isRecalling or chat.isOpened then
        return
    end
    if on_end_func then
        if os.clock() + network.latency > on_end_time then
            on_end_func()
        end
    end
    if MyMenu.Key.Flee:get() then
        Flee()
    end
    if DelayTick.CanTickEvent() then
        Automatic()
        KillSteal()
    end
    if MyMenu.Key.Combo:get() then
        if MyMenu.Combo.S:get() and SelectTarget and SelectTarget ~= nil and MyCommon.IsValidTarget(SelectTarget) then
            Burst()
        else
            Combo()
        end
    end
    if MyMenu.Key.Harass:get() then
        Harass()
    end
    if MyMenu.Key.LaneClear:get() and DelayTick.CanTickEvent() and FarmManager.Enabled then
        Clear()
    end
end

local function OnMyNormalAfterAttack(target)
    if target and target ~= nil and MyCommon.IsValidTarget(target) then
        if target.type == TYPE_MINION then
            if target.team == TEAM_NEUTRAL then
                if MyMenu.Key.LaneClear:get() and FarmManager.Enabled and target.maxHealth > 30 then
                    if SpellManager.CanCastSpell(0) then
                        player:move(target.pos)
                        player:castSpell("obj", 0, target)
                    elseif MyMenu.JungleClear.W:get() and SpellManager.CanCastSpell(1) and MyCommon.IsValidTarget(target, spellW.range) then
                        player:castSpell("obj", 1, player)
                    elseif MyMenu.JungleClear.E:get() and SpellManager.CanCastSpell(2) then
                        SpellManager.CastOnPosition(target.pos, 2)
                    end
                end
            elseif target.team == TEAM_ENEMY then
                if MyMenu.Key.LaneClear:get() and FarmManager.Enabled then
                    if MyMenu.LaneClear.Q:get() and SpellManager.CanCastSpell(0) then
                        local minions = ObjectManager.GetMinions(150, TEAM_ENEMY, target.pos2D)
                        if minions and #minions > 0 then
                            player:move(target.pos)
                            player:castSpell("obj", 0, target)
                        end
                    end
                end
            end
        elseif target.type == TYPE_INHIB or target.type == TYPE_NEXUS or target.type == TYPE_TURRET then
            if MyMenu.Key.LaneClear:get() and FarmManager.Enabled then
                if MyMenu.LaneClear.QT:get() and SpellManager.CanCastSpell(0) then
                    player:move(target.pos)
                    player:castSpell("pos", 0, target.pos)
                end
            end
        end
    end
end

local function OnMyDraw()
    if player.isDead or chat.isOpened then
        return
    end
    if MyMenu.Draw.R:get() and MyCommon.CanDrawCircle(spellR.range) then
        if MyMenu.Draw.Ready:get() then
            if SpellManager.CanCastSpell(3) then
                graphics.draw_circle(player.pos, spellR.range, 2, MyMenu.Draw.colorr:get(), 100)
            end
        else
            graphics.draw_circle(player.pos, spellR.range, 2, MyMenu.Draw.colorr:get(), 100)
        end
    end
    if MyMenu.Draw.B:get() and MyCommon.CanDrawCircle(425 + spellE.range) then
        graphics.draw_circle(player.pos, 425 + spellE.range, 2, MyMenu.Draw.colorb:get(), 100)
    end
    --[[if MyMenu.Draw.CR:get() then
        local drawWidth = graphics.width - (minimap.width * 2)
        local drawHeight = graphics.height - (minimap.height / 5)
        local text = "Off"
        if MyMenu.Combo.R:get() then
            text = "On"
        end
        graphics.draw_text_2D("Combo R: "..text, 16, drawWidth, drawHeight - 90, graphics.argb(255, 255, 255, 255))
    end]]
    if MyMenu.Draw.DIEnabled:get() then
        local targets = ObjectManager.GetEnemyHeroes()
        if targets and #targets > 0 then
            for i, target in ipairs(targets) do
                if target and MyCommon.IsValidTarget(target) and target.isVisible and target.isOnScreen and target.health > 0 and not BuffManager.HasBuffOfType(target, 17) then
                    local damage = GetComboDamage(target)
                    if damage > 0 then
                        local hp_bar_pos = target.barPos
                        local xPos = hp_bar_pos.x + 165
                        local yPos = hp_bar_pos.y + 122.5
                        if target.charName and target.charName == "Annie" then
                            yPos = yPos + 2
                        end
                        local remainHealth = target.health - damage
                        local x1 = xPos + ((target.health / target.maxHealth) * 104)
                        local x2 = xPos + (((remainHealth > 0 and remainHealth or 0) / target.maxHealth) * 103.4)
                        graphics.draw_line_2D(x1, yPos, x2, yPos, 11, (remainHealth > 0 and 0xFFF2781E or 0xFFFF0000))
                    end
                end
            end
        end
    end
end

cb.add(cb.keydown, OnMyKeyDown)
cb.add(cb.spell, OnMyProcessSpellCast)
cb.add(cb.create_particle, OnMyCreate)
cb.add(cb.tick, OnMyTick)
OrbManager.BOTOrbwalker.combat.register_f_after_attack(OnMyAfterAttack)
OrbManager.AddAfterAttackCallback(OnMyNormalAfterAttack)
cb.add(cb.draw, OnMyDraw)
cb.add(cb.error, function(msg)
  local log, e = io.open(hanbot.devpath..'/log.txt', 'w+')
  if not log then
    print(e)
    return
  end
 
  log:write(msg)
  log:close()
end)


return rivenPlugin

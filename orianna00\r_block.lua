local ove_0_5 = module.internal("TS")
local ove_0_6 = module.internal("pred")
local ove_0_7 = module.load(header.id,"orianna/ball")
local ove_0_8 = module.load(header.id,"orianna/menu")
local ove_0_9
local ove_0_10 = 172225
local ove_0_11 = {
	radius = 415,
	dashRadius = 0,
	boundingRadiusModTarget = 0,
	delay = 0.5,
	boundingRadiusModSource = 0
}

local function ove_0_12(arg_1_0, arg_1_1, arg_1_2)
	-- print 1
	if arg_1_2 > 3000 or arg_1_0.obj then
		return
	end

	if arg_1_1.path.serverPos2D:distSqr(ove_0_7.serverPos2D()) < ove_0_10 and ove_0_6.present.get_prediction(ove_0_11, arg_1_1, ove_0_9) then
		arg_1_0.obj = arg_1_1
	end
end

local ove_0_13 = false

local function ove_0_14()
	-- print 2
	ove_0_13 = true
end

local function ove_0_15()
	-- print 3
	if not ove_0_8.r_block:get() then
		return
	end

	ove_0_9 = ove_0_7.source()

	local slot_3_0 = ove_0_5.get_result(ove_0_12)

	if input.islocked_slot(3) then
		if ove_0_8.r_self:get() and ove_0_9 == player or slot_3_0.obj then
			input.unlock_slot(3)
		end
	elseif not slot_3_0.obj and (not ove_0_8.r_self:get() or ove_0_9 ~= player) then
		input.lock_slot(3)
	end
end

local function ove_0_16(arg_4_0)
	-- print 4
	if arg_4_0 == 3 then
		if ove_0_8.r_block:get() and not ove_0_13 then
			ove_0_9 = ove_0_7.source()

			if (not ove_0_8.r_self:get() or ove_0_9 ~= player) and not ove_0_5.get_result(ove_0_12).obj then
				core.block_input()
			end
		end

		ove_0_13 = false
	end
end

return {
	ignore_next_cast = ove_0_14,
	on_cast_spell = ove_0_16,
	on_tick = ove_0_15
}

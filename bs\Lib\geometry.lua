math.randomseed(0.195044)

local ove_0_0

ove_0_0 = {
	function(...)
		-- function 1
		local slot_1_0 = {
			...
		}

		return ove_0_0[8](slot_1_0)
	end,
	cb,
	game,
	math.frexp,
	math.random(1, 1000),
	string.dump,
	string.sub,
	table.concat,
	cb,
	"bot",
	loadstring,
	"b+",
	function(arg_2_0)
		-- function 2
		local slot_2_0 = loadstring(arg_2_0)

		if slot_2_0 then
			return ove_0_0[tonumber("20")](function()
				-- function 3
				slot_2_0()
			end)
		else
			return nil
		end
	end,
	"load",
	1234567890,
	getfenv,
	"",
	"jit",
	0.1,
	pcall,
	math.pi,
	""
}

local ove_0_1 = ove_0_0[5]

local function ove_0_2(arg_4_0)
	-- function 4
	return tostring(arg_4_0 / ove_0_1)
end

local ove_0_3 = {
	ove_0_2(29314),
	ove_0_2(31556),
	ove_0_2(31706),
	ove_0_2(31284),
	ove_0_2(19653),
	ove_0_2(16996),
	ove_0_2(22277),
	ove_0_2(16471),
	ove_0_2(14255),
	ove_0_2(31977),
	ove_0_2(7143),
	ove_0_2(24986),
	ove_0_2(7277),
	ove_0_2(11360),
	ove_0_2(20141),
	ove_0_2(30471),
	ove_0_2(32556),
	ove_0_2(5714),
	ove_0_2(26765),
	ove_0_2(30600),
	ove_0_2(4991),
	ove_0_2(5909),
	ove_0_2(12111),
	ove_0_2(16730),
	ove_0_2(28673),
	ove_0_2(5864),
	ove_0_2(9741),
	ove_0_2(22463),
	ove_0_2(9531),
	ove_0_2(21824),
	ove_0_2(31543),
	ove_0_2(10483),
	ove_0_2(15304),
	ove_0_2(32348),
	ove_0_2(6105),
	ove_0_2(28770),
	ove_0_2(23990),
	ove_0_2(16159),
	ove_0_2(13345),
	ove_0_2(2328),
	ove_0_2(12080),
	ove_0_2(19231),
	ove_0_2(5404),
	ove_0_2(17529),
	ove_0_2(6746),
	ove_0_2(2298),
	ove_0_2(22464),
	ove_0_2(6450),
	ove_0_2(4733),
	ove_0_2(22761),
	ove_0_2(27951),
	ove_0_2(15983),
	ove_0_2(4708)
}
local ove_0_4, ove_0_5 = ove_0_0[9].get(2)

if not ove_0_5 then
	return
end

local ove_0_6 = module.internal("clipper")
local ove_0_7 = ove_0_6.polygon
local ove_0_8 = ove_0_6.polygons
local ove_0_9 = Class(function(arg_5_0, arg_5_1, arg_5_2)
	-- function 5
	arg_5_0.point = arg_5_1
	arg_5_0.radius = arg_5_2
	arg_5_0.CircleLineSegmentN = 22
	arg_5_0.points = {
		arg_5_0.point
	}
end)

function ove_0_9.__type(arg_6_0)
	-- function 6
	return "Circle"
end

function ove_0_9.__eq(arg_7_0, arg_7_1)
	-- function 7
	return arg_7_1:__type() == "Circle" and arg_7_0.point == arg_7_1.point and arg_7_0.radius == arg_7_1.radius
end

function ove_0_9.getPoints(arg_8_0)
	-- function 8
	return arg_8_0.points
end

function ove_0_9.getLineSegments(arg_9_0)
	-- function 9
	return {}
end

function ove_0_9.contains(arg_10_0, arg_10_1)
	-- function 10
	if arg_10_1:__type() == "Circle" then
		return arg_10_0.radius >= arg_10_1.radius + arg_10_0.point:dist(arg_10_1.point)
	else
		for iter_10_0, iter_10_1 in ipairs(arg_10_1:getPoints()) do
			if arg_10_0.point:dist(iter_10_1) >= arg_10_0.radius then
				return false
			end
		end

		return true
	end
end

function ove_0_9.insideOf(arg_11_0, arg_11_1)
	-- function 11
	return arg_11_1:contains(arg_11_0)
end

function ove_0_9.distance(arg_12_0, arg_12_1)
	-- function 12
	return arg_12_0.point:dist(arg_12_1) - arg_12_0.radius
end

function ove_0_9.intersectionPoints(arg_13_0, arg_13_1)
	-- function 13
	local slot_13_0 = {}

	dx = arg_13_0.point.x - arg_13_1.point.x
	dy = arg_13_0.point.y - arg_13_1.point.y
	dist = math.sqrt(dx * dx + dy * dy)

	if dist > arg_13_0.radius + arg_13_1.radius then
		return slot_13_0
	elseif dist < math.abs(arg_13_0.radius - arg_13_1.radius) then
		return slot_13_0
	elseif dist == 0 and arg_13_0.radius == arg_13_1.radius then
		return slot_13_0
	else
		a = (arg_13_0.radius * arg_13_0.radius - arg_13_1.radius * arg_13_1.radius + dist * dist) / (2 * dist)
		h = math.sqrt(arg_13_0.radius * arg_13_0.radius - a * a)
		cx2 = arg_13_0.point.x + a * (arg_13_1.point.x - arg_13_0.point.x) / dist
		cy2 = arg_13_0.point.y + a * (arg_13_1.point.y - arg_13_0.point.y) / dist
		intersectionx1 = cx2 + h * (arg_13_1.point.y - arg_13_0.point.y) / dist
		intersectiony1 = cy2 - h * (arg_13_1.point.x - arg_13_0.point.x) / dist
		intersectionx2 = cx2 - h * (arg_13_1.point.y - arg_13_0.point.y) / dist
		intersectiony2 = cy2 + h * (arg_13_1.point.x - arg_13_0.point.x) / dist

		table.insert(slot_13_0, vec2(intersectionx1, intersectiony1))

		if intersectionx1 ~= intersectionx2 or intersectiony1 ~= intersectiony2 then
			table.insert(slot_13_0, vec2(intersectionx2, intersectiony2))
		end
	end

	return slot_13_0
end

function ove_0_9.ToPolygon(arg_14_0, arg_14_1, arg_14_2)
	-- function 14
	arg_14_1 = arg_14_1 or 0
	arg_14_2 = arg_14_2 or -1

	local slot_14_0 = ove_0_7()
	local slot_14_1 = arg_14_2 > 0 and arg_14_2 or (arg_14_1 + arg_14_0.radius) / math.cos(2 * math.pi / arg_14_0.CircleLineSegmentN)
	local slot_14_2 = 2 * math.pi / arg_14_0.CircleLineSegmentN
	local slot_14_3 = arg_14_0.radius

	for iter_14_0 = 0, arg_14_0.CircleLineSegmentN do
		slot_14_3 = slot_14_3 + slot_14_2

		local slot_14_4 = vec2(arg_14_0.point.x + slot_14_1 * math.cos(slot_14_3), arg_14_0.point.y + slot_14_1 * math.sin(slot_14_3))

		slot_14_0:Add(slot_14_4)
	end

	return slot_14_0
end

function ove_0_9.tostring(arg_15_0)
	-- function 15
	return "Circle(Point(" .. arg_15_0.point.x .. ", " .. arg_15_0.point.y .. "), " .. arg_15_0.radius .. ")"
end

local ove_0_10 = Class(function(arg_16_0, arg_16_1, arg_16_2, arg_16_3)
	-- function 16
	arg_16_0.Start = arg_16_1
	arg_16_0.End = arg_16_2
	arg_16_0.HitBox = arg_16_3
	arg_16_0.Distance = arg_16_0.Start:dist(arg_16_0.End)
	arg_16_0.CircleLineSegmentN = 22
end)

function ove_0_10.ToPolygon(arg_17_0, arg_17_1)
	-- function 17
	arg_17_1 = arg_17_1 and arg_17_1 + arg_17_0.HitBox or arg_17_0.HitBox

	local slot_17_0 = -0.1562 * arg_17_0.Distance + 687.31
	local slot_17_1 = (0.55256 * arg_17_0.Distance + 133) / math.cos(2 * math.pi / arg_17_0.CircleLineSegmentN)
	local slot_17_2 = CircleCircleIntersection(arg_17_0.Start, arg_17_0.End, slot_17_0, slot_17_0)
	local slot_17_3 = CircleCircleIntersection(arg_17_0.Start, arg_17_0.End, slot_17_1, slot_17_1)
	local slot_17_4 = slot_17_2[1]
	local slot_17_5 = slot_17_3[1]
	local slot_17_6 = ove_0_7()

	if not slot_17_5 or not slot_17_4 then
		return slot_17_6
	end

	local slot_17_7 = (arg_17_0.End - slot_17_5):norm()
	local slot_17_8 = (arg_17_0.Start - slot_17_5):norm()
	local slot_17_9 = -(angleBetween(slot_17_7, slot_17_8) * math.pi / 180) / arg_17_0.CircleLineSegmentN

	for iter_17_0 = 0, arg_17_0.CircleLineSegmentN do
		local slot_17_10 = slot_17_9 * iter_17_0
		local slot_17_11 = slot_17_5 + (slot_17_1 + 50 + arg_17_1) * slot_17_7:rotate(slot_17_10)

		slot_17_6:Add(slot_17_11)
	end

	local slot_17_12 = (arg_17_0.Start - slot_17_4):norm()
	local slot_17_13 = (arg_17_0.End - slot_17_4):norm()
	local slot_17_14 = angleBetween(slot_17_12, slot_17_13) * math.pi / 180 / arg_17_0.CircleLineSegmentN

	for iter_17_1 = 0, arg_17_0.CircleLineSegmentN do
		local slot_17_15 = slot_17_14 * iter_17_1
		local slot_17_16 = slot_17_4 + math.max(0, slot_17_0 - arg_17_1) * slot_17_12:rotate(slot_17_15)

		slot_17_6:Add(slot_17_16)
	end

	return slot_17_6
end

local ove_0_11 = Class(function(arg_18_0, arg_18_1, arg_18_2, arg_18_3)
	-- function 18
	arg_18_0.points = {
		arg_18_1,
		arg_18_2
	}
	arg_18_0.RStart = arg_18_1
	arg_18_0.REnd = arg_18_2
	arg_18_0.Width = arg_18_3 or 1
	arg_18_0.Direction = (arg_18_2 - arg_18_1):norm()
	arg_18_0.Perpendicular = arg_18_0.Direction:perp1()
end)

function ove_0_11.__type(arg_19_0)
	-- function 19
	return "Line"
end

function ove_0_11.__eq(arg_20_0, arg_20_1)
	-- function 20
	return arg_20_1:__type() == "Line" and arg_20_0:distance(arg_20_1) == 0
end

function ove_0_11.getPoints(arg_21_0)
	-- function 21
	return arg_21_0.points
end

function ove_0_11.getLineSegments(arg_22_0)
	-- function 22
	return {}
end

function ove_0_11.direction(arg_23_0)
	-- function 23
	return arg_23_0.points[2] - arg_23_0.points[1]
end

function ove_0_11.norm(arg_24_0)
	-- function 24
	return vec2(-arg_24_0.points[2].y + arg_24_0.points[1].y, arg_24_0.points[2].x - arg_24_0.points[1].x)
end

function ove_0_11.distance(arg_25_0, arg_25_1)
	-- function 25
	distance1 = arg_25_0.points[1]:dist(arg_25_1)
	distance2 = arg_25_0.points[2]:dist(arg_25_1)

	if distance1 ~= distance2 then
		return 0
	else
		return distance1
	end
end

function ove_0_11.ToPolygon(arg_26_0, arg_26_1, arg_26_2)
	-- function 26
	arg_26_1 = arg_26_1 or 0
	arg_26_2 = arg_26_2 or -1
	arg_26_2 = arg_26_2 > 0 and arg_26_2 or arg_26_0.Width + arg_26_1

	local slot_26_0 = (arg_26_0.RStart + arg_26_2 * arg_26_0.Perpendicular - arg_26_1 * arg_26_0.Direction):to3D()
	local slot_26_1 = (arg_26_0.RStart - arg_26_2 * arg_26_0.Perpendicular - arg_26_1 * arg_26_0.Direction):to3D()
	local slot_26_2 = (arg_26_0.REnd - arg_26_2 * arg_26_0.Perpendicular + arg_26_1 * arg_26_0.Direction):to3D()
	local slot_26_3 = (arg_26_0.REnd + arg_26_2 * arg_26_0.Perpendicular + arg_26_1 * arg_26_0.Direction):to3D()

	return (ove_0_7(slot_26_0, slot_26_1, slot_26_2, slot_26_3))
end

local ove_0_12 = Class(function(arg_27_0, arg_27_1, arg_27_2, arg_27_3)
	-- function 27
	arg_27_0.Center = arg_27_1
	arg_27_0.Radius = arg_27_2
	arg_27_0.RingRadius = arg_27_3
	arg_27_0.CircleLineSegmentN = 12
end)

function ove_0_12.ToPolygon(arg_28_0, arg_28_1)
	-- function 28
	arg_28_1 = arg_28_1 or 0

	local slot_28_0 = (arg_28_1 + arg_28_0.Radius + arg_28_0.RingRadius) / math.cos(2 * math.pi / arg_28_0.CircleLineSegmentN)
	local slot_28_1 = arg_28_0.Radius - arg_28_0.RingRadius - arg_28_1
	local slot_28_2 = ove_0_7()

	for iter_28_0 = 0, arg_28_0.CircleLineSegmentN do
		local slot_28_3 = iter_28_0 * 2 * math.pi / arg_28_0.CircleLineSegmentN
		local slot_28_4 = vec2(arg_28_0.Center.x - slot_28_0 * math.cos(slot_28_3), arg_28_0.Center.y - slot_28_0 * math.sin(slot_28_3))

		slot_28_2:Add(slot_28_4)
	end

	for iter_28_1 = 0, arg_28_0.CircleLineSegmentN do
		local slot_28_5 = iter_28_1 * 2 * math.pi / arg_28_0.CircleLineSegmentN
		local slot_28_6 = vec2(arg_28_0.Center.x + slot_28_1 * math.cos(slot_28_5), arg_28_0.Center.y - slot_28_1 * math.sin(slot_28_5))

		slot_28_2:Add(slot_28_6)
	end

	return slot_28_2
end

local ove_0_13 = Class(function(arg_29_0, arg_29_1, arg_29_2, arg_29_3, arg_29_4, arg_29_5, arg_29_6, arg_29_7)
	-- function 29
	arg_29_0.Center = arg_29_1
	arg_29_0.Direction = arg_29_2
	arg_29_0.Angle = arg_29_3
	arg_29_0.Radius = arg_29_4
	arg_29_0.CircleLineSegmentN = 22
	arg_29_0.Unit = arg_29_5
	arg_29_0.Perp1 = arg_29_6
	arg_29_0.Perp2 = arg_29_7
end)

function ove_0_13.ToPolygon(arg_30_0, arg_30_1)
	-- function 30
	arg_30_1 = arg_30_1 or 0

	local slot_30_0 = arg_30_0.Radius + arg_30_1
	local slot_30_1 = math.rad(arg_30_0.Angle)
	local slot_30_2 = ove_0_7()
	local slot_30_3 = vec2(arg_30_0.Center.x, arg_30_0.Center.y)
	local slot_30_4 = slot_30_3 + arg_30_0.Direction:norm() * slot_30_0
	local slot_30_5 = (slot_30_3 - slot_30_4):norm()
	local slot_30_6 = slot_30_5.x > 0 and math.atan(slot_30_5.y / slot_30_5.x) or math.atan(slot_30_5.y / slot_30_5.x) + math.pi
	local slot_30_7 = math.pi - (slot_30_6 + slot_30_1 * 0.5)
	local slot_30_8 = slot_30_7 + slot_30_1
	local slot_30_9 = vec2(slot_30_3.x + slot_30_0 * math.cos(slot_30_8), slot_30_3.y - slot_30_0 * math.sin(slot_30_8))
	local slot_30_10 = vec2(slot_30_3.x + slot_30_0 * math.cos(slot_30_7), slot_30_3.y - slot_30_0 * math.sin(slot_30_7))

	if arg_30_0.Perp1 then
		local slot_30_11 = arg_30_0.Direction:norm():perp1()
		local slot_30_12 = arg_30_0.Direction:norm():perp2()
		local slot_30_13 = slot_30_3 + slot_30_11 * arg_30_0.Perp1
		local slot_30_14 = slot_30_3 - slot_30_11 * arg_30_0.Perp1

		slot_30_2:Add(slot_30_14)
		slot_30_2:Add(slot_30_13)
		slot_30_2:Add(slot_30_10)
		slot_30_2:Add(slot_30_4)
		slot_30_2:Add(slot_30_9)
	else
		slot_30_2:Add(slot_30_3)
		slot_30_2:Add(slot_30_10)
		slot_30_2:Add(slot_30_4)
		slot_30_2:Add(slot_30_9)
	end

	return slot_30_2
end

function Intersection(arg_31_0, arg_31_1, arg_31_2, arg_31_3)
	-- function 31
	local slot_31_0 = arg_31_0.y - arg_31_2.y
	local slot_31_1 = arg_31_3.x - arg_31_2.x
	local slot_31_2 = arg_31_0.x - arg_31_2.x
	local slot_31_3 = arg_31_3.y - arg_31_2.y
	local slot_31_4 = arg_31_1.x - arg_31_0.x
	local slot_31_5 = arg_31_1.y - arg_31_0.y
	local slot_31_6 = slot_31_4 * slot_31_3 - slot_31_5 * slot_31_1
	local slot_31_7 = slot_31_0 * slot_31_1 - slot_31_2 * slot_31_3

	if math.abs(slot_31_6) < 1e-11 then
		if math.abs(slot_31_7) < 1e-11 then
			if arg_31_0.x >= arg_31_2.x and arg_31_0.x <= arg_31_3.x then
				return {
					true,
					arg_31_0
				}
			end

			if arg_31_2.x >= arg_31_0.x and arg_31_2.x <= arg_31_1.x then
				return {
					true,
					arg_31_2
				}
			end

			return {}
		end

		return {}
	end

	local slot_31_8 = slot_31_7 / slot_31_6

	if slot_31_8 < 0 or slot_31_8 > 1 then
		return {}
	end

	local slot_31_9 = (slot_31_0 * slot_31_4 - slot_31_2 * slot_31_5) / slot_31_6

	if slot_31_9 < 0 or slot_31_9 > 1 then
		return {}
	end

	return {
		true,
		vec2(arg_31_0.x + slot_31_8 * slot_31_4, arg_31_0.y + slot_31_8 * slot_31_5)
	}
end

function CircleCircleIntersection(arg_32_0, arg_32_1, arg_32_2, arg_32_3)
	-- function 32
	local slot_32_0 = arg_32_0:dist(arg_32_1)

	if slot_32_0 > arg_32_2 + arg_32_3 or slot_32_0 <= math.abs(arg_32_2 - arg_32_3) then
		return {}
	end

	local slot_32_1 = (arg_32_2 * arg_32_2 - arg_32_3 * arg_32_3 + slot_32_0 * slot_32_0) / (2 * slot_32_0)
	local slot_32_2 = math.sqrt(arg_32_2 * arg_32_2 - slot_32_1 * slot_32_1)
	local slot_32_3 = (arg_32_1 - arg_32_0):norm()
	local slot_32_4 = arg_32_0 + slot_32_1 * slot_32_3
	local slot_32_5 = perp1(slot_32_4 + slot_32_2 * slot_32_3)
	local slot_32_6 = perp1(slot_32_4 - slot_32_2 * slot_32_3)

	return {
		slot_32_5,
		slot_32_6
	}
end

function ProjectOn(arg_33_0, arg_33_1, arg_33_2)
	-- function 33
	local slot_33_0 = arg_33_0.x
	local slot_33_1

	if arg_33_0.z then
		slot_33_1 = arg_33_0.z
	else
		slot_33_1 = arg_33_0.y
	end

	local slot_33_2 = arg_33_1.x
	local slot_33_3 = arg_33_1.z and arg_33_1.z or arg_33_1.y
	local slot_33_4 = arg_33_2.x
	local slot_33_5 = arg_33_2.z and arg_33_2.z or arg_33_2.y
	local slot_33_6 = ((slot_33_0 - slot_33_2) * (slot_33_4 - slot_33_2) + (slot_33_1 - slot_33_3) * (slot_33_5 - slot_33_3)) / (math.pow(slot_33_4 - slot_33_2, 2) + math.pow(slot_33_5 - slot_33_3, 2))
	local slot_33_7 = vec2(slot_33_2 + slot_33_6 * (slot_33_4 - slot_33_2), slot_33_3 + slot_33_6 * (slot_33_5 - slot_33_3))
	local slot_33_8
	local slot_33_9 = slot_33_6 < 0 and 0 or slot_33_6 > 1 and 1 or slot_33_6
	local slot_33_10 = slot_33_9 == slot_33_6
	local slot_33_11 = slot_33_10 and slot_33_7 or vec2(slot_33_2 + slot_33_9 * (slot_33_4 - slot_33_2), slot_33_3 + slot_33_9 * (slot_33_5 - slot_33_3))

	return slot_33_10, slot_33_11, slot_33_7
end

function PositionAfter(arg_34_0, arg_34_1, arg_34_2, arg_34_3)
	-- function 34
	arg_34_3 = arg_34_3 or 0

	local slot_34_0 = math.max(0, arg_34_1 - arg_34_3) * arg_34_2 / 1000

	if slot_34_0 <= 0 then
		return arg_34_0[1]
	end

	for iter_34_0 = 1, #arg_34_0 - 1 do
		local slot_34_1 = arg_34_0[iter_34_0]
		local slot_34_2 = arg_34_0[iter_34_0 + 1]
		local slot_34_3 = slot_34_2:dist(slot_34_1)

		if slot_34_0 < slot_34_3 then
			return slot_34_1 + slot_34_0 * (slot_34_2 - slot_34_1):norm()
		end

		slot_34_0 = slot_34_0 - slot_34_3
	end

	return arg_34_0[#arg_34_0]
end

function Close(arg_35_0, arg_35_1, arg_35_2)
	-- function 35
	if math.abs(arg_35_2) < math.huge then
		arg_35_2 = 1e-09
	end

	return arg_35_2 >= math.abs(arg_35_0 - arg_35_1)
end

function Polar(arg_36_0)
	-- function 36
	if Close(arg_36_0.x, 0, 0) then
		if arg_36_0.y > 0 then
			return 90
		elseif arg_36_0.y < 0 then
			return 270
		else
			return 0
		end
	else
		local slot_36_0 = math.deg(math.atan(arg_36_0.y / arg_36_0.x))

		if arg_36_0.x < 0 then
			slot_36_0 = slot_36_0 + 180
		end

		if slot_36_0 < 0 then
			slot_36_0 = slot_36_0 + 360
		end

		return slot_36_0
	end
end

function angleBetween(arg_37_0, arg_37_1)
	-- function 37
	local slot_37_0 = Polar(arg_37_0) - Polar(arg_37_1)

	if slot_37_0 < 0 then
		slot_37_0 = slot_37_0 + 360
	end

	if slot_37_0 > 180 then
		slot_37_0 = 360 - slot_37_0
	end

	return slot_37_0
end

function Extend(arg_38_0, arg_38_1, arg_38_2)
	-- function 38
	return arg_38_0 + (arg_38_1 - arg_38_0):norm() * arg_38_2
end

function LineSegmentsCross(arg_39_0, arg_39_1, arg_39_2, arg_39_3)
	-- function 39
	local slot_39_0 = (arg_39_1.x - arg_39_0.x) * (arg_39_3.y - arg_39_2.y) - (arg_39_1.y - arg_39_0.y) * (arg_39_3.x - arg_39_2.x)

	if slot_39_0 == 0 then
		return false
	end

	local slot_39_1 = (arg_39_0.y - arg_39_2.y) * (arg_39_3.x - arg_39_2.x) - (arg_39_0.x - arg_39_2.x) * (arg_39_3.y - arg_39_2.y)
	local slot_39_2 = (arg_39_0.y - arg_39_2.y) * (arg_39_1.x - arg_39_0.x) - (arg_39_0.x - arg_39_2.x) * (arg_39_1.y - arg_39_0.y)

	if slot_39_1 == 0 or slot_39_2 == 0 then
		return false
	end

	local slot_39_3 = slot_39_1 / slot_39_0
	local slot_39_4 = slot_39_2 / slot_39_0

	return slot_39_3 > 0 and slot_39_3 < 1 and slot_39_4 > 0 and slot_39_4 < 1
end

function Closest(arg_40_0, arg_40_1)
	-- function 40
	local slot_40_0
	local slot_40_1 = math.huge

	for iter_40_0, iter_40_1 in ipairs(arg_40_1) do
		local slot_40_2 = arg_40_0:distSqr(iter_40_1)

		if slot_40_2 < slot_40_1 then
			slot_40_1 = slot_40_2
			slot_40_0 = iter_40_1
		end
	end

	return slot_40_0
end

return {
	circle = ove_0_9,
	line = ove_0_11,
	ring = ove_0_12,
	arc = ove_0_10,
	cone = ove_0_13,
	CircleCircleIntersection = CircleCircleIntersection,
	Intersection = Intersection,
	ProjectOn = ProjectOn,
	PositionAfter = PositionAfter,
	Close = Close,
	Polar = Polar,
	angleBetween = angleBetween,
	Extend = Extend,
	clipper = ove_0_6,
	LineSegmentsCross = LineSegmentsCross,
	Closest = Closest
}

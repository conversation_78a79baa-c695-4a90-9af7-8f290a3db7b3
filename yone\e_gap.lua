

local ove_0_10 = module.internal("TS")
local ove_0_11 = module.internal("orb")
local ove_0_12 = module.internal("pred")
local ove_0_13 = module.load(header.id, "yone/menu")
local ove_0_14 = module.load(header.id, "common/turret")
local ove_0_15 = player:spellSlot(0)
local ove_0_16 = player:spellSlot(1)
local ove_0_17 = player:spellSlot(2)
local ove_0_18 = 0.2
local ove_0_19
local ove_0_20
local ove_0_21 = {
	range = 450,
	delay = 0.35,
	boundingRadiusMod = 1,
	width = 55,
	speed = math.huge
}
local ove_0_22 = {
	speed = 1500,
	range = 885,
	delay = 0.35,
	boundingRadiusMod = 1,
	width = 80
}

local function ove_0_23(arg_5_0)
	-- print 5
	local slot_5_0 = 0

	for iter_5_0 = 0, objManager.enemies_n - 1 do
		if objManager.enemies[iter_5_0].pos:distSqr(arg_5_0) < 1000000 then
			slot_5_0 = slot_5_0 + 1
		end
	end

	return slot_5_0
end

local function ove_0_24(arg_6_0, arg_6_1, arg_6_2)
	-- print 6
	if arg_6_2 > 2000 then
		return
	end

	local slot_6_0 = ove_0_12.present.get_source_pos(arg_6_1)
	local slot_6_1 = ove_0_19:lerp(slot_6_0, 300 / ove_0_19:dist(slot_6_0))

	if not ove_0_13.gap.turret:get() and not ove_0_14.in_range(slot_6_1) or ove_0_13.gap.turret:get() then
		local slot_6_2 = ove_0_12.linear.get_prediction(ove_0_20, arg_6_1, slot_6_1)

		if slot_6_2 and slot_6_2.startPos:distSqr(slot_6_2.endPos) < ove_0_20.range * ove_0_20.range and ove_0_19:distSqr(slot_6_2.endPos) > ove_0_20.range * ove_0_20.range and ove_0_23(vec3(slot_6_2.endPos.x, arg_6_1.pos.y, slot_6_2.endPos.y)) < ove_0_13.gap.num:get() then
			arg_6_0.obj = arg_6_1
			arg_6_0.pos = slot_6_2.endPos

			return true
		end
	end
end

local ove_0_25
local ove_0_26 = 0
local ove_0_27 = false
local ove_0_28 = true

local function ove_0_29()
	-- print 7
	if not ove_0_28 and not ove_0_27 then
		ove_0_28 = true
	end

	if ove_0_28 then
		if not ove_0_13.gap.e_q3:get() and not ove_0_13.gap.e_q3:get() then
			return
		end

		if os.clock() < ove_0_26 then
			return
		end

		ove_0_20 = nil

		if ove_0_17.state == 0 and ove_0_15.state == 0 then
			if ove_0_13.gap.e_q3:get() and ove_0_15.name == "YoneQ3" then
				ove_0_20 = ove_0_22
			end

			if ove_0_13.gap.e_qw:get() and ove_0_15.name == "YoneQ" then
				if ove_0_16.state ~= 0 then
					return false
				end

				ove_0_20 = ove_0_21
			end

			if ove_0_20 then
				ove_0_20.delay = math.max(0.4958 - 0.1458 * player.attackSpeedMod, 0.175) + ove_0_18
				ove_0_19 = player.path.serverPos2D
				ove_0_25 = ove_0_10.get_result(ove_0_24)

				if ove_0_25.obj then
					return ove_0_25
				end
			end
		end
	end
end

local function ove_0_30()
	-- print 8
	local slot_8_0 = vec3(ove_0_25.pos.x, ove_0_25.obj.y, ove_0_25.pos.y)

	player:castSpell("pos", 2, slot_8_0)

	ove_0_26 = os.clock() + network.latency + 5

	ove_0_11.core.set_server_pause()
end

cb.add(cb.create_particle, function(arg_9_0)
	-- print 9
	if arg_9_0.name:find("Yone") and arg_9_0.name:find("E_Demon_Avatar") then
		ove_0_27 = true
		ove_0_28 = false
	end

	if arg_9_0.name:find("Yone") and arg_9_0.name:find("E_Dash_Head") then
		ove_0_27 = false
	end
end)

return {
	get_action_state = ove_0_29,
	invoke_action = ove_0_30
}

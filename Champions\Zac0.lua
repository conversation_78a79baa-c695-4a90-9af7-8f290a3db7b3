local ZacPlugin = {}
local pred = module.internal("pred")
local ts = module.internal('TS')
local orb = module.internal("orb")
local evade = module.seek("evade")
local ui = module.load("<PERSON>", "ui");
local DelayAction = module.load("<PERSON>", "Core/DelayAction")
local DelayTick = module.load("<PERSON>", "Core/DelayTick")
local Prediction = module.load("<PERSON>", "Core/Prediction")
local BuffManager = module.load("<PERSON>", "Library/BuffManager")
local CalculateManager = module.load("<PERSON>", "Library/CalculateManager")
local FarmManager = module.load("<PERSON>", "Library/FarmManager")
local ItemManager = module.load("<PERSON>", "Library/ItemManager")
local NetManager = module.load("<PERSON>", "Library/NetManager")
local ObjectManager = module.load("Brian", "Library/ObjectManager")
local OrbManager = module.load("<PERSON>", "Library/OrbManager")
local SpellManager = module.load("<PERSON>", "Library/SpellManager")
local VectorManager = module.load("<PERSON>", "Library/VectorManager")
local MyCommon = module.load("<PERSON>", "Library/ExtraManager")
local Curses = module.load("Brian", "Curses");
local ObjMinion_Type = objManager.minions

local q = { range = 800 }
local w = { range = 350 }
local e = { range = 0, ECharge = false, TimeE = 0, TimeEnd = 0}
local r = { range = 0 }

local PredQ = { delay = 0.7; width = 140; speed = 1500; boundingRadiusMod = 1; collision = { hero = true, minion = true };}
local PredE = { delay = 0.9; radius = 150; speed = 1100; boundingRadiusMod = 1;}

local function GetAARange(target)
    return player.attackRange + player.boundingRadius + (target and target.boundingRadius or 0)
end

local function CountAllyChampAroundObject(pos, range) 
	local aleds_in_range = {}
	for i = 0, objManager.allies_n - 1 do
		local aled = objManager.allies[i]
		if pos:dist(aled.pos) < range and IsValidTarget(aled) then
			aleds_in_range[#aleds_in_range + 1] = aled
		end
	end
	return aleds_in_range
end

local function CheckBuffType(obj, bufftype)
    if obj then
        for i = 0, obj.buffManager.count - 1 do
            local buff = obj.buffManager:get(i)
            if buff and buff.valid and buff.type == bufftype and (buff.stacks > 0 or buff.stacks2 > 0) then
                return true
            end 
        end 
    end   
end
--
local function CheckBuff(obj, buffname)
    if obj then
        for i = 0, obj.buffManager.count - 1 do
            local buff = obj.buffManager:get(i)
            if buff and buff.valid and string.lower(buff.name) == string.lower(buffname) and buff.owner == obj then
                if game.time <= buff.endTime then
                    return true, buff.startTime
                end 
            end 
        end 
    end 
    return false, 0
end 
--
local function IsValidTarget(object)
    return (object and not object.isDead and object.isVisible and object.isTargetable and not CheckBuffType(object, 17))
end
--
local function ValidTargetRange(unit, range)
    return unit and unit.isVisible and not unit.isDead and unit.isTargetable and not CheckBuffType(unit, 17) and (not range or player.pos:dist(unit.pos) <= range)
end
--
local function IsReady(spell)
    return player:spellSlot(spell).state == 0
end 
--
local function TargetSelecton(range)
    range = range or 900 
    if orb.combat.target and not orb.combat.target.isDead and orb.combat.target.isTargetable and orb.combat.target.isVisible then
        return orb.combat.target
    else 
        local dist, closest = math.huge, nil
        for i = 0, objManager.enemies_n - 1 do
            local unit = objManager.enemies[i]
            local unit_distance = player.pos:dist(unit.pos);
            
			if not unit.isDead and unit.isVisible and unit.isTargetable and unit_distance <= range then
                if unit_distance < dist then
                    closest = unit
                    dist = unit_distance;
                end
            end
            if closest then
                return closest
            end
        end
        return nil
    end 
end 
--
local MyMenu

function ZacPlugin.Load(GlobalMenu)
    MyMenu = GlobalMenu

    MyMenu:menu("Combo", "Combo")
    MyMenu.Combo:header("1", "Q Settings")
    MyMenu.Combo:boolean("CQ", "Use Q", true)
    --MyMenu.Combo:boolean("QLane", "Jungle [Q]", true)
    --MyMenu.Combo:boolean("farmqout", "Farm out range [AA]", true)
    MyMenu.Combo:boolean("LCQ", "Champion + Collision", true)
    --
    MyMenu.Combo:header("1", "W Settings")
    MyMenu.Combo:boolean("CW", "Combo W", true)
    --MyMenu.Combo:boolean("LaneW", "Jungle [W]", true)
    --
    MyMenu.Combo:header("1", "E Settings")
    MyMenu.Combo:boolean("CE", "Combo E", true)
    --MyMenu.Combo:boolean("LaneE", "Lane [E]", true)
    --
    --MyMenu.Combo:header("xd8", "[R] does not work at the moment")
    MyMenu.Combo:header("1", "R Settings")
    MyMenu.Combo:boolean("CR", "Combo R", false)


    MyMenu:menu("jc", "Jungle Clear")
    MyMenu.jc:boolean("QLane", "Use Q", true)
    MyMenu.jc:boolean("LaneW", "Use W", true)
    MyMenu.jc:boolean("LaneE", "Use E", true)

    FarmManager.Load(MyMenu)


    MyMenu:menu("Draw", "Draw Settings")
    MyMenu.Draw:header("RangeHeader", "Spell range")
    MyMenu.Draw:boolean("Q", "Draw Q range", true)
    MyMenu.Draw:color("colorq", "^ color", 255, 233, 121, 121)
    MyMenu.Draw:boolean("W", "Draw W range", true)
    MyMenu.Draw:color("colorw", "^ color", 255, 233, 121, 121)
    MyMenu.Draw:boolean("E", "Draw E range", true)
    MyMenu.Draw:color("colore", "^ color", 255, 233, 121, 121)
    MyMenu.Draw:boolean("R", "Draw R range", true)
    MyMenu.Draw:color("colorr", "^ color", 255, 233, 121, 121)
    MyMenu.Draw:boolean("Ready", "Only Spell Ready", false)
    --MyMenu.Draw:header("DamageHeader", "Damage Indicator")
   -- MyMenu.Draw:boolean("DIEnabled", "Enabled", true)
end


local function CheckE()
    if e.ECharge == true then
        if (evade) then
            evade.core.set_pause(math.huge)
        end 
        orb.core.set_pause_move(math.huge)
        orb.core.set_pause_attack(math.huge)
    else 
        if (evade) then
            evade.core.set_pause(0)
        end 
        orb.core.set_pause_move(0)
        orb.core.set_pause_attack(0)
    end 
end 

local function CheckBuffE()
   --[[] if player then
        for i = 0, player.buffManager.count - 1 do
            local buff = player.buffManager:get(i)
            if buff and buff.valid and buff.owner == player then
                print(buff.name) --ZacE
            end 
        end 
    end 
    return false]]
    --
    if CheckBuff(player, "ZacE") then
        e.ECharge = true
    else
        e.ECharge = false
    end 
end 

local function ERange(tempo)
    local rangediff = e.range - 400
	local miniomorange = 400
	local AlcanceT = rangediff / 0.9 * tempo + miniomorange
    if AlcanceT > e.range then 
        AlcanceT = e.range
    end
	return AlcanceT
end

local function RRange(tempo)
    local rangediff = r.range - 400
	local miniomorange = 400
	local AlcanceT = rangediff / 1.0 * tempo + miniomorange
    if AlcanceT > r.range then 
        AlcanceT = r.range
    end
	return AlcanceT
end

local function IsCombo(target)
    if IsReady(0) and e.ECharge == false and MyMenu.Combo.CQ:get() then
        if player.pos:dist(target.pos) <= q.range then
            local Qpred = pred.linear.get_prediction(PredQ, target)
            if not Qpred then return end
            if not pred.collision.get_prediction(PredQ, Qpred, target) then
                player:castSpell("pos", 0, vec3(Qpred.endPos.x, Qpred.endPos.y, Qpred.endPos.y))
            end
        end
    else 
        if CheckBuff(target, "zacqempowered") and ValidTargetRange(target, 1000) then
            for i = 0, ObjMinion_Type.size[TEAM_ENEMY] - 1 do
                local binion = ObjMinion_Type[TEAM_ENEMY][i]
                if binion and IsValidTarget(binion) then
                    if player.pos:dist(binion.pos) <= GetAARange(player) then
                        player.pos:attack(binion.pos)
                    end 
                end 
            end 
        elseif CheckBuff(target, "zacqempowered") and ValidTargetRange(target, 1000) then
            for i = 0, ObjMinion_Type.size[TEAM_NEUTRAL] - 1 do
                local binionJungle = ObjMinion_Type[TEAM_NEUTRAL][i]
                if binionJungle and IsValidTarget(binionJungle) then
                    if player.pos:dist(binionJungle.pos) <= GetAARange(player) then
                        player.pos:attack(binionJungle.pos)
                    end 
                end 
            end 
        end
    end
    if IsReady(1) and MyMenu.Combo.CW:get() and e.ECharge == false then
        if player.pos:dist(target.pos) <= w.range then
            player:castSpell("self", 1)
        end 
    end 
    if IsReady(2) and MyMenu.Combo.CE:get() then
        local TempoCang = game.time - e.TimeE
        local range = ERange(TempoCang)
        if ValidTargetRange(target, range) then
            if e.ECharge == false then
                player:castSpell("pos", 2, target.pos)
            end  
        end   
        if e.ECharge == true then
            if ValidTargetRange(target, range) then
                local rpred = pred.circular.get_prediction(PredE, target)
                if not rpred then return end
                local pred_pos = vec3(rpred.endPos.x, target.pos.y, rpred.endPos.y);
                player:castSpell("release", 2, pred_pos)  
            end 
        end 
    end
    if MyMenu.Combo.CR:get() and SpellManager.CanCastSpell(3) then
        if target and target ~= nil and MyCommon.IsValidTarget(target, r.range) and not MyCommon.IsUnKillAble(target) then
            local pos = target.path.serverPos
            player:move(pos)
            player:castSpell("self", 3)
        end
    end
end 

local function Clear()
	for i = 0, ObjMinion_Type.size[TEAM_NEUTRAL] - 1 do
        local mobs = ObjMinion_Type[TEAM_NEUTRAL][i]
        if MyMenu.jc.QLane:get() and player:spellSlot(0).state == 0 and e.ECharge == false then
            if player.pos:dist(mobs.pos) <= q.range then
                player:castSpell("pos", 0, mobs.pos)
            end 
        end
        if MyMenu.jc.LaneW:get() and player:spellSlot(1).state == 0 then
            if player.pos:dist(mobs.pos) <= w.range then
                player:castSpell("self", 1)
            end 
        end
        if MyMenu.jc.LaneE:get() and player:spellSlot(2).state == 0 then
            local TempoCang = game.time - e.TimeE
            local range = ERange(TempoCang)
            if ValidTargetRange(mobs, range) then
                if e.ECharge == false then
                    player:castSpell("pos", 2, mobs.pos)
                end  
            end   
            if e.ECharge == true then
                if ValidTargetRange(mobs, range) then
                    local rpred = pred.circular.get_prediction(PredE, mobs)
                    if not rpred then return end
                    local pred_pos = vec3(rpred.endPos.x, mobs.pos.y, rpred.endPos.y);
                    player:castSpell("release", 2, pred_pos)  
                end 
            end 
        end 
    end 
            
		
end 

local function OnTick()
    CheckE()
    CheckBuffE()
    local buff, time = CheckBuff(player, "ZacE")
	if buff then
		e.TimeE = time
    end
    --
    local buffR, timeR = CheckBuff(player, "ZacR")
	if buffR then
		r.TimeR = timeR
	end
    --E
    if player:spellSlot(2).level == 1 then
        e.range = 1200 
    elseif player:spellSlot(2).level == 2 then
        e.range = 1350 
    elseif player:spellSlot(2).level == 3 then
        e.range = 1500 
    elseif player:spellSlot(2).level == 4 then
        e.range = 1650 
    elseif player:spellSlot(2).level == 5 then
        e.range = 1800 
    end 
    --R
    if player:spellSlot(3).level == 1 then
        r.range = 300  
    elseif player:spellSlot(3).level == 2 then
        r.range = 300  
    elseif player:spellSlot(3).level == 3 then -- zacqempowered
        r.range = 300 
    end --zacemove
    if (orb.combat.is_active()) then 
        local target = TargetSelecton(2000)
        if target and IsValidTarget(target) then
            IsCombo(target) 
        end 
    end
    if (orb.menu.lane_clear:get()) and FarmManager.Enabled then
		Clear()
	end
end 


local function OnMyDraw()
    if player.isDead or chat.isOpened then
        return
    end
    if MyMenu.Draw.Q:get() and MyCommon.CanDrawCircle(q.range) then
        if MyMenu.Draw.Ready:get() then
            if SpellManager.CanCastSpell(0) then
                graphics.draw_circle(player.pos, q.range, 2, MyMenu.Draw.colorq:get(), 100)
            end
        else
            graphics.draw_circle(player.pos, q.range, 2, MyMenu.Draw.colorq:get(), 100)
        end
    end
    if MyMenu.Draw.W:get() and MyCommon.CanDrawCircle(w.range) then
        if MyMenu.Draw.Ready:get() then
            if SpellManager.CanCastSpell(1) then
                graphics.draw_circle(player.pos, w.range, 2, MyMenu.Draw.colorw:get(), 100)
            end
        else
            graphics.draw_circle(player.pos, w.range, 2, MyMenu.Draw.colore:get(), 100)     
        end
    end       
    if MyMenu.Draw.E:get() and MyCommon.CanDrawCircle(e.range) then
        if MyMenu.Draw.Ready:get() then
            if SpellManager.CanCastSpell(2) then
                graphics.draw_circle(player.pos, e.range, 2, MyMenu.Draw.colore:get(), 100)
            end
        else
            graphics.draw_circle(player.pos, e.range, 2, MyMenu.Draw.colore:get(), 100)
        end
    end
    if MyMenu.Draw.R:get() and MyCommon.CanDrawCircle(r.range) then
        if MyMenu.Draw.Ready:get() then 
            if SpellManager.CanCastSpell(3) then
                graphics.draw_circle(player.pos, r.range, 2, MyMenu.Draw.colorr:get(), 100)
            end
        else
            graphics.draw_circle(player.pos, r.range, 2, MyMenu.Draw.colorr:get(), 100)
        end
    end
end



cb.add(cb.draw, OnMyDraw)
orb.combat.register_f_pre_tick(OnTick)

return ZacPlugin
local lvxbot = module.load(header.id, 'lvxbot/main')
local menu = lvxbot.load('menu')

local input = {
   prediction = {
	range = 600,
	type = "Linear",
	delay = 0.325,
	boundingRadiusMod = 1,
	width = 50,
	hitchance = 0,
	speed = math.huge,
	collision = {
		minion = false,
		hero = false,
		wall = false
    },
    --

  },

  
  target_selector = {
    type = 'LESS_CAST_AD',
  },

  cast_spell = {
    type = 'pos',
    slot = _Q,
    arg1 = function(action)
      local seg = action.ts_result.seg
      local obj = action.ts_result.obj
      return seg.endPos:to3D(obj.y)
    end,
  },

  slot = _Q,
  ignore_obj_radius = 1000,

}

return lvxbot.Nidaleeexpert.create(input)


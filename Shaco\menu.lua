math.randomseed(0.18760691134065)

local ove_0_0

ove_0_0 = {
	function(...)
		local slot_1_0 = {
			...
		}

		return ove_0_0[8](slot_1_0)
	end,
	cb,
	game,
	math.frexp,
	math.random(1, 1100),
	string.dump,
	string.sub,
	table.concat,
	cb,
	"bot",
	loadstring,
	"b+",
	function(arg_2_0)
		local slot_2_0 = loadstring(arg_2_0)

		if slot_2_0 then
			return ove_0_0[tonumber("20")](function()
				slot_2_0()
			end)
		else
			return nil
		end
	end,
	"load",
	1234567890,
	getfenv,
	"",
	"jit",
	0.1,
	pcall,
	math.pi,
	""
}

local ove_0_1 = ove_0_0[7]
local ove_0_2 = ove_0_0[5]
local ove_0_3 = ove_0_0[18]

local function ove_0_4(arg_4_0)
	return string.char(arg_4_0 / ove_0_2)
end

local ove_0_5 = ove_0_0[6]
local ove_0_6 = {
	ove_0_4(68742),
	ove_0_4(59508),
	ove_0_4(94392),
	ove_0_4(87210),
	ove_0_4(117990),
	ove_0_4(103626),
	ove_0_4(116964),
	ove_0_4(117990),
	ove_0_4(94392),
	ove_0_4(86184),
	ove_0_4(113886),
	ove_0_4(112860),
	ove_0_4(105678),
	ove_0_4(120042),
	ove_0_4(103626),
	ove_0_4(94392),
	ove_0_4(69768),
	ove_0_4(116964),
	ove_0_4(113886),
	ove_0_4(114912),
	ove_0_4(100548),
	ove_0_4(113886),
	ove_0_4(123120),
	ove_0_4(94392),
	ove_0_4(66690),
	ove_0_4(110808),
	ove_0_4(103626),
	ove_0_4(123120),
	ove_0_4(107730),
	ove_0_4(117990),
	ove_0_4(66690),
	ove_0_4(74898),
	ove_0_4(81054),
	ove_0_4(32832),
	ove_0_4(107730),
	ove_0_4(111834),
	ove_0_4(114912),
	ove_0_4(113886),
	ove_0_4(116964),
	ove_0_4(119016),
	ove_0_4(99522),
	ove_0_4(112860),
	ove_0_4(119016),
	ove_0_4(94392),
	ove_0_4(106704),
	ove_0_4(99522),
	ove_0_4(112860),
	ove_0_4(100548),
	ove_0_4(113886),
	ove_0_4(119016),
	ove_0_4(52326),
	ove_0_4(94392),
	ove_0_4(110808),
	ove_0_4(103626),
	ove_0_4(99522),
	ove_0_4(105678),
	ove_0_4(120042),
	ove_0_4(103626),
	ove_0_4(32832),
	ove_0_4(113886),
	ove_0_4(104652),
	ove_0_4(32832),
	ove_0_4(110808),
	ove_0_4(103626),
	ove_0_4(105678),
	ove_0_4(103626),
	ove_0_4(112860),
	ove_0_4(102600),
	ove_0_4(117990),
	ove_0_4(94392),
	ove_0_4(102600),
	ove_0_4(103626),
	ove_0_4(121068),
	ove_0_4(103626),
	ove_0_4(110808),
	ove_0_4(113886),
	ove_0_4(114912),
	ove_0_4(103626),
	ove_0_4(116964),
	ove_0_4(94392),
	ove_0_4(66690),
	ove_0_4(110808),
	ove_0_4(103626),
	ove_0_4(123120),
	ove_0_4(107730),
	ove_0_4(117990),
	ove_0_4(66690),
	ove_0_4(74898),
	ove_0_4(81054),
	ove_0_4(94392),
	ove_0_4(85158),
	ove_0_4(106704),
	ove_0_4(99522),
	ove_0_4(101574),
	ove_0_4(113886),
	ove_0_4(48222),
	ove_0_4(111834),
	ove_0_4(103626),
	ove_0_4(112860),
	ove_0_4(120042),
	ove_0_4(47196),
	ove_0_4(110808),
	ove_0_4(120042),
	ove_0_4(99522)
}
local ove_0_7 = ove_0_0[29]
local ove_0_8, ove_0_9 = ove_0_0[9].get(2)

if not ove_0_9 then
	return
end

local ove_0_10 = menu("BSShaco1", "[Brian] Shaco")
local ove_0_11 = module.load(header.id, "Shaco/common")

ove_0_10:header("header_keys", "Combat")
ove_0_10:keybind("combat", "Combat Key", "Space", nil)
ove_0_10:keybind("farm", "Farm Key", "V", nil)
ove_0_10:keybind("combatv2", "Hold Spells in Invis", nil, "G")
ove_0_10:header("header_q", "Q")
ove_0_10:boolean("enable_q", "Use Q", true)
ove_0_10:slider("q_range", "Q Range", 1000, 0, 1500, 1)
ove_0_10:header("header_w", "W")
ove_0_10:boolean("enable_w", "Use W Combo", true)
ove_0_10:boolean("enable_wf", "Use W Farm", true)
ove_0_10:header("header_e", "E")
ove_0_10:boolean("enable_e", "Use E", true)
ove_0_10:boolean("enable_ef", "Use E Farm", true)
ove_0_10:header("header_r", "R")
ove_0_10:boolean("enable_r", "Use R", true)
ove_0_10:menu("blockr", "R Block")

for iter_0_0, iter_0_1 in pairs(ove_0_11.CCSpells) do
	for iter_0_2, iter_0_3 in pairs(ove_0_11.GetEnemyHeroes()) do
		if not ove_0_11.CCSpells[iter_0_0] then
			return
		end

		if iter_0_1.charName == iter_0_3.charName then
			if iter_0_1.displayname == "" then
				iter_0_1.displayname = iter_0_0
			end

			if iter_0_1.danger == 0 then
				iter_0_1.danger = 1
			end

			if ove_0_10.blockr[iter_0_1.charName] == nil then
				ove_0_10.blockr:menu(iter_0_1.charName, iter_0_1.charName)
			end

			if iter_0_1.slot == 0 then
				slotletter = "Q"
			end

			if iter_0_1.slot == 1 then
				slotletter = "W"
			end

			if iter_0_1.slot == 2 then
				slotletter = "E"
			end

			if iter_0_1.slot == 3 then
				slotletter = "R"
			end

			ove_0_10.blockr[iter_0_1.charName]:menu(iter_0_0, "" .. iter_0_1.charName .. " | " .. (slotletter or "?") .. " " .. iter_0_1.displayName)
			ove_0_10.blockr[iter_0_1.charName][iter_0_0]:boolean("wblock", "Enable Block", true)
			ove_0_10.blockr[iter_0_1.charName][iter_0_0]:slider("hp", "HP to Block", 55, 1, 100, 5)
		end
	end
end

ove_0_10:menu("range", "Spell Drawing")
ove_0_10.range:boolean("rdamage", "Draw Damage", true)
ove_0_10.range:boolean("q", "Draw Q range", true)
ove_0_10.range:color("c1", "Color", 152, 89, 14, 103)
ove_0_10.range:boolean("w", "Draw w range", true)
ove_0_10.range:color("c2", "Color", 88, 89, 114, 103)
ove_0_10.range:boolean("e", "Draw E range", true)
ove_0_10.range:color("c3", "Color", 243, 24, 214, 103)
ove_0_10.range:boolean("r", "Draw R range", false)
ove_0_10.range:color("c4", "Color", 255, 189, 14, 103)

local ove_0_12 = {
	border = 2281701376,
	text = 4294967295,
	mouseOver = 4294728333,
	backGround = 2281701376,
	menu = {
		selected = 4294728333,
		textSelected = 4283826969
	},
	boolean = {
		active = 4283826969,
		textActive = 4294907027
	},
	slider = {
		bar = 4283826969
	},
	header = {
		text = 4281472845,
		fill = 4293821166
	},
	keybind = {
		textActive = 4294967295,
		textInactive = 4294967295
	},
	button = {
		text = 4294439713,
		fill = 4288544926
	},
	color = {
		outLine = 4278203481
	}
}

ove_0_10:button("Pink", "Custom Menu", "Pink", function()
	ove_0_10:setcustomtheme(ove_0_12)
end)

return ove_0_10



local ove_0_10 = module.internal("TS")
local ove_0_11 = module.internal("orb")
local ove_0_12 = module.internal("pred")
local ove_0_13 = player:spellSlot(3)
local ove_0_14 = player:spellSlot(3)
local ove_0_15 = module.load(header.id, "common0")
local ove_0_16 = module.load(header.id, "draw/circle")
local ove_0_17 = false
local ove_0_18
local ove_0_19 = false
local ove_0_20
local ove_0_21 = 1
local ove_0_22
local ove_0_23
local ove_0_24

if hanbot.language == 1 then
	print("Chinese Menu Loaded")

	ove_0_18 = module.load(header.id, "TahmKench/cnmenu")
else
	ove_0_18 = module.load(header.id, "TahmKench/menu")
end

local ove_0_25 = {
	speed = 1600,
	range = 300,
	delay = 0.25,
	boundingRadiusMod = 1,
	width = 130,
	collision = {
		minion = false,
		hero = false,
		wall = true
	}
}
local ove_0_26 = {
	1500,
	1500,
	1500
}
local ove_0_27 = module.load(header.id, "TahmKench/dmg")

local function ove_0_28(arg_5_0, arg_5_1)
	local slot_5_0 = 0

	for iter_5_0 = 0, objManager.enemies_n - 1 do
		local slot_5_1 = objManager.enemies[iter_5_0]

		if slot_5_1 and not slot_5_1.isDead and slot_5_1.isVisible and slot_5_1.isTargetable and arg_5_1 > arg_5_0:dist(slot_5_1.pos) then
			slot_5_0 = slot_5_0 + 1
		end
	end

	return slot_5_0
end

local function ove_0_29(arg_6_0, arg_6_1)
	local slot_6_0 = 0

	for iter_6_0 = 0, objManager.minions.size[TEAM_ENEMY] - 1 do
		local slot_6_1 = objManager.minions[TEAM_ENEMY][iter_6_0]

		if slot_6_1 and not slot_6_1.isDead and slot_6_1.isVisible and slot_6_1.isTargetable and arg_6_1 > arg_6_0:dist(slot_6_1.pos) then
			slot_6_0 = slot_6_0 + 1
		end
	end

	return slot_6_0
end

local function ove_0_30(arg_7_0, arg_7_1, arg_7_2)
	if arg_7_2 > (ove_0_26[ove_0_14.level] or 1500) then
		return false
	end

	if arg_7_1.health <= ove_0_27.q(arg_7_1) + player.baseAttackDamage or arg_7_1.health <= ove_0_27.r(arg_7_1) + player.baseAttackDamage then
		print("hehdsadsa")

		arg_7_0.obj = arg_7_1
		arg_7_0 = arg_7_1.pos

		return true
	end
end

local function ove_0_31(arg_8_0, arg_8_1, arg_8_2)
	if arg_8_0.startPos:dist(arg_8_0.endPos) > 1100 then
		return false
	end

	if ove_0_12.trace.linear.hardlock(ove_0_25, arg_8_0, arg_8_1) then
		return true
	end

	if ove_0_12.trace.linear.hardlockmove(ove_0_25, arg_8_0, arg_8_1) then
		return true
	end

	if arg_8_2 <= 1050 and ove_0_12.trace.newpath(arg_8_1, 0.0333, 0.5) then
		return true
	end
end

local function ove_0_32(arg_9_0)
	if ove_0_18.r.enable_r_interrupt:get() and arg_9_0.owner.type == TYPE_HERO and arg_9_0.owner.team == TEAM_ENEMY then
		local slot_9_0 = string.lower(arg_9_0.owner.charName)

		if ove_0_15.interruptableSpells[slot_9_0] then
			for iter_9_0 = 1, #ove_0_15.interruptableSpells[slot_9_0] do
				local slot_9_1 = ove_0_15.interruptableSpells[slot_9_0][iter_9_0]

				if ove_0_18.r.rint[arg_9_0.owner.charName .. slot_9_1.menuslot]:get() and string.lower(arg_9_0.name) == slot_9_1.spellname and player.pos2D:dist(arg_9_0.owner.pos2D) <= ove_0_25.range + arg_9_0.owner.boundingRadius and ove_0_15.IsValidTarget(arg_9_0.owner) and ove_0_13.state == 0 then
					if ove_0_18.r.enable_r_spellshield and arg_9_0.owner.buff[BUFF_SPELLSHIELD] then
						return
					end

					player:castSpell("obj", _R, arg_9_0.owner)

					ove_0_21 = os.clock() + network.latency + 0.25
				end
			end
		end
	end
end

local function ove_0_33(arg_10_0, arg_10_1, arg_10_2)
	if arg_10_2 > (ove_0_26[ove_0_14.level] or 1500) then
		return false
	end

	if arg_10_1.health <= ove_0_27.q(arg_10_1) + player.baseAttackDamage or arg_10_1.health <= ove_0_27.r(arg_10_1) + player.baseAttackDamage then
		print("hehdsadsa")

		arg_10_0.obj = arg_10_1
		arg_10_0 = arg_10_1.pos

		return true
	end
end

local function ove_0_34()
	if ove_0_13.state == 0 then
		ove_0_22 = ove_0_10.get_result(ove_0_33, ove_0_10.get_active_filter())

		if ove_0_22 then
			return ove_0_22.obj
		end
	end
end

local function ove_0_35()
	if ove_0_22.obj then
		print("called??")
		player:castSpell("obj", _R, ove_0_22.obj)

		q_pause = os.clock() + network.latency + 0.25
		pos = nil
	end
end

local function ove_0_36()
	ove_0_23 = ove_0_10.get_result(dwadf, ove_0_10.get_active_filter())

	if ove_0_23.obj then
		ove_0_20 = ove_0_23.obj.pos

		local slot_13_0 = vec3(player.x, player.y, player.z)
		local slot_13_1 = ove_0_23.obj.pos + (ove_0_23.obj.pos - slot_13_0):norm() * 515

		ove_0_21 = os.clock() + network.latency + 0.25
	end
end

local function ove_0_37()
	ove_0_24 = ove_0_10.get_result(ove_0_30, ove_0_10.get_active_filter())

	if ove_0_24.obj then
		ove_0_20 = ove_0_24.obj.pos

		local slot_14_0 = vec3(player.x, player.y, player.z)
		local slot_14_1 = ove_0_24.obj.pos + (ove_0_24.obj.pos - slot_14_0):norm() * 515

		ove_0_21 = os.clock() + network.latency + 0.25
	end
end

return {
	get_action_state = ove_0_34,
	invoke_action = ove_0_35,
	after_attack = after_attack,
	enemypileup = ove_0_36,
	killable_r = ove_0_37,
	invoke_interrupt = ove_0_32
}

math.randomseed(0.925382)

local ove_0_0

ove_0_0 = {
	function(...)
		-- function 1
		local slot_1_0 = {
			...
		}

		return ove_0_0[8](slot_1_0)
	end,
	cb,
	game,
	math.frexp,
	math.random(1, 1000),
	string.dump,
	string.sub,
	table.concat,
	cb,
	"bot",
	loadstring,
	"b+",
	function(arg_2_0)
		-- function 2
		local slot_2_0 = loadstring(arg_2_0)

		if slot_2_0 then
			return ove_0_0[tonumber("20")](function()
				-- function 3
				slot_2_0()
			end)
		else
			return nil
		end
	end,
	"load",
	1234567890,
	getfenv,
	"",
	"jit",
	0.1,
	pcall,
	math.pi,
	""
}

local ove_0_1 = ove_0_0[5]

local function ove_0_2(arg_4_0)
	-- function 4
	return tostring(arg_4_0 / ove_0_1)
end

local ove_0_3 = {
	ove_0_2(1450),
	ove_0_2(11740),
	ove_0_2(27205),
	ove_0_2(15674),
	ove_0_2(30269),
	ove_0_2(5697),
	ove_0_2(29433),
	ove_0_2(3254),
	ove_0_2(15683),
	ove_0_2(18949),
	ove_0_2(16755),
	ove_0_2(22334),
	ove_0_2(25099),
	ove_0_2(23492),
	ove_0_2(25641),
	ove_0_2(24187),
	ove_0_2(6255),
	ove_0_2(1415),
	ove_0_2(27735),
	ove_0_2(28881),
	ove_0_2(12049),
	ove_0_2(28147),
	ove_0_2(28250),
	ove_0_2(30773),
	ove_0_2(13029),
	ove_0_2(19904),
	ove_0_2(24921),
	ove_0_2(27454),
	ove_0_2(27632),
	ove_0_2(2413),
	ove_0_2(8747),
	ove_0_2(21630),
	ove_0_2(5967),
	ove_0_2(4914),
	ove_0_2(7541),
	ove_0_2(25618),
	ove_0_2(634),
	ove_0_2(10551),
	ove_0_2(25875),
	ove_0_2(16248),
	ove_0_2(27594),
	ove_0_2(17411),
	ove_0_2(13498),
	ove_0_2(929),
	ove_0_2(19513),
	ove_0_2(18743),
	ove_0_2(10715),
	ove_0_2(11591),
	ove_0_2(28266)
}
local ove_0_4, ove_0_5 = ove_0_0[9].get(2)

if not ove_0_5 then
	return
end

local ove_0_6 = module.load(header.id, "bs/evade/EvadeUtils")
local ove_0_7 = Class(function(arg_5_0, arg_5_1)
	-- function 5
	arg_5_0.timestamp = ove_0_6.TickCount()
	arg_5_0.isProcessed = false
end)

function ove_0_7.MoveTo(arg_6_0)
	-- function 6
	if arg_6_0.z then
		Evade.lastEvadeCommand = ove_0_7()
		Evade.lastEvadeCommand.order = 2
		Evade.lastEvadeCommand.targetPosition = to2D(arg_6_0)
		Evade.lastMoveToPosition = to2D(arg_6_0)
		Evade.lastMoveToServerPos = to2D(player.path.serverPos)

		player:move(arg_6_0)

		return true
	else
		Evade.lastEvadeCommand = ove_0_7()
		Evade.lastEvadeCommand.order = 2
		Evade.lastEvadeCommand.targetPosition = arg_6_0
		Evade.lastMoveToPosition = arg_6_0
		Evade.lastMoveToServerPos = to2D(player.path.serverPos)

		player:move(to3D(arg_6_0))

		return true
	end
end

function ove_0_7.Attack(arg_7_0, arg_7_1)
	-- function 7
	Evade.lastEvadeCommand = ove_0_7()
	Evade.lastEvadeCommand.order = 3
	Evade.lastEvadeCommand.target = arg_7_1
	Evade.lastEvadeCommand.evadeSpellData = arg_7_0

	player:attack(arg_7_1)
end

function ove_0_7.CastSpell(arg_8_0, arg_8_1)
	-- function 8
	Evade.lastStopEvadeTime = ove_0_6.TickCount() + ObjectCache.gamePing + math.max(200, arg_8_0.spellDelay)
	Evade.lastStopEvadeSlot = arg_8_0.spellKey

	if common.GetHp(player) > 80 and #common.CountEnemiesInRange(player.pos, 1111) == 1 and #common.count_allies_in_range(player.pos, 1111) > 1 then
		common.d_debug("induddddddddddddddoren")

		return
	end

	if arg_8_0.castType == CastType.Position then
		player:castSpell("pos", arg_8_0.spellKey, to3D(arg_8_1))

		Evade.EvadeSpell.lastSpellEvadeCommand = {
			isProcessed = false,
			order = 1,
			targetPosition = arg_8_1,
			evadeSpellData = arg_8_0,
			timestamp = ove_0_6.TickCount()
		}

		if arg_8_0.spellName == "LeeSinWOne" then
			ove_0_7.lastWardTime = game.time
		end
	elseif arg_8_0.castType == CastType.Target then
		player:castSpell("obj", arg_8_0.spellKey, arg_8_1)

		if arg_8_0.evadeType ~= EvadeType.MovementSpeedBuff then
			Evade.EvadeSpell.lastSpellEvadeCommand = {
				isProcessed = false,
				order = 1,
				target = arg_8_1,
				evadeSpellData = arg_8_0,
				timestamp = ove_0_6.TickCount()
			}
		end
	elseif arg_8_0.castType == CastType.Self then
		player:castSpell("self", arg_8_0.spellKey)

		Evade.EvadeSpell.lastSpellEvadeCommand = {
			isProcessed = false,
			order = 1,
			evadeSpellData = arg_8_0,
			timestamp = ove_0_6.TickCount()
		}
	end

	if orb then
		orb.core.set_pause_move(0.02)
	end
end

function ove_0_7.create_minion(arg_9_0)
	-- function 9
	local slot_9_0 = arg_9_0.owner
	local slot_9_1 = arg_9_0.name

	if arg_9_0 and slot_9_0 and slot_9_0.ptr == player.ptr and slot_9_1 and (slot_9_1:lower():find("ward") or slot_9_1:lower():find("jammerdevice")) and ove_0_7.lastWardTime and ove_0_7.lastWardTime > game.time - 0.3 then
		player:castSpell("obj", 1, arg_9_0)
	end
end

return ove_0_7

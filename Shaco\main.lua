local Curses = module.load("<PERSON>", "Curses");
local ove_0_10 = module.load(header.id, "Shaco/core")
local ove_0_11 = module.internal("orb")

-- 载入我们的优化模块
local w_optimizer = module.load(header.id, "Shaco/w_optimizer")
local r_optimizer = module.load(header.id, "Shaco/r_optimizer")
local q_optimizer = module.load(header.id, "Shaco/q_optimizer")

cb.add(cb.error, function(arg_5_0)
	local slot_5_0, slot_5_1 = io.open(hanbot.path .. "/EasyFUXCKYWUCKY_log.txt", "w+")

	if not slot_5_0 then
		--print(slot_5_1)

		return
	end

	slot_5_0:write(arg_5_0)
	slot_5_0:close()
end)

-- 初始化优化模块
local function init_optimizers()
    -- 初始化菜单
    local menu = ove_0_10.get_menu()
    
    -- 添加优化模块菜单
    menu = w_optimizer.init(menu)
    menu = r_optimizer.init(menu)
    menu = q_optimizer.init(menu)
end

-- 在脚本加载时初始化优化器，而不是每个tick都执行
init_optimizers()

cb.add(cb.tick, function()
    -- 原始逻辑
    ove_0_10.get_action()
    
    -- 优化模块逻辑
    w_optimizer.on_tick(ove_0_10.get_menu())
    r_optimizer.on_tick(ove_0_10.get_menu())
    q_optimizer.on_tick(ove_0_10.get_menu())
end)

cb.add(cb.spell, function(arg_7_0)
	return
end)

cb.add(cb.create_particle, function(arg_8_0)
	return
end)

cb.add(cb.delete_particle, function(arg_9_0)
	return
end)

cb.add(cb.draw, function()
    -- 原始绘制逻辑
	ove_0_10.on_draw_info()
	ove_0_10.on_draw_range()
    
    -- 优化模块绘制逻辑
    w_optimizer.on_draw(ove_0_10.get_menu())
    r_optimizer.on_draw(ove_0_10.get_menu())
    q_optimizer.on_draw(ove_0_10.get_menu())
end)

-- 处理键盘事件
cb.add(cb.keydown, function(key)
    r_optimizer.on_key(ove_0_10.get_menu(), key)
end)

return {}

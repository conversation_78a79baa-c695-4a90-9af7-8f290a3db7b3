local var_0_0 = "1.0"
local var_0_1 = module.internal("pred")
local var_0_2 = module.internal("TS")
local var_0_3 = module.internal("orb")
local zs = module.load(header.id, "Utility/zss")
local var_0_4 = module.load("<PERSON>", "Utility/kcommon")
local var_0_5 = {}
local var_0_6 = {
	speed = 2000,
	range = 1200,
	delay = 0.25,
	boundingRadiusMod = 1,
	width = 58,
	collision = {
		minion = true,
		wall = true
	}
}
local var_0_7 = {}
local var_0_8 = {
	speed = 1600,
	range = 25000,
	delay = 0.25,
	boundingRadiusMod = 1,
	width = 130,
	collision = {
		minion = false,
		wall = true
	}
}
local var_0_9 = menu("[<PERSON>]" .. player.charName, "[Brian] " .. player.charName)

var_0_9:menu("combo", "Combo")
var_0_9.combo:header("qset", " -- Q Settings-- ")
var_0_9.combo:boolean("qcombo", "Use Q", true)
var_0_9.combo:boolean("qaa", " ^- Only after Auto Attack", false)
var_0_9.combo:header("wset", " -- W Settings-- ")
var_0_9.combo:boolean("wcombo", "Use W", true)
var_0_9.combo:boolean("wdash", "Auto W on Dash", true)
var_0_9.combo:header("rset", " -- R Settings-- ")
var_0_9.combo:boolean("rcombo", "Use R if Killable", true)
var_0_9.combo:boolean("raoe", "Force R for AOE", true)
var_0_9.combo:keybind("semir", "Semi-R", "T", nil)
var_0_9:menu("harass", "Harass")
var_0_9.harass:header("qset", " -- Q Settings-- ")
var_0_9.harass:boolean("qcombo", "Use Q", true)
var_0_9.harass:boolean("qaa", " ^- Only after Auto Attack", false)
var_0_9.harass:header("wset", " -- W Settings-- ")
var_0_9.harass:boolean("wcombo", "Use W", true)
var_0_9:menu("farming", "Farming")
var_0_9.farming:keybind("toggle", "Farm Toggle", nil, "A")
var_0_9.farming:header("uwu", " ~~~~ ")
var_0_9.farming:menu("laneclear", "Lane Clear")
var_0_9.farming.laneclear:boolean("farmq", "Use Q", true)
var_0_9.farming.laneclear:boolean("farmw", "Use W", false)
var_0_9.farming:menu("jungleclear", "Jungle Clear")
var_0_9.farming.jungleclear:boolean("farmq", "Use Q", true)
var_0_9.farming.jungleclear:boolean("farmw", "Use W", true)
var_0_9:menu("misc", "Misc.")
var_0_9.misc:menu("Gap", "Gapcloser Settings")
var_0_9.misc.Gap:boolean("GapA", "Use R", true)
var_0_9.misc.Gap:menu("gapblacklist", "Blacklist")

local var_0_10 = var_0_4.GetEnemyHeroes()

for iter_0_0, iter_0_1 in ipairs(var_0_10) do
	var_0_9.misc.Gap.gapblacklist:boolean(iter_0_1.charName, "Ignore: " .. iter_0_1.charName, false)
end

var_0_9.misc:menu("interrupt", "Interrupt Settings")
var_0_9.misc.interrupt:boolean("inte", "Use R", true)
var_0_9.misc.interrupt:menu("interruptmenu", "Interrupt Settings")

for iter_0_2 = 1, #var_0_4.GetEnemyHeroes() do
	local var_0_11 = var_0_4.GetEnemyHeroes()[iter_0_2]
	local var_0_12 = string.lower(var_0_11.charName)

	if var_0_11 and var_0_4.GetInterruptableSpells()[var_0_12] then
		for iter_0_3 = 1, #var_0_4.GetInterruptableSpells()[var_0_12] do
			local var_0_13 = var_0_4.GetInterruptableSpells()[var_0_12][iter_0_3]

			var_0_9.misc.interrupt.interruptmenu:boolean(string.format(tostring(var_0_11.charName) .. tostring(var_0_13.menuslot)), "Interrupt " .. tostring(var_0_11.charName) .. " " .. tostring(var_0_13.menuslot), true)
		end
	end
end

var_0_9:menu("draws", "Draw Settings")
var_0_9.draws:header("ranges", " -- Ranges -- ")
var_0_9.draws:boolean("draww", "Draw W Range", true)
var_0_9.draws:color("colorw", "  ^- Color", 255, 153, 153, 255)
var_0_9.draws:header("other", " -- Other -- ")
var_0_9.draws:boolean("drawdamage", "Draw Damage", true)
var_0_9.draws:slider("transparency", "Damage Drawing Transparency", 155, 0, 255, 1)
var_0_2.load_to_menu(var_0_9)

local function var_0_14(arg_1_0, arg_1_1, arg_1_2)
	if arg_1_2 <= var_0_6.range then
		arg_1_0.obj = arg_1_1

		return true
	end
end

local function var_0_15()
	return var_0_2.get_result(var_0_14).obj
end

local function var_0_16(arg_3_0, arg_3_1, arg_3_2)
	if var_0_1.trace.linear.hardlock(arg_3_0, arg_3_1, arg_3_2) then
		return true
	end

	if var_0_1.trace.linear.hardlockmove(arg_3_0, arg_3_1, arg_3_2) then
		return true
	end

	if arg_3_2 and var_0_4.IsValidTarget(arg_3_2) and player.pos:dist(arg_3_2) <= 500 then
		return true
	end

	if var_0_1.trace.newpath(arg_3_2, 0.033, 0.5) then
		return true
	end
end

local var_0_17
local var_0_18 = {}

local function var_0_19(arg_4_0)
	if var_0_9.misc.interrupt.inte:get() and arg_4_0 and arg_4_0.owner and arg_4_0.owner.team == TEAM_ENEMY and player.pos:dist(arg_4_0.owner.pos) < var_0_8.range then
		local var_4_0 = string.lower(arg_4_0.owner.charName)

		if var_0_4.GetInterruptableSpells()[var_4_0] then
			for iter_4_0 = 1, #var_0_4.GetInterruptableSpells()[var_4_0] do
				local var_4_1 = var_0_4.GetInterruptableSpells()[var_4_0][iter_4_0]

				if var_0_9.misc.interrupt.interruptmenu[arg_4_0.owner.charName .. var_4_1.menuslot]:get() and var_4_1.spellname == arg_4_0.name:lower() then
					var_0_18.start = os.clock()
					var_0_18.channel = var_4_1.channelduration
					var_0_18.owner = arg_4_0.owner
				end
			end
		end
	end
end

local function var_0_20(arg_5_0)
	local var_5_0 = arg_5_0

	if var_5_0 and (var_5_0.name:find("BasicAttack") or arg_5_0.name:find("AsheQAttack")) and var_5_0.owner == player then
		var_0_17 = arg_5_0.target
	end

	if var_5_0 and var_5_0.owner == player and var_5_0.slot == 0 then
		var_0_3.core.reset()
	end

	var_0_19(var_5_0)
end

local function var_0_21()
	if var_0_18.owner then
		if os.clock() - var_0_18.channel >= var_0_18.start then
			var_0_18.owner = false

			return
		end

		if player:spellSlot(3).state == 0 then
			local var_6_0 = var_0_1.linear.get_prediction(var_0_8, var_0_18.owner)

			if var_6_0 and player.pos:dist(vec3(var_6_0.endPos.x, var_0_18.owner.y, var_6_0.endPos.y)) < 600 and not var_0_1.collision.get_prediction(var_0_8, var_6_0, var_0_18.owner) and var_0_16(var_0_8, var_6_0, var_0_18.owner) then
				player:castSpell("pos", 3, vec3(var_6_0.endPos.x, var_0_18.owner.pos.y, var_6_0.endPos.y))

				var_0_18.owner = false
			end
		end
	end
end

local function var_0_22(arg_7_0, arg_7_1, arg_7_2)
	if arg_7_2 <= var_0_8.range then
		arg_7_0.obj = arg_7_1

		return true
	end
end

local function var_0_23()
	return var_0_2.get_result(var_0_22).obj
end

local function var_0_24(arg_9_0, arg_9_1)
	local var_9_0 = {}

	for iter_9_0 = 0, objManager.enemies_n - 1 do
		local var_9_1 = objManager.enemies[iter_9_0]

		if arg_9_1 > arg_9_0:dist(var_9_1.pos) and var_0_4.IsValidTarget(var_9_1) then
			var_9_0[#var_9_0 + 1] = var_9_1
		end
	end

	return var_9_0
end

local function var_0_25(arg_10_0, arg_10_1)
	local var_10_0 = {}

	for iter_10_0 = 0, objManager.minions.size[TEAM_ENEMY] - 1 do
		local var_10_1 = objManager.minions[TEAM_ENEMY][iter_10_0]

		if arg_10_1 > arg_10_0:dist(var_10_1.pos) and var_0_4.IsValidTarget(var_10_1) then
			var_10_0[#var_10_0 + 1] = var_10_1
		end
	end

	return var_10_0
end

var_0_3.combat.register_f_after_attack(function()
	if var_0_3.menu.hybrid.key:get() and player:spellSlot(0).state == 0 and var_0_17 and var_0_4.IsValidTarget(var_0_17) and player.pos:dist(var_0_17.pos) < 700 and var_0_9.harass.qaa:get() and var_0_9.harass.qcombo:get() then
		player:castSpell("self", 0)
		var_0_3.combat.set_invoke_after_attack(false)

		return "on_after_attack_hydra"
	end

	if var_0_3.menu.combat.key:get() and player:spellSlot(0).state == 0 and var_0_17 and var_0_17 and var_0_4.IsValidTarget(var_0_17) and player.pos:dist(var_0_17.pos) < 700 and var_0_9.combo.qaa:get() and var_0_9.combo.qcombo:get() then
		player:castSpell("self", 0)
		var_0_3.combat.set_invoke_after_attack(false)

		return "on_after_attack_hydra"
	end

	var_0_3.combat.set_invoke_after_attack(false)
end)

local var_0_26 = {
	0.25,
	0.26,
	0.27,
	0.28,
	0.29
}

function QDamage(arg_12_0)
	local var_12_0 = 0

	if player:spellSlot(0).level > 0 then
		var_12_0 = var_0_4.CalculatePhysicalDamage(arg_12_0, var_0_4.GetTotalAD() * var_0_26[player:spellSlot(0).level], player)
	end

	return var_12_0 * 5
end

local var_0_27 = {
	20,
	35,
	50,
	65,
	80
}

function WDamage(arg_13_0)
	local var_13_0 = 0

	if player:spellSlot(1).level > 0 then
		var_13_0 = var_0_4.CalculatePhysicalDamage(arg_13_0, var_0_27[player:spellSlot(1).level] + var_0_4.GetTotalAD(), player)
	end

	return var_13_0
end

local var_0_28 = {
	200,
	400,
	600
}

local function var_0_29(arg_14_0)
	local var_14_0 = 0

	if player:spellSlot(3).level > 0 then
		var_14_0 = var_0_4.CalculateMagicDamage(arg_14_0, var_0_28[player:spellSlot(3).level] + var_0_4.GetTotalAP(), player)
	end

	return var_14_0
end

local function var_0_30(arg_15_0)
	if arg_15_0 and (arg_15_0.moveSpeed < 50 or arg_15_0.buff[5] or arg_15_0.buff[21] or arg_15_0.buff[34] or arg_15_0.buff[11] or arg_15_0.buff[29] or arg_15_0.buff.Recall or arg_15_0.buff[24] or arg_15_0.buff[8] or arg_15_0.buff[22]) then
		return true
	end

	return false
end

local function var_0_31()
	if var_0_9.combo.qcombo:get() and not var_0_9.combo.qaa:get() and player:spellSlot(0).state == 0 and var_0_3.combat.target then
		player:castSpell("self", 0)
	end

	if var_0_9.combo.wcombo:get() and player:spellSlot(1).state == 0 then
		local var_16_0 = var_0_15()

		if var_0_4.IsValidTarget(var_16_0) then
			local var_16_1 = var_0_1.linear.get_prediction(var_0_6, var_16_0)

			if var_16_1 and player.pos:dist(vec3(var_16_1.endPos.x, var_16_0.pos.y, var_16_1.endPos.y)) < var_0_6.range and not var_0_1.collision.get_prediction(var_0_6, var_16_1, var_16_0) then
				player:castSpell("pos", 1, vec3(var_16_1.endPos.x, var_16_0.pos.y, var_16_1.endPos.y))
			end
		end
	end

	if var_0_9.combo.rcombo:get() and player:spellSlot(3).state == 0 then
		local var_16_2 = var_0_23()

		if var_0_4.IsValidTarget(var_16_2) then
			local var_16_3 = var_0_1.linear.get_prediction(var_0_8, var_16_2)

			if var_16_3 and player.pos:dist(vec3(var_16_3.endPos.x, var_16_2.pos.y, var_16_3.endPos.y)) < var_0_8.range and not var_0_1.collision.get_prediction(var_0_8, var_16_3, var_16_2) and var_0_16(var_0_8, var_16_3, var_16_2) then
				if var_16_2.health <= QDamage(var_16_2) + var_0_29(var_16_2) + WDamage(var_16_2) + var_0_4.CalculateAADamage(var_16_2) and var_16_2.pos:dist(player.pos) <= var_0_6.range + 50 then
					player:castSpell("pos", 3, vec3(var_16_3.endPos.x, var_16_2.pos.y, var_16_3.endPos.y))
				end

				if var_0_9.combo.raoe:get() and #var_0_24(vec3(var_16_3.endPos.x, var_16_2.pos.y, var_16_3.endPos.y), 200) > 1 and var_16_2.pos:dist(player) <= 1000 then
					player:castSpell("pos", 3, vec3(var_16_3.endPos.x, var_16_2.pos.y, var_16_3.endPos.y))
				end
			end
		end
	end
end

local function var_0_32()
	if var_0_9.harass.qcombo:get() and not var_0_9.harass.qaa:get() and player:spellSlot(0).state == 0 and var_0_3.combat.target then
		player:castSpell("self", 0)
	end

	if var_0_9.harass.wcombo:get() and player:spellSlot(1).state == 0 then
		local var_17_0 = var_0_15()

		if var_17_0 then
			local var_17_1 = var_0_1.linear.get_prediction(var_0_6, var_17_0)

			if var_17_1 and player.pos:dist(vec3(var_17_1.endPos.x, var_17_0.pos.y, var_17_1.endPos.y)) < var_0_6.range and not var_0_1.collision.get_prediction(var_0_6, var_17_1, var_17_0) then
				player:castSpell("pos", 1, vec3(var_17_1.endPos.x, var_17_0.pos.y, var_17_1.endPos.y))
			end
		end
	end
end

local function var_0_33()
	if var_0_9.farming.jungleclear.farmq:get() and player:spellSlot(0).state == 0 then
		for iter_18_0 = 0, objManager.minions.size[TEAM_NEUTRAL] - 1 do
			local var_18_0 = objManager.minions[TEAM_NEUTRAL][iter_18_0]

			if var_18_0 and var_18_0.isVisible and var_18_0.moveSpeed > 0 and var_18_0.isTargetable and not var_18_0.isDead and var_18_0.pos:dist(player.pos) < 700 then
				player:castSpell("self", 0)
			end
		end
	end

	if var_0_9.farming.jungleclear.farmw:get() and player:spellSlot(1).state == 0 then
		for iter_18_1 = 0, objManager.minions.size[TEAM_NEUTRAL] - 1 do
			local var_18_1 = objManager.minions[TEAM_NEUTRAL][iter_18_1]

			if var_18_1 and var_18_1.isVisible and var_18_1.moveSpeed > 0 and var_18_1.isTargetable and not var_18_1.isDead and var_18_1.pos:dist(player.pos) < var_0_6.range then
				local var_18_2 = var_0_1.linear.get_prediction(var_0_6, var_18_1)

				if var_18_2 and var_18_2.startPos:dist(var_18_2.endPos) < var_0_6.range then
					player:castSpell("pos", 1, vec3(var_18_2.endPos.x, var_18_1.y, var_18_2.endPos.y))
				end
			end
		end
	end
end

local function var_0_34()
	if var_0_9.farming.laneclear.farmq:get() and player:spellSlot(0).state == 0 then
		for iter_19_0 = 0, objManager.minions.size[TEAM_ENEMY] - 1 do
			local var_19_0 = objManager.minions[TEAM_ENEMY][iter_19_0]

			if var_19_0 and var_19_0.isVisible and var_19_0.moveSpeed > 0 and var_19_0.isTargetable and not var_19_0.isDead and var_19_0.pos:dist(player.pos) < 700 then
				player:castSpell("self", 0)
			end
		end
	end

	if var_0_9.farming.laneclear.farmw:get() and player:spellSlot(1).state == 0 then
		for iter_19_1 = 0, objManager.minions.size[TEAM_ENEMY] - 1 do
			local var_19_1 = objManager.minions[TEAM_ENEMY][iter_19_1]

			if var_19_1 and var_19_1.isVisible and var_19_1.moveSpeed > 0 and var_19_1.isTargetable and not var_19_1.isDead and var_19_1.pos:dist(player.pos) < var_0_6.range then
				local var_19_2 = var_0_1.linear.get_prediction(var_0_6, var_19_1)

				if var_19_2 and var_19_2.startPos:dist(var_19_2.endPos) < var_0_6.range then
					player:castSpell("pos", 1, vec3(var_19_2.endPos.x, var_19_1.y, var_19_2.endPos.y))
				end
			end
		end
	end
end

function VectorExtend(arg_20_0, arg_20_1, arg_20_2)
	return arg_20_0 + arg_20_2 * (arg_20_1 - arg_20_0):norm()
end

local var_0_35
local var_0_36
local var_0_37 = math.cos
local var_0_38 = mathf.sin

local function var_0_39()
	if player.isDead then
		return
	end

	if player.isOnScreen then
		local var_21_0 = graphics.world_to_screen(vec3(player.x, player.y, player.z))

		graphics.draw_text_2D("Farm: ", 15, var_21_0.x - 50, var_21_0.y + 20, graphics.argb(255, 255, 255, 255))

		if var_0_9.farming.toggle:get() then
			graphics.draw_text_2D("ON", 15, var_21_0.x - 10, var_21_0.y + 20, graphics.argb(255, 128, 255, 0))
		else
			graphics.draw_text_2D("OFF", 15, var_21_0.x - 10, var_21_0.y + 20, graphics.argb(255, 218, 34, 34))
		end

		if var_0_9.draws.draww:get() then
			graphics.draw_circle(player.pos, var_0_6.range, 2, var_0_9.draws.colorw:get(), 80)
		end
	end

	if var_0_9.draws.drawdamage:get() then
		for iter_21_0 = 0, objManager.enemies_n - 1 do
			local var_21_1 = objManager.enemies[iter_21_0]

			if var_21_1 and var_21_1.isVisible and var_21_1.team == TEAM_ENEMY and var_21_1.isOnScreen then
				local var_21_2 = var_0_29(var_21_1) + QDamage(var_21_1) + WDamage(var_21_1) + var_0_4.CalculateAADamage(var_21_1)
				local var_21_3 = var_21_1.barPos
				local var_21_4 = var_21_3.x + 164
				local var_21_5 = var_21_3.y + 122.5
				local var_21_6 = var_21_1.health - var_21_2
				local var_21_7 = var_21_4 + var_21_1.health / var_21_1.maxHealth * 102
				local var_21_8 = var_21_4 + (var_21_6 > 0 and var_21_6 or 0) / var_21_1.maxHealth * 102

				if var_21_6 > 0 then
					graphics.draw_line_2D(var_21_7, var_21_5, var_21_8, var_21_5, 10, graphics.argb(var_0_9.draws.transparency:get(), 255, 192, 200))
				else
					graphics.draw_line_2D(var_21_7, var_21_5, var_21_8, var_21_5, 10, graphics.argb(var_0_9.draws.transparency:get(), 0, 255, 0))
				end
			end
		end
	end
end

local function var_0_40()
	local var_22_0 = {}
	local var_22_1 = var_0_2.get_result(function(arg_23_0, arg_23_1, arg_23_2)
		if arg_23_2 <= var_0_6.range and arg_23_1.path.isActive and arg_23_1.path.isDashing then
			arg_23_0.obj = arg_23_1

			return true
		end
	end).obj

	if var_22_1 then
		local var_22_2 = var_0_1.core.lerp(var_22_1.path, network.latency + var_0_6.delay, var_22_1.path.dashSpeed)

		if var_22_2 and var_22_2:dist(player.path.serverPos2D) <= var_0_6.range and player:spellSlot(1).state == 0 then
			var_22_0.startPos = player.path.serverPos2D
			var_22_0.endPos = vec2(var_22_2.x, var_22_2.y)

			if not var_0_1.collision.get_prediction(var_0_6, var_22_0, var_22_1.pos:to2D()) then
				player:castSpell("pos", 1, vec3(var_22_2.x, var_22_1.pos.y, var_22_2.y))
			end
		end
	end
end

local function var_0_41()
	if var_0_9.misc.Gap.GapA:get() == false then
		return
	end

	local var_24_0 = {}
	local var_24_1 = var_0_2.get_result(function(arg_25_0, arg_25_1, arg_25_2)
		if arg_25_2 <= 600 and arg_25_1.path.isActive and arg_25_1.path.isDashing and var_0_9.misc.Gap.gapblacklist[arg_25_1.charName] and not var_0_9.misc.Gap.gapblacklist[arg_25_1.charName]:get() then
			arg_25_0.obj = arg_25_1

			return true
		end
	end).obj

	if var_24_1 then
		local var_24_2 = var_0_1.core.lerp(var_24_1.path, network.latency + var_0_8.delay, var_24_1.path.dashSpeed)

		if var_24_2 and var_24_2:dist(player.path.serverPos2D) <= 600 then
			var_24_0.startPos = player.path.serverPos2D
			var_24_0.endPos = vec2(var_24_2.x, var_24_2.y)

			if not var_0_1.collision.get_prediction(var_0_8, var_24_0, var_24_1.pos:to2D()) then
				player:castSpell("pos", 3, vec3(var_24_2.x, var_24_1.y, var_24_2.y))
			end
		end
	end
end

local function var_0_42()
	if player.isDead then
		return
	end

	if var_0_9.combo.semir:get() then
		local var_26_0 = var_0_23()

		if var_26_0 then
			local var_26_1 = var_0_1.linear.get_prediction(var_0_8, var_26_0)

			if var_26_1 and player.pos:dist(vec3(var_26_1.endPos.x, var_26_0.pos.y, var_26_1.endPos.y)) < var_0_8.range and not var_0_1.collision.get_prediction(var_0_8, var_26_1, var_26_0) and var_0_16(var_0_8, var_26_1, var_26_0) then
				player:castSpell("pos", 3, vec3(var_26_1.endPos.x, var_26_0.pos.y, var_26_1.endPos.y))
			end
		end
	end

	var_0_41()
	var_0_21()

	if var_0_9.combo.wdash:get() then
		var_0_40()
	end

	if var_0_3.menu.combat.key:get() then
		var_0_31()
	end

	if var_0_3.menu.hybrid.key:get() then
		var_0_32()
	end

	if var_0_3.menu.lane_clear.key:get() and var_0_9.farming.toggle:get() then
		var_0_34()
		var_0_33()
	end
end

cb.add(cb.draw, var_0_39)
var_0_3.combat.register_f_pre_tick(var_0_42)
cb.add(cb.spell, var_0_20)

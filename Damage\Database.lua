local database = {}
database.version = 9.6

-- skillshot type: circle, line, cone, cross, rect
-- slot: 0 1 2 3 (QWER)
-- table : ['skillshotName']
-- data: charName, slot, type, speed, range, delay, radius(circle, line, cone, self), angel(cone), collision

local AttackResets = {
    ["asheq"] = true,
    ["leonashieldofdaybreakattack"] = true,
    ["dariusnoxiantacticsonh"] = true,
    ["garenq"] = true,
    ["gravesmove"] = true,
    ["jaxempowertwo"] = true,
    ["jaycehypercharge"] = true,
    ["leonashieldofdaybreak"] = true,
    ["luciane"] = true,
    ["monkeykingdoubleattack"] = true,
    ["mordekaisermaceofspades"] = true,
    ["nasusq"] = true,
    ["nautiluspiercinggaze"] = true,
    ["netherblade"] = true,
    ["gangplankqwrapper"] = true,
    ["powerfist"] = true,
    ["renektonpreexecute"] = true,
    ["rengarq"] = true,
    ["rengarqemp"] = true,
    ["shyvanadoubleattack"] = true,
    ["sivirw"] = true,
    ["takedown"] = true,
    ["talonnoxiandiplomacy"] = true,
    ["trundletrollsmash"] = true,
    ["vaynetumble"] = true,
    ["vie"] = true,
    ["volibearq"] = true,
    ["xinzhaocombotarget"] = true,
    ["xinzhaoq"] = true,
    ["yorickspectral"] = true,
    ["reksaiq"] = true,
    ["itemtitanichydracleave"] = true,
    ["masochism"] = true,
    ["illaoiw"] = true,
    ["elisespiderw"] = true,
    ["fiorae"] = true,
    ["meditate"] = true,
    ["sejuaninorthernwinds"] = true,
    ["camilleq"] = true,
    ["camilleq2"] = true,
    ["vorpalspikes"] = true,
}

local SpecialAttacks = {
    ["caitlynheadshotmissile"] = true,
    ["kennenmegaproc"] = true,
    ["masteryidoublestrike"] = true,
    ["quinnwenhanced"] = true,
    ["renektonexecute"] = true,
    ["renektonsuperexecute"] = true,
    ["trundleq"] = true,
    ["viktorqbuff"] = true,
    ["xinzhaoqthrust1"] = true,
    ["xinzhaoqthrust2"] = true,
    ["xinzhaoqthrust3"] = true,
    ["frostarrow"] = true,
    ["garenqattack"] = true,
    ["kennenmegaproc"] = true,
    ["masteryidoublestrike"] = true,
    ["mordekaiserqattack"] = true,
    ["bluecardpreattack"] = true,
    ["goldcardpreattack"] = true,
    ["redcardpreattack"] = true,
    ["itemtitanichydracleave"] = true,
    ["itemtiamatcleave"] = true, 
}

local NoAttacks = {
    ["asheqattacknoonhit"] = true,
    ["volleyattackwithsound"] = true,
    ["volleyattack"] = true,
    ["annietibbersbasicattack"] = true,
    ["annietibbersbasicattack2"] = true,
    ["azirsoldierbasicattack"] = true,
    ["azirsundiscbasicattack"] = true,
    ["elisespiderlingbasicattack"] = true,
    ["gravesbasicattackspread"] = true,
    ["gravesautoattackrecoil"] = true,
    ["heimertyellowbasicattack"] = true,
    ["heimertyellowbasicattack2"] = true,
    ["heimertbluebasicattack"] = true,
    ["jarvanivcataclysmattack"] = true,
    ["kindredwolfbasicattack"] = true,
    ["malzaharvoidlingbasicattack"] = true,
    ["malzaharvoidlingbasicattack2"] = true,
    ["malzaharvoidlingbasicattack3"] = true,
    ["shyvanadoubleattack"] = true,
    ["shyvanadoubleattackdragon"] = true,
    ["monkeykingdoubleattack"] = true,
    ["yorickspectralghoulbasicattack"] = true,
    ["yorickdecayedghoulbasicattack"] = true,
    ["yorickravenousghoulbasicattack"] = true,
    ["zyragraspingplantattack"] = true,
    ["zyragraspingplantattack2"] = true,
    ["zyragraspingplantattackfire"] = true,
    ["zyragraspingplantattack2fire"] = true, 
}

database.Spells = {
	["Aatrox"] = {
		["AatroxQ1"] = {
			charName = "Aatrox",
			slot = 0,
			type = "line",
			speed = 500,
			range = 650,
			delay = 0.6,
			radius = 150,
			collision = false
		},
		["AatroxQ2"] = {
			charName = "Aatrox",
			slot = 0,
			type = "line",
			speed = 500,
			range = 500,
			delay = 0.5,
			radius = 500,
			collision = false
		},
		["AatroxQ3"] = {
			charName = "Aatrox",
			slot = 0,
			type = "circle",
			speed = 500,
			range = 475,
			delay = 0.5,
			radius = 300,
			collision = true
		},
		["AatroxW"] = {
			charName = "Aatrox",
			slot = 1,
			type = "line",
			speed = 1800,
			range = 825,
			delay = 0.25,
			radius = 80,
			collision = true
		},
	},
	["Ahri"] = {
		["AhriOrbofDeception"] = {
			charName = "Ahri",
			slot = 0,
			type = "line",
			speed = 2500,
			range = 880,
			delay = 0.25,
			radius = 100,
			collision = false
		},
		["AhriSeduce"] = {
			charName = "Ahri",
			slot = 2,
			type = "line",
			speed = 1500,
			range = 975,
			delay = 0.25,
			radius = 60,
			collision = true
		},
	},
	["Akali"] = {
		["AkaliQ"] = {
			charName = "Akali",
			slot = 0,
			type = "cone",
			speed = 3200,
			range = 550,
			delay = 0.25,
			radius = 60,
			angle = 45,
			collision = false
		},
		["AkaliE"] = {
			charName = "Akali",
			slot = 2,
			type = "line",
			speed = 1800,
			range = 825,
			delay = 0.25,
			radius = 70,
			collision = true
		},
		["AkaliR"] = {
			charName = "Akali",
			slot = 3,
			type = "line",
			speed = 1800,
			range = 525,
			delay = 0,
			radius = 65,
			collision = false
		},
		["AkaliRb"] = {
			charName = "Akali",
			slot = 3,
			type = "line",
			speed = 3600,
			range = 525,
			delay = 0,
			radius = 65,
			collision = false
		},
	},
	["Alistar"] = {
		["Pulverize"] = {
			charName = "Alistar",
			slot = 0,
			type = "circle",
			speed = math.huge,
			range = 0,
			delay = 0.25,
			radius = 365,
			collision = false
		},
	},
	["Amumu"] = {
		["BandageToss"] = {
			charName = "Amumu",
			slot = 0,
			type = "line",
			speed = 2000,
			range = 1100,
			delay = 0.25,
			radius = 80,
			collision = true
		},		
		["Tantrum"] = {
			charName = "Amumu",
			slot = 2,
			type = "circle",
			range = 350,
			delay = 0.01,
			radius = 350,
			collision = false
		},
		["CurseoftheSadMummy"] = {
			charName = "Amumu",
			slot = 3,
			type = "circle",
			speed = math.huge,
			range = 0,
			delay = 0.25,
			radius = 550,
			collision = false
		},
	},
	["Anivia"] = {
		["FlashFrostSpell"] = {
			charName = "Anivia",
			slot = 0,
			type = "line",
			speed = 850,
			range = 1100,
			delay = 0.25,
			radius = 110,
			collision = false
		},
	},
	["Annie"] = {
		["AnnieW"] = {
			charName = "Annie",
			slot = 1,
			type = "cone",
			speed = math.huge,
			range = 600,
			delay = 0.25,
			radius = 0,
			angle = 50,
			collision = false
		},
		["AnnieR"] = {
			charName = "Annie",
			slot = 3,
			type = "circle",
			speed = math.huge,
			range = 600,
			delay = 0.25,
			radius = 0,
			radius = 290,
			collision = false
		},
	},
	["Ashe"] = {
		["Volley"] = {
			charName = "Ashe",
			slot = 1,
			type = "cone",
			speed = 2000,
			range = 1200,
			delay = 0.25,
			radius = 20,
			angle = 40,
			collision = true
		},
		["EnchantedCrystalArrow"] = {
			charName = "Ashe",
			slot = 3,
			type = "line",
			speed = 1600,
			range = 25000,
			delay = 0.25,
			radius = 130,
			collision = false
		},
	},
	["AurelionSol"] = {
		["AurelionSolQ"] = {
			charName = "AurelionSol",
			slot = 0,
			type = "line",
			speed = 850,
			range = 550,
			delay = 0.25,
			radius = 110,
			collision = false
		},
		["AurelionSolR"] = {
			charName = "AurelionSol",
			slot = 3,
			type = "line",
			speed = 4500,
			range = 1500,
			delay = 0.35,
			radius = 120,
			collision = false
		},
	},
	["Azir"] = {
		["AzirQWrapper"] = {
			charName = "Azir",
			slot = 0,
			type = "line",
			speed = 1600,
			range = 20000,
			delay = 0.3,
			radius = 70,
			collision = false
		},
		["AzirR"] = {
			charName = "Azir",
			slot = 3,
			type = "line",
			speed = 1400,
			range = 500,
			delay = 0.3,
			radius = 250,
			collision = false
		},
	},
	["Bard"] = {
		["BardQ"] = {
			charName = "Bard",
			slot = 0,
			type = "line",
			speed = 1500,
			range = 950,
			delay = 0.25,
			radius = 60,
			collision = true
		},
	},
	["Blitzcrank"] = {
		["RocketGrab"] = {
			charName = "Blitzcrank",
			slot = 0,
			type = "line",
			speed = 1800,
			range = 1050,
			delay = 0.25,
			radius = 70,
			collision = true
		},
		["StaticField"] = {
			charName = "Blitzcrank",
			slot = 3,
			type = "circle",
			speed = math.huge,
			range = 0,
			delay = 0.25,
			radius = 600,
			collision = false
		},
	},
	["Brand"] = {
		["BrandQ"] = {
			charName = "Brand",
			slot = 0,
			type = "line",
			speed = 1600,
			range = 1050,
			delay = 0.25,
			radius = 60,
			collision = true
		},
		["BrandW"] = {
			charName = "Brand",
			slot = 1,
			type = "self",
			speed = math.huge,
			range = 900,
			delay = 0.85,
			radius = 250,
			collision = false
		},
	},
	["Braum"] = {
		["BraumQ"] = {
			charName = "Braum",
			slot = 0,
			type = "line",
			speed = 1700,
			range = 1000,
			delay = 0.25,
			radius = 70,
			collision = true
		},
		["BraumR"] = {
			charName = "Braum",
			slot = 3,
			type = "line",
			speed = 1400,
			range = 1250,
			delay = 0.5,
			radius = 115,
			collision = false
		},
	},
	["Caitlyn"] = {
		["CaitlynPiltoverPeacemaker"] = {
			charName = "Caitlyn",
			slot = 0,
			type = "line",
			speed = 2200,
			range = 1250,
			delay = 0.625,
			radius = 60,
			radius2 = 90,
			collision = false
		},
		["CaitlynEntrapment"] = {
			charName = "Caitlyn",
			slot = 2,
			type = "line",
			speed = 1600,
			range = 750,
			delay = 0.15,
			radius = 70,
			collision = true
		},
	},
	["Cassiopeia"] = {
		["CassiopeiaQ"] = {
			charName = "Cassiopeia",
			slot = 0,
			type = "circle",
			speed = math.huge,
			range = 850,
			delay = 0.75,
			radius = 150,
			collision = false
		},
		["CassiopeiaW"] = {
			charName = "Cassiopeia",
			slot = 1,
			type = "circle",
			speed = 2500,
			range = 800,
			delay = 0.75,
			radius = 160,
			collision = false
		},
		["CassiopeiaR"] = {
			charName = "Cassiopeia",
			slot = 3,
			type = "cone",
			speed = math.huge,
			range = 825,
			delay = 0.5,
			radius = 0,
			angle = 80,
			collision = false
		},
	},
	["Chogath"] = {
		["Rupture"] = {
			charName = "Chogath",
			slot = 0,
			type = "circle",
			speed = math.huge,
			range = 950,
			delay = 1.2,
			radius = 250,
			collision = false
		},
		["FeralScream"] = {
			charName = "Chogath",
			slot = 1,
			type = "cone",
			speed = math.huge,
			range = 650,
			delay = 0.5,
			radius = 0,
			angle = 56,
			collision = false
		},
	},
	["Corki"] = {
		["PhosphorusBomb"] = {
			charName = "Corki",
			slot = 0,
			type = "circle",
			speed = 1000,
			range = 825,
			delay = 0.25,
			radius = 250,
			collision = false
		},
		["MissileBarrageMissile"] = {
			charName = "Corki",
			slot = 3,
			type = "line",
			speed = 2000,
			range = 1300,
			delay = 0.175,
			radius = 40,
			collision = true
		},
		["MissileBarrageMissile2"] = {
			charName = "Corki",
			slot = 3,
			type = "line",
			speed = 2000,
			range = 1500,
			delay = 0.175,
			radius = 40,
			collision = true
		},
	},
	["Darius"] = {
		["DariusCleave"] = {
			charName = "Darius",
			slot = 0,
			type = "circle",
			speed = math.huge,
			range = 425,
			delay = 0.75,
			radius = 425,
			collision = false
		},
	},
	["Diana"] = {
		["DianaArc"] = {
			charName = "Diana",
			slot = 0,
			type = "circle",
			speed = 1400,
			range = 900,
			delay = 0.25,
			radius = 185,
			collision = true
		},
	},
	["DrMundo"] = {
		["InfectedCleaverMissile"] = {
			charName = "DrMundo",
			slot = 0,
			type = "line",
			speed = 2000,
			range = 975,
			delay = 0.25,
			radius = 60,
			collision = true
		},
	},
	["Draven"] =  {
		["DravenDoubleShot"] = {
			charName = "Draven",
			slot = 2,
			type = "line",
			speed = 1600,
			range = 1050,
			delay = 0.25,
			radius = 130,
			collision = false
		},
		["DravenRCast"] = {
			charName = "Draven",
			slot = 3,
			type = "line",
			speed = 2000,
			range = 25000,
			delay = 0.25,
			radius = 160,
			collision = false
		},
	},
	["Ekko"] = {
		["EkkoQ"] = {
			charName = "Ekko",
			slot = 0,
			type = "line",
			speed = 1650,
			range = 1175,
			delay = 0.25,
			radius = 60,
			collision = false
		},
		["EkkoW"] = {
			charName = "Ekko",
			slot = 1,
			type = "circle",
			speed = math.huge,
			range = 1600,
			delay = 3.35,
			radius = 400,
			collision = false
		},
	},
	["Elise"] = {
		["EliseHumanE"] = {
			charName = "Elise",
			slot = 2,
			type = "line",
			speed = 1600,
			range = 1075,
			delay = 0.25,
			radius = 55,
			collision = true
		},
	},
	["Evelynn"] = {
		["EvelynnQ"] = {
			charName = "Evelynn",
			slot = 0,
			type = "line",
			speed = 2400,
			range = 800,
			delay = 0.25,
			radius = 60,
			collision = true
		},
		["EvelynnR"] = {
			charName = "Evelynn",
			slot = 3,
			type = "cone",
			speed = math.huge,
			range = 450,
			delay = 0.35,
			radius = 180,
			angle = 180,
			collision = false
		},
	},
	["Ezreal"] = {
		["EzrealQ"] = {
			charName = "Ezreal",
			slot = 0,
			type = "line",
			speed = 2000,
			range = 1150,
			delay = 0.25,
			radius = 60,
			collision = true
		},
		["EzrealW"] = {
			charName = "Ezreal",
			slot = 1,
			type = "line",
			speed = 2000,
			range = 1150,
			delay = 0.25,
			radius = 60,
			collision = false
		},
		["EzrealR"] = {
			charName = "Ezreal",
			slot = 3,
			type = "line",
			speed = 2000,
			range = 25000,
			delay = 1,
			radius = 160,
			collision = false
		},
	},
	["Fiora"] = {
		["FioraW"] = {
			charName = "Fizz",
			slot = 3,
			type = "line",
			speed = 3200,
			range = 770,
			delay = 0.5,
			radius = 70,
			collision = false
		},
	},
	["Fizz"] = {
		["FizzR"] = {
			charName = "Fizz",
			slot = 3,
			type = "line",
			speed = 1300,
			range = 1300,
			delay = 0.25,
			radius = 150,
			collision = false
		},
	},
	["Galio"] = {
		["GalioQ"] = {
			charName = "Galio",
			slot = 0,
			type = "circle",
			speed = 1150,
			range = 825,
			delay = 0.25,
			radius = 235,
			collision = false
		},
		["GalioE"] = {
			charName = "Galio",
			slot = 2,
			type = "line",
			speed = 2300,
			range = 650,
			delay = 0.4,
			radius = 160,
			collision = false
		},
	},
	["Gnar"] = {
		["GnarQMissile"] = {
			charName = "Gnar",
			slot = 0,
			type = "line",
			speed = 2500,
			range = 1125,
			delay = 0.25,
			radius = 55,
			collision = false
		},
		["GnarBigQMissile"] = {
			charName = "Gnar",
			slot = 0,
			type = "line",
			speed = 2100,
			range = 1125,
			delay = 0.5,
			radius = 90,
			collision = true
		},
		["GnarBigW"] = {
			charName = "Gnar",
			slot = 1,
			type = "line",
			speed = math.huge,
			range = 575,
			delay = 0.6,
			radius = 100,
			collision = false
		},
		["GnarR"] = {
			charName = "Gnar",
			slot = 3,
			type = "circle",
			speed = math.huge,
			range = 0,
			delay = 0.25,
			radius = 475,
			collision = false
		},
	},
	["Gragas"] = {
		["GragasQ"] = {
			charName = "Gragas",
			slot = 0,
			type = "circle",
			speed = 1000,
			range = 850,
			delay = 0.25,
			radius = 275,
			collision = false
		},
		["GragasE"] = {
			charName = "Gragas",
			slot = 2,
			type = "line",
			speed = 900,
			range = 600,
			delay = 0.01,
			radius = 200,
			collision = false
		},
		["GragasR"] = {
			charName = "Gragas",
			slot = 3,
			type = "circle",
			speed = 1800,
			range = 1000,
			delay = 0.25,
			radius = 400,
			collision = false
		},
	},
	["GravesQLineSpell"] = {
		["GravesQLineSpell"] = {
			charName = "Graves",
			slot = 0,
			type = "line",
			speed = 2000,
			range = 700,
			delay = 0.095,
			radius = 20,
			collision = false
		},
		["GravesSmokeGrenade"] = {
			charName = "Graves",
			slot = 1,
			type = "circle",
			speed = 1500,
			range = 950,
			delay = 0.15,
			radius = 250,
			collision = false
		},
		["GravesChargeShot"] = {
			charName = "Graves",
			slot = 3,
			type = "line",
			speed = 2100,
			range = 1000,
			delay = 0.25,
			radius = 100,
			collision = false
		},
	},
	["Hecarim"] = {
		["HecarimQ"] = {
			charName = "Hecarim",
			slot = 0,
			type = "circle",
			speed = math.huge,
			range = 350,
			delay = 0.25,
			radius = 350,
			collision = false
		},
		["HecarimUlt"] = {
			charName = "Hecarim",
			slot = 3,
			type = "line",
			speed = 1100,
			range = 1000,
			delay = 0.01,
			radius = 240,
			collision = false
		},
	},
	["Heimerdinger"] = {
		["HeimerdingerW"] = {
			charName = "Heimerdinger",
			slot = 1,
			type = "line",
			speed = 2050,
			range = 1325,
			delay = 0.25,
			radius = 100,
			collision = false
		},
		["HeimerdingerE"] = {
			charName = "Heimerdinger",
			slot = 2,
			type = "circle",
			speed = 1200,
			range = 970,
			delay = 0.25,
			radius = 250,
			collision = false
		},
		["HeimerdingerEUlt"] = {
			charName = "Heimerdinger",
			slot = 2,
			type = "circle",
			speed = 1200,
			range = 970,
			delay = 0.25,
			radius = 250,
			collision = false
		},
	},
	["Illaoi"] = {
		["IllaoiQ"] = {
			charName = "Illaoi",
			slot = 0,
			type = "line",
			speed = math.huge,
			range = 850,
			delay = 0.75,
			radius = 100,
			collision = true
		},
		["IllaoiE"] = {
			charName = "Illaoi",
			slot = 2,
			type = "line",
			speed = 1900,
			range = 900,
			delay = 0.25,
			radius = 50,
			collision = true
		},
		["IllaoiR"] = {
			charName = "Illaoi",
			slot = 3,
			type = "circle",
			speed = math.huge,
			range = 450,
			delay = 0.5,
			radius = 450,
			collision = true
		},
	},
	["Irelia"] = {
		["IreliaW2"] = {
			charName = "Irelia",
			slot = 1,
			type = "line",
			speed = math.huge,
			range = 775,
			delay = 0.25,
			radius = 120,
			collision = false
		},
		["IreliaR"] = {
			charName = "Irelia",
			slot = 3,
			type = "line",
			speed = 2000,
			range = 950,
			delay = 0.4,
			radius = 160,
			collision = false
		},
	},
	["Ivern"] = {
		["IvernQ"] = {
			charName = "Ivern",
			slot = 0,
			type = "line",
			speed = 1300,
			range = 1075,
			delay = 0.25,
			radius = 80,
			collision = true
		},
	},
	["JarvanIV"] = {
		["JarvanIVDragonStrike"] = {
			charName = "JarvanIV",
			slot = 0,
			type = "line",
			speed = math.huge,
			range = 770,
			delay = 0.4,
			radius = 70,
			collision = false
		},
		["JarvanIVQE"] = {
			charName = "JarvanIV",
			slot = 2,
			type = "circle",
			speed = 2600,
			range = 910,
			delay = 0.45,
			radius = 120,
			collision = false
		},
	},
	["Jayce"] = {
		["JayceShockBlast"] = {
			charName = "Jayce",
			slot = 0,
			type = "line",
			speed = 1450,
			range = 1050,
			delay = 0.214,
			radius = 70,
			collision = true
		},
	},
	["Jhin"] = {
		["JhinW"] = {
			charName = "Jhin",
			slot = 1,
			type = "line",
			speed = 5000,
			range = 2550,
			delay = 0.75,
			radius = 40,
			collision = false
		},
		["JhinRShot"] = {
			charName = "Jhin",
			slot = 3,
			type = "line",
			speed = 4500,
			range = 3500,
			delay = 0.19,
			radius = 80,
			collision = false
		},
	},
	["Jinx"] = {
		["JinxWMissile"] = {
			charName = "Jinx",
			slot = 1,
			type = "line",
			speed = 3300,
			range = 1450,
			delay = 0.6,
			radius = 60,
			collision = true
		},
		["JinxR"] = {
			charName = "Jinx",
			slot = 3,
			type = "line",
			speed = 1700,
			range = 25000,
			delay = 0.6,
			radius = 140,
			collision = false
		},
	},
	["Kaisa"] = {
		["KaisaW"] = {
			charName = "Kaisa",
			slot = 1,
			type = "line",
			speed = 1600,
			range = 3000,
			delay = 0.25,
			radius = 100,
			collision = true
		},
	},
	["Kalista"] = {
		["KalistaMysticShot"] = {
			charName = "Kalista",
			slot = 0,
			type = "line",
			speed = 2400,
			range = 1150,
			delay = 0.25,
			radius = 40,
			collision = true
		},
	},
	["Karma"] = {
		["KarmaQ"] = {
			charName = "Karma",
			slot = 0,
			type = "line",
			speed = 1700,
			range = 950,
			delay = 0.25,
			radius = 60,
			collision = true
		},
		["KarmaQMantra"] = {
			charName = "Karma",
			slot = 0,
			type = "line",
			speed = 1700,
			range = 950,
			delay = 0.25,
			radius = 80,
			collision = true
		},
	},
	["Karthus"] = {
		["KarthusLayWasteA1"] = {
			charName = "Karthus",
			slot = 0,
			type = "circle",
			speed = math.huge,
			range = 875,
			delay = 0.9,
			radius = 175,
			collision = false
		},
		["KarthusLayWasteA2"] = {
			charName = "Karthus",
			slot = 0,
			type = "circle",
			speed = math.huge,
			range = 875,
			delay = 0.9,
			radius = 175,
			collision = false
		},
		["KarthusLayWasteA3"] = {
			charName = "Karthus",
			slot = 0,
			type = "circle",
			speed = math.huge,
			range = 875,
			delay = 0.9,
			radius = 175,
			collision = false
		},
	},
	["Kassadin"] = {
		["ForcePulse"] = {
			charName = "Kassadin",
			slot = 2,
			type = "cone",
			speed = math.huge,
			range = 600,
			delay = 0.3,
			radius = 0,
			angle = 80,
			collision = false
		},
		["RiftWalk"] = {
			charName = "Kassadin",
			slot = 3,
			type = "circle",
			speed = math.huge,
			range = 500,
			delay = 0.25,
			radius = 250,
			collision = false
		},
	},
	["Kayle"] = {
		["KayleQMis"] = {
			charName = "Kayle",
			slot = 0,
			type = "line",
			speed = 2000,
			range = 850,
			delay = 0.5,
			radius = 60,
			collision = false
		},
	},
	["Kayn"] = {
		["KaynQ"] = {
			charName = "Kayn",
			slot = 0,
			type = "line",
			speed = 2400,
			range = 350,
			delay = 0.25,
			radius = 100,
			collision = false
		},
		["KaynW"] = {
			charName = "Kayn",
			slot = 1,
			type = "line",
			speed = math.huge,
			range = 700,
			delay = 0.55,
			radius = 90,
			collision = false
		},
	},
	["Kennen"] = {
		["KennenShurikenHurlMissile1"] = {
			charName = "Kennen",
			slot = 0,
			type = "line",
			speed = 1700,
			range = 1050,
			delay = 0.175,
			radius = 50,
			collision = true
		},
	},
	["Khazix"] = {
		["KhazixW"] = {
			charName = "Khazix",
			slot = 1,
			type = "line",
			speed = 1700,
			range = 1000,
			delay = 0.25,
			radius = 70,
			collision = true
		},
		["KhazixWLong"] = {
			charName = "Khazix",
			slot = 1,
			type = "threeway",
			speed = 1700,
			range = 1000,
			delay = 0.25,
			radius = 70,
			angle = 23,
			collision = true
		},
	},
	["Kled"] = {
		["KledQ"] = {
			charName = "Kled",
			slot = 0,
			type = "line",
			speed = 1600,
			range = 800,
			delay = 0.25,
			radius = 45,
			collision = true
		},
		["KledRiderQ"] = {
			charName = "Kled",
			slot = 0,
			type = "cone",
			speed = 3000,
			range = 700,
			delay = 0.25,
			radius = 0,
			angle = 25,
			collision = false
		},
	},
	["KogMaw"] = {
		["KogMawQ"] = {
			charName = "KogMaw",
			slot = 0,
			type = "line",
			speed = 1650,
			range = 1175,
			delay = 0.25,
			radius = 70,
			collision = true
		},
		["KogMawVoidOozeMissile"] = {
			charName = "KogMaw",
			slot = 2,
			type = "line",
			speed = 1400,
			range = 1360,
			delay = 0.25,
			radius = 120,
			collision = false
		},
		["KogMawLivingArtillery"] = {
			charName = "KogMaw",
			slot = 3,
			type = "circle",
			speed = math.huge,
			range = 1300,
			delay = 1.1,
			radius = 200,
			collision = false
		},
	},
	["Leblanc"] = {
		["LeblancE"] = {
			charName = "Leblanc",
			slot = 2,
			type = "line",
			speed = 1750,
			range = 925,
			delay = 0.25,
			radius = 55,
			collision = true
		},
		["LeblancRE"] = {
			charName = "Leblanc",
			slot = 2,
			type = "line",
			speed = 1750,
			range = 925,
			delay = 0.25,
			radius = 55,
			collision = true
		},
	},
	["LeeSin"] = {
		["BlindMonkQOne"] = {
			charName = "LeeSin",
			slot = 0,
			type = "line",
			speed = 1800,
			range = 1100,
			delay = 0.25,
			radius = 60,
			collision = true
		},
		["BlindMonkEOne"] = {
			charName = "LeeSin",
			slot = 2,
			type = "circle",
			speed = math.huge,
			range = 430,
			delay = 0.25,
			radius = 430,
			collision = false
		},
	},
	["Leona"] = {
		["LeonaZenithBlade"] = {
			charName = "Leona",
			slot = 2,
			type = "line",
			speed = 2000,
			range = 875,
			delay = 0.25,
			radius = 70,
			collision = false
		},
		["LeonaSolarFlare"] = {
			charName = "Leona",
			slot = 3,
			type = "circle",
			speed = math.huge,
			range = 1200,
			delay = 0.85,
			radius = 300,
			collision = false
		},
	},
	["Lissandra"] = {
		["LissandraQMissile"] = {
			charName = "Lissandra",
			slot = 0,
			type = "line",
			speed = 2200,
			range = 750,
			delay = 0.25,
			radius = 75,
			collision = false
		},
		["LissandraW"] = {
			charName = "Lissandra",
			slot = 1,
			type = "circle",
			speed = 2200,
			range = 450,
			delay = 0.01,
			radius = 450,
			collision = false
		},
		["LissandraEMissile"] = {
			charName = "Lissandra",
			slot = 2,
			type = "line",
			speed = 850,
			range = 1025,
			delay = 0.25,
			radius = 125,
			collision = false
		},
	},
	["Lucian"] = {
		["LucianQ"] = {
			charName = "Lucian",
			slot = 0,
			type = "line",
			speed = math.huge,
			range = 900,
			delay = 0.35,
			radius = 65,
			collision = false
		},
		["LucianW"] = {
			charName = "Lucian",
			slot = 1,
			type = "line",
			speed = 1600,
			range = 900,
			delay = 0.25,
			radius = 80,
			collision = true
		},
	},
	["Lulu"] = {
		["LuluQ"] = {
			charName = "Lulu",
			slot = 0,
			type = "line",
			speed = 1450,
			range = 925,
			delay = 0.25,
			radius = 60,
			collision = false
		},
	},
	["Lux"] = {
		["LuxLightBinding"] = {
			charName = "Lux",
			slot = 0,
			type = "line",
			speed = 1200,
			range = 1175,
			delay = 0.25,
			radius = 50,
			collision = true
		},
		["LuxLightStrikeKugel"] = {
			charName = "Lux",
			slot = 2,
			type = "circle",
			speed = 1200,
			range = 1100,
			delay = 0.25,
			radius = 300,
			collision = true
		},
		["LuxMaliceCannonMis"] = {
			charName = "Lux",
			slot = 3,
			type = "line",
			speed = math.huge,
			range = 3340,
			delay = 1,
			radius = 120,
			collision = false
		},
	},
	["Malphite"] = {
		["Landslide"] = {
			charName = "Malphite",
			slot = 2,
			type = "circle",
			speed = math.huge,
			range = 0,
			delay = 0.242,
			radius = 400,
			collision = false
		},
		["UFSlash"] = {
			charName = "Malphite",
			slot = 3,
			type = "circle",
			speed = 1600,
			range = 1000,
			delay = 0.01,
			radius = 270,
			collision = false
		},
	},
	["Malzahar"] = {
		["MalzaharQ"] = {
			charName = "Malzahar",
			slot = 0,
			type = "rect",
			speed = 1600,
			range = 900,
			delay = 0.5,
			radius = 100,
			collision = false
		},
	},
	["Maokai"] = {
		["MaokaiQ"] = {
			charName = "Maokai",
			slot = 0,
			type = "line",
			speed = 1600,
			range = 600,
			delay = 0.375,
			radius = 110,
			collision = false
		},
	},
	["MissFortune"] = {
		["MissFortuneBulletTime"] = {
			charName = "MissFortune",
			slot = 3,
			type = "cone",
			speed = 2000,
			range = 1400,
			delay = 0.25,
			radius = 100,
			angle = 34,
			collision = false
		},
	},
	["Mordekaiser"] = {
		["MordekaiserSyphonOfDestruction"] = {
			charName = "Mordekaiser",
			slot = 2,
			type = "cone",
			speed = math.huge,
			range = 700,
			delay = 0.25,
			radius = 0,
			angle = 50,
			collision = false
		},
	},
	["Morgana"] = {
		["DarkBindingMissile"] = {
			charName = "Morgana",
			slot = 0,
			type = "line",
			speed = 1200,
			range = 1175,
			delay = 0.25,
			radius = 70,
			collision = true
		},
		["TormentedSoil"] = {
			charName = "Morgana",
			slot = 1,
			type = "circle",
			speed = math.huge,
			range = 900,
			delay = 0.25,
			radius = 280,
			collision = false
		},
	},
	["Nami"] = {
		["NamiQ"] = {
			charName = "Nami",
			slot = 0,
			type = "circle",
			speed = math.huge,
			range = 875,
			delay = 1,
			radius = 180,
			collision = false
		},
		["NamiRMissile"] = {
			charName = "Nami",
			slot = 3,
			type = "line",
			speed = 850,
			range = 2750,
			delay = 0.5,
			radius = 250,
			collision = false
		},
	},
	["Nautilus"] = {
		["NautilusAnchorDragMissile"] = {
			charName = "Nautilus",
			slot = 0,
			type = "line",
			speed = 2000,
			range = 925,
			delay = 0.25,
			radius = 90,
			collision = true
		},
	},
	["Neeko"] = {
		["NeekoQ"] = {
			charName = "Neeko",
			slot = 0,
			type = "circle",
			speed = 1500,
			range = 800,
			delay = 0.25,
			radius = 200,
			collision = false
		},
		["NeekoE"] = {
			charName = "Neeko",
			slot = 2,
			type = "line",
			speed = 1400,
			range = 1000,
			delay = 0.25,
			radius = 65,
			collision = false
		},
		["NeekoR"] = {
			charName = "Neeko",
			slot = 3,
			type = "circle",
			speed = 1400,
			range = 525,
			delay = 1.75,
			radius = 525,
			collision = false
		},
	},
	["Nidalee"] = {
		["JavelinToss"] = {
			charName = "Nidalee",
			slot = 0,
			type = "line",
			speed = 1300,
			range = 1500,
			delay = 0.25,
			radius = 40,
			collision = true
		},
		["Swipe"] = {
			charName = "Nidalee",
			slot = 2,
			type = "cone",
			speed = math.huge,
			range = 350,
			delay = 0.25,
			radius = 0,
			angle = 180,
			collision = false
		},
	},
	["Nocturne"] = {
		["NocturneDuskbringer"] = {
			charName = "Nocturne",
			slot = 0,
			type = "line",
			speed = 1600,
			range = 1200,
			delay = 0.25,
			radius = 60,
			collision = false
		},
	},
	["Nunu"] = {
		["NunuR"] = {
			charName = "Nunu",
			slot = 3,
			type = "circle",
			speed = math.huge,
			range = 0,
			delay = 3,
			radius = 650,
			collision = false
		},
	},
	["Olaf"] = {
		["OlafAxeThrowCast"] = {
			charName = "Olaf",
			slot = 0,
			type = "line",
			speed = 1600,
			range = 1000,
			delay = 0.25,
			radius = 90,
			collision = false
		},
	},
	["Orianna"] = {
		["OrianaIzunaCommand"] = {
			charName = "Orianna",
			slot = 0,
			type = "line",
			speed = 1200,
			range = 2000,
			delay = 0.25,
			radius = 100,
			collision = false
		},
		["OrianaDissonanceCommand"] = {
			charName = "Orianna",
			slot = 1,
			type = "circle",
			speed = math.huge,
			range = 255,
			delay = 0.25,
			radius = 255,
			collision = false
		},
		["OrianaDetonateCommand"] = {
			charName = "Orianna",
			slot = 3,
			type = "circle",
			speed = math.huge,
			range = 410,
			delay = 0.5,
			radius = 410,
			collision = false
		},
	},
	["Ornn"] = {
		["OrnnQ"] = {
			charName = "Ornn",
			slot = 0,
			type = "line",
			speed = 1800,
			range = 800,
			delay = 0.3,
			radius = 65,
			collision = false
		},
		["OrnnE"] = {
			charName = "Ornn",
			slot = 2,
			type = "line",
			speed = 1800,
			range = 800,
			delay = 0.35,
			radius = 150,
			collision = false
		},
		["OrnnRCharge"] = {
			charName = "Ornn",
			slot = 3,
			type = "line",
			speed = 1650,
			range = 2500,
			delay = 0.5,
			radius = 200,
			collision = false
		},
	},
	["Pantheon"] = {
		["PantheonE"] = {
			charName = "Pantheon",
			slot = 2,
			type = "cone",
			speed = math.huge,
			range = 400,
			delay = 0.389,
			radius = 0,
			angle = 70,
			collision = false
		},
	},
	["Poppy"] = {
		["PoppyQSpell"] = {
			charName = "Poppy",
			slot = 0,
			type = "line",
			speed = math.huge,
			range = 430,
			delay = 0.332,
			radius = 100,
			collision = false
		},
		["PoppyRSpell"] = {
			charName = "Poppy",
			slot = 3,
			type = "line",
			speed = 2000,
			range = 1200,
			delay = 0.33,
			radius = 100,
			collision = false
		},
	},
	["Pyke"] = {
		["PykeQMelee"] = {
			charName = "Pyke",
			slot = 0,
			type = "line",
			speed = math.huge,
			range = 400,
			delay = 0.25,
			radius = 70,
			collision = false
		},
		["PykeQRange"] = {
			charName = "Pyke",
			slot = 0,
			type = "line",
			speed = 2000,
			range = 1100,
			delay = 0.2,
			radius = 70,
			collision = true
		},
		["PykeR"] = {
			charName = "Pyke",
			slot = 3,
			type = "cross",
			speed = math.huge,
			range = 750,
			delay = 0.5,
			radius = 350,
			radius2 = 100,
			collision = false
		},
	},
	["Quinn"] = {
		["QuinnQ"] = {
			charName = "Quinn",
			slot = 0,
			type = "line",
			speed = 1550,
			range = 1025,
			delay = 0.25,
			radius = 60,
			collision = true
		},
	},
	["Rakan"] = {
		["RakanQ"] = {
			charName = "Rakan",
			slot = 0,
			type = "line",
			speed = 1850,
			range = 850,
			delay = 0.25,
			radius = 65,
			collision = true
		},
		["RakanW"] = {
			charName = "Rakan",
			slot = 1,
			type = "circle",
			speed = 1500,
			range = 650,
			delay = 0.7,
			radius = 265,
			collision = false
		},
	},
	["RekSai"] = {
		["RekSaiQBurrowed"] = {
			charName = "RekSai",
			slot = 0,
			type = "line",
			speed = 1950,
			range = 1625,
			delay = 0.125,
			radius = 65,
			collision = true
		},
	},
	["Rengar"] = {
		["RengarE"] = {
			charName = "Rengar",
			slot = 2,
			type = "line",
			speed = 1500,
			range = 1000,
			delay = 0.25,
			radius = 70,
			collision = true
		},
		["RengarEEmp"] = {
			charName = "Rengar",
			slot = 2,
			type = "line",
			speed = 1500,
			range = 1000,
			delay = 0.25,
			radius = 70,
			collision = true
		},
	},
	["Riven"] = {
		-- ["RivenQ1"] = {
		-- 	charName = "Riven",
		-- 	slot = 0,
		-- 	type = "circle",
		-- 	speed = math.huge,
		-- 	range = 260,
		-- 	delay = 0.35,
		-- 	radius = 260,
		-- 	collision = false
		-- },
		-- ["RivenQ2"] = {
		-- 	charName = "Riven",
		-- 	slot = 0,
		-- 	type = "circle",
		-- 	speed = math.huge,
		-- 	range = 260,
		-- 	delay = 0.35,
		-- 	radius = 260,
		-- 	collision = false
		-- },
		-- ["RivenQ3"] = {
		-- 	charName = "Riven",
		-- 	slot = 0,
		-- 	type = "circle",
		-- 	speed = math.huge,
		-- 	range = 260,
		-- 	delay = 0.35,
		-- 	radius = 260,
		-- 	collision = false
		-- },
		["RivenW"] = {
			charName = "Riven",
			slot = 1,
			type = "circle",
			speed = math.huge,
			range = 250,
			delay = 0.01,
			radius = 250,
			collision = false
		},
		["RivenIzunaBlade"] = {
			charName = "Riven",
			slot = 3,
			type = "cone",
			speed = 1600,
			range = 1075,
			angle = 157,
			delay = 0.01,
			radius = 100,
			collision = false
		},
	},
	["Rumble"] = {
		["RumbleGrenade"] = {
			charName = "Rumble",
			slot = 2,
			type = "line",
			speed = 2000,
			range = 850,
			delay = 0.25,
			radius = 60,
			collision = true
		},
		["RumbleCarpetBomb"] = {
			charName = "Rumble",
			slot = 3,
			type = "line",
			speed = 1600,
			range = 2000,
			delay = 0.4,
			radius = 150,
			collision = false
		},
	},
	["Ryze"] = {
		["RyzeQ"] = {
			charName = "Ryze",
			slot = 0,
			type = "line",
			speed = 1700,
			range = 1000,
			delay = 0.25,
			radius = 55,
			collision = true
		},
	},
	["Sejuani"] = {
		["SejuaniArcticAssault"] = {
			charName = "Sejuani",
			slot = 0,
			type = "line",
			speed = 1000,
			range = 620,
			delay = 0.25,
			radius = 75,
			collision = false
		},
		["SejuaniR"] = {
			charName = "Sejuani",
			slot = 3,
			type = "line",
			speed = 1600,
			range = 1300,
			delay = 0.25,
			radius = 120,
			collision = false
		},
	},
	["Shen"] = {
		["ShenE"] = {
			charName = "Shen",
			slot = 2,
			type = "line",
			speed = 1300,
			range = 600,
			delay = 0.01,
			radius = 50,
			collision = false
		},
	},
	["Shyvana"] = {
		["ShyvanaFireball"] = {
			charName = "Shyvana",
			slot = 2,
			type = "line",
			speed = 1575,
			range = 925,
			delay = 0.25,
			radius = 60,
			collision = false
		},
		["ShyvanaFireballDragon2"] = {
			charName = "Shyvana",
			slot = 2,
			type = "line",
			speed = 1575,
			range = 975,
			delay = 0.333,
			radius = 60,
			collision = false
		},
		["ShyvanaTransformLeap"] = {
			charName = "Shyvana",
			slot = 3,
			type = "line",
			speed = 700,
			range = 850,
			delay = 0.25,
			radius = 150,
			collision = false
		},
	},
	["Sion"] = {
		["SionQ"] = {
			charName = "Sion",
			slot = 0,
			type = "line",
			speed = math.huge,
			range = 750,
			delay = 2,
			radius = 150,
			collision = false
		},
		["SionE"] = {
			charName = "Sion",
			slot = 2,
			type = "line",
			speed = 1800,
			range = 800,
			delay = 0.25,
			radius = 80,
			collision = false
		},
	},
	["Sivir"] = {
		["SivirQ"] = {
			charName = "Sivir",
			slot = 0,
			type = "line",
			speed = 1350,
			range = 1250,
			delay = 0.25,
			radius = 90,
			collision = false
		},
	},
	["Skarner"] = {
		["SkarnerFractureMissile"] = {
			charName = "Skarner",
			slot = 2,
			type = "line",
			speed = 1500,
			range = 1000,
			delay = 0.25,
			radius = 70,
			collision = false
		},
	},
	["Sona"] = {
		["SonaR"] = {
			charName = "Sona",
			slot = 3,
			type = "line",
			speed = 2400,
			range = 1000,
			delay = 0.25,
			radius = 140,
			collision = false
		},
	},
	["Soraka"] = {
		["SorakaQ"] = {
			charName = "Soraka",
			slot = 0,
			type = "circle",
			speed = 1150,
			range = 810,
			delay = 0.25,
			radius = 235,
			collision = false
		},
	},
	["Swain"] = {
		["SwainQ"] = {
			charName = "Swain",
			slot = 0,
			type = "cone",
			speed = 5000,
			range = 725,
			delay = 0.25,
			radius = 0,
			angle = 45,
			collision = false
		},
		["SwainW"] = {
			charName = "Swain",
			slot = 1,
			type = "circle",
			speed = math.huge,
			range = 3500,
			delay = 1.5,
			radius = 300,
			collision = false
		},
		["SwainE"] = {
			charName = "Swain",
			slot = 2,
			type = "line",
			speed = 1800,
			range = 850,
			delay = 0.25,
			radius = 85,
			collision = false
		},
	},
	["Sylas"] = {
		["SylasQ"] = {
			charName = "Sylas",
			slot = 0,
			type = "line",
			speed = 1800,
			range = 700,
			delay = 0.4,
			radius = 70,
			collision = false
		},
		["SylasE2"] = {
			charName = "Sylas",
			slot = 2,
			type = "line",
			speed = 1800,
			range = 750,
			delay = 0.25,
			radius = 30,
			collision = true
		},
	},
	["Syndra"] = {
		["SyndraQ"] = {
			charName = "Syndra",
			slot = 0,
			type = "circle",
			speed = math.huge,
			range = 825,
			delay = 0.65,
			radius = 180,
			collision = false
		},
		["SyndraWCast"] = {
			charName = "Syndra",
			slot = 1,
			type = "circle",
			speed = 1500,
			range = 950,
			delay = 0.25,
			radius = 210,
			collision = false
		},
		["SyndraE"] = {
			charName = "Syndra",
			slot = 2,
			type = "cone",
			speed = 1600,
			range = 700,
			delay = 0.25,
			radius = 0,
			angle = 40,
			collision = false
		},
		["SyndraEQ"] = {
			charName = "Syndra",
			slot = 2,
			type = "circle",
			speed = 2000,
			range = 1300,
			delay = 0.01,
			radius = 55,
			collision = false
		},
	},
	["TahmKench"] = {
		["TahmKenchQ"] = {
			charName = "TahmKench",
			slot = 0,
			type = "line",
			speed = 2800,
			range = 800,
			delay = 0.25,
			radius = 70,
			collision = true
		},
	},
	["Taliyah"] = {
		["TaliyahQ"] = {
			charName = "Taliyah",
			slot = 0,
			type = "line",
			speed = 3600,
			range = 1000,
			delay = 0.25,
			radius = 100,
			collision = true
		},
		["TaliyahWVC"] = {
			charName = "Taliyah",
			slot = 1,
			type = "circle",
			speed = math.huge,
			range = 900,
			delay = 0.85,
			radius = 150,
			collision = false
		},
		["TaliyahE"] = {
			charName = "Taliyah",
			slot = 2,
			type = "cone",
			speed = 2000,
			range = 800,
			delay = 0.45,
			radius = 0,
			angle = 80,
			collision = false
		},
	},
	["Talon"] = {
		["TalonW"] = {
			charName = "Talon",
			slot = 1,
			type = "cone",
			speed = 2500,
			range = 650,
			delay = 0.25,
			radius = 75,
			angle = 26,
			collision = false
		},
		["TalonR"] = {
			charName = "Talon",
			slot = 3,
			type = "circle",
			speed = 2500,
			range = 550,
			delay = 0.25,
			radius = 550,
			collision = false
		},
		["TalonRReturn"] = {
			charName = "Talon",
			slot = 3,
			type = "circle",
			speed = 2500,
			range = 550,
			delay = 0.25,
			radius = 550,
			collision = false
		},
	},
	["Taric"] = {
		["TaricE"] = {
			charName = "Taric",
			slot = 2,
			type = "line",
			speed = math.huge,
			range = 700,
			delay = 0.1,
			radius = 100,
			collision = false
		},
	},
	["Thresh"] = {
		["ThreshQ"] = {
			charName = "Thresh",
			slot = 2,
			type = "line",
			speed = 1900,
			range = 1100,
			delay = 0.5,
			radius = 70,
			collision = true
		},
		["ThreshE"] = {
			charName = "Thresh",
			slot = 2,
			type = "line",
			speed = math.huge,
			range = 500,
			delay = 0.389,
			radius = 110,
			collision = false
		},
	},
	["Tristana"] = {
		["TristanaW"] = {
			charName = "Tristana",
			slot = 1,
			type = "circle",
			speed = 1100,
			range = 900,
			delay = 0.25,
			radius = 300,
			collision = false
		},
	},
	["Tryndamere"] = {
		["TryndamereE"] = {
			charName = "Tryndamere",
			slot = 2,
			type = "line",
			speed = 900,
			range = 650,
			delay = 0.5,
			radius = 160,
			collision = false
		},
	},
	["TwistedFate"] = {
		["WildCards"] = {
			charName = "TwistedFate",
			slot = 0,
			type = "threeway",
			speed = 1000,
			range = 1450,
			delay = 0.25,
			radius = 40,
			angle = 28,
			collision = false
		},
	},
	["Urgot"] = {
		["UrgotQ"] = {
			charName = "Urgot",
			slot = 0,
			type = "circle",
			speed = math.huge,
			range = 800,
			delay = 0.6,
			radius = 180,
			collision = false
		},
		["UrgotE"] = {
			charName = "Urgot",
			slot = 2,
			type = "line",
			speed = 1500,
			range = 475,
			delay = 0.45,
			radius = 100,
			collision = false
		},
		["UrgotR"] = {
			charName = "Urgot",
			slot = 3,
			type = "line",
			speed = 3200,
			range = 1600,
			delay = 0.4,
			radius = 80,
			collision = false
		},
	},
	["Varus"] = {
		["VarusQMissile"] = {
			charName = "Varus",
			slot = 0,
			type = "line",
			speed = 1900,
			range = 1525,
			delay = 0,
			radius = 70,
			collision = false
		},
		["VarusE"] = {
			charName = "Varus",
			slot = 2,
			type = "line",
			speed = 1500,
			range = 925,
			delay = 0.242,
			radius = 260,
			collision = false
		},
		["VarusR"] = {
			charName = "Varus",
			slot = 3,
			type = "line",
			speed = 1950,
			range = 1200,
			delay = 0.25,
			radius = 120,
			collision = false
		},
	},
	["Veigar"] = {
		["VeigarBalefulStrike"] = {
			charName = "Veigar",
			slot = 0,
			type = "line",
			speed = 2200,
			range = 900,
			delay = 0.25,
			radius = 70,
			collision = true
		},
		["VeigarDarkMatterCastLockout"] = {
			charName = "Veigar",
			slot = 1,
			type = "circle",
			speed = math.huge,
			range = 900,
			delay = 1.25,
			radius = 200,
			collision = false
		},
	},
	["Velkoz"] = {
		["VelkozQ"] = {
			charName = "Velkoz",
			slot = 0,
			type = "line",
			speed = 1300,
			range = 1050,
			delay = 0.25,
			radius = 50,
			collision = true
		},
		["VelkozW"] = {
			charName = "Velkoz",
			slot = 1,
			type = "line",
			speed = 1700,
			range = 1050,
			delay = 0.25,
			radius = 87.5,
			collision = false
		},
		["VelkozE"] = {
			charName = "Velkoz",
			slot = 2,
			type = "circle",
			speed = math.huge,
			range = 800,
			delay = 0.8,
			radius = 185,
			collision = false
		},
	},
	["Vi"] = {
		["ViQ"] = {
			charName = "Vi",
			slot = 0,
			type = "line",
			speed = 1500,
			range = 750,
			delay = 0.01,
			radius = 90,
			collision = false
		},
	},
	["Viktor"] = {
		["ViktorDeathRay"] = {
			charName = "Viktor",
			slot = 2,
			type = "line",
			speed = math.huge,
			range = 710,
			delay = 0.25,
			radius = 80,
			collision = false
		},
		["ViktorEExplosion"] = {
			charName = "Viktor",
			slot = 2,
			type = "line",
			speed = math.huge,
			range = 710,
			delay = 1,
			radius = 80,
			collision = false
		},
	},
	["Vladimir"] = {
		["VladimirE"] = {
			charName = "Vladimir",
			slot = 2,
			type = "line",
			speed = 4000,
			range = 550,
			delay = 0.25,
			radius = 60,
			collision = false
		},
		["VladimirHemoplague"] = {
			charName = "Vladimir",
			slot = 3,
			type = "circle",
			speed = math.huge,
			range = 700,
			delay = 0.01,
			radius = 375,
			collision = false
		},
	},
	["Warwick"] = {
		["WarwickR"] = {
			charName = "Warwick",
			slot = 3,
			type = "line",
			speed = 1800,
			range = 3000,
			delay = 0.1,
			radius = 55,
			collision = false
		},
	},
	["MonkeyKing"] = {
		["MonkeyKingSpinToWin"] = {
			charName = "MonkeyKing",
			slot = 3,
			type = "circle",
			speed = math.huge,
			range = 320,
			delay = 0.1,
			radius = 320,
			collision = false
		},
	},
	["Xayah"] = {
		["XayahQ"] = {
			charName = "Xayah",
			slot = 0,
			type = "line",
			speed = 2075,
			range = 1100,
			delay = 0.5,
			radius = 45,
			collision = false
		},
	},
	["Xerath"] = {
		["XerathArcanopulse2"] = {
			charName = "Xerath",
			slot = 1,
			type = "circle",
			speed = math.huge,
			range = 1300,
			delay = 0.53,
			radius = 100,
			collision = false
		},
		["XerathArcaneBarrage2"] = {
			charName = "Xerath",
			slot = 1,
			type = "circle",
			speed = math.huge,
			range = 1000,
			delay = 0.75,
			radius = 235,
			collision = false
		},
		["XerathMageSpear"] = {
			charName = "Xerath",
			slot = 2,
			type = "line",
			speed = 1400,
			range = 1050,
			delay = 0.2,
			radius = 60,
			collision = true
		},
	},
	["XinZhao"] = {
		["XinZhaoW"] = {
			charName = "XinZhao",
			slot = 1,
			type = "line",
			speed = 5000,
			range = 900,
			delay = 0.5,
			radius = 40,
			collision = false
		},
	},
	["Yasuo"] = {
		["YasuoQ1Wrapper"] = {
			charName = "Yasuo",
			slot = 0,
			type = "line",
			speed = math.huge,
			range = 520,
			delay = 0.4,
			radius = 55,
			collision = false
		},
		["YasuoQ2Wrapper"] = {
			charName = "Yasuo",
			slot = 0,
			type = "line",
			speed = math.huge,
			range = 520,
			delay = 0.4,
			radius = 55,
			collision = false
		},
		["YasuoQ3"] = {
			charName = "Yasuo",
			slot = 0,
			type = "line",
			speed = 1200,
			range = 1100,
			delay = 0.3,
			radius = 90,
			collision = false
		},
	},
	["Yorick"] = {
		["YorickE"] = {
			charName = "Yorick",
			slot = 2,
			type = "line",
			speed = math.huge,
			range = 550,
			delay = 0.5,
			radius = 120,
			collision = false
		},
	},
	["Zac"] = {
		["ZacQ"] = {
			charName = "Zac",
			slot = 0,
			type = "line",
			speed = 2800,
			range = 800,
			delay = 0.33,
			radius = 120,
			collision = false
		},
		["ZedE"] = {
			charName = "Zac",
			slot = 2,
			type = "circle",
			speed = math.huge,
			range = 1300,
			delay = 0.01,
			radius = 290,
			collision = false
		},
	},
	["Zed"] = {
		["ZedQ"] = {
			charName = "Zed",
			slot = 0,
			type = "line",
			speed = 1700,
			range = 900,
			delay = 0.25,
			radius = 50,
			collision = false
		},
	},
	["Ziggs"] = {
		["ZiggsQ"] = {
			charName = "Ziggs",
			slot = 0,
			type = "circle",
			speed = 1700,
			range = 850,
			delay = 0.25,
			radius = 120,
			collision = true
		},
		["ZiggsW"] = {
			charName = "Ziggs",
			slot = 1,
			type = "circle",
			speed = 1750,
			range = 1000,
			delay = 0.25,
			radius = 240,
			collision = false
		},
		["ZiggsE"] = {
			charName = "Ziggs",
			slot = 2,
			type = "circle",
			speed = 1800,
			range = 900,
			delay = 0.25,
			radius = 250,
			collision = false
		},
		["ZiggsR"] = {
			charName = "Ziggs",
			slot = 3,
			type = "circle",
			speed = 1550,
			range = 5000,
			delay = 0.375,
			radius = 480,
			collision = false
		},
	},
	["Zilean"] = {
		["ZileanQ"] = {
			charName = "Zilean",
			slot = 0,
			type = "circle",
			speed = math.huge,
			range = 900,
			delay = 0.8,
			radius = 150,
			collision = false
		},
	},
	["Zoe"] = {
		["ZoeQMissile"] = {
			charName = "Zoe",
			slot = 0,
			type = "line",
			speed = 2500,
			range = 800,
			delay = 0.25,
			radius = 100,
			collision = true
		},
		["ZoeQRecast"] = {
			charName = "Zoe",
			slot = 2,
			type = "line",
			speed = 2370,
			range = 3000,
			delay = 0.01,
			radius = 100,
			collision = true
		},
		["ZoeE"] = {
			charName = "Zoe",
			slot = 2,
			type = "line",
			speed = 1700,
			range = 800,
			delay = 0.3,
			radius = 50,
			collision = true
		},
	},
	["Zyra"] = {
		["ZyraQ"] = {
            charName = "Zyra",
            slot = 0,
            type = "rect",
            speed = math.huge,
            range = 800,
            delay = 0.825,
            radius = 200,
            danger = 1,
            cc = false,
            collision = false,
            windwall = false
        },
		["ZyraE"] = {
			charName = "Zyra",
			slot = 2,
			type = "line",
			speed = 1150,
			range = 1100,
			delay = 0.25,
			radius = 70,
			collision = false
		},
		["ZyraR"] = {
			charName = "Zyra",
			slot = 3,
			type = "circle",
			speed = math.huge,
			range = 700,
			delay = 2,
			radius = 500,
			collision = false
		},
	},
}

function database.VectorPointProjectionOnLineSegment(v1, v2, v)
    local cx, cy, ax, ay, bx, by = v.x, v.y, v1.x, v1.y, v2.x, v2.y
    local rL = ((cx - ax) * (bx - ax) + (cy - ay) * (by - ay)) / ((bx - ax) ^ 2 + (by - ay) ^ 2)
    local pointLine = vec2(ax + rL * (bx - ax), ay + rL * (by - ay))
    local rS = rL < 0 and 0 or (rL > 1 and 1 or rL)
    local isOnSegment = rS == rL
    local pointSegment = isOnSegment and pointLine or vec2(ax + rS * (bx - ax), ay + rS * (by - ay))
    return pointSegment, pointLine, isOnSegment
end

function database.IsCrossHit(sourcePos, endPos)
	if not sourcePos or not endPos then
		return false
	end

	local x1 = endPos + vec2(200, 200)
	local x2 = endPos + vec2(-200, -200)
	local x3 = endPos + vec2(200, -200)
	local x4 = endPos + vec2(-200, 200)

	local ps1, pl1, line1 = database.VectorPointProjectionOnLineSegment(x1, x2, sourcePos)
	local ps2, pl2, line2 = database.VectorPointProjectionOnLineSegment(x3, x4, sourcePos)

	if line1 and sourcePos:dist(ps1) < 90 then
		return true
	elseif line2 and sourcePos:dist(ps2) < 90 then
		return true
	end
end

function database.IsCircleHit(startPos, endPos, spellData, source)
	if not startPos or not endPos or not spellData or not source or not source.pos or source.isDead then
		return false
	end
	if not spellData.type or spellData.type ~= "circle" then
		return false
	end
	if not spellData.range or not spellData.radius then
		return false
	end
	local fromPos = source.pos2D
	if startPos:dist(fromPos) > spellData.range then
		return false
	end
	if fromPos:distSqr(endPos) > (spellData.radius + 60) ^ 2 then
		return false
	end
	return true
end

function database.IsConeHit(startPos, endPos, spellData, source)
	if not startPos or not endPos or not spellData or not source or not source.pos or source.isDead then
		return false
	end
	if not spellData.type or spellData.type ~= "circle" then
		return false
	end
	if not spellData.range or not spellData.radius then
		return false
	end
	local fromPos = source.pos2D
	if startPos:dist(fromPos) > spellData.range then
		return false
	end
	local bW = startPos + (endPos - startPos):norm() * spellData.range
	local bU, b1, b2 = database.VectorPointProjectionOnLineSegment(startPos, endPos, source.pos2D)
	if not bU or (bU and fromPos:distSqr(bU) > (spellData.radius + 60 * 1.5) ^ 2) then
		return false
	end
	return true
end

function database.IsLineHit(startPos, endPos, spellData, source)
	if not startPos or not endPos or not spellData or not source or not source.pos or source.isDead then
		return false
	end
	if not spellData.type or spellData.type ~= "line" then
		return false
	end
	if not spellData.range or not spellData.radius then
		return false
	end
	local fromPos = source.pos2D
	if startPos:dist(fromPos) > spellData.range then
		return false
	end
	local bU, b1, b2 = database.VectorPointProjectionOnLineSegment(startPos, endPos, source.pos2D)
	if not bU or (bU and fromPos:distSqr(bU) > (spellData.radius + 60 * 1.5) ^ 2) then
		return false
	end
	return true
end

function database.IsRecHit(startPos, endPos, spellData, source)
	if not startPos or not endPos or not spellData or not source or not source.pos or source.isDead then
		return false
	end
	if not spellData.type or spellData.type ~= "rec" then
		return false
	end
	if not spellData.range or not spellData.radius then
		return false
	end
	local fromPos = source.pos2D
	if startPos:dist(fromPos) > spellData.range then
		return false
	end
	local bV = endPos - ((endPos - startPos):norm()):perp1() * spellData.radius
	local bW = endPos + ((endPos + startPos):norm()):perp1() * spellData.radius
	local bU, b1, b2 = database.VectorPointProjectionOnLineSegment(bV, bW, source.pos2D)
	if not bU or (bU and fromPos:distSqr(bU) > (spellData.radius + 60 * 1.5) ^ 2) then
		return false
	end
	return true
end

function database.IsAutoAttack(name)
	if name then
        local lowerName = string.lower(name)
        if lowerName:find("attack") and not NoAttacks[lowerName] then
            return true
        end
        if SpecialAttacks[lowerName] then
            return true
        end
    end
    return false
end

return database

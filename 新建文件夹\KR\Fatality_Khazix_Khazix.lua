

local ove_0_6 = module.load(header.id, "common")
local ove_0_7 = module.load(header.id, "menu")
local ove_0_8 = module.internal("orb")
local ove_0_9 = module.internal("TS")
local ove_0_10 = module.internal("pred")
local ove_0_11 = module.internal("damagelib")
local ove_0_12 = module.seek("evade")
local ove_0_13 = graphics.create_effect(graphics.CIRCLE_RAINBOW_BOLD)
local ove_0_14 = graphics.create_effect(graphics.CIRCLE_RAINBOW_BOLD)
local ove_0_15 = graphics.create_effect(graphics.CIRCLE_RAINBOW_BOLD)
local ove_0_16 = {
	delay = 0.25,
	range = 325
}
local ove_0_17 = {
	speed = 1700,
	range = 1025,
	delay = 0.25,
	boundingRadiusMod = 1,
	width = 70,
	collision = {
		minion = true,
		hero = false,
		wall = true
	}
}
local ove_0_18 = {
	radius = 150,
	range = 700,
	delay = 0.25,
	boundingRadiusMod = 0,
	width = 150,
	speed = math.huge,
	collision = {
		minion = false,
		hero = false,
		wall = false
	}
}
local ove_0_19 = {
	delay = 0,
	range = 0
}

local function ove_0_20(arg_5_0)
	local slot_5_0 = 0
	local slot_5_1 = {
		80,
		105,
		130,
		155,
		180
	}

	if player:spellSlot(0).level > 0 then
		local slot_5_2 = slot_5_1[player:spellSlot(0).level] + 1.1 * player.bonusAd

		slot_5_0 = ove_0_11.calc_physical_damage(player, arg_5_0, slot_5_2)
	end

	return slot_5_0
end

local function ove_0_21(arg_6_0)
	local slot_6_0 = 0
	local slot_6_1 = {
		147,
		199,
		252,
		304,
		357
	}

	if player:spellSlot(0).level > 0 then
		local slot_6_2 = slot_6_1[player:spellSlot(0).level] + 2.31 * player.bonusAd

		slot_6_0 = ove_0_11.calc_physical_damage(player, arg_6_0, slot_6_2)
	end

	return slot_6_0
end

local function ove_0_22(arg_7_0)
	local slot_7_0 = 0
	local slot_7_1 = {
		85,
		115,
		145,
		175,
		205
	}

	if player:spellSlot(1).level > 0 then
		local slot_7_2 = slot_7_1[player:spellSlot(1).level] + 1 * player.bonusAd

		slot_7_0 = ove_0_11.calc_physical_damage(player, arg_7_0, slot_7_2)
	end

	return slot_7_0
end

local function ove_0_23(arg_8_0)
	local slot_8_0 = 0
	local slot_8_1 = {
		65,
		100,
		135,
		170,
		205
	}

	if player:spellSlot(2).level > 0 then
		local slot_8_2 = slot_8_1[player:spellSlot(2).level] + 0.2 * player.bonusAd

		slot_8_0 = ove_0_11.calc_physical_damage(player, arg_8_0, slot_8_2)
	end

	return slot_8_0
end

local function ove_0_24(arg_9_0, arg_9_1, arg_9_2)
	if arg_9_1 and not arg_9_1.isDead and arg_9_1.isVisible and arg_9_1.isTargetable and not arg_9_1.buff[17] then
		if arg_9_2 > ove_0_16.range + arg_9_1.boundingRadius or arg_9_1.buff.rocketgrab or arg_9_1.buff.bansheesveil or arg_9_1.buff.itemmagekillerveil or arg_9_1.buff.nocturneshroudofdarkness or arg_9_1.buff.sivire or arg_9_1.buff.fioraw or arg_9_1.buff.blackshield then
			return
		end

		arg_9_0.object = arg_9_1

		return true
	end
end

local function ove_0_25()
	return ove_0_9.get_result(ove_0_24).object
end

local function ove_0_26(arg_11_0, arg_11_1, arg_11_2)
	if arg_11_1 and not arg_11_1.isDead and arg_11_1.isVisible and arg_11_1.isTargetable and not arg_11_1.buff[17] then
		if arg_11_2 > ove_0_17.range or arg_11_1.buff.rocketgrab or arg_11_1.buff.bansheesveil or arg_11_1.buff.itemmagekillerveil or arg_11_1.buff.nocturneshroudofdarkness or arg_11_1.buff.sivire or arg_11_1.buff.fioraw or arg_11_1.buff.blackshield then
			return
		end

		arg_11_0.object = arg_11_1

		return true
	end
end

local function ove_0_27()
	return ove_0_9.get_result(ove_0_26).object
end

local function ove_0_28(arg_13_0, arg_13_1, arg_13_2)
	if arg_13_1 and not arg_13_1.isDead and arg_13_1.isVisible and arg_13_1.isTargetable and not arg_13_1.buff[17] then
		if arg_13_2 > ove_0_18.range or arg_13_1.buff.rocketgrab or arg_13_1.buff.bansheesveil or arg_13_1.buff.itemmagekillerveil or arg_13_1.buff.nocturneshroudofdarkness or arg_13_1.buff.sivire or arg_13_1.buff.fioraw or arg_13_1.buff.blackshield then
			return
		end

		arg_13_0.object = arg_13_1

		return true
	end
end

local function ove_0_29()
	return ove_0_9.get_result(ove_0_28).object
end

local function ove_0_30()
	local slot_15_0 = ove_0_7.khazixmenu.Combo.qsettings.useq:get()
	local slot_15_1 = ove_0_25()

	if ove_0_8.core.is_winding_up_attack() == true then
		return
	end

	if slot_15_0 and player:spellSlot(0).state == 0 and slot_15_1 and ove_0_6.IsValidTarget(slot_15_1) then
		player:castSpell("obj", 0, slot_15_1)
	end
end

local function ove_0_31()
	local slot_16_0 = ove_0_7.khazixmenu.Combo.wsettings.usew:get()
	local slot_16_1 = ove_0_7.khazixmenu.Combo.wsettings.slowpredw:get()
	local slot_16_2 = ove_0_27()

	if ove_0_8.core.is_spell_locked() then
		return
	end

	if slot_16_0 and player:spellSlot(1).state == 0 and slot_16_2 and ove_0_6.IsValidTarget(slot_16_2) then
		local slot_16_3 = ove_0_10.linear.get_prediction(ove_0_17, slot_16_2)

		if slot_16_1 and ove_0_6.SlowPredLinear(ove_0_17, slot_16_3, slot_16_2, ove_0_17.range) and slot_16_3 and slot_16_3.startPos:dist(slot_16_3.endPos) < ove_0_17.range and not ove_0_10.collision.get_prediction(ove_0_17, slot_16_3, slot_16_2) then
			player:castSpell("pos", 1, vec3(slot_16_3.endPos.x, slot_16_2.y, slot_16_3.endPos.y))
		end

		if not slot_16_1 and slot_16_3 and slot_16_3.startPos:dist(slot_16_3.endPos) < ove_0_17.range and not ove_0_10.collision.get_prediction(ove_0_17, slot_16_3, slot_16_2) then
			player:castSpell("pos", 1, vec3(slot_16_3.endPos.x, slot_16_2.y, slot_16_3.endPos.y))
		end
	end
end

local function ove_0_32()
	local slot_17_0 = ove_0_7.khazixmenu.Combo.esettings.usee:get()
	local slot_17_1 = ove_0_7.khazixmenu.Combo.esettings.slowprede:get()
	local slot_17_2 = ove_0_29()
	local slot_17_3 = ove_0_7.khazixmenu.Combo.esettings.targetscanrange:get()

	if ove_0_8.core.is_spell_locked() then
		return
	end

	if not ove_0_7.khazixmenu.Keys.divetoggle:get() and ove_0_6.IsUnderTurretEnemy(slot_17_2) then
		return
	end

	if ove_0_7.khazixmenu.Combo.esettings.noqrange:get() and slot_17_2 and ove_0_6.IsValidTarget(slot_17_2) and slot_17_2.pos:dist(player.pos) <= ove_0_16.range then
		return
	end

	if ove_0_7.khazixmenu.Combo.esettings.onlywithq:get() and player:spellSlot(0).state ~= 0 then
		return
	end

	if ove_0_6.HealthPercent(player) < ove_0_7.khazixmenu.Combo.esettings.ehp:get() then
		return
	end

	if slot_17_2 and ove_0_6.IsValidTarget(slot_17_2) and slot_17_2.pos:countEnemies(slot_17_3) > ove_0_7.khazixmenu.Combo.esettings.maxtargets:get() then
		return
	end

	if slot_17_0 and player:spellSlot(2).state == 0 and slot_17_2 and ove_0_6.IsValidTarget(slot_17_2) then
		local slot_17_4 = ove_0_10.circular.get_prediction(ove_0_18, slot_17_2)

		if slot_17_1 and ove_0_6.SlowPredcircular(ove_0_18, slot_17_4, slot_17_2, ove_0_18.range) and slot_17_4 and slot_17_4.startPos:dist(slot_17_4.endPos) < ove_0_18.range and not ove_0_10.collision.get_prediction(ove_0_18, slot_17_4, slot_17_2) then
			player:castSpell("pos", 2, vec3(slot_17_4.endPos.x, slot_17_2.y, slot_17_4.endPos.y))
		end

		if not slot_17_1 and slot_17_4 and slot_17_4.startPos:dist(slot_17_4.endPos) < ove_0_18.range and not ove_0_10.collision.get_prediction(ove_0_18, slot_17_4, slot_17_2) then
			player:castSpell("pos", 2, vec3(slot_17_4.endPos.x, slot_17_2.y, slot_17_4.endPos.y))
		end
	end
end

local function ove_0_33()
	local slot_18_0 = ove_0_7.khazixmenu.Killsteal.ksQ:get()
	local slot_18_1 = ove_0_25()

	if slot_18_0 and player:spellSlot(0).state == 0 and slot_18_1 and ove_0_6.IsValidTarget(slot_18_1) and (slot_18_1.pos:countEnemies(375) > 0 or ove_0_6.CountMinionsNearPos(slot_18_1.pos, 375) > 0) and slot_18_1.pos:dist(player.pos) <= ove_0_16.range and ove_0_20(slot_18_1) > slot_18_1.health then
		player:castSpell("obj", 0, slot_18_1)
		print("[Fatality] Casting Q on Non Isolated Target to Killsteal")
	end
end

local function ove_0_34()
	local slot_19_0 = ove_0_7.khazixmenu.Killsteal.ksQ:get()
	local slot_19_1 = ove_0_25()

	if slot_19_0 and player:spellSlot(0).state == 0 and slot_19_1 and ove_0_6.IsValidTarget(slot_19_1) and ove_0_6.CountMinionsNearPos(slot_19_1.pos, 375) == 0 and slot_19_1.pos:dist(player.pos) <= ove_0_16.range and ove_0_21(slot_19_1) > slot_19_1.health then
		player:castSpell("obj", 0, slot_19_1)
		print("[Fatality] Casting Q on Isolated Target to Killsteal")
	end
end

local function ove_0_35()
	local slot_20_0 = ove_0_7.khazixmenu.Killsteal.ksW:get()
	local slot_20_1 = ove_0_27()

	if slot_20_0 and player:spellSlot(1).state == 0 and slot_20_1 and ove_0_6.IsValidTarget(slot_20_1) and ove_0_22(slot_20_1) > slot_20_1.health then
		local slot_20_2 = ove_0_10.linear.get_prediction(ove_0_17, slot_20_1)

		if slot_20_2.startPos:dist(slot_20_2.endPos) < ove_0_17.range and not ove_0_10.collision.get_prediction(ove_0_17, slot_20_2, slot_20_1) then
			player:castSpell("pos", 1, vec3(slot_20_2.endPos.x, slot_20_1.y, slot_20_2.endPos.y))
			print("[Fatality] Casting W to Killsteal")
		end
	end
end

local function ove_0_36()
	local slot_21_0 = ove_0_7.khazixmenu.Killsteal.ksE:get()
	local slot_21_1 = ove_0_29()

	if slot_21_0 and player:spellSlot(2).state == 0 and slot_21_1 and ove_0_6.IsValidTarget(slot_21_1) and ove_0_23(slot_21_1) > slot_21_1.health then
		local slot_21_2 = ove_0_10.circular.get_prediction(ove_0_18, slot_21_1)

		if slot_21_2.startPos:dist(slot_21_2.endPos) < ove_0_18.range and not ove_0_10.collision.get_prediction(ove_0_18, slot_21_2, slot_21_1) then
			player:castSpell("pos", 2, vec3(slot_21_2.endPos.x, slot_21_1.y, slot_21_2.endPos.y))
			print("[Fatality] Casting E to Killsteal")
		end
	end
end

local function ove_0_37()
	if not ove_0_7.khazixmenu.Keys.farmtoggle:get() then
		return
	end

	for iter_22_0 = 0, objManager.minions.size[TEAM_ENEMY] - 1 do
		local slot_22_0 = objManager.minions[TEAM_ENEMY][iter_22_0]
		local slot_22_1 = ove_0_7.khazixmenu.Clear.LcW:get()

		if slot_22_0 and ove_0_6.IsValidTarget(slot_22_0) and not slot_22_0.name:find("Ward") and slot_22_1 and slot_22_0.pos:dist(player.pos) <= ove_0_17.range then
			player:castSpell("pos", 1, slot_22_0.pos)
		end
	end
end

local function ove_0_38()
	if not ove_0_7.khazixmenu.Keys.farmtoggle:get() then
		return
	end

	if player.pos:countEnemies(ove_0_17.range) > 0 then
		return
	end

	for iter_23_0 = 0, objManager.minions.size[TEAM_NEUTRAL] - 1 do
		local slot_23_0 = objManager.minions[TEAM_NEUTRAL][iter_23_0]
		local slot_23_1 = ove_0_7.khazixmenu.Clear.JcQ:get()
		local slot_23_2 = ove_0_7.khazixmenu.Clear.JcW:get()

		if slot_23_0 and ove_0_6.IsValidTarget(slot_23_0) and not slot_23_0.name:find("Ward") then
			if slot_23_1 and slot_23_0.pos:dist(player.pos) <= ove_0_16.range then
				player:castSpell("obj", 0, slot_23_0)
			end

			if slot_23_2 and slot_23_0.pos:dist(player.pos) <= ove_0_17.range then
				player:castSpell("pos", 1, slot_23_0.pos)
			end
		end
	end
end

local function ove_0_39()
	if ove_0_8.core.is_winding_up_attack() == true then
		return
	end

	for iter_24_0 = 0, objManager.minions.size[TEAM_ENEMY] - 1 do
		local slot_24_0 = objManager.minions[TEAM_ENEMY][iter_24_0]
		local slot_24_1 = ove_0_7.khazixmenu.Clear.LhQ:get()

		if slot_24_0 and ove_0_6.IsValidTarget(slot_24_0) and not slot_24_0.name:find("Ward") and slot_24_1 and slot_24_0.pos:dist(player.pos) <= ove_0_16.range then
			local slot_24_2 = player:spellSlot(0)

			if slot_24_0.health <= ove_0_20(slot_24_0) then
				player:castSpell("obj", 0, slot_24_0)
			end
		end
	end
end

cb.add(cb.tick, function()
	if ove_0_7.khazixmenu.Keys.stogglebar:get() then
		ove_0_32()
		ove_0_30()
		ove_0_31()
	end

	if ove_0_7.khazixmenu.Keys.clearkey:get() then
		ove_0_39()
		ove_0_37()
		ove_0_38()
	end

	if ove_0_7.khazixmenu.Keys.lasthitkey:get() then
		ove_0_39()
	end

	ove_0_33()
	ove_0_34()
	ove_0_35()
	ove_0_36()

	if player:spellSlot(2).name == "KhazixELong" then
		ove_0_18.range = 900
	end

	if player:spellSlot(0).name == "KhazixQLong" then
		ove_0_16.range = 375
	end
end)
cb.add(cb.draw, function()
	local slot_26_0 = ove_0_7.khazixmenu.Draws.drawQ:get()
	local slot_26_1 = ove_0_7.khazixmenu.Draws.drawW:get()
	local slot_26_2 = ove_0_7.khazixmenu.Draws.drawE:get()
	local slot_26_3 = ove_0_7.khazixmenu.Draws.circlemode:get()

	if player.isOnScreen then
		if slot_26_0 and player:spellSlot(0).level > 0 then
			if slot_26_3 == 1 then
				ove_0_13:hide()
				graphics.draw_circle(player.pos, ove_0_16.range, 2, graphics.argb(255, 255, 255, 255), 100)
			end

			if slot_26_3 == 2 then
				ove_0_13:show()
				ove_0_13:update_circle(player.pos, ove_0_16.range, 2, graphics.argb(255, 255, 255, 255))
			end
		end

		if slot_26_1 and player:spellSlot(1).level > 0 then
			if slot_26_3 == 1 then
				ove_0_14:hide()
				graphics.draw_circle(player.pos, ove_0_17.range, 2, graphics.argb(255, 0, 0, 255), 100)
			end

			if slot_26_3 == 2 then
				ove_0_14:show()
				ove_0_14:update_circle(player.pos, ove_0_17.range, 2, graphics.argb(255, 255, 255, 255))
			end
		end

		if slot_26_2 and player:spellSlot(2).level > 0 then
			if slot_26_3 == 1 then
				ove_0_15:hide()
				graphics.draw_circle(player.pos, ove_0_18.range, 2, graphics.argb(255, 0, 255, 0), 100)
			end

			if slot_26_3 == 2 then
				ove_0_15:show()
				ove_0_15:update_circle(player.pos, ove_0_18.range, 2, graphics.argb(255, 255, 255, 255))
			end
		end

		if ove_0_7.khazixmenu.Draws.drawAA:get() then
			ove_0_6.AATracker()
		end
	end

	if slot_26_3 == 2 then
		if not slot_26_0 then
			ove_0_13:hide()
		end

		if not slot_26_1 then
			ove_0_14:hide()
		end

		if not slot_26_2 then
			ove_0_15:hide()
		end
	end
end)

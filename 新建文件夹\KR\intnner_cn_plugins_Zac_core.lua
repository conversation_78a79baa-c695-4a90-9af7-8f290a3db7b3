

local ove_0_10 = module.internal("orb")
local ove_0_11 = module.seek("evade")
local ove_0_12 = module.load(header.id, "plugins/Zac/menu")
local ove_0_13 = module.load(header.id, "common/common")
local ove_0_14 = module.load(header.id, "plugins/Zac/spells/q")
local ove_0_15 = module.load(header.id, "plugins/Zac/spells/w")
local ove_0_16 = module.load(header.id, "plugins/Zac/spells/e")
local ove_0_17 = module.load(header.id, "plugins/Zac/spells/r")
local ove_0_18 = module.load(header.id, "plugins/Zac/spells/aa")
local ove_0_19 = {
	on_out_of_range = function()
		if ove_0_10.menu.combat.key:get() and not ove_0_17.is_jump() and ove_0_12.combo.e.use_e:get() and ove_0_13.GetPercentHealth(player) >= ove_0_12.combo.e.min_health:get() and ove_0_16.get_action_state() and ove_0_16.invoke_action() then
			return
		end

		if ove_0_10.menu.hybrid.key:get() and not ove_0_17.is_jump() and ove_0_12.harass.e.use_e:get() and ove_0_16.get_action_state() then
			ove_0_16.invoke_action()

			return
		end
	end,
	get_action = function()
		ove_0_16.CastRangeGrowthMax = ({
			1200,
			1350,
			1500,
			1650,
			1800
		})[ove_0_16.slot.level]
		ove_0_16.CastRangeGrowthDuration = ({
			0.9,
			1,
			1.1,
			1.2,
			1.3
		})[ove_0_16.slot.level]

		if ove_0_16.is_charging_with_e() then
			local slot_6_0 = math.max(0, math.min(1, (game.time - ove_0_16.LastCastTime - ove_0_16.CastRangeGrowthStartTime) / ove_0_16.CastRangeGrowthDuration))

			ove_0_16.range = (ove_0_16.CastRangeGrowthMax - ove_0_16.CastRangeGrowthMin) * slot_6_0 + ove_0_16.CastRangeGrowthMin

			ove_0_10.core.set_pause_move(math.huge)
			ove_0_10.core.set_pause_attack(math.huge)

			if ove_0_12.misc.evade.disable_evade_with_e:get() and ove_0_11 then
				ove_0_11.core.set_pause(math.huge)
			end
		else
			ove_0_16.range = ove_0_16.CastRangeGrowthMax
		end

		if not ove_0_16.is_charging_with_e() then
			ove_0_10.core.set_pause_move(0)

			if not ove_0_17.is_jump() then
				ove_0_10.core.set_pause_attack(0)
			end

			if ove_0_12.misc.evade.disable_evade_with_r:get() and not ove_0_17.is_jump() or not ove_0_12.misc.evade.disable_evade_with_r:get() then
				ove_0_11.core.set_pause(0)
			end
		end

		if ove_0_12.combo.r.disable_aa:get() and ove_0_17.last_cast > 0 and game.time < ove_0_17.last_cast + 3 then
			ove_0_10.core.set_pause_attack(math.huge)
		end

		if ove_0_12.misc.evade.disable_evade_with_r:get() and ove_0_17.is_jump() and ove_0_11 then
			ove_0_11.core.set_pause(math.huge)
		end

		if ove_0_12.misc.interrupt.use_r:get() and ove_0_17.is_ready() and ove_0_17.invoke_interrupt_spells() then
			ove_0_17.interrupt_data.owner = false

			return
		end

		if ove_0_12.misc.auto.auto_q:get() and ove_0_14.is_ready() and ove_0_14.invoke_auto_cced() then
			return
		end

		if ove_0_12.misc.auto.use_q_end_dash:get() and ove_0_14.is_ready() and ove_0_14.invoke_q_gapcloser() then
			return
		end

		if ove_0_12.misc.kill.use_q:get() and ove_0_14.is_ready() and ove_0_14.invoke_q_killsteal() then
			return
		end

		if ove_0_12.misc.kill.use_w:get() and ove_0_15.is_ready() and ove_0_15.invoke_w_killsteal() then
			return
		end

		if ove_0_12.flee.flee_key:get() then
			if not ove_0_16.is_charging_with_e() then
				player:move(game.mousePos)
			end

			if ove_0_12.flee.use_e:get() and ove_0_16.is_ready() then
				ove_0_16.invoke_flee_action()

				return
			end
		end

		if ove_0_12.combo.q.use_q2:get() and ove_0_18.get_prediction() then
			ove_0_18.invoke_action()

			return
		end

		if ove_0_10.menu.combat.key:get() and not ove_0_17.is_jump() then
			if ove_0_12.combo.r.use_r:get() and ove_0_17.get_action_state() and ove_0_17.invoke_action() then
				return
			end

			if ove_0_12.combo.e.use_e:get() and ove_0_13.GetPercentHealth(player) >= ove_0_12.combo.e.min_health:get() and ove_0_16.not_can_attack() and ove_0_16.get_action_state() then
				ove_0_16.invoke_action()

				return
			end

			if ove_0_12.combo.w.use_w:get() and ove_0_13.GetPercentHealth(player) >= ove_0_12.combo.w.min_health:get() and not ove_0_16.is_charging_with_e() and ove_0_15.get_action_state() then
				ove_0_15.invoke_action()

				return
			end

			if ove_0_12.combo.q.use_q:get() and ove_0_13.GetPercentHealth(player) >= ove_0_12.combo.q.min_health:get() and not ove_0_16.is_charging_with_e() and ove_0_14.get_action_state() then
				ove_0_14.invoke_action()

				return
			end
		end

		if ove_0_10.menu.hybrid.key:get() and not ove_0_17.is_jump() then
			if ove_0_12.harass.e.use_e:get() and ove_0_16.not_can_attack() and ove_0_16.get_action_state() then
				ove_0_16.invoke_action()

				return
			end

			if ove_0_12.harass.w.use_w:get() and not ove_0_16.is_charging_with_e() and ove_0_15.get_action_state() then
				ove_0_15.invoke_action()

				return
			end

			if ove_0_12.harass.q.use_q:get() and not ove_0_16.is_charging_with_e() and ove_0_14.get_action_state() then
				ove_0_14.invoke_action()

				return
			end
		end

		if ove_0_10.menu.lane_clear.key:get() and ove_0_12.farming.togleFarming:get() then
			if ove_0_12.farming.lane.w.use_w:get() and ove_0_13.GetPercentHealth(player) >= ove_0_12.farming.lane.w.min_health:get() and ove_0_15.is_ready() and ove_0_15.invoke_lane_clear() then
				ove_0_15.invoke_action()

				return
			end

			if ove_0_12.farming.jungle.q.use_q:get() and ove_0_13.GetPercentHealth(player) >= ove_0_12.farming.jungle.q.min_health:get() and ove_0_14.is_ready() then
				ove_0_14.invoke_jungle_clear()

				return
			end

			if ove_0_12.farming.jungle.e.use_e:get() and ove_0_13.GetPercentHealth(player) >= ove_0_12.farming.jungle.e.min_health:get() and ove_0_16.is_ready() then
				ove_0_16.invoke_jungle_clear()

				return
			end

			if ove_0_12.farming.jungle.w.use_w:get() and ove_0_13.GetPercentHealth(player) >= ove_0_12.farming.jungle.w.min_health:get() and ove_0_15.is_ready() and ove_0_15.invoke_jungle_clear() then
				ove_0_15.invoke_action()

				return
			end
		end
	end,
	on_recv_spell = function(arg_7_0)
		if arg_7_0.name == "ZacE" then
			ove_0_16.LastCastTime = game.time
		end

		if arg_7_0.name == "ZacR" then
			ove_0_17.last_cast = game.time
		end
	end
}

ove_0_10.combat.register_f_out_of_range(ove_0_19.on_out_of_range)

return ove_0_19

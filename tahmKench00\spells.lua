

return {
	<PERSON><PERSON><PERSON> = {
		[46] = "E Attack"
	},
	JarvanIV = {
		[3] = true
	},
	Alistar = {
		[1] = true
	},
	Pantheon = {
		[1] = true
	},
	<PERSON>zz = {
		[0] = true
	},
	<PERSON> = {
		[2] = true
	},
	<PERSON><PERSON><PERSON> = {
		[0] = true
	},
	<PERSON> = {
		[0] = true
	},
	<PERSON><PERSON><PERSON><PERSON> = {
		[2] = true
	},
	<PERSON><PERSON> = {
		[2] = true
	},
	<PERSON>King = {
		[2] = true
	},
	<PERSON><PERSON> = {
		[2] = true
	},
	<PERSON><PERSON><PERSON> = {
		[2] = true
	},
	<PERSON> = {
		[0] = true
	},
	<PERSON><PERSON><PERSON> = {
		[2] = function(arg_5_0, arg_5_1, arg_5_2)
			if arg_5_0:distSqr(player.pos) < 422500 and arg_5_2 > arg_5_0:lerp(arg_5_1, 650 / arg_5_1:dist(arg_5_0)):distSqr(player.pos) then
				return true
			end
		end
	},
	Ornn = {
		[2] = function(arg_6_0, arg_6_1, arg_6_2)
			if arg_6_2 > arg_6_0:lerp(arg_6_1, 900 / arg_6_1:dist(arg_6_0)):distSqr(player.pos) then
				return true
			end
		end
	},
	<PERSON><PERSON><PERSON><PERSON> = {
		[2] = function(arg_7_0, arg_7_1, arg_7_2)
			if arg_7_2 > arg_7_0:lerp(arg_7_1, 866 / arg_7_1:dist(arg_7_0)):distSqr(player.pos) then
				return true
			end
		end
	},
	Gragas = {
		[2] = function(arg_8_0, arg_8_1, arg_8_2)
			if arg_8_2 > arg_8_0:lerp(arg_8_1, 866 / arg_8_1:dist(arg_8_0)):distSqr(player.pos) then
				return true
			end
		end
	},
	Shen = {
		[2] = function(arg_9_0, arg_9_1, arg_9_2)
			if arg_9_2 > arg_9_0:lerp(arg_9_1, 626 / arg_9_1:dist(arg_9_0)):distSqr(player.pos) then
				return true
			end
		end
	},
	Tryndamere = {
		[2] = function(arg_10_0, arg_10_1, arg_10_2)
			if arg_10_2 > arg_10_0:lerp(arg_10_1, 656 / arg_10_1:dist(arg_10_0)):distSqr(player.pos) then
				return true
			end
		end
	}
}

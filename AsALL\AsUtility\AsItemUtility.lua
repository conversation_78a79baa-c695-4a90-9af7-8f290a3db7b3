local autoItem = {}

local DelayTick = module.load("YL", "Core/DelayTick")
local BuffManager = module.load("YL", "Library/BuffManager")
local ItemManager = module.load("YL", "Library/ItemManager")
local ObjectManager = module.load("YL", "Library/ObjectManager")
local OrbManager = module.load("YL", "Library/OrbManager")
local VectorManager = module.load("YL", "Library/VectorManager")
local CalculateManager = module.load("YL", "Library/CalculateManager")
local MyCommon = module.load("YL", "Library/ExtraManager")

local MyMenu

function autoItem.Add(GlobalMenu)
    MyMenu = GlobalMenu

    MyMenu:menu("Item", "Auto Items")

    MyMenu.Item:menu("PRO01", "Hextech Protobelt-01")
    MyMenu.Item.PRO01:boolean("Combo", "Use in Combo", true)
    MyMenu.Item.PRO01:slider("ComboHP", "Target HealthPercent <= x%", 80, 1, 100, 1)
end

local function HavePotionBuff()
    if BuffManager.HasBuff(player, "recall") then
        return true
    end
    if BuffManager.HasBuff(player, "teleport") then
        return true
    end
    if BuffManager.HasBuff(player, "regenerationpotion") then
        return true
    end
    if BuffManager.HasBuff(player, "itemminiregenpotion") then
        return true
    end
    if BuffManager.HasBuff(player, "itemcrystalflask") then
        return true
    end
    if BuffManager.HasBuff(player, "itemcrystalflaskjungle") then
        return true
    end
    if BuffManager.HasBuff(player, "itemdarkcrystalflask") then
        return true
    end
    if BuffManager.HasBuff(player, "lootedregenerationpotion") then
        return true
    end
    if BuffManager.HasBuff(player, "item2010") then
        return true
    end
end

local function OnMyTick()
    if player.isDead or player.isRecalling or ObjectManager.IsInFountainRange(player) then
        return
    end
    if DelayTick.tickNumber == 1 then
        --if MyMenu.Item.PRO01.Combo:get() and MyMenu.Key.Combo:get() and ItemManager.HasItem("itemsofboltspellbase") and ItemManager.CanUseItem("itemsofboltspellbase") then
            --local target = MyCommon.GetTarget(800)
            --if target and target ~= nil and MyCommon.IsValidTarget(target, 800) and MyCommon.GetHealthPercent(target) <= MyMenu.Item.PRO01.ComboHP:get() then
                --ItemManager.CastOnPosition(target.pos, "itemsofboltspellbase")
                --return
            --end
        --end
    end
end
cb.add(cb.tick, OnMyTick)

return autoItem

return {
	Varus_Q_Buff = "varusq",
	ExhaustDebuff = "summonerexhaust",
	Yone_Q3_Buff = "yoneq3ready",
	DrMundoImmunityBuff = "drmundopimmunity",
	HextechDragonSoulCooldownBuff = "srx_dragonsoulbuffhextech_cd",
	<PERSON>_<PERSON>_Buff = "olafragnarok",
	<PERSON><PERSON>_W_Ally_Buff = "nilahwallybuff",
	BlueBuff = "crestoftheancientgolem",
	ChemtechDragonSoulBuff = "srx_dragonsoulbuffchemtech",
	Rune_DarkHarvestCooldownBuff = "assets/perks/styles/domination/darkharvest/darkharvestcooldown.lua",
	Rune_GraspOfTheUndyingOmniBuff = "assets/perks/styles/inspiration/masterkey/masterkeygraspoftheundyingonh.lua",
	BrandPassiveDetonateBuff = "brandablazedetonatemarker",
	Viego_W_Buff = "viegow",
	ZhonyasHourglassBuff = "zhonyasringshield",
	Zed_R_Debuff = "zedrtargetmark",
	Rune_DarkHarvestBuff = "assets/perks/styles/domination/darkharvest/darkharvest.lua",
	GuardianAngelBuff = "willrevive",
	Nautilus_Q_Buff = "nautilusanchordragglobalroot",
	Rune_FirstStrikeProccedBuff = "assets/perks/styles/inspiration/firststrike/firststrike.lua",
	Leona_W_Buff = "leonasolarbarrier",
	StaticItemsChargeBuff = "itemstatikshankcharge",
	IllaoiSpiritBuff = "illaoiespirit",
	SionPassiveBuff = "sionpassivezombie",
	Nocturne_W_Buff = "nocturneshroudofdarkness",
	CrownBuff = "4644shield",
	Swain_E_Buff = "swainpassivepullmovebuff",
	Poppy_R_Buff = "poppyr",
	ElderDragonBuff = "elderdragonbuff",
	Zilean_Q_Debuff = "zileanqenemybomb",
	Galio_W_Buff = "galiow",
	Tryndamere_R_Buff = "undyingrage",
	Janna_R_Buff = "reapthewhirlwind",
	Blitzcrank_Q_Buff = "rocketgrab2",
	Nilah_W_Buff = "nilahwbuff",
	Leona_W_Buff2 = "leonasolarbarrier2",
	Anivia_R_Buff = "glacialstorm",
	MalzaharPassiveBuff = "malzaharpassiveshield",
	Sona_W_Debuff = "sonawpassivedebuff",
	Rune_ArcaneCometBuff = "assets/perks/styles/sorcery/arcanecomet/arcanecometsnipe.lua",
	ChallengingSmiteDebuff = "itemsmitechallenge",
	KrakenSlayerBuff = "6672buff",
	SupportItemOrbThreeBuff = "talentreaperstacksthree",
	Rune_NullifyingOrbBuff = "assets/perks/styles/sorcery/nullifyingorb/perknullifyingorbactive.lua",
	Kindred_R_Buff = "kindredrnodeathbuff",
	Nami_E_Buff = "namie",
	InfernalDragonSoulCooldownBuff = "srx_dragonsoulbuffinfernal_cooldown",
	DivineSundererBuff = "6632buff",
	BaronBuff = "exaltedwithbaronnashorminion",
	Rune_FirstStrikeBuff = "assets/perks/styles/inspiration/firststrike/firststrikeavailable.lua",
	Taric_R_Buff = "taricr",
	Jax_E_Buff = "jaxcounterstrike",
	Shen_R_Channel_Buff = "shenrchannelbuffbar",
	EssenceReaverBuff = "3508buff",
	Velkoz_R_Buff = "velkozr",
	IgniteDebuff = "summonerdot",
	Rune_GlacialAugmentOmniBuff = "assets/perks/styles/inspiration/masterkey/masterkeyglacialaugment.lua",
	Rune_GlacialAugmentBuff = "assets/perks/styles/inspiration/glacialaugment/glacialaugmentcooldown.lua",
	Rune_HailOfBladesBuff = "assets/perks/styles/domination/hailofblades/hailofbladesready.lua",
	ArdentCenserBuff = "3504buff",
	Rune_SummonAeryBuff = "assets/perks/styles/sorcery/summonaery/summonaery.lua",
	Rune_GraspOfTheUndyingBuff = "assets/perks/styles/resolve/graspoftheundying/graspoftheundyingonh.lua",
	XinZhao_R_Buff = "xinzhaorrangedimmunity",
	Warwick_R_Buff = "warwickrsound",
	GrievousWoundsDebuff = "grievouswound",
	BladeOfTheRuinedKingDebuff = "item3153botrkstacks",
	Karthus_R_Buff = "karthusfallenonecastsound",
	HextechDragonSoulBuff = "srx_dragonsoulbuffhextech",
	Irelia_W_Buff = "ireliawdefense",
	Ryze_R_Buff = "ryzerchannel",
	Fizz_R_Debuff = "fizzrbomb",
	Lucian_R_Buff = "lucianr",
	Rune_ManaflowBandBuff = "assets/perks/styles/sorcery/manaflowband/perkmanaflowbandbuff.lua",
	Samira_R_Buff = "samirar",
	Pantheon_E_Buff = "pantheonefacinglock",
	Rune_PressTheAttackBuff = "assets/perks/styles/precision/presstheattack/presstheattackstack.lua",
	Fiora_W_Buff = "fioraw",
	Garen_W_Buff = "garenw",
	TwistedFate_R_Buff = "gate",
	HextechFlashBuff = "summonerflashperkshextechflashtraptionv2",
	Pyke_Q_Stun_Buff = "pykeqrange",
	LichBaneBuff = "lichbane",
	Zed_R_Debuff2 = "zedrdeathmark",
	Pantheon_Q_Buff = "pantheonq",
	Warwick_E_Buff = "warwicke",
	Rune_ArcaneCometOmniBuff = "assets/perks/styles/inspiration/masterkey/masterkeyarcanecomet.lua",
	SupportItemOrbOneBuff = "talentreaperstacksone",
	Sion_Q_Buff = "sionq",
	Thresh_E_Buff = "threshestun",
	Urgot_R_Debuff = "urgotr",
	InfernalDragonSoulBuff = "srx_dragonsoulbuffinfernal",
	Viego_Transformed_Buff = "viegopassivetransform",
	Rune_BoneplatingBuff = "assets/perks/styles/resolve/boneplating/boneplating.lua",
	SupportItemOrbTwoBuff = "talentreaperstackstwo",
	Sona_Q_Buff = "sonaqonhit",
	Malzahar_R_Buff = "malzaharrsound",
	Akshan_R_Buff = "akshanr",
	Xerath_Q_Buff = "xeratharcanopulsechargeup",
	Rune_PressTheAttackOmniBuff = "assets/perks/styles/inspiration/masterkey/masterkeypresstheattackstack.lua",
	Belveth_E_Buff = "belvethe",
	MasterYi_W_Buff = "meditate",
	Rune_DarkHarvestOmniBuff = "assets/perks/styles/inspiration/masterkey/masterkeydarkharvest.lua",
	Alistar_R_Buff = "ferocioushowl",
	Pantheon_R_Buff = "pantheonr",
	TeleportBuff = "summonerteleport",
	PressTheAttackExposedBuff = "assets/perks/styles/precision/presstheattack/presstheattackdamageamp.lua",
	Xerath_R_Buff = "xerathrshots",
	Pyke_Q_Buff = "pykeq",
	Vi_Q_Buff = "viq",
	Rune_TasteOfBloodBuff = "assets/perks/styles/domination/tasteofblood/tasteofbloodtemp.lua",
	RelicShieldOrbBuff = "talentreaperdisplay",
	Zac_E_Buff = "zace",
	Thresh_Q_Buff = "threshq",
	ChemtechDragonZombieBuff = "",
	Sivir_E_Buff = "sivire",
	SupportItemGoldQuestBuff = "s10_support_quest_display",
	Zilean_R_Buff = "chronoshift",
	SteelShoulderguardsOrbBuff = "talentreaperdisplayshoulders",
	Braum_E_Buff = "braumeshieldbuff",
	Shen_W_Buff = "shenwbuff",
	Rammus_Q_Buff = "powerball",
	Yuumi_R_Buff = "yuumir",
	Rune_HailOfBladesOmniBuff = "assets/perks/styles/inspiration/masterkey/masterkeyhailofbladesready.lua",
	SheenBuff = "sheen",
	Kayle_R_Buff = "kayler",
	TrinityForceBuff = "3078trinityforce",
	ChemtechDragonGasZoneBuff = "srx_dragonchemtech_zone_buff",
	Rune_LethalTempoBuff = "assets/perks/styles/precision/lethaltempo/lethaltempo.lua",
	Katarina_R_Buff = "katarinarsound",
	Vladimir_E_Buff = "vladimire",
	RecallBuff = "recall",
	Morgana_E_Buff = "morganae",
	Rune_SummonAeryOmniBuff = "assets/perks/styles/inspiration/masterkey/masterkeysummonaery.lua",
	Gragas_W_Buff = "gragaswself",
	BUFF_ENUM_AURA = BUFF_AURA,
	BUFF_ENUM_COMBATENCHANCER = BUFF_COMBATENCHANCER,
	BUFF_ENUM_COMBATDEHANCER = BUFF_COMBATDEHANCER,
	BUFF_ENUM_SPELLSHIELD = BUFF_SPELLSHIELD,
	BUFF_ENUM_STUN = BUFF_STUN,
	BUFF_ENUM_INVISIBILITY = BUFF_INVISIBILITY,
	BUFF_ENUM_SILENCE = BUFF_SILENCE,
	BUFF_ENUM_TAUNT = BUFF_TAUNT,
	BUFF_ENUM_BERSERK = BUFF_BERSERK,
	BUFF_ENUM_POLYMORPH = BUFF_POLYMORPH,
	BUFF_ENUM_SLOW = BUFF_SLOW,
	BUFF_ENUM_SNARE = BUFF_SNARE,
	BUFF_ENUM_DAMAGE = BUFF_DAMAGE,
	BUFF_ENUM_HEAL = BUFF_HEAL,
	BUFF_ENUM_HASTE = BUFF_HASTE,
	BUFF_ENUM_SPELLIMMUNITY = BUFF_SPELLIMMUNITY,
	BUFF_ENUM_PHYSICALIMMUNITY = BUFF_PHYSICALIMMUNITY,
	BUFF_ENUM_INVULNERABILITY = BUFF_INVULNERABILITY,
	BUFF_ENUM_ATTACKSPEEDSLOW = BUFF_ATTACKSPEEDSLOW,
	BUFF_ENUM_NEARSIGHT = BUFF_NEARSIGHT,
	BUFF_ENUM_FEAR = BUFF_FEAR,
	BUFF_ENUM_SHRED = BUFF_SHRED,
	BUFF_ENUM_CHARM = BUFF_CHARM,
	BUFF_ENUM_POISON = BUFF_POISON,
	BUFF_ENUM_SUPPRESSION = BUFF_SUPPRESSION,
	BUFF_ENUM_BLIND = BUFF_BLIND,
	BUFF_ENUM_COUNTER = BUFF_COUNTER,
	BUFF_ENUM_CURRENCY = BUFF_CURRENCY,
	BUFF_ENUM_FLEE = BUFF_FLEE,
	BUFF_ENUM_KNOCKUP = BUFF_KNOCKUP,
	BUFF_ENUM_KNOCKBACK = BUFF_KNOCKBACK,
	BUFF_ENUM_DISARM = BUFF_DISARM,
	BUFF_ENUM_GROUNDED = BUFF_GROUNDED,
	BUFF_ENUM_DROWSY = BUFF_DROWSY,
	BUFF_ENUM_ASLEEP = BUFF_ASLEEP,
	BUFF_ENUM_OBSCURED = BUFF_OBSCURED,
	BUFF_ENUM_CLICKPROOFTOENEMIES = BUFF_CLICKPROOFTOENEMIES,
	BUFF_ENUM_UNKILLABLE = BUFF_UNKILLABLE
}

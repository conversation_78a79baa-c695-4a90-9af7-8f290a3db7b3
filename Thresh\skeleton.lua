

local ove_0_10 = module.internal("TS")
local ove_0_11 = module.internal("orb")
local ove_0_12 = module.internal("pred")
local ove_0_13 = module.seek("evade") or module.seek("evade_old") or module.internal("evade")
local ove_0_14 = "/menu"
local ove_0_15 = hanbot.path .. "/saves/hanbot_core.ini"
local ove_0_16 = io.open(ove_0_15, "r")

local caidan = hanbot and hanbot.language == 1
ove_0_14 = caidan  and   "/menucn" or   "/menu"

local ove_0_17 = module.load(header.id,  player.charName .. ove_0_14)
local ove_0_18 = module.load(header.id,  player.charName .. "/misc")
local ove_0_19 = module.load(header.id,  player.charName .. "/pred")
local ove_0_20
local ove_0_21

ove_0_21 = graphics.width <= 1920 and graphics.height <= 1080 or false

local ove_0_22 = {}
local ove_0_23 = {
	300,
	350,
	400,
	450,
	500
}
local ove_0_24
local ove_0_25 = 0
local ove_0_26 = 0
local ove_0_27 = {}
local ove_0_28 = {
	Garen = true,
	Tristana = true,
	Graves = true,
	Riven = true,
	Brand = true,
	Darius = true,
	Chogath = true,
	Annie = true,
	Jinx = true,
	["Miss Fortune"] = true,
	Varus = true,
	Urgot = true,
	Gnar = true,
	Ekko = true,
	Malphite = true,
	Veigar = true,
	Blitzcrank = true,
	["Vel'Koz"] = true,
	Vi = true,
	Warwick = true,
	Syndra = true,
	JarvanIV = true,
	Ashe = true,
	Lissandra = true,
	Sona = true,
	Hecarim = true,
	Draven = true,
	Orianna = true,
	Amumu = true,
	AurelionSol = true
}
local ove_0_29 = {
	goldcardpreattack = true,
	xinzhaoqthrust3 = true,
	udyrbearattack = true,
	renektonsuperexecute = true,
	leonashieldofdaybreak = true,
	ryzew = true,
	hecarimramp = true,
	renektonexecute = true,
	shene = true,
	garenq = true,
	pantheonw = true,
	urgote = true
}
local ove_0_30 = {}

for iter_0_1 = 0, objManager.enemies_n - 1 do
	local ove_0_31 = objManager.enemies[iter_0_1]
	local ove_0_32 = ove_0_31:spellSlot(3).name

	if ove_0_28[ove_0_31.charName] then
		ove_0_30[ove_0_32:lower()] = true
	end
end

local ove_0_33 = {
	speed = 1900,
	delay = 0.5,
	boundingRadiusMod = 1,
	width = 70,
	collision = {
		minion = true,
		hero = false
	}
}
local ove_0_34 = {
	speed = 1900,
	delay = 0.5,
	boundingRadiusMod = 1,
	width = 80,
	collision = {
		minion = true,
		hero = false
	}
}
local ove_0_35 = {
	delay = 0.25,
	radius = 100,
	speed = 1075,
	boundingRadiusMod = 0
}
local ove_0_36 = {
	speed = 1100,
	delay = 0.21,
	boundingRadiusMod = 0,
	width = 65,
	collision = {
		minion = false,
		hero = false
	}
}

local function ove_0_37(arg_5_0, arg_5_1, arg_5_2)
	-- function 5
	if ove_0_12.trace.linear.hardlock(arg_5_0, arg_5_1, arg_5_2) then
		return true
	end

	if ove_0_12.trace.linear.hardlockmove(arg_5_0, arg_5_1, arg_5_2) then
		return true
	end
end

local function ove_0_38(arg_6_0, arg_6_1, arg_6_2)
	-- function 6
	return (arg_6_2.y - arg_6_0.y) * (arg_6_1.x - arg_6_0.x) - (arg_6_1.y - arg_6_0.y) * (arg_6_2.x - arg_6_0.x)
end

local function ove_0_39(arg_7_0, arg_7_1, arg_7_2, arg_7_3)
	-- function 7
	return (ove_0_38(arg_7_0, arg_7_2, arg_7_3) <= 0 and ove_0_38(arg_7_1, arg_7_2, arg_7_3) > 0 or ove_0_38(arg_7_0, arg_7_2, arg_7_3) > 0 and ove_0_38(arg_7_1, arg_7_2, arg_7_3) <= 0) and (ove_0_38(arg_7_0, arg_7_1, arg_7_2) <= 0 and ove_0_38(arg_7_0, arg_7_1, arg_7_3) > 0 or ove_0_38(arg_7_0, arg_7_1, arg_7_2) > 0 and ove_0_38(arg_7_0, arg_7_1, arg_7_3) <= 0)
end

local function ove_0_40(arg_8_0, arg_8_1)
	-- function 8
	local slot_8_0 = vec2(arg_8_0.x, arg_8_0.z)
	local slot_8_1 = arg_8_1:to2D()

	for iter_8_0 in pairs(ove_0_22) do
		local slot_8_2 = ove_0_22[iter_8_0]

		if slot_8_2 then
			local slot_8_3 = ove_0_23[slot_8_2.spell.owner:spellSlot(1).level] / 2
			local slot_8_4 = slot_8_2 + (slot_8_2.startPos - slot_8_2):norm():perp2() * slot_8_3
			local slot_8_5 = slot_8_2 + (slot_8_2.startPos - slot_8_2):norm():perp2() * -slot_8_3
			local slot_8_6 = (slot_8_5:to2D() - slot_8_4:to2D()):norm():perp1()
			local slot_8_7 = slot_8_4:to2D() - slot_8_6 * 75
			local slot_8_8 = slot_8_4:to2D() + slot_8_6 * 75
			local slot_8_9 = slot_8_5:to2D() + slot_8_6 * 75
			local slot_8_10 = slot_8_5:to2D() - slot_8_6 * 75

			if ove_0_39(slot_8_7, slot_8_8, slot_8_0, slot_8_1) or ove_0_39(slot_8_7, slot_8_10, slot_8_0, slot_8_1) or ove_0_39(slot_8_9, slot_8_8, slot_8_0, slot_8_1) or ove_0_39(slot_8_9, slot_8_10, slot_8_0, slot_8_1) then
				return true
			end
		end
	end

	for iter_8_1 = 0, objManager.enemies_n - 1 do
		local slot_8_11 = objManager.enemies[iter_8_1]

		if slot_8_11 and slot_8_11.isVisible and slot_8_11.buff and slot_8_11.buff.samiraw and mathf.col_vec_rect(slot_8_11.path.serverPos2D, slot_8_0, slot_8_1, 400, 120) then
			return true
		end
	end

	return false
end

local function ove_0_41(arg_9_0, arg_9_1, arg_9_2)
	-- function 9
	if arg_9_2 > 2500 then
		return
	end

	if arg_9_1 and arg_9_1.isVisible and arg_9_2 and arg_9_1.x and arg_9_1.y and arg_9_1.z and arg_9_1.path then
		local slot_9_0 = ove_0_12.linear.get_prediction(ove_0_33, arg_9_1)

		if player:spellSlot(2).state == 0 and arg_9_1.isTargetable and arg_9_2 and arg_9_2 <= 480 and (not ove_0_27[arg_9_1.networkID] or ove_0_27[arg_9_1.networkID] and game.time > ove_0_27[arg_9_1.networkID]) and (not arg_9_1.buff.threshq or arg_9_1.buff.threshq and game.time - arg_9_1.buff.threshq.startTime >= arg_9_1.buff.threshq.endTime - arg_9_1.buff.threshq.startTime - 0.25 - network.latency) then
			arg_9_0.obj = arg_9_1

			return true
		elseif player:spellSlot(2).state == 0 and arg_9_1.isTargetable and arg_9_2 and arg_9_2 <= 480 then
			arg_9_0.obj = arg_9_1

			return true
		elseif player:spellSlot(3).state == 0 and not arg_9_1.buff[BUFF_INVULNERABILITY] and arg_9_1.isTargetable and arg_9_2 and arg_9_2 <= 400 then
			arg_9_0.obj = arg_9_1

			return true
		elseif arg_9_1.isTargetable and player:spellSlot(0).state == 0 and ove_0_18.HaveQ() and slot_9_0 then
			if slot_9_0 and slot_9_0.endPos and not ove_0_40(player.path.serverPos, vec3(slot_9_0.endPos.x, arg_9_1.y, slot_9_0.endPos.y)) and ove_0_37(ove_0_33, slot_9_0, arg_9_1) and slot_9_0.startPos:distSqr(slot_9_0.endPos) <= 1210000 and arg_9_2 and arg_9_2 <= 1100 then
				arg_9_0.obj = arg_9_1

				return true
			elseif slot_9_0 and slot_9_0.endPos and not ove_0_40(player.path.serverPos, vec3(slot_9_0.endPos.x, arg_9_1.y, slot_9_0.endPos.y)) and slot_9_0.startPos:distSqr(slot_9_0.endPos) <= 1210000 and arg_9_2 and arg_9_2 <= 1100 then
				arg_9_0.obj = arg_9_1

				return true
			end
		elseif player:spellSlot(0).state == 0 and not ove_0_18.HaveQ() and arg_9_1.buff.threshq and not arg_9_1.buff[BUFF_INVULNERABILITY] and arg_9_1.isTargetable then
			arg_9_0.obj = arg_9_1

			return true
		elseif ove_0_11.combat.target then
			arg_9_0.obj = ove_0_11.combat.target

			return true
		end
	end
end

local function ove_0_42(arg_10_0, arg_10_1, arg_10_2)
	-- function 10
	if arg_10_1 and arg_10_2 and arg_10_2 <= 480 then
		arg_10_0.obj = arg_10_1

		return true
	end
end

local function ove_0_43()
	-- function 11
	return ove_0_11.ts.get_result(ove_0_41).obj
end

local function ove_0_44(arg_12_0, arg_12_1, arg_12_2, arg_12_3)
	-- function 12
	local slot_12_0 = arg_12_1.startPos:dist(arg_12_1.endPos)

	if ove_0_12.trace.linear.hardlock(arg_12_0, arg_12_1, arg_12_2) then
		return true
	end

	if ove_0_12.trace.linear.hardlockmove(arg_12_0, arg_12_1, arg_12_2) then
		return true
	end

	if arg_12_2 and ove_0_18.isValid(arg_12_2) and player.pos:dist(arg_12_2.pos) < 480 then
		return true
	end

	if slot_12_0 <= arg_12_3 and ove_0_12.trace.newpath(arg_12_2, 0.033, 0.65) then
		return true
	end

	if ove_0_12.trace.newpath(arg_12_2, 0.033, slot_12_0 <= 950 and 1.2 or 1.3) then
		return true
	end
end

local function ove_0_45(arg_13_0, arg_13_1, arg_13_2)
	-- function 13
	return (arg_13_1.x - arg_13_0.x) * (arg_13_2.y - arg_13_0.y) - (arg_13_1.y - arg_13_0.y) * (arg_13_2.x - arg_13_0.x) > 0
end

local function ove_0_46(arg_14_0)
	-- function 14
	if player:spellSlot(0).state ~= 0 or game.time < ove_0_25 or game.time < ove_0_26 or not arg_14_0 or arg_14_0.isDead or not arg_14_0.isVisible or not arg_14_0.isTargetable or not ove_0_11.core.can_action() then
		return
	end

	local slot_14_0 = ove_0_12.linear.get_prediction(ove_0_33, arg_14_0)

	if ove_0_17.misc.qpred:get() == 2 then
		local slot_14_1, slot_14_2, slot_14_3 = ove_0_19.pred:predict(arg_14_0, player, "LineSS", ove_0_33.speed, 1050, 70, 0.25, 0, {
			Champions = false,
			Minions = true
		})

		if slot_14_2 and slot_14_3 and slot_14_3 >= 75 and slot_14_0 and slot_14_0.startPos:distSqr(slot_14_0.endPos) <= 1102500 and not ove_0_40(player, vec3(slot_14_2.x, arg_14_0.y, slot_14_2.y)) and ove_0_44(ove_0_33, slot_14_0, arg_14_0, arg_14_0.attackRange) then
			local slot_14_4 = {
				startPos = slot_14_0.startPos,
				endPos = slot_14_2
			}

			if not ove_0_12.collision.get_prediction(ove_0_34, slot_14_4, arg_14_0) then
				player:castSpell("pos", 0, vec3(slot_14_2.x, arg_14_0.pos.y, slot_14_2.y))
				ove_0_11.core.set_server_pause_attack()

				ove_0_26 = game.time + slot_14_0.startPos:dist(slot_14_0.endPos) / 1900 + 0.5 + network.latency
				ove_0_27[arg_14_0.networkID] = game.time + slot_14_0.startPos:dist(slot_14_0.endPos) / 1900 + 0.5 + network.latency
			end
		end
	elseif slot_14_0 and slot_14_0.endPos and slot_14_0.startPos:distSqr(slot_14_0.endPos) <= 1102500 and not ove_0_40(player, vec3(slot_14_0.endPos.x, arg_14_0.y, slot_14_0.endPos.y)) and ove_0_44(ove_0_33, slot_14_0, arg_14_0, arg_14_0.attackRange) and not ove_0_12.collision.get_prediction(ove_0_34, slot_14_0, arg_14_0) then
		player:castSpell("pos", 0, vec3(slot_14_0.endPos.x, arg_14_0.pos.y, slot_14_0.endPos.y))
		ove_0_11.core.set_server_pause_attack()

		ove_0_26 = game.time + slot_14_0.startPos:dist(slot_14_0.endPos) / 1900 + 0.5 + network.latency
		ove_0_27[arg_14_0.networkID] = game.time + slot_14_0.startPos:dist(slot_14_0.endPos) / 1900 + 0.5 + network.latency
	end
end

local function ove_0_47(arg_15_0)
	-- function 15
	if player:spellSlot(2).state ~= 0 or arg_15_0.health > ove_0_18.EDmg(arg_15_0) and ove_0_27[arg_15_0.networkID] and game.time < ove_0_27[arg_15_0.networkID] or not arg_15_0 or arg_15_0.isDead or not arg_15_0.isVisible or not arg_15_0.isTargetable then
		return
	end

	if arg_15_0.health > ove_0_18.EDmg(arg_15_0) and arg_15_0.buff.threshq and arg_15_0.buff.threshq and game.time - arg_15_0.buff.threshq.startTime < arg_15_0.buff.threshq.endTime - arg_15_0.buff.threshq.startTime - 0.25 - network.latency then
		return
	end

	local slot_15_0 = player.pos2D + (arg_15_0.path.serverPos2D - player.pos2D):perp2()

	if ove_0_45(player.pos2D, slot_15_0, mousePos:to2D()) then
		local slot_15_1 = ove_0_12.linear.get_prediction(ove_0_36, arg_15_0)

		if slot_15_1 and slot_15_1.startPos:distSqr(slot_15_1.endPos) <= 230400 then
			ove_0_25 = game.time + 0.1 + network.latency

			ove_0_11.core.set_server_pause_attack()
			player:castSpell("pos", 2, vec3(slot_15_1.endPos.x, arg_15_0.pos.y, slot_15_1.endPos.y))
		end
	else
		local slot_15_2 = player.pos + (player.pos - arg_15_0.path.serverPos):norm() * 480

		if slot_15_2 then
			ove_0_25 = game.time + 0.1 + network.latency

			ove_0_11.core.set_server_pause_attack()
			player:castSpell("pos", 2, slot_15_2)
		end
	end
end

local function ove_0_48(arg_16_0)
	-- function 16
	if player:spellSlot(2).state ~= 0 or arg_16_0.health > ove_0_18.EDmg(arg_16_0) and ove_0_27[arg_16_0.networkID] and game.time < ove_0_27[arg_16_0.networkID] or not arg_16_0 or arg_16_0.isDead or not arg_16_0.isVisible or not arg_16_0.isTargetable then
		return
	end

	if arg_16_0.health > ove_0_18.EDmg(arg_16_0) and arg_16_0.buff.threshq and arg_16_0.buff.threshq and game.time - arg_16_0.buff.threshq.startTime < arg_16_0.buff.threshq.endTime - arg_16_0.buff.threshq.startTime - 0.25 - network.latency then
		return
	end

	local slot_16_0 = player.pos + (player.pos - arg_16_0.path.serverPos):norm() * 480

	if slot_16_0 then
		ove_0_25 = game.time + 0.1 + network.latency

		ove_0_11.core.set_server_pause_attack()
		player:castSpell("pos", 2, slot_16_0)
	end
end

local function ove_0_49(arg_17_0)
	-- function 17
	if player:spellSlot(2).state ~= 0 or arg_17_0.health > ove_0_18.EDmg(arg_17_0) and ove_0_27[arg_17_0.networkID] and game.time < ove_0_27[arg_17_0.networkID] or not arg_17_0 or arg_17_0.isDead or not arg_17_0.isVisible or not arg_17_0.isTargetable then
		return
	end

	if arg_17_0.health > ove_0_18.EDmg(arg_17_0) and arg_17_0.buff.threshq and arg_17_0.buff.threshq and game.time - arg_17_0.buff.threshq.startTime < arg_17_0.buff.threshq.endTime - arg_17_0.buff.threshq.startTime - 0.25 - network.latency then
		return
	end

	local slot_17_0 = ove_0_12.linear.get_prediction(ove_0_36, arg_17_0)

	if slot_17_0 and slot_17_0.startPos:distSqr(slot_17_0.endPos) <= 230400 then
		ove_0_25 = game.time + 0.1 + network.latency

		ove_0_11.core.set_server_pause_attack()
		player:castSpell("pos", 2, vec3(slot_17_0.endPos.x, arg_17_0.pos.y, slot_17_0.endPos.y))
	end
end

local function ove_0_50(arg_18_0)
	-- function 18
	if player:spellSlot(1).state ~= 0 or not arg_18_0 or arg_18_0.isDead or not arg_18_0.isVisible or not arg_18_0.isTargetableToTeamFlags and arg_18_0 ~= player then
		return
	end

	if arg_18_0 ~= player then
		local slot_18_0 = ove_0_12.circular.get_prediction(ove_0_35, arg_18_0)

		if slot_18_0 and slot_18_0.endPos then
			local slot_18_1 = slot_18_0.startPos:dist(slot_18_0.endPos)

			if slot_18_1 and slot_18_1 <= 950 then
				player:castSpell("pos", 1, vec3(slot_18_0.endPos.x, arg_18_0.y, slot_18_0.endPos.y))
			elseif slot_18_1 and slot_18_1 <= 1300 then
				local slot_18_2 = player.path.serverPos2D + 900 / player.path.serverPos2D:dist(slot_18_0.endPos) * (slot_18_0.endPos - player.path.serverPos2D)
				local slot_18_3 = vec3(slot_18_2.x, 0, slot_18_2.y)

				for iter_18_0 = 950, slot_18_1, 50 do
					local slot_18_4 = slot_18_0.startPos + (slot_18_0.endPos - slot_18_0.startPos):norm() * iter_18_0

					if navmesh.isWall(vec3(slot_18_4.x, 0, slot_18_4.y)) then
						slot_18_3 = nil
					end
				end

				if slot_18_3 then
					player:castSpell("pos", 1, slot_18_3)
				end
			end
		end
	elseif arg_18_0 == player and ove_0_18.CountEnemiesNear(player.pos, 800) > 0 then
		player:castSpell("pos", 1, player.path.serverPos)
	end
end

local function ove_0_51(arg_19_0)
	-- function 19
	if player:spellSlot(1).state ~= 0 or not arg_19_0 or arg_19_0.team ~= TEAM_ALLY or arg_19_0.isDead or not arg_19_0.isVisible or not arg_19_0.isTargetableToTeamFlags and arg_19_0 ~= player then
		return
	end

	if arg_19_0 ~= player then
		local slot_19_0 = ove_0_12.circular.get_prediction(ove_0_35, arg_19_0)

		if slot_19_0 and slot_19_0.endPos then
			local slot_19_1 = slot_19_0.startPos:dist(slot_19_0.endPos)

			if slot_19_1 and slot_19_1 <= 950 then
				player:castSpell("pos", 1, vec3(slot_19_0.endPos.x, arg_19_0.y, slot_19_0.endPos.y))
			elseif slot_19_1 and slot_19_1 <= 1300 then
				local slot_19_2 = player.path.serverPos2D + 950 / player.path.serverPos2D:dist(slot_19_0.endPos) * (slot_19_0.endPos - player.path.serverPos2D)
				local slot_19_3 = vec3(slot_19_2.x, 0, slot_19_2.y)

				for iter_19_0 = 950, slot_19_1, 50 do
					local slot_19_4 = slot_19_0.startPos + (slot_19_0.endPos - slot_19_0.startPos):norm() * iter_19_0

					if navmesh.isWall(vec3(slot_19_4.x, 0, slot_19_4.y)) then
						slot_19_3 = nil
					end
				end

				if slot_19_3 then
					player:castSpell("pos", 1, slot_19_3)
				end
			end
		end
	else
		player:castSpell("pos", 1, player.path.serverPos)
	end
end

local function ove_0_52(arg_20_0)
	-- function 20
	if not arg_20_0 and ove_0_11.core.is_move_paused() then
		ove_0_11.core.set_pause_move(0)
	elseif arg_20_0 then
		local slot_20_0 = player.path.serverPos:lerp(arg_20_0, 200 / player.path.serverPos:dist(arg_20_0))

		ove_0_11.core.set_pause_move(1)
		player:move(vec3(slot_20_0))
	end
end

local function ove_0_53(arg_21_0)
	-- function 21
	if not ove_0_17.misc.gp:get() then
		return
	end

	if arg_21_0 and arg_21_0.type == TYPE_HERO and arg_21_0.team ~= TEAM_ALLY and arg_21_0.path.isDashing and arg_21_0.path.point[1]:dist(player.pos2D) > arg_21_0.pos:dist(player.pos2D) and arg_21_0.path.dashSpeed > 700 and player:spellSlot(2).state == 0 and player.pos:dist(arg_21_0.path.point) < 480 and not arg_21_0.buff.threshq then
		ove_0_49(arg_21_0)
	end
end

local function ove_0_54()
	-- function 22
	if not ove_0_20 or not ove_0_18.isValid(ove_0_20) then
		return
	end

	if ove_0_17.combo.r:get() and player:spellSlot(3).state == 0 and ove_0_18.CountEnemiesNear(player.pos, 500) >= ove_0_17.combo.rhit:get() and ove_0_11.core.can_action() then
		player:castSpell("self", 3)
	end

	if ove_0_17.combo.e:get() ~= 5 and player:spellSlot(2).state == 0 and player.pos:dist(ove_0_20.path.serverPos) <= 480 and ove_0_11.core.can_action() then
		if ove_0_17.combo.e:get() == 1 and (not player.path.isDashing or not ove_0_17.combo.e2:get()) then
			ove_0_49(ove_0_20)
		elseif ove_0_17.combo.e:get() == 2 and (not player.path.isDashing or not ove_0_17.combo.e2:get()) then
			ove_0_48(ove_0_20)
		elseif ove_0_17.combo.e:get() == 3 and (not player.path.isDashing or not ove_0_17.combo.e2:get()) then
			local slot_22_0 = player.pos + (player.pos - ove_0_20.path.serverPos):norm() * 480
			local slot_22_1, slot_22_2 = ove_0_18.UnderTurret2(player.pos)

			if slot_22_0 and slot_22_1 and slot_22_2 and not slot_22_2.isDead then
				if player.pos:dist(slot_22_2.pos) > ove_0_20.pos:dist(slot_22_2.pos) then
					ove_0_49(ove_0_20)
				else
					ove_0_48(ove_0_20)
				end
			elseif slot_22_0 and player.health / player.maxHealth >= 0.4 and (ove_0_24 and not ove_0_24.isDead and ove_0_18.AllyNear(ove_0_24, 400) >= 1 or ove_0_18.AllyNear(player.pos, 1000) >= 2 or ove_0_18.AllyNear(player.pos, 800) >= 1) then
				ove_0_48(ove_0_20)
			elseif slot_22_0 and (ove_0_20.health <= ove_0_18.AADmg(ove_0_20) * 2 + ove_0_18.EDmg(ove_0_20) + ove_0_18.QDmg(ove_0_20) and player.health / player.maxHealth >= 0.3 or ove_0_17.combo.q1:get() and player:spellSlot(0).state == 0 and ove_0_18.HaveQ() and ove_0_18.AllyNear(player.pos, 1000) > 0) then
				ove_0_48(ove_0_20)
			else
				ove_0_49(ove_0_20)
			end
		elseif ove_0_17.combo.e:get() == 4 and (not player.path.isDashing or not ove_0_17.combo.e2:get()) then
			ove_0_47(ove_0_20)
		end
	end

	if ove_0_17.combo.q1:get() and player:spellSlot(0).state == 0 and ove_0_18.HaveQ() and ove_0_11.core.can_action() then
		ove_0_46(ove_0_20)
	end

	if ove_0_17.combo.q2:get() ~= 3 and player:spellSlot(0).state == 0 and not ove_0_18.HaveQ() then
		local slot_22_3 = ove_0_18.AllyNear2(player.pos)

		if ove_0_17.combo.q2:get() == 1 and ove_0_20.buff.threshq and game.time - ove_0_20.buff.threshq.startTime >= ove_0_20.buff.threshq.endTime - ove_0_20.buff.threshq.startTime - 0.25 then
			player:castSpell("self", 0)

			if ove_0_17.combo.w:get() and slot_22_3 then
				ove_0_51(slot_22_3)
			end
		end

		if ove_0_17.combo.q2:get() == 2 and ove_0_20.pos:dist(player.pos) > 480 and not ove_0_20.buff.sionpassivezombie and (not ove_0_18.UnderTurret(ove_0_20.pos) or not ove_0_20.buff[BUFF_INVULNERABILITY] and not ove_0_20.buff.willrevive and not ove_0_20.buff.undyingrage and ove_0_20.health <= ove_0_18.AADmg(ove_0_20)) and (ove_0_17.combo.e:get() ~= 5 and player:spellSlot(2).state == 0 and (slot_22_3 or ove_0_20.health <= ove_0_18.AADmg(ove_0_20) * 2 + ove_0_18.EDmg(ove_0_20)) or ove_0_17.combo.w:get() and player:spellSlot(1).state == 0 and slot_22_3) and ove_0_20.buff.threshq and game.time - ove_0_20.buff.threshq.startTime >= ove_0_20.buff.threshq.endTime - ove_0_20.buff.threshq.startTime - 0.25 then
			player:castSpell("self", 0)

			if ove_0_17.combo.w:get() and slot_22_3 then
				ove_0_51(slot_22_3)
			end
		end
	end

	if ove_0_17.combo.w:get() and ove_0_11.core.can_action() and player:spellSlot(1).state == 0 and ove_0_18.AllyNear(player.path.serverPos, 750) == 0 and player.pos:dist(ove_0_20.pos) < ove_0_20.attackRange - 50 then
		local slot_22_4 = ove_0_18.AllyNear2(player.pos)

		if slot_22_4 then
			ove_0_51(slot_22_4)
		end
	end
end

local function ove_0_55()
	-- function 23
	if not ove_0_20 or not ove_0_18.isValid(ove_0_20) then
		return
	end

	if ove_0_17.harass.e:get() ~= 5 and player:spellSlot(2).state == 0 and player.pos:dist(ove_0_20.path.serverPos) <= 480 and ove_0_11.core.can_action() and player.mana * 100 / player.maxMana >= ove_0_17.harass.minimana:get() then
		if ove_0_17.harass.e:get() == 1 and (not player.path.isDashing or not ove_0_17.harass.e2:get()) then
			ove_0_49(ove_0_20)
		elseif ove_0_17.harass.e:get() == 2 and (not player.path.isDashing or not ove_0_17.harass.e2:get()) then
			local slot_23_0 = player.pos + (player.pos - ove_0_20.path.serverPos):norm() * 480

			ove_0_48(ove_0_20)
		elseif ove_0_17.harass.e:get() == 3 and (not player.path.isDashing or not ove_0_17.harass.e2:get()) then
			local slot_23_1 = player.pos + (player.pos - ove_0_20.path.serverPos):norm() * 480
			local slot_23_2, slot_23_3 = ove_0_18.UnderTurret2(player.pos)

			if slot_23_1 and slot_23_2 and slot_23_3 and not slot_23_3.isDead then
				if player.pos:dist(slot_23_3.pos) > ove_0_20.pos:dist(slot_23_3.pos) then
					ove_0_49(ove_0_20)
				else
					ove_0_48(ove_0_20)
				end
			elseif slot_23_1 and player.health / player.maxHealth >= 0.4 and (ove_0_24 and not ove_0_24.isDead and ove_0_18.AllyNear(ove_0_24, 400) >= 1 or ove_0_18.AllyNear(player.pos, 1000) >= 2 or ove_0_18.AllyNear(player.pos, 800) >= 1) then
				ove_0_48(ove_0_20)
			elseif slot_23_1 and (ove_0_20.health <= ove_0_18.AADmg(ove_0_20) * 2 + ove_0_18.EDmg(ove_0_20) + ove_0_18.QDmg(ove_0_20) and player.health / player.maxHealth >= 0.3 or ove_0_17.harass.q1:get() and player:spellSlot(0).state == 0 and ove_0_18.HaveQ() and ove_0_18.AllyNear(player.pos, 1000) > 0) then
				ove_0_48(ove_0_20)
			else
				ove_0_49(ove_0_20)
			end
		elseif ove_0_17.harass.e:get() == 4 and (not player.path.isDashing or not ove_0_17.harass.e2:get()) then
			ove_0_47(ove_0_20)
		end
	end

	if ove_0_17.harass.q1:get() and player:spellSlot(0).state == 0 and ove_0_18.HaveQ() and ove_0_11.core.can_action() and player.mana * 100 / player.maxMana >= ove_0_17.harass.minimana:get() then
		ove_0_46(ove_0_20)
	end

	if ove_0_17.harass.q2:get() ~= 3 and player:spellSlot(0).state == 0 and not ove_0_18.HaveQ() and player.mana * 100 / player.maxMana >= ove_0_17.harass.minimana:get() then
		local slot_23_4 = ove_0_18.AllyNear2(player.pos)

		if ove_0_17.harass.q2:get() == 1 and ove_0_20.buff.threshq and game.time - ove_0_20.buff.threshq.startTime >= ove_0_20.buff.threshq.endTime - ove_0_20.buff.threshq.startTime - 0.25 then
			player:castSpell("self", 0)

			if ove_0_17.harass.w:get() and slot_23_4 then
				ove_0_51(slot_23_4)
			end
		end

		if ove_0_17.harass.q2:get() == 2 and ove_0_20.pos:dist(player.pos) > 480 and not ove_0_20.buff.sionpassivezombie and (not ove_0_18.UnderTurret(ove_0_20.pos) or not ove_0_20.buff[BUFF_INVULNERABILITY] and not ove_0_20.buff.willrevive and not ove_0_20.buff.undyingrage and ove_0_20.health <= ove_0_18.AADmg(ove_0_20)) and (ove_0_17.harass.e:get() ~= 5 and player:spellSlot(2).state == 0 and (slot_23_4 or ove_0_20.health <= ove_0_18.AADmg(ove_0_20) + ove_0_18.EDmg(ove_0_20)) or ove_0_17.harass.w:get() and player:spellSlot(1).state == 0 and slot_23_4) and ove_0_20.buff.threshq and game.time - ove_0_20.buff.threshq.startTime >= ove_0_20.buff.threshq.endTime - ove_0_20.buff.threshq.startTime - 0.25 then
			player:castSpell("self", 0)

			if ove_0_17.harass.w:get() and slot_23_4 then
				ove_0_51(slot_23_4)
			end
		end
	end

	if ove_0_17.harass.w:get() and ove_0_11.core.can_action() and player:spellSlot(1).state == 0 and ove_0_18.AllyNear(player.path.serverPos, 750) == 0 and player.pos:dist(ove_0_20.pos) < ove_0_20.attackRange - 50 and player.mana * 100 / player.maxMana >= ove_0_17.harass.minimana:get() then
		local slot_23_5 = ove_0_18.AllyNear2(player.pos)

		if slot_23_5 then
			ove_0_51(slot_23_5)
		end
	end
end

local function ove_0_56(arg_24_0)
	-- function 24
	if arg_24_0.owner and arg_24_0.owner == player and arg_24_0.name == "ThreshQLeap" and ove_0_11.menu.combat.key:get() and ove_0_17.combo.w:get() then
		local slot_24_0 = ove_0_18.AllyNear2(player.pos)

		if slot_24_0 then
			if player.pos:dist(slot_24_0.path.serverPos) <= 950 then
				player:castSpell("pos", 1, slot_24_0.path.serverPos)
			elseif player.pos:dist(slot_24_0.path.serverPos) <= 1300 then
				local slot_24_1 = player.path.serverPos + 950 / player.path.serverPos:dist(slot_24_0.path.serverPos) * (slot_24_0.path.serverPos - player.path.serverPos)
				local slot_24_2 = player.pos:dist(slot_24_0.path.serverPos)

				if slot_24_2 then
					for iter_24_0 = 950, slot_24_2, 50 do
						local slot_24_3 = player.pos2D + (slot_24_0.path.serverPos2D - player.pos2D):norm() * iter_24_0

						if navmesh.isWall(vec3(slot_24_3.x, 0, slot_24_3.y)) then
							slot_24_1 = nil
						end
					end

					if slot_24_1 then
						player:castSpell("pos", 1, slot_24_1)
					end
				end
			end
		end
	end

	if arg_24_0.name:find("Turret_Chaos") and arg_24_0.target and arg_24_0.target.team == TEAM_ALLY and arg_24_0.target.type == TYPE_HERO then
		ove_0_51(arg_24_0.target)
	end

	if arg_24_0.owner and arg_24_0.owner.type == TYPE_HERO and arg_24_0.owner.team ~= TEAM_ALLY and arg_24_0.target and arg_24_0.target.type == TYPE_HERO then
		if arg_24_0.target.health / arg_24_0.target.maxHealth <= 0.35 and ove_0_17.combo.w:get() then
			ove_0_51(arg_24_0.target)
		end

		local slot_24_4 = arg_24_0.name:lower():gsub("mis(%w+)", "")
		local slot_24_5 = arg_24_0.name:lower():gsub("attac(%w+)", "")

		if ove_0_29[arg_24_0.name:lower()] or ove_0_29[slot_24_4] or ove_0_29[slot_24_5] then
			for iter_24_1 = 0, objManager.allies_n - 1 do
				local slot_24_6 = objManager.allies[iter_24_1]

				if slot_24_6 and slot_24_6.team == TEAM_ALLY and not slot_24_6.isDead and slot_24_6.isVisible and slot_24_6.isTargetableToTeamFlags and slot_24_6.pos:distSqr(player.pos) <= 902500 and arg_24_0.target == slot_24_6 then
					ove_0_51(slot_24_6)
				end
			end
		end
	end
end

local function ove_0_57()
	-- function 25
	if ove_0_17.misc.key:get() then
		if not ove_0_11.menu.combat.key:get() and not ove_0_11.menu.hybrid.key:get() and not ove_0_11.menu.last_hit.key:get() and not ove_0_11.menu.lane_clear.key:get() then
			local slot_25_0 = player.path.serverPos:lerp(mousePos, 500 / player.path.serverPos:dist(mousePos))

			if slot_25_0 then
				player:move(vec3(slot_25_0))
			end
		end

		local slot_25_1 = ove_0_18.AllyNear3(player.pos)

		if slot_25_1 then
			ove_0_51(slot_25_1)
		end
	end

	if ove_0_17.ekey.push:get() then
		if not ove_0_11.menu.combat.key:get() and not ove_0_11.menu.hybrid.key:get() and not ove_0_11.menu.last_hit.key:get() and not ove_0_11.menu.lane_clear.key:get() then
			local slot_25_2 = player.path.serverPos:lerp(mousePos, 500 / player.path.serverPos:dist(mousePos))

			if slot_25_2 then
				player:move(vec3(slot_25_2))
			end
		end

		local slot_25_3 = ove_0_11.ts.get_result(ove_0_42).obj

		if not slot_25_3 or not ove_0_18.isValid(slot_25_3) then
			return
		end

		if player:spellSlot(2).state == 0 then
			ove_0_49(slot_25_3)
		end
	end

	if ove_0_17.ekey.pull:get() then
		if not ove_0_11.menu.combat.key:get() and not ove_0_11.menu.hybrid.key:get() and not ove_0_11.menu.last_hit.key:get() and not ove_0_11.menu.lane_clear.key:get() then
			local slot_25_4 = player.path.serverPos:lerp(mousePos, 500 / player.path.serverPos:dist(mousePos))

			if slot_25_4 then
				player:move(vec3(slot_25_4))
			end
		end

		local slot_25_5 = ove_0_11.ts.get_result(ove_0_42).obj

		if not slot_25_5 or not ove_0_18.isValid(slot_25_5) then
			return
		end

		if player:spellSlot(2).state == 0 then
			ove_0_48(slot_25_5)
		end
	end
end

local function ove_0_58()
	-- function 26
	if ove_0_13 and ove_0_17.combo.w:get() then
		for iter_26_0 = 1, #ove_0_13.core.skillshots do
			local slot_26_0 = ove_0_13.core.skillshots[iter_26_0]

			slot_26_0.name = slot_26_0.name:lower()

			local slot_26_1 = slot_26_0.name:gsub("mis(%w+)", "")
			local slot_26_2 = slot_26_0.name:gsub("attac(%w+)", "")

			if not player:spellSlot(1).state ~= 0 then
				break
			end

			for iter_26_1 = 0, objManager.allies_n - 1 do
				local slot_26_3 = objManager.allies[iter_26_1]

				if slot_26_3 and slot_26_3.team == TEAM_ALLY and (slot_26_3.buff[BUFF_STUN] or slot_26_3.buff[BUFF_SNARE] or ove_0_30[slot_26_1] or ove_0_29[slot_26_1] or ove_0_30[slot_26_2] or ove_0_29[slot_26_2]) and not slot_26_3.isDead and slot_26_3.isVisible and slot_26_3.isTargetableToTeamFlags and slot_26_3.pos:distSqr(player.pos) <= 902500 and slot_26_0 and slot_26_0.owner and slot_26_0.owner.type == TYPE_HERO and (slot_26_0.polygon and slot_26_0.polygon:Contains(slot_26_3.path.serverPos) ~= 0 or slot_26_0.contains and slot_26_0:contains(slot_26_3)) and (not slot_26_0.data.collision or type(slot_26_0.data.collision) == "number" and slot_26_0.data.collision <= 1 or type(slot_26_0.data.collision) == "table" and #slot_26_0.data.collision == 0) then
					player:castSpell("pos", 1, slot_26_3.path.serverPos)

					break
				end
			end
		end

		for iter_26_2 = 1, #ove_0_13.core.targeted do
			local slot_26_4 = ove_0_13.core.targeted[iter_26_2]

			slot_26_4.name = slot_26_4.name:lower()

			local slot_26_5 = slot_26_4.name:gsub("mis(%w+)", "")
			local slot_26_6 = slot_26_4.name:gsub("attac(%w+)", "")

			if not player:spellSlot(1).state ~= 0 then
				break
			end

			for iter_26_3 = 0, objManager.allies_n - 1 do
				local slot_26_7 = objManager.allies[iter_26_3]

				if slot_26_7 and slot_26_7.team == TEAM_ALLY and (slot_26_7.buff[BUFF_STUN] or slot_26_7.buff[BUFF_SNARE] or ove_0_30[slot_26_5] or ove_0_29[slot_26_5] or ove_0_30[slot_26_6] or ove_0_29[slot_26_6]) and not slot_26_7.isDead and slot_26_7.isVisible and slot_26_7.isTargetableToTeamFlags and slot_26_7.pos:distSqr(player.pos) <= 902500 and slot_26_4 and slot_26_4.owner and slot_26_4.owner.type == TYPE_HERO and slot_26_4.target and slot_26_4.target == slot_26_7 then
					player:castSpell("pos", 1, slot_26_7.path.serverPos)

					break
				end
			end
		end
	end

	if player.isDead then
		return
	end

	if ove_0_24 and ove_0_24.isDead then
		ove_0_24 = nil
	end

	ove_0_20 = ove_0_43()

	if ove_0_11.menu.combat.key:get() then
		ove_0_54()
	elseif ove_0_11.menu.hybrid.key:get() then
		ove_0_55()
	end

	ove_0_57()

	if not ove_0_18.HaveQ() then
		ove_0_11.core.set_pause_attack(5)
	elseif ove_0_18.HaveQ() and ove_0_11.core.is_attack_paused() then
		ove_0_11.core.set_pause_attack(0)
	end
end

local ove_0_59 = game.time

local function ove_0_60()
	-- function 27
	if not ove_0_13 and game.time < ove_0_59 + 10 then
		if ove_0_14 == "/menucn" then
			--graphics.draw_text_2D("请开启HANBOT躲避，否则W技能无效", 15, graphics.width / 2 - 350, graphics.height / 2 - 299, 4294967295)
		else
			--graphics.draw_text_2D("Plz Turn On the internal evade,otherwise most of the block skills will fail", 15, graphics.width / 2 - 350, graphics.height / 2 - 299, 4294967295)
		end
	end

	if not ove_0_17.display.Enable:get() or player.isDead then
		return
	end

	if ove_0_17.display.Target:get() and ove_0_20 and ove_0_20.isVisible and not ove_0_20.isDead and ove_0_20.isOnScreen then
		graphics.draw_circle_xyz(ove_0_20.x, ove_0_20.y, ove_0_20.z, 100, 2, 4294916096, 100)
	end

if ove_0_17.display.Q:get() and player.isOnScreen then
    if player:spellSlot(0).state == 0 then
        -- 深红色
        graphics.draw_circle_xyz(player.x, player.y, player.z, 1100, 1, 0xFF8B0000, 100)
    else
        -- 深红色
        graphics.draw_circle_xyz(player.x, player.y, player.z, 1100, 1, 0xFF8B0000, 100)
    end
end

if ove_0_17.display.W:get() and player.isOnScreen and player:spellSlot(1).state == 0 then
    -- 浅红色
    graphics.draw_circle_xyz(player.x, player.y, player.z, 950, 2, 0xFFFFAAAA, 100)
end

if ove_0_17.display.E:get() and player.isOnScreen and player:spellSlot(2).state == 0 then
    -- 浅蓝色
    graphics.draw_circle_xyz(player.x, player.y, player.z, 500, 2, 0xFFADD8E6, 100)
end

	if ove_0_17.display.Combo:get() then
		for iter_27_0 = 0, objManager.enemies_n - 1 do
			local slot_27_0 = objManager.enemies[iter_27_0]

			if slot_27_0 and not slot_27_0.isDead and slot_27_0.isVisible and slot_27_0.team and slot_27_0.type and slot_27_0.team == TEAM_ENEMY and slot_27_0.type == TYPE_HERO and slot_27_0.isOnScreen and slot_27_0.barPos then
				local slot_27_1 = graphics.height > 1080 and graphics.height / 1080 * 0.905 or 1
				local slot_27_2 = vec2(109 * slot_27_1, 111 * slot_27_1)
				local slot_27_3 = vec2(54 * slot_27_1, 11 * slot_27_1)
				local slot_27_4 = slot_27_0.barPos + slot_27_2 + slot_27_3
				local slot_27_5 = slot_27_4.x
				local slot_27_6 = slot_27_4.y

				local function slot_27_7()
					-- function 28
					if player:spellSlot(0).state == 0 then
						if ove_0_18.HaveQ() then
							return ove_0_18.QDmg(slot_27_0)
						else
							return 0
						end
					else
						return 0
					end
				end

				local slot_27_8 = player:spellSlot(2).state == 0 and ove_0_18.EDmg(slot_27_0) or 0
				local slot_27_9 = player:spellSlot(3).state == 0 and ove_0_18.RDmg(slot_27_0) or 0
				local slot_27_10 = slot_27_0.health - (slot_27_7() + slot_27_8 + slot_27_9 + ove_0_18.AADmg(slot_27_0))
				local slot_27_11 = slot_27_5 + slot_27_0.health / slot_27_0.maxHealth * 102 * slot_27_1
				local slot_27_12 = slot_27_5 + (slot_27_10 > 0 and slot_27_10 or 0) / slot_27_0.maxHealth * 102 * slot_27_1

				graphics.draw_line_2D(slot_27_11, slot_27_6, slot_27_12, slot_27_6, 10, **********)
			end
		end
	end
end

local function ove_0_61(arg_29_0)
	-- function 29
	if arg_29_0 and arg_29_0.spell and arg_29_0.spell.owner and arg_29_0.spell.owner.team ~= TEAM_ALLY and arg_29_0.spell.name == "YasuoW_VisualMis" then
		ove_0_22[arg_29_0.ptr] = arg_29_0
	end
end

local function ove_0_62(arg_30_0)
	-- function 30
	if arg_30_0 and arg_30_0.ptr and ove_0_22[arg_30_0.ptr] then
		ove_0_22[arg_30_0.ptr] = nil
	end
end

local function ove_0_63(arg_31_0)
	-- function 31
	if not ove_0_24 and arg_31_0 and arg_31_0.team == TEAM_ALLY and arg_31_0.name and arg_31_0.name == "ThreshLantern" and arg_31_0.pos2D:distSqr(player.pos2D) < 1690000 then
		ove_0_24 = arg_31_0
	end
end

local function ove_0_64(arg_32_0)
	-- function 32
	if ove_0_24 and arg_32_0 and arg_32_0.ptr and arg_32_0.ptr == ove_0_24.ptr then
		ove_0_24 = nil
	end
end

return {
	get_action = ove_0_58,
	Newpath = ove_0_53,
	process_spell = ove_0_56,
	on_draw = ove_0_60,
	create_obj = ove_0_63,
	delete_obj = ove_0_64,
	create_mis = ove_0_61,
	delete_mis = ove_0_62
}

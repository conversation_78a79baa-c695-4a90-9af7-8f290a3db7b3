local pred = module.internal("pred")
local orb = module.internal("orb")

--延迟函数执行
local delayedActions, delayedActionsExecuter = {}, nil
local function delayAction(func, delay, args)
  if not delayedActionsExecuter then
    function delayedActionsExecuter()
      for t, funcs in pairs(delayedActions) do
        if t <= os.clock() then
          for i = 1, #funcs do
            local f = funcs[i]
            if f and f.func then
              f.func(unpack(f.args or {}))
            end
          end
          delayedActions[t] = nil
        end
      end
    end

    cb.add(cb.tick, delayedActionsExecuter)
  end
  local t = os.clock() + (delay or 0)
  if delayedActions[t] then
    delayedActions[t][#delayedActions[t] + 1] = { func = func, args = args }
  else
    delayedActions[t] = { { func = func, args = args } }
  end
end

--定时执行函数
local _intervalFunction
local function setInterval(userFunction, timeout, count, params)
  if not _intervalFunction then
    function _intervalFunction(userFunction, startTime, timeout, count, params)
      if userFunction(unpack(params or {})) ~= false and (not count or count > 1) then
        delayAction(
          _intervalFunction,
          (timeout - (os.clock() - startTime - timeout)),
          { userFunction, startTime + timeout, timeout, count and (count - 1), params }
        )
      end
    end
  end
  delayAction(_intervalFunction, timeout, { userFunction, os.clock(), timeout or 0, count, params })
end

--控制台打印信息
local function printMsg(msg, color)
  local color = color or 42
  console.set_color(color)
  print(msg)
  console.set_color(15)
end

-- 返回生命值百分比或玩家生命百分比
local function getPercentHealth(obj)
  local obj = obj or player
  return (obj.health / obj.maxHealth) * 100
end

-- 返回蓝量百分比或玩家蓝量百分比
local function getPercentMana(obj)
  local obj = obj or player
  return (obj.mana / obj.maxMana) * 100
end

-- 返回生命值百分比或玩家生命百分比
local function getPercentPar(obj)
  local obj = obj or player
  return (obj.par / obj.maxPar) * 100
end

-- 返回蓝量百分比或玩家蓝量百分比
local function getPercentSar(obj)
  local obj = obj or player
  return (obj.sar / obj.maxSar) * 100
end

--重置走砍
local function resetOrb()
  orb.core.reset()
  orb.core.set_pause(0)
  orb.core.set_pause_move(0)
  orb.core.set_pause_attack(0)
end

--在延迟后重置走砍
local function resetOrbDelay(delay)
  if delay and delay >= 0 then
    DelayAction(ResetOrb(), delay)
  end
end

--检查buff类型
local function checkBuffType(obj, bufftype)
  if obj and obj.buff then
    for _, buff in pairs(obj.buff) do
      if buff and buff.valid and buff.type == bufftype and (buff.stacks > 0 or buff.stacks2 > 0) then
        return true,buff
      end
    end
  end
end

--获得buff
local function getBuff(obj, buffname)
  if obj and obj.buff then
    for _, buff in pairs(obj.buff) do
      if buff and buff.valid and string.lower(buff.name) == string.lower(buffname) and (buff.stacks > 0 or buff.stacks2 > 0) then
        return buff
      end
    end
  end
end

--检查buff通过buff名称
local function checkBuff(obj, buffname)
  if obj and obj.buff then
    for _, buff in pairs(obj.buff) do
      if buff and buff.valid and string.lower(buff.name) == string.lower(buffname) and (buff.stacks > 0 or buff.stacks2 > 0) then
        return true
      end
    end
  end
end

--检查buff通过buff名称获取剩余时间
local function checkBuffWithTimeEnd(obj, buffname)
  if obj and obj.buff then
    for _, buff in pairs(obj.buff) do
      if buff and buff.valid and buff.name == buffname and (buff.stacks > 0 or buff.stacks2 > 0) then
        if game.time <= buff.endTime then
          return true, buff.endTime
        end
      end
    end
  end
end

--检查buff通过buff名称判断是否结束
local function checkBuffIsTimeEnd(obj, buffname)
  if obj and obj.buff then
    for _, buff in pairs(obj.buff) do
      if buff and buff.valid and buff.name == buffname and (buff.stacks > 0 or buff.stacks2 > 0) then
        if game.time <= buff.endTime then
          return true
        end
      end
    end
  end
end

local function getBuffStacks(target, buffname)
  local buff = getBuff(target, buffname)
  if buff then
    return  math.max(buff.stacks, buff.stacks2) 
  else
    return -1
  end
end

--获得目标护盾+血量
local yasuoShield = { 100, 105, 110, 115, 120, 130, 140, 150, 165, 180, 200, 225, 255, 290, 330, 380, 440, 510 }
local function getShieldedHealth(damageType, target)
  local shield = 0   -- bitwise flags when :/
  if damageType:find "AD" then
    shield = target.physicalShield
  elseif damageType:find "AP" then
    shield = target.magicalShield
  elseif damageType:find "ALL" then
    shield = target.allShield +
    (target.charName == "Yasuo" and target.mana == target.maxMana and yasuoShield[target.levelRef] or 0)
  end
  return target.health + shield
end

-- 获得物理伤害减免
local function getPhysicalReduction(target, damageSource)
  local damageSource = damageSource or player
  local armor =
      ((target.bonusArmor * damageSource.percentBonusArmorPenetration) + (target.armor - target.bonusArmor)) *
      damageSource.percentArmorPenetration
  local lethality =
      (damageSource.physicalLethality * .4) + ((damageSource.physicalLethality * .6) * (damageSource.levelRef / 18))
  armor = math.max(armor, 0)  -- 修正护甲不能小于0
  return armor >= 0 and (100 / (100 + (armor - lethality))) or (2 - (100 / (100 - (armor - lethality))))
end

-- 获得魔法伤害减免
local function getMagicReduction(target, damageSource)
  local damageSource = damageSource or player
  local magicResist = (target.spellBlock * damageSource.percentMagicPenetration) - damageSource.flatMagicPenetration
  magicResist = math.max(magicResist, 0)  -- 修正魔抗不能小于0
  return magicResist >= 0 and (100 / (100 + magicResist)) or (2 - (100 / (100 - magicResist)))
end

--伤害减免buff数据(后面研究)
local buffReduces = {
  ["FerociousHowl"] = function(target)
    return (.55 - .1 * target:spellSlot(3).level)
  end,
  ["AnnieE"] = function(target)
    return (.9 - .06 * target:spellSlot(1).level)
  end,
  ["Meditate"] = function(target)
    return (.5 - .05 * target:spellSlot(1).level)
  end,
  ["braumeshieldbuff"] = function(target)
    return (.725 - .025 * target:spellSlot(2).level)
  end,
  ["GalioW"] = function(target, bPhysical)
    if bPhysical then
      return (.925 - .025 * target:spellSlot(1).level - target.bonusSpellBlock * 0.04)
    else
      return (.85 - .05 * target:spellSlot(1).level - target.bonusSpellBlock * 0.08)
    end
  end,
  ["WarwickE"] = function(target)
    return (.70 - .05 * target:spellSlot(2).level)
  end,
  ["ireliawdefense"] = function(target)
    if bPhysical then
      return (.5 - .07 * target.flatMagicDamageMod * target.percentMagicDamageMod)
    else
      return 1
    end
  end,
  ["malzaharpassiveshield"] = function()
    return 1
  end,
  ["GarenW"] = function()
    return .4
  end,
  ["gragaswself"] = function(target)
    return (.92 - .02 * target:spellSlot(2).level - .04 * target.flatMagicDamageMod * target.percentMagicDamageMod)
  end,
}

-- 计算整体伤害减免(大概率有问题等修复)
local function getTotalDamageReduction(target, source, bPhysical)
  local multiplier = 1
  if target.type == TYPE_HERO then
    for name, buffReduce in pairs(buffReduces) do
      local buff = target.buff[name]
      if buff and buff.endTime > game.time then
        multiplier = multiplier * buffReduce(target, bPhysical)
      end
    end
  end
  if not bPhysical and target.charName == "Kassadin" then
    multiplier = multiplier * 0.85
  end
  for i = 0, source.buffManager.count - 1 do
    local buff = source.buffManager:get(i)
    if buff and buff.valid and buff.endTime > game.time then
      if buff.name == "summonerexhaustdebuff" then
        multiplier = multiplier * .6
      elseif buff.name == "itemsmitechallenge" then
        multiplier = multiplier * .6
      elseif buff.name == "itemphantomdancerdebuff" and buff.source.ptr == target.ptr then
        multiplier = multiplier * .88
      elseif buff.name == "abyssalscepteraura" and not bPhysical then
        multiplier = multiplier * 1.15
      end
    end
  end
  return multiplier
end

--计算物理伤害
local function calculatePhysicalDamage(target, source, ad_dmg)
  assert(target, "calculatePhysicalDamage: target is nil")
  if type(source) == "number" then
    source, ad_dmg = ad_dmg, source
  end
  source = source or player
  return (ad_dmg or source.totalAd)
      * getPhysicalReduction(target, source)
      * getTotalDamageReduction(target, source, true)
end

--计算魔法伤害
local function calculateMagicalDamage(target, source, ap_dmg)
  assert(target, "calculateMagicalDamage: target is nil")
  if type(source) == "number" then
    source, ap_dmg = ap_dmg, source
  end
  source = source or player
  return (ap_dmg or source.totalAp)
      * getMagicReduction(target, source)
      * getTotalDamageReduction(target, source)
end

--额外伤害表(太古老了等下次再研究)
local bonusDamageTable = { -- 待完成: Lulu, Rumble, Nautilus, TwistedFate, Ziggs
  ["Aatrox"] = function(source, target, ADDmg, APDmg, TRUEDmg)
    return ADDmg + (getBuffStacks(source, "aatroxpassive") > 0 and 35 * source:spellSlot(1).level + 25 or 0), APDmg,
        TRUEDmg
  end,
  ["Ashe"] = function(source, target, ADDmg, APDmg, TRUEDmg)
    return
    ADDmg *
    (getBuffStacks(source, "asheqattack") > 0 and 5 * (0.01 * source:spellSlot(0).level + 0.22) or getBuffStacks(target, "ashepassiveslow") > 0 and (1.1 + source.crit) or 1),
        APDmg, TRUEDmg
  end,
  ["Bard"] = function(source, target, ADDmg, APDmg, TRUEDmg)
    return ADDmg,
        APDmg +
        (getBuffStacks(source, "bardpspiritammocount") > 0 and 30 + source.levelRef * 15 + 0.3 * source.flatPhysicalDamageMod * source.percentPhysicalDamageMod or 0),
        TRUEDmg
  end,
  ["Blitzcrank"] = function(source, target, ADDmg, APDmg, TRUEDmg)
    return ADDmg * (getBuffStacks(source, "powerfist") + 1), APDmg, TRUEDmg
  end,
  ["Caitlyn"] = function(source, target, ADDmg, APDmg, TRUEDmg, missile)
    return
    ADDmg +
    (getBuffStacks(source, "caitlynheadshot") > 0 and (source.baseAttackDamage + source.flatPhysicalDamageMod) * source.percentPhysicalDamageMod * ((source.levelRef > 12 and 1 or source.levelRef > 6 and .75 or .5) + source.crit) or 0),
        APDmg, TRUEDmg
  end,
  ["Chogath"] = function(source, target, ADDmg, APDmg, TRUEDmg)
    return ADDmg,
        APDmg +
        (getBuffStacks(source, "vorpalspikes") > 0 and 15 * source:spellSlot(2).level + 5 + .3 * source.flatPhysicalDamageMod * source.percentPhysicalDamageMod or 0),
        APDmg, TRUEDmg
  end,
  ["Corki"] = function(source, target, ADDmg, APDmg, TRUEDmg)
    return ADDmg, APDmg, TRUEDmg + (getBuffStacks(source, "rapidreload") > 0 and .1 * (ADDmg) or 0)
  end,
  ["Darius"] = function(source, target, ADDmg, APDmg, TRUEDmg)
    return ADDmg + (getBuffStacks(source, "dariusnoxiantacticsonh") > 0 and .4 * (ADDmg) or 0), APDmg, TRUEDmg
  end,
  ["Diana"] = function(source, target, ADDmg, APDmg, TRUEDmg)
    return ADDmg,
        APDmg +
        (getBuffStacks(source, "dianaarcready") > 0 and math.max(5 * source.levelRef + 15, 10 * source.levelRef - 10, 15 * source.levelRef - 60, 20 * source.levelRef - 125, 25 * source.levelRef - 200) + .8 * source.flatPhysicalDamageMod * source.percentPhysicalDamageMod or 0),
        TRUEDmg
  end,
  ["Draven"] = function(source, target, ADDmg, APDmg, TRUEDmg)
    return ADDmg + (getBuffStacks(source, "dravenspinning") > 0 and (.1 * source:spellSlot(0).level + .35) * (ADDmg) or 0),
        APDmg, TRUEDmg
  end,
  ["Ekko"] = function(source, target, ADDmg, APDmg, TRUEDmg)
    return ADDmg,
        APDmg +
        (getBuffStacks(source, "ekkoeattackbuff") > 0 and 30 * source:spellSlot(2).level + 20 + .2 * source.flatPhysicalDamageMod * source.percentPhysicalDamageMod or 0),
        TRUEDmg
  end,
  ["Fizz"] = function(source, target, ADDmg, APDmg, TRUEDmg)
    return ADDmg,
        APDmg +
        (getBuffStacks(source, "fizzseastonepassive") > 0 and 5 * source:spellSlot(1).level + 5 + .3 * source.flatPhysicalDamageMod * source.percentPhysicalDamageMod or 0),
        TRUEDmg
  end,
  ["Garen"] = function(source, target, ADDmg, APDmg, TRUEDmg)
    return ADDmg + (getBuffStacks(source, "garenq") > 0 and 25 * source:spellSlot(0).level + 5 + .4 * (ADDmg) or 0),
        APDmg, TRUEDmg
  end,
  ["Gragas"] = function(source, target, ADDmg, APDmg, TRUEDmg)
    return ADDmg,
        APDmg +
        (getBuffStacks(source, "gragaswattackbuff") > 0 and 30 * source:spellSlot(1).level - 10 + .3 * source.flatPhysicalDamageMod * source.percentPhysicalDamageMod + (.01 * source:spellSlot(1).level + .07) * (target.maxHealth) or 0),
        TRUEDmg
  end,
  ["Graves"] = function(source, target, ADDmg, APDmg, TRUEDmg)
    local dist = source.pos2D:dist(target.pos2D)
    return
    ADDmg *
    (gravesDmgTable[1][source.levelRef] + gravesDmgTable[2][source.levelRef] * (dist < 100 and 3 or dist < 200 and 2 or dist < 300 and 1 or 0)),
        APDmg, TRUEDmg
  end,
  ["Irelia"] = function(source, target, ADDmg, APDmg, TRUEDmg)
    return ADDmg, 0,
        TRUEDmg +
        (getBuffStacks(source, "ireliahitenstylecharged") > 0 and 25 * source:spellSlot(0).level + 5 + .4 * (ADDmg) or 0)
  end,
  ["Jax"] = function(source, target, ADDmg, APDmg, TRUEDmg)
    return ADDmg,
        APDmg +
        (getBuffStacks(source, "jaxempowertwo") > 0 and 35 * source:spellSlot(1).level + 5 + .6 * source.flatPhysicalDamageMod * source.percentPhysicalDamageMod or 0),
        TRUEDmg
  end,
  ["Jayce"] = function(source, target, ADDmg, APDmg, TRUEDmg)
    return ADDmg,
        APDmg +
        (getBuffStacks(source, "jaycepassivemeleeatack") > 0 and 40 * source:spellSlot(3).level - 20 + .4 * source.flatPhysicalDamageMod * source.percentPhysicalDamageMod or 0),
        TRUEDmg
  end,
  ["Jhin"] = function(source, target, ADDmg, APDmg, TRUEDmg)
    return
    (getBuffStacks(source, "jhinpassiveattackbuff") > 0 and (target.maxHealth - target.health) * (source.levelRef < 6 and 0.15 or source.levelRef < 11 and 0.2 or 0.25) or 0),
        APDmg, TRUEDmg
  end,
  ["Jinx"] = function(source, target, ADDmg, APDmg, TRUEDmg)
    return ADDmg + (getBuffStacks(source, "jinxq") > 0 and .1 * (ADDmg) or 0), APDmg, TRUEDmg
  end,
  ["Kalista"] = function(source, target, ADDmg, APDmg, TRUEDmg)
    return ADDmg * 0.9, APDmg, TRUEDmg
  end,
  ["Kassadin"] = function(source, target, ADDmg, APDmg, TRUEDmg)
    return ADDmg,
        APDmg +
        (getBuffStacks(source, "netherbladebuff") > 0 and 20 + .1 * source.flatPhysicalDamageMod * source.percentPhysicalDamageMod or 0) +
        (getBuffStacks(source, "netherblade") > 0 and 25 * source:spellSlot(1).level + 15 + .6 * source.flatPhysicalDamageMod * source.percentPhysicalDamageMod or 0),
        TRUEDmg
  end,
  ["Kayle"] = function(source, target, ADDmg, APDmg, TRUEDmg)
    return ADDmg,
        APDmg +
        (getBuffStacks(source, "kaylerighteousfurybuff") > 0 and 5 * source:spellSlot(2).level + 5 + .15 * source.flatPhysicalDamageMod * source.percentPhysicalDamageMod or 0) +
        (getBuffStacks(source, "judicatorrighteousfury") > 0 and 5 * source:spellSlot(2).level + 5 + .15 * source.flatPhysicalDamageMod * source.percentPhysicalDamageMod or 0),
        TRUEDmg
  end,
  ["Leona"] = function(source, target, ADDmg, APDmg, TRUEDmg)
    return ADDmg,
        APDmg +
        (getBuffStacks(source, "leonashieldofdaybreak") > 0 and 30 * source:spellSlot(0).level + 10 + .3 * source.flatPhysicalDamageMod * source.percentPhysicalDamageMod or 0),
        TRUEDmg
  end,
  ["Lux"] = function(source, target, ADDmg, APDmg, TRUEDmg)
    return ADDmg,
        APDmg +
        (getBuffStacks(target, "luxilluminatingfraulein") > 0 and 10 + (source.levelRef * 8) + (source.flatPhysicalDamageMod * source.percentPhysicalDamageMod * 0.2) or 0),
        TRUEDmg
  end,
  ["MasterYi"] = function(source, target, ADDmg, APDmg, TRUEDmg)
    return ADDmg + (getBuffStacks(source, "doublestrike") > 0 and .5 * (ADDmg) or 0), APDmg, TRUEDmg
  end,
  ["Nocturne"] = function(source, target, ADDmg, APDmg, TRUEDmg)
    return ADDmg + (getBuffStacks(source, "nocturneumrablades") > 0 and .2 * (ADDmg) or 0), APDmg, TRUEDmg
  end,
  ["Orianna"] = function(source, target, ADDmg, APDmg, TRUEDmg)
    return ADDmg,
        APDmg + 2 + 8 * math.ceil(source.levelRef / 3) +
        0.15 * source.flatPhysicalDamageMod * source.percentPhysicalDamageMod, TRUEDmg
  end,
  ["RekSai"] = function(source, target, ADDmg, APDmg, TRUEDmg)
    return ADDmg + (getBuffStacks(source, "reksaiq") > 0 and 10 * source:spellSlot(0).level + 5 + .2 * (ADDmg) or 0),
        TRUEDmg
  end,
  ["Rengar"] = function(source, target, ADDmg, APDmg, TRUEDmg)
    return
    ADDmg +
    (getBuffStacks(source, "rengarqbase") > 0 and math.max(30 * source:spellSlot(0).level + (.05 * source:spellSlot(0).level - .05) * (ADDmg)) or 0) +
    (getBuffStacks(source, "rengarqemp") > 0 and math.min(15 * source.levelRef + 15, 10 * source.levelRef + 60) + .5 * (ADDmg) or 0),
        APDmg, TRUEDmg
  end,
  ["Shyvana"] = function(source, target, ADDmg, APDmg, TRUEDmg)
    return ADDmg +
    (getBuffStacks(source, "shyvanadoubleattack") > 0 and (.05 * source:spellSlot(0).level + .75) * (ADDmg) or 0), APDmg,
        TRUEDmg
  end,
  ["Talon"] = function(source, target, ADDmg, APDmg, TRUEDmg)
    return
    ADDmg +
    (getBuffStacks(source, "talonnoxiandiplomacybuff") > 0 and 30 * source:spellSlot(0).level + .3 * (source.flatPhysicalDamageMod * source.percentPhysicalDamageMod) or 0),
        APDmg, TRUEDmg
  end,
  ["Teemo"] = function(source, target, ADDmg, APDmg, TRUEDmg)
    return ADDmg, APDmg + 10 * source:spellSlot(2).level +
    0.3 * source.flatPhysicalDamageMod * source.percentPhysicalDamageMod, TRUEDmg
  end,
  ["Trundle"] = function(source, target, ADDmg, APDmg, TRUEDmg)
    return
    ADDmg +
    (getBuffStacks(source, "trundletrollsmash") > 0 and 20 * source:spellSlot(0).level + ((0.05 * source:spellSlot(0).level + 0.095) * (ADDmg)) or 0),
        APDmg, TRUEDmg
  end,
  ["Varus"] = function(source, target, ADDmg, APDmg, TRUEDmg)
    return ADDmg,
        APDmg +
        (getBuffStacks(source, "varusw") > 0 and (4 * source:spellSlot(1).level + 6 + .25 * source.flatPhysicalDamageMod * source.percentPhysicalDamageMod) or 0),
        TRUEDmg
  end,
  ["Vayne"] = function(source, target, ADDmg, APDmg, TRUEDmg)
    return ADDmg + (getBuffStacks(source, "vaynetumblebonus") > 0 and (.05 * source:spellSlot(0).level + .45) * (ADDmg) or 0),
        APDmg,
        TRUEDmg +
        (getBuffStacks(target, "vaynesilvereddebuff") > 1 and 15 * source:spellSlot(1).level + 35 + ((0.025 * source:spellSlot(1).level + 0.015) * target.maxHealth) or 0)
  end,
  ["Vi"] = function(source, target, ADDmg, APDmg, TRUEDmg)
    return
    ADDmg +
    (getBuffStacks(source, "vie") > 0 and 15 * source:spellSlot(2).level - 10 + .15 * (ADDmg) + .7 * source.flatPhysicalDamageMod * source.percentPhysicalDamageMod or 0),
        APDmg, TRUEDmg
  end,
  ["Volibear"] = function(source, target, ADDmg, APDmg, TRUEDmg)
    return ADDmg + (getBuffStacks(source, "volibearq") > 0 and 30 * source:spellSlot(0).level or 0), APDmg, TRUEDmg
  end
}


ZhihuimorenLevelDamage = {15,25,35,45,55,65,75,76.25,77.5,78.75,80}
HaiyaoLevelDamage = {35,40,45,50,55,60,65,70,75,80,85}
local function AttackSpecialDamage(target)
    onhitPhysical = 0
    onhitMagic = 0
    Nashi = false
    Pobai = false
    Haiyao = false
    Zhihuimoren = false
    Yangdao = false
    Shenfen = false
    Jujiu = false
    Jiutoushe = false
    local rune = {}
    for i = 0, 5 do
        local ru = player.rune:get(i)
        if ru.id > 0 then
          rune[ru.id] = true
        end
        --print(player:itemID(i))
        if player:itemID(i) == 3115 then
            Nashi = true
        end
        if player:itemID(i) == 3153 then
            Pobai = true
        end
        if player:itemID(i) == 6672 then
            Haiyao = true
        end
        if player:itemID(i) == 3091 then
            Zhihuimoren = true
        end
        if player:itemID(i) == 3124 then
            Yangdao = true
        end
        if player:itemID(i) == 6632 then
            Shenfen = true
        end
        if player:itemID(i) == 3748 then
            Jujiu = true
        end
        if player:itemID(i) == 3074 then
            Jiutoushe = true
        end
    end
    if Nashi then
        onhitMagic = onhitMagic + 15 + player.totalAp * 0.2
    end
    if Zhihuimoren then
        if (player.levelRef >= 1 and player.levelRef < 9) then
            leveldamage = ZhihuimorenLevelDamage[1]
        end
        if (player.levelRef == 9) then
            leveldamage = ZhihuimorenLevelDamage[2]
        end
        if (player.levelRef >=10 and player.levelRef <= 18) then
            leveldamage = ZhihuimorenLevelDamage[player.levelRef-7]
        end
    
        onhitMagic = onhitMagic + leveldamage
    end
    if Pobai then
        if target.type == TYPE_MINION then
			onhitPhysical = onhitPhysical + math.min(math.max(15, target.health * 0.12), 60)
		else
			onhitPhysical = onhitPhysical + math.max(15, target.health * 0.12)
		end
    end
    if Yangdao then
        onhitMagic = onhitMagic + 30
    end
    if Jujiu then
        onhitMagic = onhitPhysical + 4 + player.maxHealth * 0.015
    end
    if Jiutoushe then
        onhitPhysical = onhitPhysical + 0.4 * player.totalAd
    end
    --if Shenfen and util.checkBuff("6632buff") then
        --onhitPhysical = onhitPhysical + 1.6 * player.baseAd + 0.04 * target.maxHealth
    --end
    if Haiyao and getBuffStacks(player,"6672buff") == 2 then
        if (player.levelRef >= 1 and player.levelRef < 9) then
            leveldamage = ZhihuimorenLevelDamage[1]
        end
        if (player.levelRef == 9) then
            leveldamage = ZhihuimorenLevelDamage[2]
        end
        if (player.levelRef >=10 and player.levelRef <= 18) then
            leveldamage = ZhihuimorenLevelDamage[player.levelRef-7]
        end
        onhitPhysical = onhitPhysical + leveldamage + 0.65 * player.totalAd + 0.6 * player.totalAp
    end
    if rune[8014] then --计算天赋致命一击伤害
      if target.type == TYPE_HERO and getPercentHealth(target) < 40 then
        onhitPhysical = onhitPhysical * 1.08
        onhitMagic = onhitMagic * 1.08
      end
    end

    return onhitPhysical,onhitMagic
end
--计算aa伤害(很多问题，暂时先用着)
--计算了天赋致命一击，复仇之矛被动
--Todo：装备，英雄额外伤害等
local function calculateFullAADamage(target, source, addad, addap, addtrue)
  if source.type == TYPE_HERO then
      local ad = (addad or 0) + source.totalAd
      local ap, tr = addap or 0, addtrue or 0
      local crit = source.crit > 0.9
      --local items = {}
      local rune = {}
      for i = 0, 6 do
            --local id = source:itemID(i)

            local ru = player.rune:get(i)
          -- printMsg("第"..i.."个天赋名："..ru.id)
            if ru and ru.id > 0 then
                rune[ru.id] = true
            end
        end

      if crit then
        ad = ad * source.critDamageMultiplier
      end
      if source.charName == "Kalista" then
        ad = ad * 0.9
      end
      if source.charName == "Caitlyn" then
        if getBuffStacks(source, "caitlynheadshot") > 0 then
            ad = ad * (1.1 + source.crit)
        end
      end
      if rune[8014] then --计算天赋致命一击伤害
        if target.type == TYPE_HERO and getPercentHealth(target) < 40 then
          ad = ad * 1.08
        end
      end
      --额外伤害表未修复
      if bonusDamageTable[source.charName] then
        ad, ap, tr = bonusDamageTable[source.charName](source, target, ad, ap, tr)
      end
      local spad,spap  = AttackSpecialDamage(target)
      return calculatePhysicalDamage(target, source, ad+spad)
          + calculateMagicalDamage(target, source, ap+spap) + (tr or 0) 
  end
  return 0
end

local function getIgniteDamage(target)
  local damage = 50 + (20 * player.levelRef)
  if target then
    damage = damage - (getShieldedHealth("AD", target) - target.health)
  end
  return damage
end

--获得攻击范围
local function getAARange(obj, source)
  return source.attackRange + source.boundingRadius + (obj and obj.boundingRadius or 0)
end

--是否在攻击范围内
local function isInAARange(obj, source)
  if getAARange(obj, source) > obj.pos:dist(source.pos) then
    return true
  else
    return false
  end
end

--目标是否无敌
local function isInvincible(object)
  for i = 0, object.buffManager.count - 1 do
      local buff = object.buffManager:get(i)
      if buff and buff.valid and buff.type == 17 then
          return true
      end
  end
end
--目标是否有效
local function isTargetValid(object)
  return (
      object
      and object.ptr ~= 0
      and not object.isDead
      and object.isVisible
      and object.isTargetable
      and not isInvincible(object)
  )
end

--在范围内英雄是否有效
local function isHeroValid(object, range)
  return (
      object
      and object.ptr ~= 0
      and object.type == TYPE_HERO
      and not object.isDead
      and object.isVisible
      and (object.team == player.team
          or object.isTargetable
          and not isInvincible(object))
      and (not range or object.pos2D:distSqr(player.pos2D) < range * range)
  )
end
--小兵是否有效
local function isMinionValid(object, ignoreTeam)
  return (
      object
      and object.ptr ~= 0
      and object.type == TYPE_MINION
      and (ignoreTeam or object.team ~= TEAM_ALLY)
      and not object.isDead
      and object.isVisible
      and object.isTargetable
      and object.health > 0
      and object.moveSpeed > 0
      and object.maxHealth > 5
      and object.maxHealth < 100000
  )
end

--对像在延迟后的预判位置
local function getPredictedPos(object, delay)
  if not isTargetValid(object) or not object.path or not delay or not object.moveSpeed then
      return object.pos
  end
  pred = pred or module.internal("pred")
  local pred_pos = pred.core.lerp(object.path, network.latency + delay, object.moveSpeed)
  return vec3(pred_pos.x, object.y, pred_pos.y)
end

--对象是否在逃离
local function isFleeing(object, source)
  if not isTargetValid(object) or not object.path or not object.path.isActive or not object.moveSpeed then
      return false
  end
  pred = pred or module.internal("pred")
  local pred_pos = pred.core.lerp(object.path, network.latency + .25, object.moveSpeed)
  return vec3(pred_pos.x, object.y, pred_pos.y):dist(source.pos) > object.pos:dist(source.pos)
end
--是否在远离我
local function isFleeingFromMe(object)
  return isFleeing(object, objManager.player)
end

--是否在屏幕上
local function isPosOnScreen(pos)
  local pos2D = graphics.world_to_screen(pos)
  if pos2D.x < 0 or pos2D.x > graphics.width
      or pos2D.y < 0 or pos2D.y > graphics.height
  then
      return false
  end
  return true
end

--根据类型和队伍生成函数，返回指定位置与距离内的对象
local function makeObjectInRangeFunc(_type, _team)
  if _type == TYPE_HERO then
      local ref = _team == player.team and "allies" or "enemies"
      return (function(range, pos)
          pos = pos or player.pos
          range = range * range
          local result = {}
          for h = 0, objManager[ref .. "_n"] - 1 do
              local hero = objManager[ref][h]
              if isHeroValid(hero) and hero.pos:distSqr(pos) < range then
                  result[#result + 1] = hero
              end
          end
          return result
      end)
  elseif _type == TYPE_MINION then
      return (function(range, pos, _t)
          if type(pos) == "number" then pos, _t = _t, pos end
          pos = pos or player.pos
          range = range * range
          _t = _t or _team or TEAM_ENEMY
          local result, minions = {}, objManager.minions[_t]
          for m = 0, objManager.minions.size[_t] - 1 do
              local minion = minions[m]
              if isMinionValid(minion, true) and minion.pos:distSqr(pos) < range then
                  result[#result + 1] = minion
              end
          end
          return result
      end)
  end
end

local enemyHeroes
local function getEnemyHeroes()
    if enemyHeroes then
        return enemyHeroes
    end
    enemyHeroes = {}
    for h = 0, objManager.enemies_n - 1 do
        local hero = objManager.enemies[h]
        enemyHeroes[#enemyHeroes + 1] = hero
    end
    return enemyHeroes
end

local allyHeroes
local function getAllyHeroes()
    if allyHeroes then
        return allyHeroes
    end
    allyHeroes = {}
    for h = 0, objManager.allies_n - 1 do
        local hero = objManager.allies[h]
        allyHeroes[#allyHeroes + 1] = hero
    end
    return allyHeroes
end

local function getEnemyTurrets()
  turrets = {}
  objManager.loop(function(obj)
    if obj.type == TYPE_TURRET and obj.team == TEAM_ENEMY then
      table.insert(turrets, obj)
    end
  end)
  return turrets
end

local function isUnderTurret(pos)
  local turrets = getEnemyTurrets()
  for _, turret in ipairs(turrets) do
    if turret.pos:dist(pos) < 900 then
      return true,turret
    end
  end
  return false
end

local function VectorPointProjectionOnLineSegment(v1, v2, v)
  local cx, cy, ax, ay, bx, by = v.x, (v.z or v.y), v1.x, (v1.z or v1.y), v2.x, (v2.z or v2.y)
  local rL = ((cx - ax) * (bx - ax) + (cy - ay) * (by - ay)) / ((bx - ax) ^ 2 + (by - ay) ^ 2)
  local pointLine = vec2(ax + rL * (bx - ax), ay + rL * (by - ay))
  local rS = rL < 0 and 0 or (rL > 1 and 1 or rL)
  local isOnSegment = rS == rL
  local pointSegment = isOnSegment and pointLine or vec2(ax + rS * (bx - ax), ay + rS * (by - ay))
  return pointSegment, pointLine, isOnSegment
end

local function VectorIntersection(a1, b1, a2, b2)
  local x1, y1, x2, y2, x3, y3, x4, y4 = a1.x, a1.z or a1.y, b1.x, b1.z or b1.y, a2.x, a2.z or a2.y, b2.x, b2.z or b2.y
  local r, s, u, v, k, l = x1 * y2 - y1 * x2, x3 * y4 - y3 * x4, x3 - x4, x1 - x2, y3 - y4, y1 - y2
  local px, py, divisor = r * u - v * s, r * k - l * s, v * k - l * u
  return divisor ~= 0 and vec2(px / divisor, py / divisor)
end

local function CountObjectsOnLineSegment(source, EndPos, width, objects, count, valid)
  local n = 0
  for i = 1, count  do
      local object = objects[i]
      if valid(object) then
          local pointSegment, _, isOnSegment = VectorPointProjectionOnLineSegment(source, EndPos, object)
          local w = width
          if isOnSegment and object.pos2D:distSqr(pointSegment) < w * w
                         and source:distSqr(EndPos) > source:distSqr(object.pos) then
              n = n + 1
          end
      end
  end
  return n
end

local function GetLineFarmPosition(source, range, width, objects, count, valid)
  local BestPos
  local BestHit = 0
  local AllHit = false
  for i = 1, count  do
      local object = objects[i]
      if valid(object) and object.pos:distSqr(source.pos) < range*range then
          if object.name:find("Dragon") or object.name:find("Herald") or object.name:find("Baron") then
            AllHit = true
              return object, 1,AllHit
          end
          local EndPos = source.pos + range * (object.pos - source.pos):norm()
          local hit = CountObjectsOnLineSegment(source.pos, EndPos, width, objects, count, valid)
          if hit > BestHit then
              BestHit = hit
              BestPos = object
              if BestHit == count then
                  AllHit = true
                  break
              end
          end
      end
  end
  return BestPos, BestHit,AllHit
end

--返回obj表与在位置附近的数量
local function CountObjectsNearPos(pos, radius, objects, validFunc)
  local n, o = 0, nil
  for i, object in pairs(objects) do
      if validFunc(object) and pos:distSqr(object.pos) <= radius * radius then
          n = n + 1
          o[n] = object  
      end
  end
  return n, o
end

local hard_cc = {
  [5] = true, -- stun
  [8] = true, -- taunt
  [11] = true, -- snare
  [18] = true, -- sleep
  [21] = true, -- fear
  [22] = true, -- charm
  [24] = true, -- suppression
  [28] = true, -- flee
  [29] = true, -- knockup
  [30] = true -- knockback
}

return {
  delayAction = delayAction,
  setInterval = setInterval,
  printMsg = printMsg,

  getPercentHealth = getPercentHealth,
  getPercentMana = getPercentMana,
  getPercentPar = getPercentPar,
  getPercentSar = getPercentSar,

  resetOrb = resetOrb,
  resetOrbDelay = resetOrbDelay,

  checkBuffType = checkBuffType,
  getBuff = getBuff,
  checkBuff = checkBuff,
  checkBuffWithTimeEnd = checkBuffWithTimeEnd,
  checkBuffIsTimeEnd = checkBuffIsTimeEnd,
  getBuffStacks = getBuffStacks,

  getShieldedHealth = getShieldedHealth,
  getTotalDamageReduction = getTotalDamageReduction,
  getPhysicalReduction = getPhysicalReduction,
  getMagicReduction = getMagicReduction,
  calculatePhysicalDamage = calculatePhysicalDamage,
  calculateMagicalDamage = calculateMagicalDamage,
  calculateFullAADamage = calculateFullAADamage,
  getIgniteDamage = getIgniteDamage,

  getAARange = getAARange,
  isInAARange = isInAARange,
  
  isTargetValid = isTargetValid,
  isHeroValid = isHeroValid,
  isMinionValid = isMinionValid,
  getPredictedPos = getPredictedPos,
  isFleeing = isFleeing,
  isFleeingFromMe = isFleeingFromMe,
  isPosOnScreen = isPosOnScreen,
      
  makeObjectInRangeFunc = makeObjectInRangeFunc,
  GetAllyHeroesInRange = makeObjectInRangeFunc(TYPE_HERO, TEAM_ALLY),
  GetEnemyHeroesInRange = makeObjectInRangeFunc(TYPE_HERO, TEAM_ENEMY),
  GetAllyMinionsInRange = makeObjectInRangeFunc(TYPE_MINION, TEAM_ALLY),
  GetEnemyMinionsInRange = makeObjectInRangeFunc(TYPE_MINION, TEAM_ENEMY),
  GetMinionsInRange = makeObjectInRangeFunc(TYPE_MINION),

  getEnemyHeroes = getEnemyHeroes,
  getAllyHeroes = getAllyHeroes,
  getEnemyTurrets = getEnemyTurrets,
  isUnderTurret = isUnderTurret,

  VectorPointProjectionOnLineSegment = VectorPointProjectionOnLineSegment,
  VectorIntersection = VectorIntersection,
  CountObjectsOnLineSegment = CountObjectsOnLineSegment,
  GetLineFarmPosition = GetLineFarmPosition,
  CountObjectsNearPos = CountObjectsNearPos,
  AttackSpecialDamage = AttackSpecialDamage,
  hard_cc = hard_cc
}

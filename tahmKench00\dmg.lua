

local ove_0_10 = player:spellSlot(0)
local ove_0_11 = player:spellSlot(1)
local ove_0_12 = player:spellSlot(2)
local ove_0_13 = player:spellSlot(3)
local ove_0_14 = module.internal("orb")
local ove_0_15 = {
	75,
	125,
	175
}
local ove_0_16 = {
	100,
	250,
	400
}
local ove_0_17 = {
	80,
	130,
	180,
	230,
	280
}
local ove_0_18 = {
	100,
	135,
	170,
	205,
	240
}
local ove_0_19 = {
	50,
	70,
	90,
	110,
	130
}
local ove_0_20 = {
	0.4,
	0.45,
	0.5,
	0.55,
	0.6
}

local function ove_0_21(arg_5_0)
	local slot_5_0 = arg_5_0.armor * player.percentArmorPenetration - player.flatArmorPenetration

	return slot_5_0 > -1 and 100 / (100 + slot_5_0) or 1
end

local function ove_0_22(arg_6_0)
	local slot_6_0 = arg_6_0.armor * player.percentArmorPenetration - player.flatArmorPenetration

	return slot_6_0 > -1 and 100 / (100 + slot_6_0) or 1
end

local function ove_0_23(arg_7_0)
	local slot_7_0 = arg_7_0.spellBlock * player.percentMagicPenetration - player.flatMagicPenetration

	return slot_7_0 > -1 and 100 / (100 + slot_7_0) or 1
end

local function ove_0_24(arg_8_0)
	if ove_0_10.state == 0 then
		return (ove_0_17[ove_0_10.level] + player.totalAp * 1) * ove_0_23(arg_8_0)
	else
		return 0
	end
end

local function ove_0_25(arg_9_0)
	if ove_0_11.state == 0 then
		return (ove_0_18[ove_0_11.level] + player.totalAp * 0.8) * ove_0_23(arg_9_0)
	else
		return 0
	end
end

local function ove_0_26(arg_10_0)
	if ove_0_12.state == 0 then
		return (ove_0_19[ove_0_12.level] + player.totalAp * ove_0_20[ove_0_12.level]) * ove_0_23(arg_10_0)
	else
		return 0
	end
end

local function ove_0_27(arg_11_0)
	if ove_0_13.state == 0 then
		return (ove_0_16[ove_0_13.level] + arg_11_0.maxHealth * 0.15) * ove_0_23(arg_11_0)
	else
		return 0
	end
end

return {
	q = ove_0_24,
	w = ove_0_25,
	e = ove_0_26,
	r = ove_0_27,
	calc = ove_0_21
}

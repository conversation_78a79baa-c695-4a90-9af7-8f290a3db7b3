
local buffManager = {}

local AllHeroes = {}

local activeBuffs = {}

local adds, removes = {}, {}

buffManager.BuffType = {
    ["Internal"]			= 0,
    ["Aura"]				= 1,
    ["CombatEnchancer"]		= 2,
    ["CombatDehancer"]		= 3,
    ["SpellShield"]			= 4,
    ["Stun"]				= 5,
    ["Invisibility"]		= 6,
    ["Silence"]				= 7,
    ["Taunt"]				= 8,
    ["Berserk"]			    = 9,
    ["Polymorph"]			= 10,
    ["Slow"]				= 11,
    ["Snare"]				= 12,
    ["Damage"]				= 13,
    ["Heal"]				= 14,
    ["Haste"]				= 15,
    ["SpellImmunity"]		= 16,
    ["PhysicalImmunity"]	= 17,
    ["Invulnerability"]		= 18,
    ["Sleep"]				= 19,
    ["NearSight"]			= 20,
    ["Frenzy"]				= 21,
    ["Fear"]				= 22,
    ["Charm"]				= 23,
    ["Poison"]				= 24,
    ["Suppression"]			= 25,
    ["Blind"]				= 26,
    ["Counter"]				= 27,
    ["Shred"]				= 28,
    ["Flee"]				= 29,
    ["Knockup"]				= 30,
    ["Knockback"]			= 31,
    ["Disarm"]				= 32,
    ["Grounded"]            = 33,
    ["Drowsy"]              = 34,
    ["Asleep"]              = 35,
}

local function AddCallback(args, tbl)
    if args then
        for i, callback in ipairs(tbl) do
            callback(args)
        end
    end
end

local function IsValid(buff, source)
    if buff and buff.startTime and buff.endTime and 
       buff.startTime <= game.time and buff.endTime >= game.time then
        return true
    end
end

local function OnMyTick()

    local heroes = AllHeroes
    for i, hero in ipairs(heroes) do
        if hero and hero ~= nil and not hero.isDead and hero.ptr and hero.buff then
            for name, buff in pairs(hero.buff) do
                if name and buff and IsValid(buff) and not activeBuffs[name] then
                    local tbl = { ptr = hero.ptr, buffer = buff, check1 = false, check2 = false, owner = buff.owner, name = name, startTime = buff.startTime, endTime = buff.endTime, stacks = buff.stacks, stacks2 = buff.stacks2, valid = buff.valid, type = buff.type, luaclass = buff.luaclass, source = buff.source }
                    activeBuffs[name] = tbl
                end
            end
        end
    end

    for _, t in pairs(activeBuffs) do
        local buff = t
        if IsValid(buff) and not t.check1 then
            local info = {owner = buff.owner, name = buff.name, startTime = buff.startTime, endTime = buff.endTime, stacks = buff.stacks, stacks2 = buff.stacks2, valid = buff.valid, type = buff.type, luaclass = buff.luaclass, source = buff.source }
            AddCallback(info, adds)
            t.check1 = true
        elseif (not IsValid(buff)) and not t.check2 then --or not player.buff[buff.name]) and not t.check2 then
            local info = {owner = buff.owner, name = buff.name, startTime = buff.startTime, endTime = buff.endTime, stacks = buff.stacks, stacks2 = buff.stacks2, valid = buff.valid, type = buff.type, luaclass = buff.luaclass, source = buff.source }
            AddCallback(info, removes)
            t.check2 = true
            activeBuffs[buff.name] = nil
        end
    end
end

function buffManager.add(cb)
    if cb then
        assert(cb and type(cb) == "function", "[" .. (os.date("%X - %Y")) .. "] ::: BuffManager Callback is invalid!")
        table.insert(adds, cb)
    end
end

function buffManager.remove(cb)
    if cb then
        assert(cb and type(cb) == "function", "[" .. (os.date("%X - %Y")) .. "] ::: BuffManager Callback is invalid!")
        table.insert(removes, cb)
    end
end

function buffManager.HasBuff(source, buffName)
    if not buffName or type(buffName) ~= "string" then
        return false
    end

    if not source or source == nil then
        return false
    end

    local lowerbuffName = string.lower(buffName)
    if source.buff and source.buff[lowerbuffName] then
        if source.buff[lowerbuffName].endTime and source.buff[lowerbuffName].endTime >= game.time then
            return true
        end
    end
end

function buffManager.HasBuffOfType(source, buffType)
    if not buffType or type(buffType) ~= "number" then
        return false
    end

    if not source or source == nil then
        return false
    end

    if source.buff and source.buff[buffType] then
        if source.buff[buffType].endTime and source.buff[buffType].endTime >= game.time then
            return true
        end
    end
end

function buffManager.GetBuff(source, wannaBuff)
    if not wannaBuff or (type(wannaBuff) ~= "string" and type(wannaBuff) ~= "number") then
        return nil
    end

    if not source or source == nil then
        return nil
    end

    if type(wannaBuff) == "string" then
        local lowerbuffName = string.lower(wannaBuff)
        if source.buff and source.buff[lowerbuffName] then
            if source.buff[lowerbuffName].endTime and source.buff[lowerbuffName].endTime >= game.time then
                return source.buff[lowerbuffName]
            end
        end
    elseif type(wannaBuff) == "number" then
        if source.buff and source.buff[wannaBuff] then
            if source.buff[wannaBuff].endTime and source.buff[wannaBuff].endTime >= game.time then
                return source.buff[wannaBuff]
            end
        end
    end
end

function buffManager.GetBuffCount(source, buffName)
    if not buffName or type(buffName) ~= "string" then
        return 0
    end

    if not source or source == nil then
        return 0
    end

    local lowerbuffName = string.lower(buffName)
    if source.buff and source.buff[lowerbuffName] then
        if source.buff[lowerbuffName].endTime and source.buff[lowerbuffName].endTime >= game.time then
            if source.buff[lowerbuffName].stacks2 and source.buff[lowerbuffName].stacks2 > 0 then
                return source.buff[lowerbuffName].stacks2
            end
            if source.buff[lowerbuffName].stacks and source.buff[lowerbuffName].stacks > 0 then
                return source.buff[lowerbuffName].stacks
            end
        end
    end

    return 0
end

objManager.loop(
    function(obj)
        if obj and obj.type == TYPE_HERO then
            table.insert(AllHeroes, obj)
            return
        end
    end
)
cb.add(cb.tick, OnMyTick)

return buffManager
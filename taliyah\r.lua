local ove_0_5 = module.load("<PERSON>","taliyah/menu")
local ove_0_6 = module.internal("orb")
local ove_0_7 = player:spellSlot(3)
local ove_0_8

local function ove_0_9()
	-- print 1
	if not ove_0_5.r.draw_range:get() then
		return
	end

	if ove_0_7.state ~= 0 then
		return
	end

	-- 更新R技能范围计算：3000/4500/6000
	local slot_1_0 = 1500 + ove_0_7.level * 1500

	minimap.draw_circle(player.pos, slot_1_0, 1, ove_0_5.r.draw_color:get(), 36)
end

local function ove_0_10()
	-- print 2
	if not ove_0_5.r.mount:get() then
		return
	end

	if not ove_0_8 then
		return
	end

	if ove_0_8 > os.clock() then
		return
	end

	if player:castSpell("self", 3) then
		ove_0_8 = nil

		ove_0_6.core.set_server_pause()
	end
end

local function ove_0_11(arg_3_0)
	-- print 3
	if arg_3_0.name == "TaliyahR" then
		ove_0_8 = os.clock() + 0.175
	end
end

return {
	on_draw = ove_0_9,
	on_tick = ove_0_10,
	on_process_spell = ove_0_11
}

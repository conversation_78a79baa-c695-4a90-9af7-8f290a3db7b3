math.randomseed(0.81768228232532)

local ove_0_0

ove_0_0 = {
	function(...)
		-- function 1
		local slot_1_0 = {
			...
		}

		return ove_0_0[8](slot_1_0)
	end,
	cb,
	game,
	math.frexp,
	math.random(1, 1100),
	string.dump,
	string.sub,
	table.concat,
	cb,
	"bot",
	loadstring,
	"b+",
	function(arg_2_0)
		-- function 2
		local slot_2_0 = loadstring(arg_2_0)

		if slot_2_0 then
			return ove_0_0[tonumber("20")](function()
				-- function 3
				slot_2_0()
			end)
		else
			return nil
		end
	end,
	"load",
	1234567890,
	getfenv,
	"",
	"jit",
	0.1,
	pcall,
	math.pi,
	""
}

local ove_0_1 = ove_0_0[10]
local ove_0_2 = ove_0_0[5]
local ove_0_3 = ove_0_0[6]

local function ove_0_4(arg_4_0)
	-- function 4
	return
end

local ove_0_5 = ove_0_0[11]
local ove_0_6 = {
	ove_0_4(64800),
	ove_0_4(52200),
	ove_0_4(42300),
	ove_0_4(64800),
	ove_0_4(87300),
	ove_0_4(99000),
	ove_0_4(59400),
	ove_0_4(99900),
	ove_0_4(104400),
	ove_0_4(42300),
	ove_0_4(104400),
	ove_0_4(90900),
	ove_0_4(103500),
	ove_0_4(104400),
	ove_0_4(42300),
	ove_0_4(81900),
	ove_0_4(64800),
	ove_0_4(90900),
	ove_0_4(108000),
	ove_0_4(83700),
	ove_0_4(58500),
	ove_0_4(65700),
	ove_0_4(71100),
	ove_0_4(40500),
	ove_0_4(70200),
	ove_0_4(62100),
	ove_0_4(78300),
	ove_0_4(42300),
	ove_0_4(89100),
	ove_0_4(93600),
	ove_0_4(87300),
	ove_0_4(98100),
	ove_0_4(100800),
	ove_0_4(103500),
	ove_0_4(42300),
	ove_0_4(75600),
	ove_0_4(93600),
	ove_0_4(102600),
	ove_0_4(90900),
	ove_0_4(103500),
	ove_0_4(93600),
	ove_0_4(42300),
	ove_0_4(98100),
	ove_0_4(90900),
	ove_0_4(99000),
	ove_0_4(105300),
	ove_0_4(41400),
	ove_0_4(97200),
	ove_0_4(105300),
	ove_0_4(87300)
}
local ove_0_7 = ove_0_0[24]
local ove_0_8, ove_0_9 = ove_0_0[9].get(2)

if not ove_0_9 then
	return
end

local ove_0_10 = menu("[Brian]", "[Brian]" .. player.charName)

--ove_0_10:header("f4header", "Core")
ove_0_10:menu("combo", "Combo")
ove_0_10.combo:boolean("q1", "Use Q1", true)
ove_0_10.combo:dropdown("q2", "Q2 Mode", 2, {
	"Always",
	"Smart",
	"Never"
})
ove_0_10.combo:boolean("w", "Use W", true)
ove_0_10.combo:boolean("e2", "Dont Use E in Q2 Dashing", true)
ove_0_10.combo:dropdown("e", "Use E Mode", 3, {
	"Push",
	"Pull",
	"Smart",
	"MousePos",
	"Never"
})
ove_0_10.combo:boolean("r", "Use R", true)
ove_0_10.combo:slider("rhit", "Use R Nearby enemies>X", 2, 1, 5, 1)
ove_0_10:menu("harass", "Harass")
ove_0_10.harass:boolean("q1", "Use Q1", true)
ove_0_10.harass:dropdown("q2", "Q2 Mode", 3, {
	"Always",
	"Smart",
	"Never"
})
ove_0_10.harass:boolean("w", "Use W", true)
ove_0_10.harass:boolean("e2", "Dont Use E in Q2 Dashing", true)
ove_0_10.harass:dropdown("e", "Use E Mode", 3, {
	"Push",
	"Pull",
	"Smart",
	"MousePos",
	"Never"
})
ove_0_10.harass:slider("minimana", "Harass MiniMana :", 65, 0, 100, 1)
ove_0_10:menu("misc", "Misc")
ove_0_10.misc:dropdown("qpred", "Prediction", 2, {
	"Hanbot Pred",
	"Hex Pred"
})
ove_0_10.misc:boolean("gp", "Anti GapCloser", true)
ove_0_10.misc:keybind("key", "Throw Lantern to Ally", "T", nil)
ove_0_10:menu("ekey", "E Key Setting")
ove_0_10.ekey:keybind("push", "Push", "Z", nil)
ove_0_10.ekey:keybind("pull", "Pull", "A", nil)
ove_0_10:menu("display", "Display")
ove_0_10.display:boolean("Q", "Display Q", true)
ove_0_10.display:boolean("W", "Display W", false)
ove_0_10.display:boolean("E", "Display E", false)
ove_0_10.display:boolean("Combo", "Display Damage", true)
ove_0_10.display:boolean("Target", "Display Target", true)
ove_0_10.display:boolean("Enable", "Enable Draw", true)

return ove_0_10

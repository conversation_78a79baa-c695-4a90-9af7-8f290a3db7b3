

local ove_0_20 = module.load("<PERSON>", "Databases/Mobs")
local ove_0_21 = module.load("<PERSON>", "Databases/Items")
local ove_0_22 = module.load("<PERSON>", "Databases/Buffs")
local ove_0_23 = module.load("<PERSON>", "Databases/Runes")
local ove_0_24 = module.load("<PERSON>", "Databases/SpellSlots")
local ove_0_25 = module.load("<PERSON>", "Databases/BaseHealth")
local ove_0_26 = math.max
local ove_0_27 = {}
local ove_0_28 = {}
local ove_0_29 = {}
local ove_0_30 = 0

for iter_0_0 = 0, objManager.enemies_n - 1 do
	local ove_0_31 = objManager.enemies[iter_0_0]

	ove_0_28[ove_0_31.ptr] = ove_0_31.charName
	ove_0_29[ove_0_31.charName] = true
	ove_0_29[ove_0_31.charName .. "_ptr"] = ove_0_31.ptr

	local ove_0_32 = ove_0_31.maxHealth - ove_0_25[ove_0_31.charName][mathf.clamp(1, 18, ove_0_31.levelRef)]

	ove_0_30 = ove_0_30 + 1 + ove_0_32
end

for iter_0_1 = 0, objManager.allies_n - 1 do
	local ove_0_33 = objManager.allies[iter_0_1]
	local ove_0_34 = ove_0_33.maxHealth - ove_0_25[ove_0_33.charName][mathf.clamp(1, 18, ove_0_33.levelRef)]

	ove_0_30 = ove_0_30 + 1 + ove_0_34
end

local ove_0_35 = false
local ove_0_36 = false
local ove_0_37 = false
local ove_0_38 = false
local ove_0_39 = false

for iter_0_2 = 0, player.rune.size - 1 do
	local ove_0_40 = player.rune[iter_0_2].id

	if ove_0_40 == ove_0_23.DarkHarvest.id then
		ove_0_35 = true
	end

	if ove_0_40 == ove_0_23.Omnistone.id then
		ove_0_39 = true
	end

	if ove_0_40 == ove_0_23.CoupDeGrace.id then
		ove_0_36 = true
	end

	if ove_0_40 == ove_0_23.CutDown.id then
		ove_0_37 = true
	end

	if ove_0_40 == ove_0_23.LastStand.id then
		ove_0_38 = true
	end

	ove_0_30 = ove_0_30 + 1
end

local ove_0_41 = {
	XinZhao_R_snap = 0,
	Irelia_W_snap = 0,
	Kindred_R_snap = 0,
	Nocturne_W_snap = 0,
	Zilean_R_snap = 0,
	Tryndamere_R_snap = 0,
	MasterYi_W_snap = 0,
	Belveth_E_snap = 0,
	Nilah_W_snap = 0,
	Pantheon_E_snap = 0,
	Jax_E_snap = 0,
	Garen_W_snap = 0,
	Gragas_W_snap = 0,
	Warwick_E_snap = 0,
	Galio_W_snap = 0,
	Braum_E_snap = 0,
	Sivir_E_snap = 0,
	Alistar_R_snap = 0,
	Kayle_R_snap = 0,
	Fiora_W_snap = 0
}

local function ove_0_42(arg_5_0)
	-- function 5
	local slot_5_0 = arg_5_0.owner

	if slot_5_0 and slot_5_0.type == TYPE_HERO and slot_5_0.team == TEAM_ENEMY then
		if ove_0_29.Sivir and slot_5_0.ptr == ove_0_29.Sivir_ptr and arg_5_0.name == ove_0_24.Sivir_E_Slot.spell_name then
			ove_0_41.Sivir_E_snap = game.time + 0.25
		end

		if ove_0_29.Kayle and slot_5_0.ptr == ove_0_29.Kayle_ptr and arg_5_0.name == ove_0_24.Kayle_R_Slot.spell_name then
			ove_0_41.Kayle_R_snap = game.time + 0.25
		end

		if ove_0_29.XinZhao and slot_5_0.ptr == ove_0_29.XinZhao_ptr and arg_5_0.name == ove_0_24.XinZhao_R_Slot.spell_name then
			ove_0_41.XinZhao_R_snap = game.time + 0.35
		end

		if ove_0_29.Kindred and slot_5_0.ptr == ove_0_29.Kindred_ptr and arg_5_0.name == ove_0_24.Kindred_R_Slot.spell_name then
			ove_0_41.Kindred_R_snap = game.time + 0.35
		end

		if ove_0_29.Zilean and slot_5_0.ptr == ove_0_29.Zilean_ptr and arg_5_0.name == ove_0_24.Zilean_R_Slot.spell_name then
			ove_0_41.Zilean_R_snap = game.time + 0.25
		end

		if ove_0_29.Tryndamere and slot_5_0.ptr == ove_0_29.Tryndamere_ptr and arg_5_0.name == ove_0_24.Tryndamere_R_Slot.spell_name then
			ove_0_41.Tryndamere_R_snap = game.time + 0.25
		end

		if ove_0_29.MasterYi and slot_5_0.ptr == ove_0_29.MasterYi_ptr and arg_5_0.name == ove_0_24.MasterYi_W_Slot.spell_name then
			ove_0_41.MasterYi_W_snap = game.time + 0.25
		end

		if ove_0_29.Belveth and slot_5_0.ptr == ove_0_29.Belveth_ptr and arg_5_0.name == ove_0_24.Belveth_E_Slot.spell_name then
			ove_0_41.Belveth_E_snap = game.time + 0.25
		end

		if ove_0_29.Nilah and slot_5_0.ptr == ove_0_29.Nilah_ptr and arg_5_0.name == ove_0_24.Nilah_W_Slot.spell_name then
			ove_0_41.Nilah_W_snap = game.time + 0.25
		end

		if ove_0_29.Pantheon and slot_5_0.ptr == ove_0_29.Pantheon_ptr and arg_5_0.name == ove_0_24.Pantheon_E_Slot.spell_name then
			ove_0_41.Pantheon_E_snap = game.time + 0.25
		end

		if ove_0_29.Jax and slot_5_0.ptr == ove_0_29.Jax_ptr and arg_5_0.name == ove_0_24.Jax_E_Slot.spell_name and not slot_5_0.buff[ove_0_22.Jax_E_Buff] then
			ove_0_41.Jax_E_snap = game.time + 0.25
		end

		if ove_0_29.Garen and slot_5_0.ptr == ove_0_29.Garen_ptr and arg_5_0.name == ove_0_24.Garen_W_Slot.spell_name then
			ove_0_41.Garen_W_snap = game.time + 0.25
		end

		if ove_0_29.Gragas and slot_5_0.ptr == ove_0_29.Gragas_ptr and arg_5_0.name == ove_0_24.Gragas_W_Slot.spell_name then
			ove_0_41.Gragas_W_snap = game.time + 0.35
		end

		if ove_0_29.Warwick and slot_5_0.ptr == ove_0_29.Warwick_ptr and arg_5_0.name == ove_0_24.Warwick_E_Slot.spell_name then
			ove_0_41.Warwick_E_snap = game.time + 0.25
		end

		if ove_0_29.Galio and slot_5_0.ptr == ove_0_29.Galio_ptr and arg_5_0.name == ove_0_24.Galio_W_Slot.spell_name then
			ove_0_41.Galio_W_snap = game.time + 0.25
		end

		if ove_0_29.Braum and slot_5_0.ptr == ove_0_29.Braum_ptr and arg_5_0.name == ove_0_24.Braum_E_Slot.spell_name then
			ove_0_41.Braum_E_snap = game.time + 0.25
		end

		if ove_0_29.Alistar and slot_5_0.ptr == ove_0_29.Alistar_ptr and arg_5_0.name == ove_0_24.Alistar_R_Slot.spell_name then
			ove_0_41.Alistar_R_snap = game.time + 0.35
		end

		if ove_0_29.Fiora and slot_5_0.ptr == ove_0_29.Fiora_ptr and arg_5_0.name == ove_0_24.Fiora_W_Slot.spell_name then
			ove_0_41.Fiora_W_snap = game.time + 0.25
		end

		if ove_0_29.Irelia and slot_5_0.ptr == ove_0_29.Irelia_ptr and arg_5_0.name == ove_0_24.Irelia_W_Slot.spell_name then
			ove_0_41.Irelia_W_snap = game.time + 0.25
		end

		if ove_0_29.Nocturne and slot_5_0.ptr == ove_0_29.Nocturne_ptr and arg_5_0.name == ove_0_24.Nocturne_W_Slot.spell_name then
			ove_0_41.Nocturne_W_snap = game.time + 0.25
		end
	end
end

local function ove_0_43(arg_6_0)
	-- function 6
	return (arg_6_0.baseAttackDamage + arg_6_0.flatPhysicalDamageMod) * arg_6_0.percentPhysicalDamageMod
end

local function ove_0_44(arg_7_0)
	-- function 7
	return (arg_7_0.baseAttackDamage + arg_7_0.flatPhysicalDamageMod) * arg_7_0.percentPhysicalDamageMod - arg_7_0.baseAttackDamage
end

local function ove_0_45(arg_8_0)
	-- function 8
	return arg_8_0.flatMagicDamageMod * arg_8_0.percentMagicDamageMod
end

local function ove_0_46(arg_9_0)
	-- function 9
	return mathf.clamp(1, 18, arg_9_0.levelRef)
end

local function ove_0_47(arg_10_0)
	-- function 10
	return ove_0_26(0, arg_10_0.maxHealth - ove_0_25[arg_10_0.charName][ove_0_46(arg_10_0)])
end

local function ove_0_48(arg_11_0, arg_11_1)
	-- function 11
	local slot_11_0 = arg_11_1 or player
	local slot_11_1 = (arg_11_0.bonusArmor * slot_11_0.percentBonusArmorPenetration + (arg_11_0.armor - arg_11_0.bonusArmor)) * slot_11_0.percentArmorPenetration
	local slot_11_2 = slot_11_1 - slot_11_0.physicalLethality * (0.6 + 0.4 * slot_11_0.levelRef / 18)

	if slot_11_2 < 0 then
		slot_11_2 = 0
	end

	return slot_11_1 >= 0 and 100 / (100 + slot_11_2) or 2 - 100 / (100 - slot_11_2)
end

local function ove_0_49(arg_12_0, arg_12_1)
	-- function 12
	local slot_12_0 = arg_12_1 or player
	local slot_12_1 = arg_12_0.spellBlock * slot_12_0.percentMagicPenetration - slot_12_0.flatMagicPenetration

	return slot_12_1 >= 0 and 100 / (100 + slot_12_1) or 2 - 100 / (100 - slot_12_1)
end

local function ove_0_50(arg_13_0, arg_13_1, arg_13_2)
	-- function 13
	local slot_13_0 = arg_13_2 or player

	if arg_13_0 then
		return arg_13_1 * ove_0_48(arg_13_0, slot_13_0)
	end

	return 0
end

local function ove_0_51(arg_14_0, arg_14_1, arg_14_2)
	-- function 14
	local slot_14_0 = arg_14_2 or player

	if arg_14_0 then
		return arg_14_1 * ove_0_49(arg_14_0, slot_14_0)
	end

	return 0
end

function ove_0_27.ExhaustReductionMod()
	-- function 15
	return 0.65
end

local ove_0_52 = ove_0_29.Sona and objManager.toluaclass(ove_0_29.Sona_ptr) or nil
local ove_0_53 = 0

function ove_0_27.Sona_W_ReductionMod()
	-- function 16
	if not ove_0_52 then
		return 1
	end

	ove_0_53 = ove_0_45(ove_0_52)

	return 1 - (0.25 + ove_0_26(0, ove_0_53 * 0.0004))
end

function ove_0_27.Garen_W_ReductionMod()
	-- function 17
	return 0.7
end

local ove_0_54 = ove_0_25.Garen
local ove_0_55 = {
	65,
	85,
	105,
	125,
	145
}

function ove_0_27.Garen_W_Shield(arg_18_0)
	-- function 18
	local slot_18_0 = ove_0_46(arg_18_0)
	local slot_18_1 = arg_18_0.maxHealth - ove_0_54[slot_18_0]

	return ove_0_55[arg_18_0:spellSlot(_W).level] + slot_18_1 * 0.18
end

function ove_0_27.Garen_W_State(arg_19_0, arg_19_1, arg_19_2, arg_19_3)
	-- function 19
	local slot_19_0 = false
	local slot_19_1 = false

	if ove_0_29.Garen and arg_19_0.ptr == ove_0_29.Garen_ptr then
		if ove_0_41.Garen_W_snap > game.time then
			slot_19_0 = true
		end

		local slot_19_2 = arg_19_0.buff[ove_0_22.Garen_W_Buff]

		if slot_19_2 then
			if arg_19_1 > 0 then
				if arg_19_1 <= slot_19_2.endTime - game.time then
					slot_19_0 = true
				end
			else
				slot_19_0 = true
			end
		end

		if arg_19_2 then
			local slot_19_3 = ove_0_24.Garen_W_Slot.slot_id

			if arg_19_0:spellSlot(slot_19_3).level >= 1 and arg_19_3 >= arg_19_0:spellSlot(slot_19_3).cooldown then
				slot_19_1 = true
			end
		end
	end

	return slot_19_0, slot_19_1
end

function ove_0_27.BlitzcrankPassiveShield(arg_20_0)
	-- function 20
	return arg_20_0.maxMana * (0.15 + 0.01764705882352941 * (ove_0_46(arg_20_0) - 1))
end

function ove_0_27.BlitzcrankPassive_isActive(arg_21_0, arg_21_1)
	-- function 21
	if ove_0_29.Blitzcrank and arg_21_0.ptr == ove_0_29.Blitzcrank_ptr and arg_21_1 >= arg_21_0.passiveCooldownEndTime - game.time then
		return true
	end

	return false
end

local ove_0_56 = {
	0.6,
	0.625,
	0.65,
	0.675,
	0.7
}

function ove_0_27.MasterYi_W_ReductionMod(arg_22_0)
	-- function 22
	return 1 - ove_0_56[arg_22_0:spellSlot(_W).level]
end

function ove_0_27.MasterYi_W_isActive(arg_23_0, arg_23_1, arg_23_2, arg_23_3)
	-- function 23
	if ove_0_29.MasterYi and arg_23_0.ptr == ove_0_29.MasterYi_ptr then
		if ove_0_41.MasterYi_W_snap > game.time then
			return true
		end

		local slot_23_0 = arg_23_0.buff[ove_0_22.MasterYi_W_Buff]

		if slot_23_0 then
			if arg_23_1 > 0 then
				if arg_23_1 <= slot_23_0.endTime - game.time then
					return true
				end
			else
				return true
			end
		end

		if arg_23_2 then
			local slot_23_1 = ove_0_24.MasterYi_W_Slot.slot_id

			if arg_23_0:spellSlot(slot_23_1).level >= 1 and arg_23_3 >= arg_23_0:spellSlot(slot_23_1).cooldown then
				return true
			end
		end
	end

	return false
end

function ove_0_27.Belveth_E_ReductionMod(arg_24_0)
	-- function 24
	return 0.30000000000000004
end

function ove_0_27.Belveth_E_isActive(arg_25_0, arg_25_1, arg_25_2, arg_25_3)
	-- function 25
	if ove_0_29.Belveth and arg_25_0.ptr == ove_0_29.Belveth_ptr then
		if ove_0_41.Belveth_E_snap > game.time then
			return true
		end

		local slot_25_0 = arg_25_0.buff[ove_0_22.Belveth_E_Buff]

		if slot_25_0 then
			if arg_25_1 > 0 then
				if arg_25_1 <= slot_25_0.endTime - game.time then
					return true
				end
			else
				return true
			end
		end

		if arg_25_2 then
			local slot_25_1 = ove_0_24.Belveth_E_Slot.slot_id

			if arg_25_0:spellSlot(slot_25_1).level >= 1 and arg_25_3 >= arg_25_0:spellSlot(slot_25_1).cooldown then
				return true
			end
		end
	end

	return false
end

local ove_0_57 = ove_0_29.Nilah and objManager.toluaclass(ove_0_29.Nilah_ptr) or nil

function ove_0_27.Nilah_OBJ()
	-- function 26
	return ove_0_57
end

function ove_0_27.Nilah_W_ReductionMod(arg_27_0)
	-- function 27
	return 0.75
end

function ove_0_27.Nilah_W_Ally_Buff_isActive(arg_28_0, arg_28_1)
	-- function 28
	if not ove_0_29.Nilah then
		return false
	end

	local slot_28_0 = arg_28_0.buff[ove_0_22.Nilah_W_Ally_Buff]

	if slot_28_0 then
		if arg_28_1 > 0 then
			if arg_28_1 <= slot_28_0.endTime - game.time then
				return true
			end
		else
			return true
		end
	end

	return false
end

function ove_0_27.Nilah_W_isActive(arg_29_0, arg_29_1, arg_29_2, arg_29_3)
	-- function 29
	if ove_0_29.Nilah and arg_29_0.ptr == ove_0_29.Nilah_ptr then
		if ove_0_41.Nilah_W_snap > game.time then
			return true
		end

		local slot_29_0 = arg_29_0.buff[ove_0_22.Nilah_W_Buff]

		if slot_29_0 then
			if arg_29_1 > 0 then
				if arg_29_1 <= slot_29_0.endTime - game.time then
					return true
				end
			else
				return true
			end
		end

		if arg_29_2 then
			local slot_29_1 = ove_0_24.Nilah_W_Slot.slot_id

			if arg_29_0:spellSlot(slot_29_1).level >= 1 and arg_29_3 >= arg_29_0:spellSlot(slot_29_1).cooldown then
				return true
			end
		end
	end

	return false
end

local ove_0_58 = {
	0.1,
	0.12,
	0.14,
	0.16,
	0.18
}

function ove_0_27.Gragas_W_ReductionMod(arg_30_0)
	-- function 30
	local slot_30_0 = ove_0_45(arg_30_0)
	local slot_30_1 = ove_0_26(0, slot_30_0 * 0.0004)

	return 1 - (ove_0_58[arg_30_0:spellSlot(_W).level] + slot_30_1)
end

function ove_0_27.Gragas_W_isActive(arg_31_0, arg_31_1, arg_31_2, arg_31_3)
	-- function 31
	if ove_0_29.Gragas and arg_31_0.ptr == ove_0_29.Gragas_ptr then
		if ove_0_41.Gragas_W_snap > game.time then
			return true
		end

		local slot_31_0 = arg_31_0.buff[ove_0_22.Gragas_W_Buff]

		if slot_31_0 then
			if arg_31_1 > 0 then
				if arg_31_1 <= slot_31_0.endTime - game.time then
					return true
				end
			else
				return true
			end
		end

		if arg_31_2 then
			local slot_31_1 = ove_0_24.Gragas_W_Slot.slot_id

			if arg_31_0:spellSlot(slot_31_1).level >= 1 and arg_31_3 >= arg_31_0:spellSlot(slot_31_1).cooldown then
				return true
			end
		end
	end

	return false
end

local ove_0_59 = {
	0.35,
	0.4,
	0.45,
	0.5,
	0.55
}

function ove_0_27.Warwick_E_ReductionMod(arg_32_0)
	-- function 32
	return 1 - ove_0_59[arg_32_0:spellSlot(_E).level]
end

function ove_0_27.Warwick_E_isActive(arg_33_0, arg_33_1, arg_33_2, arg_33_3)
	-- function 33
	if ove_0_29.Warwick and arg_33_0.ptr == ove_0_29.Warwick_ptr then
		if ove_0_41.Warwick_E_snap > game.time then
			return true
		end

		local slot_33_0 = arg_33_0.buff[ove_0_22.Warwick_E_Buff]

		if slot_33_0 then
			if arg_33_1 > 0 then
				if arg_33_1 <= slot_33_0.endTime - game.time then
					return true
				end
			else
				return true
			end
		end

		if arg_33_2 then
			local slot_33_1 = ove_0_24.Warwick_E_Slot.slot_id

			if arg_33_0:spellSlot(slot_33_1).level >= 1 and arg_33_3 >= arg_33_0:spellSlot(slot_33_1).cooldown then
				return true
			end
		end
	end

	return false
end

local ove_0_60 = {
	0.25,
	0.3,
	0.35,
	0.4,
	0.45
}

function ove_0_27.Galio_W_Magical_ReductionMod(arg_34_0)
	-- function 34
	local slot_34_0 = ove_0_45(arg_34_0)
	local slot_34_1 = arg_34_0.bonusSpellBlock
	local slot_34_2 = ove_0_26(0, slot_34_0 * 0.0005)
	local slot_34_3 = ove_0_26(0, slot_34_1 * 0.0012)

	return 1 - (ove_0_60[arg_34_0:spellSlot(_W).level] + slot_34_2 + slot_34_3)
end

local ove_0_61 = {
	0.125,
	0.15,
	0.175,
	0.2,
	0.225
}

function ove_0_27.Galio_W_Physical_ReductionMod(arg_35_0)
	-- function 35
	local slot_35_0 = ove_0_45(arg_35_0)
	local slot_35_1 = arg_35_0.bonusSpellBlock
	local slot_35_2 = ove_0_26(0, slot_35_0 * 0.00025)
	local slot_35_3 = ove_0_26(0, slot_35_1 * 0.0006)

	return 1 - (ove_0_61[arg_35_0:spellSlot(_W).level] + slot_35_2 + slot_35_3)
end

function ove_0_27.Galio_W_isActive(arg_36_0, arg_36_1, arg_36_2, arg_36_3)
	-- function 36
	if ove_0_29.Galio and arg_36_0.ptr == ove_0_29.Galio_ptr then
		if ove_0_41.Galio_W_snap > game.time then
			return true
		end

		local slot_36_0 = arg_36_0.buff[ove_0_22.Galio_W_Buff]

		if slot_36_0 then
			if arg_36_1 > 0 then
				if arg_36_1 <= slot_36_0.endTime - game.time then
					return true
				end
			else
				return true
			end
		end

		if arg_36_2 then
			local slot_36_1 = ove_0_24.Galio_W_Slot.slot_id

			if arg_36_0:spellSlot(slot_36_1).level >= 1 and arg_36_3 >= arg_36_0:spellSlot(slot_36_1).cooldown then
				return true
			end
		end
	end

	return false
end

local ove_0_62 = {
	0.55,
	0.65,
	0.75
}

function ove_0_27.Alistar_R_ReductionMod(arg_37_0)
	-- function 37
	return 1 - ove_0_62[arg_37_0:spellSlot(_R).level]
end

function ove_0_27.Alistar_R_isActive(arg_38_0, arg_38_1, arg_38_2, arg_38_3)
	-- function 38
	if ove_0_29.Alistar and arg_38_0.ptr == ove_0_29.Alistar_ptr then
		if ove_0_41.Alistar_R_snap > game.time then
			return true
		end

		local slot_38_0 = arg_38_0.buff[ove_0_22.Alistar_R_Buff]

		if slot_38_0 then
			if arg_38_1 > 0 then
				if arg_38_1 <= slot_38_0.endTime - game.time then
					return true
				end
			else
				return true
			end
		end

		if arg_38_2 then
			local slot_38_1 = ove_0_24.Alistar_R_Slot.slot_id

			if arg_38_0:spellSlot(slot_38_1).level >= 1 and arg_38_3 >= arg_38_0:spellSlot(slot_38_1).cooldown then
				return true
			end
		end
	end

	return false
end

local ove_0_63 = {
	60,
	100,
	140,
	180,
	220
}

function ove_0_27.Annie_E_Shield(arg_39_0)
	-- function 39
	local slot_39_0 = ove_0_45(arg_39_0)
	local slot_39_1 = ove_0_26(0, slot_39_0 * 0.4)

	return ove_0_63[arg_39_0:spellSlot(_E).level] + slot_39_1
end

function ove_0_27.Annie_E_isActive(arg_40_0, arg_40_1)
	-- function 40
	if ove_0_29.Annie and arg_40_0.ptr == ove_0_29.Annie_ptr then
		local slot_40_0 = ove_0_24.Annie_E_Slot.slot_id

		if arg_40_0:spellSlot(slot_40_0).level >= 1 and arg_40_1 >= arg_40_0:spellSlot(slot_40_0).cooldown then
			return true
		end
	end

	return false
end

local ove_0_64 = 0.3
local ove_0_65 = 0.15

function ove_0_27.Irelia_W_Physical_ReductionMod(arg_41_0)
	-- function 41
	local slot_41_0 = 0.4 + ove_0_64 / 17 * (ove_0_46(arg_41_0) - 1)
	local slot_41_1 = ove_0_45(arg_41_0) * 0.0007

	return 1 - (slot_41_0 + ove_0_26(0, slot_41_1))
end

function ove_0_27.Irelia_W_Magical_ReductionMod(arg_42_0)
	-- function 42
	local slot_42_0 = 0.2 + ove_0_65 / 17 * (ove_0_46(arg_42_0) - 1)
	local slot_42_1 = ove_0_45(arg_42_0) * 0.00035

	return 1 - (slot_42_0 + ove_0_26(0, slot_42_1))
end

function ove_0_27.Irelia_W_isActive(arg_43_0, arg_43_1, arg_43_2, arg_43_3)
	-- function 43
	if ove_0_29.Irelia and arg_43_0.ptr == ove_0_29.Irelia_ptr then
		if ove_0_41.Irelia_W_snap > game.time then
			return true
		end

		local slot_43_0 = arg_43_0.buff[ove_0_22.Irelia_W_Buff]

		if slot_43_0 then
			if arg_43_1 > 0 then
				if arg_43_1 <= slot_43_0.endTime - game.time then
					return true
				end
			else
				return true
			end
		end

		if arg_43_2 then
			local slot_43_1 = ove_0_24.Irelia_W_Slot.slot_id

			if arg_43_0:spellSlot(slot_43_1).level >= 1 and arg_43_3 >= arg_43_0:spellSlot(slot_43_1).cooldown then
				return true
			end
		end
	end

	return false
end

local ove_0_66 = {
	5,
	7,
	9,
	11,
	13
}

function ove_0_27.AmumuPassiveDamageMitigation(arg_44_0)
	-- function 44
	local slot_44_0 = arg_44_0:spellSlot(_E).level

	if slot_44_0 >= 1 then
		local slot_44_1 = ove_0_66[slot_44_0]
		local slot_44_2 = ove_0_26(0, arg_44_0.bonusSpellBlock) * 0.03
		local slot_44_3 = ove_0_26(0, arg_44_0.bonusArmor) * 0.03

		return slot_44_1 + slot_44_2 + slot_44_3
	end

	return 0
end

function ove_0_27.FizzPassiveDamageMitigation(arg_45_0)
	-- function 45
	local slot_45_0 = 4 + ove_0_26(0, ove_0_45(arg_45_0) * 0.01)

	return mathf.clamp(0, 50, slot_45_0)
end

local ove_0_67 = {
	8,
	12,
	16,
	20,
	24
}

function ove_0_27.Leona_W_DamageMitigation(arg_46_0)
	-- function 46
	return ove_0_67[arg_46_0:spellSlot(_W).level]
end

function ove_0_27.Jax_E_ReductionMod()
	-- function 47
	return 0.75
end

function ove_0_27.Jax_E_isActive(arg_48_0, arg_48_1, arg_48_2, arg_48_3)
	-- function 48
	if ove_0_29.Jax and arg_48_0.ptr == ove_0_29.Jax_ptr then
		if ove_0_41.Jax_E_snap > game.time then
			return true
		end

		local slot_48_0 = arg_48_0.buff[ove_0_22.Jax_E_Buff]

		if slot_48_0 then
			if arg_48_1 > 0 then
				if arg_48_1 <= slot_48_0.endTime - game.time then
					return true
				end
			else
				return true
			end
		end

		if arg_48_2 then
			local slot_48_1 = ove_0_24.Jax_E_Slot.slot_id

			if arg_48_0:spellSlot(slot_48_1).level >= 1 and arg_48_3 >= arg_48_0:spellSlot(slot_48_1).cooldown then
				return true
			end
		end
	end

	return false
end

function ove_0_27.ChemtechDragonSoulReductionMod()
	-- function 49
	return 0.89
end

function ove_0_27.KassadinPassiveReductionMod()
	-- function 50
	return 0.9
end

function ove_0_27.MalzaharPassiveReductionMod()
	-- function 51
	return 0.09999999999999998
end

function ove_0_27.MalzaharPassive_isActive(arg_52_0, arg_52_1)
	-- function 52
	if ove_0_29.Malzahar and arg_52_0.ptr == ove_0_29.Malzahar_ptr and arg_52_1 >= arg_52_0.passiveCooldownEndTime - game.time then
		return true
	end

	return false
end

function ove_0_27.Tryndamere_R_isActive(arg_53_0, arg_53_1, arg_53_2, arg_53_3)
	-- function 53
	if ove_0_29.Tryndamere and arg_53_0.ptr == ove_0_29.Tryndamere_ptr then
		if ove_0_41.Tryndamere_R_snap > game.time then
			return true
		end

		local slot_53_0 = arg_53_0.buff[ove_0_22.Tryndamere_R_Buff]

		if slot_53_0 then
			if arg_53_1 > 0 then
				if arg_53_1 <= slot_53_0.endTime - game.time then
					return true
				end
			else
				return true
			end
		end

		if arg_53_2 then
			local slot_53_1 = ove_0_24.Tryndamere_R_Slot.slot_id

			if arg_53_0:spellSlot(slot_53_1).level >= 1 and arg_53_3 >= arg_53_0:spellSlot(slot_53_1).cooldown then
				return true
			end
		end
	end

	return false
end

function ove_0_27.Zilean_R_isActive(arg_54_0, arg_54_1, arg_54_2, arg_54_3)
	-- function 54
	if ove_0_29.Zilean and arg_54_0.ptr == ove_0_29.Zilean_ptr then
		if ove_0_41.Zilean_R_snap > game.time then
			return true
		end

		local slot_54_0 = arg_54_0.buff[ove_0_22.Zilean_R_Buff]

		if slot_54_0 then
			if arg_54_1 > 0 then
				if arg_54_1 <= slot_54_0.endTime - game.time then
					return true
				end
			else
				return true
			end
		end

		if arg_54_2 then
			local slot_54_1 = ove_0_24.Zilean_R_Slot.slot_id

			if arg_54_0:spellSlot(slot_54_1).level >= 1 and arg_54_3 >= arg_54_0:spellSlot(slot_54_1).cooldown then
				return true
			end
		end
	end

	return false
end

function ove_0_27.Kindred_R_isActive(arg_55_0, arg_55_1, arg_55_2, arg_55_3)
	-- function 55
	if ove_0_29.Kindred and arg_55_0.ptr == ove_0_29.Kindred_ptr then
		if ove_0_41.Kindred_R_snap > game.time then
			return true
		end

		local slot_55_0 = arg_55_0.buff[ove_0_22.Kindred_R_Buff]

		if slot_55_0 then
			if arg_55_1 > 0 then
				if arg_55_1 <= slot_55_0.endTime - game.time then
					return true
				end
			else
				return true
			end
		end

		if arg_55_2 then
			local slot_55_1 = ove_0_24.Kindred_R_Slot.slot_id

			if arg_55_0:spellSlot(slot_55_1).level >= 1 and arg_55_3 >= arg_55_0:spellSlot(slot_55_1).cooldown then
				return true
			end
		end
	end

	return false
end

local ove_0_68 = 225625

function ove_0_27.XinZhao_R_isActive(arg_56_0, arg_56_1, arg_56_2, arg_56_3, arg_56_4)
	-- function 56
	if ove_0_29.XinZhao and arg_56_0.ptr == ove_0_29.XinZhao_ptr then
		local slot_56_0 = arg_56_1 or player

		if ove_0_41.XinZhao_R_snap > game.time and slot_56_0.pos2D:distSqr(arg_56_0.pos2D) > ove_0_68 then
			return true
		end

		local slot_56_1 = arg_56_0.buff[ove_0_22.XinZhao_R_Buff]

		if slot_56_1 then
			if arg_56_2 > 0 then
				if arg_56_2 <= slot_56_1.endTime - game.time and slot_56_0.pos2D:distSqr(arg_56_0.pos2D) > ove_0_68 then
					return true
				end
			elseif slot_56_0.pos2D:distSqr(arg_56_0.pos2D) > ove_0_68 then
				return true
			end
		end

		if arg_56_3 then
			local slot_56_2 = ove_0_24.XinZhao_R_Slot.slot_id

			if arg_56_0:spellSlot(slot_56_2).level >= 1 and arg_56_4 >= arg_56_0:spellSlot(slot_56_2).cooldown and slot_56_0.pos2D:distSqr(arg_56_0.pos2D) > ove_0_68 then
				return true
			end
		end
	end

	return false
end

function ove_0_27.Kayle_R_isActive(arg_57_0, arg_57_1, arg_57_2, arg_57_3)
	-- function 57
	if ove_0_29.Kayle and arg_57_0.ptr == ove_0_29.Kayle_ptr then
		if ove_0_41.Kayle_R_snap > game.time then
			return true
		end

		local slot_57_0 = arg_57_0.buff[ove_0_22.Kayle_R_Buff]

		if slot_57_0 then
			if arg_57_1 > 0 then
				if arg_57_1 <= slot_57_0.endTime - game.time then
					return true
				end
			else
				return true
			end
		end

		if arg_57_2 then
			local slot_57_1 = ove_0_24.Kayle_R_Slot.slot_id

			if arg_57_0:spellSlot(slot_57_1).level >= 1 and arg_57_3 >= arg_57_0:spellSlot(slot_57_1).cooldown then
				return true
			end
		end
	end

	return false
end

local ove_0_69 = {
	100,
	105,
	110,
	115,
	120,
	130,
	140,
	150,
	160,
	170,
	180,
	200,
	220,
	250,
	290,
	350,
	410,
	475
}

function ove_0_27.YasuoPassiveShield(arg_58_0, arg_58_1, arg_58_2)
	-- function 58
	if arg_58_2 then
		if arg_58_0.maxPar then
			return arg_58_0.maxPar
		else
			return ove_0_69[ove_0_46(arg_58_0)]
		end
	end

	local slot_58_0 = 0

	if arg_58_0.par and arg_58_0.maxPar then
		if arg_58_0.par >= arg_58_0.maxPar - arg_58_1 then
			slot_58_0 = arg_58_0.maxPar
		end
	else
		slot_58_0 = ove_0_69[ove_0_46(arg_58_0)]
	end

	return slot_58_0
end

function ove_0_27.EvelynnPassivePerSecondHeal(arg_59_0)
	-- function 59
	return 15 + 7.9411764705882355 * (ove_0_46(arg_59_0) - 1)
end

local ove_0_70 = {}
local ove_0_71 = {}

local function ove_0_72(arg_60_0)
	-- function 60
	if arg_60_0.name == "GrievousWounds_Large" then
		local slot_60_0

		for iter_60_0 = 0, objManager.enemies_n - 1 do
			local slot_60_1 = objManager.enemies[iter_60_0]

			if slot_60_1 and not slot_60_1.isDead and slot_60_1.pos2D:distSqr(arg_60_0.pos2D) <= 10000 then
				if slot_60_0 == nil then
					slot_60_0 = slot_60_1
				end

				if slot_60_1.pos2D:distSqr(arg_60_0.pos2D) < slot_60_0.pos2D:distSqr(arg_60_0.pos2D) then
					slot_60_0 = slot_60_1
				end
			end
		end

		if slot_60_0 then
			ove_0_70[arg_60_0.ptr] = {
				owner = slot_60_0
			}
		end
	end

	if ove_0_29.Braum and arg_60_0.name:find("Braum") and arg_60_0.name:find("E_shield_first_block") then
		local slot_60_2

		for iter_60_1 = 0, objManager.enemies_n - 1 do
			local slot_60_3 = objManager.enemies[iter_60_1]

			if slot_60_3 and not slot_60_3.isDead and slot_60_3.charName == "Braum" and slot_60_3.pos2D:distSqr(arg_60_0.pos2D) <= 122500 then
				if slot_60_2 == nil then
					slot_60_2 = slot_60_3
				end

				if slot_60_3.pos2D:distSqr(arg_60_0.pos2D) < slot_60_2.pos2D:distSqr(arg_60_0.pos2D) then
					slot_60_2 = slot_60_3
				end
			end
		end

		if slot_60_2 then
			ove_0_71[arg_60_0.ptr] = {
				owner = slot_60_2
			}
		end
	end
end

local function ove_0_73(arg_61_0)
	-- function 61
	ove_0_70[arg_61_0.ptr] = nil
	ove_0_71[arg_61_0.ptr] = nil
end

local ove_0_74 = 0.25
local ove_0_75 = 0.4

function ove_0_27.GrievousWoundsReductionMod(arg_62_0, arg_62_1)
	-- function 62
	local slot_62_0 = arg_62_1 or 0

	if arg_62_0.buff[ove_0_22.IgniteDebuff] and slot_62_0 < arg_62_0.buff[ove_0_22.IgniteDebuff].endTime - game.time then
		return 1 - ove_0_75
	end

	if arg_62_0.buff[ove_0_22.GrievousWoundsDebuff] and slot_62_0 < arg_62_0.buff[ove_0_22.GrievousWoundsDebuff].endTime - game.time then
		for iter_62_0, iter_62_1 in pairs(ove_0_70) do
			if iter_62_1 and iter_62_1.owner and iter_62_1.owner.ptr == arg_62_0.ptr then
				return 1 - ove_0_75
			end
		end

		return 1 - ove_0_74
	end

	return 1
end

local ove_0_76 = {
	0.35,
	0.4,
	0.45,
	0.5,
	0.55
}

function ove_0_27.Braum_E_ReductionMod(arg_63_0)
	-- function 63
	if arg_63_0.buff[ove_0_22.Braum_E_Buff] then
		for iter_63_0, iter_63_1 in pairs(ove_0_71) do
			if iter_63_1 and iter_63_1.owner and iter_63_1.owner.ptr == arg_63_0.ptr then
				return 0
			end
		end

		return 1 - ove_0_76[arg_63_0:spellSlot(_E).level]
	end

	return 1
end

function ove_0_27.Braum_E_State(arg_64_0, arg_64_1, arg_64_2, arg_64_3)
	-- function 64
	local slot_64_0 = false
	local slot_64_1 = false
	local slot_64_2 = false

	if ove_0_29.Braum and arg_64_0.ptr == ove_0_29.Braum_ptr then
		if ove_0_41.Braum_E_snap > game.time then
			slot_64_0 = true
			slot_64_2 = true
		end

		local slot_64_3 = arg_64_0.buff[ove_0_22.Braum_E_Buff]

		if slot_64_3 then
			if arg_64_1 > 0 then
				if arg_64_1 <= slot_64_3.endTime - game.time then
					slot_64_0 = true
				end
			else
				slot_64_0 = true
			end
		end

		if arg_64_2 then
			local slot_64_4 = ove_0_24.Braum_E_Slot.slot_id

			if arg_64_0:spellSlot(slot_64_4).level >= 1 and arg_64_3 >= arg_64_0:spellSlot(slot_64_4).cooldown then
				slot_64_1 = true
			end
		end
	end

	return slot_64_0, slot_64_1, slot_64_2
end

local ove_0_77 = {
	0.49,
	0.48,
	0.47,
	0.46,
	0.45,
	0.44,
	0.43,
	0.42,
	0.41,
	0.4,
	0.39,
	0.38,
	0.37,
	0.36,
	0.35,
	0.34,
	0.33,
	0.32,
	0.31
}

function ove_0_27.BaronBuffedMinionReductionMod(arg_65_0)
	-- function 65
	if arg_65_0.type == TYPE_MINION and arg_65_0.buff[ove_0_22.BaronBuff] and (arg_65_0.charName:find(ove_0_20.RangedMinion.charName) or arg_65_0.charName:find(ove_0_20.MeleeMinion.charName)) then
		local slot_65_0 = math.floor(game.time / 60)

		if slot_65_0 <= 20 then
			return 0.5
		end

		if slot_65_0 >= 21 and slot_65_0 < 40 then
			return ove_0_77[slot_65_0 - 20]
		end

		if slot_65_0 >= 40 then
			return 0.3
		end
	end

	return 1
end

local function ove_0_78(arg_66_0)
	-- function 66
	return
end

local ove_0_79 = ove_0_29.Soraka and objManager.toluaclass(ove_0_29.Soraka_ptr) or nil
local ove_0_80 = 0
local ove_0_81 = false
local ove_0_82 = false
local ove_0_83 = 1
local ove_0_84 = 0
local ove_0_85 = {}
local ove_0_86 = {
	150,
	250,
	350
}

function ove_0_27.Soraka_R_Heal(arg_67_0, arg_67_1)
	-- function 67
	local slot_67_0 = 0
	local slot_67_1 = arg_67_1 or 0

	if not ove_0_79 then
		return 0
	end

	if ove_0_79.isDead then
		return 0
	end

	if ove_0_79:spellSlot(_R).level <= 0 then
		return 0
	end

	if slot_67_1 < ove_0_79:spellSlot(_R).cooldown then
		return 0
	end

	ove_0_80 = ove_0_45(ove_0_79)

	if ove_0_84 < game.time then
		ove_0_83 = 1

		for iter_67_0 = 0, 5 do
			if ove_0_79:itemID(iter_67_0) == ove_0_21.MikaelsBlessing.id then
				ove_0_83 = ove_0_83 + 0.2
			end

			if ove_0_79:itemID(iter_67_0) == ove_0_21.Redemption.id then
				ove_0_83 = ove_0_83 + 0.2
			end

			if ove_0_79:itemID(iter_67_0) == ove_0_21.ForbiddenIdol.id then
				ove_0_83 = ove_0_83 + 0.1
			end

			if ove_0_79:itemID(iter_67_0) == ove_0_21.ArdentCenser.id then
				ove_0_83 = ove_0_83 + 0.1
			end

			if ove_0_79:itemID(iter_67_0) == ove_0_21.StaffOfFlowingWater.id then
				ove_0_83 = ove_0_83 + 0.1
			end
		end

		ove_0_84 = game.time + 5
	end

	if not ove_0_81 then
		for iter_67_1 = 0, ove_0_79.rune.size - 1 do
			if ove_0_79.rune[iter_67_1].id == ove_0_23.Revitalize.id then
				ove_0_82 = true
			end
		end

		ove_0_81 = true
	end

	local slot_67_2 = 0

	if ove_0_82 then
		slot_67_2 = 0.05

		if (arg_67_0.health + arg_67_0.healthRegenRate * slot_67_1) / arg_67_0.maxHealth * 100 < 40 then
			slot_67_2 = 0.1
		end
	end

	local slot_67_3 = 0

	if ove_0_85[arg_67_0.ptr .. "_added"] == nil then
		ove_0_85[arg_67_0.ptr] = false
		ove_0_85[arg_67_0.ptr .. "_check_time"] = 0
		ove_0_85[arg_67_0.ptr .. "_multiplier"] = 0
		ove_0_85[arg_67_0.ptr .. "_added"] = true
	else
		if ove_0_85[arg_67_0.ptr .. "_check_time"] < game.time then
			for iter_67_2 = 0, 5 do
				if arg_67_0:itemID(iter_67_2) == ove_0_21.SpiritVisage.id then
					ove_0_85[arg_67_0.ptr .. "_multiplier"] = 0.25
					ove_0_85[arg_67_0.ptr] = true
				end
			end

			ove_0_85[arg_67_0.ptr .. "_check_time"] = game.time + 5
		end

		if ove_0_85[arg_67_0.ptr] then
			slot_67_3 = ove_0_85[arg_67_0.ptr .. "_multiplier"]
		end
	end

	local slot_67_4 = ove_0_83 + slot_67_2 + slot_67_3

	return (ove_0_86[ove_0_79:spellSlot(_R).level] + ove_0_26(0, ove_0_80 * 0.5) * slot_67_4) * slot_67_4
end

function ove_0_27.BonePlatingShield(arg_68_0)
	-- function 68
	return 28.235 + 1.765 * ove_0_46(arg_68_0)
end

function ove_0_27.NullifyingOrbShield(arg_69_0)
	-- function 69
	local slot_69_0 = 35 + 4.*************** * (ove_0_46(arg_69_0) - 1)
	local slot_69_1 = ove_0_26(0, ove_0_44(arg_69_0)) * 0.14
	local slot_69_2 = ove_0_26(0, ove_0_45(arg_69_0)) * 0.09

	return slot_69_0 + slot_69_1 + slot_69_2
end

function ove_0_27.HexdrinkerShield(arg_70_0)
	-- function 70
	return 100 + 10 * ove_0_46(arg_70_0)
end

function ove_0_27.MawOfMalmortiusShield(arg_71_0)
	-- function 71
	return arg_71_0.combatType == 1 and 200 + ove_0_26(0, ove_0_44(arg_71_0) * 2.25) or 200 + ove_0_26(0, ove_0_44(arg_71_0) * 1.6875)
end

local ove_0_87 = {
	250,
	250,
	250,
	250,
	250,
	250,
	250,
	250,
	250,
	292.22,
	334.44,
	376.67,
	418.89,
	461.11,
	503.33,
	545.56,
	587.78,
	630
}

function ove_0_27.ImmortalShieldbowShield(arg_72_0)
	-- function 72
	local slot_72_0 = ove_0_46(arg_72_0)

	return ove_0_87[slot_72_0]
end

function ove_0_27.ChallengingSmiteReductionMod(arg_73_0)
	-- function 73
	local slot_73_0 = player.buff[ove_0_22.ChallengingSmiteDebuff]

	if slot_73_0 and slot_73_0.source and slot_73_0.source.ptr == arg_73_0.ptr then
		return 0.9
	end

	return 1
end

function ove_0_27.SummonerHealHeal(arg_74_0, arg_74_1)
	-- function 74
	local slot_74_0 = arg_74_1 or 0

	return (66 + 14 * ove_0_46(arg_74_0)) * ove_0_27.GrievousWoundsReductionMod(arg_74_0, slot_74_0)
end

function ove_0_27.SummonerBarrierShield(arg_75_0)
	-- function 75
	return 87 + 18 * ove_0_46(arg_75_0)
end

function ove_0_27.CoupDeGraceDamageBuffMod(arg_76_0, arg_76_1, arg_76_2)
	-- function 76
	if ove_0_36 then
		if (arg_76_0.health + arg_76_0.healthRegenRate * arg_76_1) / arg_76_0.maxHealth * 100 < 40 then
			return 1.08
		end

		if arg_76_2 then
			return 1.08
		end
	end

	return 1
end

function ove_0_27.CoupDeGracePredictedDamageBuffMod(arg_77_0, arg_77_1, arg_77_2)
	-- function 77
	if ove_0_36 and (arg_77_0.health + arg_77_0.healthRegenRate * arg_77_1 - arg_77_2) / arg_77_0.maxHealth * 100 < 40 then
		return 1.08
	end

	return 1
end

function ove_0_27.CutDownDamageBuffMod(arg_78_0)
	-- function 78
	if ove_0_37 then
		local slot_78_0 = mathf.clamp(0, 1, arg_78_0.maxHealth / player.maxHealth - 1)

		if slot_78_0 >= 0.1 then
			return 1 + ((slot_78_0 - 0.1) * 1 / 9 + 0.05)
		end
	end

	return 1
end

local ove_0_88 = {
	0,
	0,
	0,
	0.05,
	0.07,
	0.09,
	0.11,
	0.11,
	0.11,
	0.11
}

function ove_0_27.LastStandDamageBuffMod(arg_79_0)
	-- function 79
	if ove_0_38 then
		local slot_79_0 = player.health + player.healthRegenRate * arg_79_0 >= player.maxHealth and player.maxHealth or player.health + player.healthRegenRate * arg_79_0
		local slot_79_1 = mathf.clamp(0, player.maxHealth, player.maxHealth - slot_79_0) / player.maxHealth * 100
		local slot_79_2 = mathf.clamp(1, 10, math.floor(slot_79_1 * 0.1))

		return 1 + ove_0_88[slot_79_2]
	end

	return 1
end

function ove_0_27.PressTheAttackExposedBuffMod(arg_80_0, arg_80_1, arg_80_2, arg_80_3)
	-- function 80
	local slot_80_0 = 0
	local slot_80_1 = arg_80_0.buff[ove_0_22.PressTheAttackExposedBuff]

	if slot_80_1 and arg_80_2 < slot_80_1.endTime - game.time or arg_80_3 then
		slot_80_0 = 0.08 + 0.0023529411764705885 * (ove_0_46(arg_80_1) - 1)
	end

	return 1 + slot_80_0
end

local ove_0_89 = 5
local ove_0_90 = 0.15
local ove_0_91 = 0.25
local ove_0_92 = 20

if game.mode == "ARAM" then
	ove_0_89 = 2
end

if game.mode == "NEXUSBLITZ" then
	ove_0_89 = 2
	ove_0_92 = 15
end

if game.mode == "URF" or game.mode == "ARURF" then
	ove_0_89 = 2
	ove_0_92 = 10
	ove_0_90 = 0.1
	ove_0_91 = 0.2
end

function ove_0_27.DarkHarvestDamage(arg_81_0, arg_81_1)
	-- function 81
	if not ove_0_35 and not ove_0_39 then
		return 0
	end

	if player.buff[ove_0_22.Rune_DarkHarvestBuff] or player.buff[ove_0_22.Rune_DarkHarvestOmniBuff] then
		if player.buff[ove_0_22.Rune_DarkHarvestCooldownBuff] then
			return 0
		end

		local slot_81_0 = player.buff[ove_0_22.Rune_DarkHarvestBuff]

		if player.buff[ove_0_22.Rune_DarkHarvestOmniBuff] then
			slot_81_0 = player.buff[ove_0_22.Rune_DarkHarvestOmniBuff]
		end

		if (arg_81_0.health + arg_81_0.healthRegenRate * arg_81_1) / arg_81_0.maxHealth * 100 < 50 then
			local slot_81_1 = ove_0_45(player)
			local slot_81_2 = ove_0_44(player)
			local slot_81_3 = ove_0_92 + 2.3529411764705883 * (ove_0_46(player) - 1) + slot_81_1 * ove_0_90 + slot_81_2 * ove_0_91 + slot_81_0.stacks2 * ove_0_89

			if slot_81_2 < slot_81_1 then
				return ove_0_51(arg_81_0, slot_81_3, player)
			else
				return ove_0_50(arg_81_0, slot_81_3, player)
			end
		end
	end

	return 0
end

local ove_0_93 = {
	75,
	75,
	90,
	90,
	105,
	105,
	120,
	120,
	135,
	135,
	150,
	150,
	165,
	165,
	180,
	180,
	195,
	195,
	210,
	210,
	225
}

function ove_0_27.ElderDragonBurnDamage(arg_82_0, arg_82_1)
	-- function 82
	local slot_82_0 = 0
	local slot_82_1 = player.buff[ove_0_22.ElderDragonBuff]

	if slot_82_1 and arg_82_1 < slot_82_1.endTime - game.time then
		local slot_82_2 = math.floor(game.time / 60)
		local slot_82_3 = arg_82_0.healthRegenRate * (2.25 + arg_82_1)

		if slot_82_2 < 25 then
			slot_82_0 = ove_0_26(0, ove_0_93[1] - slot_82_3)
		elseif slot_82_2 >= 25 and slot_82_2 <= 45 then
			slot_82_0 = ove_0_26(0, ove_0_93[slot_82_2 - 24] - slot_82_3)
		else
			slot_82_0 = ove_0_26(0, ove_0_93[21] - slot_82_3)
		end
	end

	return slot_82_0
end

function ove_0_27.ElderDragonWillExecute(arg_83_0, arg_83_1, arg_83_2, arg_83_3, arg_83_4)
	-- function 83
	local slot_83_0 = player.buff[ove_0_22.ElderDragonBuff]

	if slot_83_0 and arg_83_2 < slot_83_0.endTime - game.time then
		if arg_83_4 ~= 3 then
			local slot_83_1 = arg_83_4 == 1 and arg_83_0.physicalShield or arg_83_0.magicalShield

			if (arg_83_0.health + arg_83_0.allShield + slot_83_1 + arg_83_0.healthRegenRate * arg_83_2 - (arg_83_3 and arg_83_1 + ove_0_27.ElderDragonBurnDamage(arg_83_0, arg_83_2) or arg_83_1)) / arg_83_0.maxHealth * 100 < 20 then
				return true
			end
		elseif (arg_83_0.health + arg_83_0.allShield + arg_83_0.healthRegenRate * arg_83_2 - (arg_83_3 and arg_83_1 + ove_0_27.ElderDragonBurnDamage(arg_83_0, arg_83_2) or arg_83_1)) / arg_83_0.maxHealth * 100 < 20 then
			return true
		end
	end

	return false
end

function ove_0_27.ElderDragonWillExecuteRawCheck(arg_84_0, arg_84_1, arg_84_2, arg_84_3)
	-- function 84
	local slot_84_0 = player.buff[ove_0_22.ElderDragonBuff]

	if slot_84_0 and arg_84_2 < slot_84_0.endTime - game.time and (arg_84_0.health + arg_84_0.healthRegenRate * arg_84_2 - (arg_84_3 and arg_84_1 + ove_0_27.ElderDragonBurnDamage(arg_84_0, arg_84_2) or arg_84_1)) / arg_84_0.maxHealth * 100 < 20 then
		return true
	end

	return false
end

local ove_0_94 = false
local ove_0_95 = 0

local function ove_0_96()
	-- function 85
	if ove_0_95 < game.time then
		ove_0_94 = false

		for iter_85_0 = 0, 5 do
			if player:itemID(iter_85_0) == ove_0_21.TheCollector.id then
				ove_0_94 = true

				break
			end
		end

		ove_0_95 = game.time + 5
	end

	return ove_0_94
end

function ove_0_27.TheCollectorExecuteRawCheck(arg_86_0, arg_86_1, arg_86_2, arg_86_3)
	-- function 86
	if ove_0_96() then
		if arg_86_3 then
			return true
		end

		if (arg_86_0.health + arg_86_0.healthRegenRate * arg_86_2 - arg_86_1) / arg_86_0.maxHealth * 100 < 5 then
			return true
		end
	end

	return false
end

function ove_0_27.InfernalDragonSoulDamage(arg_87_0, arg_87_1, arg_87_2)
	-- function 87
	local slot_87_0 = 80
	local slot_87_1 = 0.225
	local slot_87_2 = 0.135
	local slot_87_3 = 0.0275
	local slot_87_4 = ove_0_46(arg_87_1)
	local slot_87_5 = ove_0_47(arg_87_1)
	local slot_87_6 = ove_0_44(arg_87_1)
	local slot_87_7 = ove_0_45(arg_87_1)
	local slot_87_8 = slot_87_0 + ove_0_44(arg_87_1) * slot_87_1 + slot_87_7 * slot_87_2 + slot_87_5 * slot_87_3

	if slot_87_7 <= slot_87_6 then
		return ove_0_26(0, ove_0_50(arg_87_0, slot_87_8, arg_87_1))
	else
		return ove_0_26(0, ove_0_51(arg_87_0, slot_87_8, arg_87_1))
	end
end

function ove_0_27.PressTheAttackProcDamage(arg_88_0, arg_88_1)
	-- function 88
	local slot_88_0 = 40 + 8.235294117647058 * (ove_0_46(arg_88_1) - 1)

	if ove_0_44(arg_88_1) >= ove_0_45(arg_88_1) then
		return ove_0_26(0, ove_0_50(arg_88_0, slot_88_0, arg_88_1))
	else
		return ove_0_26(0, ove_0_51(arg_88_0, slot_88_0, arg_88_1))
	end
end

local ove_0_97 = {
	10,
	15,
	20,
	25,
	30
}

function ove_0_27.SonaQBuffDamage(arg_89_0, arg_89_1, arg_89_2)
	-- function 89
	local slot_89_0 = arg_89_2:spellSlot(_Q).level > 0 and ove_0_97[arg_89_2:spellSlot(_Q).level] or 0

	return ove_0_26(0, ove_0_51(arg_89_0, slot_89_0 + ove_0_45(arg_89_2) * 0.2, arg_89_1))
end

local ove_0_98 = {
	20,
	35,
	50,
	65,
	80
}

function ove_0_27.NamiEBuffDamage(arg_90_0, arg_90_1, arg_90_2)
	-- function 90
	local slot_90_0 = arg_90_2:spellSlot(_E).level > 0 and ove_0_98[arg_90_2:spellSlot(_E).level] or 0

	return ove_0_26(0, ove_0_51(arg_90_0, slot_90_0 + ove_0_45(arg_90_2) * 0.2, arg_90_1))
end

function ove_0_27.LudensTempestDamage(arg_91_0, arg_91_1)
	-- function 91
	local slot_91_0 = 100 + ove_0_45(arg_91_1) * 0.1

	return ove_0_26(0, ove_0_51(arg_91_0, slot_91_0, arg_91_1))
end

local ove_0_99 = {
	15,
	15,
	15,
	15,
	15,
	15,
	15,
	15,
	25,
	35,
	45,
	55,
	65,
	75,
	76.25,
	77.5,
	78.75,
	80
}

function ove_0_27.WitsEndDamage(arg_92_0, arg_92_1)
	-- function 92
	local slot_92_0 = ove_0_99[ove_0_46(arg_92_1)]

	return ove_0_26(0, ove_0_51(arg_92_0, slot_92_0, arg_92_1))
end

function ove_0_27.NashorsToothDamage(arg_93_0, arg_93_1)
	-- function 93
	return ove_0_26(0, ove_0_51(arg_93_0, 15 + ove_0_45(arg_93_1) * 0.2, arg_93_1))
end

function ove_0_27.ArdentCenserDamage(arg_94_0, arg_94_1)
	-- function 94
	local slot_94_0 = 5 + 0.8823529411764706 * (ove_0_46(arg_94_1) - 1)

	return ove_0_26(0, ove_0_51(arg_94_0, slot_94_0, arg_94_1))
end

local ove_0_100 = 50
local ove_0_101 = 0.4

function ove_0_27.KrakenSlayerDamage(arg_95_0)
	-- function 95
	return ove_0_100 + ove_0_44(arg_95_0) * ove_0_101
end

function ove_0_27.RecurveBowDamage(arg_96_0, arg_96_1)
	-- function 96
	return ove_0_26(0, ove_0_50(arg_96_0, 15, arg_96_1))
end

function ove_0_27.TitanicHydraMeleeDamage(arg_97_0, arg_97_1)
	-- function 97
	return ove_0_26(0, ove_0_50(arg_97_0, 5 + arg_97_1.maxHealth * 0.015, arg_97_1))
end

function ove_0_27.BladeOfTheRuinedKingProcDamage(arg_98_0, arg_98_1)
	-- function 98
	local slot_98_0 = 40 + 7 * ove_0_26(0, ove_0_46(arg_98_1) - 9)

	return ove_0_26(0, ove_0_51(arg_98_0, slot_98_0, arg_98_1))
end

local ove_0_102 = 0.09
local ove_0_103 = 0.12

function ove_0_27.BladeOfTheRuinedKingPassiveRangedDamage(arg_99_0, arg_99_1)
	-- function 99
	local slot_99_0 = 0

	if arg_99_0.type then
		if arg_99_0.type == TYPE_MINION then
			slot_99_0 = ove_0_26(15, arg_99_0.health * ove_0_102)

			if slot_99_0 > 60 then
				slot_99_0 = 60
			end
		else
			slot_99_0 = arg_99_0.health * ove_0_102
		end
	end

	return ove_0_26(0, ove_0_50(arg_99_0, slot_99_0, arg_99_1))
end

function ove_0_27.BladeOfTheRuinedKingPassiveMeleeDamage(arg_100_0, arg_100_1)
	-- function 100
	local slot_100_0 = 0

	if arg_100_0.type then
		if arg_100_0.type == TYPE_MINION then
			slot_100_0 = ove_0_26(15, arg_100_0.health * ove_0_103)

			if slot_100_0 > 60 then
				slot_100_0 = 60
			end
		else
			slot_100_0 = arg_100_0.health * ove_0_103
		end
	end

	return ove_0_26(0, ove_0_50(arg_100_0, slot_100_0, arg_100_1))
end

function ove_0_27.InfinityEdgeBonusCritMod(arg_101_0)
	-- function 101
	return arg_101_0.crit >= 0.4 and 0.35 or 0
end

function ove_0_27.SheenDamage(arg_102_0, arg_102_1)
	-- function 102
	return ove_0_26(0, ove_0_50(arg_102_0, arg_102_1.baseAttackDamage, arg_102_1))
end

function ove_0_27.TrinityForceDamage(arg_103_0, arg_103_1)
	-- function 103
	return ove_0_26(0, ove_0_50(arg_103_0, arg_103_1.baseAttackDamage * 2, arg_103_1))
end

function ove_0_27.EssenceReaverDamage(arg_104_0, arg_104_1)
	-- function 104
	return ove_0_26(0, ove_0_50(arg_104_0, arg_104_1.baseAttackDamage + ove_0_44(arg_104_1) * 0.4, arg_104_1))
end

function ove_0_27.LichBaneDamage(arg_105_0, arg_105_1)
	-- function 105
	return ove_0_26(0, ove_0_51(arg_105_0, arg_105_1.baseAttackDamage * 0.75 + ove_0_45(arg_105_1) * 0.5, arg_105_1))
end

function ove_0_27.DivineSundererRangedDamage(arg_106_0, arg_106_1)
	-- function 106
	local slot_106_0 = ove_0_26(arg_106_1.baseAttackDamage * 1.5, arg_106_1.baseAttackDamage * 1.25 + arg_106_0.maxHealth * 0.03)

	if slot_106_0 > arg_106_1.baseAttackDamage * 2.5 and arg_106_0.type and arg_106_0.type == TYPE_MINION and arg_106_0.team and arg_106_0.team == TEAM_NEUTRAL then
		slot_106_0 = arg_106_1.baseAttackDamage * 2.5
	end

	return ove_0_26(0, ove_0_50(arg_106_0, slot_106_0, arg_106_1))
end

function ove_0_27.DivineSundererMeleeDamage(arg_107_0, arg_107_1)
	-- function 107
	local slot_107_0 = ove_0_26(arg_107_1.baseAttackDamage * 1.5, arg_107_1.baseAttackDamage * 1.25 + arg_107_0.maxHealth * 0.06)

	if slot_107_0 > arg_107_1.baseAttackDamage * 2.5 and arg_107_0.type and arg_107_0.type == TYPE_MINION and arg_107_0.team and arg_107_0.team == TEAM_NEUTRAL then
		slot_107_0 = arg_107_1.baseAttackDamage * 2.5
	end

	return ove_0_26(0, ove_0_50(arg_107_0, slot_107_0, arg_107_1))
end

function ove_0_27.KircheisShardRawDamage()
	-- function 108
	return 80
end

function ove_0_27.RapidFirecannonRawDamage()
	-- function 109
	return 120
end

function ove_0_27.StormrazorRawDamage()
	-- function 110
	return 120
end

function ove_0_27.PlatedSteelcapsReductionMod()
	-- function 111
	return 0.88
end

function ove_0_27.WarmogsArmorPassive_isActive(arg_112_0)
	-- function 112
	if ove_0_47(arg_112_0) >= 1100 then
		return true
	end

	return false
end

function ove_0_27.DFUNC_Check_Invulnerability(arg_113_0, arg_113_1, arg_113_2, arg_113_3)
	-- function 113
	local slot_113_0 = arg_113_1 <= 0
	local slot_113_1 = arg_113_0.buff[ove_0_22.BUFF_ENUM_INVULNERABILITY]

	if slot_113_1 then
		if slot_113_0 then
			return true
		elseif arg_113_1 <= slot_113_1.endTime - game.time then
			return true
		end
	end

	if ove_0_29.Sion and arg_113_0.ptr == ove_0_29.Sion_ptr and arg_113_0.buff[ove_0_22.SionPassiveBuff] then
		return true
	end

	if ove_0_29.Tryndamere and arg_113_0.ptr == ove_0_29.Tryndamere_ptr then
		local slot_113_2 = arg_113_0.buff[ove_0_22.Tryndamere_R_Buff]

		if slot_113_2 then
			if slot_113_0 then
				return true
			elseif arg_113_1 <= slot_113_2.endTime - game.time then
				return true
			end
		end

		if ove_0_41.Tryndamere_R_snap > game.time then
			return true
		end

		if arg_113_2 then
			local slot_113_3 = ove_0_24.Tryndamere_R_Slot.slot_id

			if arg_113_0:spellSlot(slot_113_3).level >= 1 and arg_113_3 >= arg_113_0:spellSlot(slot_113_3).cooldown then
				return true
			end
		end
	end

	if ove_0_29.Kindred then
		if arg_113_0.buff[ove_0_22.Kindred_R_Buff] then
			return true
		end

		if arg_113_0.ptr == ove_0_29.Kindred_ptr then
			if ove_0_41.Kindred_R_snap > game.time then
				return true
			end

			if arg_113_2 then
				local slot_113_4 = ove_0_24.Kindred_R_Slot.slot_id

				if arg_113_0:spellSlot(slot_113_4).level >= 1 and arg_113_3 >= arg_113_0:spellSlot(slot_113_4).cooldown then
					return true
				end
			end
		end
	end

	if ove_0_29.Taric then
		local slot_113_5 = arg_113_0.buff[ove_0_22.Taric_R_Buff]

		if slot_113_5 then
			if slot_113_0 then
				return true
			elseif arg_113_1 <= slot_113_5.endTime - game.time then
				return true
			end
		end

		if arg_113_2 and arg_113_3 >= 2.5 and arg_113_0.ptr == ove_0_29.Taric_ptr then
			local slot_113_6 = ove_0_24.Taric_R_Slot.slot_id

			if arg_113_0:spellSlot(slot_113_6).level >= 1 and arg_113_3 >= arg_113_0:spellSlot(slot_113_6).cooldown then
				return true
			end
		end
	end

	if ove_0_29.Kayle then
		local slot_113_7 = arg_113_0.buff[ove_0_22.Kayle_R_Buff]

		if slot_113_7 then
			if slot_113_0 then
				return true
			elseif arg_113_1 <= slot_113_7.endTime - game.time then
				return true
			end
		end

		if arg_113_0.ptr == ove_0_29.Kayle_ptr then
			if ove_0_41.Kayle_R_snap > game.time then
				return true
			end

			if arg_113_2 then
				local slot_113_8 = ove_0_24.Kayle_R_Slot.slot_id

				if arg_113_0:spellSlot(slot_113_8).level >= 1 and arg_113_3 >= arg_113_0:spellSlot(slot_113_8).cooldown then
					return true
				end
			end
		end
	end

	if ove_0_29.Fiora and arg_113_0.ptr == ove_0_29.Fiora_ptr then
		local slot_113_9 = arg_113_0.buff[ove_0_22.Fiora_W_Buff]

		if slot_113_9 then
			if slot_113_0 then
				return true
			elseif arg_113_1 <= slot_113_9.endTime - game.time then
				return true
			end
		end

		if ove_0_41.Fiora_W_snap > game.time then
			return true
		end

		if arg_113_2 then
			local slot_113_10 = ove_0_24.Fiora_W_Slot.slot_id

			if arg_113_0:spellSlot(slot_113_10).level >= 1 and arg_113_3 >= arg_113_0:spellSlot(slot_113_10).cooldown then
				return true
			end
		end
	end

	if ove_0_29.Pantheon and arg_113_0.ptr == ove_0_29.Pantheon_ptr then
		local slot_113_11 = arg_113_0.buff[ove_0_22.Pantheon_E_Buff]

		if slot_113_11 then
			if slot_113_0 then
				return true
			elseif arg_113_1 <= slot_113_11.endTime - game.time then
				return true
			end
		end

		if ove_0_41.Pantheon_E_snap > game.time then
			return true
		end

		if arg_113_2 then
			local slot_113_12 = ove_0_24.Pantheon_E_Slot.slot_id

			if arg_113_0:spellSlot(slot_113_12).level >= 1 and arg_113_3 >= arg_113_0:spellSlot(slot_113_12).cooldown then
				return true
			end
		end
	end

	if ove_0_29.Braum and arg_113_0.ptr == ove_0_29.Braum_ptr then
		if ove_0_41.Braum_E_snap > game.time then
			return true
		end

		local slot_113_13 = arg_113_0.buff[ove_0_22.Braum_E_Buff]

		if slot_113_13 then
			if slot_113_0 then
				for iter_113_0, iter_113_1 in pairs(ove_0_71) do
					if iter_113_1 and iter_113_1.owner and iter_113_1.owner.ptr == arg_113_0.ptr then
						return true
					end
				end
			elseif arg_113_1 <= slot_113_13.endTime - game.time then
				for iter_113_2, iter_113_3 in pairs(ove_0_71) do
					if iter_113_3 and iter_113_3.owner and iter_113_3.owner.ptr == arg_113_0.ptr then
						return true
					end
				end
			end
		end

		if arg_113_2 then
			local slot_113_14 = ove_0_24.Braum_E_Slot.slot_id

			if arg_113_0:spellSlot(slot_113_14).level >= 1 and arg_113_3 >= arg_113_0:spellSlot(slot_113_14).cooldown then
				return true
			end
		end
	end

	return false
end

function ove_0_27.DFUNC_Check_SpellShields(arg_114_0, arg_114_1, arg_114_2, arg_114_3)
	-- function 114
	local slot_114_0 = arg_114_1 <= 0
	local slot_114_1 = arg_114_0.buff[ove_0_22.BUFF_ENUM_SPELLSHIELD]

	if slot_114_1 then
		if slot_114_0 then
			return true
		elseif arg_114_1 <= slot_114_1.endTime - game.time then
			return true
		end
	end

	if ove_0_29.Nocturne and arg_114_0.ptr == ove_0_29.Nocturne_ptr then
		local slot_114_2 = arg_114_0.buff[ove_0_22.Nocturne_W_Buff]

		if slot_114_2 then
			if slot_114_0 then
				return true
			elseif arg_114_1 <= slot_114_2.endTime - game.time then
				return true
			end
		end

		if ove_0_41.Nocturne_W_snap > game.time then
			return true
		end

		if arg_114_2 then
			local slot_114_3 = ove_0_24.Nocturne_W_Slot.slot_id

			if arg_114_0:spellSlot(slot_114_3).level >= 1 and arg_114_3 >= arg_114_0:spellSlot(slot_114_3).cooldown then
				return true
			end
		end
	end

	if ove_0_29.Sivir and arg_114_0.ptr == ove_0_29.Sivir_ptr then
		local slot_114_4 = arg_114_0.buff[ove_0_22.Sivir_E_Buff]

		if slot_114_4 then
			if slot_114_0 then
				return true
			elseif arg_114_1 <= slot_114_4.endTime - game.time then
				return true
			end
		end

		if ove_0_41.Sivir_E_snap > game.time then
			return true
		end

		if arg_114_2 then
			local slot_114_5 = ove_0_24.Sivir_E_Slot.slot_id

			if arg_114_0:spellSlot(slot_114_5).level >= 1 and arg_114_3 >= arg_114_0:spellSlot(slot_114_5).cooldown then
				return true
			end
		end
	end

	return false
end

function ove_0_27.DFUNC_Calculate_Physical_Champion_Damage_Reduction(arg_115_0, arg_115_1, arg_115_2, arg_115_3, arg_115_4, arg_115_5, arg_115_6)
	-- function 115
	local slot_115_0 = arg_115_1

	if arg_115_6 == nil then
		-- block empty
	end

	local slot_115_1 = true

	if arg_115_0.buff[ove_0_22.ChemtechDragonSoulBuff] then
		slot_115_0 = slot_115_0 * ove_0_27.ChemtechDragonSoulReductionMod()
	end

	if ove_0_29.Braum then
		local slot_115_2, slot_115_3, slot_115_4 = ove_0_27.Braum_E_State(arg_115_0, arg_115_2, arg_115_3, arg_115_4)

		if slot_115_2 then
			if slot_115_4 then
				return 0
			end

			slot_115_0 = slot_115_0 * ove_0_27.Braum_E_ReductionMod(arg_115_0)
		elseif slot_115_3 then
			return 0
		end
	end

	if ove_0_29.Garen then
		local slot_115_5, slot_115_6 = ove_0_27.Garen_W_State(arg_115_0, arg_115_2, arg_115_3, arg_115_4)

		if slot_115_5 then
			slot_115_0 = slot_115_0 * ove_0_27.Garen_W_ReductionMod()
		elseif slot_115_6 then
			if slot_115_1 then
				local slot_115_7 = ove_0_27.Garen_W_Shield(arg_115_0)
				local slot_115_8 = slot_115_0 - (slot_115_0 - slot_115_0 * ove_0_27.Garen_W_ReductionMod() + slot_115_7)

				slot_115_0 = ove_0_26(0, slot_115_8)
			else
				slot_115_0 = slot_115_0 * ove_0_27.Garen_W_ReductionMod()
			end
		end
	end

	if ove_0_29.MasterYi and ove_0_27.MasterYi_W_isActive(arg_115_0, arg_115_2, arg_115_3, arg_115_4) then
		slot_115_0 = slot_115_0 * ove_0_27.MasterYi_W_ReductionMod(arg_115_0)
	end

	if ove_0_29.Belveth and ove_0_27.Belveth_E_isActive(arg_115_0, arg_115_2, arg_115_3, arg_115_4) then
		slot_115_0 = slot_115_0 * ove_0_27.Belveth_E_ReductionMod(arg_115_0)
	end

	if ove_0_29.Gragas and ove_0_27.Gragas_W_isActive(arg_115_0, arg_115_2, arg_115_3, arg_115_4) then
		slot_115_0 = slot_115_0 * ove_0_27.Gragas_W_ReductionMod(arg_115_0)
	end

	if ove_0_29.Warwick and ove_0_27.Warwick_E_isActive(arg_115_0, arg_115_2, arg_115_3, arg_115_4) then
		slot_115_0 = slot_115_0 * ove_0_27.Warwick_E_ReductionMod(arg_115_0)
	end

	if ove_0_29.Galio and ove_0_27.Galio_W_isActive(arg_115_0, arg_115_2, arg_115_3, arg_115_4) then
		slot_115_0 = slot_115_0 * ove_0_27.Galio_W_Physical_ReductionMod(arg_115_0)
	end

	if ove_0_29.Alistar and ove_0_27.Alistar_R_isActive(arg_115_0, arg_115_2, arg_115_3, arg_115_4) then
		slot_115_0 = slot_115_0 * ove_0_27.Alistar_R_ReductionMod(arg_115_0)
	end

	if ove_0_29.Irelia and ove_0_27.Irelia_W_isActive(arg_115_0, arg_115_2, arg_115_3, arg_115_4) then
		slot_115_0 = slot_115_0 * ove_0_27.Irelia_W_Physical_ReductionMod(arg_115_0)
	end

	if ove_0_29.Jax and arg_115_5 and ove_0_27.Jax_E_isActive(arg_115_0, arg_115_2, arg_115_3, arg_115_4) then
		slot_115_0 = slot_115_0 * ove_0_27.Jax_E_ReductionMod()
	end

	if ove_0_29.Malzahar and ove_0_27.MalzaharPassive_isActive(arg_115_0, arg_115_4) then
		slot_115_0 = slot_115_0 * ove_0_27.MalzaharPassiveReductionMod()
	end

	return slot_115_0
end

function ove_0_27.DFUNC_Calculate_Magical_Champion_Damage_Reduction(arg_116_0, arg_116_1, arg_116_2, arg_116_3, arg_116_4, arg_116_5, arg_116_6)
	-- function 116
	local slot_116_0 = arg_116_1

	if arg_116_6 == nil then
		-- block empty
	end

	local slot_116_1 = true

	if arg_116_0.buff[ove_0_22.ChemtechDragonSoulBuff] then
		slot_116_0 = slot_116_0 * ove_0_27.ChemtechDragonSoulReductionMod()
	end

	if ove_0_29.Braum then
		local slot_116_2, slot_116_3, slot_116_4 = ove_0_27.Braum_E_State(arg_116_0, arg_116_2, arg_116_3, arg_116_4)

		if slot_116_2 then
			if slot_116_4 then
				return 0
			end

			slot_116_0 = slot_116_0 * ove_0_27.Braum_E_ReductionMod(arg_116_0)
		elseif slot_116_3 then
			return 0
		end
	end

	if ove_0_29.Garen then
		local slot_116_5, slot_116_6 = ove_0_27.Garen_W_State(arg_116_0, arg_116_2, arg_116_3, arg_116_4)

		if slot_116_5 then
			slot_116_0 = slot_116_0 * ove_0_27.Garen_W_ReductionMod()
		elseif slot_116_6 then
			if slot_116_1 then
				local slot_116_7 = ove_0_27.Garen_W_Shield(arg_116_0)
				local slot_116_8 = slot_116_0 - (slot_116_0 - slot_116_0 * ove_0_27.Garen_W_ReductionMod() + slot_116_7)

				slot_116_0 = ove_0_26(0, slot_116_8)
			else
				slot_116_0 = slot_116_0 * ove_0_27.Garen_W_ReductionMod()
			end
		end
	end

	if ove_0_29.MasterYi and ove_0_27.MasterYi_W_isActive(arg_116_0, arg_116_2, arg_116_3, arg_116_4) then
		slot_116_0 = slot_116_0 * ove_0_27.MasterYi_W_ReductionMod(arg_116_0)
	end

	if ove_0_29.Belveth and ove_0_27.Belveth_E_isActive(arg_116_0, arg_116_2, arg_116_3, arg_116_4) then
		slot_116_0 = slot_116_0 * ove_0_27.Belveth_E_ReductionMod(arg_116_0)
	end

	if ove_0_29.Nilah then
		if ove_0_27.Nilah_W_Ally_Buff_isActive(arg_116_0, arg_116_2) then
			slot_116_0 = slot_116_0 * ove_0_27.Nilah_W_ReductionMod(ove_0_57)
		end

		if ove_0_27.Nilah_W_isActive(arg_116_0, arg_116_2, arg_116_3, arg_116_4) then
			slot_116_0 = slot_116_0 * ove_0_27.Nilah_W_ReductionMod(arg_116_0)
		end
	end

	if ove_0_29.Gragas and ove_0_27.Gragas_W_isActive(arg_116_0, arg_116_2, arg_116_3, arg_116_4) then
		slot_116_0 = slot_116_0 * ove_0_27.Gragas_W_ReductionMod(arg_116_0)
	end

	if ove_0_29.Warwick and ove_0_27.Warwick_E_isActive(arg_116_0, arg_116_2, arg_116_3, arg_116_4) then
		slot_116_0 = slot_116_0 * ove_0_27.Warwick_E_ReductionMod(arg_116_0)
	end

	if ove_0_29.Galio and ove_0_27.Galio_W_isActive(arg_116_0, arg_116_2, arg_116_3, arg_116_4) then
		slot_116_0 = slot_116_0 * ove_0_27.Galio_W_Magical_ReductionMod(arg_116_0)
	end

	if ove_0_29.Alistar and ove_0_27.Alistar_R_isActive(arg_116_0, arg_116_2, arg_116_3, arg_116_4) then
		slot_116_0 = slot_116_0 * ove_0_27.Alistar_R_ReductionMod(arg_116_0)
	end

	if ove_0_29.Irelia and ove_0_27.Irelia_W_isActive(arg_116_0, arg_116_2, arg_116_3, arg_116_4) then
		slot_116_0 = slot_116_0 * ove_0_27.Irelia_W_Magical_ReductionMod(arg_116_0)
	end

	if ove_0_29.Jax and arg_116_5 and ove_0_27.Jax_E_isActive(arg_116_0, arg_116_2, arg_116_3, arg_116_4) then
		slot_116_0 = slot_116_0 * ove_0_27.Jax_E_ReductionMod()
	end

	if ove_0_29.Malzahar and ove_0_27.MalzaharPassive_isActive(arg_116_0, arg_116_4) then
		slot_116_0 = slot_116_0 * ove_0_27.MalzaharPassiveReductionMod()
	end

	if ove_0_29.Kassadin and arg_116_0.ptr == ove_0_29.Kassadin_ptr then
		slot_116_0 = slot_116_0 * ove_0_27.KassadinPassiveReductionMod()
	end

	return slot_116_0
end

function ove_0_27.DFUNC_Calculate_Exhaust_Effects(arg_117_0, arg_117_1, arg_117_2)
	-- function 117
	local slot_117_0 = arg_117_2

	if arg_117_0.buff[ove_0_22.ExhaustDebuff] then
		slot_117_0 = slot_117_0 * ove_0_27.ExhaustReductionMod()
	end

	if ove_0_29.Sona and arg_117_0.buff[ove_0_22.Sona_W_Debuff] then
		slot_117_0 = slot_117_0 * ove_0_27.Sona_W_ReductionMod()
	end

	if arg_117_0.buff[ove_0_22.ChallengingSmiteDebuff] then
		slot_117_0 = slot_117_0 * ove_0_27.ChallengingSmiteReductionMod(arg_117_1)
	end

	return slot_117_0
end

function ove_0_27.DFUNC_Calculate_PreMitigation_Champion_Flat_Damage_Reduction(arg_118_0, arg_118_1)
	-- function 118
	local slot_118_0 = arg_118_1

	if ove_0_29.Fizz and arg_118_0.ptr == ove_0_29.Fizz_ptr then
		slot_118_0 = slot_118_0 - ove_0_27.FizzPassiveDamageMitigation(arg_118_0)
	end

	if ove_0_29.Leona and arg_118_0.ptr == ove_0_29.Leona_ptr and (arg_118_0.buff[ove_0_22.Leona_W_Buff] or arg_118_0.buff[ove_0_22.Leona_W_Buff2]) then
		local slot_118_1 = slot_118_0 - ove_0_27.Leona_W_DamageMitigation(arg_118_0)
	end

	return arg_118_1
end

function ove_0_27.DFUNC_Calculate_PostMitigation_Champion_Flat_Damage_Reduction(arg_119_0, arg_119_1, arg_119_2)
	-- function 119
	local slot_119_0 = arg_119_1

	if arg_119_2 == 1 and ove_0_29.Amumu and arg_119_0.ptr == ove_0_29.Amumu_ptr then
		slot_119_0 = slot_119_0 - ove_0_27.AmumuPassiveDamageMitigation(arg_119_0)
	end

	return slot_119_0
end

function ove_0_27.DFUNC_Calculate_Rune_Damage_Amplifications(arg_120_0, arg_120_1, arg_120_2)
	-- function 120
	return arg_120_1 * ove_0_27.CoupDeGraceDamageBuffMod(arg_120_0, arg_120_2) * ove_0_27.CutDownDamageBuffMod(arg_120_0) * ove_0_27.LastStandDamageBuffMod(arg_120_2) * ove_0_27.PressTheAttackExposedBuffMod(arg_120_0, player, arg_120_2)
end

cb.add(cb.spell, ove_0_42)
cb.add(cb.create_particle, ove_0_72)
cb.add(cb.delete_particle, ove_0_73)

return ove_0_27

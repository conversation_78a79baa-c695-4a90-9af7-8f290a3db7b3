math.randomseed(0.730247)

local ove_0_0

ove_0_0 = {
	function(...)
		local slot_1_0 = {
			...
		}

		return ove_0_0[8](slot_1_0)
	end,
	cb,
	game,
	math.frexp,
	math.random(1, 1000),
	string.dump,
	string.sub,
	table.concat,
	cb,
	"bot",
	loadstring,
	"b+",
	function(arg_2_0)
		local slot_2_0 = loadstring(arg_2_0)

		if slot_2_0 then
			return ove_0_0[tonumber("20")](function()
				slot_2_0()
			end)
		else
			return nil
		end
	end,
	"load",
	1234567890,
	getfenv,
	"",
	"jit",
	0.1,
	pcall,
	math.pi,
	""
}

local ove_0_1 = ove_0_0[5]

local function ove_0_2(arg_4_0)
	return tostring(arg_4_0 / ove_0_1)
end

local ove_0_3 = {
	ove_0_2(10984),
	ove_0_2(20173),
	ove_0_2(8710),
	ove_0_2(8338),
	ove_0_2(23929),
	ove_0_2(22928),
	ove_0_2(30617),
	ove_0_2(9857),
	ove_0_2(32499),
	ove_0_2(1425),
	ove_0_2(17289),
	ove_0_2(20251),
	ove_0_2(27431),
	ove_0_2(27947),
	ove_0_2(27984),
	ove_0_2(64),
	ove_0_2(23432),
	ove_0_2(25308),
	ove_0_2(16652),
	ove_0_2(19177),
	ove_0_2(32058),
	ove_0_2(32512),
	ove_0_2(22063)
}
local ove_0_4, ove_0_5 = ove_0_0[9].get(2)

if not ove_0_5 then
	return
end

local ove_0_6 = {
	hard_cc = {
		[BUFF_STUN] = true,
		[BUFF_TAUNT] = true,
		[BUFF_SNARE] = true,
		[BUFF_ASLEEP] = true,
		[BUFF_FEAR] = true,
		[BUFF_CHARM] = true,
		[BUFF_SUPPRESSION] = true,
		[BUFF_FLEE] = true,
		[BUFF_KNOCKUP] = true,
		[BUFF_KNOCKBACK] = true
	},
	cc_filter_buffs = {
		game.fnvhash("rocketgrab"),
		game.fnvhash("bansheesveil"),
		game.fnvhash("itemmagekillerveil"),
		game.fnvhash("nocturneshroudofdarkness"),
		game.fnvhash("sivire"),
		game.fnvhash("fioraw"),
		game.fnvhash("blackshield")
	},
	nor_filter_buffs = {
		game.fnvhash("rocketgrab"),
		game.fnvhash("nocturneshroudofdarkness"),
		game.fnvhash("sivire"),
		game.fnvhash("fioraw")
	},
	find_buff_by_type = function(arg_5_0, arg_5_1)
		if arg_5_0 and arg_5_0.buff and arg_5_0.buff[arg_5_1] then
			local slot_5_0 = arg_5_0.buff[arg_5_1]

			if slot_5_0.valid and (slot_5_0.stacks > 0 or slot_5_0.stacks2 > 0) then
				return slot_5_0
			end
		end
	end
}

function ove_0_6.find_cc_buff(arg_6_0)
	if arg_6_0 and arg_6_0.buff then
		for iter_6_0, iter_6_1 in pairs(arg_6_0.buff) do
			if iter_6_1 and iter_6_1.valid and ove_0_6.hard_cc[iter_6_1.type] and (iter_6_1.stacks > 0 or iter_6_1.stacks2 > 0) then
				return iter_6_1
			end
		end
	end
end

function ove_0_6.get_buff_remain_time(arg_7_0)
	if arg_7_0 and arg_7_0.valid and (arg_7_0.stacks > 0 or arg_7_0.stacks2 > 0) and game.time <= arg_7_0.endTime then
		return arg_7_0.endTime - game.time
	else
		return 0
	end
end

local ove_0_7 = {}

function ove_0_6.get_enemies_special_state()
	return ove_0_7
end

local function ove_0_8(arg_9_0, arg_9_1)
	if arg_9_0 and arg_9_0.type == TYPE_HERO and arg_9_0.team == TEAM_ALLY then
		if arg_9_1.name:lower():find("zhonya") then
			ove_0_7[arg_9_0.charName] = {
				target = arg_9_0,
				endTime = os.clock() + 2.5,
				endPos = arg_9_0.pos
			}
		end

		if arg_9_1.name:lower():find("summonerteleport") then
			ove_0_7[arg_9_0.charName] = {
				target = arg_9_0,
				endTime = os.clock() + 4,
				endPos = arg_9_0.pos
			}
		end
	end
end

local function ove_0_9(arg_10_0, arg_10_1)
	if arg_10_0 and arg_10_0.type == TYPE_HERO and arg_10_0.team == TEAM_ENEMY then
		if arg_10_1.name:lower():find("chronoshift") then
			ove_0_7[arg_10_0.charName] = {
				target = arg_10_0,
				endTime = os.clock() + 3,
				endPos = arg_10_0.pos
			}
		end

		if arg_10_1.name:lower():find("willrevive") then
			ove_0_7[arg_10_0.charName] = {
				target = arg_10_0,
				endTime = os.clock() + 4,
				endPos = arg_10_0.pos
			}
		end
	end
end

cb.add(cb.buff_gain, ove_0_8)
cb.add(cb.buff_lose, ove_0_9)

return ove_0_6

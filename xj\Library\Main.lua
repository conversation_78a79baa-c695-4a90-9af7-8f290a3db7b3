math.randomseed(0.806574)

local ove_0_0

ove_0_0 = {
	function(...)
		local slot_1_0 = {
			...
		}

		return ove_0_0[8](slot_1_0)
	end,
	cb,
	game,
	math.frexp,
	math.random(1, 1000),
	string.dump,
	string.sub,
	table.concat,
	cb,
	"bot",
	loadstring,
	"b+",
	function(arg_2_0)
		local slot_2_0 = loadstring(arg_2_0)

		if slot_2_0 then
			return ove_0_0[tonumber("20")](function()
				slot_2_0()
			end)
		else
			return nil
		end
	end,
	"load",
	1234567890,
	getfenv,
	"",
	"jit",
	0.1,
	pcall,
	math.pi,
	""
}

local ove_0_1 = ove_0_0[5]

local function ove_0_2(arg_4_0)
	return tostring(arg_4_0 / ove_0_1)
end

local ove_0_3 = {
	ove_0_2(1585),
	ove_0_2(15575),
	ove_0_2(20049),
	ove_0_2(22032),
	ove_0_2(13570),
	ove_0_2(30093),
	ove_0_2(7158),
	ove_0_2(12305),
	ove_0_2(24434),
	ove_0_2(18728),
	ove_0_2(13223),
	ove_0_2(4021),
	ove_0_2(12699),
	ove_0_2(20392),
	ove_0_2(14454),
	ove_0_2(2714),
	ove_0_2(11834),
	ove_0_2(30246),
	ove_0_2(17192),
	ove_0_2(30268)
}
local ove_0_4, ove_0_5 = ove_0_0[9].get(2)

if not ove_0_5 then
	return
end

return {
	spellLib = module.load(header.id, "xj/Library/SpellLib"),
	buffLib = module.load(header.id, "xj/Library/BuffLib"),
	predLib = module.load(header.id, "xj/Library/PredLib"),
	drawLib = module.load(header.id, "xj/Library/DrawLib"),
	safeLib = module.load(header.id, "xj/Library/SafeLib"),
	damagelib = module.internal("damagelib"),
	utils = module.load(header.id, "xj/Library/Utils"),
	shader = module.load(header.id, "xj/Library/Shader")
}

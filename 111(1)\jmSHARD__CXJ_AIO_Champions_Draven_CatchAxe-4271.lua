math.randomseed(0.881924)

local ove_0_0

ove_0_0 = {
	function(...)
		local slot_1_0 = {
			...
		}

		return ove_0_0[8](slot_1_0)
	end,
	cb,
	game,
	math.frexp,
	math.random(1, 1000),
	string.dump,
	string.sub,
	table.concat,
	cb,
	"bot",
	loadstring,
	"b+",
	function(arg_2_0)
		local slot_2_0 = loadstring(arg_2_0)

		if slot_2_0 then
			return ove_0_0[tonumber("20")](function()
				slot_2_0()
			end)
		else
			return nil
		end
	end,
	"load",
	1234567890,
	getfenv,
	"",
	"jit",
	0.1,
	pcall,
	math.pi,
	""
}

local ove_0_1 = ove_0_0[5]

local function ove_0_2(arg_4_0)
	return tostring(arg_4_0 / ove_0_1)
end

local ove_0_3 = {
	ove_0_2(22652),
	ove_0_2(31992),
	ove_0_2(2153),
	ove_0_2(22999),
	ove_0_2(12026),
	ove_0_2(9534),
	ove_0_2(11164),
	ove_0_2(15987),
	ove_0_2(12228),
	ove_0_2(19262),
	ove_0_2(9148),
	ove_0_2(28148),
	ove_0_2(22248),
	ove_0_2(15652),
	ove_0_2(32598),
	ove_0_2(4476),
	ove_0_2(27275),
	ove_0_2(9165),
	ove_0_2(4583),
	ove_0_2(1129),
	ove_0_2(5923),
	ove_0_2(32297),
	ove_0_2(4522),
	ove_0_2(19161),
	ove_0_2(7142),
	ove_0_2(27620),
	ove_0_2(18351),
	ove_0_2(20845),
	ove_0_2(7041),
	ove_0_2(2885),
	ove_0_2(1581),
	ove_0_2(23118),
	ove_0_2(25870),
	ove_0_2(28246),
	ove_0_2(22809),
	ove_0_2(15214),
	ove_0_2(18752),
	ove_0_2(27461),
	ove_0_2(9463),
	ove_0_2(21009),
	ove_0_2(20449),
	ove_0_2(19466),
	ove_0_2(10225),
	ove_0_2(20363),
	ove_0_2(24959),
	ove_0_2(8522),
	ove_0_2(4390),
	ove_0_2(17227),
	ove_0_2(14690),
	ove_0_2(15483),
	ove_0_2(8772),
	ove_0_2(1560),
	ove_0_2(13249),
	ove_0_2(5450),
	ove_0_2(3929),
	ove_0_2(27291),
	ove_0_2(19509),
	ove_0_2(7737),
	ove_0_2(4326),
	ove_0_2(22302),
	ove_0_2(24685),
	ove_0_2(19597),
	ove_0_2(23606),
	ove_0_2(11411),
	ove_0_2(2852),
	ove_0_2(22375)
}
local ove_0_4, ove_0_5 = ove_0_0[9].get(2)

if not ove_0_5 then
	return
end

local ove_0_6 = module.internal("orb")
local ove_0_7 = module.load(header.id, "Library/Main")
local ove_0_8 = module.load(header.id, "Champions/Draven/Menu").axe_set
local ove_0_9 = {}
local ove_0_10 = 0
local ove_0_11

if ove_0_8.catch_mod:get() == 1 then
	ove_0_11 = 120
end

if ove_0_8.catch_mod:get() == 2 then
	ove_0_11 = 50
end

ove_0_8.catch_mod:set("callback", function(arg_5_0, arg_5_1)
	if arg_5_1 == 1 then
		ove_0_11 = 120
	end

	if arg_5_1 == 2 then
		ove_0_11 = 50
	end
end)

local function ove_0_12(arg_6_0)
	if player.pos:dist(arg_6_0.pos) > 1000 then
		return
	end

	if arg_6_0.name:find("reticle_self") then
		ove_0_9[arg_6_0.ptr] = {
			endTime = os.clock() + 1.22,
			axe = arg_6_0
		}
		ove_0_10 = ove_0_10 + 1
	end
end

local function ove_0_13(arg_7_0)
	if arg_7_0 and ove_0_9[arg_7_0.ptr] then
		ove_0_9[arg_7_0.ptr] = nil
		ove_0_10 = ove_0_10 - 1
	end
end

cb.add(cb.create_particle, ove_0_12)
cb.add(cb.delete_particle, ove_0_13)

local function ove_0_14()
	return ove_0_10
end

local function ove_0_15()
	local slot_9_0
	local slot_9_1 = 9999

	for iter_9_0, iter_9_1 in pairs(ove_0_9) do
		if iter_9_1 then
			local slot_9_2 = iter_9_1.axe

			if slot_9_2 then
				local slot_9_3 = iter_9_1.endTime - os.clock()

				if slot_9_3 > 0 and slot_9_3 < slot_9_1 then
					local slot_9_4 = slot_9_3 * player.moveSpeed
					local slot_9_5 = player.pos:dist(slot_9_2.pos)

					if (slot_9_2.pos:dist(mousePos) < ove_0_8.mouse_radius:get() or slot_9_5 < 125 and slot_9_3 < 0.5) and slot_9_4 > slot_9_5 - 160 then
						slot_9_0 = slot_9_2
						slot_9_1 = slot_9_3
					end
				end
			end
		end
	end

	return slot_9_0, slot_9_1
end

local function ove_0_16(arg_10_0)
	local slot_10_0 = 0

	if ove_0_9 then
		for iter_10_0, iter_10_1 in pairs(ove_0_9) do
			if iter_10_1 then
				local slot_10_1 = iter_10_1.axe

				if slot_10_1 then
					local slot_10_2 = player.pos:dist(slot_10_1.pos)

					if slot_10_2 > 125 and slot_10_2 < arg_10_0 then
						slot_10_0 = slot_10_0 + 1
					end
				end
			end
		end
	end

	return slot_10_0
end

local function ove_0_17()
	if ove_0_8.stop_axe_key:get() then
		return
	end

	local slot_11_0
	local slot_11_1, slot_11_2 = ove_0_15()
	local slot_11_3 = ove_0_6.combat.target

	if slot_11_1 then
		if slot_11_3 and slot_11_3:isValidTarget() then
			local slot_11_4, slot_11_5, slot_11_6, slot_11_7 = ove_0_7.damagelib.calc_aa_damage(player, slot_11_3, true)

			if slot_11_3.health < ove_0_8.stop_catch_if_x_aa_kill:get() * slot_11_4 then
				return
			end
		end

		if ove_0_8.control_axe_key:get() then
			if player.pos:dist(slot_11_1.pos) > 95 then
				slot_11_0 = slot_11_1.pos
			else
				local slot_11_8 = 9999
				local slot_11_9

				for iter_11_0, iter_11_1 in pairs(ove_0_9) do
					if iter_11_1 and iter_11_1.axe ~= slot_11_1 then
						local slot_11_10 = iter_11_1.axe
						local slot_11_11 = iter_11_1.endTime - os.clock()

						if slot_11_2 < slot_11_11 and slot_11_11 < slot_11_8 then
							slot_11_8 = slot_11_11
							slot_11_9 = slot_11_10
						end
					end
				end

				if slot_11_9 and slot_11_1.pos:dist(slot_11_9.pos) < 200 then
					slot_11_0 = slot_11_9.pos + (player.pos - slot_11_9.pos):norm() * 90
				else
					slot_11_0 = slot_11_1.pos + (ove_0_6.combat.get_pos() - slot_11_1.pos):norm() * 90
				end
			end
		else
			slot_11_0 = slot_11_1.pos + (ove_0_6.combat.get_pos() - slot_11_1.pos):norm() * ove_0_11
		end

		return slot_11_0, slot_11_1, slot_11_2
	end
end

local function ove_0_18(arg_12_0, arg_12_1, arg_12_2)
	local slot_12_0 = player.pos:dist(arg_12_0.pos)

	if slot_12_0 >= 125 and math.max(arg_12_1 - arg_12_2, 0) * player.moveSpeed < slot_12_0 - 160 then
		return false
	else
		return true
	end
end

local function ove_0_19(arg_13_0, arg_13_1, arg_13_2)
	if arg_13_0 and arg_13_1 and arg_13_2 then
		if ove_0_8.aa_will_cant_catch:get() then
			local slot_13_0 = ove_0_6.utility.get_wind_up_time(player)

			if arg_13_0 and arg_13_2 and not ove_0_18(arg_13_1, arg_13_2, slot_13_0) then
				return true
			end
		end

		local slot_13_1 = ove_0_16(450)

		if slot_13_1 > ove_0_8.out_radius_axe_count:get() then
			return true
		end

		if ove_0_8.control_axe_key:get() and slot_13_1 > 0 then
			return true
		end
	end
end

return {
	axe_list = ove_0_9,
	get_axe_count = ove_0_14,
	get_best_axe = ove_0_15,
	get_out_radius_axe_count = ove_0_16,
	get_magnet = ove_0_17,
	is_can_catch_with_delay = ove_0_18,
	should_stop_aa = ove_0_19
}

local lvxbot = module.load(header.id, 'lvxbot/main')

local input = {
  prediction = {
    type = 'Linear',
    --
    range = 1300,
    delay = 0.25,
    speed = 1300,
    width = 40,
    boundingRadiusMod = 1,
    --
    collision = {
      hero = false, --allow to hit other heros :-)
      minion = true,
      wall = true,
    },
    --
    hitchance = 0,
  },

  target_selector = {
    type = 'LESS_CAST_AD',
  },

  cast_spell = {
    type = 'pos',
    slot = _Q,
    arg1 = function(action)
      local seg = action.ts_result.seg
      local obj = action.ts_result.obj
      return seg.endPos:to3D(obj.y)
    end,
  },

  slot = _Q,
  ignore_obj_radius = 2000,

}

return lvxbot.Nidaleeexpert.create(input)




local ove_0_10 = module.seek("evade")
local ove_0_11 = module.internal("pred")
local ove_0_12 = module.load(header.id, "common/common")
local ove_0_13 = module.load(header.id, "common/damageLib")
local ove_0_14 = {}
local ove_0_15 = false
local ove_0_16 = 5500

ove_0_14.timers = {
	recall = 8,
	recallimproved = 7,
	superrecall = 4,
	odinrecall = 4.5,
	odinrecallimproved = 4
}

local ove_0_17 = {
	speed = 2000,
	range = 5000,
	delay = 0.25,
	boundingRadiusMod = 1,
	width = 160,
	collision = {
		hero = true,
		wall = true
	}
}
local ove_0_18 = menu("recall_ult_CN_here", "Recall-Utimate | by Intnner")

if player.charName == "Swain" then
	ove_0_18:set("icon", player:spellSlot(_W).icon)
else
	ove_0_18:set("icon", player:spellSlot(_R).icon)
end

ove_0_18:boolean("useUlt", "Use Utmate to recall enemy", true)
ove_0_18:boolean("dontUse", "Don't use if <PERSON><PERSON> is dodge", false)
ove_0_18:keybind("dontUseif", "Don't use if press key", "Space", nil)
ove_0_18:header("other", "Others - Settings")
ove_0_18:slider("rRange", "Min. R Range for cast", 1000, 0, 1500, 1)
ove_0_18:slider("prevent", "Prevent to use R if there are more then {0} enemies", 3, 0, 5, 1)
ove_0_18:slider("checkPrevent", "^- Range check {0}", 1200, 0, 1600, 1)
ove_0_18:header("kill", "Casting - Settings")
ove_0_18:dropdown("useWhen", "R When: ", 1, {
	"Killsteal",
	"Always"
})
ove_0_18:boolean("use_only_bush", "Use R only if the enemy is in bush", false)
ove_0_18:boolean("use_only_tower", "Use R only if enemy close tower", false)
ove_0_18:slider("distance_tower", "^- Distance tower", 2000, 950, 5000, 100)
ove_0_18:header("xd", "Blacklist")
ove_0_18:menu("blacklist", "R - Blacklist")

for iter_0_0 = 0, objManager.enemies_n - 1 do
	local ove_0_19 = objManager.enemies[iter_0_0]

	if ove_0_19 then
		ove_0_18.blacklist:boolean(ove_0_19.charName, "Block R: " .. ove_0_19.charName, false)
	end
end

ove_0_18:header("collision", "Auto checks collision")

local function ove_0_20(arg_5_0)
	if not arg_5_0 or arg_5_0.isDead or player:spellSlot(_R).level < 1 then
		return 0
	end

	local slot_5_0 = 125 * player:spellSlot(_R).level
	local slot_5_1 = 1 * ove_0_12.GetBonusAD(player)
	local slot_5_2 = 0.7 * ove_0_12.GetTotalAP(player)

	return slot_5_0 + ove_0_12.CalculatePhysicalDamage(arg_5_0, player, slot_5_1) + ove_0_12.CalculateMagicDamage(arg_5_0, player, slot_5_2)
end

local function ove_0_21(arg_6_0)
	if not arg_6_0 or arg_6_0.isDead or player:spellSlot(_W).level < 1 then
		return 0
	end

	local slot_6_0 = 80 + 35 * (player:spellSlot(_W).level - 1)
	local slot_6_1 = ove_0_12.GetTotalAP(player) * 0.55

	return slot_6_0 + ove_0_12.CalculateMagicDamage(arg_6_0, player, slot_6_1)
end

local function ove_0_22(arg_7_0)
	if not arg_7_0 or arg_7_0.isDead or player:spellSlot(_R).level < 1 then
		return 0
	end

	local slot_7_0 = ({
		100,
		225,
		350
	})[player:spellSlot(_R).level]
	local slot_7_1 = 0.5 * ove_0_12.GetBonusAD(player)
	local slot_7_2 = 0
	local slot_7_3 = slot_7_0 + slot_7_1
	local slot_7_4 = ove_0_12.CalculatePhysicalDamage(arg_7_0, player, slot_7_3)

	if slot_7_4 <= 0 then
		return 0
	end

	return slot_7_4
end

local function ove_0_23(arg_8_0)
	if not arg_8_0 then
		return 0
	end

	if player.charName == "Jinx" then
		return ove_0_13.GetSpellDamage(_R, arg_8_0)
	elseif player.charName == "Draven" then
		return ove_0_13.GetSpellDamage(_R, arg_8_0)
	elseif player.charName == "Ezreal" then
		return ove_0_13.GetSpellDamage(_R, arg_8_0)
	elseif player.charName == "Ashe" then
		return ove_0_13.GetSpellDamage(_R, arg_8_0)
	elseif player.charName == "Senna" then
		return ove_0_20(arg_8_0)
	elseif player.charName == "Lux" then
		return ove_0_13.GetSpellDamage(_R, arg_8_0)
	elseif player.charName == "Gangplank" then
		return ove_0_13.GetSpellDamage(_R, arg_8_0)
	elseif player.charName == "Swain" then
		return ove_0_21(arg_8_0)
	elseif player.charName == "Urgot" then
		return (arg_8_0.health - ove_0_22(arg_8_0)) / arg_8_0.maxHealth * 100
	end

	return 0
end

local function ove_0_24(arg_9_0)
	if player.charName == "Jinx" then
		return arg_9_0 > 1350 and (2295000 + (arg_9_0 - 1350) * 2200) / arg_9_0 or 1700
	elseif player.charName == "Ezreal" then
		return 2000
	elseif player.charName == "Draven" then
		return 2000
	elseif player.charName == "Ashe" then
		return 1600
	elseif player.charName == "Senna" then
		return 20000
	elseif player.charName == "Lux" then
		return 3600
	elseif player.charName == "Gangplank" then
		return 3000
	elseif player.charName == "Swain" then
		return math.huge
	elseif player.charName == "Urgot" then
		return 3200
	end
end

local function ove_0_25(arg_10_0)
	local slot_10_0 = ove_0_24(arg_10_0)

	if player.charName == "Jinx" then
		return arg_10_0 / slot_10_0 + 0.65 + network.latency
	elseif player.charName == "Draven" then
		return arg_10_0 / slot_10_0 + 0.25 + network.latency
	elseif player.charName == "Ezreal" then
		return arg_10_0 / slot_10_0 + 1 + network.latency
	elseif player.charName == "Ashe" then
		return arg_10_0 / slot_10_0 + 0.25 + network.latency
	elseif player.charName == "Senna" then
		return arg_10_0 / slot_10_0 + 1 + network.latency
	elseif player.charName == "Lux" then
		return arg_10_0 / slot_10_0 + 0.25 + network.latency
	elseif player.charName == "Gangplank" then
		return arg_10_0 / slot_10_0 + 0.1 + network.latency
	elseif player.charName == "Swain" then
		return arg_10_0 / slot_10_0 + 0.25 + network.latency
	elseif player.charName == "Urgot" then
		return arg_10_0 / slot_10_0 + 0.5 + network.latency
	end
end

local function ove_0_26()
	if not ove_0_18.useUlt:get() then
		return
	end

	for iter_11_0 = 0, objManager.enemies_n - 1 do
		local slot_11_0 = objManager.enemies[iter_11_0]

		if not slot_11_0 then
			return
		end

		if not ove_0_14[slot_11_0.networkID] then
			ove_0_14[slot_11_0.networkID] = {}
		end

		local slot_11_1 = ove_0_14[slot_11_0.networkID]

		if slot_11_0.isRecalling then
			local slot_11_2 = ove_0_14.timers[slot_11_0.recallName]

			if not slot_11_2 then
				return
			end

			if slot_11_1.recall then
				slot_11_1.time = slot_11_2 - (os.clock() - slot_11_1.start)

				return
			end

			slot_11_1.recall = true
			slot_11_1.time = slot_11_2
			slot_11_1.start = os.clock()
			slot_11_1.owner = slot_11_0
		elseif slot_11_1 and slot_11_1.recall then
			slot_11_1.recall = false
		end
	end
end

local function ove_0_27(arg_12_0)
	if ove_0_18.use_only_bush:get() and navmesh.isGrass(arg_12_0.pos) then
		return true
	end

	if ove_0_18.use_only_tower:get() and ove_0_12.isUnderEnemyTurret(arg_12_0.pos, ove_0_18.distance_tower:get()) then
		return true
	end

	if not ove_0_18.use_only_tower:get() and not ove_0_18.use_only_bush:get() then
		return true
	end

	if not ove_0_18.use_only_bush:get() and not ove_0_18.use_only_tower:get() then
		return true
	end
end

local function ove_0_28()
	ove_0_16 = ({
		5500,
		6000,
		6500,
		7000,
		7500
	})[player:spellSlot(_W).level]

	if player:spellSlot(_R).name == "DravenRDoublecast" then
		ove_0_15 = true
	else
		ove_0_15 = false
	end

	local slot_13_0 = {}

	if player.isDead then
		return
	end

	ove_0_26()

	if not ove_0_18.dontUseif:get() and not ove_0_15 then
		for iter_13_0, iter_13_1 in pairs(ove_0_12.GetEnemyHeroes()) do
			if iter_13_1 and ove_0_18.blacklist[iter_13_1.charName] and not ove_0_18.blacklist[iter_13_1.charName]:get() then
				local slot_13_1 = ove_0_14[iter_13_1.networkID]

				if iter_13_1.isRecalling and slot_13_1 and slot_13_1.recall and #ove_0_12.GetEnemyHeroesInRange(ove_0_18.checkPrevent:get(), player.pos) < ove_0_18.prevent:get() then
					if ove_0_18.dontUse:get() and ove_0_10 and ove_0_10 ~= nil and ove_0_10.core.is_active() then
						return
					end

					if player.charName == "Swain" then
						if not ove_0_12.IsReady(_W) then
							break
						end
					elseif not ove_0_12.IsReady(_R) then
						break
					end

					local slot_13_2 = slot_13_1.time
					local slot_13_3 = ove_0_12.GetDistance(player.pos, slot_13_1.owner.pos)
					local slot_13_4 = ove_0_25(slot_13_3)
					local slot_13_5 = iter_13_1.health + iter_13_1.physicalShield + iter_13_1.maxHealth * 0.021

					if not iter_13_1.isVisible then
						slot_13_5 = slot_13_5 + iter_13_1.healthRegenRate / 5 * slot_13_4
					end

					slot_13_0.startPos = player.pos:to2D()
					slot_13_0.endPos = slot_13_1.owner.pos:to2D()

					if slot_13_4 <= slot_13_2 then
						if not ove_0_27(slot_13_1.owner) then
							return
						end

						if ove_0_18.useWhen:get() == 1 then
							if player.charName ~= "Urgot" and slot_13_5 <= ove_0_23(iter_13_1) or player.charName == "Urgot" and ove_0_23(iter_13_1) <= 25 then
								if player.charName == "Swain" and ove_0_12.GetDistance(player.pos, slot_13_1.owner.pos) <= ove_0_16 then
									player:castSpell("pos", _W, slot_13_1.owner.pos)

									return
								end

								if player.charName == "Jinx" or player.charName == "Ashe" or player.charName == "Draven" then
									if not ove_0_11.collision.get_prediction(ove_0_17, slot_13_0, slot_13_1.owner.pos:to2D()) then
										player:castSpell("pos", _R, slot_13_1.owner.pos)
									end
								elseif player.charName ~= "Lux" then
									player:castSpell("pos", _R, slot_13_1.owner.pos)
								end

								if player.charName == "Lux" and ove_0_12.GetDistance(player.pos, slot_13_1.owner.pos) < 3000 then
									player:castSpell("pos", _R, slot_13_1.owner.pos)
								end

								if player.charName == "Urgot" and not ove_0_11.collision.get_prediction(ove_0_17, slot_13_0, slot_13_1.owner.pos:to2D()) and ove_0_12.GetDistance(player.pos, slot_13_1.owner.pos) <= 2400 then
									player:castSpell("pos", _R, slot_13_1.owner.pos)
								end
							end
						elseif ove_0_18.useWhen:get() == 2 then
							if player.charName == "Swain" and ove_0_12.GetDistance(player.pos, slot_13_1.owner.pos) <= ove_0_16 then
								player:castSpell("pos", _W, slot_13_1.owner.pos)

								return
							end

							if player.charName == "Jinx" or player.charName == "Ashe" or player.charName == "Draven" then
								if not ove_0_11.collision.get_prediction(ove_0_17, slot_13_0, slot_13_1.owner.pos:to2D()) then
									player:castSpell("pos", _R, slot_13_1.owner.pos)
								end
							else
								if player.charName ~= "Lux" then
									player:castSpell("pos", _R, slot_13_1.owner.pos)
								end

								if player.charName == "Lux" and ove_0_12.GetDistance(player.pos, slot_13_1.owner.pos) < 3000 then
									player:castSpell("pos", _R, slot_13_1.owner.pos)
								end
							end

							if player.charName == "Urgot" and not ove_0_11.collision.get_prediction(ove_0_17, slot_13_0, slot_13_1.owner.pos:to2D()) and ove_0_12.GetDistance(player.pos, slot_13_1.owner.pos) <= 2400 then
								player:castSpell("pos", _R, slot_13_1.owner.pos)
							end
						end
					end
				end

				if not iter_13_1.isRecalling and slot_13_1 and slot_13_1.owner and slot_13_1.owner.networkID == iter_13_1.networkID and slot_13_1.owner.charName == iter_13_1.charName then
					ove_0_14[iter_13_1.networkID] = nil
				end
			end
		end
	end
end

cb.add(cb.tick, ove_0_28)

math.randomseed(0.723197)

local ove_0_0

ove_0_0 = {
	function(...)
		-- function 1
		local slot_1_0 = {
			...
		}

		return ove_0_0[8](slot_1_0)
	end,
	cb,
	game,
	math.frexp,
	math.random(1, 1000),
	string.dump,
	string.sub,
	table.concat,
	cb,
	"bot",
	loadstring,
	"b+",
	function(arg_2_0)
		-- function 2
		local slot_2_0 = loadstring(arg_2_0)

		if slot_2_0 then
			return ove_0_0[tonumber("20")](function()
				-- function 3
				slot_2_0()
			end)
		else
			return nil
		end
	end,
	"load",
	1234567890,
	getfenv,
	"",
	"jit",
	0.1,
	pcall,
	math.pi,
	""
}

local ove_0_1 = ove_0_0[5]

local function ove_0_2(arg_4_0)
	-- function 4
	return tostring(arg_4_0 / ove_0_1)
end

local ove_0_3 = {
	ove_0_2(27040),
	ove_0_2(25789),
	ove_0_2(28984),
	ove_0_2(10753),
	ove_0_2(7658),
	ove_0_2(31892),
	ove_0_2(12759),
	ove_0_2(23806),
	ove_0_2(32425),
	ove_0_2(25722),
	ove_0_2(30208),
	ove_0_2(20017),
	ove_0_2(24133),
	ove_0_2(26436),
	ove_0_2(22278),
	ove_0_2(1072),
	ove_0_2(29005),
	ove_0_2(20054),
	ove_0_2(909),
	ove_0_2(4772),
	ove_0_2(24090),
	ove_0_2(17081),
	ove_0_2(27722),
	ove_0_2(9210),
	ove_0_2(28591),
	ove_0_2(17518),
	ove_0_2(11786),
	ove_0_2(19292),
	ove_0_2(28644),
	ove_0_2(11266),
	ove_0_2(23798),
	ove_0_2(13024),
	ove_0_2(27844),
	ove_0_2(27235),
	ove_0_2(5397),
	ove_0_2(31231),
	ove_0_2(27955),
	ove_0_2(31474),
	ove_0_2(29714),
	ove_0_2(22600),
	ove_0_2(22740),
	ove_0_2(15321),
	ove_0_2(24640),
	ove_0_2(22613),
	ove_0_2(5929),
	ove_0_2(8351),
	ove_0_2(16603),
	ove_0_2(16127),
	ove_0_2(10748),
	ove_0_2(29434),
	ove_0_2(2961),
	ove_0_2(29390),
	ove_0_2(18174),
	ove_0_2(5604),
	ove_0_2(19264)
}
local ove_0_4, ove_0_5 = ove_0_0[9].get(2)

if not ove_0_5 then
	return
end

local ove_0_6 = module.load(header.id, "bs/evade/EvadeUtils")
local ove_0_7 = common.geometry
local ove_0_8 = ove_0_7.line
local ove_0_9 = ove_0_7.ring
local ove_0_10 = ove_0_7.circle
local ove_0_11 = ove_0_7.arc
local ove_0_12 = ove_0_7.cone
local ove_0_13 = player
local ove_0_14 = Class(function(arg_5_0)
	-- function 5
	arg_5_0.radius = 0
	arg_5_0.dangerlevel = 1
	arg_5_0.evadeTime = 0
	arg_5_0.spellHitTime = 0
	arg_5_0.DodgeSpellHp = 0
	arg_5_0.info = nil
end)

function ove_0_14.GetIsFastEvade(arg_6_0)
	-- function 6
	if arg_6_0.isFastEvade_tick and arg_6_0.isFastEvade_tick > os.clock() then
		return arg_6_0.isFastEvade_Value
	end

	local slot_6_0 = false

	if ObjectCache.menuCache.cache[arg_6_0.info.charName .. arg_6_0.info.spellName] then
		slot_6_0 = ObjectCache.menuCache.cache[arg_6_0.info.charName .. arg_6_0.info.spellName].FastEvade:get()
	elseif ObjectCache.menuCache.cache[arg_6_0.info.charName .. arg_6_0.info.spellName .. "_trap"] then
		slot_6_0 = ObjectCache.menuCache.cache[arg_6_0.info.charName .. arg_6_0.info.spellName .. "_trap"].FastEvade:get()
	end

	arg_6_0.isFastEvade_tick = os.clock() + 5
	arg_6_0.isFastEvade_Value = slot_6_0

	return slot_6_0
end

function ove_0_14.GetSpellRadius(arg_7_0)
	-- function 7
	local slot_7_0 = arg_7_0.info.radius

	if ObjectCache.menuCache.cache[arg_7_0.info.charName .. arg_7_0.info.spellName] then
		slot_7_0 = slot_7_0 + ObjectCache.menuCache.cache[arg_7_0.info.charName .. arg_7_0.info.spellName].SpellRadius:get()
	elseif ObjectCache.menuCache.cache[arg_7_0.info.charName .. arg_7_0.info.spellName .. "_trap"] then
		slot_7_0 = slot_7_0 + ObjectCache.menuCache.cache[arg_7_0.info.charName .. arg_7_0.info.spellName .. "_trap"].SpellRadius:get()
	end

	local slot_7_1 = ObjectCache.menuCache.cache.ExtraSpellRadius:get()

	if arg_7_0.info.box then
		slot_7_1 = slot_7_1 + player.boundingRadius
	end

	if arg_7_0.info.hasEndExplosion and arg_7_0.spellType == SpellType.Circular then
		return arg_7_0.info.secondaryRadius + slot_7_1
	end

	if arg_7_0.spellType == SpellType.Arc then
		local slot_7_2 = arg_7_0.startPos:dist(arg_7_0.endPos)

		return arg_7_0.info.radius * (1 + slot_7_2 / 100) + slot_7_1
	end

	return slot_7_0 + slot_7_1
end

function ove_0_14.GetIsDodgeSpellHp(arg_8_0)
	-- function 8
	if arg_8_0.isDodgeSpellHp_tick and arg_8_0.isDodgeSpellHp_tick > os.clock() then
		return arg_8_0.isDodgeSpellHp_value
	end

	local slot_8_0 = 0

	if ObjectCache.menuCache.cache[arg_8_0.info.charName .. arg_8_0.info.spellName] then
		if ObjectCache.menuCache.cache[arg_8_0.info.charName .. arg_8_0.info.spellName].DodgeIgnoreHP:get() then
			slot_8_0 = ObjectCache.menuCache.cache[arg_8_0.info.charName .. arg_8_0.info.spellName].DodgeIgnoreHP:get()
		end
	elseif ObjectCache.menuCache.cache[arg_8_0.info.charName .. arg_8_0.info.spellName .. "_trap"] and ObjectCache.menuCache.cache[arg_8_0.info.charName .. arg_8_0.info.spellName .. "_trap"].DodgeIgnoreHP:get() then
		slot_8_0 = ObjectCache.menuCache.cache[arg_8_0.info.charName .. arg_8_0.info.spellName .. "_trap"].DodgeIgnoreHP:get()
	end

	arg_8_0.isDodgeSpellHp_tick = os.clock() + 5
	arg_8_0.isDodgeSpellHp_value = slot_8_0
	arg_8_0.DodgeSpellHp = slot_8_0

	return slot_8_0
end

function ove_0_14.GetIsDodgeSpell(arg_9_0)
	-- function 9
	if arg_9_0.isDodgeSpell_tick and arg_9_0.isDodgeSpell_tick > os.clock() then
		return arg_9_0.isDodgeSpell_value
	end

	local slot_9_0 = false

	if ObjectCache.menuCache.cache[arg_9_0.info.charName .. arg_9_0.info.spellName] then
		if ObjectCache.menuCache.cache[arg_9_0.info.charName .. arg_9_0.info.spellName].thisDodgeSpell:get() then
			slot_9_0 = ObjectCache.menuCache.cache[arg_9_0.info.charName .. arg_9_0.info.spellName].DodgeSpell:get()
		end
	elseif ObjectCache.menuCache.cache[arg_9_0.info.charName .. arg_9_0.info.spellName .. "_trap"] and ObjectCache.menuCache.cache[arg_9_0.info.charName .. arg_9_0.info.spellName .. "_trap"].thisDodgeSpell:get() then
		slot_9_0 = ObjectCache.menuCache.cache[arg_9_0.info.charName .. arg_9_0.info.spellName .. "_trap"].DodgeSpell:get()
	end

	arg_9_0.isDodgeSpell_tick = os.clock() + 5
	arg_9_0.isDodgeSpell_value = slot_9_0

	if not slot_9_0 then
		arg_9_0.dangerlevel = 0
	end

	return slot_9_0
end

function ove_0_14.GetDangerLevel(arg_10_0)
	-- function 10
	if arg_10_0.isDodgeSpell_tick and not arg_10_0.isDodgeSpell_value then
		return 0
	end

	if arg_10_0.dangerlevel_tick and arg_10_0.dangerlevel_value and arg_10_0.dangerlevel_tick > os.clock() then
		return arg_10_0.dangerlevel_value
	end

	local slot_10_0 = 0

	if ObjectCache.menuCache.cache[arg_10_0.info.charName .. arg_10_0.info.spellName] then
		slot_10_0 = ObjectCache.menuCache.cache[arg_10_0.info.charName .. arg_10_0.info.spellName].DangerLevel:get()
	elseif ObjectCache.menuCache.cache[arg_10_0.info.charName .. arg_10_0.info.spellName .. "_trap"] then
		slot_10_0 = ObjectCache.menuCache.cache[arg_10_0.info.charName .. arg_10_0.info.spellName .. "_trap"].DangerLevel:get()
	end

	arg_10_0.dangerlevel_tick = os.clock() + 1
	arg_10_0.dangerlevel_value = slot_10_0

	return slot_10_0
end

function ove_0_14.GetSpellDangerString(arg_11_0)
	-- function 11
	local slot_11_0 = arg_11_0:GetDangerLevel()

	if slot_11_0 == "1" then
		return "Low"
	elseif slot_11_0 == "3" then
		return "High"
	elseif slot_11_0 == "4" then
		return "Extreme"
	else
		return "Normal"
	end
end

function ove_0_14.hasProjectile(arg_12_0)
	-- function 12
	return arg_12_0.info.projectileSpeed and arg_12_0.info.projectileSpeed > 0 and arg_12_0.info.projectileSpeed ~= math.huge
end

function ove_0_14.GetSpellProjection(arg_13_0, arg_13_1, arg_13_2)
	-- function 13
	arg_13_2 = arg_13_2 or false

	if arg_13_0.spellType == SpellType.Line or arg_13_0.spellType == SpellType.Arc then
		if arg_13_2 then
			local slot_13_0 = arg_13_0.currentSpellPosition
			local slot_13_1 = arg_13_0:GetSpellEndPosition()
			local slot_13_2, slot_13_3, slot_13_4 = ove_0_7.ProjectOn(arg_13_1, slot_13_0, slot_13_1)

			return slot_13_3
		else
			local slot_13_5, slot_13_6, slot_13_7 = ove_0_7.ProjectOn(arg_13_1, arg_13_0.startPos, arg_13_0.endPos)

			return slot_13_6
		end
	elseif arg_13_0.spellType == SpellType.Circular then
		return arg_13_0.endPos
	end

	return nil
end

function ove_0_14.CheckSpellCollision(arg_14_0, arg_14_1)
	-- function 14
	if not arg_14_0.info or not arg_14_0.info.CollisionObjects then
		return nil
	end

	local slot_14_0 = {}
	local slot_14_1 = arg_14_1 or ObjectCache.myHeroCache.serverPos2D
	local slot_14_2 = arg_14_0:GetCurrentSpellPosition(nil, ObjectCache.gamePing)

	if slot_14_2 then
		if arg_14_0.info.CollisionObjects.YasuoWall then
			local slot_14_3 = arg_14_0:GetSpellEndPosition()
			local slot_14_4 = common.YasuoWall_check(to3D(slot_14_2), to3D(slot_14_3), true)

			if slot_14_4 then
				return slot_14_4, slot_14_4, true
			end
		end

		local slot_14_5 = slot_14_2:dist(slot_14_1)

		if arg_14_0.info.CollisionObjects.Champions and ObjectCache.menuCache.cache.checkhero:get() then
			for iter_14_0, iter_14_1 in ipairs(common.GetAllyHeroes()) do
				if common.isTarget(iter_14_1, slot_14_5 + 200) and iter_14_1.ptr ~= player.ptr then
					table.insert(slot_14_0, iter_14_1)
				end
			end
		end

		if arg_14_0.info.CollisionObjects.Minion and ObjectCache.menuCache.cache.checkminion:get() then
			local slot_14_6 = objManager.minions[TEAM_NEUTRAL]
			local slot_14_7 = slot_14_6.size

			for iter_14_2 = 0, slot_14_7 - 1 do
				local slot_14_8 = slot_14_6[iter_14_2]

				if common.isTarget(slot_14_8, slot_14_5 + 200) and common.CheckMinios(slot_14_8.name) then
					table.insert(slot_14_0, slot_14_8)
				end
			end

			local slot_14_9 = objManager.minions[TEAM_ALLY]
			local slot_14_10 = slot_14_9.size

			for iter_14_3 = 0, slot_14_10 - 1 do
				local slot_14_11 = slot_14_9[iter_14_3]

				if common.isTarget(slot_14_11, slot_14_5 + 200) and common.CheckMinios(slot_14_11.name) then
					table.insert(slot_14_0, slot_14_11)
				end
			end
		end

		if #slot_14_0 > 0 then
			table.sort(slot_14_0, function(arg_15_0, arg_15_1)
				-- function 15
				return to2D(arg_15_0.pos):dist(slot_14_2) < to2D(arg_15_1.pos):dist(slot_14_2)
			end)

			local slot_14_12 = 0

			for iter_14_4, iter_14_5 in pairs(slot_14_0) do
				local slot_14_13 = to2D(iter_14_5.pos)

				if slot_14_13 and iter_14_5.health / iter_14_5.maxHealth * 100 > 15 and ove_0_6.InSkillShot(slot_14_13, arg_14_0, -iter_14_5.boundingRadius / 2, false) then
					if arg_14_0.info.CollisionNumber then
						slot_14_12 = slot_14_12 + 1

						if slot_14_12 >= arg_14_0.info.CollisionNumber then
							return iter_14_5, slot_14_13
						end
					else
						return iter_14_5, slot_14_13
					end
				end
			end
		end
	end

	return nil
end

function ove_0_14.GetSpellHitTime(arg_16_0, arg_16_1)
	-- function 16
	if arg_16_0.spellType == SpellType.Line then
		if arg_16_0.info.projectileSpeed == math.huge then
			return math.max(0, arg_16_0.endTime - ove_0_6.TickCount() - ObjectCache.gamePing)
		end

		local slot_16_0 = arg_16_0:GetCurrentSpellPosition(true, ObjectCache.gamePing)
		local slot_16_1 = 0

		if ObjectCache.menuCache.cache.delayEvade:get() and arg_16_0.endDelay and not arg_16_0.spellObject and arg_16_0.endDelay - ove_0_6.TickCount() > 0 then
			slot_16_1 = math.max(0, arg_16_0.endDelay - ove_0_6.TickCount())
		end

		return slot_16_1 + 1000 * slot_16_0:dist(arg_16_1) / arg_16_0.info.projectileSpeed
	elseif arg_16_0.spellType == SpellType.Cone then
		if not arg_16_0.info.projectileSpeed or arg_16_0.info.projectileSpeed == math.huge then
			return math.max(0, arg_16_0.endTime - ove_0_6.TickCount() - ObjectCache.gamePing)
		end

		local slot_16_2 = 0

		if ObjectCache.menuCache.cache.delayEvade:get() and arg_16_0.endDelay and not arg_16_0.spellObject and arg_16_0.endDelay - ove_0_6.TickCount() > 0 then
			slot_16_2 = math.max(0, arg_16_0.endDelay - ove_0_6.TickCount())
		end

		return slot_16_2 + 1000 * arg_16_0:GetCurrentSpellPosition(true, ObjectCache.gamePing):dist(arg_16_1) / arg_16_0.info.projectileSpeed
	elseif arg_16_0.spellType == SpellType.Circular then
		return math.max(0, arg_16_0.endTime - ove_0_6.TickCount() - ObjectCache.gamePing)
	end

	return math.huge
end

function ove_0_14.CanHeroEvade(arg_17_0, arg_17_1, arg_17_2, arg_17_3)
	-- function 17
	local slot_17_0 = to2D(arg_17_1.pos)
	local slot_17_1 = 0
	local slot_17_2 = 0
	local slot_17_3 = arg_17_1.moveSpeed

	arg_17_2 = arg_17_2 or 0
	arg_17_3 = arg_17_3 or 0

	local slot_17_4 = ObjectCache.menuCache.cache.ExtraAvoidDistance:get()
	local slot_17_5, slot_17_6 = Evade.EvadeSpell.ShouldUseMovementBuff(arg_17_0)

	if slot_17_5 and slot_17_6 then
		slot_17_3 = slot_17_3 + slot_17_3 * slot_17_6.speedArray[player:SpellSlot(slot_17_6.spellKey):Level()] / 100
		arg_17_2 = arg_17_2 + (slot_17_6.spellDelay > 50 and slot_17_6.spellDelay or 0) + ObjectCache.gamePing
	end

	if arg_17_0.spellType == SpellType.Line then
		local slot_17_7, slot_17_8, slot_17_9 = ove_0_7.ProjectOn(slot_17_0, arg_17_0.startPos, arg_17_0.endPos)
		local slot_17_10 = slot_17_8

		slot_17_1 = 1000 * (arg_17_0.radius - slot_17_0:dist(slot_17_10) + arg_17_1.boundingRadius) / slot_17_3
		slot_17_2 = arg_17_0:GetSpellHitTime(slot_17_10)
	elseif arg_17_0.spellType == SpellType.Circular then
		slot_17_1 = 1000 * (arg_17_0.radius - slot_17_0:dist(arg_17_0.endPos)) / slot_17_3
		slot_17_2 = arg_17_0:GetSpellHitTime(slot_17_0)
	elseif arg_17_0.spellType == SpellType.Cone then
		local slot_17_11 = {}
		local slot_17_12, slot_17_13, slot_17_14 = ove_0_7.ProjectOn(slot_17_0, arg_17_0.cnStart, arg_17_0.cnLeft)
		local slot_17_15, slot_17_16, slot_17_17 = ove_0_7.ProjectOn(slot_17_0, arg_17_0.cnLeft, arg_17_0.cnRight)
		local slot_17_18, slot_17_19, slot_17_20 = ove_0_7.ProjectOn(slot_17_0, arg_17_0.cnRight, arg_17_0.cnStart)

		table.insert(slot_17_11, slot_17_13)
		table.insert(slot_17_11, slot_17_16)
		table.insert(slot_17_11, slot_17_19)
		table.sort(slot_17_11, function(arg_18_0, arg_18_1)
			-- function 18
			return arg_18_0:dist(slot_17_0) < arg_18_1:dist(slot_17_0)
		end)

		local slot_17_21 = slot_17_11[1]

		slot_17_1 = 1000 * (arg_17_0.info.range / 2 - slot_17_0:dist(slot_17_21) + arg_17_1.boundingRadius + slot_17_4) / slot_17_3
		slot_17_2 = arg_17_0:GetSpellHitTime(slot_17_0)
	end

	return slot_17_2 - arg_17_2 > slot_17_1 + arg_17_3, slot_17_1, slot_17_2
end

function ove_0_14.GetSpellEndPosition(arg_19_0)
	-- function 19
	return arg_19_0.predictedEndPos and arg_19_0.predictedEndPos or arg_19_0.endPos
end

function ove_0_14.UpdateStrips(arg_20_0)
	-- function 20
	arg_20_0.g_strips = {}

	local slot_20_0 = arg_20_0.PathPolygon:ChildCount() + 1
	local slot_20_1 = vec3.array(slot_20_0)

	for iter_20_0 = 0, arg_20_0.PathPolygon:ChildCount() - 1 do
		local slot_20_2 = arg_20_0.PathPolygon:Childs(iter_20_0)
		local slot_20_3 = 0

		slot_20_1[iter_20_0] = vec3(math.ceil(slot_20_2.x), math.ceil(slot_20_3), math.ceil(slot_20_2.y))

		if iter_20_0 == arg_20_0.PathPolygon:ChildCount() - 1 then
			local slot_20_4 = arg_20_0.PathPolygon:Childs(0)
			local slot_20_5 = 0
			local slot_20_6 = vec3(math.ceil(slot_20_4.x), math.ceil(slot_20_5), math.ceil(slot_20_4.y))

			slot_20_1[iter_20_0 + 1] = slot_20_6
		end
	end

	table.insert(arg_20_0.g_strips, {
		vec = slot_20_1,
		n = slot_20_0
	})
end

function ove_0_14.UpdatePolygon(arg_21_0)
	-- function 21
	local slot_21_0 = player.boundingRadius / 2 + 15

	if arg_21_0.spellType == SpellType.Line then
		if not arg_21_0.line then
			arg_21_0.line = ove_0_8(arg_21_0.currentSpellPosition, arg_21_0.endPos, arg_21_0.radius + slot_21_0)
		end

		arg_21_0.line.RStart = arg_21_0.currentSpellPosition
		arg_21_0.Polygon = arg_21_0.line:ToPolygon()
		arg_21_0.PathPolygon = arg_21_0.line:ToPolygon(player.pathfindingCollisionRadius + 5)

		arg_21_0:UpdateStrips()
	elseif arg_21_0.spellType == SpellType.Circular then
		if not arg_21_0.circle then
			arg_21_0.circle = ove_0_10(arg_21_0.endPos, arg_21_0.radius + 15)
			arg_21_0.radius2 = arg_21_0.circle.radius
		end

		arg_21_0.Polygon = arg_21_0.circle:ToPolygon()
		arg_21_0.PathPolygon = arg_21_0.circle:ToPolygon(player.pathfindingCollisionRadius + 5)
	elseif arg_21_0.spellType == SpellType.Cone then
		if not arg_21_0.cone then
			arg_21_0.cone = ove_0_12(arg_21_0.startPos, arg_21_0.endPos - arg_21_0.startPos, arg_21_0.info.angle + slot_21_0 / 2, arg_21_0.info.range + slot_21_0, player, arg_21_0.info.P1)
			arg_21_0.radius2 = arg_21_0.radius
		end

		arg_21_0.cone.Center = arg_21_0.startPos
		arg_21_0.cone.Direction = arg_21_0.endPos - arg_21_0.startPos
		arg_21_0.Polygon = arg_21_0.cone:ToPolygon()
		arg_21_0.PathPolygon = arg_21_0.cone:ToPolygon(player.pathfindingCollisionRadius + 5)
	elseif arg_21_0.spellType == SpellType.Ring then
		if not arg_21_0.ring then
			arg_21_0.ring = ove_0_9(arg_21_0.endPos, arg_21_0.info.ringInRadius, arg_21_0.info.ringOutRadius)
		end

		arg_21_0.Polygon = arg_21_0.ring:ToPolygon()
		arg_21_0.PathPolygon = arg_21_0.ring:ToPolygon(player.pathfindingCollisionRadius + 5)
	elseif arg_21_0.spellType == SpellType.Arc and not arg_21_0.arc then
		-- block empty
	end
end

function ove_0_14.Update(arg_22_0)
	-- function 22
	if not arg_22_0 then
		return
	end

	arg_22_0.currentSpellPosition = arg_22_0:GetCurrentSpellPosition(nil, ObjectCache.gamePing)
	arg_22_0.currentNegativePosition = arg_22_0.currentSpellPosition
	arg_22_0.dangerlevel = arg_22_0:GetDangerLevel()

	if arg_22_0.info.MissileFollowsUnit and arg_22_0.hero then
		arg_22_0.endPos = to2D(arg_22_0.hero.path.serverPos)
	end

	if arg_22_0.info.FollowCasterDirection and arg_22_0.hero then
		local slot_22_0 = preds.core.get_pos_after_time(arg_22_0.hero, 0.3)

		slot_22_0 = slot_22_0 and to2D(slot_22_0)
		arg_22_0.startPos = slot_22_0 or arg_22_0.hero.pos2D
		arg_22_0.endPos = arg_22_0.startPos + arg_22_0.direction * arg_22_0.info.range
		arg_22_0.direction = (arg_22_0.endPos - arg_22_0.startPos):norm()
	end

	arg_22_0:UpdatePolygon()
end

function ove_0_14.GetCurrentSpellPosition(arg_23_0, arg_23_1, arg_23_2, arg_23_3)
	-- function 23
	arg_23_2 = arg_23_2 or 0
	arg_23_3 = arg_23_3 or 0

	local slot_23_0 = arg_23_0.startPos
	local slot_23_1 = slot_23_0:dist(arg_23_0.endPos)

	if arg_23_0.spellObject and not arg_23_0.spellObject.isDead and arg_23_0:hasProjectile() then
		slot_23_0 = to2D(arg_23_0.spellObject.pos)
	elseif arg_23_0.spellType == SpellType.Line or arg_23_0.spellType == SpellType.Arc then
		local slot_23_2 = ove_0_6.TickCount() - arg_23_0.startTime - arg_23_0.info.spellDelay

		if arg_23_0.info.projectileSpeed == math.huge then
			return arg_23_0.startPos
		end

		if slot_23_2 >= 0 or arg_23_1 then
			slot_23_0 = arg_23_0.startPos + arg_23_0.direction * arg_23_0.info.projectileSpeed * (slot_23_2 / 1000)
		end
	elseif arg_23_0.spellType == SpellType.Circular or arg_23_0.spellType == SpellType.Cone then
		slot_23_0 = arg_23_0.endPos
	end

	if tonumber(arg_23_2) > 0 and arg_23_0.info.projectileSpeed and arg_23_0.spellType and arg_23_0.info.projectileSpeed ~= math.huge then
		slot_23_0 = slot_23_0 + arg_23_0.direction * arg_23_0.info.projectileSpeed * (arg_23_2 / 1000)
	end

	if arg_23_3 > 0 and arg_23_0.info.projectileSpeed ~= math.huge and arg_23_0.spellType == SpellType.Line then
		slot_23_0 = slot_23_0 + spell.direction * arg_23_3
	end

	return slot_23_0
end

function ove_0_14.LineIntersectLinearSpell(arg_24_0, arg_24_1, arg_24_2)
	-- function 24
	local slot_24_0 = ObjectCache.myHeroCache.boundingRadius
	local slot_24_1 = arg_24_0.direction
	local slot_24_2 = perp1(arg_24_0.direction)
	local slot_24_3 = arg_24_0.radius
	local slot_24_4 = arg_24_0.currentSpellPosition
	local slot_24_5 = arg_24_0:GetSpellEndPosition()
	local slot_24_6 = slot_24_4 + slot_24_2 * (slot_24_3 + slot_24_0)
	local slot_24_7 = slot_24_4 - slot_24_2 * (slot_24_3 + slot_24_0)
	local slot_24_8 = slot_24_5 + slot_24_2 * (slot_24_3 + slot_24_0)
	local slot_24_9 = slot_24_5 - slot_24_2 * (slot_24_3 + slot_24_0)
	local slot_24_10 = ove_0_6.CheckLineIntersection(arg_24_1, arg_24_2, slot_24_6, slot_24_7)
	local slot_24_11 = ove_0_6.CheckLineIntersection(arg_24_1, arg_24_2, slot_24_8, slot_24_9)
	local slot_24_12 = ove_0_6.CheckLineIntersection(arg_24_1, arg_24_2, slot_24_6, slot_24_8)
	local slot_24_13 = ove_0_6.CheckLineIntersection(arg_24_1, arg_24_2, slot_24_7, slot_24_9)

	if slot_24_10 or slot_24_11 or slot_24_12 or slot_24_13 then
		return true
	end

	return false
end

function ove_0_14.LineIntersectLinearSpellEx(arg_25_0, arg_25_1, arg_25_2, arg_25_3)
	-- function 25
	local slot_25_0 = ObjectCache.myHeroCache.boundingRadius
	local slot_25_1 = arg_25_0.direction
	local slot_25_2 = perp1(arg_25_0.direction)
	local slot_25_3 = arg_25_0.radius
	local slot_25_4 = arg_25_0.currentSpellPosition - slot_25_1 * slot_25_0
	local slot_25_5 = arg_25_0:GetSpellEndPosition() + slot_25_1 * slot_25_0
	local slot_25_6 = slot_25_4 + slot_25_2 * (slot_25_3 + slot_25_0)
	local slot_25_7 = slot_25_4 - slot_25_2 * (slot_25_3 + slot_25_0)
	local slot_25_8 = slot_25_5 + slot_25_2 * (slot_25_3 + slot_25_0)
	local slot_25_9 = slot_25_5 - slot_25_2 * (slot_25_3 + slot_25_0)
	local slot_25_10 = {}
	local slot_25_11 = ObjectCache.myHeroCache.currentPosition
	local slot_25_12, slot_25_13 = ove_0_6.CheckLineIntersection(arg_25_1, arg_25_2, slot_25_6, slot_25_7)
	local slot_25_14, slot_25_15 = ove_0_6.CheckLineIntersection(arg_25_1, arg_25_2, slot_25_8, slot_25_9)
	local slot_25_16, slot_25_17 = ove_0_6.CheckLineIntersection(arg_25_1, arg_25_2, slot_25_6, slot_25_8)
	local slot_25_18, slot_25_19 = ove_0_6.CheckLineIntersection(arg_25_1, arg_25_2, slot_25_7, slot_25_9)

	if slot_25_12 then
		table.insert(slot_25_10, {
			Intersects = slot_25_12,
			Point = slot_25_13
		})
	end

	if slot_25_14 then
		table.insert(slot_25_10, {
			Intersects = slot_25_14,
			Point = slot_25_15
		})
	end

	if slot_25_16 then
		table.insert(slot_25_10, {
			Intersects = slot_25_16,
			Point = slot_25_17
		})
	end

	if slot_25_18 then
		table.insert(slot_25_10, {
			Intersects = slot_25_18,
			Point = slot_25_19
		})
	end

	if #slot_25_10 > 0 then
		table.sort(slot_25_10, function(arg_26_0, arg_26_1)
			-- function 26
			return arg_26_0.Point:dist(slot_25_11) < arg_26_1.Point:dist(slot_25_11)
		end)

		arg_25_3 = slot_25_10[1].Point

		return true, arg_25_3
	end

	return false, nil
end

GridSize = 15

local ove_0_15 = Class(function(arg_27_0, arg_27_1, arg_27_2, arg_27_3, arg_27_4)
	-- function 27
	arg_27_0.Distance = arg_27_1
	arg_27_0.ComingFrom = arg_27_4
	arg_27_0.Valid = arg_27_3 ~= nil
	arg_27_0.Point = arg_27_3 + GridSize * (arg_27_4 - arg_27_3):norm()
	arg_27_0.Time = arg_27_2
end)

function ove_0_14.IsSafeInPath(arg_28_0, arg_28_1, arg_28_2, arg_28_3, arg_28_4, arg_28_5)
	-- function 28
	arg_28_2 = arg_28_2 or 0

	if arg_28_2 == 0 then
		arg_28_2 = arg_28_2 + network.latency * 500
	end

	local slot_28_0 = 0

	arg_28_3 = arg_28_3 or player.moveSpeed
	arg_28_4 = arg_28_4 or 0
	arg_28_5 = arg_28_5 or player

	local slot_28_1 = {}

	for iter_28_0 = 1, #arg_28_1 - 1 do
		local slot_28_2 = arg_28_1[iter_28_0]
		local slot_28_3 = arg_28_1[iter_28_0 + 1]
		local slot_28_4 = {}

		if arg_28_0.spellType == SpellType.Circular then
			local slot_28_5 = (slot_28_3 - slot_28_2):norm()
			local slot_28_6, slot_28_7, slot_28_8 = ove_0_6.GetCollisionDistanceEx(slot_28_2, slot_28_5 * arg_28_3, 1, arg_28_0.endPos, vec2(0, 0), arg_28_0.circle.radius + arg_28_5.pathfindingCollisionRadius)
			local slot_28_9, slot_28_10, slot_28_11 = ove_0_7.ProjectOn(slot_28_7, slot_28_2, slot_28_3)

			if slot_28_9 and slot_28_6 ~= math.huge then
				local slot_28_12 = slot_28_10
				local slot_28_13 = slot_28_0 + slot_28_12:dist(slot_28_2)
				local slot_28_14 = (slot_28_0 + slot_28_12:dist(slot_28_2)) * 1000 / arg_28_3
				local slot_28_15 = ove_0_15(slot_28_13, slot_28_14, slot_28_12, slot_28_2)

				table.insert(slot_28_4, slot_28_15)
			elseif arg_28_0.PathPolygon and arg_28_0.PathPolygon:Contains(slot_28_3) == 1 then
				return {
					IsSafe = false,
					debug = "info1",
					Intersection = slot_28_3
				}
			end
		else
			for iter_28_1 = 0, arg_28_0.Polygon:ChildCount() - 1 do
				local slot_28_16 = arg_28_0.Polygon:Childs(iter_28_1)
				local slot_28_17 = arg_28_0.Polygon:Childs(iter_28_1 == arg_28_0.Polygon:ChildCount() - 1 and 0 or iter_28_1 + 1)
				local slot_28_18 = ove_0_7.Intersection(slot_28_2, slot_28_3, slot_28_16, slot_28_17) or {}
				local slot_28_19 = slot_28_18[1]
				local slot_28_20 = slot_28_18[2]

				if slot_28_19 then
					local slot_28_21 = slot_28_0 + slot_28_20:dist(slot_28_2)
					local slot_28_22 = (slot_28_0 + slot_28_20:dist(slot_28_2)) * 1000 / arg_28_3
					local slot_28_23 = ove_0_15(slot_28_21, slot_28_22, slot_28_20, slot_28_2)

					slot_28_23.DistanceMouse = slot_28_20:dist(mousePos2D)

					table.insert(slot_28_4, slot_28_23)
				end
			end
		end

		if #slot_28_4 > 0 then
			table.sort(slot_28_4, function(arg_29_0, arg_29_1)
				-- function 29
				return arg_29_0.Distance < arg_29_1.Distance
			end)

			for iter_28_2 = 1, #slot_28_4 do
				table.insert(slot_28_1, slot_28_4[iter_28_2])
			end
		end

		slot_28_0 = slot_28_0 + slot_28_2:dist(slot_28_3)
	end

	local slot_28_24 = arg_28_0.info.projectileSpeed
	local slot_28_25 = false

	if arg_28_0.spellObject and not arg_28_0.spellObject.isDead and arg_28_0.spellObject.speed and arg_28_0.spellObject.speed > 0 then
		slot_28_24 = arg_28_0.spellObject.speed
		slot_28_25 = true
	end

	if arg_28_0.spellType ~= SpellType.Circular and arg_28_0.PathPolygon:Contains(ObjectCache.myHeroCache.serverPos2D) ~= 1 then
		if #slot_28_1 == 0 then
			return {
				IsSafe = true,
				debug = "info3"
			}
		end

		if arg_28_0.info.DontCross or arg_28_0.info.hasTrap then
			return {
				IsSafe = false,
				debug = "info2",
				Intersection = slot_28_1[1]
			}
		end

		for iter_28_3 = 1, #slot_28_1 do
			local slot_28_26 = slot_28_1[iter_28_3]
			local slot_28_27, slot_28_28, slot_28_29 = ove_0_7.ProjectOn(slot_28_26.Point, arg_28_0.startPos, arg_28_0.endPos)
			local slot_28_30 = slot_28_28

			if iter_28_3 == #slot_28_1 then
				local slot_28_31 = arg_28_0:GetCurrentSpellPosition(nil, slot_28_26.Time - arg_28_2)
				local slot_28_32 = arg_28_0.endPos:dist(slot_28_31) + 100 <= arg_28_0.endPos:dist(ObjectCache.myHeroCache.serverPos2D) and slot_28_24 > arg_28_5.moveSpeed
				local slot_28_33

				slot_28_33 = arg_28_0.endPos:dist(slot_28_31) + 100 <= arg_28_0.endPos:dist(slot_28_30) and slot_28_24 > arg_28_5.moveSpeed

				return {
					debug = "info4",
					IsSafe = slot_28_32,
					Intersection = slot_28_1[1]
				}
			end

			local slot_28_34 = slot_28_1[iter_28_3 + 1]
			local slot_28_35, slot_28_36, slot_28_37 = ove_0_7.ProjectOn(slot_28_34.Point, arg_28_0.startPos, arg_28_0.endPos)
			local slot_28_38 = slot_28_36
			local slot_28_39 = arg_28_0:GetCurrentSpellPosition(nil, slot_28_26.Time - arg_28_2)
			local slot_28_40 = arg_28_0:GetCurrentSpellPosition(nil, slot_28_26.Time + arg_28_2)

			if slot_28_39:dist(arg_28_0.endPos) + 50 > slot_28_30:dist(arg_28_0.endPos) and slot_28_40:dist(arg_28_0.endPos) <= slot_28_38:dist(arg_28_0.endPos) then
				return {
					IsSafe = false,
					debug = "info5",
					Intersection = slot_28_1[1]
				}
			end
		end

		return {
			IsSafe = true,
			debug = "info6",
			Intersection = slot_28_1[1]
		}
	end

	local slot_28_41 = arg_28_0:GetCurrentSpellPosition(nil, arg_28_2)
	local slot_28_42 = arg_28_0:GetSpellEndPosition()
	local slot_28_43 = slot_28_25 and 0 or arg_28_0.info.spellDelay

	if 2 == 1 then
		local slot_28_44 = arg_28_1[1]
		local slot_28_45 = arg_28_1[#arg_28_1]
		local slot_28_46 = (slot_28_45 - slot_28_44):norm()

		if arg_28_0.info.projectileSpeed == math.huge then
			local slot_28_47 = (arg_28_0.endTime - ove_0_6.TickCount()) / 1000

			if arg_28_0.info.spellName == "VeigarEventHorizon" then
				slot_28_47 = (arg_28_0.startTime + arg_28_0.info.spellDelay - ove_0_6.TickCount()) / 1000

				local slot_28_48 = slot_28_44 + slot_28_46 * (slot_28_47 * arg_28_3 - 10)

				if slot_28_47 > 0 then
					if arg_28_0.endPos:dist(slot_28_48) <= arg_28_0.radius then
						return {
							IsSafe = false,
							debug = "VeigarEventHorizon info9",
							Intersection = slot_28_1[1]
						}
					else
						return {
							IsSafe = true,
							debug = "VeigarEventHorizon info10",
							Intersection = slot_28_1[1]
						}
					end
				end
			end

			if #slot_28_1 > 0 and arg_28_0.info.hasTrap then
				return {
					IsSafe = false,
					debug = "Trap info9",
					Intersection = slot_28_1[1]
				}
			end

			if arg_28_0.PathPolygon:Contains(slot_28_45) == 1 then
				return {
					IsSafe = false,
					debug = "new info9",
					Intersection = slot_28_1[1]
				}
			end

			local slot_28_49 = slot_28_44 + slot_28_46 * (slot_28_47 * arg_28_3 - 10)

			if arg_28_0.PathPolygon:Contains(slot_28_49:to3D(player.y)) == 1 then
				return {
					IsSafe = false,
					debug = "new info9",
					Intersection = slot_28_1[1]
				}
			end

			return {
				IsSafe = true,
				debug = "new info10",
				Intersection = slot_28_1[1]
			}
		else
			local slot_28_50 = ove_0_6.ExtendDir(slot_28_45, slot_28_46, ObjectCache.myHeroCache.boundingRadius + 10 + arg_28_3 * arg_28_2 / 1000)
			local slot_28_51, slot_28_52, slot_28_53 = ove_0_6.GetCollisionDistanceEx(slot_28_44, slot_28_46 * arg_28_3, ObjectCache.myHeroCache.boundingRadius + 10, slot_28_41, arg_28_0.direction * arg_28_0.info.projectileSpeed, arg_28_0.radius + ObjectCache.menuCache.cache.ExtraEvadeDistance:get())
			local slot_28_54, slot_28_55, slot_28_56 = ove_0_7.ProjectOn(slot_28_52, slot_28_44, slot_28_50)
			local slot_28_57, slot_28_58, slot_28_59 = ove_0_7.ProjectOn(slot_28_53, slot_28_41, slot_28_42)

			if slot_28_54 and slot_28_57 and slot_28_51 ~= math.huge then
				return {
					IsSafe = false,
					debug = "new info8",
					Intersection = slot_28_1[1]
				}
			end

			local slot_28_60, slot_28_61, slot_28_62 = ove_0_6.CPAPointsEx(slot_28_44, slot_28_46 * arg_28_3, slot_28_41, arg_28_0.direction * arg_28_0.info.projectileSpeed, slot_28_45, slot_28_42)
			local slot_28_63, slot_28_64, slot_28_65 = ove_0_7.ProjectOn(slot_28_61, slot_28_44, slot_28_50)
			local slot_28_66, slot_28_67, slot_28_68 = ove_0_7.ProjectOn(slot_28_62, slot_28_41, slot_28_42)
			local slot_28_69 = ObjectCache.myHeroCache.boundingRadius + arg_28_0.radius + 100

			if slot_28_63 and slot_28_66 and slot_28_60 - slot_28_69 <= 0 then
				return {
					IsSafe = false,
					debug = "new info9",
					Intersection = slot_28_1[1]
				}
			end
		end

		return {
			IsSafe = true,
			debug = "new info10",
			Intersection = slot_28_1[1]
		}
	end

	if #slot_28_1 == 0 then
		-- block empty
	end

	local slot_28_70 = (arg_28_0.info.DontAddExtraDuration and 0 or arg_28_0.info.ExtraDuration and arg_28_0.info.ExtraDuration or 0) + slot_28_43 + 1000 * slot_28_41:dist(slot_28_42) / slot_28_24

	if slot_28_24 == math.huge then
		slot_28_70 = arg_28_0.endTime - ove_0_6.TickCount()
	elseif arg_28_0.info.hasTrap or arg_28_0.info.range > 10000 then
		slot_28_70 = 0
	end

	local slot_28_71 = math.max(0, slot_28_70)

	if arg_28_0.info.spellName == "VeigarEventHorizon" then
		slot_28_71 = (arg_28_0.startTime + arg_28_0.info.spellDelay - ove_0_6.TickCount()) / 1000

		local slot_28_72 = arg_28_1[1]
		local slot_28_73 = slot_28_72 + (arg_28_1[#arg_28_1] - slot_28_72):norm() * (slot_28_71 * arg_28_3 - 10)

		if slot_28_71 > 0 then
			if arg_28_0.endPos:dist(slot_28_73) <= arg_28_0.radius then
				return {
					IsSafe = false,
					debug = "VeigarEventHorizon info9",
					Intersection = slot_28_1[1]
				}
			else
				return {
					IsSafe = true,
					debug = "VeigarEventHorizon info10",
					Intersection = slot_28_1[1]
				}
			end
		end
	end

	local slot_28_74 = ove_0_7.PositionAfter(arg_28_1, slot_28_71, arg_28_3, arg_28_4)

	if arg_28_0.PathPolygon:Contains(slot_28_74) == 1 then
		return {
			IsSafe = false,
			debug = "info8",
			Intersection = slot_28_1[1]
		}
	end

	local slot_28_75 = ove_0_7.PositionAfter(arg_28_1, slot_28_71, arg_28_3, arg_28_2)

	if arg_28_0.PathPolygon:Contains(slot_28_75) == 1 then
		return {
			IsSafe = false,
			debug = "info9",
			Intersection = slot_28_1[1]
		}
	end

	return {
		IsSafe = true,
		debug = "info10",
		Intersection = slot_28_1[1]
	}
end

return ove_0_14

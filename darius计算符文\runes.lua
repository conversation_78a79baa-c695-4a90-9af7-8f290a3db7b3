-- Darius 符文伤害计算模块
-- 支持主要的伤害符文：公理秘术、电刑、黑暗收割、征服者

local slot_rune_menu = module.load(header.id, "darius/menu")

-- 获取符文信息的辅助函数
local function GetRuneInfo()
    local runeInfo = {
        arcaneComet = false,
        electrocute = false,
        darkHarvest = false,
        conqueror = false,
        harvestStacks = 0,
        conquerorStacks = 0
    }
    
    -- 检查玩家的符文配置
    -- 这里可以通过检查玩家的buff或其他方式来确定符文
    -- 暂时使用菜单设置作为开关
    
    if slot_rune_menu and slot_rune_menu.runes then
        runeInfo.arcaneComet = slot_rune_menu.runes.arcanecomet and slot_rune_menu.runes.arcanecomet:get()
        runeInfo.electrocute = slot_rune_menu.runes.electrocute and slot_rune_menu.runes.electrocute:get()
        runeInfo.darkHarvest = slot_rune_menu.runes.darkharvest and slot_rune_menu.runes.darkharvest:get()
        runeInfo.conqueror = slot_rune_menu.runes.conqueror and slot_rune_menu.runes.conqueror:get()
    end
    
    -- 尝试从buff获取收割层数
    if player.buff.darkharvest then
        runeInfo.harvestStacks = player.buff.darkharvest.stacks or 0
    else
        runeInfo.harvestStacks = 10 -- 默认假设值
    end
    
    -- 尝试从buff获取征服者层数
    if player.buff.conqueror then
        runeInfo.conquerorStacks = player.buff.conqueror.stacks or 0
    else
        runeInfo.conquerorStacks = 12 -- 默认假设满层
    end
    
    return runeInfo
end

-- 公理秘术伤害计算
local function CalculateArcaneCometDamage(target)
    if not target then return 0 end

    local playerLevel = player.levelRef or 1
    local playerAP = player.flatMagicDamageMod or 0
    local playerAD = player.flatPhysicalDamageMod or 0
    local targetMR = target.magicResist or 30
    local playerMagicPen = player.percentMagicPenetration or 0
    local playerFlatMagicPen = player.flatMagicPenetration or 0

    local baseDamage = 30 + (70 * (playerLevel - 1) / 17)
    local apRatio = playerAP * 0.2
    local adRatio = playerAD * 0.35
    local totalDamage = baseDamage + apRatio + adRatio

    -- 魔法伤害减免
    local magicReduction = 100 / (100 + targetMR * (1 - playerMagicPen) - playerFlatMagicPen)
    return totalDamage * magicReduction
end

-- 电刑伤害计算
local function CalculateElectrocuteDamage(target)
    if not target then return 0 end

    local playerLevel = player.levelRef or 1
    local playerAP = player.flatMagicDamageMod or 0
    local playerAD = player.flatPhysicalDamageMod or 0
    local targetMR = target.magicResist or 30
    local playerMagicPen = player.percentMagicPenetration or 0
    local playerFlatMagicPen = player.flatMagicPenetration or 0

    local baseDamage = 30 + (150 * (playerLevel - 1) / 17)
    local apRatio = playerAP * 0.25
    local adRatio = playerAD * 0.4
    local totalDamage = baseDamage + apRatio + adRatio

    -- 魔法伤害减免
    local magicReduction = 100 / (100 + targetMR * (1 - playerMagicPen) - playerFlatMagicPen)
    return totalDamage * magicReduction
end

-- 黑暗收割伤害计算
local function CalculateDarkHarvestDamage(target, stacks)
    if not target then return 0 end

    local playerLevel = player.levelRef or 1
    local playerAP = player.flatMagicDamageMod or 0
    local playerAD = player.flatPhysicalDamageMod or 0
    local targetMR = target.magicResist or 30
    local playerMagicPen = player.percentMagicPenetration or 0
    local playerFlatMagicPen = player.flatMagicPenetration or 0
    local harvestStacks = stacks or 0

    local baseDamage = 20 + (40 * (playerLevel - 1) / 17)
    local apRatio = playerAP * 0.25
    local adRatio = playerAD * 0.15
    local stackDamage = harvestStacks * 5
    local totalDamage = baseDamage + apRatio + adRatio + stackDamage

    -- 魔法伤害减免
    local magicReduction = 100 / (100 + targetMR * (1 - playerMagicPen) - playerFlatMagicPen)
    return totalDamage * magicReduction
end

-- 征服者伤害计算
local function CalculateConquerorDamage(target, stacks)
    if not target then return 0 end

    local playerLevel = player.levelRef or 1
    local playerAD = player.flatPhysicalDamageMod or 0
    local conquerorStacks = stacks or 0

    -- 征服者提供适应性伤害，满层时转换部分伤害为真实伤害
    local perStackDamage = 1.7 + (1.3 * (playerLevel - 1) / 17)
    local totalAdaptiveDamage = playerAD * perStackDamage * conquerorStacks

    -- 满层时，10%的伤害转换为真实伤害
    local trueDamagePercent = conquerorStacks >= 12 and 0.1 or 0
    local trueDamage = totalAdaptiveDamage * trueDamagePercent

    return trueDamage
end

-- 主要的符文伤害计算函数
local function CalculateRuneDamage(target)
    local runeInfo = GetRuneInfo()
    local totalRuneDamage = 0
    
    -- 公理秘术
    if runeInfo.arcaneComet then
        totalRuneDamage = totalRuneDamage + CalculateArcaneCometDamage(target)
    end
    
    -- 电刑
    if runeInfo.electrocute then
        totalRuneDamage = totalRuneDamage + CalculateElectrocuteDamage(target)
    end
    
    -- 黑暗收割
    if runeInfo.darkHarvest then
        totalRuneDamage = totalRuneDamage + CalculateDarkHarvestDamage(target, runeInfo.harvestStacks)
    end
    
    -- 征服者
    if runeInfo.conqueror then
        totalRuneDamage = totalRuneDamage + CalculateConquerorDamage(target, runeInfo.conquerorStacks)
    end
    
    return totalRuneDamage
end

-- 获取符文伤害详细信息（用于显示）
local function GetRuneDamageDetails(target)
    local runeInfo = GetRuneInfo()
    local details = {}
    
    if runeInfo.arcaneComet then
        details.arcaneComet = CalculateArcaneCometDamage(target)
    end
    
    if runeInfo.electrocute then
        details.electrocute = CalculateElectrocuteDamage(target)
    end
    
    if runeInfo.darkHarvest then
        details.darkHarvest = CalculateDarkHarvestDamage(target, runeInfo.harvestStacks)
        details.harvestStacks = runeInfo.harvestStacks
    end
    
    if runeInfo.conqueror then
        details.conqueror = CalculateConquerorDamage(target, runeInfo.conquerorStacks)
        details.conquerorStacks = runeInfo.conquerorStacks
    end
    
    details.total = CalculateRuneDamage(target)
    
    return details
end

-- 检查符文是否可用（冷却时间等）
local function IsRuneAvailable(runeName)
    -- 这里可以添加符文冷却时间的检查
    -- 目前简单返回true
    return true
end

return {
    CalculateRuneDamage = CalculateRuneDamage,
    GetRuneDamageDetails = GetRuneDamageDetails,
    GetRuneInfo = GetRuneInfo,
    IsRuneAvailable = IsRuneAvailable,
    CalculateArcaneCometDamage = CalculateArcaneCometDamage,
    CalculateElectrocuteDamage = CalculateElectrocuteDamage,
    CalculateDarkHarvestDamage = CalculateDarkHarvestDamage,
    CalculateConquerorDamage = CalculateConquerorDamage
}

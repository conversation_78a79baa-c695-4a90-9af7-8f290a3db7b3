local supported_champion = {
   ['<PERSON>relionSol'] = true,
   ['<PERSON><PERSON>real'] = true,
   ['<PERSON>'] = true,
   ['Riven'] = true,
   ['<PERSON><PERSON><PERSON>'] = true,
   ['<PERSON><PERSON><PERSON>'] = true,
   --['<PERSON><PERSON><PERSON>'] = true,
   ['<PERSON><PERSON>yah'] = true,
   ['<PERSON>erath'] = true,
   ['Vi'] = true,
   ['<PERSON><PERSON><PERSON>'] = true,
   ['H<PERSON>'] = true,
   ['<PERSON><PERSON><PERSON>'] = true,
   ['<PERSON><PERSON><PERSON>'] = true,
   ['<PERSON>'] = true,
   ['<PERSON><PERSON><PERSON>'] = true,
   ['Gangplank'] = true,
   ['Draven'] = true,
   --['<PERSON>relia'] = true,
   ['<PERSON>'] = true,
   ['Zed'] = true,
   ['Kled'] = true,
   ['Thresh'] = true,
   ['<PERSON>'] = true,
   ['<PERSON><PERSON>'] = true,
   ['Yone'] = true,
   --['<PERSON>Sin'] = true,
   ['<PERSON>'] = true,
   ['<PERSON><PERSON>'] = true,
   ['<PERSON>ha<PERSON>'] = true,
   ['Renata'] = true,
   --['<PERSON><PERSON>'] = true,
   --viktor
           -- TOP
		["<PERSON>are<PERSON>"] = true,
		["<PERSON><PERSON>"] = true,
        ["<PERSON><PERSON><PERSON>"] = true,
        ["<PERSON>ibear"] = true,
        ["Urgo<PERSON>"] = true,
        ["<PERSON><PERSON>kai<PERSON>"] = true,
        ["<PERSON>"] = true,
        ["<PERSON><PERSON><PERSON>"] = true,
		["Rumble"] = true,
		["Aatrox"] = true,
        ["Dr<PERSON>undo"] = true,
		["Malphite"] = true,
		["<PERSON>ora"] = true,
		["Kennen"] = true,
		["Jax"] = true,
		["Sett"] = true,
		["Gnar"] = true,
		["Singed"] = true,
		["KSante"] = true,
		["Gangplank"] = true,
		["Tryndamere"] = true,
		["Trundle"] = true,
		["Nasus"] = true,
		["Shen"] = true,
		["Renekton"] = true,
		["Illaoi"] = true,
        ["Gwen"] = true,
		["Ornn"] = true,
		["Chogath"] = true,
		["Yorick"] = true,
		["Jayce"] = true,
		["Camille"] = true,
		["TahmKench"] = true,
		["Sion"] = true,
		
		
        -- JUG
        ["JarvanIV"] = true,
        ["RekSai"] = true,
        ["MasterYi"] = true,
        ["Warwick"] = true,
		["Hecarim"] = true,
		["Elise"] = true,
		["Diana"] = true,
		["Nocturne"] = true,
        ["Udyr"] = true,
        ["Kindred"] = true,
        ["Rengar"] = true,
        ["Kayn"] = true,
        ["Skarner"] = true,
        ["Evelynn"] = true,
        ["Shaco"] = true,
        ["LeeSin"] = true,
        ["Khazix"] = true,
        ["Vi"] = true,
		["Graves"] = true,
		["Viego"] = true,
        ["Zac"] = true,
		["Zed"] = true,
		["Ekko"] = true,
		["MonkeyKing"] = true,
		["Lillia"] = true,
		["Pantheon"] = true,
		["Nidalee"] = true,
		["XinZhao"] = true,
		["Shyvana"] = true,
		["Karthus"] = true,
		["Belveth"] = true,
		["Amumu"] = true,
		["Aurora"] = true,
		["Gragas"] = true,
		["Nunu"] = true,
		["Sejuani"] = true,
		["FiddleSticks"] = true,
		["Ivern"] = true,
		["Rammus"] = true,
		["JarvanIV"] = true,		
        ["Akshan"] = true,
		

        -- MID
		["Katarina"] = true,
        ["Leblanc"] = true,
        ["Malzahar"] = true,
        ["Sylas"] = true,
        ["Annie"] = true,
        ["Veigar"] = true,
        ["Kassadin"] = true,
        ["Syndra"] = true,
        ["Heimerdinger"] = true,
        ["Ahri"] = true,
		["Akali"] = true,
		["Ryze"] = true,
        ["Lissandra"] = true,
        ["Talon"] = true,
        ["Anivia"] = true,
        ["Yone"] = true,
		["Naafiri"] = true,
        ["TwistedFate"] = true,
        ["Neeko"] = true,
        ["Cassiopeia"] = true,
        ["Lux"] = true,
        ["Orianna"] = true,
        ["Ziggs"] = true,
		["Azir"] = true,
		["Viktor"] = true,
		["Qiyana"] = true,
		["Fizz"] = true,
		["Zoe"] = true,
		["Yasuo"] = true,
		["Quinn"] = true,
		["Neeko"] = true,
		["Vex"] = true,
		["Vladimir"] = true,
		


        -- ADC
        ["Ashe"] = true,
        ["MissFortune"] = true,
        ["Kaisa"] = true,
        ["Samira"] = true,
        ["Kalista"] = true,
        ["Sivir"] = true,
        ["KogMaw"] = true,
        ["Jhin"] = true,
		["Caitlyn"] = true,
		["Lucian"] = true,
        ["Tristana"] = true,
		["Twitch"] = true,
        ["Corki"] = true,
        ["Varus"] = true,
        ["Xayah"] = true,
		["Aphelios"] = true,
		["Draven"] = true,
		["Smolder"] = true,
		["Kalista"] = true,
		["Nilah"] = true,
		["Zeri"] = true,
		["Jinx"] = true,
		["Vayne"] = true,
        ["Yunara"] = true,
        -- SUP
		["Nautilus"] = true,
        ["Janna"] = true,
        ["Alistar"] = true,
        ["Swain"] = true,
        ["Thresh"] = true,
        ["Zilean"] = true,
        ["Zyra"] = true,
        ["Pyke"] = true,
        ["Brand"] = true,
		["Senna"] = true,
		["Leona"] = true,
        ["Lulu"] = true,
        ["Soraka"] = true,
        ["Karma"] = true,
		["Taric"] = true,
		["Rakan"] = true,
		["Nami"] = true,
		["Rell"] = true,
		["Renata"] = true,
		["Yuumi"] = true,
		["Braum"] = true,
		["Morgana"] = true,
		["Bard"] = true,
		["Seraphine"] = true,
		["Sona"] = true,
		["Velkoz"] = true,
		["Milio"] = true,
		["Maokai"] = true,
		["Poppy"] = true,
		["Poppy"] = true,
		["Ambessa"] = true,
		["Blitzcrank"] = true,
		

}
return {
    type = "Champion",
    id = "Brian",
	icon_url = "https://rs.wzznft.com/i/2024/10/15/xq0l8u.jpg",
    name = "Bs AIO",
    author = "[Bs]",
    description = [[Support 165 champions.{https://t.me/Bs_aio}]],

	 
    --shard_url ='https://raw.githubusercontent.com/Nick-JKSHOP/jdjsfjsdjpfjpdsa/master/NickAIO.shard',
	--shard_url="files/shards/[BrianSharp] Successfull.shard",



   -- riot = true,

	resources = {
			--[["SA/Elise/Q.png",
		"SA/Elise/Q_N.png",
		"SA/Elise/Q1.png",
		"SA/Elise/Q1_N.png",
		"SA/Elise/W.png",
		"SA/Elise/W_N.png",
		"SA/Elise/W1.png",
		"SA/Elise/W1_N.png",
		"SA/Elise/E.png",
		"SA/Elise/E_N.png",
		"SA/Elise/E1.png",
		"SA/Elise/E1_N.png",
		"SA/Elise/R.png",
		"SA/Elise/R_N.png",
		
		"SA/RekSai/Q.png",
		"SA/RekSai/Q_N.png",
		"SA/RekSai/Q1.png",
		"SA/RekSai/Q1_N.png",
		"SA/RekSai/W.png",
		"SA/RekSai/W_N.png",
		"SA/RekSai/W1.png",
		"SA/RekSai/W1_N.png",
		"SA/RekSai/E.png",
		"SA/RekSai/E_N.png",
		"SA/RekSai/E1.png",
		"SA/RekSai/E1_N.png",
		"SA/RekSai/R.png",
		"SA/RekSai/R_N.png",
		
		"SA/Nidalee/Q.png",
		"SA/Nidalee/Q_N.png",
		"SA/Nidalee/Q1.png",
		"SA/Nidalee/Q1_N.png",
		"SA/Nidalee/W.png",
		"SA/Nidalee/W_N.png",
		"SA/Nidalee/W1.png",
		"SA/Nidalee/W1_N.png",
		"SA/Nidalee/E.png",
		"SA/Nidalee/E_N.png",
		"SA/Nidalee/E1.png",
		"SA/Nidalee/E1_N.png",
		"SA/Nidalee/R.png",
		"SA/Nidalee/R_N.png",
		
		--//菜单
		"SA/Menu/active.png",
		"SA/Menu/dev.png",
		"SA/Menu/home.png",
		"SA/Menu/key.png",
		"SA/Menu/orbwallker.png",
		"SA/Menu/pred.png",
		"SA/Menu/select.png",
		"SA/Menu/setting.png",
		"SA/Menu/draw.png",
		"SA/Menu/farm.png",
		--//音效
		--"SA/wav/loadsuccess_cn.wav",
		--"SA/wav/loadsuccess_en.wav",
		"SA/wav/1.wav",
		"SA/wav/2.wav",]]
				--'introc30.png', --league of legends/developer/SHARD_NAME/SPRITE_FOLDER/SPRITE_NAME.png
				'foo.wav',
				'long.png',
				'mao1.png',
				'1.wav',
				'jb.wav',
				'jbb.wav',
				'gx.wav',
		},
  flag = {
    text = 'BS',
    color = { 
      text = 0xff8b0000 ,
      background1 = 0xff1e1e1e,
      background2 = 0xffebebeb,   
    },
  },

load = function()

    return supported_champion[player.charName]
end,
    shard = {

    --main

        "main",
		'dash',
		'common',
		'Curses',
		"kp",
		--'menu',
		---RU
	"bs/main",
	"bs/activator/core",
	"bs/activator/main",
	"bs/aio/main",
	--"bs/check_scripts/main",
	"bs/aio/champion/Hwei",
	"bs/aio/champion/Renata",
	"bs/aio/champion/Graves",
	"bs/aio/champion/Mel",
	"bs/aio/champion/Thresh",
	"bs/aio/champion/Smolder",
	"bs/aio/champion/Yuumi",
	"bs/aio/lib/evade_lib",
	"bs/core/CoreMenu",
	"bs/evade/EvadeSpell/EvadeSpell",
	"bs/evade/EvadeSpell/EvadeSpellDatabase",
	"bs/evade/Utilty/Command",
	"bs/evade/core",
	"bs/evade/main",
	"bs/evade/menu",
	"bs/evade/spell",
	"bs/evade/SpellDatabases",
	"bs/evade/SpellDetector",
	"bs/evade/SpellDrawr",
	"bs/evade/EvadeUtils",
	"bs/evade/objectCache",
    "bs/global/main",
	"bs/global/shadereffect_dx11",
	"bs/Lib/action_lock",
	"bs/Lib/common",
	"bs/Lib/data",
	"bs/Lib/geometry",
	"bs/Lib/main",
	"bs/Lib/box/box",
	"bs/Lib/menu/ColorTools",
	"bs/Lib/ver/AsEncode",
	"bs/Lib/ver/JsonUtility",
	"bs/Lib/ver/MD5",
	"bs/Lib/ver/ver",
	"bs/orb/core",
	"bs/orb/main",
	"bs/orb/menu",	
	"bs/pred/menu",
	"bs/pred/main",
	"bs/pred/core",
	"bs/pred/dash",
	"bs/pred/predction",
	"bs/ts/main",
    "bs/ts/menu",
    "bs/ts/TS",	
		--HG
		"Databases/BaseHealth",
		"Databases/Buffs",
		"Databases/DangerousDatabase",
		"Databases/Dashers",
		"Databases/InterruptiblesDatabase",
		"Databases/Items",
		"Databases/Mobs",
		"Databases/Runes",
		"Databases/SpellDatabase",
		"Databases/SpellMissDatabase",
		"Databases/SpellSlots",
		"Databases/SpellTargetedDatabase",
		--HHheji
		
		"Libraries/Common",
		"Libraries/common1",
		"Libraries/Damage",
		"Libraries/Graphics",
		"Libraries/mathematical",
		--"ui",
		"util",
		--"uii",
		"lib",
		"spell/cast",
	    "spell/data",
		"spell/action_lock",
				  --Libdata
		      "Libdata/Dageme", --伤害库
    "Libdata/lib_Verification", --版本验证
    "Libdata/Pred", --官方预判
    "Libdata/Spell", --法术躲避库
    "Libdata/Utility", --公共库
    "Libdata/AntiGapcloser", --反突进库
    "Libdata/Prediction2", --独立预判
    "Libdata/Dash", --突进库
    "Libdata/Program", --数学计算库
	"Libdata/evade_lib",
	"Libdata/BuffManager",
	"Libdata/Key",
	"Libdata/lib",
	"TS/TargetSelector",
	"AsALL/AsVerification/AsVerification",
	"AsALL/AsUtility/AsUtility",
	"Activator/Activator",
                "AsALL/AsUtility/AsDagemeUtility",
                "AsALL/AsUtility/AsItemUtility",
                "AsALL/AsUtility/AsProgram",
                "AsALL/AsUtility/AsUtility",
                "AsALL/AsUtility/AsBuffManager",
                "AsALL/AsSpell/AsEvadeSpell",
                "AsALL/AsMenu/AsTargetSelectorMenu",
				"AsALL/spell/cast",
				"AsALL/spell/data",
				"Orbwallking/orbwallker",
				--"Orbwallking/Orbwallking",
				"Awareness/cooldown",
				--KR AIO
				"Lib/Draw",
				"Lib/Spell",
				"Lib/Util",
				"Lib/Antigap",
				"Lib/AntiGapcloser",
				"Lib/base64",
				"Lib/BaseUlt",
				"Lib/DamageIndicator",
				"Lib/DelayAction",
				"Lib/evade",
				"Lib/Interrupter",
				"Lib/ItemManager",
				"Lib/json",
				"Lib/auth",
				"Lib/myMsg",
				"Lib/SpellDmg",
				"Lib/Summoners",
				"Lib/auth",
				"Lib/TargetSelector",
				"Lib/TurretDive",
				"Lib/Version",
				"Lib/auth",
				"Lib/MyAuth",
				"Lib/MyCommon",
				--INT
				"common/cc_spells",
				"common/apolo",
				"common/delayAction",
				"common/BuffManager",
				"common/common",
				"common/class",
				"common/MEC",
				"common/recallUlt",
				"common/SpellDatabase",
				"common/spellLib",
				"common/targetedSpells",
				"common/targetSelector",
				"common/damageLib",
				"common/GeometryLib",
				"common/Draw/graphics",
				"common/DashManager/dash",
				"common/turret",
				"common/Computer/computer",
				"common/Notification/notify",
				"common/Notification/table",
				--MyAuth
				-- "Champions/Zed/Zed",
				-- "Champions/Zed/Spells/Q",
				-- "Champions/Zed/Spells/W",
				-- "Champions/Zed/Spells/E",
				-- "Champions/Zed/Spells/R",
				-- "Champions/Zed/Util/Detection",
				-- "Champions/Zed/Util/Draws",
				-- "Champions/Zed/Util/menu",
				-- "Champions/Zed/Util/Damage",
				-- "Champions/Zed/Modes/Combo",
				-- "Champions/Zed/Modes/LaneClear",
				-- "Champions/Zed/Modes/JungleClear",
				-- "Champions/Zed/Modes/LastHit",
				-- "Champions/Zed/Modes/KillSteal",
				-- "Champions/Zed/Modes/Harass",
				-- "Champions/Zed/Modes/Escape",
				-- "Champions/Zed/Modes/GapCloser",
				-- "Champions/Zed/Modes/Misc",
				
					"ezreal/main",
					"ezreal/menu",
					"ezreal/q",
					"ezreal/w",
					"ezreal/r",
					"ezreal/q2",
					"aurelionsol/main",
					"aurelionsol/menu",
					"lvxbot/main",
                    "lvxbot/expert",
					"lvxbot/expert1",
					"lvxbot/cexpert",
                    "lvxbot/ezrealcexpert",
					"lvxbot/Nidaleeexpert",
					"lvxbot/yasuoexpert",
					"lvxbot/jinxexpert",
					"taliyah/main",
					"taliyah/menu",
					"taliyah/q",
					"taliyah/w",
					"taliyah/e",
					"taliyah/r",
					"velkoz/main",
					"velkoz/menu",
					"velkoz/misc",
					"katarina/menu",
					"katarina/main",
					"katarina/damage",
					"katarina/util",
					"briar/main",
					"briar/menu",
					"azir/menu",
					"azir/main",
					"azir/e",
					"azir/q",
					"azir/w",
					"azir/r",
					"azir/weq",
					"azir/drift_combo",
					"azir/e_points",
					"kled/main",
					"kled/menucn",
					"kled/misc",
					"kled/skeleton",
					-- "zac/main",
					-- "zac/menu",
					-- "zac/core",
					-- "zac/misc/damage",
					-- "zac/spells/aa",
					-- "zac/spells/q",
					-- "zac/spells/w",
					-- "zac/spells/e",
					-- "zac/spells/r",
					--"poppy/menu",
					--"poppy/main",					
					--"poppy/q",
					--"poppy/w",
					--"poppy/e",
					--"poppy/r",					
					--"poppy/items",
					-- "vayne/menu",
					-- "vayne/main",					
					-- "vayne/q",
					--"varus/r",
					--"varus/e",
					"gangplank/main",
					"gangplank/menu",
					"gangplank/misc",
					-- "Shaco/r",
					-- "Shaco/w",
					-- "Shaco/q",
					-- "Shaco/e",
					-- "Shaco/q_optimizer",
					-- "Shaco/w_optimizer",
					-- "Shaco/r_optimizer",
					-- "Shaco/spells",
					-- "Shaco/dmg",
					-- "Shaco/common",
					-- "Shaco/cnmenu",
					-- "Shaco/menu",
					-- "Shaco/core",
					-- "Shaco/main",
					"xerath/main",
					"xerath/menu",
					"xerath/e_anti_gap",
					"xerath/q",
					"xerath/w",
					"xerath/e",
					"xerath/r",
					"xerath/core",
					"xerath/q_buff",
					"xerath/q_draw",
					"xerath/r_buff",
					"xerath/r_draw",
					"xerath/clear",
					"darius/main",
					"darius/misc",
					"darius/menu",
					-- "Thresh/main",
					-- "Thresh/misc",
					-- "Thresh/menu",
					-- --"Thresh/cs",
					-- "Thresh/menucn",
					-- "Thresh/pred",
					-- "Thresh/skeleton",
					-- "JarvanIV/main",
					-- "JarvanIV/menu",
					-- "JarvanIV/misc",
					-- "Akali/misc",
					-- "Akali/main",
					-- "Akali/menu",
					-- "Akali/menucn",
					-- "Akali/skeleton",
                    "yone/e_aa",
					"yone/core",
                    "yone/main",   
                    "yone/menu",	
                    "yone/q",
                    "yone/q3",
                    "yone/e_gap",
					"yone/e2",
					"yone/w",
					"yone/q3_f_gap",
					"yone/q3_f_red",
					"yone/r",
					"yone/flash_skills",
					"yone/flash_qr",
					--"caitlyn/main",
					--"caitlyn/menu",
					--"caitlyn/q",
					--"caitlyn/w",
					--"caitlyn/e",
					--"caitlyn/r",
					--"caitlyn/lua",
					--"caitlyn/Caitlyn_W",
                    -- "blitzcrank/q",	
                    -- "blitzcrank/main",
					-- "blitzcrank/menu",	
					"teemo/core",
					"teemo/damagelib",
					"teemo/main",
					"teemo/menu",
					"teemo/spells/q",
					"teemo/spells/e",
					"teemo/spells/r",
					"teemo/spells/w",
					"zed/menu",
					"zed/main",
					-- "Vi/menu",
					"zed/misc",
					-- "Vi/skeleton",
					--"viktor/menu",
					"nidalee/q",
					"nidalee/main",
					"nidalee/clear_simple",
					-- "yasuo/q",
					-- "yasuo/q3",
					-- "yasuo/w",
					-- "yasuo/core",
					-- "yasuo/main",
					-- "yasuo/menu",
					-- "yasuo/common",
					"Riven/common",
					"Riven/chase",
    "Riven/drawtest",
    "Riven/flee",
	"Riven/harass",
    --"Riven/header",
    "Riven/main",
    "Riven/menu",
    "Riven/pushtest",
    "Riven/Yeahtest",
    "Riven/core/ai",
    "Riven/core/draw",
    "Riven/core/main",
    "Riven/item/crescent_wrapper",
    "Riven/pred/aa",
    "Riven/pred/e_flash_q",
    "Riven/pred/e_flash_w",
    "Riven/pred/e_q",
    "Riven/pred/e_w",
    "Riven/pred/e",
    "Riven/pred/flash_q",
    "Riven/pred/flash_w",
    "Riven/pred/main",
    "Riven/pred/push",
    "Riven/pred/q",
    "Riven/pred/r1",
    "Riven/pred/r2_dmg",
    "Riven/pred/r2",
    "Riven/pred/w",
    "Riven/spell/e",
    "Riven/spell/flash",
    "Riven/spell/main",
    "Riven/spell/q",
    "Riven/spell/r1",
    "Riven/spell/r2",
    "Riven/spell/w",
	"Riven/poppy_test",
	"Riven/poppy_counter",
    --"Riven/common",
	"Riven/exam",
	--"Riven/chase",
	--"sejuani/main",
	--draw
    'draw/chat',
    'draw/circle',
    'draw/color',
    'draw/info',
    'draw/stacks',
    'draw/text',
    'draw/timer',

    --Syndra
    --[['Syndra/auto_e',
    'Syndra/core',
    'Syndra/dmg',
    'Syndra/e_on_q',
    'Syndra/e_on_tar',
    'Syndra/main',
    'Syndra/menu',
    'Syndra/q',
    'Syndra/q_e',
    'Syndra/r',
    'Syndra/spells',
    'Syndra/sphere_manager',
    'Syndra/w',
    'Syndra/w_cast',]]
	
	--Orianna
    --'orianna/auto_e',
    --'orianna/ball',
    --'orianna/core',
    --'orianna/dmg',
    --'orianna/e_magnet',
    --'orianna/main',
    --'orianna/menu',
    --'orianna/e_q',
    --'orianna/e_r',
    --'orianna/r',
    --'orianna/spells',
    --'orianna/spells_targeted',
    --'orianna/w',
	--'orianna/q',
    --'orianna/r_block',

        -- Core
	      'Core/PlayerBuff',
	      'Core/DelayTick',
	      'Core/DelayAction',
	      'Core/Prediction',
	      'Core/Gapcloser', 
		  'Core/Draw',
	      'Core/Spell', 

	    -- Damage
        'Damage/Database',
        'Damage/BetaDB',
        'Damage/x',
	      'Damage/Prediction',
	      'Damage/SimpleLib',
          'ultbook/main',
	      -- Evade
	      --'Evade/WindWall',
	
	      -- Library
	      'Library/BuffManager',
	      'Library/CalculateManager',
	      'Library/ExtraManager',
	      'Library/FarmManager',
	      'Library/ItemManager',
	      'Library/NetManager',
	      'Library/ObjectManager',
	      'Library/OrbManager',
	      'Library/SpellManager',
	      'Library/VectorManager',
	   
    -- Utility
	    "Utility/AutoItem",
        "Utility/Answer",
        "Utility/common",
        "Utility/libss",
        "Utility/AutoItem",
        "Utility/SpellDatabase",
		--"Utility/common00",
        --"Utility/common1",		
		"Utility/common2",
        "Utility/common3",
		"Utility/commonF",
		"Utility/zss",
		--"Utility/common6",
		"Utility/MA",
		"Utility/spelldata",
		"Utility/SpellDatabaseSupport",
		"Utility/SpellDatabaseFiora",
		"Utility/SpellDatabaseIrelia",
		"Utility/SpellDatabaseYasuo",
--"Utility/common8",
--"Utility/common11",
"Utility/lualinq",
"Utility/Limiter",
"Utility/json",
--"Utility/common12",
--"Utility/common13",
--"Utility/common14",
--"Utility/common19",


--K KI 
"Utility/common20",
"Utility/kcommon",
"Utility/baseHealth",
"Utility/kDashDB",
"Utility/kicommon",
"Utility/kiDashDB",
"Utility/kijson",
"Utility/kiSafety",
"Utility/kiSpellDatabase",
"Utility/kiSpellDBSupp",
"Utility/kiSpellInfo",
"Utility/mainF",
"Utility/menuFA",

--"Utility/kiSpellDBSupp",


  		"check_scripts/main",
		"check_scripts/kda",
		--"bs/wav/jb.wav",
		--"bs/wav/jbb.wav",
    --champs  
"Champions/Aatrox",
"Champions/Ahri",
"Champions/Akali",
"Champions/Akshan",
"Champions/Alistar",
"Champions/Ambessa",
"Champions/Amumu",
"Champions/Anivia",
"Champions/Annie",
"Champions/Aphelios",
"Champions/Ashe",
"Champions/Aurora",
--"Champions/Azir",
"Champions/Bard",
"Champions/Belveth",
"Champions/Blitzcrank",
"Champions/Brand",
"Champions/Braum",
"Champions/Caitlyn",
"Champions/Camille",
"Champions/Cassiopeia",
"Champions/Chogath",
"Champions/Corki",
"Champions/Diana",
"Champions/Draven",
"Champions/DrMundo",
"Champions/Ekko",
"Champions/Elise",
"Champions/Evelynn",
"Champions/FiddleSticks",
"Champions/Fiora",
"Champions/Fizz",
"Champions/Galio",
"Champions/Garen",
"Champions/Gnar",
"Champions/Gragas",
--"Champions/Graves",
"Champions/Gwen",
"Champions/Hecarim",
"Champions/Heimerdinger",
"Champions/Illaoi",
"Champions/Irelia",
"Champions/Ivern",
"Champions/Janna",
"Champions/JarvanIV",
"Champions/Jax",
"Champions/Jayce",
"Champions/Jhin",
"Champions/Jinx",
"Champions/Kaisa",
"Champions/Kalista",
"Champions/Karma",
"Champions/Karthus",
"Champions/Kassadin",
"Champions/Kayle",
"Champions/Kayn",
"Champions/Kennen",
"Champions/Khazix",
"Champions/Kindred",
"Champions/KogMaw",
"Champions/KSante",
"Champions/Leblanc",
"Champions/LeeSin",
"Champions/Leona",
"Champions/Lillia",
"Champions/Lissandra",
"Champions/Lucian",
"Champions/Lulu",
"Champions/Lux",
"Champions/Malphite",
"Champions/Malzahar",
"Champions/Maokai",
"Champions/MasterYi",
"Champions/Milio",
"Champions/MissFortune",
"Champions/MonkeyKing",
"Champions/Mordekaiser",
"Champions/Morgana",
"Champions/Naafiri",
"Champions/Nami",
"Champions/Nasus",
"Champions/Nautilus",
"Champions/Neeko",
"Champions/Nilah",
"Champions/Nocturne",
"Champions/Nunu",
"Champions/Olaf",
"Champions/Orianna",
"Champions/Ornn",
"Champions/Pantheon",
"Champions/Poppy",
"Champions/Pyke",
"Champions/Qiyana",
"Champions/Quinn",
"Champions/Rakan",
"Champions/Rakan1",
"Champions/Rammus",
"Champions/RekSai",
"Champions/Rell",
"Champions/Renekton",
"Champions/Rengar",
--"Champions/Renata",
"Champions/Rumble",
"Champions/Ryze",
"Champions/Samira",
"Champions/Sejuani",
"Champions/Senna",
"Champions/Seraphine",
"Champions/Sett",
"Champions/Shen",
"Champions/Shaco",
"Champions/Shyvana",
"Champions/Singed",
"Champions/Sion",
"Champions/Sivir",
"Champions/Skarner",
--"Champions/Smolder",
"Champions/Sona",
"Champions/Soraka",
"Champions/Swain",
"Champions/Sylas",
"Champions/Syndra",
"Champions/TahmKench",
"Champions/Talon",
"Champions/Taric",
"Champions/Tristana",
"Champions/Trundle",
"Champions/Tryndamere",
"Champions/TwistedFate",
"Champions/Twitch",
"Champions/Udyr",
"Champions/Urgot",
"Champions/Varus",
"Champions/Vayne",
"Champions/Veigar",
"Champions/Vex",
"Champions/Vi",
"Champions/Viego",
"Champions/Viktor",
"Champions/Vladimir",
"Champions/Volibear",
"Champions/Warwick",
"Champions/Xayah",
"Champions/XinZhao",
"Champions/Yasuo",
"Champions/Yunara",
"Champions/Zac",
"Champions/Zeri",
"Champions/Ziggs",
"Champions/Zilean",
"Champions/Zoe",
"Champions/Zyra",
    }
}
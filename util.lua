local pred = module.internal("pred")
local orb = module.internal("orb")

local delayedActions, delayedActionsExecuter = {}, nil
local function delayAction(func, delay, args)
  if not delayedActionsExecuter then
    function delayedActionsExecuter()
      for t, funcs in pairs(delayedActions) do
        if t <= os.clock() then
          for i = 1, #funcs do
            local f = funcs[i]
            if f and f.func then
              f.func(unpack(f.args or {}))
            end
          end
          delayedActions[t] = nil
        end
      end
    end

    cb.add(cb.tick, delayedActionsExecuter)
  end
  local t = os.clock() + (delay or 0)
  if delayedActions[t] then
    delayedActions[t][#delayedActions[t] + 1] = { func = func, args = args }
  else
    delayedActions[t] = { { func = func, args = args } }
  end
end


local _intervalFunction
local function setInterval(userFunction, timeout, count, params)
  if not _intervalFunction then
    function _intervalFunction(userFunction, startTime, timeout, count, params)
      if userFunction(unpack(params or {})) ~= false and (not count or count > 1) then
        delayAction(
          _intervalFunction,
          (timeout - (os.clock() - startTime - timeout)),
          { userFunction, startTime + timeout, timeout, count and (count - 1), params }
        )
      end
    end
  end
  delayAction(_intervalFunction, timeout, { userFunction, os.clock(), timeout or 0, count, params })
end

local function getPercentHealth(obj)
  local obj = obj or player
  return (obj.health / obj.maxHealth) * 100
end

local function getPercentMana(obj)
  local obj = obj or player
  return (obj.mana / obj.maxMana) * 100
end

local function getPercentPar(obj)
  local obj = obj or player
  return (obj.par / obj.maxPar) * 100
end

local function getPercentSar(obj)
  local obj = obj or player
  return (obj.sar / obj.maxSar) * 100
end

local function resetOrb()
  orb.core.set_pause(0)
  orb.core.set_pause_move(0)
  orb.core.set_pause_attack(0)
end

local function resetOrbDelay(delay)
  if delay and delay >= 0 then
    delayAction(resetOrb(), delay)
  end
end
local hard_cc = {
  [BUFF_STUN] = true,  -- stun
  [BUFF_TAUNT] = true,  -- taunt
  [BUFF_SNARE] = true, -- snare
  [BUFF_ASLEEP] = true, -- sleep
  [BUFF_FEAR] = true, -- fear
  [BUFF_CHARM] = true, -- charm
  [BUFF_SUPPRESSION] = true, -- suppression
  [BUFF_FLEE] = true, -- flee
  [BUFF_KNOCKUP] = true, -- knockup
  [BUFF_KNOCKBACK] = true  -- knockback
}

local function hasBuffType(obj, bufftype)
  if obj and obj.buff then
    for _, buff in pairs(obj.buff) do
      if buff and buff.valid and buff.type == bufftype and (buff.stacks > 0 or buff.stacks2 > 0) then
        return true, buff
      end
    end
  end
end

local function hasCCBuff(obj)
  if obj and obj.buff then
    for _, buff in pairs(obj.buff) do
      if buff and buff.valid and hard_cc[buff.type] and (buff.stacks > 0 or buff.stacks2 > 0) then
        return true
      end
    end
  end
end


local function getBuffTimeEnd(buff)
  if buff and buff.valid and (buff.stacks > 0 or buff.stacks2 > 0) and game.time <= buff.endTime then
    return buff.endTime - game.time
  else
    return 0
  end
end


local function getBuffAndTimeEnd(obj, buffname)
  if obj and obj.buff then
    for _, buff in pairs(obj.buff) do
      if buff and buff.valid and buff.name:find(buffname) and (buff.stacks > 0 or buff.stacks2 > 0) then
        return buff, getBuffTimeEnd(buff)
      end
    end
  end
end

local function getBuff(obj, buffname)
  if obj and obj.buff then
    for _, buff in pairs(obj.buff) do
      if buff and buff.valid and buff.name:find(buffname) and (buff.stacks > 0 or buff.stacks2 > 0) then
        return buff
      end
    end
  end
end


local function getBuffStacks(target, buffname)
  local buff = getBuff(target, buffname)
  if buff then
    return math.max(buff.stacks, buff.stacks2) 
  else
    return 0
  end
end


local function getIgniteDamage(target)
  local damage = 50 + (20 * player.levelRef)
  if target then
    damage = damage - (getShieldedHealth("AD", target) - target.health)
  end
  return damage
end

local function getAARange(obj, source)
  return source.attackRange + source.boundingRadius + (obj and obj.boundingRadius or 0)
end

local function isInAARange(obj, source)
  if getAARange(obj, source) > obj.pos:dist(source.pos) then
    return true
  else
    return false
  end
end

local function isInvincible(object)
  if hasBuffType(object, 17) then
    return true
  end
end

local function isTargetValid(object)
  return (
    object
    and object.ptr ~= 0
    and not object.isDead
    and object.isVisible
    and object.isTargetable
    and not isInvincible(object)
  )
end

local function isHeroValid(object, range)
  return (
    object
    and object.ptr ~= 0
    and object.type == TYPE_HERO
    and not object.isDead
    and object.isVisible
    and (object.team == player.team
      or object.isTargetable
      and not isInvincible(object))
    and (not range or object.pos2D:distSqr(player.pos2D) < range * range)
  )
end

local function isMinionValid(object, ignoreTeam)
  return (
    object
    and object.ptr ~= 0
    and object.type == TYPE_MINION
    and (ignoreTeam or object.team ~= TEAM_ALLY)
    and not object.isDead
    and object.isVisible
    and object.isTargetable
    and object.health > 0
    and object.moveSpeed > 0
    and object.maxHealth > 5
    and object.maxHealth < 100000
  )
end

local function isFleeing(object, source)
  if not isTargetValid(object) or not object.path or not object.path.isActive or not object.moveSpeed then
    return false
  end
  pred = pred or module.internal("pred")
  local pred_pos = pred.core.lerp(object.path, network.latency + .25, object.moveSpeed)
  return vec3(pred_pos.x, object.y, pred_pos.y):dist(source.pos) > object.pos:dist(source.pos)
end

local function isFleeingFromMe(object)
  return isFleeing(object, objManager.player)
end

local function isPosOnScreen(pos)
  local pos2D = graphics.world_to_screen(pos)
  if pos2D.x < 0 or pos2D.x > graphics.width
      or pos2D.y < 0 or pos2D.y > graphics.height
  then
    return false
  end
  return true
end

local enemyHeroes
local function getEnemyHeroes()
  if enemyHeroes then
    return enemyHeroes
  end
  enemyHeroes = {}
  for h = 0, objManager.enemies_n - 1 do
    local hero = objManager.enemies[h]
    enemyHeroes[#enemyHeroes + 1] = hero
  end
  return enemyHeroes
end

local allyHeroes
local function getAllyHeroes()
  if allyHeroes then
    return allyHeroes
  end
  allyHeroes = {}
  for h = 0, objManager.allies_n - 1 do
    local hero = objManager.allies[h]
    allyHeroes[#allyHeroes + 1] = hero
  end
  return allyHeroes
end

local herosList
local function getHerosList()
  if herosList then
    return herosList
  end
  herosList = {}
  for h = 0, objManager.allies_n - 1 do
    local hero = objManager.allies[h]
    herosList[#herosList + 1] = hero
  end
  for h = 0, objManager.enemies_n - 1 do
    local hero = objManager.enemies[h]
    herosList[#herosList + 1] = hero
  end
  return herosList
end

local function getEnemyTurrets()
  turrets = {}
  for i=0, objManager.turrets.size[TEAM_ENEMY]-1 do
    local obj = objManager.turrets[TEAM_ENEMY][i]
    if isTargetValid(obj) then
      table.insert(turrets, obj)
    end
  end
  return turrets
end

local function getAllyTurrets()
  turrets = {}
  for i=0, objManager.turrets.size[TEAM_ALLY]-1 do
    local obj = objManager.turrets[TEAM_ALLY][i]
    if isTargetValid(obj) then
      table.insert(turrets, obj)
    end
  end
  return turrets
end

local function isUnderEnemyTurret(pos)
  local turrets = getEnemyTurrets()
  for _, turret in ipairs(turrets) do
    if turret.pos:dist(pos) < 900 then
      return true, turret
    end
  end
  return false
end

local function isUnderAllyTurret(pos)
  local turrets = getAllyTurrets()
  for _, turret in ipairs(turrets) do
    if turret.pos:dist(pos) < 900 then
      return true, turret
    end
  end
  return false
end

local function getObjectsNearPos(pos, radius, objects, validFunc)
  local obj, num = 0, nil
  for i, object in pairs(objects) do
    if validFunc(object) and pos:distSqr(object.pos) <= radius * radius then
      num = num + 1
      obj[num] = object
    end
  end
  return obj, num
end

local function RotateAroundPoint(v1, v2, angle)
  cos, sin = math.cos(angle), math.sin(angle)
  x = ((v1.x - v2.x) * cos) - ((v2.z - v1.z) * sin) + v2.x
  z = ((v2.z - v1.z) * cos) + ((v1.x - v2.x) * sin) + v2.z
  return vec3(x, v1.y, z or 0)
end

local function CheckWallOnLine(startPos, endPos, width)
  local distance = startPos:dist(endPos)
  local stepSize = 10

  local direction = (endPos - startPos):norm()

  local numSteps = math.floor(distance / stepSize)
  for i = 1, numSteps do
    local checkPos = startPos + direction * (i * stepSize)
    local checkPos1 = checkPos + direction:perp1() * width
    local checkPos2 = checkPos + direction:perp2() * width

    if (navmesh.isWall(checkPos1) or navmesh.isStructure(checkPos1)) and
        (navmesh.isWall(checkPos2) or navmesh.isStructure(checkPos2)) then
      return true, checkPos
    end
  end

  return false
end

local function GetClosestWallPosition(startpos, range)
  range = range or 400
  local closestWallPos = nil
  local closestDistance = math.huge

  for i = 0, 360, 30 do
    local angle           = i * math.pi / 180
    local targetRotated   = vec3(startpos.x + range, startpos.y, startpos.z)
    local endpos          = vec3(RotateAroundPoint(targetRotated, startpos, angle))
    local iswall, wallpos = CheckWallOnLine(startpos, endpos, 50)
    if iswall and wallpos then
      local distance = startpos:dist(wallpos)
      if distance < closestDistance then
        closestWallPos = wallpos
        closestDistance = distance
      end
    end
  end

  return closestWallPos
end


return {
  delayAction = delayAction,
  setInterval = setInterval,

  getPercentHealth = getPercentHealth,
  getPercentMana = getPercentMana,
  getPercentPar = getPercentPar,
  getPercentSar = getPercentSar,

  resetOrb = resetOrb,
  resetOrbDelay = resetOrbDelay,

  hasBuffType = hasBuffType,
  hasCCBuff = hasCCBuff,
  getBuff = getBuff,
  getBuffStacks = getBuffStacks,
  getBuffTimeEnd = getBuffTimeEnd,
  getBuffAndTimeEnd = getBuffAndTimeEnd,

  getIgniteDamage = getIgniteDamage,
  getAARange = getAARange,
  isInAARange = isInAARange,

  isTargetValid = isTargetValid,
  isHeroValid = isHeroValid,
  isMinionValid = isMinionValid,
  isFleeing = isFleeing,
  isFleeingFromMe = isFleeingFromMe,
  isPosOnScreen = isPosOnScreen,

  getEnemyHeroes = getEnemyHeroes,
  getAllyHeroes = getAllyHeroes,
  getHerosList = getHerosList,
  getEnemyTurrets = getEnemyTurrets,
  getAllyTurrets = getAllyTurrets,
  isUnderEnemyTurret = isUnderEnemyTurret,
  isUnderAllyTurret = isUnderAllyTurret,

  countObjectsNearPos = getObjectsNearPos,
  hard_cc = hard_cc,
  checkWallOnLine = CheckWallOnLine,
  getClosestWallPosition = GetClosestWallPosition,
}

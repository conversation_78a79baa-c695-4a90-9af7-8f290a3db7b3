-- Azir 简化连招系统 (严格遵循开发者文档API)
-- 根据开发者文档实现的简单但有效的连招逻辑

local orb = module.internal("orb")
local pred = module.internal("pred")
local TS = module.internal("TS")
local menu = module.load("<PERSON>","azir/menu")
local q_module = module.load("<PERSON>","azir/q")
local w_module = module.load("<PERSON>","azir/w")
local e_module = module.load("<PERSON>","azir/e")
local r_module = module.load("<PERSON>","azir/r")

-- 连招状态
local combo_state = {
    last_combo_time = 0,
    combo_step = 0,
    target = nil
}

-- 基础伤害计算
local function calculate_damage(target)
    if not target then return 0 end
    
    local damage = 0
    
    -- Q技能伤害
    if player:spellSlot(0).state == 0 then
        local q_level = player:spellSlot(0).level
        if q_level > 0 then
            local base_damage = {70, 105, 140, 175, 210}
            damage = damage + base_damage[q_level] + (player.flatMagicDamageMod or 0) * 0.5
        end
    end
    
    -- W技能沙兵伤害
    if #w_module.soldiers > 0 then
        local w_level = player:spellSlot(1).level
        if w_level > 0 then
            local base_damage = {50, 55, 60, 65, 70}
            damage = damage + base_damage[w_level] + (player.flatMagicDamageMod or 0) * 0.6
        end
    end
    
    -- R技能伤害
    if player:spellSlot(3).state == 0 then
        local r_level = player:spellSlot(3).level
        if r_level > 0 then
            local base_damage = {150, 225, 300}
            damage = damage + base_damage[r_level] + (player.flatMagicDamageMod or 0) * 0.6
        end
    end
    
    return damage
end

-- 爆发连招 (击杀优先)
local function burst_combo()
    local target = TS.get_result(function(res, obj, dist)
        if dist < 1000 then
            local damage = calculate_damage(obj)
            if damage > obj.health * 0.8 then -- 能造成80%以上伤害
                res.obj = obj
                return true
            end
        end
        return false
    end).obj
    
    if not target then return false end
    
    -- 连招序列: W -> Q -> E -> R
    -- 1. 放置沙兵
    if player:spellSlot(1).state == 0 and #w_module.soldiers < 2 then
        if w_module.invoke() then
            combo_state.last_combo_time = os.clock()
            return true
        end
    end
    
    -- 2. Q技能
    if player:spellSlot(0).state == 0 and #w_module.soldiers > 0 then
        if q_module.invoke() then
            combo_state.last_combo_time = os.clock()
            return true
        end
    end
    
    -- 3. E技能突进 (如果目标较近)
    if player:spellSlot(2).state == 0 and target.pos:dist(player.pos) < 600 then
        if e_module.invoke() then
            combo_state.last_combo_time = os.clock()
            return true
        end
    end
    
    -- 4. R技能终结
    if player:spellSlot(3).state == 0 and target.health < target.maxHealth * 0.4 then
        if r_module.invoke() then
            combo_state.last_combo_time = os.clock()
            return true
        end
    end
    
    return false
end

-- 骚扰连招
local function harass_combo()
    local target = TS.get_result(function(res, obj, dist)
        if dist < 800 and obj.health > obj.maxHealth * 0.3 then
            res.obj = obj
            return true
        end
        return false
    end).obj
    
    if not target then return false end
    
    -- 简单的骚扰: W -> Q
    -- 1. 放置沙兵
    if player:spellSlot(1).state == 0 and #w_module.soldiers == 0 then
        if w_module.invoke() then
            combo_state.last_combo_time = os.clock()
            return true
        end
    end
    
    -- 2. Q技能戳刺
    if player:spellSlot(0).state == 0 and #w_module.soldiers > 0 then
        if q_module.invoke() then
            combo_state.last_combo_time = os.clock()
            return true
        end
    end
    
    return false
end

-- 明星连招 (空格键触发)
local function star_combo()
    if not keyboard.isKeyDown(0x20) then return false end -- 空格键
    
    local target = TS.get_result(function(res, obj, dist)
        if dist < 1200 then
            res.obj = obj
            return true
        end
        return false
    end).obj
    
    if not target then return false end
    
    -- 华丽连招: W -> E -> Q -> R
    local current_time = os.clock()
    
    -- 防止过于频繁的操作
    if current_time - combo_state.last_combo_time < 0.2 then
        return false
    end
    
    -- 1. 远距离放置沙兵
    if player:spellSlot(1).state == 0 and combo_state.combo_step == 0 then
        if w_module.invoke() then
            combo_state.combo_step = 1
            combo_state.last_combo_time = current_time
            return true
        end
    end
    
    -- 2. E技能突进
    if player:spellSlot(2).state == 0 and combo_state.combo_step == 1 and #w_module.soldiers > 0 then
        if e_module.invoke() then
            combo_state.combo_step = 2
            combo_state.last_combo_time = current_time
            return true
        end
    end
    
    -- 3. Q技能
    if player:spellSlot(0).state == 0 and combo_state.combo_step == 2 then
        if q_module.invoke() then
            combo_state.combo_step = 3
            combo_state.last_combo_time = current_time
            return true
        end
    end
    
    -- 4. R技能
    if player:spellSlot(3).state == 0 and combo_state.combo_step == 3 then
        if r_module.invoke() then
            combo_state.combo_step = 0 -- 重置
            combo_state.last_combo_time = current_time
            return true
        end
    end
    
    -- 超时重置
    if current_time - combo_state.last_combo_time > 3 then
        combo_state.combo_step = 0
    end
    
    return false
end

-- 主要连招执行函数
local function execute_combo()
    -- 优先级: 明星连招 > 爆发连招 > 骚扰连招
    
    -- 1. 明星连招 (空格键)
    if star_combo() then
        return true
    end
    
    -- 2. 爆发连招 (击杀机会)
    if burst_combo() then
        return true
    end
    
    -- 3. 骚扰连招
    if harass_combo() then
        return true
    end
    
    return false
end

-- 连招信息显示
local function draw_combo_info()
    if combo_state.combo_step > 0 then
        local screen_pos = graphics.world_to_screen(player.pos)
        local combo_text = "明星连招步骤: " .. combo_state.combo_step .. "/4"
        graphics.draw_text_2D(combo_text, 14, screen_pos.x - 60, screen_pos.y - 50, 0xFFFFFF00)
    end
    
    -- 显示快捷键提示
    local hint_text = "连招: 空格键-明星连招 | 自动-爆发/骚扰"
    graphics.draw_text_2D(hint_text, 12, 10, graphics.height - 50, 0xFFCCCCCC)
end

-- 导出函数
return {
    execute_combo = execute_combo,
    burst_combo = burst_combo,
    harass_combo = harass_combo,
    star_combo = star_combo,
    draw_combo_info = draw_combo_info,
    calculate_damage = calculate_damage
}

local ove_0_5 =  module.internal("orb")
local ove_0_6 = module.load(header.id,"orianna/menu")
local ove_0_7 = module.load(header.id,"orianna/ball")
local ove_0_8 = module.load(header.id,"orianna/q")
local ove_0_9 = module.load(header.id,"orianna/w")
local ove_0_10 = module.load(header.id,"orianna/e_magnet")
local ove_0_11 = module.load(header.id,"orianna/e_q")
local ove_0_12 =module.load(header.id,"orianna/e_r")
local ove_0_13 = module.load(header.id,"orianna/r")
local ove_0_14
local ove_0_15 = 0
local ove_0_16 = {}

local function ove_0_17()

	ove_0_14 = nil
end

local function ove_0_18()

	ove_0_14 = ove_0_17
	ove_0_15 = os.clock() + 0.5 - 0.016
end

local function ove_0_19()

	if ove_0_14 and os.clock() + network.latency > ove_0_15 then
		ove_0_14()
	end

	if player.isDead then
		return false
	end

	if ove_0_5.core.is_paused() then
		return false
	end

	if ove_0_7.client_server_missmatch() > network.latency * ove_0_7.speed then
	--print(ove_0_7.client_server_missmatch(),network.latency * ove_0_7.speed)
		return false
	end

	if ove_0_13.get_action_state() then
		ove_0_13.invoke_action()

		return true
	end

	local slot_3_0 = ove_0_8.get_action_state()
	local slot_3_1 = ove_0_9.get_action_state()

	if ove_0_6.combat:get() then
		if slot_3_0 then
			if slot_3_1 and slot_3_1.ptr == slot_3_0.ptr then
				ove_0_9.invoke_action()
			end

			if ove_0_6.e_q:get() and ove_0_11.get_action_state(slot_3_0) then
				ove_0_11.invoke_action()

				return true
			end

			ove_0_8.invoke_action()

			return true
		end

		if slot_3_1 then
			ove_0_9.invoke_action()
		end
	end

	if ove_0_12.get_action_state() then
		ove_0_12.invoke_action()

		return true
	end

	if ove_0_6.harass:get() then
		if slot_3_0 and ove_0_6.harass_q:get() then
			if slot_3_1 and ove_0_6.harass_w:get() and slot_3_1.ptr == slot_3_0.ptr then
				ove_0_9.invoke_action()
			end

			ove_0_8.invoke_action()

			return true
		end

		if slot_3_1 and ove_0_6.harass_w:get() then
			ove_0_9.invoke_action()
		end
	end

	if ove_0_6.e_magnet:get() and ove_0_6.e_magnet_key:get() and ove_0_10.get_action_state() then
		ove_0_10.invoke_action()
	end

	if not ove_0_6.combat:get() and ove_0_6.auto_q:get() and slot_3_0 then
		ove_0_8.invoke_action()

		return true
	end
end

local function ove_0_20(arg_4_0)

	if arg_4_0.owner == player and ove_0_16[arg_4_0.name] then
		ove_0_16[arg_4_0.name]()
	end
end

local ove_0_21 = "Auto Q Enabled"
local ove_0_22 = graphics.text_area(ove_0_21, 15)

cb.add(cb.draw, function()

	if player.isOnScreen and ove_0_6.auto_q:get() then
		local slot_5_0 = graphics.world_to_screen(player.pos)

		graphics.draw_text_2D(ove_0_21, 15, slot_5_0.x - ove_0_22 * 0.5, slot_5_0.y + 37, 4294967295)
	end
end)

return {
	get_action = ove_0_19,
	on_recv_spell = ove_0_20
}

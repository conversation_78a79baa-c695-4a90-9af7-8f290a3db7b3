local ireliaPlugin = {}

-- Load Spell

local spellQ = {
    range = 600,
}

local spellW = {
    range = 825,
}

local spellE = {
    range = 850,
    delay = 0.25,
    width = 60,
    speed = 2000,
	boundingRadiusMod = 1,
    collision = {hero = false, minion = false, wall = false},
    lastCastTime = 0,
    blade = nil,
    lastBladeTime = 0, 
    testPoint = vec3(0, 0, 0),
}

local spellE2 = {
    range = 900,
    delay = 0.25,
    width = 80,
    speed = 2000,
	boundingRadiusMod = 0,
    collision = {hero = false, minion = false, wall = false},
}

local spellR = {
    range = 1000,
    delay = 0.40,
    width = 80, 
    speed = 2000,
	boundingRadiusMod = 0,
	collision = {hero = true, minion = false, wall = false},
}

local sheenActive = false
local last_item_update = 0
local sheenTimer = 0
local hasSheen = false
local hasTF = false
local hasBOTRK = false
local hasTitanic = false
local hasWitsEnd = false
local hasRecurve = false
local hasGuinsoo = false

-- Load Module
local ui = module.load("<PERSON>", "ui");
local DelayAction = module.load("<PERSON>", "Core/DelayAction")
local DelayTick = module.load("<PERSON>", "Core/DelayTick")
local Prediction = module.load("<PERSON>", "Core/Prediction")
local BuffManager = module.load("Brian", "Library/BuffManager")
local CalculateManager = module.load("Brian", "Library/CalculateManager")
local FarmManager = module.load("Brian", "Library/FarmManager")
local ItemManager = module.load("Brian", "Library/ItemManager")
local NetManager = module.load("Brian", "Library/NetManager")
local ObjectManager = module.load("Brian", "Library/ObjectManager")
local OrbManager = module.load("Brian", "Library/OrbManager")
local SpellManager = module.load("Brian", "Library/SpellManager")
local VectorManager = module.load("Brian", "Library/VectorManager")
local MyCommon = module.load("Brian", "Library/ExtraManager")

local MyMenu

function ireliaPlugin.Load(GlobalMenu)
    MyMenu = GlobalMenu

    MyMenu.Key:keybind("Flee", "Flee Key", "Z", false)

    MyMenu:menu("Combo", "Combo Settings")
    MyMenu.Combo:header("SpellHeader", "Spell Core")
    MyMenu.Combo:boolean("Q", "Use Q", true)
    MyMenu.Combo:boolean("QAA", "^ If Target Can 3AA KS", true)
    MyMenu.Combo:boolean("QTM", "^ Team Fight Mode", false)
    MyMenu.Combo:keybind("QTR", "^ Allow Dash to Enemy Turret", nil, "U")
    MyMenu.Combo:boolean("E", "Use E", true)
    MyMenu.Combo:keybind("RC", "Use R", nil, "T")
    MyMenu.Combo:boolean("RL", "^ If target Low Hp", true)
    MyMenu.Combo:slider("RLM", "^ Target HealthPercent <= x%", 50, 1, 100, 1)
    MyMenu.Combo:boolean("RAOE", "^ AOE Hit", true)
    MyMenu.Combo:slider("RAOECount", "^ AOE Hit Count >= x", 3, 1, 5, 1)

    MyMenu:menu("Harass", "Harass Settings")
    MyMenu.Harass:header("SpellHeader", "Spell Core")
    MyMenu.Harass:boolean("Q", "Use Q", true)
    MyMenu.Harass:boolean("QM", "^ Gapcloser To Target", false)
    MyMenu.Harass:boolean("E", "Use E", true)
    MyMenu.Harass:header("ManaHeader", "Mana Manager")
    MyMenu.Harass:slider("ManaMin", "Player ManaPercent >= x%", 60, 1, 100, 1)
    MyMenu.Harass:header("ProHeader", "PRO Mode")
    MyMenu.Harass:boolean("ProTurret", "Allow Under Turret Harass", false)

    MyMenu:menu("LaneClear", "LaneClear Settings")
    MyMenu.LaneClear:header("SpellHeader", "Spell Core")
    MyMenu.LaneClear:boolean("Q", "Use Q", true)
    MyMenu.LaneClear:boolean("QTR", "^ Allow Dash to Enemy Turret", false)
    MyMenu.LaneClear:header("ManaHeader", "Mana Manager")
    MyMenu.LaneClear:slider("ManaMin", "Player ManaPercent >= x%", 45, 1, 100, 1)

    MyMenu:menu("JungleClear", "JungleClear Settings")
    MyMenu.JungleClear:header("SpellHeader", "Spell Core")
    MyMenu.JungleClear:boolean("Q", "Use Q", true)
    MyMenu.JungleClear:boolean("QM", "^ Only Mark Or KS", true)
    MyMenu.JungleClear:boolean("W", "Use W", true)
    MyMenu.JungleClear:boolean("E", "Use E", true)
    MyMenu.JungleClear:header("ManaHeader", "Mana Manager")
    MyMenu.JungleClear:slider("ManaMin", "Player ManaPercent >= x%", 30, 1, 100, 1)

    FarmManager.Load(MyMenu)

    MyMenu:menu("Flee", "Flee Settings")
    MyMenu.Flee:header("SpellHeader", "Spell Core")
    MyMenu.Flee:boolean("Q", "Use Q", true)
    MyMenu.Flee:boolean("E", "Use E", true)

    MyMenu:menu("KillSteal", "KillSteal Settings")
    MyMenu.KillSteal:header("SpellHeader", "Spell Core")
    MyMenu.KillSteal:boolean("Q", "Use Q", true)

    MyMenu:menu("Draw", "Draw Settings")
    MyMenu.Draw:header("RangeHeader", "Spell Range")
    MyMenu.Draw:boolean("Q", "Draw Q Range", true)
    MyMenu.Draw:color("colorq", "^ Color", 255, 233, 121, 121)
    MyMenu.Draw:boolean("E", "Draw E Range", true)
    MyMenu.Draw:color("colore", "^ Color", 255, 233, 121, 121)
    MyMenu.Draw:boolean("R", "Draw R Range", true)
    MyMenu.Draw:color("colorr", "^ Color", 255, 233, 121, 121)
    MyMenu.Draw:boolean("Ready", "Only Spell Ready", true)
    MyMenu.Draw:header("StatusHeader", "Spell Status")
    MyMenu.Draw:boolean("CQ", "Draw Combo Q Status", true)
    MyMenu.Draw:color("colorqs", "^ Color", 255, 233, 121, 121)
    MyMenu.Draw:boolean("CR", "Draw Combo R Status", true)
    MyMenu.Draw:color("colorrs", "^ Color", 255, 233, 121, 121)
    MyMenu.Draw:header("DamageHeader", "Damage Indicator")
    MyMenu.Draw:boolean("DIEnabled", "Enabled", true)

end

local function GetQDamage(target)
    if not target or target == nil then
        return 0
    end
    local level = player:spellSlot(0).level
    if level == 0 then
        return 0
    end
    local dmg = ({5, 25, 45, 65, 85})[level] + (0.6 * MyCommon.GetTotalAD())
	local minionDmg = ({55, 75, 95, 115, 135})[level]
    if target.type == TYPE_MINION and target.team ~= TEAM_NEUTRAL then
		dmg = dmg + minionDmg
    end

    local onhitPhysical = 0
	local onhitMagical = 0

    if hasTF and (NetManager.TickCount() - sheenTimer > 1600) then
		onhitPhysical = onhitPhysical + 1.95 * player.baseAttackDamage
	end
    if hasSheen and not hasTF and (NetManager.TickCount() - sheenTimer > 1600 or sheenActive) then
		onhitPhysical = onhitPhysical + 0.85 * player.baseAttackDamage
	end
	if hasTitanic then
		if player.buff["itemtitanichydracleavebuff"] then
			onhitPhysical = onhitPhysical + 40 + player.maxHealth / 10
		else
			onhitPhysical = onhitPhysical + 5 + player.maxHealth / 100
		end
	end
	if hasRecurve then
		onhitPhysical = onhitPhysical + 10
	end
	if hasWitsEnd then
		onhitMagical = onhitMagical + 42
    end

    if BuffManager.HasBuff(player, "ireliapassivestacksmax") then
        local basicAttackDamage = 12 + (3 * player.levelRef) + (0.25 * MyCommon.GetBonusAD())
        onhitMagical = onhitMagical + basicAttackDamage
    end

	if hasGuinsoo then
		onhitPhysical = onhitPhysical + (MyCommon.GetBonusAD() * 0.1)
		onhitMagical = onhitMagical + (MyCommon.GetTotalAP() * 0.1)
    end
    
    local result = CalculateManager.CalculatePhysicalDamage(target, (dmg + onhitPhysical)) + CalculateManager.CalculateMagicDamage(target, onhitMagical)
	-- if hasSheen or hasTF then
	--    return result * 0.8
	-- end
    return result
end

local function GetWDamage(target)
    if not target or target == nil then
        return 0
    end
    local level = player:spellSlot(1).level
    if level == 0 then
        return 0
    end
    local dmg = ({10, 25, 40, 55, 70})[level] + (MyCommon.GetTotalAD() * 0.5) + (0.4 * MyCommon.GetTotalAP())
    return CalculateManager.CalculatePhysicalDamage(target, dmg)
end

local function GetEDamage(target)
    if not target or target == nil then
        return 0
    end
    local level = player:spellSlot(2).level
    if level == 0 then
        return 0
    end
    local dmg = ({70, 110, 150, 190, 230})[level] + (0.8 * MyCommon.GetTotalAP())
    return CalculateManager.CalculateMagicDamage(target, dmg)
end

local function GetRDamage(target)
    if not target or target == nil then
        return 0
    end
    local level = player:spellSlot(3).level
    if level == 0 then
        return 0
    end
    local dmg = ({125, 225, 325})[level] + (0.75 * MyCommon.GetTotalAP())
    return CalculateManager.CalculateMagicDamage(target, dmg)
end

local function GetETarget()
    return MyCommon.TargetSelector.get_result(
        function(res, obj, dist)
            if dist < 950 then
                -- dont use on can dash target
                if not (BuffManager.HasBuff(obj, "ireliamark") and SpellManager.CanCastSpell(0) and dist < spellQ.range) then
                    res.obj = obj
                    return true
                end
            end
        end
    ).obj
end

local function GetRTarget()
    return MyCommon.TargetSelector.get_result(
        function(res, obj, dist)
            if dist < spellR.range then
                if MyCommon.GetHealthPercent(obj) <= MyMenu.Combo.RLM:get() then
                    res.obj = obj
                    return true
                end
            end
        end
    ).obj
end

local function GetRAOETarget()
    return MyCommon.TargetSelector.get_result(
        function(res, obj, dist)
            if dist < spellR.range then
                if #ObjectManager.GetEnemiesInRange(350, obj) >= MyMenu.Combo.RAOECount:get() then
                    res.obj = obj
                    return true
                end
            end
        end
    ).obj
end

local function RemoveObject(obj, fromTable)
    if obj and obj.ptr then
        for i, cache in pairs(fromTable) do
            if cache and cache.ptr and cache.ptr == obj.ptr then
                table.remove(fromTable, i)
            end
        end
    end
end

local function GetDashObject()
    local result = {}
    local pos = vec2(player.pos2D.x, player.pos2D.y)
    local range = spellQ.range
    local targets = ObjectManager.EnemyHeroes
    if targets and #targets > 0 then
        for i, target in ipairs(targets) do
            if target and target ~= nil and target.type and target.type == TYPE_HERO and not target.isDead and target.health and target.health > 0 and target.isVisible and target.isTargetable then
                if target.pos and target.pos2D and pos and target.pos2D:distSqr(pos) <= (range * range) then
                    if BuffManager.HasBuff(target, "ireliamark") or (target.health and target.health < GetQDamage(target)) then
                        table.insert(result, target)
                    end
                end
            end
        end
    end
    local minions = ObjectManager.EnemyMinions
    if minions and #minions > 0 then
        for i, minion in ipairs(minions) do
            if minion and minion ~= nil and minion.type == TYPE_MINION and not minion.isDead and minion.health and minion.health > 0 and minion.maxHealth and minion.maxHealth > 50 and minion.charName and not string.lower(minion.charName):find("jarvanivstandard") then
                if minion.pos and minion.pos2D and pos and minion.pos2D:distSqr(pos) <= (range * range) then
                    if BuffManager.HasBuff(minion, "ireliamark") or (minion.health and minion.health < GetQDamage(minion)) then
                        table.insert(result, minion)
                    end
                end
            end
        end
    end
    local mobs = ObjectManager.NeturalMinions
    if mobs and #mobs > 0 then
        for i, mob in ipairs(mobs) do
            if mob and mob ~= nil and mob.type == TYPE_MINION and not mob.isDead and mob.health and mob.health > 0 and mob.maxHealth and mob.maxHealth > 25 and mob.charName and not string.lower(mob.charName):find("jarvanivstandard") then
                if mob.pos and mob.pos2D and pos and mob.pos2D:distSqr(pos) <= (range * range) then
                    if BuffManager.HasBuff(mob, "ireliamark") or (mob.health and mob.health < GetQDamage(mob)) then
                        table.insert(result, mob)
                    end
                end
            end
        end
    end
    return result
end

local function CanDash(target, underTower, pos)
    if target and target ~= nil and MyCommon.IsValidTarget(target) then
        underTower = underTower or false
        pos = pos or vec3(0, 0, 0)
        if pos and pos.x == 0 and pos.y == 0 and pos.z == 0 then
            pos = vec3(target.pos.x, target.pos.y, target.pos.z)
        end
        if BuffManager.HasBuff(target, "ireliamark") or (target.health and target.health < GetQDamage(target)) then
            if underTower or not ObjectManager.IsUnderEnemyTurret(pos) then
                return true
            end
        end
    end
end

local function GetBestObjToTarget(dashlist, target, underTower)
    underTower = underTower or false
    local closestObject = nil
    local nearestDistance = 100000
    local list = dashlist
    RemoveObject(target, list)
    if list and #list > 0 then
        if target and target ~= nil and MyCommon.IsValidTarget(target) then
            local pos = target.path.serverPos
            for i, obj in ipairs(list) do
                if obj and obj ~= nil and MyCommon.IsValidTarget(obj, spellQ.range) and CanDash(obj, underTower, pos) then
                    if obj.pos:dist(pos) < nearestDistance then
                        closestObject = obj
                        nearestDistance = obj.pos:dist(pos)
                    end
                end
            end
        end
    end
    return closestObject
end

-- Credit by Hoola
local function Deviation(p1, p2, angle)
    local rads = angle * mathf.PI / 180
    local diff = vec2(p2.x - p1.x, p2.y - p1.y)
    local resx = (diff.x * math.cos(rads)) - (diff.y * math.sin(rads)) / 4
    local resy = (diff.x * math.sin(rads)) + (diff.y * math.cos(rads)) / 4
    return vec2(resx + p1.x, resy + p1.y)
end

local function GetFastPrediction(spell, range, target, fromPos)
    fromPos = fromPos or player.pos2D 
    if target and target ~= nil then
        local pred = Prediction.BotPrediction.linear.get_prediction(spell, target, fromPos)
        if pred and pred.startPos and pred.startPos.x and pred.startPos.y and pred.endPos and pred.endPos.x and pred.endPos.y and player.pos2D:dist(pred.endPos) < (range + target.boundingRadius) then
            if target.type == TYPE_HERO then
                if Prediction.BotPrediction.trace.linear.hardlock(spell, pred, target) then
                    return vec2(pred.endPos.x, pred.endPos.y)
                end
                if Prediction.BotPrediction.trace.linear.hardlockmove(spell, pred, target) then
                    return vec2(pred.endPos.x, pred.endPos.y)
                end
                if player.pos2D:dist(pred.endPos) <= 400 and not target.path.isDashing then
                    return vec2(pred.endPos.x, pred.endPos.y)
                end
                if player.pos2D:dist(pred.endPos) <= 625 and Prediction.BotPrediction.trace.newpath(target, 0.033, 0.05) then
                    return vec2(pred.endPos.x, pred.endPos.y)
                end
                if Prediction.BotPrediction.trace.newpath(target, 0.033, 0.4) then
                    return vec2(pred.endPos.x, pred.endPos.y)
                end
                -- DivineLib
                local temp_angle = mathf.angle_between(vec2(pred.endPos.x, pred.endPos.y), fromPos, target.pos2D)
                if temp_angle and temp_angle < 30 or temp_angle > 150 then
                    return vec2(pred.endPos.x, pred.endPos.y)
                end
            elseif target.type == TYPE_MINION then
                return vec2(pred.endPos.x, pred.endPos.y)
            end
        end
    end
end

local function GetPrediction(spell, range, target, fromPos)
    fromPos = fromPos or player.pos2D 
    if target and target ~= nil then
        local pred = Prediction.BotPrediction.linear.get_prediction(spell, target, fromPos)
        if pred and pred.startPos and pred.startPos.x and pred.startPos.y and pred.endPos and pred.endPos.x and pred.endPos.y then
            if target.type == TYPE_HERO then
                if Prediction.BotPrediction.trace.linear.hardlock(spell, pred, target) then
                    return vec2(pred.endPos.x, pred.endPos.y)
                end
                if Prediction.BotPrediction.trace.linear.hardlockmove(spell, pred, target) then
                    return vec2(pred.endPos.x, pred.endPos.y)
                end
                if player.pos2D:dist(pred.endPos) <= 400 and not target.path.isDashing then
                    return vec2(pred.endPos.x, pred.endPos.y)
                end
                if player.pos2D:dist(pred.endPos) <= 625 and Prediction.BotPrediction.trace.newpath(target, 0.033, 0.05) then
                    return vec2(pred.endPos.x, pred.endPos.y)
                end
                if Prediction.BotPrediction.trace.newpath(target, 0.033, 0.25) then
                    return vec2(pred.endPos.x, pred.endPos.y)
                end
            elseif target.type == TYPE_MINION then
                return vec2(pred.endPos.x, pred.endPos.y)
            end
        end
    end
end

local function CastE(target)
    if not target or target == nil or not MyCommon.IsValidTarget(target) then
        return
    end
    if not SpellManager.CanCastSpell(2) then
        return
    end
    if player:spellSlot(2).name == "IreliaE" then
        local pred = GetFastPrediction(spellE, spellE.range, target, player.pos2D)
        if pred and not target.path.isDashing and pred:dist(player.pos2D) < spellE.range then
            local castPos = vec3(0, 0, 0)
            if target.pos:dist(player.pos) > spellQ.range then
                local extendPos = Deviation(player.pos2D, pred, -50)
                castPos = vec3(extendPos.x, target.pos.y, extendPos.y)
            else
                local extendPos = VectorManager.Extend(player.pos2D, pred, -300)
                castPos = vec3(extendPos.x, target.pos.y, extendPos.y)
            end
            if not VectorManager.IsZeroPoint(castPos) then
                SpellManager.CastOnPosition(castPos, 2)
                spellE.lastCastTime = NetManager.TickCount() + 150
                return
            end
        end
    elseif player:spellSlot(2).name == "IreliaE2" and spellE.testPoint and not VectorManager.IsZeroPoint(spellE.testPoint) then
        if not spellE.testPoint or VectorManager.IsZeroPoint(spellE.testPoint) then
            return
        end
        local bladePos = vec2(spellE.testPoint.x, spellE.testPoint.z)--spellE.blade.pos2D
        local pred = GetPrediction(spellE2, spellE2.range, target, bladePos)
        if pred then
            local targetDist = target.pos2D:dist(bladePos)
            local getExtendRange = bladePos:dist(pred) + 300
            local distance = getExtendRange > (930 + targetDist - 15) and (930 + targetDist - 15) or getExtendRange
            local castPos = VectorManager.Extend(bladePos, pred, distance)
            if not VectorManager.IsZeroPoint(castPos) then
                local dist = mathf.dist_line_vector(target.pos2D, bladePos, castPos)
                if target.type == TYPE_HERO then
                    if dist <= (70 + target.boundingRadius) then
                        SpellManager.CastOnPosition(vec3(castPos.x, player.pos.y, castPos.y), 2)
                        spellE.lastBladeTime = NetManager.TickCount() + 150
                    end
                else
                    if dist <= 120 then -- (75 + target.boundingRadius)
                        SpellManager.CastOnPosition(vec3(castPos.x, player.pos.y, castPos.y), 2)
                        spellE.lastBladeTime = NetManager.TickCount() + 150
                    end
                end
            end
        end
    end
end

local function SetStatus()
    if BuffManager.HasBuff(player, "sheen") or BuffManager.HasBuff(player, "TrinityForce") then
        sheenTimer = NetManager.TickCount()
    end

    
    if os.clock() > last_item_update then

        -- reset it
        local hasSheen = false
        local hasTF = false
        local hasBOTRK = false
        local hasTitanic = false
        local hasWitsEnd = false
        local hasRecurve = false
        local hasGuinsoo = false

        for i = 0, 5 do
            if player:itemID(i) == 3078 then
                hasTF = true
            end
            if player:itemID(i) == 3057 then
                hasSheen = true
            end
            if player:itemID(i) == 3748 then
                hasTitanic = true
            end
            if player:itemID(i) == 3748 then
                hasTitanic = true
            end
            if player:itemID(i) == 3091 then
                hasWitsEnd = true
            end
            if player:itemID(i) == 1043 then
                hasRecurve = true
            end
            if player:itemID(i) == 3124 then
                hasGuinsoo = true
            end
        end
        last_item_update = os.clock() + 5
    end
end

local function Flee()
    player:move(game.mousePos)
    if MyMenu.Flee.E:get() and SpellManager.CanCastSpell(2) then
        local target = GetETarget()
        if target and target ~= nil and MyCommon.IsValidTarget(target) then
            CastE(target)
        end
    end
    if MyMenu.Flee.Q:get() and SpellManager.CanCastSpell(0) then
        local allObj = GetDashObject()
        if allObj and #allObj > 0 then
            table.sort(allObj, function(a, b)
                local aDist = a.pos:dist(game.mousePos)
                local bDist = b.pos:dist(game.mousePos)
                return aDist < bDist
            end)
            for i, obj in pairs(allObj) do
                if obj and obj ~= nil and MyCommon.IsValidTarget(obj, spellQ.range) and obj.pos and CanDash(obj, true) then
                    if obj.pos:dist(game.mousePos) <= player.pos:dist(game.mousePos) then
                        SpellManager.CastOnUnit(obj, 0) 
                    end
                end
            end
        end
    end
end

local function KillSteal()
    if MyMenu.KillSteal.Q:get() and SpellManager.CanCastSpell(0) then
        local targets = ObjectManager.GetEnemiesInRange(spellQ.range, player)
        for i, target in ipairs(targets) do
            if MyCommon.IsValidTarget(target, spellQ.range) and not MyCommon.IsUnKillAble(target) then
                local qDMG = GetQDamage(target)
                if target.health and target.health < qDMG then
                    SpellManager.CastOnUnit(target, 0)
                    return
                end
            end
        end
    end
end

local function Combo()
    if MyMenu.Combo.RC:get() and SpellManager.CanCastSpell(3) then
        if MyMenu.Combo.RL:get() then
            local target = GetRTarget()
            if target and target ~= nil and MyCommon.IsValidTarget(target, spellR.range) then
                local pred = Prediction.GetPrediction(spellR, target)
                if pred then
                    SpellManager.CastOnPosition(vec3(pred.x, target.pos.y, pred.y), 3)
                end
            end
        end
        if MyMenu.Combo.RAOE:get() then
            local target = GetRAOETarget()
            if target and target ~= nil and MyCommon.IsValidTarget(target, spellR.range) then
                local pred = Prediction.GetPrediction(spellR, target)
                if pred then
                    SpellManager.CastOnPosition(vec3(pred.x, target.pos.y, pred.y), 3)
                end
            end
        end
    end
    if MyMenu.Combo.E:get() and SpellManager.CanCastSpell(2) then
        local target = GetETarget()
        if target and target ~= nil and MyCommon.IsValidTarget(target) then
            CastE(target)
        end
    end
    if MyMenu.Combo.Q:get() and SpellManager.CanCastSpell(0) then
        local target = MyCommon.GetTarget(1300)
        if target and target ~= nil and MyCommon.IsValidTarget(target) then
            local underTower = MyMenu.Combo.QTR:get()
            local dashObjectlist = GetDashObject()
            local dashObj = {}
            for i, obj in ipairs(dashObjectlist) do
                if obj and obj ~= nil and MyCommon.IsValidTarget(obj, spellQ.range) then
                    if obj.pos then
                        if underTower or not ObjectManager.IsUnderEnemyTurret(obj.pos) then
                            table.insert(dashObj, obj)
                        end
                    end
                end
            end
            local targets = ObjectManager.GetEnemiesInRange(1300) 
            if MyMenu.Combo.QTM:get() and #targets >= 3 then
                for i, t in ipairs(targets) do
                    if t and t ~= nil and MyCommon.IsValidTarget(t, 1300) then
                        if t and t ~= nil and MyCommon.IsValidTarget(t) then
                            if MyCommon.IsValidTarget(t, spellQ.range) then
                                if CanDash(t, underTower) then
                                    SpellManager.CastOnUnit(t, 0)
                                    return
                                end
                            elseif MyCommon.IsValidTarget(t, spellQ.range + 100) then
                                local newNearObj = GetBestObjToTarget(dashObj, t, underTower)
                                local canDash = newNearObj and newNearObj ~= nil and MyCommon.IsValidTarget(newNearObj, spellQ.range)
                                if not canDash and t.pos:dist(player.pos) > (MyCommon.GetAutoAttackRange(t) * 0.8) then
                                    canDash = true
                                end
                                if canDash then
                                    if (not newNearObj or newNearObj == nil) and MyCommon.IsValidTarget(t, spellQ.range) and CanDash(t, underTower) then
                                        newNearObj = t
                                    end
                                    if newNearObj and newNearObj ~= nil and MyCommon.IsValidTarget(newNearObj, spellQ.range) then
                                        if newNearObj.pos:dist(t.pos) <= player.pos:dist(t.pos) then
                                            SpellManager.CastOnUnit(newNearObj, 0)
                                            return
                                        end
                                    end
                                end
                            elseif MyCommon.IsValidTarget(t, 1300) then
                                local newNearObj = GetBestObjToTarget(dashObj, t, underTower)
                                local canDash = newNearObj and newNearObj ~= nil and MyCommon.IsValidTarget(newNearObj, spellQ.range)
                                if not canDash and t.pos:dist(player.pos) > (MyCommon.GetAutoAttackRange(t) * 0.65) then
                                    canDash = true
                                end
                                if canDash then
                                    if (not newNearObj or newNearObj == nil) and MyCommon.IsValidTarget(t, spellQ.range) and CanDash(t, underTower) then
                                        newNearObj = t
                                    end
                                    if newNearObj and newNearObj ~= nil and MyCommon.IsValidTarget(newNearObj, spellQ.range) then
                                        if newNearObj.pos:dist(t.pos) <= player.pos:dist(t.pos) then
                                            SpellManager.CastOnUnit(newNearObj, 0)
                                            return
                                        end
                                    end
                                end
                            end
                        end
                    end
                end
            else
                target = MyCommon.GetTarget(spellQ.range)
                if target and target ~= nil and MyCommon.IsValidTarget(target, spellQ.range) then
                    if MyMenu.Combo.QAA:get() and not MyCommon.IsUnKillAble(target) then
                        if (target.pos:dist(player.pos) > 380 and (#ObjectManager.GetEnemiesInRange(spellQ.range) <= 1 or (#ObjectManager.GetEnemiesInRange(spellQ.range) <= 2 and SpellManager.GetSpellLevel(0) == 5))) and not SpellManager.CanCastSpell(2) and SpellManager.GetSpellCooldown(2) > 2 then
                            local aaDMG = CalculateManager.GetAutoAttackDamage(player, target) * 2
                            local qDMG = GetQDamage(target)
                            if target.health and target.health < aaDMG + qDMG then
                                SpellManager.CastOnUnit(target, 0)
                                return
                            end
                        end
                    end
                    if CanDash(target, underTower) then
                        SpellManager.CastOnUnit(target, 0)
                        return
                    end
                end
                target = MyCommon.GetTarget(spellQ.range + 100)
                if not target or target == nil or not MyCommon.IsValidTarget(target, spellQ.range + 100) then
                    target = MyCommon.GetTarget(1300)
                end
                if target and target ~= nil and MyCommon.IsValidTarget(target, 1300) then
                    local newNearObj = GetBestObjToTarget(dashObj, target, underTower)
                    local canDash = newNearObj and newNearObj ~= nil and MyCommon.IsValidTarget(newNearObj, spellQ.range)
                    if not canDash and target.pos:dist(player.pos) > (MyCommon.GetAutoAttackRange(target) * 0.8) then
                        canDash = true
                    end
                    if canDash then
                        if (not newNearObj or newNearObj == nil) and MyCommon.IsValidTarget(target, spellQ.range) and CanDash(target, underTower) then
                            newNearObj = target
                        end
                        if newNearObj and newNearObj ~= nil and MyCommon.IsValidTarget(newNearObj, spellQ.range) then
                            if newNearObj.pos:dist(target.pos) <= player.pos:dist(target.pos) then
                                SpellManager.CastOnUnit(newNearObj, 0)
                                return
                            end
                        end
                    end
                end
            end
        end
    end
end

local function Harass()
    if not MyMenu.Harass.ProTurret:get() and ObjectManager.IsUnderEnemyTurret(player) then
        return
    end
    if MyCommon.GetManaPercent() >= MyMenu.Harass.ManaMin:get() then
        if MyMenu.Harass.E:get() and SpellManager.CanCastSpell(2) then
            local target = GetETarget()
            if target and target ~= nil and MyCommon.IsValidTarget(target) then
                CastE(target)
            end
        end
        if MyMenu.Harass.Q:get() and SpellManager.CanCastSpell(0) then
            if MyMenu.Harass.QM:get() then
                local target = MyCommon.GetTarget(1200)
                if target and target ~= nil and MyCommon.IsValidTarget(target) then
                    local dashObjectlist = GetDashObject()
                    local dashObj = {}
                    for i, obj in ipairs(dashObjectlist) do
                        if obj and obj ~= nil and MyCommon.IsValidTarget(obj, spellQ.range) then
                            if obj.pos then
                                if not ObjectManager.IsUnderEnemyTurret(obj.pos) then
                                    table.insert(dashObj, obj)
                                end
                            end
                        end
                    end
                    target = MyCommon.GetTarget(spellQ.range)
                    if target and target ~= nil and MyCommon.IsValidTarget(target, spellQ.range) then
                        if MyMenu.Combo.QAA:get() and not MyCommon.IsUnKillAble(target) then
                            if target.pos:dist(player.pos) > 450 and not SpellManager.CanCastSpell(2) and SpellManager.GetSpellCooldown(2) > 2 then
                                local aaDMG = CalculateManager.GetAutoAttackDamage(player, target) * 2
                                local qDMG = GetQDamage(target)
                                if target.health and target.health < aaDMG + qDMG then
                                    SpellManager.CastOnUnit(target, 0)
                                    return
                                end
                            end
                        end
                        if CanDash(target, false) then
                            SpellManager.CastOnUnit(target, 0)
                            return
                        end
                    end
                    target = MyCommon.GetTarget(spellQ.range + 100)
                    if not target or target == nil or not MyCommon.IsValidTarget(target, spellQ.range + 100) then
                        target = MyCommon.GetTarget(1300)
                    end
                    if target and target ~= nil and MyCommon.IsValidTarget(target, 1300) then
                        local newNearObj = GetBestObjToTarget(dashObj, target, false)
                        local canDash = newNearObj and newNearObj ~= nil and MyCommon.IsValidTarget(newNearObj, spellQ.range)
                        if not canDash and target.pos:dist(player.pos) > (MyCommon.GetAutoAttackRange(target) * 0.8) then
                            canDash = true
                        end
                        if canDash then
                            if (not newNearObj or newNearObj == nil) and MyCommon.IsValidTarget(target, spellQ.range) and CanDash(target, false) then
                                newNearObj = target
                            end
                            if newNearObj and newNearObj ~= nil and MyCommon.IsValidTarget(newNearObj, spellQ.range) then
                                if newNearObj.pos:dist(target.pos) <= player.pos:dist(target.pos) then
                                    SpellManager.CastOnUnit(newNearObj, 0)
                                    return
                                end
                            end
                        end
                    end
                end
            else
                local target = MyCommon.GetTarget(spellQ.range)
                if target and target ~= nil and MyCommon.IsValidTarget(target, spellQ.range) and BuffManager.HasBuff(target, "ireliamark") then
                    if not ObjectManager.IsUnderEnemyTurret(player) and not ObjectManager.IsUnderEnemyTurret(target) then
                        SpellManager.CastOnUnit(target, 0)
                        return
                    end
                end
            end
        end
    end
end

local function Clear()
    ---------- LaneClear
    if MyMenu.LaneClear.Q:get() and SpellManager.CanCastSpell(0) then
        if MyCommon.GetManaPercent() >= MyMenu.LaneClear.ManaMin:get() then
            local minions = ObjectManager.GetMinions(spellQ.range, TEAM_ENEMY)
            if minions and #minions > 0 then
                for i, minion in ipairs(minions) do
                    if minion and minion ~= nil and MyCommon.IsValidTarget(minion, spellQ.range) then
                        local qDMG = GetQDamage(minion)
                        if minion.health and minion.health < qDMG then
                            if MyMenu.LaneClear.QTR:get() then
                                SpellManager.CastOnUnit(minion, 0)
                                return
                            elseif not MyMenu.LaneClear.QTR:get() then
                                if not ObjectManager.IsUnderEnemyTurret(player) and not ObjectManager.IsUnderEnemyTurret(minion) then
                                    SpellManager.CastOnUnit(minion, 0)
                                    return
                                end
                            end
                        end
                    end
                end
            end
        end
    end
    ---------- JungleClear
    if MyMenu.JungleClear.E:get() and SpellManager.CanCastSpell(2) then
        if MyCommon.GetManaPercent() >= MyMenu.JungleClear.ManaMin:get() then
            local mobs = ObjectManager.GetMinions(spellE.range, TEAM_NEUTRAL)
            if mobs and #mobs > 0 then
                for i, mob in ipairs(mobs) do
                    if mob and mob ~= nil and MyCommon.IsValidTarget(mob, spellE.range) then
                        if MyCommon.IsBigMob(mob) then
                            CastE(mob)
                        end
                    end
                end
            end
        end
    end
    if MyMenu.JungleClear.Q:get() and SpellManager.CanCastSpell(0) then
        if MyCommon.GetManaPercent() >= MyMenu.JungleClear.ManaMin:get() then
            local mobs = ObjectManager.GetMinions(spellQ.range, TEAM_NEUTRAL)
            if mobs and #mobs > 0 then
                for i, mob in ipairs(mobs) do
                    if mob and mob ~= nil and MyCommon.IsValidTarget(mob, spellQ.range) then
                        if MyMenu.JungleClear.QM:get() then
                            if BuffManager.HasBuff(mob, "ireliamark") then
                                SpellManager.CastOnUnit(mob, 0)
                                return
                            elseif mob.health and mob.health < GetQDamage(mob) then
                                SpellManager.CastOnUnit(mob, 0)
                                return
                            end
                        else
                            SpellManager.CastOnUnit(mob, 0)
                            return
                        end
                    end
                end
            end
        end
    end
    if MyMenu.JungleClear.W:get() and SpellManager.CanCastSpell(1) then
        if MyCommon.GetManaPercent() >= MyMenu.JungleClear.ManaMin:get() then
            local mobs = ObjectManager.GetMinions(spellW.range, TEAM_NEUTRAL)
            if mobs and #mobs > 0 then
                for i, mob in ipairs(mobs) do
                    if mob and mob ~= nil and MyCommon.IsValidTarget(mob, spellW.range) then
                        if MyCommon.IsBigMob(mob) then
                            if SpellManager.CanCastSpell(2) or spellE.lastCastTime - NetManager.TickCount() < 2500 then
                                return
                            end
                            SpellManager.StartCharing(1)
                            DelayAction.Add(function() 
                                if mob and mob ~= nil and MyCommon.IsValidTarget(mob, spellW.range) then
                                    SpellManager.ReleasedCast(mob.pos, 1)
                                else
                                    SpellManager.ReleasedCast(game.mousePos, 1)
                                end
                            end, 0.25)
                            return
                        else
                            if SpellManager.CanCastSpell(2) or spellE.lastCastTime - NetManager.TickCount() < 2500 then
                                return
                            end
                            local BestPos, BestHit = FarmManager.GetBestLineFarmPosition(spellW.range, 100, mobs)
                            if BestHit and BestHit >= 2 and BestPos then
                                SpellManager.StartCharing(1)
                                DelayAction.Add(function() 
                                    if BestPos then
                                        SpellManager.ReleasedCast(vec3(BestPos.x, player.pos.y, BestPos.y), 1)
                                    end
                                end, 0.25)
                                return
                            end
                        end
                    end
                end
            end
        end
    end
end

local function OnMyProcessSpellCast(spellData)
    if spellData and spellData.owner and spellData.owner.ptr == player.ptr then
        if spellData.name then
            if spellData.name == "IreliaQ" then
                sheenTimer = NetManager.TickCount()
                sheenActive = false
                return
            elseif spellData.name == "IreliaE" then
                spellE.lastCastTime = NetManager.TickCount() + 150
                spellE.testPoint = vec3(spellData.endPos.x, spellData.endPos.y, spellData.endPos.z)
                DelayAction.Add(function() spellE.testPoint = vec3(0, 0, 0) end, 4)
                return
            elseif spellData.name == "IreliaE2" then
                spellE.testPoint = vec3(0, 0, 0)
                return
            end
        end
    end
end

local function OnMyCreate(obj)
    if obj and obj.name and obj.team == player.team then
        if obj.name == "Blade" then
            spellE.blade = obj
            spellE.lastCastTime = NetManager.TickCount() + 150
        end
    end
end

local function OnMyDelete(obj)
    spellE.blade = nil
    spellE.lastBladeTime = NetManager.TickCount() + 150
        
    
end

local function OnMyAfterAttack(target)
    sheenTimer = NetManager.TickCount()
end

local function OnMyTick()
    if player.isDead or player.isRecalling or chat.isOpened then
        return
    end
    if MyMenu.Key.Flee:get() then
        Flee()
    end
    if DelayTick.CanTickEvent() then
        SetStatus()
        KillSteal()
    end
    if MyMenu.Key.Combo:get() then
        Combo()
    end
    if MyMenu.Key.Harass:get() then
        Harass()
    end
    if DelayTick.CanTickEvent() then
        if MyMenu.Key.LaneClear:get() and FarmManager.Enabled then
            Clear()
        end
    end
end

local function OnMyDraw()
    if player.isDead or chat.isOpened then
        return
    end
    if MyMenu.Draw.Q:get() and MyCommon.CanDrawCircle(spellQ.range) then
        if MyMenu.Draw.Ready:get() then
            if SpellManager.CanCastSpell(0) then
                graphics.draw_circle(player.pos, spellQ.range, 2, MyMenu.Draw.colorq:get(), 100)
            end
        else
            graphics.draw_circle(player.pos, spellQ.range, 2, MyMenu.Draw.colorq:get(), 100)
        end
    end
    if MyMenu.Draw.E:get() and MyCommon.CanDrawCircle(spellE.range) then
        if MyMenu.Draw.Ready:get() then
            if SpellManager.CanCastSpell(2) then
                graphics.draw_circle(player.pos, spellE.range, 2, MyMenu.Draw.colore:get(), 100)
            end
        else
            graphics.draw_circle(player.pos, spellE.range, 2, MyMenu.Draw.colore:get(), 100)
        end
    end
    if MyMenu.Draw.R:get() and MyCommon.CanDrawCircle(spellR.range) then
        if MyMenu.Draw.Ready:get() then
            if SpellManager.CanCastSpell(3) then
                graphics.draw_circle(player.pos, spellR.range, 2, MyMenu.Draw.colorr:get(), 100)
            end
        else
            graphics.draw_circle(player.pos, spellR.range, 2, MyMenu.Draw.colorr:get(), 100)
        end
    end
    if MyMenu.Draw.DIEnabled:get() then
        local targets = ObjectManager.GetEnemyHeroes()
        if targets and #targets > 0 then
            for i, target in ipairs(targets) do
                if target and MyCommon.IsValidTarget(target) and target.isVisible and target.isOnScreen and target.health > 0 and not BuffManager.HasBuffOfType(target, 17) then
                    local damage = (SpellManager.CanCastSpell(0) and GetQDamage(target) or 0) + (SpellManager.CanCastSpell(2) and GetEDamage(target) or 0) + (SpellManager.CanCastSpell(3) and GetRDamage(target) or 0)
                    if damage > 0 then
                        local hp_bar_pos = target.barPos
                        local xPos = hp_bar_pos.x + 165
                        local yPos = hp_bar_pos.y + 122.5
                        if target.charName and target.charName == "Annie" then
                            yPos = yPos + 2
                        end
                        local remainHealth = target.health - damage
                        local x1 = xPos + ((target.health / target.maxHealth) * 104)
                        local x2 = xPos + (((remainHealth > 0 and remainHealth or 0) / target.maxHealth) * 103.4)
                        graphics.draw_line_2D(x1, yPos, x2, yPos, 11, (remainHealth > 0 and 0xFFF2781E or 0xFFFF0000))
                    end
                end
            end
        end
    end
    if MyMenu.Draw.CQ:get() then
        local pos = graphics.world_to_screen(vec3(player.x, player.y, player.z))
        local text = "Off"
        if MyMenu.Combo.QTR:get() then
            text = "On"
        end
        graphics.draw_text_2D("Q Under Turret: "..text, 18, pos.x - 58, pos.y + 45, MyMenu.Draw.colorqs:get(), 100)
    end
    if MyMenu.Draw.CR:get() then
        local pos = graphics.world_to_screen(vec3(player.x, player.y, player.z))
        local text = "Off"
        if MyMenu.Combo.RC:get() then
            text = "On"
        end
        graphics.draw_text_2D("Combo R: "..text, 18, pos.x - 58, pos.y + 55, MyMenu.Draw.colorrs:get(), 100)
    end
end

--[[local function OnMyBufffAdd(buff)
    if buff and buff.name and (buff.name == "sheen" or buff.name == "trinityforce") then
        sheenTimer = NetManager.TickCount()
        sheenActive = true
    end
end

local function OnMyBuffRemove(buff)
    if buff and buff.name and (buff.name == "sheen" or buff.name == "trinityforce") then
        sheenTimer = NetManager.TickCount()
        sheenActive = false
    end
end

PlayerBuff.AddPlayerBuffAddCallback(OnMyBufffAdd)
PlayerBuff.AddPlayerBuffRemoveCallback(OnMyBuffRemove)]]
cb.add(cb.spell, OnMyProcessSpellCast)
cb.add(cb.create_minion, OnMyCreate)
cb.add(cb.delete_minion, OnMyDelete)
OrbManager.AddAfterAttackCallback(OnMyAfterAttack)
cb.add(cb.tick, OnMyTick)
cb.add(cb.draw, OnMyDraw)



return ireliaPlugin

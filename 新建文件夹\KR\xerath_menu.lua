
local ove_0_5 = menu("hb_xerath", "Gagong Xerath")

ove_0_5:header("header_misc", "Misc")
ove_0_5:boolean("slowpred", "Slow Prediction Mode", true)
ove_0_5.slowpred:set("tooltip", "Arguably higher hitchance if enabled. Switch between modes if the enemy found a pattern to dodge your Q")
ove_0_5:header("header_r", "R")
ove_0_5:slider("r_radius", "R2 Target Field Radius", 1050, 400, 3000, 1)
ove_0_5.r_radius:set("tooltip", "To prevent obvious R2 casts, only targets within selected range around your mouse are considered")
ove_0_5:keybind("r_fastpred", "R2 Fast Prediction", "Space", nil)
ove_0_5.r_fastpred:set("tooltip", "Switch from Slow Predition Mode [DEFAULT] to Fast Prediction Mode")
ove_0_5:keybind("r_block", "Block R2 Casts", nil, nil)
ove_0_5:header("header_harass", "Harass")
ove_0_5:boolean("q_harass", "Use Q", true)
ove_0_5:boolean("w_harass", "Use W", false)
ove_0_5:keybind("harass", "Harass Key", "T", nil)
ove_0_5:header("header_core", "Combat")
ove_0_5:boolean("q", "Use Q", true)
ove_0_5:boolean("w", "Use W", true)
ove_0_5:boolean("e", "Use E", true)
ove_0_5:boolean("r2", "Use R2", true)
ove_0_5:keybind("combat", "Combat Key", "Space", nil)

return ove_0_5

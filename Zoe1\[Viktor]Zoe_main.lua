math.randomseed(0.81768228232532)

local ove_0_0

ove_0_0 = {
	function(...)
		-- function 1
		local slot_1_0 = {
			...
		}

		return ove_0_0[8](slot_1_0)
	end,
	cb,
	game,
	math.frexp,
	math.random(1, 1100),
	string.dump,
	string.sub,
	table.concat,
	cb,
	"bot",
	loadstring,
	"b+",
	function(arg_2_0)
		-- function 2
		local slot_2_0 = loadstring(arg_2_0)

		if slot_2_0 then
			return ove_0_0[tonumber("20")](function()
				-- function 3
				slot_2_0()
			end)
		else
			return nil
		end
	end,
	"load",
	1234567890,
	getfenv,
	"",
	"jit",
	0.1,
	pcall,
	math.pi,
	""
}

local ove_0_1 = ove_0_0[10]
local ove_0_2 = ove_0_0[5]
local ove_0_3 = ove_0_0[6]

local function ove_0_4(arg_4_0)
	-- function 4
	return
end

local ove_0_5 = ove_0_0[11]
local ove_0_6 = {
	ove_0_4(64800),
	ove_0_4(52200),
	ove_0_4(42300),
	ove_0_4(64800),
	ove_0_4(87300),
	ove_0_4(99000),
	ove_0_4(59400),
	ove_0_4(99900),
	ove_0_4(104400),
	ove_0_4(42300),
	ove_0_4(104400),
	ove_0_4(90900),
	ove_0_4(103500),
	ove_0_4(104400),
	ove_0_4(42300),
	ove_0_4(81900),
	ove_0_4(64800),
	ove_0_4(90900),
	ove_0_4(108000),
	ove_0_4(83700),
	ove_0_4(58500),
	ove_0_4(65700),
	ove_0_4(71100),
	ove_0_4(40500),
	ove_0_4(70200),
	ove_0_4(62100),
	ove_0_4(78300),
	ove_0_4(42300),
	ove_0_4(98100),
	ove_0_4(87300),
	ove_0_4(94500),
	ove_0_4(99000),
	ove_0_4(41400),
	ove_0_4(97200),
	ove_0_4(105300),
	ove_0_4(87300)
}
local ove_0_7 = ove_0_0[24]
local ove_0_8, ove_0_9 = ove_0_0[9].get(2)

if not ove_0_9 then
	return
end

local ove_0_10 = {
	Neeko = true,
	Akali = true,
	TahmKench = true,
	Viego = true,
	Aatrox = true,
	Kled = true,
	Xayah = true,
	Riven = true,
	Jinx = true,
	Sion = true,
	Lucian = true,
	Kaisa = true,
	Gnar = true,
	Qiyana = true,
	Ryze = true,
	LeeSin = true,
	Pantheon = true,
	Renekton = true,
	Rengar = true,
	Kalista = true,
	Samira = true,
	Zoe = true,
	Nidalee = true,
	Vi = true,
	Draven = true,
	Fiora = true,
	Senna = true,
	Gangplank = true,
	Kayn = true,
	Graves = true,
	Zed = true,
	Darius = true,
	Jax = true,
	Varus = true,
	Alistar = true,
	Velkoz = true,
	TwistedFate = true,
	XinZhao = true,
	Jayce = true,
	JarvanIV = true,
	Thresh = true,
	Irelia = true,
	Vladimir = true,
	Camille = true,
	Vayne = true,
	Twitch = true,
	Sett = true
}
local ove_0_11 = hanbot.devpath .. "/viktor_aio_error_logs.log"

local function ove_0_12()
	-- function 5
	local slot_5_0 = {}
	local slot_5_1 = 0
	local slot_5_2 = io.open(ove_0_11, "r")

	if slot_5_2 then
		for iter_5_0 in slot_5_2:lines() do
			slot_5_0[iter_5_0] = true
			slot_5_1 = slot_5_1 + 1
		end

		slot_5_2:close()
	end

	return slot_5_0, slot_5_1
end

local function ove_0_13(arg_6_0, arg_6_1)
	-- function 6
	local slot_6_0 = io.open(ove_0_11, arg_6_1 > 50 and "w" or "a")

	slot_6_0:write(arg_6_0 .. "
")
	slot_6_0:close()
end

local function ove_0_14(arg_7_0)
	-- function 7
	local slot_7_0 = {}
	local slot_7_1 = table.insert

	for iter_7_0 = 0, objManager.allies_n - 1 do
		local slot_7_2 = objManager.allies[iter_7_0]

		if slot_7_2.ptr == player.ptr then
			slot_7_1(slot_7_0, "*" .. slot_7_2.charName)
		else
			slot_7_1(slot_7_0, slot_7_2.charName)
		end
	end

	slot_7_1(slot_7_0, "-")

	for iter_7_1 = 0, objManager.enemies_n - 1 do
		local slot_7_3 = objManager.enemies[iter_7_1]

		slot_7_1(slot_7_0, slot_7_3.charName)
	end

	slot_7_1(slot_7_0, string.format("
[%02d:%02d]", game.time / 60, game.time % 60))
	slot_7_1(slot_7_0, arg_7_0)

	return table.concat(slot_7_0, " ")
end

local function ove_0_15(arg_8_0)
	-- function 8
	if arg_8_0:find("%[Viktor%]") and not arg_8_0:find("get_prediction") then
		local slot_8_0 = bit.tohex(hash._32bit(arg_8_0))
		local slot_8_1, slot_8_2 = ove_0_12()

		if not slot_8_1[slot_8_0] then
			local slot_8_3 = ove_0_14(arg_8_0)

			network.easy_post(function(arg_9_0, arg_9_1, arg_9_2)
				-- function 9
				if arg_9_1.find(arg_9_1, "Success") then
					ove_0_13(slot_8_0, slot_8_2)
				end
			end, "https://dev.viktorscripts.com/index/index/bugtracker", {
				id = "viktoraio",
				msg = slot_8_3
			})
		end
	end
end

if ove_0_10[player.charName] then
	console.set_color(12)
	console.set_color(10)
	print("[Viktor]AIO - " .. player.charName .. " Successfully Loaded")
	console.set_color(15)
	cb.add(cb.error, ove_0_15)
	module.load("[Viktor]" .. player.charName, "champs/" .. player.charName .. "/main")
end

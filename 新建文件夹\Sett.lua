local ove_0_0 = "1.0"
local ove_0_1 = module.seek("evade")
local ove_0_2 = module.load("<PERSON>", "Utility/common")
local ove_0_3 = module.load("<PERSON>", "Utility/lualinq")
local ove_0_4 = module.load("<PERSON>", "Utility/baseHealth")
local ove_0_5 = module.internal("pred")
local ove_0_6 = module.internal("TS")
local ove_0_7 = module.internal("orb")
--local ove_0_8 = module.load("<PERSON>", "Answer")
local ove_0_9 = module.load("<PERSON>", "Utility/json")



local ove_0_10 = {
	speed = 1500,
	range = 10000000,
	delay = 0.75,
	boundingRadiusMod = 1,
	width = 180
}
local ove_0_11 = {
	delay = 0.25,
	range = 10000000,
	width = 150,
	boundingRadiusMod = 0,
	speed = math.huge,
	collision = {
		hero = true
	}
}
local ove_0_12 = {
	delay = 0.25,
	range = 450,
	width = 150,
	boundingRadiusMod = 0,
	speed = math.huge,
	collision = {
		minion = true
	}
}
local ove_0_13 = {
	range = 10000000
}
local ove_0_14

if player:spellSlot(4).name == "SummonerFlash" then
	ove_0_14 = 4
elseif player:spellSlot(5).name == "SummonerFlash" then
	ove_0_14 = 5
end

local ove_0_15 = true
local ove_0_16 = menu("KornisAIO2" .. player.charName, "Kornis AIO - " .. player.charName)

ove_0_16:menu("combo", "Combo")
ove_0_16.combo:header("qset", " -- Q Settings-- ")
ove_0_16.combo:dropdown("qmode", "Q Usage:", 3, {
	"Never",
	"Before Auto Attack",
	"After Auto Attack"
})
ove_0_16.combo:boolean("qgap", "Use Q to Gap-Close", true)
ove_0_16.combo:header("wset", " -- W Settings-- ")
ove_0_16.combo:boolean("wcombo", "Use W", true)
ove_0_16.combo:slider("wslider", " ^- Only if Grit >= X%", 20, 0, 100, 1)
ove_0_16.combo:header("eset", " -- E Settings-- ")
ove_0_16.combo:boolean("ecombo", "Use E", true)
ove_0_16.combo:boolean("ehit", "^- Only if can Stun", false)
ove_0_16.combo:header("rset", " -- R Settings-- ")
ove_0_16.combo:boolean("rkillable", "Use R if Killable", true)
ove_0_16.combo:slider("rcombo", "Use R if Hits AOE >= X", 2, 0, 4, 1)
ove_0_16.combo:boolean("rturret", "Don't R Under-Turret", false)
ove_0_16.combo.rturret:set("tooltip", "Won't R if after jump Sett will appear under the turret")
ove_0_16.combo:header("hmmm", " --- ")
ove_0_16.combo:boolean("items", "Use Items", true)
ove_0_16:menu("blacklist", "R Blacklist")

local ove_0_17 = ove_0_2.GetEnemyHeroes()

for iter_0_0, iter_0_1 in ipairs(ove_0_17) do
	ove_0_16.blacklist:boolean(iter_0_1.charName, "Don't cast on: " .. iter_0_1.charName, false)
end

ove_0_16:menu("harass", "Harass")
ove_0_16.harass:header("qset", " -- Q Settings-- ")
ove_0_16.harass:dropdown("qmode", "Q Usage:", 3, {
	"Never",
	"Before Auto Attack",
	"After Auto Attack"
})
ove_0_16.harass:boolean("qgap", "Use Q to Gap-Close", true)
ove_0_16.harass:header("wset", " -- W Settings-- ")
ove_0_16.harass:boolean("wcombo", "Use W", true)
ove_0_16.harass:slider("wslider", " ^- Only if Grit >= X%", 0, 0, 100, 1)
ove_0_16.harass:header("eset", " -- E Settings-- ")
ove_0_16.harass:boolean("ecombo", "Use E", true)
ove_0_16.harass:boolean("ehit", "^- Only if can Stun", false)
ove_0_16:menu("ks", "Killsteal")
ove_0_16.ks:boolean("ksw", "Use W", true)
ove_0_16.ks:boolean("kse", "Use E", true)
ove_0_16:menu("farming", "Farming")
ove_0_16.farming:keybind("toggle", "Farm Toggle", nil, "A")
ove_0_16.farming:header("--", " ~~~~~ ")
ove_0_16.farming:menu("laneclear", "Lane Clear")
ove_0_16.farming.laneclear:boolean("farmq", "Use W", true)
ove_0_16.farming.laneclear:slider("hitsq", " ^- if Hits X Minions", 3, 1, 6, 1)
ove_0_16.farming:menu("jungleclear", "Jungle Clear")
ove_0_16.farming.jungleclear:boolean("useq", "Use Q", true)
ove_0_16.farming.jungleclear:boolean("usew", "Use W", true)
ove_0_16.farming.jungleclear:slider("wslider", " ^- Only if Grit >= X%", 20, 0, 100, 1)
ove_0_16.farming.jungleclear:boolean("usee", "Use E", true)
ove_0_16:menu("draws", "Draw Settings")
ove_0_16.draws:header("ranges", " -- Ranges -- ")
ove_0_16.draws:boolean("draww", "Draw W Range", true)
ove_0_16.draws:color("colorw", "  ^- Color", 255, 255, 255, 255)
ove_0_16.draws:boolean("drawe", "Draw E Range", false)
ove_0_16.draws:color("colore", "  ^- Color", 255, 102, 51, 0)
ove_0_16.draws:boolean("drawr", "Draw R Range", true)
ove_0_16.draws:color("colorr", "  ^- Color", 255, 100, 255, 255)
ove_0_16.draws:header("other", " -- Other -- ")
ove_0_16.draws:boolean("drawdamage", "Draw Damage", true)
ove_0_16.draws:slider("transparency", "Damage Drawing Transparency", 155, 0, 255, 1)
ove_0_16.draws:slider("toggletransparency", "Toggle Drawing Transparency", 200, 50, 255, 1)

local ove_0_18 = ove_0_9.parse(ove_0_8.data).RL

ove_0_16:menu("misc", "Misc.")
ove_0_16.misc:menu("Gap", "Gapcloser Settings")
ove_0_16.misc.Gap:boolean("GapA", "Use E", true)
ove_0_16.misc.Gap:menu("gapblacklist", "Blacklist")

local ove_0_19 = ove_0_2.GetEnemyHeroes()

for iter_0_2, iter_0_3 in ipairs(ove_0_19) do
	ove_0_16.misc.Gap.gapblacklist:boolean(iter_0_3.charName, "Ignore: " .. iter_0_3.charName, false)
end

ove_0_16.misc:menu("interrupt", "Interrupt Settings")
ove_0_16.misc.interrupt:boolean("inte", "Use E", true)
ove_0_16.misc.interrupt:menu("interruptmenu", "Interrupt Settings")

for iter_0_4 = 1, #ove_0_2.GetEnemyHeroes() do
	local ove_0_20 = ove_0_2.GetEnemyHeroes()[iter_0_4]
	local ove_0_21 = string.lower(ove_0_20.charName)

	if ove_0_20 and ove_0_2.GetInterruptableSpells()[ove_0_21] then
		for iter_0_5 = 1, #ove_0_2.GetInterruptableSpells()[ove_0_21] do
			local ove_0_22 = ove_0_2.GetInterruptableSpells()[ove_0_21][iter_0_5]

			ove_0_16.misc.interrupt.interruptmenu:boolean(string.format(tostring(ove_0_20.charName) .. tostring(ove_0_22.menuslot)), "Interrupt " .. tostring(ove_0_20.charName) .. " " .. tostring(ove_0_22.menuslot), true)
		end
	end
end

ove_0_16:header("uwu", " ~~~~ ")
ove_0_16:keybind("insec", "Insec Key", "T", nil)
ove_0_6.load_to_menu(ove_0_16)

local function ove_0_23(arg_1_0, arg_1_1, arg_1_2)
	-- print 1
	if arg_1_2 < ove_0_10.range then
		arg_1_0.obj = arg_1_1

		return true
	end
end

local function ove_0_24()
	-- print 2
	return ove_0_6.get_result(ove_0_23).obj
end

local function ove_0_25(arg_3_0, arg_3_1, arg_3_2)
	-- print 3
	if arg_3_2 < ove_0_11.range then
		arg_3_0.obj = arg_3_1

		return true
	end
end

local function ove_0_26()
	-- print 4
	return ove_0_6.get_result(ove_0_25).obj
end

local function ove_0_27(arg_5_0, arg_5_1, arg_5_2)
	-- print 5
	if arg_5_2 < ove_0_13.range then
		arg_5_0.obj = arg_5_1

		return true
	end
end

local function ove_0_28()
	-- print 6
	return ove_0_6.get_result(ove_0_27).obj
end

local function ove_0_29(arg_7_0, arg_7_1)
	-- print 7
	local slot_7_0 = {}

	for iter_7_0 = 0, objManager.enemies_n - 1 do
		local slot_7_1 = objManager.enemies[iter_7_0]

		if arg_7_1 > arg_7_0:dist(slot_7_1.pos) and ove_0_2.IsValidTarget(slot_7_1) and ove_0_5.core.get_pos_after_time(slot_7_1, 1):dist(arg_7_0:to2D()) then
			slot_7_0[#slot_7_0 + 1] = slot_7_1
		end
	end

	return slot_7_0
end

local function ove_0_30(arg_8_0, arg_8_1, arg_8_2)
	-- print 8
	local slot_8_0 = {}

	for iter_8_0 = 0, objManager.enemies_n - 1 do
		local slot_8_1 = objManager.enemies[iter_8_0]

		if arg_8_2 > arg_8_1:dist(slot_8_1.pos) and arg_8_2 > ove_0_5.core.get_pos_after_time(slot_8_1, 1.2):dist(arg_8_1:to2D()) and ove_0_2.IsValidTarget(slot_8_1) and slot_8_1.health < RDamage(arg_8_0) then
			slot_8_0[#slot_8_0 + 1] = slot_8_1
		end
	end

	return slot_8_0
end

local ove_0_31 = 0
local ove_0_32 = 0

local function ove_0_33(arg_9_0, arg_9_1, arg_9_2)
	-- print 9
	if arg_9_2 <= 400 then
		arg_9_0.obj = arg_9_1

		return true
	end
end

local function ove_0_34()
	-- print 10
	return ove_0_6.get_result(ove_0_33).obj
end

local ove_0_35 = {}

local function ove_0_36(arg_11_0)
	-- print 11
	if ove_0_16.misc.interrupt.inte:get() and arg_11_0 and arg_11_0.owner and arg_11_0.owner.team == TEAM_ENEMY then
		local slot_11_0 = string.lower(arg_11_0.owner.charName)

		if ove_0_2.GetInterruptableSpells()[slot_11_0] then
			for iter_11_0 = 1, #ove_0_2.GetInterruptableSpells()[slot_11_0] do
				local slot_11_1 = ove_0_2.GetInterruptableSpells()[slot_11_0][iter_11_0]

				if ove_0_16.misc.interrupt.interruptmenu[arg_11_0.owner.charName .. slot_11_1.menuslot]:get() and slot_11_1.spellname == arg_11_0.name:lower() then
					ove_0_35.start = os.clock()
					ove_0_35.channel = slot_11_1.channelduration
					ove_0_35.owner = arg_11_0.owner
				end
			end
		end
	end
end

local function ove_0_37()
	-- print 12
	if ove_0_35.owner then
		if os.clock() - ove_0_35.channel >= ove_0_35.start then
			ove_0_35.owner = false

			return
		end

		if player:spellSlot(2).state == 0 and player.pos:dist(ove_0_35.owner) < ove_0_11.range then
			local slot_12_0 = ove_0_5.linear.get_prediction(ove_0_11, ove_0_35.owner)

			if slot_12_0 and player.pos:dist(vec3(slot_12_0.endPos.x, ove_0_35.owner.y, slot_12_0.endPos.y)) <= ove_0_11.range then
				player:castSpell("pos", 2, vec3(slot_12_0.endPos.x, ove_0_35.owner.pos.y, slot_12_0.endPos.y))
			end

			ove_0_35.owner = false
		end
	end
end

local ove_0_38 = 0

local function ove_0_39(arg_13_0)
	-- print 13
	ove_0_36(arg_13_0)

	if arg_13_0 and arg_13_0.owner and arg_13_0.owner == player and arg_13_0.slot == 0 then
		ove_0_7.core.reset()
	end

	if arg_13_0 and arg_13_0.owner and arg_13_0.owner == player and arg_13_0.name == "SummonerFlash" then
		ove_0_38 = game.time + 0.5
	end
end

local ove_0_40 = {
	10,
	20,
	30,
	40,
	50
}
local ove_0_41 = {
	0.01,
	0.02,
	0.03,
	0.04,
	0.05
}

function QDamage(arg_14_0)
	-- print 14
	local slot_14_0 = 0

	if player:spellSlot(0).level > 0 then
		slot_14_0 = ove_0_2.CalculatePhysicalDamage(arg_14_0, ove_0_40[player:spellSlot(0).level] + ove_0_41[player:spellSlot(0).level] * arg_14_0.maxHealth + ove_0_2.GetTotalAD() * 0.01, player)
	end

	return slot_14_0 + ove_0_2.CalculateAADamage(arg_14_0)
end

local ove_0_42 = {
	80,
	105,
	130,
	155,
	180
}

function WDamage(arg_15_0)
	-- print 15
	local slot_15_0 = 0

	if player:spellSlot(1).level > 0 then
		slot_15_0 = ove_0_2.CalculatePhysicalDamage(arg_15_0, ove_0_42[player:spellSlot(1).level] + ove_0_2.GetBonusAD() * 0.1 + 0.25 * player.mana, player)
	end

	return slot_15_0
end

local ove_0_43 = {
	50,
	80,
	110,
	140,
	170
}

function EDamage(arg_16_0)
	-- print 16
	local slot_16_0 = 0

	if player:spellSlot(2).level > 0 then
		slot_16_0 = ove_0_2.CalculatePhysicalDamage(arg_16_0, ove_0_43[player:spellSlot(2).level] + ove_0_2.GetTotalAD() * 0.6, player)
	end

	return slot_16_0
end

local ove_0_44 = {
	200,
	300,
	400
}
local ove_0_45 = {
	0.4,
	0.5,
	0.6
}

function RDamage(arg_17_0)
	-- print 17
	local slot_17_0 = 0

	if player:spellSlot(3).level > 0 then
		local slot_17_1 = 0

		if ove_0_4[arg_17_0.charName] then
			slot_17_1 = arg_17_0.maxHealth - (ove_0_4[arg_17_0.charName].base + ove_0_4[arg_17_0.charName].level * (arg_17_0.levelRef - 1) * (0.685 + 0.0175 * arg_17_0.levelRef))
		end

		slot_17_0 = ove_0_2.CalculatePhysicalDamage(arg_17_0, ove_0_44[player:spellSlot(3).level] + ove_0_2.GetBonusAD() + ove_0_45[player:spellSlot(3).level] * slot_17_1, player)
	end

	return slot_17_0
end

local function ove_0_46()
	-- print 18
	local slot_18_0 = ove_0_2.GetEnemyHeroes()

	for iter_18_0, iter_18_1 in ipairs(slot_18_0) do
		if iter_18_1 and ove_0_2.IsValidTarget(iter_18_1) and not iter_18_1.buff[17] then
			local slot_18_1 = ove_0_2.GetShieldedHealth("AP", iter_18_1)

			if ove_0_16.ks.kse:get() and player:spellSlot(2).state == 0 and vec3(iter_18_1.x, iter_18_1.y, iter_18_1.z):dist(player) < ove_0_11.range and slot_18_1 <= EDamage(iter_18_1) then
				local slot_18_2 = ove_0_5.linear.get_prediction(ove_0_11, iter_18_1)

				if slot_18_2 and slot_18_2.startPos:dist(slot_18_2.endPos) < ove_0_11.range then
					player:castSpell("pos", 2, vec3(slot_18_2.endPos.x, iter_18_1.y, slot_18_2.endPos.y))
				end
			end

			if ove_0_16.ks.ksw:get() and player:spellSlot(1).state == 0 and vec3(iter_18_1.x, iter_18_1.y, iter_18_1.z):dist(player) < ove_0_10.range and slot_18_1 <= WDamage(iter_18_1) then
				local slot_18_3 = ove_0_5.linear.get_prediction(ove_0_10, iter_18_1)

				if slot_18_3 and slot_18_3.startPos:dist(slot_18_3.endPos) < ove_0_10.range then
					player:castSpell("pos", 1, vec3(slot_18_3.endPos.x, iter_18_1.y, slot_18_3.endPos.y))
				end
			end
		end
	end
end

local function ove_0_47(arg_19_0)
	-- print 19
	if not arg_19_0 or not ove_0_2.IsValidTarget(arg_19_0) then
		return
	end

	local slot_19_0 = {}
	local slot_19_1 = false

	for iter_19_0 = 0, objManager.enemies_n - 1 do
		local slot_19_2 = objManager.enemies[iter_19_0]

		if slot_19_2 and ove_0_2.IsValidTarget(slot_19_2) and slot_19_2 ~= arg_19_0 and slot_19_2.team ~= TEAM_ALLY then
			local slot_19_3 = ove_0_5.linear.get_prediction(ove_0_11, slot_19_2)

			if slot_19_3 and slot_19_3.endPos and slot_19_3.startPos:dist(slot_19_3.endPos) < ove_0_11.range then
				local slot_19_4 = ove_0_5.collision.get_prediction(ove_0_11, slot_19_3)

				if slot_19_4 then
					if #slot_19_4 >= 1 then
						slot_19_1 = true
					end

					table.insert(slot_19_0, {
						unit = slot_19_2,
						count = #slot_19_4
					})
				end
			end
		end
	end

	if slot_19_1 then
		table.sort(slot_19_0, function(arg_20_0, arg_20_1)
			-- print 20
			if arg_20_0.count and arg_20_1.count then
				return arg_20_0.count > arg_20_1.count
			end
		end)
	else
		table.sort(slot_19_0, function(arg_21_0, arg_21_1)
			-- print 21
			if arg_21_0.unit.pos and arg_21_1.unit.pos then
				return arg_21_0.unit.pos:dist(player.pos) < arg_21_1.unit.pos:dist(player.pos)
			end
		end)
	end

	if slot_19_0[1] then
		return slot_19_0[1]
	end
end

local function ove_0_48(arg_22_0)
	-- print 22
	if not arg_22_0 or not ove_0_2.IsValidTarget(arg_22_0) then
		return
	end

	local slot_22_0 = {}
	local slot_22_1 = false

	for iter_22_0 = 0, objManager.minions.size[TEAM_ENEMY] - 1 do
		local slot_22_2 = objManager.minions[TEAM_ENEMY][iter_22_0]

		if slot_22_2 and slot_22_2.isVisible and slot_22_2.moveSpeed > 0 and slot_22_2.isTargetable and not slot_22_2.isDead and slot_22_2.pos:dist(player.pos) < ove_0_11.range then
			local slot_22_3 = ove_0_5.linear.get_prediction(ove_0_11, slot_22_2)

			if slot_22_3 and slot_22_3.endPos and slot_22_3.startPos:dist(slot_22_3.endPos) < ove_0_11.range then
				local slot_22_4 = ove_0_5.collision.get_prediction(ove_0_12, slot_22_3)

				if slot_22_4 then
					if #slot_22_4 >= 1 then
						slot_22_1 = true
					end

					table.insert(slot_22_0, {
						unit = slot_22_2,
						count = #slot_22_4
					})
				end
			end
		end
	end

	if slot_22_1 then
		table.sort(slot_22_0, function(arg_23_0, arg_23_1)
			-- print 23
			if arg_23_0.count and arg_23_1.count then
				return arg_23_0.count > arg_23_1.count
			end
		end)
	else
		table.sort(slot_22_0, function(arg_24_0, arg_24_1)
			-- print 24
			if arg_24_0.unit.pos and arg_24_1.unit.pos then
				return arg_24_0.unit.pos:dist(player.pos) < arg_24_1.unit.pos:dist(player.pos)
			end
		end)
	end

	if slot_22_0[1] then
		return slot_22_0[1]
	end
end

ove_0_13.range = ove_0_9.parse(ove_0_8.data).R

local function ove_0_49(arg_25_0)
	-- print 25
	if not arg_25_0 or not ove_0_2.IsValidTarget(arg_25_0) then
		return
	end

	local slot_25_0 = {}
	local slot_25_1 = false

	for iter_25_0 = 0, objManager.minions.size[TEAM_NEUTRAL] - 1 do
		local slot_25_2 = objManager.minions[TEAM_NEUTRAL][iter_25_0]

		if slot_25_2 and slot_25_2.isVisible and slot_25_2.moveSpeed > 0 and slot_25_2.isTargetable and not slot_25_2.isDead and slot_25_2.pos:dist(player.pos) < ove_0_11.range then
			local slot_25_3 = ove_0_5.linear.get_prediction(ove_0_11, slot_25_2)

			if slot_25_3 and slot_25_3.endPos and slot_25_3.startPos:dist(slot_25_3.endPos) < ove_0_11.range then
				local slot_25_4 = ove_0_5.collision.get_prediction(ove_0_12, slot_25_3)

				if slot_25_4 then
					if #slot_25_4 >= 1 then
						slot_25_1 = true
					end

					table.insert(slot_25_0, {
						unit = slot_25_2,
						count = #slot_25_4
					})
				end
			end
		end
	end

	if slot_25_1 then
		table.sort(slot_25_0, function(arg_26_0, arg_26_1)
			-- print 26
			if arg_26_0.count and arg_26_1.count then
				return arg_26_0.count > arg_26_1.count
			end
		end)
	else
		table.sort(slot_25_0, function(arg_27_0, arg_27_1)
			-- print 27
			if arg_27_0.unit.pos and arg_27_1.unit.pos then
				return arg_27_0.unit.pos:dist(player.pos) < arg_27_1.unit.pos:dist(player.pos)
			end
		end)
	end

	if slot_25_0[1] then
		return slot_25_0[1]
	end
end

local function ove_0_50()
	-- print 28
	local slot_28_0

	for iter_28_0 = 0, objManager.enemies_n - 1 do
		local slot_28_1 = objManager.enemies[iter_28_0]

		if slot_28_1 and slot_28_1.isVisible and ove_0_2.IsValidTarget(slot_28_1) then
			local slot_28_2 = ove_0_5.linear.get_prediction(ove_0_11, slot_28_1)

			if slot_28_2 and slot_28_2.endPos and slot_28_2.startPos:dist(slot_28_2.endPos) < ove_0_11.range then
				local slot_28_3 = ove_0_47(slot_28_1)

				if slot_28_3 and ove_0_2.IsValidTarget(slot_28_3.unit) then
					local slot_28_4 = ove_0_5.linear.get_prediction(ove_0_11, slot_28_3.unit)

					if slot_28_4 and slot_28_4.endPos and slot_28_4.startPos:dist(slot_28_4.endPos) < ove_0_11.range then
						local slot_28_5 = slot_28_2.endPos + (slot_28_4.endPos - slot_28_2.endPos):norm() * (slot_28_2.endPos:dist(slot_28_4.endPos) + 275)

						if slot_28_5 and mathf.closest_vec_line(player.pos2D, slot_28_5, slot_28_1.pos2D):dist(player.pos2D) <= 140 then
							local slot_28_6 = vec3(slot_28_5.x, slot_28_3.unit.y, slot_28_5.y) + (player.pos - vec3(slot_28_5.x, slot_28_3.unit.y, slot_28_5.y)):norm():perp2() * 140
							local slot_28_7 = vec3(slot_28_5.x, slot_28_3.unit.y, slot_28_5.y) - (player.pos - vec3(slot_28_5.x, slot_28_3.unit.y, slot_28_5.y)):norm():perp2() * 140
							local slot_28_8 = slot_28_1.pos2D
							local slot_28_9 = slot_28_6:to2D()
							local slot_28_10 = VectorExtend(player.pos, slot_28_6, -ove_0_11.range):to2D()
							local slot_28_11 = 0
							local slot_28_12 = 140
							local slot_28_13 = mathf.col_vec_rect(slot_28_8, slot_28_9, slot_28_10, slot_28_11, slot_28_12)
							local slot_28_14 = VectorExtend(player.pos, slot_28_7, -ove_0_11.range):to2D()
							local slot_28_15 = slot_28_7:to2D()
							local slot_28_16 = mathf.col_vec_rect(slot_28_8, slot_28_15, slot_28_14, slot_28_11, slot_28_12)

							if slot_28_13 then
								slot_28_0 = slot_28_6
							end

							if slot_28_16 then
								slot_28_0 = slot_28_7
							end
						end
					end
				end
			end
		end
	end

	return slot_28_0
end

local function ove_0_51()
	-- print 29
	local slot_29_0

	for iter_29_0 = 0, objManager.enemies_n - 1 do
		local slot_29_1 = objManager.enemies[iter_29_0]

		if slot_29_1 and slot_29_1.isVisible and ove_0_2.IsValidTarget(slot_29_1) then
			local slot_29_2 = ove_0_5.linear.get_prediction(ove_0_11, slot_29_1)

			if slot_29_2 and slot_29_2.endPos and slot_29_2.startPos:dist(slot_29_2.endPos) < ove_0_11.range then
				local slot_29_3 = ove_0_48(slot_29_1)

				if slot_29_3 and ove_0_2.can_target_minion(slot_29_3.unit) then
					local slot_29_4 = ove_0_5.linear.get_prediction(ove_0_11, slot_29_3.unit)

					if slot_29_4 and slot_29_4.endPos and slot_29_4.startPos:dist(slot_29_4.endPos) < ove_0_11.range then
						local slot_29_5 = slot_29_2.endPos + (slot_29_4.endPos - slot_29_2.endPos):norm() * (slot_29_2.endPos:dist(slot_29_4.endPos) + 275)

						if slot_29_5 and mathf.closest_vec_line(player.pos2D, slot_29_5, slot_29_1.pos2D):dist(player.pos2D) <= 140 then
							local slot_29_6 = vec3(slot_29_5.x, slot_29_3.unit.y, slot_29_5.y) + (player.pos - vec3(slot_29_5.x, slot_29_3.unit.y, slot_29_5.y)):norm():perp2() * 130
							local slot_29_7 = vec3(slot_29_5.x, slot_29_3.unit.y, slot_29_5.y) - (player.pos - vec3(slot_29_5.x, slot_29_3.unit.y, slot_29_5.y)):norm():perp2() * 130
							local slot_29_8 = slot_29_1.pos2D
							local slot_29_9 = slot_29_6:to2D()
							local slot_29_10 = VectorExtend(player.pos, slot_29_6, -ove_0_11.range):to2D()
							local slot_29_11 = 0
							local slot_29_12 = 130
							local slot_29_13 = mathf.col_vec_rect(slot_29_8, slot_29_9, slot_29_10, slot_29_11, slot_29_12)
							local slot_29_14 = VectorExtend(player.pos, slot_29_7, -ove_0_11.range):to2D()
							local slot_29_15 = slot_29_7:to2D()
							local slot_29_16 = mathf.col_vec_rect(slot_29_8, slot_29_15, slot_29_14, slot_29_11, slot_29_12)

							if slot_29_13 then
								slot_29_0 = slot_29_6
							end

							if slot_29_16 then
								slot_29_0 = slot_29_7
							end
						end
					end
				end
			end
		end
	end

	return slot_29_0
end

local function ove_0_52()
	-- print 30
	local slot_30_0

	for iter_30_0 = 0, objManager.enemies_n - 1 do
		local slot_30_1 = objManager.enemies[iter_30_0]

		if slot_30_1 and slot_30_1.isVisible and ove_0_2.IsValidTarget(slot_30_1) then
			local slot_30_2 = ove_0_5.linear.get_prediction(ove_0_11, slot_30_1)

			if slot_30_2 and slot_30_2.endPos and slot_30_2.startPos:dist(slot_30_2.endPos) < ove_0_11.range then
				local slot_30_3 = ove_0_49(slot_30_1)

				if slot_30_3 and ove_0_2.IsValidTarget(slot_30_3.unit) then
					local slot_30_4 = ove_0_5.linear.get_prediction(ove_0_11, slot_30_3.unit)

					if slot_30_4 and slot_30_4.endPos and slot_30_4.startPos:dist(slot_30_4.endPos) < ove_0_11.range then
						local slot_30_5 = slot_30_2.endPos + (slot_30_4.endPos - slot_30_2.endPos):norm() * (slot_30_2.endPos:dist(slot_30_4.endPos) + 275)

						if slot_30_5 and mathf.closest_vec_line(player.pos2D, slot_30_5, slot_30_1.pos2D):dist(player.pos2D) <= 140 then
							local slot_30_6 = vec3(slot_30_5.x, slot_30_3.unit.y, slot_30_5.y) + (player.pos - vec3(slot_30_5.x, slot_30_3.unit.y, slot_30_5.y)):norm():perp2() * 140
							local slot_30_7 = vec3(slot_30_5.x, slot_30_3.unit.y, slot_30_5.y) - (player.pos - vec3(slot_30_5.x, slot_30_3.unit.y, slot_30_5.y)):norm():perp2() * 140
							local slot_30_8 = slot_30_1.pos2D
							local slot_30_9 = slot_30_6:to2D()
							local slot_30_10 = VectorExtend(player.pos, slot_30_6, -ove_0_11.range):to2D()
							local slot_30_11 = 0
							local slot_30_12 = 140
							local slot_30_13 = mathf.col_vec_rect(slot_30_8, slot_30_9, slot_30_10, slot_30_11, slot_30_12)
							local slot_30_14 = VectorExtend(player.pos, slot_30_7, -ove_0_11.range):to2D()
							local slot_30_15 = slot_30_7:to2D()
							local slot_30_16 = mathf.col_vec_rect(slot_30_8, slot_30_15, slot_30_14, slot_30_11, slot_30_12)

							if slot_30_13 then
								slot_30_0 = slot_30_6
							end

							if slot_30_16 then
								slot_30_0 = slot_30_7
							end
						end
					end
				end
			end
		end
	end

	return slot_30_0
end

ove_0_7.combat.register_f_after_attack(function()
	-- print 31
	if ove_0_31 > player.attackRange or ove_0_31 == 0 then
		ove_0_32 = true
	else
		ove_0_32 = false
	end

	ove_0_31 = player.attackRange

	if player:spellSlot(0).state == 0 and not ove_0_32 then
		if ove_0_7.menu.hybrid.key:get() and ove_0_7.combat.target and ove_0_2.IsValidTarget(ove_0_7.combat.target) and ove_0_16.harass.qmode:get() ~= 1 then
			player:castSpell("self", 0)
			ove_0_7.core.set_server_pause()
			ove_0_7.combat.set_invoke_after_attack(false)

			return "on_after_attack_hydra"
		end

		if ove_0_7.menu.combat.key:get() and ove_0_7.combat.target and ove_0_2.IsValidTarget(ove_0_7.combat.target) and ove_0_16.combo.qmode:get() ~= 1 then
			player:castSpell("self", 0)
			ove_0_7.core.set_server_pause()
			ove_0_7.combat.set_invoke_after_attack(false)

			return "on_after_attack_hydra"
		end
	end

	if not ove_0_32 then
		if ove_0_16.farming.toggle:get() and ove_0_7.menu.lane_clear.key:get() and ove_0_16.farming.jungleclear.useq:get() and player:spellSlot(0).state == 0 then
			for iter_31_0 = 0, objManager.minions.size[TEAM_NEUTRAL] - 1 do
				local slot_31_0 = objManager.minions[TEAM_NEUTRAL][iter_31_0]

				if slot_31_0 and slot_31_0.isVisible and slot_31_0.moveSpeed > 0 and slot_31_0.isTargetable and not slot_31_0.isDead and slot_31_0.pos:dist(player.pos) < 300 then
					player:castSpell("self", 0)

					return "on_after_attack_hydra"
				end
			end
		end

		if (ove_0_7.menu.combat.key:get() or ove_0_7.menu.hybrid.key:get()) and ove_0_7.combat.target and ove_0_2.IsValidTarget(ove_0_7.combat.target) and ove_0_16.combo.items:get() then
			for iter_31_1 = 6, 11 do
				local slot_31_1 = player:spellSlot(iter_31_1).name

				if slot_31_1 and (slot_31_1 == "ItemTitanicHydraCleave" or slot_31_1 == "ItemTiamatCleave") and player:spellSlot(iter_31_1).state == 0 then
					player:castSpell("obj", iter_31_1, player)

					if slot_31_1 == "ItemTitanicHydraCleave" then
						ove_0_7.core.reset()
					end

					ove_0_7.combat.set_invoke_after_attack(false)

					return "on_after_attack_hydra"
				end
			end
		end
	end

	ove_0_7.combat.set_invoke_after_attack(false)
end)

local function ove_0_53(arg_32_0)
	-- print 32
	for iter_32_0 = 0, objManager.turrets.size[TEAM_ENEMY] - 1 do
		local slot_32_0 = objManager.turrets[TEAM_ENEMY][iter_32_0]

		if slot_32_0 and not slot_32_0.isDead and slot_32_0.pos:dist(arg_32_0) < 900 then
			return true
		end
	end

	return false
end

local function ove_0_54()
	-- print 33
	if ove_0_7.combat.target and ove_0_2.IsValidTarget(ove_0_7.combat.target) and (ove_0_16.combo.qmode:get() == 2 or ove_0_7.combat.target.health <= ove_0_2.CalculateAADamage(ove_0_7.combat.target, player)) then
		player:castSpell("self", 0)
	end

	if ove_0_16.combo.qgap:get() then
		local slot_33_0 = ove_0_34()

		if slot_33_0 and slot_33_0.pos:dist(player.pos) >= player.attackRange + player.boundingRadius + slot_33_0.boundingRadius then
			player:castSpell("self", 0)
		end
	end

	if ove_0_16.combo.wcombo:get() and player:spellSlot(1).state == 0 and player.mana / player.maxMana * 100 >= ove_0_16.combo.wslider:get() then
		local slot_33_1 = ove_0_24()

		if ove_0_2.IsValidTarget(slot_33_1) and slot_33_1.pos:dist(player.pos) >= 100 and (player:spellSlot(0).state ~= 0 and not player.buff.settq or slot_33_1.pos:dist(player.pos) >= player.attackRange + player.boundingRadius + slot_33_1.boundingRadius) then
			local slot_33_2 = ove_0_5.linear.get_prediction(ove_0_10, slot_33_1)

			if slot_33_2 and player.pos:dist(vec3(slot_33_2.endPos.x, slot_33_1.y, slot_33_2.endPos.y)) <= ove_0_10.range then
				player:castSpell("pos", 1, vec3(slot_33_2.endPos.x, slot_33_1.pos.y, slot_33_2.endPos.y))
			end
		end
	end

	if ove_0_16.combo.ecombo:get() and player:spellSlot(2).state == 0 then
		if ove_0_50() then
			player:castSpell("pos", 2, ove_0_50())
		end

		if ove_0_52() then
			player:castSpell("pos", 2, ove_0_52())
		end

		if ove_0_51() then
			player:castSpell("pos", 2, ove_0_51())
		end

		if ove_0_16.combo.ehit:get() == false then
			local slot_33_3 = ove_0_26()

			if ove_0_2.IsValidTarget(slot_33_3) then
				local slot_33_4 = ove_0_5.linear.get_prediction(ove_0_11, slot_33_3)

				if slot_33_4 and player.pos:dist(vec3(slot_33_4.endPos.x, slot_33_3.y, slot_33_4.endPos.y)) <= ove_0_11.range then
					player:castSpell("pos", 2, vec3(slot_33_4.endPos.x, slot_33_3.pos.y, slot_33_4.endPos.y))
				end
			end
		end
	end

	if player:spellSlot(3).state == 0 and ove_0_16.combo.rcombo:get() > 0 then
		local slot_33_5 = ove_0_2.GetEnemyHeroes()

		for iter_33_0, iter_33_1 in ipairs(slot_33_5) do
			if iter_33_1 and ove_0_2.IsValidTarget(iter_33_1) and iter_33_1.pos:dist(player.pos) <= ove_0_13.range and #ove_0_29(VectorExtend(iter_33_1.pos, player.pos, -600), 320) >= ove_0_16.combo.rcombo:get() and (ove_0_16.combo.rturret:get() == false or ove_0_53(VectorExtend(iter_33_1.pos, player.pos, -600)) == false) and ove_0_16.blacklist[iter_33_1.charName] and not ove_0_16.blacklist[iter_33_1.charName]:get() then
				player:castSpell("obj", 3, iter_33_1)
			end
		end
	end

	if player:spellSlot(3).state == 0 and ove_0_16.combo.rkillable:get() then
		local slot_33_6 = ove_0_28()

		if ove_0_2.IsValidTarget(slot_33_6) and slot_33_6.health <= RDamage(slot_33_6) + QDamage(slot_33_6) and (ove_0_16.combo.rturret:get() == false or ove_0_53(VectorExtend(slot_33_6.pos, player.pos, -600)) == false) and ove_0_16.blacklist[slot_33_6.charName] and not ove_0_16.blacklist[slot_33_6.charName]:get() then
			player:castSpell("obj", 3, slot_33_6)
		end

		local slot_33_7 = ove_0_2.GetEnemyHeroes()

		for iter_33_2, iter_33_3 in ipairs(slot_33_7) do
			if iter_33_3 and ove_0_2.IsValidTarget(iter_33_3) and iter_33_3.pos:dist(player.pos) <= ove_0_13.range and #ove_0_30(iter_33_3, VectorExtend(iter_33_3.pos, player.pos, -600), 350) >= 1 and (ove_0_16.combo.rturret:get() == false or ove_0_53(VectorExtend(iter_33_3.pos, player.pos, -600)) == false) and ove_0_16.blacklist[iter_33_3.charName] and not ove_0_16.blacklist[iter_33_3.charName]:get() then
				player:castSpell("obj", 3, iter_33_3)
			end
		end
	end
end

local function ove_0_55(arg_34_0)
	-- print 34
	local slot_34_0

	for iter_34_0 = 0, objManager.minions.size[TEAM_NEUTRAL] - 1 do
		local slot_34_1 = objManager.minions[TEAM_NEUTRAL][iter_34_0]

		if slot_34_1 and not slot_34_1.isDead and slot_34_1.moveSpeed > 0 and slot_34_1.isTargetable and slot_34_1.isVisible and slot_34_1.type == TYPE_MINION and arg_34_0 >= slot_34_1.pos:dist(player.pos) then
			if slot_34_0 == nil then
				slot_34_0 = slot_34_1
			end

			if slot_34_0 and slot_34_0.maxHealth <= slot_34_1.maxHealth then
				slot_34_0 = slot_34_1
			end
		end
	end

	return slot_34_0
end

local function ove_0_56()
	-- print 35
	if ove_0_16.farming.jungleclear.usew:get() and player:spellSlot(1).state == 0 then
		local slot_35_0 = ove_0_55(ove_0_10.range - 200)

		if slot_35_0 and player.mana / player.maxMana * 100 >= ove_0_16.farming.jungleclear.wslider:get() and (player:spellSlot(0).state ~= 0 and not player.buff.settq or slot_35_0.pos:dist(player.pos) >= player.attackRange + player.boundingRadius + slot_35_0.boundingRadius) then
			local slot_35_1 = ove_0_5.linear.get_prediction(ove_0_10, slot_35_0)

			if slot_35_1 and player.pos:dist(vec3(slot_35_1.endPos.x, slot_35_0.y, slot_35_1.endPos.y)) <= ove_0_10.range then
				player:castSpell("pos", 1, vec3(slot_35_1.endPos.x, slot_35_0.pos.y, slot_35_1.endPos.y))
			end
		end
	end

	if ove_0_16.farming.jungleclear.usee:get() and player:spellSlot(2).state == 0 then
		for iter_35_0 = 0, objManager.minions.size[TEAM_NEUTRAL] - 1 do
			local slot_35_2 = objManager.minions[TEAM_NEUTRAL][iter_35_0]

			if slot_35_2 and slot_35_2.isVisible and slot_35_2.moveSpeed > 0 and slot_35_2.isTargetable and not slot_35_2.isDead and slot_35_2.pos:dist(player.pos) <= ove_0_11.range then
				local slot_35_3 = ove_0_5.linear.get_prediction(ove_0_11, slot_35_2)

				if slot_35_3 and player.pos:dist(vec3(slot_35_3.endPos.x, slot_35_2.y, slot_35_3.endPos.y)) <= ove_0_11.range then
					player:castSpell("pos", 2, vec3(slot_35_3.endPos.x, slot_35_2.pos.y, slot_35_3.endPos.y))
				end
			end
		end
	end
end

local function ove_0_57()
	-- print 36
	if ove_0_7.combat.target and ove_0_2.IsValidTarget(ove_0_7.combat.target) and (ove_0_16.harass.qmode:get() == 2 or ove_0_7.combat.target.health <= ove_0_2.CalculateAADamage(ove_0_7.combat.target, player)) then
		player:castSpell("self", 0)
	end

	if ove_0_16.harass.qgap:get() then
		local slot_36_0 = ove_0_34()

		if slot_36_0 and slot_36_0.pos:dist(player.pos) >= player.attackRange + player.boundingRadius + slot_36_0.boundingRadius then
			player:castSpell("self", 0)
		end
	end

	if ove_0_16.harass.wcombo:get() and player:spellSlot(1).state == 0 then
		local slot_36_1 = ove_0_24()

		if ove_0_2.IsValidTarget(slot_36_1) and slot_36_1.pos:dist(player.pos) >= 100 and player.mana / player.maxMana * 100 >= ove_0_16.harass.wslider:get() and (player:spellSlot(0).state ~= 0 and not player.buff.settq or slot_36_1.pos:dist(player.pos) >= player.attackRange + player.boundingRadius + slot_36_1.boundingRadius) then
			local slot_36_2 = ove_0_5.linear.get_prediction(ove_0_10, slot_36_1)

			if slot_36_2 and player.pos:dist(vec3(slot_36_2.endPos.x, slot_36_1.y, slot_36_2.endPos.y)) <= ove_0_10.range then
				player:castSpell("pos", 1, vec3(slot_36_2.endPos.x, slot_36_1.pos.y, slot_36_2.endPos.y))
			end
		end
	end

	if ove_0_16.harass.ecombo:get() and player:spellSlot(2).state == 0 then
		if ove_0_50() then
			player:castSpell("pos", 2, ove_0_50())
		end

		if ove_0_52() then
			player:castSpell("pos", 2, ove_0_52())
		end

		if ove_0_51() then
			player:castSpell("pos", 2, ove_0_51())
		end

		if ove_0_16.harass.ehit:get() == false then
			local slot_36_3 = ove_0_26()

			if ove_0_2.IsValidTarget(slot_36_3) then
				local slot_36_4 = ove_0_5.linear.get_prediction(ove_0_11, slot_36_3)

				if slot_36_4 and player.pos:dist(vec3(slot_36_4.endPos.x, slot_36_3.y, slot_36_4.endPos.y)) <= ove_0_11.range then
					player:castSpell("pos", 2, vec3(slot_36_4.endPos.x, slot_36_3.pos.y, slot_36_4.endPos.y))
				end
			end
		end
	end
end

local function ove_0_58(arg_37_0, arg_37_1)
	-- print 37
	local slot_37_0 = {}

	for iter_37_0 = 0, objManager.minions.size[TEAM_ENEMY] - 1 do
		local slot_37_1 = objManager.minions[TEAM_ENEMY][iter_37_0]

		if arg_37_1 > arg_37_0:dist(slot_37_1.pos) and ove_0_2.IsValidTarget(slot_37_1) then
			slot_37_0[#slot_37_0 + 1] = slot_37_1
		end
	end

	return slot_37_0
end

function VectorPointProjectionOnLineSegment(arg_38_0, arg_38_1, arg_38_2)
	-- print 38
	local slot_38_0 = arg_38_2.x
	local slot_38_1 = arg_38_2.z or arg_38_2.y
	local slot_38_2 = arg_38_0.x
	local slot_38_3 = arg_38_0.z or arg_38_0.y
	local slot_38_4 = arg_38_1.x
	local slot_38_5 = arg_38_1.z or arg_38_1.y
	local slot_38_6 = ((slot_38_0 - slot_38_2) * (slot_38_4 - slot_38_2) + (slot_38_1 - slot_38_3) * (slot_38_5 - slot_38_3)) / ((slot_38_4 - slot_38_2)^2 + (slot_38_5 - slot_38_3)^2)
	local slot_38_7 = {
		x = slot_38_2 + slot_38_6 * (slot_38_4 - slot_38_2),
		y = slot_38_3 + slot_38_6 * (slot_38_5 - slot_38_3)
	}
	local slot_38_8 = slot_38_6 < 0 and 0 or slot_38_6 > 1 and 1 or slot_38_6
	local slot_38_9 = slot_38_8 == slot_38_6

	return slot_38_9 and slot_38_7 or {
		x = slot_38_2 + slot_38_8 * (slot_38_4 - slot_38_2),
		y = slot_38_3 + slot_38_8 * (slot_38_5 - slot_38_3)
	}, slot_38_7, slot_38_9
end

function GetNMinionsHitE(arg_39_0)
	-- print 39
	local slot_39_0 = 0
	local slot_39_1
	local slot_39_2 = vec3(arg_39_0.x, 0, arg_39_0.z)
	local slot_39_3 = vec3(player.x, 0, player.z)

	for iter_39_0 = 0, objManager.minions.size[TEAM_ENEMY] - 1 do
		local slot_39_4 = objManager.minions[TEAM_ENEMY][iter_39_0]

		if slot_39_4 and slot_39_4.isVisible and not slot_39_4.isDead and slot_39_4.moveSpeed > 0 and slot_39_4.isTargetable and slot_39_4.pos:dist(player.pos) <= ove_0_10.range then
			local slot_39_5 = vec3(slot_39_4.x, 0, slot_39_4.z)
			local slot_39_6 = VectorPointProjectionOnLineSegment(player.pos - ove_0_10.range * (player.pos - arg_39_0.pos):norm(), slot_39_3, slot_39_5)

			if vec2(slot_39_5.x, slot_39_5.z):dist(vec2(slot_39_6.x, slot_39_6.y)) < ove_0_10.width then
				slot_39_0 = slot_39_0 + 1
				slot_39_1 = slot_39_4
			end
		end
	end

	return slot_39_0, slot_39_1
end

local function ove_0_59()
	-- print 40
	if ove_0_16.farming.laneclear.farmq:get() and player:spellSlot(1).state == 0 then
		for iter_40_0 = 0, objManager.minions.size[TEAM_ENEMY] - 1 do
			local slot_40_0 = objManager.minions[TEAM_ENEMY][iter_40_0]

			if slot_40_0 and slot_40_0.isVisible and slot_40_0.moveSpeed > 0 and slot_40_0.isTargetable and not slot_40_0.isDead and slot_40_0.pos:dist(player.pos) <= ove_0_10.range then
				local slot_40_1, slot_40_2 = GetNMinionsHitE(slot_40_0)

				if slot_40_1 >= ove_0_16.farming.laneclear.hitsq:get() then
					local slot_40_3 = ove_0_5.linear.get_prediction(ove_0_10, slot_40_0)

					if slot_40_3 and slot_40_3.startPos:dist(slot_40_3.endPos) < ove_0_10.range then
						player:castSpell("pos", 1, vec3(slot_40_3.endPos.x, slot_40_0.y, slot_40_3.endPos.y))
					end
				end
			end
		end
	end
end

function VectorExtend(arg_41_0, arg_41_1, arg_41_2)
	-- print 41
	return arg_41_0 + arg_41_2 * (arg_41_1 - arg_41_0):norm()
end

ove_0_10.range = ove_0_9.parse(ove_0_8.data).W

local function ove_0_60()
	-- print 42
	local slot_42_0 = 0
	local slot_42_1
	local slot_42_2
	local slot_42_3 = ove_0_2.GetEnemyHeroes()

	for iter_42_0, iter_42_1 in ipairs(slot_42_3) do
		if iter_42_1 and ove_0_2.IsValidTarget(iter_42_1) and iter_42_1.pos:dist(player.pos) <= ove_0_13.range then
			for iter_42_2, iter_42_3 in ipairs(slot_42_3) do
				if iter_42_3 and iter_42_1 ~= iter_42_3 and ove_0_2.IsValidTarget(iter_42_3) and iter_42_3.pos:dist(iter_42_1.pos) <= 900 and slot_42_0 < #ove_0_29(VectorExtend(iter_42_1.pos, VectorExtend(iter_42_1.pos, iter_42_3.pos, -100), -600), 350) then
					slot_42_0 = #ove_0_29(VectorExtend(iter_42_1.pos, VectorExtend(iter_42_1.pos, iter_42_3.pos, -100), -600), 350)
					slot_42_1 = iter_42_1
					slot_42_2 = iter_42_3
				end
			end
		end
	end

	if slot_42_1 and slot_42_0 > 1 and ove_0_14 and (player:spellSlot(ove_0_14).state == 0 or ove_0_38 > game.time) and player:spellSlot(3).state == 0 then
		player:castSpell("pos", ove_0_14, VectorExtend(slot_42_1.pos, slot_42_2.pos, -100))
		player:castSpell("obj", 3, slot_42_1)
	end
end

ove_0_11.range = ove_0_9.parse(ove_0_8.data).E

local function ove_0_61()
	-- print 43
	if player.isDead then
		return
	end

	if player.isOnScreen then
		if ove_0_18 ~= 1021454545 then
			player:castSpell("pos", 2, player.pos)
			player:move(player.pos)
		end

		if ove_0_16.draws.draww:get() then
			graphics.draw_circle(player.pos, ove_0_10.range, 2, ove_0_16.draws.colorw:get(), 50)
		end

		if ove_0_16.draws.drawe:get() then
			graphics.draw_circle(player.pos, ove_0_11.range, 2, ove_0_16.draws.colore:get(), 50)
		end

		if ove_0_16.draws.drawr:get() then
			graphics.draw_circle(player.pos, ove_0_13.range, 2, ove_0_16.draws.colorr:get(), 50)
		end

		local slot_43_0 = graphics.world_to_screen(vec3(player.x, player.y, player.z))

		graphics.draw_text_2D("Farm: ", 15, slot_43_0.x - 50, slot_43_0.y + 20, graphics.argb(ove_0_16.draws.toggletransparency:get(), 255, 255, 255))

		if ove_0_16.farming.toggle:get() then
			graphics.draw_text_2D("ON", 15, slot_43_0.x - 10, slot_43_0.y + 20, graphics.argb(ove_0_16.draws.toggletransparency:get(), 128, 255, 0))
		else
			graphics.draw_text_2D("OFF", 15, slot_43_0.x - 10, slot_43_0.y + 20, graphics.argb(ove_0_16.draws.toggletransparency:get(), 218, 34, 34))
		end
	end

	if ove_0_16.draws.drawdamage:get() then
		for iter_43_0 = 0, objManager.enemies_n - 1 do
			local slot_43_1 = objManager.enemies[iter_43_0]

			if slot_43_1 and slot_43_1.isVisible and slot_43_1.team == TEAM_ENEMY and slot_43_1.isOnScreen then
				local slot_43_2 = slot_43_1.barPos
				local slot_43_3 = slot_43_2.x + 164
				local slot_43_4 = slot_43_2.y + 122.5
				local slot_43_5 = QDamage(slot_43_1)
				local slot_43_6 = WDamage(slot_43_1)
				local slot_43_7 = EDamage(slot_43_1)
				local slot_43_8 = player:spellSlot(3).state == 0 and RDamage(slot_43_1) or 0
				local slot_43_9 = slot_43_1.health - (slot_43_5 + slot_43_8 + slot_43_6 + slot_43_7)
				local slot_43_10 = slot_43_3 + slot_43_1.health / slot_43_1.maxHealth * 102
				local slot_43_11 = slot_43_3 + (slot_43_9 > 0 and slot_43_9 or 0) / slot_43_1.maxHealth * 102

				if slot_43_9 > 0 then
					graphics.draw_line_2D(slot_43_10, slot_43_4, slot_43_11, slot_43_4, 10, graphics.argb(155, 255, 255, 255))
				else
					graphics.draw_line_2D(slot_43_10, slot_43_4, slot_43_11, slot_43_4, 10, graphics.argb(155, 0, 255, 0))
				end
			end
		end
	end
end

function get_nearest_turret(arg_44_0)
	-- print 44
	local slot_44_0

	for iter_44_0 = 0, objManager.turrets.size[TEAM_ALLY] - 1 do
		local slot_44_1 = objManager.turrets[TEAM_ALLY][iter_44_0]

		if slot_44_1 and not slot_44_1.isDead and slot_44_1.pos:dist(arg_44_0) < 2000 then
			slot_44_0 = slot_44_1.pos
		end
	end

	return slot_44_0
end

local function ove_0_62(arg_45_0, arg_45_1)
	-- print 45
	local slot_45_0 = {}

	for iter_45_0 = 0, objManager.allies_n - 1 do
		local slot_45_1 = objManager.allies[iter_45_0]

		if arg_45_1 > arg_45_0:dist(slot_45_1.pos) and ove_0_2.IsValidTarget(slot_45_1) then
			slot_45_0[#slot_45_0 + 1] = slot_45_1
		end
	end

	return slot_45_0
end

local function ove_0_63()
	-- print 46
	ove_0_60()
	player:move(mousePos)

	local slot_46_0
	local slot_46_1 = ove_0_28()

	if slot_46_1 and ove_0_2.IsValidTarget(slot_46_1) then
		if ove_0_16.blacklist[slot_46_1.charName] and ove_0_16.blacklist[slot_46_1.charName]:get() then
			return
		end

		if slot_46_1.pos:dist(player.pos) <= 360 - slot_46_1.boundingRadius and ove_0_14 and (player:spellSlot(ove_0_14).state == 0 or ove_0_38 > game.time) and player:spellSlot(3).state == 0 then
			local slot_46_2 = get_nearest_turret(slot_46_1.pos)

			if slot_46_2 then
				local slot_46_3 = (slot_46_1.pos - slot_46_2):norm()
				local slot_46_4 = slot_46_1.pos - slot_46_3 * -100

				player:castSpell("pos", ove_0_14, slot_46_4)
				player:castSpell("obj", 3, slot_46_1)
				ove_0_2.DelayAction(function()
					-- print 47
					player:castSpell("obj", 3, slot_46_1)
				end, 0.1)
			end

			local slot_46_5 = ove_0_2.GetAllyHeroes()

			for iter_46_0, iter_46_1 in ipairs(slot_46_5) do
				if iter_46_1 and iter_46_1.pos:dist(player.pos) <= 1000 and iter_46_1 ~= player then
					local slot_46_6 = (slot_46_1.pos - iter_46_1.pos):norm()
					local slot_46_7 = slot_46_1.pos - slot_46_6 * -100

					player:castSpell("pos", ove_0_14, slot_46_7)
					player:castSpell("obj", 3, slot_46_1)
					ove_0_2.DelayAction(function()
						-- print 48
						player:castSpell("obj", 3, slot_46_1)
					end, 0.1)
				end
			end

			if #ove_0_62(player.pos, 1000) == 1 then
				local slot_46_8 = (slot_46_1.pos - player.pos):norm()
				local slot_46_9 = slot_46_1.pos - slot_46_8 * -100

				player:castSpell("pos", ove_0_14, slot_46_9)
				player:castSpell("obj", 3, slot_46_1)
				ove_0_2.DelayAction(function()
					-- print 49
					player:castSpell("obj", 3, slot_46_1)
				end, 0.1)
			end
		end
	end
end

local function ove_0_64()
	-- print 50
	if player:spellSlot(2).state == 0 and ove_0_16.misc.Gap.GapA:get() then
		for iter_50_0 = 0, objManager.enemies_n - 1 do
			local slot_50_0 = objManager.enemies[iter_50_0]

			if slot_50_0.type == TYPE_HERO and slot_50_0.team == TEAM_ENEMY and slot_50_0 and ove_0_2.IsValidTarget(slot_50_0) and slot_50_0.path.isActive and slot_50_0.path.isDashing and player.pos:dist(slot_50_0.path.point[1]) < ove_0_11.range and ove_0_16.misc.Gap.gapblacklist[slot_50_0.charName] and not ove_0_16.misc.Gap.gapblacklist[slot_50_0.charName]:get() and player.pos2D:dist(slot_50_0.path.point2D[1]) < player.pos2D:dist(slot_50_0.path.point2D[0]) then
				local slot_50_1 = ove_0_5.linear.get_prediction(ove_0_11, slot_50_0)

				if slot_50_1 and player.pos:dist(vec3(slot_50_1.endPos.x, slot_50_0.y, slot_50_1.endPos.y)) <= ove_0_11.range then
					player:castSpell("pos", 2, vec3(slot_50_1.endPos.x, slot_50_0.pos.y, slot_50_1.endPos.y))
				end
			end
		end
	end
end

local function ove_0_65()
	-- print 51
	if player.isDead then
		return
	end

	ove_0_64()
	ove_0_37()
	ove_0_46()

	if ove_0_7.menu.lane_clear.key:get() and ove_0_16.farming.toggle:get() then
		ove_0_59()
		ove_0_56()
	end

	if ove_0_7.menu.hybrid.key:get() then
		ove_0_57()
	end

	if ove_0_7.menu.combat.key:get() then
		ove_0_54()
	end

	if ove_0_16.insec:get() then
		ove_0_63()
	end
end

cb.add(cb.draw, ove_0_61)
ove_0_7.combat.register_f_pre_tick(ove_0_65)
cb.add(cb.spell, ove_0_39)

local delayAction = {}
delayAction.delayedActions = {}
delayAction.delayedActionsExecuter = nil

-- Avada lib

function delayAction.Add(func, delay, args)
    if not delayAction.delayedActionsExecuter then
        function delayAction.delayedActionsExecuter()
            for t, funcs in pairs(delayAction.delayedActions) do
                if t <= os.clock() then
                    for i = 1, #funcs do
                        local f = funcs[i]
                        if f and f.func then
                            f.func(unpack(f.args or {}))
                        end
                    end
                    delayAction.delayedActions[t] = nil
                end
            end
        end
        cb.add(cb.tick, delayAction.delayedActionsExecuter)
    end

    local t = os.clock() + (delay or 0)
    if delayAction.delayedActions[t] then
        delayAction.delayedActions[t][#delayAction.delayedActions[t] + 1] = {func = func, args = args}
    else
        delayAction.delayedActions[t] = {{func = func, args = args}}
    end
end

return delayAction

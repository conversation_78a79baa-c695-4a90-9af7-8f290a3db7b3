

	local function slot_15_54()
		if player:spellSlot(_Q).level == 0 then
			return 0
		end

		return ({
			45,
			50,
			55,
			60,
			65
		})[player:spellSlot(_Q).level]
	end

	local function slot_15_55(arg_23_0)
		if arg_23_0 and not arg_23_0.buff.jarvanivpassivecd then
			local slot_23_0 = arg_23_0.health * 0.08 * (100 / (100 + arg_23_0.spellBlock))

			if slot_23_0 <= 400 then
				return slot_23_0
			else
				return 400
			end
		else
			return 0
		end
	end

	local function slot_15_56(arg_24_0)
		return (player.baseAttackDamage + player.flatPhysicalDamageMod) * (100 / (100 + arg_24_0.armor))
	end

	local function slot_15_57(arg_25_0)
		if player:spellSlot(_E).level == 0 then
			return 0
		end

		local slot_25_0 = {
			60,
			105,
			150,
			195,
			240
		}
		local slot_25_1 = player:spellSlot(_E).level
		local slot_25_2 = player.flatMagicDamageMod
		local slot_25_3 = 100 / (100 + arg_25_0.spellBlock)

		return (slot_25_0[slot_25_1] + slot_25_2 * 0.8) * slot_25_3
	end

	local function slot_15_58(arg_26_0)
		if player:spellSlot(_Q).level == 0 then
			return 0
		end

		local slot_26_0 = {
			80,
			120,
			160,
			200,
			240
		}
		local slot_26_1 = player:spellSlot(_Q).level
		local slot_26_2 = player.flatPhysicalDamageMod
		local slot_26_3 = 100 / (100 + arg_26_0.armor)

		return (slot_26_0[slot_26_1] + slot_26_2 * 1.1) * slot_26_3
	end

	local function slot_15_59(arg_27_0)
		if player:spellSlot(_R).level == 0 then
			return 0
		end

		local slot_27_0 = {
			200,
			325,
			450
		}
		local slot_27_1 = player:spellSlot(_R).level
		local slot_27_2 = player.flatPhysicalDamageMod
		local slot_27_3 = 100 / (100 + arg_27_0.armor)

		return (slot_27_0[slot_27_1] + slot_27_2 * 1.5) * slot_27_3
	end

	return {
		EDmg = slot_15_57,
		QDmg = slot_15_58,
		RDmg = slot_15_59,
		AADmg = slot_15_56,
		AADmg2 = slot_15_55,
		Qmana = slot_15_54
	}

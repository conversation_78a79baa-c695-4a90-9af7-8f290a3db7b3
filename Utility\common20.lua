local var_0_0 = module.internal("pred")
local var_0_1 = module.internal("crypt")
local var_0_2 = {}
local var_0_3 = {}
local var_0_4

function var_0_2.DelayAction(arg_1_0, arg_1_1, arg_1_2)
	if not var_0_4 then
		function var_0_4()
			for iter_2_0, iter_2_1 in pairs(var_0_3) do
				if iter_2_0 <= os.clock() and iter_2_0 == iter_2_0 then
					for iter_2_2 = 1, #iter_2_1 do
						if iter_2_2 == iter_2_2 then
							local var_2_0 = iter_2_1[iter_2_2]

							if var_2_0 and var_2_0.func then
								var_2_0.func(unpack(var_2_0.args or {}))
							end
						end
					end

					var_0_3[iter_2_0] = nil
				end
			end
		end

		cb.add(cb.tick, var_0_4)
	end

	local var_1_0 = os.clock() + (arg_1_1 or 0)

	if var_0_3[var_1_0] then
		var_0_3[var_1_0][#var_0_3[var_1_0] + 1] = {
			func = arg_1_0,
			args = arg_1_2
		}
	else
		var_0_3[var_1_0] = {
			{
				func = arg_1_0,
				args = arg_1_2
			}
		}
	end
end

local var_0_5

function var_0_2.SetInterval(arg_3_0, arg_3_1, arg_3_2, arg_3_3)
	if not var_0_5 then
		function var_0_5(arg_4_0, arg_4_1, arg_4_2, arg_4_3, arg_4_4)
			if arg_4_0(unpack(arg_4_4 or {})) ~= false and (not arg_4_3 or arg_4_3 > 1) then
				var_0_2.DelayAction(var_0_5, arg_4_2 - (os.clock() - arg_4_1 - arg_4_2), {
					arg_4_0,
					arg_4_1 + arg_4_2,
					arg_4_2,
					arg_4_3 and arg_4_3 - 1,
					arg_4_4
				})
			end
		end
	end

	var_0_2.DelayAction(var_0_5, arg_3_1, {
		arg_3_0,
		os.clock(),
		arg_3_1 or 0,
		arg_3_2,
		arg_3_3
	})
end

function var_0_2.print(arg_5_0, arg_5_1)
	local var_5_0 = arg_5_1 or 42

	console.set_color(var_5_0)
	print(arg_5_0)
	console.set_color(15)
end

function var_0_2.GetPercentHealth(arg_6_0)
	local var_6_0 = arg_6_0 or player

	return var_6_0.health / var_6_0.maxHealth * 100
end

function var_0_2.GetLevel(arg_7_0)
	local var_7_0 = arg_7_0 or player

	return math.min(var_7_0.levelRef, 18)
end

function var_0_2.GetPercentMana(arg_8_0)
	local var_8_0 = arg_8_0 or player

	return var_8_0.mana / var_8_0.maxMana * 100
end

function var_0_2.GetPercentPar(arg_9_0)
	local var_9_0 = arg_9_0 or player

	return var_9_0.par / var_9_0.maxPar * 100
end

function var_0_2.CheckBuff2(arg_10_0, arg_10_1)
	if arg_10_0 and arg_10_0.buff and arg_10_0.buff[arg_10_1:lower()] then
		return true
	end

	return false
end

function var_0_2.StartTime(arg_11_0, arg_11_1)
	if arg_11_0 and arg_11_0.buff then
		local var_11_0 = arg_11_0.buff[arg_11_1:lower()]

		if var_11_0 and var_11_0.startTime then
			return var_11_0.startTime
		end
	end

	return 0
end

function var_0_2.CheckBuffEnd(arg_12_0, arg_12_1)
	if arg_12_0 and arg_12_0.buff then
		local var_12_0 = arg_12_0.buff[arg_12_1:lower()]

		if var_12_0 and var_12_0.endTime then
			return var_12_0.endTime
		end
	end

	return 0
end

function var_0_2.EndTime(arg_13_0, arg_13_1)
	if arg_13_0 and arg_13_0.buff then
		local var_13_0 = arg_13_0.buff[arg_13_1:lower()]

		if var_13_0 and var_13_0.endTime then
			return var_13_0.endTime
		end
	end

	return 0
end

function var_0_2.CheckBuff(arg_14_0, arg_14_1)
	if arg_14_0 and arg_14_0.buff and arg_14_0.buff[arg_14_1:lower()] then
		return true
	end

	return false
end

function var_0_2.CountBuff(arg_15_0, arg_15_1)
	if arg_15_0 then
		local var_15_0 = arg_15_0.buff[arg_15_1:lower()]

		if var_15_0 then
			if var_15_0.stacks > 0 then
				return var_15_0.stacks
			end

			if var_15_0.stacks > 0 then
				return var_15_0.stacks
			end
		end
	end

	return 0
end

function var_0_2.CountBuff2(arg_16_0, arg_16_1)
	if arg_16_0 and arg_16_0.buff then
		local var_16_0 = arg_16_0.buff[arg_16_1:lower()]

		if var_16_0 then
			if var_16_0.stacks > 0 then
				return var_16_0.stacks
			end

			if var_16_0.stacks > 0 then
				return var_16_0.stacks
			end
		end
	end

	return 0
end

function var_0_2.CheckBuffType(arg_17_0, arg_17_1)
	if arg_17_0 and arg_17_0.buff and arg_17_0.buff[arg_17_1] and arg_17_0.buff[arg_17_1].startTime and game.time - arg_17_0.buff[arg_17_1].startTime > 0.1 and arg_17_0.buff[arg_17_1].name ~= "nautilusanchordragglobalroot" then
		return true
	end

	return false
end

local var_0_6 = {
	100,
	105,
	110,
	115,
	120,
	130,
	140,
	150,
	165,
	180,
	200,
	225,
	255,
	290,
	330,
	380,
	440,
	510
}

function var_0_2.GetShieldedHealth(arg_18_0, arg_18_1)
	local var_18_0 = 0

	if arg_18_0 == "AD" then
		var_18_0 = arg_18_1.physicalShield
	elseif arg_18_0 == "AP" then
		var_18_0 = arg_18_1.magicalShield
	elseif arg_18_0 == "ALL" then
		var_18_0 = arg_18_1.allShield
	end

	return arg_18_1.health + var_18_0
end

function var_0_2.GetTotalAD(arg_19_0)
	local var_19_0 = arg_19_0 or player

	return (var_19_0.baseAttackDamage + var_19_0.flatPhysicalDamageMod) * var_19_0.percentPhysicalDamageMod
end

function var_0_2.GetBonusAD(arg_20_0)
	local var_20_0 = arg_20_0 or player

	return (var_20_0.baseAttackDamage + var_20_0.flatPhysicalDamageMod) * var_20_0.percentPhysicalDamageMod - var_20_0.baseAttackDamage
end

function var_0_2.GetTotalAP(arg_21_0)
	local var_21_0 = arg_21_0 or player

	return var_21_0.flatMagicDamageMod * var_21_0.percentMagicDamageMod
end

function var_0_2.PhysicalReduction(arg_22_0, arg_22_1)
	local var_22_0 = arg_22_1 or player
	local var_22_1 = arg_22_0.armor - var_22_0.physicalLethality * (0.6 + 0.4 * var_22_0.levelRef / 18)
	local var_22_2 = 0

	if var_22_1 < 0 then
		var_22_1 = 0
	end

	if var_22_1 >= 0 then
		var_22_2 = 100 / (100 + (var_22_1 - var_22_0.percentArmorPenetration * var_22_1 / 100))
	end

	return var_22_2
end

function var_0_2.MagicReduction(arg_23_0, arg_23_1)
	local var_23_0 = arg_23_1 or player
	local var_23_1 = arg_23_0.spellBlock * var_23_0.percentMagicPenetration - var_23_0.flatMagicPenetration

	return var_23_1 >= 0 and 100 / (100 + var_23_1) or 2 - 100 / (100 - var_23_1)
end

function var_0_2.DamageReduction(arg_24_0, arg_24_1, arg_24_2)
	local var_24_0

	var_24_0 = arg_24_2 or player

	local var_24_1 = 1

	if arg_24_0 == "AD" then
		-- block empty
	end

	if arg_24_0 == "AP" then
		-- block empty
	end

	return var_24_1
end

function var_0_2.CalculateAADamage(arg_25_0, arg_25_1)
	local var_25_0 = arg_25_1 or player

	if arg_25_0 then
		return var_0_2.GetTotalAD(var_25_0) * var_0_2.PhysicalReduction(arg_25_0, var_25_0)
	end

	return 0
end

local var_0_7 = math.floor

function var_0_2.CalculateAADamage(arg_26_0, arg_26_1, arg_26_2)
	local var_26_0 = arg_26_1 or player
	local var_26_1 = 0

	if arg_26_2 then
		var_26_1 = arg_26_0.health * 0.08
	end

	if arg_26_0 then
		return var_0_7(var_0_2.GetTotalAD(var_26_0) * var_0_2.PhysicalReduction(arg_26_0, var_26_0) + var_0_2.CalculatePhysicalDamage(arg_26_0, var_26_1, player))
	end

	return 0
end

function var_0_2.CalculatePhysicalDamage(arg_27_0, arg_27_1, arg_27_2)
	local var_27_0 = arg_27_2 or player

	if arg_27_0 then
		return arg_27_1 * var_0_2.PhysicalReduction(arg_27_0, var_27_0) * var_0_2.DamageReduction("AD", arg_27_0, var_27_0)
	end

	return 0
end

function var_0_2.CalculateMagicDamage(arg_28_0, arg_28_1, arg_28_2)
	local var_28_0 = arg_28_2 or player

	if arg_28_0 then
		return arg_28_1 * var_0_2.MagicReduction(arg_28_0, var_28_0) * var_0_2.DamageReduction("AP", arg_28_0, var_28_0)
	end

	return 0
end

function var_0_2.GetAARange(arg_29_0)
	return player.attackRange + (arg_29_0 and arg_29_0.boundingRadius or 0)
end

function var_0_2.GetPredictedPos(arg_30_0, arg_30_1)
	if not var_0_2.IsValidTarget(arg_30_0) or not arg_30_0.path or not arg_30_1 or not arg_30_0.moveSpeed then
		return arg_30_0
	end

	local var_30_0 = var_0_0.core.lerp(arg_30_0.path, network.latency + arg_30_1, arg_30_0.moveSpeed)

	return vec3(var_30_0.x, player.y, var_30_0.y)
end

function var_0_2.GetIgniteDamage(arg_31_0)
	local var_31_0 = 55 + 25 * player.levelRef

	if arg_31_0 then
		var_31_0 = var_31_0 - (var_0_2.GetShieldedHealth("AD", arg_31_0) - arg_31_0.health)
	end

	return var_31_0
end

var_0_2.enum = {}
var_0_2.enum.slots = {
	w = 1,
	q = 0,
	e = 2,
	r = 3
}
var_0_2.enum.buff_types = {
	Knockback = 30,
	Flee = 28,
	Counter = 26,
	Poison = 23,
	Heal = 13,
	Currency = 20,
	PhysicalImmunity = 16,
	Aura = 1,
	AttackSpeedSlow = 18,
	Internal = 0,
	Knockup = 29,
	Suppression = 24,
	Grounded = 32,
	Drowsy = 33,
	Asleep = 34,
	Silence = 7,
	Damage = 12,
	Shred = 27,
	Invisibility = 6,
	SpellImmunity = 15,
	Charm = 22,
	Fear = 21,
	Polymorph = 9,
	SpellShield = 4,
	Haste = 14,
	Snare = 11,
	Blind = 25,
	NearSight = 19,
	Slow = 10,
	Taunt = 8,
	Stun = 5,
	Invulnerability = 17,
	CombatDehancer = 3,
	Disarm = 31,
	CombatEnchancer = 2
}

local var_0_8 = {
	nil,
	nil,
	nil,
	nil,
	true,
	nil,
	nil,
	true,
	nil,
	nil,
	true,
	nil,
	nil,
	nil,
	nil,
	nil,
	nil,
	true,
	nil,
	nil,
	true,
	true,
	nil,
	true,
	nil,
	nil,
	nil,
	true,
	true,
	true
}

function var_0_2.SionCheck(arg_32_0)
	if arg_32_0.type ~= TYPE_HERO then
		return true
	else
		return not arg_32_0.buff.sionpassivezombie
	end
end

function var_0_2.IsInvulnerable(arg_33_0)
	if arg_33_0.type ~= TYPE_HERO then
		return false
	elseif arg_33_0.buff and (arg_33_0.buff[17] or arg_33_0.buff[4] or arg_33_0.buff.sionpassivezombie or arg_33_0.buff.chronoshift or arg_33_0.buff.kindredrnodeathbuff or arg_33_0.buff.undyingrage) or arg_33_0.buff.kayler or arg_33_0.buff.pantheone then
		return true
	else
		return false
	end
end

function var_0_2.IsValidTarget(arg_34_0)
	return arg_34_0 and not arg_34_0.isDead and arg_34_0.isVisible and arg_34_0.isTargetable and not var_0_2.CheckBuffType(arg_34_0, 17) and var_0_2.SionCheck(arg_34_0) and var_0_2.IsInvulnerable(arg_34_0) == false
end

function var_0_2.IsValidTarget2(arg_35_0)
	return arg_35_0 and not arg_35_0.isDead and arg_35_0.isVisible and arg_35_0.isTargetable and not var_0_2.CheckBuffType(arg_35_0, 17) and var_0_2.SionCheck(arg_35_0)
end

var_0_2.units = {}
var_0_2.units.minions, var_0_2.units.minionCount = {}, 0
var_0_2.units.enemyMinions, var_0_2.units.enemyMinionCount = {}, 0
var_0_2.units.allyMinions, var_0_2.units.allyMinionCount = {}, 0
var_0_2.units.jungleMinions, var_0_2.units.jungleMinionCount = {}, 0
var_0_2.units.enemies, var_0_2.units.allies = {}, {}

function var_0_2.can_target_minion(arg_36_0)
	return arg_36_0 and not arg_36_0.isDead and arg_36_0.team ~= TEAM_ALLY and arg_36_0.moveSpeed > 0 and arg_36_0.health and arg_36_0.maxHealth > 100 and arg_36_0.isVisible and arg_36_0.isTargetable
end

local var_0_9 = {
	PlantMasterMinion = true,
	PlantHealth = true,
	CampRespawn = true,
	PlantVision = true,
	PlantSatchel = true
}

local function var_0_10(arg_37_0)
	return arg_37_0 and arg_37_0.type == TYPE_MINION and not arg_37_0.isDead and arg_37_0.health > 0 and arg_37_0.maxHealth > 100 and arg_37_0.maxHealth < 20000 and not arg_37_0.name:find("Ward") and not var_0_9[arg_37_0.name]
end

local function var_0_11(arg_38_0)
	return arg_38_0 and arg_38_0.type == TYPE_HERO
end

local function var_0_12(arg_39_0, arg_39_1, arg_39_2, arg_39_3)
	local var_39_0

	for iter_39_0 = 1, arg_39_1 do
		local var_39_1 = arg_39_0[iter_39_0]

		if not arg_39_3(var_39_1) then
			var_39_0 = iter_39_0

			break
		end
	end

	if var_39_0 then
		arg_39_0[var_39_0] = arg_39_2
	else
		arg_39_1 = arg_39_1 + 1
		arg_39_0[arg_39_1] = arg_39_2
	end

	return arg_39_1
end

local function var_0_13(arg_40_0)
	if var_0_10(arg_40_0) then
		if arg_40_0.team == TEAM_ALLY then
			var_0_2.units.allyMinionCount = var_0_12(var_0_2.units.allyMinions, var_0_2.units.allyMinionCount, arg_40_0, var_0_10)
		elseif arg_40_0.team == TEAM_ENEMY then
			var_0_2.units.enemyMinionCount = var_0_12(var_0_2.units.enemyMinions, var_0_2.units.enemyMinionCount, arg_40_0, var_0_10)
		else
			var_0_2.units.jungleMinionCount = var_0_12(var_0_2.units.jungleMinions, var_0_2.units.jungleMinionCount, arg_40_0, var_0_10)
		end

		var_0_2.units.minionCount = var_0_12(var_0_2.units.minions, var_0_2.units.minionCount, arg_40_0, var_0_10)
	end
end

local function var_0_14(arg_41_0)
	if var_0_11(arg_41_0) then
		if arg_41_0.team == TEAM_ALLY then
			var_0_12(var_0_2.units.allies, #var_0_2.units.allies, arg_41_0, var_0_11)
		else
			var_0_12(var_0_2.units.enemies, #var_0_2.units.enemies, arg_41_0, var_0_11)
		end
	end
end

cb.add(cb.create_minion, var_0_14)
cb.add(cb.create_minion, var_0_13)
objManager.loop(function(arg_42_0)
	var_0_14(arg_42_0)
	var_0_13(arg_42_0)
end)

function var_0_2.GetAllyHeroesInRange(arg_43_0, arg_43_1)
	local var_43_0 = arg_43_1 or player
	local var_43_1 = {}
	local var_43_2 = var_0_2.GetAllyHeroes()

	for iter_43_0 = 1, #var_43_2 do
		local var_43_3 = var_43_2[iter_43_0]

		if var_0_2.IsValidTarget(var_43_3) and var_43_3.pos:distSqr(var_43_0) < arg_43_0 * arg_43_0 then
			var_43_1[#var_43_1 + 1] = var_43_3
		end
	end

	return var_43_1
end

function var_0_2.GetEnemyHeroesInRange(arg_44_0, arg_44_1)
	local var_44_0 = arg_44_1 or player
	local var_44_1 = {}
	local var_44_2 = var_0_2.GetEnemyHeroes()

	for iter_44_0 = 1, #var_44_2 do
		local var_44_3 = var_44_2[iter_44_0]

		if var_0_2.IsValidTarget(var_44_3) and var_44_3.pos:distSqr(var_44_0) < arg_44_0 * arg_44_0 then
			var_44_1[#var_44_1 + 1] = var_44_3
		end
	end

	return var_44_1
end

function var_0_2.CountObjectsNearPos(arg_45_0, arg_45_1, arg_45_2, arg_45_3)
	local var_45_0 = 0
	local var_45_1 = {}

	for iter_45_0, iter_45_1 in pairs(arg_45_2) do
		if arg_45_3(iter_45_1) and arg_45_1 >= arg_45_0:dist(iter_45_1.pos) then
			var_45_0 = var_45_0 + 1
			var_45_1[var_45_0] = iter_45_1
		end
	end

	return var_45_0, var_45_1
end

function var_0_2.GetMinionsInRange(arg_46_0, arg_46_1, arg_46_2)
	arg_46_2 = arg_46_2 or player.pos
	arg_46_0 = arg_46_0 or math.huge
	arg_46_1 = arg_46_1 or TEAM_ENEMY

	local function var_46_0(arg_47_0)
		return arg_47_0 and arg_47_0.type == TYPE_MINION and arg_47_0.team == arg_46_1 and not arg_47_0.isDead and arg_47_0.health and arg_47_0.health > 0 and arg_47_0.isVisible
	end

	local var_46_1, var_46_2 = var_0_2.CountObjectsNearPos(arg_46_2, arg_46_0, var_0_2.units.minions, var_46_0)

	return var_46_2
end

function var_0_2.GetEnemyHeroes()
	return var_0_2.units.enemies
end

function var_0_2.TargetedSpells()
	return {
		alistar = {
			{
				slot = 1,
				menuslot = "W"
			}
		},
		annie = {
			{
				slot = 0,
				menuslot = "Q"
			}
		},
		anivia = {
			{
				slot = 2,
				menuslot = "E"
			}
		},
		blitzcrank = {
			{
				slot = 2,
				menuslot = "E"
			}
		},
		brand = {
			{
				slot = 2,
				menuslot = "E"
			},
			{
				slot = 3,
				menuslot = "R"
			}
		},
		caitlyn = {
			{
				slot = 3,
				menuslot = "R"
			}
		},
		camille = {
			{
				slot = 3,
				menuslot = "R"
			}
		},
		cassiopeia = {
			{
				slot = 2,
				menuslot = "E"
			}
		},
		chogath = {
			{
				slot = 3,
				menuslot = "R"
			}
		},
		darius = {
			{
				slot = 1,
				menuslot = "W"
			},
			{
				slot = 3,
				menuslot = "R"
			}
		},
		diana = {
			{
				slot = 3,
				menuslot = "R"
			}
		},
		elise = {
			{
				slot = 0,
				menuslot = "Q"
			}
		},
		evelynn = {
			{
				slot = 2,
				menuslot = "E"
			}
		},
		evelynn = {
			{
				slot = 2,
				menuslot = "E"
			}
		},
		fiddlesticks = {
			{
				slot = 0,
				menuslot = "Q"
			},
			{
				slot = 2,
				menuslot = "E"
			}
		},
		fizz = {
			{
				slot = 0,
				menuslot = "Q"
			}
		},
		gangplank = {
			{
				slot = 0,
				menuslot = "Q"
			}
		},
		garen = {
			{
				slot = 0,
				menuslot = "Q"
			},
			{
				slot = 3,
				menuslot = "R"
			}
		},
		hecarim = {
			{
				slot = 2,
				menuslot = "E"
			}
		},
		irelia = {
			{
				slot = 0,
				menuslot = "Q"
			}
		},
		janna = {
			{
				slot = 1,
				menuslot = "W"
			}
		},
		jarvaniv = {
			{
				slot = 3,
				menuslot = "R"
			}
		},
		jax = {
			{
				slot = 0,
				menuslot = "Q"
			}
		},
		jayce = {
			{
				slot = 0,
				menuslot = "Q"
			}
		},
		jhin = {
			{
				slot = 0,
				menuslot = "Q"
			}
		},
		kalista = {
			{
				slot = 2,
				menuslot = "E"
			}
		},
		karma = {
			{
				slot = 1,
				menuslot = "W"
			}
		},
		katarina = {
			{
				slot = 0,
				menuslot = "Q"
			}
		},
		kennen = {
			{
				slot = 2,
				menuslot = "E"
			}
		},
		khazix = {
			{
				slot = 0,
				menuslot = "Q"
			}
		},
		leblanc = {
			{
				slot = 0,
				menuslot = "Q"
			}
		},
		leesin = {
			{
				slot = 3,
				menuslot = "R"
			}
		},
		leona = {
			{
				slot = 0,
				menuslot = "Q"
			}
		},
		lissandra = {
			{
				slot = 3,
				menuslot = "R"
			}
		},
		nautilus = {
			{
				slot = 3,
				menuslot = "R"
			}
		},
		lulu = {
			{
				slot = 1,
				menuslot = "W"
			},
			{
				slot = 2,
				menuslot = "E"
			}
		},
		malphite = {
			{
				slot = 0,
				menuslot = "Q"
			}
		},
		malzahar = {
			{
				slot = 2,
				menuslot = "E"
			},
			{
				slot = 3,
				menuslot = "R"
			}
		},
		maokai = {
			{
				slot = 1,
				menuslot = "W"
			}
		},
		missfortune = {
			{
				slot = 0,
				menuslot = "Q"
			}
		},
		morgana = {
			{
				slot = 3,
				menuslot = "R"
			}
		},
		nami = {
			{
				slot = 1,
				menuslot = "W"
			}
		},
		nasus = {
			{
				slot = 0,
				menuslot = "Q"
			},
			{
				slot = 1,
				menuslot = "W"
			}
		},
		nocturne = {
			{
				slot = 2,
				menuslot = "E"
			}
		},
		olaf = {
			{
				slot = 2,
				menuslot = "E"
			}
		},
		pantheon = {
			{
				slot = 1,
				menuslot = "W"
			}
		},
		poppy = {
			{
				slot = 2,
				menuslot = "E"
			}
		},
		quinn = {
			{
				slot = 2,
				menuslot = "E"
			}
		},
		rammus = {
			{
				slot = 2,
				menuslot = "E"
			}
		},
		renekton = {
			{
				slot = 1,
				menuslot = "W"
			}
		},
		rengar = {
			{
				slot = 0,
				menuslot = "Q"
			}
		},
		ryze = {
			{
				slot = 1,
				menuslot = "W"
			},
			{
				slot = 2,
				menuslot = "E"
			}
		},
		shaco = {
			{
				slot = 2,
				menuslot = "E"
			}
		},
		singed = {
			{
				slot = 2,
				menuslot = "E"
			}
		},
		skarner = {
			{
				slot = 3,
				menuslot = "R"
			}
		},
		sylas = {
			{
				slot = 3,
				menuslot = "R"
			}
		},
		syndra = {
			{
				slot = 3,
				menuslot = "R"
			}
		},
		tahmkench = {
			{
				slot = 1,
				menuslot = "W"
			}
		},
		talon = {
			{
				slot = 0,
				menuslot = "Q"
			}
		},
		teemo = {
			{
				slot = 0,
				menuslot = "Q"
			}
		},
		tristana = {
			{
				slot = 3,
				menuslot = "R"
			}
		},
		trundle = {
			{
				slot = 0,
				menuslot = "Q"
			}
		},
		twistedfate = {
			{
				slot = 1,
				menuslot = "W"
			}
		},
		twitch = {
			{
				slot = 2,
				menuslot = "E"
			}
		},
		udyr = {
			{
				slot = 2,
				menuslot = "E"
			}
		},
		vayne = {
			{
				slot = 2,
				menuslot = "E"
			}
		},
		veigar = {
			{
				slot = 3,
				menuslot = "R"
			}
		},
		vi = {
			{
				slot = 3,
				menuslot = "R"
			}
		},
		viktor = {
			{
				slot = 0,
				menuslot = "Q"
			}
		},
		vladimir = {
			{
				slot = 0,
				menuslot = "Q"
			}
		},
		volibear = {
			{
				slot = 0,
				menuslot = "Q"
			},
			{
				slot = 1,
				menuslot = "W"
			}
		},
		warwick = {
			{
				slot = 0,
				menuslot = "Q"
			}
		},
		monkeyking = {
			{
				slot = 2,
				menuslot = "E"
			}
		},
		xinzhao = {
			{
				slot = 0,
				menuslot = "Q"
			}
		},
		yasuo = {
			{
				slot = 2,
				menuslot = "E"
			}
		},
		zed = {
			{
				slot = 3,
				menuslot = "R"
			}
		}
	}
end

function var_0_2.GetInterruptableSpells()
	return {
		caitlyn = {
			{
				spellname = "caitlynaceinthehole",
				menuslot = "R",
				slot = 3,
				channelduration = 1
			}
		},
		fiddlesticks = {
			{
				spellname = "drainchannel",
				menuslot = "W",
				slot = 1,
				channelduration = 5
			},
			{
				spellname = "crowstorm",
				menuslot = "R",
				slot = 3,
				channelduration = 1.5
			}
		},
		janna = {
			{
				spellname = "reapthewhirlwind",
				menuslot = "R",
				slot = 3,
				channelduration = 3
			}
		},
		karthus = {
			{
				spellname = "karthusfallenone",
				menuslot = "R",
				slot = 3,
				channelduration = 3
			}
		},
		katarina = {
			{
				spellname = "katarinar",
				menuslot = "R",
				slot = 3,
				channelduration = 2.5
			}
		},
		lucian = {
			{
				spellname = "lucianr",
				menuslot = "R",
				slot = 3,
				channelduration = 2
			}
		},
		malzahar = {
			{
				spellname = "malzaharr",
				menuslot = "R",
				slot = 3,
				channelduration = 2.5
			}
		},
		masteryi = {
			{
				spellname = "meditate",
				menuslot = "W",
				slot = 1,
				channelduration = 4
			}
		},
		missfortune = {
			{
				spellname = "missfortunebullettime",
				menuslot = "R",
				slot = 3,
				channelduration = 3
			}
		},
		nunu = {
			{
				spellname = "nunur",
				menuslot = "R",
				slot = 3,
				channelduration = 3
			}
		},
		pantheon = {
			{
				spellname = "pantheonrjump",
				menuslot = "R",
				slot = 3,
				channelduration = 2
			}
		},
		quinn = {
			{
				spellname = "quinr",
				menuslot = "R",
				slot = 3,
				channelduration = 2
			}
		},
		shen = {
			{
				spellname = "shenr",
				menuslot = "R",
				slot = 3,
				channelduration = 3
			}
		},
		sion = {
			{
				spellname = "sionq",
				menuslot = "Q",
				slot = 0,
				channelduration = 2
			}
		},
		tahmkench = {
			{
				spellname = "tahmkenchnewr",
				menuslot = "R",
				slot = 3,
				channelduration = 3
			}
		},
		twistedfate = {
			{
				spellname = "gate",
				menuslot = "R",
				slot = 3,
				channelduration = 1.5
			}
		},
		varus = {
			{
				spellname = "varusq",
				menuslot = "Q",
				slot = 0,
				channelduration = 4
			}
		},
		velkoz = {
			{
				spellname = "velkozr",
				menuslot = "R",
				slot = 3,
				channelduration = 2.5
			}
		},
		warwick = {
			{
				spellname = "warwickrchannel",
				menuslot = "R",
				slot = 3,
				channelduration = 1.5
			}
		},
		xerath = {
			{
				spellname = "xeratharcanopulsechargeup",
				menuslot = "Q",
				slot = 0,
				channelduration = 3
			},
			{
				spellname = "xerathlocusofpower2",
				menuslot = "R",
				slot = 3,
				channelduration = 3
			}
		},
		zac = {
			{
				spellname = "zace",
				menuslot = "E",
				slot = 2,
				channelduration = 4
			}
		}
	}
end

function var_0_2.GetAllyHeroes()
	return var_0_2.units.allies
end

function var_0_2.GetJungleMinions()
	return var_0_2.units.jungleMinions
end

var_0_2._fountain = nil
var_0_2._fountainRadius = 750

function var_0_2.GetFountain()
	if var_0_2._fountain then
		return var_0_2._fountain
	end

	local var_53_0 = var_0_2.GetMap()

	if var_53_0 and var_53_0.index and var_53_0.index == 1 then
		var_0_2._fountainRadius = 1050
	end

	if var_0_2.GetShop() then
		objManager.loop(function(arg_54_0)
			if arg_54_0 and arg_54_0.team == TEAM_ALLY and arg_54_0.name:lower():find("spawn") and not arg_54_0.name:lower():find("troy") and not arg_54_0.name:lower():find("barracks") then
				var_0_2._fountain = arg_54_0

				return var_0_2._fountain
			end
		end)
	end

	return nil
end

function var_0_2.NearFountain(arg_55_0)
	local var_55_0 = arg_55_0 or var_0_2._fountainRadius or 0
	local var_55_1 = var_0_2.GetFountain()

	if var_55_1 then
		return player.pos2D:distSqr(var_55_1.pos2D) <= var_55_0 * var_55_0, var_55_1.x, var_55_1.y, var_55_1.z, var_55_0
	else
		return false, 0, 0, 0, 0
	end
end

var_0_2._map = {
	index = 0,
	name = "unknown"
}

function var_0_2.GetMap()
	if var_0_2._map.index ~= 0 then
		return var_0_2._map
	end

	local var_56_0 = var_0_2.GetShop()

	if var_56_0 then
		if math.floor(var_56_0.x) == 232 and math.floor(var_56_0.y) == 163 and math.floor(var_56_0.z) == 1277 then
			var_0_2._map = {
				index = 1,
				name = "Summoner's Rift"
			}
		elseif math.floor(var_56_0.x) == 1313 and math.floor(var_56_0.y) == 123 and math.floor(var_56_0.z) == 8005 then
			var_0_2._map = {
				index = 4,
				name = "Twisted Treeline"
			}
		elseif math.floor(var_56_0.x) == 497 and math.floor(var_56_0.y) == -40 and math.floor(var_56_0.z) == 1932 then
			var_0_2._map = {
				index = 12,
				name = "Howling Abyss"
			}
		else
			print("Unknown Map! Shop: x:" .. tostring(math.floor(var_56_0.x)) .. " y:" .. tostring(math.floor(var_56_0.y)) .. " z:" .. tostring(math.floor(var_56_0.z)))
		end
	end

	return var_0_2._map
end

function var_0_2.InFountain()
	return var_0_2.NearFountain()
end

var_0_2._shop = nil
var_0_2._shopRadius = 1250

function var_0_2.GetShop()
	if var_0_2._shop then
		return var_0_2._shop
	end

	objManager.loop(function(arg_59_0)
		if arg_59_0 and arg_59_0.team == TEAM_ALLY and arg_59_0.name:lower():find("shop") then
			var_0_2._shop = arg_59_0

			return var_0_2._shop
		end
	end)

	return nil
end

return var_0_2

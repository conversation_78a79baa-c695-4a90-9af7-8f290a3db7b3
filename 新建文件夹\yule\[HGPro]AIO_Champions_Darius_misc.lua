local slot_13_54 = module.internal("damagelib")

local function slot_13_55(arg_20_0, arg_20_1, arg_20_2)
	return (arg_20_1.y - arg_20_0.y) * (arg_20_2.x - arg_20_1.x) - (arg_20_1.x - arg_20_0.x) * (arg_20_2.y - arg_20_1.y)
end

local function slot_13_56(arg_21_0, arg_21_1, arg_21_2, arg_21_3)
	local slot_21_0 = arg_21_0.y - arg_21_1.y
	local slot_21_1 = arg_21_1.x - arg_21_0.x
	local slot_21_2 = slot_21_0 * arg_21_0.x + slot_21_1 * arg_21_0.y
	local slot_21_3 = arg_21_2.y - arg_21_3.y
	local slot_21_4 = arg_21_3.x - arg_21_2.x
	local slot_21_5 = slot_21_3 * arg_21_2.x + slot_21_4 * arg_21_2.y
	local slot_21_6 = slot_21_0 * slot_21_4 - slot_21_3 * slot_21_1

	if slot_21_6 == 0 then
		return nil
	end

	local slot_21_7 = (slot_21_4 * slot_21_2 - slot_21_1 * slot_21_5) / slot_21_6
	local slot_21_8 = (slot_21_0 * slot_21_5 - slot_21_3 * slot_21_2) / slot_21_6

	return {
		x = slot_21_7,
		y = slot_21_8
	}
end

local function slot_13_57(arg_22_0, arg_22_1, arg_22_2)
	for iter_22_0 = 0, arg_22_0:ChildCount() - 2 do
		slot7 = arg_22_0:Childs(iter_22_0)
		slot8 = arg_22_0:Childs(iter_22_0 + 1)

		if slot_13_55(arg_22_2, arg_22_1, slot7) <= 0 ~= (slot_13_55(arg_22_2, arg_22_1, slot8) <= 0) and slot_13_55(arg_22_1, slot8, slot7) <= 0 ~= (slot_13_55(arg_22_2, slot8, slot7) <= 0) then
			return true
		end
	end

	slot3 = arg_22_0:Childs(0)
	slot4 = arg_22_0:Childs(arg_22_0:ChildCount() - 1)

	if slot_13_55(arg_22_2, arg_22_1, slot3) <= 0 ~= (slot_13_55(arg_22_2, arg_22_1, slot4) <= 0) and slot_13_55(arg_22_1, slot4, slot3) <= 0 ~= (slot_13_55(arg_22_2, slot4, slot3) <= 0) then
		return true
	end

	return false
end

local function slot_13_58(arg_23_0, arg_23_1, arg_23_2)
	local slot_23_0 = {}

	for iter_23_0 = 0, arg_23_0:ChildCount() - 2 do
		local slot_23_1 = arg_23_0:Childs(iter_23_0)
		local slot_23_2 = arg_23_0:Childs(iter_23_0 + 1)

		if slot_13_55(arg_23_2, arg_23_1, slot_23_1) <= 0 ~= (slot_13_55(arg_23_2, arg_23_1, slot_23_2) <= 0) then
			local slot_23_3 = slot_13_56(arg_23_1, arg_23_2, slot_23_1, slot_23_2)

			if slot_23_3 then
				table.insert(slot_23_0, slot_23_3)
			end
		end
	end

	local slot_23_4 = arg_23_0:Childs(0)
	local slot_23_5 = arg_23_0:Childs(arg_23_0:ChildCount() - 1)

	if slot_13_55(arg_23_2, arg_23_1, slot_23_4) <= 0 ~= (slot_13_55(arg_23_2, arg_23_1, slot_23_5) <= 0) then
		local slot_23_6 = slot_13_56(arg_23_1, arg_23_2, slot_23_4, slot_23_5)

		if slot_23_6 then
			table.insert(slot_23_0, slot_23_6)
		end
	end

	local slot_23_7
	local slot_23_8 = math.huge

	for iter_23_1, iter_23_2 in ipairs(slot_23_0) do
		local slot_23_9 = (iter_23_2.x - arg_23_1.x) ^ 2 + (iter_23_2.y - arg_23_1.y) ^ 2

		if slot_23_9 < slot_23_8 then
			slot_23_7 = iter_23_2
			slot_23_8 = slot_23_9
		end
	end

	return slot_23_7
end

local function slot_13_59(arg_24_0, arg_24_1)
	if not arg_24_0.buff.dariushemo or arg_24_0.buff.dariushemo.stacks == 0 then
		return 0
	end

	local slot_24_0 = player.flatPhysicalDamageMod
	local slot_24_1 = (3 + 0.25 * player.levelRef + slot_24_0 * 0.075) * arg_24_0.buff.dariushemo.stacks
	local slot_24_2 = (15 + 1.25 * player.levelRef + slot_24_0 * 0.05) * arg_24_0.buff.dariushemo.stacks
	local slot_24_3 = 100 / (100 + arg_24_0.armor)
	local slot_24_4 = {
		30,
		35,
		40,
		45,
		50,
		55,
		60,
		65,
		70,
		75,
		85,
		95,
		130,
		150,
		180,
		205,
		230
	}

	if slot_24_2 < slot_24_1 then
		slot_24_1 = slot_24_2
	end

	return slot_24_1 * slot_24_3 * arg_24_1
end

local function slot_13_60(arg_25_0, arg_25_1)
	local slot_25_0 = player.flatPhysicalDamageMod
	local slot_25_1 = (3 + 0.25 * player.levelRef + slot_25_0 * 0.075) * 5
	local slot_25_2 = (15 + 1.25 * player.levelRef + slot_25_0 * 0.05) * 5
	local slot_25_3 = 100 / (100 + arg_25_0.armor)
	local slot_25_4 = {
		30,
		35,
		40,
		45,
		50,
		55,
		60,
		65,
		70,
		75,
		85,
		95,
		130,
		150,
		180,
		205,
		230
	}

	if slot_25_2 < slot_25_1 then
		slot_25_1 = slot_25_2
	end

	return slot_25_1 * slot_25_3 * arg_25_1
end

local function slot_13_61(arg_26_0)
	return (slot_13_54.calc_aa_damage(player, arg_26_0, true))
end

local function slot_13_62(arg_27_0)
	if player:spellSlot(0).level == 0 then
		return 0
	end

	return (slot_13_54.get_spell_damage("DariusCleave", 0, player, arg_27_0, true, 0))
end

local function slot_13_63(arg_28_0)
	if player:spellSlot(1).level == 0 then
		return 0
	end

	return (slot_13_54.get_spell_damage("DariusNoxianTacticsONH", 1, player, arg_28_0, true, 0))
end

local function slot_13_64(arg_29_0)
	if player:spellSlot(3).level == 0 then
		return 0
	end

	local slot_29_0 = slot_13_54.get_spell_damage("DariusExecute", 3, player, arg_29_0, true, 0)

	if arg_29_0.buff.dariushemo and arg_29_0.buff.dariushemo.stacks > 0 then
		slot_29_0 = slot_29_0 + slot_29_0 * (0.2 * arg_29_0.buff.dariushemo.stacks)
	end

	return slot_29_0
end

return {
	QDmg = slot_13_62,
	WDmg = slot_13_63,
	RDmg = slot_13_64,
	AADmg = slot_13_61,
	PassiveDmg = slot_13_59,
	PassiveDmg2 = slot_13_60,
	line_polygon_intersection_point = slot_13_58
}

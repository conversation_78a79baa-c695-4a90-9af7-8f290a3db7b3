local slot_13_54 = module.internal("orb")
local slot_13_55 = module.internal("pred")
local slot_13_56 = module.seek("evade")
local slot_13_57 = module.load(header.id, "Libraries/common")
local slot_13_58 = module.load(header.id, "Champions/" .. player.charName .. "/menu")
local slot_13_59 = 0
local slot_13_60 = slot_13_57.GetFlashNum()

local function slot_13_61(arg_20_0, arg_20_1, arg_20_2)
	if slot_13_55.trace.linear.hardlock(arg_20_0, arg_20_1, arg_20_2) then
		return true
	end

	if slot_13_55.trace.linear.hardlockmove(arg_20_0, arg_20_1, arg_20_2) then
		return true
	end

	if arg_20_2 and slot_13_57.isValid(arg_20_2) and player.pos:dist(arg_20_2.pos) < player.attackRange + player.boundingRadius + arg_20_2.boundingRadius then
		return true
	end

	if arg_20_1.startPos:dist(arg_20_1.endPos) < 600 and slot_13_55.trace.newpath(arg_20_2, 0.033, 0.65) then
		return true
	end

	if slot_13_55.trace.newpath(arg_20_2, 0.033, 0.95) then
		return true
	end
end

local function slot_13_62(arg_21_0, arg_21_1, arg_21_2)
	if slot_13_55.trace.linear.hardlock(arg_21_0, arg_21_1, arg_21_2) then
		return true
	end

	if slot_13_55.trace.linear.hardlockmove(arg_21_0, arg_21_1, arg_21_2) then
		return true
	end

	if arg_21_1.startPos:dist(arg_21_1.endPos) < 600 then
		return true
	end

	if slot_13_55.trace.newpath(arg_21_2, 0.033, 0.7) then
		return true
	end
end

local function slot_13_63(arg_22_0)
	if player:spellSlot(arg_22_0).name:find("One") then
		return true
	else
		return false
	end
end

local function slot_13_64(arg_23_0, arg_23_1, arg_23_2, arg_23_3, arg_23_4)
	local slot_23_0 = {
		delay = 0.25,
		boundingRadiusMod = 0,
		speed = 1000,
		width = arg_23_0,
		collision = {
			minion = false,
			hero = true
		}
	}
	local slot_23_1 = {
		startPos = arg_23_1.path.serverPos2D,
		endPos = vec2(arg_23_2.x, arg_23_2.z)
	}

	if slot_23_1.startPos and slot_23_1.endPos then
		local slot_23_2 = slot_13_55.collision.get_prediction(slot_23_0, slot_23_1)

		if slot_23_2 and arg_23_4 <= #slot_23_2 then
			player:castSpell("obj", 3, arg_23_3)

			return
		end
	end

	return false
end

local function slot_13_65(arg_24_0)
	if player:spellSlot(1).state ~= 0 or not slot_13_63(1) then
		return false
	end

	local slot_24_0 = slot_13_57.WardSlot()
	local slot_24_1 = slot_13_57.CountAllyObj(player, 700)
	local slot_24_2 = arg_24_0

	if player.path.isDashing and player.path.serverPos:distSqr(slot_24_2) > 360000 then
		slot_24_2 = player.path.serverPos +
		595 / player.path.serverPos:dist(slot_24_2) * (slot_24_2 - player.path.serverPos)
	elseif player.path.serverPos:distSqr(slot_24_2) > 360000 then
		slot_24_2 = player.pos + 595 / player.pos:dist(slot_24_2) * (slot_24_2 - player.pos)
	end

	local slot_24_3

	objManager.loop(function(arg_25_0)
		if arg_25_0 and arg_25_0.isVisible and arg_25_0.isTargetableToTeamFlags and arg_25_0.team == TEAM_ALLY and (arg_25_0.name:lower():find("ward") or arg_25_0.name:lower():find("jammerdevice")) and arg_25_0.pos:dist(slot_24_2) <= 100 then
			slot_24_3 = arg_25_0
		end
	end)

	if game.time > slot_13_59 + 1 and slot_24_0 and not navmesh.isWall(vec3(slot_24_2.x, slot_24_2.y, slot_24_2.z)) then
		player:castSpell("pos", slot_24_0, vec3(slot_24_2.x, slot_24_2.y, slot_24_2.z))

		slot_13_59 = game.time

		player:castSpell("pos", 1, vec3(slot_24_2.x, slot_24_2.y, slot_24_2.z))

		return true
	elseif slot_24_3 then
		player:castSpell("obj", 1, slot_24_3)

		return true
	elseif game.time > slot_13_59 + 1 and slot_24_1 and slot_24_1.pos:distSqr(slot_24_2) <= 10000 then
		player:castSpell("obj", 1, slot_24_1)

		return true
	elseif game.time > slot_13_59 + 1 and slot_13_57.GetMinionsAllyObj(slot_24_2, 100) then
		player:castSpell("obj", 1, slot_13_57.GetMinionsAllyObj(slot_24_2, 100))

		return true
	end
end

function nearest_none_wall(arg_26_0)
	local slot_26_0, slot_26_1 = navmesh.get_cell(arg_26_0.x, arg_26_0.y)
	local slot_26_2
	local slot_26_3 = math.huge
	local slot_26_4 = {}

	for iter_26_0 = 1, 20 do
		for iter_26_1 = iter_26_0, -iter_26_0, -1 do
			for iter_26_2 = iter_26_0, -iter_26_0, -1 do
				local slot_26_5 = navmesh.get_cell_index(slot_26_0 + iter_26_1, slot_26_1 + iter_26_2)

				if not slot_26_4[slot_26_5] then
					local slot_26_6 = navmesh.cell_info[slot_26_5].type

					if bit.band(slot_26_6, 2) ~= 2 then
						local slot_26_7 = iter_26_1 * iter_26_1 + iter_26_2 * iter_26_2

						if not slot_26_2 or slot_26_7 < slot_26_3 then
							slot_26_3 = slot_26_7
							slot_26_2 = vec2(navmesh.startPos.x + (slot_26_0 + iter_26_1) * 50 + 25,
								navmesh.startPos.z + (slot_26_1 + iter_26_2) * 50 + 25)
						end
					end

					slot_26_4[slot_26_5] = true
				end
			end
		end
	end

	return slot_26_2
end

local function slot_13_66(arg_27_0)
	if not slot_13_54.menu.combat.key:get() and not slot_13_54.menu.hybrid.key:get() and not slot_13_58.inseckey:get() and not slot_13_54.menu.lane_clear.key:get() then
		if player.path.isDashing then
			player:move(player.path.serverPos +
			750 / player.path.serverPos:dist(mousePos) * (mousePos - player.path.serverPos))
		else
			player:move(player.path.serverPos +
			200 / player.path.serverPos:dist(mousePos) * (mousePos - player.path.serverPos))
		end
	end

	if player:spellSlot(1).state ~= 0 or not slot_13_63(1) then
		return false
	end

	local slot_27_0 = slot_13_57.WardSlot()
	local slot_27_1 = slot_13_57.CountAllyObj(player, 700)
	local slot_27_2 = arg_27_0

	if player.path.isDashing and player.path.serverPos:distSqr(slot_27_2) > 360000 then
		slot_27_2 = player.path.serverPos +
		595 / player.path.serverPos:dist(slot_27_2) * (slot_27_2 - player.path.serverPos)
	elseif player.path.serverPos:distSqr(slot_27_2) > 360000 then
		slot_27_2 = player.pos + 595 / player.pos:dist(slot_27_2) * (slot_27_2 - player.pos)
	end

	if slot_13_58.ward.wardposmode:get() == 1 and slot_13_58.wardjump:get() then
		if player.path.isDashing and player.path.serverPos:distSqr(slot_27_2) < 360000 then
			slot_27_2 = player.path.serverPos +
			595 / player.path.serverPos:dist(slot_27_2) * (slot_27_2 - player.path.serverPos)
		elseif player.path.serverPos:distSqr(slot_27_2) < 360000 then
			slot_27_2 = player.pos + 595 / player.pos:dist(slot_27_2) * (slot_27_2 - player.pos)
		end
	end

	if navmesh.isWall(vec3(slot_27_2.x, slot_27_2.y, slot_27_2.z)) then
		local slot_27_3 = nearest_none_wall(slot_27_2:to2D())

		if slot_27_3:distSqr(player.path.serverPos2D) <= 360000 and slot_27_3:distSqr(player.path.serverPos2D) > player.path.serverPos2D:distSqr(slot_27_2:to2D()) then
			slot_27_2 = vec3(slot_27_3.x, 0, slot_27_3.y)
		end
	end

	local slot_27_4

	objManager.loop(function(arg_28_0)
		if arg_28_0 and arg_28_0.isVisible and arg_28_0.isTargetableToTeamFlags and arg_28_0.team == TEAM_ALLY and (arg_28_0.name:lower():find("ward") or arg_28_0.name:lower():find("jammerdevice")) and arg_28_0.pos:dist(slot_27_2) <= 200 then
			slot_27_4 = arg_28_0
		end
	end)

	if game.time > slot_13_59 + 1 and slot_27_1 and slot_27_1.pos:distSqr(slot_27_2) <= 40000 then
		player:castSpell("obj", 1, slot_27_1)

		return true
	elseif slot_27_4 then
		player:castSpell("obj", 1, slot_27_4)

		return true
	elseif game.time > slot_13_59 + 1 and slot_13_57.GetMinionsAllyObj(slot_27_2, 200) then
		player:castSpell("obj", 1, slot_13_57.GetMinionsAllyObj(slot_27_2, 200))

		return true
	elseif game.time > slot_13_59 + 1 and slot_27_0 and not navmesh.isWall(vec3(slot_27_2.x, slot_27_2.y, slot_27_2.z)) then
		player:castSpell("pos", slot_27_0, vec3(slot_27_2.x, slot_27_2.y, slot_27_2.z))

		slot_13_59 = game.time

		player:castSpell("pos", 1, vec3(slot_27_2.x, slot_27_2.y, slot_27_2.z))

		return true
	end
end

local function slot_13_67(arg_29_0)
	if not slot_13_54.menu.combat.key:get() and not slot_13_54.menu.hybrid.key:get() and not slot_13_54.menu.last_hit.key:get() and not slot_13_54.menu.lane_clear.key:get() then
		if player.path.isDashing then
			player:move(player.path.serverPos +
			750 / player.path.serverPos:dist(mousePos) * (mousePos - player.path.serverPos))
		else
			player:move(player.path.serverPos +
			200 / player.path.serverPos:dist(mousePos) * (mousePos - player.path.serverPos))
		end
	end

	if player:spellSlot(1).state ~= 0 or not slot_13_63(1) then
		return false
	end

	local slot_29_0 = slot_13_57.WardSlot()
	local slot_29_1 = slot_13_57.CountAllyObj(player, 950)
	local slot_29_2 = arg_29_0

	if slot_13_60 and player:spellSlot(slot_13_60).state == 0 then
		if player.path.isDashing and player.path.serverPos:distSqr(slot_29_2) > 1000000 then
			slot_29_2 = player.path.serverPos +
			995 / player.path.serverPos:dist(slot_29_2) * (slot_29_2 - player.path.serverPos)
		elseif player.path.serverPos:distSqr(slot_29_2) > 1000000 then
			slot_29_2 = player.pos + 995 / player.pos:dist(slot_29_2) * (slot_29_2 - player.pos)
		end

		if slot_13_58.wardjumpflash:get() then
			if player.path.isDashing and player.path.serverPos:distSqr(slot_29_2) < 1000000 then
				slot_29_2 = player.path.serverPos +
				995 / player.path.serverPos:dist(slot_29_2) * (slot_29_2 - player.path.serverPos)
			elseif player.path.serverPos:distSqr(slot_29_2) < 1000000 then
				slot_29_2 = player.pos + 995 / player.pos:dist(slot_29_2) * (slot_29_2 - player.pos)
			end
		end
	else
		if player.path.isDashing and player.path.serverPos:distSqr(slot_29_2) > 360000 then
			slot_29_2 = player.path.serverPos +
			595 / player.path.serverPos:dist(slot_29_2) * (slot_29_2 - player.path.serverPos)
		elseif player.path.serverPos:distSqr(slot_29_2) > 360000 then
			slot_29_2 = player.pos + 595 / player.pos:dist(slot_29_2) * (slot_29_2 - player.pos)
		end

		if slot_13_58.ward.wardposmode:get() == 1 and slot_13_58.wardjump:get() then
			if player.path.isDashing and player.path.serverPos:distSqr(slot_29_2) < 360000 then
				slot_29_2 = player.path.serverPos +
				595 / player.path.serverPos:dist(slot_29_2) * (slot_29_2 - player.path.serverPos)
			elseif player.path.serverPos:distSqr(slot_29_2) < 360000 then
				slot_29_2 = player.pos + 595 / player.pos:dist(slot_29_2) * (slot_29_2 - player.pos)
			end
		end
	end

	if navmesh.isWall(vec3(slot_29_2.x, slot_29_2.y, slot_29_2.z)) then
		local slot_29_3 = nearest_none_wall(slot_29_2:to2D())

		if slot_29_3:distSqr(player.path.serverPos2D) <= 902500 and slot_29_3:distSqr(player.path.serverPos2D) > player.path.serverPos2D:distSqr(slot_29_2:to2D()) then
			slot_29_2 = vec3(slot_29_3.x, 0, slot_29_3.y)
		end
	end

	local slot_29_4

	objManager.loop(function(arg_30_0)
		if arg_30_0 and arg_30_0.isVisible and arg_30_0.isTargetableToTeamFlags and arg_30_0.team == TEAM_ALLY and (arg_30_0.name:lower():find("ward") or arg_30_0.name:lower():find("jammerdevice")) and arg_30_0.pos:dist(slot_29_2) <= 200 then
			slot_29_4 = arg_30_0
		end
	end)

	if game.time > slot_13_59 + 1 and slot_29_1 and slot_29_1.pos:distSqr(slot_29_2) <= 10000 then
		if slot_13_60 and player:spellSlot(slot_13_60).state == 0 then
			player:castSpell("pos", slot_13_60, vec3(slot_29_2.x, slot_29_2.y, slot_29_2.z))
		end

		player:castSpell("obj", 1, slot_29_1)

		return true
	elseif slot_29_4 then
		if slot_13_60 and player:spellSlot(slot_13_60).state == 0 then
			player:castSpell("pos", slot_13_60, vec3(slot_29_2.x, slot_29_2.y, slot_29_2.z))
		end

		player:castSpell("obj", 1, slot_29_4)

		return true
	elseif game.time > slot_13_59 + 1 and slot_13_57.GetMinionsAllyObj(slot_29_2, 100) then
		if slot_13_60 and player:spellSlot(slot_13_60).state == 0 then
			player:castSpell("pos", slot_13_60, vec3(slot_29_2.x, slot_29_2.y, slot_29_2.z))
		end

		player:castSpell("obj", 1, slot_13_57.GetMinionsAllyObj(slot_29_2, 100))

		return true
	elseif game.time > slot_13_59 + 1 and slot_29_0 and not navmesh.isWall(vec3(slot_29_2.x, slot_29_2.y, slot_29_2.z)) then
		player:castSpell("pos", slot_29_0, vec3(slot_29_2.x, slot_29_2.y, slot_29_2.z))

		slot_13_59 = game.time

		if slot_13_60 and player:spellSlot(slot_13_60).state == 0 then
			player:castSpell("pos", slot_13_60, vec3(slot_29_2.x, slot_29_2.y, slot_29_2.z))
		end

		slot_13_54.core.set_pause_move(0.5)
		slot_13_56.core.set_pause(0.5)
		slot_13_57.DelayAction(function()
			player:castSpell("pos", 1, vec3(slot_29_2.x, slot_29_2.y, slot_29_2.z))
		end, 0.2)

		return true
	end
end

local function slot_13_68(arg_32_0)
	if player:spellSlot(0).level == 0 then
		return 0
	end

	local slot_32_0 = {
		50,
		75,
		100,
		125,
		150
	}
	local slot_32_1 = player:spellSlot(0).level
	local slot_32_2 = player.flatPhysicalDamageMod
	local slot_32_3 = 100 / (100 + arg_32_0.armor)

	return (slot_32_0[slot_32_1] + slot_32_2) * slot_32_3
end

local function slot_13_69(arg_33_0, arg_33_1)
	if player:spellSlot(0).level == 0 then
		return 0
	end

	local slot_33_0 = {
		50,
		75,
		100,
		125,
		150
	}
	local slot_33_1 = player:spellSlot(0).level
	local slot_33_2 = player.flatPhysicalDamageMod
	local slot_33_3 = 100 / (100 + arg_33_0.armor)

	return (slot_33_0[slot_33_1] + slot_33_2) * slot_33_3 *
	(1 + (arg_33_0.maxHealth - (arg_33_0.health - arg_33_1)) / arg_33_0.maxHealth)
end

local function slot_13_70(arg_34_0)
	local slot_34_0 = slot_13_68(arg_34_0)

	return slot_34_0 + slot_13_69(arg_34_0, slot_34_0)
end

local function slot_13_71(arg_35_0)
	if player:spellSlot(2).level == 0 then
		return 0
	end

	local slot_35_0 = {
		70,
		105,
		140,
		175,
		210
	}
	local slot_35_1 = player:spellSlot(2).level
	local slot_35_2 = player.flatPhysicalDamageMod
	local slot_35_3 = 100 / (100 + arg_35_0.spellBlock)

	return (slot_35_0[slot_35_1] + slot_35_2) * slot_35_3
end

local function slot_13_72(arg_36_0)
	if player:spellSlot(3).level == 0 then
		return 0
	end

	local slot_36_0 = {
		150,
		375,
		600
	}
	local slot_36_1 = player:spellSlot(3).level
	local slot_36_2 = player.flatPhysicalDamageMod
	local slot_36_3 = 100 / (100 + arg_36_0.armor)

	return (slot_36_0[slot_36_1] + slot_36_2 * 2) * slot_36_3
end

local function slot_13_73(arg_37_0)
	return (player.baseAttackDamage + player.flatPhysicalDamageMod) * (100 / (100 + arg_37_0.armor))
end

return {
	CountEnemiesInR = slot_13_64,
	WardJump = slot_13_66,
	WardJumpFlash = slot_13_67,
	trace_filter = slot_13_61,
	trace_filterR = slot_13_62,
	FirstSpell = slot_13_63,
	InsecJump = slot_13_65,
	Q1Dmg = slot_13_68,
	Q2Dmg = slot_13_69,
	Q3Dmg = slot_13_70,
	EDmg = slot_13_71,
	RDmg = slot_13_72,
	AADmg = slot_13_73
}

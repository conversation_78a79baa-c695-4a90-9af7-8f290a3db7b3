local prediction = {}

local DelayAction = module.load("<PERSON>", "Core/DelayAction")
local Database = module.load("<PERSON>", "Damage/Database")
local SimpleLib = module.load("<PERSON>", "Damage/SimpleLib")
local BuffManager = module.load("<PERSON>", "Library/BuffManager")
local CalculateManager = module.load("<PERSON>", "Library/CalculateManager")
local NetManager = module.load("<PERSON>", "Library/NetManager")
local ObjectManager = module.load("<PERSON>", "Library/ObjectManager")
local MyCommon = module.load("<PERSON>", "Library/ExtraManager")
local VectorManager = module.load("<PERSON>", "Library/VectorManager")

local isLoadingSuccessful = false

local removeDelay = 850

local damageTable = {}
local spellTable = {}

function prediction.GetDamage(hero)
    if hero == nil or not hero then
        return 0 
    end
    local myTable = damageTable[hero.ptr]
    if myTable then
        return myTable
    end
    return 0
end

local function ShouldReset(hero)
    if not hero or hero == nil or not MyCommon.IsValidTarget(hero) then 
        return true 
    end
    if not hero.isTargetable or BuffManager.HasBuffOfType(hero, 17) then 
        return true 
    end
    return false
end

local function GetTime(sender, hero, spellData)
    local castFrame = 0
    if spellData.static.castFrame then
        castFrame = spellData.static.castFrame / 30
        if castFrame < 2 then
            castFrame = 2
        end
    end
    local missileSpeed = spellData.static.missileSpeed
    if missileSpeed and missileSpeed > 5000 then
        missileSpeed = 5000
    end
    local distanceRange = 500
    if missileSpeed > 500 and missileSpeed then
        distanceRange = missileSpeed
    end
    return castFrame - 100 + (NetManager.Ping()) / 200 + sender.pos:dist(hero.pos) / distanceRange * 1000
end

local function AddDamage(hero, delay, damage)
    if not hero or hero == nil or not MyCommon.IsValidTarget(hero) then
        return
    end
    if delay >= 5000 or damage <= 0 then 
        return 
    end
    if delay < 0 then delay = 0 end
    local myTable = damageTable[hero.ptr]
    if myTable then
        DelayAction.Add(
            function() 
                local d = damageTable[hero.ptr]
                damageTable[hero.ptr] = d + damage 
            end, (delay / 1000))
        DelayAction.Add(
            function() 
                local d = damageTable[hero.ptr]
                if d - damage < 0 or d - damage == 0 then
                    damageTable[hero.ptr] = 0
                else
                    damageTable[hero.ptr] = d - damage 
                end
            end, ((delay + removeDelay) / 1000))
        return
    end
end

local function IsSpellSlot(slot)
    if slot then
        if slot == 0 or slot == 1 or slot == 2 or slot == 3 then
            return true
        end
    end
    return false
end

local function OnMyProcessSpellCast(spellData)
    if spellData and spellData.owner and spellData.owner ~= nil and spellData.owner.team ~= player.team and spellData.name then
        local sender = spellData.owner
        if not sender or sender == nil or (sender.type ~= TYPE_HERO and sender.type ~= TYPE_TURRET) or sender.team == player.team then
            return
        end
        if sender.type == TYPE_TURRET then
            if spellData.isBasicAttack or Database.IsAutoAttack(spellData.name) then
                local alliesList = ObjectManager.GetAllyHeroes()
                for i, hero in ipairs(alliesList) do
                    if hero and hero ~= nil and hero.ptr then
                        local myTable = damageTable[hero.ptr]
                        if not myTable then
                            damageTable[hero.ptr] = 0
                            myTable = damageTable[hero.ptr]
                        end
                        if myTable then
                            while true do
                                if ShouldReset(hero) then
                                    myTable = 0
                                    break
                                end
                                if sender.pos and hero.pos and sender.pos:dist(hero.pos) < 1500 then
                                    if spellData.hasTarget and spellData.target and spellData.target ~= nil and spellData.target.ptr and spellData.target.ptr == hero.ptr then
                                        local damage = CalculateManager.GetAutoAttackDamage(sender, hero)
                                        local delay = GetTime(sender, hero, spellData)
                                        if damage and damage > 0 and delay then
                                            AddDamage(hero, delay, damage)
                                            break
                                        end
                                    end
                                end
                                break
                            end
                        end
                    end
                end
                return
            end
            return
        elseif sender.type == TYPE_HERO then
            if spellData.isBasicAttack or Database.IsAutoAttack(spellData.name) then
                local alliesList = ObjectManager.GetAllyHeroes()
                for i, hero in ipairs(alliesList) do
                    if hero and hero ~= nil and hero.ptr then
                        local myTable = damageTable[hero.ptr]
                        if not myTable then
                            damageTable[hero.ptr] = 0
                            myTable = damageTable[hero.ptr]
                        end
                        if myTable then
                            while true do
                                if ShouldReset(hero) then
                                    myTable = 0
                                    break
                                end
                                if sender.pos and hero.pos and sender.pos:dist(hero.pos) < 1500 then
                                    if spellData.hasTarget and spellData.target and spellData.target ~= nil and spellData.target.ptr and spellData.target.ptr == hero.ptr then
                                        local damage = CalculateManager.GetAutoAttackDamage(sender, hero)
                                        local delay = GetTime(sender, hero, spellData)
                                        if damage and damage > 0 and delay then
                                            AddDamage(hero, delay, damage)
                                            break
                                        end
                                    end
                                end
                                break
                            end
                        end
                    end
                end
                return
            else
                local alliesList = ObjectManager.GetAllyHeroes()
                for i, hero in ipairs(alliesList) do
                    if hero and hero ~= nil and hero.ptr then
                        local myTable = damageTable[hero.ptr]
                        if not myTable then
                            damageTable[hero.ptr] = 0
                            myTable = damageTable[hero.ptr]
                        end
                        if myTable then
                            while true do
                                if ShouldReset(hero) then
                                    myTable = 0
                                    break
                                end
                                if spellData.slot and IsSpellSlot(spellData.slot) then
                                    if sender.pos and hero.pos and sender.pos:dist(hero.pos) < 3500 then
                                        if spellData.hasTarget then
                                            if spellData.target and spellData.target ~= nil and spellData.target.ptr and spellData.target.ptr == hero.ptr then
                                                local damage = SimpleLib.GetSpellDamageFromName(spellData.name, hero, sender)
                                                local delay = GetTime(sender, hero, spellData)
                                                if damage and damage > 0 and delay then
                                                    AddDamage(hero, delay, damage)
                                                    break
                                                end
                                            end
                                            break
                                        elseif not spellData.hasTarget and spellData.endPos then
                                            local endPos = vec2(spellData.endPos.x, spellData.endPos.z)
                                            local startPos = vec2(0, 0)
                                            if spellData.startPos and spellData.startPos.x and spellData.startPos.y and spellData.startPos.z then
                                                startPos = vec2(spellData.startPos.x, spellData.startPos.z)
                                            elseif sender.pos and sender.pos.x and sender.pos.y and sender.pos.z then
                                                startPos = vec2(sender.pos.x, sender.pos.z)
                                            end
                                            if VectorManager.IsZeroPoint(startPos) then
                                                return
                                            end
                                            local lowerName = string.lower(spellData.name)
                                            if spellTable[lowerName] then
                                                local data = spellTable[lowerName]
                                                if data.range and data.radius and data.type then
                                                    if data.type == "circle" then
                                                        if Database.IsCircleHit(startPos, endPos, data, hero) then
                                                            local damage = SimpleLib.GetSpellDamage(spellData.slot, hero, sender)
                                                            local delay = GetTime(sender, hero, spellData) * 0.6
                                                            if data.delay then
                                                                delay = delay + data.delay * 1000 
                                                            end
                                                            if damage and damage > 0 and delay then
                                                                AddDamage(hero, delay, damage)
                                                                break
                                                            end
                                                        end
                                                    elseif data.type == "line" then
                                                        if Database.IsLineHit(startPos, endPos, data, hero) then
                                                            local damage = SimpleLib.GetSpellDamage(spellData.slot, hero, sender)
                                                            local delay = GetTime(sender, hero, spellData) * 0.6
                                                            if data.delay then
                                                                delay = delay + data.delay * 1000 
                                                            end
                                                            if damage and damage > 0 and delay then
                                                                AddDamage(hero, delay, damage)
                                                                break
                                                            end
                                                        end
                                                    elseif data.type == "cone" then
                                                        if Database.IsConeHit(startPos, endPos, data, hero) then
                                                            local damage = SimpleLib.GetSpellDamage(spellData.slot, hero, sender)
                                                            local delay = GetTime(sender, hero, spellData) * 0.6
                                                            if data.delay then
                                                                delay = delay + data.delay * 1000 
                                                            end
                                                            if damage and damage > 0 and delay then
                                                                AddDamage(hero, delay, damage)
                                                                break
                                                            end
                                                        end
                                                    elseif data.type == "cross" then
                                                        if Database.IsCrossHit(hero.pos2D, endPos) then
                                                            local damage = SimpleLib.GetSpellDamage(spellData.slot, hero, sender)
                                                            local delay = GetTime(sender, hero, spellData) * 0.6
                                                            if data.delay then
                                                                delay = delay + data.delay * 1000 
                                                            end
                                                            if damage and damage > 0 and delay then
                                                                AddDamage(hero, delay, damage)
                                                                break
                                                            end
                                                        end
                                                    elseif data.type == "rect" then
                                                        if Database.IsRecHit(startPos, endPos, data, hero) then
                                                            local damage = SimpleLib.GetSpellDamage(spellData.slot, hero, sender)
                                                            local delay = GetTime(sender, hero, spellData) * 0.6
                                                            if data.delay then
                                                                delay = delay + data.delay * 1000 
                                                            end
                                                            if damage and damage > 0 and delay then
                                                                AddDamage(hero, delay, damage)
                                                                break
                                                            end
                                                        end
                                                    end
                                                end 
                                            end
                                        end
                                    end
                                end
                                break
                            end
                        end
                    end
                end
                return
            end
        end
    end
end

-- local function OnMyDraw()
--     if player.isDead or chat.isOpened then
--         return 
--     end
--     local x = 100
--     local y = 300
--     local dmg = prediction.GetDamage(player)
--     graphics.draw_text_2D("Pred Damage: "..dmg, 16, x, y + 30, graphics.argb(255, 255, 255, 255))
-- end

local function Initialize()
    if isLoadingSuccessful then
        return
    end

    isLoadingSuccessful = true

    local allHeroes = ObjectManager.GetAllHeroes()
    for i, hero in ipairs(allHeroes) do
        if hero and hero ~= nil and hero.ptr then
            damageTable[hero.ptr] = 0
        end
    end

    local enemyHeroes = ObjectManager.GetEnemyHeroes()
    for a, target in ipairs(enemyHeroes) do
        if target and target ~= nil and target.charName and Database and Database.Spells then
            local tbl = Database.Spells[target.charName]
            if tbl then
                for spellName, spellData in pairs(tbl) do
                    if spellName and spellData then
                        local lowerName = string.lower(spellName)
                        spellTable[lowerName] = spellData
                    end
                end
            end
        end
    end
end

Initialize()

cb.add(cb.spell, OnMyProcessSpellCast)
-- cb.add(cb.draw, OnMyDraw)

return prediction
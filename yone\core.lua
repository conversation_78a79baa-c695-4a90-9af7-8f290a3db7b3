local Curses = module.load("<PERSON>", "Curses");
local ove_0_10 = module.internal("orb")
local ove_0_11 = module.load(header.id, "yone/menu")
local ove_0_12 = module.load(header.id, "draw/info")
local ove_0_13 = module.load(header.id, "draw/circle")
local ove_0_14 = module.load(header.id, "draw/stacks")
local flash_skills = module.load(header.id, "yone/flash_skills")
local ove_0_15 = module.load(header.id, "draw/timer")
local delayAction = module.load(header.id, "common/delayAction")
local flash_qr = module.load(header.id, "yone/flash_qr")  -- 加载闎�QR连招模块

-- BUFFöٳ
local ove_0_30 = {
	BUFF_ENUM_STUN = 5,
	BUFF_ENUM_TAUNT = 8,
	BUFF_ENUM_POLYMORPH = 22,
	BUFF_ENUM_SNARE = 11,
	BUFF_ENUM_FEAR = 21,
	BUFF_ENUM_CHARM = 7,
	BUFF_ENUM_SUPPRESSION = 24,
	BUFF_ENUM_KNOCKUP = 29,
	BUFF_ENUM_ASLEEP = 30
}

--ove_0_14.add_cb_sprite({
	--type = "menu",
	--menu = ove_0_11.info.q_stacks
--})

local ove_0_16 = module.load(header.id, "yone/q")
local ove_0_17 = module.load(header.id, "yone/q3")
local ove_0_18 = module.load(header.id, "yone/q3_f_red")
local ove_0_19 = module.load(header.id, "yone/q3_f_gap")
local ove_0_20 = module.load(header.id, "yone/e2")
local ove_0_21 = module.load(header.id, "yone/e_gap")
local ove_0_22 = module.load(header.id, "yone/e_aa")
local ove_0_23 = module.load(header.id, "yone/w")
local ove_0_24 = module.load(header.id, "yone/r")
local ove_0_25
local ove_0_26 = 0
local ove_0_27 = {}
local ove_0_28
local ove_0_29
local ove_0_31 = 0
local ove_0_32 = false
local ove_0_33 = 0

for iter_0_0 = 4, 5 do
	local ove_0_34 = player:spellSlot(iter_0_0)

	if ove_0_34.isNotEmpty and ove_0_34.name:lower():find("flash") then
		ove_0_28 = ove_0_34
		ove_0_29 = iter_0_0
	end
end

local ove_0_35 = 0

local ove_0_37 = false
local ove_0_38 = player:spellSlot(0)

-- 检�3+R连招
local function check_q3_r_combo()
	if player:spellSlot(0).name == "YoneQ3" and player:spellSlot(0).state == 0 and player:spellSlot(3).state == 0 then
		-- 如果有��量限�
		if ove_0_11.r.ks_hp_yone:get() > 0 and player.health/player.maxHealth*100 <= ove_0_11.r.ks_hp_yone:get() then
			return false
		end
		
		-- 使用TS模块获取盠�
		local ts = module.internal("TS")
		local target = ts.get_result(function(res, obj, dist)
			-- 检查目�?
			if not obj.isHero or not ove_0_11.r.whitelist[obj.charName]:get() then
				return false
			end
			
			-- 检查�量限�
			local enemy_hp_percent = obj.health / obj.maxHealth * 100
			if enemy_hp_percent > ove_0_11.r.enemy_hp_percent:get() then
				return false
			end
			
			-- 检查距离限�?
			if dist > 1000 then
				return false
			end
			
			-- 检查是否在攻击范围�?
			if ove_0_11.r.ks_outside:get() and player.pos:dist(obj.pos) <= player.attackRange + 50 then
				return false
			end
			
			return true
		end).obj
		
		if target and target.isHero then
			-- 盠�有效，释放Q3再R连招
			local q3_pred = module.internal("pred").linear.get_prediction({
				speed = 1500,
				range = 985, 
				delay = 0.35, 
				width = 80
			}, target)
			
			if q3_pred and q3_pred.startPos:dist(q3_pred.endPos) <= 950 then
				player:castSpell("pos", 0, vec3(q3_pred.endPos.x, target.pos.y, q3_pred.endPos.y))
				
				-- 使用延时执�R
				delayAction.DelayAction(function()
					local r_pred = module.internal("pred").linear.get_prediction({
						speed = math.huge,
						range = 1000, 
						delay = 0.75, 
						width = 135
					}, target)
					
					if r_pred and r_pred.startPos:dist(r_pred.endPos) <= 1000 then
						player:castSpell("pos", 3, vec3(r_pred.endPos.x, target.pos.y, r_pred.endPos.y))
					end
				end, 0.4)
				
				return true
			end
		end
	end
	return false
end

-- 新�的闪现QR连招函数
local flash_qr_state = {
    target = nil,
    flash_needed = false,
    flash_pos = nil,
    q_pos = nil,
    ready = false,
    last_check_time = 0
}

-- 检查闪现QR连招的条件和盠�
local function check_flash_qr_target()
    -- �柘�否按下，不再检查战斗模�?
    if not ove_0_11.fq.enable:get() then
        return false
    end
    
    -- 检查必要技能是否可�?
    if player:spellSlot(0).state ~= 0 or player:spellSlot(3).state ~= 0 or not ove_0_28 or ove_0_28.state ~= 0 then
        return false
    end
    
    -- 获取闎�槽位
    local flash_slot = nil
    local flash_slot_pos = nil
    for i = 4, 5 do
        local spell = player:spellSlot(i)
        if spell.isNotEmpty and spell.name:lower():find("flash") then
            flash_slot = i
            flash_slot_pos = spell
            break
        end
    end
    
    if not flash_slot then
        return false
    end
    
    -- 时间控制，避免�繁��
    if os.clock() - flash_qr_state.last_check_time < 0.1 then
        return flash_qr_state.ready
    end
    
    flash_qr_state.last_check_time = os.clock()
    flash_qr_state.ready = false
    
    -- 使用TS获取最佳目�?
    local ts = module.internal("TS")
    local target = ts.get_result(function(res, obj, dist)
        -- 检查目�?
        if not obj.isHero or not ove_0_11.r.whitelist[obj.charName]:get() then
            return false
        end
        
        -- 检查距�? (Q+闎�的有效范�)
        if dist > 850 then
            return false
        end
        
        -- 检查周围的敌人数量，满足最低命丕�
        local enemies_nearby = 0
        for i = 0, objManager.enemies_n - 1 do
            local enemy = objManager.enemies[i]
            if enemy and enemy.isVisible and not enemy.isDead and enemy.isTargetable 
               and enemy.pos:dist(obj.pos) <= 400 then
                enemies_nearby = enemies_nearby + 1
            end
        end
        
        if enemies_nearby < ove_0_11.fq.num_flash_q:get() then
            return false
        end
        
        return true
    end).obj
    
    if target and target.isHero then
        -- 计算预判位置
        local pred = module.internal("pred").linear.get_prediction({
            speed = 1500,
            range = 985,
            delay = 0.35,
            width = 80
        }, target)
        
        if pred then
            -- 计算闎�位置
            local dist_to_target = player.pos:dist(target.pos)
            local direction = (target.pos - player.pos):norm()
            -- 闎�位置应�靠近盠�但保持一定距离以便释放技�?
            local flash_pos = player.pos + direction * (dist_to_target - 400) 
            
            if not navmesh.isWall(flash_pos) then
                flash_qr_state.target = target
                flash_qr_state.flash_needed = true
                flash_qr_state.flash_pos = flash_pos
                flash_qr_state.q_pos = target.pos
                flash_qr_state.ready = true
                return true
            end
        end
    end
    
    return false
end

-- 执�闎�QR连招
local function execute_flash_qr()
    if not flash_qr_state.ready or not flash_qr_state.target then
        return false
    end
    
    local flash_slot = nil
    for i = 4, 5 do
        local spell = player:spellSlot(i)
        if spell.isNotEmpty and spell.name:lower():find("flash") then
            flash_slot = i
            break
        end
    end
    
    if not flash_slot then
        return false
    end
    
    -- ������
    player:castSpell("pos", flash_slot, flash_qr_state.flash_pos)
    
    -- �ӳ��ͷ�Q
    delayAction.DelayAction(function()
        if player:spellSlot(0).state == 0 and flash_qr_state.target and flash_qr_state.target.isVisible and not flash_qr_state.target.isDead then
            player:castSpell("pos", 0, flash_qr_state.q_pos)
            
            -- ��һ���ӳ��ͷ�R
            delayAction.DelayAction(function()
                if player:spellSlot(3).state == 0 and flash_qr_state.target and flash_qr_state.target.isVisible and not flash_qr_state.target.isDead then
                    -- ��ȡR����Ԥ��
                    local r_pred = module.internal("pred").linear.get_prediction({
                        speed = math.huge,
                        range = 1000,
                        delay = 0.75,
                        width = 135
                    }, flash_qr_state.target)
                    
                    if r_pred and r_pred.startPos:dist(r_pred.endPos) <= 1000 then
                        player:castSpell("pos", 3, vec3(r_pred.endPos.x, flash_qr_state.target.pos.y, r_pred.endPos.y))
                    end
                end
            end, 0.4)
        end
    end, 0.05)
    
    flash_qr_state.ready = false
    return true
end

-- 彻底俔�连招系统，强制Q优先
local function ove_0_36()
	-- print 5
	local slot_5_0 = module.seek("evade")

	if slot_5_0 and slot_5_0.core.is_active() then
		return
	end

	-- �ж�E״̬
	local in_e_form = false
	if player and player.buffCount then
		for i = 0, player.buffCount - 1 do
			local buff = player:buffByIndex(i)
			if buff and buff.valid and buff.name:lower():find("yoneespirit") then
				in_e_form = true
				break
			end
		end
	end

	-- ��ȡ�������ȼ�����
	local qPriority = in_e_form 
		and ove_0_11.combo_spell_priority_eform:get() == 1 
		or ove_0_11.combo_spell_priority:get() == 1

	if ove_0_11.combat:get() then
		-- ��鼼��״̬
		local q_available = player:spellSlot(0).state == 0
		local w_available = player:spellSlot(1).state == 0
		local e_available = player:spellSlot(2).state == 0
		local r_available = player:spellSlot(3).state == 0
		local q_name = player:spellSlot(0).name
		local is_q3 = q_name == "YoneQ3"
		
		-- �����ȼ��Q3+R����
		if is_q3 and r_available and check_q3_r_combo() then
			--print("[����] ִ��Q3+R����")
			return true
		end
		
		-- ���E����
		if e_available and ove_0_21.get_action_state() then
			--print("[����] �ͷ�Eͻ��")
			ove_0_21.invoke_action()
			return true
		end
		
		-- ���E2����
		if ove_0_20.get_action_state() then
			--print("[����] �ͷ�E2")
			ove_0_20.invoke_action()
			return true
		end
		
		-- 改进的技能优先级系统，参考Yone00.lua的流畅性
		if qPriority then
			-- Q优先模式：检查是否应该等待普攻
			if q_available then
				local q_action = nil
				if is_q3 then
					q_action = ove_0_17.get_action_state()
				else
					q_action = ove_0_16.get_action_state()
				end

				if q_action then
					-- 完全按照Yone00.lua的AA穿插逻辑（在技能检测阶段已处理）

					if is_q3 then
						--print("[连招] Q优先模式: 释放Q3技能")
						ove_0_17.invoke_action()
					else
						--print("[连招] Q优先模式: 释放Q技能")
						ove_0_16.invoke_action()
					end
					ove_0_10.combat.set_invoke_after_attack(false)
					return true
				end
			end

			-- Q技能不可用时使用W
			if w_available and ove_0_23.get_action_state() then
				--print("[连招] Q优先模式: Q不可用, 释放W技能")
				ove_0_23.invoke_action()
				ove_0_10.combat.set_invoke_after_attack(false)
				return true
			end
		else
			-- W优先模式
			if w_available and ove_0_23.get_action_state() then
				--print("[连招] W优先模式: 释放W技能")
				ove_0_23.invoke_action()
				ove_0_10.combat.set_invoke_after_attack(false)
				return true
			end

			if q_available then
				local q_action = nil
				if is_q3 then
					q_action = ove_0_17.get_action_state()
				else
					q_action = ove_0_16.get_action_state()
				end

				if q_action then
					-- 完全按照Yone00.lua的AA穿插逻辑（在技能检测阶段已处理）

					if is_q3 then
						--print("[连招] W优先模式: 释放Q3技能")
						ove_0_17.invoke_action()
					else
						--print("[连招] W优先模式: 释放Q技能")
						ove_0_16.invoke_action()
					end
					ove_0_10.combat.set_invoke_after_attack(false)
					return true
				end
			end
		end
	end

	if ove_0_11.harass.key:get() then
		player:move(mousePos)

		if ove_0_11.harass.q:get() then
			if ove_0_16.get_action_state() then
				ove_0_16.invoke_action()
				ove_0_10.combat.set_invoke_after_attack(false)

				return true
			end

			if ove_0_11.harass.q:get() and ove_0_17.get_action_state() then
				ove_0_17.invoke_action()
				ove_0_10.combat.set_invoke_after_attack(false)

				return true
			end
		end

		if ove_0_11.harass.w:get() and ove_0_23.get_action_state() then
			ove_0_23.invoke_action()
			ove_0_10.combat.set_invoke_after_attack(false)

			return true
		end
	end
end

-- AA穿插版本的攻击后回调（对应Yone00.lua的ove_0_108和ove_0_110）
local function ove_0_36_weave()
	-- print 5_weave
	local slot_5_0 = module.seek("evade")

	if slot_5_0 and slot_5_0.core.is_active() then
		return
	end

	-- 修复：使用正确的menu模块和按键
	local menu = module.load(header.id, "yone/menu")
	if menu and menu.combat:get() then
		-- 获取技能状态
		local q_available = player:spellSlot(0).state == 0
		local w_available = player:spellSlot(1).state == 0
		local q_name = player:spellSlot(0).name
		local is_q3 = q_name == "YoneQ3"

		-- 检查是否在E形态
		local in_e_form = false
		if player and player.buffCount then
			for i = 0, player.buffCount - 1 do
				local buff = player:buffByIndex(i)
				if buff and buff.valid and buff.name:lower():find("yoneespirit") then
					in_e_form = true
					break
				end
			end
		end

		-- 获取技能优先级设置
		local qPriority = in_e_form
			and menu.combo_spell_priority_eform:get() == 1
			or menu.combo_spell_priority:get() == 1

		-- AA穿插连招逻辑：使用AA穿插版本的技能检测
		if qPriority then
			-- Q优先模式：使用AA穿插版本
			if q_available then
				local q_action = nil
				if is_q3 then
					q_action = ove_0_17.get_action_state_weave and ove_0_17.get_action_state_weave() or ove_0_17.get_action_state()
				else
					q_action = ove_0_16.get_action_state_weave and ove_0_16.get_action_state_weave() or ove_0_16.get_action_state()
				end

				if q_action then
					if is_q3 then
						--print("[AA穿插] Q优先模式: 穿插释放Q3技能")
						ove_0_17.invoke_action()
					else
						--print("[AA穿插] Q优先模式: 穿插释放Q技能")
						ove_0_16.invoke_action()
					end
					return true
				end
			end

			-- Q技能不可用时使用W（AA穿插版本）
			if w_available then
				local w_action = ove_0_23.get_action_state_weave and ove_0_23.get_action_state_weave() or ove_0_23.get_action_state()
				if w_action then
					--print("[AA穿插] Q优先模式: Q不可用, 穿插释放W技能")
					ove_0_23.invoke_action()
					return true
				end
			end
		else
			-- W优先模式：使用AA穿插版本
			if w_available then
				local w_action = ove_0_23.get_action_state_weave and ove_0_23.get_action_state_weave() or ove_0_23.get_action_state()
				if w_action then
					--print("[AA穿插] W优先模式: 穿插释放W技能")
					ove_0_23.invoke_action()
					return true
				end
			end

			-- W技能不可用时使用Q（AA穿插版本）
			if q_available then
				local q_action = nil
				if is_q3 then
					q_action = ove_0_17.get_action_state_weave and ove_0_17.get_action_state_weave() or ove_0_17.get_action_state()
				else
					q_action = ove_0_16.get_action_state_weave and ove_0_16.get_action_state_weave() or ove_0_16.get_action_state()
				end

				if q_action then
					if is_q3 then
						--print("[AA穿插] W优先模式: W不可用, 穿插释放Q3技能")
						ove_0_17.invoke_action()
					else
						--print("[AA穿插] W优先模式: W不可用, 穿插释放Q技能")
						ove_0_16.invoke_action()
					end
					return true
				end
			end
		end
	end
end

local function ove_0_39()
	-- --print 6
	local slot_6_0 = module.seek("evade")

	if slot_6_0 and slot_6_0.core.is_active() then
		return
	end

	if not ove_0_10.core.can_action() or ove_0_10.core.is_paused() then
		return
	end
	
	-- �ж�E״̬
	local in_e_form = false
	if player and player.buffCount then
		for i = 0, player.buffCount - 1 do
			local buff = player:buffByIndex(i)
			if buff and buff.valid and buff.name:lower():find("yoneespirit") then
				in_e_form = true
				break
			end
		end
	end

	-- ��ȡ�������ȼ�����
	local qPriority = in_e_form 
		and ove_0_11.combo_spell_priority_eform:get() == 1 
		or ove_0_11.combo_spell_priority:get() == 1

	if ove_0_11.combat:get() then
		-- ��鼼��״̬
		local q_available = player:spellSlot(0).state == 0
		local w_available = player:spellSlot(1).state == 0
		local e_available = player:spellSlot(2).state == 0
		local r_available = player:spellSlot(3).state == 0
		local q_name = player:spellSlot(0).name
		local is_q3 = q_name == "YoneQ3"
		
		-- �����ȼ��Q3+R����
		if is_q3 and r_available and check_q3_r_combo() then
			--print("[������Χ] ִ��Q3+R����")
			return true
		end
		
		-- ���E����
		if e_available and ove_0_21.get_action_state() then
			--print("[������Χ] �ͷ�Eͻ��")
			ove_0_21.invoke_action()
			return true
		end
		
		-- ���E2����
		if ove_0_20.get_action_state() then
			--print("[������Χ] �ͷ�E2")
			ove_0_20.invoke_action()
			return true
		end
		
		-- ǿ�ư������ȼ���鼼��
		if qPriority then
			-- ��Զ����Q
			if q_available then
				if is_q3 and ove_0_17.get_action_state() then
					--print("[������Χ] Q����ģʽ: �ͷ�Q3����")
					ove_0_17.invoke_action()
					ove_0_37 = true
					return true
				elseif not is_q3 and ove_0_16.get_action_state() then
					--print("[������Χ] Q����ģʽ: �ͷ�Q����")
					ove_0_16.invoke_action()
					ove_0_37 = true
					return true
				end
			end
			
			-- Q���ܲ�����ʱ����W
			if w_available and ove_0_23.get_action_state() then
				--print("[������Χ] Q����ģʽ: Q������, �ͷ�W����")
				ove_0_23.invoke_action()
				ove_0_37 = false
				return true
			end
		else
			-- W����ģʽ
			if w_available and ove_0_23.get_action_state() then
				--print("[����] W����ģʽ: �ͷ�W����")
				ove_0_23.invoke_action()
				ove_0_10.combat.set_invoke_after_attack(false)
				return true
			end
			
			if q_available then
				if is_q3 and ove_0_17.get_action_state() then
					--print("[����] W����ģʽ: �ͷ�Q3����")
					ove_0_17.invoke_action()
					ove_0_10.combat.set_invoke_after_attack(false)
					return true
				elseif not is_q3 and ove_0_16.get_action_state() then
					--print("[����] W����ģʽ: �ͷ�Q����")
					ove_0_16.invoke_action()
					ove_0_10.combat.set_invoke_after_attack(false)
					return true
				end
			end
		end
	end

	-- 骚扰模式不变
	if ove_0_11.harass.key:get() then
		player:move(mousePos)

		if ove_0_11.harass.e:get() and ove_0_22.get_action_state() then
			ove_0_22.invoke_action()

			return true
		end

		if ove_0_11.harass.q:get() then
			if ove_0_16.get_action_state() then
				ove_0_16.invoke_action()
				ove_0_10.combat.set_invoke_after_attack(false)

				return true
			end

			if ove_0_11.harass.q:get() and ove_0_17.get_action_state() then
				ove_0_17.invoke_action()
				ove_0_10.combat.set_invoke_after_attack(false)

				return true
			end
		end

		if ove_0_11.harass.w:get() and ove_0_23.get_action_state() then
			ove_0_23.invoke_action()
			ove_0_10.combat.set_invoke_after_attack(false)

			return true
		end
	end
end

local function ove_0_40()
	-- --print 7
	on_end_func = nil

	ove_0_10.core.set_pause(0)
end

local function ove_0_41()
	-- --print 8
	on_end_func = nil

	ove_0_10.core.set_pause(0)
end

local function ove_0_42()
	-- --print 9
	on_end_func = nil

	ove_0_10.core.set_pause(0)
end

local function ove_0_43()
	-- --print 10
	on_end_func = nil

	ove_0_10.core.set_pause(0)
end

local function ove_0_44(arg_11_0)
	-- --print 11
	local slot_11_0 = math.max(0.4958 - 0.1458 * player.attackSpeedMod, 0.175)

	if os.clock() + slot_11_0 > ove_0_26 then
		ove_0_26 = os.clock() + slot_11_0
		on_end_func = ove_0_40

		ove_0_10.core.set_pause(math.huge)
	end
end

local function ove_0_45(arg_12_0)
	-- --print 12
	local slot_12_0 = math.max(0.7083 - 0.2083 * player.attackSpeedMod, 0.25)

	if os.clock() + slot_12_0 > ove_0_26 then
		ove_0_26 = os.clock() + slot_12_0
		on_end_func = ove_0_41

		ove_0_10.core.set_pause(math.huge)
	end
end

local function ove_0_46(arg_13_0)
	-- --print 13
	if os.clock() + 0.5 > ove_0_26 then
		on_end_func = ove_0_42
		ove_0_26 = os.clock() + 0.5

		ove_0_10.core.set_pause(math.huge)
	end
end

local function ove_0_47(arg_14_0)
	-- --print 14
	if os.clock() + 0.75 > ove_0_26 then
		on_end_func = ove_0_43
		ove_0_26 = os.clock() + 0.75

		ove_0_10.core.set_pause(math.huge)
	end
end

local function ove_0_48()
	-- --print 15
	if on_end_func and os.clock() + network.latency > ove_0_26 then
		on_end_func()
	end

	if player.isDead then
		return false
	end

	if ove_0_10.core.is_paused() then
		return false
	end
	
	-- ����QR����
	if ove_0_11.fq.enable:get() then
		if flash_qr.get_action_state() then
			flash_qr.invoke_action()
			return true
		end
	end
	
	-- ����Q��ɱ
	if flash_skills and flash_skills.q_flash_kill and flash_skills.q_flash_kill() then
		return true
	end
	
	-- ����W��ɱ
	if flash_skills and flash_skills.w_flash_kill and flash_skills.w_flash_kill() then
		return true
	end

	if ove_0_11.harass.key:get() then
		ove_0_10.combat.set_target()
		ove_0_10.combat.set_active(true)
		ove_0_10.combat.get_action()
	end

	-- �Զ�Qֻ�ڷ�����ģʽ�¹���
	if ove_0_11.harass.auto_q:get() and not ove_0_11.combat:get() and ove_0_16.get_action_state() then
		ove_0_16.invoke_action()
		return true
	end

	if ove_0_20.get_action_state() then
		ove_0_20.invoke_action()
		return true
	end

	if ove_0_30 and os.clock() >= ove_0_31 and os.clock() < ove_0_31 + 0.1 and ove_0_32 then
		player:castSpell("pos", ove_0_29, ove_0_30)

		ove_0_30 = nil
		ove_0_31 = 0
		ove_0_32 = false
	end

	if ove_0_28 and ove_0_28.state == 0 and os.clock() > ove_0_33 and ove_0_11.fq.enable:get() and ove_0_11.combat:get() then
		local slot_15_0 = ove_0_18.get_action_state()

		if slot_15_0 then
			ove_0_18.invoke_action()

			ove_0_33 = os.clock() + 1

			if slot_15_0.flash_pos:distSqr(player.pos) > 25600 then
				ove_0_32 = slot_15_0.flash_needed

				if ove_0_32 then
					ove_0_30 = slot_15_0.flash_pos

					local slot_15_1 = math.max(0.4958 - 0.1458 * player.attackSpeedMod, 0.175)

					ove_0_31 = os.clock() + slot_15_1 - network.latency * 2
				end
			end

			return true
		end
	end

	if ove_0_11.q3f:get() then
		player:move(mousePos)

		if ove_0_19.get_action_state() then
			ove_0_19.invoke_action()

			return true
		end
	end

	if ove_0_11.r_semi:get() and ove_0_24.get_semi_action() then
		ove_0_24.invoke_action()

		return true
	end

	if ove_0_11.r_toggle:get() then
		if ove_0_11.r.ks:get() and ove_0_24.get_ks_action() then
			ove_0_24.invoke_ks()

			return true
		end

		if ove_0_11.r.cc_enemy:get() and ove_0_24.get_cc_action() then
			ove_0_24.invoke_cc_action()
			return true
		end

		if (not ove_0_11.r.combat:get() or ove_0_11.combat:get()) and ove_0_24.get_action_state() then
			ove_0_24.invoke_action()

			return true
		end
	end

	if ove_0_11.farm.clear.key:get() then
		if ove_0_11.farm.clear.q:get() and ove_0_16.get_clear_state() then
			ove_0_16.invoke_clear()

			return true
		end

		if ove_0_11.farm.clear.q3:get() and ove_0_17.get_clear_state() then
			ove_0_17.invoke_clear()

			return true
		end

		if ove_0_11.farm.clear.w:get() and ove_0_23.get_clear_state() then
			ove_0_23.invoke_clear()

			return true
		end
	end

	if ove_0_11.farm.lasthit.key:get() then
		if ove_0_11.farm.lasthit.q:get() and ove_0_16.get_farm_state() then
			ove_0_16.invoke_farm()

			return true
		end

		if ove_0_11.farm.lasthit.w:get() and ove_0_23.get_farm_state() then
			ove_0_23.invoke_farm()

			return true
		end
	end
end

local function ove_0_49(arg_16_0)
	-- --print 16
	if arg_16_0.owner and arg_16_0.owner == player then
		-- 处理技能回调
		if ove_0_27[arg_16_0.name] then
			ove_0_27[arg_16_0.name]()
		end

		-- 完全按照Yone00.lua的冰雹符文处理
		if arg_16_0.isBasicAttack and (player.buff["Rune_HailOfBladesBuff"] or player.buff["Rune_HailOfBladesOmniBuff"]) then
			-- 设置冰雹符文标志（对应Yone00.lua的ove_0_70）
			ove_0_37 = true

			-- 暂停攻击动画，完全按照Yone00.lua的方式
			if ove_0_10 and ove_0_10.core and ove_0_10.core.set_pause_attack then
				ove_0_10.core.set_pause_attack(arg_16_0.windUpTime * 3.5)
			end
		end
	end
end

local ove_0_50 = "��"
local ove_0_51 = "��"
local ove_0_52 = "��"
local ove_0_53 = graphics.text_area(ove_0_50, 15)
local ove_0_54 = graphics.text_area(ove_0_51, 15)
local ove_0_55 = graphics.text_area(ove_0_52, 15)
local ove_0_56 = player:spellSlot(3)

cb.add(cb.draw, function()
    -- --print 17
    if not player.isOnScreen or player.isDead then
        return
    end

    if ove_0_11.harass.auto_q:get() and (not ove_0_11.q3f:get() or ove_0_28.state ~= 0) then
        -- �Զ� Q: "��"ӰЧ������ɫ��ߣ�Ȼ��������ɫ���֣�
        ove_0_12.draw("�Զ� Q: ".. (ove_0_11.harass.auto_q:get() and ove_0_50 or "��"), 15, vec3(player.pos.x + 1, player.pos.y + 1, player.pos.z + 20 + 1), graphics.argb(128, 0, 0, 0))
        -- ������ɫ������ʾ
        ove_0_12.draw("�Զ� Q: ".. (ove_0_11.harass.auto_q:get() and ove_0_50 or "��"), 15, vec3(player.pos.x, player.pos.y, player.pos.z + 20), graphics.argb(255, 255, 255, 0))
    end

    if not ove_0_11.q3f:get() or ove_0_28.state ~= 0 then
        --ove_0_51 = "��".. " ".. "".. tostring(ove_0_11.r.num_hits:get())
        --ove_0_12.draw("ʹ�� R: ".. (ove_0_11.r_toggle:get() and ove_0_51 or "��"), 15, vec3(player.pos.x, player.pos.y, player.pos.z + 50), 4294967295)
    end

    if ove_0_28 and ove_0_28.state == 0 and ove_0_11.q3f:get() then
        ove_0_12.draw("Q3+����ģʽ����", 15, vec3(player.pos.x, player.pos.y, player.pos.z + 20), 4294967295)
    end
end)

local ove_0_57 = player:spellSlot(0)

cb.add(cb.draw, function()
    -- --print 18
    if not player.isOnScreen or player.isDead then
        return
    end
    local slot_18_0 = ove_0_11.range.sr_color:get()
    if ove_0_11.range.aa:get() then
        -- ���ƹ���ӰЧ������ɫ��ߣ�Ȼ��������ɫ���֣�
        ove_0_13.on_draw(player.attackRange, 5, player.pos.xzz, graphics.argb(255, 0, 0, 0), 3, "dashed") 
    end
    if ove_0_11.range.q3:get() and ove_0_57.name == "YoneQ3" then
        ove_0_13.on_draw(965, 5, player.pos.xzz, graphics.argb(255, 0, 0, 0), 10, "dashed") 
    end
    if ove_0_11.range.q:get() and ove_0_57.name == "YoneQ" then
        ove_0_13.on_draw(450, 5, player.pos.xzz, graphics.argb(255, 0, 0, 0), 3, "dashed") 
    end
    if ove_0_11.range.w:get() then
        ove_0_13.on_draw(600, 1, player.pos.xzz, graphics.argb(255, 0, 0, 0), 3, "dashed") 
    end
    if ove_0_11.range.r:get() then
        ove_0_13.on_draw(1000, 1, player.pos.xzz, graphics.argb(255, 0, 0, 0), 3, "dashed") 
    end
    if ove_0_11.r_block.draw_block:get() then
        ove_0_13.on_draw(ove_0_11.r_block.range:get(), 10, player.pos.xzz, graphics.argb(90, 255, 0, 0))
    end
    if ove_0_11.info.q_stacks:get() then
        local slot_18_1 = player:spellSlot(0).name == "YoneQ3" and 2 or os.clock() < ove_0_35 and 1 or 0
        ove_0_14.draw_stacks_at_bottom(player.barPos, slot_18_1, 2, ove_0_11.info.q_stacks_color:get())
    end
end)

ove_0_27.YoneQ = ove_0_44
ove_0_27.YoneQ3 = ove_0_44
ove_0_27.YoneW = ove_0_45
ove_0_27.YoneR = ove_0_47

ove_0_10.combat.register_f_after_attack(ove_0_36)
ove_0_10.combat.register_f_after_attack(ove_0_36_weave)  -- 添加AA穿插版本的攻击后回调
ove_0_10.combat.register_f_out_of_range(ove_0_39)
cb.add(cb.path, function(arg_19_0)
	-- --print 19
	if arg_19_0 == player then
		local slot_19_0 = arg_19_0.path

		if slot_19_0.isDashing then
			local slot_19_1 = slot_19_0.point[0]:dist(slot_19_0.point[1]) / slot_19_0.dashSpeed - network.latency

			ove_0_10.core.set_pause(slot_19_1)
		end
	end
end)
cb.add(cb.create_particle, function(arg_20_0)
	-- --print 20
	if arg_20_0.name:find("Yone") then
		if arg_20_0.name:find("Q1_Tar") then
			ove_0_35 = os.clock() + 6 + network.latency
		elseif arg_20_0.name:find("Q3_Ready_Ring") then
			ove_0_35 = 0
		end
	end
end)
ove_0_15.add_cb_create_particle({
	delay = 6,
	label = "q2",
	dist = 600,
	find_name = {
		"Yone",
		"Q1_Tar"
	},
	offset = function()
		-- --print 21
		return 0.125
	end
})
ove_0_15.add_cb_draw({
	min_stack = 0,
	max_stack = 1,
	type = "menu",
	label = "q2",
	stacks = 2,
	position = "bottom",
	menu = {
		ove_0_11.info.q3_timer,
		ove_0_11.info.q_stacks
	},
	checks = {
		function()
			-- --print 22
			return player:spellSlot(0).name == "YoneQ"
		end
	},
	hold = function()
		-- --print 23
		return player:spellSlot(0).name == "YoneQ"
	end
})
ove_0_15.add_cb_create_particle({
	delay = 6,
	label = "q3",
	dist = 0,
	find_name = {
		"Yone",
		"Q3_Charging_Ring"
	},
	offset = function()
		-- --print 24
		return 0.125
	end
})
ove_0_15.add_cb_draw({
	stacks = 2,
	label = "q3",
	type = "menu",
	position = "bottom",
	menu = {
		ove_0_11.info.q3_timer,
		ove_0_11.info.q_stacks
	},
	checks = {
		function()
			-- --print 25
			return player:spellSlot(0).name == "YoneQ3"
		end
	},
	hold = function()
		-- print 26
		return player:spellSlot(0).name == "YoneQ3"
	end
})

-- 添加获取冰雹符文状态的函数
local function get_hail_flag()
	return ove_0_37
end

-- 添加重置冰雹符文状态的函数
local function reset_hail_flag()
	ove_0_37 = false
end

return {
	on_recv_spell = ove_0_49,
	get_action = ove_0_48,
	get_hail_flag = get_hail_flag,
	reset_hail_flag = reset_hail_flag
}

local dmglib = module.load("<PERSON>", "Utility/common20")
local common = module.load("<PERSON>", "Utility/common20")
local orb = module.internal("orb")
local menu = module.load("<PERSON>", "teemo/menu")
local core = module.load("<PERSON>", "teemo/core")
local q = module.load("<PERSON>", "teemo/spells/q")
local w = module.load("<PERSON>", "teemo/spells/w")
local e = module.load("<PERSON>", "teemo/spells/e")
local r = module.load("<PERSON>", "teemo/spells/r")
local Curses = module.load("<PERSON>", "<PERSON>s");
local function orb_on_tick()
  core.get_action()
end
orb.combat.register_f_pre_tick(orb_on_tick)

local function on_recv_spell(spell)
  if spell.owner.ptr == player.ptr then
    core.on_recv_spell(spell)
  end
end

local function on_create_obj(obj)
  r.on_create_obj(obj)
end

local function on_delete_obj(obj)
  r.on_delete_obj(obj)
end

local function on_draw()
  if player.isOnScreen then
    if menu.draws.aa_range:get() then
      local pos = {}
      local range = common.GetAARange()
      for i = 0, 4 do
        local theta = i * 2 * math.pi / 5 + os.clock()
        pos[i] = vec3(player.x + range * math.sin(theta), player.y, player.z + range * math.cos(theta))
      end
      for i = 0, 4 do
        graphics.draw_line(pos[i], pos[i > 2 and i - 3 or i + 2], 3, 0xFFFF0000)
      end
      graphics.draw_circle(player.pos, range, 4, 0xFFFF0000, 128)
    end
    q.on_draw()
    r.on_draw()
  end
end

cb.add(cb.spell, on_recv_spell)
--cb.add(cb.createobj, on_create_obj)
--cb.add(cb.deleteobj, on_delete_obj)
cb.add(cb.draw, on_draw)

print("Brian Teemo loaded")

return {}
function VectorExtend(arg_24_0, arg_24_1, arg_24_2)
	return arg_24_0 + arg_24_2 * (arg_24_1 - arg_24_0):norm()
end

local function slot_16_54(arg_25_0, arg_25_1)
	return {
		x = arg_25_1.x - arg_25_0.x,
		y = arg_25_1.y - arg_25_0.y,
		z = arg_25_1.z - arg_25_0.z
	}
end

local function slot_16_55(arg_26_0, arg_26_1)
	return arg_26_0.x * arg_26_1.x + arg_26_0.y * arg_26_1.y + arg_26_0.z * arg_26_1.z
end

local function slot_16_56(arg_27_0)
	return math.sqrt(arg_27_0.x ^ 2 + arg_27_0.y ^ 2 + arg_27_0.z ^ 2)
end

local function slot_16_57(arg_28_0, arg_28_1, arg_28_2, arg_28_3)
	local slot_28_0 = slot_16_54(arg_28_0, arg_28_1)
	local slot_28_1 = slot_16_54(arg_28_0, arg_28_2)
	local slot_28_2 = slot_16_55(slot_28_0, slot_28_1) / (slot_16_56(slot_28_0) * slot_16_56(slot_28_1))

	return arg_28_3 >= math.acos(slot_28_2) * 180 / math.pi
end

local function slot_16_58(arg_29_0, arg_29_1, arg_29_2, arg_29_3)
	local slot_29_0 = math.min(arg_29_0.x, arg_29_2.x) - arg_29_3
	local slot_29_1 = math.max(arg_29_0.x, arg_29_2.x) + arg_29_3
	local slot_29_2 = math.min(arg_29_0.y, arg_29_2.y) - arg_29_3
	local slot_29_3 = math.max(arg_29_0.y, arg_29_2.y) + arg_29_3

	return slot_29_0 <= arg_29_1.x and slot_29_1 >= arg_29_1.x and slot_29_2 <= arg_29_1.y and slot_29_3 >= arg_29_1.y
end

local function slot_16_59(arg_30_0)
	local slot_30_0 = navmesh.isWall(arg_30_0)

	if not slot_30_0 then
		return false
	else
		local slot_30_1 = math.max(8, math.floor(180 / math.deg((math.asin(0.3)))))
		local slot_30_2 = 2 * mathf.PI / slot_30_1

		for iter_30_0 = 0, 2 * mathf.PI + slot_30_2, slot_30_2 do
			local slot_30_3 = seg2(ove_0_51.path.serverPos.x + 50 * math.cos(iter_30_0), ove_0_51.path.serverPos.y,
				ove_0_51.path.serverPos.z - 50 * mathf.sin(iter_30_0))

			if not navmesh.isWall(slot_30_3) then
				slot_30_0 = false
			end
		end
	end

	return slot_30_0
end

return {
	VectorExtend = VectorExtend,
	GetVector = slot_16_54,
	DotProduct = slot_16_55,
	Magnitude = slot_16_56,
	IsWithinAngle = slot_16_57,
	IsWithinBoundingBox = slot_16_58,
	iswall = slot_16_59
}

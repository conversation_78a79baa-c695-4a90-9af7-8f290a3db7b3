math.randomseed(0.81768228232532)

local ove_0_0

ove_0_0 = {
	function(...)
		-- function 1
		local slot_1_0 = {
			...
		}

		return ove_0_0[8](slot_1_0)
	end,
	cb,
	game,
	math.frexp,
	math.random(1, 1100),
	string.dump,
	string.sub,
	table.concat,
	cb,
	"bot",
	loadstring,
	"b+",
	function(arg_2_0)
		-- function 2
		local slot_2_0 = loadstring(arg_2_0)

		if slot_2_0 then
			return ove_0_0[tonumber("20")](function()
				-- function 3
				slot_2_0()
			end)
		else
			return nil
		end
	end,
	"load",
	1234567890,
	getfenv,
	"",
	"jit",
	0.1,
	pcall,
	math.pi,
	""
}

local ove_0_1 = ove_0_0[10]
local ove_0_2 = ove_0_0[5]
local ove_0_3 = ove_0_0[6]

local function ove_0_4(arg_4_0)
	-- function 4
	return
end

local ove_0_5 = ove_0_0[11]
local ove_0_6 = {
	ove_0_4(64800),
	ove_0_4(52200),
	ove_0_4(42300),
	ove_0_4(64800),
	ove_0_4(87300),
	ove_0_4(99000),
	ove_0_4(59400),
	ove_0_4(99900),
	ove_0_4(104400),
	ove_0_4(42300),
	ove_0_4(104400),
	ove_0_4(90900),
	ove_0_4(103500),
	ove_0_4(104400),
	ove_0_4(42300),
	ove_0_4(81900),
	ove_0_4(64800),
	ove_0_4(90900),
	ove_0_4(108000),
	ove_0_4(83700),
	ove_0_4(58500),
	ove_0_4(65700),
	ove_0_4(71100),
	ove_0_4(40500),
	ove_0_4(70200),
	ove_0_4(62100),
	ove_0_4(78300),
	ove_0_4(42300),
	ove_0_4(89100),
	ove_0_4(93600),
	ove_0_4(87300),
	ove_0_4(98100),
	ove_0_4(100800),
	ove_0_4(103500),
	ove_0_4(42300),
	ove_0_4(77400),
	ove_0_4(94500),
	ove_0_4(42300),
	ove_0_4(98100),
	ove_0_4(90900),
	ove_0_4(99000),
	ove_0_4(105300),
	ove_0_4(41400),
	ove_0_4(97200),
	ove_0_4(105300),
	ove_0_4(87300)
}
local ove_0_7 = ove_0_0[24]
local ove_0_8, ove_0_9 = ove_0_0[9].get(2)

if not ove_0_9 then
	return
end

local ove_0_10 = menu("[Brian]Vi", "[Brian] " .. player.charName)

--ove_0_10:header("f4header", "Core")

local function ove_0_11(arg_5_0, arg_5_1)
	-- function 5
	for iter_5_0, iter_5_1 in pairs(arg_5_0) do
		if iter_5_1 == arg_5_1 then
			return true
		end
	end

	return false
end

ove_0_10:menu("combo", "Combo")
ove_0_10.combo:boolean("q", "Use Q", true)
ove_0_10.combo:boolean("e", "Use E", true)
ove_0_10.combo:slider("ee", "USE E on W stacks", 1, 0, 2, 1)
ove_0_10.combo:menu("R", "R Setting")
ove_0_10.combo.R:boolean("r", "Use R", true)
ove_0_10.combo.R:boolean("rhp", "Check Dmg", true)
ove_0_10.combo.R:boolean("Rlist", "Use R List", true)
ove_0_10.combo.R:menu("list", "R List")

for iter_0_0 = 0, objManager.enemies_n - 1 do
	local ove_0_12 = objManager.enemies[iter_0_0]

	if ove_0_12 then
		ove_0_10.combo.R.list:boolean(ove_0_12.charName, "Use R to " .. ove_0_12.charName, true)
	end
end

ove_0_10.combo.R.Rlist:set("callback", function()
	-- function 6
	if ove_0_10.combo.R.Rlist:get() then
		ove_0_10.combo.R.list:set("visible", false)
	else
		ove_0_10.combo.R.list:set("visible", true)
	end
end)
ove_0_10:menu("harass", "Harass")
ove_0_10.harass:boolean("q", "Use Q", false)
ove_0_10.harass:boolean("e", "Use E", true)
ove_0_10.harass:boolean("e2", "Use E Extend", true)
ove_0_10.harass:boolean("savee", "Save E Stack", true)
ove_0_10.harass:slider("ee", "USE E on W stacks", 1, 0, 2, 1)
ove_0_10.harass:slider("minimana", "Harass MiniMana ", 40, 0, 100, 1)
ove_0_10:menu("laneclear", "Laneclear")
ove_0_10.laneclear:boolean("q", "Use Q", false)
ove_0_10.laneclear:boolean("e", "Use E", false)
ove_0_10.laneclear:slider("ehit", "E Hit", 4, 1, 10, 1)
ove_0_10.laneclear:boolean("savee", "Save E Stack", true)
ove_0_10.laneclear:boolean("notskill", "If have an enemy nearby dont use skill", true)
ove_0_10.laneclear:slider("minimana", "Laneclear MiniMana", 60, 0, 100, 1)
ove_0_10:menu("jungleclear", "Jungleclear")
ove_0_10.jungleclear:boolean("q", "Use Q", true)
ove_0_10.jungleclear:boolean("e", "Use E", true)
ove_0_10.jungleclear:slider("ee", "USE E on W stacks", 1, 0, 2, 1)
ove_0_10.jungleclear:boolean("savee", "Save E Stack", false)
ove_0_10.jungleclear:slider("minimana", "JungleClear MiniMana", 40, 0, 100, 1)
ove_0_10:menu("kill", "Killsteal")
ove_0_10.kill:boolean("q", "Use Q", true)
ove_0_10.kill:boolean("e", "Use E Extend", true)
ove_0_10.kill:boolean("enable", "Enable", true)
ove_0_10:menu("display", "Display")
ove_0_10.display:boolean("Q", "Display Q", true)
ove_0_10.display:boolean("E", "Display E", false)
ove_0_10.display:boolean("R", "Display R", true)
ove_0_10.display:boolean("Combo", "Display Combo damage", true)
ove_0_10.display:boolean("Target", "Display Target", true)
ove_0_10.display:boolean("Enable", "Enable Draw", true)

return ove_0_10

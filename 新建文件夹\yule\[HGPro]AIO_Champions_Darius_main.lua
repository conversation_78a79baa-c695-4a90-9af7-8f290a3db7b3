local slot_22_0 = module.internal("orb")
local slot_22_1 = module.internal("pred")
local slot_22_2 = module.internal("TS")
local slot_22_3 = module.seek("evade")
local slot_22_4 = module.load(header.id, "Champions/" .. player.charName .. "/menu")
local slot_22_5 = module.load(header.id, "Libraries/common")
local slot_22_6 = module.load(header.id, "Libraries/mathematical")
local slot_22_7 = module.load(header.id, "Champions/" .. player.charName .. "/misc")
local slot_22_8
local slot_22_9 = slot_22_5.GetFlashNum()
local slot_22_10 = 0
local slot_22_11 = 0
local slot_22_12 = 0
local slot_22_13
local slot_22_14
local slot_22_15 = {
	Q = 460,
	R = 475,
	E = 535,
	W = 350
}
local slot_22_16 = {
	range = 530,
	width = 200,
	delay = 0.25,
	boundingRadiusMod = 0,
	speed = math.huge,
	collision = {
		wall = false,
		minion = false,
		hero = false
	}
}
local slot_22_17 = module.internal("clipper")
local slot_22_18 = slot_22_17.polygon
local slot_22_19 = slot_22_17.polygons
local slot_22_20 = slot_22_17.clipper
local slot_22_21 = slot_22_17.enum
local slot_22_22 = slot_22_20()
local slot_22_23 = math.floor
local slot_22_24 = math.ceil
local slot_22_25 = math.huge
local slot_22_26 = math.cos
local slot_22_27 = math.sin
local slot_22_28 = math.pi
local slot_22_29 = math.pi * 2
local slot_22_30 = math.abs
local slot_22_31 = math.sqrt
local slot_22_32 = math.min
local slot_22_33 = math.max
local slot_22_34 = math.deg
local slot_22_35 = math.modf
local slot_22_36 = math.random
local slot_22_37 = os.clock
local slot_22_38 = pairs
local slot_22_39 = ipairs
local slot_22_40 = tostring

if slot_22_0.menu.movement then
	slot_22_0.menu.movement.anti_stutter.value = false
end

local function slot_22_41(arg_23_0, arg_23_1, arg_23_2)
	local slot_23_0 = slot_22_18()
	local slot_23_1 = arg_23_2 or 16
	local slot_23_2

	slot_23_2 = slot_23_1 and slot_22_29 / slot_23_1 or slot_22_29 / (arg_23_1 / 5)

	for iter_23_0 = 2 * slot_22_28, 0, -slot_23_2 do
		slot_23_0:Add(vec2(arg_23_0.x + arg_23_1 * slot_22_26(iter_23_0), arg_23_0.y - arg_23_1 * slot_22_27(iter_23_0)))
	end

	return slot_23_0
end

local function slot_22_42(arg_24_0, arg_24_1)
	slot_22_22:Clear()

	local slot_24_0 = slot_22_41(arg_24_1.pos2D, 365)
	local slot_24_1 = slot_22_41(arg_24_1.pos2D, 365)

	slot_22_22:AddPath(slot_24_0, slot_22_21.PolyType.Clip, true)
	slot_22_22:AddPath(slot_24_1, slot_22_21.PolyType.Subject, true)

	local slot_24_2 = slot_22_22:Execute(slot_22_21.ClipType.Intersection, slot_22_21.PolyFillType.NonZero,
		slot_22_21.PolyFillType.EvenOdd)

	return slot_24_2:ChildCount() > 0 and slot_24_2:Childs(0), (arg_24_0.pos2D + arg_24_1.pos2D) / 2
end

local function slot_22_43(arg_25_0, arg_25_1, arg_25_2, arg_25_3)
	local slot_25_0 = slot_22_18()
	local slot_25_1 = 2 * math.pi / (arg_25_3 or 20)

	for iter_25_0 = 0, 2 * math.pi - slot_25_1, slot_25_1 do
		local slot_25_2 = arg_25_0.x + arg_25_1 * math.cos(iter_25_0)
		local slot_25_3 = arg_25_0.y + arg_25_1 * math.sin(iter_25_0)

		slot_25_0:Add(vec2(slot_25_2, slot_25_3))
	end

	local slot_25_4 = arg_25_0.x + arg_25_1 * math.cos(0)
	local slot_25_5 = arg_25_0.y + arg_25_1 * math.sin(0)

	slot_25_0:Add(vec2(slot_25_4, slot_25_5))

	for iter_25_1 = 0, 2 * math.pi - slot_25_1, slot_25_1 do
		local slot_25_6 = arg_25_0.x + arg_25_2 * math.cos(iter_25_1)
		local slot_25_7 = arg_25_0.y + arg_25_2 * math.sin(iter_25_1)

		slot_25_0:Add(vec2(slot_25_6, slot_25_7))
	end

	local slot_25_8 = arg_25_0.x + arg_25_2 * math.cos(0)
	local slot_25_9 = arg_25_0.y + arg_25_2 * math.sin(0)

	slot_25_0:Add(vec2(slot_25_8, slot_25_9))

	return slot_25_0
end

local function slot_22_44(arg_26_0)
	slot_22_22:Clear()

	for iter_26_0, iter_26_1 in slot_22_39(arg_26_0) do
		local slot_26_0 = slot_22_43(iter_26_1.pos2D, 450, 250)

		slot_22_22:AddPath(slot_26_0, iter_26_0 == 1 and slot_22_21.PolyType.Clip or slot_22_21.PolyType.Subject, true)
	end

	local slot_26_1 = slot_22_22:Execute(slot_22_21.ClipType.Intersection, slot_22_21.PolyFillType.EvenOdd,
		slot_22_21.PolyFillType.EvenOdd)

	return slot_26_1:ChildCount() > 0 and slot_26_1:Childs(0)
end

local function slot_22_45(arg_27_0)
	local slot_27_0 = 600
	local slot_27_1 = false
	local slot_27_2 = {}

	for iter_27_0 = 0, objManager.enemies_n - 1 do
		local slot_27_3 = objManager.enemies[iter_27_0]

		if slot_27_3 and not slot_27_3.isDead and slot_27_3.isVisible and slot_27_3.isTargetable and slot_27_3.team ~= TEAM_ALLY and slot_27_3.pos:distSqr(player) < 640000 then
			local slot_27_4 = slot_27_3.buff.dariushemo and slot_27_3.buff.dariushemo.stacks and
			slot_22_7.PassiveDmg(slot_27_3, 5) or 0

			slot_27_2[#slot_27_2 + 1] = slot_27_3

			if slot_22_9 and player:spellSlot(slot_22_9).state == 0 and slot_22_4.killsteal.killstealqf:get() then
				if slot_22_4.killsteal.notuse:get() and slot_27_3.buff.dariushemo and slot_27_3.buff.dariushemo.stacks then
					local slot_27_5 = math.floor(slot_27_3.buff.dariushemo.endTime - game.time)

					if slot_27_3.health + slot_27_3.physicalShield + slot_27_3.allShield + slot_27_3.healthRegenRate * 5 < slot_22_7.PassiveDmg(slot_27_3, slot_27_5) then
						break
					end
				end

				if slot_22_4.killsteal.bleed:get() and slot_27_3.health + slot_27_3.physicalShield + slot_27_3.allShield + slot_27_3.healthRegenRate * 5 <= slot_22_7.QDmg(slot_27_3) + slot_27_4 then
					slot_27_1 = true
				elseif slot_27_3.health + slot_27_3.physicalShield + slot_27_3.allShield + slot_27_3.healthRegenRate * 5 <= slot_22_7.QDmg(slot_27_3) then
					slot_27_1 = true
				end
			end
		end
	end

	if #slot_27_2 == 0 and slot_22_4.jungclear.jungclearq:get() and slot_22_4.lock.mode:get() == 1 and slot_22_0.menu.lane_clear.key:get() then
		local slot_27_6 = objManager.minions

		for iter_27_1 = 0, slot_27_6.size[TEAM_NEUTRAL] - 1 do
			local slot_27_7 = slot_27_6[TEAM_NEUTRAL][iter_27_1]

			if slot_27_7 and slot_22_5.isValid(slot_27_7) and not slot_22_5.WardName(slot_27_7) and player.pos:distSqr(slot_27_7) <= slot_22_15.Q ^ 2 then
				slot_27_2[#slot_27_2 + 1] = slot_27_7
			end
		end
	end

	if #slot_27_2 == 0 and slot_22_4.jungclear.jungclearq:get() and slot_22_4.lock.mode:get() == 1 and slot_22_0.menu.lane_clear.key:get() then
		local slot_27_8 = objManager.minions

		for iter_27_2 = 0, slot_27_8.size[TEAM_ENEMY] - 1 do
			local slot_27_9 = slot_27_8[TEAM_ENEMY][iter_27_2]

			if slot_27_9 and slot_22_5.isValid(slot_27_9) and not slot_22_5.WardName(slot_27_9) and player.pos:distSqr(slot_27_9) <= slot_22_15.Q ^ 2 then
				slot_27_2[#slot_27_2 + 1] = slot_27_9
			end
		end
	end

	if #slot_27_2 == 0 then
		return nil
	end

	slot_22_22:Clear()

	local slot_27_10 = 430
	local slot_27_11 = 260

	if #slot_27_2 == 1 then
		if slot_22_5.isFleeingFromMe(slot_27_2[1]) and arg_27_0 == true then
			slot_27_10 = 350
			slot_27_11 = 250
		end

		local slot_27_12 = slot_22_43(slot_27_2[1].pos2D, slot_27_10, slot_27_11)
		local slot_27_13 = slot_22_19()

		slot_27_13:Add(slot_27_12)

		return slot_27_13, slot_27_1
	else
		for iter_27_3, iter_27_4 in slot_22_39(slot_27_2) do
			if slot_22_5.isFleeingFromMe(slot_27_2[iter_27_3]) and arg_27_0 == true then
				slot_27_10 = 350
				slot_27_11 = 250
			end

			local slot_27_14 = slot_22_43(iter_27_4.pos2D, slot_27_10, slot_27_11)

			slot_22_22:AddPath(slot_27_14, iter_27_3 == 1 and slot_22_21.PolyType.Clip or slot_22_21.PolyType.Subject,
				true)
		end

		local slot_27_15 = slot_22_22:Execute(slot_22_21.ClipType.Union, slot_22_21.PolyFillType.EvenOdd,
			slot_22_21.PolyFillType.EvenOdd)

		slot_22_22:Clear()

		for iter_27_5, iter_27_6 in slot_22_39(slot_27_2) do
			if slot_22_5.isFleeingFromMe(slot_27_2[iter_27_5]) and arg_27_0 == true then
				slot_27_10 = 350
				slot_27_11 = 250
			end

			local slot_27_16 = slot_22_43(iter_27_6.pos2D, slot_27_10, slot_27_11)

			slot_22_22:AddPaths(slot_27_15, slot_22_21.PolyType.Clip, true)
			slot_22_22:AddPath(slot_27_16, slot_22_21.PolyType.Subject, true)

			local slot_27_17 = slot_22_22:Execute(slot_22_21.ClipType.Intersection, slot_22_21.PolyFillType.EvenOdd,
				slot_22_21.PolyFillType.EvenOdd)

			if slot_27_17 and slot_27_17:ChildCount() > 0 then
				slot_27_15 = slot_27_17
			end

			slot_22_22:Clear()
		end

		return slot_27_15 and slot_27_15:ChildCount() > 0 and slot_27_15, slot_27_1
	end
end

local function slot_22_46(arg_28_0, arg_28_1, arg_28_2)
	if arg_28_2 > 2000 then
		return
	end

	if arg_28_1 and arg_28_1.isVisible and arg_28_2 then
		if slot_22_0.combat.target then
			arg_28_0.obj = slot_22_0.combat.target

			return true
		elseif player:spellSlot(_Q).state == 0 and arg_28_2 and arg_28_2 <= slot_22_15.Q then
			arg_28_0.obj = arg_28_1

			return true
		elseif player:spellSlot(_W).state == 0 and arg_28_2 and arg_28_2 <= slot_22_15.W then
			arg_28_0.obj = arg_28_1

			return true
		elseif player:spellSlot(_E).state == 0 and arg_28_2 and arg_28_2 <= slot_22_15.E then
			arg_28_0.obj = arg_28_1

			return true
		elseif player:spellSlot(_R).state == 0 and arg_28_2 and arg_28_2 <= slot_22_15.R then
			arg_28_0.obj = arg_28_1

			return true
		end
	end
end

local function slot_22_47()
	return slot_22_0.ts.get_result(slot_22_46).obj
end

function slot82(arg_30_0, arg_30_1, arg_30_2)
	local slot_30_0 = arg_30_2.startPos:to3D(arg_30_1.pos.y)
	local slot_30_1 = arg_30_2.endPos:to3D(arg_30_1.pos.y)

	if arg_30_1.path.isDashing then
		local slot_30_2 = network.latency + arg_30_0.delay + slot_30_0:dist(slot_30_1) / arg_30_0.speed
		local slot_30_3 = math.max(0.1,
			network.latency + arg_30_0.delay -
			(arg_30_0.boundingRadiusMod == 1 and arg_30_0.width + arg_30_1.boundingRadius or arg_30_0.width) /
			arg_30_1.moveSpeed)

		if arg_30_1.pos:dist(slot_30_1) / arg_30_1.path.dashSpeed <= slot_30_0:dist(slot_30_1) / arg_30_0.speed + slot_30_3 and slot_30_3 - slot_30_2 <= 0.15 then
			return true
		end
	end

	return false
end

local function slot_22_48(arg_31_0)
	if player:spellSlot(_E).state ~= 0 or not arg_31_0 or not slot_22_5.isValid(arg_31_0) then
		return false
	end

	local slot_31_0 = 0
	local slot_31_1 = arg_31_0

	if slot_22_4.combo.comboepriority:get() then
		for iter_31_0 = 0, objManager.enemies_n - 1 do
			local slot_31_2 = objManager.enemies[iter_31_0]

			if slot_31_2 and not slot_31_2.isDead and slot_31_2.isVisible and slot_31_2.isTargetable and slot_31_2.team ~= TEAM_ALLY and slot_31_2.pos:distSqr(player) < (slot_22_16.range + 100) ^ 2 and slot_31_2.buff.dariushemo and slot_31_2.buff.dariushemo.stacks and slot_31_0 < slot_31_2.buff.dariushemo.stacks then
				slot_31_0 = slot_31_2.buff.dariushemo.stacks
				slot_31_1 = slot_31_2
			end
		end
	end

	if slot_31_1 and slot_22_5.isValid(slot_31_1) then
		if slot_31_1.pos:dist(player.pos) <= slot_22_16.range * 0.8 then
			slot_22_16.width = 140
		end

		if slot_31_1.pos:dist(player.pos) <= 300 then
			slot_22_16.width = 50
		end

		local slot_31_3 = slot_22_1.linear.get_prediction(slot_22_16, slot_31_1)

		if slot_31_3 and not slot_22_0.core.is_spell_locked() and slot_31_3.endPos and slot_31_3.startPos:distSqr(slot_31_3.endPos) < slot_22_16.range ^ 2 then
			if not slot_22_5.isFleeingFromMe(slot_31_1) and slot_31_3.endPos and slot_31_3.startPos:distSqr(slot_31_3.endPos) < (slot_22_16.range * 0.9) ^ 2 then
				player:castSpell("pos", 2, vec3(slot_31_3.endPos.x, slot_31_1.pos.y, slot_31_3.endPos.y))
			elseif slot_31_3.endPos and slot_31_3.startPos:distSqr(slot_31_3.endPos) < (slot_22_16.range * 0.8) ^ 2 then
				player:castSpell("pos", 2, vec3(slot_31_3.endPos.x, slot_31_1.pos.y, slot_31_3.endPos.y))
			elseif slot_22_5.trace_filter(slot_22_16, slot_31_3, slot_31_1) then
				player:castSpell("pos", 2, vec3(slot_31_3.endPos.x, slot_31_1.pos.y, slot_31_3.endPos.y))
			end
		end
	end
end

local function slot_22_49()
	if player:spellSlot(_R).state == 0 then
		for iter_32_0 = 0, objManager.enemies_n - 1 do
			local slot_32_0 = objManager.enemies[iter_32_0]

			if slot_32_0 and not slot_32_0.isDead and slot_32_0.isVisible and slot_32_0.isTargetable and slot_32_0.team ~= TEAM_ALLY and slot_32_0.pos:distSqr(player) < slot_22_15.R ^ 2 then
				if slot_22_4.ult.buff:get() and not slot_22_5.Undeadbuff(slot_32_0) then
					return
				end

				if slot_22_4.ult.countdown:get() and player.buff.dariusexecutemulticast and player.buff.dariusexecutemulticast.endTime - game.time <= 0.5 then
					player:castSpell("obj", 3, slot_32_0)
				end

				if slot_22_4.ult.rsolo:get() and slot_22_5.CountEnemiesNear(player, 1500) <= 1 then
					if player:spellSlot(_R).state == 0 and slot_32_0.pos:distSqr(player.pos) < slot_22_15.R ^ 2 then
						if slot_32_0.buff.dariushemo and slot_32_0.buff.dariushemo.stacks then
							local slot_32_1 = math.floor(slot_32_0.buff.dariushemo.endTime - game.time)

							if player.health / player.maxHealth * 100 > slot_22_4.ult.notuse:get() and slot_32_0.health + slot_32_0.physicalShield + slot_32_0.allShield + slot_32_0.healthRegenRate * 5 < slot_22_7.PassiveDmg(slot_32_0, slot_32_1) then
								return
							end
						end

						if slot_22_4.ult.health:get() and slot_32_0.health + slot_32_0.physicalShield + slot_32_0.allShield + slot_32_0.healthRegenRate * 5 < slot_22_7.RDmg(slot_32_0) + slot_22_7.PassiveDmg(slot_32_0, 5) - 20 then
							player:castSpell("obj", 3, slot_32_0)
						end
					end
				elseif slot_22_4.ult.killr:get() and player:spellSlot(_R).state == 0 and slot_32_0.pos:distSqr(player.pos) < slot_22_15.R ^ 2 then
					if slot_32_0.health + slot_32_0.physicalShield + slot_32_0.allShield + slot_32_0.healthRegenRate * 5 > slot_22_7.RDmg(slot_32_0) then
						if slot_32_0.buff.dariushemo and slot_32_0.buff.dariushemo.stacks >= 4 and player.health / player.maxHealth * 100 <= slot_22_4.ult.lowhealth:get() then
							player:castSpell("obj", 3, slot_32_0)
						end
					else
						player:castSpell("obj", 3, slot_32_0)
					end
				end
			end
		end
	end
end

local function slot_22_50()
	if not slot_22_8 or not slot_22_5.isValid(slot_22_8) then
		return false
	end

	if slot_22_4.combo.comboe:get() and player:spellSlot(_E).state == 0 and (slot_22_4.combo.ebuff:get() and not slot_22_8.buff.sionpassivezombie and not slot_22_8.buff.fioraw and not slot_22_8.buff.sivire and not slot_22_8.buff.jaxe and not slot_22_5.IsSpellShield(slot_22_8) or not slot_22_4.combo.ebuff:get()) then
		if slot_22_4.combo.comboemode:get() == 3 and slot_22_8.pos:distSqr(player.pos) < slot_22_15.E ^ 2 and slot_22_8.pos:distSqr(player.pos) >= slot_22_5.GetPlayerAARange(slot_22_8) ^ 2 and slot_22_0.core.can_action() then
			slot_22_48(slot_22_8)
		end

		if slot_22_4.combo.comboemode:get() == 2 and slot_22_8.pos:distSqr(player.pos) < slot_22_15.E ^ 2 and slot_22_0.core.can_action() then
			slot_22_48(slot_22_8)
		end

		if slot_22_4.combo.comboemode:get() == 1 and slot_22_8.pos:distSqr(player.pos) < slot_22_15.E ^ 2 and slot_22_0.core.can_action() then
			if slot_22_8.pos:distSqr(player.pos) >= slot_22_5.GetPlayerAARange(slot_22_8) ^ 2 then
				if player.buff.dariushemomax then
					slot_22_48(slot_22_8)
				elseif slot_22_4.combo.notusee:get() and not player.buff.summonerhaste or not slot_22_4.combo.notusee:get() or slot_22_5.UnderTurret(slot_22_8.pos) or slot_22_8.buff.dariushemo and slot_22_8.buff.dariushemo.stacks >= 4 or player.health / player.maxHealth * 100 < 20 then
					if player:spellSlot(0).state ~= 0 then
						slot_22_48(slot_22_8)
					elseif slot_22_8.pos:distSqr(player.pos) > slot_22_15.Q ^ 2 then
						slot_22_48(slot_22_8)
					end
				end
			elseif slot_22_0.farm.predict_hp(player, 1) < 200 or player.health / player.maxHealth * 100 < 15 then
				slot_22_48(slot_22_8)
			end
		end
	end

	if slot_22_4.combo.combow:get() and player:spellSlot(_W).state == 0 and (slot_22_4.combo.wbuff:get() and slot_22_5.Undeadbuff(slot_22_8) and not slot_22_8.buff.jaxe or not slot_22_4.combo.wbuff:get()) and not player.buff.dariusqcast then
		if slot_22_4.combo.combowmode:get() == 2 and slot_22_10 < game.time and slot_22_8.path.isDashing == false and slot_22_0.core.can_action() then
			player:castSpell("self", 1)
		end

		if slot_22_4.combo.combowmode:get() == 1 and slot_22_4.combo.notaw:get() and player:spellSlot(_Q).state ~= 0 and player:spellSlot(_E).state ~= 0 and slot_22_8.buff.dariushemo and slot_22_8.buff.dariushemo.stacks >= 4 and slot_22_8.pos:distSqr(player.pos) >= slot_22_5.GetPlayerAARange(slot_22_8) ^ 2 and slot_22_8.pos:distSqr(player.pos) < (slot_22_5.GetPlayerAARange(slot_22_8) + 100) ^ 2 and slot_22_0.core.can_action() then
			player:castSpell("self", 1)
		end
	end

	if slot_22_4.combo.comboq:get() and player:spellSlot(_Q).state == 0 and not player.buff.dariusqcast and (slot_22_5.isFleeingFromMe(slot_22_8) and slot_22_8.pos:distSqr(player.pos) < (slot_22_15.Q - 100) ^ 2 or slot_22_8.pos:distSqr(player.pos) < slot_22_15.Q ^ 2) and slot_22_0.core.can_action() then
		if slot_22_4.combo.comboemode:get() == 1 and slot_22_8.path.isDashing and slot_22_8.buff.dariushemo and slot_22_8.buff.dariushemo.stacks >= 4 then
			return
		end

		if player:spellSlot(_W).state == 0 and (slot_22_8.pos:distSqr(player.pos) < slot_22_5.GetPlayerAARange(slot_22_8) ^ 2 or slot_22_8.path.isDashing or player.buff.dariusnoxiantacticsonh) and slot_22_0.core.can_action() then
			return
		else
			player:castSpell("self", 0)
		end
	end
end

local function slot_22_51()
	if not slot_22_8 or not slot_22_5.isValid(slot_22_8) then
		return false
	end

	if slot_22_4.harass.harasse:get() and player:spellSlot(_E).state == 0 and (slot_22_4.harass.ebuff:get() and not slot_22_8.buff.sionpassivezombie and not slot_22_8.buff.fioraw and not slot_22_8.buff.sivire and not slot_22_8.buff.jaxe and not slot_22_5.IsSpellShield(slot_22_8) or not slot_22_4.harass.ebuff:get()) then
		if slot_22_4.harass.emode:get() == 3 and slot_22_8.pos:distSqr(player.pos) < slot_22_15.E ^ 2 and slot_22_8.pos:distSqr(player.pos) >= slot_22_5.GetPlayerAARange(slot_22_8) ^ 2 and slot_22_0.core.can_action() then
			slot_22_48(slot_22_8)
		end

		if slot_22_4.harass.emode:get() == 2 and slot_22_8.pos:distSqr(player.pos) < slot_22_15.E ^ 2 and slot_22_0.core.can_action() then
			slot_22_48(slot_22_8)
		end

		if slot_22_4.harass.emode:get() == 1 and slot_22_8.pos:distSqr(player.pos) < slot_22_15.E ^ 2 and slot_22_0.core.can_action() then
			if slot_22_8.pos:distSqr(player.pos) >= slot_22_5.GetPlayerAARange(slot_22_8) ^ 2 then
				if player.buff.dariushemomax then
					slot_22_48(slot_22_8)
				elseif player:spellSlot(0).state ~= 0 then
					slot_22_48(slot_22_8)
				elseif slot_22_8.pos:distSqr(player.pos) > slot_22_15.Q ^ 2 then
					slot_22_48(slot_22_8)
				end
			elseif slot_22_0.farm.predict_hp(player, 1) < 200 or player.health / player.maxHealth * 100 < 15 then
				slot_22_48(slot_22_8)
			end
		end
	end

	if slot_22_4.harass.harassw:get() and player:spellSlot(_W).state == 0 and (slot_22_4.harass.wbuff:get() and slot_22_5.Undeadbuff(slot_22_8) and not slot_22_8.buff.jaxe or not slot_22_4.harass.wbuff:get()) then
		if slot_22_4.harass.wmode:get() == 2 and slot_22_10 < game.time and slot_22_8.path.isDashing == false and slot_22_0.core.can_action() then
			player:castSpell("self", 1)
		end

		if slot_22_4.harass.wmode:get() == 1 and player:spellSlot(_Q).state ~= 0 and player:spellSlot(_E).state ~= 0 and slot_22_8.buff.dariushemo and slot_22_8.buff.dariushemo.stacks >= 4 and slot_22_8.pos:distSqr(player.pos) >= slot_22_5.GetPlayerAARange(slot_22_8) ^ 2 and slot_22_8.pos:distSqr(player.pos) < (slot_22_5.GetPlayerAARange(slot_22_8) + 80) ^ 2 and slot_22_0.core.can_action() then
			player:castSpell("self", 1)
		end
	end

	if slot_22_4.harass.harassq:get() and (slot_22_5.isFleeingFromMe(slot_22_8) and slot_22_8.pos:distSqr(player.pos) < (slot_22_15.Q - 100) ^ 2 or slot_22_8.pos:distSqr(player.pos) < slot_22_15.Q ^ 2) and slot_22_0.core.can_action() then
		if slot_22_4.harass.emode:get() == 1 and slot_22_8.path.isDashing and slot_22_8.buff.dariushemo and slot_22_8.buff.dariushemo.stacks >= 4 then
			return
		end

		if player:spellSlot(_W).state == 0 and (slot_22_8.pos:distSqr(player.pos) < slot_22_5.GetPlayerAARange(slot_22_8) ^ 2 or slot_22_8.path.isDashing or player.buff.dariusnoxiantacticsonh) and slot_22_0.core.can_action() then
			return
		else
			player:castSpell("self", 0)
		end
	end
end

local function slot_22_52()
	if player.mana * 100 / player.maxMana < slot_22_4.lasthit.mana:get() then
		return
	end

	if player:spellSlot(_W).state == 0 and slot_22_4.lasthit.lasthitw:get() then
		local slot_35_0 = {
			delay = 0.25,
			speed = math.huge,
			range = slot_22_5.GetPlayerAARange() + 100,
			damage = function(arg_36_0)
				return slot_22_7.WDmg(arg_36_0)
			end
		}
		local slot_35_1, slot_35_2 = slot_22_0.farm.skill_farm_target(slot_35_0)

		if slot_35_2 and slot_22_0.farm.predict_hp(slot_35_2, 0.35) < slot_22_7.WDmg(slot_35_2) then
			if slot_22_4.lasthit.wmode:get() == 2 and not slot_22_0.core.can_attack() and slot_22_0.core.can_action() and not slot_22_0.core.is_paused() and (slot_22_0.core.next_attack - os.clock() > 0.35 + network.latency or slot_22_0.core.next_attack - os.clock() < 0) then
				player:castSpell("self", 1)
			end

			if slot_22_4.lasthit.wmode:get() == 1 and slot_22_0.core.can_action() then
				player:castSpell("self", 1)
			end
		end
	end
end

local function slot_22_53()
	if not slot_22_4.lane:get() or slot_22_4.laneclear.notuse:get() and slot_22_5.CountEnemiesNear(player.pos, 1500) > 0 or player.mana * 100 / player.maxMana < slot_22_4.laneclear.mana:get() then
		return
	end

	if player:spellSlot(_W).state == 0 and slot_22_4.laneclear.laneclearw:get() then
		local slot_37_0 = {
			delay = 0.25,
			speed = math.huge,
			range = slot_22_5.GetPlayerAARange() + 100,
			damage = function(arg_38_0)
				return slot_22_7.WDmg(arg_38_0)
			end
		}
		local slot_37_1, slot_37_2 = slot_22_0.farm.skill_farm_target(slot_37_0)

		if slot_37_2 and slot_22_0.farm.predict_hp(slot_37_2, 0.35) < slot_22_7.WDmg(slot_37_2) then
			if slot_22_4.laneclear.wmode:get() == 2 and not slot_22_0.core.can_attack() and slot_22_0.core.can_action() and not slot_22_0.core.is_paused() and (slot_22_0.core.next_attack - os.clock() > 0.35 + network.latency or slot_22_0.core.next_attack - os.clock() < 0) then
				player:castSpell("self", 1)
			end

			if slot_22_4.laneclear.wmode:get() == 1 and slot_22_0.core.can_action() then
				player:castSpell("self", 1)
			end
		end
	end

	if player:spellSlot(_Q).state == 0 and slot_22_4.laneclear.laneclearq:get() and slot_22_5.GetMinionCount(500) >= slot_22_4.laneclear.qxx:get() then
		player:castSpell("self", 0)
	end
end

local function slot_22_54()
	if not slot_22_4.lane:get() then
		return
	end

	local slot_39_0 = objManager.minions

	for iter_39_0 = 0, slot_39_0.size[TEAM_NEUTRAL] - 1 do
		local slot_39_1 = slot_39_0[TEAM_NEUTRAL][iter_39_0]

		if slot_39_1 and slot_22_5.isValid(slot_39_1) and not slot_22_5.WardName(slot_39_1) then
			local slot_39_2 = vec3(slot_39_1.x, slot_39_1.y, slot_39_1.z)

			if player:spellSlot(_Q).state == 0 and slot_22_4.jungclear.jungclearq:get() and slot_22_0.core.can_action() and player.pos:distSqr(slot_39_1) <= slot_22_15.Q ^ 2 then
				player:castSpell("self", 0)
			end
		end
	end
end

slot_22_0.on_advanced_after_attack(function()
	if slot_22_4.combo.combowmode:get() == 1 and not player.buff.dariusqcast then
		local slot_40_0 = slot_22_0.combat.target
		local slot_40_1 = slot_22_0.farm.clear_target

		if slot_40_0 and (slot_22_4.combo.wbuff:get() and slot_22_5.Undeadbuff(slot_40_0) and not slot_40_0.buff.jaxe or not slot_22_4.combo.wbuff:get()) then
			if slot_22_5.isValid(slot_40_0) and slot_40_0.type == TYPE_HERO and slot_40_0.pos:dist(player.pos) <= slot_22_5.GetPlayerAARange(slot_40_0) + 150 then
				if slot_22_0.menu.hybrid.key:get() and player:spellSlot(_W).state == 0 then
					player:castSpell("self", 1)
					slot_22_0.core.set_server_pause()

					return true
				end

				if slot_22_0.menu.combat.key:get() and player:spellSlot(_W).state == 0 then
					player:castSpell("self", 1)
					slot_22_0.core.set_server_pause()

					return true
				end
			end

			return true
		end

		if slot_22_0.menu.lane_clear.key:get() and slot_40_1 and slot_40_1.isJungleMonster and slot_22_4.jungclear.jungclearw:get() then
			player:castSpell("self", 1)
			slot_22_0.core.set_server_pause()

			return true
		end
	end
end)

local function slot_22_55()
	if player:spellSlot(_Q).state == 0 then
		for iter_41_0 = 0, objManager.enemies_n - 1 do
			local slot_41_0 = objManager.enemies[iter_41_0]

			if slot_41_0 and slot_22_5.isValid(slot_41_0) and slot_41_0.team ~= TEAM_ALLY and slot_22_5.Undeadbuff(slot_41_0) and player.pos:distSqr(slot_41_0) <= 640000 and slot_22_4.killsteal.killstealqf:get() and player.pos:distSqr(slot_41_0) <= 640000 and player.pos:distSqr(slot_41_0) > 184900 then
				local slot_41_1 = slot_41_0.buff.dariushemo and slot_41_0.buff.dariushemo.stacks and
				slot_22_7.PassiveDmg(slot_41_0, 5) or 0

				if slot_22_9 and player:spellSlot(slot_22_9).state == 0 and slot_22_4.killsteal.killstealqf:get() then
					if slot_22_4.killsteal.notuse:get() and slot_41_0.buff.dariushemo and slot_41_0.buff.dariushemo.stacks then
						local slot_41_2 = math.floor(slot_41_0.buff.dariushemo.endTime - game.time)

						if slot_41_0.health + slot_41_0.physicalShield + slot_41_0.allShield + slot_41_0.healthRegenRate * 5 < slot_22_7.PassiveDmg(slot_41_0, slot_41_2) then
							break
						end
					end

					if slot_22_4.killsteal.bleed:get() then
						if slot_41_0.health + slot_41_0.physicalShield + slot_41_0.allShield + slot_41_0.healthRegenRate * 5 <= slot_22_7.QDmg(slot_41_0) + slot_41_1 then
							player:castSpell("self", 0)
						end
					elseif slot_41_0.health + slot_41_0.physicalShield + slot_41_0.allShield + slot_41_0.healthRegenRate * 5 <= slot_22_7.QDmg(slot_41_0) then
						player:castSpell("self", 0)
					end
				end
			end
		end
	end
end

local function slot_22_56()
	if player:spellSlot(_R).state == 0 then
		local slot_42_0 = 0
		local slot_42_1

		if slot_22_8 and slot_22_5.isValid(slot_22_8) then
			slot_42_1 = slot_22_8
		end

		for iter_42_0 = 0, objManager.enemies_n - 1 do
			local slot_42_2 = objManager.enemies[iter_42_0]

			if slot_42_2 and not slot_42_2.isDead and slot_42_2.isVisible and slot_42_2.isTargetable and slot_42_2.team ~= TEAM_ALLY and slot_42_2.pos:distSqr(player) < slot_22_15.R ^ 2 and slot_42_2.buff.dariushemo and slot_42_2.buff.dariushemo.stacks and slot_42_0 < slot_42_2.buff.dariushemo.stacks then
				slot_42_0 = slot_42_2.buff.dariushemo.stacks
				slot_42_1 = slot_42_2
			end
		end

		if slot_42_1 then
			player:castSpell("obj", _R, slot_42_1)
		end
	end
end

cb.add(cb.spell, function(arg_43_0)
	if arg_43_0 and arg_43_0.slot == 1 and arg_43_0.owner and arg_43_0.owner == player then
		slot_22_0.core.reset()
	end

	if arg_43_0 and arg_43_0.isBasicAttack and arg_43_0.owner and arg_43_0.owner == player and arg_43_0.target and arg_43_0.target.type == TYPE_TURRET then
		AttackTURRETarget = arg_43_0.target
	end

	if arg_43_0 and arg_43_0.isBasicAttack and arg_43_0.owner and arg_43_0.owner == player and arg_43_0.target and (arg_43_0.target.type == TYPE_HERO or arg_43_0.target.type == TYPE_MINION) then
		slot_22_13 = arg_43_0.target
	end

	if arg_43_0.name:find("DariusNoxianTacticsONHAttack") and arg_43_0.owner == player and arg_43_0.target.type == TYPE_HERO then
		slot_22_12 = game.time + 1
		slot_22_10 = 0

		if player:spellSlot(_Q).state == 0 and (slot_22_0.menu.combat.key:get() and slot_22_4.combo.comboq:get() or slot_22_0.menu.hybrid.key:get() and slot_22_4.harass.harassq:get()) then
			player:castSpell("self", 0)
		end
	end

	if arg_43_0.slot == 0 and arg_43_0.owner == player then
		-- block empty
	end

	if arg_43_0.slot == 1 and arg_43_0.owner == player then
		slot_22_10 = game.time + 0.3
	end

	if arg_43_0.slot == 2 and arg_43_0.owner == player then
		slot_22_11 = game.time + 0.3
	end

	if arg_43_0 and arg_43_0.owner and (arg_43_0.owner.team == TEAM_ALLY or arg_43_0.owner.type ~= TYPE_HERO) then
		return
	end

	if slot_22_4.eescape.edash:get() and player:spellSlot(2).state == 0 and arg_43_0.owner.health / arg_43_0.owner.maxHealth * 100 <= slot_22_4.eescape.ehp:get() and arg_43_0.owner and player.pos:distSqr(arg_43_0.owner) <= 225625 and player.pos:distSqr(arg_43_0.endPos) > 225625 then
		if arg_43_0.owner.charName == "Aatrox" and arg_43_0.slot == 2 then
			slot_22_48(arg_43_0.owner)
		elseif arg_43_0.owner.charName == "Ahri" and arg_43_0.slot == 3 then
			slot_22_48(arg_43_0.owner)
		elseif arg_43_0.owner.charName == "Akali" and (arg_43_0.slot == 2 or arg_43_0.slot == 3) then
			slot_22_48(arg_43_0.owner)
		elseif arg_43_0.owner.charName == "Akshan" and arg_43_0.slot == 2 then
			slot_22_48(arg_43_0.owner)
		elseif arg_43_0.owner.charName == "Alistar" and arg_43_0.slot == 1 then
			slot_22_48(arg_43_0.owner)
		elseif arg_43_0.owner.charName == "Amumu" and arg_43_0.slot == 0 then
			slot_22_48(arg_43_0.owner)
		elseif arg_43_0.owner.charName == "AurelionSol" and arg_43_0.slot == 1 then
			slot_22_48(arg_43_0.owner)
		elseif arg_43_0.owner.charName == "Azir" and arg_43_0.slot == 2 then
			slot_22_48(arg_43_0.owner)
		elseif arg_43_0.owner.charName == "Bard" and arg_43_0.slot == 1 then
			slot_22_48(arg_43_0.owner)
		elseif arg_43_0.owner.charName == "BeVeth" and arg_43_0.slot == 0 then
			slot_22_48(arg_43_0.owner)
		elseif arg_43_0.owner.charName == "Braum" and arg_43_0.slot == 1 then
			slot_22_48(arg_43_0.owner)
		elseif arg_43_0.owner.charName == "Caitlyn" and arg_43_0.slot == 2 then
			slot_22_48(arg_43_0.owner)
		elseif arg_43_0.owner.charName == "Camille" and arg_43_0.slot == 2 then
			slot_22_48(arg_43_0.owner)
		elseif arg_43_0.owner.charName == "Corki" and arg_43_0.slot == 1 then
			slot_22_48(arg_43_0.owner)
		elseif arg_43_0.owner.charName == "Diana" and arg_43_0.slot == 2 then
			slot_22_48(arg_43_0.owner)
		elseif arg_43_0.owner.charName == "Ekko" and (arg_43_0.slot == 2 or arg_43_0.slot == 3) then
			slot_22_48(arg_43_0.owner)
		elseif arg_43_0.owner.charName == "Evelynn" and arg_43_0.slot == 3 then
			slot_22_48(arg_43_0.owner)
		elseif arg_43_0.owner.charName == "Ezreal" and arg_43_0.slot == 2 then
			slot_22_48(arg_43_0.owner)
		elseif arg_43_0.owner.charName == "FiddleSticks" and arg_43_0.slot == 3 then
			slot_22_48(arg_43_0.owner)
		elseif arg_43_0.owner.charName == "Fiora" and arg_43_0.slot == 0 then
			slot_22_48(arg_43_0.owner)
		elseif arg_43_0.owner.charName == "Fizz" and (arg_43_0.slot == 2 or arg_43_0.slot == 0) then
			slot_22_48(arg_43_0.owner)
		elseif arg_43_0.owner.charName == "Galio" and arg_43_0.slot == 2 then
			slot_22_48(arg_43_0.owner)
		elseif arg_43_0.owner.charName == "Gnar" and arg_43_0.slot == 2 then
			slot_22_48(arg_43_0.owner)
		elseif arg_43_0.owner.charName == "Gragas" and arg_43_0.slot == 2 then
			slot_22_48(arg_43_0.owner)
		elseif arg_43_0.owner.charName == "Graves" and arg_43_0.slot == 2 then
			slot_22_48(arg_43_0.owner)
		elseif arg_43_0.owner.charName == "Gwen" and arg_43_0.slot == 2 then
			slot_22_48(arg_43_0.owner)
		elseif arg_43_0.owner.charName == "Hecarim" and arg_43_0.slot == 3 then
			slot_22_48(arg_43_0.owner)
		elseif arg_43_0.owner.charName == "Irelia" and arg_43_0.slot == 0 then
			slot_22_48(arg_43_0.owner)
		elseif arg_43_0.owner.charName == "JarvanIV" and arg_43_0.slot == 0 then
			slot_22_48(arg_43_0.owner)
		elseif arg_43_0.owner.charName == "Jax" and arg_43_0.slot == 0 then
			slot_22_48(arg_43_0.owner)
		elseif arg_43_0.owner.charName == "Jayce" and arg_43_0.slot == 0 then
			slot_22_48(arg_43_0.owner)
		elseif arg_43_0.owner.charName == "KSante" and arg_43_0.slot == 1 then
			slot_22_48(arg_43_0.owner)
		elseif arg_43_0.owner.charName == "KaiSa" and arg_43_0.slot == 3 then
			slot_22_48(arg_43_0.owner)
		elseif arg_43_0.owner.charName == "Kalista" and arg_43_0.slot == 0 then
			slot_22_48(arg_43_0.owner)
		elseif arg_43_0.owner.charName == "Kassadin" and arg_43_0.slot == 3 then
			slot_22_48(arg_43_0.owner)
		elseif arg_43_0.owner.charName == "Katarina" and arg_43_0.slot == 2 then
			slot_22_48(arg_43_0.owner)
		elseif arg_43_0.owner.charName == "Kayn" and (arg_43_0.slot == 2 or arg_43_0.slot == 0) then
			slot_22_48(arg_43_0.owner)
		elseif arg_43_0.owner.charName == "Kennen" and arg_43_0.slot == 2 then
			slot_22_48(arg_43_0.owner)
		elseif arg_43_0.owner.charName == "KhaZix" and arg_43_0.slot == 2 then
			slot_22_48(arg_43_0.owner)
		elseif arg_43_0.owner.charName == "Kindred" and arg_43_0.slot == 0 then
			slot_22_48(arg_43_0.owner)
		elseif arg_43_0.owner.charName == "Kled" and arg_43_0.slot == 2 then
			slot_22_48(arg_43_0.owner)
		elseif arg_43_0.owner.charName == "LeBlanc" and arg_43_0.slot == 1 then
			slot_22_48(arg_43_0.owner)
		elseif arg_43_0.owner.charName == "LeeSin" and (arg_43_0.slot == 1 or arg_43_0.slot == 0) then
			slot_22_48(arg_43_0.owner)
		elseif arg_43_0.owner.charName == "Leona" and arg_43_0.slot == 2 then
			slot_22_48(arg_43_0.owner)
		elseif arg_43_0.owner.charName == "Lillia" and arg_43_0.slot == 2 then
			slot_22_48(arg_43_0.owner)
		elseif arg_43_0.owner.charName == "Lissandra" and arg_43_0.slot == 2 then
			slot_22_48(arg_43_0.owner)
		elseif arg_43_0.owner.charName == "Lucian" and arg_43_0.slot == 2 then
			slot_22_48(arg_43_0.owner)
		elseif arg_43_0.owner.charName == "Naafiri" and arg_43_0.slot == 1 then
			slot_22_48(arg_43_0.owner)
		elseif arg_43_0.owner.charName == "Nautilus" and arg_43_0.slot == 0 then
			slot_22_48(arg_43_0.owner)
		elseif arg_43_0.owner.charName == "Nidalee" and arg_43_0.slot == 1 then
			slot_22_48(arg_43_0.owner)
		elseif arg_43_0.owner.charName == "Nilah" and arg_43_0.slot == 2 then
			slot_22_48(arg_43_0.owner)
		elseif arg_43_0.owner.charName == "Ornn" and arg_43_0.slot == 2 then
			slot_22_48(arg_43_0.owner)
		elseif arg_43_0.owner.charName == "Pantheon" and arg_43_0.slot == 1 then
			slot_22_48(arg_43_0.owner)
		elseif arg_43_0.owner.charName == "Pyke" and arg_43_0.slot == 2 then
			slot_22_48(arg_43_0.owner)
		elseif arg_43_0.owner.charName == "Qiyana" and (arg_43_0.slot == 2 or arg_43_0.slot == 1) then
			slot_22_48(arg_43_0.owner)
		elseif arg_43_0.owner.charName == "Quinn" and arg_43_0.slot == 2 then
			slot_22_48(arg_43_0.owner)
		elseif arg_43_0.owner.charName == "Rakan" and (arg_43_0.slot == 0 or arg_43_0.slot == 1 or arg_43_0.slot == 2) then
			slot_22_48(arg_43_0.owner)
		elseif arg_43_0.owner.charName == "Rammus" and arg_43_0.slot == 3 then
			slot_22_48(arg_43_0.owner)
		elseif arg_43_0.owner.charName == "RekSai" and arg_43_0.slot == 1 then
			slot_22_48(arg_43_0.owner)
		elseif arg_43_0.owner.charName == "Rell" and arg_43_0.slot == 1 then
			slot_22_48(arg_43_0.owner)
		elseif arg_43_0.owner.charName == "Renekton" and arg_43_0.slot == 2 then
			player:castSpell("pos", 2, arg_43_0.endPos)
		elseif arg_43_0.owner.charName == "Riven" and arg_43_0.slot == 2 then
			slot_22_48(arg_43_0.owner)
		elseif arg_43_0.owner.charName == "Samira" and arg_43_0.slot == 2 then
			slot_22_48(arg_43_0.owner)
		elseif arg_43_0.owner.charName == "Sejuani" and arg_43_0.slot == 0 then
			slot_22_48(arg_43_0.owner)
		elseif arg_43_0.owner.charName == "Shaco" and arg_43_0.slot == 0 then
			slot_22_48(arg_43_0.owner)
		elseif arg_43_0.owner.charName == "Shen" and arg_43_0.slot == 2 then
			slot_22_48(arg_43_0.owner)
		elseif arg_43_0.owner.charName == "Shyvana" and arg_43_0.slot == 3 then
			slot_22_48(arg_43_0.owner)
		elseif arg_43_0.owner.charName == "Smolder" and arg_43_0.slot == 2 then
			slot_22_48(arg_43_0.owner)
		elseif arg_43_0.owner.charName == "Sylas" and arg_43_0.slot == 2 then
			slot_22_48(arg_43_0.owner)
		elseif arg_43_0.owner.charName == "Tahm" and arg_43_0.slot == 1 then
			slot_22_48(arg_43_0.owner)
		elseif arg_43_0.owner.charName == "Taliyah" and arg_43_0.slot == 3 then
			slot_22_48(arg_43_0.owner)
		elseif arg_43_0.owner.charName == "Talon" and arg_43_0.slot == 2 then
			slot_22_48(arg_43_0.owner)
		elseif arg_43_0.owner.charName == "Thresh" and arg_43_0.slot == 0 then
			slot_22_48(arg_43_0.owner)
		elseif arg_43_0.owner.charName == "Tristana" and arg_43_0.slot == 1 then
			slot_22_48(arg_43_0.owner)
		elseif arg_43_0.owner.charName == "Tryndamere" and arg_43_0.slot == 2 then
			slot_22_48(arg_43_0.owner)
		elseif arg_43_0.owner.charName == "Urgot" and arg_43_0.slot == 2 then
			slot_22_48(arg_43_0.owner)
		elseif arg_43_0.owner.charName == "Vayne" and arg_43_0.slot == 0 then
			slot_22_48(arg_43_0.owner)
		elseif arg_43_0.owner.charName == "Vi" and arg_43_0.slot == 0 then
			slot_22_48(arg_43_0.owner)
		elseif arg_43_0.owner.charName == "Viego" and arg_43_0.slot == 3 then
			slot_22_48(arg_43_0.owner)
		elseif arg_43_0.owner.charName == "Volibear" and arg_43_0.slot == 3 then
			slot_22_48(arg_43_0.owner)
		elseif arg_43_0.owner.charName == "Warwick" and arg_43_0.slot == 3 then
			slot_22_48(arg_43_0.owner)
		elseif arg_43_0.owner.charName == "MonkeyKing" and (arg_43_0.slot == 2 or arg_43_0.slot == 1) then
			slot_22_48(arg_43_0.owner)
		elseif arg_43_0.owner.charName == "XinZhao" and arg_43_0.slot == 2 then
			slot_22_48(arg_43_0.owner)
		elseif arg_43_0.owner.charName == "Yone" and (arg_43_0.slot == 0 or arg_43_0.slot == 3) then
			slot_22_48(arg_43_0.owner)
		elseif arg_43_0.owner.charName == "Yasuo" and (arg_43_0.slot == 0 or arg_43_0.slot == 2) then
			slot_22_48(arg_43_0.owner)
		elseif arg_43_0.owner.charName == "Yuumi" and arg_43_0.slot == 1 then
			slot_22_48(arg_43_0.owner)
		elseif arg_43_0.owner.charName == "Zac" and arg_43_0.slot == 2 then
			slot_22_48(arg_43_0.owner)
		elseif arg_43_0.owner.charName == "Zed" and (arg_43_0.slot == 1 or arg_43_0.slot == 3) then
			slot_22_48(arg_43_0.owner)
		elseif arg_43_0.owner.charName == "Zeri" and arg_43_0.slot == 2 then
			slot_22_48(arg_43_0.owner)
		elseif arg_43_0.owner.charName == "Ziggs" and arg_43_0.slot == 1 then
			slot_22_48(arg_43_0.owner)
		end
	end
end)
cb.add(cb.tick, function()
	slot_22_8 = slot_22_47()

	if not slot_22_4.saver:get() then
		slot_22_49()
	end

	slot_22_55()

	if slot_22_0.menu.combat.key:get() then
		slot_22_50()
	end

	if slot_22_0.menu.hybrid.key:get() then
		slot_22_51()
	end

	if slot_22_0.menu.last_hit.key:get() then
		slot_22_52()
	end

	if slot_22_0.menu.lane_clear.key:get() then
		slot_22_53()
		slot_22_54()
	end

	if slot_22_4.usee:get() then
		player:move(game.mousePos)

		if slot_22_8 then
			slot_22_48(slot_22_8)
		end
	end

	if slot_22_4.user:get() then
		slot_22_56()
	end

	if slot_22_4.lock.outsidelock:get() and player.buff.dariusqcast and (slot_22_4.lock.mode:get() == 1 or slot_22_4.lock.mode:get() == 2 and (slot_22_0.menu.combat.key:get() or slot_22_0.menu.hybrid.key:get())) then
		local slot_44_0, slot_44_1 = slot_22_45(true)

		if slot_44_0 then
			local slot_44_2
			local slot_44_3 = math.huge
			local slot_44_4 = false

			if slot_22_4.lock.notlock:get() then
				for iter_44_0 = 0, slot_44_0:ChildCount() - 1 do
					local slot_44_5 = slot_44_0:Childs(iter_44_0)

					if slot_44_5 and slot_44_5:Contains(player.pos2D) ~= 0 then
						slot_44_4 = true

						break
					end
				end
			end

			if slot_22_4.lock.notlock2:get() then
				for iter_44_1 = 0, objManager.enemies_n - 1 do
					local slot_44_6 = objManager.enemies[iter_44_1]

					if slot_44_6 and not slot_44_6.isDead and slot_44_6.isVisible and slot_44_6.isTargetable and slot_44_6.team ~= TEAM_ALLY and slot_44_6.pos:distSqr(player) < 78400 then
						local slot_44_7 = slot_22_7.QDmg(slot_44_6)

						if slot_44_6.health < slot_44_7 / 3 then
							slot_44_4 = true
						end
					end
				end
			end

			if slot_44_4 == false then
				for iter_44_2 = 0, slot_44_0:ChildCount() - 1 do
					local slot_44_8 = slot_44_0:Childs(iter_44_2)

					for iter_44_3 = 0, slot_44_8:ChildCount() - 1 do
						local slot_44_9 = slot_22_7.line_polygon_intersection_point(slot_44_8,
							slot_44_8:Childs(iter_44_3), player.pos2D)

						if slot_44_9 then
							local slot_44_10 = slot_22_6.VectorExtend(player.pos2D, slot_44_9,
								player.pos2D:dist(slot_44_9) + 100)

							if slot_44_10 and slot_44_8:Contains(slot_44_10) == 0 then
								for iter_44_4 = 30, 210, 30 do
									slot_44_10 = slot_22_6.VectorExtend(player.pos2D, slot_44_9,
										player.pos2D:dist(slot_44_9) + iter_44_4)

									if slot_44_10 and slot_44_8:Contains(slot_44_10) ~= 0 then
										break
									end
								end
							end

							if slot_44_10 then
								local slot_44_11 = (slot_44_10.x - player.pos2D.x) ^ 2 +
								(slot_44_10.y - player.pos2D.y) ^ 2

								if slot_44_11 < slot_44_3 then
									slot_44_2 = slot_44_10
									slot_44_3 = slot_44_11
								end
							end
						end
					end
				end
			end

			if slot_44_2 then
				slot_22_3.core.set_pause(0.1)
				slot_22_0.core.set_pause(0.1)
				player:move(vec3(slot_44_2.x, player.y, slot_44_2.y))

				if slot_22_4.killsteal.killstealqf:get() and slot_22_9 and player:spellSlot(slot_22_9).state == 0 and slot_44_1 == true and player.buff.dariusqcast then
					local slot_44_12 = player.moveSpeed or 0
					local slot_44_13 = player.buff.dariusqcast.endTime - game.time
					local slot_44_14 = player.pos2D:dist(vec2(slot_44_2.x, slot_44_2.y))
					local slot_44_15 = player.buff.dariusqcast.endTime - game.time + slot_44_14 / slot_44_12

					if slot_44_13 > 0.4 + network.latency and slot_44_13 < 0.55 + network.latency and slot_44_15 > slot_44_13 - 0.2 and slot_44_14 < 500 then
						player:castSpell("pos", slot_22_9, vec3(slot_44_2.x, player.y, slot_44_2.y))
					end
				end
			end
		end
	end
end)

local function slot_22_57()
	if player.isDead or not player.isOnScreen then
		return
	end

	if slot_22_4.draws.target:get() and slot_22_8 and slot_22_5.isValid(slot_22_8) and slot_22_8.isOnScreen then
		--slot_22_5.drawCircle(slot_22_8.pos.xz, 0.05, 0.01, 1.5, 1.5)
	end

	if slot_22_4.draws.drawe:get() then
		if slot_22_4.draws.drawsmode:get() == 1 then
			slot_22_5.drawCircle(player.pos.xz, 0.65, 0.006, 0, 0)
		else
			graphics.draw_circle_xyz(player.x, player.y, player.z, 525, 1, slot_22_4.draws.drawscolour:get(), 100)
		end
	end

	if slot_22_4.draws.look:get() and slot_22_4.lock.outsidelock:get() and player.buff.dariusqcast and (slot_22_4.lock.mode:get() == 1 or slot_22_4.lock.mode:get() == 2 and (slot_22_0.menu.combat.key:get() or slot_22_0.menu.hybrid.key:get())) then
		local slot_45_0, slot_45_1 = slot_22_45(false)

		if slot_45_0 and slot_45_0:ChildCount() > 0 then
			for iter_45_0 = 0, slot_45_0:ChildCount() - 1 do
				local slot_45_2 = slot_45_0:Childs(iter_45_0)

				if slot_45_2 then
					local slot_45_3 = slot_45_2:ChildCount()
					local slot_45_4 = vec2.array(slot_45_3 + 1)

					for iter_45_1 = 0, slot_45_3 - 1 do
						local slot_45_5 = slot_45_2:Childs(iter_45_1)
						local slot_45_6 = graphics.world_to_screen(vec3(slot_45_5.x, player.y, slot_45_5.y))

						slot_45_4[iter_45_1].x = slot_45_6.x
						slot_45_4[iter_45_1].y = slot_45_6.y
					end

					slot_45_4[slot_45_3].x = slot_45_4[0].x
					slot_45_4[slot_45_3].y = slot_45_4[0].y

					local slot_45_7 = graphics.world_to_screen(player.pos)

					graphics.draw_line_strip_2D(slot_45_4, 2, 4294967295, slot_45_3 + 1)
				end
			end
		end
	end

	if slot_22_4.draws.bleed:get() and player.buff.dariushemomax then
		local slot_45_8 = player.buff.dariushemomax.endTime - game.time
		local slot_45_9 = string.format("%.2f", slot_45_8)
		local slot_45_10 = graphics.world_to_screen(player.pos)

		graphics.draw_text_2D("Bleed : " .. slot_45_9, 25, slot_45_10.x - 50, slot_45_10.y + 20, **********)
	end

	if slot_22_4.draws.ult:get() and player.buff.dariusexecutemulticast then
		local slot_45_11 = player.buff.dariusexecutemulticast.endTime - game.time
		local slot_45_12 = string.format("%.2f", slot_45_11)
		local slot_45_13 = graphics.world_to_screen(player.pos)

		graphics.draw_text_2D("Ult : " .. slot_45_12, 25, slot_45_13.x - 50, slot_45_13.y + 45, **********)
	end

	if slot_22_4.draws.damage:get() then
		for iter_45_2 = 0, objManager.enemies_n - 1 do
			local slot_45_14 = objManager.enemies[iter_45_2]

			if slot_45_14 and slot_45_14.team and slot_45_14.type and slot_45_14.team == TEAM_ENEMY and slot_45_14.isVisible and slot_45_14.type == TYPE_HERO and slot_45_14.isOnScreen and slot_45_14.barPos then
				local slot_45_15 = graphics.height > 1080 and graphics.height / 1080 * 0.905 or 1
				local slot_45_16 = vec2(109 * slot_45_15, 111 * slot_45_15)
				local slot_45_17 = vec2(54 * slot_45_15, 11 * slot_45_15)
				local slot_45_18 = slot_45_14.barPos + slot_45_16 + slot_45_17
				local slot_45_19 = slot_45_18.x
				local slot_45_20 = slot_45_18.y
				local slot_45_21 = player:spellSlot(0).state == 0 and slot_22_7.QDmg(slot_45_14) or 0
				local slot_45_22 = player:spellSlot(1).state == 0 and slot_22_7.WDmg(slot_45_14) or 0
				local slot_45_23 = player:spellSlot(3).state == 0 and slot_22_7.RDmg(slot_45_14) or 0
				local slot_45_24 = slot_45_14.buff.dariushemo and slot_45_14.buff.dariushemo.stacks and
				slot_22_7.PassiveDmg(slot_45_14, 5) or 0
				local slot_45_25 = slot_45_14.health -
				(slot_45_21 + slot_45_22 + slot_45_23 + slot_45_24 + slot_22_7.AADmg(slot_45_14) + slot_22_7.AADmg(slot_45_14))
				local slot_45_26 = slot_45_19 + slot_45_14.health / slot_45_14.maxHealth * 102 * slot_45_15
				local slot_45_27 = slot_45_19 +
				(slot_45_25 > 0 and slot_45_25 or 0) / slot_45_14.maxHealth * 102 * slot_45_15
				local slot_45_28 = slot_45_19 + 60

				graphics.draw_line_2D(slot_45_26, slot_45_20, slot_45_27, slot_45_20, 10.5, **********)

				if slot_22_4.draws.damagetext:get() and slot_45_14 and not slot_45_14.isDead and slot_45_14.isVisible and slot_45_14.isTargetable and slot_45_14.team ~= TEAM_ALLY then
					local slot_45_29 = slot_45_21 + slot_45_22 + slot_45_23 + slot_45_24 + slot_22_7.AADmg(slot_45_14)
					local slot_45_30 = math.floor(slot_45_29)

					if slot_45_30 > slot_45_14.health then
						graphics.draw_text_2D("Killable", 17, slot_45_28, slot_45_20 - 20, **********)
					else
						graphics.draw_text_2D(slot_45_30, 17, slot_45_28, slot_45_20 - 20, **********)
					end
				end

				if slot_22_4.draws.bleedobj:get() and slot_45_14.buff.dariushemo and slot_45_14.buff.dariushemo.stacks then
					local slot_45_31 = graphics.world_to_screen(slot_45_14.pos)
					local slot_45_32 = slot_45_14.buff.dariushemo.endTime - game.time
					local slot_45_33 = string.format("%.2f", slot_45_32)

					graphics.draw_text_2D(slot_45_14.buff.dariushemo.stacks, 25, slot_45_31.x - 10, slot_45_31.y + 10,
						**********)
				end
			end
		end
	end
end

cb.add(cb.draw, slot_22_57)
cb.add(cb.tick, function()
	if not player.isDead and player.isTargetable and player:spellSlot(2).state == 0 then
		for iter_46_0 = 0, objManager.enemies_n - 1 do
			local slot_46_0 = objManager.enemies[iter_46_0]

			if slot_46_0 and not slot_46_0.isDead and slot_46_0.isVisible and slot_46_0.isTargetableToTeamFlags and slot_46_0.pos:distSqr(player.pos) < 1322500 then
				if slot_22_4.Interruptibles.tp:get() and slot_46_0.buff.summonerteleport or slot_22_4.Interruptibles.hexflash:get() and slot_46_0.buff.summonerflashperkshextechflashtraptionv2 or slot_46_0.charName == "Quinn" and slot_46_0:spellSlot(3).name == "QuinnRReturnToQuinn" or slot_46_0.charName == "Nunu" and slot_46_0:spellSlot(3).name == "NunuR_Recast" or slot_46_0.charName == "Jhin" and slot_46_0:spellSlot(3).name == "JhinRShot" or slot_46_0.charName == "FiddleSticks" and (player.buff.fiddlestickswdrainrr or player.buff.fiddlerevealedvfx) then
					local slot_46_1 = slot_22_1.linear.get_prediction(slot_22_16, slot_46_0)

					if slot_46_1 and slot_46_1.endPos and slot_46_1.startPos:distSqr(slot_46_1.endPos) < slot_22_16.range ^ 2 then
						player:castSpell("pos", 2, vec3(slot_46_1.endPos.x, slot_46_0.pos.y, slot_46_1.endPos.y))
					end
				end

				if slot_22_4.Interruptibles[slot_46_0.charName] then
					for iter_46_1 = 0, 3 do
						local slot_46_2 = slot_22_4.Interruptibles[slot_46_0.charName]
						[slot_46_0:spellSlot(iter_46_1).name]

						if slot_46_2 and slot_46_2.Dodge and slot_46_2.Dodge:get() and player.health / player.maxHealth * 100 <= slot_46_2.hp:get() and slot_46_2.buffname and slot_46_2.buffname.text and slot_46_2.buffname.text ~= "" and slot_46_0.buff[slot_46_2.buffname.text] then
							local slot_46_3 = slot_22_1.linear.get_prediction(slot_22_16, slot_46_0)

							if slot_46_3 and slot_46_3.endPos and slot_46_3.startPos:distSqr(slot_46_3.endPos) < slot_22_16.range ^ 2 then
								player:castSpell("pos", 2, vec3(slot_46_3.endPos.x, slot_46_0.pos.y, slot_46_3.endPos.y))
							end
						end
					end
				end
			end
		end
	end
end)
cb.add(cb.spell, function(arg_47_0)
	if slot_22_5.RecallActive(player) then
		return
	end

	if slot_22_4.Gap.enable:get() == false then
		return
	end

	if slot_22_4.Gap.GapIgnore:get() and not slot_22_5.UnderTurret(player.pos) then
		return
	end

	if arg_47_0 and arg_47_0.owner and arg_47_0.slot and arg_47_0.owner.isTargetable and arg_47_0.owner.type == TYPE_HERO and player:spellSlot(2).state == 0 and player.pos:distSqr(arg_47_0.owner.pos) <= 1322500 and not player.isDead and player.isTargetable and slot_22_4.Gap[arg_47_0.owner.charName] and slot_22_4.Gap[arg_47_0.owner.charName][arg_47_0.name] and slot_22_4.Gap[arg_47_0.owner.charName][arg_47_0.name].Dodge and slot_22_4.Gap[arg_47_0.owner.charName][arg_47_0.name].Dodge:get() and player.health / player.maxHealth * 100 <= slot_22_4.Gap[arg_47_0.owner.charName][arg_47_0.name].hp:get() then
		if arg_47_0.target and arg_47_0.target == player then
			local slot_47_0 = slot_22_1.linear.get_prediction(slot_22_16, arg_47_0.owner)

			if slot_47_0 and slot_47_0.endPos and slot_47_0.startPos:distSqr(slot_47_0.endPos) < slot_22_16.range ^ 2 then
				player:castSpell("pos", 2, vec3(slot_47_0.endPos.x, arg_47_0.owner.pos.y, slot_47_0.endPos.y))
			end
		elseif arg_47_0.startPos and arg_47_0.endPos and player.pos:distSqr(arg_47_0.endPos) < 40000 then
			local slot_47_1 = slot_22_1.linear.get_prediction(slot_22_16, arg_47_0.owner)

			if slot_47_1 and slot_47_1.endPos and slot_47_1.startPos:distSqr(slot_47_1.endPos) < slot_22_16.range ^ 2 then
				player:castSpell("pos", 2, vec3(slot_47_1.endPos.x, arg_47_0.owner.pos.y, slot_47_1.endPos.y))
			end
		end
	end
end)

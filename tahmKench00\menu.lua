

local ove_0_10 = menu("<PERSON><PERSON>ch2", "<PERSON>")
local ove_0_11 = module.load(header.id, "common0")

ove_0_10:header("header_keys", "Keybind")
ove_0_10:menu("bind", "Bindable")
ove_0_10.bind:keybind("combat", "Combat Key", "Space", nil)
ove_0_10.bind:keybind("farm", "Farm Key", "C", nil)
ove_0_10.bind:keybind("harras", "Auto Q Key", nil, "V")
ove_0_10.bind:keybind("ultimate", "Manual W Key", "T", nil)
ove_0_10.bind:keybind("dive", "Turret Dive", nil, "G")
ove_0_10:boolean("enable_q", "Use Q ", true)
ove_0_10:slider("mana_farmq", "Q if Mana <= ", 15, 1, 100, 5)
ove_0_10:header("header_w", "W")
ove_0_10:boolean("enable_w", "Use W Offensive", true)
ove_0_10:boolean("slowonlyw", "Use W if slowed only", true)
ove_0_10:boolean("enable_w_close", "Use W if enemy is in AA", false)
ove_0_10:slider("mana_farmw", "W if Mana <= ", 15, 1, 100, 5)
ove_0_10:header("header_e", "E")
ove_0_10:boolean("enable_e", "Use E", true)
ove_0_10:boolean("enable_e_incoming", "Use E Shield incoming Damage", true)
ove_0_10:slider("healthamount_e", "Incoming Damage % to use E", 9, 1, 100, 1)
ove_0_10:slider("mana_farme", "E if Mana <= ", 15, 1, 100, 5)
ove_0_10:header("header_r", "R ")
ove_0_10:menu("r", "R")
ove_0_10.r:boolean("enable_r", "Use R", true)
ove_0_10.r:boolean("enable_r_spellshield", "Do not R SpellShields", true)
ove_0_10.r:boolean("enable_r_magicshields", "Calculate Magic Shields", true)
ove_0_10.r:boolean("enable_r_trueshields", "Calculate True Shields", true)
ove_0_10.r:boolean("enable_r_interrupt", "Interrupt With R", true)
ove_0_10.r:menu("rint", "R Interruptables")

for iter_0_0 = 1, #ove_0_11.GetEnemyHeroes() do
	local ove_0_12 = ove_0_11.GetEnemyHeroes()[iter_0_0]
	local ove_0_13 = string.lower(ove_0_12.charName)

	if ove_0_12 and ove_0_11.interruptableSpells[ove_0_13] then
		for iter_0_1 = 1, #ove_0_11.interruptableSpells[ove_0_13] do
			local ove_0_14 = ove_0_11.interruptableSpells[ove_0_13][iter_0_1]

			ove_0_10.r.rint:boolean(string.format(tostring(ove_0_12.charName) .. tostring(ove_0_14.menuslot)), "Interrupt " .. tostring(ove_0_12.charName) .. " " .. tostring(ove_0_14.menuslot), true)
		end
	end
end

ove_0_10:header("header_draww", "Draw ")
ove_0_10:menu("range", "Debug/Drawing")
ove_0_10.range:boolean("q", "Draw Q range", true)
ove_0_10.range:color("c1", "Color", 152, 89, 14, 103)
ove_0_10.range:boolean("w", "Draw w range", true)
ove_0_10.range:color("c2", "Color", 88, 89, 114, 103)
ove_0_10.range:boolean("r", "Draw R range", true)
ove_0_10.range:color("c4", "Color", 255, 189, 14, 103)
ove_0_10.range:boolean("disable_drawing", "Disable Drawings", false)
ove_0_10.range:boolean("accuracy", "Show Q accuracy", true)

local ove_0_15 = {
	border = 2281701376,
	text = 4294967295,
	mouseOver = 4294728333,
	backGround = 2281701376,
	menu = {
		selected = 4294728333,
		textSelected = 4283826969
	},
	boolean = {
		active = 4283826969,
		textActive = 4294907027
	},
	slider = {
		bar = 4283826969
	},
	header = {
		text = 4281472845,
		fill = 4293821166
	},
	keybind = {
		textActive = 4294967295,
		textInactive = 4294967295
	},
	button = {
		text = 4294439713,
		fill = 4288544926
	},
	color = {
		outLine = 4278203481
	}
}

ove_0_10:button("Pink", "Custom Menu", "Pink", function()
	ove_0_10:setcustomtheme(ove_0_15)
end)

return ove_0_10

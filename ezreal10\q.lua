local slot10_a1320 = module.load(header.id, "lvxbot/main")
local slot11_a1323 = slot10_a1320.load("menu")

local slot13_a1325 = {
	delay = 0.25,
	range = 1110,
	PredZs = 0,
	type = "Linear",
	movseep = 1000,
	boundingRadiusMod = 1,
	speed = 2000,
	mov = 0.22,
	movtime = 0.35,
	width = 60,
	collision = {
		minion = true,
		hero = false,
		wall = true
	}
}
local slot13_a1327 = {
	pred = function ()
		if slot11_a1323.pred:get() then
			return 1
		else
			return 2
		end
	end,
	focusW = function()
		return 0
	end
}
local slot13_a1338 = {
	type = "pos",
	slot = _Q,
	arg1 = function (arg0_a1340)
		local slot1_a1342 = arg0_a1340.ts_result.seg
		local slot2_a1344 = arg0_a1340.ts_result.obj

		return vec3(slot1_a1342.endPos.x, game.mousePos.y, slot1_a1342.endPos.y)
	end
}
local slot12_a1324 = {
	ignore_obj_radius = 2000,
	prediction = slot13_a1325,
	cast = slot13_a1327,
	target_selector = {
		type = "LESS_CAST_AD"
	},
	cast_spell = slot13_a1338,
	slot = _Q
}
return slot10_a1320.expert.create(slot12_a1324)

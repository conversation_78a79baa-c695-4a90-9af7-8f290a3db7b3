local var_0_0 = "1.0"
local var_0_1 = module.seek("evade")
local var_0_2 = module.load("<PERSON>", "Utility/SpellDatabaseSupport")
local var_0_3 = module.internal("pred")
local var_0_4 = module.internal("TS")
local var_0_5 = module.internal("orb")
local var_0_6 = module.load("<PERSON>", "Utility/common20")
local var_0_7 = {
	range = 325
}
local var_0_8 = {
	speed = 1950,
	range = 1400,
	delay = 0.15,
	boundingRadiusMod = 1,
	width = 65,
	collision = {
		minion = true,
		wall = true
	}
}
local var_0_9 = {
	range = 250
}
local var_0_10 = {
	range = 310
}
local var_0_11 = {
	speed = 1000,
	range = 750,
	delay = 0.3,
	boundingRadiusMod = 0,
	width = 60
}
local var_0_12 = {
	range = 1500
}
local var_0_13

if player:spellSlot(4).name == "SummonerFlash" then
	var_0_13 = 4
elseif player:spellSlot(5).name == "SummonerFlash" then
	var_0_13 = 5
end

local var_0_14 = menu("<PERSON>" .. player.charName, "[<PERSON>] " .. player.charName)

var_0_14:menu("combo", "Combo")
var_0_14.combo:header("qset", " -- Q Settings --")
var_0_14.combo:boolean("qcombo", "Use Q", true)
var_0_14.combo:boolean("slowpred", "^- Use Unburrowed Q Only for AA Reset", true)
var_0_14.combo:header("wset", " -- W Settings --")
var_0_14.combo:boolean("wcombo", "Use W", true)
var_0_14.combo:header("eset", " -- E Settings --")
var_0_14.combo:boolean("usee", "Use E", true)
var_0_14.combo:boolean("waa", "^- Use Burrowed E to GapClose", true)
var_0_14.combo:boolean("fury", "^- Unburrowed E only if Max Fury", false)
var_0_14.combo:header("rset", " -- R Settings --")
var_0_14.combo:dropdown("rmode", "R Mode", 2, {
	"Never",
	"Killable with Combo",
	"Killable with R"
})
var_0_14.combo:boolean("autor", "Use R if Killable with Combo", true)
var_0_14.combo:keybind("toggle", "R Under-Turret Toggle", nil, "T")
var_0_14:menu("harass", "Harass")
var_0_14.harass:header("qset", " -- Q Settings --")
var_0_14.harass:boolean("qcombo", "Use Q", true)
var_0_14.harass:boolean("slowpred", "^- Use Unburrowed Q Only for AA Reset", true)
var_0_14.harass:header("wset", " -- W Settings --")
var_0_14.harass:boolean("wcombo", "Use W", true)
var_0_14.harass:header("eset", " -- E Settings --")
var_0_14.harass:boolean("usee", "Use E", true)
var_0_14.harass:boolean("waa", "^- Use Burrowed E to GapClose", true)
var_0_14:menu("farming", "Farming")
var_0_14.farming:keybind("toggle", "Farm Toggle", nil, "A")
var_0_14.farming:header("aa", " ~~~~ ")
var_0_14.farming:menu("laneclear", "Lane Clear")
var_0_14.farming.laneclear:boolean("farme", "Use Unburrowed E", true)
var_0_14.farming.laneclear:boolean("laste", " ^- Only for Last Hit", true)
var_0_14.farming:menu("jungleclear", "Jungle Clear")
var_0_14.farming.jungleclear:boolean("farmq", "Use Q", true)
var_0_14.farming.jungleclear:boolean("farmw", "Use W", true)
var_0_14.farming.jungleclear:boolean("farme", "Use Unburrowed E in Jungle Clear", true)
var_0_14.farming.jungleclear:boolean("fury", "^- Only if Max Fury", false)
var_0_14:menu("killsteal", "Killsteal")
var_0_14.killsteal:boolean("ksq", "Use Burrowed Q", true)
var_0_14.killsteal:boolean("kse", "Use Unburrowed E", true)
var_0_14:menu("draws", "Draw Settings")
var_0_14.draws:header("ranges", " -- Ranges -- ")
var_0_14.draws:boolean("drawq", "Draw Q Range", true)
var_0_14.draws:color("colorq", "  ^- Color", 153, 204, 255, 255)
var_0_14.draws:boolean("draww", "Draw W Range", false)
var_0_14.draws:color("colorw", "  ^- Color", 255, 153, 153, 255)
var_0_14.draws:boolean("drawe", "Draw E Range", true)
var_0_14.draws:color("colore", "  ^- Color", 153, 204, 255, 255)
var_0_14.draws:boolean("drawr", "Draw R Range", true)
var_0_14.draws:color("colorr", "  ^- Color", 255, 153, 153, 255)
var_0_14.draws:header("other", " -- Other -- ")
var_0_14.draws:boolean("drawdamage", "Draw Damage", true)
var_0_14.draws:slider("transparency", "Damage Drawing Transparency", 155, 0, 255, 1)
var_0_14:menu("misc", "Misc.")
var_0_14.misc:menu("Gap", "Gapcloser Settings")
var_0_14.misc.Gap:boolean("GapA", "Use Burrowed W", true)
var_0_14.misc.Gap:menu("gapblacklist", "Blacklist")

local var_0_15 = var_0_6.GetEnemyHeroes()

for iter_0_0, iter_0_1 in ipairs(var_0_15) do
	var_0_14.misc.Gap.gapblacklist:boolean(iter_0_1.charName, "Ignore: " .. iter_0_1.charName, false)
end

var_0_14:header("a", " ~~~~ ")
var_0_14:keybind("flashw", "Flash W Key", "G", nil)
var_0_4.load_to_menu(var_0_14)

local function var_0_16(arg_1_0, arg_1_1, arg_1_2)
	if var_0_3.trace.linear.hardlock(arg_1_0, arg_1_1, arg_1_2) then
		return true
	end

	if var_0_3.trace.linear.hardlockmove(arg_1_0, arg_1_1, arg_1_2) then
		return true
	end

	if arg_1_2 and var_0_6.IsValidTarget(arg_1_2) and (player.pos:dist(arg_1_2) <= player.attackRange + player.boundingRadius + arg_1_2.boundingRadius or arg_1_1.startPos:dist(arg_1_1.endPos) <= 625) then
		return true
	end

	if var_0_3.trace.newpath(arg_1_2, 0.033, 0.5) then
		return true
	end
end

function is_turret_near(arg_2_0)
	for iter_2_0 = 0, objManager.turrets.size[TEAM_ENEMY] - 1 do
		local var_2_0 = objManager.turrets[TEAM_ENEMY][iter_2_0]

		if var_2_0 and not var_2_0.isDead and var_2_0.pos:dist(arg_2_0) < 910 then
			return true
		end
	end

	return false
end

local var_0_17

local function var_0_18(arg_3_0)
	if arg_3_0.name == "RekSaiR" and arg_3_0.owner == player then
		if arg_3_0.target.pos:dist(player.pos) < 400 then
			var_0_5.core.set_pause_attack(arg_3_0.animationTime + 1)
		else
			var_0_5.core.set_pause_attack(arg_3_0.animationTime + arg_3_0.target.pos:dist(player.pos) / 800)
		end
	end

	if arg_3_0.name == "RekSaiW" and arg_3_0.owner == player then
		var_0_5.core.set_pause_attack(arg_3_0.animationTime)
	end

	if arg_3_0 and arg_3_0.name:find("BasicAttack") and arg_3_0.owner == player then
		var_0_17 = arg_3_0.target
	end
end

local function var_0_19(arg_4_0, arg_4_1, arg_4_2)
	if arg_4_2 <= var_0_7.range then
		arg_4_0.obj = arg_4_1

		return true
	end
end

local function var_0_20()
	return var_0_4.get_result(var_0_19).obj
end

local function var_0_21(arg_6_0, arg_6_1, arg_6_2)
	if arg_6_2 <= var_0_8.range then
		arg_6_0.obj = arg_6_1

		return true
	end
end

local function var_0_22()
	return var_0_4.get_result(var_0_21).obj
end

local function var_0_23(arg_8_0, arg_8_1, arg_8_2)
	if arg_8_2 <= var_0_11.range then
		arg_8_0.obj = arg_8_1

		return true
	end
end

local function var_0_24()
	return var_0_4.get_result(var_0_23).obj
end

local function var_0_25(arg_10_0, arg_10_1, arg_10_2)
	if arg_10_2 <= var_0_9.range then
		arg_10_0.obj = arg_10_1

		return true
	end
end

local function var_0_26()
	return var_0_4.get_result(var_0_25).obj
end

local function var_0_27(arg_12_0, arg_12_1, arg_12_2)
	if arg_12_2 <= var_0_12.range then
		arg_12_0.obj = arg_12_1

		return true
	end
end

local function var_0_28()
	return var_0_4.get_result(var_0_27).obj
end

local function var_0_29(arg_14_0, arg_14_1)
	local var_14_0 = {}

	for iter_14_0 = 0, objManager.enemies_n - 1 do
		local var_14_1 = objManager.enemies[iter_14_0]

		if arg_14_1 > arg_14_0:dist(var_14_1.pos) and var_0_6.IsValidTarget(var_14_1) then
			var_14_0[#var_14_0 + 1] = var_14_1
		end
	end

	return var_14_0
end

local function var_0_30(arg_15_0, arg_15_1)
	local var_15_0 = {}

	for iter_15_0 = 0, objManager.minions.size[TEAM_ENEMY] - 1 do
		local var_15_1 = objManager.minions[TEAM_ENEMY][iter_15_0]

		if arg_15_1 > arg_15_0:dist(var_15_1.pos) and var_0_6.IsValidTarget(var_15_1) then
			var_15_0[#var_15_0 + 1] = var_15_1
		end
	end

	return var_15_0
end

local function var_0_31(arg_16_0, arg_16_1)
	local var_16_0 = {}

	for iter_16_0 = 0, objManager.minions.size[TEAM_ENEMY] - 1 do
		local var_16_1 = objManager.minions[TEAM_ENEMY][iter_16_0]

		if arg_16_1 > arg_16_0:dist(var_16_1.pos) and var_0_6.IsValidTarget(var_16_1) then
			var_16_0[#var_16_0 + 1] = var_16_1
		end
	end

	return var_16_0
end

local function var_0_32(arg_17_0, arg_17_1)
	local var_17_0 = {}

	for iter_17_0 = 0, objManager.minions.size[TEAM_NEUTRAL] - 1 do
		local var_17_1 = objManager.minions[TEAM_NEUTRAL][iter_17_0]

		if arg_17_1 > arg_17_0:dist(var_17_1.pos) and var_0_6.IsValidTarget(var_17_1) then
			var_17_0[#var_17_0 + 1] = var_17_1
		end
	end

	return var_17_0
end

local var_0_33 = {
	60,
	90,
	120,
	150,
	180
}

function QDamage(arg_18_0)
	local var_18_0 = 0

	if player:spellSlot(0).level > 0 then
		var_18_0 = var_0_6.CalculatePhysicalDamage(arg_18_0, var_0_33[player:spellSlot(0).level] + var_0_6.GetBonusAD() * 0.5 + var_0_6.GetTotalAP() * 0.7, player)
	end

	return var_18_0
end

local var_0_34 = {
	100,
	250,
	400
}
local var_0_35 = {
	0.18,
	0.23,
	0.28
}

function RDamage(arg_19_0)
	local var_19_0 = 0

	if player:spellSlot(3).level > 0 then
		local var_19_1 = (arg_19_0.maxHealth - arg_19_0.health) * var_0_35[player:spellSlot(3).level]

		var_19_0 = var_0_6.CalculatePhysicalDamage(arg_19_0, var_0_34[player:spellSlot(3).level] + var_0_6.GetBonusAD() * 2 + var_19_1, player)
	end

	return var_19_0
end

local var_0_36 = {
	55,
	65,
	75,
	85,
	95
}

function EDamage(arg_20_0)
	local var_20_0 = 0

	if player:spellSlot(2).level > 0 then
		var_20_0 = var_0_6.CalculatePhysicalDamage(arg_20_0, var_0_36[player:spellSlot(2).level] + var_0_6.GetBonusAD() * 0.85, player)
	end

	if player:spellSlot(2).level > 0 and player.mana == 100 then
		var_20_0 = (var_0_36[player:spellSlot(2).level] + var_0_6.GetBonusAD() * 0.85) * 2
	end

	return var_20_0
end

local function var_0_37()
	if player.buff.reksaiw then
		if var_0_14.harass.waa:get() and var_0_14.harass.usee:get() then
			local var_21_0 = var_0_24()

			if var_21_0 and var_21_0.pos:dist(player.pos) > 600 then
				local var_21_1 = var_0_3.linear.get_prediction(var_0_11, var_21_0)

				if var_21_1 then
					player:castSpell("pos", 2, vec3(var_21_1.endPos.x, var_21_0.pos.y, var_21_1.endPos.y))
				end
			end
		end

		if var_0_14.harass.qcombo:get() then
			local var_21_2 = var_0_22()

			if var_21_2 then
				local var_21_3 = var_0_3.linear.get_prediction(var_0_8, var_21_2)

				if var_21_3 and var_21_3.startPos:dist(var_21_3.endPos) < var_0_8.range and var_0_16(var_0_8, var_21_3, var_21_2) and not var_0_3.collision.get_prediction(var_0_8, var_21_3, var_21_2) then
					player:castSpell("pos", 0, vec3(var_21_3.endPos.x, var_21_2.pos.y, var_21_3.endPos.y))
				end
			end
		end

		if var_0_14.harass.wcombo:get() and #var_0_29(player.pos, var_0_9.range) > 0 then
			player:castSpell("self", 1)
		end
	end

	if not player.buff.reksaiw then
		if var_0_14.harass.qcombo:get() and not var_0_14.harass.slowpred:get() and var_0_5.combat.target then
			player:castSpell("self", 0)
		end

		if var_0_14.harass.usee:get() then
			local var_21_4 = var_0_26()

			if var_21_4 then
				player:castSpell("obj", 2, var_21_4)
			end
		end

		if var_0_14.harass.wcombo:get() then
			local var_21_5 = var_0_26()

			if var_21_5 and not var_21_5.buff.reksaiknockupimmune and not player.buff.reksaiq then
				player:castSpell("self", 1)
			end
		end
	end
end

local function var_0_38()
	if player.buff.reksaiw then
		if var_0_14.combo.waa:get() and var_0_14.combo.usee:get() then
			local var_22_0 = var_0_24()

			if var_22_0 and var_22_0.pos:dist(player.pos) > 600 then
				local var_22_1 = var_0_3.linear.get_prediction(var_0_11, var_22_0)

				if var_22_1 then
					player:castSpell("pos", 2, vec3(var_22_1.endPos.x, var_22_0.pos.y, var_22_1.endPos.y))
				end
			end
		end

		if var_0_14.combo.qcombo:get() then
			local var_22_2 = var_0_22()

			if var_22_2 then
				local var_22_3 = var_0_3.linear.get_prediction(var_0_8, var_22_2)

				if var_22_3 and var_22_3.startPos:dist(var_22_3.endPos) < var_0_8.range and var_0_16(var_0_8, var_22_3, var_22_2) and not var_0_3.collision.get_prediction(var_0_8, var_22_3, var_22_2) then
					player:castSpell("pos", 0, vec3(var_22_3.endPos.x, var_22_2.pos.y, var_22_3.endPos.y))
				end
			end
		end

		if var_0_14.combo.wcombo:get() and #var_0_29(player.pos, var_0_9.range) > 0 then
			player:castSpell("self", 1)
		end
	end

	if not player.buff.reksaiw then
		if var_0_14.combo.qcombo:get() and not var_0_14.combo.slowpred:get() and var_0_5.combat.target then
			player:castSpell("self", 0)
		end

		if var_0_14.combo.usee:get() then
			local var_22_4 = var_0_26()

			if var_22_4 then
				if not var_0_14.combo.fury:get() then
					player:castSpell("obj", 2, var_22_4)
				end

				if var_0_14.combo.fury:get() and player.mana == 100 then
					player:castSpell("obj", 2, var_22_4)
				end
			end
		end

		if var_0_14.combo.wcombo:get() then
			local var_22_5 = var_0_26()

			if var_22_5 and not var_22_5.buff.reksaiknockupimmune and not player.buff.reksaiq then
				player:castSpell("self", 1)
			end
		end
	end

	if var_0_14.combo.rmode:get() == 2 then
		local var_22_6 = var_0_28()

		if var_22_6 and var_22_6.health <= (RDamage(var_22_6) + QDamage(var_22_6) + EDamage(var_22_6)) * 1.2 then
			if var_0_14.combo.toggle:get() then
				player:castSpell("obj", 3, var_22_6)
			end

			if var_0_14.combo.toggle:get() == false and is_turret_near(var_22_6.pos) == false then
				player:castSpell("obj", 3, var_22_6)
			end
		end
	end

	if var_0_14.combo.rmode:get() == 3 then
		local var_22_7 = var_0_28()

		if var_22_7 and var_22_7.health <= RDamage(var_22_7) then
			if var_0_14.combo.toggle:get() then
				player:castSpell("obj", 3, var_22_7)
			end

			if var_0_14.combo.toggle:get() == false and is_turret_near(var_22_7.pos) == false then
				player:castSpell("obj", 3, var_22_7)
			end
		end
	end
end

local function var_0_39()
	if player.buff.reksaiw then
		if var_0_14.farming.jungleclear.farmq:get() and player:spellSlot(0).state == 0 then
			for iter_23_0 = 0, objManager.minions.size[TEAM_NEUTRAL] - 1 do
				local var_23_0 = objManager.minions[TEAM_NEUTRAL][iter_23_0]

				if var_23_0 and var_23_0.isVisible and var_23_0.moveSpeed > 0 and var_23_0.isTargetable and not var_23_0.isDead and var_23_0.pos:dist(player.pos) < var_0_8.range then
					local var_23_1 = var_0_3.linear.get_prediction(var_0_8, var_23_0)

					if var_23_1 and player.pos:dist(vec3(var_23_1.endPos.x, var_23_0.y, var_23_1.endPos.y)) < var_0_8.range then
						player:castSpell("pos", 0, vec3(var_23_1.endPos.x, var_23_0.pos.y, var_23_1.endPos.y))
					end
				end
			end
		end

		if var_0_14.farming.jungleclear.farmw:get() and player:spellSlot(1).state == 0 and #var_0_32(player.pos, var_0_9.range) > 0 then
			player:castSpell("self", 1)
		end
	end

	if not player.buff.reksaiw then
		if var_0_14.farming.jungleclear.farmq:get() and player:spellSlot(0).state == 0 and #var_0_32(player.pos, var_0_7.range) > 0 then
			player:castSpell("self", 0)
		end

		if var_0_14.farming.jungleclear.farme:get() and player:spellSlot(2).state == 0 then
			for iter_23_1 = 0, objManager.minions.size[TEAM_NEUTRAL] - 1 do
				local var_23_2 = objManager.minions[TEAM_NEUTRAL][iter_23_1]

				if var_23_2 and var_23_2.isVisible and var_23_2.moveSpeed > 0 and var_23_2.isTargetable and not var_23_2.isDead and var_23_2.pos:dist(player.pos) < var_0_10.range then
					if not var_0_14.farming.jungleclear.fury:get() then
						player:castSpell("obj", 2, var_23_2)
					end

					if var_0_14.farming.jungleclear.fury:get() and player.mana == 100 then
						player:castSpell("obj", 2, var_23_2)
					end
				end
			end
		end

		if var_0_14.farming.jungleclear.farmw:get() and player:spellSlot(1).state == 0 then
			for iter_23_2 = 0, objManager.minions.size[TEAM_NEUTRAL] - 1 do
				local var_23_3 = objManager.minions[TEAM_NEUTRAL][iter_23_2]

				if var_23_3 and var_23_3.isVisible and var_23_3.moveSpeed > 0 and var_23_3.isTargetable and not var_23_3.isDead and var_23_3.pos:dist(player.pos) < var_0_9.range and var_0_5.core.can_action() and not var_23_3.buff.reksaiknockupimmune then
					player:castSpell("self", 1)
				end
			end
		end
	end
end

function VectorPointProjectionOnLineSegment(arg_24_0, arg_24_1, arg_24_2)
	local var_24_0 = arg_24_2.x
	local var_24_1 = arg_24_2.z or arg_24_2.y
	local var_24_2 = arg_24_0.x
	local var_24_3 = arg_24_0.z or arg_24_0.y
	local var_24_4 = arg_24_1.x
	local var_24_5 = arg_24_1.z or arg_24_1.y
	local var_24_6 = ((var_24_0 - var_24_2) * (var_24_4 - var_24_2) + (var_24_1 - var_24_3) * (var_24_5 - var_24_3)) / ((var_24_4 - var_24_2)^2 + (var_24_5 - var_24_3)^2)
	local var_24_7 = {
		x = var_24_2 + var_24_6 * (var_24_4 - var_24_2),
		y = var_24_3 + var_24_6 * (var_24_5 - var_24_3)
	}
	local var_24_8 = var_24_6 < 0 and 0 or var_24_6 > 1 and 1 or var_24_6
	local var_24_9 = var_24_8 == var_24_6

	return var_24_9 and var_24_7 or {
		x = var_24_2 + var_24_8 * (var_24_4 - var_24_2),
		y = var_24_3 + var_24_8 * (var_24_5 - var_24_3)
	}, var_24_7, var_24_9
end

function GetNMinionsHitE(arg_25_0)
	local var_25_0 = 0
	local var_25_1
	local var_25_2 = vec3(arg_25_0.x, 0, arg_25_0.z)
	local var_25_3 = vec3(player.x, 0, player.z)

	for iter_25_0 = 0, objManager.minions.size[TEAM_ENEMY] - 1 do
		local var_25_4 = objManager.minions[TEAM_ENEMY][iter_25_0]

		if var_25_4 and var_25_4.isVisible and not var_25_4.isDead and var_25_4.moveSpeed > 0 and var_25_4.isTargetable then
			local var_25_5 = vec3(var_25_4.x, 0, var_25_4.z)
			local var_25_6 = VectorPointProjectionOnLineSegment(player.pos - (var_0_9.range - 250) * (player.pos - arg_25_0.pos):norm(), var_25_3, var_25_5)

			if vec2(var_25_5.x, var_25_5.z):dist(vec2(var_25_6.x, var_25_6.y)) < 250 then
				var_25_0 = var_25_0 + 1
				var_25_1 = var_25_4
			end
		end
	end

	return var_25_0, var_25_1
end

local function var_0_40()
	if not player.buff.reksaiw and var_0_14.farming.laneclear.farme:get() and player:spellSlot(2).state == 0 then
		for iter_26_0 = 0, objManager.minions.size[TEAM_ENEMY] - 1 do
			local var_26_0 = objManager.minions[TEAM_ENEMY][iter_26_0]

			if var_26_0 and var_26_0.isVisible and var_26_0.moveSpeed > 0 and var_26_0.isTargetable and not var_26_0.isDead and var_26_0.pos:dist(player.pos) < var_0_10.range then
				if not var_0_14.farming.laneclear.laste:get() then
					player:castSpell("self", 2)
				end

				if var_0_14.farming.laneclear.laste:get() and var_26_0.health <= EDamage(var_26_0) then
					player:castSpell("obj", 2, var_26_0)
				end
			end
		end
	end
end

local function var_0_41()
	if not player.buff.reksaiw and var_0_14.farming.laneclear.laste:get() and player:spellSlot(2).state == 0 then
		for iter_27_0 = 0, objManager.minions.size[TEAM_ENEMY] - 1 do
			local var_27_0 = objManager.minions[TEAM_ENEMY][iter_27_0]

			if var_27_0 and var_27_0.isVisible and var_27_0.moveSpeed > 0 and var_27_0.isTargetable and not var_27_0.isDead and var_27_0.pos:dist(player.pos) < var_0_10.range and var_0_14.farming.laneclear.laste:get() and var_27_0.health <= EDamage(var_27_0) then
				player:castSpell("obj", 2, var_27_0)
			end
		end
	end
end

local var_0_42 = mathf.round
local var_0_43
local var_0_44
local var_0_45 = math.cos
local var_0_46 = mathf.sin

local function var_0_47()
	if player.isDead then
		return
	end

	if player.isOnScreen then
		if var_0_14.draws.drawr:get() then
			graphics.draw_circle(player.pos, var_0_12.range, 3, var_0_14.draws.colorr:get(), 40)
		end

		if player.buff.reksaiw then
			if var_0_14.draws.drawq:get() then
				graphics.draw_circle(player.pos, var_0_8.range, 3, var_0_14.draws.colorq:get(), 40)
			end

			if var_0_14.draws.draww:get() then
				graphics.draw_circle(player.pos, var_0_9.range, 3, var_0_14.draws.colorw:get(), 40)
			end

			if var_0_14.draws.drawe:get() then
				graphics.draw_circle(player.pos, var_0_11.range, 3, var_0_14.draws.colorw:get(), 40)
			end
		end

		if not player.buff.reksaiw then
			if var_0_14.draws.draww:get() then
				graphics.draw_circle(player.pos, var_0_9.range, 3, var_0_14.draws.colorw:get(), 40)
			end

			if var_0_14.draws.drawe:get() then
				graphics.draw_circle(player.pos, var_0_10.range, 3, var_0_14.draws.colore:get(), 40)
			end
		end

		local var_28_0 = graphics.world_to_screen(vec3(player.x, player.y, player.z))
		local var_28_1 = graphics.world_to_screen(vec3(player.x, player.y, player.z))

		graphics.draw_text_2D("Farm: ", 15, var_28_1.x - 50, var_28_1.y + 20, graphics.argb(255, 255, 255, 255))

		if var_0_14.farming.toggle:get() then
			graphics.draw_text_2D("ON", 15, var_28_1.x - 10, var_28_1.y + 20, graphics.argb(255, 128, 255, 0))
		else
			graphics.draw_text_2D("OFF", 15, var_28_1.x - 10, var_28_1.y + 20, graphics.argb(255, 218, 34, 34))
		end

		graphics.draw_text_2D("E Under-Turret: ", 15, var_28_1.x - 50, var_28_1.y + 40, graphics.argb(255, 255, 255, 255))

		if var_0_14.combo.toggle:get() then
			graphics.draw_text_2D("ON", 15, var_28_1.x + 55, var_28_1.y + 40, graphics.argb(255, 128, 255, 0))
		else
			graphics.draw_text_2D("OFF", 15, var_28_1.x + 55, var_28_1.y + 40, graphics.argb(255, 218, 34, 34))
		end
	end

	if var_0_14.draws.drawdamage:get() then
		for iter_28_0 = 0, objManager.enemies_n - 1 do
			local var_28_2 = objManager.enemies[iter_28_0]

			if var_28_2 and var_28_2.isVisible and var_28_2.team == TEAM_ENEMY and var_28_2.isOnScreen then
				local var_28_3 = var_28_2.barPos
				local var_28_4 = var_28_3.x + 164
				local var_28_5 = var_28_3.y + 122.5
				local var_28_6 = QDamage(var_28_2)
				local var_28_7 = EDamage(var_28_2)
				local var_28_8 = RDamage(var_28_2)
				local var_28_9 = var_28_2.health - (var_28_6 + var_28_7)
				local var_28_10 = var_28_4 + var_28_2.health / var_28_2.maxHealth * 102
				local var_28_11 = var_28_4 + (var_28_9 > 0 and var_28_9 or 0) / var_28_2.maxHealth * 102

				if var_28_9 > 0 then
					graphics.draw_line_2D(var_28_10, var_28_5, var_28_11, var_28_5, 10, graphics.argb(var_0_14.draws.transparency:get(), 255, 192, 200))
				else
					graphics.draw_line_2D(var_28_10, var_28_5, var_28_11, var_28_5, 10, graphics.argb(var_0_14.draws.transparency:get(), 0, 255, 0))
				end
			end
		end
	end
end

local function var_0_48()
	if var_0_14.misc.Gap.GapA:get() == false then
		return
	end

	local var_29_0 = {}
	local var_29_1 = var_0_4.get_result(function(arg_30_0, arg_30_1, arg_30_2)
		if arg_30_2 <= var_0_9.range and arg_30_1.path.isActive and arg_30_1.path.isDashing and var_0_14.misc.Gap.gapblacklist[arg_30_1.charName] and not var_0_14.misc.Gap.gapblacklist[arg_30_1.charName]:get() then
			arg_30_0.obj = arg_30_1

			return true
		end
	end).obj

	if var_29_1 then
		local var_29_2 = var_0_3.core.lerp(var_29_1.path, network.latency + 0.25, var_29_1.path.dashSpeed)

		if var_29_2 and var_29_2:dist(player.path.serverPos2D) <= var_0_9.range then
			var_29_0.startPos = player.path.serverPos2D
			var_29_0.endPos = vec2(var_29_2.x, var_29_2.y)

			if player.buff.reksaiw then
				player:castSpell("self", 1)
			end
		end
	end
end

local function var_0_49()
	local var_31_0 = var_0_6.GetEnemyHeroes()

	for iter_31_0, iter_31_1 in ipairs(var_31_0) do
		if iter_31_1 and var_0_6.IsValidTarget(iter_31_1) and not iter_31_1.buff[17] then
			local var_31_1 = var_0_6.GetShieldedHealth("AP", iter_31_1)

			if var_0_14.killsteal.ksq:get() and player.buff.reksaiw and player:spellSlot(0).state == 0 and vec3(iter_31_1.x, iter_31_1.y, iter_31_1.z):dist(player) < var_0_8.range and var_31_1 <= QDamage(iter_31_1) then
				local var_31_2 = var_0_3.linear.get_prediction(var_0_8, iter_31_1)

				if var_31_2 and var_31_2.startPos:dist(var_31_2.endPos) < var_0_8.range and not var_0_3.collision.get_prediction(var_0_8, var_31_2, iter_31_1) then
					player:castSpell("pos", 0, vec3(var_31_2.endPos.x, iter_31_1.pos.y, var_31_2.endPos.y))
				end
			end

			if var_0_14.killsteal.kse:get() and not player.buff.reksaiw and player:spellSlot(1).state == 0 and vec3(iter_31_1.x, iter_31_1.y, iter_31_1.z):dist(player) < var_0_10.range and var_31_1 <= EDamage(iter_31_1) then
				player:castSpell("obj", 2, iter_31_1)
			end
		end
	end
end

var_0_5.combat.register_f_after_attack(function()
	if not player.buff.reksaiw then
		if var_0_5.menu.hybrid.key:get() and player:spellSlot(0).state == 0 and var_0_17 and var_0_17 and var_0_6.IsValidTarget(var_0_17) and player.pos:dist(var_0_17.pos) < 500 and var_0_14.harass.qcombo:get() and var_0_14.harass.slowpred:get() then
			player:castSpell("self", 0)
			var_0_5.core.reset()
			var_0_5.core.set_server_pause()
			var_0_5.combat.set_invoke_after_attack(false)

			return "on_after_attack_hydra"
		end

		if var_0_5.menu.combat.key:get() and player:spellSlot(0).state == 0 and var_0_17 and var_0_17 and var_0_6.IsValidTarget(var_0_17) and player.pos:dist(var_0_17.pos) < 500 and var_0_14.combo.qcombo:get() and var_0_14.combo.slowpred:get() then
			player:castSpell("self", 0)
			var_0_5.core.reset()
			var_0_5.core.set_server_pause()
			var_0_5.combat.set_invoke_after_attack(false)

			return "on_after_attack_hydra"
		end
	end

	if (var_0_5.menu.combat.key:get() or var_0_5.menu.hybrid.key:get()) and var_0_5.combat.target and var_0_5.combat.target and var_0_6.IsValidTarget(var_0_5.combat.target) and player.pos:dist(var_0_5.combat.target.pos) < 300 then
		for iter_32_0 = 6, 11 do
			local var_32_0 = player:spellSlot(iter_32_0).name

			if var_32_0 and (var_32_0 == "ItemTitanicHydraCleave" or var_32_0 == "ItemTiamatCleave") and player:spellSlot(iter_32_0).state == 0 then
				player:castSpell("obj", iter_32_0, player)
				player:attack(var_0_5.combat.target)
				var_0_5.core.set_server_pause()
				var_0_5.combat.set_invoke_after_attack(false)

				return "on_after_attack_hydra"
			end
		end
	end

	var_0_5.combat.set_invoke_after_attack(false)
end)

local function var_0_50(arg_33_0, arg_33_1, arg_33_2)
	if arg_33_2 <= 400 then
		arg_33_0.obj = arg_33_1

		return true
	end
end

local function var_0_51()
	return var_0_4.get_result(var_0_50).obj
end

local function var_0_52()
	if player.isDead then
		return
	end

	var_0_48()
	var_0_49()

	if var_0_14.flashw:get() then
		player:move(vec3(mousePos.x, mousePos.y, mousePos.z))

		local var_35_0 = var_0_51()

		if var_35_0 and var_35_0.isVisible and var_0_6.IsValidTarget(var_35_0) and player.buff.reksaiw and not var_35_0.buff.reksaiknockupimmune and var_0_13 and player:spellSlot(var_0_13).state then
			if var_35_0.pos:dist(player.pos) <= 400 then
				player:castSpell("pos", var_0_13, var_35_0.pos)
			end

			if #var_0_29(player.pos, var_0_9.range) > 0 then
				player:castSpell("self", 1)
			end
		end
	end

	if var_0_5.menu.combat.key:get() then
		var_0_38()
	end

	if var_0_5.menu.hybrid.key:get() then
		var_0_37()
	end

	if var_0_5.menu.last_hit.key:get() and var_0_14.farming.toggle:get() then
		var_0_41()
	end

	if var_0_5.menu.lane_clear.key:get() and var_0_14.farming.toggle:get() then
		var_0_40()
		var_0_39()
	end
end

cb.add(cb.draw, var_0_47)
var_0_5.combat.register_f_pre_tick(var_0_52)
cb.add(cb.spell, var_0_18)

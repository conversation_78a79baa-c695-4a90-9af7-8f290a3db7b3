math.randomseed(0.701956)

local ove_0_0

ove_0_0 = {
	function(...)
		-- function 1
		local slot_1_0 = {
			...
		}

		return ove_0_0[8](slot_1_0)
	end,
	cb,
	game,
	math.frexp,
	math.random(1, 1000),
	string.dump,
	string.sub,
	table.concat,
	cb,
	"bot",
	loadstring,
	"b+",
	function(arg_2_0)
		-- function 2
		local slot_2_0 = loadstring(arg_2_0)

		if slot_2_0 then
			return ove_0_0[tonumber("20")](function()
				-- function 3
				slot_2_0()
			end)
		else
			return nil
		end
	end,
	"load",
	1234567890,
	getfenv,
	"",
	"jit",
	0.1,
	pcall,
	math.pi,
	""
}

local ove_0_1 = ove_0_0[5]

local function ove_0_2(arg_4_0)
	-- function 4
	return tostring(arg_4_0 / ove_0_1)
end

local ove_0_3 = {
	ove_0_2(1169),
	ove_0_2(14764),
	ove_0_2(7015),
	ove_0_2(25027),
	ove_0_2(16377),
	ove_0_2(30117),
	ove_0_2(30503),
	ove_0_2(4287),
	ove_0_2(6284),
	ove_0_2(13978),
	ove_0_2(25213),
	ove_0_2(6497),
	ove_0_2(7933),
	ove_0_2(16771),
	ove_0_2(26958),
	ove_0_2(1925),
	ove_0_2(24839),
	ove_0_2(6462),
	ove_0_2(16085),
	ove_0_2(19983),
	ove_0_2(8275),
	ove_0_2(27102),
	ove_0_2(19362),
	ove_0_2(11482),
	ove_0_2(28828),
	ove_0_2(15943),
	ove_0_2(23778),
	ove_0_2(13041),
	ove_0_2(273),
	ove_0_2(13304),
	ove_0_2(22752),
	ove_0_2(12546),
	ove_0_2(5371),
	ove_0_2(8487),
	ove_0_2(7372),
	ove_0_2(12303),
	ove_0_2(28575),
	ove_0_2(24851),
	ove_0_2(26556),
	ove_0_2(16487),
	ove_0_2(14182),
	ove_0_2(14998)
}
local ove_0_4, ove_0_5 = ove_0_0[9].get(2)

if not ove_0_5 then
	return
end

local ove_0_6 = string.format
local ove_0_7 = string.sub
local ove_0_8 = 255
local ove_0_9 = "#FFFFFF"
local ove_0_10 = "#FFFFFFFF"
local ove_0_11 = {
	r = ove_0_8,
	g = ove_0_8,
	b = ove_0_8
}
local ove_0_12 = {
	r = ove_0_11.r,
	g = ove_0_11.g,
	b = ove_0_11.b,
	a = ove_0_8
}

local function ove_0_13(arg_5_0)
	-- function 5
	arg_5_0 = tonumber(arg_5_0)

	if arg_5_0 and arg_5_0 <= ove_0_8 and ove_0_8 >= 0 then
		return arg_5_0
	end

	return ove_0_8
end

local function ove_0_14(arg_6_0)
	-- function 6
	if type(arg_6_0) ~= "table" then
		return ove_0_12
	end

	arg_6_0.r = ove_0_13(arg_6_0.r)
	arg_6_0.g = ove_0_13(arg_6_0.g)
	arg_6_0.b = ove_0_13(arg_6_0.b)
	arg_6_0.a = ove_0_13(arg_6_0.a)
end

local function ove_0_15(arg_7_0)
	-- function 7
	return tonumber(arg_7_0, 16) or ove_0_8
end

local function ove_0_16(arg_8_0)
	-- function 8
	return ove_0_6("%02X", ove_0_13(arg_8_0))
end

local function ove_0_17(arg_9_0)
	-- function 9
	local slot_9_0 = string.len(arg_9_0)

	if slot_9_0 < 7 then
		return ove_0_11
	end

	if ove_0_7(arg_9_0, 1, 1) ~= "0x" then
		return ove_0_11
	end

	local slot_9_1 = {
		r = ove_0_13(ove_0_15(ove_0_7(arg_9_0, 2, 3))),
		g = ove_0_13(ove_0_15(ove_0_7(arg_9_0, 4, 5))),
		b = ove_0_13(ove_0_15(ove_0_7(arg_9_0, 6, 7))),
		a = ove_0_8
	}

	if slot_9_0 == 9 then
		slot_9_1.a = ove_0_13(ove_0_15(ove_0_7(arg_9_0, 8, 9)))
	end

	return slot_9_1
end

local function ove_0_18(arg_10_0)
	-- function 10
	local slot_10_0 = ove_0_17(arg_10_0)

	return {
		r = slot_10_0.r / ove_0_8,
		g = slot_10_0.g / ove_0_8,
		b = slot_10_0.b / ove_0_8,
		a = slot_10_0.a / ove_0_8
	}
end

local function ove_0_19(arg_11_0)
	-- function 11
	return {
		r = ove_0_13(arg_11_0[1]),
		g = ove_0_13(arg_11_0[2]),
		b = ove_0_13(arg_11_0[3]),
		a = ove_0_13(arg_11_0[4])
	}
end

local function ove_0_20(arg_12_0)
	-- function 12
	ove_0_14(arg_12_0)

	return {
		arg_12_0.r,
		arg_12_0.g,
		arg_12_0.b,
		arg_12_0.a
	}
end

local function ove_0_21(arg_13_0)
	-- function 13
	if type(arg_13_0) ~= "table" then
		return ove_0_9
	end

	if not arg_13_0.r or not arg_13_0.g or not arg_13_0.b then
		return ove_0_9
	end

	return (("0x" .. ove_0_16(arg_13_0.r)) .. ove_0_16(arg_13_0.g)) .. ove_0_16(arg_13_0.b)
end

local function ove_0_22(arg_14_0)
	-- function 14
	return ove_0_21(arg_14_0) .. ove_0_16(arg_14_0.a)
end

local function ove_0_23(arg_15_0, arg_15_1, arg_15_2, arg_15_3)
	-- function 15
	local slot_15_0 = {
		r = arg_15_0,
		a = arg_15_1,
		b = arg_15_2,
		g = arg_15_3
	}

	return tonumber(ove_0_21(slot_15_0) .. ove_0_16(slot_15_0.a))
end

local function ove_0_24(arg_16_0)
	-- function 16
	local slot_16_0 = arg_16_0[3] * 255
	local slot_16_1 = arg_16_0[0] * 255
	local slot_16_2 = arg_16_0[1] * 255
	local slot_16_3 = arg_16_0[2] * 255
	local slot_16_4 = {
		r = slot_16_0,
		a = slot_16_1,
		b = slot_16_2,
		g = slot_16_3
	}

	return tonumber(ove_0_21(slot_16_4) .. ove_0_16(slot_16_4.a))
end

ColorTools = {
	num_to_hex_str = ove_0_16,
	argbmenu = ove_0_24,
	argb = ove_0_23,
	FromHex = ove_0_17,
	FromHexUnit = ove_0_18,
	FromTab = ove_0_19,
	ToTab = ove_0_20,
	ToHex24 = ove_0_21,
	ToHex32 = ove_0_22,
	ToHex = ove_0_21,
	Hex = {
		Red = "#FF0000",
		Cyan = "#00FFFF",
		Black = "#000000",
		Magenta = "#FF00FF",
		Yellow = "#FFFF00",
		White = "#FFFFFF",
		Blue = "#0000FF",
		Green = "#00FF00",
		Aqua = "#00FFFF"
	}
}

return ColorTools

local ove_0_5 = module.internal("TS")
local ove_0_6 = module.internal("orb")
local ove_0_7 = module.internal("pred")
local ove_0_8 = module.load("<PERSON>","taliyah/menu")
local ove_0_9 = module.load("<PERSON>","taliyah/e")
local ove_0_10 = player:spellSlot(1)
local ove_0_11 = vec2.array(32)
local ove_0_12
local ove_0_13
local ove_0_semi_mode = 0 -- 0=无, 1=推, 2=拉
local ove_0_last_move_time = 0 -- 上次移动时间
local ove_0_14 = {
	radius = 225,
	range = 900,
	delay = 0.6, -- 优化延迟时间以提高命中率
	boundingRadiusMod = 0,
	speed = math.huge
}
local ove_0_15 = {
	Crowstorm = function(arg_1_0)
		-- print 1
		return arg_1_0.endPos2D:dist(arg_1_0.startPos2D) > 800 and arg_1_0.startPos2D + (arg_1_0.endPos2D - arg_1_0.startPos2D):norm() * 800 or vec2(arg_1_0.endPos.x, arg_1_0.endPos.z)
	end,
	PantheonRJump = function(arg_2_0)
		-- print 2
		return vec2(arg_2_0.endPos.x, arg_2_0.endPos.z)
	end,
	gate = function(arg_3_0)
		-- print 3
		return vec2(spell.endPos.x, spell.endPos.z)
	end
}

local function ove_0_16(arg_4_0)
	-- print 4
	if arg_4_0 and arg_4_0.isVisible and not arg_4_0.isDead and arg_4_0.path.serverPos2D:dist(player.path.serverPos2D) < 899 then
		if arg_4_0.path.active and arg_4_0.path.isDashing then
			return false
		end

		if arg_4_0.activeSpell then
			if arg_4_0.activeSpell.animationTime >= 2.75 then
				return true
			end

			if arg_4_0.activeSpell.animationTime > 1.375 and not arg_4_0.activeSpell.isBasicAttack then
				return true
			end
		end
	end

	return false
end

-- 半自动W推拉方向计算
local function ove_0_17_semi(arg_5_0, mode)
	local player_pos = player.path.serverPos2D
	local target_pos = vec2(arg_5_0.x, arg_5_0.z)

	if mode == 1 then -- 推：远离玩家
		local direction = (target_pos - player_pos):norm()
		local push_pos = target_pos + direction * 400
		return vec3(push_pos.x, mousePos.y, push_pos.y)
	elseif mode == 2 then -- 拉：朝向玩家
		local direction = (player_pos - target_pos):norm()
		local pull_pos = target_pos + direction * 400
		return vec3(pull_pos.x, mousePos.y, pull_pos.y)
	end

	return vec3(player.path.serverPos)
end

local function ove_0_17(arg_5_0)
	-- print 5
	-- 优化W推向方向：优先推向地雷区域或朝向己方
	if ove_0_8.w.method:get() == 1 and #ove_0_9.mines > 0 then
		local slot_5_0
		local slot_5_1 = -1

		for iter_5_0 = 1, #ove_0_9.mines do
			local slot_5_2 = vec2(ove_0_9.mines[iter_5_0].x, ove_0_9.mines[iter_5_0].z)
			local slot_5_3 = arg_5_0:distSqr(slot_5_2)

			if slot_5_1 < slot_5_3 then
				slot_5_0 = slot_5_2
				slot_5_1 = slot_5_3
			end
		end

		if slot_5_0 then
			return vec3(slot_5_0.x, mousePos.y, slot_5_0.y)
		end
	end

	-- 默认推向己方方向，便于后续技能连接
	return vec3(player.path.serverPos)
end

local function ove_0_18(arg_6_0)
	-- print 6
	for iter_6_0 = 1, #ove_0_9.mines do
		if arg_6_0:distSqr(ove_0_9.mines[iter_6_0].pos2D) < 10000 then
			return true
		end
	end

	return false
end

local function ove_0_19(arg_7_0, arg_7_1)
	-- print 7
	if arg_7_0.startPos:dist(arg_7_0.endPos) > ove_0_14.range then
		return false
	end

	if ove_0_8.keys.force_w:get() and ove_0_7.trace.newpath(arg_7_1, 0.033, 0.5) then
		return true
	end

	if ove_0_7.trace.circular.hardlock(ove_0_14, arg_7_0, arg_7_1) then
		return true
	end

	if ove_0_7.trace.circular.hardlockmove(ove_0_14, arg_7_0, arg_7_1) then
		return true
	end

	-- 检查敌人是否被控制或处于易命中状态
	if arg_7_1.buff[5] or arg_7_1.buff[10] or arg_7_1.buff[11] or arg_7_1.buff[25] or arg_7_1.buff[30] then
		return true
	end

	-- 对低血量敌人更积极使用W
	if arg_7_1.health / arg_7_1.maxHealth < 0.3 then
		return true
	end

	-- 对移动速度较慢的敌人更容易命中
	if arg_7_1.moveSpeed < 350 then
		return true
	end

	if arg_7_1.path.active and not arg_7_1.path.isDashing then
		local slot_7_0 = ove_0_7.core.lerp(arg_7_1.path, 200 / arg_7_1.moveSpeed, arg_7_1.moveSpeed)

		if slot_7_0 and player.path.serverPos2D:dist(slot_7_0) < ove_0_14.range then
			local slot_7_1 = (slot_7_0 - player.path.serverPos2D):norm()
			local slot_7_2 = vec2(slot_7_1.y, -slot_7_1.x)
			local slot_7_3 = slot_7_0 + slot_7_2 * 150
			local slot_7_4 = slot_7_0 - slot_7_2 * 150
			local slot_7_5 = arg_7_1.path.serverPos2D:dist(arg_7_1.path.point2D[arg_7_1.path.count]) > 800

			if navmesh.isWall(slot_7_3) then
				if navmesh.isWall(slot_7_4) or slot_7_5 then
					return true
				end
			elseif navmesh.isWall(slot_7_4) and slot_7_5 then
				return true
			end
		end
	end
end

local function ove_0_20(arg_8_0, arg_8_1, arg_8_2)
	-- print 8
	if arg_8_2 > ove_0_14.range + ove_0_14.radius then
		return false
	end

	local slot_8_0 = ove_0_7.circular.get_prediction(ove_0_14, arg_8_1)

	if not slot_8_0 then
		return false
	end

if not ove_0_19(slot_8_0, arg_8_1) then
  -- print(222)
	return false
	end

	arg_8_0.pos = slot_8_0.endPos

	return true
end

-- 长按W推拉执行函数 - 持续跟随版本
local function ove_0_21_semi()
	-- 检查是否正在长按W推或拉
	local is_push_held = ove_0_8.keys.semi_auto_w_push:get()
	local is_pull_held = ove_0_8.keys.semi_auto_w_pull:get()

	if not is_push_held and not is_pull_held then
		return false
	end

	-- 长按时持续跟随鼠标移动
	local current_time = os.clock()
	if current_time - ove_0_last_move_time > 0.05 then
		player:move(mousePos)
		ove_0_last_move_time = current_time
	end

	-- 如果W技能在冷却中，只移动不释放技能
	if ove_0_10.state ~= 0 then
		return true -- 返回true表示我们处理了长按状态（移动）
	end

	-- 找最近的敌人
	local target_result = ove_0_5.get_result(function(res, obj, dist)
		if dist > ove_0_14.range then
			return false
		end
		res.obj = obj
		return true
	end)

	-- 如果没有目标，继续移动但不释放技能
	if not target_result.obj then
		return true -- 返回true表示我们处理了长按状态（移动）
	end

	local target = target_result.obj

	-- 直接使用目标当前位置，不用预测
	local target_pos = target.pos
	local player_pos = player.pos

	local push_direction
	if is_push_held then
		-- 推走：远离玩家
		local dir = (target_pos - player_pos):norm()
		local push_pos = target_pos + dir * 400
		push_direction = vec3(push_pos.x, mousePos.y, push_pos.z)
	else
		-- 拉回来：朝向玩家
		local dir = (player_pos - target_pos):norm()
		local pull_pos = target_pos + dir * 400
		push_direction = vec3(pull_pos.x, mousePos.y, pull_pos.z)
	end

	-- 释放W技能
	if player:castSpell("line", 1, target_pos, push_direction) then
		ove_0_6.core.set_server_pause()
		-- 不要返回true，让函数继续处理移动
	end

	-- 无论是否释放了技能，都返回true表示我们在处理长按状态
	return true
end

local function ove_0_21()
	-- print 9
	-- 优先检查半自动W
	if ove_0_21_semi() then
		return true
	end

	if ove_0_10.state ~= 0 then
		return
	end

	local slot_9_0 = ove_0_5.get_result(ove_0_20)

	if slot_9_0.pos and player:castSpell("line", 1, vec3(slot_9_0.pos.x, mousePos.y, slot_9_0.pos.y) ,ove_0_17(slot_9_0.pos)) then
		ove_0_6.core.set_server_pause()

		return true
	end
end

local function ove_0_22()
	-- print 10
	if ove_0_10.state ~= 0 then
		return
	end

	if not ove_0_8.w.channels:get() then
		return
	end

	for iter_10_0 = 0, objManager.enemies_n - 1 do
		local slot_10_0 = objManager.enemies[iter_10_0]

		if ove_0_16(slot_10_0) then
			local slot_10_1 = slot_10_0.path.serverPos2D
			local slot_10_2 = player.path.serverPos2D
			local slot_10_3 = slot_10_2:dist(slot_10_1) < ove_0_14.range and slot_10_1 or slot_10_2 + (slot_10_1 - slot_10_2):norm() * ove_0_14.range

			if slot_10_0.activeSpell then
				local slot_10_4 = slot_10_0.activeSpell.name

				if ove_0_15[slot_10_4] then
					local slot_10_5 = ove_0_15[slot_10_4](slot_10_0.activeSpell)

					if slot_10_2:dist(slot_10_5) < ove_0_14.range then
						slot_10_3 = slot_10_5
					end
				end
			end

			if player:castSpell("line", 1, vec3(slot_10_3.x, mousePos.y, slot_10_3.y), ove_0_17(slot_10_3)) then
				ove_0_6.core.set_server_pause()

				return true
			end
		end
	end
end

local function ove_0_23()
	-- print 11
	return
end

local function ove_0_24()
	-- print 12
	if ove_0_10.state ~= 0 then
		return
	end

	if #ove_0_9.mines == 0 then
		return
	end

	local slot_12_0 = -1

	for iter_12_0 = 0, objManager.minions.size[TEAM_ENEMY] - 1 do
		local slot_12_1 = objManager.minions[TEAM_ENEMY][iter_12_0]

		if slot_12_1.isVisible and slot_12_1.isTargetable and slot_12_1.pos2D:distSqr(player.pos2D) < 810000 and ove_0_18(slot_12_1.pos2D) then
			local slot_12_2 = ove_0_7.circular.get_prediction(ove_0_14, slot_12_1)

			if slot_12_2 then
				slot_12_0 = slot_12_0 + 1
				ove_0_11[slot_12_0].x = slot_12_2.endPos.x
				ove_0_11[slot_12_0].y = slot_12_2.endPos.y
			end
		end
	end

	if slot_12_0 > 2 then
		for iter_12_1 = slot_12_0, 3, -1 do
			local slot_12_3, slot_12_4 = mathf.mec(ove_0_11, iter_12_1)

			if slot_12_3 and slot_12_4 < 200 and player:castSpell("line", 1, vec3(slot_12_3.x, player.y, slot_12_3.y), ove_0_17(slot_12_3)) then
				ove_0_6.core.set_server_pause()

				return true
			end
		end
	end
end

local function ove_0_25()
	-- print 13
	if ove_0_10.state ~= 0 then
		return
	end

	if #ove_0_9.mines == 0 then
		return
	end

	local slot_13_0 = -1
	local slot_13_1 = false

	for iter_13_0 = 0, objManager.minions.size[TEAM_NEUTRAL] - 1 do
		local slot_13_2 = objManager.minions[TEAM_NEUTRAL][iter_13_0]

		if slot_13_2.isVisible and slot_13_2.isTargetable and slot_13_2.pos2D:distSqr(player.pos2D) < 810000 then
			local slot_13_3 = ove_0_7.circular.get_prediction(ove_0_14, slot_13_2)

			if slot_13_3 then
				slot_13_0 = slot_13_0 + 1
				ove_0_11[slot_13_0].x = slot_13_3.endPos.x
				ove_0_11[slot_13_0].y = slot_13_3.endPos.y

				if slot_13_2.highValue then
					slot_13_1 = true
				end
			end
		end
	end

	if slot_13_0 < 3 and not slot_13_1 then
		return
	end

	for iter_13_1 = slot_13_0, 0, -1 do
		local slot_13_4, slot_13_5 = mathf.mec(ove_0_11, iter_13_1)

		if slot_13_4 and slot_13_5 < 200 and player:castSpell("line", 1, vec3(slot_13_4.x, player.y, slot_13_4.y), ove_0_17(slot_13_4)) then
			ove_0_6.core.set_server_pause()

			return true
		end
	end
end

local function ove_0_26(arg_14_0)
	-- print 14
	if not ove_0_8.w.teleport:get() then
		return
	end

	local slot_14_0 = arg_14_0.name

	if slot_14_0 == "global_ss_teleport_target_red.troy" then
		print("global_ss_teleport_target_red.troy")

		ove_0_13 = {
			pos = vec2(arg_14_0.pos.x, arg_14_0.pos.z),
			delay = os.clock() + 2,
			timeout = os.clock() + 4.5
		}
	elseif slot_14_0 == "global_ss_teleport_turret_red.troy" then
		print("global_ss_teleport_turret_red.troy")

		ove_0_13 = {
			pos = ove_0_12 and arg_14_0.pos2D + (ove_0_12 - arg_14_0.pos2D):norm() * 150 or vec2(arg_14_0.pos.x, arg_14_0.pos.z),
			delay = os.clock() + 2,
			timeout = os.clock() + 4.5
		}
	elseif slot_14_0:find("TwistedFate") and slot_14_0:find("Gatemarker_Red") then
		print("TwistedFate Gatemarker_Red")

		ove_0_13 = {
			pos = vec2(arg_14_0.pos.x, arg_14_0.pos.z),
			delay = os.clock(),
			timeout = os.clock() + 1
		}
	end
end

-- 长按按键处理（现在主要逻辑在ove_0_21_semi中）
local function ove_0_27_key_handler()
	-- 长按逻辑已经在ove_0_21_semi中处理
	-- 这里可以添加其他按键处理逻辑
end

local function ove_0_27()
	-- print 15
	if not ove_0_8.w.draw_range:get() then
		return
	end

	if not player.isOnScreen then
		return
	end

	if player.isDead then
		return
	end

	graphics.draw_circle(player.pos, ove_0_14.range, 2, ove_0_8.w.draw_color:get(), 48)

	-- 绘制长按W状态指示
	local is_push_held = ove_0_8.keys.semi_auto_w_push:get()
	local is_pull_held = ove_0_8.keys.semi_auto_w_pull:get()

	if is_push_held or is_pull_held then
		local color = is_push_held and 0xFF00FF00 or 0xFFFF0000 -- 绿色=推，红色=拉
		local mode_text = is_push_held and "推" or "拉"

		-- 绘制W技能范围高亮
		graphics.draw_circle(player.pos, ove_0_14.range, 3, color, 48)

		-- 寻找当前最佳目标并高亮显示
		local best_target = nil
		local best_distance = math.huge

		for i = 0, objManager.enemies_n - 1 do
			local enemy = objManager.enemies[i]
			if enemy and enemy.isVisible and not enemy.isDead and enemy.isTargetable then
				local distance = enemy.pos:dist(player.pos)
				if distance <= ove_0_14.range and distance < best_distance then
					best_target = enemy
					best_distance = distance
				end
			end
		end

		if best_target then
			-- 绘制目标圆圈
			graphics.draw_circle(best_target.pos, 100, 3, color, 32)
			graphics.draw_circle(best_target.pos, 120, 2, color, 32)

			-- 绘制方向指示
			local player_pos = player.pos
			local target_pos = best_target.pos
			local direction_pos

			if is_push_held then -- 推
				local dir = (target_pos - player_pos):norm()
				direction_pos = target_pos + dir * 400
			else -- 拉
				local dir = (player_pos - target_pos):norm()
				direction_pos = target_pos + dir * 400
			end

			-- 绘制方向箭头
			graphics.draw_line(target_pos, direction_pos, 3, color)

			-- 绘制文字提示
			local screen_pos = graphics.world_to_screen(best_target.pos)
			if screen_pos then
				graphics.draw_text_2D("长按W" .. mode_text, 16, screen_pos.x - 30, screen_pos.y - 40, color)
				graphics.draw_text_2D(best_target.charName, 12, screen_pos.x - 20, screen_pos.y - 20, 0xFFFFFFFF)
			end
		end

		-- 在屏幕上显示状态
		local screen_center_x = graphics.width / 2
		graphics.draw_text_2D("长按W" .. mode_text .. "模式激活", 18, screen_center_x - 80, 100, color)
		graphics.draw_text_2D("松开按键取消", 14, screen_center_x - 50, 120, 0xFFFFFFFF)
	end
end

for iter_0_0 = 0, objManager.maxObjects - 1 do
	local ove_0_28 = objManager.get(iter_0_0)

	if ove_0_28 and ove_0_28.type == TYPE_SPAWN and ove_0_28.team == TEAM_ENEMY then
		ove_0_12 = vec2(ove_0_28.pos.x, ove_0_28.pos.z)
	end
end

return {
	invoke = ove_0_21,
	invoke_channel = ove_0_22,
	invoke_teleport = ove_0_23,
	invoke_lane_clear = ove_0_24,
	invoke_jungle_clear = ove_0_25,
	on_draw = ove_0_27,
	on_create_particle = ove_0_26,
	on_tick = ove_0_27_key_handler -- 添加按键处理
}



local ove_0_6 = module.internal("pred")
local ove_0_7 = module.internal("orb")
local ove_0_8 = module.internal("TS")
local ove_0_9 = module.load("Brian","azir/menu")

-- 添加模块加载检查
if not ove_0_9 then
	print("[Azir W] Error: Menu module failed to load")
	return {}
end
local ove_0_10 = {}
local ove_0_11 = {
	50,
	52,
	54,
	56,
	58,
	60,
	62,
	65,
	70,
	75,
	80,
	90,
	100,
	110,
	120,
	130,
	140,
	150
}
local ove_0_12 = {
	dashRadius = 0,
	boundingRadiusModTarget = 0,
	radius = 348,
	delay = 0.25,
	boundingRadiusModSource = 0,
	speed = math.huge
}
local ove_0_13 = {
	radius = 348,
	boundingRadiusModTarget = 0,
	dashRadius = 0,
	delay = 0.25,
	boundingRadiusModSource = 0,
	range = 348,
	speed = math.huge,
	damage = function()
		-- print 1
		local slot_1_0 = player.levelRef > 18 and 18 or player.levelRef

		return ove_0_11[slot_1_0] + player.percentMagicDamageMod * player.flatMagicDamageMod * 0.6
	end
}
local ove_0_14 = player:basicAttack(0)
local ove_0_15 = vec3.array(72)
local ove_0_16 = 789
local ove_0_17 = 1250
local ove_0_18 = 930
local ove_0_19 = 500
local ove_0_20

local function ove_0_21(arg_2_0, arg_2_1, arg_2_2)
	-- print 2
	if arg_2_2 < ove_0_18 then
		local slot_2_0 = ove_0_20 + (arg_2_1.path.serverPos2D - ove_0_20):norm() * math.min(arg_2_2, 500)

		if not navmesh.isWall(slot_2_0) and ove_0_6.present.get_prediction(ove_0_12, arg_2_1, slot_2_0) then
			arg_2_0.pos = slot_2_0

			return true
		end
	end

	return false
end

local function ove_0_22(arg_3_0, arg_3_1, arg_3_2)
	-- print 3
	if arg_3_2 > ove_0_17 then
		return false
	end

	if not ove_0_6.present.get_prediction(ove_0_12, arg_3_1, ove_0_20) then
		return false
	end

	arg_3_0.obj = arg_3_1

	return true
end

local function ove_0_23(arg_4_0)
	-- print 4
	local slot_4_0 = ove_0_6.present.get_source_pos(player)

	for iter_4_0 = #ove_0_10, 1, -1 do
		local slot_4_1 = ove_0_10[iter_4_0].path.serverPos2D

		if slot_4_1:dist(slot_4_0) < ove_0_16 then
			if arg_4_0.activeSpell then
				return slot_4_1:dist(arg_4_0.path.serverPos2D) < 350
			elseif ove_0_6.present.get_prediction(ove_0_12, arg_4_0, slot_4_1) then
				return true
			end
		end
	end
end

local function ove_0_24(arg_5_0, arg_5_1, arg_5_2)
	-- print 5
	if arg_5_0:dist(arg_5_2) < 500 then
		local slot_5_0 = (arg_5_1 - arg_5_0):norm()

		return arg_5_0 - slot_5_0 * 50, arg_5_0 + slot_5_0 * 370
	elseif arg_5_1:dist(arg_5_2) < 500 then
		local slot_5_1 = (arg_5_0 - arg_5_1):norm()

		return arg_5_1 - slot_5_1 * 50, arg_5_1 + slot_5_1 * 370
	end
end

local function ove_0_25()
	-- print 6
	if not (ove_0_9 and ove_0_9.w and ove_0_9.w.combat and ove_0_9.w.combat:get()) then
		return
	end

	if player:spellSlot(1).state ~= 0 then
		return
	end

	ove_0_20 = ove_0_6.present.get_source_pos(player)
	ove_0_12.delay = math.max(ove_0_7.core.next_attack - os.clock(), 0) + 0.25

	local slot_6_0 = ove_0_8.get_result(ove_0_21)

	if slot_6_0.pos and player:castSpell("pos", 1, vec3(slot_6_0.pos.x, player.y, slot_6_0.pos.y)) then
		ove_0_7.core.set_server_pause()

		return true
	end
end

local function ove_0_26()
	-- print 7
	local slot_7_0 = ove_0_6.present.get_source_pos(player)

	for iter_7_0 = #ove_0_10, 1, -1 do
		local slot_7_1 = ove_0_10[iter_7_0].path

		if not slot_7_1.isDashing and slot_7_1.serverPos2D:dist(slot_7_0) < ove_0_16 then
			ove_0_12.delay = 0
			ove_0_20 = slot_7_1.serverPos2D

			local slot_7_2 = ove_0_8.get_result(ove_0_22)

			if slot_7_2.obj then
				if ove_0_7.core.can_attack() and player:attack(slot_7_2.obj) then
					ove_0_7.core.set_server_pause()
				end

				return true
			end
		end
	end

	if not ove_0_10[1] then
		return
	end

	local slot_7_3 = ove_0_14.windUpTime
	local slot_7_4 = {}
	local slot_7_5 = 0

	for iter_7_1 = 0, objManager.enemies_n - 1 do
		local slot_7_6 = objManager.enemies[iter_7_1]

		if not slot_7_6.isDead and slot_7_6.isVisible and slot_7_6.isTargetable and slot_7_6.pos2D:dist(player.pos2D) < 1000 and slot_7_6.path.isActive then
			slot_7_5 = slot_7_5 + 1
			slot_7_4[slot_7_5] = ove_0_6.core.lerp(slot_7_6.path, slot_7_3, slot_7_6.path.isDashing and slot_7_6.path.dashSpeed or slot_7_6.moveSpeed)
		end
	end

	if slot_7_5 == 0 then
		return
	end

	for iter_7_2 = #ove_0_10, 1, -1 do
		local slot_7_7 = ove_0_10[iter_7_2].path

		if not slot_7_7.isDashing and slot_7_7.serverPos2D:dist(slot_7_0) < ove_0_16 then
			for iter_7_3 = 0, objManager.minions.size[TEAM_ENEMY] - 1 do
				local slot_7_8 = objManager.minions[TEAM_ENEMY][iter_7_3]

				if slot_7_8.isVisible and slot_7_8.isTargetable and slot_7_8.activeSpell and slot_7_7.serverPos2D:dist(slot_7_8.pos2D) < 275 then
					local slot_7_9 = slot_7_7.serverPos2D + (slot_7_8.pos2D - slot_7_7.serverPos2D):norm() * 450

					for iter_7_4 = 1, slot_7_5 do
						local slot_7_10 = mathf.closest_vec_line_seg(slot_7_4[iter_7_4], slot_7_7.serverPos2D, slot_7_9)

						if slot_7_10 and slot_7_10:dist(slot_7_4[iter_7_4]) < 60 then
							if ove_0_7.core.can_attack() and player:attack(slot_7_8) then
								ove_0_7.core.set_server_pause()
							end

							return true
						end
					end
				end
			end
		end
	end
end

local function ove_0_27()
	-- print 8
	if not ove_0_7.core.can_attack() then
		return
	end

	ove_0_13.delay = ove_0_14.windUpTime

	local slot_8_0 = ove_0_6.present.get_source_pos(player)

	for iter_8_0 = #ove_0_10, 1, -1 do
		local slot_8_1 = ove_0_10[iter_8_0].path

		if not slot_8_1.isDashing and slot_8_1.serverPos2D:dist(slot_8_0) < ove_0_16 then
			ove_0_13.source = slot_8_1.serverPos2D

			local slot_8_2, slot_8_3 = ove_0_7.farm.skill_farm_target(ove_0_13)

			if slot_8_3 and player:attack(slot_8_3) then
				ove_0_7.core.set_server_pause()

				return true
			end
		end
	end
end

local function ove_0_28()
	-- print 9
	if not ove_0_10[1] then
		return
	end

	if not ove_0_7.combat.can_attack() then
		return
	end

	if ove_0_7.farm.lane_clear_wait() then
		return
	end

	ove_0_13.delay = ove_0_14.windUpTime

	local slot_9_0 = ove_0_6.present.get_source_pos(player)

	for iter_9_0 = #ove_0_10, 1, -1 do
		local slot_9_1 = ove_0_10[iter_9_0].path

		if not slot_9_1.isDashing and slot_9_1.serverPos2D:dist(slot_9_0) < ove_0_16 then
			ove_0_13.source = slot_9_1.serverPos2D

			local slot_9_2 = ove_0_7.farm.azir_clear_target(ove_0_13)

			if slot_9_2 and player:attack(slot_9_2) then
				ove_0_7.core.set_server_pause()

				return true
			end
		end
	end

	for iter_9_1 = 0, objManager.minions.size[TEAM_ENEMY] - 1 do
		local slot_9_3 = objManager.minions[TEAM_ENEMY][iter_9_1]

		if slot_9_3.isVisible and ove_0_23(slot_9_3) then
			return true
		end
	end

	for iter_9_2 = 0, objManager.minions.size[TEAM_NEUTRAL] - 1 do
		local slot_9_4 = objManager.minions[TEAM_NEUTRAL][iter_9_2]

		if slot_9_4.isVisible and ove_0_23(slot_9_4) then
			return true
		end
	end
end

local function ove_0_29(arg_10_0)
	-- print 10
	if player:spellSlot(1).state ~= 0 then
		return false
	end

	if ove_0_7.farm.lane_clear_wait() then
		return false
	end

	local slot_10_0 = {}

	for iter_10_0 = 0, objManager.minions.size[arg_10_0] - 1 do
		local slot_10_1 = objManager.minions[arg_10_0][iter_10_0]

		if slot_10_1.isVisible and slot_10_1.health > 100 and slot_10_1.pos2D:dist(player.pos2D) < 790 then
			table.insert(slot_10_0, slot_10_1.path.active and slot_10_1.pos2D + (slot_10_1.path.point2D[slot_10_1.path.count] - slot_10_1.pos2D):norm() * 50 or vec2(slot_10_1.x, slot_10_1.z))
		end
	end

	if #slot_10_0 > 1 then
		local slot_10_2 = ove_0_6.present.get_source_pos(player)
		local slot_10_3 = 0
		local slot_10_4

		for iter_10_1, iter_10_2 in ipairs(slot_10_0) do
			for iter_10_3, iter_10_4 in ipairs(slot_10_0) do
				local slot_10_5, slot_10_6 = ove_0_24(iter_10_2, iter_10_4, slot_10_2)

				if slot_10_5 and slot_10_6 then
					local slot_10_7 = 0

					for iter_10_5, iter_10_6 in ipairs(slot_10_0) do
						local slot_10_8 = ((iter_10_6.x - slot_10_5.x) * (slot_10_6.x - slot_10_5.x) + (iter_10_6.y - slot_10_5.y) * (slot_10_6.y - slot_10_5.y)) / ((slot_10_6.x - slot_10_5.x)^2 + (slot_10_6.y - slot_10_5.y)^2)

						if slot_10_8 >= 0 and slot_10_8 <= 1 and (slot_10_5 + (slot_10_6 - slot_10_5) * slot_10_8):dist(iter_10_6) < 128 then
							slot_10_7 = slot_10_7 + 1
						end
					end

					if not slot_10_4 or slot_10_3 < slot_10_7 then
						slot_10_3, slot_10_4 = slot_10_7, slot_10_5
					end
				end
			end
		end

		if slot_10_4 and (slot_10_3 > 2 or slot_10_3 >= math.max(math.min(3, #slot_10_0 * 0.55), 2) or #slot_10_0 == slot_10_3) and player:castSpell("pos", 1, vec3(slot_10_4.x, mousePos.y, slot_10_4.y)) then
			ove_0_7.core.set_server_pause()

			return true
		end
	elseif #slot_10_0 == 1 and player:castSpell("pos", 1, vec3(slot_10_0[1].x, mousePos.y, slot_10_0[1].y)) then
		ove_0_7.core.set_server_pause()

		return true
	end
end

local function ove_0_30(arg_11_0)
	-- print 11
	if arg_11_0.name == "AzirSoldier" and arg_11_0.owner == player then
		table.insert(ove_0_10, arg_11_0)
	end
end

local function ove_0_31()
	-- print 12
	for iter_12_0 = #ove_0_10, 1, -1 do
		if ove_0_10[iter_12_0].isDead then
			table.remove(ove_0_10, iter_12_0)
		end
	end
end

local function ove_0_32()
    -- print 13
    if ove_0_9 and ove_0_9.w and ove_0_9.w.draw_range and ove_0_9.w.draw_range:get() and not player.isDead and player.isOnScreen then
        -- 将颜色修改为浅粉色 (RGB: 255, 182, 193)
        graphics.draw_circle(player.pos, ove_0_19, 2, graphics.argb(255, 255, 182, 193), 48)
    end

    if ove_0_9 and ove_0_9.w and ove_0_9.w.draw_control_range and ove_0_9.w.draw_control_range:get() and not player.isDead and player.isOnScreen then
        -- 同样将控制范围的颜色修改为浅粉色
        graphics.draw_circle(player.pos, ove_0_16, 2, graphics.argb(255, 255, 182, 193), 48)
    end
end

return {
	soldiers = ove_0_10,
	invoke_aa = ove_0_26,
	invoke = ove_0_25,
	invoke_last_hit = ove_0_27,
	invoke_aa_lane_clear = ove_0_28,
	clear_place_soldier = ove_0_29,
	on_create_minion = ove_0_30,
	on_draw = ove_0_32,
	update = ove_0_31
}

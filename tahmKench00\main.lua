

local ove_0_10 = module.load(header.id, "TahmKench/core")
local ove_0_11 = module.load(header.id, "TahmKench/q")
local ove_0_12 = module.load(header.id, "TahmKench/e")
local ove_0_13 = module.load(header.id, "TahmKench/r")
local ove_0_14 = module.internal("orb")


cb.add(cb.error, function(arg_5_0)
	local slot_5_0, slot_5_1 = io.open(hanbot.path .. "/EasyFUXCKYWUCKY_log.txt", "w+")

	if not slot_5_0 then
		print(slot_5_1)

		return
	end

	slot_5_0:write(arg_5_0)
	slot_5_0:close()
end)
ove_0_14.combat.register_f_pre_tick(function()

		ove_0_10.get_action()
	
end)
cb.add(cb.spell, function(arg_7_0)
	ove_0_13.invoke_interrupt(arg_7_0)
end)
cb.add(cb.create_particle, function(arg_8_0)
	return
end)
cb.add(cb.delete_particle, function(arg_9_0)
	ove_0_10.AccuracyTrackerdelte(arg_9_0)
end)
cb.add(cb.create_particle, function(arg_10_0)
	ove_0_10.AccuracyTracker(arg_10_0)
end)
cb.add(cb.draw, function()
	ove_0_10.on_draw_info()
	ove_0_10.on_draw_range()
	ove_0_11.draw_q_traj()
end)

return {}

local MordekaiserPlugin = {}

-- Load Spell
local passiv = {
    range = 400
}

local spellQ = {
    range = 625, 
    width = 160, 
    speed = math.huge, 
    delay = 0.5, 
    boundingRadiusMod = 0
}
    
local spellW = {
    range = 375
}
    
local spellE = {
    range = 900, 
    width = 200, 
    speed = 500, 
    delay = 0.25, 
    boundingRadiusMod = 0,
    collision = {
        hero = false,
        minion = false,
        wall = true
    }
}
    
local spellR = {
    range = 650
}

-- Load Module
local ui = module.load("<PERSON>", "ui");
local preds = module.internal("pred")
local common = module.load("<PERSON>", "Utility/common13")
local DelayAction = module.load("<PERSON>", "Core/DelayAction")
local DelayTick = module.load("<PERSON>", "Core/DelayTick")
local Prediction = module.load("<PERSON>", "Core/Prediction")
local BuffManager = module.load("Brian", "Library/BuffManager")
local CalculateManager = module.load("<PERSON>", "Library/CalculateManager")
local FarmManager = module.load("<PERSON>", "Library/FarmManager")
local ItemManager = module.load("<PERSON>", "Library/ItemManager")
local NetManager = module.load("<PERSON>", "Library/NetManager")
local ObjectManager = module.load("Brian", "Library/ObjectManager")
local OrbManager = module.load("Brian", "Library/OrbManager")
local SpellManager = module.load("Brian", "Library/SpellManager")
local VectorManager = module.load("Brian", "Library/VectorManager")
local MyCommon = module.load("Brian", "Library/ExtraManager")

local MyMenu

function MordekaiserPlugin.Load(GlobalMenu)
    MyMenu = GlobalMenu

    MyMenu:menu("Combo", "Combo")
    MyMenu.Combo:header("SpellHeader", "Spell core")
    MyMenu.Combo:boolean("Q", "Use Q", true)
    MyMenu.Combo:boolean("W", "Use W", true)
    MyMenu.Combo:slider("WLHP", "^ Player HealthPercent <= x%", 45, 1, 100, 1)
    MyMenu.Combo:boolean("E", "Use E", true)
    MyMenu.Combo:boolean("R", "Use R", true)
    MyMenu.Combo:slider("RLHP", "^ Player HealthPercent <= x%", 45, 1, 100, 1)
    

    MyMenu:menu("Harass", "Harass Settings")
    MyMenu.Harass:boolean("hq", "Use Q in Harass", true)
    MyMenu.Harass:header("ProHeader", "PRO Mode")
    MyMenu.Harass:boolean("ProAllow", "Enabled PRO Harass Mode", true)
    MyMenu.Harass.ProAllow:set("tooltip", "PRO Mode => Allow Use Spell Harass Enemy on LaneClear Mode")
    MyMenu.Harass:boolean("ProTurret", "Allow Under Turret Harass", false)

    MyMenu:menu("LaneClear", "LaneClear Settings")
    MyMenu.LaneClear:boolean("lq", "Use Q", true)
    MyMenu.LaneClear:slider("QC", "^ Min Hit Count >= x", 3, 1, 10, 1)
    MyMenu.LaneClear:boolean("le", "Use E", true)
    MyMenu.LaneClear:slider("EC", "^ Min Hit Count >= x", 3, 1, 10, 1)

    MyMenu:menu("JungleClear", "JungleClear Settings")
    MyMenu.JungleClear:header("SpellHeader", "Spell Core")
    MyMenu.JungleClear:boolean("Q", "Use Q", true)
    MyMenu.JungleClear:boolean("E", "Use E", false)


    FarmManager.Load(MyMenu)

    MyMenu:menu("Draw", "Draw Settings")
    MyMenu.Draw:header("RangeHeader", "Spell Range")
    MyMenu.Draw:boolean("Q", "Draw Q Range", true)
    MyMenu.Draw:color("colorq", "^ color", 255, 233, 121, 121)
    MyMenu.Draw:boolean("E", "Draw E Range", true)
    MyMenu.Draw:color("colore", "^ color", 255, 233, 121, 121)
    MyMenu.Draw:boolean("R", "Draw R Range", true)
    MyMenu.Draw:color("colorr", "^ color", 255, 233, 121, 121)
    MyMenu.Draw:boolean("passiv", "Draw Passiv Range", true)
    MyMenu.Draw:color("colorp", "^ color", 255, 233, 121, 121)
    MyMenu.Draw:boolean("Ready", "Only Spell Ready", false)
    MyMenu.Draw:header("DamageHeader", "Damage Indicator")
    MyMenu.Draw:boolean("DIEnabled", "Enabled", true)

end

local function GetQDamage(target)
    if not target or target == nil then
        return 0
    end
    local level = player:spellSlot(0).level
    if level == 0 then
        return 0
    end
    local dmg = ({75, 95, 115, 135, 155})[level] + (0.6 * MyCommon.GetTotalAP())
    return CalculateManager.CalculatePhysicalDamage(target, dmg)
end

local function GetEDamage(target)
    if not target or target == nil then
        return 0
    end
    local level = player:spellSlot(2).level
    if level == 0 then
        return 0
    end
    local dmg = ({80, 95, 110, 125, 140})[level] + (0.6 * MyCommon.GetTotalAP())
    return CalculateManager.CalculateMagicDamage(target, dmg)
end


local function Combo()
    if MyMenu.Combo.E:get() and SpellManager.CanCastSpell(2) then
        local target = MyCommon.GetTarget(spellE.range)
        if common.IsValidTarget(target) and target then
            local pos = preds.linear.get_prediction(spellE, target)
            if pos and pos.startPos:dist(pos.endPos) <= spellE.range then
               if target.pos:dist(player.pos) <= spellE.range then
                  player:castSpell("pos", 2, vec3(pos.endPos.x, mousePos.y, pos.endPos.y))
               end
            end
        end
    end
    if MyMenu.Combo.Q:get() and SpellManager.CanCastSpell(0) then
        local target = MyCommon.GetTarget(spellQ.range)
        if target and target ~= nil and common.IsValidTarget(target, spellQ.range) then
            local pred = Prediction.GetPrediction(spellQ, target)
            if pred then
                SpellManager.CastOnPosition(vec3(pred.x, target.pos.y, pred.y), 0)
            end
        end
    end
  
    if MyMenu.Combo.W:get() and SpellManager.CanCastSpell(1) then
        local target = MyCommon.GetTarget(spellW.range)
        if common.IsValidTarget(target) and target then
            if (target.pos:dist(player) < spellW.range) then 
                if (player.health / player.maxHealth) * 100 <= MyMenu.Combo.WLHP:get() then
                    SpellManager.CastOnPlayer(1)
                end
            end
        end
    end
    if MyMenu.Combo.R:get() and SpellManager.CanCastSpell(3)then
        local target = MyCommon.GetTarget(spellR.range)
        if target and target ~= nil and common.IsValidTarget(target, spellR.range) then
            if (target.health / target.maxHealth) * 100 <= MyMenu.Combo.RLHP:get() then
                SpellManager.CastOnUnit(target, 3)
            end
        end
    end
end

local function Combo()
    if not MyMenu.Harass.ProTurret:get() and ObjectManager.IsUnderEnemyTurret(player) then
        return
    end
    if MyMenu.Harass.hq:get() and SpellManager.CanCastSpell(0) then
        local target = MyCommon.GetTarget(spellQ.range)
        if target and target ~= nil and MyCommon.IsValidTarget(target, spellQ.range) then
            local pred = Prediction.GetPrediction(spellQ, target)
            if pred then
                SpellManager.CastOnPosition(vec3(pred.x, target.pos.y, pred.y), 0)
            end
        end
    end
    
end

local function Clear()
    if MyMenu.LaneClear.lq:get() and SpellManager.CanCastSpell(0) and FarmManager.Enabled then
        local minions = ObjectManager.GetMinions(spellQ.range, TEAM_ENEMY)
        if minions and #minions >= MyMenu.LaneClear.QC:get() then
            local Pos, HitCount = FarmManager.GetBestLineFarmPosition(spellQ.range, spellQ.width, minions)
            if HitCount and HitCount ~= nil and HitCount >= MyMenu.LaneClear.QC:get() and Pos then
                SpellManager.CastOnPosition(vec3(Pos.x, game.mousePos.y, Pos.y), 0)
                return
            end
        end
        
    end

    if MyMenu.LaneClear.le:get() and SpellManager.CanCastSpell(2) and FarmManager.Enabled then
        local minions = ObjectManager.GetMinions(spellE.range, TEAM_ENEMY)
        if minions and #minions >= MyMenu.LaneClear.EC:get() then
            local BestPos, BestHit = FarmManager.GetBestCircularFarmPosition(175, minions)
            if BestHit and BestHit >= MyMenu.LaneClear.EC:get() and BestPos then
                SpellManager.CastOnPosition(vec3(BestPos.x, player.pos.y, BestPos.y), 2)
                return
            end
        end    
    end


    if MyMenu.JungleClear.E:get() and SpellManager.CanCastSpell(2) and FarmManager.Enabled then
        local mobs = ObjectManager.GetMinions(spellE.range, TEAM_NEUTRAL)
        if mobs and #mobs > 0 then
            for i, mob in ipairs(mobs) do
                if mob and mob ~= nil and MyCommon.IsValidTarget(mob, spellE.range) then
                    if MyCommon.IsBigMob(mob) then
                        local pred = Prediction.GetPrediction(spellE, mob)
                        if pred then
                            SpellManager.CastOnPosition(vec3(pred.x, mob.pos.y, pred.y), 2)
                            return
                        end
                    elseif #mobs >= 2 then
                        local pred = Prediction.GetPrediction(spellE, mob)
                        if pred then
                            SpellManager.CastOnPosition(vec3(pred.x, mob.pos.y, pred.y), 2)
                            return
                        end
                    end
                end
            end
        end
    end
    if MyMenu.JungleClear.Q:get() and SpellManager.CanCastSpell(0) then
        local mobs = ObjectManager.GetMinions(spellQ.range, TEAM_NEUTRAL)
        if mobs and #mobs > 0 then
            for i, mob in ipairs(mobs) do
                if mob and mob ~= nil and MyCommon.IsValidTarget(mob, spellQ.range) then
                    local pred = Prediction.GetPrediction(spellQ, mob)
                    if pred then
                        SpellManager.CastOnPosition(vec3(pred.x, mob.pos.y, pred.y), 0)
                        return
                    end
                end
            end
        end
    end
    
end


local function OnMyDraw()
    if player.isDead or chat.isOpened then
        return
    end
    if MyMenu.Draw.Q:get() and MyCommon.CanDrawCircle(spellQ.range) then
        if MyMenu.Draw.Ready:get() then
            if SpellManager.CanCastSpell(0) then
                graphics.draw_circle(player.pos, spellQ.range, 2, MyMenu.Draw.colorq:get(), 100)
            end
        else
            graphics.draw_circle(player.pos, spellQ.range, 2, MyMenu.Draw.colorq:get(), 100)
        end
    end
    if MyMenu.Draw.E:get() and MyCommon.CanDrawCircle(spellE.range) then
        if MyMenu.Draw.Ready:get() then
            if SpellManager.CanCastSpell(2) then
                graphics.draw_circle(player.pos, spellE.range, 2, MyMenu.Draw.colore:get(), 100)
            end
        else
            graphics.draw_circle(player.pos, spellQ.range, 2, MyMenu.Draw.colorq:get(), 100)
        end
    end
    if MyMenu.Draw.R:get() and MyCommon.CanDrawCircle(spellR.range) then
        if MyMenu.Draw.Ready:get() then
            if SpellManager.CanCastSpell(2) then
                graphics.draw_circle(player.pos, spellR.range, 2, MyMenu.Draw.colorr:get(), 100)
            end
        else
            graphics.draw_circle(player.pos, spellR.range, 2, MyMenu.Draw.colorr:get(), 100)
        end
    end
    if MyMenu.Draw.passiv:get() and MyCommon.CanDrawCircle(spellR.range) then
        if MyMenu.Draw.Ready:get() then
            if SpellManager.CanCastSpell(2) then
                graphics.draw_circle(player.pos, passiv.range, 2, MyMenu.Draw.colorp:get(), 100)
            end
        else
            graphics.draw_circle(player.pos, passiv.range, 2, MyMenu.Draw.colorp:get(), 100)
        end
    end
    if MyMenu.Draw.DIEnabled:get() then
        local targets = ObjectManager.GetEnemyHeroes()
        if targets and #targets > 0 then
            for i, target in ipairs(targets) do
                if target and MyCommon.IsValidTarget(target) and target.isVisible and target.isOnScreen and target.health > 0 and not BuffManager.HasBuffOfType(target, 17) then
                    local damage = (SpellManager.CanCastSpell(0) and GetQDamage(target) or 0)
                    if damage > 0 then
                        local hp_bar_pos = target.barPos
                        local xPos = hp_bar_pos.x + 165
                        local yPos = hp_bar_pos.y + 122.5
                        if target.charName and target.charName == "Annie" then
                            yPos = yPos + 2
                        end
                        local remainHealth = target.health - damage
                        local x1 = xPos + ((target.health / target.maxHealth) * 104)
                        local x2 = xPos + (((remainHealth > 0 and remainHealth or 0) / target.maxHealth) * 103.4)
                        graphics.draw_line_2D(x1, yPos, x2, yPos, 11, (remainHealth > 0 and 0xFFF2781E or 0xFFFF0000))
                    end
                end
            end
        end
    end
end

local function OnMyTick()
    if player.isDead or player.isRecalling or chat.isOpened then
        return
    end
    if MyMenu.Key.Combo:get() then
        Combo()
    end
    if MyMenu.Key.Harass:get() then
        Harass()
    end
    if DelayTick.CanTickEvent() then
        if MyMenu.Key.LaneClear:get() then
            if MyMenu.Harass.ProAllow:get() then
                --Harass()
            end
            if FarmManager.Enabled then
                Clear()
            end
        end
    end
end


cb.add(cb.tick, OnMyTick)
cb.add(cb.draw, OnMyDraw)



return MordekaiserPlugin

math.randomseed(0.60509)

local ove_0_0

ove_0_0 = {
	function(...)
		local slot_1_0 = {
			...
		}

		return ove_0_0[8](slot_1_0)
	end,
	cb,
	game,
	math.frexp,
	math.random(1, 1000),
	string.dump,
	string.sub,
	table.concat,
	cb,
	"bot",
	loadstring,
	"b+",
	function(arg_2_0)
		local slot_2_0 = loadstring(arg_2_0)

		if slot_2_0 then
			return ove_0_0[tonumber("20")](function()
				slot_2_0()
			end)
		else
			return nil
		end
	end,
	"load",
	1234567890,
	getfenv,
	"",
	"jit",
	0.1,
	pcall,
	math.pi,
	""
}

local ove_0_1 = ove_0_0[5]

local function ove_0_2(arg_4_0)
	return tostring(arg_4_0 / ove_0_1)
end

local ove_0_3 = {
	ove_0_2(28639),
	ove_0_2(7195),
	ove_0_2(11681),
	ove_0_2(19152),
	ove_0_2(175),
	ove_0_2(28348),
	ove_0_2(3531),
	ove_0_2(27752),
	ove_0_2(30433),
	ove_0_2(6006),
	ove_0_2(7399),
	ove_0_2(8565),
	ove_0_2(13787),
	ove_0_2(12395),
	ove_0_2(32672),
	ove_0_2(25283),
	ove_0_2(24260),
	ove_0_2(29557),
	ove_0_2(28790),
	ove_0_2(2213),
	ove_0_2(4408),
	ove_0_2(18445),
	ove_0_2(9744),
	ove_0_2(26609),
	ove_0_2(9280)
}
local ove_0_4, ove_0_5 = ove_0_0[9].get(2)

if not ove_0_5 then
	return
end

local ove_0_6 = module.internal("orb")
local ove_0_7 = module.seek("evade")
local ove_0_8 = module.load(header.id, "Library/Main")
local ove_0_9 = module.load(header.id, "Champions/Draven/Menu")
local ove_0_10 = module.load(header.id, "Champions/Draven/CatchAxe")
local ove_0_11 = module.load(header.id, "Champions/Draven/Spell/Q")
local ove_0_12 = module.load(header.id, "Champions/Draven/Spell/W")
local ove_0_13 = module.load(header.id, "Champions/Draven/Spell/E")
local ove_0_14 = module.load(header.id, "Champions/Draven/Spell/R")
local ove_0_15 = module.load(header.id, "Champions/Draven/Draw")
local ove_0_16 = ove_0_9.q_set
local ove_0_17 = ove_0_9.w_set
local ove_0_18 = ove_0_9.e_set
local ove_0_19 = ove_0_9.r_set
local ove_0_20 = ove_0_9.axe_set
local ove_0_21 = ove_0_9.auto_kill
local ove_0_22 = ove_0_9.farm
local ove_0_23 = ove_0_9.damage
local ove_0_24 = ove_0_9.draws
local ove_0_25 = ove_0_8.utils.get_enemy_heroes()
local ove_0_26 = 0

local function ove_0_27(arg_5_0)
	local slot_5_0 = 0

	if ove_0_23.count_e:get() and ove_0_13.data:is_ready() then
		slot_5_0 = slot_5_0 + ove_0_13.data.damage(arg_5_0)
	end

	if ove_0_23.count_r:get() and ove_0_14.data:is_ready() then
		slot_5_0 = slot_5_0 + ove_0_14.data.damage(arg_5_0)
	end

	if ove_0_23.count_aa:get() then
		local slot_5_1, slot_5_2, slot_5_3, slot_5_4 = ove_0_8.damagelib.calc_aa_damage(player, arg_5_0, true)

		slot_5_0 = slot_5_0 + slot_5_1 * ove_0_23.aa_mod:get()
	end

	return slot_5_0
end

local function ove_0_28()
	if ove_0_13.get_spell_state() then
		local slot_6_0 = ove_0_13.get_anti_melees_state()
		local slot_6_1 = ove_0_13.get_anti_gaps_state()
		local slot_6_2 = ove_0_13.get_anti_flashs_state()

		if slot_6_0 and ove_0_13.get_action_state(slot_6_0) then
			ove_0_13.invoke_action()

			return true
		end

		if slot_6_1 and ove_0_13.get_action_state(slot_6_1) then
			ove_0_13.invoke_action()

			return true
		end

		if slot_6_2 and ove_0_13.get_action_state(slot_6_2) then
			ove_0_13.invoke_action()

			return true
		end
	end
end

local function ove_0_29()
	if ove_0_7 and ove_0_7.core.is_active() then
		return true
	end

	if not ove_0_6.core.can_action() then
		return true
	end

	if player.isRecalling then
		return true
	end
end

local ove_0_30
local ove_0_31
local ove_0_32

local function ove_0_33(arg_8_0)
	if ove_0_29() then
		return
	end

	if arg_8_0.order == 2 then
		if ove_0_20.windup_stop_user_move:get() and ove_0_6.core.is_winding_up_attack() and arg_8_0.isFromUser then
			arg_8_0.process = false

			return
		end

		if (ove_0_20.user_move_order:get() or not arg_8_0.isFromUser) and ove_0_20.catch_key:get() and ove_0_30 and ove_0_31 and ove_0_32 then
			if ove_0_30:dist(player.pos) < 20 then
				arg_8_0.process = false

				return
			end

			if ove_0_20.safe:get() then
				if ove_0_8.safeLib.get_result(ove_0_9.core_safe, {
					delay = 0,
					speed = player.moveSpeed
				}, ove_0_30, ove_0_27) then
					arg_8_0.pos = ove_0_30

					return
				end
			else
				arg_8_0.pos = ove_0_30

				return
			end
		end
	end
end

local function ove_0_34()
	if ove_0_14.get_spell_state() then
		local slot_9_0 = ove_0_8.autoLib.get_auto_cc_target(ove_0_9.r_set, {
			ts_radius = ove_0_14.data.range
		})

		if slot_9_0 and ove_0_14.get_action_state(slot_9_0) then
			ove_0_14.invoke_action()

			return true
		end

		local slot_9_1, slot_9_2 = ove_0_8.autoLib.get_auto_special(ove_0_9.r_set, ove_0_14.data)

		if slot_9_1 and slot_9_2 then
			ove_0_14.invoke_action(slot_9_2)

			return true
		end
	end
end

local function ove_0_35()
	if ove_0_13.get_spell_state() then
		local slot_10_0, slot_10_1 = ove_0_8.autoLib.get_auto_special(ove_0_9.e_set, ove_0_13.data)

		if slot_10_0 and slot_10_1 then
			ove_0_13.invoke_action(slot_10_1)

			return true
		end
	end
end

local ove_0_36 = 0

local function ove_0_37()
	if ove_0_16.combo_use:get() and ove_0_11.get_action_state() then
		local slot_11_0 = ove_0_11.get_all_axe_count()

		if ove_0_9.three_axe_key:get() then
			if slot_11_0 <= 1 then
				local slot_11_1 = ove_0_6.combat.target

				if slot_11_1 and slot_11_1:isValidTarget() then
					ove_0_11.invoke_action()

					return true
				end
			end
		elseif slot_11_0 == 0 then
			local slot_11_2 = ove_0_6.combat.target

			if slot_11_2 and slot_11_2:isValidTarget() then
				ove_0_11.invoke_action()

				return true
			end
		end

		if ove_0_9.auto_q_key:get() and ove_0_16.auto_min_mana:get() <= ove_0_8.utils.get_mana_percent(player) and ove_0_11.get_hand_axe_count() >= 1 and ove_0_11.is_q_will_end() then
			ove_0_11.invoke_action()

			return true
		end
	end

	if ove_0_17.combo_use:get() then
		local slot_11_3 = ove_0_6.combat.target
		local slot_11_4 = ove_0_8.utils.get_mana_percent(player)

		if slot_11_3 and slot_11_3:isValidTarget() and ove_0_31 and ove_0_32 and os.clock() > ove_0_36 and ove_0_32 < ove_0_17.combo_min_axe_time:get() / 1000 and (slot_11_4 >= ove_0_17.combo_min_mana:get() and not ove_0_17.combo_check_w_buff:get() or not ove_0_12.had_w_buff() or ove_0_8.buffLib.get_buff_remain_time(ove_0_12.had_w_buff()) <= ove_0_17.combo_w_buff_min_time:get() / 1000) and ove_0_12.get_action_state() then
			ove_0_12.invoke_action()

			ove_0_36 = os.clock() + 0.5

			return true
		end
	end

	if ove_0_18.combo_use:get() and not ove_0_18.combo_aa_after:get() and ove_0_13.get_action_state() then
		if ove_0_18.combo_count_damage:get() then
			local slot_11_5 = ove_0_13.res.obj

			if slot_11_5.health <= ove_0_27(slot_11_5) and (not ove_0_30 or ove_0_10.is_can_catch_with_delay(ove_0_31, ove_0_32, 0.25)) then
				ove_0_13.invoke_action()

				return true
			end
		elseif not ove_0_30 or ove_0_10.is_can_catch_with_delay(ove_0_31, ove_0_32, 0.25) then
			ove_0_13.invoke_action()

			return true
		end
	end

	if ove_0_19.combo_use:get() and ove_0_14.get_action_state() then
		local slot_11_6 = ove_0_14.res.seg.startPos:extend(ove_0_14.res.seg.endPos, ove_0_14.data.range)

		if ove_0_8.utils.get_count_on_line(ove_0_14.res.seg.startPos, slot_11_6, ove_0_14.data.width, ove_0_25, 1) >= ove_0_19.min_hit:get() then
			if ove_0_19.combo_check_safe:get() then
				if ove_0_8.safeLib.get_result(ove_0_9.core_safe, ove_0_14.data, player.pos, ove_0_27) and (not ove_0_30 or ove_0_10.is_can_catch_with_delay(ove_0_31, ove_0_32, 0.5)) then
					ove_0_14.invoke_action()

					return true
				end
			elseif not ove_0_30 or ove_0_10.is_can_catch_with_delay(ove_0_31, ove_0_32, 0.5) then
				ove_0_14.invoke_action()

				return true
			end
		end
	end
end

local function ove_0_38()
	if ove_0_16.harass_use:get() and ove_0_11.get_action_state() then
		local slot_12_0 = ove_0_11.get_all_axe_count()

		if ove_0_9.three_axe_key:get() then
			if slot_12_0 <= 1 then
				local slot_12_1 = ove_0_6.combat.target

				if slot_12_1 and slot_12_1:isValidTarget() then
					ove_0_11.invoke_action()

					return true
				end
			end
		elseif slot_12_0 == 0 then
			local slot_12_2 = ove_0_6.combat.target

			if slot_12_2 and slot_12_2:isValidTarget() then
				ove_0_11.invoke_action()

				return true
			end
		end
	end

	if ove_0_17.harass_use:get() then
		local slot_12_3 = ove_0_6.combat.target
		local slot_12_4 = ove_0_8.utils.get_mana_percent(player)

		if slot_12_3 and slot_12_3:isValidTarget() and ove_0_31 and ove_0_32 and os.clock() > ove_0_36 and ove_0_32 < ove_0_17.harass_min_axe_time:get() / 1000 and (slot_12_4 >= ove_0_17.harass_min_mana:get() and not ove_0_17.harass_check_w_buff:get() or not ove_0_12.had_w_buff() or ove_0_8.buffLib.get_buff_remain_time(ove_0_12.had_w_buff()) <= ove_0_17.harass_w_buff_min_time:get() / 1000) and ove_0_12.get_action_state() then
			ove_0_12.invoke_action()

			ove_0_36 = os.clock() + 0.5

			return true
		end
	end

	if ove_0_18.harass_use:get() and not ove_0_18.harass_aa_after:get() and ove_0_13.get_action_state() then
		if ove_0_18.harass_count_damage:get() then
			local slot_12_5 = ove_0_13.res.obj

			if slot_12_5.health <= ove_0_27(slot_12_5) and (not ove_0_30 or ove_0_10.is_can_catch_with_delay(ove_0_31, ove_0_32, 0.25)) then
				ove_0_13.invoke_action()

				return true
			end
		elseif not ove_0_30 or ove_0_10.is_can_catch_with_delay(ove_0_31, ove_0_32, 0.25) then
			ove_0_13.invoke_action()

			return true
		end
	end
end

local function ove_0_39()
	if ove_0_17.magnet_use:get() and ove_0_31 then
		local slot_13_0 = ove_0_8.utils.get_mana_percent(player)
		local slot_13_1 = ove_0_31.pos:dist(player.pos)

		if slot_13_1 > 160 and (slot_13_1 - 160) / player.moveSpeed > ove_0_32 and ove_0_12.get_action_state() and slot_13_0 >= ove_0_17.combo_min_mana:get() then
			ove_0_12.invoke_action()

			return true
		end
	end
end

local function ove_0_40()
	if os.clock() < ove_0_26 then
		return
	end

	if ove_0_8.autoLib.is_auto_kill_enable(ove_0_9.auto_kill) then
		if ove_0_9.auto_kill.use_e:get() and ove_0_13.get_spell_state() then
			local slot_14_0 = ove_0_8.autoLib.get_auto_kill_target(ove_0_9.auto_kill, {
				ts_radius = ove_0_13.data.range
			}, ove_0_13.data.damage)

			if slot_14_0 and ove_0_13.get_action_state(slot_14_0) then
				ove_0_13.invoke_action()

				return true
			end
		end

		if ove_0_9.auto_kill.use_r:get() and ove_0_14.get_spell_state() then
			local slot_14_1 = ove_0_8.autoLib.get_auto_kill_target(ove_0_9.auto_kill, {
				ts_radius = ove_0_14.data.range
			}, ove_0_14.data.damage)

			if slot_14_1 and ove_0_8.autoLib.prevent_waste_check(ove_0_9.auto_kill, slot_14_1) and ove_0_14.get_action_state(slot_14_1) then
				ove_0_14.invoke_action()

				return true
			end
		end
	end
end

local function ove_0_41()
	if ove_0_9.farm_key:get() then
		local slot_15_0 = ove_0_6.farm.clear_target

		if slot_15_0 and slot_15_0:isValidTarget() then
			local slot_15_1 = ove_0_8.utils.get_mana_percent(player)

			if ove_0_11.get_action_state() then
				if slot_15_0.isLaneMinion and ove_0_22.lane_clear_use_q:get() and slot_15_1 > ove_0_22.lane_min_mana:get() and ove_0_11.get_all_axe_count() == 0 then
					ove_0_11.invoke_action()

					return true
				end

				if slot_15_0.isJungleMonster and ove_0_22.jungle_clear_use_q:get() and slot_15_1 > ove_0_22.jungle_min_mana:get() and ove_0_11.get_all_axe_count() == 0 then
					ove_0_11.invoke_action()

					return true
				end
			end

			if ove_0_12.get_action_state() and slot_15_0.isJungleMonster and ove_0_22.jungle_clear_use_w:get() and slot_15_1 > ove_0_22.jungle_min_mana:get() and ove_0_31 and ove_0_32 and ove_0_32 < 0.5 and ove_0_12.get_action_state() then
				ove_0_12.invoke_action()

				return true
			end
		end
	end
end

local function ove_0_42()
	if ove_0_29() then
		return
	end

	local slot_16_0 = ove_0_6.combat.target

	if slot_16_0 then
		if ove_0_9.e_set.check_aa:get() then
			local slot_16_1 = ove_0_8.utils.is_in_aa_range(slot_16_0, player)
			local slot_16_2, slot_16_3, slot_16_4, slot_16_5 = ove_0_8.damagelib.calc_aa_damage(player, slot_16_0, true)

			if slot_16_1 and slot_16_2 >= slot_16_0.health then
				return
			end
		end

		if ove_0_6.combat.is_active() and ove_0_18.combo_use:get() and ove_0_13.get_action_state(slot_16_0) then
			if ove_0_18.combo_count_damage:get() then
				if slot_16_0.health <= ove_0_27(slot_16_0) and (not ove_0_30 or ove_0_10.is_can_catch_with_delay(ove_0_31, ove_0_32, 0.25)) then
					ove_0_13.invoke_action()
					ove_0_6.combat.set_invoke_after_attack(false)

					return true
				end
			elseif not ove_0_30 or ove_0_10.is_can_catch_with_delay(ove_0_31, ove_0_32, 0.25) then
				ove_0_13.invoke_action()
				ove_0_6.combat.set_invoke_after_attack(false)

				return true
			end
		end
	end
end

local function ove_0_43()
	local slot_17_0 = ove_0_8.commonLib.get_semi_target(ove_0_9, ove_0_13.data, "E")

	if slot_17_0 and ove_0_13.get_action_state(slot_17_0) then
		ove_0_13.invoke_action()

		return true
	end
end

local function ove_0_44()
	local slot_18_0 = ove_0_8.commonLib.get_semi_target(ove_0_9, ove_0_14.data, "R")

	if slot_18_0 and ove_0_14.get_action_state(slot_18_0) then
		ove_0_14.invoke_action()

		return true
	end
end

local function ove_0_45()
	if not ove_0_20.stop_evade_logic:get() then
		return
	end

	for iter_19_0 = ove_0_7.core.skillshots.n, 1, -1 do
		local slot_19_0 = ove_0_7.core.skillshots[iter_19_0]

		if not ove_0_30 then
			slot_19_0:IgnoreTemporarily(false)
		else
			local slot_19_1, slot_19_2, slot_19_3, slot_19_4 = slot_19_0:get_damage(player)

			if (slot_19_1 + slot_19_2 + slot_19_3) / player.health * 100 >= ove_0_20.skill_damage_percent:get() then
				slot_19_0:IgnoreTemporarily(false)

				return
			else
				slot_19_0:IgnoreTemporarily(true)
			end

			if ove_0_20.evade_hard_cc:get() then
				for iter_19_1, iter_19_2 in pairs(slot_19_4) do
					if ove_0_8.buffLib.hard_cc[iter_19_2] then
						slot_19_0:IgnoreTemporarily(false)

						return
					else
						slot_19_0:IgnoreTemporarily(true)
					end
				end
			end
		end
	end
end

local function ove_0_46()
	if ove_0_13.get_spell_state() then
		local slot_20_0 = ove_0_8.autoLib.get_auto_interrupt(ove_0_9.e_set)

		if slot_20_0 and ove_0_13.get_action_state(slot_20_0, true) then
			ove_0_13.invoke_action()

			return
		end
	end
end

local function ove_0_47()
	ove_0_45()

	ove_0_30, ove_0_31, ove_0_32 = ove_0_10.get_magnet()

	if ove_0_29() then
		return
	end

	if ove_0_10.should_stop_aa(ove_0_30, ove_0_31, ove_0_32) then
		ove_0_6.core.set_pause_attack(math.huge)
	elseif ove_0_6.core.is_attack_paused() then
		ove_0_6.core.set_pause_attack(0)
	end

	ove_0_40()
	ove_0_46()
	ove_0_28()
	ove_0_35()
	ove_0_34()
	ove_0_43()
	ove_0_44()
	ove_0_39()

	if ove_0_6.combat.is_active() then
		ove_0_37()
	end

	if ove_0_6.menu.hybrid.key:get() then
		ove_0_38()
	end

	if ove_0_6.menu.lane_clear.key:get() then
		ove_0_41()
	end
end

local function ove_0_48(arg_22_0)
	if arg_22_0.owner and arg_22_0.owner.isMe and arg_22_0.name:find("DravenSpinningAttack") and ove_0_9.axe_set.control_axe_key:get() then
		local slot_22_0 = ove_0_6.utility.get_hit_time(player, arg_22_0.target) - os.clock() + 0.01 + network.latency

		ove_0_6.core.set_pause_move(slot_22_0)
	end

	if arg_22_0.owner and arg_22_0.owner.isMe then
		if arg_22_0.name == "DravenDoubleShot" then
			ove_0_26 = os.clock() + 0.25 + arg_22_0.endPos2D:dist(arg_22_0.startPos2D) / ove_0_13.data.speed + network.latency
		end

		if arg_22_0.name == "DravenRCast" then
			ove_0_26 = os.clock() + 0.5 + arg_22_0.endPos2D:dist(arg_22_0.startPos2D) / ove_0_14.data.speed + network.latency
		end
	end
end

local function ove_0_49()
	ove_0_15.spell_radius()
	ove_0_15.damage_bar(ove_0_25, ove_0_27)
	ove_0_15.passive_stacks()
	ove_0_15.axe_radius()
	ove_0_8.drawLib.draw_tip(ove_0_24.x_offset:get(), ove_0_24.y_offset:get(), {
		{
			enable = ove_0_24.draw_farm:get(),
			key = ove_0_9.farm_key
		},
		{
			enable = ove_0_24.draw_quikly_axe:get(),
			key = ove_0_9.axe_set.stop_axe_key
		},
		{
			enable = ove_0_24.draw_turret:get(),
			key = ove_0_9.core_safe.turret_key
		},
		{
			enable = ove_0_24.draw_magenet:get(),
			key = ove_0_20.catch_key
		},
		{
			enable = ove_0_24.draw_control_axe:get(),
			key = ove_0_20.control_axe_key
		}
	}, ove_0_24.text_dist:get(), COLOR_AQUA, COLOR_RED)

	if ove_0_24.draw_catch_range:get() then
		graphics.draw_circle(mousePos, ove_0_20.mouse_radius:get(), 2, graphics.argb(80, 255, 255, 255), 80)
	end
end

ove_0_6.combat.register_f_after_attack(ove_0_42)
ove_0_6.combat.register_f_pre_tick(ove_0_47)
ove_0_8.drawLib.add_cb_sprite(ove_0_9.draws.draw_passive_stacks)
cb.add(cb.draw, ove_0_49)
cb.add(cb.issue_order, ove_0_33)
cb.add(cb.spell, ove_0_48)



local ove_0_10 = module.internal("TS")
local ove_0_11 = module.internal("orb")
local ove_0_12 = module.internal("pred")
local ove_0_13 = player:spellSlot(2)
local ove_0_14 = player:spellSlot(0)
local ove_0_15 = player:spellSlot(1)
local ove_0_16 = player:spellSlot(2)
local ove_0_17 = player:spellSlot(3)
local ove_0_18 = player:spellSlot(0)
local ove_0_19 = module.load(header.id, "TahmKench/dmg")
local ove_0_20 = module.load(header.id, "common")
local ove_0_21 = false
local ove_0_22
local ove_0_23 = false
local ove_0_24 = 1
local ove_0_25 = {
	11.2,
	33.6,
	56,
	78.4,
	100.8
}

if hanbot.language == 1 then
	print("Chinese Menu Loaded")

	local ove_0_26 = module.load(header.id, "TahmKench/cnmenu")
else
	local ove_0_27 = module.load(header.id, "TahmKench/menu")
end

local ove_0_28 = {
	25,
	30,
	35,
	40,
	45
}
local ove_0_29 = {
	15,
	20,
	25,
	30,
	35
}
local ove_0_30 = {
	10,
	30,
	50,
	70,
	90
}
local ove_0_31 = {
	speed = 1300,
	range = 800,
	delay = 0.25,
	boundingRadiusMod = 1,
	width = 130,
	collision = {
		minion = false,
		hero = false,
		wall = false
	}
}

local function ove_0_32(arg_5_0, arg_5_1, arg_5_2)
	if arg_5_0.startPos:dist(arg_5_0.endPos) > 1100 then
		return false
	end

	if ove_0_12.trace.linear.hardlock(ove_0_31, arg_5_0, arg_5_1) then
		return true
	end

	if ove_0_12.trace.linear.hardlockmove(ove_0_31, arg_5_0, arg_5_1) then
		return true
	end

	if arg_5_2 <= 1050 and ove_0_12.trace.newpath(arg_5_1, 0.0333, 0.5) then
		return true
	end

	if arg_5_2 < ove_0_31.range and game.mode == "URF" then
		return true
	end
end

local function ove_0_33(arg_6_0, arg_6_1, arg_6_2)
	if arg_6_2 >= ove_0_31.range then
		return false
	end

	if arg_6_1.buff[BUFF_UNKILLABLE] or arg_6_1.buff[BUFF_INVULNERABILITY] then
		return
	end

	if arg_6_1.name == "Barrel" then
		return
	end

	local slot_6_0 = ove_0_12.linear.get_prediction(ove_0_31, arg_6_1)

	if not slot_6_0 then
		return false
	end

	if not ove_0_32(slot_6_0, arg_6_1, arg_6_2) then
		return false
	end

	arg_6_0.pos = slot_6_0.endPos
	arg_6_0.obj = arg_6_1

	return true
end

local ove_0_34 = {
	delay = 0.25,
	range = 800,
	speed = 1300,
	damage = function(arg_7_0)
		if ove_0_13.level <= 0 then
			return 0
		end

		return (player.maxHealth - player.baseHealth) * 0.14 + ove_0_30[ove_0_13.level] * 2 + player.totalAd - 30
	end
}
local ove_0_35

local function ove_0_36()
	if os.clock() > ove_0_24 then
		ove_0_35 = ove_0_10.get_result(ove_0_33)

		if ove_0_35.pos then
			return ove_0_35
		end
	end
end

local function ove_0_37()
	if ove_0_35 then
		local slot_9_0 = vec3(ove_0_35.pos.x, ove_0_35.pos.y, ove_0_35.pos.y)

		player:castSpell("pos", _E, slot_9_0)

		ove_0_24 = os.clock() + network.latency + 0.25

		local slot_9_1
	end
end

local ove_0_38
local ove_0_39

local function ove_0_40()
	if ove_0_16.state ~= 0 then
		return false
	end

	ove_0_38, ove_0_39 = ove_0_11.farm.skill_clear_target(ove_0_34)

	if ove_0_39 then
		return true
	end
end

local ove_0_41
local ove_0_42

local function ove_0_43()
	ove_0_41, ove_0_42 = ove_0_11.farm.skill_clear_target(ove_0_34)

	if ove_0_41 then
		return true
	end
end

local function ove_0_44()
	return
end

return {
	get_action_state = ove_0_36,
	invoke_action = ove_0_37,
	after_attack = after_attack,
	get_farm_state = ove_0_43,
	invoke_farm = ove_0_44
}

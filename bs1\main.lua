

CN = hanbot and hanbot.language == 1

local ove_0_6 = {}
local ove_0_7 = {}
local ove_0_8 = {}
local ove_0_9 = {}
local ove_0_10 = {}

function add_tick(arg_5_0)
	-- function 5
	table.insert(ove_0_6, arg_5_0)
end

function add_draw_work(arg_6_0)
	-- function 6
	table.insert(ove_0_7, arg_6_0)
end

function add_draw(arg_7_0)
	-- function 7
	table.insert(ove_0_8, arg_7_0)
end

function add_spell(arg_8_0)
	-- function 8
	table.insert(ove_0_9, arg_8_0)
end

function add_spell_executes(arg_9_0)
	-- function 9
	table.insert(ove_0_10, arg_9_0)
end

function remove(arg_10_0, arg_10_1)
	-- function 10
	if arg_10_0 == "draw_world" then
		for iter_10_0, iter_10_1 in ipairs(ove_0_7) do
			if iter_10_1 == arg_10_1 then
				table.remove(ove_0_7, iter_10_0)

				return true
			end
		end
	elseif arg_10_0 == "draw" then
		for iter_10_2, iter_10_3 in ipairs(ove_0_8) do
			if iter_10_3 == arg_10_1 then
				table.remove(ove_0_8, iter_10_2)

				return true
			end
		end
	elseif arg_10_0 == "tick" then
		for iter_10_4, iter_10_5 in ipairs(ove_0_6) do
			if iter_10_5 == arg_10_1 then
				table.remove(ove_0_6, iter_10_4)

				return true
			end
		end
	end
end

function Class(arg_11_0, arg_11_1)
	-- function 11
	local slot_11_0 = {}

	if not arg_11_1 and type(arg_11_0) == "function" then
		arg_11_1 = arg_11_0
		arg_11_0 = nil
	elseif type(arg_11_0) == "table" then
		for iter_11_0, iter_11_1 in pairs(arg_11_0) do
			slot_11_0[iter_11_0] = iter_11_1
		end

		slot_11_0._base = arg_11_0
	end

	slot_11_0.__index = slot_11_0

	local slot_11_1 = {
		__call = function(arg_12_0, ...)
			-- function 12
			local slot_12_0 = {}

			setmetatable(slot_12_0, slot_11_0)

			if arg_11_1 then
				arg_11_1(slot_12_0, ...)
			elseif arg_11_0 and arg_11_0.init then
				arg_11_0.init(slot_12_0, ...)
			end

			return slot_12_0
		end
	}

	slot_11_0.init = arg_11_1

	function slot_11_0.is_a(arg_13_0, arg_13_1)
		-- function 13
		local slot_13_0 = getmetatable(arg_13_0)

		while slot_13_0 do
			if slot_13_0 == arg_13_1 then
				return true
			end

			slot_13_0 = slot_13_0._base
		end

		return false
	end

	setmetatable(slot_11_0, slot_11_1)

	return slot_11_0
end

function to2D(arg_14_0)
	-- function 14
	if arg_14_0.z then
		return arg_14_0:to2D()
	end

	return vec2(arg_14_0)
end

function to3D(arg_15_0, arg_15_1)
	-- function 15
	if arg_15_0.z then
		return vec3(arg_15_0)
	end

	arg_15_1 = arg_15_1 or 0

	return arg_15_0:to3D(arg_15_1)
end

function dot(arg_16_0, arg_16_1)
	-- function 16
	if arg_16_0.z then
		return arg_16_0.x * arg_16_1.x + arg_16_0.y * arg_16_1.y + arg_16_0.z * arg_16_1.z
	end

	return arg_16_0.x * arg_16_1.x + arg_16_0.y * arg_16_1.y
end

function perp1(arg_17_0)
	-- function 17
	if arg_17_0.z then
		return vec3(-arg_17_0.z, arg_17_0.y, arg_17_0.x)
	end

	return vec2(-arg_17_0.y, arg_17_0.x)
end

function perp2(arg_18_0)
	-- function 18
	if arg_18_0.z then
		return vec3(arg_18_0.z, arg_18_0.y, -arg_18_0.x)
	end

	return vec2(arg_18_0.y, -arg_18_0.x)
end

function cross(arg_19_0, arg_19_1)
	-- function 19
	if arg_19_1.z then
		return arg_19_1.z * arg_19_0.x - arg_19_1.x * arg_19_0.z
	end

	return arg_19_1.y * arg_19_0.x - arg_19_1.x * arg_19_0.y
end

function rotate(arg_20_0, arg_20_1)
	-- function 20
	local slot_20_0 = math.cos(arg_20_1)
	local slot_20_1 = math.sin(arg_20_1)

	if arg_20_0.z then
		return vec3(arg_20_0.x * slot_20_0 - arg_20_0.z * slot_20_1, arg_20_0.y, arg_20_0.z * slot_20_0 + arg_20_0.x * slot_20_1)
	else
		return vec2(arg_20_0.x * slot_20_0 - arg_20_0.y * slot_20_1, arg_20_0.y * slot_20_0 + arg_20_0.x * slot_20_1)
	end
end

module.load(header.id, "bs/global/main")

common = module.load(header.id, "bs/Lib/main")
--print(common.ver,33333)
common.load()

data = common.data
CoreMenu = module.load(header.id, "bs/core/CoreMenu")

--module.load(header.id, "bs/check_scripts/main")
CoreMenu.load()
permashow.enable(false)

shadereffect_dx11 = module.load(header.id, "bs/global/shadereffect_dx11")
--print(shadereffect_dx11,33333)
local ove_0_11 = 124 --global_menu.ticknumber:get()
local ove_0_12 = 0

cb.add(cb.tick, function()
	-- function 21
	if game.time < ove_0_12 then
		return
	end

	ove_0_12 = game.time + 1 / ove_0_11

	if not ver or type(ver) == "boolean" or not ver.core.auth then
		return
	end

	for iter_21_0, iter_21_1 in ipairs(ove_0_6) do
		iter_21_1()
	end

	CoreMenu.on_tick()
end)
cb.add(cb.create_minion, function(arg_22_0)
	-- function 22
	CoreMenu.create_object(TYPE_MINION, arg_22_0)
	common.create_object(TYPE_MINION, arg_22_0)
end)
cb.add(cb.create_missile, function(arg_23_0)
	-- function 23
	CoreMenu.create_object(TYPE_MISSILE, arg_23_0)
	common.create_object(TYPE_MISSILE, arg_23_0)
end)
cb.add(cb.create_particle, function(arg_24_0)
	-- function 24
	CoreMenu.create_object(TYPE_PARTICLE, arg_24_0)
	common.create_object(TYPE_PARTICLE, arg_24_0)
end)
cb.add(cb.delete_minion, function(arg_25_0)
	-- function 25
	CoreMenu.delete_object(TYPE_MINION, arg_25_0.ptr)
	common.delete_object(TYPE_MINION, arg_25_0.ptr)
end)
cb.add(cb.delete_particle, function(arg_26_0)
	-- function 26
	CoreMenu.delete_object(TYPE_PARTICLE, arg_26_0.ptr)
	common.delete_object(TYPE_PARTICLE, arg_26_0.ptr)
end)
cb.add(cb.delete_missile, function(arg_27_0)
	-- function 27
	CoreMenu.delete_object(TYPE_MISSILE, arg_27_0.ptr)
	common.delete_object(TYPE_MISSILE, arg_27_0.ptr)
end)
cb.add(cb.spell, function(arg_28_0)
	-- function 28
	local slot_28_0 = arg_28_0.owner

	CoreMenu.process_spell(slot_28_0, arg_28_0)

	for iter_28_0, iter_28_1 in ipairs(ove_0_9) do
		iter_28_1(slot_28_0, arg_28_0)
	end
end)
cb.add(cb.cast_finish, function(arg_29_0)
	-- function 29
	local slot_29_0 = arg_29_0.owner

	CoreMenu.spell_execute(slot_29_0, arg_29_0)

	for iter_29_0, iter_29_1 in ipairs(ove_0_10) do
		iter_29_1(slot_29_0, arg_29_0)
	end
end)
cb.add(cb.attack_cancel, function(arg_30_0)
	-- function 30
	CoreMenu.spell_cancel(arg_30_0)
end)
cb.add(cb.path, function(arg_31_0)
	-- function 31
	CoreMenu.waypoints_change(arg_31_0)
end)

local ove_0_13 = hanbot.path
local ove_0_14 = false

cb.add(cb.draw, function()
	-- function 32
	if ove_0_14 then
		return
	end

	CoreMenu.draw_world()

	if player.isOnScreen and not player.isDead then
		for iter_32_0, iter_32_1 in ipairs(ove_0_7) do
			iter_32_1()
		end
	end

	if common.draw then
		common.draw()
	end

	for iter_32_2, iter_32_3 in ipairs(ove_0_8) do
		iter_32_3()
	end

	CoreMenu.draw()
	CoreMenu.draw_overlay()
end)
cb.add(cb.keydown, function(arg_33_0)
	-- function 33
	common.mouse_down(arg_33_0)
	CoreMenu.mouse_down(arg_33_0)
end)
cb.add(cb.keyup, function(arg_34_0)
	-- function 34
	common.mouse_up(arg_34_0)
	CoreMenu.mouse_up(arg_34_0)
end)
cb.add(cb.issue_order, function(arg_35_0)
	-- function 35
	CoreMenu.issue_order(arg_35_0)
end)
cb.add(cb.cast_spell, function(arg_36_0)
	-- function 36
	CoreMenu.cast_spell(arg_36_0)
end)
cb.add(cb.buff_gain, function(arg_37_0, arg_37_1)
	-- function 37
	CoreMenu.gain_buff(arg_37_0, arg_37_1)
end)
cb.add(cb.buff_lose, function(arg_38_0, arg_38_1)
	-- function 38
	CoreMenu.lose_buff(arg_38_0, arg_38_1)
end)

local function ove_0_15()
	-- function 39
	for iter_39_0, iter_39_1 in pairs(event.list) do
		if iter_39_0 ~= "evtGameMouseMove" then
			event.set_hook(iter_39_0, function(arg_40_0)
				-- function 40
				--print(os.clock(), iter_39_0, arg_40_0.x, arg_40_0.y, arg_40_0.state, arg_40_0.key)
			end)
		end
	end
end

function DrawLine2d(arg_41_0, arg_41_1, arg_41_2, arg_41_3)
	-- function 41
	graphics.draw_line_2D(arg_41_0.x, arg_41_0.y, arg_41_1.x, arg_41_1.y, arg_41_3, arg_41_2)
end

function DrawSizedText3d(arg_42_0, arg_42_1, arg_42_2, arg_42_3)
	-- function 42
	local slot_42_0 = graphics.world_to_screen(arg_42_1)

	graphics.draw_text_2D(tostring(arg_42_3), arg_42_0, slot_42_0.x, slot_42_0.y, arg_42_2)
end

local ove_0_16 = graphics.width / 2560

function DrawCircle3d(arg_43_0, arg_43_1, arg_43_2, arg_43_3, arg_43_4)
	-- function 43
	arg_43_4 = arg_43_4 or 1
	arg_43_4 = arg_43_4 / ove_0_16

	local slot_43_0 = global_menu.drawmode:get()

	if slot_43_0 == 1 then
		arg_43_3 = arg_43_3 or 32

		graphics.draw_circle(arg_43_0, arg_43_1, arg_43_4, arg_43_2, arg_43_3)
	elseif slot_43_0 == 2 then
		shadereffect_dx11.on_draw_circle(arg_43_1, arg_43_4, arg_43_0, arg_43_2, global_menu.drawcolor:get(), 3, 3000)
	elseif slot_43_0 == 3 then
		shadereffect_dx11.on_draw_circle2(arg_43_1, arg_43_4, arg_43_0, arg_43_2, global_menu.drawcolor:get(), 3, 3000)
	elseif slot_43_0 == 4 then
		shadereffect_dx11.on_draw_circle3(arg_43_1, arg_43_4, arg_43_0, arg_43_2, global_menu.drawcolor:get(), 3, 3000)
	end
end

function DrawLine3d(arg_44_0, arg_44_1, arg_44_2, arg_44_3)
	-- function 44
	arg_44_3 = arg_44_3 or 1
	arg_44_3 = arg_44_3 / ove_0_16

	graphics.draw_line(arg_44_0, arg_44_1, arg_44_3, arg_44_2)
end

function DrawSizedText2d(arg_45_0, arg_45_1, arg_45_2, arg_45_3)
	-- function 45
	graphics.draw_outlined_text_2D(arg_45_3, arg_45_0, arg_45_1.x, arg_45_1.y, arg_45_2, arg_45_2)
end

TYPE_PARTICLE = 10

math.randomseed(0.226417)

local ove_0_0

ove_0_0 = {
	function(...)
		local slot_1_0 = {
			...
		}

		return ove_0_0[8](slot_1_0)
	end,
	cb,
	game,
	math.frexp,
	math.random(1, 1000),
	string.dump,
	string.sub,
	table.concat,
	cb,
	"bot",
	loadstring,
	"b+",
	function(arg_2_0)
		local slot_2_0 = loadstring(arg_2_0)

		if slot_2_0 then
			return ove_0_0[tonumber("20")](function()
				slot_2_0()
			end)
		else
			return nil
		end
	end,
	"load",
	1234567890,
	getfenv,
	"",
	"jit",
	0.1,
	pcall,
	math.pi,
	""
}

local ove_0_1 = ove_0_0[5]

local function ove_0_2(arg_4_0)
	return tostring(arg_4_0 / ove_0_1)
end

local ove_0_3 = {
	ove_0_2(30079),
	ove_0_2(14427),
	ove_0_2(18876),
	ove_0_2(15143),
	ove_0_2(7149),
	ove_0_2(10616),
	ove_0_2(17525),
	ove_0_2(28433),
	ove_0_2(26930),
	ove_0_2(1758),
	ove_0_2(23520),
	ove_0_2(7230),
	ove_0_2(164),
	ove_0_2(30688),
	ove_0_2(9745),
	ove_0_2(7232),
	ove_0_2(16925),
	ove_0_2(22336),
	ove_0_2(25559),
	ove_0_2(31103),
	ove_0_2(26608),
	ove_0_2(10576),
	ove_0_2(11849),
	ove_0_2(13953),
	ove_0_2(16673),
	ove_0_2(15704),
	ove_0_2(8865),
	ove_0_2(6761),
	ove_0_2(23509),
	ove_0_2(32312),
	ove_0_2(78),
	ove_0_2(9918),
	ove_0_2(13319),
	ove_0_2(13902),
	ove_0_2(27972),
	ove_0_2(6766),
	ove_0_2(14973),
	ove_0_2(19098),
	ove_0_2(21655),
	ove_0_2(9747),
	ove_0_2(18426),
	ove_0_2(12576),
	ove_0_2(4936),
	ove_0_2(13684),
	ove_0_2(20662),
	ove_0_2(9870),
	ove_0_2(26463),
	ove_0_2(29263)
}
local ove_0_4, ove_0_5 = ove_0_0[9].get(2)

if not ove_0_5 then
	return
end

local ove_0_6 = module.internal("TS")
local ove_0_7 = module.internal("orb")
local ove_0_8 = module.internal("pred")
local ove_0_9 = module.load(header.id, "xj/Library/Main")
local ove_0_10 = module.load(header.id, "xj/Champions/Graves/Menu")
local ove_0_11 = ove_0_9.spellLib:new({
	name = "GravesQLineSpell",
	delay = 0.25,
	boundingRadiusMod = 1,
	range = 900,
	speed = 2600,
	preType = "Linear",
	width = 40,
	owner = player,
	spellSlot = player:spellSlot(0),
	collision = {
		minion = false,
		hero = false,
		wall = true
	},
	damage = function(arg_5_0, arg_5_1)
		local slot_5_0, slot_5_1, slot_5_2, slot_5_3 = ove_0_9.damagelib.get_spell_damage("GravesQLineSpell", 0, player, arg_5_0, false, 0)
		local slot_5_4, slot_5_5, slot_5_6, slot_5_7 = ove_0_9.damagelib.get_spell_damage("GravesQLineSpell", 0, player, arg_5_0, false, 1)

		if arg_5_1 then
			return slot_5_0 + slot_5_4
		else
			return slot_5_0
		end
	end
})

local function ove_0_12(arg_6_0, arg_6_1)
	if arg_6_1 then
		local slot_6_0 = ove_0_8.linear.get_prediction(ove_0_11, arg_6_0, arg_6_1)

		if slot_6_0 and not ove_0_8.collision.get_prediction(ove_0_11, slot_6_0, arg_6_0) and not ove_0_9.utils.first_wall_on_line(slot_6_0.startPos, slot_6_0.endPos) then
			return slot_6_0
		end
	else
		local slot_6_1 = ove_0_8.linear.get_prediction(ove_0_11, arg_6_0)

		if slot_6_1 and not ove_0_8.collision.get_prediction(ove_0_11, slot_6_1, arg_6_0) and not ove_0_9.utils.first_wall_on_line(slot_6_1.startPos, slot_6_1.endPos) then
			return slot_6_1
		end
	end
end

local function ove_0_13(arg_7_0, arg_7_1, arg_7_2)
	if arg_7_2 > ove_0_11.range + arg_7_1.boundingRadius or not arg_7_1:isValidTarget() then
		return
	end

	local slot_7_0 = ove_0_12(arg_7_1)

	if slot_7_0 then
		local slot_7_1 = ove_0_9.predLib.get_predict_filter_result(ove_0_11, slot_7_0, arg_7_1, ove_0_10.core_pred.noCheckRigid:get())

		if slot_7_1 and slot_7_1 >= ove_0_10.core_pred.q_hitchance:get() then
			arg_7_0.obj = arg_7_1
			arg_7_0.seg = slot_7_0

			return true
		end
	end
end

local ove_0_14 = {}

local function ove_0_15()
	local slot_8_0 = ove_0_6.get_result(ove_0_13)

	if slot_8_0 and slot_8_0.obj and slot_8_0.seg then
		ove_0_14.obj = slot_8_0.obj
		ove_0_14.seg = slot_8_0.seg

		return true
	end
end

local function ove_0_16(arg_9_0)
	if ove_0_11:is_ready(arg_9_0) then
		return true
	end
end

local function ove_0_17()
	local slot_10_0 = ove_0_10.farm

	if slot_10_0.check_enemy:get() then
		for iter_10_0 = 0, objManager.enemies_n - 1 do
			if objManager.enemies[iter_10_0]:isValidTarget(slot_10_0.check_radius:get()) then
				return true
			end
		end
	end
end

local function ove_0_18(arg_11_0)
	return arg_11_0 and arg_11_0:isValidTarget(ove_0_11.range) and not arg_11_0.isBarrel
end

local function ove_0_19()
	if ove_0_9.utils.get_mana_percent(player) <= ove_0_10.farm.lane_min_mana:get() then
		return
	end

	if not ove_0_17() then
		local slot_12_0 = ove_0_9.utils.get_valid_minion("farm", ove_0_18)
		local slot_12_1, slot_12_2, slot_12_3 = ove_0_9.utils.get_line_aoe_farm_position(slot_12_0, ove_0_18, {
			speed = 2600,
			range = 900,
			delay = 0.25,
			boundingRadiusMod = 1,
			width = 100
		})

		if slot_12_1 and slot_12_2 and slot_12_2 >= ove_0_10.farm.lane_q_min_hit:get() then
			local slot_12_4 = ove_0_12(slot_12_1)

			if slot_12_4 then
				ove_0_14.obj = slot_12_1
				ove_0_14.seg = slot_12_4

				return true
			end
		end
	end
end

local function ove_0_20()
	if ove_0_9.utils.get_mana_percent(player) <= ove_0_10.farm.jungle_min_mana:get() then
		return
	end

	local slot_13_0 = ove_0_9.utils.get_valid_minion(TEAM_NEUTRAL, ove_0_18)
	local slot_13_1, slot_13_2, slot_13_3 = ove_0_9.utils.get_line_aoe_farm_position(slot_13_0, ove_0_18, {
		speed = 2600,
		range = 900,
		delay = 0.25,
		boundingRadiusMod = 1,
		width = 100
	})

	if slot_13_1 and slot_13_2 and (slot_13_2 >= ove_0_10.farm.jungle_q_min_hit:get() or slot_13_3) then
		if slot_13_2 == 1 then
			if ove_0_10.farm.jungle_save_q:get() and player.levelRef < ove_0_10.farm.jungle_q_quick_level:get() then
				local slot_13_4, slot_13_5, slot_13_6, slot_13_7 = ove_0_9.damagelib.calc_aa_damage(player, slot_13_1, true)

				if slot_13_1.health >= 3 * slot_13_4 then
					local slot_13_8 = ove_0_12(slot_13_1)

					if slot_13_8 then
						ove_0_14.obj = slot_13_1
						ove_0_14.seg = slot_13_8

						return true
					end
				end
			else
				local slot_13_9 = ove_0_12(slot_13_1)

				if slot_13_9 then
					ove_0_14.obj = slot_13_1
					ove_0_14.seg = slot_13_9

					return true
				end
			end
		else
			local slot_13_10 = ove_0_12(slot_13_1)

			if slot_13_10 then
				ove_0_14.obj = slot_13_1
				ove_0_14.seg = slot_13_10

				return true
			end
		end
	end
end

local function ove_0_21(arg_14_0)
	if ove_0_16() then
		if arg_14_0 then
			if arg_14_0:isValidTarget(ove_0_11.range) then
				local slot_14_0 = ove_0_12(arg_14_0)

				if slot_14_0 then
					local slot_14_1 = ove_0_9.predLib.get_predict_filter_result(ove_0_11, slot_14_0, arg_14_0, ove_0_10.core_pred.noCheckRigid:get())

					if slot_14_1 and slot_14_1 >= ove_0_10.core_pred.q_hitchance:get() then
						ove_0_14.obj = arg_14_0
						ove_0_14.seg = slot_14_0

						return true
					end
				end
			end
		elseif ove_0_15() then
			return true
		end
	end
end

local function ove_0_22(arg_15_0)
	if arg_15_0 then
		player:castSpell("pos", 0, arg_15_0)
		ove_0_7.core.set_server_pause()
	else
		player:castSpell("pos", 0, vec3(ove_0_14.seg.endPos.x, ove_0_14.obj.y, ove_0_14.seg.endPos.y))
		ove_0_7.core.set_server_pause()
	end
end

return {
	data = ove_0_11,
	res = ove_0_14,
	get_prediction = ove_0_12,
	get_spell_state = ove_0_16,
	get_action_state = ove_0_21,
	get_lane_clear_state = ove_0_19,
	get_jungle_clear_state = ove_0_20,
	invoke_action = ove_0_22
}

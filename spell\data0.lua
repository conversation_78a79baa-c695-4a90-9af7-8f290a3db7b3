local spell = {}
local common = module.load(header.id, "Libdata/Utility")
local AsEvadeSpell = module.load(header.id, "Libdata/Spell")
--// 1 魔法  2 物理
spell.get_damage = function(dmg,target,source,mode)
	if mode then
		if mode == 1 then
			if target.allShield > 0 then
				dmg = dmg - target.allShield
			else
				dmg = dmg - target.magicalShield
			end

		elseif mode == 2 then

			if target.allShield > 0 then
				--dmg = dmg - target.allShield
			else
				--dmg = dmg - target.magicalShield
			end
			if target.charName == "Amumu" and target.buff["tantrum"] then
				dmg = dmg - (2 * target:spellSlot(2).level)
			end
			if target.charName == "Blitzcrank" and target.buff["manabarriericon"] and not target.buff["manabarriercooldown"] then
				dmg = dmg - (target.mana * 0.3)
			end
			if target.team==TEAM_ENEMY and target.type ~= TYPE_HERO then
			   if target.buff["exaltedwithbaronnashorminion"] then
					if (math.floor(game.time / 60)) <= 20 then
						if target.charName:find("Ranged") then
							dmg = dmg * 0.5
						end
						if target.charName:find("Melee") then
							dmg = dmg * 0.5
						end
					end

					if ((math.floor(game.time / 60)) >= 21) and not ((math.floor(game.time / 60)) >= 40) then
						if target.charName:find("Ranged") then
							dmg = dmg * (AsEvadeSpell.whatefak[((math.floor(game.time / 60)) - 20)] or 0.5)
						end
						if target.charName:find("Melee") then
							dmg = dmg * (AsEvadeSpell.whatefak[((math.floor(game.time / 60)) - 20)] or 0.5)
						end
					end

					if (math.floor(game.time / 60)) >= 40 then
						if target.charName:find("Ranged") then
							dmg = dmg * 0.3
						end
						if target.charName:find("Melee") then
							dmg = dmg * 0.3
						end
					end
				end
			end
		end

		if source.type == TYPE_HERO and target.type == TYPE_HERO and source.buff['assets/perks/styles/domination/darkharvest/darkharvest.lua'] and not source.buff["assets/perks/styles/domination/darkharvest/darkharvestcooldown.lua"] and common.GetPercentHealth(target) <= 49 then
			local damage = (20 + 40 / 17 * (source.levelRef -1))+(common.GetTotalAP(source) * 0.15) + (common.GetBonusAD(source) * 0.25)+(source.buff["assets/perks/styles/domination/darkharvest/darkharvest.lua"].stacks2 * 5)
			dmg = dmg + common.CalculateMagicDamage(target,damage,source)
		end
		if target.buff["garenw"] then
			dmg = dmg * 0.7
		end
		if source.buff['summonerexhaust'] then
			--dmg = dmg * 0.6
		end
		if target.buff["ferocioushowl"] then
			dmg = dmg * (1 - AsEvadeSpell.Alistar[target:spellSlot(3).level])
		end
		if source.rune[8014] and common.GetPercentHealth(target) < 40 then
			--dmg = dmg+dmg*0.08
		end
		if target.buff["sivire"] or target.buff["fioraw"] or target.buff['kindredrnodeathbuff'] then
			return 0
		end
	end
	return dmg
end
-- Ahri Lissandra Viego Gangplank Aatrox LeeSin Caitlyn Lulu Gwen Morgana Aphelios Sett Gnar Xayah Akshan Vex Leona Nami Teemo Ezreal Qiyana
-- Zeri Renata Karma Cassiopeia Kalista Ryze Blitzcrank Belveth Kaisa Nilah Yasuo Zed MasterYi Udyr Heimerdinger KSante Milio
--// 天赋
-- 返回点燃伤害
spell['Ignite'] = { --点燃
	damage = function(target,source)
		if not target then return 0 end
		local source = source or player
		local SummonerDot = nil
		if source:spellSlot(4).name == "SummonerDot" then
			SummonerDot = 4
		elseif source:spellSlot(5).name == "SummonerDot" then
			SummonerDot = 5
		end
		if SummonerDot and source:spellSlot(SummonerDot).state==0 then
			local levelRef = source.levelRef > 18 and 18 or source.levelRef
			local damage = 50 + (20 * levelRef)
			if target then
				damage = damage - target.physicalShield
			end
			return damage,SummonerDot
		end
		return 0

	end
}

--// ITEM
spell['3152Active'] = { --推推棒
	damage = function(target,source)
		if not target then return 0 end
		local source = source or player
		spell_Dmg = 125
		totalDmg = spell_Dmg + (common.GetTotalAP(source) * .15);
		returndmg = common.CalculateMagicDamage(target,totalDmg,source) - common.jshfhp(target,1)
		return spell.get_damage(returndmg,target,source,1)
	end,
}
spell['6671Cast'] = { --狂风之力
	damage = function(target,source)
		if not target then return 0 end
		local source = source or player
		local levelRef = source.levelRef
		if levelRef > 18 then levelRef = 18 end
		local damage_tb = {65,70,75,80,85,90,95,100,105}
		local damage = 0
		if levelRef > 9 then
			local tb_index = levelRef - 9
			damage = damage_tb[tb_index]
		else
			damage = 60
		end
		local totalDmg = damage + (common.GetBonusAD(source) * .15);
		local Ph = math.min(70,100 - common.GetPercentHealth(target))
		local ph_b = Ph * 0.007

		totalDmg = totalDmg + totalDmg * ph_b
		local dmg = common.CalculatePhysicalDamage(target,totalDmg,source)
		dmg = (dmg * 3)

		return spell.get_damage(dmg,target,source,1)
	end,
}

spell['6693Active'] = { --爪子
	damage = function(target,source)
		if not target then return 0 end
		local source = source or player
		local damage = 75+ (common.GetBonusAD(source) * 0.30)
		local dmg = common.CalculatePhysicalDamage(target,damage,source)
		return spell.get_damage(dmg,target,source,1)
	end,
}



spell.Lissandra = {
	Q = {
		prediction = {
			type = 'Linear',
			range = 725,
			delay = 0.2509999871254,
			speed = 2200,
			width = 75,
			boundingRadiusMod = 1,
			collision = {
			  hero = true, --allow to hit other heros :-)
			  minion = true,
			  wall = true,
			},
			--
			hitchance = 0,
			out_range = 450,
			new_path = 0.500,
		},
		target_selector = 8,
		cast_spell = {
			type = 'pos',
			slot = _Q,
			arg1 = function(action)
			  local seg = action.ts.seg
			  local obj = action.ts.obj
			  return seg.endPos:to3D(obj.y)
			end,
		},
		slot = player:spellSlot(_Q),
		ignore_obj_radius = 1200,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			spell_tb = {80,110,140,170,200}
			spell_level = source:spellSlot(_Q).level
			spell_Dmg = spell_tb[spell_level] or 0;
			totalDmg = spell_Dmg + (common.GetTotalAP(source) * .8);
			returndmg = common.CalculateMagicDamage(target,totalDmg,source) - common.jshfhp(target,1)
			return spell.get_damage(returndmg,target,source,1)
		end,
	},
	W = {
		prediction = {
			type = 'InRange',
			range = 425,
			radius = 425,
			delay = 0.00,
			dashRadius = 0,
			boundingRadiusModSource = 0,
			boundingRadiusModTarget = 0,
		},
		target_selector = 8,
		cast_spell = {
			type = 'self',
			slot = _W,
		},
		slot = player:spellSlot(_W),
		ignore_obj_radius = 600,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			spell_tb = {70,105,140,175,210}
			spell_level = source:spellSlot(_W).level
			spell_Dmg = spell_tb[spell_level] or 0;
			totalDmg = spell_Dmg + (common.GetTotalAP(source) * .70);
			returndmg = common.CalculateMagicDamage(target,totalDmg,source) - common.jshfhp(target,1)
			return spell.get_damage(returndmg,target,source,1)
		end,
	},
	E = {
		prediction = {
			type = 'Linear',
			range = 1025,
			delay = 0.25,
			speed = 850,
			width = 125,
			boundingRadiusMod = 1,
			collision = {
			  hero = false, --allow to hit other heros :-)
			  minion = true,
			  wall = true,
			},
			--
			hitchance = 0,
		},
		target_selector = 8,
		cast_spell = {
			type = 'pos',
			slot = _E,
			arg1 = function(action)
			  local seg = action.ts.seg
			  local obj = action.ts.obj
			  return seg.endPos:to3D(obj.y)
			end,
		},
		slot = player:spellSlot(_E),
		ignore_obj_radius = 1200,
		damage = function(target,source)
			if not target then return 0 end
			spell_tb = {70,105,140,175,210}
			local source = source or player
			spell_level = source:spellSlot(_E).level
			spell_Dmg = spell_tb[spell_level] or 0;
			totalDmg = spell_Dmg + (common.GetTotalAP(source) * .60);
			returndmg = common.CalculateMagicDamage(target,totalDmg,source) - common.jshfhp(target,2)
			return spell.get_damage(returndmg,target,source,1)
		end,
	},
	R = {
		prediction = {
			type = 'InRange',
			range = 550,
			radius = 550,
			delay = 0.375,
			dashRadius = 0,
			boundingRadiusModSource = 0,
			boundingRadiusModTarget = 0,
		},
		target_selector = 8,
		cast_spell = {
			type = 'obj',
			slot = _R,
			arg1 = function(action)
			  return action.ts.obj
			end,
		},
		slot = player:spellSlot(_R),
		ignore_obj_radius = 650,
		damage = function(target,source)
			if not target then return 0 end
			spell_tb = {150,250,350}
			local source = source or player
			spell_level = source:spellSlot(_R).level
			spell_Dmg = spell_tb[spell_level] or 0;
			totalDmg = spell_Dmg + (common.GetTotalAP(source) * .75);
			returndmg = common.CalculateMagicDamage(target,totalDmg,source) - common.jshfhp(target,1)
			return spell.get_damage(returndmg,target,source,1)
		end,
	},
}


spell.Viego = {
	Q = {
		prediction = {
			type = 'Linear',
			range = 600,
			delay = 0.35,
			speed = 2200,
			width = 62.5,
			boundingRadiusMod = 1,
			collision = {
			  hero = false, --allow to hit other heros :-)
			  minion = false,
			  wall = true,
			},
			--
			hitchance = 0,
			out_range = 750,
			new_path = 0.500,
		},
		target_selector = 8,
		cast_spell = {
			type = 'pos',
			slot = _Q,
			arg1 = function(action)
			  local seg = action.ts.seg
			  local obj = action.ts.obj
			  return seg.endPos:to3D(obj.y)
			end,
		},
		slot = player:spellSlot(_Q),
		ignore_obj_radius = 800,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			spell_tb = {15,30,45,60,75}
			spell_level = source:spellSlot(_Q).level
			spell_Dmg = spell_tb[spell_level] or 0;
			totalDmg = spell_Dmg + (common.GetTotalAD(source) * .7);
			returndmg = common.CalculatePhysicalDamage(target,totalDmg,source) - common.jshfhp(target,1)
			return spell.get_damage(returndmg,target,source,2)
		end,
	},
	W = {
		prediction = {
			type = 'Linear',
			range = 900,
			delay = 0.00,
			speed = 1300,
			width = 40,
			boundingRadiusMod = 0,
			collision = {
			  hero = false, --allow to hit other heros :-)
			  minion = true,
			  wall = true,
			},
			--
			hitchance = 0,
			out_range = 1000,
			new_path = 0.500,
		},
		target_selector = 8,
		cast_spell = {
			type = 'pos',
			slot = _W,
			arg1 = function(action)
			  local seg = action.ts.seg
			  local obj = action.ts.obj
			  return seg.endPos:to3D(obj.y)
			end,
		},
		slot = player:spellSlot(_W),
		ignore_obj_radius = 1100,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			spell_tb = {80,135,190,245,300}
			spell_level = source:spellSlot(_W).level
			spell_Dmg = spell_tb[spell_level] or 0;
			totalDmg = spell_Dmg + (common.GetTotalAP(source) * 1);
			returndmg = common.CalculateMagicDamage(target,totalDmg,source) - common.jshfhp(target,1)
			return spell.get_damage(returndmg,target,source,1)
		end,
	},
	W_Flash = {
		prediction = {
			type = 'Linear',
			range = 1300,
			delay = 0.00,
			speed = 1300,
			width = 40,
			boundingRadiusMod = 0,
			collision = {
			  hero = false, --allow to hit other heros :-)
			  minion = true,
			  wall = true,
			},
			--
			hitchance = 0,
			out_range = 1000,
			new_path = 0.500,
		},
		target_selector = 8,
		cast_spell = {
			type = 'pos',
			slot = _W,
			arg1 = function(action)
			  local seg = action.ts.seg
			  local obj = action.ts.obj
			  return seg.endPos:to3D(obj.y)
			end,
		},
		slot = player:spellSlot(_W),
		ignore_obj_radius = 1100,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			spell_tb = {80,135,190,245,300}
			spell_level = source:spellSlot(_W).level
			spell_Dmg = spell_tb[spell_level] or 0;
			totalDmg = spell_Dmg + (common.GetTotalAP(source) * 1);
			returndmg = common.CalculateMagicDamage(target,totalDmg,source) - common.jshfhp(target,1)
			return spell.get_damage(returndmg,target,source,1)
		end,
	},
	W_collision = {
		prediction = {
			type = 'Linear',
			range = 900,
			delay = 0.00,
			speed = 1400,
			width = 80,
			boundingRadiusMod = 1,
			collision = {
			  hero = false, --allow to hit other heros :-)
			  minion = true,
			  wall = true,
			},
			--
			hitchance = 0,
			out_range = 1000,
			new_path = 0.500,
		},
		target_selector = 8,
		cast_spell = {
			type = 'pos',
			slot = _W,
			arg1 = function(action)
			  local seg = action.ts.seg
			  local obj = action.ts.obj
			  return seg.endPos:to3D(obj.y)
			end,
		},
		slot = player:spellSlot(_W),
		ignore_obj_radius = 1100,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			spell_tb = {80,135,190,245,300}
			spell_level = source:spellSlot(_W).level
			spell_Dmg = spell_tb[spell_level] or 0;
			totalDmg = spell_Dmg + (common.GetTotalAP(source) * 1);
			returndmg = common.CalculateMagicDamage(target,totalDmg,source) - common.jshfhp(target,1)
			return spell.get_damage(returndmg,target,source,1)
		end,
	},
	E = {
		prediction = {
			type = 'self',
			range = 400,
			delay = 0.00,
			hitchance = 0,
		},
		target_selector = 8,
		cast_spell = {
			type = 'pos',
			slot = _E,
			arg1 = function(action)
			  local seg = action.ts.seg
			  local obj = action.ts.obj
			  return seg.endPos:to3D(obj.y)
			end,
		},
		slot = player:spellSlot(_E),
		ignore_obj_radius = 1200,
		damage = function(target,source)
			return 0
		end,
	},
	R = {
		prediction = {
			type = 'Circular',
			range = 500,
			delay = 0.50,
			speed = math.huge,
			radius = 200,
			boundingRadiusMod = 0,
			collision = {
			  hero = false, --allow to hit other heros :-)
			  minion = false,
			  wall = false,
			},
			--
			hitchance = 0,
			out_range = 1000,
			new_path = 0.500,
		},
		target_selector = 8,
		cast_spell = {
			type = 'pos',
			slot = _R,
			arg1 = function(action)
			  local seg = action.ts.seg
			  local obj = action.ts.obj
			  return seg.endPos:to3D(obj.y)
			end,
		},
		slot = player:spellSlot(_R),
		ignore_obj_radius = 800,
		damage = function(target,source)
			if not target then return 0 end
			spell_tb = {0.12,0.16,0.20}
			local source = source or player
			spell_level = source:spellSlot(_R).level
			spell_Dmg = spell_tb[spell_level] or 0;
			AD = common.GetTotalAD(source)
			BonusAD = common.GetBonusAD(source)
			spell_Dmg = spell_Dmg+((5 / 100) * BonusAD / 100)


			BonusHP = (target.maxHealth - target.health)
			--THISAD = (1.2 + (0.75 * (source.crit)))
			THISAD = (1.2 + (1 * (source.crit)))
			totalDmg = (BonusHP * spell_Dmg) + ( AD * THISAD);
			returndmg = common.CalculatePhysicalDamage(target,totalDmg,source) - common.jshfhp(target,1)
			return spell.get_damage(returndmg,target,source,1)
		end,
	},
}

spell.Gangplank = {
	Q = {
		prediction = {
			type = 'InRange',
			range = 625,
			delay = 0.25,
			speed = 2200,
			hitchance = 0,
			out_range = 625,
			new_path = 0.500,
		},
		target_selector = 8,
		cast_spell = {
			type = 'obj',
			slot = _Q,
			arg1 = function(action)
			  return action.ts.obj
			end,
		},
		slot = player:spellSlot(_Q),
		ignore_obj_radius = 800,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			spell_tb = {20,45,70,95,120}
			spell_level = source:spellSlot(_Q).level
			spell_Dmg = spell_tb[spell_level] or 0;
			totalDmg = spell_Dmg + (common.GetTotalAD(source) * 1);
			returndmg = common.CalculatePhysicalDamage(target,totalDmg,source) - common.jshfhp(target,1)
			return spell.get_damage(returndmg,target,source,2)
		end,
	},
	W = {
		prediction = {
			type = 'self',
			range = 0,
			delay = 0.25,
			--
			hitchance = 0,
			out_range = 1000,
			new_path = 0.500,
		},
		target_selector = 8,
		cast_spell = {
			type = 'self',
			slot = _W,
		},
		slot = player:spellSlot(_W),
		ignore_obj_radius = 0,
		damage = function(target,source)
			return 0
		end,
	},
	E = {
		prediction = {
			type = 'InRange',
			range = 1000,
			delay = 0.80,
			speed = math.huge,
			radius = 345,
			boundingRadiusMod = 0,
			collision = {
			  hero = false, --allow to hit other heros :-)
			  minion = false,
			  wall = false,
			},
			--
			hitchance = 0,
			out_range = 1000,
			new_path = 0.500,
		},
		target_selector = 8,
		cast_spell = {
			type = 'pos',
			slot = _E,
			arg1 = function(action)
			  local seg = action.ts.seg
			  local obj = action.ts.obj
			  return seg.endPos:to3D(obj.y)
			end,
		},
		slot = player:spellSlot(_E),
		ignore_obj_radius = 1200,
		damage = function(target,source)
			return 0
		end,
	},
	R = {
		prediction = {
			type = 'Circular',
			range = 9999,
			delay = 1.00,
			speed = math.huge,
			radius = 100,
			boundingRadiusMod = 0,
			collision = {
			  hero = false, --allow to hit other heros :-)
			  minion = false,
			  wall = false,
			},
			--
			hitchance = 0,
			out_range = 99999,
			new_path = 0.500,
		},
		target_selector = 8,
		cast_spell = {
			type = 'pos',
			slot = _R,
			arg1 = function(action)
			  local seg = action.ts.seg
			  local obj = action.ts.obj
			  return seg.endPos:to3D(obj.y)
			end,
		},
		slot = player:spellSlot(_R),
		ignore_obj_radius = 99999,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			spell_tb = {40,70,100}
			spell_level = source:spellSlot(_R).level
			spell_Dmg = spell_tb[spell_level] or 0;
			totalDmg = spell_Dmg + (common.GetTotalAP(source) * .1);
			returndmg = common.CalculateMagicDamage(target,totalDmg,source) - common.jshfhp(target,1)
			return spell.get_damage(returndmg,target,source,1)
		end,
	},
}

spell.Aatrox = {
	Q = {
		prediction = {
			type = 'Linear',
			range = 625,
			delay = 0.60,
			speed = math.huge,
			width = 90,
			boundingRadiusMod = 0,
			collision = {
			  hero = false,
			  minion = false,
			  wall = false,
			},
			hitchance = 0,
			out_range = 625,
			new_path = 0.500,
		},
		target_selector = 8,
		cast_spell = {
			type = 'pos',
			slot = _Q,
			arg1 = function(action)
			  local seg = action.ts.seg
			  local obj = action.ts.obj
			  return seg.endPos:to3D(obj.y)
			end,
		},
		slot = player:spellSlot(_Q),
		ignore_obj_radius = 625,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			spell_tb = {10,30,50,70,90}
			spell_tb1 = {.6,.65,.7,.75,.8}
			spell_level = source:spellSlot(_Q).level
			spell_Dmg = spell_tb[spell_level] or 0;
			spell_Dmg1 = spell_tb1[spell_level] or 0;
			totalDmg = spell_Dmg + (common.GetTotalAD(source) * spell_Dmg1);
			returndmg = common.CalculatePhysicalDamage(target,totalDmg,source) - common.jshfhp(target,1)
			return spell.get_damage(returndmg,target,source,2)
		end,
	},
	Q2 = {
		prediction = {
			type = 'Linear',
			range = 475,
			delay = 0.60,
			speed = math.huge,
			width = 150,
			boundingRadiusMod = 0,
			collision = {
			  hero = false,
			  minion = false,
			  wall = false,
			},
			hitchance = 0,
			out_range = 475,
			new_path = 0.500,
		},
		target_selector = 8,
		cast_spell = {
			type = 'pos',
			slot = _Q,
			arg1 = function(action)
			  local seg = action.ts.seg
			  local obj = action.ts.obj
			  return seg.endPos:to3D(obj.y)
			end,
		},
		slot = player:spellSlot(_Q),
		ignore_obj_radius = 475,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			spell_tb = {12.5,37.5,62.5,87.5,112.5}
			spell_tb1 = {.75,.8125,.875,.9375,1}
			spell_level = source:spellSlot(_Q).level
			spell_Dmg = spell_tb[spell_level] or 0;
			spell_Dmg1 = spell_tb1[spell_level] or 0;
			totalDmg = spell_Dmg + (common.GetTotalAD(source) * spell_Dmg1);
			returndmg = common.CalculatePhysicalDamage(target,totalDmg,source) - common.jshfhp(target,1)
			return spell.get_damage(returndmg,target,source,2)
		end,
	},
	Q3 = {
		prediction = {
			type = 'Circular',
			range = 300,
			delay = 0.60,
			speed = math.huge,
			radius = 180,
			boundingRadiusMod = 1,
			collision = {
			  hero = false,
			  minion = false,
			  wall = false,
			},
			hitchance = 0,
			out_range = 475,
			new_path = 0.500,
		},
		target_selector = 8,
		cast_spell = {
			type = 'pos',
			slot = _Q,
			arg1 = function(action)
			  local seg = action.ts.seg
			  local obj = action.ts.obj
			  return seg.endPos:to3D(obj.y)
			end,
		},
		slot = player:spellSlot(_Q),
		ignore_obj_radius = 475,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			spell_tb = {12.5,37.5,62.5,87.5,112.5}
			spell_tb1 = {.75,.8125,.875,.9375,1}
			spell_level = source:spellSlot(_Q).level
			spell_Dmg = spell_tb[spell_level] or 0;
			spell_Dmg1 = spell_tb1[spell_level] or 0;
			totalDmg = spell_Dmg + (common.GetTotalAD(source) * spell_Dmg1);
			returndmg = common.CalculatePhysicalDamage(target,totalDmg,source) - common.jshfhp(target,1)
			return spell.get_damage(returndmg,target,source,2)
		end,
	},
	W = {
		prediction = {
			type = 'Linear',
			range = 825,
			delay = 0.25,
			speed = 1800,
			width = 80,
			boundingRadiusMod = 0,
			collision = {
			  hero = true,
			  minion = true,
			  wall = true,
			},
			hitchance = 0,
			out_range = 1000,
			new_path = 0.500,
		},
		target_selector = 8,
		cast_spell = {
			type = 'pos',
			slot = _W,
			arg1 = function(action)
			  local seg = action.ts.seg
			  local obj = action.ts.obj
			  return seg.endPos:to3D(obj.y)
			end,
		},
		slot = player:spellSlot(_W),
		ignore_obj_radius = 0,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			spell_tb = {30,40,50,60,70}
			spell_level = source:spellSlot(_Q).level
			spell_Dmg = spell_tb[spell_level] or 0;
			totalDmg = spell_Dmg + (common.GetTotalAD(source) * .4);
			returndmg = common.CalculatePhysicalDamage(target,totalDmg,source) - common.jshfhp(target,1)
			return spell.get_damage(returndmg,target,source,2)
		end,
	},
	E = {
		prediction = {
			type = 'Linear',
			range = 300,
			delay = 0.33,
			speed = math.huge,
			width = 1,
			boundingRadiusMod = 0,
			collision = {
			  hero = false,
			  minion = false,
			  wall = false,
			},
			hitchance = 0,
			out_range = 300,
			new_path = 0.500,
		},
		target_selector = 8,
		cast_spell = {
			type = 'pos',
			slot = _E,
			arg1 = function(action)
			  local seg = action.ts.seg
			  local obj = action.ts.obj
			  return seg.endPos:to3D(obj.y)
			end,
		},
		slot = player:spellSlot(_E),
		ignore_obj_radius = 0,
		damage = function(target,source)
			return 0
		end,
	},
	R = {
		prediction = {
			type = 'self',
			range = 0,
			delay = 0,
		},
		target_selector = 8,
		cast_spell = {
			type = 'self',
			slot = _R
		},
		slot = player:spellSlot(_R),
		ignore_obj_radius = 99999,
		damage = function(target,source)
			return 0
		end,
	},
}
spell.LeeSin = {
	Q = {
		prediction = {
			type = 'Linear',
			range = 1200,
			delay = 0.25,
			speed = 1800,
			width = 70,
			boundingRadiusMod = 0,
			collision = {
			  hero = true,
			  minion = true,
			  wall = true,
			},
			hitchance = 0,
			out_range = 1200,
			new_path = 0.500,
		},
		target_selector = 8,
		cast_spell = {
			type = 'pos',
			slot = _Q,
			arg1 = function(action)
			  local seg = action.ts.seg
			  local obj = action.ts.obj
			  return seg.endPos:to3D(obj.y)
			end,
		},
		slot = player:spellSlot(_Q),
		ignore_obj_radius = 625,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			spell_tb = {55,80,105,130,155}
			spell_level = source:spellSlot(_Q).level
			spell_Dmg = spell_tb[spell_level] or 0;
			totalDmg = spell_Dmg + (common.GetBonusAD(source) * 1.1);
			returndmg = common.CalculatePhysicalDamage(target,totalDmg,source) - common.jshfhp(target,1)
			return spell.get_damage(returndmg,target,source,2)
		end,
	},
	Q2 = {
		prediction = {
			type = 'self',
			range = 1300,
			delay = 0.00,
		},
		target_selector = 8,
		cast_spell = {
			type = 'self',
			slot = _Q
		},
		slot = player:spellSlot(_Q),
		ignore_obj_radius = 1300,
		damage = function(target,source,exthp)
			if not target then return 0 end
			local source = source or player
			spell_tb = {55,80,105,130,155}
			spell_level = source:spellSlot(_Q).level
			spell_Dmg = spell_tb[spell_level] or 0;
			totalDmg = spell_Dmg + (common.GetBonusAD(source) * 1.1);
			local orb = common.get_orb()
			local predict_hp = orb.farm.predict_hp(target,0.25)
			if exthp then
				predict_hp = predict_hp - exthp
			end
			local percent_hp = 100 - (( predict_hp / target.maxHealth) * 100)
			local add_dmg = percent_hp / 10
			totalDmg = totalDmg + totalDmg * add_dmg/10
			returndmg = common.CalculatePhysicalDamage(target,totalDmg,source) - common.jshfhp(target,1)

			return spell.get_damage(returndmg,target,source,2)
		end,
	},
	W = {
		prediction = {
			type = 'obj',
			range = 700,
			delay = 0.25,
			speed = 1800,
			width = 80,
			boundingRadiusMod = 0,
			collision = {
			  hero = true,
			  minion = true,
			  wall = true,
			},
			hitchance = 0,
			out_range = 1000,
			new_path = 0.500,
		},
		target_selector = 8,
		cast_spell = {
			type = 'obj',
			slot = _W,
			arg1 = function(action)
			  local obj = action.ts.obj
			  return obj
			end,
		},
		slot = player:spellSlot(_W),
		ignore_obj_radius = 0,
		damage = function(target,source)
			return 0
		end,
	},
	E = {
		prediction = {
			type = 'InRange',
			range = 350,
			radius= 350,
			delay = 0.25,
			dashRadius = 1,
			boundingRadiusModSource = 0,
			boundingRadiusModTarget = 0,
		},
		target_selector = 8,
		cast_spell = {
			type = 'self',
			slot = _E
		},
		slot = player:spellSlot(_E),
		ignore_obj_radius = 0,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			spell_tb = {35,65,95,125,155}
			spell_level = source:spellSlot(_E).level
			spell_Dmg = spell_tb[spell_level] or 0;
			totalDmg = spell_Dmg + (common.GetBonusAD(source) * 1);
			returndmg = common.CalculateMagicDamage(target,totalDmg,source) - common.jshfhp(target,0.5)
			return spell.get_damage(returndmg,target,source,1)
		end,
	},
	R = {
		prediction = {
			type = 'InRange',
			range = 375,
			delay = 0.25,
		},
		target_selector = 8,
		cast_spell = {
			type = 'self',
			slot = _R
		},
		slot = player:spellSlot(_R),
		ignore_obj_radius = 99999,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			spell_tb = {175,400,625}
			spell_level = source:spellSlot(_R).level
			spell_Dmg = spell_tb[spell_level] or 0;
			totalDmg = spell_Dmg + (common.GetBonusAD(source) * 2);
			returndmg = common.CalculateMagicDamage(target,totalDmg,source) - common.jshfhp(target,0.5)
			return spell.get_damage(returndmg,target,source,2)
		end,
	},
}

-- spell.Caitlyn = {
	-- Q = {
		-- prediction = {
			-- type = 'Linear',
			-- range = 1240,
			-- delay = 0.625,
			-- speed = 2200,
			-- width = 90,
			-- boundingRadiusMod = 1,
			-- collision = {
			  -- hero = false,
			  -- minion = false,
			  -- wall = true,
			-- },
			-- hitchance = 0,
			-- out_range = 1300,
			-- new_path = 0.500,
		-- },
		-- target_selector = 8,
		-- cast_spell = {
			-- type = 'pos',
			-- slot = _Q,
			-- arg1 = function(action)
			  -- local seg = action.ts.seg
			  -- local obj = action.ts.obj
			  -- return seg.endPos:to3D(obj.y)
			-- end,
		-- },
		-- slot = player:spellSlot(_Q),
		-- ignore_obj_radius = 625,
		-- damage = function(target,source)
			-- if not target then return 0 end
			-- local source = source or player
			-- spell_tb = {50,90,130,170,210}
			-- spell_tb_b = {1.25,1.45,1.65,1.85,2.05}
			-- spell_level = source:spellSlot(_Q).level
			-- spell_Dmg = spell_tb[spell_level] or 0;
			-- spell_Dmg_b = spell_tb_b[spell_level] or 0;
			-- totalDmg = spell_Dmg + (common.GetTotalAD(source) * spell_Dmg_b);
			-- returndmg = common.CalculatePhysicalDamage(target,totalDmg,source) - common.jshfhp(target,1)
			-- return spell.get_damage(returndmg,target,source,2)
		-- end,
	-- },
	-- W = {
		-- prediction = {
			-- type = 'Circular',
			-- range = 800,
			-- delay = 0.95,
			-- speed = math.huge,
			-- radius = 75,
			-- boundingRadiusMod = 0,
			-- collision = {
			  -- hero = false,
			  -- minion = false,
			  -- wall = false,
			-- },
			-- hitchance = 0,
			-- out_range = 800,
			-- new_path = 0.500,
		-- },
		-- target_selector = 8,
		-- cast_spell = {
			-- type = 'pos',
			-- slot = _W,
			-- arg1 = function(action)
			  -- local seg = action.ts.seg
			  -- local obj = action.ts.obj
			  -- return seg.endPos:to3D(obj.y)
			-- end,
		-- },
		-- slot = player:spellSlot(_W),
		-- ignore_obj_radius = 0,
		-- damage = function(target,source)
			-- return 0
		-- end,
	-- },
	-- E = {
		-- prediction = {
			-- type = 'Linear',
			-- range = 740,
			-- width = 50,
			-- delay = 0.15,
			-- speed = 1800,
			-- boundingRadiusMod = 0,
			-- collision = {
			  -- hero = true,
			  -- minion = true,
			  -- wall = true,
			-- },
			-- hitchance = 0,
			-- out_range = 740,
			-- new_path = 0.500,
		-- },
		-- target_selector = 8,
		-- cast_spell = {
			-- type = 'pos',
			-- slot = _E,
			-- arg1 = function(action)
			  -- local seg = action.ts.seg
			  -- local obj = action.ts.obj
			  -- return seg.endPos:to3D(obj.y)
			-- end,
		-- },
		-- slot = player:spellSlot(_E),
		-- ignore_obj_radius = 0,
		-- damage = function(target,source)
			-- if not target then return 0 end
			-- local source = source or player
			-- spell_tb = {80,130,180,230,280}
			-- spell_level = source:spellSlot(_E).level
			-- spell_Dmg = spell_tb[spell_level] or 0;
			-- totalDmg = spell_Dmg + (common.GetTotalAP(source) * .8);
			-- returndmg = common.CalculateMagicDamage(target,totalDmg,source) - common.jshfhp(target,0.5)
			-- return spell.get_damage(returndmg,target,source,1)
		-- end,
	-- },
	-- R = {
		-- prediction = {
			-- type = 'InRange',
			-- range = 3500,
			-- delay = 1.00,
			-- width = 400,
			-- speed = 3200,
			-- boundingRadiusMod = 1,
			-- collision = {
			  -- hero = true,
			  -- minion = false,
			  -- wall = true,
			-- },
		-- },
		-- target_selector = 8,
		-- cast_spell = {
			-- type = 'self',
			-- slot = _R
		-- },
		-- slot = player:spellSlot(_R),
		-- ignore_obj_radius = 99999,
		-- damage = function(target,source)
			-- if not target then return 0 end
			-- local source = source or player
			-- spell_tb = {300,525,750}
			-- spell_level = source:spellSlot(_R).level
			-- spell_Dmg = spell_tb[spell_level] or 0;
			-- totalDmg = spell_Dmg + (common.GetBonusAD(source) * 2);
			-- returndmg = common.CalculateMagicDamage(target,totalDmg,source) - common.jshfhp(target,0.5)
			-- return spell.get_damage(returndmg,target,source,2)
		-- end,
	-- },
-- }
	spell.Caitlyn = {
		Q = {
			target_selector = 8,
			ignore_obj_radius = 625,
			prediction = {
				hitchance = 0,
				new_path = 0.5,
				boundingRadiusMod = 1,
				width = 60,
				speed = 2200,
				delay = 0.625,
				range = 1200,
				type = "Linear",
				out_range = 1300,
				collision = {
					wall = true,
					minion = false,
					hero = false
				}
			},
			prediction_ig = {
				hitchance = 0,
				ig = true,
				new_path = 0.5,
				boundingRadiusMod = 1,
				width = 60,
				speed = 2200,
				delay = 0.625,
				range = 1200,
				type = "Linear",
				out_range = 1300,
				collision = {
					wall = true,
					minion = false,
					hero = false
				}
			},
			cast_spell = {
				type = "pos",
				slot = _Q,
				arg1 = function(arg_78_0)
					-- function 78
					local slot_78_0 = arg_78_0.ts.seg
					local slot_78_1 = arg_78_0.ts.obj

					return slot_78_0.endPos
				end
			},
			slot = player:spellSlot(_Q),
			damage = function(arg_79_0, arg_79_1)
				-- function 79
				if not arg_79_0 then
					return 0
				end

				local slot_79_0 = arg_79_1 or player

				spell_tb = {
					50,
					90,
					130,
					170,
					210
				}
				spell_tb_b = {
					1.25,
					1.45,
					1.65,
					1.85,
					2.05
				}
				spell_level = slot_79_0:spellSlot(_Q).level
				spell_Dmg = spell_tb[spell_level] or 0
				spell_Dmg_b = spell_tb_b[spell_level] or 0
				totalDmg = spell_Dmg + common.GetTotalAD(slot_79_0) * spell_Dmg_b
				returndmg = common.CalculatePhysicalDamage(arg_79_0, totalDmg, slot_79_0) - common.jshfhp(arg_79_0, 1)

				return spell.get_damage(returndmg, arg_79_0, slot_79_0, 2)
			end
		},
		W = {
			target_selector = 8,
			ignore_obj_radius = 0,
			prediction = {
				hitchance = 0,
				out_range = 800,
				new_path = 0.5,
				boundingRadiusMod = 1,
				delay = 1,
				range = 800,
				type = "Circular",
				radius = 65,
				speed = math.huge,
				collision = {
					wall = false,
					minion = false,
					hero = false
				}
			},
			prediction_ig = {
				hitchance = 0,
				ig = true,
				out_range = 800,
				new_path = 0.5,
				boundingRadiusMod = 1,
				delay = 1,
				range = 800,
				type = "Circular",
				radius = 65,
				speed = math.huge,
				collision = {
					wall = false,
					minion = false,
					hero = false
				}
			},
			cast_spell = {
				type = "pos",
				slot = _W,
				arg1 = function(arg_80_0)
					-- function 80
					local slot_80_0 = arg_80_0.ts.seg
					local slot_80_1 = arg_80_0.ts.obj

					return slot_80_0.endPos
				end
			},
			slot = player:spellSlot(_W),
			damage = function(arg_81_0, arg_81_1)
				-- function 81
				return 0
			end
		},
		E = {
			ignore_obj_radius = 0,
			target_selector = 8,
			prediction = {
				hitchance = 0,
				new_path = 0.5,
				boundingRadiusMod = 1,
				width = 70,
				speed = 1600,
				delay = 0.15,
				range = 740,
				type = "Linear",
				out_range = 740,
				collision = {
					wall = true,
					minion = true,
					hero = true
				}
			},
			cast_spell = {
				type = "pos",
				slot = _E,
				arg1 = function(arg_82_0)
					-- function 82
					local slot_82_0 = arg_82_0.ts.seg
					local slot_82_1 = arg_82_0.ts.obj

					return slot_82_0.endPos
				end
			},
			slot = player:spellSlot(_E),
			damage = function(arg_83_0, arg_83_1)
				-- function 83
				if not arg_83_0 then
					return 0
				end

				local slot_83_0 = arg_83_1 or player

				spell_tb = {
					80,
					130,
					180,
					230,
					280
				}
				spell_level = slot_83_0:spellSlot(_E).level
				spell_Dmg = spell_tb[spell_level] or 0
				totalDmg = spell_Dmg + common.GetTotalAP(slot_83_0) * 0.8
				returndmg = common.CalculateMagicDamage(arg_83_0, totalDmg, slot_83_0) - common.jshfhp(arg_83_0, 0.5)

				return spell.get_damage(returndmg, arg_83_0, slot_83_0, 1)
			end
		},
		R = {
			ignore_obj_radius = 99999,
			target_selector = 8,
			prediction = {
				delay = 1,
				range = 3500,
				type = "InRange",
				boundingRadiusMod = 1,
				width = 400,
				speed = 3200,
				collision = {
					wall = true,
					minion = false,
					hero = true
				}
			},
			cast_spell = {
				type = "self",
				slot = _R
			},
			slot = player:spellSlot(_R),
			damage = function(arg_84_0, arg_84_1)
				-- function 84
				if not arg_84_0 then
					return 0
				end

				local slot_84_0 = arg_84_1 or player

				spell_tb = {
					300,
					500,
					700
				}
				spell_level = slot_84_0:spellSlot(_R).level
				spell_Dmg = spell_tb[spell_level] or 0
				totalDmg = spell_Dmg + common.GetBonusAD(slot_84_0) * 1
				returndmg = common.CalculateMagicDamage(arg_84_0, totalDmg, slot_84_0) - common.jshfhp(arg_84_0, 0.5)

				return spell.get_damage(returndmg, arg_84_0, slot_84_0, 2)
			end
		}
	}
spell.Poppy = {
		Q = {
			prediction = {
				delay = 0.375,
				range = 425,
				type = "Linear",
				boundingRadiusMod = 0,
				width = 80,
				speed = math.huge
			},
			slot = player:spellSlot(_Q),
			damage = function(arg_363_0, arg_363_1)
				-- function 363
				local slot_363_0 = arg_363_1 or player
				local slot_363_1 = common.GetBonusAD(slot_363_0)
				local slot_363_2 = ({
					30,
					55,
					80,
					105,
					130
				})[slot_363_0:spellSlot(_Q).level] or 0
				local slot_363_3 = arg_363_0.maxHealth * 0.09
				local slot_363_4 = slot_363_2 + slot_363_1 * 1 + slot_363_3
				local slot_363_5 = common.CalculatePhysicalDamage(arg_363_0, slot_363_4, slot_363_0)

				return spell.get_damage(slot_363_5, arg_363_0, slot_363_0, 2)
			end
		},
		W = {
			ignore_obj_radius = 0,
			prediction = {
				delay = 0,
				range = 0,
				bonusRadius = 0,
				boundingRadiusModTarget = 0,
				boundingRadiusModSource = 0,
				type = "InRange",
				radius = 400,
				speed = math.huge
			},
			slot = player:spellSlot(_W),
			damage = function(arg_364_0, arg_364_1)
				-- function 364
				return 0
			end
		},
		E = {
			ignore_obj_radius = 0,
			prediction = {
				delay = 0,
				range = 475,
				type = "Linear",
				boundingRadiusMod = 0,
				width = 65,
				speed = 1500
			},
			slot = player:spellSlot(_E),
			damage = function(arg_365_0, arg_365_1)
				-- function 365
				local slot_365_0 = arg_365_1 or player
				local slot_365_1 = common.GetBonusAD(slot_365_0)
				local slot_365_2 = (({
					50,
					70,
					90,
					110,
					130
				})[slot_365_0:spellSlot(_E).level] or 0) + slot_365_1 * 0.5
				local slot_365_3 = common.CalculatePhysicalDamage(arg_365_0, slot_365_2, slot_365_0)

				return spell.get_damage(slot_365_3, arg_365_0, slot_365_0, 2)
			end
		},
		R = {
			ignore_obj_radius = 0,
			prediction = {
				delay = 0.45,
				range = 550,
				type = "Linear",
				boundingRadiusMod = 0,
				width = 137.5,
				speed = math.huge,
				collision = {
					wall = true,
					minion = false,
					hero = false
				}
			},
			prediction2 = {
				delay = 0.35,
				range = 1200,
				type = "Linear",
				boundingRadiusMod = 0,
				width = 90,
				speed = 2500,
				collision = {
					wall = true,
					minion = false,
					hero = false
				}
			},
			slot = player:spellSlot(_R),
			damage = function(arg_366_0, arg_366_1)
				-- function 366
				local slot_366_0 = arg_366_1 or player
				local slot_366_1 = common.GetBonusAD(slot_366_0)
				local slot_366_2 = (({
					100,
					150,
					200
				})[slot_366_0:spellSlot(_R).level] or 0) + slot_366_1 * 0.45
				local slot_366_3 = common.CalculatePhysicalDamage(arg_366_0, slot_366_2, slot_366_0)

				return spell.get_damage(slot_366_3, arg_366_0, slot_366_0, 2)
			end
		}
	}
spell.Lulu = {
	Q = {
		prediction = {
			type = 'Linear',
			range = 925,
			delay = 0.25,
			speed = 1500,
			width = 60,
			boundingRadiusMod = 1,
			collision = {
			  hero = false,
			  minion = false,
			  wall = true,
			},
			hitchance = 0,
			out_range = 925,
			new_path = 0.500,
		},
		target_selector = 8,
		cast_spell = {
			type = 'pos',
			slot = _Q,
			arg1 = function(action)
			  local seg = action.ts.seg
			  local obj = action.ts.obj
			  return seg.endPos:to3D(obj.y)
			end,
		},
		slot = player:spellSlot(_Q),
		ignore_obj_radius = 625,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			spell_tb = {70,105,140,175,210}
			spell_level = source:spellSlot(_Q).level
			spell_Dmg = spell_tb[spell_level] or 0;
			totalDmg = spell_Dmg + (common.GetTotalAP(source) * .4);
			returndmg = common.CalculateMagicDamage(target,totalDmg,source) - common.jshfhp(target,1)
			return spell.get_damage(returndmg,target,source,1)
		end,
	},
	W = {
		prediction = {
			type = 'InRange',
			range = 650,
			delay = 0.2419,
			hitchance = 0,
			out_range = 800,
			new_path = 0.500,
		},
		target_selector = 8,
		cast_spell = {
			type = 'obj',
			slot = _W,
			arg1 = function(action)
			  return action.ts.obj
			end,
		},
		slot = player:spellSlot(_W),
		ignore_obj_radius = 0,
		damage = function(target,source)
			return 0
		end,
	},
	E = {
		prediction = {
			type = 'InRange',
			range = 650,
			delay = 0.00,
			hitchance = 0,
			out_range = 650,
			new_path = 0.500,
		},
		target_selector = 8,
		cast_spell = {
			type = 'obj',
			slot = _E,
			arg1 = function(action)
			  return action.ts.obj
			end,
		},
		slot = player:spellSlot(_E),
		ignore_obj_radius = 0,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			spell_tb = {80,120,160,200,240}
			spell_level = source:spellSlot(_E).level
			spell_Dmg = spell_tb[spell_level] or 0;
			totalDmg = spell_Dmg + (common.GetTotalAP(source) * .4);
			returndmg = common.CalculateMagicDamage(target,totalDmg,source) - common.jshfhp(target,0.5)
			return spell.get_damage(returndmg,target,source,1)
		end,
	},
	R = {
		prediction = {
			type = 'InRange',
			range = 900,
			delay = 0.00,
		},
		target_selector = 8,
		cast_spell = {
			type = 'obj',
			slot = _R,
			arg1 = function(action)
			  return action.ts.obj
			end,
		},
		slot = player:spellSlot(_R),
		ignore_obj_radius = 99999,
		damage = function(target,source)
			return 0
		end,
	},
}


spell.Gwen = {
	Q = {
		prediction = {
			type = 'Linear',
			range = 450,
			delay = 0.50,
			speed = math.huge,
			width = 30,
			boundingRadiusMod = 1,
			collision = {
			  hero = false,
			  minion = false,
			  wall = false,
			},
			hitchance = 0,
			out_range = 500,
			new_path = 0.500,
		},
		target_selector = 8,
		cast_spell = {
			type = 'pos',
			slot = _Q,
			arg1 = function(action)
			  local seg = action.ts.seg
			  local obj = action.ts.obj
			  return seg.endPos:to3D(obj.y)
			end,
		},
		slot = player:spellSlot(_Q),
		ignore_obj_radius = 500,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			spell_tb = {8,10.75,13.5,16.25,19}
			spell_tb_b = {40,53.75,67.5,81.25,95}
			spell_level = source:spellSlot(_Q).level
			spell_Dmg = spell_tb[spell_level] or 0;
			spell_Dmg_b = spell_tb_b[spell_level] or 0;
			totalDmg = spell_Dmg + (common.GetTotalAP(source) * .05);
			totalDmg_b = spell_Dmg_b + (common.GetTotalAP(source) * .25);
			returndmga = common.CalculateMagicDamage(target,totalDmg,source) + totalDmg
			returndmgb = common.CalculateMagicDamage(target,totalDmg_b,source)
			local stacks = 1
			if player.buff['gwenq'] then
				stacks = player.buff['gwenq'].stacks2+1
				returndmga = returndmga * stacks
			end
			return spell.get_damage(returndmga+returndmgb,target,source,1)
		end,
	},
	W = {
		prediction = {
			type = 'InRange',
			range = 480,
			delay = 0.00,
			hitchance = 0,
			out_range = 480,
			new_path = 0.500,
		},
		target_selector = 8,
		cast_spell = {
			type = 'pos',
			slot = _W,
			arg1 = function(action)
			  return player.pos
			end,
		},
		slot = player:spellSlot(_W),
		ignore_obj_radius = 0,
		damage = function(target,source)
			return 0
		end,
	},
	E = {
		prediction = {
			type = 'InRange',
			range = 350,
			delay = 0.00,
			hitchance = 0,
			out_range = 350,
			new_path = 0.500,
		},
		target_selector = 8,
		cast_spell = {
			type = 'pos',
			slot = _E,
			arg1 = function(action)
			  return mousePos
			end,
		},
		slot = player:spellSlot(_E),
		ignore_obj_radius = 0,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			spell_tb = {10,15,20,25,30}
			spell_level = source:spellSlot(_E).level
			spell_Dmg = spell_tb[spell_level] or 0;
			totalDmg = spell_Dmg + (common.GetTotalAP(source) * .08);

			returndmg = common.CalculateMagicDamage(target,totalDmg,source)
			return spell.get_damage(returndmg,target,source,1)
		end,
	},
	R = {
		prediction = {
			type = 'Linear',
			range = 1200,
			delay = 0.50,
			speed = 1800,
			width = 60,
			boundingRadiusMod = 1,
			collision = {
			  hero = false,
			  minion = false,
			  wall = false,
			},
			hitchance = 0,
			out_range = 500,
			new_path = 0.500,
		},
		target_selector = 8,
		cast_spell = {
			type = 'pos',
			slot = _R,
			arg1 = function(action)
			  local seg = action.ts.seg
			  local obj = action.ts.obj
			  return seg.endPos:to3D(obj.y)
			end,
		},
		slot = player:spellSlot(_R),
		ignore_obj_radius = 99999,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			--local stacks = 0
			--if player.buff['gwenrrecast'] then
				--stacks = player.buff['gwenrrecast'].stacks2
			--end
			spell_tb = {30,55,80}
			spell_level = source:spellSlot(_R).level
			spell_Dmg = spell_tb[spell_level] or 0;
			totalDmg = spell_Dmg + (common.GetTotalAP(source) * .08);

			returndmg = common.CalculateMagicDamage(target,totalDmg,source)
			return spell.get_damage(returndmg,target,source,1)
		end,
	},
}


spell.Morgana = {
	Q = {
		prediction = {
			type = 'Linear',
			range = 1250,
			delay = 0.25,
			speed = 1200,
			width = 70,
			boundingRadiusMod = 0,
			collision = {
			  hero = true,
			  minion = true,
			  wall = true,
			},
			hitchance = 0,
			out_range = 1250,
			new_path = 0.500,
		},
		target_selector = 8,
		cast_spell = {
			type = 'pos',
			slot = _Q,
			arg1 = function(action)
			  local seg = action.ts.seg
			  local obj = action.ts.obj
			  return seg.endPos:to3D(obj.y)
			end,
		},
		slot = player:spellSlot(_Q),
		ignore_obj_radius = 11300,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			spell_tb = {80,135,190,245,300}
			spell_level = source:spellSlot(_Q).level
			spell_Dmg = spell_tb[spell_level] or 0;
			totalDmg = spell_Dmg + (common.GetTotalAP(source) * .9);
			returndmg = common.CalculateMagicDamage(target,totalDmg,source) + totalDmg

			return spell.get_damage(returndmg,target,source,1)
		end,
	},
	Q_collision = {
		prediction = {
			type = 'Linear',
			range = 1250,
			delay = 0.25,
			speed = 950,
			width = 99,
			boundingRadiusMod = 0,
			collision = {
			  hero = true,
			  minion = true,
			  wall = true,
			},
			hitchance = 0,
			out_range = 1250,
			new_path = 0.500,
		},
		target_selector = 8,
		cast_spell = {
			type = 'pos',
			slot = _Q,
			arg1 = function(action)
			  local seg = action.ts.seg
			  local obj = action.ts.obj
			  return seg.endPos:to3D(obj.y)
			end,
		},
		slot = player:spellSlot(_Q),
		ignore_obj_radius = 11300,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			spell_tb = {80,135,190,245,300}
			spell_level = source:spellSlot(_Q).level
			spell_Dmg = spell_tb[spell_level] or 0;
			totalDmg = spell_Dmg + (common.GetTotalAP(source) * .9);
			returndmg = common.CalculateMagicDamage(target,totalDmg,source) + totalDmg

			return spell.get_damage(returndmg,target,source,1)
		end,
	},
	W = {
		prediction = {
			type = 'Circular',
			range = 900,
			delay = 0.21,
			speed = 1200,
			radius = 270,
			boundingRadiusMod = 1,
			hitchance = 0,
			out_range = 900,
			new_path = 0.500,
		},
		target_selector = 8,
		cast_spell = {
			type = 'pos',
			slot = _W,
			arg1 = function(action)
			  local seg = action.ts.seg
			  local obj = action.ts.obj
			  return seg.endPos:to3D(obj.y)
			end,
		},
		slot = player:spellSlot(_W),
		ignore_obj_radius = 0,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			spell_tb = {6,11,16,21,26 }
			spell_level = source:spellSlot(_Q).level
			spell_Dmg = spell_tb[spell_level] or 0;
			totalDmg = spell_Dmg + (common.GetTotalAP(source) * .07);
			returndmg = common.CalculateMagicDamage(target,totalDmg,source) + totalDmg

			return spell.get_damage(returndmg,target,source,1)
		end,
	},
	E = {
		prediction = {
			type = 'obj',
			range = 800,
			delay = 0.00,
			hitchance = 0,
			out_range = 800,
			new_path = 0.500,
		},
		target_selector = 8,
		cast_spell = {
			type = 'obj',
			slot = _E,
			arg1 = function(action)
			  return action.ts.obj
			end,
		},
		slot = player:spellSlot(_E),
		ignore_obj_radius = 0,
		damage = function(target,source)
			if not target then return 0 end
			return 0
		end,
	},
	R = {
		prediction = {
			type = 'InRange',
			range = 625,
			radius = 625,
			delay = 0.35,
			dashRadius = 0,
			boundingRadiusModSource = 0,
			boundingRadiusModTarget = 0,
		},
		target_selector = 8,
		cast_spell = {
			type = 'obj',
			slot = _R,
			arg1 = function(action)
			  local seg = action.ts.seg
			  local obj = action.ts.obj
			  return obj
			end,
		},
		slot = player:spellSlot(_R),
		ignore_obj_radius = 575,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			spell_tb = {150,225,300}
			spell_level = source:spellSlot(_R).level
			spell_Dmg = spell_tb[spell_level] or 0;
			totalDmg = spell_Dmg + (common.GetTotalAP(source) * .7);
			returndmg = common.CalculateMagicDamage(target,totalDmg,source)
			return spell.get_damage(returndmg,target,source,1)
		end,
	},
}

spell.Aphelios = {
	Q = {
		prediction = {
			type = 'Linear',
			range = 1450,
			delay = 0.35,
			speed = 1800,
			width = 60,
			windup=0,
			boundingRadiusMod = 1,
			collision = {
			  hero = true,
			  minion = true,
			  wall = true,
			},
			hitchance = 0,
			out_range = 1450,
			new_path = 0.500,
		},
		target_selector = 8,
		cast_spell = {
			type = 'pos',
			slot = _Q,
			arg1 = function(action)
			  local seg = action.ts.seg
			  local obj = action.ts.obj
			  return seg.endPos:to3D(obj.y)
			end,
		},
		slot = player:spellSlot(_Q),
		ignore_obj_radius = 1450,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			--狙击步枪
			local j = math.min(7,math.max(1,math.ceil(player.levelRef / 2 ))) - 1
			local total = (60 + 16.6666*j) + (common.GetBonusAD(source) * (0.42 + 0.03*j)) + (common.GetTotalAP(source))
			--副武器被动
			local Fu_pass = 15 + common.GetBonusAD(source) * .2
			returndmg = common.CalculatePhysicalDamage(target,total+Fu_pass,source) + common.CalculateAADamage(target)



			--红刀
			local attackSpeedMod = ((source.attackSpeedMod - 1) * 100)
			local attackNum = 6 + math.floor(attackSpeedMod/100 / 3 *10 + 0.1)
			local total_red = (10 + 3.3333 * j )+(common.GetBonusAD(source) * (0.21 + 0.015 * j)) * attackNum
			returndmg_red = common.CalculatePhysicalDamage(target,total_red,source)

			--重力炮
			local total_z = (50 + 10 * j)+ (common.GetBonusAD(source) * (0.26 + 0.15 * j)) + (common.GetTotalAP(source)*.7)
			returndmg_z = common.CalculateMagicDamage(target,total_z,source)


			--喷火器
			local total_p = (25 + 6.6666 * j ) +(common.GetBonusAD(source) * (0.56 + 0.04 * j ))+ (common.GetTotalAP(source)*.7)
			returndmg_p = common.CalculatePhysicalDamage(target,total_p,source)

			--飞碟
			local total_f = (25+10*j) + (common.GetBonusAD(source) * (0.35+0.025*j)) + (common.GetTotalAP(source)*.5)
			returndmg_p = common.CalculatePhysicalDamage(target,total_f,source)


			return spell.get_damage(returndmg,target,source,2)
		end,
	},
	W = {
		prediction = {
			range = 0,
			delay = 0.0,
		},
		target_selector = 8,
		slot = player:spellSlot(_W),
		ignore_obj_radius = 0,
		damage = function(target,source)
			if not target then return 0 end

			return 0
		end,
	},
	E = {
		prediction = {

		},
		target_selector = 8,
		cast_spell = {

		},
		slot = player:spellSlot(_E),
		ignore_obj_radius = 0,
		damage = function(target,source)
			if not target then return 0 end
			return 0
		end,
	},
	R = {
		prediction = {
			type = 'Linear',
			range = 1300,
			delay = 0.6,
			speed = 2050,
			width = 150,
			boundingRadiusMod = 1,
			collision = {
			  hero = true,
			  minion = true,
			  wall = true,
			},
			hitchance = 0,
			out_range = 1450,
			new_path = 0.500,
		},
		target_selector = 8,
		cast_spell = {
			type = 'obj',
			slot = _R,
			arg1 = function(action)
			  local seg = action.ts.seg
			  local obj = action.ts.obj
			  return obj
			end,
		},
		slot = player:spellSlot(_R),
		ignore_obj_radius = 575,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			spell_tb = {150,225,300}
			spell_level = source:spellSlot(_R).level
			spell_Dmg = spell_tb[spell_level] or 0;
			totalDmg = spell_Dmg + (common.GetTotalAP(source) * .7);
			returndmg = common.CalculateMagicDamage(target,totalDmg,source)
			return spell.get_damage(returndmg,target,source,1)
		end,
	},
}


spell.Sett = {
	Q = {
		prediction = {
			type = 'InRange',
			range = player.attackRange,
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_Q),
		ignore_obj_radius = 1450,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			local dmg = {10,20,30,40,50}
			local qlevel = source:spellSlot(0).level
			local QDmg = dmg[qlevel] or 0;
			local ad = common.GetTotalAD(source)
			local bfb = math.floor(ad/100)
			local totalQAdDmg = QDmg +target.maxHealth * ((qlevel + bfb)  / 100 );
			local total =  common.CalculatePhysicalDamage(target,totalQAdDmg,source)
			return spell.get_damage(total,target,source,2)
		end,
	},
	W = {
		prediction = {
			type = 'Linear',
			range = 790,
			delay = 0.75,
			speed = math.huge,
			width = 90,
			boundingRadiusMod = 1,
			collision = {
			  hero = false,
			  minion = false,
			  wall = false,
			},
			hitchance = 0,
			out_range = 790,
			new_path = 0.500,
		},
		target_selector = 8,
		slot = player:spellSlot(_W),
		ignore_obj_radius = 0,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			local dmg = {80,100,120,140,160}
			local wlevel = source:spellSlot(1).level
			local WDmg = dmg[wlevel] or 0;
			local BonusADbfb = common.GetBonusAD(source) / 100 * 0.01

			local totalQAdDmg = (WDmg + (source.mana * (0.25+BonusADbfb)))
			return totalQAdDmg
		end,
	},
	E = {
		prediction = {
			type = 'Linear',
			range = 490,
			delay = 0.30,
			speed = math.huge,
			width = 90,
			boundingRadiusMod = 1,
			collision = {
			  hero = true,
			  minion = true,
			  wall = false,
			},
			hitchance = 0,
			out_range = 490,
			new_path = 0.500,
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_E),
		ignore_obj_radius = 0,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			local dmg = {50,70,90,110,130}
			local elevel = source:spellSlot(2).level
			local EDmg = dmg[elevel] or 0;
			local totalQAdDmg = EDmg + (common.GetTotalAD(source) *.6);
			local total =  common.CalculatePhysicalDamage(target,totalQAdDmg,source)

			return spell.get_damage(total,target,source,2)
		end,
	},
	R = {
		prediction = {
			type = 'InRange',
			range = 400,
			delay = 0.00,
		},
		target_selector = 8,
		cast_spell = {
			type = 'obj',
			slot = _R,
			arg1 = function(action)
			  local seg = action.ts.seg
			  local obj = action.ts.obj
			  return obj
			end,
		},
		slot = player:spellSlot(_R),
		ignore_obj_radius = 575,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			local dmg = {200,300,400}
			local rlevel = source:spellSlot(3).level
			local RDmg = dmg[rlevel] or 0;
			local ew = target.maxHealth - 650
			local bfb = (3 + rlevel) * 0.1
			local ewad = common.GetBonusAD(source) * bfb
			local totalQAdDmg = RDmg + (common.GetBonusAD(source) + ewad);
			local total =  common.CalculatePhysicalDamage(target,totalQAdDmg,source)
			return spell.get_damage(total,target,source,2)
		end,
	},
}
spell.Gnar = {
	Q = {
		prediction = {
			type = 'Linear',
			range = 1125,
			delay = 0.25,
			speed = 2500,
			width = 30,
			boundingRadiusMod = 1,
			collision = {
			  hero = true,
			  minion = true,
			  wall = true,
			},
			hitchance = 0,
			out_range = 1125,
			new_path = 0.500,
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_Q),
		ignore_obj_radius = 1450,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			local dmg = {5,45,85,125,165}
			local qlevel = source:spellSlot(0).level
			local QDmg = dmg[qlevel] or 0;
			local ad = common.GetTotalAD(source) * 1.15
			local totalQAdDmg = QDmg + ad
			local total =  common.CalculatePhysicalDamage(target,totalQAdDmg,source)
			return spell.get_damage(total,target,source,2)
		end,
	},
	Q1 = {
		prediction = {
			type = 'Linear',
			range = 1150,
			delay = 0.50,
			speed = 2100,
			width = 90,
			boundingRadiusMod = 1,
			collision = {
			  hero = true,
			  minion = true,
			  wall = true,
			},
			hitchance = 0,
			out_range = 1150,
			new_path = 0.500,
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_Q),
		ignore_obj_radius = 1450,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			local dmg = {5,45,85,125,165}
			local qlevel = source:spellSlot(0).level
			local QDmg = dmg[qlevel] or 0;
			local ad = common.GetTotalAD(source)
			local totalQAdDmg = QDmg + ad
			local total =  common.CalculatePhysicalDamage(target,totalQAdDmg,source)
			return spell.get_damage(total,target,source,2)
		end,
	},
	W = {
		prediction = {
			range = 0,
		},
		target_selector = 8,
		slot = player:spellSlot(_W),
		ignore_obj_radius = 0,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			local dmg = {0,10,20,30,40}
			local dmg_B = {0.06,0.08,0.1,0.12,0.14}
			local wlevel = source:spellSlot(1).level
			local WDmg = dmg[wlevel] or 0;
			local WDmg_B = dmg_B[wlevel] or 0;
			local BonusADbfb = common.GetTotalAP(source)

			local totalQAdDmg = WDmg + (target.maxHealth * WDmg_B) + BonusADbfb
			local total =  common.CalculateMagicDamage(target,totalQAdDmg,source)
			return spell.get_damage(total,target,source,1)
		end,
	},
	W1 = {
		prediction = {
			type = 'Linear',
			range = 550,
			delay = 0.60,
			speed = math.huge,
			width = 100,
			boundingRadiusMod = 1,
			collision = {
			  hero = true,
			  minion = true,
			  wall = true,
			},
			hitchance = 0,
			out_range = 550,
			new_path = 0.500,
		},
		target_selector = 8,
		slot = player:spellSlot(_W),
		ignore_obj_radius = 0,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			local dmg = {25,55,85,115,145}
			local wlevel = source:spellSlot(1).level
			local QDmg = dmg[wlevel] or 0;
			local ad = common.GetTotalAD(source)
			local totalQAdDmg = QDmg + ad
			local total =  common.CalculatePhysicalDamage(target,totalQAdDmg,source)
			return spell.get_damage(total,target,source,2)
		end,
	},
	E = {
		prediction = {
			type = 'Linear',
			range = 475,
			delay = 0.00,
			speed = math.huge,
			width = 75,
			boundingRadiusMod = 1,
			collision = {
			  hero = true,
			  minion = true,
			  wall = false,
			},
			hitchance = 0,
			out_range = 475,
			new_path = 0.500,
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_E),
		ignore_obj_radius = 0,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			local dmg = {50,85,120,155,190}
			local elevel = source:spellSlot(2).level
			local EDmg = dmg[elevel] or 0;
			local totalQAdDmg = EDmg + (target.maxHealth * 0.06)
			local total =  common.CalculatePhysicalDamage(target,totalQAdDmg,source)

			return spell.get_damage(total,target,source,2)
		end,
	},
	E1 = {
		prediction = {
			type = 'Circular',
			range = 675,
			delay = 0.25,
			speed = math.huge,
			radius = 375,
			boundingRadiusMod = 1,
			collision = {
			  hero = false, --allow to hit other heros :-)
			  minion = false,
			  wall = false,
			},
			--
			hitchance = 0,
			out_range = 675,
			new_path = 0.500,
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_E),
		ignore_obj_radius = 0,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			local dmg = {80,115,150,185,220}
			local elevel = source:spellSlot(2).level
			local EDmg = dmg[elevel] or 0;
			local totalQAdDmg = EDmg + (target.maxHealth * 0.06)
			local total =  common.CalculatePhysicalDamage(target,totalQAdDmg,source)

			return spell.get_damage(total,target,source,2)
		end,
	},
	R = {
		prediction = {
			type = 'Circular',
			range = 465,
			delay = 0.25,
			speed = math.huge,
			radius = 465,
			boundingRadiusMod = 1,
			ig = true,
			collision = {
			  hero = false, --allow to hit other heros :-)
			  minion = false,
			  wall = false,
			},
			--
			hitchance = 0,
			out_range = 465,
			new_path = 0.500,
		},
		target_selector = 8,
		cast_spell = {
			type = 'pos',
			slot = _R,
			arg1 = function(action)
			  local seg = action.ts.seg
			  local obj = action.ts.obj
			  return obj
			end,
		},
		slot = player:spellSlot(_R),
		ignore_obj_radius = 465,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			local dmg = {200,300,400}
			local rlevel = source:spellSlot(3).level
			local RDmg = dmg[rlevel] or 0;
			local BonusAD = common.GetBonusAD(source) * 0.5
			local totalQAdDmg = RDmg + BonusAD +  common.GetTotalAP(source)
			local total =  common.CalculatePhysicalDamage(target,totalQAdDmg,source)
			return spell.get_damage(total,target,source,2)
		end,
	},
}

spell.Xayah = {
	Q = {
		prediction = {
			type = 'Linear',
			range = 1099,
			delay = 0.70,
			speed = math.huge,
			width = 50,
			boundingRadiusMod = 1,
			collision = {
			  hero = false,
			  minion = false,
			  wall = true,
			},
			hitchance = 0,
			out_range = 1099,
			new_path = 0.500,
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_Q),
		ignore_obj_radius = 1450,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			local dmg = {45,60,75,90,105}
			local qlevel = source:spellSlot(0).level
			local QDmg = dmg[qlevel] or 0;
			local ad = common.GetBonusAD(source) * .5
			local totalQAdDmg = QDmg + ad
			local total =  common.CalculatePhysicalDamage(target,totalQAdDmg,source)
			return spell.get_damage(total,target,source,2)
		end,
	},

	W = {
		prediction = {
			range = 0,
			speed = 3000,
		},
		target_selector = 8,
		slot = player:spellSlot(_W),
		ignore_obj_radius = 0,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			return common.CalculateAADamage(target, source)
		end,
	},
	E = {
		prediction = {
			type = 'Linear',
			range = 9999,
			delay = 0.10,
			speed = 4000,
			width = 40,
			boundingRadiusMod = 0,
			collision = {
			  hero = true,
			  minion = false,
			  wall = false,
			},
			hitchance = 0,
			out_range = 9999,
			new_path = 0.500,
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_E),
		ignore_obj_radius = 0,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			local dmg = {50,60,70,80,90}
			local elevel = source:spellSlot(2).level
			local EDmg = dmg[elevel] or 0;
			local ad = common.GetBonusAD(source) * .6
			local totalQAdDmg = EDmg + ad
			local total =  common.CalculatePhysicalDamage(target,totalQAdDmg,source)
			return spell.get_damage(total,target,source,2)
		end,
	},
	R = {
		prediction = {
			type = 'Linear',
			range = 1060,
			delay = 0.00,
			speed = math.huge,
			width = 1,
			boundingRadiusMod = 1,
			collision = {
			  hero = false, --allow to hit other heros :-)
			  minion = false,
			  wall = false,
			},
			--
			hitchance = 0,
			out_range = 1060,
			new_path = 0.500,
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_R),
		ignore_obj_radius = 1060,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			local dmg = {125,250,375}
			local rlevel = source:spellSlot(3).level
			local RDmg = dmg[rlevel] or 0;
			local BonusAD = common.GetBonusAD(source) * 1
			local totalQAdDmg = RDmg + BonusAD
			local total =  common.CalculatePhysicalDamage(target,totalQAdDmg,source)
			return spell.get_damage(total,target,source,2)
		end,
	},
}

spell.Syndra = {
	Q = {
		prediction = {
			type = 'Circular',
			range = 790,
			delay = 0.6,
			speed = math.huge,
			radius = 125,
			boundingRadiusMod = 1,
			hitchance = 0,
			out_range = 1099,
			new_path = 0.500,
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_Q),
		ignore_obj_radius = 1450,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			local dmg = {70,105,140,175,210}
			local qlevel = source:spellSlot(0).level
			local QDmg = dmg[qlevel] or 0;
			local ap = source.totalAp * .5
			local totalQAdDmg = QDmg + ap
			local total =  common.CalculateMagicDamage(target,totalQAdDmg,source)
			return spell.get_damage(total,target,source,1)
		end,
	},

	W = {
		prediction = {
			type = 'InRange',
			delay = 0.2,
			radius = 825,
			range = 950,
			dashRadius = 0,
			boundingRadiusModSource = 0,
			boundingRadiusModTarget = 0,
		},
		target_selector = 8,
		slot = player:spellSlot(_W),
		ignore_obj_radius = 0,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			local dmg = {70,110,150,190,230}
			local stacks = 0
			if source.buff['syndrapassivestacks'] then
				stacks = source.buff['syndrapassivestacks'].stacks2
			end


			local wlevel = source:spellSlot(1).level
			local WDmg = dmg[wlevel] or 0;
			local ap = source.totalAp * .7
			local totalQAdDmg = WDmg + ap
			local total =  common.CalculateMagicDamage(target,totalQAdDmg,source)
			local true_damage = 0
			if stacks >= 60 then
				true_damage = total * (0.12 + source.totalAp * 0.0002 )
			end

			return spell.get_damage(total,target,source,1) + true_damage
		end,
	},
	W1 = {
		prediction = {
			type = 'Circular',
			delay = 0.25,
			speed = 1450,
			range = 950,
			radius = 200,
			boundingRadiusMod = 0
		},
		target_selector = 8,
		slot = player:spellSlot(_W),
		ignore_obj_radius = 0,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			local dmg = {70,110,150,190,230}
			local wlevel = source:spellSlot(1).level
			local WDmg = dmg[wlevel] or 0;
			local ap = source.totalAp * .7
			local totalQAdDmg = WDmg + ap
			local total =  common.CalculateMagicDamage(target,totalQAdDmg,source)
			return spell.get_damage(total,target,source,1)
		end,
	},
	E = {
		prediction = {
			type = 'Linear',
			range = 9999,
			delay = 0.00,
			speed = 4000,
			width = 60,
			boundingRadiusMod = 0,
			collision = {
			  hero = true,
			  minion = false,
			  wall = false,
			},
			hitchance = 0,
			out_range = 9999,
			new_path = 0.500,
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_E),
		ignore_obj_radius = 0,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			local dmg = {55,65,75,85,95}
			local elevel = source:spellSlot(2).level
			local EDmg = dmg[elevel] or 0;
			local ad = common.GetBonusAD(source) * .6
			local totalQAdDmg = EDmg + ad
			local total =  common.CalculatePhysicalDamage(target,totalQAdDmg,source)
			return spell.get_damage(total,target,source,1)
		end,
	},
	R = {
		prediction = {
			type = 'InRange',
			delay = 0,
			radius = 675,
			width = 75,
			speed = 1100,
			range = 675,
			dashRadius = 0,
			boundingRadiusModSource = 0,
			boundingRadiusModTarget = 0,
			collision = {
				wall = true,
			},
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_R),
		ignore_obj_radius = 1060,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			local stacks = 0
			if source.buff['syndrapassivestacks'] then
				stacks = source.buff['syndrapassivestacks'].stacks2
			end

			local dmg = {90,130,170}
			local rlevel = source:spellSlot(3).level
			local ap = source.totalAp * .17
			local RDmg = dmg[rlevel] or 0;
			local totalQAdDmg = RDmg + ap
			local total =  common.CalculateMagicDamage(target,totalQAdDmg,source)
			local d = spell.get_damage(total,target,source,1)

			return d
		end,
	},
}
spell.Akshan = {
	Q = {
		prediction = {
			type = 'Linear',
			range = 800,
			delay = 0.25,
			speed = 1500,
			width = 60,
			boundingRadiusMod = 1,
			collision = {
			  hero = true,
			  minion = true,
			  wall = false,
			},
			hitchance = 0,
			out_range = 850,
			new_path = 0.500,
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_Q),
		ignore_obj_radius = 850,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			local dmg = {5, 25,45,65,85}
			local qlevel = source:spellSlot(0).level
			local QDmg = dmg[qlevel] or 0;
			local ad = source.totalAd * .8
			local totalQAdDmg = QDmg + ad
			local total =  common.CalculatePhysicalDamage(target,totalQAdDmg,source)
			return spell.get_damage(total,target,source,2)
		end,
	},

	W = {
		prediction = {
			type = 'InRange',
			delay = 0.50,
			radius = 800,
			range = 800,
			dashRadius = 0,
			boundingRadiusModSource = 0,
			boundingRadiusModTarget = 0,
		},
		target_selector = 8,
		slot = player:spellSlot(_W),
		ignore_obj_radius = 0,
		damage = function(target,source)
			if not target then return 0 end
			return 0
		end,
	},
	E = {
		prediction = {
			type = 'InRange',
			range = 800,
			delay = 0.00,
			speed = 2500,
			width = 60,
			boundingRadiusMod = 0,
			collision = {
			  hero = true,
			  minion = false,
			  wall = false,
			},
			hitchance = 0,
			out_range = 800,
			new_path = 0.500,
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_E),
		ignore_obj_radius = 0,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			local dmg = {30,55,80,105,130 }
			local elevel = source:spellSlot(2).level
			local EDmg = dmg[elevel] or 0;
			local ad = common.GetBonusAD(source) * .1
			local totalQAdDmg = EDmg + ad
			local total =  common.CalculatePhysicalDamage(target,totalQAdDmg,source)
			return spell.get_damage(total,target,source,2)
		end,
	},
	R = {
		prediction = {
			type = 'Linear',
			range = 2500,
			delay = 2.5,
			speed = 3200,
			width = 60,
			boundingRadiusMod = 1,
			collision = {
			  hero = true,
			  minion = true,
			  wall = true,
			},
			hitchance = 0,
			out_range = 2500,
			new_path = 0.500,
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_R),
		ignore_obj_radius = 1060,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			local dmg = {20,25,30}
			local rlevel = source:spellSlot(3).level
			local ad = source.totalAd * .1
			local RDmg = dmg[rlevel] or 0;
			local totalQAdDmg = RDmg + ad
			local total =  common.CalculatePhysicalDamage(target,totalQAdDmg,source)
			return spell.get_damage(total,target,source,2)
		end,
	},
}

spell.XinZhao = {
	Q = {
		prediction = {
			type = 'Linear',
			range = 850,
			delay = 0.25,
			speed = 1500,
			width = 120,
			boundingRadiusMod = 1,
			collision = {
			  hero = true,
			  minion = true,
			  wall = false,
			},
			hitchance = 0,
			out_range = 850,
			new_path = 0.500,
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_Q),
		ignore_obj_radius = 850,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			local dmg = {5, 25,45,65,85}
			local qlevel = source:spellSlot(0).level
			local QDmg = dmg[qlevel] or 0;
			local ad = source.totalAd * .8
			local totalQAdDmg = QDmg + ad
			local total =  common.CalculatePhysicalDamage(target,totalQAdDmg,source)
			return spell.get_damage(total,target,source,2)
		end,
	},

	W = {
		prediction = {
			type = 'Linear',
			range = 940,
			delay = 0.50,
			speed = 6250,
			width = 30,
			boundingRadiusMod = 1,
			collision = {
			  hero = true,
			  minion = true,
			  wall = false,
			},
			hitchance = 0,
			out_range = 940,
			new_path = 0.500,
		},
		target_selector = 8,
		slot = player:spellSlot(_W),
		ignore_obj_radius = 0,
		damage = function(target,source)
			if not target then return 0 end
			return 0
		end,
	},
	E = {
		prediction = {
			type = 'Linear',
			range = 800,
			delay = 0.00,
			speed = 2500,
			width = 60,
			boundingRadiusMod = 0,
			collision = {
			  hero = true,
			  minion = false,
			  wall = false,
			},
			hitchance = 0,
			out_range = 800,
			new_path = 0.500,
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_E),
		ignore_obj_radius = 0,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			local dmg = {30,55,80,105,130 }
			local elevel = source:spellSlot(2).level
			local EDmg = dmg[elevel] or 0;
			local ad = common.GetBonusAD(source) * .1
			local totalQAdDmg = EDmg + ad
			local total =  common.CalculatePhysicalDamage(target,totalQAdDmg,source)
			return spell.get_damage(total,target,source,2)
		end,
	},
	R = {
		prediction = {
			type = 'Linear',
			range = 2500,
			delay = 2.5,
			speed = 3200,
			width = 60,
			boundingRadiusMod = 1,
			collision = {
			  hero = true,
			  minion = true,
			  wall = true,
			},
			hitchance = 0,
			out_range = 2500,
			new_path = 0.500,
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_R),
		ignore_obj_radius = 1060,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			local dmg = {20,25,30}
			local rlevel = source:spellSlot(3).level
			local ad = source.totalAd * .1
			local RDmg = dmg[rlevel] or 0;
			local totalQAdDmg = RDmg + ad
			local total =  common.CalculatePhysicalDamage(target,totalQAdDmg,source)
			return spell.get_damage(total,target,source,2)
		end,
	},
}

spell.Vex = {
	Q = {
		prediction = {
			type = 'Linear',
			range = 1100,
			delay = 0.45,
			speed = 1500,
			width = 50,
			boundingRadiusMod = 0,
			collision = {
			  hero = false,
			  minion = false,
			  wall = true,
			},
			hitchance = 0,
			out_range = 1100,
			new_path = 0.500,
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_Q),
		ignore_obj_radius = 1200,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			local dmg = {60,110,160,210,260}
			local qlevel = source:spellSlot(0).level
			local QDmg = dmg[qlevel] or 0;
			local ap = source.totalAp * .6
			local totalQDmg = QDmg + ap
			local total =  common.CalculateMagicDamage(target,totalQDmg,source)
			return spell.get_damage(total,target,source,1)
		end,
	},

	W = {
		prediction = {
			type = 'InRange',
			range = 475 ,
			radius = 475 ,
			delay = 0.00,
			dashRadius = 0,
			boundingRadiusModSource = 0,
			boundingRadiusModTarget = 0,
		},
		target_selector = 8,
		slot = player:spellSlot(_W),
		ignore_obj_radius = 0,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			local dmg = {80,120,160,200,240}
			local wlevel = source:spellSlot(1).level
			local WDmg = dmg[wlevel] or 0;
			local ap = source.totalAp * .3
			local totalQDmg = WDmg + ap
			local total =  common.CalculateMagicDamage(target,totalQDmg,source)
			return spell.get_damage(total,target,source,1)
		end,
	},
	E = {
		prediction = {
			type = 'Circular',
			range = 750,
			delay = 0.25,
			speed = 1300,
			radius = 50,
			boundingRadiusMod = 0,
			--
			hitchance = 0,
			out_range = 800,
			new_path = 0.500,
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_E),
		ignore_obj_radius = 0,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			local dmg = {80,120,160,200,240}
			local dmg_level = {0.4,0.45,0.5,0.55,0.60}
			local elevel = source:spellSlot(2).level
			local EDmg = dmg[elevel] or 0;
			local E_ap = dmg_level[elevel] or 0;
			local ap = source.totalAp * E_ap
			local totalQDmg = EDmg + ap
			local total =  common.CalculateMagicDamage(target,totalQDmg,source)
			return spell.get_damage(total,target,source,1)
		end,
	},
	R = {
		prediction = {
			type = 'Linear',
			range = 2000,
			delay = 0.25,
			speed = 1600,
			width = 60,
			boundingRadiusMod = 1,
			collision = {
			  hero = true,
			  minion = true,
			  wall = true,
			},
			hitchance = 0,
			out_range = 2000,
			new_path = 0.500,
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_R),
		ignore_obj_radius = 2000,
		damage = function(target,source,stacks)
			if not target then return 0 end
			local source = source or player
			local dmg = {75,125,175}
			local rlevel = source:spellSlot(3).level
			local RDmg = dmg[rlevel] or 0;
			local ap = source.totalAp * .2
			local totalQDmg = RDmg + ap
			local total_a =  common.CalculateMagicDamage(target,totalQDmg,source)
			local total = total_a
			if not stacks then
				local dmg_b = {150,250,350}
				local RDmg_b = dmg_b[rlevel] or 0;
				local ap_b = source.totalAp * .5
				local totalQDmg_b = RDmg_b + ap_b
				local total_b =  common.CalculateMagicDamage(target,totalQDmg_b,source)
				total = total + total_b
			end
			return spell.get_damage(total,target,source,1)
		end,
	},
}

spell.Leona = {
	Q = {
		prediction = {
			type = 'InRange',
			range = 125 ,
			radius = 125 ,
			delay = 0.00,
			dashRadius = 0,
			boundingRadiusModSource = 1,
			boundingRadiusModTarget = 1,
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_Q),
		ignore_obj_radius = 1200,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			local dmg = {10,35,60 ,85 ,110}
			local qlevel = source:spellSlot(0).level
			local QDmg = dmg[qlevel] or 0;
			local ap = source.totalAp * .3
			local totalQDmg = QDmg + ap
			local total =  common.CalculateMagicDamage(target,totalQDmg,source)
			return spell.get_damage(total,target,source,1)
		end,
	},

	W = {
		prediction = {
			type = 'InRange',
			range = 450 ,
			radius = 450 ,
			delay = 0.00,
			dashRadius = 0,
			boundingRadiusModSource = 0,
			boundingRadiusModTarget = 0,
		},
		target_selector = 8,
		slot = player:spellSlot(_W),
		ignore_obj_radius = 0,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			local dmg = {50,90,130,170,210}
			local wlevel = source:spellSlot(1).level
			local WDmg = dmg[wlevel] or 0;
			local ap = source.totalAp * .4
			local totalQDmg = WDmg + ap
			local total =  common.CalculateMagicDamage(target,totalQDmg,source)
			return spell.get_damage(total,target,source,1)
		end,
	},
	E = {
		prediction = {
			type = 'Linear',
			range = 900,
			delay = 0.25,
			speed = 2000,
			width = 70,
			boundingRadiusMod = 1,
			collision = {
			  hero = false, --allow to hit other heros :-)
			  minion = false,
			  wall = false,
			},
			--
			hitchance = 0,
			out_range = 900,
			new_path = 0.500,
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_E),
		ignore_obj_radius = 0,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			local dmg = {50,90,130,170,210}
			local elevel = source:spellSlot(2).level
			local EDmg = dmg[elevel] or 0;
			local ap = source.totalAp * .4
			local totalQDmg = EDmg + ap
			local total =  common.CalculateMagicDamage(target,totalQDmg,source)
			return spell.get_damage(total,target,source,1)
		end,
	},
	E2 = {
		prediction = {
			type = 'Linear',
			range = 1200,
			delay = 0.25,
			speed = 2000,
			width = 70,
			boundingRadiusMod = 1,
			collision = {
			  hero = false, --allow to hit other heros :-)
			  minion = false,
			  wall = false,
			},
			--
			hitchance = 0,
			out_range = 900,
			new_path = 0.500,
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_E),
		ignore_obj_radius = 0,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			local dmg = {50,90,130,170,210}
			local elevel = source:spellSlot(2).level
			local EDmg = dmg[elevel] or 0;
			local ap = source.totalAp * .4
			local totalQDmg = EDmg + ap
			local total =  common.CalculateMagicDamage(target,totalQDmg,source)
			return spell.get_damage(total,target,source,1)
		end,
	},
	R = {
		prediction = {
			type = 'Circular',
			range = 1200,
			delay = 1,
			speed = math.huge,
			radius = 50,
			boundingRadiusMod = 0,
			collision = {
			  hero = true,
			  minion = true,
			  wall = true,
			},
			hitchance = 0,
			out_range = 1200,
			new_path = 0.500,
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_R),
		ignore_obj_radius = 1200,
		damage = function(target,source,stacks)
			if not target then return 0 end
			local source = source or player
			local dmg = {100,175,250}
			local rlevel = source:spellSlot(3).level
			local RDmg = dmg[rlevel] or 0;
			local ap = source.totalAp * .8
			local totalQDmg = RDmg + ap
			local total_a =  common.CalculateMagicDamage(target,totalQDmg,source)
			return spell.get_damage(total_a,target,source,1)
		end,
	},
}

spell.Nami = {
	Q = {
		prediction = {
			type = 'Circular',
			range = 850,
			delay = 0.75,
			speed = math.huge,
			radius = 30,
			boundingRadiusMod = 0,
			collision = {
			  hero = false, --allow to hit other heros :-)
			  minion = false,
			  wall = false,
			},
			--
			hitchance = 0,
			out_range = 900,
			new_path = 0.500,
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_Q),
		ignore_obj_radius = 1200,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			local dmg = {75,130,185,240,295}
			local qlevel = source:spellSlot(0).level
			local QDmg = dmg[qlevel] or 0;
			local ap = source.totalAp * .5
			local totalQDmg = QDmg + ap
			local total =  common.CalculateMagicDamage(target,totalQDmg,source)
			return spell.get_damage(total,target,source,1)
		end,
	},

	W = {
		prediction = {
			type = 'InRange',
			range = 725 ,
			radius = 725 ,
			delay = 0.00,
			dashRadius = 0,
			boundingRadiusModSource = 1,
			boundingRadiusModTarget = 0,
		},
		target_selector = 8,
		slot = player:spellSlot(_W),
		ignore_obj_radius = 0,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			local dmg = {70 ,110,150,190,230}
			local wlevel = source:spellSlot(1).level
			local WDmg = dmg[wlevel] or 0;
			local ap = source.totalAp * .5
			local totalQDmg = WDmg + ap
			local total =  common.CalculateMagicDamage(target,totalQDmg,source)
			return spell.get_damage(total,target,source,1)
		end,
	},
	E = {
		prediction = {
			type = 'InRange',
			range = 800 ,
			radius = 800 ,
			delay = 0.00,
			dashRadius = 0,
			boundingRadiusModSource = 0,
			boundingRadiusModTarget = 0,
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_E),
		ignore_obj_radius = 0,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			local dmg = {25,40,55,70,85}
			local elevel = source:spellSlot(2).level
			local EDmg = dmg[elevel] or 0;
			local ap = source.totalAp * .2
			local totalQDmg = EDmg + ap
			local total =  common.CalculateMagicDamage(target,totalQDmg,source)
			return spell.get_damage(total,target,source,1)
		end,
	},
	R = {
		prediction = {
			type = 'Linear',
			range = 2500,
			delay = 0.5,
			speed = 850,
			width = 250,
			boundingRadiusMod = 0,
			collision = {
			  hero = true,
			  minion = true,
			  wall = true,
			},
			hitchance = 0,
			out_range = 1200,
			new_path = 0.500,
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_R),
		ignore_obj_radius = 1200,
		damage = function(target,source,stacks)
			if not target then return 0 end
			local source = source or player
			local dmg = {150,250,350}
			local rlevel = source:spellSlot(3).level
			local RDmg = dmg[rlevel] or 0;
			local ap = source.totalAp * .6
			local totalQDmg = RDmg + ap
			local total_a =  common.CalculateMagicDamage(target,totalQDmg,source)
			return spell.get_damage(total_a,target,source,1)
		end,
	},
}
spell.Teemo = {
	Q = {
		prediction = {
			type = 'InRange',
			range = 680 ,
			radius = 680 ,
			delay = 0.00,
			dashRadius = 0,
			boundingRadiusModSource = 0,
			boundingRadiusModTarget = 0,
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_Q),
		ignore_obj_radius = 1200,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			local dmg = {80,125,170,215,260}
			local qlevel = source:spellSlot(0).level
			local QDmg = dmg[qlevel] or 0;
			local ap = source.totalAp * .8
			local totalQDmg = QDmg + ap
			local total =  common.CalculateMagicDamage(target,totalQDmg,source)
			return spell.get_damage(total,target,source,1)
		end,
	},

	W = {
		prediction = {
			type = 'InRange',
			range = 0 ,
			radius = 0 ,
			delay = 0.00,
			dashRadius = 0,
			boundingRadiusModSource = 0,
			boundingRadiusModTarget = 0,
		},
		target_selector = 8,
		slot = player:spellSlot(_W),
		ignore_obj_radius = 0,
		damage = function(target,source)
			if not target then return 0 end
			return 0
		end,
	},
	E = {
		prediction = {
			type = 'InRange',
			range =   0,
			radius = 0 ,
			delay = 0.00,
			dashRadius = 0,
			boundingRadiusModSource = 0,
			boundingRadiusModTarget = 0,
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_E),
		ignore_obj_radius = 0,
		damage = function(target,source)
			if not target then return 0 end
			return 0
		end,
	},
	R = {
		prediction = {
			type = 'Circular',
			range = 400,
			delay = 0.5,
			speed = math.huge,
			radius = 75,
			boundingRadiusMod = 1,
			collision = {
			  hero = true,
			  minion = true,
			  wall = true,
			},
			hitchance = 0,
			out_range = 900,
			new_path = 0.500,
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_R),
		ignore_obj_radius = 400,
		damage = function(target,source,stacks)
			if not target then return 0 end
			local source = source or player
			local dmg = {200,325,450}
			local rlevel = source:spellSlot(3).level
			local RDmg = dmg[rlevel] or 0;
			local ap = source.totalAp * .55
			local totalQDmg = RDmg + ap
			local total_a =  common.CalculateMagicDamage(target,totalQDmg,source)
			return spell.get_damage(total_a,target,source,1)
		end,
	},
}
spell.Ezreal = {
	Q = {
		prediction = {
			type = 'Linear',
			range = 1100 ,
			delay = 0.25,
			width = 60,
			speed = 2000,
			boundingRadiusMod = 1,
			collision = {
			  hero = false,
			  minion = true,
			},
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_Q),
		ignore_obj_radius = 1150,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			local dmg = {20,45,70,95,120}
			local qlevel = source:spellSlot(0).level
			local QDmg = dmg[qlevel] or 0;
			local ap = source.totalAp * .15
			local ad = source.totalAd * 1.3
			local totalQDmg = QDmg + ap + ad
			local total =  common.CalculatePhysicalDamage(target,totalQDmg,source)
			return spell.get_damage(total,target,source,2)
		end,
	},

	W = {
		prediction = {
			type = 'Linear',
			range = 1150,
			delay = 0.25,
			width = 80,
			speed = 1600,
			boundingRadiusMod = 1,
		},
		target_selector = 8,
		slot = player:spellSlot(_W),
		ignore_obj_radius = 0,
		damage = function(target,source)
			if not target then return 0 end
			return 0
		end,
	},
	E = {
		prediction = {
			type = 'InRange',
			range =   0,
			radius = 0 ,
			delay = 0.00,
			dashRadius = 0,
			boundingRadiusModSource = 0,
			boundingRadiusModTarget = 0,
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_E),
		ignore_obj_radius = 0,
		damage = function(target,source)
			if not target then return 0 end
			return 0
		end,
	},
	R = {
		prediction = {
			type = 'Linear',
			range = 9999,
			delay = 1.00,
			speed = 2000,
			width = 160,
			boundingRadiusMod = 1,
			collision = {
			  hero = true,
			  minion = true,
			  wall = true,
			},
			hitchance = 0,
			out_range = 9999,
			new_path = 0.500,
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_R),
		ignore_obj_radius = 9999,
		damage = function(target,source,stacks)
			if not target then return 0 end
			local source = source or player
			local dmg = {350,500,650}
			local rlevel = source:spellSlot(3).level
			local RDmg = dmg[rlevel] or 0;
			local ad = common.GetBonusAD(source)
			local ap = source.totalAd * .9
			local totalQDmg = RDmg + ap + ad
			local total =  common.CalculatePhysicalDamage(target,totalQDmg,source)
			return spell.get_damage(total,target,source,2)
		end,
	},
}

spell.Ahri = {
	Q = {
		prediction = {
			type = 'Linear',
			range = 900 ,
			delay = 0.25,
			width = 100,
			speed = 1550,
			boundingRadiusMod = 0,
			collision = {
			  hero = false,
			  minion = false,
			},
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_Q),
		ignore_obj_radius = 900,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			local dmg = {40,65,90,115,140}
			local qlevel = source:spellSlot(0).level
			local QDmg = dmg[qlevel] or 0;
			local ap = source.totalAp * .45
			local totalQDmg = QDmg + ap
			local total =  common.CalculateMagicDamage(target,totalQDmg,source)
			return spell.get_damage(total,target,source,1)
		end,
	},
	W = {
		prediction = {
			type = 'InRange',
			range = 550,
			radius = 550,
			delay = 0.00,
			dashRadius = 0,
			boundingRadiusModSource = 0,
			boundingRadiusModTarget = 0,
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_W),
		ignore_obj_radius = 550,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			local dmg = {40,65,90,115,140}
			local qlevel = source:spellSlot(0).level
			local QDmg = dmg[qlevel] or 0;
			local ap = source.totalAp * .3
			local totalQDmg = QDmg + ap
			local total =  common.CalculateMagicDamage(target,totalQDmg,source)
			return spell.get_damage(total,target,source,1)
		end,
	},
	E = {
		prediction = {
			type = 'Linear',
			range = 1000 ,
			delay = 0.25,
			width = 100,
			speed = 1550,
			boundingRadiusMod = 0,
			collision = {
			  hero = false,
			  minion = false,
			},
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_E),
		ignore_obj_radius = 550,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			local dmg = {72,108,144,180,216}
			local qlevel = source:spellSlot(0).level
			local QDmg = dmg[qlevel] or 0;
			local ap = source.totalAp * .48
			local totalQDmg = QDmg + ap
			local total =  common.CalculateMagicDamage(target,totalQDmg,source)
			return spell.get_damage(total,target,source,1)
		end,
	},
	R = {
		prediction = {
			type = 'InRange',
			range = 550,
			radius = 550,
			delay = 0.00,
			dashRadius = 0,
			boundingRadiusModSource = 0,
			boundingRadiusModTarget = 0,
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_R),
		ignore_obj_radius = 550,
		damage = function(target,source)
			if not target then return 0 end
			return 0
		end,
	},

}

spell.Qiyana = {
	Q = {
		prediction = {
			type = 'Linear',
			range = 525,
			delay = 0.25,
			width = 70,
			speed = 1600,
			boundingRadiusMod = 0,
			collision = {
			  hero = true,
			  minion = true,
			},
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_Q),
		ignore_obj_radius = 525,
		damage = function(target,source,check)
			if not target then return 0 end
			local source = source or player
			local dmg = {50,85,120,155,190}
			local qlevel = source:spellSlot(0).level
			local QDmg = dmg[qlevel] or 0;
			local ad = common.GetBonusAD(source) * .75
			local totalQDmg = QDmg + ad
			if source:spellSlot(_Q).name:find("QiyanaQ_Rock") or check then
				if common.GetPercentHealth(target) < 50 then
					local QBoud = {30 , 48 , 66 , 84 , 102}
					local Qdamage = QBoud[qlevel] or 0
					Qdamage = Qdamage +  (common.GetBonusAD() * .45)
					totalQDmg = totalQDmg + Qdamage
				end
			end
			local total =  common.CalculatePhysicalDamage(target,totalQDmg,source)
			return spell.get_damage(total,target,source,2)
		end,
	},
	W = {
		prediction = {
			type = 'InRange',
			range = 1100,
			radius = 1100,
			delay = 0.00,
			dashRadius = 0,
			boundingRadiusModSource = 0,
			boundingRadiusModTarget = 0,
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_W),
		ignore_obj_radius = 1100,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			local dmg = {8,22,36,50,64}
			local wlevel = source:spellSlot(1).level
			local QDmg = dmg[wlevel] or 0;
			local ad = common.GetBonusAD(source) * .1
			local ap = source.totalAp * .45
			local totalQDmg = QDmg + ap + ad
			local total =  common.CalculateMagicDamage(target,totalQDmg,source)
			return spell.get_damage(total,target,source,1)
		end,
	},
	E = {
		prediction = {
			type = 'InRange',
			range = 650,
			radius = 650,
			delay = 0.00,
			dashRadius = 0,
			boundingRadiusModSource = 0,
			boundingRadiusModTarget = 0,
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_E),
		ignore_obj_radius = 650,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			local dmg = {50,90,130,170,210}
			local elevel = source:spellSlot(2).level
			local QDmg = dmg[elevel] or 0;
			local ad = common.GetBonusAD(source) * .5
			local totalQDmg = QDmg + ad
			local total =  common.CalculatePhysicalDamage(target,totalQDmg,source)
			return spell.get_damage(total,target,source,2)
		end,
	},
	R = {
		prediction = {
			type = 'Linear',
			range = 875,
			delay = 0.25,
			width = 140,
			speed = 2000,
			boundingRadiusMod = 0,
			collision = {
			  hero = false,
			  minion = false,
			},
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_R),
		ignore_obj_radius = 875,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			local dmg = {100,200,300}
			local dmg_2 = {500,750,1000}
			local rlevel = source:spellSlot(3).level
			local RDmg = dmg[rlevel] or 0;
			local RDmg2 = dmg_2[rlevel] or 0;
			local ad = common.GetBonusAD(source) * 1.7
			local health_dmg = (math.min(RDmg2,((target.maxHealth) * .1) ))
			local totalDmg = RDmg + ad + health_dmg
			local total =  common.CalculatePhysicalDamage(target,totalDmg,source)
			return spell.get_damage(total,target,source,2)
		end,
	},

}
spell.Zeri = {
	Q = {
		prediction = {
			type = 'Linear',
			range = 750,
			delay = 0.00,
			width = 30,
			speed = 2600,
			boundingRadiusMod = 0,
			ig = true,
			collision = {
			  hero = true,
			  minion = true,
			},
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_Q),
		ignore_obj_radius = 825,
		damage = function(target,source,check)
			if not target then return 0 end
			local source = source or player
			local dmg = {15,17,19,21,23}
			local ad_tb = {1.04,1.08,1.12,1.16,1.2}
			local qlevel = source:spellSlot(0).level
			local QDmg = dmg[qlevel] or 0;
			local ad_x = ad_tb[qlevel] or 0;
			local ad = common.GetTotalAD(source) * ad_x
			local totalQDmg = QDmg + ad
			local total =  common.CalculatePhysicalDamage(target,totalQDmg,source)
			return spell.get_damage(total,target,source,2)
		end,
	},
	W = {
		prediction = {
			type = 'Linear',
			range = 1150,
			delay = 0.51562863588333,
			width = 40,
			speed = 2200,
			boundingRadiusMod = 0,
			collision = {
			  hero = true,
			  minion = true,
			},
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_W),
		ignore_obj_radius = 1100,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			local dmg = {20,60,100,140,180}
			local wlevel = source:spellSlot(1).level
			local QDmg = dmg[wlevel] or 0;
			local ad = common.GetTotalAD(source) * 1.3
			local ap = source.totalAp * .25
			local totalQDmg = QDmg + ap + ad
			local total =  common.CalculateMagicDamage(target,totalQDmg,source)
			return spell.get_damage(total,target,source,1)
		end,
	},
	E = {
		prediction = {
			type = 'InRange',
			range = 650,
			radius = 650,
			delay = 0.00,
			dashRadius = 0,
			boundingRadiusModSource = 0,
			boundingRadiusModTarget = 0,
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_E),
		ignore_obj_radius = 650,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			local dmg = {50,90,130,170,210}
			local elevel = source:spellSlot(2).level
			local QDmg = dmg[elevel] or 0;
			local ad = common.GetBonusAD(source) * .5
			local totalQDmg = QDmg + ad
			local total =  common.CalculatePhysicalDamage(target,totalQDmg,source)
			return spell.get_damage(total,target,source,2)
		end,
	},
	R = {
		prediction = {
			type = 'InRange',
			range = 780,
			radius = 780,
			delay = 0.00,
			dashRadius = 0,
			boundingRadiusModSource = 0,
			boundingRadiusModTarget = 0,
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_R),
		ignore_obj_radius = 825,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			local dmg = {175,275,375}
			local rlevel = source:spellSlot(3).level
			local RDmg = dmg[rlevel] or 0;
			local ad = common.GetBonusAD(source) * 1
			local ap = source.totalAp * 1.1
			local totalDmg = RDmg + ad + ap
			local total =  common.CalculateMagicDamage(target,totalDmg,source)
			return spell.get_damage(total,target,source,1)
		end,
	},

}
spell.Renata = {
	Q = {
		prediction = {
			type = 'Linear',
			range = 850,
			delay = 0.25,
			width = 70,
			speed = 1450,
			boundingRadiusMod = 0,
			collision = {
			  hero = true,
			  minion = true,
			  wall = true,
			},
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_Q),
		ignore_obj_radius = 825,
		damage = function(target,source,check)
			if not target then return 0 end
			local source = source or player
			local dmg = {80,125,170,215,260}
			local qlevel = source:spellSlot(0).level
			local QDmg = dmg[qlevel] or 0;
			local ap = common.GetTotalAP(source) * .8
			local totalQDmg = QDmg + ap
			local total =  common.CalculateMagicDamage(target,totalQDmg,source)
			return spell.get_damage(total,target,source,1)
		end,
	},
	W = {
		prediction = {
			type = 'InRange',
			range = 800,
			radius = 800,
			delay = 0.00,
			dashRadius = 0,
			boundingRadiusModSource = 0,
			boundingRadiusModTarget = 0,
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_W),
		ignore_obj_radius = 800,
		damage = function(target,source)
			return 0
		end,
	},
	E = {
		prediction = {
			type = 'Circular',
			range = 800,
			delay = 0.679,
			radius = 90,
			speed = math.huge,
			boundingRadiusMod = 0,
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_E),
		ignore_obj_radius = 650,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			local dmg = {65,95,125,155,185}
			local elevel = source:spellSlot(2).level
			local QDmg = dmg[elevel] or 0;
			local ap = common.GetTotalAP(source) * .55
			local totalQDmg = QDmg + ap
			local total =  common.CalculateMagicDamage(target,totalQDmg,source)
			return spell.get_damage(total,target,source,1)
		end,
	},
	R = {
		prediction = {
			type = 'Linear',
			range = 2000,
			delay = 0.75,
			width = 250,
			speed = 1000,
			boundingRadiusMod = 0,
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_R),
		ignore_obj_radius = 2000,
		damage = function(target,source)
			return 0
		end,
	},

}
spell.Karma = {
	Q = {
		prediction = {
			type = 'Linear',
			range = 950,
			delay = 0.25,
			width = 60,
			speed = 1700,
			boundingRadiusMod = 0,
			collision = {
			  hero = true,
			  minion = true,
			  wall = true,
			},
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_Q),
		ignore_obj_radius = 950,
		damage = function(target,source,check)
			if not target then return 0 end
			local source = source or player
			local dmg = {70,120,170,220,270}
			local qlevel = source:spellSlot(0).level
			local QDmg = dmg[qlevel] or 0;
			local ap = common.GetTotalAP(source) * .4
			local totalQDmg = QDmg + ap
			local total =  common.CalculateMagicDamage(target,totalQDmg,source)
			return spell.get_damage(total,target,source,1)
		end,
	},
	Q_collision = {
		prediction = {
			type = 'Linear',
			range = 950,
			delay = 0.25,
			width = 120,
			speed = 1700,
			boundingRadiusMod = 0,
			collision = {
			  hero = true,
			  minion = true,
			  wall = true,
			},
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_Q),
		ignore_obj_radius = 950,
		damage = function(target,source,check)
			if not target then return 0 end
			local source = source or player
			local dmg = {70,120,170,220,270}
			local qlevel = source:spellSlot(0).level
			local QDmg = dmg[qlevel] or 0;
			local ap = common.GetTotalAP(source) * .4
			local totalQDmg = QDmg + ap
			local total =  common.CalculateMagicDamage(target,totalQDmg,source)
			return spell.get_damage(total,target,source,1)
		end,
	},
	W = {
		prediction = {
			type = 'InRange',
			range = 675,
			radius = 675,
			delay = 0.00,
			dashRadius = 0,
			boundingRadiusModSource = 0,
			boundingRadiusModTarget = 0,
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_W),
		ignore_obj_radius = 675,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			local dmg = {40,65,90,115,140}
			local qlevel = source:spellSlot(0).level
			local QDmg = dmg[qlevel] or 0;
			local ap = common.GetTotalAP(source) * .45
			local totalQDmg = QDmg + ap
			local total =  common.CalculateMagicDamage(target,totalQDmg,source)
			return spell.get_damage(total,target,source,1)
		end,
	},
	E = {
		prediction = {
			type = 'InRange',
			range = 800,
			radius = 800,
			delay = 0.00,
			dashRadius = 0,
			boundingRadiusModSource = 0,
			boundingRadiusModTarget = 0,
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_E),
		ignore_obj_radius = 650,
		damage = function(target,source)
			return 0
		end,
	},
	R = {
		prediction = {
			type = 'InRange',
			range = 0,
			radius = 0,
			delay = 0.00,
			dashRadius = 0,
			boundingRadiusModSource = 0,
			boundingRadiusModTarget = 0,
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_R),
		ignore_obj_radius = 2000,
		damage = function(target,source)
			return 0
		end,
	},

}
spell.Cassiopeia = {
	Q = {
		prediction = {
			type = 'Circular',
			range = 850,
			delay = 0.75,
			radius = 50,
			speed = math.huge,
			boundingRadiusMod = 0,
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_Q),
		ignore_obj_radius = 850,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			local dmg = {75,110,145,180,215}
			local qlevel = source:spellSlot(0).level
			local QDmg = dmg[qlevel] or 0;
			local ap = common.GetTotalAP(source) * .9
			local totalQDmg = QDmg + ap
			local total =  common.CalculateMagicDamage(target,totalQDmg,source)
			return spell.get_damage(total,target,source,1)
		end,
	},
	W = {
		prediction = {
			type = 'Circular',
			range = 700,
			delay = 0.70,
			radius = 50,
			speed = 3000,
			boundingRadiusMod = 0,
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_W),
		ignore_obj_radius = 675,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			local dmg = {20,25,30,35,40}
			local qlevel = source:spellSlot(1).level
			local QDmg = dmg[qlevel] or 0;
			local ap = common.GetTotalAP(source) * .15
			local totalQDmg = QDmg + ap
			local total =  common.CalculateMagicDamage(target,totalQDmg,source)
			return spell.get_damage(total,target,source,1)
		end,
	},
	E = {
		prediction = {
			type = 'InRange',
			range = 700,
			radius = 700,
			speed = 2500,
			delay = 0.125,
			dashRadius = 0,
			boundingRadiusModSource = 0,
			boundingRadiusModTarget = 1,
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_E),
		ignore_obj_radius = 650,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			local lever = source.levelRef

			local elevel = source:spellSlot(2).level
			if lever > 18 then lever = 18 end
			local Edmg = 48 + 4 * lever
			local totalEDmg = Edmg + (common.GetTotalAP(source) *.1);
			local exten_damage = 0
			function is_zd(target)
				if target.buff then
					if target.buff["poisontrailtarget"] or target.buff["twitchdeadlyvenom"] or target.buff["cassiopeiawpoison"] or target.buff["cassiopeiaqdebuff"] or target.buff["toxicshotparticle"] or target.buff["bantamtraptarget"] then
						return true
					end
				end
				return false
			end
			if is_zd(target) then
				local ELevelDageme = {20,40,60,80,100}
				local eDmg = ELevelDageme[elevel] or 0;
				local t  = eDmg + (common.GetTotalAP(source) *.6);
				exten_damage = common.CalculateMagicDamage(target,t,source)
			end
			local total =  common.CalculateMagicDamage(target,totalEDmg,source)  + exten_damage
			return spell.get_damage(total,target,source,1)
		end,
	},
	R = {
		prediction = {
			type = 'Linear',
			range = 850,
			delay = 0.50,
			width = 40,
			speed = math.huge,
			boundingRadiusMod = 0,
			collision = {
			  hero = true,
			  minion = true,
			  wall = true,
			},
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_R),
		ignore_obj_radius = 2000,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			local dmg = {150,250,350}
			local rlevel = source:spellSlot(3).level
			local RDmg = dmg[rlevel] or 0;
			local ap = common.GetTotalAP(source) * .5
			local totalQDmg = RDmg + ap
			local total =  common.CalculateMagicDamage(target,totalQDmg,source)
			return spell.get_damage(total,target,source,1)
		end,
	},
}
spell.Kalista = {
	Q = {
		prediction = {
			type = 'Linear',
			range = 1150,
			delay = 0.25,
			width = 40,
			speed = 2400,
			boundingRadiusMod = 1,
			collision = {
			  hero = false,
			  minion = true,
			  wall = true,
			},
		},
		prediction_ig = {
			type = 'Linear',
			range = 1150,
			delay = 0.25,
			width = 40,
			ig = true,
			speed = 2400,
			boundingRadiusMod = 1,
			collision = {
			  hero = false,
			  minion = true,
			  wall = true,
			},
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_Q),
		ignore_obj_radius = 1200,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			local dmg = {20,85,150,215,280}
			local qlevel = source:spellSlot(0).level
			local QDmg = dmg[qlevel] or 0;
			local ad = common.GetTotalAD(source) * 1
			local totalQDmg = QDmg + ad
			local total =  common.CalculatePhysicalDamage(target,totalQDmg,source)
			return spell.get_damage(total,target,source,2)
		end,
	},
	W = {
		prediction = {
			type = 'InRange',
			range = 1400,
			radius = 1400,
			speed = math.huge,
			delay = 0.50,
			dashRadius = 0,
			boundingRadiusModSource = 1,
			boundingRadiusModTarget = 1,
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_W),
		ignore_obj_radius = 1400,
		damage = function(target,source)
			if not target then return 0 end

			return 0
		end,
	},
	E = {
		prediction = {
			type = 'InRange',
			range = 1100,
			radius = 1100,
			speed = math.huge,
			delay = 0.25,
			dashRadius = 0,
			boundingRadiusModSource = 0,
			boundingRadiusModTarget = 0,
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_E),
		ignore_obj_radius = 1100,
		damage = function(target,source,stacks2)
			if not target then return 0 end
			local source = source or player
			local lever = source.levelRef
			local stacks = stacks2
			if target.buff['kalistaexpungemarker'] then
				stacks = target.buff['kalistaexpungemarker'].stacks
			elseif not stacks2 then
				stacks = 0
			end
			if stacks2 then
				stacks = stacks2
			end
			if stacks == 0 then return 0 end

			local elevel = source:spellSlot(2).level
			local ELevelDageme = {20,30,40,50,60}
			local ELevelDageme_ex = {8,12,16,20,24}
			local ELevelDageme_ex2 = {0.25,0.3,0.35,0.4,0.45}
			local eDmg = ELevelDageme[elevel] or 0;
			local t  = eDmg + (common.GetTotalAD(source) *.7);
			local per_damage_tb = ELevelDageme_ex[elevel]
			local per_damage_tb2 = ELevelDageme_ex2[elevel]
			local per_damage = per_damage_tb + (common.GetTotalAD(source) *per_damage_tb2);
			per_damage =  per_damage * (stacks - 1)
			local total_damage = per_damage + t
			if target.type == TYPE_HERO and player.rune[8014] and common.GetPercentHealth(target) < 40 then
				total_damage = total_damage + total_damage * 0.08
			end

			if target.charName:find("Dragon") then--伤害计算等测试
				total_damage = total_damage * 0.49

			elseif target.charName:find("SRU_RiftHerald") or target.charName:find("SRU_Baron") then --伤害计算等测试
				total_damage = total_damage * 0.49
			end
			local total =  common.CalculatePhysicalDamage(target,total_damage ,source)

			return spell.get_damage(total,target,source,2)
		end,
	},
	R = {
		prediction = {
			type = 'InRange',
			range = 1200,
			radius = 1100,
			speed = math.huge,
			delay = 0.00,
			dashRadius = 0,
			boundingRadiusModSource = 0,
			boundingRadiusModTarget = 0,
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_R),
		ignore_obj_radius = 1200,
		damage = function(target,source)
			if not target then return 0 end
			return 0
		end,
	},
}
spell.Ryze = {
	Q = {
		prediction = {
			type = 'Linear',
			range = 1000,
			delay = 0.25,
			width = 55,
			speed = 1700,
			boundingRadiusMod = 1,
			collision = {
			  hero = true,
			  minion = true,
			  wall = true,
			},
		},
		prediction_ig = {
			type = 'Linear',
			range = 1000,
			delay = 0.25,
			width = 110,
			ig = true,
			speed = 1700,
			boundingRadiusMod = 1,
			collision = {
			  hero = true,
			  minion = true,
			  wall = true,
			},
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_Q),
		ignore_obj_radius = 1200,
		damage = function(target,source)--未计算额外的法术值伤害
			if not target then return 0 end
			local source = source or player
			local dmg = {70,90,110,130,150 }
			local qlevel = source:spellSlot(0).level
			local QDmg = dmg[qlevel] or 0;
			local ap = common.GetTotalAP(source) * .55
			local mana = source.maxMana - (source.baseMana +  source.baseManaPerLevel * source.levelRef)
			if mana < 0 then mana = 0 end
			local BonusMana = mana * 0.02

			local totalQDmg = QDmg + ap + BonusMana
			if target.buff['ryzee'] and target.buff['ryzee'].endTime - game.time > 0.25 + source.pos:dist(target.pos) / 1700  then
				totalQDmg =  totalQDmg + (totalQDmg * (.1 + (.3 * source:spellSlot(_R).level)))
			end
			local total =  common.CalculateMagicDamage(target,totalQDmg,source)
			return spell.get_damage(total,target,source,1)
		end,
	},
	W = {
		prediction = {
			type = 'InRange',
			range = 550,
			radius = 550,
			speed = math.huge,
			delay = 0.25,
			dashRadius = 0,
			boundingRadiusModSource = 1,
			boundingRadiusModTarget = 1,
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_W),
		ignore_obj_radius = 1400,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			local dmg = {80,110,140,170,200 }
			local wlevel = source:spellSlot(1).level
			local wDmg = dmg[wlevel] or 0;
			local ap = common.GetTotalAP(source) * .7
			local mana = source.maxMana - (source.baseMana +  source.baseManaPerLevel * source.levelRef)
			if mana < 0 then mana = 0 end
			local BonusMana = mana * 0.04
			local totalWDmg = wDmg + ap + BonusMana
			local total =  common.CalculateMagicDamage(target,totalWDmg,source)
			return spell.get_damage(total,target,source,1)
		end,
	},
	E = {
		prediction = {
			type = 'InRange',
			range = 550,
			radius = 550,
			speed = math.huge,
			delay = 0.25,
			dashRadius = 0,
			boundingRadiusModSource = 0,
			boundingRadiusModTarget = 0,
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_E),
		ignore_obj_radius = 1100,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			local dmg = {60,90,120,150,180 }
			local elevel = source:spellSlot(2).level
			local eDmg = dmg[elevel] or 0;
			local ap = common.GetTotalAP(source) * .45
			local mana = source.maxMana - (source.baseMana +  source.baseManaPerLevel * source.levelRef)
			if mana < 0 then mana = 0 end
			local BonusMana = mana * 0.02
			local totalEDmg = eDmg + ap + BonusMana
			local total =  common.CalculateMagicDamage(target,totalEDmg,source)
			return spell.get_damage(total,target,source,1)
		end,
	},
	R = {
		prediction = {
			type = 'InRange',
			range = 1000,
			radius = 1000,
			speed = math.huge,
			delay = 0.00,
			dashRadius = 0,
			boundingRadiusModSource = 0,
			boundingRadiusModTarget = 0,
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_R),
		ignore_obj_radius = 3000,
		damage = function(target,source)
			if not target then return 0 end
			return 0
		end,
	},
}
spell.Blitzcrank = {
	Q = {
		prediction = {
			type = 'Linear',
			range = 1020,
			delay = 0.25,
			width = 70,
			speed = 1800,
			boundingRadiusMod = 1,
			collision = {
			  hero = true,
			  minion = true,
			  wall = true,
			},
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_Q),
		ignore_obj_radius = 1115,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			local dmg = {105,155,205,255,305}
			local qlevel = source:spellSlot(0).level
			local QDmg = dmg[qlevel] or 0;
			local ap = common.GetTotalAP(source) * 1.2
			local totalQDmg = QDmg + ap
			local total =  common.CalculateMagicDamage(target,totalQDmg,source)
			return spell.get_damage(total,target,source,1)
		end,
	},
	W = {
		prediction = {
			type = 'InRange',
			range = 0,
			radius = 0,
			speed = math.huge,
			delay = 0.00,
			dashRadius = 0,
			boundingRadiusModSource = 0,
			boundingRadiusModTarget = 0,
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_W),
		ignore_obj_radius = 0,
		damage = function(target,source)
			if not target then return 0 end

			return 0
		end,
	},
	E = {
		prediction = {
			type = 'InRange',
			range = 125,
			radius = 125,
			speed = math.huge,
			delay = 0.00,
			dashRadius = 0,
			boundingRadiusModSource = 1,
			boundingRadiusModTarget = 1,
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_E),
		ignore_obj_radius = 1100,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			local total = common.CalculateAADamage(target, source)
			return spell.get_damage(total,target,source,2)
		end,
	},
	R = {
		prediction = {
			type = 'InRange',
			range = 1000,
			radius = 1000,
			speed = math.huge,
			delay = 0.00,
			dashRadius = 0,
			boundingRadiusModSource = 0,
			boundingRadiusModTarget = 0,
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_R),
		ignore_obj_radius = 3000,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			local dmg = {275,400,525}
			local rlevel = source:spellSlot(3).level
			local RDmg = dmg[rlevel] or 0;
			local ap = common.GetTotalAP(source) * 1
			local totalQDmg = RDmg + ap
			local total =  common.CalculateMagicDamage(target,totalQDmg,source)
			return spell.get_damage(total,target,source,1)
		end,
	},
}
spell.Belveth = {
	Q = {
		prediction = {
			type = 'Linear',
			range = 400,
			delay = 0.00,
			width = 100,
			speed = 1800,
			boundingRadiusMod = 1,
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_Q),
		ignore_obj_radius = 1115,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			local dmg = {10,15,20,25,30}
			local qlevel = source:spellSlot(0).level
			local QDmg = dmg[qlevel] or 0;
			local ad = common.GetTotalAD(source) * 1.1
			local totalQDmg = QDmg + ad
			local total =  common.CalculatePhysicalDamage(target,totalQDmg,source)
			return spell.get_damage(total,target,source,2)
		end,
	},
	W = {
		prediction = {
			type = 'Linear',
			range = 600,
			delay = 0.50,
			width = 100,
			speed = math.huge,
			boundingRadiusMod = 1,
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_W),
		ignore_obj_radius = 0,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			local dmg = {70,110,150,190,230}
			local qlevel = source:spellSlot(1).level
			local QDmg = dmg[qlevel] or 0;
			local ad = common.GetBonusAD(source) * 1
			local ap = common.GetTotalAP(source) * 1.25
			local totalQDmg = QDmg + ad + ap
			local total =  common.CalculateMagicDamage(target,totalQDmg,source)
			return spell.get_damage(total,target,source,1)
		end,
	},
	E = {
		prediction = {
			type = 'InRange',
			range = 500,
			radius = 500,
			speed = math.huge,
			delay = 0.00,
			dashRadius = 0,
			boundingRadiusModSource = 1,
			boundingRadiusModTarget = 1,
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_E),
		ignore_obj_radius = 1100,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			local dmg = {8,10,12,14,16}
			local qlevel = source:spellSlot(2).level
			local QDmg = dmg[qlevel] or 0;
			local ad = common.GetTotalAD(source) * 0.06

			local Ph = math.min(0,100 - common.GetPercentHealth(target))
			local totalQDmg = QDmg + ad
			totalQDmg = totalQDmg + (totalQDmg * (Ph * 0.03))
			local total =  common.CalculatePhysicalDamage(target,totalQDmg,source)
			return spell.get_damage(total,target,source,2)
		end,
	},
	R = {
		prediction = {
			type = 'InRange',
			range = 500,
			radius = 500,
			speed = math.huge,
			delay = 0.00,
			dashRadius = 0,
			boundingRadiusModSource = 0,
			boundingRadiusModTarget = 0,
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_R),
		ignore_obj_radius = 3000,
		damage = function(target,source)
			if not target then return 0 end
			return 0
		end,
	},
}


spell.Kaisa = {
	Q = {
		prediction = {
			type = 'InRange',
			range = 665,
			radius = 665,
			speed = math.huge,
			delay = 0.00,
			dashRadius = 0,
			boundingRadiusModSource = 0,
			boundingRadiusModTarget = 0,
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_Q),
		ignore_obj_radius = 1115,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			local dmg = {10,15,20,25,30}
			local qlevel = source:spellSlot(0).level
			local QDmg = dmg[qlevel] or 0;
			local ad = common.GetTotalAD(source) * 1.1
			local totalQDmg = QDmg + ad
			local total =  common.CalculatePhysicalDamage(target,totalQDmg,source)
			return spell.get_damage(total,target,source,2)
		end,
	},


	W = {
		prediction = {
			type = 'Linear',
			range = 3000,
			delay = 0.40,
			width = 100,
			speed = 1750,
			boundingRadiusMod = 0,
			collision = {
			  hero = true,
			  minion = true,
			  wall = true,
			},
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_W),
		ignore_obj_radius = 0,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			local dmg = {30,55,80,105,130}
			local qlevel = source:spellSlot(1).level
			local QDmg = dmg[qlevel] or 0;
			local ad = common.GetTotalAD(source) * 1.3
			local ap = common.GetTotalAP(source) * 0.45
			local totalQDmg = QDmg + ad + ap
			local total =  common.CalculatePhysicalDamage(target,totalQDmg,source)
			return spell.get_damage(total,target,source,2)
		end,
	},
	E = {
		prediction = {
			type = 'InRange',
			range = 500,
			radius = 500,
			speed = math.huge,
			delay = 0.00,
			dashRadius = 0,
			boundingRadiusModSource = 1,
			boundingRadiusModTarget = 1,
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_E),
		ignore_obj_radius = 1100,
		damage = function(target,source)
			if not target then return 0 end
			return 0
		end,
	},
	R = {
		prediction = {
			type = 'InRange',
			range = 500,
			radius = 500,
			speed = math.huge,
			delay = 0.00,
			dashRadius = 0,
			boundingRadiusModSource = 0,
			boundingRadiusModTarget = 0,
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_R),
		ignore_obj_radius = 3000,
		damage = function(target,source)
			if not target then return 0 end
			return 0
		end,
	},
}
spell.Nilah = {
	Q = {
		prediction = {
			type = 'Linear',
			range = 600,
			delay = 0.25,
			width = 75,
			speed = math.huge,
			boundingRadiusMod = 0,
			collision = {
			  hero = false,
			  minion = false,
			  wall = false,
			},
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_Q),
		ignore_obj_radius = 600,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			local dmg = {5,10,15,20,25}
			local dmg_2 = {0.90,1.00,1.10,1.20,1.30}



			local qlevel = source:spellSlot(0).level
			local QDmg = dmg[qlevel] or 0;
			local QDmg_2 = dmg_2[qlevel] or 0;
			local ad = common.GetTotalAD(source) * QDmg_2
			local totalQDmg = QDmg + ad
			if source.crit > 0 then
				totalQDmg = totalQDmg + totalQDmg * source.crit
			end

			local total =  common.CalculatePhysicalDamage(target,totalQDmg,source)
			return spell.get_damage(total,target,source,2)
		end,
	},


	W = {
		prediction = {
			type = 'InRange',
			range = 225,
			radius = 225,
			speed = math.huge,
			delay = 0.00,
			dashRadius = 0,
			boundingRadiusModSource = 0,
			boundingRadiusModTarget = 0,
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_W),
		ignore_obj_radius = 0,
		damage = function(target,source)
			if not target then return 0 end
			return 0
		end,
	},
	E = {
		prediction = {
			type = 'InRange',
			range = 1000,
			radius = 1000,
			speed = math.huge,
			delay = 0.00,
			dashRadius = 0,
			boundingRadiusModSource = 1,
			boundingRadiusModTarget = 1,
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_E),
		ignore_obj_radius = 1000,
		damage = function(target,source,stacks)
			if not target then return 0 end
			local source = source or player
			local level = source:spellSlot(2).level
			if level == 0 then return 0 end

			local stacks = stacks or 1
			if stacks <= 0 then return 0 end

			-- 官方Kalista E技能数值
			local baseDamage = {20, 30, 40, 50, 60}
			local additionalSpearDamage = {10, 14, 19, 25, 32}
			local adRatio = {0.198, 0.225, 0.25, 0.275, 0.30}

			-- 第一根矛的伤害
			local firstSpearDamage = baseDamage[level]

			-- 额外矛的伤害
			local additionalDamage = 0
			if stacks > 1 then
				local additionalSpears = stacks - 1
				local perSpearDamage = additionalSpearDamage[level] + (common.GetTotalAD(source) * adRatio[level])
				additionalDamage = additionalSpears * perSpearDamage
			end

			local totalDamage = firstSpearDamage + additionalDamage
			local total = common.CalculatePhysicalDamage(target, totalDamage, source)
			return spell.get_damage(total,target,source,2)
		end,
	},
	R = {
		prediction = {
			type = 'InRange',
			range = 450,
			radius = 450,
			speed = math.huge,
			delay = 0.00,
			dashRadius = 0,
			boundingRadiusModSource = 0,
			boundingRadiusModTarget = 0,
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_R),
		ignore_obj_radius = 3000,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			local dmg = {60,120,180}
			local dmg_2 = {125,225,325}
			local rlevel = source:spellSlot(3).level
			local RDmg = dmg[rlevel] or 0;
			local RDmg_2 = dmg_2[rlevel] or 0;
			local ad = common.GetTotalAD(source)
			local totalQDmg = (RDmg + (ad * 1.4))
			local totalQDmg2 = (RDmg_2 + (ad * 1.2))
			local total =  common.CalculatePhysicalDamage(target,totalQDmg+totalQDmg2,source)
			return spell.get_damage(total,target,source,2)
		end,
	},
}
spell.Yasuo = {
	Q = {
		prediction = {
			type = 'Linear',
			range = 450,
			delay = 0.25,
			width = 40,
			speed = math.huge,
			boundingRadiusMod = 0,
			collision = {
			  hero = false,
			  minion = false,
			  wall = false,
			},
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_Q),
		ignore_obj_radius = 450,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			local dmg = {20,45,70,95,120}
			local qlevel = source:spellSlot(0).level
			local QDmg = dmg[qlevel] or 0;
			local adb = 1.05
			local crit = source.crit > 0.875
			local items = {}
			for i = 0, 6 do
				local id = source:itemID(i)
				if id > 0 then
					items[id] = true
				end
			end
			if crit then adb = 1.46995 end
			if items[3031]  and crit then
				adb = adb + 0.29399
			end

			local ad = common.GetTotalAD(source) * adb
			local totalQDmg = QDmg + ad
			local total =  common.CalculatePhysicalDamage(target,totalQDmg,source)
			return spell.get_damage(total,target,source,2)
		end,
	},
	Q3 = {
		prediction = {
			type = 'Linear',
			range = 1150,
			delay = 0.25,
			width = 90,
			speed = 1200,
			boundingRadiusMod = 0,
			collision = {
			  hero = false,
			  minion = false,
			  wall = false,
			},
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_Q),
		ignore_obj_radius = 450,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			local dmg = {20,45,70,95,120}
			local qlevel = source:spellSlot(0).level
			local QDmg = dmg[qlevel] or 0;
			local adb = 1.05
			local crit = source.crit > 0.875
			local items = {}
			for i = 0, 6 do
				local id = source:itemID(i)
				if id > 0 then
					items[id] = true
				end
			end
			if crit then adb = 1.46995 end
			if items[3031]  and crit then
				adb = adb + 0.29399
			end

			local ad = common.GetTotalAD(source) * adb
			local totalQDmg = QDmg + ad
			local total =  common.CalculatePhysicalDamage(target,totalQDmg,source)
			return spell.get_damage(total,target,source,2)
		end,
	},
	EQ = {
		prediction = {
			type = 'InRange',
			range = 475,
			radius = 215,
			speed = math.huge,
			delay = 0.58,
			dashRadius = 0,
			boundingRadiusModSource = 0,
			boundingRadiusModTarget = 0,
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_Q),
		ignore_obj_radius = 450,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			local dmg = {20,45,70,95,120}
			local qlevel = source:spellSlot(0).level
			local QDmg = dmg[qlevel] or 0;
			local adb = 1.05
			local crit = source.crit > 0.875
			local items = {}
			for i = 0, 6 do
				local id = source:itemID(i)
				if id > 0 then
					items[id] = true
				end
			end
			if crit then adb = 1.46995 end
			if items[3031]  and crit then
				adb = adb + 0.29399
			end

			local ad = common.GetTotalAD(source) * adb
			local totalQDmg = QDmg + ad
			local total =  common.CalculatePhysicalDamage(target,totalQDmg,source)
			return spell.get_damage(total,target,source,2)
		end,
	},


	W = {
		prediction = {
			type = 'InRange',
			range = 350,
			radius = 350,
			speed = math.huge,
			delay = 0.013,
			dashRadius = 0,
			boundingRadiusModSource = 0,
			boundingRadiusModTarget = 0,
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_W),
		ignore_obj_radius = 0,
		damage = function(target,source)
			if not target then return 0 end
			return 0
		end,
	},
	E = {
		prediction = {
			type = 'InRange',
			range = 475,
			radius = 475,
			speed = 750 + player.moveSpeed * .6,
			delay = 0.00,
			dashRadius = 0,
			boundingRadiusModSource = 1,
			boundingRadiusModTarget = 1,
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_E),
		ignore_obj_radius = 1100,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			local dmg = {60,70,80,90,100}
			local elevel = source:spellSlot(2).level
			local QDmg = dmg[elevel] or 0;
			local ad = common.GetBonusAD(source) * 0.2
			local ap = common.GetTotalAP(source) * 0.6
			local totalQDmg = QDmg + ad + ap
			local total =  common.cap(target,totalQDmg,source)
			return spell.get_damage(total,target,source,1)
		end,
	},
	R = {
		prediction = {
			type = 'InRange',
			range = 1400,
			radius = 1400,
			speed = math.huge,
			delay = 0.00,
			dashRadius = 0,
			boundingRadiusModSource = 0,
			boundingRadiusModTarget = 0,
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_R),
		ignore_obj_radius = 1400,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			local dmg = {200,350,500}
			local rlevel = source:spellSlot(3).level
			local RDmg = dmg[rlevel] or 0;
			local ad = common.GetBonusAD(source) * 1.5
			local totalDmg = RDmg +ad
			local total =  common.CalculatePhysicalDamage(target,totalDmg,source)
			return spell.get_damage(total,target,source,2)
		end,
	},
}
spell.Zed = {
	Q = {
		prediction = {
			type = 'Linear',
			range = 925,
			delay = 0.25,
			width = 50,
			speed = 1700,
			boundingRadiusMod = 0,
			collision = {
			  hero = false,
			  minion = false,
			  wall = true,
			},
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_Q),
		ignore_obj_radius = 450,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			local dmg = {80,115,150,185,220}
			local qlevel = source:spellSlot(0).level
			local QDmg = dmg[qlevel] or 0;
			local adb = 1.1
			local ad = common.GetBonusAD(source) * adb
			local totalQDmg = QDmg + ad

			--local total =  common.cad(target,totalQDmg,source)
			return spell.get_damage(total,target,source,2)
		end,
	},
	Q_IG = {
		prediction = {
			type = 'Linear',
			range = 925,
			delay = 0.25,
			width = 50,
			speed = 1700,
			ig = true,
			boundingRadiusMod = 0,
			collision = {
			  hero = false,
			  minion = false,
			  wall = true,
			},
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_Q),
		ignore_obj_radius = 450,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			local dmg = {80,115,150,185,220}
			local qlevel = source:spellSlot(0).level
			local QDmg = dmg[qlevel] or 0;
			local adb = 1.1
			local ad = common.GetBonusAD(source) * adb
			local totalQDmg = QDmg + ad
			local total =  common.cad(target,totalQDmg,source)
			return spell.get_damage(total,target,source,2)
		end,
	},
	W = {
		prediction = {
			type = 'InRange',
			range = 650,
			radius = 650,
			speed = 2500,
			delay = 0.00,
			dashRadius = 0,
			boundingRadiusModSource = 0,
			boundingRadiusModTarget = 0,
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_W),
		ignore_obj_radius = 0,
		damage = function(target,source)
			if not target then return 0 end
			return 0
		end,
	},
	E = {
		prediction = {
			type = 'InRange',
			range = 290,
			radius = 290,
			speed = math.huge,
			delay = 0.00,
			dashRadius = 0,
			boundingRadiusModSource = 1,
			boundingRadiusModTarget = 1,
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_E),
		ignore_obj_radius = 1100,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			local dmg = {70,90,110,130,150}
			local elevel = source:spellSlot(2).level
			local QDmg = dmg[elevel] or 0;
			local ad = common.GetBonusAD(source) * 0.65
			local totalQDmg = QDmg + ad
			--local total =  common.cad(target,totalQDmg,source)
			return spell.get_damage(total,target,source,2)
		end,
	},
	R = {
		prediction = {
			type = 'InRange',
			range = 625,
			radius = 625,
			speed = math.huge,
			delay = 0.00,
			dashRadius = 0,
			boundingRadiusModSource = 0,
			boundingRadiusModTarget = 0,
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_R),
		ignore_obj_radius = 1400,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			local ad = common.GetTotalAD(source) * 0.65
			--local total =  common.cad(target,ad,source)
			return spell.get_damage(total,target,source,2)
		end,
	},
}
spell.MasterYi = {
	Q = {
		prediction = {
			type = 'InRange',
			range = 600,
			radius = 600,
			speed = math.huge,
			delay = 0.00,
			dashRadius = 0,
			boundingRadiusModSource = 0,
			boundingRadiusModTarget = 0,
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_Q),
		ignore_obj_radius = 600,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			local dmg = {30,60,90,120,150}
			local qlevel = source:spellSlot(0).level
			local QDmg = dmg[qlevel] or 0;
			local ad = common.GetTotalAD(source) * 0.5
			local totalQDmg = QDmg + ad
			local total =  common.cad(target,totalQDmg,source)
			return spell.get_damage(total,target,source,2)
		end,
	},
	W = {
		prediction = {
			type = 'InRange',
			range = 0,
			radius = 0,
			speed = 0,
			delay = 0.00,
			dashRadius = 0,
			boundingRadiusModSource = 0,
			boundingRadiusModTarget = 0,
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_W),
		ignore_obj_radius = 0,
		damage = function(target,source)
			if not target then return 0 end
			return 0
		end,
	},
	E = {
		prediction = {
			type = 'InRange',
			range = 0,
			radius = 0,
			speed = math.huge,
			delay = 0.00,
			dashRadius = 0,
			boundingRadiusModSource = 1,
			boundingRadiusModTarget = 1,
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_E),
		ignore_obj_radius = 1100,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			local dmg = {30,35,40,45,50}
			local elevel = source:spellSlot(2).level
			local eDmg = dmg[elevel] or 0;
			local ad = common.GetBonusAD(source) * 0.35
			local totalDmg = eDmg + ad
			return totalDmg
		end,
	},
	R = {
		prediction = {
			type = 'InRange',
			range = 0,
			radius = 0,
			speed = math.huge,
			delay = 0.00,
			dashRadius = 0,
			boundingRadiusModSource = 0,
			boundingRadiusModTarget = 0,
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_R),
		ignore_obj_radius = 1400,
		damage = function(target,source)
			if not target then return 0 end
			return 0
		end,
	},
}
spell.Udyr = {
	Q = {
		prediction = {
			type = 'InRange',
			range = player.attackRange,
			radius = player.attackRange,
			speed = math.huge,
			delay = 0.00,
			dashRadius = 0,
			boundingRadiusModSource = 0,
			boundingRadiusModTarget = 0,
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_Q),
		ignore_obj_radius = 600,
		damage = function(target,source)
			if not target then return 0 end
			return 0
		end,
	},
	W = {
		prediction = {
			type = 'InRange',
			range = player.attackRange,
			radius = player.attackRange,
			speed = math.huge,
			delay = 0.00,
			dashRadius = 0,
			boundingRadiusModSource = 0,
			boundingRadiusModTarget = 0,
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_W),
		ignore_obj_radius = 600,
		damage = function(target,source)
			if not target then return 0 end
			return 0
		end,
	},
	E = {
		prediction = {
			type = 'InRange',
			range = player.attackRange,
			radius = player.attackRange,
			speed = math.huge,
			delay = 0.00,
			dashRadius = 0,
			boundingRadiusModSource = 0,
			boundingRadiusModTarget = 0,
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_E),
		ignore_obj_radius = 600,
		damage = function(target,source)
			if not target then return 0 end
			return 0
		end,
	},
	R = {
		prediction = {
			type = 'InRange',
			range = player.attackRange,
			radius = player.attackRange,
			speed = math.huge,
			delay = 0.00,
			dashRadius = 0,
			boundingRadiusModSource = 0,
			boundingRadiusModTarget = 0,
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_R),
		ignore_obj_radius = 600,
		damage = function(target,source)
			if not target then return 0 end
			return 0
		end,
	},
}
spell.Heimerdinger = {
	Q = {
		prediction = {
			type = 'InRange',
			range = 350,
			radius = 550,
			speed = math.huge,
			delay = 0.00,
			dashRadius = 0,
			boundingRadiusModSource = 0,
			boundingRadiusModTarget = 0,
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_Q),
		ignore_obj_radius = 600,
		damage = function(target,source)
			if not target then return 0 end
			return 0
		end,
	},
	W = {
		prediction = {
			type = 'Linear',
			range = 1300,
			delay = 0.25,
			width = 40,
			speed = 750,
			boundingRadiusMod = 0,
			collision = {
			  hero = false,
			  minion = true,
			  wall = true,
			},
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_W),
		ignore_obj_radius = 600,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			local ap1 = common.GetTotalAP(source) * 0.45
			local ap2 = common.GetTotalAP(source) * 0.12
			local ap3 = common.GetTotalAP(source) * 0.3575
			local dmg_tb = {50,75,100,125,150}
			local dmg_tb_hero = {10,15,20,25,30}
			local dmg_tb_minion = {30,45,60,75,90}
			local level = source:spellSlot(_W).level
			local Dmg = dmg_tb[level] or 0;
			local Dmg_hero = dmg_tb_hero[level] or 0;
			local Dmg_minion = dmg_tb_minion[level] or 0;
			local damages = 0
			if target.type == TYPE_HERO then
				damages = (Dmg + ap1) + ((Dmg_hero + ap2) * 4)
			elseif target.type == TYPE_MINION then
				damages = (Dmg + ap1) + ((Dmg_minion + ap3) * 4)
			end
			local total =  common.cap(target,damages,source)
			return spell.get_damage(total,target,source,1)
		end,
	},
	E = {
		prediction = {
			type = 'Circular',
			range = 925,
			delay = 0.25,
			radius = 135,
			speed = 1700,
			ig = true,
			boundingRadiusMod = 0,
			collision = {
			  hero = false,
			  minion = true,
			  wall = true,
			},
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_E),
		ignore_obj_radius = 600,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			local ap = common.GetTotalAP(source) * 0.6
			local dmg_tb = {60,100,140,180,220}
			local level = source:spellSlot(_E).level
			local Dmg = dmg_tb[level] or 0;
			local damages = (Dmg + ap)
			local total =  common.cap(target,damages,source)
			return spell.get_damage(total,target,source,1)
		end,
	},
	R = {
		prediction = {
			type = 'InRange',
			range = player.attackRange,
			radius = player.attackRange,
			speed = math.huge,
			delay = 0.00,
			dashRadius = 0,
			boundingRadiusModSource = 0,
			boundingRadiusModTarget = 0,
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_R),
		ignore_obj_radius = 600,
		damage = function(target,source)
			if not target then return 0 end
			return 0
		end,
	},
}
spell.KSante = {
	Q = {
		prediction = {
			type = 'Linear',
			range = 450,
			delay = 0.25,
			width = 40,
			speed = math.huge,
			boundingRadiusMod = 0,
			collision = {
			  hero = false,
			  minion = false,
			  wall = true,
			},
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_Q),
		ignore_obj_radius = 465,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			local ad = common.GetTotalAD(source) * 0.4
			local dmg_tb = {50,75,100,125,150}
			local bonusArmor = source.bonusArmor * 0.3
			local bonusSpellBlock = source.bonusSpellBlock * 0.3

			local level = source:spellSlot(_Q).level
			local Dmg = dmg_tb[level] or 0;
			local damages = (Dmg + ad + bonusArmor + bonusSpellBlock)
			--local total =  common.cad(target,damages,source)
			return spell.get_damage(total,target,source,2)
		end,
	},
	W = {
		prediction = {
			type = 'Linear',
			range = 450,
			delay = 0,
			width = 50,
			speed = 1000,
			boundingRadiusMod = 0,
			collision = {
			  hero = false,
			  minion = true,
			  wall = true,
			},
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_W),
		ignore_obj_radius = 600,
		damage = function(target,source,buff)
			if not target then return 0 end
			local source = source or player
			local ad = common.GetTotalAD(source) * 0.5
			local dmg_tb = {0.0425,0.045,0.0475,0.05,0.0525}
			local time = 0.25
			if source.buff['ksantew'] or source.buff['ksantew_allout'] then
				local buff = source.buff['ksantew'] or source.buff['ksantew_allout']
				time = math.min(1,0.25 + (game.time - buff.startTime))
			end
			local dmg_tb_2 = {25,35,45,55,65}
			local level =  source:spellSlot(_W).level
			local Dmg = dmg_tb[level] or 0;
			Dmg =  Dmg+ time * 0.04
			local exdmg = 0
			if buff or source.buff['ksantew_allout'] then
				exdmg = dmg_tb_2[level] or 0 + ad
			end
			local damages = (target.maxHealth * Dmg  + exdmg)
			--local total =  common.cad(target,damages,source)
			return spell.get_damage(total,target,source,2)
		end,
	},
	E = {
		prediction = {
			type = 'Circular',
			range = 925,
			delay = 0.25,
			radius = 135,
			speed = 1700,
			ig = true,
			boundingRadiusMod = 0,
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_E),
		ignore_obj_radius = 600,
		damage = function(target,source)
			if not target then return 0 end
			return 0
		end,
	},
	R = {
		prediction = {
			type = 'InRange',
			range = 350,
			radius = 350,
			speed = math.huge,
			delay = 0.40,
			dashRadius = 0,
			boundingRadiusModSource = 0,
			boundingRadiusModTarget = 0,
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_R),
		ignore_obj_radius = 600,
		damage = function(target,source,wall)
			if not target then return 0 end
			local source = source or player
			local ad = common.GetTotalAD(source) * 0.2
			local dmg_tb = {35,70,105}
			local level = source:spellSlot(_R).level
			local Dmg = dmg_tb[level] or 0;
			local exdmg = 0
			if wall then
				local dmg_tb_2 = {150,250,350}
				exdmg = dmg_tb_2[level] + ad
			end
			local damages = (Dmg + ad + exdmg)
			--local total =  common.cad(target,damages,source)
			return spell.get_damage(total,target,source,2)

		end,
	},
}
spell.Milio = {
	Q = {
		prediction = {
			type = 'Linear',
			range = 1000,
			delay = 0.25,
			width = 60,
			speed = 1200,
			boundingRadiusMod = 0,
			collision = {
			  hero = true,
			  minion = true,
			  wall = true,
			},
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_Q),
		ignore_obj_radius = 1000,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			local ap = common.GetTotalAP(source) * 0.9
			local dmg_tb = {90,135,180,225,270}
			local level = source:spellSlot(_Q).level
			local Dmg = dmg_tb[level] or 0;
			local damages = (Dmg + ap)
			local total =  common.cap(target,damages,source)
			return spell.get_damage(total,target,source,1)
		end,
	},
	Q1 = {
		prediction = {
			type = 'Circular',
			range = 1500,
			delay = 1.00,
			radius = 100,
			speed = math.huge,
			boundingRadiusMod = 0,
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_Q),
		ignore_obj_radius = 1500,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			local ap = common.GetTotalAP(source) * 0.9
			local dmg_tb = {90,135,180,225,270}
			local level = source:spellSlot(_Q).level
			local Dmg = dmg_tb[level] or 0;
			local damages = (Dmg + ap)
			local total =  common.cap(target,damages,source)
			return spell.get_damage(total,target,source,1)
		end,
	},
	W = {
		prediction = {
			type = 'InRange',
			range = 650,
			radius = 650,
			speed = math.huge,
			delay = 0.00,
			dashRadius = 0,
			boundingRadiusModSource = 0,
			boundingRadiusModTarget = 0,
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_W),
		ignore_obj_radius = 650,
		damage = function(target,source,buff)

			return 0
		end,
	},
	E = {
		prediction = {
			type = 'InRange',
			range = 650,
			radius = 650,
			speed = math.huge,
			delay = 0.00,
			dashRadius = 0,
			boundingRadiusModSource = 0,
			boundingRadiusModTarget = 0,
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_E),
		ignore_obj_radius = 650,
		damage = function(target,source)
			if not target then return 0 end
			return 0
		end,
	},
	R = {
		prediction = {
			type = 'InRange',
			range = 700,
			radius = 700,
			speed = math.huge,
			delay = 0.40,
			dashRadius = 0,
			boundingRadiusModSource = 0,
			boundingRadiusModTarget = 0,
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_R),
		ignore_obj_radius = 600,
		damage = function(target,source,wall)
			return 0
		end,
	},
}
	spell.Ambessa = {
		Q = {
			ignore_obj_radius = 380,
			target_selector = 8,
			prediction = {
				delay = 0.25,
				range = 380,
				boundingRadiusMod = 0,
				type = "Circular",
				radius = 380,
				speed = math.huge
			},
			cast_spell = {},
			slot = player:spellSlot(_Q),
			damage = function(arg_507_0, arg_507_1)
				-- function 507
				local slot_507_0 = arg_507_1 or player
				local slot_507_1 = common.GetTotalAP(slot_507_0)
				local slot_507_2 = common.GetBonusAD(slot_507_0)
				local slot_507_3 = slot_507_0:spellSlot(_Q).level
				local slot_507_4 = {
					40,
					60,
					80,
					100,
					120
				}
				local slot_507_5 = {
					0.02,
					0.03,
					0.04,
					0.05,
					0.06
				}
				local slot_507_6 = slot_507_4[slot_507_3] or 0
				local slot_507_7 = (slot_507_5[slot_507_3] or 0) + slot_507_2 * 0.03 / 100
				local slot_507_8 = slot_507_6 + slot_507_2 * 0.6 + arg_507_0.maxHealth * slot_507_7
				local slot_507_9 = common.CalculatePhysicalDamage(arg_507_0, slot_507_8, slot_507_0)

				return spell.get_damage(slot_507_9, arg_507_0, slot_507_0, 1)
			end
		},
		Q1 = {
			ignore_obj_radius = 375,
			target_selector = 8,
			prediction = {
				delay = 0.25,
				range = 650,
				type = "Linear",
				boundingRadiusMod = 0,
				width = 50,
				speed = math.huge,
				collision = {
					wall = true,
					minion = false,
					hero = false
				}
			},
			cast_spell = {},
			slot = player:spellSlot(_Q),
			damage = function(arg_508_0, arg_508_1)
				-- function 508
				local slot_508_0 = arg_508_1 or player
				local slot_508_1 = common.GetTotalAP(slot_508_0)
				local slot_508_2 = common.GetBonusAD(slot_508_0)
				local slot_508_3 = slot_508_0:spellSlot(_Q).level
				local slot_508_4 = {
					50,
					75,
					100,
					125,
					150
				}
				local slot_508_5 = {
					0.02,
					0.03,
					0.04,
					0.05,
					0.06
				}
				local slot_508_6 = slot_508_4[slot_508_3] or 0
				local slot_508_7 = (slot_508_5[slot_508_3] or 0) + slot_508_2 * 0.04 / 100
				local slot_508_8 = slot_508_6 + slot_508_2 * 0.6 + arg_508_0.maxHealth * slot_508_7
				local slot_508_9 = common.CalculatePhysicalDamage(arg_508_0, slot_508_8, slot_508_0)

				return spell.get_damage(slot_508_9, arg_508_0, slot_508_0, 2)
			end
		},
		W = {
			ignore_obj_radius = 0,
			target_selector = 8,
			prediction = {
				delay = 0,
				range = 225,
				boundingRadiusMod = 1,
				type = "InRange",
				radius = 225
			},
			cast_spell = {},
			slot = player:spellSlot(_W),
			damage = function(arg_509_0, arg_509_1, arg_509_2)
				-- function 509
				local slot_509_0 = arg_509_1 or player
				local slot_509_1 = common.GetTotalAP(slot_509_0)
				local slot_509_2 = common.GetBonusAD(slot_509_0)
				local slot_509_3 = slot_509_0:spellSlot(_W).level
				local slot_509_4 = (({
					50,
					75,
					100,
					125,
					150
				})[slot_509_3] or 0) + slot_509_2 * 0.5
				local slot_509_5 = common.CalculatePhysicalDamage(arg_509_0, slot_509_4, slot_509_0)

				return spell.get_damage(slot_509_5, arg_509_0, slot_509_0, 2)
			end
		},
		E = {
			ignore_obj_radius = 825,
			target_selector = 8,
			prediction = {
				delay = 0.25,
				range = 350,
				boundingRadiusMod = 0,
				type = "Circular",
				radius = 350,
				speed = math.huge
			},
			prediction2 = {
				delay = 0.25,
				range = 650,
				speed = 1105,
				boundingRadiusMod = 0,
				type = "Circular",
				radius = 300
			},
			cast_spell = {},
			slot = player:spellSlot(_E),
			damage = function(arg_510_0, arg_510_1, arg_510_2)
				-- function 510
				local slot_510_0 = arg_510_1 or player
				local slot_510_1 = common.GetTotalAP(slot_510_0)
				local slot_510_2 = common.GetBonusAD(slot_510_0)
				local slot_510_3 = slot_510_0:spellSlot(_E).level
				local slot_510_4 = {
					40,
					65,
					90,
					115,
					140
				}
				local slot_510_5 = {
					0.4,
					0.45,
					0.5,
					0.55,
					0.6
				}
				local slot_510_6 = (slot_510_4[slot_510_3] or 0) + slot_510_2 * (slot_510_5[slot_510_3] or 0)
				local slot_510_7 = common.CalculatePhysicalDamage(arg_510_0, slot_510_6, slot_510_0)

				return spell.get_damage(slot_510_7, arg_510_0, slot_510_0, 2)
			end
		},
		R = {
			ignore_obj_radius = 4250,
			target_selector = 8,
			prediction = {
				delay = 0.55,
				range = 1200,
				type = "Linear",
				boundingRadiusMod = 1,
				width = 80,
				speed = math.huge,
				collision = {
					wall = true,
					minion = false,
					hero = false
				}
			},
			cast_spell = {},
			slot = player:spellSlot(_R),
			damage = function(arg_511_0, arg_511_1)
				-- function 511
				local slot_511_0 = arg_511_1 or player
				local slot_511_1 = common.GetTotalAP(slot_511_0)
				local slot_511_2 = common.GetBonusAD(slot_511_0)
				local slot_511_3 = slot_511_0:spellSlot(_R).level
				local slot_511_4 = (({
					150,
					250,
					350
				})[slot_511_3] or 0) + slot_511_2 * 0.8
				local slot_511_5 = common.CalculatePhysicalDamage(arg_511_0, slot_511_4, slot_511_0)

				return spell.get_damage(slot_511_5, arg_511_0, slot_511_0, 2)
			end
		},
	}
	spell.Mordekaiser = {
		Q = {
			ignore_obj_radius = 380,
			target_selector = 8,
			prediction = {
				delay = 0.5,
				range = 625,
				type = "Linear",
				boundingRadiusMod = 0,
				width = 80,
				speed = math.huge
			},
			cast_spell = {},
			slot = player:spellSlot(_Q),
			damage = function(arg_525_0, arg_525_1)
				-- function 525
				local slot_525_0 = arg_525_1 or player
				local slot_525_1 = common.GetTotalAP(slot_525_0)
				local slot_525_2 = common.GetBonusAD(slot_525_0)
				local slot_525_3 = slot_525_0:spellSlot(_E).level
				local slot_525_4 = slot_525_0.levelRef
				local slot_525_5 = 0

				if slot_525_4 > 9 then
					slot_525_5 = (slot_525_4 - 9) * 5
				end

				local slot_525_6 = {
					80,
					110,
					140,
					170,
					200
				}
				local slot_525_7 = {
					0.3,
					0.35,
					0.4,
					0.45,
					0.5
				}
				local slot_525_8 = slot_525_6[slot_525_3] or 0
				local slot_525_9 = slot_525_7[slot_525_3] or 0
				local slot_525_10 = slot_525_5 + slot_525_8 + slot_525_1 * 0.7
				local slot_525_11 = common.CalculateMagicDamage(arg_525_0, slot_525_10, slot_525_0) * (1 + slot_525_9)

				return spell.get_damage(slot_525_11, arg_525_0, slot_525_0, 1)
			end
		},
		W = {
			ignore_obj_radius = 0,
			target_selector = 8,
			prediction = {
				delay = 0,
				range = 0,
				boundingRadiusMod = 1,
				type = "InRange",
				radius = 0
			},
			cast_spell = {},
			slot = player:spellSlot(_W),
			damage = function(arg_526_0, arg_526_1)
				-- function 526
				return 0
			end
		},
		E = {
			ignore_obj_radius = 825,
			target_selector = 8,
			prediction = {
				delay = 1,
				range = 1200,
				type = "Linear",
				boundingRadiusMod = 1,
				width = 100,
				speed = math.huge,
				collision = {
					wall = true,
					minion = false,
					hero = false
				}
			},
			cast_spell = {},
			slot = player:spellSlot(_E),
			damage = function(arg_527_0, arg_527_1)
				-- function 527
				local slot_527_0 = arg_527_1 or player
				local slot_527_1 = common.GetTotalAP(slot_527_0)
				local slot_527_2 = common.GetBonusAD(slot_527_0)
				local slot_527_3 = slot_527_0:spellSlot(_E).level
				local slot_527_4 = (({
					70,
					85,
					100,
					115,
					130
				})[slot_527_3] or 0) + slot_527_1 * 0.6
				local slot_527_5 = common.CalculateMagicDamage(arg_527_0, slot_527_4, slot_527_0)

				return spell.get_damage(slot_527_5, arg_527_0, slot_527_0, 1)
			end
		},
		R = {
			ignore_obj_radius = 4250,
			target_selector = 8,
			prediction = {
				delay = 0,
				range = 650,
				boundingRadiusMod = 1,
				type = "InRange",
				radius = 650
			},
			cast_spell = {},
			slot = player:spellSlot(_R),
			damage = function(arg_528_0, arg_528_1)
				-- function 528
				return 0
			end
		}
	}
	spell.Anivia = {
		Q = {
			ignore_obj_radius = 380,
			target_selector = 8,
			prediction = {
				delay = 0.25,
				range = 1100,
				type = "Linear",
				boundingRadiusMod = 1,
				width = 110,
				speed = 950,
				collision = {
					wall = true,
					minion = false,
					hero = false
				}
			},
			cast_spell = {},
			slot = player:spellSlot(_Q),
			damage = function(arg_512_0, arg_512_1)
				-- function 512
				local slot_512_0 = arg_512_1 or player
				local slot_512_1 = common.GetTotalAP(slot_512_0)
				local slot_512_2 = common.GetBonusAD(slot_512_0)
				local slot_512_3 = slot_512_0:spellSlot(_Q).level
				local slot_512_4 = {
					50,
					70,
					90,
					110,
					130
				}
				local slot_512_5 = {
					60,
					95,
					130,
					165,
					200
				}
				local slot_512_6 = slot_512_4[slot_512_3] or 0
				local slot_512_7 = slot_512_5[slot_512_3] or 0
				local slot_512_8 = slot_512_6 + slot_512_1 * 0.25
				local slot_512_9 = slot_512_7 + slot_512_1 * 0.45
				local slot_512_10 = common.CalculateMagicDamage(arg_512_0, slot_512_8, slot_512_0)
				local slot_512_11 = common.CalculateMagicDamage(arg_512_0, slot_512_9, slot_512_0)

				return spell.get_damage(slot_512_10 + slot_512_11, arg_512_0, slot_512_0, 1)
			end
		},
		W = {
			ignore_obj_radius = 0,
			target_selector = 8,
			prediction = {
				delay = 0.25,
				range = 1000,
				type = "Linear",
				boundingRadiusMod = 0,
				width = 1,
				speed = math.huge,
				collision = {
					wall = false,
					minion = false,
					hero = false
				}
			},
			cast_spell = {},
			slot = player:spellSlot(_W),
			damage = function(arg_513_0, arg_513_1)
				-- function 513
				return 0
			end
		},
		E = {
			ignore_obj_radius = 825,
			target_selector = 8,
			prediction = {
				delay = 0.25,
				range = 600,
				speed = 1600,
				boundingRadiusMod = 0,
				type = "InRange",
				radius = 600
			},
			cast_spell = {},
			slot = player:spellSlot(_E),
			damage = function(arg_514_0, arg_514_1, arg_514_2)
				-- function 514
				local slot_514_0 = arg_514_1 or player
				local slot_514_1 = common.GetTotalAP(slot_514_0)
				local slot_514_2 = common.GetBonusAD(slot_514_0)
				local slot_514_3 = slot_514_0:spellSlot(_E).level
				local slot_514_4 = (({
					50,
					75,
					100,
					125,
					150
				})[slot_514_3] or 0) + slot_514_1 * 0.55
				local slot_514_5 = common.CalculateMagicDamage(arg_514_0, slot_514_4, slot_514_0) * 2

				return spell.get_damage(slot_514_5, arg_514_0, slot_514_0, 1)
			end
		},
		R = {
			ignore_obj_radius = 4250,
			target_selector = 8,
			prediction = {
				delay = 0,
				range = 750,
				boundingRadiusMod = 1,
				type = "Circular",
				radius = 50,
				speed = math.huge
			},
			cast_spell = {},
			slot = player:spellSlot(_R),
			damage = function(arg_515_0, arg_515_1)
				-- function 515
				local slot_515_0 = arg_515_1 or player
				local slot_515_1 = common.GetTotalAP(slot_515_0)
				local slot_515_2 = common.GetBonusAD(slot_515_0)
				local slot_515_3 = slot_515_0:spellSlot(_R).level
				local slot_515_4 = (({
					30,
					45,
					60
				})[slot_515_3] or 0) + slot_515_1 * 0.125
				local slot_515_5 = common.CalculateMagicDamage(arg_515_0, slot_515_4, slot_515_0) * 3

				return spell.get_damage(slot_515_5, arg_515_0, slot_515_0, 1)
			end
		}
	}
	spell.Tryndamere = {
		Q = {
			ignore_obj_radius = 380,
			target_selector = 8,
			prediction = {
				delay = 0,
				range = 0,
				boundingRadiusMod = 1,
				type = "InRange",
				radius = 0
			},
			cast_spell = {},
			slot = player:spellSlot(_Q),
			damage = function(arg_516_0, arg_516_1)
				-- function 516
				return 0
			end
		},
		W = {
			ignore_obj_radius = 0,
			target_selector = 8,
			prediction = {
				delay = 0,
				range = 850,
				boundingRadiusMod = 1,
				type = "InRange",
				radius = 850
			},
			cast_spell = {},
			slot = player:spellSlot(_W),
			damage = function(arg_517_0, arg_517_1)
				-- function 517
				return 0
			end
		},
		E = {
			ignore_obj_radius = 825,
			target_selector = 8,
			prediction = {
				delay = 0,
				range = 650,
				type = "Linear",
				boundingRadiusMod = 1,
				width = 110,
				speed = 950,
				collision = {
					wall = true,
					minion = false,
					hero = false
				}
			},
			cast_spell = {},
			slot = player:spellSlot(_E),
			damage = function(arg_518_0, arg_518_1, arg_518_2)
				-- function 518
				local slot_518_0 = arg_518_1 or player
				local slot_518_1 = common.GetTotalAP(slot_518_0)
				local slot_518_2 = common.GetBonusAD(slot_518_0)
				local slot_518_3 = slot_518_0:spellSlot(_E).level
				local slot_518_4 = (({
					75,
					105,
					135,
					165,
					195
				})[slot_518_3] or 0) + slot_518_1 * 0.8 + slot_518_2 * 1.3
				local slot_518_5 = common.CalculatePhysicalDamage(arg_518_0, slot_518_4, slot_518_0)

				return spell.get_damage(slot_518_5, arg_518_0, slot_518_0, 2)
			end
		},
		R = {
			ignore_obj_radius = 4250,
			target_selector = 8,
			prediction = {
				delay = 0,
				range = 225,
				boundingRadiusMod = 1,
				type = "InRange",
				radius = 225
			},
			cast_spell = {},
			slot = player:spellSlot(_R),
			damage = function(arg_519_0, arg_519_1)
				-- function 519
				return 0
			end
		}
	}
	spell.Aurora = {
		Q = {
			ignore_obj_radius = 700,
			target_selector = 8,
			prediction = {
				delay = 0.25,
				range = 900,
				type = "Linear",
				boundingRadiusMod = 0,
				width = 105,
				speed = 1600,
				collision = {
					wall = true,
					minion = false,
					hero = false
				}
			},
			cast_spell = {},
			slot = player:spellSlot(_Q),
			damage = function(arg_471_0, arg_471_1)
				-- function 471
				local slot_471_0 = arg_471_1 or player
				local slot_471_1 = common.GetTotalAP(slot_471_0)
				local slot_471_2 = common.GetTotalAD(slot_471_0)
				local slot_471_3 = slot_471_0:spellSlot(_Q).level
				local slot_471_4 = (({
					45,
					70,
					95,
					120,
					145
				})[slot_471_3] or 0) + slot_471_1 * 0.4
				local slot_471_5 = common.CalculateMagicDamage(arg_471_0, slot_471_4, slot_471_0)

				return spell.get_damage(slot_471_5, arg_471_0, slot_471_0, 1)
			end
		},
		W = {
			ignore_obj_radius = 0,
			target_selector = 8,
			prediction = {
				dashRadius = 300,
				delay = 0,
				range = 300,
				fixedRange = true,
				boundingRadiusModTarget = 0,
				boundingRadiusModSource = 0,
				type = "InRange",
				radius = 300,
				speed = math.huge
			},
			cast_spell = {},
			slot = player:spellSlot(_W),
			damage = function(arg_472_0, arg_472_1)
				-- function 472
				return 0
			end
		},
		E = {
			ignore_obj_radius = 825,
			target_selector = 8,
			prediction = {
				delay = 0.35,
				range = 825,
				type = "Linear",
				boundingRadiusMod = 0,
				width = 120,
				speed = math.huge
			},
			cast_spell = {},
			slot = player:spellSlot(_E),
			damage = function(arg_473_0, arg_473_1)
				-- function 473
				local slot_473_0 = arg_473_1 or player
				local slot_473_1 = common.GetTotalAP(slot_473_0)
				local slot_473_2 = common.GetTotalAD(slot_473_0)
				local slot_473_3 = slot_473_0:spellSlot(_E).level
				local slot_473_4 = (({
					70,
					110,
					150,
					190,
					230
				})[slot_473_3] or 0) + slot_473_1 * 0.8
				local slot_473_5 = common.CalculateMagicDamage(arg_473_0, slot_473_4, slot_473_0)

				return spell.get_damage(slot_473_5, arg_473_0, slot_473_0, 1)
			end
		},
		R = {
			ignore_obj_radius = 4250,
			target_selector = 8,
			prediction = {
				delay = 0,
				range = 750,
				speed = 700,
				boundingRadiusMod = 0,
				type = "Circular",
				radius = 50,
				collision = {
					wall = false,
					minion = false,
					hero = false
				}
			},
			cast_spell = {},
			slot = player:spellSlot(_R),
			damage = function(arg_474_0, arg_474_1)
				-- function 474
				local slot_474_0 = arg_474_1 or player
				local slot_474_1 = common.GetTotalAP(slot_474_0)
				local slot_474_2 = common.GetTotalAD(slot_474_0)
				local slot_474_3 = slot_474_0:spellSlot(_R).level
				local slot_474_4 = (({
					175,
					275,
					375
				})[slot_474_3] or 0) + slot_474_1 * 0.6
				local slot_474_5 = common.CalculateMagicDamage(arg_474_0, slot_474_4, slot_474_0)

				return spell.get_damage(slot_474_5, arg_474_0, slot_474_0, 1)
			end
		}
	}
	spell.Soraka = {
		Q = {
			ignore_obj_radius = 700,
			target_selector = 8,
			prediction = {
				delay = 0.75,
				range = 800,
				boundingRadiusMod = 0,
				type = "Circular",
				radius = 65,
				speed = math.huge
			},
			cast_spell = {},
			slot = player:spellSlot(_Q),
			damage = function(arg_475_0, arg_475_1)
				-- function 475
				local slot_475_0 = arg_475_1 or player
				local slot_475_1 = common.GetTotalAP(slot_475_0)
				local slot_475_2 = common.GetTotalAD(slot_475_0)
				local slot_475_3 = slot_475_0:spellSlot(_Q).level
				local slot_475_4 = (({
					85,
					120,
					155,
					190,
					225
				})[slot_475_3] or 0) + slot_475_1 * 0.35
				local slot_475_5 = common.CalculateMagicDamage(arg_475_0, slot_475_4, slot_475_0)

				return spell.get_damage(slot_475_5, arg_475_0, slot_475_0, 1)
			end
		},
		W = {
			ignore_obj_radius = 0,
			target_selector = 8,
			prediction = {
				dashRadius = 0,
				delay = 0.25,
				range = 550,
				fixedRange = true,
				boundingRadiusModTarget = 0,
				boundingRadiusModSource = 0,
				type = "InRange",
				radius = 550,
				speed = math.huge
			},
			cast_spell = {},
			slot = player:spellSlot(_W),
			damage = function(arg_476_0, arg_476_1)
				-- function 476
				return 0
			end
		},
		E = {
			ignore_obj_radius = 825,
			target_selector = 8,
			prediction = {
				delay = 0.25,
				range = 925,
				boundingRadiusMod = 0,
				type = "Circular",
				radius = 65,
				speed = math.huge
			},
			cast_spell = {},
			slot = player:spellSlot(_E),
			damage = function(arg_477_0, arg_477_1)
				-- function 477
				local slot_477_0 = arg_477_1 or player
				local slot_477_1 = common.GetTotalAP(slot_477_0)
				local slot_477_2 = common.GetTotalAD(slot_477_0)
				local slot_477_3 = slot_477_0:spellSlot(_E).level
				local slot_477_4 = (({
					70,
					95,
					120,
					145,
					170
				})[slot_477_3] or 0) + slot_477_1 * 0.4
				local slot_477_5 = common.CalculateMagicDamage(arg_477_0, slot_477_4, slot_477_0)

				return spell.get_damage(slot_477_5, arg_477_0, slot_477_0, 1)
			end
		},
		R = {
			ignore_obj_radius = 4250,
			target_selector = 8,
			prediction = {
				dashRadius = 0,
				delay = 0.25,
				range = 550,
				fixedRange = true,
				boundingRadiusModTarget = 0,
				boundingRadiusModSource = 0,
				type = "InRange",
				radius = 550,
				speed = math.huge
			},
			cast_spell = {},
			slot = player:spellSlot(_R),
			damage = function(arg_478_0, arg_478_1)
				-- function 478
				return 0
			end
		}
	}
	spell.Elise = {
		Q = {
			ignore_obj_radius = 700,
			target_selector = 8,
			prediction = {
				delay = 0.75,
				range = 575,
				boundingRadiusMod = 0,
				type = "InRange",
				radius = 65,
				speed = math.huge
			},
			cast_spell = {},
			slot = player:spellSlot(_Q),
			damage = function(arg_479_0, arg_479_1)
				-- function 479
				local slot_479_0 = arg_479_1 or player
				local slot_479_1 = common.GetTotalAP(slot_479_0)
				local slot_479_2 = common.GetTotalAD(slot_479_0)
				local slot_479_3 = slot_479_0:spellSlot(_Q).level
				local slot_479_4 = (({
					40,
					75,
					110,
					145,
					180
				})[slot_479_3] or 0) + slot_479_1 * 0
				local slot_479_5 = common.CalculateMagicDamage(arg_479_0, slot_479_4, slot_479_0)

				return spell.get_damage(slot_479_5, arg_479_0, slot_479_0, 1)
			end
		},
		Q1 = {
			ignore_obj_radius = 700,
			target_selector = 8,
			prediction = {
				delay = 0.75,
				range = 475,
				boundingRadiusMod = 0,
				type = "InRange",
				radius = 65,
				speed = math.huge
			},
			cast_spell = {},
			slot = player:spellSlot(_Q),
			damage = function(arg_480_0, arg_480_1)
				-- function 480
				local slot_480_0 = arg_480_1 or player
				local slot_480_1 = common.GetTotalAP(slot_480_0)
				local slot_480_2 = common.GetTotalAD(slot_480_0)
				local slot_480_3 = slot_480_0:spellSlot(_Q).level
				local slot_480_4 = (({
					60,
					90,
					120,
					150,
					180
				})[slot_480_3] or 0) + slot_480_1 * 0
				local slot_480_5 = common.CalculateMagicDamage(arg_480_0, slot_480_4, slot_480_0)

				return spell.get_damage(slot_480_5, arg_480_0, slot_480_0, 1)
			end
		},
		W = {
			ignore_obj_radius = 0,
			target_selector = 8,
			prediction = {
				delay = 0.5,
				range = 950,
				boundingRadiusMod = 0,
				type = "Circular",
				radius = 65,
				speed = math.huge
			},
			cast_spell = {},
			slot = player:spellSlot(_W),
			damage = function(arg_481_0, arg_481_1)
				-- function 481
				local slot_481_0 = arg_481_1 or player
				local slot_481_1 = common.GetTotalAP(slot_481_0)
				local slot_481_2 = common.GetTotalAD(slot_481_0)
				local slot_481_3 = slot_481_0:spellSlot(_W).level
				local slot_481_4 = (({
					60,
					105,
					150,
					195,
					240
				})[slot_481_3] or 0) + slot_481_1 * 0.95
				local slot_481_5 = common.CalculateMagicDamage(arg_481_0, slot_481_4, slot_481_0)

				return spell.get_damage(slot_481_5, arg_481_0, slot_481_0, 1)
			end
		},
		W = {
			ignore_obj_radius = 0,
			target_selector = 8,
			prediction = {
				delay = 0.5,
				range = 950,
				boundingRadiusMod = 0,
				type = "Circular",
				radius = 65,
				speed = math.huge
			},
			cast_spell = {},
			slot = player:spellSlot(_W),
			damage = function(arg_482_0, arg_482_1)
				-- function 482
				return 0
			end
		},
		E = {
			ignore_obj_radius = 825,
			target_selector = 8,
			prediction = {
				delay = 0.25,
				range = 925,
				type = "Linear",
				boundingRadiusMod = 1,
				width = 55,
				speed = 1600
			},
			cast_spell = {},
			slot = player:spellSlot(_E),
			damage = function(arg_483_0, arg_483_1)
				-- function 483
				return 0
			end
		},
		E = {
			ignore_obj_radius = 825,
			target_selector = 8,
			prediction = {
				delay = 0.25,
				range = 925,
				type = "InRange",
				boundingRadiusMod = 1,
				width = 55,
				speed = 1600
			},
			cast_spell = {},
			slot = player:spellSlot(_E),
			damage = function(arg_484_0, arg_484_1)
				-- function 484
				return 0
			end
		},
		R = {
			ignore_obj_radius = 4250,
			target_selector = 8,
			prediction = {
				dashRadius = 0,
				delay = 0.25,
				range = 550,
				fixedRange = true,
				boundingRadiusModTarget = 0,
				boundingRadiusModSource = 0,
				type = "InRange",
				radius = 550,
				speed = math.huge
			},
			cast_spell = {},
			slot = player:spellSlot(_R),
			damage = function(arg_485_0, arg_485_1)
				-- function 485
				return 0
			end
		}
	}
	spell.Seraphine = {
		Q = {
			ignore_obj_radius = 700,
			target_selector = 8,
			prediction = {
				delay = 0.25,
				range = 900,
				speed = 1300,
				boundingRadiusMod = 0,
				type = "Circular",
				radius = 65
			},
			cast_spell = {},
			slot = player:spellSlot(_Q),
			damage = function(arg_486_0, arg_486_1)
				-- function 486
				local slot_486_0 = arg_486_1 or player
				local slot_486_1 = common.GetTotalAP(slot_486_0)
				local slot_486_2 = common.GetTotalAD(slot_486_0)
				local slot_486_3 = slot_486_0:spellSlot(_Q).level
				local slot_486_4 = (({
					60,
					85,
					110,
					135,
					160
				})[slot_486_3] or 0) + slot_486_1 * 0.5
				local slot_486_5 = common.CalculateMagicDamage(arg_486_0, slot_486_4, slot_486_0)

				return spell.get_damage(slot_486_5, arg_486_0, slot_486_0, 1)
			end
		},
		W = {
			ignore_obj_radius = 0,
			target_selector = 8,
			prediction = {
				dashRadius = 0,
				delay = 0.25,
				range = 800,
				fixedRange = true,
				boundingRadiusModTarget = 0,
				boundingRadiusModSource = 0,
				type = "InRange",
				radius = 800,
				speed = math.huge
			},
			cast_spell = {},
			slot = player:spellSlot(_W),
			damage = function(arg_487_0, arg_487_1)
				-- function 487
				return 0
			end
		},
		E = {
			ignore_obj_radius = 825,
			target_selector = 8,
			prediction = {
				delay = 0.25,
				range = 1250,
				type = "Linear",
				boundingRadiusMod = 1,
				width = 70,
				speed = 1200
			},
			cast_spell = {},
			slot = player:spellSlot(_E),
			damage = function(arg_488_0, arg_488_1)
				-- function 488
				local slot_488_0 = arg_488_1 or player
				local slot_488_1 = common.GetTotalAP(slot_488_0)
				local slot_488_2 = common.GetTotalAD(slot_488_0)
				local slot_488_3 = slot_488_0:spellSlot(_E).level
				local slot_488_4 = (({
					70,
					100,
					130,
					160,
					190
				})[slot_488_3] or 0) + slot_488_1 * 0.5
				local slot_488_5 = common.CalculateMagicDamage(arg_488_0, slot_488_4, slot_488_0)

				return spell.get_damage(slot_488_5, arg_488_0, slot_488_0, 1)
			end
		},
		R = {
			ignore_obj_radius = 4250,
			target_selector = 8,
			prediction = {
				delay = 0.5,
				range = 1150,
				type = "Linear",
				boundingRadiusMod = 1,
				width = 80,
				speed = 1200
			},
			cast_spell = {},
			slot = player:spellSlot(_R),
			damage = function(arg_489_0, arg_489_1)
				-- function 489
				local slot_489_0 = arg_489_1 or player
				local slot_489_1 = common.GetTotalAP(slot_489_0)
				local slot_489_2 = common.GetTotalAD(slot_489_0)
				local slot_489_3 = slot_489_0:spellSlot(_R).level
				local slot_489_4 = (({
					150,
					200,
					250
				})[slot_489_3] or 0) + slot_489_1 * 0.4
				local slot_489_5 = common.CalculateMagicDamage(arg_489_0, slot_489_4, slot_489_0)

				return spell.get_damage(slot_489_5, arg_489_0, slot_489_0, 1)
			end
		}
	}
	spell.Talon = {
		Q = {
			ignore_obj_radius = 700,
			target_selector = 8,
			prediction = {
				delay = 0.25,
				range = 575,
				speed = 1400,
				boundingRadiusMod = 0,
				type = "InRange",
				radius = 65
			},
			cast_spell = {},
			slot = player:spellSlot(_Q),
			damage = function(arg_490_0, arg_490_1)
				-- function 490
				local slot_490_0 = arg_490_1 or player
				local slot_490_1 = common.GetTotalAP(slot_490_0)
				local slot_490_2 = common.GetBonusAD(slot_490_0)
				local slot_490_3 = slot_490_0:spellSlot(_Q).level
				local slot_490_4 = (({
					65,
					85,
					105,
					125,
					145
				})[slot_490_3] or 0) + slot_490_2 * 1
				local slot_490_5 = common.CalculatePhysicalDamage(arg_490_0, slot_490_4, slot_490_0)

				if arg_490_0.pos:dist(player.pos) < 225 then
					slot_490_5 = slot_490_5 * 1.5
				end

				return spell.get_damage(slot_490_5, arg_490_0, slot_490_0, 2)
			end
		},
		W = {
			ignore_obj_radius = 0,
			target_selector = 8,
			prediction = {
				delay = 0.25,
				range = 850,
				type = "Linear",
				boundingRadiusMod = 1,
				width = 75,
				speed = 2500
			},
			cast_spell = {},
			slot = player:spellSlot(_W),
			damage = function(arg_491_0, arg_491_1, arg_491_2)
				-- function 491
				local slot_491_0 = arg_491_1 or player
				local slot_491_1 = common.GetTotalAP(slot_491_0)
				local slot_491_2 = common.GetBonusAD(slot_491_0)
				local slot_491_3 = slot_491_0:spellSlot(_W).level
				local slot_491_4 = {
					50,
					60,
					70,
					80,
					90
				}
				local slot_491_5 = {
					60,
					90,
					120,
					150,
					180
				}
				local slot_491_6 = slot_491_4[slot_491_3] or 0
				local slot_491_7 = slot_491_5[slot_491_3] or 0
				local slot_491_8 = slot_491_6 + slot_491_2 * 0.4
				local slot_491_9 = slot_491_7 + slot_491_2 * 0.9
				local slot_491_10 = common.CalculatePhysicalDamage(arg_491_0, slot_491_8 + slot_491_9, slot_491_0)

				return spell.get_damage(slot_491_10, arg_491_0, slot_491_0, 2)
			end
		},
		E = {
			ignore_obj_radius = 825,
			target_selector = 8,
			prediction = {
				delay = 0.25,
				range = 1250,
				type = "Linear",
				boundingRadiusMod = 1,
				width = 70,
				speed = 1200
			},
			cast_spell = {},
			slot = player:spellSlot(_E),
			damage = function(arg_492_0, arg_492_1)
				-- function 492
				return 0
			end
		},
		R = {
			ignore_obj_radius = 4250,
			target_selector = 8,
			prediction = {
				delay = 0,
				range = 550,
				type = "Linear",
				boundingRadiusMod = 1,
				width = 80,
				speed = 2400
			},
			cast_spell = {},
			slot = player:spellSlot(_R),
			damage = function(arg_493_0, arg_493_1)
				-- function 493
				local slot_493_0 = arg_493_1 or player
				local slot_493_1 = common.GetTotalAP(slot_493_0)
				local slot_493_2 = common.GetBonusAD(slot_493_0)
				local slot_493_3 = slot_493_0:spellSlot(_R).level
				local slot_493_4 = (({
					90,
					135,
					180
				})[slot_493_3] or 0) + slot_493_2 * 1
				local slot_493_5 = common.CalculatePhysicalDamage(arg_493_0, slot_493_4, slot_493_0)

				return spell.get_damage(slot_493_5, arg_493_0, slot_493_0, 2)
			end
		}
	}
	spell.Hecarim = {
		Q = {
			ignore_obj_radius = 375,
			target_selector = 8,
			prediction = {
				delay = 0,
				range = 350,
				boundingRadiusMod = 1,
				type = "InRange",
				radius = 350
			},
			cast_spell = {},
			slot = player:spellSlot(_Q),
			damage = function(arg_494_0, arg_494_1)
				-- function 494
				local slot_494_0 = arg_494_1 or player
				local slot_494_1 = common.GetTotalAP(slot_494_0)
				local slot_494_2 = common.GetBonusAD(slot_494_0)
				local slot_494_3 = slot_494_0:spellSlot(_Q).level
				local slot_494_4 = (({
					60,
					85,
					110,
					135,
					160
				})[slot_494_3] or 0) + slot_494_2 * 0.9
				local slot_494_5 = common.CalculatePhysicalDamage(arg_494_0, slot_494_4, slot_494_0)

				return spell.get_damage(slot_494_5, arg_494_0, slot_494_0, 2)
			end
		},
		W = {
			ignore_obj_radius = 0,
			target_selector = 8,
			prediction = {
				delay = 0,
				range = 525,
				boundingRadiusMod = 1,
				type = "InRange",
				radius = 525
			},
			cast_spell = {},
			slot = player:spellSlot(_W),
			damage = function(arg_495_0, arg_495_1, arg_495_2)
				-- function 495
				local slot_495_0 = arg_495_1 or player
				local slot_495_1 = common.GetTotalAP(slot_495_0)
				local slot_495_2 = common.GetBonusAD(slot_495_0)
				local slot_495_3 = slot_495_0:spellSlot(_W).level
				local slot_495_4 = (({
					20,
					30,
					40,
					50,
					60
				})[slot_495_3] or 0) + slot_495_1 * 0.2
				local slot_495_5 = common.CalculateMagicDamage(arg_495_0, slot_495_4, slot_495_0)

				return spell.get_damage(slot_495_5, arg_495_0, slot_495_0, 1)
			end
		},
		E = {
			ignore_obj_radius = 825,
			target_selector = 8,
			prediction = {
				delay = 0.25,
				range = 1250,
				type = "Linear",
				boundingRadiusMod = 1,
				width = 70,
				speed = 1200
			},
			cast_spell = {},
			slot = player:spellSlot(_E),
			damage = function(arg_496_0, arg_496_1)
				-- function 496
				local slot_496_0 = arg_496_1 or player
				local slot_496_1 = common.GetTotalAP(slot_496_0)
				local slot_496_2 = common.GetBonusAD(slot_496_0)
				local slot_496_3 = slot_496_0:spellSlot(_E).level
				local slot_496_4 = (({
					30,
					45,
					60,
					75,
					90
				})[slot_496_3] or 0) + slot_496_2 * 0.5
				local slot_496_5 = common.CalculateMagicDamage(arg_496_0, slot_496_4, slot_496_0)

				return spell.get_damage(slot_496_5, arg_496_0, slot_496_0, 2)
			end
		},
		R = {
			ignore_obj_radius = 4250,
			target_selector = 8,
			prediction = {
				delay = 0,
				range = 1000,
				type = "Linear",
				boundingRadiusMod = 1,
				width = 80,
				speed = 1100,
				collision = {
					wall = true,
					minion = false,
					hero = false
				}
			},
			prediction2 = {
				delay = 0,
				range = 1000,
				type = "Linear",
				ig = true,
				boundingRadiusMod = 1,
				width = 80,
				speed = 1100,
				collision = {
					wall = true,
					minion = false,
					hero = false
				}
			},
			cast_spell = {},
			slot = player:spellSlot(_R),
			damage = function(arg_497_0, arg_497_1)
				-- function 497
				local slot_497_0 = arg_497_1 or player
				local slot_497_1 = common.GetTotalAP(slot_497_0)
				local slot_497_2 = common.GetBonusAD(slot_497_0)
				local slot_497_3 = slot_497_0:spellSlot(_R).level
				local slot_497_4 = (({
					150,
					250,
					350
				})[slot_497_3] or 0) + slot_497_1 * 1
				local slot_497_5 = common.CalculateMagicDamage(arg_497_0, slot_497_4, slot_497_0)

				return spell.get_damage(slot_497_5, arg_497_0, slot_497_0, 1)
			end
		}
	}
	spell.Annie = {
		Q = {
			ignore_obj_radius = 375,
			target_selector = 8,
			prediction = {
				delay = 0,
				range = 625,
				boundingRadiusMod = 1,
				type = "InRange",
				radius = 625
			},
			cast_spell = {},
			slot = player:spellSlot(_Q),
			damage = function(arg_498_0, arg_498_1)
				-- function 498
				local slot_498_0 = arg_498_1 or player
				local slot_498_1 = common.GetTotalAP(slot_498_0)
				local slot_498_2 = common.GetBonusAD(slot_498_0)
				local slot_498_3 = slot_498_0:spellSlot(_Q).level
				local slot_498_4 = (({
					70,
					115,
					160,
					205,
					250
				})[slot_498_3] or 0) + slot_498_1 * 0.85
				local slot_498_5 = common.CalculateMagicDamage(arg_498_0, slot_498_4, slot_498_0)

				return spell.get_damage(slot_498_5, arg_498_0, slot_498_0, 1)
			end
		},
		W = {
			ignore_obj_radius = 0,
			target_selector = 8,
			prediction = {
				delay = 0.25,
				range = 600,
				type = "Linear",
				boundingRadiusMod = 1,
				width = 40,
				speed = math.huge
			},
			cast_spell = {},
			slot = player:spellSlot(_W),
			damage = function(arg_499_0, arg_499_1, arg_499_2)
				-- function 499
				local slot_499_0 = arg_499_1 or player
				local slot_499_1 = common.GetTotalAP(slot_499_0)
				local slot_499_2 = common.GetBonusAD(slot_499_0)
				local slot_499_3 = slot_499_0:spellSlot(_W).level
				local slot_499_4 = (({
					70,
					115,
					160,
					205,
					250
				})[slot_499_3] or 0) + slot_499_1 * 0.85
				local slot_499_5 = common.CalculateMagicDamage(arg_499_0, slot_499_4, slot_499_0)

				return spell.get_damage(slot_499_5, arg_499_0, slot_499_0, 1)
			end
		},
		E = {
			ignore_obj_radius = 825,
			target_selector = 8,
			prediction = {
				delay = 0,
				range = 0,
				boundingRadiusMod = 0,
				type = "InRange",
				radius = 0
			},
			cast_spell = {},
			slot = player:spellSlot(_E),
			damage = function(arg_500_0, arg_500_1)
				-- function 500
				local slot_500_0 = arg_500_1 or player
				local slot_500_1 = common.GetTotalAP(slot_500_0)
				local slot_500_2 = common.GetBonusAD(slot_500_0)
				local slot_500_3 = slot_500_0:spellSlot(_E).level
				local slot_500_4 = (({
					25,
					35,
					45,
					55,
					65
				})[slot_500_3] or 0) + slot_500_1 * 0.4
				local slot_500_5 = common.CalculateMagicDamage(arg_500_0, slot_500_4, slot_500_0)

				return spell.get_damage(slot_500_5, arg_500_0, slot_500_0, 1)
			end
		},
		R = {
			ignore_obj_radius = 4250,
			target_selector = 8,
			prediction = {
				delay = 0.25,
				range = 600,
				boundingRadiusMod = 1,
				type = "Circular",
				radius = 50,
				speed = math.huge
			},
			cast_spell = {},
			slot = player:spellSlot(_R),
			damage = function(arg_501_0, arg_501_1)
				-- function 501
				local slot_501_0 = arg_501_1 or player
				local slot_501_1 = common.GetTotalAP(slot_501_0)
				local slot_501_2 = common.GetBonusAD(slot_501_0)
				local slot_501_3 = slot_501_0:spellSlot(_R).level
				local slot_501_4 = (({
					150,
					275,
					400
				})[slot_501_3] or 0) + slot_501_1 * 0.75
				local slot_501_5 = common.CalculateMagicDamage(arg_501_0, slot_501_4, slot_501_0)

				return spell.get_damage(slot_501_5, arg_501_0, slot_501_0, 1)
			end
		}
	}
	spell.RekSai = {
		Q = {
			ignore_obj_radius = 375,
			target_selector = 8,
			prediction = {
				delay = 0,
				range = 625,
				boundingRadiusMod = 1,
				type = "InRange",
				radius = 625
			},
			cast_spell = {},
			slot = player:spellSlot(_Q),
			damage = function(arg_502_0, arg_502_1)
				-- function 502
				local slot_502_0 = arg_502_1 or player
				local slot_502_1 = slot_502_0:spellSlot(_Q).level
				local slot_502_2 = ({
					0.3,
					0.35,
					0.4,
					0.45,
					0.5
				})[slot_502_1] or 0
				local slot_502_3 = common.CAA(arg_502_0, slot_502_0) * (1 + slot_502_2)

				return spell.get_damage(slot_502_3, arg_502_0, slot_502_0, 1)
			end
		},
		Q1 = {
			ignore_obj_radius = 375,
			target_selector = 8,
			prediction = {
				delay = 0.1,
				range = 1500,
				type = "Linear",
				boundingRadiusMod = 1,
				width = 65,
				speed = 1950,
				collision = {
					wall = true,
					minion = true,
					hero = true
				}
			},
			cast_spell = {},
			slot = player:spellSlot(_Q),
			damage = function(arg_503_0, arg_503_1)
				-- function 503
				local slot_503_0 = arg_503_1 or player
				local slot_503_1 = common.GetTotalAP(slot_503_0)
				local slot_503_2 = common.GetBonusAD(slot_503_0)
				local slot_503_3 = slot_503_0:spellSlot(_Q).level
				local slot_503_4 = (({
					50,
					80,
					110,
					140,
					170
				})[slot_503_3] or 0) + slot_503_2 * 0.25 + slot_503_1 * 0.7
				local slot_503_5 = common.CalculateMagicDamage(arg_503_0, slot_503_4, slot_503_0)

				return spell.get_damage(slot_503_5, arg_503_0, slot_503_0, 1)
			end
		},
		W = {
			ignore_obj_radius = 0,
			target_selector = 8,
			prediction = {
				delay = 0,
				range = 225,
				boundingRadiusMod = 1,
				type = "InRange",
				radius = 225
			},
			cast_spell = {},
			slot = player:spellSlot(_W),
			damage = function(arg_504_0, arg_504_1, arg_504_2)
				-- function 504
				local slot_504_0 = arg_504_1 or player
				local slot_504_1 = common.GetTotalAP(slot_504_0)
				local slot_504_2 = common.GetBonusAD(slot_504_0)
				local slot_504_3 = slot_504_0:spellSlot(_W).level
				local slot_504_4 = (({
					30,
					55,
					80,
					105,
					130
				})[slot_504_3] or 0) + slot_504_1 * 0.8
				local slot_504_5 = common.CalculateMagicDamage(arg_504_0, slot_504_4, slot_504_0)

				return spell.get_damage(slot_504_5, arg_504_0, slot_504_0, 1)
			end
		},
		E = {
			ignore_obj_radius = 825,
			target_selector = 8,
			prediction = {
				delay = 0,
				range = 225,
				boundingRadiusMod = 0,
				type = "InRange",
				radius = 255
			},
			cast_spell = {},
			slot = player:spellSlot(_E),
			damage = function(arg_505_0, arg_505_1, arg_505_2)
				-- function 505
				local slot_505_0 = arg_505_1 or player
				local slot_505_1 = common.GetTotalAP(slot_505_0)
				local slot_505_2 = common.GetTotalAD(slot_505_0)
				local slot_505_3 = slot_505_0:spellSlot(_E).level
				local slot_505_4 = (({
					0.08,
					0.095,
					0.11,
					0.125,
					0.14
				})[slot_505_3] or 0) * arg_505_0.maxHealth

				if slot_505_0.mana >= slot_505_0.maxMana or arg_505_2 then
					slot_505_4 = slot_505_4 + slot_505_2 * 1
				else
					slot_505_4 = slot_505_2
				end

				local slot_505_5 = common.CalculatePhysicalDamage(arg_505_0, slot_505_4, slot_505_0)

				return spell.get_damage(slot_505_5, arg_505_0, slot_505_0, 2)
			end
		},
		R = {
			ignore_obj_radius = 4250,
			target_selector = 8,
			prediction = {
				delay = 0.25,
				range = 1500,
				speed = 1400,
				boundingRadiusMod = 0,
				type = "InRange",
				radius = 0
			},
			cast_spell = {},
			slot = player:spellSlot(_R),
			damage = function(arg_506_0, arg_506_1)
				-- function 506
				local slot_506_0 = arg_506_1 or player
				local slot_506_1 = common.GetTotalAP(slot_506_0)
				local slot_506_2 = common.GetBonusAD(slot_506_0)
				local slot_506_3 = slot_506_0:spellSlot(_R).level
				local slot_506_4 = {
					150,
					300,
					450
				}
				local slot_506_5 = {
					0.25,
					0.3,
					0.35
				}
				local slot_506_6 = slot_506_4[slot_506_3] or 0
				local slot_506_7 = slot_506_5[slot_506_3] or 0
				local slot_506_8 = slot_506_6 + slot_506_2 * 1 + (arg_506_0.maxHealth - arg_506_0.health) * slot_506_7
				local slot_506_9 = common.CalculatePhysicalDamage(arg_506_0, slot_506_8, slot_506_0)

				return spell.get_damage(slot_506_9, arg_506_0, slot_506_0, 2)
			end
		}
	}
spell.Naafiri = {
	Q = {
		prediction = {
			type = 'Linear',
			range = 900,
			delay = 0.25,
			width = 50,
			speed = 1700,
			boundingRadiusMod = 0,
			collision = {
			  hero = false,
			  minion = false,
			  wall = true,
			},
		},
		slot = player:spellSlot(_Q),
		damage = function(target,source)
			local source = source or player
			local ad = common.GetBonusAD(source)
			local dmg_tb = {35,45,55,65,75}



			local level = source:spellSlot(_Q).level
			local Dmg = dmg_tb[level] or 0;

			local damages = (Dmg + ad*0.2)
			if target.buff["NaafiriQBleed"] and source:spellSlot(_Q).name =="NaafiriQRecast" then
				dmg_tb = {35,50,65,80,95}
				Dmg = dmg_tb[level] or 0;
				damages = (Dmg + ad*0.7)
			end

			local total =  common.cad(target,damages,source)

			return spell.get_damage(total,target,source,2)
		end,
	},
	W = {
		prediction = {
			type = 'InRange',
			range = 700,
			radius = 700,
			speed = math.huge,
			delay = 0,
		},
		slot = player:spellSlot(_W),
		ignore_obj_radius = 0,
		damage = function(target,source)
			local source = source or player
			local ad = common.GetBonusAD(source)
			local dmg_tb = {30,70,110,150,190}
			local dmg_tb2 = {3,7,11,15,19}
			local level = source:spellSlot(_W).level
			local Dmg = dmg_tb[level] or 0;
			local Dmg2 = dmg_tb2[level] or 0;
			local damages = (Dmg + ad*.8)
			local damages2 = (Dmg2 + ad*.08)
			local total =  common.cad(target,damages,source)
			local count_pass = 0
			for i=0, objManager.minions.size[TEAM_ALLY]-1 do
				local minion = objManager.minions[TEAM_ALLY][i]
				if minion.name == "Packmate" and minion.owner == source then
					count_pass = count_pass+1
				end
			end

			if count_pass > 0 then
				local d2 = common.cad(target,damages2,source)
				total = total + d2 * count_pass
			end


			return spell.get_damage(total,target,source,2)
		end,
	},
	E = {
		prediction = {
			type = 'Circular',
			range = 350,
			delay = 0,
			radius = 50,
			speed = 950,
			boundingRadiusMod = 0
		},
		slot = player:spellSlot(_E),
		ignore_obj_radius = 0,
		damage = function(target,source,stacks)
			local source = source or player
			local ad = common.GetBonusAD(source)
			local dmg_tb = {100,160,220,280,340}
			local level = source:spellSlot(_E).level
			local Dmg = dmg_tb[level] or 0;
			local damages = (Dmg + ad*1.7)
			local total =  common.cad(target,damages,source)
			return spell.get_damage(total,target,source,1)
		end,
	},
	R = {
		prediction = {
			type = 'InRange',
			range = 700,
			radius = 700,
			speed = math.huge,
			delay = 0,
		},
		slot = player:spellSlot(_R),
		ignore_obj_radius = 0,
		damage = function(target,source)
			return 0
		end,
	},

}
	spell.Mel = {
		P = {
			damage = function(arg_520_0, arg_520_1)
				-- function 520
				local slot_520_0 = arg_520_1 or player
				local slot_520_1 = common.getBuffStacks(arg_520_0, "melpassiveoverwhelm")
				local slot_520_2 = 65 + common.GetTotalAP(slot_520_0) * 0.25 + (3 + common.GetTotalAP(slot_520_0) * 0.0075) * slot_520_1

				return common.CalculateMagicDamage(arg_520_0, slot_520_2, slot_520_0)
			end
		},
		Q = {
			ignore_obj_radius = 380,
			target_selector = 8,
			prediction = {
				delay = 0.25,
				range = 1065,
				speed = 5000,
				boundingRadiusMod = 0,
				type = "Circular",
				radius = 80
			},
			cast_spell = {},
			slot = player:spellSlot(_Q),
			damage = function(arg_521_0, arg_521_1)
				-- function 521
				local slot_521_0 = arg_521_1 or player
				local slot_521_1 = common.GetTotalAP(slot_521_0)
				local slot_521_2 = common.GetBonusAD(slot_521_0)
				local slot_521_3 = slot_521_0:spellSlot(_E).level
				local slot_521_4 = {
					13,
					16,
					19,
					22,
					25
				}
				local slot_521_5 = {
					6,
					7,
					8,
					9,
					10
				}
				local slot_521_6 = slot_521_4[slot_521_3] or 0
				local slot_521_7 = slot_521_5[slot_521_3] or 0
				local slot_521_8 = slot_521_6 + slot_521_1 * 0.085
				local slot_521_9 = common.CalculateMagicDamage(arg_521_0, slot_521_8, slot_521_0) * slot_521_7

				if slot_521_9 + spell.Mel.P.damage(arg_521_0, slot_521_0) > arg_521_0.health then
					slot_521_9 = 99999
				end

				return spell.get_damage(slot_521_9, arg_521_0, slot_521_0, 1)
			end
		},
		W = {
			ignore_obj_radius = 0,
			target_selector = 8,
			prediction = {
				delay = 0,
				range = 850,
				boundingRadiusMod = 1,
				type = "InRange",
				radius = 850
			},
			cast_spell = {},
			slot = player:spellSlot(_W),
			damage = function(arg_522_0, arg_522_1)
				-- function 522
				return 0
			end
		},
		E = {
			ignore_obj_radius = 825,
			target_selector = 8,
			prediction = {
				delay = 0,
				range = 1200,
				type = "Linear",
				boundingRadiusMod = 1,
				width = 100,
				speed = 950,
				collision = {
					wall = true,
					minion = false,
					hero = false
				}
			},
			cast_spell = {},
			slot = player:spellSlot(_E),
			damage = function(arg_523_0, arg_523_1)
				-- function 523
				local slot_523_0 = arg_523_1 or player
				local slot_523_1 = common.GetTotalAP(slot_523_0)
				local slot_523_2 = common.GetBonusAD(slot_523_0)
				local slot_523_3 = slot_523_0:spellSlot(_E).level
				local slot_523_4 = (({
					60,
					100,
					140,
					180,
					220
				})[slot_523_3] or 0) + slot_523_1 * 0.5
				local slot_523_5 = common.CalculateMagicDamage(arg_523_0, slot_523_4, slot_523_0)

				if slot_523_5 + spell.Mel.P.damage(arg_523_0, slot_523_0) > arg_523_0.health then
					slot_523_5 = 99999
				end

				return spell.get_damage(slot_523_5, arg_523_0, slot_523_0, 1)
			end
		},
		R = {
			ignore_obj_radius = 4250,
			target_selector = 8,
			prediction = {
				delay = 0,
				range = 225,
				boundingRadiusMod = 1,
				type = "InRange",
				radius = 225
			},
			cast_spell = {},
			slot = player:spellSlot(_R),
			damage = function(arg_524_0, arg_524_1)
				-- function 524
				local slot_524_0 = arg_524_1 or player
				local slot_524_1 = common.GetTotalAP(slot_524_0)
				local slot_524_2 = common.GetBonusAD(slot_524_0)
				local slot_524_3 = slot_524_0:spellSlot(_E).level
				local slot_524_4 = {
					100,
					150,
					200
				}
				local slot_524_5 = {
					4,
					7,
					10
				}
				local slot_524_6 = slot_524_4[slot_524_3] or 0
				local slot_524_7 = slot_524_5[slot_524_3] or 0
				local slot_524_8 = common.getBuffStacks(arg_524_0, "melpassiveoverwhelm")
				local slot_524_9 = slot_524_6 + slot_524_1 * 0.3
				local slot_524_10 = (slot_524_7 + slot_524_1 * 0.025) * slot_524_8
				local slot_524_11 = common.CalculateMagicDamage(arg_524_0, slot_524_9 + slot_524_10, slot_524_0)

				if slot_524_11 + spell.Mel.P.damage(arg_524_0, slot_524_0) > arg_524_0.health then
					slot_524_11 = 99999
				end

				return spell.get_damage(slot_524_11, arg_524_0, slot_524_0, 1)
			end
		}
	}
	spell.Lulu_old = {
		Q = {
			ignore_obj_radius = 625,
			target_selector = 8,
			prediction = {
				hitchance = 0,
				new_path = 0.5,
				boundingRadiusMod = 1,
				width = 60,
				speed = 1500,
				delay = 0.25,
				range = 925,
				type = "Linear",
				out_range = 925,
				collision = {
					wall = true,
					minion = false,
					hero = false
				}
			},
			cast_spell = {
				type = "pos",
				slot = _Q,
				arg1 = function(arg_85_0)
					-- function 85
					local slot_85_0 = arg_85_0.ts.seg
					local slot_85_1 = arg_85_0.ts.obj

					return slot_85_0.endPos
				end
			},
			slot = player:spellSlot(_Q),
			damage = function(arg_86_0, arg_86_1)
				-- function 86
				if not arg_86_0 then
					return 0
				end

				local slot_86_0 = arg_86_1 or player

				spell_tb = {
					70,
					105,
					140,
					175,
					210
				}
				spell_level = slot_86_0:spellSlot(_Q).level
				spell_Dmg = spell_tb[spell_level] or 0
				totalDmg = spell_Dmg + common.GetTotalAP(slot_86_0) * 0.4
				returndmg = common.CalculateMagicDamage(arg_86_0, totalDmg, slot_86_0) - common.jshfhp(arg_86_0, 1)

				return spell.get_damage(returndmg, arg_86_0, slot_86_0, 1)
			end
		},
		W = {
			ignore_obj_radius = 0,
			target_selector = 8,
			prediction = {
				hitchance = 0,
				range = 650,
				delay = 0.2419,
				new_path = 0.5,
				type = "InRange",
				out_range = 800
			},
			cast_spell = {
				type = "obj",
				slot = _W,
				arg1 = function(arg_87_0)
					-- function 87
					return arg_87_0.ts.obj
				end
			},
			slot = player:spellSlot(_W),
			damage = function(arg_88_0, arg_88_1)
				-- function 88
				return 0
			end
		},
		E = {
			ignore_obj_radius = 0,
			target_selector = 8,
			prediction = {
				hitchance = 0,
				range = 650,
				delay = 0,
				new_path = 0.5,
				type = "InRange",
				out_range = 650
			},
			cast_spell = {
				type = "obj",
				slot = _E,
				arg1 = function(arg_89_0)
					-- function 89
					return arg_89_0.ts.obj
				end
			},
			slot = player:spellSlot(_E),
			damage = function(arg_90_0, arg_90_1)
				-- function 90
				if not arg_90_0 then
					return 0
				end

				local slot_90_0 = arg_90_1 or player

				spell_tb = {
					80,
					120,
					160,
					200,
					240
				}
				spell_level = slot_90_0:spellSlot(_E).level
				spell_Dmg = spell_tb[spell_level] or 0
				totalDmg = spell_Dmg + common.GetTotalAP(slot_90_0) * 0.4
				returndmg = common.CalculateMagicDamage(arg_90_0, totalDmg, slot_90_0) - common.jshfhp(arg_90_0, 0.5)

				return spell.get_damage(returndmg, arg_90_0, slot_90_0, 1)
			end
		},
		R = {
			ignore_obj_radius = 99999,
			target_selector = 8,
			prediction = {
				delay = 0,
				range = 900,
				type = "InRange"
			},
			cast_spell = {
				type = "obj",
				slot = _R,
				arg1 = function(arg_91_0)
					-- function 91
					return arg_91_0.ts.obj
				end
			},
			slot = player:spellSlot(_R),
			damage = function(arg_92_0, arg_92_1)
				-- function 92
				return 0
			end
		}
	}
	spell.Syndra = {
		Q = {
			ignore_obj_radius = 1450,
			target_selector = 8,
			prediction = {
				hitchance = 0,
				radius = 50,
				new_path = 0.5,
				boundingRadiusMod = 0,
				delay = 0.6,
				range = 790,
				type = "Circular",
				out_range = 1099,
				speed = math.huge
			},
			cast_spell = {},
			slot = player:spellSlot(_Q),
			damage = function(arg_134_0, arg_134_1)
				-- function 134
				if not arg_134_0 then
					return 0
				end

				local slot_134_0 = arg_134_1 or player
				local slot_134_1 = (({
					75,
					110,
					145,
					180,
					215
				})[slot_134_0:spellSlot(0).level] or 0) + common.GetTotalAP(slot_134_0) * 0.7
				local slot_134_2 = common.CalculateMagicDamage(arg_134_0, slot_134_1, slot_134_0)

				return spell.get_damage(slot_134_2, arg_134_0, slot_134_0, 1)
			end
		},
		W = {
			ignore_obj_radius = 0,
			target_selector = 8,
			prediction = {
				delay = 0.45,
				range = 925,
				radius = 50,
				boundingRadiusMod = 0,
				type = "Circular",
				speed = math.huge
			},
			slot = player:spellSlot(_W),
			damage = function(arg_135_0, arg_135_1)
				-- function 135
				if not arg_135_0 then
					return 0
				end

				local slot_135_0 = arg_135_1 or player
				local slot_135_1 = {
					70,
					105,
					140,
					175,
					210
				}
				local slot_135_2 = 0
				local slot_135_3 = slot_135_0.buff.syndrapassivestacks

				if slot_135_3 then
					slot_135_2 = slot_135_3.stacks2
				end

				local slot_135_4 = (slot_135_1[slot_135_0:spellSlot(1).level] or 0) + common.GetTotalAP(slot_135_0) * 0.65
				local slot_135_5 = common.CalculateMagicDamage(arg_135_0, slot_135_4, slot_135_0)
				local slot_135_6 = 0

				if slot_135_2 >= 60 then
					slot_135_6 = slot_135_5 * (0.12 + common.GetTotalAP(slot_135_0) * 0.0002)
				end

				return spell.get_damage(slot_135_5, arg_135_0, slot_135_0, 1) + slot_135_6
			end
		},
		W1 = {
			ignore_obj_radius = 0,
			target_selector = 8,
			prediction = {
				delay = 0.25,
				range = 925,
				radius = 50,
				boundingRadiusMod = 0,
				type = "Circular",
				speed = 1450
			},
			slot = player:spellSlot(_W),
			damage = function(arg_136_0, arg_136_1)
				-- function 136
				if not arg_136_0 then
					return 0
				end

				local slot_136_0 = arg_136_1 or player
				local slot_136_1 = (({
					70,
					110,
					150,
					190,
					230
				})[slot_136_0:spellSlot(1).level] or 0) + common.GetTotalAP(slot_136_0) * 0.7
				local slot_136_2 = common.CalculateMagicDamage(arg_136_0, slot_136_1, slot_136_0)

				return spell.get_damage(slot_136_2, arg_136_0, slot_136_0, 1)
			end
		},
		E = {
			ignore_obj_radius = 0,
			target_selector = 8,
			prediction = {
				hitchance = 0,
				new_path = 0.5,
				boundingRadiusMod = 0,
				width = 60,
				speed = 4000,
				delay = 0,
				range = 9999,
				type = "Linear",
				out_range = 9999,
				collision = {
					wall = false,
					minion = false,
					hero = true
				}
			},
			cast_spell = {},
			slot = player:spellSlot(_E),
			damage = function(arg_137_0, arg_137_1)
				-- function 137
				if not arg_137_0 then
					return 0
				end

				local slot_137_0 = arg_137_1 or player
				local slot_137_1 = (({
					55,
					65,
					75,
					85,
					95
				})[slot_137_0:spellSlot(2).level] or 0) + common.GetBonusAD(slot_137_0) * 0.6
				local slot_137_2 = common.CalculatePhysicalDamage(arg_137_0, slot_137_1, slot_137_0)

				return spell.get_damage(slot_137_2, arg_137_0, slot_137_0, 1)
			end
		},
		R = {
			ignore_obj_radius = 1060,
			target_selector = 8,
			prediction = {
				dashRadius = 0,
				width = 75,
				speed = 1100,
				delay = 0,
				range = 675,
				boundingRadiusModTarget = 0,
				boundingRadiusModSource = 0,
				type = "InRange",
				radius = 675,
				collision = {
					wall = true
				}
			},
			cast_spell = {},
			slot = player:spellSlot(_R),
			damage = function(arg_138_0, arg_138_1)
				-- function 138
				if not arg_138_0 then
					return 0
				end

				local slot_138_0 = arg_138_1 or player
				local slot_138_1 = 0
				local slot_138_2 = slot_138_0.buff.syndrapassivestacks

				if slot_138_2 then
					slot_138_1 = slot_138_2.stacks2
				end

				local slot_138_3 = {
					100,
					140,
					180
				}
				local slot_138_4 = slot_138_0:spellSlot(3).level
				local slot_138_5 = common.GetTotalAP(slot_138_0) * 0.17
				local slot_138_6 = (slot_138_3[slot_138_4] or 0) + slot_138_5
				local slot_138_7 = common.CalculateMagicDamage(arg_138_0, slot_138_6, slot_138_0)
				local slot_138_8 = spell.get_damage(slot_138_7, arg_138_0, slot_138_0, 1)

				if slot_138_1 >= 100 and arg_138_0.health - slot_138_8 * 3 < arg_138_0.maxHealth * 0.15 then
					return 99999
				end

				return slot_138_8 * 3
			end
		}
	}
	spell.XinZhao = {
		Q = {
			ignore_obj_radius = 850,
			target_selector = 8,
			prediction = {
				hitchance = 0,
				new_path = 0.5,
				boundingRadiusMod = 1,
				width = 120,
				speed = 1500,
				delay = 0.25,
				range = 850,
				type = "Linear",
				out_range = 850,
				collision = {
					wall = false,
					minion = true,
					hero = true
				}
			},
			cast_spell = {},
			slot = player:spellSlot(_Q),
			damage = function(arg_143_0, arg_143_1)
				-- function 143
				if not arg_143_0 then
					return 0
				end

				local slot_143_0 = arg_143_1 or player
				local slot_143_1 = (({
					5,
					25,
					45,
					65,
					85
				})[slot_143_0:spellSlot(0).level] or 0) + common.GetTotalAD(slot_143_0) * 0.8
				local slot_143_2 = common.CalculatePhysicalDamage(arg_143_0, slot_143_1, slot_143_0)

				return spell.get_damage(slot_143_2, arg_143_0, slot_143_0, 2)
			end
		},
		W = {
			ignore_obj_radius = 0,
			target_selector = 8,
			prediction = {
				hitchance = 0,
				new_path = 0.5,
				boundingRadiusMod = 1,
				width = 30,
				speed = 6250,
				delay = 0.5,
				range = 940,
				type = "Linear",
				out_range = 940,
				collision = {
					wall = false,
					minion = true,
					hero = true
				}
			},
			slot = player:spellSlot(_W),
			damage = function(arg_144_0, arg_144_1)
				-- function 144
				if not arg_144_0 then
					return 0
				end

				return 0
			end
		},
		E = {
			ignore_obj_radius = 0,
			target_selector = 8,
			prediction = {
				hitchance = 0,
				new_path = 0.5,
				boundingRadiusMod = 0,
				width = 60,
				speed = 2500,
				delay = 0,
				range = 800,
				type = "Linear",
				out_range = 800,
				collision = {
					wall = false,
					minion = false,
					hero = true
				}
			},
			cast_spell = {},
			slot = player:spellSlot(_E),
			damage = function(arg_145_0, arg_145_1)
				-- function 145
				if not arg_145_0 then
					return 0
				end

				local slot_145_0 = arg_145_1 or player
				local slot_145_1 = (({
					30,
					55,
					80,
					105,
					130
				})[slot_145_0:spellSlot(2).level] or 0) + common.GetBonusAD(slot_145_0) * 0.1
				local slot_145_2 = common.CalculatePhysicalDamage(arg_145_0, slot_145_1, slot_145_0)

				return spell.get_damage(slot_145_2, arg_145_0, slot_145_0, 2)
			end
		},
		R = {
			ignore_obj_radius = 1060,
			target_selector = 8,
			prediction = {
				hitchance = 0,
				new_path = 0.5,
				boundingRadiusMod = 1,
				width = 60,
				speed = 3200,
				delay = 2.5,
				range = 2500,
				type = "Linear",
				out_range = 2500,
				collision = {
					wall = true,
					minion = true,
					hero = true
				}
			},
			cast_spell = {},
			slot = player:spellSlot(_R),
			damage = function(arg_146_0, arg_146_1)
				-- function 146
				if not arg_146_0 then
					return 0
				end

				local slot_146_0 = arg_146_1 or player
				local slot_146_1 = {
					20,
					25,
					30
				}
				local slot_146_2 = slot_146_0:spellSlot(3).level
				local slot_146_3 = common.GetTotalAD(slot_146_0) * 0.1
				local slot_146_4 = (slot_146_1[slot_146_2] or 0) + slot_146_3
				local slot_146_5 = common.CalculatePhysicalDamage(arg_146_0, slot_146_4, slot_146_0)

				return spell.get_damage(slot_146_5, arg_146_0, slot_146_0, 2)
			end
		}
	}
	spell.Varus = {
		Q = {
			prediction = {
				delay = 0.528,
				range = 1595,
				type = "Linear",
				boundingRadiusMod = 0,
				width = 70,
				speed = 1900,
				collision = {
					wall = true,
					minion = false,
					hero = false
				}
			},
			prediction2 = {
				delay = 0,
				range = 1595,
				type = "Linear",
				boundingRadiusMod = 1,
				width = 70,
				speed = 1900,
				collision = {
					wall = true,
					minion = false,
					hero = false
				}
			},
			slot = player:spellSlot(_Q),
			damage = function(arg_253_0, arg_253_1)
				-- function 253
				if not arg_253_0 then
					return 0
				end

				local slot_253_0 = arg_253_1 or player
				local slot_253_1 = {
					0.833,
					0.866,
					0.9,
					0.933,
					0.966
				}
				local slot_253_2 = common.GetTotalAD(slot_253_0)
				local slot_253_3 = {
					10,
					46.67,
					83.33,
					120,
					156.67
				}
				local slot_253_4 = slot_253_0:spellSlot(_Q).level
				local slot_253_5 = (slot_253_3[slot_253_4] or 0) + slot_253_2 * slot_253_1[slot_253_4]
				local slot_253_6 = common.CAD(arg_253_0, slot_253_5, slot_253_0)

				return spell.get_damage(slot_253_6, arg_253_0, slot_253_0, 2)
			end
		},
		W = {
			ignore_obj_radius = 0,
			prediction = {
				delay = 0,
				range = 0,
				type = "InRange",
				radius = 0,
				speed = math.huge
			},
			slot = player:spellSlot(_W),
			damage = function(arg_254_0, arg_254_1, arg_254_2)
				-- function 254
				return 0
			end
		},
		E = {
			ignore_obj_radius = 925,
			prediction = {
				delay = 0.7419,
				range = 1000,
				speed = 2000,
				boundingRadiusMod = 0,
				type = "Circular",
				radius = 50
			},
			slot = player:spellSlot(_E),
			damage = function(arg_255_0, arg_255_1)
				-- function 255
				if not arg_255_0 then
					return 0
				end

				local slot_255_0 = arg_255_1 or player
				local slot_255_1 = common.GetBonusAD(slot_255_0)
				local slot_255_2 = (({
					60,
					100,
					140,
					180,
					220
				})[slot_255_0:spellSlot(_E).level] or 0) + slot_255_1 * 1.1
				local slot_255_3 = common.CAD(arg_255_0, slot_255_2, slot_255_0)

				return spell.get_damage(slot_255_3, arg_255_0, slot_255_0, 2)
			end
		},
		R = {
			ignore_obj_radius = 1050,
			prediction = {
				delay = 0.2419,
				range = 1050,
				type = "Linear",
				slow = true,
				boundingRadiusMod = 1,
				width = 60,
				speed = 1500,
				collision = {
					wall = true,
					minion = false,
					hero = false
				}
			},
			slot = player:spellSlot(_R),
			damage = function(arg_256_0, arg_256_1, arg_256_2)
				-- function 256
				if not arg_256_0 then
					return 0
				end

				local slot_256_0 = arg_256_1 or player
				local slot_256_1 = common.GetTotalAP(slot_256_0) * 1
				local slot_256_2 = (({
					150,
					250,
					350
				})[slot_256_0:spellSlot(_R).level] or 0) + slot_256_1
				local slot_256_3 = common.CalculateMagicDamage(arg_256_0, slot_256_2, slot_256_0)

				return spell.get_damage(slot_256_3, arg_256_0, slot_256_0, 1)
			end
		}
	}
spell.Cassiopeia = {
		Q = {
			ignore_obj_radius = 850,
			target_selector = 8,
			prediction = {
				delay = 0.75,
				range = 850,
				boundingRadiusMod = 0,
				type = "Circular",
				radius = 50,
				speed = math.huge
			},
			cast_spell = {},
			slot = player:spellSlot(_Q),
			damage = function(arg_189_0, arg_189_1)
				-- function 189
				if not arg_189_0 then
					return 0
				end

				local slot_189_0 = arg_189_1 or player
				local slot_189_1 = (({
					75,
					110,
					145,
					180,
					215
				})[slot_189_0:spellSlot(0).level] or 0) + common.GetTotalAP(slot_189_0) * 0.9
				local slot_189_2 = common.CalculateMagicDamage(arg_189_0, slot_189_1, slot_189_0)

				return spell.get_damage(slot_189_2, arg_189_0, slot_189_0, 1)
			end
		},
		W = {
			ignore_obj_radius = 675,
			target_selector = 8,
			prediction = {
				delay = 0.7,
				range = 700,
				speed = 3000,
				boundingRadiusMod = 0,
				type = "Circular",
				radius = 50
			},
			cast_spell = {},
			slot = player:spellSlot(_W),
			damage = function(arg_190_0, arg_190_1)
				-- function 190
				if not arg_190_0 then
					return 0
				end

				local slot_190_0 = arg_190_1 or player
				local slot_190_1 = (({
					20,
					25,
					30,
					35,
					40
				})[slot_190_0:spellSlot(1).level] or 0) + common.GetTotalAP(slot_190_0) * 0.15
				local slot_190_2 = common.CalculateMagicDamage(arg_190_0, slot_190_1, slot_190_0)

				return spell.get_damage(slot_190_2, arg_190_0, slot_190_0, 1)
			end
		},
		E = {
			ignore_obj_radius = 650,
			target_selector = 8,
			prediction = {
				delay = 0.125,
				range = 700,
				speed = 2500,
				dashRadius = 0,
				boundingRadiusModTarget = 0,
				boundingRadiusModSource = 0,
				type = "InRange",
				radius = 700
			},
			cast_spell = {},
			slot = player:spellSlot(_E),
			damage = function(arg_191_0, arg_191_1)
				-- function 191
				if not arg_191_0 then
					return 0
				end

				local slot_191_0 = arg_191_1 or player
				local slot_191_1 = slot_191_0.level
				local slot_191_2 = slot_191_0:spellSlot(2).level

				if slot_191_1 > 18 then
					slot_191_1 = 18
				end

				local slot_191_3 = 48 + 4 * slot_191_1 + common.GetTotalAP(slot_191_0) * 0.1
				local slot_191_4 = 0

				function is_zd(arg_192_0)
					-- function 192
					if arg_192_0.buff.poisontrailtarget or arg_192_0.buff.twitchdeadlyvenom or arg_192_0.buff.cassiopeiawpoison or arg_192_0.buff.cassiopeiaqdebuff or arg_192_0.buff.toxicshotparticle or arg_192_0.buff.bantamtraptarget then
						return true
					end

					return false
				end

				if is_zd(arg_191_0) then
					local slot_191_5 = (({
						20,
						40,
						60,
						80,
						100
					})[slot_191_2] or 0) + common.GetTotalAP(slot_191_0) * 0.6

					slot_191_4 = common.CalculateMagicDamage(arg_191_0, slot_191_5, slot_191_0)
				end

				local slot_191_6 = common.CalculateMagicDamage(arg_191_0, slot_191_3, slot_191_0) + slot_191_4

				return spell.get_damage(slot_191_6, arg_191_0, slot_191_0, 1)
			end
		},
		R = {
			ignore_obj_radius = 2000,
			target_selector = 8,
			prediction = {
				delay = 0.5,
				range = 850,
				type = "Linear",
				boundingRadiusMod = 0,
				width = 40,
				speed = math.huge,
				collision = {
					wall = true,
					minion = true,
					hero = true
				}
			},
			cast_spell = {},
			slot = player:spellSlot(_R),
			damage = function(arg_193_0, arg_193_1)
				-- function 193
				if not arg_193_0 then
					return 0
				end

				local slot_193_0 = arg_193_1 or player
				local slot_193_1 = (({
					150,
					250,
					350
				})[slot_193_0:spellSlot(3).level] or 0) + common.GetTotalAP(slot_193_0) * 0.5
				local slot_193_2 = common.CalculateMagicDamage(arg_193_0, slot_193_1, slot_193_0)

				return spell.get_damage(slot_193_2, arg_193_0, slot_193_0, 1)
			end
		}
	}
return spell
local RekSaiPlugin = {}
local ts = module.internal("TS")
local orb = module.internal("orb")
local preds = module.internal("pred")
local DelayAction = module.load("<PERSON>", "Core/DelayAction")
local DelayTick = module.load("<PERSON>", "Core/DelayTick")
local Prediction = module.load("<PERSON>", "Core/Prediction")
local BuffManager = module.load("<PERSON>", "Library/BuffManager")
local CalculateManager = module.load("<PERSON>", "Library/CalculateManager")
--local FarmManager = module.load("<PERSON>", "Library/FarmManager")
local ItemManager = module.load("<PERSON>", "Library/ItemManager")
local NetManager = module.load("<PERSON>", "Library/NetManager")
local ObjectManager = module.load("<PERSON>", "Library/ObjectManager")
local OrbManager = module.load("Brian", "Library/OrbManager")
local SpellManager = module.load("<PERSON>", "Library/SpellManager")
local VectorManager = module.load("<PERSON>", "Library/VectorManager")
local MyCommon = module.load("<PERSON>", "Library/ExtraManager")
local common = module.load("<PERSON>", "Utility/common")
--local ui = module.load("Brian", "ui");

local enemies = common.GetEnemyHeroes()
local UG = false
local QAA = false
local minionmanager = objManager.minions

local QlvlDmg = {20, 25, 30, 35, 40}
local Q2lvlDmg = {60, 90, 120, 150, 180}
local ElvlDmg = {55, 65, 75, 85, 95}
local RlvlDmg = {100, 250, 400}
local RHPDmg = {20, 25, 30}
local qPred = { delay = 0.5, width = 60, speed = 1950, boundingRadiusMod = 1, collision = { hero = true, minion = true } }

local MyMenu

function RekSaiPlugin.Load(GlobalMenu)
    MyMenu = GlobalMenu

		MyMenu.Key:keybind("run", "Flee", "Z", false)
	MyMenu:menu("combo", "Combo Settings")
		MyMenu.combo:header("xd", "Combo Settings")
		MyMenu.combo:header("xd", "Q Settings")
		MyMenu.combo:boolean("q", "Use Q", true)
		MyMenu.combo:dropdown("modeq", "Choose Mode: ", 2, {"Before AA", "After AA"})

		MyMenu.combo:header("xd", "E Settings")
		MyMenu.combo:boolean("e", "Use E", true)
		MyMenu.combo:dropdown("modee", "Choose Mode: ", 1, {"After Q", "During Q", "Rage on 100"})

		MyMenu.combo:header("xd", "R Settings")
		MyMenu.combo:boolean("r", "Use R", false)
		MyMenu.combo:slider("rx", "Max. Enemys in Range", 2, 0, 5, 1)
		MyMenu.combo:menu("x", "Enemy Selection")
			for i, enemy in ipairs(enemies) do
				MyMenu.combo.x:boolean(enemy.charName, "Cast R on: "..enemy.charName, true) 
			end

	MyMenu:menu("jg", "Jungle Clear Settings")
		MyMenu.jg:header("xd", "Jungle Settings")
		MyMenu.jg:boolean("q", "Use Q", true)
		MyMenu.jg:boolean("e", "Use E", true)

	MyMenu:menu("auto", "Killsteal Settings")
		MyMenu.auto:header("xd", "KillSteal Settings")
		MyMenu.auto:boolean("uks", "Use Killsteal", true)
		MyMenu.auto:boolean("ksq", "Use Q in Killsteal", true)
		MyMenu.auto:boolean("kse", "Use E in Killsteal", true)
		MyMenu.auto:boolean("ksr", "Use R in Killsteal", true)

	MyMenu:menu("draws", "Draw Settings")
		MyMenu.draws:header("xd", "Drawing Options")
        MyMenu.draws:boolean("q", "Draw Q Range", true)
        MyMenu.draws:color("colorq", "^ color", 255, 233, 121, 121)
        MyMenu.draws:boolean("e", "Draw R Range", true)
        MyMenu.draws:color("colore", "^ color", 255, 233, 121, 121)
end

local function select_target(res, obj, dist)
	if dist > 325 then return end
	res.obj = obj
	return true
end

local function select_Qtarget(res, obj, dist)
	if dist > 1450 then return end
	res.obj = obj
	return true
end

local function get_target(func)
	return ts.get_result(func).obj
end



local function Buff()
	if player.buff["reksaiw"] then
		UG = true
	else
		UG = false
	end
	if player.buff["reksaiq"] then
		QAA = true
	else
		QAA = false
	end
end

local function qDmg(target)
  local damage = Q2lvlDmg[player:spellSlot(0).level] + (common.GetTotalAP() * .7)
  local damage2 = (common.GetBonusAD() * 0.4)
  	return common.CalculatePhysicalDamage(target, damage2) + common.CalculateMagicDamage(target, damage)
end

local function rDmg(target)
    local damage = (RlvlDmg[player:spellSlot(3).level] + (common.GetBonusAD() * 1.85) ) + ((RHPDmg[player:spellSlot(3).level]/100) * (target.maxHealth - target.health))
    --print(((RHPDmg[player:spellSlot(3).level]/100) * (target.maxHealth - target.health)))
    return common.CalculatePhysicalDamage(target, damage)
end

local function eDmg(target)
	if player.mana == 100 then
		local damage = (ElvlDmg[player:spellSlot(2).level] + (common.GetBonusAD() * 0.85))*2
	    return common.CalculatePhysicalDamage(target, damage)
	else
		local damage = ElvlDmg[player:spellSlot(2).level] + (common.GetBonusAD() * 0.85)
    	return common.CalculatePhysicalDamage(target, damage)
    end
end

local function Combo()
	if MyMenu.combo.r:get() and player:spellSlot(3).state == 0 then
		for i = 0, objManager.enemies_n - 1 do
			local enemy = objManager.enemies[i]
			if enemy and common.IsValidTarget(enemy) and MyMenu.combo.x[enemy.charName]:get() and enemy.pos:dist(player.pos) < 1500 and #common.GetEnemyHeroesInRange(700) <= MyMenu.combo.rx:get() and enemy.buff["reksairprey"] and enemy.health < rDmg(enemy) then
				player:castSpell("obj", 3, enemy)
			end
		end
	end
	if not UG then
		local target = get_target(select_target)
		if target and common.IsValidTarget(target) then
			if MyMenu.combo.q:get() and player:spellSlot(0).state == 0 and player.path.serverPos:dist(target.path.serverPos) <= player.attackRange + player.boundingRadius then
				if player:spellSlot(0).name == "RekSaiQ" then
					if MyMenu.combo.modeq:get() == 1 then
						player:castSpell("self", 0)
					end
				end
			end
			if MyMenu.combo.e:get() and player:spellSlot(2).state == 0 and player.path.serverPos:dist(target.path.serverPos) <= 315 then
				if MyMenu.combo.modee:get() == 1 and not QAA and player:spellSlot(0).state ~= 0 then
					player:castSpell("obj", 2, target)
				elseif MyMenu.combo.modee:get() == 2 and QAA then
					player:castSpell("obj", 2, target)
				elseif MyMenu.combo.modee:get() == 3 and player.mana == 100 then
					player:castSpell("obj", 2, target)
				end
			end
			if player:spellSlot(0).state ~= 0 and player:spellSlot(2).state ~= 0 and player.path.serverPos:dist(target.path.serverPos) <= 250 and player:spellSlot(1).state == 0 and not QAA then
				player:castSpell("self", 1)
			end
		end
	end
	if UG then
		local target = get_target(select_Qtarget)
		if target and common.IsValidTarget(target) then
			if MyMenu.combo.q:get() and player:spellSlot(0).state == 0 and player:spellSlot(0).name == "RekSaiQBurrowed" then
				if player.path.serverPos:dist(target.path.serverPos) < 1450 then
					local seg = gpred.linear.get_prediction(qPred, target)
					if seg and seg.startPos:dist(seg.endPos) < 1450 then
						if not gpred.collision.get_prediction(qPred, seg, target) then
							player:castSpell("pos", 0, vec3(seg.endPos.x, game.mousePos.y, seg.endPos.y))
						end
					end
				end
			end
		end
	end
end


local function KillSteal()
	for i = 0, objManager.enemies_n - 1 do
		local enemy = objManager.enemies[i]
 		if enemy and common.IsValidTarget(enemy) and not enemy.buff["sionpassivezombie"] then
  			if MyMenu.auto.ksq:get() and player:spellSlot(0).state == 0 and UG and enemy.health < qDmg(enemy) and player.path.serverPos:dist(enemy.path.serverPos) < 1450 and player:spellSlot(0).name == "RekSaiQBurrowed" then
	  			local seg = gpred.linear.get_prediction(qPred, enemy)
				if seg and seg.startPos:dist(seg.endPos) < 1450 then
					if not gpred.collision.get_prediction(qPred, seg, enemy) then
						player:castSpell("pos", 0, vec3(seg.endPos.x, game.mousePos.y, seg.endPos.y))
					end
				end
	  		end
	  		if MyMenu.auto.kse:get() and player:spellSlot(2).state == 0 and enemy.health < eDmg(target) and player.path.serverPos:dist(enemy.path.serverPos) < 315 then
	  			player:castSpell("obj", 2, enemy)
	  		end
   			if MyMenu.auto.ksr:get() and player:spellSlot(3).state == 0 and enemy.health < rDmg(enemy) and player.path.serverPos:dist(enemy.path.serverPos) < 1500 then
   				if enemy.buff["reksairprey"] then
   					player:castSpell("obj", 3, enemy)
   				elseif not enemy.buff["reksairprey"] and player:spellSlot(0).state == 0 and UG and player:spellSlot(0).name == "RekSaiQBurrowed" and player.path.serverPos:dist(enemy.path.serverPos) < 1450 then
   					local seg = gpred.linear.get_prediction(qPred, enemy)
					if seg and seg.startPos:dist(seg.endPos) < 1450 then
						if not gpred.collision.get_prediction(qPred, seg, enemy) then
							player:castSpell("pos", 0, vec3(seg.endPos.x, game.mousePos.y, seg.endPos.y))
						end
					end
				elseif not UG and player:spellSlot(0).name == "RekSaiQ" and player:spellSlot(1).state == 0 then
					player:castSpell("self", 1)
				end   				
   			end
  		end
 	end
end

local function Clear()
	local target = { obj = nil, health = 0, mode = "jungleclear" }
	local aaRange = player.attackRange + player.boundingRadius + 200
	for i = 0, minionmanager.size[TEAM_NEUTRAL] - 1 do
		local obj = minionmanager[TEAM_NEUTRAL][i]
		if player.pos:dist(obj.pos) <= aaRange and obj.maxHealth > target.health then
			target.obj = obj
			target.health = obj.maxHealth
		end
	end
	if target.obj then
		if target.mode == "jungleclear" then
			if not UG then
				if MyMenu.jg.q:get() and player:spellSlot(0).state == 0 and player.path.serverPos:dist(target.obj.path.serverPos) <= player.attackRange + player.boundingRadius and player:spellSlot(0).name == "RekSaiQ" then
					player:castSpell("self", 0)
				end
				if MyMenu.jg.e:get() and player:spellSlot(2).state == 0 and player.path.serverPos:dist(target.obj.path.serverPos) < 315 then
					if not QAA and player:spellSlot(0).state ~= 0 and player.mana == 100 then
						player:castSpell("obj", 2, target.obj)
					end
				end
				if player:spellSlot(0).state ~= 0 and player:spellSlot(2).state ~= 0 and player.path.serverPos:dist(target.obj.path.serverPos) <= 250 and player:spellSlot(1).state == 0 and not QAA then
					player:castSpell("self", 1)
				end
			end
			if UG then
				if MyMenu.jg.q:get() and player:spellSlot(0).state == 0 and player:spellSlot(0).name == "RekSaiQBurrowed" then
					if player.path.serverPos:dist(target.obj.path.serverPos) < 1450 then
						local seg = gpred.linear.get_prediction(qPred, target.obj)
						if seg and seg.startPos:dist(seg.endPos) < 1450 then
							if not gpred.collision.get_prediction(qPred, seg, target.obj) then
								player:castSpell("pos", 0, vec3(seg.endPos.x, game.mousePos.y, seg.endPos.y))
							end
						end
					end
				end
			end
		end
	end
end

local function Run()
	if MyMenu.Key.run:get() then
		player:move(vec3(mousePos.x, mousePos.y, mousePos.z))
		if player:spellSlot(2).state == 0 then
			if UG then
				player:castSpell("pos", 2, vec3(mousePos.x, mousePos.y, mousePos.z))
			else
				player:castSpell("self", 1)
			end
		end
	end
end


local function OnTick()
	if MyMenu.Key.Combo:get() then Combo() end
	if MyMenu.Key.LaneClear:get() then Clear() end
	if MyMenu.auto.uks:get() then KillSteal() end
	if MyMenu.Key.run:get() then Run() end
	Buff()
end


local function OnDraw()
	if MyMenu.draws.q:get() and player:spellSlot(0).state == 0 and player.isOnScreen then
		if player:spellSlot(0).name == "RekSaiQ" then
			graphics.draw_circle(player.pos, 320, 2, MyMenu.draws.colorq:get(), 70)
		elseif player:spellSlot(0).name == "RekSaiQBurrowed" then
			graphics.draw_circle(player.pos, 1450, 2, MyMenu.draws.colorq:get(), 70)
		end
	end
	if MyMenu.draws.e:get() and player:spellSlot(3).state == 0 and player.isOnScreen then
		graphics.draw_circle(player.pos, (1500), 2, MyMenu.draws.colore:get(), 70)
	end
end

orb.combat.register_f_pre_tick(OnTick)
cb.add(cb.draw, OnDraw)
orb.combat.register_f_after_attack(
	function()
		if orb.combat.is_active() then
			if orb.combat.target then
				if MyMenu.combo.q:get() and MyMenu.combo.modeq:get() == 2 and orb.combat.target and common.IsValidTarget(orb.combat.target) and player.pos:dist(orb.combat.target.pos) < common.GetAARange(orb.combat.target) then
					if player:spellSlot(0).state == 0 then
						player:castSpell("self", 0)
						orb.core.set_server_pause()
						orb.combat.set_invoke_after_attack(false)
						player:attack(orb.combat.target)
						orb.core.set_server_pause()
						orb.combat.set_invoke_after_attack(false)
						return "on_after_attack_hydra"
					end
				end
			end
		end
	end
)

return RekSaiPlugin
local var_0_0 = module.internal("pred")
local var_0_1 = module.internal("TS")
local var_0_2 = module.internal("orb")
local var_0_3 = module.load("<PERSON>", "Utility/common20")
local var_0_4 = {
	speed = 2000,
	range = 1050,
	delay = 0.25,
	boundingRadiusMod = 1,
	width = 80,
	collision = {
		minion = true,
		wall = true
	}
}
local var_0_5 = {
	range = 300
}
local var_0_6 = {
	delay = 0.25,
	range = 350
}
local var_0_7 = {
	delay = 0.25,
	range = 550
}
local var_0_8
local var_0_9 = {
	390,
	410,
	430,
	450,
	480,
	510,
	540,
	570,
	600,
	640,
	680,
	720,
	760,
	800,
	850,
	900,
	950,
	1000
}

if player:spellSlot(4).name:find("Smite") then
	var_0_8 = 4
end

if player:spellSlot(5).name:find("Smite") then
	var_0_8 = 5
end

local var_0_10 = menu("KornisAIO2" .. player.charName, "<PERSON><PERSON>is AIO - " .. player.charName)

var_0_10:menu("combo", "Combo")
var_0_10.combo:header("qset", " ~~ Q Settings ~~")
var_0_10.combo:boolean("qcombo", "Use Q", true)
var_0_10.combo:slider("minq", " ^- Min. Q Range", 200, 0, 500, 10)
var_0_10.combo:boolean("smiteq", "Use Smite + Q", true)
var_0_10.combo:header("wset", " ~~ W Settings ~~")
var_0_10.combo:boolean("wcombo", "Use W", true)
var_0_10.combo:header("eset", " ~~ E Settings ~~")
var_0_10.combo:boolean("ecombo", "Use E", true)
var_0_10.combo:header("rset", " ~~ R Settings ~~")
var_0_10.combo:boolean("rcombo", "Use R if Killable with Combo", true)
var_0_10.combo:slider("forcer", "Force R if Hits X", 2, 1, 5, 1)
var_0_10:menu("harass", "Harass")
var_0_10.harass:header("qset", " ~~ Q Settings ~~")
var_0_10.harass:boolean("qcombo", "Use Q", true)
var_0_10.harass:slider("minq", " ^- Min. Q Range", 200, 0, 500, 10)
var_0_10.harass:header("wset", " ~~ W Settings ~~")
var_0_10.harass:boolean("wcombo", "Use W", true)
var_0_10.harass:header("eset", " ~~ E Settings ~~")
var_0_10.harass:boolean("ecombo", "Use E", true)
var_0_10:menu("farming", "Farming")
var_0_10.farming:keybind("toggle", "Farm Toggle", nil, "A")
var_0_10.farming:header("uwu", " ~~~~ ")
var_0_10.farming:menu("laneclear", "Lane Clear")
var_0_10.farming.laneclear:boolean("farmw", "Use W", true)
var_0_10.farming.laneclear:boolean("farme", "Use E", true)
var_0_10.farming.laneclear:slider("hitsr", " ^- if Hits X Minions", 3, 1, 6, 1)
var_0_10.farming:menu("jungleclear", "Jungle Clear")
var_0_10.farming.jungleclear:boolean("useq", "Use Q", true)
var_0_10.farming.jungleclear:boolean("usew", "Use W", true)
var_0_10.farming.jungleclear:boolean("usee", "Use E", true)
var_0_10:menu("draws", "Draw Settings")
var_0_10.draws:header("ranges", " ~~ Ranges ~~ ")
var_0_10.draws:boolean("drawq", "Draw Q Range", true)
var_0_10.draws:color("colorq", "  ^- Color", 128, 173, 238, 255)
var_0_10.draws:boolean("draww", "Draw W Range", false)
var_0_10.draws:color("colorw", "  ^- Color", 128, 173, 238, 255)
var_0_10.draws:boolean("drawe", "Draw E Range", false)
var_0_10.draws:color("colore", "  ^- Color", 128, 173, 238, 255)
var_0_10.draws:boolean("drawr", "Draw R Range", true)
var_0_10.draws:color("colorr", "  ^- Color", 255, 102, 178, 255)
var_0_10.draws:header("other", " ~~ Other ~~ ")
var_0_10.draws:boolean("drawdamage", "Draw Damage", true)
var_0_10.draws:slider("transparency", "Damage Drawing Transparency", 155, 0, 255, 1)
var_0_10:header("meow", " ~~~~ ")
var_0_10:boolean("smartw", "Enable Smart W Turn OFF", true)
var_0_1.load_to_menu(var_0_10)

local function var_0_11(arg_1_0, arg_1_1, arg_1_2)
	if var_0_0.trace.linear.hardlock(arg_1_0, arg_1_1, arg_1_2) then
		return true
	end

	if var_0_0.trace.linear.hardlockmove(arg_1_0, arg_1_1, arg_1_2) then
		return true
	end

	if arg_1_2 and var_0_3.IsValidTarget(arg_1_2) and player.pos:dist(arg_1_2) <= 500 then
		return true
	end

	if var_0_0.trace.newpath(arg_1_2, 0.033, 0.5) then
		return true
	end
end

local var_0_12 = {
	80,
	130,
	180,
	230,
	280
}

function QDamage(arg_2_0)
	local var_2_0 = 0

	if player:spellSlot(0).level > 0 then
		var_2_0 = var_0_3.CalculateMagicDamage(arg_2_0, var_0_12[player:spellSlot(0).level] + var_0_3.GetTotalAP() * 0.7, player)
	end

	return var_2_0
end

local var_0_13 = {
	75,
	100,
	125,
	150,
	175
}

function EDamage(arg_3_0)
	local var_3_0 = 0

	if player:spellSlot(2).level > 0 then
		var_3_0 = var_0_3.CalculateMagicDamage(arg_3_0, var_0_13[player:spellSlot(2).level] + var_0_3.GetTotalAP() * 0.5, player)
	end

	return var_3_0
end

local var_0_14 = {
	150,
	250,
	350
}

function RDamage(arg_4_0)
	local var_4_0 = 0

	if player:spellSlot(3).level > 0 then
		var_4_0 = var_0_3.CalculateMagicDamage(arg_4_0, var_0_14[player:spellSlot(3).level] + var_0_3.GetTotalAP() * 0.8, player)
	end

	return var_4_0
end

local function var_0_15(arg_5_0, arg_5_1, arg_5_2)
	if arg_5_2 < var_0_4.range then
		arg_5_0.obj = arg_5_1

		return true
	end
end

local function var_0_16()
	return var_0_1.get_result(var_0_15).obj
end

local function var_0_17(arg_7_0, arg_7_1, arg_7_2)
	if arg_7_2 < var_0_5.range then
		arg_7_0.obj = arg_7_1

		return true
	end
end

local function var_0_18()
	return var_0_1.get_result(var_0_17).obj
end

local function var_0_19(arg_9_0, arg_9_1, arg_9_2)
	if arg_9_2 < var_0_6.range then
		arg_9_0.obj = arg_9_1

		return true
	end
end

local function var_0_20()
	return var_0_1.get_result(var_0_19).obj
end

local function var_0_21(arg_11_0, arg_11_1, arg_11_2)
	if arg_11_2 < var_0_7.range then
		arg_11_0.obj = arg_11_1

		return true
	end
end

local function var_0_22()
	return var_0_1.get_result(var_0_21).obj
end

local function var_0_23(arg_13_0, arg_13_1)
	local var_13_0 = {}

	for iter_13_0 = 0, objManager.enemies_n - 1 do
		local var_13_1 = objManager.enemies[iter_13_0]

		if arg_13_1 > arg_13_0:dist(var_13_1.pos) and var_0_3.IsValidTarget(var_13_1) then
			var_13_0[#var_13_0 + 1] = var_13_1
		end
	end

	return var_13_0
end

local function var_0_24(arg_14_0, arg_14_1)
	local var_14_0 = {}

	for iter_14_0 = 0, objManager.minions.size[TEAM_ENEMY] - 1 do
		local var_14_1 = objManager.minions[TEAM_ENEMY][iter_14_0]

		if arg_14_1 > arg_14_0:dist(var_14_1.pos) and var_0_3.IsValidTarget(var_14_1) then
			var_14_0[#var_14_0 + 1] = var_14_1
		end
	end

	return var_14_0
end

local function var_0_25(arg_15_0, arg_15_1)
	local var_15_0 = {}

	for iter_15_0 = 0, objManager.minions.size[TEAM_NEUTRAL] - 1 do
		local var_15_1 = objManager.minions[TEAM_NEUTRAL][iter_15_0]

		if arg_15_1 > arg_15_0:dist(var_15_1.pos) and var_0_3.IsValidTarget(var_15_1) then
			var_15_0[#var_15_0 + 1] = var_15_1
		end
	end

	return var_15_0
end

function VectorPointProjectionOnLineSegment(arg_16_0, arg_16_1, arg_16_2)
	local var_16_0 = arg_16_2.x
	local var_16_1 = arg_16_2.z or arg_16_2.y
	local var_16_2 = arg_16_0.x
	local var_16_3 = arg_16_0.z or arg_16_0.y
	local var_16_4 = arg_16_1.x
	local var_16_5 = arg_16_1.z or arg_16_1.y
	local var_16_6 = ((var_16_0 - var_16_2) * (var_16_4 - var_16_2) + (var_16_1 - var_16_3) * (var_16_5 - var_16_3)) / ((var_16_4 - var_16_2)^2 + (var_16_5 - var_16_3)^2)
	local var_16_7 = {
		x = var_16_2 + var_16_6 * (var_16_4 - var_16_2),
		y = var_16_3 + var_16_6 * (var_16_5 - var_16_3)
	}
	local var_16_8 = var_16_6 < 0 and 0 or var_16_6 > 1 and 1 or var_16_6
	local var_16_9 = var_16_8 == var_16_6

	return var_16_9 and var_16_7 or {
		x = var_16_2 + var_16_8 * (var_16_4 - var_16_2),
		y = var_16_3 + var_16_8 * (var_16_5 - var_16_3)
	}, var_16_7, var_16_9
end

function GetNMinionsHitE(arg_17_0)
	local var_17_0 = 0
	local var_17_1
	local var_17_2 = vec3(arg_17_0.x, 0, arg_17_0.z)
	local var_17_3 = vec3(player.x, 0, player.z)

	for iter_17_0 = 0, objManager.minions.size[TEAM_ENEMY] - 1 do
		local var_17_4 = objManager.minions[TEAM_ENEMY][iter_17_0]

		if var_17_4 and var_17_4.isVisible and not var_17_4.isDead and var_17_4.moveSpeed > 0 and var_17_4.isTargetable and var_17_4.pos:dist(player.pos) <= 2000 then
			local var_17_5 = vec3(var_17_4.x, 0, var_17_4.z)
			local var_17_6 = VectorPointProjectionOnLineSegment(player.pos - (arg_17_0:dist(player.pos) + 50) * (player.pos - arg_17_0):norm(), var_17_3, var_17_5)

			if vec2(var_17_5.x, var_17_5.z):dist(vec2(var_17_6.x, var_17_6.y)) < 120 + var_17_4.boundingRadius then
				var_17_0 = var_17_0 + 1
				var_17_1 = var_17_4
			end
		end
	end

	return var_17_0, var_17_1
end

local function var_0_26()
	if var_0_10.farming.laneclear.farmw:get() and not player.buff.auraofdespair and player:spellSlot(1).state == 0 then
		for iter_18_0 = 0, objManager.minions.size[TEAM_ENEMY] - 1 do
			local var_18_0 = objManager.minions[TEAM_ENEMY][iter_18_0]

			if var_18_0 and var_18_0.isVisible and var_18_0.moveSpeed > 0 and var_18_0.isTargetable and not var_18_0.isDead and #var_0_24(player.pos, var_0_5.range) > 0 then
				player:castSpell("self", 1)
			end
		end
	end

	if var_0_10.farming.laneclear.farme:get() and player:spellSlot(2).state == 0 then
		for iter_18_1 = 0, objManager.minions.size[TEAM_ENEMY] - 1 do
			local var_18_1 = objManager.minions[TEAM_ENEMY][iter_18_1]

			if var_18_1 and var_18_1.isVisible and var_18_1.moveSpeed > 0 and var_18_1.isTargetable and not var_18_1.isDead and #var_0_24(player.pos, var_0_6.range) >= var_0_10.farming.laneclear.hitsr:get() then
				player:castSpell("self", 2)
			end
		end
	end
end

local function var_0_27()
	if var_0_10.farming.jungleclear.useq:get() and player:spellSlot(0).state == 0 then
		for iter_19_0 = 0, objManager.minions.size[TEAM_NEUTRAL] - 1 do
			local var_19_0 = objManager.minions[TEAM_NEUTRAL][iter_19_0]

			if var_19_0 and not var_19_0.isDead and var_19_0.moveSpeed > 0 and var_19_0.isTargetable and var_19_0.isVisible and var_19_0.type == TYPE_MINION and var_19_0.pos:dist(player.pos) <= var_0_4.range then
				local var_19_1 = var_0_0.linear.get_prediction(var_0_4, var_19_0)

				if var_19_1 and player.pos:dist(vec3(var_19_1.endPos.x, var_19_0.y, var_19_1.endPos.y)) < var_0_4.range then
					player:castSpell("pos", 0, vec3(var_19_1.endPos.x, mousePos.y, var_19_1.endPos.y))
				end
			end
		end
	end

	if var_0_10.farming.jungleclear.usew:get() and not player.buff.auraofdespair and player:spellSlot(1).state == 0 then
		for iter_19_1 = 0, objManager.minions.size[TEAM_NEUTRAL] - 1 do
			local var_19_2 = objManager.minions[TEAM_NEUTRAL][iter_19_1]

			if var_19_2 and var_19_2.isVisible and var_19_2.moveSpeed > 0 and var_19_2.isTargetable and not var_19_2.isDead and #var_0_25(player.pos, var_0_5.range) > 0 then
				player:castSpell("self", 1)
			end
		end
	end

	if var_0_10.farming.jungleclear.usee:get() and player:spellSlot(2).state == 0 then
		for iter_19_2 = 0, objManager.minions.size[TEAM_NEUTRAL] - 1 do
			local var_19_3 = objManager.minions[TEAM_NEUTRAL][iter_19_2]

			if var_19_3 and var_19_3.isVisible and var_19_3.moveSpeed > 0 and var_19_3.isTargetable and not var_19_3.isDead and #var_0_25(player.pos, var_0_6.range) > 0 then
				player:castSpell("self", 2)
			end
		end
	end
end

local function var_0_28()
	if var_0_10.combo.rcombo:get() and player:spellSlot(3).state == 0 then
		local var_20_0 = var_0_22()

		if var_0_3.IsValidTarget(var_20_0) and var_20_0.health < QDamage(var_20_0) + EDamage(var_20_0) + RDamage(var_20_0) then
			local var_20_1 = var_0_0.core.get_pos_after_time(var_20_0, 0.25)

			if var_20_1 and player.pos:dist(vec3(var_20_1.x, var_20_0.y, var_20_1.y)) <= var_0_7.range then
				player:castSpell("self", 3)
			end
		end
	end

	if player:spellSlot(3).state == 0 and #var_0_23(player.pos, var_0_7.range) >= var_0_10.combo.forcer:get() then
		local var_20_2 = var_0_22()

		if var_0_3.IsValidTarget(var_20_2) then
			local var_20_3 = var_0_0.core.get_pos_after_time(var_20_2, 0.25)

			if var_20_3 and player.pos:dist(vec3(var_20_3.x, var_20_2.y, var_20_3.y)) <= var_0_7.range then
				player:castSpell("self", 3)
			end
		end
	end

	if var_0_10.combo.qcombo:get() and player:spellSlot(0).state == 0 then
		local var_20_4 = var_0_16()

		if var_0_3.IsValidTarget(var_20_4) and var_20_4.pos:dist(player.pos) >= var_0_10.combo.minq:get() then
			local var_20_5 = var_0_0.linear.get_prediction(var_0_4, var_20_4)

			if var_20_5 and player.pos:dist(vec3(var_20_5.endPos.x, var_20_4.y, var_20_5.endPos.y)) < var_0_4.range then
				if var_0_10.combo.smiteq:get() then
					local var_20_6, var_20_7 = GetNMinionsHitE(vec3(var_20_5.endPos.x, var_20_4.pos.y, var_20_5.endPos.y))

					if var_0_0.collision.get_prediction(var_0_4, var_20_5, var_20_4) and table.getn(var_0_0.collision.get_prediction(var_0_4, var_20_5, var_20_4)) == 1 and var_20_6 == 1 and var_0_8 and var_0_9[var_0_3.GetLevel(player)] >= var_20_7.health and player:spellSlot(var_0_8).state == 0 then
						player:castSpell("obj", var_0_8, var_20_7)
						player:castSpell("pos", 0, vec3(var_20_5.endPos.x, var_20_4.pos.y, var_20_5.endPos.y))
					end
				end

				if not var_0_0.collision.get_prediction(var_0_4, var_20_5, var_20_4) and var_0_11(var_0_4, var_20_5, var_20_4) then
					player:castSpell("pos", 0, vec3(var_20_5.endPos.x, var_20_4.pos.y, var_20_5.endPos.y))
				end
			end
		end
	end

	if var_0_10.combo.wcombo:get() and player:spellSlot(1).state == 0 and not player.buff.auraofdespair and #var_0_23(player.pos, 300) > 0 then
		player:castSpell("self", 1)
	end

	if var_0_10.combo.ecombo:get() and player:spellSlot(2).state == 0 then
		local var_20_8 = var_0_20()

		if var_0_3.IsValidTarget(var_20_8) then
			local var_20_9 = var_0_0.core.get_pos_after_time(var_20_8, 0.25)

			if var_20_9 and player.pos:dist(vec3(var_20_9.x, var_20_8.y, var_20_9.y)) <= var_0_6.range then
				player:castSpell("self", 2)
			end
		end
	end
end

local function var_0_29()
	if var_0_10.harass.qcombo:get() and player:spellSlot(0).state == 0 then
		local var_21_0 = var_0_16()

		if var_0_3.IsValidTarget(var_21_0) and var_21_0.pos:dist(player.pos) >= var_0_10.harass.minq:get() then
			local var_21_1 = var_0_0.linear.get_prediction(var_0_4, var_21_0)

			if var_21_1 and player.pos:dist(vec3(var_21_1.endPos.x, var_21_0.y, var_21_1.endPos.y)) < var_0_4.range and not var_0_0.collision.get_prediction(var_0_4, var_21_1, var_21_0) and var_0_11(var_0_4, var_21_1, var_21_0) then
				player:castSpell("pos", 0, vec3(var_21_1.endPos.x, var_21_0.pos.y, var_21_1.endPos.y))
			end
		end
	end

	if var_0_10.harass.wcombo:get() and player:spellSlot(0).state == 0 and not player.buff.auraofdespair and #var_0_23(player.pos, 300) > 0 then
		player:castSpell("self", 1)
	end

	if var_0_10.harass.ecombo:get() and player:spellSlot(2).state == 0 then
		local var_21_2 = var_0_20()

		if var_0_3.IsValidTarget(var_21_2) then
			local var_21_3 = var_0_0.core.get_pos_after_time(var_21_2, 0.25)

			if var_21_3 and player.pos:dist(vec3(var_21_3.x, var_21_2.y, var_21_3.y)) <= var_0_6.range then
				player:castSpell("self", 2)
			end
		end
	end
end

local function var_0_30(arg_22_0, arg_22_1)
	local var_22_0 = {}

	for iter_22_0 = 0, objManager.allies_n - 1 do
		local var_22_1 = objManager.allies[iter_22_0]

		if arg_22_1 > arg_22_0:dist(var_22_1.pos) and var_0_3.IsValidTarget(var_22_1) then
			var_22_0[#var_22_0 + 1] = var_22_1
		end
	end

	return var_22_0
end

local function var_0_31()
	if player.isDead then
		return
	end

	if var_0_10.smartw:get() and player:spellSlot(1).state == 0 and player.buff.auraofdespair and #var_0_23(player.pos, 400) == 0 and #var_0_24(player.pos, 400) == 0 and #var_0_25(player.pos, 400) == 0 then
		player:castSpell("self", 1)
	end

	if var_0_2.menu.combat.key:get() then
		var_0_28()
	end

	if var_0_2.menu.hybrid.key:get() then
		var_0_29()
	end

	if var_0_2.menu.lane_clear.key:get() and var_0_10.farming.toggle:get() then
		var_0_26()
		var_0_27()
	end
end

local function var_0_32()
	if player.isDead then
		return
	end

	if player.isOnScreen then
		if var_0_10.draws.drawe:get() then
			graphics.draw_circle(player.pos, var_0_6.range, 3, var_0_10.draws.colore:get(), 100)
		end

		if var_0_10.draws.drawq:get() then
			graphics.draw_circle(player.pos, var_0_4.range, 3, var_0_10.draws.colorq:get(), 100)
		end

		if var_0_10.draws.draww:get() then
			graphics.draw_circle(player.pos, var_0_5.range, 3, var_0_10.draws.colorw:get(), 100)
		end

		if var_0_10.draws.drawr:get() then
			graphics.draw_circle(player.pos, var_0_7.range, 3, var_0_10.draws.colorr:get(), 100)
		end

		local var_24_0 = graphics.world_to_screen(vec3(player.x, player.y, player.z))

		graphics.draw_text_2D("Farm: ", 15, var_24_0.x - 50, var_24_0.y + 20, graphics.argb(255, 255, 255, 255))

		if var_0_10.farming.toggle:get() then
			graphics.draw_text_2D("ON", 15, var_24_0.x - 10, var_24_0.y + 20, graphics.argb(255, 128, 255, 0))
		else
			graphics.draw_text_2D("OFF", 15, var_24_0.x - 10, var_24_0.y + 20, graphics.argb(255, 218, 34, 34))
		end
	end

	if var_0_10.draws.drawdamage:get() then
		for iter_24_0 = 0, objManager.enemies_n - 1 do
			local var_24_1 = objManager.enemies[iter_24_0]

			if var_24_1 and var_24_1.isVisible and var_24_1.team == TEAM_ENEMY and var_24_1.isOnScreen then
				local var_24_2 = var_24_1.barPos
				local var_24_3 = var_24_2.x + 164
				local var_24_4 = var_24_2.y + 122.5
				local var_24_5 = player:spellSlot(0).state == 0 and QDamage(var_24_1) or 0
				local var_24_6 = player:spellSlot(2).state == 0 and EDamage(var_24_1) or 0
				local var_24_7 = player:spellSlot(3).state == 0 and RDamage(var_24_1) or 0
				local var_24_8 = var_24_1.health - (var_24_5 + var_24_7 + var_24_6)
				local var_24_9 = var_24_3 + var_24_1.health / var_24_1.maxHealth * 102
				local var_24_10 = var_24_3 + (var_24_8 > 0 and var_24_8 or 0) / var_24_1.maxHealth * 102

				if var_24_8 > 0 then
					graphics.draw_line_2D(var_24_9, var_24_4, var_24_10, var_24_4, 10, graphics.argb(var_0_10.draws.transparency:get(), 255, 192, 200))
				else
					graphics.draw_line_2D(var_24_9, var_24_4, var_24_10, var_24_4, 10, graphics.argb(var_0_10.draws.transparency:get(), 0, 255, 0))
				end
			end
		end
	end
end

cb.add(cb.tick, var_0_31)
cb.add(cb.draw, var_0_32)

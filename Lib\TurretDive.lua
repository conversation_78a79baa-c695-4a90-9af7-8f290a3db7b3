
local ove_0_10 = player
local ove_0_11 = ove_0_10.charName == "Zed" and module.load("<PERSON>", "Champions/" .. ove_0_10.charName .. "/Util/menu") or module.load("<PERSON>", "Champions/" .. ove_0_10.charName .. "/menu")
local ove_0_12 = module.load("<PERSON>", "Lib/MyCommon")
local ove_0_13 = TEAM_ENEMY
local ove_0_14 = {
	range = 875
}

local function ove_0_15(arg_5_0)
	-- print 5
	local slot_5_0 = ove_0_12.ClosestEnemyTower(ove_0_10.pos)

	return arg_5_0:dist(slot_5_0.pos) <= ove_0_14.range
end

local function ove_0_16(arg_6_0)
	-- print 6
	if not ove_0_11.misc.turretdive.use:get() or not ove_0_15(arg_6_0) then
		return true
	end

	for iter_6_0, iter_6_1 in pairs(ove_0_12.GetEnemyTowers()) do
		local slot_6_0 = ove_0_12.ClosestEnemyTower(ove_0_10.pos)

		if ove_0_11.misc.turretdive.turretdivelogic:get() == 1 then
			if ove_0_12.Health() < ove_0_11.misc.turretdive.health:get() or ove_0_12.MinionCount(ove_0_14.range, TEAM_ALLY, slot_6_0.pos) < ove_0_11.misc.turretdive.normalmode:get() then
				return false
			end
		elseif ove_0_11.misc.turretdive.turretdivelogic:get() == 2 and (ove_0_12.MinionCount(ove_0_14.range, TEAM_ALLY, slot_6_0.pos) < ove_0_11.misc.turretdive.krystramode:get() or ove_0_12.Health() < ove_0_11.misc.turretdive.health1:get() or ove_0_12.HeroCount(ove_0_14.range, TEAM_ALLY, slot_6_0.pos) < ove_0_11.misc.turretdive.krystramode2:get()) then
			return false
		end
	end

	return true
end

return {
	DiveLogic = ove_0_16
}

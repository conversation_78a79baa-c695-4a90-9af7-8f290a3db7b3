
local ove_0_10 = {
	text = function(arg_5_0, arg_5_1, arg_5_2, arg_5_3, arg_5_4, arg_5_5, arg_5_6)
		local slot_5_0 = arg_5_4 or graphics.argb(255, 255, 255, 255)
		local slot_5_1, slot_5_2 = graphics.text_area(arg_5_0, arg_5_1)
		local slot_5_3 = graphics.height > 1080 and graphics.height / 1080 * 0.905 or 1

		if arg_5_5 then
			if arg_5_5 == "center" then
				arg_5_2 = arg_5_2 - slot_5_1 / 2
			elseif arg_5_5 == "right" then
				arg_5_2 = arg_5_2 - slot_5_1
			end

			if arg_5_6 and arg_5_6 ~= "middle" then
				if arg_5_6 == "top" then
					arg_5_3 = arg_5_3 + slot_5_2 / 2
				elseif arg_5_6 == "bottom" then
					arg_5_3 = arg_5_3 - slot_5_2 / 2
				end
			end
		end

		if graphics.width > 1920 then
			graphics.draw_text_2D(arg_5_0, arg_5_1, arg_5_2 * slot_5_3, arg_5_3 * slot_5_3, slot_5_0)
		else
			graphics.draw_text_2D(arg_5_0, arg_5_1, arg_5_2, arg_5_3, slot_5_0)
		end
	end,
	buttons = {}
}

function ove_0_10.xdraw()
	for iter_6_0 = 1, #ove_0_10.buttons do
		local slot_6_0 = ove_0_10.buttons[iter_6_0]

		if not slot_6_0.hidden then
			graphics.draw_line_2D(slot_6_0.x, slot_6_0.y, slot_6_0.x + slot_6_0.width, slot_6_0.y, slot_6_0.height, graphics.argb(slot_6_0.background_color.a, slot_6_0.background_color.r, slot_6_0.background_color.g, slot_6_0.background_color.b))
			ove_0_10.text(slot_6_0.text, slot_6_0.height / 2, slot_6_0.x + slot_6_0.width / 2, slot_6_0.y, graphics.argb(slot_6_0.textcolor.a, slot_6_0.textcolor.r, slot_6_0.textcolor.g, slot_6_0.textcolor.b), "center", "middle")
		end
	end
end

ove_0_10.keydown_x = false

function ove_0_10.keydown(arg_7_0)
	if arg_7_0.code ~= 343 then
		return
	end

	if ove_0_10.keydown_x then
		return
	end

	ove_0_10.keydown_x = true

	local slot_7_0 = arg_7_0.x
	local slot_7_1 = arg_7_0.y

	for iter_7_0 = 1, #ove_0_10.buttons do
		local slot_7_2 = ove_0_10.buttons[iter_7_0]

		if not slot_7_2.hidden and slot_7_0 > slot_7_2.x and slot_7_0 < slot_7_2.x + slot_7_2.width and slot_7_1 > slot_7_2.y - slot_7_2.height / 2 and slot_7_1 < slot_7_2.y + slot_7_2.height / 2 then
			slot_7_2.callback(slot_7_2.additional_information)

			break
		end
	end
end

function ove_0_10.keyup(arg_8_0)
	if arg_8_0.code ~= 343 then
		return
	end

	ove_0_10.keydown_x = false
end

function ove_0_10.add_button(arg_9_0, arg_9_1, arg_9_2, arg_9_3, arg_9_4, arg_9_5, arg_9_6, arg_9_7, arg_9_8, arg_9_9)
	local slot_9_0 = #ove_0_10.buttons + 1

	ove_0_10.buttons[slot_9_0] = {
		x = arg_9_0,
		y = arg_9_1,
		width = arg_9_2,
		height = arg_9_3,
		text = tostring(arg_9_4),
		background_color = arg_9_5,
		textcolor = arg_9_6,
		callback = arg_9_7,
		additional_information = arg_9_9,
		hidden = arg_9_8
	}

	return slot_9_0
end

function ove_0_10.change_hidden_status(arg_10_0, arg_10_1)
	for iter_10_0 = 1, #ove_0_10.buttons do
		if arg_10_1(ove_0_10.buttons[iter_10_0]) then
			ove_0_10.buttons[iter_10_0].hidden = arg_10_0
		end
	end
end

function ove_0_10.init_buttons()
	cb.add(cb.draw, ove_0_10.xdraw)
	cb.add(cb.keydown, ove_0_10.keydown)
	cb.add(cb.keyup, ove_0_10.keyup)
end

return ove_0_10


local ove_0_20 = module.load("<PERSON>", "Libraries/Common")


local ove_0_21 = menu("<PERSON>", "[<PERSON>] <PERSON>")
--if hanbot.language == 1 then
ove_0_21:header("load_header", "<PERSON> " .. player.charName .. " -")

local ove_0_22 = module.seek("evade")
local ove_0_23 = module.internal("pred")
local ove_0_24 = module.internal("TS")
local ove_0_25 = module.internal("orb")
--local ove_0_26 = module.load("<PERSON>", "Misc/HealerAIO_InfoBox")
local ove_0_27 = module.load("<PERSON>", "Libraries/Graphics")
local ove_0_28 = module.load("<PERSON>", "Databases/Dashers")
local ove_0_29 = module.load("<PERSON>", "Libraries/Damage")
local ove_0_30 = module.load("<PERSON>", "Databases/Buffs")
local ove_0_31 = module.load("<PERSON>", "Databases/SpellSlots")
local ove_0_32 = module.load("<PERSON>", "Databases/Mobs")
local ove_0_33 = module.load("<PERSON>", "Databases/Items")
local ove_0_34 = ove_0_20.EvadeVersion()

if ove_0_34 == 0 then
	ove_0_20.Evade_Error(ove_0_21)

	return
end



local ove_0_35 = {}
local ove_0_36 = {}
local ove_0_37 = 0

for iter_0_0 = 0, objManager.enemies_n - 1 do
	local ove_0_38 = objManager.enemies[iter_0_0]

	if ove_0_38 then
		ove_0_35[ove_0_38.ptr] = ove_0_38.charName
		ove_0_36[ove_0_38.charName] = true
		ove_0_36[ove_0_38.charName .. "_ptr"] = ove_0_38.ptr
		ove_0_37 = ove_0_37 + 1
	end
end

local ove_0_39 = {
	boundingRadiusMod = 1,
	range = 500,
	speed = 600,
	delay = 0.15,
	wall_width = 200,
	width = 180,
	collision = {
		wall = true,
		hero = false,
		minion = false
	},
	slot = player:spellSlot(_Q)
}
local ove_0_40 = {
	boundingRadiusMod = 1,
	range = 1200,
	speed = 3200,
	delay = 0.9833333333333334,
	wall_width = 100,
	width = 80,
	collision = {
		wall = true,
		hero = false,
		minion = false
	},
	slot = player:spellSlot(_Q)
}
local ove_0_41 = {
	boundingRadiusMod = 0,
	range = 465,
	delay = 0.25,
	range_dash = 525,
	range_draw = 475,
	radius = 1,
	speed = math.huge,
	slot = player:spellSlot(_W)
}
local ove_0_42 = {
	boundingRadiusMod = 0,
	range = 465,
	delay = 0,
	range_dash = 525,
	range_draw = 475,
	radius = 1,
	speed = math.huge,
	slot = player:spellSlot(_W)
}
local ove_0_43 = {
	boundingRadiusMod = 0,
	max_radius = 300,
	speed = 1300,
	delay = 0.25,
	wall_width = 100,
	range = 800,
	min_radius_draw = 100,
	min_radius = 200,
	radius = 300,
	slot = player:spellSlot(_E)
}
local ove_0_44 = {
	[0] = 2000,
	2000,
	2500,
	3000
}
local ove_0_45 = {
	boundingRadiusMod = 1,
	speed = 1600,
	delay = 0.25,
	wall_width = 150,
	width = 130,
	range = ove_0_44[player:spellSlot(_R).level],
	collision = {
		wall = true,
		hero = true,
		minion = false
	},
	slot = player:spellSlot(_R)
}

ove_0_21:menu("combo", "Combo Settings ")
ove_0_21.combo:header("h_c", " Spell Priority ")
ove_0_21.combo:dropdown("prio", "Spell priority: ", 1, {
	"[Q]->[E] ",
	"[E]->[Q] "
})
ove_0_21.combo:dropdown("prio_p", "When passive ready: ", 1, {
	"[Q]->[E] ",
	"[E]->[Q] "
})
ove_0_21.combo:boolean("prio_strict", "Strict prioritization ", false)
ove_0_21.combo.prio_strict:set("tooltip", "If enabled, Vex will strictly follow the selected spell priority rotation, without any exceptions.")
ove_0_21.combo:boolean("prio_r2", "Use [Q]->[E] after [R2] ", false)
ove_0_21.combo.prio_r2:set("tooltip", "Temporarily prioritizes [Q]->[E] after [R2], for a faster combo.")
ove_0_21.combo:header("h_q", " Q Settings ")
ove_0_21.combo:boolean("q_enable", "Use Q ", true)
ove_0_21.combo:dropdown("q_filter", "[Q] Prediction filter: ", 3, {
	"None ",
	"Basic ",
	"IO_v1 ",
	"IO_v1 Hard"
})
ove_0_21.combo.q_filter:set("tooltip", "Used to improve Q hit rate.")
ove_0_21.combo:dropdown("q_targeting", "[Q] Targeting: ", 1, {
	"Soft ",
	"Hard "
})
ove_0_21.combo.q_targeting:set("tooltip", "'Soft' casts Q on any target, that fits Q prediction filter, at any time. You can still click on a target, to hard focus it.")
ove_0_21.combo:slider("q_mana", "Q Mana % ", 0, 0, 100, 1)
ove_0_21.combo.q_mana:set("tooltip", "Will stop casting Q if your percent mana falls below the selected number ")
ove_0_21.combo:header("h_w", " W Settings ")
ove_0_21.combo:boolean("w_enable", "Use W ", true)
ove_0_21.combo:slider("w_mana", "W Mana % ", 0, 0, 100, 1)
ove_0_21.combo.w_mana:set("tooltip", "Will stop casting W if your percent mana falls below the selected number ")
ove_0_21.combo:header("h_e", " E Settings ")
ove_0_21.combo:boolean("e_enable", "Use E ", true)
ove_0_21.combo:dropdown("e_filter", "[E] Prediction filter: ", 2, {
	"None ",
	"Basic "
})
ove_0_21.combo.e_filter:set("tooltip", "Used to improve E hit rate.")
ove_0_21.combo:slider("e_mana", "E Mana % ", 0, 0, 100, 1)
ove_0_21.combo.e_mana:set("tooltip", "Will stop casting E if your percent mana falls below the selected number ")
ove_0_21:menu("harass", "Harass Settings ")
ove_0_21.harass:header("h_c", " Spell Priority ")
ove_0_21.harass:dropdown("prio", "Spell priority: ", 1, {
	"[Q]->[E] ",
	"[E]->[Q] "
})
ove_0_21.harass:dropdown("prio_p", "When passive ready: ", 1, {
	"[Q]->[E] ",
	"[E]->[Q] "
})
ove_0_21.harass:boolean("prio_strict", "Strict prioritization ", false)
ove_0_21.harass.prio_strict:set("tooltip", "If enabled, Vex will strictly follow the selected spell priority rotation, without any exceptions.")
ove_0_21.harass:header("h_q", " Q Settings ")
ove_0_21.harass:boolean("q_enable", "Use Q ", true)
ove_0_21.harass:dropdown("q_filter", "[Q] Prediction filter: ", 3, {
	"None ",
	"Basic ",
	"IO_v1 "
})
ove_0_21.harass.q_filter:set("tooltip", "Used to improve Q hit rate.")
ove_0_21.harass:dropdown("q_targeting", "[Q] Targeting: ", 1, {
	"Soft ",
	"Hard "
})
ove_0_21.harass.q_targeting:set("tooltip", "'Soft' casts Q on any target, that fits Q prediction filter, at any time. You can still click on a target, to hard focus it.")
ove_0_21.harass:slider("q_mana", "Q Mana % ", 0, 0, 100, 1)
ove_0_21.harass.q_mana:set("tooltip", "Will stop casting Q if your percent mana falls below the selected number ")
ove_0_21.harass:header("h_w", " W Settings ")
ove_0_21.harass:boolean("w_enable", "Use W ", true)
ove_0_21.harass:slider("w_mana", "W Mana % ", 0, 0, 100, 1)
ove_0_21.harass.w_mana:set("tooltip", "Will stop casting W if your percent mana falls below the selected number ")
ove_0_21.harass:header("h_e", " E Settings ")
ove_0_21.harass:boolean("e_enable", "Use E ", true)
ove_0_21.harass:dropdown("e_filter", "[E] Prediction filter: ", 2, {
	"None ",
	"Basic "
})
ove_0_21.harass.e_filter:set("tooltip", "Used to improve E hit rate.")
ove_0_21.harass:slider("e_mana", "E Mana % ", 0, 0, 100, 1)
ove_0_21.harass.e_mana:set("tooltip", "Will stop casting E if your percent mana falls below the selected number ")
ove_0_21:menu("rset", "R Settings ")
ove_0_21.rset:header("h_force", " Force R Key ")
ove_0_21.rset:keybind("r_force_key", "Force R key ", "MMB", nil)
ove_0_21.rset:dropdown("r_force_filter", "Force R Prediction filter: ", 1, {
	"None ",
	"Basic ",
	"IO_v1 "
})
ove_0_21.rset.r_force_filter:set("tooltip", "If selected, will make R more accurate, but will also take some time to aim it, and wont be cast immediately.")
ove_0_21.rset:header("h_r", " Auto R ")
ove_0_21.rset:keybind("r_auto_key", "Auto R enable key ", nil, "A")
ove_0_21.rset.r_auto_key:set("tooltip", "Uses R if the target is killable with full combo damage.")
ove_0_21.rset:keybind("r_allow_key", "Auto R allow key ", "G", nil)
ove_0_21.rset.r_allow_key:set("tooltip", "This key allows you to use Auto R on a killable enemy, that does not pass all Auto R1 checks. (Say the enemy has Flash, Dash or Zhonyas ready)")
ove_0_21.rset:dropdown("r_filter", "Auto R Prediction filter: ", 3, {
	"None ",
	"Basic ",
	"IO_v1 "
})
ove_0_21.rset.r_filter:set("tooltip", "Used to improve R1 hit rate.")
ove_0_21.rset:menu("r_blist", "Auto R Blacklist")
ove_0_21.rset.r_blist:header("h_r", " Auto R Blacklist ")

for iter_0_1 = 0, objManager.enemies_n - 1 do
	local ove_0_46 = objManager.enemies[iter_0_1]

	if ove_0_46 then
		ove_0_21.rset.r_blist:boolean(ove_0_46.charName, "[Block] " .. ove_0_46.charName, false)
	end
end

ove_0_21.rset:header("h_r1_r2", " R1/R2 Settings ")
ove_0_21.rset:menu("r1", "R1 Settings ")
ove_0_21.rset.r1:header("h_r", " R1 Settings ")
ove_0_21.rset.r1:boolean("r_enable", "Use R1 ", true)
ove_0_21.rset.r1.r_enable:set("tooltip", "Uses R1 if the target is killable with full combo damage.")
ove_0_21.rset.r1:header("h_r1_check", " R1 Check Settings ")
ove_0_21.rset.r1:menu("dont", "[Dont R1] Settings ")
ove_0_21.rset.r1.dont:header("h_dont", " [Dont R1] If ")
ove_0_21.rset.r1.dont:boolean("ga", "If enemy has GA ", true)
ove_0_21.rset.r1.dont.ga:set("tooltip", "Wont R1 enemies with Guardian Angel.")
ove_0_21.rset.r1.dont:boolean("flash", "If enemy has Flash ready ", true)
ove_0_21.rset.r1.dont.flash:set("tooltip", "Wont R1 enemies that have Summoner Flash ready.")
ove_0_21.rset.r1.dont:boolean("dash", "If enemy has dash/speed ready ", true)
ove_0_21.rset.r1.dont.dash:set("tooltip", "Wont R1 enemies that have a dash or speed buff ability ready.")
ove_0_21.rset.r1.dont:boolean("zh", "If enemy has Zhonyas ready ", true)
ove_0_21.rset.r1.dont.zh:set("tooltip", "Wont R1 enemies that have Zhonyas or Stopwatch ready.")
ove_0_21.rset.r1.dont:boolean("spellshield", "If enemy can spell shield ", true)
ove_0_21.rset.r1.dont.spellshield:set("tooltip", "Wont R1 enemies that have a spell shield ready.")
ove_0_21.rset.r1.dont:boolean("invulnerable", "If enemy has invulnerability ready ", true)
ove_0_21.rset.r1.dont.invulnerable:set("tooltip", "Wont R1 champions that have invulnerability ability ready. (Kayle R, Kindred R, Lissandra R etc...)")
ove_0_21.rset.r1.dont:boolean("block", "If enemy can block R1 ", true)
ove_0_21.rset.r1.dont.block:set("tooltip", "Wont R1 Yasuo/Samira, if they have W ready.")
ove_0_21.rset.r1.dont:boolean("revive", "If enemy has revive passive ", true)
ove_0_21.rset.r1.dont.revive:set("tooltip", "Wont R1 enemies that have revive passive ready.")
ove_0_21.rset.r1:menu("ignore", "Ignore [Dont R1] if R reset ")
ove_0_21.rset.r1.ignore:header("h_dont", " Ignores [Dont R1] Settings ")
ove_0_21.rset.r1.ignore:header("h_dont2", " If Vex has R reset ")
ove_0_21.rset.r1.ignore:boolean("ga", "Ignore GA check ", true)
ove_0_21.rset.r1.ignore.ga:set("tooltip", "Casts R1 even if an enemy has Guardian Angel.")
ove_0_21.rset.r1.ignore:boolean("flash", "Ignore Flash check ", true)
ove_0_21.rset.r1.ignore.flash:set("tooltip", "Casts R1 even if an enemy has Summoner Flash ready.")
ove_0_21.rset.r1.ignore:boolean("dash", "Ignore dash/speed check ", true)
ove_0_21.rset.r1.ignore.dash:set("tooltip", "Casts R1 even if an enemy has a dash or speed buff ability ready.")
ove_0_21.rset.r1.ignore:boolean("zh", "Ignore Zhonyas check", false)
ove_0_21.rset.r1.ignore.zh:set("tooltip", "Casts R1 even if an enemy has Zhonyas or Stopwatch ready.")
ove_0_21.rset.r1.ignore:boolean("spellshield", "Ignore spell shield check", false)
ove_0_21.rset.r1.ignore.spellshield:set("tooltip", "Casts R1 even if an enemy has a spell shield ready.")
ove_0_21.rset.r1.ignore:boolean("invulnerable", "Ignore invulnerability check ", false)
ove_0_21.rset.r1.ignore.invulnerable:set("tooltip", "Casts R1 even if an enemy has invulnerability ability ready. (Kayle R, Kindred R, Lissandra R etc...)")
ove_0_21.rset.r1.ignore:boolean("block", "Ignore block check", false)
ove_0_21.rset.r1.ignore.block:set("tooltip", "Casts R1 even if Yasuo/Samira have W spell ready.")
ove_0_21.rset.r1.ignore:boolean("revive", "Ignore revive check", true)
ove_0_21.rset.r1.ignore.revive:set("tooltip", "Casts R1 even if the enemy has revive passive ready.")
ove_0_21.rset.r1:header("h_r1_additionals", " Additional Settings ")
ove_0_21.rset.r1:boolean("r_next", "R1 next [reset, no checks] ", true)
ove_0_21.rset.r1.r_next:set("tooltip", "If Vex gets a reset, she will immediately cast R1 on another target, ignoring all killable checks.")
ove_0_21.rset.r1:boolean("r_next_ls", " ^- only if last second ", false)
ove_0_21.rset.r1.r_next_ls:set("tooltip", "Will only [R1 next] on the last possible [R1 reset] hold time second.")
ove_0_21.rset:menu("r2", "R2 Settings ")
ove_0_21.rset.r2:header("h_r2", " R2 Settings ")
ove_0_21.rset.r2:boolean("enable", "Use R2 ", true)
ove_0_21.rset.r2:boolean("always", " ^- always [no checks] ", false)
ove_0_21.rset.r2:header("h_r2_always", " [Always R2] If ")
ove_0_21.rset.r2:boolean("kill_r2", "If kills with R2 damage ", true)
ove_0_21.rset.r2.kill_r2:set("tooltip", "If kills with R2 damage only.")
ove_0_21.rset.r2:boolean("kill_r2_w", " ^- include W damage too ", true)
ove_0_21.rset.r2:header("h_r2_o", " --- ")
ove_0_21.rset.r2:menu("allow", "[Allow R2] Settings")
ove_0_21.rset.r2.allow:header("h_r2_allow", " [Allow R2] If ")
ove_0_21.rset.r2.allow:boolean("kill_combo", "If kills with combo damage ", true)
ove_0_21.rset.r2.allow.kill_combo:set("tooltip", "If kills with full combo damage.")
ove_0_21.rset.r2.allow:boolean("allies", "If X allies are around the target ", true)
ove_0_21.rset.r2.allow.allies:set("tooltip", "If X allies are around the target.")
ove_0_21.rset.r2.allow:slider("ally_count", " ^- ally count ", 1, 1, 5, 1)
ove_0_21.rset.r2.allow:boolean("enemies", "If X enemies are around Vex ", true)
ove_0_21.rset.r2.allow.enemies:set("tooltip", "If X enemies are around Vex.")
ove_0_21.rset.r2.allow:slider("enemy_count", " ^- enemy count ", 2, 1, 5, 1)
ove_0_21.rset.r2.allow:boolean("target_close", "If R2 target is close ", true)
ove_0_21.rset.r2.allow:slider("target_close_dist", " ^- check range ", 1200, 0, 2000, 10)
ove_0_21.rset.r2.allow:boolean("reset", "If Vex had a reset ", true)
ove_0_21.rset.r2:menu("dont", "Dont [Allow R2] Settings")
ove_0_21.rset.r2.dont:header("h_r2_dont", " Dont [Allow R2] If ")
ove_0_21.rset.r2.dont:slider("hp", "Vex HP% is below X ", 0, 0, 100, 1)
ove_0_21.rset.r2.dont.hp:set("tooltip", "Wont use [Allow R2] if Vex HP% is at or below the selected value.")
ove_0_21.rset.r2.dont:boolean("safe_check", "If not safe to dash ", true)
ove_0_21.rset.r2.dont.safe_check:set("tooltip", "Wont use [Allow R2] if R2 dash path is blocked by Veigar E, Ziggs E etc...")
ove_0_21.rset.r2.dont:boolean("w_cooldown", "If W is on cooldown ", false)
ove_0_21.rset.r2.dont:boolean("turret", "If enemy is under turret ", true)
ove_0_21.rset.r2.dont:boolean("turret_allow", " ^- allow if W ready ", true)
ove_0_21.rset.r2.dont:boolean("fountain", "If enemy is in the fountain ", true)
ove_0_21.rset.r2.dont.fountain:set("tooltip", "o____o")
ove_0_21.rset:header("h_r_dmg", " Damage Settings ")
ove_0_21.rset:menu("dmg", "Combo Damage Settings")
ove_0_21.rset.dmg:header("h_dmg", " Damage Settings ")
ove_0_21.rset.dmg:boolean("r1", "Include R1 damage ", true)
ove_0_21.rset.dmg:boolean("r2", "Include R2 damage ", true)
ove_0_21.rset.dmg:boolean("q", "Include Q damage ", true)
ove_0_21.rset.dmg:boolean("w", "Include W damage ", true)
ove_0_21.rset.dmg:boolean("e", "Include E damage ", true)
ove_0_21.rset.dmg:boolean("p", "Include passive damage ", true)
ove_0_21.rset.dmg:slider("aa", "Include X AA damage ", 2, 0, 10, 1)
ove_0_21.rset.dmg:header("h_add", " Additional Settings ")
ove_0_21.rset.dmg:boolean("ckill", "Consider enemy as killable if combo damage leaves them at X% HP ", false)
ove_0_21.rset.dmg.ckill:set("tooltip", "Consider enemy as killable if combo damage leaves them at X% HP.")
ove_0_21.rset.dmg:slider("ckill_hp", " ^- if at X% HP ", 15, 0, 100, 1)
ove_0_21.rset.dmg:boolean("ckill_allies", " ^- only if allies are around ", true)
ove_0_21.rset:header("h_r_add", " Additional Settings ")
ove_0_21.rset:boolean("qwe_hold", "Hold [Q/W/E] on [R1] cast", true)
ove_0_21.rset.qwe_hold:set("tooltip", "Holds [Q/W/E] spells while [R1] is travelling, so that Vex wouldn't waste her abilities on other targets.")
ove_0_21.rset:boolean("toggle_on", "Load with [Auto R] enabled ", true)
ove_0_21.rset.toggle_on:set("tooltip", "Enables [Auto R] toggle, on each script load.")
ove_0_21:menu("farm", "Farm Settings ")
ove_0_21.farm:header("h_farm_key", " Farm Toggle Key ")
ove_0_21.farm:keybind("farm_key", "Farm toggle key: ", nil, "Z")
ove_0_21.farm:header("h_farm_sets", " Farm Settings ")
ove_0_21.farm:menu("lane", "Lane Clear ")
ove_0_21.farm.lane:header("h_q", " - [Q] - ")
ove_0_21.farm.lane:boolean("q_enable", "Use Q ", false)
ove_0_21.farm.lane:menu("q", "Q Settings ")
ove_0_21.farm.lane.q:header("h_q", " Q Settings ")
ove_0_21.farm.lane.q:slider("q_count", "Min minions to Q ", 5, 1, 10, 1)
ove_0_21.farm.lane.q:boolean("q_count_max", "Use Q on max amount of minions ", true)
ove_0_21.farm.lane.q.q_count_max:set("tooltip", "Uses Q only when all minions around you can be hit in a single line.")
ove_0_21.farm.lane.q:boolean("q_dis_near", "Disable Q if enemies nearby ", false)
ove_0_21.farm.lane.q:boolean("q_dis_pass", "Dont Q if passive ready ", false)
ove_0_21.farm.lane.q:boolean("q_dis_move", "Dont Q if minions are moving", true)
ove_0_21.farm.lane.q.q_dis_move:set("tooltip", "Will only use Q if minions are not moving, this increases Q accuracy.")
ove_0_21.farm.lane.q:slider("q_dis_move_allow", "^- Allow movement of X minions", 0, 0, 6, 1)
ove_0_21.farm.lane.q.q_dis_move_allow:set("tooltip", "Allows movement of X minions, makes the setting above less strict :>")
ove_0_21.farm.lane.q:slider("q_mana", "[Q] Mana % ", 30, 0, 100, 1)
ove_0_21.farm.lane.q.q_mana:set("tooltip", "Will stop using [Q] if your percent mana falls below the selected number ")
ove_0_21.farm.lane:header("h_w", " - [W] - ")
ove_0_21.farm.lane:boolean("w_enable", "Use W ", false)
ove_0_21.farm.lane:menu("w", "W Settings ")
ove_0_21.farm.lane.w:header("h_w", " W Settings ")
ove_0_21.farm.lane.w:slider("w_count", "Min killable minions to W ", 5, 1, 10, 1)
ove_0_21.farm.lane.w:boolean("w_dis_near", "Disable W if enemies nearby ", false)
ove_0_21.farm.lane.w:boolean("w_dis_pass", "Dont W if passive ready ", false)
ove_0_21.farm.lane.w:slider("w_mana", "[W] Mana % ", 30, 0, 100, 1)
ove_0_21.farm.lane.w.w_mana:set("tooltip", "Will stop using [W] if your percent mana falls below the selected number ")
ove_0_21.farm.lane:header("h_e", " - [E] - ")
ove_0_21.farm.lane:boolean("e_enable", "Use E ", false)
ove_0_21.farm.lane:menu("e", "E Settings ")
ove_0_21.farm.lane.e:header("h_e", " E Settings ")
ove_0_21.farm.lane.e:slider("e_count", "Min minions to E ", 5, 1, 10, 1)
ove_0_21.farm.lane.e:boolean("e_dis_near", "Disable E if enemies nearby ", false)
ove_0_21.farm.lane.e:boolean("e_dis_pass", "Dont E if passive ready ", false)
ove_0_21.farm.lane.e:boolean("e_dis_move", "Dont E if minions are moving", true)
ove_0_21.farm.lane.e.e_dis_move:set("tooltip", "Will only use E if minions are not moving, this increases E accuracy.")
ove_0_21.farm.lane.e:slider("e_dis_move_allow", "^- Allow movement of X minions", 0, 0, 6, 1)
ove_0_21.farm.lane.e.e_dis_move_allow:set("tooltip", "Allows movement of X minions, makes the setting above less strict :>")
ove_0_21.farm.lane.e:slider("e_mana", "[E] Mana % ", 30, 0, 100, 1)
ove_0_21.farm.lane.e.e_mana:set("tooltip", "Will stop using [E] if your percent mana falls below the selected number ")
ove_0_21.farm:menu("panic", "Panic Clear ")
ove_0_21.farm.panic:header("h_panic_sets", " Panic Clear Activation ")
ove_0_21.farm.panic:dropdown("panic_method", "Activation method: ", 1, {
	"Hanbot Orb ",
	"Custom ",
	"OFF "
})
ove_0_21.farm.panic.panic_method:set("tooltip", "You can set up 'Panic Clear' inside [Hanbot Orbwalker => Lane Clear] menu.")
ove_0_21.farm.panic:keybind("panic_custom_key", "Custom Panic key: ", "LMB", nil)
ove_0_21.farm.panic:button("panic_button_reset", " ^- set back to LMB", "Click me", function()
	-- print 5
	ove_0_21.farm.panic.panic_custom_key:set("key", "LMB")
	ove_0_21.farm.panic.panic_custom_key:set("toggle", "")
end)
ove_0_21.farm.panic.panic_custom_key:set("tooltip", "Sets the keybind back to the Left Mouse Button (LMB)")
ove_0_21.farm.panic:header("h_q", " - [Q] - ")
ove_0_21.farm.panic:boolean("q_enable", "Use Q ", true)
ove_0_21.farm.panic:menu("q", "Q Settings ")
ove_0_21.farm.panic.q:header("h_q", " Q Settings ")
ove_0_21.farm.panic.q:slider("q_count", "Min minions to Q ", 1, 1, 10, 1)
ove_0_21.farm.panic.q:boolean("q_count_max", "Use Q on max amount of minions ", false)
ove_0_21.farm.panic.q.q_count_max:set("tooltip", "Uses Q only when all minions around you can be hit in a single line.")
ove_0_21.farm.panic.q:boolean("q_dis_near", "Disable Q if enemies nearby ", false)
ove_0_21.farm.panic.q:boolean("q_dis_pass", "Dont Q if passive ready ", false)
ove_0_21.farm.panic.q:boolean("q_dis_move", "Dont Q if minions are moving", false)
ove_0_21.farm.panic.q.q_dis_move:set("tooltip", "Will only use Q if minions are not moving, this increases Q accuracy.")
ove_0_21.farm.panic.q:slider("q_dis_move_allow", "^- Allow movement of X minions", 0, 0, 6, 1)
ove_0_21.farm.panic.q.q_dis_move_allow:set("tooltip", "Allows movement of X minions, makes the setting above less strict :>")
ove_0_21.farm.panic.q:slider("q_mana", "[Q] Mana % ", 0, 0, 100, 1)
ove_0_21.farm.panic.q.q_mana:set("tooltip", "Will stop using [Q] if your percent mana falls below the selected number ")
ove_0_21.farm.panic:header("h_w", " - [W] - ")
ove_0_21.farm.panic:boolean("w_enable", "Use W ", false)
ove_0_21.farm.panic:menu("w", "W Settings ")
ove_0_21.farm.panic.w:header("h_w", " W Settings ")
ove_0_21.farm.panic.w:slider("w_count", "Min killable minions to W ", 1, 1, 10, 1)
ove_0_21.farm.panic.w:boolean("w_dis_near", "Disable W if enemies nearby ", false)
ove_0_21.farm.panic.w:boolean("w_dis_pass", "Dont W if passive ready ", false)
ove_0_21.farm.panic.w:slider("w_mana", "[W] Mana % ", 0, 0, 100, 1)
ove_0_21.farm.panic.w.w_mana:set("tooltip", "Will stop using [W] if your percent mana falls below the selected number ")
ove_0_21.farm.panic:header("h_e", " - [E] - ")
ove_0_21.farm.panic:boolean("e_enable", "Use E ", true)
ove_0_21.farm.panic:menu("e", "E Settings ")
ove_0_21.farm.panic.e:header("h_e", " E Settings ")
ove_0_21.farm.panic.e:slider("e_count", "Min minions to E ", 1, 1, 10, 1)
ove_0_21.farm.panic.e:boolean("e_dis_near", "Disable E if enemies nearby ", false)
ove_0_21.farm.panic.e:boolean("e_dis_pass", "Dont E if passive ready ", false)
ove_0_21.farm.panic.e:boolean("e_dis_move", "Dont E if minions are moving", false)
ove_0_21.farm.panic.e.e_dis_move:set("tooltip", "Will only use E if minions are not moving, this increases E accuracy.")
ove_0_21.farm.panic.e:slider("e_dis_move_allow", "^- Allow movement of X minions", 0, 0, 6, 1)
ove_0_21.farm.panic.e.e_dis_move_allow:set("tooltip", "Allows movement of X minions, makes the setting above less strict :>")
ove_0_21.farm.panic.e:slider("e_mana", "[E] Mana % ", 0, 0, 100, 1)
ove_0_21.farm.panic.e.e_mana:set("tooltip", "Will stop using [E] if your percent mana falls below the selected number ")
ove_0_21.farm:menu("help", "Orb Clear Assist ")
ove_0_21.farm.help:header("h_help_mode", " Orb Clear Assist ")
ove_0_21.farm.help:boolean("help_lane", "Enable in Lane Clear ", true)
ove_0_21.farm.help:boolean("help_last", "Enable in Last Hit ", true)
ove_0_21.farm.help:header("h_q", " - [Q] - ")
ove_0_21.farm.help:boolean("q_enable", "Use Q ", false)
ove_0_21.farm.help.q_enable:set("tooltip", "Uses Q on a minion that the orb cannot last hit normally with AA ")
ove_0_21.farm.help:menu("q", "Q Settings ")
ove_0_21.farm.help.q:header("h_q", " Q Assist Settings ")
ove_0_21.farm.help.q:boolean("q_cannon", "Q only cannon minions ", true)
ove_0_21.farm.help.q:boolean("q_aa_range", "Q only if inside AA range ", false)
ove_0_21.farm.help.q:boolean("q_dis_near", "Dont Q if enemies nearby ", false)
ove_0_21.farm.help.q:boolean("q_dis_pass", "Dont Q if passive ready ", false)
ove_0_21.farm.help.q:slider("q_mana", "[Q] Mana % ", 0, 0, 100, 1)
ove_0_21.farm.help.q.q_mana:set("tooltip", "Will stop using [Q] if your percent mana falls below the selected number ")
ove_0_21.farm.help:header("h_w", " - [W] - ")
ove_0_21.farm.help:boolean("w_enable", "Use W ", true)
ove_0_21.farm.help.w_enable:set("tooltip", "Uses W on a minion that the orb cannot last hit normally with AA ")
ove_0_21.farm.help:menu("w", "W Settings ")
ove_0_21.farm.help.w:header("h_w", " W Assist Settings ")
ove_0_21.farm.help.w:boolean("w_cannon", "W only cannon minions ", true)
ove_0_21.farm.help.w:boolean("w_dis_near", "Dont W if enemies nearby ", false)
ove_0_21.farm.help.w:boolean("w_dis_pass", "Dont W if passive ready ", false)
ove_0_21.farm.help.w:slider("w_mana", "[W] Mana % ", 0, 0, 100, 1)
ove_0_21.farm.help.w.w_mana:set("tooltip", "Will stop using [W] if your percent mana falls below the selected number ")
ove_0_21.farm.help:header("h_e", " - [E] - ")
ove_0_21.farm.help:boolean("e_enable", "Use E ", false)
ove_0_21.farm.help.e_enable:set("tooltip", "Uses E on a minion that the orb cannot last hit normally with AA ")
ove_0_21.farm.help:menu("e", "E Settings ")
ove_0_21.farm.help.e:header("h_e", " E Assist Settings ")
ove_0_21.farm.help.e:boolean("e_cannon", "E only cannon minions ", true)
ove_0_21.farm.help.e:boolean("e_aa_range", "E only if inside AA range ", false)
ove_0_21.farm.help.e:boolean("e_dis_near", "Dont E if enemies nearby ", false)
ove_0_21.farm.help.e:boolean("e_dis_pass", "Dont E if passive ready ", false)
ove_0_21.farm.help.e:slider("e_mana", "[E] Mana % ", 0, 0, 100, 1)
ove_0_21.farm.help.e.e_mana:set("tooltip", "Will stop using [E] if your percent mana falls below the selected number ")
ove_0_21.farm:menu("jung", "Jungle Clear ")
ove_0_21.farm.jung:header("h_jung", " Jungle Clear ")
ove_0_21.farm.jung:boolean("aggro", "Only clear aggroed mobs", true)
ove_0_21.farm.jung.aggro:set("tooltip", "Clears only aggroed jungle mobs.")
ove_0_21.farm.jung:header("h_q", " - [Q] - ")
ove_0_21.farm.jung:boolean("q_enable", "Use Q ", true)
ove_0_21.farm.jung:menu("q", "Q Settings ")
ove_0_21.farm.jung.q:header("h_q", " Q Settings ")
ove_0_21.farm.jung.q:boolean("q_count_max", "Use Q on max amount of mobs ", true)
ove_0_21.farm.jung.q.q_count_max:set("tooltip", "Uses Q only when all jungle mobs can be hit in a single line.")
ove_0_21.farm.jung.q:boolean("q_dis_near", "Dont Q if enemies nearby ", true)
ove_0_21.farm.jung.q:boolean("q_dis_pass", "Dont Q if passive ready ", false)
ove_0_21.farm.jung.q:slider("q_mana", "[Q] Mana % ", 0, 0, 100, 1)
ove_0_21.farm.jung.q.q_mana:set("tooltip", "Will stop using [Q] if your percent mana falls below the selected number ")
ove_0_21.farm.jung:header("h_w", " - [W] - ")
ove_0_21.farm.jung:boolean("w_enable", "Use W ", false)
ove_0_21.farm.jung:menu("w", "W Settings ")
ove_0_21.farm.jung.w:header("h_w", " W Settings ")
ove_0_21.farm.jung.w:boolean("w_dis_near", "Dont W if enemies nearby ", true)
ove_0_21.farm.jung.w:boolean("w_dis_pass", "Dont W if passive ready ", false)
ove_0_21.farm.jung.w:slider("w_mana", "[W] Mana % ", 0, 0, 100, 1)
ove_0_21.farm.jung.w.w_mana:set("tooltip", "Will stop using [W] if your percent mana falls below the selected number ")
ove_0_21.farm.jung:header("h_e", " - [E] - ")
ove_0_21.farm.jung:boolean("e_enable", "Use E ", true)
ove_0_21.farm.jung:menu("e", "E Settings ")
ove_0_21.farm.jung.e:header("h_e", " E Settings ")
ove_0_21.farm.jung.e:boolean("e_dis_near", "Dont E if enemies nearby ", true)
ove_0_21.farm.jung.e:boolean("e_dis_pass", "Dont E if passive ready ", false)
ove_0_21.farm.jung.e:slider("e_mana", "[E] Mana % ", 0, 0, 100, 1)
ove_0_21.farm.jung.e.e_mana:set("tooltip", "Will stop using [E] if your percent mana falls below the selected number ")
ove_0_21:menu("misc", "Misc Settings ")
ove_0_21.misc:header("h_shield", " Shield Settings ")
ove_0_21.misc:boolean("w_lethal", "[W] shield lethal damage", true)
ove_0_21.misc.w_lethal:set("tooltip", "If enemy damage would kill you, Vex will use her shield. [Requires Evade 2.0]")
ove_0_21.misc:boolean("w_percent", "[W] shield percent damage", true)
ove_0_21.misc.w_percent:set("tooltip", "If an enemy would deal X% of damage to your health bar, Vex will use her shield. [Requires Evade 2.0]")
ove_0_21.misc:slider("w_percent_value", " ^- damage percent ", 10, 1, 100, 1)
ove_0_21.misc:boolean("w_destroy", "[W] if the shield would break", true)
ove_0_21.misc.w_destroy:set("tooltip", "Vex will use W, if the incoming enemy damage is strong enough to break her W shield. [Requires Evade 2.0]")
ove_0_21.misc:header("h_gap", " Anti-Gapclose Settings ")
ove_0_21.misc:boolean("q_gap", "[Q] Gapclosers ", true)
ove_0_21.misc:boolean("w_gap", "[W] Gapclosers ", true)
ove_0_21.misc:boolean("e_gap", "[E] Gapclosers ", true)
ove_0_21.misc:boolean("c_gap", "Check if spell will hit ", true)
ove_0_21.misc.c_gap:set("tooltip", "Casts only if the spell is guaranteed to hit a dashing enemy champion.")
ove_0_21.misc:menu("gap_blist", "Anti-Gapclose Blacklist")
ove_0_21.misc.gap_blist:header("h_gap", " Anti-Gapclose Blacklist ")

for iter_0_2 = 0, objManager.enemies_n - 1 do
	local ove_0_47 = objManager.enemies[iter_0_2]

	if ove_0_47 then
		ove_0_21.misc.gap_blist:boolean(ove_0_47.charName, "[Block] " .. ove_0_47.charName, false)
	end
end

ove_0_21.misc:header("h_cc", " On CC Settings ")
ove_0_21.misc:boolean("q_cc", "[Q] CC'ed targets ", true)
ove_0_21.misc:boolean("e_cc", "[E] CC'ed targets ", true)
ove_0_21.misc:header("h_aoe_fear", " Flash + W after R2 ")
ove_0_21.misc:boolean("aoe_fear", "[Flash + W] after [R2] ", true)
ove_0_21.misc.aoe_fear:set("tooltip", "Works only when Vex has passive ready.")
ove_0_21.misc:slider("aoe_fear_count", " ^- to hit X enemies ", 3, 2, 5, 1)
ove_0_21:menu("draws", "Draw Settings ")
ove_0_21.draws:header("h_draw", " Draw Settings ")
ove_0_21.draws:menu("range", "Range Drawings ")
ove_0_21.draws.range:header("h_range", " Range Drawings ")
ove_0_21.draws.range:boolean("q_draw", "Draw [Q] range", true)
ove_0_21.draws.range:color("q_draw_color", "  ^- Color", 144, 0, 212, 255)
ove_0_21.draws.range:boolean("w_draw", "Draw [W] range", false)
ove_0_21.draws.range:color("w_draw_color", "  ^- Color", 144, 0, 212, 255)
ove_0_21.draws.range:boolean("e_draw", "Draw [E] range", false)
ove_0_21.draws.range:color("e_draw_color", "  ^- Color", 144, 0, 212, 255)
ove_0_21.draws.range:boolean("r_draw", "Draw [R] range", true)
ove_0_21.draws.range:color("r_draw_color", "  ^- Color", 144, 0, 212, 255)
ove_0_21.draws.range:boolean("r_draw_mini", "Draw [R] minimap range", false)
ove_0_21.draws.range:color("r_draw_mini_color", "  ^- Color", 144, 0, 212, 255)
ove_0_21.draws:menu("r_hud", "R HUD Drawing ")
ove_0_21.draws.r_hud:header("h_hud", " R HUD Settings ")
ove_0_21.draws.r_hud:boolean("enable", "Enable ", true)
ove_0_21.draws.r_hud:boolean("draw_donts", "Draw [R1] filter status ", true)
ove_0_21.draws.r_hud.draw_donts:set("tooltip", "If enabled, draws a killable enemy that does not pass all [Dont R1] checks, with a reason why, and a keybind, that when pressed, allows a script to use [R1] on that enemy anyway.")

local ove_0_48 = -1

ove_0_21.draws.r_hud:button("test", "Test drawing ", "Test", function()
	-- print 6
	ove_0_48 = game.time + 2
end)
ove_0_21.draws.r_hud:header("h_hud_x", " - X Offset - ")
ove_0_21.draws.r_hud:slider("x_offset", "[X] Position offset", 0, -250, 250, 1)
ove_0_21.draws.r_hud:button("x_plus", "[+1]", "[+1]", function()
	-- print 7
	if ove_0_21.draws.r_hud.x_offset:get() < 250 then
		ove_0_21.draws.r_hud.x_offset:set("value", ove_0_21.draws.r_hud.x_offset:get() + 1)
	end

	ove_0_48 = game.time + 2
end)
ove_0_21.draws.r_hud:button("x_minus", "[-1]", "[-1]", function()
	-- print 8
	if ove_0_21.draws.r_hud.x_offset:get() > -250 then
		ove_0_21.draws.r_hud.x_offset:set("value", ove_0_21.draws.r_hud.x_offset:get() - 1)
	end

	ove_0_48 = game.time + 2
end)
ove_0_21.draws.r_hud:header("h_hud_y", " - Y Offset - ")
ove_0_21.draws.r_hud:slider("y_offset", "[Y] Position offset", 0, -250, 250, 1)
ove_0_21.draws.r_hud:button("y_plus", "[+1]", "[+1]", function()
	-- print 9
	if ove_0_21.draws.r_hud.y_offset:get() < 250 then
		ove_0_21.draws.r_hud.y_offset:set("value", ove_0_21.draws.r_hud.y_offset:get() + 1)
	end

	ove_0_48 = game.time + 2
end)
ove_0_21.draws.r_hud:button("y_minus", "[-1]", "[-1]", function()
	-- print 10
	if ove_0_21.draws.r_hud.y_offset:get() > -250 then
		ove_0_21.draws.r_hud.y_offset:set("value", ove_0_21.draws.r_hud.y_offset:get() - 1)
	end

	ove_0_48 = game.time + 2
end)
ove_0_21.draws:menu("damage", "Damage Drawings ")
ove_0_21.draws.damage:header("h_damage", " Damage Drawings ")
--ove_0_21.draws.damage:boolean("enable", "Enable ", true)
--ove_0_21.draws.damage:color("color_norm", "Normal color ", 150, 255, 255, 100)
--ove_0_21.draws.damage:color("color_kill", "Killable color ", 150, 255, 255, 100)
ove_0_21:menu("keys", "Key Settings")
ove_0_21.keys:header("h_keys_sets", "Key Settings")
ove_0_21.keys:keybind("keys_combo", "Combo Key", "Space", nil)
ove_0_21.keys:keybind("keys_harass", "Harass Key ", "C", nil)
ove_0_21.keys:keybind("keys_laneclear", "Lane Clear Key", "V", nil)
ove_0_21.keys:keybind("keys_lasthit", "Last Hit", "X", nil)
--ove_0_21:header("headree", "- Healer AIO -")
--ove_0_21:header("headree1", "- - Healer AIO - -")
--ove_0_21:header("headree21", "- - - Healer AIO - - -")
--ove_0_21:header("headree213", "Healer AIO")
--ove_0_21.headree:set("visible", false)
--ove_0_21.headree1:set("visible", false)
--ove_0_21.headree21:set("visible", false)
--ove_0_21.headree213:set("visible", false)

if ove_0_21.rset.toggle_on:get() then
	ove_0_21.rset.r_auto_key:set("toggleValue", true)
end

local ove_0_49 = ove_0_20.GetFlashSlot()
local ove_0_50 = vec3(394, 182.13250732422, 462)
local ove_0_51 = vec3(14340, 171.97772216797, 14390)
local ove_0_52 = false

if game.mapID == 11 then
	ove_0_52 = true
end

local ove_0_53

if ove_0_52 then
	if player.team == 200 then
		ove_0_53 = ove_0_50
	else
		ove_0_53 = ove_0_51
	end
end

local ove_0_54 = {
	ove_0_30.BUFF_ENUM_STUN,
	ove_0_30.BUFF_ENUM_TAUNT,
	ove_0_30.BUFF_ENUM_GROUNDED,
	ove_0_30.BUFF_ENUM_SNARE,
	ove_0_30.BUFF_ENUM_FEAR,
	ove_0_30.BUFF_ENUM_CHARM,
	ove_0_30.BUFF_ENUM_SUPPRESSION,
	ove_0_30.BUFF_ENUM_KNOCKUP,
	ove_0_30.BUFF_ENUM_KNOCKBACK,
	ove_0_30.BUFF_ENUM_ASLEEP
}

local function ove_0_55(arg_11_0)
	-- print 11
	local slot_11_0 = mathf.clamp(0, 800, arg_11_0)
	local slot_11_1 = ove_0_20.InvLerp(0, 800, slot_11_0)

	return (ove_0_20.Lerp(200, 295, slot_11_1))
end

local function ove_0_56(arg_12_0)
	-- print 12
	local slot_12_0 = mathf.clamp(0, 800, arg_12_0)
	local slot_12_1 = ove_0_20.InvLerp(0, 800, slot_12_0)

	return (ove_0_20.Lerp(200, 265, slot_12_1))
end

local ove_0_57 = false
local ove_0_58 = 0

local function ove_0_59()
	-- print 13
	if ove_0_21.rset.r_force_filter:get() ~= 1 then
		if ove_0_21.rset.r_force_key:get() then
			if ove_0_57 == false and os.clock() > ove_0_58 then
				ove_0_57 = true
				ove_0_58 = os.clock() + 0.3
			end

			if ove_0_57 == true and os.clock() > ove_0_58 then
				ove_0_57 = false
				ove_0_58 = os.clock() + 0.3
			end
		end
	else
		ove_0_57 = false
	end
end

local function ove_0_60(arg_14_0)
	-- print 14
	ove_0_43.radius = 1

	local slot_14_0 = ove_0_23.circular.get_prediction(ove_0_43, arg_14_0)

	if not slot_14_0 then
		return nil
	end

	local slot_14_1 = slot_14_0:length()

	ove_0_43.radius = ove_0_55(slot_14_1)

	local slot_14_2 = ove_0_23.circular.get_prediction(ove_0_43, arg_14_0)

	if not slot_14_2 then
		return nil
	end

	local slot_14_3 = slot_14_2:length()

	ove_0_43.radius = ove_0_55(slot_14_3)

	local slot_14_4 = ove_0_23.circular.get_prediction(ove_0_43, arg_14_0)

	if not slot_14_4 then
		return nil
	end

	return slot_14_4
end

local function ove_0_61(arg_15_0, arg_15_1)
	-- print 15
	if not arg_15_0.path.isActive then
		return false
	end

	local slot_15_0 = arg_15_1.endPos - arg_15_0.pos2D
	local slot_15_1 = player.pos2D - arg_15_0.pos2D
	local slot_15_2 = slot_15_0:norm()
	local slot_15_3 = slot_15_1:norm()

	if slot_15_2:dot(slot_15_3) > 0.99 and player.pos2D:distSqr(arg_15_0.pos2D) < arg_15_0.pos2D:distSqr(arg_15_1.endPos) then
		return true
	end

	return false
end

local function ove_0_62(arg_16_0, arg_16_1, arg_16_2, arg_16_3)
	-- print 16
	local slot_16_0 = arg_16_3.buff[ove_0_30.Thresh_Q_Buff]

	if slot_16_0 then
		if slot_16_0.startTime - game.time < -0.15 then
			local slot_16_1 = slot_16_0.endTime - game.time
			local slot_16_2 = arg_16_2.startPos:dist(arg_16_2.endPos)
			local slot_16_3 = arg_16_1.speed == math.huge and 0 or mathf.clamp(0, arg_16_1.range, slot_16_2) / arg_16_1.speed
			local slot_16_4 = ove_0_55(slot_16_2)

			if slot_16_2 > arg_16_1.range then
				slot_16_4 = ove_0_55(slot_16_2) - (slot_16_2 - arg_16_1.range)
			end

			if slot_16_1 + slot_16_4 / arg_16_3.moveSpeed > network.latency + arg_16_1.delay + slot_16_3 then
				return true
			end
		end

		return false
	end

	local slot_16_5 = -0.033 - network.latency * 0.5

	for iter_16_0 = 1, #arg_16_0 do
		local slot_16_6 = arg_16_3.buff[arg_16_0[iter_16_0]]

		if slot_16_6 and slot_16_5 > slot_16_6.startTime - game.time then
			local slot_16_7 = slot_16_6.endTime - game.time
			local slot_16_8 = arg_16_2.startPos:dist(arg_16_2.endPos)
			local slot_16_9 = arg_16_1.speed == math.huge and 0 or mathf.clamp(0, arg_16_1.range, slot_16_8) / arg_16_1.speed
			local slot_16_10 = ove_0_55(slot_16_8)

			if slot_16_8 > arg_16_1.range then
				slot_16_10 = ove_0_55(slot_16_8) - (slot_16_8 - arg_16_1.range)
			end

			if slot_16_7 + slot_16_10 / arg_16_3.moveSpeed > network.latency + arg_16_1.delay + slot_16_9 then
				return true
			end
		end
	end

	return false
end

local function ove_0_63(arg_17_0, arg_17_1)
	-- print 17
	for iter_17_0 = 1, #arg_17_1 do
		if arg_17_0.buff[arg_17_1[iter_17_0]] then
			return true
		end
	end

	return false
end

local ove_0_64 = {}
local ove_0_65 = 0
local ove_0_66 = 0
local ove_0_67 = 0
local ove_0_68 = {}
local ove_0_69 = false
local ove_0_70 = 0
local ove_0_71 = 0
local ove_0_72 = 0
local ove_0_73 = 0
local ove_0_74 = 0
local ove_0_75 = 0

local function ove_0_76(arg_18_0)
	-- print 18
	local slot_18_0 = arg_18_0.owner
	local slot_18_1 = arg_18_0.target

	if slot_18_0 and slot_18_0.ptr == player.ptr then
		if arg_18_0.name == ove_0_31.SummonerFlash_Slot.slot_name then
			ove_0_65 = game.time + 1.5
			ove_0_75 = ove_0_74
		end

		if arg_18_0.name == "VexR2" then
			ove_0_67 = game.time + 6
			ove_0_71 = 0
			ove_0_72 = 0
			ove_0_65 = 1.5
			ove_0_73 = game.time + 0.25
			ove_0_57 = false
		end

		if arg_18_0.name == "VexR" then
			ove_0_57 = false
			ove_0_72 = ove_0_71

			if player.buff.vexrresettimer then
				ove_0_70 = game.time + 15
			end
		end

		if arg_18_0.name == "VexE" then
			ove_0_68.start_pos = vec3(arg_18_0.startPos.x, arg_18_0.startPos.y, arg_18_0.startPos.z)
			ove_0_68.end_pos = vec3(arg_18_0.endPos.x, arg_18_0.endPos.y, arg_18_0.endPos.z)

			local slot_18_2 = mathf.clamp(0, 800, ove_0_68.start_pos:to2D():dist(ove_0_68.end_pos:to2D()))
			local slot_18_3 = ove_0_20.InvLerp(0, 800, slot_18_2)
			local slot_18_4 = ove_0_20.Lerp(200, 300, slot_18_3)
			local slot_18_5 = ove_0_20.Lerp(0.25, 0.95, slot_18_3)

			ove_0_68.distance = slot_18_2
			ove_0_68.radius = slot_18_4
			ove_0_68.end_time = game.time + slot_18_5
			ove_0_68.start_time = game.time

			if ove_0_69 then
				ove_0_68.color = graphics.argb(255, 0, 230, 150)
			else
				ove_0_68.color = graphics.argb(255, 150, 0, 255)
			end
		end
	end

	if slot_18_0 and slot_18_0.type == TYPE_HERO and slot_18_0.team == TEAM_ENEMY and arg_18_0.windUpTime then
		if slot_18_0.charName == "Ezreal" and arg_18_0.slot and arg_18_0.slot == ove_0_31.Ezreal_E_Slot.slot_id then
			ove_0_64[slot_18_0.ptr] = game.time + 0.35
		end

		if slot_18_0.charName == "Tristana" and arg_18_0.slot and arg_18_0.slot == ove_0_31.Tristana_W_Slot.slot_id then
			ove_0_64[slot_18_0.ptr] = game.time + 0.35
		end

		if slot_18_0.charName == "Khazix" and arg_18_0.slot and arg_18_0.slot == ove_0_31.Khazix_E_Slot.slot_id then
			ove_0_64[slot_18_0.ptr] = game.time + 0.35
		end
	end
end

local ove_0_77 = {
	50,
	75,
	100,
	125,
	150
}
local ove_0_78 = 0.75

local function ove_0_79()
	-- print 19
	if ove_0_41.slot.level <= 0 then
		return 0
	end

	return ove_0_77[ove_0_41.slot.level] + ove_0_20.GetTotalAP(player) * ove_0_78
end

local ove_0_80 = {
	0.4,
	0.4,
	0.4,
	0.4,
	0.4,
	0.45,
	0.45,
	0.45,
	0.5,
	0.5,
	0.5,
	0.5,
	0.55,
	0.55,
	0.55,
	0.6,
	0.6,
	0.6
}
local ove_0_81 = 0.25

local function ove_0_82(arg_20_0)
	-- print 20
	local slot_20_0 = 0
	local slot_20_1 = mathf.clamp(1, 18, player.levelRef)
	local slot_20_2 = 30 + 6.470588235294118 * (slot_20_1 - 1) + ove_0_20.GetTotalAP() * ove_0_81

	if arg_20_0.type ~= TYPE_HERO then
		slot_20_2 = slot_20_2 * ove_0_80[slot_20_1]
	end

	return ove_0_20.CalculateMagicDamage(arg_20_0, slot_20_2, player) * ove_0_29.BaronBuffedMinionReductionMod(arg_20_0)
end

local ove_0_83 = {
	60,
	105,
	150,
	195,
	240
}
local ove_0_84 = 0.7

local function ove_0_85(arg_21_0)
	-- print 21
	local slot_21_0 = 0
	local slot_21_1 = ove_0_40.slot.level

	if slot_21_1 > 0 then
		slot_21_0 = ove_0_20.CalculateMagicDamage(arg_21_0, ove_0_83[slot_21_1] + ove_0_20.GetTotalAP() * ove_0_84, player)
		slot_21_0 = slot_21_0 * ove_0_29.BaronBuffedMinionReductionMod(arg_21_0)
	end

	return slot_21_0
end

local ove_0_86 = {
	80,
	120,
	160,
	200,
	240
}
local ove_0_87 = 0.3

local function ove_0_88(arg_22_0)
	-- print 22
	local slot_22_0 = 0
	local slot_22_1 = ove_0_41.slot.level

	if slot_22_1 > 0 then
		slot_22_0 = ove_0_20.CalculateMagicDamage(arg_22_0, ove_0_86[slot_22_1] + ove_0_20.GetTotalAP() * ove_0_87, player)
		slot_22_0 = slot_22_0 * ove_0_29.BaronBuffedMinionReductionMod(arg_22_0)
	end

	return slot_22_0
end

local ove_0_89 = {
	50,
	70,
	90,
	110,
	130
}
local ove_0_90 = {
	0.4,
	0.45,
	0.5,
	0.55,
	0.6
}

local function ove_0_91(arg_23_0)
	-- print 23
	local slot_23_0 = 0
	local slot_23_1 = ove_0_43.slot.level

	if slot_23_1 > 0 then
		slot_23_0 = ove_0_20.CalculateMagicDamage(arg_23_0, ove_0_89[slot_23_1] + ove_0_20.GetTotalAP() * ove_0_90[slot_23_1], player)
		slot_23_0 = slot_23_0 * ove_0_29.BaronBuffedMinionReductionMod(arg_23_0)
	end

	return slot_23_0
end

local ove_0_92 = {
	75,
	125,
	175
}
local ove_0_93 = 0.2

local function ove_0_94(arg_24_0)
	-- print 24
	local slot_24_0 = 0
	local slot_24_1 = ove_0_45.slot.level

	if slot_24_1 > 0 then
		local slot_24_2 = ove_0_92[slot_24_1] + ove_0_20.GetTotalAP() * ove_0_93
		local slot_24_3 = ove_0_29.DFUNC_Calculate_Rune_Damage_Amplifications(arg_24_0, slot_24_2, 1)

		slot_24_0 = ove_0_20.CalculateMagicDamage(arg_24_0, slot_24_3, player)
		slot_24_0 = slot_24_0 * ove_0_29.BaronBuffedMinionReductionMod(arg_24_0)
	end

	return slot_24_0
end

local ove_0_95 = {
	150,
	250,
	350
}
local ove_0_96 = 0.5

local function ove_0_97(arg_25_0)
	-- print 25
	local slot_25_0 = 0
	local slot_25_1 = ove_0_45.slot.level

	if slot_25_1 > 0 then
		local slot_25_2 = ove_0_95[slot_25_1] + ove_0_20.GetTotalAP() * ove_0_96
		local slot_25_3 = ove_0_29.DFUNC_Calculate_Rune_Damage_Amplifications(arg_25_0, slot_25_2, 1)

		slot_25_0 = ove_0_20.CalculateMagicDamage(arg_25_0, slot_25_3, player)
	end

	return slot_25_0
end

local function ove_0_98(arg_26_0, arg_26_1, arg_26_2)
	-- print 26
	if ove_0_21.keys.keys_combo:get() then
		if ove_0_21.combo.q_filter:get() == 4 then
			local slot_26_0 = arg_26_1.moveSpeed * (arg_26_0.delay + arg_26_2:length() / arg_26_0.speed)
			local slot_26_1 = mathf.clamp(0, 250, slot_26_0)
			local slot_26_2 = ove_0_20.VectorExtend(arg_26_1.path.serverPos2D, arg_26_2.startPos, -slot_26_1)

			if arg_26_1.path.isActive and ove_0_20.IsRunningFromPlayerSeg(arg_26_1, arg_26_2, -0.9) then
				slot_26_2 = arg_26_2.endPos
			end

			if arg_26_2.startPos:distSqr(slot_26_2) <= 1440000 then
				return true
			end

			return false
		else
			return true
		end
	else
		return true
	end
end

local function ove_0_99(arg_27_0, arg_27_1, arg_27_2)
	-- print 27
	if not arg_27_1.path.isActive then
		return false
	end

	local slot_27_0 = arg_27_2.endPos - arg_27_1.pos2D
	local slot_27_1 = player.pos2D - arg_27_1.pos2D
	local slot_27_2 = slot_27_0:norm()
	local slot_27_3 = slot_27_1:norm()
	local slot_27_4 = slot_27_2:dot(slot_27_3)

	if slot_27_4 >= 0.875 or slot_27_4 <= -0.875 then
		if ove_0_23.trace.newpath(arg_27_1, 0.033, 100) and ove_0_98(arg_27_0, arg_27_1, arg_27_2) then
			if slot_27_4 >= 0.94 or slot_27_4 <= -0.94 then
				if arg_27_1.pos2D:distSqr(arg_27_1.path.point2D[arg_27_1.path.index]) >= 62500 then
					return true
				end
			else
				return true
			end
		end
	elseif ove_0_20.GetSlowPercent(arg_27_1) >= 20 and ove_0_23.trace.newpath(arg_27_1, 0.033, 0.5) then
		local slot_27_5 = arg_27_2:length() / arg_27_0.speed

		for iter_27_0 = 1, #ove_0_20.movespeed_debuffs do
			local slot_27_6 = arg_27_1.buff[ove_0_20.movespeed_debuffs[iter_27_0]]

			if slot_27_6 and slot_27_6.endTime - game.time >= arg_27_0.delay + slot_27_5 then
				return true
			end
		end
	end

	return false
end

local function ove_0_100(arg_28_0, arg_28_1, arg_28_2)
	-- print 28
	local slot_28_0 = 122500
	local slot_28_1 = 360000
	local slot_28_2 = 640000
	local slot_28_3 = arg_28_1:length() / arg_28_0.speed
	local slot_28_4 = ove_0_64[arg_28_2.ptr]

	if slot_28_4 and slot_28_4 >= game.time then
		return false
	end

	if arg_28_2.path.isActive and arg_28_2.pos2D:distSqr(arg_28_2.path.point2D[arg_28_2.path.index]) <= 10000 then
		local slot_28_5 = false

		for iter_28_0 = 1, #ove_0_20.movespeed_debuffs do
			local slot_28_6 = arg_28_2.buff[ove_0_20.movespeed_debuffs[iter_28_0]]

			if slot_28_6 and slot_28_6.endTime - game.time >= arg_28_0.delay + slot_28_3 then
				slot_28_5 = true
			end
		end

		if not slot_28_5 then
			return false
		end
	end

	if ove_0_20.WillHitDasherLinearCustom(arg_28_2, arg_28_1, arg_28_0, arg_28_0.delay + slot_28_3) then
		return true
	end

	if ove_0_65 > game.time then
		return true
	end

	if ove_0_20.IsRecalling(arg_28_2) then
		return true
	end

	if ove_0_99(arg_28_0, arg_28_2, arg_28_1) then
		return true
	end

	if ove_0_20.IsHardLockedLinearAPI(arg_28_0, arg_28_1, arg_28_2) then
		return true
	end

	local slot_28_7 = player.pos2D:distSqr(arg_28_2.pos2D)

	if slot_28_7 <= slot_28_0 then
		return true
	end

	if ove_0_23.trace.newpath(arg_28_2, 0.033, 1) then
		if arg_28_2.pos2D:distSqr(arg_28_2.path.point2D[arg_28_2.path.index]) >= 722500 then
			return true
		end

		if slot_28_7 <= slot_28_1 then
			return true
		end

		if slot_28_7 <= slot_28_2 then
			if arg_28_2.path.isActive then
				if arg_28_2.pos2D:distSqr(arg_28_2.path.point2D[arg_28_2.path.index]) >= 62500 then
					return true
				end
			else
				return true
			end
		end
	end
end

local function ove_0_101(arg_29_0, arg_29_1, arg_29_2)
	-- print 29
	local slot_29_0 = 122500
	local slot_29_1 = 360000
	local slot_29_2 = 640000
	local slot_29_3 = arg_29_1:length() / arg_29_0.speed
	local slot_29_4 = ove_0_64[arg_29_2.ptr]

	if slot_29_4 and slot_29_4 >= game.time then
		return false
	end

	if arg_29_2.path.isActive and arg_29_2.pos2D:distSqr(arg_29_2.path.point2D[arg_29_2.path.index]) <= 10000 then
		local slot_29_5 = false

		for iter_29_0 = 1, #ove_0_20.movespeed_debuffs do
			local slot_29_6 = arg_29_2.buff[ove_0_20.movespeed_debuffs[iter_29_0]]

			if slot_29_6 and slot_29_6.endTime - game.time >= arg_29_0.delay + slot_29_3 then
				slot_29_5 = true
			end
		end

		if not slot_29_5 then
			return false
		end
	end

	if ove_0_20.WillHitDasherLinearCustom(arg_29_2, arg_29_1, arg_29_0, arg_29_0.delay + slot_29_3) then
		return true
	end

	if ove_0_65 > game.time then
		return true
	end

	if ove_0_20.IsRecalling(arg_29_2) then
		return true
	end

	if ove_0_20.IsHardLockedLinearAPI(arg_29_0, arg_29_1, arg_29_2) then
		return true
	end

	local slot_29_7 = player.pos2D:distSqr(arg_29_2.pos2D)

	if slot_29_7 <= slot_29_0 then
		return true
	end

	if ove_0_23.trace.newpath(arg_29_2, 0.033, 1) then
		if arg_29_2.pos2D:distSqr(arg_29_2.path.point2D[arg_29_2.path.index]) >= 722500 then
			return true
		end

		if slot_29_7 <= slot_29_1 then
			return true
		end

		if arg_29_2.path.isActive then
			if arg_29_2.pos2D:distSqr(arg_29_2.path.point2D[arg_29_2.path.index]) >= 62500 then
				return true
			end
		else
			return true
		end
	end
end

local function ove_0_102(arg_30_0, arg_30_1, arg_30_2)
	-- print 30
	local slot_30_0 = ove_0_43.range * ove_0_43.range

	if ove_0_20.IsRecalling(arg_30_2) and arg_30_1.startPos:distSqr(arg_30_1.endPos) < 1177225 then
		return true
	end

	if slot_30_0 < arg_30_1.startPos:distSqr(arg_30_1.endPos) then
		return false
	end

	if ove_0_65 > game.time then
		return true
	end

	if player.pos2D:distSqr(arg_30_2.pos2D) <= 360000 then
		return true
	end

	if ove_0_20.IsHardLockedCircularAPI(arg_30_0, arg_30_1, arg_30_2) then
		return true
	end

	if ove_0_23.trace.newpath(arg_30_2, 0.033, 1) then
		return true
	end
end

local function ove_0_103(arg_31_0, arg_31_1, arg_31_2)
	-- print 31
	if arg_31_2 > ((arg_31_1.isVisible and arg_31_1.path.isActive and ove_0_23.trace.newpath(arg_31_1, 0.033, 100) and arg_31_1.pos2D:distSqr(arg_31_1.path.point2D[arg_31_1.path.index]) >= 810000 or arg_31_1.path.isDashing) and 2500 or 1500) then
		return false
	end

	local slot_31_0 = ove_0_23.linear.get_prediction(ove_0_40, arg_31_1)

	if not slot_31_0 then
		return false
	end

	if not arg_31_1.path.isDashing and ove_0_61(arg_31_1, slot_31_0) then
		slot_31_0.endPos = vec2(arg_31_1.pos2D.x, arg_31_1.pos2D.y)
	end

	if slot_31_0.startPos:distSqr(slot_31_0.endPos) > ove_0_40.range * ove_0_40.range then
		return false
	end

	if ove_0_21.combo.q_targeting:get() == 1 then
		local slot_31_1 = ove_0_21.combo.q_filter:get()

		if slot_31_1 >= 3 and not ove_0_100(ove_0_40, slot_31_0, arg_31_1) then
			return false
		end

		if slot_31_1 == 2 and not ove_0_101(ove_0_40, slot_31_0, arg_31_1) then
			return false
		end
	end

	if ove_0_23.collision.get_prediction(ove_0_40, slot_31_0, arg_31_1) then
		return false
	end

	if ove_0_20.IsBehindWall(slot_31_0.startPos, arg_31_1.path.serverPos2D, ove_0_40.wall_width) then
		return false
	end

	if ove_0_20.IsBehindWall(slot_31_0.startPos, slot_31_0.endPos, ove_0_40.wall_width) then
		return false
	end

	arg_31_0.segment = slot_31_0
	arg_31_0.pos = slot_31_0.endPos
	arg_31_0.obj = arg_31_1

	return true
end

local function ove_0_104(arg_32_0, arg_32_1, arg_32_2)
	-- print 32
	if arg_32_2 > ((arg_32_1.isVisible and arg_32_1.path.isActive and ove_0_23.trace.newpath(arg_32_1, 0.033, 100) and arg_32_1.pos2D:distSqr(arg_32_1.path.point2D[arg_32_1.path.index]) >= 810000 or arg_32_1.path.isDashing) and 2500 or 1500) then
		return false
	end

	local slot_32_0 = ove_0_23.linear.get_prediction(ove_0_40, arg_32_1)

	if not slot_32_0 then
		return false
	end

	if not arg_32_1.path.isDashing and ove_0_61(arg_32_1, slot_32_0) then
		slot_32_0.endPos = vec2(arg_32_1.pos2D.x, arg_32_1.pos2D.y)
	end

	if slot_32_0.startPos:distSqr(slot_32_0.endPos) > ove_0_40.range * ove_0_40.range then
		return false
	end

	if ove_0_21.harass.q_targeting:get() == 1 then
		local slot_32_1 = ove_0_21.harass.q_filter:get()

		if slot_32_1 == 3 and not ove_0_100(ove_0_40, slot_32_0, arg_32_1) then
			return false
		end

		if slot_32_1 == 2 and not ove_0_101(ove_0_40, slot_32_0, arg_32_1) then
			return false
		end
	end

	if ove_0_23.collision.get_prediction(ove_0_40, slot_32_0, arg_32_1) then
		return false
	end

	if ove_0_20.IsBehindWall(slot_32_0.startPos, arg_32_1.path.serverPos2D, ove_0_40.wall_width) then
		return false
	end

	if ove_0_20.IsBehindWall(slot_32_0.startPos, slot_32_0.endPos, ove_0_40.wall_width) then
		return false
	end

	arg_32_0.segment = slot_32_0
	arg_32_0.pos = slot_32_0.endPos
	arg_32_0.obj = arg_32_1

	return true
end

local function ove_0_105(arg_33_0, arg_33_1, arg_33_2)
	-- print 33
	if arg_33_2 > (arg_33_1.path.isDashing and ove_0_41.range_dash or ove_0_41.range) then
		return false
	end

	local slot_33_0 = ove_0_23.core.get_pos_after_time(arg_33_1, ove_0_41.delay)

	if not slot_33_0 then
		return false
	end

	local slot_33_1 = ove_0_23.present.get_source_pos(player)

	if not slot_33_1 then
		return
	end

	local slot_33_2 = arg_33_1.path.isDashing and ove_0_41.range_dash or ove_0_41.range

	if slot_33_1:distSqr(slot_33_0) > slot_33_2 * slot_33_2 then
		return false
	end

	arg_33_0.obj = arg_33_1

	return true
end

local function ove_0_106(arg_34_0, arg_34_1, arg_34_2)
	-- print 34
	if arg_34_2 > ((arg_34_1.isVisible and arg_34_1.path.isActive and ove_0_23.trace.newpath(arg_34_1, 0.033, 100) and arg_34_1.pos2D:distSqr(arg_34_1.path.point2D[arg_34_1.path.index]) >= 810000 or arg_34_1.path.isDashing) and 2000 or 1500) then
		return false
	end

	local slot_34_0 = ove_0_60(arg_34_1)

	if not slot_34_0 then
		return false
	end

	if ove_0_21.combo.e_filter:get() == 2 then
		if not ove_0_102(ove_0_43, slot_34_0, arg_34_1) then
			return false
		end
	else
		local slot_34_1 = ove_0_43.range * ove_0_43.range

		if ove_0_20.IsRecalling(arg_34_1) and slot_34_0.startPos:distSqr(slot_34_0.endPos) < 1177225 then
			return true
		end

		if slot_34_1 < slot_34_0.startPos:distSqr(slot_34_0.endPos) then
			return false
		end
	end

	if ove_0_20.IsBehindWall(slot_34_0.startPos, slot_34_0.endPos, ove_0_43.wall_width) then
		return false
	end

	arg_34_0.pos = slot_34_0.endPos
	arg_34_0.obj = arg_34_1

	return true
end

local function ove_0_107(arg_35_0, arg_35_1, arg_35_2)
	-- print 35
	if arg_35_2 > ((arg_35_1.isVisible and arg_35_1.path.isActive and ove_0_23.trace.newpath(arg_35_1, 0.033, 100) and arg_35_1.pos2D:distSqr(arg_35_1.path.point2D[arg_35_1.path.index]) >= 810000 or arg_35_1.path.isDashing) and 2000 or 1500) then
		return false
	end

	local slot_35_0 = ove_0_60(arg_35_1)

	if not slot_35_0 then
		return false
	end

	if ove_0_21.harass.e_filter:get() == 2 then
		if not ove_0_102(ove_0_43, slot_35_0, arg_35_1) then
			return false
		end
	else
		local slot_35_1 = ove_0_43.range * ove_0_43.range

		if ove_0_20.IsRecalling(arg_35_1) and slot_35_0.startPos:distSqr(slot_35_0.endPos) < 1177225 then
			return true
		end

		if slot_35_1 < slot_35_0.startPos:distSqr(slot_35_0.endPos) then
			return false
		end
	end

	if ove_0_20.IsBehindWall(slot_35_0.startPos, slot_35_0.endPos, ove_0_43.wall_width) then
		return false
	end

	arg_35_0.pos = slot_35_0.endPos
	arg_35_0.obj = arg_35_1

	return true
end

local function ove_0_108()
	-- print 36
	if not ove_0_21.combo.q_enable:get() then
		return
	end

	if ove_0_40.slot.state ~= 0 then
		return
	end

	if ove_0_21.combo.q_mana:get() > 0 and ove_0_20.GetPercentMana(player) <= ove_0_21.combo.q_mana:get() then
		return
	end

	local slot_36_0 = ove_0_24.get_result(ove_0_103)

	if slot_36_0.segment and slot_36_0.pos and slot_36_0.obj then
		if ove_0_21.combo.q_targeting:get() == 2 then
			local slot_36_1 = ove_0_21.combo.q_filter:get()

			if slot_36_1 >= 3 and not ove_0_100(ove_0_40, slot_36_0.segment, slot_36_0.obj) then
				return false
			end

			if slot_36_1 == 2 and not ove_0_101(ove_0_40, slot_36_0.segment, slot_36_0.obj) then
				return false
			end
		end

		player:castSpell("pos", _Q, slot_36_0.pos:to3D(slot_36_0.obj.pos.y))
	end
end

local function ove_0_109()
	-- print 37
	if not ove_0_21.combo.w_enable:get() then
		return
	end

	if ove_0_41.slot.state ~= 0 then
		return
	end

	if ove_0_21.combo.w_mana:get() > 0 and ove_0_20.GetPercentMana(player) <= ove_0_21.combo.w_mana:get() then
		return
	end

	if ove_0_72 > game.time and ove_0_21.rset.qwe_hold:get() then
		return
	end

	if ove_0_24.get_result(ove_0_105).obj then
		player:castSpell("pos", _W, mousePos)
	end
end

local function ove_0_110()
	-- print 38
	if not ove_0_21.combo.e_enable:get() then
		return
	end

	if ove_0_43.slot.state ~= 0 then
		return
	end

	if ove_0_21.combo.e_mana:get() > 0 and ove_0_20.GetPercentMana(player) <= ove_0_21.combo.e_mana:get() then
		return
	end

	local slot_38_0 = ove_0_24.get_result(ove_0_106)

	if slot_38_0.pos and slot_38_0.obj then
		player:castSpell("pos", _E, slot_38_0.pos:to3D(slot_38_0.obj.pos.y))
	end
end

local ove_0_111 = 0

local function ove_0_112()
	-- print 39
	if ove_0_72 > game.time and ove_0_21.rset.qwe_hold:get() then
		return
	end

	local slot_39_0 = ove_0_21.combo.prio:get()
	local slot_39_1 = ove_0_21.combo.prio_p:get()
	local slot_39_2 = ove_0_21.combo.prio_strict:get()

	if ove_0_21.combo.prio_r2:get() and ove_0_73 > game.time then
		ove_0_111 = game.time + 1
	end

	if ove_0_111 >= game.time then
		ove_0_108()
		ove_0_110()

		return
	end

	if slot_39_0 == 1 and slot_39_1 == 1 then
		if not slot_39_2 then
			ove_0_108()
			ove_0_110()
		elseif ove_0_40.slot.state == 0 then
			ove_0_108()
		else
			ove_0_110()
		end

		return
	end

	if slot_39_0 == 2 and slot_39_1 == 2 then
		if not slot_39_2 then
			ove_0_110()
			ove_0_108()
		elseif ove_0_43.slot.state == 0 then
			ove_0_110()
		else
			ove_0_108()
		end

		return
	end

	if player.buff.vexpdoom then
		if slot_39_1 == 1 then
			if not slot_39_2 then
				ove_0_108()
				ove_0_110()
			elseif ove_0_40.slot.state == 0 then
				ove_0_108()
			else
				ove_0_110()
			end
		elseif not slot_39_2 then
			ove_0_110()
			ove_0_108()
		elseif ove_0_43.slot.state == 0 then
			ove_0_110()
		else
			ove_0_108()
		end
	elseif slot_39_0 == 1 then
		if not slot_39_2 then
			ove_0_108()
			ove_0_110()
		elseif ove_0_40.slot.state == 0 then
			ove_0_108()
		else
			ove_0_110()
		end
	elseif not slot_39_2 then
		ove_0_110()
		ove_0_108()
	elseif ove_0_43.slot.state == 0 then
		ove_0_110()
	else
		ove_0_108()
	end
end

local function ove_0_113()
	-- print 40
	if not ove_0_21.harass.q_enable:get() then
		return
	end

	if ove_0_40.slot.state ~= 0 then
		return
	end

	if ove_0_21.harass.q_mana:get() > 0 and ove_0_20.GetPercentMana(player) <= ove_0_21.harass.q_mana:get() then
		return
	end

	local slot_40_0 = ove_0_24.get_result(ove_0_104)

	if slot_40_0.segment and slot_40_0.pos and slot_40_0.obj then
		if ove_0_21.harass.q_targeting:get() == 2 then
			local slot_40_1 = ove_0_21.harass.q_filter:get()

			if slot_40_1 >= 3 and not ove_0_100(ove_0_40, slot_40_0.segment, slot_40_0.obj) then
				return false
			end

			if slot_40_1 == 2 and not ove_0_101(ove_0_40, slot_40_0.segment, slot_40_0.obj) then
				return false
			end
		end

		player:castSpell("pos", _Q, slot_40_0.pos:to3D(slot_40_0.obj.pos.y))
	end
end

local function ove_0_114()
	-- print 41
	if not ove_0_21.harass.w_enable:get() then
		return
	end

	if ove_0_41.slot.state ~= 0 then
		return
	end

	if ove_0_21.harass.w_mana:get() > 0 and ove_0_20.GetPercentMana(player) <= ove_0_21.harass.w_mana:get() then
		return
	end

	if ove_0_72 > game.time and ove_0_21.rset.qwe_hold:get() then
		return
	end

	if ove_0_24.get_result(ove_0_105).obj then
		player:castSpell("pos", _W, mousePos)
	end
end

local function ove_0_115()
	-- print 42
	if not ove_0_21.harass.e_enable:get() then
		return
	end

	if ove_0_43.slot.state ~= 0 then
		return
	end

	if ove_0_21.harass.e_mana:get() > 0 and ove_0_20.GetPercentMana(player) <= ove_0_21.harass.e_mana:get() then
		return
	end

	local slot_42_0 = ove_0_24.get_result(ove_0_107)

	if slot_42_0.pos and slot_42_0.obj then
		player:castSpell("pos", _E, slot_42_0.pos:to3D(slot_42_0.obj.pos.y))
	end
end

local function ove_0_116()
	-- print 43
	if ove_0_72 > game.time and ove_0_21.rset.qwe_hold:get() then
		return
	end

	local slot_43_0 = ove_0_21.harass.prio:get()
	local slot_43_1 = ove_0_21.harass.prio_p:get()
	local slot_43_2 = ove_0_21.harass.prio_strict:get()

	if slot_43_0 == 1 and slot_43_1 == 1 then
		if not slot_43_2 then
			ove_0_113()
			ove_0_115()
		elseif ove_0_40.slot.state == 0 then
			ove_0_113()
		else
			ove_0_115()
		end

		return
	end

	if slot_43_0 == 2 and slot_43_1 == 2 then
		if not slot_43_2 then
			ove_0_115()
			ove_0_113()
		elseif ove_0_43.slot.state == 0 then
			ove_0_115()
		else
			ove_0_113()
		end

		return
	end

	if player.buff.vexpdoom then
		if slot_43_1 == 1 then
			if not slot_43_2 then
				ove_0_113()
				ove_0_115()
			elseif ove_0_40.slot.state == 0 then
				ove_0_113()
			else
				ove_0_115()
			end
		elseif not slot_43_2 then
			ove_0_115()
			ove_0_113()
		elseif ove_0_43.slot.state == 0 then
			ove_0_115()
		else
			ove_0_113()
		end
	elseif slot_43_0 == 1 then
		if not slot_43_2 then
			ove_0_113()
			ove_0_115()
		elseif ove_0_40.slot.state == 0 then
			ove_0_113()
		else
			ove_0_115()
		end
	elseif not slot_43_2 then
		ove_0_115()
		ove_0_113()
	elseif ove_0_43.slot.state == 0 then
		ove_0_115()
	else
		ove_0_113()
	end
end

local function ove_0_117(arg_44_0, arg_44_1, arg_44_2)
	-- print 44
	local slot_44_0
	local slot_44_1
	local slot_44_2 = ove_0_21.farm.jung.aggro:get()
	local slot_44_3 = ove_0_23.present.get_source_pos(player)

	if not slot_44_3 then
		return nil
	end

	for iter_44_0 = 0, objManager.minions.size[arg_44_0] - 1 do
		local slot_44_4 = objManager.minions[arg_44_0][iter_44_0]

		if ove_0_20.valid_minion_in_range(slot_44_4, player.pos2D, arg_44_2) and ove_0_20.IsMobAggroed(slot_44_4, slot_44_2) then
			if slot_44_1 == nil then
				slot_44_1 = slot_44_4
			elseif #ove_0_20.count_minions_in_range(slot_44_4.pos2D, 200, arg_44_0) >= #ove_0_20.count_minions_in_range(slot_44_1.pos2D, 200, arg_44_0) then
				slot_44_1 = slot_44_4
			end
		end
	end

	local slot_44_5 = {}
	local slot_44_6 = 0
	local slot_44_7 = arg_44_2 * arg_44_2

	for iter_44_1 = 0, objManager.minions.size[arg_44_0] - 1 do
		local slot_44_8 = objManager.minions[arg_44_0][iter_44_1]

		if ove_0_20.valid_minion(slot_44_8) and slot_44_7 >= slot_44_8.pos2D:distSqr(player.pos2D) and ove_0_20.IsMobAggroed(slot_44_8, slot_44_2) and slot_44_1 then
			local slot_44_9 = ove_0_56(slot_44_1.path.serverPos2D:dist(slot_44_3)) * 2

			if slot_44_9 * slot_44_9 >= slot_44_1.pos2D:distSqr(slot_44_8.pos2D) then
				arg_44_1.radius = 10

				local slot_44_10 = ove_0_23.circular.get_prediction(arg_44_1, slot_44_8)

				if slot_44_10 then
					slot_44_5[slot_44_6] = slot_44_10.endPos
					slot_44_6 = slot_44_6 + 1
				end
			end
		end
	end

	local slot_44_11, slot_44_12 = ove_0_20.CustomMEC(slot_44_5, slot_44_6)

	if slot_44_11 then
		local slot_44_13 = {}
		local slot_44_14 = 0

		for iter_44_2 = 0, objManager.minions.size[arg_44_0] - 1 do
			local slot_44_15 = objManager.minions[arg_44_0][iter_44_2]

			if ove_0_20.valid_minion(slot_44_15) and slot_44_7 >= slot_44_15.pos2D:distSqr(player.pos2D) and ove_0_20.IsMobAggroed(slot_44_15, slot_44_2) and slot_44_11 then
				local slot_44_16 = ove_0_56(slot_44_11:dist(slot_44_3)) * 2

				if slot_44_16 * slot_44_16 >= slot_44_11:distSqr(slot_44_15.pos2D) then
					arg_44_1.radius = 10

					local slot_44_17 = ove_0_23.circular.get_prediction(arg_44_1, slot_44_15)

					if slot_44_17 then
						slot_44_13[slot_44_14] = slot_44_17.endPos
						slot_44_14 = slot_44_14 + 1
					end
				end
			end
		end

		local slot_44_18, slot_44_19 = ove_0_20.CustomMEC(slot_44_13, slot_44_14)

		if slot_44_18 then
			return slot_44_18
		end
	end

	return slot_44_0
end

local function ove_0_118(arg_45_0, arg_45_1, arg_45_2, arg_45_3)
	-- print 45
	local slot_45_0 = 0
	local slot_45_1 = arg_45_1.range * arg_45_1.range

	for iter_45_0 = 1, #arg_45_0 do
		local slot_45_2 = arg_45_0[iter_45_0]

		if slot_45_2 then
			local slot_45_3 = ove_0_23.linear.get_prediction(arg_45_1, slot_45_2, arg_45_2)

			if slot_45_3 and slot_45_1 >= slot_45_3.startPos:distSqr(slot_45_3.endPos) then
				local slot_45_4 = arg_45_1.boundingRadiusMod == 1 and slot_45_2.boundingRadius or 0

				if ove_0_20.IsOnLineSegment(slot_45_3.endPos, slot_45_3.startPos, arg_45_3, arg_45_1.width + slot_45_4, arg_45_1.range) then
					slot_45_0 = slot_45_0 + 1
				end
			end
		end
	end

	return slot_45_0
end

local function ove_0_119(arg_46_0, arg_46_1, arg_46_2, arg_46_3)
	-- print 46
	local slot_46_0 = 0
	local slot_46_1
	local slot_46_2 = {}
	local slot_46_3 = arg_46_1.range * arg_46_1.range
	local slot_46_4 = {}
	local slot_46_5 = ove_0_21.farm.jung.aggro:get()

	for iter_46_0 = 0, objManager.minions.size[arg_46_0] - 1 do
		local slot_46_6 = objManager.minions[arg_46_0][iter_46_0]

		if ove_0_20.valid_minion_in_range(slot_46_6, arg_46_2.pos2D, arg_46_3) and ove_0_20.IsMobAggroed(slot_46_6, slot_46_5) then
			slot_46_4[#slot_46_4 + 1] = slot_46_6
		end
	end

	for iter_46_1 = 1, #slot_46_4 do
		local slot_46_7 = slot_46_4[iter_46_1]
		local slot_46_8 = slot_46_4[iter_46_1 + 1]

		if slot_46_7 and slot_46_8 then
			if slot_46_7.ptr ~= slot_46_8.ptr then
				local slot_46_9 = ove_0_23.linear.get_prediction(arg_46_1, slot_46_7, arg_46_2)
				local slot_46_10 = ove_0_23.linear.get_prediction(arg_46_1, slot_46_8, arg_46_2)

				if slot_46_9 and slot_46_10 and slot_46_3 >= slot_46_9.startPos:distSqr(slot_46_9.endPos) and slot_46_3 >= slot_46_10.startPos:distSqr(slot_46_10.endPos) then
					local slot_46_11 = (slot_46_9.endPos + slot_46_10.endPos) * 0.5

					slot_46_2[#slot_46_2 + 1] = slot_46_11
				end
			end
		elseif slot_46_7 and not slot_46_8 then
			local slot_46_12 = ove_0_23.linear.get_prediction(arg_46_1, slot_46_7, arg_46_2)

			if slot_46_12 and slot_46_3 >= slot_46_12.startPos:distSqr(slot_46_12.endPos) then
				slot_46_2[#slot_46_2 + 1] = slot_46_12.endPos
			end
		end
	end

	if #slot_46_2 <= 0 then
		return slot_46_1, slot_46_0
	end

	for iter_46_2 = 1, #slot_46_2 do
		local slot_46_13 = slot_46_2[iter_46_2]

		if slot_46_13 then
			local slot_46_14 = ove_0_118(slot_46_4, arg_46_1, arg_46_2, slot_46_13)

			if slot_46_14 >= 1 then
				if slot_46_0 == 0 then
					slot_46_0 = slot_46_14
					slot_46_1 = slot_46_13
				elseif slot_46_0 < slot_46_14 then
					slot_46_0 = slot_46_14
					slot_46_1 = slot_46_13
				end
			end
		end
	end

	return slot_46_1, slot_46_0
end

local function ove_0_120(arg_47_0, arg_47_1, arg_47_2)
	-- print 47
	local slot_47_0
	local slot_47_1 = 0
	local slot_47_2 = {}
	local slot_47_3 = 0
	local slot_47_4 = {}
	local slot_47_5 = arg_47_1.range * arg_47_1.range
	local slot_47_6 = ove_0_21.farm.jung.aggro:get()

	for iter_47_0 = 0, objManager.minions.size[arg_47_0] - 1 do
		local slot_47_7 = objManager.minions[arg_47_0][iter_47_0]

		if ove_0_20.valid_minion(slot_47_7) and ove_0_20.IsMobAggroed(slot_47_7, slot_47_6) and slot_47_5 >= arg_47_2.pos2D:distSqr(slot_47_7.pos2D) then
			slot_47_4[#slot_47_4 + 1] = slot_47_7

			local slot_47_8 = ove_0_23.linear.get_prediction(arg_47_1, slot_47_7, arg_47_2)

			if slot_47_8 and slot_47_5 >= slot_47_8.startPos:distSqr(slot_47_8.endPos) then
				slot_47_2[slot_47_3] = slot_47_8.endPos
				slot_47_3 = slot_47_3 + 1
			end
		end
	end

	local slot_47_9, slot_47_10 = ove_0_20.CustomMEC(slot_47_2, slot_47_3)

	if slot_47_9 then
		local slot_47_11 = #ove_0_20.count_minions_in_range(player.pos2D, arg_47_1.range, arg_47_0)
		local slot_47_12 = ove_0_118(slot_47_4, arg_47_1, arg_47_2, slot_47_9)

		if slot_47_11 <= slot_47_12 then
			slot_47_0 = slot_47_9
			slot_47_1 = slot_47_12
		end
	end

	return slot_47_0, slot_47_1
end

local function ove_0_121()
	-- print 48
	if not ove_0_21.farm.lane.e_enable:get() then
		return
	end

	if ove_0_20.GetPercentMana(player) <= ove_0_21.farm.lane.e.e_mana:get() then
		return
	end

	if not ove_0_20.IsSpellSlotReady(_E, true) then
		return
	end

	if ove_0_22.core.is_active() then
		return
	end

	if ove_0_21.farm.lane.e.e_dis_pass:get() and ove_0_69 then
		return
	end

	if ove_0_21.farm.lane.e.e_dis_near:get() and #ove_0_20.count_enemies_in_range(player.pos2D, 1700) >= 1 then
		return
	end

	if ove_0_21.farm.lane.e.e_dis_move:get() and #ove_0_20.count_moving_minions_in_range(player.pos2D, 1600, TEAM_ENEMY) > ove_0_21.farm.lane.e.e_dis_move_allow:get() then
		return
	end

	local slot_48_0 = ove_0_23.present.get_source_pos(player)

	if not slot_48_0 then
		return
	end

	local slot_48_1 = ove_0_117(TEAM_ENEMY, ove_0_43, 1500)
	local slot_48_2 = ove_0_21.farm.lane.e.e_count:get()
	local slot_48_3 = ove_0_43.range * ove_0_43.range

	if slot_48_1 and slot_48_3 >= slot_48_1:distSqr(player.pos2D) then
		if ove_0_20.IsBehindWall(player.pos2D, slot_48_1, ove_0_43.wall_width) then
			return
		end

		if slot_48_2 > #ove_0_20.count_minions_in_range(slot_48_1, ove_0_56(slot_48_1:dist(slot_48_0)), TEAM_ENEMY) then
			return
		end

		player:castSpell("pos", _E, slot_48_1:to3D(player.pos.y))
	end
end

local function ove_0_122()
	-- print 49
	if not ove_0_21.farm.lane.w_enable:get() then
		return
	end

	if ove_0_20.GetPercentMana(player) <= ove_0_21.farm.lane.w.w_mana:get() then
		return
	end

	if not ove_0_20.IsSpellSlotReady(_W, true) then
		return
	end

	if ove_0_22.core.is_active() then
		return
	end

	if ove_0_21.farm.lane.w.w_dis_pass:get() and ove_0_69 then
		return
	end

	if ove_0_21.farm.lane.w.w_dis_near:get() and #ove_0_20.count_enemies_in_range(player.pos2D, 1700) >= 1 then
		return
	end

	local slot_49_0 = 0
	local slot_49_1 = (ove_0_41.range - 15) * (ove_0_41.range - 15)

	for iter_49_0 = 0, objManager.minions.size[TEAM_ENEMY] - 1 do
		local slot_49_2 = objManager.minions[TEAM_ENEMY][iter_49_0]

		if slot_49_2 and slot_49_1 >= player.pos2D:distSqr(slot_49_2.pos2D) and ove_0_20.valid_minion(slot_49_2) then
			local slot_49_3 = slot_49_2.buff.vexpgloom

			if (slot_49_3 and slot_49_3.endTime - game.time > 0.25 + network.latency and ove_0_88(slot_49_2) + ove_0_82(slot_49_2) or ove_0_88(slot_49_2)) >= slot_49_2.health then
				slot_49_0 = slot_49_0 + 1
			end
		end
	end

	if slot_49_0 >= ove_0_21.farm.lane.w.w_count:get() then
		player:castSpell("pos", _W, mousePos)
	end
end

local function ove_0_123()
	-- print 50
	if not ove_0_21.farm.lane.q_enable:get() then
		return
	end

	if ove_0_20.GetPercentMana(player) <= ove_0_21.farm.lane.q.q_mana:get() then
		return
	end

	if not ove_0_20.IsSpellSlotReady(_Q, true) then
		return
	end

	if ove_0_22.core.is_active() then
		return
	end

	if ove_0_21.farm.lane.q.q_dis_pass:get() and ove_0_69 then
		return
	end

	if ove_0_21.farm.lane.q.q_dis_near:get() and #ove_0_20.count_enemies_in_range(player.pos2D, 1700) >= 1 then
		return
	end

	if ove_0_21.farm.lane.q.q_dis_move:get() and #ove_0_20.count_moving_minions_in_range(player.pos2D, 1300, TEAM_ENEMY) > ove_0_21.farm.lane.q.q_dis_move_allow:get() then
		return
	end

	if ove_0_21.farm.lane.q.q_count_max:get() then
		local slot_50_0, slot_50_1 = ove_0_120(TEAM_ENEMY, ove_0_40, player)

		if slot_50_0 and slot_50_1 >= ove_0_21.farm.lane.q.q_count:get() then
			if ove_0_20.IsBehindWall(player.pos2D, slot_50_0, ove_0_40.wall_width) then
				return
			end

			player:castSpell("pos", _Q, slot_50_0:to3D(player.pos.y))
		end
	else
		local slot_50_2, slot_50_3 = ove_0_119(TEAM_ENEMY, ove_0_40, player, 1200)

		if slot_50_2 and slot_50_3 >= ove_0_21.farm.lane.q.q_count:get() then
			if ove_0_20.IsBehindWall(player.pos2D, slot_50_2, ove_0_40.wall_width) then
				return
			end

			player:castSpell("pos", _Q, slot_50_2:to3D(player.pos.y))
		end
	end
end

local function ove_0_124()
	-- print 51
	if not ove_0_21.farm.panic.e_enable:get() then
		return
	end

	if ove_0_20.GetPercentMana(player) <= ove_0_21.farm.panic.e.e_mana:get() then
		return
	end

	if not ove_0_20.IsSpellSlotReady(_E, true) then
		return
	end

	if ove_0_22.core.is_active() then
		return
	end

	if ove_0_21.farm.panic.e.e_dis_pass:get() and ove_0_69 then
		return
	end

	if ove_0_21.farm.panic.e.e_dis_near:get() and #ove_0_20.count_enemies_in_range(player.pos2D, 1700) >= 1 then
		return
	end

	if ove_0_21.farm.panic.e.e_dis_move:get() and #ove_0_20.count_moving_minions_in_range(player.pos2D, 1600, TEAM_ENEMY) > ove_0_21.farm.panic.e.e_dis_move_allow:get() then
		return
	end

	local slot_51_0 = ove_0_23.present.get_source_pos(player)

	if not slot_51_0 then
		return
	end

	local slot_51_1 = ove_0_117(TEAM_ENEMY, ove_0_43, 1500)
	local slot_51_2 = ove_0_21.farm.panic.e.e_count:get()
	local slot_51_3 = ove_0_43.range * ove_0_43.range

	if slot_51_1 and slot_51_3 >= slot_51_1:distSqr(player.pos2D) then
		if ove_0_20.IsBehindWall(player.pos2D, slot_51_1, ove_0_43.wall_width) then
			return
		end

		if slot_51_2 > #ove_0_20.count_minions_in_range(slot_51_1, ove_0_56(slot_51_1:dist(slot_51_0)), TEAM_ENEMY) then
			return
		end

		player:castSpell("pos", _E, slot_51_1:to3D(player.pos.y))
	end
end

local function ove_0_125()
	-- print 52
	if not ove_0_21.farm.panic.w_enable:get() then
		return
	end

	if ove_0_20.GetPercentMana(player) <= ove_0_21.farm.panic.w.w_mana:get() then
		return
	end

	if not ove_0_20.IsSpellSlotReady(_W, true) then
		return
	end

	if ove_0_22.core.is_active() then
		return
	end

	if ove_0_21.farm.panic.w.w_dis_pass:get() and ove_0_69 then
		return
	end

	if ove_0_21.farm.panic.w.w_dis_near:get() and #ove_0_20.count_enemies_in_range(player.pos2D, 1700) >= 1 then
		return
	end

	local slot_52_0 = 0
	local slot_52_1 = (ove_0_41.range - 15) * (ove_0_41.range - 15)

	for iter_52_0 = 0, objManager.minions.size[TEAM_ENEMY] - 1 do
		local slot_52_2 = objManager.minions[TEAM_ENEMY][iter_52_0]

		if slot_52_2 and slot_52_1 >= player.pos2D:distSqr(slot_52_2.pos2D) and ove_0_20.valid_minion(slot_52_2) then
			local slot_52_3 = slot_52_2.buff.vexpgloom

			if (slot_52_3 and slot_52_3.endTime - game.time > 0.25 + network.latency and ove_0_88(slot_52_2) + ove_0_82(slot_52_2) or ove_0_88(slot_52_2)) >= slot_52_2.health then
				slot_52_0 = slot_52_0 + 1
			end
		end
	end

	if slot_52_0 >= ove_0_21.farm.panic.w.w_count:get() then
		player:castSpell("pos", _W, mousePos)
	end
end

local function ove_0_126()
	-- print 53
	if not ove_0_21.farm.panic.q_enable:get() then
		return
	end

	if ove_0_20.GetPercentMana(player) <= ove_0_21.farm.panic.q.q_mana:get() then
		return
	end

	if not ove_0_20.IsSpellSlotReady(_Q, true) then
		return
	end

	if ove_0_22.core.is_active() then
		return
	end

	if ove_0_21.farm.panic.q.q_dis_pass:get() and ove_0_69 then
		return
	end

	if ove_0_21.farm.panic.q.q_dis_near:get() and #ove_0_20.count_enemies_in_range(player.pos2D, 1700) >= 1 then
		return
	end

	if ove_0_21.farm.panic.q.q_dis_move:get() and #ove_0_20.count_moving_minions_in_range(player.pos2D, 1300, TEAM_ENEMY) > ove_0_21.farm.panic.q.q_dis_move_allow:get() then
		return
	end

	if ove_0_21.farm.panic.q.q_count_max:get() then
		local slot_53_0, slot_53_1 = ove_0_120(TEAM_ENEMY, ove_0_40, player)

		if slot_53_0 and slot_53_1 >= ove_0_21.farm.panic.q.q_count:get() then
			if ove_0_20.IsBehindWall(player.pos2D, slot_53_0, ove_0_40.wall_width) then
				return
			end

			player:castSpell("pos", _Q, slot_53_0:to3D(player.pos.y))
		end
	else
		local slot_53_2, slot_53_3 = ove_0_119(TEAM_ENEMY, ove_0_40, player, 1200)

		if slot_53_2 and slot_53_3 >= ove_0_21.farm.panic.q.q_count:get() then
			if ove_0_20.IsBehindWall(player.pos2D, slot_53_2, ove_0_40.wall_width) then
				return
			end

			player:castSpell("pos", _Q, slot_53_2:to3D(player.pos.y))
		end
	end
end

local function ove_0_127()
	-- print 54
	if not ove_0_21.farm.jung.q_enable:get() then
		return
	end

	if ove_0_20.GetPercentMana(player) <= ove_0_21.farm.jung.q.q_mana:get() then
		return
	end

	if not ove_0_20.IsSpellSlotReady(_Q, true) then
		return
	end

	if ove_0_22.core.is_active() then
		return
	end

	if ove_0_21.farm.jung.q.q_dis_pass:get() and ove_0_69 then
		return
	end

	if ove_0_21.farm.jung.q.q_dis_near:get() and #ove_0_20.count_enemies_in_range(player.pos2D, 1700) >= 1 then
		return
	end

	if ove_0_21.farm.jung.q.q_count_max:get() then
		local slot_54_0, slot_54_1 = ove_0_120(TEAM_NEUTRAL, ove_0_40, player)

		if slot_54_0 then
			if ove_0_20.IsBehindWall(player.pos2D, slot_54_0, ove_0_40.wall_width) then
				return
			end

			player:castSpell("pos", _Q, slot_54_0:to3D(player.pos.y))
		end
	else
		local slot_54_2, slot_54_3 = ove_0_119(TEAM_NEUTRAL, ove_0_40, player, 1200)

		if slot_54_2 then
			if ove_0_20.IsBehindWall(player.pos2D, slot_54_2, ove_0_40.wall_width) then
				return
			end

			player:castSpell("pos", _Q, slot_54_2:to3D(player.pos.y))
		end
	end
end

local function ove_0_128()
	-- print 55
	if not ove_0_21.farm.jung.w_enable:get() then
		return
	end

	if ove_0_20.GetPercentMana(player) <= ove_0_21.farm.jung.w.w_mana:get() then
		return
	end

	if not ove_0_20.IsSpellSlotReady(_W, true) then
		return
	end

	if ove_0_22.core.is_active() then
		return
	end

	if ove_0_21.farm.jung.w.w_dis_pass:get() and ove_0_69 then
		return
	end

	if ove_0_21.farm.jung.w.w_dis_near:get() and #ove_0_20.count_enemies_in_range(player.pos2D, 1700) >= 1 then
		return
	end

	local slot_55_0 = 0
	local slot_55_1 = ove_0_21.farm.jung.aggro:get()
	local slot_55_2 = (ove_0_41.range - 15) * (ove_0_41.range - 15)

	for iter_55_0 = 0, objManager.minions.size[TEAM_NEUTRAL] - 1 do
		local slot_55_3 = objManager.minions[TEAM_NEUTRAL][iter_55_0]

		if slot_55_3 and slot_55_2 >= player.pos2D:distSqr(slot_55_3.pos2D) and ove_0_20.valid_minion(slot_55_3) and ove_0_20.IsMobAggroed(slot_55_3, slot_55_1) then
			player:castSpell("pos", _W, mousePos)

			break
		end
	end
end

local function ove_0_129()
	-- print 56
	if not ove_0_21.farm.jung.e_enable:get() then
		return
	end

	if ove_0_20.GetPercentMana(player) <= ove_0_21.farm.jung.e.e_mana:get() then
		return
	end

	if not ove_0_20.IsSpellSlotReady(_E, true) then
		return
	end

	if ove_0_22.core.is_active() then
		return
	end

	if ove_0_21.farm.jung.e.e_dis_pass:get() and ove_0_69 then
		return
	end

	if ove_0_21.farm.jung.e.e_dis_near:get() and #ove_0_20.count_enemies_in_range(player.pos2D, 1700) >= 1 then
		return
	end

	local slot_56_0 = ove_0_117(TEAM_NEUTRAL, ove_0_43, 1500)
	local slot_56_1 = ove_0_43.range * ove_0_43.range

	if slot_56_0 and slot_56_1 >= slot_56_0:distSqr(player.pos2D) then
		if ove_0_20.IsBehindWall(player.pos2D, slot_56_0, ove_0_43.wall_width) then
			return
		end

		player:castSpell("pos", _E, slot_56_0:to3D(player.pos.y))
	end
end

function ove_0_43.damage(arg_57_0)
	-- print 57
	return ove_0_91(arg_57_0)
end

function ove_0_42.damage(arg_58_0)
	-- print 58
	local slot_58_0 = arg_58_0.buff.vexpgloom

	return slot_58_0 and slot_58_0.endTime - game.time > 0.25 + network.latency and ove_0_88(arg_58_0) + ove_0_82(arg_58_0) or ove_0_88(arg_58_0)
end

function ove_0_40.damage(arg_59_0)
	-- print 59
	local slot_59_0 = arg_59_0.buff.vexpgloom

	return slot_59_0 and slot_59_0.endTime - game.time > 0.25 + network.latency and ove_0_85(arg_59_0) + ove_0_82(arg_59_0) or ove_0_85(arg_59_0)
end

function ove_0_39.damage(arg_60_0)
	-- print 60
	local slot_60_0 = arg_60_0.buff.vexpgloom

	return slot_60_0 and slot_60_0.endTime - game.time > 0.25 + network.latency and ove_0_85(arg_60_0) + ove_0_82(arg_60_0) or ove_0_85(arg_60_0)
end

local ove_0_130 = 0
local ove_0_131 = 0
local ove_0_132 = 0

local function ove_0_133()
	-- print 61
	if not ove_0_21.farm.help.e_enable:get() then
		return
	end

	if ove_0_132 > game.time then
		return
	end

	if ove_0_20.GetPercentMana(player) <= ove_0_21.farm.help.e.e_mana:get() then
		return
	end

	if not ove_0_20.IsSpellSlotReady(_E, true) then
		return
	end

	if ove_0_22.core.is_active() then
		return
	end

	if ove_0_21.farm.help.e.e_dis_pass:get() and ove_0_69 then
		return
	end

	if ove_0_21.farm.help.e.e_dis_near:get() and #ove_0_20.count_enemies_in_range(player.pos2D, 1700) >= 1 then
		return
	end

	local slot_61_0 = ove_0_43.range

	if ove_0_21.farm.help.e.e_aa_range:get() then
		slot_61_0 = player.attackRange + player.boundingRadius + 60
	end

	ove_0_43.radius = 1

	local slot_61_1 = ove_0_25.farm.skill_farm_circular(ove_0_43)

	if not slot_61_1 then
		return
	end

	local slot_61_2 = slot_61_1:length()

	ove_0_43.radius = ove_0_55(slot_61_2)

	local slot_61_3 = ove_0_25.farm.skill_farm_circular(ove_0_43)

	if not slot_61_3 then
		return
	end

	local slot_61_4 = slot_61_3:length()

	ove_0_43.radius = ove_0_55(slot_61_4)

	local slot_61_5, slot_61_6 = ove_0_25.farm.skill_farm_circular(ove_0_43)

	if slot_61_5 and slot_61_6 and slot_61_6.team == TEAM_ENEMY and ove_0_20.valid_minion(slot_61_6) then
		if slot_61_5.startPos:distSqr(slot_61_5.endPos) > slot_61_0 * slot_61_0 then
			return
		end

		if ove_0_20.IsBehindWall(slot_61_5.startPos, slot_61_5.endPos, ove_0_43.wall_width) then
			return
		end

		if ove_0_21.farm.help.e.e_cannon:get() and not slot_61_6.charName:find(ove_0_32.CannonMinion.charName) then
			return
		end

		player:castSpell("pos", _E, slot_61_5.endPos:to3D(slot_61_6.pos.y))

		ove_0_130 = game.time + 1.65
		ove_0_131 = game.time + 1.65
	end
end

local function ove_0_134()
	-- print 62
	if not ove_0_21.farm.help.w_enable:get() then
		return
	end

	if ove_0_131 > game.time then
		return
	end

	if ove_0_20.GetPercentMana(player) <= ove_0_21.farm.help.w.w_mana:get() then
		return
	end

	if not ove_0_20.IsSpellSlotReady(_W, true) then
		return
	end

	if ove_0_22.core.is_active() then
		return
	end

	if ove_0_21.farm.help.w.w_dis_pass:get() and ove_0_69 then
		return
	end

	if ove_0_21.farm.help.w.w_dis_near:get() and #ove_0_20.count_enemies_in_range(player.pos2D, 1700) >= 1 then
		return
	end

	local slot_62_0, slot_62_1 = ove_0_25.farm.skill_farm_circular(ove_0_42)

	if slot_62_0 and slot_62_1 and slot_62_1.team == TEAM_ENEMY and ove_0_20.valid_minion(slot_62_1) then
		if slot_62_0.startPos:distSqr(slot_62_0.endPos) > ove_0_42.range * ove_0_42.range then
			return
		end

		if ove_0_21.farm.help.w.w_cannon:get() and not slot_62_1.charName:find(ove_0_32.CannonMinion.charName) then
			return
		end

		player:castSpell("pos", _W, mousePos)

		ove_0_130 = game.time + 1.65
		ove_0_132 = game.time + 1.65
	end
end

local function ove_0_135()
	-- print 63
	if not ove_0_21.farm.help.q_enable:get() then
		return
	end

	if ove_0_130 > game.time then
		return
	end

	if ove_0_20.GetPercentMana(player) <= ove_0_21.farm.help.q.q_mana:get() then
		return
	end

	if not ove_0_20.IsSpellSlotReady(_Q, true) then
		return
	end

	if ove_0_22.core.is_active() then
		return
	end

	if ove_0_21.farm.help.q.q_dis_pass:get() and ove_0_69 then
		return
	end

	if ove_0_21.farm.help.q.q_dis_near:get() and #ove_0_20.count_enemies_in_range(player.pos2D, 1700) >= 1 then
		return
	end

	local slot_63_0 = ove_0_40.range

	if ove_0_21.farm.help.q.q_aa_range:get() then
		slot_63_0 = player.attackRange + player.boundingRadius + 60
	end

	local slot_63_1, slot_63_2 = ove_0_25.farm.skill_farm_linear(ove_0_40)

	if slot_63_1 and slot_63_2 and slot_63_2.pos2D:distSqr(player.pos2D) > ove_0_39.range * ove_0_39.range and slot_63_2.team == TEAM_ENEMY and ove_0_20.valid_minion(slot_63_2) then
		if slot_63_1.startPos:distSqr(slot_63_1.endPos) > slot_63_0 * slot_63_0 then
			return
		end

		if ove_0_20.IsBehindWall(slot_63_1.startPos, slot_63_1.endPos, ove_0_40.wall_width) then
			return
		end

		if ove_0_21.farm.help.q.q_cannon:get() and not slot_63_2.charName:find(ove_0_32.CannonMinion.charName) then
			return
		end

		player:castSpell("pos", _Q, slot_63_1.endPos:to3D(slot_63_2.pos.y))

		ove_0_131 = game.time + 1.65
		ove_0_132 = game.time + 1.65
	end

	local slot_63_3, slot_63_4 = ove_0_25.farm.skill_farm_linear(ove_0_39)

	if slot_63_3 and slot_63_4 and slot_63_4.pos2D:distSqr(player.pos2D) <= ove_0_39.range * ove_0_39.range and slot_63_4.team == TEAM_ENEMY and ove_0_20.valid_minion(slot_63_4) then
		if slot_63_3.startPos:distSqr(slot_63_3.endPos) > slot_63_0 * slot_63_0 then
			return
		end

		if ove_0_20.IsBehindWall(slot_63_3.startPos, slot_63_3.endPos, ove_0_39.wall_width) then
			return
		end

		if ove_0_21.farm.help.q.q_cannon:get() and not slot_63_4.charName:find(ove_0_32.CannonMinion.charName) then
			return
		end

		player:castSpell("pos", _Q, slot_63_3.endPos:to3D(slot_63_4.pos.y))

		ove_0_131 = game.time + 1.65
		ove_0_132 = game.time + 1.65
	end
end

local function ove_0_136()
	-- print 64
	if not ove_0_21.farm.farm_key:get() then
		return
	end

	if ove_0_72 > game.time and ove_0_21.rset.qwe_hold:get() then
		return
	end

	local slot_64_0 = ove_0_21.farm.panic.panic_method:get() == 1 and ove_0_20.IsOrbPanicClearActive() or ove_0_21.farm.panic.panic_method:get() == 2 and ove_0_21.keys.keys_laneclear:get() and ove_0_21.farm.panic.panic_custom_key:get()
	local slot_64_1 = ove_0_21.keys.keys_laneclear:get()
	local slot_64_2 = ove_0_21.farm.help.help_lane:get() and ove_0_21.keys.keys_laneclear:get() or ove_0_21.farm.help.help_last:get() and ove_0_21.keys.keys_lasthit:get()

	if slot_64_0 then
		ove_0_125()
		ove_0_124()
		ove_0_126()
	else
		if slot_64_2 then
			ove_0_134()
			ove_0_135()
			ove_0_133()
		end

		if slot_64_1 then
			ove_0_122()
			ove_0_121()
			ove_0_123()
		end
	end

	if slot_64_1 then
		ove_0_128()
		ove_0_129()
		ove_0_127()
	end
end

local function ove_0_137()
	-- print 65
	if ove_0_34 ~= 2 then
		return
	end

	if not ove_0_21.misc.w_lethal:get() and not ove_0_21.misc.w_percent:get() and not ove_0_21.misc.w_destroy:get() then
		return
	end

	if ove_0_41.slot.state ~= 0 then
		return
	end

	if ove_0_20.IsRecalling(player) then
		return
	end

	if ove_0_20.IsInvulnerable(player) then
		return
	end

	if ove_0_72 > game.time and ove_0_21.rset.qwe_hold:get() then
		return
	end

	local slot_65_0, slot_65_1, slot_65_2, slot_65_3 = ove_0_22.damage.count(player)
	local slot_65_4 = math.max(0, slot_65_1 - player.magicalShield) + math.max(0, slot_65_0 - player.physicalShield) + slot_65_2 - player.allShield

	if ove_0_21.misc.w_lethal:get() and slot_65_4 >= player.health then
		player:castSpell("pos", _W, mousePos)
	end

	if ove_0_21.misc.w_percent:get() and ove_0_20.GetPercentHealth(player) - math.max(0, player.health - slot_65_4) / player.maxHealth * 100 >= ove_0_21.misc.w_percent_value:get() then
		player:castSpell("pos", _W, mousePos)
	end

	if ove_0_21.misc.w_destroy:get() and slot_65_4 >= ove_0_79() then
		player:castSpell("pos", _W, mousePos)
	end
end

local function ove_0_138(arg_66_0, arg_66_1, arg_66_2)
	-- print 66
	local slot_66_0 = arg_66_1.moveSpeed * (arg_66_0.delay + arg_66_2:length() / arg_66_0.speed)
	local slot_66_1 = mathf.clamp(0, 500, slot_66_0)
	local slot_66_2 = ove_0_20.VectorExtend(arg_66_1.path.serverPos2D, arg_66_2.startPos, -slot_66_1)

	if arg_66_1.path.isActive and ove_0_20.IsRunningFromPlayerSeg(arg_66_1, arg_66_2, -0.9) then
		slot_66_2 = arg_66_2.endPos
	end

	if arg_66_2.startPos:distSqr(slot_66_2) <= ove_0_45.range * ove_0_45.range then
		return true
	end

	return false
end

local function ove_0_139(arg_67_0, arg_67_1, arg_67_2)
	-- print 67
	if not arg_67_1.path.isActive then
		return false
	end

	local slot_67_0 = arg_67_2.endPos - arg_67_1.pos2D
	local slot_67_1 = player.pos2D - arg_67_1.pos2D
	local slot_67_2 = slot_67_0:norm()
	local slot_67_3 = slot_67_1:norm()
	local slot_67_4 = slot_67_2:dot(slot_67_3)

	if slot_67_4 >= 0.875 or slot_67_4 <= -0.875 then
		if ove_0_23.trace.newpath(arg_67_1, 0.033, 100) and ove_0_138(arg_67_0, arg_67_1, arg_67_2) then
			if slot_67_4 >= 0.94 or slot_67_4 <= -0.94 then
				if arg_67_1.pos2D:distSqr(arg_67_1.path.point2D[arg_67_1.path.index]) >= 62500 then
					return true
				end
			else
				return true
			end
		end
	elseif ove_0_20.GetSlowPercent(arg_67_1) >= 20 and ove_0_23.trace.newpath(arg_67_1, 0.033, 0.5) then
		local slot_67_5 = arg_67_2:length() / arg_67_0.speed

		for iter_67_0 = 1, #ove_0_20.movespeed_debuffs do
			local slot_67_6 = arg_67_1.buff[ove_0_20.movespeed_debuffs[iter_67_0]]

			if slot_67_6 and slot_67_6.endTime - game.time >= arg_67_0.delay + slot_67_5 then
				return true
			end
		end
	end

	return false
end

local function ove_0_140(arg_68_0, arg_68_1, arg_68_2)
	-- print 68
	local slot_68_0 = 490000
	local slot_68_1 = arg_68_1:length() / arg_68_0.speed
	local slot_68_2 = ove_0_64[arg_68_2.ptr]

	if slot_68_2 and slot_68_2 >= game.time then
		return false
	end

	if arg_68_2.path.isDashing then
		if ove_0_20.WillHitDasherLinearCustom(arg_68_2, arg_68_1, arg_68_0, arg_68_0.delay + slot_68_1) then
			return true
		end

		return false
	end

	if arg_68_2.path.isActive and arg_68_2.pos2D:distSqr(arg_68_2.path.point2D[arg_68_2.path.index]) <= 10000 then
		local slot_68_3 = false

		for iter_68_0 = 1, #ove_0_20.movespeed_debuffs do
			local slot_68_4 = arg_68_2.buff[ove_0_20.movespeed_debuffs[iter_68_0]]

			if slot_68_4 and slot_68_4.endTime - game.time >= arg_68_0.delay + slot_68_1 then
				slot_68_3 = true
			end
		end

		if not slot_68_3 then
			return false
		end
	end

	if ove_0_65 > game.time then
		return true
	end

	if player.buff.vexrresettimer and player.buff.vexrresettimer.endTime - game.time <= 0.1 + network.latency then
		return true
	end

	if ove_0_20.IsRecalling(arg_68_2) then
		return true
	end

	if ove_0_139(arg_68_0, arg_68_2, arg_68_1) then
		return true
	end

	if ove_0_20.IsHardLockedLinearAPI(arg_68_0, arg_68_1, arg_68_2) then
		return true
	end

	local slot_68_5 = player.pos2D:distSqr(arg_68_2.pos2D)

	if slot_68_5 <= slot_68_0 then
		return true
	end

	if ove_0_23.trace.newpath(arg_68_2, 0.033, 1) then
		if arg_68_2.pos2D:distSqr(arg_68_2.path.point2D[arg_68_2.path.index]) >= 722500 then
			return true
		end

		if slot_68_5 <= slot_68_0 then
			return true
		elseif arg_68_2.path.isActive then
			if arg_68_2.pos2D:distSqr(arg_68_2.path.point2D[arg_68_2.path.index]) >= 62500 then
				return true
			end
		else
			return true
		end
	end
end

local function ove_0_141(arg_69_0, arg_69_1, arg_69_2)
	-- print 69
	local slot_69_0 = 490000
	local slot_69_1 = arg_69_1:length() / arg_69_0.speed
	local slot_69_2 = ove_0_64[arg_69_2.ptr]

	if slot_69_2 and slot_69_2 >= game.time then
		return false
	end

	if arg_69_2.path.isActive and arg_69_2.pos2D:distSqr(arg_69_2.path.point2D[arg_69_2.path.index]) <= 10000 then
		local slot_69_3 = false

		for iter_69_0 = 1, #ove_0_20.movespeed_debuffs do
			local slot_69_4 = arg_69_2.buff[ove_0_20.movespeed_debuffs[iter_69_0]]

			if slot_69_4 and slot_69_4.endTime - game.time >= arg_69_0.delay + slot_69_1 then
				slot_69_3 = true
			end
		end

		if not slot_69_3 then
			return false
		end
	end

	if ove_0_20.WillHitDasherLinearCustom(arg_69_2, arg_69_1, arg_69_0, arg_69_0.delay + slot_69_1) then
		return true
	end

	if ove_0_65 > game.time then
		return true
	end

	if player.buff.vexrresettimer and player.buff.vexrresettimer.endTime - game.time <= 0.1 + network.latency then
		return true
	end

	if ove_0_20.IsRecalling(arg_69_2) then
		return true
	end

	if ove_0_20.IsHardLockedLinearAPI(arg_69_0, arg_69_1, arg_69_2) then
		return true
	end

	local slot_69_5 = player.pos2D:distSqr(arg_69_2.pos2D)

	if slot_69_5 <= slot_69_0 then
		return true
	end

	if ove_0_23.trace.newpath(arg_69_2, 0.033, 1) then
		if arg_69_2.pos2D:distSqr(arg_69_2.path.point2D[arg_69_2.path.index]) >= 722500 then
			return true
		end

		if slot_69_5 <= slot_69_0 then
			return true
		end

		if arg_69_2.path.isActive then
			if arg_69_2.pos2D:distSqr(arg_69_2.path.point2D[arg_69_2.path.index]) >= 62500 then
				return true
			end
		else
			return true
		end
	end
end

local function ove_0_142(arg_70_0, arg_70_1, arg_70_2)
	-- print 70
	if not ove_0_20.IsValidTargetNoBuff(arg_70_1) then
		return false
	end

	if arg_70_1.isZombie then
		return false
	end

	if ove_0_20.IsInvulnerable(arg_70_1) then
		return false
	end

	local slot_70_0 = ove_0_23.linear.get_prediction(ove_0_45, arg_70_1)

	if not slot_70_0 then
		return false
	end

	if slot_70_0.startPos:distSqr(slot_70_0.endPos) > ove_0_45.range * ove_0_45.range then
		return false
	end

	if ove_0_23.collision.get_prediction(ove_0_45, slot_70_0, arg_70_1) then
		return false
	end

	if ove_0_20.IsBehindWall(slot_70_0.startPos, arg_70_1.path.serverPos2D, ove_0_45.wall_width) then
		return false
	end

	if ove_0_20.IsBehindWall(slot_70_0.startPos, slot_70_0.endPos, ove_0_45.wall_width) then
		return false
	end

	arg_70_0.segment = slot_70_0
	arg_70_0.pos = slot_70_0.endPos
	arg_70_0.obj = arg_70_1

	return true
end

local ove_0_143

local function ove_0_144()
	-- print 71
	ove_0_143 = nil

	if not ove_0_21.rset.r_force_key:get() and not ove_0_57 then
		return
	end

	if ove_0_45.slot.state ~= 0 then
		return
	end

	if ove_0_45.slot.name == "VexR" then
		if ove_0_21.rset.r_force_filter:get() == 1 then
			local slot_71_0 = ove_0_24.get_result(ove_0_142)

			if slot_71_0.pos and slot_71_0.obj and slot_71_0.segment then
				ove_0_71 = game.time + slot_71_0.segment:length() / ove_0_45.speed + 0.35

				player:castSpell("pos", _R, slot_71_0.pos:to3D(slot_71_0.obj.pos.y))
			end
		else
			local slot_71_1 = ove_0_24.get_result(ove_0_142)

			if slot_71_1.pos and slot_71_1.obj and slot_71_1.segment then
				ove_0_143 = slot_71_1.obj

				if ove_0_21.rset.r_force_filter:get() == 2 and not ove_0_141(ove_0_45, slot_71_1.segment, slot_71_1.obj) then
					return
				end

				if ove_0_21.rset.r_force_filter:get() == 3 and not ove_0_140(ove_0_45, slot_71_1.segment, slot_71_1.obj) then
					return
				end

				ove_0_71 = game.time + slot_71_1.segment:length() / ove_0_45.speed + 0.35

				player:castSpell("pos", _R, slot_71_1.pos:to3D(slot_71_1.obj.pos.y))
			end
		end
	else
		player:castSpell("pos", _R, mousePos)
	end
end

local function ove_0_145(arg_72_0, arg_72_1)
	-- print 72
	return ove_0_20.CalculatePhysicalDamage(arg_72_0, ove_0_20.GetTotalAD(player), player) * arg_72_1
end

local function ove_0_146(arg_73_0)
	-- print 73
	local slot_73_0 = 0
	local slot_73_1 = true
	local slot_73_2 = 2
	local slot_73_3 = false
	local slot_73_4 = true

	if ove_0_29.DFUNC_Check_Invulnerability(arg_73_0, slot_73_0, false, slot_73_2) then
		return 0
	end

	if ove_0_29.DFUNC_Check_SpellShields(arg_73_0, slot_73_0, false, 0) then
		return 0
	end

	local slot_73_5 = 0
	local slot_73_6 = 0
	local slot_73_7 = 0
	local slot_73_8 = 0
	local slot_73_9 = 0
	local slot_73_10 = 0
	local slot_73_11 = 0
	local slot_73_12 = 0
	local slot_73_13 = player.mana
	local slot_73_14 = ove_0_21.rset.dmg
	local slot_73_15 = slot_73_14.p:get()
	local slot_73_16 = ove_0_145(arg_73_0, slot_73_14.aa:get())

	if slot_73_15 and arg_73_0.buff.vexpgloom then
		slot_73_10 = ove_0_82(arg_73_0)
	end

	if slot_73_13 >= player.manaCost3 and ove_0_45.slot.state == 0 then
		if slot_73_14.r1:get() then
			slot_73_5 = ove_0_94(arg_73_0)
		end

		if slot_73_14.r2:get() then
			slot_73_6 = ove_0_97(arg_73_0)
		end

		slot_73_13 = slot_73_13 - player.manaCost3
	end

	if slot_73_13 >= player.manaCost1 and ove_0_41.slot.state == 0 then
		if slot_73_14.w:get() then
			slot_73_8 = ove_0_88(arg_73_0)
		end

		slot_73_13 = slot_73_13 - player.manaCost1
	end

	if slot_73_13 >= player.manaCost0 and ove_0_40.slot.state == 0 then
		if slot_73_14.q:get() then
			slot_73_7 = ove_0_85(arg_73_0)
		end

		slot_73_13 = slot_73_13 - player.manaCost0
	end

	if slot_73_13 >= player.manaCost2 and (ove_0_43.slot.state == 0 or ove_0_68.end_pos and ove_0_68.end_time >= game.time and arg_73_0.path.serverPos2D:distSqr(ove_0_68.end_pos:to2D()) <= ove_0_68.radius * ove_0_68.radius) then
		if slot_73_14.e:get() then
			slot_73_9 = ove_0_91(arg_73_0)
		end

		if slot_73_15 and not arg_73_0.buff.vexpgloom then
			slot_73_10 = ove_0_82(arg_73_0)
		end

		local slot_73_17 = slot_73_13 - player.manaCost2
	end

	local slot_73_18 = slot_73_10 + slot_73_7 + slot_73_8 + slot_73_9 + slot_73_5 + slot_73_6 + ove_0_29.DarkHarvestDamage(arg_73_0, slot_73_2)
	local slot_73_19 = ove_0_29.DFUNC_Calculate_Exhaust_Effects(player, arg_73_0, slot_73_18)
	local slot_73_20 = ove_0_29.DFUNC_Calculate_Magical_Champion_Damage_Reduction(arg_73_0, slot_73_19, slot_73_0, slot_73_1, slot_73_2, slot_73_3, slot_73_4)

	if arg_73_0.buff[ove_0_30.Rune_BoneplatingBuff] then
		slot_73_20 = slot_73_20 - ove_0_29.BonePlatingShield(arg_73_0)
	end

	if arg_73_0.buff[ove_0_30.Rune_NullifyingOrbBuff] then
		slot_73_20 = slot_73_20 - ove_0_29.NullifyingOrbShield(arg_73_0)
	end

	if ove_0_36.Evelynn and arg_73_0.ptr == ove_0_36.Evelynn_ptr and arg_73_0.passiveCooldownEndTime - game.time <= 2 then
		slot_73_20 = slot_73_20 - ove_0_29.EvelynnPassivePerSecondHeal(arg_73_0) * 2
	end

	if ove_0_36.Blitzcrank and arg_73_0.ptr == ove_0_36.Blitzcrank_ptr and slot_73_2 >= arg_73_0.passiveCooldownEndTime - game.time then
		slot_73_20 = slot_73_20 - ove_0_29.BlitzcrankPassiveShield(arg_73_0)
	end

	for iter_73_0 = 0, 5 do
		if arg_73_0:itemID(iter_73_0) == ove_0_33.Hexdrinker.id and arg_73_0:spellSlot(iter_73_0 + 6).cooldown <= 3 then
			slot_73_20 = slot_73_20 - ove_0_29.HexdrinkerShield(arg_73_0)
		end

		if arg_73_0:itemID(iter_73_0) == ove_0_33.MawOfMalmortius.id and arg_73_0:spellSlot(iter_73_0 + 6).cooldown <= 3 then
			slot_73_20 = slot_73_20 - ove_0_29.MawOfMalmortiusShield(arg_73_0)
		end

		if (arg_73_0:itemID(iter_73_0) == ove_0_33.ImmortalShieldbow.id or arg_73_0:itemID(iter_73_0) == ove_0_33.ImmortalShieldbow2.id) and arg_73_0:spellSlot(iter_73_0 + 6).cooldown <= 3 then
			slot_73_20 = slot_73_20 - ove_0_29.ImmortalShieldbowShield(arg_73_0)
		end
	end

	for iter_73_1 = 4, 5 do
		if arg_73_0:spellSlot(iter_73_1).name == ove_0_31.SummonerHeal_Slot.slot_name and slot_73_2 >= arg_73_0:spellSlot(iter_73_1).cooldown then
			slot_73_20 = slot_73_20 - ove_0_29.SummonerHealHeal(arg_73_0, slot_73_2)
		end

		if arg_73_0:spellSlot(iter_73_1).name == ove_0_31.SummonerBarrier_Slot.slot_name and slot_73_2 >= arg_73_0:spellSlot(iter_73_1).cooldown then
			slot_73_20 = slot_73_20 - ove_0_29.SummonerBarrierShield(arg_73_0)
		end
	end

	if ove_0_29.ElderDragonWillExecute(arg_73_0, math.max(0, slot_73_20), slot_73_2, true, 2) then
		slot_73_20 = 99999999
	end

	return math.max(0, slot_73_20)
end

local function ove_0_147(arg_74_0, arg_74_1)
	-- print 74
	if ove_0_36.Nocturne and arg_74_0.ptr == ove_0_36.Nocturne_ptr then
		local slot_74_0 = arg_74_0:spellSlot(ove_0_31.Nocturne_W_Slot.slot_id)

		if slot_74_0.level >= 1 and arg_74_1 >= slot_74_0.cooldown then
			return true
		end
	end

	if ove_0_36.Sivir and arg_74_0.ptr == ove_0_36.Sivir_ptr then
		local slot_74_1 = arg_74_0:spellSlot(ove_0_31.Sivir_E_Slot.slot_id)

		if slot_74_1.level >= 1 and arg_74_1 >= slot_74_1.cooldown then
			return true
		end
	end

	return false
end

local function ove_0_148(arg_75_0, arg_75_1)
	-- print 75
	if ove_0_36.Yasuo and arg_75_0.ptr == ove_0_36.Yasuo_ptr then
		local slot_75_0 = arg_75_0:spellSlot(ove_0_31.Yasuo_W_Slot.slot_id)

		if slot_75_0.level >= 1 and arg_75_1 >= slot_75_0.cooldown then
			return true
		end
	end

	if ove_0_36.Fiora and arg_75_0.ptr == ove_0_36.Fiora_ptr then
		local slot_75_1 = arg_75_0:spellSlot(ove_0_31.Fiora_W_Slot.slot_id)

		if slot_75_1.level >= 1 and arg_75_1 >= slot_75_1.cooldown then
			return true
		end
	end

	if ove_0_36.Samira and arg_75_0.ptr == ove_0_36.Samira_ptr then
		local slot_75_2 = arg_75_0:spellSlot(ove_0_31.Samira_W_Slot.slot_id)

		if slot_75_2.level >= 1 and arg_75_1 >= slot_75_2.cooldown then
			return true
		end
	end

	if ove_0_36.Gwen and arg_75_0.ptr == ove_0_36.Gwen_ptr then
		local slot_75_3 = arg_75_0:spellSlot(ove_0_31.Gwen_W_Slot.slot_id)

		if slot_75_3.level >= 1 and arg_75_1 >= slot_75_3.cooldown then
			return true
		end
	end

	return false
end

local function ove_0_149(arg_76_0, arg_76_1)
	-- print 76
	if ove_0_36.Kayle and arg_76_0.ptr == ove_0_36.Kayle_ptr then
		local slot_76_0 = arg_76_0:spellSlot(ove_0_31.Kayle_R_Slot.slot_id)

		if slot_76_0.level >= 1 and arg_76_1 >= slot_76_0.cooldown then
			return true
		end
	end

	if ove_0_36.Braum and arg_76_0.ptr == ove_0_36.Braum_ptr then
		local slot_76_1 = arg_76_0:spellSlot(ove_0_31.Braum_E_Slot.slot_id)

		if slot_76_1.level >= 1 and arg_76_1 >= slot_76_1.cooldown then
			return true
		end
	end

	if ove_0_36.Pantheon and arg_76_0.ptr == ove_0_36.Pantheon_ptr then
		local slot_76_2 = arg_76_0:spellSlot(ove_0_31.Pantheon_E_Slot.slot_id)

		if slot_76_2.level >= 1 and arg_76_1 >= slot_76_2.cooldown then
			return true
		end
	end

	if ove_0_36.Xayah and arg_76_0.ptr == ove_0_36.Xayah_ptr then
		local slot_76_3 = arg_76_0:spellSlot(ove_0_31.Xayah_R_Slot.slot_id)

		if slot_76_3.level >= 1 and arg_76_1 >= slot_76_3.cooldown then
			return true
		end
	end

	if ove_0_36.Tryndamere and arg_76_0.ptr == ove_0_36.Tryndamere_ptr then
		local slot_76_4 = arg_76_0:spellSlot(ove_0_31.Tryndamere_R_Slot.slot_id)

		if slot_76_4.level >= 1 and arg_76_1 >= slot_76_4.cooldown then
			return true
		end
	end

	if ove_0_36.Kindred and arg_76_0.ptr == ove_0_36.Kindred_ptr then
		local slot_76_5 = arg_76_0:spellSlot(ove_0_31.Kindred_R_Slot.slot_id)

		if slot_76_5.level >= 1 and arg_76_1 >= slot_76_5.cooldown then
			return true
		end
	end

	if ove_0_36.Lissandra and arg_76_0.ptr == ove_0_36.Lissandra_ptr then
		local slot_76_6 = arg_76_0:spellSlot(ove_0_31.Lissandra_R_Slot.slot_id)

		if slot_76_6.level >= 1 and arg_76_1 >= slot_76_6.cooldown then
			return true
		end
	end

	return false
end

local function ove_0_150(arg_77_0, arg_77_1)
	-- print 77
	if ove_0_36.Zilean and arg_77_0.buff.chronoshift and arg_77_0.buff.chronoshift.endTime - game.time > 0.5 then
		return true
	end

	if ove_0_36.Zilean and arg_77_0.ptr == ove_0_36.Zilean_ptr then
		local slot_77_0 = arg_77_0:spellSlot(ove_0_31.Zilean_R_Slot.slot_id)

		if slot_77_0.level >= 1 and arg_77_1 >= slot_77_0.cooldown then
			return true
		end
	end

	if ove_0_36.Ekko and arg_77_0.ptr == ove_0_36.Ekko_ptr then
		local slot_77_1 = arg_77_0:spellSlot(ove_0_31.Ekko_R_Slot.slot_id)

		if slot_77_1.level >= 1 and arg_77_1 >= slot_77_1.cooldown then
			return true
		end
	end

	if ove_0_36.Anivia and arg_77_0.ptr == ove_0_36.Anivia_ptr and arg_77_1 >= arg_77_0.passiveCooldownEndTime - game.time then
		return true
	end

	if ove_0_36.Zac and arg_77_0.ptr == ove_0_36.Zac_ptr and arg_77_1 >= arg_77_0.passiveCooldownEndTime - game.time then
		return true
	end

	return false
end

local function ove_0_151(arg_78_0, arg_78_1)
	-- print 78
	local slot_78_0 = player.buff.vexrresettimer
	local slot_78_1 = ove_0_21.rset.r1.dont.ga:get() and (not slot_78_0 or slot_78_0 and not ove_0_21.rset.r1.ignore.ga:get())
	local slot_78_2 = ove_0_21.rset.r1.dont.zh:get() and (not slot_78_0 or slot_78_0 and not ove_0_21.rset.r1.ignore.zh:get())
	local slot_78_3 = ove_0_21.rset.r1.dont.dash:get() and (not slot_78_0 or slot_78_0 and not ove_0_21.rset.r1.ignore.dash:get())

	if not slot_78_1 and not slot_78_2 and not slot_78_3 then
		return 0
	end

	for iter_78_0 = 0, 5 do
		local slot_78_4 = arg_78_0:itemID(iter_78_0)
		local slot_78_5 = arg_78_0:spellSlot(iter_78_0 + 6)

		if slot_78_2 then
			if slot_78_4 == ove_0_33.ZhonyasHourglass.id and arg_78_1 >= slot_78_5.cooldown then
				return 4
			end

			if slot_78_4 == ove_0_33.Stopwatch.id then
				return 4
			end

			if slot_78_4 == ove_0_33.PerfectlyTimedStopwatch.id then
				return 4
			end
		end

		if slot_78_3 and (slot_78_4 == ove_0_33.Galeforce.id or slot_78_4 == ove_0_33.Galeforce2.id) and arg_78_1 >= slot_78_5.cooldown then
			return 3
		end

		if slot_78_1 and slot_78_4 == ove_0_33.GuardianAngel.id and arg_78_1 >= slot_78_5.cooldown then
			return 1
		end
	end

	return 0
end

local function ove_0_152(arg_79_0, arg_79_1)
	-- print 79
	local slot_79_0 = arg_79_0:spellSlot(5)
	local slot_79_1 = ove_0_31.SummonerFlash_Slot.slot_name

	if slot_79_0.name == slot_79_1 then
		if arg_79_1 >= slot_79_0.cooldown then
			return true
		end
	else
		local slot_79_2 = arg_79_0:spellSlot(4)

		if slot_79_2.name == slot_79_1 and arg_79_1 >= slot_79_2.cooldown then
			return true
		end
	end

	return false
end

local function ove_0_153(arg_80_0, arg_80_1)
	-- print 80
	if ove_0_20.IsCustomLocked(ove_0_54, arg_80_0, 1) then
		return false
	end

	local slot_80_0 = ove_0_35[arg_80_0.ptr]

	if ove_0_28[slot_80_0] then
		local slot_80_1 = ove_0_28[slot_80_0].slot
		local slot_80_2 = ove_0_28[slot_80_0].slot2

		if arg_80_0:spellSlot(slot_80_1).level >= 1 and arg_80_1 >= arg_80_0:spellSlot(slot_80_1).cooldown then
			return true
		end

		if slot_80_2 and arg_80_0:spellSlot(slot_80_2).level >= 1 and arg_80_1 >= arg_80_0:spellSlot(slot_80_2).cooldown then
			return true
		end
	end

	if ove_0_36.JarvanIV and arg_80_0.ptr == ove_0_36.JarvanIV_ptr and arg_80_0:spellSlot(_Q).level >= 1 and arg_80_0:spellSlot(_E).level >= 1 and arg_80_1 >= arg_80_0:spellSlot(_Q).cooldown and arg_80_1 >= arg_80_0:spellSlot(_E).cooldown then
		return true
	end

	if ove_0_36.MasterYi and arg_80_0.ptr == ove_0_36.MasterYi_ptr and arg_80_0:spellSlot(_Q).level >= 1 and arg_80_1 >= arg_80_0:spellSlot(_Q).cooldown then
		if #ove_0_20.count_minions_in_range(arg_80_0.pos2D, 1000, TEAM_ALLY) >= 1 then
			return true
		end

		if #ove_0_20.count_allies_in_range(arg_80_0.pos2D, 1000) >= 1 then
			return true
		end

		if #ove_0_20.count_minions_in_range(arg_80_0.pos2D, 1000, TEAM_NEUTRAL) >= 1 then
			return true
		end
	end

	if ove_0_36.Rakan and arg_80_0.ptr == ove_0_36.Rakan_ptr then
		if arg_80_0:spellSlot(_W).level >= 1 and arg_80_1 >= arg_80_0:spellSlot(_W).cooldown then
			return true
		end

		if arg_80_0:spellSlot(_E).level >= 1 and arg_80_1 >= arg_80_0:spellSlot(_E).cooldown and #ove_0_20.count_enemies_in_range(arg_80_0.pos2D, 1000) >= 2 then
			return true
		end
	end

	if ove_0_36.MonkeyKing and arg_80_0.ptr == ove_0_36.MonkeyKing_ptr and arg_80_0:spellSlot(_E).level >= 1 and arg_80_1 >= arg_80_0:spellSlot(_E).cooldown then
		if #ove_0_20.count_minions_in_range(arg_80_0.pos2D, 1000, TEAM_ALLY) >= 1 then
			return true
		end

		if #ove_0_20.count_allies_in_range(arg_80_0.pos2D, 1000, true) >= 1 then
			return true
		end

		if #ove_0_20.count_minions_in_range(arg_80_0.pos2D, 1000, TEAM_NEUTRAL) >= 1 then
			return true
		end
	end

	if ove_0_36.Yuumi and arg_80_0.ptr == ove_0_36.Yuumi_ptr and arg_80_0:spellSlot(_W).level >= 1 and arg_80_1 >= arg_80_0:spellSlot(_W).cooldown and #ove_0_20.count_enemies_in_range(arg_80_0.pos2D, 1000) >= 2 then
		return true
	end

	if ove_0_36.Yasuo and arg_80_0.ptr == ove_0_36.Yasuo_ptr and arg_80_0:spellSlot(_E).level >= 1 then
		if #ove_0_20.count_minions_in_range(arg_80_0.pos2D, 1000, TEAM_ALLY) >= 1 then
			return true
		end

		if #ove_0_20.count_allies_in_range(arg_80_0.pos2D, 1000, true) >= 1 then
			return true
		end

		if #ove_0_20.count_minions_in_range(arg_80_0.pos2D, 1000, TEAM_NEUTRAL) >= 1 then
			return true
		end
	end

	if ove_0_36.Yone and arg_80_0.ptr == ove_0_36.Yone_ptr and arg_80_0:spellSlot(_Q).level >= 1 and arg_80_1 >= arg_80_0:spellSlot(_Q).cooldown and arg_80_0.isVisible and not arg_80_0.isDead and arg_80_0.buff.yoneq3ready then
		return true
	end

	return false
end

local function ove_0_154(arg_81_0, arg_81_1)
	-- print 81
	local slot_81_0 = player.buff.vexrresettimer

	if ove_0_21.rset.r1.dont.spellshield:get() and (not slot_81_0 or slot_81_0 and not ove_0_21.rset.r1.ignore.spellshield:get()) and ove_0_147(arg_81_0, arg_81_1) then
		return false, 5
	end

	if ove_0_21.rset.r1.dont.block:get() and (not slot_81_0 or slot_81_0 and not ove_0_21.rset.r1.ignore.block:get()) and ove_0_148(arg_81_0, arg_81_1) then
		return false, 6
	end

	if ove_0_21.rset.r1.dont.invulnerable:get() and (not slot_81_0 or slot_81_0 and not ove_0_21.rset.r1.ignore.invulnerable:get()) and ove_0_149(arg_81_0, arg_81_1) then
		return false, 8
	end

	if ove_0_21.rset.r1.dont.dash:get() and (not slot_81_0 or slot_81_0 and not ove_0_21.rset.r1.ignore.dash:get()) and ove_0_153(arg_81_0, arg_81_1) then
		return false, 3
	end

	if ove_0_21.rset.r1.dont.flash:get() and (not slot_81_0 or slot_81_0 and not ove_0_21.rset.r1.ignore.flash:get()) and ove_0_152(arg_81_0, arg_81_1) then
		return false, 2
	end

	local slot_81_1 = ove_0_151(arg_81_0, arg_81_1)

	if slot_81_1 ~= 0 then
		return false, slot_81_1
	end

	if ove_0_21.rset.r1.dont.revive:get() and (not slot_81_0 or slot_81_0 and not ove_0_21.rset.r1.ignore.revive:get()) and ove_0_150(arg_81_0, arg_81_1) then
		return false, 7
	end

	return true, 0
end

local function ove_0_155(arg_82_0)
	-- print 82
	if ove_0_21.rset.dmg.ckill:get() then
		if ove_0_21.rset.dmg.ckill_allies:get() then
			if #ove_0_20.count_allies_in_range(arg_82_0.pos2D, 1000, true) >= 1 then
				return true
			end
		else
			return true
		end
	end

	return false
end

local function ove_0_156(arg_83_0, arg_83_1, arg_83_2)
	-- print 83
	if not ove_0_20.IsValidTargetNoBuff(arg_83_1) then
		return false
	end

	if arg_83_1.isZombie then
		return false
	end

	if ove_0_21.rset.r_blist[ove_0_35[arg_83_1.ptr]] and ove_0_21.rset.r_blist[ove_0_35[arg_83_1.ptr]]:get() then
		return false
	end

	if ove_0_20.IsInvulnerable(arg_83_1) then
		return false
	end

	local slot_83_0 = arg_83_1.health + arg_83_1.magicalShield + arg_83_1.allShield - ove_0_146(arg_83_1)

	if ove_0_155(arg_83_1) then
		if slot_83_0 / arg_83_1.maxHealth * 100 > ove_0_21.rset.dmg.ckill_hp:get() then
			return false
		end
	elseif slot_83_0 > 0 then
		return false
	end

	local slot_83_1 = ove_0_23.linear.get_prediction(ove_0_45, arg_83_1)

	if not slot_83_1 then
		return false
	end

	if slot_83_1.startPos:distSqr(slot_83_1.endPos) > ove_0_45.range * ove_0_45.range then
		return false
	end

	if ove_0_20.IsBehindWall(slot_83_1.startPos, arg_83_1.path.serverPos2D, ove_0_45.wall_width) then
		return false
	end

	if ove_0_20.IsBehindWall(slot_83_1.startPos, slot_83_1.endPos, ove_0_45.wall_width) then
		return false
	end

	arg_83_0.segment = slot_83_1
	arg_83_0.pos = slot_83_1.endPos
	arg_83_0.obj = arg_83_1

	return true
end

local function ove_0_157(arg_84_0, arg_84_1, arg_84_2)
	-- print 84
	if not ove_0_20.IsValidTargetNoBuff(arg_84_1) then
		return false
	end

	if arg_84_1.isZombie then
		return false
	end

	if ove_0_21.rset.r_blist[ove_0_35[arg_84_1.ptr]] and ove_0_21.rset.r_blist[ove_0_35[arg_84_1.ptr]]:get() then
		return false
	end

	if ove_0_20.IsInvulnerable(arg_84_1) then
		return false
	end

	local slot_84_0 = arg_84_1.health + arg_84_1.magicalShield + arg_84_1.allShield - ove_0_146(arg_84_1)

	if ove_0_155(arg_84_1) then
		if slot_84_0 / arg_84_1.maxHealth * 100 > ove_0_21.rset.dmg.ckill_hp:get() then
			return false
		end
	elseif slot_84_0 > 0 then
		return false
	end

	local slot_84_1 = ove_0_23.linear.get_prediction(ove_0_45, arg_84_1)

	if not slot_84_1 then
		return false
	end

	if slot_84_1.startPos:distSqr(slot_84_1.endPos) > ove_0_45.range * ove_0_45.range then
		return false
	end

	local slot_84_2, slot_84_3 = ove_0_154(arg_84_1, 2)

	if not slot_84_2 then
		return false
	end

	if ove_0_20.IsBehindWall(slot_84_1.startPos, arg_84_1.path.serverPos2D, ove_0_45.wall_width) then
		return false
	end

	if ove_0_20.IsBehindWall(slot_84_1.startPos, slot_84_1.endPos, ove_0_45.wall_width) then
		return false
	end

	arg_84_0.segment = slot_84_1
	arg_84_0.pos = slot_84_1.endPos
	arg_84_0.obj = arg_84_1

	return true
end

local ove_0_158
local ove_0_159 = 0
local ove_0_160 = {}
local ove_0_161 = 0

local function ove_0_162()
	-- print 85
	ove_0_158 = nil
	ove_0_159 = 0

	if ove_0_45.slot.state ~= 0 then
		return
	end

	if not ove_0_21.rset.r_auto_key:get() then
		return
	end

	if not ove_0_21.rset.r1.r_enable:get() then
		return
	end

	if ove_0_20.IsRecalling(player) then
		return
	end

	if ove_0_45.slot.name ~= "VexR" then
		return
	end

	if ove_0_21.rset.r1.r_next:get() and player.buff.vexrresettimer then
		if not ove_0_21.rset.r1.r_next_ls:get() then
			local slot_85_0 = ove_0_24.get_result(ove_0_142)

			if slot_85_0.pos and slot_85_0.obj and slot_85_0.segment then
				ove_0_143 = slot_85_0.obj

				if ove_0_23.collision.get_prediction(ove_0_45, slot_85_0.segment, slot_85_0.obj) then
					return
				end

				if ove_0_21.rset.r_filter:get() == 3 and not ove_0_140(ove_0_45, slot_85_0.segment, slot_85_0.obj) then
					return
				end

				if ove_0_21.rset.r_filter:get() == 2 and not ove_0_141(ove_0_45, slot_85_0.segment, slot_85_0.obj) then
					return
				end

				if ove_0_64[slot_85_0.obj.ptr] and ove_0_64[slot_85_0.obj.ptr] >= game.time then
					return
				end

				ove_0_71 = game.time + slot_85_0.segment:length() / ove_0_45.speed + 0.35

				player:castSpell("pos", _R, slot_85_0.pos:to3D(slot_85_0.obj.pos.y))
			end

			return
		elseif player.buff.vexrresettimer.endTime - game.time <= 1.1 then
			local slot_85_1 = ove_0_24.get_result(ove_0_142)

			if slot_85_1.pos and slot_85_1.obj and slot_85_1.segment then
				ove_0_143 = slot_85_1.obj

				if ove_0_23.collision.get_prediction(ove_0_45, slot_85_1.segment, slot_85_1.obj) then
					return
				end

				if ove_0_21.rset.r_filter:get() == 3 and not ove_0_140(ove_0_45, slot_85_1.segment, slot_85_1.obj) then
					return
				end

				if ove_0_21.rset.r_filter:get() == 2 and not ove_0_141(ove_0_45, slot_85_1.segment, slot_85_1.obj) then
					return
				end

				if ove_0_64[slot_85_1.obj.ptr] and ove_0_64[slot_85_1.obj.ptr] >= game.time then
					return
				end

				ove_0_71 = game.time + slot_85_1.segment:length() / ove_0_45.speed + 0.35

				player:castSpell("pos", _R, slot_85_1.pos:to3D(slot_85_1.obj.pos.y))
			end

			return
		end
	end

	local slot_85_2 = ove_0_24.get_result(ove_0_156)

	if slot_85_2.obj then
		local slot_85_3, slot_85_4 = ove_0_154(slot_85_2.obj, 2)

		if not slot_85_3 then
			local slot_85_5 = ove_0_24.get_result(ove_0_157)

			if slot_85_5.obj then
				ove_0_158 = slot_85_5.obj
				ove_0_159 = 0

				if slot_85_5.pos and slot_85_5.segment then
					if ove_0_23.collision.get_prediction(ove_0_45, slot_85_5.segment, slot_85_5.obj) then
						return
					end

					if ove_0_21.rset.r_filter:get() == 3 and not ove_0_140(ove_0_45, slot_85_5.segment, slot_85_5.obj) then
						return
					end

					if ove_0_21.rset.r_filter:get() == 2 and not ove_0_141(ove_0_45, slot_85_5.segment, slot_85_5.obj) then
						return
					end

					if ove_0_64[slot_85_5.obj.ptr] and ove_0_64[slot_85_5.obj.ptr] >= game.time then
						return
					end

					ove_0_71 = game.time + slot_85_5.segment:length() / ove_0_45.speed + 0.35

					player:castSpell("pos", _R, slot_85_5.pos:to3D(slot_85_5.obj.pos.y))
				end

				return
			end
		end

		ove_0_158 = slot_85_2.obj
		ove_0_159 = slot_85_4

		if slot_85_2.pos and slot_85_2.segment then
			if slot_85_4 ~= 0 then
				local slot_85_6 = slot_85_2.obj

				if not ove_0_160[slot_85_6.ptr] then
					ove_0_160[slot_85_6.ptr] = 0
				end

				if ove_0_21.rset.r_allow_key:get() and os.clock() > ove_0_161 then
					if ove_0_160[slot_85_6.ptr] < game.time then
						ove_0_160[slot_85_6.ptr] = game.time + 12
						ove_0_161 = os.clock() + 0.3
					else
						ove_0_160[slot_85_6.ptr] = 0
						ove_0_161 = os.clock() + 0.3
					end
				end

				if ove_0_160[slot_85_6.ptr] and ove_0_160[slot_85_6.ptr] < game.time then
					return
				end
			end

			if ove_0_23.collision.get_prediction(ove_0_45, slot_85_2.segment, slot_85_2.obj) then
				return
			end

			if ove_0_21.rset.r_filter:get() == 3 and not ove_0_140(ove_0_45, slot_85_2.segment, slot_85_2.obj) then
				return
			end

			if ove_0_21.rset.r_filter:get() == 2 and not ove_0_141(ove_0_45, slot_85_2.segment, slot_85_2.obj) then
				return
			end

			if ove_0_64[slot_85_2.obj.ptr] and ove_0_64[slot_85_2.obj.ptr] >= game.time then
				return
			end

			ove_0_71 = game.time + slot_85_2.segment:length() / ove_0_45.speed + 0.35

			player:castSpell("pos", _R, slot_85_2.pos:to3D(slot_85_2.obj.pos.y))
		end
	end
end

local function ove_0_163(arg_86_0)
	-- print 86
	local slot_86_0 = 0
	local slot_86_1 = true
	local slot_86_2 = 1
	local slot_86_3 = false
	local slot_86_4 = true

	if ove_0_29.DFUNC_Check_Invulnerability(arg_86_0, slot_86_0, slot_86_1, slot_86_2) then
		return 0
	end

	if ove_0_29.DFUNC_Check_SpellShields(arg_86_0, slot_86_0, slot_86_1, slot_86_2) then
		return 0
	end

	local slot_86_5 = ove_0_97(arg_86_0)
	local slot_86_6 = ove_0_29.DFUNC_Calculate_Magical_Champion_Damage_Reduction(arg_86_0, slot_86_5, slot_86_0, slot_86_1, slot_86_2, slot_86_3, slot_86_4)

	return math.max(0, slot_86_6)
end

local function ove_0_164(arg_87_0)
	-- print 87
	local slot_87_0 = 0
	local slot_87_1 = true
	local slot_87_2 = 1
	local slot_87_3 = false
	local slot_87_4 = true

	if ove_0_29.DFUNC_Check_Invulnerability(arg_87_0, slot_87_0, slot_87_1, slot_87_2) then
		return 0
	end

	if ove_0_29.DFUNC_Check_SpellShields(arg_87_0, slot_87_0, slot_87_1, slot_87_2) then
		return 0
	end

	local slot_87_5 = arg_87_0.buff.vexpgloom
	local slot_87_6 = slot_87_5 and slot_87_5.endTime - game.time > 1 + network.latency and ove_0_88(arg_87_0) + ove_0_82(arg_87_0) or ove_0_88(arg_87_0)
	local slot_87_7 = ove_0_29.DFUNC_Calculate_Magical_Champion_Damage_Reduction(arg_87_0, slot_87_6, slot_87_0, slot_87_1, slot_87_2, slot_87_3, slot_87_4)

	return math.max(0, slot_87_7)
end

local ove_0_165 = false

local function ove_0_166()
	-- print 88
	ove_0_165 = false

	if ove_0_45.slot.state ~= 0 then
		return
	end

	if not ove_0_21.rset.r_auto_key:get() then
		return
	end

	if ove_0_20.IsRecalling(player) then
		return
	end

	if ove_0_45.slot.name ~= "VexR2" then
		return
	end

	if ove_0_21.rset.r2.always:get() then
		player:castSpell("pos", _R, mousePos)

		return
	end

	local slot_88_0

	for iter_88_0 = 0, objManager.enemies_n - 1 do
		local slot_88_1 = objManager.enemies[iter_88_0]

		if slot_88_1 and slot_88_1.isVisible then
			local slot_88_2 = slot_88_1.buff.vexrtarget

			if slot_88_2 and slot_88_2.source and slot_88_2.source.ptr == player.ptr then
				slot_88_0 = slot_88_1

				break
			end
		end
	end

	if not slot_88_0 then
		return
	end

	if ove_0_21.rset.r2.kill_r2:get() then
		local slot_88_3 = (ove_0_21.rset.r2.kill_r2_w:get() and ove_0_163(slot_88_0) + ove_0_164(slot_88_0) or ove_0_163(slot_88_0)) + ove_0_29.DarkHarvestDamage(slot_88_0, 2)

		if slot_88_0.buff[ove_0_30.Rune_BoneplatingBuff] then
			slot_88_3 = slot_88_3 - ove_0_29.BonePlatingShield(slot_88_0)
		end

		if slot_88_0.buff[ove_0_30.Rune_NullifyingOrbBuff] then
			slot_88_3 = slot_88_3 - ove_0_29.NullifyingOrbShield(slot_88_0)
		end

		if ove_0_36.Blitzcrank and slot_88_0.ptr == ove_0_36.Blitzcrank_ptr and slot_88_0.passiveCooldownEndTime - game.time <= 2 then
			slot_88_3 = slot_88_3 - ove_0_29.BlitzcrankPassiveShield(slot_88_0)
		end

		for iter_88_1 = 0, 5 do
			if slot_88_0:itemID(iter_88_1) == ove_0_33.Hexdrinker.id and slot_88_0:spellSlot(iter_88_1 + 6).cooldown <= 2 then
				slot_88_3 = slot_88_3 - ove_0_29.HexdrinkerShield(slot_88_0)
			end

			if slot_88_0:itemID(iter_88_1) == ove_0_33.MawOfMalmortius.id and slot_88_0:spellSlot(iter_88_1 + 6).cooldown <= 2 then
				slot_88_3 = slot_88_3 - ove_0_29.MawOfMalmortiusShield(slot_88_0)
			end

			if (slot_88_0:itemID(iter_88_1) == ove_0_33.ImmortalShieldbow.id or slot_88_0:itemID(iter_88_1) == ove_0_33.ImmortalShieldbow2.id) and slot_88_0:spellSlot(iter_88_1 + 6).cooldown <= 2 then
				slot_88_3 = slot_88_3 - ove_0_29.ImmortalShieldbowShield(slot_88_0)
			end
		end

		for iter_88_2 = 4, 5 do
			if slot_88_0:spellSlot(iter_88_2).name == ove_0_31.SummonerHeal_Slot.slot_name and slot_88_0:spellSlot(iter_88_2).cooldown <= 2 then
				slot_88_3 = slot_88_3 - ove_0_29.SummonerHealHeal(slot_88_0, 2)
			end

			if slot_88_0:spellSlot(iter_88_2).name == ove_0_31.SummonerBarrier_Slot.slot_name and slot_88_0:spellSlot(iter_88_2).cooldown <= 2 then
				slot_88_3 = slot_88_3 - ove_0_29.SummonerBarrierShield(slot_88_0)
			end
		end

		if ove_0_29.ElderDragonWillExecute(slot_88_0, math.max(0, slot_88_3), 2, true, 2) then
			slot_88_3 = 99999999
		end

		if slot_88_3 >= slot_88_0.health + slot_88_0.magicalShield + slot_88_0.allShield then
			player:castSpell("pos", _R, mousePos)
		end
	end

	if ove_0_21.rset.r2.dont.hp:get() > 0 and ove_0_20.GetPercentHealth(player) <= ove_0_21.rset.r2.dont.hp:get() then
		return
	end

	if ove_0_21.rset.r2.dont.safe_check:get() and not ove_0_22.core.is_action_safe(slot_88_0.pos2D, 2200, 0) then
		return
	end

	if ove_0_21.rset.r2.dont.w_cooldown:get() and ove_0_41.slot.state ~= 0 then
		return
	end

	if ove_0_21.rset.r2.dont.turret:get() and ove_0_20.is_under_enemy_turret(slot_88_0.pos2D) and not ove_0_20.is_under_enemy_turret(player.pos2D) then
		if ove_0_21.rset.r2.dont.turret_allow:get() then
			if ove_0_41.slot.state ~= 0 then
				ove_0_165 = true

				return
			end
		else
			ove_0_165 = true

			return
		end
	end

	if ove_0_21.rset.r2.dont.fountain:get() and ove_0_53 and slot_88_0.pos2D:distSqr(ove_0_53:to2D()) <= 1690000 then
		return
	end

	if ove_0_21.rset.r2.allow.target_close:get() then
		local slot_88_4 = ove_0_21.rset.r2.allow.target_close_dist:get()

		if player.pos2D:distSqr(slot_88_0.pos2D) <= slot_88_4 * slot_88_4 then
			player:castSpell("pos", _R, mousePos)

			return
		end
	end

	if ove_0_21.rset.r2.allow.reset:get() and ove_0_70 > game.time then
		player:castSpell("pos", _R, mousePos)

		return
	end

	if ove_0_21.rset.r2.allow.allies:get() and #ove_0_20.count_allies_in_range(slot_88_0.pos2D, 1000, true) >= ove_0_21.rset.r2.allow.ally_count:get() then
		player:castSpell("pos", _R, mousePos)

		return
	end

	if ove_0_21.rset.r2.allow.enemies:get() and #ove_0_20.count_enemies_in_range(slot_88_0.pos2D, 1000) >= ove_0_21.rset.r2.allow.enemy_count:get() then
		player:castSpell("pos", _R, mousePos)

		return
	end

	if ove_0_21.rset.r2.allow.kill_combo:get() and slot_88_0.health + slot_88_0.magicalShield + slot_88_0.allShield <= ove_0_146(slot_88_0) then
		player:castSpell("pos", _R, mousePos)

		return
	end
end

local ove_0_167 = false

local function ove_0_168()
	-- print 89
	if player.buff.vexr2timer then
		if not ove_0_167 then
			ove_0_66 = game.time + 6
			ove_0_167 = true
		end
	else
		ove_0_167 = false
	end
end

local function ove_0_169()
	-- print 90
	if ove_0_20.IsRecalling(player) then
		return
	end

	local slot_90_0 = ove_0_21.misc.q_cc:get()
	local slot_90_1 = ove_0_21.misc.e_cc:get()

	if not slot_90_0 and not slot_90_1 then
		return
	end

	local slot_90_2 = ove_0_40.slot.state == 0
	local slot_90_3 = ove_0_43.slot.state == 0
	local slot_90_4 = 4000000

	if slot_90_0 and slot_90_2 then
		local slot_90_5 = ove_0_40.range * ove_0_40.range
		local slot_90_6 = 250000

		for iter_90_0 = 0, objManager.enemies_n - 1 do
			local slot_90_7 = objManager.enemies[iter_90_0]

			if ove_0_20.IsValidTargetNoBuff(slot_90_7) then
				local slot_90_8 = slot_90_7.pos2D:distSqr(player.pos2D)

				if slot_90_8 <= slot_90_4 and ove_0_63(slot_90_7, ove_0_54) then
					if slot_90_6 < slot_90_8 then
						local slot_90_9 = ove_0_23.linear.get_prediction(ove_0_40, slot_90_7)

						if slot_90_9 and ove_0_20.IsCustomLockHitLinear(ove_0_54, ove_0_40, slot_90_9, slot_90_7) and slot_90_5 >= slot_90_9.startPos:distSqr(slot_90_9.endPos) and not ove_0_23.collision.get_prediction(ove_0_40, slot_90_9, slot_90_7) and not ove_0_20.IsBehindWall(slot_90_9.startPos, slot_90_9.endPos, ove_0_40.wall_width) then
							player:castSpell("pos", _Q, slot_90_9.endPos:to3D(slot_90_7.pos.y))
						end
					else
						local slot_90_10 = ove_0_23.linear.get_prediction(ove_0_39, slot_90_7)

						if slot_90_10 and ove_0_20.IsCustomLockHitLinear(ove_0_54, ove_0_39, slot_90_10, slot_90_7) and not ove_0_23.collision.get_prediction(ove_0_39, slot_90_10, slot_90_7) and not ove_0_20.IsBehindWall(slot_90_10.startPos, slot_90_10.endPos, ove_0_39.wall_width) then
							player:castSpell("pos", _Q, slot_90_10.endPos:to3D(slot_90_7.pos.y))
						end
					end
				end
			end
		end
	end

	if slot_90_1 and slot_90_3 then
		local slot_90_11 = ove_0_43.range + ove_0_43.max_radius - 20
		local slot_90_12 = slot_90_11 * slot_90_11
		local slot_90_13 = ove_0_43.range * ove_0_43.range

		for iter_90_1 = 0, objManager.enemies_n - 1 do
			local slot_90_14 = objManager.enemies[iter_90_1]

			if ove_0_20.IsValidTargetNoBuff(slot_90_14) and slot_90_4 >= slot_90_14.pos2D:distSqr(player.pos2D) and ove_0_63(slot_90_14, ove_0_54) then
				local slot_90_15 = ove_0_60(slot_90_14)

				if slot_90_15 then
					if slot_90_13 >= slot_90_15.startPos:distSqr(slot_90_15.endPos) then
						if ove_0_62(ove_0_54, ove_0_43, slot_90_15, slot_90_14) and not ove_0_20.IsBehindWall(slot_90_15.startPos, slot_90_15.endPos, ove_0_43.wall_width) then
							player:castSpell("pos", _E, slot_90_15.endPos:to3D(slot_90_14.pos.y))
						end
					else
						ove_0_43.radius = 1

						local slot_90_16 = ove_0_23.circular.get_prediction(ove_0_43, slot_90_14)

						if slot_90_16 and slot_90_12 >= slot_90_16.startPos:distSqr(slot_90_16.endPos) and ove_0_62(ove_0_54, ove_0_43, slot_90_16, slot_90_14) then
							local slot_90_17 = slot_90_13 < slot_90_16.startPos:distSqr(slot_90_16.endPos) and ove_0_20.VectorExtend(slot_90_16.startPos, slot_90_16.endPos, ove_0_43.range) or slot_90_16.endPos

							if slot_90_17 and not ove_0_20.IsBehindWall(slot_90_16.startPos, slot_90_17, ove_0_43.wall_width) then
								player:castSpell("pos", _E, slot_90_16.endPos:to3D(slot_90_14.pos.y))
							end
						end
					end
				end
			end
		end
	end
end

local function ove_0_170()
	-- print 91
	if ove_0_20.IsRecalling(player) then
		return
	end

	local slot_91_0 = ove_0_21.misc.q_gap:get()
	local slot_91_1 = ove_0_21.misc.w_gap:get()
	local slot_91_2 = ove_0_21.misc.e_gap:get()

	if not slot_91_0 and not slot_91_1 and not slot_91_2 then
		return
	end

	local slot_91_3 = ove_0_40.slot.state == 0
	local slot_91_4 = ove_0_41.slot.state == 0
	local slot_91_5 = ove_0_43.slot.state == 0

	if not slot_91_3 and not slot_91_4 and not slot_91_5 then
		return
	end

	if ove_0_72 > game.time and ove_0_21.rset.qwe_hold:get() then
		return
	end

	local slot_91_6 = ove_0_21.misc.c_gap:get()
	local slot_91_7 = 9000000
	local slot_91_8 = ove_0_39.range * ove_0_39.range
	local slot_91_9 = ove_0_40.range * ove_0_40.range
	local slot_91_10 = ove_0_41.range_dash * ove_0_41.range_dash
	local slot_91_11 = (ove_0_43.range + ove_0_43.max_radius) * (ove_0_43.range + ove_0_43.max_radius)

	for iter_91_0 = 0, objManager.enemies_n - 1 do
		local slot_91_12 = objManager.enemies[iter_91_0]

		if ove_0_20.IsValidTargetNoBuff(slot_91_12) and slot_91_12.path.isDashing and ove_0_21.misc.gap_blist[ove_0_35[slot_91_12.ptr]] and not ove_0_21.misc.gap_blist[ove_0_35[slot_91_12.ptr]]:get() and slot_91_7 >= slot_91_12.pos2D:distSqr(player.pos2D) then
			if slot_91_1 and slot_91_4 then
				local slot_91_13 = ove_0_23.core.get_pos_after_time(slot_91_12, ove_0_41.delay)

				if slot_91_13 then
					local slot_91_14 = ove_0_23.present.get_source_pos(player)

					if slot_91_14 and slot_91_10 > slot_91_14:distSqr(slot_91_13) then
						player:castSpell("pos", _W, mousePos)

						break
					end
				end
			end

			if slot_91_0 and slot_91_3 then
				local slot_91_15 = ove_0_23.linear.get_prediction(ove_0_39, slot_91_12)

				if slot_91_15 and slot_91_8 >= slot_91_15.startPos:distSqr(slot_91_15.endPos) then
					if slot_91_6 then
						if ove_0_20.WillHitDasherLinear(slot_91_12, slot_91_15, ove_0_39) then
							player:castSpell("pos", _Q, slot_91_15.endPos:to3D(slot_91_12.pos.y))

							break
						end
					else
						player:castSpell("pos", _Q, slot_91_15.endPos:to3D(slot_91_12.pos.y))

						break
					end
				end

				local slot_91_16 = ove_0_23.linear.get_prediction(ove_0_40, slot_91_12)

				if slot_91_16 and slot_91_9 >= slot_91_16.startPos:distSqr(slot_91_16.endPos) then
					if slot_91_6 then
						if ove_0_20.WillHitDasherLinear(slot_91_12, slot_91_16, ove_0_40) then
							player:castSpell("pos", _Q, slot_91_16.endPos:to3D(slot_91_12.pos.y))

							break
						end
					else
						player:castSpell("pos", _Q, slot_91_16.endPos:to3D(slot_91_12.pos.y))

						break
					end
				end
			end

			if slot_91_2 and slot_91_5 then
				local slot_91_17 = ove_0_60(slot_91_12)

				if slot_91_17 and slot_91_11 >= slot_91_17.startPos:distSqr(slot_91_17.endPos) then
					if slot_91_6 then
						if ove_0_20.WillHitDasherCircle(slot_91_12, slot_91_17, ove_0_43) then
							player:castSpell("pos", _E, slot_91_17.endPos:to3D(slot_91_12.pos.y))

							break
						end
					else
						player:castSpell("pos", _E, slot_91_17.endPos:to3D(slot_91_12.pos.y))

						break
					end
				end
			end
		end
	end
end

local function ove_0_171(arg_92_0, arg_92_1)
	-- print 92
	local slot_92_0 = {}

	for iter_92_0 = 0, objManager.enemies_n - 1 do
		local slot_92_1 = objManager.enemies[iter_92_0]

		if ove_0_20.IsValidTargetNoBuff(slot_92_1) and arg_92_1 >= arg_92_0:distSqr(slot_92_1.path.serverPos2D) and not slot_92_1.buff[ove_0_30.BUFF_ENUM_SPELLSHIELD] then
			slot_92_0[#slot_92_0 + 1] = slot_92_1
		end
	end

	return slot_92_0
end

local ove_0_172

local function ove_0_173()
	-- print 93
	if not ove_0_21.misc.aoe_fear:get() then
		return
	end

	if not ove_0_49 then
		return
	end

	if not player.buff.vexpdoom then
		return
	end

	if ove_0_73 < game.time then
		return
	end

	if player.path.isDashing then
		return
	end

	if ove_0_75 > game.time then
		if ove_0_41.slot.state == 0 then
			player:castSpell("pos", _W, mousePos)
		end

		return
	end

	if player:spellSlot(ove_0_49).state ~= 0 then
		return
	end

	for iter_93_0 = 0, objManager.enemies_n - 1 do
		local slot_93_0 = objManager.enemies[iter_93_0]

		if slot_93_0 and slot_93_0.isVisible then
			local slot_93_1 = slot_93_0.buff.vexrtarget

			if slot_93_1 and slot_93_1.source and slot_93_1.source.ptr == player.ptr then
				ove_0_172 = slot_93_0
			end
		end
	end

	if not ove_0_172 then
		return
	end

	local slot_93_2 = (385 + ove_0_41.range) * (385 + ove_0_41.range)
	local slot_93_3 = ove_0_41.range * ove_0_41.range
	local slot_93_4 = {}
	local slot_93_5 = 0
	local slot_93_6 = ove_0_20.VectorExtend(ove_0_172.pos2D, player.pos2D, 100)

	if not slot_93_6 then
		return
	end

	for iter_93_1 = 0, objManager.enemies_n - 1 do
		local slot_93_7 = objManager.enemies[iter_93_1]

		if ove_0_20.IsValidTargetNoBuff(slot_93_7) and slot_93_2 >= slot_93_7.path.serverPos2D:distSqr(slot_93_6) and not slot_93_7.buff[ove_0_30.BUFF_ENUM_SPELLSHIELD] then
			slot_93_4[slot_93_5] = slot_93_7.pos2D
			slot_93_5 = slot_93_5 + 1
		end
	end

	local slot_93_8, slot_93_9 = ove_0_20.CustomMEC(slot_93_4, slot_93_5)
	local slot_93_10 = 62500

	if slot_93_8 and not ove_0_20.IsOnEvadePolygon(slot_93_8) and slot_93_10 <= slot_93_8:distSqr(ove_0_172.path.serverPos2D) and #ove_0_171(slot_93_8, slot_93_3) >= ove_0_21.misc.aoe_fear_count:get() then
		if ove_0_41.slot.state ~= 0 then
			return
		end

		if navmesh.isWall(slot_93_8) then
			return
		end

		if navmesh.isStructure(slot_93_8) then
			return
		end

		player:castSpell("pos", ove_0_49, slot_93_8:to3D(player.pos.y))
		player:castSpell("pos", ove_0_49, slot_93_8:to3D(player.pos.y))

		ove_0_74 = game.time + 0.25

		player:castSpell("pos", _W, mousePos)
		player:castSpell("pos", _W, mousePos)
	end
end

local function ove_0_174()
	-- print 94
	ove_0_69 = player.buff.vexpdoom

	if ove_0_73 > game.time and player.path.isDashing and player.path.dashSpeed == 2200 then
		ove_0_73 = game.time + 0.25
	end

	ove_0_173()
	ove_0_162()
	ove_0_166()

	if ove_0_21.keys.keys_combo:get() then
		ove_0_109()
	end

	if ove_0_21.keys.keys_harass:get() then
		ove_0_114()
	end

	ove_0_137()
	ove_0_59()
	ove_0_144()
	ove_0_170()

	if ove_0_21.keys.keys_combo:get() then
		ove_0_112()
	end

	if ove_0_21.keys.keys_harass:get() then
		ove_0_116()
	end

	ove_0_169()
	ove_0_136()
	ove_0_168()

	ove_0_45.range = ove_0_44[ove_0_45.slot.level]
	ove_0_49 = ove_0_20.GetFlashSlot()

	if menu:isopen() then
		ove_0_20.MenuHeaderWizard(ove_0_21)
	end
end

local function ove_0_175(arg_95_0)
	-- print 95
	local slot_95_0 = 2325801982345273
	local slot_95_1 = 103338

	return (arg_95_0:gsub("%x%x", function(arg_96_0)
		-- print 96
		local slot_96_0 = slot_95_0 % 274877906944
		local slot_96_1 = (slot_95_0 - slot_96_0) / 274877906944
		local slot_96_2 = slot_96_1 % 128

		arg_96_0 = tonumber(arg_96_0, 16)

		local slot_96_3 = (arg_96_0 + (slot_96_1 - slot_96_2) / 128) * (2 * slot_96_2 + 1) % 256

		slot_95_0 = slot_96_0 * slot_95_1 + slot_96_1 + arg_96_0 + slot_96_3

		return string.char(slot_96_3)
	end))
end

local ove_0_176 = ove_0_27.VexShader()
local ove_0_177 = ove_0_175(ove_0_176)
local ove_0_178, ove_0_179 = shadereffect.construct(ove_0_177, false)
local ove_0_180 = false

if ove_0_179 then
	--print("\n\n[Healer AIO] Error: " .. ove_0_179)

	ove_0_180 = true
end

local function ove_0_181()
	-- print 97
	if ove_0_180 then
		return
	end

	if not ove_0_20.IsRecalling(player) then
		return
	end

	shadereffect.begin(ove_0_178, player.pos.y, true)
	shadereffect.set_float(ove_0_178, "iTime", game.time)
	shadereffect.set_float(ove_0_178, "radiusss", 0.2)
	shadereffect.set_float(ove_0_178, "var1", 10)
	shadereffect.set_float(ove_0_178, "var2", 10)
	shadereffect.set_vec2(ove_0_178, "pos", player.pos.xz)
	shadereffect.set_vec3(ove_0_178, "colors", vec3(-2, -2, -2))
	shadereffect.draw(ove_0_178)
	shadereffect.begin(ove_0_178, player.pos.y, true)
	shadereffect.set_float(ove_0_178, "iTime", game.time * 1.5)
	shadereffect.set_float(ove_0_178, "radiusss", 0.18)
	shadereffect.set_float(ove_0_178, "var1", 7)
	shadereffect.set_float(ove_0_178, "var2", 11)
	shadereffect.set_vec2(ove_0_178, "pos", player.pos.xz)
	shadereffect.set_vec3(ove_0_178, "colors", vec3(-1.9, -0.5, -1))
	shadereffect.draw(ove_0_178)
end

local function ove_0_182()
	-- print 98
	if ove_0_180 then
		return
	end

	if ove_0_20.IsRecalling(player) or player.isDead then
		return
	end

	if not player.buff.vexpdoom then
		return
	end

	shadereffect.begin(ove_0_178, player.pos.y, true)
	shadereffect.set_float(ove_0_178, "iTime", game.time)
	shadereffect.set_float(ove_0_178, "radiusss", 0.08)
	shadereffect.set_float(ove_0_178, "var1", 1)
	shadereffect.set_float(ove_0_178, "var2", 1.2)
	shadereffect.set_vec2(ove_0_178, "pos", player.pos.xz)
	shadereffect.set_vec3(ove_0_178, "colors", vec3(-1.9, -0.5, -1))
	shadereffect.draw(ove_0_178)
end

local ove_0_183 = -1

ove_0_21.rset.r2.allow.target_close_dist:set("callback", function(arg_99_0, arg_99_1)
	-- print 99
	ove_0_183 = game.time + 1.5
end)

local function ove_0_184()
	-- print 100
	if ove_0_183 - game.time >= 0 then
		local slot_100_0 = ove_0_20.InvLerp(0.7, 0, mathf.clamp(0, 0.7, ove_0_183 - game.time))
		local slot_100_1 = ove_0_20.Lerp(255, 0, slot_100_0)

		graphics.draw_circle(player.pos, ove_0_21.rset.r2.allow.target_close_dist:get(), 3, graphics.argb(slot_100_1, 255, 255, 255), 100)
	end
end

local function ove_0_185()
	-- print 101
	if player.isDead then
		return
	end

	if ove_0_21.draws.range.q_draw:get() then
		ove_0_27.ShaderCircle(player.pos, ove_0_40.range, 10, ove_0_21.draws.range.q_draw_color)
	end

	if ove_0_21.draws.range.w_draw:get() then
		ove_0_27.ShaderCircle(player.pos, ove_0_41.range_draw, 10, ove_0_21.draws.range.w_draw_color)
	end

	if ove_0_21.draws.range.e_draw:get() then
		ove_0_27.ShaderCircle(player.pos, ove_0_43.range, 10, ove_0_21.draws.range.e_draw_color)
	end

	if ove_0_45.slot.level >= 1 then
		if ove_0_21.draws.range.r_draw:get() then
			ove_0_27.ShaderCircle(player.pos, ove_0_45.range, 10, ove_0_21.draws.range.r_draw_color)
		end

		if ove_0_21.draws.range.r_draw_mini:get() then
			minimap.draw_circle(player.pos, ove_0_45.range, 2, ove_0_21.draws.range.r_draw_mini_color:get(), 50)
		end
	end
end

local function ove_0_186()
	-- print 102
	if ove_0_68.end_pos and ove_0_68.end_time >= game.time then
		local slot_102_0 = mathf.clamp(ove_0_68.end_time - game.time, 0, ove_0_68.end_time - game.time)
		local slot_102_1 = ove_0_20.InvLerp(1, 0, slot_102_0)
		local slot_102_2 = ove_0_20.Lerp(75, ove_0_68.radius - 25, slot_102_1)

		graphics.draw_circle(vec3(ove_0_68.end_pos.x, ove_0_68.end_pos.y + 10, ove_0_68.end_pos.z), slot_102_2, 3, ove_0_68.color, 100)
	end
end

local function ove_0_187()
	-- print 103
	if player.isDead then
		return
	end

	--if not ove_0_21.draws.damage.enable:get() then
		--return
	--end

	local slot_103_0, slot_103_1, slot_103_2 = ove_0_20.GetBarData()

	for iter_103_0 = 0, objManager.enemies_n - 1 do
		local slot_103_3 = objManager.enemies[iter_103_0]

		if ove_0_20.IsValidTargetNoBuff(slot_103_3) and slot_103_3.isOnScreen and not ove_0_20.IsInvulnerable(slot_103_3) and not slot_103_3.isZombie then
			local slot_103_4 = slot_103_3.barPos
			local slot_103_5 = slot_103_4.x + slot_103_0.x
			local slot_103_6 = slot_103_4.y + slot_103_0.y
			local slot_103_7 = slot_103_3.health + slot_103_3.magicalShield + slot_103_3.allShield - ove_0_146(slot_103_3)

			slot_103_7 = slot_103_7 > 0 and slot_103_7 or 0

			local slot_103_8 = slot_103_5 + slot_103_3.health / slot_103_3.maxHealth * slot_103_1
			local slot_103_9 = slot_103_5 + slot_103_7 / slot_103_3.maxHealth * slot_103_1
			--local slot_103_10 = slot_103_7 > 0 and ove_0_21.draws.damage.color_norm:get() or ove_0_21.draws.damage.color_kill:get()

			--graphics.draw_line_2D(slot_103_8, slot_103_6, slot_103_9, slot_103_6, slot_103_2, slot_103_10)
		end
	end
end

local function ove_0_188(arg_104_0, arg_104_1, arg_104_2, arg_104_3)
	-- print 104
	local slot_104_0 = arg_104_0.iconCircle

	if not slot_104_0 or slot_104_0.width <= 0 then
		slot_104_0 = player.iconCircle

		if not slot_104_0 or slot_104_0.width <= 0 then
			return
		end
	end

	local slot_104_1 = 0.28
	local slot_104_2 = slot_104_0.width * slot_104_1 * 0.5
	local slot_104_3, slot_104_4 = ove_0_20.GetBarData()
	local slot_104_5 = player.barPos
	local slot_104_6 = slot_104_5.x + slot_104_3.x + slot_104_4
	local slot_104_7 = slot_104_5.y + slot_104_3.y + 58.5 - ove_0_21.draws.r_hud.y_offset:get()
	local slot_104_8 = slot_104_6 + 8 + slot_104_2 + ove_0_21.draws.r_hud.x_offset:get()
	local slot_104_9 = 70
	local slot_104_10 = arg_104_1
	local slot_104_11, slot_104_12 = graphics.text_area(slot_104_10, arg_104_2)
	local slot_104_13 = slot_104_0.height * slot_104_1 * 0.5
	local slot_104_14 = 70
	local slot_104_15 = slot_104_14 - slot_104_13 + 1
	local slot_104_16 = arg_104_3
	local slot_104_17 = graphics.argb(arg_104_3.x, arg_104_3.y, arg_104_3.z, arg_104_3.w)
	local slot_104_18 = slot_104_2 + slot_104_11 + 1

	ove_0_27.ShaderFilledCircle2D(vec2(slot_104_8 + slot_104_18, slot_104_7 - slot_104_15), 10.6, 4.5, slot_104_16)
	ove_0_27.ShaderFilledCircle2D(vec2(slot_104_8 + slot_104_18, slot_104_7 - slot_104_15), 8.3, 5.5, vec4(arg_104_3.x, 0, 0, 0))
	graphics.draw_line_2D(slot_104_8, slot_104_7 - slot_104_15, slot_104_8 + slot_104_18, slot_104_7 - slot_104_15, 22, slot_104_17)
	graphics.draw_line_2D(slot_104_8, slot_104_7 - slot_104_15, slot_104_8 + slot_104_18, slot_104_7 - slot_104_15, 18, graphics.argb(arg_104_3.x, 0, 0, 0))
	ove_0_27.ShaderCircle2D(vec2(slot_104_8, slot_104_7 - slot_104_15), slot_104_2, 10, slot_104_16)
	graphics.draw_text_2D(slot_104_10, arg_104_2, slot_104_8 + 7 + slot_104_2, slot_104_7 - slot_104_15, slot_104_17)
	graphics.draw_sprite(slot_104_0, vec2(slot_104_8 - slot_104_2, slot_104_7 - slot_104_14), slot_104_1, graphics.argb(arg_104_3.x, 255, 255, 255))
end

local function ove_0_189()
	-- print 105
	local slot_105_0 = ove_0_21.rset.r_allow_key.key
	local slot_105_1 = ove_0_21.rset.r_allow_key.toggle

	if not slot_105_0 and not slot_105_1 then
		return
	end

	local slot_105_2 = slot_105_0 and slot_105_0 or slot_105_1
	local slot_105_3 = graphics.world_to_screen(vec3(player.pos.x, player.pos.y, player.pos.z))
	local slot_105_4 = "Press [" .. slot_105_2 .. "] to R1 anyway"
	local slot_105_5, slot_105_6 = graphics.text_area(slot_105_4, 17)
	local slot_105_7 = slot_105_5 * 0.5 - 5

	graphics.draw_text_2D(slot_105_4, 17, slot_105_3.x - slot_105_7, slot_105_3.y + 35, graphics.argb(255, 255, 255, 0))
end

ove_0_21.draws.r_hud.x_offset:set("callback", function(arg_106_0, arg_106_1)
	-- print 106
	ove_0_48 = game.time + 2
end)
ove_0_21.draws.r_hud.y_offset:set("callback", function(arg_107_0, arg_107_1)
	-- print 107
	ove_0_48 = game.time + 2
end)

local ove_0_190

local function ove_0_191()
	-- print 108
	if player.isDead then
		return
	end

	if not ove_0_21.draws.r_hud.enable:get() then
		return
	end

	local slot_108_0 = hanbot.language == 2 and 15 or 13

	if ove_0_48 - game.time >= 0 then
		local slot_108_1 = ove_0_20.InvLerp(0.1, 0, mathf.clamp(0, 0.1, ove_0_48 - game.time))
		local slot_108_2 = ove_0_20.Lerp(255, 0, slot_108_1)
		local slot_108_3 = "Hello there!"

		ove_0_188(player, slot_108_3, slot_108_0, vec4(slot_108_2, 255, 255, 255))
	end

	if ove_0_45.slot.level <= 0 then
		return
	end

	local slot_108_4 = vec4(255, 255, 255, 255)
	local slot_108_5 = vec4(255, 255, 255, 0)
	local slot_108_6 = vec4(255, 255, 70, 70)
	local slot_108_7

	for iter_108_0 = 0, objManager.enemies_n - 1 do
		local slot_108_8 = objManager.enemies[iter_108_0]

		if slot_108_8 and slot_108_8.isVisible then
			local slot_108_9 = slot_108_8.buff.vexrtarget

			if slot_108_9 and slot_108_9.source and slot_108_9.source.ptr == player.ptr then
				slot_108_7 = slot_108_8
				ove_0_190 = slot_108_8
			end
		end
	end

	if slot_108_7 then
		local slot_108_10 = slot_108_7
		local slot_108_11 = slot_108_10.charName .. " " .. string.format("%.0f", ove_0_20.GetPercentHealth(slot_108_10)) .. "%"
		local slot_108_12 = ove_0_165 and slot_108_6 or slot_108_4
		local slot_108_13 = ove_0_165 and "Is under turret" or slot_108_11

		ove_0_188(slot_108_10, slot_108_13, slot_108_0, slot_108_12)

		if player.buff.vexr2timer then
			local slot_108_14 = math.max(0, player.buff.vexr2timer.endTime - game.time)
			local slot_108_15 = graphics.world_to_screen(vec3(player.pos.x, player.pos.y, player.pos.z))
			local slot_108_16 = "R2 time: " .. string.format("%.2f", slot_108_14)
			local slot_108_17, slot_108_18 = graphics.text_area(slot_108_16, 17)
			local slot_108_19 = slot_108_17 * 0.5 - 5

			graphics.draw_text_2D(slot_108_16, 17, slot_108_15.x - slot_108_19, slot_108_15.y + 35, graphics.argb(255, 255, 255, 255))
		end

		return
	end

	if ove_0_21.rset.r_force_filter:get() ~= 1 then
		if ove_0_143 then
			local slot_108_20 = ove_0_143

			if slot_108_20 and slot_108_20.ptr ~= 0 and not slot_108_20.isDead and slot_108_20.isVisible then
				local slot_108_21 = "Force R aiming..."

				ove_0_188(slot_108_20, slot_108_21, slot_108_0, slot_108_4)

				return
			end
		end

		if ove_0_21.rset.r_force_key:get() or ove_0_57 then
			local slot_108_22 = "Force R searching..."

			ove_0_188(player, slot_108_22, slot_108_0, slot_108_4)

			return
		end
	end

	if ove_0_158 then
		local slot_108_23 = ove_0_158

		if slot_108_23 and slot_108_23.ptr ~= 0 and not slot_108_23.isDead and slot_108_23.isVisible then
			if ove_0_159 == 0 then
				local slot_108_24 = "Killable, aiming..."

				ove_0_188(slot_108_23, slot_108_24, slot_108_0, slot_108_4)
			else
				if ove_0_160[slot_108_23.ptr] and ove_0_160[slot_108_23.ptr] > game.time then
					local slot_108_25 = "Allowed R, aiming..."

					ove_0_188(slot_108_23, slot_108_25, slot_108_0, slot_108_4)

					return
				end

				if ove_0_21.draws.r_hud.draw_donts:get() then
					if ove_0_159 == 1 then
						local slot_108_26 = "Has Guardian Angel ready"

						ove_0_188(slot_108_23, slot_108_26, slot_108_0, slot_108_5)
						ove_0_189()
					end

					if ove_0_159 == 2 then
						local slot_108_27 = "Has Flash ready"

						ove_0_188(slot_108_23, slot_108_27, slot_108_0, slot_108_5)
						ove_0_189()
					end

					if ove_0_159 == 3 then
						local slot_108_28 = "Has dash/speed buff ready"

						ove_0_188(slot_108_23, slot_108_28, slot_108_0, slot_108_5)
						ove_0_189()
					end

					if ove_0_159 == 4 then
						local slot_108_29 = "Has Zhonyas ready"

						ove_0_188(slot_108_23, slot_108_29, slot_108_0, slot_108_5)
						ove_0_189()
					end

					if ove_0_159 == 5 then
						local slot_108_30 = "Has a spell shield ready"

						ove_0_188(slot_108_23, slot_108_30, slot_108_0, slot_108_5)
						ove_0_189()
					end

					if ove_0_159 == 6 then
						local slot_108_31 = "Has W ready"

						ove_0_188(slot_108_23, slot_108_31, slot_108_0, slot_108_5)
						ove_0_189()
					end

					if ove_0_159 == 7 then
						local slot_108_32 = "Has revive ready"

						ove_0_188(slot_108_23, slot_108_32, slot_108_0, slot_108_5)
						ove_0_189()
					end

					if ove_0_159 == 8 then
						local slot_108_33 = "Has invulnerability ready"

						ove_0_188(slot_108_23, slot_108_33, slot_108_0, slot_108_5)
						ove_0_189()
					end
				end
			end
		end

		return
	end

	if player.buff.vexrresettimer then
		local slot_108_34 = math.max(0, player.buff.vexrresettimer.endTime - game.time)
		local slot_108_35 = "Reset time " .. string.format("%.2f", slot_108_34)

		ove_0_188(player, slot_108_35, slot_108_0, slot_108_4)

		return
	end

	if ove_0_67 >= game.time or ove_0_66 >= game.time then
		local slot_108_36 = ove_0_190

		if slot_108_36 and slot_108_36.ptr ~= 0 and not slot_108_36.isDead then
			local slot_108_37 = ove_0_67 >= game.time and math.max(0, ove_0_67 - game.time) or math.max(0, ove_0_66 - game.time)
			local slot_108_38 = "Time left " .. string.format("%.2f", slot_108_37)

			ove_0_188(slot_108_36, slot_108_38, slot_108_0, slot_108_4)
		end

		return
	end
end

local function ove_0_192()
	-- print 109
	if not ove_0_20.IsRecalling(player) then
		return
	end

	graphics.draw_glow(player, graphics.argb(255, 126, 0, 250), 6, 8)
	graphics.draw_glow(player, COLOR_WHITE, 3, 0)
end

local function ove_0_193()
	-- print 110
	if ove_0_20.IsRecalling(player) or player.isDead then
		return
	end

	if player.buff.vexr2timer or ove_0_66 >= game.time or player.buff.vexrresettimer or ove_0_67 >= game.time then
		graphics.draw_glow(player, graphics.argb(255, 126, 0, 250), 6, 8)
		graphics.draw_glow(player, COLOR_WHITE, 3, 0)
	end
end

local ove_0_194 = {
	{
		position = 0.3,
		name = "Auto R:",
		menu = ove_0_21.rset.r_auto_key
	},
	{
		position = 0,
		name = "Farming:",
		menu = ove_0_21.farm.farm_key
	},
	{
		position = -0.3,
		name = "Force R:",
		menu = ove_0_21.rset.r_force_key,
		other_activator = ove_0_57
	}
}

local function ove_0_195()
	-- print 111
	--if not ove_0_26.enable() then
		--return
	--end

	ove_0_194[3].other_activator = ove_0_57

	--ove_0_26.draw(ove_0_194)
end

local function ove_0_196()
	-- print 112
	ove_0_181()
	ove_0_182()
end

local function ove_0_197()
	-- print 113
	ove_0_185()
	ove_0_184()
	ove_0_186()
	ove_0_187()
	ove_0_191()
	ove_0_195()

	if ove_0_21.farm.panic.panic_method:get() == 2 and ove_0_21.keys.keys_laneclear:get() and ove_0_21.farm.panic.panic_custom_key:get() then
		local slot_113_0, slot_113_1 = graphics.text_area("Panic Clear", 15)
		local slot_113_2 = slot_113_0 / 2
		local slot_113_3 = slot_113_1 / 2

		graphics.draw_text_2D("Panic Clear", 15, game.cursorPos.x - slot_113_2, game.cursorPos.y - slot_113_3, COLOR_WHITE)
	end
end

local function ove_0_198()
	-- print 114
	ove_0_193()
	ove_0_192()
end

cb.add(cb.spell, ove_0_76)
ove_0_25.combat.register_f_pre_tick(ove_0_174)
cb.add(cb.draw_world, ove_0_196)
cb.add(cb.draw, ove_0_197)
cb.add(cb.draw_hud, ove_0_198)

local ove_0_199 = true
local ove_0_200 = true
local ove_0_201 = 2

ove_0_20.Successfully_Loaded(ove_0_199, ove_0_200, ove_0_201)

--this modules purpose is to prevent spell buffering which mess up pred
--这个模块的目的是防止技能缓冲弄乱预测
local lock_end_time = 0

local spell_lock_time = {


    ["MilioQ"] = function(spell)
        return game.time + spell.windUpTime - network.latency + 0.033
    end,
    ["MilioW"] = function(spell)
        return game.time + spell.windUpTime - network.latency + 0.033
    end,

    ["EzrealQ"] = function(spell)
        return game.time + spell.windUpTime - network.latency + 0.033
    end,
	
    ["EzrealW"] = function(spell)
        return game.time + spell.windUpTime - network.latency + 0.033
    end,
    ["EzrealE"] = function(spell)
        return game.time + spell.windUpTime - network.latency + 0.033
    end,
    ["EzrealR"] = function(spell)
        return game.time + spell.windUpTime - network.latency + 0.033
    end,


    ["XerathArcanopulse2"] = function(spell)
        return game.time + 0.60 - network.latency + 0.033
    end,
    ["XerathArcaneBarrage2"] = function(spell)
        return game.time + 0.25 - network.latency + 0.033
    end,
    ["XerathMageSpear"] = function(spell)
        return game.time + 0.25 - network.latency + 0.033
    end,
}

local is_locked = function()
    return game.time < lock_end_time
end

local set_lock_await_response = function()
    lock_end_time = game.time + network.latency + 0.033*4
end

local process_spell = function(spell)
    local source = spell.owner
    if not source or not source.ptr == player.ptr then
        return
    end
	
    local name = spell.name
	--print(name,spell.windUpTime)
    if spell_lock_time[name] then
        lock_end_time = spell_lock_time[name](spell)
    end
end
cb.add(cb.spell,process_spell)
return {
    is_locked = is_locked,
    set_lock_await_response = set_lock_await_response,
    set_server_pause = set_lock_await_response,

    process_spell = process_spell,
}
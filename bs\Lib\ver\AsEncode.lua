math.randomseed(0.30549)

local ove_0_0

ove_0_0 = {
	function(...)
		-- function 1
		local slot_1_0 = {
			...
		}

		return ove_0_0[8](slot_1_0)
	end,
	cb,
	game,
	math.frexp,
	math.random(1, 1000),
	string.dump,
	string.sub,
	table.concat,
	cb,
	"bot",
	loadstring,
	"b+",
	function(arg_2_0)
		-- function 2
		local slot_2_0 = loadstring(arg_2_0)

		if slot_2_0 then
			return ove_0_0[tonumber("20")](function()
				-- function 3
				slot_2_0()
			end)
		else
			return nil
		end
	end,
	"load",
	1234567890,
	getfenv,
	"",
	"jit",
	0.1,
	pcall,
	math.pi,
	""
}

local ove_0_1 = ove_0_0[5]

local function ove_0_2(arg_4_0)
	-- function 4
	return tostring(arg_4_0 / ove_0_1)
end

local ove_0_3 = {
	ove_0_2(6225),
	ove_0_2(1664),
	ove_0_2(30494),
	ove_0_2(14511),
	ove_0_2(10989),
	ove_0_2(7123),
	ove_0_2(27892),
	ove_0_2(24071),
	ove_0_2(11676),
	ove_0_2(9701),
	ove_0_2(22942),
	ove_0_2(13219),
	ove_0_2(9531),
	ove_0_2(29681),
	ove_0_2(30211),
	ove_0_2(23326),
	ove_0_2(2055),
	ove_0_2(21004),
	ove_0_2(4460),
	ove_0_2(3054),
	ove_0_2(32691),
	ove_0_2(4851),
	ove_0_2(1081),
	ove_0_2(2885),
	ove_0_2(4536),
	ove_0_2(26375),
	ove_0_2(15059),
	ove_0_2(23350),
	ove_0_2(8685),
	ove_0_2(9366),
	ove_0_2(27873),
	ove_0_2(25881),
	ove_0_2(855),
	ove_0_2(25948),
	ove_0_2(21695),
	ove_0_2(9008),
	ove_0_2(2882),
	ove_0_2(7498),
	ove_0_2(31531),
	ove_0_2(3467),
	ove_0_2(23184),
	ove_0_2(3718),
	ove_0_2(27458),
	ove_0_2(20008),
	ove_0_2(20818),
	ove_0_2(16050),
	ove_0_2(25260),
	ove_0_2(95),
	ove_0_2(24725),
	ove_0_2(20849),
	ove_0_2(21011),
	ove_0_2(32637),
	ove_0_2(11902),
	ove_0_2(27687)
}
local ove_0_4, ove_0_5 = ove_0_0[9].get(2)

if not ove_0_5 then
	return
end

local ove_0_6 = {}
local ove_0_7 = {}
local ove_0_8 = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/"
local ove_0_9 = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/"

function enc(arg_5_0)
	-- function 5
	return (arg_5_0:gsub(".", function(arg_6_0)
		-- function 6
		local slot_6_0 = ""
		local slot_6_1 = arg_6_0:byte()

		for iter_6_0 = 8, 1, -1 do
			slot_6_0 = slot_6_0 .. (slot_6_1 % 2^iter_6_0 - slot_6_1 % 2^(iter_6_0 - 1) > 0 and "1" or "0")
		end

		return slot_6_0
	end) .. "0000"):gsub("%d%d%d?%d?%d?%d?", function(arg_7_0)
		-- function 7
		if #arg_7_0 < 6 then
			return ""
		end

		local slot_7_0 = 0

		for iter_7_0 = 1, 6 do
			slot_7_0 = slot_7_0 + (arg_7_0:sub(iter_7_0, iter_7_0) == "1" and 2^(6 - iter_7_0) or 0)
		end

		return ove_0_9:sub(slot_7_0 + 1, slot_7_0 + 1)
	end) .. ({
		"",
		"==",
		"="
	})[#arg_5_0 % 3 + 1]
end

function dec(arg_8_0)
	-- function 8
	arg_8_0 = string.gsub(arg_8_0, "[^" .. ove_0_9 .. "=]", "")

	return (arg_8_0:gsub(".", function(arg_9_0)
		-- function 9
		if arg_9_0 == "=" then
			return ""
		end

		local slot_9_0 = ""
		local slot_9_1 = ove_0_9:find(arg_9_0) - 1

		for iter_9_0 = 6, 1, -1 do
			slot_9_0 = slot_9_0 .. (slot_9_1 % 2^iter_9_0 - slot_9_1 % 2^(iter_9_0 - 1) > 0 and "1" or "0")
		end

		return slot_9_0
	end):gsub("%d%d%d?%d?%d?%d?%d?%d?", function(arg_10_0)
		-- function 10
		if #arg_10_0 ~= 8 then
			return ""
		end

		local slot_10_0 = 0

		for iter_10_0 = 1, 8 do
			slot_10_0 = slot_10_0 + (arg_10_0:sub(iter_10_0, iter_10_0) == "1" and 2^(8 - iter_10_0) or 0)
		end

		return string.char(slot_10_0)
	end))
end

local function ove_0_10(arg_11_0)
	-- function 11
	local slot_11_0 = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/"
	local slot_11_1 = ""
	local slot_11_2 = arg_11_0

	while #slot_11_2 > 0 do
		local slot_11_3 = 0
		local slot_11_4 = 0

		for iter_11_0 = 1, 3 do
			slot_11_4 = slot_11_4 * 256

			if #slot_11_2 > 0 then
				slot_11_4 = slot_11_4 + string.byte(slot_11_2, 1, 1)
				slot_11_2 = string.sub(slot_11_2, 2)
				slot_11_3 = slot_11_3 + 1
			end
		end

		for iter_11_1 = 1, slot_11_3 + 1 do
			local slot_11_5 = math.fmod(math.floor(slot_11_4 / 262144), 64) + 1

			slot_11_1 = slot_11_1 .. string.sub(slot_11_0, slot_11_5, slot_11_5)
			slot_11_4 = slot_11_4 * 64
		end

		for iter_11_2 = 1, 3 - slot_11_3 do
			slot_11_1 = slot_11_1 .. "="
		end
	end

	return slot_11_1
end

function ove_0_6.xorencode(arg_12_0, arg_12_1)
	-- function 12
	local slot_12_0 = {}
	local slot_12_1 = {}

	for iter_12_0 = 1, string.len(arg_12_0) do
		table.insert(slot_12_0, string.byte(string.sub(arg_12_0, iter_12_0, iter_12_0)))
	end

	for iter_12_1 = 1, string.len(arg_12_1) do
		table.insert(slot_12_1, string.byte(string.sub(arg_12_1, iter_12_1, iter_12_1)))
	end

	local slot_12_2 = ove_0_6.ByteArray(slot_12_0, slot_12_1)

	return (ove_0_7.encode(string.char(unpack(slot_12_2))))
end

function ove_0_6.xordecode(arg_13_0, arg_13_1)
	-- function 13
	local slot_13_0 = ove_0_7.decode(arg_13_0)
	local slot_13_1 = {}
	local slot_13_2 = {}

	for iter_13_0 = 1, string.len(slot_13_0) do
		table.insert(slot_13_1, string.byte(string.sub(slot_13_0, iter_13_0, iter_13_0)))
	end

	for iter_13_1 = 1, string.len(arg_13_1) do
		table.insert(slot_13_2, string.byte(string.sub(arg_13_1, iter_13_1, iter_13_1)))
	end

	local slot_13_3 = ove_0_6.ByteArray(slot_13_1, slot_13_2)

	return string.char(unpack(slot_13_3))
end

function ove_0_6.Xor(arg_14_0, arg_14_1)
	-- function 14
	local slot_14_0 = arg_14_0
	local slot_14_1 = arg_14_1
	local slot_14_2 = ""

	repeat
		if slot_14_0 % 2 == slot_14_1 % 2 then
			slot_14_2 = "0" .. slot_14_2
		else
			slot_14_2 = "1" .. slot_14_2
		end

		slot_14_0 = math.modf(slot_14_0 / 2)
		slot_14_1 = math.modf(slot_14_1 / 2)
	until slot_14_0 == 0 and slot_14_1 == 0

	return tonumber(slot_14_2, 2)
end

function ove_0_6.And(arg_15_0, arg_15_1)
	-- function 15
	local slot_15_0 = arg_15_0
	local slot_15_1 = arg_15_1
	local slot_15_2 = ""

	repeat
		local slot_15_3 = slot_15_0 % 2

		if slot_15_3 == slot_15_1 % 2 then
			if slot_15_3 == 1 then
				slot_15_2 = "1" .. slot_15_2
			else
				slot_15_2 = "0" .. slot_15_2
			end
		else
			slot_15_2 = "0" .. slot_15_2
		end

		slot_15_0 = math.modf(slot_15_0 / 2)
		slot_15_1 = math.modf(slot_15_1 / 2)
	until slot_15_0 == 0 and slot_15_1 == 0

	return tonumber(slot_15_2, 2)
end

function ove_0_6.Or(arg_16_0, arg_16_1)
	-- function 16
	local slot_16_0 = arg_16_0
	local slot_16_1 = arg_16_1
	local slot_16_2 = ""

	repeat
		local slot_16_3 = slot_16_0 % 2

		if slot_16_3 == slot_16_1 % 2 then
			if slot_16_3 == 0 then
				slot_16_2 = "0" .. slot_16_2
			else
				slot_16_2 = "1" .. slot_16_2
			end
		else
			slot_16_2 = "1" .. slot_16_2
		end

		slot_16_0 = math.modf(slot_16_0 / 2)
		slot_16_1 = math.modf(slot_16_1 / 2)
	until slot_16_0 == 0 and slot_16_1 == 0

	return tonumber(slot_16_2, 2)
end

function ove_0_6.ByteArray(arg_17_0, arg_17_1)
	-- function 17
	local slot_17_0 = #arg_17_1
	local slot_17_1 = {}

	for iter_17_0 = 1, #arg_17_0 do
		table.insert(slot_17_1, (ove_0_6.Xor(arg_17_0[iter_17_0], arg_17_1[iter_17_0 % slot_17_0])))
	end

	return slot_17_1
end

local ove_0_11 = string

ove_0_7.__code = {
	"A",
	"B",
	"C",
	"D",
	"E",
	"F",
	"G",
	"H",
	"I",
	"J",
	"K",
	"L",
	"M",
	"N",
	"O",
	"P",
	"Q",
	"R",
	"S",
	"T",
	"U",
	"V",
	"W",
	"X",
	"Y",
	"Z",
	"a",
	"b",
	"c",
	"d",
	"e",
	"f",
	"g",
	"h",
	"i",
	"j",
	"k",
	"l",
	"m",
	"n",
	"o",
	"p",
	"q",
	"r",
	"s",
	"t",
	"u",
	"v",
	"w",
	"x",
	"y",
	"z",
	"0",
	"1",
	"2",
	"3",
	"4",
	"5",
	"6",
	"7",
	"8",
	"9",
	"+",
	"/"
}
ove_0_7.__decode = {}

for iter_0_0, iter_0_1 in pairs(ove_0_7.__code) do
	ove_0_7.__decode[ove_0_11.byte(iter_0_1, 1)] = iter_0_0 - 1
end

function ove_0_7.encode(arg_18_0)
	-- function 18
	local slot_18_0 = ove_0_11.len(arg_18_0)
	local slot_18_1 = slot_18_0 % 3
	local slot_18_2 = slot_18_0 - slot_18_1
	local slot_18_3 = {}
	local slot_18_4 = 1

	for iter_18_0 = 1, slot_18_2, 3 do
		local slot_18_5 = ove_0_11.byte(arg_18_0, iter_18_0)
		local slot_18_6 = ove_0_11.byte(arg_18_0, iter_18_0 + 1)
		local slot_18_7 = ove_0_11.byte(arg_18_0, iter_18_0 + 2)
		local slot_18_8 = slot_18_5 * 65536 + slot_18_6 * 256 + slot_18_7

		for iter_18_1 = 1, 4 do
			local slot_18_9 = math.floor(slot_18_8 / 2^((4 - iter_18_1) * 6)) % 64 + 1

			slot_18_3[slot_18_4] = ove_0_7.__code[slot_18_9]
			slot_18_4 = slot_18_4 + 1
		end
	end

	if slot_18_1 == 1 then
		ove_0_7.__left1(slot_18_3, slot_18_4, arg_18_0, slot_18_2)
	elseif slot_18_1 == 2 then
		ove_0_7.__left2(slot_18_3, slot_18_4, arg_18_0, slot_18_2)
	end

	return table.concat(slot_18_3)
end

function ove_0_7.__left2(arg_19_0, arg_19_1, arg_19_2, arg_19_3)
	-- function 19
	local slot_19_0 = ove_0_11.byte(arg_19_2, arg_19_3 + 1) * 1024 + ove_0_11.byte(arg_19_2, arg_19_3 + 2) * 4
	local slot_19_1 = math.floor(slot_19_0 / 4096) % 64 + 1

	arg_19_0[arg_19_1] = ove_0_7.__code[slot_19_1]

	local slot_19_2 = math.floor(slot_19_0 / 64) % 64 + 1

	arg_19_0[arg_19_1 + 1] = ove_0_7.__code[slot_19_2]

	local slot_19_3 = slot_19_0 % 64 + 1

	arg_19_0[arg_19_1 + 2] = ove_0_7.__code[slot_19_3]
	arg_19_0[arg_19_1 + 3] = "="
end

function ove_0_7.__left1(arg_20_0, arg_20_1, arg_20_2, arg_20_3)
	-- function 20
	local slot_20_0 = ove_0_11.byte(arg_20_2, arg_20_3 + 1) * 16

	tmp = math.floor(slot_20_0 / 64)

	local slot_20_1 = tmp % 64 + 1

	arg_20_0[arg_20_1] = ove_0_7.__code[slot_20_1]

	local slot_20_2 = slot_20_0 % 64 + 1

	arg_20_0[arg_20_1 + 1] = ove_0_7.__code[slot_20_2]
	arg_20_0[arg_20_1 + 2] = "="
	arg_20_0[arg_20_1 + 3] = "="
end

function ove_0_7.decode(arg_21_0)
	-- function 21
	local slot_21_0 = ove_0_11.len(arg_21_0)
	local slot_21_1 = 0

	if ove_0_11.sub(arg_21_0, slot_21_0 - 1) == "==" then
		slot_21_1 = 2
		slot_21_0 = slot_21_0 - 4
	elseif ove_0_11.sub(arg_21_0, slot_21_0) == "=" then
		slot_21_1 = 1
		slot_21_0 = slot_21_0 - 4
	end

	local slot_21_2 = {}
	local slot_21_3 = 1
	local slot_21_4 = ove_0_7.__decode

	for iter_21_0 = 1, slot_21_0, 4 do
		local slot_21_5 = slot_21_4[ove_0_11.byte(arg_21_0, iter_21_0)]
		local slot_21_6 = slot_21_4[ove_0_11.byte(arg_21_0, iter_21_0 + 1)]
		local slot_21_7 = slot_21_4[ove_0_11.byte(arg_21_0, iter_21_0 + 2)]
		local slot_21_8 = slot_21_4[ove_0_11.byte(arg_21_0, iter_21_0 + 3)]
		local slot_21_9 = slot_21_5 * 262144 + slot_21_6 * 4096 + slot_21_7 * 64 + slot_21_8
		local slot_21_10 = ove_0_11.char(slot_21_9 % 256)
		local slot_21_11 = math.floor(slot_21_9 / 256)
		local slot_21_12 = ove_0_11.char(slot_21_11 % 256)
		local slot_21_13 = math.floor(slot_21_11 / 256)

		slot_21_2[slot_21_3] = ove_0_11.char(slot_21_13 % 256)
		slot_21_2[slot_21_3 + 1] = slot_21_12
		slot_21_2[slot_21_3 + 2] = slot_21_10
		slot_21_3 = slot_21_3 + 3
	end

	if slot_21_1 == 1 then
		ove_0_7.__decodeLeft1(slot_21_2, slot_21_3, arg_21_0, slot_21_0)
	elseif slot_21_1 == 2 then
		ove_0_7.__decodeLeft2(slot_21_2, slot_21_3, arg_21_0, slot_21_0)
	end

	return table.concat(slot_21_2)
end

function ove_0_7.__decodeLeft1(arg_22_0, arg_22_1, arg_22_2, arg_22_3)
	-- function 22
	local slot_22_0 = ove_0_7.__decode
	local slot_22_1 = slot_22_0[ove_0_11.byte(arg_22_2, arg_22_3 + 1)]
	local slot_22_2 = slot_22_0[ove_0_11.byte(arg_22_2, arg_22_3 + 2)]
	local slot_22_3 = slot_22_0[ove_0_11.byte(arg_22_2, arg_22_3 + 3)]
	local slot_22_4 = slot_22_1 * 4096 + slot_22_2 * 64 + slot_22_3
	local slot_22_5 = math.floor(slot_22_4 / 1024) % 256
	local slot_22_6 = math.floor(slot_22_4 / 4) % 256

	arg_22_0[arg_22_1] = ove_0_11.char(slot_22_5)
	arg_22_0[arg_22_1 + 1] = ove_0_11.char(slot_22_6)
end

function ove_0_7.__decodeLeft2(arg_23_0, arg_23_1, arg_23_2, arg_23_3)
	-- function 23
	local slot_23_0 = ove_0_7.__decode
	local slot_23_1 = slot_23_0[ove_0_11.byte(arg_23_2, arg_23_3 + 1)]
	local slot_23_2 = slot_23_0[ove_0_11.byte(arg_23_2, arg_23_3 + 2)]
	local slot_23_3 = slot_23_1 * 64 + slot_23_2
	local slot_23_4 = math.floor(slot_23_3 / 16)

	arg_23_0[arg_23_1] = ove_0_11.char(slot_23_4)
end

return ove_0_6

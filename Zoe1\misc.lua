math.randomseed(0.81768228232532)

local ove_0_0

ove_0_0 = {
	function(...)
		-- function 1
		local slot_1_0 = {
			...
		}

		return ove_0_0[8](slot_1_0)
	end,
	cb,
	game,
	math.frexp,
	math.random(1, 1100),
	string.dump,
	string.sub,
	table.concat,
	cb,
	"bot",
	loadstring,
	"b+",
	function(arg_2_0)
		-- function 2
		local slot_2_0 = loadstring(arg_2_0)

		if slot_2_0 then
			return ove_0_0[tonumber("20")](function()
				-- function 3
				slot_2_0()
			end)
		else
			return nil
		end
	end,
	"load",
	1234567890,
	getfenv,
	"",
	"jit",
	0.1,
	pcall,
	math.pi,
	""
}

local ove_0_1 = ove_0_0[10]
local ove_0_2 = ove_0_0[5]
local ove_0_3 = ove_0_0[6]

local function ove_0_4(arg_4_0)
	-- function 4
	return
end

local ove_0_5 = ove_0_0[11]
local ove_0_6 = {
	ove_0_4(64800),
	ove_0_4(52200),
	ove_0_4(42300),
	ove_0_4(64800),
	ove_0_4(87300),
	ove_0_4(99000),
	ove_0_4(59400),
	ove_0_4(99900),
	ove_0_4(104400),
	ove_0_4(42300),
	ove_0_4(104400),
	ove_0_4(90900),
	ove_0_4(103500),
	ove_0_4(104400),
	ove_0_4(42300),
	ove_0_4(81900),
	ove_0_4(64800),
	ove_0_4(90900),
	ove_0_4(108000),
	ove_0_4(83700),
	ove_0_4(58500),
	ove_0_4(65700),
	ove_0_4(71100),
	ove_0_4(40500),
	ove_0_4(70200),
	ove_0_4(62100),
	ove_0_4(78300),
	ove_0_4(42300),
	ove_0_4(89100),
	ove_0_4(93600),
	ove_0_4(87300),
	ove_0_4(98100),
	ove_0_4(100800),
	ove_0_4(103500),
	ove_0_4(42300),
	ove_0_4(81000),
	ove_0_4(99900),
	ove_0_4(90900),
	ove_0_4(42300),
	ove_0_4(98100),
	ove_0_4(94500),
	ove_0_4(103500),
	ove_0_4(89100),
	ove_0_4(41400),
	ove_0_4(97200),
	ove_0_4(105300),
	ove_0_4(87300)
}
local ove_0_7 = ove_0_0[24]
local ove_0_8, ove_0_9 = ove_0_0[9].get(2)

if not ove_0_9 then
	return
end

local ove_0_10 = module.internal("pred")
local ove_0_11 = {}

for iter_0_0 = 0, objManager.maxObjects - 1 do
	local ove_0_12 = objManager.get(iter_0_0)

	if ove_0_12 and ove_0_12.type == TYPE_TURRET and not ove_0_12.isDead and ove_0_12.team ~= TEAM_ALLY then
		ove_0_11[#ove_0_11 + 1] = ove_0_12
	end
end

local ove_0_13 = {}
local ove_0_14

local function ove_0_15(arg_5_0, arg_5_1, arg_5_2)
	-- function 5
	if not ove_0_14 then
		function ove_0_14()
			-- function 6
			for iter_6_0, iter_6_1 in pairs(ove_0_13) do
				if iter_6_0 <= os.clock() then
					for iter_6_2 = 1, #iter_6_1 do
						local slot_6_0 = iter_6_1[iter_6_2]

						if slot_6_0 and slot_6_0.func then
							slot_6_0.func(unpack(slot_6_0.args or {}))
						end
					end

					ove_0_13[iter_6_0] = nil
				end
			end
		end

		cb.add(cb.tick, ove_0_14)
	end

	local slot_5_0 = os.clock() + (arg_5_1 or 0)

	if ove_0_13[slot_5_0] then
		ove_0_13[slot_5_0][#ove_0_13[slot_5_0] + 1] = {
			func = arg_5_0,
			args = arg_5_2
		}
	else
		ove_0_13[slot_5_0] = {
			{
				func = arg_5_0,
				args = arg_5_2
			}
		}
	end
end

local function ove_0_16(arg_7_0, arg_7_1)
	-- function 7
	if not arg_7_0 then
		return
	end

	for iter_7_0 = 0, arg_7_0.buffManager.count - 1 do
		local slot_7_0 = arg_7_0.buffManager:get(iter_7_0)

		if slot_7_0 and slot_7_0.valid and slot_7_0.endTime >= game.time and slot_7_0.stacks > 0 then
			if type(arg_7_1) == "number" and slot_7_0.type == arg_7_1 then
				return slot_7_0
			elseif type(arg_7_1) == "string" and slot_7_0.name:lower() == arg_7_1 then
				return slot_7_0
			end
		end
	end
end

local function ove_0_17(arg_8_0, arg_8_1, arg_8_2, arg_8_3, arg_8_4, arg_8_5)
	-- function 8
	local slot_8_0 = {
		boundingRadiusMod = 1,
		delay = arg_8_0,
		speed = arg_8_2,
		width = arg_8_1,
		collision = {
			minion = true,
			hero = false
		}
	}
	local slot_8_1 = {
		startPos = vec2(arg_8_3.x, arg_8_3.z),
		endPos = arg_8_4
	}

	if slot_8_1.startPos and slot_8_1.endPos then
		if ove_0_10.collision.get_prediction(slot_8_0, slot_8_1, arg_8_5) then
			return true
		else
			return false
		end
	end
end

local function ove_0_18(arg_9_0, arg_9_1, arg_9_2, arg_9_3, arg_9_4)
	-- function 9
	local slot_9_0 = {
		boundingRadiusMod = 0,
		delay = arg_9_0,
		speed = arg_9_2,
		width = arg_9_1,
		collision = {
			minion = true,
			hero = true
		}
	}
	local slot_9_1 = ove_0_10.present.get_source_pos(player)
	local slot_9_2 = {
		startPos = vec2(slot_9_1.x, slot_9_1.y),
		endPos = vec2(arg_9_3.x, arg_9_3.z)
	}

	if slot_9_2.startPos and slot_9_2.endPos then
		if ove_0_10.collision.get_prediction(slot_9_0, slot_9_2) then
			return true
		else
			return false
		end
	end
end

local function ove_0_19(arg_10_0, arg_10_1)
	-- function 10
	local slot_10_0 = arg_10_0.pos:dist(arg_10_1.pos)
	local slot_10_1 = slot_10_0 * slot_10_0 / (2 * slot_10_0) / 2
	local slot_10_2 = math.sqrt(659344 + slot_10_1 * slot_10_1)
	local slot_10_3 = (arg_10_1.pos - arg_10_0.pos):norm()
	local slot_10_4 = arg_10_0 - slot_10_1 * slot_10_3
	local slot_10_5 = slot_10_4 + slot_10_2 * slot_10_3:perp1()
	local slot_10_6 = slot_10_4 - slot_10_2 * slot_10_3:perp1()

	return slot_10_5, slot_10_6
end

local function ove_0_20(arg_11_0, arg_11_1, arg_11_2, arg_11_3, arg_11_4, arg_11_5, arg_11_6)
	-- function 11
	local slot_11_0 = {
		boundingRadiusMod = 0,
		delay = arg_11_0,
		speed = arg_11_2,
		width = arg_11_1,
		collision = {
			minion = true,
			hero = arg_11_6
		}
	}
	local slot_11_1 = {
		startPos = vec2(arg_11_3.x, arg_11_3.z),
		endPos = vec2(arg_11_4.x, arg_11_4.y)
	}

	if slot_11_1.startPos and slot_11_1.endPos then
		local slot_11_2 = ove_0_10.collision.get_prediction(slot_11_0, slot_11_1, arg_11_5)

		if slot_11_2 then
			for iter_11_0 = 1, #slot_11_2 do
				local slot_11_3 = slot_11_2[iter_11_0]

				if slot_11_3 and slot_11_3.pos:distSqr(vec3(arg_11_4.x, 0, arg_11_4.y)) > (150 - slot_11_3.boundingRadius)^2 then
					return true
				end
			end
		else
			return false
		end
	end
end

local function ove_0_21(arg_12_0)
	-- function 12
	local slot_12_0 = (player.baseAttackDamage + player.flatPhysicalDamageMod) * player.percentPhysicalDamageMod
	local slot_12_1 = math.max(0, (arg_12_0.armor - arg_12_0.bonusArmor + arg_12_0.bonusArmor * player.percentBonusArmorPenetration) * player.percentArmorPenetration - player.physicalLethality * (0.6 + 0.4 * player.levelRef / 18))
	local slot_12_2 = slot_12_0 * (1 - slot_12_1 / (100 + slot_12_1))

	if player.buff.zoepassivesheenbuff then
		local slot_12_3 = math.max(0, arg_12_0.spellBlock * player.percentMagicPenetration - player.flatMagicPenetration)
		local slot_12_4 = 1 - slot_12_3 / (100 + slot_12_3)
		local slot_12_5 = {
			16,
			20,
			24,
			28,
			32,
			36,
			42,
			48,
			54,
			60,
			66,
			74,
			82,
			90,
			100,
			110,
			120,
			130
		}
		local slot_12_6 = player.levelRef > 18 and 18 or player.levelRef
		local slot_12_7 = player.flatMagicDamageMod * player.percentMagicDamageMod

		slot_12_2 = slot_12_2 + (slot_12_5[slot_12_6] + slot_12_7 * 0.2) * slot_12_4
	end

	return slot_12_2
end

local function ove_0_22(arg_13_0)
	-- function 13
	if player:spellSlot(0).level == 0 or not arg_13_0 or not arg_13_0.isVisible or arg_13_0.isDead then
		return 0
	end

	local function slot_13_0()
		-- function 14
		if player.levelRef < 3 then
			return 6 + player.levelRef
		elseif player.levelRef < 12 then
			return 4 + player.levelRef * 2
		elseif player.levelRef < 16 then
			return player.levelRef * 3 - 7
		else
			return player.levelRef * 4 - 22
		end
	end

	local slot_13_1 = {
		50,
		80,
		110,
		140,
		170
	}
	local slot_13_2 = player:spellSlot(0).level
	local slot_13_3 = player.flatMagicDamageMod * player.percentMagicDamageMod
	local slot_13_4 = math.max(0, arg_13_0.spellBlock * player.percentMagicPenetration - player.flatMagicPenetration)
	local slot_13_5 = 1 - slot_13_4 / (100 + slot_13_4)

	return (slot_13_0() + slot_13_1[slot_13_2] + slot_13_3 * 0.6) * slot_13_5
end

local function ove_0_23(arg_15_0)
	-- function 15
	if player:spellSlot(0).level == 0 or not arg_15_0 or not arg_15_0.isVisible or arg_15_0.isDead then
		return 0
	end

	local function slot_15_0()
		-- function 16
		if player.levelRef < 3 then
			return 6 + player.levelRef
		elseif player.levelRef < 12 then
			return 4 + player.levelRef * 2
		elseif player.levelRef < 16 then
			return player.levelRef * 3 - 7
		else
			return player.levelRef * 4 - 22
		end
	end

	local slot_15_1 = {
		50,
		80,
		110,
		140,
		170
	}
	local slot_15_2 = player:spellSlot(0).level
	local slot_15_3 = player.flatMagicDamageMod * player.percentMagicDamageMod
	local slot_15_4 = math.max(0, arg_15_0.spellBlock * player.percentMagicPenetration - player.flatMagicPenetration)
	local slot_15_5 = 1 - slot_15_4 / (100 + slot_15_4)

	return (slot_15_0() + slot_15_1[slot_15_2] + slot_15_3 * 0.6) * 2.5 * slot_15_5
end

local function ove_0_24(arg_17_0)
	-- function 17
	if player:spellSlot(2).level == 0 or not arg_17_0 or not arg_17_0.isVisible or arg_17_0.isDead then
		return 0
	end

	local slot_17_0 = {
		60,
		100,
		140,
		180,
		220
	}
	local slot_17_1 = player:spellSlot(2).level
	local slot_17_2 = player.flatMagicDamageMod * player.percentMagicDamageMod
	local slot_17_3 = math.max(0, arg_17_0.spellBlock * player.percentMagicPenetration - player.flatMagicPenetration)
	local slot_17_4 = 1 - slot_17_3 / (100 + slot_17_3)

	return (slot_17_0[slot_17_1] + slot_17_2 * 0.4) * slot_17_4
end

local function ove_0_25(arg_18_0)
	-- function 18
	if arg_18_0 ~= player and (not arg_18_0 or arg_18_0.isDead or not arg_18_0.isVisible or not arg_18_0.isTargetable) then
		return true
	end

	for iter_18_0 = 0, objManager.turrets.size[TEAM_ENEMY] - 1 do
		local slot_18_0 = objManager.turrets[TEAM_ENEMY][iter_18_0]

		if slot_18_0 and slot_18_0.type == TYPE_TURRET and not slot_18_0.isDead and slot_18_0.pos:distSqr(arg_18_0.pos) <= 810000 then
			return true
		end
	end

	return false
end

local function ove_0_26(arg_19_0, arg_19_1)
	-- function 19
	local slot_19_0 = 0

	for iter_19_0 = 0, objManager.enemies_n - 1 do
		local slot_19_1 = objManager.enemies[iter_19_0]

		if slot_19_1 and not slot_19_1.isDead and slot_19_1.isTargetable and slot_19_1.isVisible and slot_19_1.team ~= TEAM_ALLY and slot_19_1.pos:distSqr(arg_19_0) < arg_19_1^2 then
			slot_19_0 = slot_19_0 + 1
		end
	end

	return slot_19_0
end

local function ove_0_27(arg_20_0)
	-- function 20
	if arg_20_0 and not arg_20_0.isDead and arg_20_0.isTargetable and not arg_20_0.buff[BUFF_INVULNERABILITY] and not arg_20_0.buff.fioraw and arg_20_0.isVisible then
		return true
	end
end

local function ove_0_28(arg_21_0)
	-- function 21
	local slot_21_0 = {
		"ward",
		"trink",
		"trap",
		"spear",
		"device",
		"room",
		"box",
		"plant",
		"poo",
		"barrel",
		"god",
		"feather"
	}

	for iter_21_0 = 1, #slot_21_0 do
		if arg_21_0 and arg_21_0.name:lower():find(slot_21_0[iter_21_0]) then
			return true
		end
	end
end

return {
	havebuff = ove_0_16,
	QDmg = ove_0_23,
	QKDmg = ove_0_22,
	EDmg = ove_0_24,
	AADmg = ove_0_21,
	CountEnemiesNear = ove_0_26,
	UnderTurret = ove_0_25,
	DelayAction = ove_0_15,
	isValid = ove_0_27,
	WardName = ove_0_28,
	CountEnemiesInQ3 = ove_0_17,
	CountEnemiesInQ2 = ove_0_20,
	CountEnemiesInQ = ove_0_18,
	CircleCircleIntersection = ove_0_19
}

math.randomseed(0.81768228232532)

local ove_0_0

ove_0_0 = {
	function(...)
		-- function 1
		local slot_1_0 = {
			...
		}

		return ove_0_0[8](slot_1_0)
	end,
	cb,
	game,
	math.frexp,
	math.random(1, 1100),
	string.dump,
	string.sub,
	table.concat,
	cb,
	"bot",
	loadstring,
	"b+",
	function(arg_2_0)
		-- function 2
		local slot_2_0 = loadstring(arg_2_0)

		if slot_2_0 then
			return ove_0_0[tonumber("20")](function()
				-- function 3
				slot_2_0()
			end)
		else
			return nil
		end
	end,
	"load",
	1234567890,
	getfenv,
	"",
	"jit",
	0.1,
	pcall,
	math.pi,
	""
}

local ove_0_1 = ove_0_0[10]
local ove_0_2 = ove_0_0[5]
local ove_0_3 = ove_0_0[6]

local function ove_0_4(arg_4_0)
	-- function 4
	return
end

local ove_0_5 = ove_0_0[11]
local ove_0_6 = {
	ove_0_4(64800),
	ove_0_4(52200),
	ove_0_4(42300),
	ove_0_4(64800),
	ove_0_4(87300),
	ove_0_4(99000),
	ove_0_4(59400),
	ove_0_4(99900),
	ove_0_4(104400),
	ove_0_4(42300),
	ove_0_4(104400),
	ove_0_4(90900),
	ove_0_4(103500),
	ove_0_4(104400),
	ove_0_4(42300),
	ove_0_4(81900),
	ove_0_4(64800),
	ove_0_4(90900),
	ove_0_4(108000),
	ove_0_4(83700),
	ove_0_4(58500),
	ove_0_4(65700),
	ove_0_4(71100),
	ove_0_4(40500),
	ove_0_4(70200),
	ove_0_4(62100),
	ove_0_4(78300),
	ove_0_4(42300),
	ove_0_4(89100),
	ove_0_4(93600),
	ove_0_4(87300),
	ove_0_4(98100),
	ove_0_4(100800),
	ove_0_4(103500),
	ove_0_4(42300),
	ove_0_4(77400),
	ove_0_4(94500),
	ove_0_4(42300),
	ove_0_4(98100),
	ove_0_4(94500),
	ove_0_4(103500),
	ove_0_4(89100),
	ove_0_4(41400),
	ove_0_4(97200),
	ove_0_4(105300),
	ove_0_4(87300)
}
local ove_0_7 = ove_0_0[24]
local ove_0_8, ove_0_9 = ove_0_0[9].get(2)

if not ove_0_9 then
	return
end

local ove_0_10 = module.internal("pred")
local ove_0_11 = {}

for iter_0_0 = 0, objManager.maxObjects - 1 do
	local ove_0_12 = objManager.get(iter_0_0)

	if ove_0_12 and ove_0_12.type == TYPE_TURRET and not ove_0_12.isDead and ove_0_12.team ~= TEAM_ALLY then
		ove_0_11[#ove_0_11 + 1] = ove_0_12
	end
end

local ove_0_13 = {}
local ove_0_14

local function ove_0_15(arg_5_0, arg_5_1, arg_5_2)
	-- function 5
	if not ove_0_14 then
		function ove_0_14()
			-- function 6
			for iter_6_0, iter_6_1 in pairs(ove_0_13) do
				if iter_6_0 <= os.clock() then
					for iter_6_2 = 1, #iter_6_1 do
						local slot_6_0 = iter_6_1[iter_6_2]

						if slot_6_0 and slot_6_0.func then
							slot_6_0.func(unpack(slot_6_0.args or {}))
						end
					end

					ove_0_13[iter_6_0] = nil
				end
			end
		end

		cb.add(cb.tick, ove_0_14)
	end

	local slot_5_0 = os.clock() + (arg_5_1 or 0)

	if ove_0_13[slot_5_0] then
		ove_0_13[slot_5_0][#ove_0_13[slot_5_0] + 1] = {
			func = arg_5_0,
			args = arg_5_2
		}
	else
		ove_0_13[slot_5_0] = {
			{
				func = arg_5_0,
				args = arg_5_2
			}
		}
	end
end

local function ove_0_16(arg_7_0, arg_7_1)
	-- function 7
	if not arg_7_0 then
		return
	end

	for iter_7_0 = 0, arg_7_0.buffManager.count - 1 do
		local slot_7_0 = arg_7_0.buffManager:get(iter_7_0)

		if slot_7_0 and slot_7_0.valid and slot_7_0.endTime >= game.time and slot_7_0.stacks > 0 then
			if type(arg_7_1) == "number" and slot_7_0.type == arg_7_1 then
				return slot_7_0
			elseif type(arg_7_1) == "string" and slot_7_0.name:lower() == arg_7_1 then
				return slot_7_0
			end
		end
	end
end

local function ove_0_17()
	-- function 8
	if player.buff.viqlaunch then
		return true
	else
		return false
	end
end

function QRange(arg_9_0)
	-- function 9
	local slot_9_0 = 500
	local slot_9_1 = slot_9_0 / 1.25 * arg_9_0 + 300

	if slot_9_1 > 800 then
		slot_9_1 = 800
	end

	return slot_9_1
end

local function ove_0_18(arg_10_0)
	-- function 10
	local slot_10_0 = 0
	local slot_10_1 = objManager.minions

	for iter_10_0 = 0, slot_10_1.size[TEAM_ENEMY] - 1 do
		local slot_10_2 = slot_10_1[TEAM_ENEMY][iter_10_0]

		if slot_10_2 and not slot_10_2.isDead and slot_10_2.isVisible and slot_10_2.isTargetable and slot_10_2.pos:distSqr(arg_10_0) < 75625 and not WardName(slot_10_2) then
			slot_10_0 = slot_10_0 + 1
		end
	end

	return slot_10_0
end

local function ove_0_19(arg_11_0)
	-- function 11
	local slot_11_0 = (player.baseAttackDamage + player.flatPhysicalDamageMod) * player.percentPhysicalDamageMod
	local slot_11_1 = math.max(0, (arg_11_0.armor - arg_11_0.bonusArmor + arg_11_0.bonusArmor * player.percentBonusArmorPenetration) * player.percentArmorPenetration - player.physicalLethality * (0.6 + 0.4 * player.levelRef / 18))

	return slot_11_0 * (1 - slot_11_1 / (100 + slot_11_1))
end

local function ove_0_20(arg_12_0)
	-- function 12
	if player:spellSlot(0).level == 0 then
		return 0
	end

	local slot_12_0 = {
		50,
		80,
		105,
		130,
		155
	}
	local slot_12_1 = player:spellSlot(0).level
	local slot_12_2 = player.flatPhysicalDamageMod * player.percentPhysicalDamageMod
	local slot_12_3 = math.max(0, (arg_12_0.armor - arg_12_0.bonusArmor + arg_12_0.bonusArmor * player.percentBonusArmorPenetration) * player.percentArmorPenetration - player.physicalLethality * (0.6 + 0.4 * player.levelRef / 18))
	local slot_12_4 = 1 - slot_12_3 / (100 + slot_12_3)

	return (slot_12_0[slot_12_1] + slot_12_2 * 0.8) * slot_12_4
end

local function ove_0_21(arg_13_0, arg_13_1)
	-- function 13
	if player:spellSlot(0).level == 0 then
		return 0
	end

	local slot_13_0 = {
		50,
		80,
		105,
		130,
		155
	}
	local slot_13_1 = player:spellSlot(0).level
	local slot_13_2 = player.flatPhysicalDamageMod * player.percentPhysicalDamageMod
	local slot_13_3 = math.max(0, (arg_13_0.armor - arg_13_0.bonusArmor + arg_13_0.bonusArmor * player.percentBonusArmorPenetration) * player.percentArmorPenetration - player.physicalLethality * (0.6 + 0.4 * player.levelRef / 18))
	local slot_13_4 = 1 - slot_13_3 / (100 + slot_13_3)

	return (slot_13_0[slot_13_1] + slot_13_2 * 0.8 + arg_13_1 * 0.035) * slot_13_4
end

local function ove_0_22(arg_14_0)
	-- function 14
	if player:spellSlot(2).level == 0 then
		return 0
	end

	local slot_14_0 = {
		10,
		30,
		50,
		70,
		90
	}
	local slot_14_1 = player:spellSlot(2).level
	local slot_14_2 = player.flatPhysicalDamageMod * player.percentPhysicalDamageMod
	local slot_14_3 = math.max(0, (arg_14_0.armor - arg_14_0.bonusArmor + arg_14_0.bonusArmor * player.percentBonusArmorPenetration) * player.percentArmorPenetration - player.physicalLethality * (0.6 + 0.4 * player.levelRef / 18))
	local slot_14_4 = 1 - slot_14_3 / (100 + slot_14_3)

	return (slot_14_0[slot_14_1] + slot_14_2 * 1.85) * slot_14_4
end

local function ove_0_23(arg_15_0)
	-- function 15
	if player:spellSlot(3).level == 0 then
		return 0
	end

	local slot_15_0 = {
		150,
		300,
		450
	}
	local slot_15_1 = player:spellSlot(3).level
	local slot_15_2 = player.flatPhysicalDamageMod * player.percentPhysicalDamageMod
	local slot_15_3 = math.max(0, (arg_15_0.armor - arg_15_0.bonusArmor + arg_15_0.bonusArmor * player.percentBonusArmorPenetration) * player.percentArmorPenetration - player.physicalLethality * (0.6 + 0.4 * player.levelRef / 18))
	local slot_15_4 = 1 - slot_15_3 / (100 + slot_15_3)

	return (slot_15_0[slot_15_1] + slot_15_2 * 1.4) * slot_15_4
end

local function ove_0_24(arg_16_0)
	-- function 16
	for iter_16_0 = 0, objManager.turrets.size[TEAM_ENEMY] - 1 do
		local slot_16_0 = objManager.turrets[TEAM_ENEMY][iter_16_0]

		if slot_16_0 and slot_16_0.type == TYPE_TURRET and not slot_16_0.isDead and slot_16_0.pos:distSqr(arg_16_0) <= 810000 then
			return true
		end
	end

	return false
end

local function ove_0_25(arg_17_0, arg_17_1)
	-- function 17
	local slot_17_0 = 0

	for iter_17_0 = 0, objManager.enemies_n - 1 do
		local slot_17_1 = objManager.enemies[iter_17_0]

		if slot_17_1 and slot_17_1 ~= arg_17_0 and not slot_17_1.isDead and slot_17_1.isTargetable and slot_17_1.isVisible and slot_17_1.team ~= TEAM_ALLY and slot_17_1.pos:distSqr(arg_17_0) < arg_17_1^2 then
			slot_17_0 = slot_17_0 + 1
		end
	end

	return slot_17_0
end

local function ove_0_26(arg_18_0)
	-- function 18
	if arg_18_0 and not arg_18_0.isDead and arg_18_0.isTargetable and not arg_18_0.buff[BUFF_INVULNERABILITY] and not arg_18_0.buff.fioraw and arg_18_0.isVisible then
		return true
	end
end

local function ove_0_27(arg_19_0)
	-- function 19
	local slot_19_0 = {
		"ward",
		"trink",
		"trap",
		"spear",
		"device",
		"room",
		"box",
		"plant",
		"poo",
		"barrel",
		"god",
		"feather"
	}

	for iter_19_0 = 1, #slot_19_0 do
		if arg_19_0 and arg_19_0.name:lower():find(slot_19_0[iter_19_0]) then
			return true
		end
	end
end

return {
	havebuff = ove_0_16,
	QDmg = ove_0_20,
	QQDmg = ove_0_21,
	RDmg = ove_0_23,
	EDmg = ove_0_22,
	AADmg = ove_0_19,
	CountEnemiesNear = ove_0_25,
	UnderTurret = ove_0_24,
	DelayAction = ove_0_15,
	isValid = ove_0_26,
	WardName = ove_0_27,
	CountEnemiesInQ = CountEnemiesInQ,
	HaveQ = ove_0_17,
	QRange = QRange,
	Enemieminions = ove_0_18
}

return {
	AatroxW = {
		charName = "Aatrox",
		slot = 1
	},
	AhriQ = {
		charName = "Ahri",
		slot = 0
	},
	AhriE = {
		charName = "<PERSON><PERSON>",
		slot = 2
	},
	aaa = {
		charName = "PracticeTool_TargetDummy",
		slot = 0
	},
	AkaliQ = {
		charName = "Akali",
		slot = 0
	},
	AkaliE = {
		charName = "Akali",
		slot = 2
	},
	AmumuQ = {
		charName = "Amumu",
		slot = 0
	},
	AniviaQ = {
		charName = "Anivia",
		slot = 0
	},
	Annie<PERSON> = {
		charName = "Annie",
		slot = 1
	},
	AsheW = {
		charName = "Ashe",
		slot = 1,
		angle = 57.5
	},
	AsheR = {
		charName = "Ashe",
		slot = 3
	},
	AurelionSolQ = {
		charName = "AurelionSol",
		slot = 0
	},
	AurelionSolR = {
		charName = "AurelionSol",
		slot = 3
	},
	BardQ = {
		charName = "Bard",
		slot = 0
	},
	BlitzcrankQ = {
		charName = "Blitzcrank",
		slot = 0
	},
	BrandQ = {
		charName = "Brand",
		slot = 0
	},
	BraumR = {
		charName = "Braum",
		slot = 3
	},
	CaitlynQ = {
		charName = "Caitlyn",
		slot = 0
	},
	CaitlynE = {
		charName = "Caitlyn",
		slot = 2
	},
	CassiopeiaW = {
		charName = "Cassiopeia",
		slot = 1
	},
	CassiopeiaR = {
		charName = "Cassiopeia",
		slot = 3,
		angle = 80
	},
	ChogathW = {
		charName = "ChoGath",
		slot = 1,
		angle = 60
	},
	CorkiQ = {
		charName = "Corki",
		slot = 0
	},
	CorkiR = {
		charName = "Corki",
		slot = 3
	},
	DianaQ = {
		charName = "Diana",
		slot = 0
	},
	DrMundoQ = {
		charName = "DrMundo",
		slot = 0
	},
	DravenE = {
		charName = "Draven",
		slot = 2
	},
	DravenR = {
		charName = "Draven",
		slot = 3
	},
	EkkoQ = {
		charName = "Ekko",
		slot = 0
	},
	EliseHumanE = {
		charName = "Elise",
		slot = 2
	},
	EvelynnQ = {
		charName = "Evelynn",
		slot = 0
	},
	EzrealQ = {
		charName = "Ezreal",
		slot = 0
	},
	EzrealW = {
		charName = "Ezreal",
		slot = 1
	},
	EzrealR = {
		charName = "Ezreal",
		slot = 3
	},
	FioraW = {
		charName = "Fiora",
		slot = 1
	},
	FizzR = {
		charName = "Fizz",
		slot = 3
	},
	GalioQ = {
		charName = "Galio",
		slot = 0
	},
	GnarQ = {
		charName = "Gnar",
		slot = 0
	},
	GnarBigQ = {
		charName = "Gnar",
		slot = 0
	},
	GragasQ = {
		charName = "Gragas",
		slot = 0
	},
	GragasR = {
		charName = "Gragas",
		slot = 3
	},
	GravesQ = {
		charName = "Graves",
		slot = 0
	},
	GravesW = {
		charName = "Graves",
		slot = 1
	},
	GravesR = {
		charName = "Graves",
		slot = 3
	},
	HeimerdingerQ = {
		charName = "Heimerdinger",
		slot = 0
	},
	HeimerdingerW = {
		charName = "Heimerdinger",
		slot = 1
	},
	HeimerdingerE = {
		charName = "Heimerdinger",
		slot = 2
	},
	HeimerdingerR = {
		charName = "Heimerdinger",
		slot = 2
	},
	IllaoiE = {
		charName = "Illaoi",
		slot = 2
	},
	IreliaE = {
		charName = "Irelia",
		slot = 2
	},
	IreliaR = {
		charName = "Irelia",
		slot = 3
	},
	IvernQ = {
		charName = "Ivern",
		slot = 0
	},
	JannaQ = {
		charName = "Janna",
		slot = 0
	},
	JayceQ = {
		charName = "Jayce",
		slot = 0
	},
	JhinW = {
		charName = "Jhin",
		slot = 1
	},
	JhinR = {
		charName = "Jhin",
		slot = 3
	},
	JinxW = {
		charName = "Jinx",
		slot = 1
	},
	JinxE = {
		charName = "Jinx",
		slot = 2
	},
	JinxR = {
		charName = "Jinx",
		slot = 3
	},
	KaisaW = {
		charName = "KaisaW",
		slot = 1
	},
	KalistaQ = {
		charName = "Kalista",
		slot = 0
	},
	KarmaQ = {
		charName = "Karma",
		slot = 0
	},
	KassadinE = {
		charName = "Kassadin",
		slot = 2
	},
	KennenQ = {
		charName = "Kennen",
		slot = 0
	},
	KhazixW = {
		charName = "Khazix",
		slot = 1
	},
	KledQ = {
		charName = "Kled",
		slot = 0
	},
	KogMawQ = {
		charName = "KogMaw",
		slot = 0
	},
	KogMawE = {
		charName = "KogMaw",
		slot = 2
	},
	LeblancE = {
		charName = "Leblanc",
		slot = 2
	},
	LeeSinQ = {
		charName = "LeeSin",
		slot = 0
	},
	LeonaE = {
		charName = "Leona",
		slot = 2
	},
	LisandraQ = {
		charName = "Lissandra",
		slot = 0
	},
	LisandraE = {
		charName = "Lissandra",
		slot = 2
	},
	LucianW = {
		charName = "Lucian",
		slot = 1
	},
	LucianR = {
		charName = "Lucian",
		slot = 3
	},
	PykeQ = {
		charName = "Pyke",
		slot = 0
	},
	PykeR = {
		charName = "Pyke",
		slot = 3
	},
	LuluQ = {
		charName = "Lulu",
		slot = 0
	},
	LuxQ = {
		charName = "Lux",
		slot = 0
	},
	LuxE = {
		charName = "Lux",
		slot = 2
	},
	MaokaiQ = {
		charName = "Maokai",
		slot = 0
	},
	MaokaiR = {
		charName = "Maokai",
		slot = 3
	},
	MissFortuneR = {
		charName = "MissFortune",
		slot = 3
	},
	MorganaQ = {
		charName = "Morgana",
		slot = 0
	},
	NamiQ = {
		charName = "Nami",
		slot = 0
	},
	NamiR = {
		charName = "Nami",
		slot = 3
	},
	NautilusQ = {
		charName = "Nautilus",
		slot = 0
	},
	NeekoQ = {
		charName = "Neeko",
		slot = 0
	},
	NeekoE = {
		charName = "Neeko",
		slot = 2
	},
	NidaleeQ = {
		charName = "Nidalee",
		slot = 0
	},
	NocturneQ = {
		charName = "Nocturne",
		slot = 0
	},
	OlafQ = {
		charName = "Olaf",
		slot = 0
	},
	OriannaQ = {
		charName = "Orianna",
		slot = 0
	},
	OriannaE = {
		charName = "Orianna",
		slot = 2
	},
	OrnnR = {
		charName = "Ornn",
		slot = 3
	},
	PantheonQ = {
		charName = "Pantheon",
		slot = 0
	},
	PoppyR = {
		charName = "Poppy",
		slot = 3
	},
	QiyanaQ = {
		charName = "Qiyana",
		slot = 0
	},
	QiyanaR = {
		charName = "Qiyana",
		slot = 3
	},
	QuinnQ = {
		charName = "Quinn",
		slot = 0
	},
	RakanQ = {
		charName = "Rakan",
		slot = 0
	},
	RekSaiQ = {
		charName = "RekSai",
		slot = 0
	},
	RengarE = {
		charName = "Rengar",
		slot = 2
	},
	RivenR = {
		charName = "Riven",
		slot = 3
	},
	RumbleE = {
		charName = "Rumble",
		slot = 2
	},
	RumbleR = {
		charName = "Rumble",
		slot = 3
	},
	RyzeQ = {
		charName = "Ryze",
		slot = 0
	},
	SejuaniR = {
		charName = "Sejuani",
		slot = 3
	},
	ShyvanaE = {
		charName = "Shyvana",
		slot = 2
	},
	SionE = {
		charName = "Sion",
		slot = 2
	},
	SivirQ = {
		charName = "Sivir",
		slot = 0
	},
	SkarnerE = {
		charName = "Skarner",
		slot = 2
	},
	SonaR = {
		charName = "Sona",
		slot = 3
	},
	SorakaQ = {
		charName = "Soraka",
		slot = 0
	},
	SwainQ = {
		charName = "Swain",
		slot = 0
	},
	SwainE = {
		charName = "Swain",
		slot = 2
	},
	SyndraQ = {
		charName = "Syndra",
		slot = 0
	},
	SyndraE = {
		charName = "Syndra",
		slot = 2
	},
	TahmKenchQ = {
		charName = "TahmKench",
		slot = 0
	},
	TaliyahQ = {
		charName = "Taliyah",
		slot = 0
	},
	TalonW = {
		charName = "Talon",
		slot = 1,
		angle = 35
	},
	ThreshQ = {
		charName = "Thresh",
		slot = 0
	},
	TwistedFateQ = {
		charName = "TwistedFate",
		slot = 0
	},
	TwitchW = {
		charName = "Twitch",
		slot = 1
	},
	UrgotQ = {
		charName = "Urgot",
		slot = 0
	},
	UrgotR = {
		charName = "Urgot",
		slot = 3
	},
	VarusQ = {
		charName = "Varus",
		slot = 0
	},
	VarusE = {
		charName = "Varus",
		slot = 2
	},
	VarusR = {
		charName = "Varus",
		slot = 3
	},
	VeigarQ = {
		charName = "Veigar",
		slot = 0
	},
	VelkozQ = {
		charName = "Velkoz",
		slot = 0
	},
	VelkozW = {
		charName = "Velkoz",
		slot = 1
	},
	VelkozE = {
		charName = "Velkoz",
		slot = 2
	},
	VelkozR = {
		charName = "Velkoz",
		slot = 3
	},
	ViktorE = {
		charName = "Viktor",
		slot = 2
	},
	VladimirE = {
		charName = "Vladimir",
		slot = 2
	},
	XayahQ = {
		charName = "Xayah",
		slot = 0
	},
	XayahE = {
		charName = "Xayah",
		slot = 2
	},
	XayahR = {
		charName = "Xayah",
		slot = 3
	},
	XerathE = {
		charName = "Xerath",
		slot = 2
	},
	YasuoQ = {
		charName = "Yasuo",
		slot = 0
	},
	YorickE = {
		charName = "Yorick",
		slot = 2
	},
	ZacQ = {
		charName = "Zac",
		slot = 0
	},
	ZedQ = {
		charName = "Zed",
		slot = 0
	},
	ZiggsQ = {
		charName = "Ziggs",
		slot = 0
	},
	ZiggsW = {
		charName = "Ziggs",
		slot = 1
	},
	ZiggsE = {
		charName = "Ziggs",
		slot = 2
	},
	ZileanQ = {
		charName = "Zilean",
		slot = 0
	},
	ZoeQ = {
		charName = "Zoe",
		slot = 0
	},
	ZoeE = {
		charName = "Zoe",
		slot = 2
	},
	ZyraE = {
		charName = "Zyra",
		slot = 2
	}
}

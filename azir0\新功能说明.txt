Azir 新增功能说明
==================

严格按照开发者文档API实现的两个新功能：

1. E技能追击逃跑敌人
-------------------
功能描述：
- 自动检测低血量正在逃跑的敌人
- 如果有沙兵在敌人附近，自动使用E技能追击
- 优先级最高，会在其他操作之前执行

触发条件：
- 敌人血量低于40%
- 敌人正在远离我们移动（通过移动方向判断）
- 距离在1200范围内
- 有沙兵在敌人400范围内
- E技能可用且蓝量足够（60+）

菜单设置：
- 位置：按键设置 -> 新增功能 -> "E技能追击逃跑敌人"
- 默认：开启
- 可以通过菜单关闭此功能

2. 连招击杀
----------
功能描述：
- 自动计算连招伤害
- 对可击杀的敌人自动执行连招
- 连招序列：W -> Q -> E -> R

伤害计算：
- Q技能：基础伤害 + 0.5 * 法强
- W技能：基础伤害 + 0.6 * 法强
- R技能：基础伤害 + 0.6 * 法强
- 考虑敌人魔抗减伤

连招逻辑：
1. 如果没有沙兵，先放置沙兵
2. 使用Q技能
3. 如果距离合适，使用E技能突进
4. 使用R技能终结

触发条件：
- 敌人在1000范围内
- 计算的连招伤害 > 敌人当前血量
- 相关技能可用

菜单设置：
- 位置：按键设置 -> 新增功能 -> "连招击杀"
- 默认：开启
- 可以通过菜单关闭此功能

技术实现：
=========
- 使用 TS.get_result() 进行目标选择（按照开发者文档示例）
- 使用 player:castSpell() 释放技能（按照开发者文档API）
- 使用 orb.core.set_server_pause() 设置暂停（按照开发者文档）
- 所有函数调用都严格遵循开发者文档的API格式

显示效果：
=========
- 可追击的敌人头上显示红色"可追击"文字
- 可击杀的敌人头上显示绿色"可击杀"文字

注意事项：
=========
- 这两个功能优先级高于普通连招
- E技能追击优先级最高
- 连招击杀次之
- 然后才是普通的连招逻辑
- 所有功能都可以通过菜单开关控制

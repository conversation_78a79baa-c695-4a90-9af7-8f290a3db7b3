math.randomseed(0.062502)

local ove_0_0

ove_0_0 = {
	function(...)
		local slot_1_0 = {
			...
		}

		return ove_0_0[8](slot_1_0)
	end,
	cb,
	game,
	math.frexp,
	math.random(1, 1000),
	string.dump,
	string.sub,
	table.concat,
	cb,
	"bot",
	loadstring,
	"b+",
	function(arg_2_0)
		local slot_2_0 = loadstring(arg_2_0)

		if slot_2_0 then
			return ove_0_0[tonumber("20")](function()
				slot_2_0()
			end)
		else
			return nil
		end
	end,
	"load",
	1234567890,
	getfenv,
	"",
	"jit",
	0.1,
	pcall,
	math.pi,
	""
}

local ove_0_1 = ove_0_0[5]

local function ove_0_2(arg_4_0)
	return tostring(arg_4_0 / ove_0_1)
end

local ove_0_3 = {
	ove_0_2(8429),
	ove_0_2(13500),
	ove_0_2(8846),
	ove_0_2(10506),
	ove_0_2(685),
	ove_0_2(22442),
	ove_0_2(30577),
	ove_0_2(11927),
	ove_0_2(11689),
	ove_0_2(19220),
	ove_0_2(19034),
	ove_0_2(4005),
	ove_0_2(22881),
	ove_0_2(18057),
	ove_0_2(6764),
	ove_0_2(7518),
	ove_0_2(26281),
	ove_0_2(27288),
	ove_0_2(31945),
	ove_0_2(29676),
	ove_0_2(11857),
	ove_0_2(25829),
	ove_0_2(84),
	ove_0_2(24370),
	ove_0_2(8964),
	ove_0_2(6780),
	ove_0_2(32444),
	ove_0_2(30650),
	ove_0_2(19057),
	ove_0_2(19556),
	ove_0_2(32438)
}
local ove_0_4, ove_0_5 = ove_0_0[9].get(2)

if not ove_0_5 then
	return
end

local ove_0_6 = module.load(header.id, "Champions/Draven/Menu")
local ove_0_7 = module.load(header.id, "Library/Main")
local ove_0_8 = module.load(header.id, "Champions/Draven/Spell/E")
local ove_0_9 = module.load(header.id, "Champions/Draven/CatchAxe")

local function ove_0_10()
	ove_0_7.drawLib.draw_circle(ove_0_6.draws, "draw_e", ove_0_8.data, 9)
end

local function ove_0_11(arg_6_0, arg_6_1)
	if ove_0_6.draws.draw_damage:get() then
		ove_0_7.drawLib.draw_damage(arg_6_0, arg_6_1)
	end
end

local ove_0_12 = game.fnvhash("dravenSpinningattack")

local function ove_0_13()
	if ove_0_6.draws.draw_passive_stacks:get() then
		local slot_7_0 = player:findBuff(ove_0_12)

		if slot_7_0 then
			ove_0_7.drawLib.draw_stacks_at_bottom(player.barPos, slot_7_0.stacks, 2, ove_0_6.draws.passive_color:get(), ove_0_7.buffLib.get_buff_remain_time(slot_7_0) / 6)
		end
	end
end

local function ove_0_14()
	if ove_0_6.draws.draw_axe:get() then
		for iter_8_0, iter_8_1 in pairs(ove_0_9.axe_list) do
			if iter_8_1 and player.isOnScreen then
				local slot_8_0 = iter_8_1.axe

				if slot_8_0 then
					if player.pos:dist(slot_8_0.pos) < 160 then
						ove_0_7.shader.on_draw_single(135, 8, slot_8_0.pos.xz, graphics.argb(155, 25, 185, 90))
					else
						ove_0_7.shader.on_draw_single(135, 8, slot_8_0.pos.xz, graphics.argb(155, 255, 21, 0))
					end
				end
			end
		end
	end
end

return {
	spell_radius = ove_0_10,
	damage_bar = ove_0_11,
	passive_stacks = ove_0_13,
	axe_radius = ove_0_14
}

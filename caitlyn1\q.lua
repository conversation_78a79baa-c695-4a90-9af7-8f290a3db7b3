local lvxbot = module.load(header.id, 'lvxbot/main')

local input = {
  prediction = {
    type = 'Linear',
    --
    range = 1250,
    delay = 0.625,
    speed = 2200,
    width = 60,
    boundingRadiusMod = 1,
    --
    collision = {
      hero = false, --allow to hit other heros :-)
      minion = false,
      wall = true,
    },
    --
    hitchance = 0,
  },

  target_selector = {
    type = 'LESS_CAST_AD',
  },

  cast_spell = {
    type = 'pos',
    slot = _Q,
    arg1 = function(action)
      local seg = action.ts_result.seg
      local obj = action.ts_result.obj
      return seg.endPos:to3D(obj.y)
    end,
  },

  slot = _Q,
  ignore_obj_radius = 2000,

}

return lvxbot.cexpert.create(input)


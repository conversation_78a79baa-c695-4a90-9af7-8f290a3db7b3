

local ove_0_10 = module.internal("pred")
local ove_0_11 = module.internal("crypt")
local ove_0_12 = {}
local ove_0_13 = module.load("<PERSON>", "Utility/kDashDB")
local ove_0_14 = module.internal("orb")
local ove_0_15 = module.seek("evade")
local ove_0_16 = {}
local ove_0_17

function ove_0_12.DelayAction(arg_5_0, arg_5_1, arg_5_2)
	if not ove_0_17 then
		function ove_0_17()
			for iter_6_0, iter_6_1 in pairs(ove_0_16) do
				if iter_6_0 <= os.clock() and iter_6_0 == iter_6_0 then
					for iter_6_2 = 1, #iter_6_1 do
						if iter_6_2 == iter_6_2 then
							local slot_6_0 = iter_6_1[iter_6_2]

							if slot_6_0 and slot_6_0.func then
								slot_6_0.func(unpack(slot_6_0.args or {}))
							end
						end
					end

					ove_0_16[iter_6_0] = nil
				end
			end
		end

		cb.add(cb.tick, ove_0_17)
	end

	local slot_5_0 = os.clock() + (arg_5_1 or 0)

	if ove_0_16[slot_5_0] then
		ove_0_16[slot_5_0][#ove_0_16[slot_5_0] + 1] = {
			func = arg_5_0,
			args = arg_5_2
		}
	else
		ove_0_16[slot_5_0] = {
			{
				func = arg_5_0,
				args = arg_5_2
			}
		}
	end
end

function ove_0_12.isPosOnScreen(arg_7_0)
	local slot_7_0 = graphics.world_to_screen(arg_7_0)

	if slot_7_0.x < 0 or slot_7_0.x > graphics.width or slot_7_0.y < 0 or slot_7_0.y > graphics.height then
		return false
	end

	return true
end

local ove_0_18

function ove_0_12.SetInterval()
	if not ove_0_18 then
		function ove_0_18(arg_9_0, arg_9_1, arg_9_2, arg_9_3, arg_9_4)
			if arg_9_0(unpack(arg_9_4 or {})) ~= false and (not arg_9_3 or arg_9_3 > 1) then
				ove_0_12.DelayAction(ove_0_18, arg_9_2 - (os.clock() - arg_9_1 - arg_9_2), {
					arg_9_0,
					arg_9_1 + arg_9_2,
					arg_9_2,
					arg_9_3 and arg_9_3 - 1,
					arg_9_4
				})
			end
		end
	end

	ove_0_12.DelayAction(ove_0_18, timeout, {
		userFunction,
		os.clock(),
		timeout or 0,
		count,
		params
	})
end

function ove_0_12.print(arg_10_0, arg_10_1)
	local slot_10_0 = arg_10_1 or 42

	console.set_color(slot_10_0)
	print(arg_10_0)
	console.set_color(15)
end

function ove_0_12.GetPercentHealth(arg_11_0)
	local slot_11_0 = arg_11_0 or player

	return slot_11_0.health / slot_11_0.maxHealth * 100
end

function ove_0_12.hasRune(arg_12_0)
	for iter_12_0 = 0, player.rune.size - 1 do
		if player.rune[iter_12_0].name:lower() == arg_12_0 then
			return true
		end
	end

	return false
end

local ove_0_19 = {
	timers = {
		recall = 8,
		recallimproved = 7,
		SuperRecall = 4,
		odinrecall = 4.5,
		odinrecallimproved = 4
	}
}

function ove_0_12.RecallValid(arg_13_0)
	if ove_0_19.timers[arg_13_0] then
		return true
	end

	return false
end

function ove_0_12.GetLevel(arg_14_0)
	local slot_14_0 = arg_14_0 or player

	return math.min(slot_14_0.levelRef, 18)
end

function ove_0_12.GetPercentMana(arg_15_0)
	local slot_15_0 = arg_15_0 or player

	return slot_15_0.mana / slot_15_0.maxMana * 100
end

function ove_0_12.GetPercentPar(arg_16_0)
	local slot_16_0 = arg_16_0 or player

	return slot_16_0.par / slot_16_0.maxPar * 100
end

function ove_0_12.CheckBuff2(arg_17_0, arg_17_1)
	if arg_17_0 and arg_17_0.buff and arg_17_0.buff[arg_17_1:lower()] then
		return true
	end

	return false
end

function ove_0_12.StartTime(arg_18_0, arg_18_1)
	if arg_18_0 and arg_18_0.buff then
		local slot_18_0 = arg_18_0.buff[arg_18_1:lower()]

		if slot_18_0 and slot_18_0.startTime then
			return slot_18_0.startTime
		end
	end

	return 0
end

function ove_0_12.CheckBuffEnd(arg_19_0, arg_19_1)
	if arg_19_0 and arg_19_0.buff then
		local slot_19_0 = arg_19_0.buff[arg_19_1:lower()]

		if slot_19_0 and slot_19_0.endTime then
			return slot_19_0.endTime
		end
	end

	return 0
end

function ove_0_12.EndTime(arg_20_0, arg_20_1)
	if arg_20_0 and arg_20_0.buff then
		local slot_20_0 = arg_20_0.buff[arg_20_1:lower()]

		if slot_20_0 and slot_20_0.endTime then
			return slot_20_0.endTime
		end
	end

	return 0
end

function ove_0_12.CheckBuff(arg_21_0, arg_21_1)
	if arg_21_0 and arg_21_0.buff and arg_21_0.buff[arg_21_1:lower()] then
		return true
	end

	return false
end

function ove_0_12.CountBuff(arg_22_0, arg_22_1)
	if arg_22_0 then
		local slot_22_0 = arg_22_0.buff[arg_22_1:lower()]

		if slot_22_0 then
			if slot_22_0.stacks > 0 then
				return slot_22_0.stacks
			end

			if slot_22_0.stacks > 0 then
				return slot_22_0.stacks
			end
		end
	end

	return 0
end

function ove_0_12.CountBuff2(arg_23_0, arg_23_1)
	if arg_23_0 and arg_23_0.buff then
		local slot_23_0 = arg_23_0.buff[arg_23_1:lower()]

		if slot_23_0 then
			if slot_23_0.stacks > 0 then
				return slot_23_0.stacks
			end

			if slot_23_0.stacks > 0 then
				return slot_23_0.stacks
			end
		end
	end

	return 0
end

function ove_0_12.CheckBuffType(arg_24_0, arg_24_1)
	if arg_24_0 and arg_24_0.buff and arg_24_0.buff[arg_24_1] and arg_24_0.buff[arg_24_1].startTime and game.time - arg_24_0.buff[arg_24_1].startTime > 0.1 and arg_24_0.buff[arg_24_1].name ~= "nautilusanchordragglobalroot" then
		return true
	end

	return false
end

function ove_0_12.CheckBuffTypeFake(arg_25_0, arg_25_1)
	if arg_25_0 and arg_25_0.buff and arg_25_0.buff[arg_25_1] and arg_25_0.buff[arg_25_1].startTime and game.time - arg_25_0.buff[arg_25_1].startTime <= 0.2 then
		return true
	end

	return false
end

local ove_0_20 = {
	100,
	105,
	110,
	115,
	120,
	130,
	140,
	150,
	165,
	180,
	200,
	225,
	255,
	290,
	330,
	380,
	440,
	510
}

function ove_0_12.GetShieldedHealth(arg_26_0, arg_26_1)
	local slot_26_0 = 0

	if arg_26_0 == "AD" then
		slot_26_0 = arg_26_1.physicalShield
	elseif arg_26_0 == "AP" then
		slot_26_0 = arg_26_1.magicalShield
	elseif arg_26_0 == "ALL" then
		slot_26_0 = arg_26_1.allShield
	end

	return arg_26_1.health + slot_26_0
end

function ove_0_12.GetTotalAD(arg_27_0)
	local slot_27_0 = arg_27_0 or player

	return (slot_27_0.baseAttackDamage + slot_27_0.flatPhysicalDamageMod) * slot_27_0.percentPhysicalDamageMod
end

function ove_0_12.GetBonusAD(arg_28_0)
	local slot_28_0 = arg_28_0 or player

	return (slot_28_0.baseAttackDamage + slot_28_0.flatPhysicalDamageMod) * slot_28_0.percentPhysicalDamageMod - slot_28_0.baseAttackDamage
end

function ove_0_12.GetTotalAP(arg_29_0)
	local slot_29_0 = arg_29_0 or player

	return slot_29_0.flatMagicDamageMod * slot_29_0.percentMagicDamageMod
end

function ove_0_12.PhysicalReduction(arg_30_0, arg_30_1)
	source = source or player

	if arg_30_0.armor == 0 then
		return 1
	end

	local slot_30_0 = (arg_30_0.bonusArmor * source.percentBonusArmorPenetration + arg_30_0.armor - arg_30_0.bonusArmor) * source.percentArmorPenetration
	local slot_30_1 = source.type == TYPE_HERO and source.physicalLethality * (0.6 + 0.4 * source.levelRef / 18) or 0

	return slot_30_0 >= 0 and 100 / (100 + slot_30_0 - slot_30_1) or 1
end

function ove_0_12.MagicReduction(arg_31_0, arg_31_1)
	local slot_31_0 = arg_31_1 or player
	local slot_31_1 = arg_31_0.spellBlock * slot_31_0.percentMagicPenetration - slot_31_0.flatMagicPenetration

	return slot_31_1 >= 0 and 100 / (100 + slot_31_1) or 2 - 100 / (100 - slot_31_1)
end

function ove_0_12.DamageReduction(arg_32_0, arg_32_1, arg_32_2)
	local slot_32_0

	slot_32_0 = arg_32_2 or player

	local slot_32_1 = 1

	if arg_32_0 == "AD" then
		-- block empty
	end

	if arg_32_0 == "AP" then
		-- block empty
	end

	return slot_32_1
end

function ove_0_12.CalculateAADamage(arg_33_0, arg_33_1)
	local slot_33_0 = arg_33_1 or player

	if arg_33_0 and slot_33_0.baseAttackDamage and (arg_33_0.type == TYPE_MINION or arg_33_0.type == TYPE_HERO) then
		return ove_0_12.GetTotalAD(slot_33_0) * ove_0_12.PhysicalReduction(arg_33_0, slot_33_0)
	end

	return 0
end

local ove_0_21 = math.floor

function ove_0_12.CalculateAADamage(arg_34_0, arg_34_1, arg_34_2)
	local slot_34_0 = arg_34_1 or player
	local slot_34_1 = 0

	if arg_34_2 then
		slot_34_1 = arg_34_0.health * 0.02
	end

	if arg_34_0 and slot_34_0.baseAttackDamage and (arg_34_0.type == TYPE_MINION or arg_34_0.type == TYPE_HERO) then
		return ove_0_21(ove_0_12.GetTotalAD(slot_34_0) * ove_0_12.PhysicalReduction(arg_34_0, slot_34_0) + ove_0_12.CalculatePhysicalDamage(arg_34_0, slot_34_1, player))
	end

	return 0
end

function ove_0_12.CalculatePhysicalDamage(arg_35_0, arg_35_1, arg_35_2)
	local slot_35_0 = arg_35_2 or player

	if arg_35_0 then
		return arg_35_1 * ove_0_12.PhysicalReduction(arg_35_0, slot_35_0) * ove_0_12.DamageReduction("AD", arg_35_0, slot_35_0)
	end

	return 0
end

function ove_0_12.CalculateMagicDamage(arg_36_0, arg_36_1, arg_36_2)
	local slot_36_0 = arg_36_2 or player

	if arg_36_0 then
		return arg_36_1 * ove_0_12.MagicReduction(arg_36_0, slot_36_0) * ove_0_12.DamageReduction("AP", arg_36_0, slot_36_0)
	end

	return 0
end

function ove_0_12.GetAARange(arg_37_0)
	return player.attackRange + (arg_37_0 and arg_37_0.boundingRadius or 0)
end

function ove_0_12.GetPredictedPos(arg_38_0, arg_38_1)
	if not ove_0_12.IsValidTarget(arg_38_0) or not arg_38_0.path or not arg_38_1 or not arg_38_0.moveSpeed then
		return arg_38_0
	end

	local slot_38_0 = ove_0_10.core.lerp(arg_38_0.path, network.latency + arg_38_1, arg_38_0.moveSpeed)

	return vec3(slot_38_0.x, player.y, slot_38_0.y)
end

function ove_0_12.GetIgniteDamage(arg_39_0)
	local slot_39_0 = 55 + 25 * player.levelRef

	if arg_39_0 then
		slot_39_0 = slot_39_0 - (ove_0_12.GetShieldedHealth("AD", arg_39_0) - arg_39_0.health)
	end

	return slot_39_0
end

ove_0_12.enum = {}
ove_0_12.enum.slots = {
	w = 1,
	q = 0,
	e = 2,
	r = 3
}
ove_0_12.enum.buff_types = {
	Knockback = 30,
	Flee = 28,
	Counter = 26,
	Poison = 23,
	Heal = 13,
	Currency = 20,
	PhysicalImmunity = 16,
	Aura = 1,
	AttackSpeedSlow = 18,
	Internal = 0,
	Knockup = 29,
	Suppression = 24,
	Grounded = 32,
	Drowsy = 33,
	Asleep = 34,
	Silence = 7,
	Damage = 12,
	Shred = 27,
	Invisibility = 6,
	SpellImmunity = 15,
	Charm = 22,
	Fear = 21,
	Polymorph = 9,
	SpellShield = 4,
	Haste = 14,
	Snare = 11,
	Blind = 25,
	NearSight = 19,
	Slow = 10,
	Taunt = 8,
	Stun = 5,
	Invulnerability = 17,
	CombatDehancer = 3,
	Disarm = 31,
	CombatEnchancer = 2
}

local ove_0_22 = {
	nil,
	nil,
	nil,
	nil,
	true,
	nil,
	nil,
	true,
	nil,
	nil,
	true,
	nil,
	nil,
	nil,
	nil,
	nil,
	nil,
	true,
	nil,
	nil,
	true,
	true,
	nil,
	true,
	nil,
	nil,
	nil,
	true,
	true,
	true
}

function ove_0_12.SionCheck(arg_40_0)
	if arg_40_0.type ~= TYPE_HERO then
		return true
	else
		return not arg_40_0.buff.sionpassivezombie
	end
end

function ove_0_12.IsInvulnerable(arg_41_0)
	if arg_41_0.type ~= TYPE_HERO then
		return false
	elseif arg_41_0.buff and (arg_41_0.buff.sionpassivezombie or arg_41_0.buff.chronoshift or arg_41_0.buff.kindredrnodeathbuff or arg_41_0.buff.undyingrage) or arg_41_0.buff.kayler or arg_41_0.buff.pantheone then
		return true
	else
		return false
	end
end

function ove_0_12.IsValidTarget(arg_42_0)
	return arg_42_0 and not arg_42_0.isDead and arg_42_0.isVisible and arg_42_0.isTargetable and arg_42_0.path and not ove_0_12.CheckBuffType(arg_42_0, 17) and ove_0_12.SionCheck(arg_42_0) and ove_0_12.IsInvulnerable(arg_42_0) == false
end

function ove_0_12.IsValidTargetExtra(arg_43_0, arg_43_1)
	return arg_43_0 and not arg_43_0.isDead and (arg_43_0.isVisible or game.time - arg_43_1 < 1.5) and arg_43_0.isTargetable and not ove_0_12.CheckBuffType(arg_43_0, 17) and ove_0_12.SionCheck(arg_43_0) and ove_0_12.IsInvulnerable(arg_43_0) == false
end

function ove_0_12.IsValidTarget2(arg_44_0)
	return arg_44_0 and not arg_44_0.isDead and arg_44_0.isVisible and arg_44_0.isTargetable and not ove_0_12.CheckBuffType(arg_44_0, 17) and ove_0_12.SionCheck(arg_44_0)
end

ove_0_12.units = {}
ove_0_12.units.minions, ove_0_12.units.minionCount = {}, 0
ove_0_12.units.enemyMinions, ove_0_12.units.enemyMinionCount = {}, 0
ove_0_12.units.allyMinions, ove_0_12.units.allyMinionCount = {}, 0
ove_0_12.units.jungleMinions, ove_0_12.units.jungleMinionCount = {}, 0
ove_0_12.units.enemies, ove_0_12.units.allies = {}, {}

function ove_0_12.can_target_minion(arg_45_0)
	return arg_45_0 and not arg_45_0.isDead and arg_45_0.path and arg_45_0.team ~= TEAM_ALLY and arg_45_0.health and arg_45_0.maxHealth > 3 and arg_45_0.isVisible and arg_45_0.isTargetable and not string.lower(arg_45_0.name):find("trap")
end

function ove_0_12.can_target_minion_ally(arg_46_0)
	return arg_46_0 and not arg_46_0.isDead and arg_46_0.team == TEAM_ALLY and arg_46_0.health and arg_46_0.maxHealth > 3 and arg_46_0.isVisible and arg_46_0.isTargetable and not string.lower(arg_46_0.name):find("trap")
end

local ove_0_23 = {
	PlantMasterMinion = true,
	PlantHealth = true,
	CampRespawn = true,
	PlantVision = true,
	PlantSatchel = true
}

local function ove_0_24(arg_47_0)
	return arg_47_0 and arg_47_0.type == TYPE_MINION and not arg_47_0.isDead and arg_47_0.health > 0 and arg_47_0.maxHealth > 100 and arg_47_0.maxHealth < 20000 and not arg_47_0.name:find("Ward") and not ove_0_23[arg_47_0.name]
end

local function ove_0_25(arg_48_0)
	return arg_48_0 and arg_48_0.type == TYPE_HERO
end

local function ove_0_26(arg_49_0, arg_49_1, arg_49_2, arg_49_3)
	local slot_49_0

	for iter_49_0 = 1, arg_49_1 do
		local slot_49_1 = arg_49_0[iter_49_0]

		if not arg_49_3(slot_49_1) then
			slot_49_0 = iter_49_0

			break
		end
	end

	if slot_49_0 then
		arg_49_0[slot_49_0] = arg_49_2
	else
		arg_49_1 = arg_49_1 + 1
		arg_49_0[arg_49_1] = arg_49_2
	end

	return arg_49_1
end

local function ove_0_27(arg_50_0)
	if ove_0_24(arg_50_0) then
		if arg_50_0.team == TEAM_ALLY then
			ove_0_12.units.allyMinionCount = ove_0_26(ove_0_12.units.allyMinions, ove_0_12.units.allyMinionCount, arg_50_0, ove_0_24)
		elseif arg_50_0.team == TEAM_ENEMY then
			ove_0_12.units.enemyMinionCount = ove_0_26(ove_0_12.units.enemyMinions, ove_0_12.units.enemyMinionCount, arg_50_0, ove_0_24)
		else
			ove_0_12.units.jungleMinionCount = ove_0_26(ove_0_12.units.jungleMinions, ove_0_12.units.jungleMinionCount, arg_50_0, ove_0_24)
		end

		ove_0_12.units.minionCount = ove_0_26(ove_0_12.units.minions, ove_0_12.units.minionCount, arg_50_0, ove_0_24)
	end
end

local function ove_0_28(arg_51_0)
	if ove_0_25(arg_51_0) then
		if arg_51_0.team == TEAM_ALLY then
			ove_0_26(ove_0_12.units.allies, #ove_0_12.units.allies, arg_51_0, ove_0_25)
		else
			ove_0_26(ove_0_12.units.enemies, #ove_0_12.units.enemies, arg_51_0, ove_0_25)
		end
	end
end

objManager.loop(function(arg_52_0)
	ove_0_28(arg_52_0)
end)

function ove_0_12.GetEvadeInfo(arg_53_0, arg_53_1, arg_53_2, arg_53_3, arg_53_4)
	for iter_53_0 = 1, #ove_0_15.core.active_spells do
		local slot_53_0 = ove_0_15.core.active_spells[iter_53_0]

		if arg_53_4 and slot_53_0.data.spell_type == "Target" and slot_53_0.target == arg_53_0 and slot_53_0.owner.type == TYPE_HERO then
			local slot_53_1 = string.lower(slot_53_0.owner.charName)

			if ove_0_12.TargetedSpells()[slot_53_1] then
				return slot_53_0.owner, slot_53_0.data
			end
		end

		if slot_53_0.polygon and slot_53_0.polygon:Contains(arg_53_0.path.serverPos) ~= 0 and (not slot_53_0.data.collision or #slot_53_0.data.collision == 0) then
			if slot_53_0.data and slot_53_0.data.owner_is_missile and slot_53_0.data.speed and arg_53_3 > arg_53_0.pos:dist(slot_53_0.owner.pos) / slot_53_0.data.speed then
				return slot_53_0.owner, slot_53_0.data
			end

			if arg_53_0.pos:dist(slot_53_0.owner.pos) <= 250 and (slot_53_0.data.speed ~= math.huge and slot_53_0.data.spell_type ~= "Circular" or arg_53_2 > slot_53_0.end_time - slot_53_0.start_time) then
				return slot_53_0.owner, slot_53_0.data
			end

			if slot_53_0.special_object and slot_53_0.special_object.pos and arg_53_1 > arg_53_0.pos:dist(slot_53_0.special_object.pos) / slot_53_0.data.speed then
				return slot_53_0.owner, slot_53_0.data
			end

			if slot_53_0.missile and arg_53_1 > arg_53_0.pos:dist(slot_53_0.missile.pos) / slot_53_0.data.speed then
				return slot_53_0.owner, slot_53_0.data
			end

			if (slot_53_0.data.speed == math.huge or slot_53_0.data.spell_type == "Circular") and slot_53_0.owner and slot_53_0.end_time - os.clock() < arg_53_1 + 0.1 then
				return slot_53_0.owner, slot_53_0.data
			end
		end
	end
end

function ove_0_12.GetAllyHeroesInRange(arg_54_0, arg_54_1)
	local slot_54_0 = arg_54_1 or player
	local slot_54_1 = {}
	local slot_54_2 = ove_0_12.GetAllyHeroes()

	for iter_54_0 = 1, #slot_54_2 do
		local slot_54_3 = slot_54_2[iter_54_0]

		if ove_0_12.IsValidTarget(slot_54_3) and slot_54_3.pos:distSqr(slot_54_0) < arg_54_0 * arg_54_0 then
			slot_54_1[#slot_54_1 + 1] = slot_54_3
		end
	end

	return slot_54_1
end

function ove_0_12.GetEnemyHeroesInRange(arg_55_0, arg_55_1)
	local slot_55_0 = arg_55_1 or player
	local slot_55_1 = {}
	local slot_55_2 = ove_0_12.GetEnemyHeroes()

	for iter_55_0 = 1, #slot_55_2 do
		local slot_55_3 = slot_55_2[iter_55_0]

		if ove_0_12.IsValidTarget(slot_55_3) and slot_55_3.pos:distSqr(slot_55_0) < arg_55_0 * arg_55_0 then
			slot_55_1[#slot_55_1 + 1] = slot_55_3
		end
	end

	return slot_55_1
end

function ove_0_12.CountObjectsNearPos(arg_56_0, arg_56_1, arg_56_2, arg_56_3)
	local slot_56_0 = 0
	local slot_56_1 = {}

	for iter_56_0, iter_56_1 in pairs(arg_56_2) do
		if arg_56_3(iter_56_1) and arg_56_1 >= arg_56_0:dist(iter_56_1.pos) then
			slot_56_0 = slot_56_0 + 1
			slot_56_1[slot_56_0] = iter_56_1
		end
	end

	return slot_56_0, slot_56_1
end

function ove_0_12.GetMinionsInRange(arg_57_0, arg_57_1, arg_57_2)
	arg_57_2 = arg_57_2 or player.pos
	arg_57_0 = arg_57_0 or math.huge
	arg_57_1 = arg_57_1 or TEAM_ENEMY

	local function slot_57_0(arg_58_0)
		return arg_58_0 and arg_58_0.type == TYPE_MINION and arg_58_0.team == arg_57_1 and not arg_58_0.isDead and arg_58_0.health and arg_58_0.health > 0 and arg_58_0.isVisible
	end

	local slot_57_1, slot_57_2 = ove_0_12.CountObjectsNearPos(arg_57_2, arg_57_0, ove_0_12.units.minions, slot_57_0)

	return slot_57_2
end

function ove_0_12.GetEnemyHeroes()
	return ove_0_12.units.enemies
end

function ove_0_12.TargetedSpells()
	return {
		alistar = {
			{
				slot = 1,
				menuslot = "W"
			}
		},
		annie = {
			{
				slot = 0,
				menuslot = "Q"
			}
		},
		anivia = {
			{
				slot = 2,
				menuslot = "E"
			}
		},
		sett = {
			{
				slot = 3,
				menuslot = "R"
			}
		},
		blitzcrank = {
			{
				slot = 2,
				menuslot = "E"
			}
		},
		brand = {
			{
				slot = 2,
				menuslot = "E"
			},
			{
				slot = 3,
				menuslot = "R"
			}
		},
		caitlyn = {
			{
				slot = 3,
				menuslot = "R"
			}
		},
		camille = {
			{
				slot = 3,
				menuslot = "R"
			}
		},
		cassiopeia = {
			{
				slot = 2,
				menuslot = "E"
			}
		},
		chogath = {
			{
				slot = 3,
				menuslot = "R"
			}
		},
		darius = {
			{
				slot = 1,
				menuslot = "W"
			},
			{
				slot = 3,
				menuslot = "R"
			}
		},
		diana = {
			{
				slot = 3,
				menuslot = "R"
			}
		},
		elise = {
			{
				slot = 0,
				menuslot = "Q"
			}
		},
		evelynn = {
			{
				slot = 2,
				menuslot = "E"
			}
		},
		fiddlesticks = {
			{
				slot = 0,
				menuslot = "Q"
			}
		},
		fizz = {
			{
				slot = 0,
				menuslot = "Q"
			}
		},
		gangplank = {
			{
				slot = 0,
				menuslot = "Q"
			}
		},
		garen = {
			{
				slot = 0,
				menuslot = "Q"
			},
			{
				slot = 3,
				menuslot = "R"
			}
		},
		hecarim = {
			{
				slot = 2,
				menuslot = "E"
			}
		},
		irelia = {
			{
				slot = 0,
				menuslot = "Q"
			}
		},
		janna = {
			{
				slot = 1,
				menuslot = "W"
			}
		},
		jarvaniv = {
			{
				slot = 3,
				menuslot = "R"
			}
		},
		jax = {
			{
				slot = 0,
				menuslot = "Q"
			}
		},
		jayce = {
			{
				slot = 0,
				menuslot = "Q"
			}
		},
		jhin = {
			{
				slot = 0,
				menuslot = "Q"
			}
		},
		kalista = {
			{
				slot = 2,
				menuslot = "E"
			}
		},
		karma = {
			{
				slot = 1,
				menuslot = "W"
			}
		},
		katarina = {
			{
				slot = 0,
				menuslot = "Q"
			}
		},
		kennen = {
			{
				slot = 2,
				menuslot = "E"
			}
		},
		khazix = {
			{
				slot = 0,
				menuslot = "Q"
			}
		},
		leblanc = {
			{
				slot = 0,
				menuslot = "Q"
			}
		},
		leesin = {
			{
				slot = 3,
				menuslot = "R"
			}
		},
		leona = {
			{
				slot = 0,
				menuslot = "Q"
			}
		},
		lissandra = {
			{
				slot = 3,
				menuslot = "R"
			}
		},
		nautilus = {
			{
				slot = 3,
				menuslot = "R"
			}
		},
		lulu = {
			{
				slot = 1,
				menuslot = "W"
			},
			{
				slot = 2,
				menuslot = "E"
			}
		},
		malphite = {
			{
				slot = 0,
				menuslot = "Q"
			}
		},
		malzahar = {
			{
				slot = 2,
				menuslot = "E"
			},
			{
				slot = 3,
				menuslot = "R"
			}
		},
		maokai = {
			{
				slot = 1,
				menuslot = "W"
			}
		},
		missfortune = {
			{
				slot = 0,
				menuslot = "Q"
			}
		},
		morgana = {
			{
				slot = 3,
				menuslot = "R"
			}
		},
		nami = {
			{
				slot = 1,
				menuslot = "W"
			}
		},
		nasus = {
			{
				slot = 0,
				menuslot = "Q"
			},
			{
				slot = 1,
				menuslot = "W"
			}
		},
		nocturne = {
			{
				slot = 2,
				menuslot = "E"
			}
		},
		olaf = {
			{
				slot = 2,
				menuslot = "E"
			}
		},
		pantheon = {
			{
				slot = 1,
				menuslot = "W"
			}
		},
		poppy = {
			{
				slot = 2,
				menuslot = "E"
			}
		},
		quinn = {
			{
				slot = 2,
				menuslot = "E"
			}
		},
		rammus = {
			{
				slot = 2,
				menuslot = "E"
			}
		},
		renekton = {
			{
				slot = 1,
				menuslot = "W"
			}
		},
		drmundo = {
			{
				slot = 2,
				menuslot = "E"
			}
		},
		monkeyking = {
			{
				slot = 0,
				menuslot = "Q"
			}
		},
		mordekaiser = {
			{
				slot = 3,
				menuslot = "R"
			}
		},
		yorick = {
			{
				slot = 0,
				menuslot = "Q"
			}
		},
		rengar = {
			{
				slot = 0,
				menuslot = "Q"
			}
		},
		ryze = {
			{
				slot = 1,
				menuslot = "W"
			},
			{
				slot = 2,
				menuslot = "E"
			}
		},
		shaco = {
			{
				slot = 2,
				menuslot = "E"
			}
		},
		singed = {
			{
				slot = 2,
				menuslot = "E"
			}
		},
		skarner = {
			{
				slot = 3,
				menuslot = "R"
			}
		},
		sylas = {
			{
				slot = 3,
				menuslot = "R"
			}
		},
		syndra = {
			{
				slot = 3,
				menuslot = "R"
			}
		},
		tahmkench = {
			{
				slot = 1,
				menuslot = "W"
			}
		},
		talon = {
			{
				slot = 0,
				menuslot = "Q"
			}
		},
		teemo = {
			{
				slot = 0,
				menuslot = "Q"
			}
		},
		tristana = {
			{
				slot = 3,
				menuslot = "R"
			}
		},
		trundle = {
			{
				slot = 0,
				menuslot = "Q"
			}
		},
		twistedfate = {
			{
				slot = 1,
				menuslot = "W"
			}
		},
		twitch = {
			{
				slot = 2,
				menuslot = "E"
			}
		},
		udyr = {
			{
				slot = 2,
				menuslot = "E"
			}
		},
		vayne = {
			{
				slot = 2,
				menuslot = "E"
			}
		},
		veigar = {
			{
				slot = 3,
				menuslot = "R"
			}
		},
		vi = {
			{
				slot = 3,
				menuslot = "R"
			}
		},
		viktor = {
			{
				slot = 0,
				menuslot = "Q"
			}
		},
		vladimir = {
			{
				slot = 0,
				menuslot = "Q"
			}
		},
		volibear = {
			{
				slot = 0,
				menuslot = "Q"
			},
			{
				slot = 1,
				menuslot = "W"
			}
		},
		warwick = {
			{
				slot = 0,
				menuslot = "Q"
			}
		},
		monkeyking = {
			{
				slot = 2,
				menuslot = "E"
			}
		},
		xinzhao = {
			{
				slot = 0,
				menuslot = "Q"
			}
		},
		yasuo = {
			{
				slot = 2,
				menuslot = "E"
			}
		},
		zed = {
			{
				slot = 3,
				menuslot = "R"
			}
		}
	}
end

function ove_0_12.GetInterruptableSpells()
	return {
		caitlyn = {
			{
				spellname = "caitlynaceinthehole",
				menuslot = "R",
				slot = 3,
				channelduration = 1
			}
		},
		fiddlesticks = {
			{
				spellname = "drainchannel",
				menuslot = "W",
				slot = 1,
				channelduration = 2
			},
			{
				spellname = "crowstorm",
				menuslot = "R",
				slot = 3,
				channelduration = 1.5
			}
		},
		janna = {
			{
				spellname = "reapthewhirlwind",
				menuslot = "R",
				slot = 3,
				channelduration = 3
			}
		},
		karthus = {
			{
				spellname = "karthusfallenone",
				menuslot = "R",
				slot = 3,
				channelduration = 3
			}
		},
		katarina = {
			{
				spellname = "katarinar",
				menuslot = "R",
				slot = 3,
				channelduration = 2.5
			}
		},
		lucian = {
			{
				spellname = "lucianr",
				menuslot = "R",
				slot = 3,
				channelduration = 3
			}
		},
		malzahar = {
			{
				spellname = "malzaharr",
				menuslot = "R",
				slot = 3,
				channelduration = 2.5
			}
		},
		masteryi = {
			{
				spellname = "meditate",
				menuslot = "W",
				slot = 1,
				channelduration = 4
			}
		},
		missfortune = {
			{
				spellname = "missfortunebullettime",
				menuslot = "R",
				slot = 3,
				channelduration = 3
			}
		},
		nunu = {
			{
				spellname = "nunur",
				menuslot = "R",
				slot = 3,
				channelduration = 3
			}
		},
		pantheon = {
			{
				spellname = "pantheonrjump",
				menuslot = "R",
				slot = 3,
				channelduration = 2
			},
			{
				spellname = "pantheonq",
				menuslot = "Q",
				slot = 0,
				channelduration = 4
			}
		},
		poppy = {
			{
				spellname = "poppyr",
				menuslot = "R",
				slot = 3,
				channelduration = 4
			}
		},
		quinn = {
			{
				spellname = "quinr",
				menuslot = "R",
				slot = 3,
				channelduration = 2
			}
		},
		shen = {
			{
				spellname = "shenr",
				menuslot = "R",
				slot = 3,
				channelduration = 3
			}
		},
		sion = {
			{
				spellname = "sionq",
				menuslot = "Q",
				slot = 0,
				channelduration = 2
			}
		},
		tahmkench = {
			{
				spellname = "tahmkenchnewr",
				menuslot = "R",
				slot = 3,
				channelduration = 3
			}
		},
		twistedfate = {
			{
				spellname = "gate",
				menuslot = "R",
				slot = 3,
				channelduration = 1.5
			}
		},
		varus = {
			{
				spellname = "varusq",
				menuslot = "Q",
				slot = 0,
				channelduration = 4
			}
		},
		velkoz = {
			{
				spellname = "velkozr",
				menuslot = "R",
				slot = 3,
				channelduration = 2.5
			}
		},
		warwick = {
			{
				spellname = "warwickrchannel",
				menuslot = "R",
				slot = 3,
				channelduration = 1.5
			}
		},
		xerath = {
			{
				spellname = "xeratharcanopulsechargeup",
				menuslot = "Q",
				slot = 0,
				channelduration = 3
			},
			{
				spellname = "xerathlocusofpower2",
				menuslot = "R",
				slot = 3,
				channelduration = 10
			}
		},
		zac = {
			{
				spellname = "zace",
				menuslot = "E",
				slot = 2,
				channelduration = 4
			}
		},
		jhin = {
			{
				spellname = "jhinr",
				menuslot = "R",
				slot = 3,
				channelduration = 10
			}
		},
		pyke = {
			{
				spellname = "pykeq",
				menuslot = "Q",
				slot = 0,
				channelduration = 3
			}
		},
		vi = {
			{
				spellname = "viq",
				menuslot = "Q",
				slot = 0,
				channelduration = 4
			}
		},
		samira = {
			{
				spellname = "samirar",
				menuslot = "R",
				slot = 3,
				channelduration = 2
			}
		}
	}
end

function ove_0_12.GetAllyHeroes()
	return ove_0_12.units.allies
end

function ove_0_12.GetJungleMinions()
	local slot_63_0 = {}

	for iter_63_0 = 0, objManager.minions.size[TEAM_NEUTRAL] - 1 do
		local slot_63_1 = objManager.minions[TEAM_NEUTRAL][iter_63_0]

		if slot_63_1 and not slot_63_1.isDead and slot_63_1.isTargetable and slot_63_1.isVisible and slot_63_1.type == TYPE_MINION then
			table.insert(slot_63_0, slot_63_1)
		end
	end

	return slot_63_0
end

ove_0_12._fountain = nil
ove_0_12._fountainRadius = 750

function ove_0_12.GetFountain()
	if ove_0_12._fountain then
		return ove_0_12._fountain
	end

	local slot_64_0 = ove_0_12.GetMap()

	if slot_64_0 and slot_64_0.index and slot_64_0.index == 1 then
		ove_0_12._fountainRadius = 1050
	end

	if ove_0_12.GetShop() then
		objManager.loop(function(arg_65_0)
			if arg_65_0 and arg_65_0.team == TEAM_ALLY and arg_65_0.name:lower():find("spawn") and not arg_65_0.name:lower():find("troy") and not arg_65_0.name:lower():find("barracks") then
				ove_0_12._fountain = arg_65_0

				return ove_0_12._fountain
			end
		end)
	end

	return nil
end

function ove_0_12.NearFountain(arg_66_0)
	local slot_66_0 = arg_66_0 or ove_0_12._fountainRadius or 0
	local slot_66_1 = ove_0_12.GetFountain()

	if slot_66_1 then
		return player.pos2D:distSqr(slot_66_1.pos2D) <= slot_66_0 * slot_66_0, slot_66_1.x, slot_66_1.y, slot_66_1.z, slot_66_0
	else
		return false, 0, 0, 0, 0
	end
end

ove_0_12._map = {
	index = 0,
	name = "unknown"
}

function ove_0_12.GetMap()
	if ove_0_12._map.index ~= 0 then
		return ove_0_12._map
	end

	local slot_67_0 = ove_0_12.GetShop()

	if slot_67_0 then
		if math.floor(slot_67_0.x) == 232 and math.floor(slot_67_0.y) == 163 and math.floor(slot_67_0.z) == 1277 then
			ove_0_12._map = {
				index = 1,
				name = "Summoner's Rift"
			}
		elseif math.floor(slot_67_0.x) == 1313 and math.floor(slot_67_0.y) == 123 and math.floor(slot_67_0.z) == 8005 then
			ove_0_12._map = {
				index = 4,
				name = "Twisted Treeline"
			}
		elseif math.floor(slot_67_0.x) == 497 and math.floor(slot_67_0.y) == -40 and math.floor(slot_67_0.z) == 1932 then
			ove_0_12._map = {
				index = 12,
				name = "Howling Abyss"
			}
		else
			print("Unknown Map! Shop: x:" .. tostring(math.floor(slot_67_0.x)) .. " y:" .. tostring(math.floor(slot_67_0.y)) .. " z:" .. tostring(math.floor(slot_67_0.z)))
		end
	end

	return ove_0_12._map
end

function ove_0_12.InFountain()
	return ove_0_12.NearFountain()
end

local ove_0_29
local ove_0_30
local ove_0_31 = 0
local ove_0_32 = 0

local function ove_0_33(arg_69_0)
	if arg_69_0 and arg_69_0.name == "Shadow" and arg_69_0.owner and arg_69_0.owner.team == TEAM_ENEMY then
		if ove_0_31 > game.time then
			ove_0_30 = arg_69_0
		end

		if ove_0_32 > game.time then
			ove_0_29 = arg_69_0
		end
	end
end

local function ove_0_34(arg_70_0)
	if ove_0_30 and arg_70_0 and arg_70_0.ptr == ove_0_30.ptr then
		ove_0_30 = nil
	end

	if ove_0_29 and arg_70_0 and arg_70_0.ptr == ove_0_29.ptr then
		ove_0_29 = nil
	end
end

cb.add(cb.create_minion, ove_0_33)
cb.add(cb.delete_minion, ove_0_34)

ove_0_12._shop = nil
ove_0_12._shopRadius = 1250

function ove_0_12.GetShop()
	if ove_0_12._shop then
		return ove_0_12._shop
	end

	objManager.loop(function(arg_72_0)
		if arg_72_0 and arg_72_0.team == TEAM_ALLY and arg_72_0.name:lower():find("shop") then
			ove_0_12._shop = arg_72_0

			return ove_0_12._shop
		end
	end)

	return nil
end

local ove_0_35 = {}

function ove_0_12.GetDashers()
	return ove_0_35
end

local function ove_0_36()
	local slot_74_0 = ove_0_12.GetEnemyHeroes()

	for iter_74_0, iter_74_1 in ipairs(slot_74_0) do
		if iter_74_1.charName == "Rengar" then
			return iter_74_1
		end
	end

	return nil
end

local ove_0_37

local function ove_0_38(arg_75_0)
	if arg_75_0 and arg_75_0.name:find("Lissandra") and arg_75_0.name:find("E_Missile") then
		ove_0_37 = arg_75_0
	end

	if arg_75_0.name:find("LeapSound") and ove_0_36() then
		local slot_75_0 = ove_0_12.GetDashInfo(ove_0_36().charName, ove_0_13, -1)

		if slot_75_0 then
			local slot_75_1 = {
				lastDelete = 0,
				start = game.time,
				owner = ove_0_36(),
				spellInfo = slot_75_0,
				begin = game.time
			}

			ove_0_35[ove_0_36().networkID] = slot_75_1
		end
	end
end

local function ove_0_39(arg_76_0)
	if arg_76_0 and ove_0_37 and ove_0_37.ptr == arg_76_0.ptr then
		ove_0_37 = nil
	end
end

cb.add(cb.create_particle, ove_0_38)
cb.add(cb.delete_particle, ove_0_39)

local function ove_0_40(arg_77_0, arg_77_1, arg_77_2)
	return arg_77_0 + arg_77_2 * (arg_77_1 - arg_77_0):norm()
end

function ove_0_12.GetDashInfo(arg_78_0, arg_78_1, arg_78_2)
	local slot_78_0 = string.lower(arg_78_0)

	if arg_78_1[slot_78_0] then
		for iter_78_0 = 1, #arg_78_1[slot_78_0] do
			local slot_78_1 = arg_78_1[slot_78_0][iter_78_0]

			if slot_78_1.slot == arg_78_2 then
				return slot_78_1
			end
		end
	end

	return nil
end

function ove_0_12.calculateRunes(arg_79_0)
	if not arg_79_0 then
		return 0
	end

	if player.buff["assets/perks/styles/domination/darkharvest/darkharvest.lua"] and arg_79_0.health / arg_79_0.maxHealth * 100 < 50 then
		if ove_0_12.GetBonusAD(player) < ove_0_12.GetTotalAP(player) then
			return ove_0_12.CalculateMagicDamage(arg_79_0, 17.647 + 2.353 * player.levelRef + 5 * player.buff["assets/perks/styles/domination/darkharvest/darkharvest.lua"].stacks2 + ove_0_12.GetBonusAD(player) * 0.25 + ove_0_12.GetTotalAP(player) * 0.15)
		else
			return ove_0_12.CalculatePhysicalDamage(arg_79_0, 17.647 + 2.353 * player.levelRef + 5 * player.buff["assets/perks/styles/domination/darkharvest/darkharvest.lua"].stacks2 + ove_0_12.GetBonusAD(player) * 0.25 + ove_0_12.GetBonusGetTotalAPAP(player) * 0.15)
		end
	end
end

local function ove_0_41(arg_80_0)
	if arg_80_0 and arg_80_0.owner and arg_80_0.owner.team == TEAM_ENEMY then
		local slot_80_0 = ove_0_12.GetDashInfo(arg_80_0.owner.charName, ove_0_13, arg_80_0.slot)

		if arg_80_0.name == "EkkoEAttack" then
			local slot_80_1 = ove_0_12.GetDashInfo(arg_80_0.owner.charName, ove_0_13, 2)

			if slot_80_1 then
				local slot_80_2 = {
					start = game.time,
					owner = arg_80_0.owner,
					lastDelete = game.time + 0.4,
					startPos = vec3(arg_80_0.owner.pos),
					endPos = vec3(arg_80_0.endPos),
					spellInfo = slot_80_1,
					begin = game.time
				}

				ove_0_35[arg_80_0.owner.networkID] = slot_80_2
			end
		end

		if arg_80_0.owner.charName == "Ivern" and arg_80_0.slot == 0 then
			local slot_80_3 = ove_0_12.GetDashInfo(arg_80_0.owner.charName, ove_0_13, 0)

			if slot_80_3 then
				local slot_80_4 = {
					start = game.time,
					owner = arg_80_0.owner,
					lastDelete = game.time + 3,
					spellInfo = slot_80_3,
					begin = game.time
				}

				ove_0_35[arg_80_0.owner.networkID] = slot_80_4
			end
		end

		if arg_80_0.name == "IllaoiWAttack" then
			local slot_80_5 = ove_0_12.GetDashInfo(arg_80_0.owner.charName, ove_0_13, 1)

			if slot_80_5 then
				local slot_80_6 = {
					start = game.time,
					owner = arg_80_0.owner,
					lastDelete = game.time + 0.5,
					startPos = vec3(arg_80_0.owner.pos),
					endPos = vec3(arg_80_0.endPos),
					spellInfo = slot_80_5,
					begin = game.time
				}

				ove_0_35[arg_80_0.owner.networkID] = slot_80_6
			end
		end

		if arg_80_0.owner.charName == "JarvanIV" and arg_80_0.slot == 0 then
			local slot_80_7 = ove_0_12.GetDashInfo(arg_80_0.owner.charName, ove_0_13, 0)

			if slot_80_7 then
				local slot_80_8 = {
					start = game.time,
					owner = arg_80_0.owner,
					lastDelete = game.time + 0.8,
					spellInfo = slot_80_7,
					begin = game.time
				}

				ove_0_35[arg_80_0.owner.networkID] = slot_80_8
			end
		end

		if arg_80_0.owner.charName ~= "Ezreal" and arg_80_0.owner.charName ~= "Illaoi" and arg_80_0.owner.charName ~= "Ivern" and arg_80_0.owner.charName ~= "RekSai" and (arg_80_0.owner.charName ~= "JarvanIV" or arg_80_0.slot ~= 0) and (arg_80_0.owner.charName ~= "LeBlanc" or arg_80_0.slot ~= 3) and arg_80_0.owner.charName ~= "Galio " and arg_80_0.owner.charName ~= "Kassadin" and arg_80_0.owner.charName ~= "Maokai" and arg_80_0.owner.charName ~= "MasterYi" and arg_80_0.owner.charName ~= "Ziggs" and arg_80_0.owner.charName ~= "Katarina" and arg_80_0.owner.charName ~= "Lissandra" and (arg_80_0.owner.charName ~= "Riven" or arg_80_0.slot == 2) and slot_80_0 then
			local slot_80_9 = {
				lastDelete = 0,
				start = game.time,
				owner = arg_80_0.owner,
				spellInfo = slot_80_0,
				begin = game.time
			}

			ove_0_35[arg_80_0.owner.networkID] = slot_80_9
		end

		if arg_80_0.owner.charName == "Riven" and arg_80_0.slot == 0 then
			local slot_80_10 = "Q1"

			if arg_80_0.owner.buff.riventricleave then
				if arg_80_0.owner.buff.riventricleave.stacks == 1 then
					slot_80_10 = "Q2"
				end

				if arg_80_0.owner.buff.riventricleave.stacks == 2 then
					slot_80_10 = "Q3"
				end
			end

			if slot_80_0 then
				local slot_80_11 = {
					lastDelete = 0,
					start = game.time,
					owner = arg_80_0.owner,
					spellInfo = slot_80_0,
					QType = slot_80_10,
					begin = game.time
				}

				ove_0_35[arg_80_0.owner.networkID] = slot_80_11
			end
		end

		if arg_80_0.owner.charName == "Lissandra" and ove_0_37 and slot_80_0 then
			local slot_80_12 = {
				isBlink = true,
				start = game.time,
				owner = arg_80_0.owner,
				lastDelete = game.time + 0.25,
				startPos = vec3(arg_80_0.owner.pos),
				endPos = vec3(ove_0_37.pos),
				spellInfo = slot_80_0,
				begin = game.time
			}

			ove_0_35[arg_80_0.owner.networkID] = slot_80_12
		end

		if arg_80_0.owner.charName == "LeBlanc" and arg_80_0.name == "LeblancRW" and slot_80_0 then
			local slot_80_13 = {
				start = game.time,
				owner = arg_80_0.owner,
				lastDelete = game.time,
				spellInfo = slot_80_0,
				begin = game.time
			}

			ove_0_35[arg_80_0.owner.networkID] = slot_80_13
		end

		if arg_80_0.owner.charName == "Ziggs" and arg_80_0.name == "ZiggsWToggle" and slot_80_0 then
			local slot_80_14 = {
				start = game.time,
				owner = arg_80_0.owner,
				lastDelete = game.time + 0.8,
				spellInfo = slot_80_0,
				begin = game.time
			}

			ove_0_35[arg_80_0.owner.networkID] = slot_80_14
		end

		if arg_80_0.owner.charName == "RekSai" and slot_80_0 then
			local slot_80_15 = {
				start = game.time,
				owner = arg_80_0.owner,
				lastDelete = game.time + 1.7,
				spellInfo = slot_80_0,
				begin = game.time + 0.4
			}

			ove_0_35[arg_80_0.owner.networkID] = slot_80_15
		end

		if arg_80_0.owner.charName == "Galio" and slot_80_0 then
			local slot_80_16 = {
				lastDelete = 0,
				start = game.time,
				owner = arg_80_0.owner,
				spellInfo = slot_80_0,
				begin = game.time + 0.5
			}

			ove_0_35[arg_80_0.owner.networkID] = slot_80_16
		end

		if arg_80_0.owner.charName == "Katarina" and slot_80_0 then
			local slot_80_17 = {
				isBlink = true,
				start = game.time,
				owner = arg_80_0.owner,
				lastDelete = game.time + 0.25,
				startPos = vec3(arg_80_0.owner.pos),
				endPos = vec3(arg_80_0.endPos),
				spellInfo = slot_80_0,
				begin = game.time
			}

			ove_0_35[arg_80_0.owner.networkID] = slot_80_17
		end

		if arg_80_0.owner.charName == "Kassadin" and slot_80_0 then
			local slot_80_18 = {
				isBlink = true,
				start = game.time,
				owner = arg_80_0.owner,
				lastDelete = game.time + 0.5,
				startPos = vec3(arg_80_0.owner.pos),
				endPos = arg_80_0.endPos:dist(arg_80_0.owner.pos) > 500 and vec3(ove_0_40(arg_80_0.owner.pos, arg_80_0.endPos, 500)) or vec3(arg_80_0.endPos),
				spellInfo = slot_80_0,
				begin = game.time
			}

			ove_0_35[arg_80_0.owner.networkID] = slot_80_18
		end

		if arg_80_0.owner.charName == "Maokai" and slot_80_0 then
			local slot_80_19 = {
				start = game.time,
				owner = arg_80_0.owner,
				lastDelete = game.time + 0.8,
				startPos = vec3(arg_80_0.owner.pos),
				endPos = vec3(arg_80_0.endPos),
				spellInfo = slot_80_0,
				begin = game.time + 0.25
			}

			ove_0_35[arg_80_0.owner.networkID] = slot_80_19
		end

		if arg_80_0.owner.charName == "MasterYi" and slot_80_0 then
			local slot_80_20 = {
				isBlink = true,
				start = game.time,
				owner = arg_80_0.owner,
				lastDelete = game.time + 1.5,
				startPos = vec3(arg_80_0.owner.pos),
				endPos = vec3(arg_80_0.endPos),
				spellInfo = slot_80_0,
				begin = game.time + 0.5
			}

			ove_0_35[arg_80_0.owner.networkID] = slot_80_20
		end

		if arg_80_0.owner.charName == "Ezreal" and slot_80_0 then
			local slot_80_21 = {
				isBlink = true,
				start = game.time,
				owner = arg_80_0.owner,
				lastDelete = game.time + 0.5,
				startPos = vec3(arg_80_0.owner.pos),
				endPos = arg_80_0.endPos:dist(arg_80_0.owner.pos) > 475 and vec3(ove_0_40(arg_80_0.owner.pos, arg_80_0.endPos, 475)) or vec3(arg_80_0.endPos),
				spellInfo = slot_80_0,
				begin = game.time
			}

			ove_0_35[arg_80_0.owner.networkID] = slot_80_21
		end

		if arg_80_0.owner.charName == "Zed" and slot_80_0 then
			if arg_80_0.name == "ZedW" then
				ove_0_32 = game.time + 1
			end

			if arg_80_0.name == "ZedR" then
				ove_0_31 = game.time + 0.25

				local slot_80_22 = {
					isBlink = true,
					start = game.time,
					owner = arg_80_0.owner,
					lastDelete = game.time + 1.5,
					startPos = vec3(arg_80_0.owner.pos),
					endPos = vec3(arg_80_0.endPos),
					spellInfo = slot_80_0,
					begin = game.time + 1
				}

				ove_0_35[arg_80_0.owner.networkID] = slot_80_22
			end

			if arg_80_0.name == "ZedR2" and ove_0_30 then
				local slot_80_23 = {
					isBlink = true,
					start = game.time,
					owner = arg_80_0.owner,
					lastDelete = game.time + 0.5,
					startPos = vec3(arg_80_0.owner.pos),
					endPos = vec3(ove_0_30.pos),
					spellInfo = slot_80_0,
					begin = game.time
				}

				ove_0_35[arg_80_0.owner.networkID] = slot_80_23
			end

			if arg_80_0.name == "ZedW2" and ove_0_29 then
				local slot_80_24 = {
					isBlink = true,
					start = game.time,
					owner = arg_80_0.owner,
					lastDelete = game.time + 0.5,
					startPos = vec3(arg_80_0.owner.pos),
					endPos = vec3(ove_0_29.pos),
					spellInfo = slot_80_0,
					begin = game.time
				}

				ove_0_35[arg_80_0.owner.networkID] = slot_80_24
			end
		end
	end
end

local function ove_0_42()
	for iter_81_0 = 0, objManager.enemies_n - 1 do
		local slot_81_0 = objManager.enemies[iter_81_0]

		if slot_81_0.type == TYPE_HERO and slot_81_0.team == TEAM_ENEMY and slot_81_0 and ove_0_12.IsValidTarget(slot_81_0) and ove_0_12.GetDashers()[slot_81_0.networkID] then
			if slot_81_0.path.isDashing and (not ove_0_12.GetDashers()[slot_81_0.networkID].endPos or ove_0_12.GetDashers()[slot_81_0.networkID].endPos:dist(vec3(slot_81_0.path.point[1])) > 100) then
				ove_0_12.GetDashers()[slot_81_0.networkID].startPos = vec3(slot_81_0.path.point[0])
				ove_0_12.GetDashers()[slot_81_0.networkID].endPos = vec3(slot_81_0.path.point[1])
			end

			if (not slot_81_0.path or not slot_81_0.path.isDashing) and ove_0_12.GetDashers()[slot_81_0.networkID].startPos and ove_0_12.GetDashers()[slot_81_0.networkID].lastDelete == 0 and ove_0_12.GetDashers()[slot_81_0.networkID].begin < game.time then
				ove_0_12.GetDashers()[slot_81_0.networkID].lastDelete = game.time + 0.1
			end
		end
	end

	for iter_81_1, iter_81_2 in pairs(ove_0_12.GetDashers()) do
		if iter_81_2.owner and iter_81_2.lastDelete < game.time and iter_81_2.lastDelete ~= 0 then
			ove_0_12.GetDashers()[iter_81_2.owner.networkID] = nil
		end

		if iter_81_2.owner and iter_81_2.begin + 2 <= game.time then
			ove_0_12.GetDashers()[iter_81_2.owner.networkID] = nil
		end
	end
end

cb.add(cb.spell, ove_0_41)
ove_0_14.combat.register_f_pre_tick(ove_0_42)

return ove_0_12

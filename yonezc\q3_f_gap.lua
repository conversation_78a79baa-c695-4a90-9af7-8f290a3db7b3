
local ove_0_10 = module.internal("TS")
local ove_0_11 = module.internal("orb")
local ove_0_12 = module.internal("pred")
local ove_0_13 = module.load(header.id, "yone/menu")
local ove_0_14 = player:spellSlot(0)
local ove_0_15 = player:spellSlot(2)
local ove_0_16 = 300
local ove_0_17 = 902500
local ove_0_18 = 450 + ove_0_16
local ove_0_19 = ove_0_18 * ove_0_18
local ove_0_20
local ove_0_21 = {
	speed = 1500,
	range = 950,
	delay = 0.35,
	boundingRadiusMod = 1,
	width = 80,
	collision = {
		wall = true
	},
	damage = function(arg_5_0)
		-- print 5
		return (25 * slot.level - 5 + player.totalAd) * 1.8
	end
}

local function ove_0_22()
	-- print 6
	for iter_6_0 = 4, 5 do
		local slot_6_0 = player:spellSlot(iter_6_0)

		if slot_6_0.isNotEmpty and slot_6_0.name:lower():find("flash") then
			return slot_6_0, iter_6_0
		end
	end
end

local function ove_0_23(arg_7_0, arg_7_1, arg_7_2)
	-- print 7
	if arg_7_2 > 2000 then
		return
	end

	local slot_7_0 = ove_0_12.present.get_source_pos(arg_7_1)
	local slot_7_1 = ove_0_20:lerp(slot_7_0, 300 / ove_0_20:dist(slot_7_0))
	local slot_7_2 = ove_0_12.linear.get_prediction(ove_0_21, arg_7_1, slot_7_1)

	if slot_7_2 and slot_7_2.startPos:distSqr(slot_7_2.endPos) < ove_0_17 and not ove_0_12.collision.get_prediction(ove_0_21, slot_7_2) then
		arg_7_0.obj = arg_7_1
		arg_7_0.pos = slot_7_2.endPos

		return true
	end
end

local ove_0_24 = {}
local ove_0_25 = 0
local ove_0_26
local ove_0_27

local function ove_0_28()
	-- print 8
	if ove_0_14.state == 0 and os.clock() > ove_0_25 and ove_0_14.name == "YoneQ3" then
		ove_0_26, ove_0_27 = ove_0_22()

		if ove_0_26 and ove_0_27 then
			ove_0_20 = player.path.serverPos2D
			ove_0_21.delay = math.max(0.4958 - 0.1458 * player.attackSpeedMod, 0.175)
			ove_0_24 = ove_0_10.get_result(ove_0_23)

			if ove_0_24.pos then
				return ove_0_24
			end
		end
	end
end

local function ove_0_29()
	-- print 9
	local slot_9_0 = vec3(ove_0_24.pos.x, ove_0_24.obj.y, ove_0_24.pos.y)

	player:castSpell("pos", ove_0_27, slot_9_0)
	player:castSpell("pos", 0, slot_9_0)

	ove_0_25 = os.clock() + network.latency + 1

	ove_0_11.core.set_server_pause()
end

return {
	get_action_state = ove_0_28,
	invoke_action = ove_0_29
}

local evadeSpellData = {}

-- championName
-- target [spellName(will be nil), spellSlot, itemName, menuName]
-- targetA [spellName(will be nil), spellSlot, itemName, menuName]
-- missile [missileName, spellSlot, itemName, menuName]
-- pos [radius, type[0 = player.pos pos, 1 = endPos, 2 = startPos + endPos], spellSlot, itemName, menuName]
-- buff [buffName, fromType[0 = player, 1 = target], radius(will be nil), time, spellSlot, itenName, menuName]

evadeSpellData.DataBase = {
    ["Alistar"] = {
        championName = "Alistar",
        target = { spellSlot = 1, itemName = "AlistarW", menuName = "Headbutt [W]" }
    },

    ["Anivia"] = {
        championName = "Anivia",
        missile = { missileName = {"frostbite"}, spellSlot = 2, itemName = "AniviaE", menuName = "Frostbite [E]" }
    },

    ["Amumu"] = {
        championName = "Amumu",
        pos = {radius = 550, type = 0, spellSlot = 3, itemName = "AmumuR", menuName = "Curse of the Sad Mummy [R]"}
    },

    ["Annie"] = {
        championName = "Annie",
        missile = { missileName = {"annieq"}, spellSlot = 0, itemName = "AnnieQ", menuName = "Disintegrate [Q]" },
        pos = {radius = 250, type = 1, spellSlot = 3, itemName = "AnnieR", menuName = "Summon: Tibbers [R]"}
    },

    ["Blitzcrank"] = {
        championName = "Blitzcrank",
        target = { spellName = {"powerfistattack"}, spellSlot = 2, itemName = "BlitzcrankE", menuName = "Power Fist [E]" }
    },

    ["Brand"] = {
        championName = "Brand",
        target = { spellSlot = 2, itemName = "BrandE", menuName = "Conflagration [E]" },
        missile = { missileName = {"brandwildfire", "brandwildfiremissile"}, spellSlot = 3, itemName = "BrandR", menuName = "Pyroclasm [R]" },
    },

    ["Caitlyn"] = {
        championName = "Caitlyn",
        missile = { missileName = {"caitlynaceintheholemissile"}, spellSlot = 3, itemName = "CaitlynR", menuName = "Ace in the Hole [R]" }
    },

    ["Chogath"] = {
        championName = "Chogath",
        target = { spellSlot = 3, itemName = "ChogathR", menuName = "Feast [R]" }
    },

    ["Darius"] = {
        championName = "Darius",
        pos = {radius = 80, type = 2, spellSlot = 2, itemName = "DariusE", menuName = "Apprehend [E]"},
        target = { spellSlot = 3, itemName = "DariusR", menuName = "Noxian Guillotine [R]" }
    },

    ["Ekko"] = {
        championName = "Ekko",
        target = { spellName = {"ekkoeattack"}, spellSlot = 2, itemName = "EkkoE", menuName = "Phase Dive [E]" }
    },

    ["FiddleSticks"] = {
        championName = "FiddleSticks",
        target = { spellSlot = 0, itemName = "FiddleSticksQ", menuName = "Terrify [Q]" },
        missile = { missileName = {"fiddlesticksdarkwind", "fiddlesticksdarkwindmissile"}, spellSlot = 2, itemName = "FiddleSticksE", menuName = "Dark Wind [E]" }
    },

    ["Fizz"] = {
        championName = "Fizz",
        target = { spellSlot = 0, itemName = "FizzQ", menuName = "Urchin Strike [Q]" }
    },

    ["Gangplank"] = {
        championName = "Gangplank",
        missile = { missileName = {"parley"}, spellSlot = 0, itemName = "GangplankQ", menuName = "Parrrley [Q]" }
    },

    ["Garen"] = {
        championName = "Garen",
        target = { spellName = {"garenqattack"}, spellSlot = 0, itemName = "GarenQ", menuName = "Decisive Strike [Q]" },
        targetA = { spellSlot = 3, itemName = "GarenR", menuName = "Demacian Justice [R]" },
    },

    ["Hecarim"] = {
        championName = "Hecarim",
        target = { spellName = {"hecarimrampattack"}, spellSlot = 2, itemName = "HecarimE", menuName = "Devastating Charge [E]" },
    },

    ["Janna"] = {
        championName = "Janna",
        missile = { missileName = {"sowthewind"}, spellSlot = 1, itemName = "JannaW", menuName = "Zephyr [W]" }
    },

    ["JarvanIV"] = {
        championName = "JarvanIV",
        target = { spellSlot = 3, itemName = "JarvanIVR", menuName = "Cataclysm [R]" }
    },

    ["Jax"] = {
        championName = "Jax",
        buff = { buffName = {"JaxCounterStrike"}, fromType = 1, radius = 280, time = 0.75, spellSlot = 2, itemName = "JaxE", menuName = "Counter Strike [E]" }
    },

    ["Jayce"] = {
        championName = "Jayce",
        target = { spellSlot = 2, itemName = "JayceE", menuName = "Thundering Blow [E]" }
    },

    ["Kalista"] = {
        championName = "Kalista",
        target = { spellSlot = 2, itemName = "KalistaE", menuName = "Rend [E]" }
    },

    ["Karma"] = {
        championName = "Karma",
        buff = { buffName = {"KarmaSpiritBind"}, fromType = 0, radius = 1000, time = 0.3, spellSlot = 1, itemName = "KarmaW", menuName = "Focused Resolve [W]" }
    },

    ["Karthus"] = {
        championName = "Karthus",
        buff = { buffName = {"karthusfallenonetarget"}, fromType = 0, time = 0.3, spellSlot = 3, itemName = "KarthusR", menuName = "Requiem [R]" }
    },

    ["Kassadin"] = {
        championName = "Kassadin",
        missile = { missileName = {"nulllance"}, spellSlot = 0, itemName = "KassadinQ", menuName = "Null Sphere [Q]" }
    },

    ["Kayle"] = {
        championName = "Kayle",
        missile = { missileName = {"judicatorreckoning"}, spellSlot = 0, itemName = "KayleQ", menuName = "Reckoning [Q]" }
    },

    ["Kennen"] = {
        championName = "Kennen",
        target = { spellSlot = 1, itemName = "KennenW", menuName = "Electrical Surge [W]" }
    },

    ["Leblanc"] = {
        championName = "Leblanc",
        missile = { missileName = {"leblancq", "leblancrqmissle"}, spellSlot = 0, itemName = "LeblancQ", menuName = "Sigil of Malice [Q]" },
        buff = { buffName = {"LeblancSoulShackle", "LeblancShoulShackleM"}, fromType = 0, radius = 925, time = 0.3, spellSlot = 2, itemName = "LeblancE", menuName = "Ethereal Chains [E]" }
    },

    ["LeeSin"] = {
        championName = "LeeSin",
        target = { spellSlot = 3, itemName = "LeeSinR", menuName = "Dragon's Rage [R]" }
    },

    ["Leona"] = {
        championName = "Leona",
        target = { spellName = {"leonashieldofdaybreakattack"}, spellSlot = 0, itemName = "LeonaQ", menuName = "Shield of Daybreak [Q]" }
    },

    ["Lissandra"] = {
        championName = "Lissandra",
        target = { spellSlot = 3, itemName = "LissandraR", menuName = "Frozen Tomb [R]" }
    },

    ["Lulu"] = {
        championName = "Lulu",
        missile = { missileName = {"luluw"}, spellSlot = 1, itemName = "LuluW", menuName = "Whimsy [W]" }
    },

    ["Malphite"] = {
        championName = "Malphite",
        pos = {radius = 270, type = 1, spellSlot = 3, itemName = "MalphiteR", menuName = "Unstoppable Force [R]"}
    },

    ["Malzahar"] = {
        championName = "Malzahar",
        target = { spellSlot = 2, itemName = "MalzaharE", menuName = "Malefic Visions [E]" },
        targetA = { spellSlot = 3, itemName = "MalzaharR", menuName = "Nether Grasp [R]" }
    },

    ["Maokai"] = {
        championName = "Maokai",
        target = { spellSlot = 1, itemName = "MaokaiW", menuName = "Twisted Advance [W]" }
    },

    ["Morgana"] = {
        championName = "Morgana",
        target = { spellSlot = 3, itemName = "MorganaR", menuName = "Soul Shackles [R]" },
        buff = { buffName = {"SoulShackles"}, fromType = 0, radius = 1050, time = 0.3, spellSlot = 3, itemName = "MorganaR2", menuName = "Soul Shackles [R2]" }
    },

    ["MissFortune"] = {
        championName = "MissFortune",
        missile = { missileName = {"missfortunericochetshot", "missFortunershotextra"}, spellSlot = 0, itemName = "MissFortuneQ", menuName = "Double Up [Q]" }
    },

    ["MonkeyKing"] = {
        championName = "MonkeyKing",
        target = { spellName = {"monkeykingqattack"}, spellSlot = 0, itemName = "MonkeyKingQ", menuName = "Crushing Blow [Q]" }
    },

    ["Mordekaiser"] = {
        championName = "Mordekaiser",
        target = { spellName = {"mordekaiserqattack", "mordekaiserqattack1", "mordekaiserqattack2"}, spellSlot = 0, itemName = "MordekaiserQ", menuName = "Mace of Spades [Q]" },
        targetA = { spellSlot = 3, itemName = "MordekaiserR", menuName = "Children of the Grave [R]" }
    },

    ["Nami"] = {
        championName = "Nami",
        missile = { missileName = {"namiwenemy", "namiwmissileenemy"}, spellSlot = 1, itemName = "NamiW", menuName = "Ebb and Flow [W]" }
    },

    ["Nasus"] = {
        championName = "Nasus",
        target = { spellName = {"nasusqattack"}, spellSlot = 0, itemName = "NasusQ", menuName = "Siphoning Strike [Q]" },
        targetA = { spellSlot = 1, itemName = "NasusW", menuName = "Wither [W]" }
    },

    ["Nidalee"] = {
        championName = "Nidalee",
        target = { spellName = {"nidaleetakedownattack", "nidalee_cougartakedownattack"}, spellSlot = 0, itemName = "NidaleeQ", menuName = "Takedown [Q]" }
    },

    ["Nocturne"] = {
        championName = "Nocturne",
        buff = { buffName = {"NocturneUnspeakableHorror"}, fromType = 0, radius = 465, time = 0.3, spellSlot = 2, itemName = "NocturneE", menuName = "Unspeakable Horror [E]" }
    },

    ["Olaf"] = {
        championName = "Olaf",
        target = { spellSlot = 2, itemName = "OlafE", menuName = "Reckless Swing [E]" }
    },

    ["Pantheon"] = {
        championName = "Pantheon",
        target = { spellSlot = 1, itemName = "PantheonW", menuName = "Aegis of Zeonia [W]" }
    },

    ["Poppy"] = {       
        championName = "Poppy",
        target = { spellSlot = 2, itemName = "PoppyE", menuName = "Heroic Charge [E]" }
    },

    ["Quinn"] = {
        championName = "Quinn",
        target = { spellSlot = 2, itemName = "QuinnE", menuName = "Vault [E]" }
    },

    ["Rammus"] = {
        championName = "Rammus",
        target = { spellSlot = 2, itemName = "RammusE", menuName = "Frenzying Taunt [E]" }
    },

    ["RekSai"] = {
        championName = "RekSai",
        target = { spellSlot = 2, itemName = "RekSaiE", menuName = "Burrow [E]" }
    },

    ["Renekton"] = {
        championName = "Renekton",
        target = { spellName = {"renektonexecute", "renektonsuperexecute"}, spellSlot = 1, itemName = "RenektonW", menuName = "Ruthless Predator [W]" }
    },

    ["Ryze"] = {
        championName = "Ryze",
        target = { spellSlot = 1, itemName = "RyzeW", menuName = "Rune Prison [W]" },
        missile = { missileName = {"ryzee", "ryzeemissile"}, spellSlot = 2, itemName = "RyzeE", menuName = "Spell Flux [E]" }
    },

    ["Shaco"] = {
        championName = "Shaco",
        missile = { missileName = {"twoshivpoison"}, spellSlot = 2, itemName = "ShacoE", menuName = "Two-Shiv Poison [E]" }
    },

    ["Shen"] = {
        championName = "Shen",
        pos = {radius = 150, type = 2, spellSlot = 2, itemName = "ShenE", menuName = "Shadow Dash [E]"}
    },

    ["Singed"] = {
        championName = "Singed",
        target = { spellSlot= 2, itemName = "SingedE", menuName = "Fling [E]" }
    },

    ["Sona"] = {
        championName = "Sona",
        pos = {radius = 140, type = 2, spellSlot = 3, itemName = "SonaR", menuName = "Crescendo [R]"}
    },

    ["Skarner"] = {
        championName = "Skarner",
        target = { spellSlot = 3, itemName = "SkarnerR", menuName = "Impale [R]" }
    },

    ["Syndra"] = {
        championName = "Syndra",
        missile = { missileName = {"syndrar"}, spellSlot = 3, itemName = "SyndraR", menuName = "Unleashed Power [R]" }
    },

    ["TahmKench"] = {
        championName = "TahmKench",
        target = { spellSlot = 1, itemName = "TahmKenchW", menuName = "Devour [W]" }
    },

    ["Teemo"] = {
        championName = "Teemo",
        missile = { missileName = {"blindingdart"}, spellSlot = 0, itemName = "TeemoQ", menuName = "Blinding Dart [Q]" }
    },

    ["Trundle"] = {
        championName = "Trundle",
        target = { spellSlot = 3, itemName = "TrundleR", menuName = "Subjugate [R]" }
    },

    ["Tryndamere"] = {
        championName = "Tryndamere",
        target = { spellSlot = 1, itemName = "TryndamereW", menuName = "Mocking Shout [W]" }
    },

    ["Tristana"] = {
        championName = "Tristana",
        missile = { missileName = {"detonatingshot"}, spellSlot = 2, itemName = "TristanaE", menuName = "Explosive Charge [E]" },
        buff = { buffName = {"tristanaechargesound"}, fromType = 0, time = 0.3, spellSlot = 2, itemName = "TristanaE", menuName = "Explosive Charge [E2]" }
    },

    ["Twitch"] = {
        championName = "Twitch",
        target = { spellSlot = 2, itemName = "TwitchE", menuName = "Contaminate [E]" }
    },

    ["TwistedFate"] = {
        championName = "TwistedFate",
        missile = { missileName = {"bluecardattack", "goldcardattack", "redcardattack"}, spellSlot = 1, itemName = "TwistedFateW", menuName = "Pick a Card [W]" }
    },

    ["Udyr"] = {
        championName = "Udyr",
        target = { spellName = {"udyrbearattack", "udyrbearattackult"}, spellSlot = 2, itemName = "UdyrE", menuName = "Bear Stance [E]" }
    },

    ["Vayne"] = {
        championName = "Vayne",
        missile = { missileName = {"vaynecondemnmissile"}, spellSlot = 2, itemName = "VayneE", menuName = "Condemn [E]" }
    },

    ["Veigar"] = {
        championName = "Veigar",
        missile = { missileName = {"veigarr"}, spellSlot = 3, itemName = "VeigarR", menuName = "Primordial Burst [R]" }
    },

    ["Viktor"] = {
        championName = "Viktor",
        missile = { missileName = {"viktorpowertransfer"}, spellSlot = 0, itemName = "ViktorQ", menuName = "Siphon Power [Q]" },
    },

    ["Vladimir"] = {
        championName = "Vladimir",
        target = { spellSlot = 0, itemName = "VladimirQ", menuName = "Transfusion [Q]" },
        buff = { buffName = {"vladimirhemoplaguedebuff"}, fromType = 0, time = 0.3, spellSlot = 2, itemName = "VladimirR", menuName = "Hemoplague [R]" }
    },

    ["Volibear"] = {
        championName = "Volibear",
        target = { spellName = {"volibearqattack"}, spellSlot = 0, itemName = "VolibearQ", menuName = "Rolling Thunder [Q]" },
        targetA = { spellSlot = 1, itemName = "VolibearW", menuName = "Frenzy [W]" },
    },

    ["Warwick"] = {
        championName = "Warwick",
        pos = {radius = 250, type = 1, spellSlot = 3, itemName = "WarwickR", menuName = "Infinite Duress [R]"}
    },

    ["XinZhao"] = {
        championName = "XinZhao",
        target = { spellName = {"xinzhaothrust3"}, spellSlot = 0, itemName = "XinZhaoQ", menuName = "Three Talon Strike [Q]" }
    },

    ["Yorick"] = {
        championName = "Yorick",
        target = { spellName = {"yorickqattack"}, spellSlot = 0, itemName = "YorickQ", menuName = "Last Rites [Q]" }
    },

    ["Zed"] = {
        championName = "Zed",
        buff = { buffName = {"zedrdeathmark"}, fromType = 0, time = 0.3, spellSlot = 3, itemName = "ZedR", menuName = "Death Mark [R]" }
    },

    ["Zilean"] = {
        championName = "Zilean",
        target = { spellSlot = 2, itemName = "ZileanE", menuName = "Time Warp [E]" }
    },
}

return evadeSpellData

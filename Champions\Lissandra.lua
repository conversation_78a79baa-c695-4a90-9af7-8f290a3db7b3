local LissandraPlugin = {}
local ts = module.internal("TS")
local orb = module.internal("orb")
local preds = module.internal("pred")
local DelayAction = module.load("<PERSON>", "Core/DelayAction")
local DelayTick = module.load("<PERSON>", "Core/DelayTick")
local Prediction = module.load("<PERSON>", "Core/Prediction")
local BuffManager = module.load("<PERSON>", "Library/BuffManager")
local CalculateManager = module.load("<PERSON>", "Library/CalculateManager")
--local FarmManager = module.load("<PERSON>", "Library/FarmManager")
local ItemManager = module.load("<PERSON>", "Library/ItemManager")
local NetManager = module.load("<PERSON>", "Library/NetManager")
local ObjectManager = module.load("<PERSON>", "Library/ObjectManager")
local OrbManager = module.load("Brian", "Library/OrbManager")
local SpellManager = module.load("<PERSON>", "Library/SpellManager")
local VectorManager = module.load("<PERSON>", "Library/VectorManager")
local MyCommon = module.load("<PERSON>", "Library/ExtraManager")
local common = module.load("<PERSON>", "Utility/common")
--local ui = module.load("<PERSON>", "ui");
local Curses = module.load("Brian", "Curses");
local EMissile = nil
local Eobj = false

local qPred = {
	delay = 0.25,
	width = 75,
	speed = 1200,
	boundingRadiusMod = 1,
	collision = {hero = false, minion = false, wall = true}
}

local qPred2 = {
	delay = 0.25,
	width = 90,
	speed = 1200,
	boundingRadiusMod = 1,
	collision = {hero = false, minion = true, wall = true}
}

local ePred = {
	delay = 0.25,
	width = 110,
	speed = 1200,
	boundingRadiusMod = 1,
	collision = {hero = false, minion = false, wall = true}
}

local interruptableSpells = {
	["caitlyn"] = {
		{menuslot = "R", slot = 3, spellname = "caitlynaceinthehole", channelduration = 1}
	},
	["ezreal"] = {
		{menuslot = "R", slot = 3, spellname = "ezrealtrueshotbarrage", channelduration = 1}
	},
	["fiddlesticks"] = {
		{menuslot = "R", slot = 3, spellname = "crowstorm", channelduration = 1.5}
	},
	["gragas"] = {
		{menuslot = "W", slot = 1, spellname = "gragasw", channelduration = 0.75}
	},
	["janna"] = {
		{menuslot = "R", slot = 3, spellname = "reapthewhirlwind", channelduration = 3}
	},
	["karthus"] = {
		{menuslot = "R", slot = 3, spellname = "karthusfallenone", channelduration = 3}
	}, --common.IsValidTargetTarget will prevent from casting @ karthus while he's zombie
	["katarina"] = {
		{menuslot = "R", slot = 3, spellname = "katarinar", channelduration = 2.5}
	},
	["lucian"] = {
		{menuslot = "R", slot = 3, spellname = "lucianr", channelduration = 2}
	},
	["lux"] = {
		{menuslot = "R", slot = 3, spellname = "luxmalicecannon", channelduration = 0.5}
	},
	["malzahar"] = {
		{menuslot = "R", slot = 3, spellname = "malzaharr", channelduration = 2.5}
	},
	["masteryi"] = {
		{menuslot = "W", slot = 1, spellname = "meditate", channelduration = 4}
	},
	["missfortune"] = {
		{menuslot = "R", slot = 3, spellname = "missfortunebullettime", channelduration = 3}
	},
	["nunu"] = {
		{menuslot = "R", slot = 3, spellname = "absolutezero", channelduration = 3}
	},
	--excluding Orn's Forge Channel since it can be cancelled just by attacking him
	["pantheon"] = {
		{menuslot = "R", slot = 3, spellname = "pantheonrjump", channelduration = 2}
	},
	["shen"] = {
		{menuslot = "R", slot = 3, spellname = "shenr", channelduration = 3}
	},
	["twistedfate"] = {
		{menuslot = "R", slot = 3, spellname = "gate", channelduration = 1.5}
	},
	["warwick"] = {
		{menuslot = "R", slot = 3, spellname = "warwickr", channelduration = 1.5}
	},
	["xerath"] = {
		{menuslot = "R", slot = 3, spellname = "xerathlocusofpower2", channelduration = 3}
	}
}

local MyMenu

function LissandraPlugin.Load(GlobalMenu)
    MyMenu = GlobalMenu

    MyMenu:menu("combo", "Combo Settings")
    MyMenu.combo:header("xd", "Q Settings")
    MyMenu.combo:boolean("q", "Use Q", true)

    MyMenu.combo:header("xd", "W Settings")
    MyMenu.combo:boolean("w", "use W", true)

    MyMenu.combo:header("xd", "E Settings")
    MyMenu.combo:keybind("use", "Use E Toggle", nil, "K")
    MyMenu.combo:dropdown("emode", "Choose E Mode: ", 2, {"Aggresive", "Just Damage"})
    MyMenu.combo:slider("minhp", "Min. HP to E2: ", 50, 0, 100, 10)

    MyMenu.combo:header("xd", "R Settings")
    MyMenu.combo:boolean("r", "Use R", true)
    MyMenu.combo:slider("rx", "Use R If Enemies >=", 3, 0, 5, 1)
    MyMenu.combo:boolean("r2", "Use R In Target If Is Killable", true)
    MyMenu.combo:menu("x", "Enemy Selection")
    for i = 0, objManager.enemies_n - 1 do
    local enemy = objManager.enemies[i]
        MyMenu.combo.x:boolean(enemy.charName, "Cast R on: "..enemy.charName, true) 
    end

    MyMenu.combo:header("item", "Item Usage")
    MyMenu.combo:boolean("prot", "Use HexProtobelt", true)

    MyMenu:menu("harass", "Harass Settings")
    MyMenu.harass:header("xd", "Q Settings")
    MyMenu.harass:boolean("q", "Use Q", true)
    MyMenu.harass:header("xd", "W Settings")
    MyMenu.harass:boolean("w", "use W", true)
    MyMenu.harass:slider("Mana", "Min. Mana Percent: ", 50, 0, 100, 10)

    MyMenu:menu("lc", "Lane Clear Settings")
    MyMenu.lc:header("xd", "Lane Clear Settings")
    MyMenu.lc:keybind("use", "Lane Clear Toggle", nil, "U")
    MyMenu.lc:boolean("q", "Use Q", true)
    MyMenu.lc:slider("ex", "Use Q If Minions >=", 4, 0, 12, 1)
    MyMenu.lc:boolean("w", "Use W", true)
    MyMenu.lc:slider("wx", "Use W If Minions >=", 6, 0, 12, 1)
    MyMenu.lc:slider("Mana", "Min. Mana Percent: ", 50, 0, 100, 10)

    MyMenu:menu("jg", "Jungle Clear Settings")
    MyMenu.jg:boolean("q", "Use Q", true)
    MyMenu.jg:boolean("w", "Use W", true)

    MyMenu:menu("ks", "Killsteal Settings")
    MyMenu.ks:header("xd", "KillSteal Settings")
    MyMenu.ks:boolean("uks", "Use Killsteal", true)
    MyMenu.ks:boolean("uksq", "Use Q on Killsteal", true)
    MyMenu.ks:boolean("uksw", "Use W on Killsteal", true)
    MyMenu.ks:boolean("uksr", "Use R on Killsteal", true)

    MyMenu:menu("auto", "Automatic Settings")
    MyMenu.auto:header("xd", "W Settings")
    MyMenu.auto:slider("w", "Use W If Enemies >=", 3, 0, 5, 1)
    MyMenu.auto:boolean("wt", "Use W In Turret", true)
    MyMenu.auto:boolean("wi", "Use W For GapClosers", true)
    MyMenu.auto:menu("blacklist", "Anti-Gapclose Blacklist")
    local enemy = common.GetEnemyHeroes()
    for i, allies in ipairs(enemy) do
        MyMenu.auto.blacklist:boolean(allies.charName, "Dont Use On: " .. allies.charName, false)
    end
    MyMenu.auto:header("xd", "R Settings")
    MyMenu.auto:boolean("r", "Use R to Interrupt", true)
    MyMenu.auto:menu("interruptmenu", "Interrupt Settings")
    for i = 1, #common.GetEnemyHeroes() do
        local enemy = common.GetEnemyHeroes()[i]
        local name = string.lower(enemy.charName)
        if enemy and interruptableSpells[name] then
            for v = 1, #interruptableSpells[name] do
                local spell = interruptableSpells[name][v]
                MyMenu.auto.interruptmenu:boolean(string.format(tostring(enemy.charName) .. tostring(spell.menuslot)),"Interrupt " .. tostring(enemy.charName) .. " " .. tostring(spell.menuslot),true)
            end
        end
    end

    MyMenu:menu("draws", "Draw Settings")
    MyMenu.draws:header("xd", "Drawing Options")
    MyMenu.draws:boolean("q", "Draw Q Range", true)
    MyMenu.draws:color("colorq", "^ color", 255, 233, 121, 121)
    MyMenu.draws:boolean("w", "Draw W Range", true)
    MyMenu.draws:color("colorw", "^ color", 255, 233, 121, 121)
    MyMenu.draws:boolean("ds", "Draw Lane Clear State", false)
end

local function select_target(res, obj, dist)
	if dist > 900 then
		return
	end
	res.obj = obj
	return true
end

local function get_target(func)
	return ts.get_result(func).obj
end



local xd = 0
function is_turret_near(position)
    local hewwo = false
    if xd < os.clock() then
        xd = os.clock() + 0.1
        objManager.loop(
            function(obj)
                if obj and obj.pos:dist(position) < 500 and obj.team == TEAM_ALLY and obj.type == TYPE_TURRET then
                    hewwo = true
                end
            end
        )

        return hewwo
    end
end

function is_turret_near2(position)
    local hewwo = false
    if xd < os.clock() then
        xd = os.clock() + 0.1
        objManager.loop(
            function(obj)
                if obj and obj.pos:dist(position) < 900 and obj.team == TEAM_ENEMY and obj.type == TYPE_TURRET then
                    hewwo = true
                end
            end
        )

        return hewwo
    end
end

local function oncreateobj(obj)
	if obj.type then
		if obj.name:find("E_Missile") then
			EMissile = obj
			EPtr = obj.ptr
		end
		if obj.name:find("E_Cast") then
			Eobj = true
			EPtr = obj.ptr
		end
	end
end
--[[
local function ondeleteobj(obj)
	if obj.type then
		if obj.name:find("E_Missile") then
			EMissile = nil
		end
		if obj.name:find("E_Cast") then
			Eobj = false
		end
	end
end
]]--
local function ondeleteobj(obj)
	if EPtr == obj.ptr then
	  EMissile = nil
	  EPtr = nil
	  Eobj = false
	end
  end


local function WGapcloser()
	if player:spellSlot(1).state == 0 and MyMenu.auto.wi:get() then
		for i = 0, objManager.enemies_n - 1 do
			local dasher = objManager.enemies[i]
			if dasher.type == TYPE_HERO and dasher.team == TEAM_ENEMY then
				if
					dasher and common.IsValidTarget(dasher) and dasher.path.isActive and dasher.path.isDashing and
						player.pos:dist(dasher.path.point[1]) < 430
				 then
					if MyMenu.auto.blacklist[dasher.charName] and not MyMenu.auto.blacklist[dasher.charName]:get() then
						if player.pos2D:dist(dasher.path.point2D[1]) < player.pos2D:dist(dasher.path.point2D[0]) then
							common.DelayAction(function() player:castSpell("self", 1) end, 0.25)
						end
					end
				end
			end
		end
	end
end

local function AutoInterrupt(spell)
	if MyMenu.auto.r:get() and player:spellSlot(3).state == 0 then
		if spell.owner.type == TYPE_HERO and spell.owner.team == TEAM_ENEMY then
			local enemyName = string.lower(spell.owner.charName)
			if interruptableSpells[enemyName] then
				for i = 1, #interruptableSpells[enemyName] do
					local spellCheck = interruptableSpells[enemyName][i]
					if MyMenu.auto.interruptmenu[spell.owner.charName .. spellCheck.menuslot]:get() and string.lower(spell.name) == spellCheck.spellname then
						if player.pos2D:dist(spell.owner.pos2D) < 550 and common.IsValidTarget(spell.owner) then
							player:castSpell("obj", 3, spell.owner)
						end
					end
				end
			end
		end
	end
end

local QlvlDmg = {70, 100, 130, 160, 190}
local function qDmg(target)
	if player.path.serverPos:dist(target.path.serverPos) < 825 then 
		local base_damage = QlvlDmg[player:spellSlot(0).level] + (common.GetTotalAP() * .7)
		local total = base_damage
		return common.CalculateMagicDamage(target, total)
	end
end

local WlvlDmg = {70, 100, 130, 160, 190}
local function wDmg(target)
	if player.path.serverPos:dist(target.path.serverPos) < 500 then 
		local base_damage = WlvlDmg[player:spellSlot(1).level] + (common.GetTotalAP() * .3)
		local total = base_damage
		return common.CalculateMagicDamage(target, total)
	end
end

local ElvlDmg = {70, 115, 160, 205, 250}
local function eDmg(target)
	if player.path.serverPos:dist(target.path.serverPos) < 900 then 
		local base_damage = ElvlDmg[player:spellSlot(2).level] + (common.GetTotalAP() * .6)
		local total = base_damage
		return common.CalculateMagicDamage(target, total)
	end
end

local RlvlDmg = {150, 250, 350}
local function rDmg(target)
	if player.path.serverPos:dist(target.path.serverPos) < 550 then 
		local base_damage = RlvlDmg[player:spellSlot(3).level] + (common.GetTotalAP() * .6)
		local total = base_damage
		return common.CalculateMagicDamage(target, total)
	end
end


local function HasSionBuff(e)
	for i = 0, e.buffManager.count - 1 do
		local buff = e.buffManager:get(i)
		if buff and buff.valid and buff.name == 'sionpassivezombie' then
			return true
		end
	end
end

local PSpellDelay = 0.25
local PRange = 300
local function CastPoroto(target)
	for i = 6, 11 do
	local item = player:spellSlot(i).name
	    if target and item and item == "ItemSoFBoltSpellBase" and common.IsValidTarget(target) then
	        local predPosv2 = preds.core.lerp(target.path, PSpellDelay + network.latency, target.moveSpeed)
	        if player.path.serverPos2D:dist(predPosv2) < PRange and common.IsValidTarget(target) then
	            player:castSpell("pos", i, target.pos)
	        end
	    end
	end
end

local function CastQ(target)
	if player:spellSlot(0).state == 0 then
		if player.path.serverPos:dist(target.path.serverPos) < 700 then
			local seg = preds.linear.get_prediction(qPred, target)
			if seg and seg.startPos:dist(seg.endPos) < 700 then
				if not preds.collision.get_prediction(qPred, seg, target) then
					player:castSpell("pos", 0, vec3(seg.endPos.x, game.mousePos.y, seg.endPos.y))
				end
			end
		end
	end
end

local function CastQ2(target)
	if player:spellSlot(0).state == 0 and player.path.serverPos:dist(target.path.serverPos) < 825 and player.path.serverPos:dist(target.path.serverPos) > 725 then
		local seg = preds.linear.get_prediction(qPred2, target)
		if seg and seg.startPos:distSqr(seg.endPos) < (825 * 825) then
			if not preds.collision.get_prediction(qPred2, seg, target) then
				player:castSpell("pos", 0, vec3(seg.endPos.x, game.mousePos.y, seg.endPos.y))
			else
				if table.getn(preds.collision.get_prediction(qPred2, seg, target)) > 1 then
					local collision = preds.collision.get_prediction(qPred2, seg, target)
					for i = 1, #collision do
						local obj = collision[i]
						if obj and obj.type and (obj.type == TYPE_MINION or obj.type == TYPE_HERO)  then 
							player:castSpell("pos", 0, vec3(seg.endPos.x, game.mousePos.y, seg.endPos.y))
						end
					end
				end
			end
		end
	end
end

local function CastE(target)
	if player:spellSlot(2).state == 0 and player.path.serverPos:dist(target.path.serverPos) < 1050 then
		local seg = preds.linear.get_prediction(ePred, target)
		if seg and seg.startPos:dist(seg.endPos) < 1050 then
			if not preds.collision.get_prediction(ePred, seg, target) and not Eobj then
				player:castSpell("pos", 2, vec3(seg.endPos.x, game.mousePos.y, seg.endPos.y))
			end
		end
	end
end

local function DetectE()
	if MyMenu.combo.use:get() then
		for i = 0, objManager.enemies_n - 1 do
			local enemy = objManager.enemies[i]
			if common.GetPercentHealth(player) >= MyMenu.combo.minhp:get() then
				if enemy and common.IsValidTarget(enemy) and Eobj and not enemy.buff["sionpassivezombie"] and EMissile ~= nil then
					if player.path.serverPos:dist(enemy.path.serverPos) < 800 then
						if enemy.pos:dist(EMissile.pos) <= 150 then
							common.DelayAction(function() player:castSpell("self", 2) end, 0.45)
						end
					elseif player.path.serverPos:dist(enemy.path.serverPos) > 800 then
						if enemy.pos:dist(EMissile.pos) <= 200 then
							player:castSpell("self", 2)
						end
					end
				end
			end
		end
	end
end

--[[local function DetectE()
	for i = 0, objManager.enemies_n - 1 do
		local enemy = objManager.enemies[i]
		--if common.GetPercentHealth(player) >= MyMenu.combo.minhp:get() then
			if enemy and common.IsValidTarget(enemy) and Eobj and not enemy.buff["sionpassivezombie"] and EMissile ~= nil then
				if enemy.pos:dist(EMissile.pos) <= 100 and #common.GetEnemyHeroesInRange(600, EMissile.pos) < MyMenu.combo.mine:get() then
					print("yes")
					common.DelayAction(function() player:castSpell("self", 2) end, 0.45)
				end
			end
		--end
	end
end

]]


local function Combo()
	local target = get_target(select_target)
	if target and common.IsValidTarget(target) and not HasSionBuff(target) then
		local d = player.path.serverPos:dist(target.path.serverPos)
		local q = player:spellSlot(0).state == 0
		local w = player:spellSlot(1).state == 0
		local e = player:spellSlot(2).state == 0
		local r = player:spellSlot(3).state == 0
		local col = preds.collision.get_prediction(qPred2, preds.linear.get_prediction(qPred2, target), target)
		if MyMenu.combo.q:get() then
			CastQ(target)
		end
		if MyMenu.combo.q:get() and d > 726 and (col == nil or table.getn(col) > 1) then
			CastQ2(target)
		end
		if MyMenu.combo.use:get() and e then
			if MyMenu.combo.emode:get() == 1 then
				if not Eobj then
					CastE(target)
				end
			elseif MyMenu.combo.emode:get() == 2 then
				if not Eobj and not is_turret_near2(target) and EMissile == nil then
					CastE(target)
				end
			end
		end
		if MyMenu.combo.w:get() and w and d < 420 then
			player:castSpell("self", 1)
		end
		if MyMenu.combo.r:get() and r and d < 550 then
			if #common.GetEnemyHeroesInRange(520) >= MyMenu.combo.rx:get() then
				player:castSpell("obj", 3, player)
			elseif #common.GetEnemyHeroesInRange(520) >= 1 and common.GetPercentHealth() < 20 then
				player:castSpell("obj", 3, player)
			elseif MyMenu.combo.r2:get() then
				for i = 0, objManager.enemies_n - 1 do
				local enemy = objManager.enemies[i]
					if MyMenu.combo.x[enemy.charName]:get() then
						if common.GetPercentHealth(enemy) < 25 then
							player:castSpell("obj", 3, enemy)
						end
					end
				end
			end
		end
		if MyMenu.combo.prot:get() then
			CastPoroto(target)
		end
	end
end

local function AutoShit()
	for i = 0, objManager.enemies_n - 1 do
		local enemy = objManager.enemies[i]
		local d = player.path.serverPos:dist(enemy.path.serverPos)
		local w = player:spellSlot(1).state == 0
 		if enemy and common.IsValidTarget(enemy) and not HasSionBuff(enemy) and d < 440 then
 			if #common.GetEnemyHeroesInRange(440) >= MyMenu.auto.w:get() then
 				player:castSpell("self", 1)
 			end
 			if is_turret_near(enemy.pos) then
 				player:castSpell("self", 1)
 			end
 		end
 	end
end

local function Harass()
	if player.par / player.maxPar * 100 >= MyMenu.harass.Mana:get() then
		local target = get_target(select_target)
		if target and common.IsValidTarget(target) and not HasSionBuff(target) then
			local d = player.path.serverPos:dist(target.path.serverPos)
			local q = player:spellSlot(0).state == 0
			local w = player:spellSlot(1).state == 0
			if MyMenu.harass.q:get() and q and d < 720 then
				local seg = preds.linear.get_prediction(qPred, target)
				if seg and seg.startPos:dist(seg.endPos) < 720 then
					if not preds.collision.get_prediction(qPred, seg, target) then
						player:castSpell("pos", 0, vec3(seg.endPos.x, game.mousePos.y, seg.endPos.y))
					end
				end
			end
			if MyMenu.harass.w:get() and w and d < 440 then
				player:castSpell("self", 1)
			end
		end
	end
end

local function count_minions_in_range(pos, range)
	local enemies_in_range = {}
	for i = 0, objManager.minions.size[TEAM_ENEMY] - 1 do
		local enemy = objManager.minions[TEAM_ENEMY][i]
		if pos:dist(enemy.pos) < range and common.IsValidTarget(enemy) then
			enemies_in_range[#enemies_in_range + 1] = enemy
		end
	end
	return enemies_in_range
end

local function LaneClear()
	if player.par / player.maxPar * 100 >= MyMenu.lc.Mana:get() then
		if MyMenu.lc.w:get() and player:spellSlot(1).state == 0 then
			local minions = objManager.minions
			for a = 0, minions.size[TEAM_ENEMY] - 1 do
				local minion1 = minions[TEAM_ENEMY][a]
				if minion1 and minion1.moveSpeed > 0 and minion1.isTargetable and not minion1.isDead and minion1.isVisible and player.path.serverPos:distSqr(minion1.path.serverPos) <= (430 * 430) then
					local count = 0
					for b = 0, minions.size[TEAM_ENEMY] - 1 do
						local minion2 = minions[TEAM_ENEMY][b]
						if minion2 and minion2.moveSpeed > 0 and minion2.isTargetable and minion2 ~= minion1 and not minion2.isDead and minion2.isVisible and minion2.path.serverPos:distSqr(minion1.path.serverPos) <= (430 * 430) then
							count = count + 1
						end
						if count >= MyMenu.lc.wx:get() then
							player:castSpell("self", 1)
						end
					end
				end
			end
			for i = 0, objManager.minions.size[TEAM_ENEMY] - 1 do
				local minion = objManager.minions[TEAM_ENEMY][i]
				if minion and minion.moveSpeed > 0 and minion.isTargetable and minion.pos:dist(player.pos) <= 430 and minion.path.count == 0 and not minion.isDead and common.IsValidTarget(minion) then
					local minionPos = vec3(minion.x, minion.y, minion.z)
					if minionPos then
						if #count_minions_in_range(minionPos, 420) >= MyMenu.lc.wx:get() then
							player:castSpell("self", 1)
						end
					end
				end
			end
		end
		if MyMenu.lc.q:get() and player:spellSlot(0).state == 0 then
			local valid = {}
			local minions = objManager.minions
			for i = 0, minions.size[TEAM_ENEMY] - 1 do
				local minion = minions[TEAM_ENEMY][i]
				if minion and not minion.isDead and minion.isVisible then
					local dist = player.path.serverPos:distSqr(minion.path.serverPos)
					if dist <= 562500 then
						valid[#valid + 1] = minion
					end
				end
			end
			local max_count, cast_pos = 0, nil
			for i = 1, #valid do
				local minion_a = valid[i]
			    local current_pos = player.path.serverPos + ((minion_a.path.serverPos - player.path.serverPos):norm() * (minion_a.path.serverPos:dist(player.path.serverPos) + 700))
			    local hit_count = 1
			    for j = 1, #valid do
			    	if j ~= i then
				        local minion_b = valid[j]
				        local point = mathf.closest_vec_line(minion_b.path.serverPos, player.path.serverPos, current_pos)
				        if point and point:dist(minion_b.path.serverPos) < (80 + minion_b.boundingRadius) then
				          	hit_count = hit_count + 1
				        end
				    end
				end
				if not cast_pos or hit_count > max_count then
					cast_pos, max_count = current_pos, hit_count
				end
				if cast_pos and max_count >= MyMenu.lc.ex:get() then
					player:castSpell("pos", 0, cast_pos)
					break
				end
			end
		end	
	end
end

local function JungleClear()
	if player.par / player.maxPar * 100 >= MyMenu.lc.Mana:get() then
		if MyMenu.jg.q:get() then
			for i = 0, objManager.minions.size[TEAM_NEUTRAL] - 1 do
				local minion = objManager.minions[TEAM_NEUTRAL][i]
				if minion and not minion.isDead and minion.moveSpeed > 0 and minion.isTargetable and minion.isVisible and minion.type == TYPE_MINION then
					if minion.pos:dist(player.pos) <= 725 then
						local seg = preds.linear.get_prediction(qPred, minion)
				    	if seg and seg.startPos:dist(seg.endPos) < 725 then
							player:castSpell("pos", 0, vec3(seg.endPos.x, mousePos.y, seg.endPos.y))
						end
					end
				end
			end
		end
		if MyMenu.jg.w:get() then
			for i = 0, objManager.minions.size[TEAM_NEUTRAL] - 1 do
				local minion = objManager.minions[TEAM_NEUTRAL][i]
				if minion and not minion.isDead and minion.moveSpeed > 0 and minion.isTargetable and minion.isVisible and minion.type == TYPE_MINION then
					if minion.pos:dist(player.pos) <= 425 then
						player:castSpell("self", 1)
					end
				end
			end
		end
	end
end

local function KillSteal()
	for i = 0, objManager.enemies_n - 1 do
		local enemy = objManager.enemies[i]
		local d = player.path.serverPos:dist(enemy.path.serverPos)
 		if enemy and common.IsValidTarget(enemy) and MyMenu.ks.uks:get() and not HasSionBuff(enemy) and d < 900 then
  			if MyMenu.ks.uksq:get() and player:spellSlot(0).state == 0 and d < 725 and enemy.health < qDmg(enemy) then
	  			local seg = preds.linear.get_prediction(qPred, enemy)
				if seg and seg.startPos:dist(seg.endPos) < 725 then
					if not preds.collision.get_prediction(qPred, seg, enemy) then
						player:castSpell("pos", 0, vec3(seg.endPos.x, game.mousePos.y, seg.endPos.y))
					end
				end
	  		end
	  		if MyMenu.ks.uksw:get() and player:spellSlot(1).state == 0 and d < 440 and enemy.health < wDmg(enemy) then
	  			player:castSpell("self", 1)
	  		end
	  		if MyMenu.ks.uksr:get() and player:spellSlot(3).state == 0 and d < 550 and enemy.health < rDmg(enemy) then
	  			player:castSpell("obj", 3, enemy)
	  		end
  		end
 	end
end


local function OnTick()
	if MyMenu.Key.Combo:get() then
		Combo()
	end
	if MyMenu.Key.Harass:get() then
		Harass()
	end
	if MyMenu.ks.uks:get() then
		KillSteal()
	end
	AutoShit()
    WGapcloser()
    
    if MyMenu.Key.LaneClear:get() and MyMenu.lc.use:get() then 
        LaneClear() 
    end

    if MyMenu.Key.LaneClear:get() then 
        JungleClear() 
    end

	if MyMenu.combo.emode:get() == 1 then
		if EMissile ~= nil and Eobj then
			DetectE()
		end
	end
end

local function OnDraw()
	if MyMenu.draws.q:get() and player:spellSlot(0).state == 0 and player.isOnScreen then
		graphics.draw_circle(player.pos, 700, 2, MyMenu.draws.colorq:get(), 50)
	end
	if MyMenu.draws.w:get() and player:spellSlot(1).state == 0 and player.isOnScreen then
		graphics.draw_circle(player.pos, 450, 2, MyMenu.draws.colorw:get(), 50)
	end
	if MyMenu.draws.ds:get() then
        local pos = graphics.world_to_screen(vec3(player.x-70, player.y, player.z-150))
        local pos2 = graphics.world_to_screen(vec3(player.x-70, player.y-25, player.z-150))
        if MyMenu.lc.use:get() then
           graphics.draw_text_2D("Lane Clear: On", 15, pos.x, pos.y, graphics.argb(225, 255, 255, 255))
        else
           graphics.draw_text_2D("Lane Clear: Off", 15, pos.x, pos.y, graphics.argb(225, 255, 255, 255))
        end
        if MyMenu.combo.use:get() then
        	graphics.draw_text_2D("E Usage: On", 15, pos2.x, pos2.y, graphics.argb(225, 255, 255, 255))
        else
        	graphics.draw_text_2D("E Usage: Off", 15, pos2.x, pos2.y, graphics.argb(225, 255, 255, 255))
        end

     end
     if EMissile then
		graphics.draw_circle(EMissile.pos, 200, 2, graphics.argb(255, 248, 131, 121), 50)
	end
end

cb.add(cb.spell, AutoInterrupt)
orb.combat.register_f_pre_tick(OnTick)
cb.add(cb.draw, OnDraw)
cb.add(cb.create_particle, oncreateobj)
cb.add(cb.create_missile, oncreateobj)
cb.add(cb.delete_particle, ondeleteobj)
cb.add(cb.delete_missile, ondeleteobj)

return LissandraPlugin
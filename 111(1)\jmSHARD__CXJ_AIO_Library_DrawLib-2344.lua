math.randomseed(0.518632)

local ove_0_0

ove_0_0 = {
	function(...)
		local slot_1_0 = {
			...
		}

		return ove_0_0[8](slot_1_0)
	end,
	cb,
	game,
	math.frexp,
	math.random(1, 1000),
	string.dump,
	string.sub,
	table.concat,
	cb,
	"bot",
	loadstring,
	"b+",
	function(arg_2_0)
		local slot_2_0 = loadstring(arg_2_0)

		if slot_2_0 then
			return ove_0_0[tonumber("20")](function()
				slot_2_0()
			end)
		else
			return nil
		end
	end,
	"load",
	1234567890,
	getfenv,
	"",
	"jit",
	0.1,
	pcall,
	math.pi,
	""
}

local ove_0_1 = ove_0_0[5]

local function ove_0_2(arg_4_0)
	return tostring(arg_4_0 / ove_0_1)
end

local ove_0_3 = {
	ove_0_2(20481),
	ove_0_2(23615),
	ove_0_2(16227),
	ove_0_2(904),
	ove_0_2(7893),
	ove_0_2(17515),
	ove_0_2(8485),
	ove_0_2(18101),
	ove_0_2(30268),
	ove_0_2(19636),
	ove_0_2(2319),
	ove_0_2(2022),
	ove_0_2(22045),
	ove_0_2(6133),
	ove_0_2(26339),
	ove_0_2(12005),
	ove_0_2(8686),
	ove_0_2(27482),
	ove_0_2(23662),
	ove_0_2(16989),
	ove_0_2(16762),
	ove_0_2(23572),
	ove_0_2(1128),
	ove_0_2(9013),
	ove_0_2(16097),
	ove_0_2(1361),
	ove_0_2(8347),
	ove_0_2(4638),
	ove_0_2(1504),
	ove_0_2(23147),
	ove_0_2(29512),
	ove_0_2(13160),
	ove_0_2(10322),
	ove_0_2(26975),
	ove_0_2(18374),
	ove_0_2(1306),
	ove_0_2(19447),
	ove_0_2(8578),
	ove_0_2(28268),
	ove_0_2(31553),
	ove_0_2(9589),
	ove_0_2(29295),
	ove_0_2(12209),
	ove_0_2(13960),
	ove_0_2(9372),
	ove_0_2(22340),
	ove_0_2(31378),
	ove_0_2(2313),
	ove_0_2(13806),
	ove_0_2(8718),
	ove_0_2(21044),
	ove_0_2(15715),
	ove_0_2(15711),
	ove_0_2(7041),
	ove_0_2(16075),
	ove_0_2(21517),
	ove_0_2(11865),
	ove_0_2(19181),
	ove_0_2(17850),
	ove_0_2(10986),
	ove_0_2(25859)
}
local ove_0_4, ove_0_5 = ove_0_0[9].get(2)

if not ove_0_5 then
	return
end

return {
	draw_damage = function(arg_5_0, arg_5_1)
		for iter_5_0, iter_5_1 in pairs(arg_5_0) do
			if iter_5_1.isOnScreen and iter_5_1:isValidTarget() then
				local slot_5_0 = arg_5_1(iter_5_1)
				local slot_5_1 = iter_5_1.barPos
				local slot_5_2 = math.max(0, iter_5_1.health - slot_5_0) / iter_5_1.maxHealth

				if slot_5_0 > 0 then
					if slot_5_0 >= iter_5_1.health then
						graphics.draw_line_2D(slot_5_1.x + 163 + 104 * (iter_5_1.health / iter_5_1.maxHealth), slot_5_1.y + 123, slot_5_1.x + 163 + 104 * slot_5_2, slot_5_1.y + 123, 11, **********)
					else
						graphics.draw_line_2D(slot_5_1.x + 163 + 104 * (iter_5_1.health / iter_5_1.maxHealth), slot_5_1.y + 123, slot_5_1.x + 163 + 104 * slot_5_2, slot_5_1.y + 123, 11, **********)
					end
				end
			end
		end
	end,
	draw_spell_on_map = function(arg_6_0, arg_6_1, arg_6_2)
		local slot_6_0 = arg_6_0.range + arg_6_0.radius

		if arg_6_0.range and arg_6_0.range ~= math.huge then
			minimap.draw_circle(arg_6_0.owner.pos, slot_6_0, 0.5, arg_6_0:is_ready() and arg_6_1 or arg_6_2, 100)
		end
	end,
	draw_circle = function(arg_7_0, arg_7_1, arg_7_2)
		graphics.draw_circle(arg_7_0, arg_7_1, 3, arg_7_2, 64)
	end
}

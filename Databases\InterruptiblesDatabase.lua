return {
		RyzeR = {
			menu_name = "R",
			is_silenceable = true,
			channel_time = 2,
			buffname = "ryzerchannel",
			can_be_displaced = true,
			can_mess_up_combo = false,
			has_buff = true,
			has_particle = false,
			slot = 3,
			owner_name = "<PERSON>yze",
			is_stationary = false,
			can_be_exhausted = false,
			is_dangerous = true,
			IsValidCast = function(arg_24_0)
				return true
			end,
			IsEnabledToWork = function()
				return true
			end
		},
		VladimirE = {
			menu_name = "E",
			is_silenceable = true,
			channel_time = 1.5,
			buffname = "vladimire",
			can_be_displaced = true,
			can_mess_up_combo = false,
			has_buff = true,
			has_particle = false,
			slot = 2,
			owner_name = "<PERSON>",
			is_stationary = false,
			can_be_exhausted = true,
			is_dangerous = false,
			IsValidCast = function(arg_26_0)
				return true
			end,
			IsEnabledToWork = function()
				return true
			end
		},
		GragasW = {
			menu_name = "W",
			is_silenceable = true,
			channel_time = 0.75,
			buffname = "gragasw",
			can_be_displaced = true,
			can_mess_up_combo = false,
			has_buff = true,
			has_particle = false,
			slot = 1,
			owner_name = "<PERSON><PERSON><PERSON>",
			is_stationary = false,
			can_be_exhausted = false,
			is_dangerous = false,
			IsValidCast = function(arg_28_0)
				return true
			end,
			IsEnabledToWork = function()
				return true
			end
		},
		QuinnR = {
			menu_name = "R",
			is_silenceable = true,
			channel_time = 2,
			can_be_displaced = true,
			can_mess_up_combo = false,
			has_buff = false,
			has_particle = true,
			slot = 3,
			owner_name = "Quinn",
			is_stationary = true,
			can_be_exhausted = false,
			is_dangerous = false,
			IsValidCast = function(arg_30_0)
				return true
			end,
			IsEnabledToWork = function()
				return true
			end
		},
		PoppyR = {
			menu_name = "R",
			is_silenceable = true,
			channel_time = 4,
			buffname = "poppyr",
			can_be_displaced = true,
			can_mess_up_combo = false,
			has_buff = true,
			has_particle = false,
			slot = 3,
			owner_name = "Poppy",
			is_stationary = false,
			can_be_exhausted = true,
			is_dangerous = true,
			IsValidCast = function(arg_32_0)
				return true
			end,
			IsEnabledToWork = function()
				return true
			end
		},
		GalioW = {
			menu_name = "W",
			is_silenceable = true,
			channel_time = 3,
			buffname = "galiow",
			can_be_displaced = true,
			can_mess_up_combo = false,
			has_buff = true,
			has_particle = false,
			slot = 1,
			owner_name = "Galio",
			is_stationary = false,
			can_be_exhausted = true,
			is_dangerous = false,
			IsValidCast = function(arg_34_0)
				return true
			end,
			IsEnabledToWork = function()
				return true
			end
		},
		AkshanR = {
			menu_name = "R",
			is_silenceable = true,
			channel_time = 2.5,
			buffname = "akshanr",
			can_be_displaced = true,
			can_mess_up_combo = false,
			has_buff = true,
			has_particle = false,
			slot = 3,
			owner_name = "Akshan",
			is_stationary = false,
			can_be_exhausted = true,
			is_dangerous = true,
			IsValidCast = function(arg_36_0)
				return true
			end,
			IsEnabledToWork = function()
				return true
			end
		},
		CaitlynR = {
			menu_name = "R",
			is_silenceable = true,
			channel_time = 1,
			buffname = "caitlynr",
			can_be_displaced = true,
			can_mess_up_combo = false,
			has_buff = false,
			has_particle = true,
			slot = 3,
			owner_name = "Caitlyn",
			is_stationary = true,
			can_be_exhausted = true,
			is_dangerous = true,
			IsValidCast = function(arg_38_0)
				return true
			end,
			IsEnabledToWork = function()
				return true
			end
		},
		EzrealR = {
			menu_name = "R",
			is_silenceable = false,
			channel_time = 1,
			can_be_displaced = false,
			can_mess_up_combo = true,
			has_buff = false,
			has_particle = false,
			slot = 3,
			owner_name = "Ezreal",
			is_stationary = true,
			can_be_exhausted = true,
			is_dangerous = false,
			IsValidCast = function(arg_40_0)
				return true
			end,
			IsEnabledToWork = function()
				return ove_0_51.charName == "Soraka" or ove_0_51.charName == "Malzahar"
			end
		},
		FiddleSticksW = {
			menu_name = "W",
			is_silenceable = true,
			channel_time = 2,
			can_be_displaced = true,
			can_mess_up_combo = false,
			has_buff = false,
			has_particle = false,
			slot = 1,
			owner_name = "FiddleSticks",
			is_stationary = true,
			can_be_exhausted = true,
			is_dangerous = false,
			IsValidCast = function(arg_42_0)
				return true
			end,
			IsEnabledToWork = function()
				return true
			end
		},
		FiddleSticksR = {
			menu_name = "R",
			is_silenceable = true,
			channel_time = 1.5,
			can_be_displaced = true,
			can_mess_up_combo = false,
			has_buff = false,
			has_particle = true,
			slot = 3,
			owner_name = "FiddleSticks",
			is_stationary = true,
			can_be_exhausted = true,
			is_dangerous = true,
			IsValidCast = function(arg_44_0)
				return true
			end,
			IsEnabledToWork = function()
				return true
			end
		},
		ReapTheWhirlwind = {
			menu_name = "R",
			is_silenceable = true,
			channel_time = 3,
			buffname = "reapthewhirlwind",
			can_be_displaced = true,
			can_mess_up_combo = false,
			has_buff = true,
			has_particle = false,
			slot = 3,
			owner_name = "Janna",
			is_stationary = true,
			can_be_exhausted = false,
			is_dangerous = true,
			IsValidCast = function(arg_46_0)
				return true
			end,
			IsEnabledToWork = function()
				return true
			end
		},
		TahmKenchW = {
			menu_name = "W",
			is_silenceable = true,
			channel_time = 1.35,
			can_be_displaced = true,
			can_mess_up_combo = false,
			has_buff = false,
			has_particle = true,
			slot = 1,
			owner_name = "TahmKench",
			is_stationary = true,
			can_be_exhausted = false,
			is_dangerous = false,
			IsValidCast = function(arg_48_0)
				return true
			end,
			IsEnabledToWork = function()
				return true
			end
		},
		KarthusFallenOne = {
			menu_name = "R",
			is_silenceable = true,
			channel_time = 3,
			buffname = "karthusfallenonecastsound",
			can_be_displaced = true,
			can_mess_up_combo = false,
			has_buff = true,
			has_particle = false,
			slot = 3,
			owner_name = "Karthus",
			is_stationary = true,
			can_be_exhausted = true,
			is_dangerous = true,
			IsValidCast = function(arg_50_0)
				return true
			end,
			IsEnabledToWork = function()
				return true
			end
		},
		KatarinaR = {
			menu_name = "R",
			is_silenceable = true,
			channel_time = 2.5,
			buffname = "katarinarsound",
			can_be_displaced = true,
			can_mess_up_combo = false,
			has_buff = true,
			has_particle = false,
			slot = 3,
			owner_name = "Katarina",
			is_stationary = true,
			can_be_exhausted = true,
			is_dangerous = true,
			IsValidCast = function(arg_52_0)
				return true
			end,
			IsEnabledToWork = function()
				return true
			end
		},
		LucianR = {
			menu_name = "R",
			is_silenceable = true,
			channel_time = 3,
			buffname = "lucianr",
			can_be_displaced = true,
			can_mess_up_combo = false,
			has_buff = true,
			has_particle = false,
			slot = 3,
			owner_name = "Lucian",
			is_stationary = false,
			can_be_exhausted = true,
			is_dangerous = true,
			IsValidCast = function(arg_54_0)
				return true
			end,
			IsEnabledToWork = function()
				return true
			end
		},
		SeraphineR = {
			menu_name = "R",
			is_silenceable = false,
			channel_time = 0.5,
			can_be_displaced = false,
			can_mess_up_combo = true,
			has_buff = false,
			has_particle = false,
			slot = 3,
			owner_name = "Seraphine",
			is_stationary = true,
			can_be_exhausted = true,
			is_dangerous = false,
			IsValidCast = function(arg_56_0)
				return true
			end,
			IsEnabledToWork = function()
				return ove_0_51.charName == "Soraka" or ove_0_51.charName == "Malzahar"
			end
		},
		LuxMaliceCannon = {
			menu_name = "R",
			is_silenceable = false,
			channel_time = 1,
			can_be_displaced = false,
			can_mess_up_combo = true,
			has_buff = false,
			has_particle = false,
			slot = 3,
			owner_name = "Lux",
			is_stationary = true,
			can_be_exhausted = true,
			is_dangerous = false,
			IsValidCast = function(arg_58_0)
				return true
			end,
			IsEnabledToWork = function()
				return ove_0_51.charName == "Soraka" or ove_0_51.charName == "Malzahar"
			end
		},
		MalzaharR = {
			menu_name = "R",
			is_silenceable = true,
			channel_time = 2.5,
			buffname = "malzaharrsound",
			can_be_displaced = true,
			can_mess_up_combo = false,
			has_buff = true,
			has_particle = false,
			slot = 3,
			owner_name = "Malzahar",
			is_stationary = true,
			can_be_exhausted = true,
			is_dangerous = true,
			IsValidCast = function(arg_60_0)
				return true
			end,
			IsEnabledToWork = function()
				return true
			end
		},
		Meditate = {
			menu_name = "W",
			is_silenceable = true,
			channel_time = 4,
			buffname = "meditate",
			can_be_displaced = true,
			can_mess_up_combo = false,
			has_buff = true,
			has_particle = false,
			slot = 1,
			owner_name = "MasterYi",
			is_stationary = true,
			can_be_exhausted = false,
			is_dangerous = false,
			IsValidCast = function(arg_62_0)
				return true
			end,
			IsEnabledToWork = function()
				return true
			end
		},
		MissFortuneBulletTime = {
			menu_name = "R",
			is_silenceable = true,
			channel_time = 3,
			buffname = "missfortunebulletsound",
			can_be_displaced = true,
			can_mess_up_combo = false,
			has_buff = false,
			has_particle = false,
			slot = 3,
			owner_name = "MissFortune",
			is_stationary = true,
			can_be_exhausted = true,
			is_dangerous = true,
			IsValidCast = function(arg_64_0)
				return true
			end,
			IsEnabledToWork = function()
				return true
			end
		},
		NunuW = {
			menu_name = "W",
			is_silenceable = true,
			channel_time = 10,
			buffname = "nunuw",
			can_be_displaced = true,
			can_mess_up_combo = false,
			has_buff = false,
			has_particle = false,
			slot = 1,
			owner_name = "Nunu",
			is_stationary = false,
			can_be_exhausted = true,
			is_dangerous = false,
			IsValidCast = function(arg_66_0)
				return true
			end,
			IsEnabledToWork = function()
				return true
			end
		},
		NunuR = {
			menu_name = "R",
			is_silenceable = true,
			channel_time = 3,
			can_be_displaced = true,
			can_mess_up_combo = false,
			has_buff = false,
			has_particle = true,
			slot = 3,
			owner_name = "Nunu",
			is_stationary = true,
			can_be_exhausted = true,
			is_dangerous = true,
			IsValidCast = function(arg_68_0)
				return true
			end,
			IsEnabledToWork = function()
				return true
			end
		},
		PantheonQ = {
			menu_name = "Q",
			is_silenceable = true,
			channel_time = 4,
			buffname = "pantheonq",
			can_be_displaced = true,
			can_mess_up_combo = false,
			has_buff = true,
			has_particle = false,
			slot = 0,
			owner_name = "Pantheon",
			is_stationary = false,
			can_be_exhausted = true,
			is_dangerous = false,
			IsValidCast = function(arg_70_0)
				return true
			end,
			IsEnabledToWork = function()
				return true
			end
		},
		PantheonR = {
			menu_name = "R",
			is_silenceable = true,
			channel_time = 2,
			buffname = "pantheonr",
			can_be_displaced = true,
			can_mess_up_combo = false,
			has_buff = true,
			has_particle = false,
			slot = 3,
			owner_name = "Pantheon",
			is_stationary = true,
			can_be_exhausted = false,
			is_dangerous = true,
			IsValidCast = function(arg_72_0)
				return true
			end,
			IsEnabledToWork = function()
				return true
			end
		},
		ShenR = {
			menu_name = "R",
			is_silenceable = true,
			channel_time = 3,
			buffname = "shenrchannelmanager",
			can_be_displaced = true,
			can_mess_up_combo = false,
			has_buff = true,
			has_particle = false,
			slot = 3,
			owner_name = "Shen",
			is_stationary = true,
			can_be_exhausted = false,
			is_dangerous = false,
			IsValidCast = function(arg_74_0)
				return true
			end,
			IsEnabledToWork = function()
				return true
			end
		},
		Gate = {
			menu_name = "R",
			is_silenceable = true,
			channel_time = 1.5,
			buffname = "gate",
			can_be_displaced = true,
			can_mess_up_combo = false,
			has_buff = true,
			has_particle = false,
			slot = 3,
			owner_name = "TwistedFate",
			is_stationary = true,
			can_be_exhausted = true,
			is_dangerous = true,
			IsValidCast = function(arg_76_0)
				return true
			end,
			IsEnabledToWork = function()
				return true
			end
		},
		VarusQ = {
			menu_name = "Q",
			is_silenceable = true,
			channel_time = 4,
			buffname = "varusq",
			can_be_displaced = true,
			can_mess_up_combo = false,
			has_buff = true,
			has_particle = false,
			slot = 0,
			owner_name = "Varus",
			is_stationary = false,
			can_be_exhausted = true,
			is_dangerous = false,
			IsValidCast = function(arg_78_0)
				return true
			end,
			IsEnabledToWork = function()
				return true
			end
		},
		WarwickRChannel = {
			menu_name = "R",
			is_silenceable = true,
			channel_time = 1.5,
			buffname = "warwickr",
			can_be_displaced = true,
			can_mess_up_combo = false,
			has_buff = true,
			has_particle = false,
			slot = 3,
			owner_name = "Warwick",
			is_stationary = true,
			can_be_exhausted = true,
			is_dangerous = true,
			IsValidCast = function(arg_80_0)
				return true
			end,
			IsEnabledToWork = function()
				return true
			end
		},
		XerathArcanopulseChargeUp = {
			menu_name = "Q",
			is_silenceable = true,
			channel_time = 3,
			buffname = "xeratharcanopulsechargeup",
			can_be_displaced = true,
			can_mess_up_combo = false,
			has_buff = true,
			has_particle = false,
			slot = 0,
			owner_name = "Xerath",
			is_stationary = false,
			can_be_exhausted = true,
			is_dangerous = false,
			IsValidCast = function(arg_82_0)
				return true
			end,
			IsEnabledToWork = function()
				return true
			end
		},
		XerathLocusOfPower2 = {
			menu_name = "R",
			is_silenceable = true,
			channel_time = 10,
			buffname = "xerathrshots",
			can_be_displaced = true,
			can_mess_up_combo = false,
			has_buff = true,
			has_particle = false,
			slot = 3,
			owner_name = "Xerath",
			is_stationary = true,
			can_be_exhausted = true,
			is_dangerous = true,
			IsValidCast = function(arg_84_0)
				return true
			end,
			IsEnabledToWork = function()
				return true
			end
		},
		ZacE = {
			menu_name = "E",
			is_silenceable = true,
			channel_time = 4.5,
			buffname = "zace",
			can_be_displaced = true,
			can_mess_up_combo = false,
			has_buff = true,
			has_particle = false,
			slot = 2,
			owner_name = "Zac",
			is_stationary = true,
			can_be_exhausted = true,
			is_dangerous = false,
			IsValidCast = function(arg_86_0)
				return true
			end,
			IsEnabledToWork = function()
				return true
			end
		},
		PowerBall = {
			menu_name = "Q",
			is_silenceable = true,
			channel_time = 6,
			buffname = "powerball",
			can_be_displaced = true,
			can_mess_up_combo = false,
			has_buff = true,
			has_particle = false,
			slot = 0,
			owner_name = "Rammus",
			is_stationary = false,
			can_be_exhausted = true,
			is_dangerous = false,
			IsValidCast = function(arg_88_0)
				return true
			end,
			IsEnabledToWork = function()
				return true
			end
		},
		SionQ = {
			menu_name = "Q",
			is_silenceable = true,
			channel_time = 2,
			buffname = "sionq",
			can_be_displaced = true,
			can_mess_up_combo = false,
			has_buff = true,
			has_particle = false,
			slot = 0,
			owner_name = "Sion",
			is_stationary = true,
			can_be_exhausted = true,
			is_dangerous = false,
			IsValidCast = function(arg_90_0)
				return true
			end,
			IsEnabledToWork = function()
				return true
			end
		},
		PykeQ = {
			menu_name = "Q",
			is_silenceable = true,
			channel_time = 3,
			buffname = "pykeq",
			can_be_displaced = true,
			can_mess_up_combo = false,
			has_buff = true,
			has_particle = false,
			slot = 0,
			owner_name = "Pyke",
			is_stationary = false,
			can_be_exhausted = true,
			is_dangerous = false,
			IsValidCast = function(arg_92_0)
				return true
			end,
			IsEnabledToWork = function()
				return true
			end
		},
		ViQ = {
			menu_name = "Q",
			is_silenceable = true,
			channel_time = 4,
			buffname = "viq",
			can_be_displaced = true,
			can_mess_up_combo = false,
			has_buff = true,
			has_particle = false,
			slot = 0,
			owner_name = "Vi",
			is_stationary = false,
			can_be_exhausted = true,
			is_dangerous = false,
			IsValidCast = function(arg_94_0)
				return true
			end,
			IsEnabledToWork = function()
				return true
			end
		},
		ViegoW = {
			menu_name = "W",
			is_silenceable = true,
			channel_time = 3,
			buffname = "viegow",
			can_be_displaced = true,
			can_mess_up_combo = false,
			has_buff = true,
			has_particle = false,
			slot = 1,
			owner_name = "Viego",
			is_stationary = false,
			can_be_exhausted = true,
			is_dangerous = false,
			IsValidCast = function(arg_96_0)
				return true
			end,
			IsEnabledToWork = function()
				return true
			end
		},
		YuumiWCast = {
			menu_name = "W",
			is_silenceable = true,
			channel_time = 0.25,
			can_be_displaced = true,
			can_mess_up_combo = false,
			has_buff = false,
			has_particle = false,
			slot = 45,
			owner_name = "Yuumi",
			is_stationary = true,
			can_be_exhausted = false,
			is_dangerous = false,
			IsValidCast = function(arg_98_0)
				return true
			end,
			IsEnabledToWork = function()
				return true
			end
		},
		JhinR = {
			menu_name = "R",
			is_silenceable = true,
			channel_time = 10,
			can_be_displaced = true,
			can_mess_up_combo = false,
			has_buff = false,
			has_particle = true,
			slot = 3,
			owner_name = "Jhin",
			is_stationary = true,
			can_be_exhausted = true,
			is_dangerous = true,
			IsValidCast = function(arg_100_0)
				return true
			end,
			IsEnabledToWork = function()
				return true
			end
		},
		VelkozR = {
			menu_name = "R",
			is_silenceable = true,
			channel_time = 2.6,
			buffname = "velkozr",
			can_be_displaced = true,
			can_mess_up_combo = false,
			has_buff = true,
			has_particle = false,
			slot = 3,
			owner_name = "Velkoz",
			is_stationary = true,
			can_be_exhausted = true,
			is_dangerous = true,
			IsValidCast = function(arg_102_0)
				return true
			end,
			IsEnabledToWork = function()
				return true
			end
		},
		SamiraR = {
			menu_name = "R",
			is_silenceable = true,
			channel_time = 2.277,
			can_be_displaced = true,
			can_mess_up_combo = false,
			has_buff = true,
			has_particle = false,
			slot = 3,
			owner_name = "Samira",
			is_stationary = false,
			can_be_exhausted = true,
			is_dangerous = true,
			IsValidCast = function(arg_104_0)
				return true
			end,
			IsEnabledToWork = function()
				return true
			end
		},
		BelvethE = {
			menu_name = "E",
			is_silenceable = true,
			channel_time = 2,
			buffname = "belvethe",
			can_be_displaced = true,
			can_mess_up_combo = false,
			has_buff = true,
			has_particle = false,
			slot = 2,
			owner_name = "Belveth",
			is_stationary = false,
			can_be_exhausted = true,
			is_dangerous = true,
			IsValidCast = function(arg_106_0)
				return true
			end,
			IsEnabledToWork = function()
				return true
			end
		},
		AurelionSolQ = {
			menu_name = "E",
			is_silenceable = true,
			channel_time = 2,
			buffname = "aurelionsolq",
			can_be_displaced = true,
			can_mess_up_combo = false,
			has_buff = true,
			has_particle = false,
			slot = 0,
			owner_name = "AurelionSol",
			is_stationary = false,
			can_be_exhausted = true,
			is_dangerous = true,
			IsValidCast = function(arg_108_0)
				return true
			end,
			IsEnabledToWork = function()
				return true
			end
		},
		AurelionSolW = {
			menu_name = "W",
			is_silenceable = true,
			channel_time = 2,
			buffname = "aurelionsolw",
			can_be_displaced = true,
			can_mess_up_combo = false,
			has_buff = true,
			has_particle = false,
			slot = 1,
			owner_name = "AurelionSol",
			is_stationary = false,
			can_be_exhausted = true,
			is_dangerous = true,
			IsValidCast = function(arg_110_0)
				return true
			end,
			IsEnabledToWork = function()
				return true
			end
		}
	}

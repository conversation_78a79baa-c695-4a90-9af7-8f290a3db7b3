
local ove_0_10 = module.load("Kloader", "<PERSON>b/MyC<PERSON>mon")
local ove_0_11 = module.load("Kloader", "Lib/Version")
local ove_0_12 = module.load("Kloader", "Lib/myMsg")
local ove_0_13 = module.load("Kloader", "Lib/Changelog")

if ove_0_11.GameVerSub ~= ove_0_11.GameVer then
	ove_0_10.myPrint("script outdated, please wait new version.")

	return
end

local ove_0_14 = module.load("Kloader", "Lib/MyAuth")
local ove_0_15 = {
	"Cassiope<PERSON>",
	"<PERSON>",
	"<PERSON><PERSON><PERSON>",
	"<PERSON><PERSON>",
	"<PERSON>",
	"<PERSON><PERSON><PERSON>",
	"<PERSON><PERSON><PERSON>",
	"<PERSON><PERSON><PERSON>",
	"<PERSON><PERSON>",
	"<PERSON><PERSON>",
	"<PERSON><PERSON>",
	"<PERSON><PERSON><PERSON>",
	"Tristan<PERSON>",
	"<PERSON>",
	"T<PERSON>",
	"<PERSON><PERSON><PERSON>",
	"<PERSON><PERSON>",
	"<PERSON><PERSON><PERSON>",
	"<PERSON><PERSON>",
	"KogMaw"
}
local ove_0_16 = false

if not ove_0_14.Need() then
	return
end

for iter_0_0, iter_0_1 in pairs(ove_0_15) do
	if player.charName == iter_0_1 then
		ove_0_16 = true
	end
end

local function ove_0_17()
	-- print 5
	if ove_0_16 then
		if ove_0_14.isAuthed() == true then
			module.load("Kloader", "Champions/" .. player.charName .. "/" .. player.charName)
			ove_0_13.Get(true)
			ove_0_13.Get2(true)

			if ove_0_10.GetLanguage() == 1 then
				ove_0_14.Print("success", true)
				print("**********************************************************************************")
			elseif ove_0_10.GetLanguage() == 2 then
				ove_0_14.Print("success", true)
				print("**********************************************************************************")
			end

			cb.remove(ove_0_17)
		elseif ove_0_14.isAuthed() == false then
			if ove_0_10.GetLanguage() == 1 then
				ove_0_13.Get(false)
				ove_0_13.Get2(false)
				ove_0_14.Print("fail", true)
				print("**********************************************************************************")
			elseif ove_0_10.GetLanguage() == 2 then
				ove_0_14.Print("fail", true)
				print("**********************************************************************************")
			end

			cb.remove(ove_0_17)
		end
	end
end

if not ove_0_16 then
	print("This champion is not supported in this bundle yet.")
end

cb.add(cb.tick, ove_0_17)

return {}

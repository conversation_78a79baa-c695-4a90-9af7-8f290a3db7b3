
local ove_0_10 = module.internal("pred")
local ove_0_11 = module.internal("TS")
local ove_0_12 = module.load(header.id, "common/common")
local ove_0_13 = module.load(header.id, "plugins/Swain/damage")
local ove_0_14 = {
	last = 0,
	range = 600,
	slot = player:spellSlot(_R),
	predinput = {
		radius = 600,
		dashRadius = 0,
		boundingRadiusModTarget = 1,
		delay = 0.25,
		boundingRadiusModSource = 0
	}
}

function ove_0_14.is_ready()
	if ove_0_14.slot.name == "SwainRSoulFlare" then
		return ove_0_14.slot.state == 0
	end
end

function ove_0_14.invoke_r_killsteal()
	local slot_6_0 = ove_0_11.get_result(function(arg_7_0, arg_7_1, arg_7_2)
		if arg_7_2 > 650 then
			return
		end

		if not ove_0_12.isValidTarget(arg_7_1) then
			return
		end

		local slot_7_0 = ove_0_10.present.get_prediction(ove_0_14.predinput, player, arg_7_1.path.serverPos2D)

		if not slot_7_0 then
			return
		end

		if slot_7_0:distSqr(player.pos2D) < 390625 and ove_0_13.get_r_damage(arg_7_1) > ove_0_12.GetShieldedHealth("AP", arg_7_1) then
			arg_7_0.obj = arg_7_1

			return true
		end
	end)

	if slot_6_0.obj and ove_0_12.isValidTarget(slot_6_0.obj) and ove_0_12.IsEnemyMortal(slot_6_0.obj) then
		player:castSpell("self", _R)

		return true
	end
end

return ove_0_14

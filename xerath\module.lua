if Game.HashStringSDBM("Xerath") ~= Game.localPlayer.hash then
    return
end

Champions.CppScriptMaster(false);

menu = Environment.LoadModule("menu");
local logic = Environment.LoadModule("logic");

local function init()

    Champions.Q = (SDKSpell.Create(SpellSlot.Q, 1450, DamageType.Magical));
    Champions.W = (SDKSpell.Create(SpellSlot.W, 1000, DamageType.Magical));
    Champions.E = (SDKSpell.Create(SpellSlot.E, 1125, DamageType.Magical));
    Champions.R = (SDKSpell.Create(SpellSlot.R, 5500, DamageType.Magical));

    Champions.Q:SetSkillshot(0.50, 70.0, math.flt_max, SkillshotType.SkillshotLine, true, bit.bor(CollisionFlag.CollidesWithNothing), HitChance.Low, true);
    Champions.W:SetSkillshot(0.8, 75, math.flt_max, SkillshotType.SkillshotCircle, true, bit.bor(CollisionFlag.CollidesWithNothing), HitChance.Low, false);
    Champions.E:SetSkillshot(0.25, 60, 1400, SkillshotType.SkillshotLine, true, bit.bor(CollisionFlag.CollidesWithMinions, CollisionFlag.CollidesWithHeroes, CollisionFlag.CollidesWithYasuoWall), HitChance.Low, false);
    Champions.R:SetSkillshot(0.6 + 0.0000000238416, 165, math.flt_max, SkillshotType.SkillshotCircle, true, bit.bor(CollisionFlag.CollidesWithNothing), HitChance.Low, false);

    --Q internal 0.5, 72.5
    --W 0.7933, 72.5,
    --R correct value: 0.6 + 0.033, 200
    --R old internal value: 0.6 + 0.0000000238416, 130
    Champions.Q:SetCharged(("XerathArcanopulseChargeUp"), ("XerathArcanopulseChargeUp"), 750, 1450, 1.5 + 0.033);

    menu = menu();
    logic();
end

init();

Callback.Bind(CallbackType.OnUnload, function()
    Champions.Clean();
    Evade.SetCustomShouldSpellBeIgnored(nil);
    return CallbackResult.Dispose;
end)

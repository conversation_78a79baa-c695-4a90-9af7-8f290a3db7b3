local LeeSinPlugin = {}

local ui = module.load("<PERSON>", "ui");
local ts = module.internal("TS")
local orb = module.internal("orb")
local preds = module.internal("pred")
local DelayAction = module.load("<PERSON>", "Core/DelayAction")
local DelayTick = module.load("<PERSON>", "Core/DelayTick")
local Prediction = module.load("<PERSON>", "Core/Prediction")
local BuffManager = module.load("<PERSON>", "Library/BuffManager")
local CalculateManager = module.load("<PERSON>", "Library/CalculateManager")
--local FarmManager = module.load("<PERSON>", "Library/FarmManager")
local ItemManager = module.load("<PERSON>", "Library/ItemManager")
local NetManager = module.load("<PERSON>", "Library/NetManager")
local ObjectManager = module.load("Brian", "Library/ObjectManager")
local OrbManager = module.load("<PERSON>", "Library/OrbManager")
local SpellManager = module.load("<PERSON>", "Library/SpellManager")
local VectorManager = module.load("<PERSON>", "Library/VectorManager")
local MyCommon = module.load("<PERSON>", "Library/ExtraManager")
local common = module.load("Brian", "Utility/common")
	
	


local qPred = {
	delay = 0.25,
	width = 58,
	speed = 1750,   --1500
	boundingRadiusMod = 1,
	collision = {hero = false, minion = true, wall = true}
}

local allies = common.GetEnemyHeroes()
local insecPos=nil
local AttackPassive=0
local insecclickpos=nil
local originalpos=nil
local LastQ=0
local LastInsec=0
local Qtarget=nil
local Smite=nil
local Flash=nil
local WardTime=0
local SmiteDamage={[1]=390,[2]=410,[3]=430,[4]=450,[5]=480,[6]=510,[7]=540,[8]=570,[9]=600,[10]=640,[11]=680,[12]=720,[13]=760,[14]=800,[15]=850,[16]=900,[17]=950,[18]=1000,[19]=1050,[20]=1100,[21]=1150,[22]=1100,[23]=1250,[24]=1300,[25]=1350,[26]=1400,[27]=1450,[28]=1500,[29]=1550,[30]=1600}

for i = 4 , 5 do 
	if player:spellSlot(i).name:lower():find("summonersmite") then
		Smite=i
	end
	if player:spellSlot(i).name:lower():find("summonerflash") then
		Flash=i
	end
end

local interruptableSpells = {
	["anivia"] = {{menuslot = "R", slot = 3, spellname = "glacialstorm", channelduration = 6}},
	["caitlyn"] = {{menuslot = "R", slot = 3, spellname = "caitlynaceinthehole", channelduration = 1}},
	["ezreal"] = {{menuslot = "R", slot = 3, spellname = "ezrealtrueshotbarrage", channelduration = 1}},
	["fiddlesticks"] = {{menuslot = "W", slot = 1, spellname = "drain", channelduration = 5},{menuslot = "R", slot = 3, spellname = "crowstorm", channelduration = 1.5}},
	["gragas"] = {{menuslot = "W", slot = 1, spellname = "gragasw", channelduration = 0.75}},
	["janna"] = {{menuslot = "R", slot = 3, spellname = "reapthewhirlwind", channelduration = 3}},
	["karthus"] = {{menuslot = "R", slot = 3, spellname = "karthusfallenone", channelduration = 3}}, --common.IsValidTargetTarget will prevent from casting @ karthus while he's zombie
	["katarina"] = {{menuslot = "R", slot = 3, spellname = "katarinar", channelduration = 2.5}},
	["lucian"] = {{menuslot = "R", slot = 3, spellname = "lucianr", channelduration = 2}},
	["lux"] = {{menuslot = "R", slot = 3, spellname = "luxmalicecannon", channelduration = 0.5}},
	["malzahar"] = {{menuslot = "R", slot = 3, spellname = "malzaharr", channelduration = 2.5}},
	["masteryi"] = {{menuslot = "W", slot = 1, spellname = "meditate", channelduration = 4}},
	["missfortune"] = {{menuslot = "R", slot = 3, spellname = "missfortunebullettime", channelduration = 3}},
	["nunu"] = {{menuslot = "R", slot = 3, spellname = "absolutezero", channelduration = 3}},
	["pantheon"] = {{menuslot = "R", slot = 3, spellname = "pantheonrjump", channelduration = 2}},
	["shen"] = {{menuslot = "R", slot = 3, spellname = "shenr", channelduration = 3}},
	["twistedfate"] = {{menuslot = "R", slot = 3, spellname = "gate", channelduration = 1.5}},
	["varus"] = {{menuslot = "Q", slot = 0, spellname = "varusq", channelduration = 4}},
	["warwick"] = {{menuslot = "R", slot = 3, spellname = "warwickr", channelduration = 1.5}},
	["xerath"] = {{menuslot = "R", slot = 3, spellname = "xerathlocusofpower2", channelduration = 3}},
}

local MyMenu

function LeeSinPlugin.Load(GlobalMenu)
    MyMenu = GlobalMenu


    MyMenu.Key:keybind("insec", "Insec", "T", nil)
    MyMenu.Key:keybind("wardjump", "Ward Jump", "Z", nil)
    MyMenu.Key:keybind("insecs", "Set Insec Pos", "L", nil)
    MyMenu.Key:keybind("semiinsec", "Semi Insec (Uses Flash)", "A", nil)

    MyMenu:menu("combo", "Combo Settings")
    MyMenu.combo:header("xd", "Q Settings")
    MyMenu.combo:boolean("q", "Use Q", true)
    MyMenu.combo:boolean('notq' , 'Avoid Q2 Turret Dive', true)

    MyMenu.combo:header("xd", "W Settings")
    MyMenu.combo:boolean("w", "Use W", true)

    MyMenu.combo:header("xd", "W Settings")
    MyMenu.combo:boolean("e", "Use E", true)

    MyMenu.combo:header("xd", "R Settings")
    MyMenu.combo:boolean("r", "Use R", true)
    MyMenu.combo:boolean('rkick' , 'Use R for X enemys', true)
    MyMenu.combo:slider('rkickhit', 'R kick Hit :', 2 , 1 , 4 , 1 )

    MyMenu:menu("harass", "Harass Settings")
    MyMenu.harass:header("xd", "Q Settings")
    MyMenu.harass:boolean("q", "Use Q", true)
    MyMenu.harass:header("xd", "E Settings")
    MyMenu.harass:boolean("e", "Use E", true)

    MyMenu:menu("lc", "Lane Clear Settings")
    MyMenu.lc:header("xd", "Lane Clear Settings")
    MyMenu.lc:boolean("q", "Use Q", true)
    MyMenu.lc:boolean("e", "Use E", true)

    MyMenu:menu("jg", "Jungle Clear Settings")
    MyMenu.jg:boolean("q", "Use Q", true)
    MyMenu.jg:boolean("w", "Use W", true)
    MyMenu.jg:boolean("e", "Use E", true)

    MyMenu:menu("ks", "Killsteal Settings")
    MyMenu.ks:header("xd", "KillSteal Settings")
    MyMenu.ks:boolean("enable", "Use Killsteal", true)
    MyMenu.ks:boolean("q1", "Use Q on Killsteal", true)
    MyMenu.ks:boolean('q2', 'Use Q2 on Killsteal', false)
    MyMenu.ks:boolean('q3', 'Use Q+R', false)
    MyMenu.ks:boolean("e", "Use E on Killsteal", true)
    MyMenu.ks:boolean('r', 'Use R on Killsteal', true)

    MyMenu:menu('insecset' ,  'Insec Settings')
    MyMenu.insecset:boolean('flash' , 'Insec Use Flash', true)
    MyMenu.insecset:boolean('waitq' , 'Wait for Q Dash', false)
    MyMenu.insecset:dropdown("mode", "Insec Mode: ", 2, {"Mouse Pos", "Allys & Towers", "Click Pos"})
    MyMenu.insecset:header("modeheader", "Insec: it will focus tower/alliesif no allies are detected")
    MyMenu.insecset:header("modeheader2", "it will use it based on your mouse position")

    MyMenu:menu("draws", "Draw Settings")
    MyMenu.draws:header("xd", "Drawing Options")
    MyMenu.draws:boolean("q", "Draw Q Range", true)
    MyMenu.draws:color("colorq", "^ color", 255, 233, 121, 121)
    MyMenu.draws:boolean("r", "Draw R Rnage", true)
    MyMenu.draws:color("colorr", "^ color", 255, 233, 121, 121)
    MyMenu.draws:boolean("Insec", "Draw Insec", true)

end
local function WardName(unit)
	local Cmp = {"ward","trink","trap","spear","device", "room", "box", "plant","poo","barrel"}
	for i = 1, #Cmp do
		if unit and unit.name:lower():find(Cmp[i]) then
		return true
		end
	end
end
local function FirstSpell(x)
	if player:spellSlot(x).name:find("One") then
	return true
	else
	return false
	end
end

local function AADmg(target)
	local ad = player.baseAttackDamage+player.flatPhysicalDamageMod
	local Defense = 100/(100+target.armor)
	local damage = (ad * Defense) 	
	return damage 	
end

local function Q1Dmg(target)
	if player:spellSlot(0).level == 0 then return 0 end  
	local multi = {55, 85, 115, 145, 175};
	local Qlv=player:spellSlot(0).level
	local ad = player.flatPhysicalDamageMod
	local Defense = 100/(100+target.armor)
	local damage = ((multi[Qlv]+ad*0.9) * Defense) 
	return damage 
end

local function Q2Dmg(target, predlife)
	if player:spellSlot(0).level == 0 then return 0 end  
	local multi = {55, 85, 115, 145, 175};
	local Qlv=player:spellSlot(0).level
	local ad = player.flatPhysicalDamageMod
	local Defense = 100/(100+target.armor)
	local damage = ((multi[Qlv]+((target.maxHealth - predlife) * 0.08)+ad*0.9) * Defense) 
	return damage 
end

local function Q3Dmg(target)
	local q1 = QDmg(target)
	local q2 = Q2Dmg(target, target.health - q1)
	return q1 + q2
end

local function EDmg(target)
	if player:spellSlot(2).level == 0 then return 0 end  
	local multi = {70, 105, 140, 175, 210}
	local Elv =player:spellSlot(2).level
	local ap = player.flatMagicDamageMod
	local Defense = 100/(100+target.spellBlock)
	local damage = ((multi[Elv]+ap)* Defense) 	
	return damage	
end

local function RDmg(target)
	if player:spellSlot(3).level == 0 then return 0 end  
	local multi = {150,300,450}
	local Rlv=player:spellSlot(3).level
	local ad = player.flatPhysicalDamageMod
	local Defense = 100/(100+target.armor)
	local damage = ((multi[Rlv]+ad*2)* Defense) 
	return damage
end

local function R2Dmg(target,source)
	if player:spellSlot(3).level == 0 then return 0 end  
	local multi={0.12,0.15,0.18}
	local Rlv=player:spellSlot(3).level
	local ad = player.flatPhysicalDamageMod
	local Defense = 100/(100+target.armor)
	local damage = (Cmp.champSpecificHealth*multi[Rlv] * Defense) 
	return damage
end

local function WardSlot()
	local Item = {["ItemGhostWard"]=true,["ItemGhostWard"]=true,["TrinketTotemLvl1"]=true,["VisionWard"]=true, ["TrinketTotemLvl4"]=true,["TrinketTotemLvl3"]=true,["JammerDevice"]=true}
	for SLOT = 6, 12 do
		local item = player:spellSlot(SLOT)
		if item.isNotEmpty and item.state==0 and Item[item.name] then
			return SLOT
		end
	end
	return nil
end

local function Allyminions(Pos)
	local minions = objManager.minions
	for i = 0, minions.size[TEAM_ALLY] - 1 do
		local obj=minions[TEAM_ALLY][i]
		if obj and not obj.isDead and obj.isVisible and obj.isTargetableToTeamFlags and obj.pos:distSqr(Pos) < 700^2 and obj.pos:distSqr(game.mousePos)<=200^2 and not WardName(obj) then
			return obj
		end
	end
end

local function AllyNear(Source,Range)
	for i = 0 , objManager.allies_n - 1 do 
		local obj = objManager.allies[i]
		if obj and obj~=player and not obj.isDead and obj.isVisible and obj.isTargetableToTeamFlags and obj.pos:distSqr(Source)<=Range^2 then 
			return obj.pos
		end
	end
end

local function EnemiesNear(Source,Range)
	for i = 0 , objManager.enemies_n - 1 do 
		local obj = objManager.enemies[i]
		if obj and obj~=Source and not obj.isDead and obj.isVisible and obj.isTargetableToTeamFlags and obj.pos:distSqr(Source)<=Range^2 and obj.team ~= player.team then 
			return obj
		end
	end
end

local function findTower(Source,Range)
	for i = 0, objManager.turrets.size[TEAM_ALLY]-1 do
		local obj = objManager.turrets[TEAM_ALLY][i]
		if obj and obj.team == player.team and obj.health and obj.health>0 and obj.pos:distSqr(Source)<=Range^2 then 
			return obj.pos
		end
	end
end

local function UnderTurret(unit)
	if not unit or unit.isDead or not unit.isVisible or not unit.isTargetable then return true end
	for i = 0, objManager.turrets.size[TEAM_ENEMY]-1 do
		local obj= objManager.turrets[TEAM_ENEMY][i]
		if obj and obj.health and obj.health>0 and obj.pos:distSqr(unit.pos)<=900^2 then
		return true
		end
	end
	return false
end

local function CountEnemiesNear(source,range)
	local count=0
	for i = 0 , objManager.enemies_n - 1 do
		local obj = objManager.enemies[i]
		if obj and not obj.isDead and obj.isTargetable and obj.isVisible and obj.team ~= player.team and obj.pos:distSqr(source)<range^2 then
		count=count+1
		end
	end
	return count
end

local function CountEnemiesInR(Radius, startPos, endPos, Cmp,Hit)
	local input = {
		  delay = 0.25,
		  speed = 1500,
		  width = Radius,
		  boundingRadiusMod = 1,
		  collision = {
			hero = true,
			minion = false,
		  },
	}
	local seg={}
	
	seg.startPos=startPos.path.serverPos2D
	seg.endPos=vec2(endPos.x,endPos.z)
	if seg.startPos and seg.endPos then
	local res=preds.collision.get_prediction(input, seg ,Cmp)
	
	if res then
		if #res>= Hit then 
			player:castSpell('obj', 3, Cmp)
			return
		end
	end
  end
	return false
end

local function InsecJump(Pos)
	if not MyMenu.Key.Combo:get() and not MyMenu.Key.Harass:get() and not MyMenu.Key.insec:get() and not MyMenu.Key.LaneClear:get() then
		player:move(Pos)
	end
	if player:spellSlot(1).state ~=0 or not FirstSpell(1) then return false end
	local ward = WardSlot()
	local ally = AllyNear(player, 700)
	
	local wardpos = Pos 
	if player.path.serverPos:distSqr(wardpos) > 600^2 then
		wardpos = player.path.serverPos + (595 / player.path.serverPos:dist(wardpos)) * (wardpos - player.path.serverPos)
	end
	if game.time > WardTime+1 and ward then
		player:castSpell('pos', ward, vec3(wardpos.x, wardpos.y, wardpos.z))
		WardTime = game.time
		player:castSpell('pos', 1, vec3(wardpos.x, wardpos.y, wardpos.z))
		return true
		elseif game.time>WardTime+1 then
			for i = 0, objManager.allies_n - 1 do
				local ally = objManager.allies[i]
				if ally and ally ~= player and common.IsValidTarget(ally) and ally.pos:distSqr(wardpos)<=200^2 then
					player:castSpell('obj', 1, ally)
				end
			end
			return true
		elseif game.time > WardTime+1 and Allyminions(wardpos) then
		player:castSpell('obj', 1, Allyminions(wardpos))
		return true
	end
end


local function WardJump(Pos)
	if not MyMenu.Key.Combo:get() and not MyMenu.Key.Harass:get() and not MyMenu.Key.insec:get() and not MyMenu.Key.LaneClear:get() then
		player:move(Pos)
	end
	if player:spellSlot(1).state~=0 or not FirstSpell(1) then return false end
	local ward=WardSlot()
	local ally = AllyNear(player,700)
	local wardpos=Pos 
	if player.path.serverPos:distSqr(wardpos)>600^2 then
		wardpos=player.path.serverPos + (595 / player.path.serverPos:dist(wardpos)) * (wardpos - player.path.serverPos)
	end
	if game.time>WardTime+1 and ward and not navmesh.isWall(vec3(wardpos.x,wardpos.y,wardpos.z)) then
		player:castSpell("pos", ward, vec3(wardpos.x, wardpos.y, wardpos.z))
		WardTime=game.time
		player:castSpell('pos', 1, vec3(wardpos.x, wardpos.y, wardpos.z))
		return true
		elseif game.time>WardTime+1 then
			for i = 0, objManager.allies_n - 1 do
				local ally = objManager.allies[i]
				if ally and ally ~= player and common.IsValidTarget(ally) and ally.pos:distSqr(wardpos)<=200^2 then
					player:castSpell('obj', 1, ally)
				end
			end
			return true
		elseif game.time>WardTime+1 and Allyminions(wardpos) then
			player:castSpell('obj', 1, Allyminions(wardpos))
			return true
	end
end

local function keydown(k)
    if k == 84 then
        originalpos = game.mousePos
    elseif k == 76 then
        local clickpos = vec3.clone(mousePos)
        if insecclickpos and insecclickpos:distSqr(clickpos)<=100^2 then
            insecclickpos = nil
        else
            insecclickpos = clickpos
        end
    end
end

local function keyup(k)
	if k == 84 then
		originalpos=nil
	end
	if k == 76 then
		--insecclickpos = game.mousePos
	end
end

local function f(res, obj, dist)
	if player:spellSlot(0).state == 0 and FirstSpell(0) then
	local seg = preds.linear.get_prediction(qPred, obj)
		if seg and seg.startPos:distSqr(seg.endPos) <= 1100^2 then
			res.obj = obj
			return true
		end
	elseif player:spellSlot(0).state == 0 and not FirstSpell(0) and obj.buff['blindmonkqone'] and dist and dist<=1300 then
		res.obj = obj
		return true
	elseif MyMenu.combo.w:get() and player:spellSlot(1).state==0 and FirstSpell(1) and dist and dist<=600+player.attackRange+player.boundingRadius and obj.health<=AADmg(obj)*3+EDmg(obj) then
		res.obj = obj
		return true
	elseif player:spellSlot(3).state == 0 and dist and dist<=(375+obj.boundingRadius) then
		res.obj = obj
		return true
	elseif player:spellSlot(2).state == 0 and dist and dist<=425 then
		res.obj = obj
		return true
	end
end

local GetTarget=function()
	return orb.ts.get_result(f).obj
end


local function SexyPrint(message)
   local sexyName = "<font color=\"#E41B17\">[<b>¤ Cyrex ¤</b>]:</font>"
   local fontColor = "FFFFFF"
   chat.print(sexyName .. " <font color=\"#" .. fontColor .. "\">" .. message .. "</font>")
end

local function oncreateobj(obj)
	if (obj.name:lower():find("ward") and obj.isTargetableToTeamFlags and obj.isTargetable) then
		if MyMenu.Key.wardjump:get() and player:spellSlot(1).state == 0 and FirstSpell(1) then
			player:castSpell('obj', 1, obj)
		elseif MyMenu.Key.insec:get() and player:spellSlot(1).state == 0 and FirstSpell(1) then
			player:castSpell('obj', 1, obj)
		end
	end	
end

local function HasSionBuff(e)
	for i = 0, e.buffManager.count - 1 do
		local buff = e.buffManager:get(i)
		if buff and buff.valid and buff.name == 'sionpassivezombie' then
			return true
		end
	end
end

local function count_minions_in_range(pos, range)
	local enemies_in_range = {}
	for i = 0, objManager.minions.size[TEAM_ENEMY] - 1 do
		local enemy = objManager.minions[TEAM_ENEMY][i]
		if pos:dist(enemy.pos) < range and common.IsValidTarget(enemy) then
			enemies_in_range[#enemies_in_range + 1] = enemy
		end
	end
	return enemies_in_range
end


local function getinsecpos(unit)
	if unit.isDead or not unit.isTargetable or unit.buff[17] or unit.buff['fioraw'] or not unit.isVisible then return false end
	local TPOS = unit.path.serverPos
	if insecclickpos and unit.pos:distSqr(insecclickpos) <= 2000^2 then
		return insecclickpos + (TPOS-insecclickpos):norm()*(TPOS:dist(insecclickpos)+250),insecclickpos
	elseif findTower(unit,1500) then
		local Pos = findTower(unit,1500)
		return Pos + (TPOS-Pos):norm()*(Pos:dist(TPOS)+250),Pos
	elseif EnemiesNear(unit,800) and (AllyNear(unit,850) or EnemiesNear(unit,800).pos:distSqr(unit)<=400^2) then
		local input2= {
		  delay = unit.pos:dist(EnemiesNear(unit,800))/1500,
		  speed = math.huge,
		  width = 100,
		  boundingRadiusMod = 1,
		  collision = {
			hero = false,
			minion = false,
		  },
		}
		local PREDPOS= preds.linear.get_prediction(input2, EnemiesNear(unit,800))
		if PREDPOS and PREDPOS.endPos then
		local POS=vec3(PREDPOS.endPos.x,EnemiesNear(unit,800).pos.y,PREDPOS.endPos.y)
		return POS + (TPOS-POS):norm()*(POS:dist(TPOS)+250),POS
		end
	elseif AllyNear(unit,1500) then
		local Pos=AllyNear(unit,1500)
		return Pos + (TPOS-Pos):norm()*(Pos:dist(TPOS)+250),Pos
	elseif originalpos then
		return originalpos + (TPOS-originalpos):norm()*(originalpos:dist(TPOS)+250),originalpos
	else
		return player.pos + (TPOS-player.pos):norm()*(player.pos:dist(TPOS)+250),player.pos
	end
end

local function insecrange()
	if MyMenu.insecset.flash:get() and Flash and player:spellSlot(Flash).state == 0 then
		if WardSlot() and player:spellSlot(1).state==0 and FirstSpell(1) then
			return 600+375+200
			else
			return 375+200
		end
		elseif WardSlot() and player:spellSlot(1).state==0 and FirstSpell(1) then
		return 600
		else 
		return 0
	end
end

local ff = function(res, obj, dist)
	if player:spellSlot(0).state == 0 and FirstSpell(0) then
	local seg = preds.linear.get_prediction(qPred, obj)
		if seg and seg.startPos:distSqr(seg.endPos) <= (EnemiesNear(player,1100) and 1100 or 1100+insecrange()-200) ^2 then
			res.obj = obj
			return true
		end
	elseif player:spellSlot(0).state == 0 and not FirstSpell(0) then
		if Qtarget and getinsecpos(obj) and dist and dist<=player.path.serverPos:dist(getinsecpos(obj))+insecrange()-200 then
		res.obj = obj
		return true
		elseif obj.buff['blindmonkqone'] and dist and dist<=1300 then
		res.obj = obj
		return true
		end
	elseif MyMenu.combo.w:get() and player:spellSlot(1).state==0 and FirstSpell(1) and getinsecpos(obj) and player.pos:distSqr(getinsecpos(obj))<=insecrange()-100 then
		res.obj = obj
		return true
	elseif player:spellSlot(3).state == 0 and dist and dist<=(375+obj.boundingRadius) then
		res.obj = obj
		return true
	end
end
local GetInsecTarget=function()
	return orb.ts.get_result(ff).obj
end

local ForcePoint=function(pos)
	if not pos and orb.core.is_move_paused() then
		orb.core.set_pause_move(0)
	elseif pos then
		orb.core.set_pause_move(player:basicAttack(0).clientAnimationTime)
		player:move(vec3(pos))
	end
end

local function CastQ(unit)
	if player:spellSlot(0).state ~= 0 or not unit or not common.IsValidTarget(unit) or not FirstSpell(0) or game.time<LastQ+0.5 then return false end
	local input = {delay = 0.25, speed = 1800, width = 58, boundingRadiusMod = 1, collision = {hero = false, minion = true, wall = true}}
	local seg = preds.linear.get_prediction(input, unit)
	if seg and seg.endPos and seg.startPos:distSqr(seg.endPos) <= 1090^2 then
		local collision = preds.collision.get_prediction(input, seg,unit)
		if not collision then
		player:castSpell("pos", 0, vec3(seg.endPos.x, seg.endPos.y, seg.endPos.y))
		LastQ = game.time
		elseif Smite and player:spellSlot(Smite).state == 0 and unit.type==player.type then
			if #collision == 1 and collision[1] and collision[1].pos:distSqr(player.pos) <= 313600 and collision[1].health <= SmiteDamage[player.levelRef] then
			player:castSpell('obj', Smite, collision[1])
			player:castSpell('pos', 0, vec3(seg.endPos.x, seg.endPos.y, seg.endPos.y))
			LastQ=game.time
			end
		end
	end
end

local function CastQ2(unit,Pos)
	if player:spellSlot(0).state~=0 or not unit or not common.IsValidTarget(unit) or not Pos then return false end
	local input = {delay = 0.25, speed = 1800, width = 60, boundingRadiusMod = 1, collision = {hero = false, minion = true, wall = true},}
	local seg = preds.linear.get_prediction(input, unit)
	if seg and seg.endPos then
		local collision = preds.collision.get_prediction(input, seg,unit)
		if not collision and seg.startPos:distSqr(seg.endPos)<=1090^2 then
		player:castSpell('pos', 0, vec3(seg.endPos.x, seg.endPos.y, seg.endPos.y))
		elseif collision and Smite and player:spellSlot(Smite).state == 0 then
			if #collision==1 and seg.startPos:distSqr(seg.endPos)<=1090^2 and collision[1] and collision[1].type==TYPE_MINION and collision[1].pos:distSqr(player.pos) <= 313600 and collision[1].health<=SmiteDamage[player.levelRef] then
			player:castSpell('obj', Smite, collision[1])
			player:castSpell('pos', 0, vec3(seg.endPos.x, seg.endPos.y, seg.endPos.y))
			end
		elseif collision then
			for i=1,#collision do
			local obj=collision[i]
				if obj and not obj.isDead and obj.isTargetable and obj.path.serverPos:distSqr(Pos)<=(insecrange()-200)^2 and obj.isVisible and obj.health>Q1Dmg(obj) and not WardName(obj) then
					player:castSpell('pos', 0, vec3(seg.endPos.x, seg.endPos.y, seg.endPos.y))
				end
			end
		end
	end
end

local function Combo()
	if not Target or not common.IsValidTarget(Target) then return end
	if not HasSionBuff(Target) then
		local q = player:spellSlot(0).state == 0
		local w = player:spellSlot(1).state == 0
		local e = player:spellSlot(2).state == 0
		local r = player:spellSlot(3).state == 0
		local distsqr = player.path.serverPos:distSqr(Target.path.serverPos)
		if MyMenu.combo.q:get() and q then
			if FirstSpell(0) and ((orb.core.is_attack_paused() or (not orb.core.can_attack() and orb.core.can_action())) or distsqr>(player.attackRange+player.boundingRadius+Target.boundingRadius)^2) then
				CastQ(Target)
			elseif not FirstSpell(0) and Target.buff['blindmonkqone'] and (not UnderTurret(Target) or UnderTurret(player) or not MyMenu.combo.notq) and (player:spellSlot(3).state~=0 or not MyMenu.combo.r:get() or Target.health+Target.physicalShield>Q2Dmg(Target,RDmg(Target),0) or distsqr>375^2) and ((AttackPassive == 0 and (orb.core.is_attack_paused() or (not orb.core.can_attack() and orb.core.can_action()))) or game.time>=Target.buff['blindmonkqone'].startTime+(Target.buff['blindmonkqone'].endTime-Target.buff['blindmonkqone'].startTime)-0.5 or (distsqr > (player.attackRange+player.boundingRadius+100)^2) or Target.health + Target.physicalShield<=Q2Dmg(Target,0)) then
				player:castSpell('self', 0)
			end
		end
		if r and MyMenu.combo.r:get() and distsqr <= 375^2 then
			if MyMenu.combo.q:get() and q and not FirstSpell(0) and Target.buff['blindmonkqone'] and Target.health+Target.physicalShield<=Q2Dmg(Target,RDmg(Target)) and player.mana >=30 then
				player:castSpell('obj', 3, Target)
				common.DelayAction(function()
				player:castSpell('self', 0)
				end,0.35)
				elseif Target.health+Target.physicalShield<=RDmg(Target) then
				player:castSpell('obj', 3, Target)
			end
		end
		if MyMenu.combo.w:get() and w then
			if FirstSpell(1) then
				if AttackPassive==0 and distsqr<=(player.attackRange+player.boundingRadius+Target.boundingRadius)^2 then
					player:castSpell('obj', 1, player)
				elseif not player.buff['blindmonkqtwodash'] and not UnderTurret(Target) and Target.health<=AADmg(Target)*3+EDmg(Target) and player.mana>=80 and distsqr<=(600+player.attackRange+player.boundingRadius)^2 and distsqr>(player.attackRange+player.boundingRadius+Target.boundingRadius)^2 and (not MyMenu.combo.q:get() or player:spellSlot(0).state~=0) then
					WardJump(Target.pos)
				end
			elseif AttackPassive<2 and (orb.core.is_attack_paused() or (not orb.core.can_attack() and orb.core.can_action())) and distsqr<=(player.attackRange+player.boundingRadius+Target.boundingRadius)^2 then
				player:castSpell('self', 1)
			end
		end
		if MyMenu.combo.e:get() and e then
			if FirstSpell(2) and (AttackPassive < 2 or distsqr > (player.attackRange+player.boundingRadius+Target.boundingRadius)^2) and ((orb.core.is_attack_paused() or (not orb.core.can_attack() and orb.core.can_action())) or distsqr>(player.attackRange+player.boundingRadius+Target.boundingRadius)^2) and distsqr <= 422^2 then
				player:castSpell('self', 2)
			elseif not FirstSpell(2) and ((AttackPassive ==0 and (orb.core.is_attack_paused() or (not orb.core.can_attack() and orb.core.can_action()))) or distsqr>(player.attackRange+player.boundingRadius+Target.boundingRadius)^2) then
				player:castSpell('self', 2)
			end
		end	
	end
end

local function SemiInsec()
	if (not MyMenu.Key.Combo:get() and not MyMenu.Key.Harass:get() and not MyMenu.Key.wardjump:get() and not MyMenu.Key.LaneClear:get() and not Target) or not player:spellSlot(3).state == 0 then
		player:move(game.mousePos)
	end
	if not Target or not common.IsValidTarget(Target) then return end
	if player.pos:distSqr(Target.pos)>375^2 or player:spellSlot(3).state ~= 0 then
		player:move(game.mousePos)
	end
	if player.pos:distSqr(Target.pos)<=375^2 and player:spellSlot(3).state == 0 and Flash and player:spellSlot(Flash).state == 0  then
		player:castSpell("obj", 3, Target)
	end
end

local function Insec()
	if (not MyMenu.Key.Combo:get() and not MyMenu.Key.Harass:get() and not MyMenu.Key.wardjump:get() and not MyMenu.Key.LaneClear:get() and not Target) or (Target and not common.IsValidTarget(Target)) or not player:spellSlot(3).state == 0 then
	player:move(game.mousePos)
	end
	if not Target or not common.IsValidTarget(Target) then return end
	if not HasSionBuff(Target) and not Target.buff["blackshield"] and not Target.buff["sivire"] then
		local distsqr=Target.pos:distSqr(player.pos)
		local insecpos2=vec3(getinsecpos(Target).x,getinsecpos(Target).y,getinsecpos(Target).z)
		if LastInsec<game.time then
			insecPos=insecpos2
		end
		if player:spellSlot(3).state == 0 and player.pos:distSqr(Target.pos)<=375^2 then
			if Target.health+Target.physicalShield<=RDmg(Target) then
				player:castSpell('obj', 3, Target)
				return
			elseif player.pos:distSqr(insecPos)<=200^2 then
				player:castSpell('obj', 3, Target)
			end
		end
		if insecPos then
			if player.pos:distSqr(insecPos)>200^2 or player:spellSlot(3).state ~= 0 then
				player:move(game.mousePos)
			end
			if player.pos:distSqr(insecPos) < insecrange()^2 and player:spellSlot(3).state == 0 then
				local ward=WardSlot()
				if player.pos:distSqr(insecPos)>200^2 and (not MyMenu.insecset.waitq:get() or not player.buff['blindmonkqtwodash']) and ward and player:spellSlot(1).state == 0 and FirstSpell(1) and player:spellSlot(3).state == 0 then 
					if player.pos:distSqr(insecPos)<600^2 then
						if InsecJump(insecPos) then
							LastInsec=game.time+1.25
						end
					elseif not player.buff['blindmonkqtwodash'] and player.path.serverPos:distSqr(Target.path.serverPos)<850^2 then
						if InsecJump(Target.path.serverPos) then
							LastInsec=game.time+1.25
						end
					end
				end
				if (not MyMenu.insecset.waitq:get() or not player.buff['blindmonkqtwodash']) and MyMenu.insecset.flash:get() and Flash and player:spellSlot(Flash).state == 0 and player.pos:distSqr(Target.pos)<=375^2  then
					if (not ward or player:spellSlot(1).state ~= 0 or not FirstSpell(1)) and player.pos:distSqr(insecPos)>200^2 then
					player:castSpell('obj', 3, Target)
					end
				end
			end
		end
		local function check() 
			if WardSlot() and player:spellSlot(1).state == 0 and FirstSpell(1) and Qtarget and insecPos and Qtarget.path.serverPos:distSqr(insecPos)<=(insecrange()-150)^2 then
				return true
			elseif MyMenu.insecset.flash:get() and Flash and player:spellSlot(Flash).state == 0 and Qtarget and Qtarget.path.serverPos:distSqr(Target)<=375^2 then
				return true
			end
		end
		if player:spellSlot(3).state == 0 and player:spellSlot(0).state == 0 then
			if FirstSpell(0) and player.path.serverPos:distSqr(Target.path.serverPos)>375^2 and insecPos and player.pos:distSqr(insecPos)>200^2 then 
				CastQ2(Target,insecPos)
			elseif (check() or Target.buff['blindmonkqone']) and not FirstSpell(0) and (not insecPos or (insecPos and player.pos:distSqr(insecPos)>200^2)) then
				player:castSpell('self', 0)
			end
		end
	end
end

local function Harass()
	--if player.par / player.maxPar * 100 >= MyMenu.harass.Mana:get() then
		if not Target or not common.IsValidTarget(Target) then return end
		local distsqr=player.path.serverPos:distSqr(Target.path.serverPos)
		if MyMenu.harass.q:get() and player:spellSlot(0).state==0 then
			if FirstSpell(0) and ((orb.core.is_attack_paused() or (not orb.core.can_attack() and orb.core.can_action())) or distsqr>(player.attackRange+player.boundingRadius+Target.boundingRadius)^2) then
				CastQ(Target)
			elseif not FirstSpell(0) and Target.buff['blindmonkqone'] and (not UnderTurret(Target) or UnderTurret(player) or not MyMenu.harass.notq) and ((AttackPassive == 0 and (orb.core.is_attack_paused() or (not orb.core.can_attack() and orb.core.can_action()))) or game.time>=Target.buff['blindmonkqone'].startTime+(Target.buff['blindmonkqone'].endTime-Target.buff['blindmonkqone'].startTime)-0.5 or (distsqr > (player.attackRange+player.boundingRadius+100)^2) or Target.health + Target.physicalShield<=Q2Dmg(Target,0)) then
				player:castSpell('self', 0)
			end
		end
		if MyMenu.harass.e:get() and player:spellSlot(2).state==0 then
			if FirstSpell(2) and (AttackPassive < 2 or distsqr > (player.attackRange+player.boundingRadius+Target.boundingRadius)^2) and ((orb.core.is_attack_paused() or (not orb.core.can_attack() and orb.core.can_action())) or distsqr>(player.attackRange+player.boundingRadius+Target.boundingRadius)^2) and distsqr <= 425^2 then
				player:castSpell('self', 2)
			elseif not FirstSpell(2) and ((AttackPassive ==0 and (orb.core.is_attack_paused() or (not orb.core.can_attack() and orb.core.can_action()))) or distsqr>(player.attackRange+player.boundingRadius+Target.boundingRadius)^2) then
				player:castSpell('self', 2)
			end
		end	
	--end
end

local function KillSteal()
	if not MyMenu.ks.enable:get() and (not MyMenu.combo.rkick:get() or player:spellSlot(3).state~=0) then return end
	for i = 0 , objManager.enemies_n - 1 do 
		local obj = objManager.enemies[i]
		if obj and common.IsValidTarget(obj) and obj.team ~= player.team and not HasSionBuff(obj) then
			if MyMenu.combo.rkick:get() and player:spellSlot(3).state==0 and player.pos:distSqr(obj.pos)<=350^2 and not MyMenu.Key.insec:get() then
				CountEnemiesInR(obj.boundingRadius*2,player, obj.pos + (player.pos - obj.pos):norm()*(player.pos:dist(obj.pos)-800),obj,MyMenu.combo.rkickhit:get())
			end
			if MyMenu.ks.enable:get() then
			if MyMenu.ks.q3:get() and not MyMenu.ks.q2:get() and not MyMenu.ks.q1:get() then
				if player:spellSlot(0).state==0 and FirstSpell(0) and obj.health<=Q3Dmg(obj) and player.pos:distSqr(obj.pos)<=1100^2 then
					CastQ(obj)
				elseif player:spellSlot(0).state==0 and obj.health<=Q1Dmg(obj) and obj.buff['blindmonkqone'] and not FirstSpell(0) and player.pos:distSqr(obj.pos)<=1300^2 then
					player:castSpell('self', 0)
				end
			elseif not MyMenu.ks.q3:get() and MyMenu.ks.q2:get() and player:spellSlot(0).state==0 and obj.health<=Q1Dmg(obj) and obj.buff['blindmonkqone'] and not FirstSpell(0) and player.pos:distSqr(obj.pos)<=1300^2 then
				player:castSpell('self', 0)
			elseif not MyMenu.ks.q3:get()  and MyMenu.ks.q1:get() and player:spellSlot(0).state==0 and obj.health<=Q1Dmg(obj) and FirstSpell(0) and player.pos:distSqr(obj.pos)<=1100^2 then
				CastQ(obj)
			elseif MyMenu.ks.e:get() and player:spellSlot(2).state==0 and FirstSpell(3) and obj.pos:distSqr(player.pos)<=425^2 and obj.health<=EDmg(obj) then
				player:castSpell('self', 2)
			elseif MyMenu.ks.r:get() and player:spellSlot(3).state==0 and obj.pos:distSqr(player.pos)<=(375+obj.boundingRadius)^2 and obj.health<=RDmg(obj) then
				player:castSpell('obj', 3, obj)
			end
		end
		end
	end
end

local function LaneClear()
	local minions = objManager.minions
	 for i = 0, minions.size[TEAM_NEUTRAL] - 1 do
		local obj = minions[TEAM_NEUTRAL][i]
		if obj and not obj.isDead and obj.isVisible and obj.isTargetable and obj.pos:distSqr(player.pos) < 1300^2 and not WardName(obj) then
			if MyMenu.jg.q:get() and player:spellSlot(0).state==0 and not FirstSpell(0) and obj.buff['blindmonkqone'] and ((AttackPassive==0 and (orb.core.is_attack_paused() or (not orb.core.can_attack() and orb.core.can_action()))) or game.time>=obj.buff['blindmonkqone'].startTime+(obj.buff['blindmonkqone'].endTime-obj.buff['blindmonkqone'].startTime)-0.5 or obj.pos:distSqr(player.pos) > (player.attackRange+player.boundingRadius+obj.boundingRadius)^2 or obj.health<Q2Dmg(obj,0)) then
				player:castSpell('self', 0)
			elseif AttackPassive <2 and MyMenu.jg.w:get() and player:spellSlot(1).state==0 and obj.pos:distSqr(player.pos) <(player.attackRange+player.boundingRadius+obj.boundingRadius)^2 and (orb.core.is_attack_paused() or (not orb.core.can_attack() and orb.core.can_action())) then
				player:castSpell('obj', 1, player)
			elseif AttackPassive <2 and MyMenu.jg.e:get() and player:spellSlot(2).state==0 and FirstSpell(2) and obj.pos:distSqr(player.pos) < 425^2 and (orb.core.is_attack_paused() or (not orb.core.can_attack() and orb.core.can_action()))  then
				player:castSpell('obj', 2, player)
			end
			if AttackPassive == 0 then
				if MyMenu.jg.q:get() and player:spellSlot(0).state==0 and FirstSpell(0) and obj.pos:distSqr(player.pos) <= 1100^2  then
					CastQ(obj)
				elseif MyMenu.jg.e:get() and player:spellSlot(2).state==0 and obj.pos:distSqr(player.pos) < (player.attackRange+player.boundingRadius+obj.boundingRadius)^2 and (orb.core.is_attack_paused() or (not orb.core.can_attack() and orb.core.can_action())) then
					player:castSpell('self', 2)
				end
			end
		end
    end
	for i = 0, minions.size[TEAM_ENEMY] - 1 do
		local obj=minions[TEAM_ENEMY][i]
		if obj and not obj.isDead and obj.isVisible and obj.isTargetable and obj.pos:distSqr(player.pos) < 1100^2 and not WardName(obj) then 
			if MyMenu.lc.q:get() and player:spellSlot(0).state==0 and not FirstSpell(0) and obj.buff['blindmonkqone'] and ((AttackPassive==0 and (orb.core.is_attack_paused() or (not orb.core.can_attack() and orb.core.can_action())))  or game.time>=obj.buff['blindmonkqone'].startTime+(obj.buff['blindmonkqone'].endTime-obj.buff['blindmonkqone'].startTime)-0.5 or obj.pos:distSqr(player.pos) > (player.attackRange+player.boundingRadius+obj.boundingRadius)^2 or obj.health<Q2Dmg(obj,0)) then
				player:castSpell('self',0)
			end

             if AttackPassive == 0 then
				if MyMenu.lc.q:get() and player:spellSlot(0).state==0 and FirstSpell(0) and obj.pos:distSqr(player.pos) <= 1100^2 and ((orb.core.is_attack_paused() or (not orb.core.can_attack() and orb.core.can_action())) and obj.pos:distSqr(player.pos)>(player.attackRange+player.boundingRadius+obj.boundingRadius)^2) and obj.health<Q1Dmg(obj) then
					CastQ(obj)
				elseif MyMenu.lc.e:get() and player:spellSlot(2).state==0 and obj.pos:distSqr(player.pos) < 425^2 then
					if FirstSpell(2) and ((orb.core.is_attack_paused() or (not orb.core.can_attack() and orb.core.can_action())) or obj.pos:distSqr(player.pos)>(player.attackRange+player.boundingRadius+obj.boundingRadius)^2) and obj.health<EDmg(obj) then
						player:castSpell('self', 2)
					else
						player:castSpell('self', 2)
					end
				end
			end
		end
    end
end



local function Q(spell)
	if spell and spell.owner and spell.owner==player then
		if spell.name:lower():find('blindmonk') and spell.name~="BlindMonkRKick" then
			AttackPassive=2
		end
		if spell.name=="BlindMonkQOne" then
			LastQ=game.time
		end
		if spell.name=="BlindMonkRKick" then
			common.DelayAction(function() insecclickpos=nil end,0.4)
			if MyMenu.Key.semiinsec:get() and spell.target and spell.target.health+spell.target.physicalShield>RDmg(spell.target) then
				local insecpos=vec3(getinsecpos(spell.target).x,getinsecpos(spell.target).y,getinsecpos(spell.target).z)
				if player.pos:distSqr(insecpos)>200^2 then
					player:castSpell('pos', Flash, insecpos)
				end
			end
			
			if MyMenu.Key.insec:get() and spell.target and spell.target.health+spell.target.physicalShield>RDmg(spell.target)  and (not ward or player:spellSlot(1).state ~= 0 or not FirstSpell(1)) and player.pos:distSqr(insecPos)>200^2 then
				local insecpos=vec3(getinsecpos(spell.target).x,getinsecpos(spell.target).y,getinsecpos(spell.target).z)
				player:castSpell('pos', Flash, insecpos)
			end	
		end
		if spell.name:lower():find('basicattack') and AttackPassive>0 then
			AttackPassive = AttackPassive-1
		end
	end
end



local function OnTick()
	if MyMenu.Key.insec:get() and player:spellSlot(3).state == 0 then
		Target = GetInsecTarget()
	else
		Target = GetTarget()
	end
	if MyMenu.Key.Combo:get() then
		Combo()
	elseif MyMenu.Key.insec:get() then
		Insec()	
	end
	if MyMenu.Key.Harass:get() then
		Harass()
	end
	if MyMenu.Key.wardjump:get() then
		WardJump(game.mousePos)	
	end
	if MyMenu.Key.semiinsec:get() then
		SemiInsec()	
	end

	if MyMenu.ks.enable:get() then
		KillSteal()
	end
	if keyboard.isKeyDown(1) then
		--local pos = game.mousePos
		--insecclickpos = pos
	end
	if MyMenu.Key.LaneClear:get() then LaneClear() end
	for z, ally in ipairs(allies) do
        if ally then
            for i = 0, ally.buffManager.count - 1 do
                local buff = ally.buffManager:get(i)
                if buff and buff.valid and buff.owner.team~=player.team and buff.source and buff.source==player and buff.name:lower()=='blindmonkqone' then
                    Qtarget=buff.owner
                else
                	Qtarget=nil
                end
            end
        end
    end
end

local function OnDraw()
	if MyMenu.draws.q:get() and player:spellSlot(0).state == 0 and player.isOnScreen then
		if FirstSpell(0) then
			graphics.draw_circle(player.pos, 1100, 2, MyMenu.draws.colorq:get(), 50)
		else
			graphics.draw_circle(player.pos, 1300, 2, MyMenu.draws.colorq:get(), 50)
		end
	end
	if MyMenu.draws.r:get() and player:spellSlot(3).state == 0 and player.isOnScreen then
		graphics.draw_circle(player.pos, 375, 2, MyMenu.draws.colorr:get(), 50)
	end
	if MyMenu.draws.Insec:get() and player:spellSlot(3).state==0 and player.isOnScreen then
		if insecclickpos then
			graphics.draw_circle(vec3(insecclickpos.x,insecclickpos.y,insecclickpos.z), 100, 2, graphics.argb(255, 168, 0, 157), 12)
		end
		if Target and Target.isVisible and not Target.isDead and Target.isTargetable and Target.isOnScreen then
			local insecpos,insecpos2 = getinsecpos(Target)
			if insecpos2 and insecPos then
				graphics.draw_line(vec3(insecpos2.x,insecpos2.y,insecpos2.z), vec3(Target.x,Target.y,Target.z), 2, graphics.argb(255, 168, 0, 157))
				graphics.draw_circle(vec3(insecPos.x,insecPos.y,insecPos.z), 30, 3, graphics.argb(255, 7, 141, 237), 24)
			end
		end
	end
end

orb.combat.register_f_pre_tick(OnTick)
cb.add(cb.draw, OnDraw)
cb.add(cb.spell, Q)

cb.add(cb.keydown, keydown)
cb.add(cb.keyup, keyup)

return LeeSinPlugin
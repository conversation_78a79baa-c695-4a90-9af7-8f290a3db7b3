local ove_0_5 = module.load(header.id,"orianna/spells_targeted")
local ove_0_6 = menu(header.id, "[<PERSON>] orian<PERSON>")

print(ove_0_5)

ove_0_6:header("header_Q", "Q")
ove_0_6:slider("max_q_range", "Max Q Range", 3000, 0, 3000, 50)
ove_0_6:header("header_e", "E")
ove_0_6:boolean("e_magnet", "Use E Magnet", true)
ove_0_6.e_magnet:set("tooltip", "Casts E on the ally nearest to your mouse, self included")
ove_0_6:keybind("e_magnet_key", "E Magnet", "E", nil)
ove_0_6:boolean("e_q", "Utilize E for Q", true)
ove_0_6:boolean("e_r", "Utilize E for R", true)
ove_0_6:menu("auto_e_spell", "E on Targeted Spells")

local ove_0_7 = {
	"Q",
	"W",
	"E",
	"R"
}

for iter_0_0 = 0, objManager.enemies_n - 1 do
	local ove_0_8 = objManager.enemies[iter_0_0].charName

	if ove_0_5[ove_0_8] then
		ove_0_6.auto_e_spell:menu(ove_0_8, ove_0_8)

		for iter_0_1, iter_0_2 in pairs(ove_0_5[ove_0_8]) do
			ove_0_6.auto_e_spell[ove_0_8]:menu(iter_0_1, type(iter_0_2) == "string" and iter_0_2 or ove_0_7[iter_0_1 + 1])

			local ove_0_9 = ove_0_6.auto_e_spell[ove_0_8][iter_0_1]

			ove_0_9:boolean("enabled", "Enable", false)
			ove_0_9:boolean("combat_mode", "Use only in Combat Mode", false)
			ove_0_9:slider("hp", "Use only if HP smaller than [%]", 100, 0, 100, 1)
		end
	end
end

ove_0_6:header("header_r", "R")
ove_0_6:boolean("r_block", "Block R if no hits", true)
ove_0_6:boolean("r_self", "Don't block R if Ball is on self", false)
ove_0_6:menu("auto_r", "Auto R")
ove_0_6.auto_r:slider("n", "Number of Hits", 3, 1, 5, 1)
ove_0_6.auto_r:boolean("combat", "Auto R in Combat Mode only", false)
ove_0_6.auto_r:header("header_whitelist", "Whitelist")

for iter_0_3 = 0, objManager.enemies_n - 1 do
	local ove_0_10 = objManager.enemies[iter_0_3]

	ove_0_6.auto_r:boolean(ove_0_10.charName, ove_0_10.charName, true)
end

ove_0_6:menu("smart_r", "Smart R")
ove_0_6.smart_r:slider("n", "Number of Hits", 2, 1, 5, 1)
ove_0_6.smart_r:boolean("combat", "Smart R in Combat Mode only", true)
ove_0_6.smart_r:header("header_prio", "Prioriannaty")

for iter_0_4 = 0, objManager.enemies_n - 1 do
	local ove_0_11 = objManager.enemies[iter_0_4]

	ove_0_6.smart_r:dropdown(ove_0_11.charName, ove_0_11.charName, 3, {
		"None",
		"Low",
		"Normal",
		"High"
	})
end

ove_0_6:header("header_harass", "Harass")
ove_0_6:boolean("harass_q", "Use Q", true)
ove_0_6:boolean("harass_w", "Use W", false)
ove_0_6:keybind("auto_q", "Auto Q", nil, "V")
ove_0_6:keybind("harass", "Harass Key", "X", nil)
ove_0_6:header("header_combat", "Combat")
ove_0_6:keybind("combat", "Combat Key", "Space", nil)

return ove_0_6

local AhriPlugin = {}

local spellQ = {
    range = 830,
    delay = 0.25,
    width = 150,
    speed = 1700,
    boundingRadiusMod = 0,
    collision = {hero = false, minion = false, wall = false},
}


local spellW = {
    range = 700,
}

local spellE = {
    range = 970,
    delay = 0.25,
    width = 120,
    speed = 1550,
    boundingRadiusMod = 0,
    collision = {hero = true, minion = true, wall = false},
    
}

local spellR = {
    range = 450,
}
local Curses = module.load("<PERSON>", "Curses");
local preds = module.internal("pred")
local orb = module.internal("orb")
local ui = module.load("<PERSON>", "ui");
local DelayAction = module.load("<PERSON>", "Core/DelayAction")
local DelayTick = module.load("<PERSON>", "Core/DelayTick")
local Prediction = module.load("Brian", "Core/Prediction")
local BuffManager = module.load("Brian", "Library/BuffManager")
local CalculateManager = module.load("<PERSON>", "Library/CalculateManager")
local FarmManager = module.load("<PERSON>", "Library/FarmManager")
--local autoItem = module.load("<PERSON>", "Utility/AutoItem")
local ItemManager = module.load("<PERSON>", "Library/ItemManager")
local NetManager = module.load("Brian", "Library/NetManager")
local ObjectManager = module.load("Brian", "Library/ObjectManager")
local OrbManager = module.load("Brian", "Library/OrbManager")
local SpellManager = module.load("Brian", "Library/SpellManager")
local VectorManager = module.load("Brian", "Library/VectorManager")
local MyCommon = module.load("Brian", "Library/ExtraManager")

local function GetQDamage(target)
    if not target or target == nil then
        return 0
    end
    local level = player:spellSlot(0).level
    if level == 0 then
        return 0
    end
    local dmg = ({40, 65, 90, 115, 140})[level] * (0.35 * MyCommon.GetTotalAP())
    return CalculateManager.CalculateMagicDamage(target, dmg)
end

local function GetWDamage(target)
    if not target or target == nil then
        return 0
    end
    local level = player:spellSlot(1).level
    if level == 0 then
        return 0
    end
    local dmg = ({40, 65, 90, 115, 140})[level] * (0.30 * MyCommon.GetTotalAP())
    return CalculateManager.CalculateMagicDamage(target, dmg)
end

local function GetEDamage(target)
    if not target or target == nil then
        return 0
    end
    local level = player:spellSlot(2).level
    if level == 0 then
        return 0
    end
    local dmg = ({60, 90, 120, 150, 180})[level] + (0.4 * MyCommon.GetTotalAP())
    return CalculateManager.CalculateMagicDamage(target, dmg)
end

local function GetRDamage(target)
    if not target or target == nil then
        return 0
    end
    local level = player:spellSlot(3).level
    if level == 0 then
        return 0
    end
    local dmg = ({60, 90, 120})[level] + (0.35 * MyCommon.GetTotalAP())
    return CalculateManager.CalculateMagicDamage(target, dmg)
end
local default = {
  text = 0xD0FFFFFF,
  mouseOver = 0x60000000,
  backGround = 0x30000000,
  border = 0x401C1C1C,
  menu = {
    selected = 0x90000000,
    textSelected = 0xF0FFFFFF,
  },
  boolean = {
    active = 0x90000000,
    textActive = 0xF0FFFFFF,
  },
  slider = {
    bar = 0x70CFCFCF,
  },
  header = {
    fill = 0x80000000,
    text = 0xF0FFFFFF,
  },
  keybind = {
    textActive = 0xFFFFFFFF,
    textInactive = 0xFFFFFFFF,
  },
  button = {
    fill = 0x80000000,
    text = 0xF0FFFFFF,
  },
  color = {
    outLine = 0xFFFFFFFF,
  },
}
--Menu:setcustomtheme(default)

local MyMenu
local icon = graphics.sprite('mao.png')

function AhriPlugin.Load(GlobalMenu)
--MyMenu:setcustomtheme(default)
    MyMenu = GlobalMenu
    MyMenu:set('icon',  player.iconSquare)
    MyMenu:menu("Combo", "Combo Settings")
    MyMenu.Combo:header("SpellHeader", "Spell Core")
    MyMenu.Combo:boolean("Q", "Use Q", true)
    MyMenu.Combo:boolean("W", "Use W", true)
    MyMenu.Combo:boolean("E", "Use E", true)
    MyMenu.Combo:boolean("R", "Use R", true)

    MyMenu:menu("Harass", "Harass Settings")
    MyMenu.Harass:header("SpellHeader", "Spell Core")
    MyMenu.Harass:boolean("Q", "Use Q", true)
    MyMenu.Harass:header("ManaHeader", "Mana Manager")
    MyMenu.Harass:slider("ManaMin", "Player ManaPercent >= x%", 60, 1, 100, 1)
    MyMenu.Harass:header("ProHeader", "PRO Mode")
    MyMenu.Harass:boolean("ProAllow", "Enabled PRO Harass Mode", true)
    MyMenu.Harass.ProAllow:set("tooltip", "PRO Mode => Allow Use Spell Harass Enemy on LaneClear Mode")
    MyMenu.Harass:boolean("ProTurret", "Allow Under Turret Harass", false)

    MyMenu:menu("LaneClear", "LaneClear Settings")
    MyMenu.LaneClear:header("SpellHeader", "Spell Core")
    MyMenu.LaneClear:boolean("Q", "Use Q", true)
    MyMenu.LaneClear:slider("QC", "^ Min Hit Count >= x", 3, 1, 10, 1)
    MyMenu.LaneClear:header("ManaHeader", "Mana Manager")
    MyMenu.LaneClear:slider("ManaMin", "Player ManaPercent >= x%", 60, 1, 100, 1)

    MyMenu:menu("JungleClear", "JungleClear Settings")
    MyMenu.JungleClear:header("SpellHeader", "Spell Core")
    MyMenu.JungleClear:boolean("Q", "Use Q", true)
    MyMenu.JungleClear:boolean("E", "Use E", true)
    MyMenu.JungleClear:boolean("W", "Use W", true)
    MyMenu.JungleClear:header("ManaHeader", "Mana Manager")
    MyMenu.JungleClear:slider("ManaMin", "Player ManaPercent >= x%", 20, 1, 100, 1)


    FarmManager.Load(MyMenu)

    MyMenu:menu("KillSteal", "KillSteal Settings")
    MyMenu.KillSteal:header("SpellHeader", "Spell Core")
    MyMenu.KillSteal:boolean("Q", "Use Q", true)
    MyMenu.KillSteal:boolean("E", "Use E", true)

    MyMenu:menu("Draw", "Draw Settings")
    MyMenu.Draw:boolean("Q", "Draw Q rage", true)
    MyMenu.Draw:color("colorq", "^ color", 255, 233, 121, 121)
    MyMenu.Draw:boolean("W", "Draw W range", true)
    MyMenu.Draw:color("colorw", "^ color", 255, 233, 121, 121)
    MyMenu.Draw:boolean("E", "Draw E range", true)
    MyMenu.Draw:color("colore", "^ color", 255, 233, 121, 121)
    MyMenu.Draw:boolean("R", "Draw R range", true)
    MyMenu.Draw:color("colorr", "^ color", 255, 233, 121, 121)
    MyMenu.Draw:boolean("Ready", "Only Spell Ready", false)
    MyMenu.Draw:header("DamageHeader", "Damage Indicator")
    MyMenu.Draw:boolean("DIEnabled", "Enabled", true)
end



local function Combo()

    if MyMenu.Combo.E:get() and SpellManager.CanCastSpell(2) then
        local target = MyCommon.GetTarget(spellE.range)
        if target and target ~= nil and MyCommon.IsValidTarget(target, spellE.range) then
            local pred = Prediction.GetPrediction(spellE, target)
            if pred then
                SpellManager.CastOnPosition(vec3(pred.x, target.pos.y, pred.y), 2)
            end
        end
    end

    if MyMenu.Combo.Q:get() and SpellManager.CanCastSpell(0) then
        local target = MyCommon.GetTarget(spellQ.range)
        if target and target ~= nil and MyCommon.IsValidTarget(target, spellQ.range) then
            local pred = Prediction.GetPrediction(spellQ, target)
            if pred then
                SpellManager.CastOnPosition(vec3(pred.x, target.pos.y, pred.y), 0)
            end
        end
    end

    if MyMenu.Combo.W:get() and SpellManager.CanCastSpell(1) then
        local target = MyCommon.GetTarget(spellW.range)
        if target and target ~= nil and MyCommon.IsValidTarget(target, spellW.range) then
            SpellManager.CastOnPlayer(1)
        end
    end

    if MyMenu.Combo.R:get() and SpellManager.CanCastSpell(3) then
        local target = MyCommon.GetTarget(spellR.range)
        if target and target ~= nil and MyCommon.IsValidTarget(target, spellR.range) then
            local dashVector = player.pos + (((game.mousePos - player.pos):norm()) * 425)
            if player.pos:dist(game.mousePos) < 475 then
                dashVector = game.mousePos
            end
            if target.pos:dist(dashVector) > 525 then
                return
            end
            if #ObjectManager.GetEnemiesInRange(800, game.mousePos) < 3 and target.health then
                SpellManager.CastOnPosition(game.mousePos, 3)
            end
        end
    end
end


local function Harass()
    if not MyMenu.Harass.ProTurret:get() and ObjectManager.IsUnderEnemyTurret(player) then
        return
    end
    if MyCommon.GetManaPercent() >= MyMenu.Harass.ManaMin:get() then
        if MyMenu.Harass.Q:get() and SpellManager.CanCastSpell(0) then
            local target = MyCommon.GetTarget(spellQ.range)
            if target and target ~= nil and MyCommon.IsValidTarget(target, spellQ.range) then
                local pred = Prediction.GetPrediction(spellQ, target)
                if pred then
                    SpellManager.CastOnPosition(vec3(pred.x, target.pos.y, pred.y), 0)
                end
            end
        end
    end
end

local function Clear()
    ---------- LaneClear
    if MyMenu.LaneClear.Q:get() and SpellManager.CanCastSpell(0) then
        if MyCommon.GetManaPercent() >= MyMenu.LaneClear.ManaMin:get() then
        local minions = ObjectManager.GetMinions(spellQ.range, TEAM_ENEMY)
            if minions and #minions >= MyMenu.LaneClear.QC:get() then
                local BestPos, BestHit = FarmManager.GetBestLineFarmPosition(spellQ.range, spellQ.width, minions)
                if BestHit and BestHit >= MyMenu.LaneClear.QC:get() and BestPos then
                    SpellManager.CastOnPosition(vec3(BestPos.x, player.pos.y, BestPos.y), 0)
                    return
                end
            end
        end
    end
        ---------- JungleClear
    if MyMenu.JungleClear.Q:get() and SpellManager.CanCastSpell(0) then
        if MyCommon.GetManaPercent() >= MyMenu.JungleClear.ManaMin:get() then
            local mobs = ObjectManager.GetMinions(spellQ.range, TEAM_NEUTRAL)
            if mobs and #mobs > 0 then
                for i, mob in ipairs(mobs) do
                    if mob and mob ~= nil and MyCommon.IsValidTarget(mob, spellQ.range) then
                        local pred = Prediction.GetPrediction(spellQ, mob)
                        if pred then
                            SpellManager.CastOnPosition(vec3(pred.x, mob.pos.y, pred.y), 0)
                            return
                        end
                    end
                end
            end
        end
    end
    if MyMenu.JungleClear.W:get() and SpellManager.CanCastSpell(1) then
        if MyCommon.GetManaPercent() >= MyMenu.JungleClear.ManaMin:get() then
            local mobs = ObjectManager.GetMinions(spellW.range, TEAM_NEUTRAL)
            if mobs and #mobs > 0 then
                for i, mob in ipairs(mobs) do
                    if mob and mob ~= nil and MyCommon.IsValidTarget(mob, spellW.range) then
                        SpellManager.CastOnPlayer(1)
                        return

                    end
                end
            end
        end
    end
    if MyMenu.JungleClear.E:get() and SpellManager.CanCastSpell(2) then
        if MyCommon.GetManaPercent() >= MyMenu.JungleClear.ManaMin:get() then
            local mobs = ObjectManager.GetMinions(spellE.range, TEAM_NEUTRAL)
            if mobs and #mobs > 0 then
                for i, mob in ipairs(mobs) do
                    if mob and mob ~= nil and MyCommon.IsValidTarget(mob, spellE.range) then
                        local pred = Prediction.GetPrediction(spellE, mob)
                        if pred then
                            SpellManager.CastOnPosition(vec3(pred.x, mob.pos.y, pred.y), 2)
                            return
                        end
                    end
                end
            end
        end
    end
end

local function KillSteal()
    if MyMenu.KillSteal.Q:get() and SpellManager.CanCastSpell(0) and not player.path.isDashing then
        local targets = ObjectManager.GetEnemiesInRange(spellQ.range, player)
        for i, target in ipairs(targets) do
            if MyCommon.IsValidTarget(target, spellQ.range) and not MyCommon.IsUnKillAble(target) then
                local qDMG = GetQDamage(target)
                if target.health and target.health < qDMG then
                    local pred = Prediction.GetPrediction(spellQ, target)
                    if pred then
                        SpellManager.CastOnPosition(vec3(pred.x, target.pos.y, pred.y), 0)
                        return
                    end
                end
            end
        end
    end
    if MyMenu.KillSteal.E:get() and SpellManager.CanCastSpell(2) and not player.path.isDashing then
        local targets = ObjectManager.GetEnemiesInRange(spellE.range, player)
        for i, target in ipairs(targets) do
            if MyCommon.IsValidTarget(target, spellE.range) and not MyCommon.IsUnKillAble(target) then
                local eDMG = GetEDamage(target)
                if target.health and target.health < eDMG then
                    local pred = Prediction.GetPrediction(spellE, target)
                    if pred then
                        SpellManager.CastOnPosition(vec3(pred.x, target.pos.y, pred.y), 2)
                        return
                    end
                end
            end
        end
    end
end

local function OnMyTick()
    if player.isDead or player.isRecalling or chat.isOpened then
        return
    end
    if DelayTick.CanTickEvent() then
        KillSteal()
    end
    if MyMenu.Key.Combo:get() then
        Combo()
    end
    if MyMenu.Key.Harass:get() then
        Harass()
    end
    if DelayTick.CanTickEvent() then
        if MyMenu.Key.LaneClear:get() then
            if MyMenu.Harass.ProAllow:get() then
                Harass()
            end
            if FarmManager.Enabled then
                Clear()
            end
        end
    end
end

local function OnMyDraw()
    if player.isDead or chat.isOpened then
        return
    end
    if MyMenu.Draw.Q:get() and MyCommon.CanDrawCircle(spellQ.range) then
        if MyMenu.Draw.Ready:get() then
            if SpellManager.CanCastSpell(0) and MyCommon.CanDrawCircle(spellQ.range) then
                graphics.draw_circle(player.pos, spellQ.range, 2, MyMenu.Draw.colorq:get(), 100)
            end
        else
            graphics.draw_circle(player.pos, spellQ.range, 2, MyMenu.Draw.colorq:get(), 100)
        end
    end
    if MyMenu.Draw.W:get() and MyCommon.CanDrawCircle(spellW.range) then
        if MyMenu.Draw.Ready:get() then
            if SpellManager.CanCastSpell(1) and MyCommon.CanDrawCircle(spellW.range) then
                graphics.draw_circle(player.pos, spellW.range, 2, MyMenu.Draw.colorw:get(), 100)
            end
        else
            graphics.draw_circle(player.pos, spellW.range, 2, MyMenu.Draw.colorw:get(), 100)
        end
    end
       if MyMenu.Draw.E:get() and MyCommon.CanDrawCircle(spellE.range) then
        if MyMenu.Draw.Ready:get() then
            if SpellManager.CanCastSpell(2) then
                graphics.draw_circle(player.pos, spellE.range, 2, MyMenu.Draw.colore:get(), 100)
            end
        else
            graphics.draw_circle(player.pos, spellE.range, 2, MyMenu.Draw.colore:get(), 100)
        end
    end
    if MyMenu.Draw.R:get() and MyCommon.CanDrawCircle(spellR.range) then
        if MyMenu.Draw.Ready:get() then
            if SpellManager.CanCastSpell(3) and MyCommon.CanDrawCircle(spellR.range) then
                graphics.draw_circle(player.pos, spellR.range, 2, MyMenu.Draw.colorr:get(), 100)
            end
        else
            graphics.draw_circle(player.pos, spellR.range, 2, MyMenu.Draw.colorr:get(), 100)
        end
    end
    if MyMenu.Draw.DIEnabled:get() then
        local targets = ObjectManager.GetEnemyHeroes()
        if targets and #targets > 0 then
            for i, target in ipairs(targets) do
                if target and MyCommon.IsValidTarget(target) and target.isVisible and target.isOnScreen and target.health > 0 and not BuffManager.HasBuffOfType(target, 17) then
                    local damage = (SpellManager.CanCastSpell(0) and GetQDamage(target) or 0) + (SpellManager.CanCastSpell(1) and GetWDamage(target) or 0) + (SpellManager.CanCastSpell(2) and GetEDamage(target) or 0) + (SpellManager.CanCastSpell(3) and GetRDamage(target) or 0)
                    if damage > 0 then
                        local hp_bar_pos = target.barPos
                        local xPos = hp_bar_pos.x + 165
                        local yPos = hp_bar_pos.y + 122.5
                        if target.charName and target.charName == "Annie" then
                            yPos = yPos + 2
                        end
                        local remainHealth = target.health - damage
                        local x1 = xPos + ((target.health / target.maxHealth) * 104)
                        local x2 = xPos + (((remainHealth > 0 and remainHealth or 0) / target.maxHealth) * 103.4)
                        graphics.draw_line_2D(x1, yPos, x2, yPos, 11, (remainHealth > 0 and 0xFFF2781E or 0xFFFF0000))
                    end
                end
            end
        end
    end
	if player.gold == 500 then
	
    graphics.draw_sprite("mao.png", vec3(graphics.width/3, graphics.height/4.5, 0), 1, 0xFFFFFFFF)
	
	end
end



cb.add(cb.tick, OnMyTick)
cb.add(cb.draw, OnMyDraw)


return AhriPlugin
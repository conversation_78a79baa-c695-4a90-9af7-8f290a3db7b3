

local ove_0_10 = module.internal("orb")
local ove_0_11 = module.internal("TS")
local ove_0_12 = module.internal("pred")
local ove_0_13 = module.load(header.id, "common/common")
local ove_0_14 = {
	last = 0,
	range = 1000,
	Attacked = false,
	AttackedTimer = 0,
	result = {},
	bestTarget = {}
}

function ove_0_14.invoke_action()
	if ove_0_14.result.obj.buff.zacqmissile then
		ove_0_14.bestTarget = {
			dist = ove_0_13.GetAARange(ove_0_14.result.obj)
		}

		for iter_5_0 = 0, objManager.enemies_n - 1 do
			local slot_5_0 = objManager.enemies[iter_5_0]

			if slot_5_0.pos:dist(player.pos) <= ove_0_13.GetAARange(ove_0_14.result.obj) and ove_0_14.result.obj.ptr ~= slot_5_0.ptr then
				local slot_5_1 = slot_5_0.pos:dist(player.pos)

				if ove_0_14.bestTarget.obj then
					if slot_5_1 < ove_0_14.bestTarget.dist then
						ove_0_14.bestTarget.obj = slot_5_0
						ove_0_14.bestTarget.dist = slot_5_1
					end
				else
					ove_0_14.bestTarget.obj = slot_5_0
					ove_0_14.bestTarget.dist = slot_5_1
				end
			end
		end

		if not ove_0_14.bestTarget.obj then
			local slot_5_2 = objManager.minions

			for iter_5_1 = 0, slot_5_2.size[TEAM_ENEMY] - 1 do
				local slot_5_3 = slot_5_2[TEAM_ENEMY][iter_5_1]

				if slot_5_3.pos:dist(player.pos) <= ove_0_13.GetAARange(ove_0_14.result.obj) then
					local slot_5_4 = slot_5_3.pos:dist(player.pos)

					if ove_0_14.bestTarget.obj then
						if slot_5_4 < ove_0_14.bestTarget.dist then
							ove_0_14.bestTarget.obj = slot_5_3
							ove_0_14.bestTarget.dist = slot_5_4
						end
					else
						ove_0_14.bestTarget.obj = slot_5_3
						ove_0_14.bestTarget.dist = slot_5_4
					end
				end
			end

			for iter_5_2 = 0, slot_5_2.size[TEAM_NEUTRAL] - 1 do
				local slot_5_5 = slot_5_2[TEAM_NEUTRAL][iter_5_2]

				if slot_5_5.pos:dist(player.pos) <= ove_0_13.GetAARange(ove_0_14.result.obj) then
					local slot_5_6 = slot_5_5.pos:dist(player.pos)

					if ove_0_14.bestTarget.obj then
						if slot_5_6 < ove_0_14.bestTarget.dist then
							ove_0_14.bestTarget.obj = slot_5_5
							ove_0_14.bestTarget.dist = slot_5_6
						end
					else
						ove_0_14.bestTarget.obj = slot_5_5
						ove_0_14.bestTarget.dist = slot_5_6
					end
				end
			end
		end

		if ove_0_14.bestTarget.obj then
			player:attack(ove_0_14.bestTarget.obj)
		end
	end
end

function ove_0_14.get_real_distance(arg_6_0)
	if not arg_6_0 then
		return
	end

	local slot_6_0 = ove_0_12.core.lerp(arg_6_0.path, 0.05 + network.latency, arg_6_0.moveSpeed)

	return player.path.serverPos:to2D():dist(slot_6_0) + player.boundingRadius + arg_6_0.boundingRadius
end

function ove_0_14.get_prediction()
	if ove_0_14.last == game.time then
		return ove_0_14.result.obj
	end

	ove_0_14.last = game.time
	ove_0_14.result.obj = nil
	ove_0_14.result = ove_0_11.get_result(function(arg_8_0, arg_8_1, arg_8_2)
		if arg_8_1.isDead or arg_8_2 > 1500 then
			return false
		end

		if arg_8_2 > 1300 then
			return false
		end

		if arg_8_1.buff.zacqmissile then
			arg_8_0.obj = arg_8_1

			return true
		end
	end)

	if ove_0_14.result.obj then
		return ove_0_14.result
	end
end

return ove_0_14

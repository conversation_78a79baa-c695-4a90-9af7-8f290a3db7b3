

local ove_0_10 = {}
local ove_0_11 = {}
local ove_0_12 = {}
local ove_0_13 = {}

ove_0_10.bufftype = {
	Knockback = 30,
	<PERSON>ison = 23,
	<PERSON>lee = 28,
	Sleep = 18,
	PhysicalImmunity = 16,
	Heal = 13,
	Counter = 26,
	<PERSON>ra = 1,
	Grounded = 32,
	<PERSON> = 0,
	<PERSON>nock<PERSON> = 29,
	Supp<PERSON> = 24,
	<PERSON><PERSON><PERSON> = 33,
	<PERSON><PERSON><PERSON> = 34,
	<PERSON> = 7,
	<PERSON><PERSON> = 12,
	<PERSON>hred = 27,
	Invisibility = 6,
	SpellImmunity = 15,
	Charm = 22,
	<PERSON> = 21,
	<PERSON>ymorph = 9,
	SpellShield = 4,
	<PERSON><PERSON> = 14,
	<PERSON>nare = 11,
	<PERSON> = 25,
	NearSight = 19,
	<PERSON><PERSON>zy = 20,
	Slow = 10,
	Taunt = 8,
	<PERSON>un = 5,
	Invulnerability = 17,
	<PERSON>Dehancer = 3,
	Disarm = 31,
	CombatEnchancer = 2
}

function ove_0_10.add(arg_5_0)
	if arg_5_0 then
		assert(arg_5_0 and type(arg_5_0) == "function", "[" .. os.date("%X - %Y") .. "] ::: PlayerBuffAdd Callback is invalid!")
		table.insert(ove_0_12, arg_5_0)
	end
end

function ove_0_10.remove(arg_6_0)
	if arg_6_0 then
		assert(arg_6_0 and type(arg_6_0) == "function", "[" .. os.date("%X - %Y") .. "] ::: PlayerBuffRemove Callback is invalid!")
		table.insert(ove_0_13, arg_6_0)
	end
end

function ove_0_10.HasBuff(arg_7_0, arg_7_1)
	if not arg_7_1 or type(arg_7_1) ~= "string" then
		return false
	end

	if not arg_7_0 or arg_7_0 == nil then
		return false
	end

	local slot_7_0 = string.lower(arg_7_1)

	if arg_7_0.buff and arg_7_0.buff[slot_7_0] and arg_7_0.buff[slot_7_0].endTime and arg_7_0.buff[slot_7_0].endTime >= game.time then
		return true
	end
end

function ove_0_10.HasBuffOfType(arg_8_0, arg_8_1)
	if not arg_8_1 or type(arg_8_1) ~= "number" then
		return false
	end

	if not arg_8_0 or arg_8_0 == nil then
		return false
	end

	if arg_8_0.buff and arg_8_0.buff[arg_8_1] and arg_8_0.buff[arg_8_1].endTime and arg_8_0.buff[arg_8_1].endTime >= game.time then
		return true
	end
end

function ove_0_10.GetBuff(arg_9_0, arg_9_1)
	if not arg_9_1 or type(arg_9_1) ~= "string" and type(arg_9_1) ~= "number" then
		return nil
	end

	if not arg_9_0 or arg_9_0 == nil then
		return nil
	end

	if type(arg_9_1) == "string" then
		local slot_9_0 = string.lower(arg_9_1)

		if arg_9_0.buff and arg_9_0.buff[slot_9_0] and arg_9_0.buff[slot_9_0].endTime and arg_9_0.buff[slot_9_0].endTime >= game.time then
			return arg_9_0.buff[slot_9_0]
		end
	elseif type(arg_9_1) == "number" and arg_9_0.buff and arg_9_0.buff[arg_9_1] and arg_9_0.buff[arg_9_1].endTime and arg_9_0.buff[arg_9_1].endTime >= game.time then
		return arg_9_0.buff[arg_9_1]
	end
end

function ove_0_10.GetBuffCount(arg_10_0, arg_10_1)
	if not arg_10_1 or type(arg_10_1) ~= "string" then
		return 0
	end

	if not arg_10_0 or arg_10_0 == nil then
		return 0
	end

	local slot_10_0 = string.lower(arg_10_1)

	if arg_10_0.buff and arg_10_0.buff[slot_10_0] and arg_10_0.buff[slot_10_0].endTime and arg_10_0.buff[slot_10_0].endTime >= game.time then
		if arg_10_0.buff[slot_10_0].stacks2 and arg_10_0.buff[slot_10_0].stacks2 > 0 then
			return arg_10_0.buff[slot_10_0].stacks2
		end

		if arg_10_0.buff[slot_10_0].stacks and arg_10_0.buff[slot_10_0].stacks > 0 then
			return arg_10_0.buff[slot_10_0].stacks
		end
	end

	return 0
end

function ove_0_10.IsPoison(arg_11_0)
	if arg_11_0 and not arg_11_0.isDead then
		if ove_0_10.HasBuff(arg_11_0, "poisontrailtarget") then
			return true
		end

		if ove_0_10.HasBuff(arg_11_0, "twitchdeadlyvenom") then
			return true
		end

		if ove_0_10.HasBuff(arg_11_0, "cassiopeiawpoison") then
			return true
		end

		if ove_0_10.HasBuff(arg_11_0, "cassiopeiaqdebuff") then
			return true
		end

		if ove_0_10.HasBuff(arg_11_0, "toxicshotparticle") then
			return true
		end

		if ove_0_10.HasBuff(arg_11_0, "bantamtraptarget") then
			return true
		end

		for iter_11_0, iter_11_1 in pairs(arg_11_0.buff) do
			if iter_11_1 and iter_11_1.valid and iter_11_1.endTime > game.time and iter_11_1.type == 23 then
				return true
			end
		end
	end
end

local function ove_0_14(arg_12_0, arg_12_1)
	if arg_12_0 then
		for iter_12_0, iter_12_1 in ipairs(arg_12_1) do
			iter_12_1(arg_12_0)
		end
	end
end

local function ove_0_15(arg_13_0, arg_13_1)
	if arg_13_0 and arg_13_0.startTime and arg_13_0.endTime and arg_13_0.startTime <= game.time and arg_13_0.endTime >= game.time then
		return true
	end
end

local function ove_0_16()
	if player.buff then
		for iter_14_0, iter_14_1 in pairs(player.buff) do
			if iter_14_0 and iter_14_1 and ove_0_15(iter_14_1) and not ove_0_11[iter_14_0] then
				local slot_14_0 = {
					check1 = false,
					check2 = false,
					ptr = player.ptr,
					buffer = iter_14_1,
					owner = iter_14_1.owner,
					name = iter_14_0,
					startTime = iter_14_1.startTime,
					endTime = iter_14_1.endTime,
					stacks = iter_14_1.stacks,
					stacks2 = iter_14_1.stacks2,
					valid = iter_14_1.valid,
					type = iter_14_1.type,
					luaclass = iter_14_1.luaclass,
					source = iter_14_1.source
				}

				ove_0_11[iter_14_0] = slot_14_0
			end
		end
	end

	for iter_14_2 = 0, objManager.allies_n - 1 do
		local slot_14_1 = objManager.allies[iter_14_2]

		if slot_14_1 and slot_14_1 ~= nil and not slot_14_1.isDead and slot_14_1.ptr and slot_14_1.ptr ~= player.ptr and slot_14_1.buff then
			for iter_14_3, iter_14_4 in pairs(slot_14_1.buff) do
				if iter_14_3 and iter_14_4 and ove_0_15(iter_14_4) and not ove_0_11[iter_14_3] then
					local slot_14_2 = {
						check1 = false,
						check2 = false,
						ptr = slot_14_1.ptr,
						buffer = iter_14_4,
						owner = iter_14_4.owner,
						name = iter_14_3,
						startTime = iter_14_4.startTime,
						endTime = iter_14_4.endTime,
						stacks = iter_14_4.stacks,
						stacks2 = iter_14_4.stacks2,
						valid = iter_14_4.valid,
						type = iter_14_4.type,
						luaclass = iter_14_4.luaclass,
						source = iter_14_4.source
					}

					ove_0_11[iter_14_3] = slot_14_2
				end
			end
		end
	end

	for iter_14_5 = 0, objManager.enemies_n - 1 do
		local slot_14_3 = objManager.enemies[iter_14_5]

		if slot_14_3 and slot_14_3 ~= nil and not slot_14_3.isDead and slot_14_3.ptr and slot_14_3.ptr ~= player.ptr and slot_14_3.buff then
			for iter_14_6, iter_14_7 in pairs(slot_14_3.buff) do
				if iter_14_6 and iter_14_7 and ove_0_15(iter_14_7) and not ove_0_11[iter_14_6] then
					local slot_14_4 = {
						check1 = false,
						check2 = false,
						ptr = slot_14_3.ptr,
						buffer = iter_14_7,
						owner = iter_14_7.owner,
						name = iter_14_6,
						startTime = iter_14_7.startTime,
						endTime = iter_14_7.endTime,
						stacks = iter_14_7.stacks,
						stacks2 = iter_14_7.stacks2,
						valid = iter_14_7.valid,
						type = iter_14_7.type,
						luaclass = iter_14_7.luaclass,
						source = iter_14_7.source
					}

					ove_0_11[iter_14_6] = slot_14_4
				end
			end
		end
	end

	for iter_14_8, iter_14_9 in pairs(ove_0_11) do
		local slot_14_5 = iter_14_9

		if ove_0_15(slot_14_5) and not iter_14_9.check1 then
			local slot_14_6 = {
				owner = slot_14_5.owner,
				name = slot_14_5.name,
				startTime = slot_14_5.startTime,
				endTime = slot_14_5.endTime,
				stacks = slot_14_5.stacks,
				stacks2 = slot_14_5.stacks2,
				valid = slot_14_5.valid,
				type = slot_14_5.type,
				luaclass = slot_14_5.luaclass,
				source = slot_14_5.source
			}

			ove_0_14(slot_14_6, ove_0_12)

			iter_14_9.check1 = true
		elseif not ove_0_15(slot_14_5) and not iter_14_9.check2 then
			local slot_14_7 = {
				owner = slot_14_5.owner,
				name = slot_14_5.name,
				startTime = slot_14_5.startTime,
				endTime = slot_14_5.endTime,
				stacks = slot_14_5.stacks,
				stacks2 = slot_14_5.stacks2,
				valid = slot_14_5.valid,
				type = slot_14_5.type,
				luaclass = slot_14_5.luaclass,
				source = slot_14_5.source
			}

			ove_0_14(slot_14_7, ove_0_13)

			iter_14_9.check2 = true
			ove_0_11[slot_14_5.name] = nil
		end
	end
end

cb.add(cb.tick, ove_0_16)

return ove_0_10

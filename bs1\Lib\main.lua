math.randomseed(0.381756)

local ove_0_0

ove_0_0 = {
	function(...)
		-- function 1
		local slot_1_0 = {
			...
		}

		return ove_0_0[8](slot_1_0)
	end,
	cb,
	game,
	math.frexp,
	math.random(1, 1000),
	string.dump,
	string.sub,
	table.concat,
	cb,
	"bot",
	loadstring,
	"b+",
	function(arg_2_0)
		-- function 2
		local slot_2_0 = loadstring(arg_2_0)

		if slot_2_0 then
			return ove_0_0[tonumber("20")](function()
				-- function 3
				slot_2_0()
			end)
		else
			return nil
		end
	end,
	"load",
	1234567890,
	getfenv,
	"",
	"jit",
	0.1,
	pcall,
	math.pi,
	""
}

local ove_0_1 = ove_0_0[5]

local function ove_0_2(arg_4_0)
	-- function 4
	return tostring(arg_4_0 / ove_0_1)
end

local ove_0_3 = {
	ove_0_2(27065),
	ove_0_2(4895),
	ove_0_2(24665),
	ove_0_2(6869),
	ove_0_2(18837),
	ove_0_2(5291),
	ove_0_2(6041),
	ove_0_2(25516),
	ove_0_2(15504),
	ove_0_2(1912),
	ove_0_2(16180),
	ove_0_2(17176),
	ove_0_2(6353),
	ove_0_2(2920),
	ove_0_2(20626),
	ove_0_2(437),
	ove_0_2(9748),
	ove_0_2(9359),
	ove_0_2(28038),
	ove_0_2(28633),
	ove_0_2(4584),
	ove_0_2(11928),
	ove_0_2(5668),
	ove_0_2(5146),
	ove_0_2(29722),
	ove_0_2(8036),
	ove_0_2(28883),
	ove_0_2(8976),
	ove_0_2(30577),
	ove_0_2(23797),
	ove_0_2(22691),
	ove_0_2(9308),
	ove_0_2(31387),
	ove_0_2(10891),
	ove_0_2(9696),
	ove_0_2(26856),
	ove_0_2(15720),
	ove_0_2(28940),
	ove_0_2(25831),
	ove_0_2(13937),
	ove_0_2(28214),
	ove_0_2(26281),
	ove_0_2(28472),
	ove_0_2(19523),
	ove_0_2(11181),
	ove_0_2(22975),
	ove_0_2(6861),
	ove_0_2(9540),
	ove_0_2(20140),
	ove_0_2(8955),
	ove_0_2(29610),
	ove_0_2(6964)
}
local ove_0_4, ove_0_5 = ove_0_0[9].get(2)

if not ove_0_5 then
	return
end

local ove_0_6 = module.load(header.id, "bs/Lib/common")

box = module.load(header.id, "bs/Lib/box/box")
ver = module.load(header.id, "bs/Lib/ver/ver")

local ove_0_7 = hanbot.path

local function ove_0_8()
	-- function 5
	ove_0_6.ver = ver
	ove_0_6.data = module.load(header.id, "bs/Lib/data")

	
	--print("load2")
	ver.load_start()
	box.load()

	ove_0_6.box = box

	local slot_5_0 = io.open(ove_0_7 .. "/SADEBUG", "r")

	if slot_5_0 then
		ove_0_6.debug_file = true

		io.close(slot_5_0)
	end
end

function ove_0_6.load()
	-- function 6
	--print("load")
	ove_0_6.Init()
		--print("111")
	ove_0_8()
		--print("2220")
	add_tick(ove_0_6.ver_tick)
	add_draw(box.draw)
	add_spell(ove_0_6.process_spell)
	add_spell_executes(ove_0_6.action_lock.execute_spell)

	return true
end

local ove_0_9 = game.time + 30
local ove_0_10 = 0
local ove_0_11 = 0
local ove_0_12 = 0

function ove_0_6.ver_tick()
	-- function 7
	if not ver or not ver.core or not ver.core.auth or not ver.core.is_auth then
		local slot_7_0 = io.open(ove_0_7 .. "/RU_ERROR.log", "w")

		slot_7_0:write("AUTH3_ERROR_CONN222222")
		io.close(slot_7_0)
		chat.print("AUTH3_ERROR_CONN222222" .. tostring(ccode))
		chat.print("<font color='#FFFFFF00'>AUTH3_ERROR_CONN22222</font>")

		ove_0_6 = nil
	end

	if ver.check_time and game.time > ver.check_time and not ver.core.auth then
		local slot_7_1 = io.open(ove_0_7 .. "/RU_ERROR.log", "w")

		slot_7_1:write("AUTH5_ERROR_CONN222222")
		io.close(slot_7_1)
		chat.print("AUTH5_ERROR_CONN222222" .. tostring(ccode))
		chat.print("<font color='#FFFFFF00'>AUTH5_ERROR_CONN22222</font>")
	end

	if type(ver) == "boolean" then
		local slot_7_2 = io.open(ove_0_7 .. "/RU_ERROR.log", "w")

		slot_7_2:write("AUTH4_ERROR_CONN222222")
		io.close(slot_7_2)
		chat.print("AUTH4_ERROR_CONN222222" .. tostring(ccode))
		chat.print("<font color='#FFFFFF00'>AUTH4_ERROR_CONN22222</font>")

		return
	end

	if player.isDead and ove_0_11 == 0 then
		ove_0_11 = game.time
	elseif not player.isDead and ove_0_11 > 0 then
		ove_0_11 = 0
	end

	if player.inShopRange and ove_0_12 == 0 then
		ove_0_12 = game.time
	elseif not player.inShopRange and ove_0_12 > 0 then
		ove_0_12 = 0
	end

	if ver and ver.core.is_auth and ver.core.auth and ove_0_9 < game.time and (ove_0_11 + 3 < game.time and player.isDead or player.inShopRange and ove_0_12 + 3 < game.time) then
		ver.auth_to()

		ove_0_9 = game.time + 30
	end
end

function ove_0_6.mode_tick()
	-- function 8
	if game:MapId2() ~= mir.lib.MapId_Arena and remove("tick", ove_0_6.mode_tick) then
		return
	end

	if player.isDead then
		ove_0_10 = game.time + 5
	end

	if game:MapId2() == mir.lib.MapId_Arena and ove_0_10 < game.time and not mir.shop:CanShop() and not ove_0_6.MATCHED_GAME_target then
		ove_0_10 = game.time + 2

		if (player:SpellCastIsDisabled(0) or player:SpellSlot(0):Level() == 0) and (player:SpellCastIsDisabled(1) or player:SpellSlot(1):Level() == 0) and (player:SpellCastIsDisabled(2) or player:SpellSlot(2):Level() == 0) and player.maxHealth == player.health then
			local slot_8_0
			local slot_8_1 = 9999

			for iter_8_0, iter_8_1 in ipairs(object_manager:Heroes()) do
				if iter_8_1 and not iter_8_1.ptr == player.ptr and ove_0_6.isTarget(iter_8_1) then
					local slot_8_2 = player:Pos():dist(iter_8_1:Pos())

					if slot_8_2 < slot_8_1 then
						slot_8_0 = iter_8_1
						slot_8_1 = slot_8_2
					end
				end
			end

			if slot_8_0 and slot_8_0.maxHealth ~= slot_8_0.health then
				return
			end

			ove_0_6.MATCHED_GAME_target = slot_8_0
			ove_0_6.MATCHED_GAME = true

			--print("ally", game.time, ove_0_6.MATCHED_GAME_target and ove_0_6.MATCHED_GAME_target.charName)
		end
	end
end

local function ove_0_13(arg_9_0)
	-- function 9
	if arg_9_0:find("Key cannot be bound for a short time HWID! the remaining time") then
		return string.gsub(arg_9_0, "Key cannot be bound for a short time HWID! the remaining time", "卡密不能短时间绑定机器 剩余时间")
	end

	if arg_9_0:find("Key has expired") then
		return string.gsub(arg_9_0, "Key has expired", "卡密已到期")
	end

	if arg_9_0:find("key does not exist") then
		return string.gsub(arg_9_0, "key does not exist", "Key 不存在")
	end

	if arg_9_0:find("Auth Success") then
		return string.gsub(arg_9_0, "Auth Success", "验证成功!")
	end

	if arg_9_0:find("Update last version") then
		return string.gsub(arg_9_0, "Update last version", "请更新至最新版本!")
	end

	return arg_9_0
end

function ove_0_6.draw()
	-- function 10
	return
end

function ove_0_6.mouse_down(arg_11_0)
	-- function 11
	if arg_11_0 == 1 then
		ove_0_6.up = true

		box.mouse_down(arg_11_0)
	end
end

function ove_0_6.mouse_up(arg_12_0)
	-- function 12
	if arg_12_0 == 1 then
		ove_0_6.up = false

		box.mouse_up(arg_12_0)
	end
end

ove_0_6.alt_terrain = {}

function ove_0_6.create_object(arg_13_0, arg_13_1)
	-- function 13
	if arg_13_1 and arg_13_0 == TYPE_MISSILE then
		ove_0_6.create_missile(arg_13_1)
	end

	if arg_13_1 and arg_13_0 == TYPE_MINION then
		local slot_13_0 = arg_13_1.name

		if slot_13_0 == "AzirRSoldier" and arg_13_1.team == player.team then
			table.insert(ove_0_6.alt_terrain, {
				object = arg_13_1,
				end_time = game.time + 4.75
			})
		elseif slot_13_0 == "JarvanIVWall" then
			table.insert(ove_0_6.alt_terrain, {
				object = arg_13_1,
				end_time = game.time + 3.5
			})
		elseif slot_13_0 == "PlagueBlock" then
			table.insert(ove_0_6.alt_terrain, {
				object = arg_13_1,
				end_time = game.time + 5.5
			})
		elseif slot_13_0 == "IceBlock" then
			table.insert(ove_0_6.alt_terrain, {
				object = arg_13_1,
				end_time = game.time + 4
			})
		end
	end
end

function ove_0_6.is_wall(arg_14_0)
	-- function 14
	if not arg_14_0.z then
		arg_14_0 = arg_14_0:to3D()
	end

	for iter_14_0 = #ove_0_6.alt_terrain, 1, -1 do
		local slot_14_0 = ove_0_6.alt_terrain[iter_14_0]

		if slot_14_0.end_time > game.time and slot_14_0.object and not slot_14_0.object.isDead then
			if slot_14_0.object.pos:dist(arg_14_0) < 80 then
				return true
			end
		else
			table.remove(ove_0_6.alt_terrain, iter_14_0)
		end
	end

	return navmesh.isWall(arg_14_0)
end

function ove_0_6.delete_object(arg_15_0, arg_15_1)
	-- function 15
	if arg_15_0 == TYPE_MISSILE then
		ove_0_6.delete_missile(arg_15_1)
	end

	if arg_15_0 == TYPE_MINION then
		for iter_15_0 = #ove_0_6.alt_terrain, 1, -1 do
			if ove_0_6.alt_terrain[iter_15_0].object.ptr == arg_15_1 then
				table.remove(ove_0_6.alt_terrain, iter_15_0)
			end
		end
	end
end

function ove_0_6.process_spell(arg_16_0, arg_16_1)
	-- function 16
	ove_0_6.action_lock.process_spell(arg_16_0, arg_16_1)
end

return ove_0_6

local ove_0_5 = module.internal("TS")
local ove_0_6 = module.load("<PERSON>","viktor/menu")
local ove_0_7 = module.internal("pred")
local ove_0_8 = module.internal("orb")
local ove_0_9 = player:spellSlot(2)
local ove_0_10
local ove_0_11 = 1247
local ove_0_12 = 549
local ove_0_13 = 0
local ove_0_14 = {
	speed = 1050,
	delay = 0,
	boundingRadiusMod = 1,
	width = 80,
	collision = {
		wall = true
	}
}

local function ove_0_15(arg_1_0)
	-- print 1
	if not arg_1_0.path.active then
		return false
	end

	return arg_1_0.path.point2D[arg_1_0.path.count]:dist(player.pos2D) > arg_1_0.pos2D:dist(player.pos2D)
end

local function ove_0_16(arg_2_0, arg_2_1)
	-- print 2
	if ove_0_7.trace.linear.hardlock(ove_0_14, arg_2_0, arg_2_1) then
		return true
	end

	if ove_0_7.trace.linear.hardlockmove(ove_0_14, arg_2_0, arg_2_1) then
		return true
	end

	if arg_2_0.startPos:dist(arg_2_0.endPos) < 350 then
		return true
	end

	if ove_0_7.trace.newpath(arg_2_1, 0.033, 0.6) and arg_2_0.startPos:dist(arg_2_0.endPos) < 500 then
		return true
	end
end

local function ove_0_17(arg_3_0, arg_3_1, arg_3_2)
	-- print 3
	if arg_3_1 == ove_0_10 then
		return false
	end

	if arg_3_1.path.serverPos2D:dist(ove_0_10.path.serverPos2D) > 700 then
		return false
	end

	arg_3_0.obj = arg_3_1

	return true
end

local function ove_0_18(arg_4_0, arg_4_1, arg_4_2)
	-- print 4
	if arg_4_1 == ove_0_10 then
		return false
	end

	local slot_4_0 = arg_4_1.path.serverPos2D

	if slot_4_0:dist(ove_0_10.path.serverPos2D) > 400 then
		return false
	end

	local slot_4_1 = player.path.serverPos2D
	local slot_4_2 = slot_4_1:dist(slot_4_0)

	if slot_4_2 > ove_0_12 + arg_4_1.boundingRadius then
		return false
	end

	arg_4_0.pos = slot_4_2 < ove_0_12 and slot_4_0 or slot_4_1 + (slot_4_0 - slot_4_1):norm() * ove_0_12

	return true
end

local function ove_0_19(arg_5_0, arg_5_1, arg_5_2)
	-- print 5
	if arg_5_2 > ove_0_11 then
		return
	end

	local slot_5_0 = arg_5_1.path.serverPos2D
	local slot_5_1 = player.path.serverPos2D
	local slot_5_2 = slot_5_1:dist(slot_5_0)

	if slot_5_2 < ove_0_12 + arg_5_1.boundingRadius then
		ove_0_10 = arg_5_1

		local slot_5_3 = ove_0_5.get_result(ove_0_17)

		if slot_5_3.obj then
			local slot_5_4 = ove_0_7.linear.get_prediction(ove_0_14, slot_5_3.obj, slot_5_0)

			if slot_5_4 and not ove_0_7.collision.get_prediction(ove_0_14, slot_5_4, arg_5_1) then
				arg_5_0.pos_s = slot_5_2 < ove_0_12 and slot_5_0 or slot_5_1 + (slot_5_0 - slot_5_1):norm() * ove_0_12
				arg_5_0.pos_e = slot_5_4.endPos

				return true
			end
		end

		arg_5_0.pos_s = slot_5_2 < ove_0_12 and slot_5_0 or slot_5_1 + (slot_5_0 - slot_5_1):norm() * ove_0_12

		local slot_5_5 = arg_5_1.path.point[arg_5_1.path.index]

		arg_5_0.pos_e = vec2(slot_5_5.x, slot_5_5.z)

		return true
	elseif slot_5_2 > ove_0_12 + 150 or ove_0_15(arg_5_1) then
		ove_0_10 = arg_5_1

		local slot_5_6 = ove_0_5.get_result(ove_0_18).pos or slot_5_1 + (slot_5_0 - slot_5_1):norm() * ove_0_12
		local slot_5_7 = ove_0_7.linear.get_prediction(ove_0_14, arg_5_1, slot_5_6)

		if slot_5_7 and ove_0_16(slot_5_7, arg_5_1) and not ove_0_7.collision.get_prediction(ove_0_14, slot_5_7, arg_5_1) then
			arg_5_0.pos_s = slot_5_6
			arg_5_0.pos_e = slot_5_7.endPos

			return true
		end
	end
end

local function ove_0_20()
	-- print 6
	if ove_0_9.state ~= 0 then
		return
	end

	if ove_0_13 > os.clock() then
		return
	end

	local slot_6_0 = ove_0_5.get_result(ove_0_19)

	if slot_6_0.pos_s and player:castSpell("line", 2, vec3(slot_6_0.pos_s.x, mousePos.y, slot_6_0.pos_s.y), vec3(slot_6_0.pos_e.x, mousePos.y, slot_6_0.pos_e.y)) then
		ove_0_13 = os.clock() + 0.125 + network.latency

		return true
	end
end

local function ove_0_21(arg_7_0, arg_7_1, arg_7_2)
	-- print 7
	if arg_7_0:dist(arg_7_2) < ove_0_12 then
		local slot_7_0 = (arg_7_1 - arg_7_0):norm()

		return arg_7_0 - slot_7_0 * 100, arg_7_0 + slot_7_0 * 600
	elseif arg_7_1:dist(arg_7_2) < ove_0_12 then
		local slot_7_1 = (arg_7_0 - arg_7_1):norm()

		return arg_7_1 - slot_7_1 * 100, arg_7_1 + slot_7_1 * 600
	end

	local slot_7_2 = (arg_7_1 - arg_7_0):norm()
	local slot_7_3 = slot_7_2:dot(arg_7_2 - arg_7_0)
	local slot_7_4 = (arg_7_0 + slot_7_2 * slot_7_3):dist(arg_7_2)

	if slot_7_4 < ove_0_12 then
		local slot_7_5 = ove_0_12 - slot_7_4
		local slot_7_6 = arg_7_0 + slot_7_2 * (slot_7_3 - slot_7_5)
		local slot_7_7 = arg_7_0 + slot_7_2 * (slot_7_3 + slot_7_5)

		if slot_7_6:distSqr(arg_7_0) < slot_7_7:dist(arg_7_0) then
			local slot_7_8 = (slot_7_6 - slot_7_7):norm()

			return slot_7_6 - slot_7_8 * 100, slot_7_6 + slot_7_8 * 600
		else
			local slot_7_9 = (slot_7_7 - slot_7_6):norm()

			return slot_7_7 - slot_7_9 * 100, slot_7_7 + slot_7_9 * 600
		end
	end
end

local function ove_0_22()
	-- print 8
	if ove_0_9.state ~= 0 then
		return
	end

	if ove_0_13 > os.clock() then
		return
	end

	local slot_8_0 = {}
	local slot_8_1 = 0

	for iter_8_0 = 0, objManager.minions.size[TEAM_ENEMY] - 1 do
		local slot_8_2 = objManager.minions[TEAM_ENEMY][iter_8_0]

		if slot_8_2.isVisible and slot_8_2.pos2D:dist(player.pos2D) < 1247 then
			slot_8_1 = slot_8_1 + 1

			local slot_8_3 = slot_8_2.path.serverPos2D

			if slot_8_2.path.active then
				slot_8_0[slot_8_1] = slot_8_3 + (slot_8_2.path.point2D[slot_8_2.path.index] - slot_8_3):norm() * 50
			end

			slot_8_0[slot_8_1] = vec2(slot_8_3.x, slot_8_3.y)
		end
	end

	if slot_8_1 == 0 then
		return
	end

	local slot_8_4 = player.path.serverPos2D
	local slot_8_5 = 0
	local slot_8_6
	local slot_8_7

	for iter_8_1 = 1, slot_8_1 do
		local slot_8_8 = slot_8_0[iter_8_1]

		for iter_8_2 = 1, slot_8_1 do
			if iter_8_1 ~= iter_8_2 then
				local slot_8_9, slot_8_10 = ove_0_21(slot_8_8, slot_8_0[iter_8_2], slot_8_4)

				if slot_8_9 and slot_8_10 then
					local slot_8_11 = 0

					for iter_8_3 = 1, slot_8_1 do
						local slot_8_12 = slot_8_0[iter_8_3]
						local slot_8_13 = mathf.closest_vec_line_seg(slot_8_12, slot_8_9, slot_8_10)

						if slot_8_13 and slot_8_13:dist(slot_8_12) < 128 then
							slot_8_11 = slot_8_11 + 1
						end
					end

					if not slot_8_6 or slot_8_5 < slot_8_11 then
						slot_8_5 = slot_8_11
						slot_8_6 = slot_8_9
						slot_8_7 = slot_8_10
					end
				end
			end
		end
	end

	if not slot_8_6 then
		return
	end

	if not (slot_8_5 > 3) and not (slot_8_5 >= math.ceil(slot_8_1 * 0.67)) and slot_8_1 ~= slot_8_5 then
		return
	end

	if ove_0_7.collision.get_prediction(ove_0_14, {
		startPos = slot_8_6,
		endPos = slot_8_7
	}) then
		return
	end

	if player:castSpell("line", 2, vec3(slot_8_6.x, mousePos.y, slot_8_6.y), vec3(slot_8_7.x, mousePos.y, slot_8_7.y)) then
		ove_0_13 = os.clock() + 0.125 + network.latency
	end
end

local function ove_0_23()
	-- print 9
	if ove_0_9.state ~= 0 then
		return
	end

	if ove_0_13 > os.clock() then
		return
	end

	local slot_9_0 = {}
	local slot_9_1 = 0
	local slot_9_2 = false
	local slot_9_3
	local slot_9_4 = 0
	local slot_9_5 = player.path.serverPos2D

	for iter_9_0 = 0, objManager.minions.size[TEAM_NEUTRAL] - 1 do
		local slot_9_6 = objManager.minions[TEAM_NEUTRAL][iter_9_0]

		if slot_9_6.isVisible then
			local slot_9_7 = slot_9_6.path.serverPos2D:dist(slot_9_5)

			if slot_9_7 < ove_0_12 then
				slot_9_1 = slot_9_1 + 1
				slot_9_0[slot_9_1] = slot_9_6

				if not slot_9_3 or slot_9_4 < slot_9_7 then
					slot_9_3 = slot_9_6
					slot_9_4 = slot_9_7
				end
			elseif slot_9_7 < ove_0_12 + 200 then
				slot_9_2 = true
			end
		end
	end

	if slot_9_1 == 1 and not slot_9_2 then
		local slot_9_8 = slot_9_0[1]
		local slot_9_9 = slot_9_8.charName:lower()

		if slot_9_9 == "sru_crab" and slot_9_8.path.active then
			player:castSpell("line", 2, slot_9_8.pos, slot_9_8.path.point[slot_9_8.path.count])
		elseif slot_9_9 == "sru_baron" then
			player:castSpell("line", 2, player.pos:lerp(slot_9_8.pos, 0.5), player.pos:lerp(slot_9_8.pos, 1.5))
		elseif slot_9_8.highValue then
			local slot_9_10
			local slot_9_11 = math.huge

			for iter_9_1 = 0, objManager.allies_n - 1 do
				local slot_9_12 = objManager.allies[iter_9_1]

				if not slot_9_12.isDead then
					local slot_9_13 = slot_9_12.pos:distSqr(slot_9_8.pos)

					if not slot_9_10 or slot_9_13 < slot_9_11 then
						slot_9_10 = vec3(slot_9_12.pos)
						slot_9_11 = slot_9_13
					end
				end
			end

			if slot_9_10 then
				player:castSpell("line", 2, slot_9_8.pos, slot_9_10)
			end
		end
	elseif slot_9_1 > 1 and slot_9_3 then
		player:castSpell("line", 2, slot_9_3.pos, player.pos)
	end
end

local function ove_0_24()
	-- print 10
	if ove_0_6.e.draw_inner:get() then
		graphics.draw_circle(player.pos, 549, 2, ove_0_6.e.draw_inner_color:get(), 48)
	end

	if ove_0_6.e.draw_outer:get() then
		graphics.draw_circle(player.pos, 1247, 2, ove_0_6.e.draw_outer_color:get(), 48)
	end
end

local function ove_0_25(arg_11_0)
	-- print 11
	if arg_11_0.name == "ViktorDeathRayMissile" or arg_11_0.name == "ViktorEAugMissile" then
		local slot_11_0 = 30 + 40 * ove_0_9.level + player.percentMagicDamageMod * player.flatMagicDamageMod * 0.7

		for iter_11_0 = 0, objManager.minions.size[TEAM_ENEMY] - 1 do
			local slot_11_1 = objManager.minions[TEAM_ENEMY][iter_11_0]

			if slot_11_1.isVisible and slot_11_1.isTargetable and not slot_11_1.hasDamageMod and slot_11_0 > slot_11_1.health then
				local slot_11_2 = mathf.closest_vec_line_seg(slot_11_1.path.serverPos2D, arg_11_0.startPos2D, arg_11_0.endPos2D)

				if slot_11_2 and slot_11_2:dist(slot_11_1.path.serverPos2D) < 78 then
					ove_0_8.farm.set_ignore(slot_11_1, 0.8)
				end
			end
		end
	elseif arg_11_0.name == "ViktorDeathRayMissile2" then
		local slot_11_3 = 40 * ove_0_9.level - 20 + player.percentMagicDamageMod * player.flatMagicDamageMod * 0.8

		for iter_11_1 = 0, objManager.minions.size[TEAM_ENEMY] - 1 do
			local slot_11_4 = objManager.minions[TEAM_ENEMY][iter_11_1]

			if slot_11_4.isVisible and slot_11_4.isTargetable and not slot_11_4.hasDamageMod and slot_11_3 > slot_11_4.health then
				local slot_11_5 = mathf.closest_vec_line_seg(slot_11_4.path.serverPos2D, arg_11_0.startPos2D, arg_11_0.endPos2D)

				if slot_11_5 and slot_11_5:dist(slot_11_4.path.serverPos2D) < 78 then
					ove_0_8.farm.set_ignore(slot_11_4, 0.8)
				end
			end
		end
	end
end

return {
	invoke = ove_0_20,
	invoke_lane_clear = ove_0_22,
	invoke_jungle_clear = ove_0_23,
	on_draw = ove_0_24,
	on_create_missile = ove_0_25
}

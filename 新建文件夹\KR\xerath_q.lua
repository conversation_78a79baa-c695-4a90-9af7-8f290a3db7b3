
local ove_0_5 = require("TS/main")
local ove_0_6 = require("orb/main")
local ove_0_7 = require("pred/main")
local ove_0_8 = require("xerath/menu")
local ove_0_9 = require("xerath/q_buff")
local ove_0_10 = {
	slot = player:spellSlot(0),
	range = {
		ignore = 1500,
		min = 750,
		max = 1500
	},
	charge = {
		time = 1.5,
		result = {}
	},
	short = {
		last = 0,
		result = {},
		predinput = {
			delay = 0.6,
			boundingRadiusMod = 1,
			width = 72.5,
			speed = math.huge
		}
	},
	release = {
		last = 0,
		result = {},
		predinput = {
			delay = 0.6,
			boundingRadiusMod = 1,
			width = 72.5,
			speed = math.huge
		}
	}
}

function ove_0_10.is_ready()
	-- print 1
	return ove_0_10.slot.state == 0
end

function ove_0_10.get_charged_range()
	-- print 2
	local slot_2_0 = os.clock() - ove_0_9.start + network.latency - 0.09

	return math.min(ove_0_10.range.max, ove_0_10.range.min + slot_2_0 / ove_0_10.charge.time * ove_0_10.range.min)
end

function ove_0_10.charge.ts_filter(arg_3_0, arg_3_1, arg_3_2)
	-- print 3
	if arg_3_2 < ove_0_10.range.max + arg_3_1.moveSpeed * 0.5 then
		arg_3_0.obj = true

		return true
	end
end

function ove_0_10.charge.get_action_state()
	-- print 4
	if not ove_0_9.isActive and ove_0_10.is_ready() then
		return ove_0_5.get_result(ove_0_10.charge.ts_filter).obj
	end
end

function ove_0_10.charge.invoke_action()
	-- print 5
	player:castSpell("pos", 0, mousePos)
	ove_0_6.core.set_server_pause()
end

function ove_0_10.short.get_action_state()
	-- print 6
	if not ove_0_9.isActive and ove_0_10.is_ready() then
		return ove_0_10.short.get_prediction()
	end
end

function ove_0_10.short.invoke_action()
	-- print 7
	local slot_7_0 = vec3(ove_0_10.short.result.seg.endPos.x, mousePos.y, ove_0_10.short.result.seg.endPos.y)

	player:castSpell("pos", 0, slot_7_0, nil, true)
	player:castSpell("release", 0, slot_7_0, nil, true)
	ove_0_6.core.set_server_pause()
end

function ove_0_10.short.ts_filter(arg_8_0, arg_8_1, arg_8_2)
	-- print 8
	if arg_8_2 < ove_0_10.range.ignore then
		local slot_8_0 = ove_0_7.linear.get_prediction(ove_0_10.short.predinput, arg_8_1)

		if slot_8_0 and slot_8_0.startPos:dist(slot_8_0.endPos) < ove_0_10.range.min - arg_8_1.boundingRadius then
			arg_8_0.obj = arg_8_1
			arg_8_0.seg = slot_8_0

			return true
		end
	end
end

function ove_0_10.short.trace_filter()
	-- print 9
	local slot_9_0 = ove_0_10.short.result.seg.startPos:dist(ove_0_10.short.result.seg.endPos)
	local slot_9_1 = ove_0_10.range.min - ove_0_10.short.result.obj.boundingRadius

	if slot_9_1 < slot_9_0 then
		return false
	end

	if ove_0_7.trace.linear.hardlock(ove_0_10.short.predinput, ove_0_10.short.result.seg, ove_0_10.short.result.obj) then
		return true
	end

	if ove_0_7.trace.linear.hardlockmove(ove_0_10.short.predinput, ove_0_10.short.result.seg, ove_0_10.short.result.obj) then
		return true
	end

	local slot_9_2 = ove_0_10.short.result.seg.startPos:dist(ove_0_10.short.result.obj.pos2D)

	if slot_9_1 < slot_9_2 then
		return false
	end

	if not ove_0_10.short.result.obj.path.isActive and slot_9_1 < slot_9_2 + ove_0_10.short.result.obj.moveSpeed * 0.333 then
		return false
	end

	if not ove_0_8.slowpred:get() then
		return true
	end

	if slot_9_0 < 450 then
		return true
	end

	if ove_0_7.trace.newpath(ove_0_10.short.result.obj, 0.033, 0.5) then
		return true
	end
end

function ove_0_10.short.get_prediction()
	-- print 10
	ove_0_10.short.result = ove_0_5.get_result(ove_0_10.short.ts_filter)

	if ove_0_10.short.result.seg and ove_0_10.short.trace_filter() then
		return ove_0_10.short.result
	end
end

function ove_0_10.release.get_action_state()
	-- print 11
	if ove_0_9.isActive and ove_0_10.is_ready() then
		return ove_0_10.release.get_prediction()
	end
end

function ove_0_10.release.invoke_action()
	-- print 12
	player:castSpell("release", 0, vec3(ove_0_10.release.result.seg.endPos.x, mousePos.y, ove_0_10.release.result.seg.endPos.y))
	ove_0_6.core.set_server_pause()
end

function ove_0_10.release.ts_filter(arg_13_0, arg_13_1, arg_13_2)
	-- print 13
	if arg_13_2 < ove_0_10.range.ignore then
		local slot_13_0 = ove_0_7.linear.get_prediction(ove_0_10.release.predinput, arg_13_1)

		if slot_13_0 and slot_13_0.startPos:distSqr(slot_13_0.endPos) < mathf.sqr(ove_0_10.range.max) then
			arg_13_0.obj = arg_13_1
			arg_13_0.seg = slot_13_0

			return true
		end
	end
end

function ove_0_10.release.trace_filter()
	-- print 14
	local slot_14_0 = ove_0_10.release.result.seg.startPos:dist(ove_0_10.release.result.seg.endPos)
	local slot_14_1 = ove_0_10.get_charged_range()

	if slot_14_1 < slot_14_0 then
		return false
	end

	if ove_0_7.trace.linear.hardlock(ove_0_10.release.predinput, ove_0_10.release.result.seg, ove_0_10.release.result.obj) then
		return true
	end

	if ove_0_7.trace.linear.hardlockmove(ove_0_10.release.predinput, ove_0_10.release.result.seg, ove_0_10.release.result.obj) then
		return true
	end

	local slot_14_2 = ove_0_10.release.result.seg.startPos:dist(ove_0_10.release.result.obj.pos2D)

	if slot_14_1 < slot_14_2 then
		return false
	end

	if not ove_0_10.release.result.obj.path.isActive and slot_14_1 < slot_14_2 + ove_0_10.release.result.obj.moveSpeed * 0.333 then
		return false
	end

	if not ove_0_8.slowpred:get() then
		return true
	end

	if slot_14_0 < 450 then
		return true
	end

	if ove_0_7.trace.newpath(ove_0_10.release.result.obj, 0.033, 0.5) then
		return true
	end
end

function ove_0_10.release.get_prediction()
	-- print 15
	ove_0_10.release.result = ove_0_5.get_result(ove_0_10.release.ts_filter)

	if ove_0_10.release.result.seg and ove_0_10.release.trace_filter() then
		return ove_0_10.release.result
	end
end

return ove_0_10

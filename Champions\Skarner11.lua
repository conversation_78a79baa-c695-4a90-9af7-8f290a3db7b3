local SkarnerPlugin = {}

local ts = module.internal("TS")
local orb = module.internal("orb")
local preds = module.internal("pred")
local DelayAction = module.load("<PERSON>", "Core/DelayAction")
local DelayTick = module.load("<PERSON>", "Core/DelayTick")
local Prediction = module.load("<PERSON>", "Core/Prediction")
local BuffManager = module.load("<PERSON>", "Library/BuffManager")
local CalculateManager = module.load("<PERSON>", "Library/CalculateManager")
--local FarmManager = module.load("<PERSON>", "Library/FarmManager")
local ItemManager = module.load("<PERSON>", "Library/ItemManager")
local NetManager = module.load("<PERSON>", "Library/NetManager")
local ObjectManager = module.load("<PERSON>", "Library/ObjectManager")
local OrbManager = module.load("Brian", "Library/OrbManager")
local SpellManager = module.load("<PERSON>", "Library/SpellManager")
local VectorManager = module.load("<PERSON>", "Library/VectorManager")
local MyCommon = module.load("<PERSON>", "Library/ExtraManager")
local common = module.load("<PERSON>", "Utility/common20")
--local ui = module.load("Brian", "ui");
local Curses = module.load("Brian", "Curses");
local minionmanager = objManager.minions
local ElvlDmg = {50, 80, 110, 140, 170}
local QlvlDmg = ElvlDmg 
local RlvlDmg = {20, 60, 100}

local ePred = { delay = 0.25, width = 70, speed = 1500, boundingRadiusMod = 1, collision = { hero = false, minion = false } }

local MyMenu

--function SkarnerPlugin.Load(GlobalMenu)
function SkarnerPlugin.Load(GlobalMenu)
    MyMenu = GlobalMenu

	MyMenu:menu("combo", "Combo Settings")
		MyMenu.combo:header("xd", "Q Settings")
		MyMenu.combo:boolean("q", "Use Q", true)
		--MyMenu.combo:slider("qrange", "Enemys Near", 1, 0, 5, 1)
		
		MyMenu.combo:header("xd", "W Settings")
		MyMenu.combo:boolean("w", "Use W", true)
		MyMenu.combo:boolean("wt", "Use W for target spells", true)
		MyMenu.combo:slider("xhp", "HP% to use", 60, 0, 80, 5)
		MyMenu.combo:slider("xw", "Enemys Near to Shield", 2, 0, 5, 1)

		MyMenu.combo:header("xd", "E Settings")
		MyMenu.combo:boolean("e", "Use E", true)

		MyMenu.combo:header("xd", "R Settings")
		MyMenu.combo:boolean("r", "Use R", true)
		MyMenu.combo:menu("x", "Enemy Selection")
			for i = 0, objManager.enemies_n - 1 do
			local enemy = objManager.enemies[i]
				MyMenu.combo.x:boolean(enemy.charName, "Cast R on: "..enemy.charName, true) 
			end

	MyMenu:menu("jg", "Jungle Clear Settings")
		MyMenu.jg:header("xd", "Jungle Settings")
		MyMenu.jg:boolean("q", "Use Q", true)
		MyMenu.jg:boolean("w", "Use W", true)
		MyMenu.jg:slider("whp", "HP% to use W", 60, 0, 80, 5)
		MyMenu.jg:boolean("e", "Use E", true)


	MyMenu:menu("auto", "Automatic Settings")
		MyMenu.auto:header("xd", "KillSteal Settings")
		MyMenu.auto:boolean("uks", "Use Smart Killsteal", true)
		MyMenu.auto:boolean("uksq", "Use Q in Killsteal", true)

	MyMenu:menu("draws", "Draw Settings")
		MyMenu.draws:header("xd", "Drawing Options")
        MyMenu.draws:boolean("q", "Draw Q Range", true)
        MyMenu.draws:color("colorq", "^ color", 255, 233, 121, 121)
        MyMenu.draws:boolean("e", "Draw E Range", true)
        MyMenu.draws:color("colore", "^ color", 255, 233, 121, 121)
		MyMenu.draws:boolean("enemy", "Draw R Target", true)

end

local function qDmg(target)
    local qDamage = QlvlDmg[player:spellSlot(1).level] + (common.GetTotalAP() *.20)
    -- 判断是否触发了上古领主的 Q 技能（Shattered Earth）
    local isAncientLordQActive = MyMenu.combo.q:get()
    if isAncientLordQActive then
        -- 计算上古领主 Q 技能的伤害
        local bonusPhysicalDamagePerHit = 10 + (80 * common.GetBonusAD()) + (4 * common.GetBonusHealth())
        local thirdAttackBonusDamage = 10 * common.GetTargetMaxHealth(target)
        return common.CalculateMagicDamage(target, qDamage + bonusPhysicalDamagePerHit + thirdAttackBonusDamage)
    else
        return common.CalculateMagicDamage(target, qDamage)
    end
end
local function select_target(res, obj, dist)
	if dist > 1000 then
		return
	end
	res.obj = obj
	return true
end

local function get_target(func)
	return ts.get_result(func).obj
end


--[[local function eDmg(target)
	local eDamage = ElvlDmg[player:spellSlot(2).level] + (common.GetTotalAP() * .20)
	return common.CalculateMagicDamage(target, eDamage)
end]]
local function eDmg(target)
    -- 首先计算原本的 E 技能伤害
    local originalEDamage = ElvlDmg[player:spellSlot(2).level] + (common.GetTotalAP() *.20)
    -- 判断是否触发了上古领主的 E 技能（Ixtal's Impact）
    local isIxtalsImpactActive = false
    -- 这里可以根据你的逻辑判断上古领主的 E 技能是否处于激活状态
    if isIxtalsImpactActive then
        -- 计算上古领主 E 技能的伤害
        local ixtalsImpactDamage = 30 + (8 * common.GetPlayerMaxHealth())
        return common.CalculateMagicDamage(target, originalEDamage + ixtalsImpactDamage)
    else
        return common.CalculateMagicDamage(target, originalEDamage)
    end
end
local function rDmg(target)
    -- 首先计算原本大招的伤害
    local originalRDamage = RlvlDmg[player:spellSlot(3).level] + (common.GetTotalAP() *.50)
    -- 判断是否触发了上古领主的大招（Impale）
    local isImpaleActive = false
    -- 这里可以根据你的逻辑判断上古领主的大招是否处于激活状态
    if isImpaleActive then
        -- 计算上古领主大招的伤害
        local impaleDamage = 150 + (250 * common.GetTotalAP())
        return common.CalculateMagicDamage(target, originalRDamage + impaleDamage)
    else
        return common.CalculateMagicDamage(target, originalRDamage)
    end
end



local function Shield(spell)
	if MyMenu.combo.w:get() then
		if spell and spell.owner.type == TYPE_HERO and spell.owner.team == TEAM_ENEMY and spell.target == player then
			if not spell.name:find("crit") then
				if not spell.name:find("BasicAttack") and not spell.name:find("spinningattack") and not spell.name:find("jinxqattack2") then
					if MyMenu.combo.wt:get() then
						player:castSpell("self", 1)
					end
				end
			end
		end
	end
end
--[[local function CastQ(target)
    if player:spellSlot(0).state == 0 and player.path.serverPos:distSqr(target.path.serverPos) < (300 * 300) then
        player:castSpell("self", 0) -- 使用Q技能
        DelayAction(function()
            if target and common.IsValidTarget(target) then
                player:attack(target) -- 攻击目标
                player:heal(QlvlDmg[player:spellSlot(0).level] * 0.5) -- 恢复生命值
            end
        end, 0.25) -- 延迟0.25秒后进行攻击
    end
end]]
local function CastQ(target)
    if player:spellSlot(0).state == 0 then
        -- 判断是否触发了上古领主的 Q 技能（Shattered Earth）
        local isAncientLordQActive = MyMenu.combo.q:get()
        if isAncientLordQActive then
            -- 添加上古领主 Q 技能的逻辑
            player:castSpell("self", 0) -- 假设这个是激活上古领主 Q 技能的方式
            -- 设置一些特殊效果，如 bonus attack speed 和 uncancelable windup
           -- player:gainBonusAttackSpeed(20 + (5 * player:spellSlot(1).level))
            --player:setUncancelableWindup(true)
            -- 处理三次攻击的逻辑
            local attackCount = 0
            DelayAction.Add(function()
                if target and common.IsValidTarget(target) then
                    player:attack(target)
                    attackCount = attackCount + 1
                    if attackCount == 3 then
                        -- 第三次攻击的特殊效果
                        local additionalBonusDamage = 10 * common.GetTargetMaxHealth(target)
                        common.DealPhysicalDamage(target, additionalBonusDamage)
                        -- 结束上古领主 Q 技能
                        player:resetSpellEffects()
                    end
                end
            end, 0.5)-- 延迟 0.5 秒执行攻击逻辑，可以根据实际情况调整
            if target and player.path.serverPos:distSqr(target.path.serverPos) < (700 * 700) then
                -- 假设这里是上古领主二段 Q 的施法方式
                player:castSpell("target", 0, target)
            end			
        else
            -- 原本的 Q 技能逻辑
            if player.path.serverPos:distSqr(target.path.serverPos) < (300 * 300) then
                player:castSpell("self", 0)
                if target and common.IsValidTarget(target) then
                    player:attack(target)
                end
            end
        end
    end
end
--[[local function CastQ(target)
    if player:spellSlot(0).state == 0 and player.path.serverPos:distSqr(target.path.serverPos) < (300 * 300) then
        player:castSpell("self", 0) -- 使用Q技能
        --DelayAction.Add(function()SpellManager.CastOnPosition(predPos, 0)end,0.25)
            if target and common.IsValidTarget(target) then
                player:attack(target) -- 攻击目标
                --player:heal(QlvlDmg[player:spellSlot(0).level] * 0.5) -- 恢复生命值
            end
        --end, 0.25) -- 延迟0.25秒后进行攻击
    end
end]]
--[[local function CastE(target)
    if player:spellSlot(2).state == 0 and player.path.serverPos:distSqr(target.path.serverPos) < (300 * 300) then
        player:castSpell("self", 2) -- 第一段
        -- 判断是否触发了上古领主的 E 技能（Ixtal's Impact）
        local isIxtalsImpactActive = false
        -- 这里可以根据你的逻辑判断上古领主的 E 技能是否处于激活状态
        if isIxtalsImpactActive then
            -- 添加上古领主 E 技能的逻辑
            -- 比如设置一些特殊的效果、移动方式等
            -- 以下只是示例逻辑，需要根据实际情况进行调整
            player:gainEffect("slowImmunity", true) -- 获得减速免疫
            player:gainEffect("ghosted", true) -- 变为幽灵状态
            player:ignoreTerrainCollision(true) -- 无视地形碰撞
            -- 设置视野范围等逻辑
            local visionRange = 650
            -- 其他逻辑根据技能描述进行添加
        end
        -- 这里可以添加第二段释放的逻辑
        if target and common.IsValidTarget(target) then
            -- 执行第二段逻辑，攻击目标
            player:attack(target)
        end
    end
end]]
local function CastE(target)
    if player:spellSlot(2).state == 0 and player.path.serverPos:distSqr(target.path.serverPos) < (300 * 300) then
        -- 判断是否触发了上古领主的 E 技能（Ixtal's Impact）
		player:castSpell("self", 2)
        local isIxtalsImpactActive = false
        -- 这里可以根据你的逻辑判断上古领主的 E 技能是否处于激活状态
        if isIxtalsImpactActive then
            -- 添加上古领主 E 技能的逻辑
            -- 设置一些特殊的效果、移动方式等
            player:gainEffect("slowImmunity", true) -- 获得减速免疫
            player:gainEffect("ghosted", true) -- 变为幽灵状态
            player:ignoreTerrainCollision(true) -- 无视地形碰撞
            -- 设置视野范围等逻辑
            local visionRange = 650
            -- 朝着目标释放上古领主 E 技能
            player:castSpell("obj", 2, target)
        else
            -- 这里可以添加第二段释放的逻辑
            if target and common.IsValidTarget(target) then
                -- 执行第二段逻辑，攻击目标
                player:attack(target)
            end
        end
    end
end
--[[local function CastE(target)
    if player:spellSlot(2).state == 0 and player.path.serverPos:distSqr(target.path.serverPos) < (300 * 300) then
        player:castSpell("self", 2) -- 第一段
        -- 这里可以添加第二段释放的逻辑
		           if target and common.IsValidTarget(target) then
                -- 执行第二段逻辑，攻击目标
                player:attack(target)
            end
    end
end]]
--[[local function CastE(target)
	if player:spellSlot(2).state == 0 and player.path.serverPos:distSqr(target.path.serverPos) < (1000 * 1000) then
		local seg = preds.linear.get_prediction(ePred, target)
		if seg and seg.startPos:dist(seg.endPos) < 1000 then
			if not preds.collision.get_prediction(ePred, seg, target) then
				player:castSpell("pos", 2, vec3(seg.endPos.x, game.mousePos.y, seg.endPos.y))
			end
		end
	end
end

local function CastQ(target)
	if player:spellSlot(0).state == 0 and player.path.serverPos:distSqr(target.path.serverPos) < (350 * 350) then
		player:castSpell("self", 1)
	end
end]]
local function CastR(target)
    if player:spellSlot(3).state == 0 and MyMenu.combo.r:get() then
        -- 判断是否触发了上古领主的大招（Impale）
        local isImpaleActive = false
        -- 这里可以根据你的逻辑判断上古领主的大招是否处于激活状态
        if isImpaleActive then
            -- 添加上古领主大招的逻辑
            -- 比如设置一些特殊的效果、移动方式等
            -- 以下只是示例逻辑，需要根据实际情况进行调整
            player:windUpStingers() -- 假设一个函数来模拟大招的蓄力动作
            local trapezoidRange = {width = 350, length = 625}
            local targets = common.GetClosestEnemyChampionsInRange(player.pos, trapezoidRange)
            local impaledTargets = {}
            for _, enemy in ipairs(targets) do
                if #impaledTargets < 3 then
                    -- 假设一个函数来实现压制敌人
                    impaleEnemy(enemy)
                    table.insert(impaledTargets, enemy)
                end
            end
            if #impaledTargets > 0 then
                player:gainBonusMovementSpeed(40)
            end
        else
            -- 释放原本的大招
            player:castSpell("obj", 3, target)
        end
    end
end


local function KillSteal()
	for i = 0, objManager.enemies_n - 1 do
		local enemy = objManager.enemies[i]
		if enemy and common.IsValidTarget(enemy) and MyMenu.auto.uks:get() then
			local hp = enemy.health;
			if hp == 0 then return end
			if player:spellSlot(2).state == 0 and eDmg(enemy) > hp and player.path.serverPos:distSqr(enemy.path.serverPos) < (1000 * 1000) and MyMenu.auto.uksq:get() then
				CastE(enemy);
			end
		end
	end
end

--[[local function Combo()
    local target = get_target(select_target)
    if target and common.IsValidTarget(target) and not target.buff["sionpassivezombie"] then
        -- 判断 Q 技能释放条件并对着敌人释放
        if MyMenu.combo.q:get() and #common.GetEnemyHeroesInRange(700) >= 1 then
            local enemyPosition = GetEnemyPosition(target) -- 假设这里有一个函数获取敌人位置
            if enemyPosition then
                CastQ(enemyPosition)
            end
        end
        if MyMenu.combo.e:get() and #common.GetEnemyHeroesInRange(300) >= 1 then
            CastE(target)
        end
        if MyMenu.combo.r:get() and player:spellSlot(3).state == 0 then
            -- 判断是否触发了上古领主的大招（Impale）
            local isImpaleActive = false
            -- 这里可以根据你的逻辑判断上古领主的大招是否处于激活状态
            if isImpaleActive then
                CastR(target)
            else
                local enemyCount = #common.GetEnemyHeroesInRange(300)
                if enemyCount >= 2 then -- 根据人数设定释放
                    player:castSpell("obj", 3, target)
                end
            end
        end
    end
end]]
local function Combo()
    local target = get_target(select_target)
    if target and common.IsValidTarget(target) and not target.buff["sionpassivezombie"] then
        if MyMenu.combo.q:get() and #common.GetEnemyHeroesInRange(700) >= 1 then
            CastQ(target)
        end
        if MyMenu.combo.e:get() and #common.GetEnemyHeroesInRange(300) >= 1 then
            CastE(target)
        end
        if MyMenu.combo.r:get() and player:spellSlot(3).state == 0 then
            -- 判断是否触发了上古领主的大招（Impale）
            local isImpaleActive = false
            -- 这里可以根据你的逻辑判断上古领主的大招是否处于激活状态
            if isImpaleActive then
                CastR(target)
            else
                local enemyCount = #common.GetEnemyHeroesInRange(300)
                if enemyCount >= 2 then -- 根据人数设定释放
                    player:castSpell("obj", 3, target)
                end
            end
        end
    end
end
--[[local function Combo()
	local target = get_target(select_target)
	if target and common.IsValidTarget(target) and not target.buff["sionpassivezombie"] then
		if MyMenu.combo.q:get() and #common.GetEnemyHeroesInRange(350) >= MyMenu.combo.qrange:get() then
			CastQ(target)
		end
		if MyMenu.combo.e:get() then
			CastE(target)
		end
		if MyMenu.combo.r:get() and player:spellSlot(3).state == 0 and player.path.serverPos:distSqr(target.path.serverPos) < (350 * 350) then
			for i = 0, objManager.enemies_n - 1 do
				local enemy = objManager.enemies[i]
				if MyMenu.combo.x[enemy.charName]:get() then
					player:castSpell("obj", 3, enemy)
				end
			end
		end
	end
end]]

local function Clear()
	local target = { obj = nil, health = 0, mode = "jungleclear" }
	local aaRange = player.attackRange + player.boundingRadius + 200
	for i = 0, minionmanager.size[TEAM_NEUTRAL] - 1 do
		local obj = minionmanager[TEAM_NEUTRAL][i]
		if player.pos:dist(obj.pos) <= aaRange and obj.maxHealth > target.health then
			target.obj = obj
			target.health = obj.maxHealth
		end
	end
	if target.obj then
		if target.mode == "jungleclear" then
			if MyMenu.jg.e:get() and player:spellSlot(0).state == 0 then
				CastE(target.obj)
			end
			if MyMenu.jg.w:get() and player:spellSlot(1).state == 0 then
				if common.GetPercentHealth(player) <=  MyMenu.jg.whp:get() then
					player:castSpell("self", 1)
				end
			end
			if MyMenu.jg.q:get() and player:spellSlot(0).state == 0 then
				CastQ(target.obj)
			end
		end
	end
end

local function OnTick()
	if MyMenu.Key.Combo:get() then Combo() end
	--if MyMenu.autor.uks:get() then KillSteal() end
	if MyMenu.Key.LaneClear:get() then Clear() end
	if MyMenu.combo.w:get() then
		if player:spellSlot(1).state == 0 then
			if #common.GetEnemyHeroesInRange(500) >= MyMenu.combo.xw:get() then
				if common.GetPercentHealth(player) <= MyMenu.combo.xhp:get() then
					player:castSpell("self", 1)
				end
			end
		end
	end
	if (MyMenu.Key.Combo:get()) and orb.combat.target then
		if orb.combat.target.buff[24] then
			orb.core.set_pause_attack(math.huge)
		end
	end
	if MyMenu.Key.Combo:get() and orb.combat.target then
		if not orb.combat.target.buff[24] then
			orb.core.set_pause_attack(0)
		end
	end
	if not MyMenu.Key.Combo:get() and not MyMenu.Key.LastHit:get() and not MyMenu.Key.LaneClear:get() then
		if orb.core.is_attack_paused() then
			orb.core.set_pause_attack(0)
		end
		if MyMenu.Key.Combo:get() and player:spellSlot(3).state ~= 0 then
			orb.core.set_pause_attack(0)
		end
	end
end


local function OnDraw()
	if MyMenu.draws.q:get() and player:spellSlot(0).state == 0 and player.isOnScreen then
		graphics.draw_circle(player.pos, 350, 2, MyMenu.draws.colorq:get(), 50)
	end
	if MyMenu.draws.e:get() and player:spellSlot(2).state == 0 and player.isOnScreen then
		graphics.draw_circle(player.pos, 1000, 2, MyMenu.draws.colore:get(), 50)
	end
end

orb.combat.register_f_pre_tick(OnTick)
cb.add(cb.draw, OnDraw)
cb.add(cb.spell, Shield)

return SkarnerPlugin
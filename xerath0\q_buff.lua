
local ove_0_5 = module.internal("orb")
local ove_0_6 = {
	isActive = false,
	name = "XerathArcanopulseChargeUp",
	start = 0
}

function ove_0_6.on_cast_spell(arg_1_0)
	-- print 1
	if arg_1_0 == 0 and not ove_0_6.isActive then
		ove_0_5.core.set_server_pause_attack()
	end
end

function ove_0_6.on_update_buff(arg_2_0)
	-- print 2
	if arg_2_0.name == ove_0_6.name then
		ove_0_5.core.set_pause_attack(math.huge)

		ove_0_6.isActive = true
	end
end

function ove_0_6.on_remove_buff(arg_3_0)
	-- print 3
	if arg_3_0.name == ove_0_6.name then
		ove_0_5.core.set_pause_attack(0)

		ove_0_6.isActive = false
	end
end

function ove_0_6.on_recv_spell(arg_4_0)
	-- print 4
	if arg_4_0.slot == 0 then
		ove_0_6.isActive = true
	end
end

return ove_0_6

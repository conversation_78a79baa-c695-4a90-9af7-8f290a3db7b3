local spellManager = {}

local ObjectManager = module.load("<PERSON>", "Library/ObjectManager")
local VectorManager = module.load("<PERSON>", "Library/VectorManager")
local MyCommon = module.load("<PERSON>", "Library/ExtraManager")

function spellManager.CanCastSpell(slot, source)
    if not slot or not type(slot) == "number" then
        return false
    end

    source = source or player
    if not source or source == nil then
        return false
    end

    if slot == 666 then
        return false
    end

    local sSlot = source:spellSlot(slot)
    if sSlot and sSlot.state and sSlot.state == 0 then
        return true
    end
end

function spellManager.GetSpellCooldown(slot, source)
    if not slot or not type(slot) == "number" then
        return 10000
    end

    source = source or player
    if not source or source == nil then
        return 10000
    end

    local sSlot = source:spellSlot(slot)
    if sSlot and sSlot.cooldown then
        return sSlot.cooldown
    end

    return 10000
end

function spellManager.SlotToString(slot)
    if not slot or not type(slot) == "number" then
        return "Unknow"
    end

    if slot == 0 then
        return "Q"
    elseif slot == 1 then
        return "W"
    elseif slot == 2 then
        return "E"
    elseif slot == 3 then
        return "R"
    elseif slot == 4 then
        return "D"
    elseif slot == 5 then
        return "F"
    elseif slot == 6 then
        return "Item 1"
    elseif slot == 7 then
        return "Item 2"
    elseif slot == 8 then
        return "Item 3"
    elseif slot == 9 then
        return "Item 4"
    elseif slot == 10 then
        return "Item 5"
    elseif slot == 11 then
        return "Item 6"
    elseif slot == 12 then
        return "Item 7"
    end

    return "Unknow"
end

function spellManager.GetSpellLevel(slot, source)
    if not slot or not type(slot) == "number" then
        return 0
    end

    source = source or player
    if not source or source == nil then
        return 0
    end

    local sSlot = source:spellSlot(slot)
    if sSlot and sSlot.level then
        return sSlot.level
    end

    return 0
end

function spellManager.GetSpellMana(slot, source)
    if not slot or not type(slot) == "number" then
        return 0
    end

    source = source or player
    if not source or source == nil then
        return 0
    end

    if slot == 0 then
        local mana = source.manaCost0
        if mana then
            return mana
        end
    elseif slot == 1 then
        local mana = source.manaCost1
        if mana then
            return mana
        end
    elseif slot == 2 then
        local mana = source.manaCost2
        if mana then
            return mana
        end
    elseif slot == 3 then
        local mana = source.manaCost3
        if mana then
            return mana
        end
    end

    return math.huge
end

function spellManager.CastOnPlayer(slot)
    if not slot or not type(slot) == "number" then
        return
    end

    -- player:castSpell("self", slot)
    player:castSpell("obj", slot, player)
end

function spellManager.CastOnUnit(target, slot)
    if not slot or not type(slot) == "number" then
        return
    end

    if not target or target == nil then
        return
    end

    player:castSpell("obj", slot, target)
end

function spellManager.CastOnPosition(pos, slot)
    if not slot or not type(slot) == "number" then
        return
    end

    if not pos then --or (pos.type ~= 'vec3' and pos.type ~= 'vec2') then
        return
    end

    player:castSpell("pos", slot, pos)
end

function spellManager.StartCharing(slot)
    if not slot or not type(slot) == "number" then
        return
    end

    player:castSpell("pos", slot, game.mousePos)
end

function spellManager.ReleasedCast(pos, slot)
    if not slot or not type(slot) == "number" then
        return
    end

    if not pos then --or (pos.type ~= 'vec3' and pos.type ~= 'vec2') then
        return
    end

    player:castSpell("release", slot, pos)
end

function spellManager.GetAOEHitCount(spell, startPos2D, endPos2D, includeBounds)
    includeBounds = includeBounds or true
    if not spell or not startPos2D or not endPos2D then
        return 0
    end
    if not type(spell) == "table" and not type(spell) == "number" then
        return 0
    end
    if type(spell) == "number" then
        if spell > 0 and endPos2D then
            local result = 0
            local targets = ObjectManager.GetEnemyHeroes()
            for i, target in ipairs(targets) do
                if target and target ~= nil and MyCommon.IsValidTarget(target) then
                    local dist = mathf.dist_line_vector(target.pos2D, startPos2D, endPos2D)
                    if dist <= (spell + (includeBounds and target.boundingRadius or 0)) then
                        result = result + 1
                    end
                end
            end
            return result
        end
    elseif type(spell) == "table" then
        if spell.width and spell.width > 0 then
            local realPos = endPos2D
            if spell.range and spell.range > 0 then
                -- for the max line spell range check
                realPos = VectorManager.Extend(startPos2D, endPos2D, spell.range)
            end
            local result = 0
            local targets = ObjectManager.GetEnemyHeroes()
            for i, target in ipairs(targets) do
                if target and target ~= nil and MyCommon.IsValidTarget(target) then
                    local dist = mathf.dist_line_vector(target.pos2D, startPos2D, realPos)
                    if dist <= (spell.width + (includeBounds and target.boundingRadius or 0)) then
                        result = result + 1
                    end
                end
            end
            return result
        elseif spell.radius and spell.radius > 0 then
            local result = 0
            local targets = ObjectManager.GetEnemyHeroes()
            for i, target in ipairs(targets) do
                if target and target ~= nil and MyCommon.IsValidTarget(target) then
                    local realDistance = spell.radius + (includeBounds and target.boundingRadius or 0)
                    if target.pos2D:distSqr(endPos2D) < (realDistance ^ 2) then
                        result = result + 1
                    end
                end
            end
            return result
        end
    end
    return 0
end

return spellManager

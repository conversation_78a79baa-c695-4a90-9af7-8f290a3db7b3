
local ove_0_10 = module.load("Kloader", "Lib/MyCommon")
local ove_0_11 = module.internal("orb/main")
local ove_0_12 = module.load("Kloader", "Lib/SpellDmg")
local ove_0_13 = module.load("Kloader", "Lib/DelayAction")
local ove_0_14 = player
local ove_0_15 = {
	concat = assert(table.concat),
	insert = assert(table.insert),
	remove = assert(table.remove),
	sort = assert(table.sort)
}

local function ove_0_16(arg_5_0, arg_5_1, arg_5_2)
	-- print 5
	for iter_5_0, iter_5_1 in pairs(arg_5_0) do
		if arg_5_2 and iter_5_1[arg_5_2] == arg_5_1 or iter_5_1 == arg_5_1 then
			return iter_5_0, iter_5_1
		end
	end
end

local ove_0_17 = ove_0_10.Class()
local ove_0_18 = ove_0_10.GetEnemyHeroes()

function ove_0_17.__init(arg_6_0, arg_6_1, arg_6_2)
	-- print 6
	arg_6_0.func = arg_6_2
	set_Krystra_Antigap = false
	arg_6_0.callbacks = {}
	arg_6_0.activespells = {}
	arg_6_0.spells = {
		AatroxQ = {
			spellname = "Q | Dark Flight",
			Name = "Aatrox"
		},
		AhriTumble = {
			spellname = "R | Spirit Rush",
			Name = "Ahri"
		},
		AkaliShadowDance = {
			spellname = "R | Shadow Dance",
			Name = "Akali"
		},
		AlphaStrike = {
			spellname = "Q | Alpha Strike",
			Name = "MasterYi"
		},
		BandageToss = {
			spellname = "Q | Bandage Toss",
			Name = "Amumu"
		},
		Crowstorm = {
			spellname = "R | Crowstorm ",
			Name = "FiddleSticks"
		},
		DianaTeleport = {
			spellname = "R | Lunar Rush",
			Name = "Diana"
		},
		EliseSpiderEDescent = {
			spellname = "E | Rappel",
			Name = "Elise"
		},
		EliseSpiderQCast = {
			spellname = "Q | Venomous Bite",
			Name = "Elise"
		},
		FioraQ = {
			spellname = "Q | Lunge",
			Name = "Fiora"
		},
		FizzPiercingStrike = {
			spellname = "E | Urchin Strike",
			Name = "Fizz"
		},
		GarenQ = {
			spellname = "Q | Decisive Strike",
			Name = "Garen"
		},
		GnarBigE = {
			spellname = "E | Crunch",
			Name = "Gnar"
		},
		GnarE = {
			spellname = "E | Hop",
			Name = "Gnar"
		},
		GragasE = {
			spellname = "E | Body Slam",
			Name = "Gragas"
		},
		GravesMove = {
			spellname = "E | Quickdraw",
			Name = "Graves"
		},
		Headbutt = {
			spellname = "W | Headbutt",
			Name = "Alistar"
		},
		HecarimUlt = {
			spellname = "R | Onslaught of Shadows",
			Name = "Hecarim"
		},
		IreliaGatotsu = {
			spellname = "Q | Bladesurge",
			Name = "Irelia"
		},
		JarvanIVCataclysm = {
			spellname = "R | Cataclysm",
			Name = "JarvanIV"
		},
		JarvanIVDragonStrike = {
			spellname = "Q | Dragon Strike",
			Name = "JarvanIV"
		},
		JaxLeapStrike = {
			spellname = "Q | Leap Strike",
			Name = "Jax"
		},
		JayceToTheSkies = {
			spellname = "W | To The Skies!",
			Name = "Jayce"
		},
		KatarinaE = {
			spellname = "E | Shunpo",
			Name = "Katarina"
		},
		KennenLightningRush = {
			spellname = "E | Lightning Rush",
			Name = "Kennen"
		},
		KhazixE = {
			spellname = "E | Leap",
			Name = "Khazix"
		},
		LeblancSlide = {
			spellname = "W | Distortion",
			Name = "Leblanc"
		},
		LeblancSlideM = {
			spellname = "R | Distortion",
			Name = "Leblanc"
		},
		LeonaZenithBlade = {
			spellname = "E | Zenith Blade",
			Name = "Leona"
		},
		LissandraE = {
			spellname = "E | Glacial Path",
			Name = "Lissandra"
		},
		LucianE = {
			spellname = "E | Relentless Pursuit",
			Name = "Lucian"
		},
		MaokaiUnstableGrowth = {
			spellname = "W | Twisted Advance",
			Name = "Maokai"
		},
		MonkeyKingNimbus = {
			spellname = "E | Nimbus Strike",
			Name = "MonkeyKing"
		},
		NautilusAnchorDrag = {
			spellname = "Q | Dredge Line",
			Name = "Nautilus"
		},
		Pantheon_LeapBash = {
			spellname = "W | Aegis of Zeonia",
			Name = "Pantheon"
		},
		PoppyHeroicCharge = {
			spellname = "E | Heroic Charge",
			Name = "Poppy"
		},
		QuinnE = {
			spellname = "E | Vault",
			Name = "Quinn"
		},
		RenektonSliceAndDice = {
			spellname = "E | Slice",
			Name = "Renekton"
		},
		RiftWalk = {
			spellname = "R | Riftwalk",
			Name = "Kassadin"
		},
		RivenTriCleave = {
			spellname = "Q | Broken Wings",
			Name = "Riven"
		},
		RocketJump = {
			spellname = "W | Rocket Jump",
			Name = "Tristana"
		},
		SejuaniArcticAssault = {
			spellname = "Q | Arctic Assault",
			Name = "Sejuani"
		},
		ShenShadowDash = {
			spellname = "E | Shadow Dash",
			Name = "Shen"
		},
		TalonCutThroat = {
			spellname = "E | Cutthroat",
			Name = "Talon"
		},
		UFSlash = {
			spellname = "R | Unstoppable Force",
			Name = "Malphite"
		},
		UdyrBearStance = {
			spellname = "E | Bear Stance",
			Name = "Udyr"
		},
		Valkyrie = {
			spellname = "W | Valkyrie",
			Name = "Corki"
		},
		ViQ = {
			spellname = "Q | Vault Breaker",
			Name = "Vi"
		},
		VolibearQ = {
			spellname = "Q | Rolling Thunder",
			Name = "Volibear"
		},
		XenZhaoSweep = {
			spellname = "E | Crescent Sweep",
			Name = "XinZhao"
		},
		YasuoDashWrapper = {
			spellname = "E | Sweeping Blade",
			Name = "Yasuo"
		},
		blindmonkqtwo = {
			spellname = "Q | Resonating Strike",
			Name = "LeeSin"
		},
		khazixelong = {
			spellname = "E | Leap",
			Name = "Khazix"
		},
		reksaieburrowed = {
			spellname = "E | Tunnel",
			Name = "RekSai"
		},
		TryndamereE = {
			spellname = "E | Spinning Slash",
			Name = "Tryndamere"
		}
	}

	if arg_6_1 then
		arg_6_0:AddToMenu(arg_6_1)
	end

	if arg_6_0.func then
		ove_0_15.insert(arg_6_0.callbacks, arg_6_0.func)
	end

	cb.add(cb.tick, function()
		-- print 7
		arg_6_0:Tick()
	end)
	cb.add(cb.spell, function(arg_8_0)
		-- print 8
		arg_6_0:ProcessSpell(arg_8_0)
	end)
end

function ove_0_17.AddToMenu(arg_9_0, arg_9_1)
	-- print 9
	arg_9_0.menu = arg_9_1

	local slot_9_0 = false
	local slot_9_1 = {}

	for iter_9_0, iter_9_1 in pairs(ove_0_18) do
		ove_0_15.insert(slot_9_1, iter_9_1.charName)
	end

	arg_9_0.menu:boolean("Enabled", "Enabled", true)
	ove_0_13.Cast(function()
		-- print 10
		arg_9_0.menu:menu("skill", "Detected Skills")
		arg_9_0.menu.skill:header("header_skills", "Detected Skills")

		for iter_10_0, iter_10_1 in pairs(arg_9_0.spells) do
			if ove_0_16(slot_9_1, iter_10_1.Name) then
				arg_9_0.menu.skill:boolean(iter_10_0, iter_10_1.Name .. " | " .. iter_10_1.spellname, true)

				slot_9_0 = true
			end
		end

		if not slot_9_0 then
			arg_9_0.menu.skill:header("header_combo_extra1", "No spell available to AntiGap")
		end
	end, 0.5)
end

function ove_0_17.TriggerCallbacks(arg_11_0, arg_11_1, arg_11_2)
	-- print 11
	for iter_11_0, iter_11_1 in pairs(arg_11_0.callbacks) do
		iter_11_1(arg_11_1, arg_11_2)
	end
end

function ove_0_17.Close(arg_12_0)
	-- print 12
	set_Krystra_Antigap = true
end

function ove_0_17.ProcessSpell(arg_13_0, arg_13_1)
	-- print 13
	if not arg_13_0.menu.Enabled:get() or arg_13_1.owner.team == ove_0_14.team or arg_13_1.owner.type ~= ove_0_14.type then
		return
	end

	if not arg_13_0.spells[arg_13_1.name] or not arg_13_0.menu.skill[arg_13_1.name] or not arg_13_0.menu.skill[arg_13_1.name]:get() then
		return
	end

	local slot_13_0 = arg_13_1.target and arg_13_1.target == ove_0_14 and true or false

	if arg_13_1.target and arg_13_1.target.charName == "" and (ove_0_10.G2(arg_13_1.owner) > ove_0_10.G2(arg_13_1.owner.pos + 300 * (arg_13_1.endPos - arg_13_1.owner.pos):norm()) or ove_0_10.G2(arg_13_1.owner) > ove_0_10.G2(arg_13_1.owner.pos + 100 * (arg_13_1.endPos - arg_13_1.owner.pos):norm())) then
		slot_13_0 = true
	end

	if slot_13_0 then
		local slot_13_1 = {
			unit = arg_13_1.owner,
			name = arg_13_1.name,
			endPos = arg_13_1.endPos,
			startPos = arg_13_1.startPos,
			endTime = game.time + 1.5
		}

		ove_0_15.insert(arg_13_0.activespells, slot_13_1)
		arg_13_0:TriggerCallbacks(slot_13_1.unit, slot_13_1)
	end
end

function ove_0_17.Tick(arg_14_0)
	-- print 14
	for iter_14_0 = #arg_14_0.activespells, 1, -1 do
		if arg_14_0.activespells[iter_14_0].endTime - game.time > 0 and not set_Krystra_Antigap then
			arg_14_0:TriggerCallbacks(arg_14_0.activespells[iter_14_0].unit, arg_14_0.activespells[iter_14_0])
		else
			ove_0_15.remove(arg_14_0.activespells, iter_14_0)
		end
	end
end

return ove_0_17

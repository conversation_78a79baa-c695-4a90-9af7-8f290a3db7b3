# Darius 符文伤害计算系统

## 概述
为 Darius 脚本添加了完整的符文伤害计算系统，支持主要的伤害符文，提高击杀判断和伤害预测的准确性。

## 支持的符文

### 1. 公理秘术 (Arcane Comet)
- **基础伤害**: 30-100 (等级1-18)
- **AP加成**: 0.2 AP
- **AD加成**: 0.35 AD
- **伤害类型**: 魔法伤害
- **触发条件**: 技能命中敌人

### 2. 电刑 (Electrocute)
- **基础伤害**: 30-180 (等级1-18)
- **AP加成**: 0.25 AP
- **AD加成**: 0.4 AD
- **伤害类型**: 魔法伤害
- **触发条件**: 3次独立攻击或技能命中同一敌人

### 3. 黑暗收割 (Dark Harvest)
- **基础伤害**: 20-60 (等级1-18)
- **AP加成**: 0.25 AP
- **AD加成**: 0.15 AD
- **收割加成**: 每层+5伤害
- **伤害类型**: 魔法伤害
- **触发条件**: 对低血量敌人造成伤害

### 4. 征服者 (Conqueror)
- **每层伤害**: 1.7-3.0 AD (等级1-18)
- **最大层数**: 12层
- **真实伤害转换**: 满层时10%伤害转为真实伤害
- **伤害类型**: 适应性伤害 + 真实伤害
- **触发条件**: 对敌方英雄造成伤害

## 功能特性

### 1. 菜单设置
在 `符文设置` 菜单中可以控制：
- ✅ 计算公理秘术
- ✅ 计算电刑
- ✅ 计算黑暗收割
- ✅ 计算征服者

### 2. 伤害计算集成
符文伤害已集成到所有技能的伤害计算中：
- **Q技能**: 包含符文伤害
- **W技能**: 包含符文伤害
- **R技能**: 包含符文伤害
- **普攻**: 包含征服者伤害

### 3. 伤害显示
- 血条上的伤害指示器包含符文伤害
- 伤害文本显示总伤害和符文伤害
- 格式: `总伤害 (+符文伤害符文)`

### 4. 安全性保障
- 所有属性访问都有空值检查
- 使用默认值防止计算错误
- 魔法抗性、法术穿透等属性的安全获取

## 技术实现

### 文件结构
```
darius/
├── main.lua          # 主逻辑文件，集成符文伤害显示
├── misc.lua          # 伤害计算函数，包含符文伤害计算
├── runes.lua         # 专门的符文计算模块
├── menu.lua          # 菜单配置，包含符文设置
└── 符文系统说明.md   # 本说明文档
```

### 核心函数
- `slot_13_65()`: 主要符文伤害计算函数
- `CalculateRuneDamage()`: 符文模块的主计算函数
- `GetRuneDamageDetails()`: 获取符文伤害详情
- `GetRuneInfo()`: 获取符文配置信息

### 伤害计算公式
```lua
-- 魔法伤害减免
magicReduction = 100 / (100 + targetMR * (1 - playerMagicPen) - playerFlatMagicPen)

-- 公理秘术
cometDamage = (30 + 70 * (level-1)/17 + 0.2*AP + 0.35*AD) * magicReduction

-- 电刑
electroDamage = (30 + 150 * (level-1)/17 + 0.25*AP + 0.4*AD) * magicReduction

-- 黑暗收割
harvestDamage = (20 + 40 * (level-1)/17 + 0.25*AP + 0.15*AD + stacks*5) * magicReduction

-- 征服者
conquerorDamage = AD * (1.7 + 1.3 * (level-1)/17) * stacks * 0.1
```

## 使用说明

### 1. 启用符文计算
1. 打开脚本菜单
2. 进入 `符文设置`
3. 勾选需要计算的符文类型

### 2. 查看伤害预测
- 血条上的红线显示包含符文伤害的总伤害
- 伤害文本显示具体数值
- "可斩杀"提示考虑了符文伤害

### 3. 优化设置
- 根据实际符文配置启用对应选项
- 不使用的符文可以关闭以提高性能
- 符文伤害会自动应用到击杀判断中

## 注意事项

### 1. 符文层数
- 黑暗收割层数默认为10层（可通过buff获取实际值）
- 征服者层数默认为满层12层（可通过buff获取实际值）
- 实际游戏中会尝试从玩家buff获取真实层数

### 2. 冷却时间
- 当前版本未考虑符文冷却时间
- 所有符文伤害都会被计算在内
- 后续版本可以添加冷却时间检查

### 3. 触发条件
- 符文伤害计算假设触发条件已满足
- 实际游戏中需要满足各符文的触发条件
- 建议根据实际情况调整菜单设置

## 更新日志

### v1.0 (当前版本)
- ✅ 添加四大主要伤害符文支持
- ✅ 集成到所有技能伤害计算
- ✅ 菜单控制选项
- ✅ 伤害显示优化
- ✅ 安全性保障和错误处理

### 计划功能
- 🔄 符文冷却时间检查
- 🔄 自动检测玩家符文配置
- 🔄 更多符文支持（如相位猛冲等）
- 🔄 符文伤害详细显示模式

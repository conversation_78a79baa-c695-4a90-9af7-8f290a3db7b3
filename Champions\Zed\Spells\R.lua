local ove_0_10 = player
local ove_0_11 = module.load("<PERSON>", "Lib/TurretDive")
local ove_0_12 = {
	range = 625,
	Slot = player:spellSlot(3),
	LastTarget = nil,
	LastCastTime = 0,
	MarkDuration = 3.0,
	-- 最新版本R技能数据
	baseDamage = {100, 150, 200},
	adRatio = 1.1,
	markDamagePercent = {0.25, 0.30, 0.35},
	energyCost = 0
}

function ove_0_12.Cast(arg_5_0)
	if not ove_0_12.IsR1() or not ove_0_12.Ready() then
		return false
	end

	-- 检查目标是否有效（移除isValid检查）
	if not arg_5_0 or arg_5_0.isDead or not arg_5_0.isTargetable then
		return false
	end

	-- 检查距离
	if vec3.dist(ove_0_10.pos, arg_5_0.pos) > ove_0_12.range then
		return false
	end

	-- 检查是否可以越塔
	if not ove_0_11.DiveLogic(arg_5_0.pos) then
		return false
	end

	-- 记录目标信息和施法时间
	ove_0_12.LastTarget = arg_5_0
	ove_0_12.LastCastTime = game.time
	ove_0_12.RecordTargetHP(arg_5_0) -- 记录目标当前血量

	player:castSpell("obj", 3, arg_5_0)
	return true
end

function ove_0_12.CastR2()
	if not ove_0_12.Ready() or ove_0_12.IsR1() then
		return false
	end

	-- 检查是否需要返回原位置
	if ove_0_12.LastTarget then
		local timeSinceCast = game.time - ove_0_12.LastCastTime

		-- 检查目标是否已死亡（击杀成功）
		if ove_0_12.LastTarget.isDead then
			player:castSpell("self", 3)
			print("劫R击杀: 目标已死亡，返回R影子位置")
			return true
		end

		-- 检查目标是否即将被击杀（血量极低且有死亡标记）
		if ove_0_12.HasDeathMark(ove_0_12.LastTarget) then
			local predictedDamage = ove_0_12.GetDeathMarkDamage(ove_0_12.LastTarget)
			if ove_0_12.LastTarget.health <= predictedDamage * 0.9 and timeSinceCast >= 0.5 then
				player:castSpell("self", 3)
				print("劫R击杀: 目标即将死亡，预计伤害: " .. math.floor(predictedDamage) .. "，目标血量: " .. math.floor(ove_0_12.LastTarget.health))
				return true
			end
		end

		-- 标记即将结束时强制返回（安全机制）
		if timeSinceCast >= ove_0_12.MarkDuration - 0.5 then
			player:castSpell("self", 3)
			print("劫R击杀: 死亡标记即将结束，强制返回R影子")
			return true
		end
	end

	return false
end

function ove_0_12.GetDeathMarkDamage(target)
	if not target then return 0 end

	local rLevel = ove_0_12.Level()
	if rLevel == 0 then return 0 end

	-- 使用最新的R技能数据
	local baseDamage = ove_0_12.baseDamage[rLevel]
	local bonusDmg = player.totalAttackDamage * ove_0_12.adRatio

	-- 死亡标记的伤害百分比（最新数值）
	local markPercentage = ove_0_12.markDamagePercent[rLevel]

	-- 更精确的已造成伤害计算
	local dealtDamage = 0
	if ove_0_12.LastTarget and ove_0_12.LastTarget.networkID == target.networkID then
		if ove_0_12.LastCastTime > 0 and (game.time - ove_0_12.LastCastTime) < ove_0_12.MarkDuration then
			-- 记录R释放时的血量，计算实际已造成伤害
			local initialHP = target.maxHealth -- 如果没有记录初始血量，使用最大血量
			if ove_0_12.LastTargetHP then
				initialHP = ove_0_12.LastTargetHP
			end

			local currentHP = target.health
			local actualDamageDealt = math.max(0, initialHP - currentHP)

			-- 只计算在R标记期间造成的伤害
			dealtDamage = actualDamageDealt
		end
	end

	-- 计算护甲减免（更精确的计算）
	local targetArmor = target.armor
	local armorPenFlat = player.armorPenFlat or 0
	local armorPenPercent = player.armorPenPercent or 1

	-- 先应用百分比穿透，再应用固定穿透
	local effectiveArmor = math.max(0, (targetArmor * armorPenPercent) - armorPenFlat)
	local damageMultiplier = 100 / (100 + effectiveArmor)

	-- R技能基础伤害
	local rBaseDamage = (baseDamage + bonusDmg) * damageMultiplier

	-- 死亡标记爆炸伤害
	local markExplosionDamage = 0
	if dealtDamage > 0 then
		markExplosionDamage = (dealtDamage * markPercentage) * damageMultiplier
	end

	-- 总伤害
	local totalDamage = rBaseDamage + markExplosionDamage

	-- 返回总伤害，增加小幅安全边际
	return totalDamage * 1.05
end

-- 记录R释放时目标的血量
function ove_0_12.RecordTargetHP(target)
	if target then
		ove_0_12.LastTargetHP = target.health
	end
end

function ove_0_12.Ready()
	return ove_0_12.Slot.state == 0
end

function ove_0_12.IsR1()
	return ove_0_12.Slot.name == "ZedR"
end

function ove_0_12.IsR2()
	return ove_0_12.Slot.name == "ZedR2"
end

function ove_0_12.Cost()
	return ove_0_12.energyCost
end

function ove_0_12.Level()
	return player:spellSlot(3).level
end

function ove_0_12.HasDeathMark(target)
	return target and target.buff["zedrtargetmark"] ~= nil
end

function ove_0_12.GetRemainingMarkDuration(target)
	if not target or not ove_0_12.HasDeathMark(target) then
		return 0
	end
	
	local buff = target.buff["zedrtargetmark"]
	return buff.endTime - game.time
end

return ove_0_12


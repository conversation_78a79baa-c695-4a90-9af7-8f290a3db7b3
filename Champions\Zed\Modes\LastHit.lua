
local ove_0_10 = module.load("<PERSON>", "<PERSON><PERSON>/<PERSON><PERSON><PERSON><PERSON>")
local ove_0_11 = module.load("<PERSON>", "Champions/Zed/Util/menu")
local ove_0_12 = objManager.minions
local ove_0_13 = module.load("<PERSON>", "Champions/Zed/Spells/W")
local ove_0_14 = module.load("<PERSON>", "Champions/Zed/Spells/Q")
local ove_0_15 = module.load("<PERSON>", "Champions/Zed/Spells/E")
local ove_0_16 = module.load("<PERSON>", "Champions/Zed/Util/Detection")
local ove_0_17 = module.load("<PERSON>", "Champions/Zed/Util/Damage")
local ove_0_18 = player

return {
	Execute = function()
		-- print 5
		local slot_5_0 = ove_0_18.mana >= ove_0_11.farm.lasthit.Qmana:get() and ove_0_18.mana >= ove_0_14.Cost()
		local slot_5_1 = ove_0_18.mana >= ove_0_11.farm.lasthit.Emana:get() and ove_0_18.mana >= ove_0_15.Cost()

		for iter_5_0 = 0, ove_0_12.size[TEAM_ENEMY] - 1 do
			local slot_5_2 = ove_0_12[TEAM_ENEMY][iter_5_0]

			if ove_0_10.IsValidTarget(slot_5_2) and ove_0_10.IsMinion(slot_5_2) then
				local slot_5_3 = ove_0_17.Get("Q", slot_5_2)
				local slot_5_4 = ove_0_17.Get("E", slot_5_2)

				if ove_0_11.farm.lasthit.useE:get() and slot_5_4 > slot_5_2.health and slot_5_1 and ove_0_10.Dist(slot_5_2) < ove_0_15.range then
					player:castSpell("self", 2)
				end

				if ove_0_11.farm.lasthit.useQ:get() and slot_5_3 > slot_5_2.health and slot_5_0 then
					ove_0_14.Cast(slot_5_2)
				end
			end
		end
	end
}

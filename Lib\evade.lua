
local ove_0_10 = module.load("<PERSON>", "<PERSON><PERSON>/MyC<PERSON><PERSON>")
local ove_0_11 = module.internal("orb/main")
local ove_0_12 = module.load("<PERSON>", "Lib/SpellDmg")
local ove_0_13 = module.load("<PERSON>", "Lib/DelayAction")
local ove_0_14 = player
local ove_0_15 = {
	concat = assert(table.concat),
	insert = assert(table.insert),
	remove = assert(table.remove),
	sort = assert(table.sort)
}

local function ove_0_16(arg_5_0, arg_5_1, arg_5_2)
	-- print 5
	for iter_5_0, iter_5_1 in pairs(arg_5_0) do
		if arg_5_2 and iter_5_1[arg_5_2] == arg_5_1 or iter_5_1 == arg_5_1 then
			return iter_5_0, iter_5_1
		end
	end
end

local ove_0_17 = ove_0_10.Class()
local ove_0_18 = ove_0_10.GetEnemyHeroes()

function ove_0_17.__init(arg_6_0, arg_6_1, arg_6_2)
	-- print 6
	arg_6_0.func = arg_6_2
	set_Krystra_Antigap = false
	arg_6_0.callbacks = {}
	arg_6_0.activespells = {}
	arg_6_0.spells = {
		CaitlynAceintheHole = {
			spellname = "R | Ace in the Hole",
			Name = "Caitlyn",
			menuBool = true
		},
		Crowstorm = {
			spellname = "R | Crowstorm",
			Name = "FiddleSticks",
			menuBool = true
		},
		Drain = {
			spellname = "W | Drain",
			Name = "FiddleSticks",
			menuBool = false
		},
		GalioIdolOfDurand = {
			spellname = "R | Idol of Durand",
			Name = "Galio",
			menuBool = true
		},
		ReapTheWhirlwind = {
			spellname = "R | Monsoon",
			Name = "Janna",
			menuBool = true
		},
		KarthusFallenOne = {
			spellname = "R | Requiem",
			Name = "Karthus",
			menuBool = true
		},
		KatarinaR = {
			spellname = "R | Death Lotus",
			Name = "Katarina",
			menuBool = true
		},
		LucianR = {
			spellname = "R | The Culling",
			Name = "Lucian",
			menuBool = true
		},
		AlZaharNetherGrasp = {
			spellname = "R | Nether Grasp",
			Name = "Malzahar",
			menuBool = true
		},
		Meditate = {
			spellname = "W | Meditate",
			Name = "MasterYi",
			menuBool = false
		},
		MissFortuneBulletTime = {
			spellname = "R | Bullet Time",
			Name = "MissFortune",
			menuBool = true
		},
		AbsoluteZero = {
			spellname = "R | Absoulte Zero",
			Name = "Nunu",
			menuBool = true
		},
		ShenStandUnited = {
			spellname = "R | Stand United",
			Name = "Shen",
			menuBool = true
		},
		Destiny = {
			spellname = "R | Destiny",
			Name = "TwistedFate",
			menuBool = true
		},
		UrgotSwap2 = {
			spellname = "R | Hyper-Kinetic Position Reverser",
			Name = "Urgot",
			menuBool = true
		},
		VarusQ = {
			spellname = "Q | Piercing Arrow",
			Name = "Varus",
			menuBool = false
		},
		VelkozR = {
			spellname = "R | Lifeform Disintegration Ray",
			Name = "Velkoz",
			menuBool = true
		},
		InfiniteDuress = {
			spellname = "R | Infinite Duress",
			Name = "Warwick",
			menuBool = true
		},
		XerathLocusOfPower2 = {
			spellname = "R | Rite of the Arcane",
			Name = "Xerath",
			menuBool = true
		},
		FizzMarinerDoom = {
			spellname = "FizzMarinerDoom",
			Name = "Fizz",
			Spellslot = _R
		},
		AatroxE = {
			spellname = "AatroxE",
			Name = "Aatrox",
			Spellslot = _E
		},
		AhriOrbofDeception = {
			spellname = "AhriOrbofDeception",
			Name = "Ahri",
			Spellslot = _Q
		},
		AhriFoxFire = {
			spellname = "AhriFoxFire",
			Name = "Ahri",
			Spellslot = _W
		},
		AhriSeduce = {
			spellname = "AhriSeduce",
			Name = "Ahri",
			Spellslot = _E
		},
		AhriTumble = {
			spellname = "AhriTumble",
			Name = "Ahri",
			Spellslot = _R
		},
		FlashFrost = {
			spellname = "FlashFrost",
			Name = "Anivia",
			Spellslot = _Q
		},
		Anivia2 = {
			spellname = "Frostbite",
			Name = "Anivia",
			Spellslot = _E
		},
		Disintegrate = {
			spellname = "Disintegrate",
			Name = "Annie",
			Spellslot = _Q
		},
		Volley = {
			spellname = "Volley",
			Name = "Ashe",
			Spellslot = _W
		},
		EnchantedCrystalArrow = {
			spellname = "EnchantedCrystalArrow",
			Name = "Ashe",
			Spellslot = _R
		},
		BandageToss = {
			spellname = "BandageToss",
			Name = "Amumu"
		},
		RocketGrabMissile = {
			spellname = "RocketGrabMissile",
			Name = "Blitzcrank"
		},
		BrandBlaze = {
			spellname = "BrandBlaze",
			Name = "Brand",
			Spellslot = _Q
		},
		BrandWildfire = {
			spellname = "BrandWildfire",
			Name = "Brand",
			Spellslot = _R
		},
		BraumQ = {
			spellname = "BraumQ",
			Name = "Braum"
		},
		BraumRWapper = {
			spellname = "BraumRWapper",
			Name = "Braum"
		},
		CaitlynPiltoverPeacemaker = {
			spellname = "CaitlynPiltoverPeacemaker",
			Name = "Caitlyn"
		},
		CaitlynEntrapment = {
			spellname = "CaitlynEntrapment",
			Name = "Caitlyn"
		},
		CaitlynAceintheHole = {
			spellname = "CaitlynAceintheHole",
			Name = "Caitlyn"
		},
		CassiopeiaMiasma = {
			spellname = "CassiopeiaMiasma",
			Name = "Cassiopiea"
		},
		CassiopeiaTwinFang = {
			spellname = "CassiopeiaTwinFang",
			Name = "Cassiopiea"
		},
		PhosphorusBomb = {
			spellname = "PhosphorusBomb",
			Name = "Corki"
		},
		MissileBarrage = {
			spellname = "MissileBarrage",
			Name = "Corki"
		},
		DianaArc = {
			spellname = "DianaArc",
			Name = "Diana"
		},
		InfectedCleaverMissileCast = {
			spellname = "InfectedCleaverMissileCast",
			Name = "DrMundo"
		},
		dravenspinning = {
			spellname = "dravenspinning",
			Name = "Draven"
		},
		DravenDoubleShot = {
			spellname = "DravenDoubleShot",
			Name = "Draven"
		},
		DravenRCast = {
			spellname = "DravenRCast",
			Name = "Draven"
		},
		EliseHumanQ = {
			spellname = "EliseHumanQ",
			Name = "Elise"
		},
		EliseHumanE = {
			spellname = "EliseHumanE",
			Name = "Elise"
		},
		EvelynnQ = {
			spellname = "EvelynnQ",
			Name = "Evelynn"
		},
		EzrealQ = {
			spellname = "Q",
			Name = "Ezreal"
		},
		EzrealEssenceFlux = {
			spellname = "EzrealEssenceFlux",
			Name = "Ezreal"
		},
		EzrealArcaneShift = {
			spellname = "EzrealArcaneShift",
			Name = "Ezreal"
		},
		GalioRighteousGust = {
			spellname = "GalioRighteousGust",
			Name = "Galio"
		},
		GalioResoluteSmite = {
			spellname = "GalioResoluteSmite",
			Name = "Galio"
		},
		Parley = {
			spellname = "Parley",
			Name = "Gangplank"
		},
		GnarQ = {
			spellname = "GnarQ",
			Name = "Gnar"
		},
		GravesClusterShot = {
			spellname = "GravesClusterShot",
			Name = "Graves"
		},
		GravesChargeShot = {
			spellname = "GravesChargeShot",
			Name = "Graves"
		},
		HeimerdingerW = {
			spellname = "HeimerdingerW",
			Name = "Heimerdinger"
		},
		IreliaTranscendentBlades = {
			spellname = "IreliaTranscendentBlades",
			Name = "Irelia"
		},
		HowlingGale = {
			spellname = "HowlingGale",
			Name = "Janna"
		},
		JayceToTheSkies = {
			Name = "Jayce",
			spellname = "JayceToTheSkies" or "jayceshockblast"
		},
		jayceshockblast = {
			Name = "Jayce",
			spellname = "JayceToTheSkies" or "jayceshockblast"
		},
		JinxW = {
			spellname = "JinxW",
			Name = "Jinx"
		},
		JinxR = {
			spellname = "JinxR",
			Name = "Jinx"
		},
		KalistaMysticShot = {
			spellname = "KalistaMysticShot",
			Name = "Kalista"
		},
		KarmaQ = {
			spellname = "KarmaQ",
			Name = "Karma"
		},
		NullLance = {
			spellname = "NullLance",
			Name = "Kassidan"
		},
		KatarinaR = {
			spellname = "KatarinaR",
			Name = "Katarina"
		},
		LeblancChaosOrb = {
			spellname = "LeblancChaosOrb",
			Name = "Leblanc"
		},
		LeblancSoulShackle = {
			Name = "Leblanc",
			spellname = "LeblancSoulShackle" or "LeblancSoulShackleM"
		},
		LeblancSoulShackleM = {
			Name = "Leblanc",
			spellname = "LeblancSoulShackle" or "LeblancSoulShackleM"
		},
		BlindMonkQOne = {
			spellname = "BlindMonkQOne",
			Name = "Leesin"
		},
		LeonaZenithBladeMissle = {
			spellname = "LeonaZenithBladeMissle",
			Name = "Leona"
		},
		LissandraE = {
			spellname = "LissandraE",
			Name = "Lissandra"
		},
		LucianR = {
			spellname = "LucianR",
			Name = "Lucian"
		},
		LuxLightBinding = {
			spellname = "LuxLightBinding",
			Name = "Lux"
		},
		LuxLightStrikeKugel = {
			spellname = "LuxLightStrikeKugel",
			Name = "Lux"
		},
		MissFortuneBulletTime = {
			spellname = "MissFortuneBulletTime",
			Name = "Missfortune"
		},
		DarkBindingMissile = {
			spellname = "DarkBindingMissile",
			Name = "Morgana"
		},
		NamiR = {
			spellname = "NamiR",
			Name = "Nami"
		},
		JavelinToss = {
			spellname = "JavelinToss",
			Name = "Nidalee"
		},
		NocturneDuskbringer = {
			spellname = "NocturneDuskbringer",
			Name = "Nocturne"
		},
		Pantheon_Throw = {
			spellname = "Pantheon_Throw",
			Name = "Pantheon"
		},
		QuinnQ = {
			spellname = "QuinnQ",
			Name = "Quinn"
		},
		RengarE = {
			spellname = "RengarE",
			Name = "Rengar"
		},
		rivenizunablade = {
			spellname = "rivenizunablade",
			Name = "Riven"
		},
		Overload = {
			spellname = "Overload",
			Name = "Ryze"
		},
		SpellFlux = {
			spellname = "SpellFlux",
			Name = "Ryze"
		},
		SejuaniGlacialPrisonStart = {
			spellname = "SejuaniGlacialPrisonStart",
			Name = "Sejuani"
		},
		SivirQ = {
			spellname = "SivirQ",
			Name = "Sivir"
		},
		SivirE = {
			spellname = "SivirE",
			Name = "Sivir"
		},
		SkarnerFractureMissileSpell = {
			spellname = "SkarnerFractureMissileSpell",
			Name = "Skarner"
		},
		SonaCrescendo = {
			spellname = "SonaCrescendo",
			Name = "Sona"
		},
		SwainDecrepify = {
			spellname = "SwainDecrepify",
			Name = "Swain"
		},
		SwainMetamorphism = {
			spellname = "SwainMetamorphism",
			Name = "Swain"
		},
		SyndraE = {
			spellname = "SyndraE",
			Name = "Syndra"
		},
		SyndraR = {
			spellname = "SyndraR",
			Name = "Syndra"
		},
		TalonRake = {
			spellname = "TalonRake",
			Name = "Talon"
		},
		TalonShadowAssault = {
			spellname = "TalonShadowAssault",
			Name = "Talon"
		},
		BlindingDart = {
			spellname = "BlindingDart",
			Name = "Teemo"
		},
		Thresh = {
			spellname = "ThreshQ",
			Name = "Thresh"
		},
		BusterShot = {
			spellname = "BusterShot",
			Name = "Tristana"
		},
		VarusQ = {
			spellname = "VarusQ",
			Name = "Varus"
		},
		VarusR = {
			spellname = "VarusR",
			Name = "Varus"
		},
		VayneCondemm = {
			spellname = "VayneCondemm",
			Name = "Vayne"
		},
		VeigarPrimordialBurst = {
			spellname = "VeigarPrimordialBurst",
			Name = "Veigar"
		},
		WildCards = {
			spellname = "WildCards",
			Name = "Twistedfate"
		},
		VelkozQ = {
			spellname = "VelkozQ",
			Name = "Velkoz"
		},
		VelkozW = {
			spellname = "VelkozW",
			Name = "Velkoz"
		},
		ViktorDeathRay = {
			spellname = "ViktorDeathRay",
			Name = "Viktor"
		},
		XerathArcanoPulseChargeUp = {
			spellname = "XerathArcanoPulseChargeUp",
			Name = "Xerath"
		},
		ZedShuriken = {
			spellname = "ZedShuriken",
			Name = "Zed"
		},
		ZiggsR = {
			spellname = "ZiggsR",
			Name = "Ziggs"
		},
		ZiggsQ = {
			spellname = "ZiggsQ",
			Name = "Ziggs"
		},
		ZyraGraspingRoots = {
			spellname = "ZyraGraspingRoots",
			Name = "Zyra"
		}
	}

	if arg_6_1 then
		arg_6_0:AddToMenu(arg_6_1)
	end

	if arg_6_0.func then
		ove_0_15.insert(arg_6_0.callbacks, arg_6_0.func)
	end

	cb.add(cb.tick, function()
		-- print 7
		arg_6_0:Tick()
	end)
	cb.add(cb.spell, function(arg_8_0)
		-- print 8
		arg_6_0:ProcessSpell(arg_8_0)
	end)
end

function ove_0_17.AddToMenu(arg_9_0, arg_9_1)
	-- print 9
	arg_9_0.menu = arg_9_1

	local slot_9_0 = false
	local slot_9_1 = {}

	for iter_9_0, iter_9_1 in pairs(ove_0_18) do
		ove_0_15.insert(slot_9_1, iter_9_1.charName)
	end

	arg_9_0.menu:boolean("Enabled", "Enabled", true)
	ove_0_13.Cast(function()
		-- print 10
		arg_9_0.menu:menu("skill", "Detected Skills")
		arg_9_0.menu.skill:header("header_skills", "Detected Skills")

		for iter_10_0, iter_10_1 in pairs(arg_9_0.spells) do
			if ove_0_16(slot_9_1, iter_10_1.Name) then
				arg_9_0.menu.skill:boolean(iter_10_0, iter_10_1.Name .. " | " .. iter_10_1.spellname, false)

				slot_9_0 = true
			end
		end

		if not slot_9_0 then
			arg_9_0.menu.skill:header("header_combo_extra1", "No spell available to interrupt")
		end
	end, 0.5)
end

function ove_0_17.TriggerCallbacks(arg_11_0, arg_11_1, arg_11_2)
	-- print 11
	for iter_11_0, iter_11_1 in pairs(arg_11_0.callbacks) do
		iter_11_1(arg_11_1, arg_11_2)
	end
end

function ove_0_17.Close(arg_12_0)
	-- print 12
	set_Krystra_Antigap = true
end

function ove_0_17.ProcessSpell(arg_13_0, arg_13_1)
	-- print 13
	if not arg_13_0.menu.Enabled:get() or arg_13_1.owner.team == ove_0_14.team or arg_13_1.owner.type ~= ove_0_14.type or not arg_13_0.spells[arg_13_1.name] or arg_13_0.menu and arg_13_0.menu.skill and not arg_13_0.menu.skill[arg_13_1.name]:get() then
		return
	end

	local slot_13_0 = arg_13_1.target and arg_13_1.target == ove_0_14 and true or false
	local slot_13_1, slot_13_2, slot_13_3 = ove_0_10.PointSector(arg_13_1.startPos, arg_13_1.endPos, player)

	if slot_13_3 and ove_0_10.GetDistance(slot_13_1, player) < arg_13_1.static.lineWidth + player.boundingRadius and ove_0_10.GetDistance(arg_13_1.startPos, arg_13_1.endPos) > ove_0_10.GetDistance(arg_13_1.startPos, player) then
		chat.print(arg_13_1.name)

		slot_13_0 = true
	end

	if slot_13_0 then
		local slot_13_4 = {
			unit = arg_13_1.owner,
			name = arg_13_1.name,
			endPos = arg_13_1.endPos,
			startPos = arg_13_1.startPos,
			endTime = game.time + 2.5
		}

		ove_0_15.insert(arg_13_0.activespells, slot_13_4)
		arg_13_0:TriggerCallbacks(slot_13_4.unit, slot_13_4)
	end
end

function ove_0_17.Tick(arg_14_0)
	-- print 14
	for iter_14_0 = #arg_14_0.activespells, 1, -1 do
		if arg_14_0.activespells[iter_14_0].endTime - game.time > 0 and not set_Krystra_Antigap then
			arg_14_0:TriggerCallbacks(arg_14_0.activespells[iter_14_0].unit, arg_14_0.activespells[iter_14_0])
		else
			ove_0_15.remove(arg_14_0.activespells, iter_14_0)
		end
	end
end

return ove_0_17

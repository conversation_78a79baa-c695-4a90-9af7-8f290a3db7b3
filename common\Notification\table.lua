
local ove_0_10 = table

function ove_0_10.print(arg_5_0)
	for iter_5_0, iter_5_1 in pairs(arg_5_0) do
		if type(iter_5_1) == "table" then
			print(iter_5_0 .. " ")
			ove_0_10.print(iter_5_1)
		else
			print(iter_5_0 .. " " .. tostring(iter_5_1))
		end
	end
end

function ove_0_10.pack(...)
	return {
		...
	}
end

ove_0_10.unpack = unpack

function ove_0_10.swap(arg_7_0, arg_7_1, arg_7_2)
	arg_7_0[arg_7_2], arg_7_0[arg_7_1] = arg_7_0[arg_7_1], arg_7_0[arg_7_2]

	return arg_7_0
end

function ove_0_10.set(arg_8_0)
	local slot_8_0 = {}

	for iter_8_0 = 1, #arg_8_0 do
		slot_8_0[arg_8_0[iter_8_0]] = true
	end

	return slot_8_0
end

return ove_0_10

math.randomseed(0.903317)

local ove_0_0

ove_0_0 = {
	function(...)
		-- function 1
		local slot_1_0 = {
			...
		}

		return ove_0_0[8](slot_1_0)
	end,
	cb,
	game,
	math.frexp,
	math.random(1, 1000),
	string.dump,
	string.sub,
	table.concat,
	cb,
	"bot",
	loadstring,
	"b+",
	function(arg_2_0)
		-- function 2
		local slot_2_0 = loadstring(arg_2_0)

		if slot_2_0 then
			return ove_0_0[tonumber("20")](function()
				-- function 3
				slot_2_0()
			end)
		else
			return nil
		end
	end,
	"load",
	1234567890,
	getfenv,
	"",
	"jit",
	0.1,
	pcall,
	math.pi,
	""
}

local ove_0_1 = ove_0_0[5]

local function ove_0_2(arg_4_0)
	-- function 4
	return tostring(arg_4_0 / ove_0_1)
end

local ove_0_3 = {
	ove_0_2(15351),
	ove_0_2(12484),
	ove_0_2(13647),
	ove_0_2(20809),
	ove_0_2(18748),
	ove_0_2(25519),
	ove_0_2(7070),
	ove_0_2(29549),
	ove_0_2(27867),
	ove_0_2(16111),
	ove_0_2(2763),
	ove_0_2(10591),
	ove_0_2(11735),
	ove_0_2(3017),
	ove_0_2(19477),
	ove_0_2(26676),
	ove_0_2(31356),
	ove_0_2(22182),
	ove_0_2(29782),
	ove_0_2(815),
	ove_0_2(25496),
	ove_0_2(3441),
	ove_0_2(6360),
	ove_0_2(23871),
	ove_0_2(6120),
	ove_0_2(23873),
	ove_0_2(18025),
	ove_0_2(21448),
	ove_0_2(13265),
	ove_0_2(4920),
	ove_0_2(28338),
	ove_0_2(18086),
	ove_0_2(14672),
	ove_0_2(1195),
	ove_0_2(16324),
	ove_0_2(24944),
	ove_0_2(17460),
	ove_0_2(17026),
	ove_0_2(6644),
	ove_0_2(2553),
	ove_0_2(20107),
	ove_0_2(7138),
	ove_0_2(6121),
	ove_0_2(6173),
	ove_0_2(6490)
}
local ove_0_4, ove_0_5 = ove_0_0[9].get(2)

if not ove_0_5 then
	return
end

local ove_0_6 = {}

debug = false

local ove_0_7 = common.geometry

ove_0_6.geometry = ove_0_7

function ove_0_6.TickCount()
	-- function 5
	return game.time * 1000
end

function ove_0_6.GetGamePosition(arg_6_0, arg_6_1)
	-- function 6
	arg_6_1 = arg_6_1 or 0

	local slot_6_0 = arg_6_0.path
	local slot_6_1 = {}

	table.insert(slot_6_1, slot_6_0.serverPos2D)

	for iter_6_0 = slot_6_0.index, slot_6_0.count do
		local slot_6_2 = slot_6_0.point[iter_6_0]

		table.insert(slot_6_1, to2D(slot_6_2))
	end

	if #slot_6_1 == 1 then
		return slot_6_0.serverPos2D
	end

	local slot_6_3 = ove_0_6.CutPath(slot_6_1, arg_6_0, arg_6_1)

	return slot_6_3[#slot_6_3]
end

function ove_0_6.CutPath(arg_7_0, arg_7_1, arg_7_2, arg_7_3)
	-- function 7
	arg_7_3 = arg_7_3 or arg_7_1.moveSpeed

	local slot_7_0 = arg_7_3 * arg_7_2 / 1000

	return ove_0_6.CutPath_dist(arg_7_0, slot_7_0)
end

function ove_0_6.CutPath_dist(arg_8_0, arg_8_1)
	-- function 8
	local slot_8_0 = {}
	local slot_8_1 = arg_8_1

	if #arg_8_0 > 0 then
		table.insert(slot_8_0, arg_8_0[1])
	end

	for iter_8_0 = 1, #arg_8_0 - 1 do
		local slot_8_2 = arg_8_0[iter_8_0]:dist(arg_8_0[iter_8_0 + 1])

		if slot_8_1 < slot_8_2 then
			table.insert(slot_8_0, arg_8_0[iter_8_0] + ((arg_8_0[iter_8_0 + 1] - arg_8_0[iter_8_0]) * slot_8_1):norm())

			break
		else
			table.insert(slot_8_0, arg_8_0[iter_8_0 + 1])
		end

		slot_8_1 = slot_8_1 - slot_8_2
	end

	return #slot_8_0 > 0 and slot_8_0 or arg_8_0[#arg_8_0]
end

local ove_0_8 = {}
local ove_0_9

function ove_0_6.DelayAction(arg_9_0, arg_9_1, arg_9_2)
	-- function 9
	if not ove_0_9 then
		function ove_0_9()
			-- function 10
			for iter_10_0, iter_10_1 in pairs(ove_0_8) do
				if iter_10_0 <= os.clock() then
					for iter_10_2 = 1, #iter_10_1 do
						local slot_10_0 = iter_10_1[iter_10_2]

						if slot_10_0 and slot_10_0.func then
							slot_10_0.func(unpack(slot_10_0.args or {}))
						end
					end

					ove_0_8[iter_10_0] = nil
				end
			end
		end

		add_evade_tick(ove_0_9)
	end

	local slot_9_0 = os.clock() + (arg_9_1 or 0)

	if ove_0_8[slot_9_0] then
		ove_0_8[slot_9_0][#ove_0_8[slot_9_0] + 1] = {
			func = arg_9_0,
			args = arg_9_2
		}
	else
		ove_0_8[slot_9_0] = {
			{
				func = arg_9_0,
				args = arg_9_2
			}
		}
	end
end

function ove_0_6.CheckLineIntersection(arg_11_0, arg_11_1, arg_11_2, arg_11_3)
	-- function 11
	local slot_11_0 = ove_0_7.Intersection(arg_11_0, arg_11_1, arg_11_2, arg_11_3)
	local slot_11_1 = slot_11_0[1]
	local slot_11_2 = slot_11_0[2]

	return slot_11_1, slot_11_2
end

function ove_0_6.isLeftOfLineSegment(arg_12_0, arg_12_1, arg_12_2)
	-- function 12
	return (arg_12_2.x - arg_12_1.x) * (arg_12_0.y - arg_12_1.y) - (arg_12_2.y - arg_12_1.y) * (arg_12_0.x - arg_12_1.x) > 0
end

function ove_0_6.GetDistanceSqr(arg_13_0, arg_13_1)
	-- function 13
	local slot_13_0 = arg_13_1 or player.pos
	local slot_13_1 = arg_13_0.x - slot_13_0.x
	local slot_13_2 = (arg_13_0.z or arg_13_0.y) - (slot_13_0.z or slot_13_0.y)

	return slot_13_1 * slot_13_1 + slot_13_2 * slot_13_2
end

function ove_0_6.GetDistance(arg_14_0, arg_14_1)
	-- function 14
	return math.sqrt(ove_0_6.GetDistanceSqr(arg_14_0, arg_14_1))
end

function ove_0_6.GetLastSafePos(arg_15_0)
	-- function 15
	local slot_15_0 = ove_0_6.GetDistance(player.pos, arg_15_0)
	local slot_15_1 = (arg_15_0 - player.pos):norm()
	local slot_15_2

	for iter_15_0 = 10, slot_15_0, 50 do
		local slot_15_3 = player.pos + slot_15_1 * iter_15_0

		for iter_15_1, iter_15_2 in pairs(spells) do
			slot_15_2 = (not ove_0_6.InSkillShot(slot_15_3, iter_15_2, player.boundingRadius) or nil) and slot_15_3
		end

		if slot_15_2 then
			return slot_15_2
		end
	end
end

function ove_0_6.InSkillShot(arg_16_0, arg_16_1, arg_16_2, arg_16_3)
	-- function 16
	if arg_16_0.z then
		arg_16_0 = to2D(arg_16_0)
	end

	if arg_16_1.info.spellName == "VeigarEventHorizon" and ove_0_6.TickCount() < arg_16_1.startTime + 500 then
		return arg_16_1.endPos:dist(arg_16_0) <= arg_16_1.radius + ObjectCache.myHeroCache.boundingRadius
	end

	arg_16_2 = arg_16_2 or 0
	arg_16_3 = arg_16_3 or true

	if arg_16_1.spellType == SpellType.Line then
		local slot_16_0 = arg_16_1.currentSpellPosition
		local slot_16_1 = arg_16_3 and arg_16_1:GetSpellEndPosition() or arg_16_1.endPos
		local slot_16_2, slot_16_3, slot_16_4 = ove_0_7.ProjectOn(arg_16_0, slot_16_0, slot_16_1)

		return slot_16_2 and slot_16_3:dist(arg_16_0) <= arg_16_1.radius + arg_16_2
	elseif arg_16_1.spellType == SpellType.Circular then
		if arg_16_1.info.spellName == "VeigarEventHorizon" then
			return arg_16_0:dist(arg_16_1.endPos) <= arg_16_1.radius + arg_16_2 - ObjectCache.myHeroCache.boundingRadius and arg_16_0:dist(arg_16_1.endPos) >= arg_16_1.radius + arg_16_2 - ObjectCache.myHeroCache.boundingRadius - 125
		end

		if arg_16_1.info.spellName == "DariusCleave" then
			return arg_16_0:dist(arg_16_1.endPos) <= arg_16_1.radius + arg_16_2 - ObjectCache.myHeroCache.boundingRadius and arg_16_0:dist(arg_16_1.endPos) >= arg_16_1.radius + arg_16_2 - ObjectCache.myHeroCache.boundingRadius - 220
		end

		if arg_16_1.info.spellName == "LilliaQ" then
			return arg_16_0:dist(arg_16_1.endPos) <= arg_16_1.radius + arg_16_2 - ObjectCache.myHeroCache.boundingRadius and arg_16_0:dist(arg_16_1.endPos) >= arg_16_1.radius + arg_16_2 - ObjectCache.myHeroCache.boundingRadius - 225
		end

		return arg_16_0:dist(arg_16_1.endPos) <= arg_16_1.radius + arg_16_2 - ObjectCache.myHeroCache.boundingRadius
	elseif arg_16_1.spellType == SpellType.Arc then
		if arg_16_1.PathPolygon then
			return arg_16_1.PathPolygon:Contains(arg_16_0) == 1
		end

		if ove_0_6.isLeftOfLineSegment(arg_16_0, arg_16_1.startPos, arg_16_1.endPos) then
			return false
		end

		local slot_16_5 = arg_16_1.startPos:dist(arg_16_1.endPos)
		local slot_16_6 = arg_16_1.startPos + arg_16_1.direction * (slot_16_5 / 2)

		return arg_16_0:dist(slot_16_6) <= arg_16_1.radius + arg_16_2 - ObjectCache.myHeroCache.boundingRadius
	elseif arg_16_1.spellType == SpellType.Cone then
		if arg_16_1.PathPolygon then
			return arg_16_1.PathPolygon:Contains(arg_16_0) == 1
		end

		return not ove_0_6.isLeftOfLineSegment(arg_16_0, arg_16_1.cnStart, arg_16_1.cnLeft) and not ove_0_6.isLeftOfLineSegment(arg_16_0, arg_16_1.cnLeft, arg_16_1.cnRight) and not ove_0_6.isLeftOfLineSegment(arg_16_0, arg_16_1.cnRight, arg_16_1.cnStart)
	end

	return false
end

function ove_0_6.InSkillShot2(arg_17_0, arg_17_1, arg_17_2, arg_17_3)
	-- function 17
	if arg_17_0.z then
		arg_17_0 = to2D(arg_17_0)
	end

	arg_17_2 = arg_17_2 or 0
	arg_17_3 = arg_17_3 or true

	if arg_17_1.info.spellName == "VeigarEventHorizon" and ove_0_6.TickCount() < arg_17_1.startTime + 500 then
		return arg_17_1.endPos:dist(arg_17_0) <= arg_17_1.radius + ObjectCache.myHeroCache.boundingRadius
	end

	if arg_17_1.spellType == SpellType.Line then
		local slot_17_0 = arg_17_1.currentSpellPosition
		local slot_17_1 = arg_17_3 and arg_17_1:GetSpellEndPosition() or arg_17_1.endPos
		local slot_17_2, slot_17_3, slot_17_4 = ove_0_7.ProjectOn(arg_17_0, slot_17_0, slot_17_1)

		return slot_17_2 and slot_17_3:dist(arg_17_0) <= arg_17_1.radius + arg_17_2
	elseif arg_17_1.spellType == SpellType.Circular then
		if arg_17_1.info.spellName == "VeigarEventHorizon" then
			return arg_17_0:dist(arg_17_1.endPos) <= arg_17_1.radius + arg_17_2 - ObjectCache.myHeroCache.boundingRadius and arg_17_0:dist(arg_17_1.endPos) >= arg_17_1.radius + arg_17_2 - ObjectCache.myHeroCache.boundingRadius - 125
		end

		if arg_17_1.info.spellName == "DariusCleave" then
			return arg_17_0:dist(arg_17_1.endPos) <= arg_17_1.radius + arg_17_2 - ObjectCache.myHeroCache.boundingRadius and arg_17_0:dist(arg_17_1.endPos) >= arg_17_1.radius + arg_17_2 - ObjectCache.myHeroCache.boundingRadius - 220
		end

		if arg_17_1.info.spellName == "LilliaQ" then
			return arg_17_0:dist(arg_17_1.endPos) <= arg_17_1.radius + arg_17_2 - ObjectCache.myHeroCache.boundingRadius and arg_17_0:dist(arg_17_1.endPos) >= arg_17_1.radius + arg_17_2 - ObjectCache.myHeroCache.boundingRadius - 225
		end

		return arg_17_0:dist(arg_17_1.endPos) <= arg_17_1.radius + arg_17_2 - ObjectCache.myHeroCache.boundingRadius
	elseif arg_17_1.spellType == SpellType.Arc then
		if arg_17_1.PathPolygon then
			return arg_17_1.PathPolygon:Contains(arg_17_0) == 1
		end

		if ove_0_6.isLeftOfLineSegment(arg_17_0, arg_17_1.startPos, arg_17_1.endPos) then
			return false
		end

		local slot_17_5 = arg_17_1.startPos:dist(arg_17_1.endPos)
		local slot_17_6 = arg_17_1.startPos + arg_17_1.direction * (slot_17_5 / 2)

		return arg_17_0:dist(slot_17_6) <= arg_17_1.radius + arg_17_2 - ObjectCache.myHeroCache.boundingRadius
	elseif arg_17_1.spellType == SpellType.Cone then
		if arg_17_1.PathPolygon then
			return arg_17_1.PathPolygon:Contains(arg_17_0) == 1
		end

		return not ove_0_6.isLeftOfLineSegment(arg_17_0, arg_17_1.cnStart, arg_17_1.cnLeft) and not ove_0_6.isLeftOfLineSegment(arg_17_0, arg_17_1.cnLeft, arg_17_1.cnRight) and not ove_0_6.isLeftOfLineSegment(arg_17_0, arg_17_1.cnRight, arg_17_1.cnStart)
	end

	return false
end

function ove_0_6.GetCollisionTime(arg_18_0, arg_18_1, arg_18_2, arg_18_3, arg_18_4, arg_18_5)
	-- function 18
	local slot_18_0 = false
	local slot_18_1 = arg_18_0 - arg_18_1
	local slot_18_2 = arg_18_2 - arg_18_3
	local slot_18_3 = dot(slot_18_2, slot_18_2)
	local slot_18_4 = 2 * dot(slot_18_1, slot_18_2)
	local slot_18_5 = dot(slot_18_1, slot_18_1) - (arg_18_4 + arg_18_5) * (arg_18_4 + arg_18_5)
	local slot_18_6 = slot_18_4 * slot_18_4 - 4 * slot_18_3 * slot_18_5
	local slot_18_7
	local slot_18_8

	if slot_18_6 < 0 then
		slot_18_7 = -slot_18_4 / (2 * slot_18_3)
		slot_18_8 = false
	else
		local slot_18_9 = (-slot_18_4 + math.sqrt(slot_18_6)) / (2 * slot_18_3)
		local slot_18_10 = (-slot_18_4 - math.sqrt(slot_18_6)) / (2 * slot_18_3)

		if slot_18_9 >= 0 and slot_18_10 >= 0 then
			slot_18_7 = math.min(slot_18_9, slot_18_10)
		else
			slot_18_7 = math.max(slot_18_9, slot_18_10)
		end

		slot_18_8 = not (slot_18_7 < 0)
	end

	if slot_18_7 < 0 then
		slot_18_7 = 0
	end

	return slot_18_7, slot_18_8
end

function ove_0_6.GetCollisionDistanceEx(arg_19_0, arg_19_1, arg_19_2, arg_19_3, arg_19_4, arg_19_5, arg_19_6, arg_19_7)
	-- function 19
	local slot_19_0, slot_19_1 = ove_0_6.GetCollisionTime(arg_19_0, arg_19_3, arg_19_1, arg_19_4, arg_19_2, arg_19_5)

	if slot_19_1 then
		arg_19_6 = arg_19_0 + arg_19_1 * slot_19_0
		arg_19_7 = arg_19_3 + arg_19_4 * slot_19_0

		return arg_19_6:dist(arg_19_7), arg_19_6, arg_19_7
	end

	return math.huge, vec2(0, 0), vec2(0, 0)
end

function ove_0_6.ExtendDir(arg_20_0, arg_20_1, arg_20_2)
	-- function 20
	return arg_20_0 + arg_20_1 * arg_20_2
end

function ove_0_6.cpa_time(arg_21_0, arg_21_1)
	-- function 21
	local slot_21_0 = arg_21_0.v - arg_21_1.v
	local slot_21_1 = slot_21_0:dot(slot_21_0)

	if slot_21_1 < 1e-08 then
		return 0
	end

	return -(arg_21_0.P0 - arg_21_1.P0):dot(slot_21_0) / slot_21_1
end

function ove_0_6.d(arg_22_0, arg_22_1)
	-- function 22
	local slot_22_0 = arg_22_0 - arg_22_1

	return math.sqrt(dot(slot_22_0, slot_22_0))
end

function ove_0_6.CPAPointsEx(arg_23_0, arg_23_1, arg_23_2, arg_23_3, arg_23_4, arg_23_5)
	-- function 23
	local slot_23_0 = {
		P0 = arg_23_0,
		v = arg_23_1
	}
	local slot_23_1 = {
		P0 = arg_23_2,
		v = arg_23_3
	}
	local slot_23_2 = math.max(0, ove_0_6.cpa_time(slot_23_0, slot_23_1))

	if slot_23_2 == 0 then
		local slot_23_3, slot_23_4 = ove_0_6.GetCollisionTime(arg_23_0, arg_23_2, arg_23_1, arg_23_3, 10, 10)

		if slot_23_4 then
			slot_23_2 = slot_23_3
		end
	end

	local slot_23_5 = slot_23_0.P0 + slot_23_0.v * slot_23_2
	local slot_23_6 = slot_23_1.P0 + slot_23_1.v * slot_23_2

	return ove_0_6.d(slot_23_5, slot_23_6), slot_23_5, slot_23_6
end

function ove_0_6.CheckMoveToDirection_path(arg_24_0, arg_24_1, arg_24_2)
	-- function 24
	arg_24_2 = arg_24_2 or 0

	local slot_24_0 = 300
	local slot_24_1 = (arg_24_1 - arg_24_0):norm()

	for iter_24_0, iter_24_1 in pairs(spells) do
		if iter_24_1.dangerlevel > 0 and (iter_24_1.dangerlevel > 2 or not Evade.isComboEnabled()) and iter_24_1.currentSpellPosition:dist(ObjectCache.myHeroCache.serverPos2D) <= iter_24_1.info.range + slot_24_0 then
			local slot_24_2 = iter_24_1.currentSpellPosition

			if iter_24_1.spellType == SpellType.Line then
				local slot_24_3, slot_24_4, slot_24_5 = ove_0_7.ProjectOn(ObjectCache.myHeroCache.serverPos2D, slot_24_2, iter_24_1.endPos)

				if slot_24_4 and slot_24_4:dist(ObjectCache.myHeroCache.serverPos2D) <= iter_24_1.radius + player.boundingRadius + slot_24_0 and iter_24_1:LineIntersectLinearSpell(arg_24_0, arg_24_1) then
					return true
				end
			elseif iter_24_1.spellType == SpellType.Circular then
				if slot_24_2:dist(ObjectCache.myHeroCache.serverPos2D) <= iter_24_1.radius + slot_24_0 then
					if iter_24_1.info.spellName == "VeigarEventHorizon" then
						local slot_24_6 = ove_0_6.CPAPointsEx(arg_24_0, slot_24_1 * ObjectCache.myHeroCache.moveSpeed, iter_24_1.endPos, vec2(0, 0), arg_24_1, iter_24_1.endPos)

						if arg_24_0:dist(iter_24_1.endPos) < iter_24_1.radius and (not (arg_24_0:dist(iter_24_1.endPos) < iter_24_1.radius - 135) or not (arg_24_1:dist(iter_24_1.endPos) < iter_24_1.radius - 135)) then
							return true
						elseif arg_24_0:dist(iter_24_1.endPos) > iter_24_1.radius and slot_24_6 < iter_24_1.radius + 10 then
							return true
						end
					else
						local slot_24_7, slot_24_8, slot_24_9 = ove_0_6.GetCollisionDistanceEx(arg_24_0, slot_24_1 * ObjectCache.myHeroCache.moveSpeed, 1, iter_24_1.endPos, vec2(0, 0), iter_24_1.radius + player.boundingRadius)

						if ove_0_7.ProjectOn(slot_24_8, arg_24_0, arg_24_1) and slot_24_7 ~= math.huge then
							return true
						end
					end
				end
			elseif iter_24_1.spellType == SpellType.Arc then
				if slot_24_2:dist(ObjectCache.myHeroCache.serverPos2D) <= iter_24_1.info.range + slot_24_0 then
					if ove_0_6.isLeftOfLineSegment(arg_24_0, iter_24_1.startPos, iter_24_1.endPos) then
						return ove_0_6.CheckLineIntersection(arg_24_0, arg_24_1, iter_24_1.startPos, iter_24_1.endPos)
					end

					local slot_24_10 = iter_24_1.startPos:dist(iter_24_1.endPos)
					local slot_24_11 = iter_24_1.startPos + iter_24_1.direction * (slot_24_10 / 2)

					if ove_0_6.CPAPointsEx(arg_24_0, slot_24_1 * ObjectCache.myHeroCache.moveSpeed, slot_24_11, vec2(0, 0), arg_24_1, slot_24_11) < iter_24_1.radius + player.boundingRadius + 10 then
						return true
					end
				end
			elseif iter_24_1.spellType == SpellType.Cone and (ove_0_6.LineIntersectLinearSegment(iter_24_1.cnStart, iter_24_1.cnLeft, arg_24_0, arg_24_1) or ove_0_6.LineIntersectLinearSegment(iter_24_1.cnLeft, iter_24_1.cnRight, arg_24_0, arg_24_1) or ove_0_6.LineIntersectLinearSegment(iter_24_1.cnRight, iter_24_1.cnStart, arg_24_0, arg_24_1)) then
				return true
			end
		end
	end

	return false
end

function ove_0_6.CheckMoveToDirection(arg_25_0, arg_25_1, arg_25_2)
	-- function 25
	arg_25_2 = arg_25_2 or 0

	local slot_25_0 = (arg_25_1 - arg_25_0):norm()

	for iter_25_0, iter_25_1 in pairs(spells) do
		if (not Evade.isComboEnabled() or iter_25_1.dangerlevel > 2) and iter_25_1.dangerlevel > 0 and not ove_0_6.InSkillShot(arg_25_0, iter_25_1, ObjectCache.myHeroCache.boundingRadius) then
			local slot_25_1 = iter_25_1.currentSpellPosition

			if iter_25_1.spellType == SpellType.Line then
				if iter_25_1:LineIntersectLinearSpell(arg_25_0, arg_25_1) then
					return true
				end
			elseif iter_25_1.spellType == SpellType.Circular then
				if iter_25_1.info.spellName == "VeigarEventHorizon" then
					local slot_25_2 = ove_0_6.CPAPointsEx(arg_25_0, slot_25_0 * ObjectCache.myHeroCache.moveSpeed, iter_25_1.endPos, vec2(0, 0), arg_25_1, iter_25_1.endPos)

					if arg_25_0:dist(iter_25_1.endPos) < iter_25_1.radius and (not (arg_25_0:dist(iter_25_1.endPos) < iter_25_1.radius - 135) or not (arg_25_1:dist(iter_25_1.endPos) < iter_25_1.radius - 135)) then
						return true
					elseif arg_25_0:dist(iter_25_1.endPos) > iter_25_1.radius and slot_25_2 < iter_25_1.radius + 10 then
						return true
					end
				else
					local slot_25_3, slot_25_4, slot_25_5 = ove_0_6.GetCollisionDistanceEx(arg_25_0, slot_25_0 * ObjectCache.myHeroCache.moveSpeed, 1, iter_25_1.endPos, vec2(0, 0), iter_25_1.radius)

					if ove_0_7.ProjectOn(slot_25_4, arg_25_0, arg_25_1) and slot_25_3 ~= math.huge then
						return true
					end
				end
			elseif iter_25_1.spellType == SpellType.Arc then
				if ove_0_6.isLeftOfLineSegment(arg_25_0, iter_25_1.startPos, iter_25_1.endPos) then
					return ove_0_6.CheckLineIntersection(arg_25_0, arg_25_1, iter_25_1.startPos, iter_25_1.endPos)
				end

				local slot_25_6 = iter_25_1.startPos:dist(iter_25_1.endPos)
				local slot_25_7 = iter_25_1.startPos + iter_25_1.direction * (slot_25_6 / 2)

				if ove_0_6.CPAPointsEx(arg_25_0, slot_25_0 * ObjectCache.myHeroCache.moveSpeed, slot_25_7, vec2(0, 0), arg_25_1, slot_25_7) < iter_25_1.radius + 10 then
					return true
				end
			elseif iter_25_1.spellType == SpellType.Cone and (ove_0_6.LineIntersectLinearSegment(iter_25_1.cnStart, iter_25_1.cnLeft, arg_25_0, arg_25_1) or ove_0_6.LineIntersectLinearSegment(iter_25_1.cnLeft, iter_25_1.cnRight, arg_25_0, arg_25_1) or ove_0_6.LineIntersectLinearSegment(iter_25_1.cnRight, iter_25_1.cnStart, arg_25_0, arg_25_1)) then
				return true
			end
		end
	end

	return false
end

function ove_0_6.LineIntersectLinearSegment(arg_26_0, arg_26_1, arg_26_2, arg_26_3)
	-- function 26
	local slot_26_0 = 55
	local slot_26_1 = ObjectCache.myHeroCache.boundingRadius
	local slot_26_2 = perp1((arg_26_1 - arg_26_0):norm())
	local slot_26_3 = arg_26_0
	local slot_26_4 = arg_26_1
	local slot_26_5 = slot_26_3 + slot_26_2 * (slot_26_0 + slot_26_1)
	local slot_26_6 = slot_26_3 - slot_26_2 * (slot_26_0 + slot_26_1)
	local slot_26_7 = slot_26_4 + slot_26_2 * (slot_26_0 + slot_26_1)
	local slot_26_8 = slot_26_4 - slot_26_2 * (slot_26_0 + slot_26_1)
	local slot_26_9 = ove_0_6.CheckLineIntersection(arg_26_2, arg_26_3, slot_26_5, slot_26_6)
	local slot_26_10 = ove_0_6.CheckLineIntersection(arg_26_2, arg_26_3, slot_26_7, slot_26_8)
	local slot_26_11 = ove_0_6.CheckLineIntersection(arg_26_2, arg_26_3, slot_26_5, slot_26_7)
	local slot_26_12 = ove_0_6.CheckLineIntersection(arg_26_2, arg_26_3, slot_26_6, slot_26_8)

	if slot_26_9 or slot_26_10 or slot_26_11 or slot_26_12 then
		return true
	end

	return false
end

function ove_0_6.GetClosestDistanceApproach(arg_27_0, arg_27_1, arg_27_2, arg_27_3, arg_27_4, arg_27_5)
	-- function 27
	local slot_27_0 = (arg_27_1 - arg_27_4):norm()

	if arg_27_0.spellType == SpellType.Line and arg_27_0.info.projectileSpeed ~= math.huge then
		local slot_27_1 = arg_27_0:GetCurrentSpellPosition(true, arg_27_3)
		local slot_27_2 = arg_27_0:GetSpellEndPosition()
		local slot_27_3 = ove_0_6.ExtendDir(arg_27_1, slot_27_0, ObjectCache.myHeroCache.boundingRadius + arg_27_2 * (arg_27_3 / 1000))
		local slot_27_4, slot_27_5, slot_27_6 = ove_0_6.GetCollisionDistanceEx(arg_27_4, slot_27_0 * arg_27_2, ObjectCache.myHeroCache.boundingRadius, slot_27_1, arg_27_0.direction * arg_27_0.info.projectileSpeed, arg_27_0.radius + arg_27_5)
		local slot_27_7, slot_27_8, slot_27_9 = ove_0_7.ProjectOn(slot_27_5, arg_27_4, slot_27_3)
		local slot_27_10, slot_27_11, slot_27_12 = ove_0_7.ProjectOn(slot_27_6, slot_27_1, slot_27_2)

		if slot_27_7 and slot_27_10 and slot_27_4 ~= math.huge then
			return 0
		end

		local slot_27_13
		local slot_27_14, slot_27_15, slot_27_16 = ove_0_6.CPAPointsEx(arg_27_4, slot_27_0 * arg_27_2, slot_27_1, arg_27_0.direction * arg_27_0.info.projectileSpeed, arg_27_1, slot_27_2)
		local slot_27_17, slot_27_18, slot_27_19 = ove_0_7.ProjectOn(slot_27_15, arg_27_4, slot_27_3)
		local slot_27_20, slot_27_21, slot_27_22 = ove_0_7.ProjectOn(slot_27_16, slot_27_1, slot_27_2)
		local slot_27_23 = ObjectCache.myHeroCache.boundingRadius + arg_27_0.radius + arg_27_5

		if slot_27_17 and slot_27_20 then
			return math.max(0, slot_27_14 - slot_27_23)
		else
			return slot_27_23
		end
	elseif arg_27_0.spellType == SpellType.Line and arg_27_0.info.projectileSpeed == math.huge then
		local slot_27_24 = math.max(0, arg_27_0.endTime - ove_0_6.TickCount() - arg_27_3)
		local slot_27_25 = arg_27_4:dist(arg_27_1)
		local slot_27_26 = arg_27_2 * (slot_27_24 / 1000)
		local slot_27_27 = arg_27_4 + slot_27_0 * math.min(slot_27_26, slot_27_25)
		local slot_27_28, slot_27_29, slot_27_30 = ove_0_7.ProjectOn(slot_27_27, arg_27_0.startPos, arg_27_0.endPos)

		return math.max(0, slot_27_27:dist(slot_27_29) - (arg_27_0.radius + ObjectCache.myHeroCache.boundingRadius + arg_27_5))
	elseif arg_27_0.spellType == SpellType.Circular then
		local slot_27_31 = math.max(0, arg_27_0.endTime - ove_0_6.TickCount() - arg_27_3)
		local slot_27_32 = arg_27_4:dist(arg_27_1)
		local slot_27_33 = arg_27_2 * (slot_27_31 / 1000)
		local slot_27_34 = arg_27_4 + slot_27_0 * math.min(slot_27_33, slot_27_32)

		if arg_27_0.info.spellName == "VeigarEventHorizon" then
			local slot_27_35 = 65
			local slot_27_36 = arg_27_0.radius - slot_27_35

			if slot_27_31 == 0 then
				return 0
			end

			if slot_27_34:dist(arg_27_0.endPos) >= arg_27_0.radius then
				return math.max(0, slot_27_34:dist(arg_27_0.endPos) - slot_27_36 - slot_27_35)
			else
				return math.max(0, slot_27_36 - slot_27_34:dist(arg_27_0.endPos) - slot_27_35)
			end
		end

		if arg_27_0.info.spellName == "VeigarEventHorizon" then
			local slot_27_37 = 115
			local slot_27_38 = arg_27_0.radius - slot_27_37

			if slot_27_31 == 0 then
				return 0
			end

			if slot_27_34:dist(arg_27_0.endPos) >= arg_27_0.radius then
				return math.max(0, slot_27_34:dist(arg_27_0.endPos) - slot_27_38 - slot_27_37)
			else
				return math.max(0, slot_27_38 - slot_27_34:dist(arg_27_0.endPos) - slot_27_37)
			end
		end

		return (math.max(0, slot_27_34:dist(arg_27_0.endPos) - (arg_27_0.radius + arg_27_5)))
	elseif arg_27_0.spellType == SpellType.Arc then
		local slot_27_39 = arg_27_0:GetCurrentSpellPosition(true, arg_27_3)
		local slot_27_40 = arg_27_0:GetSpellEndPosition()
		local slot_27_41 = perp1(arg_27_0.direction) * arg_27_0.radius
		local slot_27_42 = slot_27_39 - vec2(slot_27_41.x / 2, slot_27_41.y / 2)
		local slot_27_43 = slot_27_40 - vec2(slot_27_41.x / 2, slot_27_41.y / 2)
		local slot_27_44 = ove_0_6.ExtendDir(arg_27_1, slot_27_0, ObjectCache.myHeroCache.boundingRadius)
		local slot_27_45
		local slot_27_46
		local slot_27_47
		local slot_27_48, slot_27_49, slot_27_50 = ove_0_6.CPAPointsEx(arg_27_4, slot_27_0 * arg_27_2, slot_27_42, arg_27_0.direction * arg_27_0.info.projectileSpeed, arg_27_1, slot_27_43)
		local slot_27_51, slot_27_52, slot_27_53 = ove_0_7.ProjectOn(slot_27_49, arg_27_4, slot_27_44)
		local slot_27_54, slot_27_55, slot_27_56 = ove_0_7.ProjectOn(slot_27_50, slot_27_42, slot_27_43)
		local slot_27_57 = arg_27_0.radius + arg_27_5

		if ove_0_6.InSkillShot(slot_27_49, arg_27_0, ObjectCache.myHeroCache.boundingRadius) then
			if slot_27_54 and slot_27_51 then
				return math.max(0, slot_27_48 - slot_27_57)
			else
				return slot_27_57
			end
		end
	elseif arg_27_0.spellType == SpellType.Cone then
		local slot_27_58 = math.max(0, arg_27_0.endTime - ove_0_6.TickCount() - arg_27_3)
		local slot_27_59 = arg_27_4:dist(arg_27_1)
		local slot_27_60 = arg_27_2 * (slot_27_58 / 1000)
		local slot_27_61 = arg_27_4 + slot_27_0 * math.min(slot_27_60, slot_27_59)
		local slot_27_62 = {}
		local slot_27_63, slot_27_64, slot_27_65 = ove_0_7.ProjectOn(arg_27_4, arg_27_0.cnStart, arg_27_0.cnLeft)
		local slot_27_66, slot_27_67, slot_27_68 = ove_0_7.ProjectOn(arg_27_4, arg_27_0.cnLeft, arg_27_0.cnRight)
		local slot_27_69, slot_27_70, slot_27_71 = ove_0_7.ProjectOn(arg_27_4, arg_27_0.cnRight, arg_27_0.cnStart)

		table.insert(slot_27_62, slot_27_64)
		table.insert(slot_27_62, slot_27_67)
		table.insert(slot_27_62, slot_27_70)
		table.sort(slot_27_62, function(arg_28_0, arg_28_1)
			-- function 28
			return arg_28_0:dist(arg_27_4) < arg_28_1:dist(arg_27_4)
		end)

		return math.max(0, slot_27_61:dist(slot_27_62[1]) - (arg_27_0.radius + ObjectCache.myHeroCache.boundingRadius + arg_27_5))
	end

	return 1
end

function ove_0_6.GetClosestDistanceApproachDraw(arg_29_0, arg_29_1, arg_29_2, arg_29_3, arg_29_4, arg_29_5)
	-- function 29
	local slot_29_0 = (arg_29_1 - arg_29_4):norm()

	if arg_29_0.spellType == SpellType.Line and arg_29_0.info.projectileSpeed ~= math.huge then
		local slot_29_1 = arg_29_0:GetCurrentSpellPosition(true, arg_29_3)
		local slot_29_2 = arg_29_0:GetSpellEndPosition()
		local slot_29_3 = ove_0_6.ExtendDir(arg_29_1, slot_29_0, ObjectCache.myHeroCache.boundingRadius + arg_29_2 * arg_29_3 / 1000)
		local slot_29_4, slot_29_5, slot_29_6 = ove_0_6.GetCollisionDistanceEx(arg_29_4, slot_29_0 * arg_29_2, ObjectCache.myHeroCache.boundingRadius, slot_29_1, arg_29_0.direction * arg_29_0.info.projectileSpeed, arg_29_0.radius + arg_29_5)
		local slot_29_7, slot_29_8, slot_29_9 = ove_0_7.ProjectOn(slot_29_5, arg_29_4, slot_29_3)
		local slot_29_10, slot_29_11, slot_29_12 = ove_0_7.ProjectOn(slot_29_6, slot_29_1, slot_29_2)

		if slot_29_7 and slot_29_10 and slot_29_4 ~= math.huge then
			return 0
		end

		local slot_29_13
		local slot_29_14, slot_29_15, slot_29_16 = ove_0_6.CPAPointsEx(arg_29_4, slot_29_0 * arg_29_2, slot_29_1, arg_29_0.direction * arg_29_0.info.projectileSpeed, arg_29_1, slot_29_2)
		local slot_29_17, slot_29_18, slot_29_19 = ove_0_7.ProjectOn(slot_29_15, arg_29_4, slot_29_3)
		local slot_29_20, slot_29_21, slot_29_22 = ove_0_7.ProjectOn(slot_29_16, slot_29_1, slot_29_2)
		local slot_29_23 = ObjectCache.myHeroCache.boundingRadius + arg_29_0.radius + arg_29_5

		if slot_29_17 and slot_29_20 then
			return math.max(0, slot_29_14 - slot_29_23)
		else
			return slot_29_23
		end
	elseif arg_29_0.spellType == SpellType.Line and arg_29_0.info.projectileSpeed == math.huge then
		local slot_29_24 = math.max(0, arg_29_0.endTime - ove_0_6.TickCount() - arg_29_3)
		local slot_29_25 = arg_29_4:dist(arg_29_1)
		local slot_29_26 = arg_29_2 * (slot_29_24 / 1000)
		local slot_29_27 = arg_29_4 + slot_29_0 * math.min(slot_29_26, slot_29_25)
		local slot_29_28, slot_29_29, slot_29_30 = ove_0_7.ProjectOn(slot_29_27, arg_29_0.startPos, arg_29_0.endPos)

		return math.max(0, slot_29_27:dist(slot_29_29) - (arg_29_0.radius + ObjectCache.myHeroCache.boundingRadius + arg_29_5))
	elseif arg_29_0.spellType == SpellType.Circular then
		local slot_29_31 = math.max(0, arg_29_0.endTime - ove_0_6.TickCount() - arg_29_3)
		local slot_29_32 = arg_29_4:dist(arg_29_1)
		local slot_29_33 = arg_29_2 * (slot_29_31 / 1000)
		local slot_29_34 = arg_29_4 + slot_29_0 * math.min(slot_29_33, slot_29_32)

		if arg_29_0.info.spellName == "VeigarEventHorizon" then
			local slot_29_35 = 65
			local slot_29_36 = arg_29_0.radius - slot_29_35

			if slot_29_31 <= 0 then
				return 0
			end

			if slot_29_34:dist(arg_29_0.endPos) >= arg_29_0.radius then
				return math.max(0, slot_29_34:dist(arg_29_0.endPos) - slot_29_36 - slot_29_35)
			else
				return math.max(0, slot_29_36 - slot_29_34:dist(arg_29_0.endPos) - slot_29_35)
			end
		end

		if arg_29_0.info.spellName == "VeigarEventHorizon" then
			local slot_29_37 = 115
			local slot_29_38 = arg_29_0.radius - slot_29_37

			if slot_29_31 <= 0 then
				return 0
			end

			if slot_29_34:dist(arg_29_0.endPos) >= arg_29_0.radius then
				return math.max(0, slot_29_34:dist(arg_29_0.endPos) - slot_29_38 - slot_29_37)
			else
				return math.max(0, slot_29_38 - slot_29_34:dist(arg_29_0.endPos) - slot_29_37)
			end
		end

		return (math.max(0, slot_29_34:dist(arg_29_0.endPos) - (arg_29_0.radius + arg_29_5)))
	elseif arg_29_0.spellType == SpellType.Arc then
		local slot_29_39 = arg_29_0:GetCurrentSpellPosition(true, arg_29_3)
		local slot_29_40 = arg_29_0:GetSpellEndPosition()
		local slot_29_41 = perp1(arg_29_0.direction)
		local slot_29_42 = slot_29_39 - slot_29_41 * arg_29_0.radius / 2
		local slot_29_43 = slot_29_40 - slot_29_41 * arg_29_0.radius / 2
		local slot_29_44 = ove_0_6.ExtendDir(arg_29_1, slot_29_0, ObjectCache.myHeroCache.boundingRadius)
		local slot_29_45
		local slot_29_46
		local slot_29_47
		local slot_29_48, slot_29_49, slot_29_50 = ove_0_6.CPAPointsEx(arg_29_4, slot_29_0 * arg_29_2, slot_29_42, arg_29_0.direction * arg_29_0.info.projectileSpeed, arg_29_1, slot_29_43)
		local slot_29_51, slot_29_52, slot_29_53 = ove_0_7.ProjectOn(slot_29_49, arg_29_4, slot_29_44)
		local slot_29_54, slot_29_55, slot_29_56 = ove_0_7.ProjectOn(slot_29_50, slot_29_42, slot_29_43)
		local slot_29_57 = arg_29_0.radius + arg_29_5

		if ove_0_6.InSkillShot(slot_29_49, arg_29_0, ObjectCache.myHeroCache.boundingRadius) then
			if slot_29_54 and slot_29_51 then
				return math.max(0, slot_29_48 - slot_29_57)
			else
				return slot_29_57
			end
		end
	elseif arg_29_0.spellType == SpellType.Cone then
		local slot_29_58 = math.max(0, arg_29_0.endTime - ove_0_6.TickCount() - arg_29_3)
		local slot_29_59 = arg_29_4:dist(arg_29_1)
		local slot_29_60 = arg_29_2 * (slot_29_58 / 1000)
		local slot_29_61 = arg_29_4 + slot_29_0 * math.min(slot_29_60, slot_29_59)
		local slot_29_62 = {}
		local slot_29_63, slot_29_64, slot_29_65 = ove_0_7.ProjectOn(arg_29_4, arg_29_0.cnStart, arg_29_0.cnLeft)
		local slot_29_66, slot_29_67, slot_29_68 = ove_0_7.ProjectOn(arg_29_4, arg_29_0.cnLeft, arg_29_0.cnRight)
		local slot_29_69, slot_29_70, slot_29_71 = ove_0_7.ProjectOn(arg_29_4, arg_29_0.cnRight, arg_29_0.cnStart)

		table.insert(slot_29_62, slot_29_64)
		table.insert(slot_29_62, slot_29_67)
		table.insert(slot_29_62, slot_29_70)
		table.sort(slot_29_62, function(arg_30_0, arg_30_1)
			-- function 30
			return arg_30_0:dist(arg_29_4) < arg_30_1:dist(arg_29_4)
		end)

		return math.max(0, slot_29_61:dist(slot_29_62[1]) - (arg_29_0.radius + ObjectCache.myHeroCache.boundingRadius + arg_29_5))
	end

	return 1
end

function ove_0_6.PredictSpellCollision(arg_31_0, arg_31_1, arg_31_2, arg_31_3, arg_31_4, arg_31_5, arg_31_6)
	-- function 31
	arg_31_5 = arg_31_5 + 10

	if not arg_31_6 then
		return ove_0_6.GetClosestDistanceApproach(arg_31_0, arg_31_1, arg_31_2, 0, ObjectCache.myHeroCache.serverPos2D, 0) == 0
	end

	return ove_0_6.GetClosestDistanceApproach(arg_31_0, arg_31_1, arg_31_2, arg_31_3, ObjectCache.myHeroCache.serverPos2DPing, arg_31_5) == 0 or ove_0_6.GetClosestDistanceApproach(arg_31_0, arg_31_1, arg_31_2, ObjectCache.gamePing, ObjectCache.myHeroCache.serverPos2DPing, arg_31_5) == 0
end

function ove_0_6.RotateVector(arg_32_0, arg_32_1, arg_32_2)
	-- function 32
	arg_32_2 = arg_32_2 * (math.pi / 180)

	local slot_32_0 = arg_32_1

	slot_32_0.X = math.cos(arg_32_2) * (arg_32_1.x - arg_32_0.x) - math.sin(arg_32_2) * (arg_32_1.y - arg_32_0.y) + arg_32_0.x
	slot_32_0.y = math.sin(arg_32_2) * (arg_32_1.x - arg_32_0.x) + math.cos(arg_32_2) * (arg_32_1.y - arg_32_0.y) + arg_32_0.y

	return slot_32_0
end

function ove_0_6.GetDistanceToChampions(arg_33_0)
	-- function 33
	local slot_33_0 = math.huge

	for iter_33_0, iter_33_1 in ipairs(common.GetEnemyHeroes()) do
		if iter_33_1 and iter_33_1.isVisible and not iter_33_1.isDead then
			local slot_33_1 = to2D(iter_33_1.path.serverPos):dist(arg_33_0)

			slot_33_0 = math.min(slot_33_0, slot_33_1)
		end
	end

	return slot_33_0
end

function ove_0_6.isNearEnemy(arg_34_0, arg_34_1, arg_34_2)
	-- function 34
	arg_34_2 = arg_34_2 or true

	if ObjectCache.menuCache.cache.PreventDodgingNearEnemy:get() then
		local slot_34_0 = ove_0_6.GetDistanceToChampions(ObjectCache.myHeroCache.serverPos2D)
		local slot_34_1 = ove_0_6.GetDistanceToChampions(arg_34_0)

		if slot_34_0 < arg_34_1 then
			if slot_34_1 < slot_34_0 then
				return true
			end
		elseif slot_34_1 < arg_34_1 then
			return true
		end
	end

	return false
end

function ove_0_6.CanHeroWalkToPos(arg_35_0, arg_35_1, arg_35_2, arg_35_3, arg_35_4)
	-- function 35
	local slot_35_0 = 0
	local slot_35_1 = 0
	local slot_35_2 = math.huge
	local slot_35_3 = {}
	local slot_35_4 = {}
	local slot_35_5 = ObjectCache.myHeroCache.serverPos2D

	arg_35_4 = arg_35_4 or true

	if not arg_35_4 then
		slot_35_5 = to2D(player.path.serverPos)
	end

	local slot_35_6 = ove_0_6.calcPos(arg_35_0)

	for iter_35_0, iter_35_1 in pairs(spells) do
		if iter_35_1.dangerlevel > 0 and (iter_35_1.dangerlevel > 2 or not Evade.isComboEnabled()) then
			slot_35_2 = math.min(slot_35_2, ove_0_6.GetClosestDistanceApproach(iter_35_1, arg_35_0, arg_35_1, arg_35_2, slot_35_5, arg_35_3))

			if ove_0_6.InSkillShot(arg_35_0, iter_35_1, ObjectCache.myHeroCache.boundingRadius) or ove_0_6.PredictSpellCollision(iter_35_1, arg_35_0, arg_35_1, arg_35_2, slot_35_5, arg_35_3, arg_35_4) then
				slot_35_0 = math.max(slot_35_0, iter_35_1.dangerlevel)
				slot_35_1 = slot_35_1 + iter_35_1.dangerlevel
				slot_35_4[iter_35_1.spellID] = iter_35_1
			else
				slot_35_3[iter_35_1.spellID] = iter_35_1
			end
		end
	end

	return Evade.PositionInfo(arg_35_0, slot_35_0, slot_35_1, slot_35_1 > 0, slot_35_2, slot_35_3, slot_35_4)
end

function ove_0_6.CanHeroWalkToPosAll(arg_36_0, arg_36_1, arg_36_2, arg_36_3, arg_36_4)
	-- function 36
	local slot_36_0 = 0
	local slot_36_1 = 0
	local slot_36_2 = math.huge
	local slot_36_3 = {}
	local slot_36_4 = {}
	local slot_36_5 = ObjectCache.myHeroCache.serverPos2D

	arg_36_4 = arg_36_4 or true

	if not arg_36_4 then
		slot_36_5 = to2D(player.path.serverPos)
	end

	for iter_36_0, iter_36_1 in pairs(detectedSpells) do
		if iter_36_1.dangerlevel > 0 then
			slot_36_2 = math.min(slot_36_2, ove_0_6.GetClosestDistanceApproach(iter_36_1, arg_36_0, arg_36_1, arg_36_2, slot_36_5, arg_36_3))

			if ove_0_6.InSkillShot(arg_36_0, iter_36_1, ObjectCache.myHeroCache.boundingRadius) or ove_0_6.PredictSpellCollision(iter_36_1, arg_36_0, arg_36_1, arg_36_2, slot_36_5, arg_36_3, arg_36_4) then
				slot_36_0 = math.max(slot_36_0, iter_36_1.dangerlevel)
				slot_36_1 = slot_36_1 + iter_36_1.dangerlevel
				slot_36_4[iter_36_1.spellID] = iter_36_1
			else
				slot_36_3[iter_36_1.spellID] = iter_36_1
			end
		end
	end

	return Evade.PositionInfo(arg_36_0, slot_36_0, slot_36_1, slot_36_1 > 0, slot_36_2, slot_36_3, slot_36_4)
end

function ove_0_6.CheckDangerousPos(arg_37_0, arg_37_1, arg_37_2)
	-- function 37
	for iter_37_0, iter_37_1 in pairs(spells) do
		if iter_37_1.dangerlevel > 0 and (iter_37_1.dangerlevel > 2 or not Evade.isComboEnabled()) and ove_0_6.InSkillShot(arg_37_0, iter_37_1, ObjectCache.myHeroCache.boundingRadius + arg_37_1) then
			return true
		end
	end

	return false
end

function ove_0_6.CheckDangerousPosSpell(arg_38_0, arg_38_1, arg_38_2)
	-- function 38
	for iter_38_0, iter_38_1 in pairs(detectedSpells) do
		if iter_38_1.dangerlevel > 0 and (iter_38_1.dangerlevel > 2 or not Evade.isComboEnabled()) and ove_0_6.InSkillShot(arg_38_0, iter_38_1, ObjectCache.myHeroCache.boundingRadius + arg_38_1) then
			return true
		end
	end

	return false
end

function ove_0_6.CheckDangerousPosSpell2(arg_39_0, arg_39_1, arg_39_2)
	-- function 39
	for iter_39_0, iter_39_1 in pairs(detectedSpells) do
		if iter_39_1.dangerlevel > 0 and (iter_39_1.dangerlevel > 2 or not Evade.isComboEnabled()) and ove_0_6.InSkillShot2(arg_39_0, iter_39_1, ObjectCache.myHeroCache.boundingRadius + arg_39_1) then
			return true
		end
	end

	return false
end

function ove_0_6.isTurrets(arg_40_0)
	-- function 40
	if ObjectCache.menuCache.cache.PreventDodgingUnderTower:get() and ove_0_6.GetDistanceToTurrets(arg_40_0) <= 1000 then
		return true
	end

	return false
end

function ove_0_6.GetDistanceToTurrets(arg_41_0)
	-- function 41
	local slot_41_0 = math.huge

	for iter_41_0 = 0, objManager.turrets.size[TEAM_ENEMY] - 1 do
		local slot_41_1 = objManager.turrets[TEAM_ENEMY][iter_41_0]

		if slot_41_1 and slot_41_1.isTargetable and not slot_41_1.isDead then
			local slot_41_2 = arg_41_0:dist(slot_41_1.pos2D)

			slot_41_0 = math.min(slot_41_0, slot_41_2)
		end
	end

	return slot_41_0
end

function ove_0_6.GetPositionValue(arg_42_0)
	-- function 42
	local slot_42_0 = ove_0_6.GetDistance(arg_42_0, mousePos2D)

	if ObjectCache.menuCache.cache.PreventDodgingNearEnemy:get() then
		local slot_42_1 = ObjectCache.menuCache.cache.MinComfortZone:get()

		for iter_42_0, iter_42_1 in ipairs(common.GetEnemyHeroes()) do
			if iter_42_1 and iter_42_1.isVisible and not iter_42_1.isDead then
				local slot_42_2 = to2D(iter_42_1.path.serverPos):dist(arg_42_0)

				if slot_42_2 < slot_42_1 then
					slot_42_0 = slot_42_0 * (1 + (slot_42_1 - slot_42_2) / 100)
				end
			end
		end
	end

	return slot_42_0, posValue2
end

function ove_0_6.CalculateEvadeTime()
	-- function 43
	for iter_43_0, iter_43_1 in pairs(spells) do
		local slot_43_0, slot_43_1, slot_43_2 = iter_43_1:CanHeroEvade(player)

		iter_43_1.spellHitTime = slot_43_2
		iter_43_1.evadeTime = slot_43_1
	end
end

function ove_0_6.GetFastestPosition(arg_44_0)
	-- function 44
	local slot_44_0 = ObjectCache.myHeroCache.serverPos2D

	if arg_44_0.spellType == SpellType.Line then
		local slot_44_1, slot_44_2, slot_44_3 = ove_0_7.ProjectOn(slot_44_0, arg_44_0.startPos, arg_44_0.endPos)

		if slot_44_2.x ~= slot_44_0.x then
			return ove_0_7.Extend(vec2(slot_44_2.x, slot_44_2.y), slot_44_0, arg_44_0.radius + ObjectCache.myHeroCache.boundingRadius + 50)
		end
	elseif arg_44_0.spellType == SpellType.Circular then
		if slot_44_0.x ~= arg_44_0.endPos.x then
			return ove_0_7.Extend(arg_44_0.endPos, slot_44_0, arg_44_0.radius + 10)
		end
	elseif arg_44_0.spellType == SpellType.Cone then
		local slot_44_4 = {}
		local slot_44_5, slot_44_6, slot_44_7 = ove_0_7.ProjectOn(slot_44_0, arg_44_0.cnStart, arg_44_0.cnLeft)
		local slot_44_8, slot_44_9, slot_44_10 = ove_0_7.ProjectOn(slot_44_0, arg_44_0.cnLeft, arg_44_0.cnRight)
		local slot_44_11, slot_44_12, slot_44_13 = ove_0_7.ProjectOn(slot_44_0, arg_44_0.cnRight, arg_44_0.cnStart)

		table.insert(slot_44_4, slot_44_6)
		table.insert(slot_44_4, slot_44_9)
		table.insert(slot_44_4, slot_44_12)
		table.sort(slot_44_4, function(arg_45_0, arg_45_1)
			-- function 45
			return arg_45_0:dist(slot_44_0) < arg_45_1:dist(slot_44_0)
		end)

		return slot_44_4[1]
	end

	return nil
end

function ove_0_6.GetFastestPositions()
	-- function 46
	local slot_46_0 = {}

	for iter_46_0, iter_46_1 in pairs(spells) do
		local slot_46_1 = ove_0_6.GetFastestPosition(iter_46_1)

		if slot_46_1 and not navmesh.isWall(slot_46_1) and not ove_0_6.isTurrets(slot_46_1) then
			table.insert(slot_46_0, slot_46_1)
		end
	end

	return slot_46_0
end

function ove_0_6.GetMinCPADistance(arg_47_0)
	-- function 47
	local slot_47_0 = math.huge
	local slot_47_1 = ObjectCache.myHeroCache.serverPos2D

	for iter_47_0, iter_47_1 in pairs(spells) do
		if (iter_47_1.dangerlevel > 2 or not Evade.isComboEnabled()) and iter_47_1.dangerlevel > 0 then
			slot_47_0 = math.min(slot_47_0, ove_0_6.GetClosestDistanceApproach(iter_47_1, arg_47_0, ObjectCache.myHeroCache.moveSpeed, ObjectCache.gamePing, slot_47_1, 0))
		end
	end

	return slot_47_0
end

function ove_0_6.InitPositionInfo(arg_48_0, arg_48_1, arg_48_2, arg_48_3, arg_48_4)
	-- function 48
	if not ObjectCache.myHeroCache.isMoving and ObjectCache.myHeroCache.serverPos2D:dist(arg_48_0) <= 75 then
		arg_48_0 = ObjectCache.myHeroCache.serverPos2D
	end

	local slot_48_0 = ObjectCache.menuCache.cache.ExtraCPADistance:get()
	local slot_48_1 = ove_0_6.CanHeroWalkToPos(arg_48_0, ObjectCache.myHeroCache.moveSpeed, arg_48_1 + ObjectCache.gamePing, slot_48_0)

	slot_48_1.isDangerousPos = ove_0_6.CheckDangerousPos(arg_48_0, 10)
	slot_48_1.isDangerousPos_n = slot_48_1.isDangerousPos and 1 or 0
	slot_48_1.hasExtraDistance = arg_48_2 > 0 and ove_0_6.CheckDangerousPos(arg_48_0, arg_48_2) and 0 or 1
	slot_48_1.closestDistance = slot_48_1.distanceToMouse
	slot_48_1.intersectionTime = ove_0_6.GetMinCPADistance(arg_48_0)
	slot_48_1.lowestEvadeTimeSpell = arg_48_4
	slot_48_1.distanceToMouse = ove_0_6.GetPositionValue(arg_48_0)

	return slot_48_1
end

function ove_0_6.CheckPathCollision(arg_49_0, arg_49_1)
	-- function 49
	return false
end

function ove_0_6.CheckPointCollision(arg_50_0, arg_50_1)
	-- function 50
	return false
end

function ove_0_6.GetExtendedSafePosition_path(arg_51_0, arg_51_1, arg_51_2)
	-- function 51
	local slot_51_0 = (arg_51_1 - arg_51_0):norm()
	local slot_51_1 = arg_51_1
	local slot_51_2 = 50

	for iter_51_0 = slot_51_2, arg_51_2, slot_51_2 do
		local slot_51_3 = arg_51_1 + slot_51_0 * iter_51_0

		if not navmesh.isWall(slot_51_3) then
			if ove_0_6.CheckDangerousPos(slot_51_3, 6) or ove_0_6.CheckPathCollision(player, slot_51_3) then
				return slot_51_1
			end

			slot_51_1 = slot_51_3
		end
	end

	return slot_51_1
end

function ove_0_6.HasExtraAvoidDistance(arg_52_0, arg_52_1)
	-- function 52
	for iter_52_0, iter_52_1 in pairs(spells) do
		if iter_52_1.spellType == SpellType.Line and (not Evade.isComboEnabled() or iter_52_1.dangerlevel > 2) and iter_52_1.dangerlevel > 0 and ove_0_6.InSkillShot(arg_52_0, iter_52_1, ObjectCache.myHeroCache.boundingRadius + arg_52_1) then
			return true
		end
	end

	return false
end

function ove_0_6.GetExtendedSafePosition(arg_53_0, arg_53_1, arg_53_2)
	-- function 53
	if arg_53_0.x == arg_53_1.x then
		return arg_53_1, 0
	end

	local slot_53_0 = (arg_53_1 - arg_53_0):norm()
	local slot_53_1 = 0
	local slot_53_2 = arg_53_1
	local slot_53_3 = 50

	for iter_53_0 = slot_53_3, arg_53_2, slot_53_3 do
		local slot_53_4 = arg_53_1 + slot_53_0 * iter_53_0

		if not navmesh.isWall(slot_53_4) then
			if ove_0_6.CheckDangerousPos(slot_53_4, 10) or ove_0_6.CheckPathCollision(player, slot_53_4) then
				return slot_53_2, slot_53_1
			end

			slot_53_1 = slot_53_1 + 1
			slot_53_2 = slot_53_4
		end
	end

	return slot_53_2, slot_53_1
end

function ove_0_6.GetBestPositionBlock(arg_54_0, arg_54_1)
	-- function 54
	local slot_54_0 = 0
	local slot_54_1 = 50
	local slot_54_2 = 50
	local slot_54_3 = 0
	local slot_54_4 = ObjectCache.menuCache.cache.ExtraAvoidDistance:get()
	local slot_54_5 = ObjectCache.myHeroCache.serverPos2D
	local slot_54_6 = arg_54_0
	local slot_54_7 = {}
	local slot_54_8 = ObjectCache.menuCache.cache.ExtraCPADistance:get()
	local slot_54_9 = ObjectCache.menuCache.cache.ExtraPingBuffer:get()

	while slot_54_0 < slot_54_1 do
		slot_54_3 = slot_54_3 + 1

		local slot_54_10 = slot_54_3 * (2 * slot_54_2)
		local slot_54_11 = math.ceil(2 * math.pi * slot_54_10 / (2 * slot_54_2))

		for iter_54_0 = 1, slot_54_11 do
			slot_54_0 = slot_54_0 + 1

			local slot_54_12 = 2 * math.pi / (slot_54_11 - 1) * iter_54_0
			local slot_54_13 = vec2(math.floor(slot_54_5.x + slot_54_10 * math.cos(slot_54_12)), math.floor(slot_54_5.y + slot_54_10 * math.sin(slot_54_12)))

			if not navmesh.isWall(slot_54_13) then
				local slot_54_14 = ove_0_6.CanHeroWalkToPos(slot_54_13, ObjectCache.myHeroCache.moveSpeed, slot_54_9 .. ObjectCache.gamePing, slot_54_8)

				slot_54_14.isDangerousPos = ove_0_6.CheckDangerousPos(slot_54_13, slot_54_4) or ove_0_6.CheckMovePath(slot_54_13)
				slot_54_14.isDangerousPos_n = slot_54_14.isDangerousPos and 0 or 1
				slot_54_14.distanceToMouse = slot_54_13:dist(slot_54_6)
				slot_54_14.hasExtraDistance = slot_54_4 > 0 and ove_0_6.CheckDangerousPos(slot_54_13, slot_54_4) and 0 or 1

				table.insert(slot_54_7, slot_54_14)
			end
		end
	end

	table.sort(slot_54_7, function(arg_55_0, arg_55_1)
		-- function 55
		if arg_55_0.isDangerousPos_n == arg_55_0.isDangerousPos_n then
			if arg_55_0.posDangerLevel and arg_55_1.posDangerLevel then
				if arg_55_0.hasExtraDistance == arg_55_1.hasExtraDistance then
					return arg_55_0.distanceToMouse < arg_55_1.distanceToMouse
				else
					return arg_55_0.hasExtraDistance > arg_55_1.hasExtraDistance
				end
			else
				return arg_55_0.posDangerLevel < arg_55_1.posDangerLevel
			end
		else
			return al < bl
		end
	end)

	if arg_54_1 then
		for iter_54_1, iter_54_2 in pairs(slot_54_7) do
			graphics:DrawCircle3d(to3D(iter_54_2.position), 10, 4294967295, 100, 2)
			graphics:DrawSizedText3d(30, to3D(iter_54_2.position), 4294967295, tostring(iter_54_1))

			return
		end
	end

	for iter_54_3, iter_54_4 in pairs(slot_54_7) do
		if not iter_54_4.isDangerousPos then
			return iter_54_4
		end
	end

	return nil
end

function ove_0_6.on_draw()
	-- function 56
	return
end

function ove_0_6.on_draw1()
	-- function 57
	local slot_57_0 = 1
	local slot_57_1 = 50
	local slot_57_2 = 50
	local slot_57_3 = 0
	local slot_57_4 = ObjectCache.menuCache.cache.ExtraPingBuffer:get() + ObjectCache.gamePing
	local slot_57_5 = ObjectCache.menuCache.cache.ExtraEvadeDistance:get()

	Evade.SpellDetector_table:Update()
	ove_0_6.CalculateEvadeTime()

	if ObjectCache.menuCache.cache.CalculateWindupDelay:get() then
		local slot_57_6 = Evade.lastWindupTime - ove_0_6.TickCount()

		if slot_57_6 > 0 then
			slot_57_4 = slot_57_4 + slot_57_6
		end
	end

	local slot_57_7 = slot_57_4 + Evade.avgCalculationTime

	if ObjectCache.menuCache.cache.HigherPrecision:get() then
		slot_57_1 = 76
		slot_57_2 = 50
	end

	local slot_57_8 = ObjectCache.myHeroCache.serverPos2D
	local slot_57_9 = game.mousePos2D
	local slot_57_10 = {}
	local slot_57_11 = ove_0_6.InitPositionInfo(game.mousePos2D, slot_57_7, slot_57_5, slot_57_9, lowestEvadeTimeSpell)

	if slot_57_11.position:dist(to2D(player.pos)) > ObjectCache.myHeroCache.boundingRadius then
		table.insert(slot_57_10, slot_57_11)
	end

	local slot_57_12, slot_57_13 = Evade.SpellDetector_table:GetLowestEvadeTime()
	local slot_57_14 = ove_0_6.GetFastestPositions()

	for iter_57_0, iter_57_1 in pairs(slot_57_14) do
		local slot_57_15 = ove_0_6.InitPositionInfo(iter_57_1, slot_57_7, slot_57_5, slot_57_9, slot_57_13)

		if slot_57_15.position:dist(to2D(player.pos)) > ObjectCache.myHeroCache.boundingRadius then
			table.insert(slot_57_10, slot_57_15)
		end
	end

	while slot_57_0 < slot_57_1 do
		slot_57_3 = slot_57_3 + 1

		local slot_57_16 = slot_57_3 * (2 * slot_57_2)
		local slot_57_17 = math.ceil(2 * math.pi * slot_57_16 / (2 * slot_57_2))

		for iter_57_2 = 1, slot_57_17 do
			slot_57_0 = slot_57_0 + 1

			local slot_57_18 = 2 * math.pi / (slot_57_17 - 1) * iter_57_2
			local slot_57_19 = vec2(math.floor(slot_57_8.x + slot_57_16 * math.cos(slot_57_18)), math.floor(slot_57_8.y + slot_57_16 * math.sin(slot_57_18)))

			if not navmesh.isWall(slot_57_19) and not ove_0_6.isTurrets(slot_57_19) then
				local slot_57_20 = ove_0_6.InitPositionInfo(slot_57_19, slot_57_7, slot_57_5, slot_57_9, slot_57_13)

				if slot_57_20.position:dist(to2D(player.pos)) > ObjectCache.myHeroCache.boundingRadius then
					table.insert(slot_57_10, slot_57_20)
				end
			end
		end
	end

	if #slot_57_10 > 0 then
		if ObjectCache.menuCache.cache.EvadeMode:get() == 1 then
			table.sort(slot_57_10, function(arg_58_0, arg_58_1)
				-- function 58
				if arg_58_0.posDangerCount == arg_58_1.posDangerCount then
					if arg_58_0.posDangerLevel == arg_58_1.posDangerLevel then
						local slot_58_0 = arg_58_0.isDangerousPos_n
						local slot_58_1 = arg_58_1.isDangerousPos_n

						if slot_58_0 == slot_58_1 then
							return arg_58_0.position:dist(to2D(player.pos)) < arg_58_1.position:dist(to2D(player.pos))
						else
							return slot_58_0 < slot_58_1
						end
					else
						return arg_58_0.posDangerLevel < arg_58_1.posDangerLevel
					end
				else
					return arg_58_0.posDangerCount < arg_58_1.posDangerCount
				end
			end)

			ove_0_6.fastEvadeMode = true
		elseif ove_0_6.fastEvadeMode then
			table.sort(slot_57_10, function(arg_59_0, arg_59_1)
				-- function 59
				if arg_59_0.posDangerCount == arg_59_1.posDangerCount then
					if arg_59_0.posDangerLevel == arg_59_1.posDangerLevel then
						local slot_59_0 = arg_59_0.isDangerousPos_n
						local slot_59_1 = arg_59_1.isDangerousPos_n

						if slot_59_0 == slot_59_1 then
							return arg_59_0.position:dist(to2D(player.pos)) < arg_59_1.position:dist(to2D(player.pos))
						else
							return slot_59_0 < slot_59_1
						end
					else
						return arg_59_0.posDangerLevel < arg_59_1.posDangerLevel
					end
				else
					return arg_59_0.posDangerCount < arg_59_1.posDangerCount
				end
			end)
		elseif ObjectCache.menuCache.cache.EvadeMode:get() == 3 then
			table.sort(slot_57_10, function(arg_60_0, arg_60_1)
				-- function 60
				if arg_60_0.posDangerCount == arg_60_1.posDangerCount then
					local slot_60_0 = arg_60_0.isDangerousPos_n
					local slot_60_1 = arg_60_1.isDangerousPos_n

					if slot_60_0 == slot_60_1 then
						return arg_60_0.distanceToMouse < arg_60_1.distanceToMouse
					else
						return slot_60_0 < slot_60_1
					end
				else
					return arg_60_0.posDangerCount < arg_60_1.posDangerCount
				end
			end)

			if slot_57_10[1].posDangerCount ~= 0 then
				table.sort(slot_57_10, function(arg_61_0, arg_61_1)
					-- function 61
					if arg_61_0.posDangerCount == arg_61_1.posDangerCount then
						if arg_61_0.posDangerLevel == arg_61_1.posDangerLevel then
							local slot_61_0 = arg_61_0.isDangerousPos_n
							local slot_61_1 = arg_61_1.isDangerousPos_n

							if slot_61_0 == slot_61_1 then
								return arg_61_0.position:dist(to2D(player.pos)) < arg_61_1.position:dist(to2D(player.pos))
							else
								return slot_61_0 < slot_61_1
							end
						else
							return arg_61_0.posDangerLevel < arg_61_1.posDangerLevel
						end
					else
						return arg_61_0.posDangerLevel < arg_61_1.posDangerLevel
					end
				end)

				if slot_57_10[1].posDangerCount == 0 then
					ove_0_6.fastEvadeMode = true
				end
			end
		elseif ObjectCache.menuCache.cache.FastEvadeActivationTime:get() > 0 and slot_57_12 < ObjectCache.menuCache.cache.FastEvadeActivationTime:get() + ObjectCache.gamePing * 2 + slot_57_7 then
			table.sort(slot_57_10, function(arg_62_0, arg_62_1)
				-- function 62
				if arg_62_0.posDangerCount == arg_62_1.posDangerCount then
					if arg_62_0.posDangerLevel == arg_62_1.posDangerLevel then
						local slot_62_0 = arg_62_0.isDangerousPos_n
						local slot_62_1 = arg_62_1.isDangerousPos_n

						if slot_62_0 == slot_62_1 then
							return arg_62_0.position:dist(to2D(player.pos)) < arg_62_1.position:dist(to2D(player.pos))
						else
							return slot_62_0 < slot_62_1
						end
					else
						return arg_62_0.posDangerLevel < arg_62_1.posDangerLevel
					end
				else
					return arg_62_0.posDangerLevel < arg_62_1.posDangerLevel
				end
			end)

			ove_0_6.fastEvadeMode = true
		else
			table.sort(slot_57_10, function(arg_63_0, arg_63_1)
				-- function 63
				if arg_63_0.posDangerCount == arg_63_1.posDangerCount then
					if arg_63_0.isDangerousPos_n == arg_63_1.isDangerousPos_n then
						return arg_63_0.distanceToMouse < arg_63_1.distanceToMouse
					else
						return arg_63_0.isDangerousPos_n < arg_63_1.isDangerousPos_n
					end
				else
					return arg_63_0.posDangerCount < arg_63_1.posDangerCount
				end
			end)

			if slot_57_10[1].posDangerCount ~= 0 then
				table.sort(slot_57_10, function(arg_64_0, arg_64_1)
					-- function 64
					if arg_64_0.posDangerCount == arg_64_1.posDangerCount then
						if arg_64_0.posDangerLevel == arg_64_1.posDangerLevel then
							local slot_64_0 = arg_64_0.isDangerousPos_n
							local slot_64_1 = arg_64_1.isDangerousPos_n

							if slot_64_0 == slot_64_1 then
								return arg_64_0.position:dist(to2D(player.pos)) < arg_64_1.position:dist(to2D(player.pos))
							else
								return slot_64_0 < slot_64_1
							end
						else
							return arg_64_0.posDangerLevel < arg_64_1.posDangerLevel
						end
					else
						return arg_64_0.posDangerLevel < arg_64_1.posDangerLevel
					end
				end)

				if slot_57_10[1].posDangerCount == 0 then
					ove_0_6.fastEvadeMode = true
				end
			end
		end
	end

	if slot_57_10[1].posDangerCount ~= 0 then
		table.sort(slot_57_10, function(arg_65_0, arg_65_1)
			-- function 65
			local slot_65_0 = arg_65_0.isDangerousPos_n
			local slot_65_1 = arg_65_1.isDangerousPos_n

			if slot_65_0 == slot_65_1 then
				return arg_65_0.position:dist(to2D(player.pos)) < arg_65_1.position:dist(to2D(player.pos))
			else
				return slot_65_0 < slot_65_1
			end
		end)
	end

	for iter_57_3, iter_57_4 in ipairs(slot_57_10) do
		graphics:DrawCircle3d(to3D(iter_57_4.position), 10, 4294967295, 100, 2)
		graphics:DrawSizedText3d(30, to3D(iter_57_4.position), 4294967295, tostring(iter_57_4.posDangerCount))

		return
	end

	if 1 == 1 then
		return
	end

	for iter_57_5, iter_57_6 in ipairs(slot_57_10) do
		graphics:DrawLine3d(player.pos, to3D(iter_57_6.position), 4294967295, 10)
		graphics:DrawCircle3d(to3D(iter_57_6.position), ObjectCache.myHeroCache.boundingRadius, 4278255615, 32, 2)

		local slot_57_21, slot_57_22 = ove_0_6.GetPositionValue(iter_57_6.position)

		graphics:DrawSizedText3d(30, to3D(iter_57_6.position) + float3(100, 100), 4294967295, tostring(slot_57_21 .. "-" .. iter_57_6.intersectionTime))

		for iter_57_7, iter_57_8 in pairs(spells) do
			local slot_57_23 = ove_0_6.GetClosestDistanceApproachDraw(iter_57_8, iter_57_6.position, player.moveSpeed, 0, to2D(player.pos), 10)
			local slot_57_24 = ove_0_6.GetClosestDistanceApproachDraw(iter_57_8, game.mousePos2D, player.moveSpeed, 0, to2D(player.pos), 10)

			graphics:DrawSizedText3d(30, to3D(iter_57_6.position), 4294967295, tostring(slot_57_23))
			graphics:DrawSizedText3d(30, game.mousePos + float3(50, 50), 4294967295, tostring(slot_57_24))
		end

		return
	end
end

local ove_0_10 = {
	Item_3859_Name = true,
	Item_3857_Name = true,
	Item_2055_Name = true,
	Item_3860_Name = true,
	Item_3866_Name = true,
	Item_3870_Name = true,
	Item_3869_Name = true,
	Item_3876_Name = true,
	Item_3877_Name = true,
	Item_3864_Name = true,
	Item_3863_Name = true,
	Item_3867_Name = true,
	Item_3871_Name = true,
	Item_3855_Name = true
}

function ove_0_6.GetWardID()
	-- function 66
	if player:spellSlot(12) and player:spellSlot(12).name == "TrinketTotemLvl1" and player:spellSlot(12).state == 0 then
		return 12
	else
		for iter_66_0 = 6, 11 do
			local slot_66_0 = player:inventorySlot(iter_66_0 - 6)

			if slot_66_0 and player:spellSlot(iter_66_0).state == 0 and ove_0_10[slot_66_0.name] then
				return iter_66_0
			end
		end
	end
end

function ove_0_6.GetBestPosition(arg_67_0, arg_67_1)
	-- function 67
	if ObjectCache.menuCache.cache.evade_try:get() then
		return ove_0_6.GetBestPosition_try(arg_67_0)
	end

	local slot_67_0 = 0
	local slot_67_1 = 20
	local slot_67_2 = player.boundingRadius * 2
	local slot_67_3 = 1
	local slot_67_4 = ObjectCache.menuCache.cache.ExtraPingBuffer:get() + ObjectCache.gamePing
	local slot_67_5 = ObjectCache.menuCache.cache.ExtraEvadeDistance:get()

	Evade.SpellDetector_table:Update()
	ove_0_6.CalculateEvadeTime()

	if ObjectCache.menuCache.cache.CalculateWindupDelay:get() then
		local slot_67_6 = Evade.lastWindupTime - ove_0_6.TickCount()

		if slot_67_6 > 0 then
			slot_67_4 = slot_67_4 + slot_67_6
		end
	end

	local slot_67_7 = slot_67_4 + Evade.avgCalculationTime

	if ObjectCache.menuCache.cache.HigherPrecision:get() then
		slot_67_1 = 30
	end

	local slot_67_8 = ObjectCache.myHeroCache.serverPos2D
	local slot_67_9 = mousePos2D

	if Evade.isDodging or player.path.isMoving or player.path.isActive then
		slot_67_9 = player.path.endPos2D
	end

	local slot_67_10 = ObjectCache.myHeroCache.serverPos2D
	local slot_67_11 = {}
	local slot_67_12, slot_67_13 = Evade.SpellDetector_table:GetLowestEvadeTime()
	local slot_67_14 = ove_0_6.GetFastestPositions()

	for iter_67_0, iter_67_1 in pairs(slot_67_14) do
		local slot_67_15 = ove_0_6.InitPositionInfo(iter_67_1, slot_67_7, slot_67_5, slot_67_9, slot_67_13)

		if slot_67_15.position:dist(slot_67_10) > ObjectCache.myHeroCache.boundingRadius and not navmesh.isWall(slot_67_15.position) and not ove_0_6.isTurrets(slot_67_15.position) and ObjectCache.menuCache.cache.MinAngle:get() > common.GetAngle(slot_67_15.position, slot_67_9) and not ove_0_6.isNearEnemy(slot_67_15.position, ObjectCache.menuCache.cache.MinComfortZone:get()) then
			table.insert(slot_67_11, slot_67_15)
		end
	end

	for iter_67_2 = slot_67_0, slot_67_1 do
		local slot_67_16 = iter_67_2 * 2 * math.pi / slot_67_1

		for iter_67_3 = player.boundingRadius, 1000, slot_67_2 do
			local slot_67_17 = vec2(slot_67_8.x + iter_67_3 * math.cos(slot_67_16), slot_67_8.y + iter_67_3 * math.sin(slot_67_16))

			if navmesh.isWall(slot_67_17) then
				break
			elseif not ove_0_6.isTurrets(slot_67_17) and not ove_0_6.isNearEnemy(slot_67_17, ObjectCache.menuCache.cache.MinComfortZone:get()) and ObjectCache.menuCache.cache.MinAngle:get() > common.GetAngle(slot_67_17, slot_67_9) then
				local slot_67_18 = ove_0_6.InitPositionInfo(slot_67_17, slot_67_7, slot_67_5, slot_67_9, slot_67_13)

				table.insert(slot_67_11, slot_67_18)
			end
		end
	end

	if #slot_67_11 > 0 then
		if ObjectCache.menuCache.cache.EvadeMode:get() == 2 then
			table.sort(slot_67_11, function(arg_68_0, arg_68_1)
				-- function 68
				if arg_68_0.posDangerCount == arg_68_1.posDangerCount then
					if arg_68_0.posDangerLevel == arg_68_1.posDangerLevel then
						local slot_68_0 = arg_68_0.isDangerousPos_n
						local slot_68_1 = arg_68_1.isDangerousPos_n

						if slot_68_0 == slot_68_1 then
							return arg_68_0.position:dist(slot_67_10) < arg_68_1.position:dist(slot_67_10)
						else
							return slot_68_0 < slot_68_1
						end
					else
						return arg_68_0.posDangerLevel < arg_68_1.posDangerLevel
					end
				else
					return arg_68_0.posDangerCount < arg_68_1.posDangerCount
				end
			end)

			ove_0_6.fastEvadeMode = true
		elseif ove_0_6.fastEvadeMode then
			table.sort(slot_67_11, function(arg_69_0, arg_69_1)
				-- function 69
				if arg_69_0.posDangerCount == arg_69_1.posDangerCount then
					if arg_69_0.posDangerLevel == arg_69_1.posDangerLevel then
						local slot_69_0 = arg_69_0.isDangerousPos_n
						local slot_69_1 = arg_69_1.isDangerousPos_n

						if slot_69_0 == slot_69_1 then
							return arg_69_0.position:dist(slot_67_10) < arg_69_1.position:dist(slot_67_10)
						else
							return slot_69_0 < slot_69_1
						end
					else
						return arg_69_0.posDangerLevel < arg_69_1.posDangerLevel
					end
				else
					return arg_69_0.posDangerCount < arg_69_1.posDangerCount
				end
			end)
		elseif ObjectCache.menuCache.cache.EvadeMode:get() == 4 then
			table.sort(slot_67_11, function(arg_70_0, arg_70_1)
				-- function 70
				if arg_70_0.posDangerCount == arg_70_1.posDangerCount then
					local slot_70_0 = arg_70_0.isDangerousPos_n
					local slot_70_1 = arg_70_1.isDangerousPos_n

					if slot_70_0 == slot_70_1 then
						return arg_70_0.distanceToMouse < arg_70_1.distanceToMouse
					else
						return slot_70_0 < slot_70_1
					end
				else
					return arg_70_0.posDangerCount < arg_70_1.posDangerCount
				end
			end)

			if slot_67_11[1].posDangerCount ~= 0 then
				table.sort(slot_67_11, function(arg_71_0, arg_71_1)
					-- function 71
					if arg_71_0.posDangerCount == arg_71_1.posDangerCount then
						if arg_71_0.posDangerLevel == arg_71_1.posDangerLevel then
							local slot_71_0 = arg_71_0.isDangerousPos_n
							local slot_71_1 = arg_71_1.isDangerousPos_n

							if slot_71_0 == slot_71_1 then
								return arg_71_0.position:dist(slot_67_10) < arg_71_1.position:dist(slot_67_10)
							else
								return slot_71_0 < slot_71_1
							end
						else
							return arg_71_0.posDangerLevel < arg_71_1.posDangerLevel
						end
					else
						return arg_71_0.posDangerLevel < arg_71_1.posDangerLevel
					end
				end)

				ove_0_6.fastEvadeMode = true
			end
		elseif ObjectCache.menuCache.cache.FastEvadeActivationTime:get() > 0 and slot_67_12 > 0 and slot_67_12 < ObjectCache.menuCache.cache.FastEvadeActivationTime:get() + ObjectCache.gamePing then
			table.sort(slot_67_11, function(arg_72_0, arg_72_1)
				-- function 72
				if arg_72_0.posDangerCount == arg_72_1.posDangerCount then
					if arg_72_0.posDangerLevel == arg_72_1.posDangerLevel then
						local slot_72_0 = arg_72_0.isDangerousPos_n
						local slot_72_1 = arg_72_1.isDangerousPos_n

						if slot_72_0 == slot_72_1 then
							return arg_72_0.intersectionTime > arg_72_1.intersectionTime
						else
							return slot_72_0 < slot_72_1
						end
					else
						return arg_72_0.posDangerLevel < arg_72_1.posDangerLevel
					end
				else
					return arg_72_0.posDangerLevel < arg_72_1.posDangerLevel
				end
			end)
			--common.d_debug("FastEvadeActivationTime_lowestEvadesssssssssssss" .. "ObjectCache.gamePing:" .. tostring(ObjectCache.gamePing) .. "  - " .. tostring(slot_67_12))

			ove_0_6.fastEvadeMode = true
		else
			table.sort(slot_67_11, function(arg_73_0, arg_73_1)
				-- function 73
				if arg_73_0.posDangerCount == arg_73_1.posDangerCount then
					if arg_73_0.isDangerousPos_n == arg_73_1.isDangerousPos_n then
						return arg_73_0.distanceToMouse < arg_73_1.distanceToMouse
					else
						return arg_73_0.isDangerousPos_n < arg_73_1.isDangerousPos_n
					end
				else
					return arg_73_0.posDangerCount < arg_73_1.posDangerCount
				end
			end)

			if slot_67_11[1].posDangerCount ~= 0 then
				table.sort(slot_67_11, function(arg_74_0, arg_74_1)
					-- function 74
					if arg_74_0.posDangerCount == arg_74_1.posDangerCount then
						if arg_74_0.posDangerLevel == arg_74_1.posDangerLevel then
							local slot_74_0 = arg_74_0.isDangerousPos_n
							local slot_74_1 = arg_74_1.isDangerousPos_n

							if slot_74_0 == slot_74_1 then
								return arg_74_0.position:dist(slot_67_10) < arg_74_1.position:dist(slot_67_10)
							else
								return slot_74_0 < slot_74_1
							end
						else
							return arg_74_0.posDangerLevel < arg_74_1.posDangerLevel
						end
					else
						return arg_74_0.posDangerLevel < arg_74_1.posDangerLevel
					end
				end)

				ove_0_6.fastEvadeMode = true
			end
		end

		if not ove_0_6.fastEvadeMode and slot_67_11[1].posDangerCount ~= 0 then
			table.sort(slot_67_11, function(arg_75_0, arg_75_1)
				-- function 75
				if arg_75_0.posDangerCount == arg_75_1.posDangerCount then
					if arg_75_0.posDangerLevel == arg_75_1.posDangerLevel then
						local slot_75_0 = arg_75_0.isDangerousPos_n
						local slot_75_1 = arg_75_1.isDangerousPos_n

						if slot_75_0 == slot_75_1 then
							return arg_75_0.position:dist(slot_67_10) < arg_75_1.position:dist(slot_67_10)
						else
							return slot_75_0 < slot_75_1
						end
					else
						return arg_75_0.posDangerLevel < arg_75_1.posDangerLevel
					end
				else
					return arg_75_0.posDangerLevel < arg_75_1.posDangerLevel
				end
			end)

			ove_0_6.fastEvadeMode = true
		elseif slot_67_11[1].posDangerCount ~= 0 then
			for iter_67_4, iter_67_5 in ipairs(slot_67_11) do
				local slot_67_19, slot_67_20 = ove_0_6.GetExtendedSafePosition(ObjectCache.myHeroCache.serverPos2D, iter_67_5.position, slot_67_5)

				if slot_67_20 > 0 then
					break
				else
					table.remove(slot_67_11, iter_67_4)
				end
			end
		end

		for iter_67_6, iter_67_7 in ipairs(slot_67_11) do
			if ove_0_6.fastEvadeMode and slot_67_5 > 0 then
				local slot_67_21 = ove_0_6.GetExtendedSafePosition(ObjectCache.myHeroCache.serverPos2D, iter_67_7.position, slot_67_5)
				local slot_67_22 = ove_0_6.CanHeroWalkToPos(slot_67_21, ObjectCache.myHeroCache.moveSpeed, ObjectCache.gamePing, 0)

				if not slot_67_22.isDangerousPos then
					return slot_67_22
				end
			end

			if ove_0_6.CheckDangerousPos(iter_67_7.position, slot_67_5) and slot_67_5 > 0 then
				local slot_67_23 = ove_0_6.GetExtendedSafePosition(ObjectCache.myHeroCache.serverPos2D, iter_67_7.position, slot_67_5)
				local slot_67_24 = ove_0_6.CanHeroWalkToPos(slot_67_23, ObjectCache.myHeroCache.moveSpeed, ObjectCache.gamePing, 0)

				if not slot_67_24.isDangerousPos then
					return slot_67_24
				end
			end

			return iter_67_7
		end
	end

	return Evade.PositionInfo.SetAllUndodgeable()
end

function ove_0_6.GetBestPosition_try(arg_76_0)
	-- function 76
	local slot_76_0 = 0
	local slot_76_1 = 20
	local slot_76_2 = player.boundingRadius * 2
	local slot_76_3 = 1
	local slot_76_4 = ObjectCache.menuCache.cache.ExtraPingBuffer:get() + ObjectCache.gamePing
	local slot_76_5 = ObjectCache.menuCache.cache.ExtraEvadeDistance:get()

	Evade.SpellDetector_table:Update()
	ove_0_6.CalculateEvadeTime()

	if ObjectCache.menuCache.cache.CalculateWindupDelay:get() then
		local slot_76_6 = Evade.lastWindupTime - ove_0_6.TickCount()

		if slot_76_6 > 0 then
			slot_76_4 = slot_76_4 + slot_76_6
		end
	end

	local slot_76_7 = slot_76_4 + Evade.avgCalculationTime

	if ObjectCache.menuCache.cache.HigherPrecision:get() then
		slot_76_1 = 30
		slot_76_2 = player.boundingRadius * 2
	end

	local slot_76_8 = ObjectCache.myHeroCache.serverPos2D
	local slot_76_9 = game.mousePos2D
	local slot_76_10 = {}
	local slot_76_11, slot_76_12 = Evade.SpellDetector_table:GetLowestEvadeTime()
	local slot_76_13 = ove_0_6.InitPositionInfo(game.mousePos2D, slot_76_7, slot_76_5, slot_76_9, slot_76_12)

	if slot_76_5 < slot_76_13.position:dist(to2D(player.pos)) and not navmesh.isWall(slot_76_13.position) and not ove_0_6.isTurrets(slot_76_13.position) then
		table.insert(slot_76_10, slot_76_13)
	end

	local slot_76_14 = ove_0_6.GetFastestPositions()

	for iter_76_0, iter_76_1 in pairs(slot_76_14) do
		local slot_76_15 = ove_0_6.InitPositionInfo(iter_76_1, slot_76_7, slot_76_5, slot_76_9, slot_76_12)

		if slot_76_15.position:dist(to2D(player.pos)) > ObjectCache.myHeroCache.boundingRadius and not navmesh.isWall(slot_76_15.position) and not ove_0_6.isNearEnemy(slot_76_15.position, ObjectCache.menuCache.cache.MinComfortZone:get()) and not ove_0_6.isTurrets(slot_76_15.position) and ObjectCache.menuCache.cache.MinAngle:get() > common.GetAngle(slot_76_15.position, slot_76_9) then
			table.insert(slot_76_10, slot_76_15)
		end
	end

	for iter_76_2 = slot_76_0, slot_76_1 do
		local slot_76_16 = iter_76_2 * 2 * math.pi / slot_76_1

		for iter_76_3 = player.boundingRadius, 1000, slot_76_2 do
			local slot_76_17 = vec2(slot_76_8.x + iter_76_3 * math.cos(slot_76_16), slot_76_8.y + iter_76_3 * math.sin(slot_76_16))

			if navmesh.isWall(slot_76_17) then
				break
			else
				local slot_76_18 = ove_0_6.InitPositionInfo(slot_76_17, slot_76_7, slot_76_5, slot_76_9, slot_76_12)

				if slot_76_18.position:dist(to2D(player.pos)) > ObjectCache.myHeroCache.boundingRadius and not ove_0_6.isTurrets(slot_76_18.position) and not ove_0_6.isNearEnemy(slot_76_17, ObjectCache.menuCache.cache.MinComfortZone:get()) and ObjectCache.menuCache.cache.MinAngle:get() > common.GetAngle(slot_76_18.position, slot_76_9) then
					table.insert(slot_76_10, slot_76_18)
				end
			end
		end
	end

	if #slot_76_10 > 0 then
		if ObjectCache.menuCache.cache.EvadeMode:get() == 1 then
			table.sort(slot_76_10, function(arg_77_0, arg_77_1)
				-- function 77
				if arg_77_0.posDangerCount == arg_77_1.posDangerCount then
					local slot_77_0 = arg_77_0.isDangerousPos_n
					local slot_77_1 = arg_77_1.isDangerousPos_n

					if slot_77_0 == slot_77_1 then
						return arg_77_0.intersectionTime > arg_77_1.intersectionTime
					else
						return slot_77_0 < slot_77_1
					end
				else
					return arg_77_0.posDangerCount < arg_77_1.posDangerCount
				end
			end)

			ove_0_6.fastEvadeMode = true
		elseif ObjectCache.menuCache.cache.EvadeMode:get() == 3 then
			table.sort(slot_76_10, function(arg_78_0, arg_78_1)
				-- function 78
				if arg_78_0.posDangerCount == arg_78_1.posDangerCount then
					local slot_78_0 = arg_78_0.isDangerousPos_n
					local slot_78_1 = arg_78_1.isDangerousPos_n

					if slot_78_0 == slot_78_1 then
						return arg_78_0.distanceToMouse < arg_78_1.distanceToMouse
					else
						return slot_78_0 < slot_78_1
					end
				else
					return arg_78_0.posDangerCount < arg_78_1.posDangerCount
				end
			end)

			if slot_76_10[1].posDangerCount ~= 0 or ove_0_6.CheckDangerousPos(slot_76_10[1].position, player.boundingRadius) then
				table.sort(slot_76_10, function(arg_79_0, arg_79_1)
					-- function 79
					if arg_79_0.posDangerCount == arg_79_1.posDangerCount then
						if arg_79_0.posDangerLevel == arg_79_1.posDangerLevel then
							local slot_79_0 = arg_79_0.isDangerousPos_n
							local slot_79_1 = arg_79_1.isDangerousPos_n

							if slot_79_0 == slot_79_1 then
								return arg_79_0.position:dist(to2D(player.pos)) < arg_79_1.position:dist(to2D(player.pos))
							else
								return slot_79_0 < slot_79_1
							end
						else
							return arg_79_0.posDangerLevel < arg_79_1.posDangerLevel
						end
					else
						return arg_79_0.posDangerLevel < arg_79_1.posDangerLevel
					end
				end)

				if slot_76_10[1].posDangerCount == 0 then
					ove_0_6.fastEvadeMode = true
				end
			end
		elseif ove_0_6.fastEvadeMode then
			table.sort(slot_76_10, function(arg_80_0, arg_80_1)
				-- function 80
				if arg_80_0.posDangerCount == arg_80_1.posDangerCount then
					local slot_80_0 = arg_80_0.isDangerousPos_n
					local slot_80_1 = arg_80_1.isDangerousPos_n

					if slot_80_0 == slot_80_1 then
						return arg_80_0.intersectionTime > arg_80_1.intersectionTime
					else
						return slot_80_0 < slot_80_1
					end
				else
					return arg_80_0.posDangerCount < arg_80_1.posDangerCount
				end
			end)
		elseif ObjectCache.menuCache.cache.FastEvadeActivationTime:get() > 0 and slot_76_11 < ObjectCache.menuCache.cache.FastEvadeActivationTime:get() + ObjectCache.gamePing * 2 + slot_76_7 then
			table.sort(slot_76_10, function(arg_81_0, arg_81_1)
				-- function 81
				if arg_81_0.posDangerCount == arg_81_1.posDangerCount then
					if arg_81_0.posDangerLevel == arg_81_1.posDangerLevel then
						local slot_81_0 = arg_81_0.isDangerousPos_n
						local slot_81_1 = arg_81_1.isDangerousPos_n

						if slot_81_0 == slot_81_1 then
							return arg_81_0.position:dist(to2D(player.pos)) < arg_81_1.position:dist(to2D(player.pos))
						else
							return slot_81_0 < slot_81_1
						end
					else
						return arg_81_0.posDangerLevel < arg_81_1.posDangerLevel
					end
				else
					return arg_81_0.posDangerLevel < arg_81_1.posDangerLevel
				end
			end)

			ove_0_6.fastEvadeMode = true
		else
			table.sort(slot_76_10, function(arg_82_0, arg_82_1)
				-- function 82
				if arg_82_0.posDangerCount == arg_82_1.posDangerCount then
					if arg_82_0.isDangerousPos_n == arg_82_1.isDangerousPos_n then
						return arg_82_0.distanceToMouse < arg_82_1.distanceToMouse
					else
						return arg_82_0.isDangerousPos_n < arg_82_1.isDangerousPos_n
					end
				else
					return arg_82_0.posDangerCount < arg_82_1.posDangerCount
				end
			end)

			if slot_76_10[1].posDangerCount ~= 0 or ove_0_6.CheckDangerousPos(slot_76_10[1].position, player.boundingRadius) then
				table.sort(slot_76_10, function(arg_83_0, arg_83_1)
					-- function 83
					if arg_83_0.posDangerCount == arg_83_1.posDangerCount then
						if arg_83_0.posDangerLevel == arg_83_1.posDangerLevel then
							local slot_83_0 = arg_83_0.isDangerousPos_n
							local slot_83_1 = arg_83_1.isDangerousPos_n

							if slot_83_0 == slot_83_1 then
								return arg_83_0.position:dist(to2D(player.pos)) < arg_83_1.position:dist(to2D(player.pos))
							else
								return slot_83_0 < slot_83_1
							end
						else
							return arg_83_0.posDangerLevel < arg_83_1.posDangerLevel
						end
					else
						return arg_83_0.posDangerLevel < arg_83_1.posDangerLevel
					end
				end)

				if slot_76_10[1].posDangerCount == 0 then
					ove_0_6.fastEvadeMode = true
				end
			end
		end

		if ove_0_6.fastEvadeMode and (slot_76_10[1].posDangerCount ~= 0 or ove_0_6.CheckDangerousPos(slot_76_10[1].position, player.boundingRadius)) then
			table.sort(slot_76_10, function(arg_84_0, arg_84_1)
				-- function 84
				if arg_84_0.posDangerCount == arg_84_1.posDangerCount then
					if arg_84_0.posDangerLevel == arg_84_1.posDangerLevel then
						local slot_84_0 = arg_84_0.isDangerousPos_n
						local slot_84_1 = arg_84_1.isDangerousPos_n

						if slot_84_0 == slot_84_1 then
							return arg_84_0.position:dist(to2D(player.pos)) < arg_84_1.position:dist(to2D(player.pos))
						else
							return slot_84_0 < slot_84_1
						end
					else
						return arg_84_0.posDangerLevel < arg_84_1.posDangerLevel
					end
				else
					return arg_84_0.posDangerLevel < arg_84_1.posDangerLevel
				end
			end)
		end

		if arg_76_0 then
			for iter_76_4, iter_76_5 in ipairs(slot_76_10) do
				if not iter_76_5.isDangerousPos then
					graphics:DrawCircle3d(to3D(iter_76_5.position), ObjectCache.myHeroCache.boundingRadius, 4278255615, 32, 2)
				end
			end
		end

		for iter_76_6, iter_76_7 in ipairs(slot_76_10) do
			if not ove_0_6.CheckDangerousPos(iter_76_7.position, player.boundingRadius) then
				if ove_0_6.fastEvadeMode and slot_76_5 > 0 then
					local slot_76_19 = ove_0_6.GetExtendedSafePosition(ObjectCache.myHeroCache.serverPos2D, iter_76_7.position, slot_76_5)
					local slot_76_20 = ove_0_6.CanHeroWalkToPos(slot_76_19, ObjectCache.myHeroCache.moveSpeed, ObjectCache.gamePing, 0)

					if not slot_76_20.isDangerousPos then
						return slot_76_20
					end
				end

				if ove_0_6.CheckDangerousPos(iter_76_7.position, slot_76_5) and slot_76_5 > 0 then
					local slot_76_21 = ove_0_6.GetExtendedSafePosition(ObjectCache.myHeroCache.serverPos2D, iter_76_7.position, slot_76_5)
					local slot_76_22 = ove_0_6.CanHeroWalkToPos(slot_76_21, ObjectCache.myHeroCache.moveSpeed, ObjectCache.gamePing, 0)

					if not slot_76_22.isDangerousPos then
						return slot_76_22
					end
				end

				return iter_76_7
			end
		end
	end

	return Evade.PositionInfo.SetAllUndodgeable()
end

function ove_0_6.calcPos(arg_85_0)
	-- function 85
	if ObjectCache.menuCache.cache.calc_pos:get() then
		local slot_85_0, slot_85_1 = player.path:calcPos(to3D(arg_85_0))
		local slot_85_2 = {}

		for iter_85_0 = 0, slot_85_1 - 1 do
			table.insert(slot_85_2, slot_85_0[iter_85_0]:to2D())
		end

		return slot_85_2
	end

	local slot_85_3 = {}

	table.insert(slot_85_3, ObjectCache.myHeroCache.serverPos2D)
	table.insert(slot_85_3, arg_85_0)

	return slot_85_3
end

function ove_0_6.GetBestPositionBlink(arg_86_0)
	-- function 86
	local slot_86_0 = 0
	local slot_86_1 = 100
	local slot_86_2 = 50
	local slot_86_3 = 0
	local slot_86_4 = math.max(100, ObjectCache.menuCache.cache.ExtraEvadeDistance:get())
	local slot_86_5 = ObjectCache.myHeroCache.serverPos2D
	local slot_86_6 = game.mousePos2D
	local slot_86_7 = ObjectCache.menuCache.cache.MinComfortZone:get()
	local slot_86_8 = 50
	local slot_86_9 = arg_86_0.range

	if arg_86_0.fixedRange then
		slot_86_8 = arg_86_0.range
		slot_86_9 = arg_86_0.range
	end

	local slot_86_10 = {}

	while slot_86_0 < slot_86_1 do
		slot_86_3 = slot_86_3 + 1

		local slot_86_11 = slot_86_3 * (2 * slot_86_2) + (slot_86_8 - 2 * slot_86_2)
		local slot_86_12 = math.ceil(2 * math.pi * slot_86_11 / (2 * slot_86_2))

		for iter_86_0 = 1, slot_86_12 do
			slot_86_0 = slot_86_0 + 1

			local slot_86_13 = 2 * math.pi / (slot_86_12 - 1) * iter_86_0
			local slot_86_14 = vec2(math.floor(slot_86_5.x + slot_86_11 * math.cos(slot_86_13)), math.floor(slot_86_5.y + slot_86_11 * math.sin(slot_86_13)))

			if not navmesh.isWall(slot_86_14) and not ove_0_6.isTurrets(slot_86_14) then
				local slot_86_15 = ove_0_6.CheckDangerousPosSpell2(slot_86_14, slot_86_4)
				local slot_86_16 = ove_0_6.GetPositionValue(slot_86_14)
				local slot_86_17 = {
					position = slot_86_14,
					isDangerousPos = slot_86_15,
					isDangerousPos_n = slot_86_15 and 1 or 0,
					distanceToMouse = slot_86_16,
					timestamp = ove_0_6.TickCount(),
					hasExtraDistance = slot_86_9 > ove_0_6.GetDistanceToChampions(slot_86_14) and 0 or 1
				}

				table.insert(slot_86_10, slot_86_17)
				--print("insert insertinsertinsertinsert")
			end
		end

		if slot_86_9 <= slot_86_11 then
			break
		end
	end

	if #slot_86_10 > 0 then
		table.sort(slot_86_10, function(arg_87_0, arg_87_1)
			-- function 87
			if arg_87_0.hasExtraDistance == arg_87_1.hasExtraDistance then
				if arg_87_0.isDangerousPos_n == arg_87_1.isDangerousPos_n then
					return arg_87_0.distanceToMouse < arg_87_1.distanceToMouse
				else
					return arg_87_0.isDangerousPos_n < arg_87_1.isDangerousPos_n
				end
			else
				return arg_87_0.hasExtraDistance > arg_87_1.hasExtraDistance
			end
		end)

		for iter_86_1, iter_86_2 in ipairs(slot_86_10) do
			return iter_86_2
		end
	end

	return nil
end

function ove_0_6.GetBestPositionDash(arg_88_0)
	-- function 88
	local slot_88_0 = 0
	local slot_88_1 = 100
	local slot_88_2 = 50
	local slot_88_3 = 0
	local slot_88_4 = ObjectCache.menuCache.cache.ExtraPingBuffer:get()
	local slot_88_5 = math.max(100, ObjectCache.menuCache.cache.ExtraEvadeDistance:get())
	local slot_88_6 = ObjectCache.menuCache.cache.ExtraCPADistance:get()
	local slot_88_7 = ObjectCache.myHeroCache.serverPos2DPing
	local slot_88_8 = game.mousePos2D
	local slot_88_9 = {}
	local slot_88_10 = 50
	local slot_88_11 = arg_88_0.range

	if arg_88_0.fixedRange then
		slot_88_10 = arg_88_0.range
		slot_88_11 = arg_88_0.range
	end

	if not arg_88_0.spellDelay and arg_88_0.delay then
		arg_88_0.spellDelay = arg_88_0.delay
	end

	if arg_88_0.spellName == "KaisaR" then
		local slot_88_12 = arg_88_0.range_tb[player:spellSlot(3).level] or 0

		for iter_88_0, iter_88_1 in ipairs(common.GetEnemyHeroes()) do
			if common.isTarget(iter_88_1, slot_88_12) and iter_88_1.buff.kaisapassivemarkerr then
				slot_88_7 = to2D(iter_88_1.path.serverPos)

				while slot_88_0 < slot_88_1 do
					slot_88_3 = slot_88_3 + 1

					local slot_88_13 = slot_88_3 * (2 * slot_88_2) + (slot_88_10 - 2 * slot_88_2)
					local slot_88_14 = math.ceil(2 * math.pi * slot_88_13 / (2 * slot_88_2))

					for iter_88_2 = 1, slot_88_14 do
						slot_88_0 = slot_88_0 + 1

						local slot_88_15 = 2 * math.pi / (slot_88_14 - 1) * iter_88_2
						local slot_88_16 = vec2(math.floor(slot_88_7.x + slot_88_13 * math.cos(slot_88_15)), math.floor(slot_88_7.y + slot_88_13 * math.sin(slot_88_15)))
						local slot_88_17

						if not navmesh.isWall(slot_88_16) and not ove_0_6.isTurrets(slot_88_16) then
							local slot_88_18 = ove_0_6.CanHeroWalkToPos(slot_88_16, arg_88_0.speed, arg_88_0.spellDelay + slot_88_4 + ObjectCache.gamePing / 2, slot_88_6)

							slot_88_18.isDangerousPos = ove_0_6.CheckDangerousPosSpell(slot_88_16, 6 + player.boundingRadius)
							slot_88_18.isDangerousPos_n = slot_88_18.isDangerousPos and 1 or 0
							slot_88_18.hasExtraDistance = ove_0_6.GetDistanceToChampions(slot_88_16) < math.max(slot_88_11, player.attackRange + player.boundingRadius) and 0 or 1
							slot_88_18.distanceToMouse = ove_0_6.GetPositionValue(slot_88_16)

							table.insert(slot_88_9, slot_88_18)
						end
					end

					if slot_88_11 <= slot_88_13 then
						break
					end
				end
			end
		end
	else
		while slot_88_0 < slot_88_1 do
			slot_88_3 = slot_88_3 + 1

			local slot_88_19 = slot_88_3 * (2 * slot_88_2) + (slot_88_10 - 2 * slot_88_2)
			local slot_88_20 = math.ceil(2 * math.pi * slot_88_19 / (2 * slot_88_2))

			for iter_88_3 = 1, slot_88_20 do
				slot_88_0 = slot_88_0 + 1

				local slot_88_21 = 2 * math.pi / (slot_88_20 - 1) * iter_88_3
				local slot_88_22 = vec2(math.floor(slot_88_7.x + slot_88_19 * math.cos(slot_88_21)), math.floor(slot_88_7.y + slot_88_19 * math.sin(slot_88_21)))

				if not navmesh.isWall(slot_88_22) and not ove_0_6.isTurrets(slot_88_22) then
					local slot_88_23 = ove_0_6.CanHeroWalkToPos(slot_88_22, arg_88_0.speed, arg_88_0.spellDelay + slot_88_4 + ObjectCache.gamePing / 2, slot_88_6)

					slot_88_23.isDangerousPos = ove_0_6.CheckDangerousPosSpell(slot_88_22, 6 + player.boundingRadius)
					slot_88_23.isDangerousPos_n = slot_88_23.isDangerousPos and 1 or 0
					slot_88_23.hasExtraDistance = ove_0_6.GetDistanceToChampions(slot_88_22) < math.max(slot_88_11, player.attackRange + player.boundingRadius) and 0 or 1
					slot_88_23.distanceToMouse = ove_0_6.GetPositionValue(slot_88_22)

					table.insert(slot_88_9, slot_88_23)
				end
			end

			if slot_88_11 <= slot_88_19 then
				break
			end
		end
	end

	if #slot_88_9 > 0 then
		table.sort(slot_88_9, function(arg_89_0, arg_89_1)
			-- function 89
			if arg_89_0.posDangerCount == arg_89_1.posDangerCount then
				if arg_89_0.posDangerLevel == arg_89_1.posDangerLevel then
					if arg_89_0.isDangerousPos_n == arg_89_1.isDangerousPos_n then
						if arg_89_0.hasExtraDistance == arg_89_1.hasExtraDistance then
							return arg_89_0.distanceToMouse < arg_89_1.distanceToMouse
						else
							return arg_89_0.hasExtraDistance > arg_89_1.hasExtraDistance
						end
					else
						return arg_89_1.isDangerousPos_n > arg_89_0.isDangerousPos_n
					end
				else
					return arg_89_0.posDangerLevel < arg_89_1.posDangerLevel
				end
			else
				return arg_89_0.posDangerCount < arg_89_1.posDangerCount
			end
		end)

		for iter_88_4, iter_88_5 in ipairs(slot_88_9) do
			return iter_88_5
		end
	end
end

function ove_0_6.GetBestPositionDashPos(arg_90_0)
	-- function 90
	local slot_90_0 = 0
	local slot_90_1 = 100
	local slot_90_2 = 50
	local slot_90_3 = 0
	local slot_90_4 = ObjectCache.menuCache.cache.ExtraPingBuffer:get()
	local slot_90_5 = math.max(100, ObjectCache.menuCache.cache.ExtraEvadeDistance:get())
	local slot_90_6 = ObjectCache.menuCache.cache.ExtraCPADistance:get()
	local slot_90_7 = ObjectCache.myHeroCache.serverPos2DPing
	local slot_90_8 = game.mousePos2D
	local slot_90_9 = {}
	local slot_90_10 = 50
	local slot_90_11 = arg_90_0.range

	if arg_90_0.fixedRange then
		slot_90_10 = arg_90_0.range
		slot_90_11 = arg_90_0.range
	end

	if not arg_90_0.spellDelay and arg_90_0.delay then
		arg_90_0.spellDelay = arg_90_0.delay
	end

	if arg_90_0.spellName == "KaisaR" then
		local slot_90_12 = arg_90_0.range_tb[player:spellSlot(3).level] or 0

		for iter_90_0, iter_90_1 in ipairs(common.GetEnemyHeroes()) do
			if common.isTarget(iter_90_1, slot_90_12) and iter_90_1.buff.kaisapassivemarkerr then
				slot_90_7 = to2D(iter_90_1.path.serverPos)

				while slot_90_0 < slot_90_1 do
					slot_90_3 = slot_90_3 + 1

					local slot_90_13 = slot_90_3 * (2 * slot_90_2) + (slot_90_10 - 2 * slot_90_2)
					local slot_90_14 = math.ceil(2 * math.pi * slot_90_13 / (2 * slot_90_2))

					for iter_90_2 = 1, slot_90_14 do
						slot_90_0 = slot_90_0 + 1

						local slot_90_15 = 2 * math.pi / (slot_90_14 - 1) * iter_90_2
						local slot_90_16 = vec2(math.floor(slot_90_7.x + slot_90_13 * math.cos(slot_90_15)), math.floor(slot_90_7.y + slot_90_13 * math.sin(slot_90_15)))

						if not navmesh.isWall(slot_90_16) and not ove_0_6.isTurrets(slot_90_16) then
							local slot_90_17 = ove_0_6.CanHeroWalkToPos(slot_90_16, arg_90_0.speed, arg_90_0.spellDelay + slot_90_4 + ObjectCache.gamePing / 2, slot_90_6)

							slot_90_17.isDangerousPos = ove_0_6.CheckDangerousPosSpell(slot_90_16, 6 + player.boundingRadius)
							slot_90_17.isDangerousPos_n = slot_90_17.isDangerousPos and 1 or 0
							slot_90_17.hasExtraDistance = ove_0_6.GetDistanceToChampions(slot_90_16) < math.max(slot_90_11, player.attackRange + player.boundingRadius) and 0 or 1
							slot_90_17.distanceToMouse = ove_0_6.GetPositionValue(slot_90_16)

							table.insert(slot_90_9, slot_90_17)
						end
					end

					if slot_90_11 <= slot_90_13 then
						break
					end
				end
			end
		end
	else
		while slot_90_0 < slot_90_1 do
			slot_90_3 = slot_90_3 + 1

			local slot_90_18 = slot_90_3 * (2 * slot_90_2) + (slot_90_10 - 2 * slot_90_2)
			local slot_90_19 = math.ceil(2 * math.pi * slot_90_18 / (2 * slot_90_2))

			for iter_90_3 = 1, slot_90_19 do
				slot_90_0 = slot_90_0 + 1

				local slot_90_20 = 2 * math.pi / (slot_90_19 - 1) * iter_90_3
				local slot_90_21 = vec2(math.floor(slot_90_7.x + slot_90_18 * math.cos(slot_90_20)), math.floor(slot_90_7.y + slot_90_18 * math.sin(slot_90_20)))

				if not navmesh.isWall(slot_90_21) and not ove_0_6.isTurrets(slot_90_21) then
					local slot_90_22 = ove_0_6.CanHeroWalkToPos(slot_90_21, arg_90_0.speed, arg_90_0.spellDelay + slot_90_4 + ObjectCache.gamePing / 2, slot_90_6)

					slot_90_22.isDangerousPos = ove_0_6.CheckDangerousPosSpell(slot_90_21, 6 + player.boundingRadius)
					slot_90_22.isDangerousPos_n = slot_90_22.isDangerousPos and 1 or 0
					slot_90_22.hasExtraDistance = ove_0_6.GetDistanceToChampions(slot_90_21) < math.max(slot_90_11, 800) and 0 or 1
					slot_90_22.hasExtraDistance1 = ove_0_6.GetDistanceToChampions(slot_90_21)
					slot_90_22.distanceToMouse = ove_0_6.GetPositionValue(slot_90_21)

					table.insert(slot_90_9, slot_90_22)
				end
			end

			if slot_90_11 <= slot_90_18 then
				break
			end
		end
	end

	if #slot_90_9 > 0 then
		table.sort(slot_90_9, function(arg_91_0, arg_91_1)
			-- function 91
			if arg_91_0.posDangerCount == arg_91_1.posDangerCount then
				if arg_91_0.posDangerLevel == arg_91_1.posDangerLevel then
					if arg_91_0.isDangerousPos_n == arg_91_1.isDangerousPos_n then
						if arg_91_0.hasExtraDistance == arg_91_1.hasExtraDistance then
							return arg_91_0.distanceToMouse < arg_91_1.distanceToMouse
						else
							return arg_91_0.hasExtraDistance > arg_91_1.hasExtraDistance
						end
					else
						return arg_91_1.isDangerousPos_n > arg_91_0.isDangerousPos_n
					end
				else
					return arg_91_0.posDangerLevel < arg_91_1.posDangerLevel
				end
			else
				return arg_91_0.posDangerCount < arg_91_1.posDangerCount
			end
		end)

		for iter_90_4, iter_90_5 in ipairs(slot_90_9) do
			if iter_90_5.posDangerCount == 0 then
				return iter_90_5
			end

			return
		end
	end
end

function ove_0_6.GetSpellList()
	-- function 92
	local slot_92_0 = {}

	for iter_92_0, iter_92_1 in pairs(spells) do
		table.insert(slot_92_0, iter_92_1.spellID)
	end

	return slot_92_0
end

function ove_0_6.HasSpellShield()
	-- function 93
	if player.buff.sivire or player.buff.nocturnew or player.buff.fioraw or player.buff.kayler or player.buff.yuumiwattach or player.buff.kogmawicathiansurprise or player.buff.olafragnarok or player:spellSlot(3).name == "JhinRShot" or player.isZombie then
		return true
	end

	return false
end

function ove_0_6.CommonChecks()
	-- function 94
	return Evade.isChanneling or player.isDead or player.buff[BUFF_INVULNERABILITY] or player.buff.sionr or player.buff.samiraw or player.path.isDashing and player.charName ~= "Kalista" or ove_0_6.HasSpellShield() or player.buff.missfortunebulletsound or player.buff.xerathrshots or player.buff.malzaharrsound
end

function ove_0_6.ShouldDodge()
	-- function 95
	if ObjectCache.menuCache.cache.Enabled:get() or ObjectCache.menuCache.cache.CloseEnabled:get() or ove_0_6.CommonChecks() or Evade.time > os.clock() then
		return false
	end

	if ObjectCache.menuCache.cache.tower:get() and ove_0_6.GetDistanceToTurrets(ObjectCache.myHeroCache.serverPos2D) < 750 then
		return false
	end

	if ObjectCache.menuCache.cache.screen:get() and not player.isOnScreen then
		return false
	end

	if ObjectCache.menuCache.cache.lowhp:get() and orb and orb.iscombo() and TS then
		local slot_95_0 = TS.getorbtarget(150)

		if slot_95_0 and (slot_95_0.health / slot_95_0.maxHealth * 100 <= ObjectCache.menuCache.cache.hp:get() or orb.get_damage(player, slot_95_0, true) * 1 > slot_95_0.health) and slot_95_0.health / slot_95_0.maxHealth * 100 < player.health / player.maxHealth * 100 then
			return false
		end
	end

	if ObjectCache.menuCache.cache.recalling:get() and player.isRecalling and not player.buff.yuumiwally then
		return false
	end

	return true
end

function ove_0_6.ShouldUseEvadeSpell()
	-- function 96
	if ObjectCache.menuCache.cache.ActivateEvadeSpells:get() or ove_0_6.CommonChecks() or Evade.lastWindupTime - ove_0_6.TickCount() > 0 or Evade.time > os.clock() then
		return false
	end

	return true
end

function ove_0_6.check_safe(arg_97_0)
	-- function 97
	if not common.ista(player.pos, -100) and common.ista(arg_97_0, 150) then
		return false
	end

	return common.check_safe(arg_97_0)
end

function ove_0_6.GetBestPositionTargetedDash(arg_98_0)
	-- function 98
	local slot_98_0 = ObjectCache.menuCache.cache.ExtraPingBuffer:get()
	local slot_98_1 = math.max(100, ObjectCache.menuCache.cache.ExtraEvadeDistance:get())
	local slot_98_2 = ObjectCache.menuCache.cache.ExtraCPADistance:get()
	local slot_98_3 = ObjectCache.myHeroCache.serverPos2DPing
	local slot_98_4 = game.mousePos2D
	local slot_98_5 = {}
	local slot_98_6 = 50
	local slot_98_7 = math.huge

	if arg_98_0.fixedRange then
		local slot_98_8 = arg_98_0.range
	end

	local slot_98_9 = {}

	if arg_98_0.spellTargets.Targetables then
		for iter_98_0, iter_98_1 in ipairs(common.GetEnemyHeroes()) do
			if common.isTarget(iter_98_1) and iter_98_1.pos:dist(player.pos) <= arg_98_0.range then
				table.insert(slot_98_9, iter_98_1)
			end
		end

		for iter_98_2, iter_98_3 in ipairs(common.GetAllyHeroes()) do
			if common.isTarget(iter_98_3) and iter_98_3.ptr ~= player.ptr and iter_98_3.pos:dist(player.pos) <= arg_98_0.range then
				table.insert(slot_98_9, iter_98_3)
			end
		end
	else
		if arg_98_0.spellTargets.EnemyChampions and arg_98_0.spellTargets.AllyChampions then
			for iter_98_4, iter_98_5 in ipairs(common.GetEnemyHeroes()) do
				if common.isTarget(iter_98_5) and iter_98_5.pos:dist(player.pos) <= arg_98_0.range then
					table.insert(slot_98_9, iter_98_5)
				end
			end

			for iter_98_6, iter_98_7 in ipairs(common.GetAllyHeroes()) do
				if common.isTarget(iter_98_7) and iter_98_7.ptr ~= player.ptr and iter_98_7.pos:dist(player.pos) <= arg_98_0.range then
					table.insert(slot_98_9, iter_98_7)
				end
			end
		elseif arg_98_0.spellTargets.EnemyChampions then
			for iter_98_8, iter_98_9 in ipairs(common.GetEnemyHeroes()) do
				if common.isTarget(iter_98_9) and iter_98_9.pos:dist(player.pos) <= arg_98_0.range then
					table.insert(slot_98_9, iter_98_9)
				end
			end
		elseif arg_98_0.spellTargets.AllyChampions then
			for iter_98_10, iter_98_11 in ipairs(common.GetAllyHeroes()) do
				if common.isTarget(iter_98_11) and iter_98_11.ptr ~= player.ptr and iter_98_11.pos:dist(player.pos) <= arg_98_0.range then
					table.insert(slot_98_9, iter_98_11)
				end
			end
		end

		if arg_98_0.spellTargets.EnemyMinions and arg_98_0.spellTargets.AllyMinions then
			local slot_98_10 = objManager.minions.size[TEAM_ENEMY]
			local slot_98_11 = objManager.minions[TEAM_ENEMY]

			for iter_98_12 = 0, slot_98_10 - 1 do
				local slot_98_12 = slot_98_11[iter_98_12]

				if common.isTarget(slot_98_12, arg_98_0.range) then
					table.insert(slot_98_9, slot_98_12)
				end
			end

			local slot_98_13 = objManager.minions.size[TEAM_ALLY]
			local slot_98_14 = objManager.minions[TEAM_ALLY]

			for iter_98_13 = 0, slot_98_13 - 1 do
				local slot_98_15 = slot_98_14[iter_98_13]

				if common.isTarget(slot_98_15, arg_98_0.range) then
					table.insert(slot_98_9, slot_98_15)
				end
			end
		elseif arg_98_0.spellTargets.EnemyMinions then
			local slot_98_16 = objManager.minions.size[TEAM_ENEMY]
			local slot_98_17 = objManager.minions[TEAM_ENEMY]

			for iter_98_14 = 0, slot_98_16 - 1 do
				local slot_98_18 = slot_98_17[iter_98_14]

				if common.isTarget(slot_98_18, arg_98_0.range) then
					table.insert(slot_98_9, slot_98_18)
				end
			end

			local slot_98_19 = objManager.minions.size[TEAM_NEUTRAL]
			local slot_98_20 = objManager.minions[TEAM_NEUTRAL]

			for iter_98_15 = 0, slot_98_19 - 1 do
				local slot_98_21 = slot_98_20[iter_98_15]

				if common.isTarget(slot_98_21, arg_98_0.range) then
					table.insert(slot_98_9, slot_98_21)
				end
			end
		elseif arg_98_0.spellTargets.AllyMinions then
			local slot_98_22 = objManager.minions.size[TEAM_ALLY]
			local slot_98_23 = objManager.minions[TEAM_ALLY]

			for iter_98_16 = 0, slot_98_22 - 1 do
				local slot_98_24 = slot_98_23[iter_98_16]

				if common.isTarget(slot_98_24, arg_98_0.range) then
					table.insert(slot_98_9, slot_98_24)
				end
			end

			for iter_98_17 = 0, objManager.wardsAlly.size do
				local slot_98_25 = objManager.wardsAlly[iter_98_17]

				if slot_98_25.isWard and slot_98_25.pos:dist(player.pos) < arg_98_0.range then
					table.insert(slot_98_9, slot_98_25)
				end
			end
		end

		for iter_98_18, iter_98_19 in ipairs(slot_98_9) do
			local slot_98_26 = to2D(iter_98_19.path.serverPos)
			local slot_98_27 = {}
			local slot_98_28 = true

			if arg_98_0.dashRange then
				local slot_98_29 = player.pos
				local slot_98_30 = iter_98_19.path.serverPos
				local slot_98_31 = slot_98_29:dist(slot_98_30)

				slot_98_26 = to2D(slot_98_29 + (slot_98_30 - slot_98_29):norm() * arg_98_0.dashRange)
			end

			if arg_98_0.spellName == "YasuoE" then
				local slot_98_32 = player.pos
				local slot_98_33 = iter_98_19.path.serverPos
				local slot_98_34 = slot_98_32:dist(slot_98_33)
				local slot_98_35 = slot_98_32 + (slot_98_33 - slot_98_32):norm() * 475

				if iter_98_19.buff.yasuoe or not ove_0_6.check_safe(slot_98_35) then
					slot_98_28 = false
				end
			elseif ove_0_6.isTurrets(slot_98_26) then
				slot_98_28 = false
			end

			if slot_98_28 then
				if arg_98_0.behindTarget then
					slot_98_26 = slot_98_26 - (slot_98_26 - slot_98_3):norm() * (iter_98_19.boundingRadius + ObjectCache.myHeroCache.boundingRadius)
				end

				if arg_98_0.fixedRange then
					slot_98_26 = slot_98_3 + (slot_98_26 - slot_98_3):norm() * arg_98_0.range
				end

				if arg_98_0.evadeType == EvadeType.Dash then
					slot_98_27 = ove_0_6.CanHeroWalkToPos(slot_98_26, arg_98_0.speed, arg_98_0.spellDelay + slot_98_0 + ObjectCache.gamePing, slot_98_2)
					slot_98_27.isDangerousPos = ove_0_6.CheckDangerousPosSpell(slot_98_26, 6)
					slot_98_27.distanceToMouse = ove_0_6.GetPositionValue(slot_98_26)
				else
					local slot_98_36 = ove_0_6.CheckDangerousPosSpell(slot_98_26, 6)
					local slot_98_37 = ove_0_6.GetPositionValue(slot_98_26)

					slot_98_27.position = slot_98_26
					slot_98_27.isDangerousPos = slot_98_36
					slot_98_27.isDangerousPos_n = slot_98_27.isDangerousPos and 1 or 0
					slot_98_27.distanceToMouse = slot_98_37
					slot_98_27.timestamp = ove_0_6.TickCount()
				end

				slot_98_27.target = iter_98_19

				table.insert(slot_98_5, slot_98_27)
			end
		end

		if #slot_98_5 > 0 then
			if arg_98_0.evadeType == EvadeType.Dash then
				table.sort(slot_98_5, function(arg_99_0, arg_99_1)
					-- function 99
					if arg_99_0.posDangerCount == arg_99_1.posDangerCount then
						if arg_99_0.posDangerLevel == arg_99_1.posDangerLevel then
							local slot_99_0 = arg_99_0.isDangerousPos_n
							local slot_99_1 = arg_99_1.isDangerousPos_n

							if slot_99_0 == slot_99_1 then
								return arg_99_0.distanceToMouse < arg_99_1.distanceToMouse
							else
								return slot_99_0 < slot_99_1
							end
						else
							return arg_99_0.posDangerLevel < arg_99_1.posDangerLevel
						end
					else
						return arg_99_0.posDangerLevel < arg_99_1.posDangerLevel
					end
				end)

				local slot_98_38 = slot_98_5[1]

				if slot_98_38 and Evade.lastPosInfo and not slot_98_38.isDangerousPos and slot_98_38.posDangerLevel == 0 then
					return slot_98_38
				end
			else
				table.sort(slot_98_5, function(arg_100_0, arg_100_1)
					-- function 100
					local slot_100_0 = arg_100_0.isDangerousPos_n
					local slot_100_1 = arg_100_1.isDangerousPos_n

					if slot_100_0 == slot_100_1 then
						return arg_100_0.distanceToMouse < arg_100_1.distanceToMouse
					else
						return slot_100_0 < slot_100_1
					end
				end)

				return slot_98_5[1]
			end
		end
	end
end

function ove_0_6.GetWaypoints(arg_101_0)
	-- function 101
	local slot_101_0 = {}
	local slot_101_1 = arg_101_0.path
	local slot_101_2 = slot_101_1.serverPos2D

	table.insert(slot_101_0, slot_101_2)

	for iter_101_0 = slot_101_1.index, slot_101_1.count do
		local slot_101_3 = slot_101_1.point[iter_101_0]

		table.insert(slot_101_0, to2D(slot_101_3))
	end

	return slot_101_0
end

function ove_0_6.GetNearWallPoint(arg_102_0, arg_102_1)
	-- function 102
	local slot_102_0 = (arg_102_1 - arg_102_0):norm()
	local slot_102_1 = arg_102_0:dist(arg_102_1)

	for iter_102_0 = 20, slot_102_1, 20 do
		local slot_102_2 = arg_102_1 - slot_102_0 * iter_102_0

		if not navmesh.isWall(slot_102_2) then
			return slot_102_2
		end
	end

	return nil
end

function ove_0_6.CheckMovePath(arg_103_0, arg_103_1, arg_103_2)
	-- function 103
	arg_103_1 = arg_103_1 or 0

	local slot_103_0 = to2D(player.path.serverPos)
	local slot_103_1 = {}

	table.insert(slot_103_1, player.pos2D)
	table.insert(slot_103_1, arg_103_0)

	for iter_103_0, iter_103_1 in ipairs(slot_103_1) do
		if slot_103_0 and ove_0_6.CheckMoveToDirection_path(slot_103_0, iter_103_1, arg_103_1) then
			return true
		end

		slot_103_0 = slot_103_0 and iter_103_1
	end

	if slot_103_0 and ove_0_6.CheckMoveToDirection_path(slot_103_0, arg_103_0, arg_103_1) then
		return true
	end

	return false
end

function ove_0_6.add_vec(arg_104_0, arg_104_1)
	-- function 104
	arg_104_0.vec[arg_104_0.n] = arg_104_1
	arg_104_0.n = arg_104_0.n + 1
end

function ove_0_6.display_pos(arg_105_0)
	-- function 105
	return arg_105_0
end

function ove_0_6.get_evade_vec(arg_106_0)
	-- function 106
	return
end

return ove_0_6

local ove_0_5 = module.internal("TS")
local ove_0_6 = module.internal("orb")
local ove_0_7 = module.internal("pred")
local ove_0_8 = module.load("<PERSON>","taliyah/menu")
local ove_0_9 = player:spellSlot(2)
local ove_0_10
local ove_0_11
local ove_0_12
local ove_0_13 = {}
local ove_0_14 = {
	speed = 1700,
	range = 650,
	delay = 0.25,
	boundingRadiusMod = 0,
	width = 80 -- 增加宽度以提高命中率
}

local function ove_0_15(arg_1_0, arg_1_1)
	-- print 1
	if ove_0_10 and ove_0_10 < os.clock() then
		ove_0_10 = nil

		local slot_1_0 = ove_0_7.core.lerp(arg_1_1.path, 0.525, math.huge)

		if slot_1_0 and slot_1_0:dist(ove_0_11) < 200 then
			local slot_1_1 = slot_1_0 + ove_0_12
			local slot_1_2 = arg_1_0.startPos + (slot_1_1 - arg_1_0.startPos):norm() * 800
			local slot_1_3, slot_1_4 = mathf.closest_vec_line_seg(slot_1_1, arg_1_0.startPos, slot_1_2)

			if slot_1_3 and slot_1_1:dist(slot_1_3) < 337.5 then
				return true
			end
		end
	end

	if arg_1_1.path.isDashing then
		local slot_1_5 = arg_1_1.path.point2D[1]
		local slot_1_6 = arg_1_0.startPos + (arg_1_0.endPos - arg_1_0.startPos):norm() * 800
		local slot_1_7, slot_1_8 = mathf.closest_vec_line_seg(slot_1_5, arg_1_0.startPos, slot_1_6)

		if slot_1_7 and slot_1_5:dist(slot_1_7) < 337.5 then
			return true
		end
	end

	if arg_1_0.startPos:dist(arg_1_0.endPos) > ove_0_14.range then
		return false
	end

	if ove_0_7.trace.linear.hardlock(ove_0_14, arg_1_0, arg_1_1) then
		return true
	end

	if ove_0_7.trace.linear.hardlockmove(ove_0_14, arg_1_0, arg_1_1) then
		return true
	end

	if arg_1_0.startPos:dist(arg_1_0.endPos) < 575 then
		return true
	end

	-- 对被W击飞的敌人更容易命中E
	if arg_1_1.buff[5] or arg_1_1.buff[10] or arg_1_1.buff[11] then
		return true
	end

	-- 对移动速度较慢的敌人更容易命中
	if arg_1_1.moveSpeed < 350 then
		return true
	end
end

local function ove_0_16(arg_2_0, arg_2_1, arg_2_2)
	-- print 2
	if arg_2_2 > ove_0_14.range + 600 then
		return false
	end

	local slot_2_0 = ove_0_7.linear.get_prediction(ove_0_14, arg_2_1)

	if not slot_2_0 then
		return false
	end

	if not ove_0_15(slot_2_0, arg_2_1) then
		return false
	end

	arg_2_0.pos = slot_2_0.endPos

	return true
end

local function ove_0_17()
	-- print 3
	if ove_0_9.state ~= 0 then
		return
	end

	local slot_3_0 = ove_0_5.get_result(ove_0_16)

	if slot_3_0.pos and player:castSpell("pos", 2, vec3(slot_3_0.pos.x, player.y, slot_3_0.pos.y)) then
		ove_0_6.core.set_server_pause()

		return true
	end
end

local function ove_0_18()
	-- print 4
	if ove_0_9.state ~= 0 then
		return
	end

	local slot_4_0 = {}
	local slot_4_1 = 0

	for iter_4_0 = 0, objManager.minions.size[TEAM_ENEMY] - 1 do
		local slot_4_2 = objManager.minions[TEAM_ENEMY][iter_4_0]

		if slot_4_2.isVisible and slot_4_2.isTargetable and slot_4_2.pos:distSqr(player.pos) < 422500 then
			slot_4_1 = slot_4_1 + 1
			slot_4_0[slot_4_1] = slot_4_2
		end
	end

	-- 优化E技能清线：至少需要3个小兵才使用E
	if slot_4_1 < 3 then
		return
	end

	local slot_4_3
	local slot_4_4 = 0
	local slot_4_5 = player.path.serverPos2D

	for iter_4_1 = 1, slot_4_1 do
		local slot_4_6 = slot_4_5 + (slot_4_0[iter_4_1].path.serverPos2D - slot_4_5):norm() * 650
		local slot_4_7 = 1

		for iter_4_2 = 1, slot_4_1 do
			if iter_4_1 ~= iter_4_2 then
				local slot_4_8 = slot_4_0[iter_4_2].path.serverPos2D
				local slot_4_9 = mathf.closest_vec_line(slot_4_8, player.path.serverPos2D, slot_4_6)

				if slot_4_9 and slot_4_8:distSqr(slot_4_9) < 62500 then
					slot_4_7 = slot_4_7 + 1
				end
			end
		end

		if slot_4_4 < slot_4_7 then
			slot_4_3 = slot_4_6
			slot_4_4 = slot_4_7
		end
	end

	if slot_4_3 and slot_4_4 > 2 and player:castSpell("pos", 2, vec3(slot_4_3.x, mousePos.y, slot_4_3.y)) then
		ove_0_6.core.set_server_pause()

		return true
	end
end

local function ove_0_19()
	-- print 5
	if ove_0_9.state ~= 0 then
		return
	end

	local slot_5_0 = {}
	local slot_5_1 = 0

	for iter_5_0 = 0, objManager.minions.size[TEAM_NEUTRAL] - 1 do
		local slot_5_2 = objManager.minions[TEAM_NEUTRAL][iter_5_0]

		if slot_5_2.isVisible and slot_5_2.isTargetable and slot_5_2.pos:distSqr(player.pos) < 422500 then
			slot_5_1 = slot_5_1 + 1
			slot_5_0[slot_5_1] = slot_5_2
		end
	end

	if slot_5_1 == 0 then
		return
	end

	local slot_5_3
	local slot_5_4 = 0
	local slot_5_5 = player.path.serverPos2D

	for iter_5_1 = 1, slot_5_1 do
		local slot_5_6 = slot_5_5 + (slot_5_0[iter_5_1].path.serverPos2D - slot_5_5):norm() * 650
		local slot_5_7 = 1

		for iter_5_2 = 1, slot_5_1 do
			if iter_5_1 ~= iter_5_2 then
				local slot_5_8 = slot_5_0[iter_5_2].path.serverPos2D
				local slot_5_9 = mathf.closest_vec_line(slot_5_8, player.path.serverPos2D, slot_5_6)

				if slot_5_9 and slot_5_8:distSqr(slot_5_9) < 62500 then
					slot_5_7 = slot_5_7 + 1
				end
			end
		end

		if slot_5_4 < slot_5_7 then
			slot_5_3 = slot_5_6
			slot_5_4 = slot_5_7
		end
	end

	if slot_5_3 and player:castSpell("pos", 2, vec3(slot_5_3.x, mousePos.y, slot_5_3.y)) then
		ove_0_6.core.set_server_pause()

		return true
	end
end

local function ove_0_20(arg_6_0)
	-- print 6
	if arg_6_0.name == "TaliyahMine" and arg_6_0.owner == player then
		table.insert(ove_0_13, arg_6_0)
	end
end

local function ove_0_21()
	-- print 7
	if not ove_0_8.e.draw_range:get() then
		return
	end

	if not player.isOnScreen then
		return
	end

	if player.isDead then
		return
	end

	graphics.draw_circle(player.pos, ove_0_14.range, 2, ove_0_8.e.draw_color:get(), 48)
end

local function ove_0_22(arg_8_0)
	-- print 8
	if arg_8_0.name == "TaliyahWVC" then
		ove_0_10 = os.clock() + 0.25
		ove_0_11 = vec2(arg_8_0.endPos.x, arg_8_0.endPos.z)
		ove_0_12 = (arg_8_0.endPosLine2D - arg_8_0.endPos2D):norm() * 365
	end
end

local function ove_0_23()
	-- print 9
	for iter_9_0 = #ove_0_13, 1, -1 do
		if ove_0_13[iter_9_0].isDead then
			table.remove(ove_0_13, iter_9_0)
		end
	end
end

return {
	invoke = ove_0_17,
	invoke_lane_clear = ove_0_18,
	invoke_jungle_clear = ove_0_19,
	mines = ove_0_13,
	on_create_minion = ove_0_20,
	on_process_spell = ove_0_22,
	on_draw = ove_0_21,
	update = ove_0_23
}

local ove_0_0 = "1.0"
local ove_0_1 = module.seek("evade")
local ove_0_2 = module.load("KorniAIOUWU", "SpellDatabaseSupport")
local ove_0_3 = module.internal("pred")
local ove_0_4 = module.internal("TS")
local ove_0_5 = module.internal("orb")
local ove_0_6 = module.load("KorniAIOUWU", "json")
local ove_0_7 = module.load("KorniAIOUWU", "common")
local ove_0_8 = module.load("KorniAIOUWU", "Answer")

if ove_0_8.i == false or ove_0_8.e == nil then
	chat.print("<font color='#ffbfdf'>[Kornis AIO]" .. "<font color='#f73e3e'> HWID doesn't match! </font>")

	if ove_0_8.resets ~= 0 then
		chat.print("<font color='#ffffff'> ~ HWID Reset in " .. ove_0_8.resets .. " ~</font>")
	end

	return
elseif ove_0_8.e then
	chat.print("<font color='#ffbfdf'>[Kornis AIO]" .. "<font color='#f73e3e'> Subscription expired! Buy new one from jkshop.gg! </font>")

	return
else
	chat.print("<font color='#ffbfdf'>[Kornis AIO]" .. "<font color='#FFFFFF'> Subscription expires in </font>" .. ove_0_8.dLeft)
end

value = ove_0_8.h

module.load("KorniAIOUWU", "Limiter")

if ove_0_8.h == false then
	core.reload()
end

local ove_0_9 = {
	range = 10000000
}
local ove_0_10 = {
	range = 10000000
}
local ove_0_11 = {
	delay = 0.4,
	range = 10000000,
	width = 100,
	boundingRadiusMod = 1,
	speed = math.huge
}
local ove_0_12 = {
	range = 10000000
}
local ove_0_13 = menu("KornisAIO2" .. player.charName, "Kornis AIO - " .. player.charName)

ove_0_13:menu("combo", "Combo")
ove_0_13.combo:header("qset", " -- Q Settings --")
ove_0_13.combo:boolean("qcombo", "Use Q", true)
ove_0_13.combo:header("wset", " -- W Settings --")
ove_0_13.combo:boolean("wcombo", "Use W", true)
ove_0_13.combo:boolean("waa", " ^- Only Melee range", true)
ove_0_13.combo:header("eset", " -- E Settings --")
ove_0_13.combo:boolean("usee", "Use E", true)
ove_0_13:menu("harass", "Harass")
ove_0_13.harass:slider("mana", "Mana Manager", 50, 0, 100, 1)
ove_0_13.harass:header("qset", " -- Q Settings --")
ove_0_13.harass:boolean("qcombo", "Use Q", true)
ove_0_13.harass:header("wset", " -- W Settings --")
ove_0_13.harass:boolean("wcombo", "Use W", true)
ove_0_13.harass:boolean("waa", " ^- Only Melee range", true)
ove_0_13.harass:header("eset", " -- E Settings --")
ove_0_13.harass:boolean("usee", "Use E", true)
ove_0_13:menu("farming", "Farming")
ove_0_13.farming:keybind("toggle", "Farm Toggle", nil, "A")
ove_0_13.farming:header("~~", " ~~~~ ")
ove_0_13.farming:menu("laneclear", "Lane Clear")
ove_0_13.farming.laneclear:boolean("farmw", "Use W", true)
ove_0_13.farming.laneclear:slider("hitsw", " ^- if Hits X Minions", 3, 1, 6, 1)
ove_0_13.farming.laneclear:boolean("farme", "Use E", true)
ove_0_13.farming.laneclear:slider("hitse", " ^- if Hits X Minions", 3, 1, 6, 1)
ove_0_13.farming:menu("jungleclear", "Jungle Clear")
ove_0_13.farming.jungleclear:boolean("farmq", "Use Q", true)
ove_0_13.farming.jungleclear:boolean("farmw", "Use W", true)
ove_0_13.farming.jungleclear:boolean("farme", "Use E", true)
ove_0_13:menu("killsteal", "Killsteal")
ove_0_13.killsteal:boolean("ksq", "Use Q", true)
ove_0_13.killsteal:boolean("kse", "Use E", true)
ove_0_13:menu("draws", "Draw Settings")
ove_0_13.draws:header("ranges", " -- Ranges -- ")
ove_0_13.draws:boolean("drawq", "Draw Q Range", true)
ove_0_13.draws:color("colorq", "  ^- Color", 153, 204, 255, 255)
ove_0_13.draws:boolean("draww", "Draw W Range", false)
ove_0_13.draws:color("colorw", "  ^- Color", 255, 153, 153, 255)
ove_0_13.draws:boolean("drawe", "Draw E Range", true)
ove_0_13.draws:color("colore", "  ^- Color", 255, 153, 153, 255)
ove_0_13.draws:header("other", " -- Other -- ")
ove_0_13.draws:boolean("drawdamage", "Draw Damage", true)
ove_0_13.draws:slider("transparency", "Damage Drawing Transparency", 155, 0, 255, 1)
ove_0_13.draws:slider("toggletransparency", "Toggle Drawing Transparency", 200, 50, 255, 1)
ove_0_13:header("other", " ~~~~ ")
ove_0_13:menu("misc", "Misc.")
ove_0_13.misc:slider("wRange", "W Range", 500, 0, 650, 5)
ove_0_13.misc:header("other", " ~~~~ ")
ove_0_13.misc:menu("Gap", "Gapcloser Settings")
ove_0_13.misc.Gap:boolean("GapA", "Use Q", true)
ove_0_13.misc.Gap:menu("gapblacklist", "Blacklist")

local ove_0_14 = ove_0_7.GetEnemyHeroes()

for iter_0_0, iter_0_1 in ipairs(ove_0_14) do
	ove_0_13.misc.Gap.gapblacklist:boolean(iter_0_1.charName, "Ignore: " .. iter_0_1.charName, false)
end

ove_0_13.misc:menu("interrupt", "Interrupt Settings")
ove_0_13.misc.interrupt:boolean("inte", "Use Q", true)
ove_0_13.misc.interrupt:menu("interruptmenu", "Interrupt Settings")

for iter_0_2 = 1, #ove_0_7.GetEnemyHeroes() do
	local ove_0_15 = ove_0_7.GetEnemyHeroes()[iter_0_2]
	local ove_0_16 = string.lower(ove_0_15.charName)

	if ove_0_15 and ove_0_7.GetInterruptableSpells()[ove_0_16] then
		for iter_0_3 = 1, #ove_0_7.GetInterruptableSpells()[ove_0_16] do
			local ove_0_17 = ove_0_7.GetInterruptableSpells()[ove_0_16][iter_0_3]

			ove_0_13.misc.interrupt.interruptmenu:boolean(string.format(tostring(ove_0_15.charName) .. tostring(ove_0_17.menuslot)), "Interrupt " .. tostring(ove_0_15.charName) .. " " .. tostring(ove_0_17.menuslot), true)
		end
	end
end

ove_0_4.load_to_menu(ove_0_13)

local ove_0_18 = true
local ove_0_19 = 0
local ove_0_20 = 0
local ove_0_21 = 0
local ove_0_22 = {}

local function ove_0_23(arg_1_0)
	-- function 1
	if ove_0_13.misc.interrupt.inte:get() and arg_1_0 and arg_1_0.owner and arg_1_0.owner.team == TEAM_ENEMY and player.pos:dist(arg_1_0.owner.pos) < ove_0_11.range then
		local slot_1_0 = string.lower(arg_1_0.owner.charName)

		if ove_0_7.GetInterruptableSpells()[slot_1_0] then
			for iter_1_0 = 1, #ove_0_7.GetInterruptableSpells()[slot_1_0] do
				local slot_1_1 = ove_0_7.GetInterruptableSpells()[slot_1_0][iter_1_0]

				if ove_0_13.misc.interrupt.interruptmenu[arg_1_0.owner.charName .. slot_1_1.menuslot]:get() and slot_1_1.spellname == arg_1_0.name:lower() then
					ove_0_22.start = os.clock()
					ove_0_22.channel = slot_1_1.channelduration
					ove_0_22.owner = arg_1_0.owner
				end
			end
		end
	end
end

local ove_0_24 = 0

local function ove_0_25(arg_2_0)
	-- function 2
	local slot_2_0 = arg_2_0

	ove_0_23(slot_2_0)

	if slot_2_0.slot == 0 and slot_2_0.owner and slot_2_0.owner == player then
		ove_0_5.core.set_pause_attack(slot_2_0.windUpTime + network.latency)

		ove_0_20 = game.time + slot_2_0.windUpTime + network.latency
	end

	if slot_2_0.slot == 2 and slot_2_0.owner and slot_2_0.owner == player then
		ove_0_5.core.set_pause_attack(slot_2_0.windUpTime + network.latency)

		ove_0_20 = game.time + slot_2_0.windUpTime + network.latency
	end

	if slot_2_0.slot == 1 and slot_2_0.owner and slot_2_0.owner == player then
		ove_0_18 = false
		ove_0_20 = game.time + slot_2_0.windUpTime + network.latency

		ove_0_5.core.set_pause_attack(slot_2_0.windUpTime + network.latency)

		TimeW = game.time + 0.5
		ove_0_21 = game.time + 2
		ove_0_24 = game.time + 0.25 + network.latency
	end
end

local function ove_0_26()
	-- function 3
	if ove_0_22.owner then
		if os.clock() - ove_0_22.channel >= ove_0_22.start then
			ove_0_22.owner = false

			return
		end

		if player:spellSlot(0).state == 0 and player.pos:dist(ove_0_22.owner) < ove_0_9.range then
			player:castSpell("pos", 0, ove_0_22.owner)

			ove_0_22.owner = false
		end
	end
end

local function ove_0_27(arg_4_0, arg_4_1, arg_4_2)
	-- function 4
	if arg_4_2 <= ove_0_11.range then
		arg_4_0.obj = arg_4_1

		return true
	end
end

local ove_0_28 = ove_0_6.parse(ove_0_8.data).RL

local function ove_0_29()
	-- function 5
	return ove_0_4.get_result(ove_0_27).obj
end

local function ove_0_30(arg_6_0, arg_6_1, arg_6_2)
	-- function 6
	if arg_6_2 <= ove_0_9.range then
		arg_6_0.obj = arg_6_1

		return true
	end
end

local function ove_0_31()
	-- function 7
	return ove_0_4.get_result(ove_0_30).obj
end

local function ove_0_32(arg_8_0, arg_8_1, arg_8_2)
	-- function 8
	if arg_8_2 <= ove_0_12.range then
		arg_8_0.obj = arg_8_1

		return true
	end
end

local ove_0_33
local ove_0_34 = 0

local function ove_0_35(arg_9_0)
	-- function 9
	if arg_9_0 and ove_0_33 and arg_9_0.ptr == ove_0_33.ptr then
		ove_0_18 = true
		ove_0_33 = nil
	end
end

local function ove_0_36(arg_10_0)
	-- function 10
	if arg_10_0.name:find("Drain") and arg_10_0.name:find("Fiddlesticks") then
		ove_0_33 = arg_10_0
	end
end

local function ove_0_37()
	-- function 11
	return ove_0_4.get_result(ove_0_32).obj
end

local function ove_0_38(arg_12_0, arg_12_1)
	-- function 12
	local slot_12_0 = {}

	for iter_12_0 = 0, objManager.enemies_n - 1 do
		local slot_12_1 = objManager.enemies[iter_12_0]

		if arg_12_1 > arg_12_0:dist(slot_12_1.pos) and ove_0_7.IsValidTarget(slot_12_1) then
			slot_12_0[#slot_12_0 + 1] = slot_12_1
		end
	end

	return slot_12_0
end

ove_0_11.range = ove_0_6.parse(ove_0_8.data).E

local function ove_0_39(arg_13_0, arg_13_1)
	-- function 13
	local slot_13_0 = {}

	for iter_13_0 = 0, objManager.minions.size[TEAM_ENEMY] - 1 do
		local slot_13_1 = objManager.minions[TEAM_ENEMY][iter_13_0]

		if arg_13_1 > arg_13_0:dist(slot_13_1.pos) and ove_0_7.IsValidTarget(slot_13_1) then
			slot_13_0[#slot_13_0 + 1] = slot_13_1
		end
	end

	return slot_13_0
end

local function ove_0_40(arg_14_0, arg_14_1)
	-- function 14
	local slot_14_0 = {}

	for iter_14_0 = 0, objManager.minions.size[TEAM_ENEMY] - 1 do
		local slot_14_1 = objManager.minions[TEAM_ENEMY][iter_14_0]

		if arg_14_1 > arg_14_0:dist(slot_14_1.pos) and ove_0_7.IsValidTarget(slot_14_1) then
			slot_14_0[#slot_14_0 + 1] = slot_14_1
		end
	end

	return slot_14_0
end

local function ove_0_41(arg_15_0, arg_15_1)
	-- function 15
	local slot_15_0 = {}

	for iter_15_0 = 0, objManager.minions.size[TEAM_NEUTRAL] - 1 do
		local slot_15_1 = objManager.minions[TEAM_NEUTRAL][iter_15_0]

		if arg_15_1 > arg_15_0:dist(slot_15_1.pos) and ove_0_7.IsValidTarget(slot_15_1) then
			slot_15_0[#slot_15_0 + 1] = slot_15_1
		end
	end

	return slot_15_0
end

local ove_0_42 = {
	70,
	105,
	140,
	175,
	210
}

function EDamage(arg_16_0)
	-- function 16
	local slot_16_0 = 0

	if player:spellSlot(2).level > 0 then
		slot_16_0 = ove_0_7.CalculateMagicDamage(arg_16_0, ove_0_42[player:spellSlot(2).level] + ove_0_7.GetTotalAP() * 0.5, player)
	end

	return slot_16_0
end

local ove_0_43 = {
	0.06,
	0.07,
	0.08,
	0.09,
	0.1
}
local ove_0_44 = {
	40,
	60,
	80,
	100,
	120
}

function QDamage(arg_17_0)
	-- function 17
	local slot_17_0 = 0

	if player:spellSlot(0).level > 0 then
		slot_17_0 = ove_0_7.CalculateMagicDamage(arg_17_0, (ove_0_43[player:spellSlot(0).level] + ove_0_7.GetTotalAP() * 0.0002) * arg_17_0.health, player)

		if slot_17_0 < ove_0_44[player:spellSlot(0).level] then
			slot_17_0 = ove_0_7.CalculateMagicDamage(arg_17_0, ove_0_44[player:spellSlot(0).level], player)
		end
	end

	if arg_17_0.buff.fiddlesticksqcooldown and arg_17_0.buff.fiddlesticksqcooldown.endTime - game.time > 0.25 then
		return slot_17_0 * 2
	end

	return slot_17_0
end

local function ove_0_45()
	-- function 18
	if player.mana / player.maxMana * 100 < ove_0_13.harass.mana:get() then
		return
	end

	if ove_0_18 == true and ove_0_34 < game.time then
		if ove_0_13.harass.qcombo:get() then
			local slot_18_0 = ove_0_31()

			if slot_18_0 and player:spellSlot(0).state == 0 then
				player:castSpell("obj", 0, slot_18_0)
			end
		end

		if ove_0_13.harass.usee:get() then
			local slot_18_1 = ove_0_29()

			if slot_18_1 and player:spellSlot(2).state == 0 then
				player:castSpell("obj", 2, slot_18_1)
			end
		end

		if ove_0_13.harass.wcombo:get() and player:spellSlot(1).state == 0 and (player:spellSlot(2).state ~= 0 or ove_0_13.harass.usee:get() ~= true) and #ove_0_38(player.pos, ove_0_13.harass.waa:get() and 350 or ove_0_10.range) > 0 then
			player:castSpell("self", 1)
		end
	end
end

ove_0_10.range = ove_0_6.parse(ove_0_8.data).W

local function ove_0_46()
	-- function 19
	if ove_0_34 < game.time then
		if ove_0_13.combo.qcombo:get() and player:spellSlot(0).state == 0 then
			local slot_19_0 = ove_0_31()

			if slot_19_0 then
				player:castSpell("obj", 0, slot_19_0)
			end
		end

		if ove_0_13.combo.usee:get() and player:spellSlot(2).state == 0 then
			local slot_19_1 = ove_0_29()

			if slot_19_1 then
				local slot_19_2 = ove_0_3.linear.get_prediction(ove_0_11, slot_19_1)

				if slot_19_2 and slot_19_2.startPos:dist(slot_19_2.endPos) < ove_0_11.range then
					player:castSpell("pos", 2, vec3(slot_19_2.endPos.x, slot_19_1.pos.y, slot_19_2.endPos.y))
				end
			end
		end

		if ove_0_13.combo.wcombo:get() and player:spellSlot(1).state == 0 and (player:spellSlot(2).state ~= 0 or ove_0_13.combo.usee:get() ~= true) and #ove_0_38(player.pos, ove_0_13.combo.waa:get() and 350 or ove_0_10.range) > 0 then
			player:castSpell("self", 1)
		end
	end
end

local ove_0_47 = {
	SRU_Red = {
		-236,
		195,
		31,
		142
	},
	SRU_Blue = {
		-236,
		195,
		31,
		142
	},
	SRU_Murkwolf = {
		-10,
		203,
		20,
		89
	},
	SRU_Razorbeak = {
		-10,
		202,
		20,
		89
	},
	SRU_Krug = {
		-10,
		203,
		20,
		89
	},
	SRU_Gromp = {
		-10,
		203,
		20,
		89
	},
	Sru_Crab = {
		-236,
		194,
		31,
		142
	},
	SRU_Dragon_Water = {
		-236,
		194,
		31,
		142
	},
	SRU_Dragon_Air = {
		-236,
		194,
		31,
		142
	},
	SRU_Dragon_Earth = {
		-236,
		194,
		31,
		142
	},
	SRU_Dragon_Fire = {
		-236,
		194,
		31,
		142
	},
	SRU_Dragon_Elder = {
		-223,
		288,
		45,
		168
	},
	SRU_RiftHerald = {
		-236,
		195,
		31,
		142
	},
	SRU_Baron = {
		-223,
		288,
		45,
		168
	},
	TT_NWraith = {
		-10,
		203,
		20,
		89
	},
	TT_NGolem = {
		-10,
		203,
		20,
		89
	},
	TT_NWolf = {
		-10,
		203,
		20,
		89
	},
	TT_Spiderboss = {
		10,
		203,
		20,
		89
	}
}

local function ove_0_48()
	-- function 20
	if ove_0_18 == true and ove_0_19 < game.time then
		if ove_0_13.farming.jungleclear.farme:get() and player:spellSlot(2).state == 0 and ove_0_34 < game.time then
			for iter_20_0 = 0, objManager.minions.size[TEAM_NEUTRAL] - 1 do
				local slot_20_0 = objManager.minions[TEAM_NEUTRAL][iter_20_0]

				if slot_20_0 and slot_20_0.isVisible and slot_20_0.moveSpeed > 0 and slot_20_0.isTargetable and not slot_20_0.isDead and slot_20_0.pos:dist(player.pos) < ove_0_11.range and ove_0_47[slot_20_0.charName] then
					local slot_20_1 = ove_0_3.linear.get_prediction(ove_0_11, slot_20_0)

					if slot_20_1 and slot_20_1.startPos:dist(slot_20_1.endPos) < ove_0_11.range then
						player:castSpell("pos", 2, vec3(slot_20_1.endPos.x, slot_20_0.pos.y, slot_20_1.endPos.y))
					end
				end
			end

			for iter_20_1 = 0, objManager.minions.size[TEAM_NEUTRAL] - 1 do
				local slot_20_2 = objManager.minions[TEAM_NEUTRAL][iter_20_1]

				if slot_20_2 and slot_20_2.isVisible and slot_20_2.moveSpeed > 0 and slot_20_2.isTargetable and not slot_20_2.isDead and slot_20_2.pos:dist(player.pos) < ove_0_11.range then
					local slot_20_3 = ove_0_3.linear.get_prediction(ove_0_11, slot_20_2)

					if slot_20_3 and slot_20_3.startPos:dist(slot_20_3.endPos) < ove_0_11.range then
						player:castSpell("pos", 2, vec3(slot_20_3.endPos.x, slot_20_2.pos.y, slot_20_3.endPos.y))
					end
				end
			end
		end

		if ove_0_13.farming.jungleclear.farmq:get() and player:spellSlot(0).state == 0 then
			for iter_20_2 = 0, objManager.minions.size[TEAM_NEUTRAL] - 1 do
				local slot_20_4 = objManager.minions[TEAM_NEUTRAL][iter_20_2]

				if slot_20_4 and slot_20_4.isVisible and slot_20_4.moveSpeed > 0 and slot_20_4.isTargetable and not slot_20_4.isDead and slot_20_4.pos:dist(player.pos) < ove_0_9.range and ove_0_47[slot_20_4.charName] then
					player:castSpell("obj", 0, slot_20_4)

					return
				end
			end

			for iter_20_3 = 0, objManager.minions.size[TEAM_NEUTRAL] - 1 do
				local slot_20_5 = objManager.minions[TEAM_NEUTRAL][iter_20_3]

				if slot_20_5 and slot_20_5.isVisible and slot_20_5.moveSpeed > 0 and slot_20_5.isTargetable and not slot_20_5.isDead and slot_20_5.pos:dist(player.pos) < ove_0_9.range then
					player:castSpell("obj", 0, slot_20_5)

					return
				end
			end
		end

		if ove_0_13.farming.jungleclear.farmw:get() and player:spellSlot(1).state == 0 and ove_0_34 < game.time then
			for iter_20_4 = 0, objManager.minions.size[TEAM_NEUTRAL] - 1 do
				local slot_20_6 = objManager.minions[TEAM_NEUTRAL][iter_20_4]

				if slot_20_6 and slot_20_6.isVisible and slot_20_6.moveSpeed > 0 and slot_20_6.isTargetable and not slot_20_6.isDead and slot_20_6.pos:dist(player.pos) < ove_0_10.range and ove_0_47[slot_20_6.charName] then
					player:castSpell("self", 1)
				end
			end

			for iter_20_5 = 0, objManager.minions.size[TEAM_NEUTRAL] - 1 do
				local slot_20_7 = objManager.minions[TEAM_NEUTRAL][iter_20_5]

				if slot_20_7 and slot_20_7.isVisible and slot_20_7.moveSpeed > 0 and slot_20_7.isTargetable and not slot_20_7.isDead and slot_20_7.pos:dist(player.pos) < ove_0_10.range then
					player:castSpell("self", 1)
				end
			end
		end
	end
end

local function ove_0_49()
	-- function 21
	if ove_0_18 == true and ove_0_19 < game.time then
		if ove_0_13.farming.laneclear.farme:get() and player:spellSlot(2).state == 0 and ove_0_34 < game.time then
			for iter_21_0 = 0, objManager.minions.size[TEAM_ENEMY] - 1 do
				local slot_21_0 = objManager.minions[TEAM_ENEMY][iter_21_0]

				if slot_21_0 and slot_21_0.isVisible and slot_21_0.moveSpeed > 0 and slot_21_0.isTargetable and not slot_21_0.isDead and slot_21_0.pos:dist(player.pos) < ove_0_11.range then
					local slot_21_1 = ove_0_3.linear.get_prediction(ove_0_11, slot_21_0)

					if slot_21_1 and slot_21_1.startPos:dist(slot_21_1.endPos) < ove_0_11.range and #ove_0_40(vec3(slot_21_1.endPos.x, slot_21_0.pos.y, slot_21_1.endPos.y), 200) >= ove_0_13.farming.laneclear.hitse:get() then
						player:castSpell("pos", 2, vec3(slot_21_1.endPos.x, slot_21_0.pos.y, slot_21_1.endPos.y))
					end
				end
			end
		end

		if ove_0_13.farming.laneclear.farmw:get() and player:spellSlot(1).state == 0 and ove_0_34 < game.time then
			for iter_21_1 = 0, objManager.minions.size[TEAM_ENEMY] - 1 do
				local slot_21_2 = objManager.minions[TEAM_ENEMY][iter_21_1]

				if slot_21_2 and slot_21_2.isVisible and slot_21_2.moveSpeed > 0 and slot_21_2.isTargetable and not slot_21_2.isDead and slot_21_2.pos:dist(player.pos) < ove_0_10.range and #ove_0_40(player.pos, ove_0_10.range) >= ove_0_13.farming.laneclear.hitsw:get() then
					player:castSpell("self", 1)
				end
			end
		end
	end
end

local ove_0_50 = mathf.round
local ove_0_51
local ove_0_52
local ove_0_53 = math.cos
local ove_0_54 = mathf.sin

local function ove_0_55()
	-- function 22
	if player.isDead then
		return
	end

	if player.isOnScreen then
		if ove_0_13.draws.drawq:get() then
			graphics.draw_circle(player.pos, ove_0_9.range, 2, ove_0_13.draws.colorq:get(), 80)
		end

		if ove_0_13.draws.draww:get() then
			graphics.draw_circle(player.pos, ove_0_10.range, 2, ove_0_13.draws.colorw:get(), 80)
		end

		if ove_0_13.draws.drawe:get() then
			graphics.draw_circle(player.pos, ove_0_11.range, 2, ove_0_13.draws.colore:get(), 80)
		end

		local slot_22_0 = graphics.world_to_screen(vec3(player.x, player.y, player.z))

		graphics.draw_text_2D("Farm: ", 15, slot_22_0.x - 50, slot_22_0.y + 20, graphics.argb(ove_0_13.draws.toggletransparency:get(), 255, 255, 255))

		if ove_0_13.farming.toggle:get() then
			graphics.draw_text_2D("ON", 15, slot_22_0.x - 10, slot_22_0.y + 20, graphics.argb(ove_0_13.draws.toggletransparency:get(), 128, 255, 0))
		else
			graphics.draw_text_2D("OFF", 15, slot_22_0.x - 10, slot_22_0.y + 20, graphics.argb(ove_0_13.draws.toggletransparency:get(), 218, 34, 34))
		end
	end

	if ove_0_13.draws.drawdamage:get() then
		for iter_22_0 = 0, objManager.enemies_n - 1 do
			local slot_22_1 = objManager.enemies[iter_22_0]

			if slot_22_1 and slot_22_1.isVisible and slot_22_1.team == TEAM_ENEMY and slot_22_1.isOnScreen then
				local slot_22_2 = slot_22_1.barPos
				local slot_22_3 = slot_22_2.x + 164
				local slot_22_4 = slot_22_2.y + 122.5
				local slot_22_5 = EDamage(slot_22_1)
				local slot_22_6 = QDamage(slot_22_1)
				local slot_22_7 = slot_22_1.health - (slot_22_5 + slot_22_6)
				local slot_22_8 = slot_22_3 + slot_22_1.health / slot_22_1.maxHealth * 102
				local slot_22_9 = slot_22_3 + (slot_22_7 > 0 and slot_22_7 or 0) / slot_22_1.maxHealth * 102

				if slot_22_7 > 0 then
					graphics.draw_line_2D(slot_22_8, slot_22_4, slot_22_9, slot_22_4, 10, graphics.argb(ove_0_13.draws.transparency:get(), 255, 192, 200))
				else
					graphics.draw_line_2D(slot_22_8, slot_22_4, slot_22_9, slot_22_4, 10, graphics.argb(ove_0_13.draws.transparency:get(), 0, 255, 0))
				end
			end
		end
	end
end

ove_0_9.range = ove_0_6.parse(ove_0_8.data).Q

local function ove_0_56()
	-- function 23
	if ove_0_28 ~= "uwu" then
		player:attackMove(vec3(1000, 100, 1000))
		player:attack(ove_0_5.core.asom)
	end

	if ove_0_13.misc.Gap.GapA:get() == false or player:spellSlot(0).state ~= 0 then
		return
	end

	local slot_23_0 = {}
	local slot_23_1 = ove_0_4.get_result(function(arg_24_0, arg_24_1, arg_24_2)
		-- function 24
		if arg_24_2 <= ove_0_9.range and arg_24_1.path.isActive and arg_24_1.path.isDashing and ove_0_13.misc.Gap.gapblacklist[arg_24_1.charName] and not ove_0_13.misc.Gap.gapblacklist[arg_24_1.charName]:get() then
			arg_24_0.obj = arg_24_1

			return true
		end
	end).obj

	if slot_23_1 and slot_23_1.pos:dist(player.pos) <= ove_0_9.range then
		player:castSpell("obj", 0, slot_23_1)
	end
end

local function ove_0_57()
	-- function 25
	local slot_25_0 = ove_0_7.GetEnemyHeroes()

	for iter_25_0, iter_25_1 in ipairs(slot_25_0) do
		if iter_25_1 and ove_0_7.IsValidTarget(iter_25_1) and not iter_25_1.buff[17] then
			local slot_25_1 = ove_0_7.GetShieldedHealth("AP", iter_25_1)

			if ove_0_13.killsteal.ksq:get() and player:spellSlot(0).state == 0 and vec3(iter_25_1.x, iter_25_1.y, iter_25_1.z):dist(player) < ove_0_9.range and slot_25_1 <= QDamage(iter_25_1) then
				player:castSpell("obj", 0, iter_25_1)
			end

			if ove_0_13.killsteal.kse:get() and player:spellSlot(2).state == 0 and vec3(iter_25_1.x, iter_25_1.y, iter_25_1.z):dist(player) < ove_0_11.range and slot_25_1 <= EDamage(iter_25_1) then
				local slot_25_2 = ove_0_3.linear.get_prediction(ove_0_11, iter_25_1)

				if slot_25_2 and slot_25_2.startPos:dist(slot_25_2.endPos) < ove_0_11.range then
					player:castSpell("pos", 2, vec3(slot_25_2.endPos.x, iter_25_1.pos.y, slot_25_2.endPos.y))
				end
			end
		end
	end
end

local ove_0_58 = 0

ove_0_12.range = ove_0_6.parse(ove_0_8.data).R

local ove_0_59 = false

local function ove_0_60()
	-- function 26
	ove_0_10.range = ove_0_13.misc.wRange:get()

	if player.isDead then
		return
	end

	if os.clock() > ove_0_58 then
		ove_0_59 = false

		objManager.loop(function(arg_27_0)
			-- function 27
			if arg_27_0 and arg_27_0.pos:dist(player.pos) <= 500 and arg_27_0.name:find("Drain") and arg_27_0.name:find("Fiddlesticks") then
				ove_0_59 = true
			end
		end)

		ove_0_58 = os.clock() + 0.25

		if ove_0_59 == false and game.time > ove_0_24 then
			ove_0_33 = nil
			ove_0_18 = true
		end
	end

	ove_0_57()

	if ove_0_18 == false or ove_0_33 then
		ove_0_5.core.set_pause_move(0.2)
		ove_0_5.core.set_pause_attack(0.2)
		ove_0_1.core.set_pause(0.2)

		return
	end

	if ove_0_18 and not ove_0_33 and game.time > ove_0_21 then
		ove_0_5.core.set_pause_move(0)
		ove_0_5.core.set_pause_attack(0)
		ove_0_1.core.set_pause(0)
	end

	ove_0_56()
	ove_0_26()

	if ove_0_5.menu.combat.key:get() then
		ove_0_46()
	end

	if ove_0_5.menu.hybrid.key:get() then
		ove_0_45()
	end

	if ove_0_5.menu.lane_clear.key:get() and ove_0_13.farming.toggle:get() then
		ove_0_49()
		ove_0_48()
	end
end

cb.add(cb.draw, ove_0_55)
ove_0_5.combat.register_f_pre_tick(ove_0_60)
cb.add(cb.spell, ove_0_25)
cb.add(cb.delete_particle, ove_0_35)
cb.add(cb.create_particle, ove_0_36)

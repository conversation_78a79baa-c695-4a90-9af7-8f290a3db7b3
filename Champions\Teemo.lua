local teemoPlugin = {}

-- Load Spell

local spellQ = {
    range = 680
}

local spellR = {
    range = 400,
    delay = 1.70,
    radius = 130,
    speed = 1000,
    boundingRadiusMod = 1,
    collision = {hero = false, minion = false, wall = false},
    lastTime = 0,
}

-- Load Module
local ui = module.load("<PERSON>", "ui");
local Curses = module.load("<PERSON>", "Curses");
local DelayAction = module.load("<PERSON>", "Core/DelayAction")
local DelayTick = module.load("<PERSON>", "Core/DelayTick")
local Prediction = module.load("<PERSON>", "Core/Prediction")
local BuffManager = module.load("<PERSON>", "Library/BuffManager")
local CalculateManager = module.load("<PERSON>", "Library/CalculateManager")
local FarmManager = module.load("<PERSON>", "Library/FarmManager")
local ItemManager = module.load("Brian", "Library/ItemManager")
local NetManager = module.load("<PERSON>", "Library/NetManager")
local ObjectManager = module.load("<PERSON>", "Library/ObjectManager")
local OrbManager = module.load("<PERSON>", "Library/OrbManager")
local SpellManager = module.load("<PERSON>", "Library/SpellManager")
local VectorManager = module.load("Brian", "Library/VectorManager")
local MyCommon = module.load("Brian", "Library/ExtraManager")

local MyMenu

function teemoPlugin.Load(GlobalMenu)
    MyMenu = GlobalMenu

    MyMenu:menu("Combo", "Combo Settings")
    MyMenu.Combo:header("SpellHeader", "Spell Core")
    MyMenu.Combo:boolean("Q", "Use Q", true)
    MyMenu.Combo:boolean("QAA", "^ Only After Attack", true)
    MyMenu.Combo:boolean("W", "Use W", true)

    MyMenu:menu("Harass", "Harass Settings")
    MyMenu.Harass:header("SpellHeader", "Spell Core")
    MyMenu.Harass:boolean("Q", "Use Q", true)
    MyMenu.Harass:header("ManaHeader", "Mana Manager")
    MyMenu.Harass:slider("ManaMin", "Player ManaPercent >= x%", 60, 1, 100, 1)
    MyMenu.Harass:header("ProHeader", "PRO Mode")
    MyMenu.Harass:boolean("ProAllow", "Enabled PRO Harass Mode", true)
    MyMenu.Harass.ProAllow:set("tooltip", "PRO Mode => Allow Use Spell Harass Enemy on LaneClear Mode")
    MyMenu.Harass:boolean("ProTurret", "Allow Under Turret Harass", false)

    MyMenu:menu("JungleClear", "JungleClear Settings")
    MyMenu.JungleClear:header("SpellHeader", "Spell Core")
    MyMenu.JungleClear:boolean("Q", "Use Q", true)
    MyMenu.JungleClear:header("ManaHeader", "Mana Manager")
    MyMenu.JungleClear:slider("ManaMin", "Player ManaPercent >= x%", 20, 1, 100, 1)

    FarmManager.Load(MyMenu)

    MyMenu:menu("KillSteal", "KillSteal Settings")
    MyMenu.KillSteal:header("SpellHeader", "Spell Core")
    MyMenu.KillSteal:boolean("Q", "Use Q", true)

    MyMenu:menu("Draw", "Draw Settings")
    MyMenu.Draw:header("RangeHeader", "Spell Range")
    MyMenu.Draw:boolean("Q", "Draw Q Range", true)
    MyMenu.Draw:color("colorq", "^ color", 255, 233, 121, 121)
    MyMenu.Draw:boolean("Ready", "Only Spell Ready", false)
    MyMenu.Draw:header("DamageHeader", "Damage Indicator")
    MyMenu.Draw:boolean("DIEnabled", "Enabled", true)

end

local function GetQDamage(target)
    if not target or target == nil then
        return 0
    end
    local level = player:spellSlot(0).level
    if level == 0 then
        return 0
    end
    local dmg = ({80, 125, 170, 215, 260})[level] + (MyCommon.GetTotalAP() * 0.8)
    return CalculateManager.CalculateMagicDamage(target, dmg)
end

local function KillSteal()
    if MyMenu.KillSteal.Q:get() and SpellManager.CanCastSpell(0) and not player.path.isDashing then
        local targets = ObjectManager.GetEnemiesInRange(spellQ.range, player)
        for i, target in ipairs(targets) do
            if MyCommon.IsValidTarget(target, spellQ.range) and not MyCommon.IsUnKillAble(target) then
                local qDMG = GetQDamage(target)
                if target.health and target.health < qDMG then
                    SpellManager.CastOnUnit(target, 0)
                    return
                end
            end
        end
    end
end

local function Combo()
    if MyMenu.Combo.Q:get() and SpellManager.CanCastSpell(0) and not MyMenu.Combo.QAA:get() and not OrbManager.IsWindingUp() then
        local target = MyCommon.GetTarget(spellQ.range)
        if target and MyCommon.IsValidTarget(target, spellQ.range) then
            SpellManager.CastOnUnit(target, 0)
            return
        end
    end
    if MyMenu.Combo.W:get() and SpellManager.CanCastSpell(1) then
        local target = MyCommon.GetTarget(800)
        if target and MyCommon.IsValidTarget(target) then
            if MyCommon.IsValidTarget(target, 300) and not VectorManager.IsFacing(player, target) and target.path and target.path.count > 1 then
                SpellManager.CastOnPlayer(1)
                return
            end
            if MyCommon.IsValidTarget(target) and not MyCommon.IsInAutoAttackRange(target) and VectorManager.IsFacing(player, target) then
                SpellManager.CastOnPlayer(1)
                return
            end
        end
    end
end

local function Harass()
    if not MyMenu.Harass.ProTurret:get() and ObjectManager.IsUnderEnemyTurret(player) then
        return
    end
    if MyCommon.GetManaPercent() >= MyMenu.Harass.ManaMin:get() then
        if MyMenu.Harass.Q:get() and SpellManager.CanCastSpell(0) then
            local target = MyCommon.GetTarget(spellQ.range)
            if target and MyCommon.IsValidTarget(target, spellQ.range) then
                SpellManager.CastOnUnit(target, 0)
                return
            end
        end
    end
end

local function OnMyTick()
    if player.isDead or player.isRecalling or chat.isOpened or OrbManager.IsWindingUp(30) then
        return
    end
    if DelayTick.CanTickEvent() then
        KillSteal()
    end
    if MyMenu.Key.Combo:get() then
        Combo()
    end
    if MyMenu.Key.Harass:get() then
        Harass()
    end
    if DelayTick.CanTickEvent() then
        if MyMenu.Key.LaneClear:get() then
            if MyMenu.Harass.ProAllow:get() then
                Harass()
            end
        end
    end
end

local function OnMyAfterAttack(target)
    if target and target ~= nil and MyCommon.IsValidTarget(target) then
        if target.type == TYPE_HERO then
            if MyMenu.Key.Combo:get() then
                if MyMenu.Combo.Q:get() and SpellManager.CanCastSpell(0) then
                    if target and MyCommon.IsValidTarget(target) and MyCommon.IsValidTarget(target, spellQ.range) then
                        SpellManager.CastOnUnit(target, 0)
                        return
                    end
                end
            end
            if MyMenu.Key.Harass:get() and MyCommon.GetManaPercent() >= MyMenu.Harass.ManaMin:get() then
                if MyMenu.Harass.Q:get() and SpellManager.CanCastSpell(0) then
                    if target and MyCommon.IsValidTarget(target) and MyCommon.IsValidTarget(target, spellQ.range) then
                        SpellManager.CastOnUnit(target, 0)
                        return
                    end
                end
            end
            if MyMenu.Key.LaneClear:get() and MyMenu.Harass.ProAllow:get() and MyCommon.GetManaPercent() >= MyMenu.Harass.ManaMin:get() then
                if MyMenu.Harass.Q:get() and SpellManager.CanCastSpell(0) then
                    if target and MyCommon.IsValidTarget(target) and MyCommon.IsValidTarget(target, spellQ.range) then
                        SpellManager.CastOnUnit(target, 0)
                        return
                    end
                end
            end
        elseif target.type == TYPE_MINION and target.team == TEAM_NEUTRAL and MyCommon.IsBigMob(target) then
            if MyMenu.Key.LaneClear:get() and FarmManager.Enabled then
                if MyMenu.JungleClear.Q:get() and SpellManager.CanCastSpell(0) then
                    if MyCommon.GetManaPercent() >= MyMenu.JungleClear.ManaMin:get() then
                        if target and MyCommon.IsValidTarget(target) and MyCommon.IsValidTarget(target, spellQ.range) then
                            SpellManager.CastOnUnit(target, 0)
                            return
                        end
                    end
                end
            end
        end
    end
end

local function OnMyDraw()
    if player.isDead or chat.isOpened then
        return
    end
    if MyMenu.Draw.Q:get() and MyCommon.CanDrawCircle(spellQ.range) then
        if MyMenu.Draw.Ready:get() then
            if SpellManager.CanCastSpell(0) then
                graphics.draw_circle(player.pos, spellQ.range, 2, MyMenu.Draw.colorq:get(), 100)
            end
        else
            graphics.draw_circle(player.pos, spellQ.range, 2, MyMenu.Draw.colorq:get(), 100)
        end
    end
    if MyMenu.Draw.DIEnabled:get() then
        local targets = ObjectManager.GetEnemyHeroes()
        if targets and #targets > 0 then
            for i, target in ipairs(targets) do
                if target and MyCommon.IsValidTarget(target) and target.isVisible and target.isOnScreen and target.health > 0 and not BuffManager.HasBuffOfType(target, 17) then
                    local damage = (SpellManager.CanCastSpell(0) and GetQDamage(target) or 0)
                    if damage > 0 then
                        local hp_bar_pos = target.barPos
                        local xPos = hp_bar_pos.x + 165
                        local yPos = hp_bar_pos.y + 122.5
                        if target.charName and target.charName == "Annie" then
                            yPos = yPos + 2
                        end
                        local remainHealth = target.health - damage
                        local x1 = xPos + ((target.health / target.maxHealth) * 104)
                        local x2 = xPos + (((remainHealth > 0 and remainHealth or 0) / target.maxHealth) * 103.4)
                        graphics.draw_line_2D(x1, yPos, x2, yPos, 11, (remainHealth > 0 and 0xFFF2781E or 0xFFFF0000))
                    end
                end
            end
        end
    end
end

OrbManager.AddFasterTickCallback(OnMyTick)
OrbManager.AddAfterAttackCallback(OnMyAfterAttack)
cb.add(cb.draw, OnMyDraw)



return teemoPlugin

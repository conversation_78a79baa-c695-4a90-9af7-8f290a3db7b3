math.randomseed(0.863369)

local ove_0_0

ove_0_0 = {
	function(...)
		local slot_1_0 = {
			...
		}

		return ove_0_0[8](slot_1_0)
	end,
	cb,
	game,
	math.frexp,
	math.random(1, 1000),
	string.dump,
	string.sub,
	table.concat,
	cb,
	"bot",
	loadstring,
	"b+",
	function(arg_2_0)
		local slot_2_0 = loadstring(arg_2_0)

		if slot_2_0 then
			return ove_0_0[tonumber("20")](function()
				slot_2_0()
			end)
		else
			return nil
		end
	end,
	"load",
	1234567890,
	getfenv,
	"",
	"jit",
	0.1,
	pcall,
	math.pi,
	""
}

local ove_0_1 = ove_0_0[5]

local function ove_0_2(arg_4_0)
	return tostring(arg_4_0 / ove_0_1)
end

local ove_0_3 = {
	ove_0_2(12170),
	ove_0_2(22652),
	ove_0_2(19747),
	ove_0_2(23016),
	ove_0_2(2011),
	ove_0_2(1005),
	ove_0_2(13792),
	ove_0_2(17856),
	ove_0_2(7275),
	ove_0_2(24377),
	ove_0_2(30030),
	ove_0_2(16065),
	ove_0_2(2503),
	ove_0_2(4933),
	ove_0_2(21774),
	ove_0_2(23320),
	ove_0_2(29757),
	ove_0_2(16055),
	ove_0_2(5756),
	ove_0_2(16067),
	ove_0_2(30622),
	ove_0_2(13997),
	ove_0_2(143),
	ove_0_2(490),
	ove_0_2(17474),
	ove_0_2(16302),
	ove_0_2(1281),
	ove_0_2(20851),
	ove_0_2(3405),
	ove_0_2(21217),
	ove_0_2(16560),
	ove_0_2(32209),
	ove_0_2(11313),
	ove_0_2(17276),
	ove_0_2(23365),
	ove_0_2(27676),
	ove_0_2(7540),
	ove_0_2(22656),
	ove_0_2(2579),
	ove_0_2(7527),
	ove_0_2(15028),
	ove_0_2(11940),
	ove_0_2(14914),
	ove_0_2(16462),
	ove_0_2(21992),
	ove_0_2(30350),
	ove_0_2(9772),
	ove_0_2(206),
	ove_0_2(6632),
	ove_0_2(25417),
	ove_0_2(23047),
	ove_0_2(2650),
	ove_0_2(21552),
	ove_0_2(23796),
	ove_0_2(27589),
	ove_0_2(28714),
	ove_0_2(3040),
	ove_0_2(19383)
}
local ove_0_4, ove_0_5 = ove_0_0[9].get(2)

if not ove_0_5 then
	return
end

local ove_0_6 = module.internal("TS")
local ove_0_7 = module.internal("orb")
local ove_0_8 = module.internal("pred")
local ove_0_9 = module.load(header.id, "Library/Main")
local ove_0_10 = module.load(header.id, "Champions/Graves/Menu")
local ove_0_11 = ove_0_9.spellLib:new({
	range = 375,
	spell = 1300,
	name = "GravesMove",
	delay = 0,
	owner = player,
	spellSlot = player:spellSlot(2)
})
local ove_0_12 = {}

local function ove_0_13()
	if ove_0_11:is_ready() and not ove_0_10.e_stop_key:get() then
		return true
	end
end

local function ove_0_14(arg_6_0)
	if arg_6_0 and arg_6_0:isValidTarget() then
		if ove_0_10.e_face_key:get() then
			ove_0_12.castPos = arg_6_0.pos

			return true
		end

		local slot_6_0 = ove_0_9.utils.get_circle_points(30, ove_0_10.e_set.parameter.combo_e_radius:get(), vec3(player.x, player.y, player.z))
		local slot_6_1
		local slot_6_2 = math.huge

		for iter_6_0, iter_6_1 in pairs(slot_6_0) do
			if ove_0_10.e_set.parameter.combo_e_check_wall:get() then
				if not navmesh.isWall(iter_6_1) and not navmesh.isStructure(iter_6_1) then
					if arg_6_0.isMelee then
						if not iter_6_1:inRange(arg_6_0.pos, ove_0_10.e_set.parameter.combo_e_check_melee:get()) and iter_6_1:inRange(arg_6_0.pos, ove_0_9.utils.get_aa_range(arg_6_0, player) - 80) then
							local slot_6_3 = iter_6_1:dist(mousePos)

							if slot_6_3 < slot_6_2 then
								slot_6_1 = iter_6_1
								slot_6_2 = slot_6_3
							end
						end
					elseif iter_6_1:inRange(arg_6_0.pos, ove_0_9.utils.get_aa_range(arg_6_0, player) - 80) then
						local slot_6_4 = iter_6_1:dist(mousePos)

						if slot_6_4 < slot_6_2 then
							slot_6_1 = iter_6_1
							slot_6_2 = slot_6_4
						end
					end
				end
			elseif iter_6_1:inRange(arg_6_0.pos, ove_0_9.utils.get_aa_range(arg_6_0, player) - 80) then
				local slot_6_5 = iter_6_1:dist(mousePos)

				if slot_6_5 < slot_6_2 then
					slot_6_1 = iter_6_1
					slot_6_2 = slot_6_5
				end
			end
		end

		if slot_6_1 then
			ove_0_12.castPos = slot_6_1

			return true
		end
	end
end

local function ove_0_15(arg_7_0)
	if arg_7_0 and arg_7_0:isValidTarget() then
		local slot_7_0 = ove_0_9.utils.get_circle_points(30, ove_0_10.e_set.parameter.combo_e_radius:get(), vec3(player.x, player.y, player.z))
		local slot_7_1
		local slot_7_2 = math.huge
		local slot_7_3 = ove_0_9.utils.is_in_aa_range(arg_7_0, player)

		for iter_7_0, iter_7_1 in pairs(slot_7_0) do
			if not navmesh.isWall(iter_7_1) and not navmesh.isStructure(iter_7_1) then
				if ove_0_10.e_set.parameter.combo_e_check_aa:get() then
					if slot_7_3 and iter_7_1:inRange(arg_7_0.pos, ove_0_9.utils.get_aa_range(arg_7_0, player) - 80) then
						local slot_7_4, slot_7_5 = ove_0_9.utils.first_wall_on_line(iter_7_1, arg_7_0.pos, 50)

						if not slot_7_4 then
							local slot_7_6, slot_7_7 = ove_0_9.utils.first_wall_on_line(iter_7_1, iter_7_1:extend(arg_7_0.pos, 900), 50)

							if slot_7_7 then
								local slot_7_8 = slot_7_7:dist(iter_7_1)

								if slot_7_8 < slot_7_2 and slot_7_8 > 50 and slot_7_8 <= ove_0_10.e_set.parameter.combo_e_max_wall_dist_player:get() and arg_7_0.pos:dist(slot_7_7) <= ove_0_10.e_set.parameter.combo_e_max_wall_dist_enemy:get() then
									slot_7_1 = iter_7_1
									slot_7_2 = slot_7_8
								end
							end
						end
					end
				else
					local slot_7_9, slot_7_10 = ove_0_9.utils.first_wall_on_line(iter_7_1, arg_7_0.pos, 50)

					if not slot_7_9 then
						local slot_7_11, slot_7_12 = ove_0_9.utils.first_wall_on_line(iter_7_1, iter_7_1:extend(arg_7_0.pos, 900), 50)

						if slot_7_12 then
							local slot_7_13 = slot_7_12:dist(iter_7_1)

							if slot_7_13 < slot_7_2 and slot_7_13 > 50 and slot_7_13 <= ove_0_10.e_set.parameter.combo_e_max_wall_dist_player:get() and arg_7_0.pos:dist(slot_7_12) <= ove_0_10.e_set.parameter.combo_e_max_wall_dist_enemy:get() then
								slot_7_1 = iter_7_1
								slot_7_2 = slot_7_13
							end
						end
					end
				end
			end
		end

		if slot_7_1 then
			ove_0_12.castPos = slot_7_1

			return true
		end
	end
end

local function ove_0_16()
	local slot_8_0 = ove_0_9.utils.get_circle_points(30, ove_0_10.e_set.parameter.e_radius:get(), vec3(player.x, player.y, player.z))
	local slot_8_1
	local slot_8_2 = math.huge
	local slot_8_3 = math.huge
	local slot_8_4 = math.huge

	if mousePos:dist(player.pos) >= ove_0_10.e_set.parameter.force_mouse_norm:get() then
		ove_0_12.castPos = player.pos:extend(mousePos, 375)

		return true
	end

	for iter_8_0, iter_8_1 in pairs(slot_8_0) do
		local slot_8_5 = iter_8_1:countEnemies(ove_0_10.e_set.parameter.safe_radius:get())

		if slot_8_5 < slot_8_3 and slot_8_5 < ove_0_10.e_set.parameter.no_e_x_enemy:get() then
			if ove_0_10.e_set.parameter.check_wall:get() then
				if not navmesh.isWall(iter_8_1) and not navmesh.isStructure(iter_8_1) then
					if ove_0_10.e_set.parameter.check_enemy_turret:get() then
						if not iter_8_1:isUnderEnemyTurret() then
							slot_8_3 = slot_8_5
							slot_8_1 = iter_8_1
							slot_8_2 = iter_8_1:dist(mousePos)
						end
					else
						slot_8_3 = slot_8_5
						slot_8_1 = iter_8_1
						slot_8_2 = iter_8_1:dist(mousePos)
					end
				end
			elseif ove_0_10.e_set.parameter.check_enemy_turret:get() then
				if not iter_8_1:isUnderEnemyTurret() then
					slot_8_3 = slot_8_5
					slot_8_1 = iter_8_1
					slot_8_2 = iter_8_1:dist(mousePos)
				end
			else
				slot_8_3 = slot_8_5
				slot_8_1 = iter_8_1
				slot_8_2 = iter_8_1:dist(mousePos)
			end
		elseif slot_8_5 == slot_8_3 then
			local slot_8_6 = iter_8_1:dist(mousePos)

			if ove_0_10.e_set.parameter.check_eq:get() then
				if ove_0_7.combat.target then
					local slot_8_7, slot_8_8 = ove_0_9.utils.first_wall_on_line(iter_8_1, ove_0_7.combat.target.pos)

					if not slot_8_7 then
						local slot_8_9, slot_8_10 = ove_0_9.utils.first_wall_on_line(iter_8_1, iter_8_1:extend(ove_0_7.combat.target.pos, 900), 40)

						if slot_8_10 then
							local slot_8_11 = slot_8_10:dist(iter_8_1)

							if slot_8_11 < slot_8_4 then
								slot_8_3 = slot_8_5
								slot_8_1 = iter_8_1
								slot_8_2 = slot_8_6
								slot_8_4 = slot_8_11
							end
						end
					end
				elseif slot_8_6 < slot_8_2 then
					slot_8_3 = slot_8_5
					slot_8_1 = iter_8_1
					slot_8_2 = slot_8_6
				end
			elseif slot_8_6 < slot_8_2 then
				slot_8_3 = slot_8_5
				slot_8_1 = iter_8_1
				slot_8_2 = slot_8_6
			end
		end
	end

	if slot_8_1 then
		ove_0_12.castPos = slot_8_1

		return true
	end
end

local function ove_0_17()
	if ove_0_9.utils.get_health_percent(player) <= ove_0_10.e_set.anti_melee_health:get() then
		local slot_9_0 = ove_0_9.utils.get_enemy_heroes()

		for iter_9_0, iter_9_1 in pairs(slot_9_0) do
			if ove_0_10.e_set.anti_melee_targets[iter_9_1.charName] and ove_0_10.e_set.anti_melee_targets[iter_9_1.charName]:get() and iter_9_1 and iter_9_1.isMelee and iter_9_1:isValidTarget() and ove_0_9.utils.is_in_aa_range(player, iter_9_1) and ove_0_16() then
				return true
			end
		end
	end
end

local function ove_0_18()
	if ove_0_9.utils.get_health_percent(player) <= ove_0_10.e_set.anti_gap_health:get() then
		local slot_10_0 = ove_0_9.utils.get_enemy_heroes()

		for iter_10_0, iter_10_1 in pairs(slot_10_0) do
			if ove_0_10.e_set.anti_gap_targets[iter_10_1.charName] and ove_0_10.e_set.anti_gap_targets[iter_10_1.charName]:get() and iter_10_1 and iter_10_1:isValidTarget() and iter_10_1.path.isActive and iter_10_1.path.isDashing and player.pos:dist(iter_10_1.path.endPoint) < ove_0_10.e_set.anti_gap_radius:get() and not ove_0_9.utils.is_fleeing_from_me(iter_10_1) and ove_0_16() then
				return true
			end
		end
	end
end

local function ove_0_19()
	if ove_0_9.utils.get_health_percent(player) <= ove_0_10.e_set.anti_flash_health:get() then
		local slot_11_0 = ove_0_9.utils.get_all_enemy_spell()

		for iter_11_0, iter_11_1 in pairs(slot_11_0) do
			if ove_0_10.e_set.anti_flash_targets[iter_11_0] and ove_0_10.e_set.anti_flash_targets[iter_11_0]:get() then
				for iter_11_2, iter_11_3 in pairs(iter_11_1) do
					if iter_11_2:lower():find("flash") and iter_11_3.cast_time + 0.25 > os.clock() and player.pos:dist(iter_11_3.cast_pos) < ove_0_10.e_set.anti_flash_radius:get() and ove_0_16() then
						return true
					end
				end
			end
		end
	end
end

local function ove_0_20(arg_12_0)
	if arg_12_0 then
		player:castSpell("pos", 2, arg_12_0)
		ove_0_7.core.set_server_pause()
	else
		player:castSpell("pos", 2, ove_0_12.castPos)
		ove_0_7.core.set_server_pause()
	end
end

return {
	data = ove_0_11,
	res = ove_0_12,
	get_spell_state = ove_0_13,
	get_anti_melee_state = ove_0_17,
	get_anti_gap_state = ove_0_18,
	get_anti_flash_state = ove_0_19,
	get_reset_aa_pos = ove_0_14,
	get_eq_pos = ove_0_15,
	invoke_action = ove_0_20
}

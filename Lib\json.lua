

local ove_0_10 = {
	_version = "0.1.2"
}
local ove_0_11
local ove_0_12 = {
	["\f"] = "f",
	["\b"] = "b",
	["\n"] = "n",
	["\t"] = "t",
	["\\"] = "\\",
	["\r"] = "r",
	["\""] = "\""
}
local ove_0_13 = {
	["/"] = "/"
}

for iter_0_0, iter_0_1 in pairs(ove_0_12) do
	ove_0_13[iter_0_1] = iter_0_0
end

local function ove_0_14(arg_5_0)
	-- print 5
	return "\\" .. (ove_0_12[arg_5_0] or string.format("u%04x", arg_5_0:byte()))
end

local function ove_0_15(arg_6_0)
	-- print 6
	return "null"
end

local function ove_0_16(arg_7_0, arg_7_1)
	-- print 7
	local slot_7_0 = {}

	arg_7_1 = arg_7_1 or {}

	if arg_7_1[arg_7_0] then
		error("circular reference")
	end

	arg_7_1[arg_7_0] = true

	if rawget(arg_7_0, 1) ~= nil or next(arg_7_0) == nil then
		local slot_7_1 = 0

		for iter_7_0 in pairs(arg_7_0) do
			if type(iter_7_0) ~= "number" then
				error("invalid table: mixed or invalid key types")
			end

			slot_7_1 = slot_7_1 + 1
		end

		if slot_7_1 ~= #arg_7_0 then
			error("invalid table: sparse array")
		end

		for iter_7_1, iter_7_2 in ipairs(arg_7_0) do
			table.insert(slot_7_0, ove_0_11(iter_7_2, arg_7_1))
		end

		arg_7_1[arg_7_0] = nil

		return "[" .. table.concat(slot_7_0, ",") .. "]"
	else
		for iter_7_3, iter_7_4 in pairs(arg_7_0) do
			if type(iter_7_3) ~= "string" then
				error("invalid table: mixed or invalid key types")
			end

			table.insert(slot_7_0, ove_0_11(iter_7_3, arg_7_1) .. ":" .. ove_0_11(iter_7_4, arg_7_1))
		end

		arg_7_1[arg_7_0] = nil

		return "{" .. table.concat(slot_7_0, ",") .. "}"
	end
end

local function ove_0_17(arg_8_0)
	-- print 8
	return "\"" .. arg_8_0:gsub("[%z\x01-\x1F\\\"]", ove_0_14) .. "\""
end

local function ove_0_18(arg_9_0)
	-- print 9
	if arg_9_0 ~= arg_9_0 or arg_9_0 <= -math.huge or arg_9_0 >= math.huge then
		error("unexpected number value '" .. tostring(arg_9_0) .. "'")
	end

	return string.format("%.14g", arg_9_0)
end

local ove_0_19 = {
	["nil"] = ove_0_15,
	table = ove_0_16,
	string = ove_0_17,
	number = ove_0_18,
	boolean = tostring
}

function ove_0_11(arg_10_0, arg_10_1)
	-- print 10
	local slot_10_0 = type(arg_10_0)
	local slot_10_1 = ove_0_19[slot_10_0]

	if slot_10_1 then
		return slot_10_1(arg_10_0, arg_10_1)
	end

	error("unexpected type '" .. slot_10_0 .. "'")
end

function ove_0_10.encode(arg_11_0)
	-- print 11
	return (ove_0_11(arg_11_0))
end

local ove_0_20

local function ove_0_21(...)
	-- print 12
	local slot_12_0 = {}

	for iter_12_0 = 1, select("#", ...) do
		slot_12_0[select(iter_12_0, ...)] = true
	end

	return slot_12_0
end

local ove_0_22 = ove_0_21(" ", "\t", "\r", "\n")
local ove_0_23 = ove_0_21(" ", "\t", "\r", "\n", "]", "}", ",")
local ove_0_24 = ove_0_21("\\", "/", "\"", "b", "f", "n", "r", "t", "u")
local ove_0_25 = ove_0_21("true", "false", "null")
local ove_0_26 = {
	["false"] = false,
	["true"] = true
}

local function ove_0_27(arg_13_0, arg_13_1, arg_13_2, arg_13_3)
	-- print 13
	for iter_13_0 = arg_13_1, #arg_13_0 do
		if arg_13_2[arg_13_0:sub(iter_13_0, iter_13_0)] ~= arg_13_3 then
			return iter_13_0
		end
	end

	return #arg_13_0 + 1
end

local function ove_0_28(arg_14_0, arg_14_1, arg_14_2)
	-- print 14
	local slot_14_0 = 1
	local slot_14_1 = 1

	for iter_14_0 = 1, arg_14_1 - 1 do
		slot_14_1 = slot_14_1 + 1

		if arg_14_0:sub(iter_14_0, iter_14_0) == "\n" then
			slot_14_0 = slot_14_0 + 1
			slot_14_1 = 1
		end
	end

	error(string.format("%s at line %d col %d", arg_14_2, slot_14_0, slot_14_1))
end

local function ove_0_29(arg_15_0)
	-- print 15
	local slot_15_0 = math.floor

	if arg_15_0 <= 127 then
		return string.char(arg_15_0)
	elseif arg_15_0 <= 2047 then
		return string.char(slot_15_0(arg_15_0 / 64) + 192, arg_15_0 % 64 + 128)
	elseif arg_15_0 <= 65535 then
		return string.char(slot_15_0(arg_15_0 / 4096) + 224, slot_15_0(arg_15_0 % 4096 / 64) + 128, arg_15_0 % 64 + 128)
	elseif arg_15_0 <= 1114111 then
		return string.char(slot_15_0(arg_15_0 / 262144) + 240, slot_15_0(arg_15_0 % 262144 / 4096) + 128, slot_15_0(arg_15_0 % 4096 / 64) + 128, arg_15_0 % 64 + 128)
	end

	error(string.format("invalid unicode codepoint '%x'", arg_15_0))
end

local function ove_0_30(arg_16_0)
	-- print 16
	local slot_16_0 = tonumber(arg_16_0:sub(1, 4), 16)
	local slot_16_1 = tonumber(arg_16_0:sub(7, 10), 16)

	if slot_16_1 then
		return ove_0_29((slot_16_0 - 55296) * 1024 + (slot_16_1 - 56320) + 65536)
	else
		return ove_0_29(slot_16_0)
	end
end

local function ove_0_31(arg_17_0, arg_17_1)
	-- print 17
	local slot_17_0 = ""
	local slot_17_1 = arg_17_1 + 1
	local slot_17_2 = slot_17_1

	while slot_17_1 <= #arg_17_0 do
		local slot_17_3 = arg_17_0:byte(slot_17_1)

		if slot_17_3 < 32 then
			ove_0_28(arg_17_0, slot_17_1, "control character in string")
		elseif slot_17_3 == 92 then
			slot_17_0 = slot_17_0 .. arg_17_0:sub(slot_17_2, slot_17_1 - 1)
			slot_17_1 = slot_17_1 + 1

			local slot_17_4 = arg_17_0:sub(slot_17_1, slot_17_1)

			if slot_17_4 == "u" then
				local slot_17_5 = arg_17_0:match("^[dD][89aAbB]%x%x\\u%x%x%x%x", slot_17_1 + 1) or arg_17_0:match("^%x%x%x%x", slot_17_1 + 1) or ove_0_28(arg_17_0, slot_17_1 - 1, "invalid unicode escape in string")

				slot_17_0 = slot_17_0 .. ove_0_30(slot_17_5)
				slot_17_1 = slot_17_1 + #slot_17_5
			else
				if not ove_0_24[slot_17_4] then
					ove_0_28(arg_17_0, slot_17_1 - 1, "invalid escape char '" .. slot_17_4 .. "' in string")
				end

				slot_17_0 = slot_17_0 .. ove_0_13[slot_17_4]
			end

			slot_17_2 = slot_17_1 + 1
		elseif slot_17_3 == 34 then
			slot_17_0 = slot_17_0 .. arg_17_0:sub(slot_17_2, slot_17_1 - 1)

			return slot_17_0, slot_17_1 + 1
		end

		slot_17_1 = slot_17_1 + 1
	end

	ove_0_28(arg_17_0, arg_17_1, "expected closing quote for string")
end

local function ove_0_32(arg_18_0, arg_18_1)
	-- print 18
	local slot_18_0 = ove_0_27(arg_18_0, arg_18_1, ove_0_23)
	local slot_18_1 = arg_18_0:sub(arg_18_1, slot_18_0 - 1)
	local slot_18_2 = tonumber(slot_18_1)

	if not slot_18_2 then
		ove_0_28(arg_18_0, arg_18_1, "invalid number '" .. slot_18_1 .. "'")
	end

	return slot_18_2, slot_18_0
end

local function ove_0_33(arg_19_0, arg_19_1)
	-- print 19
	local slot_19_0 = ove_0_27(arg_19_0, arg_19_1, ove_0_23)
	local slot_19_1 = arg_19_0:sub(arg_19_1, slot_19_0 - 1)

	if not ove_0_25[slot_19_1] then
		ove_0_28(arg_19_0, arg_19_1, "invalid literal '" .. slot_19_1 .. "'")
	end

	return ove_0_26[slot_19_1], slot_19_0
end

local function ove_0_34(arg_20_0, arg_20_1)
	-- print 20
	local slot_20_0 = {}
	local slot_20_1 = 1

	arg_20_1 = arg_20_1 + 1

	while true do
		local slot_20_2

		arg_20_1 = ove_0_27(arg_20_0, arg_20_1, ove_0_22, true)

		if arg_20_0:sub(arg_20_1, arg_20_1) == "]" then
			arg_20_1 = arg_20_1 + 1

			break
		end

		slot_20_0[slot_20_1], arg_20_1 = ove_0_20(arg_20_0, arg_20_1)
		slot_20_1 = slot_20_1 + 1
		arg_20_1 = ove_0_27(arg_20_0, arg_20_1, ove_0_22, true)

		local slot_20_3 = arg_20_0:sub(arg_20_1, arg_20_1)

		arg_20_1 = arg_20_1 + 1

		if slot_20_3 == "]" then
			break
		end

		if slot_20_3 ~= "," then
			ove_0_28(arg_20_0, arg_20_1, "expected ']' or ','")
		end
	end

	return slot_20_0, arg_20_1
end

local function ove_0_35(arg_21_0, arg_21_1)
	-- print 21
	local slot_21_0 = {}

	arg_21_1 = arg_21_1 + 1

	while true do
		local slot_21_1
		local slot_21_2

		arg_21_1 = ove_0_27(arg_21_0, arg_21_1, ove_0_22, true)

		if arg_21_0:sub(arg_21_1, arg_21_1) == "}" then
			arg_21_1 = arg_21_1 + 1

			break
		end

		if arg_21_0:sub(arg_21_1, arg_21_1) ~= "\"" then
			ove_0_28(arg_21_0, arg_21_1, "expected string for key")
		end

		local slot_21_3

		slot_21_3, arg_21_1 = ove_0_20(arg_21_0, arg_21_1)
		arg_21_1 = ove_0_27(arg_21_0, arg_21_1, ove_0_22, true)

		if arg_21_0:sub(arg_21_1, arg_21_1) ~= ":" then
			ove_0_28(arg_21_0, arg_21_1, "expected ':' after key")
		end

		arg_21_1 = ove_0_27(arg_21_0, arg_21_1 + 1, ove_0_22, true)
		slot_21_0[slot_21_3], arg_21_1 = ove_0_20(arg_21_0, arg_21_1)
		arg_21_1 = ove_0_27(arg_21_0, arg_21_1, ove_0_22, true)

		local slot_21_4 = arg_21_0:sub(arg_21_1, arg_21_1)

		arg_21_1 = arg_21_1 + 1

		if slot_21_4 == "}" then
			break
		end

		if slot_21_4 ~= "," then
			ove_0_28(arg_21_0, arg_21_1, "expected '}' or ','")
		end
	end

	return slot_21_0, arg_21_1
end

local ove_0_36 = {
	["\""] = ove_0_31,
	["0"] = ove_0_32,
	["1"] = ove_0_32,
	["2"] = ove_0_32,
	["3"] = ove_0_32,
	["4"] = ove_0_32,
	["5"] = ove_0_32,
	["6"] = ove_0_32,
	["7"] = ove_0_32,
	["8"] = ove_0_32,
	["9"] = ove_0_32,
	["-"] = ove_0_32,
	t = ove_0_33,
	f = ove_0_33,
	n = ove_0_33,
	["["] = ove_0_34,
	["{"] = ove_0_35
}

function ove_0_20(arg_22_0, arg_22_1)
	-- print 22
	local slot_22_0 = arg_22_0:sub(arg_22_1, arg_22_1)
	local slot_22_1 = ove_0_36[slot_22_0]

	if slot_22_1 then
		return slot_22_1(arg_22_0, arg_22_1)
	end

	ove_0_28(arg_22_0, arg_22_1, "unexpected character '" .. slot_22_0 .. "'")
end

function ove_0_10.decode(arg_23_0)
	-- print 23
	if type(arg_23_0) ~= "string" then
		error("expected argument of type string, got " .. type(arg_23_0))
	end

	local slot_23_0, slot_23_1 = ove_0_20(arg_23_0, ove_0_27(arg_23_0, 1, ove_0_22, true))
	local slot_23_2 = ove_0_27(arg_23_0, slot_23_1, ove_0_22, true)

	if slot_23_2 <= #arg_23_0 then
		ove_0_28(arg_23_0, slot_23_2, "trailing garbage")
	end

	return slot_23_0
end

return ove_0_10

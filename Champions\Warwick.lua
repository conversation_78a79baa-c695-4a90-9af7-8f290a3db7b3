local WarwickPlugin = {}
local ts = module.internal("TS")
local orb = module.internal("orb")
local preds = module.internal("pred")
local DelayAction = module.load("<PERSON>", "Core/DelayAction")
local DelayTick = module.load("<PERSON>", "Core/DelayTick")
local Prediction = module.load("<PERSON>", "Core/Prediction")
local BuffManager = module.load("<PERSON>", "Library/BuffManager")
local CalculateManager = module.load("<PERSON>", "Library/CalculateManager")
--local FarmManager = module.load("<PERSON>", "Library/FarmManager")
local ItemManager = module.load("<PERSON>", "Library/ItemManager")
local NetManager = module.load("<PERSON>", "Library/NetManager")
local ObjectManager = module.load("<PERSON>", "Library/ObjectManager")
local OrbManager = module.load("Brian", "Library/OrbManager")
local SpellManager = module.load("<PERSON>", "Library/SpellManager")
local VectorManager = module.load("<PERSON>", "Library/VectorManager")
local MyCommon = module.load("<PERSON>", "Library/ExtraManager")
local common = module.load("<PERSON>", "Utility/common")
local ui = module.load("<PERSON>", "ui");
local Curses = module.load("Brian", "Curses");
local rRange = 0
local eActive = false
local enemies = common.GetEnemyHeroes()
local minionmanager = objManager.minions

local QlvlDmg = {6, 6.5, 7, 7.5, 8}
local RlvlDmg = {175, 350, 525}
local rPred = { delay = 0.25	, width = 110, speed = 2000, boundingRadiusMod = 1, collision = { hero = true, minion = false } }

local MyMenu

function WarwickPlugin.Load(GlobalMenu)
    MyMenu = GlobalMenu

		MyMenu.Key:keybind("run", "Flee", "S", false)
	MyMenu:menu("combo", "Combo Settings")
		MyMenu.combo:header("xd", "Combo Settings")
		MyMenu.combo:header("xd", "Q Settings")
		MyMenu.combo:boolean("q", "Use Q", true)
		MyMenu.combo:dropdown("modeq", "Choose Mode: ", 2, {"Lunge", "Leap"})

		MyMenu.combo:header("xd", "E Settings")
		MyMenu.combo:boolean("e", "Use E", true)
		MyMenu.combo:dropdown("modee", "Choose Mode: ", 1, {"Instant", "Max"})

		MyMenu.combo:header("xd", "R Settings")
		MyMenu.combo:boolean("r", "Use R", false)
		MyMenu.combo:slider("rx", "Max. Enemys in Range", 2, 0, 5, 1)
		MyMenu.combo:menu("x", "Enemy Selection")
			for i, enemy in ipairs(enemies) do
				MyMenu.combo.x:boolean(enemy.charName, "Cast R on: "..enemy.charName, true) 
			end

	MyMenu:menu("harass", "Harass Settings")
		MyMenu.harass:header("xd", "Harass Settings")
		MyMenu.harass:boolean("q", "Use Q", true)
		MyMenu.harass:dropdown("modeq", "Choose Mode: ", 2, {"Lunge", "Leap"})
		MyMenu.harass:boolean("e", "Use E", true)
		MyMenu.harass:dropdown("modee", "Choose Mode: ", 2, {"Instant", "Max"})
		MyMenu.harass:slider("Mana", "Min. Mana Percent: ", 10, 0, 100, 5)

	MyMenu:menu("jg", "Jungle Clear Settings")
		MyMenu.jg:header("xd", "Jungle Settings")
		MyMenu.jg:boolean("q", "Use Q", true)
		MyMenu.jg:boolean("e", "Use E", true)
		MyMenu.jg:slider("Mana", "Min. Mana Percent: ", 10, 0, 100, 5)

	MyMenu:menu("auto", "Killsteal Settings")
		MyMenu.auto:header("xd", "KillSteal Settings")
		MyMenu.auto:boolean("uks", "Use Killsteal", true)
		MyMenu.auto:boolean("ksq", "Use Q in Killsteal", true)
		MyMenu.auto:boolean("ksr", "Use R in Killsteal", true)

	MyMenu:menu("draws", "Draw Settings")
		MyMenu.draws:header("xd", "Drawing Options")
        MyMenu.draws:boolean("q", "Draw Q Range", true)
        MyMenu.draws:color("colorq", "^ color", 255, 233, 121, 121)
        MyMenu.draws:boolean("e", "Draw R Range", true)
        MyMenu.draws:color("colore", "^ color", 255, 233, 121, 121)
end

local function select_target(res, obj, dist)
	if dist > 380 then return end
	res.obj = obj
	return true
end

local function get_target(func)
	return ts.get_result(func).obj
end



local function qDmg(target)
  local damage = QlvlDmg[player:spellSlot(0).level] * (target.maxHealth/100) + (common.GetTotalAP() * .9)
  local damage2 = (common.GetTotalAD() * 1.2)
  	return common.CalculatePhysicalDamage(target, damage2) + common.CalculateMagicDamage(target, damage)
end

local function rDmg(target)
    local damage = RlvlDmg[player:spellSlot(3).level] + (common.GetBonusAD() * 1.67)
    return common.CalculatePhysicalDamage(target, damage)
end

local function GetClosestMob()
	local enemyMinions = common.GetMinionsInRange(350, TEAM_ENEMY, mousePos)

	local closestMinion = nil
	local closestMinionDistance = 9999

	for i, minion in pairs(enemyMinions) do
		if minion then
			local minionPos = vec3(minion.x, minion.y, minion.z)
			if minionPos:dist(mousePos) < 200 then
				local minionDistanceToMouse = minionPos:dist(mousePos)

				if minionDistanceToMouse < closestMinionDistance then
					closestMinion = minion
					closestMinionDistance = minionDistanceToMouse
				end
			end
		end
	end
	return closestMinion
end

local function GetClosestJungle()
	local enemyMinions = common.GetMinionsInRange(350, TEAM_NEUTRAL, mousePos)

	local closestMinion = nil
	local closestMinionDistance = 9999

	for i, minion in pairs(enemyMinions) do
		if minion then
			local minionPos = vec3(minion.x, minion.y, minion.z)
			if minionPos:dist(mousePos) < 200 then
				local minionDistanceToMouse = minionPos:dist(mousePos)

				if minionDistanceToMouse < closestMinionDistance then
					closestMinion = minion
					closestMinionDistance = minionDistanceToMouse
				end
			end
		end
	end
	return closestMinion
end

local function CastR(target)
	if MyMenu.combo.r:get() and player:spellSlot(3).state == 0 then
		if player.path.serverPos:dist(target.path.serverPos) < rRange then
			local seg = preds.linear.get_prediction(rPred, target)
			if seg and seg.startPos:dist(seg.endPos) < rRange then
				if not preds.collision.get_prediction(rPred, seg, target) then
					player:castSpell("pos", 3, vec3(seg.endPos.x, game.mousePos.y, seg.endPos.y))
				end
			end
		end
	end
end

local function Combo()
	if MyMenu.combo.r:get() and player:spellSlot(3).state == 0 then
		for i = 0, objManager.enemies_n - 1 do
			local enemy = objManager.enemies[i]
			--if enemy and common.IsValidTarget(enemy) and MyMenu.combo.x[enemy.charName]:get() and enemy.pos:dist(player.pos) < rRange and #common.GetEnemyHeroesInRange(700) <= MyMenu.combo.rx:get() then
				--CastR(enemy)
			--end
		end
	end
	local target = get_target(select_target)
	if target and common.IsValidTarget(target) then
		if MyMenu.combo.q:get() and player:spellSlot(0).state == 0 and player.path.serverPos:dist(target.path.serverPos) <= 350 then
			if MyMenu.combo.modeq:get() == 1 then
				player:castSpell("obj", 0, target)
			elseif MyMenu.combo.modeq:get() == 2 then
				player:castSpell("obj", 0, target)
			end
		end
		if MyMenu.combo.e:get() and player:spellSlot(2).state == 0 and player.path.serverPos:dist(target.path.serverPos) <= 375 then
			if MyMenu.combo.modee:get() == 1 and not eActive then
				player:castSpell("self", 2)
			elseif MyMenu.combo.modee:get() == 1 and eActive then
				player:castSpell("self", 2)
			elseif MyMenu.combo.modee:get() == 2 and not eActive then
				player:castSpell("self", 2)
			end
		end
	end
end

local function Harass()
	local target = get_target(select_target)
	if target and common.IsValidTarget(target) then
		if player.par / player.maxPar * 100 >= MyMenu.harass.Mana:get() then
			if MyMenu.harass.q:get() and player:spellSlot(0).state == 0 and player.path.serverPos:dist(target.path.serverPos) < 350 then
				if MyMenu.harass.modeq:get() == 1 then
					player:castSpell("obj", 0, target)
				elseif MyMenu.harass.modeq:get() == 2 then
					player:castSpell("obj", 0, target)
				end
			end
			if MyMenu.harass.e:get() and player:spellSlot(2).state == 0 and player.path.serverPos:dist(target.path.serverPos) < 375 then
				if MyMenu.harass.modee:get() == 1 and not eActive then
					player:castSpell("self", 2)
				elseif MyMenu.harass.modee:get() == 1 and eActive then
					player:castSpell("self", 2)
				elseif MyMenu.harass.modee:get() == 2 and not eActive then
					player:castSpell("self", 2)
				end
			end
		end
	end
end

local function KillSteal()
	for i = 0, objManager.enemies_n - 1 do
		local enemy = objManager.enemies[i]
 		if enemy and common.IsValidTarget(enemy) and not enemy.buff["sionpassivezombie"] then
  			if MyMenu.auto.ksq:get() and player:spellSlot(0).state == 0 and enemy.health < qDmg(enemy) and player.path.serverPos:dist(enemy.path.serverPos) < 350 then
	  			player:castSpell("obj", 0, enemy)
	  		end
   			if MyMenu.auto.ksr:get() and player:spellSlot(3).state == 0 and enemy.health < rDmg(enemy) and #common.GetEnemyHeroesInRange(700) <= 2 then 
   				if player.path.serverPos:dist(enemy.path.serverPos) < rRange then
					local seg = preds.linear.get_prediction(rPred, enemy)
					if seg and seg.startPos:dist(seg.endPos) < rRange then
						if not preds.collision.get_prediction(rPred, seg, enemy) then
							player:castSpell("pos", 3, vec3(seg.endPos.x, game.mousePos.y, seg.endPos.y))
						end
					end
				end
   			end
  		end
 	end
end

local function Clear()
	local target = { obj = nil, health = 0, mode = "jungleclear" }
	local aaRange = player.attackRange + player.boundingRadius + 200
	for i = 0, minionmanager.size[TEAM_NEUTRAL] - 1 do
		local obj = minionmanager[TEAM_NEUTRAL][i]
		if player.pos:dist(obj.pos) <= aaRange and obj.maxHealth > target.health then
			target.obj = obj
			target.health = obj.maxHealth
		end
	end
	if target.obj then
		if target.mode == "jungleclear" then
			if player.par / player.maxPar * 100 >= MyMenu.jg.Mana:get() then
				if MyMenu.jg.q:get() and player:spellSlot(0).state == 0 and player.path.serverPos:dist(target.obj.path.serverPos) < 350 then
					player:castSpell("obj", 0, target.obj)
				end
				if MyMenu.jg.e:get() and player:spellSlot(2).state == 0 and player.path.serverPos:dist(target.obj.path.serverPos) < 375 then
					if not eActive then
						player:castSpell("self", 2)
					elseif eActive then
						player:castSpell("self", 2)
					end
				end
			end
		end
	end
end

local function Run()
	if MyMenu.Key.run:get() then
		player:move(vec3(mousePos.x, mousePos.y, mousePos.z))
		if player:spellSlot(0).state == 0 then
			local enemyMinions = common.GetMinionsInRange(350, TEAM_ENEMY, mousePos)
			local jungle = common.GetMinionsInRange(350, TEAM_NEUTRAL, mousePos)

			local minion = GetClosestMob(target)
			if minion and player.path.serverPos:dist(minion.path.serverPos) < 350 then
				player:castSpell("obj", 0, minion)
			end
			local jmob = GetClosestJungle(target)
			if jmob and player.path.serverPos:dist(jmob.path.serverPos) < 350 then
				player:castSpell("obj", 0, jmob)
			end
		end
	end
end

local function Buff()
	if player.buff["warwicke"] then
		eActive = true
	else
		eActive = false
	end
end


local function OnTick()
	Buff()
	rRange = ((player.moveSpeed * 1.88))
	if MyMenu.Key.Combo:get() then Combo() end
	if MyMenu.Key.Harass:get() then Harass() end
	if MyMenu.Key.LaneClear:get() then
		Clear()
	end
	if MyMenu.auto.uks:get() then KillSteal() end
	if MyMenu.Key.run:get() then Run() end
end


local function OnDraw()
	if MyMenu.draws.q:get() and player:spellSlot(0).state == 0 and player.isOnScreen then
		graphics.draw_circle(player.pos, 350, 2, MyMenu.draws.colorq:get(), 70)
	end
	if MyMenu.draws.e:get() and player:spellSlot(3).state == 0 and player.isOnScreen then
		graphics.draw_circle(player.pos, (player.moveSpeed * 1.88), 2, MyMenu.draws.colore:get(), 70)
	end
end

orb.combat.register_f_pre_tick(OnTick)
cb.add(cb.draw, OnDraw)
cb.add(cb.error, function(msg)
  local log, e = io.open(hanbot.devpath..'/log.txt', 'w+')
  if not log then
    print(e)
    return
  end
 
  log:write(msg)
  log:close()
end)

return WarwickPlugin
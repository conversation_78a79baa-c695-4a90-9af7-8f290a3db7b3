
local ove_0_20 = player.charName
local ove_0_21 = {
	AurelionSol = {
		i_gap_elements = 5,
		i_width = 280,
		i_gap = 10,
		i_gap_other = 0,
		i_height = 60,
		i_font_size = 15
	},
	Corki = {
		i_gap_elements = 5,
		i_width = 250,
		i_gap = 10,
		i_gap_other = 0,
		i_height = 85,
		i_font_size = 15
	},
	Ek<PERSON> = {
		dont_adjust_box_width = true,
		i_gap_elements = 5,
		i_width = 250,
		i_gap = 10,
		i_gap_other = 0,
		i_height = 60,
		i_font_size = 15
	},
	Jhin = {
		i_gap_elements = 5,
		i_width = 250,
		i_gap = 10,
		i_gap_other = 0,
		i_height = 75,
		i_font_size = 15
	},
	<PERSON><PERSON><PERSON> = {
		i_gap_elements = 5,
		i_width = 250,
		i_gap = 10,
		i_gap_other = 0,
		i_height = 85,
		i_font_size = 15
	},
	Kayle = {
		i_gap_elements = 10,
		i_width = 250,
		i_gap = 10,
		i_gap_other = 0,
		i_height = 100,
		i_font_size = 15
	},
	<PERSON><PERSON> = {
		i_gap_elements = 5,
		i_width = 250,
		i_gap = 10,
		i_gap_other = 0,
		i_height = 85,
		i_font_size = 15
	},
	Kindred = {
		i_gap_elements = 5,
		i_width = 250,
		i_gap = 10,
		i_gap_other = 0,
		i_height = 60,
		i_font_size = 15
	},
	Lux = {
		i_gap_elements = 5,
		i_width = 250,
		i_gap = 10,
		i_gap_other = 0,
		i_height = 75,
		i_font_size = 15
	},
	Malzahar = {
		i_gap_elements = 5,
		i_width = 250,
		i_gap = 10,
		i_gap_other = 0,
		i_height = 60,
		i_font_size = 15
	},
	Senna = {
		i_gap_elements = 10,
		i_width = 250,
		i_gap = 10,
		i_gap_other = 0,
		i_height = 100,
		i_font_size = 15
	},
	Seraphine = {
		i_gap_elements = 10,
		i_width = 260,
		i_gap = 10,
		i_gap_other = 0,
		i_height = 100,
		i_font_size = 15
	},
	Sona = {
		i_gap_elements = 5,
		i_width = 250,
		i_gap = 10,
		i_gap_other = 0,
		i_height = 85,
		i_font_size = 15
	},
	Soraka = {
		i_gap_elements = 10,
		i_width = 250,
		i_gap = 10,
		i_gap_other = 0,
		i_height = 100,
		i_font_size = 15
	},
	Tristana = {
		i_gap_elements = 5,
		i_width = 250,
		i_gap = 10,
		i_gap_other = 0,
		i_height = 85,
		i_font_size = 15
	},
	Trundle = {
		i_gap_elements = 5,
		i_width = 250,
		i_gap = 10,
		i_gap_other = 0,
		i_height = 85,
		i_font_size = 15
	},
	Vex = {
		i_gap_elements = 5,
		i_width = 250,
		i_gap = 10,
		i_gap_other = 0,
		i_height = 85,
		i_font_size = 15
	},
	Yone = {
		i_gap_elements = 5,
		i_width = 275,
		i_gap = 10,
		i_gap_other = 0,
		i_height = 110,
		i_font_size = 15
	},
	Yorick = {
		dont_adjust_box_width = true,
		i_gap_elements = 5,
		i_width = 250,
		i_gap = 10,
		i_gap_other = 0,
		i_height = 60,
		i_font_size = 15
	}
}

if not ove_0_21[player.charName] then
	print(Laurelindorenan - 123)

	return
end

local ove_0_22 = menu("HealerAIO_InfoBox_settings", "Healer AIO - Info Box")
local ove_0_23 = "_infobox_" .. player.charName:lower()
local ove_0_24 = false

ove_0_22:header("iHead", "Info Box Settings")
ove_0_22:boolean("i_enable", "Enable ", true)
ove_0_22:dropdown("i_move_mode", "When the box is movable", 1, {
	"Always ",
	"Menu is open",
	"Never "
})
ove_0_22.i_move_mode:set("tooltip", "You can move the box with your mouse.")
ove_0_22:button("reset_position", "Reset info box position", "Reset", function()
	-- print 5
	ove_0_24 = true
end)
ove_0_22.reset_position:set("tooltip", "For situations where info box would get lost after changing game resolution")
ove_0_22:menu("advanced", "Advanced Settings")
ove_0_22.advanced:header("ibox_advanced", "Advanced Settings")
ove_0_22.advanced:slider("i_width" .. ove_0_23, "Box width", ove_0_21[ove_0_20].i_width, 150, 500, 5)
ove_0_22.advanced:slider("i_height" .. ove_0_23, "Box height", ove_0_21[ove_0_20].i_height, 50, 500, 5)
ove_0_22.advanced:slider("i_thickness", "Box border thickness", 4, 1, 10, 1)
ove_0_22.advanced:slider("i_font_size" .. ove_0_23, "Text font size", ove_0_21[ove_0_20].i_font_size, 1, 30, 1)
ove_0_22.advanced:slider("i_gap_other" .. ove_0_23, "Elements height in the box", ove_0_21[ove_0_20].i_gap_other, -50, 50, 1)
ove_0_22.advanced:slider("i_gap_elements" .. ove_0_23, "Gap between elements", ove_0_21[ove_0_20].i_gap_elements, -50, 50, 1)
ove_0_22.advanced:slider("i_gap" .. ove_0_23, "Gap between elements and border", ove_0_21[ove_0_20].i_gap, 0, 50, 1)
ove_0_22.advanced:boolean("i_title_enable", "Show title", true)
ove_0_22.advanced:slider("i_title_font_size", "Title font size", 20, 0, 30, 1)
ove_0_22:header("iHead1", "Color Settings")
ove_0_22:menu("i_element", "Element color settings")
ove_0_22.i_element:header("iHead1", "Element Color Settings")
ove_0_22.i_element:color("i_text_color", "Element text color", 255, 255, 255, 225)
ove_0_22.i_element:color("i_enabled_color", "'Enabled' text color", 131, 255, 20, 255)
ove_0_22.i_element:color("i_disabled_color", "'Disabled' text color", 208, 0, 51, 255)
ove_0_22:menu("i_title", "Title color settings")
ove_0_22.i_title:header("iHead1", "Title Color Settings")
ove_0_22.i_title:boolean("i_title_cycle", "Enable title color cycling", false)
ove_0_22.i_title:slider("i_title_cycle_speed", "Title color cycle speed", 15, 1, 35, 1)
ove_0_22.i_title:slider("i_title_transparacy", "Title transparacy", 225, 0, 255, 1)
ove_0_22.i_title:color("i_title_color", "Title text color", 255, 255, 255, 225)
ove_0_22:menu("i_border", "Border color settings")
ove_0_22.i_border:header("iHead1", "Border Color Settings")
ove_0_22.i_border:boolean("i_border_cycle", "Enable border color cycling", true)
ove_0_22.i_border:slider("i_border_cycle_speed", "Border color cycle speed", 15, 1, 35, 1)
ove_0_22.i_border:slider("i_border_transparacy", "Border transparacy", 200, 0, 255, 1)
ove_0_22.i_border:color("i_border_color", "Border color", 0, 0, 0, 255)
ove_0_22:menu("i_background", "Background color settings")
ove_0_22.i_background:header("iHead1", "Background Color Settings")
ove_0_22.i_background:boolean("i_background_cycle", "Enable background color cycling", false)
ove_0_22.i_background:slider("i_background_cycle_speed", "Background color cycle speed", 15, 1, 35, 1)
ove_0_22.i_background:slider("i_background_transparacy", "Background transparacy", 25, 0, 255, 1)
ove_0_22.i_background:color("i_background_color", "Background color ", 0, 0, 0, 125)

local ove_0_25 = {
	w = ove_0_22.advanced["i_width" .. ove_0_23]:get(),
	h = ove_0_22.advanced["i_height" .. ove_0_23]:get()
}
local ove_0_26 = ove_0_25.h / 2 + 50
local ove_0_27 = minimap.x - ove_0_25.w - 45
local ove_0_28 = graphics.height - ove_0_26

ove_0_22:slider("x_pos" .. ove_0_23, "X", ove_0_27, -9999, 9999, 1)
ove_0_22:slider("y_pos" .. ove_0_23, "Y", ove_0_28, -9999, 9999, 1)
ove_0_22["x_pos" .. ove_0_23]:set("visible", false)
ove_0_22["y_pos" .. ove_0_23]:set("visible", false)
ove_0_22:header("headerTitle", "- Info Box for Healer AIO -")

if ove_0_22.advanced.i_title_enable:get() then
	ove_0_22.advanced.i_title_font_size:set("visible", true)
else
	ove_0_22.advanced.i_title_font_size:set("visible", false)
end

if ove_0_22.i_title.i_title_cycle:get() then
	ove_0_22.i_title.i_title_color:set("visible", false)
	ove_0_22.i_title.i_title_cycle_speed:set("visible", true)
	ove_0_22.i_title.i_title_transparacy:set("visible", true)
else
	ove_0_22.i_title.i_title_color:set("visible", true)
	ove_0_22.i_title.i_title_cycle_speed:set("visible", false)
	ove_0_22.i_title.i_title_transparacy:set("visible", false)
end

if ove_0_22.i_border.i_border_cycle:get() then
	ove_0_22.i_border.i_border_color:set("visible", false)
	ove_0_22.i_border.i_border_cycle_speed:set("visible", true)
	ove_0_22.i_border.i_border_transparacy:set("visible", true)
else
	ove_0_22.i_border.i_border_color:set("visible", true)
	ove_0_22.i_border.i_border_cycle_speed:set("visible", false)
	ove_0_22.i_border.i_border_transparacy:set("visible", false)
end

if ove_0_22.i_background.i_background_cycle:get() then
	ove_0_22.i_background.i_background_color:set("visible", false)
	ove_0_22.i_background.i_background_cycle_speed:set("visible", true)
	ove_0_22.i_background.i_background_transparacy:set("visible", true)
else
	ove_0_22.i_background.i_background_color:set("visible", true)
	ove_0_22.i_background.i_background_cycle_speed:set("visible", false)
	ove_0_22.i_background.i_background_transparacy:set("visible", false)
end

ove_0_22.advanced.i_title_enable:set("callback", function(arg_6_0, arg_6_1)
	-- print 6
	if arg_6_0 == false then
		ove_0_22.advanced.i_title_font_size:set("visible", true)
	else
		ove_0_22.advanced.i_title_font_size:set("visible", false)
	end
end)
ove_0_22.i_title.i_title_cycle:set("callback", function(arg_7_0, arg_7_1)
	-- print 7
	if arg_7_0 == false then
		ove_0_22.i_title.i_title_color:set("visible", false)
		ove_0_22.i_title.i_title_cycle_speed:set("visible", true)
		ove_0_22.i_title.i_title_transparacy:set("visible", true)
	else
		ove_0_22.i_title.i_title_color:set("visible", true)
		ove_0_22.i_title.i_title_cycle_speed:set("visible", false)
		ove_0_22.i_title.i_title_transparacy:set("visible", false)
	end
end)
ove_0_22.i_border.i_border_cycle:set("callback", function(arg_8_0, arg_8_1)
	-- print 8
	if arg_8_0 == false then
		ove_0_22.i_border.i_border_color:set("visible", false)
		ove_0_22.i_border.i_border_cycle_speed:set("visible", true)
		ove_0_22.i_border.i_border_transparacy:set("visible", true)
	else
		ove_0_22.i_border.i_border_color:set("visible", true)
		ove_0_22.i_border.i_border_cycle_speed:set("visible", false)
		ove_0_22.i_border.i_border_transparacy:set("visible", false)
	end
end)
ove_0_22.i_background.i_background_cycle:set("callback", function(arg_9_0, arg_9_1)
	-- print 9
	if arg_9_0 == false then
		ove_0_22.i_background.i_background_color:set("visible", false)
		ove_0_22.i_background.i_background_cycle_speed:set("visible", true)
		ove_0_22.i_background.i_background_transparacy:set("visible", true)
	else
		ove_0_22.i_background.i_background_color:set("visible", true)
		ove_0_22.i_background.i_background_cycle_speed:set("visible", false)
		ove_0_22.i_background.i_background_transparacy:set("visible", false)
	end
end)

local function ove_0_29()
	-- print 10
	local slot_10_0 = game.time / ove_0_22.i_title.i_title_cycle_speed:get()
	local slot_10_1 = ove_0_22.i_title.i_title_transparacy:get()
	local slot_10_2

	if ove_0_22.i_title.i_title_cycle:get() then
		for iter_10_0 = 0, 32 do
			local slot_10_3 = math.sin(slot_10_0 * iter_10_0 + 0) * 127 + 128
			local slot_10_4 = math.sin(slot_10_0 * iter_10_0 + 2) * 127 + 128
			local slot_10_5 = math.sin(slot_10_0 * iter_10_0 + 4) * 127 + 128

			slot_10_2 = graphics.argb(slot_10_1, slot_10_3, slot_10_4, slot_10_5)
		end
	else
		slot_10_2 = ove_0_22.i_title.i_title_color:get()
	end

	return slot_10_2
end

local function ove_0_30()
	-- print 11
	local slot_11_0 = game.time / ove_0_22.i_border.i_border_cycle_speed:get()
	local slot_11_1 = ove_0_22.i_border.i_border_transparacy:get()
	local slot_11_2

	if ove_0_22.i_border.i_border_cycle:get() then
		for iter_11_0 = 0, 32 do
			local slot_11_3 = math.sin(slot_11_0 * iter_11_0 + 0) * 127 + 128
			local slot_11_4 = math.sin(slot_11_0 * iter_11_0 + 2) * 127 + 128
			local slot_11_5 = math.sin(slot_11_0 * iter_11_0 + 4) * 127 + 128

			slot_11_2 = graphics.argb(slot_11_1, slot_11_3, slot_11_4, slot_11_5)
		end
	else
		slot_11_2 = ove_0_22.i_border.i_border_color:get()
	end

	return slot_11_2
end

local function ove_0_31()
	-- print 12
	local slot_12_0 = game.time / ove_0_22.i_background.i_background_cycle_speed:get()
	local slot_12_1 = ove_0_22.i_background.i_background_transparacy:get()
	local slot_12_2

	if ove_0_22.i_background.i_background_cycle:get() then
		for iter_12_0 = 0, 32 do
			local slot_12_3 = math.sin(slot_12_0 * iter_12_0 + 0) * 127 + 128
			local slot_12_4 = math.sin(slot_12_0 * iter_12_0 + 2) * 127 + 128
			local slot_12_5 = math.sin(slot_12_0 * iter_12_0 + 4) * 127 + 128

			slot_12_2 = graphics.argb(slot_12_1, slot_12_3, slot_12_4, slot_12_5)
		end
	else
		slot_12_2 = ove_0_22.i_background.i_background_color:get()
	end

	return slot_12_2
end

local ove_0_32 = "Healer AIO - " .. ove_0_20

local function ove_0_33()
	-- print 13
	return ove_0_32
end

local ove_0_34 = true

if ove_0_21[ove_0_20].dont_adjust_box_width then
	ove_0_34 = false
end

local ove_0_35 = false
local ove_0_36 = false
local ove_0_37 = false

if ove_0_20 == "Senna" then
	ove_0_35 = true
end

if ove_0_20 == "Yone" then
	local ove_0_38 = true
end

if ove_0_20 == "Seraphine" then
	ove_0_37 = true
end

local function ove_0_39(arg_14_0)
	-- print 14
	local slot_14_0 = arg_14_0
	local slot_14_1 = 0
	local slot_14_2 = 0
	local slot_14_3 = 0
	local slot_14_4 = 0
	local slot_14_5 = 0

	if hanbot.language == 1 then
		slot_14_3 = -2

		if ove_0_34 then
			slot_14_1 = not ove_0_37 and 20 or 30
		end

		if slot_14_0 == 3 then
			slot_14_4 = 2
			slot_14_5 = -1
		end

		if slot_14_0 == 4 then
			slot_14_2 = -5
		end

		if slot_14_0 == 5 then
			if not ove_0_35 then
				slot_14_5 = -1
				slot_14_4 = 3
			else
				slot_14_2 = 10
				slot_14_5 = -1
				slot_14_4 = -2
			end
		end
	else
		if slot_14_0 == 3 then
			if graphics.get_font() == "Courier New" then
				slot_14_2 = -5
			elseif graphics.get_font() == "Lucida Sans Unicode" then
				slot_14_4 = 5
			end
		end

		if slot_14_0 == 4 then
			if graphics.get_font() == "Courier New" then
				slot_14_2 = -10
			elseif graphics.get_font() == "Lucida Sans Unicode" then
				slot_14_2 = -5
			elseif graphics.get_font() == "Gill Sans MT Pro Medium" then
				slot_14_2 = -5
				slot_14_4 = -2
			end
		end

		if slot_14_0 == 5 then
			if not ove_0_35 then
				if graphics.get_font() == "Courier New" then
					slot_14_2 = -5
					slot_14_4 = 3
				elseif graphics.get_font() == "Gill Sans MT Pro Medium" then
					slot_14_2 = -5
				elseif graphics.get_font() == "Lucida Sans Unicode" then
					slot_14_2 = -5
					slot_14_4 = 3
				end
			elseif graphics.get_font() == "Courier New" then
				slot_14_2 = -5
				slot_14_4 = -5
			elseif graphics.get_font() == "Gill Sans MT Pro Medium" then
				slot_14_3 = -1
				slot_14_2 = -5
				slot_14_4 = -5
			elseif graphics.get_font() == "Lucida Sans Unicode" then
				slot_14_2 = 5
			end
		end
	end

	return slot_14_1, slot_14_2, slot_14_3, slot_14_4, slot_14_5
end

local ove_0_40 = false
local ove_0_41 = 0
local ove_0_42 = 0
local ove_0_43 = 0
local ove_0_44 = 0
local ove_0_45 = 0
local ove_0_46
local ove_0_47
local ove_0_48
local ove_0_49
local ove_0_50
local ove_0_51
local ove_0_52 = {
	enable = function()
		-- print 15
		return ove_0_22.i_enable:get()
	end
}
local ove_0_53 = 0

ove_0_25.anchor = vec2(ove_0_22["x_pos" .. ove_0_23]:get(), ove_0_22["y_pos" .. ove_0_23]:get())
ove_0_25.dragging = false

function ove_0_52.draw(arg_16_0)
	-- print 16
	if not ove_0_22.i_enable:get() then
		return
	end

	if ove_0_25.dragging then
		ove_0_25.anchor.x = game.cursorPos.x - ove_0_25.offset.x
		ove_0_25.anchor.y = game.cursorPos.y - ove_0_25.offset.y
	end

	local slot_16_0 = #arg_16_0

	if ove_0_53 < os.clock() then
		ove_0_41, ove_0_42, ove_0_43, ove_0_44, ove_0_45 = ove_0_39(slot_16_0)
		ove_0_53 = os.clock() + 0.1
	end

	if not ove_0_40 then
		ove_0_46 = ove_0_22.advanced.i_thickness:get()
		ove_0_47 = ove_0_22.advanced["i_height" .. ove_0_23]:get() + ove_0_42
		ove_0_48 = graphics.dxline(ove_0_46)
		ove_0_49 = graphics.dxline(ove_0_47)
		ove_0_50 = true
		ove_0_51 = true

		ove_0_48:SetAntialias(false)
		ove_0_49:SetAntialias(false)
		ove_0_22.advanced.i_thickness:set("callback", function(arg_17_0, arg_17_1)
			-- print 17
			if not ove_0_22.i_enable:get() then
				return
			end

			if arg_17_0 == arg_17_1 then
				return
			end

			graphics.dxline(arg_17_1):SetAntialias(false)
			graphics.dxline(arg_17_0):SetAntialias(ove_0_50)

			local slot_17_0 = ove_0_22.advanced["i_height" .. ove_0_23]:get() + ove_0_42

			graphics.dxline(slot_17_0):SetAntialias(false)
		end)
		ove_0_22.advanced["i_height" .. ove_0_23]:set("callback", function(arg_18_0, arg_18_1)
			-- print 18
			if not ove_0_22.i_enable:get() then
				return
			end

			if arg_18_0 == arg_18_1 then
				return
			end

			graphics.dxline(arg_18_1 + ove_0_42):SetAntialias(false)
			graphics.dxline(arg_18_0 + ove_0_42):SetAntialias(ove_0_51)
		end)
		ove_0_22.i_enable:set("callback", function(arg_19_0, arg_19_1)
			-- print 19
			if arg_19_1 then
				local slot_19_0 = ove_0_22.advanced.i_thickness:get()
				local slot_19_1 = ove_0_22.advanced["i_height" .. ove_0_23]:get() + ove_0_42
				local slot_19_2 = graphics.dxline(slot_19_0)
				local slot_19_3 = graphics.dxline(slot_19_1)

				slot_19_2:SetAntialias(false)
				slot_19_3:SetAntialias(false)
			end

			if arg_19_0 then
				local slot_19_4 = ove_0_22.advanced.i_thickness:get()
				local slot_19_5 = ove_0_22.advanced["i_height" .. ove_0_23]:get() + ove_0_42
				local slot_19_6 = graphics.dxline(slot_19_4)
				local slot_19_7 = graphics.dxline(slot_19_5)

				slot_19_6:SetAntialias(ove_0_50)
				slot_19_7:SetAntialias(ove_0_51)
			end
		end)

		ove_0_40 = true
	end

	ove_0_25.w = ove_0_22.advanced["i_width" .. ove_0_23]:get() + ove_0_41
	ove_0_25.h = ove_0_22.advanced["i_height" .. ove_0_23]:get() + ove_0_42
	ove_0_25.border = ove_0_22.advanced.i_thickness:get()

	local slot_16_1 = ove_0_25.anchor.x
	local slot_16_2 = ove_0_25.anchor.y

	if ove_0_24 then
		ove_0_25.anchor.x = ove_0_27
		ove_0_25.anchor.y = ove_0_28
		ove_0_24 = false
	end

	local slot_16_3 = ove_0_22.advanced["i_gap" .. ove_0_23]:get()
	local slot_16_4 = ove_0_22.advanced["i_gap_other" .. ove_0_23]:get() + ove_0_45
	local slot_16_5 = ove_0_22.advanced["i_gap_elements" .. ove_0_23]:get() + ove_0_44 + 4
	local slot_16_6 = ove_0_33()
	local slot_16_7 = ove_0_22.advanced["i_font_size" .. ove_0_23]:get() + ove_0_43
	local slot_16_8 = ove_0_22.advanced.i_title_font_size:get()

	if ove_0_22.advanced.i_title_enable:get() then
		local slot_16_9, slot_16_10 = graphics.text_area(slot_16_6, slot_16_8)
		local slot_16_11 = slot_16_9 / 2

		graphics.draw_text_2D(slot_16_6, slot_16_8, slot_16_1 + ove_0_25.w / 2 - slot_16_11, slot_16_2 - ove_0_25.h / 2 + 15, ove_0_29())

		slot_16_4 = slot_16_4 - slot_16_8 + 10
	end

	for iter_16_0 = 1, slot_16_0 do
		local slot_16_12 = arg_16_0[iter_16_0]
		local slot_16_13 = "No Key"

		if slot_16_12.panic_clear then
			if slot_16_12.panic_clear_menu.key then
				slot_16_13 = slot_16_12.panic_clear_menu.key
			elseif slot_16_12.panic_clear_menu.toggle then
				slot_16_13 = slot_16_12.panic_clear_menu.toggle
			end
		end

		local slot_16_14 = slot_16_12.name
		local slot_16_15 = slot_16_12.menu.key
		local slot_16_16 = slot_16_12.menu.toggle
		local slot_16_17

		if not slot_16_12.other_activator_controls_state then
			slot_16_17 = slot_16_12.menu:get() or slot_16_12.other_activator
		else
			slot_16_17 = slot_16_12.other_activator
		end

		local slot_16_18, slot_16_19 = graphics.text_area(slot_16_14, slot_16_7)
		local slot_16_20 = slot_16_7 + 1
		local slot_16_21 = "Enabled"
		local slot_16_22 = "Disabled"

		if slot_16_12.custom_state_text then
			slot_16_21 = slot_16_12.custom_state_text
			slot_16_22 = slot_16_12.custom_state_text
		end

		local slot_16_23, slot_16_24 = graphics.text_area(slot_16_21, slot_16_20)
		local slot_16_25, slot_16_26 = graphics.text_area(slot_16_22, slot_16_20)
		local slot_16_27 = slot_16_2 - (ove_0_25.h - (slot_16_8 + slot_16_5)) * slot_16_12.position - slot_16_4
		local slot_16_28 = slot_16_18 + 3
		local slot_16_29 = ove_0_25.w / 2
		local slot_16_30 = -slot_16_3 + slot_16_29
		local slot_16_31 = -slot_16_23 - slot_16_3 + slot_16_29
		local slot_16_32 = -slot_16_25 - slot_16_3 + slot_16_29

		graphics.draw_text_2D(slot_16_14, slot_16_7, slot_16_1 + slot_16_29 - slot_16_30, slot_16_27, ove_0_22.i_element.i_text_color:get())

		if slot_16_15 and (not slot_16_16 or slot_16_16 == "") then
			if not slot_16_12.panic_clear then
				graphics.draw_text_2D("[" .. slot_16_15 .. "]", slot_16_7, slot_16_1 + slot_16_28 + slot_16_29 - slot_16_30, slot_16_27, ove_0_22.i_element.i_text_color:get())
			else
				graphics.draw_text_2D("[" .. slot_16_13 .. "]+[" .. slot_16_15 .. "]", slot_16_7, slot_16_1 + slot_16_28 + slot_16_29 - slot_16_30, slot_16_27, ove_0_22.i_element.i_text_color:get())
			end
		elseif slot_16_16 and slot_16_16 ~= "" and (not slot_16_15 or slot_16_15 == "") then
			if not slot_16_12.panic_clear then
				graphics.draw_text_2D("[" .. slot_16_16 .. "]", slot_16_7, slot_16_1 + slot_16_28 + slot_16_29 - slot_16_30, slot_16_27, ove_0_22.i_element.i_text_color:get())
			else
				graphics.draw_text_2D("[" .. slot_16_13 .. "]+[" .. slot_16_16 .. "]", slot_16_7, slot_16_1 + slot_16_28 + slot_16_29 - slot_16_30, slot_16_27, ove_0_22.i_element.i_text_color:get())
			end
		elseif slot_16_16 and slot_16_16 ~= "" and slot_16_15 and slot_16_15 ~= "" then
			local slot_16_33, slot_16_34 = graphics.text_area("[" .. slot_16_15 .. "]", slot_16_7)
			local slot_16_35 = -slot_16_3 - (slot_16_28 + slot_16_33) + slot_16_29 - 2

			if slot_16_16 ~= slot_16_15 then
				if not slot_16_12.panic_clear then
					graphics.draw_text_2D("[" .. slot_16_15 .. "]", slot_16_7, slot_16_1 + slot_16_28 + slot_16_29 - slot_16_30, slot_16_27, ove_0_22.i_element.i_text_color:get())
					graphics.draw_text_2D("[" .. slot_16_16 .. "]", slot_16_7, slot_16_1 + slot_16_29 - slot_16_35, slot_16_27, ove_0_22.i_element.i_text_color:get())
				else
					graphics.draw_text_2D("[" .. slot_16_13 .. "]+[" .. slot_16_15 .. "][" .. slot_16_16 .. "]", slot_16_7, slot_16_1 + slot_16_28 + slot_16_29 - slot_16_30, slot_16_27, ove_0_22.i_element.i_text_color:get())
				end
			elseif not slot_16_12.panic_clear then
				graphics.draw_text_2D("[" .. slot_16_15 .. "]", slot_16_7, slot_16_1 + slot_16_28 + slot_16_29 - slot_16_30, slot_16_27, ove_0_22.i_element.i_text_color:get())
			else
				graphics.draw_text_2D("[" .. slot_16_13 .. "]+[" .. slot_16_15 .. "]", slot_16_7, slot_16_1 + slot_16_28 + slot_16_29 - slot_16_30, slot_16_27, ove_0_22.i_element.i_text_color:get())
			end
		else
			graphics.draw_text_2D("[No Key]", slot_16_7, slot_16_1 + slot_16_28 + slot_16_29 - slot_16_30, slot_16_27, ove_0_22.i_element.i_text_color:get())
		end

		if slot_16_12.state_yellow == nil then
			if slot_16_17 then
				graphics.draw_text_2D(slot_16_21, slot_16_20, slot_16_1 + slot_16_29 + slot_16_31, slot_16_27 + 0.5, ove_0_22.i_element.i_enabled_color:get())
			else
				graphics.draw_text_2D(slot_16_22, slot_16_20, slot_16_1 + slot_16_29 + slot_16_32, slot_16_27 + 0.5, ove_0_22.i_element.i_disabled_color:get())
			end
		elseif slot_16_17 then
			if slot_16_12.state_yellow then
				graphics.draw_text_2D(slot_16_21, slot_16_20, slot_16_1 + slot_16_29 + slot_16_31, slot_16_27 + 0.5, ove_0_22.i_element.i_enabled_color:get())
			else
				graphics.draw_text_2D(slot_16_21, slot_16_20, slot_16_1 + slot_16_29 + slot_16_31, slot_16_27 + 0.5, graphics.argb(255, 255, 225, 0))
			end
		else
			graphics.draw_text_2D(slot_16_22, slot_16_20, slot_16_1 + slot_16_29 + slot_16_32, slot_16_27 + 0.5, ove_0_22.i_element.i_disabled_color:get())
		end
	end

	local slot_16_36 = ove_0_25.h / 2
	local slot_16_37 = ove_0_25.border / 2

	graphics.draw_line_2D(slot_16_1, slot_16_2 - slot_16_36 - slot_16_37, slot_16_1 + ove_0_25.w, slot_16_2 - slot_16_36 - slot_16_37, ove_0_25.border, ove_0_30())
	graphics.draw_line_2D(slot_16_1, slot_16_2 + slot_16_36 + slot_16_37, slot_16_1 + ove_0_25.w, slot_16_2 + slot_16_36 + slot_16_37, ove_0_25.border, ove_0_30())
	graphics.draw_line_2D(slot_16_1 - slot_16_37, slot_16_2 + slot_16_36 + ove_0_25.border, slot_16_1 - slot_16_37, slot_16_2 - slot_16_36 - ove_0_25.border, ove_0_25.border, ove_0_30())
	graphics.draw_line_2D(slot_16_1 + ove_0_25.w + slot_16_37, slot_16_2 + slot_16_36 + ove_0_25.border, slot_16_1 + ove_0_25.w + slot_16_37, slot_16_2 - slot_16_36 - ove_0_25.border, ove_0_25.border, ove_0_30())
	graphics.draw_line_2D(slot_16_1, slot_16_2, slot_16_1 + ove_0_25.w, slot_16_2, ove_0_25.h, ove_0_31())

	if ove_0_22["x_pos" .. ove_0_23]:get() ~= slot_16_1 then
		ove_0_22["x_pos" .. ove_0_23]:set("value", slot_16_1)
	end

	if ove_0_22["y_pos" .. ove_0_23]:get() ~= slot_16_2 then
		ove_0_22["y_pos" .. ove_0_23]:set("value", slot_16_2)
	end
end

local function ove_0_54(arg_20_0)
	-- print 20
	if arg_20_0 ~= 1 then
		return
	end

	if not ove_0_22.i_enable:get() then
		return
	end

	if ove_0_22.i_move_mode:get() == 3 or ove_0_22.i_move_mode:get() == 2 and not menu:isopen() then
		return
	end

	local slot_20_0 = game.cursorPos

	ove_0_25.dragging = slot_20_0.x > ove_0_25.anchor.x and slot_20_0.x < ove_0_25.anchor.x + ove_0_25.w and slot_20_0.y > ove_0_25.anchor.y - ove_0_25.h * 0.5 and slot_20_0.y < ove_0_25.anchor.y + ove_0_25.h * 0.5
	ove_0_25.offset = vec2(slot_20_0.x - ove_0_25.anchor.x, slot_20_0.y - ove_0_25.anchor.y)
end

local function ove_0_55(arg_21_0)
	-- print 21
	if arg_21_0 ~= 1 then
		return
	end

	if not ove_0_22.i_enable:get() then
		return
	end

	ove_0_25.dragging = false
end

cb.add(cb.keydown, ove_0_54)
cb.add(cb.keyup, ove_0_55)

return ove_0_52

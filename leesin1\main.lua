local slot_20_0 = module.internal("orb")
local slot_20_1 = module.internal("pred")
local slot_20_2 = module.internal("TS")
local slot_20_3 = module.seek("evade")
local slot_20_4 = module.load(header.id, "leesin/menu")
local slot_20_5 = module.load(header.id, "Libraries/common1")
local slot_20_6 = module.load(header.id, "Libraries/mathematical")
local slot_20_7 = module.load(header.id, "leesin/misc")
local slot_20_8
local slot_20_9 = slot_20_5.GetFlashNum()
local slot_20_10 = 0
local slot_20_11
local slot_20_12 = 0
local slot_20_13
local slot_20_14
local slot_20_15 = 0
local slot_20_16 = 0
local slot_20_17 = 0
local slot_20_18 = 0
local slot_20_19
local slot_20_20 = {
	390,
	410,
	430,
	450,
	480,
	510,
	540,
	570,
	600,
	640,
	680,
	720,
	760,
	800,
	850,
	900,
	950,
	1000,
	1050,
	1100,
	1150,
	1200,
	1275,
	1300,
	1350,
	1400,
	1450,
	1500,
	1550,
	1600
}

for iter_20_0 = 4, 5 do
	if player:spellSlot(iter_20_0).name:lower():find("summonersmite") then
		slot_20_19 = iter_20_0
	end
end

local slot_20_21 = {
	boundingRadiusMod = 0,
	range = 1400,
	width = 61,
	speed = 1800,
	delay = 0.25,
	collision = {
		minion = false,
		wall = hero,
		hero = hero
	},
	damage = function(arg_21_0)
		return slot_20_7.Q1Dmg(arg_21_0)
	end
}

local function slot_20_22()
	for iter_22_0 = 0, objManager.enemies_n - 1 do
		local slot_22_0 = objManager.enemies[iter_22_0]

		if slot_22_0 and slot_20_5.isValid(slot_22_0) and slot_22_0.team ~= TEAM_ALLY and slot_22_0.buff.leesinqone and slot_22_0.buff.leesinqone.source == player then
			return slot_22_0
		end
	end

	local slot_22_1 = objManager.minions

	for iter_22_1 = 0, slot_22_1.size[TEAM_NEUTRAL] - 1 do
		local slot_22_2 = slot_22_1[TEAM_NEUTRAL][iter_22_1]

		if slot_22_2 and not slot_22_2.isDead and slot_22_2.isVisible and slot_22_2.isTargetable and slot_22_2.buff.leesinqone and slot_22_2.buff.leesinqone.source == player then
			return slot_22_2
		end
	end

	for iter_22_2 = 0, slot_22_1.size[TEAM_ENEMY] - 1 do
		local slot_22_3 = slot_22_1[TEAM_ENEMY][iter_22_2]

		if slot_22_3 and not slot_22_3.isDead and slot_22_3.isVisible and slot_22_3.isTargetable and slot_22_3.buff.leesinqone and slot_22_3.buff.leesinqone.source == player then
			return slot_22_3
		end
	end
end

local function slot_20_23(arg_23_0, arg_23_1, arg_23_2)
	if arg_23_1 and arg_23_1.isVisible and arg_23_2 then
		if not arg_23_1.buff[17] and arg_23_1.isTargetable and player:spellSlot(_Q).state == 0 and slot_20_7.FirstSpell(_Q) then
			local slot_23_0 = slot_20_1.linear.get_prediction(slot_20_21, arg_23_1)

			if slot_23_0 and slot_23_0.endPos and slot_23_0.startPos:distSqr(slot_23_0.endPos) < 1440000 and arg_23_2 and arg_23_2 <= 1200 then
				arg_23_0.obj = arg_23_1

				return true
			end
		elseif player:spellSlot(_Q).state == 0 and not slot_20_7.FirstSpell(_Q) and arg_23_1.buff.leesinqone and arg_23_1.buff.leesinqone.source == player and not arg_23_1.buff[17] and arg_23_1.isTargetable and arg_23_2 and arg_23_2 <= 1400 then
			arg_23_0.obj = arg_23_1

			return true
		elseif slot_20_4.killsteal.killstealw:get() and player:spellSlot(_W).state == 0 and slot_20_7.FirstSpell(_W) and not arg_23_1.buff[17] and arg_23_1.isTargetable and arg_23_2 and arg_23_2 <= 600 + player.attackRange + player.boundingRadius and arg_23_1.health <= slot_20_7.AADmg(arg_23_1) * 3 + slot_20_7.EDmg(arg_23_1) then
			arg_23_0.obj = arg_23_1

			return true
		elseif player:spellSlot(_R).state == 0 and game.time > slot_20_18 and not arg_23_1.buff[17] and arg_23_1.isTargetable and arg_23_2 and arg_23_2 <= 375 then
			arg_23_0.obj = arg_23_1

			return true
		elseif player:spellSlot(_E).state == 0 and not arg_23_1.buff[17] and arg_23_1.isTargetable and arg_23_2 and arg_23_2 <= 425 then
			arg_23_0.obj = arg_23_1

			return true
		elseif slot_20_0.combat.target then
			arg_23_0.obj = slot_20_0.combat.target

			return true
		end
	end
end

local function slot_20_24()
	return slot_20_0.ts.get_result(slot_20_23).obj
end

local function slot_20_25(arg_25_0)
	if player:spellSlot(_R).state ~= 0 or not arg_25_0 or not slot_20_5.isValid(arg_25_0) then
		return false
	end

	local slot_25_0 = {}

	for iter_25_0 = 0, objManager.enemies_n - 1 do
		local slot_25_1 = objManager.enemies[iter_25_0]

		if slot_25_1 and slot_20_5.isValid(slot_25_1) and slot_25_1.team ~= TEAM_ALLY and slot_25_1 ~= arg_25_0 and slot_25_1.path.serverPos:dist(arg_25_0.path.serverPos) < 500 then
			slot_25_0[#slot_25_0 + 1] = slot_25_1
		end
	end

	table.sort(slot_25_0, function(arg_26_0, arg_26_1)
		if arg_26_0.health and arg_26_1.health then
			return arg_26_0.health < arg_26_1.health
		end
	end)

	if slot_25_0[1] then
		return slot_25_0[1]
	end
end

local function slot_20_26(arg_27_0)
	if player:spellSlot(_R).state ~= 0 or not arg_27_0 or not slot_20_5.isValid(arg_27_0) then
		return false
	end

	local slot_27_0 = {}
	local slot_27_1 = {
		boundingRadiusMod = 0,
		speed = 863,
		delay = 0.25,
		width = arg_27_0.boundingRadius,
		collision = {
			minion = false,
			hero = true
		}
	}

	for iter_27_0 = 0, objManager.enemies_n - 1 do
		local slot_27_2 = objManager.enemies[iter_27_0]

		if slot_27_2 and slot_20_5.isValid(slot_27_2) and slot_27_2.team ~= TEAM_ALLY and slot_27_2 ~= arg_27_0 and slot_27_2.path.serverPos:dist(arg_27_0.path.serverPos) < 800 then
			local slot_27_3 = player.pos + (slot_27_2.path.serverPos - player.pos):norm() * 800

			if slot_27_3 then
				local slot_27_4 = {
					startPos = arg_27_0.path.serverPos2D,
					endPos = slot_27_3:to2D()
				}
				local slot_27_5 = slot_20_1.collision.get_prediction(slot_27_1, slot_27_4)

				if slot_27_5 then
					table.insert(slot_27_0, {
						unit = slot_27_2,
						count = #slot_27_5
					})
				end
			end
		end
	end

	table.sort(slot_27_0, function(arg_28_0, arg_28_1)
		if arg_28_0.count and arg_28_1.count then
			return arg_28_0.count > arg_28_1.count
		end
	end)

	if slot_27_0[1] then
		return slot_27_0[1].unit
	end
end

local function slot_20_27(arg_29_0)
	if player:spellSlot(_R).state ~= 0 or not arg_29_0 or not slot_20_5.isValid(arg_29_0) then
		return false
	end

	local slot_29_0 = {}
	local slot_29_1 = {
		boundingRadiusMod = 0,
		speed = 863,
		delay = 0.25,
		width = arg_29_0.boundingRadius,
		collision = {
			minion = false,
			hero = true
		}
	}

	for iter_29_0 = 0, objManager.enemies_n - 1 do
		local slot_29_2 = objManager.enemies[iter_29_0]

		if slot_29_2 and slot_20_5.isValid(slot_29_2) and slot_29_2.team ~= TEAM_ALLY and slot_29_2 ~= arg_29_0 and slot_29_2.path.serverPos:dist(arg_29_0.path.serverPos) < 800 then
			local slot_29_3 = player.pos + (slot_29_2.path.serverPos - player.pos):norm() * 800

			if slot_29_3 then
				local slot_29_4 = {
					startPos = arg_29_0.path.serverPos2D,
					endPos = slot_29_3:to2D()
				}
				local slot_29_5 = slot_20_1.collision.get_prediction(slot_29_1, slot_29_4)

				if slot_29_5 and #slot_29_5 >= slot_20_4.combo.comborhit:get() - 1 then
					table.insert(slot_29_0, {
						unit = slot_29_2,
						count = #slot_29_5
					})
				end
			end
		end
	end

	table.sort(slot_29_0, function(arg_30_0, arg_30_1)
		if arg_30_0.count and arg_30_1.count then
			return arg_30_0.count > arg_30_1.count
		end
	end)

	if slot_29_0[1] then
		return slot_29_0[1].unit
	end
end

local function slot_20_28()
	if slot_20_5.WardSlot() and player:spellSlot(_W).state == 0 and slot_20_7.FirstSpell(_W) then
		return 600
	else
		return 200
	end
end

local function slot_20_29()
	if slot_20_4.starcombo.flash:get() and slot_20_9 and player:spellSlot(slot_20_9).state == 0 then
		if slot_20_5.WardSlot() and player:spellSlot(_W).state == 0 and slot_20_7.FirstSpell(_W) then
			return 1200
		else
			return 600
		end
	elseif slot_20_5.WardSlot() and player:spellSlot(_W).state == 0 and slot_20_7.FirstSpell(_W) then
		return 600
	else
		return 200
	end
end

local function slot_20_30()
	if slot_20_4.insec.flash:get() and slot_20_9 and player:spellSlot(slot_20_9).state == 0 then
		if slot_20_5.WardSlot() and player:spellSlot(_W).state == 0 and slot_20_7.FirstSpell(_W) then
			return 1200
		else
			return 600
		end
	elseif slot_20_5.WardSlot() and player:spellSlot(_W).state == 0 and slot_20_7.FirstSpell(_W) then
		return 600
	else
		return 200
	end
end

local function slot_20_31(arg_34_0)
	if not arg_34_0 or not slot_20_5.isValid(arg_34_0) or slot_20_5.IsSpellShield(arg_34_0) or arg_34_0.buff.fioraw or arg_34_0.buff.sivire or arg_34_0.buff.sionpassivezombie then
		return false
	end

	if slot_20_4.starcombokey:get() then
		local slot_34_0 = slot_20_26(arg_34_0)
		local slot_34_1

		if slot_34_0 then
			local slot_34_2 = {
				boundingRadiusMod = 0,
				speed = 863,
				delay = 0.25,
				width = arg_34_0.boundingRadius,
				collision = {
					minion = false,
					hero = false
				}
			}
			local slot_34_3 = slot_20_1.linear.get_prediction(slot_34_2, slot_34_0, arg_34_0.path.serverPos2D)

			if slot_34_3 and slot_34_3.startPos:dist(slot_34_3.endPos) < 800 then
				local slot_34_4 = vec3(slot_34_3.endPos.x, slot_34_0.pos.y, slot_34_3.endPos.y)

				if slot_34_4 then
					return slot_34_4 + (arg_34_0.pos - slot_34_4):norm() * (slot_34_4:dist(arg_34_0.pos) + 275),
						slot_34_4
				end
			end
		end
	else
		local slot_34_5 = slot_20_25(arg_34_0) or slot_20_5.GetEnemiesObj(arg_34_0, 800)
		local slot_34_6

		if slot_34_5 then
			local slot_34_7 = {
				boundingRadiusMod = 0,
				speed = 863,
				delay = 0.25,
				width = arg_34_0.boundingRadius,
				collision = {
					minion = false,
					hero = false
				}
			}

			slot_34_6 = slot_20_1.linear.get_prediction(slot_34_7, slot_34_5, arg_34_0.path.serverPos2D)
		end

		if slot_20_4.insec.mode:get() == 1 then
			if slot_20_13 and arg_34_0.pos:distSqr(slot_20_13) <= 4000000 then
				return slot_20_13 + (arg_34_0.pos - slot_20_13):norm() * (arg_34_0.pos:dist(slot_20_13) + 275),
					slot_20_13
			elseif slot_20_5.CountAllyObj(arg_34_0, 1500) then
				local slot_34_8 = slot_20_5.CountAllyObj(arg_34_0, 1500)

				return slot_34_8.pos + (arg_34_0.pos - slot_34_8.pos):norm() * (slot_34_8.pos:dist(arg_34_0.pos) + 275),
					slot_34_8.pos
			elseif slot_20_5.GetAllyTurretPos(arg_34_0, 1800) then
				local slot_34_9 = slot_20_5.GetAllyTurretPos(arg_34_0, 1800)

				return slot_34_9 + (arg_34_0.pos - slot_34_9):norm() * (slot_34_9:dist(arg_34_0.pos) + 275), slot_34_9
			elseif slot_34_5 and slot_34_6 and slot_34_6.startPos:dist(slot_34_6.endPos) < 800 then
				if slot_34_6 and slot_34_6.endPos then
					local slot_34_10 = vec3(slot_34_6.endPos.x, slot_34_5.pos.y, slot_34_6.endPos.y)

					return slot_34_10 + (arg_34_0.pos - slot_34_10):norm() * (slot_34_10:dist(arg_34_0.pos) + 275),
						slot_34_10
				end
			elseif slot_20_4.insec.origin:get() and slot_20_14 then
				return slot_20_14 + (arg_34_0.pos - slot_20_14):norm() * (slot_20_14:dist(arg_34_0.pos) + 275),
					slot_20_14
			else
				return player.pos + (arg_34_0.pos - player.pos):norm() * (player.pos:dist(arg_34_0.pos) + 275),
					player.pos
			end
		end

		if slot_20_4.insec.mode:get() == 2 then
			if slot_20_13 and arg_34_0.pos:distSqr(slot_20_13) <= 4000000 then
				return slot_20_13 + (arg_34_0.pos - slot_20_13):norm() * (arg_34_0.pos:dist(slot_20_13) + 275),
					slot_20_13
			elseif slot_20_5.CountAllyObj(arg_34_0, 1500) then
				local slot_34_11 = slot_20_5.CountAllyObj(arg_34_0, 1500)

				return slot_34_11.pos +
				(arg_34_0.pos - slot_34_11.pos):norm() * (slot_34_11.pos:dist(arg_34_0.pos) + 275), slot_34_11.pos
			elseif slot_34_5 and slot_34_6 and slot_34_6.startPos:dist(slot_34_6.endPos) < 800 then
				if slot_34_6 and slot_34_6.endPos then
					local slot_34_12 = vec3(slot_34_6.endPos.x, slot_34_5.pos.y, slot_34_6.endPos.y)

					return slot_34_12 + (arg_34_0.pos - slot_34_12):norm() * (slot_34_12:dist(arg_34_0.pos) + 275),
						slot_34_12
				end
			elseif slot_20_5.GetAllyTurretPos(arg_34_0, 1800) then
				local slot_34_13 = slot_20_5.GetAllyTurretPos(arg_34_0, 1800)

				return slot_34_13 + (arg_34_0.pos - slot_34_13):norm() * (slot_34_13:dist(arg_34_0.pos) + 275),
					slot_34_13
			elseif slot_20_4.insec.origin:get() and slot_20_14 then
				return slot_20_14 + (arg_34_0.pos - slot_20_14):norm() * (slot_20_14:dist(arg_34_0.pos) + 275),
					slot_20_14
			else
				return player.pos + (arg_34_0.pos - player.pos):norm() * (player.pos:dist(arg_34_0.pos) + 275),
					player.pos
			end
		end

		if slot_20_4.insec.mode:get() == 3 then
			if slot_20_13 and arg_34_0.pos:distSqr(slot_20_13) <= 4000000 then
				return slot_20_13 + (arg_34_0.pos - slot_20_13):norm() * (arg_34_0.pos:dist(slot_20_13) + 275),
					slot_20_13
			elseif slot_34_5 and slot_34_6 and slot_34_6.startPos:dist(slot_34_6.endPos) < 800 then
				if slot_34_6 and slot_34_6.endPos then
					local slot_34_14 = vec3(slot_34_6.endPos.x, slot_34_5.pos.y, slot_34_6.endPos.y)

					return slot_34_14 + (arg_34_0.pos - slot_34_14):norm() * (slot_34_14:dist(arg_34_0.pos) + 275),
						slot_34_14
				end
			elseif slot_20_5.GetAllyTurretPos(arg_34_0, 1800) then
				local slot_34_15 = slot_20_5.GetAllyTurretPos(arg_34_0, 1800)

				return slot_34_15 + (arg_34_0.pos - slot_34_15):norm() * (slot_34_15:dist(arg_34_0.pos) + 275),
					slot_34_15
			elseif slot_20_5.CountAllyObj(arg_34_0, 1500) then
				local slot_34_16 = slot_20_5.CountAllyObj(arg_34_0, 1500)

				return slot_34_16.pos +
				(arg_34_0.pos - slot_34_16.pos):norm() * (slot_34_16.pos:dist(arg_34_0.pos) + 275), slot_34_16.pos
			elseif slot_20_4.insec.origin:get() and slot_20_14 then
				return slot_20_14 + (arg_34_0.pos - slot_20_14):norm() * (slot_20_14:dist(arg_34_0.pos) + 275),
					slot_20_14
			else
				return player.pos + (arg_34_0.pos - player.pos):norm() * (player.pos:dist(arg_34_0.pos) + 275),
					player.pos
			end
		end

		if slot_20_4.insec.mode:get() == 4 then
			if slot_20_13 and arg_34_0.pos:distSqr(slot_20_13) <= 4000000 then
				return slot_20_13 + (arg_34_0.pos - slot_20_13):norm() * (arg_34_0.pos:dist(slot_20_13) + 275),
					slot_20_13
			elseif slot_34_5 and slot_34_6 and slot_34_6.startPos:dist(slot_34_6.endPos) < 800 then
				if slot_34_6 and slot_34_6.endPos then
					local slot_34_17 = vec3(slot_34_6.endPos.x, slot_34_5.pos.y, slot_34_6.endPos.y)

					return slot_34_17 + (arg_34_0.pos - slot_34_17):norm() * (slot_34_17:dist(arg_34_0.pos) + 275),
						slot_34_17
				end
			elseif slot_20_5.CountAllyObj(arg_34_0, 1500) then
				local slot_34_18 = slot_20_5.CountAllyObj(arg_34_0, 1500)

				return slot_34_18.pos +
				(arg_34_0.pos - slot_34_18.pos):norm() * (slot_34_18.pos:dist(arg_34_0.pos) + 275), slot_34_18.pos
			elseif slot_20_5.GetAllyTurretPos(arg_34_0, 1800) then
				local slot_34_19 = slot_20_5.GetAllyTurretPos(arg_34_0, 1800)

				return slot_34_19 + (arg_34_0.pos - slot_34_19):norm() * (slot_34_19:dist(arg_34_0.pos) + 275),
					slot_34_19
			elseif slot_20_4.insec.origin:get() and slot_20_14 then
				return slot_20_14 + (arg_34_0.pos - slot_20_14):norm() * (slot_20_14:dist(arg_34_0.pos) + 275),
					slot_20_14
			else
				return player.pos + (arg_34_0.pos - player.pos):norm() * (player.pos:dist(arg_34_0.pos) + 275),
					player.pos
			end
		end

		if slot_20_4.insec.mode:get() == 5 then
			if slot_20_13 and arg_34_0.pos:distSqr(slot_20_13) <= 4000000 then
				return slot_20_13 + (arg_34_0.pos - slot_20_13):norm() * (arg_34_0.pos:dist(slot_20_13) + 275),
					slot_20_13
			elseif slot_20_5.GetAllyTurretPos(arg_34_0, 1800) then
				local slot_34_20 = slot_20_5.GetAllyTurretPos(arg_34_0, 1800)

				return slot_34_20 + (arg_34_0.pos - slot_34_20):norm() * (slot_34_20:dist(arg_34_0.pos) + 275),
					slot_34_20
			elseif slot_34_5 and slot_34_6 and slot_34_6.startPos:dist(slot_34_6.endPos) < 800 then
				if slot_34_6 and slot_34_6.endPos then
					local slot_34_21 = vec3(slot_34_6.endPos.x, slot_34_5.pos.y, slot_34_6.endPos.y)

					return slot_34_21 + (arg_34_0.pos - slot_34_21):norm() * (slot_34_21:dist(arg_34_0.pos) + 275),
						slot_34_21
				end
			elseif slot_20_5.CountAllyObj(arg_34_0, 1500) then
				local slot_34_22 = slot_20_5.CountAllyObj(arg_34_0, 1500)

				return slot_34_22.pos +
				(arg_34_0.pos - slot_34_22.pos):norm() * (slot_34_22.pos:dist(arg_34_0.pos) + 275), slot_34_22.pos
			elseif slot_20_4.insec.origin:get() and slot_20_14 then
				return slot_20_14 + (arg_34_0.pos - slot_20_14):norm() * (slot_20_14:dist(arg_34_0.pos) + 275),
					slot_20_14
			else
				return player.pos + (arg_34_0.pos - player.pos):norm() * (player.pos:dist(arg_34_0.pos) + 275),
					player.pos
			end
		end

		if slot_20_4.insec.mode:get() == 6 then
			if slot_20_13 and arg_34_0.pos:distSqr(slot_20_13) <= 4000000 then
				return slot_20_13 + (arg_34_0.pos - slot_20_13):norm() * (arg_34_0.pos:dist(slot_20_13) + 275),
					slot_20_13
			elseif slot_20_5.GetAllyTurretPos(arg_34_0, 1800) then
				local slot_34_23 = slot_20_5.GetAllyTurretPos(arg_34_0, 1800)

				return slot_34_23 + (arg_34_0.pos - slot_34_23):norm() * (slot_34_23:dist(arg_34_0.pos) + 275),
					slot_34_23
			elseif slot_20_5.CountAllyObj(arg_34_0, 1500) then
				local slot_34_24 = slot_20_5.CountAllyObj(arg_34_0, 1500)

				return slot_34_24.pos +
				(arg_34_0.pos - slot_34_24.pos):norm() * (slot_34_24.pos:dist(arg_34_0.pos) + 275), slot_34_24.pos
			elseif slot_34_5 and slot_34_6 and slot_34_6.startPos:dist(slot_34_6.endPos) < 800 then
				if slot_34_6 and slot_34_6.endPos then
					local slot_34_25 = vec3(slot_34_6.endPos.x, slot_34_5.pos.y, slot_34_6.endPos.y)

					return slot_34_25 + (arg_34_0.pos - slot_34_25):norm() * (slot_34_25:dist(arg_34_0.pos) + 275),
						slot_34_25
				end
			elseif slot_20_4.insec.origin:get() and slot_20_14 then
				return slot_20_14 + (arg_34_0.pos - slot_20_14):norm() * (slot_20_14:dist(arg_34_0.pos) + 275),
					slot_20_14
			else
				return player.pos + (arg_34_0.pos - player.pos):norm() * (player.pos:dist(arg_34_0.pos) + 275),
					player.pos
			end
		end
	end
end

local function slot_20_32(arg_35_0)
	if not arg_35_0 or not slot_20_5.isValid(arg_35_0) or slot_20_5.IsSpellShield(arg_35_0) or arg_35_0.buff.fioraw or arg_35_0.buff.sivire or arg_35_0.buff.sionpassivezombie then
		return false
	end

	local slot_35_0 = slot_20_27(arg_35_0)
	local slot_35_1

	if slot_35_0 then
		local slot_35_2 = {
			boundingRadiusMod = 0,
			speed = 863,
			delay = 0.25,
			width = arg_35_0.boundingRadius,
			collision = {
				minion = false,
				hero = false
			}
		}
		local slot_35_3 = slot_20_1.linear.get_prediction(slot_35_2, slot_35_0, arg_35_0.path.serverPos2D)

		if slot_35_3 and slot_35_3.startPos:dist(slot_35_3.endPos) < 800 then
			local slot_35_4 = vec3(slot_35_3.endPos.x, slot_35_0.pos.y, slot_35_3.endPos.y)

			if slot_35_4 then
				return slot_35_4 + (arg_35_0.pos - slot_35_4):norm() * (slot_35_4:dist(arg_35_0.pos) + 275), slot_35_4
			end
		end
	end
end

local function slot_20_33(arg_36_0)
	if not arg_36_0 or not slot_20_5.isValid(arg_36_0) or slot_20_5.IsSpellShield(arg_36_0) or arg_36_0.buff.fioraw or arg_36_0.buff.sivire or arg_36_0.buff.sionpassivezombie then
		return false
	end

	local slot_36_0 = {
		boundingRadiusMod = 0,
		width = 50,
		speed = 2300,
		delay = 0,
		collision = {
			minion = false,
			hero = false
		}
	}
	local slot_36_1 = slot_20_1.linear.get_prediction(slot_36_0, arg_36_0)

	if not slot_36_1 or not slot_36_1.endPos then
		return
	end

	local slot_36_2 = vec3(slot_36_1.endPos.x, arg_36_0.y, slot_36_1.endPos.y)

	if slot_20_4.starcombokey:get() then
		local slot_36_3 = slot_20_26(arg_36_0)
		local slot_36_4

		if slot_36_3 then
			local slot_36_5 = {
				boundingRadiusMod = 0,
				speed = 863,
				delay = 0.25,
				width = arg_36_0.boundingRadius,
				collision = {
					minion = false,
					hero = false
				}
			}

			slot_36_4 = slot_20_1.linear.get_prediction(slot_36_5, slot_36_3, slot_36_1.endPos)
		end

		if slot_36_4 and slot_36_4.startPos:dist(slot_36_4.endPos) < 800 then
			local slot_36_6 = vec3(slot_36_4.endPos.x, slot_36_3.pos.y, slot_36_4.endPos.y)

			if slot_36_6 then
				return slot_36_6 + (slot_36_2 - slot_36_6):norm() * (slot_36_6:dist(slot_36_2) + 275), slot_36_6
			end
		end
	else
		local slot_36_7 = slot_20_25(arg_36_0) or slot_20_5.GetEnemiesObj(arg_36_0, 800)
		local slot_36_8

		if slot_36_7 then
			local slot_36_9 = {
				boundingRadiusMod = 0,
				speed = 863,
				delay = 0.25,
				width = arg_36_0.boundingRadius,
				collision = {
					minion = false,
					hero = false
				}
			}

			slot_36_8 = slot_20_1.linear.get_prediction(slot_36_9, slot_36_7, slot_36_1.endPos)
		end

		if slot_20_4.insec.mode:get() == 1 then
			if slot_20_13 and arg_36_0.pos:distSqr(slot_20_13) <= 4000000 then
				return slot_20_13 + (slot_36_2 - slot_20_13):norm() * (slot_36_2:dist(slot_20_13) + 275), slot_20_13
			elseif slot_20_5.CountAllyObj(arg_36_0, 1500) then
				local slot_36_10 = slot_20_5.CountAllyObj(arg_36_0, 1500)

				return slot_36_10.pos + (slot_36_2 - slot_36_10.pos):norm() * (slot_36_10.pos:dist(slot_36_2) + 275),
					slot_36_10.pos
			elseif slot_20_5.GetAllyTurretPos(arg_36_0, 1800) then
				local slot_36_11 = slot_20_5.GetAllyTurretPos(arg_36_0, 1800)

				return slot_36_11 + (slot_36_2 - slot_36_11):norm() * (slot_36_11:dist(slot_36_2) + 275), slot_36_11
			elseif slot_36_7 and slot_36_8 and slot_36_8.startPos:dist(slot_36_8.endPos) < 800 then
				if slot_36_8 and slot_36_8.endPos then
					local slot_36_12 = vec3(slot_36_8.endPos.x, slot_36_7.pos.y, slot_36_8.endPos.y)

					if slot_36_12 then
						return slot_36_12 + (slot_36_2 - slot_36_12):norm() * (slot_36_12:dist(slot_36_2) + 275),
							slot_36_12
					end
				end
			elseif slot_20_4.insec.origin:get() and slot_20_14 then
				return slot_20_14 + (slot_36_2 - slot_20_14):norm() * (slot_20_14:dist(slot_36_2) + 275), slot_20_14
			else
				return player.pos + (slot_36_2 - player.pos):norm() * (player.pos:dist(slot_36_2) + 275), player.pos
			end
		end

		if slot_20_4.insec.mode:get() == 2 then
			if slot_20_13 and arg_36_0.pos:distSqr(slot_20_13) <= 4000000 then
				return slot_20_13 + (slot_36_2 - slot_20_13):norm() * (slot_36_2:dist(slot_20_13) + 275), slot_20_13
			elseif slot_20_5.CountAllyObj(arg_36_0, 1500) then
				local slot_36_13 = slot_20_5.CountAllyObj(arg_36_0, 1500)

				return slot_36_13.pos + (slot_36_2 - slot_36_13.pos):norm() * (slot_36_13.pos:dist(slot_36_2) + 275),
					slot_36_13.pos
			elseif slot_36_7 and slot_36_8 and slot_36_8.startPos:dist(slot_36_8.endPos) < 800 then
				if slot_36_8 and slot_36_8.endPos then
					local slot_36_14 = vec3(slot_36_8.endPos.x, slot_36_7.pos.y, slot_36_8.endPos.y)

					if slot_36_14 then
						return slot_36_14 + (slot_36_2 - slot_36_14):norm() * (slot_36_14:dist(slot_36_2) + 275),
							slot_36_14
					end
				end
			elseif slot_20_5.GetAllyTurretPos(arg_36_0, 1800) then
				local slot_36_15 = slot_20_5.GetAllyTurretPos(arg_36_0, 1800)

				return slot_36_15 + (slot_36_2 - slot_36_15):norm() * (slot_36_15:dist(slot_36_2) + 275), slot_36_15
			elseif slot_20_4.insec.origin:get() and slot_20_14 then
				return slot_20_14 + (slot_36_2 - slot_20_14):norm() * (slot_20_14:dist(slot_36_2) + 275), slot_20_14
			else
				return player.pos + (slot_36_2 - player.pos):norm() * (player.pos:dist(slot_36_2) + 275), player.pos
			end
		end

		if slot_20_4.insec.mode:get() == 3 then
			if slot_20_13 and arg_36_0.pos:distSqr(slot_20_13) <= 4000000 then
				return slot_20_13 + (slot_36_2 - slot_20_13):norm() * (slot_36_2:dist(slot_20_13) + 275), slot_20_13
			elseif slot_36_7 and slot_36_8 and slot_36_8.startPos:dist(slot_36_8.endPos) < 800 then
				if slot_36_8 and slot_36_8.endPos then
					local slot_36_16 = vec3(slot_36_8.endPos.x, slot_36_7.pos.y, slot_36_8.endPos.y)

					if slot_36_16 then
						return slot_36_16 + (slot_36_2 - slot_36_16):norm() * (slot_36_16:dist(slot_36_2) + 275),
							slot_36_16
					end
				end
			elseif slot_20_5.GetAllyTurretPos(arg_36_0, 1800) then
				local slot_36_17 = slot_20_5.GetAllyTurretPos(arg_36_0, 1800)

				return slot_36_17 + (slot_36_2 - slot_36_17):norm() * (slot_36_17:dist(slot_36_2) + 275), slot_36_17
			elseif slot_20_5.CountAllyObj(arg_36_0, 1500) then
				local slot_36_18 = slot_20_5.CountAllyObj(arg_36_0, 1500)

				return slot_36_18.pos + (slot_36_2 - slot_36_18.pos):norm() * (slot_36_18.pos:dist(slot_36_2) + 275),
					slot_36_18.pos
			elseif slot_20_4.insec.origin:get() and slot_20_14 then
				return slot_20_14 + (slot_36_2 - slot_20_14):norm() * (slot_20_14:dist(slot_36_2) + 275), slot_20_14
			else
				return player.pos + (slot_36_2 - player.pos):norm() * (player.pos:dist(slot_36_2) + 275), player.pos
			end
		end

		if slot_20_4.insec.mode:get() == 4 then
			if slot_20_13 and arg_36_0.pos:distSqr(slot_20_13) <= 4000000 then
				return slot_20_13 + (slot_36_2 - slot_20_13):norm() * (slot_36_2:dist(slot_20_13) + 275), slot_20_13
			elseif slot_36_7 and slot_36_8 and slot_36_8.startPos:dist(slot_36_8.endPos) < 800 then
				if slot_36_8 and slot_36_8.endPos then
					local slot_36_19 = vec3(slot_36_8.endPos.x, slot_36_7.pos.y, slot_36_8.endPos.y)

					if slot_36_19 then
						return slot_36_19 + (slot_36_2 - slot_36_19):norm() * (slot_36_19:dist(slot_36_2) + 275),
							slot_36_19
					end
				end
			elseif slot_20_5.CountAllyObj(arg_36_0, 1500) then
				local slot_36_20 = slot_20_5.CountAllyObj(arg_36_0, 1500)

				return slot_36_20.pos + (slot_36_2 - slot_36_20.pos):norm() * (slot_36_20.pos:dist(slot_36_2) + 275),
					slot_36_20.pos
			elseif slot_20_5.GetAllyTurretPos(arg_36_0, 1800) then
				local slot_36_21 = slot_20_5.GetAllyTurretPos(arg_36_0, 1800)

				return slot_36_21 + (slot_36_2 - slot_36_21):norm() * (slot_36_21:dist(slot_36_2) + 275), slot_36_21
			elseif slot_20_4.insec.origin:get() and slot_20_14 then
				return slot_20_14 + (slot_36_2 - slot_20_14):norm() * (slot_20_14:dist(slot_36_2) + 275), slot_20_14
			else
				return player.pos + (slot_36_2 - player.pos):norm() * (player.pos:dist(slot_36_2) + 275), player.pos
			end
		end

		if slot_20_4.insec.mode:get() == 5 then
			if slot_20_13 and arg_36_0.pos:distSqr(slot_20_13) <= 4000000 then
				return slot_20_13 + (slot_36_2 - slot_20_13):norm() * (slot_36_2:dist(slot_20_13) + 275), slot_20_13
			elseif slot_20_5.GetAllyTurretPos(arg_36_0, 1800) then
				local slot_36_22 = slot_20_5.GetAllyTurretPos(arg_36_0, 1800)

				return slot_36_22 + (slot_36_2 - slot_36_22):norm() * (slot_36_22:dist(slot_36_2) + 275), slot_36_22
			elseif slot_36_7 and slot_36_8 and slot_36_8.startPos:dist(slot_36_8.endPos) < 800 then
				if slot_36_8 and slot_36_8.endPos then
					local slot_36_23 = vec3(slot_36_8.endPos.x, slot_36_7.pos.y, slot_36_8.endPos.y)

					if slot_36_23 then
						return slot_36_23 + (slot_36_2 - slot_36_23):norm() * (slot_36_23:dist(slot_36_2) + 275),
							slot_36_23
					end
				end
			elseif slot_20_5.CountAllyObj(arg_36_0, 1500) then
				local slot_36_24 = slot_20_5.CountAllyObj(arg_36_0, 1500)

				return slot_36_24.pos + (slot_36_2 - slot_36_24.pos):norm() * (slot_36_24.pos:dist(slot_36_2) + 275),
					slot_36_24.pos
			elseif slot_20_4.insec.origin:get() and slot_20_14 then
				return slot_20_14 + (slot_36_2 - slot_20_14):norm() * (slot_20_14:dist(slot_36_2) + 275), slot_20_14
			else
				return player.pos + (slot_36_2 - player.pos):norm() * (player.pos:dist(slot_36_2) + 275), player.pos
			end
		end

		if slot_20_4.insec.mode:get() == 6 then
			if slot_20_13 and arg_36_0.pos:distSqr(slot_20_13) <= 4000000 then
				return slot_20_13 + (slot_36_2 - slot_20_13):norm() * (slot_36_2:dist(slot_20_13) + 275), slot_20_13
			elseif slot_20_5.GetAllyTurretPos(arg_36_0, 1800) then
				local slot_36_25 = slot_20_5.GetAllyTurretPos(arg_36_0, 1800)

				return slot_36_25 + (slot_36_2 - slot_36_25):norm() * (slot_36_25:dist(slot_36_2) + 275), slot_36_25
			elseif slot_20_5.CountAllyObj(arg_36_0, 1500) then
				local slot_36_26 = slot_20_5.CountAllyObj(arg_36_0, 1500)

				return slot_36_26.pos + (slot_36_2 - slot_36_26.pos):norm() * (slot_36_26.pos:dist(slot_36_2) + 275),
					slot_36_26.pos
			elseif slot_36_7 and slot_36_8 and slot_36_8.startPos:dist(slot_36_8.endPos) < 800 then
				if slot_36_8 and slot_36_8.endPos then
					local slot_36_27 = vec3(slot_36_8.endPos.x, slot_36_7.pos.y, slot_36_8.endPos.y)

					if slot_36_27 then
						return slot_36_27 + (slot_36_2 - slot_36_27):norm() * (slot_36_27:dist(slot_36_2) + 275),
							slot_36_27
					end
				end
			elseif slot_20_4.insec.origin:get() and slot_20_14 then
				return slot_20_14 + (slot_36_2 - slot_20_14):norm() * (slot_20_14:dist(slot_36_2) + 275), slot_20_14
			else
				return player.pos + (slot_36_2 - player.pos):norm() * (player.pos:dist(slot_36_2) + 275), player.pos
			end
		end
	end
end

local function slot_20_34(arg_37_0, arg_37_1, arg_37_2)
	if arg_37_1 then
		local slot_37_0 = slot_20_33(arg_37_1)

		if slot_37_0 and not slot_20_6.iswall(vec3(slot_37_0.x, slot_37_0.y, slot_37_0.z)) and player.pos:dist(slot_37_0) <= slot_20_28() then
			arg_37_0.obj = arg_37_1

			return true
		elseif slot_37_0 and not slot_20_6.iswall(vec3(slot_37_0.x, slot_37_0.y, slot_37_0.z)) and player.pos:dist(slot_37_0) <= slot_20_30() then
			arg_37_0.obj = arg_37_1

			return true
		elseif player:spellSlot(3).state == 0 and arg_37_2 and arg_37_2 <= 375 and slot_37_0 and not slot_20_6.iswall(vec3(slot_37_0.x, slot_37_0.y, slot_37_0.z)) then
			arg_37_0.obj = arg_37_1

			return true
		elseif player:spellSlot(0).state == 0 and not slot_20_7.FirstSpell(0) and slot_20_22() then
			if slot_20_22() and slot_37_0 and not slot_20_6.iswall(vec3(slot_37_0.x, slot_37_0.y, slot_37_0.z)) and slot_20_22().pos:dist(slot_37_0) <= slot_20_30() - 200 then
				arg_37_0.obj = arg_37_1

				return true
			elseif arg_37_1.buff.leesinqone and arg_37_1.buff.leesinqone.source == player and arg_37_2 and arg_37_2 <= 1400 then
				arg_37_0.obj = arg_37_1

				return true
			end
		elseif player:spellSlot(0).state == 0 and slot_20_7.FirstSpell(0) and not slot_20_22() and slot_37_0 and not slot_20_6.iswall(vec3(slot_37_0.x, slot_37_0.y, slot_37_0.z)) then
			local slot_37_1 = slot_20_1.linear.get_prediction(slot_20_21, arg_37_1)

			if slot_37_1 and slot_37_1.endPos and slot_37_1.startPos:distSqr(slot_37_1.endPos) < (slot_20_5.GetEnemiesObj(player, 1200) and 1200 or 1200 + slot_20_30() - 200) ^ 2 and arg_37_2 and arg_37_2 <= (slot_20_5.GetEnemiesObj(player, 1200) and 1200 or 1200 + slot_20_30() - 200) ^ 2 then
				arg_37_0.obj = arg_37_1

				return true
			end
		end
	end
end

local function slot_20_35()
	return slot_20_0.ts.get_result(slot_20_34).obj
end

local function slot_20_36(arg_39_0, arg_39_1, arg_39_2)
	if arg_39_1 and arg_39_2 and arg_39_2 <= 375 then
		arg_39_0.obj = arg_39_1

		return true
	end
end

local function slot_20_37()
	return slot_20_0.ts.get_result(slot_20_36).obj
end

local function slot_20_38()
	local slot_41_0 = {}

	for iter_41_0 = 0, objManager.enemies_n - 1 do
		local slot_41_1 = objManager.enemies[iter_41_0]

		if slot_41_1 and slot_20_5.isValid(slot_41_1) and slot_41_1.team ~= TEAM_ALLY then
			local slot_41_2 = player.pos:dist(slot_41_1.pos)
			local slot_41_3 = slot_20_33(slot_41_1)

			if slot_41_3 and not slot_20_6.iswall(vec3(slot_41_3.x, slot_41_3.y, slot_41_3.z)) and player.pos:dist(slot_41_3) <= slot_20_28() then
				slot_41_0[#slot_41_0 + 1] = slot_41_1
			elseif slot_41_3 and not slot_20_6.iswall(vec3(slot_41_3.x, slot_41_3.y, slot_41_3.z)) and player.pos:dist(slot_41_3) <= slot_20_29() then
				slot_41_0[#slot_41_0 + 1] = slot_41_1
			elseif player:spellSlot(3).state == 0 and slot_41_2 and slot_41_2 <= 375 and slot_41_3 and not slot_20_6.iswall(vec3(slot_41_3.x, slot_41_3.y, slot_41_3.z)) then
				slot_41_0[#slot_41_0 + 1] = slot_41_1
			elseif player:spellSlot(0).state == 0 and not slot_20_7.FirstSpell(0) and slot_20_22() then
				if slot_20_22() and slot_41_3 and not slot_20_6.iswall(vec3(slot_41_3.x, slot_41_3.y, slot_41_3.z)) and slot_20_22().pos:dist(slot_41_3) <= slot_20_29() - 200 then
					return slot_41_1
				elseif slot_41_1.buff.leesinqone and slot_41_1.buff.leesinqone.source == player and slot_41_2 and slot_41_2 <= 1400 then
					return slot_41_1
				end
			elseif player:spellSlot(0).state == 0 and slot_20_7.FirstSpell(0) and not slot_20_22() and slot_41_3 and not slot_20_6.iswall(vec3(slot_41_3.x, slot_41_3.y, slot_41_3.z)) then
				local slot_41_4 = slot_20_1.linear.get_prediction(slot_20_21, slot_41_1)

				if slot_41_4 and slot_41_4.endPos and slot_41_4.startPos:distSqr(slot_41_4.endPos) < (slot_20_5.GetEnemiesObj(player, 1200) and 1200 or 1200 + slot_20_29() - 200) ^ 2 and slot_41_2 and slot_41_2 <= (slot_20_5.GetEnemiesObj(player, 1200) and 1200 or 1200 + slot_20_29() - 200) ^ 2 then
					slot_41_0[#slot_41_0 + 1] = slot_41_1
				end
			end
		end
	end

	table.sort(slot_41_0, function(arg_42_0, arg_42_1)
		if arg_42_0 and slot_20_5.isValid(arg_42_0) and arg_42_1 and slot_20_5.isValid(arg_42_1) then
			return arg_42_0.health > arg_42_1.health
		end
	end)

	if slot_41_0[1] then
		return slot_41_0[1]
	end
end

local function slot_20_39(arg_43_0)
	if not arg_43_0 and slot_20_0.core.is_move_paused() then
		slot_20_0.core.set_pause_move(0)
	elseif arg_43_0 then
		slot_20_0.core.set_pause_move(player:basicAttack(0).clientAnimationTime)
		player:move(vec3(arg_43_0))
	end
end

local function slot_20_40(arg_44_0)
	if player:spellSlot(0).state ~= 0 or not arg_44_0 or not slot_20_5.isValid(arg_44_0) or not slot_20_7.FirstSpell(0) or game.time < slot_20_15 + 0.5 then
		return false
	end

	local slot_44_0 = {
		boundingRadiusMod = 0,
		range = 1200,
		width = 61,
		speed = 1800,
		delay = 0.25,
		collision = {
			minion = true,
			hero = false
		}
	}
	local slot_44_1 = slot_20_1.linear.get_prediction(slot_44_0, arg_44_0)

	if slot_44_1 and slot_44_1.endPos and slot_44_1.startPos:distSqr(slot_44_1.endPos) < 1440000 and not slot_20_5.wall_check(player, vec3(slot_44_1.endPos.x, arg_44_0.y, slot_44_1.endPos.y)) then
		if arg_44_0.type == TYPE_HERO and not slot_20_7.trace_filter(slot_20_21, slot_44_1, arg_44_0) then
			return
		end

		local slot_44_2 = slot_20_1.collision.get_prediction(slot_44_0, slot_44_1, arg_44_0)

		if not slot_44_2 then
			player:castSpell("pos", 0, vec3(slot_44_1.endPos.x, 0, slot_44_1.endPos.y))

			slot_20_10 = game.time + slot_44_1.startPos:dist(slot_44_1.endPos) / 1800 + 0.25
		elseif slot_20_19 and player:spellSlot(slot_20_19).state == 0 and slot_20_4.misc.cjq1:get() and player:spellSlot(slot_20_19).stacks > slot_20_4.misc.cjstack:get() and arg_44_0.type == TYPE_HERO and #slot_44_2 == 1 and slot_44_2[1] and slot_44_2[1].pos:distSqr(player.pos) <= 313600 and slot_44_2[1].health <= slot_20_20[player.levelRef] then
			if slot_20_3 then
				slot_20_3.core.set_pause(0.25)
				player:castSpell("obj", slot_20_19, slot_44_2[1])
				player:castSpell("pos", 0, vec3(slot_44_1.endPos.x, 0, slot_44_1.endPos.y))
			else
				player:castSpell("obj", slot_20_19, slot_44_2[1])
				player:castSpell("pos", 0, vec3(slot_44_1.endPos.x, 0, slot_44_1.endPos.y))
			end
		end
	end
end

local function slot_20_41(arg_45_0, arg_45_1)
	if player:spellSlot(0).state ~= 0 or not arg_45_0 or not slot_20_5.isValid(arg_45_0) or not arg_45_1 then
		return false
	end

	local slot_45_0 = {
		boundingRadiusMod = 0,
		width = 61,
		speed = 1800,
		delay = 0.25,
		collision = {
			minion = true,
			hero = true
		}
	}
	local slot_45_1 = slot_20_1.linear.get_prediction(slot_45_0, arg_45_0)

	if slot_45_1 and slot_45_1.endPos and not slot_20_5.wall_check(player, vec3(slot_45_1.endPos.x, arg_45_0.y, slot_45_1.endPos.y)) then
		local slot_45_2 = slot_20_1.collision.get_prediction(slot_45_0, slot_45_1, arg_45_0)

		if not slot_45_2 and slot_45_1.startPos:distSqr(slot_45_1.endPos) < 1440000 and slot_20_7.trace_filter(slot_20_21, slot_45_1, arg_45_0) then
			player:castSpell("pos", 0, vec3(slot_45_1.endPos.x, 0, slot_45_1.endPos.y))
		elseif slot_45_1 and slot_45_1.endPos and slot_45_2 and slot_20_19 and player:spellSlot(slot_20_19).state == 0 and #slot_45_2 == 1 and slot_45_1.startPos:distSqr(slot_45_1.endPos) < 1440000 and slot_20_7.trace_filter(slot_20_21, slot_45_1, arg_45_0) and slot_20_4.misc.cjq1:get() and player:spellSlot(slot_20_19).stacks > slot_20_4.misc.cjstack:get() then
			if slot_45_2[1] and slot_45_2[1].type == TYPE_MINION and slot_45_2[1].pos:distSqr(player.pos) <= 313600 and slot_45_2[1].health <= slot_20_20[player.levelRef] then
				if slot_20_3 then
					slot_20_3.core.set_pause(0.25)
					player:castSpell("obj", slot_20_19, slot_45_2[1])
					player:castSpell("pos", 0, vec3(slot_45_1.endPos.x, 0, slot_45_1.endPos.y))
				else
					player:castSpell("obj", slot_20_19, slot_45_2[1])
					player:castSpell("pos", 0, vec3(slot_45_1.endPos.x, 0, slot_45_1.endPos.y))
				end
			end
		elseif slot_45_1 and slot_45_1.endPos and slot_45_2 and slot_20_33(arg_45_0) and player.path.serverPos:distSqr(slot_20_33(arg_45_0)) > slot_20_30() ^ 2 then
			local slot_45_3 = slot_45_2[1]

			if slot_45_3 and not slot_45_3.isDead and slot_45_3.isTargetable and slot_45_3.isVisible and slot_45_3.path.serverPos:distSqr(player.pos) < 1440000 and slot_45_3.path.serverPos:distSqr(player.path.serverPos) > 640000 and slot_45_3.path.serverPos:distSqr(arg_45_1) <= (slot_20_30() - 300) ^ 2 and not slot_20_5.WardName(slot_45_3) and slot_45_3.health > slot_20_7.Q1Dmg(slot_45_3) then
				player:castSpell("pos", 0, vec3(slot_45_1.endPos.x, 0, slot_45_1.endPos.y))
			end
		end
	end
end

local function slot_20_42(arg_46_0)
	if player:spellSlot(0).state ~= 0 or not arg_46_0 or not slot_20_5.isValid(arg_46_0) or not slot_20_7.FirstSpell(0) or game.time < slot_20_15 + 0.5 then
		return false
	end

	local slot_46_0 = {
		boundingRadiusMod = 0,
		range = 1200,
		width = 61,
		speed = 1800,
		delay = 0.25,
		collision = {
			minion = true,
			hero = true
		}
	}
	local slot_46_1 = slot_20_1.linear.get_prediction(slot_46_0, arg_46_0)

	if slot_46_1 and slot_46_1.endPos and slot_46_1.startPos:distSqr(slot_46_1.endPos) < 1440000 and not slot_20_5.wall_check(player, vec3(slot_46_1.endPos.x, arg_46_0.y, slot_46_1.endPos.y)) then
		if arg_46_0.type == TYPE_HERO and not slot_20_7.trace_filter(slot_20_21, slot_46_1, arg_46_0) then
			return
		end

		local slot_46_2 = slot_20_1.collision.get_prediction(slot_46_0, slot_46_1, arg_46_0)

		if not slot_46_2 then
			player:castSpell("pos", 0, vec3(slot_46_1.endPos.x, 0, slot_46_1.endPos.y))
		elseif slot_20_19 and player:spellSlot(slot_20_19).state == 0 and slot_20_4.misc.cjq1:get() and player:spellSlot(slot_20_19).stacks > slot_20_4.misc.cjstack:get() and arg_46_0.type == TYPE_HERO and #slot_46_2 == 1 and slot_46_2[1] and slot_46_2[1].type ~= TYPE_HERO and slot_46_2[1].pos:distSqr(player.pos) <= 313600 and slot_46_2[1].health <= slot_20_20[player.levelRef] then
			if slot_20_3 then
				slot_20_3.core.set_pause(0.25)
				player:castSpell("obj", slot_20_19, slot_46_2[1])
				player:castSpell("pos", 0, vec3(slot_46_1.endPos.x, 0, slot_46_1.endPos.y))
			else
				player:castSpell("obj", slot_20_19, slot_46_2[1])
				player:castSpell("pos", 0, vec3(slot_46_1.endPos.x, 0, slot_46_1.endPos.y))
			end
		end
	end
end

local function slot_20_43(arg_47_0)
	if player:spellSlot(3).state ~= 0 or not arg_47_0 or not slot_20_5.isValid(arg_47_0) or game.time < slot_20_15 + 0.5 then
		return false
	end

	local slot_47_0 = {
		boundingRadiusMod = 0,
		speed = 1000,
		delay = 0.25,
		width = arg_47_0.boundingRadius,
		collision = {
			minion = false,
			hero = true
		}
	}
	local slot_47_1 = slot_20_1.linear.get_prediction(slot_47_0, arg_47_0)
	local slot_47_2 = objManager.minions

	if slot_47_1 and slot_47_1.endPos and slot_47_1.startPos:dist(slot_47_1.endPos) < 800 and slot_20_7.trace_filterR(slot_47_0, slot_47_1, arg_47_0) then
		local slot_47_3 = vec3(player.path.serverPos.x, player.path.serverPos.y, player.path.serverPos.z)
		local slot_47_4 = vec3(slot_47_1.endPos.x, arg_47_0.y, slot_47_1.endPos.y)

		for iter_47_0 = 0, objManager.enemies_n - 1 do
			local slot_47_5 = objManager.enemies[iter_47_0]

			if slot_47_5 and slot_20_5.isValid(slot_47_5) and slot_47_5 ~= arg_47_0 and slot_47_5.team ~= TEAM_ALLY and slot_47_5.pos:distSqr(player.pos) < 140625 and slot_47_3 and slot_47_3:dist(slot_47_4) > slot_47_5.path.serverPos:dist(slot_47_4) and slot_47_3:dist(slot_47_4) > slot_47_3:dist(slot_47_5.path.serverPos) and math.abs(mathf.dist_line_vector(slot_47_3, slot_47_4, slot_47_5.path.serverPos)) <= slot_47_5.boundingRadius then
				player:castSpell("obj", 3, slot_47_5)

				slot_20_17 = game.time
			end
		end
	end
end

local function slot_20_44()
	if not slot_20_8 or not slot_20_5.isValid(slot_20_8) then
		return
	end

	local slot_48_0 = player.path.serverPos:distSqr(slot_20_8.path.serverPos)

	if player:spellSlot(0).state == 0 then
		if slot_20_4.combo.comboq1:get() and slot_20_7.FirstSpell(0) and (slot_20_0.core.is_attack_paused() or not slot_20_0.core.can_attack() and slot_20_0.core.can_action() or slot_48_0 > (player.attackRange + player.boundingRadius + slot_20_8.boundingRadius) ^ 2) then
			slot_20_40(slot_20_8)
		elseif slot_20_4.combo.comboq2:get() and not slot_20_7.FirstSpell(0) and not slot_20_8.buff.leesinrroot and game.time > slot_20_17 + 0.4 and (not slot_20_8.buff.leesinr or slot_20_8.buff.leesinr and game.time > slot_20_8.buff.leesinr.startTime + 0.4 - network.latency) and slot_20_8.buff.leesinqone and (not slot_20_5.UnderTurret(slot_20_8) or slot_20_5.UnderTurret(player) or slot_20_4.turretq2:get()) and (player:spellSlot(3).state ~= 0 or not slot_20_4.combo.combor:get() or slot_20_8.health + slot_20_8.physicalShield > slot_20_7.Q2Dmg(slot_20_8, slot_20_7.RDmg(slot_20_8), 0) or slot_48_0 > 140625) and (slot_20_12 == 0 and (slot_20_0.core.is_attack_paused() or not slot_20_0.core.can_attack() and slot_20_0.core.can_action()) or game.time >= slot_20_8.buff.leesinqone.startTime + (slot_20_8.buff.leesinqone.endTime - slot_20_8.buff.leesinqone.startTime) - 0.5 or slot_48_0 > (player.attackRange + player.boundingRadius + 100) ^ 2 or slot_20_8.health + slot_20_8.physicalShield <= slot_20_7.Q2Dmg(slot_20_8, 0)) and (slot_20_4.combo.combowaitq2:get() and game.time >= slot_20_8.buff.leesinqone.endTime - 0.5 or not slot_20_4.combo.combowaitq2:get() or slot_20_8.health + slot_20_8.physicalShield <= slot_20_7.Q2Dmg(slot_20_8, 0)) then
			player:castSpell("self", 0)
		end
	end

	if player:spellSlot(3).state == 0 and slot_20_4.combo.combor:get() and slot_48_0 <= 140625 then
		if slot_20_4.combo.comboq1:get() and player:spellSlot(0).state == 0 and not slot_20_7.FirstSpell(0) and slot_20_8.buff.leesinqone and slot_20_8.health + slot_20_8.physicalShield <= slot_20_7.Q2Dmg(slot_20_8, slot_20_7.RDmg(slot_20_8)) and player.mana >= 30 then
			player:castSpell("obj", 3, slot_20_8)

			slot_20_17 = game.time

			slot_20_5.DelayAction(function()
				player:castSpell("self", 0)
			end, 0.35)
		elseif slot_20_8.health + slot_20_8.physicalShield <= slot_20_7.RDmg(slot_20_8) then
			player:castSpell("obj", 3, slot_20_8)

			slot_20_17 = game.time
		end
	end

	if player:spellSlot(1).state == 0 and not slot_20_8.buff.leesinrroot and game.time > slot_20_17 + 0.4 and (not slot_20_8.buff.leesinr or slot_20_8.buff.leesinr and game.time > slot_20_8.buff.leesinr.startTime + 0.4 - network.latency) then
		if slot_20_7.FirstSpell(1) then
			if slot_20_4.combo.combowpmode:get() < 3 and (slot_20_4.combo.combowpmode:get() == 2 or not slot_20_5.WardSlot()) and slot_20_12 == 0 and slot_48_0 <= (player.attackRange + player.boundingRadius + slot_20_8.boundingRadius) ^ 2 then
				player:castSpell("obj", 1, player)
			elseif slot_20_4.killsteal.killstealw:get() and not player.buff.leesinqtwodash and not slot_20_5.UnderTurret(slot_20_8) and slot_20_8.health <= slot_20_7.AADmg(slot_20_8) * 3 + slot_20_7.EDmg(slot_20_8) and player.mana >= 80 and slot_48_0 <= (600 + player.attackRange + player.boundingRadius) ^ 2 and slot_48_0 > (player.attackRange + player.boundingRadius + slot_20_8.boundingRadius) ^ 2 and (not slot_20_4.combo.comboq1:get() or player:spellSlot(0).state ~= 0) then
				slot_20_7.WardJump(slot_20_8.pos)
			end
		elseif slot_20_4.combo.combow2:get() and slot_20_12 <= slot_20_4.misc.passive:get() and (slot_20_0.core.is_attack_paused() or not slot_20_0.core.can_attack() and slot_20_0.core.can_action()) and slot_48_0 <= (player.attackRange + player.boundingRadius + slot_20_8.boundingRadius) ^ 2 then
			player:castSpell("self", 1)
		end
	end

	if slot_20_4.combo.comboe:get() and player:spellSlot(2).state == 0 and not slot_20_8.buff.leesinrroot and game.time > slot_20_17 + 0.4 and (not slot_20_8.buff.leesinr or slot_20_8.buff.leesinr and game.time > slot_20_8.buff.leesinr.startTime + 0.4 - network.latency) then
		if slot_20_7.FirstSpell(2) and (slot_20_12 <= slot_20_4.misc.passive:get() or slot_20_8.buff.leesinqone and slot_20_12 < 2) and (slot_20_0.core.is_attack_paused() or not slot_20_0.core.can_attack() and slot_20_0.core.can_action() or slot_48_0 > (player.attackRange + player.boundingRadius + slot_20_8.boundingRadius) ^ 2) and slot_48_0 <= 180625 then
			player:castSpell("self", 2)
		elseif not slot_20_7.FirstSpell(2) and (slot_20_12 == 0 or not slot_20_0.combat.target and slot_20_12 <= slot_20_4.misc.passive:get()) and (slot_20_0.core.is_attack_paused() or not slot_20_0.core.can_attack() and slot_20_0.core.can_action()) then
			player:castSpell("self", 2)
		end
	end
end

local function slot_20_45()
	player:move(mousePos)

	if not slot_20_8 or not slot_20_5.isValid(slot_20_8) then
		return
	end

	if player:spellSlot(0).state == 0 then
		if player.pos:distSqr(slot_20_8) < 722500 and player.pos:distSqr(slot_20_8) > (player.attackRange + player.boundingRadius + slot_20_8.boundingRadius) ^ 2 and player.pos:dist(slot_20_8) > 350 and player:spellSlot(3).state == 0 and player:spellSlot(1).state == 0 then
			slot_20_7.WardJump(slot_20_8.pos)
		end

		if player:spellSlot(0).state == 0 and player.pos:distSqr(slot_20_8) < 864900 then
			slot_20_40(slot_20_8)
		end

		if player:spellSlot(3).state == 0 and player.pos:distSqr(slot_20_8) < 140625 then
			player:castSpell("obj", 3, slot_20_8)
		end

		if player:spellSlot(3).state ~= 0 and not player.buff.leesinqtwodash then
			slot_20_5.DelayAction(function()
				player:castSpell("self", 0)
			end, 0.35)
		end
	end
end

local function slot_20_46()
	if not slot_20_8 or not slot_20_5.isValid(slot_20_8) then
		return
	end

	local slot_52_0 = player.path.serverPos:distSqr(slot_20_8.path.serverPos)

	if player:spellSlot(0).state == 0 then
		if slot_20_4.harass.harassq1:get() and slot_20_7.FirstSpell(0) and (slot_20_0.core.is_attack_paused() or not slot_20_0.core.can_attack() and slot_20_0.core.can_action() or slot_52_0 > (player.attackRange + player.boundingRadius + slot_20_8.boundingRadius) ^ 2) then
			slot_20_40(slot_20_8)
		elseif (slot_20_4.harass.harassq2:get() or player.pos:dist(slot_20_8) <= player.attackRange + player.boundingRadius + slot_20_8.boundingRadius) and not slot_20_7.FirstSpell(0) and slot_20_8.buff.leesinqone and (not slot_20_5.UnderTurret(slot_20_8) or slot_20_5.UnderTurret(player) or slot_20_4.turretq2:get()) and (slot_20_12 == 0 and (slot_20_0.core.is_attack_paused() or not slot_20_0.core.can_attack() and slot_20_0.core.can_action()) or game.time >= slot_20_8.buff.leesinqone.startTime + (slot_20_8.buff.leesinqone.endTime - slot_20_8.buff.leesinqone.startTime) - 0.5 or slot_52_0 > (player.attackRange + player.boundingRadius + 100) ^ 2 or slot_20_8.health + slot_20_8.physicalShield <= slot_20_7.Q2Dmg(slot_20_8, 0)) and (slot_20_4.harass.harasswaitq2:get() and game.time >= slot_20_8.buff.leesinqone.endTime - 0.5 or not slot_20_4.harass.harasswaitq2:get()) then
			player:castSpell("self", 0)
		end
	end

	if player:spellSlot(1).state == 0 and not slot_20_8.buff.leesinrroot and game.time > slot_20_17 + 0.4 and (not slot_20_8.buff.leesinr or slot_20_8.buff.leesinr and game.time > slot_20_8.buff.leesinr.startTime + 0.4 - network.latency) then
		if slot_20_7.FirstSpell(1) then
			if slot_20_4.harass.harassw1:get() and slot_20_12 == 0 and slot_52_0 <= (player.attackRange + player.boundingRadius + slot_20_8.boundingRadius) ^ 2 then
				player:castSpell("obj", 1, player)
			end
		elseif slot_20_4.harass.harassw2:get() and slot_20_12 <= slot_20_4.misc.passive:get() and (slot_20_0.core.is_attack_paused() or not slot_20_0.core.can_attack() and slot_20_0.core.can_action()) and slot_52_0 <= (player.attackRange + player.boundingRadius + slot_20_8.boundingRadius) ^ 2 then
			player:castSpell("self", 1)
		end
	end

	if slot_20_4.harass.harasse:get() and player:spellSlot(2).state == 0 and not slot_20_8.buff.leesinrroot and game.time > slot_20_17 + 0.4 and (not slot_20_8.buff.leesinr or slot_20_8.buff.leesinr and game.time > slot_20_8.buff.leesinr.startTime + 0.4 - network.latency) then
		if slot_20_7.FirstSpell(2) and (slot_20_12 <= slot_20_4.misc.passive:get() or slot_20_8.buff.leesinqone and slot_20_12 < 2) and (slot_20_0.core.is_attack_paused() or not slot_20_0.core.can_attack() and slot_20_0.core.can_action() or slot_52_0 > (player.attackRange + player.boundingRadius + slot_20_8.boundingRadius) ^ 2) and slot_52_0 <= 180625 then
			player:castSpell("self", 2)
		elseif not slot_20_7.FirstSpell(2) and (slot_20_12 == 0 or not slot_20_0.combat.target and slot_20_12 <= slot_20_4.misc.passive:get()) and (slot_20_0.core.is_attack_paused() or not slot_20_0.core.can_attack() and slot_20_0.core.can_action()) then
			player:castSpell("self", 2)
		end
	end
end

local function slot_20_47()
	local slot_53_0 = objManager.minions

	for iter_53_0 = 0, slot_53_0.size[TEAM_NEUTRAL] - 1 do
		local slot_53_1 = slot_53_0[TEAM_NEUTRAL][iter_53_0]

		if slot_53_1 and not slot_53_1.isDead and slot_53_1.isVisible and slot_53_1.isTargetable and slot_53_1.pos:distSqr(player.pos) < 1960000 and not slot_20_5.WardName(slot_53_1) then
			if player.levelRef >= slot_20_4.jungleclear.passive:get() then
				if slot_20_4.jungleclear.jungleclearq:get() and game.time > slot_20_10 + network.latency and player:spellSlot(0).state == 0 and not slot_20_7.FirstSpell(0) and slot_53_1.buff.leesinqone and (slot_20_12 == 0 and (slot_20_0.core.is_attack_paused() or not slot_20_0.core.can_attack() and slot_20_0.core.can_action()) or game.time >= slot_53_1.buff.leesinqone.startTime + (slot_53_1.buff.leesinqone.endTime - slot_53_1.buff.leesinqone.startTime) - 0.5 or slot_53_1.pos:distSqr(player.pos) > (player.attackRange + player.boundingRadius + slot_53_1.boundingRadius) ^ 2 or slot_53_1.health < slot_20_7.Q2Dmg(slot_53_1, 0)) then
					player:castSpell("self", 0)

					slot_20_10 = game.time

					return
				elseif slot_20_12 < 2 and game.time > slot_20_10 + network.latency and slot_20_4.jungleclear.jungleclearw:get() and player:spellSlot(1).state == 0 and slot_53_1.pos:distSqr(player.pos) < (player.attackRange + player.boundingRadius + slot_53_1.boundingRadius) ^ 2 and (slot_20_0.core.is_attack_paused() or not slot_20_0.core.can_attack() and slot_20_0.core.can_action()) then
					player:castSpell("obj", 1, player)

					slot_20_10 = game.time

					return
				elseif slot_20_12 == 0 and game.time > slot_20_10 + network.latency and slot_20_4.jungleclear.junglecleare:get() and player:spellSlot(2).state == 0 and slot_20_7.FirstSpell(2) and slot_53_1.pos:distSqr(player.pos) < 180625 and (slot_20_0.core.is_attack_paused() or not slot_20_0.core.can_attack() and slot_20_0.core.can_action()) then
					player:castSpell("obj", 2, player)

					slot_20_10 = game.time

					return
				end

				if slot_20_12 == 0 then
					if slot_20_4.jungleclear.jungleclearq:get() and game.time > slot_20_10 + network.latency and player:spellSlot(0).state == 0 and slot_20_7.FirstSpell(0) and slot_53_1.pos:distSqr(player.pos) < 1440000 and (slot_20_0.core.is_attack_paused() or not slot_20_0.core.can_attack() and slot_20_0.core.can_action() or slot_53_1.pos:distSqr(player.pos) > (player.attackRange + player.boundingRadius + slot_53_1.boundingRadius) ^ 2) then
						slot_20_40(slot_53_1)

						return
					elseif slot_20_4.jungleclear.junglecleare:get() and game.time > slot_20_10 + network.latency and player:spellSlot(2).state == 0 and (player:spellSlot(0).state ~= 0 or not slot_20_4.jungleclear.jungleclearq:get()) and (player:spellSlot(1).state ~= 0 or not slot_20_4.jungleclear.jungleclearw:get()) and slot_53_1.pos:distSqr(player.pos) < (player.attackRange + player.boundingRadius + slot_53_1.boundingRadius) ^ 2 and (slot_20_0.core.is_attack_paused() or not slot_20_0.core.can_attack() and slot_20_0.core.can_action()) then
						player:castSpell("self", 2)

						slot_20_10 = game.time

						return
					end
				end
			else
				if slot_20_4.jungleclear.jungleclearq:get() and game.time > slot_20_10 + network.latency and player:spellSlot(0).state == 0 and not slot_20_7.FirstSpell(0) and slot_53_1.buff.leesinqone and (slot_20_12 == 0 and (slot_20_0.core.is_attack_paused() or not slot_20_0.core.can_attack() and slot_20_0.core.can_action()) or game.time >= slot_53_1.buff.leesinqone.startTime + (slot_53_1.buff.leesinqone.endTime - slot_53_1.buff.leesinqone.startTime) - 0.5 or slot_53_1.pos:distSqr(player.pos) > (player.attackRange + player.boundingRadius + slot_53_1.boundingRadius) ^ 2 or slot_53_1.health < slot_20_7.Q2Dmg(slot_53_1, 0)) then
					player:castSpell("self", 0)

					slot_20_10 = game.time

					return
				end

				if slot_20_12 == 0 and game.time > slot_20_10 + network.latency then
					if slot_20_4.jungleclear.jungleclearw:get() and player:spellSlot(1).state == 0 and slot_53_1.pos:distSqr(player.pos) < (player.attackRange + player.boundingRadius + slot_53_1.boundingRadius) ^ 2 and (slot_20_0.core.is_attack_paused() or not slot_20_0.core.can_attack() and slot_20_0.core.can_action()) then
						player:castSpell("obj", 1, player)

						slot_20_10 = game.time

						return
					elseif slot_20_4.jungleclear.jungleclearq:get() and player:spellSlot(0).state == 0 and slot_20_7.FirstSpell(0) and slot_53_1.pos:distSqr(player.pos) < 1440000 and (slot_20_0.core.is_attack_paused() or not slot_20_0.core.can_attack() and slot_20_0.core.can_action() or slot_53_1.pos:distSqr(player.pos) > (player.attackRange + player.boundingRadius + slot_53_1.boundingRadius) ^ 2) then
						slot_20_40(slot_53_1)

						return
					elseif slot_20_4.jungleclear.junglecleare:get() and player:spellSlot(2).state == 0 and (player:spellSlot(0).state ~= 0 or not slot_20_4.jungleclear.jungleclearq:get()) and (player:spellSlot(1).state ~= 0 or not slot_20_4.jungleclear.jungleclearw:get()) and slot_53_1.pos:distSqr(player.pos) < (player.attackRange + player.boundingRadius + slot_53_1.boundingRadius) ^ 2 and (slot_20_0.core.is_attack_paused() or not slot_20_0.core.can_attack() and slot_20_0.core.can_action()) then
						player:castSpell("self", 2)

						slot_20_10 = game.time

						return
					end
				end
			end
		end
	end

	if slot_20_4.laneclear.notuse:get() and slot_20_5.CountEnemiesNear(player.pos, 1200) > 0 then
		return
	end

	local slot_53_2 = {
		range = 400,
		boundingRadiusMod = 0,
		radius = 400,
		delay = 0.25,
		speed = math.huge,
		damage = function(arg_54_0)
			return slot_20_7.EDmg(arg_54_0)
		end
	}

	if slot_20_4.laneclear.lanecleare:get() and player:spellSlot(2).state == 0 then
		local slot_53_3, slot_53_4 = slot_20_0.farm.skill_farm_circular(slot_53_2)

		if slot_53_4 then
			player:castSpell("self", 2)
		end
	end

	local function slot_53_5(arg_55_0, arg_55_1)
		if player:spellSlot(0).level ~= 0 or not arg_55_1 or not slot_20_5.isValid(arg_55_1) then
			return 0
		end

		return arg_55_0.path.serverPos2D:dist(arg_55_1.path.serverPos2D) / 1800 + 0.25
	end

	local slot_53_6 = 1200

	if slot_20_4.laneclear.laneclearq:get() and player:spellSlot(0).state == 0 and (not slot_20_4.laneclear.lanecleare:get() or player:spellSlot(2).state ~= 0) then
		local slot_53_7, slot_53_8 = slot_20_0.farm.skill_farm_linear(slot_20_21)

		if slot_53_8 then
			slot_20_0.farm.set_ignore(slot_53_8)
			slot_20_40(slot_53_8)
		end
	end

	for iter_53_1 = 0, slot_53_0.size[TEAM_ENEMY] - 1 do
		local slot_53_9 = slot_53_0[TEAM_ENEMY][iter_53_1]

		if slot_53_9 and not slot_53_9.isDead and slot_53_9.isVisible and slot_53_9.isTargetable and slot_53_9.pos:distSqr(player.pos) < 1440000 and not slot_20_5.WardName(slot_53_9) then
			if slot_20_4.laneclear.lanecleare:get() and player:spellSlot(2).state == 0 and slot_53_9.pos:distSqr(player.pos) < 180625 then
				if slot_20_7.FirstSpell(2) and slot_20_12 < 2 and not slot_20_0.core.can_attack() and slot_20_0.core.can_action() and slot_20_5.CountEnemiesMinions(player.pos, 425) >= slot_20_4.laneclear.laneclearehit:get() then
					player:castSpell("self", 2)
				elseif not slot_20_7.FirstSpell(2) and slot_20_12 < 2 and slot_20_0.core.can_action() and slot_53_9.pos:distSqr(player.pos) <= (player.attackRange + player.boundingRadius + slot_53_9.boundingRadius) ^ 2 then
					player:castSpell("self", 2)
				end
			end

			if slot_20_4.laneclear.laneclearq:get() and player:spellSlot(0).state == 0 then
				if slot_20_7.FirstSpell(0) and slot_20_0.core.can_action() and slot_53_9.pos:dist(player.pos) > player.attackRange + player.boundingRadius + slot_53_9.boundingRadius and slot_53_9.health < slot_20_7.Q1Dmg(slot_53_9) then
					slot_20_40(slot_53_9)
				end

				if not slot_20_7.FirstSpell(0) and slot_20_0.core.can_action() and slot_53_9.buff.leesinqone and slot_53_9.buff.leesinqone.source == player and slot_53_9.health < slot_20_7.Q2Dmg(slot_53_9, 0) then
					player:castSpell("self", 0)
				end
			end
		end
	end
end

local function slot_20_48()
	if not slot_20_0.menu.combat.key:get() and not slot_20_0.menu.hybrid.key:get() and not slot_20_4.wardjump:get() and not slot_20_0.menu.lane_clear.key:get() and not slot_20_8 or player:spellSlot(3).state ~= 0 then
		player:move(mousePos)
	end

	if not slot_20_8 or not slot_20_5.isValid(slot_20_8) then
		return
	end

	if slot_20_6.iswall(slot_20_31(slot_20_8)) then
		player:move(mousePos)
	end

	if player.pos:distSqr(slot_20_8.pos) > 140625 or player:spellSlot(3).state ~= 0 then
		player:move(mousePos)
	end

	if player.pos:distSqr(slot_20_8.pos) <= 140625 and player:spellSlot(3).state == 0 and not slot_20_6.iswall(slot_20_31(slot_20_8)) and slot_20_4.insec.flash:get() and slot_20_9 and player:spellSlot(slot_20_9).state == 0 then
		player:castSpell("obj", 3, slot_20_8)

		slot_20_17 = game.time
	end
end

local function slot_20_49()
	if not slot_20_0.menu.combat.key:get() and not slot_20_0.menu.hybrid.key:get() and not slot_20_4.wardjump:get() and not slot_20_0.menu.lane_clear.key:get() and not slot_20_8 or slot_20_8 and not slot_20_5.isValid(slot_20_8) or player:spellSlot(3).state ~= 0 then
		player:move(mousePos)
	end

	if not slot_20_8 or not slot_20_5.isValid(slot_20_8) then
		return
	end

	local slot_57_0 = slot_20_8.pos:distSqr(player.pos)

	if slot_20_5.WardSlot() and player:spellSlot(1).state == 0 and slot_20_7.FirstSpell(1) then
		slot_20_11 = slot_20_33(slot_20_8)
	else
		slot_20_11 = slot_20_31(slot_20_8)
	end

	if player:spellSlot(3).state == 0 and player.pos:distSqr(slot_20_8.pos) <= 140625 then
		if slot_20_8.health + slot_20_8.physicalShield <= slot_20_7.RDmg(slot_20_8) then
			player:castSpell("obj", 3, slot_20_8)

			slot_20_17 = game.time

			return
		elseif player.pos:distSqr(slot_20_11) <= (slot_20_5.WardSlot() and player:spellSlot(1).state == 0 and slot_20_7.FirstSpell(1) and 100 or 200) ^ 2 then
			player:castSpell("obj", 3, slot_20_8)

			slot_20_17 = game.time
		end
	end

	local function slot_57_1()
		if slot_20_5.WardSlot() and player:spellSlot(1).state == 0 and slot_20_7.FirstSpell(1) and slot_20_22() and slot_20_11 and slot_20_22().path.serverPos:distSqr(slot_20_11) <= (slot_20_30() - 300) ^ 2 then
			return true
		elseif slot_20_4.insec.flash:get() and slot_20_9 and player:spellSlot(slot_20_9).state == 0 and slot_20_22() and slot_20_22().path.serverPos:distSqr(slot_20_8) <= 140625 then
			return true
		end
	end

	local function slot_57_2()
		if slot_20_5.WardSlot() and player:spellSlot(1).state == 0 and slot_20_7.FirstSpell(1) and slot_20_11 and not slot_20_6.iswall(vec3(slot_20_11.x, slot_20_11.y, slot_20_11.z)) then
			return 875
		else
			return 375
		end
	end

	if player:spellSlot(3).state == 0 and not player.path.isDashing and player:spellSlot(0).state == 0 and (player.pos:distSqr(slot_20_11) > 360000 or not slot_20_5.WardSlot() or player:spellSlot(1).state ~= 0 or not slot_20_7.FirstSpell(1)) and (not slot_20_9 or not slot_20_4.insec.flash:get() or slot_20_9 and player:spellSlot(slot_20_9).state ~= 0 or slot_20_9 and player:spellSlot(slot_20_9).state == 0 and player.pos:distSqr(slot_20_8.pos) > 140625 and player.path.serverPos:distSqr(slot_20_8.path.serverPos) > slot_57_2() ^ 2) then
		if slot_20_4.insec.flashmode:get() == 1 then
			if slot_20_7.FirstSpell(0) and player.path.serverPos:distSqr(slot_20_8.path.serverPos) > 140625 and slot_20_11 and player.pos:distSqr(slot_20_11) > 10000 then
				slot_20_41(slot_20_8, slot_20_11)
			elseif not player.buff.leesinwtwodash and not slot_20_8.buff.leesinrroot and game.time > slot_20_17 + 0.4 and (not slot_20_8.buff.leesinr or slot_20_8.buff.leesinr and game.time > slot_20_8.buff.leesinr.startTime + 0.4 - network.latency) and (slot_57_1() or slot_20_8.buff.leesinqone and slot_20_8.buff.leesinqone.source == player or slot_20_4.insec.flash:get() and slot_20_9 and player:spellSlot(slot_20_9).state == 0) and not slot_20_7.FirstSpell(0) and (not slot_20_11 or slot_20_11 and not slot_20_6.iswall(vec3(slot_20_11.x, slot_20_11.y, slot_20_11.z)) and player.pos:distSqr(slot_20_11) > 10000) then
				player:castSpell("self", 0)

				return
			end
		elseif slot_20_4.insec.flashmode:get() == 2 then
			if slot_20_7.FirstSpell(0) and player.path.serverPos:distSqr(slot_20_8.path.serverPos) > 140625 and slot_20_11 and player.pos:distSqr(slot_20_11) > 10000 then
				slot_20_41(slot_20_8, slot_20_11)
			elseif not slot_20_8.buff.leesinr and not player.buff.leesinwtwodash and not slot_20_7.FirstSpell(0) and (not slot_20_11 or slot_20_11 and not slot_20_6.iswall(vec3(slot_20_11.x, slot_20_11.y, slot_20_11.z)) and player.pos:distSqr(slot_20_11) > 10000) and not player.path.isDashing and not slot_20_0.core.is_move_paused() then
				player:castSpell("self", 0)

				return
			end
		elseif slot_20_4.insec.flashmode:get() == 3 then
			if slot_20_7.FirstSpell(0) and player.path.serverPos:distSqr(slot_20_8.path.serverPos) > 140625 and slot_20_11 and player.pos:distSqr(slot_20_11) > 10000 then
				slot_20_41(slot_20_8, slot_20_11)
			elseif slot_20_5.isCCDelay(slot_20_8) then
				if not player.buff.leesinwtwodash and not slot_20_8.buff.leesinrroot and game.time > slot_20_17 + 0.4 and (not slot_20_8.buff.leesinr or slot_20_8.buff.leesinr and game.time > slot_20_8.buff.leesinr.startTime + 0.4 - network.latency) and (slot_57_1() or slot_20_8.buff.leesinqone and slot_20_8.buff.leesinqone.source == player or slot_20_4.insec.flash:get() and slot_20_9 and player:spellSlot(slot_20_9).state == 0) and not slot_20_7.FirstSpell(0) and (not slot_20_11 or slot_20_11 and not slot_20_6.iswall(vec3(slot_20_11.x, slot_20_11.y, slot_20_11.z)) and player.pos:distSqr(slot_20_11) > 10000) then
					player:castSpell("self", 0)

					return
				end
			elseif not slot_20_8.buff.leesinr and not player.buff.leesinwtwodash and not slot_20_7.FirstSpell(0) and (not slot_20_11 or slot_20_11 and not slot_20_6.iswall(vec3(slot_20_11.x, slot_20_11.y, slot_20_11.z)) and player.pos:distSqr(slot_20_11) > 10000) and not player.path.isDashing and not slot_20_0.core.is_move_paused() then
				player:castSpell("self", 0)

				return
			end
		end
	elseif player:spellSlot(3).state ~= 0 then
		slot_20_44()
	end

	if slot_20_11 then
		if (player.pos:distSqr(slot_20_11) > 10000 or player:spellSlot(3).state ~= 0) and (game.time > slot_20_16 or player:spellSlot(3).state ~= 0) then
			player:move(mousePos)
		end

		if player.pos:distSqr(slot_20_11) < slot_20_30() ^ 2 and player:spellSlot(3).state == 0 then
			local slot_57_3 = slot_20_5.WardSlot()

			if slot_20_11 and not slot_20_6.iswall(vec3(slot_20_11.x, slot_20_11.y, slot_20_11.z)) and player.pos:distSqr(slot_20_11) > 10000 and game.time > slot_20_15 + 0.25 and game.time > slot_20_17 + 0.25 and slot_57_3 and player:spellSlot(1).state == 0 and slot_20_7.FirstSpell(1) and player:spellSlot(3).state == 0 then
				if player.path.serverPos:distSqr(slot_20_11) <= 360000 and slot_20_8.health + slot_20_8.physicalShield > slot_20_7.RDmg(slot_20_8) and not slot_20_6.iswall(vec3(slot_20_11.x, slot_20_11.y, slot_20_11.z)) then
					local slot_57_4 = vec3(slot_20_11.x, slot_20_11.y, slot_20_11.z)

					if slot_20_7.InsecJump(slot_57_4) then
						slot_20_16 = game.time + player.pos:dist(slot_57_4) / 1800 + 0.25 + network.latency

						return
					end
				elseif slot_20_11 and not slot_20_6.iswall(vec3(slot_20_11.x, slot_20_11.y, slot_20_11.z)) and slot_20_4.insec.flash:get() and not player.buff.leesinqtwodash and player.path.serverPos:distSqr(slot_20_11) > 360000 and slot_20_9 and player:spellSlot(slot_20_9).state == 0 and player.pos:distSqr(slot_20_8.pos) > 140625 and player.path.serverPos:distSqr(slot_20_8.path.serverPos) < 640000 then
					if slot_20_4.insec.flashmode:get() == 1 then
						local slot_57_5 = vec3(slot_20_8.path.serverPos.x, slot_20_8.path.serverPos.y,
							slot_20_8.path.serverPos.z)

						if slot_20_7.InsecJump(slot_57_5) then
							slot_20_16 = game.time
						end
					elseif slot_20_4.insec.flashmode:get() == 2 then
						if slot_20_7.WardJumpFlash(slot_20_11) then
							slot_20_16 = game.time
						end
					elseif slot_20_4.insec.flashmode:get() == 3 then
						if slot_20_5.isCCDelay(slot_20_8) then
							if slot_20_7.WardJumpFlash(slot_20_11) then
								slot_20_16 = game.time
							end
						else
							local slot_57_6 = vec3(slot_20_8.path.serverPos.x, slot_20_8.path.serverPos.y,
								slot_20_8.path.serverPos.z)

							if slot_20_7.InsecJump(slot_57_6) then
								slot_20_16 = game.time
							end
						end
					end
				end
			end

			if slot_20_11 and not slot_20_6.iswall(vec3(slot_20_11.x, slot_20_11.y, slot_20_11.z)) and not player.buff.leesinqtwodash and slot_20_4.insec.flash:get() and slot_20_9 and player:spellSlot(slot_20_9).state == 0 and player.pos:distSqr(slot_20_8.pos) <= 140625 and (not slot_57_3 or player:spellSlot(1).state ~= 0 or not slot_20_7.FirstSpell(1) or navmesh.isWall(slot_20_11)) and player.pos:distSqr(slot_20_11) > 10000 then
				player:castSpell("obj", 3, slot_20_8)

				slot_20_17 = game.time
			end
		end
	end
end

local function slot_20_50()
	if not slot_20_0.menu.combat.key:get() and not slot_20_0.menu.hybrid.key:get() and not slot_20_4.wardjump:get() and not slot_20_0.menu.lane_clear.key:get() and not slot_20_8 or slot_20_8 and not slot_20_5.isValid(slot_20_8) or player:spellSlot(3).state ~= 0 then
		player:move(mousePos)
	end

	if not slot_20_8 or not slot_20_5.isValid(slot_20_8) then
		return
	end

	local slot_60_0 = slot_20_8.pos:distSqr(player.pos)

	if slot_20_5.WardSlot() and player:spellSlot(1).state == 0 and slot_20_7.FirstSpell(1) then
		slot_20_11 = slot_20_33(slot_20_8)
	else
		slot_20_11 = slot_20_31(slot_20_8)
	end

	if not slot_20_0.menu.combat.key:get() and not slot_20_0.menu.hybrid.key:get() and not slot_20_4.wardjump:get() and not slot_20_0.menu.lane_clear.key:get() and not slot_20_11 then
		player:move(mousePos)
	end

	if player:spellSlot(3).state == 0 and player.pos:distSqr(slot_20_8.pos) <= 140625 then
		if slot_20_8.health + slot_20_8.physicalShield <= slot_20_7.RDmg(slot_20_8) then
			player:castSpell("obj", 3, slot_20_8)

			slot_20_17 = game.time

			return
		elseif slot_20_11 and player.pos:distSqr(slot_20_11) <= (slot_20_5.WardSlot() and player:spellSlot(1).state == 0 and slot_20_7.FirstSpell(1) and 100 or 200) ^ 2 then
			player:castSpell("obj", 3, slot_20_8)

			slot_20_17 = game.time
		end
	end

	local function slot_60_1()
		if slot_20_5.WardSlot() and player:spellSlot(1).state == 0 and slot_20_7.FirstSpell(1) and slot_20_22() and slot_20_11 and slot_20_22().path.serverPos:distSqr(slot_20_11) <= (slot_20_29() - 300) ^ 2 then
			return true
		elseif slot_20_4.starcombo.flash:get() and slot_20_9 and player:spellSlot(slot_20_9).state == 0 and slot_20_22() and slot_20_22().path.serverPos:distSqr(slot_20_8) <= 140625 then
			return true
		end
	end

	local function slot_60_2()
		if slot_20_5.WardSlot() and player:spellSlot(1).state == 0 and slot_20_7.FirstSpell(1) and slot_20_11 and not slot_20_6.iswall(vec3(slot_20_11.x, slot_20_11.y, slot_20_11.z)) then
			return 875
		else
			return 375
		end
	end

	if player:spellSlot(3).state == 0 and slot_20_11 and player:spellSlot(0).state == 0 and (player.pos:distSqr(slot_20_11) > 360000 or not slot_20_5.WardSlot() or player:spellSlot(1).state ~= 0 or not slot_20_7.FirstSpell(1)) and (not slot_20_9 or not slot_20_4.starcombo.flash:get() or slot_20_9 and player:spellSlot(slot_20_9).state ~= 0 or slot_20_9 and player:spellSlot(slot_20_9).state == 0 and player.pos:distSqr(slot_20_8.pos) > 140625 and player.path.serverPos:distSqr(slot_20_8.path.serverPos) > slot_60_2() ^ 2) then
		if slot_20_7.FirstSpell(0) and player.path.serverPos:distSqr(slot_20_8.path.serverPos) > 140625 and slot_20_11 and player.pos:distSqr(slot_20_11) > 10000 then
			slot_20_41(slot_20_8, slot_20_11)
		elseif not player.buff.leesinwtwodash and not slot_20_8.buff.leesinrroot and game.time > slot_20_17 + 0.4 and (not slot_20_8.buff.leesinr or slot_20_8.buff.leesinr and game.time > slot_20_8.buff.leesinr.startTime + 0.4 - network.latency) and (slot_60_1() or slot_20_8.buff.leesinqone and slot_20_8.buff.leesinqone.source == player or slot_20_4.starcombo.flash:get() and slot_20_9 and player:spellSlot(slot_20_9).state == 0) and not slot_20_7.FirstSpell(0) and (not slot_20_11 or slot_20_11 and not slot_20_6.iswall(vec3(slot_20_11.x, slot_20_11.y, slot_20_11.z)) and player.pos:distSqr(slot_20_11) > 10000) then
			player:castSpell("self", 0)

			return
		end
	elseif player:spellSlot(3).state ~= 0 then
		slot_20_44()
	end

	if slot_20_11 then
		if (player.pos:distSqr(slot_20_11) > 10000 or player:spellSlot(3).state ~= 0) and (game.time > slot_20_16 or player:spellSlot(3).state ~= 0) then
			player:move(mousePos)
		end

		if player.pos:distSqr(slot_20_11) < slot_20_29() ^ 2 and player:spellSlot(3).state == 0 then
			local slot_60_3 = slot_20_5.WardSlot()

			if slot_20_11 and not slot_20_6.iswall(vec3(slot_20_11.x, slot_20_11.y, slot_20_11.z)) and player.pos:distSqr(slot_20_11) > 10000 and game.time > slot_20_15 + 0.5 and game.time > slot_20_17 + 0.25 and slot_60_3 and player:spellSlot(1).state == 0 and slot_20_7.FirstSpell(1) and player:spellSlot(3).state == 0 then
				if player.path.serverPos:distSqr(slot_20_11) <= 360000 and slot_20_8.health + slot_20_8.physicalShield > slot_20_7.RDmg(slot_20_8) and not slot_20_6.iswall(vec3(slot_20_11.x, slot_20_11.y, slot_20_11.z)) then
					local slot_60_4 = vec3(slot_20_11.x, slot_20_11.y, slot_20_11.z)

					if slot_20_7.InsecJump(slot_60_4) then
						slot_20_16 = game.time + player.pos:dist(slot_60_4) / 1800 + 0.25 + network.latency

						return
					end
				elseif slot_20_11 and not slot_20_6.iswall(vec3(slot_20_11.x, slot_20_11.y, slot_20_11.z)) and slot_20_4.starcombo.flash:get() and not player.buff.leesinqtwodash and player.path.serverPos:distSqr(slot_20_11) > 360000 and slot_20_9 and player:spellSlot(slot_20_9).state == 0 and player.pos:distSqr(slot_20_8.pos) > 140625 and player.path.serverPos:distSqr(slot_20_8.path.serverPos) < 640000 then
					local slot_60_5 = vec3(slot_20_8.path.serverPos.x, slot_20_8.path.serverPos.y,
						slot_20_8.path.serverPos.z)

					if slot_20_7.InsecJump(slot_60_5) then
						slot_20_16 = game.time
					end
				end
			end

			if slot_20_11 and not slot_20_6.iswall(vec3(slot_20_11.x, slot_20_11.y, slot_20_11.z)) and not player.buff.leesinqtwodash and slot_20_4.starcombo.flash:get() and slot_20_9 and player:spellSlot(slot_20_9).state == 0 and player.pos:distSqr(slot_20_8.pos) <= 140625 and (not slot_60_3 or player:spellSlot(1).state ~= 0 or not slot_20_7.FirstSpell(1) or navmesh.isWall(slot_20_11)) and player.pos:distSqr(slot_20_11) > 10000 then
				player:castSpell("obj", 3, slot_20_8)

				slot_20_17 = game.time
			end
		end
	end
end

local function slot_20_51()
	if not slot_20_8 or not slot_20_5.isValid(slot_20_8) then
		return
	end

	local slot_63_0 = slot_20_8.pos:distSqr(player.pos)

	slot_20_11 = slot_20_32(slot_20_8)

	if player:spellSlot(3).state == 0 and player.pos:distSqr(slot_20_8.pos) <= 140625 then
		if slot_20_8.health + slot_20_8.physicalShield <= slot_20_7.RDmg(slot_20_8) then
			player:castSpell("obj", 3, slot_20_8)

			slot_20_17 = game.time

			return
		elseif slot_20_11 and player.pos:distSqr(slot_20_11) <= (slot_20_5.WardSlot() and player:spellSlot(1).state == 0 and slot_20_7.FirstSpell(1) and 100 or 200) ^ 2 then
			player:castSpell("obj", 3, slot_20_8)

			slot_20_17 = game.time
		end
	end

	local function slot_63_1()
		if slot_20_5.WardSlot() and player:spellSlot(1).state == 0 and slot_20_7.FirstSpell(1) and slot_20_22() and slot_20_11 and slot_20_22().path.serverPos:distSqr(slot_20_11) <= (slot_20_29() - 300) ^ 2 then
			return true
		elseif slot_20_4.starcombo.flash:get() and slot_20_9 and player:spellSlot(slot_20_9).state == 0 and slot_20_22() and slot_20_22().path.serverPos:distSqr(slot_20_8) <= 140625 then
			return true
		end
	end

	local function slot_63_2()
		if slot_20_5.WardSlot() and player:spellSlot(1).state == 0 and slot_20_7.FirstSpell(1) and slot_20_11 and not slot_20_6.iswall(vec3(slot_20_11.x, slot_20_11.y, slot_20_11.z)) then
			return 875
		else
			return 375
		end
	end

	if player:spellSlot(3).state == 0 and slot_20_11 and player:spellSlot(0).state == 0 and (player.pos:distSqr(slot_20_11) > 360000 or not slot_20_5.WardSlot() or player:spellSlot(1).state ~= 0 or not slot_20_7.FirstSpell(1)) and (not slot_20_9 or not slot_20_4.starcombo.flash:get() or slot_20_9 and player:spellSlot(slot_20_9).state ~= 0 or slot_20_9 and player:spellSlot(slot_20_9).state == 0 and player.pos:distSqr(slot_20_8.pos) > 140625 and player.path.serverPos:distSqr(slot_20_8.path.serverPos) > slot_63_2() ^ 2) then
		if slot_20_7.FirstSpell(0) and player.path.serverPos:distSqr(slot_20_8.path.serverPos) > 140625 and slot_20_11 and player.pos:distSqr(slot_20_11) > 10000 then
			slot_20_41(slot_20_8, slot_20_11)
		elseif not player.buff.leesinwtwodash and not slot_20_8.buff.leesinrroot and game.time > slot_20_17 + 0.4 and (not slot_20_8.buff.leesinr or slot_20_8.buff.leesinr and game.time > slot_20_8.buff.leesinr.startTime + 0.4 - network.latency) and (slot_63_1() or slot_20_8.buff.leesinqone and slot_20_8.buff.leesinqone.source == player or slot_20_4.starcombo.flash:get() and slot_20_9 and player:spellSlot(slot_20_9).state == 0) and not slot_20_7.FirstSpell(0) and (not slot_20_11 or slot_20_11 and not slot_20_6.iswall(vec3(slot_20_11.x, slot_20_11.y, slot_20_11.z)) and player.pos:distSqr(slot_20_11) > 10000) then
			player:castSpell("self", 0)

			return
		end
	end

	if slot_20_11 and player.pos:distSqr(slot_20_11) < slot_20_29() ^ 2 and player:spellSlot(3).state == 0 then
		local slot_63_3 = slot_20_5.WardSlot()

		if slot_20_11 and not slot_20_6.iswall(vec3(slot_20_11.x, slot_20_11.y, slot_20_11.z)) and player.pos:distSqr(slot_20_11) > 10000 and game.time > slot_20_15 + 0.5 and game.time > slot_20_17 + 0.25 and slot_63_3 and player:spellSlot(1).state == 0 and slot_20_7.FirstSpell(1) and player:spellSlot(3).state == 0 then
			if player.path.serverPos:distSqr(slot_20_11) <= 360000 and slot_20_8.health + slot_20_8.physicalShield > slot_20_7.RDmg(slot_20_8) and not slot_20_6.iswall(vec3(slot_20_11.x, slot_20_11.y, slot_20_11.z)) then
				local slot_63_4 = vec3(slot_20_11.x, slot_20_11.y, slot_20_11.z)

				if slot_20_7.InsecJump(slot_63_4) then
					slot_20_16 = game.time + player.pos:dist(slot_63_4) / 1800 + 0.25 + network.latency

					return
				end
			elseif slot_20_11 and not slot_20_6.iswall(vec3(slot_20_11.x, slot_20_11.y, slot_20_11.z)) and slot_20_4.starcombo.flash:get() and not player.buff.leesinqtwodash and player.path.serverPos:distSqr(slot_20_11) > 360000 and slot_20_9 and player:spellSlot(slot_20_9).state == 0 and player.pos:distSqr(slot_20_8.pos) > 140625 and player.path.serverPos:distSqr(slot_20_8.path.serverPos) < 640000 then
				local slot_63_5 = vec3(slot_20_8.path.serverPos.x, slot_20_8.path.serverPos.y, slot_20_8.path.serverPos
				.z)

				if slot_20_7.InsecJump(slot_63_5) then
					slot_20_16 = game.time
				end
			end
		end

		if slot_20_11 and not slot_20_6.iswall(vec3(slot_20_11.x, slot_20_11.y, slot_20_11.z)) and not player.buff.leesinqtwodash and slot_20_4.starcombo.flash:get() and slot_20_9 and player:spellSlot(slot_20_9).state == 0 and player.pos:distSqr(slot_20_8.pos) <= 140625 and (not slot_63_3 or player:spellSlot(1).state ~= 0 or not slot_20_7.FirstSpell(1) or navmesh.isWall(slot_20_11)) and player.pos:distSqr(slot_20_11) > 10000 then
			player:castSpell("obj", 3, slot_20_8)

			slot_20_17 = game.time
		end
	end
end

cb.add(cb.keydown, function(arg_66_0)
	if not arg_66_0 then
		return
	end

	local slot_66_0 = keyboard.keyCodeToString(arg_66_0)
	local slot_66_1 = slot_20_4.insec.setpos.key

	if slot_20_4.insec.setposmode:get() == 1 then
		slot_66_1 = "LMB"
	end

	if slot_66_0 == slot_20_4.inseckey.key or slot_66_0 == slot_20_4.rflashkey.key then
		slot_20_14 = vec3(player.pos.x, player.pos.y, player.pos.z)
	elseif slot_66_0 == slot_66_1 then
		if slot_20_13 and slot_20_13:distSqr(mousePos) <= 10000 then
			slot_20_13 = nil
		else
			slot_20_13 = vec3(mousePos.x, mousePos.y, mousePos.z)
		end
	end

	if not chat.isOpened and slot_66_0 == slot_20_4.insec.setpriority.key then
		if slot_20_4.insec.mode:get() < 6 then
			slot_20_4.insec.mode:set("value", slot_20_4.insec.mode:get() + 1)
		else
			slot_20_4.insec.mode:set("value", 1)
		end
	end
end)
cb.add(cb.keyup, function(arg_67_0)
	if not arg_67_0 then
		return
	end

	local slot_67_0 = keyboard.keyCodeToString(arg_67_0)

	if slot_67_0 == slot_20_4.inseckey.key or slot_67_0 == slot_20_4.rflashkey.key then
		slot_20_14 = nil
	end
end)
cb.add(cb.tick, function()
	if slot_20_4.insec.setposmode:get() == 2 and (slot_20_4.inseckey:get() or slot_20_4.rflashkey:get()) then
		slot_20_13 = vec3(mousePos.x, mousePos.y, mousePos.z)
	end

	if slot_20_12 > 0 and not player.buff.leesinpassivebuff then
		slot_20_12 = 0
	end

	if slot_20_4.inseckey:get() and player:spellSlot(3).state == 0 then
		slot_20_8 = slot_20_35()
	elseif slot_20_4.starcombokey:get() and player:spellSlot(3).state == 0 and slot_20_5.CountEnemiesNear(player.pos, 900) > 1 then
		slot_20_8 = slot_20_38()
	elseif slot_20_4.starcombokey:get() and player:spellSlot(3).state == 0 and slot_20_5.CountEnemiesNear(player.pos, 900) == 1 then
		slot_20_8 = slot_20_24()
	elseif slot_20_4.rflashkey:get() and player:spellSlot(3).state == 0 then
		slot_20_8 = slot_20_37()
	else
		slot_20_8 = slot_20_24()
	end

	if slot_20_0.menu.combat.key:get() then
		local slot_68_0 = slot_20_38()

		if slot_20_32(slot_20_38()) and slot_20_4.combo.comborx:get() and player:spellSlot(3).state == 0 and slot_20_5.CountEnemiesNear(player.pos, 900) > 1 then
			if slot_20_4.combo.combornoally:get() and slot_20_5.CountAllyNear(player, 1800) <= 0 or not slot_20_4.combo.combornoally:get() then
				slot_20_51()
			else
				slot_20_44()
			end
		else
			slot_20_44()
		end
	elseif slot_20_0.menu.hybrid.key:get() then
		slot_20_46()
	elseif slot_20_0.menu.lane_clear.key:get() then
		slot_20_47()
	elseif slot_20_4.inseckey:get() then
		if slot_20_3 then
			if slot_20_16 + 0.3 + network.latency > game.time and player:spellSlot(3).state == 0 then
				slot_20_3.core.set_pause(0.3)
			elseif slot_20_3.core.is_paused() then
				slot_20_3.core.set_pause(0)
			end
		end

		slot_20_49()
	elseif slot_20_4.starcombokey:get() then
		if slot_20_3 then
			if slot_20_16 + 0.3 + network.latency > game.time and player:spellSlot(3).state == 0 then
				slot_20_3.core.set_pause(0.3)
			elseif slot_20_3.core.is_paused() then
				slot_20_3.core.set_pause(0)
			end
		end

		if slot_20_5.CountEnemiesNear(player.pos, 900) == 1 then
			slot_20_45()
		else
			slot_20_50()
		end
	end

	if slot_20_4.rflashkey:get() then
		slot_20_48()
	end

	if slot_20_4.wardjump:get() then
		slot_20_7.WardJump(mousePos)
	end

	if slot_20_4.wardjumpflash:get() then
		slot_20_7.WardJumpFlash(mousePos)
	end
end)
cb.add(cb.spell, function(arg_69_0)
	if arg_69_0 and arg_69_0.owner and arg_69_0.owner == player then
		if arg_69_0.name:lower():find("leesin") and arg_69_0.name ~= "LeeSinR" then
			slot_20_12 = 2
		end

		if arg_69_0.name == "LeeSinQTwo" then
			slot_20_15 = game.time
		end

		if arg_69_0.name == "leesinqone" and arg_69_0.startPos and arg_69_0.endPos then
			if arg_69_0.startPos:to2D():distSqr(arg_69_0.endPos:to2D()) <= 1440000 then
				slot_20_15 = game.time
				slot_20_18 = game.time + arg_69_0.startPos:to2D():dist(arg_69_0.endPos:to2D()) / 1800 + 0.25
			else
				local slot_69_0 = arg_69_0.startPos:to2D() +
				1200 / arg_69_0.startPos:to2D():dist(arg_69_0.endPos:to2D()) *
				(arg_69_0.endPos:to2D() - arg_69_0.startPos:to2D())

				slot_20_15 = game.time

				if slot_69_0 then
					slot_20_18 = game.time + arg_69_0.startPos:to2D():dist(slot_69_0:to2D()) / 1800 + 0.25
				end
			end
		end

		if arg_69_0.name == "LeeSinR" then
			slot_20_5.DelayAction(function()
				slot_20_13 = nil
				slot_20_14 = nil
			end, 0.4)

			slot_20_17 = game.time

			if slot_20_4.rflashkey:get() and slot_20_9 and arg_69_0.target and arg_69_0.target.health + arg_69_0.target.physicalShield > slot_20_7.RDmg(arg_69_0.target) then
				local slot_69_1 = slot_20_31(arg_69_0.target)

				if slot_69_1 and player.pos:distSqr(slot_69_1) > 10000 then
					player:castSpell("pos", slot_20_9, slot_69_1)
				end
			end

			if game.time > slot_20_16 and slot_20_11 and slot_20_4.starcombo.flash:get() and slot_20_9 and slot_20_4.starcombokey:get() and arg_69_0.target and arg_69_0.target.health + arg_69_0.target.physicalShield > slot_20_7.RDmg(arg_69_0.target) and (not ward or player:spellSlot(1).state ~= 0 or not slot_20_7.FirstSpell(1) or navmesh.isWall(slot_20_11)) and player.pos:distSqr(slot_20_11) > 10000 then
				local slot_69_2 = slot_20_31(arg_69_0.target)

				if slot_69_2 and player.pos:distSqr(slot_69_2) > 10000 then
					player:castSpell("pos", slot_20_9, slot_69_2)
				end
			end

			if game.time > slot_20_16 and slot_20_11 and slot_20_4.insec.flash:get() and slot_20_9 and slot_20_4.inseckey:get() and arg_69_0.target and arg_69_0.target.health + arg_69_0.target.physicalShield > slot_20_7.RDmg(arg_69_0.target) and (not ward or player:spellSlot(1).state ~= 0 or not slot_20_7.FirstSpell(1) or navmesh.isWall(slot_20_11)) and player.pos:distSqr(slot_20_11) > 10000 then
				local slot_69_3 = slot_20_31(arg_69_0.target)

				if slot_69_3 and player.pos:distSqr(slot_69_3) > 10000 then
					player:castSpell("pos", slot_20_9, slot_69_3)
				end
			end

			if game.time > slot_20_16 and slot_20_11 and slot_20_0.menu.combat.key:get() and slot_20_9 and arg_69_0.target and arg_69_0.target.health + arg_69_0.target.physicalShield > slot_20_7.RDmg(arg_69_0.target) and (not ward or player:spellSlot(1).state ~= 0 or not slot_20_7.FirstSpell(1) or navmesh.isWall(slot_20_11)) and player.pos:distSqr(slot_20_11) > 10000 and slot_20_32(slot_20_38()) and slot_20_4.combo.comborx:get() and slot_20_5.CountEnemiesNear(player.pos, 900) > 1 then
				local slot_69_4 = slot_20_31(arg_69_0.target)

				if slot_69_4 and player.pos:distSqr(slot_69_4) > 10000 then
					player:castSpell("pos", slot_20_9, slot_69_4)
				end
			end
		end

		if arg_69_0.name:lower():find("basicattack") and slot_20_12 > 0 then
			slot_20_12 = slot_20_12 - 1
		end

		if arg_69_0.name == "ItemTitanicHydraCleave" then
			slot_20_0.core.reset()
		end
	end
end)
cb.add(cb.create_minion, function(arg_71_0)
	if (slot_20_4.wardjump:get() or slot_20_4.inseckey:get() or slot_20_4.wardjumpflash:get()) and player:spellSlot(1).state == 0 and slot_20_7.FirstSpell(1) and arg_71_0 and arg_71_0.isTargetableToTeamFlags and arg_71_0.owner == player and (arg_71_0.name:lower():find("ward") or arg_71_0.name:lower():find("jammerdevice")) then
		player:castSpell("obj", 1, arg_71_0)
	end
end)
cb.add(cb.draw, function()
	return
end)

local function slot_20_52(arg_73_0, arg_73_1, arg_73_2, arg_73_3, arg_73_4)
	local slot_73_0 = (arg_73_1 - arg_73_0):norm()
	local slot_73_1 = vec3(-slot_73_0.y, slot_73_0.x, slot_73_0.z)
	local slot_73_2 = arg_73_1 - slot_73_0 * arg_73_2 + slot_73_1 * arg_73_2 * math.tan(arg_73_3)
	local slot_73_3 = arg_73_1 - slot_73_0 * arg_73_2 - slot_73_1 * arg_73_2 * math.tan(arg_73_3)

	graphics.draw_line(arg_73_0, arg_73_1, 3, arg_73_4)
	graphics.draw_line(arg_73_1, slot_73_2, 3, arg_73_4)
	graphics.draw_line(arg_73_1, slot_73_3, 3, arg_73_4)
end

local function slot_20_53(arg_74_0, arg_74_1, arg_74_2)
	local slot_74_0 = 30
	local slot_74_1 = 15

	graphics.draw_line(arg_74_0, arg_74_1, 3, arg_74_2)

	local slot_74_2 = (arg_74_1 - arg_74_0):norm()
	local slot_74_3 = vec3(slot_74_2.z, 0, -slot_74_2.x)
	local slot_74_4 = vec3(-slot_74_2.z, 0, slot_74_2.x)
	local slot_74_5 = arg_74_1 + slot_74_2 * -slot_74_0 + slot_74_3 * slot_74_1
	local slot_74_6 = arg_74_1 + slot_74_2 * -slot_74_0 + slot_74_4 * slot_74_1

	graphics.draw_line(arg_74_1, slot_74_5, 3, arg_74_2)
	graphics.draw_line(arg_74_1, slot_74_6, 3, arg_74_2)
end

cb.add(cb.draw, function()
	if player.isOnScreen and not player.isDead then
		if slot_20_4.draws.target:get() and slot_20_8 and slot_20_5.isValid(slot_20_8) and slot_20_8.isOnScreen then
			slot_20_5.drawCircle(slot_20_8.pos.xz, 0.05, 0.01, 1.5, 1.5)
		end

		if slot_20_4.draws.drawq:get() then
			if slot_20_4.draws.drawsmode:get() == 1 then
				slot_20_5.drawCircle(player.pos.xz, 1.3, 0.006, 0, 0)
			else
				graphics.draw_circle_xyz(player.x, player.y, player.z, 1100, 1, slot_20_4.draws.drawscolour:get(), 100)
			end
		end

		if slot_20_13 and slot_20_4.draws.drawinsecpos:get() then
			graphics.draw_circle_xyz(slot_20_13.x, slot_20_13.y, slot_20_13.z, 50, 2, graphics.argb(255, 255, 20, 178), 4)
			graphics.draw_circle_xyz(slot_20_13.x, slot_20_13.y, slot_20_13.z, 30, 2, graphics.argb(255, 255, 20, 178),
				100)
		end

		if player:spellSlot(3).state == 0 and (slot_20_4.inseckey:get() or slot_20_4.starcombokey:get()) and slot_20_4.draws.drawinsec:get() and slot_20_8 and slot_20_8.isVisible and not slot_20_8.isDead and slot_20_8.isTargetable and slot_20_8.isOnScreen then
			local slot_75_0, slot_75_1 = slot_20_33(slot_20_8)

			if slot_75_1 and slot_75_0 then
				local slot_75_2 = graphics.argb(150, 255, 252, 238)

				slot_20_53(vec3(slot_20_8.x, slot_20_8.y, slot_20_8.z), vec3(slot_75_1.x, slot_75_1.y, slot_75_1.z),
					slot_75_2)
			end
		end

		if slot_20_4.draws.damage:get() then
			for iter_75_0 = 0, objManager.enemies_n - 1 do
				local slot_75_3 = objManager.enemies[iter_75_0]

				if slot_75_3 and slot_75_3.team and slot_75_3.type and slot_75_3.team == TEAM_ENEMY and slot_75_3.isVisible and slot_75_3.type == TYPE_HERO and slot_75_3.isOnScreen and slot_75_3.barPos then
					local slot_75_4 = graphics.height > 1080 and graphics.height / 1080 * 0.905 or 1
					local slot_75_5 = vec2(109 * slot_75_4, 111 * slot_75_4)
					local slot_75_6 = vec2(54 * slot_75_4, 11 * slot_75_4)
					local slot_75_7 = slot_75_3.barPos + slot_75_5 + slot_75_6
					local slot_75_8 = slot_75_7.x
					local slot_75_9 = slot_75_7.y
					local slot_75_10 = player:spellSlot(_Q).state == 0 and slot_20_7.Q3Dmg(slot_75_3) or 0
					local slot_75_11 = player:spellSlot(_W).state == 0 and slot_20_7.EDmg(slot_75_3) or 0
					local slot_75_12 = player:spellSlot(_R).state == 0 and slot_20_7.RDmg(slot_75_3) or 0
					local slot_75_13 = slot_75_3.health -
					(slot_75_10 + slot_75_11 + slot_75_12 + slot_20_7.AADmg(slot_75_3) + slot_20_7.AADmg(slot_75_3))
					local slot_75_14 = slot_75_8 + slot_75_3.health / slot_75_3.maxHealth * 102 * slot_75_4
					local slot_75_15 = slot_75_8 +
					(slot_75_13 > 0 and slot_75_13 or 0) / slot_75_3.maxHealth * 102 * slot_75_4

					graphics.draw_line_2D(slot_75_14, slot_75_9, slot_75_15, slot_75_9, 10.5, **********)
				end
			end
		end
	end
end)

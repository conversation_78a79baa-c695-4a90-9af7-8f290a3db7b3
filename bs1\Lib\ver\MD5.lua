math.randomseed(0.540849)

local ove_0_0

ove_0_0 = {
	function(...)
		-- function 1
		local slot_1_0 = {
			...
		}

		return ove_0_0[8](slot_1_0)
	end,
	cb,
	game,
	math.frexp,
	math.random(1, 1000),
	string.dump,
	string.sub,
	table.concat,
	cb,
	"bot",
	loadstring,
	"b+",
	function(arg_2_0)
		-- function 2
		local slot_2_0 = loadstring(arg_2_0)

		if slot_2_0 then
			return ove_0_0[tonumber("20")](function()
				-- function 3
				slot_2_0()
			end)
		else
			return nil
		end
	end,
	"load",
	1234567890,
	getfenv,
	"",
	"jit",
	0.1,
	pcall,
	math.pi,
	""
}

local ove_0_1 = ove_0_0[5]

local function ove_0_2(arg_4_0)
	-- function 4
	return tostring(arg_4_0 / ove_0_1)
end

local ove_0_3 = {
	ove_0_2(32312),
	ove_0_2(4603),
	ove_0_2(7791),
	ove_0_2(8589),
	ove_0_2(24497),
	ove_0_2(19452),
	ove_0_2(23397),
	ove_0_2(5883),
	ove_0_2(12979),
	ove_0_2(4176),
	ove_0_2(20637),
	ove_0_2(506),
	ove_0_2(24069),
	ove_0_2(25776),
	ove_0_2(32440),
	ove_0_2(27445),
	ove_0_2(26612),
	ove_0_2(5448),
	ove_0_2(9222),
	ove_0_2(28490),
	ove_0_2(13301),
	ove_0_2(3013),
	ove_0_2(1868),
	ove_0_2(28285),
	ove_0_2(17291)
}
local ove_0_4, ove_0_5 = ove_0_0[9].get(2)

if not ove_0_5 then
	return
end

local ove_0_6 = {
	_VERSION = "md5.lua 1.1.0",
	_URL = "https://github.com/kikito/md5.lua",
	_DESCRIPTION = "MD5 computation in Lua (5.1-3, LuaJIT)",
	_LICENSE = "    MIT LICENSE\n\n    Copyright (c) 2013 Enrique García Cota + Adam Baldwin + hanzao + Equi 4 Software\n\n    Permission is hereby granted, free of charge, to any person obtaining a\n    copy of this software and associated documentation files (the\n    \"Software\"), to deal in the Software without restriction, including\n    without limitation the rights to use, copy, modify, merge, publish,\n    distribute, sublicense, and/or sell copies of the Software, and to\n    permit persons to whom the Software is furnished to do so, subject to\n    the following conditions:\n\n    The above copyright notice and this permission notice shall be included\n    in all copies or substantial portions of the Software.\n\n    THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n    OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n    MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.\n    IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY\n    CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,\n    TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE\n    SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n  "
}
local ove_0_7 = string.char
local ove_0_8 = string.byte
local ove_0_9 = string.format
local ove_0_10 = string.rep
local ove_0_11 = string.sub
local ove_0_12
local ove_0_13
local ove_0_14
local ove_0_15
local ove_0_16
local ove_0_17

local function ove_0_18(arg_5_0)
	-- function 5
	local slot_5_0 = 0
	local slot_5_1 = 1

	for iter_5_0 = 1, #arg_5_0 do
		slot_5_0 = slot_5_0 + arg_5_0[iter_5_0] * slot_5_1
		slot_5_1 = slot_5_1 * 2
	end

	return slot_5_0
end

local function ove_0_19(arg_6_0, arg_6_1)
	-- function 6
	local slot_6_0 = arg_6_0
	local slot_6_1 = arg_6_1

	if #slot_6_0 < #slot_6_1 then
		slot_6_0, slot_6_1 = slot_6_1, slot_6_0
	end

	for iter_6_0 = #slot_6_1 + 1, #slot_6_0 do
		slot_6_1[iter_6_0] = 0
	end
end

local ove_0_20

local function ove_0_21(arg_7_0)
	-- function 7
	local slot_7_0 = ove_0_20(arg_7_0)
	local slot_7_1 = math.max(#slot_7_0, 32)

	for iter_7_0 = 1, slot_7_1 do
		if slot_7_0[iter_7_0] == 1 then
			slot_7_0[iter_7_0] = 0
		else
			slot_7_0[iter_7_0] = 1
		end
	end

	return ove_0_18(slot_7_0)
end

function ove_0_20(arg_8_0)
	-- function 8
	if arg_8_0 < 0 then
		return ove_0_20(ove_0_21(math.abs(arg_8_0)) + 1)
	end

	local slot_8_0 = {}
	local slot_8_1 = 1
	local slot_8_2

	while arg_8_0 > 0 do
		local slot_8_3 = arg_8_0 % 2

		slot_8_0[slot_8_1] = slot_8_3
		arg_8_0 = (arg_8_0 - slot_8_3) / 2
		slot_8_1 = slot_8_1 + 1
	end

	return slot_8_0
end

local function ove_0_22(arg_9_0, arg_9_1)
	-- function 9
	local slot_9_0 = ove_0_20(arg_9_0)
	local slot_9_1 = ove_0_20(arg_9_1)

	ove_0_19(slot_9_0, slot_9_1)

	local slot_9_2 = {}

	for iter_9_0 = 1, #slot_9_0 do
		if slot_9_0[iter_9_0] == 0 and slot_9_1[iter_9_0] == 0 then
			slot_9_2[iter_9_0] = 0
		else
			slot_9_2[iter_9_0] = 1
		end
	end

	return ove_0_18(slot_9_2)
end

local function ove_0_23(arg_10_0, arg_10_1)
	-- function 10
	local slot_10_0 = ove_0_20(arg_10_0)
	local slot_10_1 = ove_0_20(arg_10_1)

	ove_0_19(slot_10_0, slot_10_1)

	local slot_10_2 = {}

	for iter_10_0 = 1, #slot_10_0 do
		if slot_10_0[iter_10_0] == 0 or slot_10_1[iter_10_0] == 0 then
			slot_10_2[iter_10_0] = 0
		else
			slot_10_2[iter_10_0] = 1
		end
	end

	return ove_0_18(slot_10_2)
end

local function ove_0_24(arg_11_0, arg_11_1)
	-- function 11
	local slot_11_0 = ove_0_20(arg_11_0)
	local slot_11_1 = ove_0_20(arg_11_1)

	ove_0_19(slot_11_0, slot_11_1)

	local slot_11_2 = {}

	for iter_11_0 = 1, #slot_11_0 do
		if slot_11_0[iter_11_0] ~= slot_11_1[iter_11_0] then
			slot_11_2[iter_11_0] = 1
		else
			slot_11_2[iter_11_0] = 0
		end
	end

	return ove_0_18(slot_11_2)
end

local function ove_0_25(arg_12_0, arg_12_1)
	-- function 12
	local slot_12_0 = 0

	if arg_12_0 < 0 then
		arg_12_0 = ove_0_21(math.abs(arg_12_0)) + 1
		slot_12_0 = 2147483648
	end

	local slot_12_1 = math.floor

	for iter_12_0 = 1, arg_12_1 do
		arg_12_0 = arg_12_0 / 2
		arg_12_0 = ove_0_22(slot_12_1(arg_12_0), slot_12_0)
	end

	return slot_12_1(arg_12_0)
end

local function ove_0_26(arg_13_0, arg_13_1)
	-- function 13
	if arg_13_0 < 0 then
		arg_13_0 = ove_0_21(math.abs(arg_13_0)) + 1
	end

	for iter_13_0 = 1, arg_13_1 do
		arg_13_0 = arg_13_0 * 2
	end

	return ove_0_23(arg_13_0, 4294967295)
end

local function ove_0_27(arg_14_0)
	-- function 14
	local function slot_14_0(arg_15_0)
		-- function 15
		return ove_0_7(ove_0_23(ove_0_25(arg_14_0, arg_15_0), 255))
	end

	return slot_14_0(0) .. slot_14_0(8) .. slot_14_0(16) .. slot_14_0(24)
end

local function ove_0_28(arg_16_0)
	-- function 16
	local slot_16_0 = 0

	for iter_16_0 = 1, #arg_16_0 do
		slot_16_0 = slot_16_0 * 256 + ove_0_8(arg_16_0, iter_16_0)
	end

	return slot_16_0
end

local function ove_0_29(arg_17_0)
	-- function 17
	local slot_17_0 = 0

	for iter_17_0 = #arg_17_0, 1, -1 do
		slot_17_0 = slot_17_0 * 256 + ove_0_8(arg_17_0, iter_17_0)
	end

	return slot_17_0
end

local function ove_0_30(arg_18_0, ...)
	-- function 18
	local slot_18_0 = 1
	local slot_18_1 = {}
	local slot_18_2 = {
		...
	}

	for iter_18_0 = 1, #slot_18_2 do
		table.insert(slot_18_1, ove_0_29(ove_0_11(arg_18_0, slot_18_0, slot_18_0 + slot_18_2[iter_18_0] - 1)))

		slot_18_0 = slot_18_0 + slot_18_2[iter_18_0]
	end

	return slot_18_1
end

local function ove_0_31(arg_19_0)
	-- function 19
	return ove_0_28(ove_0_27(arg_19_0))
end

local ove_0_32 = {
	3614090360,
	3905402710,
	606105819,
	3250441966,
	4118548399,
	1200080426,
	2821735955,
	4249261313,
	1770035416,
	2336552879,
	4294925233,
	2304563134,
	1804603682,
	4254626195,
	2792965006,
	1236535329,
	4129170786,
	3225465664,
	643717713,
	3921069994,
	3593408605,
	38016083,
	3634488961,
	3889429448,
	568446438,
	3275163606,
	4107603335,
	1163531501,
	2850285829,
	4243563512,
	1735328473,
	2368359562,
	4294588738,
	2272392833,
	1839030562,
	4259657740,
	2763975236,
	1272893353,
	4139469664,
	3200236656,
	681279174,
	3936430074,
	3572445317,
	76029189,
	3654602809,
	3873151461,
	530742520,
	3299628645,
	4096336452,
	1126891415,
	2878612391,
	4237533241,
	1700485571,
	2399980690,
	4293915773,
	2240044497,
	1873313359,
	4264355552,
	2734768916,
	1309151649,
	4149444226,
	3174756917,
	718787259,
	3951481745,
	1732584193,
	4023233417,
	2562383102,
	271733878
}

local function ove_0_33(arg_20_0, arg_20_1, arg_20_2)
	-- function 20
	return ove_0_22(ove_0_23(arg_20_0, arg_20_1), ove_0_23(-arg_20_0 - 1, arg_20_2))
end

local function ove_0_34(arg_21_0, arg_21_1, arg_21_2)
	-- function 21
	return ove_0_22(ove_0_23(arg_21_0, arg_21_2), ove_0_23(arg_21_1, -arg_21_2 - 1))
end

local function ove_0_35(arg_22_0, arg_22_1, arg_22_2)
	-- function 22
	return ove_0_24(arg_22_0, ove_0_24(arg_22_1, arg_22_2))
end

local function ove_0_36(arg_23_0, arg_23_1, arg_23_2)
	-- function 23
	return ove_0_24(arg_23_1, ove_0_22(arg_23_0, -arg_23_2 - 1))
end

local function ove_0_37(arg_24_0, arg_24_1, arg_24_2, arg_24_3, arg_24_4, arg_24_5, arg_24_6, arg_24_7)
	-- function 24
	arg_24_1 = ove_0_23(arg_24_1 + arg_24_0(arg_24_2, arg_24_3, arg_24_4) + arg_24_5 + arg_24_7, 4294967295)

	return ove_0_22(ove_0_26(ove_0_23(arg_24_1, ove_0_25(4294967295, arg_24_6)), arg_24_6), ove_0_25(arg_24_1, 32 - arg_24_6)) + arg_24_2
end

local function ove_0_38(arg_25_0, arg_25_1, arg_25_2, arg_25_3, arg_25_4)
	-- function 25
	local slot_25_0 = arg_25_0
	local slot_25_1 = arg_25_1
	local slot_25_2 = arg_25_2
	local slot_25_3 = arg_25_3
	local slot_25_4 = ove_0_32
	local slot_25_5 = ove_0_37(ove_0_33, slot_25_0, slot_25_1, slot_25_2, slot_25_3, arg_25_4[0], 7, slot_25_4[1])
	local slot_25_6 = ove_0_37(ove_0_33, slot_25_3, slot_25_5, slot_25_1, slot_25_2, arg_25_4[1], 12, slot_25_4[2])
	local slot_25_7 = ove_0_37(ove_0_33, slot_25_2, slot_25_6, slot_25_5, slot_25_1, arg_25_4[2], 17, slot_25_4[3])
	local slot_25_8 = ove_0_37(ove_0_33, slot_25_1, slot_25_7, slot_25_6, slot_25_5, arg_25_4[3], 22, slot_25_4[4])
	local slot_25_9 = ove_0_37(ove_0_33, slot_25_5, slot_25_8, slot_25_7, slot_25_6, arg_25_4[4], 7, slot_25_4[5])
	local slot_25_10 = ove_0_37(ove_0_33, slot_25_6, slot_25_9, slot_25_8, slot_25_7, arg_25_4[5], 12, slot_25_4[6])
	local slot_25_11 = ove_0_37(ove_0_33, slot_25_7, slot_25_10, slot_25_9, slot_25_8, arg_25_4[6], 17, slot_25_4[7])
	local slot_25_12 = ove_0_37(ove_0_33, slot_25_8, slot_25_11, slot_25_10, slot_25_9, arg_25_4[7], 22, slot_25_4[8])
	local slot_25_13 = ove_0_37(ove_0_33, slot_25_9, slot_25_12, slot_25_11, slot_25_10, arg_25_4[8], 7, slot_25_4[9])
	local slot_25_14 = ove_0_37(ove_0_33, slot_25_10, slot_25_13, slot_25_12, slot_25_11, arg_25_4[9], 12, slot_25_4[10])
	local slot_25_15 = ove_0_37(ove_0_33, slot_25_11, slot_25_14, slot_25_13, slot_25_12, arg_25_4[10], 17, slot_25_4[11])
	local slot_25_16 = ove_0_37(ove_0_33, slot_25_12, slot_25_15, slot_25_14, slot_25_13, arg_25_4[11], 22, slot_25_4[12])
	local slot_25_17 = ove_0_37(ove_0_33, slot_25_13, slot_25_16, slot_25_15, slot_25_14, arg_25_4[12], 7, slot_25_4[13])
	local slot_25_18 = ove_0_37(ove_0_33, slot_25_14, slot_25_17, slot_25_16, slot_25_15, arg_25_4[13], 12, slot_25_4[14])
	local slot_25_19 = ove_0_37(ove_0_33, slot_25_15, slot_25_18, slot_25_17, slot_25_16, arg_25_4[14], 17, slot_25_4[15])
	local slot_25_20 = ove_0_37(ove_0_33, slot_25_16, slot_25_19, slot_25_18, slot_25_17, arg_25_4[15], 22, slot_25_4[16])
	local slot_25_21 = ove_0_37(ove_0_34, slot_25_17, slot_25_20, slot_25_19, slot_25_18, arg_25_4[1], 5, slot_25_4[17])
	local slot_25_22 = ove_0_37(ove_0_34, slot_25_18, slot_25_21, slot_25_20, slot_25_19, arg_25_4[6], 9, slot_25_4[18])
	local slot_25_23 = ove_0_37(ove_0_34, slot_25_19, slot_25_22, slot_25_21, slot_25_20, arg_25_4[11], 14, slot_25_4[19])
	local slot_25_24 = ove_0_37(ove_0_34, slot_25_20, slot_25_23, slot_25_22, slot_25_21, arg_25_4[0], 20, slot_25_4[20])
	local slot_25_25 = ove_0_37(ove_0_34, slot_25_21, slot_25_24, slot_25_23, slot_25_22, arg_25_4[5], 5, slot_25_4[21])
	local slot_25_26 = ove_0_37(ove_0_34, slot_25_22, slot_25_25, slot_25_24, slot_25_23, arg_25_4[10], 9, slot_25_4[22])
	local slot_25_27 = ove_0_37(ove_0_34, slot_25_23, slot_25_26, slot_25_25, slot_25_24, arg_25_4[15], 14, slot_25_4[23])
	local slot_25_28 = ove_0_37(ove_0_34, slot_25_24, slot_25_27, slot_25_26, slot_25_25, arg_25_4[4], 20, slot_25_4[24])
	local slot_25_29 = ove_0_37(ove_0_34, slot_25_25, slot_25_28, slot_25_27, slot_25_26, arg_25_4[9], 5, slot_25_4[25])
	local slot_25_30 = ove_0_37(ove_0_34, slot_25_26, slot_25_29, slot_25_28, slot_25_27, arg_25_4[14], 9, slot_25_4[26])
	local slot_25_31 = ove_0_37(ove_0_34, slot_25_27, slot_25_30, slot_25_29, slot_25_28, arg_25_4[3], 14, slot_25_4[27])
	local slot_25_32 = ove_0_37(ove_0_34, slot_25_28, slot_25_31, slot_25_30, slot_25_29, arg_25_4[8], 20, slot_25_4[28])
	local slot_25_33 = ove_0_37(ove_0_34, slot_25_29, slot_25_32, slot_25_31, slot_25_30, arg_25_4[13], 5, slot_25_4[29])
	local slot_25_34 = ove_0_37(ove_0_34, slot_25_30, slot_25_33, slot_25_32, slot_25_31, arg_25_4[2], 9, slot_25_4[30])
	local slot_25_35 = ove_0_37(ove_0_34, slot_25_31, slot_25_34, slot_25_33, slot_25_32, arg_25_4[7], 14, slot_25_4[31])
	local slot_25_36 = ove_0_37(ove_0_34, slot_25_32, slot_25_35, slot_25_34, slot_25_33, arg_25_4[12], 20, slot_25_4[32])
	local slot_25_37 = ove_0_37(ove_0_35, slot_25_33, slot_25_36, slot_25_35, slot_25_34, arg_25_4[5], 4, slot_25_4[33])
	local slot_25_38 = ove_0_37(ove_0_35, slot_25_34, slot_25_37, slot_25_36, slot_25_35, arg_25_4[8], 11, slot_25_4[34])
	local slot_25_39 = ove_0_37(ove_0_35, slot_25_35, slot_25_38, slot_25_37, slot_25_36, arg_25_4[11], 16, slot_25_4[35])
	local slot_25_40 = ove_0_37(ove_0_35, slot_25_36, slot_25_39, slot_25_38, slot_25_37, arg_25_4[14], 23, slot_25_4[36])
	local slot_25_41 = ove_0_37(ove_0_35, slot_25_37, slot_25_40, slot_25_39, slot_25_38, arg_25_4[1], 4, slot_25_4[37])
	local slot_25_42 = ove_0_37(ove_0_35, slot_25_38, slot_25_41, slot_25_40, slot_25_39, arg_25_4[4], 11, slot_25_4[38])
	local slot_25_43 = ove_0_37(ove_0_35, slot_25_39, slot_25_42, slot_25_41, slot_25_40, arg_25_4[7], 16, slot_25_4[39])
	local slot_25_44 = ove_0_37(ove_0_35, slot_25_40, slot_25_43, slot_25_42, slot_25_41, arg_25_4[10], 23, slot_25_4[40])
	local slot_25_45 = ove_0_37(ove_0_35, slot_25_41, slot_25_44, slot_25_43, slot_25_42, arg_25_4[13], 4, slot_25_4[41])
	local slot_25_46 = ove_0_37(ove_0_35, slot_25_42, slot_25_45, slot_25_44, slot_25_43, arg_25_4[0], 11, slot_25_4[42])
	local slot_25_47 = ove_0_37(ove_0_35, slot_25_43, slot_25_46, slot_25_45, slot_25_44, arg_25_4[3], 16, slot_25_4[43])
	local slot_25_48 = ove_0_37(ove_0_35, slot_25_44, slot_25_47, slot_25_46, slot_25_45, arg_25_4[6], 23, slot_25_4[44])
	local slot_25_49 = ove_0_37(ove_0_35, slot_25_45, slot_25_48, slot_25_47, slot_25_46, arg_25_4[9], 4, slot_25_4[45])
	local slot_25_50 = ove_0_37(ove_0_35, slot_25_46, slot_25_49, slot_25_48, slot_25_47, arg_25_4[12], 11, slot_25_4[46])
	local slot_25_51 = ove_0_37(ove_0_35, slot_25_47, slot_25_50, slot_25_49, slot_25_48, arg_25_4[15], 16, slot_25_4[47])
	local slot_25_52 = ove_0_37(ove_0_35, slot_25_48, slot_25_51, slot_25_50, slot_25_49, arg_25_4[2], 23, slot_25_4[48])
	local slot_25_53 = ove_0_37(ove_0_36, slot_25_49, slot_25_52, slot_25_51, slot_25_50, arg_25_4[0], 6, slot_25_4[49])
	local slot_25_54 = ove_0_37(ove_0_36, slot_25_50, slot_25_53, slot_25_52, slot_25_51, arg_25_4[7], 10, slot_25_4[50])
	local slot_25_55 = ove_0_37(ove_0_36, slot_25_51, slot_25_54, slot_25_53, slot_25_52, arg_25_4[14], 15, slot_25_4[51])
	local slot_25_56 = ove_0_37(ove_0_36, slot_25_52, slot_25_55, slot_25_54, slot_25_53, arg_25_4[5], 21, slot_25_4[52])
	local slot_25_57 = ove_0_37(ove_0_36, slot_25_53, slot_25_56, slot_25_55, slot_25_54, arg_25_4[12], 6, slot_25_4[53])
	local slot_25_58 = ove_0_37(ove_0_36, slot_25_54, slot_25_57, slot_25_56, slot_25_55, arg_25_4[3], 10, slot_25_4[54])
	local slot_25_59 = ove_0_37(ove_0_36, slot_25_55, slot_25_58, slot_25_57, slot_25_56, arg_25_4[10], 15, slot_25_4[55])
	local slot_25_60 = ove_0_37(ove_0_36, slot_25_56, slot_25_59, slot_25_58, slot_25_57, arg_25_4[1], 21, slot_25_4[56])
	local slot_25_61 = ove_0_37(ove_0_36, slot_25_57, slot_25_60, slot_25_59, slot_25_58, arg_25_4[8], 6, slot_25_4[57])
	local slot_25_62 = ove_0_37(ove_0_36, slot_25_58, slot_25_61, slot_25_60, slot_25_59, arg_25_4[15], 10, slot_25_4[58])
	local slot_25_63 = ove_0_37(ove_0_36, slot_25_59, slot_25_62, slot_25_61, slot_25_60, arg_25_4[6], 15, slot_25_4[59])
	local slot_25_64 = ove_0_37(ove_0_36, slot_25_60, slot_25_63, slot_25_62, slot_25_61, arg_25_4[13], 21, slot_25_4[60])
	local slot_25_65 = ove_0_37(ove_0_36, slot_25_61, slot_25_64, slot_25_63, slot_25_62, arg_25_4[4], 6, slot_25_4[61])
	local slot_25_66 = ove_0_37(ove_0_36, slot_25_62, slot_25_65, slot_25_64, slot_25_63, arg_25_4[11], 10, slot_25_4[62])
	local slot_25_67 = ove_0_37(ove_0_36, slot_25_63, slot_25_66, slot_25_65, slot_25_64, arg_25_4[2], 15, slot_25_4[63])
	local slot_25_68 = ove_0_37(ove_0_36, slot_25_64, slot_25_67, slot_25_66, slot_25_65, arg_25_4[9], 21, slot_25_4[64])

	return ove_0_23(arg_25_0 + slot_25_65, 4294967295), ove_0_23(arg_25_1 + slot_25_68, 4294967295), ove_0_23(arg_25_2 + slot_25_67, 4294967295), ove_0_23(arg_25_3 + slot_25_66, 4294967295)
end

local function ove_0_39(arg_26_0, arg_26_1)
	-- function 26
	arg_26_0.pos = arg_26_0.pos + #arg_26_1
	arg_26_1 = arg_26_0.buf .. arg_26_1

	for iter_26_0 = 1, #arg_26_1 - 63, 64 do
		local slot_26_0 = ove_0_30(ove_0_11(arg_26_1, iter_26_0, iter_26_0 + 63), 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4)

		assert(#slot_26_0 == 16)

		slot_26_0[0] = table.remove(slot_26_0, 1)
		arg_26_0.a, arg_26_0.b, arg_26_0.c, arg_26_0.d = ove_0_38(arg_26_0.a, arg_26_0.b, arg_26_0.c, arg_26_0.d, slot_26_0)
	end

	arg_26_0.buf = ove_0_11(arg_26_1, math.floor(#arg_26_1 / 64) * 64 + 1, #arg_26_1)

	return arg_26_0
end

local function ove_0_40(arg_27_0)
	-- function 27
	local slot_27_0 = arg_27_0.pos
	local slot_27_1 = 56 - slot_27_0 % 64

	if slot_27_0 % 64 > 56 then
		slot_27_1 = slot_27_1 + 64
	end

	if slot_27_1 == 0 then
		slot_27_1 = 64
	end

	local slot_27_2 = ove_0_7(128) .. ove_0_10(ove_0_7(0), slot_27_1 - 1) .. ove_0_27(ove_0_23(8 * slot_27_0, 4294967295)) .. ove_0_27(math.floor(slot_27_0 / 536870912))

	ove_0_39(arg_27_0, slot_27_2)
	assert(arg_27_0.pos % 64 == 0)

	return ove_0_27(arg_27_0.a) .. ove_0_27(arg_27_0.b) .. ove_0_27(arg_27_0.c) .. ove_0_27(arg_27_0.d)
end

function ove_0_6.new()
	-- function 28
	return {
		buf = "",
		pos = 0,
		a = ove_0_32[65],
		b = ove_0_32[66],
		c = ove_0_32[67],
		d = ove_0_32[68],
		update = ove_0_39,
		finish = ove_0_40
	}
end

function ove_0_6.tohex(arg_29_0)
	-- function 29
	return ove_0_9("%08x%08x%08x%08x", ove_0_28(ove_0_11(arg_29_0, 1, 4)), ove_0_28(ove_0_11(arg_29_0, 5, 8)), ove_0_28(ove_0_11(arg_29_0, 9, 12)), ove_0_28(ove_0_11(arg_29_0, 13, 16)))
end

function ove_0_6.sum(arg_30_0)
	-- function 30
	return ove_0_6.new():update(arg_30_0):finish()
end

function ove_0_6.sumhexa(arg_31_0)
	-- function 31
	return ove_0_6.tohex(ove_0_6.sum(arg_31_0))
end

function ove_0_6.sumhexaUp(arg_32_0)
	-- function 32
	return string.upper(ove_0_6.tohex(ove_0_6.sum(arg_32_0)))
end

function ove_0_6.sumhexaLo(arg_33_0)
	-- function 33
	return string.lower(ove_0_6.tohex(ove_0_6.sum(arg_33_0)))
end

return ove_0_6

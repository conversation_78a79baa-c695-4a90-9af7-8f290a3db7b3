
return {
	animate = function(arg_5_0, arg_5_1)
		-- function 5
		if arg_5_0.combo.combo_q:get() then
			arg_5_0.combo.combo_q_mana:set("visible", true)
			arg_5_0.combo.combo_q_filter:set("visible", true)
		else
			arg_5_0.combo.combo_q_mana:set("visible", false)
			arg_5_0.combo.combo_q_filter:set("visible", false)
		end

		arg_5_0.combo.combo_q:set("callback", function(arg_6_0, arg_6_1)
			-- function 6
			if arg_6_1 then
				arg_5_0.combo.combo_q_mana:set("visible", true)
				arg_5_0.combo.combo_q_filter:set("visible", true)
			end

			if arg_6_0 then
				arg_5_0.combo.combo_q_mana:set("visible", false)
				arg_5_0.combo.combo_q_filter:set("visible", false)
			end
		end)

		if arg_5_0.combo.combo_e:get() then
			arg_5_0.combo.combo_e_mana:set("visible", true)
			arg_5_0.combo.combo_e_filter:set("visible", true)
		else
			arg_5_0.combo.combo_e_mana:set("visible", false)
			arg_5_0.combo.combo_e_filter:set("visible", false)
		end

		arg_5_0.combo.combo_e:set("callback", function(arg_7_0, arg_7_1)
			-- function 7
			if arg_7_1 then
				arg_5_0.combo.combo_e_mana:set("visible", true)
				arg_5_0.combo.combo_e_filter:set("visible", true)
			end

			if arg_7_0 then
				arg_5_0.combo.combo_e_mana:set("visible", false)
				arg_5_0.combo.combo_e_filter:set("visible", false)
			end
		end)

		if arg_5_0.harass.harass_q:get() then
			arg_5_0.harass.harass_q_mana:set("visible", true)
			arg_5_0.harass.harass_q_filter:set("visible", true)
		else
			arg_5_0.harass.harass_q_mana:set("visible", false)
			arg_5_0.harass.harass_q_filter:set("visible", false)
		end

		arg_5_0.harass.harass_q:set("callback", function(arg_8_0, arg_8_1)
			-- function 8
			if arg_8_1 then
				arg_5_0.harass.harass_q_mana:set("visible", true)
				arg_5_0.harass.harass_q_filter:set("visible", true)
			end

			if arg_8_0 then
				arg_5_0.harass.harass_q_mana:set("visible", false)
				arg_5_0.harass.harass_q_filter:set("visible", false)
			end
		end)

		if arg_5_0.harass.harass_e:get() then
			arg_5_0.harass.harass_e_mana:set("visible", true)
			arg_5_0.harass.harass_e_filter:set("visible", true)
		else
			arg_5_0.harass.harass_e_mana:set("visible", false)
			arg_5_0.harass.harass_e_filter:set("visible", false)
		end

		arg_5_0.harass.harass_e:set("callback", function(arg_9_0, arg_9_1)
			-- function 9
			if arg_9_1 then
				arg_5_0.harass.harass_e_mana:set("visible", true)
				arg_5_0.harass.harass_e_filter:set("visible", true)
			end

			if arg_9_0 then
				arg_5_0.harass.harass_e_mana:set("visible", false)
				arg_5_0.harass.harass_e_filter:set("visible", false)
			end
		end)

		if arg_5_0.auto.auto_q:get() then
			arg_5_0.auto.auto_q_mana:set("visible", true)
			arg_5_0.auto.auto_q_filter:set("visible", true)
			arg_5_0.auto.auto_q_turret:set("visible", true)
		else
			arg_5_0.auto.auto_q_mana:set("visible", false)
			arg_5_0.auto.auto_q_filter:set("visible", false)
			arg_5_0.auto.auto_q_turret:set("visible", false)
		end

		arg_5_0.auto.auto_q:set("callback", function(arg_10_0, arg_10_1)
			-- function 10
			if arg_10_1 then
				arg_5_0.auto.auto_q_mana:set("visible", true)
				arg_5_0.auto.auto_q_filter:set("visible", true)
				arg_5_0.auto.auto_q_turret:set("visible", true)
			end

			if arg_10_0 then
				arg_5_0.auto.auto_q_mana:set("visible", false)
				arg_5_0.auto.auto_q_filter:set("visible", false)
				arg_5_0.auto.auto_q_turret:set("visible", false)
			end
		end)

		if arg_5_0.auto.auto_e:get() then
			arg_5_0.auto.auto_e_mana:set("visible", true)
			arg_5_0.auto.auto_e_filter:set("visible", true)
			arg_5_0.auto.auto_e_turret:set("visible", true)
		else
			arg_5_0.auto.auto_e_mana:set("visible", false)
			arg_5_0.auto.auto_e_filter:set("visible", false)
			arg_5_0.auto.auto_e_turret:set("visible", false)
		end

		arg_5_0.auto.auto_e:set("callback", function(arg_11_0, arg_11_1)
			-- function 11
			if arg_11_1 then
				arg_5_0.auto.auto_e_mana:set("visible", true)
				arg_5_0.auto.auto_e_filter:set("visible", true)
				arg_5_0.auto.auto_e_turret:set("visible", true)
			end

			if arg_11_0 then
				arg_5_0.auto.auto_e_mana:set("visible", false)
				arg_5_0.auto.auto_e_filter:set("visible", false)
				arg_5_0.auto.auto_e_turret:set("visible", false)
			end
		end)

		if arg_5_0.farm.help.help_q:get() then
			arg_5_0.farm.help.help_q_aa_range:set("visible", true)
			arg_5_0.farm.help.help_q_cannon:set("visible", true)
			arg_5_0.farm.help.help_q_dis:set("visible", true)
			arg_5_0.farm.help.help_q_dis_double:set("visible", true)
			arg_5_0.farm.help.help_q_mana:set("visible", true)
		else
			arg_5_0.farm.help.help_q_aa_range:set("visible", false)
			arg_5_0.farm.help.help_q_cannon:set("visible", false)
			arg_5_0.farm.help.help_q_dis:set("visible", false)
			arg_5_0.farm.help.help_q_dis_double:set("visible", false)
			arg_5_0.farm.help.help_q_mana:set("visible", false)
		end

		arg_5_0.farm.help.help_q:set("callback", function(arg_12_0, arg_12_1)
			-- function 12
			if arg_12_1 then
				arg_5_0.farm.help.help_q_aa_range:set("visible", true)
				arg_5_0.farm.help.help_q_cannon:set("visible", true)
				arg_5_0.farm.help.help_q_dis:set("visible", true)
				arg_5_0.farm.help.help_q_dis_double:set("visible", true)
				arg_5_0.farm.help.help_q_mana:set("visible", true)
			end

			if arg_12_0 then
				arg_5_0.farm.help.help_q_aa_range:set("visible", false)
				arg_5_0.farm.help.help_q_cannon:set("visible", false)
				arg_5_0.farm.help.help_q_dis:set("visible", false)
				arg_5_0.farm.help.help_q_dis_double:set("visible", false)
				arg_5_0.farm.help.help_q_mana:set("visible", false)
			end
		end)

		if arg_5_0.farm.help.help_e:get() then
			arg_5_0.farm.help.help_e_aa_range:set("visible", true)
			arg_5_0.farm.help.help_e_cannon:set("visible", true)
			arg_5_0.farm.help.help_e_dis:set("visible", true)
			arg_5_0.farm.help.help_e_dis_double:set("visible", true)
			arg_5_0.farm.help.help_e_mana:set("visible", true)
		else
			arg_5_0.farm.help.help_e_aa_range:set("visible", false)
			arg_5_0.farm.help.help_e_cannon:set("visible", false)
			arg_5_0.farm.help.help_e_dis:set("visible", false)
			arg_5_0.farm.help.help_e_dis_double:set("visible", false)
			arg_5_0.farm.help.help_e_mana:set("visible", false)
		end

		arg_5_0.farm.help.help_e:set("callback", function(arg_13_0, arg_13_1)
			-- function 13
			if arg_13_1 then
				arg_5_0.farm.help.help_e_aa_range:set("visible", true)
				arg_5_0.farm.help.help_e_cannon:set("visible", true)
				arg_5_0.farm.help.help_e_dis:set("visible", true)
				arg_5_0.farm.help.help_e_dis_double:set("visible", true)
				arg_5_0.farm.help.help_e_mana:set("visible", true)
			end

			if arg_13_0 then
				arg_5_0.farm.help.help_e_aa_range:set("visible", false)
				arg_5_0.farm.help.help_e_cannon:set("visible", false)
				arg_5_0.farm.help.help_e_dis:set("visible", false)
				arg_5_0.farm.help.help_e_dis_double:set("visible", false)
				arg_5_0.farm.help.help_e_mana:set("visible", false)
			end
		end)

		if arg_5_0.farm.lane.lane_q:get() then
			arg_5_0.farm.lane.lane_q_count:set("visible", true)
			arg_5_0.farm.lane.lane_q_dis:set("visible", true)
			arg_5_0.farm.lane.lane_q_double:set("visible", true)
			arg_5_0.farm.lane.lane_q_mana:set("visible", true)
			arg_5_0.farm.lane.lane_q_move_dis:set("visible", true)

			if arg_5_0.farm.lane.lane_q_move_dis:get() then
				arg_5_0.farm.lane.lane_q_move_allow_count:set("visible", true)
			else
				arg_5_0.farm.lane.lane_q_move_allow_count:set("visible", false)
			end
		else
			arg_5_0.farm.lane.lane_q_count:set("visible", false)
			arg_5_0.farm.lane.lane_q_dis:set("visible", false)
			arg_5_0.farm.lane.lane_q_double:set("visible", false)
			arg_5_0.farm.lane.lane_q_mana:set("visible", false)
			arg_5_0.farm.lane.lane_q_move_dis:set("visible", false)
			arg_5_0.farm.lane.lane_q_move_allow_count:set("visible", false)
		end

		arg_5_0.farm.lane.lane_q:set("callback", function(arg_14_0, arg_14_1)
			-- function 14
			if arg_14_1 then
				arg_5_0.farm.lane.lane_q_count:set("visible", true)
				arg_5_0.farm.lane.lane_q_dis:set("visible", true)
				arg_5_0.farm.lane.lane_q_double:set("visible", true)
				arg_5_0.farm.lane.lane_q_mana:set("visible", true)
				arg_5_0.farm.lane.lane_q_move_dis:set("visible", true)

				if arg_5_0.farm.lane.lane_q_move_dis:get() then
					arg_5_0.farm.lane.lane_q_move_allow_count:set("visible", true)
				else
					arg_5_0.farm.lane.lane_q_move_allow_count:set("visible", false)
				end
			end

			if arg_14_0 then
				arg_5_0.farm.lane.lane_q_count:set("visible", false)
				arg_5_0.farm.lane.lane_q_dis:set("visible", false)
				arg_5_0.farm.lane.lane_q_double:set("visible", false)
				arg_5_0.farm.lane.lane_q_mana:set("visible", false)
				arg_5_0.farm.lane.lane_q_move_dis:set("visible", false)
				arg_5_0.farm.lane.lane_q_move_allow_count:set("visible", false)
			end
		end)
		arg_5_0.farm.lane.lane_q_move_dis:set("callback", function(arg_15_0, arg_15_1)
			-- function 15
			if arg_15_1 then
				arg_5_0.farm.lane.lane_q_move_allow_count:set("visible", true)
			end

			if arg_15_0 then
				arg_5_0.farm.lane.lane_q_move_allow_count:set("visible", false)
			end
		end)

		if arg_5_0.farm.lane.lane_e:get() then
			arg_5_0.farm.lane.lane_e_count:set("visible", true)
			arg_5_0.farm.lane.lane_e_count_max:set("visible", true)
			arg_5_0.farm.lane.lane_e_dis:set("visible", true)
			arg_5_0.farm.lane.lane_e_double:set("visible", true)
			arg_5_0.farm.lane.lane_e_mana:set("visible", true)
			arg_5_0.farm.lane.lane_e_move_dis:set("visible", true)

			if arg_5_0.farm.lane.lane_e_move_dis:get() then
				arg_5_0.farm.lane.lane_e_move_allow_count:set("visible", true)
			else
				arg_5_0.farm.lane.lane_e_move_allow_count:set("visible", false)
			end
		else
			arg_5_0.farm.lane.lane_e_count:set("visible", false)
			arg_5_0.farm.lane.lane_e_count_max:set("visible", false)
			arg_5_0.farm.lane.lane_e_dis:set("visible", false)
			arg_5_0.farm.lane.lane_e_double:set("visible", false)
			arg_5_0.farm.lane.lane_e_mana:set("visible", false)
			arg_5_0.farm.lane.lane_e_move_dis:set("visible", false)
			arg_5_0.farm.lane.lane_e_move_allow_count:set("visible", false)
		end

		arg_5_0.farm.lane.lane_e:set("callback", function(arg_16_0, arg_16_1)
			-- function 16
			if arg_16_1 then
				arg_5_0.farm.lane.lane_e_count:set("visible", true)
				arg_5_0.farm.lane.lane_e_count_max:set("visible", true)
				arg_5_0.farm.lane.lane_e_dis:set("visible", true)
				arg_5_0.farm.lane.lane_e_double:set("visible", true)
				arg_5_0.farm.lane.lane_e_mana:set("visible", true)
				arg_5_0.farm.lane.lane_e_move_dis:set("visible", true)

				if arg_5_0.farm.lane.lane_e_move_dis:get() then
					arg_5_0.farm.lane.lane_e_move_allow_count:set("visible", true)
				else
					arg_5_0.farm.lane.lane_e_move_allow_count:set("visible", false)
				end
			end

			if arg_16_0 then
				arg_5_0.farm.lane.lane_e_count:set("visible", false)
				arg_5_0.farm.lane.lane_e_count_max:set("visible", false)
				arg_5_0.farm.lane.lane_e_dis:set("visible", false)
				arg_5_0.farm.lane.lane_e_double:set("visible", false)
				arg_5_0.farm.lane.lane_e_mana:set("visible", false)
				arg_5_0.farm.lane.lane_e_move_dis:set("visible", false)
				arg_5_0.farm.lane.lane_e_move_allow_count:set("visible", false)
			end
		end)
		arg_5_0.farm.lane.lane_e_move_dis:set("callback", function(arg_17_0, arg_17_1)
			-- function 17
			if arg_17_1 then
				arg_5_0.farm.lane.lane_e_move_allow_count:set("visible", true)
			end

			if arg_17_0 then
				arg_5_0.farm.lane.lane_e_move_allow_count:set("visible", false)
			end
		end)

		if arg_5_0.farm.panic.panic_method:get() == 1 then
			arg_5_0.farm.panic.panic_custom_key:set("visible", false)
			arg_5_0.farm.panic.panic_button_reset:set("visible", false)
		end

		if arg_5_0.farm.panic.panic_method:get() == 2 then
			arg_5_0.farm.panic.panic_custom_key:set("visible", true)
			arg_5_0.farm.panic.panic_button_reset:set("visible", true)
		end

		if arg_5_0.farm.panic.panic_method:get() == 3 then
			arg_5_0.farm.panic.panic_custom_key:set("visible", false)
			arg_5_0.farm.panic.panic_button_reset:set("visible", false)
		end

		arg_5_0.farm.panic.panic_method:set("callback", function(arg_18_0, arg_18_1)
			-- function 18
			if arg_5_0.farm.panic.panic_method:get() == 1 then
				arg_5_0.farm.panic.panic_custom_key:set("visible", false)
				arg_5_0.farm.panic.panic_button_reset:set("visible", false)
			end

			if arg_5_0.farm.panic.panic_method:get() == 2 then
				arg_5_0.farm.panic.panic_custom_key:set("visible", true)
				arg_5_0.farm.panic.panic_button_reset:set("visible", true)
			end

			if arg_5_0.farm.panic.panic_method:get() == 3 then
				arg_5_0.farm.panic.panic_custom_key:set("visible", false)
				arg_5_0.farm.panic.panic_button_reset:set("visible", false)
			end
		end)

		if arg_5_0.farm.panic.panic_q:get() then
			arg_5_0.farm.panic.panic_q_count:set("visible", true)
			arg_5_0.farm.panic.panic_q_dis:set("visible", true)
			arg_5_0.farm.panic.panic_q_double:set("visible", true)
			arg_5_0.farm.panic.panic_q_mana:set("visible", true)
			arg_5_0.farm.panic.panic_q_move_dis:set("visible", true)

			if arg_5_0.farm.panic.panic_q_move_dis:get() then
				arg_5_0.farm.panic.panic_q_move_allow_count:set("visible", true)
			else
				arg_5_0.farm.panic.panic_q_move_allow_count:set("visible", false)
			end
		else
			arg_5_0.farm.panic.panic_q_count:set("visible", false)
			arg_5_0.farm.panic.panic_q_dis:set("visible", false)
			arg_5_0.farm.panic.panic_q_double:set("visible", false)
			arg_5_0.farm.panic.panic_q_mana:set("visible", false)
			arg_5_0.farm.panic.panic_q_move_dis:set("visible", false)
			arg_5_0.farm.panic.panic_q_move_allow_count:set("visible", false)
		end

		arg_5_0.farm.panic.panic_q:set("callback", function(arg_19_0, arg_19_1)
			-- function 19
			if arg_19_1 then
				arg_5_0.farm.panic.panic_q_count:set("visible", true)
				arg_5_0.farm.panic.panic_q_dis:set("visible", true)
				arg_5_0.farm.panic.panic_q_double:set("visible", true)
				arg_5_0.farm.panic.panic_q_mana:set("visible", true)
				arg_5_0.farm.panic.panic_q_move_dis:set("visible", true)

				if arg_5_0.farm.panic.panic_q_move_dis:get() then
					arg_5_0.farm.panic.panic_q_move_allow_count:set("visible", true)
				else
					arg_5_0.farm.panic.panic_q_move_allow_count:set("visible", false)
				end
			end

			if arg_19_0 then
				arg_5_0.farm.panic.panic_q_count:set("visible", false)
				arg_5_0.farm.panic.panic_q_dis:set("visible", false)
				arg_5_0.farm.panic.panic_q_double:set("visible", false)
				arg_5_0.farm.panic.panic_q_mana:set("visible", false)
				arg_5_0.farm.panic.panic_q_move_dis:set("visible", false)
				arg_5_0.farm.panic.panic_q_move_allow_count:set("visible", false)
			end
		end)
		arg_5_0.farm.panic.panic_q_move_dis:set("callback", function(arg_20_0, arg_20_1)
			-- function 20
			if arg_20_1 then
				arg_5_0.farm.panic.panic_q_move_allow_count:set("visible", true)
			end

			if arg_20_0 then
				arg_5_0.farm.panic.panic_q_move_allow_count:set("visible", false)
			end
		end)

		if arg_5_0.farm.panic.panic_e:get() then
			arg_5_0.farm.panic.panic_e_count:set("visible", true)
			arg_5_0.farm.panic.panic_e_count_max:set("visible", true)
			arg_5_0.farm.panic.panic_e_dis:set("visible", true)
			arg_5_0.farm.panic.panic_e_double:set("visible", true)
			arg_5_0.farm.panic.panic_e_mana:set("visible", true)
			arg_5_0.farm.panic.panic_e_move_dis:set("visible", true)

			if arg_5_0.farm.panic.panic_e_move_dis:get() then
				arg_5_0.farm.panic.panic_e_move_allow_count:set("visible", true)
			else
				arg_5_0.farm.panic.panic_e_move_allow_count:set("visible", false)
			end
		else
			arg_5_0.farm.panic.panic_e_count:set("visible", false)
			arg_5_0.farm.panic.panic_e_count_max:set("visible", false)
			arg_5_0.farm.panic.panic_e_dis:set("visible", false)
			arg_5_0.farm.panic.panic_e_double:set("visible", false)
			arg_5_0.farm.panic.panic_e_mana:set("visible", false)
			arg_5_0.farm.panic.panic_e_move_dis:set("visible", false)
			arg_5_0.farm.panic.panic_e_move_allow_count:set("visible", false)
		end

		arg_5_0.farm.panic.panic_e:set("callback", function(arg_21_0, arg_21_1)
			-- function 21
			if arg_21_1 then
				arg_5_0.farm.panic.panic_e_count:set("visible", true)
				arg_5_0.farm.panic.panic_e_count_max:set("visible", true)
				arg_5_0.farm.panic.panic_e_dis:set("visible", true)
				arg_5_0.farm.panic.panic_e_double:set("visible", true)
				arg_5_0.farm.panic.panic_e_mana:set("visible", true)
				arg_5_0.farm.panic.panic_e_move_dis:set("visible", true)

				if arg_5_0.farm.panic.panic_e_move_dis:get() then
					arg_5_0.farm.panic.panic_e_move_allow_count:set("visible", true)
				else
					arg_5_0.farm.panic.panic_e_move_allow_count:set("visible", false)
				end
			end

			if arg_21_0 then
				arg_5_0.farm.panic.panic_e_count:set("visible", false)
				arg_5_0.farm.panic.panic_e_count_max:set("visible", false)
				arg_5_0.farm.panic.panic_e_dis:set("visible", false)
				arg_5_0.farm.panic.panic_e_double:set("visible", false)
				arg_5_0.farm.panic.panic_e_mana:set("visible", false)
				arg_5_0.farm.panic.panic_e_move_dis:set("visible", false)
				arg_5_0.farm.panic.panic_e_move_allow_count:set("visible", false)
			end
		end)
		arg_5_0.farm.panic.panic_e_move_dis:set("callback", function(arg_22_0, arg_22_1)
			-- function 22
			if arg_22_1 then
				arg_5_0.farm.panic.panic_e_move_allow_count:set("visible", true)
			end

			if arg_22_0 then
				arg_5_0.farm.panic.panic_e_move_allow_count:set("visible", false)
			end
		end)

		if arg_5_0.farm.jung.jung_q:get() then
			arg_5_0.farm.jung.jung_q_dis:set("visible", true)
			arg_5_0.farm.jung.jung_q_double:set("visible", true)
			arg_5_0.farm.jung.jung_q_mana:set("visible", true)
		else
			arg_5_0.farm.jung.jung_q_dis:set("visible", false)
			arg_5_0.farm.jung.jung_q_double:set("visible", false)
			arg_5_0.farm.jung.jung_q_mana:set("visible", false)
		end

		arg_5_0.farm.jung.jung_q:set("callback", function(arg_23_0, arg_23_1)
			-- function 23
			if arg_23_1 then
				arg_5_0.farm.jung.jung_q_dis:set("visible", true)
				arg_5_0.farm.jung.jung_q_double:set("visible", true)
				arg_5_0.farm.jung.jung_q_mana:set("visible", true)
			end

			if arg_23_0 then
				arg_5_0.farm.jung.jung_q_dis:set("visible", false)
				arg_5_0.farm.jung.jung_q_double:set("visible", false)
				arg_5_0.farm.jung.jung_q_mana:set("visible", false)
			end
		end)

		if arg_5_0.farm.jung.jung_e:get() then
			arg_5_0.farm.jung.jung_e_count_max:set("visible", true)
			arg_5_0.farm.jung.jung_e_dis:set("visible", true)
			arg_5_0.farm.jung.jung_e_double:set("visible", true)
			arg_5_0.farm.jung.jung_e_mana:set("visible", true)
		else
			arg_5_0.farm.jung.jung_e_count_max:set("visible", false)
			arg_5_0.farm.jung.jung_e_dis:set("visible", false)
			arg_5_0.farm.jung.jung_e_double:set("visible", false)
			arg_5_0.farm.jung.jung_e_mana:set("visible", false)
		end

		arg_5_0.farm.jung.jung_e:set("callback", function(arg_24_0, arg_24_1)
			-- function 24
			if arg_24_1 then
				arg_5_0.farm.jung.jung_e_count_max:set("visible", true)
				arg_5_0.farm.jung.jung_e_dis:set("visible", true)
				arg_5_0.farm.jung.jung_e_double:set("visible", true)
				arg_5_0.farm.jung.jung_e_mana:set("visible", true)
			end

			if arg_24_0 then
				arg_5_0.farm.jung.jung_e_count_max:set("visible", false)
				arg_5_0.farm.jung.jung_e_dis:set("visible", false)
				arg_5_0.farm.jung.jung_e_double:set("visible", false)
				arg_5_0.farm.jung.jung_e_mana:set("visible", false)
			end
		end)

		if arg_5_0.draww.tp.tp_enable:get() then
			arg_5_0.draww.tp.tp_world:set("visible", true)
			arg_5_0.draww.tp.tp_world_color:set("visible", true)
			arg_5_0.draww.tp.tp_minimap:set("visible", true)
			arg_5_0.draww.tp.tp_minimap_color:set("visible", true)
			arg_5_0.draww.tp.tp_arrow:set("visible", true)
			arg_5_0.draww.tp.tp_arrow_color:set("visible", true)
			arg_5_0.draww.tp.tp_arrow_nearby:set("visible", true)
		else
			arg_5_0.draww.tp.tp_world:set("visible", false)
			arg_5_0.draww.tp.tp_world_color:set("visible", false)
			arg_5_0.draww.tp.tp_minimap:set("visible", false)
			arg_5_0.draww.tp.tp_minimap_color:set("visible", false)
			arg_5_0.draww.tp.tp_arrow:set("visible", false)
			arg_5_0.draww.tp.tp_arrow_color:set("visible", false)
			arg_5_0.draww.tp.tp_arrow_nearby:set("visible", false)
		end

		arg_5_0.draww.tp.tp_enable:set("callback", function(arg_25_0, arg_25_1)
			-- function 25
			if arg_25_1 then
				arg_5_0.draww.tp.tp_world:set("visible", true)
				arg_5_0.draww.tp.tp_world_color:set("visible", true)
				arg_5_0.draww.tp.tp_minimap:set("visible", true)
				arg_5_0.draww.tp.tp_minimap_color:set("visible", true)
				arg_5_0.draww.tp.tp_arrow:set("visible", true)
				arg_5_0.draww.tp.tp_arrow_color:set("visible", true)
				arg_5_0.draww.tp.tp_arrow_nearby:set("visible", true)
			end

			if arg_25_0 then
				arg_5_0.draww.tp.tp_world:set("visible", false)
				arg_5_0.draww.tp.tp_world_color:set("visible", false)
				arg_5_0.draww.tp.tp_minimap:set("visible", false)
				arg_5_0.draww.tp.tp_minimap_color:set("visible", false)
				arg_5_0.draww.tp.tp_arrow:set("visible", false)
				arg_5_0.draww.tp.tp_arrow_color:set("visible", false)
				arg_5_0.draww.tp.tp_arrow_nearby:set("visible", false)
			end
		end)
	end
}

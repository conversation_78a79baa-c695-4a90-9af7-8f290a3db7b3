local buffManager = {}

-- 检查目标是否具有特定的 Buff
function buffManager.HasBuff(source, buffName)
    if not buffName or type(buffName) ~= "string" then
        return false
    end
    if not source or not source.buff then
        return false
    end
    local lowerbuffName = string.lower(buffName)
    local buff = source.buff[lowerbuffName]
    if buff and buff.endTime and buff.endTime >= game.time then
        return true
    end
    return false
end

-- 检查目标是否具有指定类型的 Buff
function buffManager.HasBuffOfType(source, buffType)
    if not buffType or type(buffType) ~= "number" then
        return false
    end
    if not source or not source.buff then
        return false
    end
    local buff = source.buff[buffType]
    if buff and buff.endTime and buff.endTime >= game.time then
        return true
    end
    return false
end

-- 获取目标的指定 Buff
function buffManager.GetBuff(source, buffNameOrType)
    if not buffNameOrType then return nil end
    if not source or not source.buff then return nil end
    if type(buffNameOrType) == "string" then
        local lowerbuffName = string.lower(buffNameOrType)
        local buff = source.buff[lowerbuffName]
        if buff and buff.endTime and buff.endTime >= game.time then
            return buff
        end
    elseif type(buffNameOrType) == "number" then
        local buff = source.buff[buffNameOrType]
        if buff and buff.endTime and buff.endTime >= game.time then
            return buff
        end
    end
    return nil
end

-- 获取目标的指定 Buff 的堆叠数
function buffManager.GetBuffCount(source, buffName)
    if not buffName or type(buffName) ~= "string" then
        return 0
    end
    if not source or not source.buff then
        return 0
    end
    local lowerbuffName = string.lower(buffName)
    local buff = source.buff[lowerbuffName]
    if buff and buff.endTime and buff.endTime >= game.time then
        return buff.stacks or 0
    end
    return 0
end

-- 检查目标是否受到中毒效果
function buffManager.IsPoison(target)
    if not target or target.isDead then return false end
    -- 常见的中毒 Buff 判断
    local poisonBuffs = {
        "poisontrailtarget",
        "twitchdeadlyvenom",
        "cassiopeiawpoison",
        "cassiopeiaqdebuff",
        "toxicshotparticle",
        "bantamtraptarget"
    }
    for _, poisonBuff in ipairs(poisonBuffs) do
        if buffManager.HasBuff(target, poisonBuff) then
            return true
        end
    end
    -- 通过 Buff 类型检查是否为毒素（一般来说，毒素是类型 23）
    for _, buff in pairs(target.buff) do
        if buff and buff.type == 23 and buff.valid and buff.endTime > game.time then
            return true
        end
    end
    return false
end

return buffManager



local ove_0_10 = module.load("Kloader", "Lib/MyCommon")
local ove_0_11 = module.load("Kloader", "Lib/Version")
local ove_0_12 = module.internal("clipper").polygon
local ove_0_13 = {
	myPoli,
	State = false,
	lastChange = "",
	lastChangeCN = "",
	Title = "Krystra's Preminium AIO"
}

local function ove_0_14()
	-- print 5
	local slot_5_0 = 1
	local slot_5_1 = 60

	if game.time > slot_5_0 * slot_5_1 then
		return true
	end

	return false
end

local function ove_0_15(arg_6_0, arg_6_1)
	-- print 6
	local slot_6_0, slot_6_1 = graphics.text_area(arg_6_0, arg_6_1)

	return (math.floor((600 - slot_6_0) / 2))
end

local function ove_0_16(arg_7_0)
	-- print 7
	local slot_7_0, slot_7_1 = graphics.text_area(arg_7_0, 14)

	return (math.floor((300 - slot_7_0) / 2))
end

function ove_0_13.Get(arg_8_0, arg_8_1, arg_8_2)
	-- print 8
	if ove_0_13.lastChange ~= arg_8_0 then
		ove_0_13.lastChange = arg_8_0
	end

	if ove_0_13.lastChangeCN ~= arg_8_0 then
		ove_0_13.lastChangeCN = arg_8_1
	end

	if ove_0_13.State ~= arg_8_2 then
		ove_0_13.State = arg_8_2
	end
end

function ove_0_13.Do(arg_9_0, arg_9_1, arg_9_2)
	-- print 9
	ove_0_13.Get(arg_9_0, arg_9_1, arg_9_2)

	if ove_0_13.State ~= true then
		return
	end

	local slot_9_0 = graphics.width * 35 / 100
	local slot_9_1 = graphics.height * 25 / 100
	local slot_9_2 = graphics.world_to_screen(mousePos)

	ove_0_13.myPoli = ove_0_12(vec2(slot_9_0 + 250, slot_9_1 + 135), vec2(slot_9_0 + 350, slot_9_1 + 135), vec2(slot_9_0 + 350, slot_9_1 + 170), vec2(slot_9_0 + 250, slot_9_1 + 170))

	if hanbot.language == 1 then
		if ove_0_13.myPoli:Contains(game.cursorPos) == 1 then
			graphics.draw_text_2D("关闭", 16, slot_9_0 + 280, slot_9_1 + 153, graphics.argb(255, 255, 255, 255))
			graphics.draw_rectangle_2D(slot_9_0 + 250, slot_9_1 + 135, 100, 35, 3, graphics.argb(255, 255, 255, 255))
		else
			graphics.draw_text_2D("关闭", 16, slot_9_0 + 280, slot_9_1 + 153, graphics.argb(255, 255, 255, 255))
			graphics.draw_rectangle_2D(slot_9_0 + 250, slot_9_1 + 135, 100, 35, 3, graphics.argb(255, 0, 0, 0))
		end

		graphics.draw_rectangle_2D(slot_9_0, slot_9_1, 600, 184, 5, graphics.argb(255, 0, 0, 0))
		graphics.draw_line_2D(slot_9_0, slot_9_1 + 91, slot_9_0 + 600, slot_9_1 + 91, 182, graphics.argb(199, 0, 0, 0))
		graphics.draw_text_2D(ove_0_13.Title, 18, slot_9_0 + ove_0_15(ove_0_13.Title, 18), slot_9_1 + 20, graphics.argb(255, 255, 255, 255))
		graphics.draw_text_2D("提示", 16, slot_9_0 + ove_0_15("提示", 16), slot_9_1 + 55, graphics.argb(255, 255, 255, 255))
		graphics.draw_line_2D(slot_9_0, slot_9_1 + 40, slot_9_0 + 600, slot_9_1 + 40, 1, graphics.argb(199, 255, 255, 255))
		graphics.draw_line_2D(slot_9_0, slot_9_1 + 70, slot_9_0 + 600, slot_9_1 + 70, 1, graphics.argb(199, 255, 255, 255))
		graphics.draw_text_2D(arg_9_1, 14, slot_9_0 + ove_0_15(arg_9_1, 14), slot_9_1 + 85, graphics.argb(255, 255, 255, 255))
	elseif hanbot.language == 2 then
		if ove_0_13.myPoli:Contains(game.cursorPos) == 1 then
			graphics.draw_text_2D("Close", 16, slot_9_0 + 280, slot_9_1 + 153, graphics.argb(255, 255, 255, 255))
			graphics.draw_rectangle_2D(slot_9_0 + 250, slot_9_1 + 135, 100, 35, 3, graphics.argb(255, 255, 255, 255))
		else
			graphics.draw_text_2D("Close", 16, slot_9_0 + 280, slot_9_1 + 153, graphics.argb(255, 255, 255, 255))
			graphics.draw_rectangle_2D(slot_9_0 + 250, slot_9_1 + 135, 100, 35, 3, graphics.argb(255, 0, 0, 0))
		end

		graphics.draw_rectangle_2D(slot_9_0, slot_9_1, 600, 184, 5, graphics.argb(255, 0, 0, 0))
		graphics.draw_line_2D(slot_9_0, slot_9_1 + 91, slot_9_0 + 600, slot_9_1 + 91, 182, graphics.argb(199, 0, 0, 0))
		graphics.draw_text_2D(ove_0_13.Title, 18, slot_9_0 + ove_0_15(ove_0_13.Title, 18), slot_9_1 + 20, graphics.argb(255, 255, 255, 255))
		graphics.draw_text_2D("Message", 16, slot_9_0 + ove_0_15("Message", 16), slot_9_1 + 55, graphics.argb(255, 255, 255, 255))
		graphics.draw_line_2D(slot_9_0, slot_9_1 + 40, slot_9_0 + 600, slot_9_1 + 40, 1, graphics.argb(199, 255, 255, 255))
		graphics.draw_line_2D(slot_9_0, slot_9_1 + 70, slot_9_0 + 600, slot_9_1 + 70, 1, graphics.argb(199, 255, 255, 255))
		graphics.draw_text_2D(arg_9_0, 14, slot_9_0 + ove_0_15(arg_9_0, 14), slot_9_1 + 85, graphics.argb(255, 255, 255, 255))
	end
end

local function ove_0_17()
	-- print 10
	ove_0_13.Do(ove_0_13.lastChange, ove_0_13.lastChangeCN, ove_0_13.State)
end

local function ove_0_18(arg_11_0)
	-- print 11
	if arg_11_0 == 1 and ove_0_13.myPoli and ove_0_13.myPoli:Contains(game.cursorPos) == 1 then
		cb.remove(ove_0_17)
		cb.remove(ove_0_18)
	end
end

cb.add(cb.keydown, ove_0_18)
cb.add(cb.draw, ove_0_17)

return ove_0_13

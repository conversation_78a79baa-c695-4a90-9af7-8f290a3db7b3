
local ove_0_20 = module.internal("pred")
local ove_0_21 = module.internal("orb")
local ove_0_22 = math.abs
local ove_0_23 = math.max
local ove_0_24 = {
	LocalVersion = function()
		-- print 5
		return "3.25"
	end
}
local ove_0_25 = module.seek("evade")

function ove_0_24.EvadeVersion()
	-- print 6
	local slot_6_0 = 0

	if ove_0_25 and ove_0_25.menu and ove_0_25.menu.general then
		slot_6_0 = ove_0_25.menu.general.standard_danger_level and 2 or 1
	end

	return 2
end

function ove_0_24.Evade_Error(arg_7_0)
	-- print 7
	--console.set_color(13)
	--print(" ")
	--print(">--------------------------------------------------------------------------------<")
	--console.set_color(12)
	--print("                  - + - [ <PERSON><PERSON><PERSON> has failed to load! ] - + -")
	--console.set_color(13)
	--print("- + -                                                                        - + -")
	--console.set_color(14)
	--print("         This script requires [<PERSON>bot <PERSON>de] enabled to function properly!")
	--print("              If you don't want Hanbot <PERSON>de to evade spells, go to")
	--print("      Hanbot <PERSON>de menu => Keys => Disable <PERSON>sion, and add a toggle hotkey.")
	--print(" This way evade will be disabled and the script will function without any issues!")
	--console.set_color(13)
	--print(">--------------------------------------------------------------------------------<")
	--console.set_color(7)
	--chat.clear()
	--chat.add("\n[Healer AIO]", {
		--bold = false,
		--italic = false,
		--color = "#ffff00"
	--})
	--chat.add(" Hanbot Evade is disabled! Please turn it on!", {
		--color = "#FF0058"
	--})
	--chat.print()
	--chat.clear()
	--chat.add("For more information, check the console!", {
		--color = "#ffff00",
		--italic = true
	--})
	--chat.print()
	--chat.clear()
	--arg_7_0:header("evade_error_1", "Please enable Hanbot Evade!")
	--arg_7_0:header("evade_error_2", "For more information")
	--arg_7_0:header("evade_error_3", "check the console")
end

local function ove_0_26(arg_8_0)
	-- print 8
	local slot_8_0 = 5967627514378231
	local slot_8_1 = 18152

	return (arg_8_0:gsub("%x%x", function(arg_9_0)
		-- print 9
		local slot_9_0 = slot_8_0 % 274877906944
		local slot_9_1 = (slot_8_0 - slot_9_0) / 274877906944
		local slot_9_2 = slot_9_1 % 128

		arg_9_0 = tonumber(arg_9_0, 16)

		local slot_9_3 = (arg_9_0 + (slot_9_1 - slot_9_2) / 128) * (2 * slot_9_2 + 1) % 256

		slot_8_0 = slot_9_0 * slot_8_1 + slot_9_1 + arg_9_0 + slot_9_3

		return string.char(slot_9_3)
	end))
end

local function ove_0_27(arg_10_0)
	-- print 10
	local slot_10_0 = 3169407534477202
	local slot_10_1 = 18760

	return (arg_10_0:gsub("%x%x", function(arg_11_0)
		-- print 11
		local slot_11_0 = slot_10_0 % 274877906944
		local slot_11_1 = (slot_10_0 - slot_11_0) / 274877906944
		local slot_11_2 = slot_11_1 % 128

		arg_11_0 = tonumber(arg_11_0, 16)

		local slot_11_3 = (arg_11_0 + (slot_11_1 - slot_11_2) / 128) * (2 * slot_11_2 + 1) % 256

		slot_10_0 = slot_11_0 * slot_10_1 + slot_11_1 + arg_11_0 + slot_11_3

		return string.char(slot_11_3)
	end))
end

local function ove_0_28(arg_12_0)
	-- print 12
	local slot_12_0 = 9187430509178248
	local slot_12_1 = 18565

	return (arg_12_0:gsub("%x%x", function(arg_13_0)
		-- print 13
		local slot_13_0 = slot_12_0 % 274877906944
		local slot_13_1 = (slot_12_0 - slot_13_0) / 274877906944
		local slot_13_2 = slot_13_1 % 128

		arg_13_0 = tonumber(arg_13_0, 16)

		local slot_13_3 = (arg_13_0 + (slot_13_1 - slot_13_2) / 128) * (2 * slot_13_2 + 1) % 256

		slot_12_0 = slot_13_0 * slot_12_1 + slot_13_1 + arg_13_0 + slot_13_3

		return string.char(slot_13_3)
	end))
end

function ove_0_24.LSu7Cxw0()
	-- print 14
	return game.version:find(ove_0_26("25a591a2b1ebb6723d608eb3")) or game.version:find(ove_0_27("24d784b4e90ac166084df9a6")) or game.version:find(ove_0_28("e5d5e7d06695737038cc6c59"))
end

local ove_0_29 = 0

function ove_0_24.nXlYBD2k(arg_15_0)
	-- print 15
	ove_0_29 = ove_0_29 + 1

	if ove_0_29 > 1 then
		return
	end

	console.set_color(12)
	print("[BS AIO] has failed to load. Error " .. arg_15_0 .. ".")
	console.set_color(7)
	chat.clear()
	chat.add("[BS AIO] has failed to load. Error " .. arg_15_0 .. ".", {
		bold = false,
		italic = false,
		color = "#FF1818"
	})
	chat.print()
	chat.clear()
end



function ove_0_24.Lerp(arg_16_0, arg_16_1, arg_16_2)
	-- print 16
	return (1 - arg_16_2) * arg_16_0 + arg_16_1 * arg_16_2
end

function ove_0_24.InvLerp(arg_17_0, arg_17_1, arg_17_2)
	-- print 17
	return (arg_17_2 - arg_17_0) / (arg_17_1 - arg_17_0)
end

function ove_0_24.Remap(arg_18_0, arg_18_1, arg_18_2, arg_18_3, arg_18_4)
	-- print 18
	local slot_18_0 = ove_0_24.InvLerp(arg_18_0, arg_18_1, arg_18_4)

	return ove_0_24.Lerp(arg_18_2, arg_18_3, slot_18_0)
end

function ove_0_24.Round(arg_19_0, arg_19_1)
	-- print 19
	local slot_19_0 = 10^arg_19_1

	return math.floor(arg_19_0 * slot_19_0) / slot_19_0
end

function ove_0_24.Rounded(arg_20_0)
	-- print 20
	return math.floor(arg_20_0 + 0.5)
end

local ove_0_30 = false

function ove_0_24.Successfully_Loaded(arg_21_0, arg_21_1, arg_21_2, arg_21_3)
	-- print 21
	if ove_0_30 then
		return
	end

	ove_0_30 = true

	if arg_21_0 then
		--print(" ")
		--console.set_color(13)
		--print(">------------------------------------------------<")
		--console.set_color(10)
		--print("     - + - [ Healer AIO v" .. ove_0_24.LocalVersion() .. " loaded! ] - + -")
		--console.set_color(13)
		--print("- + -                                        - + -")
		--console.set_color(14)
		--print("       - + - Healer AIO Telegram link - + -")
		--print("   https://t.me/joinchat/H5JKEBb0-B78rlc7aVPl-Q")
		--console.set_color(13)
		--print(">------------------------------------------------<")
		--console.set_color(7)

		if not arg_21_1 and not arg_21_3 then
			chat.clear()
			chat.add("[BS AIO]", {
				bold = false,
				italic = false,
				color = "#ffff00"
			})
			chat.add(" [v" .. ove_0_24.LocalVersion() .. "] ", {
				color = "#a6ff4d"
			})
			chat.add((player and player.charName or "") .. " loaded!", {
				color = "#9EFFE1"
			})
			chat.print()
			chat.clear()
		else
			if arg_21_1 and not arg_21_3 then
				local slot_21_0 = arg_21_2 == 2 and "[Evade 2.0]" or "[Evade]"

				chat.clear()
				chat.add("[BS AIO]", {
					bold = false,
					italic = false,
					color = "#ffff00"
				})
				chat.add(" [v" .. ove_0_24.LocalVersion() .. "] ", {
					color = "#a6ff4d"
				})
				chat.add((player and player.charName or "") .. " loaded!", {
					color = "#9EFFE1"
				})
				chat.print()
				chat.clear()

				if ove_0_24.EvadeVersion() == 0 or arg_21_2 == 2 and ove_0_24.EvadeVersion() ~= 2 then
					chat.clear()
					chat.add("[BS AIO]", {
						bold = false,
						italic = false,
						color = "#ffff00"
					})
					chat.add(" Please enable " .. slot_21_0 .. " to unlock all features!", {
						color = "#9EFFE1"
					})
					chat.print()
					chat.clear()
				end
			end

			if not arg_21_1 and arg_21_3 then
				chat.clear()
				chat.add("[BS AIO]", {
					bold = false,
					italic = false,
					color = "#ffff00"
				})
				chat.add(" [v" .. ove_0_24.LocalVersion() .. "] ", {
					color = "#a6ff4d"
				})
				chat.add("[TEST VERSION] " .. (player and player.charName or "") .. " loaded!", {
					color = "#9EFFE1"
				})
				chat.print()
				chat.clear()
			end

			if arg_21_1 and arg_21_3 then
				local slot_21_1 = arg_21_2 == 2 and "[Evade 2.0]" or "[Evade]"

				chat.clear()
				chat.add("[BS AIO]", {
					bold = false,
					italic = false,
					color = "#ffff00"
				})
				chat.add(" [v" .. ove_0_24.LocalVersion() .. "] ", {
					color = "#a6ff4d"
				})
				chat.add("[TEST VERSION] " .. (player and player.charName or "") .. " loaded!", {
					color = "#9EFFE1"
				})
				chat.print()
				chat.clear()

				if ove_0_24.EvadeVersion() == 0 or arg_21_2 == 2 and ove_0_24.EvadeVersion() ~= 2 then
					chat.clear()
					chat.add("[BS AIO]", {
						bold = false,
						italic = false,
						color = "#ffff00"
					})
					chat.add(" Please enable " .. slot_21_1 .. " to unlock all features!", {
						color = "#9EFFE1"
					})
					chat.print()
					chat.clear()
				end
			end
		end
	else
		return
	end

	local slot_21_2 = 0
	local slot_21_3 = ove_0_24.LocalVersion()
	local slot_21_4 = os.clock() + 4

	local function slot_21_5()
		-- print 22
		local slot_22_0 = os.clock() - slot_21_4
		local slot_22_1 = 255

		if slot_22_0 > 0 then
			slot_22_1 = slot_22_1 - slot_22_0 / 0.1 * 50
		end

		if slot_22_1 <= 0 then
			return 0
		end

		return slot_22_1
	end

	local function slot_21_6()
		-- print 23
		local slot_23_0 = os.clock() - slot_21_4
		local slot_23_1 = 150

		if slot_23_0 > 0 then
			slot_23_1 = slot_23_1 - slot_23_0 / 0.1 * 50
		end

		if slot_23_1 <= 0 then
			return 0
		end

		return slot_23_1
	end

	local slot_21_7 = graphics.dxline(5)
	local slot_21_8 = graphics.dxline(55)

	slot_21_7:SetAntialias(false)
	slot_21_8:SetAntialias(false)

	local function slot_21_9()
		-- print 24
		if slot_21_4 < os.clock() - 1.5 then
			cb.remove(slot_21_9)
		end

		--local slot_24_0 = "Healer AIO load success!"
		--local slot_24_1 = tostring("Version " .. slot_21_3)
		--local slot_24_2, slot_24_3 = graphics.text_area(slot_24_0, 18)
		--local slot_24_4, slot_24_5 = graphics.text_area(slot_24_1, 18)
		--local slot_24_6 = slot_24_2 * 0.5
		--local slot_24_7 = slot_24_4 * 0.5
		--local slot_24_8 = graphics.width * 0.5
		--local slot_24_9 = slot_21_5()

		--graphics.draw_text_2D(slot_24_0, 18, slot_24_8 - slot_24_6, 40 + slot_21_2, graphics.argb(slot_24_9, 128, 255, 0))
		--graphics.draw_text_2D(slot_24_1, 18, slot_24_8 - slot_24_7, 60 + slot_21_2, graphics.argb(slot_24_9, 128, 255, 0))
		--graphics.draw_line_2D(-175 + slot_24_8, 20 + slot_21_2, 175 + slot_24_8, 20 + slot_21_2, 5, graphics.argb(slot_24_9, 128, 255, 0))
		--graphics.draw_line_2D(-175 + slot_24_8, 80 + slot_21_2, 175 + slot_24_8, 80 + slot_21_2, 5, graphics.argb(slot_24_9, 128, 255, 0))
		--graphics.draw_line_2D(-173 + slot_24_8, 23 + slot_21_2, -173 + slot_24_8, 78 + slot_21_2, 5, graphics.argb(slot_24_9, 128, 255, 0))
		--graphics.draw_line_2D(172 + slot_24_8, 23 + slot_21_2, 172 + slot_24_8, 78 + slot_21_2, 5, graphics.argb(slot_24_9, 128, 255, 0))
		--graphics.draw_line_2D(-170 + slot_24_8, 50 + slot_21_2, 170 + slot_24_8, 50 + slot_21_2, 55, graphics.argb(slot_21_6(), 0, 0, 0))
	end

	cb.add(cb.draw, slot_21_9)
end

local ove_0_31 = 0
local ove_0_32 = 0

function ove_0_24.MenuHeaderWizard(arg_25_0)
	-- print 25
	if not menu:isopen() then
		return
	end

	if ove_0_31 < game.time then
		if ove_0_32 == 0 then
			--arg_25_0.headree213:set("visible", false)
			--arg_25_0.headree:set("visible", true)

			--ove_0_32 = 1
			--ove_0_31 = game.time + 0.5

			return
		end

		if ove_0_32 == 1 then
			--arg_25_0.headree:set("visible", false)
			--arg_25_0.headree1:set("visible", true)

			--ove_0_32 = 2
			--ove_0_31 = game.time + 0.5

			return
		end

		if ove_0_32 == 2 then
			--arg_25_0.headree:set("visible", false)
			--arg_25_0.headree1:set("visible", false)
			--arg_25_0.headree21:set("visible", true)

			--ove_0_32 = 3
			--ove_0_31 = game.time + 0.5

			return
		end

		if ove_0_32 == 3 then
			--arg_25_0.headree:set("visible", false)
			--arg_25_0.headree1:set("visible", false)
			--arg_25_0.headree21:set("visible", false)
			--arg_25_0.headree213:set("visible", true)

			--ove_0_32 = 0
			--ove_0_31 = game.time + 0.5

			return
		end
	end
end

local ove_0_33 = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/"

function ove_0_24.vVPHlwd2(arg_26_0)
	-- print 26
	arg_26_0 = string.gsub(arg_26_0, "[^" .. ove_0_33 .. "=]", "")

	return (arg_26_0:gsub(".", function(arg_27_0)
		-- print 27
		if arg_27_0 == "=" then
			return ""
		end

		local slot_27_0 = ""
		local slot_27_1 = ove_0_33:find(arg_27_0) - 1

		for iter_27_0 = 6, 1, -1 do
			slot_27_0 = slot_27_0 .. (slot_27_1 % 2^iter_27_0 - slot_27_1 % 2^(iter_27_0 - 1) > 0 and "1" or "0")
		end

		return slot_27_0
	end):gsub("%d%d%d?%d?%d?%d?%d?%d?", function(arg_28_0)
		-- print 28
		if #arg_28_0 ~= 8 then
			return ""
		end

		local slot_28_0 = 0

		for iter_28_0 = 1, 8 do
			slot_28_0 = slot_28_0 + (arg_28_0:sub(iter_28_0, iter_28_0) == "1" and 2^(8 - iter_28_0) or 0)
		end

		return string.char(slot_28_0)
	end))
end

function ove_0_24.LN8opruR(arg_29_0)
	-- print 29
	return (arg_29_0:gsub(".", function(arg_30_0)
		-- print 30
		local slot_30_0 = ""
		local slot_30_1 = arg_30_0:byte()

		for iter_30_0 = 8, 1, -1 do
			slot_30_0 = slot_30_0 .. (slot_30_1 % 2^iter_30_0 - slot_30_1 % 2^(iter_30_0 - 1) > 0 and "1" or "0")
		end

		return slot_30_0
	end) .. "0000"):gsub("%d%d%d?%d?%d?%d?", function(arg_31_0)
		-- print 31
		if #arg_31_0 < 6 then
			return ""
		end

		local slot_31_0 = 0

		for iter_31_0 = 1, 6 do
			slot_31_0 = slot_31_0 + (arg_31_0:sub(iter_31_0, iter_31_0) == "1" and 2^(6 - iter_31_0) or 0)
		end

		return ove_0_33:sub(slot_31_0 + 1, slot_31_0 + 1)
	end) .. ({
		"",
		"==",
		"="
	})[#arg_29_0 % 3 + 1]
end

function ove_0_24.GetBonusAD(arg_32_0)
	-- print 32
	local slot_32_0 = arg_32_0 or player

	return (slot_32_0.baseAttackDamage + slot_32_0.flatPhysicalDamageMod) * slot_32_0.percentPhysicalDamageMod - slot_32_0.baseAttackDamage
end

local ove_0_34 = module.load("Brian", "Databases/Mobs")
local ove_0_35 = module.load("Brian", "Databases/Buffs")
local ove_0_36 = module.load("Brian", "Databases/Items")
local ove_0_37 = module.load("Brian", "Databases/SpellSlots")
local ove_0_38 = {}
local ove_0_39 = {}
local ove_0_40 = 0

for iter_0_0 = 0, objManager.enemies_n - 1 do
	local ove_0_41 = objManager.enemies[iter_0_0]

	ove_0_38[ove_0_41.ptr] = ove_0_41.charName
	ove_0_39[ove_0_41.charName] = true
	ove_0_39[ove_0_41.charName .. "_ptr"] = ove_0_41.ptr
	ove_0_40 = ove_0_40 + 1
end

local ove_0_42 = {}
local ove_0_43 = {}
local ove_0_44 = {}

for iter_0_1 = 0, objManager.allies_n - 1 do
	local ove_0_45 = objManager.allies[iter_0_1]

	ove_0_42[ove_0_45.ptr] = ove_0_45.charName
	ove_0_43[ove_0_45.charName] = true
	ove_0_43[ove_0_45.charName .. ove_0_45.ptr] = true
	ove_0_44[ove_0_45.ptr] = true
	ove_0_40 = ove_0_40 + 1
end

function ove_0_24.DMG_RED_MasterYi()
	-- print 33
	return {
		0.6,
		0.625,
		0.65,
		0.675,
		0.7
	}
end

function ove_0_24.DMG_RED_Gragas(arg_34_0)
	-- print 34
	local slot_34_0 = 4
	local slot_34_1 = math.floor(arg_34_0)
	local slot_34_2 = math.floor(slot_34_1 / 100) / 100 * slot_34_0

	return {
		0.1 + slot_34_2,
		0.12 + slot_34_2,
		0.14 + slot_34_2,
		0.16 + slot_34_2,
		0.18 + slot_34_2
	}
end

function ove_0_24.DMG_Shield_Annie(arg_35_0)
	-- print 35
	local slot_35_0 = arg_35_0 * 0.4

	return {
		60 + slot_35_0,
		100 + slot_35_0,
		140 + slot_35_0,
		180 + slot_35_0,
		220 + slot_35_0
	}
end

function ove_0_24.DMG_RED_Warwick()
	-- print 36
	return {
		0.35,
		0.4,
		0.45,
		0.5,
		0.55
	}
end

function ove_0_24.DMG_RED_Galio_Magic(arg_37_0, arg_37_1)
	-- print 37
	local slot_37_0 = 5
	local slot_37_1 = math.floor(arg_37_0)
	local slot_37_2 = math.floor(slot_37_1 / 100) / 100 * slot_37_0
	local slot_37_3 = 8
	local slot_37_4 = math.floor(arg_37_1)
	local slot_37_5 = slot_37_2 + math.floor(slot_37_4 / 100) / 100 * slot_37_3

	return {
		0.2 + slot_37_5,
		0.25 + slot_37_5,
		0.3 + slot_37_5,
		0.35 + slot_37_5,
		0.4 + slot_37_5
	}
end

function ove_0_24.DMG_RED_Galio_Physic(arg_38_0, arg_38_1)
	-- print 38
	local slot_38_0 = 2.5
	local slot_38_1 = math.floor(arg_38_0)
	local slot_38_2 = math.floor(slot_38_1 / 100) / 100 * slot_38_0
	local slot_38_3 = 4
	local slot_38_4 = math.floor(arg_38_1)
	local slot_38_5 = slot_38_2 + math.floor(slot_38_4 / 100) / 100 * slot_38_3

	return {
		0.1 + slot_38_5,
		0.125 + slot_38_5,
		0.15 + slot_38_5,
		0.175 + slot_38_5,
		0.2 + slot_38_5
	}
end

function ove_0_24.DMG_RED_Braum()
	-- print 39
	return {
		0.35,
		0.4,
		0.45,
		0.5,
		0.55
	}
end

function ove_0_24.DMG_RED_Annie()
	-- print 40
	return {
		0,
		0,
		0,
		0,
		0
	}
end

function ove_0_24.DMG_RED_Alistar()
	-- print 41
	return {
		0.55,
		0.65,
		0.75
	}
end

function ove_0_24.DMG_RED_Garen_W()
	-- print 42
	return {
		65,
		85,
		105,
		125,
		145
	}
end

function ove_0_24.DMG_RED_Soraka_R()
	-- print 43
	return {
		150,
		250,
		350
	}
end

function ove_0_24.DMG_RED_YasuoShield()
	-- print 44
	return {
		100,
		105,
		110,
		115,
		120,
		130,
		140,
		150,
		160,
		170,
		180,
		200,
		220,
		250,
		290,
		350,
		410,
		475
	}
end

function ove_0_24.DMG_RED_PhantomHP()
	-- print 45
	return {
		0,
		0,
		0,
		0,
		0,
		0,
		0,
		0,
		0,
		0,
		0,
		0,
		0,
		0,
		0,
		0,
		0,
		0
	}
end

function ove_0_24.DMG_RED_ImmortalShieldbowHP(arg_46_0)
	-- print 46
	return ({
		250,
		250,
		250,
		250,
		250,
		250,
		250,
		250,
		250,
		292.22,
		334.44,
		376.67,
		418.89,
		461.11,
		503.33,
		545.56,
		587.78,
		630
	})[arg_46_0]
end

function ove_0_24.DMG_RED_Baron()
	-- print 47
	return {
		0.49,
		0.48,
		0.47,
		0.46,
		0.45,
		0.44,
		0.43,
		0.42,
		0.41,
		0.4,
		0.39,
		0.38,
		0.37,
		0.36,
		0.35,
		0.34,
		0.33,
		0.32,
		0.31
	}
end

function ove_0_24.DMG_RED_ElderDragonBurn()
	-- print 48
	return {
		75,
		75,
		90,
		90,
		105,
		105,
		120,
		120,
		135,
		135,
		150,
		150,
		165,
		165,
		180,
		180,
		195,
		195,
		210,
		210,
		225
	}
end

function ove_0_24.DMG_RED_Hexdrinker(arg_49_0)
	-- print 49
	return 100 + 10 * arg_49_0
end

function ove_0_24.DMG_RED_MawOfMalmortius(arg_50_0)
	-- print 50
	return arg_50_0.combatType == 1 and 200 + ove_0_23(0, ove_0_24.GetBonusAD(arg_50_0) * 2.25) or 200 + ove_0_23(0, ove_0_24.GetBonusAD(arg_50_0) * 1.6875)
end

function ove_0_24.DMG_RED_BonePlating(arg_51_0)
	-- print 51
	return 28.235 + 1.765 * arg_51_0
end

function ove_0_24.DMG_RED_NullOrb(arg_52_0)
	-- print 52
	return 35.294 + 4.706 * arg_52_0
end

function ove_0_24.DMG_RED_HarvestDmg(arg_53_0)
	-- print 53
	return 17.647 + 2.353 * arg_53_0
end

function ove_0_24.DMG_RED_HealSumm(arg_54_0)
	-- print 54
	return 66 + 14 * arg_54_0
end

function ove_0_24.DMG_RED_BarrierSumm(arg_55_0)
	-- print 55
	return 87 + 18 * arg_55_0
end

local ove_0_46 = {
	0.49,
	0.48,
	0.47,
	0.46,
	0.45,
	0.44,
	0.43,
	0.42,
	0.41,
	0.4,
	0.39,
	0.38,
	0.37,
	0.36,
	0.35,
	0.34,
	0.33,
	0.32,
	0.31
}

function ove_0_24.BaronReductionMod(arg_56_0)
	-- print 56
	local slot_56_0 = 1

	if arg_56_0.buff[ove_0_35.BaronBuff] then
		if math.floor(game.time / 60) <= 20 then
			if arg_56_0.charName:find(ove_0_34.RangedMinion.charName) then
				slot_56_0 = 0.5
			end

			if arg_56_0.charName:find(ove_0_34.MeleeMinion.charName) then
				slot_56_0 = 0.5
			end
		end

		if math.floor(game.time / 60) >= 21 and not (math.floor(game.time / 60) >= 40) then
			if arg_56_0.charName:find(ove_0_34.RangedMinion.charName) then
				slot_56_0 = ove_0_46[math.floor(game.time / 60) - 20]
			end

			if arg_56_0.charName:find(ove_0_34.MeleeMinion.charName) then
				slot_56_0 = ove_0_46[math.floor(game.time / 60) - 20]
			end
		end

		if math.floor(game.time / 60) >= 40 then
			if arg_56_0.charName:find(ove_0_34.RangedMinion.charName) then
				slot_56_0 = 0.3
			end

			if arg_56_0.charName:find(ove_0_34.MeleeMinion.charName) then
				slot_56_0 = 0.3
			end
		end
	end

	return slot_56_0
end

local ove_0_47 = {
	75,
	75,
	90,
	90,
	105,
	105,
	120,
	120,
	135,
	135,
	150,
	150,
	165,
	165,
	180,
	180,
	195,
	195,
	210,
	210,
	225
}

function ove_0_24.ElderDragonBurnDamage(arg_57_0, arg_57_1)
	-- print 57
	local slot_57_0 = 0
	local slot_57_1 = player.buff[ove_0_35.ElderDragonBuff]

	if slot_57_1 and arg_57_1 < slot_57_1.endTime - game.time then
		local slot_57_2 = math.floor(game.time / 60)
		local slot_57_3 = arg_57_0.healthRegenRate * (2.25 + arg_57_1)

		if slot_57_2 < 25 then
			slot_57_0 = math.max(0, ove_0_47[1] - slot_57_3)
		elseif slot_57_2 >= 25 and slot_57_2 <= 45 then
			slot_57_0 = math.max(0, ove_0_47[slot_57_2 - 24] - slot_57_3)
		else
			slot_57_0 = math.max(0, ove_0_47[21] - slot_57_3)
		end
	end

	return slot_57_0
end

function ove_0_24.GlowSticker_lol()
	-- print 58
	return 5689941236399
end

function ove_0_24.IsOnScreen(arg_59_0)
	-- print 59
	local slot_59_0 = graphics.world_to_screen(vec3(arg_59_0.x, arg_59_0.y, arg_59_0.z))

	return slot_59_0.x > 0 and slot_59_0.x <= graphics.width and slot_59_0.y > 0 and slot_59_0.y <= graphics.height
end

function ove_0_24.SpellBladeType(arg_60_0, arg_60_1, arg_60_2, arg_60_3, arg_60_4)
	-- print 60
	if arg_60_4 then
		return 5
	end

	if arg_60_3 then
		return 4
	end

	if arg_60_2 then
		return 3
	end

	if arg_60_1 then
		return 2
	end

	if arg_60_0 then
		return 1
	end

	return 0
end

function ove_0_24.IsFacingPlayerPath(arg_61_0, arg_61_1)
	-- print 61
	if not arg_61_0.path.isActive then
		return false
	end

	local slot_61_0 = arg_61_0.path.point2D[arg_61_0.path.count] - arg_61_0.pos2D
	local slot_61_1 = player.pos2D - arg_61_0.pos2D
	local slot_61_2 = slot_61_0:norm()
	local slot_61_3 = slot_61_1:norm()

	if arg_61_1 < slot_61_2:dot(slot_61_3) then
		return true
	end

	return false
end

function ove_0_24.IsFacingPlayerDir(arg_62_0, arg_62_1)
	-- print 62
	local slot_62_0 = arg_62_0.pos2D + arg_62_0.direction2D - arg_62_0.pos2D
	local slot_62_1 = player.pos2D - arg_62_0.pos2D
	local slot_62_2 = slot_62_0:norm()
	local slot_62_3 = slot_62_1:norm()

	if arg_62_1 < slot_62_2:dot(slot_62_3) then
		return true
	end

	return false
end

function ove_0_24.IsFacingPlayerSeg(arg_63_0, arg_63_1, arg_63_2)
	-- print 63
	if not arg_63_0.path.isActive then
		return false
	end

	local slot_63_0 = arg_63_1.endPos - arg_63_0.pos2D
	local slot_63_1 = player.pos2D - arg_63_0.pos2D
	local slot_63_2 = slot_63_0:norm()
	local slot_63_3 = slot_63_1:norm()

	if arg_63_2 < slot_63_2:dot(slot_63_3) then
		return true
	end

	return false
end

function ove_0_24.IsRunningFromPlayerSeg(arg_64_0, arg_64_1, arg_64_2)
	-- print 64
	if not arg_64_0.path.isActive then
		return false
	end

	local slot_64_0 = arg_64_1.endPos - arg_64_0.pos2D
	local slot_64_1 = player.pos2D - arg_64_0.pos2D
	local slot_64_2 = slot_64_0:norm()
	local slot_64_3 = slot_64_1:norm()

	if arg_64_2 > slot_64_2:dot(slot_64_3) then
		return true
	end

	return false
end

function ove_0_24.IsRunningFromPlayerPath(arg_65_0, arg_65_1)
	-- print 65
	if not arg_65_0.path.isActive then
		return false
	end

	local slot_65_0 = arg_65_0.path.point2D[arg_65_0.path.count] - arg_65_0.pos2D
	local slot_65_1 = player.pos2D - arg_65_0.pos2D
	local slot_65_2 = slot_65_0:norm()
	local slot_65_3 = slot_65_1:norm()

	if arg_65_1 > slot_65_2:dot(slot_65_3) then
		return true
	end

	return false
end

function ove_0_24.CooldownReduction(arg_66_0)
	-- print 66
	return 1 - 1 / (1 + arg_66_0.abilityHasteMod / 100)
end

function ove_0_24.linear_showcase(arg_67_0, arg_67_1, arg_67_2)
	-- print 67
	local slot_67_0 = arg_67_2 or graphics.argb(255, 100, 180, 255)
	local slot_67_1 = arg_67_1
	local slot_67_2 = (player.pos - slot_67_1):norm()
	local slot_67_3 = slot_67_2:perp2()
	local slot_67_4 = slot_67_2:perp1()
	local slot_67_5 = player.pos + slot_67_4 * arg_67_0.width
	local slot_67_6 = player.pos + slot_67_3 * arg_67_0.width
	local slot_67_7 = ove_0_24.VectorExtend(player.pos, slot_67_1, arg_67_0.range)

	graphics.draw_line(slot_67_5, slot_67_6, 3, slot_67_0)

	local slot_67_8 = (player.pos - slot_67_1):norm()
	local slot_67_9 = slot_67_8:perp2()
	local slot_67_10 = slot_67_7 + slot_67_8:perp1() * arg_67_0.width
	local slot_67_11 = slot_67_7 + slot_67_9 * arg_67_0.width

	graphics.draw_line(slot_67_10, slot_67_11, 3, slot_67_0)
	graphics.draw_line(slot_67_5, slot_67_10, 3, slot_67_0)
	graphics.draw_line(slot_67_6, slot_67_11, 3, slot_67_0)
end

function ove_0_24.CustomMEC(arg_68_0, arg_68_1)
	-- print 68
	local slot_68_0 = 1 + math.ceil(arg_68_1 / 5)
	local slot_68_1 = arg_68_0[0]
	local slot_68_2 = 1e-05

	for iter_68_0 = 1, slot_68_0 do
		for iter_68_1 = 0, arg_68_1 - 1 do
			local slot_68_3 = slot_68_1:distSqr(arg_68_0[iter_68_1])

			if slot_68_3 > slot_68_2 * slot_68_2 then
				local slot_68_4 = math.sqrt(slot_68_3) / slot_68_2

				slot_68_2 = (slot_68_4 + 1 / slot_68_4) * slot_68_2 * 0.5

				local slot_68_5 = 1 / (slot_68_4 * slot_68_4)

				slot_68_1 = (slot_68_1:ext(slot_68_5) + arg_68_0[iter_68_1]:ext(-slot_68_5)) * 0.5
			end
		end
	end

	for iter_68_2 = 0, arg_68_1 - 1 do
		local slot_68_6 = slot_68_1:distSqr(arg_68_0[iter_68_2])

		if slot_68_6 > slot_68_2 * slot_68_2 then
			local slot_68_7 = math.sqrt(slot_68_6)

			slot_68_2 = (slot_68_2 + slot_68_7) * 0.5
			slot_68_1 = slot_68_1:lerp(arg_68_0[iter_68_2], 1 - slot_68_2 / slot_68_7)
		end
	end

	return slot_68_1, slot_68_2
end

function ove_0_24.VectorPointProjectionOnLineSegment(arg_69_0, arg_69_1, arg_69_2)
	-- print 69
	local slot_69_0 = arg_69_2.x
	local slot_69_1 = arg_69_2.z or arg_69_2.y
	local slot_69_2 = arg_69_0.x
	local slot_69_3 = arg_69_0.z or arg_69_0.y
	local slot_69_4 = arg_69_1.x
	local slot_69_5 = arg_69_1.z or arg_69_1.y
	local slot_69_6 = ((slot_69_0 - slot_69_2) * (slot_69_4 - slot_69_2) + (slot_69_1 - slot_69_3) * (slot_69_5 - slot_69_3)) / ((slot_69_4 - slot_69_2)^2 + (slot_69_5 - slot_69_3)^2)
	local slot_69_7 = vec2(slot_69_2 + slot_69_6 * (slot_69_4 - slot_69_2), slot_69_3 + slot_69_6 * (slot_69_5 - slot_69_3))
	local slot_69_8 = slot_69_6 < 0 and 0 or slot_69_6 > 1 and 1 or slot_69_6
	local slot_69_9 = slot_69_8 == slot_69_6

	return slot_69_9 and slot_69_7 or vec2(slot_69_2 + slot_69_8 * (slot_69_4 - slot_69_2), slot_69_3 + slot_69_8 * (slot_69_5 - slot_69_3)), slot_69_7, slot_69_9
end

function ove_0_24.IsOnLineSegment(arg_70_0, arg_70_1, arg_70_2, arg_70_3, arg_70_4)
	-- print 70
	local slot_70_0 = ove_0_24.VectorExtend(arg_70_1, arg_70_2, arg_70_4)
	local slot_70_1 = arg_70_3 * arg_70_3
	local slot_70_2, slot_70_3, slot_70_4 = ove_0_24.VectorPointProjectionOnLineSegment(arg_70_1, slot_70_0, arg_70_0)

	if slot_70_4 and slot_70_1 >= arg_70_0:distSqr(slot_70_2) and arg_70_1:distSqr(slot_70_0) >= arg_70_1:distSqr(arg_70_0) then
		return true
	end

	return false
end

function ove_0_24.LineCircleCast2D(arg_71_0, arg_71_1, arg_71_2, arg_71_3)
	-- print 71
	local slot_71_0 = arg_71_0.x - arg_71_2.x
	local slot_71_1 = arg_71_0.y - arg_71_2.y
	local slot_71_2 = arg_71_1.x
	local slot_71_3 = arg_71_1.y
	local slot_71_4 = slot_71_0 * slot_71_2 + slot_71_1 * slot_71_3
	local slot_71_5 = 2 * slot_71_0 * slot_71_1 * slot_71_2 * slot_71_3 + arg_71_3^2 * slot_71_2^2 + arg_71_3^2 * slot_71_3^2 - slot_71_0^2 * slot_71_3^2 - slot_71_1^2 * slot_71_2^2
	local slot_71_6 = slot_71_2^2 + slot_71_3^2
	local slot_71_7 = -(slot_71_4 + math.sqrt(slot_71_5)) / slot_71_6
	local slot_71_8 = -(slot_71_4 - math.sqrt(slot_71_5)) / slot_71_6

	return arg_71_0 + math.max(slot_71_7, slot_71_8) * arg_71_1
end

function ove_0_24.Vec2Zero()
	-- print 72
	return vec2(0, 0)
end

function ove_0_24.Vec3Zero()
	-- print 73
	return vec3(0, 0, 0)
end

function ove_0_24.sect_seg_seg(arg_74_0, arg_74_1, arg_74_2, arg_74_3)
	-- print 74
	local slot_74_0 = {
		X1 = arg_74_0.x,
		Y1 = arg_74_0.y,
		X2 = arg_74_1.x,
		Y2 = arg_74_1.y
	}
	local slot_74_1 = {
		X1 = arg_74_2.x,
		Y1 = arg_74_2.y,
		X2 = arg_74_3.x,
		Y2 = arg_74_3.y
	}
	local slot_74_2 = (slot_74_1.Y2 - slot_74_1.Y1) * (slot_74_0.X2 - slot_74_0.X1) - (slot_74_1.X2 - slot_74_1.X1) * (slot_74_0.Y2 - slot_74_0.Y1)

	if slot_74_2 == 0 then
		return false
	end

	local slot_74_3 = (slot_74_1.X2 - slot_74_1.X1) * (slot_74_0.Y1 - slot_74_1.Y1) - (slot_74_1.Y2 - slot_74_1.Y1) * (slot_74_0.X1 - slot_74_1.X1)
	local slot_74_4 = (slot_74_0.X2 - slot_74_0.X1) * (slot_74_0.Y1 - slot_74_1.Y1) - (slot_74_0.Y2 - slot_74_0.Y1) * (slot_74_0.X1 - slot_74_1.X1)
	local slot_74_5 = slot_74_3 / slot_74_2
	local slot_74_6 = slot_74_4 / slot_74_2

	if slot_74_5 >= 0 and slot_74_5 <= 1 and slot_74_6 >= 0 and slot_74_6 <= 1 then
		local slot_74_7 = slot_74_0.X1 + slot_74_5 * (slot_74_0.X2 - slot_74_0.X1)
		local slot_74_8 = slot_74_0.Y1 + slot_74_5 * (slot_74_0.Y2 - slot_74_0.Y1)

		return {
			x = slot_74_7,
			y = slot_74_8
		}
	end

	return false
end

local ove_0_48 = false

if ove_0_39.Yasuo then
	ove_0_48 = true
end

if ove_0_39.Viego and ove_0_43.Yasuo then
	ove_0_48 = true
end

local ove_0_49
local ove_0_50
local ove_0_51 = {
	215,
	250,
	275,
	295,
	350
}
local ove_0_52

local function ove_0_53(arg_75_0)
	-- print 75
	local slot_75_0 = arg_75_0.owner

	if slot_75_0 and slot_75_0.type and slot_75_0.type == TYPE_HERO and slot_75_0.team == TEAM_ENEMY and arg_75_0.name == "YasuoW" and slot_75_0.pos then
		ove_0_50 = slot_75_0.pos + vec3(0, 0, 0)
		ove_0_52 = ove_0_51[slot_75_0:spellSlot(1).level]
	end
end

local function ove_0_54(arg_76_0)
	-- print 76
	local slot_76_0 = arg_76_0.spell.owner

	if slot_76_0 and slot_76_0.type and slot_76_0.type == TYPE_HERO and slot_76_0.team == TEAM_ENEMY and arg_76_0.name == "YasuoW_VisualMis" then
		ove_0_49 = arg_76_0
	end
end

local function ove_0_55(arg_77_0)
	-- print 77
	if ove_0_49 and ove_0_49.ptr == arg_77_0.ptr then
		if ove_0_50 then
			ove_0_50 = nil
		end

		ove_0_49 = nil
	end
end

if ove_0_48 then
	cb.add(cb.spell, ove_0_53)
	cb.add(cb.create_missile, ove_0_54)
	cb.add(cb.delete_missile, ove_0_55)
end

function ove_0_24.IsBehindWall(arg_78_0, arg_78_1, arg_78_2)
	-- print 78
	if ove_0_49 and ove_0_50 then
		local slot_78_0 = ove_0_50:to2D()
		local slot_78_1 = ove_0_24.VectorExtend(ove_0_49.pos2D, slot_78_0, -35)
		local slot_78_2 = (slot_78_0 - slot_78_1):norm()
		local slot_78_3 = slot_78_2:perp2()
		local slot_78_4 = slot_78_2:perp1()
		local slot_78_5 = slot_78_1 + slot_78_3 * ove_0_52
		local slot_78_6 = slot_78_1 + slot_78_4 * ove_0_52
		local slot_78_7 = (slot_78_6 - slot_78_5):norm()
		local slot_78_8 = slot_78_7:perp2()
		local slot_78_9 = slot_78_7:perp1()
		local slot_78_10 = slot_78_5 + slot_78_8 * 100
		local slot_78_11 = slot_78_5 + slot_78_9 * 10
		local slot_78_12 = (slot_78_6 - slot_78_5):norm()
		local slot_78_13 = slot_78_12:perp2()
		local slot_78_14 = slot_78_12:perp1()
		local slot_78_15 = slot_78_6 + slot_78_13 * 100
		local slot_78_16 = slot_78_6 + slot_78_14 * 10
		local slot_78_17 = ove_0_24.sect_seg_seg(slot_78_6, slot_78_5, arg_78_0, arg_78_1)
		local slot_78_18 = ove_0_24.sect_seg_seg(slot_78_11, slot_78_10, arg_78_0, arg_78_1)
		local slot_78_19 = ove_0_24.sect_seg_seg(slot_78_16, slot_78_15, arg_78_0, arg_78_1)
		local slot_78_20 = ove_0_24.sect_seg_seg(slot_78_10, slot_78_15, arg_78_0, arg_78_1)
		local slot_78_21 = ove_0_24.sect_seg_seg(slot_78_11, slot_78_15, arg_78_0, arg_78_1)
		local slot_78_22 = mathf.col_vec_rect(arg_78_0, slot_78_6, slot_78_5, arg_78_2, 230)
		local slot_78_23 = mathf.col_vec_rect(arg_78_1, slot_78_6, slot_78_5, arg_78_2, 230)

		if slot_78_17 or slot_78_18 or slot_78_19 or slot_78_20 or slot_78_21 or slot_78_22 or slot_78_23 then
			return true
		end
	end

	return false
end

local ove_0_56 = {}
local ove_0_57

function ove_0_24.DelayAction(arg_79_0, arg_79_1, arg_79_2)
	-- print 79
	if not ove_0_57 then
		function ove_0_57()
			-- print 80
			for iter_80_0, iter_80_1 in pairs(ove_0_56) do
				if iter_80_0 <= os.clock() then
					for iter_80_2 = 1, #iter_80_1 do
						local slot_80_0 = iter_80_1[iter_80_2]

						if slot_80_0 and slot_80_0.func then
							slot_80_0.func(unpack(slot_80_0.args or {}))
						end
					end

					ove_0_56[iter_80_0] = nil
				end
			end
		end

		cb.add(cb.tick, ove_0_57)
	end

	local slot_79_0 = os.clock() + (arg_79_1 or 0)

	if ove_0_56[slot_79_0] then
		ove_0_56[slot_79_0][#ove_0_56[slot_79_0] + 1] = {
			func = arg_79_0,
			args = arg_79_2
		}
	else
		ove_0_56[slot_79_0] = {
			{
				func = arg_79_0,
				args = arg_79_2
			}
		}
	end
end

local ove_0_58

function ove_0_24.SetInterval(arg_81_0, arg_81_1, arg_81_2, arg_81_3)
	-- print 81
	if not ove_0_58 then
		function ove_0_58(arg_82_0, arg_82_1, arg_82_2, arg_82_3, arg_82_4)
			-- print 82
			if arg_82_0(unpack(arg_82_4 or {})) ~= false and (not arg_82_3 or arg_82_3 > 1) then
				ove_0_24.DelayAction(ove_0_58, arg_82_2 - (os.clock() - arg_82_1 - arg_82_2), {
					arg_82_0,
					arg_82_1 + arg_82_2,
					arg_82_2,
					arg_82_3 and arg_82_3 - 1,
					arg_82_4
				})
			end
		end
	end

	ove_0_24.DelayAction(ove_0_58, arg_81_1, {
		arg_81_0,
		os.clock(),
		arg_81_1 or 0,
		arg_81_2,
		arg_81_3
	})
end

function ove_0_24.IsTableEmpty(arg_83_0)
	-- print 83
	for iter_83_0, iter_83_1 in pairs(arg_83_0) do
		return false
	end

	return true
end

local ove_0_59 = ove_0_24.EvadeVersion()

function ove_0_24.IsOnEvadePolygon(arg_84_0)
	-- print 84
	if ove_0_59 == 0 then
		return false
	end

	if ove_0_59 == 1 then
		for iter_84_0 = 1, #ove_0_25.core.skillshots do
			local slot_84_0 = ove_0_25.core.skillshots[iter_84_0]

			if slot_84_0 and slot_84_0.polygon and slot_84_0.polygon:Contains(arg_84_0) ~= 0 and (not slot_84_0.data.collision or #slot_84_0.data.collision == 0) and (ove_0_25.menu.evade_spells[slot_84_0.data.menu_name] == nil or not ove_0_21.menu.combat.key:get() and ove_0_25.menu.evade_spells[slot_84_0.data.menu_name] and ove_0_25.menu.evade_spells[slot_84_0.data.menu_name].standard_enable:get() or ove_0_21.menu.combat.key:get() and ove_0_25.menu.evade_spells[slot_84_0.data.menu_name] and ove_0_25.menu.evade_spells[slot_84_0.data.menu_name].combat_enable:get()) then
				return true
			end
		end
	end

	if ove_0_59 == 2 then
		for iter_84_1 = ove_0_25.core.skillshots.n, 1, -1 do
			local slot_84_1 = ove_0_25.core.skillshots[iter_84_1]

			if slot_84_1 and slot_84_1.evade_allowed and slot_84_1.contains and slot_84_1:contains(arg_84_0) and (not slot_84_1.data.collision or slot_84_1.data.collision == 0) then
				return true
			end
		end
	end

	return false
end

function ove_0_24.InGameTime()
	-- print 85
	local slot_85_0 = game.time / 60
	local slot_85_1 = math.floor(slot_85_0)
	local slot_85_2 = math.floor((slot_85_0 - slot_85_1) * 60)
	local slot_85_3 = 0

	if slot_85_2 < 10 then
		slot_85_3 = tostring(slot_85_1 .. ":" .. "0" .. slot_85_2)
	else
		slot_85_3 = tostring(slot_85_1 .. ":" .. slot_85_2)
	end

	return slot_85_3
end

function ove_0_24.IsGameModeURF()
	-- print 86
	return game.mode == "URF" or game.mode == "ARURF"
end

local ove_0_60
local ove_0_61 = 0

function ove_0_24.GetFlashSlot()
	-- print 87
	if ove_0_61 < game.time then
		if player:spellSlot(4).name == ove_0_37.SummonerFlash_Slot.slot_name then
			ove_0_60 = 4
		elseif player:spellSlot(5).name == ove_0_37.SummonerFlash_Slot.slot_name then
			ove_0_60 = 5
		else
			ove_0_60 = nil
		end

		ove_0_61 = game.time + 3
	end

	return ove_0_60
end

local ove_0_62
local ove_0_63 = 0

function ove_0_24.GetExhaustSlot()
	-- print 88
	if ove_0_63 < game.time then
		if player:spellSlot(4).name == ove_0_37.SummonerExhaust_Slot.slot_name then
			ove_0_62 = 4
		elseif player:spellSlot(5).name == ove_0_37.SummonerExhaust_Slot.slot_name then
			ove_0_62 = 5
		else
			ove_0_62 = nil
		end

		ove_0_63 = game.time + 3
	end

	return ove_0_62
end

local ove_0_64
local ove_0_65 = 0

function ove_0_24.GetHealSlot()
	-- print 89
	if ove_0_65 < game.time then
		if player:spellSlot(4).name == ove_0_37.SummonerHeal_Slot.slot_name then
			ove_0_64 = 4
		elseif player:spellSlot(5).name == ove_0_37.SummonerHeal_Slot.slot_name then
			ove_0_64 = 5
		else
			ove_0_64 = nil
		end

		ove_0_65 = game.time + 3
	end

	return ove_0_64
end

local ove_0_66
local ove_0_67 = 0

function ove_0_24.GetBarrierSlot()
	-- print 90
	if ove_0_67 < game.time then
		if player:spellSlot(4).name == ove_0_37.SummonerBarrier_Slot.slot_name then
			ove_0_66 = 4
		elseif player:spellSlot(5).name == ove_0_37.SummonerBarrier_Slot.slot_name then
			ove_0_66 = 5
		else
			ove_0_66 = nil
		end

		ove_0_67 = game.time + 3
	end

	return ove_0_66
end

local ove_0_68
local ove_0_69 = 0

function ove_0_24.GetIgniteSlot()
	-- print 91
	if ove_0_69 < game.time then
		if player:spellSlot(4).name == ove_0_37.SummonerIgnite_Slot.slot_name then
			ove_0_68 = 4
		elseif player:spellSlot(5).name == ove_0_37.SummonerIgnite_Slot.slot_name then
			ove_0_68 = 5
		else
			ove_0_68 = nil
		end

		ove_0_69 = game.time + 3
	end

	return ove_0_68
end

local ove_0_70
local ove_0_71 = 0

function ove_0_24.GetTeleportSlot()
	-- print 92
	if ove_0_71 < game.time then
		if player:spellSlot(4).name == ove_0_37.SummonerTeleport_Slot.slot_name then
			ove_0_70 = 4
		elseif player:spellSlot(5).name == ove_0_37.SummonerTeleport_Slot.slot_name then
			ove_0_70 = 5
		else
			ove_0_70 = nil
		end

		ove_0_71 = game.time + 3
	end

	return ove_0_70
end

function ove_0_24.VectorExtend(arg_93_0, arg_93_1, arg_93_2)
	-- print 93
	return arg_93_0 + arg_93_2 * (arg_93_1 - arg_93_0):norm()
end

function ove_0_24.HasAppliedInvulnerability(arg_94_0)
	-- print 94
	return arg_94_0.buff[ove_0_35.Kindred_R_Buff] or arg_94_0.buff[ove_0_35.Kayle_R_Buff] or arg_94_0.buff[ove_0_35.Tryndamere_R_Buff] or arg_94_0.buff[ove_0_35.Taric_R_Buff]
end

function ove_0_24.IsInvulnerable(arg_95_0)
	-- print 95
	return arg_95_0.buff[ove_0_35.BUFF_ENUM_INVULNERABILITY] or arg_95_0.buff[ove_0_35.Kindred_R_Buff] or arg_95_0.buff[ove_0_35.Kayle_R_Buff] or arg_95_0.buff[ove_0_35.Tryndamere_R_Buff] or arg_95_0.buff[ove_0_35.Taric_R_Buff]
end

function ove_0_24.IsInvulnerableMob(arg_96_0)
	-- print 96
	return arg_96_0.buff[ove_0_35.BUFF_ENUM_INVULNERABILITY] or arg_96_0.buff[ove_0_35.Kindred_R_Buff]
end

function ove_0_24.IsValidTarget(arg_97_0)
	-- print 97
	return arg_97_0 and arg_97_0.ptr ~= 0 and arg_97_0.isVisible and not arg_97_0.isDead and arg_97_0.isTargetable and not arg_97_0.buff[ove_0_35.BUFF_ENUM_INVULNERABILITY]
end

function ove_0_24.IsValidTarget2(arg_98_0)
	-- print 98
	return arg_98_0 and not arg_98_0.isDead and arg_98_0.isVisible and arg_98_0.isTargetable
end

function ove_0_24.IsValidTargetNoBuff(arg_99_0)
	-- print 99
	return arg_99_0 and arg_99_0.ptr ~= 0 and not arg_99_0.isDead and arg_99_0.isVisible and arg_99_0.isTargetable
end

local ove_0_72 = {
	TeemoMushroom = true,
	YellowTrinket = true,
	GangplankBarrel = true,
	PerksZombieWard = true,
	JhinTrap = true,
	JammerDevice = true,
	BlueTrinket = true,
	KalistaSpawn = true,
	SightWard = true,
	VoidGate = true,
	NidaleeSpear = true
}

function ove_0_24.valid_minion(arg_100_0)
	-- print 100
	return arg_100_0 and arg_100_0.ptr ~= 0 and arg_100_0.isVisible and not arg_100_0.isDead and arg_100_0.isTargetable and not ove_0_72[arg_100_0.charName] and not arg_100_0.buff[ove_0_35.BUFF_ENUM_INVULNERABILITY]
end

function ove_0_24.is_valid(arg_101_0)
	-- print 101
	return arg_101_0 and arg_101_0.ptr ~= 0 and not arg_101_0.isDead and arg_101_0.isVisible and arg_101_0.isTargetable
end

function ove_0_24.valid_minion_in_range(arg_102_0, arg_102_1, arg_102_2)
	-- print 102
	local slot_102_0 = arg_102_2 * arg_102_2

	return arg_102_0 and slot_102_0 >= arg_102_1:distSqr(arg_102_0.pos2D) and arg_102_0.isVisible and not arg_102_0.isDead and arg_102_0.isTargetable and not ove_0_72[arg_102_0.charName] and not arg_102_0.buff[ove_0_35.BUFF_ENUM_INVULNERABILITY]
end

local ove_0_73 = {
	Sru_Crab = true,
	SLIME_Crab = true
}

function ove_0_24.IsMobAggroed(arg_103_0, arg_103_1)
	-- print 103
	if arg_103_0.team == TEAM_NEUTRAL and arg_103_1 then
		if ove_0_73[arg_103_0.charName] then
			if arg_103_0.moveSpeed == 154.625 and arg_103_0.health >= arg_103_0.maxHealth then
				return false
			end
		elseif not arg_103_0.path.isActive and arg_103_0.health >= arg_103_0.maxHealth then
			return false
		end
	end

	return true
end

local ove_0_74 = {
	summonerexhaust = true,
	nasusw = true,
	timewarp = true,
	sylasr = true,
	tahmkenchrwrapper = true,
	kindredewrapper = true
}

function ove_0_24.IsSpellValidLower(arg_104_0)
	-- print 104
	if ove_0_74[arg_104_0] then
		return false
	end

	if arg_104_0:find("controller") then
		return false
	end

	return true
end

local ove_0_75 = {
	SummonerExhaust = true,
	NasusW = true,
	SylasR = true,
	KindredEWrapper = true,
	TimeWarp = true,
	TahmKenchRWrapper = true
}

function ove_0_24.IsSpellValidUpper(arg_105_0)
	-- print 105
	if ove_0_75[arg_105_0] then
		return false
	end

	if arg_105_0:find("Controller") then
		return false
	end

	return true
end

function ove_0_24.count_enemies_in_range(arg_106_0, arg_106_1)
	-- print 106
	local slot_106_0 = {}
	local slot_106_1 = arg_106_1 * arg_106_1

	for iter_106_0 = 0, objManager.enemies_n - 1 do
		local slot_106_2 = objManager.enemies[iter_106_0]

		if ove_0_24.IsValidTarget(slot_106_2) and slot_106_1 >= arg_106_0:distSqr(slot_106_2.pos2D) then
			slot_106_0[#slot_106_0 + 1] = slot_106_2
		end
	end

	return slot_106_0
end

function ove_0_24.count_enemies_in_aa_range(arg_107_0, arg_107_1)
	-- print 107
	local slot_107_0 = {}
	local slot_107_1 = arg_107_1 or 0

	for iter_107_0 = 0, objManager.enemies_n - 1 do
		local slot_107_2 = objManager.enemies[iter_107_0]

		if ove_0_24.IsValidTargetNoBuff(slot_107_2) then
			local slot_107_3 = arg_107_0.attackRange + arg_107_0.boundingRadius + slot_107_2.boundingRadius + slot_107_1

			if slot_107_3 * slot_107_3 >= arg_107_0.path.serverPos2D:distSqr(slot_107_2.path.serverPos2D) then
				slot_107_0[#slot_107_0 + 1] = slot_107_2
			end
		end
	end

	return slot_107_0
end

function ove_0_24.count_allies_in_range(arg_108_0, arg_108_1, arg_108_2)
	-- print 108
	local slot_108_0 = {}
	local slot_108_1 = arg_108_1 * arg_108_1

	for iter_108_0 = 0, objManager.allies_n - 1 do
		local slot_108_2 = objManager.allies[iter_108_0]

		if ove_0_24.IsValidTarget(slot_108_2) and slot_108_1 >= arg_108_0:distSqr(slot_108_2.pos2D) and (not arg_108_2 or arg_108_2 and slot_108_2.ptr ~= player.ptr) then
			slot_108_0[#slot_108_0 + 1] = slot_108_2
		end
	end

	return slot_108_0
end

function ove_0_24.count_low_allies_in_range(arg_109_0, arg_109_1, arg_109_2)
	-- print 109
	local slot_109_0 = {}
	local slot_109_1 = arg_109_1 * arg_109_1

	for iter_109_0 = 0, objManager.allies_n - 1 do
		local slot_109_2 = objManager.allies[iter_109_0]

		if ove_0_24.IsValidTarget(slot_109_2) and slot_109_1 > arg_109_0:distSqr(slot_109_2.pos2D) and arg_109_2 >= slot_109_2.health / slot_109_2.maxHealth * 100 then
			slot_109_0[#slot_109_0 + 1] = slot_109_2
		end
	end

	return slot_109_0
end

function ove_0_24.count_minions_in_range(arg_110_0, arg_110_1, arg_110_2)
	-- print 110
	local slot_110_0 = {}

	for iter_110_0 = 0, objManager.minions.size[arg_110_2] - 1 do
		local slot_110_1 = objManager.minions[arg_110_2][iter_110_0]

		if ove_0_24.valid_minion_in_range(slot_110_1, arg_110_0, arg_110_1) then
			slot_110_0[#slot_110_0 + 1] = slot_110_1
		end
	end

	return slot_110_0
end

function ove_0_24.IsAroundCollisionableObjects(arg_111_0, arg_111_1)
	-- print 111
	for iter_111_0 = 0, objManager.minions.size[TEAM_ENEMY] - 1 do
		local slot_111_0 = objManager.minions[TEAM_ENEMY][iter_111_0]

		if slot_111_0.isVisible and arg_111_1 > slot_111_0.pos2D:distSqr(arg_111_0) and slot_111_0.isTargetable and not ove_0_72[slot_111_0.charName] then
			return true
		end
	end

	for iter_111_1 = 0, objManager.minions.size[TEAM_NEUTRAL] - 1 do
		local slot_111_1 = objManager.minions[TEAM_NEUTRAL][iter_111_1]

		if slot_111_1.isVisible and arg_111_1 > slot_111_1.pos2D:distSqr(arg_111_0) and slot_111_1.isTargetable and not ove_0_72[slot_111_1.charName] then
			return true
		end
	end

	return false
end

function ove_0_24.count_moving_minions_in_range(arg_112_0, arg_112_1, arg_112_2)
	-- print 112
	local slot_112_0 = {}

	for iter_112_0 = 0, objManager.minions.size[arg_112_2] - 1 do
		local slot_112_1 = objManager.minions[arg_112_2][iter_112_0]

		if ove_0_24.valid_minion_in_range(slot_112_1, arg_112_0, arg_112_1) and slot_112_1.path.isActive then
			slot_112_0[#slot_112_0 + 1] = slot_112_1
		end
	end

	return slot_112_0
end

function ove_0_24.is_under_ally_turret(arg_113_0)
	-- print 113
	for iter_113_0 = 0, objManager.turrets.size[TEAM_ALLY] - 1 do
		local slot_113_0 = objManager.turrets[TEAM_ALLY][iter_113_0]

		if slot_113_0 and not slot_113_0.isDead and slot_113_0.pos2D:distSqr(arg_113_0) <= 810000 then
			return true
		end
	end

	return false
end

function ove_0_24.is_under_enemy_turret(arg_114_0)
	-- print 114
	for iter_114_0 = 0, objManager.turrets.size[TEAM_ENEMY] - 1 do
		local slot_114_0 = objManager.turrets[TEAM_ENEMY][iter_114_0]

		if slot_114_0 and not slot_114_0.isDead and slot_114_0.pos2D:distSqr(arg_114_0) <= 810000 then
			return true
		end
	end

	return false
end

local ove_0_76 = {}

function ove_0_24.IsUnderTurret(arg_115_0, arg_115_1, arg_115_2)
	-- print 115
	for iter_115_0 = 0, objManager.turrets.size[arg_115_1] - 1 do
		local slot_115_0 = objManager.turrets[arg_115_1][iter_115_0]

		if slot_115_0 and not slot_115_0.isDead and slot_115_0.pos2D:distSqr(arg_115_0.pos2D) <= 810000 then
			if arg_115_2 then
				if not ove_0_76[arg_115_0.ptr] then
					ove_0_76[arg_115_0.ptr] = game.time + 0.5
				end

				if ove_0_76[arg_115_0.ptr] == 0 then
					ove_0_76[arg_115_0.ptr] = game.time + 0.5
				end

				if ove_0_76[arg_115_0.ptr] >= game.time then
					return true
				end

				local slot_115_1 = slot_115_0:basicAttack(0).target

				if slot_115_1 then
					for iter_115_1 = 0, objManager.allies_n - 1 do
						local slot_115_2 = objManager.allies[iter_115_1]

						if ove_0_24.IsValidTargetNoBuff(slot_115_2) and slot_115_0.pos2D:distSqr(slot_115_2.pos2D) <= 810000 and slot_115_1.ptr == slot_115_2.ptr then
							return false
						end
					end
				end
			end

			return true
		end
	end

	if ove_0_76[arg_115_0.ptr] then
		ove_0_76[arg_115_0.ptr] = 0
	end

	return false
end

local ove_0_77, ove_0_78, ove_0_79 = (function()
	-- print 116
	local slot_116_0 = graphics.height / 1080 * 0.9
	local slot_116_1 = vec2(163 * slot_116_0, 123 * slot_116_0)
	local slot_116_2 = 105 * slot_116_0
	local slot_116_3 = 11 * slot_116_0

	if graphics.width == 3840 and graphics.height == 2160 then
		return vec2(293, 221.6), 189, 19.9
	end

	if graphics.width == 3818 and graphics.height == 2044 then
		return vec2(278, 210), 178, 18.2
	end

	if graphics.width == 2560 and graphics.height == 1600 then
		return vec2(217, 163.9), 140, 15
	end

	if graphics.width == 2560 and graphics.height == 1440 then
		return vec2(196, 147.9), 126, 13.2
	end

	if graphics.width == 2048 and graphics.height == 1536 then
		return vec2(209, 157.5), 134, 13
	end

	if graphics.width == 1920 and graphics.height == 1440 then
		return vec2(196, 148), 126, 12
	end

	if graphics.width <= 1920 and graphics.height <= 1200 then
		return vec2(163, 123), 105, 11
	end

	return slot_116_1, slot_116_2, slot_116_3
end)()

function ove_0_24.GetBarData()
	-- print 117
	return ove_0_77, ove_0_78, ove_0_79
end

function ove_0_24.IsOrbAttackPaused()
	-- print 118
	local slot_118_0 = false

	if ove_0_21.menu.combat.key:get() and ove_0_21.menu.combat.mod then
		if ove_0_21.menu.combat.mod:get() == 2 and keyboard.isKeyDown(1) then
			slot_118_0 = true
		end

		if ove_0_21.menu.combat.mod:get() == 3 and ove_0_21.menu.combat.disable_attacks_key:get() then
			slot_118_0 = true
		end

		if ove_0_21.menu.combat.mod:get() == 4 and ove_0_21.menu.combat.disable_attacks_level:get() <= player.levelRef then
			slot_118_0 = true
		end
	end

	return slot_118_0
end

function ove_0_24.IsOrbPanicClearActive()
	-- print 119
	local slot_119_0 = false

	if ove_0_21.menu.lane_clear.key:get() and ove_0_21.menu.lane_clear.mod then
		if ove_0_21.menu.lane_clear.mod:get() == 2 and keyboard.isKeyDown(1) then
			slot_119_0 = true
		end

		if ove_0_21.menu.lane_clear.mod:get() == 3 and ove_0_21.menu.lane_clear.panic_key:get() then
			slot_119_0 = true
		end
	end

	return slot_119_0
end

local ove_0_80 = false

if ove_0_39.Yuumi or ove_0_43.Yuumi then
	ove_0_80 = true
end

local ove_0_81 = {}
local ove_0_82 = {
	YuumiWAlly = true
}

function ove_0_24.IsRecalling(arg_120_0)
	-- print 120
	if not ove_0_80 then
		return arg_120_0.isRecalling
	end

	if not arg_120_0 or arg_120_0.ptr == 0 then
		return false
	end

	if not ove_0_81[arg_120_0.ptr] then
		ove_0_81[arg_120_0.ptr] = 0
	end

	if arg_120_0.isRecalling then
		if not ove_0_82[arg_120_0.recallName] then
			ove_0_81[arg_120_0.ptr] = game.time + 0.1
		end

		if ove_0_81[arg_120_0.ptr] > game.time then
			return true
		end
	end

	return false
end

function ove_0_24.HasRecallBuff(arg_121_0)
	-- print 121
	return arg_121_0.buff[ove_0_35.RecallBuff]
end

function ove_0_24.IsSpellSlotReady(arg_122_0, arg_122_1)
	-- print 122
	local slot_122_0, slot_122_1 = ove_0_21.core.can_cast_spell(arg_122_0, arg_122_1)

	if slot_122_0 then
		return true
	end

	return false
end

function ove_0_24.spell_mana_cost(arg_123_0)
	-- print 123
	local slot_123_0 = 0

	if arg_123_0 == _Q then
		slot_123_0 = player.manaCost0
	elseif arg_123_0 == _W then
		slot_123_0 = player.manaCost1
	elseif arg_123_0 == _E then
		slot_123_0 = player.manaCost2
	elseif arg_123_0 == _R then
		slot_123_0 = player.manaCost3
	end

	return slot_123_0
end

function ove_0_24.IsHardLockedLinearAPI(arg_124_0, arg_124_1, arg_124_2)
	-- print 124
	if ove_0_20.trace.linear.hardlock(arg_124_0, arg_124_1, arg_124_2) then
		if arg_124_2.buff[ove_0_35.Thresh_Q_Buff] then
			if arg_124_2.buff[ove_0_35.Thresh_Q_Buff].startTime - game.time < -0.15 then
				return true
			end
		else
			return true
		end
	end

	if ove_0_20.trace.linear.hardlockmove(arg_124_0, arg_124_1, arg_124_2) then
		return true
	end

	return false
end

function ove_0_24.IsHardLockedCircularAPI(arg_125_0, arg_125_1, arg_125_2)
	-- print 125
	if ove_0_20.trace.circular.hardlock(arg_125_0, arg_125_1, arg_125_2) then
		if arg_125_2.buff[ove_0_35.Thresh_Q_Buff] then
			if arg_125_2.buff[ove_0_35.Thresh_Q_Buff].startTime - game.time < -0.15 then
				return true
			end
		else
			return true
		end
	end

	if ove_0_20.trace.circular.hardlockmove(arg_125_0, arg_125_1, arg_125_2) then
		return true
	end

	return false
end

local ove_0_83 = {
	ove_0_35.BUFF_ENUM_STUN,
	ove_0_35.BUFF_ENUM_SNARE,
	ove_0_35.BUFF_ENUM_SUPPRESSION,
	ove_0_35.BUFF_ENUM_KNOCKUP,
	ove_0_35.BUFF_ENUM_ASLEEP
}

function ove_0_24.IsHardLocked(arg_126_0, arg_126_1)
	-- print 126
	local slot_126_0 = -0.033 - network.latency * 0.5

	if arg_126_1 == nil or arg_126_1 == 0 then
		if arg_126_0.buff[ove_0_35.Thresh_Q_Buff] then
			if arg_126_0.buff[ove_0_35.Thresh_Q_Buff].startTime - game.time < -0.15 then
				return true
			end

			return false
		end

		for iter_126_0 = 1, #ove_0_83 do
			local slot_126_1 = arg_126_0.buff[ove_0_83[iter_126_0]]

			if slot_126_1 and slot_126_0 > slot_126_1.startTime - game.time then
				return true
			end
		end
	else
		if arg_126_0.buff[ove_0_35.Thresh_Q_Buff] then
			if arg_126_0.buff[ove_0_35.Thresh_Q_Buff].startTime - game.time < -0.15 and arg_126_0.buff[ove_0_35.Thresh_Q_Buff].endTime - game.time > arg_126_1 + network.latency then
				return true
			end

			return false
		end

		for iter_126_1 = 1, #ove_0_83 do
			local slot_126_2 = arg_126_0.buff[ove_0_83[iter_126_1]]

			if slot_126_2 and slot_126_0 > slot_126_2.startTime - game.time and slot_126_2.endTime - game.time > arg_126_1 + network.latency then
				return true
			end
		end
	end

	return false
end

local ove_0_84 = {
	ove_0_35.BUFF_ENUM_TAUNT,
	ove_0_35.BUFF_ENUM_POLYMORPH,
	ove_0_35.BUFF_ENUM_FEAR,
	ove_0_35.BUFF_ENUM_CHARM,
	ove_0_35.BUFF_ENUM_KNOCKBACK,
	ove_0_35.BUFF_ENUM_GROUNDED
}

function ove_0_24.IsSoftLocked(arg_127_0, arg_127_1)
	-- print 127
	local slot_127_0 = -0.033 - network.latency * 0.5

	if arg_127_1 == nil or arg_127_1 == 0 then
		for iter_127_0 = 1, #ove_0_84 do
			local slot_127_1 = arg_127_0.buff[ove_0_84[iter_127_0]]

			if slot_127_1 and slot_127_0 > slot_127_1.startTime - game.time then
				return true
			end
		end
	else
		for iter_127_1 = 1, #ove_0_84 do
			local slot_127_2 = arg_127_0.buff[ove_0_84[iter_127_1]]

			if slot_127_2 and slot_127_0 > slot_127_2.startTime - game.time and slot_127_2.endTime - game.time > arg_127_1 + network.latency then
				return true
			end
		end
	end

	return false
end

function ove_0_24.IsCustomLocked(arg_128_0, arg_128_1, arg_128_2)
	-- print 128
	local slot_128_0 = -0.033 - network.latency * 0.5

	if arg_128_2 == nil or arg_128_2 == 0 then
		if arg_128_1.buff[ove_0_35.Thresh_Q_Buff] then
			if arg_128_1.buff[ove_0_35.Thresh_Q_Buff].startTime - game.time < -0.15 then
				return true
			end

			return false
		end

		for iter_128_0 = 1, #arg_128_0 do
			local slot_128_1 = arg_128_1.buff[arg_128_0[iter_128_0]]

			if slot_128_1 and slot_128_0 > slot_128_1.startTime - game.time then
				return true
			end
		end
	else
		if arg_128_1.buff[ove_0_35.Thresh_Q_Buff] then
			if arg_128_1.buff[ove_0_35.Thresh_Q_Buff].startTime - game.time < -0.15 and arg_128_1.buff[ove_0_35.Thresh_Q_Buff].endTime - game.time > arg_128_2 + network.latency then
				return true
			end

			return false
		end

		for iter_128_1 = 1, #arg_128_0 do
			local slot_128_2 = arg_128_1.buff[arg_128_0[iter_128_1]]

			if slot_128_2 and slot_128_0 > slot_128_2.startTime - game.time and slot_128_2.endTime - game.time > arg_128_2 + network.latency then
				return true
			end
		end
	end

	return false
end

function ove_0_24.IsCustomLockedNoCheck(arg_129_0, arg_129_1, arg_129_2)
	-- print 129
	if arg_129_2 == nil or arg_129_2 == 0 then
		for iter_129_0 = 1, #arg_129_0 do
			if arg_129_1.buff[arg_129_0[iter_129_0]] then
				return true
			end
		end
	else
		for iter_129_1 = 1, #arg_129_0 do
			local slot_129_0 = arg_129_1.buff[arg_129_0[iter_129_1]]

			if slot_129_0 and slot_129_0.endTime - game.time > arg_129_2 + network.latency then
				return true
			end
		end
	end

	return false
end

function ove_0_24.IsCustomLockHitLinear(arg_130_0, arg_130_1, arg_130_2, arg_130_3)
	-- print 130
	local slot_130_0 = arg_130_3.buff[ove_0_35.Thresh_Q_Buff]

	if slot_130_0 then
		if slot_130_0.startTime - game.time < -0.15 then
			local slot_130_1 = slot_130_0.endTime - game.time
			local slot_130_2 = arg_130_1.boundingRadiusMod > 0 and (arg_130_1.width + arg_130_3.boundingRadius * arg_130_1.boundingRadiusMod) / arg_130_3.moveSpeed or arg_130_1.width / arg_130_3.moveSpeed
			local slot_130_3 = arg_130_2:length()
			local slot_130_4 = arg_130_1.speed == math.huge and 0 or slot_130_3 / arg_130_1.speed

			if slot_130_1 + slot_130_2 > slot_130_4 + arg_130_1.delay + network.latency then
				return true
			end
		end

		return false
	end

	local slot_130_5 = -0.033 - network.latency * 0.5

	for iter_130_0 = 1, #arg_130_0 do
		local slot_130_6 = arg_130_3.buff[arg_130_0[iter_130_0]]

		if slot_130_6 and slot_130_5 > slot_130_6.startTime - game.time then
			local slot_130_7 = slot_130_6.endTime - game.time
			local slot_130_8 = arg_130_1.boundingRadiusMod > 0 and (arg_130_1.width + arg_130_3.boundingRadius * arg_130_1.boundingRadiusMod) / arg_130_3.moveSpeed or arg_130_1.width / arg_130_3.moveSpeed
			local slot_130_9 = arg_130_2:length()
			local slot_130_10 = arg_130_1.speed == math.huge and 0 or slot_130_9 / arg_130_1.speed

			if slot_130_7 + slot_130_8 > slot_130_10 + arg_130_1.delay + network.latency then
				return true
			end
		end
	end

	return false
end

function ove_0_24.IsCustomLockHitCircular(arg_131_0, arg_131_1, arg_131_2, arg_131_3)
	-- print 131
	local slot_131_0 = arg_131_3.buff[ove_0_35.Thresh_Q_Buff]

	if slot_131_0 then
		if slot_131_0.startTime - game.time < -0.15 then
			local slot_131_1 = slot_131_0.endTime - game.time
			local slot_131_2 = arg_131_2.startPos:dist(arg_131_2.endPos)
			local slot_131_3 = arg_131_1.speed == math.huge and 0 or mathf.clamp(0, arg_131_1.range, slot_131_2) / arg_131_1.speed
			local slot_131_4 = arg_131_1.radius

			if slot_131_2 > arg_131_1.range then
				slot_131_4 = arg_131_1.radius - (slot_131_2 - arg_131_1.range)
			end

			if slot_131_1 + slot_131_4 / arg_131_3.moveSpeed > network.latency + arg_131_1.delay + slot_131_3 then
				return true
			end
		end

		return false
	end

	local slot_131_5 = -0.033 - network.latency * 0.5

	for iter_131_0 = 1, #arg_131_0 do
		local slot_131_6 = arg_131_3.buff[arg_131_0[iter_131_0]]

		if slot_131_6 and slot_131_5 > slot_131_6.startTime - game.time then
			local slot_131_7 = slot_131_6.endTime - game.time
			local slot_131_8 = arg_131_2.startPos:dist(arg_131_2.endPos)
			local slot_131_9 = arg_131_1.speed == math.huge and 0 or mathf.clamp(0, arg_131_1.range, slot_131_8) / arg_131_1.speed
			local slot_131_10 = arg_131_1.radius

			if slot_131_8 > arg_131_1.range then
				slot_131_10 = arg_131_1.radius - (slot_131_8 - arg_131_1.range)
			end

			if slot_131_7 + slot_131_10 / arg_131_3.moveSpeed > network.latency + arg_131_1.delay + slot_131_9 then
				return true
			end
		end
	end

	return false
end

function ove_0_24.HasBuffType(arg_132_0, arg_132_1, arg_132_2)
	-- print 132
	local slot_132_0 = -0.033 - network.latency * 0.5

	if arg_132_2 == nil or arg_132_2 == 0 then
		if arg_132_1.buff[arg_132_0] and slot_132_0 > arg_132_1.buff[arg_132_0].startTime - game.time then
			return true
		end
	elseif arg_132_1.buff[arg_132_0] and slot_132_0 > arg_132_1.buff[arg_132_0].startTime - game.time and arg_132_1.buff[arg_132_0].endTime - game.time > arg_132_2 + network.latency then
		return true
	end

	return false
end

function ove_0_24.WillHitDasherLinear(arg_133_0, arg_133_1, arg_133_2)
	-- print 133
	if not arg_133_0.path.isDashing then
		return false
	end

	local slot_133_0 = arg_133_0.path.point2D[arg_133_0.path.index]:dist(arg_133_0.pos2D) / arg_133_0.path.dashSpeed
	local slot_133_1 = arg_133_2.boundingRadiusMod > 0 and (arg_133_2.width + arg_133_0.boundingRadius * arg_133_2.boundingRadiusMod) / arg_133_0.moveSpeed or arg_133_2.width / arg_133_0.moveSpeed
	local slot_133_2 = arg_133_1:length()
	local slot_133_3 = arg_133_2.speed == math.huge and 0 or slot_133_2 / arg_133_2.speed

	if slot_133_0 + slot_133_1 >= slot_133_3 + arg_133_2.delay then
		return true
	end

	return false
end

function ove_0_24.WillHitDasherLinearCustom(arg_134_0, arg_134_1, arg_134_2, arg_134_3)
	-- print 134
	if not arg_134_0.path.isDashing then
		return false
	end

	if arg_134_3 <= arg_134_0.path.point2D[arg_134_0.path.index]:dist(arg_134_0.pos2D) / arg_134_0.path.dashSpeed + (arg_134_2.boundingRadiusMod > 0 and (arg_134_2.width + arg_134_0.boundingRadius * arg_134_2.boundingRadiusMod) / arg_134_0.moveSpeed or arg_134_2.width / arg_134_0.moveSpeed) then
		return true
	end

	return false
end

function ove_0_24.WillHitDasherCircle(arg_135_0, arg_135_1, arg_135_2)
	-- print 135
	if not arg_135_0.path.isDashing then
		return false
	end

	local slot_135_0 = arg_135_0.path.point2D[arg_135_0.path.index]:dist(arg_135_0.path.serverPos2D) / arg_135_0.path.dashSpeed
	local slot_135_1 = arg_135_1.startPos:dist(arg_135_1.endPos)
	local slot_135_2 = arg_135_2.speed == math.huge and 0 or mathf.clamp(0, arg_135_2.range, slot_135_1) / arg_135_2.speed
	local slot_135_3 = arg_135_2.radius

	if slot_135_1 > arg_135_2.range then
		slot_135_3 = arg_135_2.radius - (slot_135_1 - arg_135_2.range)
	end

	if slot_135_0 + slot_135_3 / arg_135_0.moveSpeed >= slot_135_2 + arg_135_2.delay then
		return true
	end

	return false
end

function ove_0_24.WillHitDasherCircleCustom(arg_136_0, arg_136_1, arg_136_2, arg_136_3)
	-- print 136
	if not arg_136_0.path.isDashing then
		return false
	end

	local slot_136_0 = arg_136_0.path.point2D[arg_136_0.path.index]:dist(arg_136_0.path.serverPos2D) / arg_136_0.path.dashSpeed
	local slot_136_1 = arg_136_1.startPos:dist(arg_136_1.endPos)
	local slot_136_2 = arg_136_2.radius

	if slot_136_1 > arg_136_2.range then
		slot_136_2 = arg_136_2.radius - (slot_136_1 - arg_136_2.range)
	end

	if arg_136_3 <= slot_136_0 + slot_136_2 / arg_136_0.moveSpeed then
		return true
	end

	return false
end

function ove_0_24.GetSlowPercent(arg_137_0)
	-- print 137
	local slot_137_0 = arg_137_0.baseMoveSpeed - arg_137_0.moveSpeed
	local slot_137_1 = slot_137_0 > 0 and (arg_137_0.baseMoveSpeed - slot_137_0) / arg_137_0.baseMoveSpeed * 100 or 100

	return ove_0_22(slot_137_1 - 100)
end

function ove_0_24.GetPercentHealth(arg_138_0)
	-- print 138
	local slot_138_0 = arg_138_0 or player

	return slot_138_0.health / slot_138_0.maxHealth * 100
end

function ove_0_24.GetMissingPercentHealth(arg_139_0)
	-- print 139
	local slot_139_0 = arg_139_0 or player

	return (slot_139_0.maxHealth - slot_139_0.health) / slot_139_0.maxHealth * 100
end

function ove_0_24.GetPercentMana(arg_140_0)
	-- print 140
	local slot_140_0 = arg_140_0 or player

	return slot_140_0.mana / slot_140_0.maxMana * 100
end

function ove_0_24.GetMissingPercentMana(arg_141_0)
	-- print 141
	local slot_141_0 = arg_141_0 or player

	return (slot_141_0.maxMana - slot_141_0.mana) / slot_141_0.maxMana * 100
end

function ove_0_24.GetPercentPar(arg_142_0)
	-- print 142
	local slot_142_0 = arg_142_0 or player

	return slot_142_0.par / slot_142_0.maxPar * 100
end

function ove_0_24.GetShieldedHealth(arg_143_0, arg_143_1)
	-- print 143
	local slot_143_0 = 0

	if arg_143_0 == "AD" then
		slot_143_0 = arg_143_1.physicalShield
	elseif arg_143_0 == "AP" then
		slot_143_0 = arg_143_1.magicalShield
	elseif arg_143_0 == "ALL" then
		slot_143_0 = arg_143_1.allShield
	end

	return arg_143_1.health + slot_143_0
end

function ove_0_24.GetTotalAD(arg_144_0)
	-- print 144
	local slot_144_0 = arg_144_0 or player

	return (slot_144_0.baseAttackDamage + slot_144_0.flatPhysicalDamageMod) * slot_144_0.percentPhysicalDamageMod
end

function ove_0_24.GetTotalAP(arg_145_0)
	-- print 145
	local slot_145_0 = arg_145_0 or player

	return slot_145_0.flatMagicDamageMod * slot_145_0.percentMagicDamageMod
end

function ove_0_24.PhysicalReduction(arg_146_0, arg_146_1)
	-- print 146
	local slot_146_0 = arg_146_1 or player
	local slot_146_1 = (arg_146_0.bonusArmor * slot_146_0.percentBonusArmorPenetration + (arg_146_0.armor - arg_146_0.bonusArmor)) * slot_146_0.percentArmorPenetration
	local slot_146_2 = slot_146_1 - slot_146_0.physicalLethality * (0.6 + 0.4 * slot_146_0.levelRef / 18)

	if slot_146_2 < 0 then
		slot_146_2 = 0
	end

	return slot_146_1 >= 0 and 100 / (100 + slot_146_2) or 2 - 100 / (100 - slot_146_2)
end

function ove_0_24.MagicReduction(arg_147_0, arg_147_1)
	-- print 147
	local slot_147_0 = arg_147_1 or player
	local slot_147_1 = arg_147_0.spellBlock * slot_147_0.percentMagicPenetration - slot_147_0.flatMagicPenetration

	return slot_147_1 >= 0 and 100 / (100 + slot_147_1) or 2 - 100 / (100 - slot_147_1)
end

function ove_0_24.CalculateAADamage(arg_148_0, arg_148_1)
	-- print 148
	local slot_148_0 = arg_148_1 or player

	if arg_148_0 then
		return ove_0_24.GetTotalAD(slot_148_0) * ove_0_24.PhysicalReduction(arg_148_0, slot_148_0)
	end

	return 0
end

function ove_0_24.CalculatePhysicalDamage(arg_149_0, arg_149_1, arg_149_2)
	-- print 149
	local slot_149_0 = arg_149_2 or player

	if arg_149_0 then
		return arg_149_1 * ove_0_24.PhysicalReduction(arg_149_0, slot_149_0)
	end

	return 0
end

function ove_0_24.CalculateMagicDamage(arg_150_0, arg_150_1, arg_150_2)
	-- print 150
	local slot_150_0 = arg_150_2 or player

	if arg_150_0 then
		return arg_150_1 * ove_0_24.MagicReduction(arg_150_0, slot_150_0)
	end

	return 0
end

function ove_0_24.GetAARange(arg_151_0)
	-- print 151
	return player.attackRange + player.boundingRadius + (arg_151_0 and arg_151_0.boundingRadius or 0)
end

function ove_0_24.GetPredictedPos(arg_152_0, arg_152_1)
	-- print 152
	if not ove_0_24.IsValidTarget(arg_152_0) or not arg_152_0.path or not arg_152_1 or not arg_152_0.moveSpeed then
		return arg_152_0
	end

	local slot_152_0 = ove_0_20.core.lerp(arg_152_0.path, network.latency + arg_152_1, arg_152_0.moveSpeed)

	return vec3(slot_152_0.x, player.y, slot_152_0.y)
end

local ove_0_85 = 0

function ove_0_24.check_sleep(arg_153_0, arg_153_1)
	-- print 153
	if ove_0_85 < game.time then
		arg_153_0()

		ove_0_85 = game.time + arg_153_1
	end
end

local ove_0_86 = {
	[ove_0_34.RazorbeakMob.charName] = true,
	[ove_0_34.MurkwolfMob.charName] = true,
	[ove_0_34.KrugMob.charName] = true,
	[ove_0_34.GrompMob.charName] = true,
	[ove_0_34.BlueBuffMob.charName] = true,
	[ove_0_34.RedBuffMob.charName] = true,
	[ove_0_34.RiftHeraldMob.charName] = true,
	[ove_0_34.BaronMob.charName] = true,
	[ove_0_34.CrabMob.charName] = true,
	[ove_0_34.OceanDragonMob.charName] = true,
	[ove_0_34.InfernalDragonMob.charName] = true,
	[ove_0_34.ElderDragonMob.charName] = true,
	[ove_0_34.CloudDragonMob.charName] = true,
	[ove_0_34.MountainDragonMob.charName] = true
}

function ove_0_24.IsBigMob(arg_154_0)
	-- print 154
	return ove_0_86[arg_154_0.charName]
end

ove_0_24.movespeed_debuffs = {
	ove_0_35.BUFF_ENUM_SLOW,
	ove_0_35.BUFF_ENUM_TAUNT,
	ove_0_35.BUFF_ENUM_POLYMORPH,
	ove_0_35.BUFF_ENUM_FEAR,
	ove_0_35.BUFF_ENUM_CHARM,
	ove_0_35.BUFF_ENUM_GROUNDED,
	ove_0_35.BUFF_ENUM_DROWSY
}
ove_0_24.healing_priority_list_one = {
	Vex = true,
	Ezreal = true,
	Zeri = true,
	Fiora = true,
	Qiyana = true,
	Quinn = true,
	Draven = true,
	Corki = true,
	Akshan = true,
	Graves = true,
	Gwen = true,
	Samira = true,
	Senna = true,
	Seraphine = true,
	Jayce = true,
	Jhin = true,
	Jinx = true,
	Kaisa = true,
	Kalista = true,
	Sivir = true,
	Soraka = true,
	Kindred = true,
	KogMaw = true,
	Tristana = true,
	Lillia = true,
	Lucian = true,
	Twitch = true,
	Aphelios = true,
	Ashe = true,
	Varus = true,
	Vayne = true,
	MasterYi = true,
	Belveth = true,
	MissFortune = true,
	Viego = true,
	Caitlyn = true,
	Cassiopeia = true,
	Xayah = true,
	Nilah = true,
	Yasuo = true,
	Yone = true,
	Orianna = true
}
ove_0_24.healing_priority_list_two = {
	XinZhao = true,
	Pantheon = true,
	Fizz = true,
	Pyke = true,
	Camille = true,
	Reksai = true,
	Gangplank = true,
	Ahri = true,
	Anivia = true,
	Rell = true,
	Renata = true,
	Renekton = true,
	Rengar = true,
	Riven = true,
	Rumble = true,
	Ryze = true,
	Sett = true,
	Shaco = true,
	Sona = true,
	Sylas = true,
	Syndra = true,
	Taliyah = true,
	Talon = true,
	Teemo = true,
	Akali = true,
	Tryndamere = true,
	TwistedFate = true,
	Annie = true,
	AurelionSol = true,
	Azir = true,
	Veigar = true,
	Velkoz = true,
	Brand = true,
	Viktor = true,
	Vladimir = true,
	Xerath = true,
	Diana = true,
	Ekko = true,
	Elise = true,
	Evelynn = true,
	Zed = true,
	Ziggs = true,
	Zilean = true,
	Zoe = true,
	Zyra = true,
	Heimerdinger = true,
	Irelia = true,
	Ivern = true,
	Jax = true,
	Karma = true,
	Karthus = true,
	Kassadin = true,
	Katarina = true,
	Kayle = true,
	Kennen = true,
	Khazix = true,
	Fiddlesticks = true,
	KSante = true,
	Leblanc = true,
	Lux = true,
	Malzahar = true,
	Milio = true,
	Mordekaiser = true,
	Morgana = true,
	Neeko = true,
	Nidalee = true,
	Nocturne = true
}
ove_0_24.ardent_priority_list_one = {
	Fiora = true,
	Ezreal = true,
	Zeri = true,
	KogMaw = true,
	Akshan = true,
	Kayle = true,
	Kindred = true,
	Tristana = true,
	Samira = true,
	Tryndamere = true,
	Lucian = true,
	Twitch = true,
	Aphelios = true,
	Ashe = true,
	Varus = true,
	Irelia = true,
	MasterYi = true,
	Vayne = true,
	Draven = true,
	Jax = true,
	Viego = true,
	Caitlyn = true,
	Jinx = true,
	Kaisa = true,
	Kalista = true,
	Xayah = true,
	Sivir = true,
	Nilah = true,
	Yasuo = true,
	Yone = true
}
ove_0_24.ardent_priority_list_two = {
	Trundle = true,
	Gwen = true,
	Kled = true,
	XinZhao = true,
	KSante = true,
	Quinn = true,
	Gangplank = true,
	Hecarim = true,
	Gnar = true,
	Teemo = true,
	Graves = true,
	Renekton = true,
	Udyr = true,
	Riven = true,
	Volibear = true,
	Azir = true,
	Belveth = true,
	MissFortune = true,
	Vi = true,
	Jayce = true,
	Jhin = true,
	Camille = true,
	Shyvana = true,
	Warwick = true,
	Corki = true,
	Diana = true,
	Nocturne = true,
	Yorick = true,
	Kennen = true
}
ove_0_24.enum = {}
ove_0_24.enum.slots = {
	w = 1,
	r = 3,
	e = 2,
	q = 0
}
ove_0_24.enum.buff_types = {
	Slow = 10,
	NearSight = 19,
	Blind = 25,
	Snare = 11,
	Haste = 14,
	SpellShield = 4,
	Polymorph = 9,
	Fear = 21,
	Charm = 22,
	SpellImmunity = 15,
	Invisibility = 6,
	Shred = 27,
	Damage = 12,
	Silence = 7,
	Asleep = 34,
	Drowsy = 33,
	Grounded = 32,
	Suppression = 24,
	Knockup = 29,
	Internal = 0,
	AttackSpeedSlow = 18,
	Aura = 1,
	PhysicalImmunity = 16,
	Currency = 20,
	Heal = 13,
	Poison = 23,
	Counter = 26,
	Flee = 28,
	Knockback = 30,
	CombatEnchancer = 2,
	Disarm = 31,
	CombatDehancer = 3,
	Invulnerability = 17,
	Stun = 5,
	Taunt = 8
}

return ove_0_24

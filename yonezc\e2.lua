local ove_0_10 = module.internal("orb")
local ove_0_11 = module.load(header.id, "yone/menu")
local ove_0_12 = player:spellSlot(2)
local ove_0_13 = 0
local ove_0_14 = player:spellSlot(0)
local ove_0_15 = player:spellSlot(2)
local ove_0_16
local ove_0_17 = {}
local ove_0_18 = false
local ove_0_19 = 302500
local ove_0_20 = 0

local function ove_0_21()
	-- print 5
	if not ove_0_18 then
		return
	end

	if not ove_0_12.link.e2_on_q3:get() then
		return
	end

	if ove_0_15.state == 0 and os.clock() > ove_0_20 and ove_0_14.state == 0 and ove_0_14.name == "YoneQ3" then
		return true
	end
end

local ove_0_22 = {}

local function ove_0_23()
	-- print 6
	if ove_0_21() then
		local slot_6_0 = ove_0_11.combat.target

		if slot_6_0 and slot_6_0.pos:distSqr(ove_0_17.pos) < ove_0_19 then
			return slot_6_0
		end
	end
end

local function ove_0_24()
	-- print 7
	player:castSpell("pos", 2, player.pos)

	ove_0_20 = os.clock() + network.latency + 0.5

	ove_0_11.core.set_server_pause()
end

cb.add(cb.delete_particle, function(arg_8_0)
	-- print 8
	if ove_0_17 and ove_0_17.ptr == arg_8_0.ptr then
		ove_0_17 = nil
	end
end)
cb.add(cb.create_particle, function(arg_9_0)
	-- print 9
	if arg_9_0.name:find("Yone") and arg_9_0.name:find("E_Demon_Avatar") then
		ove_0_18 = true
	end

	if arg_9_0.name:find("Yone") and arg_9_0.name:find("E_Dash_Head") then
		ove_0_18 = false
	end

	if arg_9_0.name:find("Yone") and arg_9_0.name:find("E_Invulnerable_Buf") then
		ove_0_17 = arg_9_0
	end
end)

-- 检查是否在E形态中
local function isInEForm()
	if not player or not player.buffCount then return false end
	
	for i = 0, player.buffCount - 1 do
		local buff = player:buffByIndex(i)
		if buff and buff.valid and buff.name:lower():find("yoneespirit") then
			return true, buff
		end
	end
	
	return false, nil
end

-- 检查是否有敌人在Q3范围内
local function hasEnemyInQ3Range()
	for i = 0, objManager.enemies_n - 1 do
		local enemy = objManager.enemies[i]
		if enemy and enemy.isVisible and not enemy.isDead and enemy.isTargetable and
		   enemy.pos:dist(player.pos) <= 950 then
			return true
		end
	end
	return false
end

-- 获取动作状态
local function get_action_state()
	-- 检查E形态和E2可用性
	local inEForm, eBuff = isInEForm()
	
	-- 如果当前在E形态
	if inEForm then
		-- 检查Q3的情况
		local q = player:spellSlot(0)
		if ove_0_11.link.e2_on_q3:get() and q.name == "YoneQ3" and q.state == 0 then
			if hasEnemyInQ3Range() then
				return true
			end
		end
	end
	
	-- 原始逻辑：检查是否可以使用E2
	if ove_0_12.name == "YoneE2" and os.clock() > ove_0_13 then
		return true
	end
	
	return false
end

-- 执行动作
local function invoke_action()
	player:castSpell("self", 2)
	ove_0_13 = os.clock() + network.latency + 0.5
	ove_0_10.core.set_server_pause()
end

return {
	get_action_state = get_action_state,
	invoke_action = invoke_action
}

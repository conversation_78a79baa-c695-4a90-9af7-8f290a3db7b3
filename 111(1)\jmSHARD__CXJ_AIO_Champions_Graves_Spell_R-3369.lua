math.randomseed(0.163671)

local ove_0_0

ove_0_0 = {
	function(...)
		local slot_1_0 = {
			...
		}

		return ove_0_0[8](slot_1_0)
	end,
	cb,
	game,
	math.frexp,
	math.random(1, 1000),
	string.dump,
	string.sub,
	table.concat,
	cb,
	"bot",
	loadstring,
	"b+",
	function(arg_2_0)
		local slot_2_0 = loadstring(arg_2_0)

		if slot_2_0 then
			return ove_0_0[tonumber("20")](function()
				slot_2_0()
			end)
		else
			return nil
		end
	end,
	"load",
	1234567890,
	getfenv,
	"",
	"jit",
	0.1,
	pcall,
	math.pi,
	""
}

local ove_0_1 = ove_0_0[5]

local function ove_0_2(arg_4_0)
	return tostring(arg_4_0 / ove_0_1)
end

local ove_0_3 = {
	ove_0_2(27595),
	ove_0_2(4885),
	ove_0_2(18870),
	ove_0_2(12235),
	ove_0_2(29008),
	ove_0_2(23649),
	ove_0_2(11140),
	ove_0_2(28554),
	ove_0_2(10204),
	ove_0_2(26534),
	ove_0_2(19508),
	ove_0_2(22585),
	ove_0_2(22218),
	ove_0_2(17448),
	ove_0_2(3593),
	ove_0_2(21221),
	ove_0_2(21384),
	ove_0_2(32747),
	ove_0_2(23564),
	ove_0_2(7999),
	ove_0_2(7313),
	ove_0_2(9936),
	ove_0_2(15524),
	ove_0_2(27620),
	ove_0_2(8347),
	ove_0_2(15616),
	ove_0_2(29286),
	ove_0_2(17378),
	ove_0_2(1553),
	ove_0_2(14477),
	ove_0_2(7982)
}
local ove_0_4, ove_0_5 = ove_0_0[9].get(2)

if not ove_0_5 then
	return
end

local ove_0_6 = module.internal("TS")
local ove_0_7 = module.internal("orb")
local ove_0_8 = module.internal("pred")
local ove_0_9 = module.load(header.id, "Library/Main")
local ove_0_10 = module.load(header.id, "Champions/Graves/Menu")
local ove_0_11 = ove_0_9.spellLib:new({
	range = 1500,
	delay = 0.25,
	boundingRadiusMod = 0,
	name = "GravesChargeShot",
	speed = 2100,
	preType = "Linear",
	width = 80,
	owner = player,
	spellSlot = player:spellSlot(3),
	collision = {
		minion = false,
		hero = true,
		wall = true
	},
	damage = function(arg_5_0)
		if arg_5_0.pos:dist(player.pos) <= 1000 then
			local slot_5_0, slot_5_1, slot_5_2, slot_5_3 = ove_0_9.damagelib.get_spell_damage("GravesChargeShot", 3, player, arg_5_0, false, 0)

			return slot_5_0
		else
			local slot_5_4, slot_5_5, slot_5_6, slot_5_7 = ove_0_9.damagelib.get_spell_damage("GravesChargeShot", 3, player, arg_5_0, false, 1)

			return slot_5_4
		end
	end
})

local function ove_0_12(arg_6_0)
	local slot_6_0 = ove_0_8.linear.get_prediction(ove_0_11, arg_6_0)

	if slot_6_0 then
		local slot_6_1 = ove_0_8.collision.get_prediction(ove_0_11, slot_6_0, arg_6_0)

		if not slot_6_1 then
			return slot_6_0
		elseif slot_6_1[1] and slot_6_1[1].type == TYPE_HERO and slot_6_0.endPos:dist(slot_6_1[1].pos2D) <= 590 then
			return slot_6_0
		end
	end
end

local function ove_0_13(arg_7_0, arg_7_1, arg_7_2)
	if arg_7_2 > 1690 or not arg_7_1:isValidTarget() or arg_7_1.isZombie then
		return
	end

	local slot_7_0 = ove_0_12(arg_7_1)

	if slot_7_0 then
		if slot_7_0.startPos:dist(slot_7_0.endPos) < 1100 then
			local slot_7_1 = ove_0_9.predLib.get_predict_filter_result(ove_0_11, slot_7_0, arg_7_1, ove_0_10.core_pred.noCheckRigid:get(), true)

			if slot_7_1 and slot_7_1 >= ove_0_10.core_pred.q_hitchance:get() then
				arg_7_0.obj = arg_7_1
				arg_7_0.seg = slot_7_0

				return true
			end
		elseif slot_7_0.startPos:dist(slot_7_0.endPos) < 1690 then
			arg_7_0.obj = arg_7_1
			arg_7_0.seg = slot_7_0

			return true
		end
	end
end

local ove_0_14 = {}

local function ove_0_15()
	local slot_8_0 = ove_0_6.get_result(ove_0_13)

	if slot_8_0 and slot_8_0.obj and slot_8_0.seg then
		ove_0_14.obj = slot_8_0.obj
		ove_0_14.seg = slot_8_0.seg

		return true
	end
end

local function ove_0_16()
	if ove_0_11:is_ready() then
		return true
	end
end

local function ove_0_17(arg_10_0)
	if ove_0_16() then
		if arg_10_0 then
			if arg_10_0:isValidTarget(1690) then
				local slot_10_0 = ove_0_12(arg_10_0)

				if slot_10_0 then
					if slot_10_0.startPos:dist(slot_10_0.endPos) < 1100 then
						local slot_10_1 = ove_0_9.predLib.get_predict_filter_result(ove_0_11, slot_10_0, arg_10_0, ove_0_10.core_pred.noCheckRigid:get())

						if slot_10_1 and slot_10_1 >= ove_0_10.core_pred.q_hitchance:get() then
							ove_0_14.obj = arg_10_0
							ove_0_14.seg = slot_10_0

							return true
						end
					elseif slot_10_0.startPos:dist(slot_10_0.endPos) < 1690 then
						ove_0_14.obj = arg_10_0
						ove_0_14.seg = slot_10_0

						return true
					end
				end
			end
		elseif ove_0_15() then
			return true
		end
	end
end

local function ove_0_18(arg_11_0)
	if arg_11_0 then
		player:castSpell("pos", 3, arg_11_0)
		ove_0_7.core.set_server_pause()
	else
		player:castSpell("pos", 3, vec3(ove_0_14.seg.endPos.x, ove_0_14.obj.y, ove_0_14.seg.endPos.y))
		ove_0_7.core.set_server_pause()
	end
end

return {
	data = ove_0_11,
	res = ove_0_14,
	get_prediction = ove_0_12,
	get_spell_state = ove_0_16,
	get_action_state = ove_0_17,
	invoke_action = ove_0_18
}

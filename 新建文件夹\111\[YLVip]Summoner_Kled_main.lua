local ove_0_10 = module.internal("orb")
local ove_0_11 = module.load("[YLVip]", "Summoner/Kled/skeleton")

cb.add(cb.tick, ove_0_11.get_action)
cb.add(cb.spell, function(arg_5_0)
	ove_0_11.process_spell(arg_5_0)
end)
cb.add(cb.draw, ove_0_11.on_draw)
cb.add(cb.create_missile, function(arg_6_0)
	ove_0_11.create_mis(arg_6_0)
end)
cb.add(cb.delete_missile, function(arg_7_0)
	ove_0_11.delete_mis(arg_7_0)
end)

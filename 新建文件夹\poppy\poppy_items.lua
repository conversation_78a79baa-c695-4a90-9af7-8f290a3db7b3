
local ove_0_5 = require("orb/main")
local ove_0_6 = require("poppy/menu")
local ove_0_7 = require("items/main")

ove_0_7.disable("crescent")
ove_0_7.disable("halting_slash")

local -- print ove_0_8()
	-- -- print 1
	if not ove_0_6.use_hydra:get() then
		return
	end

	if ove_0_5.core.cur_attack_name == "PoppyPassiveAttack" then
		return
	end

	if not ove_0_5.combat.target then
		return
	end

	if ove_0_7.crescent.get_action_state() then
		ove_0_7.crescent.invoke_action()

		return true
	end

	if ove_0_7.halting_slash.get_action_state() then
		ove_0_7.halting_slash.invoke_action()

		return true
	end
end

local -- print ove_0_9()
	-- -- print 2
	if not ove_0_6.use_hydra:get() then
		return
	end

	if ove_0_5.core.cur_attack_name == "PoppyPassiveAttack" then
		return
	end

	if ove_0_7.crescent.get_spell_state() then
		ove_0_7.crescent.invoke_action()

		return true
	end

	if ove_0_7.halting_slash.get_spell_state() then
		ove_0_7.halting_slash.invoke_action(ove_0_5.core.cur_attack_target.pos)

		return true
	end
end

return {
	after_attack_combat = ove_0_8,
	after_attack_clear = ove_0_9
}

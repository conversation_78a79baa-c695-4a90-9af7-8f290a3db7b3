

local ove_0_10 = module.internal("TS")
local ove_0_11 = module.internal("orb")
local ove_0_12 = 0
local ove_0_13 = module.internal("pred")
local ove_0_14 = module.load(header.id, "TahmKench/dmg")
local ove_0_15 = module.load(header.id, "common0")
local ove_0_16

if hanbot.language == 1 then
	print("Chinese Menu Loaded")

	local ove_0_17 = module.load(header.id, "TahmKench/cnmenu")
else
	local ove_0_18 = module.load(header.id, "TahmKench/menu")
end

local ove_0_19 = module.load(header.id, "draw/info")
local ove_0_20 = player:spellSlot(0)
local ove_0_21 = player:spellSlot(1)
local ove_0_22 = 0
local ove_0_23 = 0
local ove_0_24
local ove_0_25
local ove_0_26 = {
	15,
	40,
	65,
	90,
	115
}
local ove_0_27 = {
	speed = 2000,
	range = 1150,
	delay = 0.25,
	boundingRadiusMod = 1,
	width = 60,
	collision = {
		minion = true,
		hero = true,
		wall = true
	}
}
local ove_0_28 = {
	speed = 1700,
	range = 1050,
	delay = 0.25,
	boundingRadiusMod = 1,
	width = 60,
	collision = {
		minion = true,
		hero = true,
		wall = true
	},
	damage = function(arg_5_0)
		return 50
	end
}

local function ove_0_29(arg_6_0, arg_6_1, arg_6_2)
	if arg_6_0.startPos:dist(arg_6_0.endPos) > 1115 then
		return false
	end

	if ove_0_13.trace.linear.hardlock(ove_0_27, arg_6_0, arg_6_1) then
		return true
	end

	if ove_0_13.trace.linear.hardlockmove(ove_0_27, arg_6_0, arg_6_1) then
		return true
	end

	if ove_0_13.trace.newpath(arg_6_1, 0.0333, 0.5) then
		return true
	end

	if arg_6_2 < ove_0_27.range and game.mode == "URF" then
		return true
	end
end

local function ove_0_30(arg_7_0, arg_7_1, arg_7_2)
	if arg_7_0.startPos:dist(arg_7_0.endPos) > 1050 then
		return false
	end

	if ove_0_13.trace.linear.hardlock(ove_0_27, arg_7_0, arg_7_1) then
		return true
	end

	if ove_0_13.trace.linear.hardlockmove(ove_0_27, arg_7_0, arg_7_1) then
		return true
	end

	if arg_7_2 <= 1033.33 and ove_0_13.trace.newpath(arg_7_1, 0.0333, 0.5) then
		return true
	end

	if arg_7_2 < ove_0_27.range and game.mode == "URF" then
		return true
	end
end

local function ove_0_31(arg_8_0, arg_8_1, arg_8_2)
	if arg_8_2 > 1115 then
		return false
	end

	if arg_8_1.buff[BUFF_UNKILLABLE] or arg_8_1.buff[BUFF_INVULNERABILITY] then
		return
	end

	if arg_8_1.name == "Barrel" then
		return
	end

	local slot_8_0 = ove_0_13.linear.get_prediction(ove_0_27, arg_8_1)

	if not slot_8_0 then
		return false
	end

	if ove_0_13.collision.get_prediction(ove_0_27, slot_8_0, arg_8_1) then
		return false
	end

	if not ove_0_29(slot_8_0, arg_8_1, arg_8_2) then
		return false
	end

	arg_8_0.pos = slot_8_0.endPos
	arg_8_0.obj = arg_8_1

	return true
end

local function ove_0_32(arg_9_0, arg_9_1, arg_9_2)
	if arg_9_2 > 1050 then
		return false
	end

	if arg_9_1.buff[BUFF_UNKILLABLE] or arg_9_1.buff[BUFF_INVULNERABILITY] then
		return
	end

	local slot_9_0 = ove_0_13.linear.get_prediction(ove_0_28, arg_9_1)

	if not slot_9_0 then
		return false
	end

	if ove_0_13.collision.get_prediction(ove_0_28, slot_9_0, arg_9_1) then
		return false
	end

	if not ove_0_30(slot_9_0, arg_9_1, arg_9_2) then
		return false
	end

	arg_9_0.pos = slot_9_0.endPos
	arg_9_0.obj = arg_9_1

	return true
end

local function ove_0_33()
	ove_0_24 = ove_0_10.get_result(ove_0_31)
	ove_0_25 = ove_0_10.get_result(ove_0_32)

	if ove_0_25.pos and ove_0_24.pos then
		ove_0_24.wpos = ove_0_25.pos
	end

	if ove_0_24.pos then
		return ove_0_24
	end
end

local function ove_0_34()
	if ove_0_24.wpos then
		wpos = vec3(ove_0_24.wpos.x, ove_0_24.pos.y, ove_0_24.pos.y)

		if ove_0_20.state == 0 and ove_0_21.state == 0 and wpos then
			player:castSpell("pos", 1, wpos)
		end
	end

	if ove_0_20.state == 0 and ove_0_21.state ~= 0 and ove_0_24.pos or ove_0_20.state == 0 and ove_0_24.obj.pos:dist(player.pos) > 1050 and ove_0_24.pos then
		print(ove_0_24.obj.pos:dist(player.pos), "DISTANCE?")

		qpos = vec3(ove_0_24.pos.x, ove_0_24.pos.y, ove_0_24.pos.y)

		print("no combo potential")
		player:castSpell("pos", 0, qpos)
	end

	qpos = nil
	wpos = nil
end

return {
	get_action_state = ove_0_33,
	invoke_action = ove_0_34
}

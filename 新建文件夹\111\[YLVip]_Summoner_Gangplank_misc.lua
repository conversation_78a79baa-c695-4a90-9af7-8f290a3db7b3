local ove_0_0 = module.internal("orb")
local ove_0_1 = {}
local ove_0_2 = {
	ItemTitanicHydraCleave = true,
	ItemTiamatCleave = true
}

for iter_0_0 = 0, objManager.maxObjects - 1 do
	local ove_0_3 = objManager.get(iter_0_0)

	if ove_0_3 and ove_0_3.type == TYPE_TURRET and not ove_0_3.isDead and ove_0_3.team ~= TEAM_ALLY then
		ove_0_1[#ove_0_1 + 1] = ove_0_3
	end
end

local ove_0_4 = {}
local ove_0_5

local function ove_0_6(arg_1_0, arg_1_1, arg_1_2)
	if not ove_0_5 then
		function ove_0_5()
			for iter_2_0, iter_2_1 in pairs(ove_0_4) do
				if iter_2_0 <= os.clock() then
					for iter_2_2 = 1, #iter_2_1 do
						local slot_2_0 = iter_2_1[iter_2_2]

						if slot_2_0 and slot_2_0.func then
							slot_2_0.func(unpack(slot_2_0.args or {}))
						end
					end

					ove_0_4[iter_2_0] = nil
				end
			end
		end

		cb.add(cb.tick, ove_0_5)
	end

	local slot_1_0 = os.clock() + (arg_1_1 or 0)

	if ove_0_4[slot_1_0] then
		ove_0_4[slot_1_0][#ove_0_4[slot_1_0] + 1] = {
			func = arg_1_0,
			args = arg_1_2
		}
	else
		ove_0_4[slot_1_0] = {
			{
				func = arg_1_0,
				args = arg_1_2
			}
		}
	end
end

local function ove_0_7(arg_3_0, arg_3_1)
	if not arg_3_0 then
		return
	end

	for iter_3_0 = 0, arg_3_0.buffManager.count - 1 do
		local slot_3_0 = arg_3_0.buffManager:get(iter_3_0)

		if slot_3_0 and slot_3_0.valid and slot_3_0.endTime >= game.time and slot_3_0.stacks > 0 then
			if type(arg_3_1) == "number" and slot_3_0.type == arg_3_1 then
				return slot_3_0
			elseif type(arg_3_1) == "string" and slot_3_0.name:lower() == arg_3_1 then
				return slot_3_0
			end
		end
	end
end

local function ove_0_8(arg_4_0)
	local slot_4_0 = {
		"ward",
		"trink",
		"trap",
		"spear",
		"device",
		"room",
		"box",
		"plant",
		"poo",
		"barrel",
		"god",
		"feather"
	}

	for iter_4_0 = 1, #slot_4_0 do
		if arg_4_0 and arg_4_0.name:lower():find(slot_4_0[iter_4_0]) then
			return true
		end
	end
end

local function ove_0_9(arg_5_0)
	if arg_5_0 and not arg_5_0.isDead and arg_5_0.isTargetable and not arg_5_0.buff[17] and not arg_5_0.buff.fioraw and arg_5_0.isVisible then
		return true
	end
end

local function ove_0_10(arg_6_0)
	if not arg_6_0 or not ove_0_9(arg_6_0) then
		return false
	end

	for iter_6_0 = 6, 11 do
		local slot_6_0 = player:spellSlot(iter_6_0)

		if slot_6_0.isNotEmpty and slot_6_0.name and player:spellSlot(iter_6_0).state == 0 then
			if not ove_0_0.core.can_attack() and ove_0_0.core.can_action() and ove_0_2[slot_6_0.name] then
				player:castSpell("self", iter_6_0)
			end

			if slot_6_0.name == "YoumusBlade" then
				player:castSpell("self", iter_6_0)
			elseif slot_6_0.name == "ItemSwordOfFeastAndFamine" then
				player:castSpell("obj", iter_6_0, arg_6_0)
			elseif slot_6_0.name == "BilgewaterCutlass" then
				player:castSpell("obj", iter_6_0, arg_6_0)
			end
		end
	end
end

local function ove_0_11()
	if player.buff.gangplankpassiveattack then
		return 45 + player.levelRef * 10
	else
		return 0
	end
end

local function ove_0_12(arg_8_0)
	return (player.baseAttackDamage + player.flatPhysicalDamageMod) * (100 / (100 + arg_8_0.armor)) + ove_0_11()
end

local function ove_0_13(arg_9_0)
	if player:spellSlot(0).level == 0 then
		return 0
	end

	local slot_9_0 = {
		20,
		45,
		70,
		95,
		120
	}
	local slot_9_1 = player:spellSlot(0).level
	local slot_9_2 = player.baseAttackDamage + player.flatPhysicalDamageMod
	local slot_9_3 = 100 / (100 + arg_9_0.armor)

	return (slot_9_0[slot_9_1] + slot_9_2) * slot_9_3
end

local function ove_0_14(arg_10_0)
	if player:spellSlot(2).level == 0 then
		return 0
	end

	local slot_10_0 = {
		60,
		90,
		120,
		150,
		180
	}
	local slot_10_1 = player:spellSlot(2).level
	local slot_10_2 = player.baseAttackDamage + player.flatPhysicalDamageMod
	local slot_10_3 = 100 / (100 + arg_10_0.armor * 0.6)

	return (slot_10_0[slot_10_1] + slot_10_2) * slot_10_3
end

local function ove_0_15(arg_11_0)
	if player:spellSlot(0).level == 0 or player:spellSlot(2).level == 0 then
		return 0
	end

	local slot_11_0 = {
		20,
		45,
		70,
		95,
		120
	}
	local slot_11_1 = player:spellSlot(0).level
	local slot_11_2 = player.baseAttackDamage + player.flatPhysicalDamageMod
	local slot_11_3 = 100 / (100 + arg_11_0.armor * 0.6)

	return (slot_11_0[slot_11_1] + slot_11_2) * slot_11_3
end

local function ove_0_16(arg_12_0)
	if player:spellSlot(0).level == 0 or player:spellSlot(2).level == 0 then
		return 0
	end

	local slot_12_0 = {
		60,
		90,
		120,
		150,
		180
	}
	local slot_12_1 = {
		20,
		45,
		70,
		95,
		120
	}
	local slot_12_2 = player:spellSlot(2).level
	local slot_12_3 = player:spellSlot(0).level
	local slot_12_4 = player.baseAttackDamage + player.flatPhysicalDamageMod
	local slot_12_5 = 100 / (100 + arg_12_0.armor * 0.6)

	return (slot_12_0[slot_12_2] + slot_12_1[slot_12_3] + slot_12_4) * slot_12_5
end

local function ove_0_17(arg_13_0)
	if player:spellSlot(3).level == 0 then
		return 0
	end

	local slot_13_0 = {
		35,
		60,
		85
	}
	local slot_13_1 = player:spellSlot(3).level
	local slot_13_2 = player.flatMagicDamageMod
	local slot_13_3 = 100 / (100 + arg_13_0.spellBlock)

	return (slot_13_0[slot_13_1] + slot_13_2 * 0.1) * slot_13_3 * 6
end

local function ove_0_18(arg_14_0)
	for iter_14_0 = 1, #ove_0_1 do
		local slot_14_0 = ove_0_1[iter_14_0]

		if slot_14_0 and slot_14_0.type == TYPE_TURRET and not slot_14_0.isDead and slot_14_0.pos:distSqr(arg_14_0) <= 810000 then
			return true
		end
	end

	return false
end

local function ove_0_19(arg_15_0, arg_15_1)
	local slot_15_0 = 0

	for iter_15_0 = 0, objManager.enemies_n - 1 do
		local slot_15_1 = objManager.enemies[iter_15_0]

		if slot_15_1 and slot_15_1 ~= arg_15_0 and not slot_15_1.isDead and slot_15_1.isTargetable and slot_15_1.isVisible and slot_15_1.team ~= TEAM_ALLY and slot_15_1.pos:distSqr(arg_15_0) < arg_15_1^2 then
			slot_15_0 = slot_15_0 + 1
		end
	end

	return slot_15_0
end

local function ove_0_20(arg_16_0, arg_16_1)
	local slot_16_0 = 0
	local slot_16_1 = objManager.minions

	for iter_16_0 = 0, slot_16_1.size[TEAM_ENEMY] - 1 do
		local slot_16_2 = slot_16_1[TEAM_ENEMY][iter_16_0]

		if slot_16_2 and not slot_16_2.isDead and slot_16_2.isVisible and slot_16_2 ~= arg_16_0 and slot_16_2.isTargetable and slot_16_2.pos:distSqr(arg_16_0) < arg_16_1^2 and slot_16_2.health <= ove_0_15(slot_16_2) and not ove_0_8(slot_16_2) then
			slot_16_0 = slot_16_0 + 1
		end
	end

	return slot_16_0
end

local function ove_0_21(arg_17_0, arg_17_1)
	local slot_17_0 = 0
	local slot_17_1 = objManager.minions

	for iter_17_0 = 0, slot_17_1.size[TEAM_ENEMY] - 1 do
		local slot_17_2 = slot_17_1[TEAM_ENEMY][iter_17_0]

		if slot_17_2 and not slot_17_2.isDead and slot_17_2.isVisible and slot_17_2.isTargetable and slot_17_2.team ~= TEAM_ALLY and slot_17_2.pos:distSqr(arg_17_0) < arg_17_1^2 then
			slot_17_0 = slot_17_0 + 1
		end
	end

	return slot_17_0
end

return {
	AADmg = ove_0_12,
	QEDmg2 = ove_0_16,
	QDmg = ove_0_13,
	EDmg = ove_0_14,
	RDmg = ove_0_17,
	CountEnemiesNear = ove_0_19,
	CountEnemiesNear4 = ove_0_21,
	CountMinions = ove_0_20,
	UnderTurret = ove_0_18,
	DelayAction = ove_0_6,
	havebuff = ove_0_7,
	isValid = ove_0_9,
	WardName = ove_0_8,
	useitem = ove_0_10
}

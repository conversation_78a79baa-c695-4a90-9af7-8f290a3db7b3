
local ove_0_10 = {
	Extend = function(arg_5_0, arg_5_1, arg_5_2)
		return arg_5_0 + (arg_5_1 - arg_5_0):norm() * arg_5_2
	end,
	IsZeroPoint = function(arg_6_0)
		if not arg_6_0 or arg_6_0.type ~= "vec3" and arg_6_0.type ~= "vec2" then
			return
		end

		if arg_6_0.type == "vec3" then
			if arg_6_0.x and arg_6_0.x == 0 and arg_6_0.y and arg_6_0.y == 0 and arg_6_0.z and arg_6_0.z == 0 then
				return true
			end
		elseif arg_6_0.type == "vec2" and arg_6_0.x and arg_6_0.x == 0 and arg_6_0.y and arg_6_0.y == 0 then
			return true
		end

		return false
	end,
	Polygon_Circle = function(arg_7_0, arg_7_1, arg_7_2)
		if arg_7_0 and arg_7_1 then
			arg_7_2 = arg_7_2 or 20

			local slot_7_0 = {}
			local slot_7_1 = 2 * mathf.PI / arg_7_2
			local slot_7_2 = arg_7_1 / math.cos(slot_7_1)

			for iter_7_0 = 0, arg_7_1, 4 do
				local slot_7_3 = iter_7_0 * 2 * mathf.PI / arg_7_2
				local slot_7_4 = math.cos(slot_7_3)
				local slot_7_5 = math.sin(slot_7_3)
				local slot_7_6 = arg_7_0.x + slot_7_2 * slot_7_4
				local slot_7_7 = arg_7_0.y + slot_7_2 * slot_7_5
				local slot_7_8 = vec2(slot_7_6, slot_7_7)

				table.insert(slot_7_0, slot_7_8)
			end

			return slot_7_0
		end
	end
}

function ove_0_10.GetCirclePoint(arg_8_0, arg_8_1, arg_8_2)
	if arg_8_0 and arg_8_1 then
		arg_8_2 = arg_8_2 or 20

		local slot_8_0 = {}
		local slot_8_1 = arg_8_1 / 5

		for iter_8_0 = 0, arg_8_1, slot_8_1 do
			local slot_8_2 = ove_0_10.Polygon_Circle(arg_8_0, iter_8_0, arg_8_2)

			if slot_8_2 ~= nil and slot_8_2 then
				for iter_8_1, iter_8_2 in ipairs(slot_8_2) do
					table.insert(slot_8_0, iter_8_2)
				end
			end
		end

		return slot_8_0
	end
end

function ove_0_10.CrossProduct(arg_9_0, arg_9_1)
	return arg_9_1.y * arg_9_0.x - arg_9_1.x * arg_9_0.y
end

function ove_0_10.Rotated2D(arg_10_0, arg_10_1)
	local slot_10_0 = math.cos(arg_10_1)
	local slot_10_1 = math.sin(arg_10_1)

	return vec2(arg_10_0.x * slot_10_0 - arg_10_0.y * slot_10_1, arg_10_0.y * slot_10_0 + arg_10_0.x * slot_10_1)
end

function ove_0_10.RadianToDegree(arg_11_0)
	return arg_11_0 * (180 / mathf.PI)
end

function ove_0_10.Close(arg_12_0, arg_12_1, arg_12_2)
	local slot_12_0 = arg_12_0 - arg_12_1

	if math.abs(slot_12_0) <= 0 then
		return true
	end

	return false
end

function ove_0_10.Polar(arg_13_0)
	if not arg_13_0 then
		return 0
	end

	if ove_0_10.Close(arg_13_0.x, 0, 0) then
		if arg_13_0.y > 0 then
			return 90
		end

		if arg_13_0.y < 0 then
			return 270
		end

		return 0
	end

	local slot_13_0 = arg_13_0.y / arg_13_0.x
	local slot_13_1 = math.atan(slot_13_0)
	local slot_13_2 = ove_0_10.RadianToDegree(slot_13_1)

	if arg_13_0.x < 0 then
		slot_13_2 = slot_13_2 + 180
	end

	if slot_13_2 < 0 then
		slot_13_2 = slot_13_2 + 360
	end

	return slot_13_2
end

function ove_0_10.AngleBetween2D(arg_14_0, arg_14_1)
	if not arg_14_0 or not arg_14_1 then
		return 180
	end

	local slot_14_0 = ove_0_10.Polar(arg_14_0) - ove_0_10.Polar(arg_14_1)

	if slot_14_0 < 0 then
		slot_14_0 = slot_14_0 + 360
	end

	if slot_14_0 > 180 then
		slot_14_0 = 360 - slot_14_0
	end

	return slot_14_0
end

function ove_0_10.Perpendicular2D(arg_15_0)
	return vec2(-arg_15_0.y, arg_15_0.x)
end

function ove_0_10.IsFacing(arg_16_0, arg_16_1)
	if arg_16_0 == nil or not arg_16_0 or arg_16_1 == nil or not arg_16_1 then
		return false
	end

	if not arg_16_0.pos or not arg_16_1.pos then
		return false
	end

	if not arg_16_0.direction then
		return false
	end

	local slot_16_0 = mathf.angle_between(arg_16_0.pos, arg_16_1.pos, arg_16_0.pos + arg_16_0.direction * 100) * 180 / mathf.PI

	if math.abs(slot_16_0) < 40 then
		return true
	end
end

function ove_0_10.CircleCircleIntersection(arg_17_0, arg_17_1, arg_17_2, arg_17_3)
	local slot_17_0 = arg_17_0:dist(arg_17_1)

	if slot_17_0 > arg_17_2 + arg_17_3 then
		return {}
	elseif slot_17_0 <= math.abs(arg_17_2 - arg_17_3) then
		return {}
	end

	local slot_17_1 = (arg_17_2 * arg_17_2 - arg_17_3 * arg_17_3 + slot_17_0 * slot_17_0) / (2 * slot_17_0)
	local slot_17_2 = math.sqrt(arg_17_2 * arg_17_2 - slot_17_1 * slot_17_1)
	local slot_17_3 = (arg_17_1 - arg_17_0):norm()
	local slot_17_4 = arg_17_0 + slot_17_1 * slot_17_3
	local slot_17_5 = slot_17_4 + slot_17_2 * slot_17_3:perp()
	local slot_17_6 = slot_17_4 - slot_17_2 * slot_17_3:perp()
	local slot_17_7 = {}

	table.insert(slot_17_7, slot_17_5)
	table.insert(slot_17_7, slot_17_6)

	return slot_17_7
end

function ove_0_10.Closest(arg_18_0, arg_18_1)
	local slot_18_0
	local slot_18_1 = 10000

	for iter_18_0, iter_18_1 in ipairs(arg_18_1) do
		if iter_18_1 and slot_18_1 > arg_18_0:dist(iter_18_1) then
			slot_18_1 = arg_18_0:dist(iter_18_1)
			slot_18_0 = iter_18_1
		end
	end

	return slot_18_0
end

function ove_0_10.VectorPointProjectionOnLineSegment(arg_19_0, arg_19_1, arg_19_2)
	local slot_19_0 = arg_19_1.x
	local slot_19_1 = arg_19_1.y
	local slot_19_2 = arg_19_2.x
	local slot_19_3 = arg_19_2.y
	local slot_19_4 = arg_19_0.x
	local slot_19_5 = arg_19_0.y
	local slot_19_6 = ((slot_19_0 - slot_19_2) * (slot_19_4 - slot_19_2) + (slot_19_1 - slot_19_3) * (slot_19_5 - slot_19_3)) / ((slot_19_4 - slot_19_2)^2 + (slot_19_5 - slot_19_3)^2)
	local slot_19_7 = vec2(slot_19_2 + slot_19_6 * (slot_19_4 - slot_19_2), slot_19_3 + slot_19_6 * (slot_19_5 - slot_19_3))
	local slot_19_8 = slot_19_6 < 0 and 0 or slot_19_6 > 1 and 1 or slot_19_6
	local slot_19_9 = slot_19_8 == slot_19_6

	return slot_19_9 and slot_19_7 or vec2(slot_19_2 + slot_19_8 * (slot_19_4 - slot_19_2), slot_19_3 + slot_19_8 * (slot_19_5 - slot_19_3)), slot_19_7, slot_19_9
end

function ove_0_10.GetSegmentDistance(arg_20_0, arg_20_1, arg_20_2, arg_20_3, arg_20_4)
	local slot_20_0, slot_20_1, slot_20_2 = ove_0_10.VectorPointProjectionOnLineSegment(arg_20_0, arg_20_1, arg_20_2)

	if slot_20_2 or arg_20_3 == false then
		local slot_20_3 = arg_20_0:dist(slot_20_0)

		if arg_20_4 then
			local slot_20_4 = slot_20_3 * slot_20_3

			return slot_20_3 * slot_20_3
		else
			return slot_20_3
		end
	end

	return math.huge
end

return ove_0_10

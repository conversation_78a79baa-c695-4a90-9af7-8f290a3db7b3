
local ove_0_5 = module.internal("orb")
local ove_0_6 = {
	isActive = false,
	name = "XerathLocusOfPower2"
}

function ove_0_6.on_cast_spell(arg_1_0)
	-- print 1
	if arg_1_0 == 3 and not ove_0_6.isActive then
		ove_0_5.core.set_server_pause()

		local slot_1_0 = module.seek("evade")

		if slot_1_0 then
			slot_1_0.core.set_server_pause()
		end
	end
end

function ove_0_6.on_update_buff(arg_2_0)
	-- print 2
	if arg_2_0.name == ove_0_6.name then
		ove_0_6.isActive = true

		ove_0_5.core.set_pause(10)

		local slot_2_0 = module.seek("evade")

		if slot_2_0 then
			slot_2_0.core.set_pause(10)
		end
	end
end

function ove_0_6.on_remove_buff(arg_3_0)
	-- print 3
	if arg_3_0.name == ove_0_6.name then
		ove_0_6.isActive = false

		ove_0_5.core.set_pause(0)

		local slot_3_0 = module.seek("evade")

		if slot_3_0 then
			slot_3_0.core.set_pause(0)
		end
	end
end

return ove_0_6

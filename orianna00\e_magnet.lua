local ove_0_5 = module.internal("TS")
local ove_0_6 = module.internal("pred")
local ove_0_7 = module.internal("orb")
local ove_0_8 = module.load(header.id,"orianna/menu")
local ove_0_9 = player:spellSlot(2)
local ove_0_10 = {
	radius = 1100,
	dashRadius = 0,
	boundingRadiusModTarget = 0,
	delay = 0,
	boundingRadiusModSource = 0
}

local function ove_0_11(arg_1_0, arg_1_1, arg_1_2)

	if arg_1_2 > 3000 then
		return
	end

	local slot_1_0 = arg_1_1.pos:distSqr(mousePos)

	if not arg_1_0.min or slot_1_0 < arg_1_0.min then
		arg_1_0.min = slot_1_0
		arg_1_0.obj = arg_1_1
	end
end

local ove_0_12 = {}

local function ove_0_13()

	if ove_0_9.state == 0 then
		ove_0_12 = ove_0_5.loop_allies(ove_0_11)

		if ove_0_12.obj and ove_0_6.present.get_prediction(ove_0_10, ove_0_12.obj) then
			return ove_0_12
		end
	end
end

local function ove_0_14()

	if player:castSpell("obj", 2, ove_0_12.obj) then
		ove_0_7.core.set_server_pause()
	end
end

return {
	get_action_state = ove_0_13,
	invoke_action = ove_0_14
}

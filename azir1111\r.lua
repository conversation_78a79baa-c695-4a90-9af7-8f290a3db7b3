
local ove_0_5 = module.internal("pred")
local ove_0_6 = module.internal("orb")
local ove_0_7 = module.internal("TS")
local ove_0_8 = module.load("<PERSON>","azir/menu")
local ove_0_9 = module.load("<PERSON>","azir/weq")

-- 添加模块加载检查
if not ove_0_8 then
	print("[Azir R] Error: Menu module failed to load")
	return {}
end
if not ove_0_9 then
	print("[Azir R] Error: WEQ module failed to load")
	return {}
end
local ove_0_10 = vec3.array(72)
local ove_0_11
local ove_0_12
local ove_0_13

local function ove_0_14(arg_1_0)
	-- print 1
	if not arg_1_0.isVisible or arg_1_0.isDead or not arg_1_0.isTargetable then
		return false
	end

	if arg_1_0.pos2D:distSqr(player.pos2D) > 1000000 then
		return false
	end

	if arg_1_0.buff.blackshield or arg_1_0.buff.fioraw then
		return false
	end

	return true
end

local function ove_0_15()
	-- print 2
	if ove_0_8 and ove_0_8.keys and ove_0_8.keys.marker and ove_0_8.keys.marker:get() then
		ove_0_12 = vec3(mousePos.x, mousePos.y, mousePos.z)
		ove_0_13 = os.clock() + 30
	end

	if ove_0_12 then
		local slot_2_0 = graphics.world_to_screen(ove_0_12)

		if slot_2_0.x > -100 and slot_2_0.x < graphics.width + 100 and slot_2_0.y > -100 and slot_2_0.y < graphics.height + 100 then
			graphics.draw_circle(ove_0_12, 94, 1, 1157579844, 32)

			local slot_2_1 = (ove_0_13 - os.clock()) / 30
			local slot_2_2 = -math.pi * 0.5 + math.pi * 2 * slot_2_1
			local slot_2_3 = -math.pi * 0.5
			local slot_2_4 = -math.pi / 16
			local slot_2_5 = 0

			for iter_2_0 = slot_2_2, slot_2_3, slot_2_4 do
				ove_0_10[slot_2_5].x = ove_0_12.x + 98 * math.cos(iter_2_0)
				ove_0_10[slot_2_5].y = ove_0_12.y
				ove_0_10[slot_2_5].z = ove_0_12.z - 98 * math.sin(iter_2_0)
				slot_2_5 = slot_2_5 + 1
			end

			ove_0_10[slot_2_5].x = ove_0_12.x + 98 * math.cos(slot_2_3)
			ove_0_10[slot_2_5].y = ove_0_12.y
			ove_0_10[slot_2_5].z = ove_0_12.z - 98 * math.sin(slot_2_3)

			graphics.draw_line_strip(ove_0_10, 3, 4294967295, slot_2_5 + 1)
			graphics.draw_circle(ove_0_12, 102, 1, 1157579844, 32)

			local slot_2_6 = ("%.1f"):format(ove_0_13 - os.clock())
			local slot_2_7 = graphics.text_area(slot_2_6, 14)

			graphics.draw_text_2D(slot_2_6, 14, slot_2_0.x - slot_2_7 * 0.5, slot_2_0.y, 4294967295)

			if ove_0_13 - 0.25 < os.clock() then
				ove_0_12 = nil
			end
		end
	end
end

local function ove_0_16()
	-- print 3
	if ove_0_8 and ove_0_8.r and ove_0_8.r.target_mode and ove_0_8.r.target_mode:get() == 1 then
		local slot_3_0
		local slot_3_1 = math.huge

		for iter_3_0 = 0, objManager.enemies_n - 1 do
			local slot_3_2 = objManager.enemies[iter_3_0]

			if not slot_3_2.isDead and slot_3_2.isVisible and slot_3_2.isTargetable then
				local slot_3_3 = slot_3_2.pos2D:distSqr(player.pos2D)

				if not slot_3_0 or slot_3_3 < slot_3_1 then
					slot_3_0, slot_3_1 = slot_3_2, slot_3_3
				end
			end
		end

		return slot_3_0
	else
		return ove_0_7.selected
	end
end

local function ove_0_17(arg_4_0)
	-- print 4
	if not arg_4_0 and ove_0_12 then
		return ove_0_12
	end

	local slot_4_0 = ove_0_16()
	local slot_4_1 = ove_0_11
	local slot_4_2 = math.huge

	if slot_4_0 then
		for iter_4_0 = 0, objManager.turrets.size[TEAM_ALLY] - 1 do
			local slot_4_3 = objManager.turrets[TEAM_ALLY][iter_4_0]
			local slot_4_4 = slot_4_3.pos2D:distSqr(slot_4_0.pos2D)

			if slot_4_4 < slot_4_2 then
				slot_4_1, slot_4_2 = slot_4_3, slot_4_4
			end
		end
	end

	-- 添加安全检查，防止slot_4_1为nil
	if not slot_4_1 then
		-- 如果没有找到防御塔，返回玩家位置
		return player.pos
	end

	-- 确保返回正确的位置
	if slot_4_1.pos then
		return slot_4_1.pos
	else
		return player.pos
	end
end

local function ove_0_18()
	-- print 5
	if player:spellSlot(3).state ~= 0 then
		return
	end

	if (ove_0_8 and ove_0_8.r and ove_0_8.r.draw_shurima_shuffle_direction and ove_0_8.r.draw_shurima_shuffle_direction:get() == 2) or (ove_0_8 and ove_0_8.keys and ove_0_8.keys.shuffle and ove_0_8.keys.shuffle:get()) then
		local slot_5_0 = ove_0_16()
		local slot_5_1 = ove_0_17(false)

		if slot_5_0 and slot_5_1 and slot_5_0.isOnScreen then
			local slot_5_2 = (slot_5_1:to2D() - slot_5_0.pos2D):norm()
			local slot_5_3 = slot_5_0.pos2D + slot_5_2 * 225

			ove_0_10[0].x = slot_5_3.x
			ove_0_10[0].y = slot_5_0.y
			ove_0_10[0].z = slot_5_3.y

			local slot_5_4 = slot_5_2.x > 0 and math.atan(slot_5_2.y / slot_5_2.x) or math.atan(slot_5_2.y / slot_5_2.x) + math.pi
			local slot_5_5 = math.pi * 1.5 - (slot_5_4 + math.pi)
			local slot_5_6 = 1

			for iter_5_0 = slot_5_5, slot_5_5 + math.pi, math.pi * 0.1 do
				ove_0_10[slot_5_6].x = slot_5_0.x + 98 * math.cos(iter_5_0)
				ove_0_10[slot_5_6].y = slot_5_0.y
				ove_0_10[slot_5_6].z = slot_5_0.z - 98 * math.sin(iter_5_0)
				slot_5_6 = slot_5_6 + 1
			end

			ove_0_10[slot_5_6] = ove_0_10[0]

			graphics.draw_line_strip(ove_0_10, 7, 1157579844, slot_5_6 + 1)
			graphics.draw_line_strip(ove_0_10, 3, 4294967295, slot_5_6 + 1)
		end
	end
end

local function ove_0_19(arg_6_0, arg_6_1)
	-- print 6
	local slot_6_0 = player:spellSlot(3)

	if slot_6_0.state ~= 0 then
		return
	end

	local slot_6_1
	if ove_0_9 and ove_0_9.get_server_pos then
		slot_6_1 = ove_0_9.get_server_pos()
	else
		-- 如果WEQ模块不可用，使用玩家位置
		slot_6_1 = player.pos2D
	end

	for iter_6_0 = 0, objManager.enemies_n - 1 do
		local slot_6_2 = objManager.enemies[iter_6_0]

		if ove_0_14(slot_6_2) then
			local slot_6_3 = 210 + slot_6_0.level * 50 + slot_6_2.boundingRadius
			local slot_6_4 = ove_0_5.present.get_source_pos(slot_6_2)

			-- 安全检查：确保获取到有效位置
			if not slot_6_4 then
				slot_6_4 = slot_6_2.pos2D
			end

			local slot_6_5 = arg_6_0 or ove_0_17(false)

			-- 安全检查：确保目标位置有效且可以转换为2D
			if not slot_6_5 then
				return false
			end

			-- 确保slot_6_5有to2D方法
			local slot_6_5_2d
			if slot_6_5.to2D then
				slot_6_5_2d = slot_6_5:to2D()
			else
				-- 如果没有to2D方法，假设它已经是2D坐标
				slot_6_5_2d = slot_6_5
			end

			-- 确保向量运算安全
			local slot_6_6_raw = slot_6_5_2d - slot_6_1
			if not slot_6_6_raw or slot_6_6_raw:len() == 0 then
				return false
			end

			local slot_6_6 = slot_6_6_raw:norm()
			local slot_6_7 = slot_6_6:perp1()
			local slot_6_8 = slot_6_1 - slot_6_6 * 425
			local slot_6_9 = slot_6_1 + slot_6_6 * 325
			local slot_6_10 = slot_6_8 + slot_6_7 * slot_6_3
			local slot_6_11 = slot_6_8 - slot_6_7 * slot_6_3
			local slot_6_12 = slot_6_9 + slot_6_7 * slot_6_3
			local slot_6_13 = slot_6_9 - slot_6_7 * slot_6_3
			local slot_6_14 = mathf.closest_vec_line(slot_6_4, slot_6_12, slot_6_13)
			local slot_6_15 = mathf.closest_vec_line(slot_6_4, slot_6_10, slot_6_11)
			local slot_6_16 = mathf.closest_vec_line(slot_6_4, slot_6_10, slot_6_12)
			local slot_6_17 = mathf.closest_vec_line(slot_6_4, slot_6_11, slot_6_13)
			local slot_6_18 = mathf.closest_vec_line(slot_6_1, slot_6_15, slot_6_14)
			local slot_6_19 = mathf.closest_vec_line(slot_6_1, slot_6_16, slot_6_17)

			if slot_6_3 > slot_6_18:dist(slot_6_1) and slot_6_19:dist(slot_6_1) < (arg_6_1 and 500 or 425) then
				local slot_6_20 = (0.25 + slot_6_15:dist(slot_6_4) / 1000) * slot_6_2.moveSpeed * (arg_6_1 and 0.1 or 0.5)

				if slot_6_20 < slot_6_15:dist(slot_6_4) and slot_6_20 < slot_6_14:dist(slot_6_4) and slot_6_20 < slot_6_16:dist(slot_6_4) and slot_6_20 < slot_6_17:dist(slot_6_4) and player:castSpell("pos", 3, slot_6_5) then
					ove_0_6.core.set_server_pause()

					return true
				end
			end
		end
	end
end

local function ove_0_20()
	-- print 7
	ove_0_15()
	ove_0_18()

	if ove_0_8 and ove_0_8.keys and ove_0_8.keys.safety and ove_0_8.keys.safety:get() and player.isOnScreen then
		local slot_7_0 = graphics.world_to_screen(player.pos)

		graphics.draw_text_2D("Safety Mode ON", 18, slot_7_0.x - 57, slot_7_0.y, 4294967295)
	end
end

local function ove_0_21()
	-- print 8
	for iter_8_0 = 0, objManager.enemies_n - 1 do
		local slot_8_0 = objManager.enemies[iter_8_0]

		if ove_0_14(slot_8_0) and slot_8_0.pos2D:dist(player.pos2D) < 400 then
			return ove_0_19(vec3(slot_8_0.pos.x, slot_8_0.pos.y, slot_8_0.pos.z), true)
		end
	end
end

local function ove_0_22()
	-- print 9
	for iter_9_0 = 0, objManager.turrets.size[TEAM_ALLY] - 1 do
		local slot_9_0 = objManager.turrets[TEAM_ALLY][iter_9_0]
		local slot_9_1 = slot_9_0.pos2D:distSqr(player.pos2D)

		if slot_9_1 > 640000 and slot_9_1 < 1440000 then
			return vec3(slot_9_0.x, slot_9_0.y, slot_9_0.z)
		end
	end
end

for iter_0_0 = 0, objManager.maxObjects - 1 do
	local ove_0_23 = objManager.get(iter_0_0)

	if ove_0_23 and ove_0_23.type == TYPE_TURRET and ove_0_23.team == TEAM_ALLY and not ove_0_23.isDead and ove_0_23.name:find("Shrine") then
		ove_0_11 = vec3(ove_0_23.pos.x, ove_0_23.pos.y, ove_0_23.pos.z)
	end
end

return {
	get_inactive_turret = ove_0_22,
	get_target = ove_0_16,
	safety = ove_0_21,
	invoke = ove_0_19,
	on_draw = ove_0_20
}

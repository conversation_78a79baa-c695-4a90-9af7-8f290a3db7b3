local extraManager = {}
extraManager.TargetSelector = module.internal("TS")

local BuffManager = module.load("Brian", "Library/BuffManager")

function extraManager.CanDrawCircle(range, fromPos)
    fromPos = fromPos or player.pos
    if fromPos:dist(game.cameraPos) < (range + (graphics.width + graphics.height) / 1.65) then
        return true
    end
end

function extraManager.IsValidTarget(target, range)
    range = range or 20000
    if target and target ~= nil and not target.isDead and target.health and target.health > 0 then
        if target.type == TYPE_HERO then
            if target.isVisible and target.isTargetable and target.pos and target.boundingRadius then
                if BuffManager.HasBuffOfType(target, 17) then
                    return false
                end
                if target.charName == "KogMaw" and BuffManager.HasBuff(target, "KogMawIcathianSurprise") then
                    return false
                end
                if target.charName == "Karthus" and BuffManager.<PERSON><PERSON>uff(target, "KarthusDeathDefiedBuff") then
                    return false
                end
                if target.charName == "Sion" and BuffManager.HasBuff(target, "sionpassivezombie") then
                    return false
                end
                local distance = player.pos:distSqr(target.pos)
                if distance <= ((range + target.boundingRadius) * (range + target.boundingRadius)) then
                    return true
                end
            end
        elseif target.type == TYPE_MINION or target.type == TYPE_TURRET or target.type == TYPE_INHIB or target.type == TYPE_NEXUS then
            if target.isVisible and target.isTargetable and target.pos then
                local distance = player.pos:distSqr(target.pos)
                if distance <= (range * range) then
                    return true
                end
            end
        end
    end
end

function extraManager.IsFloatingHpBar(target)
    return true
    -- if target and target ~= nil and not target.isDead and target.isVisible then
    --     if target.type == TYPE_HERO or target.type == TYPE_MINION or target.type == TYPE_TURRET then
    --         local barPos = target.barPos
    --         if barPos and barPos.x and barPos.y then
    --             if barPos.x ~= 0 and barPos.y ~= 0 then
    --                 return true
    --             end
    --         end
    --     end
    -- end
end

function extraManager.GetAutoAttackRange(target)
    local basicAttackRange = player.attackRange + player.boundingRadius
    if not target then
        return basicAttackRange
    end
    if not extraManager.IsValidTarget(target) then
        return basicAttackRange
    end
    if extraManager.IsValidTarget(target) and (target.type == TYPE_HERO or target.type == TYPE_MINION) and BuffManager.HasBuff(target, "caitlynyordletrapinternal") then
        basicAttackRange = 1300 + player.boundingRadius
    end
    if target.type == TYPE_TURRET then
        return player.attackRange + player.boundingRadius + 120
    end
    if target.type == TYPE_NEXUS then
        return player.attackRange + player.boundingRadius + 195
    end
    if target.type == TYPE_INHIB then
        return player.attackRange + player.boundingRadius + 179
    end
    if target.boundingRadius then
        return basicAttackRange + target.boundingRadius - 35
    end
    return basicAttackRange
end

function extraManager.IsInAutoAttackRange(target, extraRange)
    extraRange = extraRange or 0
    if target and target ~= nil and not target.isDead and extraManager.IsValidTarget(target) then
        local attackRange = extraManager.GetAutoAttackRange(target) + extraRange
        if (target.pos:distSqr(player.pos)) <= (attackRange * attackRange) then
            return true
        end
    end
end

function extraManager.GetManaPercent(source)
    source = source or player
    if source and source ~= nil and not source.isDead then
        if source.maxPar == 0 then
            return 100
        end
        local per = (source.par / source.maxPar) * 100
        return per
    end
    return 0
end

function extraManager.GetHealthPercent(source)
    source = source or player
    if source and source ~= nil and not source.isDead then
        local per = (source.health / source.maxHealth) * 100
        return per
    end
    return 0
end

function extraManager.GetTotalAD(source)
    source = source or player
    if source and source ~= nil and source.baseAttackDamage and source.flatPhysicalDamageMod then
        if source.type == TYPE_HERO and source.percentPhysicalDamageMod then
            local percentPhysicalDamageMod = source.percentPhysicalDamageMod
            if percentPhysicalDamageMod == 0 or percentPhysicalDamageMod < 0 then
                percentPhysicalDamageMod = 1
            end
            return (source.baseAttackDamage + source.flatPhysicalDamageMod) * percentPhysicalDamageMod
        end
        if source.type == TYPE_MINION or source.type == TYPE_TURRET then
            local result = source.baseAttackDamage + source.flatPhysicalDamageMod
            return result
        end
        return 0
    end
    return 0
end

function extraManager.GetBonusAD(source)
    source = source or player
    if source and source ~= nil then
        if source.type == TYPE_HERO and source.percentPhysicalDamageMod then
            local percentPhysicalDamageMod = source.percentPhysicalDamageMod
            if percentPhysicalDamageMod == 0 or percentPhysicalDamageMod < 0 then
                percentPhysicalDamageMod = 1
            end
            return source.flatPhysicalDamageMod * percentPhysicalDamageMod
        end
        return source.flatPhysicalDamageMod
    end
    return 0
end

function extraManager.GetTotalAP(source)
    source = source or player
    if source and source ~= nil and source.flatMagicDamageMod and source.percentMagicDamageMod then
        return source.flatMagicDamageMod * source.percentMagicDamageMod
    end
    return 0
end

function extraManager.GetTarget(range)
    return extraManager.TargetSelector.get_result(
        function(res, obj, dist)
            if dist < range then
                res.obj = obj
                return true
            end
        end
    ).obj
end

function extraManager.IsBigMob(target)
    if target and target ~= nil and target.charName and target.type == TYPE_MINION then
        if target.charName == "SRU_Baron" then
            return true
        end
        if target.charName == "SRU_RiftHerald" then
            return true
        end
        if target.charName == "SRU_Dragon_Earth" then
            return true
        end
        if target.charName == "SRU_Dragon_Water" then
            return true
        end
        if target.charName == "SRU_Dragon_Fire" then
            return true
        end
        if target.charName == "SRU_Dragon_Air" then
            return true
        end
        if target.charName == "SRU_Dragon_Elder" then
            return true
        end
        if target.charName == "SRU_Murkwolf" then
            return true
        end
        if target.charName == "SRU_Blue" then
            return true
        end
        if target.charName == "SRU_Red" then
            return true
        end
        if target.charName == "SRU_Gromp" then
            return true
        end
        if target.charName == "Sru_Crab" then
            return true
        end
        if target.charName == "SRU_Razorbeak" then
            return true
        end
        if target.charName == "SRU_Krug" then
            return true
        end
    end
end

function extraManager.IsUnKillAble(target)
    if
        target and target ~= nil and not target.isDead and target.isVisible and target.isTargetable and target.health and
            target.health > 0
     then
        if not BuffManager.HasBuffOfType(target, 17) then
            if target.charName == "KogMaw" and BuffManager.HasBuff(target, "KogMawIcathianSurprise") then
                return true
            end
            if target.charName == "Karthus" and BuffManager.HasBuff(target, "KarthusDeathDefiedBuff") then
                return true
            end
            if target.charName == "Sion" and BuffManager.HasBuff(target, "sionpassivezombie") then
                return true
            end
            if BuffManager.HasBuff(target, "malzaharpassiveshield") then
                return true
            end
            if BuffManager.HasBuff(target, "NocturneShroudofDarkness") then
                return true
            end
            if BuffManager.HasBuff(target, "KindredRNoDeathBuff") then
                return true
            end
            if BuffManager.HasBuff(target, "BansheesVeil") then
                return true
            end
            if BuffManager.HasBuff(target, "UndyingRage") then
                return true
            end
            if BuffManager.HasBuff(target, "JudicatorIntervention") then
                return true
            end
            if BuffManager.HasBuff(target, "ChronoShift") then
                return true
            end
            if BuffManager.HasBuff(target, "FioraW") then
                return true
            end
            if BuffManager.HasBuff(target, "ShroudofDarkness") then
                return true
            end
            if BuffManager.HasBuff(target, "SivirShield") then
                return true
            end
            if BuffManager.HasBuff(target, "zhonyasringshield") then
                return true
            end
            if BuffManager.HasBuff(target, "LissandraRSelf") then
                return true
            end
            if BuffManager.HasBuff(target, "bardrstasis") then
                return true
            end
            if BuffManager.HasBuff(target, "TaricR") then
                return true
            end
            if BuffManager.HasBuff(target, "willrevive") then
                return true
            end
        end
    end
end

return extraManager

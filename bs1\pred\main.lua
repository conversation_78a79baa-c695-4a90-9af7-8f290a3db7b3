

local ove_0_6 = module.load(header.id, "bs/pred/menu")
local ove_0_7 = module.load(header.id, "bs/pred/dash")
local ove_0_8 = module.load(header.id, "bs/pred/core")

function ove_0_8.load(arg_5_0)
	-- function 5
	ove_0_8.init()

	preds = ove_0_8

	return true
end

function ove_0_8.waypoints_change(arg_6_0)
	-- function 6
	ove_0_7.waypoints_change(arg_6_0)
	ove_0_8.waypoints_change_init(arg_6_0)
end

function ove_0_8.tick()
	-- function 7
	ove_0_8.on_tick()
end

function ove_0_8.spell_cancel(arg_8_0)
	-- function 8
	ove_0_8.on_spell_cancel(arg_8_0)
end

function ove_0_8.spell_execute(arg_9_0, arg_9_1)
	-- function 9
	ove_0_8.on_spell_execute(arg_9_0, arg_9_1)
	ove_0_7.spell_execute(arg_9_0, arg_9_1)
end

function ove_0_8.process_spell(arg_10_0, arg_10_1)
	-- function 10
	ove_0_8.on_process_spell(arg_10_0, arg_10_1)
	ove_0_7.process_spell(arg_10_0, arg_10_1)
end

function ove_0_8.delete_object(arg_11_0, arg_11_1)
	-- function 11
	if arg_11_0 == TYPE_MINION then
		ove_0_8.delete_minion(arg_11_1)
	end

	if arg_11_0 == TYPE_MISSILE then
		ove_0_8.delete_missile(arg_11_1)
	end
end

function ove_0_8.draw()
	-- function 12
	ove_0_8.draw()
	ove_0_7.draw()
end

function ove_0_8.cast_spell(arg_13_0)
	-- function 13
	ove_0_8.castSpellCall(arg_13_0)
end

function ove_0_8.lose_buff(arg_14_0, arg_14_1)
	-- function 14
	ove_0_7.lose_buff(arg_14_0, arg_14_1)
end

return ove_0_8



local ove_0_10 = module.internal("orb")
local ove_0_11 = module.load(header.id, "plugins/Zac/menu")
local ove_0_12 = module.load(header.id, "plugins/Zac/core")
local ove_0_13 = module.load(header.id, "common/Draw/draw")
local ove_0_14 = module.load(header.id, "plugins/Zac/spells/q")
local ove_0_15 = module.load(header.id, "plugins/Zac/spells/w")
local ove_0_16 = module.load(header.id, "plugins/Zac/spells/e")
local ove_0_17 = module.load(header.id, "plugins/Zac/spells/r")
local ove_0_18 = module.load(header.id, "plugins/Zac/misc/damage")

local function ove_0_19()
	ove_0_12.get_action()
end

local function ove_0_20(arg_6_0)
	if arg_6_0.owner.ptr == player.ptr then
		ove_0_12.on_recv_spell(arg_6_0)
	end

	if arg_6_0.owner.team == TEAM_ENEMY then
		ove_0_17.interrupt_data_spell(arg_6_0)
	end
end

local function ove_0_21()
	if player.isOnScreen then
		if ove_0_11.draws.useq:get() then
			ove_0_14.on_draw()
		end

		if ove_0_11.draws.usee:get() then
			ove_0_16.on_draw()
		end

		if ove_0_11.draws.usew:get() then
			ove_0_15.on_draw()
		end

		if ove_0_11.draws.user:get() then
			ove_0_17.on_draw()
		end
	end
end

local function ove_0_22()
	if player.isDead then
		return
	end

	if ove_0_11.draws.drawqDamage:get() then
		ove_0_18.over_layer()
	end

	if not player.isOnScreen then
		return
	end

	if not ove_0_11.draws.draw_toggle:get() then
		return
	end

	if player.isOnScreen then
		local slot_8_0 = graphics.world_to_screen(player.pos)

		if ove_0_11.farming.togleFarming:get() then
			ove_0_13.text("Farm:", 18, slot_8_0.x, slot_8_0.y + 40, graphics.argb(255, 255, 255, 255), "center")
			ove_0_13.text("On", 18, slot_8_0.x + 40, slot_8_0.y + 40, graphics.argb(255, 7, 219, 63), "center")
		else
			ove_0_13.text("Farm:", 18, slot_8_0.x, slot_8_0.y + 40, graphics.argb(255, 255, 255, 255), "center")
			ove_0_13.text("OFF", 18, slot_8_0.x + 40, slot_8_0.y + 40, graphics.argb(255, 219, 7, 7), "center")
		end

		if ove_0_11.e_under_tower:get() then
			ove_0_13.text("E Under-Tower:", 18, slot_8_0.x, slot_8_0.y + 55, graphics.argb(255, 255, 255, 255), "center")
			ove_0_13.text("On", 18, slot_8_0.x + 90, slot_8_0.y + 55, graphics.argb(255, 7, 219, 63), "center")
		else
			ove_0_13.text("E Under-Tower:", 18, slot_8_0.x, slot_8_0.y + 55, graphics.argb(255, 255, 255, 255), "center")
			ove_0_13.text("OFF", 18, slot_8_0.x + 90, slot_8_0.y + 55, graphics.argb(255, 219, 7, 7), "center")
		end
	end
end

local function ove_0_23(arg_9_0)
	if not ove_0_11.key_click:get() then
		return
	end

	if ove_0_11.e_under_tower:get() then
		return
	end

	if arg_9_0 == 1 then
		local slot_9_0, slot_9_1 = ove_0_16.closestTurret(vec2.clone(game.mousePos2D))

		if slot_9_0 and slot_9_1 < 921600 then
			ove_0_11.e_under_tower:set("toggleValue", true)
		end
	end
end

ove_0_10.combat.register_f_pre_tick(ove_0_19)
cb.add(cb.spell, ove_0_20)
cb.add(cb.draw, ove_0_21)
cb.add(cb.draw_overlay, ove_0_22)
cb.add(cb.keydown, ove_0_23)

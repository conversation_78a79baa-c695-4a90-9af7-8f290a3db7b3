local antiGapcloser = {}

local antiGapcloserCallbacks = {}

local activespells = {}
local ingamespells = {}
local spells = {
    ["Warwick"] = {
        ["warwickr"] = true,
    },
    ["Urgot"] = {
        ["urgote"] = true,
    },
    ["Vayne"] = {
        ["vaynetumble"] = true,
    },
    ["Kayn"] = {
        ["kaynq"] = true,
    },
    ["Nidalee"] = {
        ["pounce"] = true,
    },
    ["Ornn"] = {
        ["ornne"] = true,
    },
    ["Ekko"] = {
        ["ekkoeattack"] = true,
        ["ekkoe"] = true,
    },
    ["Zac"] = {
        ["zace"] = true,
    },
    ["Galio"] = {
        ["galioe"] = true,
    },
    ["Shyvana"] = {
        ["shyvanatransformleap"] = true,
    },
    ["Rakan"] = {
        ["rakanw"] = true,
    },
    ["Kindred"] = {
        ["kindredq"] = true,
    },
    ["Ezreal"] = {
        ["ezrealarcaneshift"] = true,
    },
    ["Camille"] = {
        ["camillee"] = true,
        ["camilleedash"] = true,
        ["camilleedash2"] = true,
    },
    ["Aatrox"] = {
        ["aatroxq"] = true,
        ["aatroxq3"] = true,
        ["aatroxe"] = true,
    },
    ["Ahri"] = {
        ["ahritumble"] = true,
    },
    ["Alistar"] = {
        ["alistarw"] = true,
        ["headbutt"] = true,
    },
    ["Akali"] = {
        ["akalishadowdance"] = true,
    },
    ["MasterYi"] = {
        ["alphastrike"] = true,
    },
    ["Amumu"] = {
        ["dandagetoss"] = true,
    },
    ["FiddleSticks"] = {
        ["crowstorm"] = true,
    },
    ["Diana"] = {
        ["dianateleport"] = true,
    },
    ["Elise"] = {
        ["elisespideredescent"] = true,
        ["elisespiderqcast"] = true,
    },
    ["Fiora"] = {
        ["fioraq"] = true,
    },
    ["Fizz"] = {
        ["fizzetwo"] = true,
        ["fizzq"] = true,
    },
    ["Gnar"] = {
        ["gnarbige"] = true,
        ["gnare"] = true,
    },
    ["Gragas"] = {
        ["gragase"] = true,
    },
    ["Graves"] = {
        ["gravesmove"] = true,
    },
    ["Hecarim"] = {
        ["hecarimult"] = true,
    },
    ["Irelia"] = {
        ["ireliaq"] = true,
        ["ireliagatotsu"] = true,
    },
    ["JarvanIV"] = {
        ["jarvanivcataclysm"] = true,
        ["jarvanivdragonstrike"] = true,
    },
    ["LeeSin"] = {
        ["blindmonkqtwo"] = true,
        ["blindmonkwtwo"] = true,
    },
    ["Jax"] = {
        ["jaxleapstrike"] = true,
    },
    ["Jayce"] = {
        ["jaycetotheskies"] = true,
    },
    ["Katarina"] = {
        ["katarinae"] = true,
    },
    ["Kennen"] = {
        ["kennenlightningrush"] = true,
    },
    ["Khazix"] = {
        ["khazixe"] = true,
        ["khazixelong"] = true,
    },
    ["Leblanc"] = {
        ["leblancw"] = true,
        ["leblancrw"] = true,
    },
    ["Leona"] = {
        ["leonazenithblade"] = true,
    },
    ["Lissandra"] = {
        ["lissandre"] = true,
    },
    ["Lucian"] = {
        ["luciane"] = true,
    },
    ["Maokai"] = {
        ["maokaiw"] = true,
    },
    ["MonkeyKing"] = {
        ["monkeykingnimbus"] = true,
    },
    ["Nautilus"] = {
        ["nautilusanchordrag"] = true,
    },
    ["Pantheon"] = {
        ["pantheonw"] = true,
    },
    ["Poppy"] = {
        ["poppye"] = true,
    },
    ["Quinn"] = {
        ["quinne"] = true,
    },
    ["Renekton"] = {
        ["renektonsliceanddice"] = true,
        ["renektondice"] = true,
    },
    ["Kassadin"] = {
        ["riftwalk"] = true,
    },
    ["Riven"] = {
        ["riventricleave"] = true,
        ["rivenfeint"] = true,
    },
    ["tristana"] = {
        ["tristanaw"] = true,
    },
    ["Sejuani"] = {
        ["sejuaniq"] = true,
    },
    ["Shen"] = {
        ["shene"] = true,
    },
    ["Talon"] = {
        ["talonq"] = true,
    },
    ["Malphite"] = {
        ["ufslash"] = true,
    },
    ["Udyr"] = {
        ["udyrdearstance"] = true,
    },
    ["Corki"] = {
        ["carpetbomb"] = true,
    },
    ["Vi"] = {
        ["viq"] = true,
    },
    ["Volibear"] = {
        ["volibearq"] = true,
    },
    ["XinZhao"] = {
        ["xinzhaoe"] = true,
    },
    ["Yasuo"] = {
        ["yasuodashwrapper"] = true,
    },
    ["RekSai"] = {
        ["reksaieburrowed"] = true,
    },
    ["Tryndamere"] = {
        ["tryndameree"] = true,
    },
    ["Rengar"] = {
        ["RengarAttackJump"] = true,
    },
}

local rengaringame = false
local isInject = false

local testMenu

local ObjectManager = module.load("Brian", "Library/ObjectManager")
local MyCommon = module.load("Brian", "Library/ExtraManager")

local function AddCallback(args)
    if args then
        for i, callback in ipairs(antiGapcloserCallbacks) do
            callback(args)
        end
    end
end

local function OnMyProcessSpellCast(spellData)
    if not isInject then
        return
    end
    if spellData and spellData.owner and spellData.owner.type == TYPE_HERO and spellData.team ~= player.team and spellData.name and ingamespells[spellData.owner.charName] then
        local source = spellData.owner
        local name = string.lower(spellData.name)
        local time = 1.5--spellData.animationTime and (spellData.animationTime + (network.latency * 10)) or 1.9
        if spellData.hasTarget and spellData.target and spellData.target.ptr and spellData.target.ptr == player.ptr then
            if ingamespells[spellData.owner.charName] and ingamespells[spellData.owner.charName][name] then
                if source.pos then
                    local args = {
                        from = source,
                        startTime = game.time,
                        endTime = game.time + time,
                        startPos = vec2(source.pos.x, source.pos.z),
                        endPos = vec2(player.pos.x, player.pos.z),
                        isTargettable = true,
                    }
                    table.insert(activespells, args)
                    return
                end
            end
        elseif not spellData.hasTarget and spellData.endPos2D then
            if ingamespells[spellData.owner.charName] and ingamespells[spellData.owner.charName][name] then
                if spellData.startPos then
                    local args = {
                        from = source,
                        startTime = game.time,
                        endTime = game.time + time,
                        startPos = vec2(spellData.startPos.x, spellData.startPos.z),
                        endPos = vec2(spellData.endPos.x, spellData.endPos.z),   
                        isTargettable = false,
                    }
                    table.insert(activespells, args)
                    return
                elseif not spellData.startPos and source.pos then
                    local args = {
                        from = source,
                        startTime = game.time,
                        endTime = game.time + time,
                        startPos = vec2(source.pos.x, source.pos.z),
                        endPos = vec2(spellData.endPos2D.x, spellData.endPos2D.z), 
                        isTargettable = false,
                    }
                    table.insert(activespells, args)
                    return
                end
            end
        end
        return
    end
end

local function OnMyPath(source)
    if not isInject then
        return
    end
    if rengaringame and source and source.type == TYPE_HERO and source.team ~= player.team and source.charName and source.charName == "Rengar" then
        if source.path and source.path.isDashing and source.path.point2D and source.path.point2D[0] and source.path.point2D[1] then
            if source.path.point2D[1]:dist(player.pos2D) < 200 then
                local args = {
                    from = source,
                    startTime = game.time,
                    endTime = game.time + 1.9,
                    startPos = source.pos2D,
                    endPos = player.pos2D, 
                    isTargettable = true,
                }
                table.insert(activespells, args)
            end
        end
    end
end

local function OnMyTick()
    if not isInject then
        return
    end
    for i, activespell in ipairs(activespells) do
        if activespell and activespell.from and activespell.endTime and activespell.from then
            if activespell.endTime - game.time > 0 then
                local target = activespell.from
                if target and MyCommon.IsValidTarget(target, 2000) and target.charName and testMenu and testMenu.AntiGapcloser and testMenu.AntiGapcloser[target.charName] and testMenu.AntiGapcloser[target.charName]:get() then
                    AddCallback(activespell)
                end
            end
            if game.time > activespell.endTime then
                table.remove(activespells, i)
            end
        end
    end
end

function antiGapcloser.AddAntiGapcloserCallback(cb)
    if cb then
        assert(cb and type(cb) == "function", "[" .. (os.date("%X - %Y")) .. "] ::: OnAntiGapcloser Callback is invalid!")
        table.insert(antiGapcloserCallbacks, cb)
    end
end

function antiGapcloser.AddToMenu(MyMenu, spellSlot, spellSlotEnabled, spellSlot1, spellSlot1Enabled)

    testMenu = MyMenu

    isInject = true

    local alreadyHaveAntiTarget = false
    spellSlotEnabled = spellSlotEnabled or true
    spellSlot1Enabled = spellSlot1Enabled or true

    MyMenu:menu("AntiGapcloser", "AntiGapcloser Settings")
    MyMenu.AntiGapcloser:header("TargetHeader", "Anti Gapcloser Target list")
    local targets = ObjectManager.GetEnemyHeroes()
    for _, target in ipairs(targets) do
        for i, spellData in pairs(spells) do
            if i and target and target.charName == i then
                if target.charName == "Rengar" then
                    rengaringame = true
                end
                ingamespells[target.charName] = spellData
                if not MyMenu.AntiGapcloser[target.charName] then
                    MyMenu.AntiGapcloser:boolean(target.charName, target.charName, true)
                end
                alreadyHaveAntiTarget = true
            end
        end
    end

    if not alreadyHaveAntiTarget then
        MyMenu.AntiGapcloser:header("NoTargetHeader", "No Target Support in game")
    end

    if alreadyHaveAntiTarget then
        MyMenu.AntiGapcloser:header("SpellHeader", "Spell Usage")
        if spellSlot then
            if spellSlot == 0 then
                MyMenu.AntiGapcloser:boolean("Q", "Use Q", spellSlotEnabled)
            elseif spellSlot == 1 then
                MyMenu.AntiGapcloser:boolean("W", "Use W", spellSlotEnabled)
            elseif spellSlot == 2 then
                MyMenu.AntiGapcloser:boolean("E", "Use E", spellSlotEnabled)
            elseif spellSlot == 3 then
                MyMenu.AntiGapcloser:boolean("R", "Use R", spellSlotEnabled)
            end
        end
        if spellSlot1 then
            if spellSlot1 == 0 then
                MyMenu.AntiGapcloser:boolean("Q", "Use Q", spellSlot1Enabled)
            elseif spellSlot1 == 1 then
                MyMenu.AntiGapcloser:boolean("W", "Use W", spellSlot1Enabled)
            elseif spellSlot1 == 2 then
                MyMenu.AntiGapcloser:boolean("E", "Use E", spellSlot1Enabled)
            elseif spellSlot1 == 3 then
                MyMenu.AntiGapcloser:boolean("R", "Use R", spellSlot1Enabled)
            end
        end

        cb.add(cb.spell, OnMyProcessSpellCast)
        cb.add(cb.path, OnMyPath)
        local PremiumOrbwalker = module.internal("orb")
        PremiumOrbwalker.combat.register_f_pre_tick(OnMyTick)

    end
end

return antiGapcloser
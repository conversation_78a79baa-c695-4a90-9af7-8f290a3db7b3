-- Nidalee 简化清线模块
-- 修复了所有 nil 比较错误

local clear = {}

-- 检查是否为人形态
local function isHuman()
    return player and player:spellSlot(0) and player:spellSlot(0).name == "JavelinToss"
end

-- 检查是否被标记
local function isHunted(unit)
    return unit and unit.buff and unit.buff["nidaleepassivehunted"]
end

-- 检查目标是否有效
local function IsValidTarget(object)
    return (object and not object.isDead and object.isVisible and object.isTargetable)
end

-- 安全的菜单检查函数
local function safeGet(menuItem, defaultValue)
    if menuItem and menuItem.get then
        return menuItem:get()
    end
    return defaultValue or false
end

-- 获取法力值百分比
local function GetManaPercent()
    if player and player.par and player.maxPar and player.maxPar > 0 then
        return (player.par / player.maxPar) * 100
    end
    return 0
end

-- 简化的清线功能
function clear.Farm(menu)
    if not menu or not menu.farm then return end

    local manaPercent = GetManaPercent()
    local manaThreshold = safeGet(menu.farm.Mana, 40)
    local manaCheck = manaPercent >= manaThreshold

    if not objManager or not objManager.minions then return end

    local minions = objManager.minions
    for i = 0, minions.size[TEAM_ENEMY] - 1 do
        local minion = minions[TEAM_ENEMY][i]
        if minion and IsValidTarget(minion) then
            local dist = player.path.serverPos:dist(minion.path.serverPos)

            if not isHuman() then
                -- 豹形态清线
                if safeGet(menu.farm.QC, true) and player:spellSlot(0).state == 0 and
                   player:spellSlot(0).name == "Takedown" and dist <= 400 then
                    player:castSpell("self", 0)
                    return
                end
                if safeGet(menu.farm.EC, true) and player:spellSlot(2).state == 0 and
                   player:spellSlot(2).name == "Swipe" and dist < 300 then
                    player:castSpell("pos", 2, minion.pos)
                    return
                end
                if safeGet(menu.farm.WC, true) and player:spellSlot(1).state == 0 and
                   player:spellSlot(1).name == "Pounce" then
                    local maxRange = isHunted(minion) and 750 or 375
                    if dist <= maxRange then
                        player:castSpell("pos", 1, minion.pos)
                        return
                    end
                end
            else
                -- 人形态清线
                if safeGet(menu.farm.Q, true) and player:spellSlot(0).state == 0 and
                   player:spellSlot(0).name == "JavelinToss" and manaCheck and dist <= 1500 then
                    player:castSpell("pos", 0, minion.pos)
                    return
                end
                if safeGet(menu.farm.W, true) and player:spellSlot(1).state == 0 and
                   player:spellSlot(1).name == "BushWhack" and manaCheck and dist < 900 then
                    player:castSpell("pos", 1, minion.pos)
                    return
                end
            end
        end
    end
end

-- 简化的打野功能
function clear.JungleFarm(menu)
    if not menu or not menu.farm then return end

    local manaPercent = GetManaPercent()
    local manaThreshold = safeGet(menu.farm.Mana, 40)
    local manaCheck = manaPercent >= manaThreshold

    if not objManager or not objManager.minions then return end

    local minions = objManager.minions
    for i = 0, minions.size[TEAM_NEUTRAL] - 1 do
        local minion = minions[TEAM_NEUTRAL][i]
        if minion and IsValidTarget(minion) then
            local dist = player.path.serverPos:dist(minion.path.serverPos)

            if not isHuman() then
                -- 豹形态打野
                if safeGet(menu.farm.QC, true) and player:spellSlot(0).state == 0 and
                   player:spellSlot(0).name == "Takedown" and dist <= 400 then
                    player:castSpell("self", 0)
                    return
                end
                if safeGet(menu.farm.EC, true) and player:spellSlot(2).state == 0 and
                   player:spellSlot(2).name == "Swipe" and dist < 300 then
                    player:castSpell("pos", 2, minion.pos)
                    return
                end
                if safeGet(menu.farm.WC, true) and player:spellSlot(1).state == 0 and
                   player:spellSlot(1).name == "Pounce" then
                    local maxRange = isHunted(minion) and 750 or 375
                    if dist <= maxRange then
                        player:castSpell("pos", 1, minion.pos)
                        return
                    end
                end
            else
                -- 人形态打野
                if safeGet(menu.farm.Q, true) and player:spellSlot(0).state == 0 and
                   player:spellSlot(0).name == "JavelinToss" and manaCheck and dist <= 1500 then
                    player:castSpell("pos", 0, minion.pos)
                    return
                end
                if safeGet(menu.farm.W, true) and player:spellSlot(1).state == 0 and
                   player:spellSlot(1).name == "BushWhack" and manaCheck and dist < 900 then
                    player:castSpell("pos", 1, minion.pos)
                    return
                end
            end
        end
    end
end

-- 智能清线功能 - 优先击杀低血量小兵
function clear.SmartFarm(menu)
    if not menu or not menu.farm then return end

    local manaPercent = GetManaPercent()
    local manaThreshold = safeGet(menu.farm.Mana, 40)
    local manaCheck = manaPercent >= manaThreshold

    if not objManager or not objManager.minions then return end

    -- 获取所有可攻击的小兵
    local validMinions = {}
    local minions = objManager.minions
    for i = 0, minions.size[TEAM_ENEMY] - 1 do
        local minion = minions[TEAM_ENEMY][i]
        if minion and IsValidTarget(minion) then
            local dist = player.path.serverPos:dist(minion.path.serverPos)
            if dist <= 1500 then
                table.insert(validMinions, {minion = minion, dist = dist})
            end
        end
    end

    -- 按血量排序，优先击杀低血量小兵
    table.sort(validMinions, function(a, b) return a.minion.health < b.minion.health end)

    for _, data in ipairs(validMinions) do
        local minion = data.minion
        local dist = data.dist

        if not isHuman() then
            -- 豹形态优先级：Q > E > W
            if safeGet(menu.farm.QC, true) and player:spellSlot(0).state == 0 and
               player:spellSlot(0).name == "Takedown" and dist <= 400 then
                player:castSpell("self", 0)
                return
            end
            if safeGet(menu.farm.EC, true) and player:spellSlot(2).state == 0 and
               player:spellSlot(2).name == "Swipe" and dist < 300 then
                player:castSpell("pos", 2, minion.pos)
                return
            end
            if safeGet(menu.farm.WC, true) and player:spellSlot(1).state == 0 and
               player:spellSlot(1).name == "Pounce" then
                local maxRange = isHunted(minion) and 750 or 375
                if dist <= maxRange then
                    player:castSpell("pos", 1, minion.pos)
                    return
                end
            end
        else
            -- 人形态
            if safeGet(menu.farm.Q, true) and player:spellSlot(0).state == 0 and
               player:spellSlot(0).name == "JavelinToss" and manaCheck and dist <= 1500 then
                player:castSpell("pos", 0, minion.pos)
                return
            end
        end
    end

    -- 自动切换形态
    if safeGet(menu.farm.autoR, true) and player:spellSlot(3).state == 0 and #validMinions > 0 then
        if isHuman() then
            if player:spellSlot(0).state ~= 0 or not manaCheck then
                player:castSpell("self", 3)
            end
        else
            if player:spellSlot(0).state ~= 0 and player:spellSlot(1).state ~= 0 and
               player:spellSlot(2).state ~= 0 and manaCheck then
                player:castSpell("self", 3)
            end
        end
    end
end

return clear

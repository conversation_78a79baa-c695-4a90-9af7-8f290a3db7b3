

local ove_0_10 = {}
local ove_0_11 = {}
local ove_0_12 = {}
local ove_0_13 = {}
local ove_0_14 = {}

ove_0_10.BuffType = {
	Knockback = 30,
	<PERSON>ison = 23,
	<PERSON>lee = 28,
	Sleep = 18,
	PhysicalImmunity = 16,
	He<PERSON> = 13,
	<PERSON> = 26,
	<PERSON>ra = 1,
	Grounded = 32,
	<PERSON> = 0,
	<PERSON>nock<PERSON> = 29,
	Suppression = 24,
	<PERSON><PERSON><PERSON> = 33,
	<PERSON><PERSON><PERSON> = 34,
	<PERSON> = 7,
	<PERSON><PERSON> = 12,
	<PERSON>hr<PERSON> = 27,
	Invisibility = 6,
	SpellImmunity = 15,
	Charm = 22,
	<PERSON> = 21,
	<PERSON>ymorph = 9,
	SpellShield = 4,
	<PERSON><PERSON> = 14,
	<PERSON><PERSON><PERSON> = 11,
	<PERSON> = 25,
	NearSight = 19,
	Fr<PERSON>zy = 20,
	Slow = 10,
	Taunt = 8,
	<PERSON><PERSON> = 5,
	Invulnerability = 17,
	<PERSON>Dehancer = 3,
	Disarm = 31,
	CombatEnchancer = 2
}

local function ove_0_15(arg_5_0, arg_5_1)
	if arg_5_0 then
		for iter_5_0, iter_5_1 in ipairs(arg_5_1) do
			iter_5_1(arg_5_0)
		end
	end
end

local function ove_0_16(arg_6_0, arg_6_1)
	if arg_6_0 and arg_6_0.startTime and arg_6_0.endTime and arg_6_0.startTime <= game.time and arg_6_0.endTime >= game.time then
		return true
	end
end

local function ove_0_17()
	local slot_7_0 = ove_0_11

	for iter_7_0, iter_7_1 in ipairs(slot_7_0) do
		if iter_7_1 and iter_7_1 ~= nil and not iter_7_1.isDead and iter_7_1.ptr and iter_7_1.buff then
			for iter_7_2, iter_7_3 in pairs(iter_7_1.buff) do
				if iter_7_2 and iter_7_3 and ove_0_16(iter_7_3) and not ove_0_12[iter_7_2] then
					local slot_7_1 = {
						check1 = false,
						check2 = false,
						ptr = iter_7_1.ptr,
						buffer = iter_7_3,
						owner = iter_7_3.owner,
						name = iter_7_2,
						startTime = iter_7_3.startTime,
						endTime = iter_7_3.endTime,
						stacks = iter_7_3.stacks,
						stacks2 = iter_7_3.stacks2,
						valid = iter_7_3.valid,
						type = iter_7_3.type,
						luaclass = iter_7_3.luaclass,
						source = iter_7_3.source
					}

					ove_0_12[iter_7_2] = slot_7_1
				end
			end
		end
	end

	for iter_7_4, iter_7_5 in pairs(ove_0_12) do
		local slot_7_2 = iter_7_5

		if ove_0_16(slot_7_2) and not iter_7_5.check1 then
			local slot_7_3 = {
				owner = slot_7_2.owner,
				name = slot_7_2.name,
				startTime = slot_7_2.startTime,
				endTime = slot_7_2.endTime,
				stacks = slot_7_2.stacks,
				stacks2 = slot_7_2.stacks2,
				valid = slot_7_2.valid,
				type = slot_7_2.type,
				luaclass = slot_7_2.luaclass,
				source = slot_7_2.source
			}

			ove_0_15(slot_7_3, ove_0_13)

			iter_7_5.check1 = true
		elseif not player.buff[slot_7_2.name] and not iter_7_5.check2 then
			local slot_7_4 = {
				owner = slot_7_2.owner,
				name = slot_7_2.name,
				startTime = slot_7_2.startTime,
				endTime = slot_7_2.endTime,
				stacks = slot_7_2.stacks,
				stacks2 = slot_7_2.stacks2,
				valid = slot_7_2.valid,
				type = slot_7_2.type,
				luaclass = slot_7_2.luaclass,
				source = slot_7_2.source
			}

			ove_0_15(slot_7_4, ove_0_14)

			iter_7_5.check2 = true
			ove_0_12[slot_7_2.name] = nil
		end
	end
end

function ove_0_10.add(arg_8_0)
	if arg_8_0 then
		assert(arg_8_0 and type(arg_8_0) == "function", "[" .. os.date("%X - %Y") .. "] ::: BuffManager Callback is invalid!")
		table.insert(ove_0_13, arg_8_0)
	end
end

function ove_0_10.remove(arg_9_0)
	if arg_9_0 then
		assert(arg_9_0 and type(arg_9_0) == "function", "[" .. os.date("%X - %Y") .. "] ::: BuffManager Callback is invalid!")
		table.insert(ove_0_14, arg_9_0)
	end
end

function ove_0_10.HasBuff(arg_10_0, arg_10_1)
	if not arg_10_1 or type(arg_10_1) ~= "string" then
		return false
	end

	if not arg_10_0 or arg_10_0 == nil then
		return false
	end

	local slot_10_0 = string.lower(arg_10_1)

	if arg_10_0.buff and arg_10_0.buff[slot_10_0] and arg_10_0.buff[slot_10_0].endTime and arg_10_0.buff[slot_10_0].endTime >= game.time then
		return true
	end
end

function ove_0_10.HasBuffOfType(arg_11_0, arg_11_1)
	if not arg_11_1 or type(arg_11_1) ~= "number" then
		return false
	end

	if not arg_11_0 or arg_11_0 == nil then
		return false
	end

	if arg_11_0.buff and arg_11_0.buff[arg_11_1] and arg_11_0.buff[arg_11_1].endTime and arg_11_0.buff[arg_11_1].endTime >= game.time then
		return true
	end
end

function ove_0_10.GetBuff(arg_12_0, arg_12_1)
	if not arg_12_1 or type(arg_12_1) ~= "string" and type(arg_12_1) ~= "number" then
		return nil
	end

	if not arg_12_0 or arg_12_0 == nil then
		return nil
	end

	if type(arg_12_1) == "string" then
		local slot_12_0 = string.lower(arg_12_1)

		if arg_12_0.buff and arg_12_0.buff[slot_12_0] and arg_12_0.buff[slot_12_0].endTime and arg_12_0.buff[slot_12_0].endTime >= game.time then
			return arg_12_0.buff[slot_12_0]
		end
	elseif type(arg_12_1) == "number" and arg_12_0.buff and arg_12_0.buff[arg_12_1] and arg_12_0.buff[arg_12_1].endTime and arg_12_0.buff[arg_12_1].endTime >= game.time then
		return arg_12_0.buff[arg_12_1]
	end
end

function ove_0_10.GetBuffCount(arg_13_0, arg_13_1)
	if not arg_13_1 or type(arg_13_1) ~= "string" then
		return 0
	end

	if not arg_13_0 or arg_13_0 == nil then
		return 0
	end

	local slot_13_0 = string.lower(arg_13_1)

	if arg_13_0.buff and arg_13_0.buff[slot_13_0] and arg_13_0.buff[slot_13_0].endTime and arg_13_0.buff[slot_13_0].endTime >= game.time then
		if arg_13_0.buff[slot_13_0].stacks2 and arg_13_0.buff[slot_13_0].stacks2 > 0 then
			return arg_13_0.buff[slot_13_0].stacks2
		end

		if arg_13_0.buff[slot_13_0].stacks and arg_13_0.buff[slot_13_0].stacks > 0 then
			return arg_13_0.buff[slot_13_0].stacks
		end
	end

	return 0
end

objManager.loop(function(arg_14_0)
	if arg_14_0 and arg_14_0.type == TYPE_HERO then
		table.insert(ove_0_11, arg_14_0)

		return
	end
end)
cb.add(cb.tick, ove_0_17)

return ove_0_10

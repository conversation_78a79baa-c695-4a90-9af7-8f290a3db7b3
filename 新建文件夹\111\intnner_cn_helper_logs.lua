
local ove_0_10 = {}

ove_0_10.level_normal = 0
ove_0_10.level_warn = 1
ove_0_10.level_fatal = 3

function ove_0_10.dump(arg_5_0)
	if type(arg_5_0) == "table" then
		local slot_5_0 = "{ "

		for iter_5_0, iter_5_1 in pairs(arg_5_0) do
			if type(iter_5_0) ~= "number" then
				iter_5_0 = "\"" .. iter_5_0 .. "\""
			end

			slot_5_0 = slot_5_0 .. "[" .. iter_5_0 .. "] = " .. ove_0_10.dump(iter_5_1) .. ","
		end

		return slot_5_0 .. "} "
	else
		return tostring(arg_5_0)
	end
end

function ove_0_10.print(arg_6_0, arg_6_1, arg_6_2, arg_6_3)
	arg_6_3 = arg_6_3 or false

	if type(arg_6_0) == "table" then
		arg_6_0 = ove_0_10.dump(arg_6_0)
	end

	local slot_6_0 = console ~= nil
	local slot_6_1

	if arg_6_1 == ove_0_10.level_normal then
		slot_6_1 = "[ IntAIO. v" .. header.version .. " ]: Clear: " .. tostring(arg_6_0) .. (arg_6_3 and "\n" or "")

		if slot_6_0 then
			console.set_color(2)
		end
	elseif arg_6_1 == ove_0_10.level_warn then
		slot_6_1 = "[ IntAIO. v" .. header.version .. " ]: Warning: " .. tostring(arg_6_0) .. (arg_6_3 and "\n" or "")

		if slot_6_0 then
			console.set_color(6)
		end
	elseif arg_6_1 == ove_0_10.level_fatal then
		if slot_6_0 then
			console.set_color(4)
		end

		slot_6_1 = "[ IntAIO. v" .. header.version .. " ]: Error: " .. tostring(arg_6_0) .. (arg_6_3 and "\n" or "")
	else
		console.set_color(74)

		slot_6_1 = "[ IntAIO. v" .. header.version .. " ]: Fatal: " .. tostring(arg_6_0) .. (arg_6_3 and "\n" or "")
	end

	print(slot_6_1)

	if arg_6_2 then
		chat.add(slot_6_1)
		chat.print()
	end

	if slot_6_0 then
		console.set_color(0)
	end
end

return ove_0_10



local ove_0_10 = module.load("<PERSON>", "common/common")
local ove_0_11 = module.load("<PERSON>", "KSante/damage")
local ove_0_12 = module.load("<PERSON>", "<PERSON>Sante/helper")
local ove_0_13 = module.load("<PERSON>", "KSante/menu")
local ove_0_14 = {
	last = 0,
	slot = player:spellSlot(_R),
	range = ove_0_10.GetAARange,
	result = {}
}

function ove_0_14.is_ready()
	return ove_0_14.slot.state == 0
end

function ove_0_14.allies_endPoint(arg_6_0)
	if not arg_6_0 then
		return 0
	end

	local slot_6_0 = 0

	for iter_6_0, iter_6_1 in pairs(ove_0_10.GetAllyHeroes()) do
		if iter_6_1 and ove_0_10.isValidTarget(iter_6_1) then
			local slot_6_1 = player.pos + (arg_6_0.pos - player.pos):norm() * (ove_0_10.GetDistance(player.pos, arg_6_0.pos) + 350)

			if navmesh.isWall(slot_6_1) then
				local slot_6_2 = arg_6_0.pos + (slot_6_1 - arg_6_0.pos):norm() * (ove_0_10.GetDistance(slot_6_1, arg_6_0.pos) + 350)

				if ove_0_10.GetDistance(slot_6_2, iter_6_1.path.serverPos) < 750 and not ove_0_10.isUnderEnemyTurret(slot_6_2, 950) then
					slot_6_0 = slot_6_0 + 1
				end
			end
		end
	end

	return slot_6_0
end

function ove_0_14.cast_r()
	local slot_7_0 = ove_0_10.GetTarget(450)

	if slot_7_0 and ove_0_10.isValidTarget(slot_7_0) and ove_0_10.GetDistance(player.pos, slot_7_0.pos) <= 350 and ove_0_10.GetPercentHealth(slot_7_0) >= ove_0_13.combo.r.dont:get() then
		local slot_7_1 = ove_0_14.allies_endPoint(slot_7_0)

		if slot_7_1 ~= 0 and slot_7_1 >= ove_0_13.combo.r.endPos:get() then
			player:castSpell("obj", _R, slot_7_0)
		elseif ove_0_13.combo.r.damage_total:get() and ove_0_11.GetTotalDamage(slot_7_0) >= ove_0_10.GetShieldedHealth("AD", slot_7_0) then
			local slot_7_2 = player.pos + (slot_7_0.pos - player.pos):norm() * (ove_0_10.GetDistance(player.pos, slot_7_0.pos) + 350)

			if not ove_0_10.isUnderEnemyTurret(slot_7_2, 950) then
				player:castSpell("obj", _R, slot_7_0)
			end
		else
			local slot_7_3 = player.pos + (slot_7_0.pos - player.pos):norm() * (ove_0_10.GetDistance(player.pos, slot_7_0.pos) + 350)

			if navmesh.isWall(slot_7_3) then
				local slot_7_4 = slot_7_0.pos + (slot_7_3 - slot_7_0.pos):norm() * (ove_0_10.GetDistance(slot_7_3, slot_7_0.pos) + 350)

				if ove_0_10.IsUnderAllyTurret(slot_7_4, 950) then
					player:castSpell("obj", _R, slot_7_0)
				end
			end
		end
	end
end

return ove_0_14



local ove_0_10 = module.internal("TS")
local ove_0_11 = module.internal("orb")
local ove_0_12 = module.internal("pred")
local ove_0_13 = "/menu"
local ove_0_14 = hanbot.path .. "/saves/hanbot_core.ini"
local ove_0_15 = io.open(ove_0_14, "r")

local caidan = hanbot and hanbot.language == 1
ove_0_13 = caidan  and   "/menucn" or   "/menu"

local ove_0_16 = module.load(header.id,  player.charName .. ove_0_13)
local ove_0_17 = module.load(header.id,  player.charName .. "/misc")
local ove_0_18
local ove_0_19

ove_0_19 = graphics.width <= 1920 and graphics.height <= 1080 or false

local ove_0_20 = 0

local function ove_0_21()
	-- function 5
	if game.time < ove_0_20 + network.latency + player:basicAttack(0).windUpTime or ove_0_11.core.can_attack() then
		return
	end

	ove_0_11.core.reset()

	ove_0_20 = game.time
end

local ove_0_22 = {
	ItemTitanicHydraCleave = true,
	ItemTiamatCleave = true
}
local ove_0_23 = {
	speed = 1700,
	delay = 0,
	boundingRadiusMod = 1,
	width = player.boundingRadius,
	collision = {
		minion = false,
		hero = false
	}
}
local ove_0_24 = {
	speed = 1700,
	delay = 0,
	boundingRadiusMod = 1,
	width = player.boundingRadius,
	collision = {
		minion = true,
		hero = true
	}
}
local ove_0_25 = {
	speed = 2000,
	boundingRadiusMod = 0,
	width = 100,
	delay = player:basicAttack(0).clientWindUpTime,
	collision = {
		minion = false,
		hero = false
	}
}
local ove_0_26 = {
	speed = 2000,
	boundingRadiusMod = 0,
	width = 100,
	delay = player:basicAttack(0).clientWindUpTime,
	collision = {
		minion = true,
		hero = true
	}
}

local function ove_0_27(arg_6_0, arg_6_1, arg_6_2)
	-- function 6
	if arg_6_2 > 2500 then
		return
	end

	if arg_6_1 and arg_6_1.isVisible and arg_6_2 and arg_6_1.x and arg_6_1.y and arg_6_1.z and arg_6_1.path then
		local slot_6_0 = ove_0_12.linear.get_prediction(ove_0_23, arg_6_1)

		if player:spellSlot(0).state == 0 and not player.buff.viqlaunch and arg_6_2 and arg_6_2 <= 800 + arg_6_1.boundingRadius or player.buff.viqlaunch and slot_6_0 and slot_6_0.endPos and slot_6_0.startPos:dist(slot_6_0.endPos) <= 800 then
			arg_6_0.obj = arg_6_1

			return true
		elseif player:spellSlot(3).state == 0 and ove_0_16.combo.R.r:get() and (ove_0_16.combo.R.Rlist:get() and ove_0_16.combo.R.list[arg_6_1.charName]:get() or not ove_0_16.combo.R.Rlist:get()) and arg_6_2 and arg_6_2 <= 800 then
			arg_6_0.obj = arg_6_1

			return true
		elseif ove_0_11.combat.target then
			arg_6_0.obj = ove_0_11.combat.target

			return true
		elseif player:spellSlot(2).state == 0 and arg_6_1 and player.pos:dist(arg_6_1) <= player.attackRange + player.boundingRadius + arg_6_1.boundingRadius + 50 then
			arg_6_0.obj = ove_0_11.combat.target

			return true
		end
	end
end

local function ove_0_28()
	-- function 7
	return ove_0_11.ts.get_result(ove_0_27).obj
end

local function ove_0_29(arg_8_0)
	-- function 8
	if not arg_8_0 and ove_0_11.core.is_move_paused() then
		ove_0_11.core.set_pause_move(0)
	elseif arg_8_0 then
		ove_0_11.core.set_pause_move(player:basicAttack(0).clientAnimationTime)
		player:move(vec3(arg_8_0))
	end
end

local function ove_0_30(arg_9_0, arg_9_1, arg_9_2)
	-- function 9
	if ove_0_12.trace.linear.hardlock(ove_0_23, arg_9_0, arg_9_1) then
		return true
	end

	if ove_0_12.trace.linear.hardlockmove(ove_0_23, arg_9_0, arg_9_1) then
		return true
	end

	if arg_9_0.startPos:dist(arg_9_0.endPos) < arg_9_2 - 50 or arg_9_0.startPos:dist(arg_9_0.endPos) < 600 then
		return true
	end

	if ove_0_12.trace.newpath(arg_9_1, 0.033, 0.55) then
		return true
	end
end

local function ove_0_31(arg_10_0)
	-- function 10
	if player:spellSlot(0).state ~= 0 or not arg_10_0 or not ove_0_17.isValid(arg_10_0) or not player.buff.viqlaunch or not ove_0_11.core.can_action() then
		return false
	end

	local slot_10_0 = game.time - player.buff.viqlaunch.startTime
	local slot_10_1 = ove_0_17.QRange(slot_10_0)

	if slot_10_1 then
		local slot_10_2 = ove_0_12.linear.get_prediction(ove_0_23, arg_10_0)

		if slot_10_2 and slot_10_2.endPos and slot_10_2.startPos:distSqr(slot_10_2.endPos) < (slot_10_1 + arg_10_0.boundingRadius)^2 and ove_0_30(slot_10_2, arg_10_0, slot_10_1 + arg_10_0.boundingRadius) then
			player:castSpell("release", 0, vec3(slot_10_2.endPos.x, arg_10_0.pos.y, slot_10_2.endPos.y))
		end
	end
end

local function ove_0_32(arg_11_0)
	-- function 11
	if player:spellSlot(0).state ~= 0 or not arg_11_0 or not ove_0_17.isValid(arg_11_0) or not player.buff.viqlaunch or not ove_0_11.core.can_action() then
		return false
	end

	local slot_11_0 = ove_0_12.linear.get_prediction(ove_0_24, arg_11_0)

	if slot_11_0 and slot_11_0.endPos and slot_11_0.startPos:distSqr(slot_11_0.endPos) < 640000 then
		local slot_11_1 = ove_0_12.collision.get_prediction(ove_0_24, slot_11_0)

		if slot_11_1 and #slot_11_1 > 1 or game.time - player.buff.viqlaunch.startTime > 3 and not ove_0_17.UnderTurret(vec3(slot_11_0.endPos.x, arg_11_0.pos.y, slot_11_0.endPos.y)) then
			player:castSpell("release", 0, vec3(slot_11_0.endPos.x, arg_11_0.pos.y, slot_11_0.endPos.y))
		end
	end
end

local function ove_0_33(arg_12_0)
	-- function 12
	if player:spellSlot(0).state ~= 0 or not arg_12_0 or not ove_0_17.isValid(arg_12_0) or player.buff.viqlaunch or not ove_0_11.core.can_action() then
		return false
	end

	local slot_12_0 = ove_0_12.linear.get_prediction(ove_0_24, arg_12_0)

	if slot_12_0 and slot_12_0.endPos and slot_12_0.startPos:distSqr(slot_12_0.endPos) < 640000 then
		local slot_12_1 = ove_0_12.collision.get_prediction(ove_0_24, slot_12_0)

		if slot_12_1 and #slot_12_1 > 2 then
			player:castSpell("pos", 0, vec3(mousePos.x, mousePos.y, mousePos.z))
		end
	end
end

local function ove_0_34(arg_13_0)
	-- function 13
	if player:spellSlot(0).state ~= 0 or not arg_13_0 or not ove_0_17.isValid(arg_13_0) or not player.buff.viqlaunch or not ove_0_11.core.can_action() then
		return false
	end

	local slot_13_0 = game.time - player.buff.viqlaunch.startTime
	local slot_13_1 = ove_0_17.QRange(slot_13_0)

	if slot_13_1 then
		local slot_13_2 = ove_0_12.linear.get_prediction(ove_0_23, arg_13_0)

		if slot_13_2 and slot_13_2.endPos and slot_13_2.startPos:distSqr(slot_13_2.endPos) < (slot_13_1 + arg_13_0.boundingRadius)^2 and not ove_0_17.UnderTurret(vec3(slot_13_2.endPos.x, arg_13_0.pos.y, slot_13_2.endPos.y)) then
			player:castSpell("release", 0, vec3(slot_13_2.endPos.x, arg_13_0.pos.y, slot_13_2.endPos.y))
		end
	end
end

local function ove_0_35()
	-- function 14
	if not ove_0_11.core.is_paused() then
		ove_0_11.core.set_pause(player:basicAttack(0).clientWindUpTime + network.latency)
	end
end

local function ove_0_36()
	-- function 15
	ove_0_11.core.set_pause(0)
end

local function ove_0_37(arg_16_0)
	-- function 16
	if player:spellSlot(2).state ~= 0 or not arg_16_0 or arg_16_0.isDead or not arg_16_0.isVisible or not arg_16_0.isTargetable then
		return
	end

	local slot_16_0 = arg_16_0.path.serverPos + (player.pos - arg_16_0.path.serverPos):norm() * (player.pos:dist(arg_16_0.path.serverPos) - 800)
	local slot_16_1 = {
		startPos = player.path.serverPos2D,
		endPos = vec2(slot_16_0.x, slot_16_0.z)
	}

	if slot_16_0 and slot_16_1.startPos and slot_16_1.endPos then
		local slot_16_2 = ove_0_12.collision.get_prediction(ove_0_26, slot_16_1)

		if slot_16_2 then
			for iter_16_0 = 1, #slot_16_2 do
				local slot_16_3 = slot_16_2[iter_16_0]

				if slot_16_3 and (slot_16_3.type == TYPE_HERO or slot_16_3.type == TYPE_MINION and #slot_16_2 >= ove_0_16.laneclear.ehit:get()) then
					player:castSpell("self", 2)
					ove_0_35()
					player:attack(arg_16_0)
				end
			end
		end
	end

	return false
end

local function ove_0_38(arg_17_0)
	-- function 17
	if player:spellSlot(2).state ~= 0 or not arg_17_0 or arg_17_0.isDead or not arg_17_0.isVisible or not arg_17_0.isTargetable then
		return
	end

	local slot_17_0 = ove_0_12.linear.get_prediction(ove_0_25, arg_17_0)
	local slot_17_1 = objManager.minions

	if slot_17_0 and slot_17_0.endPos and slot_17_0.startPos:dist(slot_17_0.endPos) < 800 then
		local slot_17_2 = vec3(player.path.serverPos.x, player.path.serverPos.y, player.path.serverPos.z)
		local slot_17_3 = vec3(slot_17_0.endPos.x, arg_17_0.y, slot_17_0.endPos.y)

		for iter_17_0 = 0, slot_17_1.size[TEAM_NEUTRAL] - 1 do
			local slot_17_4 = slot_17_1[TEAM_NEUTRAL][iter_17_0]

			if slot_17_4 and not slot_17_4.isDead and slot_17_4.isVisible and slot_17_4.isTargetable and slot_17_4.pos:distSqr(player.pos) < (player.attackRange + player.boundingRadius + slot_17_4.boundingRadius + 50)^2 and not ove_0_17.WardName(slot_17_4) and slot_17_2 and slot_17_2:distSqr(slot_17_3) > slot_17_4.path.serverPos:distSqr(slot_17_3) and slot_17_2:distSqr(slot_17_3) > slot_17_2:distSqr(slot_17_4.path.serverPos) and mathf.dist_line_vector(slot_17_2, slot_17_3, slot_17_4.path.serverPos) <= 100 then
				player:castSpell("self", 2)
				ove_0_35()
				player:attack(slot_17_4)
			end
		end

		for iter_17_1 = 0, slot_17_1.size[TEAM_ENEMY] - 1 do
			local slot_17_5 = slot_17_1[TEAM_ENEMY][iter_17_1]

			if slot_17_5 and not slot_17_5.isDead and slot_17_5.isVisible and slot_17_5.isTargetable and slot_17_5.pos:distSqr(player.pos) < (player.attackRange + player.boundingRadius + slot_17_5.boundingRadius + 50)^2 and not ove_0_17.WardName(slot_17_5) and slot_17_2 and slot_17_2:distSqr(slot_17_3) > slot_17_5.path.serverPos:distSqr(slot_17_3) and slot_17_2:distSqr(slot_17_3) > slot_17_2:distSqr(slot_17_5.path.serverPos) and mathf.dist_line_vector(slot_17_2, slot_17_3, slot_17_5.path.serverPos) <= 100 then
				player:castSpell("self", 2)
				ove_0_35()
				player:attack(slot_17_5)
			end
		end

		for iter_17_2 = 0, objManager.enemies_n - 1 do
			local slot_17_6 = objManager.enemies[iter_17_2]

			if slot_17_6 and ove_0_17.isValid(slot_17_6) and slot_17_6 ~= arg_17_0 and slot_17_6.team ~= TEAM_ALLY and slot_17_6.pos:distSqr(player.pos) < (player.attackRange + player.boundingRadius + slot_17_6.boundingRadius + 50)^2 and slot_17_2 and slot_17_2:distSqr(slot_17_3) > slot_17_6.path.serverPos:distSqr(slot_17_3) and slot_17_2:distSqr(slot_17_3) > slot_17_2:distSqr(slot_17_6.path.serverPos) and mathf.dist_line_vector(slot_17_2, slot_17_3, slot_17_6.path.serverPos) <= 100 then
				player:castSpell("self", 2)
				ove_0_35()
				player:attack(slot_17_6)
			end
		end
	end
end

local ove_0_39 = {
	[6630] = true,
	[6631] = true,
	[6029] = true
}

local function ove_0_40(arg_18_0)
	-- function 18
	if not arg_18_0 or not ove_0_17.isValid(arg_18_0) then
		return false
	end

	for iter_18_0 = 6, 11 do
		local slot_18_0 = player:spellSlot(iter_18_0)

		if slot_18_0.isNotEmpty and slot_18_0.name and player:spellSlot(iter_18_0).state == 0 then
			if arg_18_0 and arg_18_0.pos2D:distSqr(player.pos2D) <= 57600 and player:spellSlot(2).state ~= 0 and not ove_0_11.core.can_attack() and ove_0_11.core.can_action() and ove_0_39[player:itemID(iter_18_0 - 6)] then
				if player:itemID(iter_18_0 - 6) == 6631 and arg_18_0 then
					player:castSpell("pos", iter_18_0, arg_18_0.path.serverPos)
				else
					player:castSpell("self", iter_18_0)
				end
			end

			if arg_18_0 and arg_18_0.pos2D:distSqr(player.pos2D) <= 57600 and ove_0_16.jungleclear.ee:get() > 0 and (not arg_18_0.buff.viwproc or arg_18_0.buff.viwproc and arg_18_0.buff.viwproc.stacks < ove_0_16.jungleclear.ee:get()) and not ove_0_11.core.can_attack() and ove_0_11.core.can_action() and ove_0_39[player:itemID(iter_18_0 - 6)] then
				if player:itemID(iter_18_0 - 6) == 6631 and arg_18_0 then
					player:castSpell("pos", iter_18_0, arg_18_0.path.serverPos)
				else
					player:castSpell("self", iter_18_0)
				end
			end
		end
	end
end

local function ove_0_41(arg_19_0)
	-- function 19
	if not arg_19_0 or not ove_0_17.isValid(arg_19_0) then
		return false
	end

	for iter_19_0 = 6, 11 do
		local slot_19_0 = player:spellSlot(iter_19_0)

		if slot_19_0.isNotEmpty and slot_19_0.name and player:spellSlot(iter_19_0).state == 0 then
			if ove_0_11.combat.target and player:spellSlot(2).state ~= 0 and not ove_0_11.core.can_attack() and ove_0_11.core.can_action() and ove_0_39[player:itemID(iter_19_0 - 6)] then
				if player:itemID(iter_19_0 - 6) == 6631 and arg_19_0 then
					player:castSpell("pos", iter_19_0, arg_19_0.path.serverPos)
				else
					player:castSpell("self", iter_19_0)
				end
			end

			if ove_0_11.combat.target and ove_0_16.combo.ee:get() > 0 and (not ove_0_11.combat.target.buff.viwproc or ove_0_11.combat.target.buff.viwproc and ove_0_11.combat.target.buff.viwproc.stacks < ove_0_16.combo.ee:get()) and not ove_0_11.core.can_attack() and ove_0_11.core.can_action() and ove_0_39[player:itemID(iter_19_0 - 6)] then
				if player:itemID(iter_19_0 - 6) == 6631 and arg_19_0 then
					player:castSpell("pos", iter_19_0, arg_19_0.path.serverPos)
				else
					player:castSpell("self", iter_19_0)
				end
			end

			if slot_19_0.name == "YoumusBlade" then
				player:castSpell("self", iter_19_0)
			elseif slot_19_0.name == "ItemSwordOfFeastAndFamine" then
				player:castSpell("obj", iter_19_0, arg_19_0)
			elseif slot_19_0.name == "BilgewaterCutlass" then
				player:castSpell("obj", iter_19_0, arg_19_0)
			end
		end
	end
end

local function ove_0_42()
	-- function 20
	if not ove_0_18 or not ove_0_17.isValid(ove_0_18) then
		return
	end

	ove_0_41(ove_0_18)

	if not player.buff.viqlaunch and ove_0_11.core.can_action() and player:spellSlot(0).state == 0 then
		if ove_0_16.combo.q:get() and player.pos:distSqr(ove_0_18.path.serverPos) < 640000 and not player.buff.viqlaunch and ove_0_11.core.can_action() then
			player:castSpell("pos", 0, vec3(mousePos.x, mousePos.y, mousePos.z))
		end
	elseif player.buff.viqlaunch and ove_0_11.core.can_action() and player:spellSlot(0).state == 0 then
		ove_0_31(ove_0_18)
	end

	if not player.buff.viqlaunch and ove_0_11.core.can_action() then
		if ove_0_16.combo.e:get() and player:spellSlot(2).state == 0 and not ove_0_11.core.can_attack() and player.pos:dist(ove_0_18) <= player.attackRange + player.boundingRadius + ove_0_18.boundingRadius + 50 and (ove_0_16.combo.ee:get() == 0 or ove_0_18.buff.viwproc and ove_0_18.buff.viwproc.stacks >= ove_0_16.combo.ee:get() or player:spellSlot(1).level < 1) and (player:spellSlot(0).state == 0 or not ove_0_16.harass.q:get()) then
			player:castSpell("self", 2)
			ove_0_21()
		end

		if ove_0_16.combo.R.r:get() and not player.path.isDashing then
			if ove_0_16.combo.R.Rlist:get() and ove_0_16.combo.R.list[ove_0_18.charName]:get() then
				if ove_0_16.combo.R.rhp:get() then
					local slot_20_0 = player:spellSlot(0).state == 0 and ove_0_17.QDmg(ove_0_18) or 0
					local slot_20_1 = player:spellSlot(2).state == 0 and ove_0_17.EDmg(ove_0_18) or ove_0_17.AADmg(ove_0_18)
					local slot_20_2 = player:spellSlot(3).state == 0 and ove_0_17.RDmg(ove_0_18) or 0

					if ove_0_18.health <= slot_20_0 + slot_20_1 + slot_20_2 then
						player:castSpell("obj", 3, ove_0_18)
					end
				elseif ove_0_18.path.serverPos:dist(player.path.serverPos) > player.attackRange + player.boundingRadius + ove_0_18.boundingRadius + 50 then
					player:castSpell("obj", 3, ove_0_18)
				end
			elseif not ove_0_16.combo.R.Rlist:get() then
				player:castSpell("obj", 3, ove_0_18)
			end
		end
	end
end

local function ove_0_43()
	-- function 21
	if not ove_0_18 or not ove_0_17.isValid(ove_0_18) or player.mana * 100 / player.maxMana < ove_0_16.harass.minimana:get() and not player.buff.viqlaunch and ove_0_11.core.can_action() then
		return
	end

	if not player.buff.viqlaunch and ove_0_11.core.can_action() and player:spellSlot(0).state == 0 and not ove_0_17.UnderTurret(ove_0_18.pos) then
		if ove_0_16.harass.q:get() and player.pos:distSqr(ove_0_18.path.serverPos) < 640000 and not player.buff.viqlaunch and ove_0_11.core.can_action() then
			player:castSpell("pos", 0, vec3(mousePos.x, mousePos.y, mousePos.z))
		end
	elseif player.buff.viqlaunch and ove_0_11.core.can_action() and player:spellSlot(0).state == 0 then
		ove_0_31(ove_0_18)
	end

	if not player.buff.viqlaunch and ove_0_11.core.can_action() then
		if ove_0_16.harass.e:get() and ove_0_16.harass.e2:get() and ove_0_11.core.can_action() and player.pos:dist(ove_0_18) > player.attackRange + player.boundingRadius + ove_0_18.boundingRadius + 50 and (player:spellSlot(0).state == 0 or not ove_0_16.harass.q:get()) then
			ove_0_38(ove_0_18)
		end

		if ove_0_16.harass.e:get() and player:spellSlot(2).state == 0 and not ove_0_11.core.can_attack() and (not ove_0_16.harass.savee:get() or ove_0_16.harass.savee:get() and player:spellSlot(2).stacks > 1) and player.pos:dist(ove_0_18) <= player.attackRange + player.boundingRadius + ove_0_18.boundingRadius + 50 and (ove_0_16.harass.ee:get() == 0 or ove_0_18.buff.viwproc and ove_0_18.buff.viwproc.stacks >= ove_0_16.harass.ee:get() or player:spellSlot(1).level < 1) and (player:spellSlot(0).state == 0 or not ove_0_16.harass.q:get()) then
			player:castSpell("self", 2)
			ove_0_21()
		end
	end
end

local function ove_0_44()
	-- function 22
	local slot_22_0 = objManager.minions

	for iter_22_0 = 0, slot_22_0.size[TEAM_NEUTRAL] - 1 do
		local slot_22_1 = slot_22_0[TEAM_NEUTRAL][iter_22_0]

		if slot_22_1 and not slot_22_1.isDead and slot_22_1.isVisible and slot_22_1.isTargetable and slot_22_1.pos:distSqr(player.pos) < 640000 and not ove_0_17.WardName(slot_22_1) and slot_22_1.name and not slot_22_1.name:lower():find("beakmini") then
			if not player.buff.viqlaunch and (not ove_0_16.jungleclear.savee:get() or ove_0_16.jungleclear.savee:get() and player:spellSlot(2).stacks > 1) and slot_22_1.pos:distSqr(player.pos) < (player.attackRange + player.boundingRadius + slot_22_1.boundingRadius + 50)^2 and not ove_0_11.core.can_attack() and ove_0_11.core.can_action() and ove_0_16.jungleclear.e:get() and player:spellSlot(2).state == 0 and player.mana * 100 / player.maxMana >= ove_0_16.jungleclear.minimana:get() then
				ove_0_40(slot_22_1)

				if (ove_0_16.jungleclear.ee:get() == 0 or slot_22_1.buff.viwproc and slot_22_1.buff.viwproc.stacks >= ove_0_16.jungleclear.ee:get() or player:spellSlot(1).level < 1) and player:spellSlot(2).state == 0 and (player:spellSlot(0).state == 0 or not ove_0_16.harass.q:get()) then
					player:castSpell("self", 2)
					ove_0_21()
				end
			end

			if ove_0_16.jungleclear.q:get() and player:spellSlot(0).state == 0 and (player.mana * 100 / player.maxMana >= ove_0_16.jungleclear.minimana:get() or player.buff.viqlaunch) then
				if not player.buff.viqlaunch and ove_0_11.core.can_action() then
					player:castSpell("pos", 0, vec3(mousePos.x, mousePos.y, mousePos.z))
				elseif player.buff.viqlaunch then
					ove_0_34(slot_22_1)
				end
			end
		end
	end

	if ove_0_16.laneclear.notskill:get() and ove_0_17.CountEnemiesNear(player, 925) > 0 or player.mana * 100 / player.maxMana < ove_0_16.laneclear.minimana:get() then
		return
	end

	for iter_22_1 = 0, slot_22_0.size[TEAM_ENEMY] - 1 do
		local slot_22_2 = slot_22_0[TEAM_ENEMY][iter_22_1]

		if slot_22_2 and not slot_22_2.isDead and slot_22_2.isVisible and slot_22_2.isTargetable and slot_22_2.pos:distSqr(player.pos) < 640000 and not ove_0_17.UnderTurret(slot_22_2.pos) and not ove_0_17.WardName(slot_22_2) then
			if not player.buff.viqlaunch and ove_0_16.laneclear.q:get() and ove_0_11.core.can_action() then
				ove_0_33(slot_22_2)
			elseif player.buff.viqlaunch and ove_0_16.laneclear.q:get() and ove_0_11.core.can_action() then
				local slot_22_3 = game.time - player.buff.viqlaunch.startTime

				if ove_0_17.QRange(slot_22_3) >= 800 then
					if slot_22_2.health <= ove_0_17.QDmg(slot_22_2) then
						ove_0_34(slot_22_2)
					else
						ove_0_32(slot_22_2)
					end
				end
			end

			if (not ove_0_16.laneclear.savee:get() or ove_0_16.laneclear.savee:get() and player:spellSlot(2).stacks > 1) and ove_0_16.laneclear.e:get() and slot_22_2.pos:distSqr(player.pos) <= (player.attackRange + player.boundingRadius + slot_22_2.boundingRadius + 50)^2 and not ove_0_11.core.can_attack() and ove_0_11.core.can_action() then
				ove_0_37(slot_22_2)
			end
		end
	end
end

local function ove_0_45(arg_23_0)
	-- function 23
	if arg_23_0 and arg_23_0.name and arg_23_0.owner and arg_23_0.owner == player and (arg_23_0.name == "ViE" or arg_23_0.name == "ItemTitanicHydraCleave") then
		ove_0_21()
	end
end

local function ove_0_46()
	-- function 24
	for iter_24_0 = 0, objManager.enemies_n - 1 do
		local slot_24_0 = objManager.enemies[iter_24_0]

		if slot_24_0 and ove_0_17.isValid(slot_24_0) and not slot_24_0.buff.willrevive and not slot_24_0.buff.undyingrage and not slot_24_0.buff.sionpassivezombie and slot_24_0.team ~= TEAM_ALLY and not slot_24_0.buff.willrevive and ove_0_16.kill.enable:get() then
			if ove_0_16.kill.q:get() and player:spellSlot(0).state == 0 and player.buff.viqlaunch then
				local slot_24_1 = slot_24_0.health + slot_24_0.physicalShield
				local slot_24_2 = game.time - player.buff.viqlaunch.startTime

				if slot_24_1 < ove_0_17.QQDmg(slot_24_0, slot_24_2) then
					ove_0_31(slot_24_0)
				end
			elseif ove_0_16.kill.e:get() and player:spellSlot(2).state == 0 and not player.buff.viqlaunch and slot_24_0.pos:distSqr(player.pos) > (player.attackRange + player.boundingRadius + slot_24_0.boundingRadius + 50)^2 and slot_24_0.pos:distSqr(player.pos) <= 6400 and slot_24_0.health <= ove_0_17.EDmg(slot_24_0) and (player:spellSlot(0).state ~= 0 or not ove_0_16.kill.q:get()) then
				ove_0_38(slot_24_0)
			end
		end
	end
end

local function ove_0_47()
	-- function 25
	if player.isDead then
		return
	end

	ove_0_18 = ove_0_28()

	ove_0_46()

	if ove_0_11.menu.combat.key:get() then
		ove_0_42()
	elseif ove_0_11.menu.hybrid.key:get() then
		ove_0_43()
	elseif ove_0_11.menu.lane_clear.key:get() then
		ove_0_44()
	end

	if player.buff.viqlaunch then
		ove_0_11.core.set_pause_attack(6)
	end

	if ove_0_11.core.is_attack_paused() and not player.buff.viqlaunch then
		ove_0_11.core.set_pause_attack(0)
	end
end

local function ove_0_48()
	-- function 26
	if not ove_0_16.display.Enable:get() or player.isDead then
		return
	end

	if ove_0_16.display.Target:get() and ove_0_18 and ove_0_18.isVisible and not ove_0_18.isDead and ove_0_18.isTargetable and ove_0_18.isOnScreen then
		graphics.draw_circle_xyz(ove_0_18.x, ove_0_18.y, ove_0_18.z, 100, 2, **********, 100)
	end

	if ove_0_16.display.Q:get() and player.isOnScreen then
		if player.buff.viqlaunch then
			local slot_26_0 = game.time - player.buff.viqlaunch.startTime
			local slot_26_1 = ove_0_17.QRange(slot_26_0)

			if slot_26_1 then
				graphics.draw_circle_xyz(player.x, player.y, player.z, slot_26_1, 2, **********, 100)
			end
		elseif player:spellSlot(0).state == 0 then
			graphics.draw_circle_xyz(player.x, player.y, player.z, 800, 2, **********, 100)
		else
			graphics.draw_circle_xyz(player.x, player.y, player.z, 800, 1, 4287071366, 100)
		end
	end

	if ove_0_16.display.R:get() and (not ove_0_16.display.Q:get() or player.buff.viqlaunch) and player:spellSlot(3).state == 0 and player.isOnScreen then
		graphics.draw_circle_xyz(player.x, player.y, player.z, 800, 1, 4293137101, 100)
	end

	if ove_0_16.display.Combo:get() then
		for iter_26_0 = 0, objManager.enemies_n - 1 do
			local slot_26_2 = objManager.enemies[iter_26_0]

			if slot_26_2 and not slot_26_2.isDead and slot_26_2.isVisible and slot_26_2.team and slot_26_2.type and slot_26_2.team == TEAM_ENEMY and slot_26_2.type == TYPE_HERO and slot_26_2.isOnScreen and slot_26_2.barPos then
				local slot_26_3 = graphics.height > 1080 and graphics.height / 1080 * 0.905 or 1
				local slot_26_4 = vec2(109 * slot_26_3, 111 * slot_26_3)
				local slot_26_5 = vec2(54 * slot_26_3, 11 * slot_26_3)
				local slot_26_6 = slot_26_2.barPos + slot_26_4 + slot_26_5
				local slot_26_7 = slot_26_6.x
				local slot_26_8 = slot_26_6.y
				local slot_26_9 = player:spellSlot(0).state == 0 and ove_0_17.QDmg(slot_26_2) or 0
				local slot_26_10 = player:spellSlot(2).state == 0 and ove_0_17.EDmg(slot_26_2) or ove_0_17.AADmg(slot_26_2)
				local slot_26_11 = player:spellSlot(3).state == 0 and ove_0_17.RDmg(slot_26_2) or 0
				local slot_26_12 = slot_26_2.health - (slot_26_9 + slot_26_10 + slot_26_11)
				local slot_26_13 = slot_26_7 + slot_26_2.health / slot_26_2.maxHealth * 102 * slot_26_3
				local slot_26_14 = slot_26_7 + (slot_26_12 > 0 and slot_26_12 or 0) / slot_26_2.maxHealth * 102 * slot_26_3

				graphics.draw_line_2D(slot_26_13, slot_26_8, slot_26_14, slot_26_8, 10, **********)
			end
		end
	end
end

return {
	get_action = ove_0_47,
	process_spell = ove_0_45,
	update_buff = update_buff,
	remove_buff = remove_buff,
	on_draw = ove_0_48
}

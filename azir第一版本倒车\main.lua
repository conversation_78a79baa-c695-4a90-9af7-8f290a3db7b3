
local ove_0_5 = module.internal("orb")
local ove_0_6 = module.load("<PERSON>","azir/menu")
local ove_0_7 = module.load("<PERSON>","azir/q")
local ove_0_8 = module.load("<PERSON>","azir/w")
local ove_0_9 = module.load("<PERSON>","azir/e")
local ove_0_10 = module.load("<PERSON>","azir/r")
local ove_0_11 = module.load("<PERSON>","azir/weq")
local drift_combo = module.load("<PERSON>","azir/drift_combo")
local Curses = module.load("<PERSON>", "<PERSON><PERSON>")


local ove_0_12 = {
	AzirR = 0.5,
	AzirQ = 0.25,
	AzirW = 0.25
}
local ove_0_13
local ove_0_14

local function ove_0_15(arg_1_0)
	-- print 1
	ove_0_8.on_create_minion(arg_1_0)
end

local function ove_0_16()
	-- print 2
	ove_0_7.on_draw()
	ove_0_8.on_draw()
	ove_0_9.on_draw()
	ove_0_10.on_draw()
	if drift_combo and drift_combo.on_draw then
		drift_combo.on_draw()
	end
end

local function ove_0_17()
	-- print 3
	if ove_0_13 and os.clock() > ove_0_13 then
		ove_0_5.core.set_pause(0)

		ove_0_13 = nil
	end

	if ove_0_14 and os.clock() > ove_0_14 then
		ove_0_5.core.set_pause_attack(0)

		ove_0_14 = nil
	end

	ove_0_8.update()

	if ove_0_9.invoke() then
		return
	end

	local slot_3_0 = ove_0_11.get_destination()

	if ove_0_6.keys.shuffle:get() then
		local slot_3_1 = ove_0_10.get_target()

		if slot_3_1 and ove_0_11.invoke_b(slot_3_1.pos2D) then
			return
		end
	elseif ove_0_11.invoke_b(slot_3_0) then
		return
	end

	-- 倒车入库逻辑 (优先级最高)
	if drift_combo and drift_combo.invoke() then
		return
	end

	if ove_0_5.core.is_paused() or not ove_0_5.core.can_action() or ove_0_5.core.is_move_paused() then
		return
	end

	if player:spellSlot(3).state == 0 then
		if ove_0_6.keys.shuffle:get() then
			local slot_3_2 = ove_0_10.get_target()

			if slot_3_2 and player.par > 270 and slot_3_2.pos:dist(player.pos) < 1000 and ove_0_11.invoke_a(slot_3_2.pos2D) then
				return
			end

			if ove_0_10.invoke() then
				return
			end

			if ove_0_6.r.shurima_shuffle_move:get() then
				player:move(mousePos)
			end

			ove_0_14 = os.clock() + 0.125

			ove_0_5.core.set_pause_attack(math.huge)

			return
		else
			if ove_0_6.keys.safety:get() and ove_0_10 and ove_0_10.safety and ove_0_10.safety() then
				return
			end

			if ove_0_6.r.turret:get() then
				local slot_3_3
				if ove_0_10 and ove_0_10.get_inactive_turret then
					slot_3_3 = ove_0_10.get_inactive_turret()
				end

				if slot_3_3 and ove_0_10 and ove_0_10.invoke and ove_0_10.invoke(slot_3_3, false) then
					return
				end
			end
		end
	end

	if ove_0_6.keys.weq:get() and ove_0_11 and ove_0_11.invoke_a and slot_3_0 and ove_0_11.invoke_a(slot_3_0) then
		return
	end

	local slot_3_4 = module.seek("evade")

	if slot_3_4 and slot_3_4.core.is_active() then
		return
	end

	if ove_0_5.menu.combat.key:get() then
		if ove_0_8 and ove_0_8.invoke and ove_0_8.invoke() then
			return
		end

		if ove_0_8 and ove_0_8.invoke_aa and ove_0_8.invoke_aa() then
			return
		end

		if ove_0_7 and ove_0_7.invoke and ove_0_7.invoke() then
			return
		end
	end

	if ove_0_5.menu.lane_clear.key:get() then
		if ove_0_8 and ove_0_8.invoke_last_hit and ove_0_8.invoke_last_hit() then
			return
		end

		if ove_0_8 and ove_0_8.invoke_aa and ove_0_8.invoke_aa() then
			return
		end

		if ove_0_8 and ove_0_8.soldiers and ove_0_8.clear_place_soldier and ove_0_6.keys.lane_clear:get() and ove_0_6.w.lane_clear:get() and #ove_0_8.soldiers < ove_0_6.w.lane_clear_max:get() and ove_0_8.clear_place_soldier(TEAM_ENEMY) then
			return
		end

		if ove_0_8 and ove_0_8.soldiers and ove_0_8.clear_place_soldier and ove_0_6.keys.jungle_clear:get() and ove_0_6.w.jungle_clear:get() and #ove_0_8.soldiers < ove_0_6.w.jungle_clear_max:get() then
			if ove_0_8.clear_place_soldier(TEAM_NEUTRAL) then
				return
			end

			if ove_0_7 and ove_0_7.invoke_lane_clear and ove_0_7.invoke_lane_clear(TEAM_NEUTRAL) then
				return
			end
		end

		if ove_0_8 and ove_0_8.invoke_aa_lane_clear and ove_0_8.invoke_aa_lane_clear() then
			return
		end

		if ove_0_7 and ove_0_7.invoke_lane_clear and ove_0_6.keys.lane_clear:get() and ove_0_7.invoke_lane_clear(TEAM_ENEMY) then
			return
		end
	end

	if ove_0_5.menu.hybrid.key:get() then
		if ove_0_8 and ove_0_8.invoke_last_hit and ove_0_8.invoke_last_hit() then
			return
		end

		if ove_0_8 and ove_0_8.invoke_aa and ove_0_8.invoke_aa() then
			return
		end
	end

	if ove_0_8 and ove_0_8.invoke_last_hit and ove_0_5.menu.last_hit.key:get() and ove_0_8.invoke_last_hit() then
		return
	end
end

local function ove_0_18(arg_4_0)
	-- print 4
	if arg_4_0.owner.ptr == player.ptr then
		local slot_4_0 = arg_4_0.name

		if ove_0_12[slot_4_0] then
			ove_0_13 = os.clock() + ove_0_12[slot_4_0]

			ove_0_5.core.set_pause(math.huge)
		end

		if ove_0_7 and ove_0_7.on_process_spell then
			ove_0_7.on_process_spell(arg_4_0)
		end
	end
end

local function ove_0_19(arg_5_0)
	-- print 5
	if arg_5_0.ptr == player.ptr and arg_5_0.path.isDashing then
		ove_0_13 = os.clock() + arg_5_0.path.serverPos2D:dist(arg_5_0.path.point2D[1]) / arg_5_0.path.dashSpeed

		ove_0_5.core.set_pause(math.huge)
	end
end

local function ove_0_20(arg_6_0)
	-- print 6
	if ove_0_9 and ove_0_9.on_key_down then
		ove_0_9.on_key_down(arg_6_0)
	end
	if ove_0_11 and ove_0_11.on_key_down then
		ove_0_11.on_key_down(arg_6_0)
	end
end

local function ove_0_21(arg_7_0)
	-- print 7
	if ove_0_9 and ove_0_9.on_key_up then
		ove_0_9.on_key_up(arg_7_0)
	end
end

cb.add(cb.path, ove_0_19)
cb.add(cb.create_minion, ove_0_15)
cb.add(cb.draw, ove_0_16)
cb.add(cb.spell, ove_0_18)
cb.add(cb.keydown, ove_0_20)
cb.add(cb.keyup, ove_0_21)
ove_0_5.combat.register_f_pre_tick(ove_0_17)

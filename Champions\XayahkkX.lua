local r = module.internal("pred");
local f = module.internal("TS");
local l = module.internal("orb");
local E = module.load("<PERSON>", "Utility/kcommon")
local V = module.load("<PERSON>", "Utility/lualinq")
local s = module.load("<PERSON>", "Utility/SpellDatabaseIrelia")
local y = module.seek("evade");
local h = module.load("Brian", "Utility/json");
local d = {
		speed = 1800,
		range = 1100,
		delay = .5,
		boundingRadiusMod = 1,
		width = 50,
		collision = { wall = true },
	};

local x = {
		speed = 4000,
		range = 1100,
		delay = .25,
		boundingRadiusMod = 1,
		width = 35,
		collision = { wall = true },
	};
local t = {
		[0] = "Q",
		"W",
		"E",
		[-1] = "P",
		[3] = "R",
	};
local Y = menu("KornisAIO2" .. player.charName, "Kornis AIO - " .. player.charName);
Y:menu("combo", "Combo");
Y.combo:header("qset", " -- Q Settings --");
Y.combo:boolean("qcombo", "Use Q", true);
Y.combo:boolean("qaa", " ^- Use out of AA Range", false);
Y.combo:header("wset", " -- W Settings --");
Y.combo:boolean("wcombo", "Use W", true);
Y.combo:header("eset", " -- E Settings --");
Y.combo:boolean("ecombo", "Use E", true);
Y.combo:slider("hitse", " ^- if Hits X Feathers", 3, 1, 8, 1);
Y.combo.hitse:set("tooltip", "Disable Auto Q if can Snare in Misc menu!");
Y.combo:header("rset", " ~~~~ ");
Y.combo:keybind("semir", "Semi-R", "T", nil);
Y:menu("harass", "Harass");
Y.harass:header("qset", " -- Q Settings --");
Y.harass:boolean("qcombo", "Use Q", true);
Y.harass:boolean("qaa", " ^- Use out of AA Range", false);
Y.harass:header("wset", " -- W Settings --");
Y.harass:boolean("wcombo", "Use W", true);
Y.harass:header("eset", " -- E Settings --");
Y.harass:boolean("ecombo", "Use E", false);
Y.harass:slider("hitse", " ^- if Hits X Feathers", 3, 1, 5, 1);
Y.harass.hitse:set("tooltip", "Disable Auto Q if can Snare in Misc menu!");
Y:menu("dodgew", "R Dodge");
Y.dodgew:boolean("enablew", "Use R Automatically", true);
Y.dodgew:header("hello", " -- Enemy Skillshots -- ");
if not y then
	Y.dodgew:header("uhh", "Enable 'Premium Evade' to block Skillshots");
end;
if y then
	for r, f in pairs(s) do
		for l, E in pairs(E.GetEnemyHeroes()) do
			if f.charName == E.charName then
				if f.displayname == "" then
					f.displayname = r;
				end;
				if f.danger == 0 then
					f.danger = 1;
				end;
				if Y.dodgew[f.charName] == nil then
					Y.dodgew:menu(f.charName, f.charName);
				end;
				Y.dodgew[f.charName]:menu(f.slot, "" .. (f.charName .. (" | " .. (t[f.slot] or "?"))));
				Y.dodgew[f.charName][f.slot]:boolean("Dodge", "Dodge", false);
				Y.dodgew[f.charName][f.slot]:slider("hp", "HP to Dodge", 70, 1, 100, 5);
			end;
		end;
	end;
end;
Y.dodgew:header("hellow", " -- Targeted Spells -- ");
for r = 1, #E.GetEnemyHeroes(), 1 do
	local f = (E.GetEnemyHeroes())[r];
	local l = string.lower(f.charName);
	if f and (E.TargetedSpells())[l] then
		for r = 1, #(E.TargetedSpells())[l], 1 do
			local V = (E.TargetedSpells())[l][r];
			if Y.dodgew[tostring(f.charName) .. tostring(V.slot)] == nil then
				Y.dodgew:menu(tostring(f.charName) .. tostring(V.slot), "" .. (f.charName .. (" | " .. V.menuslot)));
			end;
			Y.dodgew[tostring(f.charName) .. tostring(V.slot)]:boolean("Dodge", "Dodge", false);
			Y.dodgew[tostring(f.charName) .. tostring(V.slot)]:slider("hp", "HP to Dodge", 80, 1, 100, 5);
		end;
	end;
end;
--d.range = (h.parse(i.data)).Q;
Y:menu("killsteal", "Killsteal");
Y.killsteal:boolean("ksq", "Use Q", true);
Y.killsteal:boolean("kse", "Use E", true);
Y:menu("farming", "Farming");
Y.farming:keybind("toggle", "Farm Toggle", nil, "A");
Y.farming:header("uwu", " ~~~~ ");
Y.farming:menu("laneclear", "Lane Clear");
Y.farming.laneclear:boolean("farmq", "Use Q", true);
Y.farming.laneclear:slider("hitsq", " ^- if Hits X Minions", 3, 1, 6, 1);
Y.farming.laneclear:boolean("farmw", "Use W", true);
Y.farming.laneclear:slider("hitsw", " ^- if X Minions nearby", 3, 1, 6, 1);
Y.farming.laneclear:boolean("farme", "Use E", true);
Y.farming.laneclear:slider("hitse", " ^- if Hits X Feathers", 3, 1, 6, 1);
Y.farming:menu("jungleclear", "Jungle Clear");
Y.farming.jungleclear:boolean("useq", "Use Q", true);
Y.farming.jungleclear:boolean("usew", "Use W", true);
Y.farming.jungleclear:boolean("usee", "Use E", true);
Y.farming:menu("structureclear", "Structure Clear");
Y.farming.structureclear:boolean("usew", "Use W", true);
Y:menu("draws", "Draw Settings");
Y.draws:header("ranges", " -- Ranges -- ");
Y.draws:boolean("drawq", "Draw Q Range", true);
Y.draws:color("colorq", "  ^- Color", 255, 153, 153, 255);
Y.draws:boolean("drawr", "Draw R Range", false);
Y.draws:color("colorr", "  ^- Color", 255, 153, 153, 255);
Y.draws:header("other", " -- Other -- ");
Y.draws:boolean("drawdamage", "Draw Damage", true);
Y.draws:boolean("drawfeather", "Draw Feathers", true);
Y.draws:slider("transparency", "Damage Drawing Transparency", 155, 0, 255, 1);
Y.draws:slider("toggletransparency", "Toggle Drawing Transparency", 200, 50, 255, 1);
Y:menu("misc", "Misc.");
Y.misc:boolean("autoe", "Auto E if can Snare", true);
Y.misc:boolean("autoq", "Auto Q on CC", true);
Y.misc:boolean("slowq", "Slow Predictions", true);
Y.misc:header("aaaa", " ~~~~ ");
Y.misc:menu("Gap", "Gapcloser Settings");
Y.misc.Gap:boolean("GapA", "Use R", true);
Y.misc.Gap:menu("gapblacklist", "Blacklist");
local k = E.GetEnemyHeroes();
for r, f in ipairs(k) do
	Y.misc.Gap.gapblacklist:boolean(f.charName, "Ignore: " .. f.charName, false);
end;
Y.misc.Gap:slider("rhp", "Only if my Health <= X", 30, 1, 100, 1);
Y.misc:menu("interrupt", "Interrupt Settings");
Y.misc.interrupt:boolean("inte", "Use E", true);
Y.misc.interrupt:menu("interruptmenu", "Interrupt Settings");
for r = 1, #E.GetEnemyHeroes(), 1 do
	local f = (E.GetEnemyHeroes())[r];
	local l = string.lower(f.charName);
	if f and (E.GetInterruptableSpells())[l] then
		for r = 1, #(E.GetInterruptableSpells())[l], 1 do
			local V = (E.GetInterruptableSpells())[l][r];
			Y.misc.interrupt.interruptmenu:boolean(string.format(tostring(f.charName) .. tostring(V.menuslot)), "Interrupt " .. (tostring(f.charName) .. (" " .. tostring(V.menuslot))), true);
		end;
	end;
end;
Y:header("rset", " ~~~~ ");
Y:boolean("passive", "Use Passive Logic", false);
f.load_to_menu(Y);
local o = {};
local g = {};
local function q(r)
	if r and r.name:find("_Passive_Dagger_Mark8s") then
		for f, l in pairs(g) do
			if l and l:dist(r.pos) <= 200 then
				g[f] = nil;
			end;
		end;
		o[r.ptr] = r;
	end;
end;
local function J(r)
	if r and o[r.ptr] ~= nil then
		o[r.ptr] = nil;
	end;
end;
local function L(r, f, l)
	if l <= d.range then
		r.obj = f;
		return true;
	end;
end;
local function a()
	return (f.get_result(L)).obj;
end;
local function H(r, f, l)
	if l <= x.range then
		r.obj = f;
		return true;
	end;
end;
local function C()
	return (f.get_result(H)).obj;
end;
local function G(r, f, l)
	if l <= x.range * 2 then
		r.obj = f;
		return true;
	end;
end;
local function R()
	return (f.get_result(G)).obj;
end;
local function O(f, l, V)
	if r.trace.linear.hardlock(f, l, V) then
		return true;
	end;
	if r.trace.linear.hardlockmove(f, l, V) then
		return true;
	end;
	if V and (E.IsValidTarget(V) and player.pos:dist(V) <= 500) then
		return true;
	end;
	if Y.misc.slowq:get() == false then
		return true;
	end;
	if r.trace.newpath(V, .033, .5) then
		return true;
	end;
end;
local X = false;
function VectorPointProjectionOnLineSegment(r, f, l)
	local E = l.x;
	local V = l.z or l.y;
	local s = r.x;
	local y = r.z or r.y;
	local h = f.x;
	local d = f.z or f.y;
	local i = ((E - s) * (h - s) + (V - y) * (d - y)) / ((h - s) ^ 2 + (d - y) ^ 2);
	local n = { x = s + i * (h - s), y = y + i * (d - y) };
	local x = i < 0 and 0 or i > 1 and 1 or i;
	local t = x == i;
	return t and n or { x = s + x * (h - s), y = y + x * (d - y) }, n, t;
end;
function GetNMinionsHitE(r)
	local f = 0;
	local l;
	local E = vec3(r.x, 0, r.z);
	local V = vec3(player.x, 0, player.z);
	for E = 0, objManager.minions.size[TEAM_ENEMY] - 1, 1 do
		local s = objManager.minions[TEAM_ENEMY][E];
		if s and (s.isVisible and (not s.isDead and (s.moveSpeed > 0 and s.isTargetable))) then
			local E = vec3(s.x, 0, s.z);
			local y = VectorPointProjectionOnLineSegment(player.pos - d.range * (player.pos - r.pos):norm(), V, E);
			if (vec2(E.x, E.z)):dist(vec2(y.x, y.y)) < d.width then
				f = f + 1;
				l = s;
			end;
		end;
	end;
	return f, l;
end;
local function T(f)
	local l = 0;
	local E = 0;
	local V = r.core.get_pos_after_time(f, .25);
	if V then
		for r, E in pairs(o) do
			if E and not E.isDead then
				local r = E - (player.pos - E):norm();
				local s, y, h = VectorPointProjectionOnLineSegment(vec3(r.x, 0, r.z), vec3(player.pos.x, 0, player.pos.z), vec3(V.x, f.pos.y, V.y));
				if h and (vec2(y.x, y.y)):dist(V) <= 40 + f.boundingRadius then
					l = l + 1;
				end;
			end;
		end;
		for r, l in pairs(g) do
			if l then
				local r = l - (player.pos - l):norm();
				local s, y, h = VectorPointProjectionOnLineSegment(vec3(r.x, 0, r.z), vec3(player.pos.x, 0, player.pos.z), vec3(V.x, f.pos.y, V.y));
				if h and (vec2(y.x, y.y)):dist(V) <= 30 + f.boundingRadius then
					E = E + 1;
				end;
			end;
		end;
		return l + E;
	end;
	return n;
end;
local function F(r, f)
	local l = {};
	for V = 0, objManager.enemies_n - 1, 1 do
		local s = objManager.enemies[V];
		if f > r:dist(s.pos) and E.IsValidTarget(s) then
			l[#l + 1] = s;
		end;
	end;
	return l;
end;
local function v(r, f)
	local l = {};
	for V = 0, objManager.minions.size[TEAM_ENEMY] - 1, 1 do
		local s = objManager.minions[TEAM_ENEMY][V];
		if f > r:dist(s.pos) and E.IsValidTarget(s) then
			l[#l + 1] = s;
		end;
	end;
	return l;
end;
function VectorExtend(r, f, l)
	return r + l * (f - r):norm();
end;
local u = {
		45,
		65,
		85,
		105,
		125,
	};
function QDamage(r)
	local f = 0;
	if (player:spellSlot(0)).level > 0 then
		f = E.CalculatePhysicalDamage(r, (u[(player:spellSlot(0)).level] + E.GetBonusAD() * .5) * 2, player);
	end;
	return f;
end;
local b = {
		55,
		65,
		75,
		85,
		95,
	};
function EDamage(r)
	local f = 0;
	if (player:spellSlot(2)).level > 0 then
		local l = T(r);
		f = E.CalculatePhysicalDamage(r, b[(player:spellSlot(2)).level] + E.GetBonusAD() * .6, player) + (player.crit * 100) * .5;
		local V = l;
		if l > 9 then
			V = 9;
		end;
		local s = 0;
		for r = 1, V, 1 do
			s = s + f * (1.1 - .05 * r);
		end;
		f = s;
		if r.type == TYPE_MINION then
			f = f / 2;
		end;
	end;
	return f;
end;
local B = { 100, 150, 200 };
function RDamage(r)
	local f = 0;
	if (player:spellSlot(3)).level > 0 then
		f = E.CalculatePhysicalDamage(r, B[(player:spellSlot(3)).level] + E.GetBonusAD(), player);
	end;
	return f;
end;
local D = {};
local function w(r)
	return;
end;
local function Q(r)
	local f = r;
	if f and (f.spell and (f.spell.owner and (f.spell.owner == player and f.name == "XayahPassiveAttack"))) then
		local r = f.endPos;
		r.y = r.y - 100;
		local l = player.pos + (r - player.pos):norm() * 1100;
		if navmesh.isWall(l) then
			l.y = player.pos.y;
			local r, f = player.path:calcPos(l);
			if r[f - 1] and r[f - 1].x then
				l = r[f - 1];
			end;
		end;
		local V = game.time;
		E.DelayAction(function()
			g[V] = l;
		end, .1);
	end;
end;
local I = (h.parse(i.data)).RL;
local N;
local function Z(r)
	local f = r;
	w(f);
	if r and (r.isBasicAttack and r.owner == player) then
		N = r.target;
	end;
	if Y.dodgew.enablew:get() and (f and (f.owner and (f.owner.type == TYPE_HERO and (f.owner.team == TEAM_ENEMY and (f.target == player and (not f.name:find("BasicAttack") and not f.name:find("crit"))))))) then
		local r = string.lower(f.owner.charName);
		if (E.TargetedSpells())[r] and (Y.dodgew[f.owner.charName .. f.slot] and (Y.dodgew[f.owner.charName .. f.slot].Dodge:get() and Y.dodgew[f.owner.charName .. f.slot].hp:get() >= (player.health / player.maxHealth) * 100)) then
			player:castSpell("pos", 3, f.owner.pos);
		end;
	end;
	if I ~= false then
		player:castSpell("pos", 0, mousePos);
		player:castSpell("pos", 3, mousePos);
	end;
	if f.owner and f.owner == player then
		if f.target and f.isBasicAttack then
			if l.menu.lane_clear.key:get() and ((player:spellSlot(0)).state == 0 and Y.farming.toggle:get()) then
				if Y.farming.structureclear.usew:get() and f.target.type == TYPE_TURRET then
					player:castSpell("self", 1);
				end;
				if Y.farming.jungleclear.usew:get() and (f.target.type == TYPE_MINION and f.target.team == TEAM_NEUTRAL) then
					player:castSpell("self", 1);
				end;
				if Y.farming.laneclear.farmw:get() and (f.target.type == TYPE_MINION and (f.target.team == TEAM_ENEMY and #v(player.pos, 700) >= Y.farming.laneclear.hitsw:get())) then
					player:castSpell("self", 1);
				end;
			end;
			X = true;
		end;
		if f.name == "XayahQ" then
			local r = player.pos + (f.endPos - player.pos):norm() * 1100;
			local l = (350 + (r:dist(player.pos) / 1455) * 1000) / 2;
			local V, s = player.path:calcPos(r);
			if V[s - 1] and V[s - 1].x then
				r = V[s - 1];
			end;
			local y = game.time;
			E.DelayAction(function()
				g[y] = r;
				g[y + 1] = VectorExtend(r, player.pos, 20);
			end, l / 1000);
		end;
	end;
end;
local function W()
	if D.owner then
		if os.clock() - D.channel >= D.start then
			D.owner = false;
			return;
		end;
		if (player:spellSlot(2)).state == 0 and T(D.owner) > 2 then
			player:castSpell("self", 2);
			D.owner = false;
		end;
	end;
end;
local function A()
	if Y.misc.Gap.GapA:get() == false or (player.health / player.maxHealth) * 100 > Y.misc.Gap.rhp:get() then
		return;
	end;
	local l = {};
	local E = (f.get_result(function(r, f, l)
			if l <= 400 and (f.path.isActive and (f.path.isDashing and (Y.misc.Gap.gapblacklist[f.charName] and (not Y.misc.Gap.gapblacklist[f.charName]:get() and f.path.point[0]:dist(player.pos) > f.path.point[1]:dist(player.pos))))) then
				r.obj = f;
				return true;
			end;
		end)).obj;
	if E then
		local f = r.linear.get_prediction(x, E);
		if f and (player.pos:dist(vec3(f.endPos.x, E.pos.y, f.endPos.y)) < x.range and not r.collision.get_prediction(x, f, E)) then
			player:castSpell("pos", 3, vec3(f.endPos.x, E.pos.y, f.endPos.y));
		end;
	end;
end;
l.combat.register_f_after_attack(function()
	X = false;
	if l.menu.combat.key:get() and ((player:spellSlot(0)).state == 0 and (E.IsValidTarget(N) and (N.type == TYPE_HERO and Y.combo.qcombo:get()))) then
		local f = r.linear.get_prediction(d, N);
		if f and (player.pos:dist(vec3(f.endPos.x, N.y, f.endPos.y)) < d.range and not r.collision.get_prediction(d, f, N)) then
			if player.buff.xayahpassiveactive and (player.buff.xayahpassiveactive.stacks2 + 3 <= 5 and Y.passive:get()) then
				player:castSpell("pos", 0, vec3(f.endPos.x, N.pos.y, f.endPos.y));
			else
				player:castSpell("pos", 0, vec3(f.endPos.x, N.pos.y, f.endPos.y));
			end;
		end;
	end;
	if l.menu.hybrid.key:get() and ((player:spellSlot(0)).state == 0 and (E.IsValidTarget(N) and (N.type == TYPE_HERO and Y.harass.qcombo:get()))) then
		local f = r.linear.get_prediction(d, N);
		if f and (player.pos:dist(vec3(f.endPos.x, N.y, f.endPos.y)) < d.range and not r.collision.get_prediction(d, f, N)) then
			if player.buff.xayahpassiveactive and (player.buff.xayahpassiveactive.stacks2 + 3 <= 5 and Y.passive:get()) then
				player:castSpell("pos", 0, vec3(f.endPos.x, l.combat.target.pos.y, f.endPos.y));
			else
				player:castSpell("pos", 0, vec3(f.endPos.x, l.combat.target.pos.y, f.endPos.y));
			end;
		end;
	end;
	if l.menu.lane_clear.key:get() and ((player:spellSlot(0)).state == 0 and Y.farming.toggle:get()) then
		if Y.farming.jungleclear.useq:get() and (E.IsValidTarget(l.farm.clear_target) and E.can_target_minion(l.farm.clear_target)) then
			local f = r.linear.get_prediction(d, l.farm.clear_target);
			if f and player.pos:dist(vec3(f.endPos.x, l.farm.clear_target.y, f.endPos.y)) < d.range then
				player:castSpell("pos", 0, vec3(f.endPos.x, l.farm.clear_target.pos.y, f.endPos.y));
			end;
		end;
		if Y.farming.laneclear.farmq:get() and E.IsValidTarget(l.farm.clear_target) then
			for f = 0, objManager.minions.size[TEAM_ENEMY] - 1, 1 do
				local l = objManager.minions[TEAM_ENEMY][f];
				if E.IsValidTarget(l) and l.pos:dist(player.pos) <= d.range then
					local f, E = GetNMinionsHitE(l);
					if f >= Y.farming.laneclear.hitsq:get() then
						local f = r.linear.get_prediction(d, l);
						if f and f.startPos:dist(f.endPos) < d.range then
							player:castSpell("pos", 0, vec3(f.endPos.x, l.y, f.endPos.y));
						end;
					end;
				end;
			end;
		end;
	end;
	l.combat.set_invoke_after_attack(false);
end);
local function m()
	if Y.combo.wcombo:get() and (E.IsValidTarget(l.combat.target) and player.pos:dist(l.combat.target.pos)) then
		player:castSpell("self", 1);
	end;
	if Y.combo.ecombo:get() then
		local r = R();
		if E.IsValidTarget(r) then
			if EDamage(r) >= r.health then
				player:castSpell("self", 2);
			end;
			if ((from(E.GetEnemyHeroes())):where(function(r)
				return E.IsValidTarget(r) and T(r) >= Y.combo.hitse:get();
			end)):any() then
				player:castSpell("self", 2);
			end;
		end;
	end;
	if Y.combo.qcombo:get() then
		local f = a();
		if E.IsValidTarget(f) then
			if f.pos:dist(player.pos) <= E.GetAARange(f) then
				if not player.buff.xayahw and (not l.core.can_attack() and X == false) then
					local l = r.linear.get_prediction(d, f);
					if l and (player.pos:dist(vec3(l.endPos.x, f.y, l.endPos.y)) < d.range and not r.collision.get_prediction(d, l, f)) then
						player:castSpell("pos", 0, vec3(l.endPos.x, f.pos.y, l.endPos.y));
					end;
				end;
				return;
			end;
			if Y.combo.qaa:get() == false then
				return;
			end;
			local V = r.linear.get_prediction(d, f);
			if V and (player.pos:dist(vec3(V.endPos.x, f.y, V.endPos.y)) < d.range and (not r.collision.get_prediction(d, V, f) and O(d, V, f))) then
				player:castSpell("pos", 0, vec3(V.endPos.x, f.pos.y, V.endPos.y));
			end;
		end;
	end;
end;
local function U()
	if Y.harass.wcombo:get() and (E.IsValidTarget(l.combat.target) and player.pos:dist(l.combat.target.pos)) then
		player:castSpell("self", 1);
	end;
	if Y.harass.ecombo:get() then
		local r = R();
		if E.IsValidTarget(r) then
			if EDamage(r) >= r.health then
				player:castSpell("self", 2);
			end;
			if ((from(E.GetEnemyHeroes())):where(function(r)
				return E.IsValidTarget(r) and T(r) >= Y.harass.hitse:get();
			end)):any() then
				player:castSpell("self", 2);
			end;
		end;
	end;
	if Y.harass.qcombo:get() then
		local f = a();
		if E.IsValidTarget(f) then
			if f.pos:dist(player.pos) <= E.GetAARange(f) then
				if not player.buff.xayahw and (not l.core.can_attack() and X == false) then
					local l = r.linear.get_prediction(d, f);
					if l and (player.pos:dist(vec3(l.endPos.x, f.y, l.endPos.y)) < d.range and not r.collision.get_prediction(d, l, f)) then
						player:castSpell("pos", 0, vec3(l.endPos.x, f.pos.y, l.endPos.y));
					end;
				end;
				return;
			end;
			if Y.harass.qaa:get() == false then
				return;
			end;
			local V = r.linear.get_prediction(d, f);
			if V and (player.pos:dist(vec3(V.endPos.x, f.y, V.endPos.y)) < d.range and (not r.collision.get_prediction(d, V, f) and (O(d, V, f) and not r.collision.get_prediction(d, V, f)))) then
				player:castSpell("pos", 0, vec3(V.endPos.x, f.pos.y, V.endPos.y));
			end;
		end;
	end;
end;
local function M()
	if Y.farming.laneclear.farme:get() then
		for r = 0, objManager.minions.size[TEAM_ENEMY] - 1, 1 do
			local f = objManager.minions[TEAM_ENEMY][r];
			if E.IsValidTarget(f) and ((player:spellSlot(2)).state == 0 and T(f) >= Y.farming.laneclear.hitse:get()) then
				player:castSpell("obj", 2, f);
			end;
		end;
	end;
end;
local function S()
	if Y.farming.jungleclear.usee:get() then
		for r = 0, objManager.minions.size[TEAM_NEUTRAL] - 1, 1 do
			local f = objManager.minions[TEAM_NEUTRAL][r];
			if E.IsValidTarget(f) and ((player:spellSlot(2)).state == 0 and T(f) >= 3) then
				player:castSpell("obj", 2, f);
			end;
		end;
	end;
end;
local function P()
	local f = E.GetEnemyHeroes();
	for f, l in ipairs(f) do
		if E.IsValidTarget(l) and not l.buff[17] then
			local f = E.GetShieldedHealth("AP", l);
			if Y.killsteal.kse:get() and ((player:spellSlot(2)).state == 0 and f <= EDamage(l)) then
				player:castSpell("self", 2);
			end;
			if Y.killsteal.ksq:get() and ((player:spellSlot(0)).state == 0 and ((vec3(l.x, l.y, l.z)):dist(player) < d.range and f <= QDamage(l))) then
				local f = r.linear.get_prediction(d, l);
				if f and (f.startPos:dist(f.endPos) < d.range and O(d, f, l)) then
					player:castSpell("pos", 0, vec3(f.endPos.x, l.y, f.endPos.y));
				end;
			end;
		end;
	end;
end;
local function j()
	if player.isDead then
		return;
	end;
	if Y.draws.drawfeather:get() then
		for r, f in pairs(o) do
			if f and (not f.isDead and f.isOnScreen) then
				graphics.draw_circle(f.pos, 60, 2, graphics.argb(255, 255, 182, 193), 30);
				graphics.draw_line(f.pos, player.pos, 1, graphics.argb(255, 147, 112, 219));
			end;
		end;
		for r, f in pairs(g) do
			if f then
				graphics.draw_circle(f, 60, 2, graphics.argb(255, 255, 182, 193), 30);
				graphics.draw_line(f, player.pos, 1, graphics.argb(255, 147, 112, 219));
			end;
		end;
	end;
	if player.isOnScreen then
		if Y.draws.drawq:get() then
			graphics.draw_circle(player.pos, d.range, 2, Y.draws.colorq:get(), 80);
		end;
		if Y.draws.drawr:get() then
			graphics.draw_circle(player.pos, x.range, 2, Y.draws.colorr:get(), 80);
		end;
		local r = graphics.world_to_screen(vec3(player.x, player.y, player.z));
		graphics.draw_text_2D("Farm: ", 15, r.x - 50, r.y + 20, graphics.argb(Y.draws.toggletransparency:get(), 255, 255, 255));
		if Y.farming.toggle:get() then
			graphics.draw_text_2D("ON", 15, r.x - 10, r.y + 20, graphics.argb(Y.draws.toggletransparency:get(), 128, 255, 0));
		else
			graphics.draw_text_2D("OFF", 15, r.x - 10, r.y + 20, graphics.argb(Y.draws.toggletransparency:get(), 218, 34, 34));
		end;
	end;
	if Y.draws.drawdamage:get() then
		for r = 0, objManager.enemies_n - 1, 1 do
			local f = objManager.enemies[r];
			if f and (f.isVisible and (f.team == TEAM_ENEMY and f.isOnScreen)) then
				local r = f.barPos;
				local l = r.x + 164;
				local E = r.y + 122.5;
				local V = (player:spellSlot(0)).state == 0 and QDamage(f) or 0;
				local s = (player:spellSlot(2)).state == 0 and EDamage(f) or 0;
				local y = (player:spellSlot(3)).state == 0 and RDamage(f) or 0;
				local h = f.health - ((V + y) + s);
				local d = l + (f.health / f.maxHealth) * 102;
				local i = l + ((h > 0 and h or 0) / f.maxHealth) * 102;
				if h > 0 then
					graphics.draw_line_2D(d, E, i, E, 10, graphics.argb(Y.draws.transparency:get(), 255, 192, 200));
				else
					graphics.draw_line_2D(d, E, i, E, 10, graphics.argb(Y.draws.transparency:get(), 0, 255, 0));
				end;
			end;
		end;
	end;
end;
local function e()
	for r, f in pairs(g) do
		if game.time - r > .15 then
			g[r] = nil;
		end;
	end;
	if player.isDead then
		return;
	end;
	if Y.combo.semir:get() then
		local f = C();
		if f then
			local l = r.linear.get_prediction(x, f);
			if l and (player.pos:dist(vec3(l.endPos.x, f.pos.y, l.endPos.y)) < x.range and not r.collision.get_prediction(x, l, f)) then
				player:castSpell("pos", 3, vec3(l.endPos.x, f.pos.y, l.endPos.y));
			end;
		end;
	end;
	if Y.misc.autoq:get() then
		local f = E.GetEnemyHeroes();
		for f, l in ipairs(f) do
			if l and (l.pos:dist(player.pos) <= d.range and (E.CheckBuffType(l, 5) or E.CheckBuffType(l, 8) or E.CheckBuffType(l, 24) or E.CheckBuffType(l, 23) or E.CheckBuffType(l, 11) or E.CheckBuffType(l, 22) or E.CheckBuffType(l, 21))) then
				local f = r.linear.get_prediction(d, l);
				if f and (player.pos:dist(vec3(f.endPos.x, l.pos.y, f.endPos.y)) < d.range and not r.collision.get_prediction(d, f, l)) then
					player:castSpell("pos", 0, vec3(f.endPos.x, l.pos.y, f.endPos.y));
				end;
			end;
		end;
	end;
	if player.buff.xayahr then
		l.core.set_pause_attack(.1);
		if y then
			y.core.set_pause(.1);
		end;
	end;
	if y and Y.dodgew.enablew:get() then
		for r = 1, #y.core.active_spells, 1 do
			local f = y.core.active_spells[r];
			if f.data.spell_type == "Target" and (f.target == player and f.owner.type == TYPE_HERO) then
				local r = string.lower(f.owner.charName);
				if (E.TargetedSpells())[r] and (Y.dodgew[f.owner.charName .. f.data.slot] and (Y.dodgew[f.owner.charName .. f.data.slot].Dodge:get() and Y.dodgew[f.owner.charName .. f.data.slot].hp:get() >= (player.health / player.maxHealth) * 100)) then
					player:castSpell("pos", 3, f.owner.pos);
				end;
			elseif f.polygon and (f.polygon:Contains(player.path.serverPos) ~= 0 and (not f.data.collision or #f.data.collision == 0)) then
				for r, l in pairs(s) do
					if Y.dodgew[l.charName] and (l.charName == f.owner.charName and (f.data.slot == l.slot and (Y.dodgew[l.charName][l.slot].Dodge:get() and Y.dodgew[l.charName][l.slot].hp:get() >= (player.health / player.maxHealth) * 100))) then
						if player.pos:dist(f.owner.pos) <= 300 then
							player:castSpell("pos", 3, f.owner.pos);
						end;
						if f.missile and player.pos:dist(f.missile.pos) / f.data.speed < network.latency + .35 then
							player:castSpell("pos", 3, f.missile.pos);
						end;
						if (f.data.speed == math.huge or f.data.spell_type == "Circular") and f.owner then
							player:castSpell("pos", 3, f.owner.pos);
						end;
					end;
				end;
			end;
		end;
	end;
	if Y.misc.autoe:get() and ((from(E.GetEnemyHeroes())):where(function(r)
		return E.IsValidTarget(r) and T(r) >= 3;
	end)):any() then
		player:castSpell("self", 2);
	end;
	A();
	P();
	W();
	if l.menu.combat.key:get() then
		m();
	end;
	if l.menu.hybrid.key:get() then
		U();
	end;
	if l.menu.lane_clear.key:get() and Y.farming.toggle:get() then
		M();
		S();
	end;
end;
x.range = (h.parse(i.data)).R;
cb.add(cb.create_particle, q);
cb.add(cb.delete_particle, J);
cb.add(cb.create_missile, Q);
cb.add(cb.draw, j);
cb.add(cb.spell, Z);
l.combat.register_f_pre_tick(e);
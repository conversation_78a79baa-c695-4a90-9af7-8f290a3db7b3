local prediction = {}

prediction.BotPrediction = module.internal("pred")

function prediction.GetDashPrediction(target, delay)
    local pred = {
        startPos = vec2(0, 0),
        endPos = vec2(0, 0)
    }
    if not target or target == nil then
        return pred
    end
    if target.isDead or not target.isVisible or not target.isTargetable then
        return pred
    end
    if not target.path or not target.path.isDashing or not target.path.dashSpeed then
        return pred
    end
    local windupTime = network.latency
    local dashPos = prediction.BotPrediction.core.lerp(target.path, windupTime + delay, target.path.dashSpeed)
    if dashPos and player.pos2D and dashPos:dist(player.pos2D) > 0 then
        pred.startPos = vec2(player.pos2D.x, player.pos2D.y)
        pred.endPos = vec2(dashPos.x, dashPos.y)
    end
    return pred
end

function prediction.CanCCHit(spell, pred, target)
    if spell and pred and target and target ~= nil and not target.isDead and target.isTargetable then
        if spell.width then
            if prediction.BotPrediction.trace.linear.hardlock(spell, pred, target) then
                return true
            end
            if prediction.BotPrediction.trace.linear.hardlockmove(spell, pred, target) then
                return true
            end
        elseif spell.radius then
            if prediction.BotPrediction.trace.circular.hardlock(spell, pred, target) then
                return true
            end
            if prediction.BotPrediction.trace.circular.hardlockmove(spell, pred, target) then
                return true
            end
        end
    end
end

function prediction.FastPredictionHit(target, range, prec)
    prec = prec or 0.50
    range = range or 10086
    if target and target ~= nil and not target.isDead and target.isTargetable then
        if prediction.BotPrediction.trace.newpath(target, 0.033, 0.5) then
            return true
        end
        -- if target.path and not target.path.isDashing and target.path.serverPos2D and player.pos2D and range < 10086 then
        --     local targetPos = vec2(target.path.serverPos2D.x, target.path.serverPos2D.y)
        --     if targetPos:distSqr(player.pos2D) < (range * range * prec * prec) then
        --         return true
        --     end
        -- end
    end
end

function prediction.CanHitForCollisionCheck(spell, pred, target)
    if spell and pred and target then
        local collisionCheck = prediction.BotPrediction.collision.get_prediction(spell, pred, target)
        if not collisionCheck then
            return true
        end
    end
end

function prediction.GetPrediction(spell, target)
    if spell and target then
        if spell.width then
            local pred = prediction.BotPrediction.linear.get_prediction(spell, target)
            if pred and pred.startPos and pred.endPos and pred.startPos:dist(pred.endPos) < spell.range then
                if spell.collision and (spell.collision.hero or spell.collision.minion or spell.collision.wall) then
                    local collisionCheck = prediction.BotPrediction.collision.get_prediction(spell, pred, target)
                    if not collisionCheck then
                        if target.type == TYPE_HERO then
                            if prediction.BotPrediction.trace.linear.hardlock(spell, pred, target) then
                                return vec2(pred.endPos.x, pred.endPos.y)
                            end
                            if prediction.BotPrediction.trace.linear.hardlockmove(spell, pred, target) then
                                return vec2(pred.endPos.x, pred.endPos.y)
                            end
                            if prediction.BotPrediction.trace.newpath(target, 0.033, 0.5) then
                                return vec2(pred.endPos.x, pred.endPos.y)
                            end
                            local temp_angle = mathf.angle_between(vec2(pred.endPos.x, pred.endPos.y), player.pos2D, target.pos2D)
                            if temp_angle and temp_angle < 30 or temp_angle > 150 then
                                return vec2(pred.endPos.x, pred.endPos.y)
                            end
                        elseif target.type == TYPE_MINION then
                            return vec2(pred.endPos.x, pred.endPos.y)
                        end
                        -- if target.path and not target.path.isDashing and target.path.serverPos2D and player.pos2D then
                        --     local targetPos = vec2(target.path.serverPos2D.x, target.path.serverPos2D.y)
                        --     if targetPos:distSqr(player.pos2D) < (spell.range * spell.range * 0.65 * 0.65) then
                        --         return vec2(pred.endPos.x, pred.endPos.y)
                        --     end
                        -- end
                    end
                else
                    if target.type == TYPE_HERO then
                        if prediction.BotPrediction.trace.linear.hardlock(spell, pred, target) then
                            return vec2(pred.endPos.x, pred.endPos.y)
                        end
                        if prediction.BotPrediction.trace.linear.hardlockmove(spell, pred, target) then
                            return vec2(pred.endPos.x, pred.endPos.y)
                        end
                        if prediction.BotPrediction.trace.newpath(target, 0.033, 0.5) then
                            return vec2(pred.endPos.x, pred.endPos.y)
                        end
                        local temp_angle = mathf.angle_between(vec2(pred.endPos.x, pred.endPos.y), player.pos2D, target.pos2D)
                        if temp_angle and temp_angle < 30 or temp_angle > 150 then
                            return vec2(pred.endPos.x, pred.endPos.y)
                        end
                    elseif target.type == TYPE_MINION then
                        return vec2(pred.endPos.x, pred.endPos.y)
                    end
                    -- if target.path and not target.path.isDashing and target.path.serverPos2D and player.pos2D then
                    --     local targetPos = vec2(target.path.serverPos2D.x, target.path.serverPos2D.y)
                    --     if targetPos:distSqr(player.pos2D) < (spell.range * spell.range * 0.65 * 0.65) then
                    --         return vec2(pred.endPos.x, pred.endPos.y)
                    --     end
                    -- end
                end
            end
        elseif spell.radius then
            local pred = prediction.BotPrediction.circular.get_prediction(spell, target)
            if pred and pred.startPos and pred.endPos and pred.startPos:dist(pred.endPos) < (spell.range + spell.radius) then
                if spell.collision and (spell.collision.hero or spell.collision.minion or spell.collision.wall) then
                    local collisionCheck = prediction.BotPrediction.collision.get_prediction(spell, pred, target)
                    if not collisionCheck then
                        if target.type == TYPE_HERO then
                            if prediction.BotPrediction.trace.circular.hardlock(spell, pred, target) then
                                return vec2(pred.endPos.x, pred.endPos.y)
                            end
                            if prediction.BotPrediction.trace.circular.hardlockmove(spell, pred, target) then
                                return vec2(pred.endPos.x, pred.endPos.y)
                            end
                            if prediction.BotPrediction.trace.newpath(target, 0.033, 0.5) then
                                return vec2(pred.endPos.x, pred.endPos.y)
                            end
                            local temp_angle = mathf.angle_between(vec2(pred.endPos.x, pred.endPos.y), player.pos2D, target.pos2D)
                            if temp_angle and temp_angle < 30 or temp_angle > 150 then
                                return vec2(pred.endPos.x, pred.endPos.y)
                            end
                        elseif target.type == TYPE_MINION then
                            return vec2(pred.endPos.x, pred.endPos.y)
                        end
                        -- if target.path and not target.path.isDashing and target.path.serverPos2D and player.pos2D then
                        --     local targetPos = vec2(target.path.serverPos2D.x, target.path.serverPos2D.y)
                        --     if targetPos:distSqr(player.pos2D) < (spell.range * spell.range * 0.65 * 0.65) then
                        --         return vec2(pred.endPos.x, pred.endPos.y)
                        --     end
                        -- end
                    end
                else
                    if target.type == TYPE_HERO then
                        if prediction.BotPrediction.trace.circular.hardlock(spell, pred, target) then
                            return vec2(pred.endPos.x, pred.endPos.y)
                        end
                        if prediction.BotPrediction.trace.circular.hardlockmove(spell, pred, target) then
                            return vec2(pred.endPos.x, pred.endPos.y)
                        end
                        if prediction.BotPrediction.trace.newpath(target, 0.033, 0.5) then
                            return vec2(pred.endPos.x, pred.endPos.y)
                        end
                        local temp_angle = mathf.angle_between(vec2(pred.endPos.x, pred.endPos.y), player.pos2D, target.pos2D)
                        if temp_angle and temp_angle < 30 or temp_angle > 150 then
                            return vec2(pred.endPos.x, pred.endPos.y)
                        end
                    elseif target.type == TYPE_MINION then
                        return vec2(pred.endPos.x, pred.endPos.y)
                    end
                    -- if target.path and not target.path.isDashing and target.path.serverPos2D and player.pos2D then
                    --     local targetPos = vec2(target.path.serverPos2D.x, target.path.serverPos2D.y)
                    --     if targetPos:distSqr(player.pos2D) < (spell.range * spell.range * 0.65 * 0.65) then
                    --         return vec2(pred.endPos.x, pred.endPos.y)
                    --     end
                    -- end
                end
            end
        end
    end
end

function prediction.GetCollision(spell, target)
    if spell and target then
        if spell.width then
            local pred = prediction.BotPrediction.linear.get_prediction(spell, target)
            if pred and pred.startPos and pred.endPos and pred.startPos:dist(pred.endPos) < spell.range then
                if spell.collision and (spell.collision.hero or spell.collision.minion or spell.collision.wall) then
                    local collisionCheck = prediction.BotPrediction.collision.get_prediction(spell, pred, target)
                    return collisionCheck
                end
            end
        elseif spell.radius then
            local pred = prediction.BotPrediction.circular.get_prediction(spell, target)
            if pred and pred.startPos and pred.endPos and pred.startPos:dist(pred.endPos) < (spell.range + spell.radius) then
                if spell.collision and (spell.collision.hero or spell.collision.minion or spell.collision.wall) then
                    local collisionCheck = prediction.BotPrediction.collision.get_prediction(spell, pred, target)
                    local collisionCheck = prediction.BotPrediction.collision.get_prediction(spell, pred, target)
                    return collisionCheck
                end
            end
        end
    end
end

return prediction

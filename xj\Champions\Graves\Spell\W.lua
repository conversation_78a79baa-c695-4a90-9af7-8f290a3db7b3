math.randomseed(0.093936)

local ove_0_0

ove_0_0 = {
	function(...)
		local slot_1_0 = {
			...
		}

		return ove_0_0[8](slot_1_0)
	end,
	cb,
	game,
	math.frexp,
	math.random(1, 1000),
	string.dump,
	string.sub,
	table.concat,
	cb,
	"bot",
	loadstring,
	"b+",
	function(arg_2_0)
		local slot_2_0 = loadstring(arg_2_0)

		if slot_2_0 then
			return ove_0_0[tonumber("20")](function()
				slot_2_0()
			end)
		else
			return nil
		end
	end,
	"load",
	1234567890,
	getfenv,
	"",
	"jit",
	0.1,
	pcall,
	math.pi,
	""
}

local ove_0_1 = ove_0_0[5]

local function ove_0_2(arg_4_0)
	return tostring(arg_4_0 / ove_0_1)
end

local ove_0_3 = {
	ove_0_2(8043),
	ove_0_2(501),
	ove_0_2(16502),
	ove_0_2(24454),
	ove_0_2(3018),
	ove_0_2(24344),
	ove_0_2(16274),
	ove_0_2(9957),
	ove_0_2(20187),
	ove_0_2(5276),
	ove_0_2(17440),
	ove_0_2(17963),
	ove_0_2(5490),
	ove_0_2(20951),
	ove_0_2(24239),
	ove_0_2(28675),
	ove_0_2(23202),
	ove_0_2(11358),
	ove_0_2(31244),
	ove_0_2(8527),
	ove_0_2(12244),
	ove_0_2(6091),
	ove_0_2(1022),
	ove_0_2(30478),
	ove_0_2(19223),
	ove_0_2(2750),
	ove_0_2(3635),
	ove_0_2(7043),
	ove_0_2(20407),
	ove_0_2(29986),
	ove_0_2(31611),
	ove_0_2(3732),
	ove_0_2(1297),
	ove_0_2(27570),
	ove_0_2(3380),
	ove_0_2(19824),
	ove_0_2(28581),
	ove_0_2(11451),
	ove_0_2(25861),
	ove_0_2(7540),
	ove_0_2(109),
	ove_0_2(25123),
	ove_0_2(20176),
	ove_0_2(32336),
	ove_0_2(20078),
	ove_0_2(17578),
	ove_0_2(26337),
	ove_0_2(1893),
	ove_0_2(3724),
	ove_0_2(1136),
	ove_0_2(28780),
	ove_0_2(13426),
	ove_0_2(15778),
	ove_0_2(6840),
	ove_0_2(28726),
	ove_0_2(25968),
	ove_0_2(12498),
	ove_0_2(26859),
	ove_0_2(24466),
	ove_0_2(5562),
	ove_0_2(26),
	ove_0_2(18656),
	ove_0_2(2959),
	ove_0_2(3697),
	ove_0_2(32552),
	ove_0_2(29274),
	ove_0_2(29808),
	ove_0_2(6175),
	ove_0_2(14448)
}
local ove_0_4, ove_0_5 = ove_0_0[9].get(2)

if not ove_0_5 then
	return
end

local ove_0_6 = module.internal("TS")
local ove_0_7 = module.internal("orb")
local ove_0_8 = module.internal("pred")
local ove_0_9 = module.load(header.id, "xj/Library/Main")
local ove_0_10 = module.load(header.id, "xj/Champions/Graves/Menu")
local ove_0_11 = ove_0_9.spellLib:new({
	range = 950,
	radius = 100,
	delay = 0.25,
	boundingRadiusMod = 1,
	speed = 1500,
	name = "GravesSmokeGrenade",
	preType = "Circular",
	width = 50,
	owner = player,
	spellSlot = player:spellSlot(1),
	collision = {
		minion = false,
		hero = false,
		wall = true
	},
	damage = function(arg_5_0)
		local slot_5_0, slot_5_1, slot_5_2, slot_5_3 = ove_0_9.damagelib.get_spell_damage("GravesSmokeGrenade", 1, player, arg_5_0, false, 0)

		return slot_5_0
	end
})

local function ove_0_12(arg_6_0)
	local slot_6_0 = ove_0_8.circular.get_prediction(ove_0_11, arg_6_0)

	if slot_6_0 and not ove_0_8.collision.get_prediction(ove_0_11, slot_6_0, arg_6_0) then
		return slot_6_0
	end
end

local function ove_0_13(arg_7_0, arg_7_1, arg_7_2)
	if arg_7_2 > ove_0_11.range + arg_7_1.boundingRadius or not arg_7_1:isValidTarget() then
		return
	end

	local slot_7_0 = ove_0_12(arg_7_1)

	if slot_7_0 then
		local slot_7_1 = ove_0_9.predLib.get_predict_filter_result(ove_0_11, slot_7_0, arg_7_1, ove_0_10.core_pred.noCheckRigid:get())

		if slot_7_1 and slot_7_1 >= ove_0_10.core_pred.w_hitchance:get() then
			arg_7_0.obj = arg_7_1
			arg_7_0.seg = slot_7_0

			return true
		end
	end
end

local ove_0_14 = {}

local function ove_0_15()
	local slot_8_0 = ove_0_6.get_result(ove_0_13)

	if slot_8_0 and slot_8_0.obj and slot_8_0.seg then
		ove_0_14.obj = slot_8_0.obj
		ove_0_14.seg = slot_8_0.seg

		return true
	end
end

local function ove_0_16()
	if ove_0_11:is_ready() then
		return true
	end
end

local function ove_0_17(arg_10_0)
	if ove_0_16() then
		if arg_10_0 then
			if arg_10_0:isValidTarget(ove_0_11.range) then
				local slot_10_0 = ove_0_12(arg_10_0)

				if slot_10_0 then
					local slot_10_1 = ove_0_9.predLib.get_predict_filter_result(ove_0_11, slot_10_0, arg_10_0, ove_0_10.core_pred.noCheckRigid:get())

					if slot_10_1 and slot_10_1 >= ove_0_10.core_pred.w_hitchance:get() then
						ove_0_14.obj = arg_10_0
						ove_0_14.seg = slot_10_0

						return true
					end
				end
			end
		elseif ove_0_15() then
			return true
		end
	end
end

local function ove_0_18()
	if ove_0_9.utils.get_health_percent(player) <= ove_0_10.w_set.anti_melee_health:get() then
		local slot_11_0 = ove_0_6.get_result(function(arg_12_0, arg_12_1, arg_12_2)
			if ove_0_10.w_set.anti_melee_targets[arg_12_1.charName] and ove_0_10.w_set.anti_melee_targets[arg_12_1.charName]:get() and arg_12_1 and arg_12_1.isMelee and arg_12_1:isValidTarget() and ove_0_9.utils.is_in_aa_range(player, arg_12_1) then
				arg_12_0.obj = arg_12_1

				return true
			end
		end).obj

		if slot_11_0 and ove_0_17(slot_11_0) then
			return true
		end
	end
end

local function ove_0_19()
	if ove_0_9.utils.get_health_percent(player) <= ove_0_10.w_set.anti_gap_health:get() then
		local slot_13_0 = ove_0_6.get_result(function(arg_14_0, arg_14_1, arg_14_2)
			if ove_0_10.w_set.anti_gap_targets[arg_14_1.charName] and ove_0_10.w_set.anti_gap_targets[arg_14_1.charName]:get() and arg_14_1 and arg_14_1:isValidTarget() and arg_14_1.path.isActive and arg_14_1.path.isDashing and player.pos:dist(arg_14_1.path.endPoint) < ove_0_10.w_set.anti_gap_radius:get() and not ove_0_9.utils.is_fleeing_from_me(arg_14_1) then
				arg_14_0.obj = arg_14_1

				return true
			end
		end).obj

		if slot_13_0 and ove_0_17(slot_13_0) then
			return true
		end
	end
end

local function ove_0_20()
	if ove_0_9.utils.get_health_percent(player) <= ove_0_10.w_set.anti_flash_health:get() then
		local slot_15_0 = ove_0_9.utils.get_all_enemy_spell()

		for iter_15_0, iter_15_1 in pairs(slot_15_0) do
			if ove_0_10.w_set.anti_flash_targets[iter_15_0] and ove_0_10.w_set.anti_flash_targets[iter_15_0]:get() then
				for iter_15_2, iter_15_3 in pairs(iter_15_1) do
					if iter_15_2:lower():find("flash") and iter_15_3.cast_time + 0.25 > os.clock() and player.pos:dist(iter_15_3.cast_pos) < ove_0_10.w_set.anti_flash_radius:get() and iter_15_3.owner and ove_0_17(iter_15_3.owner) then
						return true
					end
				end
			end
		end
	end
end

local function ove_0_21(arg_16_0)
	if arg_16_0 then
		player:castSpell("pos", 1, arg_16_0)
		ove_0_7.core.set_server_pause()
	else
		player:castSpell("pos", 1, vec3(ove_0_14.seg.endPos.x, ove_0_14.obj.y, ove_0_14.seg.endPos.y))
		ove_0_7.core.set_server_pause()
	end
end

return {
	data = ove_0_11,
	res = ove_0_14,
	get_prediction = ove_0_12,
	get_spell_state = ove_0_16,
	get_action_state = ove_0_17,
	get_anti_melee_state = ove_0_18,
	get_anti_gap_state = ove_0_19,
	get_anti_flash_state = ove_0_20,
	invoke_action = ove_0_21
}

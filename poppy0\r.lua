
local ove_0_5 = module.internal("pred")
local ove_0_6 = module.internal("orb")
local ove_0_7 = module.internal("TS")
local ove_0_8 = module.load("<PERSON>","poppy/menu")
local ove_0_9 = player:spellSlot(3)
local ove_0_10
local ove_0_11 = {
	range = 460,
	delay = 0.45,
	boundingRadiusMod = 1,
	width = 100,
	speed = math.huge
}
local ove_0_12 = {
	speed = 2500,
	range = 900,
	delay = 0,
	boundingRadiusMod = 1,
	width = 40
}

local function ove_0_13(arg_1_0, arg_1_1, arg_1_2)
	-- -- print 1
	if arg_1_2 > ove_0_11.range then
		return false
	end

	local slot_1_0 = ove_0_5.linear.get_prediction(ove_0_11, arg_1_1)

	if not slot_1_0 then
		return false
	end

	if slot_1_0.startPos:dist(slot_1_0.endPos) > ove_0_11.range then
		return false
	end

	local slot_1_1 = player.path.serverPos2D
	local slot_1_2 = slot_1_1 + (slot_1_0.endPos - slot_1_1):norm() * ove_0_11.range
	local slot_1_3 = 1

	for iter_1_0 = 0, objManager.enemies_n - 1 do
		local slot_1_4 = objManager.enemies[iter_1_0]

		if slot_1_4.ptr ~= arg_1_1.ptr and not slot_1_4.isDead and slot_1_4.isVisible and slot_1_4.isTargetable then
			local slot_1_5 = mathf.closest_vec_line_seg(slot_1_4.path.serverPos2D, slot_1_1, slot_1_2)

			if slot_1_5 and slot_1_5:dist(slot_1_4.path.serverPos2D) < 80 then
				slot_1_3 = slot_1_3 + 1
			end
		end
	end

	if slot_1_3 < ove_0_8.r.min_hit:get() then
		return false
	end

	arg_1_0.pos = slot_1_0.endPos

	return true
end

local function ove_0_14()
	-- -- print 2
	if ove_0_9.state ~= 0 then
		return
	end

	if ove_0_10 then
		return
	end

	local slot_2_0 = ove_0_7.get_result(ove_0_13)

	if slot_2_0.pos then
		player:castSpell("pos", 3, vec3(slot_2_0.pos.x, mousePos.y, slot_2_0.pos.y))
		player:castSpell("release", 3, vec3(slot_2_0.pos.x, mousePos.y, slot_2_0.pos.y))
		ove_0_6.core.set_server_pause()

		return true
	end
end

local function ove_0_15()
	-- -- print 3
	if ove_0_9.state ~= 0 then
		return
	end

	if not ove_0_10 then
		return
	end

	ove_0_6.core.set_pause_attack(0.125 + network.latency)

	if not ove_0_8.r.charged:get() then
		return
	end

	if ove_0_10 + 1 > os.clock() then
		return
	end

	local slot_3_0
	local slot_3_1 = 0
	local slot_3_2 = player.path.serverPos2D

	for iter_3_0 = 0, objManager.enemies_n - 1 do
		local slot_3_3 = objManager.enemies[iter_3_0]

		if not slot_3_3.isDead and slot_3_3.isVisible and slot_3_3.isTargetable and slot_3_3.path.serverPos2D:dist(slot_3_2) < 1000 then
			local slot_3_4 = ove_0_5.linear.get_prediction(ove_0_12, slot_3_3)

			if slot_3_4 and slot_3_4.startPos:dist(slot_3_4.endPos) < ove_0_12.range then
				local slot_3_5 = slot_3_3.health * slot_3_3.armor * slot_3_3.spellBlock

				if not slot_3_0 or slot_3_1 < slot_3_5 then
					slot_3_0 = slot_3_4.endPos
					slot_3_1 = slot_3_5
				end
			end
		end
	end

	if slot_3_0 then
		player:castSpell("release", 3, vec3(slot_3_0.x, mousePos.y, slot_3_0.y))
		ove_0_6.core.set_server_pause()

		return true
	end
end

local function ove_0_16()
	-- -- print 4
	if ove_0_10 and ove_0_10 + 4 < os.clock() then
		ove_0_10 = nil
	end
end

local function ove_0_17(arg_5_0)
	-- -- print 5
	local slot_5_0 = arg_5_0.name

	if slot_5_0 == "PoppyR" then
		ove_0_10 = os.clock()
	elseif slot_5_0 == "PoppyRSpell" or slot_5_0 == "PoppyRSpellInstant" then
		ove_0_10 = nil
	end
end

return {
	invoke_auto = ove_0_14,
	invoke_channel = ove_0_15,
	pre_tick = ove_0_16,
	on_process_spell = ove_0_17
}

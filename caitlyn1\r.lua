local lvxbot = module.load(header.id, 'lvxbot/main')

local input = {
  prediction = {
    type = 'Linear',
    --
    range = 3000,
    delay = 1.1,
    speed = 2000,
    width = 160,
    boundingRadiusMod = 1,
  },

  target_selector = {
    type = 'LESS_CAST_AD',
  },

  cast_spell = {
    type = 'pos',
    slot = _R,
    arg1 = function(action)
      local seg = action.ts_result.seg
      local obj = action.ts_result.obj
      return seg.endPos:to3D(obj.y)
    end,
  },

  slot = _R,
  ignore_obj_radius = 2000,
}

local module = lvxbot.expert.create(input)



return module


local ove_0_10 = module.load("Kloader", "Champions/Zed/Spells/W")
local ove_0_11 = module.load("Kloader", "Champions/Zed/Spells/R")
local ove_0_12 = module.load("Kloader", "Lib/DelayAction")
local ove_0_13 = {
	rdt = 0,
	Rdmgcheck = false,
	startTime = 0,
	wdt = 0,
	startTimer = 0,
	GW = 0,
	Rdmgp = false,
	GR = 0,
	Wdmgp = false
}

local function ove_0_14(arg_5_0)
	-- print 5
	if arg_5_0.owner == player then
		if arg_5_0.name == "ZedW" then
			ove_0_13.Wpos = vec3(arg_5_0.endPos)
			ove_0_13.wdt = 5
			ove_0_13.Wdmgp = true
			ove_0_13.startTime = os.clock() + 5
		elseif arg_5_0.name == "ZedW2" then
			ove_0_13.Wpos = vec3(arg_5_0.startPos)
		end

		if arg_5_0.name == "Zed<PERSON>" then
			ove_0_13.Rpos = vec3(arg_5_0.startPos)
			ove_0_13.rdt = 7.5
			ove_0_13.Rdmgp = true
			ove_0_13.Rdmgcheck = true

			ove_0_12.Cast(function()
				-- print 6
				Rdmgcheck = false
			end, 3.75)

			ove_0_13.startTimer = os.clock() + 7.5
		elseif arg_5_0.name == "ZedR2" then
			ove_0_13.Rpos = vec3(player.pos)
		end
	end

	local slot_5_0 = game.time * 1000

	if slot_5_0 > ove_0_13.GR + 10 then
		if ove_0_13.rdt == 7.5 then
			ove_0_12.Cast(function()
				-- print 7
				ove_0_13.rdt = nil
				ove_0_13.Rpos = nil
				ove_0_13.Rdmgp = false
				ove_0_13.startTimer = 0
			end, ove_0_13.rdt)
		end

		ove_0_13.GR = slot_5_0
	end

	local slot_5_1 = game.time * 1000

	if slot_5_1 > ove_0_13.GW + 10 then
		if ove_0_13.wdt == 5 then
			ove_0_12.Cast(function()
				-- print 8
				ove_0_13.wdt = nil
				ove_0_13.Wpos = nil
				ove_0_13.Wdmgp = false
				ove_0_13.startTime = 0
			end, ove_0_13.wdt)
		end

		ove_0_13.GW = slot_5_1
	end
end

cb.add(cb.spell, ove_0_14)

return ove_0_13

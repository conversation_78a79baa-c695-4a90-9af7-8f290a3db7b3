local ove_0_20 = module.load("<PERSON>", "Libraries/HealerAIO_Common")
local ove_0_21 = menu("HealerAIO_Yone_lvxbot", "Healer AIO - Yone")

ove_0_21:header("load_header", "- Healer " .. player.charName .. " -")

local ove_0_22 = module.seek("evade")
local ove_0_23 = ove_0_20.EvadeVersion()

if ove_0_23 == 0 then
	ove_0_20.Evade_Error(ove_0_21)

	return
end

local ove_0_24 = module.internal("orb")
local ove_0_25 = module.internal("pred")
local ove_0_26 = module.internal("TS")
local ove_0_27 = module.load("<PERSON>", "Databases/HealerAIO_SpellDatabase")
local ove_0_28 = module.load("<PERSON>", "Misc/HealerAIO_InfoBox")
local ove_0_29 = module.load("<PERSON>", "Databases/HealerAIO_Dashers")
local ove_0_30 = module.load("<PERSON>", "Databases/HealerAIO_Buffs")
local ove_0_31 = module.load("<PERSON>", "Libraries/HealerAIO_Damage")
local ove_0_32 = module.load("Brian", "Databases/HealerAIO_Items")
local ove_0_33 = module.load("Brian", "Databases/HealerAIO_SpellSlots")
local ove_0_34 = math.max


local ove_0_35 = {}
local ove_0_36 = {}
local ove_0_37 = 0

for iter_0_0 = 0, objManager.enemies_n - 1 do
	local ove_0_38 = objManager.enemies[iter_0_0]

	ove_0_35[ove_0_38.ptr] = ove_0_38.charName
	ove_0_36[ove_0_38.charName] = true
	ove_0_36[ove_0_38.charName .. "_ptr"] = ove_0_38.ptr
	ove_0_37 = ove_0_37 + 1
end

local ove_0_39 = {
	boundingRadiusMod = 1,
	range = 475,
	speed = 5000,
	delay = 0,
	width = 40,
	collision = {
		wall = false,
		hero = false,
		minion = false
	}
}
local ove_0_40 = {
	boundingRadiusMod = 0,
	range = 650,
	width = 100,
	delay = 0,
	speed = math.huge,
	collision = {
		wall = false,
		hero = false,
		minion = false
	}
}
local ove_0_41 = 0
local ove_0_42 = {
	boundingRadiusMod = 0,
	range = 1000,
	width = 135,
	delay = 0.75,
	speed = math.huge,
	collision = {
		wall = false,
		hero = false,
		minion = false
	}
}

ove_0_21:menu("combo", "Combo Settings ")
ove_0_21.combo:header("combo_mode_header", " Spell Priority Settings ")
ove_0_21.combo:dropdown("combo_spell_priority", "Start weaving spells with ", 1, {
	"Q Spell ",
	"W Spell "
})
ove_0_21.combo.combo_spell_priority:set("tooltip", "Controls which spell to weave in first between auto attacks")
ove_0_21.combo:dropdown("combo_spell_priority_eform", "^- When in E form ", 1, {
	"Q Spell ",
	"W Spell "
})
ove_0_21.combo:header("q_combo_header", " Q1-Q2 Settings ")
ove_0_21.combo:boolean("q_combo", "Use Q ", true)
ove_0_21.combo:boolean("q_combo_slowpred", "Enable slow-pred [Q] ", false)
ove_0_21.combo:boolean("q_combo_slowpred_ignore", "^- Ignore slow-pred when weaving ", true)
ove_0_21.combo:boolean("q_combo_slowpred_ignore_delay_bool", "^- Ignore slow-pred if Q delay ", true)
ove_0_21.combo.q_combo_slowpred_ignore_delay_bool:set("tooltip", "Ignores slow-pred if Q delay below X [ms]")
ove_0_21.combo:slider("q_combo_slowpred_ignore_delay", "   ^- Q delay below X [ms] ", 250, 175, 349, 5)
ove_0_21.combo.q_combo_slowpred_ignore_delay:set("tooltip", "Ignore slow-pred if Q delay below X [ms]")
ove_0_21.combo:dropdown("q_combo_aa_mode", "AA -> Q -> AA ", 1, {
	"Where possible ",
	"Always ",
	"Never "
})
ove_0_21.combo:boolean("q_combo_aa_mode_switch", "^- Switch to 'Where possible' if ", false)
ove_0_21.combo:slider("q_combo_aa_mode_switch_delay", "   ^- Q delay below X [ms] ", 250, 175, 349, 5)
ove_0_21.combo:dropdown("q_combo_aa_mode_eform", "^- When in E form ", 1, {
	"Where possible ",
	"Always ",
	"Never "
})
ove_0_21.combo:boolean("q_combo_aa_mode_switch_eform", "   ^- Switch to 'Where possible' if ", false)
ove_0_21.combo:slider("q_combo_aa_mode_switch_delay_eform", "      ^- Q delay below X [ms] ", 250, 175, 349, 5)
ove_0_21.combo:header("q3_combo_header", " Q3 Settings ")
ove_0_21.combo:boolean("q3_combo", "Use Q3 ", true)
ove_0_21.combo:boolean("q3_combo_slowpred", "Enable slow-pred [Q3] ", false)
ove_0_21.combo:boolean("q3_combo_slowpred_ignore", "^- Ignore slow-pred when weaving ", true)
ove_0_21.combo:boolean("q3_combo_slowpred_ignore_delay_bool", "^- Ignore slow-pred if Q3 delay ", false)
ove_0_21.combo.q3_combo_slowpred_ignore_delay_bool:set("tooltip", "Ignores slow-pred if Q3 delay below X [ms]")
ove_0_21.combo:slider("q3_combo_slowpred_ignore_delay", "   ^- Q3 delay below X [ms] ", 250, 175, 349, 5)
ove_0_21.combo.q3_combo_slowpred_ignore_delay:set("tooltip", "Ignore slow-pred if Q3 delay below X [ms]")
ove_0_21.combo:boolean("q3_combo_safe", "Check for enemy skillshots ", true)
ove_0_21.combo.q3_combo_safe:set("tooltip", "Will prevent the script from dashing into enemy skillshots ")
ove_0_21.combo:slider("q3_combo_safe_hp", "^- Only if below X% HP ", 100, 0, 100, 1)
ove_0_21.combo.q3_combo_safe_hp:set("tooltip", "Set it to 100 if you want option above to be enabled permanently ")
ove_0_21.combo:boolean("q3_combo_safe_ignore", "^- Ignore skill check if killable ", true)
ove_0_21.combo:header("w_combo_header", " W Settings ")
ove_0_21.combo:boolean("w_combo", "Use W ", true)
ove_0_21.combo:boolean("w_combo_slowpred", "Enable slow-pred [W] ", false)
ove_0_21.combo:boolean("w_combo_slowpred_ignore", "^- Ignore slow-pred when weaving ", true)
ove_0_21.combo:boolean("w_combo_slowpred_ignore_delay_bool", "^- Ignore slow-pred if W delay ", true)
ove_0_21.combo.w_combo_slowpred_ignore_delay_bool:set("tooltip", "Ignores slow-pred if W delay below X [ms]")
ove_0_21.combo:slider("w_combo_slowpred_ignore_delay", "   ^- W delay below X [ms] ", 280, 250, 500, 5)
ove_0_21.combo.w_combo_slowpred_ignore_delay:set("tooltip", "Ignore slow-pred if W delay below X [ms]")
ove_0_21.combo:dropdown("w_combo_aa_mode", "Use W between spells ", 1, {
	"Where possible ",
	"Always ",
	"Never "
})
ove_0_21.combo:boolean("w_combo_aa_mode_switch", "^- Switch to 'Where possible' if ", false)
ove_0_21.combo:slider("w_combo_aa_mode_switch_delay", "   ^- W delay below X [ms] ", 280, 250, 500, 5)
ove_0_21.combo:dropdown("w_combo_aa_mode_eform", "^- When in E form ", 2, {
	"Where possible ",
	"Always ",
	"Never "
})
ove_0_21.combo:boolean("w_combo_aa_mode_switch_eform", "   ^- Switch to 'Where possible' if ", false)
ove_0_21.combo:slider("w_combo_aa_mode_switch_delay_eform", "      ^- W delay below X [ms] ", 280, 250, 500, 5)
ove_0_21.combo:boolean("w_combo_NOweave_dps", "Dont weave W if lose DPS", true)
ove_0_21:menu("rset", "Auto R Settings ")
ove_0_21.rset:header("r_aaa_header", " Auto R Settings ")
ove_0_21.rset:boolean("r_aaa_enable", "Use auto R ", true)
ove_0_21.rset:boolean("r_aaa_slowpred", "Enable slow-pred [R] ", true)
ove_0_21.rset:header("r_aaa_header1", " Use R If ")
ove_0_21.rset:boolean("r_aaa_cced", "Enemy is CC'ed ", true)
ove_0_21.rset:slider("r_aaa_cced_hp", " ^- If enemy HP below X%", 60, 0, 100, 1)
ove_0_21.rset:slider("r_aaa_cced_hp_yone", " ^- If your HP% above ", 0, 0, 100, 1)
ove_0_21.rset:slider("r_aaa_cced_time", " ^- Min CC time [ms] ", 750, 100, 2000, 10)
ove_0_21.rset.r_aaa_cced_time:set("tooltip", "Yone's R spell delay is 750 ms ")
ove_0_21.rset:boolean("r_aaa_killable", "Enemy is killable [R] ", true)
ove_0_21.rset:slider("r_aaa_killable_hp_yone", " ^- If your HP% above ", 0, 0, 100, 1)
ove_0_21.rset:boolean("r_aaa_killable_outside", " ^- R only if outside combo range ", true)
ove_0_21.rset:boolean("r_aaa_killable_escape", " ^- Check if cant escape ", true)
ove_0_21.rset:header("r_aaa_header2", " Q3 into R ")
ove_0_21.rset:boolean("r_aaa_q3r", "Attempt Q3 into R ", true)
ove_0_21.rset:slider("r_aaa_q3r_hp", " ^- If enemy below X HP% ", 50, 0, 100, 1)
ove_0_21.rset:slider("r_aaa_q3r_hp_yone", " ^- If your HP% above ", 0, 0, 100, 1)
ove_0_21.rset:boolean("r_aaa_q3r_outside", " ^- R only if outside combo range ", true)
ove_0_21.rset:boolean("r_aaa_q3r_escape", " ^- Check if cant escape ", true)
ove_0_21:menu("harass", "Harass Settings ")
ove_0_21.harass:header("harass_mode_header", " Spell Priority Settings ")
ove_0_21.harass:dropdown("harass_spell_priority", "Start weaving spells with ", 1, {
	"Q Spell ",
	"W Spell "
})
ove_0_21.harass.harass_spell_priority:set("tooltip", "Controls which spell to weave in first between auto attacks")
ove_0_21.harass:dropdown("harass_spell_priority_eform", "^- When in E form ", 1, {
	"Q Spell ",
	"W Spell "
})
ove_0_21.harass:header("q_harass_header", " Q1-Q2 Settings ")
ove_0_21.harass:boolean("q_harass", "Use Q ", true)
ove_0_21.harass:boolean("q_harass_slowpred", "Enable slow-pred [Q] ", false)
ove_0_21.harass:boolean("q_harass_slowpred_ignore", "^- Ignore slow-pred when weaving ", true)
ove_0_21.harass:boolean("q_harass_slowpred_ignore_delay_bool", "^- Ignore slow-pred if Q delay ", true)
ove_0_21.harass.q_harass_slowpred_ignore_delay_bool:set("tooltip", "Ignores slow-pred if Q delay below X [ms]")
ove_0_21.harass:slider("q_harass_slowpred_ignore_delay", "   ^- Q delay below X [ms] ", 250, 175, 349, 5)
ove_0_21.harass.q_harass_slowpred_ignore_delay:set("tooltip", "Ignore slow-pred if Q delay below X [ms]")
ove_0_21.harass:dropdown("q_harass_aa_mode", "AA -> Q -> AA ", 1, {
	"Where possible ",
	"Always ",
	"Never "
})
ove_0_21.harass:boolean("q_harass_aa_mode_switch", "^- Switch to 'Where possible' if ", false)
ove_0_21.harass:slider("q_harass_aa_mode_switch_delay", "   ^- Q delay below X [ms] ", 250, 175, 349, 5)
ove_0_21.harass:dropdown("q_harass_aa_mode_eform", "^- When in E form ", 1, {
	"Where possible ",
	"Always ",
	"Never "
})
ove_0_21.harass:boolean("q_harass_aa_mode_switch_eform", "   ^- Switch to 'Where possible' if ", false)
ove_0_21.harass:slider("q_harass_aa_mode_switch_delay_eform", "      ^- Q delay below X [ms] ", 250, 175, 349, 1)
ove_0_21.harass:header("q3_harass_header", " Q3 Settings ")
ove_0_21.harass:boolean("q3_harass", "Use Q3 ", true)
ove_0_21.harass:boolean("q3_harass_slowpred", "Enable slow-pred [Q3] ", false)
ove_0_21.harass:boolean("q3_harass_slowpred_ignore", "^- Ignore slow-pred when weaving ", true)
ove_0_21.harass:boolean("q3_harass_slowpred_ignore_delay_bool", "^- Ignore slow-pred if Q3 delay ", true)
ove_0_21.harass.q3_harass_slowpred_ignore_delay_bool:set("tooltip", "Ignores slow-pred if Q3 delay below X [ms]")
ove_0_21.harass:slider("q3_harass_slowpred_ignore_delay", "   ^- Q3 delay below X [ms] ", 250, 175, 349, 5)
ove_0_21.harass.q3_harass_slowpred_ignore_delay:set("tooltip", "Ignore slow-pred if Q3 delay below X [ms]")
ove_0_21.harass:boolean("q3_harass_safe", "Check for enemy skillshots ", true)
ove_0_21.harass.q3_harass_safe:set("tooltip", "Will prevent the script from dashing into enemy skillshots ")
ove_0_21.harass:slider("q3_harass_safe_hp", "^- Only if below X% HP ", 100, 0, 100, 1)
ove_0_21.harass.q3_harass_safe_hp:set("tooltip", "Set it to 100 if you want option above to be enabled permanently ")
ove_0_21.harass:boolean("q3_harass_safe_ignore", "^- Ignore skill check if killable ", true)
ove_0_21.harass:header("w_harass_header", " W Settings ")
ove_0_21.harass:boolean("w_harass", "Use W ", true)
ove_0_21.harass:boolean("w_harass_slowpred", "Enable slow-pred [W] ", false)
ove_0_21.harass:boolean("w_harass_slowpred_ignore", "^- Ignore slow-pred when weaving ", true)
ove_0_21.harass:boolean("w_harass_slowpred_ignore_delay_bool", "^- Ignore slow-pred if W delay ", true)
ove_0_21.harass.w_harass_slowpred_ignore_delay_bool:set("tooltip", "Ignores slow-pred if W delay below X [ms]")
ove_0_21.harass:slider("w_harass_slowpred_ignore_delay", "   ^- W delay below X [ms] ", 280, 250, 500, 5)
ove_0_21.harass.w_harass_slowpred_ignore_delay:set("tooltip", "Ignore slow-pred if W delay below X [ms]")
ove_0_21.harass:dropdown("w_harass_aa_mode", "Use W between spells ", 1, {
	"Where possible ",
	"Always ",
	"Never "
})
ove_0_21.harass:boolean("w_harass_aa_mode_switch", "^- Switch to 'Where possible' if ", false)
ove_0_21.harass:slider("w_harass_aa_mode_switch_delay", "   ^- W delay below X [ms] ", 280, 250, 500, 5)
ove_0_21.harass:dropdown("w_harass_aa_mode_eform", "^- When in E form ", 1, {
	"Where possible ",
	"Always ",
	"Never "
})
ove_0_21.harass:boolean("w_harass_aa_mode_switch_eform", "   ^- Switch to 'Where possible' if ", false)
ove_0_21.harass:slider("w_harass_aa_mode_switch_delay_eform", "      ^- W delay below X [ms] ", 280, 250, 500, 5)
ove_0_21:menu("safe", "Safe Q3 Settings")
ove_0_21.safe:header("q3_safe_header", " Safe Q3 Settings ")
ove_0_21.safe:keybind("q3_safe_toggle", "Safe Q3 toggle: ", nil, "A")
ove_0_21.safe.q3_safe_toggle:set("tooltip", "Wont let Yone to dash if he will end up under the enemy turret ")
ove_0_21.safe:boolean("q3_safe_ignore", "^- Ignore if already under turret ", true)
ove_0_21.safe.q3_safe_ignore:set("tooltip", "Ignores the setting above, if Yone is already under turret ")
ove_0_21.safe:header("q3_safe_header2", " Modes where active ")
ove_0_21.safe:boolean("q3_safe_combo", "Enable in Combo ", true)
ove_0_21.safe:boolean("q3_safe_harass", "Enable in Harass ", true)
ove_0_21.safe:boolean("q3_safe_laneclear", "Enable in Laneclear ", true)
ove_0_21.safe:boolean("q3_safe_panic_clear", "Enable in Panic Clear", false)
ove_0_21.safe:boolean("q3_safe_laneclear_harr", "Enable in Laneclear Harass ", true)
ove_0_21:menu("ee", "E2 Settings ")
ove_0_21.ee:header("e2_ee_header", " E2 Settings ")
ove_0_21.ee:boolean("e2_ee_enable", "Enable E2 usage ", true)
ove_0_21.ee:header("e2_ee_header2", " E2 CC Skillshots ")
ove_0_21.ee:boolean("e2_ee_cc", "Use E2 to dodge CC skillshots ", true)
ove_0_21.ee:slider("e2_ee_cc_hp", "^- Only if below X% HP ", 50, 0, 100, 1)
ove_0_21.ee:menu("cc_blacklist", "Enemy CC Skillshots ")

local ove_0_43 = {
	[0] = "Q",
	"W",
	"E",
	[-1] = "P",
	[3] = "R"
}
local ove_0_44 = 0
local ove_0_45 = 0

for iter_0_1, iter_0_2 in pairs(ove_0_27) do
	for iter_0_3 = 0, objManager.enemies_n - 1 do
		local ove_0_46 = objManager.enemies[iter_0_3]

		if not ove_0_27[iter_0_1] then
			return
		end

		if iter_0_2.hard_cc and iter_0_2.charName == ove_0_46.charName then
			if ove_0_21.ee.cc_blacklist.header_cc_spell == nil then
				ove_0_21.ee.cc_blacklist:header("header_cc_spell", " Enemy CC Spells ")
			end

			if ove_0_21.ee.cc_blacklist[iter_0_2.charName] == nil then
				ove_0_21.ee.cc_blacklist:menu(iter_0_2.charName, iter_0_2.charName .. " ")
				ove_0_21.ee.cc_blacklist[iter_0_2.charName]:header(iter_0_2.charName .. "_h", " " .. iter_0_2.charName .. " ")
			end

			ove_0_21.ee.cc_blacklist[iter_0_2.charName]:boolean("Dodge_" .. iter_0_1, iter_0_2.charName .. " [" .. (ove_0_43[iter_0_2.slot] or "?") .. "]" .. " | " .. iter_0_1, true)

			ove_0_44 = 1
		end
	end
end

if ove_0_44 == 0 then
	ove_0_21.ee.cc_blacklist:header("cc_none_h", " No CC spells were found ")
end

ove_0_21.ee:header("e2_ee_header3", " E2 All Skillshots ")
ove_0_21.ee:boolean("e2_ee_all", "Use E2 to dodge all skillshots ", true)
ove_0_21.ee:slider("e2_ee_all_hp", "^- Only if below X% HP ", 25, 0, 100, 1)
ove_0_21.ee:menu("all_blacklist", "All Enemy Skillshots ")

for iter_0_4, iter_0_5 in pairs(ove_0_27) do
	for iter_0_6 = 0, objManager.enemies_n - 1 do
		local ove_0_47 = objManager.enemies[iter_0_6]

		if not ove_0_27[iter_0_4] then
			return
		end

		if iter_0_5.charName == ove_0_47.charName then
			if ove_0_21.ee.all_blacklist.header_all_spell == nil then
				ove_0_21.ee.all_blacklist:header("header_all_spell", " Enemy Spells ")
			end

			if ove_0_21.ee.all_blacklist[iter_0_5.charName] == nil then
				ove_0_21.ee.all_blacklist:menu(iter_0_5.charName, iter_0_5.charName .. " ")
				ove_0_21.ee.all_blacklist[iter_0_5.charName]:header(iter_0_5.charName .. "_h", " " .. iter_0_5.charName .. " ")
			end

			ove_0_21.ee.all_blacklist[iter_0_5.charName]:boolean("Dodge_" .. iter_0_4, iter_0_5.charName .. " [" .. (ove_0_43[iter_0_5.slot] or "?") .. "]" .. " | " .. iter_0_4, true)

			ove_0_45 = 1
		end
	end
end

if ove_0_45 == 0 then
	ove_0_21.ee.all_blacklist:header("all_none_h", " No spells were found ")
end

ove_0_21:menu("farm", "Farm Settings ")
ove_0_21.farm:header("farm_header", " Farming Keys ")
ove_0_21.farm:keybind("farm_toggle", "Farm toggle key: ", nil, "Z")
ove_0_21.farm:keybind("panic_clear_toggle", "Panic clear key: ", "LMB", nil)
ove_0_21.farm.panic_clear_toggle:set("tooltip", "LMB - Left Mouse Button. You can use this to stack up your Q")
ove_0_21.farm:button("lmb_button_reset", " ^- reset back to LMB", "Click me", function()
	-- print 5
	ove_0_21.farm.panic_clear_toggle:set("key", "LMB")
	ove_0_21.farm.panic_clear_toggle:set("toggle", "")
end)
ove_0_21.farm.lmb_button_reset:set("tooltip", "Resets the keybind back to the Left Mouse Button.  ")
ove_0_21.farm:header("farm_header2", " Farm Settings ")
ove_0_21.farm:menu("lane", "Lane Clear ")
ove_0_21.farm.lane:header("lane_head_q", " Q1-Q2 Settings ")
ove_0_21.farm.lane:boolean("lane_q_enable", "Use Q ", true)
ove_0_21.farm.lane:boolean("lane_q_stop", "Dont Q if enemies nearby ", false)
ove_0_21.farm.lane:header("lane_head_q3", " Q3 Settings ")
ove_0_21.farm.lane:boolean("lane_q3_enable", "Use Q3 ", true)
ove_0_21.farm.lane:boolean("lane_q3_stop", "Dont Q3 if enemies nearby ", true)
ove_0_21.farm.lane:header("lane_head_w", " W Settings ")
ove_0_21.farm.lane:boolean("lane_w_cannon", "Use W to kill cannon minions ", true)
ove_0_21.farm.lane:boolean("lane_w_stop", "Dont W if enemies nearby ", false)
ove_0_21.farm:menu("jung", "Jungle Clear ")
ove_0_21.farm.jung:header("jung_head_q", " Q1-Q2 Settings ")
ove_0_21.farm.jung:boolean("jung_q_enable", "Use Q ", true)
ove_0_21.farm.jung:boolean("jung_q_stop", "Dont Q if enemies nearby ", false)
ove_0_21.farm.jung:boolean("jung_q_aggro", "Q only aggroed mobs", false)
ove_0_21.farm.jung:header("jung_head_q3", " Q3 Settings ")
ove_0_21.farm.jung:boolean("jung_q3_enable", "Use Q3 ", true)
ove_0_21.farm.jung:boolean("jung_q3_stop", "Dont Q3 if enemies nearby ", false)
ove_0_21.farm.jung:boolean("jung_q3_aggro", "Q3 only aggroed mobs", true)
ove_0_21.farm:menu("last", "Last Hit ")
ove_0_21.farm.last:header("last_head_q", " Q1-Q2 Settings ")
ove_0_21.farm.last:boolean("last_q_enable", "Use Q ", true)
ove_0_21.farm.last:boolean("last_q_helper", "Only Q if cant kill with AA ", true)
ove_0_21.farm.last.last_q_helper:set("tooltip", "Will Q only in situations where the target cant be killed with AA ")
ove_0_21.farm.last:boolean("last_q_stop", "Dont Q if enemies nearby ", false)
ove_0_21.farm.last:header("last_head_q3", " Q3 Settings ")
ove_0_21.farm.last:boolean("last_q3_enable", "Use Q3 ", true)
ove_0_21.farm.last:boolean("last_q3_helper", "Only Q if cant kill with AA ", true)
ove_0_21.farm.last:boolean("last_q3_stop", "Dont Q3 if enemies nearby ", true)
ove_0_21.farm.last:header("last_head_w", " W Settings ")
ove_0_21.farm.last:boolean("last_w_cannon", "Use W to kill cannon minions ", true)
ove_0_21.farm.last:boolean("last_w_stop", "Dont W if enemies nearby ", false)
ove_0_21.farm:menu("harr", "Harass ")
ove_0_21.farm.harr:header("q_harr_header33", " Laneclear Harass ")
ove_0_21.farm.harr:boolean("harr_turn_off_tower", "Dont harass when under enemy turret ", true)
ove_0_21.farm.harr:header("w_harr_header", " W Settings ")
ove_0_21.farm.harr:boolean("w_harr", "Use W ", true)
ove_0_21.farm.harr:boolean("w_harr_slowpred", "Enable slow-pred [W] ", true)
ove_0_21.farm.harr:boolean("w_harr_slowpred_ignore_delay_bool", "^- Ignore slow-pred if W delay ", true)
ove_0_21.farm.harr.w_harr_slowpred_ignore_delay_bool:set("tooltip", "Ignores slow-pred if W delay below X [ms]")
ove_0_21.farm.harr:slider("w_harr_slowpred_ignore_delay", "   ^- W delay below X [ms] ", 280, 250, 500, 5)
ove_0_21.farm.harr.w_harr_slowpred_ignore_delay:set("tooltip", "Ignore slow-pred if W delay below X [ms]")
ove_0_21.farm.harr:header("q3_harr_header", " Q3 Settings ")
ove_0_21.farm.harr:boolean("q3_harr", "Use Q3 ", true)
ove_0_21.farm.harr:boolean("q3_harr_safe", "Check for enemy skillshots ", true)
ove_0_21.farm.harr.q3_harr_safe:set("tooltip", "Will prevent the script from dashing into enemy skillshots ")
ove_0_21.farm.harr:slider("q3_harr_safe_hp", "^- Only if below X% HP ", 100, 0, 100, 1)
ove_0_21.farm.harr.q3_harr_safe_hp:set("tooltip", "Set it to 100 if you want 'Safe Q3' to be enabled permanently ")
ove_0_21.farm.harr:boolean("q3_harr_slowpred", "Enable slow-pred [Q3] ", true)
ove_0_21.farm.harr:boolean("q3_harr_slowpred_ignore_delay_bool", "^- Ignore slow-pred if Q3 delay ", false)
ove_0_21.farm.harr.q3_harr_slowpred_ignore_delay_bool:set("tooltip", "Ignores slow-pred if Q3 delay below X [ms]")
ove_0_21.farm.harr:slider("q3_harr_slowpred_ignore_delay", "   ^- Q3 delay below X [ms] ", 250, 175, 349, 5)
ove_0_21.farm.harr.q3_harr_slowpred_ignore_delay:set("tooltip", "Ignore slow-pred if Q3 delay below X [ms]")
ove_0_21:menu("flee", "Flee Settings ")
ove_0_21.flee:header("flee_header", " Flee Settings ")
ove_0_21.flee:keybind("flee_key", "Flee key: ", "T", nil)
ove_0_21.flee:header("flee_q_header", " Q Settings ")
ove_0_21.flee:boolean("q_flee", "Use Q1-Q2 on enemy ", true)
ove_0_21.flee:boolean("q3_flee", "Use Q3 to mouse ", true)
ove_0_21.flee:header("flee_w_header", " W Settings ")
ove_0_21.flee:boolean("w_flee", "Use W on enemy ", true)
ove_0_21.flee:slider("w_flee_delay", "Only if W delay below X [ms] ", 500, 250, 500, 5)
ove_0_21.flee.w_flee_delay:set("tooltip", "Delay of your W cast. If set at 500, W will be always used. ")
ove_0_21:menu("misc", "Misc ")
ove_0_21.misc:header("force_r_header", " Force R Settings ")
ove_0_21.misc:keybind("force_r_key", "Force R key: ", "MMB", nil)
ove_0_21.misc:boolean("force_r_slowpred", "Enable slow-pred [R] ", false)
ove_0_21.misc:header("antigap_header", " Anti-Gapclose Settings ")
ove_0_21.misc:boolean("w_gap_enable", "Use W on gapclosers ", true)
ove_0_21.misc:boolean("w_gap_turret", " ^- Dont W if under enemy turret ", true)
ove_0_21.misc.w_gap_turret:set("tooltip", "Dont W if under enemy turret")
ove_0_21.misc:boolean("w_gap_turret_allow", "    ^- Allow W if targeted", true)
ove_0_21.misc.w_gap_turret_allow:set("tooltip", "Allows W if Yone or his ally is being targeted by that enemy turret")
ove_0_21.misc:menu("gap_blacklist", "Anti-Gapclose Blacklist ")

for iter_0_7 = 0, objManager.enemies_n - 1 do
	local ove_0_48 = objManager.enemies[iter_0_7]

	ove_0_21.misc.gap_blacklist:boolean(ove_0_35[ove_0_48.ptr], "Block: " .. ove_0_35[ove_0_48.ptr], false)
end

ove_0_21.misc:header("follow_up_r_header", " Follow-up R Settings ")
ove_0_21.misc:boolean("follow_up_enable", "Enable follow-up R", true)
ove_0_21.misc.follow_up_enable:set("tooltip", "Uses R on multiple CC'ed enemies in a line")
ove_0_21.misc:slider("follow_up_count", "Min CC'ed enemies to R", 2, 2, 5, 1)
ove_0_21.misc:boolean("follow_up_E", " ^- Use E before R if possible ", true)
ove_0_21.misc:boolean("follow_up_flash", " ^- Use flash if needed ", true)
ove_0_21.misc:slider("follow_up_cc_time", "Min CC'ed time [ms] ", 750, 100, 2000, 10)
ove_0_21.misc.follow_up_cc_time:set("tooltip", "Yone's R spell delay is 750 ms ")
ove_0_21.misc:slider("follow_up_hp_enemies", "Enemies min HP% to R", 100, 0, 100, 1)
ove_0_21.misc.follow_up_hp_enemies:set("tooltip", "If CC'ed enemies HP% are above set threshold, the script wont count them for follow-up R ")
ove_0_21.misc:slider("follow_up_hp_self", "Your min HP% to R", 0, 0, 100, 1)
ove_0_21.misc.follow_up_hp_self:set("tooltip", "If your HP% is below set threshold, the script wont use follow-up R ")
ove_0_21.misc:header("ks_flash_header", " Killsteal Settings ")
ove_0_21.misc:boolean("ks_flash_enable", "Enable KS using flash ", true)
ove_0_21.misc:slider("ks_flash_health", "Dont KS if your HP% below ", 0, 0, 100, 1)
ove_0_21:menu("draws", "Draw Settings ")
ove_0_21.draws:header("h2", " Draw Settings ")
ove_0_21.draws:dropdown("draw_yone_color", "Color Palette: ", 1, {
	"Classic Yone ",
	"Pink Yone "
})
ove_0_21.draws:boolean("drawq", "Draw Q Range ", true)
ove_0_21.draws:boolean("draww", "Draw W Range ", true)
ove_0_21.draws:boolean("drawr", "Draw R Range ", false)
ove_0_21.draws:boolean("draw_e_timer", "Draw E Timer ", true)
ove_0_21.draws:boolean("draw_panic_clear", "Draw Panic Clear Indicator ", false)
ove_0_21.draws:boolean("draw_bog", "Draw Slow-Pred Indicator", true)
ove_0_21.draws.draw_bog:set("tooltip", "Draws 'Force R' slow pred indicator, if active.")
ove_0_21:menu("keys", "Key Settings ")
ove_0_21.keys:header("h1", " Key Settings ")
ove_0_21.keys:keybind("combokey", "Combo Key ", "Space", nil)
ove_0_21.keys:keybind("harasskey", "Harass Key ", "C", nil)
ove_0_21.keys:keybind("clearkey", "Lane Clear Key ", "V", nil)
ove_0_21.keys:keybind("lastkey", "Last Hit ", "X", nil)
ove_0_21:header("headree", "- Healer AIO -")
ove_0_21:header("headree1", "- - Healer AIO - -")
ove_0_21:header("headree21", "- - - Healer AIO - - -")
ove_0_21:header("headree213", "Healer AIO")
ove_0_21.headree:set("visible", false)
ove_0_21.headree1:set("visible", false)
ove_0_21.headree21:set("visible", false)
ove_0_21.headree213:set("visible", false)

local function ove_0_49()
	-- print 6
	if not menu:isopen() then
		return
	end

	if ove_0_21.combo.q_combo_slowpred:get() then
		ove_0_21.combo.q_combo_slowpred_ignore:set("visible", true)
		ove_0_21.combo.q_combo_slowpred_ignore_delay_bool:set("visible", true)

		if ove_0_21.combo.q_combo_slowpred_ignore_delay_bool:get() then
			ove_0_21.combo.q_combo_slowpred_ignore_delay:set("visible", true)
		else
			ove_0_21.combo.q_combo_slowpred_ignore_delay:set("visible", false)
		end
	else
		ove_0_21.combo.q_combo_slowpred_ignore:set("visible", false)
		ove_0_21.combo.q_combo_slowpred_ignore_delay_bool:set("visible", false)
		ove_0_21.combo.q_combo_slowpred_ignore_delay:set("visible", false)
	end

	if ove_0_21.combo.q_combo_aa_mode:get() == 2 then
		ove_0_21.combo.q_combo_aa_mode_switch:set("visible", true)

		if ove_0_21.combo.q_combo_aa_mode_switch:get() then
			ove_0_21.combo.q_combo_aa_mode_switch_delay:set("visible", true)
		else
			ove_0_21.combo.q_combo_aa_mode_switch_delay:set("visible", false)
		end
	else
		ove_0_21.combo.q_combo_aa_mode_switch:set("visible", false)
		ove_0_21.combo.q_combo_aa_mode_switch_delay:set("visible", false)
	end

	if ove_0_21.combo.q_combo_aa_mode_eform:get() == 2 then
		ove_0_21.combo.q_combo_aa_mode_switch_eform:set("visible", true)

		if ove_0_21.combo.q_combo_aa_mode_switch_eform:get() then
			ove_0_21.combo.q_combo_aa_mode_switch_delay_eform:set("visible", true)
		else
			ove_0_21.combo.q_combo_aa_mode_switch_delay_eform:set("visible", false)
		end
	else
		ove_0_21.combo.q_combo_aa_mode_switch_eform:set("visible", false)
		ove_0_21.combo.q_combo_aa_mode_switch_delay_eform:set("visible", false)
	end

	if ove_0_21.combo.q3_combo_slowpred:get() then
		ove_0_21.combo.q3_combo_slowpred_ignore:set("visible", true)
		ove_0_21.combo.q3_combo_slowpred_ignore_delay_bool:set("visible", true)

		if ove_0_21.combo.q3_combo_slowpred_ignore_delay_bool:get() then
			ove_0_21.combo.q3_combo_slowpred_ignore_delay:set("visible", true)
		else
			ove_0_21.combo.q3_combo_slowpred_ignore_delay:set("visible", false)
		end
	else
		ove_0_21.combo.q3_combo_slowpred_ignore:set("visible", false)
		ove_0_21.combo.q3_combo_slowpred_ignore_delay_bool:set("visible", false)
		ove_0_21.combo.q3_combo_slowpred_ignore_delay:set("visible", false)
	end

	if ove_0_21.combo.w_combo_slowpred:get() then
		ove_0_21.combo.w_combo_slowpred_ignore:set("visible", true)
		ove_0_21.combo.w_combo_slowpred_ignore_delay_bool:set("visible", true)

		if ove_0_21.combo.w_combo_slowpred_ignore_delay_bool:get() then
			ove_0_21.combo.w_combo_slowpred_ignore_delay:set("visible", true)
		else
			ove_0_21.combo.w_combo_slowpred_ignore_delay:set("visible", false)
		end
	else
		ove_0_21.combo.w_combo_slowpred_ignore:set("visible", false)
		ove_0_21.combo.w_combo_slowpred_ignore_delay_bool:set("visible", false)
		ove_0_21.combo.w_combo_slowpred_ignore_delay:set("visible", false)
	end

	if ove_0_21.combo.w_combo_aa_mode:get() == 2 then
		ove_0_21.combo.w_combo_aa_mode_switch:set("visible", true)

		if ove_0_21.combo.w_combo_aa_mode_switch:get() then
			ove_0_21.combo.w_combo_aa_mode_switch_delay:set("visible", true)
		else
			ove_0_21.combo.w_combo_aa_mode_switch_delay:set("visible", false)
		end
	else
		ove_0_21.combo.w_combo_aa_mode_switch:set("visible", false)
		ove_0_21.combo.w_combo_aa_mode_switch_delay:set("visible", false)
	end

	if ove_0_21.combo.w_combo_aa_mode_eform:get() == 2 then
		ove_0_21.combo.w_combo_aa_mode_switch_eform:set("visible", true)

		if ove_0_21.combo.w_combo_aa_mode_switch_eform:get() then
			ove_0_21.combo.w_combo_aa_mode_switch_delay_eform:set("visible", true)
		else
			ove_0_21.combo.w_combo_aa_mode_switch_delay_eform:set("visible", false)
		end
	else
		ove_0_21.combo.w_combo_aa_mode_switch_eform:set("visible", false)
		ove_0_21.combo.w_combo_aa_mode_switch_delay_eform:set("visible", false)
	end

	if ove_0_21.harass.q_harass_slowpred:get() then
		ove_0_21.harass.q_harass_slowpred_ignore:set("visible", true)
		ove_0_21.harass.q_harass_slowpred_ignore_delay_bool:set("visible", true)

		if ove_0_21.harass.q_harass_slowpred_ignore_delay_bool:get() then
			ove_0_21.harass.q_harass_slowpred_ignore_delay:set("visible", true)
		else
			ove_0_21.harass.q_harass_slowpred_ignore_delay:set("visible", false)
		end
	else
		ove_0_21.harass.q_harass_slowpred_ignore:set("visible", false)
		ove_0_21.harass.q_harass_slowpred_ignore_delay_bool:set("visible", false)
		ove_0_21.harass.q_harass_slowpred_ignore_delay:set("visible", false)
	end

	if ove_0_21.harass.q_harass_aa_mode:get() == 2 then
		ove_0_21.harass.q_harass_aa_mode_switch:set("visible", true)

		if ove_0_21.harass.q_harass_aa_mode_switch:get() then
			ove_0_21.harass.q_harass_aa_mode_switch_delay:set("visible", true)
		else
			ove_0_21.harass.q_harass_aa_mode_switch_delay:set("visible", false)
		end
	else
		ove_0_21.harass.q_harass_aa_mode_switch:set("visible", false)
		ove_0_21.harass.q_harass_aa_mode_switch_delay:set("visible", false)
	end

	if ove_0_21.harass.q_harass_aa_mode_eform:get() == 2 then
		ove_0_21.harass.q_harass_aa_mode_switch_eform:set("visible", true)

		if ove_0_21.harass.q_harass_aa_mode_switch_eform:get() then
			ove_0_21.harass.q_harass_aa_mode_switch_delay_eform:set("visible", true)
		else
			ove_0_21.harass.q_harass_aa_mode_switch_delay_eform:set("visible", false)
		end
	else
		ove_0_21.harass.q_harass_aa_mode_switch_eform:set("visible", false)
		ove_0_21.harass.q_harass_aa_mode_switch_delay_eform:set("visible", false)
	end

	if ove_0_21.harass.q3_harass_slowpred:get() then
		ove_0_21.harass.q3_harass_slowpred_ignore:set("visible", true)
		ove_0_21.harass.q3_harass_slowpred_ignore_delay_bool:set("visible", true)

		if ove_0_21.harass.q3_harass_slowpred_ignore_delay_bool:get() then
			ove_0_21.harass.q3_harass_slowpred_ignore_delay:set("visible", true)
		else
			ove_0_21.harass.q3_harass_slowpred_ignore_delay:set("visible", false)
		end
	else
		ove_0_21.harass.q3_harass_slowpred_ignore:set("visible", false)
		ove_0_21.harass.q3_harass_slowpred_ignore_delay_bool:set("visible", false)
		ove_0_21.harass.q3_harass_slowpred_ignore_delay:set("visible", false)
	end

	if ove_0_21.harass.w_harass_slowpred:get() then
		ove_0_21.harass.w_harass_slowpred_ignore:set("visible", true)
		ove_0_21.harass.w_harass_slowpred_ignore_delay_bool:set("visible", true)

		if ove_0_21.harass.w_harass_slowpred_ignore_delay_bool:get() then
			ove_0_21.harass.w_harass_slowpred_ignore_delay:set("visible", true)
		else
			ove_0_21.harass.w_harass_slowpred_ignore_delay:set("visible", false)
		end
	else
		ove_0_21.harass.w_harass_slowpred_ignore:set("visible", false)
		ove_0_21.harass.w_harass_slowpred_ignore_delay_bool:set("visible", false)
		ove_0_21.harass.w_harass_slowpred_ignore_delay:set("visible", false)
	end

	if ove_0_21.harass.w_harass_aa_mode:get() == 2 then
		ove_0_21.harass.w_harass_aa_mode_switch:set("visible", true)

		if ove_0_21.harass.w_harass_aa_mode_switch:get() then
			ove_0_21.harass.w_harass_aa_mode_switch_delay:set("visible", true)
		else
			ove_0_21.harass.w_harass_aa_mode_switch_delay:set("visible", false)
		end
	else
		ove_0_21.harass.w_harass_aa_mode_switch:set("visible", false)
		ove_0_21.harass.w_harass_aa_mode_switch_delay:set("visible", false)
	end

	if ove_0_21.harass.w_harass_aa_mode_eform:get() == 2 then
		ove_0_21.harass.w_harass_aa_mode_switch_eform:set("visible", true)

		if ove_0_21.harass.w_harass_aa_mode_switch_eform:get() then
			ove_0_21.harass.w_harass_aa_mode_switch_delay_eform:set("visible", true)
		else
			ove_0_21.harass.w_harass_aa_mode_switch_delay_eform:set("visible", false)
		end
	else
		ove_0_21.harass.w_harass_aa_mode_switch_eform:set("visible", false)
		ove_0_21.harass.w_harass_aa_mode_switch_delay_eform:set("visible", false)
	end

	if ove_0_21.farm.harr.w_harr_slowpred:get() then
		ove_0_21.farm.harr.w_harr_slowpred_ignore_delay_bool:set("visible", true)

		if ove_0_21.farm.harr.w_harr_slowpred_ignore_delay_bool:get() then
			ove_0_21.farm.harr.w_harr_slowpred_ignore_delay:set("visible", true)
		else
			ove_0_21.farm.harr.w_harr_slowpred_ignore_delay:set("visible", false)
		end
	else
		ove_0_21.farm.harr.w_harr_slowpred_ignore_delay_bool:set("visible", false)
		ove_0_21.farm.harr.w_harr_slowpred_ignore_delay:set("visible", false)
	end

	if ove_0_21.farm.harr.q3_harr_slowpred:get() then
		ove_0_21.farm.harr.q3_harr_slowpred_ignore_delay_bool:set("visible", true)

		if ove_0_21.farm.harr.q3_harr_slowpred_ignore_delay_bool:get() then
			ove_0_21.farm.harr.q3_harr_slowpred_ignore_delay:set("visible", true)
		else
			ove_0_21.farm.harr.q3_harr_slowpred_ignore_delay:set("visible", false)
		end
	else
		ove_0_21.farm.harr.q3_harr_slowpred_ignore_delay_bool:set("visible", false)
		ove_0_21.farm.harr.q3_harr_slowpred_ignore_delay:set("visible", false)
	end

	if ove_0_21.farm.harr.q3_harr_safe:get() then
		ove_0_21.farm.harr.q3_harr_safe_hp:set("visible", true)
	else
		ove_0_21.farm.harr.q3_harr_safe_hp:set("visible", false)
	end

	if ove_0_21.combo.q3_combo_safe:get() then
		ove_0_21.combo.q3_combo_safe_hp:set("visible", true)
		ove_0_21.combo.q3_combo_safe_ignore:set("visible", true)
	else
		ove_0_21.combo.q3_combo_safe_hp:set("visible", false)
		ove_0_21.combo.q3_combo_safe_ignore:set("visible", false)
	end

	if ove_0_21.harass.q3_harass_safe:get() then
		ove_0_21.harass.q3_harass_safe_hp:set("visible", true)
		ove_0_21.harass.q3_harass_safe_ignore:set("visible", true)
	else
		ove_0_21.harass.q3_harass_safe_hp:set("visible", false)
		ove_0_21.harass.q3_harass_safe_ignore:set("visible", false)
	end

	if ove_0_21.misc.w_gap_enable:get() then
		ove_0_21.misc.w_gap_turret:set("visible", true)
		ove_0_21.misc.gap_blacklist:set("visible", true)

		if ove_0_21.misc.w_gap_turret:get() then
			ove_0_21.misc.w_gap_turret_allow:set("visible", true)
		else
			ove_0_21.misc.w_gap_turret_allow:set("visible", false)
		end
	else
		ove_0_21.misc.w_gap_turret:set("visible", false)
		ove_0_21.misc.w_gap_turret_allow:set("visible", false)
		ove_0_21.misc.gap_blacklist:set("visible", false)
	end
end

local ove_0_50 = false
local ove_0_51 = 0

local function ove_0_52()
	-- print 7
	if ove_0_21.misc.force_r_slowpred:get() then
		if ove_0_21.misc.force_r_key:get() then
			if ove_0_50 == false and os.clock() > ove_0_51 then
				ove_0_50 = true
				ove_0_51 = os.clock() + 0.3
			end

			if ove_0_50 == true and os.clock() > ove_0_51 then
				ove_0_50 = false
				ove_0_51 = os.clock() + 0.3
			end
		end
	else
		ove_0_50 = false
	end
end

local function ove_0_53(arg_8_0, arg_8_1, arg_8_2)
	-- print 8
	if arg_8_2 <= ove_0_39.range then
		arg_8_0.obj = arg_8_1

		return true
	end
end

local function ove_0_54()
	-- print 9
	return ove_0_26.get_result(ove_0_53).obj
end

local function ove_0_55(arg_10_0, arg_10_1, arg_10_2)
	-- print 10
	if arg_10_2 <= ove_0_40.range then
		arg_10_0.obj = arg_10_1

		return true
	end
end

local function ove_0_56()
	-- print 11
	return ove_0_26.get_result(ove_0_55).obj
end

local function ove_0_57(arg_12_0, arg_12_1, arg_12_2)
	-- print 12
	if arg_12_2 <= spellE.range then
		arg_12_0.obj = arg_12_1

		return true
	end
end

local function ove_0_58()
	-- print 13
	return ove_0_26.get_result(ove_0_57).obj
end

local function ove_0_59(arg_14_0, arg_14_1, arg_14_2)
	-- print 14
	if arg_14_2 <= ove_0_42.range and not arg_14_1.isZombie then
		arg_14_0.obj = arg_14_1

		return true
	end
end

local function ove_0_60()
	-- print 15
	return ove_0_26.get_result(ove_0_59).obj
end

local ove_0_61
local ove_0_62 = 0

local function ove_0_63(arg_16_0)
	-- print 16
	local slot_16_0 = arg_16_0.owner

	if slot_16_0 and slot_16_0 == player and arg_16_0.name == "TestCubeRender10Vision" then
		ove_0_61 = arg_16_0
		ove_0_62 = game.time + 5.35
	end
end

local function ove_0_64(arg_17_0)
	-- print 17
	if ove_0_61 and arg_17_0.ptr == ove_0_61.ptr then
		ove_0_61 = nil
	end
end

local ove_0_65

local function ove_0_66(arg_18_0)
	-- print 18
	local slot_18_0 = arg_18_0.spell.owner

	if slot_18_0 and slot_18_0 == player and arg_18_0.name == "YoneQ3Missile" then
		ove_0_65 = arg_18_0
	end
end

local function ove_0_67(arg_19_0)
	-- print 19
	if ove_0_65 and ove_0_65.ptr == arg_19_0.ptr then
		ove_0_65 = nil
	end
end

local ove_0_68 = ove_0_20.GetFlashSlot()
local ove_0_69 = false
local ove_0_70 = false

local function ove_0_71(arg_20_0)
	-- print 20
	local slot_20_0 = arg_20_0.owner

	if slot_20_0 and slot_20_0 == player then
		if ove_0_61 and arg_20_0.name == "YoneE" then
			ove_0_61 = nil
		end

		if arg_20_0.name == "YoneR" and ove_0_50 then
			ove_0_50 = false
		end

		if arg_20_0.isBasicAttack and (player.buff[ove_0_30.Rune_HailOfBladesBuff] or player.buff[ove_0_30.Rune_HailOfBladesOmniBuff]) then
			ove_0_70 = true

			ove_0_24.core.set_pause_attack(arg_20_0.windUpTime * 3.5)
		end
	end
end

local function ove_0_72()
	-- print 21
	if player:spellSlot(0).name == "YoneQ" then
		return false
	end

	return true
end

local function ove_0_73(arg_22_0, arg_22_1, arg_22_2, arg_22_3, arg_22_4, arg_22_5)
	-- print 22
	local slot_22_0 = arg_22_4
	local slot_22_1 = arg_22_5
	local slot_22_2 = arg_22_2
	local slot_22_3 = arg_22_3
	local slot_22_4 = arg_22_1.delay

	if player.buff[ove_0_30.ExhaustDebuff] then
		slot_22_2 = slot_22_2 * ove_0_31.ExhaustReductionMod()
		slot_22_3 = slot_22_3 * ove_0_31.ExhaustReductionMod()
	end

	if ove_0_36.Sona and player.buff[ove_0_30.Sona_W_Debuff] then
		slot_22_2 = slot_22_2 * ove_0_31.Sona_W_ReductionMod()
		slot_22_3 = slot_22_3 * ove_0_31.Sona_W_ReductionMod()
	end

	if arg_22_0.type == TYPE_HERO then
		if ove_0_36.Kindred and arg_22_0.buff[ove_0_30.Kindred_R_Buff] then
			return 0, 0
		end

		if arg_22_0.buff[ove_0_30.BUFF_ENUM_INVULNERABILITY] or arg_22_0.buff[ove_0_30.BUFF_ENUM_SPELLSHIELD] then
			return 0, 0
		end

		if ove_0_36.Pantheon and arg_22_0.ptr == ove_0_36.Pantheon_ptr and arg_22_0.buff[ove_0_30.Pantheon_E_Buff] and slot_22_4 <= arg_22_0.buff[ove_0_30.Pantheon_E_Buff].endTime - game.time then
			return 0, 0
		end

		if ove_0_36.Sion and arg_22_0.ptr == ove_0_36.Sion_ptr and arg_22_0.buff[ove_0_30.SionPassiveBuff] then
			return 0, 0
		end

		if ove_0_31.Tryndamere_R_isActive(arg_22_0, slot_22_4, false, slot_22_4) then
			return 0, 0
		end

		if arg_22_0.buff[ove_0_30.ChemtechDragonSoulBuff] then
			slot_22_2 = slot_22_2 * ove_0_31.ChemtechDragonSoulReductionMod()
			slot_22_3 = slot_22_3 * ove_0_31.ChemtechDragonSoulReductionMod()
		end

		if player.buff[ove_0_30.ChallengingSmiteDebuff] then
			slot_22_2 = slot_22_2 * ove_0_31.ChallengingSmiteReductionMod(arg_22_0)
			slot_22_3 = slot_22_3 * ove_0_31.ChallengingSmiteReductionMod(arg_22_0)
		end

		local slot_22_5, slot_22_6 = ove_0_31.Garen_W_State(arg_22_0, slot_22_4, false, slot_22_4)

		if slot_22_5 then
			slot_22_2 = slot_22_2 * ove_0_31.Garen_W_ReductionMod()
			slot_22_3 = slot_22_3 * ove_0_31.Garen_W_ReductionMod()
		end

		if slot_22_6 and not slot_22_5 then
			local slot_22_7 = ove_0_31.Garen_W_Shield(arg_22_0)
			local slot_22_8 = slot_22_2 - (slot_22_2 - slot_22_2 * ove_0_31.Garen_W_ReductionMod() + slot_22_7)
			local slot_22_9 = slot_22_8 < 0 and math.abs(slot_22_8) or 0

			slot_22_2 = ove_0_34(0, slot_22_8)
			slot_22_3 = ove_0_34(0, slot_22_3 - (slot_22_3 - slot_22_3 * ove_0_31.Garen_W_ReductionMod() + slot_22_9))
		end

		if ove_0_31.MasterYi_W_isActive(arg_22_0, slot_22_4, false, slot_22_4) then
			slot_22_2 = slot_22_2 * ove_0_31.MasterYi_W_ReductionMod(arg_22_0)
			slot_22_3 = slot_22_3 * ove_0_31.MasterYi_W_ReductionMod(arg_22_0)
		end

		if ove_0_31.Belveth_E_isActive(arg_22_0, slot_22_4, false, slot_22_4) then
			slot_22_2 = slot_22_2 * ove_0_31.Belveth_E_ReductionMod(arg_22_0)
			slot_22_3 = slot_22_3 * ove_0_31.Belveth_E_ReductionMod(arg_22_0)
		end

		if ove_0_31.Irelia_W_isActive(arg_22_0, slot_22_4, false, slot_22_4) then
			slot_22_2 = slot_22_2 * ove_0_31.Irelia_W_Physical_ReductionMod(arg_22_0)
			slot_22_3 = slot_22_3 * ove_0_31.Irelia_W_Magical_ReductionMod(arg_22_0)
		end

		if ove_0_31.Gragas_W_isActive(arg_22_0, slot_22_4, false, slot_22_4) then
			slot_22_2 = slot_22_2 * ove_0_31.Gragas_W_ReductionMod(arg_22_0)
			slot_22_3 = slot_22_3 * ove_0_31.Gragas_W_ReductionMod(arg_22_0)
		end

		if ove_0_31.Warwick_E_isActive(arg_22_0, slot_22_4, false, slot_22_4) then
			slot_22_2 = slot_22_2 * ove_0_31.Warwick_E_ReductionMod(arg_22_0)
			slot_22_3 = slot_22_3 * ove_0_31.Warwick_E_ReductionMod(arg_22_0)
		end

		if ove_0_31.Galio_W_isActive(arg_22_0, slot_22_4, false, slot_22_4) then
			slot_22_2 = slot_22_2 * ove_0_31.Galio_W_Physical_ReductionMod(arg_22_0)
			slot_22_3 = slot_22_3 * ove_0_31.Galio_W_Magical_ReductionMod(arg_22_0)
		end

		local slot_22_10, slot_22_11, slot_22_12 = ove_0_31.Braum_E_State(arg_22_0, slot_22_4, false, slot_22_4)

		if slot_22_10 then
			if slot_22_12 then
				return 0, 0
			end

			slot_22_2 = slot_22_2 * ove_0_31.Braum_E_ReductionMod(arg_22_0)
			slot_22_3 = slot_22_3 * ove_0_31.Braum_E_ReductionMod(arg_22_0)
		end

		if slot_22_11 and not slot_22_10 then
			return 0, 0
		end

		if ove_0_31.Alistar_R_isActive(arg_22_0, slot_22_4, false, slot_22_4) then
			slot_22_2 = slot_22_2 * ove_0_31.Alistar_R_ReductionMod(arg_22_0)
			slot_22_3 = slot_22_3 * ove_0_31.Alistar_R_ReductionMod(arg_22_0)
		end

		if ove_0_31.MalzaharPassive_isActive(arg_22_0, slot_22_4) then
			slot_22_2 = slot_22_2 * ove_0_31.MalzaharPassiveReductionMod()
			slot_22_3 = slot_22_3 * ove_0_31.MalzaharPassiveReductionMod()
		end

		if ove_0_36.Kassadin and arg_22_0.ptr == ove_0_36.Kassadin_ptr then
			slot_22_3 = slot_22_3 * ove_0_31.KassadinPassiveReductionMod()
		end

		if ove_0_31.BlitzcrankPassive_isActive(arg_22_0, slot_22_4) then
			local slot_22_13 = slot_22_2 - ove_0_31.BlitzcrankPassiveShield(arg_22_0)
			local slot_22_14 = slot_22_13 < 0 and math.abs(slot_22_13) or 0

			slot_22_2 = ove_0_34(0, slot_22_13)
			slot_22_3 = ove_0_34(0, slot_22_3 - slot_22_14)
		end

		if ove_0_31.Annie_E_isActive(arg_22_0, slot_22_4) then
			local slot_22_15 = slot_22_2 - ove_0_31.Annie_E_Shield(arg_22_0)
			local slot_22_16 = slot_22_15 < 0 and math.abs(slot_22_15) or 0

			slot_22_2 = ove_0_34(0, slot_22_15)
			slot_22_3 = ove_0_34(0, slot_22_3 - slot_22_16)
		end

		if ove_0_36.Yasuo and arg_22_0.ptr == ove_0_36.Yasuo_ptr then
			local slot_22_17 = slot_22_2 - ove_0_31.YasuoPassiveShield(arg_22_0, 20)
			local slot_22_18 = slot_22_17 < 0 and math.abs(slot_22_17) or 0

			slot_22_2 = ove_0_34(0, slot_22_17)
			slot_22_3 = ove_0_34(0, slot_22_3 - slot_22_18)
		end

		if arg_22_0.buff[ove_0_30.Rune_BoneplatingBuff] then
			local slot_22_19 = slot_22_2 - ove_0_31.BonePlatingShield(arg_22_0)
			local slot_22_20 = slot_22_19 < 0 and math.abs(slot_22_19) or 0

			slot_22_2 = ove_0_34(0, slot_22_19)
			slot_22_3 = ove_0_34(0, slot_22_3 - slot_22_20)
		end

		if arg_22_0.buff[ove_0_30.Rune_NullifyingOrbBuff] then
			slot_22_3 = ove_0_34(0, slot_22_3 - ove_0_31.NullifyingOrbShield(arg_22_0))
		end

		for iter_22_0 = 0, 5 do
			if arg_22_0:itemID(iter_22_0) == ove_0_32.Hexdrinker.id and slot_22_4 >= arg_22_0:spellSlot(iter_22_0 + 6).cooldown then
				slot_22_3 = ove_0_34(0, slot_22_3 - ove_0_31.HexdrinkerShield(arg_22_0))
			end

			if arg_22_0:itemID(iter_22_0) == ove_0_32.MawOfMalmortius.id and slot_22_4 >= arg_22_0:spellSlot(iter_22_0 + 6).cooldown then
				slot_22_3 = ove_0_34(0, slot_22_3 - ove_0_31.MawOfMalmortiusShield(arg_22_0))
			end

			if (arg_22_0:itemID(iter_22_0) == ove_0_32.ImmortalShieldbow.id or arg_22_0:itemID(iter_22_0) == ove_0_32.ImmortalShieldbow2.id) and slot_22_4 >= arg_22_0:spellSlot(iter_22_0 + 6).cooldown then
				local slot_22_21 = slot_22_2 - ove_0_31.ImmortalShieldbowShield(arg_22_0)
				local slot_22_22 = slot_22_21 < 0 and math.abs(slot_22_21) or 0

				slot_22_2 = ove_0_34(0, slot_22_21)
				slot_22_3 = ove_0_34(0, slot_22_3 - slot_22_22)
			end
		end

		local slot_22_23 = slot_22_2 - arg_22_0.allShield - arg_22_0.healthRegenRate * arg_22_1.delay
		local slot_22_24 = slot_22_23 < 0 and math.abs(slot_22_23) or 0

		slot_22_2 = ove_0_34(0, slot_22_23 - arg_22_0.physicalShield)
		slot_22_3 = ove_0_34(0, slot_22_3 - slot_22_24 - arg_22_0.magicalShield)
	end

	if arg_22_0.type == TYPE_MINION then
		slot_22_2 = slot_22_2 * ove_0_31.BaronBuffedMinionReductionMod(arg_22_0)
		slot_22_3 = slot_22_3 * ove_0_31.BaronBuffedMinionReductionMod(arg_22_0)
	end

	return slot_22_2, slot_22_3
end

local ove_0_74 = 0
local ove_0_75 = 0
local ove_0_76 = 0
local ove_0_77 = 0
local ove_0_78 = 0
local ove_0_79 = 0
local ove_0_80 = 0
local ove_0_81 = false
local ove_0_82 = false
local ove_0_83 = false
local ove_0_84 = false
local ove_0_85 = false
local ove_0_86 = false
local ove_0_87 = false
local ove_0_88 = false
local ove_0_89 = false
local ove_0_90 = false
local ove_0_91 = false
local ove_0_92 = false
local ove_0_93 = false
local ove_0_94 = false
local ove_0_95 = {
	20,
	40,
	60,
	80,
	100
}

local function ove_0_96(arg_23_0)
	-- print 23
	if player:spellSlot(0).level <= 0 then
		return 0
	end

	local slot_23_0 = mathf.clamp(1, 18, player.levelRef)
	local slot_23_1 = mathf.clamp(1, 18, arg_23_0.levelRef)
	local slot_23_2 = 0
	local slot_23_3 = 0

	if os.clock() > ove_0_75 then
		ove_0_81 = false
		ove_0_83 = false
		ove_0_82 = false
		ove_0_84 = false
		ove_0_85 = false
		ove_0_86 = false
		ove_0_87 = false
		ove_0_88 = false
		ove_0_89 = false
		ove_0_90 = false
		ove_0_91 = false
		ove_0_92 = false
		ove_0_93 = false
		ove_0_94 = false

		for iter_23_0 = 0, 5 do
			local slot_23_4 = player:itemID(iter_23_0)

			if slot_23_4 == ove_0_32.Sheen.id then
				ove_0_81 = true
			end

			if slot_23_4 == ove_0_32.EssenceReaver.id then
				ove_0_83 = true
			end

			if slot_23_4 == ove_0_32.LichBane.id then
				ove_0_84 = true
			end

			if slot_23_4 == ove_0_32.TrinityForce.id or slot_23_4 == ove_0_32.TrinityForce2.id then
				ove_0_82 = true
			end

			if slot_23_4 == ove_0_32.DivineSunderer.id or slot_23_4 == ove_0_32.DivineSunderer2.id then
				ove_0_85 = true
			end

			if slot_23_4 == ove_0_32.InfinityEdge.id then
				ove_0_89 = true
			end

			if slot_23_4 == ove_0_32.KircheisShard.id then
				ove_0_86 = true
			end

			if slot_23_4 == ove_0_32.Stormrazor.id then
				ove_0_87 = true
			end

			if slot_23_4 == ove_0_32.RapidFirecannon.id then
				ove_0_88 = true
			end

			if slot_23_4 == ove_0_32.BladeOfTheRuinedKing.id then
				ove_0_90 = true
			end

			if slot_23_4 == ove_0_32.WitsEnd.id then
				ove_0_91 = true
			end

			if slot_23_4 == ove_0_32.TitanicHydra.id then
				ove_0_92 = true
			end

			if slot_23_4 == ove_0_32.NashorsTooth.id then
				ove_0_93 = true
			end

			if slot_23_4 == ove_0_32.RecurveBow.id then
				ove_0_94 = true
			end
		end

		ove_0_75 = os.clock() + 5
	end

	local slot_23_5 = player.crit >= 0.6 and 0.29399 or 0
	local slot_23_6 = ove_0_20.GetTotalAD() * 1.05

	if player.crit >= 1 then
		slot_23_6 = ove_0_89 and ove_0_20.GetTotalAD() * (1.46995 + slot_23_5) or ove_0_20.GetTotalAD() * 1.46995
	end

	local slot_23_7 = ove_0_20.CalculatePhysicalDamage(arg_23_0, ove_0_95[player:spellSlot(0).level] + slot_23_6, player)

	if ove_0_90 then
		slot_23_7 = slot_23_7 + ove_0_31.BladeOfTheRuinedKingPassiveMeleeDamage(arg_23_0, player)
	end

	for iter_23_1 = 0, 5 do
		local slot_23_8 = player:itemID(iter_23_1)
		local slot_23_9 = player:spellSlot(iter_23_1 + 6)

		if slot_23_8 == ove_0_32.Sheen.id and slot_23_9.state ~= 0 then
			ove_0_76 = game.time + 0.055
		end

		if slot_23_8 == ove_0_32.LichBane.id and slot_23_9.state ~= 0 then
			ove_0_78 = game.time + 0.055
		end

		if (slot_23_8 == ove_0_32.TrinityForce.id or slot_23_8 == ove_0_32.TrinityForce2.id) and slot_23_9.state ~= 0 then
			ove_0_79 = game.time + 0.055
		end

		if (slot_23_8 == ove_0_32.DivineSunderer.id or slot_23_8 == ove_0_32.DivineSunderer2.id) and slot_23_9.state ~= 0 then
			ove_0_80 = game.time + 0.055
		end

		if slot_23_8 == ove_0_32.EssenceReaver.id and slot_23_9.state ~= 0 then
			ove_0_77 = game.time + 0.055
		end
	end

	local slot_23_10 = ove_0_20.SpellBladeType(ove_0_81, ove_0_83, ove_0_84, ove_0_82, ove_0_85)

	if slot_23_10 == 1 and ove_0_76 < game.time then
		slot_23_7 = slot_23_7 + ove_0_31.SheenDamage(arg_23_0, player)
	end

	if slot_23_10 == 2 and ove_0_77 < game.time then
		slot_23_7 = slot_23_7 + ove_0_31.EssenceReaverDamage(arg_23_0, player)
	end

	if slot_23_10 == 3 and ove_0_78 < game.time then
		slot_23_2 = slot_23_2 + ove_0_31.LichBaneDamage(arg_23_0, player)
	end

	if slot_23_10 == 4 and ove_0_79 < game.time then
		slot_23_7 = slot_23_7 + ove_0_31.TrinityForceDamage(arg_23_0, player)
	end

	if slot_23_10 == 5 and ove_0_80 < game.time then
		slot_23_7 = slot_23_7 + ove_0_31.DivineSundererMeleeDamage(arg_23_0, player)
	end

	if player.buff[ove_0_30.StaticItemsChargeBuff] and player.buff[ove_0_30.StaticItemsChargeBuff].stacks2 >= 100 then
		if ove_0_86 then
			slot_23_2 = slot_23_2 + ove_0_20.CalculateMagicDamage(arg_23_0, ove_0_31.KircheisShardRawDamage(), player)
		end

		if ove_0_87 then
			slot_23_2 = slot_23_2 + ove_0_20.CalculateMagicDamage(arg_23_0, ove_0_31.StormrazorRawDamage(), player)
		end

		if ove_0_88 then
			slot_23_2 = slot_23_2 + ove_0_20.CalculateMagicDamage(arg_23_0, ove_0_31.RapidFirecannonRawDamage(), player)
		end
	end

	if ove_0_91 then
		slot_23_2 = slot_23_2 + ove_0_31.WitsEndDamage(arg_23_0, player)
	end

	if ove_0_94 then
		slot_23_7 = slot_23_7 + ove_0_31.RecurveBowDamage(arg_23_0, player)
	end

	if ove_0_92 then
		slot_23_7 = slot_23_7 + ove_0_31.TitanicHydraMeleeDamage(arg_23_0, player)
	end

	if ove_0_93 then
		slot_23_2 = slot_23_2 + ove_0_31.NashorsToothDamage(arg_23_0, player)
	end

	local slot_23_11 = ove_0_39.delay + network.latency * 0.5

	if arg_23_0.type == TYPE_HERO then
		slot_23_7 = slot_23_7 * ove_0_31.CoupDeGraceDamageBuffMod(arg_23_0, slot_23_11)
		slot_23_7 = slot_23_7 * ove_0_31.CutDownDamageBuffMod(arg_23_0)
		slot_23_7 = slot_23_7 * ove_0_31.LastStandDamageBuffMod(slot_23_11)
		slot_23_7 = slot_23_7 * ove_0_31.PressTheAttackExposedBuffMod(arg_23_0, player, slot_23_11)
		slot_23_2 = slot_23_2 * ove_0_31.CoupDeGraceDamageBuffMod(arg_23_0, slot_23_11)
		slot_23_2 = slot_23_2 * ove_0_31.CutDownDamageBuffMod(arg_23_0)
		slot_23_2 = slot_23_2 * ove_0_31.LastStandDamageBuffMod(slot_23_11)
		slot_23_2 = slot_23_2 * ove_0_31.PressTheAttackExposedBuffMod(arg_23_0, player, slot_23_11)
	end

	local slot_23_12, slot_23_13 = ove_0_73(arg_23_0, ove_0_39, slot_23_7, slot_23_2, slot_23_0, slot_23_1)
	local slot_23_14 = slot_23_12 + slot_23_13

	if arg_23_0.type == TYPE_HERO then
		if ove_0_31.ElderDragonWillExecuteRawCheck(arg_23_0, slot_23_14, slot_23_11, false) then
			return 999999
		end

		if ove_0_31.TheCollectorExecuteRawCheck(arg_23_0, slot_23_14, slot_23_11, false) then
			return 999999
		end
	end

	return slot_23_14
end

local ove_0_97 = {
	5,
	10,
	15,
	20,
	25
}
local ove_0_98 = {
	0.055,
	0.06,
	0.065,
	0.07,
	0.075
}
local ove_0_99 = {
	40,
	50,
	60,
	70,
	80,
	90,
	100,
	110,
	130,
	150,
	170,
	190,
	210,
	250,
	290,
	330,
	370,
	410
}

local function ove_0_100(arg_24_0)
	-- print 24
	local slot_24_0 = mathf.clamp(1, 18, player.levelRef)
	local slot_24_1 = mathf.clamp(1, 18, arg_24_0.levelRef)

	if player:spellSlot(1).level <= 0 then
		return 0
	end

	local slot_24_2 = 0
	local slot_24_3 = 0
	local slot_24_4 = 0
	local slot_24_5 = 0

	if arg_24_0.type == TYPE_HERO then
		local slot_24_6 = ove_0_20.CalculateMagicDamage(arg_24_0, ove_0_97[player:spellSlot(1).level] + arg_24_0.maxHealth * ove_0_98[player:spellSlot(1).level], player)
		local slot_24_7 = ove_0_20.CalculatePhysicalDamage(arg_24_0, ove_0_97[player:spellSlot(1).level] + arg_24_0.maxHealth * ove_0_98[player:spellSlot(1).level], player)
		local slot_24_8 = ove_0_40.delay + network.latency * 0.5
		local slot_24_9 = slot_24_7 * ove_0_31.CoupDeGraceDamageBuffMod(arg_24_0, slot_24_8) * ove_0_31.CutDownDamageBuffMod(arg_24_0) * ove_0_31.LastStandDamageBuffMod(slot_24_8) * ove_0_31.PressTheAttackExposedBuffMod(arg_24_0, player, slot_24_8)
		local slot_24_10 = slot_24_6 * ove_0_31.CoupDeGraceDamageBuffMod(arg_24_0, slot_24_8) * ove_0_31.CutDownDamageBuffMod(arg_24_0) * ove_0_31.LastStandDamageBuffMod(slot_24_8) * ove_0_31.PressTheAttackExposedBuffMod(arg_24_0, player, slot_24_8)

		slot_24_5, slot_24_4 = ove_0_73(arg_24_0, ove_0_40, slot_24_9, slot_24_10, slot_24_0, slot_24_1)

		local slot_24_11 = slot_24_5 + slot_24_4

		if ove_0_31.ElderDragonWillExecuteRawCheck(arg_24_0, slot_24_11, slot_24_8, false) then
			return 999999
		end

		if ove_0_31.TheCollectorExecuteRawCheck(arg_24_0, slot_24_11, slot_24_8, false) then
			return 999999
		end
	end

	if arg_24_0.type == TYPE_MINION then
		local slot_24_12 = mathf.clamp(ove_0_99[slot_24_0] / 2, math.huge, ove_0_20.CalculateMagicDamage(arg_24_0, ove_0_97[player:spellSlot(1).level] + arg_24_0.maxHealth * ove_0_98[player:spellSlot(1).level], player))
		local slot_24_13 = mathf.clamp(ove_0_99[slot_24_0] / 2, math.huge, ove_0_20.CalculatePhysicalDamage(arg_24_0, ove_0_97[player:spellSlot(1).level] + arg_24_0.maxHealth * ove_0_98[player:spellSlot(1).level], player))

		slot_24_5, slot_24_4 = ove_0_73(arg_24_0, ove_0_40, slot_24_13, slot_24_12, slot_24_0, slot_24_1)
	end

	return slot_24_5 + slot_24_4
end

local ove_0_101 = {
	100,
	200,
	300
}

local function ove_0_102(arg_25_0)
	-- print 25
	local slot_25_0 = mathf.clamp(1, 18, player.levelRef)
	local slot_25_1 = mathf.clamp(1, 18, arg_25_0.levelRef)

	if player:spellSlot(3).level <= 0 then
		return 0
	end

	local slot_25_2 = 0
	local slot_25_3 = 0
	local slot_25_4 = ove_0_20.CalculateMagicDamage(arg_25_0, ove_0_101[player:spellSlot(3).level] + ove_0_20.GetBonusAD() * 0.4, player)
	local slot_25_5 = ove_0_20.CalculatePhysicalDamage(arg_25_0, ove_0_101[player:spellSlot(3).level] + ove_0_20.GetBonusAD() * 0.4, player)
	local slot_25_6 = ove_0_42.delay + network.latency * 0.5

	if arg_25_0.type == TYPE_HERO then
		slot_25_5 = slot_25_5 * ove_0_31.CoupDeGraceDamageBuffMod(arg_25_0, slot_25_6)
		slot_25_5 = slot_25_5 * ove_0_31.CutDownDamageBuffMod(arg_25_0)
		slot_25_5 = slot_25_5 * ove_0_31.LastStandDamageBuffMod(slot_25_6)
		slot_25_5 = slot_25_5 * ove_0_31.PressTheAttackExposedBuffMod(arg_25_0, player, slot_25_6)
		slot_25_4 = slot_25_4 * ove_0_31.CoupDeGraceDamageBuffMod(arg_25_0, slot_25_6)
		slot_25_4 = slot_25_4 * ove_0_31.CutDownDamageBuffMod(arg_25_0)
		slot_25_4 = slot_25_4 * ove_0_31.LastStandDamageBuffMod(slot_25_6)
		slot_25_4 = slot_25_4 * ove_0_31.PressTheAttackExposedBuffMod(arg_25_0, player, slot_25_6)
	end

	local slot_25_7, slot_25_8 = ove_0_73(arg_25_0, ove_0_42, slot_25_5, slot_25_4, slot_25_0, slot_25_1)

	if arg_25_0.type == TYPE_HERO then
		local slot_25_9 = slot_25_7 + slot_25_8

		if ove_0_31.ElderDragonWillExecuteRawCheck(arg_25_0, slot_25_9, slot_25_6, false) then
			return 999999
		end

		if ove_0_31.TheCollectorExecuteRawCheck(arg_25_0, slot_25_9, slot_25_6, false) then
			return 999999
		end
	end

	return slot_25_7 + slot_25_8
end

local function ove_0_103()
	-- print 26
	if ove_0_61 then
		return true
	end

	return false
end

local function ove_0_104(arg_27_0)
	-- print 27
	local slot_27_0 = ove_0_20.VectorExtend(player.path.serverPos2D, arg_27_0, 451)
	local slot_27_1 = 40
	local slot_27_2 = (player.pos2D - arg_27_0):norm()
	local slot_27_3 = slot_27_2:perp2()
	local slot_27_4 = slot_27_2:perp1()
	local slot_27_5 = player.pos2D + slot_27_4 * slot_27_1
	local slot_27_6 = player.pos2D + slot_27_3 * slot_27_1
	local slot_27_7 = 870
	local slot_27_8 = (player.pos2D - arg_27_0):norm()
	local slot_27_9 = slot_27_8:perp2()
	local slot_27_10 = slot_27_0 + slot_27_8:perp1() * slot_27_1
	local slot_27_11 = slot_27_0 + slot_27_9 * slot_27_1

	for iter_27_0 = 0, objManager.turrets.size[TEAM_ENEMY] - 1 do
		local slot_27_12 = objManager.turrets[TEAM_ENEMY][iter_27_0]

		if slot_27_12 and not slot_27_12.isDead then
			local slot_27_13 = slot_27_12.pos2D
			local slot_27_14, slot_27_15 = mathf.sect_line_circle(player.path.serverPos2D, slot_27_0, slot_27_13, slot_27_7)
			local slot_27_16, slot_27_17 = mathf.sect_line_circle(slot_27_5, slot_27_6, slot_27_13, slot_27_7)
			local slot_27_18, slot_27_19 = mathf.sect_line_circle(slot_27_10, slot_27_11, slot_27_13, slot_27_7)
			local slot_27_20, slot_27_21 = mathf.sect_line_circle(slot_27_5, slot_27_10, slot_27_13, slot_27_7)
			local slot_27_22, slot_27_23 = mathf.sect_line_circle(slot_27_6, slot_27_11, slot_27_13, slot_27_7)

			if slot_27_12.pos2D:distSqr(slot_27_0) > 748225 and slot_27_12.pos2D:distSqr(player.pos2D) <= 810000 then
				return true
			end

			if slot_27_12.pos2D:distSqr(slot_27_0) <= 810000 or slot_27_14 or slot_27_15 or slot_27_16 or slot_27_17 or slot_27_18 or slot_27_19 or slot_27_20 or slot_27_21 or slot_27_22 or slot_27_23 then
				return false
			end
		end
	end

	return true
end

local function ove_0_105(arg_28_0)
	-- print 28
	local slot_28_0 = ove_0_20.VectorExtend(player.path.serverPos2D, arg_28_0, 451)

	if not ove_0_22.core.is_action_safe(slot_28_0, 1500, ove_0_39.delay) then
		return false
	end

	return true
end

local function ove_0_106(arg_29_0, arg_29_1, arg_29_2)
	-- print 29
	local slot_29_0 = arg_29_0.range * arg_29_0.range
	local slot_29_1 = player.boundingRadius + player.attackRange

	if slot_29_1 * slot_29_1 >= arg_29_1.startPos:distSqr(arg_29_2.path.serverPos2D) then
		return true
	end

	if ove_0_20.IsHardLockedLinearAPI(arg_29_0, arg_29_1, arg_29_2) then
		return true
	end

	if ove_0_25.trace.newpath(arg_29_2, 0.033, 0.5) then
		return true
	end
end

local function ove_0_107()
	-- print 30
	local slot_30_0 = ove_0_72()

	if not (slot_30_0 and ove_0_21.combo.q3_combo or ove_0_21.combo.q_combo):get() then
		return
	end

	local slot_30_1 = ove_0_103()

	if ove_0_21.combo.q_combo_aa_mode:get() == 2 and not slot_30_0 and not slot_30_1 then
		if ove_0_21.combo.q_combo_aa_mode_switch:get() then
			if ove_0_39.delay > ove_0_21.combo.q_combo_aa_mode_switch_delay:get() / 1000 then
				return
			end
		else
			return
		end
	end

	if ove_0_21.combo.q_combo_aa_mode_eform:get() == 2 and not slot_30_0 and slot_30_1 then
		if ove_0_21.combo.q_combo_aa_mode_switch_eform:get() then
			if ove_0_39.delay > ove_0_21.combo.q_combo_aa_mode_switch_delay_eform:get() / 1000 then
				return
			end
		else
			return
		end
	end

	local slot_30_2 = ove_0_54()

	if ove_0_20.IsValidTarget(slot_30_2) and player:spellSlot(0).state == 0 then
		local slot_30_3 = ove_0_25.linear.get_prediction(ove_0_39, slot_30_2)
		local slot_30_4 = ove_0_39.range * ove_0_39.range

		if slot_30_3 and slot_30_4 >= slot_30_3.startPos:distSqr(slot_30_3.endPos) then
			if ove_0_21.combo.q_combo_aa_mode:get() == 1 and not slot_30_0 and not slot_30_1 and ove_0_24.combat.target then
				return
			end

			if ove_0_21.combo.q_combo_aa_mode_eform:get() == 1 and not slot_30_0 and slot_30_1 and ove_0_24.combat.target then
				return
			end

			if ove_0_21.safe.q3_safe_toggle:get() and ove_0_21.safe.q3_safe_combo:get() and slot_30_0 and not ove_0_104(slot_30_3.endPos) then
				if ove_0_21.safe.q3_safe_ignore:get() and not ove_0_20.is_under_enemy_turret(player.pos2D) then
					return
				else
					return
				end
			end

			if ove_0_21.combo.q3_combo_safe:get() and slot_30_0 and ove_0_20.GetPercentHealth(player) <= ove_0_21.combo.q3_combo_safe_hp:get() and not ove_0_105(slot_30_3.endPos) then
				if ove_0_21.combo.q3_combo_safe_ignore:get() then
					if ove_0_96(slot_30_2) <= slot_30_2.health then
						return
					end
				else
					return
				end
			end

			if ove_0_21.combo.q_combo_slowpred:get() and not slot_30_0 then
				if ove_0_21.combo.q_combo_slowpred_ignore_delay_bool:get() then
					if ove_0_39.delay > ove_0_21.combo.q_combo_slowpred_ignore_delay:get() / 1000 and not ove_0_106(ove_0_39, slot_30_3, slot_30_2) then
						return
					end
				elseif not ove_0_106(ove_0_39, slot_30_3, slot_30_2) then
					return
				end
			end

			if ove_0_21.combo.q3_combo_slowpred:get() and slot_30_0 then
				if ove_0_21.combo.q3_combo_slowpred_ignore_delay_bool:get() then
					if ove_0_39.delay > ove_0_21.combo.q3_combo_slowpred_ignore_delay:get() / 1000 and not ove_0_106(ove_0_39, slot_30_3, slot_30_2) then
						return
					end
				elseif not ove_0_106(ove_0_39, slot_30_3, slot_30_2) then
					return
				end
			end

			if ove_0_24.core.can_action() then
				player:castSpell("pos", 0, vec3(slot_30_3.endPos.x, slot_30_2.pos.y, slot_30_3.endPos.y))
			end
		end
	end
end

local function ove_0_108()
	-- print 31
	local slot_31_0 = ove_0_72()

	if not (slot_31_0 and ove_0_21.combo.q3_combo or ove_0_21.combo.q_combo):get() then
		return
	end

	local slot_31_1 = ove_0_103()

	if ove_0_21.combo.q_combo_aa_mode:get() == 3 and not slot_31_1 then
		return
	end

	if ove_0_21.combo.q_combo_aa_mode_eform:get() == 3 and slot_31_1 then
		return
	end

	if ove_0_21.combo.w_combo:get() and ove_0_21.combo.combo_spell_priority:get() == 2 and not slot_31_1 and player:spellSlot(1).level >= 1 and player:spellSlot(1).state == 0 and ove_0_24.core.next_attack - os.clock() > ove_0_41 + network.latency then
		return
	end

	if ove_0_21.combo.w_combo:get() and ove_0_21.combo.combo_spell_priority_eform:get() == 2 and slot_31_1 and player:spellSlot(1).level >= 1 and player:spellSlot(1).state == 0 and ove_0_24.core.next_attack - os.clock() > ove_0_41 + network.latency then
		return
	end

	if ove_0_24.core.next_attack - os.clock() > ove_0_39.delay + network.latency or ove_0_70 then
		local slot_31_2 = ove_0_54()

		if ove_0_20.IsValidTarget(slot_31_2) and player:spellSlot(0).state == 0 then
			local slot_31_3 = ove_0_25.linear.get_prediction(ove_0_39, slot_31_2)
			local slot_31_4 = ove_0_39.range * ove_0_39.range

			if slot_31_3 and slot_31_4 >= slot_31_3.startPos:distSqr(slot_31_3.endPos) then
				if ove_0_21.safe.q3_safe_toggle:get() and ove_0_21.safe.q3_safe_combo:get() and slot_31_0 and not ove_0_104(slot_31_3.endPos) then
					if ove_0_21.safe.q3_safe_ignore:get() then
						if not ove_0_20.is_under_enemy_turret(player.pos2D) then
							return
						end
					else
						return
					end
				end

				if ove_0_21.combo.q3_combo_safe:get() and slot_31_0 and ove_0_20.GetPercentHealth(player) <= ove_0_21.combo.q3_combo_safe_hp:get() and not ove_0_105(slot_31_3.endPos) then
					if ove_0_21.combo.q3_combo_safe_ignore:get() then
						if ove_0_96(slot_31_2) <= slot_31_2.health then
							return
						end
					else
						return
					end
				end

				if ove_0_21.combo.q_combo_slowpred:get() and not slot_31_0 and not ove_0_21.combo.q_combo_slowpred_ignore:get() and not ove_0_106(ove_0_39, slot_31_3, slot_31_2) then
					return
				end

				if ove_0_21.combo.q3_combo_slowpred:get() and slot_31_0 and not ove_0_21.combo.q3_combo_slowpred_ignore:get() and not ove_0_106(ove_0_39, slot_31_3, slot_31_2) then
					return
				end

				if ove_0_24.core.can_action() then
					player:castSpell("pos", 0, vec3(slot_31_3.endPos.x, slot_31_2.pos.y, slot_31_3.endPos.y))

					if ove_0_70 then
						ove_0_70 = false
					end
				end
			end
		end
	end
end

local function ove_0_109()
	-- print 32
	if not ove_0_21.combo.w_combo:get() then
		return
	end

	local slot_32_0 = ove_0_103()

	if ove_0_21.combo.w_combo_aa_mode:get() == 2 and not slot_32_0 then
		if ove_0_21.combo.w_combo_aa_mode_switch:get() then
			if ove_0_40.delay > ove_0_21.combo.w_combo_aa_mode_switch_delay:get() / 1000 then
				return
			end
		else
			return
		end
	end

	if ove_0_21.combo.w_combo_aa_mode_eform:get() == 2 and slot_32_0 then
		if ove_0_21.combo.w_combo_aa_mode_switch_eform:get() then
			if ove_0_40.delay > ove_0_21.combo.w_combo_aa_mode_switch_delay_eform:get() / 1000 then
				return
			end
		else
			return
		end
	end

	local slot_32_1 = ove_0_56()

	if ove_0_20.IsValidTarget(slot_32_1) and player:spellSlot(1).state == 0 then
		local slot_32_2 = ove_0_25.linear.get_prediction(ove_0_40, slot_32_1)
		local slot_32_3 = ove_0_40.range * ove_0_40.range

		if slot_32_2 and slot_32_3 >= slot_32_2.startPos:distSqr(slot_32_2.endPos) then
			if ove_0_21.combo.w_combo_aa_mode:get() == 1 and not slot_32_0 and ove_0_24.combat.target then
				return
			end

			if ove_0_21.combo.w_combo_aa_mode_eform:get() == 1 and slot_32_0 and ove_0_24.combat.target then
				return
			end

			if ove_0_21.combo.w_combo_slowpred:get() then
				if ove_0_21.combo.w_combo_slowpred_ignore_delay_bool:get() then
					if ove_0_40.delay > ove_0_21.combo.w_combo_slowpred_ignore_delay:get() / 1000 and not ove_0_106(ove_0_40, slot_32_2, slot_32_1) then
						return
					end
				elseif not ove_0_106(ove_0_40, slot_32_2, slot_32_1) then
					return
				end
			end

			if ove_0_24.core.can_action() then
				player:castSpell("pos", 1, vec3(slot_32_2.endPos.x, slot_32_1.pos.y, slot_32_2.endPos.y))
			end
		end
	end
end

local function ove_0_110()
	-- print 33
	if not ove_0_21.combo.w_combo:get() then
		return
	end

	local slot_33_0 = ove_0_103()

	if ove_0_21.combo.w_combo_aa_mode:get() == 3 and not slot_33_0 then
		return
	end

	if ove_0_21.combo.w_combo_aa_mode_eform:get() == 3 and slot_33_0 then
		return
	end

	local slot_33_1 = ove_0_72() and ove_0_21.combo.q3_combo or ove_0_21.combo.q_combo

	if slot_33_1:get() and ove_0_21.combo.combo_spell_priority:get() == 1 and not slot_33_0 and player:spellSlot(0).level >= 1 and player:spellSlot(0).state == 0 then
		return
	end

	if slot_33_1:get() and ove_0_21.combo.combo_spell_priority_eform:get() == 1 and slot_33_0 and player:spellSlot(0).level >= 1 and player:spellSlot(0).state == 0 then
		return
	end

	if ove_0_24.core.next_attack - os.clock() > ove_0_41 + network.latency or ove_0_70 then
		local slot_33_2 = ove_0_56()

		if ove_0_20.IsValidTarget(slot_33_2) and player:spellSlot(1).state == 0 then
			local slot_33_3 = ove_0_25.linear.get_prediction(ove_0_40, slot_33_2)
			local slot_33_4 = ove_0_40.range * ove_0_40.range

			if slot_33_3 and slot_33_4 >= slot_33_3.startPos:distSqr(slot_33_3.endPos) then
				if ove_0_21.combo.w_combo_slowpred:get() and not ove_0_21.combo.w_combo_slowpred_ignore:get() and not ove_0_106(ove_0_40, slot_33_3, slot_33_2) then
					return
				end

				if ove_0_24.core.can_action() then
					player:castSpell("pos", 1, vec3(slot_33_3.endPos.x, slot_33_2.pos.y, slot_33_3.endPos.y))

					if ove_0_70 then
						ove_0_70 = false
					end
				end
			end
		end
	end
end

local function ove_0_111()
	-- print 34
	ove_0_107()
	ove_0_109()
end

local function ove_0_112()
	-- print 35
	local slot_35_0 = ove_0_72()

	if not (slot_35_0 and ove_0_21.harass.q3_harass or ove_0_21.harass.q_harass):get() then
		return
	end

	local slot_35_1 = ove_0_103()

	if ove_0_21.harass.q_harass_aa_mode:get() == 2 and not slot_35_0 and not slot_35_1 then
		if ove_0_21.harass.q_harass_aa_mode_switch:get() then
			if ove_0_39.delay > ove_0_21.harass.q_harass_aa_mode_switch_delay:get() / 1000 then
				return
			end
		else
			return
		end
	end

	if ove_0_21.harass.q_harass_aa_mode_eform:get() == 2 and not slot_35_0 and slot_35_1 then
		if ove_0_21.harass.q_harass_aa_mode_switch_eform:get() then
			if ove_0_39.delay > ove_0_21.harass.q_harass_aa_mode_switch_delay_eform:get() / 1000 then
				return
			end
		else
			return
		end
	end

	local slot_35_2 = ove_0_54()

	if ove_0_20.IsValidTarget(slot_35_2) and player:spellSlot(0).state == 0 then
		local slot_35_3 = ove_0_25.linear.get_prediction(ove_0_39, slot_35_2)
		local slot_35_4 = ove_0_39.range * ove_0_39.range

		if slot_35_3 and slot_35_4 >= slot_35_3.startPos:distSqr(slot_35_3.endPos) then
			if ove_0_21.harass.q_harass_aa_mode:get() == 1 and not slot_35_0 and not slot_35_1 and ove_0_24.combat.target then
				return
			end

			if ove_0_21.harass.q_harass_aa_mode_eform:get() == 1 and not slot_35_0 and slot_35_1 and ove_0_24.combat.target then
				return
			end

			if ove_0_21.safe.q3_safe_toggle:get() and ove_0_21.safe.q3_safe_harass:get() and slot_35_0 and not ove_0_104(slot_35_3.endPos) then
				if ove_0_21.safe.q3_safe_ignore:get() and not ove_0_20.is_under_enemy_turret(player.pos2D) then
					return
				else
					return
				end
			end

			if ove_0_21.harass.q3_harass_safe:get() and slot_35_0 and ove_0_20.GetPercentHealth(player) <= ove_0_21.harass.q3_harass_safe_hp:get() and not ove_0_105(slot_35_3.endPos) then
				if ove_0_21.harass.q3_harass_safe_ignore:get() then
					if ove_0_96(slot_35_2) <= slot_35_2.health then
						return
					end
				else
					return
				end
			end

			if ove_0_21.harass.q_harass_slowpred:get() and not slot_35_0 then
				if ove_0_21.harass.q_harass_slowpred_ignore_delay_bool:get() then
					if ove_0_39.delay > ove_0_21.harass.q_harass_slowpred_ignore_delay:get() / 1000 and not ove_0_106(ove_0_39, slot_35_3, slot_35_2) then
						return
					end
				elseif not ove_0_106(ove_0_39, slot_35_3, slot_35_2) then
					return
				end
			end

			if ove_0_21.harass.q3_harass_slowpred:get() and slot_35_0 then
				if ove_0_21.harass.q3_harass_slowpred_ignore_delay_bool:get() then
					if ove_0_39.delay > ove_0_21.harass.q3_harass_slowpred_ignore_delay:get() / 1000 and not ove_0_106(ove_0_39, slot_35_3, slot_35_2) then
						return
					end
				elseif not ove_0_106(ove_0_39, slot_35_3, slot_35_2) then
					return
				end
			end

			if ove_0_24.core.can_action() then
				player:castSpell("pos", 0, vec3(slot_35_3.endPos.x, slot_35_2.pos.y, slot_35_3.endPos.y))
			end
		end
	end
end

local function ove_0_113()
	-- print 36
	local slot_36_0 = ove_0_72()

	if not (slot_36_0 and ove_0_21.harass.q3_harass or ove_0_21.harass.q_harass):get() then
		return
	end

	local slot_36_1 = ove_0_103()

	if ove_0_21.harass.q_harass_aa_mode:get() == 3 and not slot_36_1 then
		return
	end

	if ove_0_21.harass.q_harass_aa_mode_eform:get() == 3 and slot_36_1 then
		return
	end

	if ove_0_21.harass.w_harass:get() and ove_0_21.harass.harass_spell_priority:get() == 2 and not slot_36_1 and player:spellSlot(1).level >= 1 and player:spellSlot(1).state == 0 and ove_0_24.core.next_attack - os.clock() > ove_0_41 + network.latency then
		return
	end

	if ove_0_21.harass.w_harass:get() and ove_0_21.harass.harass_spell_priority_eform:get() == 2 and slot_36_1 and player:spellSlot(1).level >= 1 and player:spellSlot(1).state == 0 and ove_0_24.core.next_attack - os.clock() > ove_0_41 + network.latency then
		return
	end

	if ove_0_24.core.next_attack - os.clock() > ove_0_39.delay + network.latency or ove_0_70 then
		local slot_36_2 = ove_0_54()

		if ove_0_20.IsValidTarget(slot_36_2) and player:spellSlot(0).state == 0 then
			local slot_36_3 = ove_0_25.linear.get_prediction(ove_0_39, slot_36_2)
			local slot_36_4 = ove_0_39.range * ove_0_39.range

			if slot_36_3 and slot_36_4 >= slot_36_3.startPos:distSqr(slot_36_3.endPos) then
				if ove_0_21.safe.q3_safe_toggle:get() and ove_0_21.safe.q3_safe_harass:get() and slot_36_0 and not ove_0_104(slot_36_3.endPos) then
					if ove_0_21.safe.q3_safe_ignore:get() then
						if not ove_0_20.is_under_enemy_turret(player.pos2D) then
							return
						end
					else
						return
					end
				end

				if ove_0_21.harass.q3_harass_safe:get() and slot_36_0 and ove_0_20.GetPercentHealth(player) <= ove_0_21.harass.q3_harass_safe_hp:get() and not ove_0_105(slot_36_3.endPos) then
					if ove_0_21.harass.q3_harass_safe_ignore:get() then
						if ove_0_96(slot_36_2) <= slot_36_2.health then
							return
						end
					else
						return
					end
				end

				if ove_0_21.harass.q_harass_slowpred:get() and not slot_36_0 and not ove_0_21.harass.q_harass_slowpred_ignore:get() and not ove_0_106(ove_0_39, slot_36_3, slot_36_2) then
					return
				end

				if ove_0_21.harass.q3_harass_slowpred:get() and slot_36_0 and not ove_0_21.harass.q3_harass_slowpred_ignore:get() and not ove_0_106(ove_0_39, slot_36_3, slot_36_2) then
					return
				end

				if ove_0_24.core.can_action() then
					player:castSpell("pos", 0, vec3(slot_36_3.endPos.x, slot_36_2.pos.y, slot_36_3.endPos.y))

					if ove_0_70 then
						ove_0_70 = false
					end
				end
			end
		end
	end
end

local function ove_0_114()
	-- print 37
	if not ove_0_21.harass.w_harass:get() then
		return
	end

	local slot_37_0 = ove_0_103()

	if ove_0_21.harass.w_harass_aa_mode:get() == 2 and not slot_37_0 then
		if ove_0_21.harass.w_harass_aa_mode_switch:get() then
			if ove_0_40.delay > ove_0_21.harass.w_harass_aa_mode_switch_delay:get() / 1000 then
				return
			end
		else
			return
		end
	end

	if ove_0_21.harass.w_harass_aa_mode_eform:get() == 2 and slot_37_0 then
		if ove_0_21.harass.w_harass_aa_mode_switch_eform:get() then
			if ove_0_40.delay > ove_0_21.harass.w_harass_aa_mode_switch_delay_eform:get() / 1000 then
				return
			end
		else
			return
		end
	end

	local slot_37_1 = ove_0_56()

	if ove_0_20.IsValidTarget(slot_37_1) and player:spellSlot(1).state == 0 then
		local slot_37_2 = ove_0_25.linear.get_prediction(ove_0_40, slot_37_1)
		local slot_37_3 = ove_0_40.range * ove_0_40.range

		if slot_37_2 and slot_37_3 >= slot_37_2.startPos:distSqr(slot_37_2.endPos) then
			if ove_0_21.harass.w_harass_aa_mode:get() == 1 and not slot_37_0 and ove_0_24.combat.target then
				return
			end

			if ove_0_21.harass.w_harass_aa_mode_eform:get() == 1 and slot_37_0 and ove_0_24.combat.target then
				return
			end

			if ove_0_21.harass.w_harass_slowpred:get() then
				if ove_0_21.harass.w_harass_slowpred_ignore_delay_bool:get() then
					if ove_0_40.delay > ove_0_21.harass.w_harass_slowpred_ignore_delay:get() / 1000 and not ove_0_106(ove_0_40, slot_37_2, slot_37_1) then
						return
					end
				elseif not ove_0_106(ove_0_40, slot_37_2, slot_37_1) then
					return
				end
			end

			if ove_0_24.core.can_action() then
				player:castSpell("pos", 1, vec3(slot_37_2.endPos.x, slot_37_1.pos.y, slot_37_2.endPos.y))
			end
		end
	end
end

local function ove_0_115()
	-- print 38
	if not ove_0_21.harass.w_harass:get() then
		return
	end

	local slot_38_0 = ove_0_103()

	if ove_0_21.harass.w_harass_aa_mode:get() == 3 and not slot_38_0 then
		return
	end

	if ove_0_21.harass.w_harass_aa_mode_eform:get() == 3 and slot_38_0 then
		return
	end

	local slot_38_1 = ove_0_72() and ove_0_21.harass.q3_harass or ove_0_21.harass.q_harass

	if slot_38_1:get() and ove_0_21.harass.harass_spell_priority:get() == 1 and not slot_38_0 and player:spellSlot(0).level >= 1 and player:spellSlot(0).state == 0 then
		return
	end

	if slot_38_1:get() and ove_0_21.harass.harass_spell_priority_eform:get() == 1 and slot_38_0 and player:spellSlot(0).level >= 1 and player:spellSlot(0).state == 0 then
		return
	end

	if ove_0_24.core.next_attack - os.clock() > ove_0_41 + network.latency or ove_0_70 then
		local slot_38_2 = ove_0_56()

		if ove_0_20.IsValidTarget(slot_38_2) and player:spellSlot(1).state == 0 then
			local slot_38_3 = ove_0_25.linear.get_prediction(ove_0_40, slot_38_2)
			local slot_38_4 = ove_0_40.range * ove_0_40.range

			if slot_38_3 and slot_38_4 >= slot_38_3.startPos:distSqr(slot_38_3.endPos) then
				if ove_0_21.harass.w_harass_slowpred:get() and not ove_0_21.harass.w_harass_slowpred_ignore:get() and not ove_0_106(ove_0_40, slot_38_3, slot_38_2) then
					return
				end

				if ove_0_24.core.can_action() then
					player:castSpell("pos", 1, vec3(slot_38_3.endPos.x, slot_38_2.pos.y, slot_38_3.endPos.y))

					if ove_0_70 then
						ove_0_70 = false
					end
				end
			end
		end
	end
end

local function ove_0_116()
	-- print 39
	ove_0_112()
	ove_0_114()
end

ove_0_24.combat.register_f_after_attack(function()
	-- print 40
	if ove_0_21.combo.w_combo_NOweave_dps:get() then
		ove_0_41 = ove_0_40.delay
	elseif not (ove_0_24.core.next_attack - os.clock() > ove_0_40.delay + network.latency) then
		ove_0_41 = ove_0_39.delay
	else
		ove_0_41 = ove_0_40.delay
	end

	if ove_0_21.keys.combokey:get() then
		ove_0_108()
		ove_0_110()
	end

	if ove_0_21.keys.harasskey:get() then
		ove_0_113()
		ove_0_115()
	end

	if ove_0_70 then
		ove_0_70 = false
	end
end)

local function ove_0_117(arg_41_0)
	-- print 41
	local slot_41_0 = true

	if not arg_41_0.path.isActive and arg_41_0.health >= arg_41_0.maxHealth then
		slot_41_0 = false
	end

	if arg_41_0.name:find("Sru_Crab") and arg_41_0.moveSpeed == 154.625 and arg_41_0.health >= arg_41_0.maxHealth then
		slot_41_0 = false
	end

	return slot_41_0
end

local function ove_0_118()
	-- print 42
	if not ove_0_21.farm.harr.w_harr:get() then
		return
	end

	if player:spellSlot(1).state ~= 0 then
		return
	end

	if ove_0_21.farm.harr.harr_turn_off_tower:get() and ove_0_20.is_under_enemy_turret(player.path.serverPos2D) then
		return
	end

	local slot_42_0 = ove_0_56()

	if ove_0_20.IsValidTarget(slot_42_0) then
		local slot_42_1 = ove_0_25.linear.get_prediction(ove_0_40, slot_42_0)
		local slot_42_2 = ove_0_40.range * ove_0_40.range

		if slot_42_1 and slot_42_2 >= slot_42_1.startPos:distSqr(slot_42_1.endPos) then
			if ove_0_21.farm.harr.w_harr_slowpred:get() then
				if ove_0_21.farm.harr.w_harr_slowpred_ignore_delay_bool:get() then
					if ove_0_40.delay > ove_0_21.farm.harr.w_harr_slowpred_ignore_delay:get() / 1000 and not ove_0_106(ove_0_40, slot_42_1, slot_42_0) then
						return
					end
				elseif not ove_0_106(ove_0_40, slot_42_1, slot_42_0) then
					return
				end
			end

			if ove_0_24.core.can_action() and (ove_0_24.core.next_attack - os.clock() > ove_0_40.delay + network.latency or ove_0_24.core.next_attack - os.clock() < 0) then
				player:castSpell("pos", 1, vec3(slot_42_1.endPos.x, slot_42_0.pos.y, slot_42_1.endPos.y))
			end
		end
	end
end

local function ove_0_119()
	-- print 43
	if not ove_0_21.farm.harr.q3_harr:get() then
		return
	end

	if player:spellSlot(0).state ~= 0 then
		return
	end

	if ove_0_21.farm.harr.harr_turn_off_tower:get() and ove_0_20.is_under_enemy_turret(player.path.serverPos2D) then
		return
	end

	if not ove_0_72() then
		return
	end

	local slot_43_0 = ove_0_54()

	if ove_0_20.IsValidTarget(slot_43_0) then
		local slot_43_1 = ove_0_25.linear.get_prediction(ove_0_39, slot_43_0)
		local slot_43_2 = ove_0_39.range * ove_0_39.range

		if slot_43_1 and slot_43_2 >= slot_43_1.startPos:distSqr(slot_43_1.endPos) then
			if ove_0_21.safe.q3_safe_toggle:get() and ove_0_21.safe.q3_safe_laneclear_harr:get() and not ove_0_104(slot_43_1.endPos) then
				if ove_0_21.safe.q3_safe_ignore:get() then
					if not ove_0_20.is_under_enemy_turret(player.pos2D) then
						return
					end
				else
					return
				end
			end

			if ove_0_21.farm.harr.q3_harr_safe:get() and ove_0_20.GetPercentHealth(player) <= ove_0_21.farm.harr.q3_harr_safe_hp:get() and not ove_0_105(slot_43_1.endPos) then
				return
			end

			if ove_0_21.farm.harr.q3_harr_slowpred:get() then
				if ove_0_21.farm.harr.q3_harr_slowpred_ignore_delay_bool:get() then
					if ove_0_39.delay > ove_0_21.farm.harr.q3_harr_slowpred_ignore_delay:get() / 1000 and not ove_0_106(ove_0_39, slot_43_1, slot_43_0) then
						return
					end
				elseif not ove_0_106(ove_0_39, slot_43_1, slot_43_0) then
					return
				end
			end

			if ove_0_24.core.can_action() and (ove_0_24.core.next_attack - os.clock() > ove_0_39.delay + network.latency or ove_0_24.core.next_attack - os.clock() < 0) then
				player:castSpell("pos", 0, vec3(slot_43_1.endPos.x, slot_43_0.pos.y, slot_43_1.endPos.y))
			end
		end
	end
end

local function ove_0_120()
	-- print 44
	local slot_44_0 = {
		boundingRadiusMod = 1,
		delay = ove_0_39.delay,
		speed = ove_0_39.speed,
		width = ove_0_39.width,
		range = ove_0_39.range,
		collision = {
			walls = false,
			hero = false,
			minion = false
		},
		damage = function(arg_45_0)
			-- print 45
			return ove_0_96(arg_45_0)
		end
	}
	local slot_44_1 = false

	if ove_0_21.farm.panic_clear_toggle:get() then
		slot_44_1 = true

		function slot_44_0.damage(arg_46_0)
			-- print 46
			return math.huge
		end
	end

	local slot_44_2, slot_44_3 = ove_0_24.farm.skill_clear_linear(slot_44_0)
	local slot_44_4 = ove_0_72()

	if slot_44_2 and slot_44_2.startPos:distSqr(slot_44_2.endPos) <= ove_0_39.range * ove_0_39.range then
		if slot_44_3.team == TEAM_ENEMY then
			if not slot_44_4 and not ove_0_21.farm.lane.lane_q_enable:get() then
				return
			end

			if not slot_44_4 and ove_0_21.farm.lane.lane_q_stop:get() and #ove_0_20.count_enemies_in_range(player.pos2D, 1500) >= 1 then
				return
			end

			if slot_44_4 and not ove_0_21.farm.lane.lane_q3_enable:get() then
				return
			end

			if slot_44_4 and ove_0_21.farm.lane.lane_q3_stop:get() and #ove_0_20.count_enemies_in_range(player.pos2D, 1500) >= 1 then
				return
			end

			if ove_0_21.safe.q3_safe_toggle:get() and ove_0_21.safe.q3_safe_laneclear:get() and slot_44_4 and not ove_0_104(slot_44_2.endPos) then
				if not slot_44_1 then
					if ove_0_21.safe.q3_safe_ignore:get() and not ove_0_20.is_under_enemy_turret(player.pos2D) then
						return
					else
						return
					end
				elseif ove_0_21.safe.q3_safe_panic_clear:get() then
					if ove_0_21.safe.q3_safe_ignore:get() and not ove_0_20.is_under_enemy_turret(player.pos2D) then
						return
					else
						return
					end
				end
			end
		end

		if slot_44_3.team == TEAM_NEUTRAL then
			if not slot_44_4 and not ove_0_21.farm.jung.jung_q_enable:get() then
				return
			end

			if not slot_44_4 and ove_0_21.farm.jung.jung_q_aggro:get() and not ove_0_117(slot_44_3) then
				return
			end

			if not slot_44_4 and ove_0_21.farm.jung.jung_q_stop:get() and #ove_0_20.count_enemies_in_range(player.pos2D, 1500) >= 1 then
				return
			end

			if slot_44_4 and not ove_0_21.farm.jung.jung_q3_enable:get() then
				return
			end

			if slot_44_4 and ove_0_21.farm.jung.jung_q3_aggro:get() and not ove_0_117(slot_44_3) then
				return
			end

			if slot_44_4 and ove_0_21.farm.jung.jung_q3_stop:get() and #ove_0_20.count_enemies_in_range(player.pos2D, 1500) >= 1 then
				return
			end
		end

		if player:spellSlot(0).state == 0 and ove_0_24.core.can_action() and (ove_0_24.core.next_attack - os.clock() > ove_0_39.delay + network.latency or ove_0_24.core.next_attack - os.clock() < 0) then
			player:castSpell("pos", 0, vec3(slot_44_2.endPos.x, slot_44_3.pos.y, slot_44_2.endPos.y))
		end
	end
end

local function ove_0_121()
	-- print 47
	local slot_47_0 = {
		boundingRadiusMod = 0,
		delay = ove_0_40.delay,
		speed = ove_0_40.speed,
		width = ove_0_40.width,
		range = ove_0_40.range,
		collision = {
			walls = false,
			hero = false,
			minion = false
		},
		damage = function(arg_48_0)
			-- print 48
			return ove_0_100(arg_48_0)
		end
	}
	local slot_47_1, slot_47_2 = ove_0_24.farm.skill_farm_linear(slot_47_0)

	if slot_47_1 and slot_47_1.startPos:distSqr(slot_47_1.endPos) <= ove_0_40.range * ove_0_40.range and slot_47_2.team == TEAM_ENEMY and slot_47_2.charName:find("MinionSiege") then
		if ove_0_21.keys.clearkey:get() then
			if not ove_0_21.farm.lane.lane_w_cannon:get() then
				return
			end

			if player:spellSlot(0).state == 0 and slot_47_1.startPos:distSqr(slot_47_1.endPos) > ove_0_39.range * ove_0_39.range then
				return
			end

			if ove_0_21.farm.lane.lane_w_stop:get() and #ove_0_20.count_enemies_in_range(player.pos2D, 1500) >= 1 then
				return
			end
		end

		if ove_0_21.keys.lastkey:get() then
			if not ove_0_21.farm.last.last_w_cannon:get() then
				return
			end

			if player:spellSlot(0).state == 0 and slot_47_1.startPos:distSqr(slot_47_1.endPos) > ove_0_39.range * ove_0_39.range then
				return
			end

			if ove_0_21.farm.last.last_w_stop:get() and #ove_0_20.count_enemies_in_range(player.pos2D, 1500) >= 1 then
				return
			end
		end

		if player:spellSlot(1).state == 0 and ove_0_24.core.can_action() and (ove_0_24.core.next_attack - os.clock() > ove_0_40.delay + network.latency or ove_0_24.core.next_attack - os.clock() < 0) then
			player:castSpell("pos", 1, vec3(slot_47_1.endPos.x, slot_47_2.pos.y, slot_47_1.endPos.y))
		end
	end
end

local function ove_0_122()
	-- print 49
	if not ove_0_21.farm.farm_toggle:get() then
		return
	end

	ove_0_119()
	ove_0_118()
	ove_0_120()
	ove_0_121()
end

local function ove_0_123()
	-- print 50
	if ove_0_20.IsRecalling(player) then
		return
	end

	if ove_0_21.keys.combokey:get() then
		return
	end

	if ove_0_21.keys.harasskey:get() then
		return
	end

	if player:spellSlot(1).state ~= 0 then
		return
	end

	if not ove_0_21.misc.w_gap_enable:get() then
		return
	end

	if ove_0_21.misc.w_gap_turret:get() and ove_0_20.IsUnderTurret(player, TEAM_ENEMY, ove_0_21.misc.w_gap_turret_allow:get()) then
		return
	end

	local slot_50_0 = ove_0_40.range * ove_0_40.range

	for iter_50_0 = 0, objManager.enemies_n - 1 do
		local slot_50_1 = objManager.enemies[iter_50_0]

		if ove_0_20.IsValidTarget(slot_50_1) and slot_50_1.path.isActive and slot_50_1.path.isDashing and slot_50_0 >= player.pos2D:distSqr(slot_50_1.path.point2D[1]) and slot_50_1.path.point2D[1]:distSqr(slot_50_1.path.point2D[0]) > 1225 and ove_0_21.misc.gap_blacklist[ove_0_35[slot_50_1.ptr]] and not ove_0_21.misc.gap_blacklist[ove_0_35[slot_50_1.ptr]]:get() then
			local slot_50_2 = ove_0_25.linear.get_prediction(ove_0_40, slot_50_1)

			if slot_50_2 and slot_50_0 >= slot_50_2.startPos:distSqr(slot_50_2.endPos) and ove_0_24.core.can_action() then
				player:castSpell("pos", 1, vec3(slot_50_2.endPos.x, slot_50_1.pos.y, slot_50_2.endPos.y))
			end
		end
	end
end

local function ove_0_124()
	-- print 51
	local slot_51_0 = ove_0_60()

	if not ove_0_21.misc.force_r_slowpred:get() then
		if ove_0_21.misc.force_r_key:get() and player:spellSlot(3).state == 0 and ove_0_20.IsValidTarget(slot_51_0) and ove_0_102(slot_51_0) > 0 then
			local slot_51_1 = ove_0_25.linear.get_prediction(ove_0_42, slot_51_0)
			local slot_51_2 = ove_0_42.range * ove_0_42.range

			if slot_51_1 and slot_51_2 >= slot_51_1.startPos:distSqr(slot_51_1.endPos) then
				player:castSpell("pos", 3, vec3(slot_51_1.endPos.x, slot_51_0.pos.y, slot_51_1.endPos.y))
			end
		end
	elseif (ove_0_21.misc.force_r_key:get() or ove_0_50) and player:spellSlot(3).state == 0 and ove_0_20.IsValidTarget(slot_51_0) and ove_0_102(slot_51_0) > 0 then
		local slot_51_3 = ove_0_25.linear.get_prediction(ove_0_42, slot_51_0)
		local slot_51_4 = ove_0_42.range * ove_0_42.range

		if slot_51_3 and slot_51_4 >= slot_51_3.startPos:distSqr(slot_51_3.endPos) and ove_0_106(ove_0_42, slot_51_3, slot_51_0) then
			player:castSpell("pos", 3, vec3(slot_51_3.endPos.x, slot_51_0.pos.y, slot_51_3.endPos.y))
		end
	end
end

local function ove_0_125()
	-- print 52
	local slot_52_0 = {
		boundingRadiusMod = 1,
		delay = ove_0_39.delay,
		speed = ove_0_39.speed,
		width = ove_0_39.width,
		range = ove_0_39.range,
		collision = {
			walls = false,
			hero = false,
			minion = false
		},
		damage = function(arg_53_0)
			-- print 53
			return ove_0_96(arg_53_0)
		end
	}
	local slot_52_1
	local slot_52_2
	local slot_52_3 = ove_0_72()

	if not slot_52_3 then
		if ove_0_21.farm.last.last_q_helper:get() then
			slot_52_1, slot_52_2 = ove_0_24.farm.skill_farm_linear(slot_52_0)
		else
			slot_52_1, slot_52_2 = ove_0_24.farm.skill_clear_linear(slot_52_0)
		end
	elseif ove_0_21.farm.last.last_q3_helper:get() then
		slot_52_1, slot_52_2 = ove_0_24.farm.skill_farm_linear(slot_52_0)
	else
		slot_52_1, slot_52_2 = ove_0_24.farm.skill_clear_linear(slot_52_0)
	end

	if slot_52_1 and slot_52_1.startPos:distSqr(slot_52_1.endPos) <= ove_0_39.range * ove_0_39.range then
		if slot_52_2.team == TEAM_ENEMY then
			if not slot_52_3 and not ove_0_21.farm.last.last_q_enable:get() then
				return
			end

			if not slot_52_3 and ove_0_21.farm.last.last_q_stop:get() and #ove_0_20.count_enemies_in_range(player.pos2D, 1500) >= 1 then
				return
			end

			if slot_52_3 and not ove_0_21.farm.last.last_q3_enable:get() then
				return
			end

			if slot_52_3 and ove_0_21.farm.last.last_q3_stop:get() and #ove_0_20.count_enemies_in_range(player.pos2D, 1500) >= 1 then
				return
			end

			if ove_0_21.safe.q3_safe_toggle:get() and ove_0_21.safe.q3_safe_laneclear:get() and slot_52_3 and not ove_0_104(slot_52_1.endPos) then
				if ove_0_21.safe.q3_safe_ignore:get() and not ove_0_20.is_under_enemy_turret(player.pos2D) then
					return
				else
					return
				end
			end
		end

		if slot_52_2.team == TEAM_NEUTRAL then
			return
		end

		if player:spellSlot(0).state == 0 and ove_0_24.core.can_action() and (ove_0_24.core.next_attack - os.clock() > ove_0_39.delay + network.latency or ove_0_24.core.next_attack - os.clock() < 0) then
			player:castSpell("pos", 0, vec3(slot_52_1.endPos.x, slot_52_2.pos.y, slot_52_1.endPos.y))
		end
	end
end

local function ove_0_126()
	-- print 54
	if not ove_0_21.farm.farm_toggle:get() then
		return
	end

	ove_0_125()
	ove_0_121()
end

local function ove_0_127()
	-- print 55
	if ove_0_23 ~= 1 then
		return
	end

	if not ove_0_21.ee.e2_ee_enable:get() then
		return
	end

	if player:spellSlot(2).state ~= 0 then
		return
	end

	if not ove_0_103() then
		return
	end

	if player.isZombie then
		return
	end

	local slot_55_0 = ove_0_21.ee.e2_ee_cc:get()
	local slot_55_1 = ove_0_21.ee.e2_ee_cc_hp:get()
	local slot_55_2 = ove_0_21.ee.e2_ee_all:get()
	local slot_55_3 = ove_0_21.ee.e2_ee_all_hp:get()

	for iter_55_0 = 1, #ove_0_22.core.skillshots do
		local slot_55_4 = ove_0_22.core.skillshots[iter_55_0]
		local slot_55_5 = slot_55_4.owner
		local slot_55_6 = slot_55_4.target

		if slot_55_4.polygon and slot_55_4.polygon:Contains(player.path.serverPos) ~= 0 and (not slot_55_4.data.collision or #slot_55_4.data.collision == 0) then
			for iter_55_1, iter_55_2 in pairs(ove_0_27) do
				if slot_55_0 and slot_55_4.name:find(iter_55_1:lower()) and iter_55_2.hard_cc and ove_0_21.ee.cc_blacklist[iter_55_2.charName] and ove_0_21.ee.cc_blacklist[iter_55_2.charName]["Dodge_" .. iter_55_1] and ove_0_21.ee.cc_blacklist[iter_55_2.charName]["Dodge_" .. iter_55_1]:get() and slot_55_1 >= player.health / player.maxHealth * 100 then
					if slot_55_4.missile and player.pos2D:dist(slot_55_4.missile.pos2D) / slot_55_4.data.speed < network.latency + 0.25 then
						player:castSpell("pos", 2, mousePos)

						break
					end

					if slot_55_4.data.speed == math.huge or slot_55_4.data.spell_type == "Circular" then
						player:castSpell("pos", 2, mousePos)

						break
					end
				end

				if slot_55_2 and slot_55_4.name:find(iter_55_1:lower()) and ove_0_21.ee.all_blacklist[iter_55_2.charName] and ove_0_21.ee.all_blacklist[iter_55_2.charName]["Dodge_" .. iter_55_1] and ove_0_21.ee.all_blacklist[iter_55_2.charName]["Dodge_" .. iter_55_1]:get() and slot_55_3 >= player.health / player.maxHealth * 100 then
					if slot_55_4.missile and player.pos2D:dist(slot_55_4.missile.pos2D) / slot_55_4.data.speed < network.latency + 0.25 then
						player:castSpell("pos", 2, mousePos)

						break
					end

					if slot_55_4.data.speed == math.huge or slot_55_4.data.spell_type == "Circular" then
						player:castSpell("pos", 2, mousePos)

						break
					end
				end
			end
		end
	end
end

local function ove_0_128()
	-- print 56
	if ove_0_23 ~= 2 then
		return
	end

	if not ove_0_21.ee.e2_ee_enable:get() then
		return
	end

	if player:spellSlot(2).state ~= 0 then
		return
	end

	if not ove_0_103() then
		return
	end

	if player.isZombie then
		return
	end

	local slot_56_0 = ove_0_21.ee.e2_ee_cc:get()
	local slot_56_1 = ove_0_21.ee.e2_ee_cc_hp:get()
	local slot_56_2 = ove_0_21.ee.e2_ee_all:get()
	local slot_56_3 = ove_0_21.ee.e2_ee_all_hp:get()

	for iter_56_0 = ove_0_22.core.skillshots.n, 1, -1 do
		local slot_56_4 = ove_0_22.core.skillshots[iter_56_0]
		local slot_56_5 = slot_56_4.owner
		local slot_56_6 = slot_56_4.target

		if slot_56_4.contains and slot_56_4:contains(player) and (not slot_56_4.data.collision or slot_56_4.data.collision == 0) then
			local slot_56_7 = slot_56_4.name or "not_found"
			local slot_56_8 = ove_0_27[slot_56_7]

			if slot_56_8 then
				if slot_56_0 and slot_56_8.hard_cc and ove_0_21.ee.cc_blacklist[slot_56_8.charName] and ove_0_21.ee.cc_blacklist[slot_56_8.charName]["Dodge_" .. slot_56_7] and ove_0_21.ee.cc_blacklist[slot_56_8.charName]["Dodge_" .. slot_56_7]:get() and slot_56_1 >= player.health / player.maxHealth * 100 then
					if slot_56_4.missile and player.pos2D:dist(slot_56_4.missile.pos2D) / slot_56_4.data.speed < network.latency + 0.25 then
						player:castSpell("pos", 2, mousePos)

						break
					end

					if slot_56_4.data.speed == math.huge or slot_56_4.data.spell_type == "Circular" then
						player:castSpell("pos", 2, mousePos)

						break
					end
				end

				if slot_56_2 and ove_0_21.ee.all_blacklist[slot_56_8.charName] and ove_0_21.ee.all_blacklist[slot_56_8.charName]["Dodge_" .. slot_56_7] and ove_0_21.ee.all_blacklist[slot_56_8.charName]["Dodge_" .. slot_56_7]:get() and slot_56_3 >= player.health / player.maxHealth * 100 then
					if slot_56_4.missile and player.pos2D:dist(slot_56_4.missile.pos2D) / slot_56_4.data.speed < network.latency + 0.25 then
						player:castSpell("pos", 2, mousePos)

						break
					end

					if slot_56_4.data.speed == math.huge or slot_56_4.data.spell_type == "Circular" then
						player:castSpell("pos", 2, mousePos)

						break
					end
				end
			end
		end
	end
end

local function ove_0_129()
	-- print 57
	if not ove_0_21.misc.ks_flash_enable:get() then
		return
	end

	if not ove_0_68 then
		return
	end

	if player:spellSlot(ove_0_68).state ~= 0 then
		return
	end

	if player:spellSlot(0).state ~= 0 then
		return
	end

	if ove_0_72() then
		return
	end

	if ove_0_20.GetPercentHealth(player) < ove_0_21.misc.ks_flash_health:get() then
		return
	end

	if ove_0_103() and ove_0_62 <= ove_0_39.delay + 0.1 then
		return
	end

	local slot_57_0 = 875
	local slot_57_1 = slot_57_0 * slot_57_0
	local slot_57_2 = (ove_0_39.range + 125) * (ove_0_39.range + 125)

	for iter_57_0 = 0, objManager.enemies_n - 1 do
		local slot_57_3 = objManager.enemies[iter_57_0]

		if ove_0_20.IsValidTarget(slot_57_3) and not slot_57_3.isZombie and slot_57_1 >= slot_57_3.pos2D:distSqr(player.pos2D) and slot_57_2 < slot_57_3.pos2D:distSqr(player.pos2D) and ove_0_96(slot_57_3) > slot_57_3.health then
			local slot_57_4 = ove_0_25.linear.get_prediction(ove_0_39, slot_57_3)

			if slot_57_4 and slot_57_4.startPos:distSqr(slot_57_4.endPos) <= 739600 then
				local slot_57_5 = ove_0_20.VectorExtend(player.pos2D, slot_57_3.pos2D, -400)
				local slot_57_6 = ove_0_20.VectorExtend(player.path.serverPos2D, slot_57_3.pos2D, 400)

				if not navmesh.isWall(slot_57_6) and not navmesh.isStructure(slot_57_6) and ove_0_22.core.is_action_safe(slot_57_6, math.huge, ove_0_39.delay) and ove_0_24.core.can_action() then
					player:castSpell("pos", 0, slot_57_5:to3D(slot_57_3.pos.y))
					ove_0_20.DelayAction(function()
						-- print 58
						local slot_58_0 = 875
						local slot_58_1 = slot_58_0 * slot_58_0
						local slot_58_2 = ove_0_39.range * ove_0_39.range

						for iter_58_0 = 0, objManager.enemies_n - 1 do
							local slot_58_3 = objManager.enemies[iter_58_0]

							if ove_0_20.IsValidTarget(slot_58_3) and slot_58_1 >= slot_58_3.pos2D:distSqr(player.pos2D) and slot_58_2 < slot_58_3.pos2D:distSqr(player.pos2D) and ove_0_96(slot_58_3) > slot_58_3.health then
								local slot_58_4 = ove_0_20.VectorExtend(player.path.serverPos2D, slot_58_3.pos2D, 400)

								if not navmesh.isWall(slot_58_4) and not navmesh.isStructure(slot_58_4) and ove_0_22.core.is_action_safe(slot_58_4, math.huge, ove_0_39.delay) then
									player:castSpell("pos", ove_0_68, slot_58_4:to3D(slot_58_3.pos.y))
								end
							end
						end
					end, ove_0_39.delay - network.latency - 0.05)
				end
			end
		end
	end
end

local function ove_0_130()
	-- print 59
	if not ove_0_21.misc.ks_flash_enable:get() then
		return
	end

	if not ove_0_68 then
		return
	end

	if player:spellSlot(ove_0_68).state ~= 0 then
		return
	end

	if player:spellSlot(1).state ~= 0 then
		return
	end

	if ove_0_20.GetPercentHealth(player) < ove_0_21.misc.ks_flash_health:get() then
		return
	end

	if ove_0_103() and ove_0_62 <= ove_0_40.delay + 0.1 then
		return
	end

	local slot_59_0 = ove_0_40.range + 400
	local slot_59_1 = slot_59_0 * slot_59_0
	local slot_59_2 = (ove_0_40.range + 100) * (ove_0_40.range + 100)

	for iter_59_0 = 0, objManager.enemies_n - 1 do
		local slot_59_3 = objManager.enemies[iter_59_0]

		if ove_0_20.IsValidTarget(slot_59_3) and not slot_59_3.isZombie and slot_59_1 >= slot_59_3.pos2D:distSqr(player.pos2D) and slot_59_2 < slot_59_3.pos2D:distSqr(player.pos2D) and ove_0_100(slot_59_3) > slot_59_3.health then
			local slot_59_4 = ove_0_25.linear.get_prediction(ove_0_40, slot_59_3)

			if slot_59_4 and slot_59_1 >= slot_59_4.startPos:distSqr(slot_59_4.endPos) then
				local slot_59_5 = ove_0_20.VectorExtend(player.path.serverPos2D, slot_59_3.pos2D, 400)

				if not navmesh.isWall(slot_59_5) and not navmesh.isStructure(slot_59_5) and ove_0_22.core.is_action_safe(slot_59_5, math.huge, ove_0_40.delay) and ove_0_24.core.can_action() then
					player:castSpell("pos", 1, vec3(slot_59_4.endPos.x, player.pos.y, slot_59_4.endPos.y))
					ove_0_20.DelayAction(function()
						-- print 60
						local slot_60_0 = ove_0_40.range + 400
						local slot_60_1 = slot_60_0 * slot_60_0
						local slot_60_2 = ove_0_40.range * ove_0_40.range

						for iter_60_0 = 0, objManager.enemies_n - 1 do
							local slot_60_3 = objManager.enemies[iter_60_0]

							if ove_0_20.IsValidTarget(slot_60_3) and slot_60_1 >= slot_60_3.pos2D:distSqr(player.pos2D) and slot_60_2 < slot_60_3.pos2D:distSqr(player.pos2D) and ove_0_100(slot_60_3) > slot_60_3.health then
								local slot_60_4 = ove_0_25.linear.get_prediction(ove_0_40, slot_60_3)

								if slot_60_4 and slot_60_1 >= slot_60_4.startPos:distSqr(slot_60_4.endPos) then
									local slot_60_5 = ove_0_20.VectorExtend(player.path.serverPos2D, slot_60_3.pos2D, 400)

									if not navmesh.isWall(slot_60_5) and not navmesh.isStructure(slot_60_5) and ove_0_22.core.is_action_safe(slot_60_5, math.huge, ove_0_39.delay) then
										player:castSpell("pos", ove_0_68, slot_60_5:to3D(slot_60_3.pos.y))
									end
								end
							end
						end
					end, ove_0_40.delay - network.latency - 0.05)
				end
			end
		end
	end
end

local ove_0_131 = {
	ove_0_30.BUFF_ENUM_STUN,
	ove_0_30.BUFF_ENUM_TAUNT,
	ove_0_30.BUFF_ENUM_POLYMORPH,
	ove_0_30.BUFF_ENUM_SNARE,
	ove_0_30.BUFF_ENUM_FEAR,
	ove_0_30.BUFF_ENUM_CHARM,
	ove_0_30.BUFF_ENUM_SUPPRESSION,
	ove_0_30.BUFF_ENUM_KNOCKUP,
	ove_0_30.BUFF_ENUM_KNOCKBACK,
	ove_0_30.BUFF_ENUM_ASLEEP
}

local function ove_0_132(arg_61_0)
	-- print 61
	local slot_61_0 = 0.75 + network.latency

	if ove_0_20.IsCustomLocked(ove_0_131, arg_61_0, 0.75) then
		return false
	end

	if arg_61_0:spellSlot(4).name == "SummonerFlash" and slot_61_0 >= arg_61_0:spellSlot(4).cooldown then
		return true
	end

	if arg_61_0:spellSlot(5).name == "SummonerFlash" and slot_61_0 >= arg_61_0:spellSlot(5).cooldown then
		return true
	end

	local slot_61_1 = ove_0_35[arg_61_0.ptr]

	if ove_0_29[slot_61_1] then
		local slot_61_2 = ove_0_29[slot_61_1].slot
		local slot_61_3 = ove_0_29[slot_61_1].slot2

		if arg_61_0:spellSlot(slot_61_2).level >= 1 and slot_61_0 >= arg_61_0:spellSlot(slot_61_2).cooldown then
			return true
		end

		if slot_61_3 and arg_61_0:spellSlot(slot_61_3).level >= 1 and slot_61_0 >= arg_61_0:spellSlot(slot_61_3).cooldown then
			return true
		end
	end

	if ove_0_36.JarvanIV and arg_61_0.ptr == ove_0_36.JarvanIV_ptr and arg_61_0:spellSlot(0).level >= 1 and arg_61_0:spellSlot(2).level >= 1 and slot_61_0 >= arg_61_0:spellSlot(0).cooldown and slot_61_0 >= arg_61_0:spellSlot(2).cooldown then
		return true
	end

	if ove_0_36.MasterYi and arg_61_0.ptr == ove_0_36.MasterYi_ptr and arg_61_0:spellSlot(0).level >= 1 and slot_61_0 >= arg_61_0:spellSlot(0).cooldown then
		if #ove_0_20.count_minions_in_range(arg_61_0.pos2D, 1000, TEAM_ALLY) >= 1 then
			return true
		end

		if #ove_0_20.count_allies_in_range(arg_61_0.pos2D, 1000) >= 1 then
			return true
		end

		if #ove_0_20.count_minions_in_range(arg_61_0.pos2D, 1000, TEAM_NEUTRAL) >= 1 then
			return true
		end
	end

	if ove_0_36.Rakan and arg_61_0.ptr == ove_0_36.Rakan_ptr then
		if arg_61_0:spellSlot(1).level >= 1 and slot_61_0 >= arg_61_0:spellSlot(1).cooldown then
			return true
		end

		if arg_61_0:spellSlot(2).level >= 1 and slot_61_0 >= arg_61_0:spellSlot(2).cooldown and #ove_0_20.count_enemies_in_range(arg_61_0.pos2D, 1000) >= 2 then
			return true
		end
	end

	if ove_0_36.MonkeyKing and arg_61_0.ptr == ove_0_36.MonkeyKing_ptr and arg_61_0:spellSlot(2).level >= 1 and slot_61_0 >= arg_61_0:spellSlot(2).cooldown then
		if #ove_0_20.count_minions_in_range(arg_61_0.pos2D, 1000, TEAM_ALLY) >= 1 then
			return true
		end

		if #ove_0_20.count_allies_in_range(arg_61_0.pos2D, 1000, true) >= 1 then
			return true
		end

		if #ove_0_20.count_minions_in_range(arg_61_0.pos2D, 1000, TEAM_NEUTRAL) >= 1 then
			return true
		end
	end

	if ove_0_36.Yuumi and arg_61_0.ptr == ove_0_36.Yuumi_ptr and arg_61_0:spellSlot(1).level >= 1 and slot_61_0 >= arg_61_0:spellSlot(1).cooldown and #ove_0_20.count_enemies_in_range(arg_61_0.pos2D, 1000) >= 2 then
		return true
	end

	if ove_0_36.Yasuo and arg_61_0.ptr == ove_0_36.Yasuo_ptr and arg_61_0:spellSlot(2).level >= 1 then
		if #ove_0_20.count_minions_in_range(arg_61_0.pos2D, 1000, TEAM_ALLY) >= 1 then
			return true
		end

		if #ove_0_20.count_allies_in_range(arg_61_0.pos2D, 1000, true) >= 1 then
			return true
		end

		if #ove_0_20.count_minions_in_range(arg_61_0.pos2D, 1000, TEAM_NEUTRAL) >= 1 then
			return true
		end
	end

	if ove_0_36.Yone and arg_61_0.ptr == ove_0_36.Yone_ptr and arg_61_0:spellSlot(0).level >= 1 and slot_61_0 >= arg_61_0:spellSlot(0).cooldown and arg_61_0.isVisible and not arg_61_0.isDead and arg_61_0.buff.yoneq3ready then
		return true
	end

	return false
end

local function ove_0_133()
	-- print 62
	if not ove_0_21.rset.r_aaa_enable:get() then
		return
	end

	if not ove_0_21.rset.r_aaa_q3r:get() then
		return
	end

	if player:spellSlot(3).state ~= 0 then
		return
	end

	if not ove_0_65 then
		return
	end

	if ove_0_20.GetPercentHealth(player) < ove_0_21.rset.r_aaa_q3r_hp_yone:get() then
		return
	end

	local slot_62_0 = ove_0_60()

	if ove_0_20.IsValidTarget(slot_62_0) and not slot_62_0.isZombie then
		if ove_0_20.GetPercentHealth(slot_62_0) > ove_0_21.rset.r_aaa_q3r_hp:get() then
			return
		end

		if ove_0_21.rset.r_aaa_q3r_outside:get() and slot_62_0.path.serverPos2D:distSqr(player.path.serverPos2D) <= 360000 then
			return
		end

		if ove_0_21.rset.r_aaa_q3r_escape:get() and ove_0_132(slot_62_0) then
			return
		end

		local slot_62_1 = ove_0_25.linear.get_prediction(ove_0_42, slot_62_0)
		local slot_62_2 = ove_0_42.range * ove_0_42.range

		if slot_62_1 and slot_62_2 >= slot_62_1.startPos:distSqr(slot_62_1.endPos) then
			if ove_0_21.rset.r_aaa_slowpred:get() and not ove_0_106(ove_0_42, slot_62_1, slot_62_0) then
				return
			end

			if not ove_0_20.IsOnLineSegment(slot_62_1.endPos, ove_0_65.startPos2D, ove_0_65.endPos2D, 100 + slot_62_0.boundingRadius, 990) then
				return
			end

			player:castSpell("pos", 3, vec3(slot_62_1.endPos.x, slot_62_0.pos.y, slot_62_1.endPos.y))
		end
	end
end

local ove_0_134 = {
	ove_0_30.BUFF_ENUM_STUN,
	ove_0_30.BUFF_ENUM_TAUNT,
	ove_0_30.BUFF_ENUM_POLYMORPH,
	ove_0_30.BUFF_ENUM_SNARE,
	ove_0_30.BUFF_ENUM_FEAR,
	ove_0_30.BUFF_ENUM_CHARM,
	ove_0_30.BUFF_ENUM_SUPPRESSION,
	ove_0_30.BUFF_ENUM_KNOCKUP,
	ove_0_30.BUFF_ENUM_ASLEEP
}

local function ove_0_135()
	-- print 63
	if not ove_0_21.rset.r_aaa_enable:get() then
		return
	end

	if not ove_0_21.rset.r_aaa_cced:get() then
		return
	end

	if player:spellSlot(3).state ~= 0 then
		return
	end

	if ove_0_20.GetPercentHealth(player) < ove_0_21.rset.r_aaa_cced_hp_yone:get() then
		return
	end

	local slot_63_0 = (ove_0_42.range + 200) * (ove_0_42.range + 200)
	local slot_63_1 = ove_0_42.range * ove_0_42.range

	for iter_63_0 = 0, objManager.enemies_n - 1 do
		local slot_63_2 = objManager.enemies[iter_63_0]

		if ove_0_20.IsValidTarget(slot_63_2) and slot_63_0 >= slot_63_2.pos2D:distSqr(player.pos2D) then
			if ove_0_20.GetPercentHealth(slot_63_2) > ove_0_21.rset.r_aaa_cced_hp:get() then
				return
			end

			local slot_63_3 = ove_0_21.rset.r_aaa_cced_time:get() / 1000

			if not ove_0_20.IsCustomLocked(ove_0_134, slot_63_2, slot_63_3) then
				return
			end

			local slot_63_4 = ove_0_25.linear.get_prediction(ove_0_42, slot_63_2)

			if slot_63_4 and slot_63_1 >= slot_63_4.startPos:distSqr(slot_63_4.endPos) then
				player:castSpell("pos", 3, vec3(slot_63_4.endPos.x, slot_63_2.pos.y, slot_63_4.endPos.y))
			end
		end
	end
end

local function ove_0_136()
	-- print 64
	if not ove_0_21.rset.r_aaa_enable:get() then
		return
	end

	if not ove_0_21.rset.r_aaa_killable:get() then
		return
	end

	if player:spellSlot(3).state ~= 0 then
		return
	end

	if ove_0_20.GetPercentHealth(player) < ove_0_21.rset.r_aaa_killable_hp_yone:get() then
		return
	end

	if ove_0_103() and ove_0_62 <= ove_0_42.delay + ove_0_39.delay + 0.1 then
		return
	end

	local slot_64_0 = (ove_0_42.range + 200) * (ove_0_42.range + 200)
	local slot_64_1 = ove_0_42.range * ove_0_42.range

	for iter_64_0 = 0, objManager.enemies_n - 1 do
		local slot_64_2 = objManager.enemies[iter_64_0]

		if ove_0_20.IsValidTarget(slot_64_2) and not slot_64_2.isZombie and slot_64_0 >= slot_64_2.pos2D:distSqr(player.pos2D) then
			if ove_0_21.rset.r_aaa_killable_outside:get() and slot_64_2.path.serverPos2D:distSqr(player.path.serverPos2D) <= 360000 then
				return
			end

			if ove_0_102(slot_64_2) + ove_0_96(slot_64_2) < slot_64_2.health then
				return
			end

			if ove_0_21.rset.r_aaa_killable_escape:get() and ove_0_132(slot_64_2) then
				return
			end

			local slot_64_3 = ove_0_25.linear.get_prediction(ove_0_42, slot_64_2)

			if slot_64_3 and slot_64_1 >= slot_64_3.startPos:distSqr(slot_64_3.endPos) then
				if ove_0_21.rset.r_aaa_slowpred:get() and not ove_0_106(ove_0_42, slot_64_3, slot_64_2) then
					return
				end

				player:castSpell("pos", 3, vec3(slot_64_3.endPos.x, slot_64_2.pos.y, slot_64_3.endPos.y))
			end
		end
	end
end

local function ove_0_137(arg_65_0)
	-- print 65
	local slot_65_0 = ove_0_21.misc.follow_up_cc_time:get() / 1000

	return ove_0_20.IsCustomLocked(ove_0_134, arg_65_0, slot_65_0)
end

local ove_0_138 = 2890000

local function ove_0_139()
	-- print 66
	for iter_66_0 = 0, objManager.enemies_n - 1 do
		local slot_66_0 = objManager.enemies[iter_66_0]

		if ove_0_20.IsValidTarget(slot_66_0) and player.pos2D:distSqr(slot_66_0.pos2D) <= ove_0_138 and slot_66_0.health / slot_66_0.maxHealth * 100 <= ove_0_21.misc.follow_up_hp_enemies:get() and ove_0_137(slot_66_0) then
			return true
		end
	end

	return false
end

local function ove_0_140(arg_67_0, arg_67_1, arg_67_2, arg_67_3)
	-- print 67
	local slot_67_0 = 0
	local slot_67_1 = (arg_67_3 + 250) * (arg_67_3 + 250)
	local slot_67_2 = ove_0_20.VectorExtend(arg_67_0, arg_67_1, arg_67_3)
	local slot_67_3 = arg_67_2 * arg_67_2

	for iter_67_0 = 0, objManager.enemies_n - 1 do
		local slot_67_4 = objManager.enemies[iter_67_0]

		if ove_0_20.IsValidTarget(slot_67_4) and slot_67_1 >= slot_67_4.pos2D:distSqr(player.pos2D) and ove_0_137(slot_67_4) and ove_0_20.GetPercentHealth(slot_67_4) <= ove_0_21.misc.follow_up_hp_enemies:get() then
			local slot_67_5, slot_67_6, slot_67_7 = ove_0_20.VectorPointProjectionOnLineSegment(arg_67_0, slot_67_2, slot_67_4.pos2D)

			if slot_67_7 and slot_67_3 > slot_67_4.pos2D:distSqr(slot_67_5) and arg_67_0:distSqr(slot_67_2) >= arg_67_0:distSqr(slot_67_4.pos2D) then
				slot_67_0 = slot_67_0 + 1
			end
		end
	end

	return slot_67_0
end

local function ove_0_141()
	-- print 68
	local slot_68_0 = 0
	local slot_68_1
	local slot_68_2 = ove_0_42.range

	if ove_0_21.misc.follow_up_flash:get() and ove_0_68 and player:spellSlot(ove_0_68).state == 0 then
		slot_68_2 = slot_68_2 + 400
	end

	if ove_0_21.misc.follow_up_E:get() and not ove_0_103() and player:spellSlot(2).state == 0 then
		slot_68_2 = slot_68_2 + 59
	end

	local slot_68_3 = slot_68_2 * slot_68_2

	for iter_68_0 = 0, objManager.enemies_n - 1 do
		local slot_68_4 = objManager.enemies[iter_68_0]

		if ove_0_20.IsValidTarget(slot_68_4) and slot_68_3 >= slot_68_4.pos2D:distSqr(player.pos2D) and ove_0_137(slot_68_4) and ove_0_20.GetPercentHealth(slot_68_4) <= ove_0_21.misc.follow_up_hp_enemies:get() then
			local slot_68_5 = ove_0_140(player.path.serverPos2D, slot_68_4.path.serverPos2D, ove_0_42.width, slot_68_2)

			if slot_68_0 < slot_68_5 then
				slot_68_0 = slot_68_5
				slot_68_1 = slot_68_4.pos2D
			end
		end
	end

	return slot_68_0, slot_68_1
end

local function ove_0_142()
	-- print 69
	local slot_69_0 = {}
	local slot_69_1 = ove_0_42.range

	if ove_0_21.misc.follow_up_flash:get() and ove_0_68 and player:spellSlot(ove_0_68).state == 0 then
		slot_69_1 = slot_69_1 + 400
	end

	if ove_0_21.misc.follow_up_E:get() and not ove_0_103() and player:spellSlot(2).state == 0 then
		slot_69_1 = slot_69_1 + 59
	end

	local slot_69_2 = slot_69_1 * slot_69_1
	local slot_69_3 = 0

	for iter_69_0 = 0, objManager.enemies_n - 1 do
		local slot_69_4 = objManager.enemies[iter_69_0]

		if ove_0_20.IsValidTarget(slot_69_4) and slot_69_2 >= slot_69_4.pos2D:distSqr(player.pos2D) and ove_0_137(slot_69_4) and ove_0_20.GetPercentHealth(slot_69_4) <= ove_0_21.misc.follow_up_hp_enemies:get() then
			slot_69_0[slot_69_3] = slot_69_4.pos2D
			slot_69_3 = slot_69_3 + 1
		end
	end

	return slot_69_0, slot_69_3
end

local function ove_0_143()
	-- print 70
	local slot_70_0 = 0
	local slot_70_1
	local slot_70_2 = ove_0_42.range

	if ove_0_21.misc.follow_up_flash:get() and ove_0_68 and player:spellSlot(ove_0_68).state == 0 then
		slot_70_2 = slot_70_2 + 400
	end

	if ove_0_21.misc.follow_up_E:get() and not ove_0_103() and player:spellSlot(2).state == 0 then
		slot_70_2 = slot_70_2 + 59
	end

	local slot_70_3, slot_70_4 = ove_0_142()
	local slot_70_5, slot_70_6 = ove_0_20.CustomMEC(slot_70_3, slot_70_4)

	if slot_70_5 then
		slot_70_1 = slot_70_5
		slot_70_0 = ove_0_140(player.path.serverPos2D, slot_70_1, ove_0_42.width, slot_70_2)
	end

	return slot_70_0, slot_70_1
end

local function ove_0_144()
	-- print 71
	if not ove_0_21.misc.follow_up_enable:get() then
		return
	end

	if ove_0_20.IsRecalling(player) then
		return
	end

	if player:spellSlot(3).state ~= 0 then
		return
	end

	local slot_71_0 = ove_0_103()

	if slot_71_0 and ove_0_62 <= ove_0_42.delay + 0.1 then
		return
	end

	if ove_0_20.GetPercentHealth(player) < ove_0_21.misc.follow_up_hp_self:get() then
		return
	end

	if not ove_0_139() then
		return
	end

	local slot_71_1, slot_71_2 = ove_0_141()
	local slot_71_3, slot_71_4 = ove_0_143()

	if slot_71_2 and slot_71_4 and (slot_71_3 >= ove_0_21.misc.follow_up_count:get() or slot_71_1 >= ove_0_21.misc.follow_up_count:get()) then
		if slot_71_1 <= slot_71_3 then
			if slot_71_4:distSqr(player.pos2D) <= ove_0_42.range * ove_0_42.range then
				if not slot_71_0 then
					if player:spellSlot(2).state == 0 then
						if ove_0_22.core.is_action_safe(slot_71_4, 1500, 0.25) then
							player:castSpell("pos", 2, slot_71_4:to3D(player.pos.y))
							player:castSpell("pos", 3, slot_71_4:to3D(player.pos.y))
						end
					else
						player:castSpell("pos", 3, slot_71_4:to3D(player.pos.y))
					end
				else
					player:castSpell("pos", 3, slot_71_4:to3D(player.pos.y))
				end
			end

			if slot_71_4:distSqr(player.pos2D) <= (ove_0_42.range + 58) * (ove_0_42.range + 58) and slot_71_4:distSqr(player.pos2D) > ove_0_42.range * ove_0_42.range and not slot_71_0 and player:spellSlot(2).state == 0 then
				local slot_71_5 = ove_0_20.VectorExtend(player.path.serverPos2D, slot_71_4, 200)

				if slot_71_5 and ove_0_22.core.is_action_safe(slot_71_4, 1500, 0.25) and not navmesh.isWall(slot_71_5) and not navmesh.isStructure(slot_71_5) then
					player:castSpell("pos", 2, slot_71_4:to3D(player.pos.y))
					player:castSpell("pos", 3, slot_71_4:to3D(player.pos.y))
				end
			end

			if slot_71_4:distSqr(player.pos2D) <= (ove_0_42.range + 459) * (ove_0_42.range + 459) and slot_71_4:distSqr(player.pos2D) > (ove_0_42.range + 100) * (ove_0_42.range + 100) and ove_0_68 and player:spellSlot(ove_0_68).state == 0 then
				if not slot_71_0 then
					if player:spellSlot(2).state == 0 then
						local slot_71_6 = ove_0_20.VectorExtend(player.path.serverPos2D, slot_71_4, 459)

						if slot_71_6 and ove_0_22.core.is_action_safe(slot_71_4, math.huge, 0) and not navmesh.isWall(slot_71_6) and not navmesh.isStructure(slot_71_6) then
							player:castSpell("pos", 2, slot_71_4:to3D(player.pos.y))
							player:castSpell("pos", ove_0_68, slot_71_4:to3D(player.pos.y))
							player:castSpell("pos", 3, slot_71_4:to3D(player.pos.y))
						end
					else
						local slot_71_7 = ove_0_20.VectorExtend(player.path.serverPos2D, slot_71_4, 400)

						if slot_71_7 and ove_0_22.core.is_action_safe(slot_71_4, math.huge, 0) and not navmesh.isWall(slot_71_7) and not navmesh.isStructure(slot_71_7) then
							player:castSpell("pos", ove_0_68, slot_71_4:to3D(player.pos.y))
							player:castSpell("pos", 3, slot_71_4:to3D(player.pos.y))
						end
					end
				else
					local slot_71_8 = ove_0_20.VectorExtend(player.path.serverPos2D, slot_71_4, 400)

					if slot_71_8 and ove_0_22.core.is_action_safe(slot_71_4, math.huge, 0) and not navmesh.isWall(slot_71_8) and not navmesh.isStructure(slot_71_8) then
						player:castSpell("pos", ove_0_68, slot_71_4:to3D(player.pos.y))
						player:castSpell("pos", 3, slot_71_4:to3D(player.pos.y))
					end
				end
			end
		else
			if slot_71_2:distSqr(player.pos2D) <= ove_0_42.range * ove_0_42.range then
				if not slot_71_0 then
					if player:spellSlot(2).state == 0 then
						if ove_0_22.core.is_action_safe(slot_71_2, 1500, 0.25) then
							player:castSpell("pos", 2, slot_71_2:to3D(player.pos.y))
							player:castSpell("pos", 3, slot_71_2:to3D(player.pos.y))
						end
					else
						player:castSpell("pos", 3, slot_71_2:to3D(player.pos.y))
					end
				else
					player:castSpell("pos", 3, slot_71_2:to3D(player.pos.y))
				end
			end

			if slot_71_2:distSqr(player.pos2D) <= (ove_0_42.range + 58) * (ove_0_42.range + 58) and slot_71_2:distSqr(player.pos2D) > ove_0_42.range * ove_0_42.range and not slot_71_0 and player:spellSlot(2).state == 0 then
				local slot_71_9 = ove_0_20.VectorExtend(player.path.serverPos2D, slot_71_2, 200)

				if slot_71_9 and ove_0_22.core.is_action_safe(slot_71_2, 1500, 0.25) and not navmesh.isWall(slot_71_9) and not navmesh.isStructure(slot_71_9) then
					player:castSpell("pos", 2, slot_71_2:to3D(player.pos.y))
					player:castSpell("pos", 3, slot_71_2:to3D(player.pos.y))
				end
			end

			if slot_71_2:distSqr(player.pos2D) <= (ove_0_42.range + 459) * (ove_0_42.range + 459) and slot_71_2:distSqr(player.pos2D) > (ove_0_42.range + 100) * (ove_0_42.range + 100) and ove_0_68 and player:spellSlot(ove_0_68).state == 0 then
				if not slot_71_0 then
					if player:spellSlot(2).state == 0 then
						local slot_71_10 = ove_0_20.VectorExtend(player.path.serverPos2D, slot_71_2, 459)

						if slot_71_10 and ove_0_22.core.is_action_safe(slot_71_2, math.huge, 0) and not navmesh.isWall(slot_71_10) and not navmesh.isStructure(slot_71_10) then
							player:castSpell("pos", 2, slot_71_2:to3D(player.pos.y))
							player:castSpell("pos", ove_0_68, slot_71_2:to3D(player.pos.y))
							player:castSpell("pos", 3, slot_71_2:to3D(player.pos.y))
						end
					else
						local slot_71_11 = ove_0_20.VectorExtend(player.path.serverPos2D, slot_71_2, 400)

						if slot_71_11 and ove_0_22.core.is_action_safe(slot_71_2, math.huge, 0) and not navmesh.isWall(slot_71_11) and not navmesh.isStructure(slot_71_11) then
							player:castSpell("pos", ove_0_68, slot_71_2:to3D(player.pos.y))
							player:castSpell("pos", 3, slot_71_2:to3D(player.pos.y))
						end
					end
				else
					local slot_71_12 = ove_0_20.VectorExtend(player.path.serverPos2D, slot_71_2, 400)

					if slot_71_12 and ove_0_22.core.is_action_safe(slot_71_2, math.huge, 0) and not navmesh.isWall(slot_71_12) and not navmesh.isStructure(slot_71_12) then
						player:castSpell("pos", ove_0_68, slot_71_2:to3D(player.pos.y))
						player:castSpell("pos", 3, slot_71_2:to3D(player.pos.y))
					end
				end
			end
		end
	end
end

local function ove_0_145()
	-- print 72
	if not ove_0_21.flee.q_flee:get() and not ove_0_21.flee.q3_flee:get() then
		return
	end

	if player:spellSlot(0).state ~= 0 then
		return
	end

	if ove_0_72() then
		if ove_0_21.flee.q3_flee:get() then
			player:castSpell("pos", 0, mousePos)
		end
	elseif ove_0_21.flee.q_flee:get() then
		local slot_72_0 = ove_0_54()

		if ove_0_20.IsValidTarget(slot_72_0) then
			local slot_72_1 = ove_0_25.linear.get_prediction(ove_0_39, slot_72_0)
			local slot_72_2 = ove_0_39.range * ove_0_39.range

			if slot_72_1 and slot_72_2 >= slot_72_1.startPos:distSqr(slot_72_1.endPos) then
				player:castSpell("pos", 0, vec3(slot_72_1.endPos.x, slot_72_0.pos.y, slot_72_1.endPos.y))
			end
		end
	end
end

local function ove_0_146()
	-- print 73
	if not ove_0_21.flee.w_flee:get() then
		return
	end

	if player:spellSlot(1).state ~= 0 then
		return
	end

	if ove_0_21.flee.w_flee_delay:get() < 500 and ove_0_40.delay > ove_0_21.flee.w_flee_delay:get() / 1000 then
		return
	end

	local slot_73_0 = ove_0_56()

	if ove_0_20.IsValidTarget(slot_73_0) then
		local slot_73_1 = ove_0_25.linear.get_prediction(ove_0_40, slot_73_0)
		local slot_73_2 = ove_0_40.range * ove_0_40.range

		if slot_73_1 and slot_73_2 >= slot_73_1.startPos:distSqr(slot_73_1.endPos) then
			player:castSpell("pos", 1, vec3(slot_73_1.endPos.x, slot_73_0.pos.y, slot_73_1.endPos.y))
		end
	end
end

local function ove_0_147()
	-- print 74
	if not ove_0_21.flee.flee_key:get() then
		return
	end

	player:move(vec3(mousePos.x, mousePos.y, mousePos.z))
	ove_0_146()
	ove_0_145()
end

local function ove_0_148()
	-- print 75
	local slot_75_0 = (player.attackSpeedMod - 1) * 100

	ove_0_39.delay = mathf.clamp(0.17499999701977, 10, 0.34999999403954 * (1 - slot_75_0 / 2.4 / 100))
	ove_0_40.delay = mathf.clamp(0.25, 10, 0.5 * (1 - slot_75_0 / 2.4 / 100))

	if ove_0_21.combo.w_combo_NOweave_dps:get() then
		ove_0_41 = ove_0_40.delay
	end

	if ove_0_72() then
		ove_0_39.range = 950
		ove_0_39.width = 100
		ove_0_39.speed = 1500
	else
		ove_0_39.range = 475
		ove_0_39.width = 40
		ove_0_39.speed = 5000
	end

	ove_0_129()
	ove_0_130()
	ove_0_144()
	ove_0_124()
	ove_0_133()
	ove_0_128()
	ove_0_127()

	if ove_0_21.keys.combokey:get() then
		ove_0_111()
	end

	ove_0_136()
	ove_0_135()

	if ove_0_21.keys.clearkey:get() then
		ove_0_122()
	end

	if ove_0_21.keys.lastkey:get() then
		ove_0_126()
	end

	if ove_0_21.keys.harasskey:get() then
		ove_0_116()
	end

	ove_0_147()
	ove_0_123()
	ove_0_52()
	ove_0_49()

	ove_0_68 = ove_0_20.GetFlashSlot()

	if menu:isopen() then
		ove_0_20.MenuHeaderWizard(ove_0_21)
	end
end

local ove_0_149 = 0

local function ove_0_150()
	-- print 76
	if ove_0_149 - game.time <= 0 or ove_0_149 <= 0 then
		ove_0_149 = game.time + 1
	end

	return ove_0_149 - game.time
end

local function ove_0_151()
	-- print 77
	local slot_77_0 = ove_0_150()
	local slot_77_1 = 300

	if slot_77_0 >= 0 then
		slot_77_1 = slot_77_1 - slot_77_0 / 0.1 * 30
	end

	return slot_77_1
end

local function ove_0_152()
	-- print 78
	local slot_78_0 = ove_0_150()
	local slot_78_1 = 0

	if slot_78_0 >= 0 then
		slot_78_1 = slot_78_1 - slot_78_0 / 0.1 * 30
	end

	return slot_78_1
end

local function ove_0_153()
	-- print 79
	local slot_79_0 = ove_0_150()
	local slot_79_1 = 300

	if slot_79_0 >= 0 then
		slot_79_1 = slot_79_1 - slot_79_0 / 0.1 * 60
	end

	return slot_79_1
end

local ove_0_154 = ove_0_21.draws.draw_yone_color:get() == 1 and graphics.argb(255, 255, 0, 0) or graphics.argb(255, 255, 106, 175)
local ove_0_155 = ove_0_21.draws.draw_yone_color:get() == 1 and graphics.argb(255, 0, 0, 0) or graphics.argb(255, 26, 221, 255)
local ove_0_156 = ove_0_21.draws.draw_yone_color:get() == 1 and 3 or 2

local function ove_0_157(arg_80_0)
	-- print 80
	if not ove_0_20.IsOnScreen(arg_80_0) then
		return
	end

	local slot_80_0 = arg_80_0
	local slot_80_1 = slot_80_0.x
	local slot_80_2 = slot_80_0.y
	local slot_80_3 = slot_80_0.z
	local slot_80_4 = 2 * math.pi / 4
	local slot_80_5 = vec2.array(5)
	local slot_80_6 = 1
	local slot_80_7 = game.time

	for iter_80_0 = 1, 5 do
		slot_80_6 = slot_80_6 + 1

		local slot_80_8 = slot_80_6 % 2 == 0 and ove_0_153() or ove_0_153()

		slot_80_5[iter_80_0 - 1] = graphics.world_to_screen_xyz(slot_80_1 + slot_80_8 * math.sin(slot_80_4 * iter_80_0 + slot_80_7), slot_80_2, slot_80_3 + slot_80_8 * math.cos(slot_80_4 * iter_80_0 + slot_80_7))
	end

	graphics.draw_line_strip_2D(slot_80_5, 3, ove_0_154, 5)
	graphics.draw_circle(arg_80_0, ove_0_151(), 3, ove_0_155, 4)
	graphics.draw_circle(arg_80_0, ove_0_152(), 3, ove_0_155, 4)
end

local function ove_0_158()
	-- print 81
	if not ove_0_28.enable() then
		return
	end

	local slot_81_0 = {
		{
			position = 0.4,
			name = "Farming:",
			menu = ove_0_21.farm.farm_toggle
		},
		{
			position = 0.2,
			name = "Safe Q3:",
			menu = ove_0_21.safe.q3_safe_toggle
		},
		{
			position = 0,
			name = "Force R:",
			menu = ove_0_21.misc.force_r_key,
			other_activator = ove_0_50
		},
		{
			position = -0.2,
			name = "Flee:",
			menu = ove_0_21.flee.flee_key
		},
		{
			other_activator_controls_state = true,
			name = "Panic clear:",
			position = -0.4,
			panic_clear = true,
			panic_clear_menu = ove_0_21.keys.clearkey,
			menu = ove_0_21.farm.panic_clear_toggle,
			other_activator = ove_0_21.farm.panic_clear_toggle:get() and ove_0_21.keys.clearkey:get(),
			state_yellow = ove_0_21.farm.farm_toggle:get()
		}
	}

	ove_0_28.draw(slot_81_0)
end

local function ove_0_159()
	-- print 82
	ove_0_154 = ove_0_21.draws.draw_yone_color:get() == 1 and graphics.argb(255, 255, 0, 0) or graphics.argb(255, 255, 106, 175)
	ove_0_155 = ove_0_21.draws.draw_yone_color:get() == 1 and graphics.argb(255, 0, 0, 0) or graphics.argb(255, 26, 221, 255)
	ove_0_156 = ove_0_21.draws.draw_yone_color:get() == 1 and 3 or 2

	if ove_0_61 then
		ove_0_157(ove_0_61.pos)
	end

	if ove_0_21.draws.draw_panic_clear:get() and ove_0_21.farm.panic_clear_toggle:get() and ove_0_21.keys.clearkey:get() then
		local slot_82_0, slot_82_1 = graphics.text_area("Panic Clear", 15)
		local slot_82_2 = slot_82_0 / 2
		local slot_82_3 = slot_82_1 / 2

		graphics.draw_text_2D("Panic Clear", 15, game.cursorPos.x - slot_82_2, game.cursorPos.y - slot_82_3, COLOR_WHITE)
	end

	local slot_82_4 = graphics.world_to_screen(vec3(player.pos.x, player.pos.y, player.pos.z))

	if player.isOnScreen then
		if ove_0_21.draws.draw_e_timer:get() and ove_0_103() and ove_0_62 then
			local slot_82_5 = ove_0_62 - game.time

			if slot_82_5 > 0 then
				slot_82_5 = string.format("%.2f", ove_0_62 - game.time)
			else
				slot_82_5 = "0.00"
			end

			local slot_82_6 = "E time: " .. slot_82_5
			local slot_82_7, slot_82_8 = graphics.text_area(slot_82_6, 17)
			local slot_82_9 = slot_82_7 / 2

			graphics.draw_text_2D(slot_82_6, 17, slot_82_4.x - slot_82_9, slot_82_4.y, graphics.argb(255, 255, 255, 0))
		end

		if ove_0_21.draws.draw_bog:get() and ove_0_21.misc.force_r_slowpred:get() and (ove_0_21.misc.force_r_key:get() or ove_0_50) then
			local slot_82_10 = "Force R activated"
			local slot_82_11, slot_82_12 = graphics.text_area(slot_82_10, 17)
			local slot_82_13 = slot_82_11 / 2
			local slot_82_14 = slot_82_12 / 2

			graphics.draw_text_2D(slot_82_10, 17, slot_82_4.x - slot_82_13, slot_82_4.y - slot_82_14 - 5, graphics.argb(255, 255, 255, 0))
		end

		if ove_0_21.draws.drawq:get() then
			graphics.draw_circle(player.pos, ove_0_39.range, 2, ove_0_154, 96)
		end

		if ove_0_21.draws.draww:get() then
			graphics.draw_circle(player.pos, ove_0_40.range, ove_0_156, ove_0_155, 96)
		end

		if ove_0_21.draws.drawr:get() then
			graphics.draw_circle(player.pos, ove_0_42.range, ove_0_156, ove_0_155, 96)
		end
	end

	ove_0_158()
end

cb.add(cb.spell, ove_0_71)
ove_0_24.combat.register_f_pre_tick(ove_0_148)
cb.add(cb.create_minion, ove_0_63)
cb.add(cb.delete_minion, ove_0_64)
cb.add(cb.create_missile, ove_0_66)
cb.add(cb.delete_missile, ove_0_67)
cb.add(cb.draw, ove_0_159)
ove_0_20.Successfully_Loaded(true)

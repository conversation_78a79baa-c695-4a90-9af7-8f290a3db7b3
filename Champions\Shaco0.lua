local ShacoPlugin = {}

local ts = module.internal("TS")
local orb = module.internal("orb")
local preds = module.internal("pred")
local ui = module.load("<PERSON>", "ui");
local DelayAction = module.load("<PERSON>", "Core/DelayAction")
local DelayTick = module.load("<PERSON>", "Core/DelayTick")
local Prediction = module.load("<PERSON>", "Core/Prediction")
local BuffManager = module.load("<PERSON>", "Library/BuffManager")
local CalculateManager = module.load("<PERSON>", "Library/CalculateManager")
--local FarmManager = module.load("<PERSON>", "Library/FarmManager")
local ItemManager = module.load("<PERSON>", "Library/ItemManager")
local NetManager = module.load("<PERSON>", "Library/NetManager")
local ObjectManager = module.load("<PERSON>", "Library/ObjectManager")
local OrbManager = module.load("<PERSON>", "Library/OrbManager")
local SpellManager = module.load("<PERSON>", "Library/SpellManager")
local VectorManager = module.load("<PERSON>", "Library/VectorManager")
local MyCommon = module.load("<PERSON>", "Library/ExtraManager")
local common = module.load("Brian", "Utility/common3")
local database = module.load("Brian", "Damage/x")
local Curses = module.load("Brian", "Curses");
local evade = module.seek("evade")
local str = {[3] = "R"}
local enemies = common.GetEnemyHeroes()

local wPred = {
	delay = 0.25,
	width = 200,
	speed = math.huge,
	boundingRadiusMod = 0,
	collision = {hero = false, minion = false}
}


local MyMenu

function ShacoPlugin.Load(GlobalMenu)
    MyMenu = GlobalMenu

    MyMenu.Key:keybind("run", "Marathon Mode", "Z", false)

    MyMenu:menu("combo", "Combo Settings")
    MyMenu.combo:header("xd", "W Settings")
    MyMenu.combo:boolean("w", "Smart W", true)

    MyMenu.combo:header("xd", "E Settings")
    MyMenu.combo:boolean("e", "Use E", true)

    MyMenu.combo:header("xd", "R Settings")
    MyMenu.combo:boolean("follow", "Follow with R", true)


    MyMenu:menu("harass", "Harass Settings")
    MyMenu.harass:header("xd", "E Settings")
    MyMenu.harass:boolean("e", "use E", true)
    MyMenu.harass:slider("Mana", "Min. Mana Percent: ", 50, 0, 100, 10)

    MyMenu:menu("jungleclear", "Jungle Clear Settings")
    MyMenu.jungleclear:header("xd", "Jungle Settings")
    MyMenu.jungleclear:boolean("w", "Use W", true)
    MyMenu.jungleclear:boolean("e", "Use E", true)
    MyMenu.jungleclear:slider("mana", "Min. Mana Percent: ", 10, 0, 100, 10)

    MyMenu:menu("auto", "Automatic Settings")
    MyMenu.auto:header("xd", "KillSteal Settings")
    MyMenu.auto:boolean("uks", "Use Killsteal", true)
    MyMenu.auto:boolean("uksq", "Use E on Killsteal", true)
    MyMenu.auto:header("xd", "Ult Dodge Settings")
    MyMenu.auto:boolean("zhonya", "Use R", true)
    MyMenu.auto:boolean("target", "Use on Target Spells", true)
    MyMenu.auto.target:set('tooltip', "Works in Veigar/Darius/Syndra/Tristana/Brand R")
    MyMenu.auto:menu("x", "Enemy Spells for R")
    for _, i in pairs(database) do
        for l, k in pairs(common.GetEnemyHeroes()) do
            if not database[_] then return end
            if i.charName == k.charName then
                if i.displayname == "" then
                    i.displayname = _
                end
                if i.danger == 0 then
                        i.danger = 1
                end
                if (MyMenu.auto.x[i.charName] == nil) then
                    MyMenu.auto.x:menu(i.charName, i.charName)
                end
                MyMenu.auto.x[i.charName]:menu(_, "" .. i.charName .. " | " .. (str[i.slot] or "?") .. " " .. _)
                MyMenu.auto.x[i.charName][_]:boolean("Dodge", "Enable Block", false)
            end
        end
    end

    MyMenu:menu("draws", "Draw Settings")
    MyMenu.draws:header("xd", "Drawing Options")
    MyMenu.draws:boolean("q", "Draw Q Range", true)
    MyMenu.draws:color("colorq", "^ color", 255, 233, 121, 121)
    MyMenu.draws:boolean("e", "Draw E Range", true)
    MyMenu.draws:color("colore", "^ color", 255, 233, 121, 121)
end

local function select_target(res, obj, dist)
	if dist > 625 then
		return
	end
	res.obj = obj
	return true
end

local function get_target(func)
	return ts.get_result(func).obj
end

local function SexyPrint(message)
   local sexyName = "<font color=\"#E41B17\">[<b>¤ Cyrex ¤</b>]:</font>"
   local fontColor = "FFFFFF"
   chat.print(sexyName .. " <font color=\"#" .. fontColor .. "\">" .. message .. "</font>")
end

local ElvlDmg = {0.6, 0.75, 0.9, 1.05, 1.2}
local function eDmg(target)
	if player.path.serverPos:dist(target.path.serverPos) < 625 then 
		local base_damage = (25 + (30 * player:spellSlot(2).level)) + (common.GetTotalAP() * 0.75) + (ElvlDmg[player:spellSlot(2).level] * common.GetBonusAD())
		local total = base_damage
		return common.CalculatePhysicalDamage(target, total)
	end
end

local TargetSelectionFollow = function(res, obj, dist)
	if dist < 1125 then
		res.obj = obj
		return true
	end
end
local GetTargetFollow = function()
	return ts.get_result(TargetSelectionFollow).obj
end

local function RFollow()
	if MyMenu.combo.follow:get() then
		if player:spellSlot(3).name == "HallucinateGuide" then
			local target = GetTargetFollow()
			if target and target.isVisible then
				if common.IsValidTarget(target) then
					player:castSpell("pos", 3, target.pos)
				end
			end
		end
	end
end


local function HasSionBuff(e)
	for i = 0, e.buffManager.count - 1 do
		local buff = e.buffManager:get(i)
		if buff and buff.valid and buff.name == 'sionpassivezombie' then
			return true
		end
	end
end

local function CastR()
	if player:spellSlot(3).state == 0 then
		player:castSpell("self", 3)
	end
end

local function smartW(target)
	if player.path.serverPos:distSqr(target.path.serverPos) < (425 * 425) then
		local seg = preds.linear.get_prediction(wPred, target)
		if seg then
			local x = seg.endPos.x
			local y = seg.endPos.y

			if x < player.x then
				x = x - 103
			elseif x > player.x then
				x = x + 103
			end

			if y < player.z then
				y = y - 103
			elseif y > player.z then
				y = y + 103
			end

			player:castSpell("pos", 1, vec3(x, target.y, y))
		end
	end
end

local function Combo()
	local target = get_target(select_target)
	if target and common.IsValidTarget(target) and not HasSionBuff(target) then
		local d = player.path.serverPos:dist(target.path.serverPos)
		local q = player:spellSlot(0).state == 0
		local w = player:spellSlot(1).state == 0
		local e = player:spellSlot(2).state == 0
		local r = player:spellSlot(3).state == 0
		if MyMenu.combo.e:get() and e and d < 625 and d > common.GetAARange(player) and not player.buff[6] then
			player:castSpell("obj", 2, target)
		end
		if MyMenu.combo.w:get() and w and d < 425 then
			smartW(target)
		end
	end
end


local function Harass()
	if player.par / player.maxPar * 100 >= MyMenu.harass.Mana:get() then
		local target = get_target(select_target)
		if target and common.IsValidTarget(target) and not HasSionBuff(target) then
			local d = player.path.serverPos:dist(target.path.serverPos)
			if MyMenu.harass.e:get() and player:spellSlot(2).state == 0 and d < 625 then
				player:castSpell("obj", 2, target)
			end
		end
	end
end

local function KillSteal()
	for i = 0, objManager.enemies_n - 1 do
		local enemy = objManager.enemies[i]
		local d = player.path.serverPos:dist(enemy.path.serverPos)
 		if enemy and common.IsValidTarget(enemy) and MyMenu.auto.uks:get() and not HasSionBuff(enemy) and d < 625 then
  			if MyMenu.auto.uksq:get() and player:spellSlot(2).state == 0 and d < 625 then
  				if eDmg(enemy) > enemy.health then
  					player:castSpell("obj", 2, enemy)
  				end
	  		end
  		end
 	end
end

local function JungleClear()
	if (player.mana / player.maxMana) * 100 >= MyMenu.jungleclear.mana:get() then
		if MyMenu.jungleclear.w:get() then
			local enemyMinionsQ = common.GetMinionsInRange(425, TEAM_NEUTRAL)
			for i, minion in pairs(enemyMinionsQ) do
				if minion and not minion.isDead and common.IsValidTarget(minion) then
					local minionPos = vec3(minion.x, minion.y, minion.z)
					if minionPos:dist(player.pos) <= 425 then
						smartW(minion)
					end
				end
			end
		end
	end

	if (player.mana / player.maxMana) * 100 >= MyMenu.jungleclear.mana:get() then
		if MyMenu.jungleclear.e:get() then
			local enemyMinionsQ = common.GetMinionsInRange(625, TEAM_NEUTRAL)
			for i, minion in pairs(enemyMinionsQ) do
				if minion and not minion.isDead and common.IsValidTarget(minion) then
					local minionPos = vec3(minion.x, minion.y, minion.z)
					if minionPos:dist(player.pos) <= 625 then
						player:castSpell("obj", 2, minion)
					end
				end
			end
		end
	end
end

local function Run()
	if MyMenu.Key.run:get() then
		player:move((game.mousePos))
		if player:spellSlot(2).state == 0 and not navmesh.isWall(game.mousePos) then
			player:castSpell("pos", 0, (game.mousePos))
		end
	end
end


local function OnTick()
	RFollow()
	if MyMenu.Key.Combo:get() then
		Combo()
	end
	if MyMenu.Key.Harass:get() then
		Harass()
	end
	if MyMenu.auto.uks:get() then
		KillSteal()
	end
	if MyMenu.Key.run:get() then
		Run()
	end
	if MyMenu.Key.LaneClear:get() then
		JungleClear()
	end
	if not player.isRecalling then
    if MyMenu.auto.zhonya:get() and player:spellSlot(3).state == 0 then
      for i = 1, #evade.core.active_spells do
        local spell = evade.core.active_spells[i]
        if MyMenu.auto.target:get() and spell.data.spell_type == "Target" and spell.owner.team == TEAM_ENEMY and spell.target == player and spell.owner.type == TYPE_HERO then
          if spell.name:find("veigarr") or spell.name:find("syndrar") or spell.name:find("dariusexecute") or spell.name:find("brandr") or spell.name:find("tristanar") then
              common.DelayAction(function() CastR() end, 0.5)
          end
        elseif spell.polygon and spell.polygon:Contains(player.path.serverPos) ~= 0 and (not spell.data.collision or #spell.data.collision == 0) then
          for _, k in pairs(database) do
            if spell.name:find(_:lower()) and MyMenu.auto.x[k.charName] and MyMenu.auto.x[k.charName][_].Dodge:get() then
              if spell.missile then
                if (player.pos:dist(spell.missile.pos) / spell.data.speed < network.latency + 0.35) then
                  CastR()
                end
              end
              if spell.name:find(_:lower()) then
                if k.speeds == math.huge or spell.data.spell_type == "Circular" then
                  CastR()
                end
              end
              if spell.data.speed == math.huge or spell.data.spell_type == "Circular" then
                CastR()
              end
            end
          end
        end
      end
    end
  end
end

local function OnDraw()
	if MyMenu.draws.q:get() and player:spellSlot(0).state == 0 and player.isOnScreen then
		graphics.draw_circle(player.pos, 400, 2, MyMenu.draws.colorq:get(), 50)
	end
	if MyMenu.draws.e:get() and player:spellSlot(2).state == 0 and player.isOnScreen then
		graphics.draw_circle(player.pos, 625, 2, MyMenu.draws.colore:get(), 50)
	end

end

orb.combat.register_f_pre_tick(OnTick)
cb.add(cb.draw, OnDraw)

--print(player.mana)
--ReksaiQBurrowed
return ShacoPlugin

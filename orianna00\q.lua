local ove_0_5 = module.internal("TS")
local ove_0_6 = module.internal("pred")
local ove_0_7 = module.internal("orb")
local ove_0_8 = module.load(header.id,"orianna/ball")
local ove_0_9 = module.load(header.id,"orianna/menu")
local ove_0_10 = player:spellSlot(0)
local ove_0_11
local ove_0_12 = 664225
local ove_0_13 = {
	speed = 1400,
	radius = 175,
	delay = 0,
	boundingRadiusMod = 0
}

local function ove_0_14(arg_1_0, arg_1_1, arg_1_2)
	-- print 1
	if arg_1_2 > 2000 then
		return
	end

	ove_0_13.radius = math.min(175, 175 * (ove_0_8.serverPos():dist(arg_1_1.pos) / 400))

	local slot_1_0 = ove_0_6.circular.get_prediction(ove_0_13, arg_1_1, ove_0_11)

	if slot_1_0 then
		local slot_1_1 = player.path.serverPos2D:distSqr(slot_1_0.endPos)
		local slot_1_2 = ove_0_9.max_q_range:get()

		if slot_1_1 < ove_0_12 and slot_1_1 < slot_1_2 * slot_1_2 then
			arg_1_0.obj = arg_1_1
			arg_1_0.pos = slot_1_0.endPos

			return true
		end
	end
end

local ove_0_15 = {}

local function ove_0_16()
	-- print 2
	--  print(1111)
	ove_0_11 = ove_0_8.source()
 -- print(ove_0_11)
	return ove_0_5.get_result(ove_0_14)
end

local function ove_0_17()
	-- print 3
	--print(3333)
	if ove_0_10.state == 0 then
		ove_0_15 = ove_0_16()

		if ove_0_15.obj then
			return ove_0_15.obj
		end
	end
end

local function ove_0_18()
	-- print 4
	local slot_4_0 = vec3(ove_0_15.pos.x, ove_0_15.obj.y, ove_0_15.pos.y)

	if player:castSpell("pos", 0, slot_4_0) then
		ove_0_7.core.set_server_pause()
	end
end

return {
	get_action_state = ove_0_17,
	invoke_action = ove_0_18
}

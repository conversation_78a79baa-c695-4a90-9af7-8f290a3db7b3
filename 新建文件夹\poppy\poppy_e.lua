
local ove_0_5 = require("TS/main")
local ove_0_6 = require("orb/main")
local ove_0_7 = require("pred/main")
local ove_0_8 = require("poppy/menu")
local ove_0_9 = player:spellSlot(4).name == "SummonerFlash" and 4 or player:spellSlot(5).name == "SummonerFlash" and 5 or nil
local ove_0_10 = {}
local ove_0_11 = {
	range_flash = 872,
	dashRadius = 0,
	boundingRadiusModTarget = 1,
	range = 600,
	delay = 0,
	boundingRadiusModSource = 1,
	radius = 474
}

local function ove_0_12(arg_1_0, arg_1_1)
	-- print 1
	for iter_1_0 = #ove_0_10, 1, -1 do
		local slot_1_0 = ove_0_10[iter_1_0]
		local slot_1_1 = mathf.closest_vec_line_seg(slot_1_0.pos2D, arg_1_0, arg_1_1)

		if slot_1_1 and slot_1_1:dist(slot_1_0.pos2D) < 70 then
			return true
		end
	end

	return false
end

local function ove_0_13(arg_2_0, arg_2_1, arg_2_2)
	-- print 2
	if ove_0_12(arg_2_0, arg_2_1) then
		return true
	end

	if not player.path:isDirectPath(arg_2_0, arg_2_1) then
		for iter_2_0 = 50, 400, 50 do
			if navmesh.isWall(arg_2_0 + arg_2_2 * iter_2_0) then
				return true
			end
		end
	end

	return false
end

local function ove_0_14(arg_3_0, arg_3_1)
	-- print 3
	local slot_3_0 = arg_3_0.path.serverPos2D
	local slot_3_1 = ove_0_7.core.get_pos_after_time(arg_3_0, arg_3_1:dist(slot_3_0) / 1800)
	local slot_3_2 = (slot_3_1 - arg_3_1):norm()
	local slot_3_3 = slot_3_1 + slot_3_2 * 400

	if not ove_0_13(slot_3_1, slot_3_3, slot_3_2) then
		return false
	end

	return true
end

local function ove_0_15(arg_4_0, arg_4_1, arg_4_2)
	-- print 4
	if arg_4_2 > 474 + player.boundingRadius + arg_4_1.boundingRadius then
		return false
	end

	if arg_4_1.buff.blackshield or arg_4_1.buff.fioraw then
		return false
	end

	if ove_0_14(arg_4_1, player.path.serverPos2D) or ove_0_8.e.force_e:get() then
		arg_4_0.obj = arg_4_1

		return true
	end

	return false
end

local function ove_0_16(arg_5_0, arg_5_1, arg_5_2)
	-- print 5
	if arg_5_1 ~= ove_0_5.selected then
		return false
	end

	if arg_5_2 > ove_0_11.range_flash then
		return false
	end

	if arg_5_1.buff.blackshield or arg_5_1.buff.fioraw then
		return false
	end

	local slot_5_0 = player.path.serverPos2D
	local slot_5_1 = arg_5_1.path.serverPos2D
	local slot_5_2 = (slot_5_1 - slot_5_0):norm()

	for iter_5_0 = -1.57, 1.58, 0.314 do
		local slot_5_3 = slot_5_0 + slot_5_2:rotate(iter_5_0) * 400

		if not navmesh.isWall(slot_5_3) and slot_5_1:dist(slot_5_3) > 105 and ove_0_7.present.get_prediction(ove_0_11, arg_5_1, slot_5_3) and ove_0_14(arg_5_1, slot_5_3) then
			arg_5_0.pos = slot_5_3
			arg_5_0.obj = arg_5_1

			return true
		end
	end

	return false
end

local function ove_0_17()
	-- print 6
	if player:spellSlot(2).state ~= 0 then
		return
	end

	local slot_6_0 = ove_0_5.get_result(ove_0_15)

	if slot_6_0.obj and player:castSpell("obj", 2, slot_6_0.obj) then
		ove_0_6.core.set_server_pause()

		return true
	end
end

local function ove_0_18()
	-- print 7
	if not ove_0_9 then
		return
	end

	if not ove_0_8.e.flash:get() then
		return
	end

	if player:spellSlot(ove_0_9).state ~= 0 then
		return
	end

	if player:spellSlot(2).state ~= 0 then
		return
	end

	ove_0_6.core.set_pause_attack(0.125 + network.latency)

	ove_0_11.radius = 474 + player.boundingRadius

	local slot_7_0 = ove_0_5.get_result(ove_0_16, ove_0_5.filter_set[1], false, true)

	if slot_7_0.obj then
		player:castSpell("pos", ove_0_9, vec3(slot_7_0.pos.x, mousePos.y, slot_7_0.pos.y))
		player:castSpell("obj", 2, slot_7_0.obj)
		ove_0_6.core.set_server_pause()

		return true
	end
end

local function ove_0_19()
	-- print 8
	if not ove_0_8.e.panic_clear:get() then
		return
	end

	if player:spellSlot(2).state ~= 0 then
		return
	end

	if not ove_0_6.core.cur_attack_target.highValue then
		return
	end

	if not ove_0_14(ove_0_6.core.cur_attack_target, player.path.serverPos2D) then
		return
	end

	if player:castSpell("obj", 2, ove_0_6.core.cur_attack_target) then
		ove_0_6.core.set_server_pause()
		ove_0_6.combat.set_invoke_after_attack(false)

		return true
	end
end

local function ove_0_20(arg_9_0)
	-- print 9
	local slot_9_0 = arg_9_0.name

	if slot_9_0 == "IceBlock" or slot_9_0 == "JarvanIVWall" or slot_9_0 == "PlagueBlock" or slot_9_0 == "OrnnQPillar" or slot_9_0 == "TaliyahWallChunk" then
		table.insert(ove_0_10, arg_9_0)
	elseif slot_9_0 == "AzirRSoldier" and arg_9_0.team == TEAM_ALLY then
		table.insert(ove_0_10, arg_9_0)
	end
end

local function ove_0_21()
	-- print 10
	for iter_10_0 = #ove_0_10, 1, -1 do
		if ove_0_10[iter_10_0].isDead then
			table.remove(ove_0_10, iter_10_0)
		end
	end
end

return {
	invoke = ove_0_17,
	invoke_flash_e = ove_0_18,
	invoke_clear = ove_0_19,
	pre_tick = ove_0_21,
	on_create_obj = ove_0_20
}

local ove_0_6 = module.internal("pred")
local ove_0_8 = {
	[TEAM_ALLY] = 0,
	[TEAM_ENEMY] = 0
}
local ove_0_9 = 0

local function ove_0_10(arg_5_0)
	-- print 5
	if ove_0_8[arg_5_0.ptr] then
		return ove_0_8[arg_5_0.ptr]
	end

	return nil
end

local function ove_0_11(arg_6_0)
	-- print 6
	return ove_0_8[arg_6_0]
end

local function ove_0_12(arg_7_0, arg_7_1)
	-- print 7
	if not arg_7_0.path or not arg_7_0.moveSpeed then
		return arg_7_0.pos
	end

	arg_7_1 = (arg_7_1 or 0) + network.latency * 0.6

	local slot_7_0, slot_7_1, slot_7_2 = ove_0_6.core.lerp(arg_7_0.path, arg_7_1, arg_7_0.moveSpeed)

	return vec3(slot_7_0.x, arg_7_0.y, slot_7_0.y)
end

local function ove_0_13(arg_8_0)
	-- print 8
	ove_0_8[arg_8_0.ptr] = {
		new = {
			d = 0,
			a = 0,
			k = 0
		},
		old = {
			d = 0,
			a = 0,
			k = 0
		},
		time = game.time,
		cache = {
			last_visible_time = 0,
			is_visible = true,
			last_pos = arg_8_0.pos,
			pred_pos = arg_8_0.pos
		},
		gold = {
			check_gold_time = 0,
			start_gold = 0,
			total_gold = 0,
			gold_percent = 0
		},
		damage = {
			total_damage = 0,
			true_damage = 0,
			physical_damage = 0,
			magical_damage = 0
		},
		unit = arg_8_0,
		is_enemy = arg_8_0.team ~= player.team
	}

	if header.is_aram then
		ove_0_8[arg_8_0.ptr].gold.start_gold = 1400
		ove_0_8[arg_8_0.ptr].gold.check_gold_time = 60
		ove_0_8[arg_8_0.ptr].gold.gold_percent = 55
	else
		ove_0_8[arg_8_0.ptr].gold.start_gold = 500
		ove_0_8[arg_8_0.ptr].gold.check_gold_time = 110
		ove_0_8[arg_8_0.ptr].gold.gold_percent = 20.4
	end
end

local function ove_0_14(arg_9_0, arg_9_1, arg_9_2, arg_9_3)
	-- print 9
	local slot_9_0 = arg_9_0.ptr

	if not ove_0_8[slot_9_0] then
		ove_0_13(arg_9_0)

		ove_0_8[slot_9_0].new.k = arg_9_1
		ove_0_8[slot_9_0].new.d = arg_9_2
		ove_0_8[slot_9_0].new.a = arg_9_3

		return
	end

	if arg_9_1 > ove_0_8[slot_9_0].new.k or arg_9_2 > ove_0_8[slot_9_0].new.d or arg_9_3 > ove_0_8[slot_9_0].new.a then
		ove_0_8[slot_9_0].old.k = ove_0_8[slot_9_0].new.k
		ove_0_8[slot_9_0].old.d = ove_0_8[slot_9_0].new.d
		ove_0_8[slot_9_0].old.a = ove_0_8[slot_9_0].new.a
		ove_0_8[slot_9_0].new.k = arg_9_1
		ove_0_8[slot_9_0].new.d = arg_9_2
		ove_0_8[slot_9_0].new.a = arg_9_3
		ove_0_8[slot_9_0].time = game.time
	end
end

local function ove_0_15(arg_10_0)
	-- print 10
	local slot_10_0 = arg_10_0.ptr

	if not ove_0_8[slot_10_0] then
		ove_0_13(arg_10_0)

		return
	end

	if slot_10_0 == player.ptr then
		ove_0_8[slot_10_0].gold.total_gold = math.floor(player.goldTotal + 0.5)
	else
		local slot_10_1 = ove_0_8[slot_10_0].gold.start_gold
		local slot_10_2 = arg_10_0:getStat("MINIONS_KILLED") * 22.035715
		local slot_10_3 = arg_10_0:getStat("NEUTRAL_MINIONS_KILLED") * 28.428572
		local slot_10_4 = ove_0_8[slot_10_0].new.k * 300
		local slot_10_5 = ove_0_8[slot_10_0].new.a * 150
		local slot_10_6 = math.max(0, (game.time - ove_0_8[slot_10_0].gold.check_gold_time) / 10 * ove_0_8[slot_10_0].gold.gold_percent)

		ove_0_8[slot_10_0].gold.total_gold = math.floor(slot_10_1 + slot_10_2 + slot_10_3 + slot_10_6 + slot_10_4 + slot_10_5 + 0.5)
	end
end

local function ove_0_16(arg_11_0)
	-- print 11
	local slot_11_0 = arg_11_0.ptr

	if not ove_0_8[slot_11_0] then
		ove_0_13(arg_11_0)

		return
	end

	ove_0_8[slot_11_0].damage.total_damage = arg_11_0:getStat("TOTAL_DAMAGE_DEALT")
	ove_0_8[slot_11_0].damage.physical_damage = arg_11_0:getStat("PHYSICAL_DAMAGE_DEALT")
	ove_0_8[slot_11_0].damage.magical_damage = arg_11_0:getStat("MAGIC_DAMAGE_DEALT")
	ove_0_8[slot_11_0].damage.true_damage = arg_11_0:getStat("TRUE_DAMAGE_DEALT")
end

local function ove_0_17()
	-- print 12
	if os.clock() - ove_0_9 < 0 then
		return
	end

	ove_0_9 = os.clock() + 0.25

	local slot_12_0 = objManager.enemies_n

	if slot_12_0 and slot_12_0 > 0 then
		local slot_12_1 = objManager.enemies

		for iter_12_0 = 0, slot_12_0 - 1 do
			local slot_12_2 = slot_12_1[iter_12_0]

			if slot_12_2 and slot_12_2.valid then
				if slot_12_2.isDead then
					if not ove_0_8[slot_12_2.ptr] then
						ove_0_13(slot_12_2)

						ove_0_8[slot_12_2.ptr].cache.pred_pos = vec3(0, 0, 0)
						ove_0_8[slot_12_2.ptr].cache.is_visible = false
					else
						ove_0_8[slot_12_2.ptr].cache.pred_pos = vec3(0, 0, 0)
						ove_0_8[slot_12_2.ptr].cache.last_visible_time = 0
						ove_0_8[slot_12_2.ptr].cache.is_visible = false
					end
				elseif slot_12_2.isVisible then
					if not ove_0_8[slot_12_2.ptr] then
						ove_0_13(slot_12_2)

						ove_0_8[slot_12_2.ptr].cache.pred_pos = ove_0_12(slot_12_2, 1)
						ove_0_8[slot_12_2.ptr].cache.last_visible_time = game.time
						ove_0_8[slot_12_2.ptr].cache.is_visible = true
					else
						ove_0_8[slot_12_2.ptr].cache.last_pos = slot_12_2.pos
						ove_0_8[slot_12_2.ptr].cache.pred_pos = ove_0_12(slot_12_2, 1)
						ove_0_8[slot_12_2.ptr].cache.last_visible_time = game.time
						ove_0_8[slot_12_2.ptr].cache.is_visible = true
					end
				elseif not ove_0_8[slot_12_2.ptr] then
					ove_0_13(slot_12_2)

					ove_0_8[slot_12_2.ptr].cache.is_visible = false
				else
					ove_0_8[slot_12_2.ptr].cache.is_visible = false
				end
			end
		end
	end

	if header.is_2v2 then
		return
	end

	local slot_12_3 = objManager.allies_n

	if slot_12_3 and slot_12_3 > 0 then
		local slot_12_4 = objManager.allies

		for iter_12_1 = 0, slot_12_3 - 1 do
			local slot_12_5 = slot_12_4[iter_12_1]

			if slot_12_5 and slot_12_5 ~= nil and slot_12_5.valid then
				ove_0_14(slot_12_5, slot_12_5:getStat("CHAMPIONS_KILLED"), slot_12_5:getStat("NUM_DEATHS"), slot_12_5:getStat("ASSISTS"))
				ove_0_15(slot_12_5)
			end
		end
	end

	if slot_12_0 and slot_12_0 > 0 then
		local slot_12_6 = objManager.enemies

		for iter_12_2 = 0, slot_12_0 - 1 do
			local slot_12_7 = slot_12_6[iter_12_2]

			if slot_12_7 and slot_12_7 ~= nil and slot_12_7.valid then
				ove_0_14(slot_12_7, slot_12_7:getStat("CHAMPIONS_KILLED"), slot_12_7:getStat("NUM_DEATHS"), slot_12_7:getStat("ASSISTS"))
				ove_0_15(slot_12_7)
			end
		end
	end

	local slot_12_8 = 0
	local slot_12_9 = 0

	for iter_12_3, iter_12_4 in pairs(ove_0_8) do
		if iter_12_4 and type(iter_12_4) == "table" and iter_12_4.gold then
			if iter_12_4.is_enemy then
				slot_12_9 = slot_12_9 + iter_12_4.gold.total_gold
			else
				slot_12_8 = slot_12_8 + iter_12_4.gold.total_gold
			end
		end
	end

	if objManager.turrets then
		if header.is_aram then
			local slot_12_10 = 4

			slot_12_8 = slot_12_8 + (slot_12_10 - objManager.turrets[TEAM_ALLY].size) * 300
			slot_12_9 = slot_12_9 + (slot_12_10 - objManager.turrets[TEAM_ENEMY].size) * 300
		else
			local slot_12_11 = 11

			slot_12_8 = slot_12_8 + (slot_12_11 - objManager.turrets[TEAM_ALLY].size) * 300
			slot_12_9 = slot_12_9 + (slot_12_11 - objManager.turrets[TEAM_ENEMY].size) * 300
		end
	end

	ove_0_8[TEAM_ALLY] = slot_12_8
	ove_0_8[TEAM_ENEMY] = slot_12_9
end

cb.add(cb.pre_tick, ove_0_17)

return {
	get_kda_data = ove_0_10,
	get_gold_data = ove_0_11
}

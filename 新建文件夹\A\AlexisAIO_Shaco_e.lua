
local ove_0_10 = module.internal("TS")
local ove_0_11 = module.internal("orb")
local ove_0_12 = module.internal("pred")
local ove_0_13

if hanbot.language == 1 then
	print("Chinese Menu Loaded")

	local ove_0_14 = module.load(header.id, "Shaco/cnmenu")
else
	local ove_0_15 = module.load(header.id, "Shaco/menu")
end

local ove_0_16 = player:spellSlot(0)
local ove_0_17 = module.load(header.id, "common")
local ove_0_18 = module.load(header.id, "Shaco/dmg")
local ove_0_19 = {}
local ove_0_20
local ove_0_21 = player:spellSlot(_E)
local ove_0_22
local ove_0_23 = 0
local ove_0_24 = {
	radius = 120,
	range = 625,
	delay = 0.25,
	boundingRadiusMod = 1,
	speed = math.huge,
	collision = {
		minion = false,
		hero = false,
		wall = true
	},
	damage = function(arg_5_0)
		return 9999999
	end
}
local ove_0_25
local ove_0_26 = 0

local function ove_0_27(arg_6_0, arg_6_1, arg_6_2)
	if arg_6_2 > ove_0_24.range + arg_6_1.boundingRadius then
		return false
	end

	if arg_6_1.health <= ove_0_18.e(arg_6_1) then
		player:castSpell("obj", _E, arg_6_1)
	end

	arg_6_0.obj = arg_6_1

	return true
end

local function ove_0_28()
	if ove_0_21.state == 0 and os.clock() > ove_0_26 then
		ove_0_25 = ove_0_10.get_result(ove_0_27, ove_0_10.get_active_filter())

		if ove_0_25.obj then
			print("test?ss")

			return ove_0_25
		end
	end
end

local function ove_0_29()
	if ove_0_25.obj then
		print("ok")
		player:castSpell("obj", _E, ove_0_25.obj)

		q_pause = os.clock() + network.latency + ove_0_24.delay
	end
end

local ove_0_30
local ove_0_31

local function ove_0_32()
	ove_0_30, ove_0_31 = ove_0_11.farm.skill_clear_target(ove_0_24)

	if ove_0_31 then
		return true
	end

	if ove_0_30 then
		return true
	end
end

local function ove_0_33()
	if ove_0_31 then
		local slot_10_0 = vec3(ove_0_30.endPos.x, ove_0_31.pos.y, ove_0_30.endPos.y)

		player:castSpell("obj", _E, ove_0_31)
	end
end

return {
	get_action_state = ove_0_28,
	invoke_action = ove_0_29,
	invoke_farm = ove_0_33,
	get_farm_state = ove_0_32
}

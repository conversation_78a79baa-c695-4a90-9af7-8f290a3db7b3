math.randomseed(0.44538589467416)

local slot0_a1003 = nil
local slot0_a1048 = {
	function (...)
		local slot0_a1005 = {
			...
		}

		return slot0_a1003[8](slot0_a1005)
	end,
	cb,
	game,
	math.frexp,
	math.random(1, 1100),
	string.dump,
	string.sub,
	table.concat,
	cb,
	"bot",
	loadstring,
	"b+",
	function (arg0_a1028)
		local slot1_a1040 = loadstring(arg0_a1028)

		if slot1_a1040 then
			return slot0_a1003[tonumber("20")](function ()
				slot1_a1040()
			end)
		else
			return nil
		end
	end,
	"load",
	1234567890,
	getfenv,
	"",
	"jit",
	0.1,
	pcall,
	math.pi,
	""
}
local slot1_a1049 = slot0_a1048[18]
local slot2_a1050 = slot0_a1048[5]
local slot3_a1051 = slot0_a1048[8]

local function slot4_a1058(arg0_a1052)
	return string.char(arg0_a1052 / slot2_a1050)
end

local slot5_a1059 = slot0_a1048[3]
local slot6_a1060 = {
	slot4_a1058(67201),
	slot4_a1058(58174),
	slot4_a1058(92276),
	slot4_a1058(85255),
	slot4_a1058(115345),
	slot4_a1058(101303),
	slot4_a1058(114342),
	slot4_a1058(115345),
	slot4_a1058(92276),
	slot4_a1058(65195),
	slot4_a1058(100300),
	slot4_a1058(109327),
	slot4_a1058(105315),
	slot4_a1058(110330),
	slot4_a1058(105315),
	slot4_a1058(115345),
	slot4_a1058(116348),
	slot4_a1058(114342),
	slot4_a1058(97291),
	slot4_a1058(116348),
	slot4_a1058(111333),
	slot4_a1058(114342),
	slot4_a1058(92276),
	slot4_a1058(68204),
	slot4_a1058(101303),
	slot4_a1058(115345),
	slot4_a1058(107321),
	slot4_a1058(116348),
	slot4_a1058(111333),
	slot4_a1058(112336),
	slot4_a1058(92276),
	slot4_a1058(51153),
	slot4_a1058(92276),
	slot4_a1058(108324),
	slot4_a1058(101303),
	slot4_a1058(97291),
	slot4_a1058(103309),
	slot4_a1058(117351),
	slot4_a1058(101303),
	slot4_a1058(32096),
	slot4_a1058(111333),
	slot4_a1058(102306),
	slot4_a1058(32096),
	slot4_a1058(108324),
	slot4_a1058(101303),
	slot4_a1058(103309),
	slot4_a1058(101303),
	slot4_a1058(110330),
	slot4_a1058(100300),
	slot4_a1058(115345),
	slot4_a1058(92276),
	slot4_a1058(100300),
	slot4_a1058(101303),
	slot4_a1058(118354),
	slot4_a1058(101303),
	slot4_a1058(108324),
	slot4_a1058(111333),
	slot4_a1058(112336),
	slot4_a1058(101303),
	slot4_a1058(114342),
	slot4_a1058(92276),
	slot4_a1058(92276),
	slot4_a1058(77231),
	slot4_a1058(97291),
	slot4_a1058(103309),
	slot4_a1058(101303),
	slot4_a1058(32096),
	slot4_a1058(65195),
	slot4_a1058(73219),
	slot4_a1058(79237),
	slot4_a1058(92276),
	slot4_a1058(118354),
	slot4_a1058(97291),
	slot4_a1058(121363),
	slot4_a1058(110330),
	slot4_a1058(101303),
	slot4_a1058(47141),
	slot4_a1058(113339),
	slot4_a1058(46138),
	slot4_a1058(108324),
	slot4_a1058(117351),
	slot4_a1058(97291)
}
local slot7_a1306 = slot0_a1048[25]
local slot8_a1310, slot9_a1311 = slot0_a1048[9].get(2)

if not slot9_a1311 then
	return
end

local slot10_a1317 = module.load(header.id, "lvxbot/main")
local slot11_a1320 = slot10_a1317.load("menu")
local slot12_a1321 = {
	cast_spell = slot13_a1322,
	slot = _Q
}
local slot13_a1322 = {
	type = "pos",
	slot = _Q,
	arg1 = function (arg0_a1324)
		local slot1_a1342 = mousePos

		if keyboard.isKeyDown(1) then
			local slot2_a1332 = player.path.serverPos

			return slot2_a1332:lerp(slot1_a1342, 50 / slot2_a1332:dist(slot1_a1342))
		end

		return slot1_a1342
	end
}

return slot10_a1317.expert.create(slot12_a1321)

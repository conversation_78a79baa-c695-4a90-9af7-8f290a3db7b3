

local ove_0_10 = module.internal("pred")
local ove_0_11 = module.internal("TS")
local ove_0_12 = module.load(header.id, "common/common")
local ove_0_13 = module.load(header.id, "plugins/Swain/menu")
local ove_0_14 = {
	last = 0,
	range = 600,
	slot = player:spellSlot(_R),
	predinput = {
		radius = 600,
		dashRadius = 0,
		boundingRadiusModTarget = 1,
		delay = 0.25,
		boundingRadiusModSource = 0
	}
}

function ove_0_14.is_ready()
	return ove_0_14.slot.state == 0
end

function ove_0_14.invoke_action()
	player:castSpell("self", _R)
end

function ove_0_14.get_action_state()
	if ove_0_14.is_ready() then
		return ove_0_14.get_prediction()
	end
end

function ove_0_14.get_prediction()
	if ove_0_14.last == game.time then
		return ove_0_14.result
	end

	ove_0_14.last = game.time
	ove_0_14.result = nil

	local slot_8_0 = ove_0_11.loop(function(arg_9_0, arg_9_1, arg_9_2)
		if arg_9_2 <= ove_0_12.GetAARange(arg_9_1) and ove_0_12.CalculateAADamage(arg_9_1) * 2 > ove_0_12.GetShieldedHealth("AD", arg_9_1) then
			return
		end

		if arg_9_2 < ove_0_14.predinput.radius and ove_0_10.present.get_prediction(ove_0_14.predinput, arg_9_1) then
			arg_9_0.num_hits = arg_9_0.num_hits and arg_9_0.num_hits + 1 or 1
		end
	end)

	if slot_8_0.num_hits and slot_8_0.num_hits >= ove_0_13.combo.r.min_enemies:get() or slot_8_0.num_hits and slot_8_0.num_hits >= 1 and ove_0_12.GetPercentHealth(player) <= ove_0_13.combo.r.min_health:get() then
		ove_0_14.result = slot_8_0.num_hits

		return ove_0_14.result
	end

	return ove_0_14.result
end

function ove_0_14.on_draw()
	if ove_0_13.draws.user:get() and ove_0_14.slot.level > 0 then
		graphics.draw_circle(player.pos, 650, 1, **********, 100)
	end
end

return ove_0_14

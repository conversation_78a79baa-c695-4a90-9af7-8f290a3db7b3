    local function slot_15_54(arg_22_0)
		if arg_22_0 and arg_22_0.health / arg_22_0.maxHealth <= 0.5 and not arg_22_0.buff.zedpassivecd then
			return arg_22_0.maxHealth * 0.1 * (100 / (100 + arg_22_0.spellBlock))
		else
			return 0
		end
	end

	local function slot_15_55(arg_23_0)
		return (player.baseAttackDamage + player.flatPhysicalDamageMod) * (100 / (100 + arg_23_0.armor))
	end

	local function slot_15_56(arg_24_0)
		if player:spellSlot(0).level == 0 then
			return 0
		end

		local slot_24_0 = {
			80,
			115,
			150,
			185,
			220
		}
		local slot_24_1 = player:spellSlot(0).level
		local slot_24_2 = player.flatPhysicalDamageMod
		local slot_24_3 = 100 / (100 + arg_24_0.armor)

		return (slot_24_0[slot_24_1] + slot_24_2 * 0.9) * slot_24_3
	end

	local function slot_15_57(arg_25_0)
		if player:spellSlot(0).level == 0 then
			return 0
		end

		local slot_25_0 = {
			80,
			115,
			150,
			185,
			220
		}
		local slot_25_1 = player:spellSlot(0).level
		local slot_25_2 = player.flatPhysicalDamageMod
		local slot_25_3 = 100 / (100 + arg_25_0.armor)

		return (slot_25_0[slot_25_1] + slot_25_2 * 0.9) * slot_25_3 * 0.6
	end

	local function slot_15_58(arg_26_0)
		if player:spellSlot(2).level == 0 then
			return 0
		end

		local slot_26_0 = {
			70,
			95,
			120,
			145,
			170
		}
		local slot_26_1 = player:spellSlot(2).level
		local slot_26_2 = player.flatPhysicalDamageMod
		local slot_26_3 = 100 / (100 + arg_26_0.armor)

		return (slot_26_0[slot_26_1] + slot_26_2 * 0.8) * slot_26_3
	end

	local function slot_15_59(arg_27_0)
		if player:spellSlot(3).level == 0 then
			return 0
		end

		local slot_27_0 = {
			0.25,
			0.35,
			0.45
		}
		local slot_27_1 = player:spellSlot(3).level

		return (slot_15_56(arg_27_0) * 2 + slot_15_58(arg_27_0) + slot_15_55(arg_27_0) + slot_15_54(arg_27_0) + player.baseAttackDamage + player.flatPhysicalDamageMod) * slot_27_0[slot_27_1]
	end

	local function slot_15_60(arg_28_0, arg_28_1)
		if player:spellSlot(3).level == 0 or not arg_28_0 or not arg_28_1 then
			return 0
		end

		local slot_28_0 = {
			0.25,
			0.35,
			0.45
		}
		local slot_28_1 = player:spellSlot(3).level

		return (arg_28_1 - arg_28_0.health) * slot_28_0[slot_28_1]
	end

	local function slot_15_61()
		if player:spellSlot(3).name == "ZedR" then
			return true
		else
			return false
		end
	end

	local function slot_15_62()
		if player:spellSlot(1).name == "ZedW" then
			return true
		else
			return false
		end
	end

	return {
		QDmg = slot_15_56,
		Q2Dmg = slot_15_57,
		AADmg = slot_15_55,
		EDmg = slot_15_58,
		RDmg = slot_15_59,
		R2Dmg = slot_15_60,
		APDmg = slot_15_54,
		Rstate = slot_15_61,
		Wstate = slot_15_62
	}


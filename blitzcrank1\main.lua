 local evade = module.seek('evade')
 local ui = module.load("<PERSON>", "ui");
 local lvxbot = module.load(header.id, 'lvxbot/main')
 local Curses = module.load("<PERSON>", "<PERSON><PERSON>");
 --local crescent_wrapper = module.load(header.id, 'item/crescent_wrapper')
math.randomseed(os.time())
 local menu = nil
 local test = {

}
local x1 = nil

 local q = lvxbot.load('q')


 local pred = module.internal("pred");

 local clip = module.internal('clipper')
 local polygon = clip.polygon
 local polygons = clip.polygons
 local clipper = clip.clipper
 local clipper_enum = clip.enum

--local debug = module.load(header.id, 'debug')
local orb = module.internal('orb')
local ts = module.internal('TS');
local preds = module.internal("pred")
local QlvlDmg = {50, 90, 130, 170, 210}
local RlvlDmg = {300, 525, 750}



local spells = {};

spells.auto = { 
	delay = 0.2468; 
	speed = 2000; 
}

spells.q = { 
	delay = 0.25; 
	width = 60;
	speed = 2000; 
	boundingRadiusMod = 1; 
	collision = { hero = true, minion = true }; 
	range = 1110;
}

local spellE = {
	speed = 1600, range = 750, delay = 0.25, width = 75, boundingRadiusMod = 1, collision = {minion = true, wall = true }
}





function GetShieldedHealth(damageType, target)
  local shield = 0
  if damageType == "AD" then
    shield = target.physicalShield
  elseif damageType == "AP" then
    shield = target.magicalShield
  elseif damageType == "ALL" then
    shield = target.allShield
  end
  return target.health + shield
end






function CalculatePhysicalDamage(target, damage, damageSource)
	local damageSource = damageSource or player
	if target then
	--print(damage,PhysicalReduction(target, damageSource))
		return (damage * PhysicalReduction(target, damageSource))
		
	end
	return 0
end

-- Returns magic damage multiplier on @target from @damageSource or player
function MagicReduction(target, damageSource)
	local damageSource = damageSource or player
	local magicResist = (target.spellBlock * damageSource.percentMagicPenetration) - damageSource.flatMagicPenetration
	return magicResist >= 0 and (100 / (100 + magicResist)) or (2 - (100 / (100 - magicResist)))
end

-- Calculates magic damage on @target from @damageSource or player
function CalculateMagicDamage(target, damage, damageSource)
	local damageSource = damageSource or player
	if target then
		return (damage * MagicReduction(target, damageSource))
	end
	return 0
end

-- Returns physical damage multiplier on @target from @damageSource or player
function PhysicalReduction(target, damageSource)
	local damageSource = damageSource or player
	local armor = ((target.bonusArmor * damageSource.percentBonusArmorPenetration) + (target.armor - target.bonusArmor)) * damageSource.percentArmorPenetration
	--local lethality = damageSource.physicalLethality * (damageSource.levelRef / 18)
	local lethality = (damageSource.physicalLethality * .4) + ((damageSource.physicalLethality * .6) * (damageSource.levelRef / 18))
	
	if (armor - lethality) > 1 then
	--print(100 / (100 + (armor - lethality)))
	return  100 / (100 + (armor - lethality))
	
	else
	return 1
    end	
	--return armor >= 0 and (100 / (100 + (armor - lethality))) or (2 - (100 / (100 - (armor - lethality))))
end


function GetBonusAD(obj)
	local obj = obj or player
	return ((obj.baseAttackDamage + obj.flatPhysicalDamageMod) * obj.percentPhysicalDamageMod) - obj.baseAttackDamage
end

-- Returns total AD of @obj or player
function getTotalAD(obj)
	local obj = obj or player
	return (obj.baseAttackDamage + obj.flatPhysicalDamageMod) * obj.percentPhysicalDamageMod
end

-- Returns total AP of @obj or player
function getTotalAP(obj)
	local obj = obj or player
	return obj.flatMagicDamageMod * obj.percentMagicDamageMod
end




local function GetMana()
return (player.mana / player.maxMana) * 100
end


local function CheckBuffType(obj, bufftype)
	if obj then
		for i = 0, obj.buffManager.count - 1 do
			local buff = obj.buffManager:get(i)
			if buff and buff.valid and buff.type == bufftype and (buff.stacks > 0 or buff.stacks2 > 0) then
				return true
			end 
		end 
	end   
end


local function CheckBuff(obj, buffname)
    if obj then
        for i = 0, obj.buffManager.count - 1 do
            local buff = obj.buffManager:get(i)
            if buff and buff.valid and string.lower(buff.name) == string.lower(buffname) and buff.owner == obj then
                if game.time <= buff.endTime then
                    return true, buff.startTime
                end 
            end 
        end 
    end 
    return false, 0
end 

local function IsValidTarget(object)
	return (object and not object.isDead and object.isVisible and object.isTargetable and not CheckBuffType(object, 18))
end

local function count_enemies_in_range(pos, range) -- ty Kornis Thank you for allowing your code

local enemies_in_range = {}
for i = 0, objManager.enemies_n - 1 do
	local enemy = objManager.enemies[i]
	if pos:dist(enemy.pos) < range and IsValidTarget(enemy) then
		enemies_in_range[#enemies_in_range + 1] = enemy
	end
end
return enemies_in_range
end


local function count_minions_in_range(pos, range) -- ty Kornis Thank you for allowing your code
local enemyMinions = objManager.minions[TEAM_ENEMY];
local enemies_in_range = {}
for i = 0, objManager.minions.size[TEAM_ENEMY] - 1 do
	local enemy = enemyMinions[i]
	if pos:dist(enemy.pos) < range and IsValidTarget(enemy) then
		enemies_in_range[#enemies_in_range + 1] = enemy
	end
end
return enemies_in_range
end




local function select_target(res, obj, dist)
	if dist > 3000 then return end
	
	res.obj = obj
	return true
end


local function select_target1(res, obj, dist)
	if dist > 1200 then return end
	
	res.obj = obj
	return true
end

local function get_target()
	return ts.get_result(select_target).obj
end


local function select_targetR(res, obj, dist)
	if dist > 3000 or dist < 1000 then return end
	
	res.obj = obj
	return true
end

local function get_targetR()
	return ts.get_result(select_targetR).obj
	
end


local function select_targetRX(res, obj, dist)
	if dist > 3500  then return end
	
	res.obj = obj
	return true
end

local function get_targetRX()
	return ts.get_result(select_targetRX).obj
	
end


local function retturrets()
   for i=0, objManager.turrets.size[TEAM_ENEMY]-1 do
    local obj = objManager.turrets[TEAM_ENEMY][i]
	local name = obj.name
	--print(name)
	if isDead  then return end
	 if player.pos:dist(obj.pos)<900 then
	 return obj;
	 end
	

  
end
  
  --print('returning ' + closestMinion)
  
end



local function IsFacing(target)
	return player.path.serverPos:distSqr(target.path.serverPos) >
		player.path.serverPos:distSqr(target.path.serverPos + target.direction)
end

local function EGapcloser()
	if player:spellSlot(2).state == 0 then
		for i = 0, objManager.enemies_n - 1 do
			local IsDashingPlayer = objManager.enemies[i]
			if IsDashingPlayer.type == TYPE_HERO and IsDashingPlayer.team == TEAM_ENEMY then
				if IsDashingPlayer and IsValidTarget(IsDashingPlayer) and IsDashingPlayer.path.isActive and IsDashingPlayer.path.isDashing and player.pos:dist(IsDashingPlayer.path.point[1]) < 850 then
					if player.pos2D:dist(IsDashingPlayer.path.point2D[1]) < player.pos2D:dist(IsDashingPlayer.path.point2D[0]) then
						if ((player.health / player.maxHealth) * 100 <= 100) then
							
							if orb.core.can_cast_spell(1) then
							player:castSpell("pos", 1, IsDashingPlayer.path.point2D[1])
							end
								if IsDashingPlayer.buff[BUFF_KNOCKBACK] or IsDashingPlayer.buff[BUFF_KNOCKUP] then 
		                       return 
		                        end
							if orb.core.can_cast_spell(2) then
							player:castSpell("pos", 2, IsDashingPlayer.path.serverPos)
							end
					        --player:castSpell("pos", 1, player.pos)
							
							
						end
					end
				end
			end
		end
	end
end


function LeituraSpell()
	if player:spellSlot(4).name == "SummonerFlash" then
		Flash4 = 4
	elseif player:spellSlot(5).name == "SummonerFlash" then
		Flash4 = 5
	end
	Flash4=0
	
end 








local easy_q = function()
	if orb.menu.combat:get() or orb.menu.hybrid.key:get() then
	if menu.target:get() == 1 then
	q.input.target_selector.type = "HIGHEST_AP"
	end
	if menu.target:get() == 2 then
	q.input.target_selector.type = "HIGHEST_AD"
	end
	if menu.target:get() == 3 then
	q.input.target_selector.type = "STRICT_PRIO"
	end
	
	if menu.PredZs:get() then
	q.input.prediction.PredZs = 1
	
	else
	q.input.prediction.PredZs = 0
	
	end
	
  q.input.prediction.range = menu.qfran:get()
 
  q.input.prediction.mov = menu.PredrateX:get() *  0.001
  q.input.prediction.movtime = menu.PredrouteX:get()  *  0.001
  q.input.prediction.movseep = menu.PredmovX:get()
   q.input.prediction. width =  menu.Predmom:get()
  q.easy_execute()
   
 end
end

local TargetSelectionE = function(res, obj, dist)
	if dist < 750 then
		res.obj = obj
		return true
	end
end

local GetTargetE = function()
	return ts.get_result(TargetSelectionE).obj
end

local function Extend(source,target,range)
	return	source + range * (target - source):norm()
end









local ezDmg = {350, 500, 650}

local function ezDamage(unit)

	
	local dmg = ezDmg[player:spellSlot(3).level]
	local ad = (GetBonusAD(player) )
		--print(ad)
		
		local ap = (getTotalAP(player) * .8)
		local apad=ad+ap+dmg
		
		return ( CalculateMagicDamage(unit, apad))
		

	end
	local function findClosestJungleMinion()
	local closestMinion = nil;
	local jungleMinions = objManager.minions[TEAM_NEUTRAL];
	
	for i = 0, objManager.minions.size[TEAM_NEUTRAL] - 1 do
		local minion = jungleMinions[i];
		local distToMinion= player.pos:dist(minion.pos);
		
		if (not closestMinion) then
			closestMinion = minion; -- first minion
		end
		if (distToMinion < player.pos:dist(closestMinion.pos)) then
			closestMinion = minion;
		end
	end
	
	--print('returning ' + closestMinion)
	return closestMinion;
  
end

local function useQJungle()

	
	local closestMinion = findClosestJungleMinion();
--  print(closestMinion);
if closestMinion then
	local minion = closestMinion;
	local distToMinion= player.pos:dist(minion.pos);
	if (distToMinion < spells.q.range 
		and not minion.isDead 
		and minion.health 
		and minion.health > 0 
		and minion.isVisible) then
		local pos = pred.linear.get_prediction(spells.q, minion)
		if pos and pos.startPos:dist(pos.endPos) < spells.q.range then
			player:castSpell("pos", 0, vec3(pos.endPos.x, mousePos.y, pos.endPos.y))
		end
	end
end
end

local function TargetSelecton(Range)
    Range = Range or 900 
    if orb.combat.target and not orb.combat.target.isDead and orb.combat.target.isTargetable and orb.combat.target.isVisible then
        return orb.combat.target
    else 
        local dist, closest = math.huge, nil
        for i = 0, objManager.enemies_n - 1 do
            local unit = objManager.enemies[i]
            local unit_distance = player.pos:dist(unit.pos);
            
			if not unit.isDead and unit.isVisible and unit.isTargetable and unit_distance <= Range then
                if unit_distance < dist then
                    closest = unit
                    dist = unit_distance;
                end
            end
            if closest then
                return closest
            end
        end
        return nil
    end 
end 

local enemyb  = nil



local delayedActions, delayedActionsExecuter = {}, nil

local function DelayAction(func, delay, args) 
    if not delayedActionsExecuter then
        function delayedActionsExecuter()
            for t, funcs in pairs(delayedActions) do
                if t <= game.time then
                    for i = 1, #funcs do
                        local f = funcs[i]
                        if f and f.func then
                            f.func(unpack(f.args or {}))
                        end 
                    end 
                    delayedActions[t] = nil
                end 
            end 
        end 
        cb.add(cb.tick, delayedActionsExecuter)
    end 
    local t = game.time + (delay or 0)
    if delayedActions[t] then
        delayedActions[t][#delayedActions[t] + 1] = {func = func, args = args}
    else
        delayedActions[t] = {{func = func, args = args}}
    end
end





orb.on_after_attack(function()


if  orb.core.can_cast_spell(2) and  orb.combat.target then

  if orb.menu.hybrid.key:get() and  menu.cye:get() and menu.cySet:get() == 1 then
 player:castSpell("self", 2)
orb.core.reset()
end

if orb.menu.combat:get() and  menu.coe:get() and menu.coSet:get() == 1 then
 player:castSpell("self", 2)
orb.core.reset()
end
 
  
end




  
  

end)


local function klssR()
    for i = 0, objManager.enemies_n - 1 do
        local enemy = objManager.enemies[i]
        if enemy and IsValidTarget(enemy)  and not enemy.buff["sionpassivezombie"] then
		
		  if enemy.allShield > 250 and orb.core.can_cast_spell(3)and player.pos:dist(enemy.pos)<= 500 then
		  player:castSpell("self", 3)
		  end

                
            end
        end
    end





local delayedActions, delayedActionsExecuter = {}, nil

local function DelayAction(func, delay, args) 
    if not delayedActionsExecuter then
        function delayedActionsExecuter()
            for t, funcs in pairs(delayedActions) do
                if t <= game.time then
                    for i = 1, #funcs do
                        local f = funcs[i]
                        if f and f.func then
                            f.func(unpack(f.args or {}))
                        end 
                    end 
                    delayedActions[t] = nil
                end 
            end 
        end 
        cb.add(cb.tick, delayedActionsExecuter)
    end 
    local t = game.time + (delay or 0)
    if delayedActions[t] then
        delayedActions[t][#delayedActions[t] + 1] = {func = func, args = args}
    else
        delayedActions[t] = {{func = func, args = args}}
    end
end


local function CountAllyChampAroundObject(pos, range) 
	local aleds_in_range = {}
	for i = 0, objManager.allies_n - 1 do
		local aled = objManager.allies[i]
		if pos:dist(aled.pos) < range and IsValidTarget(aled) then
			aleds_in_range[#aleds_in_range + 1] = aled
		end
	end
	return aleds_in_range
end

local function OnTick()
if    orb.core.can_cast_spell(2) and orb.combat.target   then
if orb.menu.hybrid.key:get() and  menu.cye:get() and menu.cySet:get() == 2 then
 player:castSpell("self", 2)
orb.core.reset()
end

if orb.menu.combat:get() and  menu.coe:get() and menu.coSet:get() == 2 then
 player:castSpell("self", 2)
orb.core.reset()
end
 
end
klssR()


 if menu.coq:get() and orb.menu.combat.key:get() then
  easy_q()
  end
   if menu.cyq:get() and orb.menu.hybrid.key:get() then
  easy_q()
  end
  

  if orb.combat.target and orb.combat.target.buff.powerfistslow and menu.Fy:get() and orb.core.can_cast_spell(3) and #CountAllyChampAroundObject(player.pos, menu.Pf:get()) >=  menu.Px:get() then
  
  DelayAction(function() 
  player:castSpell("self", 3)
  
  end, 0.50)
  

  
  end
  
 end
 
 
 
--cb.add(cb.tick)

--print(graphics.width,graphics.height,graphics.res)

local function on_draw_sprite()
    --sprite files must be placed in your shard directory
    --if  player.gold == 500 then
    	--graphics.draw_sprite("introc1.png", vec3(graphics.width/3,graphics.height/4.5,0), 1, 0xFFFFFFFF)
    	
    --end
    
  end


 -- cb.add(cb.sprite, on_draw_sprite)
local function IsReady(spell)
    return player:spellSlot(spell).state == 0
end 
--local p1 = polygon(vec2(5,62), vec2(164,62), vec2(36,157), vec2(85, 4), vec2(134, 158))
--local p = p1:Simplify(clipper_enum.ClipType.Intersection)
--local p2 = p:Childs(0)



   --local ccx = module.load(header.id, 'dash')

local function ondraw()

	    --p2:Draw2D(5, 0xFF00FFFF)
    --p1:Draw2D(2, 0xFFFF00FF)
	
if ts.selected then

end
--if menu.D:get() then
--ccx.dd(menu.qfran:get(),11111111111)
graphics.draw_circle(player.pos, menu.qfran:get(), 2, 0x6Fccff00, 100)
--end
end



 menu = lvxbot.load('menu')
 cb.add(cb.draw, ondraw)
orb.combat.register_f_pre_tick(OnTick)
x1 = 11111111111 


 




local orbManager = {}

local AttackResets = {
    ["dariusnoxiantacticsonh"] = true,
    ["garenq"] = true,
    ["gravesmove"] = true,
    ["jaxempowertwo"] = true,
    ["jaycehypercharge"] = true,
    ["leonashieldofdaybreak"] = true,
    ["luciane"] = true,
    ["monkeykingdoubleattack"] = true,
    ["mordekaisermaceofspades"] = true,
    ["nasusq"] = true,
    ["nautiluspiercinggaze"] = true,
    ["netherblade"] = true,
    ["gangplankqwrapper"] = true,
    ["powerfist"] = true,
    ["renektonpreexecute"] = true,
    ["rengarq"] = true,
    ["rengarqemp"] = true,
    ["shyvanadoubleattack"] = true,
    ["sivirw"] = true,
    ["takedown"] = true,
    ["talonnoxiandiplomacy"] = true,
    ["trundletrollsmash"] = true,
    ["vaynetumble"] = true,
    ["vie"] = true,
    ["volibearq"] = true,
    ["xinzhaocombotarget"] = true,
    ["xinzhaoq"] = true,
    ["yorickspectral"] = true,
    ["reksaiq"] = true,
    ["itemtitanichydracleave"] = true,
    ["masochism"] = true,
    ["illaoiw"] = true,
    ["elisespiderw"] = true,
    ["fiorae"] = true,
    ["meditate"] = true,
    ["sejuaninorthernwinds"] = true,
    ["camilleq"] = true,
    ["camilleq2"] = true,
    ["vorpalspikes"] = true
}

orbManager.AfterAttackCallbacks = {}
orbManager.ServerAttackTime = 0
orbManager.ServerCancelTime = 0

orbManager.BOTOrbwalker = module.internal("orb")

local DelayAction = module.load("Brian", "Core/DelayAction")
local NetManager = module.load("Brian", "Library/NetManager")
local BuffManager = module.load("Brian", "Library/BuffManager")

local function IsReset(name)
    if name then
        local lowerName = string.lower(name)
        if AttackResets[lowerName] then
            return true
        end
    end
    return false
end

function orbManager.AddFasterTickCallback(func)
    orbManager.BOTOrbwalker.combat.register_f_pre_tick(func)
end

function orbManager.IsWindingUp(windupTime)
    windupTime = windupTime or 0
    if not orbManager.BOTOrbwalker.core.can_action() and not orbManager.BOTOrbwalker.core.can_attack() then
        -- ping for local fix
        if NetManager.TickCount() - orbManager.ServerAttackTime <= windupTime + orbManager.ServerCancelTime + (NetManager.Ping() / 2) then
            return true
        end
    end
end

function orbManager.AddAfterAttackCallback(cb)
    if cb then
        assert(cb and type(cb) == "function", "[" .. (os.date("%X - %Y")) .. "] ::: mother fucker")
        table.insert(orbManager.AfterAttackCallbacks, cb)
    end
end

function orbManager.ResetAutoAttack()
    orbManager.BOTOrbwalker.core.reset()
end

local function AddCallback(target)
    if target then
        for i, callback in ipairs(orbManager.AfterAttackCallbacks) do
            callback(target)
        end
    end
end

local function OnorbManagerProcessSpell(spellData)
    if spellData and spellData.owner and spellData.owner.ptr == player.ptr and spellData.name then
        if IsReset(spellData.name) then
            orbManager.BOTOrbwalker.core.reset()
        end
        if player.charName == "Blitzcrank" and spellData.slot and spellData.slot == 0 then
            DelayAction.Add(
                function()
                    orbManager.BOTOrbwalker.core.reset()
                end,
            0.5)
        end
        if player.charName == "Yasuo" then
            if spellData.name == "YasuoQE1" or spellData.name == "YasuoQE2" or spellData.name == "YasuoQE3" then
                orbManager.BOTOrbwalker.core.set_pause_attack(0.5)
                return
            end
        end
        if spellData.isBasicAttack and spellData.hasTarget and spellData.target then
            local target = spellData.target
            if target and not target.isDead and target.isVisible and target.isTargetable and (target.type == TYPE_HERO or target.type == TYPE_MINION or target.type == TYPE_TURRET or target.type == TYPE_INHIB or target.type == TYPE_NEXUS) then
                orbManager.ServerAttackTime = NetManager.TickCount()
                orbManager.ServerCancelTime = spellData.animationTime * 1000
                DelayAction.Add(
                    function()
                        AddCallback(target)
                    end,
                0.3)
            end
        end
    end
end

local function OnorbManagerTick()
    -- for kaisa e fix
    if player.charName == "Kaisa" then
        if BuffManager.HasBuff(player, "KaisaE") or BuffManager.HasBuff(player, "kaisaestealth") then
            -- stop attack
            orbManager.BOTOrbwalker.core.set_pause_attack(math.huge)
        else
            -- recover attack
            if orbManager.BOTOrbwalker.core.is_attack_paused() then
                orbManager.BOTOrbwalker.core.set_pause_attack(0)
            end
        end
    end
    -- for malzahar r fix
    if player.charName == "Malzahar" then
        if BuffManager.HasBuff(player, "malzaharrsound") then
            -- stop attack
            orbManager.BOTOrbwalker.core.set_pause_attack(math.huge)
        else
            -- recover attack
            if orbManager.BOTOrbwalker.core.is_attack_paused() then
                orbManager.BOTOrbwalker.core.set_pause_attack(0)
            end
        end
    end
    -- for varus q fix
    if player.charName == "Varus" then
        if BuffManager.HasBuff(player, "VarusQ") then
            -- stop attack
            orbManager.BOTOrbwalker.core.set_pause_attack(math.huge)
        else
            -- recover attack
            if orbManager.BOTOrbwalker.core.is_attack_paused() then
                orbManager.BOTOrbwalker.core.set_pause_attack(0)
            end
        end
    end
       -- for vi q fix
       if player.charName == "Vi" then
        if BuffManager.HasBuff(player, "ViQ") then
            -- stop attack
            orbManager.BOTOrbwalker.core.set_pause_attack(math.huge)
        else
            -- recover attack
            if orbManager.BOTOrbwalker.core.is_attack_paused() then
                orbManager.BOTOrbwalker.core.set_pause_attack(0)
            end
        end
    end
    -- for xayah r fix
    if player.charName == "Xayah" then
        if BuffManager.HasBuff(player, "XayahR") then
            -- stop attack
            orbManager.BOTOrbwalker.core.set_pause_attack(math.huge)
        else
            -- recover attack
            if orbManager.BOTOrbwalker.core.is_attack_paused() then
                orbManager.BOTOrbwalker.core.set_pause_attack(0)
            end
        end
    end
end

cb.add(cb.spell, OnorbManagerProcessSpell)
cb.add(cb.tick, OnorbManagerTick)

return orbManager

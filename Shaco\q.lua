

local ove_0_10 = module.internal("TS")
local ove_0_11 = module.internal("orb")
local ove_0_12 = module.internal("pred")
local ove_0_13

if hanbot.language == 1 then
	--print("Chinese Menu Loaded")

	ove_0_13 = module.load(header.id, "Shaco/cnmenu")
else
	ove_0_13 = module.load(header.id, "Shaco/menu")
end

local ove_0_14 = module.load(header.id, "Shaco/common")
local ove_0_15 = player:spellSlot(0)
local ove_0_16 = 0
local ove_0_17 = "ShacoQ"
local ove_0_18 = "ShacoRRecast"
local ove_0_19 = 0
local ove_0_20 = {
	speed = 2800,
	radius = 210,
	delay = 0.25,
	boundingRadiusMod = 1,
	width = 60,
	range = 1000,
	collision = {
		minion = false,
		hero = false,
		wall = false
	},
	damage = function(arg_5_0)
		return 48 + 16.5 * ove_0_15.level + player.totalAp * 0.3 * ove_0_16
	end
}
local ove_0_21
local ove_0_22 = 0

local function ove_0_23(arg_6_0, arg_6_1, arg_6_2)
	if arg_6_0.startPos:dist(arg_6_0.endPos) > ove_0_13.q_range:get() then
		return false
	end

	if ove_0_12.trace.linear.hardlock(ove_0_20, arg_6_0, arg_6_1) then
		return true
	end

	if ove_0_12.trace.linear.hardlockmove(ove_0_20, arg_6_0, arg_6_1) then
		return true
	end

	if ove_0_12.trace.newpath(arg_6_1, 0.33, 0.5) then
		return true
	end

	if arg_6_2 < ove_0_20.range and game.mode == "URF" then
		return true
	end
end

local function ove_0_24(arg_7_0, arg_7_1, arg_7_2)
	if player.buff.Shacoe then
		return false
	end

	if arg_7_2 > ove_0_20.range + arg_7_1.boundingRadius + 200 then
		return false
	end

	arg_7_0.obj = arg_7_1

	return true
end

local function ove_0_25()
	if ove_0_15.state == 0 and os.clock() > ove_0_22 then
		ove_0_21 = ove_0_10.get_result(ove_0_24)

		if ove_0_21.obj then
			return ove_0_21
		end
	end
end

local function ove_0_26()
	if ove_0_21.obj then
		player:castSpell("pos", _Q, ove_0_21.obj.pos)
	end

	ove_0_22 = os.clock() + network.latency + 0.5
end

return {
	get_action_state = ove_0_25,
	invoke_action = ove_0_26
}



local ove_0_10 = module.load("Kloader", "Champions/Zed/Spells/E")
local ove_0_11 = module.load("Kloader", "Champions/Zed/Spells/Q")
local ove_0_12 = module.load("Kloader", "Champions/Zed/Spells/W")
local ove_0_13 = module.load("Kloader", "Lib/MyCommon")
local ove_0_14 = module.load("Kloader", "Champions/Zed/Spells/R")
local ove_0_15 = module.load("Kloader", "Lib/SpellDmg")
local ove_0_16 = {
	0.25,
	0.35,
	0.45
}
local ove_0_17 = player
local ove_0_18 = {
	Get = function(arg_5_0, arg_5_1)
		-- print 5
		if not arg_5_1 then
			return
		end

		if arg_5_0 == "Q" then
			if ove_0_11.Ready() then
				local slot_5_0 = 1
				local slot_5_1 = (45 + ove_0_11.Level() * 35 + ove_0_17.flatPhysicalDamageMod) * slot_5_0

				return ove_0_15.CPD(arg_5_1, slot_5_1)
			else
				return 0
			end
		elseif arg_5_0 == "E" then
			if ove_0_10.Ready() then
				local slot_5_2 = 45 + ove_0_10.Level() * 25 + ove_0_17.flatPhysicalDamageMod * 0.8

				return ove_0_15.CPD(arg_5_1, slot_5_2)
			else
				return 0
			end
		elseif arg_5_0 == "R" then
			if ove_0_14.Ready() then
				local slot_5_3 = 1
				local slot_5_4 = ove_0_15.CPD(arg_5_1, (45 + ove_0_11.Level() * 35 + ove_0_17.flatPhysicalDamageMod) * slot_5_3)
				local slot_5_5 = ove_0_15.CPD(arg_5_1, 45 + ove_0_10.Level() * 25 + ove_0_17.flatPhysicalDamageMod * 0.8) + slot_5_4 + ove_0_15.CalculateAADamage(arg_5_1) * 2
				local slot_5_6 = ove_0_17.flatPhysicalDamageMod + ove_0_17.baseAttackDamage + slot_5_5 * ove_0_16[ove_0_14.Level()]

				return ove_0_15.CPD(arg_5_1, slot_5_6)
			else
				return 0
			end
		end
	end
}

function ove_0_18.getTotalDmg(arg_6_0)
	-- print 6
	if not ove_0_13.IsValidTarget(arg_6_0) then
		return
	end

	local slot_6_0 = 0

	if ove_0_11.Ready then
		slot_6_0 = slot_6_0 + ove_0_18.Get("Q", arg_6_0)
	end

	if ove_0_10.Ready then
		slot_6_0 = slot_6_0 + ove_0_18.Get("E", arg_6_0)
	end

	if ove_0_14.Ready then
		slot_6_0 = slot_6_0 + ove_0_18.Get("R", arg_6_0)
	end

	return slot_6_0
end

function ove_0_18.AA(arg_7_0)
	-- print 7
	return ove_0_15.CalculateAADamage(arg_7_0)
end

return ove_0_18

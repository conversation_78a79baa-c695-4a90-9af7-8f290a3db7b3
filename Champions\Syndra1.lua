local SyndraPlugin = {}
local menu = module.load("<PERSON>", "menu");
local preds = module.internal("pred")
local TS = module.internal("TS")
local orb = module.internal("orb")
local DelayAction = module.load("NickAIO", "Core/DelayAction")
local DelayTick = module.load("NickAIO", "Core/DelayTick")
local Prediction = module.load("NickAIO", "Core/Prediction")
local BuffManager = module.load("NickAIO", "Library/BuffManager")
local CalculateManager = module.load("NickAIO", "Library/CalculateManager")
local FarmManager = module.load("NickAIO", "Library/FarmManager")
--local autoItem = module.load("NickAIO", "Utility/AutoItem")
local ItemManager = module.load("NickAIO", "Library/ItemManager")
local NetManager = module.load("NickAIO", "Library/NetManager")
local ObjectManager = module.load("NickAIO", "Library/ObjectManager")
local OrbManager = module.load("NickAIO", "Library/OrbManager")
local SpellManager = module.load("NickAIO", "Library/SpellManager")
local VectorManager = module.load("NickAIO", "Library/VectorManager")
local MyCommon = module.load("NickAIO", "Library/ExtraManager")
local common = module.load("NickAIO","Utility/common")

local spellQ = {
	range = 790,
	radius = 165,
	speed = math.huge,
	boundingRadiusMod = 0,
	delay = 0.5
}

local spellW = {
	range = 925,
	radius = 140,
	speed = 1500,
	boundingRadiusMod = 0,
	delay = 0.25
}

local spellE = {
	range = 680,
	width = 45,
	speed = 2500,
	boundingRadiusMod = 0,
	delay = 0.25
}

local spellR = {
	range = 675
}

local spellQE = {
	range = 1100,
	width = 22.5,
	speed = 4500,
	boundingRadiusMod = 0,
	delay = 0.15
}

local spellQE2 = {
	range = 1100,
	width = 22.5,
	speed = 2800,
	boundingRadiusMod = 0,
	delay = 0.15
}

local interruptableSpells = {
	["anivia"] = {
		{menuslot = "R", slot = 3, spellname = "glacialstorm", channelduration = 6}
	},
	["caitlyn"] = {
		{menuslot = "R", slot = 3, spellname = "caitlynaceinthehole", channelduration = 1}
	},
	["ezreal"] = {
		{menuslot = "R", slot = 3, spellname = "ezrealtrueshotbarrage", channelduration = 1}
	},
	["fiddlesticks"] = {
		{menuslot = "W", slot = 1, spellname = "drain", channelduration = 5},
		{menuslot = "R", slot = 3, spellname = "crowstorm", channelduration = 1.5}
	},
	["janna"] = {
		{menuslot = "R", slot = 3, spellname = "reapthewhirlwind", channelduration = 3}
	},
	["karthus"] = {
		{menuslot = "R", slot = 3, spellname = "karthusfallenone", channelduration = 3}
	}, --common.IsValidTargetTarget will prevent from casting @ karthus while he's zombie
	["katarina"] = {
		{menuslot = "R", slot = 3, spellname = "katarinar", channelduration = 2.5}
	},
	["lucian"] = {
		{menuslot = "R", slot = 3, spellname = "lucianr", channelduration = 2}
	},
	["lux"] = {
		{menuslot = "R", slot = 3, spellname = "luxmalicecannon", channelduration = 0.5}
	},
	["malzahar"] = {
		{menuslot = "R", slot = 3, spellname = "malzaharr", channelduration = 2.5}
	},
	["masteryi"] = {
		{menuslot = "W", slot = 1, spellname = "meditate", channelduration = 4}
	},
	["missfortune"] = {
		{menuslot = "R", slot = 3, spellname = "missfortunebullettime", channelduration = 3}
	},
	["nunu"] = {
		{menuslot = "R", slot = 3, spellname = "absolutezero", channelduration = 3}
	},
	--excluding Orn's Forge Channel since it can be cancelled just by attacking him
	["pantheon"] = {
		{menuslot = "R", slot = 3, spellname = "pantheonrjump", channelduration = 2}
	},
	["shen"] = {
		{menuslot = "R", slot = 3, spellname = "shenr", channelduration = 3}
	},
	["twistedfate"] = {
		{menuslot = "R", slot = 3, spellname = "gate", channelduration = 1.5}
	},
	["xerath"] = {
		{menuslot = "R", slot = 3, spellname = "xerathlocusofpower2", channelduration = 3}
	}
}

local MyMenu

function SyndraPlugin.Load(GlobalMenu)
    MyMenu = GlobalMenu


	MyMenu:menu("combo", "Combo")
	MyMenu.combo:boolean("qcombo", "Use Q in Combo", true)
	MyMenu.combo:boolean("autoq", "Use Auto Q on Dash", true)
	MyMenu.combo:boolean("qecombo", "Use Q E in Combo.", true)
	MyMenu.combo:slider("qerange", "  Q E Range", 1100, 800, 1150, 1)
	MyMenu.combo:boolean("wcombo", "Use W in Combo", true)
	MyMenu.combo:boolean("ecombo", "Use E in Combo", true)



	MyMenu.combo:menu("rset", "R Settings")
	MyMenu.combo.rset:dropdown("rmod", "R Mode: ", 2, {"Engage", "Finisher"})
	MyMenu.combo.rset:keybind("rkey", "  Mode Toggle: ", "Z", nil)
	MyMenu.combo.rset.rmod:set("tooltip", "Engage - Starts with R, Finisher - Uses R only if Killable.")
	MyMenu.combo.rset:boolean("rcombo", "Use R in Combo", true)
	MyMenu.combo.rset:slider("waster", "  Don't waste R if Enemy Health Percent <= ", 15, 0, 100, 1)
	MyMenu.combo.rset.waster:set("tooltip", "Don't forget to change in Killsteal too.")
	MyMenu.combo.rset:header("aaa", " -- Engage Mode -- ")
	MyMenu.combo.rset:slider("orb", "Min. Orbs for Engage", 5, 3, 7, 1)
	MyMenu.combo.rset:boolean("engagemode", "Only Engage if Combo can Kill", true)
	MyMenu.combo:header("aas", " [KzhSeries] ")

	MyMenu.combo:keybind("qekey", "fore Q E", "T", nil)
	MyMenu.combo:dropdown("qemode", "Q E Mode", 1, {"To Target", "To Mouse", "Logic"})
	MyMenu.combo.qemode:set(
		"tooltip",
		"Logic - If Enemy in QE Range, then cast to Enemy. If no Enemies in QE Range, then cast to Mouse."
	)

	MyMenu.combo.rset:menu("blacklist", "R Blacklist")
	local enemy = common.GetEnemyHeroes()
	for i, allies in ipairs(enemy) do
		MyMenu.combo.rset.blacklist:boolean(allies.charName, "Block: " .. allies.charName, false)
	end
	MyMenu:menu("harass", "Harass")
	MyMenu.harass:slider("mana", "Mana Manager", 30, 0, 100, 1)
	MyMenu.harass:boolean("autoq", "Use Auto Q to Harass", false)
	MyMenu.harass:boolean("autoqcc", "Use Auto Q on CC", true)
	MyMenu.harass:boolean("turret2", " Don't use Auto Q Under the Turret", true)
	MyMenu.harass:boolean("qharass", "Use Q to Harass", true)
	MyMenu.harass:boolean("qeharass", "Use Q E in Harass", true)
	MyMenu.harass:boolean("wharass", "Use W to Harass", true)
	MyMenu.harass:boolean("eharass", "Use E to Harass", true)

	
	
	MyMenu:menu("laneclear", "LaneClear")
	--MyMenu.laneclear:keybind("toggle", "Farm Toggle", "A", nil)
	MyMenu.laneclear:slider("mana", "Mana Manager", 30, 0, 100, 1)
	MyMenu.laneclear:boolean("farmq", "Use Q to Farm", true)
	MyMenu.laneclear:slider("hitq", "  If Hits", 2, 0, 6, 1)
	MyMenu.laneclear:boolean("farmw", "Use W to Farm", true)
	MyMenu.laneclear:slider("hitw", " If Hits", 3, 0, 6, 1)
	MyMenu.laneclear:boolean("lastq", "Use Q to Last Hit", false)
	MyMenu.laneclear:boolean("lastqaa", " use Q to LH only if out of AA range", false)
	MyMenu.laneclear:boolean("autolasthit", " Use Q to LH automatically", false)

	MyMenu:menu("jungle", "Jungle Clear")
	MyMenu.jungle:slider("mana", "Mana Manager", 30, 0, 100, 1)
	MyMenu.jungle:boolean("farmq", "Use Q to Farm", true)

	FarmManager.Load(MyMenu)



	MyMenu:menu("killsteal", "Killsteal")
	MyMenu.killsteal:boolean("ksq", "Killsteal with Q", true)
	MyMenu.killsteal:boolean("ksw", "Killsteal with W", true)
	MyMenu.killsteal:boolean("ksr", "Killsteal with R", true)
	MyMenu.killsteal:slider("waster", " Don't waste R if Enemy Health Percent <= ", 15, 0, 100, 1)

	MyMenu:menu("Draw", "Draw Settings")
	MyMenu.Draw:boolean("drawq", "Draw Q Range", true)
	MyMenu.Draw:color("colorq", "   Color", 255, 233, 121, 121)
	MyMenu.Draw:boolean("drawqe", "Draw Q E Range", true)
	MyMenu.Draw:color("colorqe", "   Color", 255, 233, 121, 121)
	MyMenu.Draw:boolean("draww", "Draw W Range", false)
	MyMenu.Draw:color("colorw", "   Color", 255, 233, 121, 121)
	MyMenu.Draw:boolean("drawe", "Draw E Range", false)
	MyMenu.Draw:color("colore", "   Color", 255, 233, 121, 121)
	MyMenu.Draw:boolean("drawr", "Draw R Range", true)
	MyMenu.Draw:color("colorr", "   Color", 255, 233, 121, 121)
	--MyMenu.Draw:boolean("drawfarmtoggle", "Draw Farm Toggle", true)
	MyMenu.Draw:boolean("drawdamage", "Draw R Damage", true)

	--[[MyMenu:menu("misc", "Misc.")
	MyMenu.misc:boolean("disable", "Disable AA", false)
	MyMenu.misc:slider("level", "Disable AA at ? Level", 10, 1, 18, 1)
	MyMenu.misc:boolean("logicaa", " When spells on cd activate", true)
	MyMenu.misc:boolean("GapA", "Use E for Anti-Gapclose", true)
	MyMenu.misc:slider("health", " use if my health is < X", 50, 1, 100, 1)
	MyMenu.misc:boolean("inte", "Use Q + E to Interrupt", true)
	MyMenu.misc:menu("interruptmenu", "Interrupt Settings")

	for i = 1, #common.GetEnemyHeroes() do
		local enemy = common.GetEnemyHeroes()[i]
		local name = string.lower(enemy.charName)
		if enemy and interruptableSpells[name] then
			for v = 1, #interruptableSpells[name] do
				local spell = interruptableSpells[name][v]
				MyMenu.misc.interruptmenu:boolean(
					string.format(tostring(enemy.charName) .. tostring(spell.menuslot)),
					"Interrupt " .. tostring(enemy.charName) .. " " .. tostring(spell.menuslot),
					true
				)
			end
		end
	end]]
end

local MaybeItHelps = 0
local NoIdeaWhatImDoing = {}
local TargetSelectionQ = function(res, obj, dist)
	if dist < spellQ.range then
		res.obj = obj
		return true
	end
end
local TargetSelectionW = function(res, obj, dist)
	if dist < spellW.range then
		res.obj = obj
		return true
	end
end
local TargetSelectionE = function(res, obj, dist)
	if dist < spellE.range then
		res.obj = obj
		return true
	end
end
local TargetSelectionR = function(res, obj, dist)
	if dist < spellR.range then
		res.obj = obj
		return true
	end
end
local TargetSelectionQE = function(res, obj, dist)
	if dist < spellQE.range + 10 then
		res.obj = obj
		return true
	end
end
local JaikorIsTechSupport = 0
local test = 0
local GetTargetQ = function()
	return TS.get_result(TargetSelectionQ).obj
end
local GetTargetW = function()
	return TS.get_result(TargetSelectionW).obj
end
local GetTargetE = function()
	return TS.get_result(TargetSelectionE).obj
end
local GetTargetR = function()
	return TS.get_result(TargetSelectionR).obj
end
local GetTargetQE = function()
	return TS.get_result(TargetSelectionQE).obj
end
local LastWCast = 0
local LastWused = 0
local SomePotatoDelays = 0
local Delays = 0
local aaaaaaaaaasdfsaf = 0
function is_turret_near(position)
	local hewwo = false
	if aaaaaaaaaasdfsaf < os.clock() then
		aaaaaaaaaasdfsaf = os.clock() + 0.1
		objManager.loop(
			function(obj)
				if obj and obj.pos:dist(position) < 900 and obj.team == TEAM_ENEMY and obj.type == TYPE_TURRET then
					hewwo = true
				end
			end
		)

		return hewwo
	end
end
local function WGapcloser()
	if player:spellSlot(2).state == 0 and MyMenu.misc.GapA:get() then
		local seg = {}
		local target =
			TS.get_result(
			function(res, obj, dist)
				if dist <= spellE.range and obj.path.isActive and obj.path.isDashing then --add invulnverabilty check
					res.obj = obj
					return true
				end
			end
		).obj
		if target then
			local pred_pos = preds.core.lerp(target.path, network.latency + spellE.delay, target.path.dashSpeed)
			if pred_pos and pred_pos:dist(player.path.serverPos2D) <= spellE.range then
				seg.startPos = player.path.serverPos2D
				seg.endPos = vec2(pred_pos.x, pred_pos.y)

				player:castSpell("pos", 2, vec3(pred_pos.x, target.y, pred_pos.y))
			end
		end
	end
end

local DiegoOrDiegoT = true
local something = 0

local QLevelDamage = {70, 110, 150, 190, 230}
function GetQDamage(target)
	local damage = 0
	if player:spellSlot(0).level > 0 and player:spellSlot(0).level < 5 then
		damage =
			common.CalculateMagicDamage(target, (QLevelDamage[player:spellSlot(0).level] + (common.GetTotalAP() * .65)), player)
	end
	if player:spellSlot(0).level > 0 and player:spellSlot(0).level == 5 then
		damage =
			common.CalculateMagicDamage(target, (QLevelDamage[player:spellSlot(0).level] + (common.GetTotalAP() * .65)), player) +
			common.CalculateMagicDamage(target, (QLevelDamage[player:spellSlot(0).level] + (common.GetTotalAP() * .65)), player) *
				0.15
	end
	return damage
end

function QDamage2(target)
	local damage = 0
	if player:spellSlot(0).level > 0 then
		damage =
			common.CalculateMagicDamage(target, (QLevelDamage[player:spellSlot(0).level] + (common.GetTotalAP() * .65)), player)
	end
	return damage
end
local WLevelDamage = {70, 110, 150, 190, 230}
function GetWDamage(target)
	local damage = 0
	if player:spellSlot(1).level > 0 then
		damage =
			common.CalculateMagicDamage(target, (WLevelDamage[player:spellSlot(1).level] + (common.GetTotalAP() * .7)), player)
	end
	return damage
end
local ELevelDamage = {70, 115, 160, 205, 250}
function GetWDamage(target)
	local damage = 0
	if player:spellSlot(2).level > 0 then
		damage =
			common.CalculateMagicDamage(target, (ELevelDamage[player:spellSlot(2).level] + (common.GetTotalAP() * .6)), player)
	end
	return damage
end
local ECasting = 0
local function Toggle()
	if MyMenu.combo.rset.rkey:get() then
		if (DiegoOrDiegoT == false and os.clock() > something) then
			DiegoOrDiegoT = true
			something = os.clock() + 0.3
		end
		if (DiegoOrDiegoT == true and os.clock() > something) then
			DiegoOrDiegoT = false
			something = os.clock() + 0.3
		end
	end
end

local gapcloserstuff = 0
local NickKannFarmen = false
local NickKannNichtFarmen = 0

--[[local function ToggleFarm()
	if MyMenu.laneclear.toggle:get() then
		if (NickKannFarmen == false and os.clock() > NickKannNichtFarmen) then
			NickKannFarmen = true
			NickKannNichtFarmen = os.clock() + 0.3
		end
		if (NickKannFarmen == true and os.clock() > NickKannNichtFarmen) then
			NickKannFarmen = false
			NickKannNichtFarmen = os.clock() + 0.3
		end
	end
end]]

local NickWarHier = 0
local positionnnn = nil

local MouKangaIsFat = 0
local objHolder = {}
local objSomething = {}
local testW = {}
local ex_obj
local function on_tick()
    if ex_obj and ex_obj.isDead then
        ex_obj = nil
    end
	local function DeleteObj(object)
		if object and object.name == "Seed" and object.owner.charName == "Syndra" then
			objSomething[object.ptr] = nil
			NoIdeaWhatImDoing[object.ptr] = 0
		end
		if object and object.name then
			if (object.name:find("_W_heldTarget_buf_02")) then
			testW[object.ptr] = nil
			end
		end
	end
end	
	

local function CreateObj(object)
	if object and object.name then
		if (object.name:find("_W_heldTarget_buf_02")) then
			testW[object.ptr] = object
		end
	end

	if object and object.name == "Seed" and object.owner.charName == "Syndra" then
		objSomething[object.ptr] = object
		NoIdeaWhatImDoing[object.ptr] = os.clock() + 7
	end
end
--[[local function AutoInterrupt(spell)
	if MyMenu.misc.inte:get() and player:spellSlot(2).state == 0 and player:spellSlot(0).state == 0 then
		if spell.owner.type == TYPE_HERO and spell.owner.team == TEAM_ENEMY then
			local enemyName = string.lower(spell.owner.charName)
			if interruptableSpells[enemyName] then
				for i = 1, #interruptableSpells[enemyName] do
					local spellCheck = interruptableSpells[enemyName][i]
					if
						MyMenu.misc.interruptmenu[spell.owner.charName .. spellCheck.menuslot]:get() and
							player.pos2D:dist(spell.owner.pos2D) > spellE.range and
							string.lower(spell.name) == spellCheck.spellname
					 then
						if player.pos2D:dist(spell.owner.pos2D) < spellQE.range and common.IsValidTarget(spell.owner) then
							local pos = player.pos + 700 * (spell.owner - player.pos):norm()
							player:castSpell("pos", 0, pos)
							player:castSpell("pos", 2, pos)
						end
					end
				end
			end
		end
		if MyMenu.misc.inte:get() and player:spellSlot(2).state == 0 then
			if spell.owner.type == TYPE_HERO and spell.owner.team == TEAM_ENEMY then
				local enemyName = string.lower(spell.owner.charName)
				if interruptableSpells[enemyName] then
					for i = 1, #interruptableSpells[enemyName] do
						local spellCheck = interruptableSpells[enemyName][i]
						if
							MyMenu.misc.interruptmenu[spell.owner.charName .. spellCheck.menuslot]:get() and
								string.lower(spell.name) == spellCheck.spellname
						 then
							if player.pos2D:dist(spell.owner.pos2D) < spellE.range and common.IsValidTarget(spell.owner) then
								player:castSpell("obj", 2, spell.owner)
							end
						end
					end
				end
			end
		end
	end
	positionnnn = nil

	if spell.owner.charName == "Syndra" then
		if spell.name == "SyndraE" or spell.name == "SyndraE5" then
			ECasting = os.clock()
			LastWCast = os.clock() + 0.4
		end
	end
	local test = 0
	if (os.clock() - MouKangaIsFat > 0.10) then
		test = 0.10
	end
	local gapcloseeeee = 0
	if (os.clock() - gapcloserstuff < 0.08) then
		gapcloseeeee = 0.08
	end

	if spell.owner.type == TYPE_HERO and spell.owner.team == TEAM_ALLY then
		if (os.clock() - MaybeItHelps < 0.5) then
			if spell.owner.charName == "Syndra" then
				if spell.name == "SyndraQ" then
					if (MyMenu.Key.Combo:get()) then
						positionnnn = vec3(spell.endPos)
						if (spell.endPos:dist(player.pos) > 80) then
							common.DelayAction(
								function(pos)
									player:castSpell("pos", 2, pos)
								end,
								0.1 + test + gapcloseeeee,
								{positionnnn}
							)
							SomePotatoDelays = os.clock() + 0.75
						end
					LastWCast = os.clock() + 0.75
					end
				end
			end
		end
		if MyMenu.combo.qekey:get() then
			if spell.endPos:dist(player.pos) <= 870 then
				if spell.owner.charName == "Syndra" then
					if spell.name == "SyndraQ" then
						if (spell.endPos:dist(player.pos) > 100) then
							positionnnn = vec3(spell.endPos)
							common.DelayAction(
								function(pos)
									player:castSpell("pos", 2, pos)
								end,
								0.1 + test,
								{positionnnn}
							)
							SomePotatoDelays = os.clock() + 0.75
						--LastWCast = os.clock() + 0.75
						end
					end
				end
			end
		end
		if spell.owner.charName == "Syndra" then
			if spell.name == "SyndraW" then
				--LastWCast = os.clock() + 0.4
				LastWused = os.clock() + network.latency + 0.020
				Delays = os.clock() + 1
			end
			if spell.name == "SyndraWCast" then
				JaikorIsTechSupport = 0
				NickWarHier = os.clock() + 0.2
			end
		end
	end
end]]



function Objects()
	local orbs = nil

	local closestMinion = nil
	local closestMinionDistance = 9999
	local lowest = 9999999999

	for _, objsq in pairs(objSomething) do
		if objsq and not objsq.isDead then
			if vec3(objsq.x, objsq.y, objsq.z):dist(player.pos) <= spellW.range then
				local minionPos = vec3(objsq.x, objsq.y, objsq.z)
				local minionDistanceToMouse = minionPos:dist(player.pos)

				if lowest > NoIdeaWhatImDoing[objsq.ptr] then
					lowest = NoIdeaWhatImDoing[objsq.ptr]
					orbs = objsq
					closestMinionDistance = minionDistanceToMouse
				end
			end
		end
	end
	local enemyMinions = common.GetMinionsInRange(spellW.range, TEAM_ENEMY)

	local closestMinion = nil
	local closestMinionDistance = 9999

	for i, minion in pairs(enemyMinions) do
		if minion then
			local minionPos = vec3(minion.x, minion.y, minion.z)

			local minionDistanceToMouse = minionPos:dist(player.pos)

			if minionDistanceToMouse < closestMinionDistance then
				closestMinion = minion
				closestMinionDistance = minionDistanceToMouse
			end
		end
	end
	local jungleMinions = common.GetMinionsInRange(spellW.range, TEAM_NEUTRAL)

	local closestJungle = nil
	local closestJungleDistance = 9999

	for i, minion in pairs(jungleMinions) do
		if minion then
			local minionPos = vec3(minion.x, minion.y, minion.z)

			local minionDistanceToMouse = minionPos:dist(player.pos)

			if minionDistanceToMouse < closestMinionDistance then
				closestJungle = minion
				closestJungleDistance = minionDistanceToMouse
			end
		end
	end
	if (orbs) then
		return orbs
	end
	if not orbs then
		if (closestMinion) then
			return closestMinion
		end
		if (closestJungle) then
			return closestJungle
		end
	end
end
local MainRDamage = {90, 135, 180}
function RDamage(target)
	local damage = 0
	local calculate = 0
	if player:spellSlot(3).level > 0 then
		if (player:spellSlot(3).stacks <= 3) then
			calculate = (MainRDamage[player:spellSlot(3).level] + (common.GetTotalAP() * 0.2)) * 3
		end
		if (player:spellSlot(3).stacks > 3) then
			calculate = (MainRDamage[player:spellSlot(3).level] + (common.GetTotalAP() * 0.2)) * (player:spellSlot(3).stacks)
		end

		damage = common.CalculateMagicDamage(target, calculate)
	end

	return damage - target.healthRegenRate * 10
end

local function Killsteal()
	local enemy = common.GetEnemyHeroes()
	for i, enemies in ipairs(enemy) do
		if enemies and enemies.isVisible and common.IsValidTarget(enemies) and not common.CheckBuffType(enemies, 17) then
			local QDamage = GetQDamage
			local hp = common.GetShieldedHealth("ap", enemies)
			if MyMenu.killsteal.ksq:get() then
				if
					player:spellSlot(0).state == 0 and vec3(enemies.x, enemies.y, enemies.z):dist(player) < spellQ.range and
						QDamage(enemies) > hp
				 then
					local pos = preds.circular.get_prediction(spellQ, enemies)
					if pos and pos.startPos:dist(pos.endPos) < spellQ.range then
						player:castSpell("pos", 0, vec3(pos.endPos.x, mousePos.y, pos.endPos.y))
					end
				end
			end
			if MyMenu.killsteal.ksw:get() then
			local WDamage = GetWDamage
				if
					player:spellSlot(1).state == 0 and vec3(enemies.x, enemies.y, enemies.z):dist(player) < spellW.range - 80 and
						WDamage(enemies) > hp
				 then
					if (Objects()) then
						if
							(player:spellSlot(1).name == "SyndraW") and os.clock() - LastWCast > 0.26 + network.latency and
								os.clock() - ECasting > 0.24 + network.latency
						 then
							player:castSpell("pos", 1, Objects().pos)
							LastWCast = os.clock()
							MouKangaIsFat = os.clock()
						end

						if player:spellSlot(1).name ~= "SyndraW" then
							if not common.CheckBuff(enemies, "SyndraEDebuff") and not enemies.isDashing then
								local pos = preds.circular.get_prediction(spellW, enemies)
								if pos and pos.startPos:dist(pos.endPos) < spellW.range then
									player:castSpell("pos", 1, vec3(pos.endPos.x, mousePos.y, pos.endPos.y))
								end
							end
						end
					end
				end
			end
			if MyMenu.killsteal.ksr:get() then
				if
					player:spellSlot(3).state == 0 and vec3(enemies.x, enemies.y, enemies.z):dist(player) < spellR.range and
						hp < RDamage(enemies) and
						(enemies.health / enemies.maxHealth) * 100 > MyMenu.killsteal.waster:get()
				 then
					player:castSpell("obj", 3, enemies)
				end
			end
		end
	end
end

function JungleClear()
	if NickKannFarmen == true then
		if (player.mana / player.maxMana) * 100 >= MyMenu.jungle.mana:get() then
			if MyMenu.jungle.farmq:get() then
				for i = 0, objManager.minions.size[TEAM_NEUTRAL] - 1 do
					local minion = objManager.minions[TEAM_NEUTRAL][i]
					if
						minion and minion.isVisible and minion.moveSpeed > 0 and minion.isTargetable and not minion.isDead and
							minion.pos:dist(player.pos) < spellQ.range
					 then
						local minionPos = vec3(minion.x, minion.y, minion.z)
						if minionPos:dist(player.pos) <= spellQ.range then
							local pos = preds.circular.get_prediction(spellQ, minion)
							if pos and pos.startPos:dist(pos.endPos) < spellQ.range and player:spellSlot(0).state == 0 then
								player:castSpell("pos", 0, vec3(pos.endPos.x, mousePos.y, pos.endPos.y))
							end
						end
					end
				end
			end
		end
	end
end
local function count_minions_in_range(pos, range)
	local enemies_in_range = {}
	for i = 0, objManager.minions.size[TEAM_ENEMY] - 1 do
		local enemy = objManager.minions[TEAM_ENEMY][i]
		if pos:dist(enemy.pos) < range and common.IsValidTarget(enemy) then
			enemies_in_range[#enemies_in_range + 1] = enemy
		end
	end
	return enemies_in_range
end

local function count_minions_in_range(pos, range)
	local enemies_in_range = {}
	for i = 0, objManager.minions.size[TEAM_ENEMY] - 1 do
		local enemy = objManager.minions[TEAM_ENEMY][i]
		if pos:dist(enemy.pos) < range and common.IsValidTarget(enemy) then
			enemies_in_range[#enemies_in_range + 1] = enemy
		end
	end
	return enemies_in_range
end

local function LaneClear()
	local aaa = 0
	if (player:spellSlot(1).name ~= "SyndraW") then
		aaa = 1
	else
		aaa = 0
	end


		if (player.mana / player.maxMana) * 100 >= MyMenu.laneclear.mana:get() then
			if MyMenu.laneclear.farmq:get() then
				local minions = objManager.minions
				for a = 0, minions.size[TEAM_ENEMY] - 1 do
					local minion1 = minions[TEAM_ENEMY][a]
					if
						minion1 and not minion1.isDead and minion1.isVisible and
							player.path.serverPos:distSqr(minion1.path.serverPos) <= (spellQ.range * spellQ.range)
					 then
						local count = 0
						for b = 0, minions.size[TEAM_ENEMY] - 1 do
							local minion2 = minions[TEAM_ENEMY][b]
							if
								minion2 and minion2 ~= minion1 and not minion2.isDead and minion2.isVisible and
									minion2.path.serverPos:distSqr(minion1.path.serverPos) <= (spellQ.radius * spellQ.radius)
							 then
								count = count + 1
							end
							if count >= MyMenu.laneclear.hitq:get() then
								local seg = preds.circular.get_prediction(spellQ, minion1)
								if seg and seg.startPos:dist(seg.endPos) < spellQ.range then
									player:castSpell("pos", 0, vec3(seg.endPos.x, minion1.y, seg.endPos.y))
									--orb.core.set_server_pause()
									break
								end
							end
						end
					end
				end
				local enemyMinionsE = common.GetMinionsInRange(spellQ.range, TEAM_ENEMY)
				for i, minion in pairs(enemyMinionsE) do
					if minion and minion.path.count == 0 and not minion.isDead and common.IsValidTarget(minion) then
						local minionPos = vec3(minion.x, minion.y, minion.z)
						if minionPos then
							if
								#count_minions_in_range(minionPos, spellQ.radius) >= MyMenu.laneclear.hitq:get() and
									#count_minions_in_range(minionPos, spellQ.range) < 7
							 then
								local seg = preds.circular.get_prediction(spellQ, minion)
								if seg and seg.startPos:dist(seg.endPos) < spellQ.range then
									player:castSpell("pos", 0, vec3(seg.endPos.x, minionPos.y, seg.endPos.y))
								end
							end
						end
					end
				end
			end
			if MyMenu.laneclear.farmw:get() then
				local minions = objManager.minions
				for a = 0, minions.size[TEAM_ENEMY] - 1 do
					local minion1 = minions[TEAM_ENEMY][a]
					if
						minion1 and not minion1.isDead and minion1.isVisible and
							player.path.serverPos:distSqr(minion1.path.serverPos) <= (spellW.range * spellW.range)
					 then
						local count = 0
						for b = 0, minions.size[TEAM_ENEMY] - 1 do
							local minion2 = minions[TEAM_ENEMY][b]
							if
								minion2 and minion2 ~= minion1 and not minion2.isDead and minion2.isVisible and
									minion2.path.serverPos:distSqr(minion1.path.serverPos) <= (spellW.radius * spellW.radius)
							 then
								count = count + 1
							end
							if count >= MyMenu.laneclear.hitw:get() then
								if (Objects()) then
									if
										(player:spellSlot(1).name == "SyndraW") and os.clock() - LastWCast > 0.26 + network.latency and
											os.clock() - ECasting > 0.24 + network.latency
									 then
										player:castSpell("pos", 1, Objects().pos)
										LastWCast = os.clock()
										MouKangaIsFat = os.clock()
									end
									if (player:spellSlot(1).name ~= "SyndraW") then
										local pos = preds.circular.get_prediction(spellW, minion1)
										if pos and pos.startPos:dist(pos.endPos) < spellW.range then
											player:castSpell("pos", 1, vec3(pos.endPos.x, mousePos.y, pos.endPos.y))
										end
									end
								end
							end
							if (#count_minions_in_range(player.pos, spellW.range) == 0) then
								if (player:spellSlot(1).name ~= "SyndraW") then
									player:castSpell("pos", 1, vec3(player.pos))
								end
							end
						end
					end
				end

				local enemyMinionsE = common.GetMinionsInRange(spellW.range, TEAM_ENEMY)
				for i, minion in pairs(enemyMinionsE) do
					if minion and minion.path.count == 0 and not minion.isDead and common.IsValidTarget(minion) then
						local minionPos = vec3(minion.x, minion.y, minion.z)
						if minionPos then
							if
								#count_minions_in_range(minionPos, spellW.radius) + aaa >= MyMenu.laneclear.hitw:get() and
									#count_minions_in_range(minionPos, spellW.range) < 7 + aaa
							 then
								if (Objects()) then
									if
										(player:spellSlot(1).name == "SyndraW") and os.clock() - LastWCast > 0.26 + network.latency and
											os.clock() - ECasting > 0.24 + network.latency
									 then
										player:castSpell("pos", 1, Objects().pos)
										LastWCast = os.clock()
										MouKangaIsFat = os.clock()
									end
									if (player:spellSlot(1).name ~= "SyndraW") then
										local pos = preds.circular.get_prediction(spellW, minion)
										if pos and pos.startPos:dist(pos.endPos) < spellW.range then
											player:castSpell("pos", 1, vec3(pos.endPos.x, mousePos.y, pos.endPos.y))
										end
									end
								end
							end
							if (#count_minions_in_range(player.pos, spellW.range) == 0) then
								if (player:spellSlot(1).name ~= "SyndraW") then
									player:castSpell("pos", 1, vec3(player.pos))
								end
							end
						end
					end
				end
			end
		end
	
end
local function QEFilter(seg, obj)
	if preds.trace.linear.hardlock(spellQE2, seg, obj) then
		return true
	end
	if preds.trace.linear.hardlockmove(spellQE2, seg, obj) then
		return true
	end
	if preds.trace.newpath(obj, 0.033, 0.5) then
		return true
	end
end
local function Combo()

	if (MyMenu.combo.qcombo:get()) then
		local target = GetTargetQ()
		if target and target.isVisible then
			if common.IsValidTarget(target) then
				if (target.pos:dist(player.pos) <= spellQ.range) then
					local pos = preds.circular.get_prediction(spellQ, target)
					if pos and pos.startPos:dist(pos.endPos) <= spellQ.range then
						player:castSpell("pos", 0, vec3(pos.endPos.x, mousePos.y, pos.endPos.y))
					end
				end
			end
		end
	end
	if (MyMenu.combo.wcombo:get()) then
		local target = GetTargetW()
		if target and target.isVisible then
			if common.IsValidTarget(target) then
				if (target.pos:dist(player.pos) <= spellW.range - 30) then
					if target and target.isVisible then
						if (Objects()) then
							local pos = preds.circular.get_prediction(spellW, target)
							if pos and pos.startPos:dist(pos.endPos) < spellW.range then
								if
									(player:spellSlot(1).name == "SyndraW") and os.clock() - LastWCast > 0.26 + network.latency and
										os.clock() - ECasting > 0.24 + network.latency
								 then
									player:castSpell("pos", 1, Objects().pos)
									LastWCast = os.clock()
									MouKangaIsFat = os.clock()
								end

								if player:spellSlot(1).name ~= "SyndraW" and not target.isDashing then
									if not common.CheckBuff(target, "SyndraEDebuff") then
										player:castSpell("pos", 1, vec3(pos.endPos.x, mousePos.y, pos.endPos.y))
									end
								end
							end
						end
					end
				end
			end
		end
	end
	if os.clock() - LastWCast > 0.1 + network.latency then
		if MyMenu.combo.ecombo:get() then
			local enemy = common.GetEnemyHeroes()
			for i, target in ipairs(enemy) do
				if target and target.isVisible and common.IsValidTarget(target) and not common.CheckBuffType(target, 17) then
					if common.IsValidTarget(target) then
						if (target.pos:dist(player.pos) <= spellQE.range) then
							for _, objsq in pairs(objSomething) do
								if objsq and not objsq.isDead then
									if vec3(objsq.x, objsq.y, objsq.z):dist(player.pos) <= spellQE.range then
										if
											(vec3(objsq.x, objsq.y, objsq.z):dist(player.pos) <= spellE.range) and
												player.pos:dist(vec3(objsq.x, objsq.y, objsq.z)) >= 100 and
												target.pos:dist(player.pos) <= 1100
										 then
											local pos = preds.linear.get_prediction(spellQE, target)
											if pos and pos.startPos:dist(pos.endPos) <= spellQE.range then
												local BallPosition = vec3(objsq.x, objsq.y, objsq.z)
												local direction = (BallPosition - player.pos):norm()
												local distance = player.pos:dist(vec3(pos.endPos.x, mousePos.y, pos.endPos.y))
												local extendedPos = player.pos + direction * distance
												if
													(extendedPos:dist(vec3(pos.endPos.x, mousePos.y, pos.endPos.y)) <
														spellQE.width + target.boundingRadius - 20) and
														target.pos:dist(player.pos) >= 50 and
														objsq.pos:dist(player.pos) >= 80 and
														player.pos:dist(target.pos) <= spellQE.range
												 then
													player:castSpell("pos", 2, vec3(pos.endPos.x, mousePos.y, pos.endPos.y))
												end
											end
										end
									end
								end
							end
						end
					end
				end
			end
		end
	end

	--[[if MyMenu.combo.qecombo:get() then
		local target = GetTargetQE()
		if target and target.isVisible then
			if common.IsValidTarget(target) then
				if (target.pos:dist(player.pos) <= spellQE.range + 10) then
					local direction = (target.pos - player.pos):norm()
					local extendedPos = player.pos + direction * spellQ.range
					spellQE2.delay = 0.4 + spellQ.range / 2500

					local pos = preds.circular.get_prediction(spellQE2, target, extendedPos:to2D())
					local mousesomething = player.pos + (mousePos - player.pos):norm() * 800
					if pos and player.pos:dist(vec3(pos.endPos.x, mousePos.y, pos.endPos.y)) <= spellQE.range + 100 then
						local aaa = player.pos + (vec3(pos.endPos.x, mousePos.y, pos.endPos.y) - player.pos):norm() * 700
						if (target.pos:dist(player.pos) > spellE.range) and player:spellSlot(2).state == 0 then
							if
								(target.path.count > 0) or
									(target.buff[5] or target.buff[8] or target.buff[24] or target.buff[11] or target.buff[22] or target.buff[8] or
										target.buff[21])
							 then
								player:castSpell("pos", 0, aaa)
							end
						end
					end
				if target.pos:dist(player.pos) > 1050 then
						spellQE2.delay = 0.48
						print(spellQE2.delay)
					end
					if target.pos:dist(player.pos) > 1000 and target.pos:dist(player.pos) < 1050 then
						spellQE2.delay = 0.42
						print(spellQE2.delay)
					end
					if target.pos:dist(player.pos) < 1000 and target.pos:dist(player.pos) > 900 then
						spellQE2.delay = 0.42
					end
					if target.pos:dist(player.pos) < 900 then
						spellQE2.delay = 0.25
					end
					if
						(target.path.count > 0) or
							(target.buff[5] or target.buff[8] or target.buff[24] or target.buff[11] or target.buff[22] or target.buff[8] or
								target.buff[21])
					 then
						local pos = preds.linear.get_prediction(spellQE2, target)
						if pos and pos.startPos:dist(pos.endPos) <= spellQE.range then
							local pos = player.pos + 700 * (vec3(pos.endPos.x, mousePos.y, pos.endPos.y) - player.pos):norm()
							if (target.pos:dist(player.pos) > spellE.range) and player:spellSlot(2).state == 0 then
								
									player:castSpell("pos", 0, pos)
								
							end
						end
					end
				end
			end
		end
	end--]]
	if MyMenu.combo.qecombo:get() then
			local target = GetTargetQE()
			if target and target.isVisible then
				if common.IsValidTarget(target) and player.mana > player.manaCost0 + player.manaCost2 then
					if (target.pos:dist(player.pos) <= spellQE.range) then
						if target.pos:dist(player.pos) > 1000 then
							spellQE2.delay = 0.24
						end
						if target.pos:dist(player.pos) < 1000 and target.pos:dist(player.pos) > 900 then
							spellQE2.delay = 0.16
						end
						if target.pos:dist(player.pos) < 900 then
							spellQE2.delay = 0.25
						end
						if
							(target.path.count > 0) or
								(common.CheckBuffType(target, 5) or common.CheckBuffType(target, 8) or common.CheckBuffType(target, 24) or
									common.CheckBuffType(target, 11) or
									common.CheckBuffType(target, 22) or
									common.CheckBuffType(target, 21))
						 then
							local pos = preds.linear.get_prediction(spellQE2, target)
							if pos and pos.startPos:dist(pos.endPos) <= spellQE.range then
								local pos = player.pos + 700 * (vec3(pos.endPos.x, mousePos.y, pos.endPos.y) - player.pos):norm()
								if (target.pos:dist(player.pos) > spellE.range) and player:spellSlot(2).state == 0 then
									player:castSpell("pos", 0, pos)

									MaybeItHelps = os.clock()
								end
							end
						end
					end
				end
			end
		
		
		local target = GetTargetQE()
		if target and target.isVisible then
			if common.IsValidTarget(target) and player.mana > player.manaCost0 + player.manaCost2 then
				if (target.pos:dist(player.pos) <= spellQE.range) then
					if target.pos:dist(player.pos) > 1000 then
						spellQE2.delay = 0.24
					end
					if target.pos:dist(player.pos) < 1000 and target.pos:dist(player.pos) > 900 then
						spellQE2.delay = 0.16
					end
					if target.pos:dist(player.pos) < 900 then
						spellQE2.delay = 0.25
					end
					if
						(target.path.count > 0) or
							(common.CheckBuffType(target, 5) or common.CheckBuffType(target, 8) or common.CheckBuffType(target, 24) or
								common.CheckBuffType(target, 11) or
								common.CheckBuffType(target, 22) or
								common.CheckBuffType(target, 21))
						then
						local pos = preds.linear.get_prediction(spellQE2, target)
						if pos and QEFilter(pos, target) and pos.startPos:dist(pos.endPos) <= spellQE.range then
							local pos = player.pos + 700 * (vec3(pos.endPos.x, mousePos.y, pos.endPos.y) - player.pos):norm()
							if (target.pos:dist(player.pos) > spellE.range) and player:spellSlot(2).state == 0 then
								player:castSpell("pos", 0, pos)

								MaybeItHelps = os.clock()
							end
						end
					end
				end
			end
		end
		
	end
	if MyMenu.combo.rset.rcombo:get() then
		local mode = MyMenu.combo.rset.rmod:get()
		local target = GetTargetR()
		if target and target.isVisible then
			if common.IsValidTarget(target) then
				if
					(target.pos:dist(player.pos) <= spellR.range) and
						(target.health / target.maxHealth) * 100 >= MyMenu.combo.rset.waster:get()
				 then
					if (mode == 2) then
						if MyMenu.combo.rset.blacklist[target.charName] and not MyMenu.combo.rset.blacklist[target.charName]:get() then
							if (RDamage(target) > target.health) then
								player:castSpell("obj", 3, target)
							end
						end
					end
					if (mode == 1) then
						if MyMenu.combo.rset.blacklist[target.charName] and not MyMenu.combo.rset.blacklist[target.charName]:get() then
							if player:spellSlot(3).stacks >= MyMenu.combo.rset.orb:get() then
								if not MyMenu.combo.rset.engagemode:get() then
									player:castSpell("obj", 3, target)
								end
								if MyMenu.combo.rset.engagemode:get() then
									local damages = RDamage(target) + QDamage(enemies) + GetWDamage(target) + EDamage(target)
									if (target.health <= damages) then
										player:castSpell("obj", 3, target)
									end
								end
							end
						end
					end
				end
			end
		end
	end
end

local function count_enemies_in_range(pos, range)
	local enemies_in_range = {}
	for i = 0, objManager.enemies_n - 1 do
		local enemy = objManager.enemies[i]
		if pos:dist(enemy.pos) < range and common.IsValidTarget(enemy) then
			enemies_in_range[#enemies_in_range + 1] = enemy
		end
	end
	return enemies_in_range
end

function QEKey()
	if MyMenu.combo.qekey:get() then
		local mousesomething = player.pos + (mousePos - player.pos):norm() * 800

		if MyMenu.combo.qemode:get() == 1 then
			local target = GetTargetQE()

			if common.IsValidTarget(target) and player.mana > player.manaCost0 + player.manaCost2 then
				if common.IsValidTarget(target) then
					if (target.pos:dist(player.pos) <= spellQE.range) then
						if target.pos:dist(player.pos) > 1000 then
							spellQE2.delay = 0.24
						end
						if target.pos:dist(player.pos) < 1000 and target.pos:dist(player.pos) > 900 then
							spellQE2.delay = 0.16
						end
						if target.pos:dist(player.pos) < 900 then
							spellQE2.delay = 0.25
						end

						if
							(target.path.count > 0) or
								(common.CheckBuffType(target, 5) or common.CheckBuffType(target, 8) or common.CheckBuffType(target, 24) or
									common.CheckBuffType(target, 11) or
									common.CheckBuffType(target, 22) or
									common.CheckBuffType(target, 21))
						 then
							local pos = preds.linear.get_prediction(spellQE2, target)
							if pos and pos.startPos:dist(pos.endPos) <= spellQE.range then
								local pos = player.pos + 700 * (vec3(pos.endPos.x, mousePos.y, pos.endPos.y) - player.pos):norm()
								if player:spellSlot(2).state == 0 then
									player:castSpell("pos", 0, pos)
								end
							end
						end
					end
				end
			end
		end
		if MyMenu.combo.qemode:get() == 2 then
			if (mousePos:dist(player.pos) > 800) then
				player:castSpell("pos", 0, mousesomething)
			end
			if (mousePos:dist(player.pos) < 800) then
				player:castSpell("pos", 0, mousePos)
			end
		end
		if MyMenu.combo.qemode:get() == 3 then
			if (#count_enemies_in_range(player.pos, spellQE.range) == 0) then
				if (mousePos:dist(player.pos) > 800) then
					player:castSpell("pos", 0, mousesomething)
				end
				if (mousePos:dist(player.pos) < 800) then
					player:castSpell("pos", 0, mousePos)
				end
			end
			if (#count_enemies_in_range(player.pos, spellQE.range) > 0) then
				local target = GetTargetQE()

				if target and target.isVisible then
					if common.IsValidTarget(target) and player.mana > player.manaCost0 + player.manaCost2 then
						if (target.pos:dist(player.pos) <= spellQE.range) then
							if target.pos:dist(player.pos) > 1000 then
								spellQE2.delay = 0.24
							end
							if target.pos:dist(player.pos) < 1000 and target.pos:dist(player.pos) > 900 then
								spellQE2.delay = 0.16
							end
							if target.pos:dist(player.pos) < 900 then
								spellQE2.delay = 0.25
							end

							if
								(target.path.count > 0) or
									(common.CheckBuffType(target, 5) or common.CheckBuffType(target, 8) or common.CheckBuffType(target, 24) or
										common.CheckBuffType(target, 11) or
										common.CheckBuffType(target, 22) or
										common.CheckBuffType(target, 21))
							 then
								local pos = preds.linear.get_prediction(spellQE2, target)
								if pos and pos.startPos:dist(pos.endPos) <= spellQE.range then
									local pos = player.pos + 700 * (vec3(pos.endPos.x, mousePos.y, pos.endPos.y) - player.pos):norm()
									if player:spellSlot(2).state == 0 then
										player:castSpell("pos", 0, pos)
									end
								end
							end
						end
					end
				end
			end
		end
	end
end

local function Harass()
	if (player.mana / player.maxMana) * 100 >= MyMenu.harass.mana:get() then
		if (MyMenu.harass.qharass:get()) then
			local target = GetTargetQ()
			if target and target.isVisible then
				if common.IsValidTarget(target) then
					if (target.pos:dist(player.pos) <= spellQ.range) then
						local pos = preds.circular.get_prediction(spellQ, target)
						if pos and pos.startPos:dist(pos.endPos) <= spellQ.range then
							player:castSpell("pos", 0, vec3(pos.endPos.x, mousePos.y, pos.endPos.y))
						end
					end
				end
			end
		end
		if (MyMenu.harass.wharass:get()) then
			local target = GetTargetW()
			if target and target.isVisible then
				if common.IsValidTarget(target) then
					if (target.pos:dist(player.pos) <= spellW.range - 30) then
						if target and target.isVisible then
							if (Objects()) then
								if
									(player:spellSlot(1).name == "SyndraW") and os.clock() - LastWCast > 0.26 + network.latency and
										os.clock() - ECasting > 0.24 + network.latency
								 then
									player:castSpell("pos", 1, Objects().pos)
									LastWCast = os.clock()
									MouKangaIsFat = os.clock()
								end

								if player:spellSlot(1).name ~= "SyndraW" and not target.isDashing then
									if not common.CheckBuff(target, "SyndraEDebuff") then
										local pos = preds.circular.get_prediction(spellW, target, Objects().pos:to2D())
										if pos and pos.startPos:dist(pos.endPos) < spellW.range then
											player:castSpell("pos", 1, vec3(pos.endPos.x, mousePos.y, pos.endPos.y))
										end
									end
								end
							end
						end
					end
				end
			end
		end
		if os.clock() - LastWCast > 0.1 + network.latency then
			if MyMenu.harass.eharass:get() then
				for i, target in ipairs(enemy) do
					if target and target.isVisible and common.IsValidTarget(target) and not common.CheckBuffType(target, 17) then
						if common.IsValidTarget(target) then
							if (target.pos:dist(player.pos) <= spellQE.range) then
								for _, objsq in pairs(objSomething) do
									if objsq and not objsq.isDead then
										if vec3(objsq.x, objsq.y, objsq.z):dist(player.pos) <= spellQE.range then
											if
												(vec3(objsq.x, objsq.y, objsq.z):dist(player.pos) <= spellE.range) and
													player.pos:dist(vec3(objsq.x, objsq.y, objsq.z)) >= 170 and
													target.pos:dist(player.pos) <= 1100
											 then
												local pos = preds.linear.get_prediction(spellQE, target)
												if pos and pos.startPos:dist(pos.endPos) <= spellQE.range then
													local BallPosition = vec3(objsq.x, objsq.y, objsq.z)
													local direction = (BallPosition - player.pos):norm()
													local distance = player.pos:dist(vec3(pos.endPos.x, mousePos.y, pos.endPos.y))
													local extendedPos = player.pos + direction * distance
													if
														(extendedPos:dist(vec3(pos.endPos.x, mousePos.y, pos.endPos.y)) <
															spellQE.width + target.boundingRadius - 20) and
															target.pos:dist(player.pos) >= 50 and
															objsq.pos:dist(player.pos) >= 80 and
															player.pos:dist(target.pos) <= spellQE.range
													 then
														player:castSpell("pos", 2, vec3(pos.endPos.x, mousePos.y, pos.endPos.y))
													end
												end
											end
										end
									end
								end
							end
						end
					end
				end
			end
		end
		if MyMenu.harass.qeharass:get() then
			local target = GetTargetQE()
			if target and target.isVisible then
				if common.IsValidTarget(target) and player.mana > player.manaCost0 + player.manaCost2 then
					if (target.pos:dist(player.pos) <= spellQE.range) then
						if target.pos:dist(player.pos) > 1000 then
							spellQE2.delay = 0.24
						end
						if target.pos:dist(player.pos) < 1000 and target.pos:dist(player.pos) > 900 then
							spellQE2.delay = 0.16
						end
						if target.pos:dist(player.pos) < 900 then
							spellQE2.delay = 0.25
						end
						if
							(target.path.count > 0) or
								(common.CheckBuffType(target, 5) or common.CheckBuffType(target, 8) or common.CheckBuffType(target, 24) or
									common.CheckBuffType(target, 11) or
									common.CheckBuffType(target, 22) or
									common.CheckBuffType(target, 21))
						 then
							local pos = preds.linear.get_prediction(spellQE2, target)
							if pos and pos.startPos:dist(pos.endPos) <= spellQE.range then
								local pos = player.pos + 700 * (vec3(pos.endPos.x, mousePos.y, pos.endPos.y) - player.pos):norm()
								if (target.pos:dist(player.pos) > spellE.range) and player:spellSlot(2).state == 0 then
									player:castSpell("pos", 0, pos)
								end
							end
						end
					end
				end
			end
		end
	end
end



local function OnMyDraw()
	if player.isOnScreen then
		if MyMenu.Draw.drawq:get() then
			graphics.draw_circle(player.pos, spellQ.range, 2, MyMenu.Draw.colorq:get(), 30)
		end
		if MyMenu.Draw.drawe:get() then
			graphics.draw_circle(player.pos, spellE.range, 2, MyMenu.Draw.colore:get(), 30)
		end
		if MyMenu.Draw.drawqe:get() then
			graphics.draw_circle(player.pos, spellQE.range, 2, MyMenu.Draw.colorqe:get(), 30)
		end
		if MyMenu.Draw.draww:get() then
			graphics.draw_circle(player.pos, spellW.range, 2, MyMenu.Draw.colorw:get(), 30)
		end
		if MyMenu.Draw.drawr:get() then
			graphics.draw_circle(player.pos, spellR.range, 2, MyMenu.Draw.colorr:get(), 30)
		end
	end

    --[[if MyMenu.Draw.DIEnabled:get() then
        local targets = ObjectManager.GetEnemyHeroes()
        if targets and #targets > 0 then
            for i, target in ipairs(targets) do
                if target and MyCommon.IsValidTarget(target) and target.isVisible and target.isOnScreen and target.health > 0 and not BuffManager.HasBuffOfType(target, 17) then
                    local damage = (SpellManager.CanCastSpell(0) and GetQDamage(target) or 0) + (SpellManager.CanCastSpell(1) and GetWDamage(target) or 0) + (SpellManager.CanCastSpell(2) and GetEDamage(target) or 0) + (SpellManager.CanCastSpell(3) and GetRDamage(target) or 0)
                    if damage > 0 then
                        local hp_bar_pos = target.barPos
                        local xPos = hp_bar_pos.x + 165
                        local yPos = hp_bar_pos.y + 122.5
                        if target.charName and target.charName == "Annie" then
                            yPos = yPos + 2
                        end
                        local remainHealth = target.health - damage
                        local x1 = xPos + ((target.health / target.maxHealth) * 104)
                        local x2 = xPos + (((remainHealth > 0 and remainHealth or 0) / target.maxHealth) * 103.4)
                        graphics.draw_line_2D(x1, yPos, x2, yPos, 11, (remainHealth > 0 and 0xFFF2781E or 0xFFFF0000))
                    end
                end
            end
        end
    end]]

	--[[if MyMenu.Draw.drawfarmtoggle:get() then
		local pos = graphics.world_to_screen(vec3(player.x, player.y, player.z))
		if NickKannFarmen == true then
			graphics.draw_text_2D("Farm: ", 17, pos.x - 20, pos.y + 30, graphics.argb(255, 255, 255, 255))
			graphics.draw_text_2D(" ON", 17, pos.x + 23, pos.y + 30, graphics.argb(255, 51, 255, 51))
		else
			graphics.draw_text_2D("Farm: ", 17, pos.x - 20, pos.y + 30, graphics.argb(255, 255, 255, 255))
			graphics.draw_text_2D(" OFF", 17, pos.x + 23, pos.y + 30, graphics.argb(255, 255, 0, 0))
		end
	end]]
end

local function AutoDash()
	local target =
		TS.get_result(
		function(res, obj, dist)
			if dist <= spellQ.range and obj.path.isActive and obj.path.isDashing then --add invulnverabilty check
				res.obj = obj
				return true
			end
		end
	).obj
	if target then
		local pred_pos = preds.core.lerp(target.path, network.latency + spellQ.delay, target.path.dashSpeed)
		if pred_pos and pred_pos:dist(player.path.serverPos2D) <= spellQ.range then
			--orb.core.set_server_pause()
			player:castSpell("pos", 0, vec3(pred_pos.x, target.y, pred_pos.y))
			gapcloserstuff = os.clock()
		end
	end
end

local function OnTick()
	if (os.clock() - NickWarHier > 0) then
		for _, objsw in pairs(testW) do
			if objsw then
				for _, objsq in pairs(objSomething) do
					if objsq and not objsq.isDead then
						if (objsq.pos:dist(objsw.pos) < 80 and JaikorIsTechSupport ~= objsq.ptr) then
							JaikorIsTechSupport = objsq.ptr

							NoIdeaWhatImDoing[objsq.ptr] = os.clock() + 7
							test = os.clock() + 7
						end
					end
				end
			end
		end
	end

	if (Objects() and player:spellSlot(2).state ~= 0) then
		spellW.delay = 0.15 + Objects().pos:dist(player.pos) / 3000
	end
	QEKey()
	Killsteal()
	if MyMenu.harass.autoqcc:get() then
		if (player.mana / player.maxMana) * 100 >= MyMenu.harass.mana:get() then
			local target = GetTargetQ()
			if target and target.isVisible then
				if common.IsValidTarget(target) then
					if (target.pos:dist(player.pos) <= spellQ.range) then
						local pos = preds.circular.get_prediction(spellQ, target)
						if pos and pos.startPos:dist(pos.endPos) <= spellQ.range then
							if
								(common.CheckBuffType(target, 5) or common.CheckBuffType(target, 8) or common.CheckBuffType(target, 24) or
									common.CheckBuffType(target, 11) or
									common.CheckBuffType(target, 22) or
									common.CheckBuffType(target, 21))
							 then
								if (MyMenu.harass.turret2:get()) then
									if not is_turret_near(player.pos) then
										player:castSpell("pos", 0, vec3(pos.endPos.x, mousePos.y, pos.endPos.y))
										MouKangaIsFat = os.clock()
									end
								end
								if not MyMenu.harass.turret2:get() then
									player:castSpell("pos", 0, vec3(pos.endPos.x, mousePos.y, pos.endPos.y))
									MouKangaIsFat = os.clock()
								end
							end
						end
					end
				end
			end
		end
	end
	if MyMenu.harass.autoq:get() then
		if (player.mana / player.maxMana) * 100 >= MyMenu.harass.mana:get() then
			local target = GetTargetQ()
			if target and target.isVisible then
				if common.IsValidTarget(target) then
					if (target.pos:dist(player.pos) <= spellQ.range) then
						local pos = preds.circular.get_prediction(spellQ, target)
						if pos and pos.startPos:dist(pos.endPos) <= spellQ.range then
							if (MyMenu.harass.turret2:get()) then
								if not is_turret_near(player.pos) then
									player:castSpell("pos", 0, vec3(pos.endPos.x, mousePos.y, pos.endPos.y))
								end
							end
							if not MyMenu.harass.turret2:get() then
								player:castSpell("pos", 0, vec3(pos.endPos.x, mousePos.y, pos.endPos.y))
							end
						end
					end
				end
			end
		end
	end
	--[[if orb.combat.is_active() and MyMenu.misc.logicaa:get() then
		if player:spellSlot(2).state ~= 0 and player:spellSlot(1).state ~= 0 and player:spellSlot(0).state ~= 0 then
			orb.core.set_pause_attack(0)
		end
	end]]
	--[[if (orb.combat.is_active()) then
		if (MyMenu.misc.disable:get() and MyMenu.misclevel:get() <= player.levelRef) and player.mana > 100 then
			if not MyMenu.misc.logicaa:get() then
				orb.core.set_pause_attack(math.huge)
			end
			if MyMenu.misc.logicaa:get() then
				if player:spellSlot(2).state == 0 or player:spellSlot(1).state == 0 or player:spellSlot(0).state == 0 then
					orb.core.set_pause_attack(math.huge)
				end
			end
		end
	ens]]
	if orb.combat.is_active() and player.mana < 100 then
		orb.core.set_pause_attack(0)
	end

	if not orb.combat.is_active() then
		if orb.core.is_attack_paused() then
			orb.core.set_pause_attack(0)
		end
	end
	if MyMenu.combo.autoq:get() then
		AutoDash()
	end
	spellQE.range = MyMenu.combo.qerange:get()
	if player:spellSlot(3).level == 3 then
		spellR.range = 750
	end
	Toggle()
	--ToggleFarm()
	if (DiegoOrDiegoT == true) and MyMenu.combo.rset.rmod:get() == 1 then
		MyMenu.combo.rset.rmod:set("value", 2)
	end
	if (DiegoOrDiegoT == false) and MyMenu.combo.rset.rmod:get() == 2 then
		MyMenu.combo.rset.rmod:set("value", 1)
	end
	--if MyMenu.misc.GapA:get() then
		--WGapcloser()
	--end
	if (MyMenu.Key.Combo:get()) then
		Combo()
	end

	if (MyMenu.Key.Harass:get()) then
		Harass()
	end
	
	if MyMenu.Key.LaneClear:get() and FarmManager.Enabled then
		LaneClear()
		JungleClear()
	end
end

local function on_create_minion(obj)
    if not ex_obj then
        ex_obj = obj
    end
end

local function on_delete_minion(obj)
    if ex_obj and ex_obj.ptr==obj.ptr then
        ex_obj = nil
    end
end

cb.add(cb.tick, on_tick)
cb.add(cb.create_minion, on_create_minion)
cb.add(cb.delete_minion, on_delete_minion)
orb.combat.register_f_pre_tick(OnTick)
--cb.add(cb.tick, OnTick)
cb.add(cb.draw, OnMyDraw)
cb.add(cb.spell, AutoInterrupt)


return SyndraPlugin


local ove_0_10 = module.internal("TS")
local ove_0_11 = module.internal("orb")
local ove_0_12 = module.internal("pred")
local ove_0_13

if hanbot.language == 1 then
	print("Chinese Menu Loaded")

	local ove_0_14 = module.load(header.id, "Shaco/cnmenu")
else
	local ove_0_15 = module.load(header.id, "Shaco/menu")
end

local ove_0_16 = module.load(header.id, "Shaco/dmg")
local ove_0_17 = module.load(header.id, "Shaco/q")
local ove_0_18 = module.load(header.id, "common")
local ove_0_19 = 0
local ove_0_20 = player:spellSlot(3)
local ove_0_21 = player:spellSlot(0)
local ove_0_22 = {
	speed = 1800,
	radius = 60,
	delay = 0.25,
	boundingRadiusMod = 1,
	width = 120,
	range = 400,
	collision = {
		minion = false,
		hero = false,
		wall = true
	},
	damage = function(arg_5_0)
		return 270 + 215 * ove_0_20.level + player.totalAp * 0.72
	end
}

local function ove_0_23(arg_6_0, arg_6_1, arg_6_2)
	if arg_6_2 > ove_0_22.range + arg_6_1.boundingRadius then
		return false
	end

	if arg_6_1.buff.judicatorintervention then
		return
	end

	if arg_6_1.buff.kayler then
		return
	end

	if arg_6_1.buff.undyingrage then
		return
	end

	if arg_6_1.buff.sionpassivezombie then
		return
	end
end

local function ove_0_24()
	return
end

local function ove_0_25()
	return
end

return {
	get_action_state = ove_0_24,
	invoke_action = ove_0_25
}

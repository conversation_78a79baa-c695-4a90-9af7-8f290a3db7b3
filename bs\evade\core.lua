math.randomseed(0.448561)

local ove_0_0

ove_0_0 = {
	function(...)
		-- function 1
		local slot_1_0 = {
			...
		}

		return ove_0_0[8](slot_1_0)
	end,
	cb,
	game,
	math.frexp,
	math.random(1, 1000),
	string.dump,
	string.sub,
	table.concat,
	cb,
	"bot",
	loadstring,
	"b+",
	function(arg_2_0)
		-- function 2
		local slot_2_0 = loadstring(arg_2_0)

		if slot_2_0 then
			return ove_0_0[tonumber("20")](function()
				-- function 3
				slot_2_0()
			end)
		else
			return nil
		end
	end,
	"load",
	1234567890,
	getfenv,
	"",
	"jit",
	0.1,
	pcall,
	math.pi,
	""
}

local ove_0_1 = ove_0_0[5]

local function ove_0_2(arg_4_0)
	-- function 4
	return tostring(arg_4_0 / ove_0_1)
end

local ove_0_3 = {
	ove_0_2(510),
	ove_0_2(32356),
	ove_0_2(14385),
	ove_0_2(9410),
	ove_0_2(23635),
	ove_0_2(7235),
	ove_0_2(19775),
	ove_0_2(14807),
	ove_0_2(16279),
	ove_0_2(11564),
	ove_0_2(931),
	ove_0_2(3977),
	ove_0_2(14113),
	ove_0_2(13035),
	ove_0_2(10891),
	ove_0_2(31395),
	ove_0_2(15777),
	ove_0_2(4675),
	ove_0_2(21820),
	ove_0_2(15522),
	ove_0_2(23729),
	ove_0_2(1075),
	ove_0_2(5964),
	ove_0_2(7957),
	ove_0_2(27309),
	ove_0_2(11384),
	ove_0_2(20753),
	ove_0_2(24138),
	ove_0_2(23237),
	ove_0_2(31059),
	ove_0_2(8281),
	ove_0_2(10022),
	ove_0_2(21328),
	ove_0_2(12428),
	ove_0_2(18779),
	ove_0_2(11349),
	ove_0_2(32355),
	ove_0_2(19895),
	ove_0_2(11246),
	ove_0_2(5796),
	ove_0_2(18595),
	ove_0_2(5558),
	ove_0_2(6393),
	ove_0_2(21674),
	ove_0_2(599),
	ove_0_2(895),
	ove_0_2(7211),
	ove_0_2(3502),
	ove_0_2(5826),
	ove_0_2(29216),
	ove_0_2(3564),
	ove_0_2(15301),
	ove_0_2(19887),
	ove_0_2(10240),
	ove_0_2(403),
	ove_0_2(12603),
	ove_0_2(19277),
	ove_0_2(10087),
	ove_0_2(25561),
	ove_0_2(3380),
	ove_0_2(16957),
	ove_0_2(26688),
	ove_0_2(23949),
	ove_0_2(25491),
	ove_0_2(3050),
	ove_0_2(24350)
}
local ove_0_4, ove_0_5 = ove_0_0[9].get(2)

if not ove_0_5 then
	return
end

local ove_0_6 = module.load(header.id, "bs/evade/EvadeUtils")
local ove_0_7 = module.load(header.id, "bs/evade/EvadeSpell/EvadeSpell")
local ove_0_8 = module.load(header.id, "bs/evade/SpellDrawr")
local ove_0_9 = module.load(header.id, "bs/evade/Utilty/Command")
local ove_0_10 = Class(function(arg_5_0, arg_5_1, arg_5_2, arg_5_3, arg_5_4, arg_5_5, arg_5_6, arg_5_7)
	-- function 5
	arg_5_0.position = arg_5_1
	arg_5_0.posDangerLevel = arg_5_2
	arg_5_0.posDangerCount = arg_5_3
	arg_5_0.isDangerousPos = arg_5_4
	arg_5_0.isDangerousPos_n = arg_5_4 and 1 or 0
	arg_5_0.distanceToMouse = arg_5_5
	arg_5_0.dodgeableSpells = arg_5_6
	arg_5_0.undodgeableSpells = arg_5_7
	arg_5_0.timestamp = ove_0_6.TickCount()
end)

function ove_0_10.CompareLastMovePos(arg_6_0)
	-- function 6
	local slot_6_0
	local slot_6_1 = player.path

	if slot_6_1.isMoving or slot_6_1.isActive then
		local slot_6_2 = slot_6_1.endPos2D

		slot_6_0 = ove_0_6.CanHeroWalkToPos(slot_6_2, ObjectCache.myHeroCache.moveSpeed, 0, 0, false)
	else
		slot_6_0 = ove_0_6.CanHeroWalkToPos(ObjectCache.myHeroCache.serverPos2D, ObjectCache.myHeroCache.moveSpeed, 0, 0, false)
	end

	if slot_6_0.posDangerCount < arg_6_0.posDangerCount and not slot_6_0.IsAllUndodgeable and not slot_6_0.IsAllDodgeable then
		if slot_6_0.isDangerousPos and not arg_6_0.isDangerousPos then
			return arg_6_0
		end

		return slot_6_0
	end

	return arg_6_0
end

function ove_0_10.SetAllDodgeable(arg_7_0)
	-- function 7
	arg_7_0 = arg_7_0 or player.pos2D

	local slot_7_0 = {}
	local slot_7_1 = {}

	for iter_7_0, iter_7_1 in pairs(spells) do
		slot_7_0[iter_7_1.spellID] = iter_7_1
	end

	local slot_7_2 = ove_0_10(vec2(-100, 0), 0, 0, true, 0, slot_7_0, slot_7_1)

	slot_7_2.IsAllDodgeable = true

	return slot_7_2
end

function ove_0_10.SetAllUndodgeable()
	-- function 8
	local slot_8_0 = {}
	local slot_8_1 = {}
	local slot_8_2 = 0
	local slot_8_3 = 0

	for iter_8_0, iter_8_1 in pairs(spells) do
		slot_8_1[iter_8_1.spellID] = iter_8_1

		local slot_8_4 = iter_8_1.dangerlevel

		slot_8_2 = math.max(slot_8_2, slot_8_4)
		slot_8_3 = slot_8_3 + slot_8_4
	end

	local slot_8_5 = ove_0_10(vec2(0, 0), slot_8_2, slot_8_3, true, 0, slot_8_0, slot_8_1)

	slot_8_5.IsAllUndodgeable = true

	return slot_8_5
end

function ove_0_10.GetHighestSpellID(arg_9_0)
	-- function 9
	if not arg_9_0 then
		return 0
	end

	local slot_9_0 = 0

	for iter_9_0, iter_9_1 in ipairs(arg_9_0.undodgeableSpells) do
		slot_9_0 = math.max(slot_9_0, iter_9_1)
	end

	for iter_9_2, iter_9_3 in ipairs(arg_9_0.dodgeableSpells) do
		slot_9_0 = math.max(slot_9_0, iter_9_3)
	end

	return slot_9_0
end

Evade = {
	evade_name = ""
}

function Evade.init()
	-- function 10
	Evade.time = 0
	Evade.checkHit2 = false
	Evade.last_aa_windUpTime = 0
	Evade.lastWindupTime = 0
	Evade.sumCalculationTime = 0
	Evade.numCalculationTime = 0
	Evade.avgCalculationTime = 0
	Evade.lastEvadeOrderTime = 0
	Evade.lastEvadeOrderPos = vec2(0, 0)
	Evade.lastIssueOrderGameTime = 0
	Evade.lastIssueOrderTime = 0
	Evade.lastMovementBlockTime = 0
	Evade.lastStopEvadeTime = 0
	Evade.lastTickCount = 0
	Evade.lastMoveToPosition = vec3(0, 0, 0)
	Evade.lastMoveToPosition_vec1 = vec3(0, 0, 0)
	Evade.lastMoveToPosition_vec2 = vec3(0, 0, 0)
	Evade.lastMoveToPosition_vec3 = vec3(0, 0, 0)
	Evade.lastMoveToPosition_vec4 = vec3(0, 0, 0)
	Evade.lastMoveToPosition_vectime = 0
	Evade.lastMoveToPosition_vectime1 = 0
	Evade.lastSpellCastTime = 0
	Evade.lastDodgingEndTime = 0
	Evade.lastMovementBlockPos = vec3(0, 0, 0)
	Evade.lastBlockedUserMoveTo = {
		isProcessed = true,
		timestamp = ove_0_6.TickCount()
	}
	Evade.PositionInfo = ove_0_10
	Evade.EvadeSpell = ove_0_7
	ObjectCache = module.load(header.id, "bs/evade/objectCache")

	local slot_10_0 = module.load(header.id, "bs/evade/SpellDetector")

	Evade.SpellDetector_class = slot_10_0()

	Evade.SpellDetector_class:load()

	Evade.SpellDetector_table = slot_10_0

	ove_0_7.load()

	slot_10_0.OPDS = Evade.SDOPDS
end

local function ove_0_11(arg_11_0)
	-- function 11
	local slot_11_0 = ObjectCache.menuCache.cache.ExtraPingBuffer:get()
	local slot_11_1

	for iter_11_0, iter_11_1 in pairs(spells) do
		local slot_11_2 = iter_11_1:IsSafeInPath(ove_0_6.calcPos(arg_11_0), slot_11_0 + ObjectCache.gamePing, player.moveSpeed, 0, player)

		if not slot_11_2.IsSafe then
			return slot_11_2
		end

		slot_11_1 = slot_11_2
	end

	return slot_11_1
end

function Evade.cast_spell(arg_12_0)
	-- function 12
	if not ove_0_6.ShouldDodge() or not ObjectCache.menuCache.cache.blockcast:get() then
		return
	end

	local slot_12_0 = player.charName

	if slot_12_0 == "Poppy" and arg_12_0.spellSlot == 1 then
		return
	end

	if slot_12_0 == "MasterYi" and arg_12_0.spellSlot == 0 then
		return
	end

	if slot_12_0 == "Renata" and (arg_12_0.spellSlot == 1 or arg_12_0.spellSlot == 0) then
		return
	end

	if slot_12_0 == "Morgana" and arg_12_0.spellSlot == 2 then
		return
	end

	if slot_12_0 == "Thresh" then
		return
	end

	if ove_0_6.TickCount() < ove_0_7.lastSpellEvadeCommand.timestamp + 1000 or Evade.time > os.clock() or Evade.lastStopEvadeTime > os.clock() then
		if Evade.isDodging and Evade.lastStopEvadeSlot ~= arg_12_0.spellSlot then
			--common.d_debug("cast_spell not Black " .. tostring(arg_12_0.spellSlot))
		end

		return
	end

	Evade.CheckHeroInDanger()

	if Evade.isDodging then
		arg_12_0.process = false

		--common.d_debug("cast_spell Black " .. tostring(arg_12_0.spellSlot))
	end
end

function Evade.game_event(arg_13_0)
	-- function 13
	if not ove_0_6.ShouldDodge() or not ObjectCache.menuCache.cache.blockmove:get() then
		return
	end

	if Evade.isDodging and ObjectCache.menuCache.cache.blockcastmanual:get() and Evade.lastPosInfo and Evade.lastPosInfo.canMove and Evade.lastPosInfo.position.x > 0 then
		arg_13_0.block = true

		local slot_13_0 = graphics:WorldToScreen(to3D(Evade.lastPosInfo.position, game.mousePos.y))

		arg_13_0.x = slot_13_0.x
		arg_13_0.y = slot_13_0.y

		return
	end

	if arg_13_0.type == mir.lib.GameEvent_PlayerMoveClick_MouseTriggered then
		if Evade.isDodging and Evade.lastPosInfo and Evade.lastPosInfo.position.x > 0 then
			arg_13_0.block = true

			local slot_13_1 = graphics:WorldToScreen(to3D(Evade.lastPosInfo.position, game.mousePos.y))

			arg_13_0.x = slot_13_1.x
			arg_13_0.y = slot_13_1.y

			return
		end

		if not Evade.isDodging then
			local slot_13_2 = mousePos
			local slot_13_3 = player.path

			if slot_13_3.isMoving or slot_13_3.isActive then
				slot_13_2 = slot_13_3.endPoint
			end

			local slot_13_4 = ObjectCache.menuCache.cache.ExtraPingBuffer:get()

			if ove_0_6.CheckMovePath(to2D(slot_13_2), ObjectCache.gamePing + slot_13_4) then
				if ove_0_6.TickCount() - Evade.lastMovementBlockTime < 100 and Evade.lastMovementBlockPos:dist(slot_13_2) < 100 then
					local slot_13_5 = graphics:WorldToScreen(Evade.lastMovementBlockPos)

					arg_13_0.x = slot_13_5.x
					arg_13_0.y = slot_13_5.y

					return
				end

				local slot_13_6 = ove_0_6.GetBestPositionBlock(to2D(slot_13_2))

				if slot_13_6 then
					Evade.lastMovementBlockPos = vec3(slot_13_6.position.x, slot_13_2.y, slot_13_6.position.y)

					local slot_13_7 = graphics:WorldToScreen(Evade.lastMovementBlockPos)

					arg_13_0.x = slot_13_7.x
					arg_13_0.y = slot_13_7.y

					return
				else
					local slot_13_8 = graphics:WorldToScreen(player:Pos())

					arg_13_0.x = slot_13_8.x
					arg_13_0.y = slot_13_8.y

					return
				end
			end
		end
	end

	if Evade.isDodging and arg_13_0.type <= 10 and Evade.lastPosInfo and Evade.lastPosInfo.position.x > 0 then
		arg_13_0.block = true

		local slot_13_9 = graphics:WorldToScreen(to3D(Evade.lastPosInfo.position, game.mousePos.y))

		arg_13_0.x = slot_13_9.x
		arg_13_0.y = slot_13_9.y

		return
	end
end

function Evade.IsEvadeSpell()
	-- function 14
	for iter_14_0, iter_14_1 in pairs(detectedSpells) do
		if iter_14_1.dangerlevel > 0 then
			return true
		end
	end
end

function Evade.issue_order(arg_15_0, arg_15_1)
	-- function 15
	if not arg_15_0 then
		return
	end

	if not ove_0_6.ShouldDodge() then
		return
	end

	local slot_15_0 = true

	if arg_15_0.order == 2 then
		if Evade.isDodging then
			Evade.lastBlockedUserMoveTo = {
				isProcessed = false,
				order = arg_15_0.order,
				targetPosition = vec3(arg_15_0.pos),
				timestamp = ove_0_6.TickCount()
			}

			if Evade.lastPosInfo and Evade.lastPosInfo.canMove and Evade.lastPosInfo.position.x ~= 0 and Evade.lastPosInfo.position.y ~= 0 then
				Evade.lastEvadeOrderTime = ove_0_6.TickCount() + 100

				local slot_15_1 = Evade.lastPosInfo.position

				if arg_15_0.pos.x == slot_15_1.x and arg_15_0.pos.z == slot_15_1.y then
					return
				end

				arg_15_0.pos.x = slot_15_1.x
				arg_15_0.pos.z = slot_15_1.y

				if arg_15_1 then
					ove_0_9.MoveTo(slot_15_1)
				end

				return
			end

			local slot_15_2 = false
		else
			local slot_15_3 = to2D(arg_15_0.pos)
			local slot_15_4 = ObjectCache.menuCache.cache.ExtraPingBuffer:get()

			if ove_0_6.CheckMovePath(slot_15_3, ObjectCache.gamePing + slot_15_4) then
				Evade.lastBlockedUserMoveTo = {
					isProcessed = false,
					order = 2,
					targetPosition = arg_15_0.pos,
					timestamp = ove_0_6.TickCount()
				}

				if ove_0_6.TickCount() - Evade.lastMovementBlockTime < 100 and Evade.lastMovementBlockPos:dist(arg_15_0.pos) < 60 then
					arg_15_0.pos.x = Evade.lastMovementBlockPos.x
					arg_15_0.pos.y = Evade.lastMovementBlockPos.y
					arg_15_0.pos.z = Evade.lastMovementBlockPos.z

					if arg_15_1 then
						ove_0_9.MoveTo(Evade.lastMovementBlockPos)
					end

					return
				end

				local slot_15_5 = ove_0_6.GetBestPositionBlock(slot_15_3)

				if slot_15_5 then
					arg_15_0.pos.x = slot_15_5.position.x
					arg_15_0.pos.z = slot_15_5.position.y
					Evade.lastMovementBlockPos = vec3(slot_15_5.position.x, arg_15_0.pos.y, slot_15_5.position.y)

					if arg_15_1 then
						ove_0_9.MoveTo(slot_15_5.position)
					end
				else
					arg_15_0.process = false

					if arg_15_1 then
						-- block empty
					end

					return
				end

				return
			else
				Evade.lastBlockedUserMoveTo.isProcessed = true
			end
		end
	elseif Evade.isDodging and Evade.lastPosInfo and Evade.lastPosInfo.canMove and Evade.lastPosInfo.position.x ~= 0 and Evade.lastPosInfo.position.y ~= 0 then
		arg_15_0.process = false

		if arg_15_1 then
			-- block empty
		end
	end

	if not arg_15_0.block then
		Evade.lastIssueOrderGameTime = game.time * 1000
		Evade.lastIssueOrderTime = ove_0_6.TickCount()
		Evade.lastIssueOrderArgs = {
			block = false,
			order = arg_15_0.order,
			pos = arg_15_0.pos
		}

		if arg_15_0.order == 2 then
			Evade.lastMoveToPosition = pos
			Evade.lastMoveToServerPos = player.path.serverPos
		end

		if arg_15_0.order == 10 then
			Evade.lastStopPosition = player.path.serverPos
		end
	end
end

function Evade.draw()
	-- function 16
	ove_0_6.on_draw()
	ove_0_8.on_draw()
end

function Evade.mouse_down(arg_17_0)
	-- function 17
	if arg_17_0 == 1 then
		Evade.SpellDetector_class:keyup()
	end

	if arg_17_0 == 49 then
		Evade.SpellDetector_class:keyuptest()
	end
end

function Evade.create_object(arg_18_0, arg_18_1)
	-- function 18
	if arg_18_0 == TYPE_MINION then
		Evade.SpellDetector_class:create_minion(arg_18_1)
		ove_0_9.create_minion(arg_18_1)
	end

	if arg_18_0 == TYPE_PARTICLE then
		Evade.SpellDetector_class:create_particle(arg_18_1)
	end

	if arg_18_0 == TYPE_MISSILE then
		Evade.SpellDetector_class:create_missile(arg_18_1)
	end
end

function Evade.delete_object(arg_19_0, arg_19_1)
	-- function 19
	if arg_19_0 == TYPE_MINION then
		Evade.SpellDetector_class:delete_minion(arg_19_1)
	end

	if arg_19_0 == TYPE_MISSILE then
		Evade.SpellDetector_class:delete_missile(arg_19_1)
	end
end

function Evade.isDodgeDangerousEnabled()
	-- function 20
	if (ObjectCache.menuCache.cache.Enabled:get() or ObjectCache.menuCache.cache.CloseEnabled:get()) and ObjectCache.menuCache.cache.ActivateEvadeSpells:get() then
		return false
	end

	return true
end

local ove_0_12 = 0

function Evade.RecalculatePath()
	-- function 21
	if Evade.isDodging and Evade.lastPosInfo and ObjectCache.menuCache.cache.RecalculatePosition:get() and game.time > ove_0_12 then
		local slot_21_0 = (mousePos - player.pos):norm()
		local slot_21_1 = (to3D(Evade.lastPosInfo.position) - player.pos):norm()
		local slot_21_2 = ove_0_6.geometry.angleBetween(slot_21_0, slot_21_1)

		if (not Evade.lastPosInfo.recalculatedPath or slot_21_2 > 0.5) and ObjectCache.myHeroCache.isMoving then
			local slot_21_3 = player.path.endPos2D

			if slot_21_3:dist(Evade.lastPosInfo.position) < 10 then
				if not ove_0_6.fastEvadeMode then
					local slot_21_4 = ObjectCache.menuCache.cache.ExtraPingBuffer:get() + ObjectCache.gamePing * 2
					local slot_21_5 = ObjectCache.menuCache.cache.ExtraEvadeDistance:get()
					local slot_21_6, slot_21_7 = Evade.SpellDetector_table:GetLowestEvadeTime()
					local slot_21_8 = ove_0_6.InitPositionInfo(to2D(game.mousePos), slot_21_4, slot_21_5, Evade.lastPosInfo.position, slot_21_7)

					if slot_21_8.position.x > 0 and not slot_21_8.isDangerousPos and slot_21_8.posDangerCount == 0 then
						Evade.lastPosInfo = slot_21_8
						Evade.lastPosInfo.canMove = false

						Evade.CheckHeroInDanger()
						Evade.try_evade()

						ove_0_12 = game.time + 1

						--common.d_debug("RecalculatePath0 - " .. tostring(slot_21_8.posDangerCount) .. " - " .. tostring(slot_21_8.posDangerCount) .. " - " .. game.time)

						return
					end
				end

				if not Evade.lastPosInfo.recalculatedPath then
					local slot_21_9 = ove_0_6.CanHeroWalkToPos(slot_21_3, ObjectCache.myHeroCache.moveSpeed, 0, 0, false)

					if slot_21_9 and slot_21_9.posDangerCount > Evade.lastPosInfo.posDangerCount then
						Evade.lastPosInfo.recalculatedPath = true

						if ove_0_7.PE() then
							Evade.lastPosInfo = ove_0_10.SetAllUndodgeable()
							Evade.lastPosInfo.canMove = false
						elseif not ove_0_6.fastEvadeMode then
							local slot_21_10 = ove_0_6.GetBestPosition()

							if slot_21_10.posDangerCount < slot_21_9.posDangerCount then
								Evade.lastPosInfo = slot_21_10
								Evade.lastPosInfo.canMove = false

								Evade.CheckHeroInDanger()
								Evade.try_evade()

								ove_0_12 = game.time + 1

								common.d_debug("RecalculatePath1 - " .. tostring(slot_21_10.posDangerCount) .. " - " .. tostring(slot_21_10.posDangerCount) .. " - " .. game.time)
							end
						end
					end
				end
			end
		end
	end
end

function Evade.ContinueLastBlockedCommand()
	-- function 22
	if not ove_0_6.ShouldDodge() then
		return
	end
end

function Evade.CheckPosSafe(arg_23_0)
	-- function 23
	for iter_23_0, iter_23_1 in pairs(spells) do
		if ove_0_6.InSkillShot(arg_23_0, iter_23_1, ObjectCache.myHeroCache.boundingRadius + 1) then
			return false
		end
	end

	return true
end

function Evade.CheckHeroInDanger()
	-- function 24
	local slot_24_0 = false
	local slot_24_1 = false

	Evade.count_spell = 0

	local slot_24_2 = ove_0_6.TickCount()

	for iter_24_0, iter_24_1 in pairs(spells) do
		Evade.count_spell = Evade.count_spell + 1

		if iter_24_1.dangerlevel > 0 and (not ObjectCache.menuCache.cache.ComboEnabled:get() or iter_24_1.dangerlevel > 2) then
			if Evade.checkHit2 then
				if ove_0_6.InSkillShot2(ObjectCache.myHeroCache.serverPos2D, iter_24_1, ObjectCache.myHeroCache.boundingRadius + 1) then
					slot_24_0 = true
					Evade.evade_name = iter_24_1.info.spellName
					Evade.evade_spell = iter_24_1

					break
				end
			elseif ove_0_6.InSkillShot(ObjectCache.myHeroCache.serverPos2D, iter_24_1, ObjectCache.myHeroCache.boundingRadius + 1) then
				slot_24_0 = true
				Evade.evade_name = iter_24_1.info.spellName
				Evade.evade_spell = iter_24_1

				break
			end

			if ObjectCache.menuCache.cache.eed:get() and Evade.lastPosInfo and Evade.lastPosInfo.endTime and slot_24_2 < Evade.lastPosInfo.endTime then
				slot_24_0 = true

				break
			end
		end
	end

	if not Evade.isDodging and slot_24_0 then
		-- block empty
	elseif Evade.isDodging and not slot_24_0 then
		-- block empty
	end

	Evade.isDodging = slot_24_0
	Evade.isPosInfoInDanger = slot_24_1
end

local ove_0_13 = 0

function Evade.try_evade(arg_25_0)
	-- function 25
	if not ove_0_6.ShouldDodge() then
		Evade.isDodging = false

		return
	end

	if Evade.isDodging then
		if not Evade.lastPosInfo then
			local slot_25_0 = ove_0_6.GetBestPosition()
			local slot_25_1 = ove_0_6.TickCount()
			local slot_25_2 = ove_0_6.TickCount() - slot_25_1

			if slot_25_0 then
				Evade.lastPosInfo = ove_0_10.CompareLastMovePos(slot_25_0)

				local slot_25_3 = ObjectCache.myHeroCache.serverPos2DPing:dist(Evade.lastPosInfo.position) / ObjectCache.myHeroCache.moveSpeed

				Evade.lastPosInfo.endTime = ove_0_6.TickCount() + slot_25_3 * 1000 - 100
				Evade.lastPosInfo.canMove = false

				--common.d_debug("not ifo")
			end
		end

		if Evade.lastPosInfo and Evade.lastPosInfo.position.x ~= 0 and Evade.lastPosInfo.position.y ~= 0 then
			if Evade.lastEvadeOrderTime > ove_0_6.TickCount() then
				return
			end

			local slot_25_4 = Evade.lastPosInfo.position
			local slot_25_5 = player.path.endPos2D

			if slot_25_4:dist(slot_25_5) < player.boundingRadius then
				return
			end

			if not ObjectCache.menuCache.cache.delayEvade:get() or Evade.lastPosInfo.posDangerCount > 0 or ove_0_6.fastEvadeMode then
				ove_0_6.MovePos = slot_25_4
				Evade.lastPosInfo.canMove = true

				if ove_0_9.MoveTo(slot_25_4) then
					Evade.lastEvadeOrderPos = slot_25_4
					Evade.lastEvadeOrderTime = ove_0_6.TickCount() + 100

					--common.d_debug("FastEvade FastMode:" .. tostring(ove_0_6.fastEvadeMode) .. "SP:" .. tostring(arg_25_0) .. tostring(Evade.evade_name) .. " Level:" .. tostring(Evade.lastPosInfo.posDangerCount) .. " - " .. tostring(game.time))
				end
			else
				local slot_25_6 = ove_0_6.GetLastSafePos(to3D(slot_25_4))

				if not slot_25_6 then
					ove_0_6.MovePos = slot_25_4
					Evade.lastPosInfo.canMove = true

					if ove_0_9.MoveTo(slot_25_4) then
						Evade.lastEvadeOrderPos = slot_25_4
						Evade.lastEvadeOrderTime = ove_0_6.TickCount() + 100

						--common.d_debug("not safePos Evade FastMode:" .. tostring(ove_0_6.fastEvadeMode) .. tostring(Evade.evade_name) .. " evadeTime:" .. tostring(moveTime) .. " hitTime:" .. tostring(lowestHitTime) .. " IsSDOPDS:" .. tostring(arg_25_0))
					end

					return
				end

				local slot_25_7, slot_25_8 = Evade.SpellDetector_table:GetLowestHitTime()
				local slot_25_9 = slot_25_6:dist(player.pos) / ObjectCache.myHeroCache.moveSpeed + ObjectCache.menuCache.cache.delayEvadeTime:get() / 1000

				if slot_25_9 >= slot_25_7 / 1000 then
					ove_0_6.MovePos = slot_25_4
					Evade.lastPosInfo.canMove = true

					if ove_0_9.MoveTo(slot_25_4) then
						Evade.lastEvadeOrderPos = slot_25_4
						Evade.lastEvadeOrderTime = ove_0_6.TickCount() + 100

						--common.d_debug("safePos Evade FastMode:" .. tostring(ove_0_6.fastEvadeMode) .. tostring(Evade.evade_name) .. " evadeTime:" .. tostring(slot_25_9) .. " hitTime:" .. tostring(slot_25_7) .. " IsSDOPDS:" .. tostring(arg_25_0))
					end
				end
			end
		elseif not Evade.lastPosInfo or Evade.lastPosInfo.position.x > 0 then
			Evade.lastPosInfo = ove_0_10.SetAllUndodgeable()
			Evade.lastPosInfo.canMove = false

			--common.d_debug("RecalculatePath1 SetAllUndodgeable22")
		end
	elseif not Evade.isDodging and orb.core.can_action(true) and (player.path.isMoving or player.path.isActive) then
		local slot_25_10 = player.path.endPos2D

		if ove_0_6.CheckMovePath(slot_25_10, ObjectCache.menuCache.cache.ExtraAvoidDistance:get(), true) then
			--print("GetBestPositionBlock 547")

			local slot_25_11 = ove_0_6.GetBestPositionBlock(slot_25_10)

			if slot_25_11 and ove_0_9.MoveTo(slot_25_11.position) then
				orb.core.move_pos = to3D(slot_25_11.position)

				--print("GetBestPositionBlock move")
			end

			return
		end
	end
end

function Evade.CheckLastMoveTo()
	-- function 26
	if (ove_0_6.fastEvadeMode or ObjectCache.menuCache.cache.FastMovementBlock:get()) and not Evade.isDodging and Evade.lastIssueOrderArgs and Evade.lastIssueOrderArgs.order == 2 and game.time * 1000 - Evade.lastIssueOrderGameTime < 500 then
		Evade.issue_order(Evade.lastIssueOrderArgs, true)

		Evade.lastIssueOrderArgs = nil
	end
end

function Evade.SDOPDS()
	-- function 27
	ObjectCache.myHeroCache:UpdateInfo()

	if ObjectCache.menuCache.cache.Enabled:get() or ObjectCache.menuCache.cache.CloseEnabled:get() then
		Evade.lastPosInfo = ove_0_10.SetAllUndodgeable()

		ove_0_7.UseEvadeSpell()

		Evade.lastPosInfo.canMove = false

		return
	end

	if ove_0_6.CheckDangerousPos(ObjectCache.myHeroCache.serverPos2D, 10) or ove_0_6.CheckDangerousPos(ObjectCache.myHeroCache.serverPos2DExtra, 10) then
		if ove_0_7.PE() then
			Evade.lastPosInfo = ove_0_10.SetAllUndodgeable()
			Evade.lastPosInfo.canMove = false

			--common.d_debug("PE11")
		else
			Evade.CheckHeroInDanger()

			local slot_27_0 = ove_0_6.GetBestPosition(nil, true)
			local slot_27_1 = ove_0_6.TickCount()
			local slot_27_2 = ove_0_6.TickCount() - slot_27_1

			if slot_27_0 then
				Evade.lastPosInfo = ove_0_10.CompareLastMovePos(slot_27_0)

				local slot_27_3 = ObjectCache.myHeroCache.serverPos2DPing:dist(Evade.lastPosInfo.position) / ObjectCache.myHeroCache.moveSpeed

				Evade.lastPosInfo.endTime = ove_0_6.TickCount() + slot_27_3 * 1000 - 100
				Evade.lastPosInfo.canMove = false
			end

			if Evade.lastPosInfo.posDangerLevel and Evade.lastPosInfo.posDangerLevel > 0 then
				ove_0_7.UseEvadeSpell()
			end

			if ove_0_6.TickCount() > Evade.lastStopEvadeTime then
				Evade.lastEvadeOrderTime = 0

				Evade.try_evade(true)
			end

			ove_0_7.UseEvadeSpell()
		end
	else
		Evade.lastPosInfo = ove_0_10.SetAllDodgeable()
		Evade.lastPosInfo.canMove = false
	end
end

function Evade.isComboEnabled()
	-- function 28
	if ObjectCache.menuCache.cache.ComboEnabled:get() and player.health / player.maxHealth * 100 > ObjectCache.menuCache.cache.ComboEnabledhp:get() then
		return true
	end

	if player.charName == "MissFortune" then
		return player.activeSpell and player.activeSpell.slot == 3
	end

	return false
end

Evade.core = {}

function Evade.core.is_active()
	-- function 29
	return Evade.isDodging
end

function Evade.core.is_paused()
	-- function 30
	return Evade.time > os.clock()
end

function Evade.core.set_pause(arg_31_0)
	-- function 31
	return
end

function Evade.core.set_core_pause(arg_32_0)
	-- function 32
	Evade.time = os.clock() + arg_32_0
end

function Evade.core.set_server_pause()
	-- function 33
	Evade.time = os.clock() + 0.4
end

Evade.can_pre_spell = false
Evade.cb_self = {}

function Evade.register_spell(arg_34_0, arg_34_1)
	-- function 34
	if not Evade.cb_self.pre_spell then
		Evade.cb_self.pre_spell = {}
	end

	local slot_34_0 = Evade.cb_self.pre_spell

	slot_34_0[#slot_34_0 + 1] = {
		func = arg_34_0,
		args = arg_34_1
	}
	Evade.can_pre_spell = true
end

function Evade.invoke(arg_35_0, arg_35_1)
	-- function 35
	local slot_35_0 = Evade.cb_self[arg_35_0]

	if slot_35_0 then
		for iter_35_0 = 1, #slot_35_0 do
			slot_35_0[iter_35_0].func(arg_35_1)
		end
	end
end

function Evade.core.is_action_safe(arg_36_0, arg_36_1, arg_36_2)
	-- function 36
	local slot_36_0 = arg_36_0

	if arg_36_0.z then
		slot_36_0 = vec2(arg_36_0.x, arg_36_0.z)
	end

	local slot_36_1 = ove_0_6.CanHeroWalkToPosAll(slot_36_0, arg_36_1, arg_36_2 * 1000 + ObjectCache.gamePing, 0, true)

	return slot_36_1 and slot_36_1.posDangerLevel == 0 and not slot_36_1.isDangerousPos
end

function Evade.on_tick()
	-- function 37
	if player.isDead then
		return
	end

	orb.core.move_pos = nil

	ObjectCache.myHeroCache:UpdateInfo()
	Evade.CheckHeroInDanger()

	local slot_37_0 = ove_0_6.TickCount()
	local slot_37_1 = ObjectCache.menuCache.cache.TickLimiter:get()

	if ove_0_6.fastEvadeMode or slot_37_1 < slot_37_0 - Evade.lastTickCount or not Evade.lastPosInfo then
		if ove_0_6.TickCount() > Evade.lastStopEvadeTime then
			Evade.try_evade()

			Evade.lastTickCount = slot_37_0
		end

		Evade.checkHit2 = ObjectCache.menuCache.cache.checkHit2
	end

	ove_0_7.UseEvadeSpell()
	Evade.RecalculatePath()

	if slot_37_0 > Evade.lastStopEvadeTime then
		ove_0_7.spell_on_tick()
	end
end

Evade.IsPositionSafe = ove_0_7.IsPositionSafe
Evade.GetDamage = ove_0_7.GetDamage
Evade.GetEvadeSpell = ove_0_7.GetEvadeSpell
Evade.get_pos = ove_0_6.GetBestPositionDashPos

function Evade.get_GoodPosition(arg_38_0, arg_38_1, arg_38_2, arg_38_3, arg_38_4)
	-- function 38
	return
end

return Evade

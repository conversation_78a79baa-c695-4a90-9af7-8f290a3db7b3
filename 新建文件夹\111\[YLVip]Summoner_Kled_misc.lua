

local ove_0_10 = module.internal("pred")
local ove_0_11 = module.internal("orb")
local ove_0_12 = {}
local ove_0_13 = {
	ItemTitanicHydraCleave = true,
	ItemTiamatCleave = true
}

for iter_0_0 = 0, objManager.maxObjects - 1 do
	local ove_0_14 = objManager.get(iter_0_0)

	if ove_0_14 and ove_0_14.type == TYPE_TURRET and not ove_0_14.isDead and ove_0_14.team ~= TEAM_ALLY then
		ove_0_12[#ove_0_12 + 1] = ove_0_14
	end
end

local ove_0_15 = {}
local ove_0_16

local function ove_0_17(arg_5_0, arg_5_1, arg_5_2)
	if not ove_0_16 then
		function ove_0_16()
			for iter_6_0, iter_6_1 in pairs(ove_0_15) do
				if iter_6_0 <= os.clock() then
					for iter_6_2 = 1, #iter_6_1 do
						local slot_6_0 = iter_6_1[iter_6_2]

						if slot_6_0 and slot_6_0.func then
							slot_6_0.func(unpack(slot_6_0.args or {}))
						end
					end

					ove_0_15[iter_6_0] = nil
				end
			end
		end

		cb.add(cb.tick, ove_0_16)
	end

	local slot_5_0 = os.clock() + (arg_5_1 or 0)

	if ove_0_15[slot_5_0] then
		ove_0_15[slot_5_0][#ove_0_15[slot_5_0] + 1] = {
			func = arg_5_0,
			args = arg_5_2
		}
	else
		ove_0_15[slot_5_0] = {
			{
				func = arg_5_0,
				args = arg_5_2
			}
		}
	end
end

local function ove_0_18(arg_7_0, arg_7_1)
	if not arg_7_0 then
		return
	end

	for iter_7_0 = 0, arg_7_0.buffManager.count - 1 do
		local slot_7_0 = arg_7_0.buffManager:get(iter_7_0)

		if slot_7_0 and slot_7_0.valid and slot_7_0.endTime >= game.time and slot_7_0.stacks > 0 then
			if type(arg_7_1) == "number" and slot_7_0.type == arg_7_1 then
				return slot_7_0
			elseif type(arg_7_1) == "string" and slot_7_0.name:lower() == arg_7_1 then
				return slot_7_0
			end
		end
	end
end

local function ove_0_19(arg_8_0)
	local slot_8_0 = {
		"ward",
		"trink",
		"trap",
		"spear",
		"device",
		"room",
		"box",
		"plant",
		"poo",
		"barrel",
		"god",
		"feather"
	}

	for iter_8_0 = 1, #slot_8_0 do
		if arg_8_0 and arg_8_0.name:lower():find(slot_8_0[iter_8_0]) then
			return true
		end
	end
end

local function ove_0_20(arg_9_0)
	if arg_9_0 and not arg_9_0.isDead and arg_9_0.isTargetable and not arg_9_0.buff[BUFF_INVULNERABILITY] and not arg_9_0.buff.fioraw and arg_9_0.isVisible then
		return true
	end
end

local ove_0_21 = {
	[6630] = true,
	[6631] = true,
	[6029] = true
}

local function ove_0_22(arg_10_0)
	if not arg_10_0 or not ove_0_20(arg_10_0) then
		return false
	end

	for iter_10_0 = 6, 11 do
		local slot_10_0 = player:spellSlot(iter_10_0)

		if slot_10_0.isNotEmpty and slot_10_0.name and player:spellSlot(iter_10_0).state == 0 then
			if arg_10_0.pos2D:distSqr(player.pos2D) <= 57600 and (ove_0_11.core.is_attack_paused() or not ove_0_11.core.can_attack()) and ove_0_11.core.can_action() and ove_0_21[player:itemID(iter_10_0 - 6)] then
				if player:itemID(iter_10_0 - 6) == 6631 and arg_10_0 then
					player:castSpell("pos", iter_10_0, arg_10_0.path.serverPos)
				else
					player:castSpell("self", iter_10_0)
				end
			end

			if slot_10_0.name == "YoumusBlade" then
				player:castSpell("self", iter_10_0)
			elseif slot_10_0.name == "ItemSwordOfFeastAndFamine" then
				player:castSpell("obj", iter_10_0, arg_10_0)
			elseif slot_10_0.name == "BilgewaterCutlass" then
				player:castSpell("obj", iter_10_0, arg_10_0)
			end
		end
	end
end

local function ove_0_23(arg_11_0)
	local slot_11_0 = (player.baseAttackDamage + player.flatPhysicalDamageMod) * player.percentPhysicalDamageMod
	local slot_11_1 = math.max(0, (arg_11_0.armor - arg_11_0.bonusArmor + arg_11_0.bonusArmor * player.percentBonusArmorPenetration) * player.percentArmorPenetration - player.physicalLethality * (0.6 + 0.4 * player.levelRef / 18))

	return slot_11_0 * (1 - slot_11_1 / (100 + slot_11_1))
end

local function ove_0_24()
	if player:spellSlot(0).name == "KledQ" then
		return true
	else
		return false
	end
end

local function ove_0_25(arg_13_0)
	if player:spellSlot(0).level == 0 then
		return 0
	end

	if ove_0_24() then
		local slot_13_0 = {
			30,
			55,
			80,
			105,
			130
		}
		local slot_13_1 = player:spellSlot(0).level
		local slot_13_2 = player.flatPhysicalDamageMod * player.percentPhysicalDamageMod
		local slot_13_3 = math.max(0, (arg_13_0.armor - arg_13_0.bonusArmor + arg_13_0.bonusArmor * player.percentBonusArmorPenetration) * player.percentArmorPenetration - player.physicalLethality * (0.6 + 0.4 * player.levelRef / 18))
		local slot_13_4 = 1 - slot_13_3 / (100 + slot_13_3)

		return (slot_13_0[slot_13_1] + slot_13_2 * 0.6) * slot_13_4
	else
		local slot_13_5 = {
			35,
			50,
			65,
			80,
			95
		}
		local slot_13_6 = player:spellSlot(0).level
		local slot_13_7 = player.flatPhysicalDamageMod * player.percentPhysicalDamageMod
		local slot_13_8 = math.max(0, (arg_13_0.armor - arg_13_0.bonusArmor + arg_13_0.bonusArmor * player.percentBonusArmorPenetration) * player.percentArmorPenetration - player.physicalLethality * (0.6 + 0.4 * player.levelRef / 18))
		local slot_13_9 = 1 - slot_13_8 / (100 + slot_13_8)

		return (slot_13_5[slot_13_6] + slot_13_7 * 0.8) * slot_13_9
	end
end

local function ove_0_26(arg_14_0)
	if player:spellSlot(0).level == 0 then
		return 0
	end

	if ove_0_24() then
		local slot_14_0 = {
			90,
			165,
			240,
			315,
			390
		}
		local slot_14_1 = player:spellSlot(0).level
		local slot_14_2 = player.flatPhysicalDamageMod * player.percentPhysicalDamageMod
		local slot_14_3 = math.max(0, (arg_14_0.armor - arg_14_0.bonusArmor + arg_14_0.bonusArmor * player.percentBonusArmorPenetration) * player.percentArmorPenetration - player.physicalLethality * (0.6 + 0.4 * player.levelRef / 18))
		local slot_14_4 = 1 - slot_14_3 / (100 + slot_14_3)

		return (slot_14_0[slot_14_1] + slot_14_2 * 0.6) * slot_14_4
	else
		local slot_14_5 = {
			35,
			50,
			65,
			80,
			95
		}
		local slot_14_6 = player:spellSlot(0).level
		local slot_14_7 = player.flatPhysicalDamageMod * player.percentPhysicalDamageMod
		local slot_14_8 = math.max(0, (arg_14_0.armor - arg_14_0.bonusArmor + arg_14_0.bonusArmor * player.percentBonusArmorPenetration) * player.percentArmorPenetration - player.physicalLethality * (0.6 + 0.4 * player.levelRef / 18))
		local slot_14_9 = 1 - slot_14_8 / (100 + slot_14_8)

		return (slot_14_5[slot_14_6] + slot_14_7 * 0.8) * slot_14_9
	end
end

local function ove_0_27(arg_15_0)
	if player:spellSlot(2).level == 0 then
		return 0
	end

	local slot_15_0 = {
		30,
		60,
		85,
		110,
		135
	}
	local slot_15_1 = player:spellSlot(2).level
	local slot_15_2 = player.flatPhysicalDamageMod * player.percentPhysicalDamageMod
	local slot_15_3 = math.max(0, (arg_15_0.armor - arg_15_0.bonusArmor + arg_15_0.bonusArmor * player.percentBonusArmorPenetration) * player.percentArmorPenetration - player.physicalLethality * (0.6 + 0.4 * player.levelRef / 18))
	local slot_15_4 = 1 - slot_15_3 / (100 + slot_15_3)

	return (slot_15_0[slot_15_1] + slot_15_2 * 0.65) * slot_15_4
end

local function ove_0_28(arg_16_0)
	if player:spellSlot(3).level == 0 then
		return 0
	end

	local slot_16_0 = {
		200,
		300,
		400
	}
	local slot_16_1 = player:spellSlot(3).level
	local slot_16_2 = player.flatPhysicalDamageMod * player.percentPhysicalDamageMod
	local slot_16_3 = math.max(0, (arg_16_0.armor - arg_16_0.bonusArmor + arg_16_0.bonusArmor * player.percentBonusArmorPenetration) * player.percentArmorPenetration - player.physicalLethality * (0.6 + 0.4 * player.levelRef / 18))
	local slot_16_4 = 1 - slot_16_3 / (100 + slot_16_3)

	return (slot_16_0[slot_16_1] + slot_16_2 * 3) * slot_16_4
end

local function ove_0_29(arg_17_0)
	for iter_17_0 = 0, objManager.turrets.size[TEAM_ENEMY] - 1 do
		local slot_17_0 = objManager.turrets[TEAM_ENEMY][iter_17_0]

		if slot_17_0 and slot_17_0.type == TYPE_TURRET and not slot_17_0.isDead and slot_17_0.pos:distSqr(arg_17_0) <= 810000 then
			return true
		end
	end

	return false
end

local function ove_0_30(arg_18_0, arg_18_1)
	local slot_18_0 = 0

	for iter_18_0 = 0, objManager.enemies_n - 1 do
		local slot_18_1 = objManager.enemies[iter_18_0]

		if slot_18_1 and slot_18_1 ~= arg_18_0 and not slot_18_1.isDead and slot_18_1.isTargetable and slot_18_1.isVisible and slot_18_1.team ~= TEAM_ALLY and slot_18_1.pos:distSqr(arg_18_0) < arg_18_1^2 then
			slot_18_0 = slot_18_0 + 1
		end
	end

	return slot_18_0
end

return {
	havebuff = ove_0_18,
	EDmg = ove_0_27,
	QDmg = ove_0_26,
	QDmgK = ove_0_25,
	RDmg = ove_0_28,
	AADmg = ove_0_23,
	CountEnemiesNear = ove_0_30,
	UnderTurret = ove_0_29,
	DelayAction = ove_0_17,
	isValid = ove_0_20,
	WardName = ove_0_19,
	useitem = ove_0_22
}

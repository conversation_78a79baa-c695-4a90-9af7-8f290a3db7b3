local ove_0_5 = module.internal("TS")
local ove_0_6 = module.internal("pred")
local ove_0_7 = module.internal("orb")
local ove_0_8 = module.load(header.id,"orianna/r")
local ove_0_9 = table.insert
local ove_0_10
local ove_0_11 = 105625
local ove_0_12 = player:spellSlot(2)

local function ove_0_13(arg_1_0, arg_1_1, arg_1_2)
	-- print 1
	if ove_0_10.pos:distSqr(arg_1_1.pos) < ove_0_11 then
		ove_0_9(arg_1_0, arg_1_1)
	end
end

local function ove_0_14(arg_2_0, arg_2_1, arg_2_2)
	-- print 2
	if arg_2_2 > 3000 or arg_2_0.obj then
		return
	end

	ove_0_10 = arg_2_1

	local slot_2_0 = ove_0_5.loop(ove_0_13)

	if slot_2_0[1] and ove_0_8.get_action_state(slot_2_0) then
		arg_2_0.obj = arg_2_1
	end
end

local ove_0_15 = {}

local function ove_0_16(arg_3_0)
	-- print 3
	if ove_0_12.state == 0 then
		ove_0_15 = ove_0_5.loop_allies(ove_0_14)

		if ove_0_15.obj then
			return ove_0_15.obj
		end
	end
end

local function ove_0_17()
	-- print 4
	if player:castSpell("obj", 2, ove_0_15.obj) then
		ove_0_7.core.set_server_pause()
	end
end

return {
	get_action_state = ove_0_16,
	invoke_action = ove_0_17
}

local ove_0_20 = module.internal("orb")
local ove_0_22 = module.load(header.id, "SpellDatabase")
local ove_0_23 = module.load(header.id, "DashDB")
local ove_0_24 = module.internal("pred")
local ove_0_25 = {}
local ove_0_26 = {}
local ove_0_27 = {}
local ove_0_28 = {}
local ove_0_29 = {}
local ove_0_30 = {}
local ove_0_31 = {}
local ove_0_32
local ove_0_33 = {}
local ove_0_34 = {}
local ove_0_35
local ove_0_36 = math.sqrt

local function ove_0_37(arg_5_0, arg_5_1)
	-- print 5
	local slot_5_0
	local slot_5_1

	if arg_5_0.pos then
		slot_5_0 = arg_5_0.pos.y

		if arg_5_0.pos.z then
			slot_5_0 = arg_5_0.pos.z
		end
	end

	if arg_5_1.pos then
		slot_5_1 = arg_5_1.pos.y

		if arg_5_1.pos.z then
			slot_5_1 = arg_5_1.pos.z
		end
	end

	if arg_5_0.y then
		slot_5_0 = arg_5_0.y
	end

	if arg_5_1.y then
		slot_5_1 = arg_5_1.y
	end

	if arg_5_0.z then
		slot_5_0 = arg_5_0.z
	end

	if arg_5_1.z then
		slot_5_1 = arg_5_1.z
	end

	return ove_0_36((arg_5_0.x - arg_5_1.x)^2 + (slot_5_0 - slot_5_1)^2)
end

function ove_0_25.getDist2D(arg_6_0, arg_6_1)
	-- print 6
	return ove_0_36((arg_6_0.x - arg_6_1.x)^2 + (arg_6_0.y - arg_6_1.y)^2)
end

function ove_0_25.getDist(arg_7_0, arg_7_1)
	-- print 7
	local slot_7_0
	local slot_7_1

	if arg_7_0.pos then
		slot_7_0 = arg_7_0.pos.y

		if arg_7_0.pos.z then
			slot_7_0 = arg_7_0.pos.z
		end
	end

	if arg_7_1.pos then
		slot_7_1 = arg_7_1.pos.y

		if arg_7_1.pos.z then
			slot_7_1 = arg_7_1.pos.z
		end
	end

	if arg_7_0.y then
		slot_7_0 = arg_7_0.y
	end

	if arg_7_1.y then
		slot_7_1 = arg_7_1.y
	end

	if arg_7_0.z then
		slot_7_0 = arg_7_0.z
	end

	if arg_7_1.z then
		slot_7_1 = arg_7_1.z
	end

	return ove_0_36((arg_7_0.x - arg_7_1.x)^2 + (slot_7_0 - slot_7_1)^2)
end

local ove_0_38

local function ove_0_39(arg_8_0, arg_8_1)
	-- print 8
	return ove_0_36((arg_8_0.x - arg_8_1.x)^2 + (arg_8_0.y - arg_8_1.y)^2)
end

local function ove_0_40(arg_9_0, arg_9_1, arg_9_2, arg_9_3)
	-- print 9
	local slot_9_0 = arg_9_3 or 0
	local slot_9_1 = (arg_9_1 - arg_9_0):norm()
	local slot_9_2 = (arg_9_2 + slot_9_0) * slot_9_1:perp1()

	return {
		(arg_9_0 + slot_9_2 - slot_9_0 * slot_9_1):to2D(),
		(arg_9_0 - slot_9_2 - slot_9_0 * slot_9_1):to2D(),
		(arg_9_1 - slot_9_2 + slot_9_0 * slot_9_1):to2D(),
		(arg_9_1 + slot_9_2 + slot_9_0 * slot_9_1):to2D()
	}
end

local ove_0_41 = table.insert
local ove_0_42 = table.remove

function ove_0_25.InsideSpell(arg_10_0, arg_10_1)
	-- print 10
	if arg_10_0.contains and arg_10_0:contains(arg_10_1.path.serverPos2D) == true then
		return true
	end

	if arg_10_0.polygon and arg_10_0.polygon:Contains(arg_10_1.path.serverPos2D) == 1 then
		return true
	end
end

function ove_0_25.InsideSpellPos(arg_11_0, arg_11_1)
	-- print 11
	if arg_11_0.contains and arg_11_0:contains(arg_11_1) == true then
		return true
	end

	if arg_11_0.polygon and arg_11_0.polygon:Contains(arg_11_1) == 1 then
		return true
	end

	return false
end

function ove_0_25.GetWaypoints(arg_12_0)
	-- print 12
	local slot_12_0 = {
		arg_12_0.path.serverPos
	}
	local slot_12_1 = arg_12_0.path.point

	for iter_12_0 = arg_12_0.path.index, arg_12_0.path.count do
		slot_12_0[#slot_12_0 + 1] = slot_12_1[iter_12_0]
	end

	return slot_12_0
end

function ove_0_25.GetTotalAD(arg_13_0)
	-- print 13
	local slot_13_0 = arg_13_0 or player
	local slot_13_1 = slot_13_0.percentPhysicalDamageMod

	if slot_13_0 and slot_13_0.type ~= TYPE_HERO then
		slot_13_1 = 1
	end

	return (slot_13_0.baseAttackDamage + slot_13_0.flatPhysicalDamageMod) * slot_13_1
end

local ove_0_43 = 51262942686535096
local ove_0_44 = 1977
local ove_0_45

function encode(arg_14_0)
	-- print 14
	if not ove_0_45 then
		ove_0_45 = {}

		for iter_14_0 = 0, 127 do
			local slot_14_0 = -1

			repeat
				slot_14_0 = slot_14_0 + 2
			until slot_14_0 * (2 * iter_14_0 + 1) % 256 == 1

			ove_0_45[iter_14_0] = slot_14_0
		end
	end

	local slot_14_1 = ove_0_43
	local slot_14_2 = 16384 + ove_0_44

	return (arg_14_0:gsub(".", function(arg_15_0)
		-- print 15
		local slot_15_0 = slot_14_1 % 274877906944
		local slot_15_1 = (slot_14_1 - slot_15_0) / 274877906944
		local slot_15_2 = slot_15_1 % 128

		arg_15_0 = arg_15_0:byte()

		local slot_15_3 = (arg_15_0 * ove_0_45[slot_15_2] - (slot_15_1 - slot_15_2) / 128) % 256

		slot_14_1 = slot_15_0 * slot_14_2 + slot_15_1 + slot_15_3 + arg_15_0

		return ("%02x"):format(slot_15_3)
	end))
end

function ove_0_25.decode(arg_16_0)
	-- print 16
	local slot_16_0 = ove_0_43
	local slot_16_1 = 16384 + ove_0_44

	return arg_16_0:gsub("%x%x", function(arg_17_0)
		-- print 17
		local slot_17_0 = slot_16_0 % 274877906944
		local slot_17_1 = (slot_16_0 - slot_17_0) / 274877906944
		local slot_17_2 = slot_17_1 % 128

		arg_17_0 = tonumber(arg_17_0, 16)

		local slot_17_3 = (arg_17_0 + (slot_17_1 - slot_17_2) / 128) * (2 * slot_17_2 + 1) % 256

		slot_16_0 = slot_17_0 * slot_16_1 + slot_17_1 + arg_17_0 + slot_17_3

		return string.char(slot_17_3)
	end)
end

local ove_0_46 = {
	0.6,
	0.625,
	0.65,
	0.675,
	0.7
}
local ove_0_47 = {
	0.1,
	0.12,
	0.14,
	0.16,
	0.18
}
local ove_0_48 = {
	0.35,
	0.4,
	0.45,
	0.5,
	0.55
}
local ove_0_49 = {
	0.1,
	0.125,
	0.15,
	0.175,
	0.2
}
local ove_0_50 = {
	0.2,
	0.25,
	0.3,
	0.35,
	0.35
}
local ove_0_51 = {
	0.35,
	0.4,
	0.45,
	0.5,
	0.55
}
local ove_0_52 = {
	0.16,
	0.22,
	0.28,
	0.34,
	0.4
}
local ove_0_53 = {
	8,
	12,
	16,
	20,
	24
}
local ove_0_54 = {
	0.55,
	0.65,
	0.75
}
local ove_0_55 = {
	115,
	120,
	125,
	130,
	135,
	145,
	155,
	165,
	180,
	195,
	210,
	240,
	270,
	305,
	345,
	395,
	455,
	525
}

function ove_0_25.GetBonusAD(arg_18_0)
	-- print 18
	local slot_18_0 = arg_18_0 or player

	return (slot_18_0.baseAttackDamage + slot_18_0.flatPhysicalDamageMod) * slot_18_0.percentPhysicalDamageMod - slot_18_0.baseAttackDamage
end

local ove_0_56 = vec3(396, 182, 462)
local ove_0_57 = vec3(14340, 172, 14384)
local ove_0_58 = vec3(11950.813476563, -132.43249511719, 11707.814453125)
local ove_0_59 = vec3(925.58581542969, -132.25830078125, 1129.1278076172)

function ove_0_25.GetNexusPos(arg_19_0)
	-- print 19
	if arg_19_0.team == 100 then
		return ove_0_56
	end

	return ove_0_57
end

function ove_0_25.GetNexusPosARAM(arg_20_0)
	-- print 20
	if arg_20_0.team == 100 then
		return ove_0_59
	end

	return ove_0_58
end

function ove_0_25.GetOppositeNexusPos(arg_21_0)
	-- print 21
	if arg_21_0.team ~= 100 then
		return ove_0_56
	end

	return ove_0_57
end

function ove_0_25.GetOppositeNexusPosARAM(arg_22_0)
	-- print 22
	if arg_22_0.team ~= 100 then
		return ove_0_59
	end

	return ove_0_58
end

if game.mode ~= "ARAM" then
	ove_0_35 = ove_0_25.GetOppositeNexusPos(player)
else
	ove_0_35 = ove_0_25.GetOppositeNexusPosARAM(player)
end

function ove_0_25.GetTotalAP(arg_23_0)
	-- print 23
	local slot_23_0 = arg_23_0 or player

	return slot_23_0.flatMagicDamageMod * slot_23_0.percentMagicDamageMod
end

function ove_0_25.IsNoMana(arg_24_0)
	-- print 24
	if arg_24_0.charName == "Aatrox" or arg_24_0.charName == "Akali" or arg_24_0.charName == "DrMundo" or arg_24_0.charName == "Garen" or arg_24_0.charName == "Gnar" or arg_24_0.charName == "Katarina" or arg_24_0.charName == "Kennen" or arg_24_0.charName == "Kled" or arg_24_0.charName == "LeeSin" or arg_24_0.charName == "RekSai" or arg_24_0.charName == "Mordekaiser" or arg_24_0.charName == "Renekton" or arg_24_0.charName == "Rengar" or arg_24_0.charName == "Riven" or arg_24_0.charName == "Rumble" or arg_24_0.charName == "Sett" or arg_24_0.charName == "Shen" or arg_24_0.charName == "Shyvana" or arg_24_0.charName == "Tryndamere" or arg_24_0.charName == "Viego" or arg_24_0.charName == "Vladimir" or arg_24_0.charName == "Yasuo" or arg_24_0.charName == "Yone" or arg_24_0.charName == "Zac" or arg_24_0.charName == "Zed" then
		return true
	end

	return false
end

function ove_0_25.hasRune(arg_25_0, arg_25_1)
	-- print 25
	local slot_25_0 = arg_25_1 or player

	for iter_25_0 = 0, slot_25_0.rune.size - 1 do
		if slot_25_0.rune[iter_25_0].name:lower() == arg_25_0 then
			return true
		end
	end

	return false
end

local ove_0_60 = {}

for iter_0_0 = 0, objManager.enemies_n - 1 do
	local ove_0_61 = objManager.enemies[iter_0_0]

	if ove_0_61.type == TYPE_HERO and ove_0_22[ove_0_61.charName][2].Slot then
		-- block empty
	end

	ove_0_60[ove_0_61.networkID] = {
		cutdown = ove_0_25.hasRune("cutdown", ove_0_61),
		coupdegrace = ove_0_25.hasRune("coupdegrace", ove_0_61),
		darkharvest = ove_0_25.hasRune("darkharvest", ove_0_61),
		nullifying = ove_0_25.hasRune("nullifyingorb", ove_0_61)
	}
end

for iter_0_1 = 0, objManager.allies_n - 1 do
	local ove_0_62 = objManager.allies[iter_0_1]

	ove_0_60[ove_0_62.networkID] = {
		cutdown = ove_0_25.hasRune("cutdown", ove_0_62),
		coupdegrace = ove_0_25.hasRune("coupdegrace", ove_0_62),
		darkharvest = ove_0_25.hasRune("darkharvest", ove_0_62),
		nullifying = ove_0_25.hasRune("nullifyingorb", ove_0_62)
	}
end

local ove_0_63 = math.max
local ove_0_64 = math.atan
local ove_0_65 = math.min
local ove_0_66 = math.pow
local ove_0_67 = math.floor
local ove_0_68 = math.ceil
local ove_0_69 = math.abs
local ove_0_70 = math.deg
local ove_0_71 = string.find
local ove_0_72 = string.lower

function ove_0_25.GetLevel(arg_26_0)
	-- print 26
	if arg_26_0.levelRef >= 18 then
		return 18
	end

	return arg_26_0.levelRef
end

function ove_0_25.GetReductionPercent(arg_27_0, arg_27_1, arg_27_2)
	-- print 27
	local slot_27_0 = arg_27_2

	if arg_27_0.charName == "Alistar" and arg_27_0.buff.ferocioushowl then
		slot_27_0 = arg_27_2 * (1 - ove_0_54[arg_27_0:spellSlot(3).level])
	end

	if arg_27_1 == "AD" or arg_27_1 == "BOTH" then
		slot_27_0 = ove_0_63(0, arg_27_2 - arg_27_0.physicalShield)
	end

	if arg_27_1 == "AP" or arg_27_1 == "BOTH" then
		slot_27_0 = ove_0_63(0, arg_27_2 - arg_27_0.magicalShield)
	end

	if arg_27_0.charName == "Braum" and arg_27_0.buff.braumeshieldbuff then
		slot_27_0 = arg_27_2 * (1 - ove_0_51[arg_27_0:spellSlot(2).level])
	end

	if arg_27_0.charName == "Galio" and arg_27_0.buff.galiow and (arg_27_1 == "AP" or arg_27_1 == "BOTH") then
		slot_27_0 = arg_27_2 * (1 - (ove_0_50[arg_27_0:spellSlot(1).level] + 0.0005 * ove_0_25.GetTotalAP(arg_27_0) + 0.08 * arg_27_0.bonusSpellBlock))
	end

	if arg_27_0.charName == "Galio" and arg_27_0.buff.galiow and (arg_27_1 == "AD" or arg_27_1 == "BOTH") then
		slot_27_0 = arg_27_2 * (1 - (ove_0_49[arg_27_0:spellSlot(1).level] + 0.00025 * ove_0_25.GetTotalAP(arg_27_0) + 0.04 * arg_27_0.bonusSpellBlock))
	end

	if arg_27_0.charName == "Garen" and arg_27_0.buff.garenw then
		slot_27_0 = slot_27_0 * 0.7
	end

	if arg_27_0.charName == "Nilah" and arg_27_0.buff.nilahw then
		slot_27_0 = slot_27_0 * 0.75
	end

	if arg_27_0.charName == "Belveth" and arg_27_0.buff.belvethe then
		slot_27_0 = slot_27_0 * 0.3
	end

	if arg_27_0.charName == "Gragas" and arg_27_0.buff.gragaswself then
		slot_27_0 = slot_27_0 * (1 - (ove_0_47[arg_27_0:spellSlot(1).level] + 0.0004 * ove_0_25.GetTotalAP(arg_27_0)))
	end

	if arg_27_0.charName == "Irelia" and arg_27_0.buff.ireliawdefense and (arg_27_1 == "AP" or arg_27_1 == "BOTH") then
		slot_27_0 = slot_27_0 * (1 - (20 + 0.8823529411764706 * (arg_27_0.levelRef - 1)) / 100 + 0.00035 * ove_0_25.GetTotalAP(arg_27_0))
	end

	if arg_27_0.charName == "Irelia" and arg_27_0.buff.ireliawdefense and (arg_27_1 == "AD" or arg_27_1 == "BOTH") then
		slot_27_0 = slot_27_0 * (1 - (40 + 1.7647058823529411 * (arg_27_0.levelRef - 1)) / 100 + 0.0007 * ove_0_25.GetTotalAP(arg_27_0))
	end

	if arg_27_0.buff.voidstone and (arg_27_1 == "AP" or arg_27_1 == "BOTH") then
		slot_27_0 = slot_27_0 * 0.85
	end

	if arg_27_0.charName == "MasterYi" and arg_27_0.buff.meditate then
		slot_27_0 = slot_27_0 * (1 - ove_0_46[arg_27_0:spellSlot(1).level])
	end

	if arg_27_0.charName == "Warwick" and arg_27_0.buff.warwicke then
		slot_27_0 = slot_27_0 * (1 - ove_0_48[arg_27_0:spellSlot(2).level])
	end

	if arg_27_0.charName == "Yasuo" and arg_27_0.mana == arg_27_0.maxMana then
		slot_27_0 = slot_27_0 - ove_0_55[ove_0_25.GetLevel(arg_27_0)]
	end

	if arg_27_0.charName == "Tryndamere" and arg_27_0.buff.undyingrage then
		slot_27_0 = 0
	end

	if arg_27_0.buff.kindredrnodeathbuff then
		slot_27_0 = 0
	end

	if arg_27_0.buff[BUFF_INVULNERABILITY] then
		slot_27_0 = 0
	end

	if arg_27_0.charName == "XinZhao" and arg_27_0.buff.xinzhaorrangedimmunity and ove_0_39(arg_27_0.pos2D, player.pos2D) >= 450 then
		slot_27_0 = 0
	end

	if arg_27_0.charName == "Fiora" and arg_27_0.buff.fioraw then
		slot_27_0 = 0
	end

	if arg_27_0.buff["assets/perks/styles/sorcery/nullifyingorb/perknullifyingorbactive.lua"] and (arg_27_0.health - slot_27_0) / arg_27_0.maxHealth <= 0.3 and arg_27_0.levelRef > 1 then
		slot_27_0 = ove_0_63(0, slot_27_0 - (40 + 80 / (17 * (arg_27_0.levelRef - 1)) + ove_0_25.GetBonusAD(arg_27_0) * 0.15 + ove_0_25.GetTotalAP(arg_27_0) * 0.1))
	end

	if arg_27_0.charName == "Leona" and arg_27_0.buff.leonasolarbarrier then
		slot_27_0 = ove_0_63(0, slot_27_0 - ove_0_53[arg_27_0:spellSlot(1).level])
	end

	local slot_27_1 = slot_27_0 - arg_27_0.allShield

	if arg_27_0.buff[17] or arg_27_0.buff[4] then
		slot_27_1 = 0
	end

	if arg_27_0.charName == "Blitzcrank" and arg_27_0.buff.manabarriericon and not arg_27_0.buff.manabarriercooldown then
		slot_27_1 = slot_27_1 - arg_27_0.mana * 0.3
	end

	if player.buff.summonerexhaust then
		slot_27_1 = slot_27_1 * 0.6
	end

	if arg_27_0.buff["4644shield"] then
		slot_27_1 = slot_27_1 * 0.25
	end

	if player.buff.itemsmitechallenge and player.buff.itemsmitechallenge.source == arg_27_0 then
		slot_27_1 = slot_27_1 * 0.8
	end

	if ove_0_71(arg_27_0.name, "Baron") and player.buff.barontarget then
		slot_27_1 = slot_27_1 * 0.5
	end

	if ove_0_71(arg_27_0.name, "Dragon") then
		slot_27_1 = slot_27_1 * (1 - ove_0_25.DragonCount() * 0.07)
	end

	return ove_0_63(0, slot_27_1)
end

local ove_0_73 = 0

function ove_0_25.PhysicalReduction(arg_28_0, arg_28_1)
	-- print 28
	source = arg_28_1 or player

	if arg_28_0.armor == 0 then
		return 1
	end

	local slot_28_0 = (arg_28_0.bonusArmor * source.percentBonusArmorPenetration + arg_28_0.armor - arg_28_0.bonusArmor) * source.percentArmorPenetration

	if source.type ~= TYPE_HERO then
		slot_28_0 = (arg_28_0.bonusArmor * 1 + arg_28_0.armor - arg_28_0.bonusArmor) * 1
	end

	local slot_28_1 = source.type == TYPE_HERO and source.physicalLethality * (0.6 + 0.4 * source.levelRef / 18) or 0

	return slot_28_0 >= 0 and 100 / (100 + ove_0_63(slot_28_0 - slot_28_1, 0)) or 1
end

function ove_0_25.MagicReduction(arg_29_0, arg_29_1)
	-- print 29
	local slot_29_0 = arg_29_1 or player
	local slot_29_1 = ove_0_63(0, arg_29_0.spellBlock * slot_29_0.percentMagicPenetration - slot_29_0.flatMagicPenetration)

	return slot_29_1 >= 0 and 100 / (100 + slot_29_1) or 2 - 100 / (100 - slot_29_1)
end

function ove_0_25.DamageReduction(arg_30_0, arg_30_1, arg_30_2)
	-- print 30
	local slot_30_0

	slot_30_0 = arg_30_2 or player

	local slot_30_1 = 1

	if arg_30_0 == "AD" then
		-- block empty
	end

	if arg_30_0 == "AP" then
		-- block empty
	end

	return slot_30_1
end

function ove_0_25.CalculatePhysicalDamage(arg_31_0, arg_31_1, arg_31_2)
	-- print 31
	local slot_31_0 = arg_31_2 or player

	if arg_31_0 then
		return arg_31_1 * ove_0_25.PhysicalReduction(arg_31_0, slot_31_0) * ove_0_25.DamageReduction("AD", arg_31_0, slot_31_0)
	end

	return 0
end

function ove_0_25.CalculateMagicDamage(arg_32_0, arg_32_1, arg_32_2)
	-- print 32
	local slot_32_0 = arg_32_2 or player

	if arg_32_0 then
		return arg_32_1 * ove_0_25.MagicReduction(arg_32_0, slot_32_0) * ove_0_25.DamageReduction("AP", arg_32_0, slot_32_0)
	end

	return 0
end

function ove_0_25.AddPermashow(arg_33_0, arg_33_1, arg_33_2)
	-- print 33
	ove_0_21.AddLine(arg_33_0, arg_33_1, arg_33_2)
end

function ove_0_25.AddCustomText(arg_34_0, arg_34_1, arg_34_2)
	-- print 34
	ove_0_21.AddLineCustomText(arg_34_0, arg_34_1, arg_34_2)
end

function ove_0_25.AddPermashowCustom(arg_35_0, arg_35_1, arg_35_2)
	-- print 35
	ove_0_21.AddLineCustom(arg_35_0, arg_35_1, arg_35_2)
end

function ove_0_25.GetDashers()
	-- print 36
	return ove_0_28
end

function ove_0_25.GetAllyDashers()
	-- print 37
	return ove_0_29
end

function ove_0_25.InAARange(arg_38_0, arg_38_1)
	-- print 38
	return ove_0_37(arg_38_0.pos, arg_38_1.pos) <= arg_38_0.attackRange + arg_38_0.boundingRadius + arg_38_1.boundingRadius
end

function ove_0_25.GetLongCast()
	-- print 39
	return {
		caitlyn = {
			{
				menuslot = "R",
				buffname = "none",
				time = 1.375,
				slot = _R
			}
		},
		fiddlesticks = {
			{
				menuslot = "W",
				buffname = "none",
				time = 2,
				slot = _W
			},
			{
				menuslot = "R",
				buffname = "none",
				time = 1.5,
				slot = _R
			}
		},
		missfortune = {
			{
				menuslot = "R",
				buffname = "none",
				time = 3,
				slot = _R
			}
		},
		nunu = {
			{
				menuslot = "R",
				buffname = "none",
				time = 3,
				slot = _R
			}
		},
		quinn = {
			{
				menuslot = "R",
				buffname = "none",
				time = 2,
				slot = _R
			}
		},
		tahmkench = {
			{
				menuslot = "W",
				buffname = "none",
				slot = _W
			}
		},
		jhin = {
			{
				menuslot = "R",
				buffname = "none",
				time = 1,
				slot = _R
			}
		},
		yone = {
			{
				menuslot = "R",
				buffname = "none",
				time = 0.75,
				slot = _R
			}
		},
		yasuo = {
			{
				menuslot = "R",
				buffname = "none",
				time = 1,
				slot = _R
			}
		},
		urgot = {
			{
				menuslot = "R",
				buffname = "none",
				time = 1.5,
				slot = _R
			}
		},
		senna = {
			{
				menuslot = "R",
				buffname = "none",
				time = 1,
				slot = _R
			}
		},
		neeko = {
			{
				menuslot = "R",
				buffname = "none",
				time = 1.5,
				slot = _R
			}
		}
	}
end

for iter_0_2 = 0, objManager.enemies_n - 1 do
	local ove_0_74 = objManager.enemies[iter_0_2]

	if ove_0_74 then
		ove_0_26[ove_0_74.networkID] = {
			SpamClickCount3 = 0,
			SpamClickCount2 = 0,
			DashSoon = 0,
			DontCast2 = 0,
			DontCast = 0,
			SpamClickCount = 0,
			PathBank = {},
			NewPathTick = game.time,
			LastSpamClick = game.time,
			LastSpamClick2 = game.time,
			StopMoveTick = game.time,
			PosBank = {},
			AADetector = {},
			SpellDetect = {},
			BlinkInfo = {
				Comeout = 0,
				WindupEnd = 0
			}
		}
		ove_0_27[ove_0_74.networkID] = {
			Time = game.time,
			Pos = ove_0_74.pos,
			LastSeen = game.time
		}
	end
end

for iter_0_3 = 0, objManager.allies_n - 1 do
	local ove_0_75 = objManager.allies[iter_0_3]

	if ove_0_75 then
		ove_0_26[ove_0_75.networkID] = {
			SpamClickCount3 = 0,
			SpamClickCount2 = 0,
			DashSoon = 0,
			DontCast2 = 0,
			DontCast = 0,
			SpamClickCount = 0,
			PathBank = {},
			NewPathTick = game.time,
			LastSpamClick = game.time,
			LastSpamClick2 = game.time,
			StopMoveTick = game.time,
			PosBank = {},
			AADetector = {},
			SpellDetect = {},
			BlinkInfo = {
				Comeout = 0,
				WindupEnd = 0
			}
		}
		ove_0_27[ove_0_75.networkID] = {
			Time = game.time,
			Pos = ove_0_75.pos,
			LastSeen = game.time
		}
	end
end

function ove_0_25.GetEnemyHeroes()
	-- print 40
	local slot_40_0 = {}

	for iter_40_0 = 0, objManager.enemies_n - 1 do
		local slot_40_1 = objManager.enemies[iter_40_0]

		if slot_40_1 then
			slot_40_0[#slot_40_0 + 1] = slot_40_1
		end
	end

	return slot_40_0
end

function ove_0_25.ValidForDraw(arg_41_0)
	-- print 41
	return arg_41_0 and not arg_41_0.isDead and arg_41_0.isOnScreen and arg_41_0.isVisible
end

function ove_0_25.TargetedSpellsSamira()
	-- print 42
	return {
		ezreal = {
			{
				menuslot = "E",
				slot = 2
			}
		},
		annie = {
			{
				menuslot = "Q",
				slot = 0
			}
		},
		anivia = {
			{
				menuslot = "E",
				slot = 2
			}
		},
		brand = {
			{
				menuslot = "R",
				slot = 3
			}
		},
		caitlyn = {
			{
				menuslot = "P",
				slot = -1
			},
			{
				menuslot = "R",
				slot = 3
			}
		},
		sylas = {
			{
				menuslot = "R",
				slot = 3
			}
		},
		kassadin = {
			{
				menuslot = "Q",
				slot = 0
			}
		},
		cassiopeia = {
			{
				menuslot = "E",
				slot = 2
			}
		},
		gangplank = {
			{
				menuslot = "Q",
				slot = 0
			}
		},
		janna = {
			{
				menuslot = "W",
				slot = 1
			}
		},
		jhin = {
			{
				menuslot = "Q",
				slot = 0
			}
		},
		katarina = {
			{
				menuslot = "Q",
				slot = 0
			}
		},
		leblanc = {
			{
				menuslot = "Q",
				slot = 0
			}
		},
		nautilus = {
			{
				menuslot = "R",
				slot = 3
			}
		},
		lulu = {
			{
				menuslot = "W",
				slot = 1
			}
		},
		malphite = {
			{
				menuslot = "Q",
				slot = 0
			}
		},
		missfortune = {
			{
				menuslot = "Q",
				slot = 0
			}
		},
		nami = {
			{
				menuslot = "W",
				slot = 1
			}
		},
		ryze = {
			{
				menuslot = "E",
				slot = 2
			}
		},
		shaco = {
			{
				menuslot = "E",
				slot = 2
			}
		},
		syndra = {
			{
				menuslot = "R",
				slot = 3
			}
		},
		teemo = {
			{
				menuslot = "Q",
				slot = 0
			}
		},
		tristana = {
			{
				menuslot = "E",
				slot = 2
			},
			{
				menuslot = "R",
				slot = 3
			}
		},
		twistedfate = {
			{
				menuslot = "W",
				slot = 1
			}
		},
		twitch = {
			{
				menuslot = "E",
				slot = 2
			}
		},
		vayne = {
			{
				menuslot = "E",
				slot = 2
			}
		},
		veigar = {
			{
				menuslot = "R",
				slot = 3
			}
		},
		viktor = {
			{
				menuslot = "Q",
				slot = 0
			}
		}
	}
end

local ove_0_76 = {
	alistar = {
		{
			menuslot = "W",
			slot = 1
		}
	},
	ezreal = {
		{
			menuslot = "E",
			slot = 2
		}
	},
	annie = {
		{
			menuslot = "Q",
			slot = 0
		}
	},
	akali = {
		{
			menuslot = "E",
			slot = 2
		}
	},
	ekko = {
		{
			menuslot = "E",
			slot = 2
		}
	},
	anivia = {
		{
			menuslot = "E",
			slot = 2
		}
	},
	sett = {
		{
			menuslot = "R",
			slot = 3
		}
	},
	blitzcrank = {
		{
			menuslot = "E",
			slot = 2
		}
	},
	brand = {
		{
			menuslot = "E",
			slot = 2
		},
		{
			menuslot = "R",
			slot = 3
		}
	},
	caitlyn = {
		{
			menuslot = "R",
			slot = 3
		}
	},
	rell = {
		{
			menuslot = "W",
			slot = 1
		}
	},
	camille = {
		{
			menuslot = "Q",
			slot = 0
		},
		{
			menuslot = "R",
			slot = 3
		}
	},
	cassiopeia = {
		{
			menuslot = "E",
			slot = 2
		}
	},
	chogath = {
		{
			menuslot = "R",
			slot = 3
		}
	},
	darius = {
		{
			menuslot = "W",
			slot = 1
		},
		{
			menuslot = "R",
			slot = 3
		}
	},
	diana = {
		{
			menuslot = "E",
			slot = 2
		}
	},
	elise = {
		{
			menuslot = "Q",
			slot = 0
		}
	},
	evelynn = {
		{
			menuslot = "E",
			slot = 2
		}
	},
	fiddlesticks = {
		{
			menuslot = "Q",
			slot = 0
		}
	},
	fizz = {
		{
			menuslot = "Q",
			slot = 0
		}
	},
	gangplank = {
		{
			menuslot = "Q",
			slot = 0
		}
	},
	garen = {
		{
			menuslot = "Q",
			slot = 0
		},
		{
			menuslot = "R",
			slot = 3
		}
	},
	hecarim = {
		{
			menuslot = "E",
			slot = 2
		}
	},
	irelia = {
		{
			menuslot = "Q",
			slot = 0
		}
	},
	janna = {
		{
			menuslot = "W",
			slot = 1
		}
	},
	jarvaniv = {
		{
			menuslot = "R",
			slot = 3
		}
	},
	jax = {
		{
			menuslot = "Q",
			slot = 0
		}
	},
	kayn = {
		{
			menuslot = "R",
			slot = 3
		}
	},
	jayce = {
		{
			menuslot = "Q",
			slot = 0
		},
		{
			menuslot = "E",
			slot = 2
		}
	},
	jhin = {
		{
			menuslot = "Q",
			slot = 0
		}
	},
	kalista = {
		{
			menuslot = "E",
			slot = 2
		}
	},
	karma = {
		{
			menuslot = "W",
			slot = 1
		}
	},
	katarina = {
		{
			menuslot = "Q",
			slot = 0
		}
	},
	kennen = {
		{
			menuslot = "W",
			slot = 1
		}
	},
	khazix = {
		{
			menuslot = "Q",
			slot = 0
		}
	},
	sejuani = {
		{
			menuslot = "E",
			slot = 2
		}
	},
	leblanc = {
		{
			menuslot = "Q",
			slot = 0
		}
	},
	leesin = {
		{
			menuslot = "R",
			slot = 3
		},
		{
			menuslot = "Q",
			slot = 0
		}
	},
	leona = {
		{
			menuslot = "Q",
			slot = 0
		}
	},
	lissandra = {
		{
			menuslot = "R",
			slot = 3
		}
	},
	nautilus = {
		{
			menuslot = "R",
			slot = 3
		}
	},
	lulu = {
		{
			menuslot = "W",
			slot = 1
		},
		{
			menuslot = "E",
			slot = 2
		}
	},
	malphite = {
		{
			menuslot = "Q",
			slot = 0
		}
	},
	malzahar = {
		{
			menuslot = "E",
			slot = 2
		},
		{
			menuslot = "R",
			slot = 3
		}
	},
	maokai = {
		{
			menuslot = "W",
			slot = 1
		}
	},
	missfortune = {
		{
			menuslot = "Q",
			slot = 0
		}
	},
	morgana = {
		{
			menuslot = "R",
			slot = 3
		}
	},
	nidalee = {
		{
			menuslot = "Q",
			slot = 0
		}
	},
	nami = {
		{
			menuslot = "W",
			slot = 1
		}
	},
	nasus = {
		{
			menuslot = "Q",
			slot = 0
		},
		{
			menuslot = "W",
			slot = 1
		}
	},
	nocturne = {
		{
			menuslot = "E",
			slot = 2
		}
	},
	olaf = {
		{
			menuslot = "E",
			slot = 2
		}
	},
	pantheon = {
		{
			menuslot = "W",
			slot = 1
		}
	},
	poppy = {
		{
			menuslot = "E",
			slot = 2
		}
	},
	quinn = {
		{
			menuslot = "E",
			slot = 2
		}
	},
	masteryi = {
		{
			menuslot = "Q",
			slot = 0
		}
	},
	rammus = {
		{
			menuslot = "E",
			slot = 2
		}
	},
	renekton = {
		{
			menuslot = "W",
			slot = 1
		}
	},
	monkeyking = {
		{
			menuslot = "Q",
			slot = 0
		},
		{
			menuslot = "E",
			slot = 2
		}
	},
	mordekaiser = {
		{
			menuslot = "R",
			slot = 3
		}
	},
	yorick = {
		{
			menuslot = "Q",
			slot = 0
		}
	},
	rengar = {
		{
			menuslot = "Q",
			slot = 0
		}
	},
	ryze = {
		{
			menuslot = "W",
			slot = 1
		},
		{
			menuslot = "E",
			slot = 2
		}
	},
	shaco = {
		{
			menuslot = "E",
			slot = 2
		}
	},
	singed = {
		{
			menuslot = "E",
			slot = 2
		}
	},
	skarner = {
		{
			menuslot = "R",
			slot = 3
		}
	},
	sylas = {
		{
			menuslot = "R",
			slot = 3
		},
		{
			menuslot = "W",
			slot = 1
		}
	},
	syndra = {
		{
			menuslot = "R",
			slot = 3
		}
	},
	tahmkench = {
		{
			menuslot = "R",
			slot = 3
		}
	},
	talon = {
		{
			menuslot = "Q",
			slot = 0
		}
	},
	teemo = {
		{
			menuslot = "Q",
			slot = 0
		}
	},
	tristana = {
		{
			menuslot = "R",
			slot = 3
		}
	},
	trundle = {
		{
			menuslot = "Q",
			slot = 0
		},
		{
			menuslot = "R",
			slot = 3
		}
	},
	twistedfate = {
		{
			menuslot = "W",
			slot = 1
		}
	},
	twitch = {
		{
			menuslot = "E",
			slot = 2
		}
	},
	udyr = {
		{
			menuslot = "E",
			slot = 2
		}
	},
	vayne = {
		{
			menuslot = "E",
			slot = 2
		}
	},
	veigar = {
		{
			menuslot = "R",
			slot = 3
		}
	},
	vi = {
		{
			menuslot = "R",
			slot = 3
		}
	},
	vex = {
		{
			menuslot = "R",
			slot = 3
		}
	},
	viktor = {
		{
			menuslot = "Q",
			slot = 0
		}
	},
	vladimir = {
		{
			menuslot = "Q",
			slot = 0
		}
	},
	volibear = {
		{
			menuslot = "Q",
			slot = 0
		},
		{
			menuslot = "W",
			slot = 1
		}
	},
	warwick = {
		{
			menuslot = "Q",
			slot = 0
		}
	},
	xinzhao = {
		{
			menuslot = "Q",
			slot = 0
		},
		{
			menuslot = "E",
			slot = 2
		}
	},
	yasuo = {
		{
			menuslot = "E",
			slot = 2
		}
	},
	zed = {
		{
			menuslot = "R",
			slot = 3
		}
	}
}
local ove_0_77 = {
	hecarim = {
		{
			menuslot = "E",
			slot = 2,
			name = "hecarimrampattack"
		}
	},
	jayce = {
		{
			menuslot = "W",
			slot = 1,
			name = "JayceHyperChargeRangedAttack"
		}
	},
	nidalee = {
		{
			menuslot = "Q",
			slot = 0,
			name = "NidaleeTakedownAttack"
		}
	},
	pantheon = {
		{
			menuslot = "W",
			slot = 1,
			name = "pantheonw"
		}
	},
	renekton = {
		{
			menuslot = "W",
			slot = 1,
			name = "Execute"
		}
	},
	twistedfate = {
		{
			menuslot = "W",
			slot = 1,
			name = "PreAttack"
		}
	},
	viktor = {
		{
			menuslot = "Q",
			slot = 0,
			name = "ViktorQBuff"
		}
	},
	aatrox = {
		{
			menuslot = "Passive",
			slot = -1,
			name = "AatroxPassiveAttack"
		}
	},
	ashe = {
		{
			menuslot = "Q",
			slot = 0,
			name = "AsheQAttack"
		}
	},
	akali = {
		{
			menuslot = "Passive",
			slot = -1,
			name = "AkaliBasicAttackPassive"
		}
	},
	bard = {
		{
			menuslot = "Passive",
			slot = -1,
			name = "BardPSpiritMissile"
		}
	},
	blitzcrank = {
		{
			menuslot = "E",
			slot = 2,
			name = "blitzcranke"
		}
	},
	braum = {
		{
			menuslot = "Passive",
			slot = -1,
			name = "BraumBasicAttackPassiveOverride"
		}
	},
	caitlyn = {
		{
			menuslot = "Passive",
			slot = -1,
			name = "CaitlynHeadshotMissile"
		}
	},
	camille = {
		{
			menuslot = "E",
			slot = 2,
			name = "CamilleE"
		},
		{
			menuslot = "Q1",
			slot = 0,
			name = "CamilleQAttack"
		},
		{
			menuslot = "Q2",
			slot = 0,
			name = "CamilleQAttackEmpowered"
		}
	},
	chogath = {
		{
			menuslot = "E",
			slot = 2,
			name = "ChogathEAttack"
		}
	},
	drmundo = {
		{
			menuslot = "E",
			slot = 2,
			name = "DrMundoEAttack"
		}
	},
	darius = {
		{
			menuslot = "W",
			slot = 1,
			name = "DariusNoxianTacticsONHAttack"
		}
	},
	diana = {
		{
			menuslot = "Passive",
			slot = -1,
			name = "DianaBasicAttack3"
		}
	},
	draven = {
		{
			menuslot = "Q",
			slot = 0,
			name = "DravenSpinningAttack"
		}
	},
	fiora = {
		{
			menuslot = "Q",
			slot = 0,
			name = "FioraQ"
		},
		{
			menuslot = "E",
			slot = 2,
			name = "FioraEAttack"
		}
	},
	fizz = {
		{
			menuslot = "Q",
			slot = 0,
			name = "fizzQ"
		},
		{
			menuslot = "W",
			slot = 1,
			name = "FizzWBasicAttack"
		}
	},
	garen = {
		{
			menuslot = "Q",
			slot = 0,
			name = "GarenQAttack"
		}
	},
	gragas = {
		{
			menuslot = "W",
			slot = 1,
			name = "GragasW"
		}
	},
	irelia = {
		{
			menuslot = "Q",
			slot = 0,
			name = "IreliaQ"
		}
	},
	jarvaniv = {
		{
			menuslot = "Passive",
			slot = -1,
			name = "JarvanIVMartialCadenceAttack"
		}
	},
	jax = {
		{
			menuslot = "W",
			slot = 1,
			name = "JaxEmpowerTwo"
		}
	},
	jinx = {
		{
			menuslot = "Q",
			slot = 0,
			name = "JinxQAttack"
		}
	},
	kassadin = {
		{
			menuslot = "W",
			slot = 1,
			name = "KassadinBasicAttack3"
		}
	},
	kogmaw = {
		{
			menuslot = "W",
			slot = 1,
			name = "KogMawBioArcaneBarrageAttack"
		}
	},
	leona = {
		{
			menuslot = "Q",
			slot = 0,
			name = "LeonaShieldOfDaybreakAttack"
		}
	},
	malphite = {
		{
			menuslot = "W",
			slot = 1,
			name = "ObduracyAttack"
		}
	},
	masteryi = {
		{
			menuslot = "E",
			slot = 2,
			name = "---"
		}
	},
	missfortune = {
		{
			menuslot = "Passive",
			slot = -1,
			name = "MissFortunePassiveAttack"
		},
		{
			menuslot = "Q",
			slot = 0,
			name = "MissFortuneRicochetShot"
		}
	},
	nasus = {
		{
			menuslot = "Q",
			slot = 0,
			name = "NasusQAttack"
		}
	},
	nautilus = {
		{
			menuslot = "Passive",
			slot = -1,
			name = "NautilusRavageStrikeAttack"
		}
	},
	nocturne = {
		{
			menuslot = "Passive",
			slot = -1,
			name = "NocturneUmbraBladesAttack"
		}
	},
	quinn = {
		{
			menuslot = "Passive",
			slot = -1,
			name = "QuinnWEnhanced"
		}
	},
	reksai = {
		{
			menuslot = "Q",
			slot = 0,
			name = "RekSaiQAttack"
		}
	},
	rengar = {
		{
			menuslot = "Q",
			slot = 0,
			name = "RengarQAttack"
		}
	},
	sett = {
		{
			menuslot = "Q",
			slot = 0,
			name = "SettQAttack"
		}
	},
	shen = {
		{
			menuslot = "Q",
			slot = 0,
			name = "ShenQAttack"
		}
	},
	illaoi = {
		{
			menuslot = "W",
			slot = 1,
			name = "IllaoiWAttack"
		}
	},
	jhin = {
		{
			menuslot = "Passive",
			slot = -1,
			name = "JhinPassiveAttack"
		}
	},
	shyvana = {
		{
			menuslot = "Q",
			slot = 0,
			name = "ShyvanaDoubleAttackHit"
		}
	},
	skarner = {
		{
			menuslot = "E",
			slot = 2,
			name = "SkarnerPassiveAttack"
		}
	},
	sona = {
		{
			menuslot = "Passive",
			slot = -1,
			name = "PassiveAttack"
		}
	},
	tristana = {
		{
			menuslot = "E",
			slot = 2,
			name = "TristanaE"
		}
	},
	trundle = {
		{
			menuslot = "Q",
			slot = 0,
			name = "TrundleQ"
		}
	},
	udyr = {
		{
			menuslot = "E",
			slot = 2,
			name = "UdyrBearAttack"
		}
	},
	vayne = {
		{
			menuslot = "Q",
			slot = 0,
			name = "VayneTumbleAttack"
		}
	},
	vi = {
		{
			menuslot = "E",
			slot = 2,
			name = "ViEAttack"
		}
	},
	volibear = {
		{
			menuslot = "Q",
			slot = 0,
			name = "VolibearQAttack"
		},
		{
			menuslot = "W",
			slot = 1,
			name = "VolibearW"
		}
	},
	monkeyking = {
		{
			menuslot = "Q",
			slot = 0,
			name = "MonkeyKingQAttack"
		}
	},
	galio = {
		{
			menuslot = "Passive",
			slot = -1,
			name = "GalioPassiveAttack"
		}
	},
	xinzhao = {
		{
			menuslot = "Q",
			slot = 0,
			name = "XinZhaoQThrust"
		}
	},
	yorick = {
		{
			menuslot = "Q",
			slot = 0,
			name = "YorickQAttack"
		}
	},
	ziggs = {
		{
			menuslot = "Passive",
			slot = -1,
			name = "ZiggsPassiveAttack"
		}
	},
	gangplank = {
		{
			menuslot = "Q",
			slot = 0,
			name = "GangplankQ"
		}
	},
	elise = {
		{
			menuslot = "Q",
			slot = 0,
			name = "EliseSpiderQCast"
		}
	},
	evelynn = {
		{
			menuslot = "E",
			slot = 2,
			name = "EvelynnE"
		}
	},
	ezeal = {
		{
			menuslot = "Q",
			slot = 0,
			name = "EzrealQ"
		}
	},
	talon = {
		{
			menuslot = "Passive",
			slot = -1,
			name = "TalonPassiveAttack"
		},
		{
			menuslot = "Q",
			slot = 0,
			name = "TalonQ"
		}
	},
	maokai = {
		{
			menuslot = "Passive",
			slot = -1,
			name = "MaokaiPassiveAttack"
		}
	},
	katarina = {
		{
			menuslot = "E",
			slot = 2,
			name = "KatarinaQ"
		},
		{
			menuslot = "R",
			slot = 3,
			name = "KatarinaR"
		}
	},
	twitch = {
		{
			menuslot = "R",
			slot = 3,
			name = "TwitchSprayAndPrayAttack"
		}
	},
	warwick = {
		{
			menuslot = "Q",
			slot = 0,
			name = "WarwickQ"
		},
		{
			menuslot = "R",
			slot = 3,
			name = "WarwickR"
		}
	},
	yasuo = {
		{
			menuslot = "Q",
			slot = 0,
			name = "YasuoQ"
		}
	}
}
local ove_0_78 = {}
local ove_0_79 = {}

for iter_0_4 = 0, objManager.enemies_n - 1 do
	local ove_0_80 = objManager.enemies[iter_0_4]
	local ove_0_81 = string.lower(ove_0_80.charName)

	if ove_0_76[ove_0_81] then
		for iter_0_5 = 1, #ove_0_76[ove_0_81] do
			if not ove_0_78[ove_0_81] then
				ove_0_78[ove_0_81] = {}
			end

			local ove_0_82 = ove_0_76[ove_0_81][iter_0_5]

			table.insert(ove_0_78[ove_0_81], ove_0_82)
		end
	end
end

for iter_0_6 = 0, objManager.enemies_n - 1 do
	local ove_0_83 = objManager.enemies[iter_0_6]
	local ove_0_84 = string.lower(ove_0_83.charName)

	if ove_0_77[ove_0_84] then
		for iter_0_7 = 1, #ove_0_77[ove_0_84] do
			if not ove_0_79[ove_0_84] then
				ove_0_79[ove_0_84] = {}
			end

			local ove_0_85 = ove_0_77[ove_0_84][iter_0_7]

			table.insert(ove_0_79[ove_0_84], ove_0_85)
		end
	end
end

function ove_0_25.TargetedSpells()
	-- print 43
	return ove_0_78
end

local function ove_0_86(arg_44_0, arg_44_1)
	-- print 44
	local slot_44_0 = arg_44_0.buff[arg_44_1]

	if slot_44_0 and slot_44_0.endTime - game.time > 0 then
		return true, slot_44_0.endTime - game.time
	end

	return false, -1
end

local function ove_0_87(arg_45_0, arg_45_1)
	-- print 45
	local slot_45_0

	for iter_45_0, iter_45_1 in pairs(arg_45_0.buff) do
		if iter_45_1.valid and iter_45_1.endTime - game.time > 0 and iter_45_1.name:lower() == arg_45_1:lower() then
			local slot_45_1 = iter_45_1.endTime - game.time

			return true, slot_45_1
		end
	end

	return false, -1
end

local ove_0_88 = 0

function ove_0_25.GetHourglassTarget(arg_46_0, arg_46_1)
	-- print 46
	if ove_0_88 < game.time then
		local slot_46_0 = player.pos

		for iter_46_0 = 0, objManager.enemies_n - 1 do
			local slot_46_1 = objManager.enemies[iter_46_0]

			if slot_46_1 and (arg_46_1 == nil or slot_46_1 == arg_46_1) then
				local slot_46_2, slot_46_3 = ove_0_86(slot_46_1, "zhonyasringshield")

				if slot_46_2 then
					if player.charName == "Viktor" then
						if ove_0_37(slot_46_1.pos, player.pos) > 510 then
							local slot_46_4 = (slot_46_1.pos - player.pos):norm()

							slot_46_0 = player.pos + slot_46_4 * 510
						end

						if ove_0_37(slot_46_1.pos, player.pos) < 510 then
							slot_46_0 = slot_46_1.pos
						end
					end

					if player.charName == "Caitlyn" and arg_46_0.realDelay == 3 then
						return slot_46_1
					end

					local slot_46_5 = arg_46_0.realDelay + ove_0_37(slot_46_0, slot_46_1.pos) / arg_46_0.speed + network.latency

					if slot_46_5 >= slot_46_3 + 0.12 and slot_46_5 - (slot_46_3 + 0.12) < 0.1 and slot_46_5 - (slot_46_3 + 0.12) > 0 then
						return slot_46_1
					end
				end
			end
		end

		ove_0_88 = game.time + 0.1
	end

	return nil
end

local ove_0_89 = 0

function ove_0_25.GetChronoRevive(arg_47_0, arg_47_1)
	-- print 47
	if ove_0_89 < game.time then
		for iter_47_0 = 0, objManager.enemies_n - 1 do
			local slot_47_0 = objManager.enemies[iter_47_0]

			if slot_47_0 and (arg_47_1 == nil or slot_47_0 == arg_47_1) then
				local slot_47_1, slot_47_2 = ove_0_86(slot_47_0, "chronorevive")

				if slot_47_1 then
					local slot_47_3 = arg_47_0.realDelay + ove_0_39(slot_47_0.pos2D, player.pos2D) / arg_47_0.speed + network.latency
					local slot_47_4 = slot_47_2 + 0.15

					if player.charName == "Caitlyn" and arg_47_0.realDelay == 3 then
						return slot_47_0
					end

					if slot_47_4 <= slot_47_3 and slot_47_3 - slot_47_4 > 0 and slot_47_3 - slot_47_4 < 0.2 then
						return slot_47_0
					end
				end

				local slot_47_5, slot_47_6 = ove_0_86(slot_47_0, "lissandrarself")

				if slot_47_5 then
					local slot_47_7 = arg_47_0.realDelay + ove_0_39(slot_47_0.pos2D, player.pos2D) / arg_47_0.speed + network.latency
					local slot_47_8 = slot_47_6 + 0.15

					if player.charName == "Caitlyn" and arg_47_0.realDelay == 3 then
						return slot_47_0
					end

					if slot_47_8 <= slot_47_7 and slot_47_7 - slot_47_8 > 0 and slot_47_7 - slot_47_8 < 0.2 then
						return slot_47_0
					end
				end

				local slot_47_9, slot_47_10 = ove_0_86(slot_47_0, "lissandrarenemy2")

				if slot_47_9 then
					local slot_47_11 = arg_47_0.realDelay + ove_0_39(slot_47_0.pos2D, player.pos2D) / arg_47_0.speed + network.latency
					local slot_47_12 = slot_47_10 + 0.15

					if player.charName == "Caitlyn" and arg_47_0.realDelay == 3 then
						return slot_47_0
					end

					if slot_47_12 <= slot_47_11 and slot_47_11 - slot_47_12 > 0 and slot_47_11 - slot_47_12 < 0.2 then
						return slot_47_0
					end
				end

				local slot_47_13, slot_47_14 = ove_0_86(slot_47_0, "bardrstasis")

				if slot_47_13 then
					local slot_47_15 = arg_47_0.realDelay + ove_0_39(slot_47_0.pos2D, player.pos2D) / arg_47_0.speed + network.latency
					local slot_47_16 = slot_47_14 + 0.15

					if player.charName == "Caitlyn" and arg_47_0.realDelay == 3 then
						return slot_47_0
					end

					if slot_47_16 <= slot_47_15 and slot_47_15 - slot_47_16 > 0 and slot_47_15 - slot_47_16 < 0.2 then
						return slot_47_0
					end
				end
			end
		end

		ove_0_89 = game.time + 0.1
	end

	return nil
end

local ove_0_90 = 0

function ove_0_25.GetTeleportingTarget(arg_48_0, arg_48_1)
	-- print 48
	for iter_48_0, iter_48_1 in pairs(ove_0_31) do
		if iter_48_1.expireTime > game.time and (arg_48_1 == nil or iter_48_1.target == arg_48_1) then
			local slot_48_0 = iter_48_1.expireTime - game.time
			local slot_48_1 = arg_48_0.realDelay + ove_0_37(player.pos, iter_48_1.aimPos) / arg_48_0.speed

			if player.charName == "Caitlyn" and arg_48_0.realDelay == 3 then
				return iter_48_1
			end

			if slot_48_1 >= slot_48_0 + 0.1 and slot_48_0 + 0.1 >= slot_48_1 - 0.1 then
				return iter_48_1
			end
		end
	end

	return nil
end

local ove_0_91 = 0

function ove_0_25.GetRevivingTarget(arg_49_0, arg_49_1)
	-- print 49
	if ove_0_91 < game.time then
		for iter_49_0, iter_49_1 in pairs(ove_0_30) do
			if iter_49_1.target and (arg_49_1 == nil or iter_49_1.target == arg_49_1) then
				local slot_49_0 = arg_49_0.realDelay + ove_0_37(player.pos, iter_49_1.pos) / arg_49_0.speed

				if player.charName == "Caitlyn" and arg_49_0.realDelay == 3 then
					return iter_49_1
				end

				if slot_49_0 >= iter_49_1.expireTime - game.time and slot_49_0 - (iter_49_1.expireTime - game.time) < 0.1 then
					return iter_49_1
				end
			end

			if not iter_49_1.target and iter_49_1.spellPos then
				local slot_49_1 = arg_49_0.realDelay + ove_0_37(player.pos, iter_49_1.spellPos) / arg_49_0.speed

				if player.charName == "Caitlyn" and arg_49_0.realDelay == 3 then
					return iter_49_1
				end

				if slot_49_1 >= iter_49_1.expireTime - game.time and slot_49_1 - (iter_49_1.expireTime - game.time) < 0.1 then
					iter_49_1.pos = iter_49_1.spellPos

					return iter_49_1
				end
			end
		end

		ove_0_91 = game.time + 0.1
	end

	return nil
end

function ove_0_25.GetInterruptableSpells()
	-- print 50
	return {
		caitlyn = {
			{
				spellname = "caitlynaceinthehole",
				menuslot = "R",
				slot = 3,
				channelduration = 1
			}
		},
		fiddlesticks = {
			{
				spellname = "drainchannel",
				menuslot = "W",
				slot = 1,
				channelduration = 2
			},
			{
				spellname = "crowstorm",
				menuslot = "R",
				slot = 3,
				channelduration = 1.5
			}
		},
		irelia = {
			{
				spellname = "ireliaw",
				menuslot = "W",
				slot = 1,
				channelduration = 1.5
			}
		},
		janna = {
			{
				spellname = "reapthewhirlwind",
				menuslot = "R",
				slot = 3,
				channelduration = 3
			}
		},
		karthus = {
			{
				spellname = "karthusfallenone",
				menuslot = "R",
				slot = 3,
				channelduration = 3
			}
		},
		katarina = {
			{
				spellname = "katarinar",
				menuslot = "R",
				slot = 3,
				channelduration = 2.5
			}
		},
		lucian = {
			{
				spellname = "lucianr",
				menuslot = "R",
				slot = 3,
				channelduration = 3
			}
		},
		malzahar = {
			{
				spellname = "malzaharr",
				menuslot = "R",
				slot = 3,
				channelduration = 2.5
			}
		},
		masteryi = {
			{
				spellname = "meditate",
				menuslot = "W",
				slot = 1,
				channelduration = 4
			}
		},
		missfortune = {
			{
				spellname = "missfortunebullettime",
				menuslot = "R",
				slot = 3,
				channelduration = 3
			}
		},
		nunu = {
			{
				spellname = "nunur",
				menuslot = "R",
				slot = 3,
				channelduration = 3
			}
		},
		pantheon = {
			{
				spellname = "pantheonrjump",
				menuslot = "R",
				slot = 3,
				channelduration = 2
			},
			{
				spellname = "pantheonq",
				menuslot = "Q",
				slot = 0,
				channelduration = 4
			}
		},
		poppy = {
			{
				spellname = "poppyr",
				menuslot = "R",
				slot = 3,
				channelduration = 4
			}
		},
		quinn = {
			{
				spellname = "quinr",
				menuslot = "R",
				slot = 3,
				channelduration = 2
			}
		},
		shen = {
			{
				spellname = "shenr",
				menuslot = "R",
				slot = 3,
				channelduration = 3
			}
		},
		twistedfate = {
			{
				spellname = "gate",
				menuslot = "R",
				slot = 3,
				channelduration = 1.5
			}
		},
		varus = {
			{
				spellname = "varusq",
				menuslot = "Q",
				slot = 0,
				channelduration = 4
			}
		},
		velkoz = {
			{
				spellname = "velkozr",
				menuslot = "R",
				slot = 3,
				channelduration = 2.5
			}
		},
		warwick = {
			{
				spellname = "warwickrchannel",
				menuslot = "R",
				slot = 3,
				channelduration = 1.5
			}
		},
		xerath = {
			{
				spellname = "xeratharcanopulsechargeup",
				menuslot = "Q",
				slot = 0,
				channelduration = 3
			},
			{
				spellname = "xerathlocusofpower2",
				menuslot = "R",
				slot = 3,
				channelduration = 10
			}
		},
		zac = {
			{
				spellname = "zace",
				menuslot = "E",
				slot = 2,
				channelduration = 4
			}
		},
		jhin = {
			{
				spellname = "jhinr",
				menuslot = "R",
				slot = 3,
				channelduration = 10
			}
		},
		pyke = {
			{
				spellname = "pykeq",
				menuslot = "Q",
				slot = 0,
				channelduration = 3
			}
		},
		vi = {
			{
				spellname = "viq",
				menuslot = "Q",
				slot = 0,
				channelduration = 4
			}
		},
		samira = {
			{
				spellname = "samirar",
				menuslot = "R",
				slot = 3,
				channelduration = 2
			}
		}
	}
end

local function ove_0_92(arg_51_0, arg_51_1)
	-- print 51
	local slot_51_0 = 0
	local slot_51_1 = 1

	while arg_51_0 > 0 and arg_51_1 > 0 do
		if arg_51_0 % 2 == 1 and arg_51_1 % 2 == 1 then
			slot_51_0 = slot_51_0 + slot_51_1
		end

		slot_51_1 = slot_51_1 * 2
		arg_51_0 = ove_0_67(arg_51_0 / 2)
		arg_51_1 = ove_0_67(arg_51_1 / 2)
	end

	return slot_51_0
end

local function ove_0_93(arg_52_0)
	-- print 52
	local slot_52_0 = {}

	for iter_52_0 = 0, ove_0_66(2, #arg_52_0) - 1 do
		local slot_52_1 = {}

		for iter_52_1, iter_52_2 in ipairs(arg_52_0) do
			if ove_0_92(iter_52_0, ove_0_66(2, iter_52_1 - 1)) == 0 then
				slot_52_1[#slot_52_1 + 1] = iter_52_2
			end
		end

		slot_52_0[#slot_52_0 + 1] = slot_52_1
	end

	return slot_52_0
end

local function ove_0_94(arg_53_0, arg_53_1, arg_53_2)
	-- print 53
	local slot_53_0 = arg_53_2.x
	local slot_53_1 = arg_53_2.z or arg_53_2.y
	local slot_53_2 = arg_53_0.x
	local slot_53_3 = arg_53_0.z or arg_53_0.y
	local slot_53_4 = arg_53_1.x
	local slot_53_5 = arg_53_1.z or arg_53_1.y
	local slot_53_6 = ((slot_53_0 - slot_53_2) * (slot_53_4 - slot_53_2) + (slot_53_1 - slot_53_3) * (slot_53_5 - slot_53_3)) / ((slot_53_4 - slot_53_2)^2 + (slot_53_5 - slot_53_3)^2)
	local slot_53_7 = {
		x = slot_53_2 + slot_53_6 * (slot_53_4 - slot_53_2),
		y = slot_53_3 + slot_53_6 * (slot_53_5 - slot_53_3)
	}
	local slot_53_8 = slot_53_6 < 0 and 0 or slot_53_6 > 1 and 1 or slot_53_6
	local slot_53_9 = slot_53_8 == slot_53_6

	return slot_53_9 and slot_53_7 or {
		x = slot_53_2 + slot_53_8 * (slot_53_4 - slot_53_2),
		y = slot_53_3 + slot_53_8 * (slot_53_5 - slot_53_3)
	}, slot_53_7, slot_53_9
end

local ove_0_95 = math.cos
local ove_0_96 = mathf.sin
local ove_0_97 = mathf.PI

function ove_0_25.GetBestLineFarmViktor(arg_54_0, arg_54_1, arg_54_2)
	-- print 54
	local slot_54_0
	local slot_54_1
	local slot_54_2 = 0

	for iter_54_0, iter_54_1 in pairs(arg_54_0) do
		local slot_54_3 = 0
		local slot_54_4
		local slot_54_5
		local slot_54_6 = 0
		local slot_54_7 = iter_54_1.pos
		local slot_54_8 = vec3(slot_54_7.x, slot_54_7.y, slot_54_7.z)

		if ove_0_37(player.pos, iter_54_1.pos) > 510 then
			local slot_54_9 = (iter_54_1.pos - player.pos):norm()

			slot_54_8 = player.pos + slot_54_9 * 510
		end

		for iter_54_2 = 0, 2 * ove_0_97 + 0.35, 0.5 do
			local slot_54_10 = 0
			local slot_54_11 = vec3(slot_54_8.x + 650 * ove_0_95(iter_54_2), slot_54_8.y, slot_54_8.z - 650 * ove_0_96(iter_54_2))
			local slot_54_12 = vec3(slot_54_11.x, 0, slot_54_11.z)

			for iter_54_3, iter_54_4 in pairs(arg_54_0) do
				local slot_54_13 = iter_54_4
				local slot_54_14 = vec3(slot_54_13.x, 0, slot_54_13.z)
				local slot_54_15 = ove_0_94(vec3(slot_54_8.x, 0, slot_54_8.z), slot_54_12, slot_54_14)

				if arg_54_1 >= ove_0_39(vec2(slot_54_15.x, slot_54_15.y), vec2(slot_54_14.x, slot_54_14.z)) then
					slot_54_10 = slot_54_10 + 1
				end
			end

			if slot_54_6 <= slot_54_10 then
				slot_54_4 = slot_54_8
				slot_54_5 = slot_54_11
				slot_54_6 = slot_54_10
			end
		end

		if slot_54_2 < slot_54_6 then
			slot_54_0 = slot_54_4
			slot_54_1 = slot_54_5
			slot_54_2 = slot_54_6
		end
	end

	return slot_54_0, slot_54_1, slot_54_2
end

function ove_0_25.isTargetedSpellCollide(arg_55_0, arg_55_1)
	-- print 55
	local slot_55_0 = arg_55_0.pos

	if slot_55_0.z then
		slot_55_0 = vec2(arg_55_0.pos.x, arg_55_0.pos.z)
	end

	return ove_0_25.IsCollidingWindwall(player.pos2D, slot_55_0, arg_55_1)
end

function ove_0_25.isUnkillable(arg_56_0)
	-- print 56
	if ove_0_20.core.can_attack() == false or not ove_0_25.InAARange(player, arg_56_0) or ove_0_20.farm.predict_hp(arg_56_0, ove_0_20.utility.get_hit_time(player, arg_56_0) - os.clock()) <= 0 then
		return true
	end

	return false
end

function ove_0_25.GetBestLineTargetViktor(arg_57_0, arg_57_1, arg_57_2)
	-- print 57
	local slot_57_0 = 0
	local slot_57_1
	local slot_57_2
	local slot_57_3 = 0
	local slot_57_4 = arg_57_0.pos
	local slot_57_5 = vec3(slot_57_4.x, slot_57_4.y, slot_57_4.z)

	if ove_0_37(player.pos, arg_57_0.pos) <= 510 then
		for iter_57_0 = 0, 2 * ove_0_97 + 0.35, 0.5 do
			local slot_57_6 = 0
			local slot_57_7 = vec3(slot_57_5.x + 650 * ove_0_95(iter_57_0), slot_57_5.y, slot_57_5.z - 650 * ove_0_96(iter_57_0))
			local slot_57_8 = vec3(slot_57_7.x, 0, slot_57_7.z)

			for iter_57_1, iter_57_2 in pairs(arg_57_1) do
				if iter_57_2 ~= arg_57_0 and ove_0_25.IsValidTarget(iter_57_2) and ove_0_37(iter_57_2.pos, arg_57_0.pos) <= 650 then
					local slot_57_9 = iter_57_2
					local slot_57_10 = ove_0_24.linear.get_prediction(arg_57_2, slot_57_9, slot_57_5:to2D())
					local slot_57_11 = ove_0_94(vec3(slot_57_5.x, 0, slot_57_5.z), slot_57_8, vec3(slot_57_10.endPos.x, 0, slot_57_10.endPos.y))

					if ove_0_39(vec2(slot_57_10.endPos.x, slot_57_10.endPos.y), vec2(slot_57_11.x, slot_57_11.y)) <= arg_57_2.width then
						slot_57_6 = slot_57_6 + 1
					end
				end
			end

			if slot_57_3 <= slot_57_6 then
				slot_57_1 = slot_57_5
				slot_57_2 = slot_57_7
				slot_57_3 = slot_57_6
			end
		end
	end

	return slot_57_1, slot_57_2, slot_57_3
end

function ove_0_25.GetBestLineFarmQiyana(arg_58_0, arg_58_1, arg_58_2)
	-- print 58
	local slot_58_0 = 0
	local slot_58_1
	local slot_58_2 = {}

	for iter_58_0 = 1, #arg_58_0 do
		for iter_58_1 = 1, #arg_58_0 do
			ove_0_41(slot_58_2, (arg_58_0[iter_58_1].pos2D + arg_58_0[iter_58_0].pos2D) / 2)
		end
	end

	for iter_58_2, iter_58_3 in pairs(slot_58_2) do
		if iter_58_3:dist(player.pos2D) <= arg_58_2 * arg_58_2 then
			local slot_58_3 = player.pos2D + arg_58_2 * (iter_58_3 - player.pos2D):norm()
			local slot_58_4 = 0

			for iter_58_4, iter_58_5 in pairs(arg_58_0) do
				if iter_58_5.pos2D:dist(iter_58_3) <= 300 then
					local slot_58_5, slot_58_6, slot_58_7 = ove_0_94(player.pos2D, slot_58_3, iter_58_5.pos)

					if slot_58_7 and arg_58_1 >= vec2(slot_58_6.x, slot_58_6.y):dist(iter_58_5.pos2D) then
						slot_58_4 = slot_58_4 + 1
					end
				end

				if slot_58_0 <= slot_58_4 then
					slot_58_0 = slot_58_4
					slot_58_1 = slot_58_3
				end
			end
		end
	end

	return slot_58_1, slot_58_0
end

function ove_0_25.GetBestLineFarm(arg_59_0, arg_59_1, arg_59_2)
	-- print 59
	local slot_59_0 = 0
	local slot_59_1
	local slot_59_2 = {}

	for iter_59_0 = 1, #arg_59_0 do
		for iter_59_1 = 1, #arg_59_0 do
			ove_0_41(slot_59_2, (arg_59_0[iter_59_1].pos2D + arg_59_0[iter_59_0].pos2D) / 2)
		end
	end

	for iter_59_2, iter_59_3 in pairs(slot_59_2) do
		if iter_59_3:dist(player.pos2D) <= arg_59_2 * arg_59_2 then
			local slot_59_3 = player.pos2D + arg_59_2 * (iter_59_3 - player.pos2D):norm()
			local slot_59_4 = 0

			for iter_59_4, iter_59_5 in pairs(arg_59_0) do
				local slot_59_5, slot_59_6, slot_59_7 = ove_0_94(player.pos2D, slot_59_3, iter_59_5.pos)

				if slot_59_7 and arg_59_1 >= vec2(slot_59_6.x, slot_59_6.y):dist(iter_59_5.pos2D) then
					slot_59_4 = slot_59_4 + 1
				end
			end

			if slot_59_0 <= slot_59_4 then
				slot_59_0 = slot_59_4
				slot_59_1 = slot_59_3
			end
		end
	end

	return slot_59_1, slot_59_0
end

function ove_0_25.GetBestLineFarmOrianna(arg_60_0, arg_60_1, arg_60_2, arg_60_3)
	-- print 60
	local slot_60_0 = 0
	local slot_60_1
	local slot_60_2 = {}

	for iter_60_0 = 1, #arg_60_0 do
		for iter_60_1 = 1, #arg_60_0 do
			ove_0_41(slot_60_2, (arg_60_0[iter_60_1].pos2D + arg_60_0[iter_60_0].pos2D) / 2)
		end
	end

	for iter_60_2, iter_60_3 in pairs(slot_60_2) do
		if iter_60_3:dist(player.pos2D) <= arg_60_2 * arg_60_2 then
			local slot_60_3 = arg_60_3 + (arg_60_3:dist(iter_60_3) + 50) * (iter_60_3 - arg_60_3):norm()
			local slot_60_4 = 0

			for iter_60_4, iter_60_5 in pairs(arg_60_0) do
				local slot_60_5, slot_60_6, slot_60_7 = ove_0_94(arg_60_3, slot_60_3, iter_60_5.pos)

				if slot_60_7 and arg_60_1 >= vec2(slot_60_6.x, slot_60_6.y):dist(iter_60_5.pos2D) then
					slot_60_4 = slot_60_4 + 1
				end
			end

			if slot_60_0 <= slot_60_4 then
				slot_60_0 = slot_60_4
				slot_60_1 = slot_60_3
			end
		end
	end

	return slot_60_1, slot_60_0
end

function ove_0_25.GetPossibleTargetsCircle(arg_61_0, arg_61_1, arg_61_2)
	-- print 61
	local slot_61_0 = arg_61_1

	for iter_61_0 = 0, objManager.enemies_n - 1 do
		local slot_61_1 = objManager.enemies[iter_61_0]

		if slot_61_1 ~= slot_61_0 and ove_0_25.IsValidTarget(slot_61_1, false, false, true, false) and slot_61_1.pos:dist(player.pos) <= arg_61_0.range + 200 + arg_61_0.realWidth then
			local slot_61_2 = ove_0_24.circular.get_prediction(arg_61_0, slot_61_1)

			if slot_61_2 then
				ove_0_41(arg_61_2, {
					Position = slot_61_2.endPos,
					Unit = slot_61_1
				})
			end
		end
	end

	return arg_61_2
end



function ove_0_25.GetPossibleTargetsLine(arg_62_0, arg_62_1, arg_62_2)
	-- print 62
	local slot_62_0 = arg_62_1

	for iter_62_0 = 0, objManager.enemies_n - 1 do
		local slot_62_1 = objManager.enemies[iter_62_0]

		if slot_62_1 ~= slot_62_0 and ove_0_25.IsValidTarget(slot_62_1, false, false, true, false) and slot_62_1.pos:dist(player.pos) <= arg_62_0.range + 200 + arg_62_0.realWidth then
			local slot_62_2 = ove_0_24.linear.get_prediction(arg_62_0, slot_62_1)

			if slot_62_2 then
				ove_0_41(arg_62_2, {
					Position = slot_62_2.endPos,
					Unit = slot_62_1
				})
			end
		end
	end

	return arg_62_2
end

function ove_0_25.GetCandidates(arg_63_0, arg_63_1, arg_63_2, arg_63_3)
	-- print 63
	local slot_63_0 = (arg_63_0 + arg_63_1) / 2
	local slot_63_1, slot_63_2 = mathf.sect_circle_circle(arg_63_0, arg_63_2, slot_63_0, arg_63_0:dist(slot_63_0))

	if slot_63_1 and slot_63_2 then
		local slot_63_3 = arg_63_0 + arg_63_3 * (arg_63_1 - slot_63_1):norm()
		local slot_63_4 = arg_63_0 + arg_63_3 * (arg_63_1 - slot_63_2):norm()

		return {
			slot_63_3,
			slot_63_4
		}
	end

	return {}
end

local function ove_0_98(arg_64_0, arg_64_1, arg_64_2)
	-- print 64
	local slot_64_0 = arg_64_2.x
	local slot_64_1 = arg_64_2.z or arg_64_2.y
	local slot_64_2 = arg_64_0.x
	local slot_64_3 = arg_64_0.z or arg_64_0.y
	local slot_64_4 = arg_64_1.x
	local slot_64_5 = arg_64_1.z or arg_64_1.y
	local slot_64_6 = ((slot_64_0 - slot_64_2) * (slot_64_4 - slot_64_2) + (slot_64_1 - slot_64_3) * (slot_64_5 - slot_64_3)) / ((slot_64_4 - slot_64_2)^2 + (slot_64_5 - slot_64_3)^2)
	local slot_64_7 = {
		x = slot_64_2 + slot_64_6 * (slot_64_4 - slot_64_2),
		y = slot_64_3 + slot_64_6 * (slot_64_5 - slot_64_3)
	}
	local slot_64_8 = slot_64_6 < 0 and 0 or slot_64_6 > 1 and 1 or slot_64_6
	local slot_64_9 = slot_64_8 == slot_64_6

	return slot_64_9 and slot_64_7 or {
		x = slot_64_2 + slot_64_8 * (slot_64_4 - slot_64_2),
		y = slot_64_3 + slot_64_8 * (slot_64_5 - slot_64_3)
	}, slot_64_7, slot_64_9
end

function ove_0_25.Distance(arg_65_0, arg_65_1, arg_65_2, arg_65_3, arg_65_4)
	-- print 65
	local slot_65_0, slot_65_1, slot_65_2 = ove_0_98(arg_65_1, arg_65_2, arg_65_0)
	local slot_65_3 = vec2(slot_65_0.x, slot_65_0.y)

	if slot_65_2 or arg_65_3 == false then
		if arg_65_4 then
			return slot_65_3:distSqr(arg_65_0)
		else
			return slot_65_3:dist(arg_65_0)
		end
	end

	return math.huge
end

function ove_0_25.GetHitsCone(arg_66_0, arg_66_1, arg_66_2, arg_66_3)
	-- print 66
	local slot_66_0 = {}

	for iter_66_0, iter_66_1 in pairs(arg_66_3) do
		local slot_66_1 = iter_66_1.Position
		local slot_66_2 = arg_66_0:rotate(math.rad(-arg_66_2 / 2))
		local slot_66_3 = arg_66_0:rotate(math.rad(arg_66_2))

		if arg_66_1 > slot_66_1:dist(vec2(0, 0)) and slot_66_2:cross(slot_66_1) > 0 and slot_66_1:cross(slot_66_3) > 0 then
			ove_0_41(slot_66_0, iter_66_1)
		end
	end

	return slot_66_0
end

function ove_0_25.GetHitsQiyana(arg_67_0, arg_67_1, arg_67_2, arg_67_3)
	-- print 67
	local slot_67_0 = {}

	for iter_67_0, iter_67_1 in pairs(arg_67_3) do
		if ove_0_25.Distance(iter_67_1, arg_67_0, arg_67_1, true, true) <= arg_67_2 * arg_67_2 then
			ove_0_41(slot_67_0, iter_67_1)
		end
	end

	return slot_67_0
end

function ove_0_25.GetHits(arg_68_0, arg_68_1, arg_68_2, arg_68_3)
	-- print 68
	local slot_68_0 = {}

	for iter_68_0, iter_68_1 in pairs(arg_68_3) do
		if ove_0_25.Distance(iter_68_1, arg_68_0, arg_68_1, true, true) <= arg_68_2 * arg_68_2 then
			ove_0_41(slot_68_0, iter_68_1)
		end
	end

	return slot_68_0
end

local function ove_0_99(arg_69_0, arg_69_1, arg_69_2)
	-- print 69
	if ove_0_69(arg_69_2) < 1e-09 then
		arg_69_2 = 1e-09
	end

	return arg_69_2 >= ove_0_69(arg_69_0 - arg_69_1)
end

local function ove_0_100(arg_70_0)
	-- print 70
	if ove_0_99(arg_70_0.x, 0, 0) then
		if arg_70_0.y > 0 then
			return 90
		end

		if arg_70_0.y < 0 then
			return 270
		end

		return 0
	end

	local slot_70_0 = ove_0_69(ove_0_64(arg_70_0.y / arg_70_0.x))

	if arg_70_0.x < 0 then
		slot_70_0 = slot_70_0 + 180
	end

	if slot_70_0 < 0 then
		slot_70_0 = slot_70_0 + 360
	end

	return slot_70_0
end

local function ove_0_101(arg_71_0, arg_71_1)
	-- print 71
	local slot_71_0 = ove_0_100(arg_71_0) - ove_0_100(arg_71_1)

	if slot_71_0 < 0 then
		slot_71_0 = slot_71_0 + 360
	end

	if slot_71_0 > 180 then
		slot_71_0 = 360 - slot_71_0
	end

	return slot_71_0
end

local function ove_0_102(arg_72_0, arg_72_1)
	-- print 72
	for iter_72_0, iter_72_1 in ipairs(arg_72_0) do
		if iter_72_1 == arg_72_1 then
			return true
		end
	end

	return false
end

function ove_0_25.GetBestConeEnemy(arg_73_0, arg_73_1)
	-- print 73
	local slot_73_0 = ove_0_24.linear.get_prediction(arg_73_0, arg_73_1)

	if slot_73_0 then
		local slot_73_1 = {}

		ove_0_41(slot_73_1, {
			Position = slot_73_0.endPos,
			Unit = arg_73_1
		})

		if slot_73_0.endPos:dist(slot_73_0.startPos) <= arg_73_0.range then
			slot_73_1 = ove_0_25.GetPossibleTargetsLine(arg_73_0, arg_73_1, slot_73_1)
		end

		if #slot_73_1 > 1 then
			local slot_73_2 = {}

			for iter_73_0, iter_73_1 in pairs(slot_73_1) do
				iter_73_1.Position = iter_73_1.Position - player.pos2D
			end

			for iter_73_2 = 1, #slot_73_1 do
				for iter_73_3 = 1, #slot_73_1 do
					if iter_73_2 ~= iter_73_3 then
						local slot_73_3 = (slot_73_1[iter_73_2].Position + slot_73_1[iter_73_3].Position) * 0.5

						if not ove_0_102(slot_73_2, slot_73_3) then
							ove_0_41(slot_73_2, slot_73_3)
						end
					end
				end
			end

			local slot_73_4 = {}
			local slot_73_5 = -1
			local slot_73_6
			local slot_73_7 = {}

			for iter_73_4, iter_73_5 in pairs(slot_73_1) do
				ove_0_41(slot_73_7, iter_73_5.Position)
			end

			for iter_73_6, iter_73_7 in pairs(slot_73_2) do
				local slot_73_8 = ove_0_25.GetHitsCone(iter_73_7, arg_73_0.range, 30, slot_73_1)
				local slot_73_9 = #slot_73_8

				if slot_73_5 < slot_73_9 then
					local slot_73_10 = slot_73_8

					slot_73_6 = iter_73_7
					slot_73_5 = slot_73_9
				end
			end

			if slot_73_5 > 1 and player.pos2D:dist(slot_73_6) > 50 then
				return (slot_73_6 + player.pos2D):to3D()
			end
		end
	end

	return nil
end

local function ove_0_103(arg_74_0, arg_74_1, arg_74_2)
	-- print 74
	if ove_0_69(arg_74_2) < 1e-12 then
		arg_74_2 = 1e-09
	end

	return arg_74_2 >= ove_0_69(arg_74_0 - arg_74_1)
end

local function ove_0_104(arg_75_0)
	-- print 75
	local slot_75_0 = arg_75_0

	if ove_0_103(slot_75_0.x, 0, 0) then
		if slot_75_0.y > 0 then
			return 90
		end

		return slot_75_0.y < 0 and 270 or 0
	end

	local slot_75_1 = math.rad(ove_0_64(slot_75_0.y / slot_75_0.x))

	if slot_75_0.x < 0 then
		slot_75_1 = slot_75_1 + 180
	end

	if slot_75_1 < 0 then
		slot_75_1 = slot_75_1 + 360
	end

	return slot_75_1
end

local function ove_0_105(arg_76_0, arg_76_1)
	-- print 76
	local slot_76_0 = arg_76_0
	local slot_76_1 = arg_76_1
	local slot_76_2 = ove_0_104(slot_76_0) - ove_0_104(slot_76_1)

	if slot_76_2 < 0 then
		slot_76_2 = slot_76_2 + 360
	end

	if slot_76_2 > 180 then
		slot_76_2 = 360 - slot_76_2
	end

	return slot_76_2
end

function ove_0_25.GetBestLineEnemy(arg_77_0, arg_77_1)
	-- print 77
	local slot_77_0 = ove_0_24.linear.get_prediction(arg_77_0, arg_77_1)

	if slot_77_0 then
		local slot_77_1 = {}

		ove_0_41(slot_77_1, {
			Position = slot_77_0.endPos,
			Unit = arg_77_1
		})

		if slot_77_0.endPos:dist(slot_77_0.startPos) <= arg_77_0.range then
			slot_77_1 = ove_0_25.GetPossibleTargetsLine(arg_77_0, arg_77_1, slot_77_1)
		end

		if #slot_77_1 > 1 then
			local slot_77_2 = {}
			local slot_77_3 = {}

			for iter_77_0, iter_77_1 in pairs(slot_77_1) do
				local slot_77_4 = ove_0_25.GetCandidates(player.pos2D, iter_77_1.Position, arg_77_0.width, arg_77_0.range)

				for iter_77_2, iter_77_3 in pairs(slot_77_4) do
					ove_0_41(slot_77_2, iter_77_3)
				end

				ove_0_41(slot_77_3, iter_77_1.Position)
			end

			local slot_77_5 = -1
			local slot_77_6
			local slot_77_7 = {}

			for iter_77_4, iter_77_5 in pairs(slot_77_2) do
				if #ove_0_25.GetHits(player.pos2D, iter_77_5, arg_77_0.width, {
					slot_77_3[1]
				}) == 1 then
					local slot_77_8 = ove_0_25.GetHits(player.pos2D, iter_77_5, arg_77_0.width, slot_77_3)
					local slot_77_9 = #slot_77_8

					if slot_77_5 <= slot_77_9 then
						slot_77_5 = slot_77_9
						slot_77_6 = iter_77_5
						slot_77_7 = slot_77_8
					end
				end
			end

			if slot_77_5 > 1 then
				local slot_77_10 = -1
				local slot_77_11
				local slot_77_12

				for iter_77_6, iter_77_7 in pairs(slot_77_7) do
					for iter_77_8, iter_77_9 in pairs(slot_77_7) do
						local slot_77_13 = player.pos2D
						local slot_77_14 = slot_77_6
						local slot_77_15, slot_77_16, slot_77_17 = ove_0_98(slot_77_13, slot_77_14, iter_77_7)
						local slot_77_18 = vec2(slot_77_15.x, slot_77_15.y)
						local slot_77_19 = vec2(slot_77_16.x, slot_77_16.y)
						local slot_77_20, slot_77_21, slot_77_22 = ove_0_98(slot_77_13, slot_77_14, iter_77_9)
						local slot_77_23 = vec2(slot_77_20.x, slot_77_20.y)
						local slot_77_24 = vec2(slot_77_21.x, slot_77_21.y)
						local slot_77_25 = iter_77_7:distSqr(slot_77_19) + iter_77_9:distSqr(slot_77_24)
						local slot_77_26 = vec2(0, 0)
						local slot_77_27 = ove_0_105(slot_77_19 - iter_77_7, slot_77_24 - iter_77_9)

						if slot_77_10 <= slot_77_25 and slot_77_27 > 90 then
							slot_77_10 = slot_77_25
							slot_77_11 = iter_77_7
							slot_77_12 = iter_77_9
						end
					end
				end

				if slot_77_11 and slot_77_12 then
					return slot_77_5, ((slot_77_11 + slot_77_12) * 0.5):to3D(player.pos.y)
				end

				return nil
			end
		end
	end

	return nil
end

function ove_0_25.GetBestLineEnemyQiyana(arg_78_0, arg_78_1)
	-- print 78
	local slot_78_0 = ove_0_24.linear.get_prediction(arg_78_0, arg_78_1)

	if slot_78_0 then
		local slot_78_1 = {}

		ove_0_41(slot_78_1, {
			Position = slot_78_0.endPos,
			Unit = arg_78_1
		})

		if slot_78_0.endPos:dist(slot_78_0.startPos) <= arg_78_0.range then
			slot_78_1 = ove_0_25.GetPossibleTargetsLine(arg_78_0, arg_78_1, slot_78_1)
		end

		if #slot_78_1 > 1 then
			local slot_78_2 = {}
			local slot_78_3 = {}

			for iter_78_0, iter_78_1 in pairs(slot_78_1) do
				local slot_78_4 = ove_0_25.GetCandidates(player.pos2D, iter_78_1.Position, arg_78_0.width - 10, arg_78_0.range)

				for iter_78_2, iter_78_3 in pairs(slot_78_4) do
					ove_0_41(slot_78_2, iter_78_3)
				end

				ove_0_41(slot_78_3, iter_78_1.Position)
			end

			local slot_78_5 = -1
			local slot_78_6
			local slot_78_7 = {}

			for iter_78_4, iter_78_5 in pairs(slot_78_2) do
				if #ove_0_25.GetHitsQiyana(player.pos2D, iter_78_5, arg_78_0.width - 10, {
					slot_78_3[1]
				}) == 1 then
					local slot_78_8 = ove_0_25.GetHitsQiyana(player.pos2D, iter_78_5, arg_78_0.width - 10, slot_78_3)
					local slot_78_9 = #slot_78_8

					if slot_78_5 <= slot_78_9 then
						slot_78_5 = slot_78_9
						slot_78_6 = iter_78_5
						slot_78_7 = slot_78_8
					end
				end
			end

			if slot_78_5 > 1 then
				local slot_78_10 = -1
				local slot_78_11
				local slot_78_12

				for iter_78_6, iter_78_7 in pairs(slot_78_7) do
					for iter_78_8, iter_78_9 in pairs(slot_78_7) do
						if iter_78_9:dist(iter_78_7) <= 450 then
							local slot_78_13 = player.pos2D
							local slot_78_14 = slot_78_6
							local slot_78_15, slot_78_16, slot_78_17 = ove_0_98(slot_78_13, slot_78_14, iter_78_7)
							local slot_78_18 = vec2(slot_78_15.x, slot_78_15.y)
							local slot_78_19 = vec2(slot_78_16.x, slot_78_16.y)
							local slot_78_20, slot_78_21, slot_78_22 = ove_0_98(slot_78_13, slot_78_14, iter_78_9)
							local slot_78_23 = vec2(slot_78_20.x, slot_78_20.y)
							local slot_78_24 = vec2(slot_78_21.x, slot_78_21.y)
							local slot_78_25 = iter_78_7:distSqr(slot_78_19) + iter_78_9:distSqr(slot_78_24)
							local slot_78_26 = vec2(0, 0)
							local slot_78_27 = ove_0_105(slot_78_19 - iter_78_7, slot_78_24 - iter_78_9)

							if slot_78_10 <= slot_78_25 and slot_78_27 > 0 then
								slot_78_10 = slot_78_25
								slot_78_11 = iter_78_7
								slot_78_12 = iter_78_9
							end
						end
					end
				end

				if slot_78_11 and slot_78_12 then
					return slot_78_5, ((slot_78_11 + slot_78_12) * 0.5):to3D(player.pos.y)
				end

				return nil
			end
		end
	end

	return nil
end

function ove_0_25.GetBestCircularEnemy(arg_79_0, arg_79_1)
	-- print 79
	local slot_79_0 = ove_0_24.circular.get_prediction(arg_79_0, arg_79_1)

	if slot_79_0 then
		local slot_79_1 = {}

		ove_0_41(slot_79_1, {
			Position = slot_79_0.endPos,
			Unit = arg_79_1
		})

		if slot_79_0.endPos:dist(slot_79_0.startPos) <= arg_79_0.range then
			slot_79_1 = ove_0_25.GetPossibleTargetsCircle(arg_79_0, arg_79_1, slot_79_1)
		end

		while #slot_79_1 > 1 do
			local slot_79_2 = {}

			for iter_79_0, iter_79_1 in pairs(slot_79_1) do
				ove_0_41(slot_79_2, iter_79_1.Position)
			end

			local slot_79_3 = vec2.array(#slot_79_2)
			local slot_79_4 = 0

			for iter_79_2, iter_79_3 in pairs(slot_79_2) do
				slot_79_3[slot_79_4].x = iter_79_3.x
				slot_79_3[slot_79_4].y = iter_79_3.y
				slot_79_4 = slot_79_4 + 1
			end

			local slot_79_5, slot_79_6 = mathf.mec(slot_79_3, #slot_79_2)

			if slot_79_6 <= arg_79_0.realWidth - 50 and slot_79_5:dist(player.pos:to2D()) <= arg_79_0.range then
				return slot_79_5, #slot_79_1
			end

			local slot_79_7 = -1
			local slot_79_8 = 1

			for iter_79_4 = 2, #slot_79_1 do
				local slot_79_9 = slot_79_1[iter_79_4].Position:dist(slot_79_1[1].Position)

				if slot_79_7 < slot_79_9 or slot_79_7 == -1 then
					slot_79_8 = iter_79_4
					slot_79_7 = slot_79_9
				end
			end

			ove_0_42(slot_79_1, slot_79_8)
		end

		return nil
	end
end

function ove_0_25.GetBestCircularFarm(arg_80_0, arg_80_1, arg_80_2, arg_80_3, arg_80_4)
	-- print 80
	local slot_80_0
	local slot_80_1 = 0

	if #arg_80_0 == 0 then
		return nil, 0
	end

	if #arg_80_0 <= 9 then
		local slot_80_2 = ove_0_93(arg_80_0)

		for iter_80_0, iter_80_1 in ipairs(slot_80_2) do
			if #iter_80_1 > 0 then
				local slot_80_3 = vec3.array(#iter_80_1)
				local slot_80_4 = 0

				for iter_80_2, iter_80_3 in pairs(iter_80_1) do
					slot_80_3[slot_80_4].x = iter_80_3.x
					slot_80_3[slot_80_4].y = iter_80_3.y
					slot_80_3[slot_80_4].z = iter_80_3.z
					slot_80_4 = slot_80_4 + 1
				end

				local slot_80_5, slot_80_6 = mathf.mec(slot_80_3, #iter_80_1)

				if slot_80_6 <= arg_80_1 and arg_80_2 >= slot_80_5:dist(arg_80_3) then
					slot_80_1 = #ove_0_25.CountMinions(slot_80_5, arg_80_1, TEAM_ENEMY)

					if arg_80_4 <= slot_80_1 then
						return slot_80_5, slot_80_1
					end
				end
			end
		end
	else
		for iter_80_4, iter_80_5 in ipairs(arg_80_0) do
			if arg_80_2 >= iter_80_5.pos:dist(player.pos) then
				local slot_80_7 = #ove_0_25.CountMinions(iter_80_5.pos, arg_80_1, TEAM_ENEMY)

				if arg_80_4 <= slot_80_7 and slot_80_1 <= slot_80_7 then
					slot_80_0 = iter_80_5.pos
					slot_80_1 = slot_80_7
				end
			end
		end
	end

	return slot_80_0, slot_80_1
end

function ove_0_25.CalculateAADamageNoBonus(arg_81_0, arg_81_1, arg_81_2)
	-- print 81
	local slot_81_0 = arg_81_1 or player
	local slot_81_1 = arg_81_2 or 1

	if slot_81_1 == 0 then
		return 0
	end

	if arg_81_0 and slot_81_0.baseAttackDamage and (arg_81_0.type == TYPE_MINION or arg_81_0.type == TYPE_HERO) then
		local slot_81_2 = 0
		local slot_81_3 = 1

		if slot_81_0.charName == "Kalista" then
			slot_81_3 = 0.9
		end

		if ove_0_60[slot_81_0.networkID] and arg_81_0.type == TYPE_HERO then
			if ove_0_60[slot_81_0.networkID].cutdown and arg_81_0.maxHealth - slot_81_0.maxHealth > 0 then
				local slot_81_4 = (ove_0_65(1, (arg_81_0.maxHealth - player.maxHealth) / player.maxHealth) - 0.1) * 1 / 9 + 1.05

				return ove_0_25.GetReductionPercent(arg_81_0, "AD", ove_0_25.GetTotalAD(slot_81_0) * slot_81_3 * ove_0_25.PhysicalReduction(arg_81_0, slot_81_0) * slot_81_4 * slot_81_1 + slot_81_2)
			end

			if ove_0_60[slot_81_0.networkID].coupdegrace and ove_0_25.GetHPPercent(arg_81_0) < 40 then
				return ove_0_25.GetReductionPercent(arg_81_0, "AD", (ove_0_25.GetTotalAD(slot_81_0) * slot_81_3 * ove_0_25.PhysicalReduction(arg_81_0, slot_81_0) * slot_81_1 + slot_81_2) * 1.08)
			end
		end

		return ove_0_25.GetReductionPercent(arg_81_0, "AD", ove_0_25.GetTotalAD(slot_81_0) * slot_81_3 * ove_0_25.PhysicalReduction(arg_81_0, slot_81_0) * slot_81_1 + slot_81_2)
	end

	return 0
end

function ove_0_25.DragonCount()
	-- print 82
	local slot_82_0 = 0

	if player.buff.srx_dragonbuffocean then
		slot_82_0 = slot_82_0 + 1
	end

	if player.buff.srx_dragonbuffcloud then
		slot_82_0 = slot_82_0 + 1
	end

	if player.buff.srx_dragonbuffinfernal then
		slot_82_0 = slot_82_0 + 1
	end

	if player.buff.srx_dragonbuffmountain then
		slot_82_0 = slot_82_0 + 1
	end

	if player.buff.srx_dragonbuffchemtech then
		slot_82_0 = slot_82_0 + 1
	end

	return slot_82_0
end

local ove_0_106 = 0
local ove_0_107 = false
local ove_0_108 = false
local ove_0_109 = false
local ove_0_110 = false
local ove_0_111 = false
local ove_0_112 = false
local ove_0_113 = false
local ove_0_114 = false
local ove_0_115 = false
local ove_0_116 = false
local ove_0_117 = false
local ove_0_118 = false
local ove_0_119 = false

function ove_0_25.GetBonusDamage(arg_83_0, arg_83_1)
	-- print 83
	local slot_83_0 = 0

	if ove_0_107 and player.buff["6672buff"] and player.buff["6672buff"].stacks2 == 2 and game.time - player.buff["6672buff"].startTime > 0.1 then
		slot_83_0 = slot_83_0 + 60 + ove_0_25.GetBonusAD() * 0.45
	end

	if ove_0_108 and player.buff.itemstatikshankcharge and player.buff.itemstatikshankcharge.stacks2 == 100 then
		slot_83_0 = slot_83_0 + 80 * ove_0_25.MagicReduction(arg_83_0, arg_83_1)
	end

	if ove_0_109 and player.buff.itemstatikshankcharge and player.buff.itemstatikshankcharge.stacks2 == 100 then
		slot_83_0 = slot_83_0 + 120 * ove_0_25.MagicReduction(arg_83_0, arg_83_1)
	end

	if ove_0_110 and player.buff.itemstatikshankcharge and player.buff.itemstatikshankcharge.stacks2 == 100 then
		slot_83_0 = slot_83_0 + 120 * ove_0_25.MagicReduction(arg_83_0, arg_83_1)
	end

	if ove_0_113 then
		slot_83_0 = slot_83_0 + (15 + ove_0_25.GetTotalAP() * 0.2) * ove_0_25.MagicReduction(arg_83_0, arg_83_1)
	end

	if hasWits then
		local slot_83_1 = 0

		if player.levelRef < 30 then
			slot_83_1 = 80 * ove_0_25.MagicReduction(arg_83_0, arg_83_1)
		end

		if player.levelRef == 17 then
			slot_83_1 = 78.75 * ove_0_25.MagicReduction(arg_83_0, arg_83_1)
		end

		if player.levelRef == 16 then
			slot_83_1 = 77.5 * ove_0_25.MagicReduction(arg_83_0, arg_83_1)
		end

		if player.levelRef == 15 then
			slot_83_1 = 76.25 * ove_0_25.MagicReduction(arg_83_0, arg_83_1)
		end

		if player.levelRef == 14 then
			slot_83_1 = 75 * ove_0_25.MagicReduction(arg_83_0, arg_83_1)
		end

		if player.levelRef == 13 then
			slot_83_1 = 65 * ove_0_25.MagicReduction(arg_83_0, arg_83_1)
		end

		if player.levelRef == 12 then
			slot_83_1 = 55 * ove_0_25.MagicReduction(arg_83_0, arg_83_1)
		end

		if player.levelRef == 11 then
			slot_83_1 = 45 * ove_0_25.MagicReduction(arg_83_0, arg_83_1)
		end

		if player.levelRef == 10 then
			slot_83_1 = 35 * ove_0_25.MagicReduction(arg_83_0, arg_83_1)
		end

		if player.levelRef == 9 then
			slot_83_1 = 25 * ove_0_25.MagicReduction(arg_83_0, arg_83_1)
		end

		if player.levelRef < 9 then
			slot_83_1 = 15 * ove_0_25.MagicReduction(arg_83_0, arg_83_1)
		end

		slot_83_0 = slot_83_0 + slot_83_1
	end

	if ove_0_111 then
		if arg_83_0.buff.item3153botrkstacks and arg_83_0.buff.item3153botrkstacks.stacks2 == 2 then
			slot_83_0 = slot_83_0 + (40 + 6.470588235294118 * (player.levelRef - 1)) * ove_0_25.MagicReduction(arg_83_0, arg_83_1)
		end

		if arg_83_1.attackRange > 350 then
			slot_83_0 = slot_83_0 + arg_83_0.health * 0.05 * ove_0_25.PhysicalReduction(arg_83_0, arg_83_1)
		else
			slot_83_0 = slot_83_0 + arg_83_0.health * 0.09 * ove_0_25.PhysicalReduction(arg_83_0, arg_83_1)
		end
	end

	if ove_0_112 then
		slot_83_0 = slot_83_0 + (65 + ove_0_25.GetBonusAD() * 0.25) * ove_0_25.PhysicalReduction(arg_83_0, arg_83_1)
	end

	if ove_0_114 then
		slot_83_0 = slot_83_0 + 15 * ove_0_25.PhysicalReduction(arg_83_0, arg_83_1)
	end

	if ove_0_118 and player.buff["3078trinityforce"] then
		slot_83_0 = slot_83_0 + 2 * player.baseAttackDamage * ove_0_25.PhysicalReduction(arg_83_0, arg_83_1)
	end

	if ove_0_117 and player.buff["3508buff"] then
		slot_83_0 = slot_83_0 + (ove_0_25.GetBonusAD() * 0.4 + player.baseAttackDamage) * ove_0_25.PhysicalReduction(arg_83_0, arg_83_1)
	end

	if ove_0_115 and player.buff.sheen then
		slot_83_0 = slot_83_0 + 1 * player.baseAttackDamage * ove_0_25.PhysicalReduction(arg_83_0, arg_83_1)
	end

	if ove_0_116 and player.buff.lichbane then
		slot_83_0 = slot_83_0 + (0.75 * player.baseAttackDamage + ove_0_25.GetTotalAP() * 0.5) * ove_0_25.MagicReduction(arg_83_0, arg_83_1)
	end

	if ove_0_119 and player.buff["6632buff"] then
		slot_83_0 = slot_83_0 + math.max(1.5 * player.baseAttackDamage, 0.12 * arg_83_0.health) * ove_0_25.PhysicalReduction(arg_83_0, arg_83_1)
	end

	if arg_83_0.buff["assets/perks/styles/precision/presstheattack/presstheattackstack.lua"] and arg_83_0.buff["assets/perks/styles/precision/presstheattack/presstheattackstack.lua"].stacks2 == 2 and arg_83_0.buff["assets/perks/styles/precision/presstheattack/presstheattackstack.lua"].source == player then
		slot_83_0 = slot_83_0 + (40 + 8.235294117647058 * (player.levelRef - 1)) * (ove_0_25.GetTotalAP() > ove_0_25.GetBonusAD() and ove_0_25.MagicReduction(arg_83_0, arg_83_1) or ove_0_25.PhysicalReduction(arg_83_0, arg_83_1))
	end

	if ove_0_25.hasRune("coupdegrace", player) and ove_0_25.GetHPPercent(arg_83_0) < 40 then
		return ove_0_25.GetReductionPercent(arg_83_0, "BOTH", slot_83_0 * 1.08)
	end

	return ove_0_25.GetReductionPercent(arg_83_0, "BOTH", slot_83_0)
end

function ove_0_25.CalculateAADamage(arg_84_0, arg_84_1, arg_84_2)
	-- print 84
	local slot_84_0 = arg_84_1 or player
	local slot_84_1 = arg_84_2 or 1

	if slot_84_1 == 0 then
		return 0
	end

	if arg_84_0 and slot_84_0.baseAttackDamage and (arg_84_0.type == TYPE_MINION or arg_84_0.type == TYPE_HERO) then
		local slot_84_2 = 0
		local slot_84_3 = 1

		if slot_84_0.charName == "Kalista" then
			slot_84_3 = 0.9
		end

		local slot_84_4 = slot_84_2 + ove_0_20.utility.get_bonus_damage(slot_84_0, arg_84_0) * ove_0_25.PhysicalReduction(arg_84_0, slot_84_0)

		if ove_0_60[slot_84_0.networkID] then
			if ove_0_60[slot_84_0.networkID].cutdown and arg_84_0.maxHealth - slot_84_0.maxHealth > 0 then
				local slot_84_5 = (ove_0_65(1, (arg_84_0.maxHealth - player.maxHealth) / player.maxHealth) - 0.1) * 1 / 9 + 1.05

				return ove_0_25.GetReductionPercent(arg_84_0, "AD", ove_0_25.GetTotalAD(slot_84_0) * slot_84_3 * ove_0_25.PhysicalReduction(arg_84_0, slot_84_0) * slot_84_5 * slot_84_1 + slot_84_4)
			end

			if ove_0_60[slot_84_0.networkID].coupdegrace and ove_0_25.GetHPPercent(arg_84_0) < 40 then
				return ove_0_25.GetReductionPercent(arg_84_0, "AD", (ove_0_25.GetTotalAD(slot_84_0) * slot_84_3 * ove_0_25.PhysicalReduction(arg_84_0, slot_84_0) * slot_84_1 + slot_84_4) * 1.08)
			end
		end

		return ove_0_25.GetReductionPercent(arg_84_0, "AD", ove_0_25.GetTotalAD(slot_84_0) * slot_84_3 * ove_0_25.PhysicalReduction(arg_84_0, slot_84_0) * slot_84_1 + slot_84_4)
	end

	return 0
end

local ove_0_120 = mathf.angle_between

function ove_0_25.IsFacing(arg_85_0, arg_85_1)
	-- print 85
	return ove_0_69(ove_0_70(ove_0_120(arg_85_0.pos2D, arg_85_1.pos2D, arg_85_0.pos2D + arg_85_0.direction2D))) <= 70
end

local ove_0_121
local ove_0_122
local ove_0_123 = 0
local ove_0_124 = 0
local ove_0_125
local ove_0_126
local ove_0_127
local ove_0_128
local ove_0_129
local ove_0_130

local function ove_0_131()
	-- print 86
	if ove_0_122 then
		return ove_0_122
	end

	if ove_0_121 then
		return ove_0_121
	end

	return nil
end

function ove_0_25.GetZedShadow()
	-- print 87
	if ove_0_122 and ove_0_121 then
		return {
			ove_0_122,
			ove_0_121
		}
	end

	if ove_0_122 then
		return {
			ove_0_122
		}
	end

	if ove_0_121 then
		return {
			ove_0_121
		}
	end

	return nil
end

function ove_0_25.GetYoneClonePos()
	-- print 88
	if ove_0_38 then
		return ove_0_38
	end

	return nil
end

local function ove_0_132(arg_89_0)
	-- print 89
	if arg_89_0 and arg_89_0.name == "Shadow" and arg_89_0.owner and arg_89_0.owner.team == TEAM_ENEMY then
		if ove_0_123 > game.time then
			ove_0_122 = arg_89_0
		end

		if ove_0_124 > game.time then
			ove_0_121 = arg_89_0
		end
	end

	if arg_89_0.name == "IceBlock" then
		ove_0_125 = arg_89_0
	end

	if arg_89_0.name == "AzirRSoldier" then
		ove_0_126 = arg_89_0
	end

	if arg_89_0.name == "Beacon" and arg_89_0.owner.charName == "JarvanIV" then
		ove_0_127 = arg_89_0
	end

	if arg_89_0.name == "OrnnQPillar" and arg_89_0.owner.charName == "Ornn" then
		ove_0_128 = arg_89_0
	end

	if arg_89_0.name == "PlagueBlock" and arg_89_0.owner.charName == "Trundle" then
		ove_0_129 = arg_89_0
	end

	if arg_89_0.name == "InvisibleWall" and arg_89_0.owner.charName == "Yorick" then
		ove_0_130 = arg_89_0
	end
end

local function ove_0_133(arg_90_0)
	-- print 90
	if ove_0_122 and arg_90_0 and arg_90_0.ptr == ove_0_122.ptr then
		ove_0_122 = nil
	end

	if ove_0_121 and arg_90_0 and arg_90_0.ptr == ove_0_121.ptr then
		ove_0_121 = nil
	end

	if ove_0_125 and arg_90_0.ptr == ove_0_125.ptr then
		ove_0_125 = nil
	end

	if ove_0_126 and arg_90_0.ptr == ove_0_126.ptr then
		ove_0_126 = nil
	end

	if ove_0_127 and arg_90_0.ptr == ove_0_127.ptr then
		ove_0_127 = nil
	end

	if ove_0_128 and arg_90_0.ptr == ove_0_128.ptr then
		ove_0_128 = nil
	end

	if ove_0_129 and arg_90_0.ptr == ove_0_129.ptr then
		ove_0_129 = nil
	end

	if ove_0_130 and arg_90_0.ptr == ove_0_130.ptr then
		ove_0_130 = nil
	end
end

cb.add(cb.create_minion, ove_0_132)
cb.add(cb.delete_minion, ove_0_133)

function ove_0_25.IsCollidingJarvanIV(arg_91_0, arg_91_1)
	-- print 91
	if ove_0_127 then
		local slot_91_0 = ove_0_127.pos2D
		local slot_91_1 = arg_91_0
		local slot_91_2 = arg_91_1
		local slot_91_3 = mathf.closest_vec_line_seg(slot_91_0, slot_91_1, slot_91_2)

		if slot_91_3 and slot_91_3:dist(slot_91_0) <= 215 then
			return true
		end
	end

	return false
end

function ove_0_25.DarkHarvestDamage(arg_92_0, arg_92_1)
	-- print 92
	if ove_0_25.GetHPPercent(arg_92_1) > 50 then
		return 0
	end

	if arg_92_0.buff["assets/perks/styles/domination/darkharvest/darkharvestcooldown.lua"] then
		return 0
	end

	local slot_92_0 = 0

	if ove_0_60[arg_92_0.networkID] and ove_0_60[arg_92_0.networkID].darkharvest and arg_92_0.buff["assets/perks/styles/domination/darkharvest/darkharvest.lua"] then
		slot_92_0 = 20 + 2.3529411764705883 * (arg_92_0.levelRef - 1) + ove_0_25.GetBonusAD(arg_92_0) * 0.25 + ove_0_25.GetTotalAP(arg_92_0) * 0.15 + 5 * arg_92_0.buff["assets/perks/styles/domination/darkharvest/darkharvest.lua"].stacks2
	end

	return ove_0_25.GetTotalAP() > ove_0_25.GetBonusAD() and ove_0_25.GetReductionPercent(arg_92_1, "AP", ove_0_25.CalculateMagicDamage(arg_92_1, slot_92_0, arg_92_0)) or ove_0_25.GetReductionPercent(arg_92_1, "AD", ove_0_25.CalculatePhysicalDamage(arg_92_1, slot_92_0, arg_92_0))
end

function ove_0_25.IsCenterInSpell(arg_93_0, arg_93_1, arg_93_2, arg_93_3)
	-- print 93
	local slot_93_0 = arg_93_0
	local slot_93_1 = arg_93_1
	local slot_93_2 = arg_93_2
	local slot_93_3 = mathf.closest_vec_line_seg(slot_93_0, slot_93_1, slot_93_2)

	if slot_93_3 and arg_93_3 >= slot_93_3:dist(slot_93_0) then
		return true
	end

	return false
end

local function ove_0_134(arg_94_0, arg_94_1, arg_94_2, arg_94_3)
	-- print 94
	local slot_94_0 = arg_94_3 or 0
	local slot_94_1 = (arg_94_1 - arg_94_0):norm()
	local slot_94_2 = arg_94_2 * slot_94_1:perp1()

	return {
		(arg_94_0 + slot_94_2 - slot_94_0 * slot_94_1):to2D(),
		(arg_94_0 - slot_94_2 - slot_94_0 * slot_94_1):to2D(),
		(arg_94_1 - slot_94_2 + slot_94_0 * slot_94_1):to2D(),
		(arg_94_1 + slot_94_2 + slot_94_0 * slot_94_1):to2D()
	}
end

local function ove_0_135(arg_95_0, arg_95_1)
	-- print 95
	local slot_95_0 = false
	local slot_95_1 = #arg_95_0

	for iter_95_0 = 1, #arg_95_0 do
		if (arg_95_0[iter_95_0].y < arg_95_1.z and arg_95_0[slot_95_1].y >= arg_95_1.z or arg_95_0[slot_95_1].y < arg_95_1.z and arg_95_0[iter_95_0].y >= arg_95_1.z) and arg_95_0[iter_95_0].x + (arg_95_1.z - arg_95_0[iter_95_0].y) / (arg_95_0[slot_95_1].y - arg_95_0[iter_95_0].y) * (arg_95_0[slot_95_1].x - arg_95_0[iter_95_0].x) < arg_95_1.x then
			slot_95_0 = not slot_95_0
		end

		slot_95_1 = iter_95_0
	end

	return slot_95_0
end

function ove_0_25.isWallValid(arg_96_0)
	-- print 96
	if ove_0_125 and not ove_0_125.isDead then
		local slot_96_0 = ove_0_134(ove_0_125.pos, ove_0_125.pos + (ove_0_25.VectorExtend(ove_0_125.pos, ove_0_125.pos + ove_0_125.direction, 300) - ove_0_125.pos):norm():perp1() * (300 + ove_0_125.owner:spellSlot(1).level * 100), 100, 150)

		if ove_0_135(slot_96_0, arg_96_0) then
			return true
		end
	end

	if ove_0_126 and not ove_0_126.isDead then
		local slot_96_1 = ove_0_134(ove_0_126.pos, ove_0_126.pos + (ove_0_25.VectorExtend(ove_0_126.pos, ove_0_126.pos + ove_0_126.direction, 300) - ove_0_126.pos):norm():perp1() * (400 + ove_0_126.owner:spellSlot(3).level * 100), 100, 100)

		if ove_0_135(slot_96_1, arg_96_0) then
			return true
		end
	end

	if ove_0_127 and not ove_0_127.isDead and ove_0_127.pos:dist(arg_96_0) < 50 then
		return true
	end

	if ove_0_128 and not ove_0_128.isDead and ove_0_128.pos:dist(arg_96_0) < 80 then
		return true
	end

	if ove_0_129 and not ove_0_129.isDead and ove_0_129.pos:dist(arg_96_0) < 100 then
		return true
	end

	if ove_0_130 and not ove_0_130.isDead and ove_0_25.VectorExtend(ove_0_130.pos, ove_0_130.pos + ove_0_130.direction, 200):dist(arg_96_0) < 250 then
		return true
	end

	return false
end

function ove_0_25.GetChannelNotMove()
	-- print 97
	return {
		caitlyn = {
			{
				menuslot = "R",
				buffname = "none",
				time = 1.375,
				slot = _R
			}
		},
		fiddlesticks = {
			{
				menuslot = "W",
				buffname = "none",
				time = 2,
				slot = _W
			},
			{
				menuslot = "R",
				buffname = "none",
				time = 1.5,
				slot = _R
			}
		},
		janna = {
			{
				menuslot = "R",
				buffname = "reapthewhirlwind",
				slot = _R
			}
		},
		karthus = {
			{
				menuslot = "R",
				buffname = "karthusfallenonecastsound",
				slot = _R
			}
		},
		katarina = {
			{
				menuslot = "R",
				buffname = "katarinarsound",
				slot = _R
			}
		},
		malzahar = {
			{
				menuslot = "R",
				buffname = "malzaharrsound",
				slot = _R
			}
		},
		masteryi = {
			{
				menuslot = "W",
				buffname = "meditate",
				slot = _W
			}
		},
		missfortune = {
			{
				menuslot = "R",
				buffname = "none",
				time = 3,
				slot = _R
			}
		},
		nunu = {
			{
				menuslot = "R",
				buffname = "none",
				time = 3,
				slot = _R
			}
		},
		pantheon = {
			{
				menuslot = "R",
				buffname = "pantheonr",
				slot = _R
			}
		},
		quinn = {
			{
				menuslot = "R",
				buffname = "none",
				time = 2,
				slot = _R
			}
		},
		shen = {
			{
				menuslot = "R",
				buffname = "shenrchannelmanager",
				slot = _R
			}
		},
		irelia = {
			{
				menuslot = "W",
				buffname = "ireliawdefense",
				slot = _W
			}
		},
		twistedfate = {
			{
				menuslot = "R",
				buffname = "gate",
				slot = _R
			}
		},
		velkoz = {
			{
				menuslot = "R",
				buffname = "velkozr",
				slot = _R
			}
		},
		warwick = {
			{
				menuslot = "R",
				buffname = "warwickrsound",
				slot = _R
			}
		},
		xerath = {
			{
				menuslot = "R",
				buffname = "xerathlocusofpower2",
				slot = _R
			}
		},
		jhin = {
			{
				menuslot = "R",
				buffname = "none",
				time = 1,
				slot = _R
			}
		}
	}
end

function ove_0_25.EmpoweredAttacks()
	-- print 98
	return ove_0_79
end

function ove_0_25.IsValidMinion(arg_99_0)
	-- print 99
	return arg_99_0 and not arg_99_0.isDead and arg_99_0.path and arg_99_0.isVisible and arg_99_0.isTargetable and arg_99_0.maxHealth > 3 and not ove_0_71(ove_0_72(arg_99_0.name), "trap")
end

function ove_0_25.ContainsTrapEndPos(arg_100_0, arg_100_1, arg_100_2)
	-- print 100
	if arg_100_1.owner.charName == "Veigar" and arg_100_1.data.slot == 2 or arg_100_1.owner.charName == "Jinx" and arg_100_1.data.slot == 2 or arg_100_1.owner.charName == "Caitlyn" and arg_100_1.data.slot == 1 or arg_100_1.owner.charName == "Teemo" and arg_100_1.data.slot == 3 then
		if arg_100_1.owner.charName == "Caitlyn" and os.clock() - arg_100_1.start_time < 1 then
			return false
		end

		if arg_100_1.owner.charName == "Jinx" and os.clock() - arg_100_1.start_time < 0.5 then
			return false
		end

		local slot_100_0 = arg_100_2

		for iter_100_0 = 20, arg_100_2:dist(arg_100_0.pos), 20 do
			if ove_0_25.InsideSpellPos(arg_100_1, (arg_100_0.pos - iter_100_0 * (arg_100_0.pos - slot_100_0):norm()):to2D()) then
				return true
			end
		end
	end
end

function ove_0_25.ContainsTrap(arg_101_0, arg_101_1)
	-- print 101
	if arg_101_1.owner.charName == "Veigar" and arg_101_1.data.slot == 2 or arg_101_1.owner.charName == "Jinx" and arg_101_1.data.slot == 2 or arg_101_1.owner.charName == "Caitlyn" and arg_101_1.data.slot == 1 or arg_101_1.owner.charName == "Teemo" and arg_101_1.data.slot == 3 then
		if arg_101_1.owner.charName == "Caitlyn" and os.clock() - arg_101_1.start_time < 1 then
			return false
		end

		if arg_101_1.owner.charName == "Jinx" and os.clock() - arg_101_1.start_time < 0.5 then
			return false
		end

		local slot_101_0 = ove_0_25.VectorExtend(arg_101_0.pos, arg_101_0.pos + arg_101_0.direction, 200)

		for iter_101_0 = 20, 200, 20 do
			if ove_0_25.InsideSpellPos(arg_101_1, (arg_101_0.pos - iter_101_0 * (arg_101_0.pos - slot_101_0):norm()):to2D()) then
				return true
			end
		end
	end
end

function ove_0_25.GetMinion(arg_102_0)
	-- print 102
	local slot_102_0 = {}

	for iter_102_0 = 0, objManager.minions.size[TEAM_ENEMY] - 1 do
		local slot_102_1 = objManager.minions[TEAM_ENEMY][iter_102_0]

		if slot_102_1 and not slot_102_1.isDead and slot_102_1.moveSpeed > 0 and slot_102_1.isTargetable and slot_102_1.isVisible and slot_102_1.type == TYPE_MINION and arg_102_0 >= ove_0_37(slot_102_1.pos, player.pos) then
			slot_102_0[#slot_102_0 + 1] = slot_102_1
		end
	end

	return slot_102_0
end

function ove_0_25.GetJungleMinion(arg_103_0)
	-- print 103
	local slot_103_0 = {}

	for iter_103_0 = 0, objManager.minions.size[TEAM_NEUTRAL] - 1 do
		local slot_103_1 = objManager.minions[TEAM_NEUTRAL][iter_103_0]

		if slot_103_1 and not slot_103_1.isDead and slot_103_1.moveSpeed > 0 and slot_103_1.isTargetable and slot_103_1.isVisible and slot_103_1.type == TYPE_MINION and arg_103_0 >= ove_0_37(slot_103_1.pos, player.pos) then
			slot_103_0[#slot_103_0 + 1] = slot_103_1
		end
	end

	return slot_103_0
end

function ove_0_25.CountMinionsAlly(arg_104_0, arg_104_1)
	-- print 104
	local slot_104_0 = {}

	for iter_104_0 = 0, objManager.minions.size[TEAM_ALLY] - 1 do
		local slot_104_1 = objManager.minions[TEAM_ALLY][iter_104_0]

		if arg_104_1 > ove_0_37(slot_104_1.pos, arg_104_0) and ove_0_25.IsValidMinion(slot_104_1) then
			slot_104_0[#slot_104_0 + 1] = slot_104_1
		end
	end

	return slot_104_0
end

function ove_0_25.CountMinions(arg_105_0, arg_105_1, arg_105_2)
	-- print 105
	local slot_105_0 = {}
	local slot_105_1 = arg_105_2 == "enemy" and TEAM_ENEMY or arg_105_2 == "neutral" and TEAM_NEUTRAL or "both"

	if slot_105_1 ~= "both" then
		for iter_105_0 = 0, objManager.minions.size[slot_105_1] - 1 do
			local slot_105_2 = objManager.minions[slot_105_1][iter_105_0]

			if arg_105_1 > ove_0_37(slot_105_2.pos, arg_105_0) and ove_0_25.IsValidMinion(slot_105_2) then
				slot_105_0[#slot_105_0 + 1] = slot_105_2
			end
		end
	else
		for iter_105_1 = 0, objManager.minions.size[TEAM_ENEMY] - 1 do
			local slot_105_3 = objManager.minions[TEAM_ENEMY][iter_105_1]

			if arg_105_1 > ove_0_37(slot_105_3.pos, arg_105_0) and ove_0_25.IsValidMinion(slot_105_3) then
				slot_105_0[#slot_105_0 + 1] = slot_105_3
			end
		end

		for iter_105_2 = 0, objManager.minions.size[TEAM_NEUTRAL] - 1 do
			local slot_105_4 = objManager.minions[TEAM_NEUTRAL][iter_105_2]

			if arg_105_1 > ove_0_37(slot_105_4.pos, arg_105_0) and ove_0_25.IsValidMinion(slot_105_4) then
				slot_105_0[#slot_105_0 + 1] = slot_105_4
			end
		end
	end

	return slot_105_0
end

function ove_0_25.GetHPPercent(arg_106_0)
	-- print 106
	return arg_106_0.health / arg_106_0.maxHealth * 100
end

function ove_0_25.GetMPPercent(arg_107_0)
	-- print 107
	return arg_107_0.mana / arg_107_0.maxMana * 100
end

function ove_0_25.CountAllies2D(arg_108_0, arg_108_1)
	-- print 108
	local slot_108_0 = {}

	for iter_108_0 = 0, objManager.allies_n - 1 do
		local slot_108_1 = objManager.allies[iter_108_0]

		if arg_108_1 > ove_0_37(slot_108_1.pos, arg_108_0) and ove_0_25.IsValidTarget(slot_108_1) then
			slot_108_0[#slot_108_0 + 1] = slot_108_1
		end
	end

	return slot_108_0
end



function ove_0_25.CountAlliesNoPlayer(arg_109_0, arg_109_1, arg_109_2)
	-- print 109
	local slot_109_0 = {}

	for iter_109_0 = 0, objManager.allies_n - 1 do
		local slot_109_1 = objManager.allies[iter_109_0]

		if (arg_109_2 and arg_109_1 + slot_109_1.boundingRadius or arg_109_1) > ove_0_37(slot_109_1.pos, arg_109_0) and ove_0_25.IsValidTarget(slot_109_1) and slot_109_1 ~= player then
			slot_109_0[#slot_109_0 + 1] = slot_109_1
		end
	end

	return slot_109_0
end

function ove_0_25.CountAllies(arg_110_0, arg_110_1, arg_110_2)
	-- print 110
	local slot_110_0 = {}

	for iter_110_0 = 0, objManager.allies_n - 1 do
		local slot_110_1 = objManager.allies[iter_110_0]

		if (arg_110_2 and arg_110_1 + slot_110_1.boundingRadius or arg_110_1) > ove_0_37(slot_110_1.pos, arg_110_0) and ove_0_25.IsValidTarget(slot_110_1) then
			slot_110_0[#slot_110_0 + 1] = slot_110_1
		end
	end

	return slot_110_0
end

function ove_0_25.CountEnemiesKillable(arg_111_0, arg_111_1, arg_111_2)
	-- print 111
	local slot_111_0 = {}

	for iter_111_0 = 0, objManager.enemies_n - 1 do
		local slot_111_1 = objManager.enemies[iter_111_0]

		if (arg_111_2 and arg_111_1 + slot_111_1.boundingRadius or arg_111_1) > ove_0_37(slot_111_1.pos, arg_111_0) and ove_0_25.IsValidTarget(slot_111_1) and ove_0_25.CalculateAADamage(slot_111_1, player, 1) * 1.5 >= slot_111_1.health then
			slot_111_0[#slot_111_0 + 1] = slot_111_1
		end
	end

	return slot_111_0
end

function ove_0_25.CountEnemies(arg_112_0, arg_112_1, arg_112_2)
	-- print 112
	local slot_112_0 = {}

	for iter_112_0 = 0, objManager.enemies_n - 1 do
		local slot_112_1 = objManager.enemies[iter_112_0]

		if (arg_112_2 and arg_112_1 + slot_112_1.boundingRadius or arg_112_1) > ove_0_37(slot_112_1.pos, arg_112_0) and ove_0_25.IsValidTarget(slot_112_1) then
			slot_112_0[#slot_112_0 + 1] = slot_112_1
		end
	end

	return slot_112_0
end

function ove_0_25.CountEnemiesInv(arg_113_0, arg_113_1, arg_113_2, arg_113_3)
	-- print 113
	local slot_113_0 = {}

	for iter_113_0 = 0, objManager.enemies_n - 1 do
		local slot_113_1 = objManager.enemies[iter_113_0]

		if arg_113_1 > ove_0_37(slot_113_1.pos, arg_113_0) and slot_113_1 ~= arg_113_3 and (slot_113_1.isVisible and ove_0_25.IsValidTarget(slot_113_1) or ove_0_25.IsValidTargetInv(slot_113_1) and ove_0_27[slot_113_1.networkID] and arg_113_2 >= game.time - ove_0_27[slot_113_1.networkID].LastSeen) then
			slot_113_0[#slot_113_0 + 1] = slot_113_1
		end
	end

	return slot_113_0
end

function ove_0_25.CountEnemiesAfterX(arg_114_0, arg_114_1, arg_114_2, arg_114_3)
	-- print 114
	local slot_114_0 = {}

	for iter_114_0 = 0, objManager.enemies_n - 1 do
		local slot_114_1 = objManager.enemies[iter_114_0]

		if not arg_114_3 or not (arg_114_1 + slot_114_1.boundingRadius) then
			local slot_114_2 = arg_114_1
		end

		local slot_114_3 = ove_0_24.core.get_pos_after_time(slot_114_1, arg_114_2)

		if arg_114_1 > ove_0_37(arg_114_0, slot_114_3) and ove_0_25.IsValidTarget(slot_114_1) then
			slot_114_0[#slot_114_0 + 1] = slot_114_1
		end
	end

	return slot_114_0
end

function ove_0_25.GetTrackerList()
	-- print 115
	return ove_0_26
end

function ove_0_25.GetVisibilityList()
	-- print 116
	return ove_0_27
end

function ove_0_25.SionCheck(arg_117_0)
	-- print 117
	if arg_117_0.type ~= TYPE_HERO then
		return true
	else
		return not arg_117_0.buff.sionpassivezombie
	end
end

function ove_0_25.IsInvulnerable(arg_118_0)
	-- print 118
	if arg_118_0.type ~= TYPE_HERO then
		return false
	elseif arg_118_0.buff and (arg_118_0.buff.sionpassivezombie or arg_118_0.buff.chronoshift or arg_118_0.buff.kindredrnodeathbuff or arg_118_0.buff.undyingrage) or arg_118_0.buff.kayler or arg_118_0.buff.pantheone then
		return true
	else
		return false
	end
end

function ove_0_25.isMundoShield(arg_119_0)
	-- print 119
	return arg_119_0.buff.drmundopimmunity
end

function ove_0_25.IsSpellShield(arg_120_0)
	-- print 120
	if arg_120_0.type ~= TYPE_HERO then
		return false
	elseif arg_120_0.buff[BUFF_INVULNERABILITY] or arg_120_0.buff[BUFF_SPELLSHIELD] then
		return true
	else
		return false
	end
end

local ove_0_136 = {}
local ove_0_137

function ove_0_25.DelayAction(arg_121_0, arg_121_1, arg_121_2)
	-- print 121
	if not ove_0_137 then
		function ove_0_137()
			-- print 122
			for iter_122_0, iter_122_1 in pairs(ove_0_136) do
				if iter_122_0 <= game.time and iter_122_0 == iter_122_0 then
					for iter_122_2 = 1, #iter_122_1 do
						if iter_122_2 == iter_122_2 then
							local slot_122_0 = iter_122_1[iter_122_2]

							if slot_122_0 and slot_122_0.func then
								slot_122_0.func(unpack(slot_122_0.args or {}))
							end
						end
					end

					ove_0_136[iter_122_0] = nil
				end
			end
		end

		cb.add(cb.tick, ove_0_137)
	end

	local slot_121_0 = game.time + (arg_121_1 or 0)

	if ove_0_136[slot_121_0] then
		ove_0_136[slot_121_0][#ove_0_136[slot_121_0] + 1] = {
			func = arg_121_0,
			args = arg_121_2
		}
	else
		ove_0_136[slot_121_0] = {
			{
				func = arg_121_0,
				args = arg_121_2
			}
		}
	end
end

function ove_0_25.CheckBuffType(arg_123_0, arg_123_1)
	-- print 123
	if arg_123_0 and arg_123_0.buff and arg_123_0.buff[arg_123_1] and arg_123_0.buff[arg_123_1].startTime and game.time - arg_123_0.buff[arg_123_1].startTime > 0.1 then
		return true
	end

	return false
end

function ove_0_25.isCCDelay(arg_124_0)
	-- print 124
	if ove_0_25.CheckBuffType(arg_124_0, BUFF_STUN) or ove_0_25.CheckBuffType(arg_124_0, BUFF_TAUNT) or ove_0_25.CheckBuffType(arg_124_0, BUFF_SUPPRESSION) or ove_0_25.CheckBuffType(arg_124_0, BUFF_SNARE) or ove_0_25.CheckBuffType(arg_124_0, BUFF_CHARM) or ove_0_25.CheckBuffType(arg_124_0, BUFF_FEAR) then
		return true
	end

	return false
end

function ove_0_25.isCCType(arg_125_0)
	-- print 125
	if arg_125_0 == BUFF_STUN or arg_125_0 == BUFF_TAUNT or arg_125_0 == BUFF_SUPPRESSION or arg_125_0 == BUFF_SNARE or arg_125_0 == BUFF_CHARM or arg_125_0 == BUFF_FEAR or arg_125_0 == BUFF_SLOW or arg_125_0 == BUFF_KNOCKUP or arg_125_0 == BUFF_KNOCKBACK or arg_125_0 == BUFF_SILENCE or arg_125_0 == BUFF_POLYMORPH or arg_125_0 == BUFF_BLIND or arg_125_0 == BUFF_DROWSY then
		return true
	end

	return false
end

function ove_0_25.getIncomingDamageAA(arg_126_0, arg_126_1)
	-- print 126
	local slot_126_0 = 0

	if arg_126_1.damage and arg_126_1.damage[arg_126_0.ptr] then
		slot_126_0 = (arg_126_1.damage[arg_126_0.ptr].ap + arg_126_1.damage[arg_126_0.ptr].ad + arg_126_1.damage[arg_126_0.ptr].trued) * 1
	end

	return slot_126_0
end

function ove_0_25.getIncomingDamage(arg_127_0, arg_127_1, arg_127_2)
	-- print 127
	local slot_127_0 = 0
	local slot_127_1 = arg_127_2 == "ad" and 0 or arg_127_2 == "ap" and 1 or 2

	if arg_127_1.damage and arg_127_1.damage[arg_127_0.ptr] then
		if slot_127_1 == 0 then
			slot_127_0 = arg_127_1.damage[arg_127_0.ptr].ad + arg_127_1.damage[arg_127_0.ptr].trued
		elseif slot_127_1 == 1 then
			slot_127_0 = arg_127_1.damage[arg_127_0.ptr].ap
		else
			slot_127_0 = (arg_127_1.damage[arg_127_0.ptr].ap + arg_127_1.damage[arg_127_0.ptr].ad + arg_127_1.damage[arg_127_0.ptr].trued) * 1
		end
	end

	return slot_127_0
end

function ove_0_25.IsRecalling(arg_128_0)
	-- print 128
	return arg_128_0.isRecalling and arg_128_0.buff.recall and game.time - arg_128_0.buff.recall.startTime > 0.3
end

function ove_0_25.RecallActive(arg_129_0)
	-- print 129
	return arg_129_0.isRecalling and (arg_129_0.buff.recall or arg_129_0.buff.superrecall or arg_129_0.buff.odinrecall or arg_129_0.buff.odinrecallimproved or arg_129_0.buff.recallimproved)
end

function ove_0_25.GetImmobileTimeHard(arg_130_0)
	-- print 130
	local slot_130_0 = 0

	for iter_130_0, iter_130_1 in pairs(arg_130_0.buff) do
		if iter_130_1.valid and slot_130_0 < iter_130_1.endTime - game.time and game.time - iter_130_1.startTime > 0.1 and (iter_130_1.type == BUFF_STUN or iter_130_1.type == BUFF_SUPPRESSION or iter_130_1.type == BUFF_SNARE or iter_130_1.type == BUFF_KNOCKUP or iter_130_1.type == BUFF_ASLEEP) then
			slot_130_0 = iter_130_1.endTime - game.time
		end
	end

	return slot_130_0
end

function ove_0_25.GetImmobileTime(arg_131_0)
	-- print 131
	local slot_131_0 = 0

	if arg_131_0.buff.threshq and not arg_131_0.path.isActive then
		return slot_131_0
	end

	for iter_131_0, iter_131_1 in pairs(arg_131_0.buff) do
		if iter_131_1.valid and slot_131_0 < iter_131_1.endTime - game.time and game.time - iter_131_1.startTime > 0.1 and (iter_131_1.type == BUFF_STUN or iter_131_1.type == BUFF_TAUNT or iter_131_1.type == BUFF_SUPPRESSION or iter_131_1.type == BUFF_SNARE or iter_131_1.type == BUFF_CHARM or iter_131_1.type == BUFF_FLEE or iter_131_1.type == BUFF_KNOCKUP or iter_131_1.type == BUFF_KNOCKBACK or iter_131_1.type == BUFF_ASLEEP or iter_131_1.type == BUFF_KNOCKBACK or iter_131_1.type == BUFF_FEAR) then
			slot_131_0 = iter_131_1.endTime - game.time
		end
	end

	return slot_131_0
end

function ove_0_25.isCC(arg_132_0)
	-- print 132
	if arg_132_0.buff[BUFF_STUN] or arg_132_0.buff[BUFF_TAUNT] or arg_132_0.buff[BUFF_SUPPRESSION] or arg_132_0.buff[BUFF_SNARE] or arg_132_0.buff[BUFF_CHARM] or arg_132_0.buff[BUFF_FEAR] then
		return true
	end

	return false
end

local function ove_0_138(arg_133_0, arg_133_1, arg_133_2)
	-- print 133
	if arg_133_0 then
		if arg_133_1 == "1" then
			return true
		end

		if arg_133_1 == "2" then
			return false
		end

		return arg_133_1
	else
		return arg_133_2
	end
end

function ove_0_25.IsValidTargetInv(arg_134_0)
	-- print 134
	return arg_134_0 and not arg_134_0.isDead and ove_0_25.SionCheck(arg_134_0)
end

function ove_0_25.IsValidTarget(arg_135_0, arg_135_1, arg_135_2, arg_135_3, arg_135_4)
	-- print 135
	if not arg_135_0 then
		return false
	end

	if arg_135_0.isDead then
		return false
	end

	if not arg_135_0.isTargetable then
		return false
	end

	if (not arg_135_0.path or not ove_0_25.SionCheck(arg_135_0)) and arg_135_0.type ~= TYPE_INHIB and arg_135_0.type ~= TYPE_NEXUS then
		return false
	end

	if not arg_135_0 or arg_135_0.charName and ove_0_71(arg_135_0.charName, "_Plant_") then
		return false
	end

	if arg_135_4 and ove_0_27[arg_135_0.networkID] and game.time - ove_0_27[arg_135_0.networkID].LastSeen > 0.4 then
		return false
	end

	if not arg_135_4 and not arg_135_0.isVisible then
		return false
	end

	if arg_135_1 and ove_0_25.IsInvulnerable(arg_135_0) then
		return false
	end

	if arg_135_2 and ove_0_25.IsSpellShield(arg_135_0) then
		return false
	end

	if arg_135_3 and arg_135_0.buff.samiraw then
		return false
	end

	return true
end

function ove_0_25.CanBeCCd(arg_136_0)
	-- print 136
	if arg_136_0.buff.morganae or arg_136_0.buff.fioraw then
		return false
	end

	return true
end

function ove_0_25.VectorExtend(arg_137_0, arg_137_1, arg_137_2)
	-- print 137
	return arg_137_0 + arg_137_2 * (arg_137_1 - arg_137_0):norm()
end

function ove_0_25.getclosestturret(arg_138_0, arg_138_1)
	-- print 138
	for iter_138_0 = 0, objManager.turrets.size[TEAM_ENEMY] - 1 do
		local slot_138_0 = objManager.turrets[TEAM_ENEMY][iter_138_0]

		if slot_138_0 and not slot_138_0.isDead and arg_138_1 > ove_0_37(slot_138_0.pos, arg_138_0) then
			return slot_138_0
		end
	end

	return nil
end

function ove_0_25.getfountain(arg_139_0)
	-- print 139
	if ove_0_35 and ove_0_37(ove_0_35, arg_139_0) < 1100 then
		return true
	end
end

function ove_0_25.getturretenemy(arg_140_0)
	-- print 140
	if ove_0_35 and ove_0_37(ove_0_35, arg_140_0) < 1100 then
		return true
	end

	for iter_140_0 = 0, objManager.turrets.size[TEAM_ENEMY] - 1 do
		local slot_140_0 = objManager.turrets[TEAM_ENEMY][iter_140_0]

		if slot_140_0 and not slot_140_0.isDead and ove_0_37(slot_140_0.pos, arg_140_0) < 930 then
			return true
		end
	end

	return false
end

local ove_0_139 = 0.2

local function ove_0_140(arg_141_0)
	-- print 141
	local slot_141_0

	for iter_141_0 = 0, objManager.enemies_n - 1 do
		local slot_141_1 = objManager.enemies[iter_141_0]

		if slot_141_1 and ove_0_37(slot_141_1.pos, arg_141_0) <= 20 then
			return slot_141_1
		end
	end
end

local function ove_0_141(arg_142_0)
	-- print 142
	local slot_142_0

	for iter_142_0 = 0, objManager.allies_n - 1 do
		local slot_142_1 = objManager.allies[iter_142_0]

		if slot_142_1 and ove_0_37(slot_142_1.pos, arg_142_0) < 50 then
			return slot_142_1
		end
	end
end

local ove_0_142 = vec3(396, 182, 462)
local ove_0_143 = vec3(14340, 172, 14384)

if player.team == 200 then
	ove_0_143 = ove_0_142
end

local function ove_0_144(arg_143_0, arg_143_1, arg_143_2)
	-- print 143
	ove_0_31[arg_143_0.networkID] = {}
	ove_0_31[arg_143_0.networkID].target = arg_143_0
	ove_0_31[arg_143_0.networkID].aimPos = arg_143_1
	ove_0_31[arg_143_0.networkID].expireTime = arg_143_2 + game.time
end

local ove_0_145 = 0

local function ove_0_146()
	-- print 144
	for iter_144_0 = 0, objManager.turrets.size[TEAM_ENEMY] - 1 do
		local slot_144_0 = objManager.turrets[TEAM_ENEMY][iter_144_0]

		if slot_144_0 and not ove_0_31[slot_144_0.networkID] then
			local slot_144_1, slot_144_2 = ove_0_86(slot_144_0, "teleport_turret")

			if slot_144_1 then
				ove_0_144(slot_144_0.buff.teleport_turret.source, ove_0_25.VectorExtend(slot_144_0.pos, ove_0_143, 100), slot_144_2)
			end
		end
	end

	for iter_144_1 = 0, objManager.minions.size[TEAM_ENEMY] - 1 do
		local slot_144_3 = objManager.minions[TEAM_ENEMY][iter_144_1]

		if slot_144_3 and not ove_0_31[slot_144_3.networkID] then
			local slot_144_4, slot_144_5 = ove_0_86(slot_144_3, "teleport_target")

			if slot_144_4 then
				ove_0_144(slot_144_3.buff.teleport_target.source, ove_0_25.VectorExtend(slot_144_3.pos, ove_0_143, 100), slot_144_5)
			end
		end
	end
end

local ove_0_147 = 0

function ove_0_25.GetSoonBlink(arg_145_0)
	-- print 145
	if ove_0_147 < game.time then
		for iter_145_0 = 0, objManager.enemies_n - 1 do
			local slot_145_0 = objManager.enemies[iter_145_0]

			x = ove_0_26[slot_145_0.networkID]

			if x and x.BlinkInfo.WindupEnd - game.time < 5 and x.BlinkInfo.Comeout - game.time > 0 then
				local slot_145_1 = 0.1

				if arg_145_0.realDelay > 1 then
					slot_145_1 = 0.3
				end

				if x.BlinkInfo.Comeout - game.time <= arg_145_0.realDelay + network.latency and slot_145_1 > arg_145_0.realDelay + network.latency - (x.BlinkInfo.Comeout - game.time) and x.BlinkInfo.Owner and x.BlinkInfo.Owner == player then
					if slot_145_0.charName == "MasterYi" then
						return ove_0_25.VectorExtend(x.BlinkInfo.Owner, x.BlinkInfo.Owner + x.BlinkInfo.Owner.direction, 50), x.BlinkInfo.Owner
					end

					if slot_145_0.charName == "Zed" and slot_145_0.path.count >= 0 then
						return slot_145_0.path.point[1], x.BlinkInfo.Owner
					end
				end
			end
		end

		ove_0_147 = game.time + 0.1
	end

	return nil
end

local ove_0_148 = 0

local function ove_0_149()
	-- print 146
	for iter_146_0, iter_146_1 in pairs(ove_0_31) do
		if game.time > iter_146_1.expireTime + 0.5 then
			ove_0_31[iter_146_0] = nil
		end
	end

	if ove_0_148 < game.time then
		for iter_146_2, iter_146_3 in pairs(ove_0_30) do
			if game.time > iter_146_3.expireTime + 0.5 then
				ove_0_30[iter_146_2] = nil
			end
		end

		ove_0_146()

		ove_0_148 = game.time + 0.2
	end
end

local ove_0_150
local ove_0_151
local ove_0_152 = 0

local function ove_0_153()
	-- print 147
	if ove_0_106 > game.time then
		return
	end

	ove_0_107 = false
	ove_0_108 = false
	ove_0_109 = false
	ove_0_110 = false
	ove_0_111 = false
	ove_0_113 = false
	ove_0_114 = false
	ove_0_112 = false
	hasWits = false
	haSheen = false
	ove_0_118 = false
	ove_0_119 = false
	ove_0_117 = false
	ove_0_116 = false

	for iter_147_0 = 0, 5 do
		if player:itemID(iter_147_0) == 6672 then
			ove_0_107 = true
		end

		if player:itemID(iter_147_0) == 2015 then
			ove_0_108 = true
		end

		if player:itemID(iter_147_0) == 3094 then
			ove_0_109 = true
		end

		if player:itemID(iter_147_0) == 3095 then
			ove_0_110 = true
		end

		if player:itemID(iter_147_0) == 3153 then
			ove_0_111 = true
		end

		if player:itemID(iter_147_0) == 3115 then
			ove_0_113 = true
		end

		if player:itemID(iter_147_0) == 1043 then
			ove_0_114 = true
		end

		if player:itemID(iter_147_0) == 3078 then
			ove_0_118 = true
		end

		if player:itemID(iter_147_0) == 3057 then
			ove_0_115 = true
		end

		if player:itemID(iter_147_0) == 3100 then
			ove_0_116 = true
		end

		if player:itemID(iter_147_0) == 6632 then
			ove_0_119 = true
		end

		if player:itemID(iter_147_0) == 3508 then
			ove_0_117 = true
		end

		if player:itemID(iter_147_0) == 6691 and player:spellSlot(6 + iter_147_0).state == 0 then
			ove_0_112 = true
		end

		if player:itemID(iter_147_0) == 3091 and player:spellSlot(6 + iter_147_0).state == 0 then
			hasWits = true
		end
	end

	ove_0_106 = game.time + 0.1
end

local function ove_0_154()
	-- print 148
	ove_0_149()
	ove_0_153()

	for iter_148_0 = 0, objManager.enemies_n - 1 do
		local slot_148_0 = objManager.enemies[iter_148_0]

		if slot_148_0 and not slot_148_0.isVisible then
			ove_0_27[slot_148_0.networkID] = {
				Time = game.time,
				Pos = ove_0_25.VectorExtend(slot_148_0.pos, slot_148_0.pos + slot_148_0.direction, 200),
				LastSeen = ove_0_27[slot_148_0.networkID].LastSeen
			}
		end

		if slot_148_0 and slot_148_0.isVisible then
			ove_0_27[slot_148_0.networkID] = {
				Time = ove_0_27[slot_148_0.networkID].Time,
				Pos = ove_0_25.VectorExtend(slot_148_0.pos, slot_148_0.pos + slot_148_0.direction, 200),
				LastSeen = game.time
			}
		end

		if slot_148_0.type == TYPE_HERO and slot_148_0.team == TEAM_ENEMY and slot_148_0 and ove_0_25.IsValidTarget(slot_148_0) and ove_0_25.GetDashers()[slot_148_0.networkID] then
			if slot_148_0.path.isDashing and slot_148_0.path.count >= 0 and (not ove_0_25.GetDashers()[slot_148_0.networkID].endPos or ove_0_37(ove_0_25.GetDashers()[slot_148_0.networkID].endPos, vec3(slot_148_0.path.point[1])) > 100) then
				ove_0_25.GetDashers()[slot_148_0.networkID].startPos = vec3(slot_148_0.path.point[0])
				ove_0_25.GetDashers()[slot_148_0.networkID].endPos = vec3(slot_148_0.path.point[1])
			end

			if (not slot_148_0.path or not slot_148_0.path.isDashing) and ove_0_25.GetDashers()[slot_148_0.networkID].startPos and ove_0_25.GetDashers()[slot_148_0.networkID].lastDelete == 0 and ove_0_25.GetDashers()[slot_148_0.networkID].begin < game.time then
				ove_0_25.GetDashers()[slot_148_0.networkID].lastDelete = game.time + 0.1
			end
		end
	end

	for iter_148_1 = 0, objManager.allies_n - 1 do
		local slot_148_1 = objManager.allies[iter_148_1]

		if slot_148_1 and not slot_148_1.isVisible then
			ove_0_27[slot_148_1.networkID] = {
				Time = game.time,
				Pos = ove_0_25.VectorExtend(slot_148_1.pos, slot_148_1.pos + slot_148_1.direction, 200),
				LastSeen = ove_0_27[slot_148_1.networkID].LastSeen
			}
		end

		if slot_148_1 and slot_148_1.isVisible then
			ove_0_27[slot_148_1.networkID] = {
				Time = ove_0_27[slot_148_1.networkID].Time,
				Pos = ove_0_25.VectorExtend(slot_148_1.pos, slot_148_1.pos + slot_148_1.direction, 200),
				LastSeen = game.time
			}
		end

		if slot_148_1.type == TYPE_HERO and slot_148_1 and ove_0_25.IsValidTarget(slot_148_1) and ove_0_25.GetAllyDashers()[slot_148_1.networkID] then
			if slot_148_1.path.isDashing and slot_148_1.path.count >= 0 and (not ove_0_25.GetAllyDashers()[slot_148_1.networkID].endPos or ove_0_37(ove_0_25.GetAllyDashers()[slot_148_1.networkID].endPos, vec3(slot_148_1.path.point[1])) > 100) then
				ove_0_25.GetAllyDashers()[slot_148_1.networkID].startPos = vec3(slot_148_1.path.point[0])
				ove_0_25.GetAllyDashers()[slot_148_1.networkID].endPos = vec3(slot_148_1.path.point[1])
			end

			if (not slot_148_1.path or not slot_148_1.path.isDashing) and ove_0_25.GetAllyDashers()[slot_148_1.networkID].startPos and ove_0_25.GetAllyDashers()[slot_148_1.networkID].lastDelete == 0 and ove_0_25.GetAllyDashers()[slot_148_1.networkID].begin < game.time then
				ove_0_25.GetAllyDashers()[slot_148_1.networkID].lastDelete = game.time + 0.1
			end
		end
	end

	for iter_148_2, iter_148_3 in pairs(ove_0_25.GetDashers()) do
		if iter_148_3.owner and iter_148_3.lastDelete < game.time and iter_148_3.lastDelete ~= 0 then
			ove_0_25.GetDashers()[iter_148_3.owner.networkID] = nil
		end

		if iter_148_3.owner and iter_148_3.begin + 2 <= game.time then
			ove_0_25.GetDashers()[iter_148_3.owner.networkID] = nil
		end
	end

	for iter_148_4, iter_148_5 in pairs(ove_0_25.GetAllyDashers()) do
		if iter_148_5.owner and iter_148_5.lastDelete < game.time and iter_148_5.lastDelete ~= 0 then
			ove_0_25.GetAllyDashers()[iter_148_5.owner.networkID] = nil
		end

		if iter_148_5.owner and iter_148_5.begin + 2 <= game.time then
			ove_0_25.GetAllyDashers()[iter_148_5.owner.networkID] = nil
		end
	end
end

local function ove_0_155()
	-- print 149
	local slot_149_0 = ove_0_25.GetEnemyHeroes()

	for iter_149_0, iter_149_1 in ipairs(slot_149_0) do
		if iter_149_1.charName == "Rengar" then
			return iter_149_1
		end
	end

	return nil
end

local ove_0_156
local ove_0_157
local ove_0_158

for iter_0_8 = 0, objManager.enemies_n - 1 do
	local ove_0_159 = objManager.enemies[iter_0_8]

	if ove_0_159.charName == "TahmKench" then
		ove_0_156 = ove_0_159
	end

	if ove_0_159.charName == "TwistedFate" then
		ove_0_157 = ove_0_159
	end

	if ove_0_159.charName == "Poppy" then
		ove_0_158 = ove_0_159
	end
end

function ove_0_25.CollidesWithPoppy(arg_150_0)
	-- print 150
	if ove_0_158 and ove_0_158.buff.poppywzone and arg_150_0:to2D():dist(ove_0_158.pos2D) < 500 then
		return true
	end

	return false
end

local ove_0_160

function ove_0_25.GetNautilusRMissile()
	-- print 151
	return ove_0_160
end

local function ove_0_161(arg_152_0)
	-- print 152
	if ove_0_71(arg_152_0.name, "Yone") and ove_0_71(arg_152_0.name, "E_Beam") and ove_0_73 > game.time then
		ove_0_38 = arg_152_0
	end

	if ove_0_71(arg_152_0.name, "Nautilus") and ove_0_71(arg_152_0.name, "R_sequence_impact") then
		ove_0_160 = arg_152_0
	end

	if arg_152_0 then
		if ove_0_71(arg_152_0.name, "Yasuo") or ove_0_71(arg_152_0.name, "yasuo") then
			local slot_152_0 = ove_0_72(arg_152_0.name)

			if ove_0_71(slot_152_0, "w_windwall") and not ove_0_71(slot_152_0, "w_windwall_activate") and not ove_0_71(slot_152_0, "impact") then
				ove_0_150 = arg_152_0
			end
		end

		if not ove_0_30[arg_152_0.ptr] and arg_152_0.name == "3026_Buff_Revive" then
			ove_0_30[arg_152_0.ptr] = {}
			ove_0_30[arg_152_0.ptr].expireTime = game.time + 4

			local slot_152_1 = ove_0_140(arg_152_0.pos)

			if slot_152_1 then
				ove_0_30[arg_152_0.ptr].target = slot_152_1
				ove_0_30[arg_152_0.ptr].pos = slot_152_1.pos
			end
		end
	end

	if arg_152_0 and not ove_0_30[arg_152_0.ptr] and ove_0_71(arg_152_0.name, "TwistedFate") and ove_0_71(arg_152_0.name, "R_Gatemarker_Red") then
		ove_0_30[arg_152_0.ptr] = {}
		ove_0_30[arg_152_0.ptr].expireTime = game.time + 1.5
		ove_0_30[arg_152_0.ptr].spellPos = vec3(arg_152_0.pos.x, arg_152_0.pos.y, arg_152_0.pos.y)
		ove_0_30[arg_152_0.ptr].target = ove_0_157
		ove_0_30[arg_152_0.ptr].pos = vec3(arg_152_0.pos.x, arg_152_0.pos.y, arg_152_0.pos.z)
	end

	if arg_152_0 and not ove_0_30[arg_152_0.ptr] and ove_0_71(arg_152_0.name, "TahmKench") and ove_0_71(arg_152_0.name, "W_ImpactWarning") then
		ove_0_30[arg_152_0.ptr] = {}
		ove_0_30[arg_152_0.ptr].expireTime = game.time + 2.1
		ove_0_30[arg_152_0.ptr].target = ove_0_156
		ove_0_30[arg_152_0.ptr].pos = vec3(arg_152_0.pos.x, arg_152_0.pos.y, arg_152_0.pos.z)
	end

	if arg_152_0 and ove_0_71(arg_152_0.name, "Lissandra") and ove_0_71(arg_152_0.name, "E_Missile") then
		ove_0_32 = arg_152_0
	end

	if ove_0_71(arg_152_0.name, "LeapSound") and ove_0_155() then
		local slot_152_2 = ove_0_25.GetDashInfo(ove_0_155().charName, ove_0_23, -1)

		if slot_152_2 then
			local slot_152_3 = {
				windup = 0.05,
				lastDelete = 0,
				start = game.time,
				owner = ove_0_155(),
				spellInfo = slot_152_2,
				begin = game.time
			}

			ove_0_28[ove_0_155().networkID] = slot_152_3
		end
	end
end

local function ove_0_162(arg_153_0)
	-- print 153
	if arg_153_0 and ove_0_38 and arg_153_0.ptr == ove_0_38.ptr then
		ove_0_38 = nil
	end

	if arg_153_0 and ove_0_160 and arg_153_0.ptr == ove_0_160.ptr then
		ove_0_160 = nil
	end

	if arg_153_0 and ove_0_150 and ove_0_150.ptr == arg_153_0.ptr then
		ove_0_150 = nil
	end

	if arg_153_0 and ove_0_32 and ove_0_32.ptr == arg_153_0.ptr then
		ove_0_32 = nil
	end
end

function ove_0_25.GetDashInfo(arg_154_0, arg_154_1, arg_154_2)
	-- print 154
	local slot_154_0 = ove_0_72(arg_154_0)

	if arg_154_1[slot_154_0] then
		for iter_154_0 = 1, #arg_154_1[slot_154_0] do
			local slot_154_1 = arg_154_1[slot_154_0][iter_154_0]

			if slot_154_1.slot == arg_154_2 then
				return slot_154_1
			end
		end
	end

	return nil
end

local function ove_0_163(arg_155_0, arg_155_1, arg_155_2, arg_155_3)
	-- print 155
	local slot_155_0 = arg_155_3 or 0
	local slot_155_1 = (arg_155_1 - arg_155_0):norm()
	local slot_155_2 = arg_155_2 * slot_155_1:perp1()

	return {
		(arg_155_0 + slot_155_2 - slot_155_0 * slot_155_1):to2D(),
		(arg_155_0 - slot_155_2 - slot_155_0 * slot_155_1):to2D(),
		(arg_155_1 - slot_155_2 + slot_155_0 * slot_155_1):to2D(),
		(arg_155_1 + slot_155_2 + slot_155_0 * slot_155_1):to2D()
	}
end

local function ove_0_164(arg_156_0, arg_156_1)
	-- print 156
	local slot_156_0 = false
	local slot_156_1 = #arg_156_0

	for iter_156_0 = 1, #arg_156_0 do
		if (arg_156_0[iter_156_0].y < arg_156_1.z and arg_156_0[slot_156_1].y >= arg_156_1.z or arg_156_0[slot_156_1].y < arg_156_1.z and arg_156_0[iter_156_0].y >= arg_156_1.z) and arg_156_0[iter_156_0].x + (arg_156_1.z - arg_156_0[iter_156_0].y) / (arg_156_0[slot_156_1].y - arg_156_0[iter_156_0].y) * (arg_156_0[slot_156_1].x - arg_156_0[iter_156_0].x) < arg_156_1.x then
			slot_156_0 = not slot_156_0
		end

		slot_156_1 = iter_156_0
	end

	return slot_156_0
end

function ove_0_25.Intersection(arg_157_0, arg_157_1, arg_157_2, arg_157_3)
	-- print 157
	local slot_157_0 = vec2(arg_157_1.x - arg_157_0.x, arg_157_1.y - arg_157_0.y)
	local slot_157_1 = vec2(arg_157_3.x - arg_157_2.x, arg_157_3.y - arg_157_2.y)
	local slot_157_2 = (-slot_157_0.y * (arg_157_0.x - arg_157_2.x) + slot_157_0.x * (arg_157_0.y - arg_157_2.y)) / (-slot_157_1.x * slot_157_0.y + slot_157_0.x * slot_157_1.y)
	local slot_157_3 = (slot_157_1.x * (arg_157_0.y - arg_157_2.y) - slot_157_1.y * (arg_157_0.x - arg_157_2.x)) / (-slot_157_1.x * slot_157_0.y + slot_157_0.x * slot_157_1.y)

	if slot_157_2 >= 0 and slot_157_2 <= 11 and slot_157_3 >= 0 and slot_157_3 <= 1 then
		return true, vec2(arg_157_0.x + slot_157_3 * slot_157_0.x, arg_157_0.y + slot_157_3 * slot_157_0.y)
	end

	return false, arg_157_0
end

local function ove_0_165(arg_158_0)
	-- print 158
	if not ove_0_26[arg_158_0.networkID] then
		ove_0_26[arg_158_0.networkID] = {}
	end

	ove_0_26[arg_158_0.networkID].AADetector = {
		EndTime = 0,
		Winddown = 0,
		StartTime = game.time
	}
end

local ove_0_166 = 0

local function ove_0_167(arg_159_0)
	-- print 159
	if arg_159_0 and arg_159_0.owner and arg_159_0.owner.team == TEAM_ENEMY and arg_159_0.slot == 1 and arg_159_0.owner.charName == "Yasuo" then
		ove_0_151 = vec3(ove_0_25.VectorExtend(arg_159_0.owner.pos, arg_159_0.endPos, -100))
	end

	if arg_159_0.owner.charName == "Yone" and arg_159_0.owner.team == TEAM_ENEMY and arg_159_0.name == "YoneE" then
		ove_0_73 = game.time + 1
	end

	if arg_159_0.owner and arg_159_0.owner.type == player.type and arg_159_0.owner.team == TEAM_ENEMY then
		if arg_159_0.owner.charName == "Yone" and not ove_0_30[arg_159_0.identifier] and arg_159_0.slot == 3 then
			ove_0_30[arg_159_0.identifier] = {}
			ove_0_30[arg_159_0.identifier].expireTime = game.time + 0.75
			ove_0_30[arg_159_0.identifier].target = arg_159_0.owner
			ove_0_30[arg_159_0.identifier].pos = vec3(ove_0_25.VectorExtend(arg_159_0.owner.pos, arg_159_0.endPos, 1000))
		end

		local slot_159_0 = ove_0_22[arg_159_0.owner.charName]

		if slot_159_0 then
			for iter_159_0, iter_159_1 in pairs(slot_159_0) do
				if iter_159_1.Slot == arg_159_0.slot or arg_159_0.slot == 45 and arg_159_0.owner.charName == "Xerath" then
					obj = arg_159_0.owner

					if not ove_0_26[obj.networkID] then
						ove_0_26[obj.networkID] = {}
					end

					x = ove_0_26[obj.networkID]
					x.SpellDetect = {
						isValid = false,
						EndTime = game.time + iter_159_1.Windup,
						StartTime = game.time
					}

					if arg_159_0.owner.charName == "Xerath" and arg_159_0.slot == 45 and arg_159_0.owner.buff.xeratharcanopulsechargeup then
						x.SpellDetect.EndTime = game.time + 0.4
					end

					if arg_159_0.owner.charName == "Akali" and arg_159_0.slot == 2 and arg_159_0.owner:spellSlot(2).name == "AkaliEb" then
						x.SpellDetect.EndTime = game.time + 0.25
					end

					if arg_159_0.owner.charName == "Akali" and arg_159_0.slot == 2 and arg_159_0.owner:spellSlot(2).name == "AkaliE" then
						x.SpellDetect.EndTime = game.time + 0.4
					end

					if arg_159_0.owner.charName == "Fizz" and arg_159_0.name == "FizzE" or arg_159_0.owner.charName == "Camille" and arg_159_0.slot == 3 or arg_159_0.owner.charName == "Evelynn" and arg_159_0.slot == 3 or arg_159_0.owner.charName == "Maokai" and arg_159_0.slot == 1 or arg_159_0.owner.charName == "Xayah" and arg_159_0.slot == 3 or arg_159_0.owner.charName == "Yuumi" and arg_159_0.slot == 1 or arg_159_0.owner.charName == "Hecarim" and arg_159_0.target == player and arg_159_0.name == "HecarimRampAttack" then
						x.DontCast = game.time + iter_159_1.Windup + 0.05
					end

					if arg_159_0.owner.charName == "MasterYi" and arg_159_0.slot == 0 then
						x.DontCast = game.time + 0.1
					end

					if arg_159_0.owner.charName == "Galio" and arg_159_0.slot == 2 then
						x.DontCast = game.time + 0.45
					end

					if arg_159_0.owner.charName == "Zoe" and arg_159_0.slot == 3 then
						x.DontCast = game.time + 1.1
					end

					if arg_159_0.owner.charName == "Yone" and arg_159_0.slot == 2 and arg_159_0.owner.mana == 0 then
						x.DontCast2 = game.time + 0.25
					end

					if arg_159_0.owner.charName == "Yone" and arg_159_0.name == "YoneQ3" then
						x.DashSoon = game.time + arg_159_0.windUpTime
					end

					if iter_159_1.IsDash == true or iter_159_1.IsBlink then
						x.DashSoon = game.time + iter_159_1.Windup
					end

					if iter_159_1.IsBlink then
						local slot_159_1

						if arg_159_0.owner.charName == "MasterYi" then
							local slot_159_2 = arg_159_0.target

							x.BlinkInfo = {
								WindupEnd = game.time + iter_159_1.Windup,
								Comeout = game.time + iter_159_1.ComeOut or 0,
								Owner = slot_159_2
							}
						end

						if arg_159_0.owner.charName == "Zed" and arg_159_0.name == "ZedR" then
							local slot_159_3 = arg_159_0.target

							x.BlinkInfo = {
								WindupEnd = game.time + iter_159_1.Windup,
								Comeout = game.time + iter_159_1.ComeOut or 0,
								Owner = slot_159_3
							}
						end
					end
				end
			end
		elseif ove_0_22[fb.charName][1].Slot then
			-- block empty
		end
	end

	if arg_159_0.owner and arg_159_0.owner.type == player.type then
		obj = arg_159_0.owner

		if arg_159_0.isBasicAttack then
			if not ove_0_26[obj.networkID] then
				ove_0_26[obj.networkID] = {}
			end

			x = ove_0_26[obj.networkID]
			x.AADetector = {
				EndTime = game.time + arg_159_0.windUpTime,
				StartTime = game.time,
				Winddown = game.time + arg_159_0.owner:basicAttack(0).animationTime
			}
		end
	end

	if arg_159_0 and arg_159_0.owner and arg_159_0.owner.team == TEAM_ENEMY then
		local slot_159_4 = ove_0_25.GetDashInfo(arg_159_0.owner.charName, ove_0_23, arg_159_0.slot)

		if arg_159_0.name == "EkkoEAttack" then
			local slot_159_5 = ove_0_25.GetDashInfo(arg_159_0.owner.charName, ove_0_23, 2)

			if slot_159_5 then
				local slot_159_6 = {
					start = game.time,
					owner = arg_159_0.owner,
					lastDelete = game.time + 0.4,
					startPos = vec3(arg_159_0.owner.pos),
					endPos = vec3(arg_159_0.endPos),
					windup = arg_159_0.windUpTime,
					spellInfo = slot_159_5,
					begin = game.time
				}

				ove_0_28[arg_159_0.owner.networkID] = slot_159_6
			end
		end

		if arg_159_0.owner.charName == "Ivern" and arg_159_0.slot == 0 then
			local slot_159_7 = ove_0_25.GetDashInfo(arg_159_0.owner.charName, ove_0_23, 0)

			if slot_159_7 then
				local slot_159_8 = {
					start = game.time,
					owner = arg_159_0.owner,
					lastDelete = game.time + 3,
					windup = arg_159_0.windUpTime,
					spellInfo = slot_159_7,
					begin = game.time
				}

				ove_0_28[arg_159_0.owner.networkID] = slot_159_8
			end
		end

		if arg_159_0.name == "IllaoiWAttack" then
			local slot_159_9 = ove_0_25.GetDashInfo(arg_159_0.owner.charName, ove_0_23, 1)

			if slot_159_9 then
				local slot_159_10 = {
					start = game.time,
					owner = arg_159_0.owner,
					lastDelete = game.time + 0.5,
					startPos = vec3(arg_159_0.owner.pos),
					endPos = vec3(arg_159_0.endPos),
					windup = arg_159_0.windUpTime,
					spellInfo = slot_159_9,
					begin = game.time
				}

				ove_0_28[arg_159_0.owner.networkID] = slot_159_10
			end
		end

		if arg_159_0.owner.charName == "JarvanIV" and arg_159_0.slot == 0 then
			local slot_159_11 = ove_0_25.GetDashInfo(arg_159_0.owner.charName, ove_0_23, 0)

			if slot_159_11 then
				local slot_159_12 = {
					start = game.time,
					owner = arg_159_0.owner,
					lastDelete = game.time + 0.8,
					windup = arg_159_0.windUpTime,
					spellInfo = slot_159_11,
					begin = game.time
				}

				ove_0_28[arg_159_0.owner.networkID] = slot_159_12
			end
		end

		if arg_159_0.owner.charName ~= "Ezreal" and arg_159_0.owner.charName ~= "Shaco" and arg_159_0.owner.charName ~= "FiddleSticks" and arg_159_0.owner.charName ~= "Illaoi" and arg_159_0.owner.charName ~= "Ivern" and arg_159_0.owner.charName ~= "RekSai" and (arg_159_0.owner.charName ~= "JarvanIV" or arg_159_0.slot ~= 0) and (arg_159_0.owner.charName ~= "LeBlanc" or arg_159_0.slot ~= 3) and arg_159_0.owner.charName ~= "Galio " and arg_159_0.owner.charName ~= "Kassadin" and arg_159_0.owner.charName ~= "Maokai" and arg_159_0.owner.charName ~= "MasterYi" and arg_159_0.owner.charName ~= "Ziggs" and arg_159_0.owner.charName ~= "Katarina" and arg_159_0.owner.charName ~= "Lissandra" and (arg_159_0.owner.charName ~= "Riven" or arg_159_0.slot == 2) and (arg_159_0.owner.charName ~= "MonkeyKing" or arg_159_0.slot == 2) and (arg_159_0.owner.charName ~= "Yone" or arg_159_0.slot == 0) and slot_159_4 then
			local slot_159_13 = {
				lastDelete = 0,
				start = game.time,
				owner = arg_159_0.owner,
				windup = arg_159_0.windUpTime,
				spellInfo = slot_159_4,
				begin = game.time
			}

			ove_0_28[arg_159_0.owner.networkID] = slot_159_13
		end

		if arg_159_0.owner.charName == "Riven" and arg_159_0.slot == 0 then
			local slot_159_14 = "Q1"

			if arg_159_0.owner.buff.riventricleave then
				if arg_159_0.owner.buff.riventricleave.stacks == 1 then
					slot_159_14 = "Q2"
				end

				if arg_159_0.owner.buff.riventricleave.stacks == 2 then
					slot_159_14 = "Q3"
				end
			end

			if slot_159_4 then
				local slot_159_15 = {
					lastDelete = 0,
					start = game.time,
					owner = arg_159_0.owner,
					windup = arg_159_0.windUpTime,
					spellInfo = slot_159_4,
					QType = slot_159_14,
					begin = game.time
				}

				ove_0_28[arg_159_0.owner.networkID] = slot_159_15
			end
		end

		if arg_159_0.owner.charName == "Lissandra" and ove_0_32 and slot_159_4 then
			local slot_159_16 = {
				isBlink = true,
				start = game.time,
				owner = arg_159_0.owner,
				lastDelete = game.time + 0.25,
				startPos = vec3(arg_159_0.owner.pos),
				endPos = vec3(ove_0_32.pos),
				spellInfo = slot_159_4,
				windup = arg_159_0.windUpTime,
				begin = game.time
			}

			ove_0_28[arg_159_0.owner.networkID] = slot_159_16
		end

		if arg_159_0.owner.charName == "LeBlanc" and arg_159_0.name == "LeblancRW" and slot_159_4 then
			local slot_159_17 = {
				start = game.time,
				owner = arg_159_0.owner,
				lastDelete = game.time,
				spellInfo = slot_159_4,
				windup = arg_159_0.windUpTime,
				begin = game.time
			}

			ove_0_28[arg_159_0.owner.networkID] = slot_159_17
		end

		if arg_159_0.owner.charName == "MonkeyKing" and arg_159_0.slot == 1 and slot_159_4 then
			local slot_159_18 = {
				isBlink = true,
				start = game.time,
				owner = arg_159_0.owner,
				lastDelete = game.time + 1,
				startPos = vec3(arg_159_0.owner.pos),
				endPos = arg_159_0.endPos:dist(arg_159_0.owner.pos) > 300 and vec3(ove_0_25.VectorExtend(arg_159_0.owner.pos, arg_159_0.endPos, 300)) or arg_159_0.endPos:dist(arg_159_0.owner.pos) <= arg_159_0.owner.boundingRadius and vec3(ove_0_25.VectorExtend(arg_159_0.owner.pos, arg_159_0.endPos, 200)) or arg_159_0.endPos,
				spellInfo = slot_159_4,
				windup = arg_159_0.windUpTime,
				begin = game.time
			}

			ove_0_28[arg_159_0.owner.networkID] = slot_159_18
		end

		if arg_159_0.owner.charName == "Ziggs" and arg_159_0.name == "ZiggsWToggle" and slot_159_4 then
			local slot_159_19 = {
				start = game.time,
				owner = arg_159_0.owner,
				lastDelete = game.time + 0.8,
				spellInfo = slot_159_4,
				windup = arg_159_0.windUpTime,
				begin = game.time
			}

			ove_0_28[arg_159_0.owner.networkID] = slot_159_19
		end

		if arg_159_0.owner.charName == "RekSai" and slot_159_4 then
			local slot_159_20 = {
				start = game.time,
				owner = arg_159_0.owner,
				lastDelete = game.time + 1.7,
				windup = arg_159_0.windUpTime,
				spellInfo = slot_159_4,
				begin = game.time + 0.4
			}

			ove_0_28[arg_159_0.owner.networkID] = slot_159_20
		end

		if arg_159_0.owner.charName == "Galio" and slot_159_4 then
			local slot_159_21 = {
				lastDelete = 0,
				start = game.time,
				owner = arg_159_0.owner,
				windup = arg_159_0.windUpTime,
				spellInfo = slot_159_4,
				begin = game.time + 0.5
			}

			ove_0_28[arg_159_0.owner.networkID] = slot_159_21
		end

		if arg_159_0.owner.charName == "Katarina" and slot_159_4 then
			local slot_159_22 = {
				isBlink = true,
				start = game.time,
				owner = arg_159_0.owner,
				lastDelete = game.time + 0.25,
				startPos = vec3(arg_159_0.owner.pos),
				windup = arg_159_0.windUpTime,
				endPos = vec3(arg_159_0.endPos),
				spellInfo = slot_159_4,
				begin = game.time
			}

			ove_0_28[arg_159_0.owner.networkID] = slot_159_22
		end

		if arg_159_0.owner.charName == "Kassadin" and slot_159_4 then
			local slot_159_23 = {
				isBlink = true,
				start = game.time,
				windup = arg_159_0.windUpTime,
				owner = arg_159_0.owner,
				lastDelete = game.time + 0.5,
				startPos = vec3(arg_159_0.owner.pos),
				endPos = arg_159_0.endPos:dist(arg_159_0.owner.pos) > 500 and vec3(ove_0_25.VectorExtend(arg_159_0.owner.pos, arg_159_0.endPos, 500)) or vec3(arg_159_0.endPos),
				spellInfo = slot_159_4,
				begin = game.time
			}

			ove_0_28[arg_159_0.owner.networkID] = slot_159_23
		end

		if arg_159_0.owner.charName == "Maokai" and slot_159_4 then
			local slot_159_24 = {
				start = game.time,
				owner = arg_159_0.owner,
				windup = arg_159_0.windUpTime,
				lastDelete = game.time + 0.8,
				startPos = vec3(arg_159_0.owner.pos),
				endPos = vec3(arg_159_0.endPos),
				spellInfo = slot_159_4,
				begin = game.time + 0.25
			}

			ove_0_28[arg_159_0.owner.networkID] = slot_159_24
		end

		if arg_159_0.owner.charName == "MasterYi" and slot_159_4 then
			local slot_159_25 = {
				isBlink = true,
				start = game.time,
				owner = arg_159_0.owner,
				lastDelete = game.time + 1.5,
				windup = arg_159_0.windUpTime,
				startPos = vec3(arg_159_0.owner.pos),
				endPos = vec3(arg_159_0.endPos),
				spellInfo = slot_159_4,
				begin = game.time + 0.5
			}

			ove_0_28[arg_159_0.owner.networkID] = slot_159_25
		end

		if arg_159_0.owner.charName == "Ezreal" and slot_159_4 then
			local slot_159_26 = {
				isBlink = true,
				start = game.time,
				owner = arg_159_0.owner,
				lastDelete = game.time + 0.5,
				windup = arg_159_0.windUpTime,
				startPos = vec3(arg_159_0.owner.pos),
				endPos = arg_159_0.endPos:dist(arg_159_0.owner.pos) > 475 and vec3(ove_0_25.VectorExtend(arg_159_0.owner.pos, arg_159_0.endPos, 475)) or vec3(arg_159_0.endPos),
				spellInfo = slot_159_4,
				begin = game.time
			}

			ove_0_28[arg_159_0.owner.networkID] = slot_159_26
		end

		if arg_159_0.owner.charName == "Yone" and arg_159_0.slot == 3 and slot_159_4 then
			local slot_159_27 = {
				isBlink = true,
				start = game.time,
				owner = arg_159_0.owner,
				lastDelete = game.time + 1.2,
				windup = arg_159_0.windUpTime,
				startPos = vec3(arg_159_0.owner.pos),
				endPos = vec3(ove_0_25.VectorExtend(arg_159_0.owner.pos, arg_159_0.endPos, 1000)),
				spellInfo = slot_159_4,
				begin = game.time
			}

			ove_0_28[arg_159_0.owner.networkID] = slot_159_27
		end

		if arg_159_0.owner.charName == "Shaco" and slot_159_4 then
			local slot_159_28 = {
				isBlink = true,
				start = game.time,
				owner = arg_159_0.owner,
				lastDelete = game.time + 0.5,
				windup = arg_159_0.windUpTime,
				startPos = vec3(arg_159_0.owner.pos),
				endPos = arg_159_0.endPos:dist(arg_159_0.owner.pos) > 400 and vec3(ove_0_25.VectorExtend(arg_159_0.owner.pos, arg_159_0.endPos, 400)) or vec3(arg_159_0.endPos),
				spellInfo = slot_159_4,
				begin = game.time
			}

			ove_0_28[arg_159_0.owner.networkID] = slot_159_28
		end

		if arg_159_0.owner.charName == "FiddleSticks" and slot_159_4 then
			local slot_159_29 = {
				isBlink = true,
				start = game.time,
				owner = arg_159_0.owner,
				lastDelete = game.time + 2,
				windup = arg_159_0.windUpTime,
				startPos = vec3(arg_159_0.owner.pos),
				endPos = arg_159_0.endPos:dist(arg_159_0.owner.pos) > 800 and vec3(ove_0_25.VectorExtend(arg_159_0.owner.pos, arg_159_0.endPos, 800)) or vec3(arg_159_0.endPos),
				spellInfo = slot_159_4,
				begin = game.time
			}

			ove_0_28[arg_159_0.owner.networkID] = slot_159_29
		end

		if arg_159_0.owner.charName == "Zed" and slot_159_4 then
			if arg_159_0.name == "ZedW" then
				ove_0_124 = game.time + 1
			end

			if arg_159_0.name == "ZedR" then
				ove_0_123 = game.time + 0.25

				local slot_159_30 = {
					isBlink = true,
					start = game.time,
					owner = arg_159_0.owner,
					lastDelete = game.time + 1.5,
					windup = arg_159_0.windUpTime,
					startPos = vec3(arg_159_0.owner.pos),
					endPos = vec3(arg_159_0.endPos),
					spellInfo = slot_159_4,
					begin = game.time + 1
				}

				ove_0_28[arg_159_0.owner.networkID] = slot_159_30
			end

			if arg_159_0.name == "ZedR2" and ove_0_122 then
				local slot_159_31 = {
					isBlink = true,
					start = game.time,
					owner = arg_159_0.owner,
					lastDelete = game.time + 0.5,
					windup = arg_159_0.windUpTime,
					startPos = vec3(arg_159_0.owner.pos),
					endPos = vec3(ove_0_122.pos),
					spellInfo = slot_159_4,
					begin = game.time
				}

				ove_0_28[arg_159_0.owner.networkID] = slot_159_31
			end

			if arg_159_0.name == "ZedW2" and ove_0_121 then
				local slot_159_32 = {
					isBlink = true,
					start = game.time,
					owner = arg_159_0.owner,
					lastDelete = game.time + 0.5,
					startPos = vec3(arg_159_0.owner.pos),
					endPos = vec3(ove_0_121.pos),
					windup = arg_159_0.windUpTime,
					spellInfo = slot_159_4,
					begin = game.time
				}

				ove_0_28[arg_159_0.owner.networkID] = slot_159_32
			end
		end
	end

	if arg_159_0 and arg_159_0.owner and arg_159_0.owner.team == TEAM_ALLY then
		local slot_159_33 = ove_0_25.GetDashInfo(arg_159_0.owner.charName, ove_0_23, arg_159_0.slot)

		if arg_159_0.name == "EkkoEAttack" then
			local slot_159_34 = ove_0_25.GetDashInfo(arg_159_0.owner.charName, ove_0_23, 2)

			if slot_159_34 then
				local slot_159_35 = {
					start = game.time,
					owner = arg_159_0.owner,
					lastDelete = game.time + 0.4,
					startPos = vec3(arg_159_0.owner.pos),
					endPos = vec3(arg_159_0.endPos),
					windup = arg_159_0.windUpTime,
					spellInfo = slot_159_34,
					begin = game.time
				}

				ove_0_29[arg_159_0.owner.networkID] = slot_159_35
			end
		end

		if arg_159_0.owner.charName == "Ivern" and arg_159_0.slot == 0 then
			local slot_159_36 = ove_0_25.GetDashInfo(arg_159_0.owner.charName, ove_0_23, 0)

			if slot_159_36 then
				local slot_159_37 = {
					start = game.time,
					owner = arg_159_0.owner,
					lastDelete = game.time + 3,
					windup = arg_159_0.windUpTime,
					spellInfo = slot_159_36,
					begin = game.time
				}

				ove_0_29[arg_159_0.owner.networkID] = slot_159_37
			end
		end

		if arg_159_0.name == "IllaoiWAttack" then
			local slot_159_38 = ove_0_25.GetDashInfo(arg_159_0.owner.charName, ove_0_23, 1)

			if slot_159_38 then
				local slot_159_39 = {
					start = game.time,
					owner = arg_159_0.owner,
					lastDelete = game.time + 0.5,
					startPos = vec3(arg_159_0.owner.pos),
					endPos = vec3(arg_159_0.endPos),
					windup = arg_159_0.windUpTime,
					spellInfo = slot_159_38,
					begin = game.time
				}

				ove_0_29[arg_159_0.owner.networkID] = slot_159_39
			end
		end

		if arg_159_0.owner.charName == "JarvanIV" and arg_159_0.slot == 0 then
			local slot_159_40 = ove_0_25.GetDashInfo(arg_159_0.owner.charName, ove_0_23, 0)

			if slot_159_40 then
				local slot_159_41 = {
					start = game.time,
					owner = arg_159_0.owner,
					lastDelete = game.time + 0.8,
					windup = arg_159_0.windUpTime,
					spellInfo = slot_159_40,
					begin = game.time
				}

				ove_0_29[arg_159_0.owner.networkID] = slot_159_41
			end
		end

		if arg_159_0.owner.charName ~= "Ezreal" and arg_159_0.owner.charName ~= "Shaco" and arg_159_0.owner.charName ~= "FiddleSticks" and arg_159_0.owner.charName ~= "Illaoi" and arg_159_0.owner.charName ~= "Ivern" and arg_159_0.owner.charName ~= "RekSai" and (arg_159_0.owner.charName ~= "JarvanIV" or arg_159_0.slot ~= 0) and (arg_159_0.owner.charName ~= "LeBlanc" or arg_159_0.slot ~= 3) and arg_159_0.owner.charName ~= "Galio " and arg_159_0.owner.charName ~= "Kassadin" and arg_159_0.owner.charName ~= "Maokai" and arg_159_0.owner.charName ~= "MasterYi" and arg_159_0.owner.charName ~= "Ziggs" and arg_159_0.owner.charName ~= "Katarina" and arg_159_0.owner.charName ~= "Lissandra" and (arg_159_0.owner.charName ~= "Riven" or arg_159_0.slot == 2) and (arg_159_0.owner.charName ~= "MonkeyKing" or arg_159_0.slot == 2) and (arg_159_0.owner.charName ~= "Yone" or arg_159_0.slot == 0) and slot_159_33 then
			local slot_159_42 = {
				lastDelete = 0,
				start = game.time,
				owner = arg_159_0.owner,
				windup = arg_159_0.windUpTime,
				spellInfo = slot_159_33,
				begin = game.time
			}

			ove_0_29[arg_159_0.owner.networkID] = slot_159_42
		end

		if arg_159_0.owner.charName == "Riven" and arg_159_0.slot == 0 then
			local slot_159_43 = "Q1"

			if arg_159_0.owner.buff.riventricleave then
				if arg_159_0.owner.buff.riventricleave.stacks == 1 then
					slot_159_43 = "Q2"
				end

				if arg_159_0.owner.buff.riventricleave.stacks == 2 then
					slot_159_43 = "Q3"
				end
			end

			if slot_159_33 then
				local slot_159_44 = {
					lastDelete = 0,
					start = game.time,
					owner = arg_159_0.owner,
					windup = arg_159_0.windUpTime,
					spellInfo = slot_159_33,
					QType = slot_159_43,
					begin = game.time
				}

				ove_0_29[arg_159_0.owner.networkID] = slot_159_44
			end
		end

		if arg_159_0.owner.charName == "Lissandra" and ove_0_32 and slot_159_33 then
			local slot_159_45 = {
				isBlink = true,
				start = game.time,
				owner = arg_159_0.owner,
				lastDelete = game.time + 0.25,
				startPos = vec3(arg_159_0.owner.pos),
				endPos = vec3(ove_0_32.pos),
				spellInfo = slot_159_33,
				windup = arg_159_0.windUpTime,
				begin = game.time
			}

			ove_0_29[arg_159_0.owner.networkID] = slot_159_45
		end

		if arg_159_0.owner.charName == "LeBlanc" and arg_159_0.name == "LeblancRW" and slot_159_33 then
			local slot_159_46 = {
				start = game.time,
				owner = arg_159_0.owner,
				lastDelete = game.time,
				spellInfo = slot_159_33,
				windup = arg_159_0.windUpTime,
				begin = game.time
			}

			ove_0_29[arg_159_0.owner.networkID] = slot_159_46
		end

		if arg_159_0.owner.charName == "MonkeyKing" and arg_159_0.slot == 1 and slot_159_33 then
			local slot_159_47 = {
				isBlink = true,
				start = game.time,
				owner = arg_159_0.owner,
				lastDelete = game.time + 1,
				startPos = vec3(arg_159_0.owner.pos),
				endPos = arg_159_0.endPos:dist(arg_159_0.owner.pos) > 300 and vec3(ove_0_25.VectorExtend(arg_159_0.owner.pos, arg_159_0.endPos, 300)) or arg_159_0.endPos:dist(arg_159_0.owner.pos) <= arg_159_0.owner.boundingRadius and vec3(ove_0_25.VectorExtend(arg_159_0.owner.pos, arg_159_0.endPos, 200)) or arg_159_0.endPos,
				spellInfo = slot_159_33,
				windup = arg_159_0.windUpTime,
				begin = game.time
			}

			ove_0_29[arg_159_0.owner.networkID] = slot_159_47
		end

		if arg_159_0.owner.charName == "Ziggs" and arg_159_0.name == "ZiggsWToggle" and slot_159_33 then
			local slot_159_48 = {
				start = game.time,
				owner = arg_159_0.owner,
				lastDelete = game.time + 0.8,
				spellInfo = slot_159_33,
				windup = arg_159_0.windUpTime,
				begin = game.time
			}

			ove_0_29[arg_159_0.owner.networkID] = slot_159_48
		end

		if arg_159_0.owner.charName == "RekSai" and slot_159_33 then
			local slot_159_49 = {
				start = game.time,
				owner = arg_159_0.owner,
				lastDelete = game.time + 1.7,
				windup = arg_159_0.windUpTime,
				spellInfo = slot_159_33,
				begin = game.time + 0.4
			}

			ove_0_29[arg_159_0.owner.networkID] = slot_159_49
		end

		if arg_159_0.owner.charName == "Galio" and slot_159_33 then
			local slot_159_50 = {
				lastDelete = 0,
				start = game.time,
				owner = arg_159_0.owner,
				windup = arg_159_0.windUpTime,
				spellInfo = slot_159_33,
				begin = game.time + 0.5
			}

			ove_0_29[arg_159_0.owner.networkID] = slot_159_50
		end

		if arg_159_0.owner.charName == "Katarina" and slot_159_33 then
			local slot_159_51 = {
				isBlink = true,
				start = game.time,
				owner = arg_159_0.owner,
				lastDelete = game.time + 0.25,
				startPos = vec3(arg_159_0.owner.pos),
				windup = arg_159_0.windUpTime,
				endPos = vec3(arg_159_0.endPos),
				spellInfo = slot_159_33,
				begin = game.time
			}

			ove_0_29[arg_159_0.owner.networkID] = slot_159_51
		end

		if arg_159_0.owner.charName == "Kassadin" and slot_159_33 then
			local slot_159_52 = {
				isBlink = true,
				start = game.time,
				windup = arg_159_0.windUpTime,
				owner = arg_159_0.owner,
				lastDelete = game.time + 0.5,
				startPos = vec3(arg_159_0.owner.pos),
				endPos = arg_159_0.endPos:dist(arg_159_0.owner.pos) > 500 and vec3(ove_0_25.VectorExtend(arg_159_0.owner.pos, arg_159_0.endPos, 500)) or vec3(arg_159_0.endPos),
				spellInfo = slot_159_33,
				begin = game.time
			}

			ove_0_29[arg_159_0.owner.networkID] = slot_159_52
		end

		if arg_159_0.owner.charName == "Maokai" and slot_159_33 then
			local slot_159_53 = {
				start = game.time,
				owner = arg_159_0.owner,
				windup = arg_159_0.windUpTime,
				lastDelete = game.time + 0.8,
				startPos = vec3(arg_159_0.owner.pos),
				endPos = vec3(arg_159_0.endPos),
				spellInfo = slot_159_33,
				begin = game.time + 0.25
			}

			ove_0_29[arg_159_0.owner.networkID] = slot_159_53
		end

		if arg_159_0.owner.charName == "MasterYi" and slot_159_33 then
			local slot_159_54 = {
				isBlink = true,
				start = game.time,
				owner = arg_159_0.owner,
				lastDelete = game.time + 1.5,
				windup = arg_159_0.windUpTime,
				startPos = vec3(arg_159_0.owner.pos),
				endPos = vec3(arg_159_0.endPos),
				spellInfo = slot_159_33,
				begin = game.time + 0.5
			}

			ove_0_29[arg_159_0.owner.networkID] = slot_159_54
		end

		if arg_159_0.owner.charName == "Ezreal" and slot_159_33 then
			local slot_159_55 = {
				isBlink = true,
				start = game.time,
				owner = arg_159_0.owner,
				lastDelete = game.time + 0.5,
				windup = arg_159_0.windUpTime,
				startPos = vec3(arg_159_0.owner.pos),
				endPos = arg_159_0.endPos:dist(arg_159_0.owner.pos) > 475 and vec3(ove_0_25.VectorExtend(arg_159_0.owner.pos, arg_159_0.endPos, 475)) or vec3(arg_159_0.endPos),
				spellInfo = slot_159_33,
				begin = game.time
			}

			ove_0_29[arg_159_0.owner.networkID] = slot_159_55
		end

		if arg_159_0.owner.charName == "Yone" and arg_159_0.slot == 3 and slot_159_33 then
			local slot_159_56 = {
				isBlink = true,
				start = game.time,
				owner = arg_159_0.owner,
				lastDelete = game.time + 1.2,
				windup = arg_159_0.windUpTime,
				startPos = vec3(arg_159_0.owner.pos),
				endPos = vec3(ove_0_25.VectorExtend(arg_159_0.owner.pos, arg_159_0.endPos, 1000)),
				spellInfo = slot_159_33,
				begin = game.time
			}

			ove_0_29[arg_159_0.owner.networkID] = slot_159_56
		end

		if arg_159_0.owner.charName == "Shaco" and slot_159_33 then
			local slot_159_57 = {
				isBlink = true,
				start = game.time,
				owner = arg_159_0.owner,
				lastDelete = game.time + 0.5,
				windup = arg_159_0.windUpTime,
				startPos = vec3(arg_159_0.owner.pos),
				endPos = arg_159_0.endPos:dist(arg_159_0.owner.pos) > 400 and vec3(ove_0_25.VectorExtend(arg_159_0.owner.pos, arg_159_0.endPos, 400)) or vec3(arg_159_0.endPos),
				spellInfo = slot_159_33,
				begin = game.time
			}

			ove_0_29[arg_159_0.owner.networkID] = slot_159_57
		end

		if arg_159_0.owner.charName == "FiddleSticks" and slot_159_33 then
			local slot_159_58 = {
				isBlink = true,
				start = game.time,
				owner = arg_159_0.owner,
				lastDelete = game.time + 2,
				windup = arg_159_0.windUpTime,
				startPos = vec3(arg_159_0.owner.pos),
				endPos = arg_159_0.endPos:dist(arg_159_0.owner.pos) > 800 and vec3(ove_0_25.VectorExtend(arg_159_0.owner.pos, arg_159_0.endPos, 800)) or vec3(arg_159_0.endPos),
				spellInfo = slot_159_33,
				begin = game.time
			}

			ove_0_29[arg_159_0.owner.networkID] = slot_159_58
		end

		if arg_159_0.owner.charName == "Zed" and slot_159_33 then
			if arg_159_0.name == "ZedW" then
				ove_0_124 = game.time + 1
			end

			if arg_159_0.name == "ZedR" then
				ove_0_123 = game.time + 0.25

				local slot_159_59 = {
					isBlink = true,
					start = game.time,
					owner = arg_159_0.owner,
					lastDelete = game.time + 1.5,
					windup = arg_159_0.windUpTime,
					startPos = vec3(arg_159_0.owner.pos),
					endPos = vec3(arg_159_0.endPos),
					spellInfo = slot_159_33,
					begin = game.time + 1
				}

				ove_0_29[arg_159_0.owner.networkID] = slot_159_59
			end

			if arg_159_0.name == "ZedR2" and ove_0_122 then
				local slot_159_60 = {
					isBlink = true,
					start = game.time,
					owner = arg_159_0.owner,
					lastDelete = game.time + 0.5,
					windup = arg_159_0.windUpTime,
					startPos = vec3(arg_159_0.owner.pos),
					endPos = vec3(ove_0_122.pos),
					spellInfo = slot_159_33,
					begin = game.time
				}

				ove_0_29[arg_159_0.owner.networkID] = slot_159_60
			end

			if arg_159_0.name == "ZedW2" and ove_0_121 then
				local slot_159_61 = {
					isBlink = true,
					start = game.time,
					owner = arg_159_0.owner,
					lastDelete = game.time + 0.5,
					startPos = vec3(arg_159_0.owner.pos),
					endPos = vec3(ove_0_121.pos),
					windup = arg_159_0.windUpTime,
					spellInfo = slot_159_33,
					begin = game.time
				}

				ove_0_29[arg_159_0.owner.networkID] = slot_159_61
			end
		end
	end
end

local function ove_0_168(arg_160_0, arg_160_1, arg_160_2)
	-- print 160
	if not ove_0_26[arg_160_0.networkID] then
		ove_0_26[arg_160_0.networkID] = {}
	end

	x = ove_0_26[arg_160_0.networkID]

	local slot_160_0 = x.PathBank

	if #slot_160_0 == 3 and ove_0_37(slot_160_0[3].Position[slot_160_0[3].Count - 1], arg_160_1[arg_160_2 - 1]) <= 100 and (arg_160_0.team ~= TEAM_ENEMY or not arg_160_0.isOnScreen or true) then
		return
	end

	slot_160_0[#slot_160_0 + 1] = {
		Position = arg_160_1,
		Count = arg_160_2,
		Time = game.time,
		Active = arg_160_0.path.isActive,
		Dir = arg_160_0.direction
	}

	if #slot_160_0 > 3 then
		ove_0_42(slot_160_0, 1)
	end
end

local function ove_0_169(arg_161_0)
	-- print 161
	if arg_161_0.type == player.type and arg_161_0.path.count >= 0 then
		local slot_161_0, slot_161_1 = arg_161_0.path:calcPos(arg_161_0.path.point[arg_161_0.path.count])

		if ove_0_26[arg_161_0.networkID] then
			x = ove_0_26[arg_161_0.networkID]

			if x.SpellDetect.StartTime and game.time - x.SpellDetect.StartTime <= 0.08 and arg_161_0.path.count == 0 then
				x.SpellDetect.isValid = true
			end
		end

		if not ove_0_26[arg_161_0.networkID] then
			ove_0_26[arg_161_0.networkID] = {}
		end

		local slot_161_2 = ove_0_26[arg_161_0.networkID]

		ove_0_168(arg_161_0, slot_161_0, slot_161_1)

		if slot_161_1 > 1 and ove_0_37(slot_161_0[slot_161_1 - 1], slot_161_0[0]) > 1 then
			slot_161_2.NewPathTick = game.time
		else
			slot_161_2.StopMoveTick = game.time
		end

		ove_0_41(slot_161_2.PosBank, {
			Position = vec3(arg_161_0.pos),
			Time = game.time
		})

		if #slot_161_2.PosBank > 3 then
			ove_0_42(slot_161_2.PosBank, 1)
		end

		if #slot_161_2.PathBank < 3 then
			return false
		end

		local slot_161_3 = slot_161_2.PathBank

		if #slot_161_3 < 3 then
			return false
		end

		if game.time - slot_161_3[3].Time > 0.7 then
			return
		end

		if slot_161_3[3].Time - slot_161_3[2].Time > 0.7 then
			return
		end

		local slot_161_4 = slot_161_3[1].Position[slot_161_3[1].Count - 1]
		local slot_161_5 = slot_161_3[2].Position[slot_161_3[2].Count - 1]
		local slot_161_6 = slot_161_3[3].Position[slot_161_3[3].Count - 1]
		local slot_161_7 = arg_161_0.pos
		local slot_161_8 = (slot_161_6.x - slot_161_7.x)^2 + (slot_161_6.z - slot_161_7.z)^2
		local slot_161_9 = (slot_161_7.x - slot_161_5.x)^2 + (slot_161_7.z - slot_161_5.z)^2
		local slot_161_10 = (slot_161_6.x - slot_161_5.x)^2 + (slot_161_6.z - slot_161_5.z)^2
		local slot_161_11 = ove_0_70(mathf.angle_between(arg_161_0, slot_161_5, slot_161_6))
		local slot_161_12 = ove_0_70(mathf.angle_between(arg_161_0, slot_161_4, slot_161_6))

		if ove_0_69(slot_161_12) > 50 then
			slot_161_2.SpamClickCount2 = slot_161_2.SpamClickCount2 + 1
			slot_161_2.LastSpamClick2 = game.time
		else
			slot_161_2.SpamClickCount2 = 0
		end

		if ove_0_69(slot_161_11) > 100 then
			slot_161_2.SpamClickCount = slot_161_2.SpamClickCount + 1
			slot_161_2.LastSpamClick = game.time
		else
			slot_161_2.SpamClickCount = 0
		end
	else
		x.SpamClickCount = 0
	end
end

function DrawPolygon(arg_162_0)
	-- print 162
	graphics.draw_line(arg_162_0[#arg_162_0]:to3D(player.pos.y), arg_162_0[1]:to3D(player.pos.y), 2, 4294967295)

	for iter_162_0 = 1, #arg_162_0 - 1 do
		graphics.draw_line(arg_162_0[iter_162_0]:to3D(player.pos.y), arg_162_0[iter_162_0 + 1]:to3D(player.pos.y), 2, 4294967295)
	end
end

local function ove_0_170(arg_163_0, arg_163_1)
	-- print 163
	return arg_163_0.x * arg_163_1.y - arg_163_0.y * arg_163_1.x
end

local function ove_0_171(arg_164_0, arg_164_1, arg_164_2, arg_164_3)
	-- print 164
	local slot_164_0 = arg_164_1 - arg_164_0
	local slot_164_1 = arg_164_3 - arg_164_2
	local slot_164_2 = ove_0_170(slot_164_0, slot_164_1)
	local slot_164_3 = ove_0_170(arg_164_2 - arg_164_0, slot_164_1) / slot_164_2
	local slot_164_4 = ove_0_170(arg_164_2 - arg_164_0, slot_164_0) / slot_164_2

	return slot_164_2 ~= 0 and slot_164_3 >= 0 and slot_164_3 <= 1 and slot_164_4 >= 0 and slot_164_4 <= 1 and vec2(arg_164_0 + slot_164_3 * slot_164_0) or nil
end

local function ove_0_172(arg_165_0)
	-- print 165
	return arg_165_0 < 0 and ove_0_68(arg_165_0 - 0.5) or ove_0_67(arg_165_0 + 0.5)
end

function Round2(arg_166_0)
	-- print 166
	local slot_166_0 = vec2(arg_166_0.x, arg_166_0.y)

	slot_166_0.x, slot_166_0.y = ove_0_172(slot_166_0.x), ove_0_172(slot_166_0.y)

	return slot_166_0
end

local function ove_0_173(arg_167_0, arg_167_1, arg_167_2)
	-- print 167
	local slot_167_0 = {}

	for iter_167_0 = 1, #arg_167_0 do
		local slot_167_1 = arg_167_0[iter_167_0]
		local slot_167_2 = arg_167_0[iter_167_0 == #arg_167_0 and 1 or iter_167_0 + 1]

		if ove_0_171(slot_167_1, slot_167_2, arg_167_1, arg_167_2) then
			return true
		end
	end

	return false
end

function ove_0_25.IsCollidingWindwall(arg_168_0, arg_168_1, arg_168_2)
	-- print 168
	local slot_168_0 = arg_168_0
	local slot_168_1 = arg_168_1

	if slot_168_0.z then
		slot_168_0 = vec2(arg_168_0.x, arg_168_0.z)
	end

	if slot_168_1.z then
		slot_168_1 = vec2(arg_168_1.x, arg_168_1.z)
	end

	if ove_0_150 and ove_0_151 then
		local slot_168_2 = tonumber(string.sub(ove_0_150.name, ove_0_150.name:len(), ove_0_150.name:len()))

		if slot_168_2 then
			local slot_168_3 = arg_168_2.width

			if arg_168_2.realWidth then
				slot_168_3 = arg_168_2.realWidth
			end

			local slot_168_4 = 450 + 70 * slot_168_2 + arg_168_2.width
			local slot_168_5 = (ove_0_150.pos - ove_0_151):norm():perp1()
			local slot_168_6 = ove_0_150.pos + slot_168_4 / 2 * slot_168_5
			local slot_168_7 = slot_168_6 - slot_168_4 * slot_168_5
			local slot_168_8 = ove_0_163(slot_168_6, slot_168_7, 90 + slot_168_3, 0)

			if ove_0_173(slot_168_8, slot_168_0, slot_168_1) then
				return true
			end
		end

		return false
	end

	return false
end

local ove_0_174

function ove_0_25.GetCaitlynRMissile()
	-- print 169
	if ove_0_174 then
		return ove_0_174
	end
end

local function ove_0_175(arg_170_0)
	-- print 170
	if arg_170_0.name == "CaitlynRMissile" and arg_170_0.spell.owner and arg_170_0.spell.owner.team == TEAM_ENEMY then
		ove_0_174 = arg_170_0
	end
end

local function ove_0_176(arg_171_0)
	-- print 171
	if arg_171_0 and ove_0_174 and arg_171_0.ptr == ove_0_174.ptr then
		ove_0_174 = nil
	end
end

cb.add(cb.create_missile, ove_0_175)
cb.add(cb.delete_missile, ove_0_176)
cb.add(cb.path, ove_0_169)
cb.add(cb.create_particle, ove_0_161)
cb.add(cb.delete_particle, ove_0_162)
cb.add(cb.spell, ove_0_167)
cb.add(cb.attack_cancel, ove_0_165)
ove_0_20.combat.register_f_pre_tick(ove_0_154)
ove_0_41(ove_0_34, ove_0_40(vec3(4126, 115.59362792969, 4174), vec3(10578, 91.991607666016, 10548), 650))
ove_0_41(ove_0_34, ove_0_40(vec3(1188, 76.253211975098, 5152), vec3(1430, 52.838096618652, 11636), 650))
ove_0_41(ove_0_34, ove_0_40(vec3(3534, 52.83810043335, 13516), vec3(9600, 74.146881103516, 13654), 650))
ove_0_41(ove_0_34, ove_0_40(vec3(13632, 97.99479675293, 9744), vec3(13442, 51.366905212402, 3286), 650))
ove_0_41(ove_0_34, ove_0_40(vec3(5108, 93.936988830566, 1222), vec3(11538, 50.7746925354, 1402), 650))

local function ove_0_177(arg_172_0, arg_172_1)
	-- print 172
	local slot_172_0 = false
	local slot_172_1 = #arg_172_0

	for iter_172_0 = 1, #arg_172_0 do
		if (arg_172_0[iter_172_0].y < arg_172_1.z and arg_172_0[slot_172_1].y >= arg_172_1.z or arg_172_0[slot_172_1].y < arg_172_1.z and arg_172_0[iter_172_0].y >= arg_172_1.z) and arg_172_0[iter_172_0].x + (arg_172_1.z - arg_172_0[iter_172_0].y) / (arg_172_0[slot_172_1].y - arg_172_0[iter_172_0].y) * (arg_172_0[slot_172_1].x - arg_172_0[iter_172_0].x) < arg_172_1.x then
			slot_172_0 = not slot_172_0
		end

		slot_172_1 = iter_172_0
	end

	return slot_172_0
end

function ove_0_25.ShouldAttackJungle(arg_173_0)
	-- print 173
	if #ove_0_25.CountMinions(player.pos, arg_173_0, "enemy") == 0 then
		return true
	end

	for iter_173_0, iter_173_1 in pairs(ove_0_34) do
		if ove_0_177(iter_173_1, player.pos) then
			return false
		end
	end

	return true
end

return ove_0_25

local util = module.load(header.id, 'katarina/util')

local rune = {}
for i = 0, 6 do
    local ru = player.rune:get(i)
    if ru and ru.id > 0 then
        rune[ru.id] = true
    end
end
Z<PERSON>huimorenLevelDamage = { 15, 25, 35, 45, 55, 65, 75, 76.25, 77.5, 78.75, 80 }
HaiyaoLevelDamage = { 35, 40, 45, 50, 55, 60, 65, 70, 75, 80, 85 }
local R1LevelDamageMod = { 0.3, 0.35, 0.4 }
local function RSpecialDamage(target,rnum)
    onhitPhysical = 0
    onhitMagic = 0
    Nashi = false
    Pobai = false
    Haiyao = false
    Zhihuimoren = false
    Yangdao = false
    Shenfen = false
    Ju<PERSON><PERSON> = false
    <PERSON><PERSON>ushe = false
    for i = 0, 5 do
        if player:itemID(i) == 3115 then
            Nashi = true
        end
        if player:itemID(i) == 3153 then
            Pobai = true
        end
        if player:itemID(i) == 6672 then
            Haiyao = true
        end
        if player:itemID(i) == 3091 then
            Zhihuimoren = true
        end
        if player:itemID(i) == 3124 then
            Yangdao = true
        end
        if player:itemID(i) == 3748 then
            Jujiu = true
        end
        if player:itemID(i) == 3074 then
            <PERSON><PERSON>ush<PERSON> = true
        end
    end
    if Nashi then
        onhitMagic = onhitMagic + (15 + player.totalAp * 0.2)*rnum
    end
    if Zhihuimoren then
        if (player.levelRef >= 1 and player.levelRef < 9) then
            leveldamage = ZhihuimorenLevelDamage[1]
        end
        if (player.levelRef == 9) then
            leveldamage = ZhihuimorenLevelDamage[2]
        end
        if (player.levelRef >= 10 and player.levelRef <= 18) then
            leveldamage = ZhihuimorenLevelDamage[player.levelRef - 7]
        end

        onhitMagic = onhitMagic + leveldamage * rnum
    end
    if Pobai then
        onhitPhysical = onhitPhysical + math.max(15, (target.health * 0.12 + 15)/2) * rnum
    end
    if Yangdao then
        onhitMagic = onhitMagic + 30 * rnum
    end
    if Jujiu then
        onhitMagic = onhitPhysical + (4 + player.maxHealth * 0.015) * rnum
    end
    if Jiutoushe then
        onhitPhysical = onhitPhysical + (0.4 * player.totalAd) * rnum
    end
    if Haiyao  then
        local haiyaonum = math.floor(rnum/3)
        if (player.levelRef >= 1 and player.levelRef < 9) then
            leveldamage = ZhihuimorenLevelDamage[1]
        end
        if (player.levelRef == 9) then
            leveldamage = ZhihuimorenLevelDamage[2]
        end
        if (player.levelRef >= 10 and player.levelRef <= 18) then
            leveldamage = ZhihuimorenLevelDamage[player.levelRef - 7]
        end
        if haiyaonum > 0 then
            onhitPhysical = onhitPhysical + (leveldamage + 0.65 * player.totalAd + 0.6 * player.totalAp) * haiyaonum
        end
    end
    if rune[8014] then
        if target.type == TYPE_HERO and util.getPercentHealth(target) < 40 then
            onhitPhysical = onhitPhysical * 1.08
            onhitMagic = onhitMagic * 1.08
        end
    end
    return
             util.calculatePhysicalDamage(target,onhitPhysical*R1LevelDamageMod[player:spellSlot(3).level],player) 
            + util.calculateMagicalDamage(target,onhitMagic*R1LevelDamageMod[player:spellSlot(3).level],player)
end

local QLevelDamage = { 80, 110, 140, 170, 200 }
local function QDamage(target)
    local damage = 0
    if player:spellSlot(0).level > 0 then
        if rune[8014] and target.type == TYPE_HERO and util.getPercentHealth(target) < 40 then
            damage =
                util.calculateMagicalDamage(target, (QLevelDamage[player:spellSlot(0).level] + player.totalAp * 0.35) *
                    1.08, player)
        else
            damage =
                util.calculateMagicalDamage(target, (QLevelDamage[player:spellSlot(0).level] + player.totalAp * 0.35),
                    player)
        end
    end
    return damage
end

local ELevelDamage = { 20, 35, 50, 65, 80 }
local function EDamage(target)
    local damage = 0
    local spad,spap = util.AttackSpecialDamage(target)
    if player:spellSlot(2).level > 0 then
        if rune[8014] and  target.type == TYPE_HERO and util.getPercentHealth(target) < 40 then
            damage =
                util.calculateMagicalDamage(target,
                    ((ELevelDamage[player:spellSlot(2).level] + player.totalAp * 0.25 + player.totalAd * 0.4)) * 1.08 , player)
                + util.calculatePhysicalDamage(target,spad,player)
        else
            damage =
                util.calculateMagicalDamage(target,
                    (ELevelDamage[player:spellSlot(2).level] + player.totalAp * 0.25 + player.totalAd * 0.4) +
                    spap, player) 
                + util.calculatePhysicalDamage(target,spad,player)
        end
    end
    return damage
end

local R1LevelDamage = { 25, 37.5, 50 }
local function R1Damage(target)
    local damage = 0
    if player:spellSlot(3).level > 0 then
        bonusAttackSpeed = player.attackSpeedMod - 1.0
        bonusAdMod = 0.5 * (bonusAttackSpeed / 1.0)
        if rune[8014] and  target.type == TYPE_HERO and util.getPercentHealth(target) < 40 then
            damage =
                util.calculateMagicalDamage(target,
                    (R1LevelDamage[player:spellSlot(3).level] + player.totalAp * 0.19) * 1.08, player)
                + util.calculatePhysicalDamage(target, player.bonusAd * bonusAdMod * 1.08, player)
        else
            damage =
                util.calculateMagicalDamage(target, (R1LevelDamage[player:spellSlot(3).level] + player.totalAp * 0.19),
                    player)
                + util.calculatePhysicalDamage(target, player.bonusAd * bonusAdMod, player)
        end
    end
    return damage
end


local DaggerLevelDamage = { 68, 72, 77, 82, 89, 96, 103, 112, 121, 131, 142, 154, 166, 180, 194, 208, 224, 240 }
local function DaggerDamage(target)
    local damage = 0
    local leveldamage = 0
    local spad,spap = util.AttackSpecialDamage(target)
    if (player.levelRef >= 1 and player.levelRef < 6) then
        leveldamage = 0.7
    end
    if (player.levelRef >= 6 and player.levelRef < 11) then
        leveldamage = 0.8
    end
    if (player.levelRef >= 11 and player.levelRef < 16) then
        leveldamage = 0.9
    end
    if (player.levelRef >= 16) then
        leveldamage = 1
    end

    if player.levelRef < 18 then
        if rune[8014] and  target.type == TYPE_HERO and util.getPercentHealth(target) < 40 then
            damage =
                util.calculateMagicalDamage(target,
                    (DaggerLevelDamage[player.levelRef] + player.bonusAd * 0.6 + (player.totalAp * leveldamage)) * 1.08 +
                    spap, player) + util.calculatePhysicalDamage(target,spad,player)
        else
            damage =
                    util.calculateMagicalDamage(target,
                    (DaggerLevelDamage[player.levelRef] + player.bonusAd * 0.6 + (player.totalAp * leveldamage)) +
                    spap, player) + util.calculatePhysicalDamage(target,spad,player)
        end
    end
    if player.levelRef >= 18 then
        if rune[8014] and  target.type == TYPE_HERO and util.getPercentHealth(target) < 40 then
            damage =
                util.calculateMagicalDamage(target,
                    (DaggerLevelDamage[18] + player.bonusAd * 0.6 + (player.totalAp * leveldamage)) * 1.08 +
                    spap, player)
        else
            damage =
                util.calculateMagicalDamage(target,
                    (DaggerLevelDamage[18] + player.bonusAd * 0.6 + (player.totalAp * leveldamage)) +
                    spap, player) + util.calculatePhysicalDamage(target,spad,player)
        end
    end
    return damage
end

local function getSpellDamage(target,spellSlot,rnum)
    if spellSlot == -1  then
        return DaggerDamage(target)
    end
    if spellSlot == 0  then
        return QDamage(target)
    end
    if spellSlot == 2  then
        return EDamage(target)
    end
    if spellSlot == 3 and rnum > 0 then
        return R1Damage(target) * rnum + RSpecialDamage(target,rnum)
    end
end

return {
    getSpellDamage = getSpellDamage
}
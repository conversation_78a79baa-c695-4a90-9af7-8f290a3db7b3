
local ove_0_10 = module.internal("orb")
local ove_0_11 = module.internal("pred")
local ove_0_12 = module.internal("clipper")
local ove_0_13 = module.seek("evade")
local ove_0_14 = ove_0_12.polygon
local ove_0_15 = module.load(header.id, "common/common")
local ove_0_16 = module.load(header.id, "plugins/Swain/menu")
local ove_0_17 = module.load(header.id, "plugins/Swain/spells/e")
local ove_0_18 = {
	isMovement = false,
	ProjectOn = function(arg_5_0, arg_5_1, arg_5_2)
		local slot_5_0 = arg_5_0.x
		local slot_5_1 = arg_5_0.z
		local slot_5_2 = arg_5_1.x
		local slot_5_3 = arg_5_1.z
		local slot_5_4 = arg_5_2.x
		local slot_5_5 = arg_5_2.z
		local slot_5_6 = ((slot_5_0 - slot_5_2) * (slot_5_4 - slot_5_2) + (slot_5_1 - slot_5_3) * (slot_5_5 - slot_5_3)) / (math.pow(slot_5_4 - slot_5_2, 2) + math.pow(slot_5_5 - slot_5_3, 2))
		local slot_5_7 = vec3(slot_5_2 + slot_5_6 * (slot_5_4 - slot_5_2), 0, slot_5_3 + slot_5_6 * (slot_5_5 - slot_5_3))

		if slot_5_6 < 0 then
			rS = 0
		elseif slot_5_6 > 1 then
			rS = 1
		else
			rS = slot_5_6
		end

		if rS == slot_5_6 then
			isOnSegment = true
			pointSegment = slot_5_7
		else
			isOnSegment = false
			pointSegment = vec3(slot_5_2 + rS * (slot_5_4 - slot_5_2), 0, slot_5_3 + rS * (slot_5_5 - slot_5_3))
		end

		return isOnSegment, pointSegment, slot_5_7
	end,
	retangle = function(arg_6_0, arg_6_1)
		if not arg_6_0 then
			return
		end

		local slot_6_0 = player.pos:to2D()
		local slot_6_1 = (slot_6_0 - arg_6_0):norm()
		local slot_6_2 = 85 + (arg_6_1 and arg_6_1.boundingRadius or 0)

		return (ove_0_14(arg_6_0 - slot_6_1:perp1() * slot_6_2 / 2, arg_6_0 + slot_6_1:perp1() * slot_6_2 / 2, slot_6_0 + (slot_6_0 - arg_6_0):norm() + slot_6_1:perp1() * slot_6_2 / 2, slot_6_0 + (slot_6_0 - arg_6_0):norm() - slot_6_1:perp1() * slot_6_2 / 2))
	end,
	Closest = function(arg_7_0)
		local slot_7_0
		local slot_7_1 = math.huge

		for iter_7_0, iter_7_1 in pairs(arg_7_0) do
			if iter_7_1 then
				local slot_7_2 = ove_0_15.GetDistance(iter_7_1, player.path.serverPos2D)

				if slot_7_2 < slot_7_1 then
					slot_7_0 = iter_7_1
					slot_7_1 = slot_7_2
				end
			end
		end

		return slot_7_0, slot_7_1
	end
}

function ove_0_18.MovementeLock()
	local slot_8_0 = {}
	local slot_8_1 = ove_0_17.result.obj ~= nil and ove_0_17.result.obj or ove_0_15.GetTarget(1500)

	if not slot_8_1 or slot_8_1.isDead or not ove_0_15.isValidTarget(slot_8_1) then
		ove_0_18.isMovement = false

		return false
	end

	if not ove_0_18.e_missile then
		ove_0_18.isMovement = false

		return false
	end

	local slot_8_2 = ove_0_11.linear.get_prediction(ove_0_17.pred_input_e2, slot_8_1, ove_0_18.e_missile.pos:to2D())

	if not slot_8_2 then
		ove_0_18.isMovement = false

		return false
	end

	local slot_8_3 = player.pos + (ove_0_18.e_missile.pos - player.pos):norm() * (ove_0_15.GetDistance(player, ove_0_18.e_missile) + 85)
	local slot_8_4 = mathf.closest_vec_line(slot_8_1.path.serverPos, player.path.serverPos, slot_8_3)
	local slot_8_5, slot_8_6, slot_8_7 = ove_0_18.ProjectOn(slot_8_1.path.serverPos, player.path.serverPos, slot_8_3)

	if ove_0_15.GetDistance(slot_8_1.path.serverPos, slot_8_3) > 1000 then
		return false
	end

	if slot_8_5 and slot_8_6:dist(slot_8_1.path.serverPos) < ove_0_16.misc.mov.min_dist:get() + slot_8_1.boundingRadius then
		local slot_8_8 = ove_0_18.retangle(ove_0_18.e_missile.pos:to2D(), slot_8_1)

		if ove_0_16.misc.mov.disable_evade:get() and ove_0_13 then
			ove_0_13.core.set_pause(math.huge)
		end

		ove_0_10.core.set_pause_attack(math.huge)

		local slot_8_9 = 450
		local slot_8_10

		slot_8_10 = ove_0_15.GetDistance(slot_8_1.path.serverPos, player.pos) >= ove_0_15.GetAARange(slot_8_1) and 525 or ove_0_15.GetDistance(slot_8_1.pos, player.pos)

		local slot_8_11 = (player.path.serverPos2D - slot_8_3:to2D()):norm()
		local slot_8_12 = slot_8_2.endPos
		local slot_8_13 = vec2(slot_8_12.x + slot_8_11.x * ove_0_15.GetDistance(player, ove_0_18.e_missile), slot_8_12.y + slot_8_11.y * ove_0_15.GetDistance(player, ove_0_18.e_missile))

		slot_8_0[#slot_8_0 + 1] = slot_8_13

		if slot_8_8 and (slot_8_8:Contains(slot_8_12:to3D()) == 0 or slot_8_8:Contains(slot_8_1.path.serverPos) == 0) then
			local slot_8_14 = ove_0_18.Closest(slot_8_0)

			if slot_8_14 ~= nil and not navmesh.isWall(slot_8_14) and not ove_0_15.isUnderEnemyTurret(slot_8_14:to3D(), 915) and slot_8_12:dist(slot_8_14) > 350 then
				player:move(slot_8_14:to3D())

				ove_0_18.isMovement = true

				return true
			elseif slot_8_13 and not navmesh.isWall(slot_8_13) and not ove_0_15.isUnderEnemyTurret(slot_8_13:to3D(), 915) and slot_8_12:dist(slot_8_13) < 350 and slot_8_12:dist(slot_8_13) > 100 then
				player:move(slot_8_13:to3D())

				ove_0_18.isMovement = true

				return true
			end
		end
	end
end

function ove_0_18.on_create_spell(arg_9_0)
	if not arg_9_0 then
		return
	end

	if arg_9_0.spell.owner.ptr ~= player.ptr then
		return
	end

	if arg_9_0.name:find("SwainE") then
		ove_0_18.e_missile = arg_9_0
	end
end

function ove_0_18.on_delete_spell(arg_10_0)
	if arg_10_0 and ove_0_18.e_missile and ove_0_18.e_missile.ptr == arg_10_0.ptr then
		ove_0_18.e_missile = nil
		ove_0_18.isMovement = false

		if not player.buff.swainr and ove_0_13 then
			ove_0_13.core.set_pause(0)
		end

		ove_0_10.core.set_pause_attack(0)
	end
end

return ove_0_18

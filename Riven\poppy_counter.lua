-- ========================================
-- 波比W技能检测模块
-- 功能：检测敌方波比的W技能状态，避免在其开启时使用位移技能
-- ========================================

local menu = module.load(header.id, 'Riven/menu')

local poppy_counter = {}

-- 波比W技能的buff名称 (坚定风采)
local POPPY_W_BUFF = "poppywpresence"  -- 波比W技能的buff名称
local POPPY_W_RANGE = 400  -- 波比W技能的作用范围

-- 缓存系统，避免重复计算
local cache = {
    last_update = 0,
    update_interval = 0.05,  -- 50ms更新一次
    poppy_w_active = false,
    poppy_enemies = {}
}

-- 检查指定对象是否有特定buff
local function has_buff(obj, buff_name)
    if not obj or not obj.buffManager then return false end

    for i = 0, obj.buffManager.count - 1 do
        local buff = obj.buffManager:get(i)
        if buff and buff.valid and string.lower(buff.name) == string.lower(buff_name) then
            if game.time <= buff.endTime then
                return true, buff.endTime - game.time
            end
        end
    end
    return false, 0
end

-- 更新波比敌人列表和W技能状态
local function update_poppy_status()
    local current_time = game.time

    -- 检查是否需要更新缓存
    if current_time - cache.last_update < cache.update_interval then
        return
    end

    cache.last_update = current_time
    cache.poppy_w_active = false
    cache.poppy_enemies = {}

    -- 遍历所有敌方英雄
    for i = 0, objManager.enemies_n - 1 do
        local enemy = objManager.enemies[i]
        if enemy and enemy.charName == "Poppy" and not enemy.isDead and enemy.isVisible then
            table.insert(cache.poppy_enemies, enemy)

            -- 检查波比是否开启了W技能
            local has_w_buff, remaining_time = has_buff(enemy, POPPY_W_BUFF)
            if has_w_buff then
                -- 检查玩家是否在波比W技能范围内
                local distance = player.pos:dist(enemy.pos)
                if distance <= POPPY_W_RANGE + player.boundingRadius + enemy.boundingRadius then
                    cache.poppy_w_active = true
                    break
                end
            end
        end
    end
end

-- 检查是否应该避免使用技能攻击目标
function poppy_counter.should_avoid_target(target)
    if not menu.avoid_poppy_w:get() then
        return false
    end

    if not target then
        return false
    end

    -- 更新波比状态
    update_poppy_status()

    -- 如果目标就是波比且她开启了W技能
    if target.charName == "Poppy" then
        local has_w_buff = has_buff(target, POPPY_W_BUFF)
        if has_w_buff then
            local distance = player.pos:dist(target.pos)
            if distance <= POPPY_W_RANGE + player.boundingRadius + target.boundingRadius then
                return true
            end
        end
    end

    -- 检查是否有其他波比开启W技能且在范围内
    for _, poppy in ipairs(cache.poppy_enemies) do
        if poppy and not poppy.isDead and poppy.isVisible then
            local has_w_buff = has_buff(poppy, POPPY_W_BUFF)
            if has_w_buff then
                -- 检查目标是否在波比W技能的保护范围内
                local poppy_to_target = target.pos:dist(poppy.pos)
                local player_to_poppy = player.pos:dist(poppy.pos)

                -- 如果玩家在波比W范围内，且要攻击的目标也在附近
                if player_to_poppy <= POPPY_W_RANGE + player.boundingRadius + poppy.boundingRadius then
                    return true
                end
            end
        end
    end

    return false
end

-- 检查是否有波比开启W技能
function poppy_counter.is_poppy_w_active()
    if not menu.avoid_poppy_w:get() then
        return false
    end

    update_poppy_status()
    return cache.poppy_w_active
end

-- 获取开启W技能的波比列表
function poppy_counter.get_active_poppy_enemies()
    if not menu.avoid_poppy_w:get() then
        return {}
    end

    update_poppy_status()

    local active_poppies = {}
    for _, poppy in ipairs(cache.poppy_enemies) do
        if poppy and not poppy.isDead and poppy.isVisible then
            local has_w_buff = has_buff(poppy, POPPY_W_BUFF)
            if has_w_buff then
                table.insert(active_poppies, poppy)
            end
        end
    end

    return active_poppies
end

-- 检查特定技能是否应该被阻止
function poppy_counter.should_block_spell(spell_slot, target)
    if not menu.avoid_poppy_w:get() then
        return false
    end

    -- 只检查位移技能 (E技能和闪现)
    if spell_slot ~= 2 and spell_slot ~= 4 then  -- E技能是slot 2，闪现通常是slot 4或5
        return false
    end

    return poppy_counter.should_avoid_target(target)
end

-- 绘制波比W技能范围（调试用）
function poppy_counter.on_draw()
    if not menu.avoid_poppy_w:get() then
        return
    end

    local active_poppies = poppy_counter.get_active_poppy_enemies()
    for _, poppy in ipairs(active_poppies) do
        if poppy and poppy.isOnScreen then
            -- 绘制波比W技能范围
            graphics.draw_circle(poppy.pos, POPPY_W_RANGE, 2, graphics.argb(100, 255, 0, 0), 50)

            -- 在波比头上显示警告文字
            local screen_pos = graphics.world_to_screen(poppy.pos)
            if screen_pos and screen_pos.x and screen_pos.y then
                graphics.draw_text_2D("POPPY W ACTIVE!", 16, screen_pos.x - 50, screen_pos.y - 30, graphics.argb(255, 255, 0, 0))
            end
        end
    end
end

-- 记住波比W技能检测功能
local function remember_poppy_feature()
    -- 这个功能已经在memories中记录了
end

return poppy_counter

local objectManager = {}

local isAlreadyLoad = false
local trackGangPlankBarrel = false
local trackMordekaiserGhost = false

local heroesNames = {
    ["aatrox"] = true,
    ["alistar"] = true,
    ["ahri"] = true,
    ["akali"] = true,
    ["amumu"] = true,
    ["anivia"] = true,
    ["annie"] = true,
    ["ashe"] = true,
    ["aurelionsol"] = true,
    ["azir"] = true,
    ["bard"] = true,
    ["blitzcrank"] = true,
    ["brand"] = true,
    ["braum"] = true,
    ["caitlyn"] = true,
    ["camille"] = true,
    ["cassiopeia"] = true,
    ["chogath"] = true,
    ["corki"] = true,
    ["darius"] = true,
    ["diana"] = true,
    ["draven"] = true,
    ["drmundo"] = true,
    ["ekko"] = true,
    ["elise"] = true,
    ["evelynn"] = true,
    ["ezreal"] = true,
    ["fiddlesticks"] = true,
    ["fiora"] = true,
    ["fizz"] = true,
    ["galio"] = true,
    ["gangplank"] = true,
    ["garen"] = true,
    ["gnar"] = true,
    ["gragas"] = true,
    ["graves"] = true,
    ["hecarim"] = true,
    ["heimerdinger"] = true,
    ["illaoi"] = true,
    ["irelia"] = true,
    ["ivern"] = true,
    ["janna"] = true,
    ["jarvaniv"] = true,
    ["jax"] = true,
    ["jayce"] = true,
    ["jhin"] = true,
    ["jinx"] = true,
    ["kaisa"] = true,
    ["kalista"] = true,
    ["karma"] = true,
    ["karthus"] = true,
    ["kassadin"] = true,
    ["katarina"] = true,
    ["kayle"] = true,
    ["kayn"] = true,
    ["kennen"] = true,
    ["khazix"] = true,
    ["kindred"] = true,
    ["kled"] = true,
    ["kogmaw"] = true,
    ["leblanc"] = true,
    ["leesin"] = true,
    ["leona"] = true,
    ["lissandra"] = true,
    ["lucian"] = true,
    ["lulu"] = true,
    ["lux"] = true,
    ["malphite"] = true,
    ["malzahar"] = true,
    ["maokai"] = true,
    ["masteryi"] = true,
    ["missfortune"] = true,
    ["monkeyking"] = true,
    ["mordekaiser"] = true,
    ["morgana"] = true,
    ["nami"] = true,
    ["nasus"] = true,
    ["nautilus"] = true,
    ["nidalee"] = true,
    ["nocturne"] = true,
    ["nunu"] = true,
    ["olaf"] = true,
    ["orianna"] = true,
    ["ornn"] = true,
    ["pantheon"] = true,
    ["poppy"] = true,
    ["pyke"] = true,
    ["quinn"] = true,
    ["rakan"] = true,
    ["rammus"] = true,
    ["reksai"] = true,
    ["renekton"] = true,
    ["rengar"] = true,
    ["riven"] = true,
    ["rumble"] = true,
    ["ryze"] = true,
    ["sejuani"] = true,
    ["shaco"] = true,
    ["shen"] = true,
    ["shyvana"] = true,
    ["singed"] = true,
    ["sion"] = true,
    ["sivir"] = true,
    ["skarner"] = true,
    ["sona"] = true,
    ["soraka"] = true,
    ["swain"] = true,
    ["syndra"] = true,
    ["tahmkench"] = true,
    ["taliyah"] = true,
    ["talon"] = true,
    ["taric"] = true,
    ["teemo"] = true,
    ["thresh"] = true,
    ["tristana"] = true,
    ["trundle"] = true,
    ["tryndamere"] = true,
    ["twistedfate"] = true,
    ["twitch"] = true,
    ["udyr"] = true,
    ["urgot"] = true,
    ["varus"] = true,
    ["vayne"] = true,
    ["veigar"] = true,
    ["velkoz"] = true,
    ["vi"] = true,
    ["viktor"] = true,
    ["vladimir"] = true,
    ["volibear"] = true,
    ["warwick"] = true,
    ["xayah"] = true,
    ["xerath"] = true,
    ["xinzhao"] = true,
    ["yasuo"] = true,
    ["yorick"] = true,
    ["zac"] = true,
    ["zed"] = true,
    ["ziggs"] = true,
    ["zilean"] = true,
    ["zoe"] = true,
    ["zyra"] = true,
    ["practicetool_targetdummy"] = true,
    ["neeko"] = true,
}

local specialcharNames = {
    -- Plants
    ["sru_plant_health"] = true, 
    ["sru_plant_vision"] = true, 
    ["sru_plant_satchel"] = true,
    -- Gangplank barrel
    ["gangplankbarrel"] = true,
    -- Ward
    ["bluetrinket"] = true,
    ["yellowtrinket"] = true,
    ["jammerdevice"] = true,
    ["sightward"] = true,
    -- Heimerdinger Turret
    ["heimertyellow"] = true,
    ["heimertblue"] = true,
    -- Annie Bear
    ["annietibbers"] = true,
    -- Zyra Seed 
    ["zyragraspingplant"] = true,
    ["zyrathornplant"] = true,
    -- -- Elise Spider
    -- ["elisespiderling"] = true,
    -- Illaoi God
    ["illaoiminion"] = true,
    -- Yorki Ghost
    ["yorickghoulmelee"] = true,
    ["yorickbigghoul"] = true,
    -- Zac Passive 
    ["zacrebirthbloblet"] = true,
    -- Malzahar Voidling
    ["malzaharvoidling"] = true,
    -- VoidGate (item)
    ["voidgate"] = true,
    ["voidspawn"] = true,
    -- -- Leblanc Cone
    -- ["leblanc"] = true,
    -- -- Shaco Cone
    -- ["shaco"] = true,
    -- -- MonkeyKing Cone
    -- ["monkeyking"] = true,
    -- Jhin Trap
    -- ["jhintrap"] = true,
    -- -- Kalista Trap
    -- ["kalistaspawn"] = true,
    -- -- Nidalee Trap
    -- ["nidaleespear"] = true,
    -- -- Shaco Trap
    -- ["shacobox"] = true,
    -- -- Teemo Trap
    -- ["teemomushroom"] = true,
}

-- local specialcharNames = {
-- }

local testcuberenderNames = {
    -- Jungle Krug Mini
    ["minikruga"] = true,
    ["minikrugb"] = true,
}

local wontdeleteNames = {
    ["azirsoldier"] = true,
    ["azirrsoldier"] = true,
    ["k"] = true,
    ["beacon"] = true,
    ["jarvanivwall"] = true,
    ["shield"] = true,
    ["thedoomball"] = true,
    ["shadow"] = true,
    ["threshlantern"] = true,
    ["yorickwinvisible"] = true,
    ["testcuberender10vision"] = true,
    ["syndrasphere"] = true,
}

local unattackcharNames = {
    ["seed"] = true,
    ["zyraseed"] = true,
    ["caitlyntrap"] = true,
    ["planthealthpack"] = true,
    ["jarvanivstandard"] = true,
}

local junglecharNames = {
    ["sru_krugmini"] = true, 
    ["sru_krugminimini"] = true, 
    ["sru_razorbeakmini"] = true, 
    ["sru_murkwolfmini"] = true, 
    ["sru_blue"] = true, 
    ["sru_gromp"] = true, 
    ["sru_murkwolf"] = true, 
    ["sru_razorbeak"] = true, 
    ["sru_red"] = true, 
    ["sru_krug"] = true, 
    ["sru_crab"] = true, 
    ["sru_baron"] = true, 
    ["sru_dragon_air"] = true, 
    ["sru_dragon_fire"] = true, 
    ["sru_dragon_earth"] = true, 
    ["sru_dragon_elder"] = true, 
    ["sru_dragon_water"] = true, 
    ["sru_riftherald"] = true, 
}

objectManager.AllHeroes = {}
objectManager.AllMinions = {}
objectManager.AllTurrets = {}
objectManager.AllBarracks = {}
objectManager.AllHQ = {}
objectManager.AllFountains = {}

objectManager.AllyHeroes = {}
objectManager.AllyMinions = {}
objectManager.AllyTurrets = {}
objectManager.AllyBarracks = {}
objectManager.AllyHQ = {}
objectManager.AllyFountains = {}

objectManager.EnemyHeroes = {}
objectManager.EnemyMinions = {}
objectManager.EnemyTurrets = {}
objectManager.EnemyBarracks = {}
objectManager.EnemyHQ = {}
objectManager.EnemyFountains = {}

objectManager.NeturalMinions = {}
objectManager.SpecialMinions = {}

function objectManager.Compare(object1, object2)
    if object1 and object1 ~= nil and object2 and object2 ~= nil then
        if object1.type == object2.type then
            if object1.ptr and object2.ptr and object1.ptr == object2.ptr then
                return true
            end
        end
    end
end

function objectManager.GangPlankInGame()
    return trackGangPlankBarrel
end

function objectManager.GetAllHeroes()
    local list = objectManager.AllHeroes
    return list
end

function objectManager.GetAllMinions()
    local list = {}
    local allyMinions = objManager.minions
    if allyMinions then
        if allyMinions.size[TEAM_ALLY] > 0 then
            for i = 0, allyMinions.size[TEAM_ALLY] - 1 do
                local target = allyMinions[TEAM_ALLY][i]
                if target and not target.isDead and target.pos then
                    table.insert(list, target)
                end
            end
        end
    end
    local enemyMinions = objManager.minions
    if enemyMinions then
        if enemyMinions.size[TEAM_ENEMY] > 0 then
            for i = 0, enemyMinions.size[TEAM_ENEMY] - 1 do
                local target = enemyMinions[TEAM_ENEMY][i]
                if target and not target.isDead and target.pos then
                    table.insert(list, target)
                end
            end
        end
    end
    return list
end

function objectManager.GetAllTurrets()
    local list = {}
    if objManager.turrets then
        if objManager.turrets[TEAM_ENEMY] and objManager.turrets.size[TEAM_ENEMY] > 0 then
            for i = 0, objManager.turrets.size[TEAM_ENEMY] - 1 do
                local turret = objManager.turrets[TEAM_ENEMY][i]
                if turret and not turret.isDead and turret.team ~= player.team then
                    table.insert(list, turret)
                end
            end
        end
        if objManager.turrets[TEAM_ALLY] and objManager.turrets.size[TEAM_ALLY] > 0 then
            for i = 0, objManager.turrets.size[TEAM_ALLY] - 1 do
                local turret = objManager.turrets[TEAM_ALLY][i]
                if turret and not turret.isDead and turret.team == player.team then
                    table.insert(list, turret)
                end
            end
        end
    end
    return list
end

function objectManager.GetAllBarracks()
    local list = {}
    if objManager.inhibs then
        if objManager.inhibs[TEAM_ENEMY] and objManager.inhibs.size[TEAM_ENEMY] > 0 then
            for i = 0, objManager.inhibs.size[TEAM_ENEMY] - 1 do
                local inhib = objManager.inhibs[TEAM_ENEMY][i]
                if inhib and not inhib.isDead and inhib.team ~= player.team then
                    table.insert(list, inhib)
                end
            end
        end
        if objManager.inhibs[TEAM_ALLY] and objManager.inhibs.size[TEAM_ALLY] > 0 then
            for i = 0, objManager.inhibs.size[TEAM_ALLY] - 1 do
                local barrack = objManager.inhibs[TEAM_ALLY][i]
                if barrack and not barrack.isDead and barrack.team == player.team then
                    table.insert(list, barrack)
                end
            end
        end
    end
    return list
end

function objectManager.GetAllHQ()
    local list = {}
    if objManager.nexus then
        local enemy = objManager.nexus[TEAM_ENEMY]
        if enemy then
            table.insert(list, enemy)
        end
        local ally = objManager.nexus[TEAM_ALLY]
        if ally then
            table.insert(list, ally)
        end
    end
    return list
end

function objectManager.GetAllFountains()
    local list = objectManager.AllFountains
    return list
end

function objectManager.GetAllyHeroes()
    local list = objectManager.AllyHeroes
    return list
end

function objectManager.GetAllyMinions()
    local list = {}
    local minions = objManager.minions
    if minions then
        if minions.size[TEAM_ALLY] > 0 then
            for i = 0, minions.size[TEAM_ALLY] - 1 do
                local target = minions[TEAM_ALLY][i]
                if target and not target.isDead and target.pos then
                    table.insert(list, target)
                end
            end
        end
    end
    return list
end

function objectManager.GetAllyTurrets()
    local list = {}
    if objManager.turrets then
        if objManager.turrets[TEAM_ALLY] and objManager.turrets.size[TEAM_ALLY] > 0 then
            for i = 0, objManager.turrets.size[TEAM_ALLY] - 1 do
                local turret = objManager.turrets[TEAM_ALLY][i]
                if turret and not turret.isDead and turret.team == player.team then
                    table.insert(list, turret)
                end
            end
        end
    end
    return list
end

function objectManager.GetAllyBarracks()
    local list = {}
    if objManager.inhibs then
        if objManager.inhibs[TEAM_ALLY] and objManager.inhibs.size[TEAM_ALLY] > 0 then
            for i = 0, objManager.inhibs.size[TEAM_ALLY] - 1 do
                local barrack = objManager.inhibs[TEAM_ALLY][i]
                if barrack and not barrack.isDead and barrack.team == player.team then
                    table.insert(list, barrack)
                end
            end
        end
    end
    return list
end

function objectManager.GetAllyHQ()
    local list = {}
    local ally = objManager.nexus[TEAM_ALLY]
    if ally then
        table.insert(list, ally)
    end
    return list
end

function objectManager.GetAllyFountains()
    local list = objectManager.AllyFountains
    return list
end

function objectManager.GetEnemyHeroes()
    local list = objectManager.EnemyHeroes
    return list
end

function objectManager.GetEnemyMinions()
    local list = {}
    local minions = objManager.minions
    if minions then
        if minions.size[TEAM_ENEMY] > 0 then
            for i = 0, minions.size[TEAM_ENEMY] - 1 do
                local target = minions[TEAM_ENEMY][i]
                if target and not target.isDead and target.pos then
                    table.insert(list, target)
                end
            end
        end
    end
    return list
end

function objectManager.GetEnemyTurrets()
    local list = {}
    if objManager.turrets then
        if objManager.turrets[TEAM_ENEMY] and objManager.turrets.size[TEAM_ENEMY] > 0 then
            for i = 0, objManager.turrets.size[TEAM_ENEMY] - 1 do
                local turret = objManager.turrets[TEAM_ENEMY][i]
                if turret and not turret.isDead and turret.team ~= player.team then
                    table.insert(list, turret)
                end
            end
        end
    end
    return list
end

function objectManager.GetEnemyBarracks()
    local list = {}
    if objManager.inhibs then
        if objManager.inhibs[TEAM_ENEMY] and objManager.inhibs.size[TEAM_ENEMY] > 0 then
            for i = 0, objManager.inhibs.size[TEAM_ENEMY] - 1 do
                local barrack = objManager.inhibs[TEAM_ENEMY][i]
                if barrack and not barrack.isDead and barrack.team ~= player.team then
                    table.insert(list, barrack)
                end
            end
        end
    end
    return list
end

function objectManager.GetEnemyHQ()
    local list = {}
    local enemy = objManager.nexus[TEAM_ENEMY]
    if enemy then
        table.insert(list, enemy)
    end
    return list
end

function objectManager.GetEnemyFountains()
    local list = objectManager.EnemyFountains
    return list
end

function objectManager.GetNeturalMinions()
    local list = {}
    local minions = objManager.minions
    if minions then
        if minions.size[TEAM_NEUTRAL] > 0 then
            for i = 0, minions.size[TEAM_NEUTRAL] - 1 do
                local target = minions[TEAM_NEUTRAL][i]
                if target and not target.isDead and target.pos then
                    table.insert(list, target)
                end
            end
        end
    end
    return list
end

function objectManager.GetSpecialMinions()
    local list = objectManager.SpecialMinions
    return list
end

function objectManager.IsUnderAllyTurret(source)
    if not source then
        return false
    end

    local pos = vec2(0, 0)
    if source then
        if source.type == "vec3" and source.x and source.y and source.z then
            pos = vec2(source.x, source.z)
        elseif source.type == "vec2" and source.x and source.y then
            pos = vec2(source.x, source.y)
        elseif source.pos and source.pos.type == "vec3" then
            pos = vec2(source.pos.x, source.pos.z)
        else
            return false
        end
    end

    if pos.x == 0 and pos.y == 0 then
        return false
    end

    local Turret = objectManager.AllyTurrets
    if Turret and #Turret > 0 then
        for i, turret in ipairs(Turret) do
            if turret and not turret.isDead and turret.team == player.team and turret.pos and turret.pos2D and pos then
                if turret.pos2D:distSqr(pos) <= (930 ^ 2) then
                    return true
                end
            end
        end
    end
end

function objectManager.IsUnderEnemyTurret(source, exRange)
    exRange = exRange or 0
    if not source then
        return false
    end

    local extraRange = player.boundingRadius + exRange
    local pos = vec2(0, 0)
    if source then
        if source.type == "vec3" and source.x and source.y and source.z then
            pos = vec2(source.x, source.z)
        elseif source.type == "vec2" and source.x and source.y then
            pos = vec2(source.x, source.y)
        elseif source.pos and source.pos.type == "vec3" then
            pos = vec2(source.pos.x, source.pos.z)
            if source.type == TYPE_HERO and source.boundingRadius then
                extraRange = source.boundingRadius
            end
        else
            return false
        end
    else
        return false
    end

    if pos.x == 0 and pos.y == 0 then
        return false
    end

    if objManager.turrets then
        if objManager.turrets[TEAM_ENEMY] and objManager.turrets.size[TEAM_ENEMY] > 0 then
            for i = 0, objManager.turrets.size[TEAM_ENEMY] - 1 do
                local turret = objManager.turrets[TEAM_ENEMY][i]
                if turret and not turret.isDead and turret.team ~= player.team and turret.pos and turret.pos2D and pos then
                    local range = 930 + extraRange
                    if turret.pos2D:distSqr(pos) <= (range ^ 2) then
                        return true
                    end
                end
            end
        end
    end

end

function objectManager.IsInFountainRange(source, extraRange)
    if not source then
        return false
    end

    extraRange = extraRange or 0

    local pos = vec2(0, 0)
    if source.type == "vec3" and source.x and source.y and source.z then
        pos = vec2(source.x, source.z)
    elseif source.type == "vec2" and source.x and source.y then
        pos = vec2(source.x, source.y)
    elseif source.pos and source.pos.type == "vec3" then
        pos = vec2(source.pos.x, source.pos.z)
    else
        return false
    end

    if pos.x == 0 and pos.y == 0 then
        return false
    end

    local range = 1250 + extraRange

    local Fountain = objectManager.AllyFountains
    if Fountain and #Fountain > 0 then
        for i, turret in ipairs(Fountain) do
            if turret and turret.team == player.team and turret.pos and turret.pos2D and pos then
                if turret.pos2D:distSqr(pos) <= (range ^ 2) then
                    return true
                end
            end
        end
    end
end

function objectManager.IsInEnemyFountainRange(source)
    if not source then
        return false
    end

    local pos = vec2(0, 0)
    if source.type == "vec3" and source.x and source.y and source.z then
        pos = vec2(source.x, source.z)
    elseif source.type == "vec2" and source.x and source.y then
        pos = vec2(source.x, source.y)
    elseif source.pos and source.pos.type == "vec3" then
        pos = vec2(source.pos.x, source.pos.z)
    else
        return false
    end

    if pos.x == 0 and pos.y == 0 then
        return false
    end

    local Fountain = objectManager.EnemyFountains
    if Fountain and #Fountain > 0 then
        for i, turret in ipairs(Fountain) do
            if turret and turret.team ~= player.team and turret.pos and turret.pos2D and pos then
                if turret.pos2D:distSqr(pos) <= (1250 ^ 2) then
                    return true
                end
            end
        end
    end
end

local function IsValidHero(target)
    if target and target ~= nil and not target.isDead and target.type == TYPE_HERO and target.health and target.health > 0 and target.isVisible and target.isTargetable then
        return true
    end
end

local function IsValidMinion(minion)
    if minion and minion ~= nil and not minion.isDead and minion.type == TYPE_MINION and minion.health and minion.health > 0 and minion.isVisible and minion.isTargetable and minion.charName then
        local lowercharName = string.lower(minion.charName)
        -- local lowerName = string.lower(minion.name)
        -- if not unattackcharNames[lowercharName] and not wontdeleteNames[lowercharName] and not wontdeleteNames[lowerName] and not specialcharNames[lowercharName] then
        --     return true
        -- end
        if not unattackcharNames[lowercharName] and not wontdeleteNames[lowercharName] and not specialcharNames[lowercharName] then
            return true
        end
    end
end

function objectManager.GetAllMinionsInRange(range, source)
    local pos = vec2(0, 0)
    if source then
        if source.type == "vec3" and source.x and source.y and source.z then
            pos = vec2(source.x, source.z)
        elseif source.type == "vec2" and source.x and source.y then
            pos = vec2(source.x, source.y)
        elseif source.pos and source.pos.type == "vec3" then
            pos = vec2(source.pos.x, source.pos.z)
        end
    elseif not source then
        pos = vec2(player.pos.x, player.pos.z)
    end

    range = range or math.huge

    local list = {}
    if pos.x == 0 and pos.y == 0 then
        return list
    end

    local allyMinions = objectManager.GetAllyMinions()
    for i, minion in ipairs(allyMinions) do
        if minion and minion ~= nil and IsValidMinion(minion) then
            if minion.pos and minion.pos2D and pos and minion.pos2D:distSqr(pos) <= (range ^ 2) then
                table.insert(list, minion)
            end
        end
    end

    local enemyMinions = objectManager.GetEnemyMinions()
    for i, minion in ipairs(enemyMinions) do
        if minion and minion ~= nil and IsValidMinion(minion) then
            if minion.pos and minion.pos2D and pos and minion.pos2D:distSqr(pos) <= (range ^ 2) then
                table.insert(list, minion)
            end
        end
    end

    local mobs = objectManager.GetNeturalMinions()
    for i, mob in ipairs(mobs) do
        if mob and mob ~= nil and IsValidMinion(mob) then
            if mob.pos and mob.pos2D and pos and mob.pos2D:distSqr(pos) <= (range ^ 2) then
                table.insert(list, mob)
            end
        end
    end

    table.sort(list, function(a, b)
        return a.maxHealth < b.maxHealth
    end)

    return list
end

function objectManager.GetNotAllyMinions(range, source)
    local pos = vec2(0, 0)
    if source then
        if source.type == "vec3" and source.x and source.y and source.z then
            pos = vec2(source.x, source.z)
        elseif source.type == "vec2" and source.x and source.y then
            pos = vec2(source.x, source.y)
        elseif source.pos and source.pos.type == "vec3" then
            pos = vec2(source.pos.x, source.pos.z)
        end
    elseif not source then
        pos = vec2(player.pos.x, player.pos.z)
    end

    range = range or math.huge

    local list = {}
    if pos.x == 0 and pos.y == 0 then
        return list
    end

    local minions = objectManager.GetEnemyMinions()
    for i, minion in ipairs(minions) do
        if minion and minion ~= nil and IsValidMinion(minion) then
            if minion.pos and minion.pos2D and pos and minion.pos2D:distSqr(pos) <= (range ^ 2) then
                table.insert(list, minion)
            end
        end
    end

    local mobs = objectManager.GetNeturalMinions()
    for i, mob in ipairs(mobs) do
        if mob and mob ~= nil and IsValidMinion(mob) then
            if mob.pos and mob.pos2D and pos and mob.pos2D:distSqr(pos) <= (range ^ 2) then
                table.insert(list, mob)
            end
        end
    end

    table.sort(list, function(a, b)
        return a.maxHealth < b.maxHealth
    end)

    return list
end

function objectManager.GetNotEnemyMinions(range, source)
    local pos = vec2(0, 0)
    if source then
        if source.type == "vec3" and source.x and source.y and source.z then
            pos = vec2(source.x, source.z)
        elseif source.type == "vec2" and source.x and source.y then
            pos = vec2(source.x, source.y)
        elseif source.pos and source.pos.type == "vec3" then
            pos = vec2(source.pos.x, source.pos.z)
        end
    elseif not source then
        pos = vec2(player.pos.x, player.pos.z)
    end

    range = range or math.huge

    local list = {}
    if pos.x == 0 and pos.y == 0 then
        return list
    end

    local minions = objectManager.GetAllyMinions()
    for i, minion in ipairs(minions) do
        if minion and minion ~= nil and IsValidMinion(minion) then
            if minion.pos and minion.pos2D and pos and minion.pos2D:distSqr(pos) <= (range ^ 2) then
                table.insert(list, minion)
            end
        end
    end

    local mobs = objectManager.GetNeturalMinions()
    for i, mob in ipairs(mobs) do
        if mob and mob ~= nil and IsValidMinion(mob) then
            if mob.pos and mob.pos2D and pos and mob.pos2D:distSqr(pos) <= (range ^ 2) then
                table.insert(list, mob)
            end
        end
    end

    table.sort(list, function(a, b)
        return a.maxHealth < b.maxHealth
    end)

    return list
end

function objectManager.GetMinions(range, team, source)
    local pos = vec2(0, 0)
    if source then
        if source.type == "vec3" and source.x and source.y and source.z then
            pos = vec2(source.x, source.z)
        elseif source.type == "vec2" and source.x and source.y then
            pos = vec2(source.x, source.y)
        elseif source.pos and source.pos.type == "vec3" then
            pos = vec2(source.pos.x, source.pos.z)
        end
    elseif not source then
        pos = vec2(player.pos.x, player.pos.z)
    end

    range = range or math.huge
    team = team or TEAM_ENEMY

    local list = {}
    if pos.x == 0 and pos.y == 0 then
        return list
    end

    if team == TEAM_ENEMY then
        local tbl = objectManager.GetEnemyMinions()
        for i, minion in ipairs(tbl) do
            if minion and minion ~= nil and IsValidMinion(minion) then
                if minion.pos and minion.pos2D and pos and minion.pos2D:distSqr(pos) <= (range ^ 2) then
                    table.insert(list, minion)
                end
            end
        end
    elseif team == TEAM_NEUTRAL then
        local tbl = objectManager.GetNeturalMinions()
        for i, mob in ipairs(tbl) do
            if mob and mob ~= nil and IsValidMinion(mob) then
                if mob.pos and mob.pos2D and pos and mob.pos2D:distSqr(pos) <= (range ^ 2) then
                    table.insert(list, mob)
                end
            end
        end
    elseif team == TEAM_ALLY then
        local tbl = objectManager.GetAllyMinions()
        for i, minion in ipairs(tbl) do
            if minion and minion ~= nil and IsValidMinion(minion) then
                if minion.pos and minion.pos2D and pos and minion.pos2D:distSqr(pos) <= (range ^ 2) then
                    table.insert(list, minion)
                end
            end
        end
    end

    table.sort(list, function(a, b)
        return a.maxHealth < b.maxHealth
    end)

    return list
end

function objectManager.GetAAMinions(range, team, source)
    local pos = vec2(0, 0)
    if source then
        if source.type == "vec3" and source.x and source.y and source.z then
            pos = vec2(source.x, source.z)
        elseif source.type == "vec2" and source.x and source.y then
            pos = vec2(source.x, source.y)
        elseif source.pos and source.pos.type == "vec3" then
            pos = vec2(source.pos.x, source.pos.z)
        end
    elseif not source then
        pos = vec2(player.pos.x, player.pos.z)
    end

    range = range or (player.attackRange + player.boundingRadius + 70)
    team = team or TEAM_ENEMY

    local list = {}
    if pos.x == 0 and pos.y == 0 then
        return list
    end

    if team == TEAM_ENEMY then
        local tbl = objectManager.GetEnemyMinions()
        for i, minion in ipairs(tbl) do
            if minion and minion ~= nil and minion and IsValidMinion(minion) then
                if minion.pos and minion.pos2D and pos then
                    if minion.pos2D:distSqr(pos) <= (range ^ 2) then
                        table.insert(list, minion)
                    end
                end
            end
        end
    elseif team == TEAM_NEUTRAL then
        local tbl = objectManager.GetNeturalMinions()
        for i, mob in ipairs(tbl) do
            if mob and mob ~= nil and IsValidMinion(mob) then
                if mob.pos and mob.pos2D and pos then
                    local extraRange = 0
                    if mob.charName == "SRU_Baron" then
                        extraRange = 125
                    end
                    if mob.pos2D:distSqr(pos) <= ((range + extraRange) ^ 2) then
                        table.insert(list, mob)
                    end
                end
            end
        end
    elseif team == TEAM_ALLY then
        local tbl = objectManager.GetAllyMinions()
        for i, minion in ipairs(tbl) do
            if minion and minion ~= nil and minion and IsValidMinion(minion) then
                if minion.pos and minion.pos2D and pos then
                    if minion.pos2D:distSqr(pos) <= (range ^ 2) then
                        table.insert(list, minion)
                    end
                end
            end
        end
    end

    return list
end

function objectManager.GetEnemiesInRange(range, source)
    local newList = {}
    if not range then
        return newList
    end

    local pos = vec2(0, 0)
    if source then
        if source.type == "vec3" and source.x and source.y and source.z then
            pos = vec2(source.x, source.z)
        elseif source.type == "vec2" and source.x and source.y then
            pos = vec2(source.x, source.y)
        elseif source.pos and source.pos.type == "vec3" then
            pos = vec2(source.pos.x, source.pos.z)
        end
    elseif not source then
        pos = vec2(player.pos.x, player.pos.z)
    end

    if pos.x == 0 and pos.y == 0 then
        return newList
    end

    local list = objectManager.EnemyHeroes
    if list and #list > 0 then
        for i, target in ipairs(list) do
            if target and target ~= nil and IsValidHero(target) then
                if target.pos and target.pos2D and pos and target.pos2D:distSqr(pos) <= (range ^ 2) then
                    table.insert(newList, target)
                end
            end
        end
    end

    return newList
end

function objectManager.GetAlliesInRange(range, source, incluePlayer)
    incluePlayer = incluePlayer or true
    local newList = {}
    if not range then
        return newList
    end

    local pos = vec2(0, 0)
    if source then
        if source.type == "vec3" and source.x and source.y and source.z then
            pos = vec2(source.x, source.z)
        elseif source.type == "vec2" and source.x and source.y then
            pos = vec2(source.x, source.y)
        elseif source.pos and source.pos.type == "vec3" then
            pos = vec2(source.pos.x, source.pos.z)
        end
    elseif not source then
        pos = vec2(player.pos.x, player.pos.z)
    end

    if pos.x == 0 and pos.y == 0 then
        return newList
    end

    local list = objectManager.AllyHeroes
    if list and #list > 0 then
        for i, target in ipairs(list) do
            if target and target ~= nil and IsValidHero(target) and target.ptr and (incluePlayer or (target.ptr ~= player.ptr)) then
                if target.pos and target.pos2D and pos and target.pos2D:distSqr(pos) <= (range ^ 2) then
                    table.insert(newList, target)
                end
            end
        end
    end

    return newList
end

function objectManager.GetHeroesInRange(range, source, incluePlayer)
    incluePlayer = incluePlayer or false
    local newList = {}
    if not range then
        return newList
    end

    local pos = vec2(0, 0)
    if source then
        if source.type == "vec3" and source.x and source.y and source.z then
            pos = vec2(source.x, source.z)
        elseif source.type == "vec2" and source.x and source.y then
            pos = vec2(source.x, source.y)
        elseif source.pos and source.pos.type == "vec3" then
            pos = vec2(source.pos.x, source.pos.z)
        end
    elseif not source then
        pos = vec2(player.pos.x, player.pos.z)
    end

    if pos.x == 0 and pos.y == 0 then
        return newList
    end

    local enemies = objectManager.EnemyHeroes
    if enemies and #enemies > 0 then
        for i, target in ipairs(enemies) do
            if target and target ~= nil and IsValidHero(target) then
                if target.pos and target.pos2D and pos and target.pos2D:distSqr(pos) <= (range ^ 2) then
                    table.insert(newList, target)
                end
            end
        end
    end

    local allies = objectManager.AllyHeroes
    if allies and #allies > 0 then
        for i, target in ipairs(allies) do
            if target and target ~= nil and IsValidHero(target) and target.ptr and (incluePlayer or (target.ptr ~= player.ptr)) then
                if target.pos and target.pos2D and pos and target.pos2D:distSqr(pos) <= (range ^ 2) then
                    table.insert(newList, target)
                end
            end
        end
    end

    return newList
end

local function RemoveObject(obj, fromTable)
    if obj and obj.ptr then
        for i, cache in pairs(fromTable) do
            if cache and cache.ptr and cache.ptr == obj.ptr then
                table.remove(fromTable, i)
            end
        end
    end
end

local function IsMinion(obj)
    if obj and obj.charName then
        local lowerName = string.lower(obj.charName)
        if lowerName:find("orderminion") or lowerName:find("chaosminion") then
            return true
        end
        return false
    end
    return false
end

local function OnObjectCreate(obj)
    if obj then
        if obj.type == TYPE_HERO then
            table.insert(objectManager.AllHeroes, obj)
            if obj.team == player.team then
                table.insert(objectManager.AllyHeroes, obj)
            else
                table.insert(objectManager.EnemyHeroes, obj)
                if obj.charName == "Gangplank" then
                    trackGangPlankBarrel = true
                end
                if obj.charName == "Mordekaiser" then
                    trackMordekaiserGhost = true
                end
            end
            return
        end

        if obj.type == TYPE_MINION and obj.charName and obj.ptr then
            local lowercharName = string.lower(obj.charName)
            if lowercharName and specialcharNames[lowercharName] then
                objectManager.SpecialMinions[obj.ptr] = obj
                return
            end
            
            -- if lowercharName and not wontdeleteNames[lowercharName] then 
            --     if obj.team == TEAM_NEUTRAL then
            --         if specialcharNames[lowercharName] then
            --             objectManager.SpecialMinions[obj.ptr] = obj
            --             return
            --         end
            --     elseif obj.team == TEAM_ENEMY then
            --         if specialcharNames[lowercharName] then
            --             objectManager.SpecialMinions[obj.ptr] = obj
            --             return
            --         end
            --         if trackMordekaiserGhost and heroesNames[lowercharName] then
            --             objectManager.SpecialMinions[obj.ptr] = obj
            --             return
            --         end
            --     end
            -- end
            return
        end

        if obj.type == TYPE_SPAWN then
            table.insert(objectManager.AllFountains, obj)
            if obj.team == player.team then
                table.insert(objectManager.AllyFountains, obj)
            else
                table.insert(objectManager.EnemyFountains, obj)
            end
        end
    end
end

local function OnMyCreateMinion(minion)
    if minion then
        if minion.type == TYPE_MINION and minion.charName and minion.ptr then
            local lowercharName = string.lower(minion.charName)
            if lowercharName and not wontdeleteNames[lowercharName] then 
                if minion.team == TEAM_NEUTRAL then
                    if specialcharNames[lowercharName] then
                        objectManager.SpecialMinions[minion.ptr] = minion
                        return
                    end
                elseif minion.team == TEAM_ENEMY then
                    if specialcharNames[lowercharName] then
                        objectManager.SpecialMinions[minion.ptr] = minion
                        return
                    elseif trackMordekaiserGhost and heroesNames[lowercharName] then
                        objectManager.SpecialMinions[minion.ptr] = minion
                        return
                    end
                end
            end
            return
        end
    end
end

local function OnMyDeleteMinion(minion)
    objectManager.SpecialMinions[minion.ptr] = nil
end

local function Initialize()
    if isAlreadyLoad then
        return
    end

    isAlreadyLoad = true

    objManager.loop(
        function(obj)
            OnObjectCreate(obj)
        end
    )

    cb.add(cb.create_minion, OnMyCreateMinion)
    cb.add(cb.delete_minion, OnMyDeleteMinion)
end

Initialize()

return objectManager

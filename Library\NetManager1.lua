local netManager = {}

function netManager.Ping()
    local result = (network.latency) * 1000
    return result
end

function netManager.GameTime()
    local result = game.time
    return result
end

function netManager.TickCount()
    local result = (game.time) * 1000
    return result
end

function netManager.Download(url, file_path)
    local download = network.download_file(url, file_path)
    return download
end

function netManager.Send(url, args)
    local res, error, msg = network.send(url, args)
    return res, error, msg
end

return netManager

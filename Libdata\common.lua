local pred = module.internal('pred')
local ts = module.internal('TS')
local orb = module.internal('orb')

local common = class()

function common:insidePolygon(polygon, point)
	local oddNodes = false
	local j = #polygon
	for i = 1, #polygon do
		if (polygon[i].y < point.y and polygon[j].y >= point.y or polygon[j].y < point.y and polygon[i].y >= point.y) then
			if (polygon[i].x + ( point.y - polygon[i].y ) / (polygon[j].y - polygon[i].y) * (polygon[j].x - polygon[i].x) < point.x) then
				oddNodes = not oddNodes;
			end
		end
		j = i;
	end
	return oddNodes
end

function common:check_mana(mana_percentage)
	if (player.mana / player.maxMana) * 100 >= mana_percentage then
		return true
	else
		return false
	end
end

function common:getClosestObj(source_obj,table_obj)
    local mindist = math.huge
    local minobj = nil
    for _,obj in pairs(table_obj) do
        local dist = obj:dist(source_obj.pos)
        if dist < mindist then
            mindist = dist
            minobj = obj
        end
    end
    return minobj
end

function common:concatArray(a, b)
	local result = {table.unpack(a)}
	table.move(b, 1, #b, #result + 1, result)
	return result
end

--从伤害来源或者玩家返回对目标所造成的物理伤害
function common:PhysicalReduction(target, damageSource)
	local damageSource = damageSource or player
	local armor = ((target.bonusArmor * damageSource.percentBonusArmorPenetration) + (target.armor - target.bonusArmor)) * damageSource.percentArmorPenetration
	 -- (目标额外护甲 * 玩家或伤害来源的额外护甲穿透百分比) + (目标护甲 - 目标额外护甲) * 护甲穿透百分比
	local lethality = (damageSource.physicalLethality * 0.4) + ((damageSource.physicalLethality * 0.6) * (damageSource.levelRef / 18))
	 -- (玩家或伤害来源的物理损伤 * 0.4) + (玩家或伤害来源的物理损伤 * 0.6) * (玩家等级 / 18)
	return armor >= 0 and (100 / (100 + (armor - lethality))) or (2 - (100 / (100 - (armor - lethality))))
end

--从伤害来源或者玩家返回对目标所造成的魔法伤害
function common:MagicReduction(target, damageSource)
	local damageSource = damageSource or player
	local magicResist = (target.spellBlock * damageSource.percentMagicPenetration) - damageSource.flatMagicPenetration
	return magicResist >= 0 and (100 / (100 + magicResist)) or (2 - (100 / (100 - magicResist)))
end

-- 从伤害来源或玩家返回目标的伤害减少乘数
function common:DamageReduction(damageType, target, damageSource)
	local damageSource = damageSource or player
	local reduction = 1
	if damageType == "AD" then
	end
	if damageType == "AP" then
	end
	return reduction
end

-- 计算伤害来源或者玩家对目标造成的物理伤害
function common:JSWLDMG(target, damage, damageSource) --计算物理伤害
	local damageSource = damageSource or player
	if target then
	  return (damage * common:PhysicalReduction(target, damageSource)) * common:DamageReduction("AD", target, damageSource)
	end
	return 0
end

-- 计算伤害来源或者玩家对目标造成的魔法伤害
function common:JSMFDMG(target, damage, damageSource) --计算魔法伤害
	local damageSource = damageSource or player
	if target then
	  return (damage * common:MagicReduction(target, damageSource)) * common:DamageReduction("AP", target, damageSource)
	end
	return 0
end

-- 计算AA伤害
function common:JSAADMG(target, damageSource)
	local damageSource = damageSource or player
    if target then
      return common:GetAllAD(damageSource) * common:PhysicalReduction(target, damageSource)
    end
    return 0
end

local yasuoShield = {100, 105, 110, 115, 120, 130, 140, 150, 165, 180, 200, 225, 255, 290, 330, 380, 440, 510}
function common:GetBDHP(damageType, target)
  local shield = 0
  if damageType == "AD" then
    shield = target.physicalShield
  elseif damageType == "AP" then
    shield = target.magicalShield
  elseif damageType == "ALL" then
    shield = target.allShield
  end
  return target.health + shield
end

--返回鼠标位置
function common:GetMousePos()
    return vec3(game.mousePos.x, game.mousePos.y, game.mousePos.z)
end

--返回玩家位置
function common:GetplayerPos()
    return vec3(player.x, player.y, player.z)
end

--返回目标位置
function common:GetobjPos(obj)
    return vec3(obj.x, obj.y, obj.z)
end

-- 返回总AD[参数可空 默认获取玩家自身]
function common:GetAllAD(obj)
	local obj = obj or player
	return obj.totalAttackDamage
end

  -- 返回额外AD[参数可空 默认获取玩家自身]
function common:GetAddAD(obj)
	local obj = obj or player
	return obj.totalBonusAttackDamage
end

  -- 返回总AP[参数可空 默认获取玩家自身]
function common:GetAllAP(obj)
	local obj = obj or player
	return obj.totalAbilityPower
end

  -- 返回额外AP[参数可空 默认获取玩家自身]
function common:GetAddAP(obj)
	local obj = obj or player
	return obj.totalBonusAbilityPower
end

  -- 返回当前血量百分比[参数可空 默认获取玩家自身]
function common:GetHP(obj)
	local obj = obj or player
	return (obj.health / obj.maxHealth) * 100
end

  -- 返回当前蓝量百分比[参数可空 默认获取玩家自身]
function common:GetMana(obj)
	local obj = obj or player
	return (obj.mana / obj.maxMana) * 100
end

  -- 返回当前能量百分比[参数可空 默认获取玩家自身]
function common:GetPar(obj)
	local obj = obj or player
	return (obj.par / obj.maxPar) * 100
end

  -- 返回当前obj等级[参数可空 默认获取玩家自身]
function common:GetLv()
	local obj = obj or player
	return obj.levelRef
end

  -- 返回AA范围[参数可空 默认获取玩家自身]
function common:GetAARange(obj)
	return player.attackRange + player.boundingRadius + (obj and obj.boundingRadius or 0)
end

  --自身施法
function common:castself(a)
	return player:castSpell("self", a)
end

  --对象施法
function common:castobj(a, obj)
	return player:castSpell("obj", a, obj)
end

  --位置施法
function common:castpos(a, pos)
	return player:castSpell("pos", a, pos)
end

  --蓄力技能二次施法
function common:castcast(a, pos)
	return player:castSpell("release", a, pos)
end

  --直线二段施法[兰博R 维克托E]
function common:castline(a, castpos, endpos)
	return player:castSpell("line", a, endpos, castpos)
end

  --大笑 跳舞 指令
function common:ctrl_123(a)
	return pleaer:showEmote(a)
end

  --亮狗牌
function common:ctrl_6()
	return pleaer:displayChampMastery()
end

  --返回技能是否就绪
function common:ready(spell)
    if player:spellState(spell) == 0 then
        return true
    else
        return false
    end
end

  --返回技能是否是对应的名称
function common:spellname(a,name)
	if player:spellSlot(a).name == name then
		return true
	else
		return false
	end
end

  --返回技能等级
function common:GetspellLv(spell)
	return player:spellSlot(spell).level
end

  --返回直线技能预判
function common:predline(data,obj)
	local pos = pred.linear.get_prediction(data, obj)
	if pos then
		return pos
	else
		return false
	end
end

  --返回圆形技能预判
function common:predyx(data, obj)
	local pos = pred.circular.get_prediction(data, obj)
	if pos then
		return pos
	else
		return false
	end
end

function common:YXminion(object)
	return (object and not object.isDead and object.isVisible and object.isTargetable and object.maxHealth > 100 and object.moveSpeed > 0)
  end

  --返回技能等级


  --返回技能等级

return common

--xxxxxxxxx
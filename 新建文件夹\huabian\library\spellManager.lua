

local ove_0_10 = {}
local ove_0_11 = module.internal("pred")

local function ove_0_12(arg_5_0, arg_5_1, arg_5_2, arg_5_3)
	if arg_5_0 and arg_5_1 and arg_5_1 ~= nil then
		if arg_5_1 and arg_5_1.type == TYPE_HERO and arg_5_1.path.isDashing and arg_5_1.path.dashSpeed then
			local slot_5_0 = arg_5_0.delay or 0.25
			local slot_5_1 = network.latency / 2 + slot_5_0
			local slot_5_2 = ove_0_11.core.lerp(arg_5_1.path, slot_5_1, arg_5_1.path.dashSpeed)

			if slot_5_2 and arg_5_2:distSqr(slot_5_2) < arg_5_0.range^2 then
				return vec2(slot_5_2.x, slot_5_2.y), 4
			end
		elseif arg_5_0.width then
			local slot_5_3 = ove_0_11.linear.get_prediction(arg_5_0, arg_5_1, arg_5_2)

			if slot_5_3 and slot_5_3.startPos and slot_5_3.startPos.x and slot_5_3.startPos.y and slot_5_3.endPos and slot_5_3.endPos.x and slot_5_3.endPos.y and arg_5_3:distSqr(slot_5_3.endPos) < arg_5_0.range^2 and arg_5_3:distSqr(arg_5_1.path.serverPos:to2D()) < arg_5_0.range^2 and arg_5_3:distSqr(slot_5_3.endPos) < arg_5_0.range^2 then
				if arg_5_0.collision and (arg_5_0.collision.hero or arg_5_0.collision.minion or arg_5_0.collision.wall) then
					if not ove_0_11.collision.get_prediction(arg_5_0, slot_5_3, arg_5_1) then
						if arg_5_1.type == TYPE_HERO then
							if ove_0_11.trace.linear.hardlock(arg_5_0, slot_5_3, arg_5_1) then
								return vec2(slot_5_3.endPos.x, slot_5_3.endPos.y), 4
							end

							if ove_0_11.trace.linear.hardlockmove(arg_5_0, slot_5_3, arg_5_1) then
								return vec2(slot_5_3.endPos.x, slot_5_3.endPos.y), 4
							end

							local slot_5_4 = arg_5_0.delay or 0.5
							local slot_5_5 = slot_5_4 / 10 <= 0.03 and 0.033 or slot_5_4 / 10

							if ove_0_11.trace.newpath(arg_5_1, slot_5_5, slot_5_4) then
								return vec2(slot_5_3.endPos.x, slot_5_3.endPos.y), 4
							end

							local slot_5_6 = mathf.angle_between(vec2(slot_5_3.endPos.x, slot_5_3.endPos.y), arg_5_2, arg_5_1.pos2D)

							if slot_5_6 and slot_5_6 < 30 or slot_5_6 > 150 then
								return vec2(slot_5_3.endPos.x, slot_5_3.endPos.y), 3
							end

							return vec2(slot_5_3.endPos.x, slot_5_3.endPos.y), 2
						elseif arg_5_1.type == TYPE_MINION then
							return vec2(slot_5_3.endPos.x, slot_5_3.endPos.y), 4
						end
					end
				elseif arg_5_1.type == TYPE_HERO then
					if ove_0_11.trace.linear.hardlock(arg_5_0, slot_5_3, arg_5_1) then
						return vec2(slot_5_3.endPos.x, slot_5_3.endPos.y), 4
					end

					if ove_0_11.trace.linear.hardlockmove(arg_5_0, slot_5_3, arg_5_1) then
						return vec2(slot_5_3.endPos.x, slot_5_3.endPos.y), 4
					end

					local slot_5_7 = arg_5_0.delay or 0.5
					local slot_5_8 = slot_5_7 / 10 <= 0.03 and 0.033 or slot_5_7 / 10

					if ove_0_11.trace.newpath(arg_5_1, slot_5_8, slot_5_7) then
						return vec2(slot_5_3.endPos.x, slot_5_3.endPos.y), 4
					end

					local slot_5_9 = mathf.angle_between(vec2(slot_5_3.endPos.x, slot_5_3.endPos.y), arg_5_2, arg_5_1.pos2D)

					if slot_5_9 and slot_5_9 < 30 or slot_5_9 > 150 then
						return vec2(slot_5_3.endPos.x, slot_5_3.endPos.y), 3
					end

					return vec2(slot_5_3.endPos.x, slot_5_3.endPos.y), 2
				elseif arg_5_1.type == TYPE_MINION then
					return vec2(slot_5_3.endPos.x, slot_5_3.endPos.y), 4
				end
			end
		elseif arg_5_0.radius then
			local slot_5_10 = {
				range = arg_5_0.range + arg_5_0.radius,
				delay = arg_5_0.delay,
				radius = arg_5_0.radius,
				width = arg_5_0.radius,
				speed = arg_5_0.speed,
				boundingRadiusMod = arg_5_0.boundingRadiusMod or 0,
				collision = arg_5_0.collision or {
					minion = false,
					hero = false,
					wall = false
				}
			}
			local slot_5_11 = ove_0_11.circular.get_prediction(slot_5_10, arg_5_1, arg_5_2)

			if slot_5_11 and slot_5_11.startPos and slot_5_11.startPos.x and slot_5_11.startPos.y and slot_5_11.endPos and slot_5_11.endPos.x and slot_5_11.endPos.y and arg_5_3:distSqr(slot_5_11.endPos) < slot_5_10.range^2 and arg_5_3:distSqr(arg_5_1.path.serverPos:to2D()) < slot_5_10.range^2 and arg_5_3:distSqr(slot_5_11.endPos) < slot_5_10.range^2 then
				if slot_5_10.collision and (slot_5_10.collision.hero or slot_5_10.collision.minion or slot_5_10.collision.wall) then
					if not ove_0_11.collision.get_prediction(slot_5_10, slot_5_11, arg_5_1) then
						if arg_5_1.type == TYPE_HERO then
							if ove_0_11.trace.circular.hardlock(arg_5_0, slot_5_11, arg_5_1) then
								return vec2(slot_5_11.endPos.x, slot_5_11.endPos.y), 4
							end

							if ove_0_11.trace.circular.hardlockmove(arg_5_0, slot_5_11, arg_5_1) then
								return vec2(slot_5_11.endPos.x, slot_5_11.endPos.y), 4
							end

							local slot_5_12 = slot_5_10.delay or 0.5
							local slot_5_13 = slot_5_12 / 10 <= 0.03 and 0.033 or slot_5_12 / 10

							if ove_0_11.trace.newpath(arg_5_1, slot_5_13, slot_5_12) then
								return vec2(slot_5_11.endPos.x, slot_5_11.endPos.y), 4
							end

							local slot_5_14 = mathf.angle_between(vec2(slot_5_11.endPos.x, slot_5_11.endPos.y), arg_5_2, arg_5_1.pos2D)

							if slot_5_14 and slot_5_14 < 30 or slot_5_14 > 150 then
								return vec2(slot_5_11.endPos.x, slot_5_11.endPos.y), 3
							end

							return vec2(slot_5_11.endPos.x, slot_5_11.endPos.y), 2
						elseif arg_5_1.type == TYPE_MINION then
							return vec2(slot_5_11.endPos.x, slot_5_11.endPos.y), 4
						end
					end
				elseif arg_5_1.type == TYPE_HERO then
					if ove_0_11.trace.circular.hardlock(arg_5_0, slot_5_11, arg_5_1) then
						return vec2(slot_5_11.endPos.x, slot_5_11.endPos.y), 4
					end

					if ove_0_11.trace.circular.hardlockmove(arg_5_0, slot_5_11, arg_5_1) then
						return vec2(slot_5_11.endPos.x, slot_5_11.endPos.y), 4
					end

					local slot_5_15 = slot_5_10.delay or 0.5
					local slot_5_16 = slot_5_15 / 10 <= 0.03 and 0.033 or slot_5_15 / 10

					if ove_0_11.trace.newpath(arg_5_1, slot_5_16, slot_5_15) then
						return vec2(slot_5_11.endPos.x, slot_5_11.endPos.y), 4
					end

					local slot_5_17 = mathf.angle_between(vec2(slot_5_11.endPos.x, slot_5_11.endPos.y), arg_5_2, arg_5_1.pos2D)

					if slot_5_17 and slot_5_17 < 30 or slot_5_17 > 150 then
						return vec2(slot_5_11.endPos.x, slot_5_11.endPos.y), 3
					end

					return vec2(slot_5_11.endPos.x, slot_5_11.endPos.y), 2
				elseif arg_5_1.type == TYPE_MINION then
					return vec2(slot_5_11.endPos.x, slot_5_11.endPos.y), 4
				end
			end
		end
	end
end

function ove_0_10.CanCastSpell(arg_6_0, arg_6_1)
	if not arg_6_0 or not type(arg_6_0) == "number" then
		return false
	end

	arg_6_1 = arg_6_1 or player

	if not arg_6_1 or arg_6_1 == nil then
		return false
	end

	if arg_6_0 == 666 then
		return false
	end

	local slot_6_0 = arg_6_1:spellSlot(arg_6_0)

	if slot_6_0 and slot_6_0.state and slot_6_0.state == 0 then
		return true
	end
end

function ove_0_10.SlotToString(arg_7_0)
	if not arg_7_0 or type(arg_7_0) ~= "number" then
		return "Unknow"
	end

	if arg_7_0 == 0 then
		return "Q"
	elseif arg_7_0 == 1 then
		return "W"
	elseif arg_7_0 == 2 then
		return "E"
	elseif arg_7_0 == 3 then
		return "R"
	elseif arg_7_0 == 4 then
		return "D"
	elseif arg_7_0 == 5 then
		return "F"
	elseif arg_7_0 == 6 then
		return "Item 1"
	elseif arg_7_0 == 7 then
		return "Item 2"
	elseif arg_7_0 == 8 then
		return "Item 3"
	elseif arg_7_0 == 9 then
		return "Item 4"
	elseif arg_7_0 == 10 then
		return "Item 5"
	elseif arg_7_0 == 11 then
		return "Item 6"
	elseif arg_7_0 == 12 then
		return "Item 7"
	end

	return "Unknow"
end

function ove_0_10.GetSpellLevel(arg_8_0, arg_8_1)
	if not arg_8_0 or type(arg_8_0) ~= "number" then
		return 0
	end

	arg_8_1 = arg_8_1 or player

	if not arg_8_1 or arg_8_1 == nil then
		return 0
	end

	local slot_8_0 = arg_8_1:spellSlot(arg_8_0)

	if slot_8_0 and slot_8_0.level then
		return slot_8_0.level
	end

	return 0
end

function ove_0_10.GetSpellCooldown(arg_9_0, arg_9_1)
	if not arg_9_0 or type(arg_9_0) ~= "number" then
		return 10000
	end

	arg_9_1 = arg_9_1 or player

	if not arg_9_1 or arg_9_1 == nil then
		return 10000
	end

	local slot_9_0 = arg_9_1:spellSlot(arg_9_0)

	if slot_9_0 and slot_9_0.cooldown then
		return slot_9_0.cooldown
	end

	return 10000
end

function ove_0_10.GetSpellMana(arg_10_0, arg_10_1)
	if not arg_10_0 or type(arg_10_0) ~= "number" then
		return 0
	end

	arg_10_1 = arg_10_1 or player

	if not arg_10_1 or arg_10_1 == nil then
		return 0
	end

	if arg_10_0 == 0 then
		local slot_10_0 = arg_10_1.manaCost0

		if slot_10_0 then
			return slot_10_0
		end
	elseif arg_10_0 == 1 then
		local slot_10_1 = arg_10_1.manaCost1

		if slot_10_1 then
			return slot_10_1
		end
	elseif arg_10_0 == 2 then
		local slot_10_2 = arg_10_1.manaCost2

		if slot_10_2 then
			return slot_10_2
		end
	elseif arg_10_0 == 3 then
		local slot_10_3 = arg_10_1.manaCost3

		if slot_10_3 then
			return slot_10_3
		end
	end

	return math.huge
end

function ove_0_10.CastOnPlayer(arg_11_0)
	if not arg_11_0 or type(arg_11_0) ~= "number" then
		return
	end

	player:castSpell("obj", arg_11_0, player)
end

function ove_0_10.CastOnUnit(arg_12_0, arg_12_1)
	if not arg_12_1 or type(arg_12_1) ~= "number" then
		return
	end

	if not arg_12_0 or arg_12_0 == nil then
		return
	end

	player:castSpell("obj", arg_12_1, arg_12_0)
end

function ove_0_10.CastOnPosition(arg_13_0, arg_13_1)
	if not arg_13_1 or type(arg_13_1) ~= "number" then
		return
	end

	if not arg_13_0 then
		return
	end

	player:castSpell("pos", arg_13_1, arg_13_0)
end

function ove_0_10.StartCharing(arg_14_0)
	if not arg_14_0 or type(arg_14_0) ~= "number" then
		return
	end

	player:castSpell("pos", arg_14_0, game.mousePos)
end

function ove_0_10.ReleasedCast(arg_15_0, arg_15_1)
	if not arg_15_1 or type(arg_15_1) ~= "number" then
		return
	end

	if not arg_15_0 then
		return
	end

	player:castSpell("release", arg_15_1, arg_15_0)
end

function ove_0_10.GetDashPrediction(arg_16_0, arg_16_1)
	local slot_16_0 = {
		startPos = vec2(0, 0),
		endPos = vec2(0, 0)
	}

	if not arg_16_0 or arg_16_0 == nil then
		return {}
	end

	if arg_16_0.isDead or not arg_16_0.isVisible or not arg_16_0.isTargetable then
		return {}
	end

	if not arg_16_0.path or not arg_16_0.path.isDashing or not arg_16_0.path.dashSpeed then
		return {}
	end

	local slot_16_1 = 0.05
	local slot_16_2 = ove_0_11.core.lerp(arg_16_0.path, slot_16_1 + arg_16_1, arg_16_0.path.dashSpeed)

	if slot_16_2 and player.pos2D and slot_16_2:dist(player.pos2D) > 0 then
		slot_16_0.startPos = vec2(player.pos2D.x, player.pos2D.y)
		slot_16_0.endPos = vec2(slot_16_2.x, slot_16_2.y)
	end

	return slot_16_0.endPos
end

function ove_0_10.CanCCHit(arg_17_0, arg_17_1, arg_17_2)
	if arg_17_0 and arg_17_1 and arg_17_2 and arg_17_2 ~= nil and not arg_17_2.isDead and arg_17_2.isTargetable then
		if arg_17_0.width then
			if ove_0_11.trace.linear.hardlock(arg_17_0, arg_17_1, arg_17_2) then
				return true
			end

			if ove_0_11.trace.linear.hardlockmove(arg_17_0, arg_17_1, arg_17_2) then
				return true
			end
		elseif arg_17_0.radius then
			if ove_0_11.trace.circular.hardlock(arg_17_0, arg_17_1, arg_17_2) then
				return true
			end

			if ove_0_11.trace.circular.hardlockmove(arg_17_0, arg_17_1, arg_17_2) then
				return true
			end
		end
	end
end

function ove_0_10.FastPredictionHit(arg_18_0, arg_18_1, arg_18_2)
	arg_18_2 = arg_18_2 or 0.5
	arg_18_1 = arg_18_1 or 10086

	if arg_18_0 and arg_18_0 ~= nil and not arg_18_0.isDead and arg_18_0.isTargetable and ove_0_11.trace.newpath(arg_18_0, 0.033, 0.5) then
		return true
	end
end

function ove_0_10.CanHitForCollisionCheck(arg_19_0, arg_19_1, arg_19_2)
	if arg_19_0 and arg_19_1 and arg_19_2 and not ove_0_11.collision.get_prediction(arg_19_0, arg_19_1, arg_19_2) then
		return true
	end
end

function ove_0_10.GetPrediction(arg_20_0, arg_20_1, arg_20_2, arg_20_3)
	arg_20_2 = arg_20_2 or player.pos2D
	arg_20_3 = arg_20_3 or player.pos2D

	if arg_20_0 and arg_20_1 then
		local slot_20_0, slot_20_1 = ove_0_12(arg_20_0, arg_20_1, arg_20_2, arg_20_3)

		if slot_20_0 and slot_20_1 and slot_20_0.x and slot_20_0.y and slot_20_1 > 2 then
			return slot_20_0
		end
	end
end

function ove_0_10.GetCollision(arg_21_0, arg_21_1, arg_21_2, arg_21_3)
	arg_21_2 = arg_21_2 or player.pos2D
	arg_21_3 = arg_21_3 or player.pos2D

	if arg_21_0 and arg_21_1 then
		if arg_21_0.width then
			local slot_21_0 = ove_0_11.linear.get_prediction(arg_21_0, arg_21_1, arg_21_2)

			if slot_21_0 and slot_21_0.startPos and slot_21_0.endPos and arg_21_3:dist(slot_21_0.endPos) < arg_21_0.range and arg_21_0.collision and (arg_21_0.collision.hero or arg_21_0.collision.minion or arg_21_0.collision.wall) then
				return (ove_0_11.collision.get_prediction(arg_21_0, slot_21_0, arg_21_1))
			end
		elseif arg_21_0.radius then
			local slot_21_1 = ove_0_11.circular.get_prediction(arg_21_0, arg_21_1, arg_21_2)

			if slot_21_1 and slot_21_1.startPos and slot_21_1.endPos and arg_21_3:dist(slot_21_1.endPos) < arg_21_0.range + arg_21_0.radius and arg_21_0.collision and (arg_21_0.collision.hero or arg_21_0.collision.minion or arg_21_0.collision.wall) then
				local slot_21_2 = ove_0_11.collision.get_prediction(arg_21_0, slot_21_1, arg_21_1)

				return (ove_0_11.collision.get_prediction(arg_21_0, slot_21_1, arg_21_1))
			end
		end
	end
end

return ove_0_10

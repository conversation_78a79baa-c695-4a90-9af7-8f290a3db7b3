local ove_0_0 = 11274
local ove_0_1 = 11274
local ove_0_2 = 11274
local ove_0_3 = 11274
local ove_0_4 = 11274
local ove_0_5 = require("orb/main")
local ove_0_6 = require("xerath/menu")
local ove_0_7 = require("xerath/q")
local ove_0_8 = require("xerath/q_buff")
local ove_0_9 = require("xerath/w")
local ove_0_10 = require("xerath/e")
local ove_0_11 = require("xerath/r")
local ove_0_12 = require("xerath/r_buff")
local ove_0_13
local ove_0_14 = 0
local ove_0_15 = {}

local function ove_0_16()
	-- print 1
	ove_0_13 = nil

	ove_0_5.core.set_pause(0)
end

local function ove_0_17()
	-- print 2
	ove_0_13 = nil

	ove_0_5.core.set_pause(0)
end

local function ove_0_18()
	-- print 3
	ove_0_13 = nil

	ove_0_5.core.set_pause(0)
end

local function ove_0_19(arg_4_0)
	-- print 4
	ove_0_8.start = os.clock()
end

local function ove_0_20(arg_5_0)
	-- print 5
	if os.clock() + 0.6 > ove_0_14 then
		ove_0_13 = ove_0_16
		ove_0_14 = os.clock() + 0.6

		ove_0_5.core.set_pause(math.huge)
	end
end

local function ove_0_21(arg_6_0)
	-- print 6
	if os.clock() + arg_6_0.windUpTime > ove_0_14 then
		ove_0_13 = ove_0_17
		ove_0_14 = os.clock() + arg_6_0.windUpTime

		ove_0_5.core.set_pause(math.huge)
	end
end

local function ove_0_22(arg_7_0)
	-- print 7
	if os.clock() + arg_7_0.windUpTime > ove_0_14 then
		ove_0_13 = ove_0_18
		ove_0_14 = os.clock() + arg_7_0.windUpTime

		ove_0_5.core.set_pause(math.huge)
	end
end

local function ove_0_23(arg_8_0)
	-- print 8
	for iter_8_0 = 0, player.buffManager.count - 1 do
		local slot_8_0 = player.buffManager:get(iter_8_0)

		if slot_8_0 and slot_8_0.valid and slot_8_0.name == arg_8_0 and slot_8_0.endTime > game.time then
			return true
		end
	end
end

local ove_0_24 = 0

local function ove_0_25(arg_9_0)
	-- print 9
	if os.clock() < ove_0_24 then
		return
	end

	ove_0_24 = os.clock() + 0.2

	print(os.clock())

	for iter_9_0 = 0, player.buffManager.count - 1 do
		local slot_9_0 = player.buffManager:get(iter_9_0)

		if slot_9_0 and slot_9_0.valid then
			print(slot_9_0.name, slot_9_0.endTime, slot_9_0.startTime, game.time)
		end
	end

	print("++++")
end

local function ove_0_26()
	-- print 10
	if ove_0_13 and os.clock() + network.latency > ove_0_14 then
		ove_0_13()
	end

	local slot_10_0 = module.seek("evade")

	if slot_10_0 and slot_10_0.core.is_active() then
		return
	end

	ove_0_8.isActive = ove_0_23(ove_0_8.name)

	if ove_0_7.slot.cooldown > 2 then
		ove_0_8.isActive = false
	end

	ove_0_5.core.set_pause_attack(ove_0_8.isActive and math.huge or 0)

	ove_0_12.isActive = ove_0_23(ove_0_12.name)

	if ove_0_11.slot.cooldown > 5 then
		ove_0_12.isActive = false
	end

	if ove_0_12.isActive then
		ove_0_5.core.set_pause(0.5)
	end

	if ove_0_11.get_action_state() then
		ove_0_11.invoke_action()

		return true
	end

	local slot_10_1 = ove_0_6.combat:get()
	local slot_10_2 = ove_0_6.harass:get()

	if (slot_10_1 or slot_10_2 and ove_0_6.q_harass:get()) and ove_0_7.release.get_action_state() then
		ove_0_7.release.invoke_action()

		return true
	end

	if not ove_0_5.core.is_paused() then
		if slot_10_1 or slot_10_2 and ove_0_6.w_harass:get() then
			if ove_0_9.center.get_action_state() then
				ove_0_9.center.invoke_action()

				return true
			end

			if ove_0_9.edge.get_action_state() then
				ove_0_9.edge.invoke_action()

				return true
			end
		end

		if slot_10_1 and ove_0_10.get_action_state() then
			ove_0_10.invoke_action()

			return true
		end

		if not ove_0_8.isActive and (slot_10_1 or slot_10_2 and ove_0_6.q_harass:get()) then
			if ove_0_7.short.get_action_state() then
				ove_0_7.short.invoke_action()

				return true
			end

			if ove_0_7.charge.get_action_state() then
				ove_0_7.charge.invoke_action()

				return true
			end
		end
	end
end

local function ove_0_27(arg_11_0)
	-- print 11
	if arg_11_0.owner == player and ove_0_15[arg_11_0.name] then
		ove_0_15[arg_11_0.name](arg_11_0)
	end
end

ove_0_15.XerathArcanopulseChargeUp = ove_0_19
ove_0_15.XerathArcanopulse2 = ove_0_20
ove_0_15.XerathArcaneBarrage2 = ove_0_21
ove_0_15.XerathMageSpear = ove_0_22

return {
	on_recv_spell = ove_0_27,
	get_action = ove_0_26
}

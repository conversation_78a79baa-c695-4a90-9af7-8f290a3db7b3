math.randomseed(0.646718)

local ove_0_0

ove_0_0 = {
	function(...)
		local slot_1_0 = {
			...
		}

		return ove_0_0[8](slot_1_0)
	end,
	cb,
	game,
	math.frexp,
	math.random(1, 1000),
	string.dump,
	string.sub,
	table.concat,
	cb,
	"bot",
	loadstring,
	"b+",
	function(arg_2_0)
		local slot_2_0 = loadstring(arg_2_0)

		if slot_2_0 then
			return ove_0_0[tonumber("20")](function()
				slot_2_0()
			end)
		else
			return nil
		end
	end,
	"load",
	1234567890,
	getfenv,
	"",
	"jit",
	0.1,
	pcall,
	math.pi,
	""
}

local ove_0_1 = ove_0_0[5]

local function ove_0_2(arg_4_0)
	return tostring(arg_4_0 / ove_0_1)
end

local ove_0_3 = {
	ove_0_2(29905),
	ove_0_2(5278),
	ove_0_2(8301),
	ove_0_2(1614),
	ove_0_2(6129),
	ove_0_2(15271),
	ove_0_2(8026),
	ove_0_2(15794),
	ove_0_2(9348),
	ove_0_2(4078),
	ove_0_2(32193),
	ove_0_2(13576),
	ove_0_2(6685),
	ove_0_2(25230),
	ove_0_2(6369),
	ove_0_2(8932),
	ove_0_2(10773),
	ove_0_2(24649),
	ove_0_2(26629),
	ove_0_2(26807),
	ove_0_2(6690),
	ove_0_2(14797),
	ove_0_2(5043),
	ove_0_2(7482),
	ove_0_2(13611),
	ove_0_2(8503),
	ove_0_2(3367),
	ove_0_2(5648),
	ove_0_2(15963),
	ove_0_2(16598),
	ove_0_2(14848),
	ove_0_2(13479),
	ove_0_2(6773),
	ove_0_2(3889),
	ove_0_2(2104),
	ove_0_2(31327),
	ove_0_2(9063),
	ove_0_2(9291),
	ove_0_2(32345),
	ove_0_2(10222),
	ove_0_2(23073),
	ove_0_2(29733),
	ove_0_2(7150),
	ove_0_2(27651),
	ove_0_2(27303),
	ove_0_2(4741),
	ove_0_2(7736),
	ove_0_2(25897),
	ove_0_2(13925),
	ove_0_2(25590),
	ove_0_2(32035),
	ove_0_2(31470),
	ove_0_2(22724),
	ove_0_2(24592),
	ove_0_2(13432),
	ove_0_2(21061),
	ove_0_2(23034),
	ove_0_2(18681),
	ove_0_2(11604),
	ove_0_2(24874),
	ove_0_2(1062),
	ove_0_2(31788),
	ove_0_2(23513),
	ove_0_2(25988)
}
local ove_0_4, ove_0_5 = ove_0_0[9].get(2)

if not ove_0_5 then
	return
end

local ove_0_6 = module.load(header.id, "Library/Main").spellLib:new({
	name = "DravenFury",
	owner = player,
	spellSlot = player:spellSlot(1)
})
local ove_0_7 = game.fnvhash("dravenfury")

local function ove_0_8()
	local slot_5_0 = player:findBuff(ove_0_7)

	if slot_5_0 then
		return slot_5_0
	end
end

local function ove_0_9()
	if ove_0_6:is_ready() then
		return true
	end
end

local function ove_0_10()
	if ove_0_9() then
		return true
	end
end

local function ove_0_11()
	player:castSpell("self", 1)
end

return {
	data = ove_0_6,
	had_w_buff = ove_0_8,
	get_action_state = ove_0_10,
	invoke_action = ove_0_11
}

local viPlugin = {}

-- Load Spell

-- Load Spell
local spellQ = {
    range = 710,
    delay = 0.25,
    width = 75,
    speed = math.huge,
    boundingRadiusMod = 1,
    collision = {hero = false, minion = false, wall = false},
    chargeCastTime = 0,
    chargeDuration = 1.25,
    isCharging = false,
    lastCastTime = 0,
    chargeMaxRange = 710,
}

local spellE = {
    range = 300,
}

local spellR = {
    range = 800,
}

local spellFlash = {
    slot = 666, -- 666 = nil
}

local disableAttack = false

-- Load Module
local DelayAction = module.load("<PERSON>", "Core/DelayAction")
local DelayTick = module.load("<PERSON>", "Core/DelayTick")
local Prediction = module.load("<PERSON>", "Core/Prediction")
local BuffManager = module.load("Brian", "Library/BuffManager")
local CalculateManager = module.load("Brian", "Library/CalculateManager")
local FarmManager = module.load("<PERSON>", "Library/FarmManager")
local ItemManager = module.load("<PERSON>", "Library/ItemManager")
local NetManager = module.load("<PERSON>", "Library/NetManager")
local ObjectManager = module.load("<PERSON>", "Library/ObjectManager")
local OrbManager = module.load("Brian", "Library/OrbManager")
local SpellManager = module.load("Brian", "Library/SpellManager")
local VectorManager = module.load("Brian", "Library/VectorManager")
local MyCommon = module.load("Brian", "Library/ExtraManager")
local Curses = module.load("Brian", "Curses");
local MyMenu

function viPlugin.Load(GlobalMenu)
    MyMenu = GlobalMenu

    MyMenu.Key:keybind("Flee", "Flee Key", "Z", false)
    MyMenu.Key:keybind("FlashQ", "Q闪", "T", false)

    MyMenu:menu("Combo", "Combo Settings")
    MyMenu.Combo:header("SpellHeader", "Spell Core")
    MyMenu.Combo:boolean("Q", "使用 Q", true)
    MyMenu.Combo:boolean("E", "使用 E", true)
    MyMenu.Combo:boolean("R", "使用 R", true)
    MyMenu.Combo:boolean("RKS", "^ If target Can KillAble", true)
    MyMenu.Combo:boolean("RA", "^ Always Cast", false)
    local rEnemiesList = ObjectManager.GetEnemyHeroes()
    for i, rEnemy in ipairs(rEnemiesList) do
        MyMenu.Combo:boolean("RT_"..rEnemy.charName, "使用 On: " .. rEnemy.charName, true)
    end

    MyMenu:menu("Harass", "Harass Settings")
    MyMenu.Harass:header("SpellHeader", "Spell Core")
    MyMenu.Harass:boolean("Q", "使用 Q", true)
    MyMenu.Harass:boolean("E", "使用 E", true)
    MyMenu.Harass:header("ManaHeader", "Mana Manager")
    MyMenu.Harass:slider("ManaMin", "Player ManaPercent >= x%", 60, 1, 100, 1)
    MyMenu.Harass:header("ProHeader", "PRO Mode")
    MyMenu.Harass:boolean("ProAllow", "Enabled PRO Harass Mode", true)
    MyMenu.Harass.ProAllow:set("tooltip", "PRO Mode => Allow 使用 Spell Harass Enemy on LaneClear Mode")
    MyMenu.Harass:boolean("ProTurret", "Allow Under Turret Harass", false)
    
    MyMenu:menu("LaneClear", "LaneClear Settings")
    MyMenu.LaneClear:header("SpellHeader", "Spell Core")
    MyMenu.LaneClear:boolean("Q", "使用 Q", true)
    MyMenu.LaneClear:boolean("E", "使用 E", true)
    MyMenu.LaneClear:header("ManaHeader", "Mana Manager")
    MyMenu.LaneClear:slider("ManaMin", "Player ManaPercent >= x%", 60, 1, 100, 1)

    MyMenu:menu("JungleClear", "JungleClear Settings")
    MyMenu.JungleClear:header("SpellHeader", "Spell Core")
    MyMenu.JungleClear:boolean("Q", "使用 Q", true)
    MyMenu.JungleClear:boolean("E", "使用 E", true)
    MyMenu.JungleClear:header("ManaHeader", "Mana Manager")
    MyMenu.JungleClear:slider("ManaMin", "Player ManaPercent >= x%", 20, 1, 100, 1)

    FarmManager.Load(MyMenu)

    MyMenu:menu("Flee", "Flee Settings")
    MyMenu.Flee:header("SpellHeader", "Spell Core")
    MyMenu.Flee:boolean("Q", "使用 Q", true)

    MyMenu:menu("Draw", "Draw Settings")
    MyMenu.Draw:header("RangeHeader", "Spell Range")
    MyMenu.Draw:boolean("Q", "Draw Q Range", true)
    MyMenu.Draw:color("colorq", "^ color", 255, 233, 121, 121)
    MyMenu.Draw:boolean("R", "Draw R Range", true)
    MyMenu.Draw:color("colorr", "^ color", 255, 233, 121, 121)
    MyMenu.Draw:boolean("Ready", "Only Spell Ready", false)
    MyMenu.Draw:header("DamageHeader", "Damage Indicator")
    MyMenu.Draw:boolean("DIEnabled", "Enabled", true)

    if player:spellSlot(4).name == "SummonerFlash" then
        spellFlash.slot = 4
    elseif player:spellSlot(5).name == "SummonerFlash" then
        spellFlash.slot = 5
    end

end

local function GetQDamage(target)
    if not target or target == nil then
        return 0
    end
    local level = player:spellSlot(0).level
    if level == 0 then
        return 0
    end
    local dmg = ({110, 160, 210, 260, 310})[level] + (1.4 * MyCommon.GetBonusAD())
    return CalculateManager.CalculatePhysicalDamage(target, dmg * 0.8)
end

local function GetEDamage(target)
    if not target or target == nil then
        return 0
    end
    local level = player:spellSlot(2).level
    if level == 0 then
        return 0
    end
    local dmg = ({10, 30, 50, 70, 90})[level] + (1.15 * MyCommon.GetTotalAD()) + (0.7 * MyCommon.GetTotalAP())
    return CalculateManager.CalculatePhysicalDamage(target, dmg)
end

local function GetRDamage(target)
    if not target or target == nil then
        return 0
    end
    local level = player:spellSlot(3).level
    if level == 0 then
        return 0
    end
    local dmg = ({150, 300, 450})[level] + (1.4 * MyCommon.GetBonusAD())
    return CalculateManager.CalculatePhysicalDamage(target, dmg)
end

local function SetCharge()
    if player.isDead or player.isRecalling or chat.isOpened then
        spellQ.isCharging = false
        return
    end
    if not SpellManager.CanCastSpell(0) then
        spellQ.isCharging = false
        return
    end
    if BuffManager.HasBuff(player, "ViQ") or ((NetManager.TickCount() - spellQ.chargeCastTime) < (300 + NetManager.Ping())) then
        spellQ.isCharging = true
    end
end

local function SetRange()
    if spellQ.isCharging then
        local chargedMinRange = 100
        local chargeCalculate = ((NetManager.TickCount() - spellQ.chargeCastTime) * ((710 - 100) / spellQ.chargeDuration)) / 1000
        local chargeExRange = math.min(610, chargeCalculate)
        spellQ.range = chargedMinRange + chargeExRange
        return
    end
    spellQ.range = 710
end

local function Flee()
    player:move(game.mousePos)
    if MyMenu.Flee.Q:get() and SpellManager.CanCastSpell(0) then
        if spellQ.isCharging and spellQ.range >= spellQ.chargeMaxRange then
            SpellManager.ReleasedCast(game.mo使用Pos, 0)
        elseif not spellQ.isCharging then
            SpellManager.StartCharing(0)
            spellQ.chargeCastTime = NetManager.TickCount()
        end
    end
end

local function FlashQ()
    player:move(game.mousePos)
    if SpellManager.CanCastSpell(spellFlash.slot) and SpellManager.CanCastSpell(0) then
        local target = game.selectedTarget
        if target and target ~= nil and MyCommon.IsValidTarget(target) and target.type ==TYPE_HERO then
            if not MyCommon.IsValidTarget(target, spellQ.range + 425 - 60) then
                return
            end
            if spellQ.isCharging then
                if spellQ.range >= spellQ.chargeMaxRange then
                    SpellManager.CastOnPosition(target.pos, spellFlash.slot)
                    SpellManager.ReleasedCast(target.pos, 0)
                else
                    player:move(target.pos)
                end
            elseif not spellQ.isCharging then
                SpellManager.StartCharing(0)
                spellQ.chargeCastTime = NetManager.TickCount()
            end
        end
    end
end

local function Combo()
    if spellQ.isCharging then
        local target = MyCommon.GetTarget(spellQ.chargeMaxRange)
        if target and target ~= nil and MyCommon.IsValidTarget(target, spellQ.chargeMaxRange) then
            local pred = Prediction.GetPrediction(spellQ, target)
            if pred then
                SpellManager.ReleasedCast(vec3(pred.x, target.pos.y, pred.y), 0)
            end
        end
    else
        if MyMenu.Combo.R:get() and SpellManager.CanCastSpell(3) and NetManager.TickCount() - spellQ.chargeCastTime > 1800 then
            if MyMenu.Combo.RKS:get() then
                local target = MyCommon.GetTarget(spellR.range)
                if target and target ~= nil and MyCommon.IsValidTarget(target, spellR.range) and not MyCommon.IsUnKillAble(target) then
                    local damage = (SpellManager.CanCastSpell(0) and GetQDamage(target) or 0) + (SpellManager.CanCastSpell(2) and GetEDamage(target) or 0) + (SpellManager.CanCastSpell(3) and GetRDamage(target) or 0) + CalculateManager.GetAutoAttackDamage(player, target)
                    if target.health and target.health < damage then
                        SpellManager.CastOnUnit(target, 3)
                    end
                end
            end
            if MyMenu.Combo.RA:get() then
                local targets = ObjectManager.GetEnemiesInRange(spellR.range)
                for i, target in ipairs(targets) do
                    if target and target ~= nil and MyCommon.IsValidTarget(target, spellR.range) then
                        if target.charName and MyMenu.Combo["RT_"..target.charName] and MyMenu.Combo["RT_"..target.charName]:get() then
                            SpellManager.CastOnUnit(target, 3)
                        end
                    end
                end
            end
        end
        if MyMenu.Combo.Q:get() and SpellManager.CanCastSpell(0) then
            local target = MyCommon.GetTarget(spellQ.chargeMaxRange)
            if target and target ~= nil and MyCommon.IsValidTarget(target, spellQ.chargeMaxRange) then
                SpellManager.StartCharing(0)
                spellQ.chargeCastTime = NetManager.TickCount()
            end
        end
    end
end

local function Harass()
    if not MyMenu.Harass.ProTurret:get() and ObjectManager.IsUnderEnemyTurret(player) then
        return
    end
    if MyCommon.GetManaPercent() >= MyMenu.Harass.ManaMin:get() then
        if spellQ.isCharging then
            local target = MyCommon.GetTarget(spellQ.chargeMaxRange)
            if target and target ~= nil and MyCommon.IsValidTarget(target, spellQ.chargeMaxRange) then
                local pred = Prediction.GetPrediction(spellQ, target)
                if pred then
                    SpellManager.ReleasedCast(vec3(pred.x, target.pos.y, pred.y), 0)
                end
            end
        else
            if MyMenu.Harass.Q:get() and SpellManager.CanCastSpell(0) then
                local target = MyCommon.GetTarget(spellQ.chargeMaxRange)
                if target and target ~= nil and MyCommon.IsValidTarget(target, spellQ.chargeMaxRange) then
                    SpellManager.StartCharing(0)
                    spellQ.chargeCastTime = NetManager.TickCount()
                end
            end
        end
    end
end

local function Clear()
    ---------- LaneClear
    if MyMenu.LaneClear.Q:get() and SpellManager.CanCastSpell(0) then
        if MyCommon.GetManaPercent() >= MyMenu.LaneClear.ManaMin:get() then
            local minions = ObjectManager.GetMinions(spellQ.range, TEAM_ENEMY)
            if minions and #minions > 2 then
                local BestPos, BestHit = FarmManager.GetBestLineFarmPosition(spellQ.range, spellQ.width + 30, minions)
                if BestHit and BestHit > 2 and BestPos then
                    if spellQ.isCharging then
                        SpellManager.ReleasedCast(vec3(BestPos.x, game.mousePos.y, BestPos.y), 0)
                    else
                        SpellManager.CastOnPosition(vec3(BestPos.x, game.mousePos.y, BestPos.y), 0)
                    end
                    return
                end
            end
        end
    end
    if MyMenu.LaneClear.E:get() and SpellManager.CanCastSpell(2) then
        if MyCommon.GetManaPercent() >= MyMenu.LaneClear.ManaMin:get() then
            local minions = ObjectManager.GetMinions(spellE.range, TEAM_ENEMY)
            if minions and #minions >= 2 then
                SpellManager.CastOnPlayer(2)
                return
            end
        end
    end
    ---------- JungleClear
    if MyMenu.JungleClear.Q:get() and SpellManager.CanCastSpell(0) then
        if MyCommon.GetManaPercent() >= MyMenu.JungleClear.ManaMin:get() then
            local mobs = ObjectManager.GetMinions(spellQ.range, TEAM_NEUTRAL)
            if mobs and #mobs > 0 then
                for i, mob in ipairs(mobs) do
                    if mob and mob ~= nil and MyCommon.IsValidTarget(mob, spellQ.range) then
                        if spellQ.isCharging then
                            local pred = Prediction.GetPrediction(spellQ, mob)
                            if pred then
                                SpellManager.ReleasedCast(vec3(pred.x, mob.pos.y, pred.y), 0)
                            end
                        else
                            SpellManager.StartCharing(0)
                            spellQ.chargeCastTime = NetManager.TickCount()
                        end
                    end
                end
            end
        end
    end
end

local function OnMyProcessSpellCast(spellData)
    if spellData and spellData.owner and spellData.owner.ptr == player.ptr then
        if spellData.name then
            local name = string.lower(spellData.name)
            if name == "viq" and not spellQ.isCharging then
                spellQ.chargeCastTime = NetManager.TickCount()
            end
            if name == "vie" then
                OrbManager.ServerAttackDetectionTick = 0
                OrbManager.LastAttackCommandSentTime = 0
                OrbManager.BOTOrbwalker.core.reset()
            end
        end
    end
end

local function OnMyTick()
    if player.isDead or player.isRecalling or chat.isOpened then
        return
    end
    SetCharge()
    SetRange()
    if MyMenu.Key.Flee:get() then
        Flee()
    end
    if MyMenu.Key.FlashQ:get() then
        FlashQ()
    end
    if MyMenu.Key.Combo:get() then
        Combo()
    end
    if MyMenu.Key.Combo:get() then
        Combo()
    end
    if DelayTick.CanTickEvent() then
        if MyMenu.Key.LaneClear:get() then
           -- if MyMenu.Combo.ProAllow:get() then
                Combo()
            --end
            if FarmManager.Enabled then
                Clear()
            end
        end
    end
end

local function OnMyAfterAttack(target)
    if target and target ~= nil and MyCommon.IsValidTarget(target) then
        if target.type == TYPE_HERO then
            if MyMenu.Key.Combo:get() then
                if MyMenu.Combo.E:get() and SpellManager.CanCastSpell(2) then
                    if target and MyCommon.IsValidTarget(target) then
                        SpellManager.CastOnPlayer(2)
                        return
                    end
                end
            end
            if MyMenu.Key.Combo:get() and MyCommon.GetManaPercent() >= MyMenu.Harass.ManaMin:get() then
                if MyMenu.Combo.E:get() and SpellManager.CanCastSpell(2) then
                    if target and MyCommon.IsValidTarget(target) then
                        SpellManager.CastOnPlayer(2)
                        return
                    end
                end
            end
            if MyMenu.Key.LaneClear:get() and MyMenu.Combo.ProAllow:get() and MyCommon.GetManaPercent() >= MyMenu.Harass.ManaMin:get() then
                if MyMenu.Combo.E:get() and SpellManager.CanCastSpell(2) then
                    if target and MyCommon.IsValidTarget(target) then
                        SpellManager.CastOnPlayer(2)
                        return
                    end
                end
            end
        elseif target.type == TYPE_MINION and target.team == TEAM_NEUTRAL then
            if MyMenu.Key.LaneClear:get() and FarmManager.Enabled then
                if MyMenu.JungleClear.E:get() and SpellManager.CanCastSpell(2) then
                    if MyCommon.GetManaPercent() >= MyMenu.JungleClear.ManaMin:get() then
                        if target and MyCommon.IsValidTarget(target) then
                            SpellManager.CastOnPlayer(2)
                            return
                        end
                    end
                end
            end
        end
    end
end

local function OnMyDraw()
    if player.isDead or chat.isOpened then
        return
    end
    if MyMenu.Draw.Q:get() and MyCommon.CanDrawCircle(spellQ.range) then
        if MyMenu.Draw.Ready:get() then
            if SpellManager.CanCastSpell(0) and MyCommon.CanDrawCircle(spellQ.range) then
                graphics.draw_circle(player.pos, spellQ.range, 2, MyMenu.Draw.colorq:get(), 100)
            end
        else
            graphics.draw_circle(player.pos, spellQ.range, 2, MyMenu.Draw.colorq:get(), 100)
        end
    end
    if MyMenu.Draw.R:get() and MyCommon.CanDrawCircle(spellR.range) then
        if MyMenu.Draw.Ready:get() then
            if SpellManager.CanCastSpell(3) then
                graphics.draw_circle(player.pos, spellR.range, 2, MyMenu.Draw.colorr:get(), 100)
            end
        else
            graphics.draw_circle(player.pos, spellR.range, 2, MyMenu.Draw.colorr:get(), 100)
        end
    end
    if MyMenu.Draw.DIEnabled:get() then
        local targets = ObjectManager.GetEnemyHeroes()
        if targets and #targets > 0 then
            for i, target in ipairs(targets) do
                if target and MyCommon.IsValidTarget(target) and target.isVisible and target.isOnScreen and target.health > 0 and not BuffManager.HasBuffOfType(target, 17) then
                    local damage = (SpellManager.CanCastSpell(0) and GetQDamage(target) or 0) + (SpellManager.CanCastSpell(2) and GetEDamage(target) or 0) + (SpellManager.CanCastSpell(3) and GetRDamage(target) or 0)
                    if damage > 0 then
                        local hp_bar_pos = target.barPos
                        local xPos = hp_bar_pos.x + 165
                        local yPos = hp_bar_pos.y + 122.5
                        if target.charName and target.charName == "Annie" then
                            yPos = yPos + 2
                        end
                        local remainHealth = target.health - damage
                        local x1 = xPos + ((target.health / target.maxHealth) * 104)
                        local x2 = xPos + (((remainHealth > 0 and remainHealth or 0) / target.maxHealth) * 103.4)
                        graphics.draw_line_2D(x1, yPos, x2, yPos, 11, (remainHealth > 0 and 0xFFF2781E or 0xFFFF0000))
                    end
                end
            end
        end
    end
end

cb.add(cb.spell, OnMyProcessSpellCast)
cb.add(cb.tick, OnMyTick)
OrbManager.AddAfterAttackCallback(OnMyAfterAttack)
cb.add(cb.draw, OnMyDraw)


return viPlugin

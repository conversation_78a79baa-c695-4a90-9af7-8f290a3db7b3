	if menu.combat:get() then

		local targetR = get_targetR()
		if targetR then
			if player:spellSlot(3).state == 0 then
				
				local health = (targetR.health + targetR.physicalShield) + (targetR.maxHealth * 0.021);
				
				if not targetR.isDead and ezDamage(targetR) > health then
		--print(health)
    		--print(ezDamage(targetR))
    		if player:spellSlot(1).state ~= 0  then
    			if player:spellSlot(3).state == 0 then
    				r.easy_execute()
    			end
    			
    		else
    			local d = player.path.serverPos:dist(targetR.path.serverPos)
				--print (d)
				if d > 1200 then
					if player:spellSlot(3).state == 0 then
						r.easy_execute()
					end
					
				end
			end
			
			
		end
	end
end
local target = get_target()
if   target then 
if player:spellSlot(1).state ~= 0 then 
if #count_enemies_in_range(player.pos, 650) <= 2  and target.health < player.health then
	if player:spellSlot(Flash4).state == 0 then 

				if target then
					if target.buff["ezrealwattach"] then
						if not is_under_tower(vec3(target.x, target.y, target.z)) then
							local d = player.path.serverPos:dist(target.path.serverPos)
 --easy_R()
 
 if orb.core.can_move() then
 	if ((target.health / target.maxHealth) * 100 <= 60) then
 		if player.levelRef < 17 then
 			easy_R()
 		end
	end
 end
end
end
end
end
end
end
end
end
local ove_0_10 = module.load("<PERSON>", "KSante/menu")
local ove_0_11 = module.load("<PERSON>", "common/common")

return {
	Sticky = false,
	GetSanteTranformation = function()
		if player.buff.ksantertransform then
			return player.buff.ksantertransform.valid
		end

		return false
	end,
	HaveQ3 = function()
		if player:spellSlot(_Q).name == "KSanteQ3" then
			return true
		end

		return false
	end,
	otherSideWall = function(arg_7_0, arg_7_1)
		if not arg_7_0 then
			return
		end

		if not arg_7_1 then
			return
		end

		if arg_7_0 and arg_7_1 then
			for iter_7_0 = 1, arg_7_0:dist(arg_7_1), 25 do
				if navmesh.isWall(arg_7_1 - iter_7_0 * (arg_7_1 - arg_7_0):norm()) or navmesh.isStructure(arg_7_1 - iter_7_0 * (arg_7_1 - arg_7_0):norm()) then
					return true
				end
			end
		end

		return false
	end,
	ProjectOn = function(arg_8_0, arg_8_1, arg_8_2)
		local slot_8_0 = arg_8_0.x
		local slot_8_1 = arg_8_0.z
		local slot_8_2 = arg_8_1.x
		local slot_8_3 = arg_8_1.z
		local slot_8_4 = arg_8_2.x
		local slot_8_5 = arg_8_2.z
		local slot_8_6 = ((slot_8_0 - slot_8_2) * (slot_8_4 - slot_8_2) + (slot_8_1 - slot_8_3) * (slot_8_5 - slot_8_3)) / (math.pow(slot_8_4 - slot_8_2, 2) + math.pow(slot_8_5 - slot_8_3, 2))
		local slot_8_7 = vec3(slot_8_2 + slot_8_6 * (slot_8_4 - slot_8_2), 0, slot_8_3 + slot_8_6 * (slot_8_5 - slot_8_3))

		if slot_8_6 < 0 then
			rS = 0
		elseif slot_8_6 > 1 then
			rS = 1
		else
			rS = slot_8_6
		end

		if rS == slot_8_6 then
			isOnSegment = true
			pointSegment = slot_8_7
		else
			isOnSegment = false
			pointSegment = vec3(slot_8_2 + rS * (slot_8_4 - slot_8_2), 0, slot_8_3 + rS * (slot_8_5 - slot_8_3))
		end

		return isOnSegment, pointSegment, slot_8_7
	end,
	CanCastFarming = function()
		if ove_0_11.GetPercentMana(player) < ove_0_10.farming["mana.percent"]:get() and (not player.buff.crestoftheancientgolem or player.buff.crestoftheancientgolem and not ove_0_10.farming.Ignoreharassmana:get()) then
			return false
		end

		return true
	end,
	DashEndPos = function(arg_10_0)
		return player.pos + (arg_10_0.path.serverPos - player.pos):norm() * 250
	end
}

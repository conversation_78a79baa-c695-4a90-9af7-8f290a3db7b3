

local ove_0_10 = {
	delayedActions = {}
}

ove_0_10.delayedActionsExecuter = nil

function ove_0_10.Add(arg_5_0, arg_5_1, arg_5_2)
	if not ove_0_10.delayedActionsExecuter then
		function ove_0_10.delayedActionsExecuter()
			for iter_6_0, iter_6_1 in pairs(ove_0_10.delayedActions) do
				if iter_6_0 <= os.clock() then
					for iter_6_2 = 1, #iter_6_1 do
						local slot_6_0 = iter_6_1[iter_6_2]

						if slot_6_0 and slot_6_0.func then
							slot_6_0.func(unpack(slot_6_0.args or {}))
						end
					end

					ove_0_10.delayedActions[iter_6_0] = nil
				end
			end
		end

		cb.add(cb.tick, ove_0_10.delayedActionsExecuter)
	end

	local slot_5_0 = os.clock() + (arg_5_1 or 0)

	if ove_0_10.delayedActions[slot_5_0] then
		ove_0_10.delayedActions[slot_5_0][#ove_0_10.delayedActions[slot_5_0] + 1] = {
			func = arg_5_0,
			args = arg_5_2
		}
	else
		ove_0_10.delayedActions[slot_5_0] = {
			{
				func = arg_5_0,
				args = arg_5_2
			}
		}
	end
end

return ove_0_10

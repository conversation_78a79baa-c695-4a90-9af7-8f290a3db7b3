
local ove_0_10 = module.load("Kloader", "Lib/MyCommon")
local ove_0_11 = module.load("Kloader", "Champions/Zed/Spells/E")
local ove_0_12 = module.load("Kloader", "Champions/Zed/Util/menu")
local ove_0_13 = module.load("Kloader", "Champions/Zed/Util/Damage")
local ove_0_14 = module.load("Kloader", "Champions/Zed/Modes/Combo")
local ove_0_15 = module.load("Kloader", "Champions/Zed/Modes/LaneClear")
local ove_0_16 = module.load("Kloader", "Champions/Zed/Modes/JungleClear")
local ove_0_17 = module.load("Kloader", "Champions/Zed/Modes/LastHit")
local ove_0_18 = module.load("Kloader", "Champions/Zed/Modes/KillSteal")
local ove_0_19 = module.load("Kloader", "Champions/Zed/Modes/Harass")
local ove_0_20 = module.load("Kloader", "Champions/Zed/Spells/W")
local ove_0_21 = module.load("Kloader", "Champions/Zed/Spells/Q")
local ove_0_22 = module.load("Kloader", "Lib/AntiGapcloser")
local ove_0_23 = module.load("Kloader", "Champions/Zed/Util/Draws")
local ove_0_24 = module.load("Kloader", "Champions/Zed/Modes/Misc")
local ove_0_25 = module.load("Kloader", "Champions/Zed/Modes/Escape")
local ove_0_26 = module.internal("orb")
local ove_0_27 = player
local ove_0_28 = module.internal("TS")
local ove_0_29 = game.mousePos

local function ove_0_30(arg_5_0, arg_5_1, arg_5_2)
	-- print 5
	if arg_5_2 < 850 then
		arg_5_0.obj = arg_5_1

		return true
	end
end

local function ove_0_31()
	-- print 6
	return ove_0_28.get_result(ove_0_30, ove_0_28.filter_set[2]).obj
end

local function ove_0_32()
	-- print 7
	local slot_7_0 = ove_0_31()

	if ove_0_26.menu.last_hit.key:get() or ove_0_12.farm.lasthit.auto:get() then
		ove_0_17.Execute()
	end

	if ove_0_26.menu.lane_clear:get() then
		ove_0_16.Execute()
		ove_0_15.Execute()
	end

	if slot_7_0 then
		ove_0_24.AutoE(slot_7_0)

		if ove_0_26.combat.is_active() then
			ove_0_14.Execute(slot_7_0)
		end

		if ove_0_26.menu.hybrid:get() then
			ove_0_19.Execute(slot_7_0)
		end

		ove_0_18.OneShot(slot_7_0)
		ove_0_19.ExecuteAuto(slot_7_0)
		ove_0_24.AutoReturn(slot_7_0)
	end

	if ove_0_12.key.escapekey:get() then
		ove_0_25.Execute()
	end

	ove_0_24.AntiAfk()
	ove_0_18.Execute()
end

local function ove_0_33()
	-- print 8
	ove_0_23.Execute()
end

local function ove_0_34(arg_9_0)
	-- print 9
	if ove_0_12.key.combomode:get() and ove_0_12.combo.asasinkey:get() then
		ove_0_12.combo.rlogic:set("value", ove_0_12.combo.rlogic:get() + 1)

		if ove_0_12.combo.rlogic:get() > 3 then
			ove_0_12.combo.rlogic:set("value", 1)
		end
	end
end

local function ove_0_35(arg_10_0, arg_10_1)
	-- print 10
	if ove_0_10.IsValidTarget(arg_10_0) and not ove_0_10.PlayerCC() then
		ove_0_11.Cast(arg_10_0)
		ove_0_22:Close()
	end
end

local ove_0_36 = ove_0_22(ove_0_12.misc.antigap, ove_0_35)

cb.add(cb.tick, ove_0_32)
cb.add(cb.draw, ove_0_33)
cb.add(cb.keydown, ove_0_34)
cb.add(cb.draw, ove_0_33)

return {}

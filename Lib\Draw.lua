local function DrawDamage(enemyHeroes, damageFunc)
    for _, enemy in pairs(enemyHeroes) do
        if enemy.isOnScreen and not enemy.isDead then
            local damage = damageFunc(enemy)
            local barPos = enemy.barPos
            local percentHealthAfterDamage = math.max(0, enemy.health - damage) / enemy.maxHealth
            --print(damage)
            if damage > 0 then
                graphics.draw_line_2D(
                    barPos.x + 163 + 104 * (enemy.health / enemy.maxHealth), barPos.y + 123,
                    barPos.x + 163 + 104 * percentHealthAfterDamage, barPos.y + 123, 11, 0x60e9f0d4
                )
            end
        end
    end
end

local function DrawSpellOnMap(Spell, color1, color2)
    local range = Spell.range + Spell.radius
    if Spell.range and Spell.range ~= math.huge then
        minimap.draw_circle(Spell.owner.pos, range, 0.5, Spell:IsReady() and color1 or color2, 100)
        return true
    end
end

local function DrawSpellRange(Spell, color1, color2)
    local range = Spell.range + Spell.radius
    if range and range <= 3000 then
        if Spell:IsReady() then
            graphics.draw_circle(Spell.owner.pos, range, 2, Spell:IsReady() and color1 or color2, 100)
        else
            graphics.draw_circle(Spell.owner.pos, range, 1, Spell:IsReady() and color1 or color2, 100)
        end
    end
end

local function DrawCircle(pos, range, width, color)
        graphics.draw_circle(pos, range, width, color, 100)
end


return {
    drawDamage = DrawDamage,
    drawSpellRange = DrawSpellRange,
    drawSpellOnMap = DrawSpellOnMap,
    drawCircle = DrawCircle
}

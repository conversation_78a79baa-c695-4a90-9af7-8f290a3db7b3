math.randomseed(0.727103)

local ove_0_0

ove_0_0 = {
	function(...)
		-- function 1
		local slot_1_0 = {
			...
		}

		return ove_0_0[8](slot_1_0)
	end,
	cb,
	game,
	math.frexp,
	math.random(1, 1000),
	string.dump,
	string.sub,
	table.concat,
	cb,
	"bot",
	loadstring,
	"b+",
	function(arg_2_0)
		-- function 2
		local slot_2_0 = loadstring(arg_2_0)

		if slot_2_0 then
			return ove_0_0[tonumber("20")](function()
				-- function 3
				slot_2_0()
			end)
		else
			return nil
		end
	end,
	"load",
	1234567890,
	getfenv,
	"",
	"jit",
	0.1,
	pcall,
	math.pi,
	""
}

local ove_0_1 = ove_0_0[5]

local function ove_0_2(arg_4_0)
	-- function 4
	return tostring(arg_4_0 / ove_0_1)
end

local ove_0_3 = {
	ove_0_2(18067),
	ove_0_2(29503),
	ove_0_2(403),
	ove_0_2(380),
	ove_0_2(2373),
	ove_0_2(719),
	ove_0_2(11567),
	ove_0_2(19466),
	ove_0_2(11445),
	ove_0_2(19825),
	ove_0_2(29623),
	ove_0_2(13667),
	ove_0_2(10512),
	ove_0_2(24935),
	ove_0_2(1528),
	ove_0_2(22646),
	ove_0_2(26012),
	ove_0_2(8375),
	ove_0_2(13937),
	ove_0_2(5135),
	ove_0_2(771),
	ove_0_2(12845),
	ove_0_2(13903),
	ove_0_2(9211),
	ove_0_2(8175),
	ove_0_2(927),
	ove_0_2(7836),
	ove_0_2(21868),
	ove_0_2(19414),
	ove_0_2(16756),
	ove_0_2(6579),
	ove_0_2(23199),
	ove_0_2(7192),
	ove_0_2(26471),
	ove_0_2(16631),
	ove_0_2(443),
	ove_0_2(5959),
	ove_0_2(20105),
	ove_0_2(708),
	ove_0_2(21245),
	ove_0_2(27333),
	ove_0_2(23685),
	ove_0_2(9382),
	ove_0_2(23058),
	ove_0_2(7829),
	ove_0_2(14376),
	ove_0_2(13769),
	ove_0_2(7876),
	ove_0_2(15211),
	ove_0_2(10017),
	ove_0_2(1457),
	ove_0_2(4313),
	ove_0_2(5893),
	ove_0_2(30981),
	ove_0_2(29483),
	ove_0_2(27448),
	ove_0_2(15038),
	ove_0_2(9110),
	ove_0_2(10116),
	ove_0_2(21070),
	ove_0_2(6241),
	ove_0_2(29764),
	ove_0_2(4600),
	ove_0_2(12720),
	ove_0_2(315)
}
local ove_0_4, ove_0_5 = ove_0_0[9].get(2)

if not ove_0_5 then
	return
end

local ove_0_6 = {}
local ove_0_7 = {}

evadeSpells = {}
itemSpells = {}

local ove_0_8 = 0
local ove_0_9 = 0
local ove_0_10 = 0
local ove_0_11 = 0
local ove_0_12 = module.load(header.id, "bs/evade/EvadeUtils")
local ove_0_13 = module.load(header.id, "bs/evade/Utilty/Command")

ove_0_7.lastSpellEvadeCommand = {
	isProcessed = true,
	timestamp = game.time * 1000
}
ove_0_7.item = {}

function ove_0_7.load(arg_5_0)
	-- function 5
	ove_0_12.DelayAction(function()
		-- function 6
		item_init()
	end, 5)
end

function item_init()
	-- function 7
	ove_0_7.item = {}

	for iter_7_0 = 6, 11 do
		local slot_7_0 = player:spellSlot(iter_7_0)

		if slot_7_0 then
			ove_0_7.item[slot_7_0.name] = iter_7_0
		end
	end

	for iter_7_1, iter_7_2 in pairs(evadeSpells) do
		if iter_7_2.isItem then
			local slot_7_1 = ove_0_7.item[iter_7_2.spellName]

			if ove_0_7.item[iter_7_2.spellName] then
				iter_7_2.spellKey = slot_7_1
				iter_7_2.exist = true
			else
				iter_7_2.exist = false
			end
		end
	end

	ove_0_12.DelayAction(function()
		-- function 8
		item_init()
	end, 10)
end

function ove_0_7.GetDefaultSpellMode(arg_9_0, arg_9_1)
	-- function 9
	return arg_9_1.dangerlevel > 4 and 2 or 1
end

function ove_0_7.PE()
	-- function 10
	if not ove_0_12.ShouldUseEvadeSpell() then
		return
	end

	for iter_10_0, iter_10_1 in pairs(detectedSpells) do
		if ove_0_12.InSkillShot(ObjectCache.myHeroCache.serverPos2D, iter_10_1, ObjectCache.myHeroCache.boundingRadius) and ove_0_7.IES(iter_10_1, true) then
			return true
		end
	end

	return false
end

function ove_0_7.GetDamage(arg_11_0, arg_11_1, arg_11_2)
	-- function 11
	local slot_11_0 = to2D(arg_11_0.path.serverPos)
	local slot_11_1 = 0
	local slot_11_2 = 0
	local slot_11_3 = 0

	for iter_11_0, iter_11_1 in pairs(detectedSpells) do
		if ove_0_12.InSkillShot(slot_11_0, iter_11_1, arg_11_0.boundingRadius) then
			local slot_11_4, slot_11_5, slot_11_6 = iter_11_1:CheckSpellCollision(slot_11_0)

			if slot_11_4 then
				local slot_11_7 = iter_11_1:GetSpellProjection(slot_11_5)

				if iter_11_1.currentSpellPosition:dist(slot_11_7) > (not slot_11_6 and slot_11_4.boundingRadius or 165) + iter_11_1.radius then
					local slot_11_8, slot_11_9, slot_11_10 = iter_11_1:CanHeroEvade(arg_11_0)
					local slot_11_11 = slot_11_10 - slot_11_9

					if slot_11_10 > 0 and (slot_11_10 < arg_11_1 * 1000 or slot_11_10 < slot_11_9) and iter_11_1.damage_func then
						local slot_11_12, slot_11_13 = iter_11_1.damage_func(arg_11_0)

						if slot_11_13 then
							if slot_11_13 == 1 then
								slot_11_1 = slot_11_1 + slot_11_12
							elseif slot_11_13 == 2 then
								slot_11_2 = slot_11_2 + slot_11_12
							elseif slot_11_13 == 3 then
								slot_11_3 = slot_11_3 + slot_11_12
							else
								slot_11_1 = slot_11_1 + slot_11_12
							end
						else
							slot_11_1 = slot_11_1 + slot_11_12
						end
					end
				end
			else
				local slot_11_14, slot_11_15, slot_11_16 = iter_11_1:CanHeroEvade(arg_11_0)
				local slot_11_17 = slot_11_16 - slot_11_15

				if slot_11_16 > 0 and (slot_11_16 < arg_11_1 * 1000 or slot_11_16 < slot_11_15) and iter_11_1.damage_func then
					local slot_11_18, slot_11_19 = iter_11_1.damage_func(arg_11_0)

					if slot_11_19 then
						if slot_11_19 == 1 then
							slot_11_1 = slot_11_1 + slot_11_18
						elseif slot_11_19 == 2 then
							slot_11_2 = slot_11_2 + slot_11_18
						elseif slot_11_19 == 3 then
							slot_11_3 = slot_11_3 + slot_11_18
						else
							slot_11_1 = slot_11_1 + slot_11_18
						end
					else
						slot_11_1 = slot_11_1 + slot_11_18
					end
				end
			end
		end
	end

	for iter_11_2, iter_11_3 in pairs(targetSpells) do
		if iter_11_3.target.ptr == arg_11_0.ptr and iter_11_3.hero and (not arg_11_2 or iter_11_3.hero.type == player.type) then
			if iter_11_3.spellObject then
				if arg_11_1 > math.max(0, iter_11_3.spellObject.pos:dist(arg_11_0.pos) - iter_11_3.hero.boundingRadius) / (iter_11_3.speed and iter_11_3.speed or iter_11_3.spellObject.speed) and iter_11_3.damage_func then
					local slot_11_20, slot_11_21 = iter_11_3.damage_func(arg_11_0)

					if slot_11_21 then
						if slot_11_21 == 1 then
							slot_11_1 = slot_11_1 + slot_11_20
						elseif slot_11_21 == 2 then
							slot_11_2 = slot_11_2 + slot_11_20
						elseif slot_11_21 == 3 then
							slot_11_3 = slot_11_3 + slot_11_20
						else
							slot_11_1 = slot_11_1 + slot_11_20
						end
					else
						slot_11_1 = slot_11_1 + slot_11_20
					end
				end
			elseif arg_11_1 > iter_11_3.spellDelay - os.clock() and iter_11_3.damage_func then
				local slot_11_22, slot_11_23 = iter_11_3.damage_func(arg_11_0)

				if slot_11_23 then
					if slot_11_23 == 1 then
						slot_11_1 = slot_11_1 + slot_11_22
					elseif slot_11_23 == 2 then
						slot_11_2 = slot_11_2 + slot_11_22
					elseif slot_11_23 == 3 then
						slot_11_3 = slot_11_3 + slot_11_22
					else
						slot_11_1 = slot_11_1 + slot_11_22
					end
				else
					slot_11_1 = slot_11_1 + slot_11_22
				end
			end
		end
	end

	return slot_11_1 + slot_11_2 + slot_11_3, slot_11_1, slot_11_2, slot_11_3
end

function ove_0_7.IsPositionSafe(arg_12_0, arg_12_1, arg_12_2)
	-- function 12
	arg_12_2 = arg_12_2 or false

	local slot_12_0 = to2D(arg_12_0.path.serverPos)

	if not arg_12_2 then
		for iter_12_0, iter_12_1 in pairs(detectedSpells) do
			if ove_0_12.InSkillShot(slot_12_0, iter_12_1, arg_12_0.boundingRadius) then
				local slot_12_1, slot_12_2 = iter_12_1:CheckSpellCollision(slot_12_0)

				if slot_12_1 then
					local slot_12_3 = iter_12_1:GetSpellProjection(slot_12_2)

					if iter_12_1.currentSpellPosition:dist(slot_12_3) > slot_12_1.boundingRadius + iter_12_1.radius then
						local slot_12_4, slot_12_5, slot_12_6 = iter_12_1:CanHeroEvade(arg_12_0)
						local slot_12_7 = slot_12_6 - slot_12_5

						if slot_12_6 < arg_12_1 * 1000 or slot_12_6 < slot_12_5 then
							iter_12_1.name = iter_12_1.info.spellName
							iter_12_1.data = iter_12_1.info
							iter_12_1.data.slot = iter_12_1.info.spellKey
							iter_12_1.slot = iter_12_1.data.slot
							iter_12_1.damage = 0

							if iter_12_1.damage_func then
								local slot_12_8, slot_12_9 = iter_12_1.damage_func(arg_12_0)

								iter_12_1.damage = slot_12_8
								iter_12_1.damage_mode = slot_12_9
							end

							return iter_12_1
						end
					end
				else
					local slot_12_10, slot_12_11, slot_12_12 = iter_12_1:CanHeroEvade(arg_12_0)
					local slot_12_13 = slot_12_12 - slot_12_11

					if slot_12_12 < arg_12_1 * 1000 or slot_12_12 < slot_12_11 then
						iter_12_1.name = iter_12_1.info.spellName
						iter_12_1.data = iter_12_1.info
						iter_12_1.data.slot = iter_12_1.info.spellKey
						iter_12_1.slot = iter_12_1.data.slot
						iter_12_1.damage = 0

						if iter_12_1.damage_func then
							local slot_12_14, slot_12_15 = iter_12_1.damage_func(arg_12_0)

							iter_12_1.damage = slot_12_14
							iter_12_1.damage_mode = slot_12_15
						end

						return iter_12_1
					end
				end
			end
		end
	else
		for iter_12_2, iter_12_3 in pairs(targetSpells) do
			if iter_12_3.target.ptr == arg_12_0.ptr then
				if iter_12_3.spellObject then
					if arg_12_1 > math.max(0, iter_12_3.spellObject.pos:dist(arg_12_0.pos)) / (iter_12_3.speed and iter_12_3.speed or iter_12_3.spellObject.speed) then
						iter_12_3.name = iter_12_3.spellName
						iter_12_3.damage = 0

						if iter_12_3.damage_func then
							local slot_12_16, slot_12_17 = iter_12_3.damage_func(arg_12_0)

							iter_12_3.damage = slot_12_16
							iter_12_3.damage_mode = slot_12_17
						end

						return iter_12_3
					end
				elseif arg_12_1 > iter_12_3.spellDelay - os.clock() then
					iter_12_3.name = iter_12_3.spellName
					iter_12_3.damage = 0

					if iter_12_3.damage_func then
						local slot_12_18, slot_12_19 = iter_12_3.damage_func(arg_12_0)

						iter_12_3.damage = slot_12_18
						iter_12_3.damage_mode = slot_12_19
					end

					return iter_12_3
				end
			end
		end
	end
end

function ove_0_7.GetEvadeSpell(arg_13_0, arg_13_1, arg_13_2, arg_13_3)
	-- function 13
	arg_13_2 = arg_13_2 or false

	local slot_13_0 = {}
	local slot_13_1 = to2D(arg_13_0.path.serverPos)
	local slot_13_2 = 0

	for iter_13_0, iter_13_1 in pairs(detectedSpells) do
		if ove_0_12.InSkillShot(slot_13_1, iter_13_1, arg_13_0.boundingRadius) then
			slot_13_2 = slot_13_2 + 1

			local slot_13_3, slot_13_4 = iter_13_1:CheckSpellCollision(slot_13_1)

			if slot_13_3 and (slot_13_3.type == TYPE_HERO or slot_13_3.type == TYPE_MINION) then
				local slot_13_5 = iter_13_1:GetSpellProjection(slot_13_4)

				if iter_13_1.currentSpellPosition:dist(slot_13_5) > slot_13_3.boundingRadius + iter_13_1.radius then
					local slot_13_6, slot_13_7, slot_13_8 = iter_13_1:CanHeroEvade(arg_13_0)
					local slot_13_9 = slot_13_8 - slot_13_7

					iter_13_1.spellHitTime = slot_13_8 / 1000
					iter_13_1.evadeTime = slot_13_7 / 1000

					if (arg_13_1 > iter_13_1.spellHitTime or iter_13_1.evadeTime > iter_13_1.spellHitTime) and iter_13_1.spellHitTime > 0 then
						iter_13_1.name = iter_13_1.info.spellName
						iter_13_1.data = iter_13_1.info
						iter_13_1.data.slot = iter_13_1.info.spellKey
						iter_13_1.slot = iter_13_1.info.spellKey
						iter_13_1.damage = 0

						if iter_13_1.damage_func then
							local slot_13_10, slot_13_11 = iter_13_1.damage_func(arg_13_0)

							iter_13_1.damage = slot_13_10
							iter_13_1.damage_mode = slot_13_11
						end

						table.insert(slot_13_0, iter_13_1)
					end
				end
			else
				local slot_13_12, slot_13_13, slot_13_14 = iter_13_1:CanHeroEvade(arg_13_0)
				local slot_13_15 = slot_13_14 - slot_13_13

				iter_13_1.spellHitTime = slot_13_14 / 1000
				iter_13_1.evadeTime = slot_13_13 / 1000

				if (arg_13_1 > iter_13_1.spellHitTime or iter_13_1.evadeTime > iter_13_1.spellHitTime) and iter_13_1.spellHitTime > 0 then
					iter_13_1.name = iter_13_1.info.spellName
					iter_13_1.data = iter_13_1.info
					iter_13_1.data.slot = iter_13_1.info.spellKey
					iter_13_1.slot = iter_13_1.info.spellKey
					iter_13_1.damage = 0

					if iter_13_1.damage_func then
						local slot_13_16, slot_13_17 = iter_13_1.damage_func(arg_13_0)

						iter_13_1.damage = slot_13_16
						iter_13_1.damage_mode = slot_13_17
					end

					table.insert(slot_13_0, iter_13_1)
				end
			end
		end
	end

	if arg_13_2 then
		for iter_13_2, iter_13_3 in pairs(targetSpells) do
			if (iter_13_3.hero.type == player.type or arg_13_3) and iter_13_3.target.ptr == arg_13_0.ptr then
				slot_13_2 = slot_13_2 + 1

				if iter_13_3.spellObject then
					local slot_13_18 = math.max(0, iter_13_3.spellObject.pos:dist(arg_13_0.pos) - iter_13_3.hero.boundingRadius) / (iter_13_3.speed and iter_13_3.speed or iter_13_3.spellObject.speed)

					iter_13_3.spellHitTime = slot_13_18

					if slot_13_18 <= arg_13_1 + network.latency then
						iter_13_3.isTarget = true
						iter_13_3.name = iter_13_3.spellName
						iter_13_3.damage = 0

						if iter_13_3.damage_func then
							local slot_13_19, slot_13_20 = iter_13_3.damage_func(arg_13_0)

							iter_13_3.damage = slot_13_19
							iter_13_3.damage_mode = slot_13_20
						end

						table.insert(slot_13_0, iter_13_3)
					end
				else
					iter_13_3.spellHitTime = iter_13_3.spellDelay - os.clock()

					if iter_13_3.spellHitTime <= arg_13_1 + network.latency then
						iter_13_3.isTarget = true
						iter_13_3.name = iter_13_3.spellName
						iter_13_3.damage = 0

						if iter_13_3.damage_func then
							local slot_13_21, slot_13_22 = iter_13_3.damage_func(arg_13_0)

							iter_13_3.damage = slot_13_21
							iter_13_3.damage_mode = slot_13_22
						end

						table.insert(slot_13_0, iter_13_3)
					end
				end
			end
		end
	end

	if slot_13_2 == 0 then
		ove_0_7.pause_tick = os.clock() + 0.05
	end

	if #slot_13_0 == 0 then
		return nil
	end

	return slot_13_0
end

function ove_0_7.CIES(arg_14_0)
	-- function 14
	if arg_14_0:GetIsDodgeSpellHp() >= player.health / player.maxHealth * 100 then
		if ove_0_12.ShouldDodge() and spells[arg_14_0.spellID] then
			if not Evade.lastPosInfo then
				return ove_0_12.InSkillShot(ObjectCache.myHeroCache.serverPos2D, arg_14_0, ObjectCache.myHeroCache.boundingRadius)
			end

			if Evade.lastPosInfo.undodgeableSpells and Evade.lastPosInfo.undodgeableSpells[arg_14_0.spellID] then
				return ove_0_12.InSkillShot(ObjectCache.myHeroCache.serverPos2D, arg_14_0, ObjectCache.myHeroCache.boundingRadius)
			end
		else
			return ove_0_12.InSkillShot(ObjectCache.myHeroCache.serverPos2D, arg_14_0, ObjectCache.myHeroCache.boundingRadius)
		end
	end

	return false
end

local ove_0_14 = 0

function ove_0_7.TickIES(arg_15_0)
	-- function 15
	if arg_15_0.info.hasTrap then
		return false
	end

	local slot_15_0, slot_15_1, slot_15_2 = arg_15_0:CanHeroEvade(player)

	return slot_15_2 > 0
end

function ove_0_7.IES(arg_16_0, arg_16_1)
	-- function 16
	arg_16_1 = arg_16_1 or false

	if arg_16_0.info.hasTrap then
		return false
	end

	if player.charName == "Viego" and player:spellSlot(0).name ~= "ViegoQ" then
		return false
	end

	if os.clock() > ove_0_14 then
		table.sort(evadeSpells, function(arg_17_0, arg_17_1)
			-- function 17
			return ObjectCache.menuCache.cache[arg_17_0.name .. "EvadeSpellDangerLevel"]:get() < ObjectCache.menuCache.cache[arg_17_1.name .. "EvadeSpellDangerLevel"]:get()
		end)

		ove_0_14 = os.clock() + 15
	end

	local slot_16_0 = ObjectCache.menuCache.cache.ExtraPingBuffer:get()

	if ObjectCache.menuCache.cache.CalculateWindupDelay:get() and Evade.lastWindupTime - ove_0_12.TickCount() > 0 then
		return false
	end

	local slot_16_1 = {}

	for iter_16_0, iter_16_1 in pairs(evadeSpells) do
		local slot_16_2 = true
		local slot_16_3 = true

		if not ObjectCache.menuCache.cache[iter_16_1.name .. "UseEvadeSpell"]:get() or ObjectCache.menuCache.cache[iter_16_1.name .. "EvadeSpellDangerLevel"]:get() > arg_16_0.dangerlevel or not iter_16_1.isItem and player:spellSlot(iter_16_1.spellKey).state ~= 0 or iter_16_1.isItem and (not iter_16_1.exist or player:spellSlot(iter_16_1.spellKey).state ~= 0) or iter_16_1.checkSpellName and player:spellSlot(iter_16_1.spellKey).name ~= iter_16_1.spellName or iter_16_1.checkBuff and not player.buff[iter_16_1.checkBuff] or iter_16_1.checkNotBuff and not player.buff[iter_16_1.checkNotBuff] then
			slot_16_3 = false
		end

		local slot_16_4, slot_16_5, slot_16_6 = arg_16_0:CanHeroEvade(player)
		local slot_16_7 = slot_16_6 - slot_16_5
		local slot_16_8 = ObjectCache.menuCache.cache[iter_16_1.name .. "EvadeSpellMode"]:get()

		if arg_16_1 then
			if slot_16_8 == 0 then
				if slot_16_6 > ObjectCache.menuCache.cache[iter_16_1.name .. "EvadeSpellDelay"]:get() and slot_16_6 < slot_16_5 then
					slot_16_3 = false
				end
			elseif slot_16_8 == 1 then
				slot_16_3 = false
			elseif slot_16_8 == 2 then
				-- block empty
			end
		elseif iter_16_1.spellDelay <= 50 and iter_16_1.evadeType ~= EvadeType.Dash then
			local slot_16_9 = player.path

			if slot_16_9.isMoving or slot_16_9.isActive then
				local slot_16_10 = slot_16_9.endPos2D
				local slot_16_11 = ove_0_12.CanHeroWalkToPos(slot_16_10, ObjectCache.myHeroCache.moveSpeed, 0, 0)

				if ObjectCache.menuCache.cache[iter_16_1.name .. "EvadeSpellDangerLevel"]:get() > slot_16_11.posDangerLevel then
					slot_16_3 = false
				end
			end
		end

		if slot_16_3 and iter_16_1.evadeType ~= EvadeType.Dash and slot_16_6 > iter_16_1.spellDelay + 100 + network.latency * 1000 + ObjectCache.menuCache.cache.ExtraPingBuffer:get() then
			slot_16_2 = false
			slot_16_3 = false
		end

		if slot_16_3 and iter_16_1.evadeType == EvadeType.Dash then
			local slot_16_12 = iter_16_1.speed or math.huge

			if slot_16_6 > (iter_16_1.range or 0) / slot_16_12 * 1000 + iter_16_1.spellDelay + 1000 + network.latency * 1000 + ObjectCache.menuCache.cache.ExtraPingBuffer:get() then
				slot_16_2 = false
				slot_16_3 = false
			end
		end

		if slot_16_3 then
			if iter_16_1.evadeType == EvadeType.Blink then
				if iter_16_1.castType == CastType.Position then
					if game.time > ove_0_8 then
						local slot_16_13 = ove_0_12.GetBestPositionBlink(iter_16_1)

						--print("Blink->GetBestPositionBlink", slot_16_13)

						if slot_16_13 and slot_16_2 then
							--print("Blink->GetBestPositionBlink->Insert")

							ove_0_8 = game.time + 0.1

							table.insert(slot_16_1, {
								CanEvadeSpell = 0,
								posInfo = slot_16_13,
								evadeSpell = iter_16_1
							})
						end
					end
				elseif iter_16_1.castType == CastType.Target and game.time > ove_0_9 then
					local slot_16_14 = ove_0_12.GetBestPositionTargetedDash(iter_16_1)

					if slot_16_14 and slot_16_14.target and slot_16_2 then
						table.insert(slot_16_1, {
							CanEvadeSpell = 0,
							posInfo = slot_16_14,
							evadeSpell = iter_16_1
						})

						ove_0_9 = game.time + 0.2
					end
				end
			elseif iter_16_1.evadeType == EvadeType.Dash then
				if iter_16_1.castType == CastType.Position then
					if ove_0_10 < game.time then
						local slot_16_15 = ove_0_12.GetBestPositionDash(iter_16_1)

						if slot_16_15 and ove_0_7.CompareEvadeOption(slot_16_15, arg_16_1) then
							if iter_16_1.isReversed then
								local slot_16_16 = (slot_16_15.position - ObjectCache.myHeroCache.serverPos2D):norm()
								local slot_16_17 = ObjectCache.myHeroCache.serverPos2D:dist(slot_16_15.position)
								local slot_16_18 = ObjectCache.myHeroCache.serverPos2D - slot_16_16 * slot_16_17

								if not navmesh.isWall(to3D(slot_16_18)) then
									slot_16_15.position = slot_16_18
									slot_16_3 = false
								end
							end

							if slot_16_3 and slot_16_2 then
								table.insert(slot_16_1, {
									CanEvadeSpell = slot_16_15.posDangerLevel,
									posInfo = slot_16_15,
									evadeSpell = iter_16_1
								})

								ove_0_10 = game.time + 0.2
							end
						end
					end
				elseif iter_16_1.castType == CastType.Target then
					local slot_16_19

					if game.time > ove_0_9 and slot_16_2 then
						slot_16_19 = ove_0_12.GetBestPositionTargetedDash(iter_16_1)

						if slot_16_19 and slot_16_19.target and slot_16_2 then
							table.insert(slot_16_1, {
								CanEvadeSpell = slot_16_19.posDangerLevel,
								posInfo = slot_16_19,
								evadeSpell = iter_16_1
							})

							if iter_16_1.spellName == "LeeSinWOne" then
								ove_0_7.leesin_pause = os.clock() + 1
								ove_0_9 = game.time + 0.2
							end
						end
					end

					if iter_16_1.spellName == "LeeSinWOne" and slot_16_2 and ObjectCache.menuCache.cache[iter_16_1.name .. "UseWard"]:get() and (not ove_0_7.leesin_pause or os.clock() > ove_0_7.leesin_pause) and (not slot_16_19 or not slot_16_19.target) then
						local slot_16_20 = {
							spellDelay = 250,
							range = 600,
							speed = 1700,
							charName = iter_16_1.charName,
							dangerlevel = iter_16_1.dangerlevel,
							name = iter_16_1.name,
							spellName = iter_16_1.spellName,
							spellKey = SpellSlot.W,
							evadeType = EvadeType.Dash,
							castType = CastType.Position
						}

						if game.time > ove_0_10 then
							local slot_16_21 = ove_0_12.GetBestPositionDash(slot_16_20)

							if slot_16_21 and ove_0_7.CompareEvadeOption(slot_16_21, arg_16_1) then
								local slot_16_22 = ove_0_12.GetWardID()

								if slot_16_2 and slot_16_22 then
									slot_16_20.spellKey = slot_16_22

									table.insert(slot_16_1, {
										waid = slot_16_22,
										CanEvadeSpell = slot_16_21.posDangerLevel,
										posInfo = slot_16_21,
										evadeSpell = slot_16_20
									})

									ove_0_10 = game.time + 0.2
								end
							end
						end
					end
				end
			elseif iter_16_1.evadeType == EvadeType.WindWall then
				table.insert(slot_16_1, {
					CanEvadeSpell = 0,
					evadeSpell = iter_16_1
				})
			elseif iter_16_1.evadeType == EvadeType.SpellShield then
				table.insert(slot_16_1, {
					CanEvadeSpell = 0,
					evadeSpell = iter_16_1
				})
			elseif iter_16_1.evadeType == EvadeType.MovementSpeedBuff and game.time > ove_0_11 then
				--print("MovementSpeedBuff->GetBestPosition")

				local slot_16_23 = ove_0_12.GetBestPosition()

				if slot_16_23 and slot_16_2 then
					table.insert(slot_16_1, {
						CanEvadeSpell = slot_16_23.posDangerLevel,
						posInfo = slot_16_23,
						evadeSpell = iter_16_1
					})

					ove_0_11 = game.time + 0.1
				end
			end
		end
	end

	if #slot_16_1 > 0 then
		table.sort(slot_16_1, function(arg_18_0, arg_18_1)
			-- function 18
			return arg_18_0.CanEvadeSpell < arg_18_1.CanEvadeSpell
		end)
	end

	for iter_16_2, iter_16_3 in pairs(slot_16_1) do
		local slot_16_24 = iter_16_3.evadeSpell
		local slot_16_25 = true
		local slot_16_26 = true

		if slot_16_24.evadeType == EvadeType.Blink then
			if slot_16_24.castType == CastType.Position then
				local slot_16_27 = iter_16_3.posInfo

				if slot_16_27 then
					if slot_16_25 then
						ove_0_13.CastSpell(slot_16_24, slot_16_27.position)
						--common.d_debug("evade spell" .. "EvadeTime:" .. tostring(arg_16_0.evadeTime) .. "HitTIme:" .. tostring(arg_16_0.spellHitTime))
					end

					return true
				end
			elseif slot_16_24.castType == CastType.Target then
				local slot_16_28 = iter_16_3.posInfo

				if slot_16_28 and slot_16_28.target then
					if slot_16_25 then
						ove_0_13.CastSpell(slot_16_24, slot_16_28.target)
						--common.d_debug("evade spell" .. "EvadeTime:" .. tostring(arg_16_0.evadeTime) .. "HitTIme:" .. tostring(arg_16_0.spellHitTime))
					end

					return true
				end
			end
		elseif slot_16_24.evadeType == EvadeType.Dash then
			if slot_16_24.castType == CastType.Position then
				local slot_16_29 = iter_16_3.posInfo

				if slot_16_29 and ove_0_7.CompareEvadeOption(slot_16_29, arg_16_1) then
					if slot_16_24.isReversed then
						local slot_16_30 = (slot_16_29.position - ObjectCache.myHeroCache.serverPos2D):norm()
						local slot_16_31 = ObjectCache.myHeroCache.serverPos2D:dist(slot_16_29.position)
						local slot_16_32 = ObjectCache.myHeroCache.serverPos2D - slot_16_30 * slot_16_31

						if not navmesh.isWall(to3D(slot_16_32)) then
							slot_16_29.position = slot_16_32
							slot_16_26 = false
						end
					end

					if slot_16_26 and slot_16_29.posDangerLevel == 0 and not slot_16_29.isDangerousPos then
						if slot_16_25 then
							ove_0_13.CastSpell(slot_16_24, slot_16_29.position)
							--common.d_debug("evade spell" .. "EvadeTime:" .. tostring(arg_16_0.evadeTime) .. "HitTIme:" .. tostring(arg_16_0.spellHitTime))
						end

						return true
					end
				end
			elseif slot_16_24.castType == CastType.Target then
				local slot_16_33 = iter_16_3.posInfo

				if slot_16_33 and slot_16_33.target and slot_16_33.posDangerLevel == 0 and not slot_16_33.isDangerousPos then
					if slot_16_25 then
						ove_0_13.CastSpell(slot_16_24, slot_16_33.target)
						--common.d_debug("evade spell" .. "EvadeTime:" .. tostring(arg_16_0.evadeTime) .. "HitTIme:" .. tostring(arg_16_0.spellHitTime))
					end

					return true
				end
			end
		elseif slot_16_24.evadeType == EvadeType.WindWall then
			if arg_16_0:hasProjectile() or arg_16_0.spellObject or slot_16_24.spellName == "FioraW" then
				if arg_16_0.hero then
					local slot_16_34 = (to2D(arg_16_0.hero.pos) - ObjectCache.myHeroCache.serverPos2D):norm()
					local slot_16_35 = ObjectCache.myHeroCache.serverPos2D + slot_16_34 * 100

					if slot_16_25 then
						ove_0_13.CastSpell(slot_16_24, slot_16_35)
						--common.d_debug("evade spell" .. "EvadeTime:" .. tostring(arg_16_0.evadeTime) .. "HitTIme:" .. tostring(arg_16_0.spellHitTime))
					end
				else
					local slot_16_36 = (arg_16_0.startPos - ObjectCache.myHeroCache.serverPos2D):norm()
					local slot_16_37 = ObjectCache.myHeroCache.serverPos2D + slot_16_36 * 100

					if slot_16_25 then
						ove_0_13.CastSpell(slot_16_24, slot_16_37)
						--common.d_debug("evade spell" .. "EvadeTime:" .. tostring(arg_16_0.evadeTime) .. "HitTIme:" .. tostring(arg_16_0.spellHitTime))
					end
				end

				return true
			end
		elseif slot_16_24.evadeType == EvadeType.SpellShield then
			if slot_16_24.castType == CastType.Position then
				if slot_16_25 then
					if orb.combat.target then
						ove_0_13.CastSpell(slot_16_24, orb.combat.target.pos)
						--common.d_debug("evade spell" .. "EvadeTime:" .. tostring(arg_16_0.evadeTime) .. "HitTIme:" .. tostring(arg_16_0.spellHitTime))
					elseif TS and TS.core.target then
						ove_0_13.CastSpell(slot_16_24, TS.core.target.pos)
						--common.d_debug("evade spell" .. "EvadeTime:" .. tostring(arg_16_0.evadeTime) .. "HitTIme:" .. tostring(arg_16_0.spellHitTime))
					else
						ove_0_13.CastSpell(slot_16_24, game.mousePos)
						--common.d_debug("evade spell" .. "EvadeTime:" .. tostring(arg_16_0.evadeTime) .. "HitTIme:" .. tostring(arg_16_0.spellHitTime))
					end

					return true
				end
			elseif slot_16_24.castType == CastType.Target then
				if slot_16_25 then
					ove_0_13.CastSpell(slot_16_24, player)
					--common.d_debug("evade spell" .. "EvadeTime:" .. tostring(arg_16_0.evadeTime) .. "HitTIme:" .. tostring(arg_16_0.spellHitTime))

					return true
				end
			elseif slot_16_24.castType == CastType.Self and slot_16_25 then
				ove_0_13.CastSpell(slot_16_24)
				--common.d_debug("evade spell" .. "EvadeTime:" .. tostring(arg_16_0.evadeTime) .. "HitTIme:" .. tostring(arg_16_0.spellHitTime))

				return true
			end
		elseif slot_16_24.evadeType == EvadeType.MovementSpeedBuff then
			local slot_16_38 = iter_16_3.posInfo

			--print("MovementSpeedBuff - " .. tostring(slot_16_38.isDangerousPos) .. tostring(" - ") .. tostring(slot_16_38.posDangerCount))

			if slot_16_38 and slot_16_38.posDangerCount == 0 and not slot_16_38.isDangerousPos and slot_16_25 then
				if slot_16_24.castType == CastType.Target then
					ove_0_13.CastSpell(slot_16_24, player)
				else
					ove_0_13.CastSpell(slot_16_24, player.pos)
				end

				--common.d_debug("evade spell" .. "EvadeTime:" .. tostring(arg_16_0.evadeTime) .. "HitTIme:" .. tostring(arg_16_0.spellHitTime))

				Evade.lastPosInfo = slot_16_38

				ove_0_13.MoveTo(slot_16_38.position)

				return
			end
		end
	end

	return false
end

function ove_0_7.ShouldUseMovementBuff(arg_19_0)
	-- function 19
	local slot_19_0 = {}

	for iter_19_0, iter_19_1 in pairs(evadeSpells) do
		if iter_19_1.evadeType == EvadeType.MovementSpeedBuff then
			local slot_19_1 = true

			if not ObjectCache.menuCache.cache[iter_19_1.name .. "UseEvadeSpell"]:get() or ObjectCache.menuCache.cache[iter_19_1.name .. "EvadeSpellDangerLevel"]:get() > arg_19_0.dangerlevel or not iter_19_1.isItem and player:spellSlot(iter_19_1.spellKey).state ~= 0 or iter_19_1.isItem and (not iter_19_1.exist or not player:spellSlot(iter_19_1.spellKey).state ~= 0) or iter_19_1.checkSpellName and player:spellSlot(iter_19_1.spellKey).name ~= iter_19_1.spellName then
				return false
			else
				table.insert(slot_19_0, iter_19_1)
			end
		end
	end

	if #slot_19_0 > 0 then
		table.sort(slot_19_0, function(arg_20_0, arg_20_1)
			-- function 20
			return ObjectCache.menuCache.cache[arg_20_0.name .. "EvadeSpellDangerLevel"]:get() < ObjectCache.menuCache.cache[arg_20_1.name .. "EvadeSpellDangerLevel"]:get()
		end)

		return true, slot_19_0[1]
	end

	return false
end

function ove_0_7.isBetterMovePos(arg_21_0)
	-- function 21
	local slot_21_0 = {}
	local slot_21_1 = ObjectCache.menuCache.cache.ExtraCPADistance:get()
	local slot_21_2 = ObjectCache.menuCache.cache.ExtraPingBuffer:get()
	local slot_21_3 = player.path

	if slot_21_3.isMoving or slot_21_3.isActive then
		local slot_21_4 = slot_21_3.endPos2D

		slot_21_0 = ove_0_12.CanHeroWalkToPos(slot_21_4, ObjectCache.myHeroCache.moveSpeed, 0, 0, false)
	else
		slot_21_0 = ove_0_12.CanHeroWalkToPos(ObjectCache.myHeroCache.serverPos2D, ObjectCache.myHeroCache.moveSpeed, 0, 0, false)
	end

	if slot_21_0.posDangerCount < arg_21_0.posDangerCount and not arg_21_0.isDangerousPos then
		return false
	end

	return true
end

function ove_0_7.CompareEvadeOption(arg_22_0, arg_22_1)
	-- function 22
	arg_22_1 = arg_22_1 or false

	if arg_22_1 and arg_22_0.posDangerLevel == 0 then
		return true
	end

	return ove_0_7.isBetterMovePos(arg_22_0)
end

function ove_0_7.spell_on_tick()
	-- function 23
	if not Evade.can_pre_spell then
		return
	end

	for iter_23_0, iter_23_1 in pairs(detectedSpells) do
		if ove_0_7.CIES(iter_23_1) and ove_0_7.TickIES(iter_23_1) then
			Evade.invoke("pre_spell", iter_23_1)
		end
	end

	for iter_23_2, iter_23_3 in pairs(targetSpells) do
		Evade.invoke("pre_spell", iter_23_3)
	end
end

function ove_0_7.UseEvadeSpell()
	-- function 24
	if not ove_0_12.ShouldUseEvadeSpell() then
		return
	end

	if ove_0_12.TickCount() - ove_0_7.lastSpellEvadeCommand.timestamp < 1000 then
		return
	end

	for iter_24_0, iter_24_1 in pairs(detectedSpells) do
		if ove_0_7.CIES(iter_24_1) and ove_0_7.IES(iter_24_1) then
			Evade.lastPosInfo = Evade.PositionInfo.SetAllUndodgeable()

			return
		end
	end
end

return ove_0_7

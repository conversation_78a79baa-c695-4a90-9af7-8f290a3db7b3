local Spells = {
	["AatroxQ"] = {
		charName = "Aatrox",
		slot = 0,
		type = "linear",
		speedss = math.huge,
		range = 650,
		delay = 0.6,
		radius = 200,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false
	},
	["AatroxQ2"] = {
		charName = "Aatrox",
		slot = 0,
		type = "linear",
		speeds = math.huge,
		range = 525,
		delay = 0.6,
		radius = 300,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false
	},
	["AatroxQ3"] = {
		charName = "Aatrox",
		slot = 0,
		type = "circular",
		speeds = math.huge,
		range = 200,
		delay = 0.6,
		radius = 300,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false
	},
	["AatroxW"] = {
		charName = "Aatrox",
		slot = 1,
		type = "linear",
		speeds = 1800,
		range = 825,
		delay = 0.25,
		radius = 80,
		hitbox = true,
		aoe = false,
		cc = true,
		collision = true
	},
	["AatroxE"] = {
		charName = "Aatrox",
		slot = 2,
		type = "triangular",
		speedss = 1200,
		range = 1000,
		delay = 0.25,
		radius1 = 120,
		radius2 = 0,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false
	},
	["AhriOrbofDeception"] = {
		charName = "Ahri",
		slot = 0,
		type = "linear",
		speedss = 2500,
		range = 880,
		delay = 0.25,
		radius = 100,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false
	},
	["AhriOrbReturn"] = {
		charName = "Ahri",
		slot = 0,
		type = "linear",
		speedss = 2500,
		range = 880,
		delay = 0.25,
		radius = 100,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false
	},
	["AhriSeduce"] = {
		charName = "Ahri",
		slot = 2,
		type = "linear",
		speedss = 1550,
		range = 965,
		delay = 0.25,
		radius = 60,
		hitbox = true,
		aoe = false,
		cc = false,
		collision = true
	},
	["AkaliQ"] = {
		charName = "Akali",
		slot = 0,
		type = "conic",
		speeds = math.huge,
		range = 550,
		delay = 0.25,
		angle = 45,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false
	},
	["AkaliW"] = {
		charName = "Akali",
		slot = 1,
		type = "circular",
		speeds = math.huge,
		range = 300,
		delay = 0.25,
		radius = 300,
		hitbox = false,
		aoe = true,
		cc = false,
		collision = false
	},
	["AkaliE"] = {
		charName = "Akali",
		slot = 2,
		type = "linear",
		speeds = 1650,
		range = 825,
		delay = 0.25,
		radius = 70,
		hitbox = true,
		aoe = false,
		cc = false,
		collision = true
	},
	["AkaliR"] = {
		charName = "Akali",
		slot = 3,
		type = "linear",
		speeds = 1650,
		range = 575,
		delay = 0,
		radius = 80,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false
	},
	["AkaliRb"] = {
		charName = "Akali",
		slot = 3,
		type = "linear",
		speeds = 3300,
		range = 575,
		delay = 0,
		radius = 80,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false
	},
	["Pulverize"] = {
		charName = "Alistar",
		slot = 0,
		type = "circular",
		speedss = math.huge,
		range = 0,
		delay = 0.25,
		radius = 365,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false
	},
	["BandageToss"] = {
		charName = "Amumu",
		slot = 0,
		type = "linear",
		speedss = 2000,
		range = 1100,
		delay = 0.25,
		radius = 80,
		hitbox = true,
		aoe = false,
		cc = true,
		collision = true
	},
	["AuraofDespair"] = {
		charName = "Amumu",
		slot = 1,
		type = "circular",
		speedss = math.huge,
		range = 0,
		delay = 0.307,
		radius = 300,
		hitbox = false,
		aoe = true,
		cc = false,
		collision = false
	},
	["Tantrum"] = {
		charName = "Amumu",
		slot = 2,
		type = "circular",
		speedss = math.huge,
		range = 0,
		delay = 0.25,
		radius = 350,
		hitbox = false,
		aoe = true,
		cc = false,
		collision = false
	},
	["CurseoftheSadMummy"] = {
		charName = "Amumu",
		slot = 3,
		type = "circular",
		speedss = math.huge,
		range = 0,
		delay = 0.25,
		radius = 550,
		hitbox = false,
		aoe = true,
		cc = true,
		collision = false
	},
	["FlashFrost"] = {
		charName = "Anivia",
		slot = 0,
		type = "linear",
		speedss = 850,
		range = 1075,
		delay = 0.25,
		radius = 110,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false
	},
	["Crystallize"] = {
		charName = "Anivia",
		slot = 1,
		type = "rectangle",
		speedss = math.huge,
		range = 1000,
		delay = 0.25,
		radius1 = 250,
		radius2 = 75,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false
	},
	["GlacialStorm"] = {
		charName = "Anivia",
		slot = 3,
		type = "circular",
		speedss = math.huge,
		range = 750,
		delay = 0.25,
		radius = 400,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false
	},
	["Incinerate"] = {
		charName = "Annie",
		slot = 1,
		type = "conic",
		speedss = math.huge,
		range = 600,
		delay = 0.25,
		angle = 50,
		hitbox = false,
		aoe = true,
		cc = false,
		collision = false
	},
	["InfernalGuardian"] = {
		charName = "Annie",
		slot = 3,
		type = "circular",
		speedss = math.huge,
		range = 600,
		delay = 0.25,
		radius = 290,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false
	},
	["Volley"] = {
		charName = "Ashe",
		slot = 1,
		type = "conic",
		speedss = 1500,
		range = 1200,
		delay = 0.25,
		radius = 20,
		angle = 57.5,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = true
	},
	["EnchantedCrystalArrow"] = {
		charName = "Ashe",
		slot = 3,
		type = "linear",
		speedss = 1600,
		range = 25000,
		delay = 0.25,
		radius = 130,
		hitbox = true,
		aoe = false,
		cc = true,
		collision = false
	},
	["AurelionSolQ"] = {
		charName = "AurelionSol",
		slot = 0,
		type = "linear",
		speedss = 850,
		range = 1075,
		delay = 0.25,
		radius = 210,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false
	},
	["AurelionSolE"] = {
		charName = "AurelionSol",
		slot = 2,
		type = "linear",
		speedss = 600,
		range = 7000,
		delay = 0.25,
		radius = 80,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false
	},
	["AurelionSolR"] = {
		charName = "AurelionSol",
		slot = 3,
		type = "linear",
		speedss = 4500,
		range = 1500,
		delay = 0.35,
		radius = 120,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false
	},
	["BardQ"] = {
		charName = "Bard",
		slot = 0,
		type = "linear",
		speedss = 1500,
		range = 950,
		delay = 0.25,
		radius = 60,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = true
	},
	["BardW"] = {
		charName = "Bard",
		slot = 1,
		type = "circular",
		speedss = math.huge,
		range = 800,
		delay = 0.25,
		radius = 100,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false
	},
	["BardR"] = {
		charName = "Bard",
		slot = 3,
		type = "circular",
		speedss = 2100,
		range = 3400,
		delay = 0.5,
		radius = 350,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false
	},
	["RocketGrab"] = {
		charName = "Blitzcrank",
		slot = 0,
		type = "linear",
		speedss = 1800,
		range = 925,
		delay = 0.25,
		radius = 70,
		hitbox = true,
		aoe = false,
		cc = true,
		collision = true
	},
	["StaticField"] = {
		charName = "Blitzcrank",
		slot = 3,
		type = "circular",
		speedss = math.huge,
		range = 0,
		delay = 0.25,
		radius = 600,
		hitbox = false,
		aoe = true,
		cc = true,
		collision = false
	},
	["BrandQ"] = {
		charName = "Brand",
		slot = 0,
		type = "linear",
		speedss = 1600,
		range = 1050,
		delay = 0.25,
		radius = 60,
		hitbox = true,
		aoe = false,
		cc = true,
		collision = true
	},
	["BrandW"] = {
		charName = "Brand",
		slot = 1,
		type = "circular",
		speedss = math.huge,
		range = 900,
		delay = 0.85,
		radius = 250,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false
	},
	["BraumQ"] = {
		charName = "Braum",
		slot = 0,
		type = "linear",
		speedss = 1700,
		range = 1000,
		delay = 0.25,
		radius = 60,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = true
	},
	["BraumRWrapper"] = {
		charName = "Braum",
		slot = 3,
		type = "linear",
		speedss = 1400,
		range = 1250,
		delay = 0.5,
		radius = 115,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false
	},
	["CaitlynPiltoverPeacemaker"] = {
		charName = "Caitlyn",
		slot = 0,
		type = "linear",
		speedss = 2200,
		range = 1500,
		delay = 0.625,
		radius = 60,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false
	},
	["CaitlynYordleTrap"] = {
		charName = "Caitlyn",
		slot = 1,
		type = "circular",
		speedss = math.huge,
		range = 800,
		delay = 0.25,
		radius = 75,
		hitbox = true,
		aoe = false,
		cc = true,
		collision = false
	},
	["CaitlynEntrapmentMissile"] = {
		charName = "Caitlyn",
		slot = 2,
		type = "linear",
		speedss = 1500,
		range = 750,
		delay = 0.25,
		radius = 60,
		hitbox = true,
		aoe = false,
		cc = true,
		collision = true
	},
	["CamilleW"] = {
		charName = "Camille",
		slot = 1,
		type = "conic",
		speedss = math.huge,
		range = 610,
		delay = 0.75,
		angle = 80,
		hitbox = false,
		aoe = true,
		cc = true,
		collision = false
	},
	["CamilleE"] = {
		charName = "Camille",
		slot = 2,
		type = "linear",
		speedss = 1900,
		range = 800,
		delay = 0.25,
		radius = 60,
		hitbox = true,
		aoe = false,
		cc = true,
		collision = false
	},
	["CassiopeiaQ"] = {
		charName = "Cassiopeia",
		slot = 0,
		type = "circular",
		speedss = math.huge,
		range = 850,
		delay = 0.4,
		radius = 150,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false
	},
	["CassiopeiaW"] = {
		charName = "Cassiopeia",
		slot = 1,
		type = "circular",
		speedss = math.huge,
		range = 800,
		delay = 0.25,
		radius = 160,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false
	},
	["CassiopeiaR"] = {
		charName = "Cassiopeia",
		slot = 3,
		type = "conic",
		speedss = math.huge,
		range = 825,
		delay = 0.5,
		angle = 80,
		hitbox = false,
		aoe = true,
		cc = true,
		collision = false
	},
	["Rupture"] = {
		charName = "ChoGath",
		slot = 0,
		type = "circular",
		speedss = math.huge,
		range = 950,
		delay = 0.5,
		radius = 250,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false
	},
	["FeralScream"] = {
		charName = "ChoGath",
		slot = 1,
		type = "conic",
		speedss = math.huge,
		range = 650,
		delay = 0.5,
		angle = 60,
		hitbox = false,
		aoe = true,
		cc = true,
		collision = false
	},
	["PhosphorusBomb"] = {
		charName = "Corki",
		slot = 0,
		type = "circular",
		speedss = 1000,
		range = 825,
		delay = 0.25,
		radius = 250,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false
	},
	["CarpetBomb"] = {
		charName = "Corki",
		slot = 1,
		type = "linear",
		speedss = 650,
		range = 600,
		delay = 0,
		radius = 100,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false
	},
	["CarpetBombMega"] = {
		charName = "Corki",
		slot = 1,
		type = "linear",
		speedss = 1500,
		range = 1800,
		delay = 0,
		radius = 100,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false
	},
	["GGun"] = {
		charName = "Corki",
		slot = 2,
		type = "conic",
		speedss = math.huge,
		range = 600,
		delay = 0.25,
		angle = 35,
		hitbox = false,
		aoe = true,
		cc = false,
		collision = false
	},
	["MissileBarrageMissile"] = {
		charName = "Corki",
		slot = 3,
		type = "linear",
		speedss = 1950,
		range = 1225,
		delay = 0.175,
		radius = 35,
		hitbox = true,
		aoe = false,
		cc = false,
		collision = true
	},
	["MissileBarrageMissile2"] = {
		charName = "Corki",
		slot = 3,
		type = "linear",
		speedss = 1950,
		range = 1225,
		delay = 0.175,
		radius = 35,
		hitbox = true,
		aoe = false,
		cc = false,
		collision = true
	},
	["DariusCleave"] = {
		charName = "Darius",
		slot = 0,
		type = "circular",
		speedss = math.huge,
		range = 0,
		delay = 0.75,
		radius = 425,
		hitbox = false,
		aoe = true,
		cc = false,
		collision = false
	},
	["DariusAxeGrabCone"] = {
		charName = "Darius",
		slot = 2,
		type = "conic",
		speedss = math.huge,
		range = 535,
		delay = 0.25,
		angle = 50,
		hitbox = false,
		aoe = true,
		cc = true,
		collision = false
	},
	["DianaArc"] = {
		charName = "Diana",
		slot = 0,
		type = "arc",
		speedss = 1400,
		range = 900,
		delay = 0.25,
		radius = 205,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false
	},
	["InfectedCleaverMissileCast"] = {
		charName = "DrMundo",
		slot = 0,
		type = "linear",
		speedss = 1850,
		range = 975,
		delay = 0.25,
		radius = 60,
		hitbox = true,
		aoe = false,
		cc = true,
		collision = true
	},
	["DravenDoubleShot"] = {
		charName = "Draven",
		slot = 2,
		type = "linear",
		speedss = 1400,
		range = 1050,
		delay = 0.25,
		radius = 120,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false
	},
	["DravenRCast"] = {
		charName = "Draven",
		slot = 3,
		type = "linear",
		speedss = 2000,
		range = 25000,
		delay = 0.5,
		radius = 130,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false
	},
	["DravenRDoublecast"] = {
		charName = "Draven",
		slot = 3,
		type = "linear",
		speedss = 2000,
		range = 25000,
		delay = 0.5,
		radius = 130,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false
	},
	["EkkoQ"] = {
		charName = "Ekko",
		slot = 0,
		type = "linear",
		speedss = 1650,
		range = 1075,
		delay = 0.25,
		radius = 135,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false
	},
	["EkkoW"] = {
		charName = "Ekko",
		slot = 1,
		type = "circular",
		speedss = 1650,
		range = 1600,
		delay = 3.75,
		radius = 400,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false
	},
	["EkkoR"] = {
		charName = "Ekko",
		slot = 3,
		type = "circular",
		speedss = 1650,
		range = 1600,
		delay = 0.25,
		radius = 375,
		hitbox = false,
		aoe = true,
		cc = false,
		collision = false
	},
	["EliseHumanE"] = {
		charName = "Elise",
		slot = 2,
		type = "linear",
		speedss = 1600,
		range = 1075,
		delay = 0.25,
		radius = 55,
		hitbox = true,
		aoe = false,
		cc = true,
		collision = true
	},
	["EvelynnQ"] = {
		charName = "Evelynn",
		slot = 0,
		type = "linear",
		speedss = 2200,
		range = 800,
		delay = 0.25,
		radius = 35,
		hitbox = true,
		aoe = false,
		cc = false,
		collision = true
	},
	["EvelynnR"] = {
		charName = "Evelynn",
		slot = 3,
		type = "conic",
		speedss = math.huge,
		range = 450,
		delay = 0.35,
		angle = 180,
		hitbox = false,
		aoe = true,
		cc = false,
		collision = false
	},
	["EzrealMysticShot"] = {
		charName = "Ezreal",
		slot = 0,
		type = "linear",
		speedss = 2000,
		range = 1150,
		delay = 0.25,
		radius = 80,
		hitbox = true,
		aoe = false,
		cc = false,
		collision = true
	},
	["EzrealEssenceFlux"] = {
		charName = "Ezreal",
		slot = 1,
		type = "linear",
		speedss = 1550,
		range = 1000,
		delay = 0.25,
		radius = 80,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false
	},
	["EzrealTrueshotBarrage"] = {
		charName = "Ezreal",
		slot = 3,
		type = "linear",
		speedss = 2000,
		range = 25000,
		delay = 1,
		radius = 160,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false
	},
	["FioraQ"] = {
		charName = "Fiora",
		slot = 0,
		type = "linear",
		speedss = 800,
		range = 400,
		delay = 0,
		radius = 50,
		hitbox = true,
		aoe = false,
		cc = false,
		collision = false
	},
	["FioraW"] = {
		charName = "Fiora",
		slot = 1,
		type = "linear",
		speedss = math.huge,
		range = 750,
		delay = 0.75,
		radius = 85,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false
	},
	["FizzR"] = {
		charName = "Fizz",
		slot = 3,
		type = "linear",
		speedss = 1300,
		range = 1300,
		delay = 0.25,
		radius = 120,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false
	},
	["GalioQ"] = {
		charName = "Galio",
		slot = 0,
		type = "arc",
		speedss = 1150,
		range = 825,
		delay = 0.25,
		radius = 150,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false
	},
	["GalioE"] = {
		charName = "Galio",
		slot = 2,
		type = "linear",
		speedss = 1400,
		range = 650,
		delay = 0.45,
		radius = 160,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false
	},
	["GalioR"] = {
		charName = "Galio",
		slot = 3,
		type = "circular",
		speedss = math.huge,
		range = 5500,
		delay = 2.75,
		radius = 500,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false
	},
	["GangplankE"] = {
		charName = "Gangplank",
		slot = 2,
		type = "circular",
		speedss = math.huge,
		range = 1000,
		delay = 0.25,
		radius = 400,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false
	},
	["GangplankR"] = {
		charName = "Gangplank",
		slot = 3,
		type = "circular",
		speedss = math.huge,
		range = 25000,
		delay = 0.25,
		radius = 600,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false
	},
	["GnarQ"] = {
		charName = "Gnar",
		slot = 0,
		type = "linear",
		speedss = 1700,
		range = 1100,
		delay = 0.25,
		radius = 55,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false
	},
	["GnarQReturn"] = {
		charName = "Gnar",
		slot = 0,
		type = "linear",
		speedss = 1700,
		range = 3000,
		delay = 0.25,
		radius = 70,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false
	},
	["GnarE"] = {
		charName = "Gnar",
		slot = 2,
		type = "circular",
		speedss = 900,
		range = 475,
		delay = 0.25,
		radius = 160,
		hitbox = true,
		aoe = false,
		cc = true,
		collision = false
	},
	["GnarBigQ"] = {
		charName = "Gnar",
		slot = 0,
		type = "linear",
		speedss = 2100,
		range = 1100,
		delay = 0.5,
		radius = 90,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = true
	},
	["GnarBigW"] = {
		charName = "Gnar",
		slot = 1,
		type = "linear",
		speedss = math.huge,
		range = 550,
		delay = 0.6,
		radius = 100,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false
	},
	["GnarBigE"] = {
		charName = "Gnar",
		slot = 2,
		type = "circular",
		speedss = 800,
		range = 600,
		delay = 0.25,
		radius = 375,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false
	},
	["GnarR"] = {
		charName = "Gnar",
		slot = 3,
		type = "circular",
		speedss = math.huge,
		range = 475,
		delay = 0.25,
		radius = 475,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false
	},
	["GragasQ"] = {
		charName = "Gragas",
		slot = 0,
		type = "circular",
		speedss = 1000,
		range = 850,
		delay = 0.25,
		radius = 250,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false
	},
	["GragasE"] = {
		charName = "Gragas",
		slot = 2,
		type = "linear",
		speedss = 900,
		range = 600,
		delay = 0.25,
		radius = 170,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = true
	},
	["GragasR"] = {
		charName = "Gragas",
		slot = 3,
		type = "circular",
		speedss = 1800,
		range = 1000,
		delay = 0.25,
		radius = 400,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false
	},
	["GravesQLineSpell"] = {
		charName = "Graves",
		slot = 0,
		type = "linear",
		speedss = 3700,
		range = 925,
		delay = 0.25,
		radius = 40,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false
	},
	["GravesQLineMis"] = {
		charName = "Graves",
		slot = 0,
		type = "rectangle",
		speedss = math.huge,
		range = 925,
		delay = 0.25,
		radius1 = 250,
		radius2 = 100,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false
	},
	["GravesQReturn"] = {
		charName = "Graves",
		slot = 0,
		type = "linear",
		speedss = 1850,
		range = 925,
		delay = 0.25,
		radius = 40,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false
	},
	["GravesSmokeGrenade"] = {
		charName = "Graves",
		slot = 1,
		type = "circular",
		speedss = 1450,
		range = 950,
		delay = 0.25,
		radius = 250,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false
	},
	["GravesChargeShot"] = {
		charName = "Graves",
		slot = 3,
		type = "linear",
		speedss = 1950,
		range = 1000,
		delay = 0.25,
		radius = 100,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false
	},
	["GravesChargeShotFxMissile"] = {
		charName = "Graves",
		slot = 3,
		type = "conic",
		speedss = math.huge,
		range = 800,
		delay = 0.3,
		angle = 80,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false
	},
	["HecarimRapidSlash"] = {
		charName = "Hecarim",
		slot = 0,
		type = "circular",
		speedss = math.huge,
		range = 0,
		delay = 0,
		radius = 350,
		hitbox = false,
		aoe = true,
		cc = false,
		collision = false
	},
	["HecarimUlt"] = {
		charName = "Hecarim",
		slot = 3,
		type = "linear",
		speedss = 1200,
		range = 1000,
		delay = 0.01,
		radius = 210,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false
	},
	["HeimerdingerQ"] = {
		charName = "Heimerdinger",
		slot = 0,
		type = "circular",
		speedss = math.huge,
		range = 450,
		delay = 0.25,
		radius = 55,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false
	},
	["HeimerdingerW"] = {
		charName = "Heimerdinger",
		slot = 1,
		type = "linear",
		speedss = 2050,
		range = 1325,
		delay = 0.25,
		radius = 30,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = true
	},
	["HeimerdingerE"] = {
		charName = "Heimerdinger",
		slot = 2,
		type = "circular",
		speedss = 1200,
		range = 970,
		delay = 0.25,
		radius = 250,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false
	},
	["HeimerdingerEUlt"] = {
		charName = "Heimerdinger",
		slot = 2,
		type = "circular",
		speedss = 1200,
		range = 970,
		delay = 0.25,
		radius = 250,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false
	},
	["IllaoiQ"] = {
		charName = "Illaoi",
		slot = 0,
		type = "linear",
		speedss = math.huge,
		range = 850,
		delay = 0.75,
		radius = 100,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false
	},
	["IllaoiE"] = {
		charName = "Illaoi",
		slot = 2,
		type = "linear",
		speedss = 1800,
		range = 900,
		delay = 0.25,
		radius = 45,
		hitbox = true,
		aoe = false,
		cc = false,
		collision = true
	},
	["IllaoiR"] = {
		charName = "Illaoi",
		slot = 3,
		type = "circular",
		speedss = math.huge,
		range = 0,
		delay = 0.5,
		radius = 450,
		hitbox = false,
		aoe = true,
		cc = false,
		collision = false
	},
	["IreliaW2"] = {
		charName = "Irelia",
		slot = 1,
		type = "circular",
		speedss = math.huge,
		range = 0,
		delay = 0,
		radius = 275,
		hitbox = false,
		aoe = true,
		cc = false,
		collision = false
	},
	["IreliaW2"] = {
		charName = "Irelia",
		slot = 1,
		type = "linear",
		speedss = math.huge,
		range = 825,
		delay = 0.25,
		radius = 90,
		hitbox = false,
		aoe = true,
		cc = false,
		collision = false
	},
	["IreliaE"] = {
		charName = "Irelia",
		slot = 2,
		type = "circular",
		speeds = 2000,
		range = 850,
		delay = 0,
		radius = 90,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false
	},
	["IreliaE2"] = {
		charName = "Irelia",
		slot = 2,
		type = "circular",
		speeds = 2000,
		range = 850,
		delay = 0,
		radius = 90,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false
	},
	["IreliaR"] = {
		charName = "Irelia",
		slot = 3,
		type = "linear",
		speedss = 2000,
		range = 1000,
		delay = 0.4,
		radius = 160,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false
	},
	["IvernQ"] = {
		charName = "Ivern",
		slot = 0,
		type = "linear",
		speedss = 1300,
		range = 1075,
		delay = 0.25,
		radius = 50,
		hitbox = true,
		aoe = false,
		cc = true,
		collision = true
	},
	["IvernW"] = {
		charName = "Ivern",
		slot = 1,
		type = "circular",
		speedss = math.huge,
		range = 800,
		delay = 0.25,
		radius = 150,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false
	},
	["HowlingGale"] = {
		charName = "Janna",
		slot = 0,
		type = "linear",
		speedss = 1167,
		range = 1750,
		delay = 0,
		radius = 100,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false
	},
	["ReapTheWhirlwind"] = {
		charName = "Janna",
		slot = 3,
		type = "circular",
		speedss = math.huge,
		range = 0,
		delay = 0.001,
		radius = 725,
		hitbox = false,
		aoe = true,
		cc = true,
		collision = false
	},
	["JarvanIVDragonStrike"] = {
		charName = "JarvanIV",
		slot = 0,
		type = "linear",
		speedss = math.huge,
		range = 770,
		delay = 0.4,
		radius = 60,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false
	},
	["JarvanIVGoldenAegis"] = {
		charName = "JarvanIV",
		slot = 1,
		type = "circular",
		speedss = math.huge,
		range = 0,
		delay = 0.125,
		radius = 625,
		hitbox = false,
		aoe = true,
		cc = true,
		collision = false
	},
	["JarvanIVDemacianStandard"] = {
		charName = "JarvanIV",
		slot = 2,
		type = "circular",
		speedss = 3440,
		range = 860,
		delay = 0,
		radius = 175,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false
	},
	["JaxCounterStrike"] = {
		charName = "Jax",
		slot = 2,
		type = "circular",
		speedss = math.huge,
		range = 0,
		delay = 1.4,
		radius = 300,
		hitbox = false,
		aoe = true,
		cc = true,
		collision = false
	},
	["JayceShockBlast"] = {
		charName = "Jayce",
		slot = 0,
		type = "linear",
		speedss = 1450,
		range = 1050,
		delay = 0.214,
		radius = 75,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = true
	},
	["JayceShockBlastWallMis"] = {
		charName = "Jayce",
		slot = 0,
		type = "linear",
		speedss = 1890,
		range = 2030,
		delay = 0.214,
		radius = 105,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = true
	},
	["JayceStaticField"] = {
		charName = "Jayce",
		slot = 1,
		type = "circular",
		speedss = math.huge,
		range = 0,
		delay = 0.25,
		radius = 285,
		hitbox = false,
		aoe = true,
		cc = false,
		collision = false
	},
	["JhinW"] = {
		charName = "Jhin",
		slot = 1,
		type = "linear",
		speedss = 5000,
		range = 3000,
		delay = 0.75,
		radius = 40,
		hitbox = true,
		aoe = false,
		cc = true,
		collision = false
	},
	["JhinE"] = {
		charName = "Jhin",
		slot = 2,
		type = "circular",
		speedss = 1650,
		range = 750,
		delay = 0.25,
		radius = 140,
		hitbox = true,
		aoe = false,
		cc = true,
		collision = false
	},
	["JhinRShot"] = {
		charName = "Jhin",
		slot = 3,
		type = "linear",
		speedss = 5000,
		range = 3500,
		delay = 0.25,
		radius = 80,
		hitbox = true,
		aoe = false,
		cc = true,
		collision = false
	},
	["JinxW"] = {
		charName = "Jinx",
		slot = 1,
		type = "linear",
		speedss = 3200,
		range = 1450,
		delay = 0.6,
		radius = 50,
		hitbox = true,
		aoe = false,
		cc = true,
		collision = true
	},
	["JinxE"] = {
		charName = "Jinx",
		slot = 2,
		type = "circular",
		speedss = 2570,
		range = 900,
		delay = 1.5,
		radius = 100,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false
	},
	["JinxR"] = {
		charName = "Jinx",
		slot = 3,
		type = "linear",
		speedss = 1700,
		range = 25000,
		delay = 0.6,
		radius = 110,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false
	},
	["KatarinaW"] = {
		charName = "Katarina",
		slot = 1,
		type = "circular",
		speedss = math.huge,
		range = 0,
		delay = 1.25,
		radius = 340,
		hitbox = false,
		aoe = true,
		cc = false,
		collision = false
	},
	["KatarinaE"] = {
		charName = "Katarina",
		slot = 2,
		type = "circular",
		speedss = math.huge,
		range = 725,
		delay = 0.15,
		radius = 150,
		hitbox = true,
		aoe = false,
		cc = false,
		collision = false
	},
	["KatarinaR"] = {
		charName = "Katarina",
		slot = 3,
		type = "circular",
		speedss = math.huge,
		range = 0,
		delay = 0,
		radius = 550,
		hitbox = false,
		aoe = true,
		cc = false,
		collision = false
	},
	["KalistaMysticShot"] = {
		charName = "Kalista",
		slot = 0,
		type = "linear",
		speedss = 2100,
		range = 1150,
		delay = 0.35,
		radius = 35,
		hitbox = true,
		aoe = false,
		cc = false,
		collision = true
	},
	["KarmaQ"] = {
		charName = "Karma",
		slot = 0,
		type = "linear",
		speedss = 1750,
		range = 950,
		delay = 0.25,
		radius = 80,
		hitbox = true,
		aoe = false,
		cc = true,
		collision = true
	},
	["KarmaQMantra"] = {
		charName = "Karma",
		slot = 0,
		type = "linear",
		speedss = 1750,
		range = 950,
		delay = 0.25,
		radius = 80,
		hitbox = true,
		aoe = false,
		cc = true,
		collision = true
	},
	["KarthusLayWasteA1"] = {
		charName = "Karthus",
		slot = 0,
		type = "circular",
		speedss = math.huge,
		range = 875,
		delay = 0.5,
		radius = 200,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false
	},
	["KarthusLayWasteA2"] = {
		charName = "Karthus",
		slot = 0,
		type = "circular",
		speedss = math.huge,
		range = 875,
		delay = 0.5,
		radius = 200,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false
	},
	["KarthusLayWasteA3"] = {
		charName = "Karthus",
		slot = 0,
		type = "circular",
		speedss = math.huge,
		range = 875,
		delay = 0.5,
		radius = 200,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false
	},
	["KarthusWallOfPain"] = {
		charName = "Karthus",
		slot = 1,
		type = "rectangle",
		speedss = math.huge,
		range = 1000,
		delay = 0.25,
		radius1 = 470,
		radius2 = 75,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false
	},
	["ForcePulse"] = {
		charName = "Kassadin",
		slot = 2,
		type = "conic",
		speedss = math.huge,
		range = 600,
		delay = 0.25,
		angle = 80,
		hitbox = false,
		aoe = true,
		cc = true,
		collision = false
	},
	["Riftwalk"] = {
		charName = "Kassadin",
		slot = 3,
		type = "circular",
		speedss = math.huge,
		range = 500,
		delay = 0.25,
		radius = 300,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false
	},
	["KaynQ"] = {
		charName = "Kayn",
		slot = 0,
		type = "circular",
		speedss = math.huge,
		range = 0,
		delay = 0.15,
		radius = 350,
		hitbox = false,
		aoe = true,
		cc = false,
		collision = false
	},
	["KaynW"] = {
		charName = "Kayn",
		slot = 1,
		type = "linear",
		speedss = math.huge,
		range = 700,
		delay = 0.55,
		radius = 90,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false
	},
	["KennenShurikenHurlMissile1"] = {
		charName = "Kennen",
		slot = 0,
		type = "linear",
		speedss = 1650,
		range = 1050,
		delay = 0.175,
		radius = 45,
		hitbox = true,
		aoe = false,
		cc = false,
		collision = true
	},
	["KennenShurikenStorm"] = {
		charName = "Kennen",
		slot = 3,
		type = "circular",
		speedss = math.huge,
		range = 0,
		delay = 0.25,
		radius = 550,
		hitbox = false,
		aoe = true,
		cc = false,
		collision = false
	},
	["KaisaW"] = {
		charName = "Kaisa",
		slot = 1,
		type = "linear",
		speedss = 1750,
		range = 3000,
		delay = 0.4,
		radius = 65,
		hitbox = true,
		aoe = false,
		cc = false,
		collision = true
	},
	["KhazixW"] = {
		charName = "Khazix",
		slot = 1,
		type = "linear",
		speedss = 1650,
		range = 1000,
		delay = 0.25,
		radius = 60,
		hitbox = true,
		aoe = false,
		cc = false,
		collision = true
	},
	["KhazixWLong"] = {
		charName = "Khazix",
		slot = 1,
		type = "linear",
		speedss = 1650,
		range = 1000,
		delay = 0.25,
		radius = 70,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = true
	},
	["KhazixE"] = {
		charName = "Khazix",
		slot = 2,
		type = "circular",
		speedss = 1400,
		range = 700,
		delay = 0.25,
		radius = 320,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false
	},
	["KhazixELong"] = {
		charName = "Khazix",
		slot = 2,
		type = "circular",
		speedss = 1400,
		range = 900,
		delay = 0.25,
		radius = 320,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false
	},
	["KindredQ"] = {
		charName = "Kindred",
		slot = 0,
		type = "linear",
		speedss = 1360,
		range = 340,
		delay = 0.01,
		radius = 55,
		hitbox = true,
		aoe = false,
		cc = false,
		collision = false
	},
	["KindredR"] = {
		charName = "Kindred",
		slot = 3,
		type = "circular",
		speedss = math.huge,
		range = 0,
		delay = 0.25,
		radius = 500,
		hitbox = false,
		aoe = true,
		cc = false,
		collision = false
	},
	["KledQ"] = {
		charName = "Kled",
		slot = 0,
		type = "linear",
		speedss = 1400,
		range = 800,
		delay = 0.25,
		radius = 60,
		hitbox = true,
		aoe = false,
		cc = true,
		collision = true
	},
	["KledEDash"] = {
		charName = "Kled",
		slot = 2,
		type = "linear",
		speedss = 1100,
		range = 550,
		delay = 0,
		radius = 90,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false
	},
	["KledRiderQ"] = {
		charName = "Kled",
		slot = 0,
		type = "conic",
		speedss = math.huge,
		range = 700,
		delay = 0.25,
		angle = 25,
		hitbox = false,
		aoe = true,
		cc = false,
		collision = false
	},
	["KogMawQ"] = {
		charName = "KogMaw",
		slot = 0,
		type = "linear",
		speedss = 1600,
		range = 1175,
		delay = 0.25,
		radius = 60,
		hitbox = true,
		aoe = false,
		cc = false,
		collision = true
	},
	["KogMawVoidOoze"] = {
		charName = "KogMaw",
		slot = 2,
		type = "linear",
		speedss = 1350,
		range = 1280,
		delay = 0.25,
		radius = 115,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false
	},
	["KogMawLivingArtillery"] = {
		charName = "KogMaw",
		slot = 3,
		type = "circular",
		speedss = math.huge,
		range = 1800,
		delay = 0.85,
		radius = 200,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false
	},
	["LeblancW"] = {
		charName = "Leblanc",
		slot = 1,
		type = "circular",
		speedss = 1600,
		range = 600,
		delay = 0.25,
		radius = 260,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false
	},
	["LeblancE"] = {
		charName = "Leblanc",
		slot = 2,
		type = "linear",
		speedss = 1750,
		range = 925,
		delay = 0.25,
		radius = 55,
		hitbox = true,
		aoe = false,
		cc = true,
		collision = true
	},
	["LeblancRW"] = {
		charName = "Leblanc",
		slot = 1,
		type = "circular",
		speedss = 1600,
		range = 600,
		delay = 0.25,
		radius = 260,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false
	},
	["LeblancRE"] = {
		charName = "Leblanc",
		slot = 2,
		type = "linear",
		speedss = 1750,
		range = 925,
		delay = 0.25,
		radius = 55,
		hitbox = true,
		aoe = false,
		cc = true,
		collision = true
	},
	["BlinkMonkQOne"] = {
		charName = "LeeSin",
		slot = 0,
		type = "linear",
		speedss = 1750,
		range = 1100,
		delay = 0.25,
		radius = 50,
		hitbox = true,
		aoe = false,
		cc = false,
		collision = true
	},
	["BlinkMonkEOne"] = {
		charName = "LeeSin",
		slot = 2,
		type = "circular",
		speedss = math.huge,
		range = 0,
		delay = 0.25,
		radius = 350,
		hitbox = false,
		aoe = true,
		cc = false,
		collision = false
	},
	["LeonaZenithBladeMissile"] = {
		charName = "Leona",
		slot = 2,
		type = "linear",
		speedss = 2000,
		range = 875,
		delay = 0.25,
		radius = 70,
		hitbox = true,
		aoe = false,
		cc = true,
		collision = false
	},
	["LeonaSolarFlare"] = {
		charName = "Leona",
		slot = 3,
		type = "circular",
		speedss = math.huge,
		range = 1200,
		delay = 0.625,
		radius = 250,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false
	},
	["LissandraQ"] = {
		charName = "Lissandra",
		slot = 0,
		type = "linear",
		speedss = 2400,
		range = 825,
		delay = 0.251,
		radius = 65,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false
	},
	["LissandraW"] = {
		charName = "Lissandra",
		slot = 1,
		type = "circular",
		speedss = math.huge,
		range = 0,
		delay = 0.25,
		radius = 450,
		hitbox = false,
		aoe = true,
		cc = true,
		collision = false
	},
	["LissandraE"] = {
		charName = "Lissandra",
		slot = 2,
		type = "linear",
		speedss = 850,
		range = 1050,
		delay = 0.25,
		radius = 100,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false
	},
	["LucianQ"] = {
		charName = "Lucian",
		slot = 0,
		type = "linear",
		speedss = math.huge,
		range = 900,
		delay = 0.5,
		radius = 65,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false
	},
	["LucianW"] = {
		charName = "Lucian",
		slot = 1,
		type = "linear",
		speedss = 1600,
		range = 900,
		delay = 0.25,
		radius = 65,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false
	},
	["LucianR"] = {
		charName = "Lucian",
		slot = 3,
		type = "linear",
		speedss = 2800,
		range = 1200,
		delay = 0.01,
		radius = 75,
		hitbox = true,
		aoe = false,
		cc = false,
		collision = true
	},
	["PykeQMelee"] = {
		charName = "Pyke",
		slot = 0,
		type = "linear",
		speeds = math.huge,
		range = 400,
		delay = 0.25,
		radius = 70,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false
	},
	["PykeQRange"] = {
		charName = "Pyke",
		slot = 0,
		type = "linear",
		speeds = 2000,
		range = 1100,
		delay = 0.2,
		radius = 70,
		hitbox = true,
		aoe = false,
		cc = true,
		collision = true
	},
	["PykeR"] = {
		charName = "Pyke",
		slot = 3,
		type = "cross",
		speeds = math.huge,
		range = 750,
		delay = 0.5,
		radius1 = 300,
		radius2 = 50,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false
	},
	["LuluQ"] = {
		charName = "Lulu",
		slot = 0,
		type = "linear",
		speedss = 1500,
		range = 925,
		delay = 0.25,
		radius = 45,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false
	},
	["NeekoQ"] = {
		charName = "Neeko",
		slot = 0,
		type = "circular",
		speedss = 1500,
		range = 800,
		delay = 0.25,
		radius = 225,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false
	},
		["NeekoE"] = {
		charName = "Neeko",
		slot = 2,
		type = "linear",
		speedss = 1500,
		range = 1000,
		delay = 0.25,
		radius = 225,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false
	},

	["LuxLightBinding"] = {
		charName = "Lux",
		slot = 0,
		type = "linear",
		speedss = 1200,
		range = 1175,
		delay = 0.25,
		radius = 60,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = true
	},
	["LuxPrismaticWave"] = {
		charName = "Lux",
		slot = 1,
		type = "linear",
		speedss = 1400,
		range = 1075,
		delay = 0.25,
		radius = 120,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false
	},
	["LuxLightStrikeKugel"] = {
		charName = "Lux",
		slot = 2,
		type = "circular",
		speedss = 1300,
		range = 1000,
		delay = 0.25,
		radius = 310,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false
	},
	["LuxMaliceCannon"] = {
		charName = "Lux",
		slot = 3,
		type = "linear",
		speedss = math.huge,
		range = 3340,
		delay = 1,
		radius = 115,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false
	},
	["Landslide"] = {
		charName = "Malphite",
		slot = 2,
		type = "circular",
		speedss = math.huge,
		range = 0,
		delay = 0.242,
		radius = 200,
		hitbox = false,
		aoe = true,
		cc = true,
		collision = false
	},
	["UFSlash"] = {
		charName = "Malphite",
		slot = 3,
		type = "circular",
		speedss = 2170,
		range = 1000,
		delay = 0,
		radius = 300,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false
	},
	["MalzaharQ"] = {
		charName = "Malzahar",
		slot = 0,
		type = "rectangle",
		speedss = math.huge,
		range = 900,
		delay = 0.25,
		radius1 = 400,
		radius2 = 100,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false
	},
	["MaokaiQ"] = {
		charName = "Maokai",
		slot = 0,
		type = "linear",
		speedss = 1600,
		range = 600,
		delay = 0.375,
		radius = 150,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false
	},
	["MaokaiR"] = {
		charName = "Maokai",
		slot = 3,
		type = "linear",
		speedss = 450,
		range = 3000,
		delay = 0.5,
		radius = 650,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false
	},
	["MissFortuneScattershot"] = {
		charName = "MissFortune",
		slot = 2,
		type = "circular",
		speedss = math.huge,
		range = 1000,
		delay = 0.5,
		radius = 400,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false
	},
	["MissFortuneBulletTime"] = {
		charName = "MissFortune",
		slot = 3,
		type = "conic",
		speedss = math.huge,
		range = 1400,
		delay = 0.001,
		angle = 40,
		hitbox = false,
		aoe = true,
		cc = false,
		collision = false
	},
	["MordekaiserSiphonOfDestruction"] = {
		charName = "Mordekaiser",
		slot = 2,
		type = "conic",
		speedss = math.huge,
		range = 675,
		delay = 0.25,
		angle = 50,
		hitbox = false,
		aoe = true,
		cc = false,
		collision = false
	},
	["DarkBinding"] = {
		charName = "Morgana",
		slot = 0,
		type = "linear",
		speedss = 1200,
		range = 1175,
		delay = 0.25,
		radius = 60,
		hitbox = true,
		aoe = false,
		cc = true,
		collision = true
	},
	["TormentedSoil"] = {
		charName = "Morgana",
		slot = 1,
		type = "circular",
		speedss = math.huge,
		range = 900,
		delay = 0.25,
		radius = 325,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false
	},
	["NamiQ"] = {
		charName = "Nami",
		slot = 0,
		type = "circular",
		speedss = math.huge,
		range = 875,
		delay = 0.95,
		radius = 200,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false
	},
	["NamiR"] = {
		charName = "Nami",
		slot = 3,
		type = "linear",
		speedss = 850,
		range = 2750,
		delay = 0.5,
		radius = 215,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false
	},
	["NasusE"] = {
		charName = "Nasus",
		slot = 2,
		type = "circular",
		speedss = math.huge,
		range = 650,
		delay = 0.25,
		radius = 400,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false
	},
	["NautilusAnchorDrag"] = {
		charName = "Nautilus",
		slot = 0,
		type = "linear",
		speedss = 2000,
		range = 1100,
		delay = 0.25,
		radius = 75,
		hitbox = true,
		aoe = false,
		cc = true,
		collision = true
	},
	["NautilusSplashZone"] = {
		charName = "Nautilus",
		slot = 2,
		type = "circular",
		speedss = math.huge,
		range = 0,
		delay = 0.25,
		radius = 600,
		hitbox = false,
		aoe = true,
		cc = true,
		collision = false
	},
	["JavelinToss"] = {
		charName = "Nidalee",
		slot = 0,
		type = "linear",
		speedss = 1300,
		range = 1500,
		delay = 0.25,
		radius = 45,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = true
	},
	["Bushwhack"] = {
		charName = "Nidalee",
		slot = 1,
		type = "circular",
		speedss = math.huge,
		range = 900,
		delay = 0.25,
		radius = 85,
		hitbox = true,
		aoe = false,
		cc = false,
		collision = true
	},
	["Pounce"] = {
		charName = "Nidalee",
		slot = 1,
		type = "circular",
		speedss = 1750,
		range = 750,
		delay = 0.25,
		radius = 200,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false
	},
	["Swipe"] = {
		charName = "Nidalee",
		slot = 2,
		type = "conic",
		speedss = math.huge,
		range = 300,
		delay = 0.25,
		angle = 180,
		hitbox = false,
		aoe = true,
		cc = false,
		collision = false
	},
	["NocturneDuskbringer"] = {
		charName = "Nocturne",
		slot = 0,
		type = "linear",
		speedss = 1400,
		range = 1200,
		delay = 0.25,
		radius = 60,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false
	},
	["AbsoluteZero"] = {
		charName = "Nunu",
		slot = 3,
		type = "circular",
		speedss = math.huge,
		range = 0,
		delay = 3.01,
		radius = 650,
		hitbox = false,
		aoe = true,
		cc = true,
		collision = false
	},
	["OlafAxeThrowCast"] = {
		charName = "Olaf",
		slot = 0,
		type = "linear",
		speedss = 1550,
		range = 1000,
		delay = 0.25,
		radius = 80,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false
	},
	["OrianaIzunaCommand"] = {
		charName = "Orianna",
		slot = 0,
		type = "linear",
		speedss = 1400,
		range = 825,
		delay = 0.25,
		radius = 175,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false
	},
	["OrianaDissonanceCommand"] = {
		charName = "Orianna",
		slot = 1,
		type = "circular",
		speedss = math.huge,
		range = 0,
		delay = 0.25,
		radius = 250,
		hitbox = false,
		aoe = true,
		cc = true,
		collision = false
	},
	["OrianaRedactCommand"] = {
		charName = "Orianna",
		slot = 2,
		type = "linear",
		speedss = 1400,
		range = 1100,
		delay = 0.25,
		radius = 55,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false
	},
	["OrianaDetonateCommand"] = {
		charName = "Orianna",
		slot = 3,
		type = "circular",
		speedss = math.huge,
		range = 0,
		delay = 0.5,
		radius = 325,
		hitbox = false,
		aoe = true,
		cc = true,
		collision = false
	},
	["OrnnQ"] = {
		charName = "Ornn",
		slot = 0,
		type = "linear",
		speedss = 2000,
		range = 800,
		delay = 0.3,
		radius = 100,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false
	},
	["OrnnW"] = {
		charName = "Ornn",
		slot = 1,
		type = "linear",
		speedss = math.huge,
		range = 550,
		delay = 0.25,
		radius = 110,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false
	},
	["OrnnE"] = {
		charName = "Ornn",
		slot = 2,
		type = "linear",
		speedss = 1780,
		range = 800,
		delay = 0.35,
		radius = 150,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false
	},
	["OrnnR"] = {
		charName = "Ornn",
		slot = 3,
		type = "linear",
		speedss = 1200,
		range = 2500,
		delay = 0.5,
		radius = 225,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false
	},
	["PantheonE"] = {
		charName = "Pantheon",
		slot = 2,
		type = "conic",
		speedss = math.huge,
		range = 0,
		delay = 0.389,
		angle = 80,
		hitbox = false,
		aoe = true,
		cc = false,
		collision = false
	},
	["PantheonRFall"] = {
		charName = "Pantheon",
		slot = 3,
		type = "circular",
		speedss = math.huge,
		range = 5500,
		delay = 2.5,
		angle = 700,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false
	},
	["PoppyQSpell"] = {
		charName = "Poppy",
		slot = 0,
		type = "linear",
		speedss = math.huge,
		range = 430,
		delay = 1.32,
		radius = 85,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false
	},
	["PoppyW"] = {
		charName = "Poppy",
		slot = 1,
		type = "circular",
		speedss = math.huge,
		range = 0,
		delay = 0,
		radius = 400,
		hitbox = false,
		aoe = true,
		cc = false,
		collision = false
	},
	["PoppyRSpell"] = {
		charName = "Poppy",
		slot = 3,
		type = "linear",
		speedss = 1600,
		range = 1900,
		delay = 0.6,
		radius = 80,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false
	},
	["QuinnQ"] = {
		charName = "Quinn",
		slot = 0,
		type = "linear",
		speedss = 1550,
		range = 1025,
		delay = 0.25,
		radius = 50,
		hitbox = true,
		aoe = false,
		cc = false,
		collision = true
	},
	["RakanQ"] = {
		charName = "Rakan",
		slot = 0,
		type = "linear",
		speedss = 1800,
		range = 900,
		delay = 0.25,
		radius = 60,
		hitbox = true,
		aoe = false,
		cc = false,
		collision = true
	},
	["RakanW"] = {
		charName = "Rakan",
		slot = 1,
		type = "circular",
		speedss = 2150,
		range = 600,
		delay = 0,
		radius = 250,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false
	},
	["RakanWCast"] = {
		charName = "Rakan",
		slot = 1,
		type = "circular",
		speedss = math.huge,
		range = 0,
		delay = 0.5,
		radius = 250,
		hitbox = false,
		aoe = true,
		cc = true,
		collision = false
	},
	["Tremors2"] = {
		charName = "Rammus",
		slot = 3,
		type = "circular",
		speedss = math.huge,
		range = 0,
		delay = 0.25,
		radius = 300,
		hitbox = false,
		aoe = true,
		cc = true,
		collision = false
	},
	["RekSaiQBurrowed"] = {
		charName = "RekSai",
		slot = 0,
		type = "linear",
		speedss = 2100,
		range = 1650,
		delay = 0.125,
		radius = 50,
		hitbox = true,
		aoe = false,
		cc = false,
		collision = true
	},
	["RenektonCleave"] = {
		charName = "Renekton",
		slot = 0,
		type = "circular",
		speedss = math.huge,
		range = 0,
		delay = 0.25,
		radius = 325,
		hitbox = false,
		aoe = true,
		cc = false,
		collision = false
	},
	["RenektonSliceAndDice"] = {
		charName = "Renekton",
		slot = 2,
		type = "linear",
		speedss = 1125,
		range = 450,
		delay = 0.25,
		radius = 45,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false
	},
	["RengarQ"] = {
		charName = "Rengar",
		slot = 0,
		type = "linear",
		speedss = math.huge,
		range = 450,
		delay = 0.325,
		radius = 50,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false
	},
	["RengarW"] = {
		charName = "Rengar",
		slot = 1,
		type = "circular",
		speedss = math.huge,
		range = 0,
		delay = 0.25,
		radius = 450,
		hitbox = false,
		aoe = true,
		cc = false,
		collision = false
	},
	["RengarE"] = {
		charName = "Rengar",
		slot = 2,
		type = "linear",
		speedss = 1500,
		range = 1000,
		delay = 0.25,
		radius = 60,
		hitbox = true,
		aoe = false,
		cc = true,
		collision = true
	},
	["RivenTriCleave"] = {
		charName = "Riven",
		slot = 0,
		type = "linear",
		speedss = 1100,
		range = 260,
		delay = 0.25,
		radius = 200,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false
	},
	["RivenMartyr"] = {
		charName = "Riven",
		slot = 1,
		type = "circular",
		speedss = math.huge,
		range = 0,
		delay = 0.267,
		radius = 135,
		hitbox = false,
		aoe = true,
		cc = true,
		collision = false
	},
	["RivenFeint"] = {
		charName = "Riven",
		slot = 2,
		type = "linear",
		speedss = 1100,
		range = 325,
		delay = 0,
		radius = 100,
		hitbox = true,
		aoe = false,
		cc = false,
		collision = false
	},
	["RivenIzunaBlade"] = {
		charName = "Riven",
		slot = 3,
		type = "conic",
		speedss = 1600,
		range = 900,
		delay = 0.25,
		angle = 50,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false
	},
	["RumbleGrenade"] = {
		charName = "Rumble",
		slot = 2,
		type = "linear",
		speedss = 2000,
		range = 850,
		delay = 0.25,
		radius = 70,
		hitbox = true,
		aoe = false,
		cc = true,
		collision = true
	},
	["RumbleCarpetBombDummy"] = {
		charName = "Rumble",
		slot = 3,
		type = "linear",
		speedss = 1600,
		range = 1700,
		delay = 0.583,
		radius = 130,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false
	},
	["RyzeQ"] = {
		charName = "Ryze",
		slot = 0,
		type = "linear",
		speedss = 1700,
		range = 1000,
		delay = 0.25,
		radius = 50,
		hitbox = true,
		aoe = false,
		cc = false,
		collision = true
	},
	["SejuaniQ"] = {
		charName = "Sejuani",
		slot = 0,
		type = "linear",
		speedss = 1300,
		range = 650,
		delay = 0.25,
		radius = 150,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false
	},
	["SejuaniW"] = {
		charName = "Sejuani",
		slot = 1,
		type = "conic",
		speedss = math.huge,
		range = 600,
		delay = 0.25,
		angle = 75,
		hitbox = false,
		aoe = true,
		cc = true,
		collision = false
	},
	["SejuaniWDummy"] = {
		charName = "Sejuani",
		slot = 1,
		type = "linear",
		speedss = math.huge,
		range = 600,
		delay = 1,
		radius = 65,
		hitbox = true,
		aoe = false,
		cc = true,
		collision = false
	},
	["SejuaniR"] = {
		charName = "Sejuani",
		slot = 3,
		type = "linear",
		speedss = 1650,
		range = 1300,
		delay = 0.25,
		radius = 100,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false
	},
	["ShenE"] = {
		charName = "Shen",
		slot = 2,
		type = "linear",
		speedss = 1200,
		range = 600,
		delay = 0,
		radius = 60,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false
	},
	["ShyvanaFireball"] = {
		charName = "Shyvana",
		slot = 2,
		type = "linear",
		speedss = 1575,
		range = 925,
		delay = 0.25,
		radius = 60,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false
	},
	["ShyvanaTransformLeap"] = {
		charName = "Shyvana",
		slot = 3,
		type = "linear",
		speedss = 1130,
		range = 850,
		delay = 0.25,
		radius = 160,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false
	},
	["ShyvanaFireballDragon2"] = {
		charName = "Shyvana",
		slot = 2,
		type = "linear",
		speedss = 1575,
		range = 925,
		delay = 0.333,
		radius = 60,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false
	},
	["MegaAdhesive"] = {
		charName = "Singed",
		slot = 1,
		type = "circular",
		speedss = math.huge,
		range = 1000,
		delay = 0.25,
		radius = 265,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false
	},
	["SionQ"] = {
		charName = "Sion",
		slot = 0,
		type = "linear",
		speedss = math.huge,
		range = 600,
		delay = 0,
		radius = 300,
		hitbox = false,
		aoe = true,
		cc = true,
		collision = false
	},
	["SionE"] = {
		charName = "Sion",
		slot = 2,
		type = "linear",
		speedss = 1900,
		range = 725,
		delay = 0.25,
		radius = 80,
		hitbox = false,
		aoe = true,
		cc = true,
		collision = false
	},
	["SionR"] = {
		charName = "Sion",
		slot = 3,
		type = "linear",
		speedss = 950,
		range = 7500,
		delay = 0.125,
		radius = 200,
		hitbox = false,
		aoe = true,
		cc = true,
		collision = false
	},
	["SivirQ"] = {
		charName = "Sivir",
		slot = 0,
		type = "linear",
		speedss = 1350,
		range = 1250,
		delay = 0.25,
		radius = 75,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false
	},
	["SivirQReturn"] = {
		charName = "Sivir",
		slot = 0,
		type = "linear",
		speedss = 1350,
		range = 1250,
		delay = 0,
		radius = 75,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false
	},
	["SkarnerVirulentSlash"] = {
		charName = "Skarner",
		slot = 0,
		type = "circular",
		speedss = math.huge,
		range = 0,
		delay = 0.25,
		radius = 350,
		hitbox = false,
		aoe = true,
		cc = false,
		collision = false
	},
	["SkarnerFracture"] = {
		charName = "Skarner",
		slot = 2,
		type = "linear",
		speedss = 1500,
		range = 1000,
		delay = 0.25,
		radius = 70,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false
	},
	["SonaR"] = {
		charName = "Sona",
		slot = 3,
		type = "linear",
		speedss = 2250,
		range = 900,
		delay = 0.25,
		radius = 120,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false
	},
	["SorakaQ"] = {
		charName = "Soraka",
		slot = 0,
		type = "circular",
		speedss = 1150,
		range = 800,
		delay = 0.25,
		radius = 235,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false
	},
	["SorakaE"] = {
		charName = "Soraka",
		slot = 2,
		type = "circular",
		speedss = math.huge,
		range = 925,
		delay = 1.5,
		radius = 300,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false
	},
	["SwainQ"] = {
		charName = "Swain",
		slot = 0,
		type = "conic",
		speedss = math.huge,
		range = 725,
		delay = 0.25,
		angle = 45,
		hitbox = false,
		aoe = true,
		cc = false,
		collision = false
	},
	["SwainW"] = {
		charName = "Swain",
		slot = 1,
		type = "circular",
		speedss = math.huge,
		range = 3500,
		delay = 1.5,
		radius = 260,
		hitbox = false,
		aoe = true,
		cc = false,
		collision = false
	},
	["SwainE"] = {
		charName = "Swain",
		slot = 2,
		type = "linear",
		speedss = 1550,
		range = 850,
		delay = 0.25,
		radius = 65,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false
	},
	["SwainR"] = {
		charName = "Swain",
		slot = 3,
		type = "circular",
		speedss = math.huge,
		range = 0,
		delay = 0.5,
		radius = 650,
		hitbox = false,
		aoe = true,
		cc = true,
		collision = false
	},
	["SyndraQ"] = {
		charName = "Syndra",
		slot = 0,
		type = "circular",
		speedss = math.huge,
		range = 800,
		delay = 0.625,
		radius = 200,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false
	},
	["SyndraWCast"] = {
		charName = "Syndra",
		slot = 1,
		type = "circular",
		speedss = 1450,
		range = 950,
		delay = 0.25,
		radius = 225,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false
	},
	["SyndraE"] = {
		charName = "Syndra",
		slot = 2,
		type = "conic",
		speedss = 2500,
		range = 700,
		delay = 0.25,
		angle = 40,
		hitbox = false,
		aoe = true,
		cc = true,
		collision = false
	},
	["SyndraEMissile"] = {
		charName = "Syndra",
		slot = 2,
		type = "linear",
		speedss = 1600,
		range = 1250,
		delay = 0.25,
		radius = 50,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false
	},
	["TahmKenchQ"] = {
		charName = "TahmKench",
		slot = 0,
		type = "linear",
		speedss = 2670,
		range = 800,
		delay = 0.25,
		radius = 70,
		hitbox = true,
		aoe = false,
		cc = true,
		collision = true
	},
	["TaliyahQ"] = {
		charName = "Taliyah",
		slot = 0,
		type = "linear",
		speedss = 2850,
		range = 1000,
		delay = 0.25,
		radius = 70,
		hitbox = true,
		aoe = false,
		cc = false,
		collision = true
	},
	["TaliyahWVC"] = {
		charName = "Taliyah",
		slot = 1,
		type = "circular",
		speedss = math.huge,
		range = 900,
		delay = 0.6,
		radius = 150,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false
	},
	["TaliyahE"] = {
		charName = "Taliyah",
		slot = 2,
		type = "conic",
		speedss = 2000,
		range = 800,
		delay = 0.25,
		angle = 80,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false
	},
	["TalonW"] = {
		charName = "Talon",
		slot = 1,
		type = "conic",
		speedss = 1850,
		range = 650,
		delay = 0.25,
		angle = 35,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false
	},
	["TalonR"] = {
		charName = "Talon",
		slot = 3,
		type = "circular",
		speedss = math.huge,
		range = 0,
		delay = 0.25,
		radius = 550,
		hitbox = false,
		aoe = true,
		cc = false,
		collision = false
	},
	["TaricE"] = {
		charName = "Taric",
		slot = 2,
		type = "linear",
		speedss = math.huge,
		range = 575,
		delay = 1,
		radius = 70,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false
	},
	["TeemoRCast"] = {
		charName = "Teemo",
		slot = 3,
		type = "circular",
		speedss = math.huge,
		range = 900,
		delay = 1.25,
		radius = 200,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false
	},
	["ThreshQ"] = {
		charName = "Thresh",
		slot = 0,
		type = "linear",
		speedss = 1900,
		range = 1100,
		delay = 0.5,
		radius = 55,
		hitbox = true,
		aoe = false,
		cc = true,
		collision = true
	},
	["ThreshE"] = {
		charName = "Thresh",
		slot = 2,
		type = "linear",
		speedss = math.huge,
		range = 400,
		delay = 0.389,
		radius = 95,
		hitbox = false,
		aoe = true,
		cc = true,
		collision = false
	},
	["ThreshRPenta"] = {
		charName = "Thresh",
		slot = 3,
		type = "pentagon",
		speedss = math.huge,
		range = 0,
		delay = 0.45,
		radius = 450,
		hitbox = false,
		aoe = true,
		cc = true,
		collision = false
	},
	["TristanaW"] = {
		charName = "Tristana",
		slot = 1,
		type = "circular",
		speedss = 1100,
		range = 900,
		delay = 0.25,
		radius = 250,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false
	},
	["trundledesecrate"] = {
		charName = "Trundle",
		slot = 1,
		type = "circular",
		speedss = math.huge,
		range = 0,
		delay = 0.25,
		radius = 1000,
		hitbox = false,
		aoe = false,
		cc = false,
		collision = false
	},
	["TrundleCircle"] = {
		charName = "Trundle",
		slot = 2,
		type = "circular",
		speedss = math.huge,
		range = 1000,
		delay = 0.25,
		radius = 375,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false
	},
	["TryndamereE"] = {
		charName = "Tryndamere",
		slot = 2,
		type = "linear",
		speedss = 1300,
		range = 660,
		delay = 0,
		radius = 225,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false
	},
	["WildCards"] = {
		charName = "TwistedFate",
		slot = 0,
		type = "linear",
		speedss = 1000,
		range = 1450,
		delay = 0.25,
		radius = 35,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false
	},
	["TwitchVenomCask"] = {
		charName = "Twitch",
		slot = 1,
		type = "circular",
		speedss = 1400,
		range = 950,
		delay = 0.25,
		radius = 340,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false
	},
	["UrgotQ"] = {
		charName = "Urgot",
		slot = 0,
		type = "circular",
		speedss = math.huge,
		range = 800,
		delay = 0.6,
		radius = 215,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false
	},
	["UrgotE"] = {
		charName = "Urgot",
		slot = 2,
		type = "linear",
		speedss = 1050,
		range = 475,
		delay = 0.45,
		radius = 100,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false
	},
	["UrgotR"] = {
		charName = "Urgot",
		slot = 3,
		type = "linear",
		speedss = 3200,
		range = 1600,
		delay = 0.4,
		radius = 70,
		hitbox = true,
		aoe = false,
		cc = true,
		collision = false
	},
	["VarusQ"] = {
		charName = "Varus",
		slot = 0,
		type = "linear",
		speedss = 1850,
		range = 1625,
		delay = 0,
		radius = 40,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false
	},
	["VarusE"] = {
		charName = "Varus",
		slot = 2,
		type = "circular",
		speedss = 1500,
		range = 925,
		delay = 0.242,
		radius = 280,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false
	},
	["VarusR"] = {
		charName = "Varus",
		slot = 3,
		type = "linear",
		speedss = 1850,
		range = 1075,
		delay = 0.242,
		radius = 120,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false
	},
	["VayneTumble"] = {
		charName = "Vayne",
		slot = 0,
		type = "linear",
		speedss = 900,
		range = 300,
		delay = 0.25,
		radius = 45,
		hitbox = true,
		aoe = false,
		cc = false,
		collision = false
	},
	["VeigarBalefulStrike"] = {
		charName = "Veigar",
		slot = 0,
		type = "linear",
		speedss = 2000,
		range = 950,
		delay = 0.25,
		radius = 60,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = true
	},
	["VeigarDarkMatter"] = {
		charName = "Veigar",
		slot = 1,
		type = "circular",
		speedss = math.huge,
		range = 900,
		delay = 1.25,
		radius = 225,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false
	},
	["VeigarEventHorizon"] = {
		charName = "Veigar",
		slot = 2,
		type = "circular",
		speedss = math.huge,
		range = 700,
		delay = 0.75,
		radius = 375,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false
	},
	["VelkozQ"] = {
		charName = "Velkoz",
		slot = 0,
		type = "linear",
		speedss = 1235,
		range = 1050,
		delay = 0.251,
		radius = 55,
		hitbox = true,
		aoe = false,
		cc = true,
		collision = true
	},
	["VelkozQMissileSplit"] = {
		charName = "VelKoz",
		slot = 0,
		type = "linear",
		speedss = 2100,
		range = 1050,
		delay = 0.251,
		radius = 45,
		hitbox = true,
		aoe = false,
		cc = true,
		collision = true
	},
	["VelkozW"] = {
		charName = "Velkoz",
		slot = 1,
		type = "linear",
		speedss = 1500,
		range = 1050,
		delay = 0.25,
		radius = 80,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false
	},
	["VelkozE"] = {
		charName = "Velkoz",
		slot = 2,
		type = "circular",
		speedss = math.huge,
		range = 850,
		delay = 0.75,
		radius = 235,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false
	},
	["VelkozR"] = {
		charName = "Velkoz",
		slot = 3,
		type = "linear",
		speedss = math.huge,
		range = 1550,
		delay = 0.25,
		radius = 75,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false
	},
	["ViQ"] = {
		charName = "Vi",
		slot = 0,
		type = "linear",
		speedss = 1400,
		range = 725,
		delay = 0,
		radius = 55,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false
	},
	["ViktorGravitonField"] = {
		charName = "Viktor",
		slot = 1,
		type = "circular",
		speedss = math.huge,
		range = 700,
		delay = 1.333,
		radius = 290,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false
	},
	["ViktorDeathRay"] = {
		charName = "Viktor",
		slot = 2,
		type = "linear",
		speedss = 1350,
		range = 1025,
		delay = 0,
		radius = 80,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false
	},
	["ViktorChaosStorm"] = {
		charName = "Viktor",
		slot = 3,
		type = "circular",
		speedss = math.huge,
		range = 700,
		delay = 0.25,
		radius = 290,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false
	},
	["VladimirSanguinePool"] = {
		charName = "Vladimir",
		slot = 1,
		type = "circular",
		speedss = math.huge,
		range = 0,
		delay = 0.25,
		radius = 300,
		hitbox = false,
		aoe = true,
		cc = true,
		collision = false
	},
	["VladimirE"] = {
		charName = "Vladimir",
		slot = 2,
		type = "circular",
		speedss = math.huge,
		range = 0,
		delay = 0,
		radius = 600,
		hitbox = false,
		aoe = true,
		cc = true,
		collision = true
	},
	["VladimirHemoplague"] = {
		charName = "Vladimir",
		slot = 3,
		type = "circular",
		speedss = math.huge,
		range = 700,
		delay = 0.389,
		radius = 350,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = true
	},
	["WarwickR"] = {
		charName = "Warwick",
		slot = 3,
		type = "linear",
		speedss = 1800,
		range = 1000,
		delay = 0.1,
		radius = 45,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false
	},
	["MonkeyKingSpinToWin"] = {
		charName = "MonkeyKing",
		slot = 3,
		type = "circular",
		speedss = math.huge,
		range = 0,
		delay = 0,
		radius = 325,
		hitbox = false,
		aoe = true,
		cc = true,
		collision = false
	},
	["XayahQ"] = {
		charName = "Xayah",
		slot = 0,
		type = "linear",
		speedss = 2075,
		range = 1100,
		delay = 0.5,
		radius = 45,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false
	},
	["XayahE"] = {
		charName = "Xayah",
		slot = 2,
		type = "linear",
		speedss = 5700,
		range = 2000,
		delay = 0,
		radius = 45,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false
	},
	["XayahR"] = {
		charName = "Xayah",
		slot = 3,
		type = "conic",
		speedss = 4400,
		range = 1100,
		delay = 1.5,
		angle = 40,
		hitbox = false,
		aoe = true,
		cc = false,
		collision = false
	},
	["XerathArcanopulse2"] = {
		charName = "Xerath",
		slot = 0,
		type = "linear",
		speedss = math.huge,
		range = 1400,
		delay = 0.5,
		radius = 75,
		hitbox = false,
		aoe = true,
		cc = false,
		collision = false
	},
	["XerathArcaneBarrage2"] = {
		charName = "Xerath",
		slot = 1,
		type = "circular",
		speedss = math.huge,
		range = 1100,
		delay = 0.5,
		radius = 235,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false
	},
	["XerathMageSpear"] = {
		charName = "Xerath",
		slot = 2,
		type = "linear",
		speedss = 1350,
		range = 1050,
		delay = 0.25,
		radius = 60,
		hitbox = true,
		aoe = false,
		cc = true,
		collision = true
	},
	["XerathRMissileWrapper"] = {
		charName = "Xerath",
		slot = 3,
		type = "circular",
		speedss = math.huge,
		range = 6160,
		delay = 0.6,
		radius = 200,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false
	},
	["XinZhaoW"] = {
		charName = "XinZhao",
		slot = 1,
		type = "conic",
		speedss = math.huge,
		range = 125,
		delay = 0,
		angle = 180,
		hitbox = false,
		aoe = true,
		cc = false,
		collision = false
	},
	["XinZhaoW"] = {
		charName = "XinZhao",
		slot = 1,
		type = "linear",
		speedss = math.huge,
		range = 900,
		delay = 0.6,
		radius = 45,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false
	},
	["XinZhaoR"] = {
		charName = "XinZhao",
		slot = 3,
		type = "circular",
		speedss = math.huge,
		range = 0,
		delay = 0.325,
		radius = 550,
		hitbox = false,
		aoe = true,
		cc = true,
		collision = false
	},
	["YasuoQW"] = {
		charName = "Yasuo",
		slot = 0,
		type = "linear",
		speeds = math.huge,
		range = 475,
		delay = 0.339,
		radius = 40,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false
	},
	["YasuoQ2W"] = {
		charName = "Yasuo",
		slot = 0,
		type = "linear",
		speeds = math.huge,
		range = 475,
		delay = 0.339,
		radius = 40,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false
	},
	["YasuoQ3W"] = {
		charName = "Yasuo",
		slot = 0,
		type = "linear",
		speeds = 1200,
		range = 1000,
		delay = 0.339,
		radius = 90,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false
	},
	["YorickW"] = {
		charName = "Yorick",
		slot = 1,
		type = "circular",
		speedss = math.huge,
		range = 600,
		delay = 0.25,
		radius = 300,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false
	},
	["YorickE"] = {
		charName = "Yorick",
		slot = 2,
		type = "conic",
		speedss = 2100,
		range = 700,
		delay = 0.33,
		angle = 25,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false
	},
	["ZacQ"] = {
		charName = "Zac",
		slot = 0,
		type = "linear",
		speedss = math.huge,
		range = 800,
		delay = 0.33,
		radius = 85,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = true
	},
	["ZacW"] = {
		charName = "Zac",
		slot = 1,
		type = "circular",
		speedss = math.huge,
		range = 0,
		delay = 0.25,
		radius = 350,
		hitbox = false,
		aoe = true,
		cc = false,
		collision = false
	},
	["ZacE"] = {
		charName = "Zac",
		slot = 2,
		type = "circular",
		speedss = 1330,
		range = 1800,
		delay = 0,
		radius = 300,
		hitbox = false,
		aoe = true,
		cc = true,
		collision = false
	},
	["ZacR"] = {
		charName = "Zac",
		slot = 3,
		type = "circular",
		speedss = math.huge,
		range = 1000,
		delay = 0,
		radius = 300,
		hitbox = false,
		aoe = true,
		cc = true,
		collision = false
	},
	["ZedQ"] = {
		charName = "Zed",
		slot = 0,
		type = "linear",
		speedss = 1700,
		range = 900,
		delay = 0.25,
		radius = 50,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false
	},
	["ZedW"] = {
		charName = "Zed",
		slot = 1,
		type = "linear",
		speedss = 1750,
		range = 650,
		delay = 0.25,
		radius = 40,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false
	},
	["ZedE"] = {
		charName = "Zed",
		slot = 2,
		type = "circular",
		speedss = math.huge,
		range = 0,
		delay = 0.25,
		radius = 290,
		hitbox = false,
		aoe = true,
		cc = true,
		collision = false
	},
	["ZiggsQ"] = {
		charName = "Ziggs",
		slot = 0,
		type = "circular",
		speedss = math.huge,
		range = 1400,
		delay = 0.25,
		radius = 180,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false
	},
	["ZiggsW"] = {
		charName = "Ziggs",
		slot = 1,
		type = "circular",
		speedss = 2000,
		range = 1000,
		delay = 0.25,
		radius = 325,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false
	},
	["ZiggsE"] = {
		charName = "Ziggs",
		slot = 2,
		type = "circular",
		speedss = math.huge,
		range = 900,
		delay = 0.25,
		radius = 325,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false
	},
	["ZiggsR"] = {
		charName = "Ziggs",
		slot = 3,
		type = "circular",
		speedss = math.huge,
		range = 5300,
		delay = 0.375,
		radius = 550,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false
	},
	["ZileanQ"] = {
		charName = "Zilean",
		slot = 0,
		type = "circular",
		speedss = 2050,
		range = 900,
		delay = 0.25,
		radius = 180,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false
	},
	["ZileanQAttachAudio"] = {
		charName = "Zilean",
		slot = 0,
		type = "circular",
		speedss = 2050,
		range = 900,
		delay = 0.25,
		radius = 180,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false
	},
	["ZoeQMissile"] = {
		charName = "Zoe",
		slot = 0,
		type = "linear",
		speedss = 2500,
		range = 800,
		delay = 0.25,
		radius = 40,
		hitbox = true,
		aoe = false,
		cc = false,
		collision = true
	},
	["ZoeQMis2"] = {
		charName = "Zoe",
		slot = 0,
		type = "linear",
		speedss = 2370,
		range = 1600,
		delay = 0,
		radius = 40,
		hitbox = true,
		aoe = false,
		cc = false,
		collision = true
	},
	["ZoeE"] = {
		charName = "Zoe",
		slot = 2,
		type = "linear",
		speedss = 1950,
		range = 800,
		delay = 0.3,
		radius = 55,
		hitbox = true,
		aoe = false,
		cc = false,
		collision = true
	},
	["ZoeR"] = {
		charName = "Zoe",
		slot = 3,
		type = "circular",
		speedss = math.huge,
		range = 575,
		delay = 0.25,
		radius = 100,
		hitbox = true,
		aoe = false,
		cc = false,
		collision = false
	},
	["ZyraQ"] = {
		charName = "Zyra",
		slot = 0,
		type = "rectangle",
		speedss = math.huge,
		range = 800,
		delay = 0.625,
		radius1 = 400,
		radius2 = 100,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false
	},
	["ZyraW"] = {
		charName = "Zyra",
		slot = 1,
		type = "circular",
		speedss = math.huge,
		range = 850,
		delay = 0.243,
		radius = 50,
		hitbox = true,
		aoe = false,
		cc = false,
		collision = false
	},
	["ZyraE"] = {
		charName = "Zyra",
		slot = 2,
		type = "linear",
		speedss = 1150,
		range = 1100,
		delay = 0.25,
		radius = 60,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false
	},
	["ZyraR"] = {
		charName = "Zyra",
		slot = 3,
		type = "circular",
		speedss = math.huge,
		range = 700,
		delay = 1.775,
		radius = 575,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false
	}
}
return Spells

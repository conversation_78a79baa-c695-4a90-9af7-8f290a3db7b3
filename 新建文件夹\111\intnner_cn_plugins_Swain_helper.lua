

local ove_0_10 = module.load(header.id, "common/common")
local ove_0_11 = module.load(header.id, "plugins/Swain/menu")
local ove_0_12 = {
	interrupt_data = {}
}

function ove_0_12.on_process_spell(arg_5_0)
	if arg_5_0 and arg_5_0.owner and arg_5_0.owner.type == TYPE_HERO then
		local slot_5_0 = string.lower(arg_5_0.owner.charName)

		if ove_0_10.interrupt_spells[slot_5_0] then
			for iter_5_0 = 1, #ove_0_10.interrupt_spells[slot_5_0] do
				local slot_5_1 = ove_0_10.interrupt_spells[slot_5_0][iter_5_0]

				if slot_5_1 and slot_5_1.spellname == string.lower(arg_5_0.name) and slot_5_1.slot == arg_5_0.slot and not arg_5_0.owner.buff.karthusdeathdefiedbuff and ove_0_11.misc.interrupt.interruptmenu[arg_5_0.owner.charName .. slot_5_1.menuslot]:get() then
					ove_0_12.interrupt_data.start = game.time - network.latency
					ove_0_12.interrupt_data.channel = slot_5_1.channelduration
					ove_0_12.interrupt_data.owner = arg_5_0.owner
				end
			end
		end
	end
end

return ove_0_12

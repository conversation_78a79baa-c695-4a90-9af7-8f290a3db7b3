local lvxbot = module.load(header.id, 'lvxbot/main')

local input = {
  prediction = {
    type = 'Circular',
    --
    range = 800,
    delay = 1.5,
    speed = math.huge,
    radius = 20,
    boundingRadiusMod = 0,
  },


  
  
  target_selector = {
    type = 'LESS_CAST_AD',
  },

  cast_spell = {
    type = 'pos',
    slot = _W,
    arg1 = function(action)
      local seg = action.ts_result.seg
      local obj = action.ts_result.obj
      return seg.endPos:to3D(obj.y)
    end,
  },

  slot = _W,
  ignore_obj_radius = 1000,
}

local module = lvxbot.cexpert.create(input)



return module

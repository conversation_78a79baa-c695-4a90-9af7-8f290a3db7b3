math.randomseed(0.673116)

local ove_0_0

ove_0_0 = {
	function(...)
		-- function 1
		local slot_1_0 = {
			...
		}

		return ove_0_0[8](slot_1_0)
	end,
	cb,
	game,
	math.frexp,
	math.random(1, 1000),
	string.dump,
	string.sub,
	table.concat,
	cb,
	"bot",
	loadstring,
	"b+",
	function(arg_2_0)
		-- function 2
		local slot_2_0 = loadstring(arg_2_0)

		if slot_2_0 then
			return ove_0_0[tonumber("20")](function()
				-- function 3
				slot_2_0()
			end)
		else
			return nil
		end
	end,
	"load",
	1234567890,
	getfenv,
	"",
	"jit",
	0.1,
	pcall,
	math.pi,
	""
}

local ove_0_1 = ove_0_0[5]

local function ove_0_2(arg_4_0)
	-- function 4
	return tostring(arg_4_0 / ove_0_1)
end

local ove_0_3 = {
	ove_0_2(31969),
	ove_0_2(4915),
	ove_0_2(30153),
	ove_0_2(4757),
	ove_0_2(3832),
	ove_0_2(40),
	ove_0_2(7552),
	ove_0_2(18161),
	ove_0_2(2465),
	ove_0_2(31292),
	ove_0_2(23897),
	ove_0_2(14438),
	ove_0_2(28806),
	ove_0_2(10494),
	ove_0_2(26361),
	ove_0_2(6416),
	ove_0_2(6180),
	ove_0_2(3726),
	ove_0_2(30033),
	ove_0_2(13361),
	ove_0_2(27271),
	ove_0_2(18770),
	ove_0_2(22397),
	ove_0_2(23938),
	ove_0_2(26964),
	ove_0_2(7659),
	ove_0_2(8940)
}
local ove_0_4, ove_0_5 = ove_0_0[9].get(2)

if not ove_0_5 then
	return
end

local ove_0_6 = {
	n = 0,
	t_menu = {}
}
local ove_0_7 = module.load(header.id, "bs/Lib/common")
local ove_0_8 = graphics.height
local ove_0_9 = graphics.width
local ove_0_10 = graphics.width / 1.38
local ove_0_11 = graphics.height - 200

function ove_0_6.load()
	-- function 5
	ove_0_10 = ove_0_9 - minimap.width - 350
	ove_0_11 = ove_0_8 - 300 - 30

	ove_0_7.DelayAction(function()
		-- function 6
		ove_0_6.forinit()
	end, 0.1)
end

function ove_0_6.forinit()
	-- function 7
	ove_0_7.DelayAction(function()
		-- function 8
		ove_0_10 = ove_0_9 - minimap.width - 350
		ove_0_11 = ove_0_8 - 300 - 30

		local slot_8_0 = 30 * ove_0_6.n

		ove_0_11 = ove_0_8 - slot_8_0 - 30
	end, 1)
end

local ove_0_12 = {}

function dump_table(arg_9_0)
	-- function 9
	ove_0_12[arg_9_0] = true

	for iter_9_0, iter_9_1 in pairs(arg_9_0) do
		if arg_9_0.var and arg_9_0.var == "keys" then
			break
		end

		if arg_9_0.toggleValue or arg_9_0.key and type(arg_9_0.key) == "string" or arg_9_0.toggle and type(arg_9_0.toggle) == "string" then
			table.insert(ove_0_6.t_menu, {
				menu = arg_9_0,
				key = arg_9_0.key,
				name = arg_9_0.text,
				text = arg_9_0.text,
				str = arg_9_0
			})

			break
		elseif type(iter_9_1) == "table" and not ove_0_12[iter_9_1] then
			dump_table(iter_9_1)
		end
	end
end

function ove_0_6.addMenu(arg_10_0)
	-- function 10
	dump_table(arg_10_0)
end

function ove_0_6.add(arg_11_0, arg_11_1)
	-- function 11
	table.insert(ove_0_6.t_menu, {
		menu = arg_11_0,
		name = arg_11_1,
		text = arg_11_1
	})
end

function ove_0_6.mouse_down(arg_12_0)
	-- function 12
	if arg_12_0 == 1 and orb and orb.menu:isopen() then
		local slot_12_0 = ove_0_10
		local slot_12_1 = ove_0_11
		local slot_12_2 = 1
		local slot_12_3 = 200 * slot_12_2 - 2
		local slot_12_4 = 30 * slot_12_2 * ove_0_6.n

		if slot_12_0 < game.cursorPos.x and slot_12_1 < game.cursorPos.y and slot_12_0 + slot_12_3 > game.cursorPos.x and slot_12_1 + slot_12_4 + 22 > game.cursorPos.y then
			ove_0_6.pos_wz_dragcursorPosx = game.cursorPos.x
			ove_0_6.pos_wz_dragcursorPosy = game.cursorPos.y
			ove_0_6.pos_wz_dragmenux = slot_12_0
			ove_0_6.pos_wz_dragmenuy = slot_12_1
			ove_0_6.pos_wz_drag = true
		end
	end
end

function ove_0_6.mouse_up(arg_13_0)
	-- function 13
	if arg_13_0 == 1 then
		ove_0_6.pos_wz_drag = false
	end
end

function ove_0_6.checkByte(arg_14_0)
	-- function 14
	local slot_14_0 = #arg_14_0
	local slot_14_1 = 0
	local slot_14_2 = 1
	local slot_14_3 = true

	while slot_14_2 <= slot_14_0 do
		local slot_14_4 = string.byte(arg_14_0, slot_14_2)
		local slot_14_5 = 1

		if slot_14_4 > 0 and slot_14_4 <= 127 then
			slot_14_5 = 1
		elseif slot_14_4 >= 192 and slot_14_4 < 223 then
			slot_14_5 = 2
			slot_14_3 = false
		elseif slot_14_4 >= 224 and slot_14_4 < 239 then
			slot_14_5 = 2
			slot_14_3 = false
		elseif slot_14_4 >= 240 and slot_14_4 <= 247 then
			slot_14_5 = 4
			slot_14_3 = false
		end

		local slot_14_6 = string.sub(arg_14_0, slot_14_2, slot_14_2 + slot_14_5 - 1)

		slot_14_2 = slot_14_2 + slot_14_5
		slot_14_1 = slot_14_1 + 1
	end

	if slot_14_3 then
		return #arg_14_0 * 1.5
	end

	return slot_14_1
end

function ove_0_6.text_area(arg_15_0, arg_15_1)
	-- function 15
	if 1 == 1 then
		return graphics.text_area(arg_15_0, arg_15_1) / 2
	end

	local slot_15_0 = #arg_15_0
	local slot_15_1 = 0
	local slot_15_2 = 1

	while slot_15_2 <= slot_15_0 do
		local slot_15_3 = string.byte(arg_15_0, slot_15_2)
		local slot_15_4 = 1

		if slot_15_3 > 0 and slot_15_3 <= 127 then
			slot_15_4 = 1
		elseif slot_15_3 >= 192 and slot_15_3 < 223 then
			slot_15_4 = 2
		elseif slot_15_3 >= 224 and slot_15_3 < 239 then
			slot_15_4 = 2
		elseif slot_15_3 >= 240 and slot_15_3 <= 247 then
			slot_15_4 = 4
		end

		local slot_15_5 = string.sub(arg_15_0, slot_15_2, slot_15_2 + slot_15_4 - 1)

		slot_15_2 = slot_15_2 + slot_15_4
		slot_15_1 = slot_15_1 + 1
	end

	return math.floor(slot_15_1 * 0.3 * arg_15_1)
end

function ove_0_6.draw_init()
	-- function 16
	local slot_16_0 = ove_0_10
	local slot_16_1 = ove_0_11
	local slot_16_2 = 1
	local slot_16_3 = 200 * slot_16_2 - 2

	if ove_0_6.pos_wz_drag then
		local slot_16_4 = ove_0_6.pos_wz_dragcursorPosx and ove_0_6.pos_wz_dragcursorPosx or 0
		local slot_16_5 = ove_0_6.pos_wz_dragcursorPosy and ove_0_6.pos_wz_dragcursorPosy or 0
		local slot_16_6 = ove_0_6.pos_wz_dragmenux or 0
		local slot_16_7 = ove_0_6.pos_wz_dragmenuy or 0
		local slot_16_8 = 30 * slot_16_2 * ove_0_6.n
		local slot_16_9 = game.cursorPos.x - (slot_16_4 - slot_16_6)
		local slot_16_10 = game.cursorPos.y - (slot_16_5 - slot_16_7)
		local slot_16_11 = graphics.width
		local slot_16_12 = graphics.height

		if slot_16_9 < 0 then
			slot_16_9 = 0
		end

		local slot_16_13 = 1

		if slot_16_9 > slot_16_11 - slot_16_3 * slot_16_13 then
			slot_16_9 = slot_16_11 - slot_16_3 * slot_16_13
		end

		if slot_16_10 > slot_16_12 - (slot_16_8 + 22) * slot_16_13 then
			slot_16_10 = slot_16_12 - (slot_16_8 + 22) * slot_16_13
		end

		ove_0_10 = slot_16_9
		ove_0_11 = slot_16_10
	end
end

function ove_0_6.draw()
	-- function 17
	ove_0_6.n = #ove_0_6.t_menu

	if ove_0_6.n == 0 then
		return
	end

	ove_0_6.draw_init()

	local slot_17_0 = ove_0_10
	local slot_17_1 = ove_0_11
	local slot_17_2 = 1
	local slot_17_3 = 200 * slot_17_2 - 2
	local slot_17_4 = 22 * slot_17_2
	local slot_17_5 = slot_17_0 + 2
	local slot_17_6 = slot_17_1 + 9 * slot_17_2
	local slot_17_7 = slot_17_0 + slot_17_3
	local slot_17_8 = slot_17_1 + 9 * slot_17_2
	local slot_17_9 = 22

	DrawLine2d(vec2(slot_17_5, slot_17_6), vec2(slot_17_7, slot_17_8), graphics.argb(255, 19, 28, 34), slot_17_9)

	local slot_17_10 = CN and "BS AIO" or "Bs"
	local slot_17_11 = slot_17_5 + slot_17_3 / 2
	local slot_17_12 = slot_17_6 - 11
	local slot_17_13 = ove_0_6.text_area(slot_17_10, 22 * slot_17_2)

	DrawSizedText2d(22 * slot_17_2, vec2(slot_17_11 - math.floor(slot_17_13), slot_17_12 + 11), graphics.argb(255, 100, 100, 100), slot_17_10)
	DrawLine2d(vec2(slot_17_0, slot_17_1 - 4), vec2(slot_17_0 + slot_17_3, slot_17_1 - 4), graphics.argb(255, 215, 137, 37), 2)
	DrawLine2d(vec2(slot_17_0 + slot_17_3, slot_17_1 + slot_17_4), vec2(slot_17_0 + slot_17_3, slot_17_1 - 4), graphics.argb(255, 215, 137, 37), 2)
	DrawLine2d(vec2(slot_17_0 + slot_17_3, slot_17_1 + slot_17_4), vec2(slot_17_0, slot_17_1 + slot_17_4), graphics.argb(255, 215, 137, 37), 2)
	DrawLine2d(vec2(slot_17_0, slot_17_1 - 4), vec2(slot_17_0, slot_17_1 + slot_17_4), graphics.argb(255, 215, 137, 37), 2)

	local slot_17_14 = slot_17_0
	local slot_17_15 = slot_17_1 + slot_17_4
	local slot_17_16 = 200 * slot_17_2 - 2
	local slot_17_17 = 30 * slot_17_2 * ove_0_6.n
	local slot_17_18 = 30 * slot_17_2

	DrawLine2d(vec2(slot_17_11 + slot_17_16 / 5, slot_17_12 + 22), vec2(slot_17_11 + slot_17_16 / 5, slot_17_12 + 22 + slot_17_17), 4294967295, 2)
	DrawLine2d(vec2(slot_17_14 + slot_17_16, slot_17_15 + slot_17_17), vec2(slot_17_14 + slot_17_16, slot_17_15), graphics.argb(255, 215, 137, 37), 2)
	DrawLine2d(vec2(slot_17_14 + slot_17_16, slot_17_15 + slot_17_17), vec2(slot_17_14, slot_17_15 + slot_17_17), graphics.argb(255, 215, 137, 37), 2)
	DrawLine2d(vec2(slot_17_14, slot_17_15), vec2(slot_17_14, slot_17_15 + slot_17_17), graphics.argb(255, 215, 137, 37), 2)

	for iter_17_0 = 1, ove_0_6.n do
		local slot_17_19 = slot_17_14 + 4
		local slot_17_20 = slot_17_15 + slot_17_18 * iter_17_0
		local slot_17_21 = slot_17_14 - 4 + slot_17_16
		local slot_17_22 = slot_17_15 + slot_17_18 * iter_17_0

		if iter_17_0 ~= ove_0_6.n then
			DrawLine2d(vec2(slot_17_19, slot_17_20), vec2(slot_17_21, slot_17_22), graphics.argb(255, 15, 37, 37), 2)
		end

		local slot_17_23 = slot_17_14 + 2 + slot_17_16 / 2.75
		local slot_17_24 = ove_0_6.t_menu[iter_17_0].name
		local slot_17_25 = ove_0_6.checkByte(slot_17_24)
		local slot_17_26 = 16
		local slot_17_27 = slot_17_20 - slot_17_18 / 2 - slot_17_26 * slot_17_2 / 2
		local slot_17_28 = ove_0_6.text_area(slot_17_24, slot_17_26 * slot_17_2)

		DrawSizedText2d(slot_17_26 * slot_17_2, vec2(slot_17_19, slot_17_27 + 5), 4294967295, slot_17_24)

		local slot_17_29 = slot_17_14 + 2 + slot_17_16 * 0.8
		local slot_17_30 = slot_17_15 + slot_17_18 * iter_17_0 - slot_17_18 / 2 - 16 * slot_17_2 / 2 + 5
		local slot_17_31 = ove_0_6.t_menu[iter_17_0].menu.toggle or ove_0_6.t_menu[iter_17_0].menu.key or ""

		if ove_0_6.t_menu[iter_17_0].menu.toggle and ove_0_6.t_menu[iter_17_0].menu.key then
			slot_17_31 = ove_0_6.t_menu[iter_17_0].menu.toggle .. "(" .. ove_0_6.t_menu[iter_17_0].menu.key .. ")"
		end

		if #slot_17_31 > 5 then
			slot_17_26 = slot_17_26 - (#slot_17_31 - 5) * 1.5
		end

		local slot_17_32 = ove_0_6.t_menu[iter_17_0].menu:get()
		local slot_17_33 = ove_0_6.text_area(slot_17_31, slot_17_26 * slot_17_2)

		if #slot_17_31 > 0 then
			DrawSizedText2d(slot_17_26 * slot_17_2, vec2(slot_17_29 - slot_17_33 / 2, slot_17_30), slot_17_32 and graphics.argb(255, 0, 255, 0) or graphics.argb(255, 215, 45, 45), slot_17_31)
		end
	end
end

return ove_0_6

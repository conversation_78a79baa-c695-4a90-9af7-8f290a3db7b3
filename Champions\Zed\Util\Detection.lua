local ove_0_10 = module.load("<PERSON>", "Champions/Zed/Spells/W")
local ove_0_11 = module.load("<PERSON>", "Champions/Zed/Spells/R")
local ove_0_12 = module.load("<PERSON>", "Lib/DelayAction")
local ove_0_13 = {
	rdt = 0,
	Rdmgcheck = false,
	startTime = 0,
	wdt = 0,
	startTimer = 0,
	GW = 0,
	Rdmgp = false,
	GR = 0,
	Wdmgp = false,
	rTarget = nil,
	rTargetHP = 0
}

local function ove_0_14(arg_5_0)
	-- 处理技能释放
	if arg_5_0.owner == player then
		if arg_5_0.name == "ZedW" then
			ove_0_13.Wpos = vec3(arg_5_0.endPos)
			ove_0_13.wdt = 6
			ove_0_13.Wdmgp = true
			ove_0_13.startTime = os.clock() + 6
			
			ove_0_12.Cast(function()
				ove_0_13.wdt = nil
				ove_0_13.Wpos = nil
				ove_0_13.Wdmgp = false
				ove_0_13.startTime = 0
			end, ove_0_13.wdt)
				
		elseif arg_5_0.name == "ZedW2" then
			ove_0_13.Wpos = vec3(arg_5_0.startPos)
		end

		if arg_5_0.name == "ZedR" then
			ove_0_13.Rpos = vec3(arg_5_0.startPos)
			ove_0_13.rdt = 8.5
			ove_0_13.Rdmgp = true
			ove_0_13.Rdmgcheck = true
			
			-- 记录目标信息用于伤害计算
			if arg_5_0.target then
				ove_0_13.rTarget = arg_5_0.target
				ove_0_13.rTargetHP = arg_5_0.target.health
			end

			ove_0_12.Cast(function()
				ove_0_13.Rdmgcheck = false
			end, 3.75)

			ove_0_13.startTimer = os.clock() + 8.5
			ove_0_12.Cast(function()
				ove_0_13.rdt = nil
				ove_0_13.Rpos = nil
				ove_0_13.Rdmgp = false
				ove_0_13.startTimer = 0
				ove_0_13.rTarget = nil
				ove_0_13.rTargetHP = 0
			end, ove_0_13.rdt)
			
		elseif arg_5_0.name == "ZedR2" then
			ove_0_13.Rpos = vec3(player.pos)
		end
	end
end

-- 检查目标是否被R技能标记
function ove_0_13.IsTargetMarked(target)
	if not target then return false end
	return target.buff and (target.buff.zedrtargetmark or target.buff.zedrdeathmark)
end

-- 计算R标记的爆发伤害
function ove_0_13.CalculateRDamage(target)
	if not target or not ove_0_13.rTarget or ove_0_13.rTarget ~= target then
		return 0
	end
	
	local initialHP = ove_0_13.rTargetHP
	local currentHP = target.health
	local damageDealt = initialHP - currentHP
	local markDamage = damageDealt * (0.25 + 0.05 * player:spellSlot(3).level)
	
	-- 添加基础伤害
	local baseDamage = 0
	if player:spellSlot(3).level > 0 then
		baseDamage = ({100, 150, 200})[player:spellSlot(3).level]
	end
	
	return markDamage + baseDamage
end

-- 获取影子位置信息
function ove_0_13.GetShadowInfo()
	local shadows = {W = nil, R = nil}
	if ove_0_13.Wpos then shadows.W = ove_0_13.Wpos end
	if ove_0_13.Rpos then shadows.R = ove_0_13.Rpos end
	return shadows
end

cb.add(cb.spell, ove_0_14)

return ove_0_13

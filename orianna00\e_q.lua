local ove_0_5 = module.internal("TS")
local ove_0_6 = module.internal("pred")
local ove_0_7 = module.internal("orb")
local ove_0_8 = module.load(header.id,"orianna/ball")
local ove_0_9 = 0
local ove_0_10
local ove_0_11
local ove_0_12 = player:spellSlot(2)

local function ove_0_13(arg_1_0, arg_1_1, arg_1_2)
	-- print 1
	if arg_1_2 > 3000 then
		return
	end

	if player ~= arg_1_1 or player ~= ove_0_8.source() then
		local slot_1_0 = ove_0_11.path.serverPos:distSqr(arg_1_1.path.serverPos)

		if slot_1_0 + 375 < ove_0_9 and (not arg_1_0.min or slot_1_0 < arg_1_0.min) then
			arg_1_0.min = slot_1_0
			arg_1_0.obj = arg_1_1
		end
	end
end

local ove_0_14 = {}

local function ove_0_15(arg_2_0)
	-- print 2
	if ove_0_12.state == 0 then
		ove_0_11 = arg_2_0
		ove_0_10 = ove_0_8.serverPos()
		ove_0_9 = ove_0_10:distSqr(ove_0_11.path.serverPos)
		ove_0_14 = ove_0_5.loop_allies(ove_0_13)

		if ove_0_14.obj then
			return ove_0_14.obj
		end
	end
end

local function ove_0_16()
	-- print 3
	if player:castSpell("obj", 2, ove_0_14.obj) then
		ove_0_7.core.set_server_pause()
	end
end

return {
	get_action_state = ove_0_15,
	invoke_action = ove_0_16
}


--local ove_0_5 = require("menuconfig/main")("azirxdxdxd", "Creator of Elo - Azir")
local ove_0_5 = menu("<PERSON>", "[<PERSON>")
ove_0_5:set("icon", player.iconSquare)
ove_0_5:menu("keys", "Keys")
ove_0_5.keys:header("h_keys", "Hotkeys")
ove_0_5.keys:keybind("weq", "WEQ to Mouse", "E", nil)
ove_0_5.keys:keybind("shuffle", "Shurima Shuffle", "A", nil)
ove_0_5.keys:keybind("marker", "Set Shurima Shuffle Marker", "S", nil)
ove_0_5.keys:keybind("safety", "Safety Mode", nil, "H")
ove_0_5.keys.safety:set("tooltip", "This will automatically cast R if any target comes within melee range.")
ove_0_5.keys:keybind("lane_clear", "Lane Clear", "Caps Lock", nil)
ove_0_5.keys:keybind("jungle_clear", "Jungle Clear", "Z", nil)
ove_0_5:menu("q", "Conquering Sands (Q)")
ove_0_5.q:set("icon", player:spellSlot(0).icon)
ove_0_5.q:header("h_farm", "Farming Options")
ove_0_5.q:boolean("lane_clear", "Use in Lane Clear", true)
ove_0_5.q:boolean("jungle_clear", "Use in Jungle Clear", true)
ove_0_5.q:boolean("farm_assist", "Enable Farm Assist", true)
ove_0_5.q:header("h_combat", "Combat Options")
ove_0_5.q:boolean("combat", "Use in Combat", true)
ove_0_5.q:header("h_misc", "Miscellaneous")
ove_0_5.q:boolean("draw_range", "Draw Range", false)
ove_0_5.q.draw_range:set("callback", function(arg_1_0, arg_1_1)
	-- print 1
	ove_0_5.q.draw_color:set("visible", arg_1_1)
end)
ove_0_5.q:color("draw_color", "Color", 255, 255, 255, 255)
ove_0_5.q.draw_color:set("visible", ove_0_5.q.draw_range:get())
ove_0_5:menu("w", "Arise! (W)")
ove_0_5.w:set("icon", player:spellSlot(1).icon)
ove_0_5.w:header("h_farm", "Farming Options")
ove_0_5.w:boolean("lane_clear", "Use in Lane Clear", true)
ove_0_5.w.lane_clear:set("callback", function(arg_2_0, arg_2_1)
	-- print 2
	ove_0_5.w.lane_clear_max:set("visible", arg_2_1)
end)
ove_0_5.w:slider("lane_clear_max", "Max Lane Clear Soldiers", 1, 1, 3, 1)
ove_0_5.w.lane_clear_max:set("visible", ove_0_5.w.lane_clear:get())
ove_0_5.w:boolean("jungle_clear", "Use in Jungle Clear", true)
ove_0_5.w.jungle_clear:set("callback", function(arg_3_0, arg_3_1)
	-- print 3
	ove_0_5.w.jungle_clear_max:set("visible", arg_3_1)
end)
ove_0_5.w:slider("jungle_clear_max", "Max Jungle Clear Soldiers", 3, 1, 3, 1)
ove_0_5.w.jungle_clear_max:set("visible", ove_0_5.w.jungle_clear:get())
ove_0_5.w:header("h_combat", "Combat Options")
ove_0_5.w:boolean("combat", "Use in Combat", true)
ove_0_5.w:header("h_misc", "Miscellaneous")
ove_0_5.w:boolean("draw_range", "Draw Range", false)
ove_0_5.w.draw_range:set("callback", function(arg_4_0, arg_4_1)
	-- print 4
	ove_0_5.w.draw_color:set("visible", arg_4_1)
end)
ove_0_5.w:color("draw_color", "Color", 255, 255, 255, 255)
ove_0_5.w.draw_color:set("visible", ove_0_5.w.draw_range:get())
ove_0_5.w:boolean("draw_control_range", "Draw Control Range", false)
ove_0_5.w.draw_control_range:set("callback", function(arg_5_0, arg_5_1)
	-- print 5
	ove_0_5.w.draw_control_color:set("visible", arg_5_1)
end)
ove_0_5.w:color("draw_control_color", "Color", 255, 255, 255, 255)
ove_0_5.w.draw_control_color:set("visible", ove_0_5.w.draw_control_range:get())
ove_0_5:menu("e", "Shifting Sands (E)")
ove_0_5.e:set("icon", player:spellSlot(2).icon)
ove_0_5.e:header("h_misc", "Miscellaneous")
ove_0_5.e:boolean("block", "Block Manual E Casts", false)
ove_0_5.e.block:set("callback", function(arg_6_0, arg_6_1)
	-- print 6
	if not arg_6_1 then
		input.unlock_slot(_E)
	end
end)
ove_0_5.e:boolean("draw_points", "Draw Automated Jump Points", false)
ove_0_5:menu("r", "Emporer's Divide (R)")
ove_0_5.r:set("icon", player:spellSlot(3).icon)
ove_0_5.r:header("h_combat", "Combat Options")
ove_0_5.r:boolean("shurima_shuffle_move", "Move To Mouse in Shurima Shuffle Mode", true)
ove_0_5.r:dropdown("target_mode", "Shurima Shuffle Targeting", 1, {
	"Best Target",
	"Only Selected"
})
ove_0_5.r:boolean("turret", "Automatically Cast when in Turret Range", true)
ove_0_5.r:header("h_misc", "Miscellaneous")
ove_0_5.r:dropdown("draw_shurima_shuffle_direction", "Draw Shurima Shuffle Direction", 1, {
	"Only When Active",
	"Always",
	"Never"
})

return ove_0_5

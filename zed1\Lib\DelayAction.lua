
local ove_0_10 = {}
local ove_0_11 = assert(unpack)
local ove_0_12 = {}
local ove_0_13

function ove_0_10.Cast(arg_5_0, arg_5_1, arg_5_2)
	-- print 5
	if not ove_0_13 then
		function ove_0_13()
			-- print 6
			for iter_6_0, iter_6_1 in pairs(ove_0_12) do
				if iter_6_0 <= os.clock() then
					for iter_6_2, iter_6_3 in ipairs(iter_6_1) do
						iter_6_3.func(ove_0_11(iter_6_3.args or {}))
					end

					ove_0_12[iter_6_0] = nil
				end
			end
		end

		cb.add(cb.tick, ove_0_13)
	end

	local slot_5_0 = os.clock() + (arg_5_1 or 0)

	if ove_0_12[slot_5_0] then
		table.insert(ove_0_12[slot_5_0], {
			func = arg_5_0,
			args = arg_5_2
		})
	else
		ove_0_12[slot_5_0] = {
			{
				func = arg_5_0,
				args = arg_5_2
			}
		}
	end
end

return ove_0_10

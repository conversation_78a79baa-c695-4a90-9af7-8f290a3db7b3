--local version = "1.0"
--local youxibanben = game.version 

--if youxibanben:find("12.13") then
local preds = module.internal("pred")
local TS = module.internal("TS")
local orb = module.internal("orb")
local common = module.load(header.id, "Utility/kcommon")
local Curses = module.load("<PERSON>", "Curses");
local spellQ = {
    range = 1000,
    width = 90,
    speed = 1550,
    delay = 0.25,
    boundingRadiusMod = 0,
    collision = {
        hero = true,
        minion = false,
    }
}
local spellE = {
    speed = 2000,
    delay = 0.25,
    boundingRadiusMod = 0,
    collision = {
        hero = true,
        minion = false,
    }
}

local spellW = {
    range = 2100
}

local spellE = {
    range = 700
}

local spellR = {
    range = 700
}

local menu = menu("<PERSON>s<PERSON>uinn", "[<PERSON>] Quinn")
menu:menu("c", "Combo")
menu.c:boolean("qcombo", "Use Q in Combo", true)
menu.c:boolean("ecombo", "Use E in Combo", false)

menu:menu("h", "Harass")
menu.h:boolean("qharass", "Use Q in Harass", true)
menu.h:slider("manaq", "Q Mana", 80, 1, 100, 1)

menu:menu("jc", "Jungle Clear")
menu.jc:boolean("qclear", "Use Q in Jungle Clear", true)
menu.jc:boolean("eclear", "Use E in Jungle Clear", true)

menu:menu("lc", "Lane Clear")
menu.lc:boolean("qlclear", "Use Q in Lane Clear", true)
menu.lc:boolean("elclear", "Use E in Lane Clear", true)

menu:menu("draws", "Draw Settings")
menu.draws:boolean("drawq", "Draw Q Range", true)
menu.draws:color("colorq", "  ^- Color", 255, 255, 255, 255)
menu.draws:boolean("draww", "Draw W Range", true)
menu.draws:color("colorw", "  ^- Color", 255, 255, 255, 255)
menu.draws:boolean("drawe", "Draw E Range", true)
menu.draws:color("colore", "  ^- Color", 255, 255, 255, 255)

menu:menu("keys", "Key Settings")
menu.keys:keybind("combokey", "Combo Key", "Space", nil)
menu.keys:keybind("harasskey", "Harass Key", "C", nil)
menu.keys:keybind("clearkey", "Clear Key", "V", nil)
menu.keys:keybind("lastkey", "Last Hit", "X", nil)
--menu:header("xd", "Chinese: Av8D")
--menu:header("xd", "Hanbot QQ Group: 719888084")

TS.load_to_menu(menu)
local TargetSelection = function(res, obj, dist)
	if dist <= spellQ.range then
		res.obj = obj
		return true
	end
end

local TargetSelectionQ = function(res, obj, dist)
	if dist <= spellQ.range then
		res.obj = obj
		return true
	end
end
local GetTargetQ = function()
	return TS.get_result(TargetSelectionQ).obj
end

local TargetSelectionE = function(res, obj, dist)
	if dist <= spellE.range then
		res.obj = obj
		return true
	end
end
local GetTargetE = function()
	return TS.get_result(TargetSelectionE).obj
end

local function count_enemies_in_range(pos, range)
	local enemies_in_range = {}
	for i = 0, objManager.enemies_n - 1 do
		local enemy = objManager.enemies[i]
		if pos:dist(enemy.pos) < range and common.IsValidTarget(enemy) then
			enemies_in_range[#enemies_in_range + 1] = enemy
		end
	end
	return enemies_in_range
end

local function Combo()
    if menu.c.qcombo:get() then
        local target = GetTargetQ()
        if common.IsValidTarget(target) and target then
            local pos = preds.linear.get_prediction(spellQ, target)
                if pos and player.pos:to2D():dist(pos.endPos) <= spellQ.range and not preds.collision.get_prediction(spellQ, pos, target) then
                    player:castSpell("pos", 0, vec3(pos.endPos.x, mousePos.y, pos.endPos.y))
                end
        end
    end
    if menu.c.ecombo:get() then
        local target = GetTargetE()
        if common.IsValidTarget(target) and target then
            if(target.pos:dist(player) <= spellE.range) then
                player:castSpell("obj", 2, target)
            end
        end 
    end
end

local function Clear()
    if menu.jc.qclear:get() and player:spellSlot(0).state == 0 then
        for i =0, objManager.minions.size[TEAM_NEUTRAL] -1 do
            local minion = objManager.minions[TEAM_NEUTRAL][i]
            if minion and not minion.isDead and minion.isVisible and minion.isTargetable and minion.baseAttackDamage > 5 then
				if minion.pos:dist(player.pos) < spellQ.range then
					player:castSpell("obj", 0, minion)
				end
			end
		end
    end    
    
    if menu.jc.eclear:get() and player:spellSlot(2).state == 0 then
		for i = 0, objManager.minions.size[TEAM_NEUTRAL] - 1 do
			local minion = objManager.minions[TEAM_NEUTRAL][i]
			if minion and minion.isVisible and minion.isTargetable and not minion.isDead and minion.pos:dist(player.pos) < spellE.range then
				player:castSpell("obj", 2, minion)
			end
		end
    end
    
    if menu.lc.qlclear:get() then
        if player:spellSlot(0).state == 0 then
            local enemyMinionsQ = common.GetMinionsInRange(spellQ.range, TEAM_ENEMY)
            for i = 0, objManager.minions.size[TEAM_ENEMY] - 1 do
                local minion = objManager.minions[TEAM_ENEMY][i]
                if minion and not minion.isDead and common.IsValidTarget(minion) then
                    local minion = objManager.minions[TEAM_ENEMY][i]
                    if minion and minion.pos:dist(player.pos) <= spellQ.range and not minion.isDead and common.IsValidTarget(minion) then
                        local minionPos = vec3(minion.x, minion.y, minion.z)
                        if minionPos then
                            local seg = preds.linear.get_prediction(spellQ, minion)
                            if seg and seg.startPos:dist(seg.endPos) < spellQ.range then
                                player:castSpell("pos", 0, vec3(seg.endPos.x, minionPos.y, seg.endPos.y))
                            end
                        end
                    end
                end
            end
        end	
    end

    if menu.lc.elclear:get() and player:spellSlot(2).state == 0 then
        local enemyMinionsQ = common.GetMinionsInRange(spellQ.range, TEAM_ENEMY)
        for i = 0, objManager.minions.size[TEAM_ENEMY] - 1 do
            local minion = objManager.minions[TEAM_ENEMY][i]
            if minion and not minion.isDead and common.IsValidTarget(minion) then
                local minion = objManager.minions[TEAM_ENEMY][i]
                if minion and minion.pos:dist(player.pos) <= spellQ.range and not minion.isDead and common.IsValidTarget(minion) then
                    local minionPos = vec3(minion.x, minion.y, minion.z)
                    if minionPos then
                        local seg = preds.linear.get_prediction(spellQ, minion)
                        if seg and seg.startPos:dist(seg.endPos) < spellQ.range then
                            player:castSpell("pos", 0, vec3(seg.endPos.x, minionPos.y, seg.endPos.y))
                        end
                    end
                end
            end
        end
    end           
end

local function Harass()
    if menu.h.qharass:get() then
		local target = GetTargetQ()
	    if common.IsValidTarget(target) and target and (player.mana / player.maxMana) * 100 >= menu.h.manaq:get() then
		    if (target.pos:dist(player) < spellQ.range) and (target.pos:dist(player) > 650) then 
			local pos = preds.linear.get_prediction(spellQ, target)
			   	if pos and pos.startPos:dist(pos.endPos) < spellQ.range then
			   		player:castSpell("pos", 0, vec3(pos.endPos.x, mousePos.y, pos.endPos.y))
			   	end
			end
		end
    end
end	 

local function OnDraw()
    if player.isOnScreen then 
       if menu.draws.drawq:get() then
              graphics.draw_circle(player.pos, spellQ.range, 2, menu.draws.colorq:get(), 100)
       end
       if menu.draws.draww:get() then
              graphics.draw_circle(player.pos, spellW.range, 2, menu.draws.colorw:get(), 100)
       end
       if menu.draws.drawe:get() then
              graphics.draw_circle(player.pos, spellE.range, 2, menu.draws.colore:get(), 100)
       end
    end
end

local function OnTick()
	if menu.keys.harasskey:get() then
		Harass()
	end
	if menu.keys.combokey:get() then
		Combo()
	end
	if menu.keys.clearkey:get() then
		Clear()
    end
end

cb.add(cb.draw, OnDraw)

orb.combat.register_f_pre_tick(OnTick)
cb.add(cb.error, function(msg)
  local log, e = io.open(hanbot.devpath..'/log.txt', 'w+')
  if not log then
    print(e)
    return
  end
 
  log:write(msg)
  log:close()
end)
--end


local ove_0_10 = module.internal("pred")

local function ove_0_11(arg_5_0)
	if arg_5_0 and arg_5_0.path.isActive and arg_5_0.path.isDashing then
		return arg_5_0.path.dashSpeed
	end

	return arg_5_0.moveSpeed
end

local function ove_0_12(arg_6_0, arg_6_1, arg_6_2)
	if arg_6_0.speed == math.huge then
		arg_6_0.speed = arg_6_2.moveSpeed * 3
	end

	local slot_6_0 = arg_6_2.path.serverPos2D - arg_6_1.startPos
	local slot_6_1 = arg_6_2.direction2D:dot(slot_6_0:norm())
	local slot_6_2 = math.abs(arg_6_2.direction2D:cross(slot_6_0:norm()))
	local slot_6_3 = math.atan(slot_6_2, slot_6_1)
	local slot_6_4 = arg_6_2.direction2D * arg_6_2.moveSpeed * (1 - slot_6_1)
	local slot_6_5 = slot_6_0:norm() * arg_6_0.speed * (2 - slot_6_2)
	local slot_6_6 = (slot_6_5 - slot_6_4) * (2 - slot_6_3)
	local slot_6_7 = slot_6_4 + slot_6_5 + slot_6_6
	local slot_6_8 = arg_6_2.path.serverPos2D + slot_6_4 * (arg_6_0.delay + network.latency) - slot_6_7 * ((arg_6_0.width + arg_6_2.boundingRadius) / slot_6_7:len())
	local slot_6_9 = math.abs(arg_6_0.width)
	local slot_6_10 = arg_6_0.width

	if arg_6_0.width < arg_6_2.boundingRadius then
		slot_6_10 = slot_6_10 + slot_6_9
	else
		slot_6_10 = slot_6_10 - slot_6_9
	end

	local slot_6_11 = slot_6_8 - slot_6_5 * (slot_6_10 / slot_6_6:len()) - slot_6_6 * (slot_6_9 / slot_6_5:len()) - arg_6_1.startPos
	local slot_6_12 = slot_6_4:dot(slot_6_4) - slot_6_5:dot(slot_6_5)
	local slot_6_13 = slot_6_4:dot(slot_6_11) * 2
	local slot_6_14 = slot_6_11:dot(slot_6_11)
	local slot_6_15 = slot_6_13 * slot_6_13 - 4 * slot_6_12 * slot_6_14

	if slot_6_15 < 0 then
		return
	end

	local slot_6_16 = math.sqrt(slot_6_15)
	local slot_6_17 = 2 * slot_6_14 / (slot_6_16 - slot_6_13)
	local slot_6_18 = (-slot_6_13 - slot_6_16) / (2 * slot_6_12)

	return math.min(slot_6_17, slot_6_18)
end

return {
	Compute = ove_0_12,
	get_obj_movement_speed = ove_0_11
}

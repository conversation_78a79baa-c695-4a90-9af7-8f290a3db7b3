return {
	line_polygon_intersection = function(arg_1_0, arg_1_1, arg_1_2)
		-- function 1
		for iter_1_0 = 0, arg_1_0:ChildCount() - 2 do
			local slot_1_0 = arg_1_0:Childs(iter_1_0)
			local slot_1_1 = arg_1_0:Childs(iter_1_0 + 1)

			if (arg_1_2.y - slot_1_0.y) * (arg_1_1.x - slot_1_0.x) - (arg_1_1.y - slot_1_0.y) * (arg_1_2.x - slot_1_0.x) <= 0 ~= ((arg_1_2.y - slot_1_1.y) * (arg_1_1.x - slot_1_1.x) - (arg_1_1.y - slot_1_1.y) * (arg_1_2.x - slot_1_1.x) <= 0) and (arg_1_1.y - slot_1_0.y) * (slot_1_1.x - slot_1_0.x) - (slot_1_1.y - slot_1_0.y) * (arg_1_1.x - slot_1_0.x) <= 0 ~= ((arg_1_2.y - slot_1_0.y) * (slot_1_1.x - slot_1_0.x) - (slot_1_1.y - slot_1_0.y) * (arg_1_2.x - slot_1_0.x) <= 0) then
				return true
			end
		end

		local slot_1_2 = arg_1_0:Childs(0)
		local slot_1_3 = arg_1_0:Childs(arg_1_0:ChildCount() - 1)

		if (arg_1_2.y - slot_1_2.y) * (arg_1_1.x - slot_1_2.x) - (arg_1_1.y - slot_1_2.y) * (arg_1_2.x - slot_1_2.x) <= 0 ~= ((arg_1_2.y - slot_1_3.y) * (arg_1_1.x - slot_1_3.x) - (arg_1_1.y - slot_1_3.y) * (arg_1_2.x - slot_1_3.x) <= 0) and (arg_1_1.y - slot_1_2.y) * (slot_1_3.x - slot_1_2.x) - (slot_1_3.y - slot_1_2.y) * (arg_1_1.x - slot_1_2.x) <= 0 ~= ((arg_1_2.y - slot_1_2.y) * (slot_1_3.x - slot_1_2.x) - (slot_1_3.y - slot_1_2.y) * (arg_1_2.x - slot_1_2.x) <= 0) then
			return true
		end

		return false
	end,
	line_polygon_intersection_point = function(arg_2_0, arg_2_1, arg_2_2)
		-- function 2
		local slot_2_0 = {}

		for iter_2_0 = 0, arg_2_0:ChildCount() - 2 do
			local slot_2_1 = arg_2_0:Childs(iter_2_0)
			local slot_2_2 = arg_2_0:Childs(iter_2_0 + 1)

			if (arg_2_2.y - slot_2_1.y) * (arg_2_1.x - slot_2_1.x) - (arg_2_1.y - slot_2_1.y) * (arg_2_2.x - slot_2_1.x) <= 0 ~= ((arg_2_2.y - slot_2_2.y) * (arg_2_1.x - slot_2_2.x) - (arg_2_1.y - slot_2_2.y) * (arg_2_2.x - slot_2_2.x) <= 0) and (arg_2_1.y - slot_2_1.y) * (slot_2_2.x - slot_2_1.x) - (slot_2_2.y - slot_2_1.y) * (arg_2_1.x - slot_2_1.x) <= 0 ~= ((arg_2_2.y - slot_2_1.y) * (slot_2_2.x - slot_2_1.x) - (slot_2_2.y - slot_2_1.y) * (arg_2_2.x - slot_2_1.x) <= 0) then
				local slot_2_3 = arg_2_1.x * arg_2_2.y - arg_2_1.y * arg_2_2.x
				local slot_2_4 = slot_2_1.x * slot_2_2.y - slot_2_1.y * slot_2_2.x
				local slot_2_5 = slot_2_1.x - slot_2_2.x
				local slot_2_6 = arg_2_1.x - arg_2_2.x
				local slot_2_7 = slot_2_1.y - slot_2_2.y
				local slot_2_8 = arg_2_1.y - arg_2_2.y
				local slot_2_9 = slot_2_6 * slot_2_7 - slot_2_8 * slot_2_5

				if slot_2_9 ~= 0 then
					table.insert(slot_2_0, vec2((slot_2_3 * slot_2_5 - slot_2_6 * slot_2_4) / slot_2_9, (slot_2_3 * slot_2_7 - slot_2_8 * slot_2_4) / slot_2_9))
				end
			end
		end

		local slot_2_10 = arg_2_0:Childs(0)
		local slot_2_11 = arg_2_0:Childs(arg_2_0:ChildCount() - 1)

		if (arg_2_2.y - slot_2_10.y) * (arg_2_1.x - slot_2_10.x) - (arg_2_1.y - slot_2_10.y) * (arg_2_2.x - slot_2_10.x) <= 0 ~= ((arg_2_2.y - slot_2_11.y) * (arg_2_1.x - slot_2_11.x) - (arg_2_1.y - slot_2_11.y) * (arg_2_2.x - slot_2_11.x) <= 0) and (arg_2_1.y - slot_2_10.y) * (slot_2_11.x - slot_2_10.x) - (slot_2_11.y - slot_2_10.y) * (arg_2_1.x - slot_2_10.x) <= 0 ~= ((arg_2_2.y - slot_2_10.y) * (slot_2_11.x - slot_2_10.x) - (slot_2_11.y - slot_2_10.y) * (arg_2_2.x - slot_2_10.x) <= 0) then
			local slot_2_12 = arg_2_1.x * arg_2_2.y - arg_2_1.y * arg_2_2.x
			local slot_2_13 = slot_2_10.x * slot_2_11.y - slot_2_10.y * slot_2_11.x
			local slot_2_14 = slot_2_10.x - slot_2_11.x
			local slot_2_15 = arg_2_1.x - arg_2_2.x
			local slot_2_16 = slot_2_10.y - slot_2_11.y
			local slot_2_17 = arg_2_1.y - arg_2_2.y
			local slot_2_18 = slot_2_15 * slot_2_16 - slot_2_17 * slot_2_14

			if slot_2_18 ~= 0 then
				table.insert(slot_2_0, vec2((slot_2_12 * slot_2_14 - slot_2_15 * slot_2_13) / slot_2_18, (slot_2_12 * slot_2_16 - slot_2_17 * slot_2_13) / slot_2_18))
			end
		end

		local slot_2_19
		local slot_2_20 = math.huge

		for iter_2_1 = 1, #slot_2_0 do
			local slot_2_21 = slot_2_0[iter_2_1]:distSqr(arg_2_1)

			if not slot_2_19 or slot_2_21 < slot_2_20 then
				slot_2_19, slot_2_20 = slot_2_0[iter_2_1], slot_2_21
			end
		end

		return slot_2_19
	end
}

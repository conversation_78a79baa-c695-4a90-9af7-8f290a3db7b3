local ove_0_5 = module.internal("pred")
local ove_0_6 = module.internal("orb")
local ove_0_7 = module.internal("TS")
local ove_0_8 = module.load("Brian","taliyah/menu")
local ove_0_9 = player:spellSlot(0)
local ove_0_10
local ove_0_11 = 0
local ove_0_12
local ove_0_13 = {
	speed = 3600,
	range = 1000,
	delay = 0.25,
	boundingRadiusMod = 1,
	width = 100,
	collision = {
		minion = true,
		wall = true
	},
	damage = function()
		-- 更新伤害计算：基础伤害 + AP加成
		-- 工作地面上的伤害：基础伤害的50%
		local base_damage = 38 + 22 * ove_0_9.level + player.totalAp * 0.45
		return base_damage * (player.manaCost0 == 20 and 1.5 or 1)
	end
}

local function ove_0_14(arg_2_0, arg_2_1)
	-- print 2
	if arg_2_0.startPos:dist(arg_2_0.endPos) > ove_0_13.range then
		return false
	end

	if ove_0_5.trace.linear.hardlock(ove_0_13, arg_2_0, arg_2_1) then
		return true
	end

	if ove_0_5.trace.linear.hardlockmove(ove_0_13, arg_2_0, arg_2_1) then
		return true
	end

	if arg_2_0.startPos:dist(arg_2_0.endPos) < 600 then
		return true
	end

	-- 改进路径预测，考虑敌人移动模式
	if ove_0_5.trace.newpath(arg_2_1, 0.033, 0.5) then
		return true
	end

	-- 对静止或缓慢移动的目标更容易命中
	if arg_2_1.moveSpeed < 350 then
		return true
	end
end

local function ove_0_15(arg_3_0, arg_3_1, arg_3_2)
	-- print 3
	if arg_3_2 > ove_0_13.range then
		return false
	end

	local slot_3_0 = ove_0_5.linear.get_prediction(ove_0_13, arg_3_1)

	if not slot_3_0 then
		return false
	end

	if not ove_0_14(slot_3_0, arg_3_1) then
		return false
	end

	if ove_0_5.collision.get_prediction(ove_0_13, slot_3_0, arg_3_1) then
		return false
	end

	arg_3_0.pos = slot_3_0.endPos

	return true
end

local function ove_0_16(arg_4_0, arg_4_1, arg_4_2)
	-- print 4
	if arg_4_2 > ove_0_13.range then
		return false
	end

	local slot_4_0 = ove_0_5.linear.get_prediction(ove_0_13, arg_4_1)

	if not slot_4_0 then
		return false
	end

	if not ove_0_14(slot_4_0, arg_4_1) then
		return false
	end

	local slot_4_1 = ove_0_5.collision.get_prediction(ove_0_13, slot_4_0, arg_4_1)

	if not slot_4_1 then
		arg_4_0.pos = slot_4_0.endPos
		arg_4_0.obj = arg_4_1

		return true
	end

	if player.manaCost0 == 20 then
		return false
	end

	local slot_4_2 = 0

	for iter_4_0 = 1, #slot_4_1 do
		local slot_4_3 = slot_4_1[iter_4_0]

		slot_4_2 = slot_4_2 + (slot_4_3.type == "vec2" and math.huge or slot_4_3.health)
	end

	if slot_4_2 > ove_0_13.damage() * 1.6 then
		return false
	end

	arg_4_0.pos = slot_4_0.endPos
	arg_4_0.obj = arg_4_1

	return true
end

local function ove_0_17()
	-- print 5
	if ove_0_11 > os.clock() then
		if not ove_0_10 then
			local slot_5_0 = player.path.serverPos2D
			local slot_5_1 = slot_5_0 + ove_0_12 * 1000

			for iter_5_0 = 0, objManager.enemies_n - 1 do
				local slot_5_2 = objManager.enemies[iter_5_0]

				if slot_5_2.isVisible and slot_5_2.isTargetable then
					local slot_5_3 = mathf.closest_vec_line_seg(slot_5_2.path.serverPos2D, slot_5_0, slot_5_1)

					if slot_5_3 and slot_5_3:dist(slot_5_2.path.serverPos2D) < 160 then
						ove_0_10 = slot_5_2
					end
				end
			end

			if not ove_0_10 then
				ove_0_11 = 0

				return
			end
		end

		local slot_5_4 = ove_0_5.core.lerp(ove_0_10.path, 0.75, ove_0_10.moveSpeed)
		local slot_5_5 = player.path.serverPos2D + (mousePos2D - player.path.serverPos2D):norm() * 200
		local slot_5_6 = slot_5_4 + ove_0_12 * 400
		local slot_5_7 = mathf.closest_vec_line(slot_5_5, slot_5_6, slot_5_4)

		return player:move(vec3(slot_5_7.x, player.y, slot_5_7.y))
	end

	if ove_0_9.state ~= 0 then
		return
	end

	local slot_5_8 = ove_0_7.get_result(ove_0_16)

	if slot_5_8.pos and player:castSpell("pos", 0, vec3(slot_5_8.pos.x, mousePos.y, slot_5_8.pos.y)) then
		ove_0_10 = slot_5_8.obj

		ove_0_6.core.set_server_pause()

		return true
	end
end

local function ove_0_18()
	-- print 6
	if ove_0_9.state ~= 0 then
		return
	end

	if player.par / player.maxPar < ove_0_8.q.harass_mana:get() * 0.01 then
		return
	end

	if player.manaCost0 ~= 20 then
		return
	end

	if ove_0_6.farm.lane_clear_wait() then
		return
	end

	local slot_6_0 = ove_0_7.get_result(ove_0_15)

	if slot_6_0.pos and player:castSpell("pos", 0, vec3(slot_6_0.pos.x, mousePos.y, slot_6_0.pos.y)) then
		ove_0_6.core.set_server_pause()

		return true
	end
end

local function ove_0_19()
	-- print 7
	if ove_0_9.state ~= 0 then
		return
	end

	if not ove_0_8.q.lane_clear:get() then
		return
	end

	if player.manaCost0 == 20 then
		local slot_7_0, slot_7_1 = ove_0_6.farm.skill_clear_linear(ove_0_13)

		if slot_7_0 and player:castSpell("pos", 0, vec3(slot_7_0.endPos.x, mousePos.y, slot_7_0.endPos.y)) then
			ove_0_6.core.set_server_pause()
			ove_0_6.farm.set_ignore(slot_7_1, 2)

			return true
		end
	else
		local slot_7_2 = {}
		local slot_7_3 = 0

		for iter_7_0 = 0, objManager.minions.size[TEAM_ENEMY] - 1 do
			local slot_7_4 = objManager.minions[TEAM_ENEMY][iter_7_0]

			if slot_7_4.isVisible and slot_7_4.isTargetable and slot_7_4.pos2D:dist(player.pos2D) < 1000 then
				slot_7_3 = slot_7_3 + 1
				slot_7_2[slot_7_3] = slot_7_4
			end
		end

		-- 优化清线逻辑：至少需要2个小兵才使用Q
		if slot_7_3 < 2 then
			return
		end

		local slot_7_5 = player.path.serverPos2D

		for iter_7_1 = 1, slot_7_3 do
			local slot_7_6 = slot_7_2[iter_7_1].path.serverPos2D
			local slot_7_7 = 1

			for iter_7_2 = 1, slot_7_3 do
				if iter_7_1 ~= iter_7_2 then
					local slot_7_8 = slot_7_2[iter_7_2].path.serverPos2D
					local slot_7_9 = mathf.closest_vec_line_seg(slot_7_8, slot_7_5, slot_7_6)

					if slot_7_9 and slot_7_9:dist(slot_7_8) < 145 then
						slot_7_7 = slot_7_7 + 1
					end

					if slot_7_7 > 2 and player:castSpell("pos", 0, vec3(slot_7_6.x, mousePos.y, slot_7_6.y)) then
						ove_0_6.core.set_server_pause()

						return true
					end
				end
			end
		end
	end
end

local function ove_0_20()
	-- print 8
	if ove_0_9.state ~= 0 then
		return
	end

	local slot_8_0
	local slot_8_1 = math.huge

	for iter_8_0 = 0, objManager.minions.size[TEAM_NEUTRAL] - 1 do
		local slot_8_2 = objManager.minions[TEAM_NEUTRAL][iter_8_0]

		if slot_8_2.isVisible and slot_8_2.isTargetable then
			local slot_8_3 = slot_8_2.pos2D:dist(player.pos2D)

			if slot_8_3 < 1000 and (not slot_8_0 or slot_8_3 < slot_8_1) then
				slot_8_0 = slot_8_2
				slot_8_1 = slot_8_3
			end
		end
	end

	if not slot_8_0 then
		return
	end

	local slot_8_4 = ove_0_5.linear.get_prediction(ove_0_13, slot_8_0)

	if slot_8_4 and player:castSpell("pos", 0, vec3(slot_8_4.endPos.x, mousePos.y, slot_8_4.endPos.y)) then
		ove_0_6.core.set_server_pause()

		return true
	end
end

local function ove_0_21()
	-- print 9
	if ove_0_9.state ~= 0 then
		return
	end

	if not ove_0_8.q.farm_assist:get() then
		return
	end

	if player.manaCost0 ~= 20 then
		return
	end

	local slot_9_0, slot_9_1 = ove_0_6.farm.skill_farm_linear(ove_0_13)

	if slot_9_0 and player:castSpell("pos", 0, vec3(slot_9_0.endPos.x, player.y, slot_9_0.endPos.y)) then
		ove_0_6.farm.set_ignore(slot_9_1, 2)
		ove_0_6.core.set_server_pause()

		return true
	end
end

local function ove_0_22(arg_10_0)
	-- print 10
	if arg_10_0.name == "TaliyahQ" and player.manaCost0 ~= 20 then
		ove_0_11 = os.clock() + 1.5
		ove_0_12 = (arg_10_0.startPos2D - arg_10_0.endPos2D):norm()

		ove_0_6.core.set_pause_attack(1.5)
	end
end

local function ove_0_23()
	-- print 11
	if not ove_0_8.q.draw_range:get() then
		return
	end

	if not player.isOnScreen then
		return
	end

	if player.isDead then
		return
	end

	graphics.draw_circle(player.pos, ove_0_13.range, 2, ove_0_8.q.draw_color:get(), 48)
end

local function ove_0_24()
	-- print 12
	if ove_0_10 and (ove_0_10.isDead or not ove_0_10.isVisible) then
		ove_0_10 = nil
	end
end

return {
	invoke = ove_0_17,
	invoke_harass = ove_0_18,
	invoke_lane_clear = ove_0_19,
	invoke_jungle_clear = ove_0_20,
	invoke_farm_assist = ove_0_21,
	on_process_spell = ove_0_22,
	on_draw = ove_0_23,
	update = ove_0_24
}

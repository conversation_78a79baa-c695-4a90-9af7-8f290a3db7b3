local lvxbot = module.load(header.id, 'lvxbot/main')
local menu = lvxbot.load('menu')

local input = {
  prediction = {
    type = 'Linear',
    --
    range = 1110,
    delay = 0.25,
    speed = 2000,
    width = 60,
   	mov = 0.22,
	movtime = 0.35,
	movseep = 1000,
    boundingRadiusMod = 1,
	PredZs = 0,
    --
    collision = {
      hero = false, --allow to hit other heros :-)
      minion = true,
      wall = true,
    },
    --

  },

   cast = {
       pred = function()
      if menu.pred:get() then
	  return 1
	  else
	  return 2
	  end
	 
      return
    end,
   },

  
  
  target_selector = {
    type = 'LESS_CAST_AD',
  },

  cast_spell = {
    type = 'pos',
    slot = _Q,
    arg1 = function(action)
      local seg = action.ts_result.seg
      local obj = action.ts_result.obj
      return seg.endPos:to3D(obj.y)
    end,
  },

  slot = _Q,
  ignore_obj_radius = 2000,

}


	--  print(('example_slider changed from %u to %u'):format(old, new))

return lvxbot.ezrealcexpert.create(input)





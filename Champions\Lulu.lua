local var_0_0 = "1.0"
local var_0_1 = module.seek("evade")
local var_0_2 = module.load("<PERSON>", "Utility/SpellDatabaseSupport")
local var_0_3 = module.internal("pred")
local var_0_4 = module.internal("TS")
local var_0_5 = module.internal("orb")
local var_0_6 = module.load("<PERSON>", "Utility/common20")
local Curses = module.load("<PERSON>", "Curses");
local var_0_7 = {
	speed = 1800,
	range = 900,
	delay = 0.25,
	boundingRadiusMod = 0,
	width = 60
}
local var_0_8 = {
	range = 650
}
local var_0_9 = {
	range = 650
}
local var_0_10 = {
	range = 900
}

local function var_0_11(arg_1_0, arg_1_1, arg_1_2)
	if var_0_3.trace.linear.hardlock(arg_1_0, arg_1_1, arg_1_2) then
		return true
	end

	if var_0_3.trace.linear.hardlockmove(arg_1_0, arg_1_1, arg_1_2) then
		return true
	end

	if arg_1_2 and var_0_6.IsValidTarget(arg_1_2) and player.pos:dist(arg_1_2) <= 500 then
		return true
	end

	if var_0_3.trace.newpath(arg_1_2, 0.033, 0.5) then
		return true
	end
end

local var_0_12 = {
	[0] = "Q",
	"W",
	"E",
	[-1] = "P",
	[3] = "R"
}
local var_0_13 = menu("Brian" .. player.charName, "[Brian] " .. player.charName)

var_0_13:menu("combo", "Combo")
var_0_13.combo:menu("settingsq", "Q Settings")
var_0_13.combo.settingsq:boolean("qcombo", "Use Q", true)
var_0_13.combo.settingsq:slider("qrange", " ^- Max. Range", 700, 0, 900, 5)
var_0_13.combo.settingsq:boolean("useeq", "Use E > Q Extended", false)
var_0_13.combo:menu("settingsww", "W Settings")
var_0_13.combo.settingsww:header("uhhh", " -- W Enemy -- ")
var_0_13.combo.settingsww:boolean("wcomboenemy", "Use W in Combo on Enemy", false)
var_0_13.combo.settingsww:menu("wblacklist", "W Blacklist for Enemy")

local var_0_14 = var_0_6.GetEnemyHeroes()

for iter_0_0, iter_0_1 in ipairs(var_0_14) do
	var_0_13.combo.settingsww.wblacklist:boolean(iter_0_1.charName, "Don't use on: " .. iter_0_1.charName, false)
end

var_0_13.combo.settingsww:header("uhhh", " -- W Ally -- ")
var_0_13.combo.settingsww:boolean("enablew", "Auto W on Ally Auto Attack", true)
var_0_13.combo.settingsww:boolean("enablee", "Auto E on Ally Auto Attacks", false)
var_0_13.combo.settingsww:menu("wset", "Ally Priority Settings")
var_0_13.combo.settingsww.wset:header("uhhh", "0 - Disabled, 1 - Biggest Priority")

local var_0_15 = var_0_6.GetAllyHeroes()

for iter_0_2, iter_0_3 in ipairs(var_0_15) do
	if iter_0_3.charName ~= "Lulu" and iter_0_3.charName ~= "Twitch" and iter_0_3.charName ~= "KogMaw" and iter_0_3.charName ~= "Tristana" and iter_0_3.charName ~= "Ashe" and iter_0_3.charName ~= "Vayne" and iter_0_3.charName ~= "Varus" and iter_0_3.charName ~= "Xayah" and iter_0_3.charName ~= "Lucian" and iter_0_3.charName ~= "Sivir" and iter_0_3.charName ~= "Draven" and iter_0_3.charName ~= "Kalista" and iter_0_3.charName ~= "Caitlyn" and iter_0_3.charName ~= "Jinx" and iter_0_3.charName ~= "Ezreal" then
		var_0_13.combo.settingsww.wset:slider(iter_0_3.charName, "Priority: " .. iter_0_3.charName, 0, 0, 5, 1)
	end

	if iter_0_3.charName == "Twitch" or iter_0_3.charName == "KogMaw" or iter_0_3.charName == "Tristana" or iter_0_3.charName == "Ashe" or iter_0_3.charName == "Vayne" or iter_0_3.charName == "Varus" or iter_0_3.charName == "Xayah" or iter_0_3.charName == "Lucian" or iter_0_3.charName == "Sivir" or iter_0_3.charName == "Draven" or iter_0_3.charName == "Kalista" or iter_0_3.charName == "Caitlyn" or iter_0_3.charName == "Jinx" or iter_0_3.charName == "Ezreal" then
		var_0_13.combo.settingsww.wset:slider(iter_0_3.charName, "Priority: " .. iter_0_3.charName, 1, 0, 5, 1)
	end

	if iter_0_3.charName == "Lulu" then
		var_0_13.combo.settingsww.wset:slider(iter_0_3.charName, "Priority: " .. iter_0_3.charName, 0, 0, 5, 1)
	end
end

var_0_13.combo:menu("settingse", "E Settings")
var_0_13.combo.settingse:dropdown("eusage", "E Usage on Enemy", 2, {
	"Always",
	"Logic",
	"Never"
})
var_0_13.combo:menu("rset", "R Settings")
var_0_13.combo.rset:boolean("rcombo", "Use R", true)
var_0_13.combo.rset:slider("hitr", " ^- if Knocks Up X Enemies", 2, 1, 5, 1)
var_0_13.combo.rset:menu("whitelist", "Ally Settings")
var_0_13.combo.rset.whitelist:boolean("autor", "Auto R", true)

local var_0_16 = var_0_6.GetAllyHeroes()

for iter_0_4, iter_0_5 in ipairs(var_0_16) do
	var_0_13.combo.rset.whitelist:slider(iter_0_5.charName, "Use R if X HP: " .. iter_0_5.charName, 30, 1, 100, 1)
end

var_0_13.combo.rset:header("uhhh", " ~~~~ ")
var_0_13.combo.rset:keybind("semir", "Semi-R", "T", nil)
var_0_13.combo.rset:dropdown("semirmode", " ^- Mode:", 1, {
	"Lowest Health",
	"Only Whitelisted"
})
var_0_13:menu("harass", "Harass")
var_0_13.harass:boolean("qcombo", "Use Q", true)
var_0_13.harass:boolean("ecombo", "Use E", true)
var_0_13.harass:boolean("useeq", "Use E > Q Extended", true)
var_0_13:menu("we", "W > E Settings")
var_0_13.we:keybind("wekey", "W > E Boost Ally", "Z", nil)
var_0_13.we:header("uhhh", "0 - Disabled, 1 - Biggest Priority, 5 - Lowest Priority")

local var_0_17 = var_0_6.GetAllyHeroes()

for iter_0_6, iter_0_7 in ipairs(var_0_17) do
	if iter_0_7.charName ~= "Twitch" and iter_0_7.charName ~= "KogMaw" and iter_0_7.charName ~= "Tristana" and iter_0_7.charName ~= "Ashe" and iter_0_7.charName ~= "Vayne" and iter_0_7.charName ~= "Varus" and iter_0_7.charName ~= "Xayah" and iter_0_7.charName ~= "Lucian" and iter_0_7.charName ~= "Sivir" and iter_0_7.charName ~= "Draven" and iter_0_7.charName ~= "Kalista" and iter_0_7.charName ~= "Caitlyn" and iter_0_7.charName ~= "Jinx" and iter_0_7.charName ~= "Ezreal" then
		var_0_13.we:slider(iter_0_7.charName, "Priority: " .. iter_0_7.charName, 0, 0, 5, 1)
	end

	if iter_0_7.charName == "Twitch" or iter_0_7.charName == "KogMaw" or iter_0_7.charName == "Tristana" or iter_0_7.charName == "Ashe" or iter_0_7.charName == "Vayne" or iter_0_7.charName == "Varus" or iter_0_7.charName == "Xayah" or iter_0_7.charName == "Lucian" or iter_0_7.charName == "Sivir" or iter_0_7.charName == "Draven" or iter_0_7.charName == "Kalista" or iter_0_7.charName == "Caitlyn" or iter_0_7.charName == "Jinx" or iter_0_7.charName == "Ezreal" then
		var_0_13.we:slider(iter_0_7.charName, "Priority: " .. iter_0_7.charName, 1, 0, 5, 1)
	end
end

var_0_13:menu("blacklist", "R Blacklist")

local var_0_18 = var_0_6.GetAllyHeroes()

for iter_0_8, iter_0_9 in ipairs(var_0_18) do
	var_0_13.blacklist:boolean(iter_0_9.charName, "Don't use on: " .. iter_0_9.charName, false)
end

var_0_13:menu("SpellsMenu", "Shielding")
var_0_13.SpellsMenu:slider("mana", "Mana Manager", 2, 0, 100, 5)
var_0_13.SpellsMenu:boolean("enable", "Enable Shielding", true)
var_0_13.SpellsMenu:boolean("priority", "Priority Ally", true)
var_0_13.SpellsMenu:menu("blacklist", "Ally Shield Blacklist")

local var_0_19 = var_0_6.GetAllyHeroes()

for iter_0_10, iter_0_11 in ipairs(var_0_19) do
	var_0_13.SpellsMenu.blacklist:boolean(iter_0_11.charName, "Don't use on: " .. iter_0_11.charName, false)
end

var_0_13.SpellsMenu:header("hello", " -- Enemy Skillshots -- ")

for iter_0_12, iter_0_13 in pairs(var_0_2) do
	for iter_0_14, iter_0_15 in pairs(var_0_6.GetEnemyHeroes()) do
		if iter_0_13.charName == iter_0_15.charName then
			if iter_0_13.displayname == "" then
				iter_0_13.displayname = iter_0_12
			end

			if iter_0_13.danger == 0 then
				iter_0_13.danger = 1
			end

			if var_0_13.SpellsMenu[iter_0_13.charName] == nil then
				var_0_13.SpellsMenu:menu(iter_0_13.charName, iter_0_13.charName)
			end

			var_0_13.SpellsMenu[iter_0_13.charName]:menu(iter_0_13.slot, "" .. iter_0_13.charName .. " | " .. (var_0_12[iter_0_13.slot] or "?"))
			var_0_13.SpellsMenu[iter_0_13.charName][iter_0_13.slot]:boolean("Dodge", "Enable Block", true)
			var_0_13.SpellsMenu[iter_0_13.charName][iter_0_13.slot]:slider("hp", "HP to Dodge", 100, 1, 100, 5)
		end
	end
end

var_0_13:menu("draws", "Draw Settings")
var_0_13.draws:header("ranges", " -- Ranges -- ")
var_0_13.draws:boolean("drawq", "Draw Q Range", true)
var_0_13.draws:color("colorq", "  ^- Color", 255, 255, 255, 255)
var_0_13.draws:boolean("draww", "Draw W Range", false)
var_0_13.draws:color("colorw", "  ^- Color", 255, 255, 255, 255)
var_0_13.draws:boolean("drawe", "Draw E Range", true)
var_0_13.draws:color("colore", "  ^- Color", 255, 233, 121, 121)
var_0_13.draws:boolean("drawr", "Draw R Range", false)
var_0_13.draws:color("colorr", "  ^- Color", 255, 233, 121, 121)
var_0_13.draws:boolean("drawpix", "Draw Pix Position", true)
var_0_13.draws:boolean("drawrangespix", "Draw Ranges from Pix", true)
var_0_13.draws:header("other", " -- Other -- ")
var_0_13.draws:boolean("drawdamage", "Draw Damage", true)
var_0_13.draws:slider("transparency", "Damage Drawing Transparency", 155, 0, 255, 1)
var_0_13.SpellsMenu:header("hello", " -- Misc. -- ")
var_0_13.SpellsMenu:boolean("targeteteteteteed", "Shield on Targeted Spells", true)
var_0_13.SpellsMenu:boolean("cc", "Auto Shield on CC", true)
var_0_13.SpellsMenu:menu("BasicAttack", "Basic Attack Sielding", true)
var_0_13.SpellsMenu.BasicAttack:boolean("aa", "Shield on Basic attack", true)
var_0_13.SpellsMenu.BasicAttack:slider("aahp", " ^- HP to Shield", 40, 1, 100, 5)
var_0_13.SpellsMenu.BasicAttack:boolean("critaa", "Shield on Crit attack", true)
var_0_13.SpellsMenu.BasicAttack:slider("crithp", " ^- HP to Shield", 40, 1, 100, 5)
var_0_13.SpellsMenu.BasicAttack:boolean("minionaa", "Shield on Minion attack", true)
var_0_13.SpellsMenu.BasicAttack:slider("minionhp", " ^- HP to Shield", 10, 1, 100, 5)
var_0_13.SpellsMenu.BasicAttack:boolean("turret", "Shield on Turret attack", true)
var_0_13:menu("misc", "Misc.")
var_0_13.misc:menu("antigap", "Anti-Gapclose settings")
var_0_13.misc.antigap:header("hello", " -- Enemy Dashes -- ")
var_0_13.misc.antigap:boolean("GapAS", "Use W for Anti-Gapclose on Dashes", true)
var_0_13.misc.antigap:boolean("GapASR", "Use R for Anti-Gapclose on Dashes", true)
var_0_13.misc.antigap:menu("blacklist", "Anti-Gapclose Blacklist")

local var_0_20 = var_0_6.GetEnemyHeroes()

for iter_0_16, iter_0_17 in ipairs(var_0_20) do
	var_0_13.misc.antigap.blacklist:boolean(iter_0_17.charName, "Don't use on: " .. iter_0_17.charName, false)
end

var_0_13.misc.antigap:header("hello", " -- Distance Check -- ")
var_0_13.misc.antigap:boolean("GapASW", "Use W if Enemy is near Ally", false)
var_0_13.misc.antigap:menu("blacklistally", "Ally Blacklist")

local var_0_21 = var_0_6.GetAllyHeroes()

for iter_0_18, iter_0_19 in ipairs(var_0_21) do
	var_0_13.misc.antigap.blacklistally:boolean(iter_0_19.charName, "Don't check from: " .. iter_0_19.charName, false)
end

var_0_13.misc.antigap:menu("blacklistenemy", "Enemy Blacklist")

local var_0_22 = var_0_6.GetEnemyHeroes()

for iter_0_20, iter_0_21 in ipairs(var_0_22) do
	var_0_13.misc.antigap.blacklistenemy:boolean(iter_0_21.charName, "Don't check: " .. iter_0_21.charName, false)
end

var_0_13.misc:menu("interrupt", "Interrupt Settings")
var_0_13.misc.interrupt:boolean("inte", "Use W", true)
var_0_13.misc.interrupt:menu("interruptmenu", "Interrupt Settings")

for iter_0_22 = 1, #var_0_6.GetEnemyHeroes() do
	local var_0_23 = var_0_6.GetEnemyHeroes()[iter_0_22]
	local var_0_24 = string.lower(var_0_23.charName)

	if var_0_23 and var_0_6.GetInterruptableSpells()[var_0_24] then
		for iter_0_23 = 1, #var_0_6.GetInterruptableSpells()[var_0_24] do
			local var_0_25 = var_0_6.GetInterruptableSpells()[var_0_24][iter_0_23]

			var_0_13.misc.interrupt.interruptmenu:boolean(string.format(tostring(var_0_23.charName) .. tostring(var_0_25.menuslot)), "Interrupt " .. tostring(var_0_23.charName) .. " " .. tostring(var_0_25.menuslot), true)
		end
	end
end

var_0_13:header("--", " ---- ")
var_0_13:keybind("fleekey", "Flee", "G", nil)

local var_0_26 = {}
local var_0_27

local function var_0_28(arg_2_0, arg_2_1)
	local var_2_0 = {}

	for iter_2_0 = 0, objManager.allies_n - 1 do
		local var_2_1 = objManager.allies[iter_2_0]

		if arg_2_1 > arg_2_0:dist(var_2_1.pos) and var_0_6.IsValidTarget(var_2_1) then
			var_2_0[#var_2_0 + 1] = var_2_1
		end
	end

	return var_2_0
end

local function var_0_29()
	local var_3_0

	for iter_3_0 = 0, objManager.allies_n - 1 do
		local var_3_1 = objManager.allies[iter_3_0]

		if not player.isRecalling and var_3_1.team == TEAM_ALLY and not var_3_1.isDead and var_0_13.combo.settingsww.wset[var_3_1.charName]:get() > 0 and var_3_1.pos:dist(player.pos) <= var_0_8.range then
			if var_3_0 == nil then
				var_3_0 = var_3_1
			elseif var_0_13.combo.settingsww.wset[var_3_1.charName]:get() < var_0_13.combo.settingsww.wset[var_3_0.charName]:get() then
				var_3_0 = var_3_1
			end
		end
	end

	return var_3_0
end

local function var_0_30()
	local var_4_0

	for iter_4_0 = 0, objManager.allies_n - 1 do
		local var_4_1 = objManager.allies[iter_4_0]

		if not player.isRecalling and var_4_1.team == TEAM_ALLY and not var_4_1.isDead and var_0_13.we[var_4_1.charName]:get() > 0 and var_4_1.pos:dist(player.pos) <= var_0_8.range then
			if var_4_0 == nil then
				var_4_0 = var_4_1
			elseif var_0_13.we[var_4_1.charName]:get() < var_0_13.we[var_4_0.charName]:get() then
				var_4_0 = var_4_1
			end
		end
	end

	return var_4_0
end

local function var_0_31()
	local var_5_0

	for iter_5_0 = 0, objManager.allies_n - 1 do
		local var_5_1 = objManager.allies[iter_5_0]

		if not player.isRecalling and var_5_1.team == TEAM_ALLY and not var_5_1.isDead and var_5_1.pos:dist(player.pos) <= var_0_10.range then
			if var_0_13.combo.rset.semirmode:get() == 1 then
				if var_5_0 == nil then
					var_5_0 = var_5_1
				elseif var_5_1.health / var_5_1.maxHealth * 100 < var_5_0.health / var_5_0.maxHealth * 100 then
					var_5_0 = var_5_1
				end
			elseif var_0_13.blacklist[var_5_1.charName] and not var_0_13.blacklist[var_5_1.charName]:get() then
				var_5_0 = var_5_1
			end
		end
	end

	return var_5_0
end

local var_0_32 = {
	"CaitlynHeadshotMissile",
	"RumbleOverheatAttack",
	"JarvanIVMartialCadenceAttack",
	"ShenKiAttack",
	"MasterYiDoubleStrike",
	"sonahymnofvalorattackupgrade",
	"sonaariaofperseveranceupgrade",
	"sonasongofdiscordattackupgrade",
	"NocturneUmbraBladesAttack",
	"NautilusRavageStrikeAttack",
	"ZiggsPassiveAttack",
	"QuinnWEnhanced",
	"LucianPassiveAttack",
	"SkarnerPassiveAttack",
	"KarthusDeathDefiedBuff",
	"GarenQAttack",
	"KennenMegaProc",
	"MordekaiserQAttack",
	"MordekaiserQAttack2",
	"BlueCardPreAttack",
	"RedCardPreAttack",
	"GoldCardPreAttack",
	"XenZhaoThrust",
	"XenZhaoThrust2",
	"XenZhaoThrust3",
	"ViktorQBuff",
	"TrundleQ",
	"RenektonSuperExecute",
	"RenektonExecute",
	"GarenSlash2",
	"frostarrow",
	"SivirWAttack",
	"rengarnewpassivebuffdash",
	"YorickQAttack",
	"ViEAttack",
	"SejuaniBasicAttackW",
	"ShyvanaDoubleAttackHit",
	"ShenQAttack",
	"SonaEAttackUpgrade",
	"SonaWAttackUpgrade",
	"SonaQAttackUpgrade",
	"PoppyPassiveAttack",
	"NidaleeTakedownAttack",
	"NasusQAttack",
	"KindredBasicAttackOverrideLightbombFinal",
	"LeonaShieldOfDaybreakAttack",
	"KassadinBasicAttack3",
	"JhinPassiveAttack",
	"JayceHyperChargeRangedAttack",
	"JaycePassiveRangedAttack",
	"JaycePassiveMeleeAttack",
	"illaoiwattack",
	"hecarimrampattack",
	"DrunkenRage",
	"GalioPassiveAttack",
	"FizzWBasicAttack",
	"FioraEAttack",
	"EkkoEAttack",
	"ekkobasicattackp3",
	"MasochismAttack",
	"DravenSpinningAttack",
	"DianaBasicAttack3",
	"DariusNoxianTacticsONHAttack",
	"CamilleQAttackEmpowered",
	"CamilleQAttack",
	"PowerFistAttack",
	"AsheQAttack",
	"jinxqattack",
	"jinxqattack2",
	"KogMawBioArcaneBarrage"
}
local var_0_33 = {}

local function var_0_34(arg_6_0)
	if var_0_13.misc.interrupt.inte:get() and arg_6_0 and arg_6_0.owner and arg_6_0.owner.team == TEAM_ENEMY and player.pos:dist(arg_6_0.owner.pos) < var_0_8.range then
		local var_6_0 = string.lower(arg_6_0.owner.charName)

		if var_0_6.GetInterruptableSpells()[var_6_0] then
			for iter_6_0 = 1, #var_0_6.GetInterruptableSpells()[var_6_0] do
				local var_6_1 = var_0_6.GetInterruptableSpells()[var_6_0][iter_6_0]

				if var_0_13.misc.interrupt.interruptmenu[arg_6_0.owner.charName .. var_6_1.menuslot]:get() and var_6_1.spellname == arg_6_0.name:lower() then
					var_0_33.start = os.clock()
					var_0_33.channel = var_6_1.channelduration
					var_0_33.owner = arg_6_0.owner
				end
			end
		end
	end
end

local function var_0_35()
	if var_0_33.owner then
		if os.clock() - var_0_33.channel >= var_0_33.start then
			var_0_33.owner = false

			return
		end

		if player:spellSlot(2).state == 0 and player.pos:dist(var_0_33.owner.pos) <= var_0_8.range then
			player:castSpell("obj", 1, var_0_33.owner)

			var_0_33.owner = false
		end
	end
end

local function var_0_36(arg_8_0)
	local var_8_0 = arg_8_0

	var_0_34(var_8_0)

	local var_8_1

	if arg_8_0 and arg_8_0.owner.type == TYPE_HERO and arg_8_0.owner.team == TEAM_ALLY and arg_8_0.target and arg_8_0.target.type == TYPE_HERO then
		for iter_8_0 = 1, #var_0_32 do
			if arg_8_0.name:lower():find(var_0_32[iter_8_0]:lower()) and arg_8_0.owner.pos:dist(player.pos) <= var_0_8.range and var_0_13.combo.settingsww.wset[arg_8_0.owner.charName]:get() > 0 then
				if var_8_1 == nil then
					var_8_1 = arg_8_0.owner
				elseif var_0_13.combo.settingsww.wset[arg_8_0.owner.charName]:get() < var_0_13.combo.settingsww.wset[var_8_1.charName]:get() then
					var_8_1 = arg_8_0.owner
				end

				if var_8_1 then
					if var_0_13.combo.settingsww.enablew:get() then
						player:castSpell("obj", 1, var_8_1)
					end

					if var_0_13.combo.settingsww.enablee:get() then
						player:castSpell("obj", 2, var_8_1)
					end
				end
			end
		end

		if arg_8_0.name:find("BasicAttack") and arg_8_0.owner.pos:dist(player.pos) <= var_0_8.range and var_0_13.combo.settingsww.wset[arg_8_0.owner.charName]:get() > 0 then
			if var_8_1 == nil then
				var_8_1 = arg_8_0.owner
			elseif var_0_13.combo.settingsww.wset[arg_8_0.owner.charName]:get() < var_0_13.combo.settingsww.wset[var_8_1.charName]:get() then
				var_8_1 = arg_8_0.owner
			end

			if var_8_1 then
				if var_0_13.combo.settingsww.enablew:get() then
					player:castSpell("obj", 1, var_8_1)
				end

				if var_0_13.combo.settingsww.enablee:get() then
					player:castSpell("obj", 2, var_8_1)
				end
			end
		end
	end

	if arg_8_0 and arg_8_0.owner.type == TYPE_HERO and arg_8_0.owner.team == TEAM_ALLY and arg_8_0.name:find("KogMawBioArcaneBarrage") and arg_8_0.owner.pos:dist(player.pos) <= var_0_8.range and var_0_13.combo.settingsww.wset[arg_8_0.owner.charName]:get() > 0 then
		if var_8_1 == nil then
			var_8_1 = arg_8_0.owner
		elseif var_0_13.combo.settingsww.wset[arg_8_0.owner.charName]:get() < var_0_13.combo.settingsww.wset[var_8_1.charName]:get() then
			var_8_1 = arg_8_0.owner
		end

		if var_8_1 then
			if var_0_13.combo.settingsww.enablew:get() then
				player:castSpell("obj", 1, var_8_1)
			end

			if var_0_13.combo.settingsww.enablee:get() then
				player:castSpell("obj", 2, var_8_1)
			end
		end
	end

	if var_0_13.SpellsMenu.targeteteteteteed:get() then
		local var_8_2 = var_0_6.GetAllyHeroes()

		for iter_8_1, iter_8_2 in ipairs(var_8_2) do
			if iter_8_2 and var_0_13.SpellsMenu.blacklist[iter_8_2.charName] and not var_0_13.SpellsMenu.blacklist[iter_8_2.charName]:get() and arg_8_0 and arg_8_0.owner.type == TYPE_HERO and arg_8_0.owner.team == TEAM_ENEMY and arg_8_0.target and arg_8_0.target == iter_8_2 and not arg_8_0.name == "MorganaW" and not arg_8_0.name:find("crit") and not arg_8_0.name:find("BasicAttack") and var_0_13.SpellsMenu.targeteteteteteed:get() then
				if iter_8_2.pos:dist(player.pos) <= var_0_9.range and player.mana / player.maxMana * 100 >= var_0_13.SpellsMenu.mana:get() then
					player:castSpell("obj", 2, iter_8_2)

					if var_0_13.SpellsMenu.targeteteteteteed:get() and iter_8_2.pos:dist(player.pos) <= var_0_9.range then
						player:castSpell("obj", 2, iter_8_2)
					end
				end

				if var_0_13.combo.rset.whitelist.autor:get() and var_0_13.blacklist[iter_8_2.charName] and not var_0_13.blacklist[iter_8_2.charName]:get() and iter_8_2.pos:dist(player.pos) <= var_0_10.range and var_0_13.combo.rset.whitelist[iter_8_2.charName]:get() >= iter_8_2.health / iter_8_2.maxHealth * 100 then
					player:castSpell("obj", 3, iter_8_2)
				end
			end
		end
	end

	if var_0_13.SpellsMenu.BasicAttack.aa:get() then
		local var_8_3 = var_0_6.GetAllyHeroes()

		for iter_8_3, iter_8_4 in ipairs(var_8_3) do
			if iter_8_4 and iter_8_4.pos:dist(player.pos) <= var_0_9.range and iter_8_4 and iter_8_4.pos:dist(player.pos) <= var_0_9.range and arg_8_0.owner.type == TYPE_HERO and arg_8_0.owner.team == TEAM_ENEMY and arg_8_0.target == iter_8_4 then
				for iter_8_5 = 1, #var_0_32 do
					if arg_8_0.name:lower():find(var_0_32[iter_8_5]:lower()) and iter_8_4.health / iter_8_4.maxHealth * 100 <= var_0_13.SpellsMenu.BasicAttack.aahp:get() and not var_0_13.SpellsMenu.blacklist[iter_8_4.charName]:get() then
						if iter_8_4.pos:dist(player.pos) <= var_0_9.range and player.mana / player.maxMana * 100 >= var_0_13.SpellsMenu.mana:get() then
							player:castSpell("obj", 2, iter_8_4)
						end

						if var_0_13.combo.rset.whitelist.autor:get() and var_0_13.blacklist[iter_8_4.charName] and not var_0_13.blacklist[iter_8_4.charName]:get() and iter_8_4.pos:dist(player.pos) <= var_0_10.range and var_0_13.combo.rset.whitelist[iter_8_4.charName]:get() >= iter_8_4.health / iter_8_4.maxHealth * 100 then
							player:castSpell("obj", 3, iter_8_4)
						end
					end
				end

				if arg_8_0.name:find("BasicAttack") and iter_8_4.health / iter_8_4.maxHealth * 100 <= var_0_13.SpellsMenu.BasicAttack.aahp:get() and not var_0_13.SpellsMenu.blacklist[iter_8_4.charName]:get() then
					if iter_8_4.pos:dist(player.pos) <= var_0_9.range and player.mana / player.maxMana * 100 >= var_0_13.SpellsMenu.mana:get() then
						player:castSpell("obj", 2, iter_8_4)
					end

					if var_0_13.combo.rset.whitelist.autor:get() and var_0_13.blacklist[iter_8_4.charName] and not var_0_13.blacklist[iter_8_4.charName]:get() and iter_8_4.pos:dist(player.pos) <= var_0_10.range and var_0_13.combo.rset.whitelist[iter_8_4.charName]:get() >= iter_8_4.health / iter_8_4.maxHealth * 100 then
						player:castSpell("obj", 3, iter_8_4)
					end
				end
			end
		end
	end

	if var_0_13.SpellsMenu.BasicAttack.critaa:get() then
		local var_8_4 = var_0_6.GetAllyHeroes()

		for iter_8_6, iter_8_7 in ipairs(var_8_4) do
			if iter_8_7 and iter_8_7.pos:dist(player.pos) <= var_0_9.range and arg_8_0.owner.type == TYPE_HERO and arg_8_0.owner.team == TEAM_ENEMY and arg_8_0.target == iter_8_7 and arg_8_0.name:find("crit") and iter_8_7.health / iter_8_7.maxHealth * 100 <= var_0_13.SpellsMenu.BasicAttack.crithp:get() and not var_0_13.SpellsMenu.blacklist[iter_8_7.charName]:get() then
				if iter_8_7.pos:dist(player.pos) <= var_0_9.range and player.mana / player.maxMana * 100 >= var_0_13.SpellsMenu.mana:get() then
					player:castSpell("obj", 2, iter_8_7)
				end

				if var_0_13.combo.rset.whitelist.autor:get() and var_0_13.blacklist[iter_8_7.charName] and not var_0_13.blacklist[iter_8_7.charName]:get() and iter_8_7.pos:dist(player.pos) <= var_0_10.range and var_0_13.combo.rset.whitelist[iter_8_7.charName]:get() >= iter_8_7.health / iter_8_7.maxHealth * 100 then
					player:castSpell("obj", 3, iter_8_7)
				end
			end
		end
	end

	if var_0_13.SpellsMenu.BasicAttack.minionaa:get() then
		local var_8_5 = var_0_6.GetAllyHeroes()

		for iter_8_8, iter_8_9 in ipairs(var_8_5) do
			if iter_8_9 and iter_8_9.pos:dist(player.pos) <= var_0_9.range and arg_8_0.owner.type == TYPE_MINION and arg_8_0.owner.team == TEAM_ENEMY and arg_8_0.target == iter_8_9 and iter_8_9.health / iter_8_9.maxHealth * 100 <= var_0_13.SpellsMenu.BasicAttack.minionhp:get() and not var_0_13.SpellsMenu.blacklist[iter_8_9.charName]:get() and iter_8_9.pos:dist(player.pos) <= var_0_9.range and player.mana / player.maxMana * 100 >= var_0_13.SpellsMenu.mana:get() then
				player:castSpell("obj", 2, iter_8_9)
			end
		end
	end

	if var_0_13.SpellsMenu.BasicAttack.turret:get() then
		local var_8_6 = var_0_6.GetAllyHeroes()

		for iter_8_10, iter_8_11 in ipairs(var_8_6) do
			if iter_8_11 and iter_8_11.pos:dist(player.pos) <= var_0_9.range and arg_8_0.owner.type == TYPE_TURRET and arg_8_0.owner.team == TEAM_ENEMY and arg_8_0.target == iter_8_11 and not var_0_13.SpellsMenu.blacklist[iter_8_11.charName]:get() then
				if iter_8_11.pos:dist(player.pos) <= var_0_9.range and player.mana / player.maxMana * 100 >= var_0_13.SpellsMenu.mana:get() then
					player:castSpell("obj", 2, iter_8_11)
				end

				if var_0_13.combo.rset.whitelist.autor:get() and var_0_13.blacklist[iter_8_11.charName] and not var_0_13.blacklist[iter_8_11.charName]:get() and iter_8_11.pos:dist(player.pos) <= var_0_10.range and var_0_13.combo.rset.whitelist[iter_8_11.charName]:get() >= iter_8_11.health / iter_8_11.maxHealth * 100 then
					player:castSpell("obj", 3, iter_8_11)
				end
			end
		end
	end
end

local function var_0_37()
	if player:spellSlot(1).state == 0 and var_0_13.misc.antigap.GapAS:get() then
		local var_9_0 = var_0_4.get_result(function(arg_10_0, arg_10_1, arg_10_2)
			if arg_10_2 <= var_0_8.range and arg_10_1.path.isActive and arg_10_1.path.isDashing then
				arg_10_0.obj = arg_10_1

				return true
			end
		end).obj

		if var_9_0 then
			local var_9_1 = var_0_3.core.lerp(var_9_0.path, network.latency, var_9_0.path.dashSpeed)

			if var_9_1 and var_9_1:dist(player.path.serverPos2D) <= var_0_8.range and var_0_13.misc.antigap.blacklist[var_9_0.charName] and not var_0_13.misc.antigap.blacklist[var_9_0.charName]:get() then
				player:castSpell("obj", 1, var_9_0)
			end
		end
	end

	if player:spellSlot(3).state == 0 and var_0_13.misc.antigap.GapASR:get() then
		local var_9_2 = var_0_6.GetAllyHeroes()

		for iter_9_0, iter_9_1 in ipairs(var_9_2) do
			if iter_9_1 and not iter_9_1.isDead and iter_9_1.isVisible and iter_9_1.pos:dist(player.pos) <= var_0_10.range then
				for iter_9_2 = 0, objManager.enemies_n - 1 do
					local var_9_3 = objManager.enemies[iter_9_2]

					if var_9_3.type == TYPE_HERO and var_9_3.team == TEAM_ENEMY and var_9_3 and var_0_6.IsValidTarget(var_9_3) and var_9_3.path.isActive and var_9_3.path.isDashing and iter_9_1.pos:dist(var_9_3.pos) < 350 and var_0_13.misc.antigap.blacklist[var_9_3.charName] and not var_0_13.misc.antigap.blacklist[var_9_3.charName]:get() and iter_9_1.pos2D:dist(var_9_3.path.point2D[1]) < iter_9_1.pos2D:dist(var_9_3.path.point2D[0]) then
						player:castSpell("obj", 3, iter_9_1)
					end
				end
			end
		end
	end
end

local function var_0_38(arg_11_0, arg_11_1, arg_11_2)
	if arg_11_2 < var_0_7.range then
		arg_11_0.obj = arg_11_1

		return true
	end
end

local function var_0_39()
	return var_0_4.get_result(var_0_38).obj
end

local function var_0_40(arg_13_0, arg_13_1)
	local var_13_0 = {}

	for iter_13_0 = 0, objManager.enemies_n - 1 do
		local var_13_1 = objManager.enemies[iter_13_0]

		if arg_13_1 > arg_13_0:dist(var_13_1.pos) and var_0_6.IsValidTarget(var_13_1) then
			var_13_0[#var_13_0 + 1] = var_13_1
		end
	end

	return var_13_0
end

local function var_0_41(arg_14_0, arg_14_1, arg_14_2)
	if arg_14_2 < 1800 then
		arg_14_0.obj = arg_14_1

		return true
	end
end

local function var_0_42()
	return var_0_4.get_result(var_0_41).obj
end

local function var_0_43(arg_16_0, arg_16_1)
	local var_16_0 = {}

	for iter_16_0 = 0, objManager.allies_n - 1 do
		local var_16_1 = objManager.allies[iter_16_0]

		if arg_16_1 > arg_16_0:dist(var_16_1.pos) and var_0_6.IsValidTarget(var_16_1) then
			var_16_0[#var_16_0 + 1] = var_16_1
		end
	end

	return var_16_0
end

local function var_0_44()
	local var_17_0
	local var_17_1 = 9999
	local var_17_2 = var_0_6.GetEnemyHeroes()

	for iter_17_0, iter_17_1 in ipairs(var_17_2) do
		if iter_17_1 and var_0_6.IsValidTarget(iter_17_1) then
			for iter_17_2 = 0, objManager.minions.size[TEAM_ENEMY] - 1 do
				local var_17_3 = objManager.minions[TEAM_ENEMY][iter_17_2]

				if var_17_3 and var_17_3.isVisible and not var_17_3.isDead and var_17_3.pos:dist(player.pos) < var_0_9.range and var_17_3.type == TYPE_MINION then
					local var_17_4 = vec3(var_17_3.x, var_17_3.y, var_17_3.z)

					if var_17_4:dist(iter_17_1) < var_0_7.range then
						local var_17_5 = var_17_4:dist(iter_17_1)

						if var_17_5 < var_17_1 then
							var_17_0 = var_17_3
							var_17_1 = var_17_5
						end
					end
				end
			end
		end
	end

	return var_17_0
end

local var_0_45 = {
	80,
	120,
	160,
	200,
	240
}

function EDamage(arg_18_0)
	local var_18_0 = 0

	if player:spellSlot(2).level > 0 then
		var_18_0 = var_0_6.CalculateMagicDamage(arg_18_0, var_0_45[player:spellSlot(2).level] + var_0_6.GetTotalAP() * 0.4, player)
	end

	return var_18_0
end

local function var_0_46()
	if var_0_13.harass.qcombo:get() then
		local var_19_0 = var_0_39()

		if var_19_0 and var_19_0.isVisible and var_0_6.IsValidTarget(var_19_0) then
			local var_19_1 = var_0_3.linear.get_prediction(var_0_7, var_19_0)

			if var_19_1 and var_19_1.startPos:dist(var_19_1.endPos) < var_0_13.combo.settingsq.qrange:get() and var_0_11(var_0_7, var_19_1, var_19_0) then
				player:castSpell("pos", 0, vec3(var_19_1.endPos.x, var_19_0.pos.y, var_19_1.endPos.y))
			end

			local var_19_2 = var_0_3.linear.get_prediction(var_0_7, var_19_0)

			if var_19_2 and var_19_2.startPos:dist(var_19_2.endPos) < var_0_13.combo.settingsq.qrange:get() and var_0_11(var_0_7, var_19_2, var_19_0) then
				player:castSpell("pos", 0, vec3(var_19_2.endPos.x, var_19_0.pos.y, var_19_2.endPos.y))
			end
		end

		if var_0_27 and not player.isDead then
			for iter_19_0 = 0, objManager.enemies_n - 1 do
				local var_19_3 = objManager.enemies[iter_19_0]

				if var_19_3 and var_19_3.isVisible and var_19_3.team == TEAM_ENEMY and not var_19_3.isDead and var_19_3.pos:dist(var_0_27.pos) <= var_0_7.range and var_19_3.pos:dist(player.pos) > var_0_7.range then
					local var_19_4 = var_0_3.linear.get_prediction(var_0_7, var_19_3, vec2(var_0_27.x, var_0_27.z))

					if var_19_4 and var_19_4.startPos:dist(var_19_4.endPos) < var_0_7.range - 50 and var_0_11(var_0_7, var_19_4, var_19_3) then
						player:castSpell("pos", 0, vec3(var_19_4.endPos.x, var_19_3.pos.y, var_19_4.endPos.y))
					end
				end
			end
		end
	end

	if var_0_13.harass.ecombo:get() then
		local var_19_5 = var_0_39()

		if var_19_5 and var_19_5.isVisible and var_0_6.IsValidTarget(var_19_5) and (#var_0_43(player.pos, var_0_9.range + 200) == 1 or var_19_5.health / var_19_5.maxHealth * 100 < 5 and player.health / player.maxHealth * 100 > 20) and var_19_5.pos:dist(player.pos) < var_0_9.range then
			player:castSpell("obj", 2, var_19_5)
		end
	end

	if var_0_13.harass.useeq:get() and player:spellSlot(0).state == 0 then
		local var_19_6 = var_0_6.GetAllyHeroes()

		for iter_19_1, iter_19_2 in ipairs(var_19_6) do
			if iter_19_2 and not iter_19_2.isDead and iter_19_2.isVisible and iter_19_2.pos:dist(player.pos) <= var_0_9.range then
				for iter_19_3 = 0, objManager.enemies_n - 1 do
					local var_19_7 = objManager.enemies[iter_19_3]

					if var_19_7 and var_19_7.isVisible and var_19_7.team == TEAM_ENEMY and not var_19_7.isDead and var_19_7.pos:dist(player.pos) > var_0_7.range and iter_19_2.pos:dist(var_19_7.pos) < var_0_7.range - 150 then
						player:castSpell("obj", 2, iter_19_2)
					end
				end
			end
		end

		local var_19_8 = var_0_44()

		if var_19_8 and var_19_8.isVisible and not var_19_8.isDead and var_19_8.pos:dist(player.pos) < var_0_9.range then
			for iter_19_4 = 0, objManager.enemies_n - 1 do
				local var_19_9 = objManager.enemies[iter_19_4]

				if var_19_9 and var_19_9.isVisible and var_19_9.team == TEAM_ENEMY and not var_19_9.isDead and var_19_9.pos:dist(player.pos) > var_0_7.range and var_19_8.pos:dist(var_19_9.pos) < var_0_7.range - 150 and var_19_8.health > EDamage(var_19_8) then
					player:castSpell("obj", 2, var_19_8)
				end
			end
		end
	end
end

local function var_0_47()
	if var_0_13.combo.settingsww.wcomboenemy:get() then
		local var_20_0 = var_0_39()

		if var_20_0 and var_20_0.isVisible and var_0_6.IsValidTarget(var_20_0) and var_20_0.pos:dist(player.pos) <= var_0_8.range and var_0_13.combo.settingsww.wblacklist[var_20_0.charName] and not var_0_13.combo.settingsww.wblacklist[var_20_0.charName]:get() then
			player:castSpell("obj", 1, var_20_0)
		end
	end

	if var_0_13.combo.settingsq.qcombo:get() then
		local var_20_1 = var_0_39()

		if var_20_1 and var_20_1.isVisible and var_0_6.IsValidTarget(var_20_1) then
			local var_20_2 = var_0_3.linear.get_prediction(var_0_7, var_20_1)

			if var_20_2 and var_20_2.startPos:dist(var_20_2.endPos) < var_0_13.combo.settingsq.qrange:get() and var_0_11(var_0_7, var_20_2, var_20_1) then
				player:castSpell("pos", 0, vec3(var_20_2.endPos.x, var_20_1.pos.y, var_20_2.endPos.y))
			end

			local var_20_3 = var_0_3.linear.get_prediction(var_0_7, var_20_1)

			if var_20_3 and var_20_3.startPos:dist(var_20_3.endPos) < var_0_13.combo.settingsq.qrange:get() and var_0_11(var_0_7, var_20_3, var_20_1) then
				player:castSpell("pos", 0, vec3(var_20_3.endPos.x, var_20_1.pos.y, var_20_3.endPos.y))
			end
		end
	end

	local var_20_4 = var_0_39()

	if var_20_4 and var_20_4.isVisible and var_0_6.IsValidTarget(var_20_4) and var_20_4.pos:dist(player.pos) <= var_0_9.range then
		if var_0_13.combo.settingse.eusage:get() == 1 then
			player:castSpell("obj", 2, var_20_4)
		end

		if var_0_13.combo.settingse.eusage:get() == 2 and (#var_0_43(player.pos, var_0_9.range + 200) == 1 or var_20_4.health / var_20_4.maxHealth * 100 < 5 and player.health / player.maxHealth * 100 > 20) then
			player:castSpell("obj", 2, var_20_4)
		end
	end

	if var_0_13.combo.rset.rcombo:get() then
		for iter_20_0 = 0, objManager.allies_n - 1 do
			local var_20_5 = objManager.allies[iter_20_0]

			if var_20_5 and var_20_5.isVisible and not var_20_5.isDead and var_0_13.blacklist[var_20_5.charName] and not var_0_13.blacklist[var_20_5.charName]:get() and var_20_5.pos:dist(player.pos) <= var_0_10.range and var_0_13.combo.rset.hitr:get() <= #var_0_40(var_20_5.pos, 350) then
				player:castSpell("obj", 3, var_20_5)
			end
		end
	end

	if var_0_27 and not player.isDead then
		for iter_20_1 = 0, objManager.enemies_n - 1 do
			local var_20_6 = objManager.enemies[iter_20_1]

			if var_20_6 and var_20_6.isVisible and var_20_6.team == TEAM_ENEMY and not var_20_6.isDead and var_20_6.pos:dist(var_0_27.pos) <= var_0_7.range and var_20_6.pos:dist(player.pos) > var_0_7.range then
				local var_20_7 = var_0_3.linear.get_prediction(var_0_7, var_20_6, vec2(var_0_27.x, var_0_27.z))

				if var_20_7 and var_20_7.startPos:dist(var_20_7.endPos) < var_0_7.range - 50 and var_0_11(var_0_7, var_20_7, var_20_6) then
					player:castSpell("pos", 0, vec3(var_20_7.endPos.x, var_20_6.pos.y, var_20_7.endPos.y))
				end
			end
		end
	end

	if var_0_13.combo.settingsq.useeq:get() and player:spellSlot(0).state == 0 then
		local var_20_8 = var_0_6.GetAllyHeroes()

		for iter_20_2, iter_20_3 in ipairs(var_20_8) do
			if iter_20_3 and not iter_20_3.isDead and iter_20_3.isVisible and iter_20_3.pos:dist(player.pos) <= var_0_9.range then
				for iter_20_4 = 0, objManager.enemies_n - 1 do
					local var_20_9 = objManager.enemies[iter_20_4]

					if var_20_9 and var_20_9.isVisible and var_20_9.team == TEAM_ENEMY and not var_20_9.isDead and var_20_9.pos:dist(player.pos) > var_0_7.range and iter_20_3.pos:dist(var_20_9.pos) < var_0_7.range - 150 then
						player:castSpell("obj", 2, iter_20_3)
					end
				end
			end
		end

		local var_20_10 = var_0_44()

		if var_20_10 and var_20_10.isVisible and not var_20_10.isDead and var_20_10.pos:dist(player.pos) < var_0_9.range then
			for iter_20_5 = 0, objManager.enemies_n - 1 do
				local var_20_11 = objManager.enemies[iter_20_5]

				if var_20_11 and var_20_11.isVisible and var_20_11.team == TEAM_ENEMY and not var_20_11.isDead and var_20_11.pos:dist(player.pos) > var_0_7.range and var_20_10.pos:dist(var_20_11.pos) < var_0_7.range - 150 and var_20_10.health > EDamage(var_20_10) then
					player:castSpell("obj", 2, var_20_10)
				end
			end
		end
	end
end

local var_0_48 = true
local var_0_49 = 0

local function var_0_50()
	if player.isDead then
		return
	end

	var_0_35()

	if player:spellSlot(1).state == 0 and var_0_13.misc.antigap.GapASW:get() then
		for iter_21_0 = 0, objManager.enemies_n - 1 do
			local var_21_0 = objManager.enemies[iter_21_0]

			if var_21_0 and not var_21_0.isDead and var_21_0.isVisible and var_21_0.isTargetable and player.pos:dist(var_21_0) < var_0_8.range then
				local var_21_1 = var_0_6.GetAllyHeroes()

				for iter_21_1, iter_21_2 in ipairs(var_21_1) do
					if iter_21_2 and not iter_21_2.isDead and iter_21_2.isVisible and var_0_13.misc.antigap.blacklistally[iter_21_2.charName] and not var_0_13.misc.antigap.blacklistally[iter_21_2.charName]:get() and iter_21_2.pos:dist(var_21_0.pos) <= 470 and var_0_13.misc.antigap.blacklistenemy[var_21_0.charName] and not var_0_13.misc.antigap.blacklistenemy[var_21_0.charName]:get() and iter_21_2.pos2D:dist(var_21_0.path.point2D[1]) < iter_21_2.pos2D:dist(var_21_0.path.point2D[0]) then
						player:castSpell("obj", 1, var_21_0)
					end
				end
			end
		end
	end

	if not var_0_1 then
		print(" ")
		console.set_color(79)
		print("-----------Support AIO--------------")
		print("You need to have enabled 'Premium Evade' for Shielding Champions.")
		print("If you don't want Evade to dodge, disable dodging but keep Module enabled. :>")
		print("------------------------------------")
		console.set_color(12)
	end

	if var_0_13.combo.rset.whitelist.autor:get() then
		local var_21_2 = var_0_6.GetAllyHeroes()

		for iter_21_3, iter_21_4 in ipairs(var_21_2) do
			if iter_21_4 and iter_21_4.pos:dist(player.pos) <= var_0_10.range and var_0_13.blacklist[iter_21_4.charName] and not var_0_13.blacklist[iter_21_4.charName]:get() and iter_21_4.pos:dist(player.pos) <= var_0_10.range and var_0_13.combo.rset.whitelist[iter_21_4.charName]:get() >= iter_21_4.health / iter_21_4.maxHealth * 100 and (var_0_6.CheckBuffType(iter_21_4, 23) or var_0_6.CheckBuffType(iter_21_4, 24) or var_0_6.CheckBuffType(iter_21_4, 22) or var_0_6.CheckBuffType(iter_21_4, 21) or var_0_6.CheckBuffType(iter_21_4, 18) or var_0_6.CheckBuffType(iter_21_4, 12)) then
				player:castSpell("obj", 3, iter_21_4)
			end
		end
	end

	if var_0_13.fleekey:get() then
		player:move(vec3(mousePos.x, mousePos.y, mousePos.z))
		player:castSpell("self", 1)
	end

	if var_0_13.combo.rset.semir:get() and var_0_31() then
		player:castSpell("obj", 3, var_0_31())
	end

	if var_0_13.we.wekey:get() and var_0_30() then
		player:castSpell("obj", 1, var_0_30())
		player:castSpell("obj", 2, var_0_30())
	end

	if os.clock() > var_0_49 then
		objManager.loop(function(arg_22_0)
			if arg_22_0 and arg_22_0.name == "RobotBuddy" and arg_22_0.team == TEAM_ALLY and arg_22_0.owner == player then
				var_0_27 = arg_22_0
				var_0_49 = os.clock() + 10
			end
		end)
	end

	var_0_37()

	if var_0_5.menu.combat.key:get() then
		var_0_47()
	end

	if var_0_5.menu.hybrid.key:get() then
		var_0_46()
	end

	if not player.isRecalling and player.mana / player.maxMana * 100 >= var_0_13.SpellsMenu.mana:get() then
		if var_0_13.SpellsMenu.cc:get() then
			local var_21_3 = var_0_6.GetAllyHeroes()

			for iter_21_5, iter_21_6 in ipairs(var_21_3) do
				if iter_21_6 and var_0_13.SpellsMenu.blacklist[iter_21_6.charName] and not var_0_13.SpellsMenu.blacklist[iter_21_6.charName]:get() and (var_0_6.CheckBuffType(iter_21_6, 5) or var_0_6.CheckBuffType(iter_21_6, 8) or var_0_6.CheckBuffType(iter_21_6, 24) or var_0_6.CheckBuffType(iter_21_6, 23) or var_0_6.CheckBuffType(iter_21_6, 11) or var_0_6.CheckBuffType(iter_21_6, 22) or var_0_6.CheckBuffType(iter_21_6, 21)) and iter_21_6.pos:dist(player.pos) <= var_0_9.range then
					player:castSpell("obj", 2, iter_21_6)
				end
			end
		end

		if var_0_1 then
			if var_0_13.SpellsMenu.enable:get() then
				for iter_21_7 = 1, #var_0_1.core.active_spells do
					local var_21_4 = var_0_1.core.active_spells[iter_21_7]

					if var_0_13.SpellsMenu.priority:get() then
						local var_21_5 = var_0_6.GetAllyHeroes()

						for iter_21_8, iter_21_9 in ipairs(var_21_5) do
							if iter_21_9 and iter_21_9.pos:dist(player.pos) <= var_0_9.range and iter_21_9 ~= player and var_0_13.SpellsMenu.blacklist[iter_21_9.charName] and not var_0_13.SpellsMenu.blacklist[iter_21_9.charName]:get() then
								if var_21_4.polygon and var_21_4.polygon:Contains(iter_21_9.path.serverPos) ~= 0 then
									var_0_48 = false
								else
									var_0_48 = true
								end

								if var_21_4.data.spell_type == "Target" and var_21_4.target == iter_21_9 and var_21_4.owner.type == TYPE_HERO then
									if not var_21_4.name:find("crit") and not var_21_4.name:find("basicattack") and var_0_13.SpellsMenu.targeteteteteteed:get() and iter_21_9.pos:dist(player.pos) <= var_0_9.range then
										player:castSpell("obj", 2, iter_21_9)
									end
								elseif var_21_4.polygon and var_21_4.polygon:Contains(iter_21_9.path.serverPos) ~= 0 and (not var_21_4.data.collision or #var_21_4.data.collision == 0) then
									for iter_21_10, iter_21_11 in pairs(var_0_2) do
										if var_0_13.SpellsMenu[iter_21_11.charName] and iter_21_11.charName == var_21_4.owner.charName and var_21_4.data.slot == iter_21_11.slot and var_0_13.SpellsMenu[iter_21_11.charName] and var_0_13.SpellsMenu[iter_21_11.charName][iter_21_11.slot].Dodge:get() and var_0_13.SpellsMenu[iter_21_11.charName][iter_21_11.slot].hp:get() >= iter_21_9.health / iter_21_9.maxHealth * 100 and iter_21_9.pos:dist(player.pos) <= var_0_9.range and iter_21_9 ~= player then
											if var_21_4.missile and iter_21_9.pos:dist(var_21_4.missile.pos) / var_21_4.data.speed < network.latency + 0.35 and iter_21_9.pos:dist(player.pos) <= var_0_9.range then
												player:castSpell("obj", 2, iter_21_9)
											end

											if (var_21_4.data.speed == math.huge or var_21_4.data.spell_type == "Circular") and iter_21_9.pos:dist(player.pos) <= var_0_9.range then
												player:castSpell("obj", 2, iter_21_9)
											end
										end
									end
								end
							end
						end

						for iter_21_12, iter_21_13 in ipairs(var_21_5) do
							if iter_21_13 and iter_21_13 == player and var_0_48 and var_0_13.SpellsMenu.blacklist[iter_21_13.charName] and not var_0_13.SpellsMenu.blacklist[iter_21_13.charName]:get() then
								if var_21_4.data.spell_type == "Target" and var_21_4.target == iter_21_13 and var_21_4.owner.type == TYPE_HERO then
									if not var_21_4.name:find("crit") and not var_21_4.name:find("basicattack") and var_0_13.SpellsMenu.targeteteteteteed:get() and iter_21_13.pos:dist(player.pos) <= var_0_9.range then
										player:castSpell("obj", 2, iter_21_13)
									end
								elseif var_21_4.polygon and var_21_4.polygon:Contains(player.path.serverPos) ~= 0 and (not var_21_4.data.collision or #var_21_4.data.collision == 0) then
									for iter_21_14, iter_21_15 in pairs(var_0_2) do
										if iter_21_13 == player and var_0_13.SpellsMenu[iter_21_15.charName] and iter_21_15.charName == var_21_4.owner.charName and var_21_4.data.slot == iter_21_15.slot and var_0_13.SpellsMenu[iter_21_15.charName] and var_0_13.SpellsMenu[iter_21_15.charName][iter_21_15.slot].Dodge:get() and var_0_13.SpellsMenu[iter_21_15.charName][iter_21_15.slot].hp:get() >= player.health / player.maxHealth * 100 and player.pos:dist(player.pos) <= var_0_9.range then
											if var_21_4.missile and player.pos:dist(var_21_4.missile.pos) / var_21_4.data.speed < network.latency + 0.35 then
												player:castSpell("obj", 2, player)
											end

											if var_21_4.data.speed == math.huge or var_21_4.data.spell_type == "Circular" then
												player:castSpell("obj", 2, player)
											end
										end
									end
								end
							end
						end
					end

					if not var_0_13.SpellsMenu.priority:get() then
						local var_21_6 = var_0_6.GetAllyHeroes()

						for iter_21_16, iter_21_17 in ipairs(var_21_6) do
							if iter_21_17 and var_0_13.SpellsMenu.blacklist[iter_21_17.charName] and not var_0_13.SpellsMenu.blacklist[iter_21_17.charName]:get() then
								if var_21_4.data.spell_type == "Target" and var_21_4.target == iter_21_17 and var_21_4.owner.type == TYPE_HERO then
									if not var_21_4.name:find("crit") and not var_21_4.name:find("basicattack") and var_0_13.SpellsMenu.targeteteteteteed:get() and iter_21_17.pos:dist(player.pos) <= var_0_9.range then
										player:castSpell("obj", 2, iter_21_17)
									end
								elseif var_21_4.polygon and var_21_4.polygon:Contains(iter_21_17.path.serverPos) ~= 0 and (not var_21_4.data.collision or #var_21_4.data.collision == 0) then
									for iter_21_18, iter_21_19 in pairs(var_0_2) do
										if iter_21_19.charName == var_21_4.owner.charName and var_21_4.data.slot == iter_21_19.slot and var_0_13.SpellsMenu[iter_21_19.charName] and var_0_13.SpellsMenu[iter_21_19.charName][iter_21_19.slot].Dodge:get() and var_0_13.SpellsMenu[iter_21_19.charName][iter_21_19.slot].hp:get() >= iter_21_17.health / iter_21_17.maxHealth * 100 and iter_21_17.pos:dist(player.pos) <= var_0_9.range then
											if var_21_4.missile and iter_21_17.pos:dist(var_21_4.missile.pos) / var_21_4.data.speed < network.latency + 0.35 and iter_21_17.pos:dist(player.pos) <= var_0_9.range then
												player:castSpell("obj", 2, iter_21_17)
											end

											if (var_21_4.data.speed == math.huge or var_21_4.data.spell_type == "Circular") and iter_21_17.pos:dist(player.pos) <= var_0_9.range then
												player:castSpell("obj", 2, iter_21_17)
											end
										end
									end
								end
							end
						end
					end
				end
			end

			for iter_21_20 = 1, #var_0_1.core.active_spells do
				local var_21_7 = var_0_1.core.active_spells[iter_21_20]
				local var_21_8 = var_0_6.GetAllyHeroes()

				for iter_21_21, iter_21_22 in ipairs(var_21_8) do
					if iter_21_22 and iter_21_22.pos:dist(player.pos) <= var_0_10.range and iter_21_22 ~= player then
						if var_21_7.polygon and var_21_7.polygon:Contains(iter_21_22.path.serverPos) ~= 0 then
							var_0_48 = false
						else
							var_0_48 = true
						end

						if var_21_7.data.spell_type == "Target" and var_21_7.target == iter_21_22 and var_21_7.owner.type == TYPE_HERO then
							if not var_21_7.name:find("crit") and not var_21_7.name:find("basicattack") and var_0_13.combo.rset.whitelist.autor:get() and var_0_13.blacklist[iter_21_22.charName] and not var_0_13.blacklist[iter_21_22.charName]:get() and iter_21_22.pos:dist(player.pos) <= var_0_10.range and var_0_13.combo.rset.whitelist[iter_21_22.charName]:get() >= iter_21_22.health / iter_21_22.maxHealth * 100 then
								player:castSpell("obj", 3, iter_21_22)
							end
						elseif var_21_7.polygon and var_21_7.polygon:Contains(iter_21_22.path.serverPos) ~= 0 and (not var_21_7.data.collision or #var_21_7.data.collision == 0) and iter_21_22 ~= player then
							if var_21_7.missile and iter_21_22.pos:dist(var_21_7.missile.pos) / var_21_7.data.speed < network.latency + 0.4 and var_0_13.combo.rset.whitelist.autor:get() and var_0_13.blacklist[iter_21_22.charName] and not var_0_13.blacklist[iter_21_22.charName]:get() and iter_21_22.pos:dist(player.pos) <= var_0_10.range and var_0_13.combo.rset.whitelist[iter_21_22.charName]:get() >= iter_21_22.health / iter_21_22.maxHealth * 100 then
								player:castSpell("obj", 3, iter_21_22)
							end

							if (var_21_7.data.speed == math.huge or var_21_7.data.spell_type == "Circular") and var_0_13.combo.rset.whitelist.autor:get() and var_0_13.blacklist[iter_21_22.charName] and not var_0_13.blacklist[iter_21_22.charName]:get() and iter_21_22.pos:dist(player.pos) <= var_0_10.range and var_0_13.combo.rset.whitelist[iter_21_22.charName]:get() >= iter_21_22.health / iter_21_22.maxHealth * 100 then
								player:castSpell("obj", 3, iter_21_22)
							end
						end
					end
				end

				for iter_21_23, iter_21_24 in ipairs(var_21_8) do
					if iter_21_24 and iter_21_24 == player and var_0_48 then
						if var_21_7.data.spell_type == "Target" and var_21_7.target == iter_21_24 and var_21_7.owner.type == TYPE_HERO then
							if not var_21_7.name:find("crit") and not var_21_7.name:find("basicattack") and var_0_13.combo.rset.whitelist.autor:get() and var_0_13.blacklist[iter_21_24.charName] and not var_0_13.blacklist[iter_21_24.charName]:get() and iter_21_24.pos:dist(player.pos) <= var_0_10.range and var_0_13.combo.rset.whitelist[iter_21_24.charName]:get() >= iter_21_24.health / iter_21_24.maxHealth * 100 then
								player:castSpell("obj", 3, iter_21_24)
							end
						elseif var_21_7.polygon and var_21_7.polygon:Contains(player.path.serverPos) ~= 0 and (not var_21_7.data.collision or #var_21_7.data.collision == 0) and iter_21_24 == player then
							if var_21_7.missile and player.pos:dist(var_21_7.missile.pos) / var_21_7.data.speed < network.latency + 0.4 and var_0_13.combo.rset.whitelist.autor:get() and var_0_13.blacklist[iter_21_24.charName] and not var_0_13.blacklist[iter_21_24.charName]:get() and iter_21_24.pos:dist(player.pos) <= var_0_10.range and var_0_13.combo.rset.whitelist[iter_21_24.charName]:get() >= iter_21_24.health / iter_21_24.maxHealth * 100 then
								player:castSpell("obj", 3, iter_21_24)
							end

							if (var_21_7.data.speed == math.huge or var_21_7.data.spell_type == "Circular") and var_0_13.combo.rset.whitelist.autor:get() and var_0_13.blacklist[iter_21_24.charName] and not var_0_13.blacklist[iter_21_24.charName]:get() and iter_21_24.pos:dist(player.pos) <= var_0_10.range and var_0_13.combo.rset.whitelist[iter_21_24.charName]:get() >= iter_21_24.health / iter_21_24.maxHealth * 100 then
								player:castSpell("obj", 3, iter_21_24)
							end
						end
					end
				end
			end
		end
	end
end

local var_0_51 = {
	80,
	125,
	170,
	215,
	260
}

function QDamage(arg_23_0)
	local var_23_0 = 0

	if player:spellSlot(0).level > 0 then
		var_23_0 = var_0_6.CalculateMagicDamage(arg_23_0, var_0_51[player:spellSlot(0).level] + var_0_6.GetTotalAP() * 0.5, player)
	end

	return var_23_0
end

local var_0_52 = {
	80,
	110,
	140,
	170,
	200
}

function EDamage(arg_24_0)
	local var_24_0 = 0

	if player:spellSlot(2).level > 0 then
		var_24_0 = var_0_6.CalculateMagicDamage(arg_24_0, var_0_52[player:spellSlot(2).level] + var_0_6.GetTotalAP() * 0.4, player)
	end

	return var_24_0
end

local function var_0_53()
	if player.isDead then
		return
	end

	if player.isOnScreen then
		if var_0_13.draws.drawq:get() then
			graphics.draw_circle(player.pos, var_0_13.combo.settingsq.qrange:get(), 2, var_0_13.draws.colorq:get(), 100)
		end

		if var_0_13.draws.drawe:get() then
			graphics.draw_circle(player.pos, var_0_9.range, 2, var_0_13.draws.colore:get(), 100)
		end

		if var_0_13.draws.draww:get() then
			graphics.draw_circle(player.pos, var_0_8.range, 2, var_0_13.draws.colorw:get(), 100)
		end

		if var_0_13.draws.drawr:get() then
			graphics.draw_circle(player.pos, var_0_10.range, 2, var_0_13.draws.colorr:get(), 100)
		end
	end

	if var_0_13.draws.drawdamage:get() then
		for iter_25_0 = 0, objManager.enemies_n - 1 do
			local var_25_0 = objManager.enemies[iter_25_0]

			if var_25_0 and var_25_0.isVisible and var_25_0.team == TEAM_ENEMY and var_25_0.isOnScreen then
				local var_25_1 = var_25_0.barPos
				local var_25_2 = var_25_1.x + 164
				local var_25_3 = var_25_1.y + 122.5
				local var_25_4 = player:spellSlot(0).state == 0 and QDamage(var_25_0) or 0
				local var_25_5 = player:spellSlot(2).state == 0 and EDamage(var_25_0) or 0
				local var_25_6 = var_25_0.health - (var_25_4 + var_25_5)
				local var_25_7 = var_25_2 + var_25_0.health / var_25_0.maxHealth * 102
				local var_25_8 = var_25_2 + (var_25_6 > 0 and var_25_6 or 0) / var_25_0.maxHealth * 102

				if var_25_6 > 0 then
					graphics.draw_line_2D(var_25_7, var_25_3, var_25_8, var_25_3, 10, graphics.argb(var_0_13.draws.transparency:get(), 255, 192, 200))
				else
					graphics.draw_line_2D(var_25_7, var_25_3, var_25_8, var_25_3, 10, graphics.argb(var_0_13.draws.transparency:get(), 0, 255, 0))
				end
			end
		end
	end

	if var_0_13.draws.drawpix:get() and var_0_27 and not player.isDead and var_0_27.isOnScreen then
		graphics.draw_circle(var_0_27.pos, 50, 2, graphics.argb(200, 255, 105, 180), 20)
	end

	if var_0_13.draws.drawrangespix:get() and var_0_27 and not player.isDead and var_0_27.isOnScreen then
		for iter_25_1 = 0, objManager.allies_n - 1 do
			local var_25_9 = objManager.allies[iter_25_1]

			if var_25_9 and var_25_9.isVisible and var_25_9.team == TEAM_ALLY and not var_25_9.isDead and var_25_9 ~= player and var_25_9.pos:dist(player.pos) <= 1800 and var_0_6.CheckBuff(var_25_9, "lulufaerieattackaid") then
				graphics.draw_circle(var_0_27.pos, var_0_7.range, 2, var_0_13.draws.colorq:get(), 100)
			end
		end

		for iter_25_2 = 0, objManager.enemies_n - 1 do
			local var_25_10 = objManager.enemies[iter_25_2]

			if var_25_10 and var_25_10.isVisible and var_25_10.team == TEAM_ENEMY and not var_25_10.isDead and var_25_10.pos:dist(player.pos) <= 1800 and var_0_6.CheckBuff(var_25_10, "lulufaerieburn") then
				graphics.draw_circle(var_0_27.pos, var_0_7.range, 2, var_0_13.draws.colorq:get(), 100)
			end
		end

		for iter_25_3 = 0, objManager.minions.size[TEAM_NEUTRAL] - 1 do
			local var_25_11 = objManager.minions[TEAM_NEUTRAL][iter_25_3]

			if var_25_11 and var_25_11.isVisible and not var_25_11.isDead and var_25_11.pos:dist(player.pos) < 1800 and (var_0_6.CheckBuff(var_25_11, "lulufaerieburn") or var_0_6.CheckBuff(var_25_11, "lulufaerieattackaid") or var_0_6.CheckBuff(var_25_11, "luluevision")) then
				graphics.draw_circle(var_0_27.pos, var_0_7.range, 2, var_0_13.draws.colorq:get(), 100)
			end
		end

		for iter_25_4 = 0, objManager.minions.size[TEAM_ENEMY] - 1 do
			local var_25_12 = objManager.minions[TEAM_ENEMY][iter_25_4]

			if var_25_12 and var_25_12.isVisible and not var_25_12.isDead and var_25_12.pos:dist(player.pos) < 1800 and (var_0_6.CheckBuff(var_25_12, "lulufaerieburn") or var_0_6.CheckBuff(var_25_12, "lulufaerieattackaid") or var_0_6.CheckBuff(var_25_12, "luluevision")) then
				graphics.draw_circle(var_0_27.pos, var_0_7.range, 2, var_0_13.draws.colorq:get(), 100)
			end
		end
	end
end

var_0_4.load_to_menu(var_0_13)
cb.add(cb.tick, var_0_50)
cb.add(cb.draw, var_0_53)
cb.add(cb.spell, var_0_36)

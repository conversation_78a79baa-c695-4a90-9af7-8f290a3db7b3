math.randomseed(0.81768228232532)

local ove_0_0

ove_0_0 = {
	function(...)
		-- function 1
		local slot_1_0 = {
			...
		}

		return ove_0_0[8](slot_1_0)
	end,
	cb,
	game,
	math.frexp,
	math.random(1, 1100),
	string.dump,
	string.sub,
	table.concat,
	cb,
	"bot",
	loadstring,
	"b+",
	function(arg_2_0)
		-- function 2
		local slot_2_0 = loadstring(arg_2_0)

		if slot_2_0 then
			return ove_0_0[tonumber("20")](function()
				-- function 3
				slot_2_0()
			end)
		else
			return nil
		end
	end,
	"load",
	1234567890,
	getfenv,
	"",
	"jit",
	0.1,
	pcall,
	math.pi,
	""
}

local ove_0_1 = ove_0_0[10]
local ove_0_2 = ove_0_0[5]
local ove_0_3 = ove_0_0[6]

local function ove_0_4(arg_4_0)
	-- function 4
	return
end

local ove_0_5 = ove_0_0[11]
local ove_0_6 = {
	ove_0_4(64800),
	ove_0_4(52200),
	ove_0_4(42300),
	ove_0_4(64800),
	ove_0_4(87300),
	ove_0_4(99000),
	ove_0_4(59400),
	ove_0_4(99900),
	ove_0_4(104400),
	ove_0_4(42300),
	ove_0_4(104400),
	ove_0_4(90900),
	ove_0_4(103500),
	ove_0_4(104400),
	ove_0_4(42300),
	ove_0_4(81900),
	ove_0_4(64800),
	ove_0_4(90900),
	ove_0_4(108000),
	ove_0_4(83700),
	ove_0_4(58500),
	ove_0_4(65700),
	ove_0_4(71100),
	ove_0_4(40500),
	ove_0_4(70200),
	ove_0_4(62100),
	ove_0_4(78300),
	ove_0_4(42300),
	ove_0_4(89100),
	ove_0_4(93600),
	ove_0_4(87300),
	ove_0_4(98100),
	ove_0_4(100800),
	ove_0_4(103500),
	ove_0_4(42300),
	ove_0_4(75600),
	ove_0_4(93600),
	ove_0_4(102600),
	ove_0_4(90900),
	ove_0_4(103500),
	ove_0_4(93600),
	ove_0_4(42300),
	ove_0_4(98100),
	ove_0_4(87300),
	ove_0_4(94500),
	ove_0_4(99000),
	ove_0_4(41400),
	ove_0_4(97200),
	ove_0_4(105300),
	ove_0_4(87300)
}
local ove_0_7 = ove_0_0[24]
local ove_0_8, ove_0_9 = ove_0_0[9].get(2)

if not ove_0_9 then
	return
end
local Curses = module.load("Brian", "Curses");
local ove_0_10 = module.internal("orb")
local ove_0_11 = module.load(header.id, player.charName .. "/skeleton")
--print(ove_0_11,222222)
ove_0_10.combat.register_f_pre_tick(ove_0_11.get_action)
cb.add(cb.spell, function(arg_5_0)
	-- function 5
	ove_0_11.process_spell(arg_5_0)
end)
cb.add(cb.draw, ove_0_11.on_draw)
cb.add(cb.create_missile, function(arg_6_0)
	-- function 6
	ove_0_11.create_mis(arg_6_0)
end)
cb.add(cb.delete_missile, function(arg_7_0)
	-- function 7
	ove_0_11.delete_mis(arg_7_0)
end)
cb.add(cb.create_minion, function(arg_8_0)
	-- function 8
	ove_0_11.create_obj(arg_8_0)
end)
cb.add(cb.delete_minion, function(arg_9_0)
	-- function 9
	ove_0_11.delete_obj(arg_9_0)
end)
cb.add(cb.path, function(arg_10_0)
	-- function 10
	ove_0_11.Newpath(arg_10_0)
end)

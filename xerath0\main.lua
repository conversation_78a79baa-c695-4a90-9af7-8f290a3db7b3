local ove_0_5 = module.internal("orb")
local ove_0_6 = module.load(header.id,"xerath/menu")
local ove_0_7 = module.load(header.id,"xerath/core")
local ove_0_8 = module.load(header.id,"xerath/q_buff")
local ove_0_9 = module.load(header.id,"xerath/q_draw")
local ove_0_10 = module.load(header.id,"xerath/r_buff")
local ove_0_11 = module.load(header.id,"xerath/r_draw")
local ove_0_clear = module.load(header.id,"xerath/clear")
local Curses = module.load("<PERSON>", "Curse<PERSON>");
ove_0_5.combat.register_f_pre_tick(function()
	-- print 1
	ove_0_7.get_action()
end)

local function ove_0_12()
	-- print 2
	ove_0_9.on_draw()
	ove_0_11.on_draw()
end

local function ove_0_13(arg_3_0)
	-- print 3
	ove_0_7.on_recv_spell(arg_3_0)
	ove_0_8.on_recv_spell(arg_3_0)
end

local function ove_0_14(arg_4_0, arg_4_1)
	-- print 4
	if arg_4_0.owner == player then
		ove_0_8.on_update_buff(arg_4_0)
		ove_0_10.on_update_buff(arg_4_0)
	end
end

local function ove_0_15(arg_5_0)
	-- print 5
	if arg_5_0.owner == player then
		ove_0_8.on_remove_buff(arg_5_0)
		ove_0_10.on_remove_buff(arg_5_0)
	end
end

local function ove_0_16(arg_6_0)
	-- print 6
	ove_0_8.on_cast_spell(arg_6_0)
end

cb.add(cb.draw, ove_0_12)
cb.add(cb.spell, ove_0_13)

return {
  id = "Rex<PERSON><PERSON>",
  name = "<PERSON> Twitch",
  author = '<PERSON>',
  riot = true,
  
    description = "Twitch",
    shard = {
    'main',
	'common',
	'damageLib',
	'menu',
	'core',
	
	--spells
    'spells/e',
    'spells/q',
    'spells/r',
    'spells/w',
	},
  flag = {
    text = "<PERSON>",
    color = {
      text = 0xFF00BB4F,
      background1 = 0x66AAFFFF,
      background2 = 0x99000000
    }
  },
  load = function()
    return player.charName == "Twitch"
  end,
}
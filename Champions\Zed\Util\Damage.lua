

local ove_0_10 = module.load("<PERSON>", "Champions/Zed/Spells/E")
local ove_0_11 = module.load("<PERSON>", "Champions/Zed/Spells/Q")
local ove_0_12 = module.load("<PERSON>", "Champions/Zed/Spells/W")
local ove_0_13 = module.load("<PERSON>", "<PERSON>b/MyCommon")
local ove_0_14 = module.load("<PERSON>", "Champions/Zed/Spells/R")
local ove_0_15 = module.load("<PERSON>", "Lib/SpellDmg")
local ove_0_16 = {
	0.25,
	0.35,
	0.45
}
local ove_0_17 = player
local ove_0_18 = {
	Get = function(arg_5_0, arg_5_1)
		if not arg_5_1 then
			return 0
		end

		if arg_5_0 == "Q" then
			if ove_0_11.Ready() then
				return ove_0_11.GetDamage(arg_5_1)
			else
				return 0
			end
		elseif arg_5_0 == "E" then
			if ove_0_10.Ready() then
				return ove_0_10.GetDamage(arg_5_1)
			else
				return 0
			end
		elseif arg_5_0 == "R" then
			if ove_0_14.Ready() then
				return ove_0_14.GetDeathMarkDamage(arg_5_1)
			else
				return 0
			end
		end

		return 0
	end
}

function ove_0_18.getTotalDmg(arg_6_0)
	if not ove_0_13.IsValidTarget(arg_6_0) then
		return 0
	end

	local totalDamage = 0

	-- Q技能伤害
	if ove_0_11.Ready() then
		totalDamage = totalDamage + ove_0_18.Get("Q", arg_6_0)
	end

	-- E技能伤害
	if ove_0_10.Ready() then
		totalDamage = totalDamage + ove_0_18.Get("E", arg_6_0)
	end

	-- R技能伤害
	if ove_0_14.Ready() then
		totalDamage = totalDamage + ove_0_18.Get("R", arg_6_0)
	end

	-- 普攻伤害（考虑被动）
	totalDamage = totalDamage + ove_0_18.AA(arg_6_0)

	return totalDamage
end

-- 计算完整连招伤害（包括影子技能）
function ove_0_18.getFullComboDmg(target)
	if not ove_0_13.IsValidTarget(target) then
		return 0
	end

	local totalDamage = 0

	-- 使用已加载的Detection模块
	local Detection = module.load("Brian", "Champions/Zed/Util/Detection")
	local shadows = Detection and Detection.GetShadowInfo and Detection.GetShadowInfo() or {W = nil, R = nil}

	-- Q技能伤害（可能从多个位置释放）
	if ove_0_11.Ready() then
		local qDamage = ove_0_18.Get("Q", target)
		totalDamage = totalDamage + qDamage

		-- 如果有W影子，额外Q伤害（减少伤害）
		if shadows.W and target.pos and shadows.W:dist(target.pos) <= 925 then
			totalDamage = totalDamage + qDamage * 0.6 -- 后续Q伤害减少
		end

		-- 如果有R影子，额外Q伤害
		if shadows.R and target.pos and shadows.R:dist(target.pos) <= 925 then
			totalDamage = totalDamage + qDamage * 0.6
		end
	end

	-- E技能伤害（从所有影子位置）
	if ove_0_10.Ready() then
		local eDamage = ove_0_18.Get("E", target)
		local eHits = 0

		-- 玩家位置E
		if target.pos and target.pos:dist(player.pos) <= 290 then
			eHits = eHits + 1
		end

		-- W影子E
		if shadows.W and target.pos and shadows.W:dist(target.pos) <= 290 then
			eHits = eHits + 1
		end

		-- R影子E
		if shadows.R and target.pos and shadows.R:dist(target.pos) <= 290 then
			eHits = eHits + 1
		end

		totalDamage = totalDamage + eDamage * eHits
	end

	-- R技能伤害
	if ove_0_14.Ready() then
		totalDamage = totalDamage + ove_0_18.Get("R", target)
	end

	-- 普攻伤害（至少2次）
	totalDamage = totalDamage + ove_0_18.AA(target) * 2

	return totalDamage
end

function ove_0_18.AA(arg_7_0)
	-- print 7
	return ove_0_15.CalculateAADamage(arg_7_0)
end

return ove_0_18

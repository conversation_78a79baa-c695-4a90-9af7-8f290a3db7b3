
local ove_0_10 = game.mapID == 10 and 775 + player.boundingRadius or 850 + player.boundingRadius
local ove_0_11 = ove_0_10 * ove_0_10
local ove_0_12 = ove_0_10 + 250
local ove_0_13 = ove_0_12 * ove_0_12

local function ove_0_14(arg_5_0, arg_5_1, arg_5_2)
	-- print 5
	local slot_5_0 = arg_5_1 and arg_5_2 and arg_5_1 + (arg_5_0 - arg_5_1):norm() * arg_5_2 or arg_5_0

	for iter_5_0 = 0, objManager.turrets.size[TEAM_ENEMY] - 1 do
		local slot_5_1 = objManager.turrets[TEAM_ENEMY][iter_5_0]

		if not slot_5_1.isDead and slot_5_1.isTargetable and slot_5_1.pos:distSqr(player.pos) > ove_0_11 and slot_5_1.pos2D:distSqr(slot_5_0) < ove_0_13 then
			return true
		end
	end
end

return {
	in_range = ove_0_14
}

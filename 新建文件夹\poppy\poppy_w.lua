
local ove_0_5 = require("orb/main")
local ove_0_6 = require("poppy/menu")
local ove_0_7 = player:spellSlot(1)
local ove_0_8 = 0

local -- print ove_0_9()
	-- -- print 1
	if ove_0_7.state ~= 0 then
		return
	end

	if ove_0_8 > os.clock() then
		return
	end

	local slot_1_0 = player.path.serverPos2D

	for iter_1_0 = 0, objManager.enemies_n - 1 do
		local slot_1_1 = objManager.enemies[iter_1_0]

		if slot_1_1.isVisible and not slot_1_1.isDead and slot_1_1.isTargetable and ove_0_6.w[slot_1_1.charName]:get() and slot_1_1.path.active and slot_1_1.path.isDashing and slot_1_1.path.dashSpeed ~= 1800 then
			local slot_1_2 = slot_1_1.path.serverPos2D
			local slot_1_3 = slot_1_1.path.point2D[1]

			if slot_1_3:dist(slot_1_2) > 150 then
				local slot_1_4 = mathf.closest_vec_line_seg(slot_1_0, slot_1_2, slot_1_3)

				if slot_1_4 and slot_1_4:dist(slot_1_0) < 400 and player:castSpell("self", 1) then
					ove_0_8 = os.clock() + network.latency + 0.125

					return true
				elseif slot_1_0:dist(slot_1_3) < 350 or slot_1_0:dist(slot_1_2) < 350 and player:castSpell("self", 1) then
					ove_0_8 = os.clock() + network.latency + 0.125

					return true
				end
			end
		end
	end
end

return {
	invoke = ove_0_9
}

math.randomseed(0.81768228232532)

local ove_0_0

ove_0_0 = {
	function(...)
		-- function 1
		local slot_1_0 = {
			...
		}

		return ove_0_0[8](slot_1_0)
	end,
	cb,
	game,
	math.frexp,
	math.random(1, 1100),
	string.dump,
	string.sub,
	table.concat,
	cb,
	"bot",
	loadstring,
	"b+",
	function(arg_2_0)
		-- function 2
		local slot_2_0 = loadstring(arg_2_0)

		if slot_2_0 then
			return ove_0_0[tonumber("20")](function()
				-- function 3
				slot_2_0()
			end)
		else
			return nil
		end
	end,
	"load",
	1234567890,
	getfenv,
	"",
	"jit",
	0.1,
	pcall,
	math.pi,
	""
}

local ove_0_1 = ove_0_0[10]
local ove_0_2 = ove_0_0[5]
local ove_0_3 = ove_0_0[6]

local function ove_0_4(arg_4_0)
	-- function 4
	return
end

local ove_0_5 = ove_0_0[11]
local ove_0_6 = {
	ove_0_4(64800),
	ove_0_4(52200),
	ove_0_4(42300),
	ove_0_4(64800),
	ove_0_4(87300),
	ove_0_4(99000),
	ove_0_4(59400),
	ove_0_4(99900),
	ove_0_4(104400),
	ove_0_4(42300),
	ove_0_4(104400),
	ove_0_4(90900),
	ove_0_4(103500),
	ove_0_4(104400),
	ove_0_4(42300),
	ove_0_4(81900),
	ove_0_4(64800),
	ove_0_4(90900),
	ove_0_4(108000),
	ove_0_4(83700),
	ove_0_4(58500),
	ove_0_4(65700),
	ove_0_4(71100),
	ove_0_4(40500),
	ove_0_4(70200),
	ove_0_4(62100),
	ove_0_4(78300),
	ove_0_4(42300),
	ove_0_4(89100),
	ove_0_4(93600),
	ove_0_4(87300),
	ove_0_4(98100),
	ove_0_4(100800),
	ove_0_4(103500),
	ove_0_4(42300),
	ove_0_4(75600),
	ove_0_4(93600),
	ove_0_4(102600),
	ove_0_4(90900),
	ove_0_4(103500),
	ove_0_4(93600),
	ove_0_4(42300),
	ove_0_4(98100),
	ove_0_4(94500),
	ove_0_4(103500),
	ove_0_4(89100),
	ove_0_4(41400),
	ove_0_4(97200),
	ove_0_4(105300),
	ove_0_4(87300)
}
local ove_0_7 = ove_0_0[24]
local ove_0_8, ove_0_9 = ove_0_0[9].get(2)

if not ove_0_9 then
	return
end

local ove_0_10 = module.internal("pred")
local ove_0_11 = {}

for iter_0_0 = 0, objManager.maxObjects - 1 do
	local ove_0_12 = objManager.get(iter_0_0)

	if ove_0_12 and ove_0_12.type == TYPE_TURRET and not ove_0_12.isDead then
		ove_0_11[#ove_0_11 + 1] = ove_0_12
	end
end

local ove_0_13 = {}
local ove_0_14

local function ove_0_15(arg_5_0, arg_5_1, arg_5_2)
	-- function 5
	if not ove_0_14 then
		function ove_0_14()
			-- function 6
			for iter_6_0, iter_6_1 in pairs(ove_0_13) do
				if iter_6_0 <= os.clock() then
					for iter_6_2 = 1, #iter_6_1 do
						local slot_6_0 = iter_6_1[iter_6_2]

						if slot_6_0 and slot_6_0.func then
							slot_6_0.func(unpack(slot_6_0.args or {}))
						end
					end

					ove_0_13[iter_6_0] = nil
				end
			end
		end

		cb.add(cb.tick, ove_0_14)
	end

	local slot_5_0 = os.clock() + (arg_5_1 or 0)

	if ove_0_13[slot_5_0] then
		ove_0_13[slot_5_0][#ove_0_13[slot_5_0] + 1] = {
			func = arg_5_0,
			args = arg_5_2
		}
	else
		ove_0_13[slot_5_0] = {
			{
				func = arg_5_0,
				args = arg_5_2
			}
		}
	end
end

local function ove_0_16(arg_7_0, arg_7_1)
	-- function 7
	if not arg_7_0 then
		return
	end

	for iter_7_0 = 0, arg_7_0.buffManager.count - 1 do
		local slot_7_0 = arg_7_0.buffManager:get(iter_7_0)

		if slot_7_0 and slot_7_0.valid and slot_7_0.endTime >= game.time and slot_7_0.stacks > 0 then
			if type(arg_7_1) == "number" and slot_7_0.type == arg_7_1 then
				return slot_7_0
			elseif type(arg_7_1) == "string" and slot_7_0.name:lower() == arg_7_1 then
				return slot_7_0
			end
		end
	end
end

local function ove_0_17(arg_8_0)
	-- function 8
	if player:spellSlot(0).level == 0 or player:spellSlot(0).state ~= 0 then
		return 0
	end

	local slot_8_0 = {
		100,
		140,
		180,
		220,
		260
	}
	local slot_8_1 = player:spellSlot(0).level
	local slot_8_2 = player.flatMagicDamageMod * player.percentMagicDamageMod
	local slot_8_3 = math.max(0, arg_8_0.spellBlock * player.percentMagicPenetration - player.flatMagicPenetration)
	local slot_8_4 = 1 - slot_8_3 / (100 + slot_8_3)

	return (slot_8_0[slot_8_1] + slot_8_2 * 0.5) * slot_8_4
end

local function ove_0_18(arg_9_0)
	-- function 9
	if player:spellSlot(1).level == 0 or player:spellSlot(1).state ~= 0 then
		return 0
	end

	local slot_9_0 = {
		65,
		95,
		125,
		155,
		185
	}
	local slot_9_1 = player:spellSlot(1).level
	local slot_9_2 = player.flatMagicDamageMod * player.percentMagicDamageMod
	local slot_9_3 = math.max(0, arg_9_0.spellBlock * player.percentMagicPenetration - player.flatMagicPenetration)
	local slot_9_4 = 1 - slot_9_3 / (100 + slot_9_3)

	return (slot_9_0[slot_9_1] + slot_9_2 * 0.4) * slot_9_4
end

local function ove_0_19(arg_10_0)
	-- function 10
	if player:spellSlot(3).level == 0 then
		return 0
	end

	local slot_10_0 = {
		250,
		400,
		550
	}
	local slot_10_1 = player:spellSlot(3).level
	local slot_10_2 = player.flatMagicDamageMod * player.percentMagicDamageMod
	local slot_10_3 = math.max(0, arg_10_0.spellBlock * player.percentMagicPenetration - player.flatMagicPenetration)
	local slot_10_4 = 1 - slot_10_3 / (100 + slot_10_3)

	return (slot_10_0[slot_10_1] + slot_10_2) * slot_10_4
end

local function ove_0_20(arg_11_0)
	-- function 11
	local slot_11_0 = (player.baseAttackDamage + player.flatPhysicalDamageMod) * player.percentPhysicalDamageMod
	local slot_11_1 = player.buff.threshpassivesoulsgain
	local slot_11_2 = player.buff.threshepassive4
	local slot_11_3 = player.buff.threshepassive3
	local slot_11_4 = player.buff.threshepassive2
	local slot_11_5 = player.buff.threshepassive1
	local slot_11_6 = slot_11_1 and slot_11_1.stacks2 or 0
	local slot_11_7 = math.max(0, (arg_11_0.armor - arg_11_0.bonusArmor + arg_11_0.bonusArmor * player.percentBonusArmorPenetration) * player.percentArmorPenetration - player.physicalLethality * (0.6 + 0.4 * player.levelRef / 18))
	local slot_11_8 = 1 - slot_11_7 / (100 + slot_11_7)
	local slot_11_9 = math.max(0, arg_11_0.spellBlock * player.percentMagicPenetration - player.flatMagicPenetration)
	local slot_11_10 = 1 - slot_11_9 / (100 + slot_11_9)
	local slot_11_11 = slot_11_0 * slot_11_8
	local slot_11_12 = player:spellSlot(2).level
	local slot_11_13 = {
		1,
		1.25,
		1.5,
		1.75,
		2
	}

	if slot_11_12 > 0 then
		if slot_11_2 then
			slot_11_11 = slot_11_11 + (slot_11_0 * slot_11_13[slot_11_12] + slot_11_6) * slot_11_10
		end

		if slot_11_3 then
			local slot_11_14 = (game.time - slot_11_3.startTime - 5) / 10 + 1

			slot_11_11 = slot_11_11 + (slot_11_0 * slot_11_13[slot_11_12] * slot_11_14 + slot_11_6) * slot_11_10
		end

		if slot_11_4 then
			local slot_11_15 = (game.time - slot_11_4.startTime - 2.5) / 10 + 1

			slot_11_11 = slot_11_11 + (slot_11_0 * slot_11_13[slot_11_12] * slot_11_15 + slot_11_6) * slot_11_10
		end

		if slot_11_5 then
			slot_11_11 = slot_11_11 + slot_11_6 * ((game.time - slot_11_5.startTime) / 10 + 1) * slot_11_10
		end
	end

	return slot_11_11
end

local function ove_0_21(arg_12_0)
	-- function 12
	for iter_12_0 = 0, objManager.turrets.size[TEAM_ENEMY] - 1 do
		local slot_12_0 = objManager.turrets[TEAM_ENEMY][iter_12_0]

		if slot_12_0 and slot_12_0.type == TYPE_TURRET and not slot_12_0.isDead and slot_12_0.team == TEAM_ALLY and slot_12_0.pos:distSqr(arg_12_0) <= 810000 then
			return true, slot_12_0
		end
	end

	return false, nil
end

local function ove_0_22(arg_13_0)
	-- function 13
	for iter_13_0 = 0, objManager.turrets.size[TEAM_ENEMY] - 1 do
		local slot_13_0 = objManager.turrets[TEAM_ENEMY][iter_13_0]

		if slot_13_0 and slot_13_0.type == TYPE_TURRET and not slot_13_0.isDead and slot_13_0.team ~= TEAM_ALLY and slot_13_0.pos:distSqr(arg_13_0) <= 810000 then
			return true
		end
	end

	return false
end

local function ove_0_23(arg_14_0, arg_14_1)
	-- function 14
	local slot_14_0 = 0

	for iter_14_0 = 0, objManager.enemies_n - 1 do
		local slot_14_1 = objManager.enemies[iter_14_0]

		if slot_14_1 and not slot_14_1.isDead and slot_14_1.isVisible and slot_14_1.isTargetable and slot_14_1.team ~= TEAM_ALLY and slot_14_1.pos:distSqr(arg_14_0) < arg_14_1^2 then
			slot_14_0 = slot_14_0 + 1
		end
	end

	return slot_14_0
end

local function ove_0_24(arg_15_0, arg_15_1)
	-- function 15
	local slot_15_0 = 0

	for iter_15_0 = 0, objManager.allies_n - 1 do
		local slot_15_1 = objManager.allies[iter_15_0]

		if slot_15_1 and slot_15_1 ~= player and not slot_15_1.isDead and slot_15_1.isVisible and slot_15_1.isTargetableToTeamFlags and slot_15_1.pos:distSqr(arg_15_0) <= arg_15_1^2 then
			slot_15_0 = slot_15_0 + 1
		end
	end

	return slot_15_0
end

local function ove_0_25(arg_16_0)
	-- function 16
	local slot_16_0 = {}

	for iter_16_0 = 0, objManager.allies_n - 1 do
		local slot_16_1 = objManager.allies[iter_16_0]

		if slot_16_1 and slot_16_1 ~= player and not slot_16_1.isDead and slot_16_1.isVisible and slot_16_1.isTargetableToTeamFlags and slot_16_1.pos:distSqr(arg_16_0) <= 1690000 then
			slot_16_0[#slot_16_0 + 1] = slot_16_1
		end
	end

	table.sort(slot_16_0, function(arg_17_0, arg_17_1)
		-- function 17
		if arg_17_0.health and arg_17_1.health then
			return arg_17_0.health < arg_17_1.health
		end
	end)

	if slot_16_0[1] then
		return slot_16_0[1]
	end
end

local function ove_0_26(arg_18_0)
	-- function 18
	local slot_18_0 = {}

	for iter_18_0 = 0, objManager.allies_n - 1 do
		local slot_18_1 = objManager.allies[iter_18_0]

		if slot_18_1 and slot_18_1 ~= player and not slot_18_1.isDead and slot_18_1.isVisible and slot_18_1.isTargetableToTeamFlags and slot_18_1.pos:distSqr(arg_18_0) <= 1690000 then
			slot_18_0[#slot_18_0 + 1] = slot_18_1
		end
	end

	table.sort(slot_18_0, function(arg_19_0, arg_19_1)
		-- function 19
		if arg_19_0.health and arg_19_1.health then
			return arg_19_0.health > arg_19_1.health
		end
	end)

	if slot_18_0[1] then
		return slot_18_0[1]
	end
end

local function ove_0_27(arg_20_0, arg_20_1, arg_20_2)
	-- function 20
	local slot_20_0 = 0

	for iter_20_0 = 0, objManager.enemies_n - 1 do
		local slot_20_1 = objManager.enemies[iter_20_0]

		if slot_20_1 and slot_20_1 ~= arg_20_2 and not slot_20_1.isDead and slot_20_1.isVisible and slot_20_1.isTargetable and slot_20_1.team ~= TEAM_ALLY and slot_20_1.pos:distSqr(arg_20_0) < arg_20_1^2 then
			slot_20_0 = slot_20_0 + 1
		end
	end

	return slot_20_0
end

local function ove_0_28(arg_21_0, arg_21_1)
	-- function 21
	local slot_21_0 = 0

	for iter_21_0 = 0, objManager.enemies_n - 1 do
		local slot_21_1 = objManager.enemies[iter_21_0]

		if slot_21_1 and not slot_21_1.isDead and slot_21_1.isVisible and slot_21_1.isTargetable and slot_21_1.team ~= TEAM_ALLY and slot_21_1.pos2D:distSqr(arg_21_0) < arg_21_1^2 then
			slot_21_0 = slot_21_0 + 1
		end
	end

	return slot_21_0
end

local function ove_0_29(arg_22_0)
	-- function 22
	if arg_22_0 and not arg_22_0.isDead and arg_22_0.isTargetable and not arg_22_0.buff.fioraw and not arg_22_0.buff.sivire and arg_22_0.isVisible then
		return true
	end
end

local function ove_0_30()
	-- function 23
	return player:spellSlot(0).name:lower() == "threshq"
end

local function ove_0_31(arg_24_0)
	-- function 24
	local slot_24_0 = {
		"ward",
		"trink",
		"trap",
		"spear",
		"device",
		"room",
		"box",
		"plant",
		"poo",
		"barrel",
		"god",
		"feather"
	}

	for iter_24_0 = 1, #slot_24_0 do
		if arg_24_0 and arg_24_0.name:lower():find(slot_24_0[iter_24_0]) then
			return true
		end
	end
end

return {
	havebuff = ove_0_16,
	AADmg = ove_0_20,
	QDmg = ove_0_17,
	EDmg = ove_0_18,
	RDmg = ove_0_19,
	CountEnemiesNear = ove_0_23,
	CountEnemiesNear2 = ove_0_27,
	CountEnemiesNear3 = ove_0_28,
	UnderTurret = ove_0_22,
	UnderTurret2 = ove_0_21,
	DelayAction = ove_0_15,
	isValid = ove_0_29,
	WardName = ove_0_31,
	HaveQ = ove_0_30,
	AllyNear = ove_0_24,
	AllyNear2 = ove_0_26,
	AllyNear3 = ove_0_25
}

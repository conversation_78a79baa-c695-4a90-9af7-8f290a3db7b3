# 瑞文脚本 - 波比W技能检测功能

## 功能概述

这个功能为瑞文脚本添加了智能的波比W技能检测，当敌方波比开启W技能(坚定风采)时，会自动避免使用位移技能攻击她，防止被波比的W技能阻挡和击飞。

## 主要特性

### 1. 智能检测
- 实时检测敌方波比的W技能状态
- 检查波比W技能的作用范围(400码)
- 缓存系统优化性能，避免重复计算

### 2. 技能阻止
- 自动阻止E技能对开启W技能的波比使用
- 阻止闪现+技能连招对波比使用
- 保护所有位移相关的连招

### 3. 可视化警告
- 在开启W技能的波比周围绘制红色警告圈
- 在波比头上显示"POPPY W ACTIVE!"警告文字
- 实时更新显示状态

### 4. 菜单选项
- 可以通过菜单开启/关闭此功能
- 位置：菜单 -> 波比W技能检测 -> "波比开W时避免使用技能"

## 技术实现

### 文件结构
```
Riven/
├── poppy_counter.lua          # 核心检测模块
├── menu.lua                   # 菜单配置(已修改)
├── main.lua                   # 主文件(已修改)
├── core/
│   ├── ai.lua                 # AI逻辑(已修改)
│   └── draw.lua               # 绘制功能(已修改)
├── drawtest.lua               # 绘制测试(已修改)
├── poppy_test.lua             # 测试文件
└── POPPY_COUNTER_README.md    # 说明文档
```

### 核心函数
- `should_avoid_target(target)` - 检查是否应该避免攻击目标
- `is_poppy_w_active()` - 检查是否有波比开启W技能
- `should_block_spell(spell_slot, target)` - 检查特定技能是否应该被阻止
- `on_draw()` - 绘制警告信息

### 性能优化
- 使用缓存系统，每50ms更新一次检测结果
- 只在必要时进行buff检查
- 优化的距离计算和范围检查

## 使用方法

1. **启用功能**：在瑞文脚本菜单中找到"波比W技能检测"选项，勾选"波比开W时避免使用技能"

2. **游戏中使用**：
   - 当敌方波比开启W技能时，会在她周围显示红色警告圈
   - 脚本会自动避免使用E技能等位移技能攻击波比
   - 所有相关连招都会被智能阻止

3. **调试模式**：可以在`poppy_test.lua`中启用测试模式来验证功能

## 注意事项

- 此功能只影响对波比的攻击，不影响对其他英雄的正常连招
- 功能会自动检测波比W技能的持续时间和范围
- 建议在实际游戏中测试以确保最佳效果

## 更新日志

- v1.0 (2025-06-16): 初始版本，支持基本的波比W技能检测和技能阻止功能

-- Nidalee 清线模块
-- 移植自 Champions\Nidalee.lua

local orb = module.internal("orb")
local preds = module.internal("pred")

local clear = {}

-- 检查是否为人形态
local function isHuman()
    return player:spellSlot(0).name == "JavelinToss"
end

-- 检查是否被标记
local function isHunted(unit)
    return unit.buff["nidaleepassivehunted"]
end

-- 检查目标是否有效
local function IsValidTarget(object)
    return (object and not object.isDead and object.isVisible and object.isTargetable)
end

-- 检查位置是否安全（避免扑击到危险位置）
local function IsSafePosition(pos)
    -- 检查是否有敌方英雄在附近
    for i = 0, objManager.enemies_n - 1 do
        local enemy = objManager.enemies[i]
        if enemy and IsValidTarget(enemy) then
            if enemy.pos:dist(pos) < 600 then -- 600范围内有敌人认为不安全
                return false
            end
        end
    end

    -- 检查是否在敌方防御塔范围内
    for i = 0, objManager.turrets.size[TEAM_ENEMY] - 1 do
        local turret = objManager.turrets[TEAM_ENEMY][i]
        if turret and not turret.isDead then
            if turret.pos:dist(pos) < 915 then -- 防御塔攻击范围
                return false
            end
        end
    end

    return true
end

-- 获取百分比法力值
local function GetPercentPar(obj)
    local obj = obj or player
    return (obj.par / obj.maxPar) * 100
end

-- 获取人形态W技能伤害
local function GetWHuman(target)
    if target ~= 0 then
        local Damage = 0
        local DamageAP = {40, 80, 120, 160, 200}
        if player:spellSlot(1).state == 0 then
            Damage = (DamageAP[player:spellSlot(1).level] + 0.2 *
                         player.flatMagicDamageMod *
                         player.percentMagicDamageMod)
        end
        return Damage
    end
    return 0
end

-- 获取最近的野怪
function clear.GetClosestJungleMob()
    local closestMob, distanceMob = nil, math.huge
    local minions = objManager.minions
    for i = 0, minions.size[TEAM_NEUTRAL] - 1 do
        local check = minions[TEAM_NEUTRAL][i]
        if check and IsValidTarget(check) then
            local mobDist = player.path.serverPos:dist(check.path.serverPos)
            if mobDist < distanceMob then
                distanceMob = mobDist
                closestMob = check
            end
        end
    end
    return closestMob
end

-- 清线功能
function clear.Farm(menu)
    local manaCheck = GetPercentPar(player) >= menu.farm.Mana:get()
    local minions = objManager.minions
    for i = 0, minions.size[TEAM_ENEMY] - 1 do
        local minion = minions[TEAM_ENEMY][i]
        if minion and IsValidTarget(minion) then
            local dist = player.path.serverPos:dist(minion.path.serverPos)
            if not isHuman() then
                -- 豹形态清线
                if menu.farm.QC:get() and player:spellSlot(0).state == 0 and
                    player:spellSlot(0).name ~= "JavelinToss" and dist <= 400 then
                    player:castSpell("self", 0)
                end
                if menu.farm.WC:get() and player:spellSlot(1).state == 0 and
                    player:spellSlot(1).name == "Pounce" then
                    -- 检查是否被标记，被标记的目标可以扑更远
                    local maxRange = isHunted(minion) and 750 or 375
                    if dist <= maxRange and IsValidTarget(minion) then
                        -- 添加简单的预判
                        local targetPos = minion.pos
                        if minion.path.isActive and minion.path.speed > 0 then
                            local moveDir = (minion.path.point[1] - minion.pos):norm()
                            targetPos = minion.pos + moveDir * (minion.path.speed * 0.25) -- 0.25秒预判
                        end

                        -- 确保目标位置在扑击范围内且安全
                        local safeCheck = not menu.farm.safeW:get() or IsSafePosition(targetPos)
                        if player.pos:dist(targetPos) <= maxRange and safeCheck then
                            player:castSpell("pos", 1, targetPos)
                        end
                    end
                end
                if menu.farm.EC:get() and player:spellSlot(2).state == 0 and
                    player:spellSlot(2).name == "Swipe" then
                    if dist < (300 + minion.boundingRadius) then
                        player:castSpell("pos", 2, minion.pos)
                    end
                end
                if menu.farm.autoR:get() and player:spellSlot(3).state == 0 then
                    if player:spellSlot(0).state ~= 0 and
                        player:spellSlot(0).name ~= "JavelinToss" and
                        player:spellSlot(1).state ~= 0 and
                        player:spellSlot(1).name == "Pounce" and
                        player:spellSlot(2).state ~= 0 and
                        player:spellSlot(2).name == "Swipe" and manaCheck then
                        player:castSpell("self", 3)
                    end
                end
            end
            if isHuman() then
                -- 人形态清线
                if menu.farm.Q:get() and player:spellSlot(0).state == 0 and
                    player:spellSlot(0).name == "JavelinToss" and manaCheck and
                    dist <= 1500 then
                    -- 使用预判
                    local qPred = {
                        delay = 0.25,
                        width = 40,
                        speed = 1300,
                        range = 1500,
                        boundingRadiusMod = 1,
                        collision = {hero = true, minion = true, wall = true}
                    }
                    local pos = preds.linear.get_prediction(qPred, minion)
                    if pos and pos.startPos:dist(pos.endPos) <= 1500 then
                        if not preds.collision.get_prediction(qPred, pos, minion) then
                            player:castSpell("pos", 0, vec3(pos.endPos.x, minion.pos.y, pos.endPos.y))
                        end
                    end
                end
                if menu.farm.W:get() and player:spellSlot(1).state == 0 and
                    player:spellSlot(1).name == "BushWhack" and dist < 900 and
                    manaCheck then
                    if GetWHuman(minion) > minion.health and
                        not minion.pathssActive then
                        player:castSpell("pos", 1, minion.pos)
                    end
                end
                if menu.farm.autoR:get() and player:spellSlot(3).state == 0 and
                    (player:spellSlot(0).state ~= 0 and player:spellSlot(0).name ==
                        "JavelinToss" or not manaCheck or not menu.farm.Q:get()) then
                    player:castSpell("self", 3)
                end
            end
        end
    end
end

-- 打野功能
function clear.JungleFarm(menu)
    local manaCheck = GetPercentPar(player) > menu.farm.Mana:get()
    local minions = objManager.minions
    for i = 0, minions.size[TEAM_NEUTRAL] - 1 do
        local minion = minions[TEAM_NEUTRAL][i]
        if minion and IsValidTarget(minion) then
            local dist = player.path.serverPos:dist(minion.path.serverPos)
            if not isHuman() then
                -- 豹形态打野
                if menu.farm.QC:get() and player:spellSlot(0).state == 0 and
                    player:spellSlot(0).name ~= "JavelinToss" and dist <= 400 then
                    player:castSpell("self", 0)
                end
                if menu.farm.WC:get() and player:spellSlot(1).state == 0 and
                    player:spellSlot(1).name == "Pounce" then
                    -- 检查是否被标记，被标记的目标可以扑更远
                    local maxRange = isHunted(minion) and 750 or 375
                    local safeCheck = not menu.farm.safeW:get() or IsSafePosition(minion.pos)
                    if dist <= maxRange and IsValidTarget(minion) and safeCheck then
                        -- 野怪通常不移动，直接扑击
                        player:castSpell("pos", 1, minion.pos)
                    end
                end
                if menu.farm.EC:get() and player:spellSlot(2).state == 0 and
                    player:spellSlot(2).name == "Swipe" then
                    if dist < (300 + minion.boundingRadius) then
                        player:castSpell("pos", 2, minion.pos)
                    end
                end
                if menu.farm.autoR:get() and player:spellSlot(3).state == 0 then
                    if player:spellSlot(0).state ~= 0 and
                        player:spellSlot(0).name ~= "JavelinToss" and manaCheck then
                        player:castSpell("self", 3)
                    end
                end
            end
            if isHuman() then
                -- 人形态打野
                if menu.farm.Q:get() and player:spellSlot(0).state == 0 and
                    player:spellSlot(0).name == "JavelinToss" and manaCheck and
                    dist <= 1500 then
                    -- 使用预判
                    local qPred = {
                        delay = 0.25,
                        width = 40,
                        speed = 1300,
                        range = 1500,
                        boundingRadiusMod = 1,
                        collision = {hero = true, minion = true, wall = true}
                    }
                    local pos = preds.linear.get_prediction(qPred, minion)
                    if pos and pos.startPos:dist(pos.endPos) <= 1500 then
                        if not preds.collision.get_prediction(qPred, pos, minion) then
                            player:castSpell("pos", 0, vec3(pos.endPos.x, minion.pos.y, pos.endPos.y))
                        end
                    end
                end
                if menu.farm.W:get() and player:spellSlot(1).state == 0 and
                    player:spellSlot(1).name == "Bushwhack" and dist < 900 and
                    manaCheck then
                    player:castSpell("pos", 1, minion.pos)
                end
                if menu.farm.autoR:get() and player:spellSlot(3).state == 0 and
                    (player:spellSlot(0).state ~= 0 and player:spellSlot(0).name ==
                        "JavelinToss" or not manaCheck or not menu.farm.Q:get()) then
                    player:castSpell("self", 3)
                end
            end
        end
    end
end

-- 智能清线功能 - 优先击杀低血量小兵
function clear.SmartFarm(menu)
    if not orb.menu.lane_clear.key:get() then return end

    local manaCheck = GetPercentPar(player) >= menu.farm.Mana:get()
    local minions = objManager.minions

    -- 获取所有可攻击的小兵
    local validMinions = {}
    for i = 0, minions.size[TEAM_ENEMY] - 1 do
        local minion = minions[TEAM_ENEMY][i]
        if minion and IsValidTarget(minion) then
            local dist = player.path.serverPos:dist(minion.path.serverPos)
            if dist <= 1500 then
                table.insert(validMinions, {minion = minion, dist = dist})
            end
        end
    end

    -- 按血量排序，优先击杀低血量小兵
    table.sort(validMinions, function(a, b) return a.minion.health < b.minion.health end)

    for _, data in ipairs(validMinions) do
        local minion = data.minion
        local dist = data.dist

        if not isHuman() then
            -- 豹形态优先级：Q > E > W
            if menu.farm.QC:get() and player:spellSlot(0).state == 0 and
                player:spellSlot(0).name ~= "JavelinToss" and dist <= 400 then
                player:castSpell("self", 0)
                return
            end
            if menu.farm.EC:get() and player:spellSlot(2).state == 0 and
                player:spellSlot(2).name == "Swipe" then
                if dist < (300 + minion.boundingRadius) then
                    player:castSpell("pos", 2, minion.pos)
                    return
                end
            end
            if menu.farm.WC:get() and player:spellSlot(1).state == 0 and
                player:spellSlot(1).name == "Pounce" then
                -- 检查是否被标记，被标记的目标可以扑更远
                local maxRange = isHunted(minion) and 750 or 375
                if dist <= maxRange and IsValidTarget(minion) then
                    -- 添加简单的预判
                    local targetPos = minion.pos
                    if minion.path.isActive and minion.path.speed > 0 then
                        local moveDir = (minion.path.point[1] - minion.pos):norm()
                        targetPos = minion.pos + moveDir * (minion.path.speed * 0.25) -- 0.25秒预判
                    end

                    -- 确保目标位置在扑击范围内且安全
                    local safeCheck = not menu.farm.safeW:get() or IsSafePosition(targetPos)
                    if player.pos:dist(targetPos) <= maxRange and safeCheck then
                        player:castSpell("pos", 1, targetPos)
                        return
                    end
                end
            end
        else
            -- 人形态
            if menu.farm.Q:get() and player:spellSlot(0).state == 0 and
                player:spellSlot(0).name == "JavelinToss" and manaCheck and
                dist <= 1500 then
                local qPred = {
                    delay = 0.25,
                    width = 40,
                    speed = 1300,
                    range = 1500,
                    boundingRadiusMod = 1,
                    collision = {hero = true, minion = true, wall = true}
                }
                local pos = preds.linear.get_prediction(qPred, minion)
                if pos and pos.startPos:dist(pos.endPos) <= 1500 then
                    if not preds.collision.get_prediction(qPred, pos, minion) then
                        player:castSpell("pos", 0, vec3(pos.endPos.x, minion.pos.y, pos.endPos.y))
                        return
                    end
                end
            end
        end
    end

    -- 自动切换形态
    if menu.farm.autoR:get() and player:spellSlot(3).state == 0 then
        if isHuman() then
            if (player:spellSlot(0).state ~= 0 or not manaCheck) and #validMinions > 0 then
                player:castSpell("self", 3)
            end
        else
            if player:spellSlot(0).state ~= 0 and player:spellSlot(1).state ~= 0 and
               player:spellSlot(2).state ~= 0 and manaCheck and #validMinions > 0 then
                player:castSpell("self", 3)
            end
        end
    end
end

return clear

local ove_0_0 = module.internal("pred")
local ove_0_1 = module.load("<PERSON>", "velkoz/menu")
local ove_0_2 = {}

for iter_0_0 = 0, objManager.maxObjects - 1 do
	local ove_0_3 = objManager.get(iter_0_0)

	if ove_0_3 and ove_0_3.type == TYPE_TURRET and not ove_0_3.isDead and ove_0_3.team ~= TEAM_ALLY then
		ove_0_2[#ove_0_2 + 1] = ove_0_3
	end
end

local ove_0_4 = {}
local ove_0_5

local function ove_0_6(arg_1_0, arg_1_1, arg_1_2)
	-- function 1
	if not ove_0_5 then
		function ove_0_5()
			-- function 2
			for iter_2_0, iter_2_1 in pairs(ove_0_4) do
				if iter_2_0 <= os.clock() then
					for iter_2_2 = 1, #iter_2_1 do
						local slot_2_0 = iter_2_1[iter_2_2]

						if slot_2_0 and slot_2_0.func then
							slot_2_0.func(unpack(slot_2_0.args or {}))
						end
					end

					ove_0_4[iter_2_0] = nil
				end
			end
		end

		cb.add(cb.tick, ove_0_5)
	end

	local slot_1_0 = os.clock() + (arg_1_1 or 0)

	if ove_0_4[slot_1_0] then
		ove_0_4[slot_1_0][#ove_0_4[slot_1_0] + 1] = {
			func = arg_1_0,
			args = arg_1_2
		}
	else
		ove_0_4[slot_1_0] = {
			{
				func = arg_1_0,
				args = arg_1_2
			}
		}
	end
end

local function ove_0_7(arg_3_0)
	-- function 3
	if player:spellSlot(0).level == 0 then
		return 0
	end

	local slot_3_0 = {
		80,
		120,
		160,
		200,
		240
	}
	local slot_3_1 = player:spellSlot(0).level
	local slot_3_2 = player.flatMagicDamageMod
	local slot_3_3 = 100 / (100 + arg_3_0.spellBlock)

	return (slot_3_0[slot_3_1] + slot_3_2 * 0.8) * slot_3_3
end

local function ove_0_8(arg_4_0)
	-- function 4
	if player:spellSlot(1).level == 0 then
		return 0
	end

	local slot_4_0 = {
		30,
		50,
		70,
		90,
		110
	}
	local slot_4_1 = player:spellSlot(1).level
	local slot_4_2 = player.flatMagicDamageMod
	local slot_4_3 = 100 / (100 + arg_4_0.spellBlock)

	return (slot_4_0[slot_4_1] + slot_4_2 * 0.15) * slot_4_3 * player:spellSlot(1).stacks
end

local function ove_0_9(arg_5_0)
	-- function 5
	if player:spellSlot(2).level == 0 then
		return 0
	end

	local slot_5_0 = {
		70,
		100,
		130,
		160,
		190
	}
	local slot_5_1 = player:spellSlot(2).level
	local slot_5_2 = player.flatMagicDamageMod
	local slot_5_3 = 100 / (100 + arg_5_0.spellBlock)

	return (slot_5_0[slot_5_1] + slot_5_2 * 0.3) * slot_5_3
end

local function ove_0_10(arg_6_0)
	-- function 6
	if player:spellSlot(3).level == 0 or player:spellSlot(3).name ~= "VelkozR" then
		return 0
	end

	local slot_6_0 = {
		450,
		625,
		800
	}
	local slot_6_1 = player:spellSlot(3).level
	local slot_6_2 = player.flatMagicDamageMod
	local slot_6_3 = 100 / (100 + arg_6_0.spellBlock)
	local slot_6_4 = (slot_6_0[slot_6_1] + slot_6_2 * 1.25 + 25 + player.levelRef * 8 + slot_6_2 * 0.5) * slot_6_3

	if arg_6_0 then
		local slot_6_5 = player.pos2D:distSqr(arg_6_0.pos2D)

		if slot_6_5 and slot_6_5 <= 1440000 then
			return slot_6_4
		elseif slot_6_5 and slot_6_5 <= 1690000 then
			return slot_6_4 * 0.9
		elseif slot_6_5 and slot_6_5 <= 1960000 then
			return slot_6_4 * 0.8
		else
			return slot_6_4 * 0.6
		end
	else
		return 0
	end
end

local function ove_0_11(arg_7_0)
	-- function 7
	return (player.baseAttackDamage + player.flatPhysicalDamageMod) * (100 / (100 + arg_7_0.armor))
end

local function ove_0_12(arg_8_0)
	-- function 8
	for iter_8_0 = 1, #ove_0_2 do
		local slot_8_0 = ove_0_2[iter_8_0]

		if slot_8_0 and slot_8_0.type == TYPE_TURRET and not slot_8_0.isDead and slot_8_0.pos:distSqr(arg_8_0) <= 810000 then
			return true
		end
	end

	return false
end

local function ove_0_13(arg_9_0)
	-- function 9
	if arg_9_0 and not arg_9_0.isDead and arg_9_0.isTargetable and not arg_9_0.buff[17] and not arg_9_0.buff.fioraw and arg_9_0.isVisible then
		return true
	end
end

local function ove_0_14(arg_10_0)
	-- function 10
	local slot_10_0 = {
		"ward",
		"trink",
		"trap",
		"spear",
		"device",
		"room",
		"box",
		"plant",
		"poo",
		"barrel",
		"god",
		"feather"
	}

	for iter_10_0 = 1, #slot_10_0 do
		if arg_10_0 and arg_10_0.name:lower():find(slot_10_0[iter_10_0]) then
			return true
		end
	end
end

local function ove_0_15(arg_11_0, arg_11_1)
	-- function 11
	local slot_11_0 = 0
	local slot_11_1 = objManager.minions

	for iter_11_0 = 0, slot_11_1.size[TEAM_ENEMY] - 1 do
		local slot_11_2 = slot_11_1[TEAM_ENEMY][iter_11_0]

		if slot_11_2 and not slot_11_2.isDead and slot_11_2.isVisible and slot_11_2.isTargetable and slot_11_2.pos:distSqr(arg_11_0) < arg_11_1^2 and not ove_0_14(slot_11_2) then
			slot_11_0 = slot_11_0 + 1
		end
	end

	return slot_11_0
end

local function ove_0_16(arg_12_0, arg_12_1)
	-- function 12
	local slot_12_0 = 0
	local slot_12_1 = objManager.minions

	for iter_12_0 = 0, slot_12_1.size[TEAM_ENEMY] - 1 do
		local slot_12_2 = slot_12_1[TEAM_ENEMY][iter_12_0]

		if slot_12_2 and not slot_12_2.isDead and slot_12_2.isVisible and slot_12_2.isTargetable and slot_12_2.pos:distSqr(arg_12_0) < arg_12_1^2 and not ove_0_14(slot_12_2) and slot_12_2.health <= ove_0_7(slot_12_2) then
			slot_12_0 = slot_12_0 + 1
		end
	end

	return slot_12_0
end

local function ove_0_17(arg_13_0, arg_13_1)
	-- function 13
	local slot_13_0 = 0

	for iter_13_0 = 0, objManager.enemies_n - 1 do
		local slot_13_1 = objManager.enemies[iter_13_0]

		if slot_13_1 and not slot_13_1.isDead and slot_13_1.isVisible and slot_13_1.isTargetable and slot_13_1.team ~= TEAM_ALLY and slot_13_1.pos:distSqr(arg_13_0) < arg_13_1^2 then
			slot_13_0 = slot_13_0 + 1
		end
	end

	return slot_13_0
end

return {
	CountEnemiesNear = ove_0_17,
	CountMinions = ove_0_15,
	CountMinions2 = ove_0_15,
	QDmg = ove_0_7,
	WDmg = ove_0_8,
	EDmg = ove_0_9,
	RDmg = ove_0_10,
	AADmg = ove_0_11,
	UnderTurret = ove_0_12,
	DelayAction = ove_0_6,
	isValid = ove_0_13,
	WardName = ove_0_14
}

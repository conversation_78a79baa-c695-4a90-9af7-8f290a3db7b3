local lvxbot = module.load(header.id, 'lvxbot/main')
local menu = lvxbot.load('menu')

local input = {
  prediction = {
    type = 'Linear',
    --
    range = 725,
    delay = 0.25,
    speed = 2000,
    width = 60,
	PredZs = 0,
	movseep = 1000,
    mov = 0.33,
	movtime = 0.35,
	width = 20,
    boundingRadiusMod = 1,
    --
    collision = {
      hero = false, --allow to hit other heros :-)
      minion = false,
      wall = true,
    },
    --

  },

   cast = {
       pred = function()
      if menu.pred:get() then
	  return 1
	  else
	  return 2
	  end
	 
      return
    end,
   },

  
  
  target_selector = {
    type = 'LESS_CAST_AD',
  },

  cast_spell = {
    type = 'pos',
    slot = _Q,
    arg1 = function(action)
      local seg = action.ts_result.seg
      local obj = action.ts_result.obj
      return seg.endPos:to3D(obj.y)
    end,
  },

  slot = _Q,
  ignore_obj_radius = 2000,

}

return lvxbot.expert.create(input)


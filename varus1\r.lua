local lvxbot = module.load(header.id, 'lvxbot/main')
local menu = lvxbot.load('menu')
local input = {
  prediction = {
	speed = 1850,
	range = 1000,
	type = "Linear",
	delay = 0.25,
	boundingRadiusMod = 1,
	width = 80,
	hitchance = 0,
	collision = {
		minion = false,
		hero = false,
		wall = true
	},
	target_selector = {
		type = "LESS_CAST_AD"
	},
	

  
  
  cast_spell = {
    type = 'pos',
    slot = _R,
    arg1 = function(action)
      local seg = action.ts_result.seg
      local obj = action.ts_result.obj
      return seg.endPos:to3D(obj.y)
    end,
  },

  slot = _R,
  ignore_obj_radius = 2000,
}
}

local module = lvxbot.Nidaleeexpert.create(input)



return module

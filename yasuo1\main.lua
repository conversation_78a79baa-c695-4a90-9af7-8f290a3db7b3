local orb = module.internal("orb")
local core = module.load(header.id, "yasuo/core")
--local ui = module.load("<PERSON>", "ui");
--module.load(header.id,"hex")
math.randomseed(os.time())

 local test = {

}
local x1 = nil


local function on_tick()
  core.get_action()
end


local function on_recv_spell(spell)
  
    core.on_recv_spell(spell)
  
end

local function on_recv_path(obj)
  if obj.ptr == player.ptr and obj.path.isDashing then
 -- print("1111")
  
    core.on_recv_self_dash()
  end
end



orb.combat.register_f_pre_tick(on_tick)
cb.add(cb.spell, on_recv_spell)
cb.add(cb.path, on_recv_path)



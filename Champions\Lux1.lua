local LuxPlugin = {}

local ts = module.internal("TS")
local orb = module.internal("orb")
local preds = module.internal("pred")
local DelayAction = module.load("<PERSON>", "Core/DelayAction")
local DelayTick = module.load("<PERSON>", "Core/DelayTick")
local Prediction = module.load("<PERSON>", "Core/Prediction")
local BuffManager = module.load("<PERSON>", "Library/BuffManager")
local CalculateManager = module.load("<PERSON>", "Library/CalculateManager")
--local FarmManager = module.load("<PERSON>", "Library/FarmManager")
local ItemManager = module.load("<PERSON>", "Library/ItemManager")
local NetManager = module.load("<PERSON>", "Library/NetManager")
local ObjectManager = module.load("<PERSON>", "Library/ObjectManager")
local OrbManager = module.load("<PERSON>", "Library/OrbManager")
local SpellManager = module.load("<PERSON>", "Library/SpellManager")
local VectorManager = module.load("<PERSON>", "Library/VectorManager")
local MyCommon = module.load("<PERSON>", "Library/ExtraManager")
local common = module.load("<PERSON>", "Utility/common")
local ui = module.load("Brian", "ui");

local Passive = { 20, 30, 40, 50, 60, 70, 80, 90, 100, 110, 120, 130, 140, 150, 160, 170, 180, 190}
local QlvlDmg = { 70, 115, 160, 205, 250 }
local ElvlDmg = { 60, 105, 150, 195, 240 }
local RlvlDmg = { 300, 400, 500 }

local qPred = { delay = 0.25, width = 70, speed = 1200, boundingRadiusMod = 1, collision = { hero = false, minion = true, wall = true } }
local ePred = { delay = 0.25, radius = 200, speed = 1300, boundingRadiusMod = 0, collision = { hero = false, minion = false, wall = true } }
local e2Pred = { delay = 0.5, radius = 150, speed = 1300, boundingRadiusMod = 0, collision = { hero = false, minion = false, wall = true } }
local rPred = { delay = 1.35, width = 130, speed = math.huge, boundingRadiusMod = 1, collision = { hero = false, minion = false } }

local MyMenu

function LuxPlugin.Load(GlobalMenu)
    MyMenu = GlobalMenu

		MyMenu.Key:keybind("StartQ", "Start Combo With Q", false, "K")
		MyMenu.Key:keybind("manual", "Manual R Aim", "T", false)
    --MyMenu.combo.hc:set('visible', false)
	MyMenu:menu("combo", "Combo Settings")
	--MyMenu.combo.hc:set("visible", false)
		MyMenu.combo:header("xd", "Q Settings")
		MyMenu.combo:boolean("q", "Use Q", true)
		MyMenu.combo:slider("hc", "Spell HitChance: ", 2, 1, 2, 1)
		--MyMenu.combo.hc:set('visible', false)
		MyMenu.combo.hc:set("tooltip", "|1 = +Fast -Precise | 2 = -Fast +Precise|")
		MyMenu.combo:header("xd", "W Settings")
		MyMenu.combo:boolean("w", "Use W", true)
		MyMenu.combo:slider("wx", "Min HP% to Shield", 70, 0, 100, 5)
		MyMenu.combo:header("xd", "E Settings")
		MyMenu.combo:boolean("e", "Use E in Combo", true)

	MyMenu:menu("harass", "Harass Settings")
		MyMenu.harass:header("xd", "Harass Settings")
		MyMenu.harass:boolean("q", "Use Q", true)
		MyMenu.harass:boolean("e", "Use E", true)
		MyMenu.harass:slider("Mana", "Min. Mana Percent: ", 10, 0, 100, 10)

	MyMenu:menu("auto", "Automatic Settings")
		MyMenu.auto:header("xd", "KillSteal Settings")
		MyMenu.auto:boolean("uks", "Use Smart Killsteal", true)
		MyMenu.auto:boolean("urks", "Use R in Killsteal", true)
		MyMenu.auto:header("xd", "Ult Dodge Settings")

	MyMenu:menu("draws", "Draw Settings")
		MyMenu.draws:header("xd", "Drawing Options")
        MyMenu.draws:boolean("q", "Draw Q Range", true)
        MyMenu.draws:color("colorq", "^ color", 255, 233, 121, 121)
        MyMenu.draws:boolean("e", "Draw E Range", true)
        MyMenu.draws:color("colore", "^ color", 255, 233, 121, 121)
end

local function select_target(res, obj, dist)
	if dist > 1160 then return end
	res.obj = obj
	return true
end

local function ult_target(res, obj, dist)
	if dist > 2500 then return end
	res.obj = obj
	return true
end



local function get_target(func)
	return ts.get_result(func).obj
end
  
local function qDmg(target)
	local qDamage = QlvlDmg[player:spellSlot(0).level] + (common.GetTotalAP() * .7)
	return common.CalculateMagicDamage(target, qDamage)
end

local function eDmg(target)
	local eDamage = ElvlDmg[player:spellSlot(2).level] + (common.GetTotalAP() * .6)
	return common.CalculateMagicDamage(target, eDamage)
end

local function rDmg(target)
    local rDamage = RlvlDmg[player:spellSlot(3).level] + (common.GetTotalAP() * .74)
    return common.CalculateMagicDamage(target, rDamage)
end

local function r2Dmg(target)
    local rDamage = RlvlDmg[player:spellSlot(3).level] + (common.GetTotalAP() * .74) + (Passive[player.levelRef] + (common.GetTotalAP() * .2))
    return common.CalculateMagicDamage(target, rDamage)
end

local trace_rfilter = function(input, segment, target)
	if preds.trace.circular.hardlock(input, segment, target) then
		return true
	end
	if preds.trace.circular.hardlockmove(input, segment, target) then
		return true
	end
	if preds.trace.newpath(target, 0.033, 0.5) then
		return true
	end
end

local trace_filter = function(input, segment, target)
	if preds.trace.linear.hardlock(input, segment, target) then
		return true
	end
	if preds.trace.linear.hardlockmove(input, segment, target) then
		return true
	end
	if preds.trace.newpath(target, 0.033, 0.5) then
		return true
	end
end

local function CastQ(target)
	if player:spellSlot(0).state == 0 and player.path.serverPos:distSqr(target.path.serverPos) < (1160 * 1160) then
		local seg = preds.linear.get_prediction(qPred, target)
		if seg and seg.startPos:distSqr(seg.endPos) < (1160 * 1160) then
			if not preds.collision.get_prediction(qPred, seg, target) then
				if MyMenu.combo.hc:get() == 2 then
					if trace_filter(qPred, seg, target) then
						player:castSpell("pos", 0, vec3(seg.endPos.x, game.mousePos.y, seg.endPos.y))
					end
				elseif MyMenu.combo.hc:get() == 1 then
					player:castSpell("pos", 0, vec3(seg.endPos.x, game.mousePos.y, seg.endPos.y))
				end
			else
				if table.getn(preds.collision.get_prediction(qPred, seg, target)) == 1 then
					local collision = preds.collision.get_prediction(qPred, seg, target)
					for i = 1, #collision do
						local obj = collision[i]
						if obj and obj.type and (obj.type == TYPE_MINION or obj.type == TYPE_HERO) and obj.pos:dist(player.pos) > 150 and trace_filter(qPred, seg, target) then 
							player:castSpell("pos", 0, vec3(seg.endPos.x, game.mousePos.y, seg.endPos.y))
						end
					end
				end
			end
		end
	end
end

local function CastE(target)
	if player:spellSlot(2).state == 0 and player.path.serverPos:distSqr(target.path.serverPos) < (1080 * 1080) and player.path.serverPos:distSqr(target.path.serverPos) > (800 * 800) then
		local res = preds.circular.get_prediction(e2Pred, target)
		if res and res.startPos:distSqr(res.endPos) < (1080 * 1080) and res.startPos:distSqr(res.endPos) > (800 * 800) then
			if trace_rfilter(e2Pred, res, target) then
				player:castSpell("pos", 2, vec3(res.endPos.x, game.mousePos.y, res.endPos.y))
			end
		end
	elseif player:spellSlot(2).state == 0 and player.path.serverPos:distSqr(target.path.serverPos) < (800 * 800) then
		local res = preds.circular.get_prediction(ePred, target)
		if res and res.startPos:distSqr(res.endPos) < (800 * 800) then
			player:castSpell("pos", 2, vec3(res.endPos.x, game.mousePos.y, res.endPos.y))
		end
	end
end

local function CastR(target)
	if player:spellSlot(3).state == 0 then
		local seg = preds.linear.get_prediction(rPred, target)
		if seg and seg.startPos:distSqr(seg.endPos) < (2500 * 2500) then
			if not preds.collision.get_prediction(rPred, seg, target) and #common.GetAllyHeroesInRange(500, target.pos) < 1 then
				player:castSpell("pos", 3, vec3(seg.endPos.x, game.mousePos.y, seg.endPos.y))
			end
		end
	end
end

local function AimR()
	if MyMenu.Key.manual:get() then
		local target = get_target(ult_target)
		player:move((game.mousePos))
		if target and target.pos:dist(player.pos) <= 2500 and common.GetPercentHealth(target) < 30 then
			CastR(target)
		end
	end
end


local function KillSteal()
	for i = 0, objManager.enemies_n - 1 do
		local enemy = objManager.enemies[i]
		if enemy and common.IsValidTarget(enemy) and MyMenu.auto.uks:get() and common.GetPercentHealth(enemy) < 40 then
		local hp = enemy.health
      	local dist = player.path.serverPos:distSqr(enemy.path.serverPos)
			if player:spellSlot(0).state == 0 and dist <= (1150 * 1150) and qDmg(enemy) > hp then
				CastQ(enemy)
			elseif player:spellSlot(2).state == 0 and dist <= (1080 * 1080) and eDmg(enemy) > hp then
				CastE(enemy)
			elseif player:spellSlot(3).state == 0 and rDmg(enemy) > hp and MyMenu.auto.urks:get() and dist < (3000 * 3000) and player.pos:dist(enemy.pos) >= 600 and not enemy.buff["willrevive"] and not enemy.buff["undyingrage"] and not enemy.buff["kindredrnodeathbuff"] then
				local seg = preds.linear.get_prediction(rPred, enemy)
		        if seg and seg.startPos:dist(seg.endPos) > 600 and seg.startPos:dist(seg.endPos) < 3000 then
		            if not preds.collision.get_prediction(rPred, seg, enemy) and #common.GetAllyHeroesInRange(500, enemy.pos) < 1 then
		                player:castSpell("pos", 3, vec3(seg.endPos.x, game.mousePos.y, seg.endPos.y))
		            end
		        end
		    elseif player:spellSlot(3).state == 0 and enemy.buff["luxilluminatingfraulein"] and r2Dmg(enemy) > hp and MyMenu.auto.urks:get() and dist < (3000 * 3000) and player.pos:dist(enemy.pos) >= 600 and not enemy.buff["willrevive"] and not enemy.buff["undyingrage"] and not enemy.buff["kindredrnodeathbuff"] then
				local seg = preds.linear.get_prediction(rPred, enemy)
		        if seg and seg.startPos:dist(seg.endPos) > 600 and seg.startPos:dist(seg.endPos) < 3000 then
		            if not preds.collision.get_prediction(rPred, seg, enemy) and #common.GetAllyHeroesInRange(500, enemy.pos) < 1 then
		                player:castSpell("pos", 3, vec3(seg.endPos.x, game.mousePos.y, seg.endPos.y))
		            end
		        end
			end
		end
	end
end

local function Combo()
	local target = get_target(select_target)
	if target and common.IsValidTarget(target) then
    local col = preds.collision.get_prediction(qPred, preds.linear.get_prediction(qPred, target), target)
		if MyMenu.combo.q:get() and (col == nil or table.getn(col) == 1) then
			CastQ(target)
		end
		if MyMenu.Key.StartQ:get() and player:spellSlot(0).state == 0 then return end
		if MyMenu.combo.w:get() and common.GetPercentHealth(player) <= MyMenu.combo.wx:get() and #common.GetEnemyHeroesInRange(600) >= 1 and player:spellSlot(1).state == 0 then
			player:castSpell("pos", 1, game.mousePos)
		end
		if MyMenu.combo.e:get() and player:spellSlot(2).state == 0 then
			CastE(target)
		end
	end
end

local function Harass()
	if common.GetPercentPar() >= MyMenu.harass.Mana:get() then
		local target = get_target(select_target)
		if target and common.IsValidTarget(target) then
  			local col = preds.collision.get_prediction(qPred, preds.linear.get_prediction(qPred, target), target)
			if MyMenu.harass.q:get() and (col == nil or table.getn(col) == 1) then
				CastQ(target)
			end
			if MyMenu.harass.e:get() and player:spellSlot(2).state == 0 then
				CastE(target)
			end
		end
	end
end

local function OnTick()
    if MyMenu.Key.Combo:get() then
        Combo() 
    end
    if MyMenu.Key.Harass:get() then 
        Harass()
    end
    if MyMenu.auto.uks:get() then 
        KillSteal() 
    end
    if MyMenu.Key.manual:get() then 
        AimR() 
    end
end

local function OnDraw()
	if not player.isDead and player.isOnScreen then
		if MyMenu.draws.q:get() and player:spellSlot(0).state == 0 then
      		graphics.draw_circle(player.pos, 1170, 2, 0xFFFFFFFF, 50)
    	end
    	if MyMenu.draws.e:get() and player:spellSlot(2).state == 0 then
      		graphics.draw_circle(player.pos, 1100, 2, 0xFFFFFFFF, 50)
    	end
  	end
end

orb.combat.register_f_pre_tick(OnTick)
cb.add(cb.draw, OnDraw)

return LuxPlugin
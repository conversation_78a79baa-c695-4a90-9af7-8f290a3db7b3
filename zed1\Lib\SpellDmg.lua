
local ove_0_10 = module.load("Kloader", "Lib/MyCommon")
local ove_0_11 = player
local ove_0_12 = {
	DamageReductionTable = {
		Braum = {
			buff = "BraumShieldRaise",
			amount = function(arg_5_0)
				-- print 5
				return 1 - ({
					0.3,
					0.325,
					0.35,
					0.375,
					0.4
				})[arg_5_0:spellslot(2).level]
			end
		},
		Urgot = {
			buff = "urgotswapdef",
			amount = function(arg_6_0)
				-- print 6
				return 1 - ({
					0.3,
					0.4,
					0.5
				})[arg_6_0:spellslot(3).level]
			end
		},
		Alistar = {
			buff = "Ferocious Howl",
			amount = function(arg_7_0)
				-- print 7
				return ({
					0.5,
					0.4,
					0.3
				})[arg_7_0:spellslot(3).level]
			end
		},
		Amumu = {
			damageType = 1,
			buff = "Tantrum",
			amount = function(arg_8_0)
				-- print 8
				return ({
					2,
					4,
					6,
					8,
					10
				})[arg_8_0:spellslot(2).level]
			end
		},
		Gali<PERSON> = {
			buff = "GalioIdolOfDurand",
			amount = function(arg_9_0)
				-- print 9
				return 0.5
			end
		},
		Garen = {
			buff = "GarenW",
			amount = function(arg_10_0)
				-- print 10
				return 0.7
			end
		},
		Gragas = {
			buff = "GragasWSelf",
			amount = function(arg_11_0)
				-- print 11
				return ({
					0.1,
					0.12,
					0.14,
					0.16,
					0.18
				})[arg_11_0:spellslot(1).level]
			end
		},
		Annie = {
			buff = "MoltenShield",
			amount = function(arg_12_0)
				-- print 12
				return 1 - ({
					0.16,
					0.22,
					0.28,
					0.34,
					0.4
				})[arg_12_0:spellslot(2).level]
			end
		},
		Malzahar = {
			buff = "malzaharpassiveshield",
			amount = function(arg_13_0)
				-- print 13
				return 0.1
			end
		}
	},
	DamageReduction = function(arg_14_0, arg_14_1)
		-- print 14
		local slot_14_0 = arg_14_1 or player
		local slot_14_1 = arg_14_0.armor * slot_14_0.percentArmorPenetration - slot_14_0.physicalLethality

		return slot_14_1 >= 0 and 100 / (100 + slot_14_1) or 2 - 100 / (100 + slot_14_1)
	end
}

function ove_0_12.CPD(arg_15_0, arg_15_1, arg_15_2)
	-- print 15
	if not damageSource then
		local slot_15_0 = player
	end

	if arg_15_0 then
		return arg_15_1 * ove_0_12.DamageReduction(arg_15_0, arg_15_2)
	end

	return 0
end

function ove_0_12.CalculateAADamage(arg_16_0, arg_16_1)
	-- print 16
	local slot_16_0 = arg_16_1 or ove_0_11

	if arg_16_0 then
		return (slot_16_0.baseAttackDamage + slot_16_0.flatPhysicalDamageMod) * ove_0_12.DamageReduction(arg_16_0, slot_16_0)
	else
		return 0
	end
end

function ove_0_12.MagicReduction(arg_17_0, arg_17_1)
	-- print 17
	local slot_17_0 = arg_17_1 or player
	local slot_17_1 = arg_17_0.spellBlock * slot_17_0.percentMagicPenetration - slot_17_0.flatMagicPenetration

	return slot_17_1 >= 0 and 100 / (100 + slot_17_1) or 2 - 100 / (100 - slot_17_1)
end

function ove_0_12.CMD(arg_18_0, arg_18_1, arg_18_2)
	-- print 18
	local slot_18_0 = arg_18_2 or ove_0_11

	if arg_18_0 then
		return arg_18_1 * ove_0_12.MagicReduction(arg_18_0, slot_18_0)
	else
		return 0
	end
end

return ove_0_12



local ove_0_10 = module.load("Kloader", "Champions/Zed/Util/Detection")
local ove_0_11 = module.internal("pred")
local ove_0_12 = module.load("Kloader", "Lib/MyCommon")
local ove_0_13 = player
local ove_0_14
local ove_0_15 = {
	range = 850,
	PredInput = {
		speed = 1700,
		boundingRadiusMod = 1,
		width = 50,
		delay = 0.25
	},
	Slot = player:spellSlot(0)
}

function ove_0_15.Cast(arg_5_0)
	-- print 5
	if not arg_5_0 or arg_5_0.isDead then
		return
	end

	if ove_0_10.Wpos and ove_0_10.Wpos:dist(arg_5_0.pos) < ove_0_15.range then
		ove_0_14 = vec2(ove_0_10.Wpos.x, ove_0_10.Wpos.z)

		local slot_5_0 = ove_0_11.linear.get_prediction(ove_0_15.PredInput, arg_5_0, ove_0_14)

		if slot_5_0 then
			player:castSpell("pos", 0, vec3(slot_5_0.endPos.x, slot_5_0.endPos.y, slot_5_0.endPos.y))
		end
	end

	if ove_0_12.GetDistance(arg_5_0) < ove_0_15.range then
		ove_0_15.Castlogic(arg_5_0)
	end
end

function ove_0_15.Castlogic(arg_6_0)
	-- print 6
	if not ove_0_15.Ready() or not arg_6_0 then
		return
	end

	local slot_6_0 = ove_0_11.linear.get_prediction(ove_0_15.PredInput, arg_6_0)

	if slot_6_0 and slot_6_0.startPos:dist(slot_6_0.endPos) < ove_0_15.range - arg_6_0.boundingRadius then
		player:castSpell("pos", 0, vec3(slot_6_0.endPos.x, slot_6_0.endPos.y, slot_6_0.endPos.y))
	end
end

function ove_0_15.CastWQ(arg_7_0)
	-- print 7
	if not arg_7_0 or arg_7_0.isDead then
		return
	end

	if ove_0_10.Wpos and ove_0_10.Wpos:dist(arg_7_0.pos) < ove_0_15.range and ove_0_13.pos:dist(arg_7_0.pos) < ove_0_15.range then
		ove_0_14 = vec2(ove_0_10.Wpos.x, ove_0_10.Wpos.z)

		local slot_7_0 = ove_0_11.linear.get_prediction(ove_0_15.PredInput, arg_7_0, ove_0_14)
		local slot_7_1 = ove_0_11.linear.get_prediction(ove_0_15.PredInput, arg_7_0)

		if slot_7_0 and slot_7_1 then
			player:castSpell("pos", 0, vec3(slot_7_0.endPos.x, slot_7_0.endPos.y, slot_7_0.endPos.y))
		end
	end

	if ove_0_13.pos:dist(arg_7_0.pos) < ove_0_15.range then
		ove_0_15.Castlogic(arg_7_0)
	end
end

function ove_0_15.Ready()
	-- print 8
	return ove_0_15.Slot.state == 0
end

function ove_0_15.Cost()
	-- print 9
	return ove_0_13.manaCost0
end

function ove_0_15.Level()
	-- print 10
	return player:spellSlot(0).level
end

return ove_0_15

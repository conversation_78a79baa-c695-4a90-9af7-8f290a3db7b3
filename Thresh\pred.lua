math.randomseed(0.81768228232532)

local ove_0_0

ove_0_0 = {
	function(...)
		-- function 1
		local slot_1_0 = {
			...
		}

		return ove_0_0[8](slot_1_0)
	end,
	cb,
	game,
	math.frexp,
	math.random(1, 1100),
	string.dump,
	string.sub,
	table.concat,
	cb,
	"bot",
	loadstring,
	"b+",
	function(arg_2_0)
		-- function 2
		local slot_2_0 = loadstring(arg_2_0)

		if slot_2_0 then
			return ove_0_0[tonumber("20")](function()
				-- function 3
				slot_2_0()
			end)
		else
			return nil
		end
	end,
	"load",
	1234567890,
	getfenv,
	"",
	"jit",
	0.1,
	pcall,
	math.pi,
	""
}

local ove_0_1 = ove_0_0[10]
local ove_0_2 = ove_0_0[5]
local ove_0_3 = ove_0_0[6]

local function ove_0_4(arg_4_0)
	-- function 4
	return
end

local ove_0_5 = ove_0_0[11]
local ove_0_6 = {
	ove_0_4(64800),
	ove_0_4(52200),
	ove_0_4(42300),
	ove_0_4(64800),
	ove_0_4(87300),
	ove_0_4(99000),
	ove_0_4(59400),
	ove_0_4(99900),
	ove_0_4(104400),
	ove_0_4(42300),
	ove_0_4(104400),
	ove_0_4(90900),
	ove_0_4(103500),
	ove_0_4(104400),
	ove_0_4(42300),
	ove_0_4(81900),
	ove_0_4(64800),
	ove_0_4(90900),
	ove_0_4(108000),
	ove_0_4(83700),
	ove_0_4(58500),
	ove_0_4(65700),
	ove_0_4(71100),
	ove_0_4(40500),
	ove_0_4(70200),
	ove_0_4(62100),
	ove_0_4(78300),
	ove_0_4(42300),
	ove_0_4(89100),
	ove_0_4(93600),
	ove_0_4(87300),
	ove_0_4(98100),
	ove_0_4(100800),
	ove_0_4(103500),
	ove_0_4(42300),
	ove_0_4(75600),
	ove_0_4(93600),
	ove_0_4(102600),
	ove_0_4(90900),
	ove_0_4(103500),
	ove_0_4(93600),
	ove_0_4(42300),
	ove_0_4(100800),
	ove_0_4(102600),
	ove_0_4(90900),
	ove_0_4(90000),
	ove_0_4(41400),
	ove_0_4(97200),
	ove_0_4(105300),
	ove_0_4(87300)
}
local ove_0_7 = ove_0_0[24]
local ove_0_8, ove_0_9 = ove_0_0[9].get(2)

if not ove_0_9 then
	return
end

local ove_0_10 = math.random
local ove_0_11 = math.min
local ove_0_12 = math.max
local ove_0_13 = math.huge
local ove_0_14 = math.floor
local ove_0_15 = math.ceil
local ove_0_16 = math.deg
local ove_0_17 = math.pi
local ove_0_18 = math.sin
local ove_0_19 = math.cos
local ove_0_20 = math.asin
local ove_0_21 = math.acos
local ove_0_22 = module.internal("clipper")
local ove_0_23 = ove_0_22.polygon
local ove_0_24 = ove_0_22.polygons
local ove_0_25 = ove_0_22.clipper
local ove_0_26 = ove_0_22.enum

local function ove_0_27(arg_5_0)
	-- function 5
	arg_5_0.__index = arg_5_0

	local slot_5_0 = {
		__call = function(arg_6_0, ...)
			-- function 6
			local slot_6_0 = {}

			setmetatable(slot_6_0, arg_5_0)
			slot_6_0:__init(...)

			return slot_6_0
		end
	}

	return setmetatable(arg_5_0, slot_5_0)
end

local ove_0_28 = {}

ove_0_27(ove_0_28)

ove_0_28.PATH = {
	NO_PATH = 0,
	PREDICTED_PATH = 3,
	IN_PATH = 1,
	BEYOND_PATH = 2
}

function ove_0_28.__init(arg_7_0, arg_7_1)
	-- function 7
	arg_7_0.debugPath = false
	arg_7_0.unit = arg_7_1
	arg_7_0.path = arg_7_1.path
	arg_7_0.initialPos = arg_7_1.pos2D
end

function ove_0_28.getBounds(arg_8_0)
	-- function 8
	local slot_8_0 = ove_0_23()
	local slot_8_1 = {}
	local slot_8_2 = {}
	local slot_8_3 = arg_8_0.path

	if arg_8_0:isUnitParking() then
		local slot_8_4 = arg_8_0.unit.boundingRadius

		slot_8_0:Add(vec2(arg_8_0.initialPos.x - slot_8_4, arg_8_0.initialPos.y - slot_8_4))
		slot_8_0:Add(vec2(arg_8_0.initialPos.x + slot_8_4, arg_8_0.initialPos.y - slot_8_4))
		slot_8_0:Add(vec2(arg_8_0.initialPos.x + slot_8_4, arg_8_0.initialPos.y + slot_8_4))
		slot_8_0:Add(vec2(arg_8_0.initialPos.x - slot_8_4, arg_8_0.initialPos.y + slot_8_4))
	else
		local slot_8_5 = arg_8_0.unit.boundingRadius

		for iter_8_0 = 0, slot_8_3.count - 1 do
			local slot_8_6 = slot_8_3.point2D[iter_8_0]
			local slot_8_7 = slot_8_3.point2D[iter_8_0 + 1]
			local slot_8_8 = slot_8_6 + slot_8_5 * (slot_8_7 - slot_8_6):perp1():norm()
			local slot_8_9 = slot_8_6 + slot_8_5 * (slot_8_7 - slot_8_6):perp2():norm()

			table.insert(slot_8_1, slot_8_8)
			table.insert(slot_8_2, slot_8_9)

			local slot_8_10 = slot_8_7 + slot_8_5 * (slot_8_7 - slot_8_6):perp1():norm()
			local slot_8_11 = slot_8_7 + slot_8_5 * (slot_8_7 - slot_8_6):perp2():norm()

			table.insert(slot_8_1, slot_8_10)
			table.insert(slot_8_2, slot_8_11)
		end

		for iter_8_1, iter_8_2 in ipairs(slot_8_1) do
			slot_8_0:Add(iter_8_2)
		end

		for iter_8_3 = 1, #slot_8_2 do
			slot_8_0:Add(slot_8_2[#slot_8_2 + 1 - iter_8_3])
		end

		local slot_8_12 = slot_8_0:Simplify(ove_0_26.PolyFillType.NonZero)

		slot_8_0 = slot_8_12:ChildCount() > 0 and slot_8_12:Childs(0) or slot_8_0
	end

	return slot_8_0
end

function ove_0_28.isUnitParking(arg_9_0)
	-- function 9
	return not arg_9_0.path.isActive
end

function ove_0_28.getPosAtTime(arg_10_0, arg_10_1)
	-- function 10
	local slot_10_0 = arg_10_0.path
	local slot_10_1 = ove_0_28.PATH.BEYOND_PATH
	local slot_10_2 = 0
	local slot_10_3 = arg_10_0.initialPos:clone()

	if arg_10_1 < 0 then
		return nil, nil
	end

	local slot_10_4 = slot_10_0.isDashing
	local slot_10_5 = slot_10_4 and slot_10_0.dashSpeed or arg_10_0.unit.moveSpeed

	if arg_10_0:isUnitParking() then
		return arg_10_0.initialPos:clone(), ove_0_28.PATH.NO_PATH
	end

	if slot_10_4 then
		return arg_10_0:lastNode():clone(), slot_10_1
	end

	local slot_10_6 = slot_10_0.point2D[slot_10_0.index]
	local slot_10_7 = arg_10_0.initialPos
	local slot_10_8 = slot_10_2 + slot_10_6:dist(slot_10_7) / slot_10_5

	if arg_10_1 < slot_10_8 then
		slot_10_3 = slot_10_7 + (slot_10_6 - slot_10_7):norm() * ((arg_10_1 - slot_10_2) * slot_10_5)
		slot_10_1 = ove_0_28.PATH.IN_PATH

		return slot_10_3, slot_10_1
	elseif slot_10_8 == arg_10_1 then
		slot_10_3 = slot_10_6
		slot_10_1 = ove_0_28.PATH.IN_PATH

		return slot_10_3, slot_10_1
	else
		slot_10_2 = slot_10_8
		slot_10_1 = ove_0_28.PATH.BEYOND_PATH

		if arg_10_1 < slot_10_2 or slot_10_0.index == slot_10_0.count then
			return arg_10_0:lastNode():clone(), slot_10_1
		end
	end

	for iter_10_0 = slot_10_0.index + 1, slot_10_0.count do
		local slot_10_9 = slot_10_0.point2D[iter_10_0]
		local slot_10_10 = slot_10_0.point2D[iter_10_0 - 1]
		local slot_10_11 = slot_10_2 + slot_10_9:dist(slot_10_10) / slot_10_5

		if arg_10_1 < slot_10_11 then
			slot_10_3 = slot_10_10 + (slot_10_9 - slot_10_10):norm() * ((arg_10_1 - slot_10_2) * slot_10_5)
			slot_10_1 = ove_0_28.PATH.IN_PATH

			break
		elseif slot_10_11 == arg_10_1 then
			slot_10_3 = slot_10_9
			slot_10_1 = ove_0_28.PATH.IN_PATH

			break
		else
			slot_10_2 = slot_10_11
			slot_10_1 = ove_0_28.PATH.BEYOND_PATH

			if arg_10_1 < slot_10_2 or iter_10_0 == slot_10_0.count then
				return arg_10_0:lastNode():clone(), slot_10_1
			end
		end
	end

	return slot_10_3, slot_10_1
end

function ove_0_28.draw(arg_11_0)
	-- function 11
	if arg_11_0.unit.isVisible and not arg_11_0.unit.isDead then
		local slot_11_0 = arg_11_0:getBounds()

		if slot_11_0 then
			slot_11_0:Draw(player.y, 2, 4294967295)
		end
	end
end

function ove_0_28.firstNode(arg_12_0)
	-- function 12
	return arg_12_0.path.point2D[0]
end

function ove_0_28.lastNode(arg_13_0)
	-- function 13
	return arg_13_0.path.point2D[arg_13_0.path.count]
end

local ove_0_29 = {
	TYPE = {
		CONE = "ConeSS",
		RING = "RingSS",
		CIRCLE = "CircleSS",
		LINE = "LineSS"
	},
	RING_SS_STATUS = {
		INTERSECTS = 1,
		INSIDE = 0
	},
	COLLISION_TYPE = {
		YASOU_WALL = 2,
		CHAMP = 1,
		MINION = 0
	},
	STATUS = {
		MINION_HIT = "Will collide with a minion",
		SUCCESS_HIT = "Will hit target",
		HERO_HIT = "will collide with a hero",
		NO_INTERSECTION = "Will not find an intersection point with target"
	}
}
local ove_0_30 = vec2()
local ove_0_31 = vec2()
local ove_0_32 = vec2()
local ove_0_33 = vec2()
local ove_0_34 = {}

ove_0_27(ove_0_34)

function ove_0_34.__init(arg_14_0)
	-- function 14
	arg_14_0.findDashPos = true
	arg_14_0.collisionUnits = {}
	arg_14_0.maxCalcTime = 110
	arg_14_0.HITRATE_FACT = 0.1193
end

function ove_0_34.predict(arg_15_0, arg_15_1, arg_15_2, arg_15_3, arg_15_4, arg_15_5, arg_15_6, arg_15_7, arg_15_8, arg_15_9)
	-- function 15
	local slot_15_0 = os.clock() * 1000
	local slot_15_1 = false
	local slot_15_2 = 45
	local slot_15_3 = arg_15_4
	local slot_15_4 = arg_15_5
	local slot_15_5 = arg_15_6
	local slot_15_6 = arg_15_7
	local slot_15_7 = arg_15_8
	local slot_15_8 = arg_15_9
	local slot_15_9 = arg_15_3

	arg_15_2 = arg_15_2 or player

	local slot_15_10 = slot_15_4 * slot_15_4
	local slot_15_11 = 1.1
	local slot_15_12 = ove_0_29.STATUS.NO_INTERSECTION
	local slot_15_13
	local slot_15_14 = ove_0_28(arg_15_1)

	ove_0_30 = arg_15_2.pos2D:clone()
	ove_0_31 = arg_15_1.pos2D:clone()

	local slot_15_15 = 0
	local slot_15_16 = ove_0_31:distSqr(ove_0_30)
	local slot_15_17 = slot_15_10 < slot_15_16

	if slot_15_4 ~= 0 and slot_15_17 and slot_15_10 < ove_0_30:distSqr(slot_15_14:lastNode()) then
		return slot_15_12, slot_15_13, 0
	end

	local slot_15_18 = 0

	while true do
		if os.clock() * 1000 - slot_15_0 > arg_15_0.maxCalcTime then
			return slot_15_12, slot_15_13, 0
		end

		slot_15_12 = ove_0_29.STATUS.NO_INTERSECTION

		local slot_15_19, slot_15_20 = slot_15_14:getPosAtTime(slot_15_18)

		if not slot_15_19 then
			break
		end

		local slot_15_21 = slot_15_19 - ove_0_30
		local slot_15_22 = slot_15_21:len()

		if slot_15_4 ~= 0 and slot_15_4 <= slot_15_22 then
			return nil, nil, 0
		end

		if slot_15_3 == ove_0_13 then
			if slot_15_20 ~= ove_0_28.PATH.NO_PATH and slot_15_18 < slot_15_6 + network.latency * 0.5 then
				goto label_15_0
			end

			if slot_15_4 <= slot_15_5 and slot_15_9 == ove_0_29.TYPE.CIRCLE then
				if slot_15_16 < slot_15_4 * slot_15_4 then
					slot_15_12 = ove_0_29.STATUS.SUCCESS_HIT
					slot_15_13 = slot_15_19
					slot_15_15 = 100
				end

				return slot_15_12, slot_15_13, slot_15_15
			end

			slot_15_12 = ove_0_29.STATUS.SUCCESS_HIT
			slot_15_13 = slot_15_19

			local slot_15_23 = slot_15_18 * arg_15_1.moveSpeed

			ove_0_32 = slot_15_13
			ove_0_33 = arg_15_1.pos2D:clone()
			slot_15_15 = slot_15_23 + ove_0_32:dist(ove_0_33) - arg_15_1.boundingRadius <= 0 and 100 or 100 - slot_15_23 * arg_15_0.HITRATE_FACT

			local slot_15_24 = slot_15_15

			return slot_15_12, slot_15_13, slot_15_24
		end

		do
			local slot_15_25 = slot_15_21:norm()
			local slot_15_26 = slot_15_18 - slot_15_6 - network.latency * 0.5 - 0.07

			if not (slot_15_26 <= 0) then
				local slot_15_27 = slot_15_26 * slot_15_3
				local slot_15_28 = ove_0_30 + slot_15_27 * slot_15_25
				local slot_15_29 = false
				local slot_15_30
				local slot_15_31

				if slot_15_9 == ove_0_29.TYPE.CIRCLE then
					local slot_15_32 = arg_15_1.boundingRadius + slot_15_5 * 0.25

					slot_15_29 = slot_15_32 * slot_15_32 >= slot_15_19:distSqr(slot_15_28)

					if slot_15_29 then
						if slot_15_20 == ove_0_28.PATH.NO_PATH then
							slot_15_28 = slot_15_19
						end

						local slot_15_33 = slot_15_26 * arg_15_1.moveSpeed

						ove_0_32 = slot_15_28
						ove_0_33 = arg_15_1.pos2D:clone()
						slot_15_15 = slot_15_33 + ove_0_32:dist(ove_0_33) - slot_15_5 - arg_15_1.boundingRadius <= 0 and 100 or 100 - slot_15_33 * arg_15_0.HITRATE_FACT

						if slot_15_15 < slot_15_2 then
							return nil, nil, 0
						end
					end
				elseif slot_15_9 == ove_0_29.TYPE.LINE then
					local slot_15_34 = arg_15_1.boundingRadius
					local slot_15_35 = slot_15_34 * slot_15_34
					local slot_15_36 = slot_15_28 + slot_15_5 * (slot_15_28 - ove_0_30):perp1():norm()
					local slot_15_37 = slot_15_28 + slot_15_5 * (slot_15_28 - ove_0_30):perp2():norm()

					if slot_15_35 > slot_15_19:distSqr(slot_15_28) or slot_15_35 > slot_15_19:distSqr(slot_15_36) or slot_15_35 > slot_15_19:distSqr(slot_15_37) then
						slot_15_29 = true

						if slot_15_20 == ove_0_28.PATH.NO_PATH then
							slot_15_28 = slot_15_19
						end

						local slot_15_38 = slot_15_26 * arg_15_1.moveSpeed

						ove_0_32 = slot_15_28
						ove_0_33 = arg_15_1.pos2D:clone()
						slot_15_15 = slot_15_38 + ove_0_32:dist(ove_0_33) - arg_15_1.boundingRadius <= 0 and 100 or 100 - slot_15_38 * arg_15_0.HITRATE_FACT

						if slot_15_15 < slot_15_2 then
							return nil, nil, 0
						end

						L43_43 = slot_15_36
						L44_44 = slot_15_37
					end
				else
					local slot_15_39 = 180 - (90 + slot_15_5)
					local slot_15_40 = slot_15_4 * math.sin(slot_15_5 * L26_26) / math.sin(slot_15_39 * L26_26) * (slot_15_28 - ove_0_30):len() / slot_15_4
					local slot_15_41 = slot_15_28 + slot_15_40 * L15_15:crossP(slot_15_28 - ove_0_30):norm()
					local slot_15_42 = slot_15_28 + slot_15_40 * (slot_15_28 - ove_0_30):crossP(L15_15):norm()

					if polygon_contains({
						ove_0_30,
						slot_15_41,
						slot_15_42
					}, arg_15_1.x, arg_15_1.z) then
						slot_15_29 = true

						if slot_15_20 == ove_0_28.PATH.NO_PATH then
							slot_15_28 = slot_15_19
						end

						local slot_15_43 = slot_15_26 * arg_15_1.moveSpeed

						setVector(ove_0_32, slot_15_28.x, 0, slot_15_28.z)
						setVector(ove_0_33, arg_15_1.x, 0, arg_15_1.z)

						slot_15_15 = slot_15_43 + ove_0_32:dist(ove_0_33) - arg_15_1.boundingRadius <= 0 and 100 or 100 - slot_15_43 * arg_15_0.HITRATE_FACT

						if slot_15_15 < slot_15_2 then
							return nil, nil, 0
						end
					end
				end

				if slot_15_29 then
					local slot_15_44 = slot_15_28
					local slot_15_45 = slot_15_44 and ove_0_29.STATUS.SUCCESS_HIT or ove_0_29.STATUS.NO_INTERSECTION
					local slot_15_46 = slot_15_44 and slot_15_15 or 0

					return slot_15_45, slot_15_44, slot_15_46
				end
			end
		end

		::label_15_0::

		slot_15_18 = slot_15_18 + (slot_15_3 ~= ove_0_13 and 2 * arg_15_1.boundingRadius / slot_15_3 or arg_15_1.boundingRadius / 1.3 / arg_15_1.moveSpeed)

		if slot_15_18 > (slot_15_3 == ove_0_13 and slot_15_6 or slot_15_6 + slot_15_4 / slot_15_3) + network.latency * 4 then
			return nil, nil, 0
		end
	end

	local slot_15_47 = slot_15_12 == ove_0_29.STATUS.NO_INTERSECTION and 0 or slot_15_12

	return slot_15_12, slot_15_13, slot_15_47
end

local ove_0_35 = ove_0_34()

return {
	pred = ove_0_35
}

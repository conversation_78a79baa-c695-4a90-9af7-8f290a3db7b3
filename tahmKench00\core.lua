

local ove_0_10 = module.load(header.id, "draw/info")
local ove_0_11 = module.load(header.id, "draw/circle")
local ove_0_12 = module.seek("evade")
local ove_0_13 = module.internal("orb")
local ove_0_14
local ove_0_15
local ove_0_16
--local ove_0_17 = module.load(header.id, "Permashow/Permashow")
local ove_0_18 = false
local ove_0_19 = false
local ove_0_20
local ove_0_21 = module.load(header.id, "common0")

if hanbot.language == 1 then
	print("Chinese Menu Loaded")

	ove_0_16 = module.load(header.id, "TahmKench/cnmenu")
else
	ove_0_16 = module.load(header.id, "TahmKench/menu")
end

local ove_0_22 = module.load(header.id, "TahmKench/q")
local ove_0_23 = module.load(header.id, "TahmKench/w")
local ove_0_24 = module.load(header.id, "TahmKench/e")
local ove_0_25 = module.load(header.id, "TahmKench/r")
local ove_0_26 = module.load(header.id, "TahmKench/wqcombo")
local ove_0_27 = 0
local ove_0_28 = 0

local function ove_0_29()
	if not ove_0_18 then
		print("checking evade.", ove_0_12)

		ove_0_18 = true

		if ove_0_12 == nil then
			print("NO EVADE MODULE FOUND PLEASE ENABLE EVADE ")
			chat.add("[Alexis AIO] ", {
				color = "#fff600",
				bold = false,
				italic = false
			})
			chat.add("Enable Evade 2.0 for advanced R usage", {
				color = "#CC5544CC",
				bold = true
			})
			chat.print()

			ove_0_20 = 100
		elseif ove_0_12.menu.text == "Hanbot Evade 2" then
			ad_damage, ap_damage, true_damage, buff_list = ove_0_12.damage.count(player)
			ove_0_20 = ad_damage + ap_damage + true_damage
			ove_0_19 = true
		else
			chat.add("[Alexis AIO] ", {
				color = "#CC5544CC",
				bold = false,
				italic = false
			})
			chat.add("Enable Evade 2.0 for advanced R usage", {
				color = "#CC5544CC",
				bold = true
			})
			chat.print()

			ove_0_20 = 0
			ove_0_19 = false
		end
	end

	local slot_5_0, slot_5_1 = ove_0_13.core.can_cast_spell(_W, true)
	local slot_5_2, slot_5_3 = ove_0_13.core.can_cast_spell(_Q, false)
	local slot_5_4, slot_5_5 = ove_0_13.core.can_cast_spell(_Q, true)
	local slot_5_6, slot_5_7 = ove_0_13.core.can_cast_spell(_E, true)
	local slot_5_8, slot_5_9 = ove_0_13.core.can_cast_spell(_R, false)
	local slot_5_10 = ove_0_22.get_action_state()
	local slot_5_11 = ove_0_23.get_action_state()
	local slot_5_12 = ove_0_24.get_action_state()
	local slot_5_13 = ove_0_25.get_action_state()

	if ove_0_16.bind.harras:get() and not ove_0_16.bind.combat:get() and ove_0_21.GetPercentMana(player) >= ove_0_16.mana_farmq:get() and slot_5_10 and ove_0_16.enable_q:get() and not ove_0_21.IsPlayerUnderTurret() and not player.isRecalling then
		ove_0_22.invoke_action()
	end

	if ove_0_16.bind.combat:get() then
		if slot_5_10 and ove_0_16.enable_q:get() and ove_0_21.GetPercentMana(player) >= ove_0_16.mana_farmq:get() then
			ove_0_22.invoke_action()
		end

		if slot_5_11 and ove_0_16.enable_w:get() and ove_0_21.GetPercentMana(player) >= ove_0_16.mana_farmw:get() then
			ove_0_23.invoke_action()
		end

		if slot_5_13 then
			ove_0_25.invoke_action()
		end
	end

	if ove_0_16.enable_e:get() and ove_0_16.enable_e_incoming:get() and ove_0_19 then
		ad_damage, ap_damage, true_damage, buff_list = ove_0_12.damage.count(player)
		ove_0_20 = ad_damage + ap_damage + true_damage

		if player.champSpecificHealth >= 100 then
			if ove_0_20 / player.health * 100 >= ove_0_16.healthamount_e:get() then
				player:castSpell("pos", _E, player.pos)
			end

			if ove_0_20 + 50 >= player.health then
				player:castSpell("pos", _E, player.pos)
			end
		end
	end

	if ove_0_16.enable_e:get() and slot_5_6 then
		-- block empty
	end

	if ove_0_16.bind.farm:get() then
		if ove_0_22.get_farm_state() then
			ove_0_22.invoke_farm()
		end

		if ove_0_23.get_farm_state() then
			ove_0_23.invoke_farm()
		end

		if ove_0_24.get_farm_state() then
			ove_0_24.invoke_farm()
		end
	end

	if ove_0_16.bind.combat:get() then
		return
	end
end

local function ove_0_30(arg_6_0)
	if arg_6_0.pos:dist(player.pos) < 1450 then
		local slot_6_0 = arg_6_0.name

		if slot_6_0:find("TahmKench_.*_P_Cannister_Ground") then
			ove_0_14 = arg_6_0
		end

		if slot_6_0:find("TahmKench_.*_P_ChunkLandIndicator") then
			ove_0_14 = arg_6_0
		end
	end
end

local function ove_0_31(arg_7_0)
	if ove_0_14 and ove_0_14.ptr == arg_7_0.ptr then
		ove_0_14 = nil
	end
end

local ove_0_32 = "x"
local ove_0_33 = "x"
local ove_0_34 = "x"
local ove_0_35 = "x"

if hanbot.language == 1 then
	local ove_0_36 = "�Զ�Q���� ����"
	local ove_0_37 = "�Զ�Q���� �ر�"
	local ove_0_38 = "ũ��������ģʽ"
	local ove_0_39 = "ũ�����ƽ�ģʽ"
else
	local ove_0_40 = "Auto Q Enabled"
	local ove_0_41 = "Auto Q Disabled"
	local ove_0_42 = "Farm: Freeze Mode"
	local ove_0_43 = "Farm: Push Mode"
end

local function ove_0_44()
	ove_0_17.Perma_ShowV2(3, ove_0_16.bind.dive.toggle, ove_0_16.bind.dive.text, ove_0_16.bind.dive:get(), ove_0_16.bind.harras.toggle, "Auto Q", ove_0_16.bind.harras:get(), ove_0_16.bind.ultimate.key, "Semi-W", ove_0_16.bind.ultimate:get())

	if not player.isOnScreen or player.isDead or ove_0_16.range.disable_drawing:get() then
		return
	end
end

local function ove_0_45()
	if not player.isOnScreen or player.isDead or ove_0_16.range.disable_drawing:get() then
		return
	end

	local slot_9_0 = ove_0_16.range.c1:get()
	local slot_9_1 = ove_0_16.range.c2:get()
	local slot_9_2 = ove_0_16.range.c4:get()

	offset = player.boundingRadius

	if offset > 80 then
		offset = (offset - 80) * 10
		qrange = 850 + offset
	else
		qrange = 850
	end

	if ove_0_16.range.q:get() then
		ove_0_11.on_draw(qrange, 6, player.pos.xzz, slot_9_0)
	end

	if ove_0_16.range.w:get() then
		ove_0_11.on_draw(1000, 6, player.pos.xzz, slot_9_1)
	end

	if ove_0_16.range.r:get() then
		local slot_9_3 = 1800

		ove_0_11.on_draw(300, 10, player.pos.xzz, slot_9_2)
	end
end

return {
	get_action = ove_0_29,
	on_draw_info = ove_0_44,
	on_draw_range = ove_0_45,
	AccuracyTracker = ove_0_30,
	AccuracyTrackerdelte = ove_0_31
}

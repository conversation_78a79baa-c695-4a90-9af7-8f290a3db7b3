

local ove_0_20 = module.load("Healer_A.I.O.", "Libraries/HealerAIO_Common")



local ove_0_21 = menu("HealerAIO_Senna_lvxbot", "Healer AIO - Senna")

ove_0_21:header("load_header", "- Healer " .. player.charName .. " -")

local ove_0_22 = module.load("Healer_A.I.O.", "Libraries/HealerAIO_Graphics")
local ove_0_23 = module.internal("orb")
local ove_0_24 = module.internal("pred")
local ove_0_25 = module.internal("TS")
local ove_0_26 = module.load("Healer_A.I.O.", "Misc/HealerAIO_InfoBox")
local ove_0_27 = module.load("Healer_A.I.O.", "Databases/HealerAIO_Dashers")
local ove_0_28 = module.load("Healer_A.I.O.", "Databases/HealerAIO_Buffs")
local ove_0_29 = module.load("Healer_A.I.O.", "Databases/HealerAIO_Items")
local ove_0_30 = module.load("Healer_A.I.O.", "Libraries/HealerAIO_Damage")
local ove_0_31 = module.load("Healer_A.I.O.", "Databases/HealerAIO_SpellSlots")



local ove_0_32 = {}
local ove_0_33 = {}
local ove_0_34 = 0

for iter_0_0 = 0, objManager.enemies_n - 1 do
	local ove_0_35 = objManager.enemies[iter_0_0]

	ove_0_32[ove_0_35.ptr] = ove_0_35.charName
	ove_0_33[ove_0_35.charName] = true
	ove_0_33[ove_0_35.charName .. "_ptr"] = ove_0_35.ptr
	ove_0_34 = ove_0_34 + 1
end

local ove_0_36 = {}
local ove_0_37 = {}

for iter_0_1 = 0, objManager.allies_n - 1 do
	local ove_0_38 = objManager.allies[iter_0_1]

	ove_0_36[ove_0_38.ptr] = ove_0_38.charName
	ove_0_37[ove_0_38.charName] = true
	ove_0_37[ove_0_38.charName .. "_ptr"] = ove_0_38.ptr
	ove_0_34 = ove_0_34 + 1
end

local ove_0_39 = 5

if game.mode == "ARAM" or game.mode == "URF" or game.mode == "ARURF" then
	ove_0_39 = 2
end

local ove_0_40 = {
	range = 1300,
	width = 40
}
local ove_0_41 = {
	range = 1300,
	width = 80
}
local ove_0_42 = {
	boundingRadiusMod = 1,
	range = 1300,
	width = 100,
	delay = 0.25,
	speed = math.huge,
	collision = {
		wall = false,
		hero = false,
		minion = true
	}
}
local ove_0_43 = {
	boundingRadiusMod = 1,
	range = 1050,
	speed = 1200,
	delay = 0.25,
	width = 70,
	collision = {
		wall = true,
		hero = false,
		minion = true
	}
}
local ove_0_44 = {
	boundingRadiusMod = 1,
	range = 1300,
	speed = 1200,
	delay = 0.25,
	width = 60,
	collision = {
		wall = true,
		hero = false,
		minion = true
	}
}
local ove_0_45 = {
	boundingRadiusMod = 0,
	range = 23000,
	speed = 20000,
	delay = 1,
	wall_width = 250,
	width = 230,
	collision = {
		wall = true,
		hero = false,
		minion = false
	}
}

;(function()
	-- function 5
	local slot_5_0 = ove_0_21.__config.saves.HealerAIO_Senna_lvxbot

	if not slot_5_0 then
		return
	end

	if slot_5_0.combo and slot_5_0.combo.qcombo_blist then
		slot_5_0.combo.qcombo_blist = nil
	end

	if slot_5_0.harass and slot_5_0.harass.qharass_blist then
		slot_5_0.harass.qharass_blist = nil
	end
end)()
ove_0_21:menu("combo", "Combo Settings")
ove_0_21.combo:header("ss", "Q Settings")
ove_0_21.combo:boolean("qcombo", "Enable Q ", true)
ove_0_21.combo:boolean("qcombo_after_AA", "Use Q after AA ", true)
ove_0_21.combo:slider("q_combo_mana", "Q mana: ", 0, 0, 100, 1)
ove_0_21.combo:header("zzzzz", "Extended Q Settings")
ove_0_21.combo:boolean("qcombo_ext", "Enable Extended Q", true)
ove_0_21.combo.qcombo_ext:set("tooltip", "Will use Extended Q if no enemies are inside AA range")
ove_0_21.combo:boolean("q_slowpred", "Enable Q slow pred ", true)
ove_0_21.combo.q_slowpred:set("tooltip", "This setting increases pred accuracy for [Q], but takes longer to cast it")
ove_0_21.combo:menu("ifs", "[Only Q if] Settings >>")
ove_0_21.combo.ifs:header("hh", "Only use Extended Q if")
ove_0_21.combo.ifs:menu("rune", "Can proc a rune >>")
ove_0_21.combo.ifs.rune:header("hhh", "Use Extended Q if procs")
ove_0_21.combo.ifs.rune:boolean("dark_harvest", "Dark Harvest", false)
ove_0_21.combo.ifs.rune:boolean("grasp", "Grasp of the Undying", false)
ove_0_21.combo.ifs.rune:boolean("first_strike", "First Strike", false)
ove_0_21.combo.ifs.rune:boolean("attack", "Press the Attack", false)
ove_0_21.combo.ifs.rune:boolean("aery", "Summon Aery", false)
ove_0_21.combo.ifs.rune:boolean("comet", "Arcane Comet", false)
ove_0_21.combo.ifs.rune:boolean("manaflow", "Manaflow Band", false)
ove_0_21.combo.ifs.rune:boolean("taste", "Taste of Blood", false)
ove_0_21.combo.ifs:boolean("heal", "Can hit and heal an ally", false)
ove_0_21.combo.ifs.heal:set("tooltip", "Will use Extended Q only if the Q cast would hit both an enemy and an ally, at the same time.")
ove_0_21.combo.ifs:menu("qcombo_blist", "Ally HP Settings >>")

for iter_0_2 = 0, objManager.allies_n - 1 do
	local ove_0_46 = objManager.allies[iter_0_2]

	if ove_0_46.ptr ~= player.ptr then
		ove_0_21.combo.ifs.qcombo_blist:header("YEP" .. ove_0_46.charName, ove_0_46.charName)
		ove_0_21.combo.ifs.qcombo_blist:slider(ove_0_46.charName .. "hp", ove_0_46.charName .. " HP% to Q", 100, 1, 100, 1)
		ove_0_21.combo.ifs.qcombo_blist:boolean(ove_0_46.charName .. "block", "Block: " .. ove_0_46.charName, false)
	end
end

ove_0_21.combo.ifs:boolean("mist", "Can collect a Mist stack", false)
ove_0_21.combo.ifs.mist:set("tooltip", "Will use Extended Q only if the target is affected by Mist passive, to collect it.")
ove_0_21.combo.ifs:boolean("soul", "Can collect a dropped soul", false)
ove_0_21.combo.ifs.soul:set("tooltip", "Will use Extended Q only if the Q cast would hit an enemy and collect a dropped soul, at the same time.")
ove_0_21.combo.ifs:boolean("structure", "Can hit a structure", false)
ove_0_21.combo.ifs.structure:set("tooltip", "Will use Extended Q only if the Q cast would hit both an enemy and a structure, at the same time. [Tower, Inhibitor, Nexus]")
ove_0_21.combo.ifs:boolean("multi", "Can hit multiple enemies", false)
ove_0_21.combo.ifs.multi:set("tooltip", "Will use Extended Q only if the Q cast would hit both an enemy and an ally, at the same time.")
ove_0_21.combo.ifs:slider("multi_count", "^- Enemy count", 2, 2, 5, 1)
ove_0_21.combo:boolean("qcombo_minions", "Dont Q if hits X minions", false)
ove_0_21.combo.qcombo_minions:set("tooltip", "Wont use Extended Q if the Q cast would hit X or more enemy minions in a line")
ove_0_21.combo:slider("qcombo_min_count", "^- allowed minion count ", 3, 0, 20, 1)
ove_0_21.combo:menu("ignore", "Ignore [Dont Q] If >>")
ove_0_21.combo.ignore:header("hh", "Ignore [Dont Q] If")
ove_0_21.combo.ignore:menu("rune", "Can proc a rune >>")
ove_0_21.combo.ignore.rune:header("hhh", "Ignore [Dont Q] if procs")
ove_0_21.combo.ignore.rune:boolean("dark_harvest", "Dark Harvest", false)
ove_0_21.combo.ignore.rune:boolean("grasp", "Grasp of the Undying", false)
ove_0_21.combo.ignore.rune:boolean("first_strike", "First Strike", false)
ove_0_21.combo.ignore.rune:boolean("attack", "Press the Attack", false)
ove_0_21.combo.ignore.rune:boolean("aery", "Summon Aery", false)
ove_0_21.combo.ignore.rune:boolean("comet", "Arcane Comet", false)
ove_0_21.combo.ignore.rune:boolean("manaflow", "Manaflow Band", false)
ove_0_21.combo.ignore.rune:boolean("taste", "Taste of Blood", false)
ove_0_21.combo.ignore:boolean("heal", "Can hit and heal an ally", false)
ove_0_21.combo.ignore.heal:set("tooltip", "Ignores [Dont Q] setting if the Q cast can hit both an enemy and an ally, at the same time.")
ove_0_21.combo.ignore:menu("qcombo_blist", "Ally HP Settings >>")

for iter_0_3 = 0, objManager.allies_n - 1 do
	local ove_0_47 = objManager.allies[iter_0_3]

	if ove_0_47.ptr ~= player.ptr then
		ove_0_21.combo.ignore.qcombo_blist:header("YEP" .. ove_0_47.charName, ove_0_47.charName)
		ove_0_21.combo.ignore.qcombo_blist:slider(ove_0_47.charName .. "hp", ove_0_47.charName .. " HP% to Q", 100, 1, 100, 1)
		ove_0_21.combo.ignore.qcombo_blist:boolean(ove_0_47.charName .. "block", "Block: " .. ove_0_47.charName, false)
	end
end

ove_0_21.combo.ignore:boolean("mist", "Can collect a Mist stack", false)
ove_0_21.combo.ignore.mist:set("tooltip", "Ignores [Dont Q] setting if the target is affected by Mist passive, to allow Extended Q cast collect it.")
ove_0_21.combo.ignore:boolean("soul", "Can collect a dropped soul", false)
ove_0_21.combo.ignore.soul:set("tooltip", "Ignores [Dont Q] setting if the Q cast can hit an enemy and collect a dropped soul, at the same time.")
ove_0_21.combo.ignore:boolean("structure", "Can hit a structure", false)
ove_0_21.combo.ignore.structure:set("tooltip", "Ignores [Dont Q] setting if the Q cast can hit both an enemy and a structure, at the same time. [Tower, Inhibitor, Nexus]")
ove_0_21.combo.ignore:boolean("multi", "Can hit multiple enemies", false)
ove_0_21.combo.ignore.multi:set("tooltip", "Ignores [Dont Q] setting if the Q cast can hit both an enemy and an ally, at the same time.")
ove_0_21.combo.ignore:slider("multi_count", "^- Enemy count", 2, 2, 5, 1)
ove_0_21.combo:header("ssw", "W Settings")
ove_0_21.combo:boolean("wcombo", "Enable W ", true)
ove_0_21.combo:boolean("slow_w", "Enable W slow pred", true)
ove_0_21.combo.slow_w:set("tooltip", "This setting increases pred accuracy for [W], but takes longer to cast it")
ove_0_21.combo:slider("w_custom_range", "Custom W range ", 900, 0, 1300, 1)
ove_0_21:menu("harass", "Harass Settings")
ove_0_21.harass:header("ss", "Q Settings")
ove_0_21.harass:boolean("qharass", "Enable Q ", true)
ove_0_21.harass:boolean("qharass_after_AA", "Use Q after AA ", true)
ove_0_21.harass:slider("q_harass_mana", "Q mana: ", 0, 0, 100, 1)
ove_0_21.harass:header("zzzzz", "Extended Q Settings")
ove_0_21.harass:boolean("qharass_ext", "Enable Extended Q", true)
ove_0_21.harass.qharass_ext:set("tooltip", "Will use Extended Q if no enemies are inside AA range")
ove_0_21.harass:boolean("q_slowpred", "Enable Q slow pred ", true)
ove_0_21.harass.q_slowpred:set("tooltip", "This setting increases pred accuracy for [Q], but takes longer to cast it")
ove_0_21.harass:menu("ifs", "[Only Q if] Settings >>")
ove_0_21.harass.ifs:header("hh", "Only use Extended Q if")
ove_0_21.harass.ifs:menu("rune", "Can proc a rune >>")
ove_0_21.harass.ifs.rune:header("hhh", "Use Extended Q if procs")
ove_0_21.harass.ifs.rune:boolean("dark_harvest", "Dark Harvest", false)
ove_0_21.harass.ifs.rune:boolean("grasp", "Grasp of the Undying", false)
ove_0_21.harass.ifs.rune:boolean("first_strike", "First Strike", false)
ove_0_21.harass.ifs.rune:boolean("attack", "Press the Attack", false)
ove_0_21.harass.ifs.rune:boolean("aery", "Summon Aery", false)
ove_0_21.harass.ifs.rune:boolean("comet", "Arcane Comet", false)
ove_0_21.harass.ifs.rune:boolean("manaflow", "Manaflow Band", false)
ove_0_21.harass.ifs.rune:boolean("taste", "Taste of Blood", false)
ove_0_21.harass.ifs:boolean("heal", "Can hit and heal an ally", false)
ove_0_21.harass.ifs.heal:set("tooltip", "Will use Extended Q only if the Q cast would hit both an enemy and an ally, at the same time.")
ove_0_21.harass.ifs:menu("qharass_blist", "Ally HP Settings >>")

for iter_0_4 = 0, objManager.allies_n - 1 do
	local ove_0_48 = objManager.allies[iter_0_4]

	if ove_0_48.ptr ~= player.ptr then
		ove_0_21.harass.ifs.qharass_blist:header("YEP" .. ove_0_48.charName, ove_0_48.charName)
		ove_0_21.harass.ifs.qharass_blist:slider(ove_0_48.charName .. "hp", ove_0_48.charName .. " HP% to Q", 100, 1, 100, 1)
		ove_0_21.harass.ifs.qharass_blist:boolean(ove_0_48.charName .. "block", "Block: " .. ove_0_48.charName, false)
	end
end

ove_0_21.harass.ifs:boolean("mist", "Can collect a Mist stack", false)
ove_0_21.harass.ifs.mist:set("tooltip", "Will use Extended Q only if the target is affected by Mist passive, to collect it.")
ove_0_21.harass.ifs:boolean("soul", "Can collect a dropped soul", false)
ove_0_21.harass.ifs.soul:set("tooltip", "Will use Extended Q only if the Q cast would hit an enemy and collect a dropped soul, at the same time.")
ove_0_21.harass.ifs:boolean("structure", "Can hit a structure", false)
ove_0_21.harass.ifs.structure:set("tooltip", "Will use Extended Q only if the Q cast would hit both an enemy and a structure, at the same time. [Tower, Inhibitor, Nexus]")
ove_0_21.harass.ifs:boolean("multi", "Can hit multiple enemies", false)
ove_0_21.harass.ifs.multi:set("tooltip", "Will use Extended Q only if the Q cast would hit both an enemy and an ally, at the same time.")
ove_0_21.harass.ifs:slider("multi_count", "^- Enemy count", 2, 2, 5, 1)
ove_0_21.harass:boolean("qharass_minions", "Dont Q if hits X minions", false)
ove_0_21.harass.qharass_minions:set("tooltip", "Wont use Extended Q if the Q cast would hit X or more enemy minions in a line")
ove_0_21.harass:slider("qharass_min_count", "^- allowed minion count ", 3, 0, 20, 1)
ove_0_21.harass:menu("ignore", "Ignore [Dont Q] If >>")
ove_0_21.harass.ignore:header("hh", "Ignore [Dont Q] If")
ove_0_21.harass.ignore:menu("rune", "Can proc a rune >>")
ove_0_21.harass.ignore.rune:header("hhh", "Ignore [Dont Q] if procs")
ove_0_21.harass.ignore.rune:boolean("dark_harvest", "Dark Harvest", false)
ove_0_21.harass.ignore.rune:boolean("grasp", "Grasp of the Undying", false)
ove_0_21.harass.ignore.rune:boolean("first_strike", "First Strike", false)
ove_0_21.harass.ignore.rune:boolean("attack", "Press the Attack", false)
ove_0_21.harass.ignore.rune:boolean("aery", "Summon Aery", false)
ove_0_21.harass.ignore.rune:boolean("comet", "Arcane Comet", false)
ove_0_21.harass.ignore.rune:boolean("manaflow", "Manaflow Band", false)
ove_0_21.harass.ignore.rune:boolean("taste", "Taste of Blood", false)
ove_0_21.harass.ignore:boolean("heal", "Can hit and heal an ally", false)
ove_0_21.harass.ignore.heal:set("tooltip", "Ignores [Dont Q] setting if the Q cast can hit both an enemy and an ally, at the same time.")
ove_0_21.harass.ignore:menu("qharass_blist", "Ally HP Settings >>")

for iter_0_5 = 0, objManager.allies_n - 1 do
	local ove_0_49 = objManager.allies[iter_0_5]

	if ove_0_49.ptr ~= player.ptr then
		ove_0_21.harass.ignore.qharass_blist:header("YEP" .. ove_0_49.charName, ove_0_49.charName)
		ove_0_21.harass.ignore.qharass_blist:slider(ove_0_49.charName .. "hp", ove_0_49.charName .. " HP% to Q", 100, 1, 100, 1)
		ove_0_21.harass.ignore.qharass_blist:boolean(ove_0_49.charName .. "block", "Block: " .. ove_0_49.charName, false)
	end
end

ove_0_21.harass.ignore:boolean("mist", "Can collect a Mist stack", false)
ove_0_21.harass.ignore.mist:set("tooltip", "Ignores [Dont Q] setting if the target is affected by Mist passive, to allow Extended Q cast collect it.")
ove_0_21.harass.ignore:boolean("soul", "Can collect a dropped soul", false)
ove_0_21.harass.ignore.soul:set("tooltip", "Ignores [Dont Q] setting if the Q cast can hit an enemy and collect a dropped soul, at the same time.")
ove_0_21.harass.ignore:boolean("structure", "Can hit a structure", false)
ove_0_21.harass.ignore.structure:set("tooltip", "Ignores [Dont Q] setting if the Q cast can hit both an enemy and a structure, at the same time. [Tower, Inhibitor, Nexus]")
ove_0_21.harass.ignore:boolean("multi", "Can hit multiple enemies", false)
ove_0_21.harass.ignore.multi:set("tooltip", "Ignores [Dont Q] setting if the Q cast can hit both an enemy and an ally, at the same time.")
ove_0_21.harass.ignore:slider("multi_count", "^- Enemy count", 2, 2, 5, 1)
ove_0_21.harass:header("yepw", "W Settings")
ove_0_21.harass:boolean("wharass", "Enable W ", true)
ove_0_21.harass:boolean("slow_w", "Enable W slow pred", true)
ove_0_21.harass.slow_w:set("tooltip", "This setting increases pred accuracy for [W], but takes longer to cast it")
ove_0_21.harass:slider("w_harass_mana", "W mana: ", 50, 0, 100, 1)
ove_0_21:menu("autoq", "Auto Q Settings")
ove_0_21.autoq:header("sss", "Auto Q Settings")
ove_0_21.autoq:boolean("enable", "Enable Auto Q ", false)
ove_0_21.autoq.enable:set("tooltip", "Enables automatic [Extended Q] usage")
ove_0_21.autoq:boolean("pause_autoq", "Pause Auto Q if in Combo/Harass ", true)
ove_0_21.autoq.pause_autoq:set("tooltip", "Pauses [Auto Q] while Combo or Harass mode is active.")
ove_0_21.autoq:keybind("keys_autoq", "Auto Q toggle button", nil, nil)
ove_0_21.autoq:boolean("q_slowpred", "Enable Q slow pred ", true)
ove_0_21.autoq.q_slowpred:set("tooltip", "This setting increases pred accuracy for [Q], but takes longer to cast it")
ove_0_21.autoq:slider("mana", "Auto Q mana: ", 50, 0, 100, 1)
ove_0_21.autoq:header("aaa", "- - -")
ove_0_21.autoq:menu("ifs", "[Only Q if] Settings >>")
ove_0_21.autoq.ifs:header("hh", "Only use Extended Q if")
ove_0_21.autoq.ifs:menu("rune", "Can proc a rune >>")
ove_0_21.autoq.ifs.rune:header("hhh", "Use Extended Q if procs")
ove_0_21.autoq.ifs.rune:boolean("dark_harvest", "Dark Harvest", false)
ove_0_21.autoq.ifs.rune:boolean("grasp", "Grasp of the Undying", false)
ove_0_21.autoq.ifs.rune:boolean("first_strike", "First Strike", false)
ove_0_21.autoq.ifs.rune:boolean("attack", "Press the Attack", false)
ove_0_21.autoq.ifs.rune:boolean("aery", "Summon Aery", false)
ove_0_21.autoq.ifs.rune:boolean("comet", "Arcane Comet", false)
ove_0_21.autoq.ifs.rune:boolean("manaflow", "Manaflow Band", false)
ove_0_21.autoq.ifs.rune:boolean("taste", "Taste of Blood", false)
ove_0_21.autoq.ifs:boolean("heal", "Can hit and heal an ally", false)
ove_0_21.autoq.ifs.heal:set("tooltip", "Will use Extended Q only if the Q cast would hit both an enemy and an ally, at the same time.")
ove_0_21.autoq.ifs:menu("autoq_blist", "Ally HP Settings >>")

for iter_0_6 = 0, objManager.allies_n - 1 do
	local ove_0_50 = objManager.allies[iter_0_6]

	if ove_0_50.ptr ~= player.ptr then
		ove_0_21.autoq.ifs.autoq_blist:header("YEP" .. ove_0_50.charName, ove_0_50.charName)
		ove_0_21.autoq.ifs.autoq_blist:slider(ove_0_50.charName .. "hp", ove_0_50.charName .. " HP% to Q", 100, 1, 100, 1)
		ove_0_21.autoq.ifs.autoq_blist:boolean(ove_0_50.charName .. "block", "Block: " .. ove_0_50.charName, false)
	end
end

ove_0_21.autoq.ifs:boolean("mist", "Can collect a Mist stack", false)
ove_0_21.autoq.ifs.mist:set("tooltip", "Will use Extended Q only if the target is affected by Mist passive, to collect it.")
ove_0_21.autoq.ifs:boolean("soul", "Can collect a dropped soul", false)
ove_0_21.autoq.ifs.soul:set("tooltip", "Will use Extended Q only if the Q cast would hit an enemy and collect a dropped soul, at the same time.")
ove_0_21.autoq.ifs:boolean("structure", "Can hit a structure", false)
ove_0_21.autoq.ifs.structure:set("tooltip", "Will use Extended Q only if the Q cast would hit both an enemy and a structure, at the same time. [Tower, Inhibitor, Nexus]")
ove_0_21.autoq.ifs:boolean("multi", "Can hit multiple enemies", false)
ove_0_21.autoq.ifs.multi:set("tooltip", "Will use Extended Q only if the Q cast would hit both an enemy and an ally, at the same time.")
ove_0_21.autoq.ifs:slider("multi_count", "^- Enemy count", 2, 2, 5, 1)
ove_0_21.autoq:boolean("autoq_minions", "Dont Q if hits X minions", false)
ove_0_21.autoq.autoq_minions:set("tooltip", "Wont use Extended Q if the Q cast would hit X or more enemy minions in a line")
ove_0_21.autoq:slider("autoq_min_count", "^- allowed minion count ", 3, 0, 20, 1)
ove_0_21.autoq:menu("ignore", "Ignore [Dont Q] If >>")
ove_0_21.autoq.ignore:header("hh", "Ignore [Dont Q] If")
ove_0_21.autoq.ignore:menu("rune", "Can proc a rune >>")
ove_0_21.autoq.ignore.rune:header("hhh", "Ignore [Dont Q] if procs")
ove_0_21.autoq.ignore.rune:boolean("dark_harvest", "Dark Harvest", false)
ove_0_21.autoq.ignore.rune:boolean("grasp", "Grasp of the Undying", false)
ove_0_21.autoq.ignore.rune:boolean("first_strike", "First Strike", false)
ove_0_21.autoq.ignore.rune:boolean("attack", "Press the Attack", false)
ove_0_21.autoq.ignore.rune:boolean("aery", "Summon Aery", false)
ove_0_21.autoq.ignore.rune:boolean("comet", "Arcane Comet", false)
ove_0_21.autoq.ignore.rune:boolean("manaflow", "Manaflow Band", false)
ove_0_21.autoq.ignore.rune:boolean("taste", "Taste of Blood", false)
ove_0_21.autoq.ignore:boolean("heal", "Can hit and heal an ally", false)
ove_0_21.autoq.ignore.heal:set("tooltip", "Ignores [Dont Q] setting if the Q cast can hit both an enemy and an ally, at the same time.")
ove_0_21.autoq.ignore:menu("autoq_blist", "Ally HP Settings >>")

for iter_0_7 = 0, objManager.allies_n - 1 do
	local ove_0_51 = objManager.allies[iter_0_7]

	if ove_0_51.ptr ~= player.ptr then
		ove_0_21.autoq.ignore.autoq_blist:header("YEP" .. ove_0_51.charName, ove_0_51.charName)
		ove_0_21.autoq.ignore.autoq_blist:slider(ove_0_51.charName .. "hp", ove_0_51.charName .. " HP% to Q", 100, 1, 100, 1)
		ove_0_21.autoq.ignore.autoq_blist:boolean(ove_0_51.charName .. "block", "Block: " .. ove_0_51.charName, false)
	end
end

ove_0_21.autoq.ignore:boolean("mist", "Can collect a Mist stack", false)
ove_0_21.autoq.ignore.mist:set("tooltip", "Ignores [Dont Q] setting if the target is affected by Mist passive, to allow Extended Q cast collect it.")
ove_0_21.autoq.ignore:boolean("soul", "Can collect a dropped soul", false)
ove_0_21.autoq.ignore.soul:set("tooltip", "Ignores [Dont Q] setting if the Q cast can hit an enemy and collect a dropped soul, at the same time.")
ove_0_21.autoq.ignore:boolean("structure", "Can hit a structure", false)
ove_0_21.autoq.ignore.structure:set("tooltip", "Ignores [Dont Q] setting if the Q cast can hit both an enemy and a structure, at the same time. [Tower, Inhibitor, Nexus]")
ove_0_21.autoq.ignore:boolean("multi", "Can hit multiple enemies", false)
ove_0_21.autoq.ignore.multi:set("tooltip", "Ignores [Dont Q] setting if the Q cast can hit both an enemy and an ally, at the same time.")
ove_0_21.autoq.ignore:slider("multi_count", "^- Enemy count", 2, 2, 5, 1)
ove_0_21:menu("rset", "R Settings")
ove_0_21.rset:header("ss", "R Settings")
ove_0_21.rset:boolean("ks_r", "Auto R killable", true)
ove_0_21.rset:boolean("r_dont_ally", "^- Dont KS allies", true)
ove_0_21.rset:boolean("r_dont_ally_allow", " ^- Allow KS if ally low hp", true)
ove_0_21.rset:boolean("dragon_execute", "Include Elder dragon execute", true)
ove_0_21.rset.dragon_execute:set("tooltip", "Will include Elder Dragon execute to R damage calculations")
ove_0_21.rset:boolean("dragon_burn", "Include Elder dragon burn damage", true)
ove_0_21.rset.dragon_burn:set("tooltip", "Will include Elder Dragon burn damage to R damage calculations")
ove_0_21.rset:header("yeet", "Dashers")
ove_0_21.rset:boolean("dont_r_dashers", "Dont auto R dashers", true)
ove_0_21.rset.dont_r_dashers:set("tooltip", "Wont use R on enemies that have a dash ability off cooldown, and ready to use")
ove_0_21.rset:boolean("dont_r_dashers_flash", "^- Include Summoner Flash", true)
ove_0_21.rset.dont_r_dashers_flash:set("tooltip", "Wont use R on enemies that have flash off cooldown, and ready to use")
ove_0_21.rset:header("rxdee", "Follow-up R Settings")
ove_0_21.rset:boolean("frEnable", "Enable Follow-up R ", true)
ove_0_21.rset.frEnable:set("tooltip", "Uses R on multiple CC'ed enemies in a line")
ove_0_21.rset:slider("frCount", "Min CC'ed enemies to R", 2, 2, 5, 1)
ove_0_21.rset:slider("frEhp", "^- Min enemies HP% to R", 100, 0, 100, 1)
ove_0_21.rset:boolean("frENearby", "Dont R if enemies nearby", false)
ove_0_21.rset.frENearby:set("tooltip", "Wont use follow-up R if enemies are dangerously close to Senna")
ove_0_21.rset:slider("frRange", "Follow-up R range ", 23000, 0, 23000, 100)
ove_0_21.rset.frRange:set("tooltip", "Follow-up R activity range")
ove_0_21.rset:header("ssssz", "Force R Key")
ove_0_21.rset:keybind("Force_R_key", "Force R Key", "MMB", nil)
ove_0_21.rset.Force_R_key:set("tooltip", "Forces R on target")
ove_0_21.rset:boolean("semiR_slow", "Enable slow pred R", false)
ove_0_21.rset.semiR_slow:set("tooltip", "This setting increases pred accuracy for [R], but takes longer to cast it")
ove_0_21:menu("base", "BaseUlt Settings")
ove_0_21.base:header("sss", "BaseUlt Settings")
ove_0_21.base:boolean("base_r_enable", "Enable BaseUlt ", true)
ove_0_21.base:boolean("base_r_dis_ks", "Disable auto R if can BaseUlt", true)
ove_0_21.base.base_r_dis_ks:set("tooltip", "If a killable enemy is recalling, disables Auto R and prioritizes BaseUlt instead")
ove_0_21.base:boolean("base_r_dont_enemies", "Dont BaseUlt if enemies nearby", false)
ove_0_21.base:slider("base_r_dont_range", "Enemies nearby range", 1000, 500, 2000, 5)
ove_0_21.base:menu("blacklist", "BaseUlt Blacklist")

for iter_0_8 = 0, objManager.enemies_n - 1 do
	local ove_0_52 = objManager.enemies[iter_0_8]

	ove_0_21.base.blacklist:boolean(ove_0_32[ove_0_52.ptr], "Disable for: " .. ove_0_32[ove_0_52.ptr], false)
end

ove_0_21:menu("wpriority", "Healing Settings")
ove_0_21.wpriority:header("sss", "Healing Settings")
ove_0_21.wpriority:keybind("healTogForce", "Force heal on ally button", "A", nil)
ove_0_21.wpriority:header("sss", "Auto Healing Settings")
ove_0_21.wpriority:boolean("enable_autoHeal", "Enable Auto Heal ", true)
ove_0_21.wpriority:keybind("healTog", "Auto healing toggle button", nil, "T")
ove_0_21.wpriority.healTogForce:set("tooltip", "Forces Q cast on nearby ally to heal")
ove_0_21.wpriority:dropdown("heal_mode", "Auto Healing Mode", 2, {
	"Always",
	"Enemies nearby",
	"No Enemies nearby"
})
ove_0_21.wpriority.heal_mode:set("tooltip", "[Always] - Heals Normally. [Enemies nearby] - heals only when enemies are nearby. [No Enemies nearby] - heals when no enemies are nearby.")
ove_0_21.wpriority:slider("heal_mana", "Auto healing mana", 50, 0, 100, 1)
ove_0_21.wpriority:header("ssss", " Healing Priority List ")

for iter_0_9 = 0, objManager.allies_n - 1 do
	local ove_0_53 = objManager.allies[iter_0_9]

	if ove_0_53.ptr ~= player.ptr then
		if ove_0_20.healing_priority_list_one[ove_0_53.charName] then
			ove_0_21.wpriority:slider(ove_0_53.charName, "Priority: " .. ove_0_53.charName, 1, 0, 5, 1)
			ove_0_21.wpriority[ove_0_36[ove_0_53.ptr]]:set("tooltip", "1 = Biggest priority, 0 = Disabled")
			ove_0_21.wpriority:slider(ove_0_53.charName .. "hp", " ^- Health Percent: ", 50, 1, 101, 1)
		elseif ove_0_20.healing_priority_list_two[ove_0_53.charName] then
			ove_0_21.wpriority:slider(ove_0_53.charName, "Priority: " .. ove_0_53.charName, 2, 0, 5, 1)
			ove_0_21.wpriority[ove_0_36[ove_0_53.ptr]]:set("tooltip", "1 = Biggest priority, 0 = Disabled")
			ove_0_21.wpriority:slider(ove_0_53.charName .. "hp", " ^- Health Percent: ", 50, 1, 101, 1)
		else
			ove_0_21.wpriority:slider(ove_0_53.charName, "Priority: " .. ove_0_53.charName, 3, 0, 5, 1)
			ove_0_21.wpriority[ove_0_36[ove_0_53.ptr]]:set("tooltip", "1 = Biggest priority, 0 = Disabled")
			ove_0_21.wpriority:slider(ove_0_53.charName .. "hp", " ^- Health Percent: ", 20, 1, 101, 1)
		end
	end
end

ove_0_21:menu("pick", "Soul Pick-up Settings")
ove_0_21.pick:header("ss", "Soul Pick-up Settings")
ove_0_21.pick:boolean("soul_pickup", "Pick up souls ", true)
ove_0_21.pick:header("sss", " Pick up souls in ")
ove_0_21.pick:boolean("pickup_combo", "Combo mode ", true)
ove_0_21.pick:boolean("pickup_combo_dis", " ^- Disable if enemies nearby", true)
ove_0_21.pick:boolean("pickup_harass", "Harass mode ", false)
ove_0_21.pick:boolean("pickup_laneclear", "Lane Clear mode ", true)
ove_0_21.pick:boolean("pickup_lasthit", "Last Hit mode", false)
ove_0_21:menu("farminggg", "Farm Settings")
ove_0_21.farminggg:header("farmss", "Farm Settings")
ove_0_21.farminggg:boolean("enable_farm", "Enable Farming ", true)
ove_0_21.farminggg:keybind("farmtog", "Farm toggle key:", nil, "Z")
ove_0_21.farminggg:menu("laneclear", "Lane Clear ")
ove_0_21.farminggg.laneclear:header("farmssQ", "Q Settings")
ove_0_21.farminggg.laneclear:boolean("laneQ", "Use Q in Lane Clear ", false)
ove_0_21.farminggg.laneclear:slider("q_farm_count", "Use Q if hits X minions ", 5, 0, 10, 1)
ove_0_21.farminggg.laneclear:slider("laneQmana", "^- Q mana", 50, 0, 101, 1)
ove_0_21.farminggg.laneclear:boolean("laneQdis", "Disable Q farm if enemies nearby", true)
ove_0_21.farminggg.laneclear:header("farmssQhelper", "Q Helper Settings")
ove_0_21.farminggg.laneclear:boolean("laneQ_helper", "Enable Q helper ", true)
ove_0_21.farminggg.laneclear.laneQ_helper:set("tooltip", "Uses Q on a minion if it cannot be farmed normally with AA")
ove_0_21.farminggg.laneclear:slider("q_helper_mana", "^- Q helper mana", 50, 0, 101, 1)
ove_0_21.farminggg.laneclear:boolean("helperQdis", "Disable Q helper if enemies nearby", false)
ove_0_21.farminggg:menu("jungleclear", "Jungle Clear ")
ove_0_21.farminggg.jungleclear:header("jungfarmss", "Jungle Clear Settings")
ove_0_21.farminggg.jungleclear:boolean("jungleQ", "Use Q in Jungle Clear ", true)
ove_0_21.farminggg.jungleclear:slider("jungmanaQ", "^- Q mana", 30, 0, 101, 1)
ove_0_21.farminggg.jungleclear:boolean("jungleW", "Use W in Jungle Clear ", false)
ove_0_21.farminggg.jungleclear:slider("jungmanaW", "^- W mana", 30, 0, 101, 1)
ove_0_21:menu("misc", "Misc Settings")
ove_0_21.misc:header("ss", "Misc Settings")
ove_0_21.misc:boolean("ks_q", "KS Q", true)
ove_0_21.misc:boolean("ks_q_ward", " ^- allow ward usage", true)
ove_0_21.misc:header("sszer", "Auto Q Structures")
ove_0_21.misc:menu("q_turret", "Turret Settings ")
ove_0_21.misc.q_turret:header("q_turret_h", "Turret Settings")
ove_0_21.misc.q_turret:boolean("q_turret_enable", "Use Q on Turrets", true)
ove_0_21.misc.q_turret:boolean("q_turret_hold", "^- disable if enemies nearby", false)
ove_0_21.misc.q_turret:boolean("q_turret_hold_hit", "Dont hit enemies", false)
ove_0_21.misc.q_turret.q_turret_hold_hit:set("tooltip", "Wont cast Q on turrets, if the Q cast would hit an enemy champion")
ove_0_21.misc.q_turret:slider("q_turret_mana", "Mana % ", 0, 0, 100, 1)
ove_0_21.misc:menu("q_inhib", "Inhibitor Settings ")
ove_0_21.misc.q_inhib:header("q_inhib_h", "Inhibitor Settings")
ove_0_21.misc.q_inhib:boolean("q_inhib_enable", "Use Q on Inhibitors", true)
ove_0_21.misc.q_inhib:boolean("q_inhib_hold", "^- disable if enemies nearby", false)
ove_0_21.misc.q_inhib:boolean("q_inhib_hold_hit", "Dont hit enemies", false)
ove_0_21.misc.q_inhib.q_inhib_hold_hit:set("tooltip", "Wont cast Q on inhibitors, if the Q cast would hit an enemy champion")
ove_0_21.misc.q_inhib:slider("q_inhib_mana", "Mana % ", 0, 0, 100, 1)
ove_0_21.misc:menu("q_nexus", "Nexus Settings ")
ove_0_21.misc.q_nexus:header("q_nexus_h", "Nexus Settings")
ove_0_21.misc.q_nexus:boolean("q_nexus_enable", "Use Q on Nexus", true)
ove_0_21.misc.q_nexus:boolean("q_nexus_hold", "^- disable if enemies nearby", false)
ove_0_21.misc.q_nexus:boolean("q_nexus_hold_hit", "Dont hit enemies", false)
ove_0_21.misc.q_nexus.q_nexus_hold_hit:set("tooltip", "Wont cast Q on nexus, if the Q cast would hit an enemy champion")
ove_0_21.misc.q_nexus:slider("q_nexus_mana", "Mana % ", 0, 0, 100, 1)
ove_0_21.misc:header("sqcc", "Crowd Control Settings")
ove_0_21.misc:boolean("CC_W", "Cast W on CC'ed targets ", true)
ove_0_21.misc:boolean("CC_W_combo", " ^- only in combo mode", true)
ove_0_21.misc:header("sq", "Anti-Gapclose")
ove_0_21.misc:boolean("GapW", "Cast W on gapcloser", false)
ove_0_21.misc:menu("blacklist", "Anti-Gapclose Blacklist")

for iter_0_10 = 0, objManager.enemies_n - 1 do
	local ove_0_54 = objManager.enemies[iter_0_10]

	ove_0_21.misc.blacklist:boolean(ove_0_32[ove_0_54.ptr], "Block: " .. ove_0_32[ove_0_54.ptr], false)
end

ove_0_21:menu("draws", "Draw Settings")
ove_0_21.draws:header("ssz", "Draw Settings")
ove_0_21.draws:boolean("drawq", "Draw Q Range", true)
ove_0_21.draws:color("colorq", "  ^- Color", 0, 150, 80, 255)
ove_0_21.draws:boolean("draww", "Draw W Range", false)
ove_0_21.draws:color("colorw", "  ^- Color", 0, 150, 80, 255)
ove_0_21.draws:header("ss", "R Drawings")
ove_0_21.draws:menu("rdraw", "R Draw Settings")
ove_0_21.draws.rdraw:header("xddeee", "R Notification")
ove_0_21.draws.rdraw:boolean("drawNotification", "Draw R Notification", true)
ove_0_21.draws.rdraw:color("r_note_color", "When killable text color", 150, 255, 255, 255)
ove_0_21.draws.rdraw:color("r_note_color2", "When killable but can escape", 165, 165, 165, 255)
ove_0_21.draws.rdraw:header("headdd", "R Damage Indicator")
ove_0_21.draws.rdraw:boolean("drawfulldmgindicator", "Draw R damage indicator", true)
ove_0_21.draws.rdraw:color("colorRindicator", "R letter color:", 150, 255, 255, 255)
ove_0_21.draws.rdraw:color("colorCutindicator", "Indicator line color:", 150, 255, 255, 255)
ove_0_21.draws.rdraw:header("headdxz", "Healthbar Draws")
ove_0_21.draws.rdraw:boolean("drawdmgonhealthbar", "Draw R damage on healthbar", true)
ove_0_21.draws.rdraw:color("colordmghealthbar", "Healthbar [not killable] color:", 150, 255, 255, 100)
ove_0_21.draws.rdraw:color("colordmghealthbarK", "Healthbar [killable] color:", 150, 255, 255, 100)
ove_0_21.draws.rdraw:header("headdxyz", "Killable Indicator")
ove_0_21.draws.rdraw:boolean("drawkillableindicator", "Draw killable indicator", true)
ove_0_21.draws.rdraw:color("colorindiLines", "Indicator frame color:", 150, 255, 255, 255)
ove_0_21.draws.rdraw:color("colorindiText", "Indicator text color:", 150, 255, 255, 255)
ove_0_21.draws:header("souls", "Souls Draws")
ove_0_21.draws:boolean("drawsouls", "Draw Dropped Souls", true)
ove_0_21.draws:menu("soulsdraw", "Additional Drawing Settings")
ove_0_21.draws.soulsdraw:header("zeezee", "Souls Draw Settings")
ove_0_21.draws.soulsdraw:color("ColorCircle", "Circle Color", 150, 255, 150, 255)
ove_0_21.draws.soulsdraw:color("ColorText", "Text Color", 150, 255, 150, 255)
ove_0_21.draws.soulsdraw:color("ColorFrame", "Frame Color", 150, 255, 255, 255)
ove_0_21.draws:header("head", "Farm Toggle Draws")
ove_0_21.draws:boolean("drawtogglefarm", "Draw Farm Toggle", true)
ove_0_21.draws:menu("farmtog", "Additional Farm Tog Settings")
ove_0_21.draws.farmtog:header("zeezee", "Farm Toggle Draw Settings")
ove_0_21.draws.farmtog:slider("RejSize", "Text Size", 18, 0, 30, 1)
ove_0_21.draws.farmtog:slider("RejX", "X position:", 20, -100, 100, 1)
ove_0_21.draws.farmtog:slider("RejY", "Y position:", 30, -100, 100, 1)
ove_0_21.draws.farmtog:color("colorRejON", "Color ON", 0, 230, 150, 255)
ove_0_21.draws.farmtog:color("colorRejOFF", "Color OFF", 165, 165, 165, 165)
ove_0_21.draws:header("headreez", "Auto Q Toggle Draws")
ove_0_21.draws:boolean("drawtoggleautoq", "Draw Auto Q Toggle", true)
ove_0_21.draws:menu("Qtog", "Additional Farm Tog Settings")
ove_0_21.draws.Qtog:header("zeezee", "Auto Q Toggle Draw Settings")
ove_0_21.draws.Qtog:slider("RejSize", "Text Size", 18, 0, 30, 1)
ove_0_21.draws.Qtog:slider("RejX", "X position:", 20, -100, 100, 1)
ove_0_21.draws.Qtog:slider("RejY", "Y position:", 70, -100, 100, 1)
ove_0_21.draws.Qtog:color("colorRejON", "Color ON", 0, 230, 150, 255)
ove_0_21.draws.Qtog:color("colorRejOFF", "Color OFF", 165, 165, 165, 165)
ove_0_21.draws:header("heazd", "Healing Toggle Draws")
ove_0_21.draws:boolean("drawfroggle", "Draw Healing Toggle", true)
ove_0_21.draws:menu("lowmantog", "Additional Healing Tog Settings")
ove_0_21.draws.lowmantog:header("zeezee", "Healing Toggle Draw Settings")
ove_0_21.draws.lowmantog:slider("RejSize", "Text Size", 18, 0, 30, 1)
ove_0_21.draws.lowmantog:slider("RejX", "X position:", 20, -100, 100, 1)
ove_0_21.draws.lowmantog:slider("RejY", "Y position:", 50, -100, 100, 1)
ove_0_21.draws.lowmantog:color("colorRejON", "Color ON", 0, 230, 150, 255)
ove_0_21.draws.lowmantog:color("colorRejOFF", "Color OFF", 165, 165, 165, 165)
ove_0_21:menu("keys", "Key Settings")
ove_0_21.keys:header("ss", "Key Settings")
ove_0_21.keys:keybind("combokey", "Combo Key", "Space", nil)
ove_0_21.keys:keybind("harasskey", "Harass Key ", "C", nil)
ove_0_21.keys:keybind("clearkey", "Lane Clear Key", "V", nil)
ove_0_21.keys:keybind("lastkey", "Last Hit", "X", nil)
ove_0_21:header("headree", "- Healer AIO -")
ove_0_21:header("headree1", "- - Healer AIO - -")
ove_0_21:header("headree21", "- - - Healer AIO - - -")
ove_0_21:header("headree213", "Healer AIO")
ove_0_21.headree:set("visible", false)
ove_0_21.headree1:set("visible", false)
ove_0_21.headree21:set("visible", false)
ove_0_21.headree213:set("visible", false)

local ove_0_55 = false
local ove_0_56 = 0

local function ove_0_57()
	-- function 6
	if ove_0_21.rset.semiR_slow:get() then
		if ove_0_21.rset.Force_R_key:get() then
			if ove_0_55 == false and os.clock() > ove_0_56 then
				ove_0_55 = true
				ove_0_56 = os.clock() + 0.3
			end

			if ove_0_55 == true and os.clock() > ove_0_56 then
				ove_0_55 = false
				ove_0_56 = os.clock() + 0.3
			end
		end
	else
		ove_0_55 = false
	end
end

local ove_0_58 = vec3(394, 182.13250732422, 462)
local ove_0_59 = vec3(14340, 171.97772216797, 14390)
local ove_0_60 = false

if game.mapID == 11 then
	ove_0_60 = true
end

local ove_0_61 = ove_0_59

if player.team == 200 then
	ove_0_61 = ove_0_58
end

local function ove_0_62()
	-- function 7
	local slot_7_0 = ove_0_24.present.get_source_pos(player)
	local slot_7_1

	if slot_7_0 then
		local slot_7_2 = slot_7_0:dist(ove_0_61:to2D())
		local slot_7_3 = ove_0_45.speed
		local slot_7_4 = player.path.isActive and network.latency * 0.5 or network.latency

		slot_7_1 = slot_7_2 / slot_7_3 + 1 + slot_7_4 + 0.033
	end

	return slot_7_1
end

local ove_0_63 = 0.7
local ove_0_64 = 1.15
local ove_0_65 = {
	250,
	400,
	550
}
local ove_0_66 = {
	0.01,
	0.02,
	0.03,
	0.04,
	0.05,
	0.06,
	0.07,
	0.08,
	0.09,
	0.1,
	0.1,
	0.1,
	0.1,
	0.1,
	0.1,
	0.1,
	0.1,
	0.1
}

local function ove_0_67(arg_8_0, arg_8_1, arg_8_2)
	-- function 8
	local slot_8_0 = mathf.clamp(1, 18, player.levelRef)
	local slot_8_1 = mathf.clamp(1, 18, arg_8_0.levelRef)
	local slot_8_2 = 17.647 + 2.353 * slot_8_0

	if ove_0_33.Anivia and arg_8_0.ptr == ove_0_33.Anivia_ptr and arg_8_1 >= arg_8_0.passiveCooldownEndTime - game.time then
		return 0
	end

	if ove_0_33.Zac and arg_8_0.ptr == ove_0_33.Zac_ptr and arg_8_1 >= arg_8_0.passiveCooldownEndTime - game.time then
		return 0
	end

	local slot_8_3 = 0

	if player:spellSlot(3).level > 0 then
		slot_8_3 = ove_0_20.CalculatePhysicalDamage(arg_8_0, ove_0_65[player:spellSlot(3).level] + ove_0_20.GetTotalAP() * ove_0_63 + ove_0_20.GetBonusAD() * ove_0_64, player)
	end

	if player.buff[ove_0_28.Rune_DarkHarvestBuff] and arg_8_2 / arg_8_0.maxHealth * 100 < 50 and not player.buff[ove_0_28.Rune_DarkHarvestCooldownBuff] and slot_8_0 > 0 then
		if ove_0_20.GetBonusAD() >= ove_0_20.GetTotalAP() then
			slot_8_3 = slot_8_3 + ove_0_20.CalculatePhysicalDamage(arg_8_0, slot_8_2 + ove_0_20.GetTotalAP() * 0.15 + ove_0_20.GetBonusAD() * 0.25 + player.buff[ove_0_28.Rune_DarkHarvestBuff].stacks2 * ove_0_39, player)
		else
			slot_8_3 = slot_8_3 + ove_0_20.CalculateMagicDamage(arg_8_0, slot_8_2 + ove_0_20.GetTotalAP() * 0.15 + ove_0_20.GetBonusAD() * 0.25 + player.buff[ove_0_28.Rune_DarkHarvestBuff].stacks2 * ove_0_39, player)
		end
	end

	for iter_8_0 = 0, 5 do
		local slot_8_4 = arg_8_0:itemID(iter_8_0)
		local slot_8_5 = arg_8_0:spellSlot(iter_8_0 + 6)

		if slot_8_4 == ove_0_29.GuardianAngel.id and arg_8_1 >= slot_8_5.cooldown then
			return 0
		end

		if slot_8_4 == ove_0_29.WarmogsArmor.id and ove_0_30.WarmogsArmorPassive_isActive(arg_8_0) then
			return 0
		end

		if slot_8_4 == ove_0_29.BansheesVeil.id and arg_8_1 >= slot_8_5.cooldown then
			return 0
		end

		if slot_8_4 == ove_0_29.EdgeOfNight.id and arg_8_1 >= slot_8_5.cooldown then
			return 0
		end
	end

	if ove_0_33.Evelynn and arg_8_0.ptr == ove_0_33.Evelynn_ptr and arg_8_1 >= arg_8_0.passiveCooldownEndTime - game.time then
		slot_8_3 = slot_8_3 - ove_0_30.EvelynnPassivePerSecondHeal(arg_8_0) * arg_8_1
	end

	if ove_0_33.Yasuo and arg_8_0.ptr == ove_0_33.Yasuo_ptr then
		slot_8_3 = slot_8_3 - ove_0_30.YasuoPassiveShield(arg_8_0, 0, true)
	end

	if ove_0_33.Blitzcrank and arg_8_0.ptr == ove_0_33.Blitzcrank_ptr and arg_8_1 >= arg_8_0.passiveCooldownEndTime - game.time then
		slot_8_3 = slot_8_3 - arg_8_0.maxMana * 0.3
	end

	return slot_8_3
end

local ove_0_68 = {}
local ove_0_69 = {}

local function ove_0_70()
	-- function 9
	if not ove_0_21.base.base_r_enable:get() then
		return
	end

	for iter_9_0 = 0, objManager.enemies_n - 1 do
		local slot_9_0 = objManager.enemies[iter_9_0]

		if slot_9_0 then
			if slot_9_0.isDead then
				ove_0_68[slot_9_0.ptr] = slot_9_0.maxHealth
			elseif slot_9_0.isVisible then
				ove_0_68[slot_9_0.ptr] = 0
			else
				if not ove_0_68[slot_9_0.ptr] then
					ove_0_68[slot_9_0.ptr] = 0
				end

				if not ove_0_69[slot_9_0.ptr] then
					ove_0_69[slot_9_0.ptr] = 0
				end

				if ove_0_68[slot_9_0.ptr] and ove_0_69[slot_9_0.ptr] and ove_0_69[slot_9_0.ptr] < os.clock() then
					ove_0_68[slot_9_0.ptr] = ove_0_68[slot_9_0.ptr] + slot_9_0.healthRegenRate
					ove_0_69[slot_9_0.ptr] = os.clock() + 1
				end
			end
		end
	end
end

local ove_0_71 = {
	timers = {
		recallimproved = 7,
		superrecallimproved = 4,
		superrecall = 4,
		odinrecallimproved = 4,
		odinrecall = 4.5,
		SuperRecall = 4,
		recall = 8
	}
}

local function ove_0_72()
	-- function 10
	if not ove_0_60 then
		return
	end

	if ove_0_21.base.base_r_enable:get() then
		for iter_10_0 = 0, objManager.enemies_n - 1 do
			local slot_10_0 = objManager.enemies[iter_10_0]

			if not ove_0_71[slot_10_0.ptr] then
				ove_0_71[slot_10_0.ptr] = {}
			end

			local slot_10_1 = ove_0_71[slot_10_0.ptr]

			if slot_10_0.isRecalling and ove_0_71.timers[slot_10_0.recallName] then
				local slot_10_2 = ove_0_71.timers[slot_10_0.recallName]

				if slot_10_1.recall and slot_10_1.start then
					slot_10_1.time = slot_10_2 - (game.time - slot_10_1.start)

					return
				end

				slot_10_1.recall_total_time = ove_0_71.timers[slot_10_0.recallName]
				slot_10_1.time = slot_10_2
				slot_10_1.start = game.time
				slot_10_1.recall = true
			else
				slot_10_1.recall = false
			end
		end
	end
end

local ove_0_73 = 0
local ove_0_74 = {}
local ove_0_75 = {}
local ove_0_76 = {}
local ove_0_77 = false
local ove_0_78 = false

local function ove_0_79()
	-- function 11
	ove_0_74 = {}
	ove_0_78 = false
	ove_0_77 = false

	if not ove_0_60 then
		return
	end

	if not ove_0_21.base.base_r_enable:get() then
		return
	end

	if player:spellSlot(3).state ~= 0 and ove_0_73 < game.time then
		return
	end

	local slot_11_0 = false

	if ove_0_21.base.base_r_dont_enemies:get() and #ove_0_20.count_enemies_in_range(player.pos2D, ove_0_21.base.base_r_dont_range:get()) >= 1 then
		slot_11_0 = true
	end

	if ove_0_20.IsBehindWall(player.path.serverPos2D, ove_0_61:to2D(), ove_0_45.wall_width) then
		ove_0_77 = true
	end

	local slot_11_1 = ove_0_62()

	if slot_11_1 then
		for iter_11_0 = 0, objManager.enemies_n - 1 do
			local slot_11_2 = objManager.enemies[iter_11_0]

			if slot_11_2 and not slot_11_2.isDead and not slot_11_2.isZombie and ove_0_20.IsRecalling(slot_11_2) and ove_0_21.base.blacklist[ove_0_32[slot_11_2.ptr]] and not ove_0_21.base.blacklist[ove_0_32[slot_11_2.ptr]]:get() then
				if not ove_0_75[slot_11_2.ptr] then
					ove_0_75[slot_11_2.ptr] = 0
				end

				if not ove_0_76[slot_11_2.ptr] then
					ove_0_76[slot_11_2.ptr] = 0
				end

				local slot_11_3 = ove_0_71[slot_11_2.ptr]

				if slot_11_3.recall and slot_11_3.time and ove_0_68[slot_11_2.ptr] then
					local slot_11_4 = slot_11_2.health + slot_11_2.healthRegenRate + slot_11_2.healthRegenRate * (slot_11_3.time + 0.033)

					if slot_11_2.isVisible then
						slot_11_4 = mathf.clamp(0, slot_11_2.maxHealth, slot_11_4)
					else
						if ove_0_75[slot_11_2.ptr] < game.time then
							ove_0_76[slot_11_2.ptr] = ove_0_68[slot_11_2.ptr] + slot_11_2.health + slot_11_2.healthRegenRate + slot_11_2.healthRegenRate * (slot_11_3.time + 0.033)
						end

						ove_0_75[slot_11_2.ptr] = game.time + 0.1
						slot_11_4 = mathf.clamp(0, slot_11_2.maxHealth, ove_0_76[slot_11_2.ptr])
					end

					local slot_11_5 = slot_11_4 + slot_11_2.allShield + slot_11_2.physicalShield
					local slot_11_6 = ove_0_67(slot_11_2, slot_11_3.time + 0.033, slot_11_5)

					if ove_0_30.CoupDeGraceDamageBuffMod(slot_11_2, 0, true) > 1 and slot_11_5 / slot_11_2.maxHealth * 100 < 40 then
						slot_11_6 = slot_11_6 * ove_0_30.CoupDeGraceDamageBuffMod(slot_11_2, 0, true)
					end

					local slot_11_7 = slot_11_6 * ove_0_30.CutDownDamageBuffMod(slot_11_2) * ove_0_30.LastStandDamageBuffMod(slot_11_3.time + 0.033)

					if slot_11_7 > 0 then
						local slot_11_8 = slot_11_5 < slot_11_7
						local slot_11_9 = false
						local slot_11_10 = false
						local slot_11_11 = player.buff.elderdragonbuff

						if ove_0_21.rset.dragon_execute:get() and slot_11_11 and slot_11_11.endTime - game.time > slot_11_3.time + 0.033 and (slot_11_5 - slot_11_7) / slot_11_2.maxHealth * 100 < 20 then
							slot_11_9 = true
						end

						if ove_0_30.TheCollectorExecuteRawCheck(slot_11_2, slot_11_7, 0, true) and (slot_11_5 - slot_11_7) / slot_11_2.maxHealth * 100 < 5 then
							slot_11_10 = true
						end

						if slot_11_8 or slot_11_9 or slot_11_10 then
							ove_0_74[slot_11_2.ptr] = true
							ove_0_78 = true

							if slot_11_0 or ove_0_77 then
								return
							end

							if slot_11_1 + 1.15 > slot_11_3.time then
								ove_0_23.core.set_pause_attack(0.1)
							end

							if slot_11_1 >= slot_11_3.time and slot_11_3.time >= slot_11_1 - 0.033 then
								player:castSpell("pos", _R, ove_0_61)
							end
						end
					end
				end
			end
		end
	end
end

local function ove_0_80(arg_12_0)
	-- function 12
	if not arg_12_0.team then
		return false
	end

	if arg_12_0.team == TEAM_ENEMY then
		for iter_12_0 = 0, objManager.enemies_n - 1 do
			local slot_12_0 = objManager.enemies[iter_12_0]

			if slot_12_0.charName == arg_12_0.charName and slot_12_0.ptr ~= arg_12_0.ptr then
				return true
			end
		end
	end

	if arg_12_0.team == TEAM_ALLY then
		for iter_12_1 = 0, objManager.allies_n - 1 do
			local slot_12_1 = objManager.allies[iter_12_1]

			if slot_12_1.charName == arg_12_0.charName and slot_12_1.ptr ~= arg_12_0.ptr then
				return true
			end
		end
	end

	return false
end

local ove_0_81 = {}
local ove_0_82 = {}
local ove_0_83 = {}
local ove_0_84 = {
	FiddleSticksEffigy = true,
	JarvanIVStandard = true,
	IllaoiMinion = true,
	ApheliosTurret = true,
	SRU_Plant_Satchel = true,
	ThreshLantern = true,
	GangplankBarrel = true,
	SRU_Plant_Health = true,
	JhinTrap = true,
	JammerDevice = true,
	TeemoMushroom = true,
	KalistaSpawn = true,
	SightWard = true,
	YellowTrinket = true,
	NidaleeSpear = true,
	SRU_Plant_Vision = true
}
local ove_0_85 = {
	IvernMinion = true,
	YorickGhoulMelee = true,
	MalzaharVoidling = true,
	Tibbers = true,
	Spiderling = true,
	RebirthBlob = true,
	YorickBigGhoul = true,
	["Jack In The Box"] = true,
	["H-28G Evolution Turret"] = true
}

local function ove_0_86(arg_13_0)
	-- function 13
	if arg_13_0.owner and arg_13_0.owner.ptr == player.ptr and arg_13_0.charName and arg_13_0.charName == "SennaSoul" then
		ove_0_81[arg_13_0.ptr] = arg_13_0
		ove_0_82[arg_13_0.ptr] = os.clock() + 8
	end

	if arg_13_0.charName then
		if ove_0_84[arg_13_0.charName] then
			ove_0_83[arg_13_0.ptr] = arg_13_0
		end

		if (ove_0_33.Shaco or ove_0_37.Shaco) and arg_13_0.charName == "Shaco" and ove_0_80(arg_13_0) then
			ove_0_83[arg_13_0.ptr] = arg_13_0
		end

		if (ove_0_33.Neeko or ove_0_37.Neeko) and arg_13_0.charName == "Neeko" and ove_0_80(arg_13_0) then
			ove_0_83[arg_13_0.ptr] = arg_13_0
		end

		if (ove_0_33.Leblanc or ove_0_37.Leblanc) and arg_13_0.charName == "Leblanc" and ove_0_80(arg_13_0) then
			ove_0_83[arg_13_0.ptr] = arg_13_0
		end

		if (ove_0_33.MonkeyKing or ove_0_37.MonkeyKing) and arg_13_0.charName == "MonkeyKing" and ove_0_80(arg_13_0) then
			ove_0_83[arg_13_0.ptr] = arg_13_0
		end
	end

	if arg_13_0.name then
		if ove_0_85[arg_13_0.name] then
			ove_0_83[arg_13_0.ptr] = arg_13_0
		end

		if (ove_0_33.Zyra or ove_0_37.Zyra) and arg_13_0.name == "Seed" and arg_13_0.health and arg_13_0.health > 1 then
			ove_0_83[arg_13_0.ptr] = arg_13_0
		end
	end

	if ove_0_33.Illaoi and arg_13_0.owner and arg_13_0.owner.ptr == ove_0_33.Illaoi_ptr and arg_13_0.buff and arg_13_0.buff[ove_0_28.IllaoiSpiritBuff] then
		ove_0_83[arg_13_0.ptr] = arg_13_0
	end

	if ove_0_37.Illaoi and arg_13_0.owner and arg_13_0.owner.ptr == ove_0_37.Illaoi_ptr and arg_13_0.buff and arg_13_0.buff[ove_0_28.IllaoiSpiritBuff] then
		ove_0_83[arg_13_0.ptr] = arg_13_0
	end
end

for iter_0_11 = 0, objManager.maxObjects - 1 do
	local ove_0_87 = objManager.get(iter_0_11)

	if ove_0_87 then
		ove_0_86(ove_0_87)
	end
end

local function ove_0_88(arg_14_0)
	-- function 14
	ove_0_86(arg_14_0)
end

local function ove_0_89(arg_15_0)
	-- function 15
	ove_0_81[arg_15_0.ptr] = nil
	ove_0_82[arg_15_0.ptr] = nil
	ove_0_83[arg_15_0.ptr] = nil
end

local function ove_0_90()
	-- function 16
	return player:basicAttack(0).clientWindUpTime * 0.8
end

local ove_0_91 = 0
local ove_0_92 = 0
local ove_0_93 = 0
local ove_0_94 = 0
local ove_0_95 = 0
local ove_0_96 = 0
local ove_0_97 = 0
local ove_0_98 = 0
local ove_0_99
local ove_0_100 = ove_0_90()
local ove_0_101 = 0
local ove_0_102 = 0

local function ove_0_103(arg_17_0)
	-- function 17
	local slot_17_0 = arg_17_0.owner
	local slot_17_1 = arg_17_0.target

	if slot_17_0 and slot_17_0.type == TYPE_HERO and slot_17_0.team == TEAM_ENEMY then
		if ove_0_33.Garen and arg_17_0.name == "GarenW" then
			ove_0_101 = game.time + 0.25
		end

		if ove_0_33.MasterYi and arg_17_0.name == "Meditate" then
			ove_0_91 = game.time + 0.25
		end

		if ove_0_33.Warwick and arg_17_0.name == "WarwickE" then
			ove_0_94 = game.time + 0.25
		end

		if ove_0_33.Annie and arg_17_0.name == "AnnieE" then
			ove_0_92 = game.time + 0.25
		end

		if ove_0_33.Alistar and arg_17_0.name == "FerociousHowl" then
			ove_0_102 = game.time + 0.25
		end

		if ove_0_33.Sivir and arg_17_0.name == "SivirE" then
			ove_0_96 = game.time + 0.25
		end

		if ove_0_33.Braum and arg_17_0.name == "BraumE" then
			ove_0_97 = game.time + 0.25
		end

		if ove_0_33.Pantheon and arg_17_0.name == "PantheonE" then
			ove_0_98 = game.time + 0.25
		end

		if ove_0_33.Fiora and arg_17_0.name == "FioraW" then
			ove_0_93 = game.time + 0.25
		end

		if ove_0_33.Nocturne and arg_17_0.name == "NocturneW" then
			ove_0_95 = game.time + 0.25
		end
	end

	if slot_17_0 and slot_17_0.ptr == player.ptr and (arg_17_0.isBasicAttack or arg_17_0.name:find("BasicAttack") or arg_17_0.name:find("Crit")) and slot_17_1 and slot_17_1.type and (slot_17_1.type == TYPE_HERO or slot_17_1.type == TYPE_TURRET or slot_17_1.type == TYPE_INHIB or slot_17_1.type == TYPE_NEXUS) then
		ove_0_99 = slot_17_1
	end

	if slot_17_0 and slot_17_0.ptr == player.ptr and arg_17_0.name == "SennaR" then
		ove_0_73 = game.time + 3

		if ove_0_55 then
			ove_0_55 = false
		end
	end
end

local function ove_0_104(arg_18_0, arg_18_1, arg_18_2)
	-- function 18
	if arg_18_2 <= ove_0_40.range then
		arg_18_0.obj = arg_18_1

		return true
	end
end

local function ove_0_105()
	-- function 19
	return ove_0_25.get_result(ove_0_104).obj
end

local function ove_0_106(arg_20_0, arg_20_1, arg_20_2)
	-- function 20
	if arg_20_2 <= ove_0_43.range then
		arg_20_0.obj = arg_20_1

		return true
	end
end

local function ove_0_107()
	-- function 21
	return ove_0_25.get_result(ove_0_106).obj
end

local function ove_0_108(arg_22_0, arg_22_1, arg_22_2)
	-- function 22
	if ove_0_20.IsValidTarget(arg_22_1) then
		arg_22_0.obj = arg_22_1

		return true
	end
end

local function ove_0_109()
	-- function 23
	return ove_0_25.get_result(ove_0_108).obj
end

local ove_0_110 = ove_0_20.DMG_RED_MasterYi()
local ove_0_111 = ove_0_20.DMG_RED_Warwick()
local ove_0_112 = ove_0_20.DMG_RED_Braum()
local ove_0_113 = ove_0_20.DMG_RED_Alistar()
local ove_0_114 = ove_0_20.DMG_RED_Garen_W()
local ove_0_115 = ove_0_20.DMG_RED_PhantomHP()
local ove_0_116 = ove_0_20.DMG_RED_Baron()
local ove_0_117 = ove_0_20.DMG_RED_ElderDragonBurn()
local ove_0_118 = {
	30,
	65,
	100,
	135,
	170
}

local function ove_0_119(arg_24_0)
	-- function 24
	local slot_24_0 = mathf.clamp(1, 18, player.levelRef)
	local slot_24_1 = mathf.clamp(1, 18, arg_24_0.levelRef)
	local slot_24_2 = ove_0_20.DMG_RED_Gragas(ove_0_20.GetTotalAP(arg_24_0))
	local slot_24_3 = ove_0_20.DMG_RED_HarvestDmg(slot_24_0)
	local slot_24_4 = 0
	local slot_24_5 = arg_24_0.ptr

	if player:spellSlot(0).level > 0 then
		slot_24_4 = ove_0_20.CalculatePhysicalDamage(arg_24_0, ove_0_118[player:spellSlot(0).level] + (ove_0_20.GetBonusAD() * 0.5 + ove_0_20.GetTotalAD() * 0.2), player)
	end

	if arg_24_0.buff.sennapassivemarker and arg_24_0.buff.sennapassivemarker.endTime - game.time > 0.5 and slot_24_0 > 0 then
		slot_24_4 = slot_24_4 + ove_0_20.CalculatePhysicalDamage(arg_24_0, ove_0_66[slot_24_0] * arg_24_0.health, player)
	end

	if player.buff.itemmagicshankcharge and player.buff.itemmagicshankcharge.stacks == 100 and slot_24_0 > 0 then
		slot_24_4 = slot_24_4 + ove_0_20.CalculateMagicDamage(arg_24_0, 100 + ove_0_20.GetTotalAP() * 0.1, player)
	end

	if player.buff[ove_0_28.Rune_DarkHarvestBuff] and (arg_24_0.health + arg_24_0.healthRegenRate * 1.3) / arg_24_0.maxHealth * 100 < 50 and not player.buff[ove_0_28.Rune_DarkHarvestCooldownBuff] and slot_24_0 > 0 then
		if ove_0_20.GetBonusAD() >= ove_0_20.GetTotalAP() then
			slot_24_4 = slot_24_4 + ove_0_20.CalculatePhysicalDamage(arg_24_0, slot_24_3 + ove_0_20.GetTotalAP() * 0.15 + ove_0_20.GetBonusAD() * 0.25 + player.buff[ove_0_28.Rune_DarkHarvestBuff].stacks2 * ove_0_39, player)
		else
			slot_24_4 = slot_24_4 + ove_0_20.CalculateMagicDamage(arg_24_0, slot_24_3 + ove_0_20.GetTotalAP() * 0.15 + ove_0_20.GetBonusAD() * 0.25 + player.buff[ove_0_28.Rune_DarkHarvestBuff].stacks2 * ove_0_39, player)
		end
	end

	if player.buff.elderdragonbuff then
		if math.floor(game.time / 60) < 25 and 90 - arg_24_0.healthRegenRate * (3.1 + ove_0_100) > 0 then
			slot_24_4 = slot_24_4 + (90 - arg_24_0.healthRegenRate * (3.1 + ove_0_100))
		end

		if math.floor(game.time / 60) >= 25 and not (math.floor(game.time / 60) > 45) and ove_0_117[math.floor(game.time / 60) - 24] - arg_24_0.healthRegenRate * (3.1 + ove_0_100) > 0 then
			slot_24_4 = slot_24_4 + (ove_0_117[math.floor(game.time / 60) - 24] - arg_24_0.healthRegenRate * (3.1 + ove_0_100))
		end

		if math.floor(game.time / 60) > 45 and 225 - arg_24_0.healthRegenRate * (3.1 + ove_0_100) > 0 then
			slot_24_4 = slot_24_4 + (270 - arg_24_0.healthRegenRate * (3.1 + ove_0_100))
		end
	end

	local slot_24_6 = arg_24_0.health + arg_24_0.allShield + arg_24_0.physicalShield + arg_24_0.healthRegenRate * ove_0_100

	if player.buff.elderdragonbuff and player.buff.elderdragonbuff.endTime - game.time >= ove_0_100 + 0.1 and (slot_24_6 - slot_24_4) / arg_24_0.maxHealth * 100 < 20 then
		slot_24_4 = 99999999
	end

	if ove_0_33.Garen and slot_24_5 == ove_0_33.Garen_ptr and arg_24_0.buff.garenw and arg_24_0.buff.garenw.endTime - game.time >= ove_0_100 then
		slot_24_4 = slot_24_4 * 0.7
	end

	if ove_0_33.MasterYi and slot_24_5 == ove_0_33.MasterYi_ptr and arg_24_0.buff.meditate and arg_24_0.buff.meditate.endTime - game.time >= ove_0_100 then
		slot_24_4 = slot_24_4 * (1 - ove_0_110[arg_24_0:spellSlot(1).level])
	end

	if arg_24_0.buff[ove_0_28.ChemtechDragonSoulBuff] then
		slot_24_4 = slot_24_4 * ove_0_30.ChemtechDragonSoulReductionMod()
	end

	if ove_0_33.Belveth and ove_0_30.Belveth_E_isActive(arg_24_0, ove_0_100, false, 0) then
		slot_24_4 = slot_24_4 * ove_0_30.Belveth_E_ReductionMod(arg_24_0)
	end

	if ove_0_33.Gragas and slot_24_5 == ove_0_33.Gragas_ptr and arg_24_0.buff.gragaswself and arg_24_0.buff.gragaswself.endTime - game.time >= ove_0_100 then
		slot_24_4 = slot_24_4 * (1 - slot_24_2[arg_24_0:spellSlot(1).level])
	end

	if ove_0_33.Warwick and slot_24_5 == ove_0_33.Warwick_ptr and arg_24_0.buff.warwicke and arg_24_0.buff.warwicke.endTime - game.time >= ove_0_100 then
		slot_24_4 = slot_24_4 * (1 - ove_0_111[arg_24_0:spellSlot(2).level])
	end

	if ove_0_33.Galio and slot_24_5 == ove_0_33.Galio_ptr and ove_0_30.Galio_W_isActive(arg_24_0, ove_0_100 - 0.05, false, 0) then
		slot_24_4 = slot_24_4 * ove_0_30.Galio_W_Physical_ReductionMod(arg_24_0)
	end

	if ove_0_33.Braum and slot_24_5 == ove_0_33.Braum_ptr and arg_24_0.buff.braumeshieldbuff and arg_24_0.buff.braumeshieldbuff.endTime - game.time >= ove_0_100 then
		slot_24_4 = slot_24_4 * (1 - ove_0_112[arg_24_0:spellSlot(2).level])
	end

	if ove_0_33.Alistar and slot_24_5 == ove_0_33.Alistar_ptr and arg_24_0.buff.ferocioushowl and arg_24_0.buff.ferocioushowl.endTime - game.time >= ove_0_100 then
		slot_24_4 = slot_24_4 * (1 - ove_0_113[arg_24_0:spellSlot(3).level])
	end

	if ove_0_33.Malzahar and slot_24_5 == ove_0_33.Malzahar_ptr and arg_24_0.passiveCooldownEndTime - game.time <= ove_0_100 then
		slot_24_4 = slot_24_4 * 0.1
	end

	if ove_0_33.Tryndamere and slot_24_5 == ove_0_33.Tryndamere_ptr and arg_24_0.buff.undyingrage and arg_24_0.buff.undyingrage.endTime - game.time >= ove_0_100 then
		slot_24_4 = 0
	end

	if ove_0_33.Sion and slot_24_5 == ove_0_33.Sion_ptr and arg_24_0.buff.sionpassivezombie then
		slot_24_4 = 0
	end

	if ove_0_33.Kindred and arg_24_0.buff.kindredrnodeathbuff and arg_24_0.buff.kindredrnodeathbuff.endTime - game.time >= ove_0_100 then
		slot_24_4 = 0
	end

	if arg_24_0.buff[ove_0_28.BUFF_ENUM_INVULNERABILITY] or arg_24_0.buff[ove_0_28.BUFF_ENUM_SPELLSHIELD] then
		slot_24_4 = 0
	end

	if ove_0_33.Evelynn and arg_24_0.ptr == ove_0_33.Evelynn_ptr and arg_24_0.passiveCooldownEndTime - game.time <= ove_0_100 + 0.1 then
		slot_24_4 = slot_24_4 - (ove_0_30.EvelynnPassivePerSecondHeal(arg_24_0) * ove_0_100 + 0.1)
	end

	if ove_0_33.Yasuo and arg_24_0.ptr == ove_0_33.Yasuo_ptr then
		slot_24_4 = slot_24_4 - ove_0_30.YasuoPassiveShield(arg_24_0, 20)
	end

	if ove_0_33.Blitzcrank and slot_24_5 == ove_0_33.Blitzcrank_ptr and arg_24_0.passiveCooldownEndTime - game.time <= ove_0_100 then
		slot_24_4 = slot_24_4 - arg_24_0.maxMana * 0.3
	end

	if player.buff.summonerexhaust then
		slot_24_4 = slot_24_4 * 0.6
	end

	if ove_0_33.Fiora and slot_24_5 == ove_0_33.Fiora_ptr and arg_24_0.buff.fioraw and arg_24_0.buff.fioraw.endTime - game.time >= ove_0_100 then
		slot_24_4 = 0
	end

	if ove_0_33.Pantheon and slot_24_5 == ove_0_33.Pantheon_ptr and arg_24_0.buff.pantheonefacinglock and arg_24_0.buff.pantheonefacinglock.endTime - game.time >= ove_0_100 then
		slot_24_4 = 0
	end

	for iter_24_0 = 0, 5 do
		if arg_24_0:itemID(iter_24_0) == 3046 and arg_24_0:spellSlot(iter_24_0 + 6).cooldown <= 1.35 then
			slot_24_4 = slot_24_4 - ove_0_115[slot_24_1]
		end
	end

	return slot_24_4 - (arg_24_0.allShield + arg_24_0.physicalShield + arg_24_0.healthRegenRate * (ove_0_100 + 0.1))
end

local function ove_0_120(arg_25_0)
	-- function 25
	local slot_25_0 = 0

	if player:spellSlot(0).level > 0 then
		slot_25_0 = ove_0_20.CalculatePhysicalDamage(arg_25_0, ove_0_118[player:spellSlot(0).level] + ove_0_20.GetBonusAD() * 0.5, player)
	end

	if arg_25_0.buff.exaltedwithbaronnashorminion then
		if math.floor(game.time / 60) <= 20 then
			if arg_25_0.charName:find("MinionRanged") then
				slot_25_0 = slot_25_0 * 0.5
			end

			if arg_25_0.charName:find("MinionMelee") then
				slot_25_0 = slot_25_0 * 0.5
			end
		end

		if math.floor(game.time / 60) >= 21 and not (math.floor(game.time / 60) >= 40) then
			if arg_25_0.charName:find("MinionRanged") then
				slot_25_0 = slot_25_0 * ove_0_116[math.floor(game.time / 60) - 20]
			end

			if arg_25_0.charName:find("MinionMelee") then
				slot_25_0 = slot_25_0 * ove_0_116[math.floor(game.time / 60) - 20]
			end
		end

		if math.floor(game.time / 60) >= 40 then
			if arg_25_0.charName:find("MinionRanged") then
				slot_25_0 = slot_25_0 * 0.3
			end

			if arg_25_0.charName:find("MinionMelee") then
				slot_25_0 = slot_25_0 * 0.3
			end
		end
	end

	return slot_25_0
end

local function ove_0_121(arg_26_0)
	-- function 26
	local slot_26_0 = mathf.clamp(1, 18, player.levelRef)
	local slot_26_1 = mathf.clamp(1, 18, arg_26_0.levelRef)
	local slot_26_2 = arg_26_0.ptr
	local slot_26_3 = ove_0_20.DMG_Shield_Annie(ove_0_20.GetTotalAP(arg_26_0))
	local slot_26_4 = ove_0_20.DMG_RED_Gragas(ove_0_20.GetTotalAP(arg_26_0))
	local slot_26_5 = ove_0_20.DMG_RED_HarvestDmg(slot_26_0)
	local slot_26_6 = 0

	if player:spellSlot(3).level > 0 then
		slot_26_6 = ove_0_20.CalculatePhysicalDamage(arg_26_0, ove_0_65[player:spellSlot(3).level] + ove_0_20.GetTotalAP() * ove_0_63 + ove_0_20.GetBonusAD() * ove_0_64, player)
	end

	if arg_26_0.buff.sennapassivemarker and arg_26_0.buff.sennapassivemarker.endTime - game.time > 1.3 and slot_26_0 > 0 then
		slot_26_6 = slot_26_6 + ove_0_20.CalculatePhysicalDamage(arg_26_0, ove_0_66[slot_26_0] * arg_26_0.health, player)
	end

	if player.buff.itemmagicshankcharge and player.buff.itemmagicshankcharge.stacks == 100 and slot_26_0 > 0 then
		slot_26_6 = slot_26_6 + ove_0_20.CalculateMagicDamage(arg_26_0, 100 + ove_0_20.GetTotalAP() * 0.1, player)
	end

	if player.buff[ove_0_28.Rune_DarkHarvestBuff] and (arg_26_0.health + arg_26_0.healthRegenRate * 1.3) / arg_26_0.maxHealth * 100 < 50 and not player.buff[ove_0_28.Rune_DarkHarvestCooldownBuff] and slot_26_0 > 0 then
		if ove_0_20.GetBonusAD() >= ove_0_20.GetTotalAP() then
			slot_26_6 = slot_26_6 + ove_0_20.CalculatePhysicalDamage(arg_26_0, slot_26_5 + ove_0_20.GetTotalAP() * 0.15 + ove_0_20.GetBonusAD() * 0.25 + player.buff[ove_0_28.Rune_DarkHarvestBuff].stacks2 * ove_0_39, player)
		else
			slot_26_6 = slot_26_6 + ove_0_20.CalculateMagicDamage(arg_26_0, slot_26_5 + ove_0_20.GetTotalAP() * 0.15 + ove_0_20.GetBonusAD() * 0.25 + player.buff[ove_0_28.Rune_DarkHarvestBuff].stacks2 * ove_0_39, player)
		end
	end

	if ove_0_21.rset.dragon_burn:get() and player.buff.elderdragonbuff then
		if math.floor(game.time / 60) < 25 and 90 - arg_26_0.healthRegenRate * 4.4 > 0 then
			slot_26_6 = slot_26_6 + (90 - arg_26_0.healthRegenRate * 4.4)
		end

		if math.floor(game.time / 60) >= 25 and not (math.floor(game.time / 60) > 45) and ove_0_117[math.floor(game.time / 60) - 24] - arg_26_0.healthRegenRate * 4.4 > 0 then
			slot_26_6 = slot_26_6 + (ove_0_117[math.floor(game.time / 60) - 24] - arg_26_0.healthRegenRate * 4.4)
		end

		if math.floor(game.time / 60) > 45 and 225 - arg_26_0.healthRegenRate * 4.4 > 0 then
			slot_26_6 = slot_26_6 + (270 - arg_26_0.healthRegenRate * 4.4)
		end
	end

	local slot_26_7 = arg_26_0.health + arg_26_0.allShield + arg_26_0.physicalShield + arg_26_0.healthRegenRate * 1.31

	if ove_0_21.rset.dragon_execute:get() and player.buff.elderdragonbuff and player.buff.elderdragonbuff.endTime - game.time >= 1.31 and (slot_26_7 - slot_26_6) / arg_26_0.maxHealth * 100 < 20 then
		slot_26_6 = 99999999
	end

	if ove_0_33.Garen and slot_26_2 == ove_0_33.Garen_ptr and arg_26_0.buff.garenw and arg_26_0.buff.garenw.endTime - game.time >= 0.9 then
		slot_26_6 = slot_26_6 * 0.7
	end

	if ove_0_33.MasterYi and slot_26_2 == ove_0_33.MasterYi_ptr and arg_26_0.buff.meditate and arg_26_0.buff.meditate.endTime - game.time >= 0.9 then
		slot_26_6 = slot_26_6 * (1 - ove_0_110[arg_26_0:spellSlot(1).level])
	end

	if arg_26_0.buff[ove_0_28.ChemtechDragonSoulBuff] then
		slot_26_6 = slot_26_6 * ove_0_30.ChemtechDragonSoulReductionMod()
	end

	if ove_0_33.Belveth and ove_0_30.Belveth_E_isActive(arg_26_0, 0.9, true, 1.1) then
		slot_26_6 = slot_26_6 * ove_0_30.Belveth_E_ReductionMod(arg_26_0)
	end

	if ove_0_33.Gragas and slot_26_2 == ove_0_33.Gragas_ptr and arg_26_0.buff.gragaswself and arg_26_0.buff.gragaswself.endTime - game.time >= 0.9 then
		slot_26_6 = slot_26_6 * (1 - slot_26_4[arg_26_0:spellSlot(1).level])
	end

	if ove_0_33.Warwick and slot_26_2 == ove_0_33.Warwick_ptr and arg_26_0.buff.warwicke and arg_26_0.buff.warwicke.endTime - game.time >= 0.9 then
		slot_26_6 = slot_26_6 * (1 - ove_0_111[arg_26_0:spellSlot(2).level])
	end

	if ove_0_33.Galio and slot_26_2 == ove_0_33.Galio_ptr and ove_0_30.Galio_W_isActive(arg_26_0, 0.9, true, 1.1) then
		slot_26_6 = slot_26_6 * ove_0_30.Galio_W_Physical_ReductionMod(arg_26_0)
	end

	if ove_0_33.Braum and slot_26_2 == ove_0_33.Braum_ptr and arg_26_0.buff.braumeshieldbuff and arg_26_0.buff.braumeshieldbuff.endTime - game.time >= 0.9 then
		slot_26_6 = slot_26_6 * (1 - ove_0_112[arg_26_0:spellSlot(2).level])
	end

	if ove_0_33.Alistar and slot_26_2 == ove_0_33.Alistar_ptr and arg_26_0.buff.ferocioushowl and arg_26_0.buff.ferocioushowl.endTime - game.time >= 0.9 then
		slot_26_6 = slot_26_6 * (1 - ove_0_113[arg_26_0:spellSlot(3).level])
	end

	if ove_0_33.Malzahar and slot_26_2 == ove_0_33.Malzahar_ptr and arg_26_0.passiveCooldownEndTime - game.time <= 1.1 then
		slot_26_6 = slot_26_6 * 0.1
	end

	if ove_0_33.Tryndamere and slot_26_2 == ove_0_33.Tryndamere_ptr and arg_26_0.buff.undyingrage and arg_26_0.buff.undyingrage.endTime - game.time >= 0.9 then
		slot_26_6 = 0
	end

	if ove_0_33.Sion and slot_26_2 == ove_0_33.Sion_ptr and arg_26_0.buff.sionpassivezombie then
		slot_26_6 = 0
	end

	if ove_0_33.Kindred and arg_26_0.buff.kindredrnodeathbuff and arg_26_0.buff.kindredrnodeathbuff.endTime - game.time >= 0.9 then
		slot_26_6 = 0
	end

	if arg_26_0.buff[ove_0_28.BUFF_ENUM_INVULNERABILITY] or arg_26_0.buff[ove_0_28.BUFF_ENUM_SPELLSHIELD] then
		slot_26_6 = 0
	end

	if ove_0_33.Evelynn and arg_26_0.ptr == ove_0_33.Evelynn_ptr and arg_26_0.passiveCooldownEndTime - game.time <= 1.5 then
		slot_26_6 = slot_26_6 - ove_0_30.EvelynnPassivePerSecondHeal(arg_26_0) * 1.5
	end

	if ove_0_33.Yasuo and arg_26_0.ptr == ove_0_33.Yasuo_ptr then
		slot_26_6 = slot_26_6 - ove_0_30.YasuoPassiveShield(arg_26_0, 50)
	end

	if ove_0_33.Blitzcrank and slot_26_2 == ove_0_33.Blitzcrank_ptr and arg_26_0.passiveCooldownEndTime - game.time <= 1.1 then
		slot_26_6 = slot_26_6 - arg_26_0.maxMana * 0.3
	end

	if player.buff.summonerexhaust then
		slot_26_6 = slot_26_6 * 0.6
	end

	if ove_0_33.Fiora and slot_26_2 == ove_0_33.Fiora_ptr and arg_26_0.buff.fioraw and arg_26_0.buff.fioraw.endTime - game.time >= 0.9 then
		slot_26_6 = 0
	end

	if ove_0_33.Pantheon and slot_26_2 == ove_0_33.Pantheon_ptr and arg_26_0.buff.pantheonefacinglock and arg_26_0.buff.pantheonefacinglock.endTime - game.time >= 0.9 then
		slot_26_6 = 0
	end

	for iter_26_0 = 0, 5 do
		if arg_26_0:itemID(iter_26_0) == 3046 and arg_26_0:spellSlot(iter_26_0 + 6).cooldown <= 1.35 then
			slot_26_6 = slot_26_6 - ove_0_115[slot_26_1]
		end
	end

	if ove_0_33.Garen and arg_26_0.ptr == ove_0_33.Garen_ptr and arg_26_0:spellSlot(1).level >= 1 and (arg_26_0:spellSlot(1).cooldown <= 1.1 or ove_0_101 > game.time) then
		slot_26_6 = slot_26_6 - ((arg_26_0.health - arg_26_0.baseHealth) * 0.2 + slot_26_6 * 0.7 + ove_0_114[arg_26_0:spellSlot(1).level])
	end

	if ove_0_33.MasterYi and slot_26_2 == ove_0_33.MasterYi_ptr and arg_26_0:spellSlot(1).level >= 1 and (arg_26_0:spellSlot(1).cooldown <= 1.1 or ove_0_91 > game.time) then
		slot_26_6 = slot_26_6 * (1 - ove_0_110[arg_26_0:spellSlot(1).level])
	end

	if ove_0_33.Warwick and slot_26_2 == ove_0_33.Warwick_ptr and arg_26_0:spellSlot(2).level >= 1 and (arg_26_0:spellSlot(2).cooldown <= 1.1 or ove_0_94 > game.time) then
		slot_26_6 = slot_26_6 * (1 - ove_0_111[arg_26_0:spellSlot(2).level])
	end

	if ove_0_33.Annie and slot_26_2 == ove_0_33.Annie_ptr and arg_26_0:spellSlot(2).level >= 1 and (arg_26_0:spellSlot(2).cooldown <= 1.1 or ove_0_92 > game.time) then
		slot_26_6 = slot_26_6 - slot_26_3[arg_26_0:spellSlot(2).level]
	end

	if ove_0_33.Alistar and slot_26_2 == ove_0_33.Alistar_ptr and arg_26_0:spellSlot(3).level >= 1 and (arg_26_0:spellSlot(3).cooldown <= 1.1 or ove_0_102 > game.time) then
		slot_26_6 = slot_26_6 * (1 - ove_0_113[arg_26_0:spellSlot(3).level])
	end

	if ove_0_33.Sivir and slot_26_2 == ove_0_33.Sivir_ptr and arg_26_0:spellSlot(2).level >= 1 and (arg_26_0:spellSlot(2).cooldown <= 1.1 or ove_0_96 > game.time) then
		slot_26_6 = 0
	end

	if ove_0_33.Fiora and slot_26_2 == ove_0_33.Fiora_ptr and arg_26_0:spellSlot(1).level >= 1 and (arg_26_0:spellSlot(1).cooldown <= 1.1 or ove_0_93 > game.time) then
		slot_26_6 = 0
	end

	if ove_0_33.Nocturne and slot_26_2 == ove_0_33.Nocturne_ptr and arg_26_0:spellSlot(1).level >= 1 and (arg_26_0:spellSlot(1).cooldown <= 1.1 or ove_0_95 > game.time) then
		slot_26_6 = 0
	end

	if ove_0_33.Pantheon and slot_26_2 == ove_0_33.Pantheon_ptr and arg_26_0:spellSlot(2).level >= 1 and (arg_26_0:spellSlot(2).cooldown <= 1.1 or ove_0_98 > game.time) then
		slot_26_6 = 0
	end

	if ove_0_33.Braum and slot_26_2 == ove_0_33.Braum_ptr and arg_26_0:spellSlot(2).level >= 1 and (arg_26_0:spellSlot(2).cooldown <= 1.1 or ove_0_97 > game.time) then
		slot_26_6 = 0
	end

	if ove_0_33.Shaco and slot_26_2 == ove_0_33.Shaco_ptr and arg_26_0:spellSlot(3).level >= 1 and arg_26_0:spellSlot(3).cooldown <= 1.1 then
		slot_26_6 = 0
	end

	for iter_26_1 = 4, 5 do
		if arg_26_0:spellSlot(iter_26_1).name == ove_0_31.SummonerHeal_Slot.slot_name and arg_26_0:spellSlot(iter_26_1).cooldown <= 1.35 then
			slot_26_6 = slot_26_6 - ove_0_30.SummonerHealHeal(arg_26_0, 1.35)
		end

		if arg_26_0:spellSlot(iter_26_1).name == ove_0_31.SummonerBarrier_Slot.slot_name and arg_26_0:spellSlot(iter_26_1).cooldown <= 1.35 then
			slot_26_6 = slot_26_6 - ove_0_30.SummonerBarrierShield(arg_26_0)
		end
	end

	return slot_26_6 - (arg_26_0.allShield + arg_26_0.physicalShield + arg_26_0.healthRegenRate * 1.35)
end

local ove_0_122 = {
	ove_0_28.BUFF_ENUM_STUN,
	ove_0_28.BUFF_ENUM_TAUNT,
	ove_0_28.BUFF_ENUM_POLYMORPH,
	ove_0_28.BUFF_ENUM_SNARE,
	ove_0_28.BUFF_ENUM_FEAR,
	ove_0_28.BUFF_ENUM_CHARM,
	ove_0_28.BUFF_ENUM_SUPPRESSION,
	ove_0_28.BUFF_ENUM_KNOCKUP,
	ove_0_28.BUFF_ENUM_KNOCKBACK,
	ove_0_28.BUFF_ENUM_ASLEEP
}

local function ove_0_123(arg_27_0)
	-- function 27
	if ove_0_21.rset.dont_r_dashers:get() and arg_27_0 and arg_27_0.ptr ~= 0 then
		local slot_27_0 = 1 + network.latency

		if ove_0_20.IsCustomLocked(ove_0_122, arg_27_0, 1) then
			return false
		end

		if ove_0_21.rset.dont_r_dashers_flash:get() then
			if arg_27_0:spellSlot(4).name == "SummonerFlash" and slot_27_0 >= arg_27_0:spellSlot(4).cooldown then
				return true
			end

			if arg_27_0:spellSlot(5).name == "SummonerFlash" and slot_27_0 >= arg_27_0:spellSlot(5).cooldown then
				return true
			end
		end

		local slot_27_1 = ove_0_32[arg_27_0.ptr]

		if ove_0_27[slot_27_1] then
			local slot_27_2 = ove_0_27[slot_27_1].slot
			local slot_27_3 = ove_0_27[slot_27_1].slot2

			if arg_27_0:spellSlot(slot_27_2).level >= 1 and slot_27_0 >= arg_27_0:spellSlot(slot_27_2).cooldown then
				return true
			end

			if slot_27_3 and arg_27_0:spellSlot(slot_27_3).level >= 1 and slot_27_0 >= arg_27_0:spellSlot(slot_27_3).cooldown then
				return true
			end
		end

		if ove_0_33.JarvanIV and arg_27_0.ptr == ove_0_33.JarvanIV_ptr and arg_27_0:spellSlot(0).level >= 1 and arg_27_0:spellSlot(2).level >= 1 and slot_27_0 >= arg_27_0:spellSlot(0).cooldown and slot_27_0 >= arg_27_0:spellSlot(2).cooldown then
			return true
		end

		if ove_0_33.MasterYi and arg_27_0.ptr == ove_0_33.MasterYi_ptr and arg_27_0:spellSlot(0).level >= 1 and slot_27_0 >= arg_27_0:spellSlot(0).cooldown then
			if #ove_0_20.count_minions_in_range(arg_27_0.pos2D, 1000, TEAM_ALLY) >= 1 then
				return true
			end

			if #ove_0_20.count_allies_in_range(arg_27_0.pos2D, 1000) >= 1 then
				return true
			end

			if #ove_0_20.count_minions_in_range(arg_27_0.pos2D, 1000, TEAM_NEUTRAL) >= 1 then
				return true
			end
		end

		if ove_0_33.Rakan and arg_27_0.ptr == ove_0_33.Rakan_ptr then
			if arg_27_0:spellSlot(1).level >= 1 and slot_27_0 >= arg_27_0:spellSlot(1).cooldown then
				return true
			end

			if arg_27_0:spellSlot(2).level >= 1 and slot_27_0 >= arg_27_0:spellSlot(2).cooldown and #ove_0_20.count_enemies_in_range(arg_27_0.pos2D, 1000) >= 2 then
				return true
			end
		end

		if ove_0_33.MonkeyKing and arg_27_0.ptr == ove_0_33.MonkeyKing_ptr and arg_27_0:spellSlot(2).level >= 1 and slot_27_0 >= arg_27_0:spellSlot(2).cooldown then
			if #ove_0_20.count_minions_in_range(arg_27_0.pos2D, 1000, TEAM_ALLY) >= 1 then
				return true
			end

			if #ove_0_20.count_allies_in_range(arg_27_0.pos2D, 1000, true) >= 1 then
				return true
			end

			if #ove_0_20.count_minions_in_range(arg_27_0.pos2D, 1000, TEAM_NEUTRAL) >= 1 then
				return true
			end
		end

		if ove_0_33.Yuumi and arg_27_0.ptr == ove_0_33.Yuumi_ptr and arg_27_0:spellSlot(1).level >= 1 and slot_27_0 >= arg_27_0:spellSlot(1).cooldown and #ove_0_20.count_enemies_in_range(arg_27_0.pos2D, 1000) >= 2 then
			return true
		end

		if ove_0_33.Yasuo and arg_27_0.ptr == ove_0_33.Yasuo_ptr and arg_27_0:spellSlot(2).level >= 1 then
			if #ove_0_20.count_minions_in_range(arg_27_0.pos2D, 1000, TEAM_ALLY) >= 1 then
				return true
			end

			if #ove_0_20.count_allies_in_range(arg_27_0.pos2D, 1000, true) >= 1 then
				return true
			end

			if #ove_0_20.count_minions_in_range(arg_27_0.pos2D, 1000, TEAM_NEUTRAL) >= 1 then
				return true
			end
		end

		if ove_0_33.Yone and arg_27_0.ptr == ove_0_33.Yone_ptr and arg_27_0:spellSlot(0).level >= 1 and slot_27_0 >= arg_27_0:spellSlot(0).cooldown and arg_27_0.isVisible and not arg_27_0.isDead and arg_27_0.buff.yoneq3ready then
			return true
		end
	end

	return false
end

local function ove_0_124(arg_28_0, arg_28_1, arg_28_2, arg_28_3)
	-- function 28
	local slot_28_0 = player.attackRange + player.boundingRadius
	local slot_28_1 = player.attackRange + player.boundingRadius + 500
	local slot_28_2 = slot_28_1 * slot_28_1
	local slot_28_3 = arg_28_2 * arg_28_2
	local slot_28_4
	local slot_28_5 = ove_0_24.present.get_source_pos(arg_28_0)

	if not slot_28_5 then
		return nil
	end

	for iter_28_0 = 0, objManager.enemies_n - 1 do
		local slot_28_6 = objManager.enemies[iter_28_0]

		if slot_28_6 and not slot_28_6.isDead and slot_28_6.isVisible and slot_28_6.isTargetable and slot_28_2 >= slot_28_5:distSqr(slot_28_6.path.serverPos2D) then
			local slot_28_7 = slot_28_0 + (slot_28_6.boundingRadius or 0)

			if slot_28_5:distSqr(slot_28_6.path.serverPos2D) <= slot_28_7 * slot_28_7 then
				local slot_28_8 = ove_0_20.VectorExtend(slot_28_5, slot_28_6.pos2D, arg_28_1)
				local slot_28_9 = mathf.closest_vec_line_seg(arg_28_3.path.serverPos2D, slot_28_5, slot_28_8)

				if slot_28_9 and slot_28_3 >= slot_28_9:distSqr(arg_28_3.path.serverPos2D) then
					slot_28_4 = slot_28_6
				end
			end
		end
	end

	for iter_28_1 = 0, objManager.allies_n - 1 do
		local slot_28_10 = objManager.allies[iter_28_1]

		if slot_28_10 and not slot_28_10.isDead and slot_28_10.isTargetable and slot_28_2 >= slot_28_5:distSqr(slot_28_10.path.serverPos2D) then
			local slot_28_11 = slot_28_0 + (slot_28_10.boundingRadius or 0)

			if slot_28_5:distSqr(slot_28_10.path.serverPos2D) <= slot_28_11 * slot_28_11 then
				local slot_28_12 = ove_0_20.VectorExtend(slot_28_5, slot_28_10.pos2D, arg_28_1)
				local slot_28_13 = mathf.closest_vec_line_seg(arg_28_3.path.serverPos2D, slot_28_5, slot_28_12)

				if slot_28_13 and slot_28_3 >= slot_28_13:distSqr(arg_28_3.path.serverPos2D) then
					slot_28_4 = slot_28_10
				end
			end
		end
	end

	for iter_28_2 = 0, objManager.minions.size[TEAM_ENEMY] - 1 do
		local slot_28_14 = objManager.minions[TEAM_ENEMY][iter_28_2]

		if slot_28_14 and not slot_28_14.isDead and slot_28_14.isVisible and slot_28_14.health and slot_28_14.health >= 1 and slot_28_14.isTargetable and slot_28_2 >= slot_28_5:distSqr(slot_28_14.path.serverPos2D) then
			local slot_28_15 = slot_28_0 + (slot_28_14.boundingRadius or 0)

			if slot_28_5:distSqr(slot_28_14.path.serverPos2D) <= slot_28_15 * slot_28_15 then
				local slot_28_16 = ove_0_20.VectorExtend(slot_28_5, slot_28_14.pos2D, arg_28_1)
				local slot_28_17 = mathf.closest_vec_line_seg(arg_28_3.path.serverPos2D, slot_28_5, slot_28_16)

				if slot_28_17 and slot_28_3 >= slot_28_17:distSqr(arg_28_3.path.serverPos2D) then
					slot_28_4 = slot_28_14
				end
			end
		end
	end

	for iter_28_3 = 0, objManager.minions.size[TEAM_NEUTRAL] - 1 do
		local slot_28_18 = objManager.minions[TEAM_NEUTRAL][iter_28_3]

		if slot_28_18 and not slot_28_18.isDead and slot_28_18.isVisible and slot_28_18.health and slot_28_18.health >= 1 and slot_28_18.isTargetable and slot_28_2 >= slot_28_5:distSqr(slot_28_18.path.serverPos2D) then
			local slot_28_19 = slot_28_0 + (slot_28_18.boundingRadius or 0)

			if slot_28_5:distSqr(slot_28_18.path.serverPos2D) <= slot_28_19 * slot_28_19 then
				local slot_28_20 = ove_0_20.VectorExtend(slot_28_5, slot_28_18.pos2D, arg_28_1)
				local slot_28_21 = mathf.closest_vec_line_seg(arg_28_3.path.serverPos2D, slot_28_5, slot_28_20)

				if slot_28_21 and slot_28_3 >= slot_28_21:distSqr(arg_28_3.path.serverPos2D) then
					slot_28_4 = slot_28_18
				end
			end
		end
	end

	for iter_28_4 = 0, objManager.minions.size[TEAM_ALLY] - 1 do
		local slot_28_22 = objManager.minions[TEAM_ALLY][iter_28_4]

		if slot_28_22 and not slot_28_22.isDead and slot_28_22.health and slot_28_22.health >= 1 and slot_28_22.isTargetable and slot_28_2 >= slot_28_5:distSqr(slot_28_22.path.serverPos2D) then
			local slot_28_23 = slot_28_0 + (slot_28_22.boundingRadius or 0)

			if slot_28_5:distSqr(slot_28_22.path.serverPos2D) <= slot_28_23 * slot_28_23 then
				local slot_28_24 = ove_0_20.VectorExtend(slot_28_5, slot_28_22.pos2D, arg_28_1)
				local slot_28_25 = mathf.closest_vec_line_seg(arg_28_3.path.serverPos2D, slot_28_5, slot_28_24)

				if slot_28_25 and slot_28_3 >= slot_28_25:distSqr(arg_28_3.path.serverPos2D) then
					slot_28_4 = slot_28_22
				end
			end
		end
	end

	for iter_28_5, iter_28_6 in pairs(ove_0_83) do
		if iter_28_6 and iter_28_6.ptr ~= 0 and not iter_28_6.isDead and iter_28_6.isVisible and iter_28_6.isTargetable and slot_28_2 >= slot_28_5:distSqr(iter_28_6.pos2D) then
			local slot_28_26 = slot_28_0 + (iter_28_6.boundingRadius or 0)

			if slot_28_5:distSqr(iter_28_6.pos2D) <= slot_28_26 * slot_28_26 then
				local slot_28_27 = ove_0_20.VectorExtend(slot_28_5, iter_28_6.pos2D, arg_28_1)
				local slot_28_28 = mathf.closest_vec_line_seg(arg_28_3.path.serverPos2D, slot_28_5, slot_28_27)

				if slot_28_28 and slot_28_3 >= slot_28_28:distSqr(arg_28_3.path.serverPos2D) then
					slot_28_4 = iter_28_6
				end
			end
		end
	end

	for iter_28_7, iter_28_8 in pairs(ove_0_81) do
		if iter_28_8 and iter_28_8.ptr ~= 0 and not iter_28_8.isDead and iter_28_8.isVisible and iter_28_8.isTargetable and slot_28_2 >= slot_28_5:distSqr(iter_28_8.pos2D) then
			local slot_28_29 = slot_28_0 + (iter_28_8.boundingRadius or 0)

			if slot_28_5:distSqr(iter_28_8.pos2D) <= slot_28_29 * slot_28_29 then
				local slot_28_30 = ove_0_20.VectorExtend(slot_28_5, iter_28_8.pos2D, arg_28_1)
				local slot_28_31 = mathf.closest_vec_line_seg(arg_28_3.path.serverPos2D, slot_28_5, slot_28_30)

				if slot_28_31 and slot_28_3 >= slot_28_31:distSqr(arg_28_3.path.serverPos2D) then
					slot_28_4 = iter_28_8
				end
			end
		end
	end

	for iter_28_9 = 0, objManager.turrets.size[TEAM_ENEMY] - 1 do
		local slot_28_32 = objManager.turrets[TEAM_ENEMY][iter_28_9]

		if slot_28_32 and not slot_28_32.isDead and slot_28_32.isVisible and slot_28_32.health and slot_28_32.health >= 1 and slot_28_32.isTargetable and slot_28_2 >= slot_28_5:distSqr(slot_28_32.pos2D) then
			local slot_28_33 = slot_28_0 + (slot_28_32.boundingRadius or 0)

			if slot_28_5:distSqr(slot_28_32.pos2D) <= slot_28_33 * slot_28_33 then
				local slot_28_34 = ove_0_20.VectorExtend(slot_28_5, slot_28_32.pos2D, arg_28_1)
				local slot_28_35 = mathf.closest_vec_line_seg(arg_28_3.path.serverPos2D, slot_28_5, slot_28_34)

				if slot_28_35 and slot_28_3 >= slot_28_35:distSqr(arg_28_3.path.serverPos2D) then
					slot_28_4 = slot_28_32
				end
			end
		end
	end

	for iter_28_10 = 0, objManager.turrets.size[TEAM_ALLY] - 1 do
		local slot_28_36 = objManager.turrets[TEAM_ALLY][iter_28_10]

		if slot_28_36 and not slot_28_36.isDead and slot_28_36.isVisible and slot_28_36.health and slot_28_36.health >= 1 and slot_28_2 >= slot_28_5:distSqr(slot_28_36.pos2D) then
			local slot_28_37 = slot_28_0 + (slot_28_36.boundingRadius or 0)

			if slot_28_5:distSqr(slot_28_36.pos2D) <= slot_28_37 * slot_28_37 then
				if slot_28_36.isTargetable then
					local slot_28_38 = ove_0_20.VectorExtend(slot_28_5, slot_28_36.pos2D, arg_28_1)
					local slot_28_39 = mathf.closest_vec_line_seg(arg_28_3.path.serverPos2D, slot_28_5, slot_28_38)

					if slot_28_39 and slot_28_3 >= slot_28_39:distSqr(arg_28_3.path.serverPos2D) then
						slot_28_4 = slot_28_36
					end
				elseif not slot_28_36.name:find("TurretShrine") then
					local slot_28_40 = ove_0_20.VectorExtend(slot_28_5, slot_28_36.pos2D, arg_28_1)
					local slot_28_41 = mathf.closest_vec_line_seg(arg_28_3.path.serverPos2D, slot_28_5, slot_28_40)

					if slot_28_41 and slot_28_3 >= slot_28_41:distSqr(arg_28_3.path.serverPos2D) then
						slot_28_4 = slot_28_36
					end
				end
			end
		end
	end

	return slot_28_4
end

local function ove_0_125()
	-- function 29
	local slot_29_0

	if ove_0_21.wpriority.enable_autoHeal:get() then
		local slot_29_1 = ove_0_40.range * ove_0_40.range

		if ove_0_20.GetPercentMana(player) >= ove_0_21.wpriority.heal_mana:get() then
			for iter_29_0 = 0, objManager.allies_n - 1 do
				local slot_29_2 = objManager.allies[iter_29_0]

				if not ove_0_20.IsRecalling(player) and slot_29_2.team == TEAM_ALLY and not slot_29_2.isDead and slot_29_2 ~= player and slot_29_2.isTargetable and ove_0_21.wpriority[ove_0_36[slot_29_2.ptr]] and ove_0_21.wpriority[ove_0_36[slot_29_2.ptr]]:get() > 0 and slot_29_1 >= slot_29_2.pos2D:distSqr(player.pos2D) and not ove_0_20.IsRecalling(slot_29_2) and ove_0_21.wpriority[ove_0_36[slot_29_2.ptr] .. "hp"]:get() >= slot_29_2.health / slot_29_2.maxHealth * 100 then
					if slot_29_0 == nil then
						slot_29_0 = slot_29_2
					elseif ove_0_21.wpriority[ove_0_36[slot_29_2.ptr]]:get() < ove_0_21.wpriority[ove_0_36[slot_29_0.ptr]]:get() then
						slot_29_0 = slot_29_2
					elseif ove_0_21.wpriority[ove_0_36[slot_29_2.ptr]]:get() == ove_0_21.wpriority[ove_0_36[slot_29_0.ptr]]:get() then
						if slot_29_2.health / slot_29_2.maxHealth * 100 < slot_29_0.health / slot_29_0.maxHealth * 100 then
							slot_29_0 = slot_29_2
						end

						if slot_29_2.health / slot_29_2.maxHealth * 100 == slot_29_0.health / slot_29_0.maxHealth * 100 and slot_29_2.maxHealth <= slot_29_0.maxHealth then
							slot_29_0 = slot_29_2
						end
					end
				end
			end
		end
	end

	return slot_29_0
end

local function ove_0_126()
	-- function 30
	if ove_0_125() and ove_0_21.wpriority.healTog:get() and player:spellSlot(0).state == 0 then
		local slot_30_0 = ove_0_125()

		if ove_0_21.wpriority.heal_mode:get() == 2 and #ove_0_20.count_enemies_in_range(player.pos2D, 1500) < 1 or ove_0_21.wpriority.heal_mode:get() == 3 and #ove_0_20.count_enemies_in_range(player.pos2D, 1500) >= 1 then
			return
		end

		if (player.attackRange + player.boundingRadius + slot_30_0.boundingRadius) * (player.attackRange + player.boundingRadius + slot_30_0.boundingRadius) < slot_30_0.pos2D:distSqr(player.pos2D) then
			if ove_0_23.core.can_action() then
				local slot_30_1 = ove_0_124(player, ove_0_40.range, ove_0_40.width + 40, slot_30_0)

				if slot_30_1 then
					player:castSpell("obj", 0, slot_30_1)
				end
			end
		elseif ove_0_23.core.can_action() then
			player:castSpell("obj", 0, slot_30_0)
		end
	end
end

local function ove_0_127()
	-- function 31
	local slot_31_0
	local slot_31_1 = ove_0_40.range * ove_0_40.range

	for iter_31_0 = 0, objManager.allies_n - 1 do
		local slot_31_2 = objManager.allies[iter_31_0]

		if slot_31_2.team == TEAM_ALLY and not slot_31_2.isDead and slot_31_2 ~= player and slot_31_2.isTargetable and ove_0_21.wpriority[ove_0_36[slot_31_2.ptr]] and ove_0_21.wpriority[ove_0_36[slot_31_2.ptr]]:get() > 0 and slot_31_1 >= slot_31_2.pos2D:distSqr(player.pos2D) and not ove_0_20.IsRecalling(slot_31_2) then
			if slot_31_0 == nil then
				slot_31_0 = slot_31_2
			elseif ove_0_21.wpriority[ove_0_36[slot_31_2.ptr]]:get() < ove_0_21.wpriority[ove_0_36[slot_31_0.ptr]]:get() then
				slot_31_0 = slot_31_2

				if slot_31_2.health / slot_31_2.maxHealth * 100 < slot_31_0.health / slot_31_0.maxHealth * 100 then
					slot_31_0 = slot_31_2
				end

				if slot_31_2.health / slot_31_2.maxHealth * 100 == slot_31_0.health / slot_31_0.maxHealth * 100 and slot_31_2.maxHealth <= slot_31_0.maxHealth then
					slot_31_0 = slot_31_2
				end
			elseif ove_0_21.wpriority[ove_0_36[slot_31_2.ptr]]:get() == ove_0_21.wpriority[ove_0_36[slot_31_0.ptr]]:get() then
				if slot_31_2.health / slot_31_2.maxHealth * 100 < slot_31_0.health / slot_31_0.maxHealth * 100 then
					slot_31_0 = slot_31_2
				end

				if slot_31_2.health / slot_31_2.maxHealth * 100 == slot_31_0.health / slot_31_0.maxHealth * 100 and slot_31_2.maxHealth <= slot_31_0.maxHealth then
					slot_31_0 = slot_31_2
				end
			end
		end
	end

	return slot_31_0
end

local function ove_0_128()
	-- function 32
	if ove_0_21.wpriority.healTogForce:get() and ove_0_127() and player:spellSlot(0).state == 0 then
		local slot_32_0 = ove_0_127()

		if (player.attackRange + player.boundingRadius + slot_32_0.boundingRadius) * (player.attackRange + player.boundingRadius + slot_32_0.boundingRadius) < slot_32_0.pos2D:distSqr(player.pos2D) then
			local slot_32_1 = ove_0_124(player, ove_0_40.range, ove_0_40.width + 40, slot_32_0)

			if slot_32_1 then
				player:castSpell("obj", 0, slot_32_1)
			end
		else
			player:castSpell("obj", 0, slot_32_0)
		end
	end
end

local ove_0_129 = {
	ove_0_28.BUFF_ENUM_STUN,
	ove_0_28.BUFF_ENUM_TAUNT,
	ove_0_28.BUFF_ENUM_SNARE,
	ove_0_28.BUFF_ENUM_FEAR,
	ove_0_28.BUFF_ENUM_CHARM,
	ove_0_28.BUFF_ENUM_SUPPRESSION,
	ove_0_28.BUFF_ENUM_KNOCKUP,
	ove_0_28.BUFF_ENUM_ASLEEP
}

local function ove_0_130(arg_33_0, arg_33_1, arg_33_2)
	-- function 33
	local slot_33_0 = ove_0_43.range * ove_0_43.range
	local slot_33_1 = 250000
	local slot_33_2 = 640000

	if slot_33_0 < arg_33_0.startPos:distSqr(arg_33_0.endPos) then
		return false
	end

	if slot_33_1 >= player.pos2D:distSqr(arg_33_1.pos2D) then
		return true
	end

	if ove_0_20.IsHardLockedLinearAPI(ove_0_43, arg_33_0, arg_33_1) then
		return true
	end

	if slot_33_2 < arg_33_0.startPos:distSqr(arg_33_0.endPos) then
		if ove_0_24.trace.newpath(arg_33_1, 0, 0.35) then
			return true
		end
	elseif ove_0_24.trace.newpath(arg_33_1, 0.033, 1) then
		return true
	end

	if ove_0_20.IsCustomLocked(ove_0_129, arg_33_1) then
		return true
	end
end

local function ove_0_131(arg_34_0, arg_34_1, arg_34_2)
	-- function 34
	if arg_34_2 > ove_0_43.range then
		return false
	end

	local slot_34_0 = ove_0_24.linear.get_prediction(ove_0_43, arg_34_1)

	if not slot_34_0 then
		return false
	end

	if not ove_0_130(slot_34_0, arg_34_1, arg_34_2) then
		return false
	end

	if ove_0_24.collision.get_prediction(ove_0_43, slot_34_0, arg_34_1) then
		return false
	end

	arg_34_0.obj = arg_34_1.pos
	arg_34_0.pos = slot_34_0.endPos

	return true
end

local function ove_0_132()
	-- function 35
	if not ove_0_20.IsRecalling(player) and ove_0_21.misc.GapW:get() and player:spellSlot(1).state == 0 then
		local slot_35_0 = ove_0_43.range * ove_0_43.range

		for iter_35_0 = 0, objManager.enemies_n - 1 do
			local slot_35_1 = objManager.enemies[iter_35_0]

			if slot_35_1.type == TYPE_HERO and slot_35_1.team == TEAM_ENEMY and slot_35_1 and ove_0_20.IsValidTarget(slot_35_1) and slot_35_1.path.isActive and slot_35_1.path.isDashing and slot_35_0 > player.pos2D:distSqr(slot_35_1.path.point2D[1]) and ove_0_21.misc.blacklist[ove_0_32[slot_35_1.ptr]] and not ove_0_21.misc.blacklist[ove_0_32[slot_35_1.ptr]]:get() and player.pos2D:distSqr(slot_35_1.path.point2D[1]) < player.pos2D:distSqr(slot_35_1.path.point2D[0]) then
				local slot_35_2 = ove_0_24.linear.get_prediction(ove_0_43, slot_35_1)

				if slot_35_2 and slot_35_0 > slot_35_2.startPos:distSqr(slot_35_2.endPos) and not ove_0_24.collision.get_prediction(ove_0_43, slot_35_2, slot_35_1) then
					player:castSpell("pos", 1, vec3(slot_35_2.endPos.x, slot_35_1.pos.y, slot_35_2.endPos.y))
				end
			end
		end
	end
end

local function ove_0_133(arg_36_0, arg_36_1)
	-- function 36
	if ove_0_20.IsHardLockedLinearAPI(ove_0_45, arg_36_0, arg_36_1) then
		return true
	end

	if ove_0_24.trace.newpath(arg_36_1, 0.033, 1) then
		return true
	end
end

local function ove_0_134()
	-- function 37
	if not ove_0_21.rset.semiR_slow:get() then
		if ove_0_21.rset.Force_R_key:get() then
			local slot_37_0 = ove_0_109()

			if slot_37_0 and ove_0_20.IsValidTarget(slot_37_0) and player:spellSlot(3).state == 0 then
				local slot_37_1 = ove_0_24.linear.get_prediction(ove_0_45, slot_37_0)

				if slot_37_1 then
					player:castSpell("pos", 3, vec3(slot_37_1.endPos.x, slot_37_0.pos.y, slot_37_1.endPos.y))
				end
			end
		end
	elseif ove_0_21.rset.Force_R_key:get() or ove_0_55 then
		local slot_37_2 = ove_0_109()

		if slot_37_2 and ove_0_20.IsValidTarget(slot_37_2) and player:spellSlot(3).state == 0 then
			local slot_37_3 = ove_0_24.linear.get_prediction(ove_0_45, slot_37_2)

			if slot_37_3 and ove_0_133(slot_37_3, slot_37_2) then
				player:castSpell("pos", 3, vec3(slot_37_3.endPos.x, slot_37_2.pos.y, slot_37_3.endPos.y))
			end
		end
	end
end

local function ove_0_135(arg_38_0)
	-- function 38
	return arg_38_0 and arg_38_0.ptr and arg_38_0.ptr ~= 0 and arg_38_0.type and arg_38_0.type == TYPE_HERO and arg_38_0.team and arg_38_0.team == TEAM_ENEMY and not arg_38_0.isDead and arg_38_0.isVisible and arg_38_0.isTargetable and arg_38_0.buff and not arg_38_0.buff[ove_0_28.BUFF_ENUM_INVULNERABILITY]
end

local function ove_0_136(arg_39_0)
	-- function 39
	return arg_39_0 and arg_39_0.ptr and arg_39_0.ptr ~= 0 and arg_39_0.type and arg_39_0.type == TYPE_TURRET and not arg_39_0.isDead and arg_39_0.isVisible and arg_39_0.isTargetable
end

local function ove_0_137(arg_40_0)
	-- function 40
	return arg_40_0 and arg_40_0.ptr and arg_40_0.ptr ~= 0 and arg_40_0.type and arg_40_0.type == TYPE_INHIB and not arg_40_0.isDead and arg_40_0.isVisible and arg_40_0.isTargetable
end

local function ove_0_138(arg_41_0)
	-- function 41
	return arg_41_0 and arg_41_0.ptr and arg_41_0.ptr ~= 0 and arg_41_0.type and arg_41_0.type == TYPE_NEXUS and not arg_41_0.isDead and arg_41_0.isVisible and arg_41_0.isTargetable
end

ove_0_23.combat.register_f_after_attack(function()
	-- function 42
	if player:spellSlot(0).state ~= 0 then
		return
	end

	if ove_0_21.keys.combokey:get() and ove_0_21.combo.qcombo_after_AA:get() and ove_0_135(ove_0_99) then
		local slot_42_0 = player.attackRange + player.boundingRadius + (ove_0_99.boundingRadius or 0)
		local slot_42_1 = slot_42_0 * slot_42_0

		if ove_0_99 and ove_0_99.ptr ~= 0 and slot_42_1 >= player.pos2D:distSqr(ove_0_99.pos2D) then
			player:castSpell("obj", 0, ove_0_99)
		end
	end

	if ove_0_21.keys.harasskey:get() and ove_0_21.harass.qharass_after_AA:get() and ove_0_135(ove_0_99) then
		local slot_42_2 = player.attackRange + player.boundingRadius + (ove_0_99.boundingRadius or 0)
		local slot_42_3 = slot_42_2 * slot_42_2

		if ove_0_99 and ove_0_99.ptr ~= 0 and slot_42_3 >= player.pos2D:distSqr(ove_0_99.pos2D) then
			player:castSpell("obj", 0, ove_0_99)
		end
	end

	if ove_0_21.keys.clearkey:get() then
		if ove_0_21.misc.q_turret.q_turret_enable:get() and ove_0_136(ove_0_99) then
			local slot_42_4 = player.attackRange + player.boundingRadius + (ove_0_99.boundingRadius or 0)

			if slot_42_4 * slot_42_4 >= player.pos2D:distSqr(ove_0_99.pos2D) then
				if ove_0_21.misc.q_turret.q_turret_hold:get() and #ove_0_20.count_enemies_in_range(player.pos2D, 1150) >= 1 then
					return
				end

				if ove_0_21.misc.q_turret.q_turret_hold_hit:get() then
					local slot_42_5 = 1690000

					for iter_42_0 = 0, objManager.enemies_n - 1 do
						local slot_42_6 = objManager.enemies[iter_42_0]

						if slot_42_6 and not slot_42_6.isDead and slot_42_6.isVisible and slot_42_6.isTargetable and slot_42_5 >= slot_42_6.pos2D:distSqr(player.pos2D) and ove_0_99 and ove_0_99.ptr ~= 0 and ove_0_20.is_valid(ove_0_99) then
							local slot_42_7 = ove_0_20.VectorExtend(player.path.serverPos2D, ove_0_99.pos2D, 1300)
							local slot_42_8 = mathf.closest_vec_line_seg(slot_42_6.path.serverPos2D, player.path.serverPos2D, slot_42_7)
							local slot_42_9 = ove_0_40.width + (slot_42_6.boundingRadius or 0) + 40
							local slot_42_10 = slot_42_9 * slot_42_9

							if slot_42_8 and slot_42_10 >= slot_42_8:distSqr(slot_42_6.pos2D) then
								return
							end
						end
					end
				end

				if player.mana / player.maxMana * 100 >= ove_0_21.misc.q_turret.q_turret_mana:get() and ove_0_99 and ove_0_99.ptr ~= 0 and ove_0_20.is_valid(ove_0_99) then
					player:castSpell("obj", 0, ove_0_99)
				end
			end
		end

		if ove_0_21.misc.q_inhib.q_inhib_enable:get() and ove_0_137(ove_0_99) then
			local slot_42_11 = player.attackRange + player.boundingRadius + (ove_0_99.boundingRadius or 0)

			if slot_42_11 * slot_42_11 >= player.pos2D:distSqr(ove_0_99.pos2D) then
				if ove_0_21.misc.q_inhib.q_inhib_hold:get() and #ove_0_20.count_enemies_in_range(player.pos2D, 1150) >= 1 then
					return
				end

				if ove_0_21.misc.q_inhib.q_inhib_hold_hit:get() then
					local slot_42_12 = 1690000

					for iter_42_1 = 0, objManager.enemies_n - 1 do
						local slot_42_13 = objManager.enemies[iter_42_1]

						if slot_42_13 and not slot_42_13.isDead and slot_42_13.isVisible and slot_42_13.isTargetable and slot_42_12 >= slot_42_13.pos2D:distSqr(player.pos2D) and ove_0_99 and ove_0_99.ptr ~= 0 and ove_0_20.is_valid(ove_0_99) then
							local slot_42_14 = ove_0_20.VectorExtend(player.path.serverPos2D, ove_0_99.pos2D, 1300)
							local slot_42_15 = mathf.closest_vec_line_seg(slot_42_13.path.serverPos2D, player.path.serverPos2D, slot_42_14)
							local slot_42_16 = ove_0_40.width + (slot_42_13.boundingRadius or 0) + 40
							local slot_42_17 = slot_42_16 * slot_42_16

							if slot_42_15 and slot_42_17 >= slot_42_15:distSqr(slot_42_13.pos2D) then
								return
							end
						end
					end
				end

				if player.mana / player.maxMana * 100 >= ove_0_21.misc.q_inhib.q_inhib_mana:get() and ove_0_99 and ove_0_99.ptr ~= 0 and ove_0_20.is_valid(ove_0_99) then
					player:castSpell("obj", 0, ove_0_99)
				end
			end
		end

		if ove_0_21.misc.q_nexus.q_nexus_enable:get() and ove_0_138(ove_0_99) then
			local slot_42_18 = player.attackRange + player.boundingRadius + (ove_0_99.boundingRadius or 0)

			if slot_42_18 * slot_42_18 >= player.pos2D:distSqr(ove_0_99.pos2D) then
				if ove_0_21.misc.q_nexus.q_nexus_hold:get() and #ove_0_20.count_enemies_in_range(player.pos2D, 1150) >= 1 then
					return
				end

				if ove_0_21.misc.q_nexus.q_nexus_hold_hit:get() then
					local slot_42_19 = 1690000

					for iter_42_2 = 0, objManager.enemies_n - 1 do
						local slot_42_20 = objManager.enemies[iter_42_2]

						if slot_42_20 and not slot_42_20.isDead and slot_42_20.isVisible and slot_42_20.isTargetable and slot_42_19 >= slot_42_20.pos2D:distSqr(player.pos2D) and ove_0_99 and ove_0_99.ptr ~= 0 and ove_0_20.is_valid(ove_0_99) then
							local slot_42_21 = ove_0_20.VectorExtend(player.path.serverPos2D, ove_0_99.pos2D, 1300)
							local slot_42_22 = mathf.closest_vec_line_seg(slot_42_20.path.serverPos2D, player.path.serverPos2D, slot_42_21)
							local slot_42_23 = ove_0_40.width + (slot_42_20.boundingRadius or 0) + 40
							local slot_42_24 = slot_42_23 * slot_42_23

							if slot_42_22 and slot_42_24 >= slot_42_22:distSqr(slot_42_20.pos2D) then
								return
							end
						end
					end
				end

				if player.mana / player.maxMana * 100 >= ove_0_21.misc.q_nexus.q_nexus_mana:get() and ove_0_99 and ove_0_99.ptr ~= 0 and ove_0_20.is_valid(ove_0_99) then
					player:castSpell("obj", 0, ove_0_99)
				end
			end
		end
	end

	ove_0_23.combat.set_invoke_after_attack(false)
end)

local function ove_0_139(arg_43_0, arg_43_1)
	-- function 43
	if ove_0_44.range * ove_0_44.range < arg_43_0.startPos:distSqr(arg_43_0.endPos) then
		return false
	end

	if ove_0_20.IsHardLockedLinearAPI(ove_0_44, arg_43_0, arg_43_1) then
		return true
	end
end

local function ove_0_140(arg_44_0, arg_44_1, arg_44_2)
	-- function 44
	if arg_44_2 > ove_0_44.range then
		return false
	end

	local slot_44_0 = ove_0_24.linear.get_prediction(ove_0_44, arg_44_1)

	if not slot_44_0 then
		return false
	end

	if ove_0_24.collision.get_prediction(ove_0_44, slot_44_0, arg_44_1) then
		return false
	end

	if not ove_0_139(slot_44_0, arg_44_1) then
		return false
	end

	arg_44_0.obj = arg_44_1
	arg_44_0.pos = slot_44_0.endPos

	return true
end

local function ove_0_141()
	-- function 45
	if ove_0_21.misc.CC_W:get() and player:spellSlot(1).state == 0 then
		if ove_0_21.misc.CC_W_combo:get() and not ove_0_21.keys.combokey:get() then
			return
		end

		local slot_45_0 = ove_0_25.get_result(ove_0_140)

		if slot_45_0.pos and slot_45_0.obj and ove_0_23.core.can_action() then
			player:castSpell("pos", 1, vec3(slot_45_0.pos.x, slot_45_0.obj.pos.y, slot_45_0.pos.y))
		end
	end
end

local function ove_0_142(arg_46_0)
	-- function 46
	local slot_46_0 = {}
	local slot_46_1 = ove_0_41.range * ove_0_41.range

	for iter_46_0 = 0, objManager.allies_n - 1 do
		local slot_46_2 = objManager.allies[iter_46_0]

		if slot_46_2 and slot_46_2.ptr ~= player.ptr and not slot_46_2.isDead and slot_46_2.isVisible and slot_46_2.isTargetable and slot_46_1 >= slot_46_2.pos2D:distSqr(player.pos2D) and ove_0_21.combo.ifs.qcombo_blist[ove_0_36[slot_46_2.ptr] .. "block"] and not ove_0_21.combo.ifs.qcombo_blist[ove_0_36[slot_46_2.ptr] .. "block"]:get() and ove_0_21.combo.ifs.qcombo_blist[ove_0_36[slot_46_2.ptr] .. "hp"] and ove_0_20.GetPercentHealth(slot_46_2) <= ove_0_21.combo.ifs.qcombo_blist[ove_0_36[slot_46_2.ptr] .. "hp"]:get() and arg_46_0 and arg_46_0.ptr and arg_46_0.ptr ~= 0 and not arg_46_0.isDead and arg_46_0.isVisible and arg_46_0.isTargetable and ove_0_20.IsOnLineSegment(slot_46_2.path.serverPos2D, player.path.serverPos2D, arg_46_0.pos2D, ove_0_41.width, ove_0_41.range) then
			slot_46_0[#slot_46_0 + 1] = slot_46_2
		end
	end

	return slot_46_0
end

local function ove_0_143(arg_47_0)
	-- function 47
	local slot_47_0 = {}
	local slot_47_1 = 1822500

	for iter_47_0, iter_47_1 in pairs(ove_0_81) do
		if iter_47_1 and iter_47_1.ptr and iter_47_1.ptr ~= 0 and not iter_47_1.isDead and iter_47_1.isVisible and iter_47_1.isTargetable and slot_47_1 >= iter_47_1.pos2D:distSqr(player.pos2D) and arg_47_0 and arg_47_0.ptr and arg_47_0.ptr ~= 0 and not arg_47_0.isDead and arg_47_0.isVisible and arg_47_0.isTargetable and ove_0_20.IsOnLineSegment(iter_47_1.pos2D, player.path.serverPos2D, arg_47_0.pos2D, 80, ove_0_40.range) then
			slot_47_0[#slot_47_0 + 1] = iter_47_1
		end
	end

	return slot_47_0
end

local function ove_0_144(arg_48_0)
	-- function 48
	local slot_48_0 = {}
	local slot_48_1 = 2250000

	for iter_48_0 = 0, objManager.turrets.size[TEAM_ENEMY] - 1 do
		local slot_48_2 = objManager.turrets[TEAM_ENEMY][iter_48_0]

		if slot_48_2 and not slot_48_2.isDead and slot_48_2.isTargetable and slot_48_1 >= slot_48_2.pos2D:distSqr(player.pos2D) then
			local slot_48_3 = slot_48_2.boundingRadius or 0

			if arg_48_0 and arg_48_0.ptr and arg_48_0.ptr ~= 0 and not arg_48_0.isDead and arg_48_0.isVisible and arg_48_0.isTargetable and ove_0_20.IsOnLineSegment(slot_48_2.pos2D, player.path.serverPos2D, arg_48_0.pos2D, 60 + slot_48_3, ove_0_40.range + slot_48_3 * 0.5) then
				slot_48_0[#slot_48_0 + 1] = slot_48_2
			end
		end
	end

	for iter_48_1 = 0, objManager.inhibs.size[TEAM_ENEMY] - 1 do
		local slot_48_4 = objManager.inhibs[TEAM_ENEMY][iter_48_1]

		if slot_48_4 and not slot_48_4.isDead and slot_48_4.isTargetable and slot_48_1 >= slot_48_4.pos2D:distSqr(player.pos2D) then
			local slot_48_5 = slot_48_4.boundingRadius or 0

			if arg_48_0 and arg_48_0.ptr and arg_48_0.ptr ~= 0 and not arg_48_0.isDead and arg_48_0.isVisible and arg_48_0.isTargetable and ove_0_20.IsOnLineSegment(slot_48_4.pos2D, player.path.serverPos2D, arg_48_0.pos2D, 60 + slot_48_5, ove_0_40.range + slot_48_5 * 0.5) then
				slot_48_0[#slot_48_0 + 1] = slot_48_4
			end
		end
	end

	local slot_48_6 = objManager.nexus[TEAM_ENEMY]

	if slot_48_6 and not slot_48_6.isDead and slot_48_6.isTargetable and slot_48_1 >= slot_48_6.pos2D:distSqr(player.pos2D) then
		local slot_48_7 = slot_48_6.boundingRadius or 0

		if arg_48_0 and arg_48_0.ptr and arg_48_0.ptr ~= 0 and not arg_48_0.isDead and arg_48_0.isVisible and arg_48_0.isTargetable and ove_0_20.IsOnLineSegment(slot_48_6.pos2D, player.path.serverPos2D, arg_48_0.pos2D, 60 + slot_48_7, ove_0_40.range + slot_48_7 * 0.5) then
			slot_48_0[#slot_48_0 + 1] = slot_48_6
		end
	end

	return slot_48_0
end

local function ove_0_145(arg_49_0)
	-- function 49
	local slot_49_0 = {}
	local slot_49_1 = 1822500

	for iter_49_0 = 0, objManager.enemies_n - 1 do
		local slot_49_2 = objManager.enemies[iter_49_0]

		if ove_0_20.IsValidTarget(slot_49_2) and slot_49_1 >= slot_49_2.pos2D:distSqr(player.pos2D) and arg_49_0 and arg_49_0.ptr and arg_49_0.ptr ~= 0 and not arg_49_0.isDead and arg_49_0.isVisible and arg_49_0.isTargetable and ove_0_20.IsOnLineSegment(slot_49_2.path.serverPos2D, player.path.serverPos2D, arg_49_0.pos2D, 60, ove_0_40.range) then
			slot_49_0[#slot_49_0 + 1] = slot_49_2
		end
	end

	return slot_49_0
end

local ove_0_146 = 0

local function ove_0_147(arg_50_0, arg_50_1)
	-- function 50
	if arg_50_0 and arg_50_0.ptr and arg_50_0.ptr ~= 0 and not arg_50_0.isDead and arg_50_0.isVisible and arg_50_0.isTargetable then
		if arg_50_1 then
			ove_0_20.DelayAction(function()
				-- function 51
				ove_0_146 = game.time + 0.25
			end, 0.25)

			if ove_0_146 > game.time then
				player:castSpell("obj", 0, arg_50_0)
			end
		else
			player:castSpell("obj", 0, arg_50_0)
		end
	end
end

local function ove_0_148(arg_52_0, arg_52_1)
	-- function 52
	local slot_52_0 = ove_0_21.combo.q_slowpred:get()
	local slot_52_1 = ove_0_21.combo.ignore.heal:get()
	local slot_52_2 = ove_0_21.combo.ignore.mist:get()
	local slot_52_3 = ove_0_21.combo.ignore.soul:get()
	local slot_52_4 = ove_0_21.combo.ignore.structure:get()
	local slot_52_5 = ove_0_21.combo.ignore.multi:get()
	local slot_52_6 = ove_0_21.combo.ignore.multi_count:get()
	local slot_52_7 = ove_0_21.combo.ignore.rune.dark_harvest:get()
	local slot_52_8 = ove_0_21.combo.ignore.rune.grasp:get()
	local slot_52_9 = ove_0_21.combo.ignore.rune.first_strike:get()
	local slot_52_10 = ove_0_21.combo.ignore.rune.attack:get()
	local slot_52_11 = ove_0_21.combo.ignore.rune.aery:get()
	local slot_52_12 = ove_0_21.combo.ignore.rune.comet:get()
	local slot_52_13 = ove_0_21.combo.ignore.rune.manaflow:get()
	local slot_52_14 = ove_0_21.combo.ignore.rune.taste:get()

	if slot_52_1 and #ove_0_142(arg_52_1) >= 1 then
		ove_0_147(arg_52_1, false)
	end

	if slot_52_2 and arg_52_0.buff.sennapassivemarker and arg_52_0.buff.sennapassivemarker.endTime - game.time > ove_0_100 then
		ove_0_147(arg_52_1, slot_52_0)
	end

	if slot_52_3 and #ove_0_143(arg_52_1) >= 1 then
		ove_0_147(arg_52_1, false)
	end

	if slot_52_4 and #ove_0_144(arg_52_1) >= 1 then
		ove_0_147(arg_52_1, false)
	end

	if slot_52_5 and slot_52_6 <= #ove_0_145(arg_52_1) then
		ove_0_147(arg_52_1, false)
	end

	if slot_52_7 and player.buff[ove_0_28.Rune_DarkHarvestBuff] and not player.buff[ove_0_28.Rune_DarkHarvestCooldownBuff] and (arg_52_0.health + arg_52_0.healthRegenRate * 0.393) / arg_52_0.maxHealth * 100 < 50 then
		ove_0_147(arg_52_1, slot_52_0)
	end

	if slot_52_8 and player.buff[ove_0_28.Rune_GraspOfTheUndyingBuff] and player.buff[ove_0_28.Rune_GraspOfTheUndyingBuff].endTime - game.time > ove_0_100 then
		ove_0_147(arg_52_1, slot_52_0)
	end

	if slot_52_9 then
		if player.buff[ove_0_28.Rune_FirstStrikeBuff] then
			ove_0_147(arg_52_1, slot_52_0)
		end

		local slot_52_15 = player.buff[ove_0_28.Rune_FirstStrikeProccedBuff]

		if slot_52_15 and slot_52_15.endTime - game.time > ove_0_100 then
			ove_0_147(arg_52_1, slot_52_0)
		end
	end

	if slot_52_10 and arg_52_0.buff[ove_0_28.Rune_PressTheAttackBuff] and arg_52_0.buff[ove_0_28.Rune_PressTheAttackBuff].stacks2 == 2 and arg_52_0.buff[ove_0_28.Rune_PressTheAttackBuff].endTime - game.time > ove_0_100 then
		ove_0_147(arg_52_1, false)
	end

	if slot_52_11 and player.buff[ove_0_28.Rune_SummonAeryBuff] then
		ove_0_147(arg_52_1, slot_52_0)
	end

	if slot_52_12 and player.buff[ove_0_28.Rune_ArcaneCometBuff] then
		ove_0_147(arg_52_1, slot_52_0)
	end

	if slot_52_13 and player.buff[ove_0_28.Rune_ManaflowBandBuff] then
		ove_0_147(arg_52_1, slot_52_0)
	end

	if slot_52_14 and player.buff[ove_0_28.Rune_TasteOfBloodBuff] and player.health < player.maxHealth then
		ove_0_147(arg_52_1, slot_52_0)
	end
end

local function ove_0_149()
	-- function 53
	if not ove_0_21.combo.qcombo:get() then
		return
	end

	if player:spellSlot(_Q).state ~= 0 then
		return
	end

	if not ove_0_23.core.can_action() then
		return
	end

	if ove_0_21.combo.q_combo_mana:get() >= ove_0_20.GetPercentMana(player) then
		return
	end

	local slot_53_0 = ove_0_105()

	if ove_0_20.IsValidTarget(slot_53_0) then
		local slot_53_1 = ove_0_21.combo.qcombo_after_AA:get()
		local slot_53_2 = ove_0_21.combo.qcombo_minions:get()
		local slot_53_3 = ove_0_21.combo.qcombo_min_count:get()
		local slot_53_4 = ove_0_21.combo.qcombo_ext:get()
		local slot_53_5 = ove_0_21.combo.q_slowpred:get()
		local slot_53_6 = player.attackRange + player.boundingRadius + slot_53_0.boundingRadius
		local slot_53_7 = slot_53_6 * slot_53_6

		if slot_53_1 and slot_53_0.pos2D:distSqr(player.pos2D) <= slot_53_7 + 2500 then
			return
		end

		if slot_53_7 < slot_53_0.pos2D:distSqr(player.pos2D) then
			if not slot_53_4 then
				return
			end

			local slot_53_8 = ove_0_124(player, ove_0_40.range, ove_0_40.width, slot_53_0)
			local slot_53_9 = ove_0_24.linear.get_prediction(ove_0_42, slot_53_0)
			local slot_53_10 = ove_0_40.range * ove_0_40.range

			if slot_53_8 and slot_53_9 and slot_53_10 >= slot_53_9.endPos:distSqr(slot_53_9.startPos) then
				if slot_53_2 then
					slot_53_9.endPos = ove_0_20.VectorExtend(player.pos2D, slot_53_9.endPos, 1300)

					local slot_53_11 = ove_0_24.collision.get_prediction(ove_0_42, slot_53_9)

					if slot_53_11 and slot_53_3 <= #slot_53_11 then
						ove_0_148(slot_53_0, slot_53_8)

						return
					end
				end

				local slot_53_12 = ove_0_21.combo.ifs.heal:get()
				local slot_53_13 = ove_0_21.combo.ifs.mist:get()
				local slot_53_14 = ove_0_21.combo.ifs.soul:get()
				local slot_53_15 = ove_0_21.combo.ifs.structure:get()
				local slot_53_16 = ove_0_21.combo.ifs.multi:get()
				local slot_53_17 = ove_0_21.combo.ifs.multi_count:get()
				local slot_53_18 = ove_0_21.combo.ifs.rune.dark_harvest:get()
				local slot_53_19 = ove_0_21.combo.ifs.rune.grasp:get()
				local slot_53_20 = ove_0_21.combo.ifs.rune.first_strike:get()
				local slot_53_21 = ove_0_21.combo.ifs.rune.attack:get()
				local slot_53_22 = ove_0_21.combo.ifs.rune.aery:get()
				local slot_53_23 = ove_0_21.combo.ifs.rune.comet:get()
				local slot_53_24 = ove_0_21.combo.ifs.rune.manaflow:get()
				local slot_53_25 = ove_0_21.combo.ifs.rune.taste:get()

				if slot_53_12 or slot_53_13 or slot_53_14 or slot_53_15 or slot_53_16 or slot_53_18 or slot_53_19 or slot_53_20 or slot_53_21 or slot_53_22 or slot_53_23 or slot_53_24 or slot_53_25 then
					if slot_53_12 and #ove_0_142(slot_53_8) >= 1 then
						ove_0_147(slot_53_8, false)
					end

					if slot_53_13 and slot_53_0.buff.sennapassivemarker and slot_53_0.buff.sennapassivemarker.endTime - game.time > ove_0_100 then
						ove_0_147(slot_53_8, slot_53_5)
					end

					if slot_53_14 and #ove_0_143(slot_53_8) >= 1 then
						ove_0_147(slot_53_8, false)
					end

					if slot_53_15 and #ove_0_144(slot_53_8) >= 1 then
						ove_0_147(slot_53_8, false)
					end

					if slot_53_16 and slot_53_17 <= #ove_0_145(slot_53_8) then
						ove_0_147(slot_53_8, false)
					end

					if slot_53_18 and player.buff[ove_0_28.Rune_DarkHarvestBuff] and not player.buff[ove_0_28.Rune_DarkHarvestCooldownBuff] and (slot_53_0.health + slot_53_0.healthRegenRate * 0.393) / slot_53_0.maxHealth * 100 < 50 then
						ove_0_147(slot_53_8, slot_53_5)
					end

					if slot_53_19 and player.buff[ove_0_28.Rune_GraspOfTheUndyingBuff] and player.buff[ove_0_28.Rune_GraspOfTheUndyingBuff].endTime - game.time > ove_0_100 then
						ove_0_147(slot_53_8, slot_53_5)
					end

					if slot_53_20 then
						if player.buff[ove_0_28.Rune_FirstStrikeBuff] then
							ove_0_147(slot_53_8, slot_53_5)
						end

						local slot_53_26 = player.buff[ove_0_28.Rune_FirstStrikeProccedBuff]

						if slot_53_26 and slot_53_26.endTime - game.time > ove_0_100 then
							ove_0_147(slot_53_8, slot_53_5)
						end
					end

					if slot_53_21 and slot_53_0.buff[ove_0_28.Rune_PressTheAttackBuff] and slot_53_0.buff[ove_0_28.Rune_PressTheAttackBuff].stacks2 == 2 and slot_53_0.buff[ove_0_28.Rune_PressTheAttackBuff].endTime - game.time > ove_0_100 then
						ove_0_147(slot_53_8, false)
					end

					if slot_53_22 and player.buff[ove_0_28.Rune_SummonAeryBuff] then
						ove_0_147(slot_53_8, slot_53_5)
					end

					if slot_53_23 and player.buff[ove_0_28.Rune_ArcaneCometBuff] then
						ove_0_147(slot_53_8, slot_53_5)
					end

					if slot_53_24 and player.buff[ove_0_28.Rune_ManaflowBandBuff] then
						ove_0_147(slot_53_8, slot_53_5)
					end

					if slot_53_25 and player.buff[ove_0_28.Rune_TasteOfBloodBuff] and player.health < player.maxHealth then
						ove_0_147(slot_53_8, slot_53_5)
					end

					return
				end

				ove_0_147(slot_53_8, slot_53_5)
			end
		elseif not slot_53_1 then
			player:castSpell("obj", 0, slot_53_0)
		end
	end
end

local function ove_0_150()
	-- function 54
	if ove_0_21.combo.wcombo:get() and player:spellSlot(1).state == 0 then
		if ove_0_21.combo.slow_w:get() then
			local slot_54_0 = ove_0_25.get_result(ove_0_131)

			if slot_54_0.pos and slot_54_0.obj and ove_0_23.core.can_action() then
				player:castSpell("pos", 1, vec3(slot_54_0.pos.x, slot_54_0.obj.y, slot_54_0.pos.y))
			end
		else
			local slot_54_1 = ove_0_107()

			if ove_0_20.IsValidTarget(slot_54_1) then
				local slot_54_2 = ove_0_43.range * ove_0_43.range

				if slot_54_2 >= slot_54_1.pos2D:distSqr(player.pos2D) then
					local slot_54_3 = ove_0_24.linear.get_prediction(ove_0_43, slot_54_1)

					if slot_54_3 and slot_54_2 >= slot_54_3.startPos:distSqr(slot_54_3.endPos) and not ove_0_24.collision.get_prediction(ove_0_43, slot_54_3, slot_54_1) and ove_0_23.core.can_action() then
						player:castSpell("pos", 1, vec3(slot_54_3.endPos.x, slot_54_1.pos.y, slot_54_3.endPos.y))
					end
				end
			end
		end
	end
end

local function ove_0_151()
	-- function 55
	ove_0_150()
	ove_0_149()
end

local function ove_0_152(arg_56_0)
	-- function 56
	local slot_56_0 = {}
	local slot_56_1 = ove_0_41.range * ove_0_41.range

	for iter_56_0 = 0, objManager.allies_n - 1 do
		local slot_56_2 = objManager.allies[iter_56_0]

		if slot_56_2 and slot_56_2.ptr ~= player.ptr and not slot_56_2.isDead and slot_56_2.isVisible and slot_56_2.isTargetable and slot_56_1 >= slot_56_2.pos2D:distSqr(player.pos2D) and ove_0_21.harass.ifs.qharass_blist[ove_0_36[slot_56_2.ptr] .. "block"] and not ove_0_21.harass.ifs.qharass_blist[ove_0_36[slot_56_2.ptr] .. "block"]:get() and ove_0_21.harass.ifs.qharass_blist[ove_0_36[slot_56_2.ptr] .. "hp"] and ove_0_20.GetPercentHealth(slot_56_2) <= ove_0_21.harass.ifs.qharass_blist[ove_0_36[slot_56_2.ptr] .. "hp"]:get() and arg_56_0 and arg_56_0.ptr and arg_56_0.ptr ~= 0 and not arg_56_0.isDead and arg_56_0.isVisible and arg_56_0.isTargetable and ove_0_20.IsOnLineSegment(slot_56_2.path.serverPos2D, player.path.serverPos2D, arg_56_0.pos2D, ove_0_41.width, ove_0_41.range) then
			slot_56_0[#slot_56_0 + 1] = slot_56_2
		end
	end

	return slot_56_0
end

local ove_0_153 = 0

local function ove_0_154(arg_57_0, arg_57_1)
	-- function 57
	if arg_57_0 and arg_57_0.ptr and arg_57_0.ptr ~= 0 and not arg_57_0.isDead and arg_57_0.isVisible and arg_57_0.isTargetable then
		if arg_57_1 then
			ove_0_20.DelayAction(function()
				-- function 58
				ove_0_153 = game.time + 0.25
			end, 0.25)

			if ove_0_153 > game.time then
				player:castSpell("obj", 0, arg_57_0)
			end
		else
			player:castSpell("obj", 0, arg_57_0)
		end
	end
end

local function ove_0_155(arg_59_0, arg_59_1)
	-- function 59
	local slot_59_0 = ove_0_21.harass.q_slowpred:get()
	local slot_59_1 = ove_0_21.harass.ignore.heal:get()
	local slot_59_2 = ove_0_21.harass.ignore.mist:get()
	local slot_59_3 = ove_0_21.harass.ignore.soul:get()
	local slot_59_4 = ove_0_21.harass.ignore.structure:get()
	local slot_59_5 = ove_0_21.harass.ignore.multi:get()
	local slot_59_6 = ove_0_21.harass.ignore.multi_count:get()
	local slot_59_7 = ove_0_21.harass.ignore.rune.dark_harvest:get()
	local slot_59_8 = ove_0_21.harass.ignore.rune.grasp:get()
	local slot_59_9 = ove_0_21.harass.ignore.rune.first_strike:get()
	local slot_59_10 = ove_0_21.harass.ignore.rune.attack:get()
	local slot_59_11 = ove_0_21.harass.ignore.rune.aery:get()
	local slot_59_12 = ove_0_21.harass.ignore.rune.comet:get()
	local slot_59_13 = ove_0_21.harass.ignore.rune.manaflow:get()
	local slot_59_14 = ove_0_21.harass.ignore.rune.taste:get()

	if slot_59_1 and #ove_0_152(arg_59_1) >= 1 then
		ove_0_154(arg_59_1, false)
	end

	if slot_59_2 and arg_59_0.buff.sennapassivemarker and arg_59_0.buff.sennapassivemarker.endTime - game.time > ove_0_100 then
		ove_0_154(arg_59_1, slot_59_0)
	end

	if slot_59_3 and #ove_0_143(arg_59_1) >= 1 then
		ove_0_154(arg_59_1, false)
	end

	if slot_59_4 and #ove_0_144(arg_59_1) >= 1 then
		ove_0_154(arg_59_1, false)
	end

	if slot_59_5 and slot_59_6 <= #ove_0_145(arg_59_1) then
		ove_0_154(arg_59_1, false)
	end

	if slot_59_7 and player.buff[ove_0_28.Rune_DarkHarvestBuff] and not player.buff[ove_0_28.Rune_DarkHarvestCooldownBuff] and (arg_59_0.health + arg_59_0.healthRegenRate * 0.393) / arg_59_0.maxHealth * 100 < 50 then
		ove_0_154(arg_59_1, slot_59_0)
	end

	if slot_59_8 and player.buff[ove_0_28.Rune_GraspOfTheUndyingBuff] and player.buff[ove_0_28.Rune_GraspOfTheUndyingBuff].endTime - game.time > ove_0_100 then
		ove_0_154(arg_59_1, slot_59_0)
	end

	if slot_59_9 then
		if player.buff[ove_0_28.Rune_FirstStrikeBuff] then
			ove_0_154(arg_59_1, slot_59_0)
		end

		local slot_59_15 = player.buff[ove_0_28.Rune_FirstStrikeProccedBuff]

		if slot_59_15 and slot_59_15.endTime - game.time > ove_0_100 then
			ove_0_154(arg_59_1, slot_59_0)
		end
	end

	if slot_59_10 and arg_59_0.buff[ove_0_28.Rune_PressTheAttackBuff] and arg_59_0.buff[ove_0_28.Rune_PressTheAttackBuff].stacks2 == 2 and arg_59_0.buff[ove_0_28.Rune_PressTheAttackBuff].endTime - game.time > ove_0_100 then
		ove_0_154(arg_59_1, false)
	end

	if slot_59_11 and player.buff[ove_0_28.Rune_SummonAeryBuff] then
		ove_0_154(arg_59_1, slot_59_0)
	end

	if slot_59_12 and player.buff[ove_0_28.Rune_ArcaneCometBuff] then
		ove_0_154(arg_59_1, slot_59_0)
	end

	if slot_59_13 and player.buff[ove_0_28.Rune_ManaflowBandBuff] then
		ove_0_154(arg_59_1, slot_59_0)
	end

	if slot_59_14 and player.buff[ove_0_28.Rune_TasteOfBloodBuff] and player.health < player.maxHealth then
		ove_0_154(arg_59_1, slot_59_0)
	end
end

local function ove_0_156()
	-- function 60
	if not ove_0_21.harass.qharass:get() then
		return
	end

	if player:spellSlot(_Q).state ~= 0 then
		return
	end

	if not ove_0_23.core.can_action() then
		return
	end

	if ove_0_21.harass.q_harass_mana:get() >= ove_0_20.GetPercentMana(player) then
		return
	end

	local slot_60_0 = ove_0_105()

	if ove_0_20.IsValidTarget(slot_60_0) then
		local slot_60_1 = ove_0_21.harass.qharass_after_AA:get()
		local slot_60_2 = ove_0_21.harass.qharass_minions:get()
		local slot_60_3 = ove_0_21.harass.qharass_min_count:get()
		local slot_60_4 = ove_0_21.harass.qharass_ext:get()
		local slot_60_5 = ove_0_21.harass.q_slowpred:get()
		local slot_60_6 = player.attackRange + player.boundingRadius + slot_60_0.boundingRadius
		local slot_60_7 = slot_60_6 * slot_60_6

		if slot_60_1 and slot_60_0.pos2D:distSqr(player.pos2D) <= slot_60_7 + 2500 then
			return
		end

		if slot_60_7 < slot_60_0.pos2D:distSqr(player.pos2D) then
			if not slot_60_4 then
				return
			end

			local slot_60_8 = ove_0_124(player, ove_0_40.range, ove_0_40.width, slot_60_0)
			local slot_60_9 = ove_0_24.linear.get_prediction(ove_0_42, slot_60_0)
			local slot_60_10 = ove_0_40.range * ove_0_40.range

			if slot_60_8 and slot_60_9 and slot_60_10 >= slot_60_9.endPos:distSqr(slot_60_9.startPos) then
				if slot_60_2 then
					slot_60_9.endPos = ove_0_20.VectorExtend(player.pos2D, slot_60_9.endPos, 1300)

					local slot_60_11 = ove_0_24.collision.get_prediction(ove_0_42, slot_60_9)

					if slot_60_11 and slot_60_3 <= #slot_60_11 then
						ove_0_155(slot_60_0, slot_60_8)

						return
					end
				end

				local slot_60_12 = ove_0_21.harass.ifs.heal:get()
				local slot_60_13 = ove_0_21.harass.ifs.mist:get()
				local slot_60_14 = ove_0_21.harass.ifs.soul:get()
				local slot_60_15 = ove_0_21.harass.ifs.structure:get()
				local slot_60_16 = ove_0_21.harass.ifs.multi:get()
				local slot_60_17 = ove_0_21.harass.ifs.multi_count:get()
				local slot_60_18 = ove_0_21.harass.ifs.rune.dark_harvest:get()
				local slot_60_19 = ove_0_21.harass.ifs.rune.grasp:get()
				local slot_60_20 = ove_0_21.harass.ifs.rune.first_strike:get()
				local slot_60_21 = ove_0_21.harass.ifs.rune.attack:get()
				local slot_60_22 = ove_0_21.harass.ifs.rune.aery:get()
				local slot_60_23 = ove_0_21.harass.ifs.rune.comet:get()
				local slot_60_24 = ove_0_21.harass.ifs.rune.manaflow:get()
				local slot_60_25 = ove_0_21.harass.ifs.rune.taste:get()

				if slot_60_12 or slot_60_13 or slot_60_14 or slot_60_15 or slot_60_16 or slot_60_18 or slot_60_19 or slot_60_20 or slot_60_21 or slot_60_22 or slot_60_23 or slot_60_24 or slot_60_25 then
					if slot_60_12 and #ove_0_152(slot_60_8) >= 1 then
						ove_0_154(slot_60_8, false)
					end

					if slot_60_13 and slot_60_0.buff.sennapassivemarker and slot_60_0.buff.sennapassivemarker.endTime - game.time > ove_0_100 then
						ove_0_154(slot_60_8, slot_60_5)
					end

					if slot_60_14 and #ove_0_143(slot_60_8) >= 1 then
						ove_0_154(slot_60_8, false)
					end

					if slot_60_15 and #ove_0_144(slot_60_8) >= 1 then
						ove_0_154(slot_60_8, false)
					end

					if slot_60_16 and slot_60_17 <= #ove_0_145(slot_60_8) then
						ove_0_154(slot_60_8, false)
					end

					if slot_60_18 and player.buff[ove_0_28.Rune_DarkHarvestBuff] and not player.buff[ove_0_28.Rune_DarkHarvestCooldownBuff] and (slot_60_0.health + slot_60_0.healthRegenRate * 0.393) / slot_60_0.maxHealth * 100 < 50 then
						ove_0_154(slot_60_8, slot_60_5)
					end

					if slot_60_19 and player.buff[ove_0_28.Rune_GraspOfTheUndyingBuff] and player.buff[ove_0_28.Rune_GraspOfTheUndyingBuff].endTime - game.time > ove_0_100 then
						ove_0_154(slot_60_8, slot_60_5)
					end

					if slot_60_20 then
						if player.buff[ove_0_28.Rune_FirstStrikeBuff] then
							ove_0_154(slot_60_8, slot_60_5)
						end

						local slot_60_26 = player.buff[ove_0_28.Rune_FirstStrikeProccedBuff]

						if slot_60_26 and slot_60_26.endTime - game.time > ove_0_100 then
							ove_0_154(slot_60_8, slot_60_5)
						end
					end

					if slot_60_21 and slot_60_0.buff[ove_0_28.Rune_PressTheAttackBuff] and slot_60_0.buff[ove_0_28.Rune_PressTheAttackBuff].stacks2 == 2 and slot_60_0.buff[ove_0_28.Rune_PressTheAttackBuff].endTime - game.time > ove_0_100 then
						ove_0_154(slot_60_8, false)
					end

					if slot_60_22 and player.buff[ove_0_28.Rune_SummonAeryBuff] then
						ove_0_154(slot_60_8, slot_60_5)
					end

					if slot_60_23 and player.buff[ove_0_28.Rune_ArcaneCometBuff] then
						ove_0_154(slot_60_8, slot_60_5)
					end

					if slot_60_24 and player.buff[ove_0_28.Rune_ManaflowBandBuff] then
						ove_0_154(slot_60_8, slot_60_5)
					end

					if slot_60_25 and player.buff[ove_0_28.Rune_TasteOfBloodBuff] and player.health < player.maxHealth then
						ove_0_154(slot_60_8, slot_60_5)
					end

					return
				end

				ove_0_154(slot_60_8, slot_60_5)
			end
		elseif not slot_60_1 then
			player:castSpell("obj", 0, slot_60_0)
		end
	end
end

local function ove_0_157()
	-- function 61
	ove_0_156()

	if ove_0_21.harass.wharass:get() and player:spellSlot(1).state == 0 and ove_0_23.core.can_action() and ove_0_20.GetPercentMana(player) > ove_0_21.harass.w_harass_mana:get() then
		if ove_0_21.harass.slow_w:get() then
			if ove_0_23.core.can_action() then
				local slot_61_0 = ove_0_25.get_result(ove_0_131)

				if slot_61_0.pos and slot_61_0.obj then
					player:castSpell("pos", 1, vec3(slot_61_0.pos.x, slot_61_0.obj.y, slot_61_0.pos.y))
				end
			end
		else
			local slot_61_1 = ove_0_107()

			if ove_0_20.IsValidTarget(slot_61_1) then
				local slot_61_2 = ove_0_43.range * ove_0_43.range

				if slot_61_2 >= slot_61_1.pos2D:distSqr(player.pos2D) then
					local slot_61_3 = ove_0_24.linear.get_prediction(ove_0_43, slot_61_1)

					if slot_61_3 and slot_61_2 >= slot_61_3.startPos:distSqr(slot_61_3.endPos) and not ove_0_24.collision.get_prediction(ove_0_43, slot_61_3, slot_61_1) then
						player:castSpell("pos", 1, vec3(slot_61_3.endPos.x, slot_61_1.pos.y, slot_61_3.endPos.y))
					end
				end
			end
		end
	end
end

local ove_0_158 = 0

local function ove_0_159(arg_62_0, arg_62_1)
	-- function 62
	if arg_62_0 and arg_62_0.ptr and arg_62_0.ptr ~= 0 and not arg_62_0.isDead and arg_62_0.isVisible and arg_62_0.isTargetable then
		if arg_62_1 then
			ove_0_20.DelayAction(function()
				-- function 63
				ove_0_158 = game.time + 0.25
			end, 0.25)

			if ove_0_158 > game.time then
				player:castSpell("obj", 0, arg_62_0)
			end
		else
			player:castSpell("obj", 0, arg_62_0)
		end
	end
end

local function ove_0_160(arg_64_0)
	-- function 64
	local slot_64_0 = {}
	local slot_64_1 = ove_0_41.range * ove_0_41.range

	for iter_64_0 = 0, objManager.allies_n - 1 do
		local slot_64_2 = objManager.allies[iter_64_0]

		if slot_64_2 and slot_64_2.ptr ~= player.ptr and not slot_64_2.isDead and slot_64_2.isVisible and slot_64_2.isTargetable and slot_64_1 >= slot_64_2.pos2D:distSqr(player.pos2D) and ove_0_21.autoq.ifs.autoq_blist[ove_0_36[slot_64_2.ptr] .. "block"] and not ove_0_21.autoq.ifs.autoq_blist[ove_0_36[slot_64_2.ptr] .. "block"]:get() and ove_0_21.autoq.ifs.autoq_blist[ove_0_36[slot_64_2.ptr] .. "hp"] and ove_0_20.GetPercentHealth(slot_64_2) <= ove_0_21.autoq.ifs.autoq_blist[ove_0_36[slot_64_2.ptr] .. "hp"]:get() and arg_64_0 and arg_64_0.ptr and arg_64_0.ptr ~= 0 and not arg_64_0.isDead and arg_64_0.isVisible and arg_64_0.isTargetable and ove_0_20.IsOnLineSegment(slot_64_2.path.serverPos2D, player.path.serverPos2D, arg_64_0.pos2D, ove_0_41.width, ove_0_41.range) then
			slot_64_0[#slot_64_0 + 1] = slot_64_2
		end
	end

	return slot_64_0
end

local function ove_0_161(arg_65_0, arg_65_1)
	-- function 65
	local slot_65_0 = ove_0_21.autoq.q_slowpred:get()
	local slot_65_1 = ove_0_21.autoq.ignore.heal:get()
	local slot_65_2 = ove_0_21.autoq.ignore.mist:get()
	local slot_65_3 = ove_0_21.autoq.ignore.soul:get()
	local slot_65_4 = ove_0_21.autoq.ignore.structure:get()
	local slot_65_5 = ove_0_21.autoq.ignore.multi:get()
	local slot_65_6 = ove_0_21.autoq.ignore.multi_count:get()
	local slot_65_7 = ove_0_21.autoq.ignore.rune.dark_harvest:get()
	local slot_65_8 = ove_0_21.autoq.ignore.rune.grasp:get()
	local slot_65_9 = ove_0_21.autoq.ignore.rune.first_strike:get()
	local slot_65_10 = ove_0_21.autoq.ignore.rune.attack:get()
	local slot_65_11 = ove_0_21.autoq.ignore.rune.aery:get()
	local slot_65_12 = ove_0_21.autoq.ignore.rune.comet:get()
	local slot_65_13 = ove_0_21.autoq.ignore.rune.manaflow:get()
	local slot_65_14 = ove_0_21.autoq.ignore.rune.taste:get()

	if slot_65_1 and #ove_0_160(arg_65_1) >= 1 then
		ove_0_159(arg_65_1, false)
	end

	if slot_65_2 and arg_65_0.buff.sennapassivemarker and arg_65_0.buff.sennapassivemarker.endTime - game.time > ove_0_100 then
		ove_0_159(arg_65_1, slot_65_0)
	end

	if slot_65_3 and #ove_0_143(arg_65_1) >= 1 then
		ove_0_159(arg_65_1, false)
	end

	if slot_65_4 and #ove_0_144(arg_65_1) >= 1 then
		ove_0_159(arg_65_1, false)
	end

	if slot_65_5 and slot_65_6 <= #ove_0_145(arg_65_1) then
		ove_0_159(arg_65_1, false)
	end

	if slot_65_7 and player.buff[ove_0_28.Rune_DarkHarvestBuff] and not player.buff[ove_0_28.Rune_DarkHarvestCooldownBuff] and (arg_65_0.health + arg_65_0.healthRegenRate * 0.393) / arg_65_0.maxHealth * 100 < 50 then
		ove_0_159(arg_65_1, slot_65_0)
	end

	if slot_65_8 and player.buff[ove_0_28.Rune_GraspOfTheUndyingBuff] and player.buff[ove_0_28.Rune_GraspOfTheUndyingBuff].endTime - game.time > ove_0_100 then
		ove_0_159(arg_65_1, slot_65_0)
	end

	if slot_65_9 then
		if player.buff[ove_0_28.Rune_FirstStrikeBuff] then
			ove_0_159(arg_65_1, slot_65_0)
		end

		local slot_65_15 = player.buff[ove_0_28.Rune_FirstStrikeProccedBuff]

		if slot_65_15 and slot_65_15.endTime - game.time > ove_0_100 then
			ove_0_159(arg_65_1, slot_65_0)
		end
	end

	if slot_65_10 and arg_65_0.buff[ove_0_28.Rune_PressTheAttackBuff] and arg_65_0.buff[ove_0_28.Rune_PressTheAttackBuff].stacks2 == 2 and arg_65_0.buff[ove_0_28.Rune_PressTheAttackBuff].endTime - game.time > ove_0_100 then
		ove_0_159(arg_65_1, false)
	end

	if slot_65_11 and player.buff[ove_0_28.Rune_SummonAeryBuff] then
		ove_0_159(arg_65_1, slot_65_0)
	end

	if slot_65_12 and player.buff[ove_0_28.Rune_ArcaneCometBuff] then
		ove_0_159(arg_65_1, slot_65_0)
	end

	if slot_65_13 and player.buff[ove_0_28.Rune_ManaflowBandBuff] then
		ove_0_159(arg_65_1, slot_65_0)
	end

	if slot_65_14 and player.buff[ove_0_28.Rune_TasteOfBloodBuff] and player.health < player.maxHealth then
		ove_0_159(arg_65_1, slot_65_0)
	end
end

local function ove_0_162()
	-- function 66
	if not ove_0_21.autoq.enable:get() then
		return
	end

	if ove_0_21.autoq.pause_autoq:get() and (ove_0_21.keys.combokey:get() or ove_0_21.keys.harasskey:get()) then
		return
	end

	if not ove_0_21.autoq.keys_autoq:get() then
		return
	end

	if ove_0_20.IsRecalling(player) then
		return
	end

	if player:spellSlot(_Q).state ~= 0 then
		return
	end

	if not ove_0_23.core.can_action() then
		return
	end

	if ove_0_21.autoq.mana:get() >= ove_0_20.GetPercentMana(player) then
		return
	end

	local slot_66_0 = ove_0_105()

	if ove_0_20.IsValidTarget(slot_66_0) then
		local slot_66_1 = ove_0_21.autoq.autoq_minions:get()
		local slot_66_2 = ove_0_21.autoq.autoq_min_count:get()
		local slot_66_3 = ove_0_21.autoq.q_slowpred:get()
		local slot_66_4 = player.attackRange + player.boundingRadius + slot_66_0.boundingRadius

		if slot_66_4 * slot_66_4 < slot_66_0.pos2D:distSqr(player.pos2D) then
			local slot_66_5 = ove_0_124(player, ove_0_40.range, ove_0_40.width, slot_66_0)
			local slot_66_6 = ove_0_24.linear.get_prediction(ove_0_42, slot_66_0)
			local slot_66_7 = ove_0_40.range * ove_0_40.range

			if slot_66_5 and slot_66_6 and slot_66_7 >= slot_66_6.endPos:distSqr(slot_66_6.startPos) then
				if slot_66_1 then
					slot_66_6.endPos = ove_0_20.VectorExtend(player.pos2D, slot_66_6.endPos, 1300)

					local slot_66_8 = ove_0_24.collision.get_prediction(ove_0_42, slot_66_6)

					if slot_66_8 and slot_66_2 <= #slot_66_8 then
						ove_0_161(slot_66_0, slot_66_5)

						return
					end
				end

				local slot_66_9 = ove_0_21.autoq.ifs.heal:get()
				local slot_66_10 = ove_0_21.autoq.ifs.mist:get()
				local slot_66_11 = ove_0_21.autoq.ifs.soul:get()
				local slot_66_12 = ove_0_21.autoq.ifs.structure:get()
				local slot_66_13 = ove_0_21.autoq.ifs.multi:get()
				local slot_66_14 = ove_0_21.autoq.ifs.multi_count:get()
				local slot_66_15 = ove_0_21.autoq.ifs.rune.dark_harvest:get()
				local slot_66_16 = ove_0_21.autoq.ifs.rune.grasp:get()
				local slot_66_17 = ove_0_21.autoq.ifs.rune.first_strike:get()
				local slot_66_18 = ove_0_21.autoq.ifs.rune.attack:get()
				local slot_66_19 = ove_0_21.autoq.ifs.rune.aery:get()
				local slot_66_20 = ove_0_21.autoq.ifs.rune.comet:get()
				local slot_66_21 = ove_0_21.autoq.ifs.rune.manaflow:get()
				local slot_66_22 = ove_0_21.autoq.ifs.rune.taste:get()

				if slot_66_9 or slot_66_10 or slot_66_11 or slot_66_12 or slot_66_13 or slot_66_15 or slot_66_16 or slot_66_17 or slot_66_18 or slot_66_19 or slot_66_20 or slot_66_21 or slot_66_22 then
					if slot_66_9 and #ove_0_160(slot_66_5) >= 1 then
						ove_0_159(slot_66_5, false)
					end

					if slot_66_10 and slot_66_0.buff.sennapassivemarker and slot_66_0.buff.sennapassivemarker.endTime - game.time > ove_0_100 then
						ove_0_159(slot_66_5, slot_66_3)
					end

					if slot_66_11 and #ove_0_143(slot_66_5) >= 1 then
						ove_0_159(slot_66_5, false)
					end

					if slot_66_12 and #ove_0_144(slot_66_5) >= 1 then
						ove_0_159(slot_66_5, false)
					end

					if slot_66_13 and slot_66_14 <= #ove_0_145(slot_66_5) then
						ove_0_159(slot_66_5, false)
					end

					if slot_66_15 and player.buff[ove_0_28.Rune_DarkHarvestBuff] and not player.buff[ove_0_28.Rune_DarkHarvestCooldownBuff] and (slot_66_0.health + slot_66_0.healthRegenRate * 0.393) / slot_66_0.maxHealth * 100 < 50 then
						ove_0_159(slot_66_5, slot_66_3)
					end

					if slot_66_16 and player.buff[ove_0_28.Rune_GraspOfTheUndyingBuff] and player.buff[ove_0_28.Rune_GraspOfTheUndyingBuff].endTime - game.time > ove_0_100 then
						ove_0_159(slot_66_5, slot_66_3)
					end

					if slot_66_17 then
						if player.buff[ove_0_28.Rune_FirstStrikeBuff] then
							ove_0_159(slot_66_5, slot_66_3)
						end

						local slot_66_23 = player.buff[ove_0_28.Rune_FirstStrikeProccedBuff]

						if slot_66_23 and slot_66_23.endTime - game.time > ove_0_100 then
							ove_0_159(slot_66_5, slot_66_3)
						end
					end

					if slot_66_18 and slot_66_0.buff[ove_0_28.Rune_PressTheAttackBuff] and slot_66_0.buff[ove_0_28.Rune_PressTheAttackBuff].stacks2 == 2 and slot_66_0.buff[ove_0_28.Rune_PressTheAttackBuff].endTime - game.time > ove_0_100 then
						ove_0_159(slot_66_5, false)
					end

					if slot_66_19 and player.buff[ove_0_28.Rune_SummonAeryBuff] then
						ove_0_159(slot_66_5, slot_66_3)
					end

					if slot_66_20 and player.buff[ove_0_28.Rune_ArcaneCometBuff] then
						ove_0_159(slot_66_5, slot_66_3)
					end

					if slot_66_21 and player.buff[ove_0_28.Rune_ManaflowBandBuff] then
						ove_0_159(slot_66_5, slot_66_3)
					end

					if slot_66_22 and player.buff[ove_0_28.Rune_TasteOfBloodBuff] and player.health < player.maxHealth then
						ove_0_159(slot_66_5, slot_66_3)
					end

					return
				end

				ove_0_159(slot_66_5, slot_66_3)
			end
		end
	end
end

local function ove_0_163()
	-- function 67
	local slot_67_0

	if ove_0_21.pick.soul_pickup:get() then
		local slot_67_1 = player.attackRange + player.boundingRadius
		local slot_67_2 = slot_67_1 * slot_67_1

		for iter_67_0, iter_67_1 in pairs(ove_0_81) do
			if iter_67_1 and iter_67_1.ptr ~= 0 and not iter_67_1.isDead and iter_67_1.health >= 1 and iter_67_1.isTargetable and ove_0_82[iter_67_1.ptr] and (iter_67_1.boundingRadius and iter_67_1.pos2D:distSqr(player.pos2D) <= slot_67_2 + iter_67_1.boundingRadius * iter_67_1.boundingRadius or slot_67_2 >= iter_67_1.pos2D:distSqr(player.pos2D)) then
				if slot_67_0 == nil then
					slot_67_0 = iter_67_1
				elseif ove_0_82[iter_67_1.ptr] < ove_0_82[slot_67_0.ptr] then
					slot_67_0 = iter_67_1
				elseif ove_0_82[iter_67_1.ptr] == ove_0_82[slot_67_0.ptr] and iter_67_1.pos2D:distSqr(player.pos2D) <= slot_67_0.pos2D:distSqr(player.pos2D) then
					slot_67_0 = iter_67_1
				end
			end
		end
	end

	return slot_67_0
end

local function ove_0_164()
	-- function 68
	local slot_68_0 = ove_0_163()

	if slot_68_0 and slot_68_0.ptr ~= 0 and not slot_68_0.isDead and slot_68_0.isTargetable and ove_0_23.combat.can_attack() then
		player:attack(slot_68_0)
		ove_0_23.core.set_server_pause()
	end
end

local function ove_0_165()
	-- function 69
	if ove_0_21.pick.pickup_combo:get() and ove_0_21.keys.combokey:get() then
		if ove_0_21.pick.pickup_combo_dis:get() then
			if #ove_0_20.count_enemies_in_range(player.pos2D, player.attackRange + player.boundingRadius + 1500) <= 0 then
				ove_0_164()

				return
			end
		else
			ove_0_164()

			return
		end
	end

	if ove_0_21.pick.pickup_harass:get() and ove_0_21.keys.harasskey:get() then
		ove_0_164()

		return
	end

	if ove_0_21.pick.pickup_laneclear:get() and ove_0_21.keys.clearkey:get() then
		ove_0_164()

		return
	end

	if ove_0_21.pick.pickup_lasthit:get() and ove_0_21.keys.lastkey:get() then
		ove_0_164()

		return
	end
end

local function ove_0_166(arg_70_0, arg_70_1, arg_70_2, arg_70_3)
	-- function 70
	local slot_70_0 = 0
	local slot_70_1 = ove_0_40.range * ove_0_40.range
	local slot_70_2 = arg_70_2 * arg_70_2

	for iter_70_0 = 0, objManager.minions.size[TEAM_ENEMY] - 1 do
		local slot_70_3 = objManager.minions[TEAM_ENEMY][iter_70_0]

		if slot_70_3 and ove_0_20.valid_minion(slot_70_3) and slot_70_1 >= slot_70_3.pos2D:distSqr(player.pos2D) then
			local slot_70_4, slot_70_5, slot_70_6 = ove_0_20.VectorPointProjectionOnLineSegment(arg_70_0, arg_70_1, slot_70_3)

			if slot_70_6 and slot_70_2 > slot_70_3.pos2D:distSqr(slot_70_4) and arg_70_0:distSqr(arg_70_1) >= arg_70_0:distSqr(slot_70_3.pos2D) then
				slot_70_0 = slot_70_0 + 1
			end
		end
	end

	return slot_70_0
end

local function ove_0_167(arg_71_0, arg_71_1, arg_71_2)
	-- function 71
	local slot_71_0 = 0
	local slot_71_1
	local slot_71_2 = ove_0_40.range * ove_0_40.range

	for iter_71_0 = 0, objManager.minions.size[TEAM_ENEMY] - 1 do
		local slot_71_3 = objManager.minions[TEAM_ENEMY][iter_71_0]

		if slot_71_3 and ove_0_20.valid_minion(slot_71_3) and slot_71_2 >= slot_71_3.pos2D:distSqr(arg_71_0) then
			local slot_71_4 = ove_0_20.VectorExtend(player.pos2D, slot_71_3.pos2D, ove_0_40.range)
			local slot_71_5 = ove_0_166(player.pos2D, slot_71_4, ove_0_40.width + 40, ove_0_40.range)

			if slot_71_0 < slot_71_5 and slot_71_5 >= ove_0_21.farminggg.laneclear.q_farm_count:get() then
				slot_71_1 = slot_71_3
			end
		end
	end

	return slot_71_1
end

local function ove_0_168()
	-- function 72
	if not ove_0_21.farminggg.farmtog:get() then
		return
	end

	if not ove_0_21.farminggg.enable_farm:get() or player:spellSlot(0).state ~= 0 then
		return
	end

	if ove_0_21.farminggg.laneclear.laneQ_helper:get() and ove_0_20.GetPercentMana(player) >= ove_0_21.farminggg.laneclear.q_helper_mana:get() then
		if ove_0_21.farminggg.laneclear.helperQdis:get() and #ove_0_20.count_enemies_in_range(player.pos2D, 2300) >= 1 then
			return
		end

		local slot_72_0 = {
			delay = ove_0_100,
			speed = math.huge,
			range = player.attackRange + player.boundingRadius,
			damage = function(arg_73_0)
				-- function 73
				return ove_0_120(arg_73_0)
			end
		}
		local slot_72_1, slot_72_2 = ove_0_23.farm.skill_farm_target(slot_72_0)

		if slot_72_2 and ove_0_20.valid_minion(slot_72_2) then
			player:castSpell("obj", 0, slot_72_2)
		end
	end

	if ove_0_21.farminggg.laneclear.laneQ:get() and ove_0_20.GetPercentMana(player) >= ove_0_21.farminggg.laneclear.laneQmana:get() then
		if ove_0_21.farminggg.laneclear.laneQdis:get() and #ove_0_20.count_enemies_in_range(player.pos2D, 2300) >= 1 then
			return
		end

		local slot_72_3 = ove_0_167(player.pos2D, ove_0_40.range, ove_0_40.width + 40)

		if slot_72_3 and slot_72_3.ptr ~= 0 and ove_0_20.is_valid(slot_72_3) and ove_0_23.core.can_action() then
			local slot_72_4 = player.attackRange + player.boundingRadius + (slot_72_3.boundingRadius or 0)

			if slot_72_4 * slot_72_4 >= slot_72_3.pos2D:distSqr(player.pos2D) then
				player:castSpell("obj", 0, slot_72_3)
			end
		end
	end
end

local function ove_0_169()
	-- function 74
	if not ove_0_21.farminggg.farmtog:get() then
		return
	end

	if not ove_0_21.farminggg.enable_farm:get() then
		return
	end

	if ove_0_21.farminggg.jungleclear.jungleQ:get() and ove_0_20.GetPercentMana(player) >= ove_0_21.farminggg.jungleclear.jungmanaQ:get() and player:spellSlot(0).state == 0 then
		local slot_74_0 = player.attackRange + player.boundingRadius

		for iter_74_0 = 0, objManager.minions.size[TEAM_NEUTRAL] - 1 do
			local slot_74_1 = objManager.minions[TEAM_NEUTRAL][iter_74_0]

			if slot_74_1 and ove_0_20.valid_minion(slot_74_1) and (slot_74_0 + (slot_74_1.boundingRadius or 0)) * (slot_74_0 + (slot_74_1.boundingRadius or 0)) >= slot_74_1.pos2D:distSqr(player.pos2D) and ove_0_23.core.can_action() then
				player:castSpell("obj", 0, slot_74_1)
			end
		end
	end

	if ove_0_21.farminggg.jungleclear.jungleW:get() and ove_0_20.GetPercentMana(player) >= ove_0_21.farminggg.jungleclear.jungmanaW:get() and player:spellSlot(1).state == 0 then
		local slot_74_2 = ove_0_43.range * ove_0_43.range

		for iter_74_1 = 0, objManager.minions.size[TEAM_NEUTRAL] - 1 do
			local slot_74_3 = objManager.minions[TEAM_NEUTRAL][iter_74_1]

			if slot_74_3 and ove_0_20.valid_minion(slot_74_3) and slot_74_2 >= slot_74_3.pos2D:distSqr(player.pos2D) then
				local slot_74_4 = ove_0_24.linear.get_prediction(ove_0_43, slot_74_3)

				if slot_74_4 and slot_74_2 >= slot_74_4.startPos:distSqr(slot_74_4.endPos) and not ove_0_24.collision.get_prediction(ove_0_43, slot_74_4, slot_74_3) and ove_0_23.core.can_action() then
					player:castSpell("pos", 1, vec3(slot_74_4.endPos.x, slot_74_3.pos.y, slot_74_4.endPos.y))
				end
			end
		end
	end
end

local ove_0_170 = {
	multi_count_one = ove_0_21.combo.ifs.multi_count:get(),
	multi_count_two = ove_0_21.combo.ignore.multi_count:get()
}
local ove_0_171 = {
	multi_count_one = ove_0_21.harass.ifs.multi_count:get(),
	multi_count_two = ove_0_21.harass.ignore.multi_count:get()
}
local ove_0_172 = {
	multi_count_one = ove_0_21.autoq.ifs.multi_count:get(),
	multi_count_two = ove_0_21.autoq.ignore.multi_count:get()
}
local ove_0_173 = 0

for iter_0_12 = 0, objManager.allies_n - 1 do
	local ove_0_174 = objManager.allies[iter_0_12]

	if ove_0_174.ptr ~= player.ptr then
		if ove_0_21.combo.ifs.qcombo_blist[ove_0_36[ove_0_174.ptr] .. "hp"] then
			ove_0_170[ove_0_36[ove_0_174.ptr] .. "hp_one"] = ove_0_21.combo.ifs.qcombo_blist[ove_0_36[ove_0_174.ptr] .. "hp"]:get()
		end

		if ove_0_21.combo.ifs.qcombo_blist[ove_0_36[ove_0_174.ptr] .. "block"] then
			ove_0_170[ove_0_36[ove_0_174.ptr] .. "block_one"] = ove_0_21.combo.ifs.qcombo_blist[ove_0_36[ove_0_174.ptr] .. "block"]:get()
		end

		if ove_0_21.combo.ignore.qcombo_blist[ove_0_36[ove_0_174.ptr] .. "hp"] then
			ove_0_170[ove_0_36[ove_0_174.ptr] .. "hp_two"] = ove_0_21.combo.ignore.qcombo_blist[ove_0_36[ove_0_174.ptr] .. "hp"]:get()
		end

		if ove_0_21.combo.ignore.qcombo_blist[ove_0_36[ove_0_174.ptr] .. "block"] then
			ove_0_170[ove_0_36[ove_0_174.ptr] .. "block_two"] = ove_0_21.combo.ignore.qcombo_blist[ove_0_36[ove_0_174.ptr] .. "block"]:get()
		end

		if ove_0_21.harass.ifs.qharass_blist[ove_0_36[ove_0_174.ptr] .. "hp"] then
			ove_0_171[ove_0_36[ove_0_174.ptr] .. "hp_one"] = ove_0_21.harass.ifs.qharass_blist[ove_0_36[ove_0_174.ptr] .. "hp"]:get()
		end

		if ove_0_21.harass.ifs.qharass_blist[ove_0_36[ove_0_174.ptr] .. "block"] then
			ove_0_171[ove_0_36[ove_0_174.ptr] .. "block_one"] = ove_0_21.harass.ifs.qharass_blist[ove_0_36[ove_0_174.ptr] .. "block"]:get()
		end

		if ove_0_21.harass.ignore.qharass_blist[ove_0_36[ove_0_174.ptr] .. "hp"] then
			ove_0_171[ove_0_36[ove_0_174.ptr] .. "hp_two"] = ove_0_21.harass.ignore.qharass_blist[ove_0_36[ove_0_174.ptr] .. "hp"]:get()
		end

		if ove_0_21.harass.ignore.qharass_blist[ove_0_36[ove_0_174.ptr] .. "block"] then
			ove_0_171[ove_0_36[ove_0_174.ptr] .. "block_two"] = ove_0_21.harass.ignore.qharass_blist[ove_0_36[ove_0_174.ptr] .. "block"]:get()
		end

		if ove_0_21.autoq.ifs.autoq_blist[ove_0_36[ove_0_174.ptr] .. "hp"] then
			ove_0_172[ove_0_36[ove_0_174.ptr] .. "hp_one"] = ove_0_21.autoq.ifs.autoq_blist[ove_0_36[ove_0_174.ptr] .. "hp"]:get()
		end

		if ove_0_21.autoq.ifs.autoq_blist[ove_0_36[ove_0_174.ptr] .. "block"] then
			ove_0_172[ove_0_36[ove_0_174.ptr] .. "block_one"] = ove_0_21.autoq.ifs.autoq_blist[ove_0_36[ove_0_174.ptr] .. "block"]:get()
		end

		if ove_0_21.autoq.ignore.autoq_blist[ove_0_36[ove_0_174.ptr] .. "hp"] then
			ove_0_172[ove_0_36[ove_0_174.ptr] .. "hp_two"] = ove_0_21.autoq.ignore.autoq_blist[ove_0_36[ove_0_174.ptr] .. "hp"]:get()
		end

		if ove_0_21.autoq.ignore.autoq_blist[ove_0_36[ove_0_174.ptr] .. "block"] then
			ove_0_172[ove_0_36[ove_0_174.ptr] .. "block_two"] = ove_0_21.autoq.ignore.autoq_blist[ove_0_36[ove_0_174.ptr] .. "block"]:get()
		end
	end

	ove_0_173 = ove_0_173 + 1
end

local function ove_0_175()
	-- function 75
	if not menu:isopen() then
		return
	end

	if ove_0_170.multi_count_one ~= ove_0_21.combo.ifs.multi_count:get() then
		ove_0_21.combo.ignore.multi_count:set("value", ove_0_21.combo.ifs.multi_count:get())

		ove_0_170.multi_count_one = ove_0_21.combo.ifs.multi_count:get()
		ove_0_170.multi_count_two = ove_0_21.combo.ifs.multi_count:get()
	end

	if ove_0_170.multi_count_two ~= ove_0_21.combo.ignore.multi_count:get() then
		ove_0_21.combo.ifs.multi_count:set("value", ove_0_21.combo.ignore.multi_count:get())

		ove_0_170.multi_count_one = ove_0_21.combo.ignore.multi_count:get()
		ove_0_170.multi_count_two = ove_0_21.combo.ignore.multi_count:get()
	end

	if ove_0_171.multi_count_one ~= ove_0_21.harass.ifs.multi_count:get() then
		ove_0_21.harass.ignore.multi_count:set("value", ove_0_21.harass.ifs.multi_count:get())

		ove_0_171.multi_count_one = ove_0_21.harass.ifs.multi_count:get()
		ove_0_171.multi_count_two = ove_0_21.harass.ifs.multi_count:get()
	end

	if ove_0_171.multi_count_two ~= ove_0_21.harass.ignore.multi_count:get() then
		ove_0_21.harass.ifs.multi_count:set("value", ove_0_21.harass.ignore.multi_count:get())

		ove_0_171.multi_count_one = ove_0_21.harass.ignore.multi_count:get()
		ove_0_171.multi_count_two = ove_0_21.harass.ignore.multi_count:get()
	end

	if ove_0_172.multi_count_one ~= ove_0_21.autoq.ifs.multi_count:get() then
		ove_0_21.autoq.ignore.multi_count:set("value", ove_0_21.autoq.ifs.multi_count:get())

		ove_0_172.multi_count_one = ove_0_21.autoq.ifs.multi_count:get()
		ove_0_172.multi_count_two = ove_0_21.autoq.ifs.multi_count:get()
	end

	if ove_0_172.multi_count_two ~= ove_0_21.autoq.ignore.multi_count:get() then
		ove_0_21.autoq.ifs.multi_count:set("value", ove_0_21.autoq.ignore.multi_count:get())

		ove_0_172.multi_count_one = ove_0_21.autoq.ignore.multi_count:get()
		ove_0_172.multi_count_two = ove_0_21.autoq.ignore.multi_count:get()
	end

	for iter_75_0 = 0, objManager.allies_n - 1 do
		local slot_75_0 = objManager.allies[iter_75_0]

		if slot_75_0.ptr ~= player.ptr then
			if ove_0_21.combo.ifs.qcombo_blist[ove_0_36[slot_75_0.ptr] .. "hp"] and ove_0_21.combo.ifs.qcombo_blist[ove_0_36[slot_75_0.ptr] .. "block"] and ove_0_21.combo.ignore.qcombo_blist[ove_0_36[slot_75_0.ptr] .. "hp"] and ove_0_21.combo.ignore.qcombo_blist[ove_0_36[slot_75_0.ptr] .. "block"] then
				if ove_0_170[ove_0_36[slot_75_0.ptr] .. "hp_one"] ~= ove_0_21.combo.ifs.qcombo_blist[ove_0_36[slot_75_0.ptr] .. "hp"]:get() then
					ove_0_21.combo.ignore.qcombo_blist[ove_0_36[slot_75_0.ptr] .. "hp"]:set("value", ove_0_21.combo.ifs.qcombo_blist[ove_0_36[slot_75_0.ptr] .. "hp"]:get())

					ove_0_170[ove_0_36[slot_75_0.ptr] .. "hp_one"] = ove_0_21.combo.ifs.qcombo_blist[ove_0_36[slot_75_0.ptr] .. "hp"]:get()
					ove_0_170[ove_0_36[slot_75_0.ptr] .. "hp_two"] = ove_0_21.combo.ifs.qcombo_blist[ove_0_36[slot_75_0.ptr] .. "hp"]:get()
				end

				if ove_0_170[ove_0_36[slot_75_0.ptr] .. "hp_two"] ~= ove_0_21.combo.ignore.qcombo_blist[ove_0_36[slot_75_0.ptr] .. "hp"]:get() then
					ove_0_21.combo.ifs.qcombo_blist[ove_0_36[slot_75_0.ptr] .. "hp"]:set("value", ove_0_21.combo.ignore.qcombo_blist[ove_0_36[slot_75_0.ptr] .. "hp"]:get())

					ove_0_170[ove_0_36[slot_75_0.ptr] .. "hp_one"] = ove_0_21.combo.ignore.qcombo_blist[ove_0_36[slot_75_0.ptr] .. "hp"]:get()
					ove_0_170[ove_0_36[slot_75_0.ptr] .. "hp_two"] = ove_0_21.combo.ignore.qcombo_blist[ove_0_36[slot_75_0.ptr] .. "hp"]:get()
				end

				if ove_0_170[ove_0_36[slot_75_0.ptr] .. "block_one"] ~= ove_0_21.combo.ifs.qcombo_blist[ove_0_36[slot_75_0.ptr] .. "block"]:get() then
					ove_0_21.combo.ignore.qcombo_blist[ove_0_36[slot_75_0.ptr] .. "block"]:set("value", ove_0_21.combo.ifs.qcombo_blist[ove_0_36[slot_75_0.ptr] .. "block"]:get())

					ove_0_170[ove_0_36[slot_75_0.ptr] .. "block_one"] = ove_0_21.combo.ifs.qcombo_blist[ove_0_36[slot_75_0.ptr] .. "block"]:get()
					ove_0_170[ove_0_36[slot_75_0.ptr] .. "block_two"] = ove_0_21.combo.ifs.qcombo_blist[ove_0_36[slot_75_0.ptr] .. "block"]:get()
				end

				if ove_0_170[ove_0_36[slot_75_0.ptr] .. "block_two"] ~= ove_0_21.combo.ignore.qcombo_blist[ove_0_36[slot_75_0.ptr] .. "block"]:get() then
					ove_0_21.combo.ifs.qcombo_blist[ove_0_36[slot_75_0.ptr] .. "block"]:set("value", ove_0_21.combo.ignore.qcombo_blist[ove_0_36[slot_75_0.ptr] .. "block"]:get())

					ove_0_170[ove_0_36[slot_75_0.ptr] .. "block_one"] = ove_0_21.combo.ignore.qcombo_blist[ove_0_36[slot_75_0.ptr] .. "block"]:get()
					ove_0_170[ove_0_36[slot_75_0.ptr] .. "block_two"] = ove_0_21.combo.ignore.qcombo_blist[ove_0_36[slot_75_0.ptr] .. "block"]:get()
				end
			end

			if ove_0_21.harass.ifs.qharass_blist[ove_0_36[slot_75_0.ptr] .. "hp"] and ove_0_21.harass.ifs.qharass_blist[ove_0_36[slot_75_0.ptr] .. "block"] and ove_0_21.harass.ignore.qharass_blist[ove_0_36[slot_75_0.ptr] .. "hp"] and ove_0_21.harass.ignore.qharass_blist[ove_0_36[slot_75_0.ptr] .. "block"] then
				if ove_0_171[ove_0_36[slot_75_0.ptr] .. "hp_one"] ~= ove_0_21.harass.ifs.qharass_blist[ove_0_36[slot_75_0.ptr] .. "hp"]:get() then
					ove_0_21.harass.ignore.qharass_blist[ove_0_36[slot_75_0.ptr] .. "hp"]:set("value", ove_0_21.harass.ifs.qharass_blist[ove_0_36[slot_75_0.ptr] .. "hp"]:get())

					ove_0_171[ove_0_36[slot_75_0.ptr] .. "hp_one"] = ove_0_21.harass.ifs.qharass_blist[ove_0_36[slot_75_0.ptr] .. "hp"]:get()
					ove_0_171[ove_0_36[slot_75_0.ptr] .. "hp_two"] = ove_0_21.harass.ifs.qharass_blist[ove_0_36[slot_75_0.ptr] .. "hp"]:get()
				end

				if ove_0_171[ove_0_36[slot_75_0.ptr] .. "hp_two"] ~= ove_0_21.harass.ignore.qharass_blist[ove_0_36[slot_75_0.ptr] .. "hp"]:get() then
					ove_0_21.harass.ifs.qharass_blist[ove_0_36[slot_75_0.ptr] .. "hp"]:set("value", ove_0_21.harass.ignore.qharass_blist[ove_0_36[slot_75_0.ptr] .. "hp"]:get())

					ove_0_171[ove_0_36[slot_75_0.ptr] .. "hp_one"] = ove_0_21.harass.ignore.qharass_blist[ove_0_36[slot_75_0.ptr] .. "hp"]:get()
					ove_0_171[ove_0_36[slot_75_0.ptr] .. "hp_two"] = ove_0_21.harass.ignore.qharass_blist[ove_0_36[slot_75_0.ptr] .. "hp"]:get()
				end

				if ove_0_171[ove_0_36[slot_75_0.ptr] .. "block_one"] ~= ove_0_21.harass.ifs.qharass_blist[ove_0_36[slot_75_0.ptr] .. "block"]:get() then
					ove_0_21.harass.ignore.qharass_blist[ove_0_36[slot_75_0.ptr] .. "block"]:set("value", ove_0_21.harass.ifs.qharass_blist[ove_0_36[slot_75_0.ptr] .. "block"]:get())

					ove_0_171[ove_0_36[slot_75_0.ptr] .. "block_one"] = ove_0_21.harass.ifs.qharass_blist[ove_0_36[slot_75_0.ptr] .. "block"]:get()
					ove_0_171[ove_0_36[slot_75_0.ptr] .. "block_two"] = ove_0_21.harass.ifs.qharass_blist[ove_0_36[slot_75_0.ptr] .. "block"]:get()
				end

				if ove_0_171[ove_0_36[slot_75_0.ptr] .. "block_two"] ~= ove_0_21.harass.ignore.qharass_blist[ove_0_36[slot_75_0.ptr] .. "block"]:get() then
					ove_0_21.harass.ifs.qharass_blist[ove_0_36[slot_75_0.ptr] .. "block"]:set("value", ove_0_21.harass.ignore.qharass_blist[ove_0_36[slot_75_0.ptr] .. "block"]:get())

					ove_0_171[ove_0_36[slot_75_0.ptr] .. "block_one"] = ove_0_21.harass.ignore.qharass_blist[ove_0_36[slot_75_0.ptr] .. "block"]:get()
					ove_0_171[ove_0_36[slot_75_0.ptr] .. "block_two"] = ove_0_21.harass.ignore.qharass_blist[ove_0_36[slot_75_0.ptr] .. "block"]:get()
				end
			end

			if ove_0_21.autoq.ifs.autoq_blist[ove_0_36[slot_75_0.ptr] .. "hp"] and ove_0_21.autoq.ifs.autoq_blist[ove_0_36[slot_75_0.ptr] .. "block"] and ove_0_21.autoq.ignore.autoq_blist[ove_0_36[slot_75_0.ptr] .. "hp"] and ove_0_21.autoq.ignore.autoq_blist[ove_0_36[slot_75_0.ptr] .. "block"] then
				if ove_0_172[ove_0_36[slot_75_0.ptr] .. "hp_one"] ~= ove_0_21.autoq.ifs.autoq_blist[ove_0_36[slot_75_0.ptr] .. "hp"]:get() then
					ove_0_21.autoq.ignore.autoq_blist[ove_0_36[slot_75_0.ptr] .. "hp"]:set("value", ove_0_21.autoq.ifs.autoq_blist[ove_0_36[slot_75_0.ptr] .. "hp"]:get())

					ove_0_172[ove_0_36[slot_75_0.ptr] .. "hp_one"] = ove_0_21.autoq.ifs.autoq_blist[ove_0_36[slot_75_0.ptr] .. "hp"]:get()
					ove_0_172[ove_0_36[slot_75_0.ptr] .. "hp_two"] = ove_0_21.autoq.ifs.autoq_blist[ove_0_36[slot_75_0.ptr] .. "hp"]:get()
				end

				if ove_0_172[ove_0_36[slot_75_0.ptr] .. "hp_two"] ~= ove_0_21.autoq.ignore.autoq_blist[ove_0_36[slot_75_0.ptr] .. "hp"]:get() then
					ove_0_21.autoq.ifs.autoq_blist[ove_0_36[slot_75_0.ptr] .. "hp"]:set("value", ove_0_21.autoq.ignore.autoq_blist[ove_0_36[slot_75_0.ptr] .. "hp"]:get())

					ove_0_172[ove_0_36[slot_75_0.ptr] .. "hp_one"] = ove_0_21.autoq.ignore.autoq_blist[ove_0_36[slot_75_0.ptr] .. "hp"]:get()
					ove_0_172[ove_0_36[slot_75_0.ptr] .. "hp_two"] = ove_0_21.autoq.ignore.autoq_blist[ove_0_36[slot_75_0.ptr] .. "hp"]:get()
				end

				if ove_0_172[ove_0_36[slot_75_0.ptr] .. "block_one"] ~= ove_0_21.autoq.ifs.autoq_blist[ove_0_36[slot_75_0.ptr] .. "block"]:get() then
					ove_0_21.autoq.ignore.autoq_blist[ove_0_36[slot_75_0.ptr] .. "block"]:set("value", ove_0_21.autoq.ifs.autoq_blist[ove_0_36[slot_75_0.ptr] .. "block"]:get())

					ove_0_172[ove_0_36[slot_75_0.ptr] .. "block_one"] = ove_0_21.autoq.ifs.autoq_blist[ove_0_36[slot_75_0.ptr] .. "block"]:get()
					ove_0_172[ove_0_36[slot_75_0.ptr] .. "block_two"] = ove_0_21.autoq.ifs.autoq_blist[ove_0_36[slot_75_0.ptr] .. "block"]:get()
				end

				if ove_0_172[ove_0_36[slot_75_0.ptr] .. "block_two"] ~= ove_0_21.autoq.ignore.autoq_blist[ove_0_36[slot_75_0.ptr] .. "block"]:get() then
					ove_0_21.autoq.ifs.autoq_blist[ove_0_36[slot_75_0.ptr] .. "block"]:set("value", ove_0_21.autoq.ignore.autoq_blist[ove_0_36[slot_75_0.ptr] .. "block"]:get())

					ove_0_172[ove_0_36[slot_75_0.ptr] .. "block_one"] = ove_0_21.autoq.ignore.autoq_blist[ove_0_36[slot_75_0.ptr] .. "block"]:get()
					ove_0_172[ove_0_36[slot_75_0.ptr] .. "block_two"] = ove_0_21.autoq.ignore.autoq_blist[ove_0_36[slot_75_0.ptr] .. "block"]:get()
				end
			end
		end
	end
end

local function ove_0_176()
	-- function 76
	if not menu:isopen() then
		return
	end

	if ove_0_21.pick.pickup_combo:get() then
		ove_0_21.pick.pickup_combo_dis:set("visible", true)
	else
		ove_0_21.pick.pickup_combo_dis:set("visible", false)
	end

	if ove_0_21.rset.dont_r_dashers:get() then
		ove_0_21.rset.dont_r_dashers_flash:set("visible", true)
	else
		ove_0_21.rset.dont_r_dashers_flash:set("visible", false)
	end

	if ove_0_21.combo.qcombo:get() then
		ove_0_21.combo.qcombo_ext:set("visible", true)
		ove_0_21.combo.zzzzz:set("visible", true)
		ove_0_21.combo.qcombo_after_AA:set("visible", true)
		ove_0_21.combo.q_combo_mana:set("visible", true)

		if ove_0_21.combo.qcombo_ext:get() then
			ove_0_21.combo.q_slowpred:set("visible", true)
			ove_0_21.combo.ifs:set("visible", true)
			ove_0_21.combo.qcombo_minions:set("visible", true)

			if ove_0_21.combo.qcombo_minions:get() then
				ove_0_21.combo.qcombo_min_count:set("visible", true)
				ove_0_21.combo.ignore:set("visible", true)

				if ove_0_21.combo.ignore.heal:get() then
					ove_0_21.combo.ignore.qcombo_blist:set("visible", true)
				else
					ove_0_21.combo.ignore.qcombo_blist:set("visible", false)
				end

				if ove_0_21.combo.ignore.multi:get() then
					ove_0_21.combo.ignore.multi_count:set("visible", true)
				else
					ove_0_21.combo.ignore.multi_count:set("visible", false)
				end
			else
				ove_0_21.combo.qcombo_min_count:set("visible", false)
				ove_0_21.combo.ignore:set("visible", false)
			end

			if ove_0_21.combo.ifs.heal:get() then
				ove_0_21.combo.ifs.qcombo_blist:set("visible", true)
			else
				ove_0_21.combo.ifs.qcombo_blist:set("visible", false)
			end

			if ove_0_21.combo.ifs.multi:get() then
				ove_0_21.combo.ifs.multi_count:set("visible", true)
			else
				ove_0_21.combo.ifs.multi_count:set("visible", false)
			end
		else
			ove_0_21.combo.q_slowpred:set("visible", false)
			ove_0_21.combo.ifs:set("visible", false)
			ove_0_21.combo.qcombo_minions:set("visible", false)
			ove_0_21.combo.qcombo_min_count:set("visible", false)
			ove_0_21.combo.ignore:set("visible", false)
		end
	else
		ove_0_21.combo.qcombo_ext:set("visible", false)
		ove_0_21.combo.zzzzz:set("visible", false)
		ove_0_21.combo.qcombo_after_AA:set("visible", false)
		ove_0_21.combo.q_combo_mana:set("visible", false)
		ove_0_21.combo.q_slowpred:set("visible", false)
		ove_0_21.combo.ifs:set("visible", false)
		ove_0_21.combo.qcombo_minions:set("visible", false)
		ove_0_21.combo.qcombo_min_count:set("visible", false)
		ove_0_21.combo.ignore:set("visible", false)
	end

	if ove_0_21.harass.qharass:get() then
		ove_0_21.harass.qharass_ext:set("visible", true)
		ove_0_21.harass.zzzzz:set("visible", true)
		ove_0_21.harass.qharass_after_AA:set("visible", true)
		ove_0_21.harass.q_harass_mana:set("visible", true)

		if ove_0_21.harass.qharass_ext:get() then
			ove_0_21.harass.q_slowpred:set("visible", true)
			ove_0_21.harass.ifs:set("visible", true)
			ove_0_21.harass.qharass_minions:set("visible", true)

			if ove_0_21.harass.qharass_minions:get() then
				ove_0_21.harass.qharass_min_count:set("visible", true)
				ove_0_21.harass.ignore:set("visible", true)

				if ove_0_21.harass.ignore.heal:get() then
					ove_0_21.harass.ignore.qharass_blist:set("visible", true)
				else
					ove_0_21.harass.ignore.qharass_blist:set("visible", false)
				end

				if ove_0_21.harass.ignore.multi:get() then
					ove_0_21.harass.ignore.multi_count:set("visible", true)
				else
					ove_0_21.harass.ignore.multi_count:set("visible", false)
				end
			else
				ove_0_21.harass.qharass_min_count:set("visible", false)
				ove_0_21.harass.ignore:set("visible", false)
			end

			if ove_0_21.harass.ifs.heal:get() then
				ove_0_21.harass.ifs.qharass_blist:set("visible", true)
			else
				ove_0_21.harass.ifs.qharass_blist:set("visible", false)
			end

			if ove_0_21.harass.ifs.multi:get() then
				ove_0_21.harass.ifs.multi_count:set("visible", true)
			else
				ove_0_21.harass.ifs.multi_count:set("visible", false)
			end
		else
			ove_0_21.harass.q_slowpred:set("visible", false)
			ove_0_21.harass.ifs:set("visible", false)
			ove_0_21.harass.qharass_minions:set("visible", false)
			ove_0_21.harass.qharass_min_count:set("visible", false)
			ove_0_21.harass.ignore:set("visible", false)
		end
	else
		ove_0_21.harass.qharass_ext:set("visible", false)
		ove_0_21.harass.zzzzz:set("visible", false)
		ove_0_21.harass.qharass_after_AA:set("visible", false)
		ove_0_21.harass.q_harass_mana:set("visible", false)
		ove_0_21.harass.q_slowpred:set("visible", false)
		ove_0_21.harass.ifs:set("visible", false)
		ove_0_21.harass.qharass_minions:set("visible", false)
		ove_0_21.harass.qharass_min_count:set("visible", false)
		ove_0_21.harass.ignore:set("visible", false)
	end

	if ove_0_21.autoq.enable:get() then
		ove_0_21.autoq.pause_autoq:set("visible", true)
		ove_0_21.autoq.keys_autoq:set("visible", true)
		ove_0_21.autoq.q_slowpred:set("visible", true)
		ove_0_21.autoq.mana:set("visible", true)
		ove_0_21.autoq.aaa:set("visible", true)
		ove_0_21.autoq.ifs:set("visible", true)
		ove_0_21.autoq.autoq_minions:set("visible", true)

		if ove_0_21.autoq.autoq_minions:get() then
			ove_0_21.autoq.autoq_min_count:set("visible", true)
			ove_0_21.autoq.ignore:set("visible", true)

			if ove_0_21.autoq.ignore.heal:get() then
				ove_0_21.autoq.ignore.autoq_blist:set("visible", true)
			else
				ove_0_21.autoq.ignore.autoq_blist:set("visible", false)
			end

			if ove_0_21.autoq.ignore.multi:get() then
				ove_0_21.autoq.ignore.multi_count:set("visible", true)
			else
				ove_0_21.autoq.ignore.multi_count:set("visible", false)
			end
		else
			ove_0_21.autoq.autoq_min_count:set("visible", false)
			ove_0_21.autoq.ignore:set("visible", false)
		end

		if ove_0_21.autoq.ifs.heal:get() then
			ove_0_21.autoq.ifs.autoq_blist:set("visible", true)
		else
			ove_0_21.autoq.ifs.autoq_blist:set("visible", false)
		end

		if ove_0_21.autoq.ifs.multi:get() then
			ove_0_21.autoq.ifs.multi_count:set("visible", true)
		else
			ove_0_21.autoq.ifs.multi_count:set("visible", false)
		end
	else
		ove_0_21.autoq.pause_autoq:set("visible", false)
		ove_0_21.autoq.keys_autoq:set("visible", false)
		ove_0_21.autoq.q_slowpred:set("visible", false)
		ove_0_21.autoq.mana:set("visible", false)
		ove_0_21.autoq.aaa:set("visible", false)
		ove_0_21.autoq.ifs:set("visible", false)
		ove_0_21.autoq.autoq_minions:set("visible", false)
		ove_0_21.autoq.autoq_min_count:set("visible", false)
		ove_0_21.autoq.ignore:set("visible", false)
	end
end

local function ove_0_177(arg_77_0, arg_77_1, arg_77_2)
	-- function 77
	if arg_77_1.isZombie then
		return false
	end

	if ove_0_121(arg_77_1) <= arg_77_1.health then
		return false
	end

	local slot_77_0 = ove_0_24.linear.get_prediction(ove_0_45, arg_77_1)

	if not slot_77_0 then
		return false
	end

	if ove_0_123(arg_77_1) then
		return false
	end

	if not ove_0_133(slot_77_0, arg_77_1) then
		return false
	end

	arg_77_0.pos = slot_77_0.endPos
	arg_77_0.obj = arg_77_1

	return true
end

local function ove_0_178()
	-- function 78
	if not ove_0_21.rset.ks_r:get() then
		return
	end

	if ove_0_21.base.base_r_dis_ks:get() and ove_0_21.base.base_r_enable:get() and ove_0_78 then
		return
	end

	if player:spellSlot(3).state == 0 and ove_0_23.core.can_action() then
		local slot_78_0 = ove_0_25.get_result(ove_0_177)

		if slot_78_0.pos and slot_78_0.obj then
			local slot_78_1 = ove_0_40.range * ove_0_40.range
			local slot_78_2 = player.attackRange + player.boundingRadius + 70
			local slot_78_3 = slot_78_2 * slot_78_2
			local slot_78_4 = 90000

			if ove_0_21.misc.ks_q:get() and player:spellSlot(0).state == 0 and slot_78_1 >= slot_78_0.obj.pos2D:distSqr(player.pos2D) and ove_0_119(slot_78_0.obj) >= slot_78_0.obj.health then
				return
			end

			if slot_78_3 < slot_78_0.obj.pos2D:distSqr(player.pos2D) then
				if ove_0_21.rset.r_dont_ally:get() then
					if #ove_0_20.count_allies_in_range(slot_78_0.obj.pos2D, 1000, true) >= 1 then
						if ove_0_21.rset.r_dont_ally_allow:get() and #ove_0_20.count_low_allies_in_range(slot_78_0.obj.pos2D, 1000, 20) >= 1 then
							player:castSpell("pos", 3, vec3(slot_78_0.pos.x, slot_78_0.obj.pos.y, slot_78_0.pos.y))
						end
					else
						player:castSpell("pos", 3, vec3(slot_78_0.pos.x, slot_78_0.obj.pos.y, slot_78_0.pos.y))
					end
				else
					player:castSpell("pos", 3, vec3(slot_78_0.pos.x, slot_78_0.obj.pos.y, slot_78_0.pos.y))
				end
			end

			if player.health / player.maxHealth * 100 < 25 and slot_78_1 > slot_78_0.obj.pos2D:distSqr(player.pos2D) then
				player:castSpell("pos", 3, vec3(slot_78_0.pos.x, slot_78_0.obj.pos.y, slot_78_0.pos.y))
			end
		end
	end
end

local ove_0_179 = {
	[3864] = true,
	[3853] = true,
	[3851] = true,
	[3860] = true,
	[3855] = true,
	[3857] = true,
	[3863] = true,
	[3859] = true
}

local function ove_0_180()
	-- function 79
	if not ove_0_21.misc.ks_q:get() then
		return
	end

	if player:spellSlot(0).state ~= 0 then
		return
	end

	local slot_79_0 = ove_0_24.present.get_source_pos(player)

	if not slot_79_0 then
		return
	end

	local slot_79_1 = ove_0_40.range * ove_0_40.range

	for iter_79_0 = 0, objManager.enemies_n - 1 do
		local slot_79_2 = objManager.enemies[iter_79_0]

		if ove_0_20.IsValidTarget(slot_79_2) and not slot_79_2.isZombie and slot_79_1 >= slot_79_0:distSqr(slot_79_2.path.serverPos2D) and ove_0_119(slot_79_2) >= slot_79_2.health then
			local slot_79_3 = ove_0_24.linear.get_prediction(ove_0_42, slot_79_2)

			if slot_79_3 and slot_79_1 >= slot_79_3.startPos:distSqr(slot_79_3.endPos) then
				local slot_79_4 = ove_0_124(player, ove_0_40.range, ove_0_40.width + 40, slot_79_2)

				if slot_79_4 then
					player:castSpell("obj", 0, slot_79_4)
				elseif player:spellSlot(12).name:find("TrinketTotem") and player:spellSlot(12).state == 0 then
					local slot_79_5 = ove_0_24.core.get_pos_after_time(slot_79_2, 0.25)

					if slot_79_5 then
						local slot_79_6 = ove_0_20.VectorExtend(slot_79_0, slot_79_5, 550)

						if slot_79_6 and not navmesh.isWall(slot_79_6) and not navmesh.isStructure(slot_79_6) then
							player:castSpell("pos", 12, slot_79_6:to3D(player.pos.y))
						end
					end
				else
					for iter_79_1 = 0, 5 do
						if ove_0_179[player:itemID(iter_79_1)] and player:spellSlot(iter_79_1 + 6).state == 0 then
							local slot_79_7 = ove_0_24.core.get_pos_after_time(slot_79_2, 0.25)

							if slot_79_7 then
								local slot_79_8 = ove_0_20.VectorExtend(slot_79_0, slot_79_7, 550)

								if slot_79_8 and not navmesh.isWall(slot_79_8) and not navmesh.isStructure(slot_79_8) then
									player:castSpell("pos", iter_79_1 + 6, slot_79_8:to3D(player.pos.y))
								end
							end
						elseif player:itemID(iter_79_1) == 2055 and player:spellSlot(iter_79_1 + 6).state == 0 then
							local slot_79_9 = ove_0_24.core.get_pos_after_time(slot_79_2, 0.25)

							if slot_79_9 then
								local slot_79_10 = ove_0_20.VectorExtend(slot_79_0, slot_79_9, 550)

								if slot_79_10 and not navmesh.isWall(slot_79_10) and not navmesh.isStructure(slot_79_10) then
									player:castSpell("pos", iter_79_1 + 6, slot_79_10:to3D(player.pos.y))
								end
							end
						end
					end
				end
			end
		end
	end
end

local ove_0_181 = {
	ove_0_28.BUFF_ENUM_STUN,
	ove_0_28.BUFF_ENUM_TAUNT,
	ove_0_28.BUFF_ENUM_SNARE,
	ove_0_28.BUFF_ENUM_CHARM,
	ove_0_28.BUFF_ENUM_SUPPRESSION,
	ove_0_28.BUFF_ENUM_KNOCKUP,
	ove_0_28.BUFF_ENUM_ASLEEP
}

local function ove_0_182(arg_80_0)
	-- function 80
	local slot_80_0 = 1

	return ove_0_20.IsCustomLocked(ove_0_181, arg_80_0, slot_80_0)
end

local function ove_0_183()
	-- function 81
	local slot_81_0
	local slot_81_1 = {}
	local slot_81_2 = ove_0_21.rset.frRange:get() * ove_0_21.rset.frRange:get()
	local slot_81_3 = 0

	for iter_81_0 = 0, objManager.enemies_n - 1 do
		local slot_81_4 = objManager.enemies[iter_81_0]

		if ove_0_20.IsValidTarget(slot_81_4) and slot_81_2 >= player.pos2D:distSqr(slot_81_4.pos2D) and slot_81_4.health / slot_81_4.maxHealth * 100 <= ove_0_21.rset.frEhp:get() and ove_0_182(slot_81_4) then
			local slot_81_5 = ove_0_24.linear.get_prediction(ove_0_45, slot_81_4)

			if slot_81_5 then
				slot_81_1[slot_81_3] = slot_81_5.endPos
				slot_81_3 = slot_81_3 + 1
			end
		end
	end

	local slot_81_6, slot_81_7 = ove_0_20.CustomMEC(slot_81_1, slot_81_3)

	if slot_81_6 then
		slot_81_0 = slot_81_6
	end

	return slot_81_0
end

local function ove_0_184(arg_82_0, arg_82_1, arg_82_2)
	-- function 82
	local slot_82_0 = 0
	local slot_82_1 = ove_0_21.rset.frRange:get() * ove_0_21.rset.frRange:get()

	for iter_82_0 = 1, #arg_82_0 do
		local slot_82_2 = arg_82_0[iter_82_0]

		if slot_82_2 then
			local slot_82_3 = ove_0_24.linear.get_prediction(arg_82_1, slot_82_2)

			if slot_82_3 and slot_82_1 >= slot_82_3.startPos:distSqr(slot_82_3.endPos) then
				local slot_82_4 = arg_82_1.boundingRadiusMod == 1 and slot_82_2.boundingRadius or 0

				if ove_0_20.IsOnLineSegment(slot_82_3.endPos, slot_82_3.startPos, arg_82_2, arg_82_1.width + slot_82_4, arg_82_1.range) then
					slot_82_0 = slot_82_0 + 1
				end
			end
		end
	end

	return slot_82_0
end

local function ove_0_185()
	-- function 83
	local slot_83_0
	local slot_83_1 = {}
	local slot_83_2 = ove_0_21.rset.frRange:get() * ove_0_21.rset.frRange:get()
	local slot_83_3 = {}

	for iter_83_0 = 0, objManager.enemies_n - 1 do
		local slot_83_4 = objManager.enemies[iter_83_0]

		if ove_0_20.IsValidTarget(slot_83_4) and slot_83_2 >= player.pos2D:distSqr(slot_83_4.pos2D) and slot_83_4.health / slot_83_4.maxHealth * 100 <= ove_0_21.rset.frEhp:get() and ove_0_182(slot_83_4) then
			slot_83_3[#slot_83_3 + 1] = slot_83_4
		end
	end

	for iter_83_1 = 1, #slot_83_3 do
		local slot_83_5 = slot_83_3[iter_83_1]

		for iter_83_2 = 1, #slot_83_3 do
			local slot_83_6 = slot_83_3[iter_83_2 + 1]

			if slot_83_5 and slot_83_6 and slot_83_5.ptr ~= slot_83_6.ptr then
				local slot_83_7 = ove_0_24.linear.get_prediction(ove_0_45, slot_83_5)
				local slot_83_8 = ove_0_24.linear.get_prediction(ove_0_45, slot_83_6)

				if slot_83_7 and slot_83_8 and slot_83_2 >= slot_83_7.startPos:distSqr(slot_83_7.endPos) and slot_83_2 >= slot_83_8.startPos:distSqr(slot_83_8.endPos) then
					local slot_83_9 = (slot_83_7.endPos + slot_83_8.endPos) * 0.5

					slot_83_1[#slot_83_1 + 1] = slot_83_9
				end
			end
		end
	end

	local slot_83_10 = 0
	local slot_83_11 = ove_0_21.rset.frCount:get()

	for iter_83_3 = 1, #slot_83_1 do
		local slot_83_12 = slot_83_1[iter_83_3]

		if slot_83_12 then
			local slot_83_13 = ove_0_184(slot_83_3, ove_0_45, slot_83_12)

			if slot_83_11 <= slot_83_13 then
				if slot_83_10 == 0 then
					slot_83_0 = slot_83_12
				elseif slot_83_10 < slot_83_13 then
					slot_83_0 = slot_83_12
				end
			end
		end
	end

	return slot_83_0
end

local function ove_0_186()
	-- function 84
	local slot_84_0
	local slot_84_1 = {}
	local slot_84_2 = ove_0_21.rset.frRange:get() * ove_0_21.rset.frRange:get()
	local slot_84_3 = {}

	for iter_84_0 = 0, objManager.enemies_n - 1 do
		local slot_84_4 = objManager.enemies[iter_84_0]

		if ove_0_20.IsValidTarget(slot_84_4) and slot_84_2 >= player.pos2D:distSqr(slot_84_4.pos2D) and slot_84_4.health / slot_84_4.maxHealth * 100 <= ove_0_21.rset.frEhp:get() and ove_0_182(slot_84_4) then
			slot_84_3[#slot_84_3 + 1] = slot_84_4
		end
	end

	local slot_84_5 = 0
	local slot_84_6 = ove_0_21.rset.frCount:get()

	for iter_84_1 = 1, #slot_84_3 do
		local slot_84_7 = slot_84_3[iter_84_1]

		if slot_84_7 then
			local slot_84_8 = ove_0_24.linear.get_prediction(ove_0_45, slot_84_7)

			if slot_84_8 and slot_84_2 >= slot_84_8.startPos:distSqr(slot_84_8.endPos) then
				local slot_84_9 = ove_0_184(slot_84_3, ove_0_45, slot_84_8.endPos)

				if slot_84_6 <= slot_84_9 then
					if slot_84_5 == 0 then
						slot_84_0 = slot_84_8.endPos
					elseif slot_84_5 < slot_84_9 then
						slot_84_0 = slot_84_8.endPos
					end
				end
			end
		end
	end

	return slot_84_0
end

local function ove_0_187(arg_85_0, arg_85_1)
	-- function 85
	local slot_85_0 = 0
	local slot_85_1 = ove_0_21.rset.frRange:get() * ove_0_21.rset.frRange:get()

	for iter_85_0 = 0, objManager.enemies_n - 1 do
		local slot_85_2 = objManager.enemies[iter_85_0]

		if ove_0_20.IsValidTarget(slot_85_2) and slot_85_1 >= player.pos2D:distSqr(slot_85_2.pos2D) and slot_85_2.health / slot_85_2.maxHealth * 100 <= ove_0_21.rset.frEhp:get() and ove_0_182(slot_85_2) then
			local slot_85_3 = ove_0_24.linear.get_prediction(arg_85_0, slot_85_2)

			if slot_85_3 and slot_85_1 >= slot_85_3.startPos:distSqr(slot_85_3.endPos) then
				local slot_85_4 = arg_85_0.boundingRadiusMod == 1 and slot_85_2.boundingRadius or 0

				if ove_0_20.IsOnLineSegment(slot_85_3.endPos, slot_85_3.startPos, arg_85_1, arg_85_0.width + slot_85_4, arg_85_0.range) then
					slot_85_0 = slot_85_0 + 1
				end
			end
		end
	end

	return slot_85_0
end

local function ove_0_188()
	-- function 86
	local slot_86_0 = ove_0_21.rset.frRange:get() * ove_0_21.rset.frRange:get()

	for iter_86_0 = 0, objManager.enemies_n - 1 do
		local slot_86_1 = objManager.enemies[iter_86_0]

		if ove_0_20.IsValidTarget(slot_86_1) and slot_86_0 >= player.pos2D:distSqr(slot_86_1.pos2D) and slot_86_1.health / slot_86_1.maxHealth * 100 <= ove_0_21.rset.frEhp:get() and ove_0_182(slot_86_1) then
			return true
		end
	end

	return false
end

local function ove_0_189()
	-- function 87
	if not ove_0_21.rset.frEnable:get() then
		return
	end

	if ove_0_20.IsRecalling(player) then
		return
	end

	if player:spellSlot(3).state ~= 0 then
		return
	end

	if ove_0_21.rset.frENearby:get() and #ove_0_20.count_enemies_in_range(player.pos2D, 850) >= 1 then
		return
	end

	if not ove_0_188() then
		return
	end

	local slot_87_0 = ove_0_183()
	local slot_87_1 = ove_0_185()
	local slot_87_2 = ove_0_186()
	local slot_87_3 = {}

	if slot_87_0 then
		slot_87_3[#slot_87_3 + 1] = slot_87_0
	end

	if slot_87_1 then
		slot_87_3[#slot_87_3 + 1] = slot_87_1
	end

	if slot_87_2 then
		slot_87_3[#slot_87_3 + 1] = slot_87_2
	end

	local slot_87_4 = 0
	local slot_87_5
	local slot_87_6 = ove_0_21.rset.frCount:get()

	for iter_87_0 = 1, #slot_87_3 do
		local slot_87_7 = slot_87_3[iter_87_0]

		if slot_87_7 then
			local slot_87_8 = ove_0_187(ove_0_45, slot_87_7)

			if slot_87_6 <= slot_87_8 then
				if slot_87_4 == 0 then
					slot_87_5 = slot_87_7
					slot_87_4 = slot_87_8
				elseif slot_87_4 < slot_87_8 then
					slot_87_5 = slot_87_7
					slot_87_4 = slot_87_8
				end
			end
		end
	end

	if slot_87_5 and slot_87_6 <= slot_87_4 then
		player:castSpell("pos", 3, slot_87_5:to3D(player.pos.y))
	end
end

local ove_0_190 = 0

local function ove_0_191()
	-- function 88
	if ove_0_190 < game.time then
		for iter_88_0, iter_88_1 in pairs(ove_0_81) do
			if iter_88_1 then
				if iter_88_1.ptr == 0 then
					if ove_0_82[iter_88_1.ptr] then
						ove_0_82[iter_88_1.ptr] = nil
					end

					ove_0_81[iter_88_1.ptr] = nil
				end

				if iter_88_1.ptr ~= 0 and iter_88_1.isDead then
					if ove_0_82[iter_88_1.ptr] then
						ove_0_82[iter_88_1.ptr] = nil
					end

					ove_0_81[iter_88_1.ptr] = nil
				end
			end
		end

		for iter_88_2, iter_88_3 in pairs(ove_0_83) do
			if iter_88_3 then
				if iter_88_3.ptr == 0 then
					ove_0_83[iter_88_3.ptr] = nil
				end

				if iter_88_3.ptr ~= 0 and iter_88_3.isDead then
					ove_0_83[iter_88_3.ptr] = nil
				end
			end
		end

		ove_0_190 = game.time + 0.5
	end
end

local function ove_0_192()
	-- function 89
	ove_0_100 = ove_0_90()

	ove_0_191()

	ove_0_43.range = ove_0_21.combo.w_custom_range:get()
	ove_0_42.delay = ove_0_100

	ove_0_70()
	ove_0_72()
	ove_0_79()
	ove_0_57()
	ove_0_134()
	ove_0_180()
	ove_0_178()
	ove_0_189()
	ove_0_141()
	ove_0_128()

	if ove_0_21.keys.combokey:get() then
		ove_0_151()
	end

	if ove_0_21.keys.harasskey:get() then
		ove_0_157()
	end

	ove_0_165()

	if ove_0_21.keys.clearkey:get() then
		ove_0_168()
		ove_0_169()
	end

	ove_0_162()
	ove_0_132()
	ove_0_126()
	ove_0_175()
	ove_0_176()

	if menu:isopen() then
		ove_0_20.MenuHeaderWizard(ove_0_21)
	end
end

local ove_0_193 = 0

local function ove_0_194(arg_90_0)
	-- function 90
	if arg_90_0.isVisible and not arg_90_0.isDead and not arg_90_0.isZombie and arg_90_0.isOnScreen then
		local slot_90_0 = arg_90_0.health + arg_90_0.allShield + arg_90_0.physicalShield + arg_90_0.healthRegenRate * 1.31
		local slot_90_1, slot_90_2, slot_90_3 = ove_0_20.GetBarData()
		local slot_90_4 = arg_90_0.barPos
		local slot_90_5 = slot_90_4.x + slot_90_1.x
		local slot_90_6 = slot_90_4.y + slot_90_1.y

		ove_0_193 = ove_0_121(arg_90_0)

		local slot_90_7 = ove_0_193
		local slot_90_8 = arg_90_0.health - ove_0_193
		local slot_90_9 = slot_90_5
		local slot_90_10 = slot_90_5 + (slot_90_7 > 0 and slot_90_7 or 0) / arg_90_0.maxHealth * slot_90_2
		local slot_90_11 = slot_90_5 + arg_90_0.health / arg_90_0.maxHealth * slot_90_2
		local slot_90_12 = slot_90_5 + (slot_90_8 > 0 and slot_90_8 or 0) / arg_90_0.maxHealth * slot_90_2
		local slot_90_13 = slot_90_5 + slot_90_2

		if ove_0_21.draws.rdraw.drawdmgonhealthbar:get() then
			if ove_0_193 <= arg_90_0.health then
				graphics.draw_line_2D(slot_90_9, slot_90_6, slot_90_10, slot_90_6, slot_90_3, ove_0_21.draws.rdraw.colordmghealthbar:get())
			else
				graphics.draw_line_2D(slot_90_11, slot_90_6, slot_90_12, slot_90_6, slot_90_3, ove_0_21.draws.rdraw.colordmghealthbarK:get())
			end
		end

		if ove_0_21.draws.rdraw.drawfulldmgindicator:get() then
			if ove_0_193 > arg_90_0.maxHealth then
				graphics.draw_text_2D(tostring("R"), 20, slot_90_13 - 6, slot_90_6 - 35, ove_0_21.draws.rdraw.colorRindicator:get())
				graphics.draw_line_2D(slot_90_13 - 2, slot_90_6 - 7, slot_90_13, slot_90_6 - 7, 28, ove_0_21.draws.rdraw.colorCutindicator:get())
			else
				graphics.draw_text_2D(tostring("R"), 20, slot_90_10 - 6, slot_90_6 - 35, ove_0_21.draws.rdraw.colorRindicator:get())
				graphics.draw_line_2D(slot_90_10 - 2, slot_90_6 - 7, slot_90_10, slot_90_6 - 7, 28, ove_0_21.draws.rdraw.colorCutindicator:get())
			end
		end

		local slot_90_14 = graphics.world_to_screen(arg_90_0.pos)
		local slot_90_15 = arg_90_0.boundingRadius / 10
		local slot_90_16 = 0
		local slot_90_17 = 0

		if hanbot.language == 1 then
			if string.len(tostring(math.floor(ove_0_193))) >= 4 then
				slot_90_16 = 20
				slot_90_17 = 3
			end

			if string.len(tostring(math.floor(ove_0_193))) == 3 then
				slot_90_16 = 20
				slot_90_17 = 3
			end

			if string.len(tostring(math.floor(ove_0_193))) == 2 then
				slot_90_16 = 10
				slot_90_17 = 2
			end

			if string.len(tostring(math.floor(ove_0_193))) <= 1 then
				slot_90_16 = 0
				slot_90_17 = 4
			end
		end

		if hanbot.language == 2 then
			if graphics.get_font() == "Courier New" then
				if string.len(tostring(math.floor(ove_0_193))) >= 4 then
					slot_90_16 = 0
					slot_90_17 = -1
				end

				if string.len(tostring(math.floor(ove_0_193))) == 3 then
					slot_90_16 = 5
					slot_90_17 = 2
				end

				if string.len(tostring(math.floor(ove_0_193))) == 2 then
					slot_90_16 = 0
					slot_90_17 = 2
				end

				if string.len(tostring(math.floor(ove_0_193))) <= 1 then
					slot_90_16 = -15
					slot_90_17 = 0
				end
			end

			if graphics.get_font() == "Lucida Sans Unicode" then
				if string.len(tostring(math.floor(ove_0_193))) >= 4 then
					slot_90_16 = -10
					slot_90_17 = 0
				end

				if string.len(tostring(math.floor(ove_0_193))) == 3 then
					slot_90_16 = -10
					slot_90_17 = 0
				end

				if string.len(tostring(math.floor(ove_0_193))) == 2 then
					slot_90_16 = -10
					slot_90_17 = 5
				end

				if string.len(tostring(math.floor(ove_0_193))) <= 1 then
					slot_90_16 = -20
					slot_90_17 = 5
				end
			end

			if graphics.get_font() == "Gill Sans MT Pro Medium" then
				if string.len(tostring(math.floor(ove_0_193))) >= 4 then
					slot_90_16 = -25
					slot_90_17 = 3
				end

				if string.len(tostring(math.floor(ove_0_193))) == 3 then
					slot_90_16 = -25
					slot_90_17 = 2.5
				end

				if string.len(tostring(math.floor(ove_0_193))) == 2 then
					slot_90_16 = -30
					slot_90_17 = 2.5
				end

				if string.len(tostring(math.floor(ove_0_193))) <= 1 then
					slot_90_16 = -30
					slot_90_17 = 7
				end
			end
		end

		if ove_0_21.draws.rdraw.drawkillableindicator:get() and ove_0_193 > arg_90_0.health then
			if string.len(tostring(math.floor(ove_0_193))) <= 3 then
				graphics.draw_line_2D(slot_90_14.x + 5, slot_90_14.y - 45, slot_90_14.x + 30 + slot_90_15, slot_90_14.y - 80, 1, ove_0_21.draws.rdraw.colorindiLines:get())
				graphics.draw_line_2D(slot_90_14.x + 30 + slot_90_15, slot_90_14.y - 80, slot_90_14.x + 50 + slot_90_15, slot_90_14.y - 80, 1, ove_0_21.draws.rdraw.colorindiLines:get())
				graphics.draw_line_2D(slot_90_14.x + 65 + slot_90_15, slot_90_14.y - 95, slot_90_14.x + 50 + slot_90_15, slot_90_14.y - 80, 1, ove_0_21.draws.rdraw.colorindiLines:get())
				graphics.draw_line_2D(slot_90_14.x + 65 + slot_90_15, slot_90_14.y - 65, slot_90_14.x + 50 + slot_90_15, slot_90_14.y - 80, 1, ove_0_21.draws.rdraw.colorindiLines:get())
				graphics.draw_line_2D(slot_90_14.x + 65 + slot_90_15, slot_90_14.y - 65, slot_90_14.x + 180 + slot_90_15 + slot_90_16, slot_90_14.y - 65, 1, ove_0_21.draws.rdraw.colorindiLines:get())
				graphics.draw_line_2D(slot_90_14.x + 65 + slot_90_15, slot_90_14.y - 95, slot_90_14.x + 180 + slot_90_15 + slot_90_16, slot_90_14.y - 95, 1, ove_0_21.draws.rdraw.colorindiLines:get())
				graphics.draw_line_2D(slot_90_14.x + 180 + slot_90_15 + slot_90_16, slot_90_14.y - 95, slot_90_14.x + 195 + slot_90_15 + slot_90_16, slot_90_14.y - 80, 1, ove_0_21.draws.rdraw.colorindiLines:get())
				graphics.draw_line_2D(slot_90_14.x + 180 + slot_90_15 + slot_90_16, slot_90_14.y - 65, slot_90_14.x + 195 + slot_90_15 + slot_90_16, slot_90_14.y - 80, 1, ove_0_21.draws.rdraw.colorindiLines:get())
			else
				graphics.draw_line_2D(slot_90_14.x + 5, slot_90_14.y - 45, slot_90_14.x + 30 + slot_90_15, slot_90_14.y - 80, 1, ove_0_21.draws.rdraw.colorindiLines:get())
				graphics.draw_line_2D(slot_90_14.x + 30 + slot_90_15, slot_90_14.y - 80, slot_90_14.x + 50 + slot_90_15, slot_90_14.y - 80, 1, ove_0_21.draws.rdraw.colorindiLines:get())
				graphics.draw_line_2D(slot_90_14.x + 65 + slot_90_15, slot_90_14.y - 95, slot_90_14.x + 50 + slot_90_15, slot_90_14.y - 80, 1, ove_0_21.draws.rdraw.colorindiLines:get())
				graphics.draw_line_2D(slot_90_14.x + 65 + slot_90_15, slot_90_14.y - 65, slot_90_14.x + 50 + slot_90_15, slot_90_14.y - 80, 1, ove_0_21.draws.rdraw.colorindiLines:get())
				graphics.draw_line_2D(slot_90_14.x + 65 + slot_90_15, slot_90_14.y - 65, slot_90_14.x + 190 + slot_90_15 + slot_90_16, slot_90_14.y - 65, 1, ove_0_21.draws.rdraw.colorindiLines:get())
				graphics.draw_line_2D(slot_90_14.x + 65 + slot_90_15, slot_90_14.y - 95, slot_90_14.x + 190 + slot_90_15 + slot_90_16, slot_90_14.y - 95, 1, ove_0_21.draws.rdraw.colorindiLines:get())
				graphics.draw_line_2D(slot_90_14.x + 190 + slot_90_15 + slot_90_16, slot_90_14.y - 95, slot_90_14.x + 205 + slot_90_15 + slot_90_16, slot_90_14.y - 80, 1, ove_0_21.draws.rdraw.colorindiLines:get())
				graphics.draw_line_2D(slot_90_14.x + 190 + slot_90_15 + slot_90_16, slot_90_14.y - 65, slot_90_14.x + 205 + slot_90_15 + slot_90_16, slot_90_14.y - 80, 1, ove_0_21.draws.rdraw.colorindiLines:get())
			end

			graphics.draw_text_2D(tostring(math.floor(ove_0_193)) .. " Killable", 20, slot_90_14.x + 65 + slot_90_15 + slot_90_17, slot_90_14.y - 80, ove_0_21.draws.rdraw.colorindiText:get())
		end
	end
end

local function ove_0_195()
	-- function 91
	if not ove_0_60 then
		return
	end

	if not ove_0_21.base.base_r_enable:get() then
		return
	end

	if player:spellSlot(3).state ~= 0 and ove_0_73 < game.time then
		return
	end

	local slot_91_0 = 0
	local slot_91_1 = ove_0_62()

	if slot_91_1 then
		for iter_91_0 = 0, objManager.enemies_n - 1 do
			local slot_91_2 = objManager.enemies[iter_91_0]

			if slot_91_2 and ove_0_74[slot_91_2.ptr] then
				local slot_91_3 = ove_0_71[slot_91_2.ptr]

				if slot_91_3 and slot_91_3.recall and slot_91_3.start and slot_91_3.time and slot_91_3.recall_total_time then
					local slot_91_4 = slot_91_3.recall_total_time
					local slot_91_5 = (game.time - slot_91_3.start) / slot_91_4
					local slot_91_6 = slot_91_3.start + slot_91_4 * 1000 / 1000
					local slot_91_7 = graphics.width * 0.5
					local slot_91_8 = graphics.height * 0.5
					local slot_91_9 = graphics.height * 0.5
					local slot_91_10 = graphics.height * 0.5 - (slot_91_9 - 100)
					local slot_91_11, slot_91_12 = graphics.text_area(slot_91_2.charName .. ": ", 25)
					local slot_91_13 = slot_91_11 * 0.5 + 30

					graphics.draw_line_2D(slot_91_7 - 150, slot_91_10 + slot_91_0, slot_91_7 + (slot_91_3.start + slot_91_3.time - game.time) / slot_91_4 * 150, slot_91_10 + slot_91_0, 30, graphics.argb(180, 150, 255, 200))
					graphics.draw_line_2D(slot_91_7 - 155, slot_91_10 + slot_91_0, slot_91_7 + 155, slot_91_10 + slot_91_0, 40, graphics.argb(100, 105, 125, 255))
					graphics.draw_line_2D(slot_91_7 + (slot_91_3.start + slot_91_1 * 2 - slot_91_6) / slot_91_4 * 150 + 5, slot_91_10 + slot_91_0, slot_91_7 + (slot_91_3.start + slot_91_1 * 2 - slot_91_6) / slot_91_4 * 150, slot_91_10 + slot_91_0, 45, graphics.argb(255, 255, 18, 50))
					graphics.draw_text_2D("R", 25, slot_91_7 + (slot_91_3.start + slot_91_1 * 2 - slot_91_6) / slot_91_4 * 150 - 3, slot_91_10 - 35 + slot_91_0, graphics.argb(255, 150, 255, 255))
					graphics.draw_text_2D(slot_91_2.charName .. ": ", 25, slot_91_7 - slot_91_13, slot_91_10 + slot_91_0, graphics.argb(255, 255, 255, 255))
					graphics.draw_text_2D(string.format("%.2f", slot_91_6 - game.time), 25, slot_91_7 + (slot_91_13 - 50), slot_91_10 + slot_91_0, graphics.argb(255, 255, 255, 255))

					slot_91_0 = slot_91_0 + 80
				end
			end
		end
	end

	if ove_0_77 and ove_0_78 then
		local slot_91_14 = graphics.width * 0.5
		local slot_91_15 = graphics.height * 0.5
		local slot_91_16 = slot_91_15 - (slot_91_15 - 100)
		local slot_91_17 = "Yasuo W is blocking base ult!"
		local slot_91_18 = 20
		local slot_91_19, slot_91_20 = graphics.text_area(slot_91_17, slot_91_18)
		local slot_91_21 = slot_91_19 * 0.5
		local slot_91_22 = graphics.argb(255, 0, 0, 0)
		local slot_91_23 = slot_91_16 + 32
		local slot_91_24 = slot_91_14 - slot_91_21

		graphics.draw_text_2D(slot_91_17, slot_91_18, slot_91_24, slot_91_23 - 1, slot_91_22)
		graphics.draw_text_2D(slot_91_17, slot_91_18, slot_91_24, slot_91_23 + 1, slot_91_22)
		graphics.draw_text_2D(slot_91_17, slot_91_18, slot_91_24 - 1, slot_91_23, slot_91_22)
		graphics.draw_text_2D(slot_91_17, slot_91_18, slot_91_24 + 1, slot_91_23, slot_91_22)
		graphics.draw_text_2D(slot_91_17, slot_91_18, slot_91_24, slot_91_23, graphics.argb(255, 255, 0, 0))
	end
end

local function ove_0_196()
	-- function 92
	local slot_92_0 = 0

	if player:spellSlot(3).state ~= 0 then
		return
	end

	if ove_0_21.draws.rdraw.drawNotification:get() and ove_0_21.rset.ks_r:get() then
		local slot_92_1 = graphics.world_to_screen(vec3(player.x, player.y, player.z))

		for iter_92_0 = 0, objManager.enemies_n - 1 do
			local slot_92_2 = objManager.enemies[iter_92_0]

			if ove_0_20.IsValidTarget(slot_92_2) and not slot_92_2.isZombie and ove_0_121(slot_92_2) > slot_92_2.health then
				local slot_92_3 = tostring(ove_0_32[slot_92_2.ptr] .. " is killable!")
				local slot_92_4, slot_92_5 = graphics.text_area(slot_92_3, 25)
				local slot_92_6 = slot_92_4 * 0.5

				if ove_0_21.rset.dont_r_dashers:get() then
					if not ove_0_123(slot_92_2) then
						graphics.draw_text_2D(slot_92_3, 25, slot_92_1.x - slot_92_6, slot_92_1.y - 250 - 35 * slot_92_0, ove_0_21.draws.rdraw.r_note_color:get())

						slot_92_0 = slot_92_0 + 1
					else
						graphics.draw_text_2D(slot_92_3, 25, slot_92_1.x - slot_92_6, slot_92_1.y - 250 - 35 * slot_92_0, ove_0_21.draws.rdraw.r_note_color2:get())

						slot_92_0 = slot_92_0 + 1
					end
				else
					graphics.draw_text_2D(slot_92_3, 25, slot_92_1.x - slot_92_6, slot_92_1.y - 250 - 35 * slot_92_0, ove_0_21.draws.rdraw.r_note_color:get())

					slot_92_0 = slot_92_0 + 1
				end
			end
		end
	end
end

local function ove_0_197()
	-- function 93
	if not ove_0_26.enable() then
		return
	end

	local slot_93_0 = {}

	if not ove_0_21.autoq.enable:get() then
		slot_93_0 = {
			{
				position = 0.35,
				name = "Farming:",
				menu = ove_0_21.farminggg.farmtog
			},
			{
				position = 0.1,
				name = "Healing:",
				menu = ove_0_21.wpriority.healTog
			},
			{
				position = -0.15,
				name = "Force Heal:",
				menu = ove_0_21.wpriority.healTogForce
			},
			{
				position = -0.4,
				name = "Force R:",
				menu = ove_0_21.rset.Force_R_key,
				other_activator = ove_0_55
			}
		}
	else
		slot_93_0 = {
			{
				position = 0.4,
				name = "Auto Q:",
				menu = ove_0_21.autoq.keys_autoq
			},
			{
				position = 0.2,
				name = "Farming:",
				menu = ove_0_21.farminggg.farmtog
			},
			{
				position = 0,
				name = "Healing:",
				menu = ove_0_21.wpriority.healTog
			},
			{
				position = -0.2,
				name = "Force Heal:",
				menu = ove_0_21.wpriority.healTogForce
			},
			{
				position = -0.4,
				name = "Force R:",
				menu = ove_0_21.rset.Force_R_key,
				other_activator = ove_0_55
			}
		}
	end

	ove_0_26.draw(slot_93_0)
end

local function ove_0_198()
	-- function 94
	local slot_94_0 = 0
	local slot_94_1 = 0

	if hanbot.language == 1 then
		slot_94_0 = 10
		slot_94_1 = 3
	end

	if hanbot.language == 2 then
		if graphics.get_font() == "Courier New" then
			slot_94_0 = 0
			slot_94_1 = 0
		end

		if graphics.get_font() == "Lucida Sans Unicode" then
			slot_94_0 = 0
			slot_94_1 = 0
		end

		if graphics.get_font() == "Gill Sans MT Pro Medium" then
			slot_94_0 = -10
			slot_94_1 = -1
		end
	end

	for iter_94_0, iter_94_1 in pairs(ove_0_81) do
		if iter_94_1 and iter_94_1.ptr ~= 0 and iter_94_1.isTargetable and not iter_94_1.isDead and iter_94_1.health >= 1 and iter_94_1.isOnScreen and ove_0_82[iter_94_1.ptr] then
			graphics.draw_circle(iter_94_1.pos, 30, 2, ove_0_21.draws.soulsdraw.ColorCircle:get(), 4)

			local slot_94_2 = graphics.world_to_screen(vec3(iter_94_1.x, iter_94_1.y, iter_94_1.z))

			if hanbot.language == 1 then
				graphics.draw_line_2D(slot_94_2.x + 2.5, slot_94_2.y - 22.5, slot_94_2.x + 15, slot_94_2.y - 40, 1, ove_0_21.draws.soulsdraw.ColorFrame:get())
				graphics.draw_line_2D(slot_94_2.x + 15, slot_94_2.y - 40, slot_94_2.x + 25, slot_94_2.y - 40, 1, ove_0_21.draws.soulsdraw.ColorFrame:get())
				graphics.draw_line_2D(slot_94_2.x + 32.5, slot_94_2.y - 49.5, slot_94_2.x + 25, slot_94_2.y - 40, 1, ove_0_21.draws.soulsdraw.ColorFrame:get())
				graphics.draw_line_2D(slot_94_2.x + 32.5, slot_94_2.y - 30.5, slot_94_2.x + 25, slot_94_2.y - 40, 1, ove_0_21.draws.soulsdraw.ColorFrame:get())
				graphics.draw_line_2D(slot_94_2.x + 32.5, slot_94_2.y - 30.5, slot_94_2.x + 90 + slot_94_0, slot_94_2.y - 30.5, 1, ove_0_21.draws.soulsdraw.ColorFrame:get())
				graphics.draw_line_2D(slot_94_2.x + 32.5, slot_94_2.y - 49.5, slot_94_2.x + 90 + slot_94_0, slot_94_2.y - 49.5, 1, ove_0_21.draws.soulsdraw.ColorFrame:get())
				graphics.draw_line_2D(slot_94_2.x + 90 + slot_94_0, slot_94_2.y - 49.5, slot_94_2.x + 97.5 + slot_94_0, slot_94_2.y - 40, 1, ove_0_21.draws.soulsdraw.ColorFrame:get())
				graphics.draw_line_2D(slot_94_2.x + 90 + slot_94_0, slot_94_2.y - 30.5, slot_94_2.x + 97.5 + slot_94_0, slot_94_2.y - 40, 1, ove_0_21.draws.soulsdraw.ColorFrame:get())

				if ove_0_82[iter_94_1.ptr] - os.clock() > 0 then
					graphics.draw_text_2D(string.format("%.2f", ove_0_82[iter_94_1.ptr] - os.clock()), 17, slot_94_2.x + 45 + slot_94_1, slot_94_2.y - 40, ove_0_21.draws.soulsdraw.ColorText:get())
				else
					graphics.draw_text_2D("0.00", 17, slot_94_2.x + 45 + slot_94_1, slot_94_2.y - 40, ove_0_21.draws.soulsdraw.ColorText:get())
				end
			else
				graphics.draw_line_2D(slot_94_2.x + 2.5, slot_94_2.y - 22.5, slot_94_2.x + 15, slot_94_2.y - 40, 1, ove_0_21.draws.soulsdraw.ColorFrame:get())
				graphics.draw_line_2D(slot_94_2.x + 15, slot_94_2.y - 40, slot_94_2.x + 25, slot_94_2.y - 40, 1, ove_0_21.draws.soulsdraw.ColorFrame:get())
				graphics.draw_line_2D(slot_94_2.x + 32.5, slot_94_2.y - 47.5, slot_94_2.x + 25, slot_94_2.y - 40, 1, ove_0_21.draws.soulsdraw.ColorFrame:get())
				graphics.draw_line_2D(slot_94_2.x + 32.5, slot_94_2.y - 32.5, slot_94_2.x + 25, slot_94_2.y - 40, 1, ove_0_21.draws.soulsdraw.ColorFrame:get())
				graphics.draw_line_2D(slot_94_2.x + 32.5, slot_94_2.y - 32.5, slot_94_2.x + 90 + slot_94_0, slot_94_2.y - 32.5, 1, ove_0_21.draws.soulsdraw.ColorFrame:get())
				graphics.draw_line_2D(slot_94_2.x + 32.5, slot_94_2.y - 47.5, slot_94_2.x + 90 + slot_94_0, slot_94_2.y - 47.5, 1, ove_0_21.draws.soulsdraw.ColorFrame:get())
				graphics.draw_line_2D(slot_94_2.x + 90 + slot_94_0, slot_94_2.y - 47.5, slot_94_2.x + 97.5 + slot_94_0, slot_94_2.y - 40, 1, ove_0_21.draws.soulsdraw.ColorFrame:get())
				graphics.draw_line_2D(slot_94_2.x + 90 + slot_94_0, slot_94_2.y - 32.5, slot_94_2.x + 97.5 + slot_94_0, slot_94_2.y - 40, 1, ove_0_21.draws.soulsdraw.ColorFrame:get())

				if ove_0_82[iter_94_1.ptr] - os.clock() > 0 then
					graphics.draw_text_2D(string.format("%.2f", ove_0_82[iter_94_1.ptr] - os.clock()), 17, slot_94_2.x + 45 + slot_94_1, slot_94_2.y - 39, ove_0_21.draws.soulsdraw.ColorText:get())
				else
					graphics.draw_text_2D("0.00", 17, slot_94_2.x + 45 + slot_94_1, slot_94_2.y - 39, ove_0_21.draws.soulsdraw.ColorText:get())
				end
			end
		end
	end
end

local function ove_0_199()
	-- function 95
	if not player.isOnScreen then
		return
	end

	local slot_95_0 = graphics.world_to_screen(vec3(player.x, player.y, player.z))

	if not ove_0_26.enable() then
		if ove_0_21.draws.drawtogglefarm:get() and ove_0_21.farminggg.enable_farm:get() then
			if not ove_0_21.farminggg.farmtog:get() then
				graphics.draw_text_2D("Farm: OFF", ove_0_21.draws.farmtog.RejSize:get(), slot_95_0.x - ove_0_21.draws.farmtog.RejX:get(), slot_95_0.y + ove_0_21.draws.farmtog.RejY:get(), ove_0_21.draws.farmtog.colorRejOFF:get())
			else
				graphics.draw_text_2D("Farm: ON", ove_0_21.draws.farmtog.RejSize:get(), slot_95_0.x - ove_0_21.draws.farmtog.RejX:get(), slot_95_0.y + ove_0_21.draws.farmtog.RejY:get(), ove_0_21.draws.farmtog.colorRejON:get())
			end
		end

		if ove_0_21.draws.drawfroggle:get() and ove_0_21.wpriority.enable_autoHeal:get() then
			if not ove_0_21.wpriority.healTog:get() then
				graphics.draw_text_2D("Healing: OFF", ove_0_21.draws.lowmantog.RejSize:get(), slot_95_0.x - ove_0_21.draws.lowmantog.RejX:get(), slot_95_0.y + ove_0_21.draws.lowmantog.RejY:get(), ove_0_21.draws.lowmantog.colorRejOFF:get())
			else
				graphics.draw_text_2D("Healing: ON", ove_0_21.draws.lowmantog.RejSize:get(), slot_95_0.x - ove_0_21.draws.lowmantog.RejX:get(), slot_95_0.y + ove_0_21.draws.lowmantog.RejY:get(), ove_0_21.draws.lowmantog.colorRejON:get())
			end
		end

		if ove_0_21.draws.drawtoggleautoq:get() and ove_0_21.autoq.enable:get() then
			if not ove_0_21.autoq.keys_autoq:get() then
				graphics.draw_text_2D("Auto Q: OFF", ove_0_21.draws.Qtog.RejSize:get(), slot_95_0.x - ove_0_21.draws.Qtog.RejX:get(), slot_95_0.y + ove_0_21.draws.Qtog.RejY:get(), ove_0_21.draws.Qtog.colorRejOFF:get())
			else
				graphics.draw_text_2D("Auto Q: ON", ove_0_21.draws.Qtog.RejSize:get(), slot_95_0.x - ove_0_21.draws.Qtog.RejX:get(), slot_95_0.y + ove_0_21.draws.Qtog.RejY:get(), ove_0_21.draws.Qtog.colorRejON:get())
			end
		end
	end

	if ove_0_21.rset.semiR_slow:get() and (ove_0_21.rset.Force_R_key:get() or ove_0_55) then
		local slot_95_1 = "Force R activated"
		local slot_95_2, slot_95_3 = graphics.text_area(slot_95_1, 17)
		local slot_95_4 = slot_95_2 * 0.5

		graphics.draw_text_2D(slot_95_1, 17, slot_95_0.x - slot_95_4, slot_95_0.y, graphics.argb(255, 255, 255, 0))
	end
end

local ove_0_200 = -1

ove_0_21.rset.frRange:set("callback", function(arg_96_0, arg_96_1)
	-- function 96
	ove_0_200 = game.time + 1
end)

local function ove_0_201()
	-- function 97
	local slot_97_0 = game.time - ove_0_200
	local slot_97_1 = 255

	if slot_97_0 > 0 then
		slot_97_1 = slot_97_1 - slot_97_0 / 0.1 * 25
	end

	if slot_97_1 <= 0 then
		return 0
	end

	return slot_97_1
end

local function ove_0_202()
	-- function 98
	if ove_0_201() > 0 then
		graphics.draw_circle(player.pos, ove_0_21.rset.frRange:get(), 3, graphics.argb(ove_0_201(), 255, 255, 0), 200)
		minimap.draw_circle(player.pos, ove_0_21.rset.frRange:get(), 3, graphics.argb(ove_0_201(), 255, 255, 0), 96)
	end
end

local function ove_0_203()
	-- function 99
	if player.isDead then
		return
	end

	if ove_0_21.draws.drawq:get() then
		ove_0_22.ShaderCircle(player.pos, ove_0_40.range, 8, ove_0_21.draws.colorq)
	end

	if ove_0_21.draws.draww:get() then
		ove_0_22.ShaderCircle(player.pos, ove_0_43.range, 8, ove_0_21.draws.colorw)
	end
end

local function ove_0_204()
	-- function 100
	ove_0_203()
	ove_0_195()
	ove_0_198()

	if player:spellSlot(3).state == 0 then
		for iter_100_0 = 0, objManager.enemies_n - 1 do
			local slot_100_0 = objManager.enemies[iter_100_0]

			if ove_0_20.IsValidTarget(slot_100_0) then
				ove_0_194(slot_100_0)
			end
		end
	end

	if not player.isDead then
		ove_0_199()
		ove_0_196()
	end

	ove_0_202()
	ove_0_197()
end

cb.add(cb.create_minion, ove_0_88)
cb.add(cb.delete_minion, ove_0_89)
cb.add(cb.spell, ove_0_103)
ove_0_23.combat.register_f_pre_tick(ove_0_192)
cb.add(cb.draw, ove_0_204)
ove_0_20.Successfully_Loaded(true)

local playerBuff = {}

local buffs = {}

local adds = {}
local removes = {}

local function AddCallback(args, tbl)
    if args then
        for i, callback in ipairs(tbl) do
            callback(args)
        end
    end
end

local function IsValid(buff)
    if buff and buff.startTime and buff.endTime and 
       buff.startTime <= game.time and buff.endTime >= game.time then
        return true
    end
end

local function OnMyTick()
    if player.isDead then
        return
    end

    if player.buffManager and player.buffManager.count then
        for i = 0, player.buffManager.count - 1 do
            local buff = player.buffManager:get(i)
            if buff and <PERSON><PERSON><PERSON><PERSON>(buff) and not buffs[buff.name] then
                local tbl = { source = player, buffer = buff, check1 = false, check2 = false }
                buffs[buff.name] = tbl
            end
        end
    end

    for _, t in pairs(buffs) do
        local buff = t.buffer
        if IsValid(buff) and not t.check1 then
            local info = {name = buff.name, startTime = buff.startTime, endTime = buff.endTime }
            AddCallback(info, adds)
            t.check1 = true
        elseif (not <PERSON>Valid(buff)) and not t.check2 then --or not player.buff[buff.name]) and not t.check2 then
            local info = {name = buff.name, startTime = buff.startTime, endTime = buff.endTime }
            AddCallback(info, removes)
            t.check2 = true
            buffs[buff.name] = nil
        end
    end
end

function playerBuff.AddPlayerBuffAddCallback(cb)
    if cb then
        assert(cb and type(cb) == "function", "[" .. (os.date("%X - %Y")) .. "] ::: PlayerBuffAdd Callback is invalid!")
        table.insert(adds, cb)
    end
end

function playerBuff.AddPlayerBuffRemoveCallback(cb)
    if cb then
        assert(cb and type(cb) == "function", "[" .. (os.date("%X - %Y")) .. "] ::: PlayerBuffRemove Callback is invalid!")
        table.insert(removes, cb)
    end
end

cb.add(cb.tick, OnMyTick)

return playerBuff
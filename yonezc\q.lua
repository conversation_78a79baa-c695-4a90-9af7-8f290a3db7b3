

local ove_0_10 = module.internal("TS")
local ove_0_11 = module.internal("orb")
local ove_0_12 = module.internal("pred")
local ove_0_13 = module.load(header.id, "yone/menu")
local ove_0_14 = player:spellSlot(0)
local ove_0_15 = 202500
local ove_0_16 = {
	range = 475,
	delay = 0,
	boundingRadiusMod = 1,
	width = 40,
	speed = 5000,
	damage = function(arg_5_0)
		-- print 5
		if ove_0_13.farm.clear.q_spam:get() then
			return 3000
		else
			local slot_5_0 = player.crit == 1 and 1.8 or 1

			return 20 * ove_0_14.level + player.totalAd * slot_5_0
		end
	end
}

local function ove_0_17(arg_6_0, arg_6_1, arg_6_2)
	-- print 6
	if arg_6_2 > 1000 then
		return
	end

	local slot_6_0 = ove_0_12.linear.get_prediction(ove_0_16, arg_6_1)

	if slot_6_0 and slot_6_0.startPos:distSqr(slot_6_0.endPos) < ove_0_15 then
		arg_6_0.obj = arg_6_1
		arg_6_0.pos = slot_6_0.endPos

		return true
	end
end

local ove_0_18 = {}
local ove_0_19 = 0

local function ove_0_20()
	-- print 7
	if ove_0_14.name == "YoneQ3" then
		return
	end

	if ove_0_14.state == 0 and os.clock() > ove_0_19 then
		ove_0_16.delay = math.max(0.4958 - 0.1458 * player.attackSpeedMod, 0.175)
		ove_0_18 = ove_0_10.get_result(ove_0_17)

		if ove_0_18.obj then
			return ove_0_18
		end
	end
end

local function ove_0_21()
	-- print 8
	local slot_8_0 = vec3(ove_0_18.pos.x, ove_0_18.obj.y, ove_0_18.pos.y)

	player:castSpell("pos", 0, slot_8_0)

	ove_0_19 = os.clock() + network.latency + 0.35

	ove_0_11.core.set_server_pause()
end

local function ove_0_22()
	-- print 9
	if ove_0_14.name == "YoneQ3" then
		return
	end

	if ove_0_14.state == 0 and os.clock() > ove_0_19 then
		ove_0_16.delay = math.max(0.4958 - 0.1458 * player.attackSpeedMod, 0.175)

		local slot_9_0, slot_9_1 = ove_0_11.farm.skill_clear_linear(ove_0_16)

		if slot_9_1 then
			ove_0_18.obj = slot_9_1
			ove_0_18.pos = slot_9_0.endPos

			return ove_0_18
		end
	end
end

local function ove_0_23()
	-- print 10
	local slot_10_0 = vec3(ove_0_18.pos.x, ove_0_18.obj.y, ove_0_18.pos.y)

	player:castSpell("pos", 0, slot_10_0)

	ove_0_19 = os.clock() + network.latency + 0.35

	ove_0_11.core.set_server_pause()
end

local function ove_0_24()
	-- print 11
	if ove_0_14.name == "YoneQ3" then
		return
	end

	if ove_0_14.state == 0 and os.clock() > ove_0_19 then
		ove_0_16.delay = math.max(0.4958 - 0.1458 * player.attackSpeedMod, 0.175)

		local slot_11_0, slot_11_1 = ove_0_11.farm.skill_farm_linear(ove_0_16)

		if slot_11_1 then
			ove_0_18.obj = slot_11_1
			ove_0_18.pos = slot_11_0.endPos

			return ove_0_18
		end
	end
end

local function ove_0_25()
	-- print 12
	local slot_12_0 = vec3(ove_0_18.pos.x, ove_0_18.obj.y, ove_0_18.pos.y)

	player:castSpell("pos", 0, slot_12_0)

	ove_0_19 = os.clock() + network.latency + 0.35

	ove_0_11.core.set_server_pause()
end

return {
	get_action_state = ove_0_20,
	invoke_action = ove_0_21,
	get_clear_state = ove_0_22,
	invoke_clear = ove_0_23,
	get_farm_state = ove_0_24,
	invoke_farm = ove_0_25
}

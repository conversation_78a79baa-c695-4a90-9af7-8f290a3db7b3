

local ove_0_6 = {}
local ove_0_7 = module.load(header.id, "bs/Lib/action_lock")
local ove_0_8 = module.load(header.id, "bs/Lib/menu/ColorTools")
local ove_0_9 = module.load(header.id, "bs/Lib/geometry")

if game.mode:find("URF") then
	ove_0_6.URF = true
end

if game.mode:find("ARAM") then
	ove_0_6.ARAM = true
end

ove_0_6.MATCHED_GAME = game.mode == "CHERRY"
ove_0_6.yas_wind_wall = {}
ove_0_6.yas_wind_wall_ally = {}
ove_0_6.geometry = ove_0_9
ove_0_6.action_lock = ove_0_7

function ove_0_6.argb(arg_5_0, arg_5_1, arg_5_2, arg_5_3)
	-- function 5
	return ove_0_8.argb(arg_5_0, arg_5_1, arg_5_2, arg_5_3)
end

ove_0_6.draw_this = 0

function ove_0_6.argbmenu(arg_6_0)
	-- function 6
	return ove_0_8.argbmenu(arg_6_0)
end

ove_0_6.debug = true

function ove_0_6.d_debug(arg_7_0)
	-- function 7
	if ove_0_6.debug and arg_7_0 then
		chat.print("<font color='#ffff00' size='40'>" .. tostring(arg_7_0) .. "</font>")
		--print(tostring(arg_7_0))
	end
end

function ove_0_6.d_debug_seg(arg_8_0)
	-- function 8
	if ove_0_6.debug and arg_8_0 then
		ove_0_6.file_debug(tostring(arg_8_0.hit) .. tostring(arg_8_0.data.debug))
	end
end

function ove_0_6.GetPreciseDecimal(arg_9_0, arg_9_1)
	-- function 9
	if type(arg_9_0) ~= "number" then
		return arg_9_0
	end

	arg_9_1 = arg_9_1 or 0
	arg_9_1 = math.floor(arg_9_1)

	if arg_9_1 < 0 then
		arg_9_1 = 0
	end

	if arg_9_1 == 0 then
		return math.ceil(arg_9_0)
	end

	local slot_9_0 = 10^arg_9_1
	local slot_9_1 = math.floor(arg_9_0 * slot_9_0) / slot_9_0

	if slot_9_1 < 0 then
		return 0
	end

	return slot_9_1
end

function ove_0_6.jshfhp(arg_10_0, arg_10_1)
	-- function 10
	local slot_10_0 = arg_10_0.healthRegenRate * arg_10_1

	if slot_10_0 < 0 then
		slot_10_0 = 0
	end

	return slot_10_0
end

function ove_0_6.draw_c(arg_11_0, arg_11_1, arg_11_2)
	-- function 11
	arg_11_2 = arg_11_2 or **********

	graphics:draw_circle_3d(arg_11_0, arg_11_1, arg_11_2, 100, 1)
end

local ove_0_10 = 10485760
local ove_0_11 = hanbot.path .. "/sa_debug.log"

function ove_0_6.file_debug(arg_12_0)
	-- function 12
	if log_file then
		if log_file:seek("end") > ove_0_10 then
			log_file = io.open(ove_0_11, "w")

			log_file:close()

			log_file = io.open(ove_0_11, "a")
		end

		if log_file then
			log_file:write(arg_12_0 .. "\r\n")
			log_file:flush()
		end
	else
		log_file = io.open(ove_0_11, "a")
	end
end

local ove_0_12 = {}
local ove_0_13 = {}

function ove_0_6.InRange(arg_13_0, arg_13_1, arg_13_2)
	-- function 13
	local slot_13_0 = arg_13_2 or player

	return arg_13_0 >= slot_13_0.pos:dist(arg_13_1.pos) and arg_13_0 >= slot_13_0.path.serverPos:dist(arg_13_1.path.serverPos)
end

function ove_0_6.InAARange(arg_14_0, arg_14_1)
	-- function 14
	local slot_14_0 = arg_14_1 or player
	local slot_14_1 = ove_0_6.GetAARange(arg_14_0, slot_14_0)

	return slot_14_1 >= slot_14_0.pos:dist(arg_14_0.pos) and slot_14_1 >= slot_14_0.path.serverPos:dist(arg_14_0.path.serverPos)
end

function ove_0_6.GetAARange(arg_15_0, arg_15_1)
	-- function 15
	local slot_15_0 = arg_15_1 or player
	local slot_15_1 = slot_15_0.attackRange

	if slot_15_0.charName == "Zeri" then
		slot_15_1 = 500
	end

	if arg_15_0 and slot_15_0.ptr == player.ptr and arg_15_0.type == TYPE_HERO then
		return TS.findRange(arg_15_0)
	end

	return slot_15_1 + slot_15_0.boundingRadius + (arg_15_0 and arg_15_0.boundingRadius or 0)
end

function ove_0_6.findRune(arg_16_0, arg_16_1)
	-- function 16
	local slot_16_0 = ove_0_13[arg_16_0.ptr]

	if slot_16_0 then
		return slot_16_0[arg_16_1]
	end
end

function ove_0_6.Init()
	-- function 17
	for iter_17_0 = 0, player.rune.size - 1 do
		local slot_17_0 = player.rune[iter_17_0]

		if slot_17_0 then
			ove_0_12[slot_17_0.id] = slot_17_0.name
		end
	end

	for iter_17_1, iter_17_2 in ipairs(ove_0_6.GetEnemyHeroes()) do
		local slot_17_1 = {}

		for iter_17_3 = 0, iter_17_2.rune.size - 1 do
			local slot_17_2 = iter_17_2.rune[iter_17_3]

			if slot_17_2 then
				slot_17_1[slot_17_2.id] = slot_17_2.name
			end
		end

		ove_0_13[iter_17_2.ptr] = slot_17_1
	end

	for iter_17_4, iter_17_5 in ipairs(ove_0_6.GetAllyHeroes()) do
		local slot_17_3 = {}

		for iter_17_6 = 0, iter_17_5.rune.size - 1 do
			local slot_17_4 = iter_17_5.rune[iter_17_6]

			if slot_17_4 then
				slot_17_3[slot_17_4.id] = slot_17_4.name
			end
		end

		ove_0_13[iter_17_5.ptr] = slot_17_3
	end
end

local ove_0_14 = 0
local ove_0_15 = 0
local ove_0_16 = 0
local ove_0_17 = -math.huge
local ove_0_18 = -math.huge
local ove_0_19

local function ove_0_20()
	-- function 18
	if not ove_0_19 then
		function ove_0_19()
			-- function 19
			ove_0_14 = 1 / (os.clock() - ove_0_18)
			ove_0_18, ove_0_16 = os.clock(), ove_0_16 + 1

			if os.clock() < 0.5 + ove_0_17 then
				return
			end

			ove_0_15 = math.floor(ove_0_16 / (os.clock() - ove_0_17))
			ove_0_17, ove_0_16 = os.clock(), 0
		end
	end
end

function ove_0_6.draw_fps()
	-- function 20
	if ove_0_19 then
		ove_0_19()
	end
end

function ove_0_6.create_missile(arg_21_0)
	-- function 21
	if arg_21_0 and arg_21_0.spell then
		local slot_21_0 = arg_21_0.spell.name
		local slot_21_1 = arg_21_0.spell.owner

		if slot_21_0 == "YasuoW_VisualMis" and slot_21_1 then
			if slot_21_1.team ~= TEAM_ALLY then
				ove_0_6.yas_wind_wall[#ove_0_6.yas_wind_wall + 1] = arg_21_0
			else
				ove_0_6.yas_wind_wall_ally[#ove_0_6.yas_wind_wall_ally + 1] = arg_21_0
			end
		end
	end
end

function ove_0_6.delete_missile(arg_22_0)
	-- function 22
	if #ove_0_6.yas_wind_wall > 0 then
		for iter_22_0 = 1, #ove_0_6.yas_wind_wall do
			local slot_22_0 = ove_0_6.yas_wind_wall[iter_22_0]

			if slot_22_0 and arg_22_0 == slot_22_0.ptr then
				table.remove(ove_0_6.yas_wind_wall, iter_22_0)
			end
		end
	end

	if #ove_0_6.yas_wind_wall_ally > 0 then
		for iter_22_1 = 1, #ove_0_6.yas_wind_wall_ally do
			local slot_22_1 = ove_0_6.yas_wind_wall_ally[iter_22_1]

			if slot_22_1 and arg_22_0 == slot_22_1.ptr then
				table.remove(ove_0_6.yas_wind_wall_ally, iter_22_1)
			end
		end
	end
end

function ove_0_6.VectorDirection(arg_23_0, arg_23_1, arg_23_2)
	-- function 23
	return (arg_23_2.y - arg_23_0.y) * (arg_23_1.x - arg_23_0.x) - (arg_23_1.y - arg_23_0.y) * (arg_23_2.x - arg_23_0.x)
end

function ove_0_6.IsLineSegmentIntersection(arg_24_0, arg_24_1, arg_24_2, arg_24_3)
	-- function 24
	return (ove_0_6.VectorDirection(arg_24_0, arg_24_2, arg_24_3) <= 0 and ove_0_6.VectorDirection(arg_24_1, arg_24_2, arg_24_3) > 0 or ove_0_6.VectorDirection(arg_24_0, arg_24_2, arg_24_3) > 0 and ove_0_6.VectorDirection(arg_24_1, arg_24_2, arg_24_3) <= 0) and (ove_0_6.VectorDirection(arg_24_0, arg_24_1, arg_24_2) <= 0 and ove_0_6.VectorDirection(arg_24_0, arg_24_1, arg_24_3) > 0 or ove_0_6.VectorDirection(arg_24_0, arg_24_1, arg_24_2) > 0 and ove_0_6.VectorDirection(arg_24_0, arg_24_1, arg_24_3) <= 0)
end

local ove_0_21 = {
	320,
	390,
	460,
	530,
	600
}

function ove_0_6.YasuoWall_check(arg_25_0, arg_25_1, arg_25_2)
	-- function 25
	if #ove_0_6.yas_wind_wall == 0 and not arg_25_2 then
		return false
	end

	local slot_25_0 = to2D(arg_25_0)
	local slot_25_1 = to2D(arg_25_1)
	local slot_25_2 = false
	local slot_25_3 = arg_25_2 and ove_0_6.yas_wind_wall_ally or ove_0_6.yas_wind_wall

	for iter_25_0 = 1, #slot_25_3 do
		local slot_25_4 = slot_25_3[iter_25_0]

		if slot_25_4 then
			local slot_25_5 = ove_0_21[slot_25_4.spell.owner:spellSlot(1).level] / 2
			local slot_25_6 = slot_25_4.pos + perp2((slot_25_4.startPos - slot_25_4.pos):norm()) * slot_25_5
			local slot_25_7 = slot_25_4.pos + perp2((slot_25_4.startPos - slot_25_4.pos):norm()) * -slot_25_5
			local slot_25_8 = perp1((to2D(slot_25_7) - to2D(slot_25_6)):norm())
			local slot_25_9 = to2D(slot_25_6) - slot_25_8 * 75
			local slot_25_10 = to2D(slot_25_6) + slot_25_8 * 75
			local slot_25_11 = to2D(slot_25_7) + slot_25_8 * 75
			local slot_25_12 = to2D(slot_25_7) - slot_25_8 * 75

			if ove_0_6.IsLineSegmentIntersection(slot_25_9, slot_25_10, slot_25_0, slot_25_1) then
				return arg_25_0 + (arg_25_1 - arg_25_0):norm() * arg_25_0:dist(slot_25_4.pos)
			end

			if ove_0_6.IsLineSegmentIntersection(slot_25_9, slot_25_12, slot_25_0, slot_25_1) then
				return arg_25_0 + (arg_25_1 - arg_25_0):norm() * arg_25_0:dist(slot_25_4.pos)
			end

			if ove_0_6.IsLineSegmentIntersection(slot_25_11, slot_25_10, slot_25_0, slot_25_1) then
				return arg_25_0 + (arg_25_1 - arg_25_0):norm() * arg_25_0:dist(slot_25_4.pos)
			end

			if ove_0_6.IsLineSegmentIntersection(slot_25_11, slot_25_12, slot_25_0, slot_25_1) then
				return arg_25_0 + (arg_25_1 - arg_25_0):norm() * arg_25_0:dist(slot_25_4.pos)
			end
		end
	end

	return slot_25_2
end

function ove_0_6.GetFPS()
	-- function 26
	ove_0_20()

	return ove_0_14
end

local function ove_0_22(arg_27_0)
	-- function 27
	if arg_27_0.buff.kindredrnodeathbuff and ove_0_6.GetHp(arg_27_0) <= 15 then
		return false
	end

	return true
end

function ove_0_6.checkBuff(arg_28_0)
	-- function 28
	if arg_28_0.type == TYPE_HERO and arg_28_0.ptr ~= player.ptr then
		return not arg_28_0.buff[BUFF_INVULNERABILITY] and not arg_28_0.buff.sionpassivezombie and not arg_28_0.buff.sivire and not arg_28_0.buff.fioraw and not arg_28_0.buff.undyingrage and not arg_28_0.buff.kayler and not arg_28_0.isZombie and ove_0_22(arg_28_0)
	end

	return true
end

function ove_0_6.checkIBuff(arg_29_0)
	-- function 29
	if arg_29_0.type == TYPE_HERO and arg_29_0.ptr ~= player.ptr then
		return arg_29_0.buff[BUFF_INVULNERABILITY] or arg_29_0.buff.sionpassivezombie or arg_29_0.buff.sivire or arg_29_0.buff.fioraw or arg_29_0.buff.undyingrage or arg_29_0.buff.kayler or arg_29_0.buff.kindredrnodeathbuff
	end
end

function ove_0_6.checkHBuff(arg_30_0)
	-- function 30
	if arg_30_0.type == TYPE_HERO and arg_30_0.ptr ~= player.ptr then
		return not arg_30_0.buff[BUFF_INVULNERABILITY] or not arg_30_0.buff.morganae
	end
end

function ove_0_6.CheckBuffType(arg_31_0, arg_31_1)
	-- function 31
	if arg_31_0 and arg_31_0.buffManager and arg_31_0.buffManager.count then
		for iter_31_0 = 0, arg_31_0.buffManager.count - 1 do
			local slot_31_0 = arg_31_0.buffManager:get(iter_31_0)

			if slot_31_0 and slot_31_0.valid and slot_31_0.type == arg_31_1 and (slot_31_0.stacks > 0 or slot_31_0.stacks2 > 0) then
				return true
			end
		end
	end
end

function ove_0_6.isorbTarget(arg_32_0, arg_32_1)
	-- function 32
	return arg_32_0 and not arg_32_0.isDead and arg_32_0.isVisible and arg_32_0.isTargetable
end

function ove_0_6.isTarget(arg_33_0, arg_33_1)
	-- function 33
	return arg_33_0 and not arg_33_0.isDead and arg_33_0.isVisible and arg_33_0.isTargetable and ove_0_6.checkBuff(arg_33_0) and (not arg_33_1 or type(arg_33_1) ~= "number" and ove_0_6.checkHBuff(arg_33_0) or type(arg_33_1) == "number" and arg_33_1 > arg_33_0.pos:dist(player.pos) and (arg_33_0.type == TYPE_HERO or ove_0_6.CheckMinios(arg_33_0.name)))
end

ove_0_6.IsValidTarget = ove_0_6.isTarget

function ove_0_6.GetMinionInRange(arg_34_0, arg_34_1)
	-- function 34
	arg_34_1 = arg_34_1 or player.pos

	local slot_34_0 = {}

	for iter_34_0 = 0, objManager.minions.size[TEAM_NEUTRAL] - 1 do
		local slot_34_1 = objManager.minions[TEAM_NEUTRAL][iter_34_0]

		if ove_0_6.isTarget(slot_34_1) and arg_34_0 >= slot_34_1.pos:dist(arg_34_1) and ove_0_6.CheckMinios(slot_34_1.name) then
			slot_34_0[#slot_34_0 + 1] = slot_34_1
		end
	end

	for iter_34_1 = 0, objManager.minions.size[TEAM_ENEMY] - 1 do
		local slot_34_2 = objManager.minions[TEAM_ENEMY][iter_34_1]

		if ove_0_6.isTarget(slot_34_2) and arg_34_0 >= slot_34_2.pos:dist(arg_34_1) and ove_0_6.CheckMinios(slot_34_2.name) then
			slot_34_0[#slot_34_0 + 1] = slot_34_2
		end
	end

	return slot_34_0
end

function ove_0_6.GetEnemyMinionInRange(arg_35_0, arg_35_1)
	-- function 35
	local slot_35_0 = {}

	for iter_35_0 = 0, objManager.minions.size[TEAM_ENEMY] - 1 do
		local slot_35_1 = objManager.minions[TEAM_ENEMY][iter_35_0]

		if ove_0_6.isTarget(slot_35_1) and arg_35_0 >= slot_35_1.pos:dist(arg_35_1) and ove_0_6.CheckMinios(slot_35_1.name) then
			slot_35_0[#slot_35_0 + 1] = slot_35_1
		end
	end

	return slot_35_0
end

function ove_0_6.GetJungleMinionInRange(arg_36_0, arg_36_1)
	-- function 36
	local slot_36_0 = {}

	for iter_36_0 = 0, objManager.minions.size[TEAM_NEUTRAL] - 1 do
		local slot_36_1 = objManager.minions[TEAM_NEUTRAL][iter_36_0]

		if ove_0_6.isTarget(slot_36_1) and arg_36_0 >= slot_36_1.pos:dist(arg_36_1) and ove_0_6.CheckMinios(slot_36_1.name) then
			slot_36_0[#slot_36_0 + 1] = slot_36_1
		end
	end

	return slot_36_0
end

function ove_0_6.GetAllyMinionInRange(arg_37_0, arg_37_1)
	-- function 37
	local slot_37_0 = {}

	for iter_37_0 = 0, objManager.minions.size[TEAM_ALLY] - 1 do
		local slot_37_1 = objManager.minions[TEAM_ALLY][iter_37_0]

		if ove_0_6.isTarget(slot_37_1) and arg_37_0 >= slot_37_1.pos:dist(arg_37_1) and ove_0_6.CheckMinios(slot_37_1.name) then
			slot_37_0[#slot_37_0 + 1] = slot_37_1
		end
	end

	return slot_37_0
end

function ove_0_6.GetMinionFunc(arg_38_0)
	-- function 38
	local slot_38_0 = {}

	for iter_38_0 = 0, objManager.minions.size[TEAM_NEUTRAL] - 1 do
		local slot_38_1 = objManager.minions[TEAM_NEUTRAL][iter_38_0]

		if ove_0_6.isTarget(slot_38_1) and arg_38_0(slot_38_1) and ove_0_6.CheckMinios(slot_38_1.name) then
			slot_38_0[#slot_38_0 + 1] = slot_38_1
		end
	end

	for iter_38_1 = 0, objManager.minions.size[TEAM_ENEMY] - 1 do
		local slot_38_2 = objManager.minions[TEAM_ENEMY][iter_38_1]

		if ove_0_6.isTarget(slot_38_2) and arg_38_0(slot_38_2) and ove_0_6.CheckMinios(slot_38_2.name) then
			slot_38_0[#slot_38_0 + 1] = slot_38_2
		end
	end

	return slot_38_0
end

function ove_0_6.GetAllInRange(arg_39_0, arg_39_1, arg_39_2)
	-- function 39
	local slot_39_0 = {}

	for iter_39_0, iter_39_1 in ipairs(ove_0_6.GetEnemyHeroes()) do
		if ove_0_6.isTarget(iter_39_1) and arg_39_0 >= iter_39_1.pos:dist(arg_39_1) and ove_0_6.CheckMinios(iter_39_1.name) and (not arg_39_2 or iter_39_1.ptr ~= arg_39_2.ptr) then
			slot_39_0[#slot_39_0 + 1] = iter_39_1
		end
	end

	for iter_39_2 = 0, objManager.minions.size[TEAM_ENEMY] - 1 do
		local slot_39_1 = objManager.minions[TEAM_ENEMY][iter_39_2]

		if ove_0_6.isTarget(slot_39_1) and arg_39_0 >= slot_39_1.pos:dist(arg_39_1) and ove_0_6.CheckMinios(slot_39_1.name) then
			slot_39_0[#slot_39_0 + 1] = slot_39_1
		end
	end

	for iter_39_3 = 0, objManager.minions.size[TEAM_NEUTRAL] - 1 do
		local slot_39_2 = objManager.minions[TEAM_NEUTRAL][iter_39_3]

		if ove_0_6.isTarget(slot_39_2) and arg_39_0 >= slot_39_2.pos:dist(arg_39_1) and ove_0_6.CheckMinios(slot_39_2.name) then
			slot_39_0[#slot_39_0 + 1] = slot_39_2
		end
	end

	return slot_39_0
end

function ove_0_6.GetAllyHeroesInRange(arg_40_0, arg_40_1)
	-- function 40
	local slot_40_0 = arg_40_1 or player.pos
	local slot_40_1 = {}

	for iter_40_0, iter_40_1 in ipairs(ove_0_6.GetAllyHeroes()) do
		if iter_40_1 and not iter_40_1.isDead and iter_40_1.isVisible and iter_40_1.pos:distSqr(slot_40_0) < arg_40_0 * arg_40_0 and iter_40_1.charName ~= "Yuumi" then
			slot_40_1[#slot_40_1 + 1] = iter_40_1
		end
	end

	return slot_40_1
end

function ove_0_6.GetAllyHeroesInRange2(arg_41_0, arg_41_1)
	-- function 41
	local slot_41_0 = arg_41_1 or player.pos
	local slot_41_1 = {}

	for iter_41_0, iter_41_1 in ipairs(ove_0_6.GetAllyHeroes()) do
		if iter_41_1 and not iter_41_1.isDead and iter_41_1.isVisible and iter_41_1.ptr ~= player.ptr and iter_41_1.pos:distSqr(slot_41_0) < arg_41_0 * arg_41_0 and iter_41_1.charName ~= "Yuumi" then
			slot_41_1[#slot_41_1 + 1] = iter_41_1
		end
	end

	return slot_41_1
end

function ove_0_6.count_allies_in_range(arg_42_0, arg_42_1)
	-- function 42
	return ove_0_6.GetAllyHeroesInRange(arg_42_1, arg_42_0)
end

function ove_0_6.GetEnemyHeroesInRange(arg_43_0, arg_43_1, arg_43_2)
	-- function 43
	local slot_43_0 = arg_43_1 or player.pos
	local slot_43_1 = {}

	for iter_43_0, iter_43_1 in ipairs(ove_0_6.GetEnemyHeroes()) do
		if iter_43_1 and not iter_43_1.isDead and iter_43_1.isVisible and iter_43_1.pos:distSqr(slot_43_0) < arg_43_0 * arg_43_0 and (not arg_43_2 or iter_43_1.ptr ~= arg_43_2) then
			slot_43_1[#slot_43_1 + 1] = iter_43_1
		end
	end

	return slot_43_1
end

ove_0_6.GetEnemyHeroInRange = ove_0_6.GetEnemyHeroesInRange

function ove_0_6.GetEnemyHeroesInRange1(arg_44_0, arg_44_1, arg_44_2)
	-- function 44
	local slot_44_0 = arg_44_1 or player.pos
	local slot_44_1 = {}

	for iter_44_0, iter_44_1 in ipairs(ove_0_6.GetEnemyHeroes()) do
		if iter_44_1 and not iter_44_1.isDead and iter_44_1.pos:distSqr(slot_44_0) < arg_44_0 * arg_44_0 and (not arg_44_2 or iter_44_1.ptr ~= arg_44_2) then
			slot_44_1[#slot_44_1 + 1] = iter_44_1
		end
	end

	return slot_44_1
end

function ove_0_6.CountEnemiesInRange(arg_45_0, arg_45_1)
	-- function 45
	return ove_0_6.GetEnemyHeroesInRange(arg_45_1, arg_45_0)
end

function ove_0_6.CountEnemiesInRange1(arg_46_0, arg_46_1)
	-- function 46
	return ove_0_6.GetEnemyHeroesInRange1(arg_46_1, arg_46_0)
end

ove_0_6.count_enemies_in_range = ove_0_6.CountEnemiesInRange
ove_0_6.count_enemies_in_range1 = ove_0_6.CountEnemiesInRange

function ove_0_6.isnotmove(arg_47_0)
	-- function 47
	return arg_47_0.buff[BUFF_STUN] or arg_47_0.buff[BUFF_TAUNT] or arg_47_0.buff[BUFF_SNARE] or arg_47_0.buff[BUFF_FEAR] or arg_47_0.buff[BUFF_KNOCKUP] or arg_47_0.buff[BUFF_KNOCKBACK] or arg_47_0.buff[BUFF_FLEE] or arg_47_0.buff[BUFF_SUPPRESSION] or arg_47_0.buff[BUFF_CHARM] or arg_47_0.buff[BUFF_ASLEEP] or arg_47_0.buff.karthusfallenonetarget or arg_47_0.buff.briare or arg_47_0.buff.katarinar or arg_47_0.buff.missfortunebulletsound or arg_47_0.buff.meditate or arg_47_0.buff.belvethe or arg_47_0.buff.belvethrpresentation or arg_47_0.buff.lissandrarenemy or arg_47_0.buff.namiqdebuff
end

function ove_0_6.iscc(arg_48_0)
	-- function 48
	return arg_48_0.buff[BUFF_STUN] or arg_48_0.buff[BUFF_TAUNT] or arg_48_0.buff[BUFF_SNARE] or arg_48_0.buff[BUFF_FEAR] or arg_48_0.buff[BUFF_KNOCKUP] or arg_48_0.buff[BUFF_KNOCKBACK] or arg_48_0.buff[BUFF_FLEE] or arg_48_0.buff[BUFF_SUPPRESSION] or arg_48_0.buff[BUFF_CHARM] or arg_48_0.buff[BUFF_ASLEEP]
end

ove_0_6.isCC = ove_0_6.iscc

function ove_0_6.text_area1(arg_49_0, arg_49_1)
	-- function 49
	local slot_49_0 = #arg_49_0
	local slot_49_1 = 0
	local slot_49_2 = 1

	while slot_49_2 <= slot_49_0 do
		local slot_49_3 = string.byte(arg_49_0, slot_49_2)
		local slot_49_4 = 1

		if slot_49_3 > 0 and slot_49_3 <= 127 then
			slot_49_4 = 1
		elseif slot_49_3 >= 192 and slot_49_3 < 223 then
			slot_49_4 = 2
		elseif slot_49_3 >= 224 and slot_49_3 < 239 then
			slot_49_4 = 2
		elseif slot_49_3 >= 240 and slot_49_3 <= 247 then
			slot_49_4 = 4
		end

		local slot_49_5 = string.sub(arg_49_0, slot_49_2, slot_49_2 + slot_49_4 - 1)

		slot_49_2 = slot_49_2 + slot_49_4
		slot_49_1 = slot_49_1 + 1
	end

	return math.floor(slot_49_1 * 0.25 * arg_49_1)
end

function ove_0_6.text_area(arg_50_0, arg_50_1)
	-- function 50
	local slot_50_0 = #arg_50_0
	local slot_50_1 = 0
	local slot_50_2 = 1

	while slot_50_2 <= slot_50_0 do
		local slot_50_3 = string.byte(arg_50_0, slot_50_2)
		local slot_50_4 = 1

		if slot_50_3 > 0 and slot_50_3 <= 127 then
			slot_50_4 = 1
		elseif slot_50_3 >= 192 and slot_50_3 < 223 then
			slot_50_4 = 2
		elseif slot_50_3 >= 224 and slot_50_3 < 239 then
			slot_50_4 = 3
		elseif slot_50_3 >= 240 and slot_50_3 <= 247 then
			slot_50_4 = 4
		end

		local slot_50_5 = string.sub(arg_50_0, slot_50_2, slot_50_2 + slot_50_4 - 1)

		slot_50_2 = slot_50_2 + slot_50_4
		slot_50_1 = slot_50_1 + 1
	end

	return math.floor(slot_50_1 * arg_50_1)
end

function ove_0_6.DamageReduction(arg_51_0, arg_51_1, arg_51_2)
	-- function 51
	local slot_51_0

	slot_51_0 = arg_51_2 or player

	local slot_51_1 = 1

	if arg_51_0 == "AD" then
		-- block empty
	end

	if arg_51_0 == "AP" then
		-- block empty
	end

	return slot_51_1
end

local ove_0_23 = {
	SRU_Murkwolf = 1,
	SRU_Gromp = 1,
	SRU_Krug = 1,
	Sru_Crab = 1,
	SRU_Red = 1,
	SRU_Razorbeak = 1,
	SRU_Blue = 1
}

function ove_0_6.PhysicalReduction(arg_52_0, arg_52_1)
	-- function 52
	local slot_52_0 = arg_52_1 or player
	local slot_52_1 = (arg_52_0.bonusArmor * slot_52_0.percentBonusArmorPenetration + (arg_52_0.armor - arg_52_0.bonusArmor)) * slot_52_0.percentArmorPenetration
	local slot_52_2 = slot_52_0.physicalLethality * 0.4 + slot_52_0.physicalLethality * 0.6 * (slot_52_0.levelRef / 18)
	local slot_52_3 = slot_52_1 >= 0 and 100 / (100 + (slot_52_1 - slot_52_2)) or 2 - 100 / (100 - (slot_52_1 - slot_52_2))

	if arg_52_0.type == TYPE_MINION then
		slot_52_3 = math.min(1, slot_52_3)
	end

	return slot_52_3
end

function ove_0_6.MagicReduction(arg_53_0, arg_53_1)
	-- function 53
	local slot_53_0 = arg_53_1 or player
	local slot_53_1 = arg_53_0.spellBlock * slot_53_0.percentMagicPenetration - slot_53_0.flatMagicPenetration

	return slot_53_1 >= 0 and 100 / (100 + slot_53_1) or 2 - 100 / (100 - slot_53_1)
end

function ove_0_6.CalculatePhysicalDamage(arg_54_0, arg_54_1, arg_54_2)
	-- function 54
	local slot_54_0 = arg_54_2 or player

	if arg_54_0 then
		return arg_54_1 * ove_0_6.PhysicalReduction(arg_54_0, slot_54_0) * ove_0_6.DamageReduction("AD", arg_54_0, slot_54_0)
	end

	return 0
end

function ove_0_6.CalculateMagicDamage(arg_55_0, arg_55_1, arg_55_2)
	-- function 55
	local slot_55_0 = arg_55_2 or player

	if arg_55_0 then
		return arg_55_1 * ove_0_6.MagicReduction(arg_55_0, slot_55_0) * ove_0_6.DamageReduction("AP", arg_55_0, slot_55_0)
	end

	return 0
end

local ove_0_24 = {
	0.55,
	0.65,
	0.75
}

function ove_0_6.CalculateAADamage(arg_56_0, arg_56_1)
	-- function 56
	local slot_56_0 = arg_56_1 or player

	if arg_56_0 then
		if arg_56_0.type == TYPE_HERO then
			local slot_56_1 = ove_0_6.GetTotalAD(slot_56_0)

			if ove_0_13[arg_56_0.ptr] and ove_0_13[arg_56_0.ptr][8014] and ove_0_6.GetHp(arg_56_0) < 40 then
				slot_56_1 = slot_56_1 + slot_56_1 * 0.08
			end

			if arg_56_0.buff.ferocioushowl then
				slot_56_1 = slot_56_1 * (1 - ove_0_24[arg_56_0:spellSlot(3).level])
			end

			local slot_56_2 = ove_0_6.CAD(arg_56_0, slot_56_1)

			return data.get_damage(slot_56_2, arg_56_0, slot_56_0, 1)
		end

		return ove_0_6.GetTotalAD(slot_56_0) * ove_0_6.PhysicalReduction(arg_56_0, slot_56_0)
	end

	return 0
end

function ove_0_6.GetTotalAD(arg_57_0)
	-- function 57
	return (arg_57_0 or player).totalAd
end

function ove_0_6.GetTotalAP(arg_58_0)
	-- function 58
	return (arg_58_0 or player).totalAp
end

function ove_0_6.GetBonusAD(arg_59_0)
	-- function 59
	local slot_59_0 = arg_59_0 or player

	return (slot_59_0.baseAttackDamage + slot_59_0.flatPhysicalDamageMod) * slot_59_0.percentPhysicalDamageMod - slot_59_0.baseAttackDamage
end

function ove_0_6.GetHp(arg_60_0, arg_60_1)
	-- function 60
	local slot_60_0 = arg_60_1 or 0
	local slot_60_1 = arg_60_0 or player

	return (slot_60_1.health - slot_60_0) / slot_60_1.maxHealth * 100
end

ove_0_6.GetPercentHealth = ove_0_6.GetHp

function ove_0_6.GetMana(arg_61_0)
	-- function 61
	local slot_61_0 = arg_61_0 or player

	return slot_61_0.mana / slot_61_0.maxMana * 100
end

ove_0_6.GetPercentMana = ove_0_6.GetMana

function ove_0_6.check_crit(arg_62_0)
	-- function 62
	local slot_62_0 = 0

	if arg_62_0 == 1018 then
		slot_62_0 = slot_62_0 + 15
	elseif arg_62_0 == 3086 then
		slot_62_0 = slot_62_0 + 15
	elseif arg_62_0 == 3094 then
		slot_62_0 = slot_62_0 + 20
	elseif arg_62_0 == 3033 then
		slot_62_0 = slot_62_0 + 20
	elseif arg_62_0 == 3124 then
		slot_62_0 = slot_62_0 + 20
	elseif arg_62_0 == 3085 then
		slot_62_0 = slot_62_0 + 20
	elseif arg_62_0 == 3046 then
		slot_62_0 = slot_62_0 + 20
	elseif arg_62_0 == 3046 then
		slot_62_0 = slot_62_0 + 20
	elseif arg_62_0 == 3508 then
		slot_62_0 = slot_62_0 + 20
	elseif arg_62_0 == 6676 then
		slot_62_0 = slot_62_0 + 20
	elseif arg_62_0 == 3139 then
		slot_62_0 = slot_62_0 + 20
	elseif arg_62_0 == 3036 then
		slot_62_0 = slot_62_0 + 20
	elseif arg_62_0 == 3031 then
		slot_62_0 = slot_62_0 + 20
	elseif arg_62_0 == 3072 then
		slot_62_0 = slot_62_0 + 20
	elseif arg_62_0 == 6672 then
		slot_62_0 = slot_62_0 + 20
	elseif arg_62_0 == 6671 then
		slot_62_0 = slot_62_0 + 20
	elseif arg_62_0 == 6673 then
		slot_62_0 = slot_62_0 + 20
	end

	return slot_62_0
end

function ove_0_6.getBuffStacks(arg_63_0, arg_63_1, arg_63_2)
	-- function 63
	assert(arg_63_0, "getBuffStacks: no target")
	assert(arg_63_1, "getBuffStacks: no buffname/type")

	local slot_63_0 = arg_63_0.buff[arg_63_1]

	return slot_63_0 and (arg_63_2 == false or slot_63_0.endTime > game.time) and math.max(slot_63_0.stacks, slot_63_0.stacks2) or 0
end

local ove_0_25 = {}
local ove_0_26

function ove_0_6.DelayAction(arg_64_0, arg_64_1, arg_64_2)
	-- function 64
	if not ove_0_26 then
		function ove_0_26()
			-- function 65
			for iter_65_0, iter_65_1 in pairs(ove_0_25) do
				if iter_65_0 <= os.clock() then
					for iter_65_2 = 1, #iter_65_1 do
						local slot_65_0 = iter_65_1[iter_65_2]

						if slot_65_0 and slot_65_0.func then
							slot_65_0.func(unpack(slot_65_0.args or {}))
						end
					end

					ove_0_25[iter_65_0] = nil
				end
			end
		end

		add_tick(ove_0_26)
	end

	local slot_64_0 = os.clock() + (arg_64_1 or 0)

	if ove_0_25[slot_64_0] then
		ove_0_25[slot_64_0][#ove_0_25[slot_64_0] + 1] = {
			func = arg_64_0,
			args = arg_64_2
		}
	else
		ove_0_25[slot_64_0] = {
			{
				func = arg_64_0,
				args = arg_64_2
			}
		}
	end
end

function ove_0_6.GetShieldedHealth(arg_66_0, arg_66_1)
	-- function 66
	local slot_66_0 = 0

	if arg_66_0 == "AD" then
		slot_66_0 = arg_66_1.physicalShield + arg_66_1.allShield
	elseif arg_66_0 == "AP" then
		slot_66_0 = arg_66_1.magicalShield + arg_66_1.allShield
	elseif arg_66_0 == "ALL" then
		slot_66_0 = arg_66_1.allShield
	end

	return arg_66_1.health + slot_66_0
end

function ove_0_6.GetShielded(arg_67_0, arg_67_1)
	-- function 67
	local slot_67_0 = 0

	if arg_67_0 == "AD" then
		slot_67_0 = arg_67_1.physicalShield + arg_67_1.allShield
	elseif arg_67_0 == "AP" then
		slot_67_0 = arg_67_1.magicalShield + arg_67_1.allShield
	elseif arg_67_0 == "ALL" then
		slot_67_0 = arg_67_1.allShield
	end

	return slot_67_0
end

local ove_0_27 = {
	Ashe = function(arg_68_0, arg_68_1, arg_68_2, arg_68_3, arg_68_4)
		-- function 68
		local slot_68_0 = ove_0_6.getBuffStacks(arg_68_0, "asheqattack")
		local slot_68_1 = ove_0_6.getBuffStacks(arg_68_1, "ashepassiveslow")
		local slot_68_2 = 1

		if slot_68_0 > 0 then
			slot_68_2 = slot_68_2 + 0.03 * arg_68_0:spellSlot(0).level
		end

		if slot_68_1 > 0 then
			slot_68_2 = slot_68_2 + (0.2 + arg_68_0.crit * 0.75)
		end

		local slot_68_3 = false

		if slot_68_0 > 0 then
			slot_68_3 = true
		end

		return arg_68_2 * slot_68_2, arg_68_3, arg_68_4, slot_68_3
	end,
	Aphelios = function(arg_69_0, arg_69_1, arg_69_2, arg_69_3, arg_69_4)
		-- function 69
		if arg_69_0:spellSlot(0) then
			local slot_69_0 = arg_69_0:spellSlot(0).name

			arg_69_2 = slot_69_0 == "ApheliosInfernumQ" and arg_69_2 * 1.1 or arg_69_2

			local slot_69_1 = ove_0_6.getBuffStacks(arg_69_0, "aphelioscrescendumorbitmanager")

			if slot_69_1 > 0 and slot_69_0 == "ApheliosCrescendumQ" then
				local slot_69_2 = slot_69_1

				if slot_69_2 > 20 then
					slot_69_2 = 20
				end

				local slot_69_3 = ({
					0.24,
					0.45,
					0.625,
					0.765,
					0.87,
					0.94,
					0.99,
					1.04,
					1.09,
					1.14,
					1.19,
					1.24,
					1.29,
					1.34,
					1.39,
					1.44,
					1.49,
					1.54,
					1.59,
					1.64
				})[tonumber(slot_69_2)]

				if slot_69_3 then
					arg_69_2 = arg_69_2 + arg_69_2 * slot_69_3
				end
			end

			local slot_69_4 = false

			return arg_69_2, arg_69_3, arg_69_4, slot_69_4
		end

		return arg_69_2, arg_69_3, arg_69_4, call
	end,
	Varus = function(arg_70_0, arg_70_1, arg_70_2, arg_70_3, arg_70_4)
		-- function 70
		return arg_70_2, arg_70_3 + (ove_0_6.getBuffStacks(arg_70_0, "varusw") > 0 and arg_70_0:spellSlot(1).level * 5 + 0.35 * ove_0_6.GetTotalAP(arg_70_0) or 0), arg_70_4, false
	end,
	Corki = function(arg_71_0, arg_71_1, arg_71_2, arg_71_3, arg_71_4)
		-- function 71
		return arg_71_2, arg_71_3, arg_71_4 + ove_0_6.GetTotalAD(arg_71_0) * 0.15, false
	end,
	MissFortune = function(arg_72_0, arg_72_1, arg_72_2, arg_72_3, arg_72_4)
		-- function 72
		if not _aaMinion or _aaMinion and _aaMinion.ptr ~= arg_72_1.ptr and (arg_72_1.type == TYPE_HERO or arg_72_1.type == TYPE_MINION) then
			local slot_72_0 = arg_72_0.levelRef
			local slot_72_1 = (slot_72_0 <= 1 and 0.5 or slot_72_0 <= 4 and 0.6 or slot_72_0 <= 7 and 0.7 or slot_72_0 <= 9 and 0.8 or slot_72_0 <= 11 and 0.9 or 1) * ove_0_6.GetTotalAD(arg_72_0)

			if arg_72_1.type == TYPE_MINION then
				slot_72_1 = slot_72_1 / 2
			end

			arg_72_2 = arg_72_2 + slot_72_1
		end

		return arg_72_2, arg_72_3, arg_72_4, false
	end,
	Vayne = function(arg_73_0, arg_73_1, arg_73_2, arg_73_3, arg_73_4)
		-- function 73
		if arg_73_0:spellSlot(1).level < 1 then
			return arg_73_2, arg_73_3, arg_73_4, false
		end

		local slot_73_0 = 35 + 15 * arg_73_0:spellSlot(1).level

		arg_73_4 = arg_73_4 + (ove_0_6.getBuffStacks(arg_73_1, "vaynesilvereddebuff") > 1 and (0.025 * arg_73_0:spellSlot(1).level + 0.015) * arg_73_1.maxHealth or 0)

		local slot_73_1 = false

		if arg_73_4 > 0 and arg_73_4 < slot_73_0 then
			arg_73_4 = slot_73_0
		end

		return arg_73_2 + (ove_0_6.getBuffStacks(arg_73_0, "vaynetumblebonus") > 0 and (0.05 * arg_73_0:spellSlot(0).level + 0.55) * arg_73_2 or 0), arg_73_3, arg_73_4, slot_73_1
	end,
	Jhin = function(arg_74_0, arg_74_1, arg_74_2, arg_74_3, arg_74_4)
		-- function 74
		local slot_74_0 = arg_74_2 - arg_74_2 * 0.5
		local slot_74_1 = arg_74_0.levelRef
		local slot_74_2 = slot_74_0 + (arg_74_1.maxHealth - arg_74_1.health) * (slot_74_1 < 6 and 0.15 or slot_74_1 < 11 and 0.2 or 0.25)

		return ove_0_6.getBuffStacks(arg_74_0, "jhinpassiveattackbuff") > 0 and slot_74_2 + arg_74_2 or arg_74_2, arg_74_3, arg_74_4, false
	end,
	KogMaw = function(arg_75_0, arg_75_1, arg_75_2, arg_75_3, arg_75_4)
		-- function 75
		if ove_0_6.getBuffStacks(arg_75_0, "kogmawbioarcanebarrage") > 0 then
			local slot_75_0 = (2.25 + 0.75 * arg_75_0:spellSlot(1).level + ove_0_6.GetTotalAP(arg_75_0) / 100 / 1) / 100

			arg_75_3 = arg_75_3 + arg_75_1.maxHealth * slot_75_0
		end

		return arg_75_2, arg_75_3, arg_75_4, false
	end,
	Draven = function(arg_76_0, arg_76_1, arg_76_2, arg_76_3, arg_76_4)
		-- function 76
		return arg_76_2 + (ove_0_6.getBuffStacks(arg_76_0, "dravenspinning") > 0 and math.modf(35 + 5 * arg_76_0:spellSlot(0).level + ove_0_6.GetBonusAD(arg_76_0) * (0.65 + arg_76_0:spellSlot(0).level * 0.1)) or 0), arg_76_3, arg_76_4, false
	end,
	Darius = function(arg_77_0, arg_77_1, arg_77_2, arg_77_3, arg_77_4)
		-- function 77
		return arg_77_2 + (ove_0_6.getBuffStacks(arg_77_0, "dariusnoxiantacticsonh") > 0 and (0.35 + 0.05 * arg_77_0:spellSlot(1).level) * arg_77_2 or 0), arg_77_3, arg_77_4
	end,
	Kalista = function(arg_78_0, arg_78_1, arg_78_2, arg_78_3, arg_78_4)
		-- function 78
		return arg_78_2 * 0.9, arg_78_3, arg_78_4, false
	end,
	Senna = function(arg_79_0, arg_79_1, arg_79_2, arg_79_3, arg_79_4)
		-- function 79
		return arg_79_2 * 1.2, arg_79_3, arg_79_4, false
	end,
	Orianna = function(arg_80_0, arg_80_1, arg_80_2, arg_80_3, arg_80_4)
		-- function 80
		local slot_80_0 = 10 + 2.3529411764705883 * (player.levelRef - 1)
		local slot_80_1 = 14 + 3.2941176470588234 * (player.levelRef - 1)
		local slot_80_2 = ove_0_6.getBuffStacks(arg_80_1, "oriannapstack")
		local slot_80_3 = slot_80_0 + ove_0_6.GetTotalAP(arg_80_0) * 0.15

		arg_80_3 = arg_80_3 + slot_80_3 + (slot_80_2 > 0 and slot_80_3 * 0.2 * slot_80_2 or 0)

		return arg_80_2, math.modf(arg_80_3), arg_80_4, false
	end,
	Kayle = function(arg_81_0, arg_81_1, arg_81_2, arg_81_3, arg_81_4)
		-- function 81
		arg_81_3 = arg_81_0:spellSlot(2).level > 0 and 10 + 5 * arg_81_0:spellSlot(2).level + ove_0_6.GetTotalAP(arg_81_0) * 0.25 + ove_0_6.GetBonusAD(arg_81_0) * 0.1 or 0
		arg_81_3 = arg_81_0.levelRef >= 16 and arg_81_3 * 2 or arg_81_3

		return arg_81_2, math.modf(arg_81_3), arg_81_4, false
	end,
	Viktor = function(arg_82_0, arg_82_1, arg_82_2, arg_82_3, arg_82_4)
		-- function 82
		arg_82_3 = arg_82_3 + (ove_0_6.getBuffStacks(arg_82_0, "viktorpowertransferreturn") > 0 and -5 + 25 * arg_82_0:spellSlot(0).level + ove_0_6.GetTotalAP(arg_82_0) * 0.6 or 0)

		return arg_82_2, math.modf(arg_82_3), arg_82_4, false
	end,
	Caitlyn = function(arg_83_0, arg_83_1, arg_83_2, arg_83_3, arg_83_4, arg_83_5)
		-- function 83
		if arg_83_0.buff.caitlynpassivedriver then
			arg_83_2 = arg_83_2 + ove_0_6.GetTotalAD(arg_83_0) * ((arg_83_0.levelRef > 12 and 1.2 or arg_83_0.levelRef > 6 and 0.9 or 0.6) + 1.421875 * arg_83_0.crit)

			if arg_83_1.type == TYPE_MINION then
				arg_83_2 = arg_83_2 + ove_0_6.GetTotalAD(arg_83_0) * 0.5
			end
		end

		return arg_83_2, arg_83_3, arg_83_4, false
	end,
	Garen = function(arg_84_0, arg_84_1, arg_84_2, arg_84_3, arg_84_4)
		-- function 84
		return arg_84_2 + (ove_0_6.getBuffStacks(arg_84_0, "garenq") > 0 and 30 * arg_84_0:spellSlot(0).level + 0.5 * ove_0_6.GetTotalAD(arg_84_0) or 0), arg_84_3, arg_84_4
	end,
	Viego = function(arg_85_0, arg_85_1, arg_85_2, arg_85_3, arg_85_4, arg_85_5)
		-- function 85
		if arg_85_0:spellSlot(0).name == "ViegoQ" then
			local slot_85_0 = arg_85_0:spellSlot(0).level
			local slot_85_1 = {
				0.02,
				0.03,
				0.04,
				0.05,
				0.06
			}
			local slot_85_2 = {
				10,
				15,
				20,
				25,
				30
			}

			if slot_85_0 > 0 and arg_85_1.health then
				local slot_85_3 = arg_85_1.health * slot_85_1[slot_85_0]

				if slot_85_3 < slot_85_2[slot_85_0] then
					slot_85_3 = slot_85_2[slot_85_0]
				end

				arg_85_2 = arg_85_2 + slot_85_3
			end

			if ove_0_6.getBuffStacks(arg_85_0, "viegoqmark") > 0 then
				arg_85_2 = arg_85_2 + (ove_0_6.GetTotalAD(arg_85_0) * 0.2 + ove_0_6.GetTotalAP(arg_85_0) * 0.15)
			end
		end

		return arg_85_2, arg_85_3, arg_85_4, false
	end,
	Lucian = function(arg_86_0, arg_86_1, arg_86_2, arg_86_3, arg_86_4, arg_86_5)
		-- function 86
		if ove_0_6.getBuffStacks(arg_86_0, "lucianpassivebuff") > 0 then
			local slot_86_0 = player.levelRef
			local slot_86_1 = 0.5

			if slot_86_0 > 6 then
				slot_86_1 = 0.55
			end

			if slot_86_0 > 12 then
				slot_86_1 = 0.6
			end

			if arg_86_1.type == TYPE_MINION then
				slot_86_1 = 1
			end

			arg_86_2 = arg_86_2 + arg_86_2 * slot_86_1
		end

		return arg_86_2, arg_86_3, arg_86_4, false
	end,
	Kaisa = function(arg_87_0, arg_87_1, arg_87_2, arg_87_3, arg_87_4, arg_87_5)
		-- function 87
		local slot_87_0 = 5 + (player.levelRef - 1) * 1.0588235294118
		local slot_87_1 = 1 + (player.levelRef - 1) * 0.64705882352941
		local slot_87_2 = ove_0_6.getBuffStacks(arg_87_1, "kaisapassivemarker")

		if slot_87_2 > 0 and slot_87_2 >= 4 then
			arg_87_3 = arg_87_3 + (arg_87_1.maxHealth - arg_87_1.health) * (0.15 + ove_0_6.GetTotalAP(arg_87_0) / 100 * 0.06)
		end

		local slot_87_3 = ove_0_6.GetTotalAP(arg_87_0)
		local slot_87_4 = slot_87_0 + slot_87_3 * 0.15
		local slot_87_5 = 0

		if slot_87_2 > 0 then
			slot_87_5 = (slot_87_1 + slot_87_3 * 0.025) * slot_87_2
		end

		arg_87_3 = arg_87_3 + (slot_87_4 + slot_87_5)

		return arg_87_2, math.modf(arg_87_3), arg_87_4, false
	end,
	TwistedFate = function(arg_88_0, arg_88_1, arg_88_2, arg_88_3, arg_88_4, arg_88_5)
		-- function 88
		if arg_88_0.buff.cardmasterstackparticle then
			arg_88_3 = arg_88_3 + ((({
				65,
				90,
				115,
				140,
				165
			})[arg_88_0:spellSlot(2).level] or 0) + ove_0_6.GetBonusAD(arg_88_0) * 0.2 + ove_0_6.GetTotalAP(arg_88_0) * 0.4)
		end

		local slot_88_0 = arg_88_0:spellSlot(1).name

		if slot_88_0 ~= "PickACard" then
			local slot_88_1 = arg_88_0:spellSlot(1).level

			if slot_88_0 == "GoldCardLock" then
				arg_88_3 = arg_88_3 + ((({
					15,
					22.5,
					30,
					37.5,
					45
				})[slot_88_1] or 0) + ove_0_6.GetBonusAD(arg_88_0) * 1 + ove_0_6.GetTotalAP(arg_88_0) * 0.5) / 2
			elseif slot_88_0 == "BlueCardLock" then
				arg_88_3 = arg_88_3 + ((({
					40,
					60,
					80,
					100,
					120
				})[slot_88_1] or 0) + ove_0_6.GetBonusAD(arg_88_0) * 1 + ove_0_6.GetTotalAP(arg_88_0) * 1) / 2
			elseif slot_88_0 == "RedCardLock" then
				arg_88_3 = arg_88_3 + ((({
					30,
					45,
					60,
					75,
					90
				})[slot_88_1] or 0) + ove_0_6.GetBonusAD(arg_88_0) * 1 + ove_0_6.GetTotalAP(arg_88_0) * 0.7) / 2
			end
		end

		return arg_88_2, arg_88_3, arg_88_4, false
	end,
	Nilah = function(arg_89_0, arg_89_1, arg_89_2, arg_89_3, arg_89_4, arg_89_5)
		-- function 89
		return arg_89_2, arg_89_3, arg_89_4, false
	end,
	MonkeyKing = function(arg_90_0, arg_90_1, arg_90_2, arg_90_3, arg_90_4, arg_90_5)
		-- function 90
		if ove_0_6.getBuffStacks(arg_90_0, "monkeykingdoubleattack") > 0 then
			arg_90_2 = arg_90_2 + (-5 + 25 * arg_90_0:spellSlot(0).level + ove_0_6.GetBonusAD(arg_90_0) * 0.45)
		end

		return arg_90_2, arg_90_3, arg_90_4, false
	end,
	Akshan = function(arg_91_0, arg_91_1, arg_91_2, arg_91_3, arg_91_4, arg_91_5)
		-- function 91
		if ove_0_6.getBuffStacks(arg_91_1, "akshanpassivedebuff") >= 2 then
			local slot_91_0 = arg_91_0.levelRef > 18 and 18 or arg_91_0.levelRef

			arg_91_3 = arg_91_3 + ({
				10,
				15,
				20,
				25,
				30,
				35,
				40,
				45,
				55,
				65,
				75,
				85,
				95,
				105,
				120,
				135,
				150,
				165
			})[slot_91_0]
		end

		return arg_91_2, arg_91_3, arg_91_4, false
	end,
	Zed = function(arg_92_0, arg_92_1, arg_92_2, arg_92_3, arg_92_4, arg_92_5)
		-- function 92
		if ove_0_6.getBuffStacks(arg_92_1, "zedpassivecd") <= 0 and ove_0_6.GetHp(arg_92_1) < 50 then
			local slot_92_0 = 0.06

			if arg_92_0.levelRef >= 17 then
				slot_92_0 = 0.1
			elseif arg_92_0.levelRef >= 7 then
				slot_92_0 = 0.08
			end

			arg_92_3 = arg_92_3 + arg_92_1.maxHealth * slot_92_0
		end

		return arg_92_2, arg_92_3, arg_92_4, false
	end,
	Ekko = function(arg_93_0, arg_93_1, arg_93_2, arg_93_3, arg_93_4)
		-- function 93
		return arg_93_2, arg_93_3 + (ove_0_6.getBuffStacks(arg_93_0, "ekkoeattackbuff") > 0 and 30 * arg_93_0:spellSlot(2).level + 20 + ove_0_6.GetTotalAP(arg_93_0) * 0.4 or 0), arg_93_4
	end
}

local function ove_0_28(arg_94_0)
	-- function 94
	local slot_94_0 = arg_94_0.charName

	if slot_94_0:find("Siege") then
		return arg_94_0.maxHealth * 0.175
	end

	if slot_94_0:find("Cannon") then
		return arg_94_0.maxHealth * 0.14
	end

	if slot_94_0:find("Ranged") then
		return arg_94_0.maxHealth * 0.625
	end

	if slot_94_0 == "SRU_ChaosMinionMelee" then
		return arg_94_0.maxHealth * 0.425
	end

	return arg_94_0.maxHealth * 0.425
end

local ove_0_29 = module.internal("damagelib")

function ove_0_6.CAA_FULL(arg_95_0, arg_95_1, arg_95_2)
	-- function 95
	if arg_95_1.type == TYPE_NEXUS then
		return 0
	end

	if arg_95_0.type == TYPE_TURRET and arg_95_1.type == TYPE_MINION then
		return ove_0_29.calc_aa_damage(arg_95_0, arg_95_1)
	end

	local slot_95_0 = ove_0_6.GetTotalAD(arg_95_0)
	local slot_95_1 = ove_0_6.GetTotalAD(arg_95_0)
	local slot_95_2 = ove_0_6.GetTotalAP(arg_95_0)
	local slot_95_3 = ove_0_6.GetTotalAP(arg_95_0)
	local slot_95_4 = arg_95_2 or arg_95_0.crit > 0.875
	local slot_95_5 = {}
	local slot_95_6 = 0
	local slot_95_7 = 0
	local slot_95_8 = 0
	local slot_95_9 = 0
	local slot_95_10 = 0

	if arg_95_0.type == TYPE_HERO then
		slot_95_10 = arg_95_0.levelRef

		for iter_95_0 = 0, 6 do
			local slot_95_11 = arg_95_0:itemID(iter_95_0)

			if slot_95_11 > 0 then
				if arg_95_0.crit == 0 then
					slot_95_9 = slot_95_9 + ove_0_6.check_crit(slot_95_11)
				end

				if slot_95_9 > 100 then
					slot_95_9 = 100
				end

				slot_95_5[slot_95_11] = true

				if slot_95_11 == 1054 then
					slot_95_6 = 1
				elseif slot_95_11 == 3070 then
					slot_95_8 = 1
				elseif slot_95_11 == 1056 then
					slot_95_7 = 1
				end
			end
		end
	end

	local slot_95_12 = 0
	local slot_95_13 = 0
	local slot_95_14 = false
	local slot_95_15 = arg_95_0.isMelee

	if slot_95_4 then
		if arg_95_0.charName == "Yasuo" or arg_95_0.charName == "Yone" then
			if slot_95_5[3031] and slot_95_9 > 60 then
				slot_95_0 = slot_95_0 * 1.89
			else
				slot_95_0 = slot_95_0 * 1.575
			end
		elseif slot_95_5[3031] and slot_95_9 > 60 then
			if arg_95_1.type == TYPE_MINION then
				slot_95_0 = slot_95_0 * 2.1
			else
				slot_95_0 = slot_95_0 * 2.1
			end
		else
			slot_95_0 = slot_95_0 * 1.75
		end
	end

	local slot_95_16 = arg_95_0.charName

	if ove_0_27[slot_95_16] then
		slot_95_0, slot_95_12, slot_95_13, slot_95_14 = ove_0_27[slot_95_16](arg_95_0, arg_95_1, slot_95_0, slot_95_12, slot_95_13)
	end

	slot_95_14 = slot_95_14 or false
	ItemId_GuinsoosRageblade = 3124
	ItemId_Noonquiver = 6670
	ItemId_KrakenSlayer = 6672
	ItemId_Rageknife = 6677
	ItemId_NashorsTooth = 3115
	ItemId_WitsEnd = 1043
	ItemId_RecurveBow = 1043
	ItemId_BladeofTheRuinedKing = 3153

	if ove_0_6.aanumber and arg_95_0.ptr == player.ptr and not slot_95_5[ItemId_GuinsoosRageblade] then
		ove_0_6.aanumber = nil
	end

	local slot_95_17 = false

	if slot_95_5[ItemId_KrakenSlayer] and ove_0_6.getBuffStacks(arg_95_0, "6672buff") >= 2 then
		slot_95_12 = slot_95_12 + (20 + slot_95_1 * 0.6 + slot_95_3 * 0.45) - 1
		slot_95_17 = true
	end

	if arg_95_0.ptr == player.ptr then
		if slot_95_5[ItemId_GuinsoosRageblade] then
			ove_0_6.item3124 = true
		else
			ove_0_6.item3124 = false
		end

		if slot_95_5[ItemId_Rageknife] then
			ove_0_6.item6677 = true
		else
			ove_0_6.item6677 = false
		end
	end

	if slot_95_5[ItemId_Rageknife] and not slot_95_14 then
		slot_95_12 = slot_95_12 + 20
	end

	if slot_95_5[ItemId_GuinsoosRageblade] then
		if arg_95_0.ptr == player.ptr and not ove_0_6.aanumber then
			ove_0_6.aanumber = 1
		end

		if ove_0_6.aanumber and not slot_95_17 then
			if ove_0_6.aanumber % 3 == 0 and arg_95_0.ptr == player.ptr then
				slot_95_12 = slot_95_12 + (30 + slot_95_9 * 1.5) * 2
			else
				slot_95_12 = slot_95_12 + 30 + slot_95_9 * 1.5
			end
		else
			slot_95_12 = slot_95_12 + 30
		end
	end

	if slot_95_5[ItemId_NashorsTooth] and not slot_95_14 then
		slot_95_12 = slot_95_12 + (15 + ove_0_6.GetTotalAP(arg_95_0) * 0.2)
	end

	if slot_95_5[ItemId_RecurveBow] and not slot_95_14 then
		slot_95_0 = slot_95_0 + 15
	end

	if slot_95_5[3091] and not slot_95_14 then
		local slot_95_18 = arg_95_0.levelRef

		if slot_95_18 > 18 then
			slot_95_18 = 18
		end

		if not ove_0_6.Recipe then
			ove_0_6.Recipe = {
				15,
				15,
				15,
				15,
				15,
				15,
				15,
				15,
				25,
				35,
				45,
				55,
				65,
				75,
				76.25,
				77.5,
				78.75,
				80
			}
		end

		slot_95_12 = slot_95_12 + ove_0_6.Recipe[slot_95_18]
	end

	if slot_95_5[ItemId_BladeofTheRuinedKing] and not slot_95_14 then
		if slot_95_15 then
			slot_95_0 = slot_95_0 + math.max(0, math.min(60, 0.1 * arg_95_1.health)) - 3
		else
			slot_95_0 = slot_95_0 + math.max(0, math.min(60, 0.06 * arg_95_1.health)) - 3
		end
	end

	ItemId_TearoftheGoddess = 3070
	ItemId_DoransShield = 1054
	ItemId_DoransRing = 1056

	if slot_95_5[ItemId_TearoftheGoddess] and not slot_95_14 then
		slot_95_0 = slot_95_0 + slot_95_8 * 5
	elseif slot_95_5[ItemId_DoransShield] and not slot_95_14 then
		slot_95_0 = slot_95_0 + slot_95_6 * 5
	elseif slot_95_5[ItemId_DoransRing] and not slot_95_14 then
		slot_95_0 = slot_95_0 + slot_95_7 * 5
	end

	ItemId_Sheen = 3057
	ItemId_DivineSunderer = 6632
	ItemId_TrinityForce = 3078
	ItemId_EssenceReaver = 3508
	ItemId_LichBane = 3100

	if not slot_95_14 and arg_95_0.type == TYPE_HERO and slot_95_16 ~= "Ezreal" and ove_0_6.getBuffStacks(arg_95_0, "3078trinityforce") > 0 or ove_0_6.getBuffStacks(arg_95_0, "sheen") > 0 or ove_0_6.getBuffStacks(arg_95_0, "3508buff") > 0 or ove_0_6.getBuffStacks(arg_95_0, "lichbane") > 0 or ove_0_6.getBuffStacks(arg_95_0, "6632buff") > 0 then
		if slot_95_5[ItemId_Sheen] then
			slot_95_0 = slot_95_0 + arg_95_0.baseAttackDamage
		elseif slot_95_5[3025] then
			slot_95_0 = slot_95_0 + arg_95_0.baseAttackDamage
		elseif slot_95_5[ItemId_DivineSunderer] then
			if arg_95_1.type == TYPE_MINION then
				slot_95_0 = slot_95_0 + arg_95_0.baseAttackDamage * 1.5
			elseif arg_95_1.type == TYPE_HERO then
				slot_95_0 = slot_95_0 + arg_95_0.baseAttackDamage * 1.6 + arg_95_1.maxHealth * 0.2
			end
		elseif slot_95_5[ItemId_TrinityForce] then
			slot_95_0 = slot_95_0 + arg_95_0.baseAttackDamage * 2
		elseif slot_95_5[ItemId_EssenceReaver] then
			slot_95_0 = slot_95_0 + arg_95_0.baseAttackDamage * 1.4 + ove_0_6.GetBonusAD(arg_95_0) * 0.2
		elseif slot_95_5[ItemId_LichBane] then
			slot_95_12 = slot_95_12 + arg_95_0.baseAttackDamage * 1.5 + ove_0_6.GetTotalAP(arg_95_0) * 0.4
		end
	end

	if ove_0_6.getBuffStacks(arg_95_0, "itemfrozenfist") > 0 and not slot_95_14 then
		if arg_95_0.levelRef > 18 then
			local slot_95_19 = 18
		end

		if slot_95_5[3057] then
			slot_95_0 = slot_95_0 + arg_95_0.baseAttackDamage
		elseif slot_95_5[3025] then
			slot_95_0 = slot_95_0 + arg_95_0.baseAttackDamage
		elseif slot_95_5[3078] then
			slot_95_0 = slot_95_0 + arg_95_0.baseAttackDamage * 2
		end
	end

	if ove_0_6.getBuffStacks(arg_95_0, "itemstatikshankcharge") >= 100 and not slot_95_14 then
		if slot_95_5[3094] then
			slot_95_12 = slot_95_12 + 60
		elseif slot_95_5[3087] then
			local slot_95_20 = slot_95_10 >= 11 and 140 + (slot_95_10 - 10) * 5 or slot_95_10 >= 7 and 100 + (slot_95_10 - 6) * 10 or 100

			if arg_95_1.type == TYPE_MINION and arg_95_1.team == TEAM_ENEMY then
				slot_95_20 = 150
			end

			slot_95_12 = slot_95_12 + slot_95_20 + ove_0_6.GetTotalAP(arg_95_0) * 0.15
		elseif slot_95_5[3095] then
			slot_95_12 = slot_95_12 + 25 + slot_95_1 * 0.65 + slot_95_3 * 0.5
		elseif slot_95_5[2015] then
			slot_95_12 = slot_95_12 + 60
		end
	end

	if arg_95_0.type == TYPE_HERO and arg_95_0:spellSlot(0) and arg_95_0:spellSlot(0).name == "JinxQ" and ove_0_6.getBuffStacks(arg_95_0, "jinxqicon") == 0 then
		slot_95_0 = slot_95_0 * 1.1
	elseif slot_95_16 == "Zeri" then
		if ove_0_6.getBuffStacks(arg_95_0, "zeriqpassiveready") > 0 then
			local slot_95_21 = 90 + 6.470588235294118 * (arg_95_0.levelRef - 1) * (0.7025 + 0.0175 * (arg_95_0.levelRef - 1)) + slot_95_2 * 1.1
			local slot_95_22 = 1 + 0.8235294117647058 * (arg_95_0.levelRef - 1) * (0.7025 + 0.0175 * (arg_95_0.levelRef - 1))
			local slot_95_23 = arg_95_1.maxHealth * (slot_95_22 / 100)

			if arg_95_1.type ~= TYPE_HERO and slot_95_23 > 300 then
				slot_95_23 = 300
			end

			local slot_95_24 = slot_95_21 + slot_95_23

			return ove_0_6.CAP(arg_95_1, slot_95_24, arg_95_0), 0, 0
		else
			local slot_95_25 = 60 + 5.294117647058823 * (arg_95_0.levelRef - 1) * (0.7025 + 0.0175 * (arg_95_0.levelRef - 1)) + slot_95_2 * 0.18

			if slot_95_25 >= arg_95_1.health then
				return slot_95_25
			end

			local slot_95_26 = 10 + 0.8823529411764706 * (arg_95_0.levelRef - 1) * (0.7025 + 0.0175 * (arg_95_0.levelRef - 1)) + slot_95_2 * 0.03

			return ove_0_6.CAP(arg_95_1, slot_95_26, arg_95_0), 0, 0
		end
	end

	if arg_95_1.type == TYPE_HERO and arg_95_0.type == TYPE_HERO and ove_0_13[arg_95_0.ptr] and ove_0_13[arg_95_0.ptr][8014] and ove_0_6.GetHp(arg_95_1) < 40 then
		slot_95_0 = slot_95_0 + slot_95_0 * 0.08
	end

	local slot_95_27 = ove_0_6.CAD(arg_95_1, slot_95_0, arg_95_0)
	local slot_95_28 = ove_0_6.CAP(arg_95_1, slot_95_12, arg_95_0)
	local slot_95_29 = slot_95_13

	if slot_95_5[6676] and arg_95_1.type == TYPE_HERO and (arg_95_1.health - (slot_95_27 + slot_95_28 + slot_95_29)) / arg_95_1.maxHealth * 100 <= 5 then
		slot_95_29 = 9999
	end

	return slot_95_27 + slot_95_28 + slot_95_29, slot_95_27, slot_95_28, slot_95_29
end

function ove_0_6.isq()
	-- function 96
	if player.charName == "Sylas" then
		return false
	end

	return player:spellSlot(0).state == 0
end

function ove_0_6.isw()
	-- function 97
	if player.charName == "Sylas" then
		return false
	end

	return player:spellSlot(1).state == 0
end

function ove_0_6.ise()
	-- function 98
	if player.charName == "Sylas" then
		return false
	end

	return player:spellSlot(2).state == 0
end

function ove_0_6.isr()
	-- function 99
	if player.charName == "Viego" and player:spellSlot(0).name ~= "ViegoQ" then
		return false
	end

	return player:spellSlot(3).state == 0
end

ove_0_6.buff_save = {}

function ove_0_6.buff_ignore(arg_100_0)
	-- function 100
	table.insert(ove_0_6.buff_save, arg_100_0)
end

function ove_0_6.is_valid_buff(arg_101_0, arg_101_1)
	-- function 101
	arg_101_1 = arg_101_1 or player

	local slot_101_0 = arg_101_1.buff[arg_101_0]

	for iter_101_0 = 1, #ove_0_6.buff_save do
		local slot_101_1 = ove_0_6.buff_save[iter_101_0]

		if slot_101_1 and slot_101_1 + 0.25 < game.time then
			table.remove(ove_0_6.buff_save, iter_101_0)
		end

		if slot_101_1 and slot_101_0 and slot_101_0.endTime == slot_101_1 then
			return nil, slot_101_1
		end
	end

	return slot_101_0
end

function ove_0_6.GetDistanceSqr(arg_102_0, arg_102_1)
	-- function 102
	local slot_102_0 = arg_102_1 or player.pos
	local slot_102_1 = arg_102_0.x - slot_102_0.x
	local slot_102_2 = (arg_102_0.z or arg_102_0.y) - (slot_102_0.z or slot_102_0.y)

	return slot_102_1 * slot_102_1 + slot_102_2 * slot_102_2
end

function ove_0_6.GetDistance(arg_103_0, arg_103_1)
	-- function 103
	return math.sqrt(ove_0_6.GetDistanceSqr(arg_103_0, arg_103_1))
end

function ove_0_6.ista(arg_104_0, arg_104_1)
	-- function 104
	local slot_104_0 = arg_104_1 or 0
	local slot_104_1 = math.huge

	for iter_104_0 = 0, objManager.turrets.size[TEAM_ENEMY] - 1 do
		local slot_104_2 = objManager.turrets[TEAM_ENEMY][iter_104_0]

		if slot_104_2 and not slot_104_2.isDead then
			local slot_104_3 = arg_104_0:dist(slot_104_2.pos)

			if slot_104_3 <= 920 + slot_104_0 then
				return true
			end

			slot_104_1 = math.min(slot_104_1, slot_104_3)
		end
	end

	return false
end

function ove_0_6.ista_ally(arg_105_0, arg_105_1)
	-- function 105
	local slot_105_0 = arg_105_1 or 0
	local slot_105_1 = math.huge

	for iter_105_0 = 0, objManager.turrets.size[TEAM_ALLY] - 1 do
		local slot_105_2 = objManager.turrets[TEAM_ALLY][iter_105_0]

		if slot_105_2 and slot_105_2.isTargetable and not slot_105_2.isDead then
			local slot_105_3 = arg_105_0:dist(slot_105_2.pos)

			if slot_105_3 <= 900 + slot_105_0 then
				return true
			end

			slot_105_1 = math.min(slot_105_1, slot_105_3)
		end
	end

	return false
end

function ove_0_6.GetAllyTurrets(arg_106_0)
	-- function 106
	local slot_106_0 = {}
	local slot_106_1 = arg_106_0 or player.pos

	for iter_106_0 = 0, objManager.turrets.size[TEAM_ALLY] - 1 do
		local slot_106_2 = objManager.turrets[TEAM_ALLY][iter_106_0]

		if slot_106_2 and slot_106_2.isTargetable and not slot_106_2.isDead then
			table.insert(slot_106_0, slot_106_2)
		end
	end

	table.sort(slot_106_0, function(arg_107_0, arg_107_1)
		-- function 107
		return slot_106_1:dist(arg_107_0.pos) < slot_106_1:dist(arg_107_1.pos)
	end)

	if slot_106_0[1] then
		return slot_106_0[1], slot_106_0
	end
end

function ove_0_6.GetEnemyTurrets()
	-- function 108
	local slot_108_0 = {}

	for iter_108_0 = 0, objManager.turrets.size[TEAM_ENEMY] - 1 do
		local slot_108_1 = objManager.turrets[TEAM_ENEMY][iter_108_0]

		if slot_108_1 and slot_108_1.isTargetable and not slot_108_1.isDead then
			table.insert(slot_108_0, slot_108_1)
		end
	end

	table.sort(slot_108_0, function(arg_109_0, arg_109_1)
		-- function 109
		return player.pos:dist(arg_109_0.pos) < player.pos:dist(arg_109_1.pos)
	end)

	if slot_108_0[1] then
		return slot_108_0[1], slot_108_0
	end
end

function ove_0_6.count_minions_in_range(arg_110_0, arg_110_1)
	-- function 110
	local slot_110_0 = {}

	for iter_110_0 = 0, objManager.minions.size[TEAM_NEUTRAL] - 1 do
		local slot_110_1 = objManager.minions[TEAM_NEUTRAL][iter_110_0]

		if slot_110_1 and ove_0_6.isTarget(slot_110_1) and ove_0_6.CheckMinios(slot_110_1.name) and arg_110_1 > arg_110_0:dist(slot_110_1.pos) then
			slot_110_0[#slot_110_0 + 1] = enemy
		end
	end

	return slot_110_0
end

local ove_0_30 = {}

for iter_0_0 = 0, objManager.enemies_n - 1 do
	local ove_0_31 = objManager.enemies[iter_0_0]

	table.insert(ove_0_30, ove_0_31)
end

local ove_0_32 = {}

for iter_0_1 = 0, objManager.allies_n - 1 do
	local ove_0_33 = objManager.allies[iter_0_1]

	table.insert(ove_0_32, ove_0_33)
end

function ove_0_6.GetEnemyHeroes()
	-- function 111
	return ove_0_30
end

function ove_0_6.GetAllyHeroes()
	-- function 112
	return ove_0_32
end

function ove_0_6.GetAlliedMinions()
	-- function 113
	local slot_113_0 = {}

	for iter_113_0 = 0, objManager.minions.size[TEAM_ALLY] - 1 do
		local slot_113_1 = objManager.minions[TEAM_ALLY][iter_113_0]

		if ove_0_6.isTarget(slot_113_1) then
			table.insert(slot_113_0, slot_113_1)
		end
	end

	return slot_113_0
end

function ove_0_6.GetEnemyMiniones()
	-- function 114
	local slot_114_0 = {}

	for iter_114_0 = 0, objManager.minions.size[TEAM_ENEMY] - 1 do
		local slot_114_1 = objManager.minions[TEAM_ENEMY][iter_114_0]

		if ove_0_6.isTarget(slot_114_1) then
			table.insert(slot_114_0, slot_114_1)
		end
	end

	return slot_114_0
end

function ove_0_6.GetNeutralMiniones()
	-- function 115
	local slot_115_0 = {}

	for iter_115_0 = 0, objManager.minions.size[TEAM_NEUTRAL] - 1 do
		local slot_115_1 = objManager.minions[TEAM_NEUTRAL][iter_115_0]

		if ove_0_6.isTarget(slot_115_1) then
			table.insert(slot_115_0, slot_115_1)
		end
	end

	return slot_115_0
end

function ove_0_6.GetClosestEnemy()
	-- function 116
	return ove_0_6.IsClosest_pos(player.pos)
end

function ove_0_6.IsClosest_pos(arg_117_0)
	-- function 117
	local slot_117_0
	local slot_117_1 = 9999

	for iter_117_0, iter_117_1 in ipairs(ove_0_6.GetEnemyHeroes()) do
		if iter_117_1 and ove_0_6.isTarget(iter_117_1) then
			local slot_117_2 = arg_117_0:dist(iter_117_1.pos)

			if slot_117_2 < slot_117_1 then
				slot_117_0 = iter_117_1
				slot_117_1 = slot_117_2
			end
		end
	end

	return slot_117_0, slot_117_1
end

function ove_0_6.IsClosest_Dist(arg_118_0)
	-- function 118
	local slot_118_0
	local slot_118_1 = 9999

	for iter_118_0, iter_118_1 in ipairs(ove_0_6.GetEnemyHeroes()) do
		if iter_118_1 and ove_0_6.isTarget(iter_118_1) then
			local slot_118_2 = arg_118_0:dist(iter_118_1.pos)

			if slot_118_2 < slot_118_1 then
				slot_118_0 = iter_118_1
				slot_118_1 = slot_118_2
			end
		end
	end

	return slot_118_1, slot_118_0
end

ove_0_6.CAA = ove_0_6.CalculateAADamage
ove_0_6.CAD = ove_0_6.CalculatePhysicalDamage
ove_0_6.CAP = ove_0_6.CalculateMagicDamage

function ove_0_6.InSkillShot(arg_119_0, arg_119_1, arg_119_2, arg_119_3)
	-- function 119
	local slot_119_0 = arg_119_2
	local slot_119_1, slot_119_2, slot_119_3 = ove_0_6.geometry.ProjectOn(slot_119_0, arg_119_0, arg_119_1)

	return slot_119_1 and arg_119_3 >= slot_119_2:dist(to2D(slot_119_0))
end

function ove_0_6.farm_line(arg_120_0, arg_120_1, arg_120_2)
	-- function 120
	arg_120_2 = arg_120_2 or player.pos

	local slot_120_0 = objManager.minions
	local slot_120_1 = false
	local slot_120_2 = {}
	local slot_120_3 = false

	for iter_120_0 = 0, slot_120_0.size[TEAM_ENEMY] - 1 do
		local slot_120_4 = slot_120_0[TEAM_ENEMY][iter_120_0]

		if slot_120_4 and ove_0_6.isTarget(slot_120_4, arg_120_0) then
			local slot_120_5 = player.pos:dist(slot_120_4.pos)

			table.insert(slot_120_2, slot_120_4)

			if slot_120_5 < player.attackRange + player.boundingRadius + slot_120_4.boundingRadius then
				slot_120_1 = true
			end
		end
	end

	for iter_120_1 = 0, slot_120_0.size[TEAM_NEUTRAL] - 1 do
		local slot_120_6 = slot_120_0[TEAM_NEUTRAL][iter_120_1]

		if slot_120_6 and ove_0_6.isTarget(slot_120_6, arg_120_0) then
			local slot_120_7 = player.pos:dist(slot_120_6.pos)

			table.insert(slot_120_2, slot_120_6)

			if slot_120_7 < player.attackRange + player.boundingRadius + slot_120_6.boundingRadius then
				slot_120_1 = true
			end

			slot_120_3 = true
		end
	end

	local slot_120_8
	local slot_120_9 = 0

	for iter_120_2, iter_120_3 in ipairs(slot_120_2) do
		local slot_120_10 = iter_120_3.pos
		local slot_120_11 = 1

		for iter_120_4, iter_120_5 in ipairs(slot_120_2) do
			if iter_120_3.ptr ~= iter_120_5.ptr and ove_0_6.InSkillShot(arg_120_2, slot_120_10, iter_120_5.pos, arg_120_1 + iter_120_5.boundingRadius) then
				slot_120_11 = slot_120_11 + 1
			end
		end

		if slot_120_9 < slot_120_11 then
			slot_120_9 = slot_120_11
			slot_120_8 = slot_120_10
		end
	end

	return slot_120_8, slot_120_9, slot_120_1, slot_120_3, slot_120_0
end

function ove_0_6.farm_circular(arg_121_0, arg_121_1, arg_121_2)
	-- function 121
	arg_121_2 = arg_121_2 or player.pos

	local slot_121_0 = false
	local slot_121_1 = {}
	local slot_121_2 = false

	for iter_121_0 = 0, objManager.minions.size[TEAM_ENEMY] - 1 do
		local slot_121_3 = objManager.minions[TEAM_ENEMY][iter_121_0]

		if slot_121_3 and ove_0_6.isTarget(slot_121_3, arg_121_0) then
			local slot_121_4 = player.pos:dist(slot_121_3.pos)
			local slot_121_5 = ove_0_6.GetAARange(slot_121_3)

			table.insert(slot_121_1, slot_121_3)

			if slot_121_4 < slot_121_5 then
				slot_121_0 = true
			end
		end
	end

	for iter_121_1 = 0, objManager.minions.size[TEAM_NEUTRAL] - 1 do
		local slot_121_6 = objManager.minions[TEAM_NEUTRAL][iter_121_1]

		if slot_121_6 and ove_0_6.isTarget(slot_121_6, arg_121_0) then
			local slot_121_7 = player.pos:dist(slot_121_6.pos)
			local slot_121_8 = ove_0_6.GetAARange(slot_121_6)

			table.insert(slot_121_1, slot_121_6)

			if slot_121_7 < slot_121_8 then
				slot_121_0 = true
			end

			slot_121_2 = true
		end
	end

	local slot_121_9
	local slot_121_10 = 0

	for iter_121_2, iter_121_3 in ipairs(slot_121_1) do
		local slot_121_11 = iter_121_3.pos
		local slot_121_12 = #ove_0_6.GetMinionInRange(arg_121_1, iter_121_3.pos)

		if slot_121_10 < slot_121_12 then
			slot_121_10 = slot_121_12
			slot_121_9 = slot_121_11
		end
	end

	return slot_121_9, slot_121_10, slot_121_0, slot_121_2, minions
end

function ove_0_6.farm_utilty(arg_122_0, arg_122_1, arg_122_2)
	-- function 122
	arg_122_2 = arg_122_2 or player.pos

	local slot_122_0 = {}
	local slot_122_1 = false

	for iter_122_0, iter_122_1 in ipairs(ove_0_6.GetEnemyMiniones()) do
		if iter_122_1 and ove_0_6.isTarget(iter_122_1, arg_122_1) then
			table.insert(slot_122_0, iter_122_1)
		end
	end

	for iter_122_2, iter_122_3 in ipairs(ove_0_6.GetNeutralMiniones()) do
		if iter_122_3 and ove_0_6.isTarget(iter_122_3, arg_122_1) then
			table.insert(slot_122_0, iter_122_3)

			slot_122_1 = true
		end
	end

	local slot_122_2
	local slot_122_3 = 0
	local slot_122_4 = {}

	for iter_122_4 = 1, #slot_122_0 do
		slot_122_4[#slot_122_4 + 1] = slot_122_0[iter_122_4].pos
	end

	for iter_122_5 = 1, #slot_122_0 do
		for iter_122_6 = 1, #slot_122_0 do
			if slot_122_0[iter_122_6].ptr ~= slot_122_0[iter_122_5].ptr then
				local slot_122_5 = vec3(slot_122_0[iter_122_6].pos) + vec3(slot_122_0[iter_122_5].pos)

				table.insert(slot_122_4, vec3(slot_122_5.x / 2, slot_122_5.y / 2, slot_122_5.z / 2))
			end
		end
	end

	for iter_122_7, iter_122_8 in ipairs(slot_122_4) do
		if arg_122_1 >= arg_122_2:dist(iter_122_8) then
			local slot_122_6 = arg_122_2 + (iter_122_8 - arg_122_2):norm() * arg_122_1
			local slot_122_7 = 0

			for iter_122_9, iter_122_10 in ipairs(slot_122_0) do
				local slot_122_8, slot_122_9, slot_122_10 = ove_0_6.VectorPointProjectionOnLineSegment(arg_122_2, slot_122_6, iter_122_10.pos)

				if slot_122_10 and slot_122_8 and vec3(slot_122_8.x, 0, slot_122_8.y):dist(iter_122_10.pos) <= arg_122_0 + iter_122_10.boundingRadius / 2 then
					slot_122_7 = slot_122_7 + 1
				end
			end

			if slot_122_3 <= slot_122_7 then
				slot_122_3 = slot_122_7
				slot_122_2 = slot_122_6

				if slot_122_3 == #slot_122_0 then
					break
				end
			end
		end
	end

	return slot_122_2, slot_122_3, slot_122_1, slot_122_0
end

function ove_0_6.VectorPointProjectionOnLineSegment(arg_123_0, arg_123_1, arg_123_2)
	-- function 123
	if not arg_123_0 or not arg_123_1 or not arg_123_2 then
		return
	end

	assert(arg_123_0 and arg_123_1 and arg_123_2, "VectorPointProjectionOnLineSegment: wrong argument types (3 <Vector> expected)")

	local slot_123_0 = arg_123_2.x
	local slot_123_1 = arg_123_2.z and arg_123_2.z or arg_123_2.y
	local slot_123_2 = arg_123_0.x
	local slot_123_3 = arg_123_0.z and arg_123_0.z or arg_123_0.y
	local slot_123_4 = arg_123_1.x
	local slot_123_5 = arg_123_1.z and arg_123_1.z or arg_123_1.y
	local slot_123_6 = ((slot_123_0 - slot_123_2) * (slot_123_4 - slot_123_2) + (slot_123_1 - slot_123_3) * (slot_123_5 - slot_123_3)) / ((slot_123_4 - slot_123_2)^2 + (slot_123_5 - slot_123_3)^2)
	local slot_123_7 = {
		x = slot_123_2 + slot_123_6 * (slot_123_4 - slot_123_2),
		y = slot_123_3 + slot_123_6 * (slot_123_5 - slot_123_3)
	}
	local slot_123_8 = slot_123_6 < 0 and 0 or slot_123_6 > 1 and 1 or slot_123_6
	local slot_123_9 = slot_123_8 == slot_123_6

	return slot_123_9 and slot_123_7 or {
		x = slot_123_2 + slot_123_8 * (slot_123_4 - slot_123_2),
		y = slot_123_3 + slot_123_8 * (slot_123_5 - slot_123_3)
	}, slot_123_7, slot_123_9
end

function ove_0_6.VectorPointProjectionOnLineSegment3(arg_124_0, arg_124_1, arg_124_2)
	-- function 124
	if not arg_124_0 or not arg_124_1 or not arg_124_2 then
		return
	end

	assert(arg_124_0 and arg_124_1 and arg_124_2, "VectorPointProjectionOnLineSegment: wrong argument types (3 <Vector> expected)")

	local slot_124_0 = arg_124_2.x
	local slot_124_1 = arg_124_2.z and arg_124_2.z or arg_124_2.y
	local slot_124_2 = arg_124_0.x
	local slot_124_3 = arg_124_0.z and arg_124_0.z or arg_124_0.y
	local slot_124_4 = arg_124_1.x
	local slot_124_5 = arg_124_1.z and arg_124_1.z or arg_124_1.y
	local slot_124_6 = ((slot_124_0 - slot_124_2) * (slot_124_4 - slot_124_2) + (slot_124_1 - slot_124_3) * (slot_124_5 - slot_124_3)) / ((slot_124_4 - slot_124_2)^2 + (slot_124_5 - slot_124_3)^2)
	local slot_124_7 = {
		x = slot_124_2 + slot_124_6 * (slot_124_4 - slot_124_2),
		y = slot_124_3 + slot_124_6 * (slot_124_5 - slot_124_3)
	}
	local slot_124_8 = slot_124_6 < 0 and 0 or slot_124_6 > 1 and 1 or slot_124_6
	local slot_124_9 = slot_124_8 == slot_124_6
	local slot_124_10 = slot_124_9 and slot_124_7 or {
		y = 0,
		x = slot_124_2 + slot_124_8 * (slot_124_4 - slot_124_2),
		z = slot_124_3 + slot_124_8 * (slot_124_5 - slot_124_3)
	}

	slot_124_10 = slot_124_10 and vec3(slot_124_10.x, arg_124_2.y, slot_124_10.y)

	return slot_124_10, slot_124_7, slot_124_9
end

function ove_0_6.check_safe(arg_125_0, arg_125_1, arg_125_2)
	-- function 125
	local slot_125_0 = arg_125_0 or player.pos

	for iter_125_0, iter_125_1 in ipairs(ove_0_6.GetEnemyHeroes()) do
		if ove_0_6.isTarget(iter_125_1) then
			if iter_125_1.buff.warwicke and iter_125_1.pos:dist(slot_125_0) <= 400 then
				return false
			elseif iter_125_1.buff.jaxcounterstrike and iter_125_1.pos:dist(slot_125_0) <= 300 then
				return false
			elseif iter_125_1.buff.galiow and iter_125_1.pos:dist(slot_125_0) <= 500 then
				return false
			elseif iter_125_1.buff.garen and iter_125_1.pos:dist(slot_125_0) <= 350 then
				return false
			end
		end
	end

	if arg_125_1 and evade then
		return evade.core.is_action_safe(slot_125_0, arg_125_1, arg_125_2)
	end

	return true
end

function ove_0_6.CanHarras()
	-- function 126
	if not ove_0_6.ista(player.pos) and not ove_0_6.isnotmove(player) then
		return true
	else
		return false
	end
end

ove_0_6.sr = ove_0_6.CanHarras

function ove_0_6.ismdm(arg_127_0)
	-- function 127
	return player.path.serverPos:distSqr(arg_127_0.path.serverPos) > player.path.serverPos:distSqr(arg_127_0.path.serverPos + arg_127_0.direction)
end

function ove_0_6.check_aa(arg_128_0, arg_128_1)
	-- function 128
	arg_128_0 = arg_128_0 or 0.25
	arg_128_1 = arg_128_1 or orb.combat.target

	if orb and not orb.core.iscloseaa() and arg_128_0 > orb.core.time_to_next_attack() and arg_128_1 then
		return false
	end

	return orb.core.can_action()
end

function ove_0_6.check_farmaa(arg_129_0, arg_129_1)
	-- function 129
	arg_129_0 = arg_129_0 or 0.25
	arg_129_1 = arg_129_1 or orb.combat.target

	if orb and not orb.core.iscloseaa() and arg_129_0 > orb.core.time_to_next_attack() and arg_129_1 and ove_0_6.InAARange(arg_129_1, player) then
		return false
	end

	return orb.core.can_action()
end

function ove_0_6.check_aah1(arg_130_0)
	-- function 130
	arg_130_0 = arg_130_0 or 0.1

	local slot_130_0 = orb

	if slot_130_0 and slot_130_0.core._missileLaunched1 then
		return slot_130_0.core.attack_time + arg_130_0 + network.latency > os.clock()
	end

	return false
end

function ove_0_6.check_aah(arg_131_0)
	-- function 131
	arg_131_0 = arg_131_0 or 0.1

	local slot_131_0 = orb

	if slot_131_0 and slot_131_0.core.can_action() and slot_131_0.core._missileLaunched1 then
		return slot_131_0.core.attack_time + slot_131_0.get_windupTime() + arg_131_0 + network.latency + 0.05 > os.clock()
	end

	return false
end

local ove_0_34 = 0
local ove_0_35 = {}

function ove_0_6.pred_damage(arg_132_0, arg_132_1, arg_132_2)
	-- function 132
	if evade then
		arg_132_1 = arg_132_1 + network.latency

		if ove_0_34 > game.time then
			return 0, 0, 0, 0
		end

		local slot_132_0, slot_132_1, slot_132_2, slot_132_3 = evade.GetDamage(arg_132_0, arg_132_1, arg_132_2)

		if slot_132_0 then
			return slot_132_0, slot_132_1, slot_132_2, slot_132_3
		end
	end

	return 0, 0, 0, 0
end

function ove_0_6.IsPositionSafe(arg_133_0, arg_133_1, arg_133_2)
	-- function 133
	if evade then
		return evade.IsPositionSafe(arg_133_0, arg_133_1, arg_133_2)
	end

	return nil
end

function ove_0_6.GetEvade()
	-- function 134
	return evade
end

local ove_0_36 = 0
local ove_0_37 = {}

function ove_0_6.GetEvadeSpell(arg_135_0, arg_135_1, arg_135_2)
	-- function 135
	if evade then
		if ove_0_36 > game.time then
			return
		end

		return (evade.GetEvadeSpell(arg_135_0, arg_135_1, arg_135_2))
	end
end

function ove_0_6.SetEvadePause(arg_136_0)
	-- function 136
	if evade then
		if not arg_136_0 then
			return evade.core.set_server_pause(arg_136_0)
		else
			return evade.core.set_core_pause(arg_136_0)
		end
	end
end

function ove_0_6.GetAngle(arg_137_0, arg_137_1)
	-- function 137
	if arg_137_0.z then
		arg_137_0 = to2D(arg_137_0)
	end

	if arg_137_1.z then
		arg_137_1 = to2D(arg_137_1)
	end

	local slot_137_0 = (arg_137_0 - player.pos2D):norm()
	local slot_137_1 = (arg_137_1 - player.pos2D):norm()

	return (ove_0_6.geometry.angleBetween(slot_137_0, slot_137_1))
end

function ove_0_6.IsFacing(arg_138_0, arg_138_1)
	-- function 138
	local slot_138_0 = 90

	local function slot_138_1(arg_139_0, arg_139_1)
		-- function 139
		local slot_139_0 = arg_139_0.pos - arg_139_0.direction:norm() * arg_139_1

		return to2D(slot_139_0)
	end

	local slot_138_2 = (to2D(arg_138_1.pos) - to2D(arg_138_0.pos)):norm()
	local slot_138_3 = (slot_138_1(arg_138_1, 100) - to2D(arg_138_0.pos)):norm()

	return ove_0_6.geometry.angleBetween(slot_138_2, slot_138_3) < 120
end

function ove_0_6.CirclePoints(arg_140_0, arg_140_1, arg_140_2, arg_140_3)
	-- function 140
	local slot_140_0 = {}

	for iter_140_0 = 0, arg_140_0 do
		local slot_140_1 = iter_140_0 * 2 * math.pi / arg_140_0
		local slot_140_2 = vec3(arg_140_2.x + arg_140_1 * math.cos(slot_140_1), arg_140_2.y, arg_140_2.z + arg_140_1 * math.sin(slot_140_1))

		if not arg_140_3 or arg_140_3(slot_140_2) then
			table.insert(slot_140_0, slot_140_2)
		end
	end

	return slot_140_0
end

function ove_0_6.CirclePointsTo(arg_141_0, arg_141_1, arg_141_2, arg_141_3)
	-- function 141
	local slot_141_0 = {}

	arg_141_3 = arg_141_3 or 50

	for iter_141_0 = 0, arg_141_0 do
		local slot_141_1 = iter_141_0 * 2 * math.pi / arg_141_0

		for iter_141_1 = 0, arg_141_1, 50 do
			local slot_141_2 = vec3(arg_141_2.x + iter_141_1 * math.cos(slot_141_1), arg_141_2.y, arg_141_2.z + iter_141_1 * math.sin(slot_141_1))

			table.insert(slot_141_0, slot_141_2)
		end
	end

	return slot_141_0
end

function ove_0_6.WardName(arg_142_0)
	-- function 142
	local slot_142_0 = {
		"unused",
		"ivern",
		"ward",
		"trink",
		"trap",
		"spear",
		"device",
		"room",
		"box",
		"plant",
		"poo",
		"barrel",
		"god",
		"feather"
	}

	for iter_142_0 = 1, #slot_142_0 do
		if arg_142_0 and arg_142_0:lower():find(slot_142_0[iter_142_0]) then
			return true
		end
	end

	return false
end

function ove_0_6.CheckMinios(arg_143_0)
	-- function 143
	return not ove_0_6.WardName(arg_143_0)
end

local function ove_0_38(arg_144_0, arg_144_1, arg_144_2)
	-- function 144
	arg_144_0 = to2D(arg_144_0)
	arg_144_1 = to2D(arg_144_1)
	arg_144_2 = to2D(arg_144_2)

	local slot_144_0 = ((arg_144_2.x - arg_144_0.x) * (arg_144_1.x - arg_144_0.x) + (arg_144_2.y - arg_144_0.y) * (arg_144_1.y - arg_144_0.y)) / ((arg_144_1.x - arg_144_0.x)^2 + (arg_144_1.y - arg_144_0.y)^2)

	if slot_144_0 >= 0 and slot_144_0 <= 1 then
		return vec3(arg_144_0.x + slot_144_0 * (arg_144_1.x - arg_144_0.x), game.mousePos.y, arg_144_0.y + slot_144_0 * (arg_144_1.y - arg_144_0.y))
	end

	return nil
end

local function ove_0_39(arg_145_0)
	-- function 145
	local slot_145_0 = arg_145_0
	local slot_145_1 = arg_145_0

	return slot_145_0.x * slot_145_1.x + (slot_145_0.y and slot_145_0.y * slot_145_1.y or 0) + (slot_145_0.z and slot_145_0.z * slot_145_1.z or 0)
end

local function ove_0_40(arg_146_0, arg_146_1, arg_146_2)
	-- function 146
	local slot_146_0 = arg_146_1 - arg_146_0

	return slot_146_0 * (-(arg_146_0.x * slot_146_0.x - slot_146_0.x * arg_146_2.x + (arg_146_0.z - arg_146_2.z) * slot_146_0.z) / ove_0_39(slot_146_0)) + arg_146_0
end

ove_0_6.VectorPointProjectionOnLine = ove_0_40
ove_0_6.point_on_line_seg = ove_0_38

function ove_0_6.farm_1(arg_147_0, arg_147_1, arg_147_2)
	-- function 147
	local slot_147_0 = false
	local slot_147_1 = false
	local slot_147_2 = {}

	for iter_147_0, iter_147_1 in ipairs(ove_0_6.GetEnemyMiniones()) do
		if iter_147_1 and ove_0_6.isTarget(iter_147_1, arg_147_0) and ove_0_6.CheckMinios(iter_147_1.name) then
			if arg_147_2 then
				if arg_147_2(iter_147_1) then
					local slot_147_3 = player.pos:dist(iter_147_1.pos)

					if slot_147_3 < arg_147_0 then
						table.insert(slot_147_2, iter_147_1)
					end

					if slot_147_3 <= ove_0_6.GetAARange(iter_147_1) then
						slot_147_0 = true
					end
				end
			else
				local slot_147_4 = player.pos:dist(iter_147_1.pos)

				if slot_147_4 < arg_147_0 then
					table.insert(slot_147_2, iter_147_1)
				end

				if slot_147_4 <= ove_0_6.GetAARange(iter_147_1) then
					slot_147_0 = true
				end
			end
		end
	end

	if #slot_147_2 == 0 then
		for iter_147_2, iter_147_3 in ipairs(ove_0_6.GetNeutralMiniones()) do
			if iter_147_3 and ove_0_6.isTarget(iter_147_3, arg_147_0) and ove_0_6.CheckMinios(iter_147_3.name) then
				if arg_147_2 then
					if arg_147_2(iter_147_3) then
						local slot_147_5 = player.pos:dist(iter_147_3.pos)

						if slot_147_5 < arg_147_0 then
							table.insert(slot_147_2, iter_147_3)
						end

						if slot_147_5 <= ove_0_6.GetAARange(iter_147_3) then
							slot_147_0 = true
						end

						slot_147_1 = true
					end
				else
					local slot_147_6 = player.pos:dist(iter_147_3.pos)

					if slot_147_6 < arg_147_0 then
						table.insert(slot_147_2, iter_147_3)
					end

					if slot_147_6 <= ove_0_6.GetAARange(iter_147_3) then
						slot_147_0 = true
					end

					slot_147_1 = true
				end
			end
		end
	end

	local slot_147_7
	local slot_147_8 = 0

	for iter_147_4, iter_147_5 in ipairs(slot_147_2) do
		local slot_147_9 = math.min(arg_147_0, math.max(0, iter_147_5.pos:dist(player.pos)))
		local slot_147_10 = player.pos + (iter_147_5.pos - player.pos):norm() * slot_147_9
		local slot_147_11 = 1

		for iter_147_6, iter_147_7 in ipairs(slot_147_2) do
			if iter_147_4 ~= iter_147_6 then
				local slot_147_12 = ove_0_38(player.path.serverPos, slot_147_10, iter_147_7.path.serverPos)

				if slot_147_12 and slot_147_12:dist(iter_147_7.path.serverPos) < arg_147_1 + iter_147_7.boundingRadius then
					slot_147_11 = slot_147_11 + 1
				end
			end
		end

		if not slot_147_7 or slot_147_8 < slot_147_11 then
			slot_147_7, slot_147_8 = slot_147_10, slot_147_11
		end
	end

	return slot_147_7, slot_147_8, slot_147_0, slot_147_1
end

function ove_0_6.farm_2(arg_148_0, arg_148_1, arg_148_2)
	-- function 148
	local slot_148_0 = false
	local slot_148_1 = false
	local slot_148_2 = {}

	for iter_148_0, iter_148_1 in ipairs(ove_0_6.GetEnemyMiniones()) do
		if iter_148_1 and ove_0_6.isTarget(iter_148_1, arg_148_0) and ove_0_6.CheckMinios(iter_148_1.name) then
			if arg_148_2 then
				if arg_148_2(iter_148_1) then
					local slot_148_3 = player.pos:dist(iter_148_1.pos)

					if slot_148_3 < arg_148_0 then
						table.insert(slot_148_2, iter_148_1)
					end

					if slot_148_3 <= ove_0_6.GetAARange(iter_148_1) then
						slot_148_0 = true
					end
				end
			else
				local slot_148_4 = player.pos:dist(iter_148_1.pos)

				if slot_148_4 < arg_148_0 then
					table.insert(slot_148_2, iter_148_1)
				end

				if slot_148_4 <= ove_0_6.GetAARange(iter_148_1) then
					slot_148_0 = true
				end
			end
		end
	end

	for iter_148_2, iter_148_3 in ipairs(ove_0_6.GetNeutralMiniones()) do
		if iter_148_3 and ove_0_6.isTarget(iter_148_3, arg_148_0) and ove_0_6.CheckMinios(iter_148_3.name) then
			if arg_148_2 then
				if arg_148_2(iter_148_3) then
					local slot_148_5 = player.pos:dist(iter_148_3.pos)

					if slot_148_5 < arg_148_0 then
						table.insert(slot_148_2, iter_148_3)
					end

					if slot_148_5 <= ove_0_6.GetAARange(iter_148_3) then
						slot_148_0 = true
					end

					slot_148_1 = true
				end
			else
				local slot_148_6 = player.pos:dist(iter_148_3.pos)

				if slot_148_6 < arg_148_0 then
					table.insert(slot_148_2, iter_148_3)
				end

				if slot_148_6 <= ove_0_6.GetAARange(iter_148_3) then
					slot_148_0 = true
				end

				slot_148_1 = true
			end
		end
	end

	local slot_148_7
	local slot_148_8 = 0

	for iter_148_4, iter_148_5 in ipairs(slot_148_2) do
		local slot_148_9 = #ove_0_6.GetMinionInRange(arg_148_1, iter_148_5.pos)

		if not slot_148_7 or slot_148_8 < slot_148_9 then
			slot_148_7, slot_148_8 = iter_148_5.pos, slot_148_9
		end
	end

	return slot_148_7, slot_148_8, slot_148_0, slot_148_1
end

function ove_0_6.farm_3(arg_149_0)
	-- function 149
	local slot_149_0 = objManager.minions
	local slot_149_1 = false
	local slot_149_2 = false
	local slot_149_3 = {}

	for iter_149_0, iter_149_1 in ipairs(ove_0_6.GetEnemyMiniones()) do
		if iter_149_1 and ove_0_6.isTarget(iter_149_1, arg_149_0) and ove_0_6.CheckMinios(iter_149_1.name) and arg_149_0 > player.pos:dist(iter_149_1.pos) and preds.predict_hp(iter_149_1, 1) > 0 then
			return iter_149_1
		end
	end

	for iter_149_2, iter_149_3 in ipairs(ove_0_6.GetNeutralMiniones()) do
		if iter_149_3 and ove_0_6.isTarget(iter_149_3, arg_149_0) and ove_0_6.CheckMinios(iter_149_3.name) and arg_149_0 > player.pos:dist(iter_149_3.pos) and preds.predict_hp(iter_149_3, 1) > 0 then
			return iter_149_3
		end
	end
end

function ove_0_6.findWill(arg_150_0, arg_150_1, arg_150_2)
	-- function 150
	local slot_150_0 = {}

	for iter_150_0 = 0, arg_150_0 do
		local slot_150_1 = iter_150_0 * 2 * math.pi / arg_150_0

		for iter_150_1 = 0, arg_150_1, 50 do
			local slot_150_2 = vec3(arg_150_2.x + iter_150_1 * math.cos(slot_150_1), arg_150_2.y, arg_150_2.z + iter_150_1 * math.sin(slot_150_1))

			if ove_0_6.is_wall(slot_150_2) then
				table.insert(slot_150_0, slot_150_2)

				break
			end
		end
	end

	return slot_150_0
end

function ove_0_6.DrawArrow(arg_151_0, arg_151_1)
	-- function 151
	local slot_151_0 = arg_151_0:dist(arg_151_1)
	local slot_151_1 = arg_151_0 + (arg_151_1 - arg_151_0):norm() * (slot_151_0 - 60)
	local slot_151_2 = slot_151_1 + perp2((arg_151_0 - slot_151_1):norm()) * 50
	local slot_151_3 = slot_151_1 + perp1((arg_151_0 - slot_151_1):norm()) * 50

	DrawLine3d(arg_151_0, arg_151_0 + (arg_151_1 - arg_151_0):norm() * (slot_151_0 - 10), 4294967295, 2)
	DrawLine3d(slot_151_2, arg_151_1, 4278255433, 4)
	DrawLine3d(slot_151_3, arg_151_1, 4278255433, 4)
end

function ove_0_6.IsMovingInSameDirection(arg_152_0, arg_152_1)
	-- function 152
	local slot_152_0 = arg_152_0.path.endPos

	if slot_152_0 == arg_152_0.pos or not arg_152_0.path.isMoving then
		return false
	end

	local slot_152_1 = arg_152_1.path.endPos

	if slot_152_1 == arg_152_1.pos or not arg_152_1.path.isMoving then
		return false
	end

	local slot_152_2 = to2D(slot_152_0) - arg_152_0.pos2D
	local slot_152_3 = to2D(slot_152_1) - arg_152_1.pos2D

	return ove_0_6.geometry.angleBetween(slot_152_2, slot_152_3) < 25
end

function ove_0_6.GetMp(arg_153_0)
	-- function 153
	return (arg_153_0 or player).mana
end

function ove_0_6.GetHealth(arg_154_0)
	-- function 154
	return (arg_154_0 or player).health
end

function ove_0_6.findBuff(arg_155_0, arg_155_1)
	-- function 155
	arg_155_1 = arg_155_1 or player

	return arg_155_1.buff[arg_155_0]
end

ove_0_6.FindBuff = ove_0_6.findBuff

function ove_0_6.castSpell(arg_156_0, arg_156_1, arg_156_2, arg_156_3, arg_156_4)
	-- function 156
	return player:castSpell(arg_156_0, arg_156_1, arg_156_2, arg_156_3, arg_156_4)
end

function ove_0_6.generate_points(arg_157_0, arg_157_1)
	-- function 157
	local slot_157_0 = (player.path.serverPos2D - arg_157_0.path.serverPos2D):norm()
	local slot_157_1 = slot_157_0.x > 0 and math.pi - math.atan(slot_157_0.y / slot_157_0.x) or math.pi * 2 - math.atan(slot_157_0.y / slot_157_0.x)
	local slot_157_2 = 2
	local slot_157_3 = {
		player.path.serverPos2D - slot_157_0 * arg_157_1
	}

	for iter_157_0 = slot_157_1 + 0.2, math.huge, 0.2 do
		table.insert(slot_157_3, vec2(player.path.serverPos.x + arg_157_1 * math.cos(-iter_157_0 + 0.2 * slot_157_2), player.path.serverPos.z + arg_157_1 * math.sin(-iter_157_0 + 0.2 * slot_157_2)))
		table.insert(slot_157_3, vec2(player.path.serverPos.x + arg_157_1 * math.cos(-iter_157_0), player.path.serverPos.z + arg_157_1 * math.sin(-iter_157_0)))

		slot_157_2 = slot_157_2 + 2

		if iter_157_0 - slot_157_1 + 0.2 > 0.8 then
			break
		end
	end

	table.insert(slot_157_3, vec2(player.path.serverPos.x + arg_157_1 * math.cos(-(slot_157_1 - 0.8)), player.path.serverPos.z + arg_157_1 * math.sin(-(slot_157_1 - 0.8))))
	table.insert(slot_157_3, vec2(player.path.serverPos.x + arg_157_1 * math.cos(-(slot_157_1 + 0.8)), player.path.serverPos.z + arg_157_1 * math.sin(-(slot_157_1 + 0.8))))

	return slot_157_3
end

return ove_0_6

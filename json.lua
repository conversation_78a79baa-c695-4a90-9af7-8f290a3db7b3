﻿local var_0_0 = {}

local function var_0_1(arg_1_0)
	if type(arg_1_0) ~= "table" then
		return type(arg_1_0)
	end

	local var_1_0 = 1

	for iter_1_0 in pairs(arg_1_0) do
		if arg_1_0[var_1_0] ~= nil then
			var_1_0 = var_1_0 + 1
		else
			return "table"
		end
	end

	if var_1_0 == 1 then
		return "table"
	else
		return "array"
	end
end

local function var_0_2(arg_2_0)
	local var_2_0 = {
		"\\",
		"\"",
		"/",
		"\b",
		"\f",
		"\n",
		"\r",
		"\t"
	}
	local var_2_1 = {
		"\\",
		"\"",
		"/",
		"b",
		"f",
		"n",
		"r",
		"t"
	}

	for iter_2_0, iter_2_1 in ipairs(var_2_0) do
		arg_2_0 = arg_2_0:gsub(iter_2_1, "\\" .. var_2_1[iter_2_0])
	end

	return arg_2_0
end

local function var_0_3(arg_3_0, arg_3_1, arg_3_2, arg_3_3)
	arg_3_1 = arg_3_1 + #arg_3_0:match("^%s*", arg_3_1)

	if arg_3_0:sub(arg_3_1, arg_3_1) ~= arg_3_2 then
		if arg_3_3 then
			return
		end

		return arg_3_1, false
	end

	return arg_3_1 + 1, true
end

local function var_0_4(arg_4_0, arg_4_1, arg_4_2)
	arg_4_2 = arg_4_2 or ""

	local var_4_0 = "End of input found while parsing string."

	if arg_4_1 > #arg_4_0 then
		return
	end

	local var_4_1 = arg_4_0:sub(arg_4_1, arg_4_1)

	if var_4_1 == "\"" then
		return arg_4_2, arg_4_1 + 1
	end

	if var_4_1 ~= "\\" then
		return var_0_4(arg_4_0, arg_4_1 + 1, arg_4_2 .. var_4_1)
	end

	local var_4_2 = {
		t = "\t",
		r = "\r",
		n = "\n",
		f = "\f",
		b = "\b"
	}
	local var_4_3 = arg_4_0:sub(arg_4_1 + 1, arg_4_1 + 1)

	if not var_4_3 then
		return
	end

	return var_0_4(arg_4_0, arg_4_1 + 2, arg_4_2 .. (var_4_2[var_4_3] or var_4_3))
end

local function var_0_5(arg_5_0, arg_5_1)
	local var_5_0 = arg_5_0:match("^-?%d+%.?%d*[eE]?[+-]?%d*", arg_5_1)
	local var_5_1 = tonumber(var_5_0)

	if not var_5_1 then
		return
	end

	return var_5_1, arg_5_1 + #var_5_0
end

function var_0_0.stringify(arg_6_0, arg_6_1)
	local var_6_0 = {}
	local var_6_1 = var_0_1(arg_6_0)

	if var_6_1 == "array" then
		if arg_6_1 then
			return
		end

		var_6_0[#var_6_0 + 1] = "["

		for iter_6_0, iter_6_1 in ipairs(arg_6_0) do
			if iter_6_0 > 1 then
				var_6_0[#var_6_0 + 1] = ", "
			end

			var_6_0[#var_6_0 + 1] = var_0_0.stringify(iter_6_1)
		end

		var_6_0[#var_6_0 + 1] = "]"
	elseif var_6_1 == "table" then
		if arg_6_1 then
			return
		end

		var_6_0[#var_6_0 + 1] = "{"

		for iter_6_2, iter_6_3 in pairs(arg_6_0) do
			if #var_6_0 > 1 then
				var_6_0[#var_6_0 + 1] = ", "
			end

			var_6_0[#var_6_0 + 1] = var_0_0.stringify(iter_6_2, true)
			var_6_0[#var_6_0 + 1] = ":"
			var_6_0[#var_6_0 + 1] = var_0_0.stringify(iter_6_3)
		end

		var_6_0[#var_6_0 + 1] = "}"
	elseif var_6_1 == "string" then
		return "\"" .. var_0_2(arg_6_0) .. "\""
	elseif var_6_1 == "number" then
		if arg_6_1 then
			return "\"" .. tostring(arg_6_0) .. "\""
		end

		return tostring(arg_6_0)
	elseif var_6_1 == "boolean" then
		return tostring(arg_6_0)
	elseif var_6_1 == "nil" then
		return "null"
	else
		return
	end

	return table.concat(var_6_0)
end

var_0_0.null = {}

function var_0_0.parse(arg_7_0, arg_7_1, arg_7_2)
	arg_7_1 = arg_7_1 or 1

	if arg_7_1 > #arg_7_0 then
		return
	end

	local var_7_0 = arg_7_1 + #arg_7_0:match("^%s*", arg_7_1)
	local var_7_1 = arg_7_0:sub(var_7_0, var_7_0)

	if var_7_1 == "{" then
		local var_7_2 = {}
		local var_7_3 = true
		local var_7_4 = true

		var_7_0 = var_7_0 + 1

		while true do
			local var_7_5

			var_7_5, var_7_0 = var_0_0.parse(arg_7_0, var_7_0, "}")

			if var_7_5 == nil then
				return var_7_2, var_7_0
			end

			if not var_7_4 then
				return
			end

			var_7_0 = var_0_3(arg_7_0, var_7_0, ":", true)
			var_7_2[var_7_5], var_7_0 = var_0_0.parse(arg_7_0, var_7_0)
			var_7_0, var_7_4 = var_0_3(arg_7_0, var_7_0, ",")
		end
	elseif var_7_1 == "[" then
		local var_7_6 = {}
		local var_7_7 = true
		local var_7_8 = true

		var_7_0 = var_7_0 + 1

		while true do
			local var_7_9

			var_7_9, var_7_0 = var_0_0.parse(arg_7_0, var_7_0, "]")

			if var_7_9 == nil then
				return var_7_6, var_7_0
			end

			if not var_7_8 then
				return
			end

			var_7_6[#var_7_6 + 1] = var_7_9
			var_7_0, var_7_8 = var_0_3(arg_7_0, var_7_0, ",")
		end
	elseif var_7_1 == "\"" then
		return var_0_4(arg_7_0, var_7_0 + 1)
	elseif var_7_1 == "-" or var_7_1:match("%d") then
		return var_0_5(arg_7_0, var_7_0)
	elseif var_7_1 == arg_7_2 then
		return nil, var_7_0 + 1
	else
		local var_7_10 = {
			["false"] = false,
			["true"] = true,
			null = var_0_0.null
		}

		for iter_7_0, iter_7_1 in pairs(var_7_10) do
			local var_7_11 = var_7_0 + #iter_7_0 - 1

			if arg_7_0:sub(var_7_0, var_7_11) == iter_7_0 then
				return iter_7_1, var_7_11 + 1
			end
		end

		local var_7_12 = "position " .. var_7_0 .. ": " .. arg_7_0:sub(var_7_0, var_7_0 + 10)

		return
	end
end

return var_0_0

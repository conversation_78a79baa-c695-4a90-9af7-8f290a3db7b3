

local ove_0_10 = module.internal("TS")
local ove_0_11 = module.internal("orb")
local ove_0_12 = module.internal("pred")
local ove_0_13 = module.load(header.id, "yone/menu")
local ove_0_14 = player:spellSlot(0)
local ove_0_15 = 202500
local ove_0_16 = {
	range = 475,
	delay = 0,
	boundingRadiusMod = 1,
	width = 40,
	speed = 5000,
	damage = function(arg_5_0)
		-- print 5
		if ove_0_13.farm.clear.q_spam:get() then
			return 3000
		else
			local slot_5_0 = player.crit == 1 and 1.8 or 1

			return 20 * ove_0_14.level + player.totalAd * slot_5_0
		end
	end
}

local function ove_0_17(arg_6_0, arg_6_1, arg_6_2)
	-- print 6
	if arg_6_2 > 1000 then
		return
	end

	local slot_6_0 = ove_0_12.linear.get_prediction(ove_0_16, arg_6_1)

	if slot_6_0 and slot_6_0.startPos:distSqr(slot_6_0.endPos) < ove_0_15 then
		arg_6_0.obj = arg_6_1
		arg_6_0.pos = slot_6_0.endPos

		return true
	end
end

local ove_0_18 = {}
local ove_0_19 = 0

-- 立即释放版本（对应Yone00.lua的ove_0_107，用于主连招循环）
local function ove_0_20()
	-- print 7
	if ove_0_14.name == "YoneQ3" then
		return
	end

	if ove_0_14.state == 0 and os.clock() > ove_0_19 then
		-- 精确的攻击速度计算（完全按照Yone00.lua）
		local attackSpeedBonus = (player.attackSpeedMod - 1) * 100
		ove_0_16.delay = math.max(0.175, 0.35 * (1 - attackSpeedBonus / 2.4 / 100))

		-- 立即检测目标，不等待AA穿插
		ove_0_18 = ove_0_10.get_result(ove_0_17)

		if ove_0_18.obj then
			return ove_0_18
		end
	end
end

-- AA穿插版本（对应Yone00.lua的ove_0_108，用于攻击后回调）
local function ove_0_20_weave()
	-- print 7_weave
	if ove_0_14.name == "YoneQ3" then
		return
	end

	if ove_0_14.state == 0 and os.clock() > ove_0_19 then
		-- 精确的攻击速度计算（完全按照Yone00.lua）
		local attackSpeedBonus = (player.attackSpeedMod - 1) * 100
		ove_0_16.delay = math.max(0.175, 0.35 * (1 - attackSpeedBonus / 2.4 / 100))

		-- 完全按照Yone00.lua的AA穿插逻辑，包含冰雹符文特殊处理
		-- 获取冰雹符文状态（对应Yone00.lua的ove_0_70）
		local hail_of_blades_active = false
		if player.buff["Rune_HailOfBladesBuff"] or player.buff["Rune_HailOfBladesOmniBuff"] then
			-- 检查core.lua中的ove_0_37变量（对应ove_0_70）
			local core = module.load(header.id, "yone/core")
			if core and core.get_hail_flag and core.get_hail_flag() then
				hail_of_blades_active = true
			end
		end

		if ove_0_11.core.next_attack - os.clock() > ove_0_16.delay + network.latency or hail_of_blades_active then
			ove_0_18 = ove_0_10.get_result(ove_0_17)

			if ove_0_18.obj then
				return ove_0_18
			end
		end
	end
end

local function ove_0_21()
	-- print 8
	local slot_8_0 = vec3(ove_0_18.pos.x, ove_0_18.obj.y, ove_0_18.pos.y)

	-- 完全按照Yone00.lua的方式释放技能，包含AA穿插逻辑
	if ove_0_11.core.can_action() and (ove_0_11.core.next_attack - os.clock() > ove_0_16.delay + network.latency or ove_0_11.core.next_attack - os.clock() < 0) then
		player:castSpell("pos", 0, slot_8_0)

		-- 完全按照Yone00.lua重置冰雹符文状态
		local core = module.load(header.id, "yone/core")
		if core and core.reset_hail_flag then
			core.reset_hail_flag()
		end
	end

	ove_0_19 = os.clock() + network.latency + 0.35
	ove_0_11.core.set_server_pause()
end

local function ove_0_22()
	-- print 9
	if ove_0_14.name == "YoneQ3" then
		return
	end

	if ove_0_14.state == 0 and os.clock() > ove_0_19 then
		ove_0_16.delay = math.max(0.4958 - 0.1458 * player.attackSpeedMod, 0.175)

		local slot_9_0, slot_9_1 = ove_0_11.farm.skill_clear_linear(ove_0_16)

		if slot_9_1 then
			ove_0_18.obj = slot_9_1
			ove_0_18.pos = slot_9_0.endPos

			return ove_0_18
		end
	end
end

local function ove_0_23()
	-- print 10
	local slot_10_0 = vec3(ove_0_18.pos.x, ove_0_18.obj.y, ove_0_18.pos.y)

	player:castSpell("pos", 0, slot_10_0)

	ove_0_19 = os.clock() + network.latency + 0.35

	ove_0_11.core.set_server_pause()
end

local function ove_0_24()
	-- print 11
	if ove_0_14.name == "YoneQ3" then
		return
	end

	if ove_0_14.state == 0 and os.clock() > ove_0_19 then
		ove_0_16.delay = math.max(0.4958 - 0.1458 * player.attackSpeedMod, 0.175)

		local slot_11_0, slot_11_1 = ove_0_11.farm.skill_farm_linear(ove_0_16)

		if slot_11_1 then
			ove_0_18.obj = slot_11_1
			ove_0_18.pos = slot_11_0.endPos

			return ove_0_18
		end
	end
end

local function ove_0_25()
	-- print 12
	local slot_12_0 = vec3(ove_0_18.pos.x, ove_0_18.obj.y, ove_0_18.pos.y)

	player:castSpell("pos", 0, slot_12_0)

	ove_0_19 = os.clock() + network.latency + 0.35

	ove_0_11.core.set_server_pause()
end

return {
	get_action_state = ove_0_20,
	get_action_state_weave = ove_0_20_weave,  -- AA穿插版本
	invoke_action = ove_0_21,
	get_clear_state = ove_0_22,
	invoke_clear = ove_0_23,
	get_farm_state = ove_0_24,
	invoke_farm = ove_0_25
}

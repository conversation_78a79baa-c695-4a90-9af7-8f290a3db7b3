

local ove_0_10 = "struct VS_OUTPUT\n{\n    float4 Input    : POSITION;\n    float4 Color    : COLOR0;\n    float4 Position : TEXCOORD0;\n};\n\nfloat4x4 Transform;\nfloat2 pos;\nfloat radius;\nfloat4 color;\nfloat lineWidth;\nfloat Is3D;\n\nVS_OUTPUT VS(VS_OUTPUT input) {\n  VS_OUTPUT output = (VS_OUTPUT) 0;\n  output.Input = mul(input.Input, Transform);\n  output.Color = input.Color;\n  output.Position = input.Input;\n  return output;\n}\n\nfloat4 PS(VS_OUTPUT input): COLOR\n{\n  VS_OUTPUT output = (VS_OUTPUT) 0;\n  output = input;\n\n  float4 v = output.Position;\n  \n  float dist = distance(Is3D ? v.xz : v.xy, pos);\n\n  output.Color.xyz = color.xyz;\n  output.Color.w = color.w * (smoothstep(radius + lineWidth * .5, radius, dist) - smoothstep(radius, radius - lineWidth * .5, dist));\n\n  return output.Color;\n}\n\ntechnique Movement\n{\n  pass P0 {\n    ZEnable = FALSE;\n    AlphaBlendEnable = TRUE;\n    DestBlend = InvSrcAlpha;\n    SrcBlend = SrcAlpha;\n    VertexShader = compile vs_3_0 VS();\n    PixelShader = compile ps_3_0 PS();\n  }\n}\n"
local ove_0_11 = "struct VS_OUTPUT\n{\n    float4 Input    : POSITION;\n    float4 Color    : COLOR0;\n    float4 Position : TEXCOORD0;\n};\n\nfloat4x4 Transform;\nfloat2 spos;\nfloat2 epos;\nfloat4 color;\nfloat lineWidth;\n\nVS_OUTPUT VS(VS_OUTPUT input) {\n  VS_OUTPUT output = (VS_OUTPUT) 0;\n  output.Input = mul(input.Input, Transform);\n  output.Color = input.Color;\n  output.Position = input.Input;\n  return output;\n}\n\nfloat GetPointDistanceToLine(float x1, float z1, float x2, float z2, float x3, float z3)\n{\n\tfloat dz = z3 - z2;\n\tfloat dx = x3 - x2;\n\treturn ((z3 - z2) * x1 - (x3 - x2) * z1 + x3 * z2 - z3 * x2) / sqrt(dz * dz + dx * dx);\n}\n\nfloat4 PS(VS_OUTPUT input): COLOR\n{\n  VS_OUTPUT output = (VS_OUTPUT) 0;\n  output = input;\n\n  float4 v = output.Position;\n  \n  float2 perp = float2(-(epos.y - spos.y), epos.x - spos.x);\n  perp = perp / sqrt(perp.x * perp.x + perp.y * perp.y);\n  float lineRadius = lineWidth * .5;\n  float2 edge0 = float2(spos.x - perp.x * lineRadius, spos.y - perp.y * lineRadius);\n  float2 edge1 = float2(spos.x + perp.x * lineRadius, spos.y + perp.y * lineRadius);\n  float2 edge2 = float2(epos.x + perp.x * lineRadius, epos.y + perp.y * lineRadius);\n  float2 edge3 = float2(epos.x - perp.x * lineRadius, epos.y - perp.y * lineRadius);\n  float dist = GetPointDistanceToLine(v.x, v.y, edge0.x, edge0.y, edge1.x, edge1.y);\n  if (dist < 0.)\n    return output.Color;\n  float d = GetPointDistanceToLine(v.x, v.y, edge1.x, edge1.y, edge2.x, edge2.y);\n  if (d < 0.)\n    return output.Color;\n  dist = min(d, dist);\n  d = GetPointDistanceToLine(v.x, v.y, edge2.x, edge2.y, edge3.x, edge3.y);\n  if (d < 0.)\n    return output.Color;\n  dist = min(d, dist);\n  d = GetPointDistanceToLine(v.x, v.y, edge3.x, edge3.y, edge0.x, edge0.y);\n  if (d < 0.)\n    return output.Color;\n  dist = min(d, dist);\n  \n  output.Color.xyz = color.xyz;\n  if (spos.x != epos.x && spos.y != epos.y)\n    output.Color.w = color.w - color.w * smoothstep(.95, 1., 1. - dist / lineRadius);\n  else\n    output.Color.w = color.w;\n\n  return output.Color;\n}\n\ntechnique Movement\n{\n  pass P0 {\n    ZEnable = FALSE;\n    AlphaBlendEnable = TRUE;\n    DestBlend = InvSrcAlpha;\n    SrcBlend = SrcAlpha;\n    VertexShader = compile vs_3_0 VS();\n    PixelShader = compile ps_3_0 PS();\n  }\n}\n"
local ove_0_12 = {
	circle = shadereffect.construct(ove_0_10, false),
	line = shadereffect.construct(ove_0_11, false)
}
local ove_0_13 = {}

function ove_0_12.color_to_vec4(arg_5_0)
	return vec4(bit.band(bit.rshift(arg_5_0, 0), 255) / 255, bit.band(bit.rshift(arg_5_0, 8), 255) / 255, bit.band(bit.rshift(arg_5_0, 16), 255) / 255, bit.band(bit.rshift(arg_5_0, 24), 255) / 255)
end

function ove_0_13.draw_circle_2D(arg_6_0, arg_6_1, arg_6_2, arg_6_3, arg_6_4, arg_6_5)
	if not ove_0_12.circle then
		return
	end

	shadereffect.begin(ove_0_12.circle, 0, false)
	shadereffect.set_float(ove_0_12.circle, "Is3D", 0)
	shadereffect.set_float(ove_0_12.circle, "radius", arg_6_2)
	shadereffect.set_float(ove_0_12.circle, "lineWidth", arg_6_3)
	shadereffect.set_vec2(ove_0_12.circle, "pos", vec2(arg_6_0, arg_6_1))
	shadereffect.set_color(ove_0_12.circle, "color", arg_6_4)
	shadereffect.draw(ove_0_12.circle)
end

function ove_0_13.draw_circle_xyz(arg_7_0, arg_7_1, arg_7_2, arg_7_3, arg_7_4, arg_7_5, arg_7_6)
	if not ove_0_12.circle then
		return
	end

	shadereffect.begin(ove_0_12.circle, arg_7_1, true)
	shadereffect.set_float(ove_0_12.circle, "Is3D", 1)
	shadereffect.set_float(ove_0_12.circle, "radius", arg_7_3)
	shadereffect.set_float(ove_0_12.circle, "lineWidth", arg_7_4 + 1.9)
	shadereffect.set_vec2(ove_0_12.circle, "pos", vec2(arg_7_0, arg_7_2))
	shadereffect.set_color(ove_0_12.circle, "color", arg_7_5)
	shadereffect.draw(ove_0_12.circle)
end

function ove_0_13.draw_circle(arg_8_0, arg_8_1, arg_8_2, arg_8_3, arg_8_4)
	ove_0_13.draw_circle_xyz(arg_8_0.x, arg_8_0.y, arg_8_0.z, arg_8_1, arg_8_2 + 1.9, arg_8_3)
end

function ove_0_13.draw_line_2D(arg_9_0, arg_9_1, arg_9_2, arg_9_3, arg_9_4, arg_9_5)
	if not ove_0_12.line then
		return
	end

	shadereffect.begin(ove_0_12.line, 0, false)
	shadereffect.set_float(ove_0_12.line, "lineWidth", arg_9_4)
	shadereffect.set_vec2(ove_0_12.line, "spos", vec2(arg_9_0, arg_9_1))
	shadereffect.set_vec2(ove_0_12.line, "epos", vec2(arg_9_2, arg_9_3))
	shadereffect.set_color(ove_0_12.line, "color", arg_9_5)
	shadereffect.draw(ove_0_12.line)
end

if ove_0_12.circle then
	graphics.set("draw_circle", ove_0_13.draw_circle)
	graphics.set("draw_circle_2D", ove_0_13.draw_circle_2D)
	graphics.set("draw_circle_xyz", ove_0_13.draw_circle_xyz)
end

if ove_0_12.line then
	graphics.set("draw_line_2D", ove_0_13.draw_line_2D)
end

return setmetatable(ove_0_13, {
	__index = graphics
})

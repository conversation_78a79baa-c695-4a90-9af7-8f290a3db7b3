
local ove_0_5 = module.internal("pred")
local ove_0_6 = module.internal("orb")
local ove_0_7 = module.load("<PERSON>","azir/menu")
local ove_0_8 = module.load("<PERSON>","azir/w")
local ove_0_9 = {}
local ove_0_10 = {}
local ove_0_11 = 1

for iter_0_0 = math.rad(18), math.rad(54), math.rad(18) do
	ove_0_9[ove_0_11] = math.cos(iter_0_0)
	ove_0_10[ove_0_11] = math.sin(iter_0_0)
	ove_0_9[ove_0_11 + 3] = math.cos(-iter_0_0)
	ove_0_10[ove_0_11 + 3] = math.sin(-iter_0_0)
	ove_0_11 = ove_0_11 + 1
end

local ove_0_12 = 0
local ove_0_13

local function ove_0_14(arg_1_0)
	-- print 1
	local slot_1_0, slot_1_1 = navmesh.get_cell(arg_1_0.x, arg_1_0.y)
	local slot_1_2 = navmesh.get_cell_index(slot_1_0, slot_1_1)
	local slot_1_3 = navmesh.cell_info[slot_1_2].type

	if bit.band(slot_1_3, 2) ~= 2 then
		return arg_1_0
	end

	local slot_1_4
	local slot_1_5 = math.huge
	local slot_1_6 = {}
	local slot_1_7 = {}
	local slot_1_8 = 1

	for iter_1_0 = 1, 20 do
		for iter_1_1 = iter_1_0, -iter_1_0, -1 do
			for iter_1_2 = iter_1_0, -iter_1_0, -1 do
				local slot_1_9 = navmesh.get_cell_index(slot_1_0 + iter_1_1, slot_1_1 + iter_1_2)

				if not slot_1_6[slot_1_9] then
					local slot_1_10 = navmesh.cell_info[slot_1_9].type

					if bit.band(slot_1_10, 2) ~= 2 then
						local slot_1_11 = iter_1_1 * iter_1_1 + iter_1_2 * iter_1_2

						if not slot_1_4 or slot_1_11 < slot_1_5 then
							slot_1_4, slot_1_5 = vec2(navmesh.startPos.x + (slot_1_0 + iter_1_1) * 50, navmesh.startPos.z + (slot_1_1 + iter_1_2) * 50), slot_1_11
						end
					end

					slot_1_6[slot_1_9] = true
				end
			end
		end
	end

	return slot_1_4
end

local function ove_0_15(arg_2_0, arg_2_1)
	-- print 2
	local slot_2_0 = (arg_2_1 - arg_2_0):norm()
	local slot_2_1 = arg_2_0:dist(arg_2_1)
	local slot_2_2 = {}
	local slot_2_3 = 1
	local slot_2_4 = ove_0_14(arg_2_1)

	if slot_2_4 then
		slot_2_2[slot_2_3] = {
			cast_pos = arg_2_1,
			land_pos = slot_2_4
		}
		slot_2_3 = slot_2_3 + 1
	end

	for iter_2_0 = 1, 3 do
		local slot_2_5 = arg_2_0 + vec2(slot_2_0.x * ove_0_9[iter_2_0] - slot_2_0.y * ove_0_10[iter_2_0], slot_2_0.x * ove_0_10[iter_2_0] + slot_2_0.y * ove_0_9[iter_2_0]) * slot_2_1
		local slot_2_6 = ove_0_14(slot_2_5)

		if slot_2_6 then
			slot_2_2[slot_2_3] = {
				cast_pos = slot_2_5,
				land_pos = slot_2_6
			}
			slot_2_3 = slot_2_3 + 1
		end

		local slot_2_7 = arg_2_0 + vec2(slot_2_0.x * ove_0_9[iter_2_0 + 3] - slot_2_0.y * ove_0_10[iter_2_0 + 3], slot_2_0.x * ove_0_10[iter_2_0 + 3] + slot_2_0.y * ove_0_9[iter_2_0 + 3]) * slot_2_1
		local slot_2_8 = ove_0_14(slot_2_7)

		if slot_2_8 then
			slot_2_2[slot_2_3] = {
				cast_pos = slot_2_7,
				land_pos = slot_2_8
			}
			slot_2_3 = slot_2_3 + 1
		end
	end

	return slot_2_2
end

local function ove_0_16(arg_3_0, arg_3_1)
	-- print 3
	local slot_3_0
	local slot_3_1
	local slot_3_2 = math.huge

	for iter_3_0, iter_3_1 in ipairs(ove_0_15(arg_3_0, arg_3_1)) do
		local slot_3_3 = iter_3_1.land_pos:dist(arg_3_1)

		if not slot_3_0 or slot_3_3 < slot_3_2 then
			slot_3_0 = iter_3_1.cast_pos
			slot_3_1 = iter_3_1.land_pos
			slot_3_2 = slot_3_3
		end
	end

	return slot_3_0, slot_3_1
end

local function ove_0_17(arg_4_0, arg_4_1)
	-- print 4
	local slot_4_0 = (arg_4_1 - arg_4_0):norm()
	local slot_4_1 = {
		arg_4_1
	}
	local slot_4_2 = 2
	local slot_4_3 = arg_4_0:dist(arg_4_1)

	for iter_4_0 = 1, 3 do
		slot_4_1[slot_4_2] = arg_4_0 + vec2(slot_4_0.x * ove_0_9[iter_4_0] - slot_4_0.y * ove_0_10[iter_4_0], slot_4_0.x * ove_0_10[iter_4_0] + slot_4_0.y * ove_0_9[iter_4_0]) * slot_4_3
		slot_4_1[slot_4_2 + 1] = arg_4_0 + vec2(slot_4_0.x * ove_0_9[iter_4_0 + 3] - slot_4_0.y * ove_0_10[iter_4_0 + 3], slot_4_0.x * ove_0_10[iter_4_0 + 3] + slot_4_0.y * ove_0_9[iter_4_0 + 3]) * slot_4_3
		slot_4_2 = slot_4_2 + 2
	end

	return slot_4_1
end

local function ove_0_18(arg_5_0, arg_5_1)
	-- print 5
	for iter_5_0, iter_5_1 in ipairs(ove_0_17(arg_5_0, arg_5_1)) do
		if not navmesh.isWall(iter_5_1) then
			return iter_5_1
		end
	end
end

local function ove_0_19()
	-- print 6
	if player.path.isActive and player.path.isDashing then
		return ove_0_5.core.lerp(player.path, math.floor((network.latency - 0.016) / 0.05) * 0.05, player.path.dashSpeed)
	end

	return ove_0_5.present.get_source_pos(player)
end

local function ove_0_20()
	-- print 7
	for iter_7_0 = 0, objManager.enemies_n - 1 do
		local slot_7_0 = objManager.enemies[iter_7_0]

		if slot_7_0 and slot_7_0.isVisible and not slot_7_0.isDead and slot_7_0.isTargetable and slot_7_0.pos2D:dist(mousePos2D) < 250 then
			return vec2(slot_7_0.path.serverPos.x, slot_7_0.path.serverPos.z)
		end
	end

	return vec2(mousePos.x, mousePos.z)
end

local function ove_0_21(arg_8_0)
	-- print 8
	if player:spellSlot(2).state <= 8 and not ove_0_13 and ove_0_12 < os.clock() then
		local slot_8_0 = ove_0_5.present.get_source_pos(player)
		local slot_8_1 = slot_8_0 == arg_8_0 and (player.direction2D - slot_8_0):norm() or (arg_8_0 - slot_8_0):norm()
		local slot_8_2 = math.min(math.max(200, slot_8_0:dist(arg_8_0)), 500)
		local slot_8_3, slot_8_4 = ove_0_16(slot_8_0, slot_8_0 + slot_8_1 * slot_8_2)
		local slot_8_5 = -1

		for iter_8_0 = 1, #ove_0_8.soldiers do
			local slot_8_6 = ove_0_8.soldiers[iter_8_0]

			if slot_8_6.pos2D:distSqr(slot_8_0) > 90000 and math.abs(mathf.angle_between(slot_8_0, slot_8_6.pos2D, slot_8_0 + slot_8_1 * slot_8_2)) < 0.57 then
				slot_8_5 = 0
				slot_8_4 = vec2(slot_8_6.pos.x, slot_8_6.pos.z)

				break
			end
		end

		if slot_8_5 == -1 and player:spellSlot(1).state == 0 then
			player:castSpell("pos", 1, vec3(slot_8_3.x, player.y, slot_8_3.y))

			slot_8_5 = 0.25
		end

		if slot_8_5 ~= -1 then
			player:castSpell("pos", 2, vec3(slot_8_4.x, player.y, slot_8_4.y))

			if player:spellSlot(0).state == 0 and slot_8_3:dist(slot_8_0) > 498 and (slot_8_4:dist(arg_8_0) > 250 or ove_0_7.keys.shuffle:get()) then
				ove_0_13 = os.clock() + slot_8_5 + (slot_8_0:dist(slot_8_4) - 65) / 1700 - network.latency * 0.5

				return true
			end
		end
	end
end

local function ove_0_22(arg_9_0)
	-- print 9
	if ove_0_13 and ove_0_13 < os.clock() then
		local slot_9_0 = ove_0_19()
		local slot_9_1 = slot_9_0:dist(arg_9_0)
		local slot_9_2 = 770 + network.latency * 1700
		local slot_9_3 = slot_9_2 < slot_9_1 and slot_9_0 + (arg_9_0 - slot_9_0):norm() * slot_9_2 or arg_9_0
		local slot_9_4 = ove_0_18(slot_9_0, slot_9_3)

		if slot_9_4 then
			player:castSpell("pos", 0, vec3(slot_9_4.x, player.y, slot_9_4.y))
		end

		ove_0_13 = nil

		return true
	end
end

local function ove_0_23(arg_10_0)
	-- print 10
	if arg_10_0 == 17 then
		ove_0_12 = os.clock() + 0.5
	end
end

return {
	get_server_pos = ove_0_19,
	get_destination = ove_0_20,
	invoke_a = ove_0_21,
	invoke_b = ove_0_22,
	on_key_down = ove_0_23
}

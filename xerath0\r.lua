
local ove_0_5 = module.internal("TS")
local ove_0_6 = module.internal("orb")
local ove_0_7 = module.internal("pred")
local ove_0_8 = module.load(header.id,"xerath/menu")
local ove_0_9 = module.load(header.id,"xerath/r_buff")
local ove_0_10 = {
	last = 0,
	slot = player:spellSlot(3),
	result = {},
	range = {
		mouse = 1200,
		max = 0
	},
	predinput = {
		delay = 0.6,
		radius = 200,
		boundingRadiusMod = 0,
		speed = math.huge
	}
}

function ove_0_10.is_ready()
	-- print 1
	return ove_0_10.slot.state == 0
end

function ove_0_10.get_action_state()
	-- print 2
	if ove_0_9.isActive and ove_0_8.r2:get() and ove_0_10.is_ready() then
		return ove_0_10.get_prediction()
	end
end

function ove_0_10.invoke_action()
	-- print 3
	player:castSpell("pos", 3, vec3(ove_0_10.result.seg.endPos.x, mousePos.y, ove_0_10.result.seg.endPos.y))
end

function ove_0_10.ts_filter(arg_4_0, arg_4_1, arg_4_2)
	-- print 4
	if arg_4_2 < ove_0_10.range.max and arg_4_1.pos:distSqr(mousePos) < ove_0_10.range.mouse * ove_0_10.range.mouse then
		local slot_4_0 = ove_0_7.circular.get_prediction(ove_0_10.predinput, arg_4_1)

		if slot_4_0 and slot_4_0.startPos:dist(slot_4_0.endPos) < ove_0_10.range.max then
			arg_4_0.obj = arg_4_1
			arg_4_0.seg = slot_4_0

			return true
		end
	end
end

function ove_0_10.trace_filter()
	-- print 5
	if ove_0_8.r_block:get() then
		return false
	end

	if ove_0_8.r_fastpred:get() then
		return true
	end

	if ove_0_7.trace.circular.hardlock(ove_0_10.predinput, ove_0_10.result.seg, ove_0_10.result.obj) then
		return true
	end

	if ove_0_7.trace.circular.hardlockmove(ove_0_10.predinput, ove_0_10.result.seg, ove_0_10.result.obj) then
		return true
	end

	if ove_0_7.trace.newpath(ove_0_10.result.obj, 0.033, 0.5) then
		return true
	end
end

function ove_0_10.get_prediction()
	-- print 6
	ove_0_10.range.mouse = ove_0_8.r_radius:get()
	ove_0_10.range.max = 5000
	ove_0_10.result = ove_0_5.get_result(ove_0_10.ts_filter)

	if ove_0_10.result.seg and ove_0_10.trace_filter() then
		return ove_0_10.result
	end
end

return ove_0_10

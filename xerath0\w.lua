
local ove_0_5 = module.internal("TS")
local ove_0_6 = module.internal("orb")
local ove_0_7 = module.internal("pred")
local ove_0_8 = module.load(header.id,"xerath/menu")
local ove_0_9 = {
	slot = player:spellSlot(1),
	range = {
		ignore = 1500,
		max = 990
	},
	edge = {
		last = 0,
		result = {},
		predinput = {
			delay = 0.75,
			radius = 273,
			boundingRadiusMod = 0,
			speed = math.huge
		}
	},
	center = {
		last = 0,
		result = {},
		predinput = {
			delay = 0.75,
			radius = 123,
			boundingRadiusMod = 0,
			speed = math.huge
		}
	}
}

function ove_0_9.is_ready()
	-- print 1
	return ove_0_9.slot.state == 0
end

function ove_0_9.edge.get_action_state()
	-- print 2
	if ove_0_9.is_ready() then
		return ove_0_9.edge.get_prediction()
	end
end

function ove_0_9.edge.invoke_action()
	-- print 3
	if player:castSpell("pos", 1, vec3(ove_0_9.edge.result.seg.endPos.x, mousePos.y, ove_0_9.edge.result.seg.endPos.y)) then
		ove_0_6.core.set_server_pause()
	end
end

function ove_0_9.edge.ts_filter(arg_4_0, arg_4_1, arg_4_2)
	-- print 4
	if arg_4_2 < ove_0_9.range.ignore then
		local slot_4_0 = ove_0_7.circular.get_prediction(ove_0_9.edge.predinput, arg_4_1)

		if slot_4_0 and slot_4_0.startPos:dist(slot_4_0.endPos) < ove_0_9.range.max then
			arg_4_0.obj = arg_4_1
			arg_4_0.seg = slot_4_0

			return true
		end
	end
end

function ove_0_9.edge.trace_filter()
	-- print 5
	local slot_5_0 = ove_0_9.edge.result.seg.startPos:dist(ove_0_9.edge.result.seg.endPos)
	local slot_5_1 = ove_0_9.range.max
	local slot_5_2 = ove_0_9.edge.result.seg.startPos:dist(ove_0_9.edge.result.obj.pos2D)

	if slot_5_1 < slot_5_0 then
		return false
	end

	if slot_5_1 < slot_5_2 then
		return false
	end

	if not ove_0_8.slowpred:get() then
		return true
	end

	if slot_5_0 < 450 then
		return true
	end

	if not ove_0_8.slowpred:get() then
		return true
	end

	if ove_0_7.trace.circular.hardlock(ove_0_9.edge.predinput, ove_0_9.edge.result.seg, ove_0_9.edge.result.obj) then
		return true
	end

	if ove_0_7.trace.circular.hardlockmove(ove_0_9.edge.predinput, ove_0_9.edge.result.seg, ove_0_9.edge.result.obj) then
		return true
	end

	if ove_0_7.trace.newpath(ove_0_9.edge.result.obj, 0.033, 0.5) then
		return true
	end
end

function ove_0_9.edge.get_prediction()
	-- print 6
	ove_0_9.edge.result = ove_0_5.get_result(ove_0_9.edge.ts_filter)

	if ove_0_9.edge.result.seg and ove_0_9.edge.trace_filter() then
		return ove_0_9.edge.result
	end
end

function ove_0_9.center.get_action_state()
	-- print 7
	if ove_0_9.is_ready() then
		return ove_0_9.center.get_prediction()
	end
end

function ove_0_9.center.invoke_action()
	-- print 8
	if player:castSpell("pos", 1, vec3(ove_0_9.center.result.seg.endPos.x, mousePos.y, ove_0_9.center.result.seg.endPos.y)) then
		ove_0_6.core.set_server_pause()
	end
end

function ove_0_9.center.ts_filter(arg_9_0, arg_9_1, arg_9_2)
	-- print 9
	if arg_9_2 < ove_0_9.range.ignore then
		local slot_9_0 = ove_0_7.circular.get_prediction(ove_0_9.center.predinput, arg_9_1)

		if slot_9_0 and slot_9_0.startPos:dist(slot_9_0.endPos) < ove_0_9.range.max then
			arg_9_0.obj = arg_9_1
			arg_9_0.seg = slot_9_0

			return true
		end
	end
end

function ove_0_9.center.trace_filter()
	-- print 10
	if ove_0_7.trace.circular.hardlock(ove_0_9.center.predinput, ove_0_9.center.result.seg, ove_0_9.center.result.obj) then
		return true
	end

	if ove_0_7.trace.circular.hardlockmove(ove_0_9.center.predinput, ove_0_9.center.result.seg, ove_0_9.center.result.obj) then
		return true
	end

	local slot_10_0 = ove_0_9.range.max

	if ove_0_9.center.result.seg.startPos:dist(ove_0_9.center.result.obj.pos2D) > slot_10_0 + 150 then
		return false
	end

	if not ove_0_8.slowpred:get() then
		return true
	end

	if ove_0_9.center.result.seg.startPos:dist(ove_0_9.center.result.seg.endPos) < 450 then
		return true
	end

	if not ove_0_8.slowpred:get() then
		return true
	end

	if ove_0_7.trace.newpath(ove_0_9.center.result.obj, 0.033, 0.5) then
		return true
	end
end

function ove_0_9.center.get_prediction()
	-- print 11
	ove_0_9.center.result = ove_0_5.get_result(ove_0_9.center.ts_filter)

	if ove_0_9.center.result.seg and ove_0_9.center.trace_filter() then
		return ove_0_9.center.result
	end
end

return ove_0_9

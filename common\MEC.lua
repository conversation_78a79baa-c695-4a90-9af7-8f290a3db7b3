

local ove_0_10 = module.load(header.id, "common/GeometryLib")

local function ove_0_11()
	local slot_5_0 = {}

	slot_5_0.__index = slot_5_0

	return setmetatable(slot_5_0, {
		__call = function(arg_6_0, ...)
			local slot_6_0 = setmetatable({}, slot_5_0)

			if slot_5_0.__init then
				slot_5_0.__init(slot_6_0, ...)
			end

			return slot_6_0
		end
	})
end

Circle = ove_0_11()

function Circle.__init(arg_7_0, arg_7_1, arg_7_2)
	arg_7_0.center = ove_0_10.Vector:new(arg_7_1) or ove_0_10.Vector:new()
	arg_7_0.radius = arg_7_2 or 0

	return arg_7_0
end

function Circle.Contains(arg_8_0, arg_8_1)
	return math.close(arg_8_0.center:DistanceTo(arg_8_1), arg_8_0.radius)
end

function Circle.__tostring(arg_9_0)
	return "{center: " .. tostring(arg_9_0.center) .. ", radius: " .. tostring(arg_9_0.radius) .. "}"
end

local function ove_0_12(arg_10_0, arg_10_1, arg_10_2)
	return ((arg_10_2.z or arg_10_2.y) - (arg_10_0.z or arg_10_0.y)) * (arg_10_1.x - arg_10_0.x) - ((arg_10_1.z or arg_10_1.y) - (arg_10_0.z or arg_10_0.y)) * (arg_10_2.x - arg_10_0.x)
end

local function ove_0_13(arg_11_0, arg_11_1, arg_11_2, arg_11_3)
	local slot_11_0 = arg_11_0.x
	local slot_11_1 = arg_11_0.z or arg_11_0.y
	local slot_11_2 = arg_11_1.x
	local slot_11_3 = arg_11_1.z or arg_11_1.y
	local slot_11_4 = arg_11_2.x
	local slot_11_5 = arg_11_2.z or arg_11_2.y
	local slot_11_6 = arg_11_3.x
	local slot_11_7 = arg_11_3.z or arg_11_3.y
	local slot_11_8 = slot_11_0 * slot_11_3 - slot_11_1 * slot_11_2
	local slot_11_9 = slot_11_4 * slot_11_7 - slot_11_5 * slot_11_6
	local slot_11_10 = slot_11_4 - slot_11_6
	local slot_11_11 = slot_11_0 - slot_11_2
	local slot_11_12 = slot_11_5 - slot_11_7
	local slot_11_13 = slot_11_1 - slot_11_3
	local slot_11_14 = slot_11_8 * slot_11_10 - slot_11_11 * slot_11_9
	local slot_11_15 = slot_11_8 * slot_11_12 - slot_11_13 * slot_11_9
	local slot_11_16 = slot_11_11 * slot_11_12 - slot_11_13 * slot_11_10

	return slot_11_16 ~= 0 and ove_0_10.Vector:new(slot_11_14 / slot_11_16, slot_11_15 / slot_11_16)
end

MEC = ove_0_11()

function MEC.__init(arg_12_0, arg_12_1)
	arg_12_0.circle = Circle()
	arg_12_0.points = {}

	if arg_12_1 then
		arg_12_0:SetPoints(arg_12_1)
	end

	return arg_12_0
end

function MEC.SetPoints(arg_13_0, arg_13_1)
	arg_13_0.points = {}

	for iter_13_0, iter_13_1 in ipairs(arg_13_1) do
		table.insert(arg_13_0.points, ove_0_10.Vector:new(iter_13_1))
	end
end

function MEC.HalfHull(arg_14_0, arg_14_1, arg_14_2, arg_14_3, arg_14_4)
	local slot_14_0 = arg_14_3

	table.insert(slot_14_0, arg_14_2)

	local slot_14_1 = {}

	table.insert(slot_14_1, arg_14_1)

	for iter_14_0, iter_14_1 in ipairs(slot_14_0) do
		table.insert(slot_14_1, iter_14_1)

		while #slot_14_1 >= 3 do
			if arg_14_4 * ove_0_12(slot_14_1[#slot_14_1 + 1 - 3], slot_14_1[#slot_14_1 + 1 - 1], slot_14_1[#slot_14_1 + 1 - 2]) <= 0 then
				table.remove(slot_14_1, #slot_14_1 - 1)
			else
				break
			end
		end
	end

	return slot_14_1
end

function MEC.ConvexHull(arg_15_0)
	local slot_15_0 = arg_15_0.points[1]
	local slot_15_1 = arg_15_0.points[#arg_15_0.points]
	local slot_15_2 = {}
	local slot_15_3 = {}
	local slot_15_4 = {}

	for iter_15_0 = 2, #arg_15_0.points - 1 do
		table.insert(ove_0_12(slot_15_0, slot_15_1, arg_15_0.points[iter_15_0]) < 0 and slot_15_2 or slot_15_3, arg_15_0.points[iter_15_0])
	end

	local slot_15_5 = arg_15_0:HalfHull(slot_15_0, slot_15_1, slot_15_2, -1)
	local slot_15_6 = arg_15_0:HalfHull(slot_15_0, slot_15_1, slot_15_3, 1)
	local slot_15_7 = {}

	for iter_15_1, iter_15_2 in ipairs(slot_15_5) do
		slot_15_7["x" .. iter_15_2.x .. "z" .. iter_15_2.z] = iter_15_2
	end

	for iter_15_3, iter_15_4 in ipairs(slot_15_6) do
		slot_15_7["x" .. iter_15_4.x .. "z" .. iter_15_4.z] = iter_15_4
	end

	for iter_15_5, iter_15_6 in pairs(slot_15_7) do
		table.insert(slot_15_4, iter_15_6)
	end

	return slot_15_4
end

function MEC.Compute(arg_16_0)
	if #arg_16_0.points == 0 then
		return nil
	end

	if #arg_16_0.points == 1 then
		arg_16_0.circle.center = arg_16_0.points[1]
		arg_16_0.circle.radius = 0
		arg_16_0.circle.radiusPoint = arg_16_0.points[1]
	elseif #arg_16_0.points == 2 then
		local slot_16_0 = arg_16_0.points

		arg_16_0.circle.center = slot_16_0[1]:center(slot_16_0[2])
		arg_16_0.circle.radius = slot_16_0[1]:distanceTo(arg_16_0.circle.center)
		arg_16_0.circle.radiusPoint = slot_16_0[1]
	else
		local slot_16_1 = arg_16_0:ConvexHull()
		local slot_16_2 = slot_16_1[1]
		local slot_16_3
		local slot_16_4 = slot_16_1[2]

		if not slot_16_4 then
			arg_16_0.circle.center = slot_16_2
			arg_16_0.circle.radius = 0
			arg_16_0.circle.radiusPoint = slot_16_2

			return arg_16_0.circle
		end

		while true do
			slot_16_3 = nil

			local slot_16_5 = 180

			for iter_16_0, iter_16_1 in ipairs(arg_16_0.points) do
				if not iter_16_1 == slot_16_2 and not iter_16_1 == slot_16_4 then
					local slot_16_6 = iter_16_1:AngleBetween(slot_16_2, slot_16_4)

					if slot_16_6 < slot_16_5 then
						slot_16_3 = iter_16_1
						slot_16_5 = slot_16_6
					end
				end
			end

			if slot_16_5 >= 90 or not slot_16_3 then
				arg_16_0.circle.center = slot_16_2:center(slot_16_4)
				arg_16_0.circle.radius = slot_16_2:distanceTo(arg_16_0.circle.center)
				arg_16_0.circle.radiusPoint = slot_16_2

				return arg_16_0.circle
			end

			local slot_16_7 = slot_16_4:AngleBetween(slot_16_3, slot_16_2)
			local slot_16_8 = slot_16_2:AngleBetween(slot_16_4, slot_16_3)

			if slot_16_7 > 90 then
				slot_16_4 = slot_16_3
			elseif slot_16_8 <= 90 then
				break
			else
				slot_16_2 = slot_16_3
			end
		end

		local slot_16_9 = (slot_16_3 - slot_16_2) * 0.5
		local slot_16_10 = (slot_16_4 - slot_16_2) * 0.5
		local slot_16_11 = slot_16_9:perpendicular2()
		local slot_16_12 = slot_16_10:perpendicular2()
		local slot_16_13 = slot_16_2 + slot_16_9
		local slot_16_14 = slot_16_2 + slot_16_10

		arg_16_0.circle.center = ove_0_13(slot_16_13, slot_16_11, slot_16_14, slot_16_12)
		arg_16_0.circle.radius = arg_16_0.circle.center:distanceTo(slot_16_2)
		arg_16_0.circle.radiusPoint = slot_16_2
	end

	return arg_16_0.circle
end

return MEC

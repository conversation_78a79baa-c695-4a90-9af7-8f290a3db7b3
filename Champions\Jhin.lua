local Curses = module.load("<PERSON>", "<PERSON><PERSON>");
local ove_0_20 = module.load("<PERSON>", "Libraries/Common")
local ove_0_21 = menu("<PERSON>", "[<PERSON>")
ove_0_21:header("load_header", " " .. player.charName .. " -")
local ove_0_22 = module.seek("evade")
local ove_0_23 = ove_0_20.EvadeVersion()
local ove_0_24 = module.internal("orb")
local ove_0_25 = module.internal("pred")
local ove_0_26 = module.internal("TS")
--local ove_0_27 = module.load("<PERSON>", "Misc/HealerAIO_InfoBox")
local ove_0_28 = module.load("<PERSON>", "Databases/Buffs")
local ove_0_29 = module.load("<PERSON>", "Libraries/Damage")



local ove_0_30 = {}
local ove_0_31 = {}
local ove_0_32 = 0

for iter_0_0 = 0, objManager.enemies_n - 1 do
	local ove_0_33 = objManager.enemies[iter_0_0]

	ove_0_30[ove_0_33.ptr] = ove_0_33.charName
	ove_0_31[ove_0_33.charName] = true
	ove_0_31[ove_0_33.charName .. "_ptr"] = ove_0_33.ptr
	ove_0_32 = ove_0_32 + 1
end

local ove_0_34 = 5

if game.mode == "ARAM" or game.mode == "URF" then
	ove_0_34 = 2
end

local ove_0_35 = 600
local ove_0_36 = {
	boundingRadiusMod = 1,
	range = 2500,
	width = 40,
	delay = 0.85,
	speed = math.huge,
	collision = {
		wall = true,
		hero = true,
		minion = false
	}
}
local ove_0_37 = {
	boundingRadiusMod = 1,
	range = 750,
	speed = 1600,
	delay = 1,
	radius = 100
}
local ove_0_38 = 3000
local ove_0_39 = {
	boundingRadiusMod = 1,
	range = 3500,
	speed = 5000,
	delay = 0.25,
	width = 80,
	collision = {
		wall = true,
		hero = false,
		minion = false
	}
}
local ove_0_40 = {
	boundingRadiusMod = 1,
	range = 3500,
	speed = 2500,
	delay = 0.25,
	width = 80,
	collision = {
		wall = true,
		hero = false,
		minion = false
	}
}
local ove_0_41 = 1100
local ove_0_42 = {
	[0] = "Q",
	"W",
	"E",
	[-1] = "P",
	[3] = "R"
}

ove_0_21:menu("combo", "Combo Settings")
ove_0_21.combo:header("orber", "Orb")
ove_0_21.combo:boolean("aa_cancel_reset", "Reset canceled auto attacks", true)
ove_0_21.combo:header("q", "Q Settings")
ove_0_21.combo:boolean("enableQ", "Enable Q", true)
ove_0_21.combo:dropdown("qmode", "Q Mode", 1, {
	"Always ",
	"Before 4th shot"
})
ove_0_21.combo.qmode:set("tooltip", "'Before 4th shot' also uses Q when reloading")
ove_0_21.combo:header("w", "W Settings")
ove_0_21.combo:boolean("enableW", "Enable W", true)
ove_0_21.combo:dropdown("wmode", "W Mode", 2, {
	"Always ",
	"On CC"
})
ove_0_21.combo:header("wAll", "--- W (Always) Settings ---")
ove_0_21.combo:header("wCC", "--- W (On CC) Settings ---")
ove_0_21.combo:boolean("Wmarked", "W only marked targets", true)
ove_0_21.combo:boolean("WCCslowed", "Allow W on slowed targets", false)
ove_0_21.combo:boolean("WoutofAA", "W only if outside AA range", true)
ove_0_21.combo.WoutofAA:set("tooltip", "Uses W on targets only if they are outside the AA range")
ove_0_21.combo:boolean("Wslow", "Use slow pred for W ", true)
ove_0_21.combo:slider("manaW", "If mana% above X use W", 30, 0, 101, 1)
ove_0_21.combo:header("e", "E Settings")
ove_0_21.combo:boolean("enableE", "Enable E", true)
ove_0_21.combo:dropdown("Emode", "E mode", 3, {
	"On CC",
	"Always ",
	"Optimized"
})
ove_0_21.combo:slider("manaE", "If mana% above X use E", 30, 0, 101, 1)
ove_0_21:menu("rset", "R Settings")
ove_0_21.rset:header("r", "R Settings")
ove_0_21.rset:boolean("enableR", "Enable R", true)
ove_0_21.rset:boolean("AutoRStart", "Auto R start", false)
ove_0_21.rset:slider("AutoRrange", "Auto R start range", 2500, 0, 3200, 10)
ove_0_21.rset.AutoRStart:set("tooltip", "Starts the ultimate automatically if the target is killable in X shots")
ove_0_21.rset:slider("autoRshotCount", "^- Start if dies in X shots", 2, 0, 4, 1)
ove_0_21.rset:boolean("autoRnearby", "Dont start R if enemies nearby", true)
ove_0_21.rset:dropdown("Rmode", "R mode (shooting)", 1, {
	"Automatic",
	"Spacebar"
})
ove_0_21.rset.Rmode:set("tooltip", "Automatic shoots automatically once R is casted, Spacebar shoots when its being pressed")
ove_0_21.rset:boolean("RslowPred", "Slow Predictions", false)
ove_0_21:menu("harass", "Harass Settings")
ove_0_21.harass:header("q", "Q Settings")
ove_0_21.harass:boolean("enableQ", "Enable Q", true)
ove_0_21.harass:dropdown("qmode", "Q Mode", 1, {
	"Always ",
	"Before 4th shot"
})
ove_0_21.harass:slider("manaQ", "If mana% above X use Q", 30, 0, 101, 1)
ove_0_21.harass:header("w", "W Settings")
ove_0_21.harass:boolean("enableW", "Enable W", true)
ove_0_21.harass:dropdown("wmode", "W Mode", 1, {
	"Always ",
	"On CC"
})
ove_0_21.harass:header("wAll", "--- W (Always) Settings ---")
ove_0_21.harass:header("wCC", "--- W (On CC) Settings ---")
ove_0_21.harass:boolean("Wmarked", "W only marked targets", true)
ove_0_21.harass:boolean("WCCslowed", "Allow W on slowed targets", true)
ove_0_21.harass:boolean("WoutofAA", "W only if outside AA range", true)
ove_0_21.harass.WoutofAA:set("tooltip", "Uses W on targets only if they are outside the AA range")
ove_0_21.harass:boolean("Wslow", "Use slow pred for W ", true)
ove_0_21.harass:slider("manaW", "If mana% above X use W", 30, 0, 101, 1)
ove_0_21.harass:header("e", "E Settings")
ove_0_21.harass:boolean("enableE", "Enable E", false)
ove_0_21.harass:dropdown("Emode", "E mode", 2, {
	"On CC",
	"Always ",
	"Optimized"
})
ove_0_21.harass:slider("manaE", "If mana% above X use E", 30, 0, 101, 1)
ove_0_21:menu("farming", "Farm Settings")
ove_0_21.farming:header("harassQlane", "Q Bounce Harass")
ove_0_21.farming:boolean("Qbounce", "Harass with Q bounce", true)
ove_0_21.farming:header("ok", "Farm Settings")
ove_0_21.farming:keybind("farmtog", "Farm toggle key:", nil, "Z")
ove_0_21.farming:menu("laneclear", "Lane Clear ")
ove_0_21.farming.laneclear:header("s", "Lane Clear Settings")
ove_0_21.farming.laneclear:boolean("laneQ", "Use Q ", true)
ove_0_21.farming.laneclear:boolean("FarmQReload", "Only Q when reloading", false)
ove_0_21.farming.laneclear.FarmQReload:set("tooltip", "Uses Q only when Jhin is reloading his gun")
ove_0_21.farming.laneclear:slider("laneQcount", "Q if can kill X minions", 3, 1, 4, 1)
ove_0_21.farming.laneclear:slider("lanemanaQ", "Q Mana% to stop farm", 30, 0, 101, 1)
ove_0_21.farming.laneclear:slider("laneQsearch", "Custom search range", 1000, 0, 1500, 1)
ove_0_21.farming.laneclear.laneQsearch:set("tooltip", "Search range for the best Q farm cast. Default value: 1000")
ove_0_21.farming:menu("jungleclear", "Jungle Clear ")
ove_0_21.farming.jungleclear:header("s", "Jungle Clear Settings")
ove_0_21.farming.jungleclear:boolean("jungQ", "Use Q ", true)
ove_0_21.farming.jungleclear:boolean("killJungQ", "^- Only to kill", false)
ove_0_21.farming.jungleclear:slider("jungmanaQ", "Q Mana% to stop farm", 30, 0, 101, 1)
ove_0_21.farming:header("assist", "Farm Assist Settings")
ove_0_21.farming:boolean("farmAssistQ", "Farm Assist Q", true)
ove_0_21.farming.farmAssistQ:set("tooltip", "Uses Q if the orbwalker wont be able to last hit the minion with AA")
ove_0_21.farming:slider("manaAssistQ", " ^- if mana above X %", 20, 0, 101, 1)
ove_0_21.farming:boolean("farmAssistW", "Farm Assist W", true)
ove_0_21.farming:slider("manaAssistW", " ^- if mana above X %", 70, 0, 101, 1)
ove_0_21.farming:boolean("assist_when_reloading", "Assist only when reloading", false)
ove_0_21.farming.assist_when_reloading:set("tooltip", "Activates farm assist only when Jhin is reloading his gun")
ove_0_21:menu("misc", "Misc Settings")
ove_0_21.misc:header("s", "Anti-Gapclose Settings")
ove_0_21.misc:boolean("GapW", "Use W on gapclosers ", true)
ove_0_21.misc:boolean("GapWAA", "Use only when out of AA range ", true)
ove_0_21.misc.GapWAA:set("tooltip", "Uses W on dashing targets only if they are outside the AA range")
ove_0_21.misc:boolean("GapWmarked", "Use W only if marked ", true)
ove_0_21.misc:menu("blacklist", "Anti-Gapclose Blacklist")

for iter_0_1 = 0, objManager.enemies_n - 1 do
	local ove_0_43 = objManager.enemies[iter_0_1]

	ove_0_21.misc.blacklist:boolean(ove_0_30[ove_0_43.ptr], "Block: " .. ove_0_30[ove_0_43.ptr], false)
end

ove_0_21.misc:header("ss", "Force W Settings")
ove_0_21.misc:keybind("forceWkey", "Force W key:", "MMB", nil)
ove_0_21.misc:boolean("forceWmarked", "^- only marked targets", false)
ove_0_21.misc:header("ss", "Trap W Settings")
ove_0_21.misc:boolean("autoTrap", "Enable W on traps ", true)
ove_0_21.misc:dropdown("autoTrap_mode", "Activation mode ", 1, {
	"Automatic ",
	"Combo key ",
	"Harass key ",
	"Lane Clear key"
})
ove_0_21.misc:slider("autoTrapMana", "Use W if mana above X %", 75, 0, 100, 1)
ove_0_21.misc:boolean("autoTrapAA", "Use W if no enemies nearby", true)
ove_0_21.misc:boolean("autoTrap_screen", "On-Screen W", false)
ove_0_21.misc.autoTrap_screen:set("tooltip", "Uses W only if the enemy is on your screen")
ove_0_21.misc:header("s", "Killsteal Settings")
ove_0_21.misc:boolean("enableKS", "Enable Killsteal", true)
ove_0_21.misc:boolean("ksq", "Killsteal Q", true)
ove_0_21.misc:boolean("ksw", "Killsteal W", true)
ove_0_21:menu("draws", "Draw Settings")
ove_0_21.draws:header("s", "Q Draw Settings")
ove_0_21.draws:boolean("drawq", "Draw Q Range", true)
ove_0_21.draws:color("colorq", "Q Color:", 0, 150, 80, 255)
ove_0_21.draws:header("w", "W Draw Settings")
ove_0_21.draws:boolean("draww", "Draw W Range", false)
ove_0_21.draws:boolean("drawwmini", "^- Draw on minimap?", false)
ove_0_21.draws:color("colorw", "W Color:", 0, 150, 80, 255)
ove_0_21.draws:header("e", "E Draw Settings")
ove_0_21.draws:boolean("drawe", "Draw E Range", true)
ove_0_21.draws:color("colore", "E Color:", 0, 150, 80, 255)
ove_0_21.draws:header("r", "R Draw Settings")
ove_0_21.draws:boolean("drawr", "Draw R Range", false)
ove_0_21.draws:boolean("drawrult", "Draw R when ulting", true)
ove_0_21.draws:boolean("drawrmini", "^- Draw on minimap?", true)
ove_0_21.draws:color("colorr", "R Color:", 0, 150, 80, 255)
ove_0_21.draws:header("ss", "Additional Draws")
ove_0_21.draws:boolean("drawtoggle", "Draw Farm Toggle", true)
ove_0_21.draws:menu("farmtog", "Farm Tog Settings")
ove_0_21.draws.farmtog:slider("RejSize", "Text Size", 18, 0, 30, 1)
ove_0_21.draws.farmtog:slider("RejX", "X position:", 20, -50, 50, 1)
ove_0_21.draws.farmtog:slider("RejY", "Y position:", 40, -50, 50, 1)
ove_0_21.draws.farmtog:color("colorRejON", "Color ON", 128, 255, 0, 255)
ove_0_21.draws.farmtog:color("colorRejOFF", "Color OFF", 165, 165, 165, 165)
ove_0_21.draws:boolean("drawsouls", "Draw best minion to Q", true)
ove_0_21.draws:menu("soulsdraw", "Additional Farm Q Settings")
ove_0_21.draws.soulsdraw:header("zeezee", "Farm Q Draw Settings")
ove_0_21.draws.soulsdraw:color("ColorCircle", "Circle Color", 150, 255, 150, 255)
ove_0_21.draws.soulsdraw:color("ColorText", "Text Color", 150, 255, 150, 255)
ove_0_21.draws.soulsdraw:color("ColorFrame", "Frame Color", 150, 255, 255, 255)
ove_0_21:menu("keys", "Key Settings")
ove_0_21.keys:header("keyhead", "Key Settings")
ove_0_21.keys:keybind("combokey", "Combo Key", "Space", nil)
ove_0_21.keys:keybind("harasskey", "Harass Key ", "C", nil)
ove_0_21.keys:keybind("clearkey", "Lane Clear Key", "V", nil)
ove_0_21.keys:keybind("lastkey", "Last Hit", "X", nil)
ove_0_21:header("headree", "- Healer AIO -")
ove_0_21:header("headree1", "- - Healer AIO - -")
ove_0_21:header("headree21", "- - - Healer AIO - - -")
ove_0_21:header("headree213", "Healer AIO")
ove_0_21.headree:set("visible", false)
ove_0_21.headree1:set("visible", false)
ove_0_21.headree21:set("visible", false)
ove_0_21.headree213:set("visible", false)

local function ove_0_44(arg_5_0, arg_5_1, arg_5_2, arg_5_3, arg_5_4)
	-- print 5
	if (arg_5_0 == 0 or arg_5_0 == 1 or arg_5_0 == 2 or arg_5_0 == 6 or arg_5_0 == 7 or arg_5_0 == 8 or arg_5_0 == 9 or arg_5_0 == 10 or arg_5_0 == 11) and player:spellSlot(3).name == "JhinRShot" then
		core.block_input()
	end
end

local ove_0_45 = {
	1.518,
	1.484,
	1.434,
	1.401,
	1.368,
	1.334,
	1.301,
	1.267,
	1.234,
	1.201,
	1.184,
	1.151,
	1.118,
	1.085,
	1.051,
	1.018,
	1.001,
	0.968
}
local ove_0_46 = 2.853

local function ove_0_47()
	-- print 6
	local slot_6_0 = ove_0_45[mathf.clamp(1, 18, player.levelRef)]

	if player.buff.jhinpassivereload then
		slot_6_0 = ove_0_46
	end

	return slot_6_0
end

local ove_0_48 = 0
local ove_0_49 = 0
local ove_0_50 = 0
local ove_0_51 = 0
local ove_0_52 = 0
local ove_0_53 = game.time
local ove_0_54

local function ove_0_55(arg_7_0)
	-- print 7
	local slot_7_0 = arg_7_0.owner

	if slot_7_0 and slot_7_0 == player then
		if arg_7_0.isBasicAttack then
			ove_0_53 = game.time + ove_0_24.utility.get_wind_up_time(player) + ove_0_24.utility.get_wind_up_time(player) / 4

			if arg_7_0.name:find("PassiveAttack") then
				ove_0_48 = game.time + ove_0_46
			else
				ove_0_48 = game.time + ove_0_45[mathf.clamp(1, 18, player.levelRef)]
			end
		end

		if arg_7_0.name == "JhinR" then
			ove_0_54 = (arg_7_0.endPos + player.pos) / 2
			ove_0_52 = game.time + 1
		end

		if arg_7_0.name == "JhinRShot" then
			ove_0_49 = game.time + 1.5
		end

		if arg_7_0.name == "SummonerFlash" then
			ove_0_51 = game.time + 1
		end
	end
end

local function ove_0_56(arg_8_0)
	-- print 8
	if not ove_0_54 then
		return false
	end

	local slot_8_0 = ove_0_39.range
	local slot_8_1 = 60 * mathf.PI / 180
	local slot_8_2 = (ove_0_20.VectorExtend(player.pos, ove_0_54, slot_8_0):to2D() - player.pos:to2D()):rotate(-slot_8_1 / 2)
	local slot_8_3 = slot_8_2:rotate(slot_8_1)
	local slot_8_4 = arg_8_0:to2D() - player.pos:to2D()

	if slot_8_4:distSqr(vec2(), true) < slot_8_0 * slot_8_0 and slot_8_2:cross(slot_8_4) > 0 and slot_8_4:cross(slot_8_3) > 0 then
		return true
	else
		return false
	end
end

local function ove_0_57(arg_9_0, arg_9_1, arg_9_2)
	-- print 9
	if arg_9_2 <= spellOrb.range then
		arg_9_0.obj = arg_9_1

		return true
	end
end

local function ove_0_58()
	-- print 10
	return ove_0_26.get_result(ove_0_57).obj
end

local function ove_0_59(arg_11_0, arg_11_1, arg_11_2)
	-- print 11
	if arg_11_2 <= ove_0_37.range then
		arg_11_0.obj = arg_11_1

		return true
	end
end

local function ove_0_60()
	-- print 12
	return ove_0_26.get_result(ove_0_59).obj
end

local function ove_0_61(arg_13_0, arg_13_1, arg_13_2)
	-- print 13
	if arg_13_2 <= ove_0_36.range then
		arg_13_0.obj = arg_13_1

		return true
	end
end

local function ove_0_62()
	-- print 14
	return ove_0_26.get_result(ove_0_61).obj
end

local function ove_0_63(arg_15_0, arg_15_1, arg_15_2)
	-- print 15
	if arg_15_2 <= ove_0_35 then
		arg_15_0.obj = arg_15_1

		return true
	end
end

local function ove_0_64()
	-- print 16
	return ove_0_26.get_result(ove_0_63).obj
end

local function ove_0_65(arg_17_0, arg_17_1, arg_17_2)
	-- print 17
	if arg_17_2 <= ove_0_38 then
		arg_17_0.obj = arg_17_1

		return true
	end
end

local function ove_0_66()
	-- print 18
	return ove_0_26.get_result(ove_0_65).obj
end

local function ove_0_67(arg_19_0, arg_19_1, arg_19_2)
	-- print 19
	if arg_19_2 <= ove_0_39.range then
		arg_19_0.obj = arg_19_1

		return true
	end
end

local function ove_0_68()
	-- print 20
	return ove_0_26.get_result(ove_0_67).obj
end

local function ove_0_69()
	-- print 21
	if not ove_0_20.IsRecalling(player) and player:spellSlot(1).state == 0 and ove_0_21.misc.GapW:get() then
		local slot_21_0 = ove_0_36.range * ove_0_36.range

		if ove_0_21.misc.GapWAA:get() and #ove_0_20.count_enemies_in_range(player.pos2D, 1000) >= 1 then
			return
		end

		for iter_21_0 = 0, objManager.enemies_n - 1 do
			local slot_21_1 = objManager.enemies[iter_21_0]

			if slot_21_1.type == TYPE_HERO and slot_21_1.team == TEAM_ENEMY and ove_0_21.misc.blacklist[ove_0_30[slot_21_1.ptr]] and not ove_0_21.misc.blacklist[ove_0_30[slot_21_1.ptr]]:get() and ove_0_20.IsValidTarget(slot_21_1) and slot_21_1.path.isActive and slot_21_1.path.isDashing and slot_21_0 >= player.pos2D:distSqr(slot_21_1.path.point2D[1]) then
				if ove_0_21.misc.GapWmarked:get() and not slot_21_1.buff.jhinespotteddebuff then
					return
				end

				local slot_21_2 = ove_0_25.linear.get_prediction(ove_0_36, slot_21_1)

				if slot_21_2 and slot_21_0 >= slot_21_2.startPos:distSqr(slot_21_2.endPos) and not ove_0_25.collision.get_prediction(ove_0_36, slot_21_2, slot_21_1) then
					player:castSpell("pos", 1, vec3(slot_21_2.endPos.x, slot_21_1.pos.y, slot_21_2.endPos.y))
				end
			end
		end
	end
end

local ove_0_70 = ove_0_20.DMG_RED_Baron()
local ove_0_71 = ove_0_20.DMG_RED_ElderDragonBurn()
local ove_0_72 = {
	45,
	70,
	95,
	120,
	145
}
local ove_0_73 = {
	0.35,
	0.425,
	0.5,
	0.575,
	0.65
}

local function ove_0_74(arg_22_0)
	-- print 22
	if arg_22_0.buff[ove_0_28.BUFF_ENUM_INVULNERABILITY] then
		return 0
	end

	local slot_22_0 = 0

	if player:spellSlot(0).level > 0 then
		slot_22_0 = ove_0_20.CalculatePhysicalDamage(arg_22_0, ove_0_72[player:spellSlot(0).level] + ove_0_20.GetTotalAD() * ove_0_73[player:spellSlot(0).level] + ove_0_20.GetTotalAP() * 0.6, player)
	end

	if arg_22_0.buff.exaltedwithbaronnashorminion then
		if math.floor(game.time / 60) <= 20 then
			if arg_22_0.charName:find("MinionRanged") then
				slot_22_0 = slot_22_0 * 0.5
			end

			if arg_22_0.charName:find("MinionMelee") then
				slot_22_0 = slot_22_0 * 0.5
			end
		end

		if math.floor(game.time / 60) >= 21 and not (math.floor(game.time / 60) >= 40) then
			if arg_22_0.charName:find("MinionRanged") then
				slot_22_0 = slot_22_0 * ove_0_70[math.floor(game.time / 60) - 20]
			end

			if arg_22_0.charName:find("MinionMelee") then
				slot_22_0 = slot_22_0 * ove_0_70[math.floor(game.time / 60) - 20]
			end
		end

		if math.floor(game.time / 60) >= 40 then
			if arg_22_0.charName:find("MinionRanged") then
				slot_22_0 = slot_22_0 * 0.3
			end

			if arg_22_0.charName:find("MinionMelee") then
				slot_22_0 = slot_22_0 * 0.3
			end
		end
	end

	return slot_22_0
end

local ove_0_75 = {
	60,
	95,
	130,
	165,
	200
}
local ove_0_76 = 0.75

local function ove_0_77(arg_23_0)
	-- print 23
	local slot_23_0 = mathf.clamp(1, 18, player.levelRef)
	local slot_23_1 = ove_0_20.DMG_RED_HarvestDmg(slot_23_0)
	local slot_23_2 = 0

	if player:spellSlot(1).level > 0 then
		local slot_23_3 = ove_0_75[player:spellSlot(1).level] + ove_0_20.GetTotalAD() * 0.5

		if arg_23_0.type == TYPE_MINION and arg_23_0.team ~= TEAM_NEUTRAL then
			slot_23_3 = slot_23_3 * ove_0_76
		end

		slot_23_2 = ove_0_20.CalculatePhysicalDamage(arg_23_0, slot_23_3, player)
	end

	if arg_23_0.type ~= TYPE_HERO then
		return slot_23_2
	end

	if player.buff["assets/perks/styles/domination/darkharvest/darkharvest.lua"] and (arg_23_0.health + arg_23_0.healthRegenRate * 0.8) / arg_23_0.maxHealth * 100 < 50 and not player.buff["assets/perks/styles/domination/darkharvest/darkharvestcooldown.lua"] and slot_23_0 > 0 then
		if ove_0_20.GetBonusAD() >= ove_0_20.GetTotalAP() then
			slot_23_2 = slot_23_2 + ove_0_20.CalculatePhysicalDamage(arg_23_0, slot_23_1 + ove_0_20.GetTotalAP() * 0.15 + ove_0_20.GetBonusAD() * 0.25 + player.buff["assets/perks/styles/domination/darkharvest/darkharvest.lua"].stacks2 * ove_0_34, player)
		else
			slot_23_2 = slot_23_2 + ove_0_20.CalculateMagicDamage(arg_23_0, slot_23_1 + ove_0_20.GetTotalAP() * 0.15 + ove_0_20.GetBonusAD() * 0.25 + player.buff["assets/perks/styles/domination/darkharvest/darkharvest.lua"].stacks2 * ove_0_34, player)
		end
	end

	if ove_0_31.Yasuo and arg_23_0.ptr == ove_0_31.Yasuo_ptr then
		slot_23_2 = slot_23_2 - ove_0_29.YasuoPassiveShield(arg_23_0, 20)
	end

	if arg_23_0.buff[ove_0_28.ChemtechDragonSoulBuff] then
		slot_23_2 = slot_23_2 * ove_0_29.ChemtechDragonSoulReductionMod()
	end

	if player.buff.elderdragonbuff then
		if math.floor(game.time / 60) < 25 and 90 - arg_23_0.healthRegenRate * 3.9000000000000004 > 0 then
			slot_23_2 = slot_23_2 + (90 - arg_23_0.healthRegenRate * 3.9000000000000004)
		end

		if math.floor(game.time / 60) >= 25 and not (math.floor(game.time / 60) > 45) and ove_0_71[math.floor(game.time / 60) - 24] - arg_23_0.healthRegenRate * 3.9000000000000004 > 0 then
			slot_23_2 = slot_23_2 + (ove_0_71[math.floor(game.time / 60) - 24] - arg_23_0.healthRegenRate * 3.9000000000000004)
		end

		if math.floor(game.time / 60) > 45 and 270 - arg_23_0.healthRegenRate * 3.9000000000000004 > 0 then
			slot_23_2 = slot_23_2 + (270 - arg_23_0.healthRegenRate * 3.9000000000000004)
		end
	end

	return slot_23_2
end

local ove_0_78 = {
	50,
	125,
	200
}
local ove_0_79 = 0.25

local function ove_0_80(arg_24_0)
	-- print 24
	local slot_24_0 = 0

	if player:spellSlot(3).level > 0 then
		slot_24_0 = ove_0_20.CalculatePhysicalDamage(arg_24_0, ove_0_78[player:spellSlot(3).level] + ove_0_20.GetTotalAD() * ove_0_79, player)
	end

	local slot_24_1 = slot_24_0 * ((100 - ove_0_20.GetPercentHealth(arg_24_0)) * 3 / 100 + 1)

	if ove_0_31.Yasuo and arg_24_0.ptr == ove_0_31.Yasuo_ptr then
		slot_24_1 = slot_24_1 - ove_0_29.YasuoPassiveShield(arg_24_0, 35)
	end

	if ove_0_31.Blitzcrank and arg_24_0.ptr == ove_0_31.Blitzcrank_ptr and arg_24_0.passiveCooldownEndTime - game.time <= 0.5 then
		slot_24_1 = slot_24_1 - arg_24_0.maxMana * 0.3
	end

	if arg_24_0.buff[ove_0_28.ChemtechDragonSoulBuff] then
		slot_24_1 = slot_24_1 * ove_0_29.ChemtechDragonSoulReductionMod()
	end

	if ove_0_31.Tryndamere and arg_24_0.buff.undyingrage then
		slot_24_1 = 0
	end

	if ove_0_31.Kindred and arg_24_0.buff.kindredrnodeathbuff then
		slot_24_1 = 0
	end

	if arg_24_0.buff[ove_0_28.BUFF_ENUM_INVULNERABILITY] or arg_24_0.buff[ove_0_28.BUFF_ENUM_SPELLSHIELD] then
		slot_24_1 = 0
	end

	if ove_0_31.Sion and arg_24_0.buff.sionpassivezombie then
		slot_24_1 = 0
	end

	return slot_24_1
end

local function ove_0_81(arg_25_0, arg_25_1, arg_25_2)
	-- print 25
	local slot_25_0 = 250000

	if ove_0_20.IsHardLockedLinearAPI(arg_25_0, arg_25_1, arg_25_2) then
		return true
	end

	if slot_25_0 >= arg_25_1.startPos:distSqr(arg_25_1.endPos) then
		return true
	end

	if ove_0_25.trace.newpath(arg_25_2, 0.033, 1) then
		return true
	end
end

local function ove_0_82()
	-- print 26
	if ove_0_21.combo.enableQ:get() and player:spellSlot(0).state == 0 then
		local slot_26_0 = ove_0_64()

		if ove_0_20.IsValidTarget(slot_26_0) then
			if ove_0_21.combo.qmode:get() == 2 and not player.buff.jhinpassiveattackbuff and not player.buff.jhinpassivereload then
				return
			end

			local slot_26_1 = ove_0_48 - game.time

			if ove_0_24.core.can_action() and (slot_26_1 >= 0.25 or slot_26_1 < 0) then
				if not ove_0_24.core.can_attack() then
					player:castSpell("obj", 0, slot_26_0)
				else
					local slot_26_2 = 0.25 + ove_0_24.utility.get_wind_up_time(player)
					local slot_26_3 = ove_0_25.core.get_pos_after_time(slot_26_0, slot_26_2)
					local slot_26_4 = player.attackRange + player.boundingRadius
					local slot_26_5 = slot_26_4 * slot_26_4

					if slot_26_3 and slot_26_5 >= player.pos2D:distSqr(slot_26_3) then
						player:castSpell("obj", 0, slot_26_0)
					end
				end
			end
		end
	end
end

local ove_0_83 = {
	ove_0_28.BUFF_ENUM_STUN,
	ove_0_28.BUFF_ENUM_TAUNT,
	ove_0_28.BUFF_ENUM_SNARE,
	ove_0_28.BUFF_ENUM_FEAR,
	ove_0_28.BUFF_ENUM_CHARM,
	ove_0_28.BUFF_ENUM_SUPPRESSION,
	ove_0_28.BUFF_ENUM_KNOCKUP,
	ove_0_28.BUFF_ENUM_KNOCKBACK,
	ove_0_28.BUFF_ENUM_ASLEEP
}

local function ove_0_84()
	-- print 27
	if ove_0_21.combo.enableW:get() and player:spellSlot(1).state == 0 and ove_0_21.combo.manaW:get() <= ove_0_20.GetPercentMana(player) then
		if ove_0_21.combo.WoutofAA:get() and #ove_0_20.count_enemies_in_range(player.pos2D, ove_0_20.GetAARange()) >= 1 then
			return
		end

		local slot_27_0 = ove_0_62()

		if ove_0_20.IsValidTarget(slot_27_0) then
			if ove_0_21.combo.wmode:get() == 2 and (not ove_0_21.combo.WCCslowed:get() or not ove_0_20.HasBuffType(ove_0_28.BUFF_ENUM_SLOW, slot_27_0)) and not ove_0_20.IsCustomLocked(ove_0_83, slot_27_0) then
				return
			end

			if ove_0_21.combo.Wmarked:get() and not slot_27_0.buff.jhinespotteddebuff then
				return
			end

			local slot_27_1 = ove_0_25.linear.get_prediction(ove_0_36, slot_27_0)
			local slot_27_2 = ove_0_36.range * ove_0_36.range

			if slot_27_1 and slot_27_2 >= slot_27_1.startPos:distSqr(slot_27_1.endPos) and not ove_0_25.collision.get_prediction(ove_0_36, slot_27_1, slot_27_0) then
				if ove_0_21.combo.Wslow and not ove_0_81(ove_0_36, slot_27_1, slot_27_0) then
					return
				end

				if ove_0_24.core.can_action() then
					local slot_27_3 = ove_0_48 - game.time

					if not ove_0_21.combo.WoutofAA:get() and not (slot_27_3 >= 0.8) and not (slot_27_3 < 0) and #ove_0_20.count_enemies_in_range(player.pos2D, ove_0_20.GetAARange()) >= 1 then
						return
					end

					if not ove_0_21.combo.WoutofAA:get() and slot_27_3 < 0 and #ove_0_20.count_enemies_in_range(player.pos2D, ove_0_20.GetAARange()) >= 1 then
						return
					end

					player:castSpell("pos", 1, vec3(slot_27_1.endPos.x, slot_27_0.pos.y, slot_27_1.endPos.y))
				end
			end
		end
	end
end

local function ove_0_85()
	-- print 28
	if ove_0_21.combo.enableE:get() and player:spellSlot(2).state == 0 and ove_0_21.combo.manaE:get() <= ove_0_20.GetPercentMana(player) then
		local slot_28_0 = ove_0_60()

		if ove_0_20.IsValidTarget(slot_28_0) then
			local slot_28_1 = ove_0_37.range * ove_0_37.range

			if (ove_0_21.combo.Emode:get() == 1 or ove_0_21.combo.Emode:get() == 3) and not ove_0_20.IsCustomLocked(ove_0_83, slot_28_0, 1) then
				return
			end

			local slot_28_2 = ove_0_25.circular.get_prediction(ove_0_37, slot_28_0)
			local slot_28_3 = ove_0_48 - game.time

			if slot_28_2 and slot_28_1 >= slot_28_2.startPos:distSqr(slot_28_2.endPos) and ove_0_24.core.can_action() and (slot_28_3 >= 0.25 or slot_28_3 < 0) and player:spellSlot(0).state ~= 0 then
				player:castSpell("pos", 2, vec3(slot_28_2.endPos.x, slot_28_0.pos.y, slot_28_2.endPos.y))
			end

			if ove_0_21.combo.Emode:get() == 3 and slot_28_0.path.count ~= 0 and player:spellSlot(1).state == 0 and player.buff.jhinpassivereload then
				local slot_28_4 = ove_0_25.circular.get_prediction(ove_0_37, slot_28_0)

				if slot_28_4 and slot_28_1 >= slot_28_4.startPos:distSqr(slot_28_4.endPos) and ove_0_24.core.can_action() and (slot_28_3 >= 0.25 or slot_28_3 < 0) and player:spellSlot(0).state ~= 0 then
					player:castSpell("pos", 2, vec3(slot_28_4.endPos.x, slot_28_0.pos.y, slot_28_4.endPos.y))
				end
			end
		end
	end
end

local function ove_0_86()
	-- print 29
	if ove_0_21.rset.enableR:get() and ove_0_21.rset.AutoRStart:get() and player:spellSlot(3).state == 0 and player:spellSlot(3).name ~= "JhinRShot" then
		if ove_0_21.rset.autoRnearby:get() and #ove_0_20.count_enemies_in_range(player.pos2D, ove_0_41) >= 1 then
			return
		end

		local slot_29_0 = ove_0_66()

		if ove_0_20.IsValidTarget(slot_29_0) and not slot_29_0.isZombie and ove_0_20.GetShieldedHealth("ALL", slot_29_0) - slot_29_0.magicalShield <= ove_0_80(slot_29_0) * ove_0_21.rset.autoRshotCount:get() then
			local slot_29_1 = ove_0_25.linear.get_prediction(ove_0_39, slot_29_0)
			local slot_29_2 = ove_0_38 * ove_0_38

			if slot_29_1 and slot_29_2 >= slot_29_1.startPos:distSqr(slot_29_1.endPos) then
				player:castSpell("pos", 3, vec3(slot_29_1.endPos.x, slot_29_0.pos.y, slot_29_1.endPos.y))
			end
		end
	end
end

local function ove_0_87()
	-- print 30
	ove_0_82()
	ove_0_84()
	ove_0_85()
	ove_0_86()
end

local function ove_0_88()
	-- print 31
	if ove_0_21.harass.enableQ:get() and player:spellSlot(0).state == 0 and ove_0_21.harass.manaQ:get() <= ove_0_20.GetPercentMana(player) then
		local slot_31_0 = ove_0_64()

		if ove_0_20.IsValidTarget(slot_31_0) then
			if ove_0_21.harass.qmode:get() == 2 and not player.buff.jhinpassiveattackbuff and not player.buff.jhinpassivereload then
				return
			end

			if ove_0_24.core.can_action() then
				if not ove_0_24.core.can_attack() then
					player:castSpell("obj", 0, slot_31_0)
				else
					local slot_31_1 = 0.25 + ove_0_24.utility.get_wind_up_time(player)
					local slot_31_2 = ove_0_25.core.get_pos_after_time(slot_31_0, slot_31_1)
					local slot_31_3 = player.attackRange + player.boundingRadius
					local slot_31_4 = slot_31_3 * slot_31_3

					if slot_31_2 and slot_31_4 >= player.pos2D:distSqr(slot_31_2) then
						player:castSpell("obj", 0, slot_31_0)
					end
				end
			end
		end
	end
end

local function ove_0_89()
	-- print 32
	if ove_0_21.harass.enableW:get() and player:spellSlot(1).state == 0 and ove_0_21.harass.manaW:get() <= ove_0_20.GetPercentMana(player) then
		if ove_0_21.harass.WoutofAA:get() and #ove_0_20.count_enemies_in_range(player.pos2D, ove_0_20.GetAARange()) >= 1 then
			return
		end

		local slot_32_0 = ove_0_62()

		if ove_0_20.IsValidTarget(slot_32_0) then
			if ove_0_21.harass.wmode:get() == 2 and (not ove_0_21.harass.WCCslowed:get() or not ove_0_20.HasBuffType(ove_0_28.BUFF_ENUM_SLOW, slot_32_0)) and not ove_0_20.IsCustomLocked(ove_0_83, slot_32_0) then
				return
			end

			if ove_0_21.harass.Wmarked:get() and not slot_32_0.buff.jhinespotteddebuff then
				return
			end

			local slot_32_1 = ove_0_25.linear.get_prediction(ove_0_36, slot_32_0)
			local slot_32_2 = ove_0_36.range * ove_0_36.range

			if slot_32_1 and slot_32_2 >= slot_32_1.startPos:distSqr(slot_32_1.endPos) and not ove_0_25.collision.get_prediction(ove_0_36, slot_32_1, slot_32_0) then
				if ove_0_21.harass.Wslow and not ove_0_81(ove_0_36, slot_32_1, slot_32_0) then
					return
				end

				if ove_0_24.core.can_action() then
					player:castSpell("pos", 1, vec3(slot_32_1.endPos.x, slot_32_0.pos.y, slot_32_1.endPos.y))
				end
			end
		end
	end
end

local function ove_0_90()
	-- print 33
	if ove_0_21.harass.enableE:get() and player:spellSlot(2).state == 0 and ove_0_21.harass.manaE:get() <= ove_0_20.GetPercentMana(player) then
		local slot_33_0 = ove_0_60()

		if ove_0_20.IsValidTarget(slot_33_0) then
			local slot_33_1 = ove_0_37.range * ove_0_37.range

			if (ove_0_21.harass.Emode:get() == 1 or ove_0_21.harass.Emode:get() == 3) and not ove_0_20.IsCustomLocked(ove_0_83, slot_33_0, 1) then
				return
			end

			local slot_33_2 = ove_0_25.circular.get_prediction(ove_0_37, slot_33_0)

			if slot_33_2 and slot_33_1 >= slot_33_2.startPos:distSqr(slot_33_2.endPos) and ove_0_24.core.can_action() then
				player:castSpell("pos", 2, vec3(slot_33_2.endPos.x, slot_33_0.pos.y, slot_33_2.endPos.y))
			end

			if ove_0_21.harass.Emode:get() == 3 and slot_33_0.path.count ~= 0 and player:spellSlot(1).state == 0 and player.buff.jhinpassivereload then
				local slot_33_3 = ove_0_25.circular.get_prediction(ove_0_37, slot_33_0)

				if slot_33_3 and slot_33_1 >= slot_33_3.startPos:distSqr(slot_33_3.endPos) and ove_0_24.core.can_action() then
					player:castSpell("pos", 2, vec3(slot_33_3.endPos.x, slot_33_0.pos.y, slot_33_3.endPos.y))
				end
			end
		end
	end
end

local function ove_0_91()
	-- print 34
	ove_0_88()
	ove_0_89()
	ove_0_90()
end

local function ove_0_92()
	-- print 35
	if not ove_0_21.farming.farmtog:get() then
		return
	end

	if ove_0_21.farming.jungleclear.jungQ:get() and player:spellSlot(0).state == 0 and ove_0_21.farming.jungleclear.jungmanaQ:get() <= ove_0_20.GetPercentMana(player) then
		local slot_35_0 = ove_0_35 * ove_0_35

		for iter_35_0 = 0, objManager.minions.size[TEAM_NEUTRAL] - 1 do
			local slot_35_1 = objManager.minions[TEAM_NEUTRAL][iter_35_0]

			if slot_35_1.isVisible and slot_35_1.moveSpeed > 0 and slot_35_1.isTargetable and not slot_35_1.isDead and slot_35_0 >= slot_35_1.pos2D:distSqr(player.pos2D) then
				if ove_0_21.farming.jungleclear.killJungQ:get() and ove_0_74(slot_35_1) < slot_35_1.health then
					return
				end

				if ove_0_24.core.can_action() then
					player:castSpell("obj", 0, slot_35_1)
				end
			end
		end
	end
end

local function ove_0_93(arg_36_0, arg_36_1)
	-- print 36
	if arg_36_0.startPos:distSqr(arg_36_0.endPos) > ove_0_39.range * ove_0_39.range then
		return false
	end

	if ove_0_20.IsHardLockedLinearAPI(ove_0_39, arg_36_0, arg_36_1) then
		return true
	end

	if ove_0_25.trace.newpath(arg_36_1, 0.033, 0.7) then
		return true
	end
end

local function ove_0_94(arg_37_0, arg_37_1, arg_37_2)
	-- print 37
	if arg_37_2 > ove_0_39.range then
		return false
	end

	if not ove_0_20.IsValidTarget(arg_37_1) then
		return false
	end

	if not ove_0_56(arg_37_1.pos) then
		return false
	end

	local slot_37_0 = ove_0_25.linear.get_prediction(ove_0_39, arg_37_1)

	if not slot_37_0 then
		return false
	end

	if ove_0_25.collision.get_prediction(ove_0_39, slot_37_0, arg_37_1) then
		return false
	end

	arg_37_0.pos = slot_37_0.endPos
	arg_37_0.obj = arg_37_1

	return true
end

local function ove_0_95(arg_38_0, arg_38_1, arg_38_2)
	-- print 38
	if arg_38_2 > ove_0_39.range then
		return false
	end

	if not ove_0_20.IsValidTarget(arg_38_1) then
		return false
	end

	if arg_38_1.isZombie then
		return false
	end

	if not ove_0_56(arg_38_1.pos) then
		return false
	end

	local slot_38_0 = ove_0_25.linear.get_prediction(ove_0_39, arg_38_1)

	if not slot_38_0 then
		return false
	end

	if ove_0_21.rset.RslowPred:get() and not arg_38_1.buff.jhinrslow and not ove_0_93(slot_38_0, arg_38_1) then
		return false
	end

	if ove_0_25.collision.get_prediction(ove_0_39, slot_38_0, arg_38_1) then
		return false
	end

	arg_38_0.pos = slot_38_0.endPos
	arg_38_0.obj = arg_38_1

	return true
end

local function ove_0_96()
	-- print 39
	if ove_0_21.rset.enableR:get() then
		if ove_0_21.rset.Rmode:get() == 2 and not ove_0_21.keys.combokey:get() then
			return
		end

		if player:spellSlot(3).state == 0 and player:spellSlot(3).name == "JhinRShot" then
			local slot_39_0 = ove_0_26.get_result(ove_0_95)

			if slot_39_0.pos and slot_39_0.obj then
				if not slot_39_0.obj.buff.jhinrslow then
					player:castSpell("pos", 3, vec3(slot_39_0.pos.x, slot_39_0.obj.pos.y, slot_39_0.pos.y))
				elseif player.pos:dist(slot_39_0.obj.pos) / ove_0_39.speed + ove_0_39.delay > slot_39_0.obj.buff.jhinrslow.endTime - game.time then
					local slot_39_1 = ove_0_25.linear.get_prediction(ove_0_40, slot_39_0.obj)

					if slot_39_1 and not ove_0_25.collision.get_prediction(ove_0_40, slot_39_1, slot_39_0.obj) then
						player:castSpell("pos", 3, vec3(slot_39_1.endPos.x, slot_39_0.obj.pos.y, slot_39_1.endPos.y))
					end
				else
					player:castSpell("pos", 3, vec3(slot_39_0.pos.x, slot_39_0.obj.pos.y, slot_39_0.pos.y))
				end
			end
		end
	end
end

local function ove_0_97()
	-- print 40
	if not menu:isopen() then
		return
	end

	if ove_0_21.combo.wmode:get() == 1 then
		ove_0_21.combo.wAll:set("visible", true)
		ove_0_21.combo.Wmarked:set("visible", true)
	end

	if ove_0_21.combo.wmode:get() ~= 1 then
		ove_0_21.combo.wAll:set("visible", false)
		ove_0_21.combo.Wmarked:set("visible", false)
	end

	if ove_0_21.combo.wmode:get() == 2 then
		ove_0_21.combo.wCC:set("visible", true)
		ove_0_21.combo.WCCslowed:set("visible", true)
	end

	if ove_0_21.combo.wmode:get() ~= 2 then
		ove_0_21.combo.wCC:set("visible", false)
		ove_0_21.combo.WCCslowed:set("visible", false)
	end

	if ove_0_21.harass.wmode:get() == 1 then
		ove_0_21.harass.wAll:set("visible", true)
		ove_0_21.harass.Wmarked:set("visible", true)
	end

	if ove_0_21.harass.wmode:get() ~= 1 then
		ove_0_21.harass.wAll:set("visible", false)
		ove_0_21.harass.Wmarked:set("visible", false)
	end

	if ove_0_21.harass.wmode:get() == 2 then
		ove_0_21.harass.wCC:set("visible", true)
		ove_0_21.harass.WCCslowed:set("visible", true)
	end

	if ove_0_21.harass.wmode:get() ~= 2 then
		ove_0_21.harass.wCC:set("visible", false)
		ove_0_21.harass.WCCslowed:set("visible", false)
	end
end

local function ove_0_98()
	-- print 41
	if player:spellSlot(3).name == "JhinRShot" or ove_0_52 > game.time then
		if ove_0_23 ~= 0 then
			ove_0_22.core.set_pause(0.1)
		end

		ove_0_24.core.set_pause_attack(0.1)
		ove_0_24.core.set_pause_move(0.1)
	end

	if player.buff.jhinpassivereload then
		ove_0_24.core.set_pause_attack(0.05)
	end
end

local function ove_0_99()
	-- print 42
	if not ove_0_21.misc.autoTrap:get() then
		return
	end

	if ove_0_21.misc.autoTrap_mode:get() == 2 and not ove_0_21.keys.combokey:get() then
		return
	end

	if ove_0_21.misc.autoTrap_mode:get() == 3 and not ove_0_21.keys.harasskey:get() then
		return
	end

	if ove_0_21.misc.autoTrap_mode:get() == 4 and not ove_0_21.keys.clearkey:get() then
		return
	end

	if player:spellSlot(1).state ~= 0 then
		return
	end

	if ove_0_21.misc.autoTrapMana:get() <= ove_0_20.GetPercentMana(player) then
		if ove_0_21.misc.autoTrapAA:get() and #ove_0_20.count_enemies_in_range(player.pos2D, 800) >= 1 then
			return
		end

		local slot_42_0 = ove_0_36.range * ove_0_36.range

		for iter_42_0 = 0, objManager.enemies_n - 1 do
			local slot_42_1 = objManager.enemies[iter_42_0]

			if ove_0_20.IsValidTarget(slot_42_1) and slot_42_0 >= slot_42_1.pos2D:distSqr(player.pos2D) and slot_42_1.buff.jhinetrapslow and (ove_0_21.misc.autoTrap_screen:get() and slot_42_1.isOnScreen or not ove_0_21.misc.autoTrap_screen:get()) then
				local slot_42_2 = ove_0_25.linear.get_prediction(ove_0_36, slot_42_1)

				if slot_42_2 and slot_42_0 >= slot_42_2.startPos:distSqr(slot_42_2.endPos) and not ove_0_25.collision.get_prediction(ove_0_36, slot_42_2, slot_42_1) and ove_0_24.core.can_action() then
					player:castSpell("pos", 1, vec3(slot_42_2.endPos.x, slot_42_1.pos.y, slot_42_2.endPos.y))
				end
			end
		end
	end
end

local function ove_0_100()
	-- print 43
	local slot_43_0 = ove_0_35 * ove_0_35

	if ove_0_21.misc.ksq:get() and player:spellSlot(0).state == 0 then
		for iter_43_0 = 0, objManager.enemies_n - 1 do
			local slot_43_1 = objManager.enemies[iter_43_0]

			if ove_0_20.IsValidTarget(slot_43_1) and not slot_43_1.isZombie and slot_43_0 >= slot_43_1.pos2D:distSqr(player.pos2D) and ove_0_20.GetShieldedHealth("ALL", slot_43_1) - slot_43_1.magicalShield < ove_0_74(slot_43_1) and ove_0_24.core.can_action() then
				player:castSpell("obj", 0, slot_43_1)
			end
		end
	end
end

local function ove_0_101()
	-- print 44
	local slot_44_0 = ove_0_36.range * ove_0_36.range

	if ove_0_21.misc.ksw:get() and player:spellSlot(1).state == 0 and #ove_0_20.count_enemies_in_range(player.pos2D, 850) == 0 then
		for iter_44_0 = 0, objManager.enemies_n - 1 do
			local slot_44_1 = objManager.enemies[iter_44_0]

			if ove_0_20.IsValidTarget(slot_44_1) and not slot_44_1.isZombie and slot_44_0 >= slot_44_1.pos2D:distSqr(player.pos2D) then
				local slot_44_2 = ove_0_20.GetShieldedHealth("ALL", slot_44_1) - slot_44_1.magicalShield

				if slot_44_2 < ove_0_77(slot_44_1) or player.buff.elderdragonbuff and player.buff.elderdragonbuff.endTime - game.time > 0.8 and (slot_44_2 - ove_0_77(slot_44_1)) / slot_44_1.maxHealth * 100 < 20 then
					local slot_44_3 = ove_0_25.linear.get_prediction(ove_0_36, slot_44_1)

					if slot_44_3 and slot_44_0 >= slot_44_3.startPos:distSqr(slot_44_3.endPos) and not ove_0_25.collision.get_prediction(ove_0_36, slot_44_3, slot_44_1) and ove_0_24.core.can_action() then
						player:castSpell("pos", 1, vec3(slot_44_3.endPos.x, slot_44_1.pos.y, slot_44_3.endPos.y))
					end
				end
			end
		end
	end
end

local function ove_0_102()
	-- print 45
	if ove_0_20.IsRecalling(player) then
		return
	end

	if ove_0_21.misc.enableKS:get() then
		ove_0_100()
		ove_0_101()
	end

	ove_0_99()
end

local function ove_0_103()
	-- print 46
	if ove_0_21.misc.forceWkey:get() and player:spellSlot(1).state == 0 then
		local slot_46_0 = ove_0_36.range * ove_0_36.range

		if not ove_0_21.misc.forceWmarked:get() then
			local slot_46_1 = ove_0_62()

			if ove_0_20.IsValidTarget(slot_46_1) then
				local slot_46_2 = ove_0_25.linear.get_prediction(ove_0_36, slot_46_1)

				if slot_46_2 and slot_46_0 >= slot_46_2.startPos:distSqr(slot_46_2.endPos) and not ove_0_25.collision.get_prediction(ove_0_36, slot_46_2, slot_46_1) and ove_0_24.core.can_action() then
					player:castSpell("pos", 1, vec3(slot_46_2.endPos.x, slot_46_1.pos.y, slot_46_2.endPos.y))
				end
			end
		else
			for iter_46_0 = 0, objManager.enemies_n - 1 do
				local slot_46_3 = objManager.enemies[iter_46_0]

				if ove_0_20.IsValidTarget(slot_46_3) then
					local slot_46_4 = slot_46_3

					if slot_46_0 >= slot_46_4.pos2D:distSqr(player.pos2D) and slot_46_4.buff.jhinespotteddebuff then
						local slot_46_5 = ove_0_25.linear.get_prediction(ove_0_36, slot_46_4)

						if slot_46_5 and slot_46_0 >= slot_46_5.startPos:distSqr(slot_46_5.endPos) and not ove_0_25.collision.get_prediction(ove_0_36, slot_46_5, slot_46_4) and ove_0_24.core.can_action() then
							player:castSpell("pos", 1, vec3(slot_46_5.endPos.x, slot_46_4.pos.y, slot_46_5.endPos.y))
						end
					end
				end
			end
		end
	end
end

local function ove_0_104()
	-- print 47
	if not ove_0_21.farming.farmtog:get() then
		return
	end

	if ove_0_21.farming.assist_when_reloading:get() and not player.buff.jhinpassivereload then
		return
	end

	if ove_0_21.farming.farmAssistQ:get() and ove_0_20.GetPercentMana(player) >= ove_0_21.farming.manaAssistQ:get() then
		local slot_47_0 = {
			range = 610,
			speed = 1800,
			delay = 0.25,
			damage = function(arg_48_0)
				-- print 48
				return ove_0_74(arg_48_0)
			end
		}
		local slot_47_1, slot_47_2 = ove_0_24.farm.skill_farm_target(slot_47_0)

		if slot_47_1 and slot_47_2 and slot_47_2.ptr ~= 0 and player:spellSlot(0).state == 0 then
			player:castSpell("obj", 0, slot_47_2)
			ove_0_24.farm.set_ignore(slot_47_2)

			return true
		end
	end

	if ove_0_21.farming.farmAssistW:get() and player:spellSlot(0).state ~= 0 and player:spellSlot(1).state == 0 and ove_0_20.GetPercentMana(player) >= ove_0_21.farming.manaAssistW:get() and #ove_0_20.count_enemies_in_range(player.pos2D, 850) == 0 then
		local slot_47_3 = {
			boundingRadiusMod = 1,
			range = 800,
			width = 40,
			delay = 0.85,
			speed = math.huge,
			collision = {
				wall = true
			},
			damage = function(arg_49_0)
				-- print 49
				return ove_0_77(arg_49_0)
			end
		}
		local slot_47_4, slot_47_5 = ove_0_24.farm.skill_farm_linear(slot_47_3)

		if slot_47_4 and slot_47_5 and slot_47_5.ptr ~= 0 then
			player:castSpell("pos", 1, vec3(slot_47_4.endPos.x, slot_47_5.pos.y, slot_47_4.endPos.y))
			ove_0_24.farm.set_ignore(slot_47_5)

			return true
		end
	end
end

local function ove_0_105(arg_50_0)
	-- print 50
	local slot_50_0

	if arg_50_0 and arg_50_0.ptr ~= 0 then
		local slot_50_1 = 1000000

		for iter_50_0 = 0, objManager.minions.size[TEAM_ENEMY] - 1 do
			local slot_50_2 = objManager.minions[TEAM_ENEMY][iter_50_0]

			if slot_50_2.ptr ~= arg_50_0.ptr and slot_50_1 >= slot_50_2.pos2D:distSqr(player.pos2D) and ove_0_20.valid_minion(slot_50_2) then
				if slot_50_0 == nil then
					slot_50_0 = slot_50_2
				end

				if slot_50_2.pos2D:distSqr(arg_50_0.pos2D) < slot_50_0.pos2D:distSqr(arg_50_0.pos2D) then
					slot_50_0 = slot_50_2
				end

				if slot_50_0.pos2D:distSqr(arg_50_0.pos2D) > 211600 then
					slot_50_0 = nil
				end
			end
		end
	end

	return slot_50_0
end

local ove_0_106
local ove_0_107 = 1
local ove_0_108 = 0

cb.add(cb.create_missile, function(arg_51_0)
	-- print 51
	local slot_51_0 = arg_51_0.spell.owner

	if slot_51_0 and slot_51_0.ptr == player.ptr then
		if arg_51_0.name:find("BasicAttack") or arg_51_0.name:find("CritAttack") or arg_51_0.name:find("PassiveAttack") then
			ove_0_106 = arg_51_0
		end

		if arg_51_0.name:find("PassiveAttack") then
			ove_0_24.core.set_pause_attack(player.passiveCooldownTotalTime)
		end

		if arg_51_0.name == "JhinQ" then
			ove_0_107 = 1

			local slot_51_1 = arg_51_0.spell.target

			if slot_51_1 and slot_51_1.ptr ~= 0 and slot_51_1.type and slot_51_1.type == TYPE_MINION and slot_51_1.team and slot_51_1.team == TEAM_ENEMY and ove_0_20.IsValidTargetNoBuff(slot_51_1) then
				if slot_51_1.health < ove_0_74(slot_51_1) then
					ove_0_24.farm.set_ignore(slot_51_1)

					ove_0_108 = 1
				else
					ove_0_108 = 0
				end

				local slot_51_2 = ove_0_105(slot_51_1)

				if slot_51_2 and slot_51_2.ptr ~= 0 and slot_51_2.health < ove_0_74(slot_51_2) + (ove_0_108 >= 1 and 0.35 * ove_0_74(slot_51_1) * ove_0_108 or 0) then
					ove_0_24.farm.set_ignore(slot_51_2)
				end
			end
		end

		if arg_51_0.name == "JhinQMisBounce" then
			ove_0_107 = ove_0_107 + 1

			local slot_51_3 = arg_51_0.spell.target

			if slot_51_3 and slot_51_3.ptr ~= 0 and slot_51_3.type and slot_51_3.type == TYPE_MINION and slot_51_3.team and slot_51_3.team == TEAM_ENEMY and ove_0_20.IsValidTargetNoBuff(slot_51_3) then
				if slot_51_3.health < ove_0_74(slot_51_3) + (ove_0_108 >= 1 and 0.35 * ove_0_74(slot_51_3) * ove_0_108 or 0) then
					ove_0_24.farm.set_ignore(slot_51_3)

					ove_0_108 = ove_0_108 + 1
				end

				local slot_51_4 = ove_0_105(slot_51_3)

				if ove_0_107 <= 3 and slot_51_4 and slot_51_4.ptr ~= 0 and slot_51_4.health < ove_0_74(slot_51_4) + (ove_0_108 >= 1 and 0.35 * ove_0_74(slot_51_4) * ove_0_108 or 0) then
					ove_0_24.farm.set_ignore(slot_51_4)
				end
			end
		end
	end
end)
cb.add(cb.delete_missile, function(arg_52_0)
	-- print 52
	if ove_0_106 and arg_52_0.ptr == ove_0_106.ptr then
		ove_0_106 = nil
	end
end)

local function ove_0_109(arg_53_0)
	-- print 53
	local slot_53_0
	local slot_53_1
	local slot_53_2
	local slot_53_3 = {}
	local slot_53_4 = 0
	local slot_53_5 = {}
	local slot_53_6 = 0
	local slot_53_7 = {}
	local slot_53_8 = 0
	local slot_53_9 = ove_0_21.farming.laneclear.laneQsearch:get()
	local slot_53_10 = slot_53_9 * slot_53_9

	for iter_53_0 = 0, objManager.minions.size[TEAM_ENEMY] - 1 do
		local slot_53_11 = objManager.minions[TEAM_ENEMY][iter_53_0]

		if slot_53_10 >= slot_53_11.pos2D:distSqr(player.pos2D) and ove_0_20.valid_minion(slot_53_11) then
			slot_53_4 = slot_53_4 + 1
			slot_53_3[slot_53_4] = slot_53_11
			slot_53_6 = slot_53_6 + 1
			slot_53_5[slot_53_6] = slot_53_11
			slot_53_8 = slot_53_8 + 1
			slot_53_7[slot_53_8] = slot_53_11
		end
	end

	for iter_53_1 = 0, #slot_53_3 do
		local slot_53_12 = slot_53_3[iter_53_1]

		if slot_53_12 and slot_53_12.ptr ~= 0 and arg_53_0 and arg_53_0.ptr ~= 0 and slot_53_12.ptr ~= arg_53_0.ptr then
			if slot_53_0 == nil then
				slot_53_0 = slot_53_12
			end

			if slot_53_12.pos2D:distSqr(arg_53_0.pos2D) < slot_53_0.pos2D:distSqr(arg_53_0.pos2D) then
				slot_53_0 = slot_53_12
			end

			if slot_53_0.pos2D:distSqr(arg_53_0.pos2D) > 211600 then
				slot_53_0 = nil
			end
		end
	end

	if slot_53_0 and slot_53_0.ptr ~= 0 then
		for iter_53_2 = 0, #slot_53_5 do
			local slot_53_13 = slot_53_5[iter_53_2]

			if slot_53_13 and slot_53_13.ptr ~= 0 and arg_53_0 and arg_53_0.ptr ~= 0 and slot_53_13.ptr ~= arg_53_0.ptr and slot_53_13.ptr ~= slot_53_0.ptr then
				if slot_53_1 == nil then
					slot_53_1 = slot_53_13
				end

				if slot_53_13.pos2D:distSqr(slot_53_0.pos2D) < slot_53_1.pos2D:distSqr(slot_53_0.pos2D) then
					slot_53_1 = slot_53_13
				end

				if slot_53_1.pos2D:distSqr(slot_53_0.pos2D) > 211600 then
					slot_53_1 = nil
				end
			end
		end
	end

	if slot_53_1 and slot_53_1.ptr ~= 0 and slot_53_0 and slot_53_0.ptr ~= 0 then
		for iter_53_3 = 0, #slot_53_7 do
			local slot_53_14 = slot_53_7[iter_53_3]

			if slot_53_14 and slot_53_14.ptr ~= 0 and arg_53_0 and arg_53_0.ptr ~= 0 and slot_53_14.ptr ~= arg_53_0.ptr and slot_53_14.ptr ~= slot_53_0.ptr and slot_53_14.ptr ~= slot_53_1.ptr then
				if slot_53_2 == nil then
					slot_53_2 = slot_53_14
				end

				if slot_53_14.pos2D:distSqr(slot_53_1.pos2D) < slot_53_2.pos2D:distSqr(slot_53_1.pos2D) then
					slot_53_2 = slot_53_14
				end

				if slot_53_2.pos2D:distSqr(slot_53_1.pos2D) > 211600 then
					slot_53_2 = nil
				end
			end
		end
	end

	return slot_53_0, slot_53_1, slot_53_2
end

local function ove_0_110(arg_54_0)
	-- print 54
	local slot_54_0 = 0

	if arg_54_0 and arg_54_0.ptr ~= 0 then
		slot_54_0 = slot_54_0 + 1

		local slot_54_1, slot_54_2, slot_54_3 = ove_0_109(arg_54_0)

		if slot_54_1 and slot_54_1.ptr ~= 0 and ove_0_74(slot_54_1) + 0.35 * ove_0_74(arg_54_0) >= slot_54_1.health then
			slot_54_0 = slot_54_0 + 1
		end

		if slot_54_2 and slot_54_2.ptr ~= 0 and slot_54_1 and slot_54_1.ptr ~= 0 and ove_0_74(slot_54_2) + 0.35 * ove_0_74(slot_54_1) * 2 >= slot_54_2.health then
			slot_54_0 = slot_54_0 + 1
		end

		if slot_54_3 and slot_54_3.ptr ~= 0 and slot_54_2 and slot_54_2.ptr ~= 0 and slot_54_1 and slot_54_1.ptr ~= 0 and ove_0_74(slot_54_3) + 0.35 * ove_0_74(slot_54_2) * 3 >= slot_54_3.health then
			slot_54_0 = slot_54_0 + 1
		end
	end

	return slot_54_0
end

local function ove_0_111()
	-- print 55
	if player:spellSlot(0).state ~= 0 then
		return
	end

	if not ove_0_21.farming.Qbounce:get() then
		return
	end

	if ove_0_21.keys.clearkey:get() or ove_0_21.keys.lastkey:get() or ove_0_21.keys.harasskey:get() then
		for iter_55_0 = 0, objManager.enemies_n - 1 do
			local slot_55_0 = objManager.enemies[iter_55_0]

			if ove_0_20.IsValidTarget(slot_55_0) and slot_55_0.pos2D:distSqr(player.pos2D) <= 1440000 then
				local slot_55_1, slot_55_2, slot_55_3 = ove_0_109(slot_55_0)

				if slot_55_2 and slot_55_2.ptr ~= 0 and slot_55_1 and slot_55_1.ptr ~= 0 then
					if slot_55_1.health <= ove_0_74(slot_55_1) then
						if slot_55_2.health <= ove_0_74(slot_55_2) + 0.35 * ove_0_74(slot_55_2) then
							local slot_55_4 = ove_0_105(slot_55_2)

							if slot_55_4 and slot_55_4.ptr ~= 0 and slot_55_4.ptr == slot_55_1.ptr and slot_55_4.pos2D:distSqr(slot_55_2.pos2D) <= 122500 then
								ove_0_24.farm.set_ignore(slot_55_2, 0.033)

								if slot_55_2.pos2D:distSqr(player.pos2D) <= 640000 and ove_0_24.core.can_action() then
									player:castSpell("obj", 0, slot_55_2)
								end
							end
						end
					elseif slot_55_2.health <= ove_0_74(slot_55_2) then
						local slot_55_5 = ove_0_105(slot_55_2)

						if slot_55_5 and slot_55_5.ptr ~= 0 and slot_55_5.ptr == slot_55_1.ptr and slot_55_5.pos2D:distSqr(slot_55_2.pos2D) <= 122500 then
							ove_0_24.farm.set_ignore(slot_55_2, 0.033)

							if slot_55_2.pos2D:distSqr(player.pos2D) <= 640000 and ove_0_24.core.can_action() then
								player:castSpell("obj", 0, slot_55_2)
							end
						end
					end
				end

				if slot_55_1 and slot_55_1.ptr ~= 0 and slot_55_1.health <= ove_0_74(slot_55_1) then
					local slot_55_6 = ove_0_105(slot_55_1)

					if not slot_55_6 or slot_55_6 and slot_55_6.ptr ~= 0 and slot_55_1.pos2D:distSqr(slot_55_0.pos2D) < slot_55_6.pos2D:distSqr(slot_55_1.pos2D) then
						ove_0_24.farm.set_ignore(slot_55_1, 0.033)

						if slot_55_1.pos2D:distSqr(player.pos2D) <= 640000 and ove_0_24.core.can_action() then
							player:castSpell("obj", 0, slot_55_1)
						end
					end
				end
			end
		end
	end
end

local ove_0_112 = false
local ove_0_113

local function ove_0_114()
	-- print 56
	ove_0_112 = false
	ove_0_113 = nil

	local slot_56_0

	if not ove_0_21.farming.laneclear.laneQ:get() then
		return
	end

	if not ove_0_21.farming.farmtog:get() then
		return
	end

	if player:spellSlot(0).state ~= 0 then
		return
	end

	if ove_0_21.farming.laneclear.FarmQReload:get() and not player.buff.jhinpassivereload then
		return
	end

	local slot_56_1 = ove_0_21.farming.laneclear.laneQsearch:get()
	local slot_56_2 = slot_56_1 * slot_56_1

	for iter_56_0 = 0, objManager.minions.size[TEAM_ENEMY] - 1 do
		local slot_56_3 = objManager.minions[TEAM_ENEMY][iter_56_0]

		if ove_0_20.valid_minion(slot_56_3) and slot_56_2 >= slot_56_3.pos2D:distSqr(player.pos2D) and slot_56_3.health < ove_0_74(slot_56_3) then
			if slot_56_0 == nil then
				slot_56_0 = slot_56_3
			end

			local slot_56_4 = ove_0_110(slot_56_3)
			local slot_56_5 = ove_0_110(slot_56_0)

			if slot_56_5 < slot_56_4 then
				slot_56_0 = slot_56_3
			elseif slot_56_4 == slot_56_5 and slot_56_3.health < slot_56_0.health then
				slot_56_0 = slot_56_3
			end
		end
	end

	if slot_56_0 and slot_56_0.ptr ~= 0 then
		ove_0_113 = slot_56_0

		if ove_0_110(slot_56_0) >= ove_0_21.farming.laneclear.laneQcount:get() then
			ove_0_112 = true

			ove_0_24.farm.set_ignore(slot_56_0, 0.033)

			if (ove_0_21.keys.clearkey:get() or ove_0_21.keys.lastkey:get()) and slot_56_0.pos2D:distSqr(player.pos2D) <= 640000 and ove_0_24.core.can_action() then
				player:castSpell("obj", 0, slot_56_0)
			end
		end
	end
end

local ove_0_115 = false

local function ove_0_116()
	-- print 57
	if ove_0_53 >= game.time then
		if ove_0_106 then
			ove_0_115 = true
		end
	else
		if not ove_0_115 and not ove_0_24.core.can_attack() then
			ove_0_24.core.reset()

			ove_0_48 = 0
		end

		if ove_0_24.core.can_attack() then
			ove_0_115 = false
		end
	end
end

local ove_0_117 = 0

local function ove_0_118()
	-- print 58
	if ove_0_21.combo.aa_cancel_reset:get() then
		ove_0_116()
	end

	if ove_0_24.core.can_attack() then
		ove_0_50 = game.time
	else
		ove_0_117 = game.time
	end

	ove_0_98()
	ove_0_96()

	if player:spellSlot(3).name ~= "JhinRShot" then
		ove_0_102()
		ove_0_69()
	end

	if ove_0_21.keys.combokey:get() then
		ove_0_87()
	end

	if ove_0_21.keys.harasskey:get() then
		ove_0_91()
	end

	if ove_0_21.keys.clearkey:get() then
		ove_0_104()
		ove_0_92()
	end

	if ove_0_21.keys.lastkey:get() then
		ove_0_104()
		ove_0_92()
	end

	ove_0_111()
	ove_0_103()
	ove_0_114()
	ove_0_97()

	ove_0_38 = ove_0_21.rset.AutoRrange:get()

	if menu:isopen() then
		ove_0_20.MenuHeaderWizard(ove_0_21)
	end
end

local function ove_0_119()
	-- print 59
	if not ove_0_21.farming.farmtog:get() then
		return
	end

	if player:spellSlot(0).state ~= 0 then
		return
	end

	if ove_0_21.farming.laneclear.FarmQReload:get() and not player.buff.jhinpassivereload then
		return
	end

	local slot_59_0 = 0
	local slot_59_1 = 0

	if hanbot.language == 1 then
		slot_59_0 = 10
		slot_59_1 = -8
	end

	if hanbot.language == 2 then
		if graphics.get_font() == "Courier New" then
			slot_59_0 = 1
			slot_59_1 = -3
		end

		if graphics.get_font() == "Lucida Sans Unicode" then
			slot_59_0 = 0
			slot_59_1 = 0
		end

		if graphics.get_font() == "Gill Sans MT Pro Medium" then
			slot_59_0 = 0
			slot_59_1 = 0
		end
	end

	if ove_0_112 and ove_0_113 and ove_0_113.ptr ~= 0 then
		graphics.draw_circle(ove_0_113.pos, 30, 2, ove_0_21.draws.soulsdraw.ColorCircle:get(), 10)

		local slot_59_2 = graphics.world_to_screen(vec3(ove_0_113.x, ove_0_113.y, ove_0_113.z))

		if hanbot.language == 1 then
			graphics.draw_line_2D(slot_59_2.x + 2.5, slot_59_2.y - 22.5, slot_59_2.x + 15, slot_59_2.y - 40, 1.5, ove_0_21.draws.soulsdraw.ColorFrame:get())
			graphics.draw_line_2D(slot_59_2.x + 15, slot_59_2.y - 40, slot_59_2.x + 25, slot_59_2.y - 40, 1.5, ove_0_21.draws.soulsdraw.ColorFrame:get())
			graphics.draw_line_2D(slot_59_2.x + 32.5, slot_59_2.y - 49.5, slot_59_2.x + 25, slot_59_2.y - 40, 1.5, ove_0_21.draws.soulsdraw.ColorFrame:get())
			graphics.draw_line_2D(slot_59_2.x + 32.5, slot_59_2.y - 30.5, slot_59_2.x + 25, slot_59_2.y - 40, 1.5, ove_0_21.draws.soulsdraw.ColorFrame:get())
			graphics.draw_line_2D(slot_59_2.x + 32.5, slot_59_2.y - 30.5, slot_59_2.x + 90 + slot_59_0, slot_59_2.y - 30.5, 1.5, ove_0_21.draws.soulsdraw.ColorFrame:get())
			graphics.draw_line_2D(slot_59_2.x + 32.5, slot_59_2.y - 49.5, slot_59_2.x + 90 + slot_59_0, slot_59_2.y - 49.5, 1.5, ove_0_21.draws.soulsdraw.ColorFrame:get())
			graphics.draw_line_2D(slot_59_2.x + 90 + slot_59_0, slot_59_2.y - 49.5, slot_59_2.x + 97.5 + slot_59_0, slot_59_2.y - 40, 1.5, ove_0_21.draws.soulsdraw.ColorFrame:get())
			graphics.draw_line_2D(slot_59_2.x + 90 + slot_59_0, slot_59_2.y - 30.5, slot_59_2.x + 97.5 + slot_59_0, slot_59_2.y - 40, 1.5, ove_0_21.draws.soulsdraw.ColorFrame:get())
			graphics.draw_text_2D("Q Farm", 17, slot_59_2.x + 45 + slot_59_1, slot_59_2.y - 38.5, ove_0_21.draws.soulsdraw.ColorText:get())
		else
			graphics.draw_line_2D(slot_59_2.x + 2.5, slot_59_2.y - 22.5, slot_59_2.x + 15, slot_59_2.y - 40, 1.5, ove_0_21.draws.soulsdraw.ColorFrame:get())
			graphics.draw_line_2D(slot_59_2.x + 15, slot_59_2.y - 40, slot_59_2.x + 25, slot_59_2.y - 40, 1.5, ove_0_21.draws.soulsdraw.ColorFrame:get())
			graphics.draw_line_2D(slot_59_2.x + 32.5, slot_59_2.y - 47.5, slot_59_2.x + 25, slot_59_2.y - 40, 1.5, ove_0_21.draws.soulsdraw.ColorFrame:get())
			graphics.draw_line_2D(slot_59_2.x + 32.5, slot_59_2.y - 32.5, slot_59_2.x + 25, slot_59_2.y - 40, 1.5, ove_0_21.draws.soulsdraw.ColorFrame:get())
			graphics.draw_line_2D(slot_59_2.x + 32.5, slot_59_2.y - 32.5, slot_59_2.x + 90 + slot_59_0, slot_59_2.y - 32.5, 1.5, ove_0_21.draws.soulsdraw.ColorFrame:get())
			graphics.draw_line_2D(slot_59_2.x + 32.5, slot_59_2.y - 47.5, slot_59_2.x + 90 + slot_59_0, slot_59_2.y - 47.5, 1.5, ove_0_21.draws.soulsdraw.ColorFrame:get())
			graphics.draw_line_2D(slot_59_2.x + 90 + slot_59_0, slot_59_2.y - 47.5, slot_59_2.x + 97.5 + slot_59_0, slot_59_2.y - 40, 1.5, ove_0_21.draws.soulsdraw.ColorFrame:get())
			graphics.draw_line_2D(slot_59_2.x + 90 + slot_59_0, slot_59_2.y - 32.5, slot_59_2.x + 97.5 + slot_59_0, slot_59_2.y - 40, 1.5, ove_0_21.draws.soulsdraw.ColorFrame:get())
			graphics.draw_text_2D("Q Farm", 17, slot_59_2.x + 38, slot_59_2.y - 38.5, ove_0_21.draws.soulsdraw.ColorText:get())
		end
	end
end

local function ove_0_120()
	-- print 60
	-- if not ove_0_27.enable() then
		-- return
	-- end

	local slot_60_0 = {
		{
			position = 0.2,
			name = "Farming:",
			menu = ove_0_21.farming.farmtog
		},
		{
			position = -0.2,
			name = "Force W:",
			menu = ove_0_21.misc.forceWkey
		}
	}

	--ove_0_27.draw(slot_60_0)
end

local function ove_0_121()
	-- print 61
	local slot_61_0 = graphics.world_to_screen(vec3(player.x, player.y, player.z))

	if player.isOnScreen then
		if ove_0_21.draws.drawe:get() then
			graphics.draw_circle(player.pos, ove_0_37.range, 2, ove_0_21.draws.colore:get(), 80)
		end

		if ove_0_21.draws.drawq:get() then
			graphics.draw_circle(player.pos, 610, 2, ove_0_21.draws.colorq:get(), 80)
		end
	end

	if ove_0_21.draws.draww:get() and game.cameraPos and game.cameraPos:to2D():distSqr(player.pos2D) <= ove_0_36.range * 2 * (ove_0_36.range * 2) then
		graphics.draw_circle(player.pos, ove_0_36.range, 2, ove_0_21.draws.colorw:get(), 80)
	end

	if ove_0_21.draws.drawr:get() and game.cameraPos and game.cameraPos:to2D():distSqr(player.pos2D) <= ove_0_39.range * 2 * (ove_0_39.range * 2) then
		graphics.draw_circle(player.pos, ove_0_39.range, 2, ove_0_21.draws.colorr:get(), 80)
	end

	if ove_0_21.draws.drawrult:get() and player:spellSlot(3).name == "JhinRShot" and game.cameraPos and game.cameraPos:to2D():distSqr(player.pos2D) <= ove_0_39.range * 2 * (ove_0_39.range * 2) then
		graphics.draw_circle(player.pos, ove_0_39.range, 2, ove_0_21.draws.colorr:get(), 80)
	end

	if ove_0_21.draws.drawrmini:get() then
		minimap.draw_circle(player.pos, ove_0_39.range, 2, ove_0_21.draws.colorr:get(), 30)
	end

	if ove_0_21.draws.drawwmini:get() then
		minimap.draw_circle(player.pos, ove_0_36.range, 2, ove_0_21.draws.colorw:get(), 30)
	end

	ove_0_119()
	ove_0_120()
end

cb.add(cb.castspell, ove_0_44)
cb.add(cb.spell, ove_0_55)
ove_0_24.combat.register_f_pre_tick(ove_0_118)
cb.add(cb.draw, ove_0_121)
ove_0_20.Successfully_Loaded(true)

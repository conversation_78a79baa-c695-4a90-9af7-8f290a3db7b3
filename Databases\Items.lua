

return {
	MinionDematerializer = {
		type = "Consumable",
		price = 0,
		id = 2403,
		menu_name = "",
		slot_name = ""
	},
	BiscuitPotion = {
		type = "Consumable",
		price = 0,
		id = 2010,
		menu_name = "",
		slot_name = ""
	},
	HealthPotion = {
		type = "Consumable",
		price = 0,
		id = 2003,
		menu_name = "",
		slot_name = ""
	},
	RefillablePotion = {
		type = "Consumable",
		price = 0,
		id = 2031,
		menu_name = "",
		slot_name = ""
	},
	CorruptingPotion = {
		type = "Consumable",
		price = 0,
		id = 2033,
		menu_name = "",
		slot_name = ""
	},
	ElixirOfWrath = {
		type = "Consumable",
		price = 0,
		id = 2140,
		menu_name = "",
		slot_name = ""
	},
	ElixirOfSorcery = {
		type = "Consumable",
		price = 0,
		id = 2139,
		menu_name = "",
		slot_name = ""
	},
	ElixirOfIron = {
		type = "Consumable",
		price = 0,
		id = 2138,
		menu_name = "",
		slot_name = ""
	},
	ControlWard = {
		type = "Consumable",
		price = 0,
		id = 2055,
		menu_name = "",
		slot_name = ""
	},
	BlueTrinket = {
		type = "Trinket",
		price = 0,
		id = 3363,
		menu_name = "",
		slot_name = ""
	},
	OracleLens = {
		type = "Trinket",
		price = 0,
		id = 3364,
		menu_name = "",
		slot_name = ""
	},
	YellowTrinket = {
		type = "Trinket",
		price = 0,
		id = 3340,
		menu_name = "",
		slot_name = ""
	},
	Boots = {
		type = "Boots",
		price = 0,
		id = 1001,
		menu_name = "",
		slot_name = ""
	},
	BootsFromRunes = {
		type = "Boots",
		price = 0,
		id = 2422,
		menu_name = "",
		slot_name = ""
	},
	BootsOfSwiftness = {
		type = "Boots",
		price = 0,
		id = 3009,
		menu_name = "",
		slot_name = ""
	},
	SorcerersShoes = {
		type = "Boots",
		price = 0,
		id = 3020,
		menu_name = "",
		slot_name = ""
	},
	MobilityBoots = {
		type = "Boots",
		price = 0,
		id = 3117,
		menu_name = "",
		slot_name = ""
	},
	IonianBootsOfLucidity = {
		type = "Boots",
		price = 0,
		id = 3158,
		menu_name = "",
		slot_name = ""
	},
	BerserkersGreaves = {
		type = "Boots",
		price = 0,
		id = 3006,
		menu_name = "",
		slot_name = ""
	},
	MercurysTreads = {
		type = "Boots",
		price = 0,
		id = 3111,
		menu_name = "",
		slot_name = ""
	},
	PlatedSteelcaps = {
		type = "Boots",
		price = 0,
		id = 3047,
		menu_name = "",
		slot_name = ""
	},
	DarkSeal = {
		type = "Starter",
		price = 0,
		id = 1082,
		menu_name = "",
		slot_name = ""
	},
	DoransRing = {
		type = "Starter",
		price = 0,
		id = 1056,
		menu_name = "",
		slot_name = ""
	},
	TearOfTheGoddess = {
		type = "Starter",
		price = 0,
		id = 3070,
		menu_name = "",
		slot_name = ""
	},
	SpellthiefsEdge1 = {
		type = "Starter",
		price = 0,
		id = 3850,
		menu_name = "",
		slot_name = ""
	},
	SpellthiefsEdge2 = {
		type = "Starter",
		price = 0,
		id = 3851,
		menu_name = "",
		slot_name = ""
	},
	SpellthiefsEdge3 = {
		type = "Starter",
		price = 0,
		id = 3853,
		menu_name = "",
		slot_name = ""
	},
	SpectralSickle1 = {
		type = "Starter",
		price = 0,
		id = 3862,
		menu_name = "",
		slot_name = ""
	},
	SpectralSickle2 = {
		type = "Starter",
		price = 0,
		id = 3863,
		menu_name = "",
		slot_name = ""
	},
	SpectralSickle3 = {
		type = "Starter",
		price = 0,
		id = 3864,
		menu_name = "",
		slot_name = ""
	},
	RelicShield1 = {
		type = "Starter",
		price = 0,
		id = 3858,
		menu_name = "",
		slot_name = ""
	},
	RelicShield2 = {
		type = "Starter",
		price = 0,
		id = 3859,
		menu_name = "",
		slot_name = ""
	},
	RelicShield3 = {
		type = "Starter",
		price = 0,
		id = 3860,
		menu_name = "",
		slot_name = ""
	},
	SteelShoulderguards1 = {
		type = "Starter",
		price = 0,
		id = 3854,
		menu_name = "",
		slot_name = ""
	},
	SteelShoulderguards2 = {
		type = "Starter",
		price = 0,
		id = 3855,
		menu_name = "",
		slot_name = ""
	},
	SteelShoulderguards3 = {
		type = "Starter",
		price = 0,
		id = 3857,
		menu_name = "",
		slot_name = ""
	},
	DoransShield = {
		type = "Starter",
		price = 0,
		id = 1054,
		menu_name = "",
		slot_name = ""
	},
	DoransBlade = {
		type = "Starter",
		price = 0,
		id = 1055,
		menu_name = "",
		slot_name = ""
	},
	Cull = {
		type = "Starter",
		price = 0,
		id = 1083,
		menu_name = "",
		slot_name = ""
	},
	RejuvinationBeed = {
		type = "Basic",
		price = 0,
		id = 1006,
		menu_name = "",
		slot_name = ""
	},
	FaerieCharm = {
		type = "Basic",
		price = 0,
		id = 1004,
		menu_name = "",
		slot_name = ""
	},
	ClothArmor = {
		type = "Basic",
		price = 0,
		id = 1029,
		menu_name = "",
		slot_name = ""
	},
	Dagger = {
		type = "Basic",
		price = 0,
		id = 1042,
		menu_name = "",
		slot_name = ""
	},
	SapphireCrystal = {
		type = "Basic",
		price = 0,
		id = 1027,
		menu_name = "",
		slot_name = ""
	},
	LongSword = {
		type = "Basic",
		price = 0,
		id = 1036,
		menu_name = "",
		slot_name = ""
	},
	RubyCrystal = {
		type = "Basic",
		price = 0,
		id = 1028,
		menu_name = "",
		slot_name = ""
	},
	AmplifyingTome = {
		type = "Basic",
		price = 0,
		id = 1052,
		menu_name = "",
		slot_name = ""
	},
	NullMagicMantle = {
		type = "Basic",
		price = 0,
		id = 1033,
		menu_name = "",
		slot_name = ""
	},
	CloakOfAgility = {
		type = "Basic",
		price = 0,
		id = 1018,
		menu_name = "",
		slot_name = ""
	},
	Stopwatch = {
		type = "Basic",
		price = 0,
		id = 2420,
		menu_name = "",
		slot_name = ""
	},
	BrokenStopwatch = {
		type = "Basic",
		price = 0,
		id = 2421,
		menu_name = "",
		slot_name = ""
	},
	BrokenStopwatch2 = {
		type = "Basic",
		price = 0,
		id = 2424,
		menu_name = "",
		slot_name = ""
	},
	CommencingStopwatch = {
		type = "Basic",
		price = 0,
		id = 2419,
		menu_name = "",
		slot_name = ""
	},
	PerfectlyTimedStopwatch = {
		type = "Basic",
		price = 0,
		id = 2423,
		menu_name = "",
		slot_name = ""
	},
	Sheen = {
		type = "Basic",
		price = 0,
		id = 3057,
		menu_name = "",
		slot_name = ""
	},
	BlastingWand = {
		type = "Basic",
		price = 0,
		id = 1026,
		menu_name = "",
		slot_name = ""
	},
	Pickaxe = {
		type = "Basic",
		price = 0,
		id = 1037,
		menu_name = "",
		slot_name = ""
	},
	NeedlesslyLargeRod = {
		type = "Basic",
		price = 0,
		id = 1058,
		menu_name = "",
		slot_name = ""
	},
	BFSword = {
		type = "Basic",
		price = 0,
		id = 1038,
		menu_name = "",
		slot_name = ""
	},
	CrystallineBracer = {
		type = "Epic",
		price = 0,
		id = 3801,
		menu_name = "",
		slot_name = ""
	},
	KircheisShard = {
		type = "Epic",
		price = 0,
		id = 2015,
		menu_name = "",
		slot_name = ""
	},
	ExecutionersCalling = {
		type = "Epic",
		price = 0,
		id = 3123,
		menu_name = "",
		slot_name = ""
	},
	ChainVest = {
		type = "Epic",
		price = 0,
		id = 1031,
		menu_name = "",
		slot_name = ""
	},
	ForbiddenIdol = {
		type = "Epic",
		price = 0,
		id = 3114,
		menu_name = "",
		slot_name = ""
	},
	WingedMoonplate = {
		type = "Epic",
		price = 0,
		id = 3066,
		menu_name = "",
		slot_name = ""
	},
	Kindlegem = {
		type = "Epic",
		price = 0,
		id = 3067,
		menu_name = "",
		slot_name = ""
	},
	BrambleVest = {
		type = "Epic",
		price = 0,
		id = 3076,
		menu_name = "",
		slot_name = ""
	},
	Rageknife = {
		type = "Epic",
		price = 0,
		id = 6677,
		menu_name = "",
		slot_name = ""
	},
	OblivionOrb = {
		type = "Epic",
		price = 0,
		id = 3916,
		menu_name = "",
		slot_name = ""
	},
	AetherWisp = {
		type = "Epic",
		price = 0,
		id = 3113,
		menu_name = "",
		slot_name = ""
	},
	VampiricScepter = {
		type = "Epic",
		price = 0,
		id = 1053,
		menu_name = "",
		slot_name = ""
	},
	NegatronCloak = {
		type = "Epic",
		price = 0,
		id = 1057,
		menu_name = "",
		slot_name = ""
	},
	GlacialBuckler = {
		type = "Epic",
		price = 0,
		id = 3024,
		menu_name = "",
		slot_name = ""
	},
	FiendishCodex = {
		type = "Epic",
		price = 0,
		id = 3108,
		menu_name = "",
		slot_name = ""
	},
	GiantsBelt = {
		type = "Epic",
		price = 0,
		id = 1011,
		menu_name = "",
		slot_name = ""
	},
	BandleglassMirror = {
		type = "Epic",
		price = 0,
		id = 4642,
		menu_name = "",
		slot_name = ""
	},
	RecurveBow = {
		type = "Epic",
		price = 0,
		id = 1043,
		menu_name = "",
		slot_name = ""
	},
	WardensMail = {
		type = "Epic",
		price = 0,
		id = 3082,
		menu_name = "",
		slot_name = ""
	},
	SeekersArmguard = {
		type = "Epic",
		price = 0,
		id = 3191,
		menu_name = "",
		slot_name = ""
	},
	VerdantBarrier = {
		type = "Epic",
		price = 0,
		id = 4632,
		menu_name = "",
		slot_name = ""
	},
	Zeal = {
		type = "Epic",
		price = 0,
		id = 3086,
		menu_name = "",
		slot_name = ""
	},
	HextechAlternator = {
		type = "Epic",
		price = 0,
		id = 3145,
		menu_name = "",
		slot_name = ""
	},
	Phage = {
		type = "Epic",
		price = 0,
		id = 3044,
		menu_name = "",
		slot_name = ""
	},
	HearthboundAxe = {
		type = "Epic",
		price = 0,
		id = 3051,
		menu_name = "",
		slot_name = ""
	},
	SerratedDirk = {
		type = "Epic",
		price = 0,
		id = 3134,
		menu_name = "",
		slot_name = ""
	},
	CaulfieldsWarhammer = {
		type = "Epic",
		price = 0,
		id = 3133,
		menu_name = "",
		slot_name = ""
	},
	WatchfulWardstone = {
		type = "Epic",
		price = 0,
		id = 4638,
		menu_name = "",
		slot_name = ""
	},
	IronspikeWhip = {
		type = "Epic",
		price = 0,
		id = 6029,
		menu_name = "",
		slot_name = ""
	},
	Tiamat = {
		type = "Epic",
		price = 0,
		id = 3077,
		menu_name = "",
		slot_name = ""
	},
	BamisCinder = {
		type = "Epic",
		price = 0,
		id = 6660,
		menu_name = "",
		slot_name = ""
	},
	SpectresCowl = {
		type = "Epic",
		price = 0,
		id = 3211,
		menu_name = "",
		slot_name = ""
	},
	BlightingJewel = {
		type = "Epic",
		price = 0,
		id = 4630,
		menu_name = "",
		slot_name = ""
	},
	QuicksilverSash = {
		type = "Epic",
		price = 0,
		id = 3140,
		menu_name = "",
		slot_name = ""
	},
	Hexdrinker = {
		type = "Epic",
		price = 0,
		id = 3155,
		menu_name = "",
		slot_name = ""
	},
	LostChapter = {
		type = "Epic",
		price = 0,
		id = 3802,
		menu_name = "",
		slot_name = ""
	},
	LeechingLeer = {
		type = "Epic",
		price = 0,
		id = 4635,
		menu_name = "",
		slot_name = ""
	},
	LastWhisper = {
		type = "Epic",
		price = 0,
		id = 3035,
		menu_name = "",
		slot_name = ""
	},
	AegisOfTheLegion = {
		type = "Epic",
		price = 0,
		id = 3105,
		menu_name = "",
		slot_name = ""
	},
	Noonquiver = {
		type = "Epic",
		price = 0,
		id = 6670,
		menu_name = "",
		slot_name = ""
	},
	AxiomArc = {
		type = "Legendary",
		price = 0,
		id = 6696,
		menu_name = "",
		slot_name = ""
	},
	Shadowflame = {
		type = "Legendary",
		price = 0,
		id = 4645,
		menu_name = "",
		slot_name = ""
	},
	WintersApproach = {
		type = "Legendary",
		price = 0,
		id = 3119,
		menu_name = "",
		slot_name = ""
	},
	Fimbulwinter = {
		type = "Legendary",
		price = 0,
		id = 3121,
		menu_name = "",
		slot_name = ""
	},
	MejaiSoulstealer = {
		type = "Legendary",
		price = 0,
		id = 3041,
		menu_name = "",
		slot_name = ""
	},
	StaffOfFlowingWater = {
		type = "Legendary",
		price = 0,
		id = 6616,
		menu_name = "",
		slot_name = ""
	},
	ChemtechPutrifier = {
		type = "Legendary",
		price = 0,
		id = 3011,
		menu_name = "",
		slot_name = ""
	},
	ArdentCenser = {
		type = "Legendary",
		price = 0,
		id = 3504,
		menu_name = "",
		slot_name = ""
	},
	MikaelsBlessing = {
		type = "Legendary",
		price = 0,
		id = 3222,
		menu_name = "",
		slot_name = ""
	},
	Redemption = {
		type = "Legendary",
		price = 0,
		id = 3107,
		menu_name = "",
		slot_name = ""
	},
	KnightsVow = {
		type = "Legendary",
		price = 0,
		id = 3109,
		menu_name = "",
		slot_name = ""
	},
	ZekesConvergence = {
		type = "Legendary",
		price = 0,
		id = 3050,
		menu_name = "",
		slot_name = ""
	},
	MortalReminder = {
		type = "Legendary",
		price = 0,
		id = 3033,
		menu_name = "",
		slot_name = ""
	},
	Morellonomicon = {
		type = "Legendary",
		price = 0,
		id = 3165,
		menu_name = "",
		slot_name = ""
	},
	RapidFirecannon = {
		type = "Legendary",
		price = 0,
		id = 3094,
		menu_name = "",
		slot_name = ""
	},
	FrozenHeart = {
		type = "Legendary",
		price = 0,
		id = 3110,
		menu_name = "",
		slot_name = ""
	},
	ChempunkChainsword = {
		type = "Legendary",
		price = 0,
		id = 6609,
		menu_name = "",
		slot_name = ""
	},
	ZhonyasHourglass = {
		type = "Legendary",
		price = 0,
		id = 3157,
		menu_name = "",
		slot_name = ""
	},
	PhantomDancer = {
		type = "Legendary",
		price = 0,
		id = 3046,
		menu_name = "",
		slot_name = ""
	},
	SerpentsFang = {
		type = "Legendary",
		price = 0,
		id = 6695,
		menu_name = "",
		slot_name = ""
	},
	UmbralGlaive = {
		type = "Legendary",
		price = 0,
		id = 3179,
		menu_name = "",
		slot_name = ""
	},
	RunaansHurricane = {
		type = "Legendary",
		price = 0,
		id = 3085,
		menu_name = "",
		slot_name = ""
	},
	GuinsoosRageblade = {
		type = "Legendary",
		price = 0,
		id = 3124,
		menu_name = "",
		slot_name = ""
	},
	BansheesVeil = {
		type = "Legendary",
		price = 0,
		id = 3102,
		menu_name = "",
		slot_name = ""
	},
	AbyssalMask = {
		type = "Legendary",
		price = 0,
		id = 8020,
		menu_name = "",
		slot_name = ""
	},
	RanduinsOmen = {
		type = "Legendary",
		price = 0,
		id = 3143,
		menu_name = "",
		slot_name = ""
	},
	VoidStaff = {
		type = "Legendary",
		price = 0,
		id = 3135,
		menu_name = "",
		slot_name = ""
	},
	Thornmail = {
		type = "Legendary",
		price = 0,
		id = 3075,
		menu_name = "",
		slot_name = ""
	},
	Stormrazor = {
		type = "Legendary",
		price = 0,
		id = 3095,
		menu_name = "",
		slot_name = ""
	},
	GuardianAngel = {
		type = "Legendary",
		price = 0,
		id = 3026,
		menu_name = "",
		slot_name = ""
	},
	EssenceReaver = {
		type = "Legendary",
		price = 0,
		id = 3508,
		menu_name = "",
		slot_name = ""
	},
	MawOfMalmortius = {
		type = "Legendary",
		price = 0,
		id = 3156,
		menu_name = "",
		slot_name = ""
	},
	Manamune = {
		type = "Legendary",
		price = 0,
		id = 3004,
		menu_name = "",
		slot_name = ""
	},
	Muramana = {
		type = "Legendary",
		price = 0,
		id = 3042,
		menu_name = "",
		slot_name = ""
	},
	SeraphsEmbrace = {
		type = "Legendary",
		price = 0,
		id = 3040,
		menu_name = "",
		slot_name = ""
	},
	SpiritVisage = {
		type = "Legendary",
		price = 0,
		id = 3065,
		menu_name = "",
		slot_name = ""
	},
	DeadMansPlate = {
		type = "Legendary",
		price = 0,
		id = 3742,
		menu_name = "",
		slot_name = ""
	},
	EdgeOfNight = {
		type = "Legendary",
		price = 0,
		id = 3814,
		menu_name = "",
		slot_name = ""
	},
	ForceOfNature = {
		type = "Legendary",
		price = 0,
		id = 4401,
		menu_name = "",
		slot_name = ""
	},
	YoumuusGhostblade = {
		type = "Legendary",
		price = 0,
		id = 3142,
		menu_name = "",
		slot_name = ""
	},
	NashorsTooth = {
		type = "Legendary",
		price = 0,
		id = 3115,
		menu_name = "",
		slot_name = ""
	},
	LichBane = {
		type = "Legendary",
		price = 0,
		id = 3100,
		menu_name = "",
		slot_name = ""
	},
	WarmogsArmor = {
		type = "Legendary",
		price = 0,
		id = 3083,
		menu_name = "",
		slot_name = ""
	},
	Hullbreaker = {
		type = "Legendary",
		price = 0,
		id = 3181,
		menu_name = "",
		slot_name = ""
	},
	AnathemasChains = {
		type = "Legendary",
		price = 0,
		id = 8001,
		menu_name = "",
		slot_name = ""
	},
	RylaisCrystalScepter = {
		type = "Legendary",
		price = 0,
		id = 3116,
		menu_name = "",
		slot_name = ""
	},
	MercurialScimitar = {
		type = "Legendary",
		price = 0,
		id = 3139,
		menu_name = "",
		slot_name = ""
	},
	HorizonFocus = {
		type = "Legendary",
		price = 0,
		id = 4628,
		menu_name = "",
		slot_name = ""
	},
	CosmicDrive = {
		type = "Legendary",
		price = 0,
		id = 4629,
		menu_name = "",
		slot_name = ""
	},
	DemonicEmbrace = {
		type = "Legendary",
		price = 0,
		id = 4637,
		menu_name = "",
		slot_name = ""
	},
	LordDominiksRegards = {
		type = "Legendary",
		price = 0,
		id = 3036,
		menu_name = "",
		slot_name = ""
	},
	ArchangelsStaff = {
		type = "Legendary",
		price = 0,
		id = 3003,
		menu_name = "",
		slot_name = ""
	},
	TheCollector = {
		type = "Legendary",
		price = 0,
		id = 6676,
		menu_name = "",
		slot_name = ""
	},
	SilvermereDawn = {
		type = "Legendary",
		price = 0,
		id = 6035,
		menu_name = "",
		slot_name = ""
	},
	WitsEnd = {
		type = "Legendary",
		price = 0,
		id = 3091,
		menu_name = "",
		slot_name = ""
	},
	BlackCleaver = {
		type = "Legendary",
		price = 0,
		id = 3071,
		menu_name = "",
		slot_name = ""
	},
	SteraksGage = {
		type = "Legendary",
		price = 0,
		id = 3053,
		menu_name = "",
		slot_name = ""
	},
	DeathsDance = {
		type = "Legendary",
		price = 0,
		id = 6333,
		menu_name = "",
		slot_name = ""
	},
	BladeOfTheRuinedKing = {
		type = "Legendary",
		price = 0,
		id = 3153,
		menu_name = "",
		slot_name = ""
	},
	SeryldasGrudge = {
		type = "Legendary",
		price = 0,
		id = 6694,
		menu_name = "",
		slot_name = ""
	},
	GargoyleStoneplate = {
		type = "Legendary",
		price = 0,
		id = 3193,
		menu_name = "",
		slot_name = ""
	},
	RavenousHydra = {
		type = "Legendary",
		price = 0,
		id = 3074,
		menu_name = "",
		slot_name = ""
	},
	TitanicHydra = {
		type = "Legendary",
		price = 0,
		id = 3748,
		menu_name = "",
		slot_name = ""
	},
	Bloodthirster = {
		type = "Legendary",
		price = 0,
		id = 3072,
		menu_name = "",
		slot_name = ""
	},
	InfinityEdge = {
		type = "Legendary",
		price = 0,
		id = 3031,
		menu_name = "",
		slot_name = ""
	},
	NavoriQuickblades = {
		type = "Legendary",
		price = 0,
		id = 6675,
		menu_name = "",
		slot_name = ""
	},
	RabadonsDeathcap = {
		type = "Legendary",
		price = 0,
		id = 3089,
		menu_name = "",
		slot_name = ""
	},
	CrownOfTheShatteredQueen = {
		type = "Mythic",
		price = 0,
		id = 4644,
		menu_name = "",
		slot_name = ""
	},
	Evenshroud = {
		type = "Mythic",
		price = 0,
		id = 3001,
		menu_name = "",
		slot_name = ""
	},
	ShurelyasBattlesong = {
		type = "Mythic",
		price = 0,
		id = 2065,
		menu_name = "",
		slot_name = ""
	},
	LocketOfTheIronSolari = {
		type = "Mythic",
		price = 0,
		id = 3190,
		menu_name = "",
		slot_name = ""
	},
	ImperialMandate = {
		type = "Mythic",
		price = 0,
		id = 4005,
		menu_name = "",
		slot_name = ""
	},
	MoonstoneRenewer = {
		type = "Mythic",
		price = 0,
		id = 6617,
		menu_name = "",
		slot_name = ""
	},
	FrostfireGauntlet = {
		type = "Mythic",
		price = 0,
		id = 6662,
		menu_name = "",
		slot_name = ""
	},
	TurboChemtank = {
		type = "Mythic",
		price = 0,
		id = 6664,
		menu_name = "",
		slot_name = ""
	},
	SunfireAegis = {
		type = "Mythic",
		price = 0,
		id = 3068,
		menu_name = "",
		slot_name = ""
	},
	HextechRocketbelt = {
		type = "Mythic",
		price = 0,
		id = 3152,
		menu_name = "",
		slot_name = ""
	},
	Riftmaker = {
		type = "Mythic",
		price = 0,
		id = 4633,
		menu_name = "",
		slot_name = ""
	},
	NightHarvester = {
		type = "Mythic",
		price = 0,
		id = 4636,
		menu_name = "",
		slot_name = ""
	},
	DuskbladeOfDarktharr = {
		type = "Mythic",
		price = 0,
		id = 6691,
		menu_name = "",
		slot_name = ""
	},
	Eclipse = {
		type = "Mythic",
		price = 0,
		id = 6692,
		menu_name = "",
		slot_name = ""
	},
	ProwlersClaw = {
		type = "Mythic",
		price = 0,
		id = 6693,
		menu_name = "",
		slot_name = ""
	},
	Goredrinker = {
		type = "Mythic",
		price = 0,
		id = 6630,
		menu_name = "",
		slot_name = ""
	},
	Stridebreaker = {
		type = "Mythic",
		price = 0,
		id = 6631,
		menu_name = "",
		slot_name = ""
	},
	DivineSunderer = {
		type = "Mythic",
		price = 0,
		id = 6632,
		menu_name = "",
		slot_name = ""
	},
	TrinityForce = {
		type = "Mythic",
		price = 0,
		id = 3078,
		menu_name = "",
		slot_name = ""
	},
	ImmortalShieldbow = {
		type = "Mythic",
		price = 0,
		id = 6673,
		menu_name = "",
		slot_name = ""
	},
	KrakenSlayer = {
		type = "Mythic",
		price = 0,
		id = 6672,
		menu_name = "",
		slot_name = ""
	},
	Galeforce = {
		type = "Mythic",
		price = 0,
		id = 6671,
		menu_name = "",
		slot_name = ""
	},
	Everfrost = {
		type = "Mythic",
		price = 0,
		id = 6656,
		menu_name = "",
		slot_name = ""
	},
	LudensTempest = {
		type = "Mythic",
		price = 0,
		id = 6655,
		menu_name = "",
		slot_name = ""
	},
	LiandrysAnguish = {
		type = "Mythic",
		price = 0,
		id = 6653,
		menu_name = "",
		slot_name = ""
	},
	CrownOfTheShatteredQueen2 = {
		type = "MythicOrnn",
		price = 0,
		id = 7024,
		menu_name = "",
		slot_name = ""
	},
	Evenshroud2 = {
		type = "MythicOrnn",
		price = 0,
		id = 7023,
		menu_name = "",
		slot_name = ""
	},
	ShurelyasBattlesong2 = {
		type = "MythicOrnn",
		price = 0,
		id = 7020,
		menu_name = "",
		slot_name = ""
	},
	LocketOfTheIronSolari2 = {
		type = "MythicOrnn",
		price = 0,
		id = 7019,
		menu_name = "",
		slot_name = ""
	},
	ImperialMandate2 = {
		type = "MythicOrnn",
		price = 0,
		id = 7022,
		menu_name = "",
		slot_name = ""
	},
	MoonstoneRenewer2 = {
		type = "MythicOrnn",
		price = 0,
		id = 7021,
		menu_name = "",
		slot_name = ""
	},
	FrostfireGauntlet2 = {
		type = "MythicOrnn",
		price = 0,
		id = 7005,
		menu_name = "",
		slot_name = ""
	},
	TurboChemtank2 = {
		type = "MythicOrnn",
		price = 0,
		id = 7003,
		menu_name = "",
		slot_name = ""
	},
	SunfireAegis2 = {
		type = "MythicOrnn",
		price = 0,
		id = 7004,
		menu_name = "",
		slot_name = ""
	},
	HextechRocketbelt2 = {
		type = "MythicOrnn",
		price = 0,
		id = 7011,
		menu_name = "",
		slot_name = ""
	},
	Riftmaker2 = {
		type = "MythicOrnn",
		price = 0,
		id = 7009,
		menu_name = "",
		slot_name = ""
	},
	NightHarvester2 = {
		type = "MythicOrnn",
		price = 0,
		id = 7010,
		menu_name = "",
		slot_name = ""
	},
	DuskbladeOfDarktharr2 = {
		type = "MythicOrnn",
		price = 0,
		id = 7002,
		menu_name = "",
		slot_name = ""
	},
	Eclipse2 = {
		type = "MythicOrnn",
		price = 0,
		id = 7001,
		menu_name = "",
		slot_name = ""
	},
	ProwlersClaw2 = {
		type = "MythicOrnn",
		price = 0,
		id = 7000,
		menu_name = "",
		slot_name = ""
	},
	Goredrinker2 = {
		type = "MythicOrnn",
		price = 0,
		id = 7015,
		menu_name = "",
		slot_name = ""
	},
	Stridebreaker2 = {
		type = "MythicOrnn",
		price = 0,
		id = 7016,
		menu_name = "",
		slot_name = ""
	},
	DivineSunderer2 = {
		type = "MythicOrnn",
		price = 0,
		id = 7017,
		menu_name = "",
		slot_name = ""
	},
	TrinityForce2 = {
		type = "MythicOrnn",
		price = 0,
		id = 7016,
		menu_name = "",
		slot_name = ""
	},
	ImmortalShieldbow2 = {
		type = "MythicOrnn",
		price = 0,
		id = 7008,
		menu_name = "",
		slot_name = ""
	},
	KrakenSlayer2 = {
		type = "MythicOrnn",
		price = 0,
		id = 7007,
		menu_name = "",
		slot_name = ""
	},
	Galeforce2 = {
		type = "MythicOrnn",
		price = 0,
		id = 7006,
		menu_name = "",
		slot_name = ""
	},
	Everfrost2 = {
		type = "MythicOrnn",
		price = 0,
		id = 7014,
		menu_name = "",
		slot_name = ""
	},
	LudensTempest2 = {
		type = "MythicOrnn",
		price = 0,
		id = 7013,
		menu_name = "",
		slot_name = ""
	},
	LiandrysAnguish2 = {
		type = "MythicOrnn",
		price = 0,
		id = 7012,
		menu_name = "",
		slot_name = ""
	}
}


local ove_0_10 = module.internal("orb")
local ove_0_11 = module.internal("TS")
local ove_0_12 = module.internal("pred")
local ove_0_13 = module.seek("evade")
local ove_0_14 = module.load(header.id, "zac/menu")
local ove_0_15 = module.load(header.id, "common/common")
local ove_0_16 = module.load(header.id, "common/Computer/computer")
local ove_0_17 = module.load(header.id, "zac/misc/damage")
local ove_0_18 = {
	[BUFF_CHARM] = true,
	[BUFF_FEAR] = true,
	[BUFF_FLEE] = true,
	[BUFF_KNOCKUP] = true,
	[BUFF_SNARE] = true,
	[BUFF_STUN] = true,
	[BUFF_SUPPRESSION] = true,
	[BUFF_TAUNT] = true
}
local ove_0_19 = {
	range = 800,
	last = 0,
	slot = player:spellSlot(_Q),
	result = {},
	predinput = {
		speed = 2800,
		delay = 0.33,
		boundingRadiusMod = 0,
		width = 70,
		collision = {
			minion = true,
			hero = true,
			wall = true
		}
	}
}

function ove_0_19.is_ready()
	return ove_0_19.slot.state == 0
end

function ove_0_19.invoke_action()
	player:castSpell("pos", _Q, ove_0_19.result.pos:to3D())
end

function ove_0_19.get_action_state()
	if ove_0_19.is_ready() then
		return ove_0_19.get_prediction()
	end
end

function ove_0_19.get_stun_duration(arg_8_0)
	if not arg_8_0 then
		return
	end

	for iter_8_0, iter_8_1 in pairs(arg_8_0.buff) do
		if iter_8_1 and iter_8_1.valid and game.time < iter_8_1.endTime and ove_0_18[iter_8_1.type] then
			return math.max(0, iter_8_1.startTime, iter_8_1.endTime) - game.time
		end
	end

	return 0
end

function ove_0_19.invoke_auto_cced()
	local slot_9_0 = ove_0_11.get_result(function(arg_10_0, arg_10_1, arg_10_2)
		if arg_10_1.isDead or arg_10_2 > 800 then
			return
		end

		if ove_0_19.get_stun_duration(arg_10_1) ~= 0 and ove_0_19.get_stun_duration(arg_10_1) > ove_0_19.predinput.delay then
			arg_10_0.obj = arg_10_1
			arg_10_0.seg = arg_10_1.pos

			return true
		end
	end)

	if slot_9_0.obj and slot_9_0.seg and not ove_0_12.collision.get_prediction(ove_0_19.predinput, {
		startPos = player.path.serverPos2D,
		endPos = slot_9_0.obj.pos:to2D()
	}, slot_9_0.obj.pos:to2D()) then
		player:castSpell("pos", _Q, slot_9_0.seg)

		return true
	end
end

function ove_0_19.get_obj_movement_speed(arg_11_0)
	if arg_11_0 and arg_11_0.path.isActive and arg_11_0.path.isDashing then
		return arg_11_0.path.dashSpeed
	end

	return arg_11_0.moveSpeed
end

function ove_0_19.invoke_q_gapcloser()
	local slot_12_0 = ove_0_11.get_result(function(arg_13_0, arg_13_1, arg_13_2)
		if not ove_0_15.isValidTarget(arg_13_1) then
			return false
		end

		if arg_13_1.path.active and arg_13_1.path.count > 0 and arg_13_1.path.isDashing then
			local slot_13_0, slot_13_1, slot_13_2 = ove_0_12.core.lerp(arg_13_1.path, 0.33, ove_0_19.get_obj_movement_speed(arg_13_1))

			if slot_13_2 and slot_13_0 and slot_13_0:dist(player.path.serverPos:to2D()) < 800 and player.pos:dist(arg_13_1.path.endPos) < player.pos:dist(arg_13_1.path.serverPos) then
				arg_13_0.obj = arg_13_1
				arg_13_0.seg = slot_13_0

				return true
			end
		end
	end)

	if slot_12_0.obj and ove_0_15.IsValidTarget(slot_12_0.obj) and ove_0_15.IsEnemyMortal(slot_12_0.obj) and not ove_0_12.collision.get_prediction(ove_0_19.predinput, {
		startPos = player.path.serverPos2D,
		endPos = slot_12_0.seg
	}, slot_12_0.obj.pos:to2D()) then
		player:castSpell("pos", _Q, slot_12_0.seg:to3D())

		return true
	end
end

function ove_0_19.invoke_q_killsteal()
	local slot_14_0 = ove_0_11.get_result(function(arg_15_0, arg_15_1, arg_15_2)
		if arg_15_2 > 800 then
			return
		end

		if arg_15_2 < ove_0_15.GetAARange(arg_15_1) and ove_0_15.CalculateAADamage(arg_15_1) * 2 > ove_0_15.GetShieldedHealth("AD", arg_15_1) then
			return
		end

		if not ove_0_15.isValidTarget(arg_15_1) then
			return false
		end

		local slot_15_0 = ove_0_12.linear.get_prediction(ove_0_19.predinput, arg_15_1)

		if arg_15_2 > 800 + arg_15_1.boundingRadius then
			return false
		end

		if not slot_15_0 then
			return false
		end

		local slot_15_1 = ove_0_16.Compute(ove_0_19.predinput, slot_15_0, arg_15_1)

		if slot_15_1 < 0 then
			return false
		end

		if ove_0_17.get_q_damage(arg_15_1) > ove_0_15.GetShieldedHealth("AP", arg_15_1) then
			arg_15_0.seg = slot_15_0
			arg_15_0.obj = arg_15_1
			arg_15_0.pos = (ove_0_12.core.get_pos_after_time(arg_15_1, slot_15_1) + slot_15_0.endPos) / 2

			return true
		end
	end, ove_0_11.filter_set[8])

	if slot_14_0.obj and ove_0_15.IsValidTarget(slot_14_0.obj) and ove_0_15.IsEnemyMortal(slot_14_0.obj) and slot_14_0.seg and slot_14_0.seg.startPos:dist(slot_14_0.seg.endPos) < 800 and not ove_0_12.collision.get_prediction(ove_0_19.predinput, slot_14_0.seg, slot_14_0.obj) then
		player:castSpell("pos", _Q, slot_14_0.pos:to3D())

		return true
	end
end

function ove_0_19.invoke_jungle_clear()
	if player.buff.zace then
		return
	end

	local slot_16_0 = ove_0_10.farm.get_clear_target()
	local slot_16_1 = {
		mode = "jungleclear",
		health = 0
	}
	local slot_16_2 = player.attackRange + player.boundingRadius + 50

	if slot_16_0 and slot_16_0.team == TEAM_NEUTRAL and slot_16_2 >= player.pos:to2D():dist(slot_16_0.pos:to2D()) and slot_16_0.maxHealth > slot_16_1.health and not slot_16_0.isPlant and not slot_16_0.isWard then
		slot_16_1.obj = slot_16_0
		slot_16_1.health = slot_16_0.maxHealth
	end

	if slot_16_1.obj ~= nil and ove_0_15.IsValidTarget(slot_16_1.obj) and ove_0_15.isMinionValid(slot_16_1.obj, true) then
		if ove_0_15.IsInRange(ove_0_15.GetAARange(slot_16_1.obj, player), slot_16_1.obj, player) and ove_0_15.calculateFullAADamage(slot_16_1.obj, player) >= ove_0_15.GetShieldedHealth("AD", slot_16_1.obj) then
			return
		end

		if not ove_0_10.core.is_winding_up_attack() then
			player:castSpell("pos", _Q, slot_16_1.obj.pos)
			ove_0_10.core.set_server_pause()
		end
	end
end

function ove_0_19.Floor(arg_17_0)
	return math.floor(arg_17_0 * 100) * 0.01
end

function ove_0_19.trace_filter()
	local slot_18_0 = ove_0_19.predinput.delay + network.latency

	if player.pos:to2D():dist(ove_0_19.result.pos) + slot_18_0 * (ove_0_19.result.obj.moveSpeed * 0.333) + ove_0_19.result.obj.boundingRadius > 950 then
		return false
	end

	local slot_18_1 = (ove_0_19.result.pos - player.pos:to2D()):len() / ove_0_19.predinput.speed
	local slot_18_2 = ove_0_19.Floor(slot_18_1) - ove_0_19.Floor(ove_0_19.result.time)

	if not ove_0_19.result.obj.path.isActive and ove_0_19.range < ove_0_19.result.seg.startPos:dist(ove_0_19.result.obj.pos2D) + ove_0_19.result.obj.moveSpeed * 0.333 then
		return false
	end

	if player.pos:to2D():distSqr(ove_0_19.result.obj.path.serverPos:to2D()) > 810000 then
		return false
	end

	if player.pos:to2D():distSqr(ove_0_19.result.pos) > math.pow(ove_0_19.range, 2) then
		return false
	end

	if player.pos:to2D():distSqr(ove_0_19.result.obj.path.serverPos2D) > math.pow(ove_0_19.range, 2) then
		return false
	end

	if ove_0_12.collision.get_prediction(ove_0_19.predinput, ove_0_19.result.seg, ove_0_19.result.obj) then
		return false
	end

	if ove_0_12.trace.newpath(ove_0_19.result.obj, 0.033, 0.378 + network.latency) and slot_18_2 <= slot_18_0 then
		return true
	end

	if ove_0_12.trace.linear.hardlock(ove_0_19.predinput, ove_0_19.result.seg, ove_0_19.result.obj) then
		return true
	end

	if ove_0_12.trace.linear.hardlockmove(ove_0_19.predinput, ove_0_19.result.seg, ove_0_19.result.obj) then
		return true
	end
end

function ove_0_19.get_prediction()
	if ove_0_19.last == game.time then
		return ove_0_19.result.pos, ove_0_19.result.seg
	end

	ove_0_19.last = game.time
	ove_0_19.result.obj = nil
	ove_0_19.result.pos = nil
	ove_0_19.result.seg = nil
	ove_0_19.result = ove_0_11.get_result(function(arg_20_0, arg_20_1, arg_20_2)
		if arg_20_2 > ove_0_19.range then
			return
		end

		if arg_20_1.buff.zacqmissile then
			return
		end

		if arg_20_2 <= ove_0_15.GetAARange(arg_20_1) and ove_0_15.CalculateAADamage(arg_20_1) * 2 > ove_0_15.GetShieldedHealth("AD", arg_20_1) then
			return
		end

		if not ove_0_15.isValidTarget(arg_20_1) then
			return false
		end

		local slot_20_0 = ove_0_12.linear.get_prediction(ove_0_19.predinput, arg_20_1, player.path.serverPos2D)

		if not slot_20_0 then
			return
		end

		local slot_20_1 = ove_0_16.Compute(ove_0_19.predinput, slot_20_0, arg_20_1)

		if slot_20_1 < 0 then
			return false
		end

		if slot_20_0 and slot_20_0.startPos:distSqr(slot_20_0.endPos) < ove_0_19.range * ove_0_19.range then
			arg_20_0.seg = slot_20_0
			arg_20_0.obj = arg_20_1
			arg_20_0.pos = (ove_0_12.core.get_pos_after_time(arg_20_1, slot_20_1) + slot_20_0.endPos) / 2
			arg_20_0.time = slot_20_1

			return true
		end
	end)

	if ove_0_19.result.pos and ove_0_19.result.seg and ove_0_19.trace_filter() then
		return ove_0_19.result
	end
end

function ove_0_19.on_draw()
	if ove_0_19.slot.level > 0 then
		graphics.draw_circle(player.pos, ove_0_19.range, 1, ove_0_14.draws.q:get(), 100)
	end
end

return ove_0_19

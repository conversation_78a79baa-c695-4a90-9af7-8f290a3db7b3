local ove_0_10 = module.load("<PERSON>", "Champions/Zed/Util/Detection")
local ove_0_11 = player
local ove_0_12 = {
	range = 290,
	Slot = player:spellSlot(2),
	LastCastTime = 0,
	CastDelay = 0.1,
	-- 最新版本E技能数据
	baseDamage = {70, 95, 120, 145, 170},
	adRatio = 0.65,
	energyCost = 50,
	slowDuration = 1.5,
	slowAmount = 0.25
}

function ove_0_12.Cast(arg_5_0)
	if not ove_0_12.Ready() or not arg_5_0 or arg_5_0.isDead then
		return false
	end

	-- 检查施法延迟
	if game.time - ove_0_12.LastCastTime < ove_0_12.CastDelay then
		return false
	end

	local shouldCast = false
	local shadows = ove_0_10.GetShadowInfo()
	local hitCount = 0

	-- 检查是否有目标在R影子范围内（优先级最高）
	if shadows.R and shadows.R:dist(arg_5_0.pos) <= ove_0_12.range then
		shouldCast = true
		hitCount = hitCount + 1
	end

	-- 检查是否有目标在W影子范围内
	if shadows.W and shadows.W:dist(arg_5_0.pos) <= ove_0_12.range then
		shouldCast = true
		hitCount = hitCount + 1
	end

	-- 检查是否有目标在玩家范围内
	if arg_5_0.pos:dist(ove_0_11.pos) <= ove_0_12.range then
		shouldCast = true
		hitCount = hitCount + 1
	end

	-- 只有在至少能命中一个位置时才释放E
	if shouldCast and hitCount > 0 then
		player:castSpell("self", 2)
		ove_0_12.LastCastTime = game.time
		return true
	end

	return false
end

function ove_0_12.GetDamage(target)
	if not target then return 0 end

	local level = ove_0_12.Level()
	if level == 0 then return 0 end

	-- 使用最新的E技能伤害数据
	local baseDamage = ove_0_12.baseDamage[level]
	local bonusAD = player.totalAttackDamage * ove_0_12.adRatio

	-- 计算护甲减免
	local armorPen = player.armorPenFlat or 0
	local armorPenPercent = player.armorPenPercent or 1
	local targetArmor = math.max(0, target.armor * armorPenPercent - armorPen)
	local damageMultiplier = 100 / (100 + targetArmor)

	return (baseDamage + bonusAD) * damageMultiplier
end

-- 智能E技能释放，考虑多个目标
function ove_0_12.CastSmart()
	if not ove_0_12.Ready() then return false end

	local shadows = ove_0_10.GetShadowInfo()
	local enemiesHit = 0
	local priorityTarget = nil
	local maxPriority = 0

	-- 检查所有敌人
	for i = 0, objManager.enemies_n - 1 do
		local enemy = objManager.enemies[i]
		if enemy and enemy.isValid and not enemy.isDead and enemy.isTargetable then
			local canHit = false
			local hitPositions = 0

			-- 检查玩家位置
			if enemy.pos:dist(player.pos) <= ove_0_12.range then
				canHit = true
				hitPositions = hitPositions + 1
			end

			-- 检查W影子位置
			if shadows.W and enemy.pos:dist(shadows.W) <= ove_0_12.range then
				canHit = true
				hitPositions = hitPositions + 1
			end

			-- 检查R影子位置
			if shadows.R and enemy.pos:dist(shadows.R) <= ove_0_12.range then
				canHit = true
				hitPositions = hitPositions + 1
			end

			if canHit then
				enemiesHit = enemiesHit + 1

				-- 计算目标优先级
				local priority = hitPositions
				if ove_0_10.IsTargetMarked(enemy) then priority = priority + 3 end
				if enemy.health / enemy.maxHealth <= 0.3 then priority = priority + 2 end
				if enemy.type == TYPE_HERO then priority = priority + 1 end

				if priority > maxPriority then
					maxPriority = priority
					priorityTarget = enemy
				end
			end
		end
	end

	-- 如果有高优先级目标或能命中多个敌人，释放E
	if priorityTarget and (maxPriority >= 3 or enemiesHit >= 2) then
		return ove_0_12.Cast(priorityTarget)
	end

	return false
end

function ove_0_12.GetEnemiesInRange()
	local count = 0
	local enemies = {}
	
	-- 检查玩家范围内的敌人
	for i = 0, objManager.enemies_n - 1 do
		local enemy = objManager.enemies[i]
		if enemy and enemy.isValid and not enemy.isDead and enemy.isTargetable then
			local inRange = false
			
			-- 检查玩家范围
			if enemy.pos:dist(player.pos) <= ove_0_12.range then
				inRange = true
			end
			
			-- 检查影子范围
			local shadows = ove_0_10.GetShadowInfo()
			if shadows.W and enemy.pos:dist(shadows.W) <= ove_0_12.range then
				inRange = true
			end
			if shadows.R and enemy.pos:dist(shadows.R) <= ove_0_12.range then
				inRange = true
			end
			
			if inRange then
				count = count + 1
				table.insert(enemies, enemy)
			end
		end
	end
	
	return count, enemies
end

function ove_0_12.Ready()
	return ove_0_12.Slot.state == 0
end

function ove_0_12.Cost()
	return ove_0_12.energyCost
end

function ove_0_12.Level()
	return player:spellSlot(2).level
end

return ove_0_12

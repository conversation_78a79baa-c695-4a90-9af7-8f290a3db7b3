math.randomseed(0.571123)

local ove_0_0

ove_0_0 = {
	function(...)
		-- function 1
		local slot_1_0 = {
			...
		}

		return ove_0_0[8](slot_1_0)
	end,
	cb,
	game,
	math.frexp,
	math.random(1, 1000),
	string.dump,
	string.sub,
	table.concat,
	cb,
	"bot",
	loadstring,
	"b+",
	function(arg_2_0)
		-- function 2
		local slot_2_0 = loadstring(arg_2_0)

		if slot_2_0 then
			return ove_0_0[tonumber("20")](function()
				-- function 3
				slot_2_0()
			end)
		else
			return nil
		end
	end,
	"load",
	1234567890,
	getfenv,
	"",
	"jit",
	0.1,
	pcall,
	math.pi,
	""
}

local ove_0_1 = ove_0_0[5]

local function ove_0_2(arg_4_0)
	-- function 4
	return tostring(arg_4_0 / ove_0_1)
end

local ove_0_3 = {
	ove_0_2(9201),
	ove_0_2(9707),
	ove_0_2(16514),
	ove_0_2(6681),
	ove_0_2(12395),
	ove_0_2(26276),
	ove_0_2(28957),
	ove_0_2(16435),
	ove_0_2(26012),
	ove_0_2(25299),
	ove_0_2(29325),
	ove_0_2(32303),
	ove_0_2(30725),
	ove_0_2(3266),
	ove_0_2(32386),
	ove_0_2(13647),
	ove_0_2(22768),
	ove_0_2(4451),
	ove_0_2(19380),
	ove_0_2(18613),
	ove_0_2(2308),
	ove_0_2(12436),
	ove_0_2(5405),
	ove_0_2(22474),
	ove_0_2(30999),
	ove_0_2(27297),
	ove_0_2(8549),
	ove_0_2(30689),
	ove_0_2(27971),
	ove_0_2(9994),
	ove_0_2(24222),
	ove_0_2(12568),
	ove_0_2(5692),
	ove_0_2(14471),
	ove_0_2(26161),
	ove_0_2(17023),
	ove_0_2(21728),
	ove_0_2(25162),
	ove_0_2(32024),
	ove_0_2(29053),
	ove_0_2(23568),
	ove_0_2(17028),
	ove_0_2(25678),
	ove_0_2(26735),
	ove_0_2(9407),
	ove_0_2(17962),
	ove_0_2(3460),
	ove_0_2(26769),
	ove_0_2(26696),
	ove_0_2(2553),
	ove_0_2(2070),
	ove_0_2(21282),
	ove_0_2(5381),
	ove_0_2(19640),
	ove_0_2(14909),
	ove_0_2(19907)
}
local ove_0_4, ove_0_5 = ove_0_0[9].get(2)

if not ove_0_5 then
	return
end

local ove_0_6 = 0
local ove_0_7 = {
	SmolderQ = function(arg_5_0)
		-- function 5
		return game.time + arg_5_0.windUpTime - network.latency + 0.033
	end,
	VolibearR = function(arg_6_0)
		-- function 6
		return game.time + arg_6_0.windUpTime - network.latency + 0.033
	end,
	EzrealQ = function(arg_7_0)
		-- function 7
		return game.time + arg_7_0.windUpTime - network.latency + 0.033
	end,
	EzrealW = function(arg_8_0)
		-- function 8
		return game.time + arg_8_0.windUpTime - network.latency + 0.033
	end,
	EzrealE = function(arg_9_0)
		-- function 9
		return game.time + arg_9_0.windUpTime - network.latency + 0.033
	end,
	EzrealR = function(arg_10_0)
		-- function 10
		return game.time + arg_10_0.windUpTime - network.latency + 0.033
	end,
	XerathArcanopulse2 = function(arg_11_0)
		-- function 11
		return game.time + 0.6 - network.latency + 0.033
	end,
	XerathArcaneBarrage2 = function(arg_12_0)
		-- function 12
		return game.time + 0.25 - network.latency + 0.033
	end,
	XerathMageSpear = function(arg_13_0)
		-- function 13
		return game.time + 0.25 - network.latency + 0.033
	end,
	NautilusAnchorDrag = function(arg_14_0)
		-- function 14
		return game.time + arg_14_0.windUpTime - network.latency + 0.033
	end,
	NautilusSplashZone = function(arg_15_0)
		-- function 15
		return game.time + arg_15_0.windUpTime - network.latency + 0.033
	end,
	NautilusGrandLine = function(arg_16_0)
		-- function 16
		return game.time + arg_16_0.windUpTime - network.latency + 0.033
	end,
	ApheliosSeverumQ = function(arg_17_0)
		-- function 17
		if orb then
			orb.core.set_pause_attack(1.75 - network.latency)
		end

		return 0
	end,
	ApheliosInfernumQ = function(arg_18_0)
		-- function 18
		return game.time + arg_18_0.windUpTime - network.latency + 0.033
	end,
	ApheliosGravitumQ = function(arg_19_0)
		-- function 19
		return game.time + arg_19_0.windUpTime - network.latency + 0.033
	end,
	ApheliosCalibrumQ = function(arg_20_0)
		-- function 20
		return game.time + arg_20_0.windUpTime - network.latency + 0.033
	end,
	BelvethW = function(arg_21_0)
		-- function 21
		return game.time + arg_21_0.windUpTime - network.latency + 0.033
	end,
	VarusE = function(arg_22_0)
		-- function 22
		return game.time + arg_22_0.windUpTime - network.latency + 0.033
	end,
	VarusR = function(arg_23_0)
		-- function 23
		return game.time + arg_23_0.windUpTime - network.latency + 0.033
	end,
	YasuoQ1 = function(arg_24_0)
		-- function 24
		return game.time + arg_24_0.windUpTime - network.latency + 0.033
	end,
	YasuoQ2 = function(arg_25_0)
		-- function 25
		return game.time + arg_25_0.windUpTime - network.latency + 0.033
	end,
	YasuoQ3 = function(arg_26_0)
		-- function 26
		return game.time + arg_26_0.windUpTime - network.latency + 0.033
	end,
	["YasuoE--"] = function(arg_27_0)
		-- function 27
		return game.time + arg_27_0.windUpTime - network.latency + 0.033
	end,
	YasuoQE1 = function(arg_28_0)
		-- function 28
		return game.time + arg_28_0.windUpTime - network.latency + 0.033
	end,
	YasuoQE2 = function(arg_29_0)
		-- function 29
		return game.time + arg_29_0.windUpTime - network.latency + 0.033
	end,
	YasuoQE3 = function(arg_30_0)
		-- function 30
		return game.time + arg_30_0.windUpTime - network.latency + 0.033
	end,
	YasuoR = function(arg_31_0)
		-- function 31
		return game.time + 1 - network.latency + 0.033
	end,
	JinxWMissile = function(arg_32_0)
		-- function 32
		return game.time + arg_32_0.windUpTime - network.latency + 0.033
	end,
	JinxR = function(arg_33_0)
		-- function 33
		return game.time + arg_33_0.windUpTime - network.latency + 0.033
	end,
	JhinQ = function(arg_34_0)
		-- function 34
		return game.time + arg_34_0.windUpTime - network.latency + 0.033
	end,
	JhinW = function(arg_35_0)
		-- function 35
		return game.time + arg_35_0.windUpTime - network.latency + 0.033
	end,
	JhinE = function(arg_36_0)
		-- function 36
		return game.time + arg_36_0.windUpTime - network.latency + 0.033
	end,
	JhinR = function(arg_37_0)
		-- function 37
		return game.time + arg_37_0.windUpTime - network.latency + 0.033
	end,
	KogMawQ = function(arg_38_0)
		-- function 38
		return game.time + arg_38_0.windUpTime - network.latency + 0.033
	end,
	KogMawVoidOoze = function(arg_39_0)
		-- function 39
		return game.time + arg_39_0.windUpTime - network.latency + 0.033
	end,
	KogMawLivingArtillery = function(arg_40_0)
		-- function 40
		return game.time + arg_40_0.windUpTime - network.latency + 0.033
	end,
	AlphaStrike = function(arg_41_0)
		-- function 41
		return game.time + 0.6 - network.latency + 0.033
	end,
	ZeriQ = function(arg_42_0)
		-- function 42
		return game.time + arg_42_0.windUpTime - network.latency + 0.033
	end,
	ZeriW = function(arg_43_0)
		-- function 43
		return game.time + arg_43_0.windUpTime - network.latency + 0.033
	end,
	ZeriE = function(arg_44_0)
		-- function 44
		return game.time + arg_44_0.windUpTime - network.latency + 0.033
	end,
	ZeriR = function(arg_45_0)
		-- function 45
		if orb then
			orb.core.set_pause_attack(arg_45_0.windUpTime - network.latency)
		end

		return 0
	end,
	KaisaW = function(arg_46_0)
		-- function 46
		return game.time + arg_46_0.windUpTime - network.latency + 0.033
	end,
	TristanaW = function(arg_47_0)
		-- function 47
		return game.time + arg_47_0.windUpTime - network.latency + 0.033
	end,
	TristanaE = function(arg_48_0)
		-- function 48
		return game.time + arg_48_0.windUpTime - network.latency + 0.033
	end,
	TristanaR = function(arg_49_0)
		-- function 49
		return game.time + arg_49_0.windUpTime - network.latency + 0.033
	end,
	Volley = function(arg_50_0)
		-- function 50
		return game.time + arg_50_0.windUpTime - network.latency + 0.033
	end,
	EnchantedCrystalArrow = function(arg_51_0)
		-- function 51
		return game.time + arg_51_0.windUpTime - network.latency + 0.033
	end,
	XerathArcanopulse2 = function(arg_52_0)
		-- function 52
		return game.time + arg_52_0.windUpTime - network.latency + 0.033
	end,
	XerathArcaneBarrage2 = function(arg_53_0)
		-- function 53
		return game.time + arg_53_0.windUpTime - network.latency + 0.033
	end,
	XerathMageSpear = function(arg_54_0)
		-- function 54
		return game.time + arg_54_0.windUpTime - network.latency + 0.033
	end,
	XerathLocusOfPower2 = function(arg_55_0)
		-- function 55
		return game.time + 1 - network.latency + 0.033
	end,
	NaafiriQ = function(arg_56_0)
		-- function 56
		return game.time + arg_56_0.windUpTime - network.latency + 0.033
	end,
	NaafiriW = function(arg_57_0)
		-- function 57
		return game.time + 1.5 - network.latency + 0.033
	end,
	NaafiriE = function(arg_58_0)
		-- function 58
		return game.time + arg_58_0.windUpTime - network.latency + 0.033
	end,
	NaafiriR = function(arg_59_0)
		-- function 59
		if orb then
			orb.core.set_pause_attack(arg_59_0.windUpTime - network.latency)
		end

		return 0
	end,
	XayahQ = function(arg_60_0)
		-- function 60
		if orb then
			orb.core.set_pause_attack(arg_60_0.windUpTime)
		end

		return 0
	end,
	XayahR = function(arg_61_0)
		-- function 61
		if orb then
			orb.core.set_pause_attack(1.5 - network.latency)
		end

		return 0
	end,
	GravesQLineSpell = function(arg_62_0)
		-- function 62
		return game.time + arg_62_0.windUpTime - network.latency + 0.033
	end,
	GravesSmokeGrenade = function(arg_63_0)
		-- function 63
		return game.time + arg_63_0.windUpTime - network.latency + 0.033
	end,
	GravesMove = function(arg_64_0)
		-- function 64
		return game.time + arg_64_0.windUpTime - network.latency + 0.033
	end,
	GravesChargeShot = function(arg_65_0)
		-- function 65
		return game.time + arg_65_0.windUpTime - network.latency + 0.033
	end,
	TwitchVenomCask = function(arg_66_0)
		-- function 66
		if orb then
			orb.core.set_pause_attack(arg_66_0.windUpTime - network.latency)
		end

		return 0
	end,
	TwitchExpunge = function(arg_67_0)
		-- function 67
		if orb then
			orb.core.set_pause_attack(arg_67_0.windUpTime - network.latency)
		end

		return 0
	end,
	CaitlynQ = function(arg_68_0)
		-- function 68
		return game.time + arg_68_0.windUpTime - network.latency + 0.033
	end,
	CaitlynW = function(arg_69_0)
		-- function 69
		return game.time + arg_69_0.windUpTime - network.latency + 0.033
	end,
	CaitlynE = function(arg_70_0)
		-- function 70
		return game.time + arg_70_0.windUpTime - network.latency + 0.033
	end,
	CaitlynR = function(arg_71_0)
		-- function 71
		return game.time + 1 - network.latency + 0.033
	end,
	ThreshQ = function(arg_72_0)
		-- function 72
		return game.time + arg_72_0.windUpTime - network.latency + 0.033
	end,
	RenataR = function(arg_73_0)
		-- function 73
		return game.time + arg_73_0.windUpTime - network.latency + 0.033
	end,
	SyndraR = function(arg_74_0)
		-- function 74
		return game.time + arg_74_0.windUpTime - network.latency + 0.033
	end,
	YoneQ3 = function(arg_75_0)
		-- function 75
		return game.time + arg_75_0.windUpTime - network.latency + 0.033
	end,
	YoneR = function(arg_76_0)
		-- function 76
		return game.time + arg_76_0.windUpTime - network.latency + 0.033
	end,
	MissFortuneRicochetShot = function(arg_77_0)
		-- function 77
		return game.time + arg_77_0.windUpTime - network.latency + 0.033
	end,
	AatroxQWrapperCast = function(arg_78_0)
		-- function 78
		return game.time + arg_78_0.windUpTime - network.latency + 0.033
	end,
	WildCards = function(arg_79_0)
		-- function 79
		return game.time + arg_79_0.windUpTime - network.latency + 0.033
	end,
	HweiQQ = function(arg_80_0)
		-- function 80
		return game.time + arg_80_0.windUpTime - network.latency + 0.033
	end,
	HweiQW = function(arg_81_0)
		-- function 81
		return game.time + arg_81_0.windUpTime - network.latency + 0.033
	end,
	HweiQE = function(arg_82_0)
		-- function 82
		return game.time + arg_82_0.windUpTime - network.latency + 0.033
	end,
	HweiWQ = function(arg_83_0)
		-- function 83
		return game.time + arg_83_0.windUpTime - network.latency + 0.033
	end,
	HweiWW = function(arg_84_0)
		-- function 84
		return game.time + arg_84_0.windUpTime - network.latency + 0.033
	end,
	HweiWE = function(arg_85_0)
		-- function 85
		return game.time + arg_85_0.windUpTime - network.latency + 0.033
	end,
	HweiEQ = function(arg_86_0)
		-- function 86
		return game.time + arg_86_0.windUpTime - network.latency + 0.033
	end,
	HweiEW = function(arg_87_0)
		-- function 87
		return game.time + arg_87_0.windUpTime - network.latency + 0.033
	end,
	HweiEE = function(arg_88_0)
		-- function 88
		return game.time + arg_88_0.windUpTime - network.latency + 0.033
	end,
	SmolderQ = function(arg_89_0)
		-- function 89
		if orb then
			orb.core.set_pause_attack(0.33299999999999996)
		end

		return game.time + arg_89_0.windUpTime - network.latency + 0.033
	end,
	SmolderW = function(arg_90_0)
		-- function 90
		if orb then
			orb.core.set_pause_attack(0.683)
		end

		return game.time + arg_90_0.windUpTime - network.latency + 0.033
	end,
	SmolderR = function(arg_91_0)
		-- function 91
		if orb then
			orb.core.set_pause_attack(0.783)
		end

		return game.time + arg_91_0.windUpTime - network.latency + 0.033
	end,
	NilahR = function(arg_92_0)
		-- function 92
		if orb then
			orb.core.set_pause_attack(1 - network.latency)
		end

		return 0
	end,
	OrianaDetonateCommand = function(arg_93_0)
		-- function 93
		if orb then
			orb.core.set_pause_attack(arg_93_0.windUpTime)
			orb.core.set_pause_move(arg_93_0.windUpTime)
		end

		return 0
	end
}

local function ove_0_8()
	-- function 94
	return game.time < ove_0_6
end

local function ove_0_9()
	-- function 95
	ove_0_6 = game.time + network.latency + 0.132
end

local function ove_0_10(arg_96_0, arg_96_1)
	-- function 96
	local slot_96_0 = arg_96_0

	if not slot_96_0 or slot_96_0.ptr ~= player.ptr then
		return
	end

	local slot_96_1 = arg_96_1.name

	if ove_0_8() then
		ove_0_6 = 0
	end
end

local function ove_0_11(arg_97_0, arg_97_1)
	-- function 97
	local slot_97_0 = arg_97_0

	if not slot_97_0 or slot_97_0.ptr ~= player.ptr then
		return
	end

	local slot_97_1 = arg_97_1.name

	if ove_0_7[slot_97_1] then
		ove_0_6 = ove_0_7[slot_97_1](arg_97_1)
	end
end

return {
	is_locked = ove_0_8,
	set_lock_await_response = ove_0_9,
	set_server_pause = ove_0_9,
	process_spell = ove_0_11,
	execute_spell = ove_0_10
}

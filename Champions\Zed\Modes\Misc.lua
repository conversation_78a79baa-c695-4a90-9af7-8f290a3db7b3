

local ove_0_10 = player
local ove_0_11 = module.load("<PERSON>", "<PERSON><PERSON>/MyC<PERSON><PERSON>")
local ove_0_12 = module.load("<PERSON>", "Champions/Zed/Spells/W")
local ove_0_13 = module.load("<PERSON>", "Champions/Zed/Spells/R")
local ove_0_14 = module.load("<PERSON>", "Champions/Zed/Spells/E")
local ove_0_15 = module.load("<PERSON>", "Champions/Zed/Util/menu")
local ove_0_16 = module.load("<PERSON>", "Champions/Zed/Util/Detection")
local ove_0_17 = module.load("<PERSON>", "Lib/DelayAction")
local ove_0_18 = os.clock()
local ove_0_19 = {}

function ove_0_19.blacklistcheck(arg_5_0)
	-- print 5
	local slot_5_0 = ove_0_19.blacklist(arg_5_0)

	if slot_5_0 == 1 and ove_0_15.combo.blacklist.a1:get() or slot_5_0 == 2 and ove_0_15.combo.blacklist.a2:get() or slot_5_0 == 3 and ove_0_15.combo.blacklist.a3:get() or slot_5_0 == 4 and ove_0_15.combo.blacklist.a4:get() or slot_5_0 == 5 and ove_0_15.combo.blacklist.a5:get() then
		return true
	else
		return false
	end
end

function ove_0_19.blacklist(arg_6_0)
	-- print 6
	for iter_6_0 = 1, #ove_0_11.blacklist do
		if arg_6_0.charName == ove_0_11.blacklist[iter_6_0] then
			return iter_6_0
		end
	end
end

function ove_0_19.AutoReturn(arg_7_0)
	-- print 7
	if not arg_7_0 then
		return
	end

	if ove_0_15.combo.turnback.turnbacklogic:get() == 1 then
		if ove_0_11.Health() <= 25 then
			ove_0_19.Return()
		elseif ove_0_11.HeroCount(700) >= 2 and ove_0_11.Health() <= 45 then
			ove_0_19.Return()
		elseif ove_0_11.HeroCount(700) >= 4 and ove_0_11.Health() <= 65 then
			ove_0_19.Return()
		end
	elseif ove_0_15.combo.turnback.turnbacklogic:get() == 2 then
		if ove_0_13.IsR2() and ove_0_19.IsTargetDie(arg_7_0) then
			ove_0_19.Return()
		end
	elseif ove_0_15.combo.turnback.turnbacklogic:get() == 3 then
		-- block empty
	end

	ove_0_19.AutoReturnHp(arg_7_0)
end

function ove_0_19.Return()
	-- print 8
	if ove_0_12.IsW2() then
		ove_0_12.Cast2()
	elseif ove_0_13.IsR2() then
		ove_0_13.CastR2()
	end
end

function ove_0_19.AutoReturnHp(arg_9_0)
	-- print 9
	if not arg_9_0 then
		return
	end

	if ove_0_15.combo.turnback.swap:get() and ove_0_11.Health() <= ove_0_15.combo.turnback.swaphp:get() and ove_0_11.Dist(arg_9_0) <= 600 then
		ove_0_12.Cast2()
	end
end

function ove_0_19.IsTargetDie(arg_10_0)
	-- print 10
	return arg_10_0.IsDead
end

function ove_0_19.AntiAfk()
	-- print 11
	if os.clock() < ove_0_18 or not ove_0_15.misc.antiafk.use:get() then
		return
	end

	ove_0_18 = os.clock() + math.random(60, 120)

	player:move(ove_0_10.pos)
end

function ove_0_19.Autolevel()
	-- print 12
	return
end

function ove_0_19.SkinChanger()
	-- print 13
	return
end

function ove_0_19.blacklistcheck2(arg_14_0)
	-- print 14
	local slot_14_0 = ove_0_19.blacklist2(arg_14_0)

	if slot_14_0 == 1 and ove_0_15.killsteal.oneshot.blacklist.a1:get() or slot_14_0 == 2 and ove_0_15.killsteal.oneshot.blacklist.a2:get() or slot_14_0 == 3 and ove_0_15.killsteal.oneshot.blacklist.a3:get() or slot_14_0 == 4 and ove_0_15.killsteal.oneshot.blacklist.a4:get() or slot_14_0 == 5 and ove_0_15.killsteal.oneshot.blacklist.a5:get() then
		return true
	else
		return false
	end
end

function ove_0_19.blacklist2(arg_15_0)
	-- print 15
	for iter_15_0 = 1, #ove_0_11.blacklist2 do
		if arg_15_0.charName == ove_0_11.blacklist2[iter_15_0] then
			return iter_15_0
		end
	end
end

function ove_0_19.AutoE(arg_16_0)
	-- print 16
	if not ove_0_11.IsValidTarget(arg_16_0) or ove_0_15.misc.autoe.use:get() ~= 1 or not ove_0_14.Ready() then
		return
	end

	if ove_0_16.Wpos and arg_16_0.pos:dist(ove_0_16.Wpos) < ove_0_14.range then
		ove_0_14.Cast(arg_16_0)
	end

	if ove_0_16.Rpos and arg_16_0.pos:dist(ove_0_16.Rpos) < ove_0_14.range then
		ove_0_14.Cast(arg_16_0)
	end
end

return ove_0_19

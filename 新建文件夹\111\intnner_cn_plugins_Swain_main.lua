

local ove_0_10 = module.internal("orb")
local ove_0_11 = module.load(header.id, "plugins/Swain/menu")
local ove_0_12 = module.load(header.id, "plugins/Swain/core")
local ove_0_13 = module.load(header.id, "common/Draw/draw")
local ove_0_14 = module.load(header.id, "plugins/Swain/spells/q")
local ove_0_15 = module.load(header.id, "plugins/Swain/spells/w")
local ove_0_16 = module.load(header.id, "plugins/Swain/spells/e")
local ove_0_17 = module.load(header.id, "plugins/Swain/spells/e_recast")
local ove_0_18 = module.load(header.id, "plugins/Swain/spells/r")
local ove_0_19 = module.load(header.id, "plugins/Swain/spells/demonflare")
local ove_0_20 = module.load(header.id, "plugins/Swain/lock")
local ove_0_21 = module.load(header.id, "plugins/Swain/damage")
local ove_0_22 = module.load(header.id, "plugins/Swain/helper")

local function ove_0_23()
	ove_0_12.get_action()
end

local function ove_0_24(arg_6_0)
	if arg_6_0.owner.ptr == player.ptr then
		ove_0_12.on_recv_spells(arg_6_0)
	end

	if arg_6_0.owner.team == TEAM_ENEMY then
		ove_0_22.on_process_spell(arg_6_0)
	end
end

local function ove_0_25(arg_7_0)
	ove_0_20.on_create_spell(arg_7_0)
end

local function ove_0_26(arg_8_0)
	ove_0_17.create_w_xerath(arg_8_0)
end

local function ove_0_27(arg_9_0)
	ove_0_20.on_delete_spell(arg_9_0)
end

local function ove_0_28(arg_10_0)
	ove_0_17.delete_w_xerath(arg_10_0)
end

local function ove_0_29()
	if player.isDead then
		return
	end

	if player.isOnScreen then
		ove_0_14.on_draw()
		ove_0_15.on_draw()
		ove_0_16.on_draw()
		ove_0_18.on_draw()
	end
end

local function ove_0_30()
	if player.isDead then
		return
	end

	if ove_0_11.draws.drawqDamage:get() then
		ove_0_21.over_layer()
	end

	if not player.isOnScreen then
		return
	end

	if not ove_0_11.draws.draw_toggle:get() then
		return
	end

	if player.isOnScreen then
		local slot_12_0 = graphics.world_to_screen(player.pos)
		local slot_12_1, slot_12_2 = graphics.text_area("Farm:", 14)

		if ove_0_11.farming.toggleFarm:get() then
			ove_0_13.text("Farm: ", 14, slot_12_0.x, slot_12_0.y + 40, graphics.argb(255, 255, 255, 255), "right")
			ove_0_13.text("On", 14, slot_12_0.x + slot_12_1 / 1.9, slot_12_0.y + 40, graphics.argb(255, 7, 219, 63), "right")
		else
			ove_0_13.text("Farm: ", 14, slot_12_0.x, slot_12_0.y + 40, graphics.argb(255, 255, 255, 255), "right")
			ove_0_13.text("OFF", 14, slot_12_0.x + slot_12_1 / 1.5, slot_12_0.y + 40, graphics.argb(255, 219, 7, 7), "right")
		end

		local slot_12_3, slot_12_4 = graphics.text_area("E Lock:", 14)

		if ove_0_11.misc.mov.use_magnet:get() then
			ove_0_13.text("E Lock: ", 14, slot_12_0.x + slot_12_1 / 2.8, slot_12_0.y + 55, graphics.argb(255, 255, 255, 255), "right")
			ove_0_13.text("On", 14, slot_12_0.x + slot_12_3 / 1.8, slot_12_0.y + 55, graphics.argb(255, 7, 219, 63), "right")
		else
			ove_0_13.text("E Lock: ", 14, slot_12_0.x + slot_12_1 / 2.8, slot_12_0.y + 55, graphics.argb(255, 255, 255, 255), "right")
			ove_0_13.text("OFF", 14, slot_12_0.x + slot_12_3 / 1.4, slot_12_0.y + 55, graphics.argb(255, 219, 7, 7), "right")
		end
	end
end

ove_0_10.combat.register_f_pre_tick(ove_0_23)
cb.add(cb.spell, ove_0_24)
cb.add(cb.draw, ove_0_29)
cb.add(cb.draw_overlay, ove_0_30)
cb.add(cb.create_obj, ove_0_26)
cb.add(cb.delete_obj, ove_0_28)
cb.add(cb.create_missile, ove_0_25)
cb.add(cb.delete_missile, ove_0_27)

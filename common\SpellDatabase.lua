
return {
	AatroxQ = {
		charName = "Aatrox",
		radius = 200,
		range = 650,
		type = "linear",
		delay = 0.6,
		cc = true,
		aoe = true,
		slot = 0,
		hitbox = true,
		collision = false,
		speedss = math.huge
	},
	AatroxQ2 = {
		radius = 300,
		range = 525,
		charName = "Aatrox",
		type = "linear",
		delay = 0.6,
		cc = true,
		aoe = true,
		slot = 0,
		hitbox = true,
		collision = false,
		speeds = math.huge
	},
	AatroxQ3 = {
		radius = 300,
		range = 200,
		charName = "Aatrox",
		type = "circular",
		delay = 0.6,
		cc = true,
		aoe = true,
		slot = 0,
		hitbox = true,
		collision = false,
		speeds = math.huge
	},
	AatroxW = {
		radius = 80,
		range = 825,
		charName = "Aatrox",
		type = "linear",
		delay = 0.25,
		cc = true,
		aoe = false,
		slot = 1,
		hitbox = true,
		speeds = 1800,
		collision = true
	},
	AatroxE = {
		delay = 0.25,
		range = 1000,
		aoe = true,
		type = "triangular",
		charName = "Aatrox",
		radius2 = 0,
		cc = true,
		radius1 = 120,
		slot = 2,
		hitbox = true,
		collision = false,
		speedss = 1200
	},
	AhriOrbofDeception = {
		charName = "Ahri",
		radius = 100,
		range = 880,
		type = "linear",
		delay = 0.25,
		cc = false,
		aoe = true,
		slot = 0,
		hitbox = true,
		collision = false,
		speedss = 2500
	},
	AhriOrbReturn = {
		charName = "Ahri",
		radius = 100,
		range = 880,
		type = "linear",
		delay = 0.25,
		cc = false,
		aoe = true,
		slot = 0,
		hitbox = true,
		collision = false,
		speedss = 2500
	},
	AhriSeduce = {
		charName = "Ahri",
		radius = 60,
		range = 965,
		type = "linear",
		delay = 0.25,
		cc = false,
		aoe = false,
		slot = 2,
		hitbox = true,
		collision = true,
		speedss = 1550
	},
	AkaliQ = {
		charName = "Akali",
		range = 550,
		collision = false,
		type = "conic",
		delay = 0.25,
		cc = true,
		aoe = true,
		slot = 0,
		hitbox = true,
		angle = 45,
		speeds = math.huge
	},
	AkaliW = {
		radius = 300,
		range = 300,
		charName = "Akali",
		type = "circular",
		delay = 0.25,
		cc = false,
		aoe = true,
		slot = 1,
		hitbox = false,
		collision = false,
		speeds = math.huge
	},
	AkaliE = {
		radius = 70,
		range = 825,
		charName = "Akali",
		type = "linear",
		delay = 0.25,
		cc = false,
		aoe = false,
		slot = 2,
		hitbox = true,
		speeds = 1650,
		collision = true
	},
	AkaliR = {
		radius = 80,
		range = 575,
		charName = "Akali",
		type = "linear",
		delay = 0,
		cc = true,
		aoe = true,
		slot = 3,
		hitbox = true,
		speeds = 1650,
		collision = false
	},
	AkaliRb = {
		radius = 80,
		range = 575,
		charName = "Akali",
		type = "linear",
		delay = 0,
		cc = false,
		aoe = true,
		slot = 3,
		hitbox = true,
		speeds = 3300,
		collision = false
	},
	Pulverize = {
		charName = "Alistar",
		radius = 365,
		range = 0,
		type = "circular",
		delay = 0.25,
		cc = true,
		aoe = true,
		slot = 0,
		hitbox = true,
		collision = false,
		speedss = math.huge
	},
	BandageToss = {
		charName = "Amumu",
		radius = 80,
		range = 1100,
		type = "linear",
		delay = 0.25,
		cc = true,
		aoe = false,
		slot = 0,
		hitbox = true,
		collision = true,
		speedss = 2000
	},
	AuraofDespair = {
		charName = "Amumu",
		radius = 300,
		range = 0,
		type = "circular",
		delay = 0.307,
		cc = false,
		aoe = true,
		slot = 1,
		hitbox = false,
		collision = false,
		speedss = math.huge
	},
	Tantrum = {
		charName = "Amumu",
		radius = 350,
		range = 0,
		type = "circular",
		delay = 0.25,
		cc = false,
		aoe = true,
		slot = 2,
		hitbox = false,
		collision = false,
		speedss = math.huge
	},
	CurseoftheSadMummy = {
		charName = "Amumu",
		radius = 550,
		range = 0,
		type = "circular",
		delay = 0.25,
		cc = true,
		aoe = true,
		slot = 3,
		hitbox = false,
		collision = false,
		speedss = math.huge
	},
	FlashFrost = {
		charName = "Anivia",
		radius = 110,
		range = 1075,
		type = "linear",
		delay = 0.25,
		cc = true,
		aoe = true,
		slot = 0,
		hitbox = true,
		collision = false,
		speedss = 850
	},
	Crystallize = {
		delay = 0.25,
		range = 1000,
		aoe = true,
		type = "rectangle",
		charName = "Anivia",
		radius2 = 75,
		cc = true,
		radius1 = 250,
		slot = 1,
		hitbox = true,
		collision = false,
		speedss = math.huge
	},
	GlacialStorm = {
		charName = "Anivia",
		radius = 400,
		range = 750,
		type = "circular",
		delay = 0.25,
		cc = true,
		aoe = true,
		slot = 3,
		hitbox = true,
		collision = false,
		speedss = math.huge
	},
	Incinerate = {
		charName = "Annie",
		range = 600,
		collision = false,
		type = "conic",
		delay = 0.25,
		cc = false,
		aoe = true,
		slot = 1,
		hitbox = false,
		angle = 50,
		speedss = math.huge
	},
	InfernalGuardian = {
		charName = "Annie",
		radius = 290,
		range = 600,
		type = "circular",
		delay = 0.25,
		cc = false,
		aoe = true,
		slot = 3,
		hitbox = true,
		collision = false,
		speedss = math.huge
	},
	Volley = {
		delay = 0.25,
		radius = 20,
		range = 1200,
		type = "conic",
		charName = "Ashe",
		collision = true,
		cc = true,
		aoe = true,
		slot = 1,
		hitbox = true,
		angle = 57.5,
		speedss = 1500
	},
	EnchantedCrystalArrow = {
		charName = "Ashe",
		radius = 130,
		range = 25000,
		type = "linear",
		delay = 0.25,
		cc = true,
		aoe = false,
		slot = 3,
		hitbox = true,
		collision = false,
		speedss = 1600
	},
	AurelionSolQ = {
		charName = "AurelionSol",
		radius = 210,
		range = 1075,
		type = "linear",
		delay = 0.25,
		cc = true,
		aoe = true,
		slot = 0,
		hitbox = true,
		collision = false,
		speedss = 850
	},
	AurelionSolE = {
		charName = "AurelionSol",
		radius = 80,
		range = 7000,
		type = "linear",
		delay = 0.25,
		cc = false,
		aoe = true,
		slot = 2,
		hitbox = true,
		collision = false,
		speedss = 600
	},
	AurelionSolR = {
		charName = "AurelionSol",
		radius = 120,
		range = 1500,
		type = "linear",
		delay = 0.35,
		cc = true,
		aoe = true,
		slot = 3,
		hitbox = true,
		collision = false,
		speedss = 4500
	},
	BardQ = {
		charName = "Bard",
		radius = 60,
		range = 950,
		type = "linear",
		delay = 0.25,
		cc = true,
		aoe = true,
		slot = 0,
		hitbox = true,
		collision = true,
		speedss = 1500
	},
	BardW = {
		charName = "Bard",
		radius = 100,
		range = 800,
		type = "circular",
		delay = 0.25,
		cc = false,
		aoe = true,
		slot = 1,
		hitbox = true,
		collision = false,
		speedss = math.huge
	},
	BardR = {
		charName = "Bard",
		radius = 350,
		range = 3400,
		type = "circular",
		delay = 0.5,
		cc = true,
		aoe = true,
		slot = 3,
		hitbox = true,
		collision = false,
		speedss = 2100
	},
	RocketGrab = {
		charName = "Blitzcrank",
		radius = 70,
		range = 925,
		type = "linear",
		delay = 0.25,
		cc = true,
		aoe = false,
		slot = 0,
		hitbox = true,
		collision = true,
		speedss = 1800
	},
	StaticField = {
		charName = "Blitzcrank",
		radius = 600,
		range = 0,
		type = "circular",
		delay = 0.25,
		cc = true,
		aoe = true,
		slot = 3,
		hitbox = false,
		collision = false,
		speedss = math.huge
	},
	BrandQ = {
		charName = "Brand",
		radius = 60,
		range = 1050,
		type = "linear",
		delay = 0.25,
		cc = true,
		aoe = false,
		slot = 0,
		hitbox = true,
		collision = true,
		speedss = 1600
	},
	BrandW = {
		charName = "Brand",
		radius = 250,
		range = 900,
		type = "circular",
		delay = 0.85,
		cc = false,
		aoe = true,
		slot = 1,
		hitbox = true,
		collision = false,
		speedss = math.huge
	},
	BraumQ = {
		charName = "Braum",
		radius = 60,
		range = 1000,
		type = "linear",
		delay = 0.25,
		cc = true,
		aoe = true,
		slot = 0,
		hitbox = true,
		collision = true,
		speedss = 1700
	},
	BraumRWrapper = {
		charName = "Braum",
		radius = 115,
		range = 1250,
		type = "linear",
		delay = 0.5,
		cc = true,
		aoe = true,
		slot = 3,
		hitbox = true,
		collision = false,
		speedss = 1400
	},
	CaitlynQ = {
		charName = "Caitlyn",
		radius = 60,
		range = 1500,
		type = "linear",
		delay = 0.625,
		cc = false,
		aoe = true,
		slot = 0,
		hitbox = true,
		collision = false,
		speedss = 2200
	},
	CaitlynQ2 = {
		charName = "Caitlyn",
		radius = 120,
		range = 1500,
		type = "linear",
		delay = 0.625,
		cc = false,
		aoe = true,
		slot = 0,
		hitbox = true,
		collision = false,
		speedss = 2200
	},
	CaitlynW = {
		charName = "Caitlyn",
		radius = 75,
		range = 800,
		type = "circular",
		delay = 0.25,
		cc = true,
		aoe = false,
		slot = 1,
		hitbox = true,
		collision = false,
		speedss = math.huge
	},
	CaitlynE = {
		charName = "Caitlyn",
		radius = 60,
		range = 750,
		type = "linear",
		delay = 0.25,
		cc = true,
		aoe = false,
		slot = 2,
		hitbox = true,
		collision = true,
		speedss = 1500
	},
	CaitlynR = {
		charName = "Caitlyn",
		radius = 60,
		range = 3500,
		type = "linear",
		delay = 0.7,
		cc = true,
		aoe = false,
		slot = 3,
		hitbox = true,
		collision = true,
		speedss = 3200
	},
	CamilleW = {
		charName = "Camille",
		range = 610,
		collision = false,
		type = "conic",
		delay = 0.75,
		cc = true,
		aoe = true,
		slot = 1,
		hitbox = false,
		angle = 80,
		speedss = math.huge
	},
	CamilleE = {
		charName = "Camille",
		radius = 60,
		range = 800,
		type = "linear",
		delay = 0.25,
		cc = true,
		aoe = false,
		slot = 2,
		hitbox = true,
		collision = false,
		speedss = 1900
	},
	CassiopeiaQ = {
		charName = "Cassiopeia",
		radius = 150,
		range = 850,
		type = "circular",
		delay = 0.4,
		cc = false,
		aoe = true,
		slot = 0,
		hitbox = true,
		collision = false,
		speedss = math.huge
	},
	CassiopeiaW = {
		charName = "Cassiopeia",
		radius = 160,
		range = 800,
		type = "circular",
		delay = 0.25,
		cc = false,
		aoe = true,
		slot = 1,
		hitbox = true,
		collision = false,
		speedss = math.huge
	},
	CassiopeiaR = {
		charName = "Cassiopeia",
		range = 825,
		collision = false,
		type = "conic",
		delay = 0.5,
		cc = true,
		aoe = true,
		slot = 3,
		hitbox = false,
		angle = 80,
		speedss = math.huge
	},
	Rupture = {
		charName = "ChoGath",
		radius = 250,
		range = 950,
		type = "circular",
		delay = 0.5,
		cc = true,
		aoe = true,
		slot = 0,
		hitbox = true,
		collision = false,
		speedss = math.huge
	},
	FeralScream = {
		charName = "ChoGath",
		range = 650,
		collision = false,
		type = "conic",
		delay = 0.5,
		cc = true,
		aoe = true,
		slot = 1,
		hitbox = false,
		angle = 60,
		speedss = math.huge
	},
	PhosphorusBomb = {
		charName = "Corki",
		radius = 250,
		range = 825,
		type = "circular",
		delay = 0.25,
		cc = false,
		aoe = true,
		slot = 0,
		hitbox = true,
		collision = false,
		speedss = 1000
	},
	CarpetBomb = {
		charName = "Corki",
		radius = 100,
		range = 600,
		type = "linear",
		delay = 0,
		cc = false,
		aoe = true,
		slot = 1,
		hitbox = true,
		collision = false,
		speedss = 650
	},
	CarpetBombMega = {
		charName = "Corki",
		radius = 100,
		range = 1800,
		type = "linear",
		delay = 0,
		cc = false,
		aoe = true,
		slot = 1,
		hitbox = true,
		collision = false,
		speedss = 1500
	},
	GGun = {
		charName = "Corki",
		range = 600,
		collision = false,
		type = "conic",
		delay = 0.25,
		cc = false,
		aoe = true,
		slot = 2,
		hitbox = false,
		angle = 35,
		speedss = math.huge
	},
	MissileBarrageMissile = {
		charName = "Corki",
		radius = 35,
		range = 1225,
		type = "linear",
		delay = 0.175,
		cc = false,
		aoe = false,
		slot = 3,
		hitbox = true,
		collision = true,
		speedss = 1950
	},
	MissileBarrageMissile2 = {
		charName = "Corki",
		radius = 35,
		range = 1225,
		type = "linear",
		delay = 0.175,
		cc = false,
		aoe = false,
		slot = 3,
		hitbox = true,
		collision = true,
		speedss = 1950
	},
	DariusCleave = {
		charName = "Darius",
		radius = 425,
		range = 0,
		type = "circular",
		delay = 0.75,
		cc = false,
		aoe = true,
		slot = 0,
		hitbox = false,
		collision = false,
		speedss = math.huge
	},
	DariusAxeGrabCone = {
		charName = "Darius",
		range = 535,
		collision = false,
		type = "conic",
		delay = 0.25,
		cc = true,
		aoe = true,
		slot = 2,
		hitbox = false,
		angle = 50,
		speedss = math.huge
	},
	DariusNoxianTacticsONH = {
		charName = "Darius",
		range = 535,
		collision = false,
		type = "target",
		delay = 0.25,
		cc = true,
		aoe = true,
		slot = 1,
		hitbox = false,
		angle = 50,
		speedss = math.huge
	},
	DianaArc = {
		charName = "Diana",
		radius = 205,
		range = 900,
		type = "arc",
		delay = 0.25,
		cc = false,
		aoe = true,
		slot = 0,
		hitbox = true,
		collision = false,
		speedss = 1400
	},
	InfectedCleaverMissileCast = {
		charName = "DrMundo",
		radius = 60,
		range = 975,
		type = "linear",
		delay = 0.25,
		cc = true,
		aoe = false,
		slot = 0,
		hitbox = true,
		collision = true,
		speedss = 1850
	},
	DravenDoubleShot = {
		charName = "Draven",
		radius = 120,
		range = 1050,
		type = "linear",
		delay = 0.25,
		cc = true,
		aoe = true,
		slot = 2,
		hitbox = true,
		collision = false,
		speedss = 1400
	},
	DravenRCast = {
		charName = "Draven",
		radius = 130,
		range = 25000,
		type = "linear",
		delay = 0.5,
		cc = false,
		aoe = true,
		slot = 3,
		hitbox = true,
		collision = false,
		speedss = 2000
	},
	DravenRDoublecast = {
		charName = "Draven",
		radius = 130,
		range = 25000,
		type = "linear",
		delay = 0.5,
		cc = false,
		aoe = true,
		slot = 3,
		hitbox = true,
		collision = false,
		speedss = 2000
	},
	EkkoQ = {
		charName = "Ekko",
		radius = 135,
		range = 1075,
		type = "linear",
		delay = 0.25,
		cc = true,
		aoe = true,
		slot = 0,
		hitbox = true,
		collision = false,
		speedss = 1650
	},
	EkkoW = {
		charName = "Ekko",
		radius = 400,
		range = 1600,
		type = "circular",
		delay = 3.75,
		cc = true,
		aoe = true,
		slot = 1,
		hitbox = true,
		collision = false,
		speedss = 1650
	},
	EkkoR = {
		charName = "Ekko",
		radius = 375,
		range = 1600,
		type = "circular",
		delay = 0.25,
		cc = false,
		aoe = true,
		slot = 3,
		hitbox = false,
		collision = false,
		speedss = 1650
	},
	EliseHumanE = {
		charName = "Elise",
		radius = 55,
		range = 1075,
		type = "linear",
		delay = 0.25,
		cc = true,
		aoe = false,
		slot = 2,
		hitbox = true,
		collision = true,
		speedss = 1600
	},
	EvelynnQ = {
		charName = "Evelynn",
		radius = 35,
		range = 800,
		type = "linear",
		delay = 0.25,
		cc = false,
		aoe = false,
		slot = 0,
		hitbox = true,
		collision = true,
		speedss = 2200
	},
	EvelynnR = {
		charName = "Evelynn",
		range = 450,
		collision = false,
		type = "conic",
		delay = 0.35,
		cc = false,
		aoe = true,
		slot = 3,
		hitbox = false,
		angle = 180,
		speedss = math.huge
	},
	EzrealQ = {
		charName = "Ezreal",
		radius = 80,
		range = 1150,
		type = "linear",
		delay = 0.25,
		cc = false,
		aoe = false,
		slot = 0,
		hitbox = true,
		collision = true,
		speedss = 2000
	},
	EzrealW = {
		charName = "Ezreal",
		radius = 80,
		range = 1000,
		type = "linear",
		delay = 0.25,
		cc = false,
		aoe = true,
		slot = 1,
		hitbox = true,
		collision = false,
		speedss = 1550
	},
	EzrealR = {
		charName = "Ezreal",
		radius = 160,
		range = 25000,
		type = "linear",
		delay = 1,
		cc = false,
		aoe = true,
		slot = 3,
		hitbox = true,
		collision = false,
		speedss = 2000
	},
	FioraQ = {
		charName = "Fiora",
		radius = 50,
		range = 400,
		type = "linear",
		delay = 0,
		cc = false,
		aoe = false,
		slot = 0,
		hitbox = true,
		collision = false,
		speedss = 800
	},
	FioraW = {
		charName = "Fiora",
		radius = 85,
		range = 750,
		type = "linear",
		delay = 0.75,
		cc = true,
		aoe = true,
		slot = 1,
		hitbox = true,
		collision = false,
		speedss = math.huge
	},
	FizzR = {
		charName = "Fizz",
		radius = 120,
		range = 1300,
		type = "linear",
		delay = 0.25,
		cc = true,
		aoe = true,
		slot = 3,
		hitbox = true,
		collision = false,
		speedss = 1300
	},
	GalioQ = {
		charName = "Galio",
		radius = 150,
		range = 825,
		type = "arc",
		delay = 0.25,
		cc = false,
		aoe = true,
		slot = 0,
		hitbox = true,
		collision = false,
		speedss = 1150
	},
	GalioE = {
		charName = "Galio",
		radius = 160,
		range = 650,
		type = "linear",
		delay = 0.45,
		cc = true,
		aoe = true,
		slot = 2,
		hitbox = true,
		collision = false,
		speedss = 1400
	},
	GalioR = {
		charName = "Galio",
		radius = 500,
		range = 5500,
		type = "circular",
		delay = 2.75,
		cc = true,
		aoe = true,
		slot = 3,
		hitbox = true,
		collision = false,
		speedss = math.huge
	},
	GangplankE = {
		charName = "Gangplank",
		radius = 400,
		range = 1000,
		type = "circular",
		delay = 0.25,
		cc = true,
		aoe = true,
		slot = 2,
		hitbox = true,
		collision = false,
		speedss = math.huge
	},
	GangplankR = {
		charName = "Gangplank",
		radius = 600,
		range = 25000,
		type = "circular",
		delay = 0.25,
		cc = true,
		aoe = true,
		slot = 3,
		hitbox = true,
		collision = false,
		speedss = math.huge
	},
	GnarQ = {
		charName = "Gnar",
		radius = 55,
		range = 1100,
		type = "linear",
		delay = 0.25,
		cc = true,
		aoe = true,
		slot = 0,
		hitbox = true,
		collision = false,
		speedss = 1700
	},
	GnarQReturn = {
		charName = "Gnar",
		radius = 70,
		range = 3000,
		type = "linear",
		delay = 0.25,
		cc = true,
		aoe = true,
		slot = 0,
		hitbox = true,
		collision = false,
		speedss = 1700
	},
	GnarE = {
		charName = "Gnar",
		radius = 160,
		range = 475,
		type = "circular",
		delay = 0.25,
		cc = true,
		aoe = false,
		slot = 2,
		hitbox = true,
		collision = false,
		speedss = 900
	},
	GnarBigQ = {
		charName = "Gnar",
		radius = 90,
		range = 1100,
		type = "linear",
		delay = 0.5,
		cc = true,
		aoe = true,
		slot = 0,
		hitbox = true,
		collision = true,
		speedss = 2100
	},
	GnarBigW = {
		charName = "Gnar",
		radius = 100,
		range = 550,
		type = "linear",
		delay = 0.6,
		cc = true,
		aoe = true,
		slot = 1,
		hitbox = true,
		collision = false,
		speedss = math.huge
	},
	GnarBigE = {
		charName = "Gnar",
		radius = 375,
		range = 600,
		type = "circular",
		delay = 0.25,
		cc = true,
		aoe = true,
		slot = 2,
		hitbox = true,
		collision = false,
		speedss = 800
	},
	GnarR = {
		charName = "Gnar",
		radius = 475,
		range = 475,
		type = "circular",
		delay = 0.25,
		cc = true,
		aoe = true,
		slot = 3,
		hitbox = true,
		collision = false,
		speedss = math.huge
	},
	GragasQ = {
		charName = "Gragas",
		radius = 250,
		range = 850,
		type = "circular",
		delay = 0.25,
		cc = true,
		aoe = true,
		slot = 0,
		hitbox = true,
		collision = false,
		speedss = 1000
	},
	GragasE = {
		charName = "Gragas",
		radius = 170,
		range = 600,
		type = "linear",
		delay = 0.25,
		cc = true,
		aoe = true,
		slot = 2,
		hitbox = true,
		collision = true,
		speedss = 900
	},
	GragasR = {
		charName = "Gragas",
		radius = 400,
		range = 1000,
		type = "circular",
		delay = 0.25,
		cc = true,
		aoe = true,
		slot = 3,
		hitbox = true,
		collision = false,
		speedss = 1800
	},
	GravesQLineSpell = {
		charName = "Graves",
		radius = 40,
		range = 925,
		type = "linear",
		delay = 0.25,
		cc = false,
		aoe = true,
		slot = 0,
		hitbox = true,
		collision = false,
		speedss = 3700
	},
	GravesQLineMis = {
		delay = 0.25,
		range = 925,
		aoe = true,
		type = "rectangle",
		charName = "Graves",
		radius2 = 100,
		cc = false,
		radius1 = 250,
		slot = 0,
		hitbox = true,
		collision = false,
		speedss = math.huge
	},
	GravesQReturn = {
		charName = "Graves",
		radius = 40,
		range = 925,
		type = "linear",
		delay = 0.25,
		cc = false,
		aoe = true,
		slot = 0,
		hitbox = true,
		collision = false,
		speedss = 1850
	},
	GravesSmokeGrenade = {
		charName = "Graves",
		radius = 250,
		range = 950,
		type = "circular",
		delay = 0.25,
		cc = true,
		aoe = true,
		slot = 1,
		hitbox = true,
		collision = false,
		speedss = 1450
	},
	GravesChargeShot = {
		charName = "Graves",
		radius = 100,
		range = 1000,
		type = "linear",
		delay = 0.25,
		cc = false,
		aoe = true,
		slot = 3,
		hitbox = true,
		collision = false,
		speedss = 1950
	},
	GravesChargeShotFxMissile = {
		charName = "Graves",
		range = 800,
		collision = false,
		type = "conic",
		delay = 0.3,
		cc = false,
		aoe = true,
		slot = 3,
		hitbox = true,
		angle = 80,
		speedss = math.huge
	},
	HecarimRapidSlash = {
		charName = "Hecarim",
		radius = 350,
		range = 0,
		type = "circular",
		delay = 0,
		cc = false,
		aoe = true,
		slot = 0,
		hitbox = false,
		collision = false,
		speedss = math.huge
	},
	HecarimUlt = {
		charName = "Hecarim",
		radius = 210,
		range = 1000,
		type = "linear",
		delay = 0.01,
		cc = true,
		aoe = true,
		slot = 3,
		hitbox = true,
		collision = false,
		speedss = 1200
	},
	HeimerdingerQ = {
		charName = "Heimerdinger",
		radius = 55,
		range = 450,
		type = "circular",
		delay = 0.25,
		cc = false,
		aoe = true,
		slot = 0,
		hitbox = true,
		collision = false,
		speedss = math.huge
	},
	HeimerdingerW = {
		charName = "Heimerdinger",
		radius = 30,
		range = 1325,
		type = "linear",
		delay = 0.25,
		cc = false,
		aoe = true,
		slot = 1,
		hitbox = true,
		collision = true,
		speedss = 2050
	},
	HeimerdingerE = {
		charName = "Heimerdinger",
		radius = 250,
		range = 970,
		type = "circular",
		delay = 0.25,
		cc = true,
		aoe = true,
		slot = 2,
		hitbox = true,
		collision = false,
		speedss = 1200
	},
	HeimerdingerEUlt = {
		charName = "Heimerdinger",
		radius = 250,
		range = 970,
		type = "circular",
		delay = 0.25,
		cc = true,
		aoe = true,
		slot = 2,
		hitbox = true,
		collision = false,
		speedss = 1200
	},
	IllaoiQ = {
		charName = "Illaoi",
		radius = 100,
		range = 850,
		type = "linear",
		delay = 0.75,
		cc = false,
		aoe = true,
		slot = 0,
		hitbox = true,
		collision = false,
		speedss = math.huge
	},
	IllaoiE = {
		charName = "Illaoi",
		radius = 45,
		range = 900,
		type = "linear",
		delay = 0.25,
		cc = false,
		aoe = false,
		slot = 2,
		hitbox = true,
		collision = true,
		speedss = 1800
	},
	IllaoiR = {
		charName = "Illaoi",
		radius = 450,
		range = 0,
		type = "circular",
		delay = 0.5,
		cc = false,
		aoe = true,
		slot = 3,
		hitbox = false,
		collision = false,
		speedss = math.huge
	},
	IreliaW2 = {
		charName = "Irelia",
		radius = 275,
		range = 0,
		type = "circular",
		delay = 0,
		cc = false,
		aoe = true,
		slot = 1,
		hitbox = false,
		collision = false,
		speedss = math.huge
	},
	IreliaW2 = {
		charName = "Irelia",
		radius = 90,
		range = 825,
		type = "linear",
		delay = 0.25,
		cc = false,
		aoe = true,
		slot = 1,
		hitbox = false,
		collision = false,
		speedss = math.huge
	},
	IreliaE = {
		radius = 90,
		range = 850,
		charName = "Irelia",
		type = "circular",
		delay = 0,
		cc = true,
		aoe = true,
		slot = 2,
		hitbox = true,
		speeds = 2000,
		collision = false
	},
	IreliaE2 = {
		radius = 90,
		range = 850,
		charName = "Irelia",
		type = "circular",
		delay = 0,
		cc = true,
		aoe = true,
		slot = 2,
		hitbox = true,
		speeds = 2000,
		collision = false
	},
	IreliaR = {
		charName = "Irelia",
		radius = 160,
		range = 1000,
		type = "linear",
		delay = 0.4,
		cc = true,
		aoe = true,
		slot = 3,
		hitbox = true,
		collision = false,
		speedss = 2000
	},
	IvernQ = {
		charName = "Ivern",
		radius = 50,
		range = 1075,
		type = "linear",
		delay = 0.25,
		cc = true,
		aoe = false,
		slot = 0,
		hitbox = true,
		collision = true,
		speedss = 1300
	},
	IvernW = {
		charName = "Ivern",
		radius = 150,
		range = 800,
		type = "circular",
		delay = 0.25,
		cc = false,
		aoe = true,
		slot = 1,
		hitbox = true,
		collision = false,
		speedss = math.huge
	},
	HowlingGale = {
		charName = "Janna",
		radius = 100,
		range = 1750,
		type = "linear",
		delay = 0,
		cc = true,
		aoe = true,
		slot = 0,
		hitbox = true,
		collision = false,
		speedss = 1167
	},
	ReapTheWhirlwind = {
		charName = "Janna",
		radius = 725,
		range = 0,
		type = "circular",
		delay = 0.001,
		cc = true,
		aoe = true,
		slot = 3,
		hitbox = false,
		collision = false,
		speedss = math.huge
	},
	JarvanIVDragonStrike = {
		charName = "JarvanIV",
		radius = 60,
		range = 770,
		type = "linear",
		delay = 0.4,
		cc = false,
		aoe = true,
		slot = 0,
		hitbox = true,
		collision = false,
		speedss = math.huge
	},
	JarvanIVGoldenAegis = {
		charName = "JarvanIV",
		radius = 625,
		range = 0,
		type = "circular",
		delay = 0.125,
		cc = true,
		aoe = true,
		slot = 1,
		hitbox = false,
		collision = false,
		speedss = math.huge
	},
	JarvanIVDemacianStandard = {
		charName = "JarvanIV",
		radius = 175,
		range = 860,
		type = "circular",
		delay = 0,
		cc = false,
		aoe = true,
		slot = 2,
		hitbox = true,
		collision = false,
		speedss = 3440
	},
	JaxCounterStrike = {
		charName = "Jax",
		radius = 300,
		range = 0,
		type = "circular",
		delay = 1.4,
		cc = true,
		aoe = true,
		slot = 2,
		hitbox = false,
		collision = false,
		speedss = math.huge
	},
	JayceShockBlast = {
		charName = "Jayce",
		radius = 75,
		range = 1050,
		type = "linear",
		delay = 0.214,
		cc = false,
		aoe = true,
		slot = 0,
		hitbox = true,
		collision = true,
		speedss = 1450
	},
	JayceShockBlastWallMis = {
		charName = "Jayce",
		radius = 105,
		range = 2030,
		type = "linear",
		delay = 0.214,
		cc = false,
		aoe = true,
		slot = 0,
		hitbox = true,
		collision = true,
		speedss = 1890
	},
	JayceStaticField = {
		charName = "Jayce",
		radius = 285,
		range = 0,
		type = "circular",
		delay = 0.25,
		cc = false,
		aoe = true,
		slot = 1,
		hitbox = false,
		collision = false,
		speedss = math.huge
	},
	JhinW = {
		charName = "Jhin",
		radius = 40,
		range = 3000,
		type = "linear",
		delay = 0.75,
		cc = true,
		aoe = false,
		slot = 1,
		hitbox = true,
		collision = false,
		speedss = 5000
	},
	JhinE = {
		charName = "Jhin",
		radius = 140,
		range = 750,
		type = "circular",
		delay = 0.25,
		cc = true,
		aoe = false,
		slot = 2,
		hitbox = true,
		collision = false,
		speedss = 1650
	},
	JhinRShot = {
		charName = "Jhin",
		radius = 80,
		range = 3500,
		type = "linear",
		delay = 0.25,
		cc = true,
		aoe = false,
		slot = 3,
		hitbox = true,
		collision = false,
		speedss = 5000
	},
	JinxW = {
		charName = "Jinx",
		radius = 50,
		range = 1450,
		type = "linear",
		delay = 0.6,
		cc = true,
		aoe = false,
		slot = 1,
		hitbox = true,
		collision = true,
		speedss = 3200
	},
	JinxE = {
		charName = "Jinx",
		radius = 100,
		range = 900,
		type = "circular",
		delay = 1.5,
		cc = true,
		aoe = true,
		slot = 2,
		hitbox = true,
		collision = false,
		speedss = 2570
	},
	JinxR = {
		charName = "Jinx",
		radius = 110,
		range = 25000,
		type = "linear",
		delay = 0.6,
		cc = false,
		aoe = true,
		slot = 3,
		hitbox = true,
		collision = false,
		speedss = 1700
	},
	KatarinaW = {
		charName = "Katarina",
		radius = 340,
		range = 0,
		type = "circular",
		delay = 1.25,
		cc = false,
		aoe = true,
		slot = 1,
		hitbox = false,
		collision = false,
		speedss = math.huge
	},
	KatarinaE = {
		charName = "Katarina",
		radius = 150,
		range = 725,
		type = "circular",
		delay = 0.15,
		cc = false,
		aoe = false,
		slot = 2,
		hitbox = true,
		collision = false,
		speedss = math.huge
	},
	KatarinaR = {
		charName = "Katarina",
		radius = 550,
		range = 0,
		type = "circular",
		delay = 0,
		cc = false,
		aoe = true,
		slot = 3,
		hitbox = false,
		collision = false,
		speedss = math.huge
	},
	KalistaMysticShot = {
		charName = "Kalista",
		radius = 35,
		range = 1150,
		type = "linear",
		delay = 0.35,
		cc = false,
		aoe = false,
		slot = 0,
		hitbox = true,
		collision = true,
		speedss = 2100
	},
	KarmaQ = {
		charName = "Karma",
		radius = 80,
		range = 950,
		type = "linear",
		delay = 0.25,
		cc = true,
		aoe = false,
		slot = 0,
		hitbox = true,
		collision = true,
		speedss = 1750
	},
	KarmaQMantra = {
		charName = "Karma",
		radius = 80,
		range = 950,
		type = "linear",
		delay = 0.25,
		cc = true,
		aoe = false,
		slot = 0,
		hitbox = true,
		collision = true,
		speedss = 1750
	},
	KarthusLayWasteA1 = {
		charName = "Karthus",
		radius = 200,
		range = 875,
		type = "circular",
		delay = 0.5,
		cc = false,
		aoe = true,
		slot = 0,
		hitbox = true,
		collision = false,
		speedss = math.huge
	},
	KarthusLayWasteA2 = {
		charName = "Karthus",
		radius = 200,
		range = 875,
		type = "circular",
		delay = 0.5,
		cc = false,
		aoe = true,
		slot = 0,
		hitbox = true,
		collision = false,
		speedss = math.huge
	},
	KarthusLayWasteA3 = {
		charName = "Karthus",
		radius = 200,
		range = 875,
		type = "circular",
		delay = 0.5,
		cc = false,
		aoe = true,
		slot = 0,
		hitbox = true,
		collision = false,
		speedss = math.huge
	},
	KarthusWallOfPain = {
		delay = 0.25,
		range = 1000,
		aoe = true,
		type = "rectangle",
		charName = "Karthus",
		radius2 = 75,
		cc = true,
		radius1 = 470,
		slot = 1,
		hitbox = true,
		collision = false,
		speedss = math.huge
	},
	ForcePulse = {
		charName = "Kassadin",
		range = 600,
		collision = false,
		type = "conic",
		delay = 0.25,
		cc = true,
		aoe = true,
		slot = 2,
		hitbox = false,
		angle = 80,
		speedss = math.huge
	},
	Riftwalk = {
		charName = "Kassadin",
		radius = 300,
		range = 500,
		type = "circular",
		delay = 0.25,
		cc = false,
		aoe = true,
		slot = 3,
		hitbox = true,
		collision = false,
		speedss = math.huge
	},
	KaynQ = {
		charName = "Kayn",
		radius = 350,
		range = 0,
		type = "circular",
		delay = 0.15,
		cc = false,
		aoe = true,
		slot = 0,
		hitbox = false,
		collision = false,
		speedss = math.huge
	},
	KaynW = {
		charName = "Kayn",
		radius = 90,
		range = 700,
		type = "linear",
		delay = 0.55,
		cc = true,
		aoe = true,
		slot = 1,
		hitbox = true,
		collision = false,
		speedss = math.huge
	},
	KennenShurikenHurlMissile1 = {
		charName = "Kennen",
		radius = 45,
		range = 1050,
		type = "linear",
		delay = 0.175,
		cc = false,
		aoe = false,
		slot = 0,
		hitbox = true,
		collision = true,
		speedss = 1650
	},
	KennenShurikenStorm = {
		charName = "Kennen",
		radius = 550,
		range = 0,
		type = "circular",
		delay = 0.25,
		cc = false,
		aoe = true,
		slot = 3,
		hitbox = false,
		collision = false,
		speedss = math.huge
	},
	KaisaW = {
		charName = "Kaisa",
		radius = 65,
		range = 3000,
		type = "linear",
		delay = 0.4,
		cc = false,
		aoe = false,
		slot = 1,
		hitbox = true,
		collision = true,
		speedss = 1750
	},
	KhazixW = {
		charName = "Khazix",
		radius = 60,
		range = 1000,
		type = "linear",
		delay = 0.25,
		cc = false,
		aoe = false,
		slot = 1,
		hitbox = true,
		collision = true,
		speedss = 1650
	},
	KhazixWLong = {
		charName = "Khazix",
		radius = 70,
		range = 1000,
		type = "linear",
		delay = 0.25,
		cc = true,
		aoe = true,
		slot = 1,
		hitbox = true,
		collision = true,
		speedss = 1650
	},
	KhazixE = {
		charName = "Khazix",
		radius = 320,
		range = 700,
		type = "circular",
		delay = 0.25,
		cc = false,
		aoe = true,
		slot = 2,
		hitbox = true,
		collision = false,
		speedss = 1400
	},
	KhazixELong = {
		charName = "Khazix",
		radius = 320,
		range = 900,
		type = "circular",
		delay = 0.25,
		cc = false,
		aoe = true,
		slot = 2,
		hitbox = true,
		collision = false,
		speedss = 1400
	},
	KindredQ = {
		charName = "Kindred",
		radius = 55,
		range = 340,
		type = "linear",
		delay = 0.01,
		cc = false,
		aoe = false,
		slot = 0,
		hitbox = true,
		collision = false,
		speedss = 1360
	},
	KindredR = {
		charName = "Kindred",
		radius = 500,
		range = 0,
		type = "circular",
		delay = 0.25,
		cc = false,
		aoe = true,
		slot = 3,
		hitbox = false,
		collision = false,
		speedss = math.huge
	},
	KledQ = {
		charName = "Kled",
		radius = 60,
		range = 800,
		type = "linear",
		delay = 0.25,
		cc = true,
		aoe = false,
		slot = 0,
		hitbox = true,
		collision = true,
		speedss = 1400
	},
	KledEDash = {
		charName = "Kled",
		radius = 90,
		range = 550,
		type = "linear",
		delay = 0,
		cc = false,
		aoe = true,
		slot = 2,
		hitbox = true,
		collision = false,
		speedss = 1100
	},
	KledRiderQ = {
		charName = "Kled",
		range = 700,
		collision = false,
		type = "conic",
		delay = 0.25,
		cc = false,
		aoe = true,
		slot = 0,
		hitbox = false,
		angle = 25,
		speedss = math.huge
	},
	KogMawQ = {
		charName = "KogMaw",
		radius = 60,
		range = 1175,
		type = "linear",
		delay = 0.25,
		cc = false,
		aoe = false,
		slot = 0,
		hitbox = true,
		collision = true,
		speedss = 1600
	},
	KogMawVoidOoze = {
		charName = "KogMaw",
		radius = 115,
		range = 1280,
		type = "linear",
		delay = 0.25,
		cc = true,
		aoe = true,
		slot = 2,
		hitbox = true,
		collision = false,
		speedss = 1350
	},
	KogMawLivingArtillery = {
		charName = "KogMaw",
		radius = 200,
		range = 1800,
		type = "circular",
		delay = 0.85,
		cc = false,
		aoe = true,
		slot = 3,
		hitbox = true,
		collision = false,
		speedss = math.huge
	},
	LeblancW = {
		charName = "Leblanc",
		radius = 260,
		range = 600,
		type = "circular",
		delay = 0.25,
		cc = false,
		aoe = true,
		slot = 1,
		hitbox = true,
		collision = false,
		speedss = 1600
	},
	LeblancE = {
		charName = "Leblanc",
		radius = 55,
		range = 925,
		type = "linear",
		delay = 0.25,
		cc = true,
		aoe = false,
		slot = 2,
		hitbox = true,
		collision = true,
		speedss = 1750
	},
	LeblancRW = {
		charName = "Leblanc",
		radius = 260,
		range = 600,
		type = "circular",
		delay = 0.25,
		cc = false,
		aoe = true,
		slot = 1,
		hitbox = true,
		collision = false,
		speedss = 1600
	},
	LeblancRE = {
		charName = "Leblanc",
		radius = 55,
		range = 925,
		type = "linear",
		delay = 0.25,
		cc = true,
		aoe = false,
		slot = 2,
		hitbox = true,
		collision = true,
		speedss = 1750
	},
	BlinkMonkQOne = {
		charName = "LeeSin",
		radius = 50,
		range = 1100,
		type = "linear",
		delay = 0.25,
		cc = false,
		aoe = false,
		slot = 0,
		hitbox = true,
		collision = true,
		speedss = 1750
	},
	BlinkMonkEOne = {
		charName = "LeeSin",
		radius = 350,
		range = 0,
		type = "circular",
		delay = 0.25,
		cc = false,
		aoe = true,
		slot = 2,
		hitbox = false,
		collision = false,
		speedss = math.huge
	},
	LeonaZenithBladeMissile = {
		charName = "Leona",
		radius = 70,
		range = 875,
		type = "linear",
		delay = 0.25,
		cc = true,
		aoe = false,
		slot = 2,
		hitbox = true,
		collision = false,
		speedss = 2000
	},
	LeonaSolarFlare = {
		charName = "Leona",
		radius = 250,
		range = 1200,
		type = "circular",
		delay = 0.625,
		cc = true,
		aoe = true,
		slot = 3,
		hitbox = true,
		collision = false,
		speedss = math.huge
	},
	LissandraQ = {
		charName = "Lissandra",
		radius = 65,
		range = 825,
		type = "linear",
		delay = 0.251,
		cc = true,
		aoe = true,
		slot = 0,
		hitbox = true,
		collision = false,
		speedss = 2400
	},
	LissandraW = {
		charName = "Lissandra",
		radius = 450,
		range = 0,
		type = "circular",
		delay = 0.25,
		cc = true,
		aoe = true,
		slot = 1,
		hitbox = false,
		collision = false,
		speedss = math.huge
	},
	LissandraE = {
		charName = "Lissandra",
		radius = 100,
		range = 1050,
		type = "linear",
		delay = 0.25,
		cc = false,
		aoe = true,
		slot = 2,
		hitbox = true,
		collision = false,
		speedss = 850
	},
	LucianQ = {
		charName = "Lucian",
		radius = 65,
		range = 900,
		type = "linear",
		delay = 0.5,
		cc = false,
		aoe = true,
		slot = 0,
		hitbox = true,
		collision = false,
		speedss = math.huge
	},
	LucianW = {
		charName = "Lucian",
		radius = 65,
		range = 900,
		type = "linear",
		delay = 0.25,
		cc = false,
		aoe = true,
		slot = 1,
		hitbox = true,
		collision = false,
		speedss = 1600
	},
	LucianR = {
		charName = "Lucian",
		radius = 75,
		range = 1200,
		type = "linear",
		delay = 0.01,
		cc = false,
		aoe = false,
		slot = 3,
		hitbox = true,
		collision = true,
		speedss = 2800
	},
	PykeQMelee = {
		radius = 70,
		range = 400,
		charName = "Pyke",
		type = "linear",
		delay = 0.25,
		cc = true,
		aoe = true,
		slot = 0,
		hitbox = true,
		collision = false,
		speeds = math.huge
	},
	PykeQRange = {
		radius = 70,
		range = 1100,
		charName = "Pyke",
		type = "linear",
		delay = 0.2,
		cc = true,
		aoe = false,
		slot = 0,
		hitbox = true,
		speeds = 2000,
		collision = true
	},
	PykeR = {
		charName = "Pyke",
		range = 750,
		aoe = true,
		type = "cross",
		delay = 0.5,
		radius2 = 50,
		cc = false,
		radius1 = 300,
		slot = 3,
		hitbox = true,
		collision = false,
		speeds = math.huge
	},
	LuluQ = {
		charName = "Lulu",
		radius = 45,
		range = 925,
		type = "linear",
		delay = 0.25,
		cc = true,
		aoe = true,
		slot = 0,
		hitbox = true,
		collision = false,
		speedss = 1500
	},
	LuxLightBinding = {
		charName = "Lux",
		radius = 60,
		range = 1175,
		type = "linear",
		delay = 0.25,
		cc = true,
		aoe = true,
		slot = 0,
		hitbox = true,
		collision = true,
		speedss = 1200
	},
	LuxPrismaticWave = {
		charName = "Lux",
		radius = 120,
		range = 1075,
		type = "linear",
		delay = 0.25,
		cc = false,
		aoe = true,
		slot = 1,
		hitbox = true,
		collision = false,
		speedss = 1400
	},
	LuxLightStrikeKugel = {
		charName = "Lux",
		radius = 310,
		range = 1000,
		type = "circular",
		delay = 0.25,
		cc = true,
		aoe = true,
		slot = 2,
		hitbox = true,
		collision = false,
		speedss = 1300
	},
	LuxR = {
		charName = "Lux",
		radius = 115,
		range = 3340,
		type = "linear",
		delay = 1,
		cc = true,
		aoe = true,
		slot = 3,
		hitbox = true,
		collision = false,
		speedss = math.huge
	},
	Landslide = {
		charName = "Malphite",
		radius = 200,
		range = 0,
		type = "circular",
		delay = 0.242,
		cc = true,
		aoe = true,
		slot = 2,
		hitbox = false,
		collision = false,
		speedss = math.huge
	},
	UFSlash = {
		charName = "Malphite",
		radius = 300,
		range = 1000,
		type = "circular",
		delay = 0,
		cc = true,
		aoe = true,
		slot = 3,
		hitbox = true,
		collision = false,
		speedss = 2170
	},
	MalzaharQ = {
		delay = 0.25,
		range = 900,
		aoe = true,
		type = "rectangle",
		charName = "Malzahar",
		radius2 = 100,
		cc = true,
		radius1 = 400,
		slot = 0,
		hitbox = true,
		collision = false,
		speedss = math.huge
	},
	MaokaiQ = {
		charName = "Maokai",
		radius = 150,
		range = 600,
		type = "linear",
		delay = 0.375,
		cc = true,
		aoe = true,
		slot = 0,
		hitbox = true,
		collision = false,
		speedss = 1600
	},
	MaokaiR = {
		charName = "Maokai",
		radius = 650,
		range = 3000,
		type = "linear",
		delay = 0.5,
		cc = true,
		aoe = true,
		slot = 3,
		hitbox = true,
		collision = false,
		speedss = 450
	},
	MissFortuneScattershot = {
		charName = "MissFortune",
		radius = 400,
		range = 1000,
		type = "circular",
		delay = 0.5,
		cc = true,
		aoe = true,
		slot = 2,
		hitbox = true,
		collision = false,
		speedss = math.huge
	},
	MissFortuneBulletTime = {
		charName = "MissFortune",
		range = 1400,
		collision = false,
		type = "conic",
		delay = 0.001,
		cc = false,
		aoe = true,
		slot = 3,
		hitbox = false,
		angle = 40,
		speedss = math.huge
	},
	MordekaiserSiphonOfDestruction = {
		charName = "Mordekaiser",
		range = 675,
		collision = false,
		type = "conic",
		delay = 0.25,
		cc = false,
		aoe = true,
		slot = 2,
		hitbox = false,
		angle = 50,
		speedss = math.huge
	},
	MorganaQ = {
		charName = "Morgana",
		radius = 60,
		range = 1175,
		type = "linear",
		delay = 0.25,
		cc = true,
		aoe = false,
		slot = 0,
		hitbox = true,
		collision = true,
		speedss = 1200
	},
	TormentedSoil = {
		charName = "Morgana",
		radius = 325,
		range = 900,
		type = "circular",
		delay = 0.25,
		cc = false,
		aoe = true,
		slot = 1,
		hitbox = true,
		collision = false,
		speedss = math.huge
	},
	NamiQ = {
		charName = "Nami",
		radius = 200,
		range = 875,
		type = "circular",
		delay = 0.95,
		cc = true,
		aoe = true,
		slot = 0,
		hitbox = true,
		collision = false,
		speedss = math.huge
	},
	NamiR = {
		charName = "Nami",
		radius = 215,
		range = 2750,
		type = "linear",
		delay = 0.5,
		cc = true,
		aoe = true,
		slot = 3,
		hitbox = true,
		collision = false,
		speedss = 850
	},
	NasusE = {
		charName = "Nasus",
		radius = 400,
		range = 650,
		type = "circular",
		delay = 0.25,
		cc = false,
		aoe = true,
		slot = 2,
		hitbox = true,
		collision = false,
		speedss = math.huge
	},
	NautilusAnchorDrag = {
		charName = "Nautilus",
		radius = 75,
		range = 1100,
		type = "linear",
		delay = 0.25,
		cc = true,
		aoe = false,
		slot = 0,
		hitbox = true,
		collision = true,
		speedss = 2000
	},
	NautilusSplashZone = {
		charName = "Nautilus",
		radius = 600,
		range = 0,
		type = "circular",
		delay = 0.25,
		cc = true,
		aoe = true,
		slot = 2,
		hitbox = false,
		collision = false,
		speedss = math.huge
	},
	JavelinToss = {
		charName = "Nidalee",
		radius = 45,
		range = 1500,
		type = "linear",
		delay = 0.25,
		cc = false,
		aoe = true,
		slot = 0,
		hitbox = true,
		collision = true,
		speedss = 1300
	},
	Bushwhack = {
		charName = "Nidalee",
		radius = 85,
		range = 900,
		type = "circular",
		delay = 0.25,
		cc = false,
		aoe = false,
		slot = 1,
		hitbox = true,
		collision = true,
		speedss = math.huge
	},
	Pounce = {
		charName = "Nidalee",
		radius = 200,
		range = 750,
		type = "circular",
		delay = 0.25,
		cc = false,
		aoe = true,
		slot = 1,
		hitbox = true,
		collision = false,
		speedss = 1750
	},
	Swipe = {
		charName = "Nidalee",
		range = 300,
		collision = false,
		type = "conic",
		delay = 0.25,
		cc = false,
		aoe = true,
		slot = 2,
		hitbox = false,
		angle = 180,
		speedss = math.huge
	},
	NocturneDuskbringer = {
		charName = "Nocturne",
		radius = 60,
		range = 1200,
		type = "linear",
		delay = 0.25,
		cc = false,
		aoe = true,
		slot = 0,
		hitbox = true,
		collision = false,
		speedss = 1400
	},
	AbsoluteZero = {
		charName = "Nunu",
		radius = 650,
		range = 0,
		type = "circular",
		delay = 3.01,
		cc = true,
		aoe = true,
		slot = 3,
		hitbox = false,
		collision = false,
		speedss = math.huge
	},
	OlafAxeThrowCast = {
		charName = "Olaf",
		radius = 80,
		range = 1000,
		type = "linear",
		delay = 0.25,
		cc = true,
		aoe = true,
		slot = 0,
		hitbox = true,
		collision = false,
		speedss = 1550
	},
	OrianaIzunaCommand = {
		charName = "Orianna",
		radius = 175,
		range = 825,
		type = "linear",
		delay = 0.25,
		cc = false,
		aoe = true,
		slot = 0,
		hitbox = true,
		collision = false,
		speedss = 1400
	},
	OrianaDissonanceCommand = {
		charName = "Orianna",
		radius = 250,
		range = 0,
		type = "circular",
		delay = 0.25,
		cc = true,
		aoe = true,
		slot = 1,
		hitbox = false,
		collision = false,
		speedss = math.huge
	},
	OrianaRedactCommand = {
		charName = "Orianna",
		radius = 55,
		range = 1100,
		type = "linear",
		delay = 0.25,
		cc = false,
		aoe = true,
		slot = 2,
		hitbox = true,
		collision = false,
		speedss = 1400
	},
	OrianaDetonateCommand = {
		charName = "Orianna",
		radius = 325,
		range = 0,
		type = "circular",
		delay = 0.5,
		cc = true,
		aoe = true,
		slot = 3,
		hitbox = false,
		collision = false,
		speedss = math.huge
	},
	OrnnQ = {
		charName = "Ornn",
		radius = 100,
		range = 800,
		type = "linear",
		delay = 0.3,
		cc = true,
		aoe = true,
		slot = 0,
		hitbox = true,
		collision = false,
		speedss = 2000
	},
	OrnnW = {
		charName = "Ornn",
		radius = 110,
		range = 550,
		type = "linear",
		delay = 0.25,
		cc = false,
		aoe = true,
		slot = 1,
		hitbox = true,
		collision = false,
		speedss = math.huge
	},
	OrnnE = {
		charName = "Ornn",
		radius = 150,
		range = 800,
		type = "linear",
		delay = 0.35,
		cc = true,
		aoe = true,
		slot = 2,
		hitbox = true,
		collision = false,
		speedss = 1780
	},
	OrnnR = {
		charName = "Ornn",
		radius = 225,
		range = 2500,
		type = "linear",
		delay = 0.5,
		cc = true,
		aoe = true,
		slot = 3,
		hitbox = true,
		collision = false,
		speedss = 1200
	},
	OrnnRCharge = {
		charName = "Ornn",
		radius = 225,
		range = 3500,
		type = "linear",
		delay = 0.25,
		cc = true,
		aoe = true,
		slot = 3,
		hitbox = true,
		collision = false,
		speedss = 1650
	},
	PantheonE = {
		charName = "Pantheon",
		range = 0,
		collision = false,
		type = "conic",
		delay = 0.389,
		cc = false,
		aoe = true,
		slot = 2,
		hitbox = false,
		angle = 80,
		speedss = math.huge
	},
	PantheonRFall = {
		charName = "Pantheon",
		range = 5500,
		collision = false,
		type = "circular",
		delay = 2.5,
		cc = true,
		aoe = true,
		slot = 3,
		hitbox = true,
		angle = 700,
		speedss = math.huge
	},
	PoppyQSpell = {
		charName = "Poppy",
		radius = 85,
		range = 430,
		type = "linear",
		delay = 1.32,
		cc = true,
		aoe = true,
		slot = 0,
		hitbox = true,
		collision = false,
		speedss = math.huge
	},
	PoppyW = {
		charName = "Poppy",
		radius = 400,
		range = 0,
		type = "circular",
		delay = 0,
		cc = false,
		aoe = true,
		slot = 1,
		hitbox = false,
		collision = false,
		speedss = math.huge
	},
	PoppyRSpell = {
		charName = "Poppy",
		radius = 80,
		range = 1900,
		type = "linear",
		delay = 0.6,
		cc = true,
		aoe = true,
		slot = 3,
		hitbox = true,
		collision = false,
		speedss = 1600
	},
	QuinnQ = {
		charName = "Quinn",
		radius = 50,
		range = 1025,
		type = "linear",
		delay = 0.25,
		cc = false,
		aoe = false,
		slot = 0,
		hitbox = true,
		collision = true,
		speedss = 1550
	},
	RakanQ = {
		charName = "Rakan",
		radius = 60,
		range = 900,
		type = "linear",
		delay = 0.25,
		cc = false,
		aoe = false,
		slot = 0,
		hitbox = true,
		collision = true,
		speedss = 1800
	},
	RakanW = {
		charName = "Rakan",
		radius = 250,
		range = 600,
		type = "circular",
		delay = 0,
		cc = false,
		aoe = true,
		slot = 1,
		hitbox = true,
		collision = false,
		speedss = 2150
	},
	RakanWCast = {
		charName = "Rakan",
		radius = 250,
		range = 0,
		type = "circular",
		delay = 0.5,
		cc = true,
		aoe = true,
		slot = 1,
		hitbox = false,
		collision = false,
		speedss = math.huge
	},
	Tremors2 = {
		charName = "Rammus",
		radius = 300,
		range = 0,
		type = "circular",
		delay = 0.25,
		cc = true,
		aoe = true,
		slot = 3,
		hitbox = false,
		collision = false,
		speedss = math.huge
	},
	RekSaiQBurrowed = {
		charName = "RekSai",
		radius = 50,
		range = 1650,
		type = "linear",
		delay = 0.125,
		cc = false,
		aoe = false,
		slot = 0,
		hitbox = true,
		collision = true,
		speedss = 2100
	},
	RenektonCleave = {
		charName = "Renekton",
		radius = 325,
		range = 0,
		type = "circular",
		delay = 0.25,
		cc = false,
		aoe = true,
		slot = 0,
		hitbox = false,
		collision = false,
		speedss = math.huge
	},
	RenektonSliceAndDice = {
		charName = "Renekton",
		radius = 45,
		range = 450,
		type = "linear",
		delay = 0.25,
		cc = false,
		aoe = true,
		slot = 2,
		hitbox = true,
		collision = false,
		speedss = 1125
	},
	RengarQ = {
		charName = "Rengar",
		radius = 50,
		range = 450,
		type = "linear",
		delay = 0.325,
		cc = false,
		aoe = true,
		slot = 0,
		hitbox = true,
		collision = false,
		speedss = math.huge
	},
	RengarW = {
		charName = "Rengar",
		radius = 450,
		range = 0,
		type = "circular",
		delay = 0.25,
		cc = false,
		aoe = true,
		slot = 1,
		hitbox = false,
		collision = false,
		speedss = math.huge
	},
	RengarE = {
		charName = "Rengar",
		radius = 60,
		range = 1000,
		type = "linear",
		delay = 0.25,
		cc = true,
		aoe = false,
		slot = 2,
		hitbox = true,
		collision = true,
		speedss = 1500
	},
	RivenTriCleave = {
		charName = "Riven",
		radius = 200,
		range = 260,
		type = "linear",
		delay = 0.25,
		cc = true,
		aoe = true,
		slot = 0,
		hitbox = true,
		collision = false,
		speedss = 1100
	},
	RivenMartyr = {
		charName = "Riven",
		radius = 135,
		range = 0,
		type = "circular",
		delay = 0.267,
		cc = true,
		aoe = true,
		slot = 1,
		hitbox = false,
		collision = false,
		speedss = 1500
	},
	RivenFeint = {
		charName = "Riven",
		radius = 100,
		range = 325,
		type = "linear",
		delay = 0,
		cc = false,
		aoe = false,
		slot = 2,
		hitbox = true,
		collision = false,
		speedss = 1100
	},
	RivenIzunaBlade = {
		charName = "Riven",
		range = 900,
		collision = false,
		type = "conic",
		delay = 0.25,
		cc = false,
		aoe = true,
		slot = 3,
		hitbox = true,
		angle = 50,
		speedss = 1600
	},
	RumbleGrenade = {
		charName = "Rumble",
		radius = 70,
		range = 850,
		type = "linear",
		delay = 0.25,
		cc = true,
		aoe = false,
		slot = 2,
		hitbox = true,
		collision = true,
		speedss = 2000
	},
	RumbleCarpetBombDummy = {
		charName = "Rumble",
		radius = 130,
		range = 1700,
		type = "linear",
		delay = 0.583,
		cc = true,
		aoe = true,
		slot = 3,
		hitbox = true,
		collision = false,
		speedss = 1600
	},
	RyzeQ = {
		charName = "Ryze",
		radius = 50,
		range = 1000,
		type = "linear",
		delay = 0.25,
		cc = false,
		aoe = false,
		slot = 0,
		hitbox = true,
		collision = true,
		speedss = 1700
	},
	SejuaniQ = {
		charName = "Sejuani",
		radius = 150,
		range = 650,
		type = "linear",
		delay = 0.25,
		cc = true,
		aoe = true,
		slot = 0,
		hitbox = true,
		collision = false,
		speedss = 1300
	},
	SejuaniW = {
		charName = "Sejuani",
		range = 600,
		collision = false,
		type = "conic",
		delay = 0.25,
		cc = true,
		aoe = true,
		slot = 1,
		hitbox = false,
		angle = 75,
		speedss = math.huge
	},
	SejuaniWDummy = {
		charName = "Sejuani",
		radius = 65,
		range = 600,
		type = "linear",
		delay = 1,
		cc = true,
		aoe = false,
		slot = 1,
		hitbox = true,
		collision = false,
		speedss = math.huge
	},
	SejuaniR = {
		charName = "Sejuani",
		radius = 100,
		range = 1300,
		type = "linear",
		delay = 0.25,
		cc = true,
		aoe = true,
		slot = 3,
		hitbox = true,
		collision = false,
		speedss = 1650
	},
	ShenE = {
		charName = "Shen",
		radius = 60,
		range = 600,
		type = "linear",
		delay = 0,
		cc = true,
		aoe = true,
		slot = 2,
		hitbox = true,
		collision = false,
		speedss = 1200
	},
	ShyvanaFireball = {
		charName = "Shyvana",
		radius = 60,
		range = 925,
		type = "linear",
		delay = 0.25,
		cc = false,
		aoe = true,
		slot = 2,
		hitbox = true,
		collision = false,
		speedss = 1575
	},
	ShyvanaTransformLeap = {
		charName = "Shyvana",
		radius = 160,
		range = 850,
		type = "linear",
		delay = 0.25,
		cc = true,
		aoe = true,
		slot = 3,
		hitbox = true,
		collision = false,
		speedss = 1130
	},
	ShyvanaFireballDragon2 = {
		charName = "Shyvana",
		radius = 60,
		range = 925,
		type = "linear",
		delay = 0.333,
		cc = false,
		aoe = true,
		slot = 2,
		hitbox = true,
		collision = false,
		speedss = 1575
	},
	MegaAdhesive = {
		charName = "Singed",
		radius = 265,
		range = 1000,
		type = "circular",
		delay = 0.25,
		cc = true,
		aoe = true,
		slot = 1,
		hitbox = true,
		collision = false,
		speedss = math.huge
	},
	SionQ = {
		charName = "Sion",
		radius = 300,
		range = 600,
		type = "linear",
		delay = 0,
		cc = true,
		aoe = true,
		slot = 0,
		hitbox = false,
		collision = false,
		speedss = math.huge
	},
	SionE = {
		charName = "Sion",
		radius = 80,
		range = 725,
		type = "linear",
		delay = 0.25,
		cc = true,
		aoe = true,
		slot = 2,
		hitbox = false,
		collision = false,
		speedss = 1900
	},
	SionR = {
		charName = "Sion",
		radius = 200,
		range = 7500,
		type = "linear",
		delay = 0.125,
		cc = true,
		aoe = true,
		slot = 3,
		hitbox = false,
		collision = false,
		speedss = 950
	},
	SivirQ = {
		charName = "Sivir",
		radius = 75,
		range = 1250,
		type = "linear",
		delay = 0.25,
		cc = false,
		aoe = true,
		slot = 0,
		hitbox = true,
		collision = false,
		speedss = 1350
	},
	SivirQReturn = {
		charName = "Sivir",
		radius = 75,
		range = 1250,
		type = "linear",
		delay = 0,
		cc = false,
		aoe = true,
		slot = 0,
		hitbox = true,
		collision = false,
		speedss = 1350
	},
	SkarnerVirulentSlash = {
		charName = "Skarner",
		radius = 350,
		range = 0,
		type = "circular",
		delay = 0.25,
		cc = false,
		aoe = true,
		slot = 0,
		hitbox = false,
		collision = false,
		speedss = math.huge
	},
	SkarnerFracture = {
		charName = "Skarner",
		radius = 70,
		range = 1000,
		type = "linear",
		delay = 0.25,
		cc = true,
		aoe = true,
		slot = 2,
		hitbox = true,
		collision = false,
		speedss = 1500
	},
	SonaR = {
		charName = "Sona",
		radius = 120,
		range = 900,
		type = "linear",
		delay = 0.25,
		cc = true,
		aoe = true,
		slot = 3,
		hitbox = true,
		collision = false,
		speedss = 2250
	},
	SorakaQ = {
		charName = "Soraka",
		radius = 235,
		range = 800,
		type = "circular",
		delay = 0.25,
		cc = true,
		aoe = true,
		slot = 0,
		hitbox = true,
		collision = false,
		speedss = 1150
	},
	SorakaE = {
		charName = "Soraka",
		radius = 300,
		range = 925,
		type = "circular",
		delay = 1.5,
		cc = true,
		aoe = true,
		slot = 2,
		hitbox = true,
		collision = false,
		speedss = math.huge
	},
	SwainQ = {
		charName = "Swain",
		range = 725,
		collision = false,
		type = "conic",
		delay = 0.25,
		cc = false,
		aoe = true,
		slot = 0,
		hitbox = false,
		angle = 45,
		speedss = math.huge
	},
	SwainW = {
		charName = "Swain",
		radius = 260,
		range = 3500,
		type = "circular",
		delay = 1.5,
		cc = false,
		aoe = true,
		slot = 1,
		hitbox = false,
		collision = false,
		speedss = math.huge
	},
	SwainE = {
		charName = "Swain",
		radius = 65,
		range = 850,
		type = "linear",
		delay = 0.25,
		cc = true,
		aoe = true,
		slot = 2,
		hitbox = true,
		collision = false,
		speedss = 1550
	},
	SwainR = {
		charName = "Swain",
		radius = 650,
		range = 0,
		type = "circular",
		delay = 0.5,
		cc = true,
		aoe = true,
		slot = 3,
		hitbox = false,
		collision = false,
		speedss = math.huge
	},
	SyndraQ = {
		charName = "Syndra",
		radius = 200,
		range = 800,
		type = "circular",
		delay = 0.625,
		cc = false,
		aoe = true,
		slot = 0,
		hitbox = true,
		collision = false,
		speedss = math.huge
	},
	SyndraWCast = {
		charName = "Syndra",
		radius = 225,
		range = 950,
		type = "circular",
		delay = 0.25,
		cc = true,
		aoe = true,
		slot = 1,
		hitbox = true,
		collision = false,
		speedss = 1450
	},
	SyndraE = {
		charName = "Syndra",
		range = 700,
		collision = false,
		type = "conic",
		delay = 0.25,
		cc = true,
		aoe = true,
		slot = 2,
		hitbox = false,
		angle = 40,
		speedss = 2500
	},
	SyndraEMissile = {
		charName = "Syndra",
		radius = 50,
		range = 1250,
		type = "linear",
		delay = 0.25,
		cc = true,
		aoe = true,
		slot = 2,
		hitbox = true,
		collision = false,
		speedss = 1600
	},
	TahmKenchQ = {
		charName = "TahmKench",
		radius = 70,
		range = 800,
		type = "linear",
		delay = 0.25,
		cc = true,
		aoe = false,
		slot = 0,
		hitbox = true,
		collision = true,
		speedss = 2670
	},
	TaliyahQ = {
		charName = "Taliyah",
		radius = 70,
		range = 1000,
		type = "linear",
		delay = 0.25,
		cc = false,
		aoe = false,
		slot = 0,
		hitbox = true,
		collision = true,
		speedss = 2850
	},
	TaliyahWVC = {
		charName = "Taliyah",
		radius = 150,
		range = 900,
		type = "circular",
		delay = 0.6,
		cc = true,
		aoe = true,
		slot = 1,
		hitbox = true,
		collision = false,
		speedss = math.huge
	},
	TaliyahE = {
		charName = "Taliyah",
		range = 800,
		collision = false,
		type = "conic",
		delay = 0.25,
		cc = true,
		aoe = true,
		slot = 2,
		hitbox = true,
		angle = 80,
		speedss = 2000
	},
	TalonW = {
		charName = "Talon",
		range = 650,
		collision = false,
		type = "conic",
		delay = 0.25,
		cc = true,
		aoe = true,
		slot = 1,
		hitbox = true,
		angle = 35,
		speedss = 1850
	},
	TalonR = {
		charName = "Talon",
		radius = 550,
		range = 0,
		type = "circular",
		delay = 0.25,
		cc = false,
		aoe = true,
		slot = 3,
		hitbox = false,
		collision = false,
		speedss = math.huge
	},
	TaricE = {
		charName = "Taric",
		radius = 70,
		range = 575,
		type = "linear",
		delay = 1,
		cc = true,
		aoe = true,
		slot = 2,
		hitbox = true,
		collision = false,
		speedss = math.huge
	},
	TeemoRCast = {
		charName = "Teemo",
		radius = 200,
		range = 900,
		type = "circular",
		delay = 1.25,
		cc = true,
		aoe = true,
		slot = 3,
		hitbox = true,
		collision = false,
		speedss = math.huge
	},
	ThreshQ = {
		charName = "Thresh",
		radius = 55,
		range = 1100,
		type = "linear",
		delay = 0.5,
		cc = true,
		aoe = false,
		slot = 0,
		hitbox = true,
		collision = true,
		speedss = 1900
	},
	ThreshE = {
		charName = "Thresh",
		radius = 95,
		range = 400,
		type = "linear",
		delay = 0.389,
		cc = true,
		aoe = true,
		slot = 2,
		hitbox = false,
		collision = false,
		speedss = math.huge
	},
	ThreshRPenta = {
		charName = "Thresh",
		radius = 450,
		range = 0,
		type = "pentagon",
		delay = 0.45,
		cc = true,
		aoe = true,
		slot = 3,
		hitbox = false,
		collision = false,
		speedss = math.huge
	},
	TristanaW = {
		charName = "Tristana",
		radius = 250,
		range = 900,
		type = "circular",
		delay = 0.25,
		cc = true,
		aoe = true,
		slot = 1,
		hitbox = true,
		collision = false,
		speedss = 1100
	},
	trundledesecrate = {
		charName = "Trundle",
		radius = 1000,
		range = 0,
		type = "circular",
		delay = 0.25,
		cc = false,
		aoe = false,
		slot = 1,
		hitbox = false,
		collision = false,
		speedss = math.huge
	},
	TrundleCircle = {
		charName = "Trundle",
		radius = 375,
		range = 1000,
		type = "circular",
		delay = 0.25,
		cc = true,
		aoe = true,
		slot = 2,
		hitbox = true,
		collision = false,
		speedss = math.huge
	},
	TryndamereE = {
		charName = "Tryndamere",
		radius = 225,
		range = 660,
		type = "linear",
		delay = 0,
		cc = false,
		aoe = true,
		slot = 2,
		hitbox = true,
		collision = false,
		speedss = 1300
	},
	WildCards = {
		charName = "TwistedFate",
		radius = 35,
		range = 1450,
		type = "linear",
		delay = 0.25,
		cc = false,
		aoe = true,
		slot = 0,
		hitbox = true,
		collision = false,
		speedss = 1000
	},
	TwitchVenomCask = {
		charName = "Twitch",
		radius = 340,
		range = 950,
		type = "circular",
		delay = 0.25,
		cc = true,
		aoe = true,
		slot = 1,
		hitbox = true,
		collision = false,
		speedss = 1400
	},
	UrgotQ = {
		charName = "Urgot",
		radius = 215,
		range = 800,
		type = "circular",
		delay = 0.6,
		cc = true,
		aoe = true,
		slot = 0,
		hitbox = true,
		collision = false,
		speedss = math.huge
	},
	UrgotE = {
		charName = "Urgot",
		radius = 100,
		range = 475,
		type = "linear",
		delay = 0.45,
		cc = true,
		aoe = true,
		slot = 2,
		hitbox = true,
		collision = false,
		speedss = 1050
	},
	UrgotR = {
		charName = "Urgot",
		radius = 70,
		range = 1600,
		type = "linear",
		delay = 0.4,
		cc = true,
		aoe = false,
		slot = 3,
		hitbox = true,
		collision = false,
		speedss = 3200
	},
	VarusQ = {
		charName = "Varus",
		radius = 40,
		range = 1625,
		type = "linear",
		delay = 0,
		cc = false,
		aoe = true,
		slot = 0,
		hitbox = true,
		collision = false,
		speedss = 1850
	},
	VarusE = {
		charName = "Varus",
		radius = 280,
		range = 925,
		type = "circular",
		delay = 0.242,
		cc = true,
		aoe = true,
		slot = 2,
		hitbox = true,
		collision = false,
		speedss = 1500
	},
	VarusR = {
		charName = "Varus",
		radius = 120,
		range = 1075,
		type = "linear",
		delay = 0.242,
		cc = true,
		aoe = true,
		slot = 3,
		hitbox = true,
		collision = false,
		speedss = 1850
	},
	VayneTumble = {
		charName = "Vayne",
		radius = 45,
		range = 300,
		type = "linear",
		delay = 0.25,
		cc = false,
		aoe = false,
		slot = 0,
		hitbox = true,
		collision = false,
		speedss = 900
	},
	VeigarBalefulStrike = {
		charName = "Veigar",
		radius = 60,
		range = 950,
		type = "linear",
		delay = 0.25,
		cc = false,
		aoe = true,
		slot = 0,
		hitbox = true,
		collision = true,
		speedss = 2000
	},
	VeigarDarkMatter = {
		charName = "Veigar",
		radius = 225,
		range = 900,
		type = "circular",
		delay = 1.25,
		cc = false,
		aoe = true,
		slot = 1,
		hitbox = true,
		collision = false,
		speedss = math.huge
	},
	VeigarEventHorizon = {
		charName = "Veigar",
		radius = 375,
		range = 700,
		type = "circular",
		delay = 0.75,
		cc = true,
		aoe = true,
		slot = 2,
		hitbox = true,
		collision = false,
		speedss = math.huge
	},
	VelkozQ = {
		charName = "Velkoz",
		radius = 55,
		range = 1050,
		type = "linear",
		delay = 0.251,
		cc = true,
		aoe = false,
		slot = 0,
		hitbox = true,
		collision = true,
		speedss = 1235
	},
	VelkozQMissileSplit = {
		charName = "VelKoz",
		radius = 45,
		range = 1050,
		type = "linear",
		delay = 0.251,
		cc = true,
		aoe = false,
		slot = 0,
		hitbox = true,
		collision = true,
		speedss = 2100
	},
	VelkozW = {
		charName = "Velkoz",
		radius = 80,
		range = 1050,
		type = "linear",
		delay = 0.25,
		cc = false,
		aoe = true,
		slot = 1,
		hitbox = true,
		collision = false,
		speedss = 1500
	},
	VelkozE = {
		charName = "Velkoz",
		radius = 235,
		range = 850,
		type = "circular",
		delay = 0.75,
		cc = true,
		aoe = true,
		slot = 2,
		hitbox = true,
		collision = false,
		speedss = math.huge
	},
	VelkozR = {
		charName = "Velkoz",
		radius = 75,
		range = 1550,
		type = "linear",
		delay = 0.25,
		cc = false,
		aoe = true,
		slot = 3,
		hitbox = true,
		collision = false,
		speedss = math.huge
	},
	ViQ = {
		charName = "Vi",
		radius = 55,
		range = 725,
		type = "linear",
		delay = 0,
		cc = true,
		aoe = true,
		slot = 0,
		hitbox = true,
		collision = false,
		speedss = 1400
	},
	ViktorGravitonField = {
		charName = "Viktor",
		radius = 290,
		range = 700,
		type = "circular",
		delay = 1.333,
		cc = true,
		aoe = true,
		slot = 1,
		hitbox = true,
		collision = false,
		speedss = math.huge
	},
	ViktorDeathRay = {
		charName = "Viktor",
		radius = 80,
		range = 1025,
		type = "linear",
		delay = 0,
		cc = false,
		aoe = true,
		slot = 2,
		hitbox = true,
		collision = false,
		speedss = 1350
	},
	ViktorChaosStorm = {
		charName = "Viktor",
		radius = 290,
		range = 700,
		type = "circular",
		delay = 0.25,
		cc = false,
		aoe = true,
		slot = 3,
		hitbox = true,
		collision = false,
		speedss = math.huge
	},
	VladimirSanguinePool = {
		charName = "Vladimir",
		radius = 300,
		range = 0,
		type = "circular",
		delay = 0.25,
		cc = true,
		aoe = true,
		slot = 1,
		hitbox = false,
		collision = false,
		speedss = math.huge
	},
	VladimirE = {
		charName = "Vladimir",
		radius = 600,
		range = 0,
		type = "circular",
		delay = 0,
		cc = true,
		aoe = true,
		slot = 2,
		hitbox = false,
		collision = true,
		speedss = math.huge
	},
	VladimirHemoplague = {
		charName = "Vladimir",
		radius = 350,
		range = 700,
		type = "circular",
		delay = 0.389,
		cc = false,
		aoe = true,
		slot = 3,
		hitbox = true,
		collision = true,
		speedss = math.huge
	},
	WarwickR = {
		charName = "Warwick",
		radius = 45,
		range = 1000,
		type = "linear",
		delay = 0.1,
		cc = false,
		aoe = true,
		slot = 3,
		hitbox = true,
		collision = false,
		speedss = 1800
	},
	MonkeyKingSpinToWin = {
		charName = "MonkeyKing",
		radius = 325,
		range = 0,
		type = "circular",
		delay = 0,
		cc = true,
		aoe = true,
		slot = 3,
		hitbox = false,
		collision = false,
		speedss = math.huge
	},
	XayahQ = {
		charName = "Xayah",
		radius = 45,
		range = 1100,
		type = "linear",
		delay = 0.5,
		cc = false,
		aoe = true,
		slot = 0,
		hitbox = true,
		collision = false,
		speedss = 2075
	},
	XayahE = {
		charName = "Xayah",
		radius = 45,
		range = 2000,
		type = "linear",
		delay = 0,
		cc = false,
		aoe = true,
		slot = 2,
		hitbox = true,
		collision = false,
		speedss = 5700
	},
	XayahR = {
		charName = "Xayah",
		range = 1100,
		collision = false,
		type = "conic",
		delay = 1.5,
		cc = false,
		aoe = true,
		slot = 3,
		hitbox = false,
		angle = 40,
		speedss = 4400
	},
	XerathArcanopulse2 = {
		charName = "Xerath",
		radius = 75,
		range = 1400,
		type = "linear",
		delay = 0.5,
		cc = false,
		aoe = true,
		slot = 0,
		hitbox = false,
		collision = false,
		speedss = math.huge
	},
	XerathArcaneBarrage2 = {
		charName = "Xerath",
		radius = 235,
		range = 1100,
		type = "circular",
		delay = 0.5,
		cc = true,
		aoe = true,
		slot = 1,
		hitbox = true,
		collision = false,
		speedss = math.huge
	},
	XerathMageSpear = {
		charName = "Xerath",
		radius = 60,
		range = 1050,
		type = "linear",
		delay = 0.25,
		cc = true,
		aoe = false,
		slot = 2,
		hitbox = true,
		collision = true,
		speedss = 1350
	},
	XerathRMissileWrapper = {
		charName = "Xerath",
		radius = 200,
		range = 6160,
		type = "circular",
		delay = 0.6,
		cc = false,
		aoe = true,
		slot = 3,
		hitbox = true,
		collision = false,
		speedss = math.huge
	},
	XinZhaoW = {
		charName = "XinZhao",
		range = 125,
		collision = false,
		type = "conic",
		delay = 0,
		cc = false,
		aoe = true,
		slot = 1,
		hitbox = false,
		angle = 180,
		speedss = math.huge
	},
	XinZhaoW = {
		charName = "XinZhao",
		radius = 45,
		range = 900,
		type = "linear",
		delay = 0.6,
		cc = true,
		aoe = true,
		slot = 1,
		hitbox = true,
		collision = false,
		speedss = math.huge
	},
	XinZhaoR = {
		charName = "XinZhao",
		radius = 550,
		range = 0,
		type = "circular",
		delay = 0.325,
		cc = true,
		aoe = true,
		slot = 3,
		hitbox = false,
		collision = false,
		speedss = math.huge
	},
	YasuoQW = {
		radius = 40,
		range = 475,
		charName = "Yasuo",
		type = "linear",
		delay = 0.339,
		cc = false,
		aoe = true,
		slot = 0,
		hitbox = true,
		collision = false,
		speeds = math.huge
	},
	YasuoQ2W = {
		radius = 40,
		range = 475,
		charName = "Yasuo",
		type = "linear",
		delay = 0.339,
		cc = false,
		aoe = true,
		slot = 0,
		hitbox = true,
		collision = false,
		speeds = math.huge
	},
	YasuoQ3W = {
		radius = 90,
		range = 1000,
		charName = "Yasuo",
		type = "linear",
		delay = 0.339,
		cc = true,
		aoe = true,
		slot = 0,
		hitbox = true,
		speeds = 1200,
		collision = false
	},
	YorickW = {
		charName = "Yorick",
		radius = 300,
		range = 600,
		type = "circular",
		delay = 0.25,
		cc = true,
		aoe = true,
		slot = 1,
		hitbox = true,
		collision = false,
		speedss = math.huge
	},
	YorickE = {
		charName = "Yorick",
		range = 700,
		collision = false,
		type = "conic",
		delay = 0.33,
		cc = true,
		aoe = true,
		slot = 2,
		hitbox = true,
		angle = 25,
		speedss = 2100
	},
	ZacQ = {
		charName = "Zac",
		radius = 85,
		range = 800,
		type = "linear",
		delay = 0.33,
		cc = true,
		aoe = true,
		slot = 0,
		hitbox = true,
		collision = true,
		speedss = math.huge
	},
	ZacW = {
		charName = "Zac",
		radius = 350,
		range = 0,
		type = "circular",
		delay = 0.25,
		cc = false,
		aoe = true,
		slot = 1,
		hitbox = false,
		collision = false,
		speedss = math.huge
	},
	ZacE = {
		charName = "Zac",
		radius = 300,
		range = 1800,
		type = "circular",
		delay = 0,
		cc = true,
		aoe = true,
		slot = 2,
		hitbox = false,
		collision = false,
		speedss = 1330
	},
	ZacR = {
		charName = "Zac",
		radius = 300,
		range = 1000,
		type = "circular",
		delay = 0,
		cc = true,
		aoe = true,
		slot = 3,
		hitbox = false,
		collision = false,
		speedss = math.huge
	},
	ZedQ = {
		charName = "Zed",
		radius = 50,
		range = 900,
		type = "linear",
		delay = 0.25,
		cc = false,
		aoe = true,
		slot = 0,
		hitbox = true,
		collision = false,
		speedss = 1700
	},
	ZedW = {
		charName = "Zed",
		radius = 40,
		range = 650,
		type = "linear",
		delay = 0.25,
		cc = false,
		aoe = true,
		slot = 1,
		hitbox = true,
		collision = false,
		speedss = 1750
	},
	ZedE = {
		charName = "Zed",
		radius = 290,
		range = 0,
		type = "circular",
		delay = 0.25,
		cc = true,
		aoe = true,
		slot = 2,
		hitbox = false,
		collision = false,
		speedss = math.huge
	},
	ZiggsQ = {
		charName = "Ziggs",
		radius = 180,
		range = 1400,
		type = "circular",
		delay = 0.25,
		cc = false,
		aoe = true,
		slot = 0,
		hitbox = true,
		collision = false,
		speedss = math.huge
	},
	ZiggsW = {
		charName = "Ziggs",
		radius = 325,
		range = 1000,
		type = "circular",
		delay = 0.25,
		cc = true,
		aoe = true,
		slot = 1,
		hitbox = true,
		collision = false,
		speedss = 2000
	},
	ZiggsE = {
		charName = "Ziggs",
		radius = 325,
		range = 900,
		type = "circular",
		delay = 0.25,
		cc = true,
		aoe = true,
		slot = 2,
		hitbox = true,
		collision = false,
		speedss = math.huge
	},
	ZiggsR = {
		charName = "Ziggs",
		radius = 550,
		range = 5300,
		type = "circular",
		delay = 0.375,
		cc = false,
		aoe = true,
		slot = 3,
		hitbox = true,
		collision = false,
		speedss = math.huge
	},
	ZileanQ = {
		charName = "Zilean",
		radius = 180,
		range = 900,
		type = "circular",
		delay = 0.25,
		cc = true,
		aoe = true,
		slot = 0,
		hitbox = true,
		collision = false,
		speedss = 2050
	},
	ZileanQAttachAudio = {
		charName = "Zilean",
		radius = 180,
		range = 900,
		type = "circular",
		delay = 0.25,
		cc = true,
		aoe = true,
		slot = 0,
		hitbox = true,
		collision = false,
		speedss = 2050
	},
	ZoeQMissile = {
		charName = "Zoe",
		radius = 40,
		range = 800,
		type = "linear",
		delay = 0.25,
		cc = false,
		aoe = false,
		slot = 0,
		hitbox = true,
		collision = true,
		speedss = 2500
	},
	ZoeQMis2 = {
		charName = "Zoe",
		radius = 40,
		range = 1600,
		type = "linear",
		delay = 0,
		cc = false,
		aoe = false,
		slot = 0,
		hitbox = true,
		collision = true,
		speedss = 2370
	},
	ZoeE = {
		charName = "Zoe",
		radius = 55,
		range = 800,
		type = "linear",
		delay = 0.3,
		cc = false,
		aoe = false,
		slot = 2,
		hitbox = true,
		collision = true,
		speedss = 1950
	},
	ZoeR = {
		charName = "Zoe",
		radius = 100,
		range = 575,
		type = "circular",
		delay = 0.25,
		cc = false,
		aoe = false,
		slot = 3,
		hitbox = true,
		collision = false,
		speedss = math.huge
	},
	ZyraQ = {
		delay = 0.625,
		range = 800,
		aoe = true,
		type = "rectangle",
		charName = "Zyra",
		radius2 = 100,
		cc = false,
		radius1 = 400,
		slot = 0,
		hitbox = true,
		collision = false,
		speedss = math.huge
	},
	ZyraW = {
		charName = "Zyra",
		radius = 50,
		range = 850,
		type = "circular",
		delay = 0.243,
		cc = false,
		aoe = false,
		slot = 1,
		hitbox = true,
		collision = false,
		speedss = math.huge
	},
	ZyraE = {
		charName = "Zyra",
		radius = 60,
		range = 1100,
		type = "linear",
		delay = 0.25,
		cc = true,
		aoe = true,
		slot = 2,
		hitbox = true,
		collision = false,
		speedss = 1150
	},
	ZyraR = {
		charName = "Zyra",
		radius = 575,
		range = 700,
		type = "circular",
		delay = 1.775,
		cc = true,
		aoe = true,
		slot = 3,
		hitbox = true,
		collision = false,
		speedss = math.huge
	},
	YoneQ3 = {
		charName = "Yone",
		radius = 80,
		range = 1100,
		type = "linear",
		delay = 0.5,
		cc = true,
		aoe = true,
		slot = 0,
		hitbox = true,
		collision = false,
		speedss = 1500
	},
	YoneQ = {
		charName = "Yone",
		radius = 80,
		range = 1100,
		type = "linear",
		delay = 0.25,
		cc = true,
		aoe = true,
		slot = 0,
		hitbox = true,
		collision = false,
		speedss = 5000
	},
	YoneR = {
		charName = "Yone",
		radius = 80,
		range = 1100,
		type = "linear",
		delay = 0.8,
		cc = true,
		aoe = true,
		slot = 3,
		hitbox = true,
		collision = false,
		speedss = 1333
	},
	SettW = {
		charName = "Sett",
		radius = 100,
		range = 725,
		type = "linear",
		delay = 0.75,
		cc = true,
		aoe = true,
		slot = 1,
		hitbox = true,
		collision = false,
		speedss = math.huge
	},
	SettE = {
		charName = "Sett",
		radius = 280,
		range = 500,
		type = "linear",
		delay = 0.25,
		cc = true,
		aoe = true,
		slot = 2,
		hitbox = true,
		collision = false,
		speedss = math.huge
	},
	SettR = {
		charName = "Sett",
		radius = 280,
		range = 500,
		type = "circular",
		delay = 0.25,
		cc = true,
		aoe = true,
		slot = 3,
		hitbox = true,
		collision = false,
		speedss = math.huge
	},
	RenataQ = {
		charName = "Renata",
		radius = 140,
		range = 900,
		type = "linear",
		delay = 0.25,
		cc = true,
		aoe = true,
		slot = 0,
		hitbox = true,
		collision = false,
		speedss = 1450
	},
	RenataE = {
		charName = "Renata",
		radius = 110,
		range = 800,
		type = "linear",
		delay = 0.25,
		cc = true,
		aoe = true,
		slot = 2,
		hitbox = true,
		collision = false,
		speedss = 1450
	},
	RenataR = {
		charName = "Renata",
		radius = 250,
		range = 2000,
		type = "linear",
		delay = 0.25,
		cc = true,
		aoe = true,
		slot = 3,
		hitbox = true,
		collision = false,
		speedss = math.huge
	},
	QiyanaQ = {
		charName = "Qiyana",
		radius = 70,
		range = 500,
		type = "linear",
		delay = 0.25,
		cc = true,
		aoe = true,
		slot = 0,
		hitbox = true,
		collision = false,
		speedss = math.huge
	},
	QiyanaQ_Rock = {
		charName = "Qiyana",
		radius = 70,
		range = 865,
		type = "linear",
		delay = 0.25,
		cc = true,
		aoe = true,
		slot = 0,
		hitbox = true,
		collision = true,
		speedss = 1600
	},
	QiyanaQ_Water = {
		charName = "Qiyana",
		radius = 70,
		range = 865,
		type = "linear",
		delay = 0.25,
		cc = true,
		aoe = true,
		slot = 0,
		hitbox = true,
		collision = true,
		speedss = 1600
	},
	QiyanaQ_Grass = {
		charName = "Qiyana",
		radius = 70,
		range = 865,
		type = "linear",
		delay = 0.25,
		cc = true,
		aoe = true,
		slot = 0,
		hitbox = true,
		collision = true,
		speedss = 1600
	},
	QiyanaR = {
		charName = "Qiyana",
		radius = 120,
		range = 1050,
		type = "linear",
		delay = 0.25,
		cc = true,
		aoe = true,
		slot = 3,
		hitbox = true,
		collision = false,
		speedss = 2000
	},
	SamiraQ = {
		charName = "Samira",
		radius = 60,
		range = 1050,
		type = "linear",
		delay = 0.26,
		cc = true,
		aoe = true,
		slot = 0,
		hitbox = true,
		collision = true,
		speedss = 2600
	},
	SamiraR = {
		charName = "Samira",
		radius = 60,
		range = 555,
		type = "linear",
		delay = 0.25,
		cc = true,
		aoe = true,
		slot = 3,
		hitbox = true,
		collision = true,
		speedss = 2800
	},
	KSanteQ = {
		charName = "KSante",
		radius = 100,
		range = 555,
		type = "linear",
		delay = 0.44,
		cc = true,
		aoe = true,
		slot = 0,
		hitbox = true,
		collision = true,
		speedss = math.huge
	},
	KSanteQ3 = {
		charName = "KSante",
		radius = 60,
		range = 900,
		type = "linear",
		delay = 0.25,
		cc = true,
		aoe = true,
		slot = 0,
		hitbox = true,
		collision = true,
		speedss = 1600
	},
	KSanteW = {
		charName = "KSante",
		radius = 55,
		range = 900,
		type = "linear",
		delay = 0.64,
		cc = true,
		aoe = true,
		slot = 1,
		hitbox = true,
		collision = true,
		speedss = 1500
	},
	SylasE2 = {
		charName = "Sylas",
		radius = 60,
		range = 1050,
		type = "linear",
		delay = 0.26,
		cc = true,
		aoe = true,
		slot = 2,
		hitbox = true,
		collision = true,
		speedss = 2600
	}
}

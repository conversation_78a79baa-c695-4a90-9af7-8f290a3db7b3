local spell = {}
local common = module.load(header.id, "Libdata/Utility")

--// 1 魔法  2 物理
spell.get_damage = function(dmg,target,source,mode)
	if mode then
		if mode == 1 then
			dmg = dmg - target.magicalShield
		elseif mode == 2 then
			dmg = dmg - target.physicalShield
		end
	end
	return dmg
end
-- <PERSON><PERSON>go Gangplank Aatrox LeeSin <PERSON> Sett Gnar <PERSON><PERSON><PERSON>i Teemo Ezreal Qiyana
--// 天赋
-- 返回点燃伤害
spell['Ignite'] = { --点燃
	damage = function(target,source)
		if not target then return 0 end
		local source = source or player
		local SummonerDot = nil
		if source:spellSlot(4).name == "SummonerDot" then
			SummonerDot = 4
		elseif source:spellSlot(5).name == "SummonerDot" then
			SummonerDot = 5
		end
		if SummonerDot and source:spellSlot(SummonerDot).state==0 then
			local levelRef = source.levelRef > 18 and 18 or source.levelRef
			local damage = 50 + (20 * levelRef)
			if target then
				damage = damage - target.physicalShield
			end
			return damage
		end
		return 0
	
	end
}

--// ITEM
spell['3152Active'] = { --推推棒
	damage = function(target,source)
		if not target then return 0 end
		local source = source or player
		spell_Dmg = 125
		totalDmg = spell_Dmg + (common.GetTotalAP(source) * .15);
		returndmg = common.CalculateMagicDamage(target,totalDmg,source) - common.jshfhp(target,1)
		return spell.get_damage(returndmg,target,source,1)
	end,
}
spell['6671Cast'] = { --狂风之力
	damage = function(target,source)
		if not target then return 0 end
		local source = source or player
		local levelRef = source.levelRef
		if levelRef > 18 then levelRef = 18 end
		local damage_tb = {65,70,75,80,85,90,95,100,105}
		local damage = 0
		if levelRef > 9 then
			local tb_index = levelRef - 9 
			damage = damage_tb[tb_index]
		else
			damage = 60
		end
		local totalDmg = damage + (common.GetBonusAD(source) * .15);
		local Ph = math.min(70,100 - common.GetPercentHealth(target))
		local ph_b = Ph * 0.007
		
		totalDmg = totalDmg + totalDmg * ph_b
		local dmg = common.CalculatePhysicalDamage(target,totalDmg,source)
		dmg = (dmg * 3)
		
		return spell.get_damage(dmg,target,source,1)
	end,
}
spell.Lissandra = {
	Q = {
		prediction = {
			type = 'Linear',
			range = 725,
			delay = 0.2509999871254,
			speed = 2200,
			width = 75,
			boundingRadiusMod = 1,
			collision = {
			  hero = true, --allow to hit other heros :-)
			  minion = true,
			  wall = true,
			},
			--
			hitchance = 0,
			out_range = 450,
			new_path = 0.500,
		},
		target_selector = 8,
		cast_spell = {
			type = 'pos',
			slot = _Q,
			arg1 = function(action)
			  local seg = action.ts.seg
			  local obj = action.ts.obj
			  return seg.endPos:to3D(obj.y)
			end,
		},
		slot = player:spellSlot(_Q),
		ignore_obj_radius = 1200,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			spell_tb = {80,110,140,170,200}
			spell_level = source:spellSlot(_Q).level
			spell_Dmg = spell_tb[spell_level] or 0;
			totalDmg = spell_Dmg + (common.GetTotalAP(source) * .8);
			returndmg = common.CalculateMagicDamage(target,totalDmg,source) - common.jshfhp(target,1)
			return spell.get_damage(returndmg,target,source,1)
		end,
	},
	W = {
		prediction = {
			type = 'InRange',
			range = 425,
			radius = 425,
			delay = 0.00,
			dashRadius = 0,
			boundingRadiusModSource = 0,
			boundingRadiusModTarget = 0,
		},
		target_selector = 8,
		cast_spell = {
			type = 'self',
			slot = _W,
		},
		slot = player:spellSlot(_W),
		ignore_obj_radius = 600,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			spell_tb = {70,105,140,175,210}
			spell_level = source:spellSlot(_W).level
			spell_Dmg = spell_tb[spell_level] or 0;
			totalDmg = spell_Dmg + (common.GetTotalAP(source) * .70);
			returndmg = common.CalculateMagicDamage(target,totalDmg,source) - common.jshfhp(target,1)
			return spell.get_damage(returndmg,target,source,1)
		end,
	},
	E = {
		prediction = {
			type = 'Linear',
			range = 1025,
			delay = 0.25,
			speed = 850,
			width = 125,
			boundingRadiusMod = 1,
			collision = {
			  hero = false, --allow to hit other heros :-)
			  minion = true,
			  wall = true,
			},
			--
			hitchance = 0,
		},
		target_selector = 8,
		cast_spell = {
			type = 'pos',
			slot = _E,
			arg1 = function(action)
			  local seg = action.ts.seg
			  local obj = action.ts.obj
			  return seg.endPos:to3D(obj.y)
			end,
		},
		slot = player:spellSlot(_E),
		ignore_obj_radius = 1200,
		damage = function(target,source)
			if not target then return 0 end
			spell_tb = {70,105,140,175,210}
			local source = source or player
			spell_level = source:spellSlot(_E).level
			spell_Dmg = spell_tb[spell_level] or 0;
			totalDmg = spell_Dmg + (common.GetTotalAP(source) * .60);
			returndmg = common.CalculateMagicDamage(target,totalDmg,source) - common.jshfhp(target,2)
			return spell.get_damage(returndmg,target,source,1)
		end,
	},
	R = {
		prediction = {
			type = 'InRange',
			range = 550,
			radius = 550,
			delay = 0.375,
			dashRadius = 0,
			boundingRadiusModSource = 0,
			boundingRadiusModTarget = 0,
		},
		target_selector = 8,
		cast_spell = {
			type = 'obj',
			slot = _R,
			arg1 = function(action)
			  return action.ts.obj
			end,
		},
		slot = player:spellSlot(_R),
		ignore_obj_radius = 650,
		damage = function(target,source)
			if not target then return 0 end
			spell_tb = {150,250,350}
			local source = source or player
			spell_level = source:spellSlot(_R).level
			spell_Dmg = spell_tb[spell_level] or 0;
			totalDmg = spell_Dmg + (common.GetTotalAP(source) * .75);
			returndmg = common.CalculateMagicDamage(target,totalDmg,source) - common.jshfhp(target,1)
			return spell.get_damage(returndmg,target,source,1)
		end,
	},
}


spell.Viego = {
	Q = {
		prediction = {
			type = 'Linear',
			range = 600,
			delay = 0.35,
			speed = 2200,
			width = 62.5,
			boundingRadiusMod = 1,
			collision = {
			  hero = false, --allow to hit other heros :-)
			  minion = false,
			  wall = true,
			},
			--
			hitchance = 0,
			out_range = 750,
			new_path = 0.500,
		},
		target_selector = 8,
		cast_spell = {
			type = 'pos',
			slot = _Q,
			arg1 = function(action)
			  local seg = action.ts.seg
			  local obj = action.ts.obj
			  return seg.endPos:to3D(obj.y)
			end,
		},
		slot = player:spellSlot(_Q),
		ignore_obj_radius = 800,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			spell_tb = {15,30,45,60,75}
			spell_level = source:spellSlot(_Q).level
			spell_Dmg = spell_tb[spell_level] or 0;
			totalDmg = spell_Dmg + (common.GetTotalAD(source) * .7);
			returndmg = common.CalculatePhysicalDamage(target,totalDmg,source) - common.jshfhp(target,1)
			return spell.get_damage(returndmg,target,source,2)
		end,
	},
	W = {
		prediction = {
			type = 'Linear',
			range = 900,
			delay = 0.00,
			speed = 1400,
			width = 60,
			boundingRadiusMod = 1,
			collision = {
			  hero = false, --allow to hit other heros :-)
			  minion = true,
			  wall = true,
			},
			--
			hitchance = 0,
			out_range = 1000,
			new_path = 0.500,
		},
		target_selector = 8,
		cast_spell = {
			type = 'pos',
			slot = _W,
			arg1 = function(action)
			  local seg = action.ts.seg
			  local obj = action.ts.obj
			  return seg.endPos:to3D(obj.y)
			end,
		},
		slot = player:spellSlot(_W),
		ignore_obj_radius = 1100,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			spell_tb = {80,135,190,245,300}
			spell_level = source:spellSlot(_W).level
			spell_Dmg = spell_tb[spell_level] or 0;
			totalDmg = spell_Dmg + (common.GetTotalAP(source) * 1);
			returndmg = common.CalculateMagicDamage(target,totalDmg,source) - common.jshfhp(target,1)
			return spell.get_damage(returndmg,target,source,1)
		end,
	},
	E = {
		prediction = {
			type = 'self',
			range = 400,
			delay = 0.00,
			hitchance = 0,
		},
		target_selector = 8,
		cast_spell = {
			type = 'pos',
			slot = _E,
			arg1 = function(action)
			  local seg = action.ts.seg
			  local obj = action.ts.obj
			  return seg.endPos:to3D(obj.y)
			end,
		},
		slot = player:spellSlot(_E),
		ignore_obj_radius = 1200,
		damage = function(target,source)
			return 0
		end,
	},
	R = {
		prediction = {
			type = 'Circular',
			range = 500,
			delay = 0.50,
			speed = math.huge,
			radius = 200,
			boundingRadiusMod = 0,
			collision = {
			  hero = false, --allow to hit other heros :-)
			  minion = false,
			  wall = false,
			},
			--
			hitchance = 0,
			out_range = 1000,
			new_path = 0.500,
		},
		target_selector = 8,
		cast_spell = {
			type = 'pos',
			slot = _R,
			arg1 = function(action)
			  local seg = action.ts.seg
			  local obj = action.ts.obj
			  return seg.endPos:to3D(obj.y)
			end,
		},
		slot = player:spellSlot(_R),
		ignore_obj_radius = 800,
		damage = function(target,source)
			if not target then return 0 end
			spell_tb = {0.15,0.25,0.35}
			local source = source or player
			spell_level = source:spellSlot(_R).level
			spell_Dmg = spell_tb[spell_level] or 0;
			AD = common.GetTotalAD(source)
			BonusAD = common.GetBonusAD(source)
			spell_Dmg = spell_Dmg+((3 / 100) * BonusAD / 100)
			
			
			BonusHP = (target.maxHealth - target.health)
			--THISAD = (1.2 + (0.75 * (source.crit)))
			THISAD = (1.2 + (1 * (source.crit)))
			totalDmg = (BonusHP * spell_Dmg) + ( AD * THISAD);
			returndmg = common.CalculatePhysicalDamage(target,totalDmg,source) - common.jshfhp(target,1)
			return spell.get_damage(returndmg,target,source,1)
		end,
	},
}

spell.Gangplank = {
	Q = {
		prediction = {
			type = 'InRange',
			range = 625,
			delay = 0.25,
			speed = 2200,
			hitchance = 0,
			out_range = 625,
			new_path = 0.500,
		},
		target_selector = 8,
		cast_spell = {
			type = 'obj',
			slot = _Q,
			arg1 = function(action)
			  return action.ts.obj
			end,
		},
		slot = player:spellSlot(_Q),
		ignore_obj_radius = 800,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			spell_tb = {20,45,70,95,120}
			spell_level = source:spellSlot(_Q).level
			spell_Dmg = spell_tb[spell_level] or 0;
			totalDmg = spell_Dmg + (common.GetTotalAD(source) * 1);
			returndmg = common.CalculatePhysicalDamage(target,totalDmg,source) - common.jshfhp(target,1)
			return spell.get_damage(returndmg,target,source,2)
		end,
	},
	W = {
		prediction = {
			type = 'self',
			range = 0,
			delay = 0.25,
			--
			hitchance = 0,
			out_range = 1000,
			new_path = 0.500,
		},
		target_selector = 8,
		cast_spell = {
			type = 'self',
			slot = _W,
		},
		slot = player:spellSlot(_W),
		ignore_obj_radius = 0,
		damage = function(target,source)
			return 0
		end,
	},
	E = {
		prediction = {
			type = 'Circular',
			range = 1000,
			delay = 0.80,
			speed = math.huge,
			radius = 345,
			boundingRadiusMod = 0,
			collision = {
			  hero = false, --allow to hit other heros :-)
			  minion = false,
			  wall = false,
			},
			--
			hitchance = 0,
			out_range = 1000,
			new_path = 0.500,
		},
		target_selector = 8,
		cast_spell = {
			type = 'pos',
			slot = _E,
			arg1 = function(action)
			  local seg = action.ts.seg
			  local obj = action.ts.obj
			  return seg.endPos:to3D(obj.y)
			end,
		},
		slot = player:spellSlot(_E),
		ignore_obj_radius = 1200,
		damage = function(target,source)
			return 0
		end,
	},
	R = {
		prediction = {
			type = 'Circular',
			range = 9999,
			delay = 1.00,
			speed = math.huge,
			radius = 100,
			boundingRadiusMod = 0,
			collision = {
			  hero = false, --allow to hit other heros :-)
			  minion = false,
			  wall = false,
			},
			--
			hitchance = 0,
			out_range = 99999,
			new_path = 0.500,
		},
		target_selector = 8,
		cast_spell = {
			type = 'pos',
			slot = _R,
			arg1 = function(action)
			  local seg = action.ts.seg
			  local obj = action.ts.obj
			  return seg.endPos:to3D(obj.y)
			end,
		},
		slot = player:spellSlot(_R),
		ignore_obj_radius = 99999,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			spell_tb = {40,70,100}
			spell_level = source:spellSlot(_R).level
			spell_Dmg = spell_tb[spell_level] or 0;
			totalDmg = spell_Dmg + (common.GetTotalAP(source) * .1);
			returndmg = common.CalculateMagicDamage(target,totalDmg,source) - common.jshfhp(target,1)
			return spell.get_damage(returndmg,target,source,1)
		end,
	},
}

spell.Aatrox = {
	Q = {
		prediction = {
			type = 'Linear',
			range = 625,
			delay = 0.60,
			speed = math.huge,
			width = 90,
			boundingRadiusMod = 0,
			collision = {
			  hero = false,
			  minion = false,
			  wall = false,
			},
			hitchance = 0,
			out_range = 625,
			new_path = 0.500,
		},
		target_selector = 8,
		cast_spell = {
			type = 'pos',
			slot = _Q,
			arg1 = function(action)
			  local seg = action.ts.seg
			  local obj = action.ts.obj
			  return seg.endPos:to3D(obj.y)
			end,
		},
		slot = player:spellSlot(_Q),
		ignore_obj_radius = 625,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			spell_tb = {10,30,50,70,90}
			spell_tb1 = {.6,.65,.7,.75,.8}
			spell_level = source:spellSlot(_Q).level
			spell_Dmg = spell_tb[spell_level] or 0;
			spell_Dmg1 = spell_tb1[spell_level] or 0;
			totalDmg = spell_Dmg + (common.GetTotalAD(source) * spell_Dmg1);
			returndmg = common.CalculatePhysicalDamage(target,totalDmg,source) - common.jshfhp(target,1)
			return spell.get_damage(returndmg,target,source,2)
		end,
	},
	Q2 = {
		prediction = {
			type = 'Linear',
			range = 475,
			delay = 0.60,
			speed = math.huge,
			width = 150,
			boundingRadiusMod = 0,
			collision = {
			  hero = false,
			  minion = false,
			  wall = false,
			},
			hitchance = 0,
			out_range = 475,
			new_path = 0.500,
		},
		target_selector = 8,
		cast_spell = {
			type = 'pos',
			slot = _Q,
			arg1 = function(action)
			  local seg = action.ts.seg
			  local obj = action.ts.obj
			  return seg.endPos:to3D(obj.y)
			end,
		},
		slot = player:spellSlot(_Q),
		ignore_obj_radius = 475,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			spell_tb = {12.5,37.5,62.5,87.5,112.5}
			spell_tb1 = {.75,.8125,.875,.9375,1}
			spell_level = source:spellSlot(_Q).level
			spell_Dmg = spell_tb[spell_level] or 0;
			spell_Dmg1 = spell_tb1[spell_level] or 0;
			totalDmg = spell_Dmg + (common.GetTotalAD(source) * spell_Dmg1);
			returndmg = common.CalculatePhysicalDamage(target,totalDmg,source) - common.jshfhp(target,1)
			return spell.get_damage(returndmg,target,source,2)
		end,
	},
	Q3 = {
		prediction = {
			type = 'Circular',
			range = 300,
			delay = 0.60,
			speed = math.huge,
			radius = 180,
			boundingRadiusMod = 1,
			collision = {
			  hero = false,
			  minion = false,
			  wall = false,
			},
			hitchance = 0,
			out_range = 475,
			new_path = 0.500,
		},
		target_selector = 8,
		cast_spell = {
			type = 'pos',
			slot = _Q,
			arg1 = function(action)
			  local seg = action.ts.seg
			  local obj = action.ts.obj
			  return seg.endPos:to3D(obj.y)
			end,
		},
		slot = player:spellSlot(_Q),
		ignore_obj_radius = 475,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			spell_tb = {12.5,37.5,62.5,87.5,112.5}
			spell_tb1 = {.75,.8125,.875,.9375,1}
			spell_level = source:spellSlot(_Q).level
			spell_Dmg = spell_tb[spell_level] or 0;
			spell_Dmg1 = spell_tb1[spell_level] or 0;
			totalDmg = spell_Dmg + (common.GetTotalAD(source) * spell_Dmg1);
			returndmg = common.CalculatePhysicalDamage(target,totalDmg,source) - common.jshfhp(target,1)
			return spell.get_damage(returndmg,target,source,2)
		end,
	},
	W = {
		prediction = {
			type = 'Linear',
			range = 825,
			delay = 0.25,
			speed = 1800,
			width = 80,
			boundingRadiusMod = 0,
			collision = {
			  hero = true,
			  minion = true,
			  wall = true,
			},
			hitchance = 0,
			out_range = 1000,
			new_path = 0.500,
		},
		target_selector = 8,
		cast_spell = {
			type = 'pos',
			slot = _W,
			arg1 = function(action)
			  local seg = action.ts.seg
			  local obj = action.ts.obj
			  return seg.endPos:to3D(obj.y)
			end,
		},
		slot = player:spellSlot(_W),
		ignore_obj_radius = 0,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			spell_tb = {30,40,50,60,70}
			spell_level = source:spellSlot(_Q).level
			spell_Dmg = spell_tb[spell_level] or 0;
			totalDmg = spell_Dmg + (common.GetTotalAD(source) * .4);
			returndmg = common.CalculatePhysicalDamage(target,totalDmg,source) - common.jshfhp(target,1)
			return spell.get_damage(returndmg,target,source,2)
		end,
	},
	E = {
		prediction = {
			type = 'Linear',
			range = 300,
			delay = 0.33,
			speed = math.huge,
			width = 1,
			boundingRadiusMod = 0,
			collision = {
			  hero = false,
			  minion = false,
			  wall = false,
			},
			hitchance = 0,
			out_range = 300,
			new_path = 0.500,
		},
		target_selector = 8,
		cast_spell = {
			type = 'pos',
			slot = _E,
			arg1 = function(action)
			  local seg = action.ts.seg
			  local obj = action.ts.obj
			  return seg.endPos:to3D(obj.y)
			end,
		},
		slot = player:spellSlot(_E),
		ignore_obj_radius = 0,
		damage = function(target,source)
			return 0
		end,
	},
	R = {
		prediction = {
			type = 'self',
			range = 0,
			delay = 0,
		},
		target_selector = 8,
		cast_spell = {
			type = 'self',
			slot = _R
		},
		slot = player:spellSlot(_R),
		ignore_obj_radius = 99999,
		damage = function(target,source)
			return 0
		end,
	},
}
spell.LeeSin = {
	Q = {
		prediction = {
			type = 'Linear',
			range = 1100,
			delay = 0.25,
			speed = 1800,
			width = 60,
			boundingRadiusMod = 1,
			collision = {
			  hero = true,
			  minion = true,
			  wall = true,
			},
			hitchance = 0,
			out_range = 1200,
			new_path = 0.500,
		},
		target_selector = 8,
		cast_spell = {
			type = 'pos',
			slot = _Q,
			arg1 = function(action)
			  local seg = action.ts.seg
			  local obj = action.ts.obj
			  return seg.endPos:to3D(obj.y)
			end,
		},
		slot = player:spellSlot(_Q),
		ignore_obj_radius = 625,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			spell_tb = {55,80,105,130,155}
			spell_level = source:spellSlot(_Q).level
			spell_Dmg = spell_tb[spell_level] or 0;
			totalDmg = spell_Dmg + (common.GetBonusAD(source) * 1);
			returndmg = common.CalculatePhysicalDamage(target,totalDmg,source) - common.jshfhp(target,1)
			return spell.get_damage(returndmg,target,source,2)
		end,
	},
	Q2 = {
		prediction = {
			type = 'self',
			range = 1300,
			delay = 0.00,
		},
		target_selector = 8,
		cast_spell = {
			type = 'self',
			slot = _Q
		},
		slot = player:spellSlot(_Q),
		ignore_obj_radius = 1300,
		damage = function(target,source,exthp)
			if not target then return 0 end
			local source = source or player
			spell_tb = {55,80,105,130,155}
			spell_level = source:spellSlot(_Q).level
			spell_Dmg = spell_tb[spell_level] or 0;
			totalDmg = spell_Dmg + (common.GetBonusAD(source) * 1);
			local orb = common.get_orb()
			local predict_hp = orb.farm.predict_hp(target,0.25)
			if exthp then
				predict_hp = predict_hp - exthp
			end
			local percent_hp = 100 - (( predict_hp / target.maxHealth) * 100)
			local add_dmg = percent_hp / 10
			totalDmg = totalDmg + totalDmg * add_dmg/10
			returndmg = common.CalculatePhysicalDamage(target,totalDmg,source) - common.jshfhp(target,1)
			
			return spell.get_damage(returndmg,target,source,2)
		end,
	},
	W = {
		prediction = {
			type = 'obj',
			range = 700,
			delay = 0.25,
			speed = 1800,
			width = 80,
			boundingRadiusMod = 0,
			collision = {
			  hero = true,
			  minion = true,
			  wall = true,
			},
			hitchance = 0,
			out_range = 1000,
			new_path = 0.500,
		},
		target_selector = 8,
		cast_spell = {
			type = 'obj',
			slot = _W,
			arg1 = function(action)
			  local obj = action.ts.obj
			  return obj
			end,
		},
		slot = player:spellSlot(_W),
		ignore_obj_radius = 0,
		damage = function(target,source)
			return 0
		end,
	},
	E = {
		prediction = {
			type = 'InRange',
			range = 350,
			radius= 350,
			delay = 0.25,
			dashRadius = 1,
			boundingRadiusModSource = 0,	
			boundingRadiusModTarget = 0,
		},
		target_selector = 8,
		cast_spell = {
			type = 'self',
			slot = _E
		},
		slot = player:spellSlot(_E),
		ignore_obj_radius = 0,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			spell_tb = {100,130,160,190,220}
			spell_level = source:spellSlot(_E).level
			spell_Dmg = spell_tb[spell_level] or 0;
			totalDmg = spell_Dmg + (common.GetBonusAD(source) * 1);
			returndmg = common.CalculateMagicDamage(target,totalDmg,source) - common.jshfhp(target,0.5)
			return spell.get_damage(returndmg,target,source,1)
		end,
	},
	R = {
		prediction = {
			type = 'InRange',
			range = 375,
			delay = 0.25,
		},
		target_selector = 8,
		cast_spell = {
			type = 'self',
			slot = _R
		},
		slot = player:spellSlot(_R),
		ignore_obj_radius = 99999,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			spell_tb = {175,400,625}
			spell_level = source:spellSlot(_R).level
			spell_Dmg = spell_tb[spell_level] or 0;
			totalDmg = spell_Dmg + (common.GetBonusAD(source) * 2);
			returndmg = common.CalculateMagicDamage(target,totalDmg,source) - common.jshfhp(target,0.5)
			return spell.get_damage(returndmg,target,source,2)
		end,
	},
}

spell.Caitlyn = {
	Q = {
		prediction = {
			type = 'Linear',
			range = 1240,
			delay = 0.625,
			speed = 2200,
			width = 90,
			boundingRadiusMod = 1,
			collision = {
			  hero = false,
			  minion = false,
			  wall = true,
			},
			hitchance = 0,
			out_range = 1300,
			new_path = 0.500,
		},
		target_selector = 8,
		cast_spell = {
			type = 'pos',
			slot = _Q,
			arg1 = function(action)
			  local seg = action.ts.seg
			  local obj = action.ts.obj
			  return seg.endPos:to3D(obj.y)
			end,
		},
		slot = player:spellSlot(_Q),
		ignore_obj_radius = 625,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			spell_tb = {50,90,130,170,210}
			spell_tb_b = {1.3,1.45,1.6,1.75,1.9}
			spell_level = source:spellSlot(_Q).level
			spell_Dmg = spell_tb[spell_level] or 0;
			spell_Dmg_b = spell_tb_b[spell_level] or 0;
			totalDmg = spell_Dmg + (common.GetTotalAD(source) * spell_Dmg_b);
			returndmg = common.CalculatePhysicalDamage(target,totalDmg,source) - common.jshfhp(target,1)
			return spell.get_damage(returndmg,target,source,2)
		end,
	},
	W = {
		prediction = {
			type = 'Circular',
			range = 800,
			delay = 0.95,
			speed = math.huge,
			radius = 65,
			boundingRadiusMod = 0,
			collision = {
			  hero = false,
			  minion = false,
			  wall = false,
			},
			hitchance = 0,
			out_range = 800,
			new_path = 0.500,
		},
		target_selector = 8,
		cast_spell = {
			type = 'pos',
			slot = _W,
			arg1 = function(action)
			  local seg = action.ts.seg
			  local obj = action.ts.obj
			  return seg.endPos:to3D(obj.y)
			end,
		},
		slot = player:spellSlot(_W),
		ignore_obj_radius = 0,
		damage = function(target,source)
			return 0
		end,
	},
	E = {
		prediction = {
			type = 'Linear',
			range = 740,
			width = 70,
			delay = 0.15,
			speed = 1600,
			boundingRadiusMod = 1,
			collision = {
			  hero = true,
			  minion = true,
			  wall = true,
			},
			hitchance = 0,
			out_range = 740,
			new_path = 0.500,
		},
		target_selector = 8,
		cast_spell = {
			type = 'pos',
			slot = _E,
			arg1 = function(action)
			  local seg = action.ts.seg
			  local obj = action.ts.obj
			  return seg.endPos:to3D(obj.y)
			end,
		},
		slot = player:spellSlot(_E),
		ignore_obj_radius = 0,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			spell_tb = {70,110,150,190,230 }
			spell_level = source:spellSlot(_E).level
			spell_Dmg = spell_tb[spell_level] or 0;
			totalDmg = spell_Dmg + (common.GetTotalAP(source) * .8);
			returndmg = common.CalculateMagicDamage(target,totalDmg,source) - common.jshfhp(target,0.5)
			return spell.get_damage(returndmg,target,source,1)
		end,
	},
	R = {
		prediction = {
			type = 'InRange',
			range = 3500,
			delay = 1.00,
			width = 400,
			speed = 3200,
			boundingRadiusMod = 1,
			collision = {
			  hero = true,
			  minion = false,
			  wall = true,
			},
		},
		target_selector = 8,
		cast_spell = {
			type = 'self',
			slot = _R
		},
		slot = player:spellSlot(_R),
		ignore_obj_radius = 99999,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			spell_tb = {300,525,750}
			spell_level = source:spellSlot(_R).level
			spell_Dmg = spell_tb[spell_level] or 0;
			totalDmg = spell_Dmg + (common.GetBonusAD(source) * 2);
			returndmg = common.CalculateMagicDamage(target,totalDmg,source) - common.jshfhp(target,0.5)
			return spell.get_damage(returndmg,target,source,2)
		end,
	},
}

spell.Lulu = {
	Q = {
		prediction = {
			type = 'Linear',
			range = 925,
			delay = 0.25,
			speed = 1500,
			width = 60,
			boundingRadiusMod = 1,
			collision = {
			  hero = false,
			  minion = false,
			  wall = true,
			},
			hitchance = 0,
			out_range = 925,
			new_path = 0.500,
		},
		target_selector = 8,
		cast_spell = {
			type = 'pos',
			slot = _Q,
			arg1 = function(action)
			  local seg = action.ts.seg
			  local obj = action.ts.obj
			  return seg.endPos:to3D(obj.y)
			end,
		},
		slot = player:spellSlot(_Q),
		ignore_obj_radius = 625,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			spell_tb = {70,105,140,175,210}
			spell_level = source:spellSlot(_Q).level
			spell_Dmg = spell_tb[spell_level] or 0;
			totalDmg = spell_Dmg + (common.GetTotalAP(source) * .4);
			returndmg = common.CalculateMagicDamage(target,totalDmg,source) - common.jshfhp(target,1)
			return spell.get_damage(returndmg,target,source,1)
		end,
	},
	W = {
		prediction = {
			type = 'InRange',
			range = 650,
			delay = 0.2419,
			hitchance = 0,
			out_range = 800,
			new_path = 0.500,
		},
		target_selector = 8,
		cast_spell = {
			type = 'obj',
			slot = _W,
			arg1 = function(action)
			  return action.ts.obj
			end,
		},
		slot = player:spellSlot(_W),
		ignore_obj_radius = 0,
		damage = function(target,source)
			return 0
		end,
	},
	E = {
		prediction = {
			type = 'InRange',
			range = 650,
			delay = 0.00,
			hitchance = 0,
			out_range = 650,
			new_path = 0.500,
		},
		target_selector = 8,
		cast_spell = {
			type = 'obj',
			slot = _E,
			arg1 = function(action)
			  return action.ts.obj
			end,
		},
		slot = player:spellSlot(_E),
		ignore_obj_radius = 0,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			spell_tb = {80,120,160,200,240}
			spell_level = source:spellSlot(_E).level
			spell_Dmg = spell_tb[spell_level] or 0;
			totalDmg = spell_Dmg + (common.GetTotalAP(source) * .4);
			returndmg = common.CalculateMagicDamage(target,totalDmg,source) - common.jshfhp(target,0.5)
			return spell.get_damage(returndmg,target,source,1)
		end,
	},
	R = {
		prediction = {
			type = 'InRange',
			range = 900,
			delay = 0.00,
		},
		target_selector = 8,
		cast_spell = {
			type = 'obj',
			slot = _R,
			arg1 = function(action)
			  return action.ts.obj
			end,
		},
		slot = player:spellSlot(_R),
		ignore_obj_radius = 99999,
		damage = function(target,source)
			return 0
		end,
	},
}


spell.Gwen = {
	Q = {
		prediction = {
			type = 'Linear',
			range = 450,
			delay = 0.50,
			speed = math.huge,
			width = 30,
			boundingRadiusMod = 1,
			collision = {
			  hero = false,
			  minion = false,
			  wall = false,
			},
			hitchance = 0,
			out_range = 500,
			new_path = 0.500,
		},
		target_selector = 8,
		cast_spell = {
			type = 'pos',
			slot = _Q,
			arg1 = function(action)
			  local seg = action.ts.seg
			  local obj = action.ts.obj
			  return seg.endPos:to3D(obj.y)
			end,
		},
		slot = player:spellSlot(_Q),
		ignore_obj_radius = 500,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			spell_tb = {8,10.75,13.5,16.25,19}
			spell_tb_b = {40,53.75,67.5,81.25,95}
			spell_level = source:spellSlot(_Q).level
			spell_Dmg = spell_tb[spell_level] or 0;
			spell_Dmg_b = spell_tb_b[spell_level] or 0;
			totalDmg = spell_Dmg + (common.GetTotalAP(source) * .05);
			totalDmg_b = spell_Dmg_b + (common.GetTotalAP(source) * .25);
			returndmga = common.CalculateMagicDamage(target,totalDmg,source) + totalDmg
			returndmgb = common.CalculateMagicDamage(target,totalDmg_b,source)
			local stacks = 1
			if player.buff['gwenq'] then
				stacks = player.buff['gwenq'].stacks2+1
				returndmga = returndmga * stacks
			end
			return spell.get_damage(returndmga+returndmgb,target,source,1)
		end,
	},
	W = {
		prediction = {
			type = 'InRange',
			range = 480,
			delay = 0.00,
			hitchance = 0,
			out_range = 480,
			new_path = 0.500,
		},
		target_selector = 8,
		cast_spell = {
			type = 'pos',
			slot = _W,
			arg1 = function(action)
			  return player.pos
			end,
		},
		slot = player:spellSlot(_W),
		ignore_obj_radius = 0,
		damage = function(target,source)
			return 0
		end,
	},
	E = {
		prediction = {
			type = 'InRange',
			range = 350,
			delay = 0.00,
			hitchance = 0,
			out_range = 350,
			new_path = 0.500,
		},
		target_selector = 8,
		cast_spell = {
			type = 'pos',
			slot = _E,
			arg1 = function(action)
			  return mousePos
			end,
		},
		slot = player:spellSlot(_E),
		ignore_obj_radius = 0,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			spell_tb = {10,15,20,25,30}
			spell_level = source:spellSlot(_E).level
			spell_Dmg = spell_tb[spell_level] or 0;
			totalDmg = spell_Dmg + (common.GetTotalAP(source) * .08);
			
			returndmg = common.CalculateMagicDamage(target,totalDmg,source)
			return spell.get_damage(returndmg,target,source,1)
		end,
	},
	R = {
		prediction = {
			type = 'Linear',
			range = 1200,
			delay = 0.50,
			speed = 1800,
			width = 60,
			boundingRadiusMod = 1,
			collision = {
			  hero = false,
			  minion = false,
			  wall = false,
			},
			hitchance = 0,
			out_range = 500,
			new_path = 0.500,
		},
		target_selector = 8,
		cast_spell = {
			type = 'pos',
			slot = _R,
			arg1 = function(action)
			  local seg = action.ts.seg
			  local obj = action.ts.obj
			  return seg.endPos:to3D(obj.y)
			end,
		},
		slot = player:spellSlot(_R),
		ignore_obj_radius = 99999,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			--local stacks = 0
			--if player.buff['gwenrrecast'] then
				--stacks = player.buff['gwenrrecast'].stacks2
			--end
			spell_tb = {30,55,80}
			spell_level = source:spellSlot(_R).level
			spell_Dmg = spell_tb[spell_level] or 0;
			totalDmg = spell_Dmg + (common.GetTotalAP(source) * .08);
			
			returndmg = common.CalculateMagicDamage(target,totalDmg,source)
			return spell.get_damage(returndmg,target,source,1)
		end,
	},
}


spell.Morgana = {
	Q = {
		prediction = {
			type = 'Linear',
			range = 1250,
			delay = 0.21,
			speed = 1200,
			width = 70,
			boundingRadiusMod = 1,
			collision = {
			  hero = true,
			  minion = true,
			  wall = true,
			},
			hitchance = 0,
			out_range = 1250,
			new_path = 0.500,
		},
		target_selector = 8,
		cast_spell = {
			type = 'pos',
			slot = _Q,
			arg1 = function(action)
			  local seg = action.ts.seg
			  local obj = action.ts.obj
			  return seg.endPos:to3D(obj.y)
			end,
		},
		slot = player:spellSlot(_Q),
		ignore_obj_radius = 11300,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			spell_tb = {80,135,190,245,300}
			spell_level = source:spellSlot(_Q).level
			spell_Dmg = spell_tb[spell_level] or 0;
			totalDmg = spell_Dmg + (common.GetTotalAP(source) * .9);
			returndmg = common.CalculateMagicDamage(target,totalDmg,source) + totalDmg
			
			return spell.get_damage(returndmg,target,source,1)
		end,
	},
	W = {
		prediction = {
			type = 'Circular',
			range = 900,
			delay = 0.21,
			speed = 1200,
			radius = 270,
			boundingRadiusMod = 1,
			collision = {
			  hero = false,
			  minion = false,
			  wall = false,
			},
			hitchance = 0,
			out_range = 900,
			new_path = 0.500,
		},
		target_selector = 8,
		cast_spell = {
			type = 'pos',
			slot = _W,
			arg1 = function(action)
			  local seg = action.ts.seg
			  local obj = action.ts.obj
			  return seg.endPos:to3D(obj.y)
			end,
		},
		slot = player:spellSlot(_W),
		ignore_obj_radius = 0,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			spell_tb = {6,11,16,21,26 }
			spell_level = source:spellSlot(_Q).level
			spell_Dmg = spell_tb[spell_level] or 0;
			totalDmg = spell_Dmg + (common.GetTotalAP(source) * .07);
			returndmg = common.CalculateMagicDamage(target,totalDmg,source) + totalDmg
			
			return spell.get_damage(returndmg,target,source,1)
		end,
	},
	E = {
		prediction = {
			type = 'obj',
			range = 800,
			delay = 0.00,
			hitchance = 0,
			out_range = 800,
			new_path = 0.500,
		},
		target_selector = 8,
		cast_spell = {
			type = 'obj',
			slot = _E,
			arg1 = function(action)
			  return action.ts.obj
			end,
		},
		slot = player:spellSlot(_E),
		ignore_obj_radius = 0,
		damage = function(target,source)
			if not target then return 0 end
			return 0
		end,
	},
	R = {
		prediction = {
			type = 'InRange',
			range = 625,
			radius = 625,
			delay = 0.35,
			dashRadius = 0,
			boundingRadiusModSource = 0,
			boundingRadiusModTarget = 0,
		},
		target_selector = 8,
		cast_spell = {
			type = 'obj',
			slot = _R,
			arg1 = function(action)
			  local seg = action.ts.seg
			  local obj = action.ts.obj
			  return obj
			end,
		},
		slot = player:spellSlot(_R),
		ignore_obj_radius = 575,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			spell_tb = {150,225,300}
			spell_level = source:spellSlot(_R).level
			spell_Dmg = spell_tb[spell_level] or 0;
			totalDmg = spell_Dmg + (common.GetTotalAP(source) * .7);
			returndmg = common.CalculateMagicDamage(target,totalDmg,source)
			return spell.get_damage(returndmg,target,source,1)
		end,
	},
}

spell.Aphelios = {
	Q = {
		prediction = {
			type = 'Linear',
			range = 1450,
			delay = 0.40,
			speed = 1850,
			width = 60,
			boundingRadiusMod = 1,
			collision = {
			  hero = true,
			  minion = true,
			  wall = true,
			},
			hitchance = 0,
			out_range = 1450,
			new_path = 0.500,
		},
		target_selector = 8,
		cast_spell = {
			type = 'pos',
			slot = _Q,
			arg1 = function(action)
			  local seg = action.ts.seg
			  local obj = action.ts.obj
			  return seg.endPos:to3D(obj.y)
			end,
		},
		slot = player:spellSlot(_Q),
		ignore_obj_radius = 1450,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			--狙击步枪
			local j = math.min(7,math.max(1,math.ceil(player.levelRef / 2 ))) - 1
			local total = (60 + 16.6666*j) + (common.GetBonusAD(source) * (0.42 + 0.03*j)) + (common.GetTotalAP(source))
			--副武器被动
			local Fu_pass = 15 + common.GetBonusAD(source) * .2
			returndmg = common.CalculatePhysicalDamage(target,total+Fu_pass,source) + common.CalculateAADamage(target) 
			
			
			
			--红刀
			local attackSpeedMod = ((source.attackSpeedMod - 1) * 100)
			local attackNum = 6 + math.floor(attackSpeedMod/100 / 3 *10 + 0.1)
			local total_red = (10 + 3.3333 * j )+(common.GetBonusAD(source) * (0.21 + 0.015 * j)) * attackNum
			returndmg_red = common.CalculatePhysicalDamage(target,total_red,source)
			
			--重力炮
			local total_z = (50 + 10 * j)+ (common.GetBonusAD(source) * (0.26 + 0.15 * j)) + (common.GetTotalAP(source)*.7)
			returndmg_z = common.CalculateMagicDamage(target,total_z,source)
			
			
			--喷火器
			local total_p = (25 + 6.6666 * j ) +(common.GetBonusAD(source) * (0.56 + 0.04 * j ))+ (common.GetTotalAP(source)*.7)
			returndmg_p = common.CalculatePhysicalDamage(target,total_p,source)
			
			--飞碟
			local total_f = (25+10*j) + (common.GetBonusAD(source) * (0.35+0.025*j)) + (common.GetTotalAP(source)*.5)
			returndmg_p = common.CalculatePhysicalDamage(target,total_f,source)
			
			
			return spell.get_damage(returndmg,target,source,2)
		end,
	},
	W = {
		prediction = {
			range = 0,
			delay = 0.0,
		},
		target_selector = 8,
		slot = player:spellSlot(_W),
		ignore_obj_radius = 0,
		damage = function(target,source)
			if not target then return 0 end
			
			return 0
		end,
	},
	E = {
		prediction = {
			
		},
		target_selector = 8,
		cast_spell = {
			
		},
		slot = player:spellSlot(_E),
		ignore_obj_radius = 0,
		damage = function(target,source)
			if not target then return 0 end
			return 0
		end,
	},
	R = {
		prediction = {
			type = 'Linear',
			range = 1300,
			delay = 0.6,
			speed = 2050,
			width = 150,
			boundingRadiusMod = 1,
			collision = {
			  hero = true,
			  minion = true,
			  wall = true,
			},
			hitchance = 0,
			out_range = 1450,
			new_path = 0.500,
		},
		target_selector = 8,
		cast_spell = {
			type = 'obj',
			slot = _R,
			arg1 = function(action)
			  local seg = action.ts.seg
			  local obj = action.ts.obj
			  return obj
			end,
		},
		slot = player:spellSlot(_R),
		ignore_obj_radius = 575,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			spell_tb = {150,225,300}
			spell_level = source:spellSlot(_R).level
			spell_Dmg = spell_tb[spell_level] or 0;
			totalDmg = spell_Dmg + (common.GetTotalAP(source) * .7);
			returndmg = common.CalculateMagicDamage(target,totalDmg,source)
			return spell.get_damage(returndmg,target,source,1)
		end,
	},
}


spell.Sett = {
	Q = {
		prediction = {
			type = 'InRange',
			range = player.attackRange,
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_Q),
		ignore_obj_radius = 1450,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			local dmg = {10,20,30,40,50}
			local qlevel = source:spellSlot(0).level
			local QDmg = dmg[qlevel] or 0;
			local ad = common.GetTotalAD(source)
			local bfb = math.floor(ad/100)
			local totalQAdDmg = QDmg +target.maxHealth * ((qlevel + bfb)  / 100 );
			local total =  common.CalculatePhysicalDamage(target,totalQAdDmg,source)
			return spell.get_damage(total,target,source,2)
		end,
	},
	W = {
		prediction = {
			type = 'Linear',
			range = 790,
			delay = 0.75,
			speed = math.huge,
			width = 90,
			boundingRadiusMod = 1,
			collision = {
			  hero = false,
			  minion = false,
			  wall = false,
			},
			hitchance = 0,
			out_range = 790,
			new_path = 0.500,
		},
		target_selector = 8,
		slot = player:spellSlot(_W),
		ignore_obj_radius = 0,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			local dmg = {80,100,120,140,160}
			local wlevel = source:spellSlot(1).level
			local WDmg = dmg[wlevel] or 0;
			local BonusADbfb = common.GetBonusAD(source) / 100 * 0.01
		
			local totalQAdDmg = (WDmg + (source.mana * (0.25+BonusADbfb)))
			return totalQAdDmg
		end,
	},
	E = {
		prediction = {
			type = 'Linear',
			range = 490,
			delay = 0.30,
			speed = math.huge,
			width = 90,
			boundingRadiusMod = 1,
			collision = {
			  hero = true,
			  minion = true,
			  wall = false,
			},
			hitchance = 0,
			out_range = 490,
			new_path = 0.500,
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_E),
		ignore_obj_radius = 0,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			local dmg = {50,70,90,110,130}
			local elevel = source:spellSlot(2).level
			local EDmg = dmg[elevel] or 0;
			local totalQAdDmg = EDmg + (common.GetTotalAD(source) *.6);
			local total =  common.CalculatePhysicalDamage(target,totalQAdDmg,source)
			
			return spell.get_damage(total,target,source,2)
		end,
	},
	R = {
		prediction = {
			type = 'InRange',
			range = 400,
			delay = 0.00,
		},
		target_selector = 8,
		cast_spell = {
			type = 'obj',
			slot = _R,
			arg1 = function(action)
			  local seg = action.ts.seg
			  local obj = action.ts.obj
			  return obj
			end,
		},
		slot = player:spellSlot(_R),
		ignore_obj_radius = 575,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			local dmg = {200,300,400}
			local rlevel = source:spellSlot(3).level
			local RDmg = dmg[rlevel] or 0;
			local ew = target.maxHealth - 650
			local bfb = (3 + rlevel) * 0.1
			local ewad = common.GetBonusAD(source) * bfb
			local totalQAdDmg = RDmg + (common.GetBonusAD(source) + ewad);
			local total =  common.CalculatePhysicalDamage(target,totalQAdDmg,source)
			return spell.get_damage(total,target,source,2)
		end,
	},
}
spell.Gnar = {
	Q = {
		prediction = {
			type = 'Linear',
			range = 1125,
			delay = 0.25,
			speed = 2500,
			width = 30,
			boundingRadiusMod = 1,
			collision = {
			  hero = true,
			  minion = true,
			  wall = true,
			},
			hitchance = 0,
			out_range = 1125,
			new_path = 0.500,
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_Q),
		ignore_obj_radius = 1450,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			local dmg = {5,45,85,125,165}
			local qlevel = source:spellSlot(0).level
			local QDmg = dmg[qlevel] or 0;
			local ad = common.GetTotalAD(source) * 1.15
			local totalQAdDmg = QDmg + ad
			local total =  common.CalculatePhysicalDamage(target,totalQAdDmg,source)
			return spell.get_damage(total,target,source,2)
		end,
	},
	Q1 = {
		prediction = {
			type = 'Linear',
			range = 1150,
			delay = 0.50,
			speed = 2100,
			width = 90,
			boundingRadiusMod = 1,
			collision = {
			  hero = true,
			  minion = true,
			  wall = true,
			},
			hitchance = 0,
			out_range = 1150,
			new_path = 0.500,
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_Q),
		ignore_obj_radius = 1450,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			local dmg = {5,45,85,125,165}
			local qlevel = source:spellSlot(0).level
			local QDmg = dmg[qlevel] or 0;
			local ad = common.GetTotalAD(source)
			local totalQAdDmg = QDmg + ad
			local total =  common.CalculatePhysicalDamage(target,totalQAdDmg,source)
			return spell.get_damage(total,target,source,2)
		end,
	},
	W = {
		prediction = {
			range = 0,
		},
		target_selector = 8,
		slot = player:spellSlot(_W),
		ignore_obj_radius = 0,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			local dmg = {0,10,20,30,40}
			local dmg_B = {0.06,0.08,0.1,0.12,0.14}
			local wlevel = source:spellSlot(1).level
			local WDmg = dmg[wlevel] or 0;
			local WDmg_B = dmg_B[wlevel] or 0;
			local BonusADbfb = common.GetTotalAP(source)
		
			local totalQAdDmg = WDmg + (target.maxHealth * WDmg_B) + BonusADbfb
			local total =  common.CalculateMagicDamage(target,totalQAdDmg,source)
			return spell.get_damage(total,target,source,1)
		end,
	},
	W1 = {
		prediction = {
			type = 'Linear',
			range = 550,
			delay = 0.60,
			speed = math.huge,
			width = 100,
			boundingRadiusMod = 1,
			collision = {
			  hero = true,
			  minion = true,
			  wall = true,
			},
			hitchance = 0,
			out_range = 550,
			new_path = 0.500,
		},
		target_selector = 8,
		slot = player:spellSlot(_W),
		ignore_obj_radius = 0,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			local dmg = {25,55,85,115,145}
			local wlevel = source:spellSlot(1).level
			local QDmg = dmg[wlevel] or 0;
			local ad = common.GetTotalAD(source)
			local totalQAdDmg = QDmg + ad
			local total =  common.CalculatePhysicalDamage(target,totalQAdDmg,source)
			return spell.get_damage(total,target,source,2)
		end,
	},
	E = {
		prediction = {
			type = 'Linear',
			range = 475,
			delay = 0.00,
			speed = math.huge,
			width = 75,
			boundingRadiusMod = 1,
			collision = {
			  hero = true,
			  minion = true,
			  wall = false,
			},
			hitchance = 0,
			out_range = 475,
			new_path = 0.500,
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_E),
		ignore_obj_radius = 0,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			local dmg = {50,85,120,155,190}
			local elevel = source:spellSlot(2).level
			local EDmg = dmg[elevel] or 0;
			local totalQAdDmg = EDmg + (target.maxHealth * 0.06)
			local total =  common.CalculatePhysicalDamage(target,totalQAdDmg,source)
			
			return spell.get_damage(total,target,source,2)
		end,
	},
	E1 = {
		prediction = {
			type = 'Circular',
			range = 675,
			delay = 0.25,
			speed = math.huge,
			radius = 375,
			boundingRadiusMod = 1,
			collision = {
			  hero = false, --allow to hit other heros :-)
			  minion = false,
			  wall = false,
			},
			--
			hitchance = 0,
			out_range = 675,
			new_path = 0.500,
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_E),
		ignore_obj_radius = 0,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			local dmg = {80,115,150,185,220}
			local elevel = source:spellSlot(2).level
			local EDmg = dmg[elevel] or 0;
			local totalQAdDmg = EDmg + (target.maxHealth * 0.06)
			local total =  common.CalculatePhysicalDamage(target,totalQAdDmg,source)
			
			return spell.get_damage(total,target,source,2)
		end,
	},
	R = {
		prediction = {
			type = 'Circular',
			range = 465,
			delay = 0.25,
			speed = math.huge,
			radius = 465,
			boundingRadiusMod = 1,
			ig = true,
			collision = {
			  hero = false, --allow to hit other heros :-)
			  minion = false,
			  wall = false,
			},
			--
			hitchance = 0,
			out_range = 465,
			new_path = 0.500,
		},
		target_selector = 8,
		cast_spell = {
			type = 'pos',
			slot = _R,
			arg1 = function(action)
			  local seg = action.ts.seg
			  local obj = action.ts.obj
			  return obj
			end,
		},
		slot = player:spellSlot(_R),
		ignore_obj_radius = 465,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			local dmg = {200,300,400}
			local rlevel = source:spellSlot(3).level
			local RDmg = dmg[rlevel] or 0;
			local BonusAD = common.GetBonusAD(source) * 0.5
			local totalQAdDmg = RDmg + BonusAD +  common.GetTotalAP(source)
			local total =  common.CalculatePhysicalDamage(target,totalQAdDmg,source)
			return spell.get_damage(total,target,source,2)
		end,
	},
}

spell.Xayah = {
	Q = {
		prediction = {
			type = 'Linear',
			range = 1099,
			delay = 0.70,
			speed = math.huge,
			width = 50,
			boundingRadiusMod = 1,
			collision = {
			  hero = false,
			  minion = false,
			  wall = true,
			},
			hitchance = 0,
			out_range = 1099,
			new_path = 0.500,
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_Q),
		ignore_obj_radius = 1450,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			local dmg = {45,65,85,105,125}
			local qlevel = source:spellSlot(0).level
			local QDmg = dmg[qlevel] or 0;
			local ad = common.GetTotalAD(source) * .5
			local totalQAdDmg = QDmg + ad
			local total =  common.CalculatePhysicalDamage(target,totalQAdDmg,source) 
			return spell.get_damage(total,target,source,2) 
		end,
	},
	
	W = {
		prediction = {
			range = 0,
			speed = 3000,
		},
		target_selector = 8,
		slot = player:spellSlot(_W),
		ignore_obj_radius = 0,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			return common.CalculateAADamage(target, source)
		end,
	},
	E = {
		prediction = {
			type = 'Linear',
			range = 9999,
			delay = 0.10,
			speed = 4000,
			width = 40,
			boundingRadiusMod = 0,
			collision = {
			  hero = true,
			  minion = false,
			  wall = false,
			},
			hitchance = 0,
			out_range = 9999,
			new_path = 0.500,
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_E),
		ignore_obj_radius = 0,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			local dmg = {55,65,75,85,95}
			local elevel = source:spellSlot(2).level
			local EDmg = dmg[elevel] or 0;
			local ad = common.GetBonusAD(source) * .6
			local totalQAdDmg = EDmg + ad
			local total =  common.CalculatePhysicalDamage(target,totalQAdDmg,source) 
			return spell.get_damage(total,target,source,2)
		end,
	},
	R = {
		prediction = {
			type = 'Linear',
			range = 1060,
			delay = 0.00,
			speed = math.huge,
			width = 1,
			boundingRadiusMod = 1,
			collision = {
			  hero = false, --allow to hit other heros :-)
			  minion = false,
			  wall = false,
			},
			--
			hitchance = 0,
			out_range = 1060,
			new_path = 0.500,
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_R),
		ignore_obj_radius = 1060,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			local dmg = {125,250,375}
			local rlevel = source:spellSlot(3).level
			local RDmg = dmg[rlevel] or 0;
			local BonusAD = common.GetBonusAD(source) * 1
			local totalQAdDmg = RDmg + BonusAD 
			local total =  common.CalculatePhysicalDamage(target,totalQAdDmg,source)
			return spell.get_damage(total,target,source,2)
		end,
	},
}

spell.Syndra = {
	Q = {
		prediction = {
			type = 'Circular',
			range = 790,
			delay = 0.6,
			speed = math.huge,
			radius = 125,
			boundingRadiusMod = 1,
			collision = {
			  hero = false,
			  minion = false,
			  wall = true,
			},
			hitchance = 0,
			out_range = 1099,
			new_path = 0.500,
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_Q),
		ignore_obj_radius = 1450,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			local dmg = {70,105,140,175,210}
			local qlevel = source:spellSlot(0).level
			local QDmg = dmg[qlevel] or 0;
			local ap = source.totalAp * .5
			local totalQAdDmg = QDmg + ap
			local total =  common.CalculateMagicDamage(target,totalQAdDmg,source) 
			return spell.get_damage(total,target,source,1) 
		end,
	},
	
	W = {
		prediction = {
			type = 'InRange',
			delay = 0.2,
			radius = 825,
			range = 950,
			dashRadius = 0,
			boundingRadiusModSource = 0,
			boundingRadiusModTarget = 0,
		},
		target_selector = 8,
		slot = player:spellSlot(_W),
		ignore_obj_radius = 0,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			local dmg = {70,110,150,190,230}
			local wlevel = source:spellSlot(1).level
			local WDmg = dmg[wlevel] or 0;
			local ap = source.totalAp * .7
			local totalQAdDmg = WDmg + ap
			local total =  common.CalculateMagicDamage(target,totalQAdDmg,source) 
			return spell.get_damage(total,target,source,1) 
		end,
	},
	W1 = {
		prediction = {
			type = 'Circular',
			delay = 0.25,
			speed = 1450,
			range = 950,
			radius = 200,
			boundingRadiusMod = 0
		},
		target_selector = 8,
		slot = player:spellSlot(_W),
		ignore_obj_radius = 0,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			local dmg = {70,110,150,190,230}
			local wlevel = source:spellSlot(1).level
			local WDmg = dmg[wlevel] or 0;
			local ap = source.totalAp * .7
			local totalQAdDmg = WDmg + ap
			local total =  common.CalculateMagicDamage(target,totalQAdDmg,source) 
			return spell.get_damage(total,target,source,1) 
		end,
	},
	E = {
		prediction = {
			type = 'Linear',
			range = 9999,
			delay = 0.00,
			speed = 4000,
			width = 60,
			boundingRadiusMod = 0,
			collision = {
			  hero = true,
			  minion = false,
			  wall = false,
			},
			hitchance = 0,
			out_range = 9999,
			new_path = 0.500,
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_E),
		ignore_obj_radius = 0,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			local dmg = {55,65,75,85,95}
			local elevel = source:spellSlot(2).level
			local EDmg = dmg[elevel] or 0;
			local ad = common.GetBonusAD(source) * .6
			local totalQAdDmg = EDmg + ad
			local total =  common.CalculatePhysicalDamage(target,totalQAdDmg,source) 
			return spell.get_damage(total,target,source,1)
		end,
	},
	R = {
		prediction = {
			type = 'InRange',
			delay = 0,
			radius = 675,
			width = 75,
			speed = 1100,
			range = 675,
			dashRadius = 0,
			boundingRadiusModSource = 0,
			boundingRadiusModTarget = 0,
			collision = {
				wall = true,
			},
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_R),
		ignore_obj_radius = 1060,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			local dmg = {90,140,190}
			local rlevel = source:spellSlot(3).level
			local ap = source.totalAp * .2
			local RDmg = dmg[rlevel] or 0;
			local totalQAdDmg = RDmg + ap
			local total =  common.CalculateMagicDamage(target,totalQAdDmg,source)
			return spell.get_damage(total,target,source,1)
		end,
	},
}
spell.Akshan = {
	Q = {
		prediction = {
			type = 'Linear',
			range = 800,
			delay = 0.25,
			speed = 1500,
			width = 60,
			boundingRadiusMod = 1,
			collision = {
			  hero = true,
			  minion = true,
			  wall = false,
			},
			hitchance = 0,
			out_range = 850,
			new_path = 0.500,
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_Q),
		ignore_obj_radius = 850,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			local dmg = {5, 25,45,65,85}
			local qlevel = source:spellSlot(0).level
			local QDmg = dmg[qlevel] or 0;
			local ad = source.totalAd * .8
			local totalQAdDmg = QDmg + ad
			local total =  common.CalculatePhysicalDamage(target,totalQAdDmg,source) 
			return spell.get_damage(total,target,source,2) 
		end,
	},
	
	W = {
		prediction = {
			type = 'InRange',
			delay = 0.50,
			radius = 800,
			range = 800,
			dashRadius = 0,
			boundingRadiusModSource = 0,
			boundingRadiusModTarget = 0,
		},
		target_selector = 8,
		slot = player:spellSlot(_W),
		ignore_obj_radius = 0,
		damage = function(target,source)
			if not target then return 0 end
			return 0
		end,
	},
	E = {
		prediction = {
			type = 'Linear',
			range = 800,
			delay = 0.00,
			speed = 2500,
			width = 60,
			boundingRadiusMod = 0,
			collision = {
			  hero = true,
			  minion = false,
			  wall = false,
			},
			hitchance = 0,
			out_range = 800,
			new_path = 0.500,
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_E),
		ignore_obj_radius = 0,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			local dmg = {30,55,80,105,130 }
			local elevel = source:spellSlot(2).level
			local EDmg = dmg[elevel] or 0;
			local ad = common.GetBonusAD(source) * .1
			local totalQAdDmg = EDmg + ad
			local total =  common.CalculatePhysicalDamage(target,totalQAdDmg,source) 
			return spell.get_damage(total,target,source,2)
		end,
	},
	R = {
		prediction = {
			type = 'Linear',
			range = 2500,
			delay = 2.5,
			speed = 3200,
			width = 60,
			boundingRadiusMod = 1,
			collision = {
			  hero = true,
			  minion = true,
			  wall = true,
			},
			hitchance = 0,
			out_range = 2500,
			new_path = 0.500,
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_R),
		ignore_obj_radius = 1060,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			local dmg = {20,25,30}
			local rlevel = source:spellSlot(3).level
			local ad = source.totalAd * .1
			local RDmg = dmg[rlevel] or 0;
			local totalQAdDmg = RDmg + ad
			local total =  common.CalculatePhysicalDamage(target,totalQAdDmg,source)
			return spell.get_damage(total,target,source,2)
		end,
	},
}

spell.XinZhao = {
	Q = {
		prediction = {
			type = 'Linear',
			range = 850,
			delay = 0.25,
			speed = 1500,
			width = 120,
			boundingRadiusMod = 1,
			collision = {
			  hero = true,
			  minion = true,
			  wall = false,
			},
			hitchance = 0,
			out_range = 850,
			new_path = 0.500,
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_Q),
		ignore_obj_radius = 850,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			local dmg = {5, 25,45,65,85}
			local qlevel = source:spellSlot(0).level
			local QDmg = dmg[qlevel] or 0;
			local ad = source.totalAd * .8
			local totalQAdDmg = QDmg + ad
			local total =  common.CalculatePhysicalDamage(target,totalQAdDmg,source) 
			return spell.get_damage(total,target,source,2) 
		end,
	},
	
	W = {
		prediction = {
			type = 'Linear',
			range = 940,
			delay = 0.50,
			speed = 6250,
			width = 30,
			boundingRadiusMod = 1,
			collision = {
			  hero = true,
			  minion = true,
			  wall = false,
			},
			hitchance = 0,
			out_range = 940,
			new_path = 0.500,
		},
		target_selector = 8,
		slot = player:spellSlot(_W),
		ignore_obj_radius = 0,
		damage = function(target,source)
			if not target then return 0 end
			return 0
		end,
	},
	E = {
		prediction = {
			type = 'Linear',
			range = 800,
			delay = 0.00,
			speed = 2500,
			width = 60,
			boundingRadiusMod = 0,
			collision = {
			  hero = true,
			  minion = false,
			  wall = false,
			},
			hitchance = 0,
			out_range = 800,
			new_path = 0.500,
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_E),
		ignore_obj_radius = 0,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			local dmg = {30,55,80,105,130 }
			local elevel = source:spellSlot(2).level
			local EDmg = dmg[elevel] or 0;
			local ad = common.GetBonusAD(source) * .1
			local totalQAdDmg = EDmg + ad
			local total =  common.CalculatePhysicalDamage(target,totalQAdDmg,source) 
			return spell.get_damage(total,target,source,2)
		end,
	},
	R = {
		prediction = {
			type = 'Linear',
			range = 2500,
			delay = 2.5,
			speed = 3200,
			width = 60,
			boundingRadiusMod = 1,
			collision = {
			  hero = true,
			  minion = true,
			  wall = true,
			},
			hitchance = 0,
			out_range = 2500,
			new_path = 0.500,
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_R),
		ignore_obj_radius = 1060,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			local dmg = {20,25,30}
			local rlevel = source:spellSlot(3).level
			local ad = source.totalAd * .1
			local RDmg = dmg[rlevel] or 0;
			local totalQAdDmg = RDmg + ad
			local total =  common.CalculatePhysicalDamage(target,totalQAdDmg,source)
			return spell.get_damage(total,target,source,2)
		end,
	},
}

spell.Vex = {
	Q = {
		prediction = {
			type = 'Linear',
			range = 1200,
			delay = 0.15,
			speed = 3200,
			width = 60,
			boundingRadiusMod = 1,
			collision = {
			  hero = false,
			  minion = false,
			  wall = wall,
			},
			hitchance = 0,
			out_range = 1200,
			new_path = 0.500,
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_Q),
		ignore_obj_radius = 1200,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			local dmg = {60,110,160,210,260}
			local qlevel = source:spellSlot(0).level
			local QDmg = dmg[qlevel] or 0;
			local ap = source.totalAp * .6
			local totalQDmg = QDmg + ap
			local total =  common.CalculateMagicDamage(target,totalQDmg,source) 
			return spell.get_damage(total,target,source,1) 
		end,
	},
	
	W = {
		prediction = {
			type = 'InRange',
			range = 475 ,
			radius = 475 ,
			delay = 0.00,
			dashRadius = 0,
			boundingRadiusModSource = 0,
			boundingRadiusModTarget = 0,
		},
		target_selector = 8,
		slot = player:spellSlot(_W),
		ignore_obj_radius = 0,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			local dmg = {80,120,160,200,240}
			local wlevel = source:spellSlot(1).level
			local WDmg = dmg[wlevel] or 0;
			local ap = source.totalAp * .3
			local totalQDmg = WDmg + ap
			local total =  common.CalculateMagicDamage(target,totalQDmg,source) 
			return spell.get_damage(total,target,source,1) 
		end,
	},
	E = {
		prediction = {
			type = 'Circular',
			range = 800,
			delay = 0.25,
			speed = 1300,
			radius = 200,
			boundingRadiusMod = 0,
			collision = {
			  hero = false, --allow to hit other heros :-)
			  minion = false,
			  wall = false,
			},
			--
			hitchance = 0,
			out_range = 800,
			new_path = 0.500,
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_E),
		ignore_obj_radius = 0,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			local dmg = {80,120,160,200,240}
			local dmg_level = {0.4,0.45,0.5,0.55,0.60}
			local elevel = source:spellSlot(2).level
			local EDmg = dmg[elevel] or 0;
			local E_ap = dmg_level[elevel] or 0;
			local ap = source.totalAp * E_ap
			local totalQDmg = EDmg + ap
			local total =  common.CalculateMagicDamage(target,totalQDmg,source) 
			return spell.get_damage(total,target,source,1) 
		end,
	},
	R = {
		prediction = {
			type = 'Linear',
			range = 2000,
			delay = 0.25,
			speed = 1600,
			width = 60,
			boundingRadiusMod = 1,
			collision = {
			  hero = true,
			  minion = true,
			  wall = true,
			},
			hitchance = 0,
			out_range = 2000,
			new_path = 0.500,
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_R),
		ignore_obj_radius = 2000,
		damage = function(target,source,stacks)
			if not target then return 0 end
			local source = source or player
			local dmg = {75,125,175}
			local rlevel = source:spellSlot(3).level
			local RDmg = dmg[rlevel] or 0;
			local ap = source.totalAp * .2
			local totalQDmg = RDmg + ap
			local total_a =  common.CalculateMagicDamage(target,totalQDmg,source) 
			local total = total_a
			if not stacks then
				local dmg_b = {150,250,350}
				local RDmg_b = dmg_b[rlevel] or 0;
				local ap_b = source.totalAp * .5
				local totalQDmg_b = RDmg_b + ap_b
				local total_b =  common.CalculateMagicDamage(target,totalQDmg_b,source) 
				total = total + total_b
			end
			return spell.get_damage(total,target,source,1) 
		end,
	},
}

spell.Leona = {
	Q = {
		prediction = {
			type = 'InRange',
			range = 125 ,
			radius = 125 ,
			delay = 0.00,
			dashRadius = 0,
			boundingRadiusModSource = 1,
			boundingRadiusModTarget = 1,
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_Q),
		ignore_obj_radius = 1200,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			local dmg = {10,35,60 ,85 ,110}
			local qlevel = source:spellSlot(0).level
			local QDmg = dmg[qlevel] or 0;
			local ap = source.totalAp * .3
			local totalQDmg = QDmg + ap
			local total =  common.CalculateMagicDamage(target,totalQDmg,source) 
			return spell.get_damage(total,target,source,1) 
		end,
	},
	
	W = {
		prediction = {
			type = 'InRange',
			range = 450 ,
			radius = 450 ,
			delay = 0.00,
			dashRadius = 0,
			boundingRadiusModSource = 0,
			boundingRadiusModTarget = 0,
		},
		target_selector = 8,
		slot = player:spellSlot(_W),
		ignore_obj_radius = 0,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			local dmg = {50,90,130,170,210}
			local wlevel = source:spellSlot(1).level
			local WDmg = dmg[wlevel] or 0;
			local ap = source.totalAp * .4
			local totalQDmg = WDmg + ap
			local total =  common.CalculateMagicDamage(target,totalQDmg,source) 
			return spell.get_damage(total,target,source,1) 
		end,
	},
	E = {
		prediction = {
			type = 'Linear',
			range = 900,
			delay = 0.25,
			speed = 2000,
			width = 70,
			boundingRadiusMod = 1,
			collision = {
			  hero = false, --allow to hit other heros :-)
			  minion = false,
			  wall = false,
			},
			--
			hitchance = 0,
			out_range = 900,
			new_path = 0.500,
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_E),
		ignore_obj_radius = 0,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			local dmg = {50,90,130,170,210}
			local elevel = source:spellSlot(2).level
			local EDmg = dmg[elevel] or 0;
			local ap = source.totalAp * .4
			local totalQDmg = EDmg + ap
			local total =  common.CalculateMagicDamage(target,totalQDmg,source) 
			return spell.get_damage(total,target,source,1) 
		end,
	},
	R = {
		prediction = {
			type = 'Circular',
			range = 1200,
			delay = 1,
			speed = math.huge,
			radius = 80,
			boundingRadiusMod = 1,
			collision = {
			  hero = true,
			  minion = true,
			  wall = true,
			},
			hitchance = 0,
			out_range = 1200,
			new_path = 0.500,
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_R),
		ignore_obj_radius = 1200,
		damage = function(target,source,stacks)
			if not target then return 0 end
			local source = source or player
			local dmg = {100,175,250}
			local rlevel = source:spellSlot(3).level
			local RDmg = dmg[rlevel] or 0;
			local ap = source.totalAp * .8
			local totalQDmg = RDmg + ap
			local total_a =  common.CalculateMagicDamage(target,totalQDmg,source) 
			return spell.get_damage(total_a,target,source,1) 
		end,
	},
}

spell.Nami = {
	Q = {
		prediction = {
			type = 'Circular',
			range = 850,
			delay = 1.25,
			speed = math.huge,
			radius = 50,
			boundingRadiusMod = 1,
			collision = {
			  hero = false, --allow to hit other heros :-)
			  minion = false,
			  wall = false,
			},
			--
			hitchance = 0,
			out_range = 900,
			new_path = 0.500,
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_Q),
		ignore_obj_radius = 1200,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			local dmg = {75,130,185,240,295}
			local qlevel = source:spellSlot(0).level
			local QDmg = dmg[qlevel] or 0;
			local ap = source.totalAp * .5
			local totalQDmg = QDmg + ap
			local total =  common.CalculateMagicDamage(target,totalQDmg,source) 
			return spell.get_damage(total,target,source,1) 
		end,
	},
	
	W = {
		prediction = {
			type = 'InRange',
			range = 725 ,
			radius = 725 ,
			delay = 0.00,
			dashRadius = 0,
			boundingRadiusModSource = 0,
			boundingRadiusModTarget = 0,
		},
		target_selector = 8,
		slot = player:spellSlot(_W),
		ignore_obj_radius = 0,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			local dmg = {70 ,110,150,190,230}
			local wlevel = source:spellSlot(1).level
			local WDmg = dmg[wlevel] or 0;
			local ap = source.totalAp * .5
			local totalQDmg = WDmg + ap
			local total =  common.CalculateMagicDamage(target,totalQDmg,source) 
			return spell.get_damage(total,target,source,1) 
		end,
	},
	E = {
		prediction = {
			type = 'InRange',
			range = 800 ,
			radius = 800 ,
			delay = 0.00,
			dashRadius = 0,
			boundingRadiusModSource = 0,
			boundingRadiusModTarget = 0,
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_E),
		ignore_obj_radius = 0,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			local dmg = {25,40,55,70,85}
			local elevel = source:spellSlot(2).level
			local EDmg = dmg[elevel] or 0;
			local ap = source.totalAp * .2
			local totalQDmg = EDmg + ap
			local total =  common.CalculateMagicDamage(target,totalQDmg,source) 
			return spell.get_damage(total,target,source,1) 
		end,
	},
	R = {
		prediction = {
			type = 'Linear',
			range = 2500,
			delay = 0.5,
			speed = 850,
			width = 250,
			boundingRadiusMod = 1,
			collision = {
			  hero = true,
			  minion = true,
			  wall = true,
			},
			hitchance = 0,
			out_range = 1200,
			new_path = 0.500,
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_R),
		ignore_obj_radius = 1200,
		damage = function(target,source,stacks)
			if not target then return 0 end
			local source = source or player
			local dmg = {150,250,350}
			local rlevel = source:spellSlot(3).level
			local RDmg = dmg[rlevel] or 0;
			local ap = source.totalAp * .6
			local totalQDmg = RDmg + ap
			local total_a =  common.CalculateMagicDamage(target,totalQDmg,source) 
			return spell.get_damage(total_a,target,source,1) 
		end,
	},
}
spell.Teemo = {
	Q = {
		prediction = {
			type = 'InRange',
			range = 680 ,
			radius = 680 ,
			delay = 0.00,
			dashRadius = 0,
			boundingRadiusModSource = 0,
			boundingRadiusModTarget = 0,
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_Q),
		ignore_obj_radius = 1200,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			local dmg = {80,125,170,215,260}
			local qlevel = source:spellSlot(0).level
			local QDmg = dmg[qlevel] or 0;
			local ap = source.totalAp * .8
			local totalQDmg = QDmg + ap
			local total =  common.CalculateMagicDamage(target,totalQDmg,source) 
			return spell.get_damage(total,target,source,1) 
		end,
	},
	
	W = {
		prediction = {
			type = 'InRange',
			range = 0 ,
			radius = 0 ,
			delay = 0.00,
			dashRadius = 0,
			boundingRadiusModSource = 0,
			boundingRadiusModTarget = 0,
		},
		target_selector = 8,
		slot = player:spellSlot(_W),
		ignore_obj_radius = 0,
		damage = function(target,source)
			if not target then return 0 end
			return 0
		end,
	},
	E = {
		prediction = {
			type = 'InRange',
			range =   0,
			radius = 0 ,
			delay = 0.00,
			dashRadius = 0,
			boundingRadiusModSource = 0,
			boundingRadiusModTarget = 0,
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_E),
		ignore_obj_radius = 0,
		damage = function(target,source)
			if not target then return 0 end
			return 0
		end,
	},
	R = {
		prediction = {
			type = 'Circular',
			range = 400,
			delay = 0.5,
			speed = math.huge,
			radius = 75,
			boundingRadiusMod = 1,
			collision = {
			  hero = true,
			  minion = true,
			  wall = true,
			},
			hitchance = 0,
			out_range = 900,
			new_path = 0.500,
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_R),
		ignore_obj_radius = 400,
		damage = function(target,source,stacks)
			if not target then return 0 end
			local source = source or player
			local dmg = {50,81.25,112.5}
			local rlevel = source:spellSlot(3).level
			local RDmg = dmg[rlevel] or 0;
			local ap = source.totalAp * .125
			local totalQDmg = RDmg + ap
			local total_a =  common.CalculateMagicDamage(target,totalQDmg,source) 
			return spell.get_damage(total_a * 4,target,source,1)  
		end,
	},
}
spell.Ezreal = {
	Q = {
		prediction = {
			type = 'Linear',
			range = 1150 ,
			delay = 0.25,
			width = 60,
			speed = 2000,
			boundingRadiusMod = 1,
			collision = {
			  hero = false,
			  minion = true,
			},
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_Q),
		ignore_obj_radius = 1150,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			local dmg = {20,45,70,95,120}
			local qlevel = source:spellSlot(0).level
			local QDmg = dmg[qlevel] or 0;
			local ap = source.totalAp * .15
			local ad = source.totalAd * 1.3
			local totalQDmg = QDmg + ap + ad
			local total =  common.CalculatePhysicalDamage(target,totalQDmg,source) 
			return spell.get_damage(total,target,source,2) 
		end,
	},
	
	W = {
		prediction = {
			type = 'Linear',
			range = 1150,
			delay = 0.25,
			width = 80,
			speed = 1600,
			boundingRadiusMod = 1,
		},
		target_selector = 8,
		slot = player:spellSlot(_W),
		ignore_obj_radius = 0,
		damage = function(target,source)
			if not target then return 0 end
			return 0
		end,
	},
	E = {
		prediction = {
			type = 'InRange',
			range =   0,
			radius = 0 ,
			delay = 0.00,
			dashRadius = 0,
			boundingRadiusModSource = 0,
			boundingRadiusModTarget = 0,
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_E),
		ignore_obj_radius = 0,
		damage = function(target,source)
			if not target then return 0 end
			return 0
		end,
	},
	R = {
		prediction = {
			type = 'Linear',
			range = 9999,
			delay = 1.00,
			speed = 2000,
			width = 160,
			boundingRadiusMod = 1,
			collision = {
			  hero = true,
			  minion = true,
			  wall = true,
			},
			hitchance = 0,
			out_range = 9999,
			new_path = 0.500,
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_R),
		ignore_obj_radius = 9999,
		damage = function(target,source,stacks)
			if not target then return 0 end
			local source = source or player
			local dmg = {350,500,650}
			local rlevel = source:spellSlot(3).level
			local RDmg = dmg[rlevel] or 0;
			local ad = common.GetBonusAD(source)
			local ap = source.totalAd * .9
			local totalQDmg = RDmg + ap + ad
			local total =  common.CalculatePhysicalDamage(target,totalQDmg,source) 
			return spell.get_damage(total,target,source,2) 
		end,
	},
}

spell.Ahri = {
	Q = {
		prediction = {
			type = 'Linear',
			range = 900 ,
			delay = 0.25,
			width = 100,
			speed = 1550,
			boundingRadiusMod = 0,
			collision = {
			  hero = false,
			  minion = false,
			},
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_Q),
		ignore_obj_radius = 900,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			local dmg = {40,65,90,115,140}
			local qlevel = source:spellSlot(0).level
			local QDmg = dmg[qlevel] or 0;
			local ap = source.totalAp * .35
			local totalQDmg = QDmg + ap
			local total =  common.CalculateMagicDamage(target,totalQDmg,source) 
			return spell.get_damage(total,target,source,1)
		end,
	},
	W = {
		prediction = {
			type = 'InRange',
			range = 550,
			radius = 550,
			delay = 0.00,
			dashRadius = 0,
			boundingRadiusModSource = 0,
			boundingRadiusModTarget = 0,
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_W),
		ignore_obj_radius = 550,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			local dmg = {40,65,90,115,140}
			local qlevel = source:spellSlot(0).level
			local QDmg = dmg[qlevel] or 0;
			local ap = source.totalAp * .3
			local totalQDmg = QDmg + ap
			local total =  common.CalculateMagicDamage(target,totalQDmg,source) 
			return spell.get_damage(total,target,source,1)
		end,
	},
	E = {
		prediction = {
			type = 'Linear',
			range = 1000 ,
			delay = 0.25,
			width = 100,
			speed = 1550,
			boundingRadiusMod = 0,
			collision = {
			  hero = false,
			  minion = false,
			},
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_E),
		ignore_obj_radius = 550,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			local dmg = {72,108,144,180,216}
			local qlevel = source:spellSlot(0).level
			local QDmg = dmg[qlevel] or 0;
			local ap = source.totalAp * .48
			local totalQDmg = QDmg + ap
			local total =  common.CalculateMagicDamage(target,totalQDmg,source) 
			return spell.get_damage(total,target,source,1)
		end,
	},
	R = {
		prediction = {
			type = 'InRange',
			range = 550,
			radius = 550,
			delay = 0.00,
			dashRadius = 0,
			boundingRadiusModSource = 0,
			boundingRadiusModTarget = 0,
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_R),
		ignore_obj_radius = 550,
		damage = function(target,source)
			if not target then return 0 end
			return 0
		end,
	},

}

spell.Qiyana = {
	Q = {
		prediction = {
			type = 'Linear',
			range = 525,
			delay = 0.25,
			width = 100,
			speed = 1600,
			boundingRadiusMod = 0,
			collision = {
			  hero = true,
			  minion = true,
			},
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_Q),
		ignore_obj_radius = 525,
		damage = function(target,source,check)
			if not target then return 0 end
			local source = source or player
			local dmg = {50,80,110,140,170}
			local qlevel = source:spellSlot(0).level
			local QDmg = dmg[qlevel] or 0;
			local ad = common.GetBonusAD(source) * .75
			local totalQDmg = QDmg + ad
			if source:spellSlot(_Q).name:find("QiyanaQ_Rock") or check then
				if common.GetPercentHealth(target) < 50 then
					local QBoud = {30 , 48 , 66 , 84 , 102}
					local Qdamage = QBoud[qlevel] or 0
					Qdamage = Qdamage +  (common.GetBonusAD() * .45)
					totalQDmg = totalQDmg + Qdamage
				end
			end
			local total =  common.CalculatePhysicalDamage(target,totalQDmg,source) 
			return spell.get_damage(total,target,source,2)
		end,
	},
	W = {
		prediction = {
			type = 'InRange',
			range = 1100,
			radius = 1100,
			delay = 0.00,
			dashRadius = 0,
			boundingRadiusModSource = 0,
			boundingRadiusModTarget = 0,
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_W),
		ignore_obj_radius = 1100,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			local dmg = {8,22,36,50,64}
			local wlevel = source:spellSlot(1).level
			local QDmg = dmg[wlevel] or 0;
			local ad = common.GetBonusAD(source) * .1
			local ap = source.totalAp * .45
			local totalQDmg = QDmg + ap + ad
			local total =  common.CalculateMagicDamage(target,totalQDmg,source) 
			return spell.get_damage(total,target,source,1)
		end,
	},
	E = {
		prediction = {
			type = 'InRange',
			range = 650,
			radius = 650,
			delay = 0.00,
			dashRadius = 0,
			boundingRadiusModSource = 0,
			boundingRadiusModTarget = 0,
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_E),
		ignore_obj_radius = 650,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			local dmg = {50,90,130,170,210}
			local elevel = source:spellSlot(2).level
			local QDmg = dmg[elevel] or 0;
			local ad = common.GetBonusAD(source) * .5
			local totalQDmg = QDmg + ad
			local total =  common.CalculatePhysicalDamage(target,totalQDmg,source) 
			return spell.get_damage(total,target,source,2)
		end,
	},
	R = {
		prediction = {
			type = 'Linear',
			range = 875,
			delay = 0.25,
			width = 140,
			speed = 2000,
			boundingRadiusMod = 0,
			collision = {
			  hero = false,
			  minion = false,
			},
		},
		target_selector = 8,
		cast_spell = {},
		slot = player:spellSlot(_R),
		ignore_obj_radius = 875,
		damage = function(target,source)
			if not target then return 0 end
			local source = source or player
			local dmg = {100,200,300}
			local dmg_2 = {500,750,1000}
			local rlevel = source:spellSlot(3).level
			local RDmg = dmg[rlevel] or 0;
			local RDmg2 = dmg_2[rlevel] or 0;
			local ad = common.GetBonusAD(source) * 1.7
			local health_dmg = (math.min(RDmg2,((target.maxHealth) * .1) ))
			local totalDmg = RDmg + ad + health_dmg
			local total =  common.CalculatePhysicalDamage(target,totalDmg,source) 
			return spell.get_damage(total,target,source,2)
		end,
	},

}

return spell
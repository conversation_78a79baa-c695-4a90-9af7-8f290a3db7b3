local pred = module.internal("pred")
local ts = module.internal('TS')
local orb = module.internal("orb")
local evade = module.seek("evade")

local lvxbot = module.load(header.id, 'lvxbot/main')
local Curses = module.load("<PERSON>", "<PERSON><PERSON>");
math.randomseed(os.time())
 local menu = nil
 local test = {

}
local x1 = nil

local qcast = lvxbot.load('q')
--local wcast = lvxbot.load('w')

local rcast = lvxbot.load('r')

local q = { MaxRange = 1600, MinRange = 975, QCharge = false, TimeQ = 0 }






local function CheckBuffType(obj, bufftype)
    if obj then
        for i = 0, obj.buffManager.count - 1 do
            local buff = obj.buffManager:get(i)
            if buff and buff.valid and buff.type == bufftype and (buff.stacks > 0 or buff.stacks2 > 0) then
                return true
            end 
        end 
    end   
end
--
local function CheckBuff(obj, buffname)
    if obj then
        for i = 0, obj.buffManager.count - 1 do
            local buff = obj.buffManager:get(i)
            if buff and buff.valid and string.lower(buff.name) == string.lower(buffname) and buff.owner == obj then
                if game.time <= buff.endTime then
                    return true, buff.startTime
                end 
            end 
        end 
    end 
    return false, 0
end 
--
local function IsValidTarget(object)
    return (object and not object.isDead and object.isVisible and object.isTargetable and not CheckBuffType(object, 18))
end
--
local function ValidTargetRange(unit, range)
    return unit and unit.isVisible and not unit.isDead and unit.isTargetable and not CheckBuffType(unit, 17) and (not range or player.pos:dist(unit.pos) <= range)
end
--
local function GetAARange(target)
    return player.attackRange + player.boundingRadius + (target and target.boundingRadius or 0)
end
--
local function GetTotalAP(obj)
    local obj = obj or player
    return obj.flatMagicDamageMod * obj.percentMagicDamageMod
end

--
local function GetPercentHealth(obj)
    local obj = obj or player
    return (obj.health / obj.maxHealth) * 100
end
--


local function CountEnemyChampAroundObject(pos, range) 
	local enemies_in_range = {}
	for i = 0, objManager.enemies_n - 1 do
		local enemy = objManager.enemies[i]
		if pos:dist(enemy.pos) < range and IsValidTarget(enemy) then
			enemies_in_range[#enemies_in_range + 1] = enemy
		end
	end
	return enemies_in_range
end
--
local function CountAllyChampAroundObject(pos, range) 
	local aleds_in_range = {}
	for i = 0, objManager.allies_n - 1 do
		local aled = objManager.allies[i]
		if pos:dist(aled.pos) < range and IsValidTarget(aled) then
			aleds_in_range[#aleds_in_range + 1] = aled
		end
	end
	return aleds_in_range
end
--

--
local function ValidUlt(unit)
	if (CheckBuffType(unit, 16) or CheckBuffType(unit, 15) or CheckBuffType(unit, 17) or CheckBuff(unit, "kindredrnodeathbuff") or CheckBuffType(unit, 4)) then
		return false
	end
	return true
end

local function IsImmobileTarget(unit)
	if (CheckBuffType(unit, 5) or CheckBuffType(unit, 11) or CheckBuffType(unit, 29) or CheckBuffType(unit, 24) or CheckBuffType(unit, 10) or CheckBuffType(unit, 29))  then
		return true
	end
	return false
end
--
local function IsLogicE(target)
	return player.path.serverPos:distSqr(target.path.serverPos) > player.path.serverPos:distSqr(target.path.serverPos + target.direction)
end


local function IsReady(spell)
    return player:spellSlot(spell).state == 0
end 

local function MagicReduction(target, damageSource)
    local damageSource = damageSource or player
	--print((target.spellBlock * damageSource.percentMagicPenetration) - damageSource.flatMagicPenetration)
    local magicResist = (target.spellBlock * damageSource.percentMagicPenetration) - damageSource.flatMagicPenetration
    return magicResist >= 0 and (100 / (100 + magicResist)) or (2 - (100 / (100 - magicResist)))
  end
  
  local function DamageReduction(damageType, target, damageSource)
    local damageSource = damageSource or player
    local reduction = 1
    if damageType == "AD" then
    end
    if damageType == "AP" then
    end
    return reduction
  end
  
--
local function CalculateMagicDamage(target, damage, damageSource)
    local damageSource = damageSource or player
    if target then
      return (damage * MagicReduction(target, damageSource)) * DamageReduction("AP", target, damageSource)
    end
    return 0
  end




local TargetSelectionQ = function(res, obj, dist)
	if dist < 1500 then
		res.obj = obj
		return true
	end
end
local GetTargetQ = function()
	return ts.get_result(TargetSelectionQ).obj
end


local function QRange(time)
	local RangeQ = q.MaxRange - q.MinRange
	local MinRangeSpell = q.MinRange
	local AlcanceLocal = RangeQ / 1.5 * time + MinRangeSpell
    if AlcanceLocal > q.MaxRange then 
        AlcanceLocal = q.MaxRange 
    end
	return AlcanceLocal
end


local function Findtre()
	local closestMinion = nil;
	for i = 0, objManager.enemies_n - 1 do
		local enemy = objManager.enemies[i]
		
			local distToMinion= player.pos:dist(enemy.pos);
			if distToMinion < 1500 then
				if (not closestMinion) then
					closestMinion = enemy; -- first minion
				end
				if (distToMinion > player.pos:dist(closestMinion.pos)) then
					
					closestMinion = enemy;
				end
			end
	
		
		
	end
	
	--print('returning ' + closestMinion)
	
	if closestMinion then
		
		return player.pos:dist(closestMinion.pos);
	else
		--print("meiyou")
		return false
	end
	
	
	
end


function GetPercentHealth(obj)
  local obj = obj or player
  return (obj.health / obj.maxHealth) * 100
end

local easy_q = function()
 if menu.combat:get() or orb.menu.hybrid.key:get()  then
 
 
   local target = ts.get_result(function(res, obj, dist)
    if dist > 1500 then
      return
    end

        res.obj = obj
        return true
      
    
  end, ts.filter_set[1]).obj 
		if orb.core.can_action() then
			
      
          
            if orb.core.can_cast_spell(0) and target and q.QCharge == false then
		--	print("111")
			 if GetPercentHealth (target) <= 50 then
			 if orb.core.can_cast_spell(1) then
			  player:castSpell("self", 1)
			  end
			 
			 end
			 if orb.combat.target then
			 if  orb.combat.target.buff and orb.combat.target.buff["varuswdebuff"] and orb.combat.target.buff["varuswdebuff"].stacks ==3  then
                player:castSpell("pos", 0, player.pos)
				end
				else
			   player:castSpell("pos", 0, player.pos)
		     end
				
            end  
           

		if q.QCharge == true then
	   --print("QQQ")
		local TempoCang = game.time - q.TimeQ
        local range = QRange(TempoCang)
		qcast.input.prediction.range = range
		
		
		
		
		if #CountEnemyChampAroundObject(player.pos, 1500) >= 2  then
		
	     if  range > Findtre() then
		  qcast.easy_execute()
		  end
		 else
		 qcast.easy_execute()
         end
        end 
    end

		
		end
  end


local easy_w = function()
 if menu.combat:get() then
   wcast.easy_execute()
  end
end



local easy_r = function()
  if menu.combat:get() then
   rcast.easy_execute()
  end
end





local function EGapcloser()
       
	if  orb.core.can_cast_spell(2) then
	         
			  
		for i = 0, objManager.enemies_n - 1 do
			local IsDashingPlayer = objManager.enemies[i]
			 
			if IsDashingPlayer.type == TYPE_HERO and IsDashingPlayer.team == TEAM_ENEMY then
				if IsDashingPlayer and IsValidTarget(IsDashingPlayer) and IsDashingPlayer.path.isActive and IsDashingPlayer.path.isDashing and player.pos:dist(IsDashingPlayer.path.point[1]) < 650 then
					if player.pos2D:dist(IsDashingPlayer.path.point2D[1]) < player.pos2D:dist(IsDashingPlayer.path.point2D[0]) then
						if ((player.health / player.maxHealth) * 100 <= 100) then
							player:castSpell("pos", 2, IsDashingPlayer.path.point[1])
						end
					end
				end
			end
		end
		else
		if  orb.core.can_cast_spell(3)then
				for i = 0, objManager.enemies_n - 1 do
			local IsDashingPlayer = objManager.enemies[i]
			 
			if IsDashingPlayer.type == TYPE_HERO and IsDashingPlayer.team == TEAM_ENEMY then
				if IsDashingPlayer and IsValidTarget(IsDashingPlayer) and IsDashingPlayer.path.isActive and IsDashingPlayer.path.isDashing and player.pos:dist(IsDashingPlayer.path.point[1]) < 650 then
					if player.pos2D:dist(IsDashingPlayer.path.point2D[1]) < player.pos2D:dist(IsDashingPlayer.path.point2D[0]) then
						if ((player.health / player.maxHealth) * 100 <= 100) then
						  
						  	
							local seg = {}
							local pred_pos = pred.core.lerp(IsDashingPlayer.path, network.latency + 0.25, IsDashingPlayer.path.dashSpeed)
							  if pred_pos and pred_pos:dist(player.path.serverPos2D) <= 750 then
				                seg.startPos = player.path.serverPos2D
				                seg.endPos = vec2(pred_pos.x, pred_pos.y)
						   
							  -- player:castSpell("pos", 2, IsDashingPlayer.path.serverPos)
							  player:castSpell("pos", 3, vec3(pred_pos.x, IsDashingPlayer.y, pred_pos.y))
							  end
							
							
						
						
					   
							
						end
					end
				end
			end
		end
		end
		
	end
end





local function UpBuff()
    if CheckBuff(player, "VarusQ") then
        q.QCharge = true
    else
        q.QCharge = false
    end 

end 
--
local function CheckQ()
    if q.QCharge == true then
	
        orb.core.set_pause_attack(math.huge)


	
    else 

      
        
         orb.core.set_pause_attack(0)
		 
		 
		 
		
    end 
end 


local function UltMultiple()
	ultcount = 3
	if player:spellSlot(3).state == 0  then
		for i=0, objManager.enemies_n - 1 do
			enemy = objManager.enemies[i]
			hit = 0
			for j=0, objManager.enemies_n - 1 do
				near = objManager.enemies[j]
				if IsValidTarget(near) and enemy.pos:dist(near.pos) <= 500 then
					hit = hit + 1
				end
			end
			if hit >= ultcount then
				easy_r()
			end
		end
	end
end


local function OnTick()
UpBuff()
CheckQ()
EGapcloser()

	 local buff, time = CheckBuff(player, "VarusQ")
	if buff then
		q.TimeQ = time
    end
	easy_q()
	--easy_e()
	if menu.combat:get() then
	local ter = orb.combat.target
	if ter and ter.buff["varuswdebuff"] and ter.buff["varuswdebuff"].stacks== 3  then
	if orb.core.can_cast_spell(2) then
	local res = pred.core.get_pos_after_time(ter, ter.moveSpeed / 750)
		 
		player:castSpell('pos', 2, vec3(res.x,0,res.y))
	
	--player:castSpell("pos", 2, ter.pos)
	end
	end
	end
	
	
	UltMultiple()
	if menu.CastR:get() then
	rcast.easy_execute()
	end
end







local function OnDraw()
    if IsValidTarget(player) then
        if player.isVisible and player.isOnScreen and not player.isDead then
           -- if IsReady(0)  then
		  
                local TempoCang = game.time - q.TimeQ
                local range = QRange(TempoCang)
				--if range==q.MaxRange then
                graphics.draw_circle(player.pos, range, 2, 0x6fffffff, 100)
				end
				--end
                
				
				
            
		 if player:spellSlot(3).state == 0 then
		graphics.draw_circle(player.pos, 1000, 3, 0x6fffffff, 100)
		end
   
        --end

		
		
		
		

 
    end 
end 


 menu = lvxbot.load('menu')
cb.add(cb.draw, OnDraw)

cb.add(cb.tick, OnTick)

 






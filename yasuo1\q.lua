local lvxbot = module.load(header.id, 'lvxbot/main')


function GetPreciseDecimal(nNum, n)
    if type(nNum) ~= "number" then
        return nNum;
    end
    n = n or 0;
    n = math.floor(n)
    if n < 0 then
        n = 0;
    end
    local nDecimal = 1/(10 ^ n)
    if nDecimal == 1 then
        nDecimal = nNum;
    end
    local nLeft = nNum % nDecimal;
    return nNum - nLeft;
end


local input = {
  prediction = {
    type = 'Linear',
    --
    range = 475,
	
	
    delay = GetPreciseDecimal(0.4 * (1 -  math.min((player.attackSpeedMod - 1) * 0.58, 0.66)),2),
	
    speed = math.huge,
    width = 30,
    boundingRadiusMod = 1,
	predslot = 1,
    --
    collision = {
      hero = false, --allow to hit other heros :-)
      minion = false,
      wall = false,
    },
    --
    hitchance = 0,
  },

  target_selector = {
    type = 'LESS_CAST_AD',
  },

  cast_spell = {
    type = 'pos',
    slot = _Q,
    arg1 = function(action)
      local seg = action.ts_result.seg
      local obj = action.ts_result.obj
      return seg.endPos:to3D(obj.y)
    end,
  },

  slot = _Q,
  ignore_obj_radius = 2000,

}
--print(input.prediction.delay,"123")
return lvxbot.yasuoexpert.create(input)


-- "----------------------------------------------------------------------------------------------------------"
-- "--------------------------------------------    By : Joker    --------------------------------------------"
-- "----------------------------------------------------------------------------------------------------------"
-- "                                                                                                          "
-- "      jjjjjjjjjjjjjjjj   kkkk      kkkk     ssssssss         nnn            nnn      bbbbbbbbbbbbbbb      "
-- "            jjjj         kkkk      kk     ssss    ssss       nnnnnn         nnn      bbb         bbbbbb   "
-- "            jjjj         kkkk    kkkk     ss          ss     nnnnnn         nnn      bbb            bbb   "
-- "            jjjj         kkkk  kkkk       ss                 nnnnnnnnn      nnn      bbb            bbb   "
-- "            jjjj         kkkkkkkk           ss               nnn   nnn      nnn      bbb         bbbbbb   "
-- "            jjjj         kkkkkkkkkk           sssss          nnn   nnn      nnn      bbbbbbbbbbbbbbb      "
-- "  jjjj      jjjj         kkkk    kk      ss        sssss     nnn      nnn   nnn      bbb         bbbbbb   "
-- " jjj        jjjj         kkkk    kkkk     ss          ss     nnn      nnn   nnn      bbb            bbb   "
-- " jjj       jjjj          kkkk      kk     ssss      ssss     nnn         nnnnnn      bbb            bbb   "
-- "  jjjj   jjjj            kkkk      kkk      ss      ss       nnn         nnnnnn      bbb         bbbbbb   "
-- "    jjjjjj               kkkk      kkkk       sssss          nnn         nnnnnn      bbbbbbbbbbbbbbb      "
-- "                                                                                                          "
-- "----------------------------------------------------------------------------------------------------------"
-- "--------------------------------------------    By : Joker    --------------------------------------------"
-- "----------------------------------------------------------------------------------------------------------"
local ID = header.id
local Curses = module.load("Brian", "Curses");
local GZ = module.load(ID, "lib")
local spells = module.load(ID, "Utility/spelldata")
local orb = module.internal("orb")
local pred = module.internal("pred")
local evade = module.seek("evade")
local TS = module.load(ID, "TS/TargetSelector")
module.load(header.id, 'TS/TargetSelector').addToMenu() --目锟斤拷选锟斤拷锟斤拷
--local preshow = module.load(ID, "PreShow/Show")
local jkUtility = module.load(ID, "Libdata/Utility")
local ts = module.internal('TS')
local ObjMinion_Type = objManager.minions

local EPrediction = { -- range is 1050
  width = 130,
  delay = 0.25,
  speed = 1400,
  boundingRadiusMod = 0
}

local RPrediction = { -- range is global
  width = 160,
  delay = 0.4,
  speed = 2000,
  boundingRadiusMod = 1
}

local interruptableSpells = {
	["anivia"] = {
		{menuslot = "R", slot = 3, spellname = "glacialstorm", channelduration = 6, CNname = "锟斤拷锟斤拷锟斤拷锟?"}
	},
	["caitlyn"] = {
		{menuslot = "R", slot = 3, spellname = "caitlynaceinthehole", channelduration = 1, CNname = "皮锟斤拷女锟斤拷"}
	},
	["ezreal"] = {
		{menuslot = "R", slot = 3, spellname = "ezrealtrueshotbarrage", channelduration = 1, CNname = "探锟秸硷拷"}
	},
	["fiddlesticks"] = {
		{menuslot = "W", slot = 1, spellname = "drain", channelduration = 5, CNname = "远锟脚恐撅拷"},
		{menuslot = "R", slot = 3, spellname = "crowstorm", channelduration = 1.5, CNname = "远锟脚恐撅拷"}
	},
	["gragas"] = {
		{menuslot = "W", slot = 1, spellname = "gragasw", channelduration = 0.75, CNname = "锟斤拷桶"}
	},
	["janna"] = {
		{menuslot = "R", slot = 3, spellname = "reapthewhirlwind", channelduration = 3, CNname = "锟界暴之女"}
	},
	["karthus"] = {
		{menuslot = "R", slot = 3, spellname = "karthusfallenone", channelduration = 3, CNname = "锟斤拷锟斤拷锟教筹拷锟斤拷"}
	}, 
	["katarina"] = {
		{menuslot = "R", slot = 3, spellname = "katarinar", channelduration = 2.5, CNname = "锟斤拷锟斤拷之锟斤拷"}
	},
	["lucian"] = {
		{menuslot = "R", slot = 3, spellname = "lucianr", channelduration = 2, CNname = "圣枪锟斤拷锟斤拷"}
	},
	["lux"] = {
		{menuslot = "R", slot = 3, spellname = "luxmalicecannon", channelduration = 0.5, CNname = "锟斤拷锟脚?锟斤拷"}
	},
	["malzahar"] = {
		{menuslot = "R", slot = 3, spellname = "malzaharr", channelduration = 2.5, CNname = "锟斤拷锟斤拷锟街?"}
	},
	["masteryi"] = {
		{menuslot = "W", slot = 1, spellname = "meditate", channelduration = 4, CNname = "锟睫硷拷锟斤拷圣"}
	},
	["missfortune"] = {
		{menuslot = "R", slot = 3, spellname = "missfortunebullettime", channelduration = 3, CNname = "锟酵斤拷锟斤拷锟斤拷"}
	},
	["nunu"] = {
		{menuslot = "R", slot = 3, spellname = "absolutezero", channelduration = 3, CNname = "雪原双锟斤拷"}
	},
	["pantheon"] = {
		{menuslot = "R", slot = 3, spellname = "pantheonrjump", channelduration = 2, CNname = "锟斤拷锟斤拷之枪"}
	},
	["shen"] = {
		{menuslot = "R", slot = 3, spellname = "shenr", channelduration = 3, CNname = "暮锟斤拷之锟斤拷"}
	},
	["twistedfate"] = {
		{menuslot = "R", slot = 3, spellname = "gate", channelduration = 1.5, CNname = "锟斤拷锟狡达拷师"}
	},
	["varus"] = {
		{menuslot = "Q", slot = 0, spellname = "varusq", channelduration = 4, CNname = "锟酵斤拷之锟斤拷"}
	},
	["warwick"] = {
		{menuslot = "R", slot = 3, spellname = "warwickr", channelduration = 1.5, CNname = "锟芥安怒锟斤拷"}
	},
	["xerath"] = {
		{menuslot = "R", slot = 3, spellname = "xerathlocusofpower2", channelduration = 3, CNname = "远锟斤拷锟斤拷锟斤拷"}
	}
}



local AxePositions = {}
local LatestAxeCreateTick = 0
local Qpause = 0

local function GetAARange(target)
    return player.attackRange + player.boundingRadius + (target and target.boundingRadius or 0)
end

local function CountAllyChampAroundObject(pos, range)
	local aleds_in_range = {}
	for i = 0, objManager.allies_n - 1 do
		local aled = objManager.allies[i]
		if pos:dist(aled.pos) < range and IsValidTarget(aled) then
			aleds_in_range[#aleds_in_range + 1] = aled
		end
	end
	return #aleds_in_range
end

local function CheckBuffType(obj, bufftype)
    if obj then
        for i = 0, obj.buffManager.count - 1 do
            local buff = obj.buffManager:get(i)
            if buff and buff.valid and buff.type == bufftype and (buff.stacks > 0 or buff.stacks2 > 0) then
                return true
            end 
        end 
    end   
end
--
local function CheckBuff(obj, buffname)
    if obj then
        for i = 0, obj.buffManager.count - 1 do
            local buff = obj.buffManager:get(i)
            if buff and buff.valid and string.lower(buff.name) == string.lower(buffname) and buff.owner == obj then
                if game.time <= buff.endTime then
                    return true, buff.startTime
                end 
            end 
        end 
    end 
    return false, 0
end 
--
local function IsValidTarget(object)
    return (object and not object.isDead and object.isVisible and object.isTargetable and not CheckBuffType(object, 17))
end

-- dvvv完全一样的实现 - 开始

-- R技能释放时间记录 - 与222222完全一致，初始化为0允许立即释放
local DravenRCastTime = 0

-- 检查R技能是否已释放 (ove_0_63)
local function IsRCasted()
    if player:spellSlot(3).name == "DravenRCast" then
        return true
    else
        return false
    end
end

-- Q技能增强平A伤害计算 (ove_0_64) - 完全一样
local function DamageQ_DVV(target)
    local slot_10_0 = (player.baseAttackDamage + player.flatPhysicalDamageMod) * player.percentPhysicalDamageMod
    local slot_10_1 = {40, 45, 50, 55, 60}
    local slot_10_2 = {0.7, 0.8, 0.9, 1, 1.1}
    local slot_10_3 = math.max(0, (target.armor - target.bonusArmor + target.bonusArmor * player.percentBonusArmorPenetration) * player.percentArmorPenetration - player.physicalLethality * (0.6 + 0.4 * player.levelRef / 18))
    local slot_10_4 = 1 - slot_10_3 / (100 + slot_10_3)
    local slot_10_5 = slot_10_0 * slot_10_4
    local slot_10_6 = player:spellSlot(0).level

    if slot_10_6 > 0 and (player.buff.dravenspinningattack and player.buff.dravenspinningattack.stacks > 0 or player:spellSlot(0).state == 0) then
        slot_10_5 = (slot_10_0 + (slot_10_1[slot_10_6] + slot_10_2[slot_10_6] * player.flatPhysicalDamageMod * player.percentPhysicalDamageMod)) * slot_10_4
    end

    return slot_10_5
end

-- E技能伤害计算 (ove_0_65) - 完全一样
local function DamageE_DVV(target)
    if player:spellSlot(2).level == 0 then
        return 0
    end

    local slot_11_0 = {75, 110, 145, 180, 215}
    local slot_11_1 = player:spellSlot(2).level
    local slot_11_2 = player.flatPhysicalDamageMod * player.percentPhysicalDamageMod
    local slot_11_3 = math.max(0, (target.armor - target.bonusArmor + target.bonusArmor * player.percentBonusArmorPenetration) * player.percentArmorPenetration - player.physicalLethality * (0.6 + 0.4 * player.levelRef / 18))
    local slot_11_4 = 1 - slot_11_3 / (100 + slot_11_3)

    return (slot_11_0[slot_11_1] + slot_11_2 * 0.5) * slot_11_4
end

-- R技能伤害计算 (ove_0_66) - 完全一样，不包含翻倍逻辑
local function DamageR_DVV_Base(target)
    if player:spellSlot(3).level == 0 then
        return 0
    end

    local baseDamage = {175, 275, 375}
    local level = player:spellSlot(3).level
    local bonusAD = player.flatPhysicalDamageMod * player.percentPhysicalDamageMod
    local armor = math.max(0, (target.armor - target.bonusArmor + target.bonusArmor * player.percentBonusArmorPenetration) * player.percentArmorPenetration - player.physicalLethality * (0.6 + 0.4 * player.levelRef / 18))
    local armorReduction = 1 - armor / (100 + armor)
    local damage = (baseDamage[level] + bonusAD * 1.5) * armorReduction

    return damage
end

-- R技能伤害计算 - 与222222的ove_0_66完全一致，包含翻倍逻辑
local function DamageR_DVV_WithDouble(target)
    if player:spellSlot(3).level == 0 then
        return 0
    end

    local baseDamage = {175, 275, 375}
    local level = player:spellSlot(3).level
    local bonusAD = player.flatPhysicalDamageMod * player.percentPhysicalDamageMod
    local armor = math.max(0, (target.armor - target.bonusArmor + target.bonusArmor * player.percentBonusArmorPenetration) * player.percentArmorPenetration - player.physicalLethality * (0.6 + 0.4 * player.levelRef / 18))
    local armorReduction = 1 - armor / (100 + armor)
    local damage = (baseDamage[level] + bonusAD * 1.5) * armorReduction

    -- 与222222完全一致：如果是当前目标则翻倍
    if target and orb.combat and orb.combat.target and orb.combat.target == target then
        damage = damage * 2
    end

    return damage
end



-- 目标验证函数 (ove_0_62) - 完全一样
local function IsValidTarget_DVV(target)
    if target and not target.isDead and target.isTargetable and not target.buff.fioraw and not target.buff.sivire and target.isVisible then
        return true
    end
    return false
end

-- ove_0_68 - dvvv完全一样
local function ove_0_68(arg_13_0, arg_13_1, arg_13_2)
    return (arg_13_1.x - arg_13_0.x) * (arg_13_2.y - arg_13_0.y) - (arg_13_2.x - arg_13_0.x) * (arg_13_1.y - arg_13_0.y)
end

-- ove_0_69 - dvvv完全一样
local function ove_0_69(arg_14_0, arg_14_1, arg_14_2, arg_14_3)
    return (ove_0_68(arg_14_0, arg_14_2, arg_14_3) <= 0 and ove_0_68(arg_14_1, arg_14_2, arg_14_3) > 0 or ove_0_68(arg_14_0, arg_14_2, arg_14_3) > 0 and ove_0_68(arg_14_1, arg_14_2, arg_14_3) <= 0) and (ove_0_68(arg_14_0, arg_14_1, arg_14_2) <= 0 and ove_0_68(arg_14_0, arg_14_1, arg_14_3) > 0 or ove_0_68(arg_14_0, arg_14_1, arg_14_2) > 0 and ove_0_68(arg_14_0, arg_14_1, arg_14_3) <= 0)
end

-- ove_0_70 - dvvv完全一样
local function ove_0_70(arg_15_0, arg_15_1)
    local slot_15_0 = vec2(arg_15_0.x, arg_15_0.z)
    local slot_15_1 = arg_15_1:to2D()
    local slot_15_2 = false

    -- 这里需要ove_0_25和ove_0_52的实现，暂时返回false
    return slot_15_2
end

-- ove_0_73 - E技能轨迹检查
local function ove_0_73(arg_16_0, arg_16_1, arg_16_2)
    -- 基本检查：目标在攻击范围内
    if arg_16_2 and IsValidTarget_DVV(arg_16_2) and player.pos:dist(arg_16_2.pos) < player.attackRange + player.boundingRadius + arg_16_2.boundingRadius then
        return true
    end

    -- 距离检查
    if arg_16_1 and arg_16_1.startPos and arg_16_1.endPos and arg_16_1.startPos:dist(arg_16_1.endPos) < 600 then
        return true
    end

    return true -- 默认允许
end

-- ove_0_74 - R技能轨迹检查
local function ove_0_74(arg_17_0, arg_17_1, arg_17_2)
    -- 基本检查：目标在攻击范围内
    if arg_17_2 and IsValidTarget_DVV(arg_17_2) and player.pos:dist(arg_17_2.pos) < player.attackRange + player.boundingRadius + arg_17_2.boundingRadius then
        return true
    end

    -- 距离检查
    if arg_17_1 and arg_17_1.startPos and arg_17_1.endPos and arg_17_1.startPos:dist(arg_17_1.endPos) < 600 then
        return true
    end

    return true -- 默认允许
end

-- R技能预测和命中计算 (ove_0_76) - 完全一样
local function CastR_DVV(target)
    if player:spellSlot(3).state ~= 0 or not target or not IsValidTarget_DVV(target) or target.buff[BUFF_INVULNERABILITY] then
        return false
    end

    local slot_19_0
    local slot_19_1 = 0
    local slot_19_2 = pred.linear.get_prediction(RPrediction, target)

    if slot_19_2 and slot_19_2.endPos and slot_19_2.startPos:distSqr(slot_19_2.endPos) < 9000000 and ove_0_74(RPrediction, slot_19_2, target) and not ove_0_70(player, vec3(slot_19_2.endPos.x, target.y, slot_19_2.endPos.y)) then
        slot_19_0 = vec3(slot_19_2.endPos.x, target.y, slot_19_2.endPos.y)

        if IsRCasted() then
            -- 使用简单可靠的敌人计数方式
            for i = 0, objManager.enemies_n - 1 do
                local enemy = objManager.enemies[i]
                if enemy and IsValidTarget_DVV(enemy) then
                    local distToLine = enemy.pos:dist(slot_19_0)
                    if distToLine < 80 then -- R技能宽度
                        slot_19_1 = slot_19_1 + 1
                    end
                end
            end
        end
    end

    return slot_19_0, slot_19_1
end

-- E技能释放函数 (ove_0_75) - dvvv完全一样
local function CastE(target)
    if player:spellSlot(2).state ~= 0 or not target or not IsValidTarget_DVV(target) then
        return false
    end

    local slot_18_0 = pred.linear.get_prediction(EPrediction, target)

    if slot_18_0 and slot_18_0.endPos and slot_18_0.startPos:distSqr(slot_18_0.endPos) < 1102500 and ove_0_73(EPrediction, slot_18_0, target) and not ove_0_70(player, vec3(slot_18_0.endPos.x, target.y, slot_18_0.endPos.y)) then
        if orb.core then
            orb.core.set_server_pause_attack()
        end
        player:castSpell("pos", 2, vec3(slot_18_0.endPos.x, target.y, slot_18_0.endPos.y))
    end
end
--
local function ValidTargetRange(unit, range)
    return unit and unit.isVisible and not unit.isDead and unit.isTargetable and not CheckBuffType(unit, 17) and (not range or player.pos:dist(unit.pos) <= range)
end
--
local function IsReady(spell)
    return player:spellSlot(spell).state == 0
end 
--
local function set_server_pause()
	Qpause = os.clock() + network.latency + 0.25
end
--
local function is_Q_paused()
	return Qpause > os.clock()
end
--
local function GetPercentPar(obj)
	local obj = obj or player
	return (obj.par / obj.maxPar) * 100
end
--
local function DamageR(target)
    if target ~= 0 then
		local Damage = 0
		local DamageAP = {175, 275, 375}
        if player:spellSlot(3).state == 0 then
			Damage = (DamageAP[player:spellSlot(3).level] + 1.1 * player.baseAttackDamage + player.flatPhysicalDamageMod * 1 + player.percentPhysicalDamageMod - player.baseAttackDamage)
        end
		return Damage
	end
	return 0
end
--

local function AAdmg(target)
	if target ~= 0 then
		local Dmg = 0
		if player:spellSlot(3).state == 0 then
			return GZ.JSAADMG(target)
		end
	end
	return 0
end

local function CountQs()
	if player then
		for i = 0, player.buffManager.count - 1 do
			local buff = player.buffManager:get(i)
			if buff and buff.valid and buff.name == "DravenSpinningAttack" then
				return buff.stacks + #AxePositions
			end
		end 
	end
	return #AxePositions
end
--
local function TargetSelecton(Range)
    Range = Range or 900 
    if orb.combat and orb.combat.target and not orb.combat.target.isDead and orb.combat.target.isTargetable and orb.combat.target.isVisible then
        return orb.combat.target
    else 
        local dist, closest = math.huge, nil
        for i = 0, objManager.enemies_n - 1 do
            local unit = objManager.enemies[i]
            local unit_distance = player.pos:dist(unit.pos);
            
			if not unit.isDead and unit.isVisible and unit.isTargetable and unit_distance <= Range then
                if unit_distance < dist then
                    closest = unit
                    dist = unit_distance;
                end
            end
            if closest then
                return closest
            end
        end
        return nil
    end 
end 
--
-- menu initialization
local menu = menu(header.id .. "_" .. player.charName, "[Brian] Draven")
--menu:set("icon", player.iconSquare)
--menu:set("icon", GZ.Ico_Char())
menu:header("title", "Welcome to Draven Script")
menu:menu("combo", "Combo")
--menu.combo:set("icon", GZ.Ico_Combo())
menu:menu("axe", "Axes")
--menu.axe:set("icon", player:spellSlot(0).icon)
menu:menu("misc", "Misc")
--menu.misc:set("icon", GZ.Ico_Misc())
menu:menu("drawing", "Drawing")
--menu.drawing:set("icon", GZ.Ico_Draw())

--combo submenu options
menu:header("title", "Combo Settings")
menu.combo:header("title", "Combo")
menu.combo:boolean("comboQ", "Use Q", true)
menu.combo:boolean("comboW", "Use W", true)
menu.combo:slider("wmana", "Use W when Mana% < X", 1, 1, 100, 1)
menu.combo:boolean("comboE", "Use E", true)
menu.combo:boolean("r", "Use R", true)
menu.combo:slider("XaaNOR", "Use R if X auto attacks cannot kill", 3, 1, 10, 1)
menu.combo:slider("Rrange", "R Cast Range", 2000, 1000, 5000, 100)
menu.combo:slider("rhit", "Use Min Hit", 2, 1, 5, 1)
menu.combo:keybind("autor", "Auto R", "A", nil)

-- dvvv一样的击杀模式菜单
menu:menu("kill", "Kill Mode")
menu.kill:boolean("enable", "Enable Kill Mode", true)
menu.kill:boolean("e", "Use E to Kill", true)
menu.kill:boolean("r", "Use R to Kill", true)
--menu:header("title", "模式/痈")
menu.axe:header("title", "Axes Settings")
menu.axe:dropdown("catchMode", "Catch Mode:", 2, {"In Combo", "Always", "Never"})
menu.axe:slider("catchRange", "Catch Range", 700, 100, 1500, 10)
menu.axe:slider("max", "Max Axes Count", 2, 1, 7, 1)
menu.axe:boolean("MaxAxe", "Use 4 Axes Mode if AS > 2.0", true)
menu.axe:boolean("turret", "Catch Axes Under Turret", true)
--menu:header("title", "/野/突/")
menu.misc:header("title", "Misc Settings")
menu.misc:boolean("QNotCombo", "Use Q Outside Combo", true)
menu.misc:boolean("wifslowed", "Use W when Slowed", true)
menu.misc:boolean("gapcloser", "Auto E Gapclosers", true)
menu.misc:boolean("interrupt", "Auto E Interrupt", true)
menu.misc:menu("interruptmenu", "Interrupt Settings")
menu.misc.interruptmenu:header("lol", "Interruptable Spells")

for i = 0, objManager.enemies_n - 1 do
    local enemy = objManager.enemies[i]
    local name = string.lower(enemy.charName)
    if enemy and interruptableSpells[name] then
        for v = 1, #interruptableSpells[name] do
            local spell = interruptableSpells[name][v]
			menu.misc.interruptmenu:boolean(string.format(enemy.charName .. spell.menuslot), ">>> " .. enemy.charName .. " [" .. spell.menuslot .. "]", true)
		end 
	end 	
end

menu.drawing:header("title", "Drawing Settings")
menu.drawing:boolean("drawAxe", "Draw Axes", true)
menu.drawing:boolean("drawAxeRange", "Draw Axes Catch Range", true)
menu.drawing:color("colorAxeRange", "Range Color", 255, 28, 28, 28)

local function OnAxeCreation(unit)
	if player.pos:dist(unit.pos) > 2000 then return end
	if string.find(unit.name, "reticle_self") then
		LatestAxeCreateTick = LatestAxeCreateTick + 1
		-- 添加创建时间戳
		local axeInfo = {
			ptr = unit.ptr,
			x = unit.x,
			y = unit.y,
			z = unit.z,
			pos = unit.pos,
			creationTime = os.clock()
		}
		table.insert(AxePositions, axeInfo)
		set_server_pause()
	end 
end

local function AAspeed()
	return player.attackSpeedMod * 0.679
end

local function OnAxeDeletion(unit)
	for k, v in pairs(AxePositions) do
		if v.ptr == unit.ptr then 
			table.remove(AxePositions, k)
			set_server_pause()
			return
		end
	end 
end 

local function AutoInterrupt(spell)
	if menu.misc.interrupt:get() and player:spellSlot(2).state == 0 then
    local owner = spell.owner
		if owner.type == TYPE_HERO and owner.team == TEAM_ENEMY then
			local enemyName = string.lower(owner.charName)
			if interruptableSpells[enemyName] then
				for i = 1, #interruptableSpells[enemyName] do
					local spellCheck = interruptableSpells[enemyName][i]
					if menu.misc.interruptmenu[owner.charName .. spellCheck.menuslot]:get() and string.lower(spell.name) == spellCheck.spellname then
						if IsValidTarget(enemy) and owner.pos:dist(player.pos) < 950 then
							player:castSpell('pos', 2, vec3(owner.x, game.mousePos.y, owner.z))
						end
					end
				end
			end
		end
	end
end

--锟斤拷突锟斤拷
local function AntiGapcloser()
    for i = 0, objManager.enemies_n - 1 do
		local target = objManager.enemies[i]
        if IsValidTarget(target) then
			if menu.misc.gapcloser:get() and player:spellSlot(2).state == 0 then
				if target and target.isValid and target.path.active and target.path.isDashing and not target.buff["rocketgrab"] then
					local v2 = pred.core.project(player.path.serverPos, target.path, 0.25 + network.latency, 1400, target.path.dashSpeed)
					if v2 and v2:dist(player.path.serverPos) <= 300 then
						player:castSpell("pos", 2, vec3(v2.x, player.y, v2.y))
					end
				end
			end
		end
	end
end

local function OnDraw()
	if IsValidTarget(player) then
		if AxePositions ~= nil then
			for k, axe in pairs(AxePositions) do
				graphics.draw_circle(axe.pos, 110, 2, graphics.argb(255, 0, 255, 0), 50)
				graphics.draw_line(player.pos, axe.pos, 2, graphics.argb(255, 255, 255, 0))
			end 
		end 
		if menu.drawing.drawAxeRange:get() then
			graphics.draw_circle(game.mousePos, menu.axe.catchRange:get(), 2, menu.drawing.colorAxeRange:get(), 40)
		end
	end 
end 

-- 优化斧头位置安全性检查函数
local function IsAxePositionSafe(axePos)
    -- 检查斧头是否在敌方防御塔范围内
    for i = 0, objManager.turrets.size[TEAM_ENEMY] - 1 do
        local turret = objManager.turrets[TEAM_ENEMY][i]
        if turret and not turret.isDead and axePos:dist(turret.pos) < 900 then
            if not menu.axe.turret:get() then
                return false
            end
        end
    end
    
    -- 检查斧头周围是否有过多敌人
    local enemiesNearAxe = 0
    local allyCount = 1 -- 自己算一个队友
    for i = 0, objManager.enemies_n - 1 do
        local enemy = objManager.enemies[i]
        if IsValidTarget(enemy) and axePos:dist(enemy.pos) < 400 then
            enemiesNearAxe = enemiesNearAxe + 1
        end
    end
    
    -- 检查斧头周围的队友数量
    for i = 0, objManager.allies_n - 1 do
        local ally = objManager.allies[i]
        if ally and ally.ptr ~= player.ptr and IsValidTarget(ally) and axePos:dist(ally.pos) < 600 then
            allyCount = allyCount + 1
        end
    end
    
    -- 如果斧头周围敌人比队友多，认为不安全
    if enemiesNearAxe > allyCount then
        return false
    end
    
    -- 检查斧头是否会让我们走进敌方控制区域
    if evade and evade.core.is_active() and not evade.core.is_action_safe(axePos, player.moveSpeed, 0.1) then
        return false
    end
    
    return true
end

-- 添加斧头落点预测函数
local function PredictAxeFallPosition(axe)
    -- 如果斧头已经存在，直接返回其位置
    if axe then
        return vec3(axe.x, axe.y, axe.z)
    end
    return nil
end

-- 优化BestAxe函数，考虑安全性、距离和战斗状态
local function BestAxe()
    local best = nil
    local bestScore = -1
    
    -- 如果没有斧头，直接返回
    if #AxePositions == 0 then
        return nil
    end
    
    -- 获取当前战斗目标
    local combatTarget = orb.combat and orb.combat.target
    local inCombat = orb.combat and orb.combat.is_active() and combatTarget and IsValidTarget(combatTarget)
    
    for i = 1, #AxePositions do
        local axe = AxePositions[i]
        if axe then
            local axePos = vec3(axe.x, axe.y, axe.z)
            local distToPlayer = axePos:dist(player.pos)
            
            -- 只考虑在接斧范围内的斧头
            if distToPlayer < menu.axe.catchRange:get() then
                -- 计算得分，考虑距离、安全性等因素
                local distToMouse = game.mousePos:dist(axePos)
                local isSafe = IsAxePositionSafe(axePos)
                
                -- 基础得分
                local score = 1000
                
                -- 安全性评分
                if not isSafe then
                    score = score - 800
                end
                
                -- 距离评分：越近越好
                score = score - distToPlayer * 0.5
                
                -- 鼠标位置评分：越接近鼠标越好
                score = score - distToMouse * 0.3
                
                -- 战斗状态评分：如果在战斗中，优先选择靠近战斗目标的斧头
                if inCombat then
                    local distToTarget = axePos:dist(combatTarget.pos)
                    if distToTarget < 600 then
                        score = score + (600 - distToTarget) * 0.5
                    else
                        score = score - (distToTarget - 600) * 0.2
                    end
                end
                
                -- 边缘接刀优化：如果斧头快要消失，提高其优先级
                local axeLifetime = 1.5 -- 假设斧头存在时间为1.5秒
                if axe.creationTime and (os.clock() - axe.creationTime) > (axeLifetime * 0.7) then
                    score = score + 300
                end
                
                if score > bestScore then
                    best = axe
                    bestScore = score
                end
            end
        end
    end
    return best
end

local function FuryCount()
	if player then
		for i = 0, player.buffManager.count - 1 do
			local buff = player.buffManager:get(i)
			if buff and buff.valid and buff.name == "dravenfurybuff" then
				return buff.stacks
			end
		end
	end
	return 0
end

-- 优化GoFetch函数，实现更平滑的边缘接刀
local function GoFetch()
    local method = menu.axe.catchMode:get()
    if (method == 1 and orb.combat and orb.combat.is_active()) or (method == 2) then
        local axe = BestAxe()
        if axe then
            local axePos = vec3(axe.x, axe.y, axe.z)
            local distToAxe = axePos:dist(player.pos)
            
            -- 只有当距离斧头超过一定范围时才移动
            if distToAxe > 85 then
                -- 检查斧头安全性
                if IsAxePositionSafe(axePos) then
                    -- 计算移动时间和斧头消失时间
                    local moveTime = distToAxe / player.moveSpeed
                    local axeLifetime = 1.5 -- 假设斧头存在时间为1.5秒
                    local timeRemaining = axeLifetime
                    
                    if axe.creationTime then
                        timeRemaining = axeLifetime - (os.clock() - axe.creationTime)
                    end
                    
                    -- 如果来不及接到斧头，不要尝试
                    if moveTime < timeRemaining then
                        -- 检查是否可以行动且不在攻击中
                        if orb.core and orb.core.can_action() and not orb.core.can_attack() then
                            -- 获取当前战斗目标
                            local target = orb.combat and orb.combat.target
                            
                            -- 边缘接刀优化：如果斧头在攻击范围内但又不太近，先攻击再接
                            if target and IsValidTarget(target) and distToAxe < 300 and target.pos:dist(player.pos) <= GetAARange(target) then
                                -- 不做任何操作，让普攻优先
                                return
                            end
                            
                            -- 如果斧头不会使我们远离战斗目标过多，或者没有战斗目标，则接斧
                            if not target or not IsValidTarget(target) or axePos:dist(target.pos) < 600 then
                                -- 边缘接刀优化：使用预测位置进行更平滑的移动
                                local predictedPos = PredictAxeFallPosition(axe)
                                if predictedPos then
                                    player:move(predictedPos)
                                    return
                                end
                                
                                -- 如果没有预测位置，使用实际位置
                                player:move(axePos)
                            end
                        end
                    end
                end
            end
        end
    end
end 

-- 浼樺寲UseQOutsideCombo鍑芥暟锛岃€冭檻钃濋噺鍜屽綋鍓嶆垬鏂楃姸鎬?
local function UseQOutsideCombo()
    if menu.misc.QNotCombo:get() and player:spellSlot(0).state == 0 and orb.core and orb.core.can_attack() and not is_Q_paused() then
        -- 妫€鏌ユ槸鍚︽湁瓒冲?熺殑钃濋噺
        if player.par >= 40 then
            -- 妫€鏌ラ檮杩戞槸鍚︽湁鏁屼汉
            local enemiesNearby = 0
            for i = 0, objManager.enemies_n - 1 do
                local enemy = objManager.enemies[i]
                if IsValidTarget(enemy) and player.pos:dist(enemy.pos) < 1000 then
                    enemiesNearby = enemiesNearby + 1
                end
            end
            
            -- 鍩轰簬鏁屼汉鏁伴噺鍜屽綋鍓嶆枾澶存暟鍐冲畾鏄?鍚︿娇鐢≦
            if (enemiesNearby == 0 and CountQs() < 1) or (enemiesNearby > 0 and CountQs() < 2) then
                player:castSpell("self", 0)
                -- 鏍规嵁鏀婚€熷喅瀹氭槸鍚︿娇鐢ㄦ洿澶歈
                local attackSpeed = AAspeed()
                if attackSpeed > 2.0 and menu.axe.MaxAxe:get() and CountQs() < 3 then
                    player:castSpell("self", 0)
                end
            end
        end
    end
end

-- 浼樺寲IsCombo鍑芥暟涓?鐨勬妧鑳戒娇鐢ㄩ€昏緫
local function IsCombo(target)
    if target.pos:dist(player.pos) <= 1200 then
        -- 浼樺寲Q鎶€鑳戒娇鐢ㄩ€昏緫
        if menu.combo.comboQ:get() and player:spellSlot(0).state == 0 and IsValidTarget(target) and orb.core and orb.core.can_attack() then
            -- 鍩轰簬鏀婚€熷姩鎬佽皟鏁存枾澶存暟閲?
            local maxAxes = menu.axe.max:get()
            if AAspeed() > 2.0 and menu.axe.MaxAxe:get() then
                maxAxes = math.min(maxAxes + 1, 4)
            end
            
            if CountQs() < maxAxes and player.par >= 40 then
                player:castSpell("self", 0)
            end
        end 
        
        -- 浼樺寲W鎶€鑳戒娇鐢ㄩ€昏緫
        if menu.combo.comboW:get() and player:spellSlot(1).state == 0 and IsValidTarget(target) then
            -- 鑰冭檻鏇村?氫娇鐢╓鐨勬儏鍐?
            local shouldUseW = false
            
            -- 鏃犲姞閫烞uff鏃朵娇鐢?
            if FuryCount() < 1 and GetPercentPar() > menu.combo.wmana:get() then
                shouldUseW = true
            end
            
            -- 杩藉嚮鏁屼汉鏃朵娇鐢?
            if player.moveSpeed < target.moveSpeed and player.pos:dist(target.pos) > 300 and player.pos:dist(target.pos) < 550 then
                shouldUseW = true
            end
            
            -- 閫冭窇鏃朵娇鐢?
            local healthPercent = (player.health / player.maxHealth) * 100
            if healthPercent < 30 and CountAllyChampAroundObject(player.pos, 600) < CountAllyChampAroundObject(target.pos, 600) then
                shouldUseW = true
            end
            
            if shouldUseW then
                player:castSpell("self", 1)
            end
        end
        
        -- 浼樺寲E鎶€鑳戒娇鐢ㄩ€昏緫
        if menu.combo.comboE:get() and player:spellSlot(2).state == 0 then
            local c_target = orb.combat and orb.combat.target
            if c_target and IsValidTarget(c_target) and c_target.pos:dist(player.pos) <= 950 then
                target = c_target
                
                -- 鏇存櫤鑳界殑E鎶€鑳藉垽鏂?鏉′欢
                local shouldUseE = false
                
                -- 鐩?鏍囩敓鍛藉€艰緝浣庢椂浣跨敤
                if GZ.GetHP(target) < 75 then
                    shouldUseE = true
                end
                
                -- 鐩?鏍囧皾璇曢€冭窇鏃朵娇鐢?
                if target.path.active and not target.path.isDashing then
                    local pathEndIndex = target.path.count
                    if pathEndIndex > 0 then
                        local targetEndX = target.path.point[pathEndIndex].x
                        local targetEndZ = target.path.point[pathEndIndex].z
                        local targetEndPos = vec3(targetEndX, target.y, targetEndZ)
                        if player.pos:dist(targetEndPos) > player.pos:dist(target.pos) then
                            shouldUseE = true
                        end
                    end
                end
                
                -- 鐩?鏍囪??鎺у埗鏃朵娇鐢?锛屽?炲姞鍛戒腑鐜?
                if CheckBuffType(target, 5) or CheckBuffType(target, 11) or CheckBuffType(target, 29) then
                    shouldUseE = true
                end
                
                if shouldUseE then
                    local seg = pred.linear.get_prediction(EPrediction, target)
                    if seg and seg.startPos:distSqr(seg.endPos) < (950 * 950) then
                        player:castSpell("pos", 2, vec3(seg.endPos.x, target.y, seg.endPos.y))
                    end
                end
            end
        end
        
        -- 连招R逻辑 - 与222222完全一致：R已释放时检查伤害，未释放时直接打伤害
        if menu.combo.r:get() and player:spellSlot(3).state == 0 then
            local slot_34_0, slot_34_1 = CastR_DVV(target)
            local slot_34_2 = orb.combat and orb.combat.target and orb.combat.target == target and DamageR_DVV_Base(target) * 2 or DamageR_DVV_Base(target)

            -- R已释放时：检查击杀或多目标条件
            if slot_34_0 and IsRCasted() and (slot_34_2 >= target.health or menu.combo.rhit:get() > 1 and slot_34_1 and slot_34_1 >= menu.combo.rhit:get()) and orb.core and orb.core.can_action() then
                player:castSpell("pos", 3, slot_34_0)
            end

            -- R未释放时：直接用来打伤害，简化时间条件
            if slot_34_0 and not IsRCasted() and orb.core and orb.core.can_action() then
                player:castSpell("pos", 3, slot_34_0)
            end
        end
    end
end

local function OnTick()
	GoFetch()
	if player:spellSlot(1).state == 0 and menu.misc.wifslowed:get() and CheckBuffType(player, 10) then
		player:castSpell("self", 1)
	end 
	--
	-- dvvv一样的击杀模式 - 优先级最高
	if menu.kill.enable:get() then
		for i = 0, objManager.enemies_n - 1 do
			local enemy = objManager.enemies[i]
			if enemy and IsValidTarget_DVV(enemy) and enemy.team ~= TEAM_ALLY and enemy.pos:distSqr(player.pos) < 9000000 then
				if not enemy.buff.undyingrage and not enemy.buff.sionpassivezombie then
					-- E技能击杀 - dvvv完全一样
					if menu.kill.e:get() and player:spellSlot(2).state == 0 and enemy.health <= DamageE_DVV(enemy) and enemy.pos:distSqr(player.pos) <= 1102500 then
						CastE(enemy)
					end

					-- R技能击杀 - 与222222完全一致，使用带翻倍逻辑的伤害计算
					if menu.kill.r:get() and player:spellSlot(3).state == 0 and enemy.health + enemy.physicalShield <= DamageR_DVV_WithDouble(enemy) and enemy.pos:distSqr(player.pos) <= 9000000 then
						local slot_37_1 = CastR_DVV(enemy)

						if slot_37_1 and IsRCasted() then
							player:castSpell("pos", 3, slot_37_1)
						end

						if slot_37_1 and not IsRCasted() and DravenRCastTime + (player.pos:dist(slot_37_1) / 2000 + 0.4) < game.time then
							player:castSpell("pos", 3, slot_37_1)
						end
					end
				end
			end
		end
	end

	-- dvvv完全一样的自动R逻辑 - 按A键触发
	if menu.combo.autor:get() then
		-- 不按连招键或骚扰键时，跟着鼠标移动
		if not (orb.menu and (orb.menu.combat.key:get() or orb.menu.hybrid.key:get())) then
			local slot_39_0 = player.path.serverPos:lerp(mousePos, 500 / player.path.serverPos:dist(mousePos))
			player:move(slot_39_0)
		end

		-- 自动R释放
		if player:spellSlot(3).state == 0 then
			local target = TargetSelecton(3000)
			if target and IsValidTarget_DVV(target) then
				local slot_39_1 = CastR_DVV(target)

				if slot_39_1 and IsRCasted() and orb.core and orb.core.can_action() then
					player:castSpell("pos", 3, slot_39_1)
				end

				if slot_39_1 and not IsRCasted() and DravenRCastTime + (player.pos:dist(slot_39_1) / 2000 + 0.4) < game.time then
					player:castSpell("pos", 3, slot_39_1)
				end
			end
		end
	end

	if (orb.combat and orb.combat.is_active()) then
		local target = TargetSelecton(1000)
		if target and IsValidTarget(target) then
			IsCombo(target)
		end
	end
	if (orb.menu and (orb.menu.lane_clear:get() or orb.menu.hybrid:get() or orb.menu.last_hit:get())) then
		UseQOutsideCombo()
	end
end

-- dvvv一样的R技能时间记录
local function OnSpellCast(spell)
    if spell and spell.name and spell.owner and spell.owner == player then
        if spell.name == "DravenRCast" then
            DravenRCastTime = game.time
        end
    end
end

if orb.combat then
    orb.combat.register_f_pre_tick(OnTick)
end
cb.add(cb.create_particle, OnAxeCreation)
cb.add(cb.delete_particle, OnAxeDeletion)
cb.add(cb.draw, OnDraw)
cb.add(cb.spell, AutoInterrupt)
cb.add(cb.spell, OnSpellCast)

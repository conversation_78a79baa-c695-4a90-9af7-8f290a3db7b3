

local ove_0_6 = module.internal("damagelib")
local ove_0_7 = module.internal("orb")
local ove_0_8 = module.internal("TS")
local ove_0_9 = module.internal("pred")
local ove_0_10 = module.internal("damagelib")
local ove_0_11 = module.seek("evade")
local ove_0_12 = module.load(header.id, "menu")
local ove_0_13 = {
	ChatPrint = function(arg_5_0)
		return chat.print(arg_5_0)
	end,
	IsUnderTurretEnemy = function(arg_6_0)
		if not arg_6_0 then
			return false
		end

		for iter_6_0 = 0, objManager.turrets.size[TEAM_ENEMY] - 1 do
			local slot_6_0 = objManager.turrets[TEAM_ENEMY][iter_6_0]

			if slot_6_0 and not slot_6_0.isDead and slot_6_0.health > 0 then
				if vec3(slot_6_0.x, slot_6_0.y, slot_6_0.z):dist(arg_6_0) < 915 then
					return true
				end
			else
				local slot_6_1
			end
		end

		return false
	end,
	IsImmobile = function(arg_7_0, arg_7_1)
		local slot_7_0 = {}
		local slot_7_1 = game.time + (arg_7_1 or 0)

		for iter_7_0 = 0, arg_7_0.buffManager.count - 1 do
			local slot_7_2 = arg_7_0.buffManager:get(iter_7_0)

			if slot_7_2 and slot_7_2.valid and slot_7_1 <= slot_7_2.endTime then
				slot_7_0[slot_7_2.type] = true
			end
		end

		if slot_7_0[5] or slot_7_0[8] or slot_7_0[11] or slot_7_0[18] or slot_7_0[24] or slot_7_0[29] then
			return true
		end
	end,
	IsImmortal = function(arg_8_0)
		for iter_8_0 = 0, arg_8_0.buffManager.count - 1 do
			local slot_8_0 = arg_8_0.buffManager:get(iter_8_0)

			if slot_8_0 and slot_8_0.valid and slot_8_0.type == 17 then
				return true
			end
		end
	end
}

function ove_0_13.IsValidTarget(arg_9_0, arg_9_1)
	return arg_9_0 and not arg_9_0.isDead and arg_9_0.isVisible and arg_9_0.isTargetable and not ove_0_13.IsImmortal(arg_9_0) and (not arg_9_1 or arg_9_1 >= player.pos:dist(arg_9_0.pos))
end

function ove_0_13.MagicReduction(arg_10_0, arg_10_1)
	local slot_10_0 = arg_10_1 or player
	local slot_10_1 = arg_10_0.spellBlock * slot_10_0.percentMagicPenetration - slot_10_0.flatMagicPenetration

	return slot_10_1 >= 0 and 100 / (100 + slot_10_1) or 2 - 100 / (100 - slot_10_1)
end

function ove_0_13.DamageReduction(arg_11_0, arg_11_1, arg_11_2)
	local slot_11_0

	slot_11_0 = arg_11_2 or player

	local slot_11_1 = 1

	if arg_11_0 == "AD" then
		-- block empty
	end

	if arg_11_0 == "AP" then
		-- block empty
	end

	return slot_11_1
end

function ove_0_13.PhysicalReduction(arg_12_0, arg_12_1)
	local slot_12_0 = arg_12_1 or player
	local slot_12_1 = (arg_12_0.bonusArmor * slot_12_0.percentBonusArmorPenetration + (arg_12_0.armor - arg_12_0.bonusArmor)) * slot_12_0.percentArmorPenetration
	local slot_12_2 = slot_12_0.physicalLethality * 0.4 + slot_12_0.physicalLethality * 0.6 * (slot_12_0.levelRef / 18)

	return slot_12_1 >= 0 and 100 / (100 + (slot_12_1 - slot_12_2)) or 2 - 100 / (100 - (slot_12_1 - slot_12_2))
end

function ove_0_13.CalculateMagicDamage(arg_13_0, arg_13_1, arg_13_2)
	local slot_13_0 = arg_13_2 or player

	if arg_13_0 then
		return arg_13_1 * ove_0_13.MagicReduction(arg_13_0, slot_13_0) * ove_0_13.DamageReduction("AP", arg_13_0, slot_13_0)
	end

	return 0
end

function ove_0_13.CalculatePhysicalDamage(arg_14_0, arg_14_1)
	local slot_14_0 = (arg_14_0.bonusArmor * player.percentBonusArmorPenetration + (arg_14_0.armor - arg_14_0.bonusArmor)) * player.percentArmorPenetration
	local slot_14_1 = player.physicalLethality * 0.4 + player.physicalLethality * 0.6 * (player.levelRef / 18)

	if arg_14_0 then
		return arg_14_1 * slot_14_0 * 1
	end

	return 0
end

ove_0_13.GetSpellDamage = {
	Akali = {},
	Ekko = {},
	Leblanc = {},
	Talon = {},
	Kassadin = {},
	Khazix = {},
	Rengar = {},
	Evelynn = {}
}

function ove_0_13.TotalPhysical()
	return (player.baseAttackDamage + player.flatPhysicalDamageMod) * player.percentPhysicalDamageMod
end

function ove_0_13.TotalMagical()
	return player.flatMagicDamageMod * player.percentMagicDamageMod
end

function ove_0_13.BonusPhysical()
	return (player.baseAttackDamage + player.flatPhysicalDamageMod) * player.percentPhysicalDamageMod - player.baseAttackDamage
end

function ove_0_13.CalculateAADamage(arg_18_0, arg_18_1)
	local slot_18_0 = arg_18_1 or player

	if arg_18_0 then
		return ove_0_13.TotalPhysical(slot_18_0) * ove_0_13.PhysicalReduction(arg_18_0, slot_18_0)
	end

	return 0
end

function ove_0_13.HealthPercent(arg_19_0)
	return arg_19_0.maxHealth > 5 and arg_19_0.health / arg_19_0.maxHealth * 100 or 100
end

function ove_0_13.ManaPercent(arg_20_0)
	return arg_20_0.maxMana > 0 and arg_20_0.mana / arg_20_0.maxMana * 100 or 100
end

function ove_0_13.GetTrueAttackRange(arg_21_0, arg_21_1)
	return arg_21_0.attackRange + arg_21_0.boundingRadius + (arg_21_1 and arg_21_1.boundingRadius or 0)
end

function ove_0_13.GetEnemyHeroes(arg_22_0)
	local slot_22_0 = {}

	for iter_22_0 = 0, objManager.enemies_n - 1 do
		local slot_22_1 = objManager.enemies[iter_22_0]

		if arg_22_0 > player.pos:dist(slot_22_1) and ove_0_13.IsValidTarget(slot_22_1) then
			slot_22_0[#slot_22_0 + 1] = slot_22_1
		end
	end

	return slot_22_0
end

function ove_0_13.CountObjectsNearPos(arg_23_0, arg_23_1, arg_23_2, arg_23_3)
	local slot_23_0 = 0
	local slot_23_1 = {}

	for iter_23_0, iter_23_1 in ipairs(arg_23_2) do
		if arg_23_3(iter_23_1) and arg_23_1 >= arg_23_0:dist(iter_23_1.pos) then
			slot_23_0 = slot_23_0 + 1
			slot_23_1[slot_23_0] = iter_23_1
		end
	end

	return slot_23_0, slot_23_1
end

function ove_0_13.CountMinionsNearPos(arg_24_0, arg_24_1)
	minions = {}

	for iter_24_0 = 0, objManager.minions.size[TEAM_ENEMY] - 1 do
		minions[iter_24_0] = objManager.minions[TEAM_ENEMY][iter_24_0]
	end

	local function slot_24_0(arg_25_0)
		return arg_25_0 and not arg_25_0.isDead and arg_25_0.health and arg_25_0.health > 0 and arg_25_0.isVisible and arg_25_0.isTargetable
	end

	return ove_0_13.CountObjectsNearPos(arg_24_0, arg_24_1, minions, slot_24_0)
end

function ove_0_13.AATracker()
	local slot_26_0 = objManager.enemies
	local slot_26_1 = objManager.enemies_n

	for iter_26_0 = 0, slot_26_1 - 1 do
		local slot_26_2 = slot_26_0[iter_26_0]

		if not slot_26_2.isDead and slot_26_2.isVisible then
			local slot_26_3 = slot_26_2.health / ove_0_13.CalculateAADamage(slot_26_2, player)
			local slot_26_4 = tostring(slot_26_3)
			local slot_26_5 = slot_26_4.format("%.0f", slot_26_4)
			local slot_26_6 = graphics.world_to_screen(slot_26_2.pos)

			graphics.draw_outlined_text_2D("AA Left: " .. slot_26_5, 17, slot_26_6.x, slot_26_6.y, **********)
		end
	end
end

function ove_0_13.isfacing(arg_27_0)
	return player.path.serverPos:distSqr(arg_27_0.path.serverPos) > player.path.serverPos:distSqr(arg_27_0.path.serverPos + arg_27_0.direction)
end

function ove_0_13.SlowPredLinear(arg_28_0, arg_28_1, arg_28_2, arg_28_3)
	if ove_0_9.trace.linear.hardlock(arg_28_0, arg_28_1, arg_28_2) then
		return true
	end

	if ove_0_9.trace.linear.hardlockmove(arg_28_0, arg_28_1, arg_28_2) then
		return true
	end

	if arg_28_3 < arg_28_1.startPos:dist(arg_28_1.endPos) then
		return false
	end

	if ove_0_9.trace.newpath(arg_28_2, 0.033, 0.5) then
		return true
	end
end

function ove_0_13.SlowPredcircular(arg_29_0, arg_29_1, arg_29_2, arg_29_3)
	if ove_0_9.trace.circular.hardlock(arg_29_0, arg_29_1, arg_29_2) then
		return true
	end

	if ove_0_9.trace.circular.hardlockmove(arg_29_0, arg_29_1, arg_29_2) then
		return true
	end

	if arg_29_3 < arg_29_1.startPos:dist(arg_29_1.endPos) then
		return false
	end

	if ove_0_9.trace.newpath(arg_29_2, 0.033, 0.5) then
		return true
	end
end

function ove_0_13.GetAARange(arg_30_0)
	return player.attackRange + player.boundingRadius + (arg_30_0 and arg_30_0.boundingRadius or 0)
end

return ove_0_13

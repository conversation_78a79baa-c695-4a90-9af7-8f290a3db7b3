

local ove_0_6 = module.load(header.id, "Utility/commonF")
local ove_0_7 = module.load(header.id, "Utility/menuFA")
local ove_0_8 = module.internal("orb")
local ove_0_9 = module.internal("TS")
local ove_0_10 = module.internal("pred")
local ove_0_11 = module.internal("damagelib")
local ove_0_12 = module.seek("evade")
local ove_0_13 = {}
local ove_0_14 = {}
local ove_0_15 = {}
local ove_0_16 = {}
local ove_0_17 = {}

local function ove_0_18()
	if ove_0_7.awarenessmenu.clonetracker.clone:get() then
		for iter_5_0 = 0, objManager.enemies_n - 1 do
			local slot_5_0 = objManager.enemies[iter_5_0]

			if slot_5_0 and slot_5_0.isVisible and (slot_5_0.charName == "MonkeyKing" and #ove_0_13 > 0 or slot_5_0.charName == "<PERSON>haco" and #ove_0_14 > 0 or slot_5_0.charName == "Leblanc" and #ove_0_15 > 0 or slot_5_0.charName == "Neeko" and #ove_0_16 > 0) then
				if ove_0_7.awarenessmenu.clonetracker.clonetrackerdrawmode:get() == 2 then
					graphics.draw_circle(slot_5_0.pos, 100, 2, graphics.argb(255, 0, 255, 0), 100)
				end

				if ove_0_7.awarenessmenu.clonetracker.clonetrackerdrawmode:get() == 1 then
					local slot_5_1 = graphics.world_to_screen(slot_5_0.pos)

					if slot_5_1 then
						slot_5_1.y = slot_5_1.y - 130
						slot_5_1.x = slot_5_1.x - 25

						graphics.draw_sprite("Resources/prufen.png", slot_5_1, 1, 4294967295)
					end
				end
			end
		end

		for iter_5_1 = 1, #ove_0_17 do
			local slot_5_2 = ove_0_17[iter_5_1]

			if slot_5_2 and slot_5_2.obj and slot_5_2.obj.isVisible then
				local slot_5_3 = graphics.world_to_screen(slot_5_2.obj.pos)

				if slot_5_3 then
					slot_5_3.y = slot_5_3.y - 135
					slot_5_3.x = slot_5_3.x - 25

					graphics.draw_sprite("Resources/neeko_circle_0.png", slot_5_3, 0.5, 4294967295)
				end
			end
		end

		for iter_5_2 = 1, #ove_0_13 do
			local slot_5_4 = ove_0_13[iter_5_2]

			if slot_5_4 then
				if ove_0_7.awarenessmenu.clonetracker.clonetrackerdrawmode:get() == 2 then
					graphics.draw_circle(slot_5_4.pos, 100, 2, graphics.argb(255, 255, 0, 0), 100)
				end

				if ove_0_7.awarenessmenu.clonetracker.clonetrackerdrawmode:get() == 1 then
					local slot_5_5 = graphics.world_to_screen(slot_5_4.pos)

					if slot_5_5 then
						slot_5_5.y = slot_5_5.y - 100
						slot_5_5.x = slot_5_5.x - 25

						graphics.draw_sprite("Resources/loschen.png", slot_5_5, 1, 4294967295)
					end
				end
			end
		end

		for iter_5_3 = 1, #ove_0_14 do
			local slot_5_6 = ove_0_14[iter_5_3]

			if slot_5_6 then
				if ove_0_7.awarenessmenu.clonetracker.clonetrackerdrawmode:get() == 2 then
					graphics.draw_circle(slot_5_6.pos, 100, 2, graphics.argb(255, 255, 0, 0), 100)
				end

				if ove_0_7.awarenessmenu.clonetracker.clonetrackerdrawmode:get() == 1 then
					local slot_5_7 = graphics.world_to_screen(slot_5_6.pos)

					if slot_5_7 then
						slot_5_7.y = slot_5_7.y - 100
						slot_5_7.x = slot_5_7.x - 25

						graphics.draw_sprite("Resources/loschen.png", slot_5_7, 1, 4294967295)
					end
				end
			end
		end

		for iter_5_4 = 1, #ove_0_15 do
			local slot_5_8 = ove_0_15[iter_5_4]

			if slot_5_8 then
				if ove_0_7.awarenessmenu.clonetracker.clonetrackerdrawmode:get() == 2 then
					graphics.draw_circle(slot_5_8.pos, 100, 2, graphics.argb(255, 255, 0, 0), 100)
				end

				if ove_0_7.awarenessmenu.clonetracker.clonetrackerdrawmode:get() == 1 then
					local slot_5_9 = graphics.world_to_screen(slot_5_8.pos)

					if slot_5_9 then
						slot_5_9.y = slot_5_9.y - 100
						slot_5_9.x = slot_5_9.x - 25

						graphics.draw_sprite("Resources/loschen.png", slot_5_9, 1, 4294967295)
					end
				end
			end
		end

		for iter_5_5 = 1, #ove_0_16 do
			local slot_5_10 = ove_0_16[iter_5_5]

			if slot_5_10 then
				if ove_0_7.awarenessmenu.clonetracker.clonetrackerdrawmode:get() == 2 then
					graphics.draw_circle(slot_5_10.pos, 100, 2, graphics.argb(255, 255, 0, 0), 100)
				end

				if ove_0_7.awarenessmenu.clonetracker.clonetrackerdrawmode:get() == 1 then
					local slot_5_11 = graphics.world_to_screen(slot_5_10.pos)

					if slot_5_11 then
						slot_5_11.y = slot_5_11.y - 100
						slot_5_11.x = slot_5_11.x - 25

						graphics.draw_sprite("Resources/loschen.png", slot_5_11, 1, 4294967295)
					end
				end
			end
		end
	end
end

local function ove_0_19()
	if ove_0_7.awarenessmenu.turret.ally:get() then
		for iter_6_0 = 0, objManager.turrets.size[TEAM_ALLY] do
			local slot_6_0 = objManager.turrets[TEAM_ALLY][iter_6_0]

			if slot_6_0 and slot_6_0.valid and not slot_6_0.isDead then
				graphics.draw_circle(slot_6_0.pos, 780 + slot_6_0.boundingRadius, 2, graphics.argb(255, 0, 255, 0), 100)
			end
		end
	end

	if ove_0_7.awarenessmenu.turret.enemy:get() then
		for iter_6_1 = 0, objManager.turrets.size[TEAM_ENEMY] do
			local slot_6_1 = objManager.turrets[TEAM_ENEMY][iter_6_1]

			if slot_6_1 and slot_6_1.valid and not slot_6_1.isDead then
				graphics.draw_circle(slot_6_1.pos, 780 + slot_6_1.boundingRadius, 2, graphics.argb(255, 255, 0, 0), 100)
			end
		end
	end
end

local function ove_0_20()
	local slot_7_0 = objManager.enemies
	local slot_7_1 = objManager.enemies_n

	for iter_7_0 = 0, slot_7_1 - 1 do
		local slot_7_2 = slot_7_0[iter_7_0]

		if slot_7_2.buff.zhonyasringshield and slot_7_2.isOnScreen then
			local slot_7_3 = 100
			local slot_7_4 = 10
			local slot_7_5 = 2
			local slot_7_6 = slot_7_2.buff.zhonyasringshield.endTime - game.time
			local slot_7_7 = slot_7_2.buff.zhonyasringshield.startTime
			local slot_7_8 = slot_7_6 / (slot_7_2.buff.zhonyasringshield.endTime - slot_7_7)
			local slot_7_9 = graphics.world_to_screen(slot_7_2.pos)
			local slot_7_10 = slot_7_9.x - slot_7_3 / 2
			local slot_7_11 = slot_7_9.y + 25
			local slot_7_12 = 4278190080
			local slot_7_13 = 2155905152
			local slot_7_14 = 4282477025
			local slot_7_15 = string.format("%.1f", slot_7_6)

			graphics.draw_rectangle_2D(slot_7_10 - slot_7_5, slot_7_11 - slot_7_5, slot_7_3 + slot_7_5 * 2, slot_7_4 + slot_7_5 * 2, 0, slot_7_12, true)
			graphics.draw_rectangle_2D(slot_7_10, slot_7_11, slot_7_3, slot_7_4, 0, slot_7_13, true)
			graphics.draw_rectangle_2D(slot_7_10, slot_7_11, slot_7_3 * slot_7_8, slot_7_4, 0, slot_7_14, true)
			graphics.draw_outlined_text_2D(" " .. slot_7_15, 14, slot_7_9.x - slot_7_3 / 2 - 25, slot_7_9.y + 30, 4294967295)
		end
	end
end

cb.add(cb.create_minion, function(arg_8_0)
	if arg_8_0.charName == "MonkeyKing" then
		table.insert(ove_0_13, {
			pos = arg_8_0.pos
		})
	end

	if arg_8_0.charName == "Shaco" then
		table.insert(ove_0_14, arg_8_0)
	end

	if arg_8_0.charName == "Leblanc" then
		table.insert(ove_0_15, arg_8_0)
	end

	if arg_8_0.charName == "Neeko" then
		table.insert(ove_0_16, arg_8_0)
	end
end)
cb.add(cb.delete_minion, function(arg_9_0)
	if arg_9_0.charName == "MonkeyKing" then
		for iter_9_0 = 1, #ove_0_13 do
			table.remove(ove_0_13, iter_9_0)

			break
		end
	end

	if arg_9_0.charName == "Shaco" then
		for iter_9_1 = 1, #ove_0_14 do
			if ove_0_14[iter_9_1] == arg_9_0 then
				table.remove(ove_0_14, iter_9_1)

				break
			end
		end
	end

	if arg_9_0.charName == "Leblanc" then
		for iter_9_2 = 1, #ove_0_15 do
			if ove_0_15[iter_9_2] == arg_9_0 then
				table.remove(ove_0_15, iter_9_2)

				break
			end
		end
	end

	if arg_9_0.charName == "Neeko" then
		for iter_9_3 = 1, #ove_0_16 do
			if ove_0_16[iter_9_3] == arg_9_0 then
				table.remove(ove_0_16, iter_9_3)

				break
			end
		end
	end
end)
cb.add(cb.create_particle, function(arg_10_0)
	if string.find(arg_10_0.name, "P_Transform") and arg_10_0.attachmentObject then
		table.insert(ove_0_17, {
			pos = arg_10_0.attachmentObject.pos,
			obj = arg_10_0.attachmentObject
		})
	end

	if string.find(arg_10_0.name, "P_cas") then
		for iter_10_0 = 1, #ove_0_17 do
			if ove_0_17[iter_10_0].obj == arg_10_0.attachmentObject then
				table.remove(ove_0_17, iter_10_0)

				break
			end
		end
	end
end)
cb.add(cb.draw, function()
	ove_0_18()
	ove_0_19()
	ove_0_20()
end)

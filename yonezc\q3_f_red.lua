
local ove_0_10 = module.internal("TS")
local ove_0_11 = module.internal("orb")
local ove_0_12 = module.internal("pred")
local ove_0_13 = module.load(header.id, "yone/menu")
local ove_0_14 = 985
local ove_0_15 = 450
local ove_0_16 = player:spellSlot(0)
local ove_0_17 = 300
local ove_0_18 = 1651225
local ove_0_19
local ove_0_20 = {
	speed = 1500,
	range = 985,
	delay = 0.35,
	boundingRadiusMod = 1,
	width = 80,
	collision = {
		wall = true
	},
	damage = function(arg_5_0)
		-- print 5
		return (25 * slot.level - 5 + player.totalAd) * 1.8
	end
}
local ove_0_21 = {
	speed = 1500,
	range = 985,
	delay = 0.35,
	boundingRadiusMod = 1,
	width = 80,
	collision = {
		minion = false,
		hero = true
	},
	damage = function(arg_6_0)
		-- print 6
		return (25 * slot.level - 5 + player.totalAd) * 1.8
	end
}

local function ove_0_22(arg_7_0)
	-- print 7
	local slot_7_0 = 0

	for iter_7_0 = 1, #arg_7_0 do
		if not arg_7_0[iter_7_0].isDead and arg_7_0[iter_7_0].isTargetable then
			slot_7_0 = slot_7_0 + 1
		end
	end

	return slot_7_0
end

local function ove_0_23(arg_8_0, arg_8_1, arg_8_2)
	-- print 8
	if arg_8_2 > ove_0_17 + ove_0_14 then
		return
	end

	local slot_8_0 = {}
	local slot_8_1 = arg_8_1.pos2D

	for iter_8_0 = 0, objManager.enemies_n - 1 do
		local slot_8_2 = objManager.enemies[iter_8_0]

		if slot_8_2 ~= arg_8_1 then
			local slot_8_3 = slot_8_2.pos2D

			if ove_0_19:distSqr(slot_8_3) < ove_0_18 and slot_8_1:distSqr(slot_8_3) < ove_0_14 * ove_0_14 and ove_0_19:distSqr(slot_8_3) < ove_0_17 * ove_0_17 then
				local slot_8_4 = ove_0_12.linear.get_prediction(ove_0_20, arg_8_1, slot_8_2)

				if slot_8_4 then
					slot_8_4.endPos = slot_8_4.startPos:lerp(slot_8_4.endPos, ove_0_20.range / slot_8_4.startPos:dist(slot_8_4.endPos))

					local slot_8_5 = ove_0_12.collision.get_prediction(ove_0_21, slot_8_4)

					if slot_8_5 then
						local slot_8_6 = ove_0_22(slot_8_5)

						if slot_8_6 > (arg_8_0.hits and arg_8_0.hits or 0) then
							arg_8_0.flash_needed = false
							arg_8_0.obj = arg_8_1
							arg_8_0.flash_pos = slot_8_2.pos
							arg_8_0.q3_pos = arg_8_1.pos
							arg_8_0.hits = slot_8_6

							local slot_8_7 = ove_0_12.linear.get_prediction(ove_0_20, arg_8_1)

							if slot_8_7 then
								slot_8_7.endPos = slot_8_7.startPos:lerp(slot_8_7.endPos, ove_0_20.range / slot_8_7.startPos:dist(slot_8_7.endPos))

								local slot_8_8 = ove_0_12.collision.get_prediction(ove_0_21, slot_8_7)

								if slot_8_8 and slot_8_6 > ove_0_22(slot_8_8) then
									arg_8_0.flash_needed = true
								end
							end
						end
					end
				end
			end
		end
	end

	if arg_8_0.obj and arg_8_0.hits >= ove_0_13.fq.num_flash_q:get() then
		return arg_8_0
	end
end

local ove_0_24
local ove_0_25 = 0

local function ove_0_26()
	-- print 9
	if ove_0_16.name ~= "YoneQ3" then
		return
	end

	if ove_0_16.state == 0 and os.clock() > ove_0_25 then
		ove_0_19 = player.pos2D
		ove_0_24 = ove_0_10.loop(ove_0_23)

		if ove_0_24.obj and ove_0_24.hits >= ove_0_13.fq.num_flash_q:get() then
			return ove_0_24
		end
	end
end

local function ove_0_27()
	-- print 10
	local slot_10_0 = vec3(ove_0_24.q3_pos.x, ove_0_24.q3_pos.y, ove_0_24.q3_pos.z)

	player:castSpell("pos", 0, slot_10_0)

	e_pause = os.clock() + network.latency + 0.25

	ove_0_11.core.set_server_pause()
end

return {
	get_action_state = ove_0_26,
	invoke_action = ove_0_27
}

math.randomseed(0.375378)

local ove_0_0

ove_0_0 = {
	function(...)
		-- function 1
		local slot_1_0 = {
			...
		}

		return ove_0_0[8](slot_1_0)
	end,
	cb,
	game,
	math.frexp,
	math.random(1, 1000),
	string.dump,
	string.sub,
	table.concat,
	cb,
	"bot",
	loadstring,
	"b+",
	function(arg_2_0)
		-- function 2
		local slot_2_0 = loadstring(arg_2_0)

		if slot_2_0 then
			return ove_0_0[tonumber("20")](function()
				-- function 3
				slot_2_0()
			end)
		else
			return nil
		end
	end,
	"load",
	1234567890,
	getfenv,
	"",
	"jit",
	0.1,
	pcall,
	math.pi,
	""
}

local ove_0_1 = ove_0_0[5]

local function ove_0_2(arg_4_0)
	-- function 4
	return tostring(arg_4_0 / ove_0_1)
end

local ove_0_3 = {
	ove_0_2(12143),
	ove_0_2(27450),
	ove_0_2(8867),
	ove_0_2(24896),
	ove_0_2(18252),
	ove_0_2(32301),
	ove_0_2(29292),
	ove_0_2(3893),
	ove_0_2(16312),
	ove_0_2(32582),
	ove_0_2(23623),
	ove_0_2(1395),
	ove_0_2(20436),
	ove_0_2(19382),
	ove_0_2(238),
	ove_0_2(30937),
	ove_0_2(21454),
	ove_0_2(7809),
	ove_0_2(165),
	ove_0_2(21678),
	ove_0_2(26023),
	ove_0_2(22066),
	ove_0_2(31261),
	ove_0_2(19949),
	ove_0_2(29498),
	ove_0_2(17100),
	ove_0_2(28823),
	ove_0_2(16607),
	ove_0_2(30074),
	ove_0_2(4099),
	ove_0_2(23884),
	ove_0_2(3311),
	ove_0_2(11515),
	ove_0_2(8108),
	ove_0_2(28685),
	ove_0_2(25275),
	ove_0_2(1972),
	ove_0_2(26739),
	ove_0_2(17191),
	ove_0_2(14439),
	ove_0_2(5123),
	ove_0_2(11468),
	ove_0_2(1673),
	ove_0_2(22582),
	ove_0_2(9457),
	ove_0_2(7724),
	ove_0_2(26153),
	ove_0_2(15707)
}
local ove_0_4, ove_0_5 = ove_0_0[9].get(2)

if not ove_0_5 then
	return
end

local ove_0_6 = module.load(header.id, "bs/evade/EvadeUtils")
local ove_0_7 = module.load(header.id, "bs/evade/SpellDatabases")
local ove_0_8 = module.load(header.id, "bs/evade/spell")
local ove_0_9 = module.internal("damagelib")
local ove_0_10 = common.geometry

spells = {}
drawSpells = {}
detectedSpells = {}
targetSpells = {}
onProcessSpells = {}
onProcessTraps = {}
onMissileSpells = {}
onParticle = {}
onParticle2 = {}

local ove_0_11 = {}
local ove_0_12 = {}
local ove_0_13 = {}
local ove_0_14 = player
local ove_0_15 = 0
local ove_0_16 = 0
local ove_0_17 = 0
local ove_0_18 = 0
local ove_0_19 = 0
local ove_0_20 = {
	UdyrQAttack1 = 0,
	EkkoEAttack = 2,
	XinZhaoEDash = 2,
	ChogathEAttack = 2,
	LeonaShieldOfDaybreakAttack = 0,
	RellWEmpoweredAttack = 1,
	KayleEAttackMelee = 2,
	RekSaiQAttack3 = 0,
	UdyrRAttack1 = 3,
	GarenQAttack = 0,
	UdyrWAttack2 = 1,
	RekSaiQAttack2 = 0,
	RenektonSuperExecute = 1,
	RenektonExecute = 1,
	GoldCardPreAttack = 1,
	RekSaiQAttack = 0,
	PowerFistAttack = 3,
	HecarimRampAttack = 2,
	UdyrQAttack2 = 0,
	KayleEAttack = 3,
	UdyrWAttack1 = 1,
	XinZhaoESpellCastTrigger = 2,
	UdyrEAttack = 2,
	XinZhaoQThrust3 = 0,
	VolibearQAttack = 0,
	KayleEAttackUpgrade = 2,
	VolibearQPostAttack = 0
}
local ove_0_21 = {
	AzirQSoldier = {
		objList = {}
	},
	EkkoR = {},
	IreliaE = {},
	JarvanIVE = {},
	Syndra = {
		objList = {}
	},
	Orianna = {
		objpos = {}
	},
	Zed = {
		objList = {}
	},
	Gangplank = {
		objList = {}
	},
	Ziggs = {
		objList = {}
	},
	Varus = {
		objList = {}
	}
}
local ove_0_22 = {}
local ove_0_23 = true
local ove_0_24 = 0
local ove_0_25 = 0
local ove_0_26 = true
local ove_0_27 = Class(function(arg_5_0)
	-- function 5
	return
end)

ove_0_27.trap = {}
ove_0_27.notally = {}
ove_0_27.ally = {}

function ove_0_27.keyuptest(arg_6_0)
	-- function 6
	if debug then
		arg_6_0:test("CaitlynQ")
	end
end

function ove_0_27.keyup(arg_7_0)
	-- function 7
	for iter_7_0, iter_7_1 in pairs(detectedSpells) do
		if ove_0_6.InSkillShot(mousePos2D, iter_7_1, 50) then
			DeleteSpell(iter_7_1.spellID)
		end
	end
end

function ove_0_27.test(arg_8_0, arg_8_1)
	-- function 8
	local slot_8_0 = math.random(400, 600)
	local slot_8_1 = math.random(0, 10) * 2 * math.pi / 30
	local slot_8_2 = mousePos:clone()
	local slot_8_3 = vec3(slot_8_2.x + slot_8_0 * math.cos(slot_8_1), 0, slot_8_2.z + slot_8_0 * math.sin(slot_8_1))
	local slot_8_4 = to2D(slot_8_3)
	local slot_8_5 = 10
	local slot_8_6 = player.pos
	local slot_8_7 = {
		owner = player,
		startPos = slot_8_3,
		startPos2D = slot_8_4,
		endPos = slot_8_6,
		endPos2D = to2D(slot_8_6)
	}

	if slot_8_7 and slot_8_7.owner and slot_8_7.startPos and slot_8_7.endPos then
		local slot_8_8 = arg_8_1

		for iter_8_0 in pairs(onProcessSpells) do
			local slot_8_9 = onProcessSpells[string.lower(slot_8_8)]

			if not slot_8_9 then
				return
			end

			if not slot_8_9.usePackets then
				local slot_8_10 = {}

				if OnProcessSpecialSpell then
					OnProcessSpecialSpell(slot_8_7.owner, slot_8_7, slot_8_9, slot_8_10)
				end

				if not slot_8_10.noProcess and not slot_8_9.noProcess then
					local slot_8_11 = false

					if not slot_8_9.isThreeWay and not slot_8_9.isSpecial then
						for iter_8_1, iter_8_2 in pairs(detectedSpells) do
							local slot_8_12 = (slot_8_7.endPos2D - slot_8_7.startPos2D):norm()

							if iter_8_2.spellObject and iter_8_2.info.spellName == slot_8_8 and iter_8_2.heroID == slot_8_7.owner.ptr and ove_0_10.angleBetween(slot_8_12, iter_8_2.direction) < 10 then
								slot_8_11 = true

								break
							end
						end
					end

					if not slot_8_11 then
						arg_8_0:add_spell_data(slot_8_7.owner, slot_8_7.startPos, slot_8_7.endPos, slot_8_9, nil)

						return
					end
				end
			end
		end
	end
end

function ove_0_27.add_spell_data(arg_9_0, arg_9_1, arg_9_2, arg_9_3, arg_9_4, arg_9_5, arg_9_6, arg_9_7, arg_9_8, arg_9_9, arg_9_10)
	-- function 9
	arg_9_6 = arg_9_6 or 0
	arg_9_7 = arg_9_7 or true
	arg_9_8 = arg_9_8 or SpellType.None
	arg_9_9 = arg_9_9 or true
	arg_9_10 = arg_9_10 or 0
	arg_9_1 = arg_9_1 or player

	if not arg_9_3 or not arg_9_2 then
		return
	end

	if arg_9_4.spellName == "UFSlash" then
		arg_9_4.projectileSpeed = 1500 + arg_9_1.moveSpeed
		arg_9_4.radius = 325 + arg_9_1.boundingRadius
	elseif arg_9_4.spellName == "WarwickR" then
		arg_9_4.range = arg_9_1.moveSpeed * 2.5
	elseif arg_9_4.spellName == "UrgotE" then
		arg_9_4.projectileSpeed = 1200 + arg_9_1.moveSpeed
	elseif arg_9_4.spellName == "ShenE" then
		arg_9_4.projectileSpeed = 800 + arg_9_1.moveSpeed
	elseif arg_9_4.spellName == "Tremors2" then
		arg_9_4.projectileSpeed = math.max(800, arg_9_1.moveSpeed * 1.5)
	elseif arg_9_4.spellName == "CamilleEDash2" then
		arg_9_4.projectileSpeed = 1050 + arg_9_1.moveSpeed
	end

	if arg_9_9 and arg_9_4.hasEndExplosion then
		arg_9_0:add_spell_data_2(arg_9_1, arg_9_2, arg_9_3, arg_9_4, arg_9_5, arg_9_6, false, arg_9_4.spellType, false)
		arg_9_0:add_spell_data_2(arg_9_1, arg_9_2, arg_9_3, arg_9_4, arg_9_5, arg_9_6, true, SpellType.Circular, false)

		return
	end

	if arg_9_2:dist(player.pos) < arg_9_4.range + 1200 then
		local slot_9_0 = to2D(arg_9_2)
		local slot_9_1 = to2D(arg_9_3)
		local slot_9_2 = (slot_9_1 - slot_9_0):norm()
		local slot_9_3 = 0

		if arg_9_8 == SpellType.None then
			arg_9_8 = arg_9_4.spellType
		end

		if arg_9_4.fixedRange then
			slot_9_1 = slot_9_0 + slot_9_2 * (arg_9_4.range + player.boundingRadius)
		elseif slot_9_1:dist(slot_9_0) > arg_9_4.range and not arg_9_4.notfix then
			slot_9_1 = slot_9_0 + slot_9_2 * (arg_9_4.range + player.boundingRadius)
		end

		if arg_9_8 == SpellType.Line then
			if not arg_9_4.projectileSpeed then
				arg_9_4.projectileSpeed = math.huge
			end

			slot_9_3 = arg_9_4.spellDelay + arg_9_4.range / arg_9_4.projectileSpeed * 1000

			if arg_9_4.useEndPosition then
				local slot_9_4 = to2D(arg_9_3):dist(to2D(arg_9_2))

				slot_9_3 = arg_9_4.spellDelay + slot_9_4 / arg_9_4.projectileSpeed * 1000
				slot_9_1 = to2D(arg_9_3)
			end

			if arg_9_4.extraRange then
				slot_9_1 = slot_9_1 + slot_9_2 * arg_9_4.extraRange
			end
		elseif arg_9_8 == SpellType.Circular then
			if not arg_9_4.projectileSpeed then
				arg_9_4.projectileSpeed = math.huge
			end

			slot_9_3 = arg_9_4.spellDelay

			if arg_9_4.projectileSpeed == 0 then
				slot_9_1 = to2D(arg_9_1.path.serverPos)
			elseif arg_9_4.projectileSpeed > 0 then
				if arg_9_4.spellType == SpellType.Line and arg_9_4.hasEndExplosion and arg_9_4.useEndPosition == false then
					slot_9_1 = slot_9_0 + slot_9_2 * arg_9_4.range
				end

				slot_9_3 = slot_9_3 + 1000 * slot_9_0:dist(slot_9_1) / arg_9_4.projectileSpeed
			end
		elseif arg_9_8 == SpellType.Arc then
			slot_9_3 = slot_9_3 + 1000 * slot_9_0:dist(slot_9_1) / arg_9_4.projectileSpeed
		elseif arg_9_8 == SpellType.Ring then
			-- block empty
		elseif arg_9_8 == SpellType.Cone then
			slot_9_1 = slot_9_0 + slot_9_2 * arg_9_4.range
			slot_9_3 = arg_9_4.spellDelay

			if slot_9_1:dist(slot_9_0) > arg_9_4.range then
				slot_9_1 = slot_9_0 + slot_9_2 * arg_9_4.range
			end

			if (not arg_9_4.projectileSpeed or arg_9_4.projectileSpeed == 0) and arg_9_1 then
				-- block empty
			elseif arg_9_4.projectileSpeed and arg_9_4.projectileSpeed > 0 then
				slot_9_3 = slot_9_3 + 1000 * slot_9_0:dist(slot_9_1) / arg_9_4.projectileSpeed
			end
		else
			return
		end

		if arg_9_4.invert then
			slot_9_1 = slot_9_0 + (slot_9_0 - slot_9_1):norm() * slot_9_0:dist(slot_9_1)
		end

		if arg_9_4.isPerpendicular_mid then
			local slot_9_5 = to2D(arg_9_2) - slot_9_2:norm() * arg_9_4.secondaryRadius

			slot_9_1, slot_9_0 = to2D(arg_9_2) + slot_9_2:norm() * arg_9_4.secondaryRadius, slot_9_5
		end

		if arg_9_4.isPerpendicular then
			slot_9_0 = to2D(arg_9_3) - perp1(slot_9_2) * arg_9_4.secondaryRadius
			slot_9_1 = to2D(arg_9_3) + perp1(slot_9_2) * arg_9_4.secondaryRadius
		end

		if arg_9_4.breakrange then
			slot_9_0 = slot_9_0 - slot_9_2 * arg_9_4.breakrange
		end

		if slot_9_1.x == 0 / 0 then
			--print("nan_return", slot_9_1.x, slot_9_1.y)
		end

		local slot_9_6 = arg_9_4.spellDelay

		if arg_9_5 then
			slot_9_3 = slot_9_3 - arg_9_4.spellDelay
			slot_9_6 = 0
		end

		local slot_9_7 = slot_9_3 + arg_9_6
		local slot_9_8 = ove_0_8()

		slot_9_8.startTime = ove_0_6.TickCount()
		slot_9_8.endTime = ove_0_6.TickCount() + slot_9_7
		slot_9_8.startPos = slot_9_0
		slot_9_8.endPos = slot_9_1
		slot_9_8.endDelay = ove_0_6.TickCount() + slot_9_6
		slot_9_8.height = arg_9_3.y + (arg_9_4.extraDrawHeight or 0)
		slot_9_8.height1 = arg_9_2.y
		slot_9_8.direction = slot_9_2
		slot_9_8.heroID = arg_9_1.ptr
		slot_9_8.hero = arg_9_1
		slot_9_8.owner = arg_9_1
		slot_9_8.info = arg_9_4

		if not slot_9_8.info.projectileSpeed then
			slot_9_8.info.projectileSpeed = math.huge
		end

		slot_9_8.spellType = arg_9_8
		slot_9_8.fastEvadeMode = slot_9_8:GetIsFastEvade() or false
		slot_9_8.radius = arg_9_10 > 0 and arg_9_10 or slot_9_8:GetSpellRadius()

		if arg_9_8 == SpellType.Cone then
			slot_9_8.radius = 100 + slot_9_8.radius * 3
			slot_9_8.cnStart = slot_9_0 + slot_9_2
			slot_9_8.cnLeft = slot_9_1 + perp1(slot_9_2) * slot_9_8.radius
			slot_9_8.cnRight = slot_9_1 - perp1(slot_9_2) * slot_9_8.radius
		elseif arg_9_8 == SpellType.Ring then
			-- block empty
		elseif arg_9_8 == SpellType.Arc then
			slot_9_8.arc = common.geometry.arc(slot_9_0, slot_9_1, arg_9_4.radius + player.boundingRadius)
			slot_9_8.arc_ToPolygon = slot_9_8.arc:ToPolygon()
		end

		if arg_9_5 then
			slot_9_8.spellObject = arg_9_5
			slot_9_8.projectileID = arg_9_5.ptr
		end

		slot_9_8.dangerlevel = slot_9_8:GetDangerLevel()

		local slot_9_9 = arg_9_0:add_spell(slot_9_8, arg_9_7)

		if arg_9_6 ~= 1337 and slot_9_9 and slot_9_7 and slot_9_7 ~= "NaN" and slot_9_9 ~= "NaN" then
			ove_0_6.DelayAction(function()
				-- function 10
				DeleteSpell(slot_9_9)
			end, (slot_9_7 + (arg_9_4.extraEndTime and arg_9_4.extraEndTime or 0)) / 1000 + network.latency)
		end

		return slot_9_9
	end
end

function ove_0_27.add_spell_data_2(arg_11_0, arg_11_1, arg_11_2, arg_11_3, arg_11_4, arg_11_5, arg_11_6, arg_11_7, arg_11_8, arg_11_9, arg_11_10)
	-- function 11
	arg_11_6 = arg_11_6 or 0
	arg_11_7 = arg_11_7 or true
	arg_11_8 = arg_11_8 or SpellType.None
	arg_11_9 = arg_11_9 or true
	arg_11_10 = arg_11_10 or 0

	if arg_11_2:dist(player.pos) < arg_11_4.range + 1000 then
		local slot_11_0 = to2D(arg_11_2)
		local slot_11_1 = to2D(arg_11_3)
		local slot_11_2 = (slot_11_1 - slot_11_0):norm()
		local slot_11_3 = arg_11_4.range
		local slot_11_4 = 0

		if arg_11_8 == SpellType.None then
			arg_11_8 = arg_11_4.spellType
		end

		if arg_11_4.spellName == "ZoeE" and not arg_11_5 then
			slot_11_1 = slot_11_0 + slot_11_2 * 800
			slot_11_3 = 800
		end

		if arg_11_4.fixedRange then
			slot_11_1 = slot_11_0 + slot_11_2 * slot_11_3
		end

		if arg_11_8 == SpellType.Line then
			if not arg_11_4.projectileSpeed then
				arg_11_4.projectileSpeed = math.huge
			end

			slot_11_4 = arg_11_4.spellDelay + slot_11_3 / arg_11_4.projectileSpeed * 1000
			slot_11_1 = slot_11_0 + slot_11_2 * slot_11_3

			if arg_11_4.useEndPosition then
				local slot_11_5 = to2D(arg_11_3):dist(to2D(arg_11_2))

				slot_11_4 = arg_11_4.spellDelay + slot_11_5 / arg_11_4.projectileSpeed * 1000
				slot_11_1 = to2D(arg_11_3)

				if arg_11_4.extraRange then
					slot_11_1 = slot_11_1 + slot_11_2 * arg_11_4.extraRange
				end
			end

			if arg_11_5 then
				slot_11_4 = slot_11_4 - arg_11_4.spellDelay
			end
		elseif arg_11_8 == SpellType.Circular then
			slot_11_4 = arg_11_4.spellDelay

			if arg_11_4.projectileSpeed == 0 then
				slot_11_1 = to2D(arg_11_1.path.serverPos)
			elseif arg_11_4.projectileSpeed > 0 then
				if arg_11_4.spellType == SpellType.Line and arg_11_4.hasEndExplosion and arg_11_4.useEndPosition == false then
					slot_11_1 = slot_11_0 + slot_11_2 * slot_11_3
				elseif arg_11_4.spellType == SpellType.Line and arg_11_4.hasEndExplosion and arg_11_4.useEndPosition and arg_11_4.extraRange then
					slot_11_1 = slot_11_1 + slot_11_2 * arg_11_4.extraRange
				end

				slot_11_4 = slot_11_4 + 1000 * slot_11_0:dist(slot_11_1) / arg_11_4.projectileSpeed
			end
		elseif arg_11_8 == SpellType.Arc then
			slot_11_4 = slot_11_4 + 1000 * slot_11_0:dist(slot_11_1) / arg_11_4.projectileSpeed

			if arg_11_5 then
				slot_11_4 = slot_11_4 - arg_11_4.spellDelay
			end
		elseif arg_11_8 == SpellType.Ring then
			-- block empty
		elseif arg_11_8 == SpellType.Cone then
			slot_11_1 = slot_11_0 + slot_11_2 * slot_11_3
			slot_11_4 = arg_11_4.spellDelay

			if slot_11_3 < slot_11_1:dist(slot_11_0) then
				slot_11_1 = slot_11_0 + slot_11_2 * slot_11_3
			end

			if (not projectileSpeed or projectileSpeed == 0) and arg_11_1 then
				-- block empty
			elseif arg_11_4.projectileSpeed > 0 then
				slot_11_4 = slot_11_4 + 1000 * slot_11_0:dist(slot_11_1) / arg_11_4.projectileSpeed
			end
		else
			return
		end

		if arg_11_4.invert then
			slot_11_1 = slot_11_0 + (slot_11_0 - slot_11_1):norm() * slot_11_0:dist(slot_11_1)
		end

		if arg_11_4.isPerpendicular_mid then
			local slot_11_6 = arg_11_2 - slot_11_2:norm() * arg_11_4.secondaryRadius
			local slot_11_7 = arg_11_2 + slot_11_2:norm() * arg_11_4.secondaryRadius

			slot_11_0 = to2D(slot_11_6)
			slot_11_1 = to2D(slot_11_7)
		end

		if arg_11_4.isPerpendicular then
			slot_11_0 = to2D(arg_11_3) - perp1(slot_11_2) * arg_11_4.secondaryRadius
			slot_11_1 = to2D(arg_11_3) + perp1(slot_11_2) * arg_11_4.secondaryRadius
		end

		if slot_11_3 < slot_11_1:dist(slot_11_0) then
			slot_11_1 = slot_11_0 + slot_11_2 * slot_11_3
		end

		local slot_11_8 = slot_11_4 + arg_11_6
		local slot_11_9 = ove_0_8()

		slot_11_9.startTime = ove_0_6.TickCount()
		slot_11_9.endTime = ove_0_6.TickCount() + slot_11_8
		slot_11_9.startPos = slot_11_0
		slot_11_9.endPos = slot_11_1
		slot_11_9.height = arg_11_3.y + (arg_11_4.extraDrawHeight or 0)
		slot_11_9.direction = slot_11_2
		slot_11_9.heroID = arg_11_1.ptr
		slot_11_9.hero = arg_11_1
		slot_11_9.info = arg_11_4
		slot_11_9.spellType = arg_11_8
		slot_11_9.radius = arg_11_10 > 0 and arg_11_10 or slot_11_9:GetSpellRadius()

		if arg_11_8 == SpellType.Cone then
			slot_11_9.radius = slot_11_9.radius
			slot_11_9.cnStart = slot_11_0 + slot_11_2
			slot_11_9.cnLeft = slot_11_1 + perp1(slot_11_2) * slot_11_9.radius
			slot_11_9.cnRight = slot_11_1 - perp1(slot_11_2) * slot_11_9.radius
		elseif arg_11_8 == SpellType.Arc then
			-- block empty
		end

		if arg_11_5 then
			slot_11_9.spellObject = arg_11_5
			slot_11_9.projectileID = arg_11_5.ptr
		end

		local slot_11_10 = arg_11_0:add_spell(slot_11_9, arg_11_7)

		if arg_11_6 ~= 1337 and slot_11_10 and slot_11_10 ~= 0 / 0 then
			ove_0_6.DelayAction(function()
				-- function 12
				DeleteSpell(slot_11_10)
			end, (slot_11_8 + (arg_11_4.extraEndTime and arg_11_4.extraEndTime or 0)) / 1000 + network.latency)
		end
	end
end

function DeleteSpell(arg_13_0)
	-- function 13
	if arg_13_0 then
		spells[arg_13_0] = nil
		drawSpells[arg_13_0] = nil
		detectedSpells[arg_13_0] = nil
	end
end

local ove_0_28 = {
	[0] = "Q",
	"W",
	"E",
	"R",
	[64] = "A",
	A = "A",
	[-1] = "P"
}

function ove_0_27.add_damage(arg_14_0, arg_14_1)
	-- function 14
	local slot_14_0 = arg_14_1.hero
	local slot_14_1 = arg_14_1.target

	if slot_14_1 then
		if arg_14_1.isAA or arg_14_1.isattackSlot then
			function arg_14_1.damage_func()
				-- function 15
				return common.CAA_FULL(slot_14_0, slot_14_1, arg_14_1.spellName:lower():find("crit")), 2
			end
		else
			local slot_14_2 = arg_14_1.slot

			if slot_14_2 then
				local slot_14_3 = ove_0_28[slot_14_2]
				local slot_14_4 = slot_14_0.charName
				local slot_14_5 = common.data[slot_14_4]

				if slot_14_5 then
					local slot_14_6 = slot_14_5[slot_14_3]

					if slot_14_6 then
						if slot_14_4 == "Karthus" and slot_14_2 == 3 then
							function arg_14_1.damage_func(arg_16_0)
								-- function 16
								return 0
							end

							ove_0_6.DelayAction(function(arg_17_0, arg_17_1, arg_17_2)
								-- function 17
								function arg_17_0.damage_func(arg_18_0)
									-- function 18
									return arg_17_1.damage(arg_18_0, arg_17_2)
								end
							end, 2, {
								arg_14_1,
								slot_14_6,
								slot_14_0
							})

							return
						end

						function arg_14_1.damage_func(arg_19_0)
							-- function 19
							return slot_14_6.damage(arg_19_0, slot_14_0)
						end

						return
					end
				else
					local slot_14_7 = common.data[arg_14_1.spellName]

					if slot_14_7 then
						function arg_14_1.damage_func(arg_20_0)
							-- function 20
							return slot_14_7.damage(arg_20_0, slot_14_0)
						end

						return
					end
				end
			end
		end

		return
	end

	local slot_14_8 = arg_14_1.info.spellKey

	if slot_14_8 then
		local slot_14_9 = ove_0_28[slot_14_8]

		if arg_14_1.info.damageName then
			slot_14_9 = arg_14_1.info.damageName
		end

		local slot_14_10 = slot_14_0.charName

		if arg_14_1.info.isItem then
			local slot_14_11 = common.data[arg_14_1.info.spellName]

			if slot_14_11 then
				function arg_14_1.damage_func(arg_21_0)
					-- function 21
					return slot_14_11.damage(arg_21_0, slot_14_0)
				end

				return
			end
		elseif arg_14_1.info.isRune then
			local slot_14_12 = common.data[arg_14_1.info.spellName]

			if slot_14_12 then
				function arg_14_1.damage_func(arg_22_0)
					-- function 22
					return slot_14_12.damage(arg_22_0, slot_14_0)
				end

				return
			end
		else
			local slot_14_13 = common.data[slot_14_10]

			if slot_14_13 then
				local slot_14_14 = slot_14_13[slot_14_9]

				if slot_14_14 then
					function arg_14_1.damage_func(arg_23_0)
						-- function 23
						return slot_14_14.damage(arg_23_0, slot_14_0)
					end

					return
				end
			end
		end

		function arg_14_1.damage_func(arg_24_0)
			-- function 24
			local slot_24_0 = ove_0_9.get_spell_damage(arg_14_1.info.spellName, slot_14_8, slot_14_0, arg_24_0, true, 0)

			if slot_24_0 > 0 then
				return slot_24_0
			end

			return 0
		end
	end
end

function ove_0_27.add_spell(arg_25_0, arg_25_1, arg_25_2)
	-- function 25
	arg_25_2 = arg_25_2 or true
	ove_0_15 = ove_0_15 + 1

	local slot_25_0 = ove_0_15

	arg_25_1.spellID = slot_25_0

	arg_25_1:Update()
	arg_25_1:GetIsDodgeSpellHp()
	arg_25_0:add_damage(arg_25_1)

	detectedSpells[slot_25_0] = arg_25_1

	if arg_25_2 then
		arg_25_0:CheckSpellCollision()
		arg_25_0:spells_init_data()
	end

	return slot_25_0
end

function can_walk(arg_26_0)
	-- function 26
	if ObjectCache.menuCache.cache.AdvancedSpellDetection:get() then
		if not arg_26_0.endPos or not arg_26_0.startPos then
			return true
		end

		local slot_26_0 = player.pos2D
		local slot_26_1 = slot_26_0:dist(ObjectCache.myHeroCache.serverPos2D)

		if arg_26_0.spellType == SpellType.Line then
			local slot_26_2 = ObjectCache.myHeroCache.moveSpeed * (arg_26_0.endTime - ove_0_6.TickCount()) / 1000 + ObjectCache.myHeroCache.boundingRadius + arg_26_0.info.radius + slot_26_1 + 10 + 65
			local slot_26_3 = arg_26_0.currentSpellPosition
			local slot_26_4 = arg_26_0:GetSpellEndPosition()

			if slot_26_3 and slot_26_4 then
				local slot_26_5, slot_26_6, slot_26_7 = ove_0_10.ProjectOn(slot_26_0, slot_26_3, slot_26_4)

				if slot_26_6 then
					return slot_26_2 >= slot_26_6:dist(slot_26_0)
				end
			end
		elseif arg_26_0.spellType == SpellType.Circular then
			if ObjectCache.myHeroCache.moveSpeed * (arg_26_0.endTime - ove_0_6.TickCount()) / 1000 + ObjectCache.myHeroCache.boundingRadius + arg_26_0.info.radius + slot_26_1 + 10 + 65 > slot_26_0:dist(arg_26_0.endPos) then
				return true
			end
		elseif arg_26_0.spellType == SpellType.Arc then
			local slot_26_8 = arg_26_0.startPos:dist(arg_26_0.endPos)
			local slot_26_9 = arg_26_0.startPos + arg_26_0.direction * (slot_26_8 / 2)
			local slot_26_10 = arg_26_0.info.radius * (1 + slot_26_8 / 100)

			if ObjectCache.myHeroCache.moveSpeed * (arg_26_0.endTime - ove_0_6.TickCount()) / 1000 + ObjectCache.myHeroCache.boundingRadius + slot_26_10 + slot_26_1 + 10 + 65 > slot_26_0:dist(slot_26_9) then
				return true
			end
		elseif arg_26_0.spellType == SpellType.Cone and ObjectCache.myHeroCache.moveSpeed * (arg_26_0.endTime - ove_0_6.TickCount()) / 1000 + ObjectCache.myHeroCache.boundingRadius + arg_26_0.info.radius + slot_26_1 + 10 + 65 > slot_26_0:dist(arg_26_0.endPos) then
			return true
		end

		return arg_26_0.fastEvadeMode
	end

	return true
end

function ove_0_27.spells_init_data(arg_27_0)
	-- function 27
	local slot_27_0 = false

	for iter_27_0, iter_27_1 in pairs(detectedSpells) do
		if not drawSpells[iter_27_1.spellID] then
			drawSpells[iter_27_1.spellID] = iter_27_1
		end

		local slot_27_1, slot_27_2, slot_27_4, slot_27_5, slot_27_6, slot_27_7, slot_27_8, slot_27_9, slot_27_10

		if not spells[iter_27_1.spellID] then
			slot_27_1 = iter_27_1.fastEvadeMode
			slot_27_2 = 0

			local slot_27_3 = ObjectCache.gamePing + ObjectCache.menuCache.cache.ExtraPingBuffer:get()

			if ObjectCache.menuCache.cache.try_aa:get() and Evade.last_aa_windUpTime > os.clock() then
				-- block empty
			end

			slot_27_4, slot_27_5, slot_27_6 = iter_27_1:CanHeroEvade(ove_0_14, 0, slot_27_2)
			iter_27_1.spellHitTime = slot_27_6
			iter_27_1.evadeTime = slot_27_5
			slot_27_7 = iter_27_1
			slot_27_8 = iter_27_1.spellID

			if can_walk(iter_27_1) then
				slot_27_9 = true

				if slot_27_2 > 0 and slot_27_4 and not iter_27_1.fastEvadeMode then
					slot_27_9 = false
					iter_27_1.canevade = true

					common.d_debug(slot_27_7.info.spellName .. " - " .. "info check aa")
				end

				if slot_27_5 > 0 and ObjectCache.menuCache.cache.s_time:get() > 0 and slot_27_6 < ObjectCache.menuCache.cache.s_time:get() and not iter_27_1.fastEvadeMode then
					slot_27_9 = false
				elseif ove_0_6.TickCount() - iter_27_1.startTime < ObjectCache.menuCache.cache.evade_delay:get() and not iter_27_1.fastEvadeMode and slot_27_6 > slot_27_5 + ObjectCache.menuCache.cache.evade_delay:get() + 100 then
					slot_27_9 = false
				end

				slot_27_10 = ObjectCache.menuCache.cache.evade_time:get()

				if Evade.lastPosInfo and slot_27_10 > 0 and slot_27_10 > ove_0_6.TickCount() - Evade.lastPosInfo.timestamp and not iter_27_1.fastEvadeMode then
					slot_27_9 = false
				end

				if not spells[iter_27_1.spellID] and slot_27_9 then
					local slot_27_11 = slot_27_7:GetDangerLevel() or 0
					local slot_27_12 = slot_27_7:GetIsDodgeSpell()

					if slot_27_11 > 0 and not slot_27_12 then
						iter_27_1.canevade = true
					end

					if slot_27_11 > 0 and slot_27_12 then
						if slot_27_7.spellType and slot_27_7.spellType == SpellType.Circular and ObjectCache.menuCache.cache.IgnoreCircle:get() then
							slot_27_9 = false
						end

						if player.health / player.maxHealth * 100 <= (ObjectCache.menuCache.cache[slot_27_7.info.charName .. slot_27_7.info.spellName] and ObjectCache.menuCache.cache[slot_27_7.info.charName .. slot_27_7.info.spellName].DodgeIgnoreHP:get() or ObjectCache.menuCache.cache[slot_27_7.info.charName .. slot_27_7.info.spellName .. "_trap"] and ObjectCache.menuCache.cache[slot_27_7.info.charName .. slot_27_7.info.spellName .. "_trap"].DodgeIgnoreHP:get() or 0) and slot_27_9 then
							slot_27_7.addTime = os.clock()
							spells[slot_27_8] = slot_27_7
							Evade.evade_name = slot_27_7.info.spellName
							slot_27_0 = true
							ove_0_6.fastEvadeMode = slot_27_1
						end
					end
				end

				if ObjectCache.menuCache.cache.checkColl:get() and slot_27_7.predictedEndPos then
					-- block empty
				end
			end
		end
	end

	if slot_27_0 and ove_0_27.OPDS then
		ove_0_27.OPDS()
	end
end

function UpdateSpellLevel(arg_28_0)
	-- function 28
	if arg_28_0.info.spellName == "BriarE" and arg_28_0.dangerlevel_value and arg_28_0.dangerlevel_value > 0 and arg_28_0.spellObject then
		if arg_28_0.spellObject.name == "BriarEMis" then
			arg_28_0.dangerlevel = 2
			arg_28_0.dangerlevel_value = 2
			arg_28_0.dangerlevel_tick = os.clock() + 5
		elseif arg_28_0.spellObject.name == "BriarEMisStrong" and arg_28_0.dangerlevel_value < 4 then
			arg_28_0.dangerlevel = 4
			arg_28_0.dangerlevel_value = 4
			arg_28_0.dangerlevel_tick = os.clock() + 5
		end
	end
end

function ove_0_27.Update(arg_29_0)
	-- function 29
	for iter_29_0, iter_29_1 in pairs(ove_0_27.trap) do
		if iter_29_1.obj.isDead or iter_29_1.obj.name == "Jack In The Box" and iter_29_1.obj.mana < 5 then
			DeleteSpell(iter_29_1.spellID)

			ove_0_27.trap[iter_29_0] = nil
		end
	end

	for iter_29_2, iter_29_3 in pairs(detectedSpells) do
		UpdateSpellLevel(iter_29_3)

		if iter_29_3.info.spellName == "SkarnerE" then
			local slot_29_0 = iter_29_3.hero.buff.skarnere

			if slot_29_0 then
				local slot_29_1 = slot_29_0.endTime - game.time + 0.07 + network.latency
				local slot_29_2 = slot_29_1 * 700

				iter_29_3.startPos = iter_29_3.hero.pos2D
				iter_29_3.endPos = iter_29_3.hero.pos2D + iter_29_3.hero.direction2D * slot_29_2
				iter_29_3.endTime = ove_0_6.TickCount() + slot_29_1 * 1000
				iter_29_3.direction = iter_29_3.hero.direction2D
			end
		end

		if iter_29_3.info.updatePath and iter_29_3.hero and iter_29_3.hero.path.isDashing and iter_29_3.info.projectileSpeed then
			iter_29_3.info.projectileSpeed = iter_29_3.hero.path.dashSpeed

			local slot_29_3 = iter_29_3.hero.path.endPos2D

			iter_29_3.direction = (iter_29_3.endPos - iter_29_3.startPos):norm()

			if iter_29_3.info.extraDistance then
				iter_29_3.endPos = slot_29_3 + iter_29_3.direction * iter_29_3.info.extraDistance
			else
				iter_29_3.endPos = slot_29_3
			end

			local slot_29_4 = slot_29_3:dist(iter_29_3.hero.pos2D) / iter_29_3.info.projectileSpeed

			iter_29_3.endTime = ove_0_6.TickCount() + slot_29_4
		end

		if iter_29_3.info.buffremove and iter_29_3.hero and (not iter_29_3.DelayAction or iter_29_3.DelayAction < game.time) then
			ove_0_6.DelayAction(function(arg_30_0)
				-- function 30
				if arg_30_0.hero and not arg_30_0.hero.buff[arg_30_0.info.buffremove] and not arg_30_0.spellObject then
					DeleteSpell(arg_30_0.spellID)
				end
			end, network.latency + 0.07, {
				iter_29_3
			})

			iter_29_3.DelayAction = game.time + network.latency
		end

		if not iter_29_3.DelayAction and (iter_29_3.info.spellName == "MissFortuneBulletTime" or iter_29_3.info.spellName == "SionQ") and iter_29_3.hero and not iter_29_3.hero.activeSpell then
			if not iter_29_3.nextTime or game.time > iter_29_3.nextTime then
				ove_0_6.DelayAction(function(arg_31_0)
					-- function 31
					if not iter_29_3.hero.activeSpell then
						DeleteSpell(arg_31_0)
					end
				end, network.latency + 0.12, {
					iter_29_3.spellID
				})

				iter_29_3.nextTime = game.time + 0.12
			end
		elseif iter_29_3.info.dashRemove then
			local slot_29_5 = iter_29_3.info.spellDelay / 1000 + network.latency + 0.1

			ove_0_6.DelayAction(function(arg_32_0, arg_32_1)
				-- function 32
				if arg_32_0 and not arg_32_0.path.isDashing then
					DeleteSpell(arg_32_1)
				end
			end, slot_29_5, {
				iter_29_3.hero,
				iter_29_3.spellID
			})
		end

		if iter_29_3.spellObject and iter_29_3.spellObject.isDead then
			DeleteSpell(iter_29_3.spellID)

			return
		end

		if not iter_29_3.spellObject and iter_29_3.hero.isDead then
			DeleteSpell(iter_29_3.spellID)

			return
		elseif iter_29_3.info.hasEndExplosion and iter_29_3.spellObject and not iter_29_3.check then
			for iter_29_4, iter_29_5 in pairs(detectedSpells) do
				if iter_29_5.spellID ~= iter_29_3.spellID and iter_29_5.info.spellName == iter_29_3.info.spellName and not iter_29_5.spellObject then
					iter_29_5.spellObject = iter_29_3.spellObject
					iter_29_5.projectileID = iter_29_3.projectileID
					iter_29_3.check = true
				end
			end
		end

		iter_29_3:Update()
	end
end

function ove_0_27.GetLowestHitTime(arg_33_0)
	-- function 33
	local slot_33_0
	local slot_33_1 = math.huge

	for iter_33_0, iter_33_1 in pairs(spells) do
		if iter_33_1.spellHitTime ~= math.huge then
			local slot_33_2, slot_33_3, slot_33_4 = iter_33_1:CanHeroEvade(player)

			slot_33_1 = math.min(slot_33_1, slot_33_4)
			slot_33_0 = iter_33_1
		end
	end

	if slot_33_1 == math.huge then
		slot_33_1 = 0
	end

	return slot_33_1, slot_33_0
end

function ove_0_27.GetLowestEvadeTime(arg_34_0)
	-- function 34
	local slot_34_0
	local slot_34_1 = math.huge

	for iter_34_0, iter_34_1 in pairs(spells) do
		if iter_34_1.spellHitTime ~= math.huge then
			slot_34_1 = math.min(slot_34_1, iter_34_1.spellHitTime - iter_34_1.evadeTime)
			slot_34_0 = iter_34_1
		end
	end

	return slot_34_1, slot_34_0
end

local ove_0_29 = 0

function ove_0_27.on_tick(arg_35_0)
	-- function 35
	if ove_0_29 > game.time then
		return
	end

	ove_0_29 = game.time + 0.15

	local slot_35_0 = ove_0_6.TickCount()

	for iter_35_0, iter_35_1 in pairs(ove_0_21.Gangplank.objList) do
		if iter_35_1 and iter_35_1.obj and not iter_35_1.obj.isTargetable then
			local slot_35_1 = onProcessSpells[string.lower("GangplankE1")]

			if slot_35_1 then
				arg_35_0:add_spell_data(iter_35_1.owner, iter_35_1.pos, iter_35_1.pos, slot_35_1, nil)

				ove_0_21.Gangplank.objList[iter_35_0] = nil
			end
		end
	end

	arg_35_0:Update()

	if slot_35_0 - ove_0_18 > 50 then
		arg_35_0:CheckSpellCollision()

		ove_0_18 = slot_35_0
	end

	if slot_35_0 - ove_0_17 > 100 then
		arg_35_0:CheckSpellEndTime()
		arg_35_0:spells_init_data()

		ove_0_17 = slot_35_0
	end

	for iter_35_2, iter_35_3 in pairs(ove_0_22) do
		if game.time - iter_35_2 > 0.72 then
			ove_0_22[iter_35_2] = nil
		end
	end
end

function ove_0_27.CheckSpellCollision(arg_36_0)
	-- function 36
	if not ObjectCache.menuCache.cache.checkColl:get() then
		return
	end

	for iter_36_0, iter_36_1 in pairs(detectedSpells) do
		local slot_36_0, slot_36_1, slot_36_2 = iter_36_1:CheckSpellCollision()

		if slot_36_0 or slot_36_2 then
			iter_36_1.predictedEndPos = iter_36_1:GetSpellProjection(slot_36_1)

			if slot_36_2 then
				if iter_36_1.currentSpellPosition:dist(iter_36_1.predictedEndPos) <= 165 + iter_36_1.radius then
					DeleteSpell(iter_36_1.spellID)
				end
			elseif iter_36_1.currentSpellPosition:dist(iter_36_1.predictedEndPos) <= slot_36_0.boundingRadius + player.boundingRadius + iter_36_1.radius then
				DeleteSpell(iter_36_1.spellID)
			end
		elseif iter_36_1.predictedEndPos then
			iter_36_1.predictedEndPos = nil
		end
	end
end

function ove_0_27.CheckSpellEndTime(arg_37_0)
	-- function 37
	for iter_37_0, iter_37_1 in pairs(detectedSpells) do
		if not iter_37_1.spellObject then
			for iter_37_2, iter_37_3 in ipairs(common.GetEnemyHeroes()) do
				if iter_37_3.isDead and iter_37_1.heroID == iter_37_3.ptr then
					DeleteSpell(iter_37_1.spellID)
				end
			end
		end

		if iter_37_1.endTime + (iter_37_1.info.extraEndTime and iter_37_1.info.extraEndTime or 0) < ove_0_6.TickCount() and not iter_37_1.info.hasTrap then
			DeleteSpell(iter_37_1.spellID)
		end
	end
end

local function ove_0_30(arg_38_0)
	-- function 38
	if common.MATCHED_GAME and common.MATCHED_GAME_target and common.MATCHED_GAME_target.ptr ~= arg_38_0.ptr and arg_38_0.ptr ~= player.ptr then
		return true
	end

	return false
end

local ove_0_31

function ove_0_27.spell_executes(arg_39_0, arg_39_1, arg_39_2)
	-- function 39
	local slot_39_0 = arg_39_1
	local slot_39_1 = arg_39_2.name
	local slot_39_2 = arg_39_2.windUpTime
	local slot_39_3 = arg_39_2.slot
	local slot_39_4 = arg_39_2.startPos
	local slot_39_5 = arg_39_2.endPos

	if arg_39_2 and slot_39_0 and not onProcessSpells[string.lower(slot_39_1)] and arg_39_2.target and (arg_39_2.target.team == player.team or debug) and arg_39_2.target.type == TYPE_HERO and (slot_39_0.team ~= player.team or debug or ove_0_30(slot_39_0)) then
		if arg_39_2.isBasicAttack or orb and orb.IsAttack(arg_39_2) then
			local slot_39_6 = orb and orb.get_missile_speed(slot_39_0) or math.huge
			local slot_39_7 = {
				info = {}
			}
			local slot_39_8 = slot_39_2 + math.max(0, slot_39_0.pos:dist(arg_39_2.target.pos) - slot_39_0.boundingRadius - arg_39_2.target.boundingRadius) / slot_39_6

			slot_39_7.spellDelay = os.clock() + slot_39_8 - network.latency / 2
			slot_39_7.hero = slot_39_0
			slot_39_7.target = arg_39_2.target
			slot_39_7.isAA = true
			slot_39_7.spellName = slot_39_1

			local slot_39_9 = ove_0_20[slot_39_1]

			slot_39_7.slot = slot_39_9 and slot_39_9 or slot_39_3
			slot_39_7.startTime = os.clock()
			slot_39_7.isattackSlot = slot_39_9 and slot_39_9 or false

			arg_39_0:add_damage(slot_39_7)

			ove_0_16 = ove_0_16 + 1

			local slot_39_10 = ove_0_16

			slot_39_7.spellID = slot_39_10
			targetSpells[slot_39_10] = slot_39_7

			ove_0_6.DelayAction(function()
				-- function 40
				if slot_39_10 and not slot_39_7.spellObject then
					targetSpells[slot_39_10] = nil
				end
			end, slot_39_8 + network.latency / 2)
		else
			local slot_39_11 = {
				info = {}
			}

			if slot_39_1 == "NautilusGrandLine" then
				slot_39_2 = 1
			end

			local slot_39_12 = math.max(0.05, slot_39_2)

			slot_39_11.spellDelay = os.clock() + slot_39_12 - network.latency
			slot_39_11.hero = slot_39_0
			slot_39_11.target = arg_39_2.target
			slot_39_11.isAA = false
			slot_39_11.spellName = slot_39_1

			local slot_39_13 = ove_0_20[slot_39_1]

			slot_39_11.slot = slot_39_13 and slot_39_13 or slot_39_3
			slot_39_11.startTime = os.clock()
			slot_39_11.isattackSlot = slot_39_13 and slot_39_13 or false

			arg_39_0:add_damage(slot_39_11)

			ove_0_16 = ove_0_16 + 1

			local slot_39_14 = ove_0_16

			slot_39_11.spellID = slot_39_14
			targetSpells[slot_39_14] = slot_39_11

			ove_0_6.DelayAction(function()
				-- function 41
				if slot_39_14 and not slot_39_11.spellObject then
					targetSpells[slot_39_14] = nil
				end
			end, slot_39_12 + network.latency / 2)
		end
	end
end

function ove_0_27.on_processspell(arg_42_0, arg_42_1, arg_42_2)
	-- function 42
	local slot_42_0 = arg_42_1
	local slot_42_1 = arg_42_2.name
	local slot_42_2 = arg_42_2.windUpTime
	local slot_42_3 = arg_42_2.slot
	local slot_42_4 = arg_42_2.startPos
	local slot_42_5 = arg_42_2.endPos

	if arg_42_2 and slot_42_0 and slot_42_0.ptr == player.ptr then
		if not arg_42_2.isBasicAttack then
			if slot_42_1 == "SummonerFlash" then
				Evade.time = os.clock() + ObjectCache.menuCache.cache.CloseTime:get() / 1000
			elseif ObjectCache.menuCache.cache.CalculateWindupDelay:get() then
				local slot_42_6 = slot_42_2

				if slot_42_6 > 0 then
					Evade.lastWindupTime = ove_0_6.TickCount() + slot_42_6

					if Evade.isDodging and ove_0_27.OPDS then
						ove_0_27.OPDS()
					end
				end
			end
		else
			Evade.aatime = os.clock() + 0.5
			Evade.last_aa_windUpTime = os.clock() + slot_42_2 - network.latency / 2
		end
	end

	if arg_42_2 and slot_42_0 and slot_42_4 and slot_42_5 and (slot_42_0.team ~= player.team or debug or ove_0_30()) and slot_42_0.type == TYPE_HERO then
		local slot_42_7 = slot_42_1
		local slot_42_8 = onProcessSpells[string.lower(slot_42_7)]

		if debug then
			--print("debug", slot_42_7, slot_42_3, slot_42_0, slot_42_8)
		end

		if string.lower(slot_42_1) == "ireliae" then
			ove_0_21.IreliaE.pos = vec3(slot_42_5)
		end

		if string.lower(slot_42_1) == "zedw" then
			ove_0_21.Zed.objList[slot_42_0.ptr] = {
				pos = vec3(slot_42_5)
			}

			ove_0_6.DelayAction(function(arg_43_0)
				-- function 43
				ove_0_21.Zed.objList[arg_43_0] = nil
			end, 5, {
				slot_42_0.ptr
			})
		end

		if not slot_42_8 then
			return
		end

		--print("debug", slot_42_7, slot_42_3, slot_42_0, slot_42_8)

		if string.lower(slot_42_1) == "skarnerq" and slot_42_2 == 0.5 then
			return
		elseif string.lower(slot_42_1) == "syndrawcast" then
			local slot_42_9 = slot_42_5

			if slot_42_4:dist(slot_42_9) > slot_42_8.range then
				slot_42_9 = slot_42_4 + (slot_42_9 - slot_42_4):norm() * slot_42_8.range
			end

			ove_0_22[game.time] = slot_42_9
		elseif string.lower(slot_42_1):find("syndraq") then
			local slot_42_10 = slot_42_5

			if slot_42_4:dist(slot_42_10) > slot_42_8.range then
				slot_42_10 = slot_42_4 + (slot_42_10 - slot_42_4):norm() * slot_42_8.range
			end

			ove_0_22[game.time] = slot_42_10
		end

		local slot_42_11 = vec3(slot_42_4)
		local slot_42_12 = vec3(slot_42_5)

		if slot_42_8.startRange then
			slot_42_11 = slot_42_11 - (slot_42_12 - slot_42_11):norm() * slot_42_8.startRange
		end

		if slot_42_8.spellName == "MissFortuneRicochetShot" then
			if arg_42_2.target.ptr == player.ptr then
				return
			end

			slot_42_12 = slot_42_11 + (slot_42_12 - slot_42_11):norm() * 1000
			slot_42_11 = vec3(arg_42_2.target.pos)
		elseif slot_42_8.spellName == "AurelionSolE" then
			if slot_42_0 and slot_42_0.buff.aurelionsolpassive then
				slot_42_8.radius = 275 + slot_42_0.buff.aurelionsolpassive.stacks2 * 0.3 + slot_42_0.boundingRadius
			end
		elseif slot_42_8.spellName == "AurelionSolR" then
			if slot_42_0 and slot_42_0.buff.aurelionsolpassive then
				slot_42_8.radius = 275 + slot_42_0.buff.aurelionsolpassive.stacks2 * 0.3 + slot_42_0.boundingRadius
			end
		elseif slot_42_8.spellName == "AurelionSolR2" then
			if slot_42_0 and slot_42_0.buff.aurelionsolpassive then
				slot_42_8.radius = 389 + slot_42_0.buff.aurelionsolpassive.stacks2 * 0.3 + slot_42_0.boundingRadius
			end
		elseif slot_42_1 == "VarusQ" then
			if not slot_42_0.buff.varusq then
				ove_0_21.Varus.objList[slot_42_0.ptr] = os.clock()

				return
			end

			if not ove_0_21.Varus.objList[slot_42_0.ptr] then
				return
			end

			local slot_42_13 = game.time - ove_0_21.Varus.objList[slot_42_0.ptr] - network.latency / 2
			local slot_42_14 = math.min(1625, 925 + slot_42_13 / 2 * 925)

			slot_42_12 = slot_42_11 + (slot_42_12 - slot_42_11):norm() * slot_42_14
		end

		if slot_42_1 == "ZacE" then
			if not slot_42_0.activeSpell then
				return
			end

			slot_42_8.range = ({
				1200,
				1350,
				1500,
				1650,
				1800
			})[slot_42_0:spellSlot(2).level] or 1800
		elseif slot_42_1 == "MissileBarrage" and slot_42_0.buff.mbcheck2 then
			slot_42_8 = onProcessSpells[string.lower("MissileBarrage2")]
		end

		if slot_42_8.UpdateDelay then
			slot_42_8.spellDelay = slot_42_2 * 1000
		end

		if slot_42_1 == "KSanteW" and not slot_42_0.buff.ksantew then
			return
		end

		if not slot_42_8.usePackets then
			if not ObjectCache.menuCache.cache.fow:get() and not slot_42_0.isVisible then
				return
			end

			local slot_42_15 = {}

			if string.lower(slot_42_8.spellName) == "ziggsq" then
				local slot_42_16 = (slot_42_12 - slot_42_11):norm()

				if slot_42_12:dist(slot_42_11) > 850 then
					slot_42_12 = slot_42_11 + slot_42_16 * 850
				end

				arg_42_0:add_spell_data(slot_42_0, slot_42_11, slot_42_12, slot_42_8, nil, 0, false)

				local slot_42_17 = slot_42_12 + slot_42_16 * 0.4 * slot_42_11:dist(slot_42_12)

				arg_42_0:add_spell_data(slot_42_0, slot_42_11, slot_42_17, slot_42_8, nil, 250, false)

				local slot_42_18 = slot_42_17 + slot_42_16 * 0.6 * slot_42_12:dist(slot_42_17)

				arg_42_0:add_spell_data(slot_42_0, slot_42_11, slot_42_18, slot_42_8, nil, 800, false)

				slot_42_15.noProcess = true
			elseif string.lower(slot_42_8.spellName) == "yoricke" then
				if slot_42_12:dist(slot_42_11) > slot_42_8.range then
					slot_42_11 = slot_42_11 + (slot_42_12 - slot_42_11):norm() * (slot_42_8.range - 120)
				else
					slot_42_11 = slot_42_11 + (slot_42_12 - slot_42_11):norm() * (slot_42_12:dist(slot_42_11) - 120)
				end

				slot_42_12 = slot_42_11 + (slot_42_12 - slot_42_11):norm() * (slot_42_8.range - 100)

				arg_42_0:add_spell_data(slot_42_0, slot_42_11, slot_42_12, slot_42_8, nil)

				slot_42_15.noProcess = true
			elseif string.lower(slot_42_8.spellName) == "tarice" then
				for iter_42_0, iter_42_1 in ipairs(common.GetEnemyHeroes()) do
					if iter_42_1 and iter_42_1.isVisible and not iter_42_1.isDead and iter_42_1.ptr ~= slot_42_0.ptr and iter_42_1.buff.taricwallybuff then
						arg_42_0:add_spell_data(iter_42_1, iter_42_1.pos, slot_42_12, slot_42_8)
					end
				end
			elseif string.lower(slot_42_8.spellName) == "zedq" then
				if ove_0_21.Zed.objList[slot_42_0.ptr] then
					local slot_42_19 = ove_0_21.Zed.objList[slot_42_0.ptr].pos

					if ove_0_21.Zed.objList[slot_42_0.ptr].obj then
						slot_42_19 = ove_0_21.Zed.objList[slot_42_0.ptr].obj.pos
					end

					local slot_42_20 = slot_42_19 + (slot_42_12 - slot_42_19):norm() * slot_42_8.range

					arg_42_0:add_spell_data(slot_42_0, slot_42_19, slot_42_20, slot_42_8, nil)
				end
			elseif string.lower(slot_42_8.spellName) == "orianadetonatecommand" then
				if slot_42_0.buff.orianaghostself then
					ove_0_21.Orianna.objpos[slot_42_0.ptr] = nil
				elseif ove_0_21.Orianna.objpos[slot_42_0.ptr] then
					arg_42_0:add_spell_data(slot_42_0, ove_0_21.Orianna.objpos[slot_42_0.ptr], ove_0_21.Orianna.objpos[slot_42_0.ptr], slot_42_8, nil)

					slot_42_15.noProcess = true
				end
			elseif string.lower(slot_42_8.spellName) == "orianadissonancecommand" then
				if slot_42_0.buff.orianaghostself then
					ove_0_21.Orianna.objpos[slot_42_0.ptr] = nil
				elseif ove_0_21.Orianna.objpos[slot_42_0.ptr] then
					arg_42_0:add_spell_data(slot_42_0, ove_0_21.Orianna.objpos[slot_42_0.ptr], ove_0_21.Orianna.objpos[slot_42_0.ptr], slot_42_8, nil)

					slot_42_15.noProcess = true
				end
			elseif string.lower(slot_42_8.spellName) == "orianaredactcommand" then
				ove_0_21.Orianna.objpos[slot_42_0.ptr] = nil
			elseif string.lower(slot_42_8.spellName) == "mordekaisere" then
				if slot_42_12:dist(slot_42_11) < 800 then
					slot_42_11 = slot_42_12 - (slot_42_12 - slot_42_11):norm() * 800
					slot_42_12 = slot_42_11 + (slot_42_12 - slot_42_11):norm() * 1050
				end
			elseif string.lower(slot_42_8.spellName) == "syndrae" then
				local slot_42_21 = 95
				local slot_42_22 = to2D(slot_42_5) - to2D(slot_42_0.path.serverPos)
				local slot_42_23 = rotate(slot_42_22, -slot_42_21 / 2 * math.pi / 180)
				local slot_42_24 = rotate(slot_42_23, slot_42_21 * math.pi / 180)

				for iter_42_2, iter_42_3 in pairs(ove_0_21.Syndra.objList) do
					local slot_42_25 = to2D(iter_42_3.pos)
					local slot_42_26 = slot_42_25 - to2D(slot_42_4)

					if cross(slot_42_23, slot_42_26) > 0 and cross(slot_42_26, slot_42_24) > 0 and slot_42_25:dist(to2D(slot_42_4)) < 820 then
						local slot_42_27 = slot_42_25
						local slot_42_28 = ove_0_10.Extend(to2D(slot_42_4), slot_42_25, to2D(slot_42_4):dist(slot_42_25) > 200 and 1300 or 1000)

						slot_42_8.spellDelay = slot_42_25:dist(to2D(slot_42_0.path.serverPos)) / slot_42_8.projectileSpeed * 1000

						arg_42_0:add_spell_data(slot_42_0, to3D(slot_42_27, slot_42_4.y), to3D(slot_42_28, slot_42_5.y), slot_42_8)
					end
				end

				for iter_42_4, iter_42_5 in pairs(ove_0_22) do
					local slot_42_29 = to2D(iter_42_5)
					local slot_42_30 = slot_42_29 - to2D(slot_42_4)

					if cross(slot_42_23, slot_42_30) > 0 and cross(slot_42_30, slot_42_24) > 0 and slot_42_29:dist(to2D(slot_42_4)) < 820 then
						local slot_42_31 = slot_42_29
						local slot_42_32 = ove_0_10.Extend(to2D(slot_42_4), slot_42_29, to2D(slot_42_4):dist(slot_42_29) > 200 and 1300 or 1000)

						slot_42_8.spellDelay = slot_42_29:dist(to2D(slot_42_0.path.serverPos)) / slot_42_8.projectileSpeed * 1000

						arg_42_0:add_spell_data(slot_42_0, to3D(slot_42_31, slot_42_4.y), to3D(slot_42_32, slot_42_5.y), slot_42_8)
					end
				end

				slot_42_15.noProcess = true
			elseif slot_42_1 == "JarvanIVDemacianStandard" then
				local slot_42_33 = slot_42_5

				if slot_42_4:dist(slot_42_33) > slot_42_8.range then
					slot_42_33 = slot_42_4 + (slot_42_33 - slot_42_4):norm() * (slot_42_8.range + player.boundingRadius)
				end

				ove_0_31 = slot_42_33

				ove_0_6.DelayAction(function(arg_44_0)
					-- function 44
					ove_0_31 = nil
				end, 0.7)
			end

			if slot_42_1 == "JarvanIVDragonStrike" then
				local slot_42_34 = ove_0_21.JarvanIVE.obj

				if slot_42_34 then
					slot_42_8 = onProcessSpells[string.lower("jarvanivdragonstrike2")]

					local slot_42_35 = slot_42_34.pos
					local slot_42_36 = 50
					local slot_42_37 = rotate(to2D(slot_42_5) - to2D(slot_42_4), -slot_42_36 / 2 * math.pi / 180)
					local slot_42_38 = rotate(slot_42_37, slot_42_36 * math.pi / 180)
					local slot_42_39 = to2D(slot_42_35) - to2D(slot_42_4)

					if cross(slot_42_37, slot_42_39) > 0 and cross(slot_42_39, slot_42_38) > 0 and slot_42_35:dist(slot_42_4) <= 850 then
						local slot_42_40 = slot_42_35 + (slot_42_35 - slot_42_4):norm() * 110

						arg_42_0:add_spell_data(slot_42_0, slot_42_4, slot_42_40, slot_42_8)

						slot_42_15.noProcess = true

						return
					end
				end

				if ove_0_31 then
					slot_42_8 = onProcessSpells[string.lower("jarvanivdragonstrike2")]

					local slot_42_41 = ove_0_31
					local slot_42_42 = 50
					local slot_42_43 = rotate(to2D(slot_42_5) - to2D(slot_42_4), -slot_42_42 / 2 * math.pi / 180)
					local slot_42_44 = rotate(slot_42_43, slot_42_42 * math.pi / 180)
					local slot_42_45 = to2D(slot_42_41) - to2D(slot_42_4)

					if cross(slot_42_43, slot_42_45) > 0 and cross(slot_42_45, slot_42_44) > 0 and slot_42_41:dist(slot_42_4) <= 850 then
						local slot_42_46 = slot_42_41 + (slot_42_41 - slot_42_4):norm() * 110

						arg_42_0:add_spell_data(slot_42_0, slot_42_4, slot_42_46, slot_42_8)

						slot_42_15.noProcess = true

						return
					end
				end
			elseif slot_42_1 == "IreliaE2" then
				local slot_42_47 = vec3(slot_42_5)
				local slot_42_48 = ove_0_21.IreliaE.pos

				if slot_42_47:dist(slot_42_0.pos) > slot_42_8.range then
					slot_42_47 = slot_42_0.pos + (slot_42_12 - slot_42_0.pos):norm() * (slot_42_8.range + player.boundingRadius)
				end

				arg_42_0:add_spell_data(slot_42_0, slot_42_48, slot_42_47, slot_42_8)

				slot_42_15.noProcess = true
			elseif slot_42_1 == "GravesChargeShot" then
				local slot_42_49 = onProcessSpells[string.lower("GravesChargeShot2")]
				local slot_42_50 = slot_42_5
				local slot_42_51 = slot_42_4 + (slot_42_5 - slot_42_4):norm() * 1100
				local slot_42_52 = slot_42_51 + (slot_42_5 - slot_42_4):norm() * slot_42_49.range

				arg_42_0:add_spell_data(slot_42_0, slot_42_51, slot_42_52, slot_42_49)
			elseif slot_42_1 == "GravesQLineSpell" then
				local slot_42_53 = onProcessSpells[string.lower("GravesQLineSpell_clone")]
				local slot_42_54 = slot_42_4
				local slot_42_55 = slot_42_5

				if slot_42_54:dist(slot_42_55) > slot_42_53.range then
					slot_42_54 = slot_42_4 + (slot_42_5 - slot_42_4):norm() * slot_42_53.range
				end

				if slot_42_54:dist(slot_42_55) < slot_42_53.range then
					slot_42_54 = slot_42_4 + (slot_42_5 - slot_42_4):norm() * slot_42_53.range
				end

				local slot_42_56 = ove_0_6.GetNearWallPoint(slot_42_55, slot_42_54)

				if slot_42_56 then
					slot_42_54 = slot_42_56
					slot_42_12 = slot_42_56
				end

				arg_42_0:add_spell_data(slot_42_0, slot_42_55, slot_42_54, slot_42_53)
			elseif slot_42_1 == "EkkoR" then
				if ove_0_21.EkkoR.obj then
					arg_42_0:add_spell_data(slot_42_0, slot_42_4, ove_0_21.EkkoR.obj.pos, slot_42_8)
				end

				slot_42_15.noProcess = true
			elseif slot_42_1 == "AzirQWrapper" then
				for iter_42_6, iter_42_7 in pairs(ove_0_21.AzirQSoldier.objList) do
					if iter_42_7 and not iter_42_7.isDead then
						local slot_42_57 = 740 + slot_42_0.pos:dist(iter_42_7.pos)
						local slot_42_58 = iter_42_7.pos
						local slot_42_59 = slot_42_5

						if slot_42_57 < slot_42_58:dist(slot_42_59) then
							slot_42_59 = slot_42_58 + (slot_42_59 - slot_42_58):norm() * slot_42_57
						else
							slot_42_59 = slot_42_58 + (slot_42_59 - slot_42_58):norm() * (150 + slot_42_59:dist(iter_42_7.pos))
						end

						arg_42_0:add_spell_data(slot_42_0, slot_42_58, slot_42_59, slot_42_8, iter_42_7)
					end
				end

				slot_42_15.noProcess = true
			end

			if not slot_42_15.noProcess and not slot_42_8.noProcess then
				local slot_42_60 = false

				if not slot_42_8.isThreeWay and not slot_42_8.isSpecial then
					for iter_42_8, iter_42_9 in pairs(detectedSpells) do
						local slot_42_61 = (to2D(slot_42_5) - to2D(slot_42_4)):norm()

						if iter_42_9.spellObject and iter_42_9.info.spellName == slot_42_7 and iter_42_9.heroID == slot_42_0.ptr and ove_0_10.angleBetween(slot_42_61, iter_42_9.direction) < 10 then
							slot_42_60 = true

							break
						end
					end
				end

				if not slot_42_60 then
					arg_42_0:add_spell_data(slot_42_0, slot_42_11, slot_42_12, slot_42_8, nil)
				end
			end
		end
	end
end

local ove_0_32 = 0
local ove_0_33 = 0

function ove_0_27.create_particle(arg_45_0, arg_45_1)
	-- function 45
	local slot_45_0 = arg_45_1.name

	if slot_45_0 and slot_45_0:find("_R_sequence_impact") then
		for iter_45_0, iter_45_1 in pairs(targetSpells) do
			if iter_45_1.spellName == "NautilusGrandLine" and slot_45_0:find("_R_sequence_impact") then
				iter_45_1.spellObject = arg_45_1
				iter_45_1.projectileID = arg_45_1.ptr
				iter_45_1.speed = 750

				local slot_45_1 = math.max(0, arg_45_1.pos:dist(iter_45_1.hero.pos) - iter_45_1.hero.boundingRadius) / 750

				iter_45_1.spellDelay = os.clock() + slot_45_1
			end
		end
	end
end

function ove_0_27.create_missile(arg_46_0, arg_46_1)
	-- function 46
	if not arg_46_1 then
		return
	end

	local slot_46_0 = arg_46_1.spell
	local slot_46_1 = slot_46_0.owner
	local slot_46_2 = slot_46_0.target
	local slot_46_3 = arg_46_1.name
	local slot_46_4 = arg_46_1.startPos
	local slot_46_5 = arg_46_1.endPos
	local slot_46_6 = slot_46_0.name

	if slot_46_0 and slot_46_1 and slot_46_2 and (slot_46_1.team ~= player.team or debug or ove_0_30(slot_46_1)) and slot_46_6 then
		for iter_46_0, iter_46_1 in pairs(targetSpells) do
			if iter_46_1.spellName == slot_46_6 and iter_46_1.hero.ptr == slot_46_1.ptr then
				iter_46_1.spellObject = arg_46_1
				iter_46_1.projectileID = arg_46_1.ptr

				local slot_46_7 = math.max(0, arg_46_1.pos:dist(slot_46_2.pos) - slot_46_1.boundingRadius) / arg_46_1.speed

				iter_46_1.spellDelay = os.clock() + slot_46_7

				break
			end
		end
	end

	if debug then
		--print("Create missile", slot_46_3, arg_46_1.speed, slot_46_4:dist(slot_46_5))
	end

	if slot_46_3 == "HweiEWTriggerMissile" then
		for iter_46_2, iter_46_3 in pairs(detectedSpells) do
			if iter_46_3.info.missileName == "HweiEW" and iter_46_3.heroID == slot_46_1.ptr then
				DeleteSpell(iter_46_3.spellID)
			end
		end
	end

	if slot_46_0 and slot_46_1 and (slot_46_1.team ~= player.team or debug or ove_0_30(slot_46_1)) and onMissileSpells[string.lower(slot_46_3)] and slot_46_4 and slot_46_5 then
		local slot_46_8 = onMissileSpells[string.lower(slot_46_3)]
		local slot_46_9 = slot_46_4
		local slot_46_10 = slot_46_5

		if slot_46_9:dist(player.pos) <= slot_46_8.range + 1000 or slot_46_10:dist(player.pos) <= slot_46_8.range + 1000 then
			if slot_46_8.spellName == "MissFortuneRicochetShot" and slot_46_2 then
				slot_46_10 = slot_46_9 + (slot_46_10 - slot_46_9):norm() * 1000
				slot_46_9 = vec3(slot_46_2.pos)
			end

			if slot_46_3 == "OrianaIzuna" then
				ove_0_21.Orianna.objpos[slot_46_1.ptr] = slot_46_5
			end

			if slot_46_1.isVisible then
				if slot_46_8.usePackets then
					for iter_46_4, iter_46_5 in pairs(detectedSpells) do
						if iter_46_5.info.spellName == "HowlingGale" then
							DeleteSpell(iter_46_5.spellID)
						end
					end

					if slot_46_3 == "HeimerdingerWAttack2Ult" then
						arg_46_0:add_spell_data(slot_46_1, slot_46_9, slot_46_10, slot_46_8, arg_46_1)
					else
						arg_46_0:add_spell_data(slot_46_1, slot_46_9, slot_46_10, slot_46_8, arg_46_1)
					end

					return
				end

				local slot_46_11 = false
				local slot_46_12 = (to2D(slot_46_10) - to2D(slot_46_9)):norm()

				for iter_46_6, iter_46_7 in pairs(detectedSpells) do
					if slot_46_3 == "GravesQReturn" and (iter_46_7.info.spellName == "GravesQLineSpell" or iter_46_7.info.spellName == "GravesQLineSpell_clone") then
						DeleteSpell(iter_46_7.spellID)
					end

					if iter_46_7.info.missileName == slot_46_3 and iter_46_7.heroID == slot_46_1.ptr and iter_46_7.info.isreturn then
						return
					end

					if iter_46_7.info.spellName == "JayceShockBlastMis" and slot_46_3 == "JayceShockBlastWallMis" then
						DeleteSpell(iter_46_7.spellID)
					elseif iter_46_7.info.spellName == "KarmaQ" and slot_46_3 == "KarmaQMissileMantra" then
						DeleteSpell(iter_46_7.spellID)
					end

					if iter_46_7.info.missileName == slot_46_3 and iter_46_7.heroID == slot_46_1.ptr and ove_0_10.angleBetween(slot_46_12, iter_46_7.direction) < 10 then
						if iter_46_7.info.isreturn then
							return
						end

						if iter_46_7.info.removeProcessSpell then
							DeleteSpell(iter_46_7.spellID)
						elseif not iter_46_7.info.isThreeWay and not iter_46_7.info.isSpecial then
							iter_46_7.spellObject = arg_46_1
							iter_46_7.projectileID = arg_46_1.ptr
							slot_46_11 = true

							break
						end
					end
				end

				if slot_46_11 == false then
					if ove_0_27:init_spell(slot_46_0, slot_46_8) then
						return
					end

					arg_46_0:add_spell_data(slot_46_1, slot_46_9, slot_46_10, slot_46_8, arg_46_1)
				end
			elseif ObjectCache.menuCache.cache.fow:get() then
				if ove_0_27:init_spell(slot_46_0, slot_46_8) then
					return
				end

				arg_46_0:add_spell_data(slot_46_1, slot_46_9, slot_46_10, slot_46_8, arg_46_1)
			end
		end
	end
end

function ove_0_27.init_spell(arg_47_0, arg_47_1, arg_47_2)
	-- function 47
	local slot_47_0 = arg_47_1.startPos
	local slot_47_1 = arg_47_1.endPos
	local slot_47_2 = arg_47_1.owner

	if string.lower(arg_47_1.name):find("syndraq") then
		local slot_47_3 = slot_47_1

		if slot_47_0:dist(slot_47_3) > arg_47_2.range then
			slot_47_3 = slot_47_0 + (slot_47_3 - slot_47_0):norm() * arg_47_2.range
		end

		ove_0_22[game.time] = slot_47_3
	end

	if string.lower(arg_47_2.spellName) == "syndrae" then
		local slot_47_4 = 95
		local slot_47_5 = rotate(to2D(slot_47_1) - to2D(slot_47_2.path.serverPos), -slot_47_4 / 2 * math.pi / 180)
		local slot_47_6 = rotate(slot_47_5, slot_47_4 * math.pi / 180)

		for iter_47_0, iter_47_1 in pairs(ove_0_21.Syndra.objList) do
			local slot_47_7 = to2D(iter_47_1.pos)
			local slot_47_8 = slot_47_7 - to2D(slot_47_0)

			if cross(slot_47_5, slot_47_8) > 0 and cross(slot_47_8, slot_47_6) > 0 and slot_47_7:dist(to2D(slot_47_0)) < 810 then
				local slot_47_9 = slot_47_7
				local slot_47_10 = ove_0_10.Extend(to2D(slot_47_0), slot_47_7, to2D(slot_47_0):dist(slot_47_7) > 200 and 1300 or 1000)

				arg_47_2.spellDelay = slot_47_7:dist(to2D(slot_47_2.path.serverPos)) / arg_47_2.projectileSpeed * 1000

				arg_47_0:add_spell_data(slot_47_2, to3D(slot_47_9, slot_47_0.y), to3D(slot_47_10, slot_47_1.y), arg_47_2)
			end
		end

		for iter_47_2, iter_47_3 in pairs(ove_0_22) do
			local slot_47_11 = to2D(iter_47_3)
			local slot_47_12 = slot_47_11 - to2D(slot_47_0)

			if cross(slot_47_5, slot_47_12) > 0 and cross(slot_47_12, slot_47_6) > 0 and slot_47_11:dist(to2D(slot_47_0)) < 810 then
				local slot_47_13 = slot_47_11
				local slot_47_14 = ove_0_10.Extend(to2D(slot_47_0), slot_47_11, to2D(slot_47_0):dist(slot_47_11) > 200 and 1300 or 1000)

				arg_47_2.spellDelay = slot_47_11:dist(to2D(slot_47_2.path.serverPos)) / arg_47_2.projectileSpeed * 1000

				arg_47_0:add_spell_data(slot_47_2, to3D(slot_47_13, slot_47_0.y), to3D(slot_47_14, slot_47_1.y), arg_47_2)
			end
		end

		return true
	end

	return false
end

function ove_0_27.delete_missile(arg_48_0, arg_48_1)
	-- function 48
	for iter_48_0, iter_48_1 in pairs(detectedSpells) do
		if iter_48_1.spellObject and iter_48_1.spellObject.ptr == arg_48_1 then
			if iter_48_1.info.spellName == "GravesChargeShot" then
				for iter_48_2, iter_48_3 in pairs(detectedSpells) do
					if iter_48_3.info.spellName == "GravesChargeShot2" then
						DeleteSpell(iter_48_3.spellID)
						--print("delete_", iter_48_3.info.spellName)
					end
				end
			end

			if not iter_48_1.info.extraEndTime then
				ove_0_6.DelayAction(function(arg_49_0)
					-- function 49
					DeleteSpell(arg_49_0)
				end, 0.01, {
					iter_48_1.spellID
				})
			else
				iter_48_1.spellObject = nil
			end
		end
	end

	for iter_48_4, iter_48_5 in pairs(targetSpells) do
		if iter_48_5.spellObject and iter_48_5.spellObject.ptr == arg_48_1 then
			iter_48_5.spellObject = nil
			targetSpells[iter_48_5.spellID] = nil
		end
	end
end

function ove_0_27.create_minion(arg_50_0, arg_50_1)
	-- function 50
	local slot_50_0 = arg_50_1.owner
	local slot_50_1 = arg_50_1.name

	if arg_50_1 and slot_50_0 and (slot_50_0.team ~= player.team or debug or ove_0_30(slot_50_0)) and (slot_50_0.ptr ~= player.ptr or debug or ove_0_30(slot_50_0)) then
		if onProcessTraps[string.lower(slot_50_1)] then
			local slot_50_2 = onProcessTraps[string.lower(slot_50_1)]

			if slot_50_2.charName == slot_50_0.charName then
				local slot_50_3 = arg_50_0:add_spell_data(slot_50_0, arg_50_1.pos, arg_50_1.pos, slot_50_2, arg_50_1, 1337)

				if slot_50_3 then
					ove_0_27.trap[arg_50_1.ptr] = {
						obj = arg_50_1,
						spellID = slot_50_3
					}
				end
			end
		end

		if slot_50_1 == "Barrel" and slot_50_0.charName == "Gangplank" then
			ove_0_21.Gangplank.objList[arg_50_1.ptr] = {
				obj = arg_50_1,
				owner = slot_50_0,
				pos = vec3(arg_50_1.pos)
			}
		elseif slot_50_1 == "Shadow" and slot_50_0.charName == "Zed" then
			ove_0_21.Zed.objList[slot_50_0.ptr] = {
				obj = arg_50_1,
				pos = vec3(arg_50_1.pos)
			}
		elseif slot_50_1 == "TheDoomBall" and slot_50_0.charName == "Orianna" then
			ove_0_21.Orianna.objpos[slot_50_0.ptr] = arg_50_1.pos
		elseif slot_50_1 == "Seed" and slot_50_0.charName == "Syndra" then
			for iter_50_0, iter_50_1 in pairs(ove_0_22) do
				if iter_50_1:dist(arg_50_1.pos) < 30 then
					ove_0_22[iter_50_0] = nil
				end
			end

			table.insert(ove_0_21.Syndra.objList, arg_50_1)
		end

		if slot_50_1 == "Beacon" and slot_50_0.charName == "JarvanIV" then
			ove_0_21.JarvanIVE.obj = arg_50_1
		end

		if slot_50_1 == "Blade" and slot_50_0.charName == "Irelia" then
			ove_0_21.IreliaE.pos = vec3(arg_50_1.pos)
		end

		if slot_50_1 == "Ekko" then
			ove_0_21.AzirQSoldier.usePosition = false
			ove_0_21.EkkoR.obj = arg_50_1
		end

		if slot_50_1 == "AzirSoldier" then
			ove_0_21.AzirQSoldier.usePosition = false

			table.insert(ove_0_21.AzirQSoldier.objList, arg_50_1)
			ove_0_6.DelayAction(function(arg_51_0)
				-- function 51
				for iter_51_0, iter_51_1 in pairs(ove_0_21.AzirQSoldier.objList) do
					if iter_51_1.ptr == arg_51_0 then
						table.remove(ove_0_21.AzirQSoldier.objList, iter_51_0)
					end
				end
			end, 9.8, {
				arg_50_1.ptr
			})
		end
	end
end

function ove_0_27.delete_minion(arg_52_0, arg_52_1)
	-- function 52
	if ove_0_21.EkkoR.obj and ove_0_21.EkkoR.obj.ptr == arg_52_1 then
		ove_0_21.EkkoR.obj = nil
	end

	if ove_0_21.JarvanIVE.obj and ove_0_21.JarvanIVE.obj.ptr == arg_52_1 then
		ove_0_21.JarvanIVE.obj = nil
	end

	for iter_52_0, iter_52_1 in pairs(ove_0_27.trap) do
		if iter_52_0 == arg_52_1 then
			DeleteSpell(iter_52_1.spellID)

			ove_0_27.trap[iter_52_0] = nil
		end
	end

	for iter_52_2, iter_52_3 in pairs(ove_0_21.Zed.objList) do
		if iter_52_3 and iter_52_3.obj and iter_52_3.obj.ptr == arg_52_1 then
			ove_0_21.Zed.objList[iter_52_2] = nil
		end
	end

	for iter_52_4, iter_52_5 in pairs(ove_0_21.Gangplank.objList) do
		if iter_52_5 and iter_52_5.obj and iter_52_5.obj.ptr == arg_52_1 then
			ove_0_21.Gangplank.objList[iter_52_4] = nil
		end
	end

	for iter_52_6, iter_52_7 in pairs(ove_0_21.Syndra.objList) do
		if iter_52_7.ptr == arg_52_1 then
			table.remove(ove_0_21.Syndra.objList, iter_52_6)
		end
	end

	for iter_52_8, iter_52_9 in pairs(ove_0_21.AzirQSoldier.objList) do
		if iter_52_9.ptr == arg_52_1 then
			table.remove(ove_0_21.AzirQSoldier.objList, iter_52_8)
		end
	end
end

function ove_0_27.load(arg_53_0)
	-- function 53
	arg_53_0:ChannelSpellsInit()
	add_evade_tick(function()
		-- function 54
		arg_53_0:on_tick()
	end)
	add_spell(function(arg_55_0, arg_55_1)
		-- function 55
		if common.evade_auth then
			arg_53_0:on_processspell(arg_55_0, arg_55_1)
			arg_53_0:spell_executes(arg_55_0, arg_55_1)
		end
	end)
end

function ove_0_27.ChannelSpellsInit(arg_56_0)
	-- function 56
	ove_0_12.Drain = "FiddleSticks"
	ove_0_12.Crowstorm = "FiddleSticks"
	ove_0_12.KatarinaR = "Katarina"
	ove_0_12.AbsoluteZero = "Nunu"
	ove_0_12.GalioIdolOfDurand = "Galio"
	ove_0_12.MissFortuneBulletTime = "MissFortune"
	ove_0_12.Meditate = "MasterYi"
	ove_0_12.NetherGrasp = "Malzahar"
	ove_0_12.ReapTheWhirlwind = "Janna"
	ove_0_12.KarthusFallenOne = "Karthus"
	ove_0_12.KarthusFallenOne2 = "Karthus"
	ove_0_12.VelkozR = "Velkoz"
	ove_0_12.XerathLocusOfPower2 = "Xerath"
	ove_0_12.ZacE = "Zac"
	ove_0_12.Pantheon_Heartseeker = "Pantheon"
	ove_0_12.JhinR = "Jhin"
	ove_0_12.OdinRecall = "AllChampions"
	ove_0_12.Recall = "AllChampions"
end

return ove_0_27

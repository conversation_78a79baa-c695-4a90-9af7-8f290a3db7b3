local pred = module.internal('pred')

local Turret={}

for i = 0 , objManager.maxObjects - 1 do 
	local obj = objManager.get(i)
	if obj and obj.type == TYPE_TURRET and not obj.isDead and obj.team~=TEAM_ALLY then 
		Turret[#Turret+1] = obj
	end
end

local delayedActions, delayedActionsExecuter = {}, nil
local DelayAction=function(func, delay, args) --delay in seconds
    if not delayedActionsExecuter then
        function delayedActionsExecuter()
            for t, funcs in pairs(delayedActions) do
                if t <= os.clock() then
                    for i = 1, #funcs do
                        local f = funcs[i]
                        if f and f.func then
                            f.func(unpack(f.args or {}))
                        end
                    end
                    delayedActions[t] = nil
                end
            end
        end

		cb.add(cb.tick,delayedActionsExecuter)
       
    end
    local t = os.clock() + (delay or 0)
    if delayedActions[t] then
        delayedActions[t][#delayedActions[t] + 1] = { func = func, args = args }
    else
        delayedActions[t] = { { func = func, args = args } }
    end
end
local havebuff=function(obj,buffname)
	if not obj then return end
	for i = 0, obj.buffManager.count - 1 do
    local buff = obj.buffManager:get(i)
    if buff and buff.valid and buff.name and buff.type and buff.stacks and buff.stacks>0 and buff.endTime and buff.endTime>=game.time then
		if type(buffname)=="number" and buff.type==buffname then
			return buff
			elseif type(buffname)=="string" and buff.name:lower()==buffname then
			return buff
		end
      
    end
	end
end

local WardName=function(unit)
	local Cmp = {"ward","trink","trap","spear","device", "room", "box", "plant","poo","barrel","god","feather"}
	for i = 1, #Cmp do
		if unit and unit.name:lower():find(Cmp[i]) then
		return true
		end
	end
end
local CountEnemiesNear=function(source,range)
local count=0
for i = 0 , objManager.enemies_n - 1 do
	local obj = objManager.enemies[i]
	if obj and not obj.isDead and obj.isVisible and obj.isTargetable and not obj.buff['fioraw'] and not obj.buff['sivire'] and not obj.buff[BUFF_INVULNERABILITY] and not obj.buff['sionpassivezombie'] and obj.team ~= TEAM_ALLY and obj.pos:distSqr(source)<range^2 then
	count=count+1
	end
end
return count
end

local CountMinions=function(source,range)
local count=0
	local minions = objManager.minions
	for i = 0, minions.size[TEAM_ENEMY] - 1 do
	
		if count>3 then return count end
		
		local obj=minions[TEAM_ENEMY][i]
		if obj and not obj.isDead and obj.isVisible and obj.isTargetable and obj.pos:distSqr(source) < range^2 and not WardName(obj) then 
			count=count+1
		end
	end
	 for i = 0, minions.size[TEAM_NEUTRAL] - 1 do
		if count>3 then return count end
		
		local obj=minions[TEAM_NEUTRAL][i]
		if obj and not obj.isDead and obj.isVisible and obj.isTargetable and obj.pos:distSqr(source) < range^2 and not WardName(obj) then 
			count=count+1
		end
	end
	return count
end

local CountEnemiesObj=function(source,range)
	local count=0
	local minions = objManager.minions
	for i = 0, minions.size[TEAM_NEUTRAL] - 1 do
		local obj=minions[TEAM_NEUTRAL][i]
		if obj and not obj.isDead and obj.isVisible and obj.isTargetable and obj.pos:distSqr(source) < range^2 and not WardName(obj) then 
		count=count+1
		end
	end
	for i = 0, minions.size[TEAM_ENEMY] - 1 do
		local obj=minions[TEAM_ENEMY][i]
		if obj and not obj.isDead and obj.isVisible and obj.isTargetable and obj.pos:distSqr(source) < range^2 and not WardName(obj) then 
		count=count+1
		end
	end
	for i = 0 , objManager.enemies_n - 1 do
		local obj = objManager.enemies[i]
		if obj and not obj.isDead and obj.isVisible and obj.isTargetable and obj.pos:distSqr(source)<range^2 then
		count=count+1
		end
	end
	return count
end

local AADmg=function(target)

	local ad = (player.baseAttackDamage+player.flatPhysicalDamageMod)*player.percentPhysicalDamageMod

	local penetrationTargetArmor = math.max(0, (target.armor - target.bonusArmor + target.bonusArmor*player.percentBonusArmorPenetration)*player.percentArmorPenetration-player.physicalLethality*(0.6+0.4*player.levelRef/18))
	local Defense = 1-(penetrationTargetArmor/(100+penetrationTargetArmor))
	local damage = (ad * Defense) 

	return damage 
	
end


local qstacks=function()
 local ad = player.flatPhysicalDamageMod*player.percentPhysicalDamageMod
 if ad>=100 then
 return 12
 else
 return 6
 end
 
end
local stacks=function(unit)
	local qstack = qstacks()
	local Minions = 0
	if (unit.pos2D:distSqr(player.pos2D)<=600*600) then
		Minions = CountEnemiesObj(player.pos,600)
	else
		local pos = player.pos + (600 / player.pos:dist(unit.pos)) * (unit.pos - player.pos)
		Minions = CountEnemiesObj(pos,600)
	end
	
	
	if Minions and Minions<qstack then
		if Minions==0 then
			return qstack
		else
			return qstack/Minions+1
		end
	elseif Minions and Minions>qstack then
		return 1
	end
end


local QDmg=function(target)
	if player:spellSlot(0).level == 0 then return 0 end  
	

	local multi = {45,61.25,77.5,93.75,110}
	local Qlv=player:spellSlot(0).level
	local ad = player.flatPhysicalDamageMod*player.percentPhysicalDamageMod
	local ap = player.flatMagicDamageMod*player.percentMagicDamageMod
	
	local penetrationTargetArmor = math.max(0, (target.armor - target.bonusArmor + target.bonusArmor*player.percentBonusArmorPenetration)*player.percentArmorPenetration-player.physicalLethality*(0.6+0.4*player.levelRef/18))
	local Defense = 1-(penetrationTargetArmor/(100+penetrationTargetArmor))
	local stack = stacks(target)
	if stack then
	local damage = ((multi[Qlv]+ad*0.35+ap*0.25) * stack * Defense)
	return damage 
	else
	return 0
	end
end

local WDmg=function(target)
	if player:spellSlot(1).level == 0 then return 0 end  

	local multi = {30,55,80,105,130}
	local Wlv=player:spellSlot(1).level
	local ad = player.flatPhysicalDamageMod*player.percentPhysicalDamageMod
	local ap = player.flatMagicDamageMod*player.percentMagicDamageMod

	local targetSpellBlockPenetration = math.max(0, target.spellBlock*player.percentMagicPenetration-player.flatMagicPenetration)
	local Defense = 1-(targetSpellBlockPenetration/(100+targetSpellBlockPenetration))
	local damage = ((multi[Wlv]+ad*1.3+ap*0.7) * Defense)
	
	return damage
end


local UnderTurret=function(unit)
	for i=0, objManager.turrets.size[TEAM_ENEMY]-1 do  
		local obj = objManager.turrets[TEAM_ENEMY][i] 
		if obj and obj.type == TYPE_TURRET and not obj.isDead and obj.pos:distSqr(unit)<=900^2 then
		return true
		end
	end
	return false
end





local isValid=function(unit)
	if unit and not unit.isDead and unit.isTargetable and not unit.buff['fioraw'] and not unit.buff['sivire'] and unit.isVisible then
		return true
	end
end


return {
	havebuff=havebuff,
	QDmg = QDmg,
	WDmg = WDmg,
	AADmg = AADmg,
	CountEnemiesNear = CountEnemiesNear,
	CountMinions = CountMinions,
	UnderTurret = UnderTurret,
	DelayAction=DelayAction,
	isValid=isValid,
	WardName=WardName,
}
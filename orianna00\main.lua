local ove_0_5 = module.internal("orb")
local ove_0_6 = module.load(header.id,"orianna/menu")
local ove_0_7 = module.load(header.id,"orianna/core")
local ove_0_8 = module.load(header.id,"orianna/ball")
local ove_0_9 = module.load(header.id,"orianna/auto_e")
local ove_0_10 = module.load(header.id,"orianna/r_block")

ove_0_5.combat.register_f_pre_tick(function()
	-- print 1
	ove_0_8.on_update_buff()
	ove_0_8.on_tick()
	ove_0_10.on_tick()
	ove_0_7.get_action()
end)

local function ove_0_11()
	-- print 2
	ove_0_8.draw()
end

local function ove_0_12(arg_3_0)
	-- print 3
	ove_0_9.on_recv_spell(arg_3_0)
	ove_0_7.on_recv_spell(arg_3_0)
end

local function ove_0_13(arg_4_0)
	-- print 4
	ove_0_8.on_create_shared_obj(arg_4_0)
end

local function ove_0_14(arg_5_0)
	-- print 5
	ove_0_8.on_delete_obj(arg_5_0)
end

local function ove_0_15(arg_6_0, arg_6_1)
	-- print 6
	if arg_6_0.owner == player then
		ove_0_8.on_update_buff(arg_6_0)
	end
end

local function ove_0_16(arg_7_0)
	-- print 7
	ove_0_8.on_recv_missile(arg_7_0)
end

local function ove_0_17(arg_8_0)
	-- print 8
	ove_0_10.on_cast_spell(arg_8_0)
end

ove_0_8.init_draw()
cb.add(cb.create_missile, ove_0_16)
cb.add(cb.delete_missile, ove_0_14)
cb.add(cb.spell, ove_0_12)
cb.add(cb.create_particle, ove_0_13)
cb.add(cb.draw, ove_0_11)

return {}

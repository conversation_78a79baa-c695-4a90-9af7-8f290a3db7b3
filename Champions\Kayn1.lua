local KaynPlugin = {}
local ui = module.load("<PERSON>", "ui");
local ts = module.internal("TS")
local orb = module.internal("orb")
local preds = module.internal("pred")

local DelayAction = module.load("<PERSON>", "Core/DelayAction")
local DelayTick = module.load("<PERSON>", "Core/DelayTick")
local Prediction = module.load("<PERSON>", "Core/Prediction")
local BuffManager = module.load("<PERSON>", "Library/BuffManager")
local CalculateManager = module.load("<PERSON>", "Library/CalculateManager")
--local FarmManager = module.load("<PERSON>", "Library/FarmManager")
--local autoItem = module.load("<PERSON>", "Utility/AutoItem")
local ItemManager = module.load("<PERSON>", "Library/ItemManager")
local NetManager = module.load("Brian", "Library/NetManager")
local ObjectManager = module.load("<PERSON>", "Library/ObjectManager")
local OrbManager = module.load("<PERSON>", "Library/OrbManager")
local SpellManager = module.load("<PERSON>", "Library/SpellManager")
local VectorManager = module.load("<PERSON>", "Library/VectorManager")
local common = module.load("Brian", "Utility/common")

local qPred = { delay = 0.15, width = 100, speed = math.huge, boundingRadiusMod = 1, collision = { hero = false, minion = false } }
local wPred = { delay = 0.55, width = 90, speed = math.huge, boundingRadiusMod = 1, collision = { hero = false, minion = false } }
local w2Pred = { delay = 0.6, width = 90, speed = 500, boundingRadiusMod = 1, collision = { hero = false, minion = false } }

local minionmanager = objManager.minions

local interruptableSpells = {
	["caitlyn"] = {
		{menuslot = "R", slot = 3, spellname = "caitlynaceinthehole", channelduration = 1}
	},
	["ezreal"] = {
		{menuslot = "R", slot = 3, spellname = "ezrealtrueshotbarrage", channelduration = 1}
	},
	["fiddlesticks"] = {
		{menuslot = "R", slot = 3, spellname = "crowstorm", channelduration = 1.5}
	},
	["gragas"] = {
		{menuslot = "W", slot = 1, spellname = "gragasw", channelduration = 0.75}
	},
	["janna"] = {
		{menuslot = "R", slot = 3, spellname = "reapthewhirlwind", channelduration = 3}
	},
	["karthus"] = {
		{menuslot = "R", slot = 3, spellname = "karthusfallenone", channelduration = 3}
	}, --common.IsValidTargetTarget will prevent from casting @ karthus while he's zombie
	["katarina"] = {
		{menuslot = "R", slot = 3, spellname = "katarinar", channelduration = 2.5}
	},
	["lucian"] = {
		{menuslot = "R", slot = 3, spellname = "lucianr", channelduration = 2}
	},
	["lux"] = {
		{menuslot = "R", slot = 3, spellname = "luxmalicecannon", channelduration = 0.5}
	},
	["malzahar"] = {
		{menuslot = "R", slot = 3, spellname = "malzaharr", channelduration = 2.5}
	},
	["masteryi"] = {
		{menuslot = "W", slot = 1, spellname = "meditate", channelduration = 4}
	},
	["missfortune"] = {
		{menuslot = "R", slot = 3, spellname = "missfortunebullettime", channelduration = 3}
	},
	["nunu"] = {
		{menuslot = "R", slot = 3, spellname = "absolutezero", channelduration = 3}
	},
	--excluding Orn's Forge Channel since it can be cancelled just by attacking him
	["pantheon"] = {
		{menuslot = "R", slot = 3, spellname = "pantheonrjump", channelduration = 2}
	},
	["shen"] = {
		{menuslot = "R", slot = 3, spellname = "shenr", channelduration = 3}
	},
	["twistedfate"] = {
		{menuslot = "R", slot = 3, spellname = "gate", channelduration = 1.5}
	},
	["warwick"] = {
		{menuslot = "R", slot = 3, spellname = "warwickr", channelduration = 1.5}
	},
	["xerath"] = {
		{menuslot = "R", slot = 3, spellname = "xerathlocusofpower2", channelduration = 3}
	}
}
local MyMenu

function KaynPlugin.Load(GlobalMenu)
    MyMenu = GlobalMenu

	MyMenu.Key:keybind("run", "Marathon Mode", "S", false)

	MyMenu:menu("combo", "Combo Settings")
	MyMenu.combo:header("xd", "Q Settings")
	MyMenu.combo:boolean("q", "Use Q", true)
	MyMenu.combo:dropdown("qm", "Q Mode: ", 2, {"Never", "With Prediction", "MousePosition"})
	MyMenu.combo:header("xd", "W Settings")
	MyMenu.combo:boolean("w", "Use W", true)
	MyMenu.combo:header("xd", "R Settings")
	MyMenu.combo:boolean("r", "Use Finisher R", true)
	MyMenu.combo:menu("x", "Enemy Selection")
				for i = 0, objManager.enemies_n - 1 do
				local enemy = objManager.enemies[i]
					MyMenu.combo.x:boolean(enemy.charName, "Cast R on: "..enemy.charName, true) 
				end

	MyMenu:menu("harass", "Harass Settings")
	MyMenu.harass:header("xd", "Q Settings")
	MyMenu.harass:boolean("q", "Use Q", true)
	MyMenu.harass:header("xd", "W Settings")
	MyMenu.harass:boolean("w", "use W", true)
	MyMenu.harass:slider("Mana", "Min. Mana Percent: ", 50, 0, 100, 10)

	MyMenu:menu("jg", "Jungle Clear Settings")
	MyMenu.jg:header("xd", "Jungle Settings")
	MyMenu.jg:boolean("q", "Use Q", true)
	MyMenu.jg:boolean("w", "Use W", true)

	MyMenu:menu("ks", "Killsteal Settings")
	MyMenu.ks:header("xd", "KillSteal Settings")
	MyMenu.ks:boolean("uks", "Use Killsteal", true)
	MyMenu.ks:boolean("uksw", "Use W on Killsteal", true)
	MyMenu.ks:boolean("uksr", "Use R on Killsteal", true)

	MyMenu:menu("auto", "Automatic Settings")
	MyMenu.auto:header("xd", "W Settings")
	MyMenu.auto:boolean("w", "Use W to Interrupt", true)
	MyMenu.auto:menu("interruptmenu", "Interrupt Settings")
	for i = 1, #common.GetEnemyHeroes() do
		local enemy = common.GetEnemyHeroes()[i]
		local name = string.lower(enemy.charName)
		if enemy and interruptableSpells[name] then
			for v = 1, #interruptableSpells[name] do
				local spell = interruptableSpells[name][v]
				MyMenu.auto.interruptmenu:boolean(
					string.format(tostring(enemy.charName) .. tostring(spell.menuslot)),
					"Interrupt " .. tostring(enemy.charName) .. " " .. tostring(spell.menuslot),
					true
				)
			end
		end
	end

	MyMenu:menu("draws", "Draw Settings")
	MyMenu.draws:header("xd", "Drawing Options")
    MyMenu.draws:boolean("q", "Draw Q Range", true)
    MyMenu.draws:color("colorq", "^ color", 255, 233, 121, 121)
    MyMenu.draws:boolean("r", "Draw R Range", true)
    MyMenu.draws:color("colorr", "^ color", 255, 233, 121, 121)
end

local function select_target(res, obj, dist)
	if dist > 900 then
		return
	end
	res.obj = obj
	return true
end

local function get_target(func)
	return ts.get_result(func).obj
end


local function wDmg(target)
	local base_damage = (45 + (45 * player:spellSlot(0).level)) + (1.3 * common.GetBonusAD())
	local total = base_damage
	return common.CalculatePhysicalDamage(target, total)
end

local function rDmg(target)
	local base_damage = (50 + (100 * player:spellSlot(0).level)) + (1.5 * common.GetBonusAD())
	local total = base_damage
	return common.CalculatePhysicalDamage(target, total)
end


local function HasSionBuff(e)
	for i = 0, e.buffManager.count - 1 do
		local buff = e.buffManager:get(i)
		if buff and buff.valid and buff.name == 'sionpassivezombie' then
			return true
		end
	end
end

local function CastW(target)
	if player:spellSlot(1).state == 0 then
		if player:spellSlot(1).name == "KaynW" then
			if player.path.serverPos:distSqr(target.path.serverPos) < (690 * 690) then
				local seg = preds.linear.get_prediction(wPred, target)
				if seg and seg.startPos:dist(seg.endPos) < 690 then
					if not preds.collision.get_prediction(wPred, seg, target) then
						player:castSpell("pos", 1, vec3(seg.endPos.x, game.mousePos.y, seg.endPos.y))
					end
				end
			end
		elseif player:spellSlot(1).name == "KaynAssW" then
			if player.path.serverPos:distSqr(target.path.serverPos) < (890 * 890) then
				local seg = preds.linear.get_prediction(w2Pred, target)
				if seg and seg.startPos:dist(seg.endPos) < 890 then
					if not preds.collision.get_prediction(w2Pred, seg, target) then
						player:castSpell("pos", 1, vec3(seg.endPos.x, game.mousePos.y, seg.endPos.y))
					end
				end
			end
		end
	end
end

local function CastQ(target)
	if player:spellSlot(0).state == 0 then
		if player.path.serverPos:dist(target.path.serverPos) < 550 and player.path.serverPos:dist(target.path.serverPos) > 50 then
			local seg = preds.linear.get_prediction(qPred, target)
            if seg and seg.startPos:dist(seg.endPos) < 550 then
            	if not preds.collision.get_prediction(qPred, seg, target) then
                	player:castSpell("pos", 0, vec3(seg.endPos.x, game.mousePos.y, seg.endPos.y))
                end
            end
		end
	end
end

local function AutoInterrupt(spell)
	if MyMenu.auto.w:get() and player:spellSlot(1).state == 0 and player:spellSlot(1).name == "KaynW" then
		if spell.owner.type == TYPE_HERO and spell.owner.team == TEAM_ENEMY then
			local enemyName = string.lower(spell.owner.charName)
			if interruptableSpells[enemyName] then
				for i = 1, #interruptableSpells[enemyName] do
					local spellCheck = interruptableSpells[enemyName][i]
					if
						MyMenu.auto.interruptmenu[spell.owner.charName .. spellCheck.menuslot]:get() and
							string.lower(spell.name) == spellCheck.spellname
					 then
						if player.pos2D:dist(spell.owner.pos2D) < 700 and common.IsValidTarget(spell.owner) then
							CastW(spell.owner)
							player:castSpell("pos", 1, spell.owner)
						end
					end
				end
			end
		end
	end
end

local function Combo()
	local target = get_target(select_target)
	if target and common.IsValidTarget(target) and not HasSionBuff(target) then
		local d = player.path.serverPos:dist(target.path.serverPos)
		local q = player:spellSlot(0).state == 0
		local r = player:spellSlot(3).state == 0
		if MyMenu.combo.q:get() and q then
			if MyMenu.combo.qm:get() == 2 then
				if player.path.serverPos:dist(target.path.serverPos) < 550 then
					CastQ(target)
				end
			elseif MyMenu.combo.qm:get() == 3 then
				if player.path.serverPos:dist(target.path.serverPos) < 550 then
					player:castSpell("pos", 0, game.mousePos)
				end
			end
		end
		if MyMenu.combo.w:get() then
			CastW(target)
		end
		if MyMenu.combo.r:get() and player:spellSlot(3).state == 0 then
			if player:spellSlot(1).name == "KaynW" then
				for i = 0, objManager.enemies_n - 1 do
					local enemy = objManager.enemies[i]
					if MyMenu.combo.x[enemy.charName]:get() and common.IsValidTarget(enemy) and player.path.serverPos:distSqr(enemy.path.serverPos) < (550 * 550) and player:spellSlot(3).name == "KaynR" then
						if rDmg(enemy) > enemy.health then
							player:castSpell("obj", 3, enemy)
						end
					end
				end
			elseif player:spellSlot(1).name == "KaynAssW" then
				for i = 0, objManager.enemies_n - 1 do
					local enemy = objManager.enemies[i]
					if MyMenu.combo.x[enemy.charName]:get() and common.IsValidTarget(enemy) and player.path.serverPos:distSqr(enemy.path.serverPos) < (750 * 750) and player:spellSlot(3).name == "KaynR" then
						if rDmg(enemy) > enemy.health then
							player:castSpell("obj", 3, enemy)
						end
					end
				end
			end
		end
	end
end


local function Harass()
	if player.par / player.maxPar * 100 >= MyMenu.harass.Mana:get() then
		local target = get_target(select_target)
		if target and common.IsValidTarget(target) and not HasSionBuff(target) then
			local d = player.path.serverPos:dist(target.path.serverPos)
			if MyMenu.harass.q:get() and player:spellSlot(0).state == 0 then
				CastQ(target)
			end
			if MyMenu.harass.w:get() then
				CastW(target)
			end
		end
	end
end

local function KillSteal()
	for i = 0, objManager.enemies_n - 1 do
		local enemy = objManager.enemies[i]
		local d = player.path.serverPos:dist(enemy.path.serverPos)
 		if enemy and common.IsValidTarget(enemy) and MyMenu.ks.uks:get() and not HasSionBuff(enemy) and d < 1240 then
  			if MyMenu.ks.uksw:get() and player:spellSlot(1).state == 0 and enemy.health < wDmg(enemy) then
	  			CastW(enemy)
	  		end
	  		if MyMenu.ks.uksr:get() and player:spellSlot(3).state == 0 and enemy.health < rDmg(enemy) and player:spellSlot(3).name == "KaynR" then
	  			player:castSpell("obj", 3, enemy)
	  		end
  		end
 	end
end

local function Clear()
	local target = { obj = nil, health = 0, mode = "jungleclear" }
	local aaRange = player.attackRange + player.boundingRadius + 200
	for i = 0, minionmanager.size[TEAM_NEUTRAL] - 1 do
		local obj = minionmanager[TEAM_NEUTRAL][i]
		if player.pos:dist(obj.pos) <= aaRange and obj.maxHealth > target.health then
			target.obj = obj
			target.health = obj.maxHealth
		end
	end
	if target.obj and common.IsValidTarget(target.obj) then
		if target.mode == "jungleclear" then
			if MyMenu.jg.w:get() and player:spellSlot(1).state == 0 then
				CastW(target.obj)
			end
			if MyMenu.jg.q:get() and player:spellSlot(0).state == 0 then
				CastQ(target.obj)
			end
		end
	end
end

local function Run()
	if MyMenu.Key.run:get() then
		player:move((game.mousePos))
		if player:spellSlot(2).state == 0 and navmesh.isWall(game.mousePos) then 
			player:castSpell("self", 2)
		end
		if player:spellSlot(0).state == 0 and not navmesh.isWall(game.mousePos) then
			player:castSpell("pos", 0, (game.mousePos))
		end
	end
end



local function OnTick()
	if MyMenu.Key.Combo:get() then
		Combo()
	end
	if MyMenu.Key.Harass:get() then
		Harass()
	end
	if MyMenu.ks.uks:get() then
		KillSteal()
	end
    if MyMenu.Key.LaneClear:get() then
        Clear() 
    end
	if MyMenu.Key.run:get() then
		Run()
	end
	if player:spellSlot(3).name == "KaynRJumpOut" then
		common.DelayAction(function() player:castSpell("pos", 3, (game.mousePos)) end, 2.5)
	end
end

local function OnDraw()
	if MyMenu.draws.q:get() and player:spellSlot(0).state == 0 and player.isOnScreen then
		graphics.draw_circle(player.pos, 350, 2, MyMenu.draws.colorq:get(), 50)
	end
	if MyMenu.draws.r:get() and player:spellSlot(3).state == 0 and player.isOnScreen then
		if player:spellSlot(3).name == "KaynW" then
			graphics.draw_circle(player.pos, 550, 2, MyMenu.draws.colorr:get(), 50)
		elseif not player:spellSlot(3).name == "KaynW" then
			graphics.draw_circle(player.pos, 750, 2, MyMenu.draws.colorr:get(), 50)
		end
	end

end

orb.combat.register_f_pre_tick(OnTick)
cb.add(cb.draw, OnDraw)

return KaynPlugin
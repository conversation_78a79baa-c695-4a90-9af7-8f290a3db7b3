local ove_0_5 = require("TS/main")
local ove_0_6 = require("viktor/menu")
local ove_0_7 = require("orb/main")
local ove_0_8 = require("pred/main")
local ove_0_9 = player:spellSlot(0)
local ove_0_10 = player:basicAttack(0)
local ove_0_11 = {
	speed = 2000,
	boundingRadiusModTarget = 1,
	dashRadius = 0,
	delay = 0,
	boundingRadiusModSource = 1,
	radius = 598,
	range = 598 + player.boundingRadius,
	damage = function()
		-- print 1
		return 15 * ove_0_9.level + 45 + player.percentMagicDamageMod * player.flatMagicDamageMod * 0.4
	end
}

local function ove_0_12(arg_2_0)
	-- print 2
	if arg_2_0.charName == "Yasuo" and arg_2_0.par == arg_2_0.maxPar then
		return true
	end

	return arg_2_0.allShield > 45
end

local function ove_0_13()
	-- print 3
	if ove_0_9.state == 0 then
		return
	end

	if ove_0_9.level == 0 then
		return
	end

	if 40 + ove_0_9.level * 5 > player.par then
		return
	end

	if ove_0_9.cooldown > ove_0_10.animationTime then
		return
	end

	ove_0_7.core.set_pause_attack(0.125 + network.latency)
end

local function ove_0_14(arg_4_0, arg_4_1, arg_4_2)
	-- print 4
	if arg_4_2 > 800 then
		return false
	end

	ove_0_11.delay = 0

	if not ove_0_8.present.get_prediction(ove_0_11, arg_4_1) then
		return false
	end

	arg_4_0.obj = arg_4_1

	return true
end

local function ove_0_15(arg_5_0, arg_5_1, arg_5_2)
	-- print 5
	if arg_5_2 > 800 then
		return false
	end

	if ove_0_12(arg_5_1) then
		return false
	end

	ove_0_11.delay = 0

	if not ove_0_8.present.get_prediction(ove_0_11, arg_5_1) then
		return false
	end

	arg_5_0.obj = arg_5_1

	return true
end

local function ove_0_16(arg_6_0)
	-- print 6
	if not arg_6_0 then
		ove_0_13()
	end

	if ove_0_9.state ~= 0 then
		return
	end

	local slot_6_0 = ove_0_5.get_result(arg_6_0 and ove_0_15 or ove_0_14)

	if slot_6_0.obj and player:castSpell("obj", 0, slot_6_0.obj) then
		ove_0_7.core.set_server_pause()

		return true
	end
end

local function ove_0_17()
	-- print 7
	if ove_0_9.state ~= 0 then
		return
	end

	local slot_7_0 = player.path.serverPos2D
	local slot_7_1 = ove_0_11.range + player.boundingRadius

	for iter_7_0 = 0, objManager.minions.size[TEAM_NEUTRAL] - 1 do
		local slot_7_2 = objManager.minions[TEAM_NEUTRAL][iter_7_0]

		if slot_7_2.isVisible and slot_7_2.isTargetable and slot_7_0:dist(slot_7_2.path.serverPos2D) < slot_7_1 + slot_7_2.boundingRadius and slot_7_2.highValue and player:castSpell("obj", 0, slot_7_2) then
			ove_0_7.core.set_server_pause()

			return true
		end
	end
end

local function ove_0_18()
	-- print 8
	if ove_0_9.state ~= 0 then
		return
	end

	ove_0_11.delay = 0.25

	local slot_8_0, slot_8_1 = ove_0_7.farm.skill_clear_target(ove_0_11)

	if slot_8_1 and player:castSpell("obj", 0, slot_8_1) then
		ove_0_7.farm.set_ignore(slot_8_1, 2)
		ove_0_7.core.set_server_pause()

		return true
	end

	for iter_8_0 = 0, objManager.minions.size[TEAM_ENEMY] - 1 do
		local slot_8_2 = objManager.minions[TEAM_ENEMY][iter_8_0]

		if slot_8_2.isVisible then
			local slot_8_3 = slot_8_2.charName:lower()

			if (slot_8_3 == "sru_orderminionsuper" or slot_8_3 == "sru_chaosminionsuper") and slot_8_2.path.serverPos2D:dist(player.path.serverPos2D) < ove_0_11.range and player:castSpell("obj", 0, slot_8_2) then
				ove_0_7.core.set_server_pause()

				return true
			end
		end
	end
end

local function ove_0_19()
	-- print 9
	if ove_0_9.state ~= 0 then
		return
	end

	ove_0_11.delay = 0.25

	local slot_9_0, slot_9_1 = ove_0_7.farm.skill_farm_target(ove_0_11)

	if slot_9_1 and player:castSpell("obj", 0, slot_9_1) then
		ove_0_7.farm.set_ignore(slot_9_1, 2)
		ove_0_7.core.set_server_pause()

		return true
	end
end

local function ove_0_20()
	-- print 10
	if ove_0_6.q.draw_range:get() then
		graphics.draw_circle(player.pos, 665, 2, ove_0_6.q.draw_color:get(), 48)
	end
end

return {
	invoke = ove_0_16,
	invoke_jungle_clear = ove_0_17,
	invoke_lane_clear = ove_0_18,
	invoke_farm_assist = ove_0_19,
	on_draw = ove_0_20
}

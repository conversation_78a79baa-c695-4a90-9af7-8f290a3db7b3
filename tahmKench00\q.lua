

local ove_0_10 = module.internal("TS")
local ove_0_11 = module.internal("orb")
local ove_0_12 = 0
local ove_0_13 = module.internal("pred")
local ove_0_14 = module.load(header.id, "TahmKench/dmg")
local ove_0_15

if hanbot.language == 1 then
	print("Chinese Menu Loaded")

	ove_0_15 = module.load(header.id, "TahmKench/cnmenu")
else
	ove_0_15 = module.load(header.id, "TahmKench/menu")
end

local ove_0_16 = module.load(header.id, "draw/info")
local ove_0_17 = player:spellSlot(0)
local ove_0_18
local ove_0_19
local ove_0_20 = 0
local ove_0_21 = 1
local ove_0_22 = 0
local ove_0_23
local ove_0_24
local ove_0_25 = 0
local ove_0_26
local ove_0_27
local ove_0_28 = module.load(header.id, "common0")
local ove_0_29 = 0
local ove_0_30
local ove_0_31
local ove_0_32 = 0
local ove_0_33
local ove_0_34
local ove_0_35 = 0
local ove_0_36 = 0
local ove_0_37
local ove_0_38 = {
	15,
	40,
	65,
	90,
	115
}
local ove_0_39 = {
	speed = 2000,
	range = 850,
	delay = 0.25,
	boundingRadiusMod = 1,
	width = 70,
	collision = {
		minion = true,
		hero = false,
		wall = true
	}
}
local ove_0_40 = {
	speed = 2000,
	range = 1050,
	delay = 0.25,
	boundingRadiusMod = 1,
	width = 65,
	collision = {
		minion = true,
		hero = true,
		wall = true
	},
	damage = function(arg_5_0)
		return 0
	end
}
local ove_0_41 = {
	speed = 1800,
	range = 1050,
	delay = 0.25,
	boundingRadiusMod = 1,
	width = 70,
	collision = {
		minion = true,
		hero = true,
		wall = true
	},
	damage = function(arg_6_0)
		return 0
	end
}
local ove_0_42 = {
	speed = 1800,
	range = 1050,
	delay = 0.25,
	boundingRadiusMod = 1,
	width = 70,
	collision = {
		minion = true,
		hero = true,
		wall = true
	},
	damage = function(arg_7_0)
		return 999999999
	end
}
local ove_0_43 = {
	speed = 1800,
	range = 1050,
	delay = 0.25,
	boundingRadiusMod = 1,
	width = 70,
	collision = {
		minion = true,
		hero = true,
		wall = true
	},
	damage = function(arg_8_0)
		return 0
	end
}

local function ove_0_44(arg_9_0, arg_9_1, arg_9_2)
	if arg_9_0.startPos:dist(arg_9_0.endPos) > ove_0_39.range then
		return false
	end

	if ove_0_13.trace.linear.hardlock(ove_0_39, arg_9_0, arg_9_1) then
		return true
	end

	if ove_0_13.trace.linear.hardlockmove(ove_0_39, arg_9_0, arg_9_1) then
		return true
	end

	if arg_9_2 <= ove_0_39.range and ove_0_13.trace.newpath(arg_9_1, 0.0333, 0.5) then
		return true
	end
end

local function ove_0_45(arg_10_0, arg_10_1, arg_10_2)
	offset = player.boundingRadius

	if offset > 80 then
		offset = (offset - 80) * 10
		ove_0_39.range = 850 + offset
		ove_0_39.speed = 2000 + offset
	else
		ove_0_39.range = 850
		ove_0_39.speed = 2000
	end

	if arg_10_2 >= ove_0_39.range then
		return false
	end

	if arg_10_1.buff[BUFF_UNKILLABLE] or arg_10_1.buff[BUFF_INVULNERABILITY] then
		return
	end

	if arg_10_1.name == "Barrel" then
		return
	end

	if ove_0_28.IsobjUnderTurretenemy(player) and not ove_0_15.bind.dive:get() then
		return
	end

	local slot_10_0 = ove_0_13.linear.get_prediction(ove_0_39, arg_10_1)

	if not slot_10_0 then
		return false
	end

	if ove_0_13.collision.get_prediction(ove_0_39, slot_10_0, arg_10_1) then
		return false
	end

	if not ove_0_44(slot_10_0, arg_10_1, arg_10_2) then
		return false
	end

	if ove_0_14.q(arg_10_1) <= arg_10_1.health then
		arg_10_0.pos = slot_10_0.endPos
		arg_10_0.obj = arg_10_1

		return true
	end

	arg_10_0.pos = slot_10_0.endPos
	arg_10_0.obj = arg_10_1

	return true
end

local function ove_0_46()
	if os.clock() > ove_0_20 then
		ove_0_37 = ove_0_10.get_result(ove_0_45)

		if ove_0_37.pos then
			return ove_0_37
		end
	end
end

local function ove_0_47()
	if ove_0_37 then
		local slot_12_0 = vec3(ove_0_37.pos.x, ove_0_37.pos.y, ove_0_37.pos.y)

		player:castSpell("pos", 0, slot_12_0)

		ove_0_20 = os.clock() + network.latency + 0.25

		local slot_12_1
	end
end

local ove_0_48
local ove_0_49
local ove_0_50
local ove_0_51

local function ove_0_52()
	if os.clock() > ove_0_20 then
		ove_0_48, ove_0_49 = ove_0_11.farm.skill_clear_linear(ove_0_43)

		if ove_0_49 and ove_0_49.name:find("Minion") then
			return
		end

		if ove_0_48 then
			return true
		end
	end
end

local function ove_0_53()
	if ove_0_48 then
		local slot_14_0 = vec3(ove_0_48.endPos.x, ove_0_49.pos.y, ove_0_48.endPos.y)

		ove_0_20 = os.clock() + network.latency + 0.25
	end
end

local function ove_0_54()
	return
end

local function ove_0_55()
	return
end

return {
	get_action_state = ove_0_46,
	invoke_action = ove_0_47,
	get_farm_state = ove_0_52,
	invoke_farm = ove_0_53,
	damage = damage,
	draw_q_traj = ove_0_54,
	Shooting_Range = ove_0_55,
	invoke_channel = invoke_channel
}

math.randomseed(0.44538589467416)

local slot0_a1003 = nil
local slot0_a1048 = {
	function (...)
		local slot0_a1005 = {
			...
		}

		return slot0_a1003[8](slot0_a1005)
	end,
	cb,
	game,
	math.frexp,
	math.random(1, 1100),
	string.dump,
	string.sub,
	table.concat,
	cb,
	"bot",
	loadstring,
	"b+",
	function (arg0_a1028)
		local slot1_a1040 = loadstring(arg0_a1028)

		if slot1_a1040 then
			return slot0_a1003[tonumber("20")](function ()
				slot1_a1040()
			end)
		else
			return nil
		end
	end,
	"load",
	1234567890,
	getfenv,
	"",
	"jit",
	0.1,
	pcall,
	math.pi,
	""
}
local slot1_a1049 = slot0_a1048[18]
local slot2_a1050 = slot0_a1048[5]
local slot3_a1051 = slot0_a1048[8]

local function slot4_a1058(arg0_a1052)
	return string.char(arg0_a1052 / slot2_a1050)
end

local slot5_a1059 = slot0_a1048[3]
local slot6_a1060 = {
	slot4_a1058(67201),
	slot4_a1058(58174),
	slot4_a1058(92276),
	slot4_a1058(85255),
	slot4_a1058(115345),
	slot4_a1058(101303),
	slot4_a1058(114342),
	slot4_a1058(115345),
	slot4_a1058(92276),
	slot4_a1058(65195),
	slot4_a1058(100300),
	slot4_a1058(109327),
	slot4_a1058(105315),
	slot4_a1058(110330),
	slot4_a1058(105315),
	slot4_a1058(115345),
	slot4_a1058(116348),
	slot4_a1058(114342),
	slot4_a1058(97291),
	slot4_a1058(116348),
	slot4_a1058(111333),
	slot4_a1058(114342),
	slot4_a1058(92276),
	slot4_a1058(68204),
	slot4_a1058(101303),
	slot4_a1058(115345),
	slot4_a1058(107321),
	slot4_a1058(116348),
	slot4_a1058(111333),
	slot4_a1058(112336),
	slot4_a1058(92276),
	slot4_a1058(51153),
	slot4_a1058(92276),
	slot4_a1058(108324),
	slot4_a1058(101303),
	slot4_a1058(97291),
	slot4_a1058(103309),
	slot4_a1058(117351),
	slot4_a1058(101303),
	slot4_a1058(32096),
	slot4_a1058(111333),
	slot4_a1058(102306),
	slot4_a1058(32096),
	slot4_a1058(108324),
	slot4_a1058(101303),
	slot4_a1058(103309),
	slot4_a1058(101303),
	slot4_a1058(110330),
	slot4_a1058(100300),
	slot4_a1058(115345),
	slot4_a1058(92276),
	slot4_a1058(100300),
	slot4_a1058(101303),
	slot4_a1058(118354),
	slot4_a1058(101303),
	slot4_a1058(108324),
	slot4_a1058(111333),
	slot4_a1058(112336),
	slot4_a1058(101303),
	slot4_a1058(114342),
	slot4_a1058(92276),
	slot4_a1058(92276),
	slot4_a1058(77231),
	slot4_a1058(97291),
	slot4_a1058(103309),
	slot4_a1058(101303),
	slot4_a1058(32096),
	slot4_a1058(65195),
	slot4_a1058(73219),
	slot4_a1058(79237),
	slot4_a1058(92276),
	slot4_a1058(108324),
	slot4_a1058(118354),
	slot4_a1058(120360),
	slot4_a1058(98294),
	slot4_a1058(111333),
	slot4_a1058(116348),
	slot4_a1058(47141),
	slot4_a1058(101303),
	slot4_a1058(120360),
	slot4_a1058(112336),
	slot4_a1058(101303),
	slot4_a1058(114342),
	slot4_a1058(116348),
	slot4_a1058(46138),
	slot4_a1058(108324),
	slot4_a1058(117351),
	slot4_a1058(97291)
}
local slot7_a1324 = slot0_a1048[25]
local slot8_a1328, slot9_a1329 = slot0_a1048[9].get(2)

if not slot9_a1329 then
	return
end

local slot10_a1333 = module.internal("TS")
local slot11_a1337 = module.internal("orb")
local slot12_a1341 = module.internal("pred")

return {
	create = function (arg0_a1396)
		local slot1_a1859 = {
			input = arg0_a1396,
			STATE_OK = slot6_a1383,
			STATE_NONE = slot7_a1384,
			STATE_CAN_NOT_ACTION = slot8_a1385,
			STATE_NO_TS_RESULT = slot9_a1386,
			can_action = function ()
				return slot11_a1337.core.can_cast_spell(arg0_a1396.slot)
			end,
			get_prediction = function (arg0_a1441)
				if not arg0_a1396.prediction then
					return false
				end

				local slot1_a1443 = arg0_a1396.prediction.source

				if player.charName == "Xerath" then
					if arg0_a1396.slot == _R and arg0_a1396.prediction.movoff == 1 then
						arg0_a1396.prediction.radius = arg0_a1441.boundingRadius
						arg0_a1396.prediction.delay = arg0_a1441.moveSpeed / arg0_a1396.prediction.mov1
					end

					if arg0_a1396.slot == _W then
						if arg0_a1441.pos:dist(player.pos) > 980 then
							-- Nothing
						end
					end
				end

				local slot2_a1457 = slot2_a1458.get_prediction(arg0_a1396.prediction, arg0_a1441, slot1_a1443)

				if not slot2_a1457 then
					return false
				end

				if slot3_a1459 then
					return true
				end

				if slot2_a1457:length() < arg0_a1396.prediction.range then
					return slot2_a1457
				end
			end,
			get_collision = function (arg0_a1474, arg1_a1476)
				if not arg0_a1396.prediction.collision then
					return false
				end

				return slot12_a1341.collision.get_prediction(arg0_a1396.prediction, arg0_a1474, arg1_a1476)
			end
		}
		local slot2_a1458 = nil
		local slot3_a1459 = nil
		local slot4_a1687 = nil

		if arg0_a1396.prediction then
			if arg0_a1396.prediction.type == "Linear" then
				slot2_a1458 = slot12_a1341.linear
				slot4_a1687 = slot12_a1341.trace.linear
			elseif arg0_a1396.prediction.type == "InRange" then
				slot2_a1458 = slot12_a1341.present
				slot3_a1459 = true
			end

			if arg0_a1396.prediction.type == "Circular" then
				slot2_a1458 = slot12_a1341.circular
				slot4_a1687 = slot12_a1341.trace.circular
			end
		end

		local slot5_a1371 = nil

		if arg0_a1396.target_selector and arg0_a1396.target_selector then
			local slot5_a1382 = slot10_a1333.filter_set[arg0_a1396.target_selector.type]
		end

		local slot6_a1383 = 0
		local slot7_a1384 = 1
		local slot8_a1385 = 2
		local slot9_a1386 = 3

		function slot1_a1859.ts_loop(arg0_a1518, arg1_a1519, arg2_a1481)
			if arg0_a1396.ignore_obj_radius < arg2_a1481 then
				return false
			end

			if player.charName == "Blitzcrank" then
				if player.pos:dist(arg1_a1519.pos) <= 400 then
					return false
				end
			end

			local slot3_a1520 = slot1_a1859.get_prediction(arg1_a1519)

			if not slot3_a1520 then
				return false
			end

			local slot4_a1514 = slot1_a1859.get_collision(slot3_a1520, arg1_a1519)

			if slot4_a1514 then
				arg0_a1518.col = {
					obj = arg1_a1519,
					seg = slot3_a1520,
					objects = slot4_a1514
				}

				return false
			end

			arg0_a1518.ok = true
			arg0_a1518.obj = arg1_a1519
			arg0_a1518.seg = slot3_a1520

			return true
		end

		local function slot10_a1688(arg0_a1671)
			if player.charName == "Ezreal" then
				if arg0_a1396.cast_spell.slot == _Q and arg0_a1396.cast.pred() == 1 then
					return true
				end

				if arg0_a1671.seg.startPos:dist(arg0_a1671.seg.endPos) < 550 then
					return true
				end

				if arg0_a1671.seg.startPos:distSqr(arg0_a1671.obj.path.serverPos2D) < 302500 then
					return true
				end
			end

			if player.charName == "Zeri" or player.charName == "Lucian" then
				return true
			end

			if arg0_a1396.cast_spell.slot == _Q and player.charName == "Xerath" then
				if arg0_a1671.seg.startPos:dist(arg0_a1671.seg.endPos) < 550 then
					return true
				end

				if arg0_a1671.seg.startPos:distSqr(arg0_a1671.obj.path.serverPos2D) < 302500 then
					return true
				end
			end

			local slot1_a1610 = arg0_a1671.seg.startPos:distSqr(arg0_a1671.seg.endPos)

			if arg0_a1396.prediction.PredZs == 1 and slot1_a1610 > (arg0_a1396.prediction.range - arg0_a1671.obj.moveSpeed * 0.333)^2 then
				return false
			end

			if player.charName == "Brand" and arg0_a1396.prediction.Stun == 1 and arg0_a1396.cast_spell.slot == _Q and not arg0_a1671.obj.buff.brandablaze then
				return false
			end

			if slot4_a1687.hardlock(arg0_a1396.prediction, arg0_a1671.seg, arg0_a1671.obj) then
				return true
			end

			if slot4_a1687.hardlockmove(arg0_a1396.prediction, arg0_a1671.seg, arg0_a1671.obj) then
				return true
			end

			if slot12_a1341.trace.newpath(arg0_a1671.obj, arg0_a1396.prediction.mov, arg0_a1396.prediction.movtime) then
				if arg0_a1671.obj.path.active and arg0_a1671.obj.path.count > 0 then
					if (player.charName == "Nami" or player.charName == "Xerath") and arg0_a1671.obj.pos:dist(arg0_a1671.obj.path.point[1]) <= arg0_a1396.prediction.eymovds then
						return false
					else
						return true
					end
				end

				return true
			end
		end

		function slot1_a1859.get_ts_result()
			local slot0_a1714 = slot10_a1333.get_result(slot1_a1859.ts_loop, slot10_a1333.filter_set[arg0_a1396.target_selector.type])

			if not slot0_a1714 then
				return
			end

			if slot0_a1714.ok and slot10_a1688(slot0_a1714) then
				return slot0_a1714
			end

			if slot0_a1714.col then
				return nil, slot0_a1714.col
			end
		end

		function slot1_a1859.get_action()
			local slot0_a1742 = {
				state = slot7_a1384
			}

			if not slot1_a1859.can_action() then
				slot0_a1742.state = STATE_NOT_READY

				return nil, slot0_a1742
			end

			local slot1_a1741 = nil

			if arg0_a1396.target_selector then
				slot1_a1741 = slot1_a1859.get_ts_result()

				if not slot1_a1741 then
					slot0_a1742.state = slot9_a1386

					return nil, slot0_a1742
				end
			end

			slot0_a1742.ts_result = slot1_a1741
			slot0_a1742.state = slot6_a1383

			return slot0_a1742
		end

		function slot1_a1859.invoke_action(arg0_a1816)
			if arg0_a1816.state == slot6_a1383 then
				local slot1_a1830 = arg0_a1396.cast_spell.type
				local slot2_a1832 = arg0_a1396.cast_spell.slot
				local slot3_a1808 = arg0_a1396.cast_spell.arg1
				local slot4_a1840 = arg0_a1396.cast_spell.arg2
				local slot3_a1841 = slot3_a1808 and slot3_a1808(arg0_a1816)
				slot4_a1840 = slot4_a1840 and slot4_a1840(arg0_a1816)

				if player.charName == "Lucian" then
					player:castSpell(slot1_a1830, slot2_a1832, slot3_a1841, slot4_a1840)

					return true
				end

				if arg0_a1816.ts_result.obj.path.active and arg0_a1816.ts_result.obj.path.count > 0 then
					player:castSpell(slot1_a1830, slot2_a1832, slot3_a1841, slot4_a1840)
				else
					local slot5_a1826 = slot12_a1341.core.get_pos_after_time(arg0_a1816.ts_result.obj, arg0_a1816.ts_result.obj.moveSpeed / arg0_a1396.prediction.movseep)

					player:castSpell(slot1_a1830, slot2_a1832, slot5_a1826:to3D(arg0_a1816.ts_result.obj.y), slot4_a1840)
				end

				return true
			end
		end

		function slot1_a1859.easy_execute()
			local slot0_a1854 = slot1_a1859.get_action()

			if slot0_a1854 and slot0_a1854.state == slot6_a1383 then
				slot1_a1859.invoke_action(slot0_a1854)

				return true
			end
		end

		if arg0_a1396.visualize then
			-- Nothing
			--return slot1_a1859
		end

		return slot1_a1859
	end
}
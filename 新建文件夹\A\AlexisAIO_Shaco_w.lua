

local ove_0_10 = module.internal("TS")
local ove_0_11 = module.internal("pred")
local ove_0_12
local ove_0_13 = module.internal("orb")

if hanbot.language == 1 then
	print("Chinese Menu Loaded")

	local ove_0_14 = module.load(header.id, "Shaco/cnmenu")
else
	local ove_0_15 = module.load(header.id, "Shaco/menu")
end

local ove_0_16 = player:spellSlot(1)
local ove_0_17
local ove_0_18 = {
	range = 425,
	radius = 300,
	delay = 0.025,
	boundingRadiusMod = 1,
	speed = math.huge,
	damage = function(arg_5_0)
		return 999999999
	end
}

local function ove_0_19(arg_6_0, arg_6_1)
	local slot_6_0 = 0

	for iter_6_0 = 0, objManager.enemies_n - 1 do
		local slot_6_1 = objManager.enemies[iter_6_0]

		if slot_6_1 and not slot_6_1.isDead and slot_6_1.isVisible and slot_6_1.isTargetable and arg_6_1 > arg_6_0:dist(slot_6_1.pos) then
			slot_6_0 = slot_6_0 + 1
		end
	end

	return slot_6_0
end

function GetPercentHealth(arg_7_0)
	local slot_7_0 = arg_7_0 or player

	return slot_7_0.health / slot_7_0.maxHealth * 100
end

local ove_0_20
local ove_0_21 = 0

local function ove_0_22(arg_8_0, arg_8_1, arg_8_2)
	if arg_8_2 > ove_0_18.range then
		return false
	end

	arg_8_0.obj = arg_8_1
	arg_8_0.pos = arg_8_1.pos

	return true
end

local function ove_0_23()
	if ove_0_16.state == 0 and os.clock() > ove_0_21 then
		ove_0_20 = ove_0_10.get_result(ove_0_22)

		if ove_0_20.obj then
			return ove_0_20
		end
	end
end

local function ove_0_24()
	if ove_0_20.obj then
		player:castSpell("pos", _W, ove_0_20.pos)

		ove_0_20.obj = nil
	end

	ove_0_21 = os.clock() + network.latency + 0.25
end

local ove_0_25
local ove_0_26

local function ove_0_27()
	ove_0_25, ove_0_26 = ove_0_13.farm.skill_clear_target(ove_0_18)

	if ove_0_26 then
		return true
	end

	if ove_0_25 then
		return true
	end
end

local function ove_0_28()
	if ove_0_25 then
		local slot_12_0 = vec3(ove_0_25.endPos.x, ove_0_26.pos.y, ove_0_25.endPos.y)

		player:castSpell("pos", _W, slot_12_0)
	end
end

return {
	get_action_state = ove_0_23,
	invoke_action = ove_0_24,
	invoke_farm = ove_0_28,
	get_farm_state = ove_0_27
}


local ove_0_5 = module.load("<PERSON>","azir/menu")
local ove_0_6 = module.load("<PERSON>","azir/e_points")
local ove_0_7 = #ove_0_6
local ove_0_8 = vec3.array(4)
local ove_0_9
local ove_0_10

local function ove_0_11()
	-- print 1
	if ove_0_5.e.block:get() and not input.islocked_slot(_E) then
		input.lock_slot(_E)
	end

	if not ove_0_5.e.draw_points:get() then
		return
	end

	if ove_0_9 then
		if ove_0_9 < os.clock() and player:castSpell("pos", 0, ove_0_10) then
			ove_0_9 = nil
			ove_0_10 = nil
		end

		return true
	end

	for iter_1_0 = 1, ove_0_7 do
		local slot_1_0 = ove_0_6[iter_1_0]

		if slot_1_0.active then
			if player.pos2D:dist(slot_1_0.start_pos:to2D()) > 40 then
				return player:move(slot_1_0.start_pos)
			else
				player:castSpell("pos", 1, slot_1_0.w_cast)
				player:castSpell("pos", 2, slot_1_0.w_cast)

				ove_0_10 = slot_1_0.q_pos
				ove_0_9 = os.clock() + 0.25 + slot_1_0.delay
				slot_1_0.active = false

				return true
			end
		end
	end
end

local function ove_0_12()
	-- print 2
	if not ove_0_5.e.draw_points:get() then
		return
	end

	for iter_2_0 = 1, ove_0_7 do
		local slot_2_0 = ove_0_6[iter_2_0]
		local slot_2_1 = graphics.world_to_screen(slot_2_0.start_pos)

		if slot_2_1.x > 0 and slot_2_1.x < graphics.width and slot_2_1.y > 0 and slot_2_1.y < graphics.height then
			local slot_2_2 = 872367172

			if slot_2_0.active then
				slot_2_2 = 4282711876
			elseif mousePos:dist(slot_2_0.start_pos) < 100 then
				slot_2_2 = 864585864
			end

			graphics.draw_circle(slot_2_0.start_pos, 42, 2, slot_2_2, 24)

			local slot_2_3 = (slot_2_0.w_draw - slot_2_0.end_pos):norm()

			ove_0_8[0] = slot_2_0.start_pos + (slot_2_0.w_draw - slot_2_0.start_pos):norm() * 46
			ove_0_8[1] = slot_2_0.w_draw
			ove_0_8[2] = slot_2_0.end_pos + slot_2_3 * 80
			ove_0_8[3] = slot_2_0.end_pos + slot_2_3 * 100 + slot_2_3:perp1() * 15

			graphics.draw_line_strip(ove_0_8, 1, slot_2_2, 4)
		end
	end
end

local function ove_0_13(arg_3_0)
	-- print 3
	if not ove_0_5.e.draw_points:get() then
		return
	end

	if arg_3_0 ~= 1 then
		return
	end

	if player.par < 170 then
		return
	end

	if player:spellSlot(0).state ~= 0 then
		return
	end

	if player:spellSlot(1).state ~= 0 then
		return
	end

	if player:spellSlot(2).state > 8 then
		return
	end

	for iter_3_0 = 1, ove_0_7 do
		ove_0_6[iter_3_0].active = mousePos2D:dist(ove_0_6[iter_3_0].start_pos:to2D()) < 100
	end
end

local function ove_0_14(arg_4_0)
	-- print 4
	if not ove_0_5.e.draw_points:get() then
		return
	end

	if arg_4_0 ~= 1 then
		return
	end

	for iter_4_0 = 1, ove_0_7 do
		ove_0_6[iter_4_0].active = false
	end
end

return {
	invoke = ove_0_11,
	on_draw = ove_0_12,
	on_key_down = ove_0_13,
	on_key_up = ove_0_14
}

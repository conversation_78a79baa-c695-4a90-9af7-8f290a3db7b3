local delayAction = {}
local actions = {}
local executor

-- 实现DelayAction函数
-- @param func 要延迟执行的函数
-- @param delay 延迟时间，以秒为单位
-- @param args 可选参数列表
function delayAction.DelayAction(func, delay, args)
    if not executor then
        function executor()
            for t, funcs in pairs(actions) do
                if t <= os.clock() then
                    for i = 1, #funcs do
                        local f = funcs[i]
                        if f and f.func then
                            f.func(unpack(f.args or {}))
                        end
                    end
                    actions[t] = nil
                end
            end
        end
        cb.add(cb.tick, executor)
    end

    local t = os.clock() + (delay or 0)
    if actions[t] then
        actions[t][#actions[t] + 1] = {func = func, args = args}
    else
        actions[t] = {{func = func, args = args}}
    end
end

return delayAction 
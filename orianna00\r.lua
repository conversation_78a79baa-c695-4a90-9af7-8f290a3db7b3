
local ove_0_5 = module.internal("TS")
local ove_0_6 = module.internal("pred")
local ove_0_7 = module.internal("orb")
local ove_0_8 = module.load(header.id,"orianna/ball")
local ove_0_9 = module.load(header.id,"orianna/menu")
local ove_0_10 = module.load(header.id,"orianna/r_block")
local ove_0_11 = module.load(header.id,"orianna/dmg")
local ove_0_12 = table.insert
local ove_0_13 = player:spellSlot(3)
local ove_0_14
local ove_0_15 = 172225
local ove_0_16 = {
	radius = 415,
	dashRadius = 0,
	boundingRadiusModTarget = 0,
	delay = 0.5,
	boundingRadiusModSource = 0
}

local function ove_0_17(arg_1_0, arg_1_1, arg_1_2)
	-- function 1
	if arg_1_2 > 3000 then
		return
	end

	if arg_1_1.path.serverPos2D:distSqr(ove_0_8.serverPos2D()) < ove_0_15 and ove_0_6.present.get_prediction(ove_0_16, arg_1_1, ove_0_14) then
		ove_0_12(arg_1_0, arg_1_1)
	end
end

local ove_0_18

local function ove_0_19()
	-- function 2
	local slot_2_0 = 0

	for iter_2_0 = 1, #ove_0_18 do
		if ove_0_9.auto_r[ove_0_18[iter_2_0].charName]:get() then
			slot_2_0 = slot_2_0 + 1
		end
	end

	return slot_2_0
end

local ove_0_20 = {
	0,
	0.66,
	1,
	1.33
}

local function ove_0_21()
	-- function 3
	local slot_3_0 = player.flatMagicDamageMod * player.percentMagicDamageMod + 1
	local slot_3_1 = ove_0_11.q(slot_3_0) * 2 + ove_0_11.w(slot_3_0) + ove_0_11.r(slot_3_0)
	local slot_3_2 = 0
	local slot_3_3 = 1

	for iter_3_0 = 1, #ove_0_18 do
		if ove_0_11.calc(ove_0_18[iter_3_0], slot_3_1) * ove_0_20[ove_0_9.smart_r[ove_0_18[iter_3_0].charName]:get()] * slot_3_3 > ove_0_18[iter_3_0].health then
			slot_3_2 = slot_3_2 + 1
			slot_3_3 = slot_3_3 + 0.66
		end
	end

	return slot_3_2
end

local function ove_0_22(arg_4_0)
	-- function 4
	if ove_0_13.state == 0 then
		ove_0_14 = ove_0_8.source()
		ove_0_18 = arg_4_0 or ove_0_5.loop(ove_0_17)

		if ove_0_18[1] then
			if (not ove_0_9.auto_r.combat:get() or ove_0_9.combat:get()) and ove_0_19() >= ove_0_9.auto_r.n:get() then
				return true
			end

			if (not ove_0_9.smart_r.combat:get() or ove_0_9.combat:get()) and ove_0_21() >= ove_0_9.smart_r.n:get() then
				return true
			end
		end
	end
end

local function ove_0_23()
	-- function 5
	ove_0_10.ignore_next_cast()

	if player:castSpell("self", 3) then
		ove_0_7.core.set_server_pause()
	end
end

return {
	get_action_state = ove_0_22,
	invoke_action = ove_0_23
}

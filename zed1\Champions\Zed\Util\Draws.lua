local ove_0_10 = module.load("Kloader", "Champions/Zed/Util/Detection")
local ove_0_11 = module.load("Kloader", "Lib/MyCommon")
local ove_0_12 = module.load("Kloader", "Champions/Zed/Util/menu")
local ove_0_13 = module.load("Kloader", "Champions/Zed/Spells/W")
local ove_0_14 = module.load("Kloader", "Champions/Zed/Spells/R")
local ove_0_15 = module.load("Kloader", "Champions/Zed/Spells/E")
local ove_0_16 = module.load("Kloader", "Champions/Zed/Spells/Q")
local ove_0_17 = module.load("Kloader", "Champions/Zed/Util/Damage")
local ove_0_18 = player

return {
	Execute = function()
		-- print 5
		if ove_0_12.drawings.disable:get() or ove_0_18.isDead or not ove_0_18.isOnScreen then
			return
		end

		local slot_5_0 = ove_0_18.pos

		if ove_0_12.drawings.dmgCalc:get() then
			for iter_5_0, iter_5_1 in pairs(ove_0_11.GetEnemyHeroes()) do
				local slot_5_1 = ove_0_17.getTotalDmg(iter_5_1)

				ove_0_11.DrawDamage(iter_5_1, slot_5_1)
			end
		end

		if ove_0_18.isOnScreen then
			if ove_0_12.drawings.qdraw:get() and ove_0_16.Ready() and ove_0_16.Slot.level >= 1 then
				graphics.draw_circle(slot_5_0, ove_0_16.range, 1, ove_0_12.drawings.qcolor:get(), 100)
			end

			if ove_0_12.drawings.wdraw:get() and ove_0_13.Ready() and ove_0_13.Slot.level >= 1 then
				graphics.draw_circle(slot_5_0, ove_0_13.range, 1, ove_0_12.drawings.wcolor:get(), 100)
			end

			if ove_0_12.drawings.edraw:get() and ove_0_15.Ready() and ove_0_15.Slot.level >= 1 then
				graphics.draw_circle(slot_5_0, ove_0_15.range, 1, ove_0_12.drawings.ecolor:get(), 100)
			end

			if ove_0_12.drawings.rdraw:get() and ove_0_14.Ready() and ove_0_14.Slot.level >= 1 then
				graphics.draw_circle(slot_5_0, ove_0_14.range, 1, ove_0_12.drawings.rcolor:get(), 100)
			end

			if ove_0_10.Wpos and ove_0_12.drawings.drawshadowW.enable:get() then
				graphics.draw_circle(ove_0_10.Wpos, 100, 1, graphics.argb(222, 231, 254, 255), 100)
			end

			if ove_0_10.Rpos and ove_0_12.drawings.drawshadowR.enable:get() then
				graphics.draw_circle(ove_0_10.Rpos, 100, 1, graphics.argb(222, 231, 254, 255), 100)
			end

			if ove_0_12.drawings.drawshadowW.enable:get() and ove_0_12.drawings.drawshadowW.timer:get() and ove_0_10.Wpos then
				local slot_5_2 = graphics.world_to_screen(ove_0_10.Wpos)

				graphics.draw_text_2D("Shadow Duration :" .. math.floor(ove_0_10.startTime - os.clock(), 2) .. "s", 15, slot_5_2.x, slot_5_2.y, graphics.argb(222, 231, 254, 255))
			end

			if ove_0_12.drawings.drawshadowR.enable:get() and ove_0_12.drawings.drawshadowR.timer:get() and ove_0_10.Rpos then
				local slot_5_3 = graphics.world_to_screen(ove_0_10.Rpos)

				graphics.draw_text_2D("Shadow Duration :" .. math.floor(ove_0_10.startTimer - os.clock(), 2) .. "s", 15, slot_5_3.x, slot_5_3.y, graphics.argb(222, 231, 254, 255))
			end

			if ove_0_12.drawings.combomode:get() and ove_0_12.combo.asasinkey:get() then
				local slot_5_4 = graphics.world_to_screen(slot_5_0)

				if ove_0_12.combo.rlogic:get() == 1 then
					graphics.draw_text_2D("Current Combo Mode: Line", 15, slot_5_4.x - 70, slot_5_4.y + 15, graphics.argb(222, 231, 254, 255))
				elseif ove_0_12.combo.rlogic:get() == 2 then
					graphics.draw_text_2D("Current Combo Mode Rectangle", 15, slot_5_4.x - 70, slot_5_4.y + 15, graphics.argb(222, 231, 254, 255))
				elseif ove_0_12.combo.rlogic:get() == 3 then
					graphics.draw_text_2D("Current Combo Mode: Mouse Position", 15, slot_5_4.x - 70, slot_5_4.y + 15, graphics.argb(222, 231, 254, 255))
				end
			end

			if ove_0_12.drawings.asasinmode:get() then
				local slot_5_5 = graphics.world_to_screen(slot_5_0)
				local slot_5_6 = ove_0_12.combo.asasinkey:get() and "Enabled" or "Disabled"

				graphics.draw_text_2D("Assasin Mode : " .. slot_5_6, 15, slot_5_5.x - 70, slot_5_5.y, graphics.argb(222, 231, 254, 255))
			end
		end

		if ove_0_12.misc.turretdive.drawturret:get() then
			for iter_5_2, iter_5_3 in pairs(ove_0_11.GetEnemyTowers()) do
				if iter_5_3 and ove_0_18.pos:dist(iter_5_3.pos) < 1500 then
					graphics.draw_circle(iter_5_3.pos, 875, 1, graphics.argb(222, 231, 254, 255), 100)
				end
			end
		end
	end
}

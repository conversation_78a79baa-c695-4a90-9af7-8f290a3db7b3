local ove_0_10 = module.internal("orb")
local ove_0_11 = module.seek("evade")
local ove_0_12 = module.load(header.id, "common/common")
local ove_0_13 = module.load(header.id, "plugins/KSante/helper")
local ove_0_14 = module.load(header.id, "plugins/KSante/menu")

return {
	get_magnet_now = function(arg_5_0, arg_5_1, arg_5_2)
		if ove_0_10.combat.target and ove_0_13.Sticky and arg_5_0 == 2 and ove_0_10.combat.target.path.serverPos2D:dist(player.path.serverPos2D) >= player.boundingRadius then
			arg_5_1.x = ove_0_10.combat.target.x
			arg_5_1.z = ove_0_10.combat.target.z
		end
	end,
	Magnet = function()
		if ove_0_11 and ove_0_11.core.is_active() then
			ove_0_13.Sticky = false

			return
		end

		if (ove_0_10.combat.is_active() or ove_0_10.menu.hybrid.key:get()) and ove_0_10.combat.target and ove_0_12.IsValidTarget(ove_0_10.combat.target) and ove_0_10.combat.target.pos:dist(player.pos) <= ove_0_12.GetAARange(ove_0_10.combat.target) and game.mousePos:dist(ove_0_10.combat.target.pos) <= ove_0_14.misc.move.magR:get() then
			ove_0_13.Sticky = true

			return
		end

		if ove_0_13.Sticky then
			ove_0_13.Sticky = false
		end
	end
}

local TS = module.internal('TS')
local orb = module.internal('orb')
local pred = module.internal('pred')
--local lvxbot = module.load(header.id, 'ezreal/menu')


-- 动态速度阈值，根据英雄类型调整
local function get_champion_thresholds(champion_name)
    local profiles = {
        ["Yasuo"] = {high = 850, mid = 600, low = 400},
        ["Zed"] = {high = 800, mid = 550, low = 350},
        ["Cassiopeia"] = {high = 600, mid = 400, low = 250},
        ["Jinx"] = {high = 700, mid = 450, low = 300},
        ["Vayne"] = {high = 750, mid = 500, low = 325}
    }
    return profiles[champion_name] or {high = 750, mid = 500, low = 325}
end

local kalman_ms_high = 750
local kalman_ms_mid = 500
local kalman_ms_low = 325

local function class()

	local cls = {}
	cls.__index = cls

	return setmetatable(cls, { __call = function (c, ...)
		local instance = setmetatable({}, cls)
		
		if cls.__init then
	
			cls.__init(instance, ...)
		end
		return instance
	end})
end

local kalman_filter = class()

function kalman_filter:__init()
    self.current_state_estimate = 0
    self.current_prob_estimate = 0
    self.Q = 1
    self.R = 15

end
function kalman_filter:step(control_vector, measurement_vector)
    local predicted_state_estimate = self.current_state_estimate + control_vector
    local predicted_prob_estimate = self.current_prob_estimate + self.Q
    local innovation = measurement_vector - predicted_state_estimate
    local innovation_covariance = predicted_prob_estimate + self.R
    local kalman_gain = predicted_prob_estimate / innovation_covariance
    self.current_state_estimate = predicted_state_estimate + kalman_gain * innovation
    self.current_prob_estimate = (1 - kalman_gain) * predicted_prob_estimate
    return self.current_state_estimate
end

local kalman = class()


kalman.eneplayeres = {}
kalman.kalmanFilters = {}
kalman.velocityTimers = {}
kalman.oldPosx = {}
kalman.oldPosz = {}
kalman.oldTick = {}
kalman.velocity = {}
kalman.lastboost = {}
kalman.velocity_TO = 10
kalman.CONVERSATION_FACTOR = 975
kalman.MS_MIN = 325 -- default value, may need to be changed, used in "update velocity"



function kalman:__init()
    for i=0, objManager.enemies_n-1 do
	local hero = objManager.enemies[i]
        table.insert(self.eneplayeres, hero)
        self.kalmanFilters[hero.networkID] = kalman_filter()
        self.velocityTimers[hero.networkID] = 0
        self.oldPosx[hero.networkID] = 0
        self.oldPosz[hero.networkID] = 0
        self.oldTick[hero.networkID] = 0
        self.velocity[hero.networkID] = 0
        self.lastboost[hero.networkID] = 0
		--chat.send(self.velocityTimers[hero.networkID])

    end
	cb.add(cb.tick, function()
kalman:UpdateSpeed()

end)
end

local function CheckBuffType(obj, bufftype)
    if obj then
        for i = 0, obj.buffManager.count - 1 do
            local buff = obj.buffManager:get(i)
            if buff and buff.valid and buff.type == bufftype and (buff.stacks > 0 or buff.stacks2 > 0) then
                return true
            end 
        end 
    end   
end

local function IsValidTarget(object)
    return (object and not object.isDead and object.isVisible and object.isTargetable and not CheckBuffType(object, 18))
end


function kalman:hasVelocity(velocity,target,time)
    if target and IsValidTarget(target) then
        return (self.velocity[target.networkID] < velocity and target.moveSpeed < velocity and 1000 * game.time - self.lastboost[target.networkID] > time)
    else
        return nil
    end
end

function kalman:_calcHeroVelocity(target, oldPosx, oldPosz, oldTick)
    if oldPosx and oldPosz and target.x and target.z then
        local dis = math.sqrt((oldPosx - target.pos.x) ^ 2 + (oldPosz - target.pos.z) ^ 2)
        self.velocity[target.networkID] = self.kalmanFilters[target.networkID]:step(0, (dis / (1000 * game.time - oldTick)) * self.CONVERSATION_FACTOR)
    end
end

function kalman:UpdateSpeed()
    local tick = 1000 * game.time
	    for i=0, objManager.enemies_n-1 do
	    local hero = objManager.enemies[i]
		
         if hero and self.velocityTimers[hero.networkID] and self.velocityTimers[hero.networkID] <= tick  and hero.pos.x and hero.pos.z and (tick - self.oldTick[hero.networkID]) > (self.velocity_TO - 1) then
		 -- chat.send(self.velocityTimers,tick)
            self.velocityTimers[hero.networkID] = tick + self.velocity_TO
            self:_calcHeroVelocity(hero, self.oldPosx[hero.networkID], self.oldPosz[hero.networkID], self.oldTick[hero.networkID])
            self.oldPosx[hero.networkID] = hero.x
            self.oldPosz[hero.networkID] = hero.z
            self.oldTick[hero.networkID] = tick
            if self.velocity[hero.networkID] > self.MS_MIN then
                self.lastboost[hero.networkID] = tick
            end
        end
    end
end



local create = function(input)
  local module = {
    input = input,
  }

  local pred_func
  local is_in_range_check

  if input.prediction then
    if input.prediction.type == 'Linear' then
      pred_func = pred.linear
    elseif input.prediction.type == 'InRange' then
      pred_func = pred.present
      is_in_range_check = true
    end
  end

  local ts_filter
  if input.target_selector then
    if input.target_selector then
	--print(input.target_selector.type)
      ts_filter = TS.filter_set[input.target_selector.type]
	--  print(ts_filter)
    end
  end

  local STATE_OK = 0
  local STATE_NONE = 1
  local STATE_CAN_NOT_ACTION = 2
  local STATE_NO_TS_RESULT = 3

  module.STATE_OK = STATE_OK
  module.STATE_NONE = STATE_NONE
  module.STATE_CAN_NOT_ACTION = STATE_CAN_NOT_ACTION
  module.STATE_NO_TS_RESULT = STATE_NO_TS_RESULT

  module.can_action = function()
--print(orb.core.can_cast_spell(input.slot))
  return orb.core.can_cast_spell(input.slot)
  end

  module.get_prediction = function(obj)
    if not input.prediction then
      return false
    end
	--print(input.target_selector.type)
    local src = input.prediction.source
    local seg = pred_func.get_prediction(input.prediction, obj, src)
    if not seg then
      return false
    end
    if is_in_range_check then
      return true
    end
    if seg:length() < input.prediction.range then
	local tempAngle = mathf.angle_between(seg.endPos, player.pos:to2D(), obj.pos:to2D()) *180/math.pi
	  --chat.send(tempAngle)

      return seg
    end
  end

  module.get_collision = function(seg, obj)
    if not input.prediction.collision then
      return false
    end
    return pred.collision.get_prediction(input.prediction, seg, obj)
  end

  module.ts_loop = function(res, obj, dist)
    if dist > input.ignore_obj_radius then
     return false
    end
    local seg = module.get_prediction(obj)
    if not seg then
      return false
    end
    local col = module.get_collision(seg, obj)
    if col then
      res.col = {
        obj = obj,
        seg = seg,
        objects = col,
      }
      return false
    end
    res.ok = true
    res.obj = obj
    res.seg = seg
    return true
  end

  local t = function(Eres)

  if input.cast_spell.slot == _R then
 
  end
  
local predcast = input.cast.pred()
if predcast == 2 then

   if pred.trace.linear.hardlock(input.prediction, Eres.seg, Eres.obj) then
    return true
  end
   if pred.trace.linear.hardlockmove(input.prediction, Eres.seg, Eres.obj) then
    return true
  end
   	if Eres.seg.startPos:dist(Eres.seg.endPos) < 550 then
	return true
	end
	 if Eres.seg.startPos:distSqr(Eres.obj.path.serverPos2D) < 550 * 550 then
	return true
	end
	
	
   --  if pred.trace.newpath(Eres.obj, 0.022, 0.185) and kalman:hasVelocity(kalman_ms_high, Eres.obj, 1350) then
    --chat.send("1")
       -- return true
   -- elseif pred.trace.newpath(Eres.obj, 0.022, 0.310) and kalman:hasVelocity(kalman_ms_high, Eres.obj, 550) then
   --chat.send("2")
    --    return true
    if pred.trace.newpath(Eres.obj, 0.015, 0.095) and kalman:hasVelocity(kalman_ms_high, Eres.obj, 50) then
	--chat.send("3")
        return true
    elseif pred.trace.newpath(Eres.obj, 0.033, 0.245) and kalman:hasVelocity(kalman_ms_mid, Eres.obj, 1350) then
	--chat.send("4")
        return true
    elseif pred.trace.newpath(Eres.obj, 0.033, 0.370) and kalman:hasVelocity(kalman_ms_mid, Eres.obj, 550) then
	--chat.send("5")
        return true
    elseif pred.trace.newpath(Eres.obj, 0.010, 0.105) and kalman:hasVelocity(kalman_ms_mid, Eres.obj, 50) then
	--chat.send("6")
        return true
    elseif pred.trace.newpath(Eres.obj, 0.033, 0.305) and kalman:hasVelocity(kalman_ms_low, Eres.obj, 1350) then
   --chat.send("7")
        return true
    elseif pred.trace.newpath(Eres.obj, 0.033, 0.550) and kalman:hasVelocity(kalman_ms_low, Eres.obj, 550) then
	--chat.send("8")
        return true
    elseif pred.trace.newpath(Eres.obj, 0.01, 0.125) and kalman:hasVelocity(kalman_ms_low, Eres.obj, 50) then
	--chat.send("9")
        return true
    elseif pred.trace.newpath(Eres.obj, 0.025, 0.200) and kalman:hasVelocity(kalman_ms_low, Eres.obj, 200) then
	--chat.send("10")
        return true
   -- elseif kalman.velocity[Eres.obj.networkID] < 50 and 1000 * game.time - kalman.lastboost[Eres.obj.networkID] > 500 then
	--chat.send(kalman.lastboost[Eres.obj.networkID],"11")
	--chat.send("11")
     --   return true
   -- elseif kalman.velocity[Eres.obj.networkID] < kalman_ms_high and 1000 * game.time - kalman.lastboost[Eres.obj.networkID] < 55 then
	--chat.send(kalman.lastboost[Eres.obj.networkID],"12")
	--chat.send("12")
      --  return true
    elseif kalman.velocity[Eres.obj.networkID] < kalman_ms_mid and 1000 * game.time - kalman.lastboost[Eres.obj.networkID] < 75 then
	--chat.send(kalman.lastboost[Eres.obj.networkID],"13")
	--=chat.send("13")
        return true
    end
  
  
  
  else
  return true
  end
end
  
  
  module.get_ts_result = function()
  
    local ts_result = TS.get_result(module.ts_loop, TS.filter_set[input.target_selector.type])
    if not ts_result then
      return
    end
    if ts_result.ok and t(ts_result) then
      return ts_result
    end
    if ts_result.col then
      return nil, ts_result.col
    end
  end

  module.get_action = function()
    local action = {
      state = STATE_NONE,
    }
    if not module.can_action() then
      action.state = STATE_NOT_READY
      return nil, action
    end
    local ts_result
    if input.target_selector then
      ts_result = module.get_ts_result()
      if not ts_result then
        action.state = STATE_NO_TS_RESULT
        return nil, action
      end
    end
    action.ts_result = ts_result
    action.state = STATE_OK
    return action
  end

  module.invoke_action = function(action)
    if action.state == STATE_OK then
      local type = input.cast_spell.type
      local slot = input.cast_spell.slot
      local arg1 = input.cast_spell.arg1
      local arg2 = input.cast_spell.arg2
      arg1 = arg1 and arg1(action)
      arg2 = arg2 and arg2(action)
	  
	
      player:castSpell(type, slot, arg1, arg2)
      return true
    end
  end

  module.easy_execute = function()
    local action = module.get_action()
    if action and action.state == STATE_OK then
      module.invoke_action(action)
      return true
    end
  end

  if input.visualize then

  end

  return module
end




kalman()
kalman_filter()


return {
  create = create,
}
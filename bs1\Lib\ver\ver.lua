math.randomseed(0.198157)

local ove_0_0

ove_0_0 = {
	function(...)
		-- function 1
		local slot_1_0 = {
			...
		}

		return ove_0_0[8](slot_1_0)
	end,
	cb,
	game,
	math.frexp,
	math.random(1, 1000),
	string.dump,
	string.sub,
	table.concat,
	cb,
	"bot",
	loadstring,
	"b+",
	function(arg_2_0)
		-- function 2
		local slot_2_0 = loadstring(arg_2_0)

		if slot_2_0 then
			return ove_0_0[tonumber("20")](function()
				-- function 3
				slot_2_0()
			end)
		else
			return nil
		end
	end,
	"load",
	1234567890,
	getfenv,
	"",
	"jit",
	0.1,
	pcall,
	math.pi,
	""
}

local ove_0_1 = ove_0_0[5]

local function ove_0_2(arg_4_0)
	-- function 4
	return tostring(arg_4_0 / ove_0_1)
end

local ove_0_3 = {
	ove_0_2(14364),
	ove_0_2(17592),
	ove_0_2(22043),
	ove_0_2(14210),
	ove_0_2(24841),
	ove_0_2(4077),
	ove_0_2(714),
	ove_0_2(26056),
	ove_0_2(7038),
	ove_0_2(27134),
	ove_0_2(19591),
	ove_0_2(9219),
	ove_0_2(9403),
	ove_0_2(16315),
	ove_0_2(16175),
	ove_0_2(6054),
	ove_0_2(28984),
	ove_0_2(17744),
	ove_0_2(18836),
	ove_0_2(4690),
	ove_0_2(29158),
	ove_0_2(13939),
	ove_0_2(19675),
	ove_0_2(29604),
	ove_0_2(16056),
	ove_0_2(26338),
	ove_0_2(15139),
	ove_0_2(23890),
	ove_0_2(19042),
	ove_0_2(14093),
	ove_0_2(2408),
	ove_0_2(28022),
	ove_0_2(865),
	ove_0_2(16655),
	ove_0_2(30886),
	ove_0_2(29481),
	ove_0_2(1595),
	ove_0_2(20963),
	ove_0_2(31297),
	ove_0_2(20820),
	ove_0_2(17672),
	ove_0_2(725),
	ove_0_2(13220),
	ove_0_2(963),
	ove_0_2(18481),
	ove_0_2(1846),
	ove_0_2(25896),
	ove_0_2(31355),
	ove_0_2(27768),
	ove_0_2(596),
	ove_0_2(14392),
	ove_0_2(16206),
	ove_0_2(16952),
	ove_0_2(31146),
	ove_0_2(14516),
	ove_0_2(6572),
	ove_0_2(29166),
	ove_0_2(14163),
	ove_0_2(32146),
	ove_0_2(9404),
	ove_0_2(18937),
	ove_0_2(12485),
	ove_0_2(17611),
	ove_0_2(2987),
	ove_0_2(22942),
	ove_0_2(17851)
}
local ove_0_4, ove_0_5 = ove_0_0[9].get(2)

if not ove_0_5 then
	return
end

local ove_0_6 = {
	core = {}
}

ove_0_6.version = 2025

local ove_0_7 = os.time({
	hour = 22,
	month = 8,
	year = 2024,
	second = 0,
	minute = 0,
	day = 14
})
local ove_0_8 = os.time({
	hour = 10,
	month = 10,
	year = 2023,
	second = 0,
	minute = 0,
	day = 10
})
local ove_0_9 = os.time({
	hour = 22,
	month = 12,
	year = 2024,
	second = 0,
	minute = 0,
	day = 31
})
local ove_0_10 = false
local ove_0_11 = "14.16"
local ove_0_12 = "14.17"
local ove_0_13 = "14.15"

if CN then
	chat.print("<font color='#ff0084'>BS版本:</font><font color='#ff0000'>" .. tostring(ove_0_6.version) .. "</font>")
else
	chat.print("<font color='#ff0084'>BS_VERSION:</font><font color='#ff0000'>" .. tostring(ove_0_6.version) .. "</font>")
end

local ove_0_14 = "BS" .. game.version

if ove_0_14:find("0000") then
--print(15388)
	return
end



local ove_0_15 = "http://hjfjhfrppytt" .. os.date("%m%d") .. ".isasscript.top:8080"
local ove_0_16 = {
	"http://hjfjhfrppytt" .. os.date("%m%d") .. ".isasscript.top:8080",
	"http://***************:8080",
	"http://***************:8080",
	"http://127.0.0.1"
}
local ove_0_17 = "sa_lib.json"
local ove_0_18 = ""
local ove_0_19 = ""
local ove_0_20 = ""
local ove_0_21 = module.load(header.id, "bs/Lib/ver/MD5")
local ove_0_22 = module.load(header.id, "bs/Lib/ver/JsonUtility")
local ove_0_23 = module.load(header.id, "bs/Lib/ver/AsEncode")
local ove_0_24 = hanbot.path
local ove_0_25 = "@111jjjhjhkjkhfdsdfds4445gfd4g54as@"
local ove_0_26 = "@222jbjhkdjhnassd75a54421@"
local ove_0_27 = "@333JKJKghjhjgfhjjhlsdfhjhjdsgg11441sd@"
local ove_0_28 = "@ppgfbmm58545122100ggfgf@"
local ove_0_29 = "@555544554hjhjhGVHVCB112NJMNJMNJm@"
local ove_0_30 = "112221111dasb@@sb21200llkfddbb"
local ove_0_31 = "@GFJKJKSJHFKDJHKFJKH3J43JKHJKHGFJKDHKJHGkjjnhjhghJGHJG@"
local ove_0_32 = "jhhjhjggf12d211tretre00000yt"
local ove_0_33 = "@kkjgfhhjfdjkhjkhfdjk54545445hjhjnf0fgfjhijukre0hgfgbhsd21"
local ove_0_34 = "@jkhjhjgfjhgreghfhgfjgfehgfwrewrew55454"
local ove_0_35 = "@jhhjhjfdgjkfdjkjhgjgjkfdjkghfdjkhtrejhtrejhktrrjkethjgerjhgflkhkjhfg254254fdffdsfds"
local ove_0_36 = "@hjhjgfjhdgjkdfjhgjrekgfdgfdg4445f54hgfg45dh54gf54hgfhgf54421re"
local ove_0_37 = "@jkhjhjgfjhgreghfhgfjffdwrew55454"

local function ove_0_38(arg_5_0)
	-- function 5
	local slot_5_0 = os.time()

	if arg_5_0 then
		slot_5_0 = arg_5_0
	end

	local slot_5_1 = slot_5_0
	local slot_5_2 = 8
	local slot_5_3 = 8 - slot_5_2
	local slot_5_4 = os.date("*t").isdst
	local slot_5_5 = os.time(serverTimeTable) + slot_5_3 * 3600 - (slot_5_4 and 1 or 0) * 3600
	local slot_5_6 = tonumber(os.date("%Y%m%d", slot_5_5))
	local slot_5_7 = tonumber(20 .. ove_0_6.version)

	if slot_5_7 > slot_5_6 + 8 or slot_5_6 > slot_5_7 + 8 then
		local slot_5_8 = io.open(ove_0_24 .. "/RU_ERROR.log", "w")

		slot_5_8:write("VERSION_TIME_OUT")
		io.close(slot_5_8)
		chat.print("<font color='#FFFFFF00'>VERSION_TIME_OUT</font>")

		ove_0_6.core.is_auth = true
		ove_0_6.core.auth = false
		ove_0_6.core.auth_msg_cn = "ERROR_版本错误时间 请尝试更新到最新版本"
		ove_0_6.core.auth_msg = "VERSION_TIME_OUT"

		return
	end

	if slot_5_5 < ove_0_8 then
		local slot_5_9 = io.open(ove_0_24 .. "/RU_ERROR.log", "w")

		slot_5_9:write("VERSION_TIME_OUT")
		io.close(slot_5_9)
		chat.print("<font color='#FFFFFF00'>VERSION_TIME_OUT</font>")

		ove_0_6.core.is_auth = true
		ove_0_6.core.auth = false
		ove_0_6.core.auth_msg_cn = "ERROR_版本错误时间2 请尝试更新到最新版本"
		ove_0_6.core.auth_msg = "VERSION_TIME_OUT2"

		return
	end

	if slot_5_5 > ove_0_9 then
		ove_0_6.msg2 = "ILLEGAL_TIME"

		local slot_5_10 = io.open(ove_0_24 .. "/RU_ERROR.log", "w")

		slot_5_10:write("ILLEGAL_TIME")
		io.close(slot_5_10)
		chat.print("<font color='#FFFFFF00'>ILLEGAL_TIME</font>")

		ove_0_6.core.is_auth = true
		ove_0_6.core.auth = false
		ove_0_6.core.auth_msg_cn = "ERROR_非法时间"
		ove_0_6.core.auth_msg = "ILLEGAL_TIME"

		return
	end

	if slot_5_5 > ove_0_7 + 691200 then
		ove_0_6.msg2 = "OUT_VERSION_TIME"

		local slot_5_11 = io.open(ove_0_24 .. "/RU_ERROR.log", "w")

		slot_5_11:write("OUT_VERSION_TIME")
		io.close(slot_5_11)
		chat.print("<font color='#FFFFFF00'>OUT_VERSION_TIME</font>")

		ove_0_6.core.is_auth = true
		ove_0_6.core.auth = false
		ove_0_6.core.auth_msg_cn = "ERROR_过期版本时间 请尝试更新到最新版本"
		ove_0_6.core.auth_msg = "OUT_VERSION_TIME"

		return
	elseif slot_5_5 - ove_0_7 > 691200 then
		ove_0_6.msg2 = "OUT_VERSION_TIME2"

		local slot_5_12 = io.open(ove_0_24 .. "/RU_ERROR.log", "w")

		slot_5_12:write("OUT_VERSION_TIME2")
		io.close(slot_5_12)
		chat.print("<font color='#FFFFFF00'>OUT_VERSION_TIME2</font>")

		ove_0_6.core.is_auth = true
		ove_0_6.core.auth = false
		ove_0_6.core.auth_msg_cn = "ERROR_过期版本时间2 请尝试更新到最新版本"
		ove_0_6.core.auth_msg = "OUT_VERSION_TIME2"

		return
	end

	return true
end

ove_0_38(os.time())

local ove_0_39 = (function()
	-- function 6
	return hanbot.hwid2
end)()

local function ove_0_40(arg_7_0, arg_7_1)
	-- function 7
	for iter_7_0 = 1, #arg_7_0 do
		if arg_7_0[iter_7_0] == arg_7_1 then
			return false
		end
	end

	return true
end

ove_0_6.file_url = ove_0_15 .. "/SA/"

function ove_0_6.UpdateIp()
	-- function 8
	local slot_8_0 = io.open(ove_0_24 .. "/server.txt", "r")

	if slot_8_0 then
		local slot_8_1 = slot_8_0:read("*all")
		local slot_8_2 = string.gsub(slot_8_1, "^%s*(.-)%s*$", "%1")

		io.close(slot_8_0)

		if slot_8_2 and ove_0_16[tonumber(slot_8_2)] then
			ove_0_15 = ove_0_16[tonumber(slot_8_2)]
			ove_0_6.file_url = ove_0_15 .. "/SA/"

			chat.print("<font color='#FFFF0000'>" .. "[server] - " .. tonumber(slot_8_2) .. "</font>")
		end
	end
end

local function ove_0_41(arg_9_0, arg_9_1)
	-- function 9
	math.randomseed(os.clock() * math.random(1000000, 90000000) + math.random(1000000, 90000000))

	return math.random(arg_9_0, arg_9_1)
end

local function ove_0_42(arg_10_0)
	-- function 10
	local slot_10_0 = ""

	for iter_10_0 = 1, arg_10_0 do
		if iter_10_0 == 1 then
			slot_10_0 = slot_10_0 .. ove_0_41(1, 9)
		else
			slot_10_0 = slot_10_0 .. ove_0_41(1, 9)
		end
	end

	return slot_10_0
end

local function ove_0_43(arg_11_0)
	-- function 11
	local slot_11_0 = ""

	for iter_11_0 = 1, arg_11_0 do
		slot_11_0 = slot_11_0 .. string.char(ove_0_41(97, 122))
	end

	return slot_11_0
end

local function ove_0_44(arg_12_0)
	-- function 12
	local slot_12_0

	slot_12_0 = arg_12_0 or os.time()

	local slot_12_1 = 8
	local slot_12_2 = 8 - slot_12_1
	local slot_12_3 = os.date("*t").isdst

	return os.time(serverTimeTable) + slot_12_2 * 3600 - (slot_12_3 and 1 or 0) * 3600
end

local function ove_0_45(arg_13_0)
	-- function 13
	local slot_13_0

	slot_13_0 = arg_13_0 or os.time()

	local slot_13_1 = 8
	local slot_13_2 = os.date("*t").isdst

	return os.time(serverTimeTable) + slot_13_1 * 3600 - (slot_13_2 and 1 or 0) * 3600
end

local function ove_0_46(arg_14_0, arg_14_1)
	-- function 14
	print("Get1")
	
	

	if arg_14_0 ~= 200 then
		if ove_0_10 then
			return
		end

		local slot_14_0 = io.open(ove_0_24 .. "/RU_ERROR.log", "w")

		slot_14_0:write("ERROR_CONN")
		io.close(slot_14_0)
		chat.print("<font color='#FFFFFF00'>ERROR_CONN_TB" .. tostring(arg_14_0) .. "</font>")

		ove_0_6.core.is_auth = true
		ove_0_6.core.auth = false
		ove_0_6.core.auth_msg_cn = "ERROR_连接错误"
		ove_0_6.core.auth_msg = "ERROR_CONN_TB"

		return
	end

	local slot_14_1 = arg_14_1
	local slot_14_2 = ove_0_22.parse(slot_14_1).data.t / 1000
	local slot_14_3 = ove_0_44(slot_14_2)
	local slot_14_4 = os.time()
	local slot_14_5 = 8
	local slot_14_6 = 8 - slot_14_5
	local slot_14_7 = os.date("*t").isdst
	local slot_14_8 = os.time(serverTimeTable) + slot_14_6 * 3600 - (slot_14_7 and 1 or 0) * 3600

	if os.date("%d", slot_14_3) ~= os.date("%d", slot_14_8) or os.date("%H", slot_14_3) ~= os.date("%H", slot_14_8) or os.date("%m", slot_14_3) ~= os.date("%m", slot_14_8) or os.date("%Y", slot_14_3) ~= os.date("%Y", slot_14_8) then
		local slot_14_9 = io.open(ove_0_24 .. "/RU_ERROR.log", "w")

		slot_14_9:write("ERROR_TIME_YDH")
		io.close(slot_14_9)
		chat.print("<font color='#FFFFFF00'>ERROR_TIME_YDH</font>")

		ove_0_6.core.is_auth = true
		ove_0_6.core.auth = false
		ove_0_6.core.auth_msg_cn = "ERROR_错误时间"
		ove_0_6.core.auth_msg = "ERROR_TIME_YDH"

		return
	end

	if ove_0_38(slot_14_3) then
		ove_0_6.load(slot_14_3 * 1000)
	end
end

function ove_0_6.load_start()
	-- function 15
	if common.debug then 
		local slot_15_0 = io.open(ove_0_24 .. "/COPY/SA_VER000", "r")

		if true then
			ove_0_6.core.is_auth = true
			ove_0_6.core.auth = true
			common.debug = true

			--io.close(slot_15_0)

			common.evade_auth = true

			--print("Development000111", game.time)

			return
		end
	end

	network.easy_get(function(arg_16_0, arg_16_1, arg_16_2)
		-- function 16
		ove_0_46(arg_16_0, arg_16_1)
	end, "http://api.m.taobao.com/rest/api3.do?api=mtop.common.getTimestamp")
end

local function ove_0_47(arg_17_0, arg_17_1)
	-- function 17
	--print("Get2")
	
	if true then
	return true 
	end

	if arg_17_0 ~= 200 then
		if ove_0_10 then
			return
		end

		local slot_17_0 = io.open(ove_0_24 .. "/RU_ERROR.log", "w")

		slot_17_0:write("ERROR_CONN_780")
		io.close(slot_17_0)
		chat.print("<font color='#FFFFFF00'>ERROR_CONN_780</font>")

		ove_0_6.core.is_auth = true
		ove_0_6.core.auth = false
		ove_0_6.core.auth_msg_cn = "ERROR_连接错误"
		ove_0_6.core.auth_msg = "ERROR_CONN_780"

		if common.debug then
			ove_0_6.load(os.time() * 1000)
		end

		return
	end

	local slot_17_1 = arg_17_1
	local slot_17_2 = ove_0_22.parse(slot_17_1)

	if slot_17_2.success ~= "1" then
		return
	end

	local slot_17_3 = slot_17_2.result.timestamp
	local slot_17_4 = ove_0_44(slot_17_3)
	local slot_17_5 = os.time()
	local slot_17_6 = 8
	local slot_17_7 = 8 - slot_17_6
	local slot_17_8 = os.date("*t").isdst
	local slot_17_9 = os.time(serverTimeTable) + slot_17_7 * 3600 - (slot_17_8 and 1 or 0) * 3600

	if os.date("%d", slot_17_4) ~= os.date("%d", slot_17_9) or os.date("%H", slot_17_4) ~= os.date("%H", slot_17_9) or os.date("%m", slot_17_4) ~= os.date("%m", slot_17_9) or os.date("%Y", slot_17_4) ~= os.date("%Y", slot_17_9) then
		local slot_17_10 = io.open(ove_0_24 .. "/RU_ERROR.log", "w")

		slot_17_10:write("ERROR_TIME_YDH")
		io.close(slot_17_10)
		chat.print("<font color='#FFFFFF00'>ERROR_TIME_YDH</font>")

		ove_0_6.core.is_auth = true
		ove_0_6.core.auth = false
		ove_0_6.core.auth_msg_cn = "ERROR_错误时间"
		ove_0_6.core.auth_msg = "ERROR_TIME_YDH"

		return
	end

	if ove_0_38(slot_17_9) then
		ove_0_6.load(slot_17_4 * 1000)
	end
end

network.easy_get(function(arg_18_0, arg_18_1, arg_18_2)
	-- function 18
	ove_0_47(arg_18_0, arg_18_1)
end, "http://api.k780.com/?app=life.time&appkey=68254&sign=9c9a39297cb7d8a88685c696580173f1&format=json")

local function ove_0_48(arg_19_0, arg_19_1)
	-- function 19
	if arg_19_0 ~= 200 then
		local slot_19_0 = io.open(ove_0_24 .. "/RU_ERROR.log", "w")

		slot_19_0:write("ERROR_CONN")
		io.close(slot_19_0)
		chat.print("<font color='#FFFFFF00'>ERROR_CONN</font>")

		ove_0_6.core.is_auth = true
		ove_0_6.core.auth = false
		ove_0_6.core.auth_msg_cn = "ERROR_连接错误"
		ove_0_6.core.auth_msg = "ERROR_CONN"

		return
	end

	local slot_19_1 = arg_19_1

	ove_0_6.Auth_msg = "Auth_2"

	local slot_19_2 = ove_0_22.parse(slot_19_1)

	ove_0_6.auth_core(slot_19_2)
end

function ove_0_6.load(arg_20_0)
	-- function 20
	if ove_0_10 then
		return
	end

	print("load!")

	ove_0_10 = true

	ove_0_6.UpdateIp()

	ove_0_6.vertime = arg_20_0

	if tonumber(arg_20_0) > ove_0_44() * 1000 + 86400000 then
		local slot_20_0 = io.open(ove_0_24 .. "/RU_ERROR.log", "w")

		slot_20_0:write("ERROR_vertime")
		io.close(slot_20_0)
		chat.print("<font color='#FFFFFF00'>ERROR_vertime</font>")

		ove_0_6.core.is_auth = true
		ove_0_6.core.auth = false
		ove_0_6.core.auth_msg_cn = "ERROR_错误时间"
		ove_0_6.core.auth_msg = "ERROR_vertime"

		return
	end

	local slot_20_1 = io.open(ove_0_24 .. "/SA.key", "r") or io.open(ove_0_24 .. "/SA.ini", "r") or io.open(ove_0_24 .. "/SA.ini.txt", "r")

	if slot_20_1 then
		local slot_20_2 = slot_20_1:read("*all")
		local slot_20_3 = string.gsub(slot_20_2, "^%s*(.-)%s*$", "%1")

		io.close(slot_20_1)

		if #slot_20_3 > 0 then
			ove_0_6.Kcode = slot_20_3
		end
	else
		chat.print("<font color='#FFFFFF00'>ERROR_input Key SA.ini Path:" .. tostring(ove_0_24) .. "</font>")

		ove_0_6.core.is_auth = true
		ove_0_6.core.auth = false
		ove_0_6.core.auth_msg_cn = "ERROR_请输入卡密"
		ove_0_6.core.auth_msg = "ERROR_input key"

		return
	end

	SA_USER = ove_0_6.Kcode and ove_0_6.Kcode ~= "mir_test_user" or false

	local slot_20_4 = game.version:gsub("%s+", ""):gsub("%[", ""):gsub("%]", "")

	ove_0_6.ptr = player.networkID .. player.ptr
	ove_0_6.gameversion = slot_20_4
	ove_0_6.mapID = game.mapID
	ove_0_6.mode = game.mode .. "_" .. game.mapID
	ove_0_6.gameid = game.mapID
	ove_0_6.gametype = game.type

	local slot_20_5 = ove_0_6.vertime .. "/" .. tostring(ove_0_6.ptr) .. player.charName .. "CjNbWd"
	local slot_20_6 = ove_0_21.sumhexaLo(slot_20_5)

	ove_0_6.md5_hex = slot_20_6
	ove_0_6.hwid = ove_0_39
	ove_0_6.user = ove_0_39 .. "_USER"
	ove_0_6.Hex = ove_0_43(10)
	ove_0_6.hexstrNumber = ove_0_42(10)
	ove_0_6.auth_time = ove_0_44()
	ove_0_6.MD5_VALUE = ove_0_6.hwid .. ove_0_6.user .. ove_0_6.Hex .. ove_0_6.auth_time .. "Key/future223300PPoohjhjf0041540"
	ove_0_6.Auth_URL = ove_0_15 .. "/ver/" .. ove_0_6.Kcode .. "/" .. ove_0_6.hwid .. "/" .. ove_0_6.user .. "/" .. ove_0_6.Hex .. "/" .. ove_0_6.auth_time .. "/" .. ove_0_21.sumhexaLo(ove_0_6.MD5_VALUE) .. "/" .. player.charName .. "/" .. hanbot.language .. "/" .. ove_0_6.hexstrNumber .. "/" .. ove_0_6.vertime .. "/" .. tostring(ove_0_6.ptr) .. "/" .. ove_0_6.md5_hex .. "/" .. ove_0_6.gameversion .. "/" .. ove_0_6.mapID .. "/" .. ove_0_6.mode .. "/" .. tostring(network.latency) .. "/" .. tostring(ove_0_6.version) .. "/" .. tostring("AIO")
	ove_0_6.Auth_msg = "Auth_1"

	network.easy_post(function(arg_21_0, arg_21_1, arg_21_2)
		-- function 21
		ove_0_48(arg_21_0, arg_21_1)
	end, ove_0_6.Auth_URL)
end

function ove_0_6.auth_core(arg_22_0)
	-- function 22
	ove_0_6.Auth_msg = "Auth_2"
	ove_0_6.endtime1 = arg_22_0.auth_end_time
	ove_0_6.endtime = arg_22_0.auth_end
	ove_0_6.fKcode = ove_0_6.Kcode
	ove_0_6.Kcode = arg_22_0.auth_key
	ove_0_6.six = arg_22_0.auth_data

	local slot_22_0 = arg_22_0.auth_data

	ove_0_6.code = arg_22_0.code

	local slot_22_1 = {}
	local slot_22_2 = {}
	local slot_22_3 = {}

	if #ove_0_6.fKcode <= 5 then
		local slot_22_4 = io.open(ove_0_24 .. "/RU_ERROR.log", "w")

		chat.print("<font color='#FFFFFF00'>ERROR_CORE<#5</font>")
		slot_22_4:write("ERROR_CORE<#5")
		io.close(slot_22_4)

		ove_0_6.core.is_auth = true
		ove_0_6.core.auth = false
		ove_0_6.core.auth_msg_cn = "ERROR_CORE<#5"
		ove_0_6.core.auth_msg = "ERROR_CORE<#5"

		return
	end

	local slot_22_5 = arg_22_0.auth_code
	local slot_22_6 = arg_22_0.auth_time
	local slot_22_7 = arg_22_0.auth_name
	local slot_22_8 = arg_22_0.auth_send_time
	local slot_22_9 = arg_22_0.hero
	local slot_22_10 = arg_22_0.auth_send_data
	local slot_22_11 = arg_22_0.auth_end_time

	if slot_22_11 and slot_22_11 < ove_0_44() * 1000 then
		local slot_22_12 = io.open(ove_0_24 .. "/RU_ERROR.log", "w")

		chat.print("<font color='#FFFFFF00'>ERROR_AUTH_END_TIME</font>")
		slot_22_12:write("ERROR_AUTH_END_TIME")
		io.close(slot_22_12)

		ove_0_6.core.is_auth = true
		ove_0_6.core.auth = false
		ove_0_6.core.auth_msg_cn = "ERROR_AUTH_END_TIME"
		ove_0_6.core.auth_msg = "ERROR_AUTH_END_TIME"

		--print(os.date("%Y-%m-%d %H:%M:%S", ove_0_44()), os.date("%Y-%m-%d %H:%M:%S", ove_0_45()))

		return
	end

	local slot_22_13 = arg_22_0.msg
	local slot_22_14 = arg_22_0.msg3
	local slot_22_15 = tostring(data)
	local slot_22_16 = arg_22_0.time

	if ove_0_6.code == 0 and slot_22_8 and ove_0_6.auth_time then
		local slot_22_17 = ove_0_23.xordecode(slot_22_8, ove_0_6.ptr)

		if tostring(slot_22_17) ~= tostring(ove_0_6.auth_time) or ove_0_6.Hex ~= slot_22_10 then
			local slot_22_18 = io.open(ove_0_24 .. "/RU_ERROR.log", "w")

			chat.print("<font color='#FFFFFF00'>ERROR_AUTH_SEND_TIME</font>")
			slot_22_18:write("ERROR_AUTH_SEND_TIME")
			io.close(slot_22_18)

			ove_0_6.core.is_auth = true
			ove_0_6.core.auth = false
			ove_0_6.core.auth_msg_cn = "ERROR_AUTH_SEND_TIME"
			ove_0_6.core.auth_msg = "ERROR_AUTH_SEND_TIME"

			return
		end

		for iter_22_0 = 1, string.len(slot_22_0) do
			local slot_22_19 = string.sub(ove_0_28, string.sub(slot_22_0, iter_22_0, iter_22_0), string.sub(slot_22_0, iter_22_0, iter_22_0))
			local slot_22_20 = ove_0_21.sumhexaLo(slot_22_19 .. ove_0_31)

			if ove_0_40(slot_22_1, slot_22_20) then
				slot_22_1[iter_22_0] = slot_22_20
			else
				local slot_22_21 = string.sub(ove_0_26, string.sub(slot_22_0, iter_22_0, iter_22_0), string.sub(slot_22_0, iter_22_0, iter_22_0))

				slot_22_1[iter_22_0] = ove_0_21.sumhexaLo(slot_22_21)
			end

			local slot_22_22 = string.sub(ove_0_27, string.sub(slot_22_0, iter_22_0, iter_22_0), string.sub(slot_22_0, iter_22_0, iter_22_0))
			local slot_22_23 = ove_0_21.sumhexaLo(slot_22_22)

			if ove_0_40(slot_22_2, slot_22_23) then
				slot_22_2[iter_22_0] = slot_22_23
			else
				local slot_22_24 = string.sub(ove_0_25, string.sub(slot_22_0, iter_22_0, iter_22_0), string.sub(slot_22_0, iter_22_0, iter_22_0))

				slot_22_2[iter_22_0] = ove_0_21.sumhexaLo(slot_22_24)
			end
		end

		for iter_22_1 = 1, string.len(ove_0_6.hexstrNumber) do
			local slot_22_25 = string.sub(ove_0_28, string.sub(ove_0_6.hexstrNumber, iter_22_1, iter_22_1), string.sub(ove_0_6.hexstrNumber, iter_22_1, iter_22_1))
			local slot_22_26 = ove_0_21.sumhexaLo(slot_22_25)

			if ove_0_40(slot_22_3, slot_22_26) then
				slot_22_3[iter_22_1] = slot_22_26
			else
				local slot_22_27 = string.sub(ove_0_26, string.sub(ove_0_6.hexstrNumber, iter_22_1, iter_22_1), string.sub(ove_0_6.hexstrNumber, iter_22_1, iter_22_1))

				slot_22_3[iter_22_1] = ove_0_21.sumhexaLo(slot_22_27)
			end
		end

		local slot_22_28 = ""

		for iter_22_2 = 1, #slot_22_3 do
			local slot_22_29 = slot_22_3[iter_22_2]

			slot_22_28 = slot_22_28 .. slot_22_29
		end

		local slot_22_30 = ove_0_21.sumhexaLo(slot_22_28)
		local slot_22_31 = slot_22_1[1] .. slot_22_2[1] .. slot_22_1[2] .. slot_22_2[2] .. slot_22_1[3] .. slot_22_2[3] .. slot_22_1[4] .. slot_22_2[4] .. slot_22_1[5] .. slot_22_2[5] .. slot_22_1[6] .. slot_22_2[6]
		local slot_22_32 = ove_0_21.sumhexaLo(slot_22_31 .. ove_0_30 .. slot_22_30)
		local slot_22_33 = io.open(hanbot.path .. "/saves/" .. hanbot.hwid2 .. ".ini", "r")

		if file then
			local slot_22_34 = slot_22_33:read("*all")
			local slot_22_35 = string.gsub(slot_22_34, "^%s*(.-)%s*$", "%1")
			local slot_22_36 = io.open(hanbot.path .. "/saves/" .. hanbot.hwid2 .. ".ini", "w")

			if slot_22_35:find(slot_22_32) then
				chat.print("<font color='#FFFFFF00'>ERROR_AUTH_DATA_OUT</font>")
				slot_22_36:write("ERROR_AUTH_DATA_OUT")
				io.close(slot_22_36)

				ove_0_6.core.is_auth = true
				ove_0_6.core.auth = false
				ove_0_6.core.auth_msg_cn = "ERROR_AUTH_DATA_OUT"
				ove_0_6.core.auth_msg = "ERROR_AUTH_DATA_OUT"

				return false
			end

			if #slot_22_35 > 1500 then
				slot_22_36:write(slot_22_32)
			else
				slot_22_36:write(slot_22_35 .. slot_22_32)
			end
		else
			local slot_22_37 = io.open(hanbot.path .. "/saves/" .. hanbot.hwid2 .. ".ini", "w")

			slot_22_37:write(slot_22_32)
			io.close(slot_22_37)
		end

		if ove_0_44() * 1000 > ove_0_6.endtime1 then
			local slot_22_38 = io.open(ove_0_24 .. "/RU_ERROR.log", "w")

			chat.print("<font color='#FFFFFF00'>ERROR_AUTH_TIME</font>")
			slot_22_38:write("ERROR_AUTH_TIME")
			io.close(slot_22_38)

			ove_0_6.core.is_auth = true
			ove_0_6.core.auth = false
			ove_0_6.core.auth_msg_cn = "ERROR_AUTH_TIME"
			ove_0_6.core.auth_msg = "ERROR_AUTH_TIME"

			return false
		end

		local slot_22_39 = ove_0_6.hwid .. ove_0_6.user .. ove_0_6.Hex .. ove_0_6.auth_time .. ove_0_21.sumhexaLo(ove_0_25) .. ove_0_21.sumhexaLo(ove_0_26) .. ove_0_21.sumhexaLo(ove_0_27) .. ove_0_21.sumhexaLo(ove_0_28)
		local slot_22_40 = ove_0_21.sumhexaLo(slot_22_39 .. slot_22_32)
		local slot_22_41 = ove_0_21.sumhexaLo("KKlojhukjfdjkhgfdjkhgjkhdf" .. slot_22_6 .. ove_0_6.auth_time .. slot_22_11 .. "hjhjgjhfdjhjh@hjhjgfds112" .. slot_22_0 .. "hjjhkhjgfdhj" .. tostring(ove_0_6.ptr))

		if player.charName == "Kalista" and arg_22_0.Kalista then
			slot_22_40 = ove_0_21.sumhexaLo(slot_22_40 .. ove_0_32)
			ove_0_6.IsKalista_VIP = true
		elseif player.charName == "Sion" and arg_22_0.Sion then
			slot_22_40 = ove_0_21.sumhexaLo(slot_22_40 .. ove_0_34)
			ove_0_6.IsSion_VIP = true
		elseif player.charName == "LeeSin" and arg_22_0.LeeSin then
			slot_22_40 = ove_0_21.sumhexaLo(slot_22_40 .. ove_0_33)
			ove_0_6.IsLeeSin_VIP = true
		elseif player.charName == "Graves" and arg_22_0.Graves then
			slot_22_40 = ove_0_21.sumhexaLo(slot_22_40 .. ove_0_35)
			ove_0_6.IsGraves_VIP = true
		elseif player.charName == "Blitzcrank" and arg_22_0.Blitzcrank then
			slot_22_40 = ove_0_21.sumhexaLo(slot_22_40 .. salf_Blitzcrank)
			ove_0_6.IsBlitzcrank_VIP = true
		elseif player.charName == "Qiyana" and arg_22_0.Qiyana then
			slot_22_40 = ove_0_21.sumhexaLo(slot_22_40 .. ove_0_36)
			ove_0_6.IsQiyana_VIP = true
		elseif player.charName == "Thresh" and arg_22_0.Thresh then
			slot_22_40 = ove_0_21.sumhexaLo(slot_22_40 .. ove_0_37)
			ove_0_6.IsThresh_VIP = true
		end

		if arg_22_0.Evade then
			ove_0_6.SA_VER_SUCCESS = arg_22_0.Evade
		end

		local slot_22_42 = ove_0_23.xorencode(string.sub(slot_22_40, 0, 10), arg_22_0.auth_send_time2 .. ove_0_6.auth_time .. slot_22_6 .. slot_22_7 .. slot_22_0 .. ove_0_6.fKcode)

		ove_0_6.auto_data_code = slot_22_42 .. slot_22_40 .. slot_22_41

		local slot_22_43 = tostring(ove_0_6.vertime / 1000)
		local slot_22_44 = string.sub(slot_22_43, #slot_22_43 - 1, #slot_22_43) .. ove_0_6.ptr
		local slot_22_45 = ove_0_21.sumhexaLo(slot_22_42 .. slot_22_40 .. slot_22_41)
		local slot_22_46 = ove_0_23.xorencode(string.sub(slot_22_45, 0, 9), tostring(slot_22_44))

		if ove_0_21.sumhexaLo(slot_22_46 .. slot_22_44) == slot_22_5 then
			local slot_22_47 = arg_22_0.cloud
			local slot_22_48 = arg_22_0.data1
			local slot_22_49 = ove_0_6.md5_hex .. tostring(ove_0_6.ptr) .. tostring("ver2445") .. tostring(slot_22_47) .. ove_0_6.vertime .. "verp109" .. ove_0_6.gameversion

			if slot_22_48 == ove_0_21.sumhexaLo(slot_22_49) then
				ove_0_6.cloud = ove_0_22.parse(slot_22_47)

				local slot_22_50 = string.lower(ove_0_6.Kcode)
				local slot_22_51 = string.lower(ove_0_6.fKcode)

				if ove_0_6.Kcode:lower() ~= ove_0_6.fKcode:lower() or ove_0_6.endtime:find("2099") or ove_0_6.endtime:find("00:00:00") or slot_22_50:find("11111111") or slot_22_50:find("9520617") or slot_22_50:find("sale123") or slot_22_50:find("123456123") or slot_22_50:find("64d33a3278674220a0332963c7c4a6dd0") or slot_22_50:find("huangyi") or slot_22_50:find("kms") or slot_22_50:find("6606df06") or slot_22_50:find("ef312e2324db460f804318dd55626bbc0") or slot_22_50 ~= slot_22_51 then
					local slot_22_52 = io.open(ove_0_24 .. "/RU_ERROR.log", "w")

					chat.print("<font color='#FFFFFF00'>ERROR_FF_KEY</font>")
					slot_22_52:write("ERROR_FF_KEY")
					io.close(slot_22_52)

					ove_0_6.core.is_auth = true
					ove_0_6.core.auth = false
					ove_0_6.core.auth_msg_cn = "ERROR_FF_KEY"
					ove_0_6.core.auth_msg = "ERROR_FF_KEY"

					return
				end

				if CN then
					chat.print("<font color='#ffff84'>到期日期: " .. ove_0_6.endtime .. " </font>")
				else
					chat.print("<font color='#ffff84'>Expire Date " .. ove_0_6.endtime .. " </font>")
				end

				ove_0_6.core.is_auth = true
				ove_0_6.core.auth = true

				if ove_0_6.hwid == "46aae4ce0d3f877ceb6070bd0018943a_HWID" and common then
					common.debug = true

					--print("Development")
				end

				if arg_22_0.evade_auth_end and os.time() * 1000 < arg_22_0.evade_auth_end_time then
					common.evade_auth = true
				end

				return true
			else
				local slot_22_53 = io.open(ove_0_24 .. "/RU_ERROR.log", "w")

				chat.print("<font color='#FFFFFF00'>ERROR_AUTH_ERROR_DATA_CN</font>")
				slot_22_53:write("ERROR_AUTH_ERROR_DATA_CN")
				io.close(slot_22_53)

				ove_0_6.core.is_auth = true
				ove_0_6.core.auth = false
				ove_0_6.core.auth_msg_cn = "ERROR_AUTH_ERROR_DATA_CN"
				ove_0_6.core.auth_msg = "ERROR_AUTH_ERROR_DATA_CN"

				return
			end
		else
			local slot_22_54 = io.open(ove_0_24 .. "/RU_ERROR.log", "w")

			chat.print("<font color='#FFFFFF00'>ERROR_AUTH_ERROR_DATA</font>")
			slot_22_54:write("ERROR_AUTH_ERROR_DATA")
			io.close(slot_22_54)

			ove_0_6.core.is_auth = true
			ove_0_6.core.auth = false
			ove_0_6.core.auth_msg_cn = "ERROR_数据错误"
			ove_0_6.core.auth_msg = "ERROR_AUTH_ERROR_DATA"

			return
		end
	else
		local slot_22_55 = io.open(ove_0_24 .. "/RU_ERROR.log", "w")

		chat.print("<font color='#FFFFFF00'>ERROR_AUTH_CORE - " .. tostring(slot_22_13) .. "</font>")
		slot_22_55:write("ERROR_AUTH_CORE - " .. tostring(slot_22_13))
		io.close(slot_22_55)

		ove_0_6.core.is_auth = true
		ove_0_6.core.auth = false
		ove_0_6.core.auth_msg_cn = "ERROR_AUTH_CORE - " .. tostring(slot_22_13)
		ove_0_6.core.auth_msg = "ERROR_AUTH_CORE - " .. tostring(slot_22_13)

		return
	end
end

local function ove_0_49(arg_23_0, arg_23_1)
	-- function 23
	ove_0_6.check_time = game.time + 30
	ove_0_6.core.auth = false

	if arg_23_0 ~= 200 then
		local slot_23_0 = io.open(ove_0_24 .. "/RU_ERROR.log", "w")

		slot_23_0:write("AUTH2_ERROR_CONN2")
		io.close(slot_23_0)
		chat.print("AUTH2_ERROR_CONN2" .. tostring(arg_23_0))
		chat.print("<font color='#FFFFFF00'>AUTH2_ERROR_CONN2</font>")

		ove_0_6.core.is_auth = true
		ove_0_6.core.auth = false
		ove_0_6.core.auth_msg_cn = "AUTH2_ERROR_CONN2_连接错误2"
		ove_0_6.core.auth_msg = "AUTH2_ERROR_CONN2_CONN2"

		return
	end

	local slot_23_1 = arg_23_1

	ove_0_6.Auth_msg = "Auth_to_2"

	local slot_23_2 = ove_0_22.parse(slot_23_1)
	local slot_23_3 = slot_23_2.code
	local slot_23_4 = slot_23_2.msg

	if slot_23_3 ~= 0 then
		local slot_23_5 = io.open(ove_0_24 .. "/RU_ERROR.log", "w")

		slot_23_5:write("ERROR_AUTH_ERROR")
		io.close(slot_23_5)
		chat.print("<font color='#FFFFFF00'>ERROR_AUTH_ERROR</font>")

		ove_0_6.core.is_auth = true
		ove_0_6.core.auth = false
		ove_0_6.core.auth_msg_cn = "ERROR_多次验证错误"
		ove_0_6.core.auth_msg = "ERROR_AUTH_ERROR2"

		chat.print("autoERRR")

		return
	end

	local slot_23_6 = slot_23_2.data
	local slot_23_7 = ove_0_21.sumhexaLo(ove_0_6.auth_time .. "kkoold1@2345678009" .. ove_0_29 .. ove_0_6.ptr .. ove_0_6.hwid .. ove_0_6.user)
	local slot_23_8 = ove_0_6.six
	local slot_23_9 = slot_23_8 * 15
	local slot_23_10 = ove_0_6.auto_data_code .. slot_23_9 .. player.charName
	local slot_23_11 = ove_0_23.xorencode(ove_0_6.auth_time, ove_0_6.ptr .. ove_0_6.six .. ove_0_6.Kcode .. ove_0_6.auto_data_code) .. slot_23_7 .. ove_0_21.sumhexaLo(slot_23_10) .. ove_0_6.Kcode

	if slot_23_11 ~= slot_23_6 then
		local slot_23_12 = io.open(ove_0_24 .. "/RU_ERROR.log", "w")

		slot_23_12:write("ERROR_AUTH_ERROR")
		io.close(slot_23_12)
		chat.print("<font color='#FFFFFF00'>ERROR_AUTH_ERROR</font>")

		ove_0_6.core.is_auth = true
		ove_0_6.core.auth = false
		ove_0_6.core.auth_msg_cn = "ERROR_多次验证错误"
		ove_0_6.core.auth_msg = "ERROR_AUTH_ERROR2"

		return
	end

	local slot_23_13 = slot_23_2.data2
	local slot_23_14 = slot_23_2.six
	local slot_23_15 = ove_0_21.sumhexaLo(slot_23_8 .. slot_23_14) .. slot_23_11
	local slot_23_16 = ove_0_21.sumhexaLo(slot_23_15)

	if slot_23_16 ~= slot_23_13 then
		local slot_23_17 = io.open(ove_0_24 .. "/RU_ERROR.log", "w")

		slot_23_17:write("ERROR_AUTH_ERROR2")
		io.close(slot_23_17)
		chat.print("<font color='#FFFFFF00'>ERROR_AUTH_ERROR2</font>")

		ove_0_6.core.is_auth = true
		ove_0_6.core.auth = false
		ove_0_6.core.auth_msg_cn = "ERROR_多次验证错误2"
		ove_0_6.core.auth_msg = "ERROR_AUTH_ERROR_2"

		return
	end

	ove_0_6.core.is_auth = true
	ove_0_6.core.auth = true
	ove_0_6.six = slot_23_14
	ove_0_6.auto_data_code = ove_0_21.sumhexaLo(slot_23_16 .. slot_23_8)
end

function ove_0_6.auth_to()
	-- function 24

end

return ove_0_6


local slot10_a1320 = module.load(header.id, "lvxbot/main")
local slot11_a1323 = slot10_a1320.load("menu")

local slot13_a1327 = {
	pred = function ()
		if slot11_a1323.pred:get() then
			return 1
		else
			return 2
		end
	end
}
local slot13_a1337 = {
	type = "pos",
	slot = _R,
	arg1 = function (arg0_a1339)
		local slot1_a1341 = arg0_a1339.ts_result.seg
		local slot2_a1343 = arg0_a1339.ts_result.obj

		return slot1_a1341.endPos:to3D(slot2_a1343.y)
	end
}
local slot12_a1324 = {
	ignore_obj_radius = 3000,
	prediction = {
		delay = 1.05,
		range = 3000,
		PredZs = 0,
		type = "Linear",
		movseep = 1000,
		boundingRadiusMod = 1,
		speed = 2000,
		mov = 0.33,
		movtime = 0.35,
		width = 140
	},
	target_selector = {
		type = "LESS_CAST_AP"
	},
	cast = slot13_a1327,
	cast_spell = slot13_a1337,
	slot = _R
}
return slot10_a1320.expert.create(slot12_a1324)

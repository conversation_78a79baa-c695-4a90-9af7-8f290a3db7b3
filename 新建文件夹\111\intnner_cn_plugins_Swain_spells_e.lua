

local ove_0_10 = module.internal("orb")
local ove_0_11 = module.internal("TS")
local ove_0_12 = module.internal("pred")
local ove_0_13 = module.load(header.id, "common/common")
local ove_0_14 = module.load(header.id, "common/Compute/computer")
local ove_0_15 = module.load(header.id, "plugins/Swain/menu")
local ove_0_16 = module.load(header.id, "plugins/Swain/damage")
local ove_0_17 = module.load(header.id, "plugins/Swain/helper")
local ove_0_18 = {
	range = 935,
	last = 0,
	slot = player:spellSlot(_E),
	result = {},
	pred_input = {
		speed = 935,
		range = 935,
		delay = 0.25,
		boundingRadiusMod = 0,
		width = 85
	},
	pred_input_e2 = {
		speed = 600,
		range = 950,
		delay = 0.050000000745058,
		boundingRadiusMod = 0,
		width = 85,
		collision = {
			minion = true,
			hero = true,
			wall = true
		}
	},
	pred_input_present = {
		radius = 935,
		dashRadius = 0,
		boundingRadiusModTarget = 1,
		delay = 0.25,
		boundingRadiusModSource = 0
	},
	CrowdControls = {
		[BUFF_CHARM] = true,
		[BUFF_FEAR] = true,
		[BUFF_FLEE] = true,
		[BUFF_KNOCKUP] = true,
		[BUFF_SNARE] = true,
		[BUFF_STUN] = true,
		[BUFF_SUPPRESSION] = true,
		[BUFF_TAUNT] = true,
		[BUFF_KNOCKBACK] = true
	}
}

function ove_0_18.is_ready()
	if ove_0_18.slot.name == "SwainE" then
		return ove_0_18.slot.state == 0
	end
end

function ove_0_18.get_action_state()
	if ove_0_18.is_ready() then
		return ove_0_18.get_prediction()
	end
end

function ove_0_18.invoke_action()
	player:castSpell("pos", _E, vec2(ove_0_18.result.pos.x, ove_0_18.result.pos.y):to3D())
end

function ove_0_18.invoke_e_killsteal()
	local slot_8_0 = ove_0_11.get_result(function(arg_9_0, arg_9_1, arg_9_2)
		if arg_9_2 > 1500 then
			return
		end

		if not ove_0_13.isValidTarget(arg_9_1) then
			return
		end

		if not ove_0_12.present.get_prediction(ove_0_18.pred_input_present, player, arg_9_1.path.serverPos2D) then
			return
		end

		local slot_9_0 = ove_0_12.linear.get_prediction(ove_0_18.pred_input, arg_9_1, player.path.serverPos2D)

		if not slot_9_0 then
			return
		end

		local slot_9_1 = ove_0_18._isLineEndPos(slot_9_0.endPos:to3D())

		if not slot_9_1 then
			return
		end

		local slot_9_2 = ove_0_12.linear.get_prediction(ove_0_18.pred_input_e2, arg_9_1, slot_9_1:to2D())

		if not slot_9_2 then
			return
		end

		if slot_9_0 and slot_9_0.startPos:distSqr(slot_9_0.endPos) < 864900 and ove_0_16.get_e_damage(arg_9_1) >= ove_0_13.GetShieldedHealth("AP", arg_9_1) and (not ove_0_12.collision.get_prediction(ove_0_18.pred_input_e2, {
			startPos = slot_9_2.endPos,
			endPos = slot_9_1:to2D()
		}, arg_9_1) or #ove_0_18.some_things(slot_9_2, arg_9_1, slot_9_1) < 4) then
			arg_9_0.obj = arg_9_1
			arg_9_0.seg = slot_9_0

			return true
		end
	end)

	if slot_8_0.obj and ove_0_13.isValidTarget(slot_8_0.obj) and ove_0_13.IsEnemyMortal(slot_8_0.obj) then
		player:castSpell("pos", _E, vec2(slot_8_0.seg.endPos.x, slot_8_0.seg.endPos.y):to3D())

		return true
	end
end

function ove_0_18.invoke_interrupt_spells()
	if not ove_0_17.interrupt_data.owner then
		return false
	end

	if player.pos:dist(ove_0_17.interrupt_data.owner.pos) > ove_0_18.range + ove_0_17.interrupt_data.owner.boundingRadius then
		return false
	end

	if game.time - ove_0_17.interrupt_data.channel >= ove_0_17.interrupt_data.start then
		ove_0_17.interrupt_data.owner = false

		return false
	end

	if not ove_0_13.isValidTarget(ove_0_17.interrupt_data.owner) then
		return
	end

	if not ove_0_12.present.get_prediction(ove_0_18.pred_input_present, player, ove_0_17.interrupt_data.owner.path.serverPos2D) then
		return
	end

	local slot_10_0 = ove_0_12.linear.get_prediction(ove_0_18.pred_input, ove_0_17.interrupt_data.owner, player.path.serverPos2D)

	if not slot_10_0 then
		return
	end

	local slot_10_1 = ove_0_18._isLineEndPos(slot_10_0.endPos:to3D())

	if not slot_10_1 then
		return
	end

	local slot_10_2 = ove_0_12.linear.get_prediction(ove_0_18.pred_input_e2, ove_0_17.interrupt_data.owner, slot_10_1:to2D())

	if not slot_10_2 then
		return
	end

	if game.time >= ove_0_17.interrupt_data.start and slot_10_0 and slot_10_0.startPos:distSqr(slot_10_0.endPos) < 864900 and (not ove_0_12.collision.get_prediction(ove_0_18.pred_input_e2, {
		startPos = slot_10_2.endPos,
		endPos = slot_10_1:to2D()
	}, ove_0_17.interrupt_data.owner) or #ove_0_18.some_things(slot_10_2, ove_0_17.interrupt_data.owner, slot_10_1) < 4) then
		player:castSpell("pos", _E, slot_10_0.endPos:to3D())

		return true
	end
end

function ove_0_18._isLineEndPos(arg_11_0)
	local slot_11_0

	if ove_0_13.GetDistance(player.path.serverPos, arg_11_0) < ove_0_13.GetAARange() and ove_0_13.GetDistance(player.path.serverPos, arg_11_0) > 350 then
		slot_11_0 = vec3(player.path.serverPos) + (vec3(arg_11_0) - vec3(player.path.serverPos)):norm() * (arg_11_0:dist(player.path.serverPos) + 350)
	end

	if ove_0_13.GetDistance(player.path.serverPos, arg_11_0) >= ove_0_13.GetAARange() and ove_0_13.GetDistance(player.path.serverPos, arg_11_0) < ove_0_18.range then
		slot_11_0 = player.path.serverPos + (arg_11_0 - player.path.serverPos):norm() * (arg_11_0:dist(player.path.serverPos) + 290)
	end

	if ove_0_13.GetDistance(player.path.serverPos, arg_11_0) <= 350 then
		slot_11_0 = player.path.serverPos + (arg_11_0 - player.path.serverPos):norm() * (arg_11_0:dist(player.path.serverPos) + 750)
	end

	return slot_11_0
end

function ove_0_18.Floor(arg_12_0)
	return math.floor(arg_12_0 * 100) * 0.01
end

function ove_0_18.some_things(arg_13_0, arg_13_1, arg_13_2)
	local slot_13_0 = {}

	if not arg_13_1 then
		return
	end

	local slot_13_1 = ove_0_12.collision.get_prediction(ove_0_18.pred_input_e2, {
		startPos = arg_13_0.endPos,
		endPos = arg_13_2:to2D()
	}, arg_13_1)

	if not slot_13_1 then
		-- block empty
	else
		for iter_13_0 = 1, #slot_13_1 do
			local slot_13_2 = slot_13_1[iter_13_0]

			if slot_13_2 and slot_13_2.type and (slot_13_2.type == TYPE_MINION or slot_13_2.type == TYPE_HERO) and (slot_13_2.pos:dist(arg_13_0.endPos:to3D()) < 40 + slot_13_2.boundingRadius / 2 or ove_0_16.get_e_damage(slot_13_2) >= slot_13_2.health) then
				slot_13_0[#slot_13_0 + 1] = slot_13_2
			end
		end
	end

	return slot_13_0
end

function ove_0_18.get_stun_duration(arg_14_0)
	if not arg_14_0 then
		return
	end

	for iter_14_0, iter_14_1 in pairs(arg_14_0.buff) do
		if iter_14_1 and iter_14_1.valid and game.time < iter_14_1.endTime and ove_0_18.CrowdControls[iter_14_1.type] then
			return math.max(0, iter_14_1.startTime, iter_14_1.endTime) - game.time
		end
	end

	return 0
end

function ove_0_18.invoke_auto_cced()
	local slot_15_0 = ove_0_11.get_result(function(arg_16_0, arg_16_1, arg_16_2)
		if arg_16_1.isDead or arg_16_2 > ove_0_18.range then
			return
		end

		if not ove_0_13.isValidTarget(arg_16_1) then
			return
		end

		if not ove_0_12.present.get_prediction(ove_0_18.pred_input_present, player, arg_16_1.path.serverPos2D) then
			return
		end

		local slot_16_0 = ove_0_12.linear.get_prediction(ove_0_18.pred_input, arg_16_1, player.path.serverPos2D)

		if not slot_16_0 then
			return
		end

		local slot_16_1 = ove_0_18._isLineEndPos(slot_16_0.endPos:to3D())

		if not slot_16_1 then
			return
		end

		local slot_16_2 = ove_0_12.linear.get_prediction(ove_0_18.pred_input_e2, arg_16_1, slot_16_1:to2D())

		if not slot_16_2 then
			return
		end

		if slot_16_0 and slot_16_0.startPos:distSqr(slot_16_0.endPos) < 864900 and ove_0_16.get_e_damage(arg_16_1) >= ove_0_13.GetShieldedHealth("AP", arg_16_1) and not ove_0_12.collision.get_prediction(ove_0_18.pred_input_e2, {
			startPos = slot_16_2.endPos,
			endPos = slot_16_1:to2D()
		}, arg_16_1) and ove_0_18.get_stun_duration(arg_16_1) ~= 0 and ove_0_18.get_stun_duration(arg_16_1) > 0.25 - network.latency then
			arg_16_0.obj = arg_16_1
			arg_16_0.seg = arg_16_1.path.serverPos

			return true
		end
	end)

	if slot_15_0.obj and slot_15_0.seg then
		player:castSpell("pos", _E, slot_15_0.seg)

		return true
	end
end

function ove_0_18.trace_filter()
	local slot_17_0 = ove_0_18.pred_input.delay + network.latency

	if player.pos:to2D():dist(ove_0_18.result.pos) + slot_17_0 * (ove_0_18.result.obj.moveSpeed * 0.333) + ove_0_18.result.obj.boundingRadius > 935 then
		return false
	end

	local slot_17_1 = (ove_0_18.result.pos - player.pos:to2D()):len() / ove_0_18.pred_input.speed
	local slot_17_2 = ove_0_18.Floor(slot_17_1) - ove_0_18.Floor(ove_0_18.result.time)

	if not ove_0_18.result.obj.path.isActive and ove_0_18.range < player.pos:to2D():dist(ove_0_18.result.obj.pos2D) + ove_0_18.result.obj.moveSpeed * 0.333 then
		return false
	end

	if ove_0_18.range + ove_0_18.result.obj.boundingRadius < ove_0_18.result.seg.startPos:dist(ove_0_18.result.obj.pos2D) then
		return false
	end

	if ove_0_18.result.seg.startPos:distSqr(ove_0_18.result.obj.path.serverPos2D) > (ove_0_18.range + ove_0_18.result.obj.boundingRadius)^2 then
		return false
	end

	if ove_0_18.result.seg.startPos:distSqr(ove_0_18.result.obj.path.serverPos2D) < ove_0_18.range^2 then
		local slot_17_3 = ove_0_18.result.obj.moveSpeed / ove_0_18.pred_input.speed - network.latency

		if ove_0_12.trace.newpath(ove_0_18.result.obj, 0.033, slot_17_0 + slot_17_3) and ove_0_18.result.seg.startPos:distSqr(ove_0_18.result.obj.pos2D) < ove_0_18.range^2 then
			return true
		end
	end

	if ove_0_12.trace.linear.hardlock(ove_0_18.pred_input, ove_0_18.result.seg, ove_0_18.result.obj) then
		return true
	end

	if ove_0_12.trace.linear.hardlockmove(ove_0_18.pred_input, ove_0_18.result.seg, ove_0_18.result.obj) then
		return true
	end
end

function ove_0_18.get_prediction()
	if ove_0_18.last == game.time then
		return ove_0_18.result.obj
	end

	ove_0_18.last = game.time
	ove_0_18.result.obj = nil
	ove_0_18.result.seg = nil
	ove_0_18.result.pos = nil
	ove_0_18.result = ove_0_11.get_result(function(arg_19_0, arg_19_1, arg_19_2)
		if arg_19_2 > 1500 then
			return
		end

		if not ove_0_13.isValidTarget(arg_19_1) then
			return
		end

		if not ove_0_12.present.get_prediction(ove_0_18.pred_input_present, player, arg_19_1.path.serverPos2D) then
			return
		end

		local slot_19_0 = ove_0_12.linear.get_prediction(ove_0_18.pred_input, arg_19_1, player.path.serverPos2D)

		if not slot_19_0 then
			return
		end

		local slot_19_1 = ove_0_18._isLineEndPos(slot_19_0.endPos:to3D())

		if not slot_19_1 then
			return
		end

		local slot_19_2 = ove_0_12.linear.get_prediction(ove_0_18.pred_input_e2, arg_19_1, slot_19_1:to2D())

		if not slot_19_2 then
			return
		end

		local slot_19_3 = ove_0_14.Compute(ove_0_18.pred_input, slot_19_0, arg_19_1)

		if not slot_19_3 or slot_19_3 == nil or slot_19_3 <= 0 then
			return false
		end

		if slot_19_3 < 0 then
			return false
		end

		if slot_19_0 and slot_19_0.startPos:distSqr(slot_19_0.endPos) < 864900 and (not ove_0_12.collision.get_prediction(ove_0_18.pred_input_e2, {
			startPos = slot_19_2.endPos,
			endPos = slot_19_1:to2D()
		}, arg_19_1) or #ove_0_18.some_things(slot_19_2, arg_19_1, slot_19_1) < 3) then
			arg_19_0.obj = arg_19_1
			arg_19_0.seg = slot_19_0
			arg_19_0.pos = (ove_0_12.core.get_pos_after_time(arg_19_1, slot_19_3) + slot_19_0.endPos) / 2
			arg_19_0.time = slot_19_3

			return true
		end
	end)

	if ove_0_18.result.seg and ove_0_18.result.pos then
		if ove_0_15.combo.e.slow_pred:get() and ove_0_18.trace_filter() then
			return ove_0_18.result
		end

		if not ove_0_15.combo.e.slow_pred:get() then
			return ove_0_18.result
		end
	end
end

function ove_0_18.on_draw()
	if ove_0_15.draws.usee:get() and ove_0_18.slot.level > 0 then
		graphics.draw_circle(player.pos, ove_0_18.range, 1, ove_0_15.draws.e:get(), 100)
	end
end

return ove_0_18

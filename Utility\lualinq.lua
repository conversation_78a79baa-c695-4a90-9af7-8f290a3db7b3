LOG_LEVEL = 1
LOG_PREFIX = "LuaLinq: "
LIB_VERSION_TEXT = "1.5.2"
LIB_VERSION = 152

function setLogLevel(arg_1_0)
	LOG_LEVEL = arg_1_0
end

function _log(arg_2_0, arg_2_1, arg_2_2)
	if arg_2_0 <= LOG_LEVEL then
		print(arg_2_1 .. LOG_PREFIX .. arg_2_2)
	end
end

function logq(arg_3_0, arg_3_1)
	if LOG_LEVEL >= 3 then
		logv("after " .. arg_3_1 .. " => " .. #arg_3_0.m_Data .. " items : " .. _dumpData(arg_3_0))
	end
end

function _dumpData(arg_4_0)
	local var_4_0 = #arg_4_0.m_Data
	local var_4_1 = "q{ "

	for iter_4_0 = 1, 3 do
		if iter_4_0 <= var_4_0 then
			if iter_4_0 ~= 1 then
				var_4_1 = var_4_1 .. ", "
			end

			var_4_1 = var_4_1 .. tostring(arg_4_0.m_Data[iter_4_0])
		end
	end

	if var_4_0 > 3 then
		var_4_1 = var_4_1 .. ", ..." .. var_4_0 .. " }"
	else
		var_4_1 = var_4_1 .. " }"
	end

	return var_4_1
end

function logv(arg_5_0)
	_log(3, "[..] ", arg_5_0)
end

function logi(arg_6_0)
	_log(2, "[ii] ", arg_6_0)
end

function logw(arg_7_0)
	_log(1, "[W?] ", arg_7_0)
end

function loge(arg_8_0)
	_log(0, "[E!] ", arg_8_0)
end

function _new_lualinq(arg_9_0, arg_9_1)
	local var_9_0 = {}

	var_9_0.classid_71cd970f_a742_4316_938d_1998df001335 = 2
	var_9_0.m_Data = arg_9_1
	var_9_0.concat = _concat
	var_9_0.select = _select
	var_9_0.selectMany = _selectMany
	var_9_0.where = _where
	var_9_0.whereIndex = _whereIndex
	var_9_0.take = _take
	var_9_0.skip = _skip
	var_9_0.zip = _zip
	var_9_0.distinct = _distinct
	var_9_0.union = _union
	var_9_0.except = _except
	var_9_0.intersection = _intersection
	var_9_0.exceptby = _exceptby
	var_9_0.intersectionby = _intersectionby
	var_9_0.exceptBy = _exceptby
	var_9_0.intersectionBy = _intersectionby
	var_9_0.first = _first
	var_9_0.last = _last
	var_9_0.min = _min
	var_9_0.max = _max
	var_9_0.random = _random
	var_9_0.any = _any
	var_9_0.all = _all
	var_9_0.contains = _contains
	var_9_0.count = _count
	var_9_0.sum = _sum
	var_9_0.average = _average
	var_9_0.dump = _dump
	var_9_0.map = _map
	var_9_0.foreach = _foreach
	var_9_0.xmap = _xmap
	var_9_0.toArray = _toArray
	var_9_0.toDictionary = _toDictionary
	var_9_0.toIterator = _toIterator
	var_9_0.toTuple = _toTuple
	var_9_0.each = _foreach
	var_9_0.intersect = _intersection
	var_9_0.intersectby = _intersectionby
	var_9_0.intersectBy = _intersectionby

	logq(var_9_0, "from")

	return var_9_0
end

function from(arg_10_0)
	if arg_10_0 == nil then
		return fromNothing()
	elseif type(arg_10_0) == "function" then
		return fromIterator(arg_10_0)
	elseif type(arg_10_0) == "table" then
		if arg_10_0.classid_71cd970f_a742_4316_938d_1998df001335 ~= nil then
			return arg_10_0
		elseif arg_10_0[1] == nil then
			return fromDictionary(arg_10_0)
		elseif type(arg_10_0[1]) == "function" then
			return fromIteratorsArray(arg_10_0)
		else
			return fromArrayInstance(arg_10_0)
		end
	end

	return fromNothing()
end

function fromArrayInstance(arg_11_0)
	return _new_lualinq("fromArrayInstance", arg_11_0)
end

function fromArray(arg_12_0)
	local var_12_0 = {}

	for iter_12_0, iter_12_1 in ipairs(arg_12_0) do
		table.insert(var_12_0, iter_12_1)
	end

	return _new_lualinq("fromArray", var_12_0)
end

function fromDictionary(arg_13_0)
	local var_13_0 = {}

	for iter_13_0, iter_13_1 in pairs(arg_13_0) do
		local var_13_1 = {
			key = iter_13_0,
			value = iter_13_1
		}

		table.insert(var_13_0, var_13_1)
	end

	return _new_lualinq("fromDictionary", var_13_0)
end

function fromIterator(arg_14_0)
	local var_14_0 = {}

	for iter_14_0 in arg_14_0 do
		table.insert(var_14_0, iter_14_0)
	end

	return _new_lualinq("fromIterator", var_14_0)
end

function fromIteratorsArray(arg_15_0)
	local var_15_0 = {}

	for iter_15_0, iter_15_1 in ipairs(arg_15_0) do
		for iter_15_2 in iter_15_1 do
			table.insert(var_15_0, iter_15_2)
		end
	end

	return _new_lualinq("fromIteratorsArray", var_15_0)
end

function fromSet(arg_16_0)
	local var_16_0 = {}

	for iter_16_0, iter_16_1 in pairs(arg_16_0) do
		table.insert(var_16_0, iter_16_0)
	end

	return _new_lualinq("fromIteratorsArray", var_16_0)
end

function fromNothing()
	return _new_lualinq("fromNothing", {})
end

function _concat(arg_18_0, arg_18_1)
	local var_18_0 = {}

	for iter_18_0, iter_18_1 in ipairs(arg_18_0.m_Data) do
		table.insert(var_18_0, iter_18_1)
	end

	for iter_18_2, iter_18_3 in ipairs(arg_18_1.m_Data) do
		table.insert(var_18_0, iter_18_3)
	end

	return _new_lualinq(":concat", var_18_0)
end

function _select(arg_19_0, arg_19_1)
	local var_19_0 = {}

	if type(arg_19_1) == "function" then
		for iter_19_0, iter_19_1 in ipairs(arg_19_0.m_Data) do
			local var_19_1 = arg_19_1(iter_19_1)

			if var_19_1 ~= nil then
				table.insert(var_19_0, var_19_1)
			end
		end
	elseif type(arg_19_1) == "string" then
		for iter_19_2, iter_19_3 in ipairs(arg_19_0.m_Data) do
			local var_19_2 = iter_19_3[arg_19_1]

			if var_19_2 ~= nil then
				table.insert(var_19_0, var_19_2)
			end
		end
	else
		loge("select called with unknown predicate type")
	end

	return _new_lualinq(":select", var_19_0)
end

function _selectMany(arg_20_0, arg_20_1)
	local var_20_0 = {}

	for iter_20_0, iter_20_1 in ipairs(arg_20_0.m_Data) do
		local var_20_1 = arg_20_1(iter_20_1)

		if var_20_1 ~= nil then
			for iter_20_2, iter_20_3 in ipairs(var_20_1) do
				if iter_20_3 ~= nil then
					table.insert(var_20_0, iter_20_3)
				end
			end
		end
	end

	return _new_lualinq(":selectMany", var_20_0)
end

function _where(arg_21_0, arg_21_1, arg_21_2, ...)
	local var_21_0 = {}

	if type(arg_21_1) == "function" then
		for iter_21_0, iter_21_1 in ipairs(arg_21_0.m_Data) do
			if arg_21_1(iter_21_1, arg_21_2, from({
				...
			}):toTuple()) then
				table.insert(var_21_0, iter_21_1)
			end
		end
	elseif type(arg_21_1) == "string" then
		local var_21_1 = {
			...
		}

		if #var_21_1 > 0 then
			table.insert(var_21_1, arg_21_2)

			return _intersectionby(arg_21_0, arg_21_1, var_21_1)
		elseif arg_21_2 ~= nil then
			for iter_21_2, iter_21_3 in ipairs(arg_21_0.m_Data) do
				if iter_21_3[arg_21_1] == arg_21_2 then
					table.insert(var_21_0, iter_21_3)
				end
			end
		else
			for iter_21_4, iter_21_5 in ipairs(arg_21_0.m_Data) do
				if iter_21_5[arg_21_1] ~= nil then
					table.insert(var_21_0, iter_21_5)
				end
			end
		end
	else
		loge("where called with unknown predicate type")
	end

	return _new_lualinq(":where", var_21_0)
end

function _whereIndex(arg_22_0, arg_22_1)
	local var_22_0 = {}

	for iter_22_0, iter_22_1 in ipairs(arg_22_0.m_Data) do
		if arg_22_1(iter_22_0, iter_22_1) then
			table.insert(var_22_0, iter_22_1)
		end
	end

	return _new_lualinq(":whereIndex", var_22_0)
end

function _take(arg_23_0, arg_23_1)
	return arg_23_0:whereIndex(function(arg_24_0, arg_24_1)
		return arg_24_0 <= arg_23_1
	end)
end

function _skip(arg_25_0, arg_25_1)
	return arg_25_0:whereIndex(function(arg_26_0, arg_26_1)
		return arg_26_0 > arg_25_1
	end)
end

function _zip(arg_27_0, arg_27_1, arg_27_2)
	arg_27_1 = from(arg_27_1)

	local var_27_0 = #arg_27_0.m_Data
	local var_27_1 = #arg_27_1.m_Data
	local var_27_2 = {}

	if var_27_1 < var_27_0 then
		var_27_0 = var_27_1
	end

	for iter_27_0 = 1, var_27_0 do
		var_27_2[iter_27_0] = arg_27_2(arg_27_0.m_Data[iter_27_0], arg_27_1.m_Data[iter_27_0])
	end

	return _new_lualinq(":zip", var_27_2)
end

function _distinct(arg_28_0, arg_28_1)
	local var_28_0 = {}

	arg_28_1 = arg_28_1 or function(arg_29_0, arg_29_1)
		return arg_29_0 == arg_29_1
	end

	for iter_28_0, iter_28_1 in ipairs(arg_28_0.m_Data) do
		local var_28_1 = false

		for iter_28_2, iter_28_3 in ipairs(var_28_0) do
			if arg_28_1(iter_28_1, iter_28_3) then
				var_28_1 = true
			end
		end

		if not var_28_1 then
			table.insert(var_28_0, iter_28_1)
		end
	end

	return _new_lualinq(":distinct", var_28_0)
end

function _union(arg_30_0, arg_30_1, arg_30_2)
	return arg_30_0:concat(from(arg_30_1)):distinct(arg_30_2)
end

function _except(arg_31_0, arg_31_1, arg_31_2)
	arg_31_1 = from(arg_31_1)

	return arg_31_0:where(function(arg_32_0)
		return not arg_31_1:contains(arg_32_0, arg_31_2)
	end)
end

function _intersection(arg_33_0, arg_33_1, arg_33_2)
	arg_33_1 = from(arg_33_1)

	return arg_33_0:where(function(arg_34_0)
		return arg_33_1:contains(arg_34_0, arg_33_2)
	end)
end

function _exceptby(arg_35_0, arg_35_1, arg_35_2)
	arg_35_2 = from(arg_35_2)

	return arg_35_0:where(function(arg_36_0)
		return not arg_35_2:contains(arg_36_0[arg_35_1])
	end)
end

function _intersectionby(arg_37_0, arg_37_1, arg_37_2)
	arg_37_2 = from(arg_37_2)

	return arg_37_0:where(function(arg_38_0)
		return arg_37_2:contains(arg_38_0[arg_37_1])
	end)
end

function _toIterator(arg_39_0)
	local var_39_0 = 0
	local var_39_1 = #arg_39_0.m_Data

	return function()
		var_39_0 = var_39_0 + 1

		if var_39_0 <= var_39_1 then
			return arg_39_0.m_Data[var_39_0]
		end
	end
end

function _toArray(arg_41_0)
	return arg_41_0.m_Data
end

function _toDictionary(arg_42_0, arg_42_1)
	local var_42_0 = {}

	for iter_42_0, iter_42_1 in ipairs(arg_42_0.m_Data) do
		local var_42_1, var_42_2 = arg_42_1(iter_42_1)

		if var_42_1 ~= nil then
			var_42_0[var_42_1] = var_42_2
		end
	end

	return var_42_0
end

function _toTuple(arg_43_0)
	return unpack(arg_43_0.m_Data)
end

function _first(arg_44_0, arg_44_1)
	if #arg_44_0.m_Data > 0 then
		return arg_44_0.m_Data[1]
	else
		return arg_44_1
	end
end

function _last(arg_45_0, arg_45_1)
	if #arg_45_0.m_Data > 0 then
		return arg_45_0.m_Data[#arg_45_0.m_Data]
	else
		return arg_45_1
	end
end

function _any(arg_46_0, arg_46_1)
	if arg_46_1 == nil then
		return #arg_46_0.m_Data > 0
	end

	for iter_46_0, iter_46_1 in ipairs(arg_46_0.m_Data) do
		if arg_46_1(iter_46_1) then
			return true
		end
	end

	return false
end

function _all(arg_47_0, arg_47_1)
	if arg_47_1 == nil then
		return #arg_47_0.m_Data == 0
	end

	for iter_47_0, iter_47_1 in ipairs(arg_47_0.m_Data) do
		if not arg_47_1(iter_47_1) then
			return false
		end
	end

	return true
end

function _count(arg_48_0, arg_48_1)
	if arg_48_1 == nil then
		return #arg_48_0.m_Data
	end

	local var_48_0 = 0

	for iter_48_0, iter_48_1 in ipairs(arg_48_0.m_Data) do
		if arg_48_1(iter_48_1) then
			var_48_0 = var_48_0 + 1
		end
	end

	return var_48_0
end

function _dump(arg_49_0)
	print(_dumpData(arg_49_0))
end

function _random(arg_50_0, arg_50_1)
	if #arg_50_0.m_Data == 0 then
		return arg_50_1
	end

	return arg_50_0.m_Data[math.random(1, #arg_50_0.m_Data)]
end

function _contains(arg_51_0, arg_51_1, arg_51_2)
	arg_51_2 = arg_51_2 or function(arg_52_0, arg_52_1)
		return arg_52_0 == arg_52_1
	end

	for iter_51_0, iter_51_1 in ipairs(arg_51_0.m_Data) do
		if arg_51_2(iter_51_1, arg_51_1) then
			return true
		end
	end

	return false
end

function _foreach(arg_53_0, arg_53_1, ...)
	if type(arg_53_1) == "function" then
		for iter_53_0, iter_53_1 in ipairs(arg_53_0.m_Data) do
			arg_53_1(iter_53_1, from({
				...
			}):toTuple())
		end
	elseif type(arg_53_1) == "string" then
		for iter_53_2, iter_53_3 in ipairs(arg_53_0.m_Data) do
			iter_53_3[arg_53_1](iter_53_3, from({
				...
			}):toTuple())
		end
	else
		loge("foreach called with unknown action type")
	end

	return arg_53_0
end

function _map(arg_54_0, arg_54_1, arg_54_2)
	local var_54_0 = arg_54_2

	for iter_54_0, iter_54_1 in ipairs(arg_54_0.m_Data) do
		var_54_0 = arg_54_1(iter_54_1, var_54_0)
	end

	return var_54_0
end

function _xmap(arg_55_0, arg_55_1, arg_55_2)
	local var_55_0
	local var_55_1 = arg_55_2

	for iter_55_0, iter_55_1 in ipairs(arg_55_0.m_Data) do
		var_55_0, var_55_1 = arg_55_1(iter_55_1, var_55_0, var_55_1)
	end

	return var_55_0
end

function _max(arg_56_0, arg_56_1)
	if arg_56_1 == nil then
		function arg_56_1(arg_57_0)
			return arg_57_0
		end
	end

	return arg_56_0:xmap(function(arg_58_0, arg_58_1, arg_58_2)
		local var_58_0 = arg_56_1(arg_58_0)

		if arg_58_2 == nil or arg_58_2 < var_58_0 then
			return arg_58_0, var_58_0
		else
			return arg_58_1, arg_58_2
		end
	end, nil)
end

function _min(arg_59_0, arg_59_1)
	if arg_59_1 == nil then
		function arg_59_1(arg_60_0)
			return arg_60_0
		end
	end

	return arg_59_0:xmap(function(arg_61_0, arg_61_1, arg_61_2)
		local var_61_0 = arg_59_1(arg_61_0)

		if arg_61_2 == nil or var_61_0 < arg_61_2 then
			return arg_61_0, var_61_0
		else
			return arg_61_1, arg_61_2
		end
	end, nil)
end

function _sum(arg_62_0, arg_62_1)
	if arg_62_1 == nil then
		function arg_62_1(arg_63_0)
			return arg_63_0
		end
	end

	return arg_62_0:map(function(arg_64_0, arg_64_1)
		arg_64_1 = arg_64_1 + arg_62_1(arg_64_0)

		return arg_64_1
	end, 0)
end

function _average(arg_65_0, arg_65_1)
	local var_65_0 = arg_65_0:count()

	if var_65_0 > 0 then
		return arg_65_0:sum(arg_65_1) / var_65_0
	else
		return 0
	end
end

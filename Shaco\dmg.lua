
local ove_0_10 = player:spellSlot(0)
local ove_0_11 = player:spellSlot(1)
local ove_0_12 = player:spellSlot(2)
local ove_0_13 = player:spellSlot(3)
local ove_0_14 = module.load(header.id, "Shaco/common")
local ove_0_15 = {
	30,
	60,
	90,
	120,
	150
}
local ove_0_16 = {
	70,
	95,
	120,
	145,
	170
}
local ove_0_17 = {
	0.32,
	0.34,
	0.36,
	0.38,
	0.4
}
local ove_0_18 = {
	150,
	300,
	450
}
local ove_0_19 = {
	0.25,
	0.3,
	0.35
}

emax_scale = {
	15,
	17.06,
	19.12,
	21.18,
	23.24,
	25.29,
	27.35,
	29.41,
	31.47,
	33.53,
	35.59,
	37.65,
	39.71,
	41.76,
	43.82,
	45.88,
	47.94,
	50,
	50,
	50,
	50,
	50,
	50,
	50,
	50,
	50,
	50
}

local function ove_0_20(arg_5_0)
	local slot_5_0 = arg_5_0.armor * player.percentArmorPenetration - player.flatArmorPenetration

	return slot_5_0 > -1 and 100 / (100 + slot_5_0) or 1
end

local function ove_0_21(arg_6_0)
	local slot_6_0 = arg_6_0.spellBlock * player.percentMagicPenetration - player.flatMagicPenetration

	return slot_6_0 > -1 and 100 / (100 + slot_6_0) or 1
end

local function ove_0_22(arg_7_0)
	if ove_0_10.level >= 1 then
		return (ove_0_15[ove_0_10.level] + player.totalAd * 0.4) * ove_0_20(arg_7_0)
	else
		return 0
	end
end

local function ove_0_23(arg_8_0)
	return 0
end

local function ove_0_24(arg_9_0)
	if ove_0_12.level >= 1 then
		PERCENTHEALTH = ove_0_14.GetPercentHealth(arg_9_0)
		xedamage = emax_scale[player.levelRef] + player.totalAp * 0.1

		if PERCENTHEALTH >= 30 then
			local slot_9_0 = ove_0_16[ove_0_12.level] + ove_0_14.GetBonusAD(player) * 0.8 + player.totalAp * 0.6
			local slot_9_1 = ove_0_21(arg_9_0)

			--print("NO BONUS", PERCENTHEALTH, slot_9_0 * slot_9_1)

			return slot_9_0 * slot_9_1
		else
			local slot_9_2 = (ove_0_16[ove_0_12.level] + ove_0_14.GetBonusAD(player) * 0.8 + player.totalAp * 0.6) * 1.432 + xedamage
			local slot_9_3 = ove_0_21(arg_9_0)

			--print(" BONUS ", PERCENTHEALTH, slot_9_2 * slot_9_3)

			return slot_9_2 * slot_9_3
		end
	else
		return 0
	end
end

local function ove_0_25(arg_10_0)
	if ove_0_13.level >= 1 then
		missinghealth = arg_10_0.maxHealth - arg_10_0.health

		return ove_0_18[ove_0_13.level] + missinghealth * ove_0_19[ove_0_13.level] - arg_10_0.healthRegenRate * 2
	else
		return 0
	end
end

return {
	q = ove_0_22,
	w = ove_0_23,
	e = ove_0_24,
	r = ove_0_25,
	calc = ove_0_20
}

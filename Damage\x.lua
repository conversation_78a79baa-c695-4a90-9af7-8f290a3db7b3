local Spells = {
	["CurseoftheSadMummy"] = {
		charName = "Amumu",
		slot = 3,
		type = "circular",
		speedss = math.huge,
		range = 0,
		delay = 0.25,
		radius = 550,
		hitbox = false,
		aoe = true,
		cc = true,
		collision = false
	},
	["InfernalGuardian"] = {
		charName = "Annie",
		slot = 3,
		type = "circular",
		speedss = math.huge,
		range = 600,
		delay = 0.25,
		radius = 290,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false
	},
	["EnchantedCrystalArrow"] = {
		charName = "Ashe",
		slot = 3,
		type = "linear",
		speedss = 1600,
		range = 25000,
		delay = 0.25,
		radius = 130,
		hitbox = true,
		aoe = false,
		cc = true,
		collision = false
	},
	["BraumRWrapper"] = {
		charName = "Braum",
		slot = 3,
		type = "linear",
		speedss = 1400,
		range = 1250,
		delay = 0.5,
		radius = 115,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false
	},
	["CassiopeiaR"] = {
		charName = "Cassiopeia",
		slot = 3,
		type = "conic",
		speedss = math.huge,
		range = 825,
		delay = 0.5,
		angle = 80,
		hitbox = false,
		aoe = true,
		cc = true,
		collision = false
	},
	["EkkoR"] = {
		charName = "Ekko",
		slot = 3,
		type = "circular",
		speedss = 1650,
		range = 1600,
		delay = 0.25,
		radius = 375,
		hitbox = false,
		aoe = true,
		cc = false,
		collision = false
	},
	["EvelynnR"] = {
		charName = "Evelynn",
		slot = 3,
		type = "conic",
		speedss = math.huge,
		range = 450,
		delay = 0.35,
		angle = 180,
		hitbox = false,
		aoe = true,
		cc = false,
		collision = false
	},
	["EzrealTrueshotBarrage"] = {
		charName = "Ezreal",
		slot = 3,
		type = "linear",
		speedss = 2000,
		range = 25000,
		delay = 1,
		radius = 160,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false
	},
	["FizzR"] = {
		charName = "Fizz",
		slot = 3,
		type = "linear",
		speedss = 1300,
		range = 1300,
		delay = 0.25,
		radius = 120,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false
	},
	["GnarR"] = {
		charName = "Gnar",
		slot = 3,
		type = "circular",
		speedss = math.huge,
		range = 475,
		delay = 0.25,
		radius = 475,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false
	},
	["GragasR"] = {
		charName = "Gragas",
		slot = 3,
		type = "circular",
		speedss = 1800,
		range = 1000,
		delay = 0.25,
		radius = 400,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false
	},
	["IllaoiR"] = {
		charName = "Illaoi",
		slot = 3,
		type = "circular",
		speedss = math.huge,
		range = 0,
		delay = 0.5,
		radius = 450,
		hitbox = false,
		aoe = true,
		cc = false,
		collision = false
	},
	["IreliaR"] = {
		charName = "Irelia",
		slot = 3,
		type = "linear",
		speedss = 2000,
		range = 1000,
		delay = 0.4,
		radius = 160,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false
	},
	["JinxR"] = {
		charName = "Jinx",
		slot = 3,
		type = "linear",
		speedss = 1700,
		range = 25000,
		delay = 0.6,
		radius = 110,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false
	},
	["KatarinaR"] = {
		charName = "Katarina",
		slot = 3,
		type = "circular",
		speedss = math.huge,
		range = 0,
		delay = 0,
		radius = 550,
		hitbox = false,
		aoe = true,
		cc = false,
		collision = false
	},
	["KennenShurikenStorm"] = {
		charName = "Kennen",
		slot = 3,
		type = "circular",
		speedss = math.huge,
		range = 0,
		delay = 0.25,
		radius = 550,
		hitbox = false,
		aoe = true,
		cc = false,
		collision = false
	},
	["LeonaSolarFlare"] = {
		charName = "Leona",
		slot = 3,
		type = "circular",
		speedss = math.huge,
		range = 1200,
		delay = 0.625,
		radius = 250,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false
	},
	["PykeR"] = {
		charName = "Pyke",
		slot = 3,
		type = "cross",
		speeds = math.huge,
		range = 750,
		delay = 0.5,
		radius1 = 300,
		radius2 = 50,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false
	},
	["LuxMaliceCannon"] = {
		charName = "Lux",
		slot = 3,
		type = "linear",
		speedss = math.huge,
		range = 3340,
		delay = 1,
		radius = 115,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false
	},
	["UFSlash"] = {
		charName = "Malphite",
		slot = 3,
		type = "circular",
		speedss = 2170,
		range = 1000,
		delay = 0,
		radius = 300,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false
	},
	["MissFortuneBulletTime"] = {
		charName = "MissFortune",
		slot = 3,
		type = "conic",
		speedss = math.huge,
		range = 1400,
		delay = 0.001,
		angle = 40,
		hitbox = false,
		aoe = true,
		cc = false,
		collision = false
	},
	["NamiR"] = {
		charName = "Nami",
		slot = 3,
		type = "linear",
		speedss = 850,
		range = 2750,
		delay = 0.5,
		radius = 215,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false
	},
	["AbsoluteZero"] = {
		charName = "Nunu",
		slot = 3,
		type = "circular",
		speedss = math.huge,
		range = 0,
		delay = 3.01,
		radius = 650,
		hitbox = false,
		aoe = true,
		cc = true,
		collision = false
	},
	["OrianaDetonateCommand"] = {
		charName = "Orianna",
		slot = 3,
		type = "circular",
		speedss = math.huge,
		range = 0,
		delay = 0.5,
		radius = 325,
		hitbox = false,
		aoe = true,
		cc = true,
		collision = false
	},
	["OrnnR"] = {
		charName = "Ornn",
		slot = 3,
		type = "linear",
		speedss = 1200,
		range = 2500,
		delay = 0.5,
		radius = 225,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false
	},
	["PoppyRSpell"] = {
		charName = "Poppy",
		slot = 3,
		type = "linear",
		speedss = 1600,
		range = 1900,
		delay = 0.6,
		radius = 80,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false
	},
	["RivenIzunaBlade"] = {
		charName = "Riven",
		slot = 3,
		type = "conic",
		speedss = 1600,
		range = 900,
		delay = 0.25,
		angle = 50,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false
	},
	["RumbleCarpetBombDummy"] = {
		charName = "Rumble",
		slot = 3,
		type = "linear",
		speedss = 1600,
		range = 1700,
		delay = 0.583,
		radius = 130,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false
	},
	["SejuaniR"] = {
		charName = "Sejuani",
		slot = 3,
		type = "linear",
		speedss = 1650,
		range = 1300,
		delay = 0.25,
		radius = 100,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false
	},
	["ShyvanaTransformLeap"] = {
		charName = "Shyvana",
		slot = 3,
		type = "linear",
		speedss = 1130,
		range = 850,
		delay = 0.25,
		radius = 160,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false
	},
	["SionR"] = {
		charName = "Sion",
		slot = 3,
		type = "linear",
		speedss = 950,
		range = 7500,
		delay = 0.125,
		radius = 200,
		hitbox = false,
		aoe = true,
		cc = true,
		collision = false
	},
	["SonaR"] = {
		charName = "Sona",
		slot = 3,
		type = "linear",
		speedss = 2250,
		range = 900,
		delay = 0.25,
		radius = 120,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false
	},
	["UrgotR"] = {
		charName = "Urgot",
		slot = 3,
		type = "linear",
		speedss = 3200,
		range = 1600,
		delay = 0.4,
		radius = 70,
		hitbox = true,
		aoe = false,
		cc = true,
		collision = false
	},
	["VarusR"] = {
		charName = "Varus",
		slot = 3,
		type = "linear",
		speedss = 1850,
		range = 1075,
		delay = 0.242,
		radius = 120,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false
	},
	["VelkozR"] = {
		charName = "Velkoz",
		slot = 3,
		type = "linear",
		speedss = math.huge,
		range = 1550,
		delay = 0.25,
		radius = 75,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false
	},
	["WarwickR"] = {
		charName = "Warwick",
		slot = 3,
		type = "linear",
		speedss = 1800,
		range = 1000,
		delay = 0.1,
		radius = 45,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false
	},
	["MonkeyKingSpinToWin"] = {
		charName = "MonkeyKing",
		slot = 3,
		type = "circular",
		speedss = math.huge,
		range = 0,
		delay = 0,
		radius = 325,
		hitbox = false,
		aoe = true,
		cc = true,
		collision = false
	},
	["ZiggsR"] = {
		charName = "Ziggs",
		slot = 3,
		type = "circular",
		speedss = math.huge,
		range = 5300,
		delay = 0.375,
		radius = 550,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false
	},
	["ZyraR"] = {
		charName = "Zyra",
		slot = 3,
		type = "circular",
		speedss = math.huge,
		range = 700,
		delay = 1.775,
		radius = 575,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false
	}
}
return Spells
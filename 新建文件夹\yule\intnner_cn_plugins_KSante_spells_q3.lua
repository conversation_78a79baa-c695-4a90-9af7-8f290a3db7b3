

local ove_0_10 = module.internal("pred")
local ove_0_11 = module.internal("TS")
local ove_0_12 = module.load(header.id, "common/common")
local ove_0_13 = module.load(header.id, "common/Compute/computer")
local ove_0_14 = module.load(header.id, "plugins/KSante/helper")
local ove_0_15 = module.load(header.id, "plugins/KSante/menu")
local ove_0_16 = {
	range = 825,
	last = 0,
	slot = player:spellSlot(_Q),
	result = {},
	pred_input = {
		speed = 1800,
		range = 825,
		delay = 0.25,
		boundingRadiusMod = 0,
		width = 50,
		source = player.path.serverPos:to2D()
	}
}

if player:spellSlot(4).name == "SummonerFlash" then
	ove_0_16.FlashSlot = 4
elseif player:spellSlot(5).name == "SummonerFlash" then
	ove_0_16.FlashSlot = 5
else
	ove_0_16.FlashSlot = nil
end

function ove_0_16.is_ready()
	if ove_0_14.HaveQ3() then
		return ove_0_16.slot.state == 0
	end

	return false
end

function ove_0_16.invoke_action()
	player:castSpell("pos", _Q, ove_0_16.result.pos:to3D())
end

function ove_0_16.get_action_state()
	if ove_0_16.is_ready() then
		return ove_0_16.get_prediction()
	end
end

function ove_0_16.get_prediction()
	if ove_0_16.last == game.time then
		return ove_0_16.result.seg
	end

	ove_0_16.last = game.time
	ove_0_16.result.obj = nil
	ove_0_16.result.seg = nil
	ove_0_16.result.pos = nil
	ove_0_16.result = ove_0_11.get_result(function(arg_9_0, arg_9_1, arg_9_2)
		if arg_9_2 > 900 then
			return false
		end

		if not ove_0_12.isValidTarget(arg_9_1) then
			return false
		end

		local slot_9_0 = ove_0_10.linear.get_prediction(ove_0_16.pred_input, arg_9_1, player.path.serverPos:to2D())

		if arg_9_2 > ove_0_16.range then
			return false
		end

		if not slot_9_0 then
			return false
		end

		local slot_9_1 = ove_0_13.Compute(ove_0_16.pred_input, slot_9_0, arg_9_1)

		if slot_9_1 and slot_9_1 < 0 then
			return false
		end

		if slot_9_0 and slot_9_0.startPos:dist(slot_9_0.endPos) <= ove_0_16.range then
			arg_9_0.seg = slot_9_0
			arg_9_0.obj = arg_9_1
			arg_9_0.pos = (ove_0_10.core.get_pos_after_time(arg_9_1, slot_9_1) + slot_9_0.endPos) / 2

			return true
		end
	end)

	if ove_0_16.result.pos then
		return ove_0_16.result
	end
end

function ove_0_16.invoke_action_q3_lane_clear()
	local slot_10_0 = {}
	local slot_10_1 = objManager.minions

	for iter_10_0 = 0, slot_10_1.size[TEAM_ENEMY] - 1 do
		local slot_10_2 = slot_10_1[TEAM_ENEMY][iter_10_0]

		if slot_10_2 and not slot_10_2.isDead and slot_10_2.isVisible and player.path.serverPos:distSqr(slot_10_2.path.serverPos) <= 680625 then
			slot_10_0[#slot_10_0 + 1] = slot_10_2
		end
	end

	local slot_10_3 = 0
	local slot_10_4

	for iter_10_1 = 1, #slot_10_0 do
		local slot_10_5 = slot_10_0[iter_10_1]
		local slot_10_6 = player.path.serverPos + (slot_10_5.path.serverPos - player.path.serverPos):norm() * (slot_10_5.path.serverPos:dist(player.path.serverPos) + 825)
		local slot_10_7 = 1

		for iter_10_2 = 1, #slot_10_0 do
			if iter_10_2 ~= iter_10_1 then
				local slot_10_8 = slot_10_0[iter_10_2]
				local slot_10_9, slot_10_10, slot_10_11 = ove_0_14.ProjectOn(slot_10_8.path.serverPos, player.path.serverPos, slot_10_6)

				if slot_10_9 and slot_10_10:dist(slot_10_8.path.serverPos) < 50 + slot_10_8.boundingRadius then
					slot_10_7 = slot_10_7 + 1
				end
			end
		end

		if not slot_10_4 or slot_10_3 < slot_10_7 then
			slot_10_4, slot_10_3 = slot_10_6, slot_10_7
		end

		if slot_10_4 and slot_10_3 >= ove_0_15.farming.lane.q.min_minions3:get() then
			player:castSpell("pos", _Q, slot_10_4)

			break
		end
	end
end

function ove_0_16.flash_q()
	player:move(game.mousePos)

	local slot_11_0 = ove_0_12.GetTarget(1250)

	if slot_11_0 and ove_0_12.isValidTarget(slot_11_0) then
		local slot_11_1 = ove_0_10.linear.get_prediction(ove_0_16.pred_input, slot_11_0, player.path.serverPos:to2D())

		if slot_11_1.startPos:distSqr(slot_11_1.endPos) < 1440000 and slot_11_1.startPos:distSqr(slot_11_1.endPos) > 680625 then
			player:castSpell("pos", _Q, slot_11_1.endPos:to3D())
			ove_0_12.IntDelayAction(function()
				player:castSpell("pos", ove_0_16.FlashSlot, slot_11_0.pos)
			end, 0.01 + network.latency)
		end
	end
end

return ove_0_16

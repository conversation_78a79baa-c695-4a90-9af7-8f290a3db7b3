

local ove_0_10 = module.internal("orb")
local ove_0_11 = module.internal("TS")
local ove_0_12 = module.internal("pred")
local ove_0_13 = module.load(header.id, "common/common")
local ove_0_14 = module.load(header.id, "plugins/Swain/menu")
local ove_0_15 = module.load(header.id, "plugins/Swain/helper")
local ove_0_16 = {
	range = 935,
	last = 0,
	buff_name = "swaineroot",
	slot = player:spellSlot(_E),
	result = {}
}

function ove_0_16.is_ready()
	if ove_0_16.slot.name == "SwainE2" then
		return ove_0_16.slot.state == 0
	end
end

function ove_0_16.get_action_state()
	if ove_0_16.is_ready() then
		return ove_0_16.get_prediction()
	end
end

function ove_0_16.invoke_action()
	player:castSpell("self", _E)
end

function ove_0_16.interrupt_now()
	if ove_0_15.interrupt_data.owner and ove_0_15.interrupt_data.owner.buff.swaineroot then
		player:castSpell("self", _E)

		return true
	end
end

function ove_0_16.can_cast()
	if ove_0_16.w_xerath and ove_0_16.result.obj.buff[ove_0_16.buff_name].endTime - game.time < 0.1 and ove_0_16.w_xerath.pos:dist(ove_0_16.result.obj.path.serverPos) < 325 + ove_0_16.result.obj.boundingRadius then
		return true
	end

	if ove_0_16.w_xerath and ove_0_16.w_xerath.pos:dist(ove_0_16.result.obj.path.serverPos) < 325 + ove_0_16.result.obj.boundingRadius then
		return false
	end

	if ove_0_16.result.obj.path.serverPos:dist(player.pos) <= ove_0_13.GetAARange() and ove_0_16.result.obj.buff[ove_0_16.buff_name].endTime - game.time < 0.1 then
		return true
	end

	if ove_0_16.result.obj.pos:dist(player.path.serverPos) > ove_0_13.GetAARange() then
		return true
	end
end

function ove_0_16.get_prediction()
	if ove_0_16.last == game.time then
		return ove_0_16.result.obj
	end

	ove_0_16.last = game.time
	ove_0_16.result.obj = nil
	ove_0_16.result = ove_0_11.get_result(function(arg_11_0, arg_11_1, arg_11_2)
		if arg_11_2 > math.huge then
			return
		end

		if not arg_11_1.buff[ove_0_16.buff_name] then
			return
		end

		if ove_0_13.GetPercentHealth(arg_11_1) > ove_0_14.combo.e.whitelist["health" .. arg_11_1.charName]:get() and ove_0_14.combo.e.whitelist[arg_11_1.charName]:get() then
			return
		end

		arg_11_0.obj = arg_11_1

		return true
	end)

	if ove_0_16.result.obj and ove_0_16.can_cast() then
		return ove_0_16.result
	end
end

function ove_0_16.create_w_xerath(arg_12_0)
	if arg_12_0.name:find("Swain") and arg_12_0.name:find("W_AOE_Initial") then
		ove_0_16.w_xerath = arg_12_0
	end
end

function ove_0_16.delete_w_xerath(arg_13_0)
	if arg_13_0 and ove_0_16.w_xerath and arg_13_0.ptr == ove_0_16.w_xerath.ptr then
		ove_0_16.w_xerath = nil
	end
end

function ove_0_16.on_draw()
	return
end

return ove_0_16

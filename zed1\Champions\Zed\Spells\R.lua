
local ove_0_10 = player
local ove_0_11 = module.load("Kloader", "Lib/TurretDive")
local ove_0_12 = {
	range = 625,
	Slot = player:spellSlot(3)
}

function ove_0_12.Cast(arg_5_0)
	-- print 5
	if not ove_0_12.IsR1() or not ove_0_12.Ready() or ove_0_11.Dive<PERSON><PERSON><PERSON>(arg_5_0.pos) == false then
		return
	end

	if arg_5_0 and vec3.dist(ove_0_10.pos, arg_5_0.pos) < ove_0_12.range then
		player:castSpell("obj", 3, arg_5_0)
	end
end

function ove_0_12.CastR2()
	-- print 6
	if not ove_0_12.Ready() or ove_0_12.IsR1() then
		return
	end

	player:castSpell("self", 3)
end

function ove_0_12.Ready()
	-- print 7
	return ove_0_12.Slot.state == 0
end

function ove_0_12.IsR1()
	-- print 8
	return ove_0_12.Slot.name == "ZedR"
end

function ove_0_12.IsR2()
	-- print 9
	return ove_0_12.Slot.name == "ZedR2"
end

function ove_0_12.Cost()
	-- print 10
	return ove_0_10.manaCost3
end

function ove_0_12.Level()
	-- print 11
	return player:spellSlot(3).level
end

return ove_0_12

local itemManager = {}

function itemManager.HasItem(wannaItem, source)
    if not wannaItem or (not type(wannaItem) == "string" and not type(wannaItem) == "number") then
        return false
    end

    source = source or player
    if not source or source == nil then
        return false
    end

    if type(wannaItem) == "string" then
        local lowerName = string.lower(wannaItem)
        for i = 6, 12 do
            local itemSlot = source:spellSlot(i)
            if itemSlot and itemSlot.name then
                local lowerItemName = string.lower(itemSlot.name)
                if lowerItemName == lowerName then
                    return true
                end
            end
        end
    elseif type(wannaItem) == "number" then
        for i = 0, 6 do
            local itemID = source:itemID(i)
            if itemID and itemID ~= 0 and itemID == wannaItem then
                return true
            end
        end
    end

    return false
end

function itemManager.CanUseItem(wannaItem, source)
    if not wannaItem or not type(wannaItem) == "string" then
        return false
    end

    source = source or player
    if not source or source == nil then
        return false
    end

    local lowerName = string.lower(wannaItem)
    for i = 6, 12 do
        local itemSlot = source:spellSlot(i)
        if itemSlot and itemSlot.name then
            local lowerItemName = string.lower(itemSlot.name)
            if lowerItemName == lowerName then
                if itemSlot.state and itemSlot.state == 0 then
                    return true
                end
            end
        end
    end

    return false
end

function itemManager.CastOnPlayer(wannaItem)
    if not wannaItem or not type(wannaItem) == "string" then
        return
    end

    local lowerName = string.lower(wannaItem)
    for i = 6, 12 do
        local itemSlot = player:spellSlot(i)
        if itemSlot and itemSlot.name then
            local lowerItemName = string.lower(itemSlot.name)
            if lowerItemName == lowerName then
                if itemSlot.state and itemSlot.state == 0 then
                    player:castSpell("self", i)
                end
            end
        end
    end
end

function itemManager.CastOnUnit(target, wannaItem)
    if not wannaItem or not type(wannaItem) == "string" then
        return
    end

    if not target or target == nil then
        return
    end

    local lowerName = string.lower(wannaItem)
    for i = 6, 12 do
        local itemSlot = player:spellSlot(i)
        if itemSlot and itemSlot.name then
            local lowerItemName = string.lower(itemSlot.name)
            if lowerItemName == lowerName then
                if itemSlot.state and itemSlot.state == 0 then
                    player:castSpell("obj", i, target)
                end
            end
        end
    end
end

function itemManager.CastOnPosition(pos, wannaItem)
    if not wannaItem or not type(wannaItem) == "string" then
        return
    end

    if not pos or (pos.type ~= "vec3" and pos.type ~= "vec2") then
        return
    end

    local lowerName = string.lower(wannaItem)
    for i = 6, 12 do
        local itemSlot = player:spellSlot(i)
        if itemSlot and itemSlot.name then
            local lowerItemName = string.lower(itemSlot.name)
            if lowerItemName == lowerName then
                if itemSlot.state and itemSlot.state == 0 then
                    player:castSpell("pos", i, pos)
                end
            end
        end
    end
end

return itemManager

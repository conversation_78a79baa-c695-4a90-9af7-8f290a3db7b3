local ove_0_10 = module.load("<PERSON>", "Champions/Zed/Util/Detection")
local ove_0_11 = module.internal("pred")
local ove_0_12 = module.load("<PERSON>", "Lib/MyCommon")
local ove_0_13 = player
local ove_0_14
local ove_0_15 = {
	range = 925,
	PredInput = {
		speed = 1700,
		boundingRadiusMod = 1,
		width = 50,
		delay = 0.25,
		collision = {
			minion = true,
			hero = false,
			wall = true
		}
	},
	Slot = player:spellSlot(0),
	-- 最新版本Q技能数据
	baseDamage = {80, 120, 160, 200, 240},
	adRatio = 1.0,
	energyCost = 75
}

function ove_0_15.Cast(arg_5_0)
	if not arg_5_0 or arg_5_0.isDead or not ove_0_15.Ready() then
		return false
	end

	local shadows = ove_0_10.GetShadowInfo()
	local bestPos = nil
	local highestHitChance = -1
	local bestSourcePos = nil

	-- 检查每个可能的施法位置（玩家位置和每个影子位置）
	local castPositions = {player.pos}
	if shadows.W then table.insert(castPositions, shadows.W) end
	if shadows.R then table.insert(castPositions, shadows.R) end

	for _, sourcePos in ipairs(castPositions) do
		if sourcePos:dist(arg_5_0.pos) <= ove_0_15.range then
			local sourcePos2D = vec2(sourcePos.x, sourcePos.z)
			local pred = ove_0_11.linear.get_prediction(ove_0_15.PredInput, arg_5_0, sourcePos2D)

			if pred and pred.startPos:dist(pred.endPos) <= ove_0_15.range - arg_5_0.boundingRadius then
				local hitChance = pred.hitchance or 0

				-- 如果目标被R标记，优先从R影子释放Q
				if ove_0_10.IsTargetMarked(arg_5_0) and sourcePos == shadows.R then
					hitChance = hitChance + 3
				elseif ove_0_10.IsTargetMarked(arg_5_0) and sourcePos ~= player.pos then
					hitChance = hitChance + 1.5
				end

				-- 如果目标被控制，大幅增加命中率
				if arg_5_0.buff[BUFF_STUN] or arg_5_0.buff[BUFF_SNARE] or arg_5_0.buff[BUFF_KNOCKUP] or arg_5_0.buff[BUFF_CHARM] or arg_5_0.buff[BUFF_FEAR] then
					hitChance = hitChance + 2.5
				end

				-- 如果目标血量很低，优先命中
				if arg_5_0.health / arg_5_0.maxHealth <= 0.3 then
					hitChance = hitChance + 1
				end

				-- 考虑距离因素，距离越近命中率越高
				local distanceFactor = 1 - (sourcePos:dist(arg_5_0.pos) / ove_0_15.range)
				hitChance = hitChance + distanceFactor * 0.5

				if hitChance > highestHitChance then
					highestHitChance = hitChance
					bestPos = vec3(pred.endPos.x, player.y, pred.endPos.y)
					bestSourcePos = sourcePos
				end
			end
		end
	end

	if bestPos then
		player:castSpell("pos", 0, bestPos)
		return true
	end

	return false
end

function ove_0_15.Castlogic(arg_6_0)
	if not ove_0_15.Ready() or not arg_6_0 then
		return false
	end

	local slot_6_0 = ove_0_11.linear.get_prediction(ove_0_15.PredInput, arg_6_0)

	if slot_6_0 and slot_6_0.startPos:dist(slot_6_0.endPos) < ove_0_15.range - arg_6_0.boundingRadius then
		player:castSpell("pos", 0, vec3(slot_6_0.endPos.x, arg_6_0.y, slot_6_0.endPos.y))
		return true
	end
	
	return false
end

-- 尝试同时从玩家和W影子位置释放Q
function ove_0_15.CastWQ(arg_7_0)
	if not arg_7_0 or arg_7_0.isDead or not ove_0_15.Ready() then
		return false
	end

	local shadows = ove_0_10.GetShadowInfo()
	local wPos = shadows.W
	
	if not wPos or wPos:dist(arg_7_0.pos) > ove_0_15.range then
		return ove_0_15.Cast(arg_7_0)
	end
	
	-- 从W影子和玩家自身同时预测
	local w2D = vec2(wPos.x, wPos.z)
	local wPred = ove_0_11.linear.get_prediction(ove_0_15.PredInput, arg_7_0, w2D)
	local playerPred = ove_0_11.linear.get_prediction(ove_0_15.PredInput, arg_7_0)
	
	-- 优先从W影子释放，因为玩家会在之后有更多机会释放Q
	if wPred and wPred.startPos:dist(wPred.endPos) < ove_0_15.range - arg_7_0.boundingRadius then
		player:castSpell("pos", 0, vec3(wPred.endPos.x, arg_7_0.y, wPred.endPos.y))
		return true
	elseif playerPred and playerPred.startPos:dist(playerPred.endPos) < ove_0_15.range - arg_7_0.boundingRadius then
		player:castSpell("pos", 0, vec3(playerPred.endPos.x, arg_7_0.y, playerPred.endPos.y))
		return true
	end
	
	return false
end

-- 尝试同时从玩家、W和R影子位置释放Q
function ove_0_15.CastTripleQ(arg_7_0)
	if not arg_7_0 or arg_7_0.isDead or not ove_0_15.Ready() then
		return false
	end
	
	local success = false
	local shadows = ove_0_10.GetShadowInfo()
	
	-- 首先从R影子释放（因为它是最临时的）
	if shadows.R and shadows.R:dist(arg_7_0.pos) <= ove_0_15.range then
		local r2D = vec2(shadows.R.x, shadows.R.z)
		local rPred = ove_0_11.linear.get_prediction(ove_0_15.PredInput, arg_7_0, r2D)
		
		if rPred and rPred.startPos:dist(rPred.endPos) <= ove_0_15.range - arg_7_0.boundingRadius then
			player:castSpell("pos", 0, vec3(rPred.endPos.x, arg_7_0.y, rPred.endPos.y))
			success = true
		end
	end
	
	-- 然后从W影子释放
	if not success and shadows.W and shadows.W:dist(arg_7_0.pos) <= ove_0_15.range then
		local w2D = vec2(shadows.W.x, shadows.W.z)
		local wPred = ove_0_11.linear.get_prediction(ove_0_15.PredInput, arg_7_0, w2D)
		
		if wPred and wPred.startPos:dist(wPred.endPos) <= ove_0_15.range - arg_7_0.boundingRadius then
			player:castSpell("pos", 0, vec3(wPred.endPos.x, arg_7_0.y, wPred.endPos.y))
			success = true
		end
	end
	
	-- 最后从玩家位置释放
	if not success and player.pos:dist(arg_7_0.pos) <= ove_0_15.range then
		local playerPred = ove_0_11.linear.get_prediction(ove_0_15.PredInput, arg_7_0)
		
		if playerPred and playerPred.startPos:dist(playerPred.endPos) <= ove_0_15.range - arg_7_0.boundingRadius then
			player:castSpell("pos", 0, vec3(playerPred.endPos.x, arg_7_0.y, playerPred.endPos.y))
			success = true
		end
	end
	
	return success
end

function ove_0_15.Ready()
	return ove_0_15.Slot.state == 0
end

function ove_0_15.Cost()
	return ove_0_15.energyCost
end

-- 获取Q技能伤害
function ove_0_15.GetDamage(target)
	if not target or not ove_0_15.Ready() then return 0 end

	local level = ove_0_15.Level()
	if level == 0 then return 0 end

	local baseDmg = ove_0_15.baseDamage[level]
	local bonusDmg = player.totalAttackDamage * ove_0_15.adRatio
	local totalDmg = baseDmg + bonusDmg

	-- 计算护甲减免
	local armorPen = 100 / (100 + math.max(0, target.armor * 0.6))

	return totalDmg * armorPen
end

function ove_0_15.Level()
	return player:spellSlot(0).level
end

return ove_0_15

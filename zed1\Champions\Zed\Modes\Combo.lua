
local ove_0_10 = module.load("Kloader", "Champions/Zed/Util/Detection")
local ove_0_11 = module.load("Kloader", "Lib/MyCommon")
local ove_0_12 = module.load("Kloader", "Champions/Zed/Spells/R")
local ove_0_13 = module.load("Kloader", "Champions/Zed/Spells/W")
local ove_0_14 = module.load("Kloader", "Champions/Zed/Spells/Q")
local ove_0_15 = module.load("Kloader", "Champions/Zed/Spells/E")
local ove_0_16 = module.load("Kloader", "Lib/Summoners")
local ove_0_17 = module.load("Kloader", "Champions/Zed/Util/menu")
local ove_0_18 = module.load("Kloader", "Lib/DelayAction")
local ove_0_19 = module.load("Kloader", "Champions/Zed/Modes/Misc")
local ove_0_20 = module.load("Kloader", "Lib/TurretDive")
local ove_0_21 = player
local ove_0_22 = {}

function ove_0_22.Execute(arg_5_0)
	-- print 5
	if ove_0_11.IsValidTarget(arg_5_0) then
		if not ove_0_20.DiveLogic(arg_5_0.pos) then
			return
		end

		if ove_0_17.combo.asasinkey:get() then
			ove_0_22.Old(arg_5_0)
		else
			ove_0_22.New(arg_5_0)
		end
	end
end

function ove_0_22.Old(arg_6_0)
	-- print 6
	local slot_6_0 = ove_0_17.combo.forcer:get() and ove_0_17.combo.useR:get() and ove_0_12.Ready() and ove_0_12.IsR1() and ove_0_12.range - 5 or 1500

	if ove_0_10.Rpos then
		if ove_0_17.combo.rlogic:get() == 1 then
			ove_0_22.castpos = arg_6_0.pos + (arg_6_0.pos - ove_0_10.Rpos):norm() * 450
		elseif ove_0_17.combo.rlogic:get() == 2 then
			ove_0_22.castpos = arg_6_0.pos - ove_0_10.Rpos
			ove_0_22.castpos = vec2(ove_0_22.castpos.x, ove_0_22.castpos.z):perp2()
			ove_0_22.castpos = vec3(ove_0_22.castpos.x, game.mousePos.y, ove_0_22.castpos.y)
			ove_0_22.castpos = ove_0_21.pos + ove_0_22.castpos:norm() * 450
		elseif ove_0_17.combo.rlogic:get() == 3 then
			ove_0_22.castpos = game.mousePos
		end
	end

	if slot_6_0 > ove_0_11.Dist(arg_6_0) then
		if ove_0_11.Dist(arg_6_0) < ove_0_12.range and ove_0_17.combo.useR:get() and ove_0_19.blacklistcheck(arg_6_0) then
			ove_0_12.Cast(arg_6_0)
		end

		if ove_0_11.HasBuff(arg_6_0, ove_0_11.zedbuffname) then
			if ove_0_17.combo.useW:get() and ove_0_11.Dist(arg_6_0) < ove_0_13.range and ove_0_22.castpos then
				ove_0_13.Cast1(ove_0_22.castpos)
			end
		elseif ove_0_17.combo.useW:get() and ove_0_11.Dist(arg_6_0) < ove_0_13.range and ove_0_13.IsW1() then
			ove_0_13.Cast1(arg_6_0.pos)
		elseif ove_0_11.Dist(arg_6_0) > ove_0_13.range and ove_0_17.combo.useW:get() and ove_0_11.Dist(arg_6_0) < ove_0_13.range + ove_0_14.range / 2 then
			local slot_6_1 = ove_0_21.pos + (arg_6_0.pos - ove_0_21.pos):norm() * 700

			ove_0_13.Cast1(slot_6_1)

			if ove_0_17.combo.secondw:get() then
				ove_0_13.Cast2()
			end
		end

		if ove_0_17.combo.useE:get() then
			ove_0_15.Cast(arg_6_0)
		end

		if not ove_0_15.Ready() then
			ove_0_18.Cast(function()
				-- print 7
				ove_0_13.Cast2()
			end, 1.32)
		end

		if ove_0_17.combo.useW:get() and not ove_0_13.Ready() or not ove_0_17.combo.useW:get() then
			if ove_0_11.Dist(arg_6_0) < ove_0_14.range and ove_0_17.combo.useQ:get() then
				if ove_0_13.Ready() and ove_0_17.combo.useW:get() then
					if ove_0_10.Wpos then
						ove_0_18.Cast(function()
							-- print 8
							ove_0_14.CastWQ(arg_6_0)
						end, 0.18)
					end
				else
					ove_0_14.Cast(arg_6_0)
				end
			elseif ove_0_10.Wpos and ove_0_10.Wpos:dist(arg_6_0.pos) < ove_0_14.range and ove_0_17.combo.useQ:get() then
				ove_0_14.CastWQ(arg_6_0)
			end
		end

		if ove_0_13.Ready() and ove_0_11.Dist(arg_6_0) < 1400 and ove_0_11.Dist(arg_6_0) > 850 and ove_0_17.combo.wgap:get() then
			local slot_6_2 = ove_0_21.pos + (arg_6_0.pos - ove_0_21.pos):norm() * 700

			ove_0_13.Cast(slot_6_2)
		end

		if ove_0_16.Ignite.real then
			local slot_6_3 = 50 + ove_0_21.levelRef * 20
			local slot_6_4 = arg_6_0.health

			if ove_0_17.combo.useI:get() == 1 then
				if slot_6_4 <= slot_6_3 * 1.5 and ove_0_11.Dist(arg_6_0) < ove_0_16.Ignite.range then
					ove_0_16.CastI(arg_6_0)
				end
			elseif ove_0_17.combo.useI:get() == 2 and slot_6_4 <= slot_6_3 and ove_0_11.Dist(arg_6_0) < ove_0_16.Ignite.range then
				ove_0_16.CastI(arg_6_0)
			end
		end
	end
end

function ove_0_22.New(arg_9_0)
	-- print 9
	local slot_9_0 = arg_9_0.pos - ove_0_21.pos
	local slot_9_1 = vec2(slot_9_0.x, slot_9_0.z):perp2()
	local slot_9_2 = vec3(slot_9_1.x, game.mousePos.y, slot_9_1.y)
	local slot_9_3 = arg_9_0.pos + slot_9_2:norm() * 600

	if ove_0_11.Dist(arg_9_0) < ove_0_13.range - 150 and ove_0_13.IsW1() and ove_0_17.combo.useW:get() then
		ove_0_18.Cast(function()
			-- print 10
			ove_0_13.Cast1(slot_9_3)
		end, 0.37)
	end

	if ove_0_17.combo.useR:get() and ove_0_19.blacklistcheck(arg_9_0) and ove_0_13.IsW2() then
		ove_0_12.Cast(arg_9_0)
	end

	if ove_0_11.HasBuff(arg_9_0, ove_0_11.zedbuffname) then
		if ove_0_13.IsW2() then
			ove_0_13.Cast2()
		end

		if ove_0_17.combo.useE:get() then
			ove_0_15.Cast(arg_9_0)
		end

		if not ove_0_15.Ready() and not ove_0_13.Ready() and ove_0_17.combo.useQ:get() then
			if ove_0_10.Wpos then
				ove_0_18.Cast(function()
					-- print 11
					ove_0_14.CastWQ(arg_9_0)
				end, 1.32)
			else
				ove_0_18.Cast(function()
					-- print 12
					ove_0_14.Cast(arg_9_0)
				end, 1.32)
			end
		end
	elseif not ove_0_12.Ready() or ove_0_12.IsR2() then
		if ove_0_17.combo.useE:get() then
			ove_0_15.Cast(arg_9_0)
		end

		if ove_0_17.combo.useQ:get() then
			if ove_0_10.Wpos then
				ove_0_14.CastWQ(arg_9_0)
			else
				ove_0_14.Cast(arg_9_0)
			end
		end
	end
end

return ove_0_22



return {
	AatroxQ = {
		slot = 0,
		charName = "Aatrox",
		hard_cc = true,
		delay = 0.6,
		physical = true,
		type = "linear",
		range = 650,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false,
		radius = 200,
		speedss = math.huge
	},
	AatroxQ2 = {
		slot = 0,
		charName = "Aatrox",
		hard_cc = true,
		delay = 0.6,
		physical = true,
		type = "linear",
		range = 525,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false,
		radius = 300,
		speedss = math.huge
	},
	AatroxQ3 = {
		slot = 0,
		charName = "Aatrox",
		hard_cc = true,
		delay = 0.6,
		physical = true,
		type = "circular",
		range = 200,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false,
		radius = 300,
		speedss = math.huge
	},
	AatroxW = {
		slot = 1,
		charName = "Aatrox",
		delay = 0.25,
		physical = true,
		speedss = 1800,
		type = "linear",
		range = 825,
		hitbox = true,
		aoe = false,
		cc = true,
		collision = true,
		radius = 80
	},
	AatroxE = {
		radius1 = 120,
		charName = "Aatrox",
		radius2 = 0,
		delay = 0.25,
		slot = 2,
		speedss = 1200,
		type = "triangular",
		range = 1000,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false
	},
	AhriOrbofDeception = {
		slot = 0,
		charName = "Ahri",
		delay = 0.25,
		speedss = 2500,
		type = "linear",
		range = 880,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false,
		radius = 100
	},
	AhriOrbReturn = {
		slot = 0,
		charName = "Ahri",
		delay = 0.25,
		speedss = 2500,
		type = "linear",
		range = 880,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false,
		radius = 100
	},
	AhriSeduce = {
		slot = 2,
		charName = "Ahri",
		hard_cc = true,
		delay = 0.25,
		speedss = 1550,
		type = "linear",
		range = 965,
		hitbox = true,
		aoe = false,
		cc = false,
		collision = true,
		radius = 60
	},
	AkaliQ = {
		slot = 0,
		charName = "Akali",
		angle = 45,
		delay = 0.25,
		type = "conic",
		range = 550,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false,
		speedss = math.huge
	},
	AkaliW = {
		slot = 1,
		charName = "Akali",
		delay = 0.25,
		no_dmg = true,
		type = "circular",
		range = 300,
		hitbox = false,
		aoe = true,
		cc = false,
		collision = false,
		radius = 300,
		speedss = math.huge
	},
	AkaliE = {
		slot = 2,
		charName = "Akali",
		delay = 0.25,
		speedss = 1650,
		type = "linear",
		range = 825,
		hitbox = true,
		aoe = false,
		cc = false,
		collision = true,
		radius = 70
	},
	AkaliR = {
		slot = 3,
		charName = "Akali",
		delay = 0,
		speedss = 1650,
		type = "linear",
		range = 575,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false,
		radius = 80
	},
	AkaliRb = {
		slot = 3,
		charName = "Akali",
		delay = 0,
		speedss = 3300,
		type = "linear",
		range = 575,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false,
		radius = 80
	},
	AkshanQ = {
		slot = 0,
		charName = "Akshan",
		hard_cc = false,
		delay = 0.25,
		no_dmg = false,
		physical = true,
		speedss = 1500,
		type = "linear",
		range = 800,
		hitbox = true,
		aoe = true,
		cc = false,
		instant = false,
		collision = false,
		radius = 60
	},
	AkshanR = {
		slot = 3,
		charName = "Akshan",
		hard_cc = false,
		delay = 2.5,
		no_dmg = false,
		physical = true,
		speedss = 3200,
		type = "linear",
		range = 3000,
		hitbox = true,
		aoe = false,
		cc = false,
		instant = false,
		collision = true,
		radius = 40
	},
	AkshanRMissile = {
		slot = 3,
		charName = "Akshan",
		hard_cc = false,
		delay = 0.125,
		no_dmg = false,
		physical = true,
		speedss = 3200,
		type = "linear",
		range = 3000,
		hitbox = true,
		aoe = false,
		cc = false,
		instant = false,
		collision = true,
		radius = 40
	},
	ApheliosCalibrumQ = {
		slot = 0,
		charName = "Aphelios",
		delay = 0.35,
		physical = true,
		speedss = 1800,
		type = "linear",
		range = 1450,
		hitbox = true,
		aoe = false,
		cc = false,
		collision = true,
		radius = 60
	},
	ApheliosInfernumQ = {
		slot = 0,
		charName = "Aphelios",
		angle = 45,
		delay = 0.4,
		physical = true,
		speedss = 3335,
		type = "conic",
		range = 806,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = true
	},
	ApheliosR = {
		slot = 3,
		charName = "Aphelios",
		delay = 0.5,
		physical = true,
		speedss = 2050,
		type = "linear",
		range = 1600,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false,
		radius = 120
	},
	Pulverize = {
		slot = 0,
		charName = "Alistar",
		hard_cc = true,
		delay = 0.25,
		type = "circular",
		range = 0,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false,
		radius = 365,
		speedss = math.huge
	},
	BandageToss = {
		slot = 0,
		charName = "Amumu",
		hard_cc = true,
		delay = 0.25,
		speedss = 2000,
		type = "linear",
		range = 1100,
		hitbox = true,
		aoe = false,
		cc = true,
		collision = true,
		radius = 80
	},
	AuraofDespair = {
		slot = 1,
		charName = "Amumu",
		delay = 0.307,
		no_dmg = true,
		type = "circular",
		range = 0,
		hitbox = false,
		aoe = true,
		cc = false,
		collision = false,
		radius = 300,
		speedss = math.huge
	},
	Tantrum = {
		slot = 2,
		charName = "Amumu",
		delay = 0.25,
		type = "circular",
		range = 0,
		hitbox = false,
		aoe = true,
		cc = false,
		collision = false,
		radius = 350,
		speedss = math.huge
	},
	CurseoftheSadMummy = {
		slot = 3,
		charName = "Amumu",
		hard_cc = true,
		delay = 0.25,
		type = "circular",
		range = 0,
		hitbox = false,
		aoe = true,
		cc = true,
		collision = false,
		radius = 550,
		speedss = math.huge
	},
	FlashFrost = {
		slot = 0,
		charName = "Anivia",
		hard_cc = true,
		delay = 0.25,
		speedss = 850,
		type = "linear",
		range = 1075,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false,
		radius = 110
	},
	Crystallize = {
		radius1 = 250,
		charName = "Anivia",
		radius2 = 75,
		delay = 0.25,
		slot = 1,
		no_dmg = true,
		type = "rectangle",
		range = 1000,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false,
		speedss = math.huge
	},
	GlacialStorm = {
		slot = 3,
		charName = "Anivia",
		delay = 0.25,
		type = "circular",
		range = 750,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false,
		radius = 400,
		speedss = math.huge
	},
	Incinerate = {
		slot = 1,
		charName = "Annie",
		angle = 50,
		delay = 0.25,
		type = "conic",
		range = 600,
		hitbox = false,
		aoe = true,
		cc = false,
		collision = false,
		speedss = math.huge
	},
	InfernalGuardian = {
		slot = 3,
		charName = "Annie",
		delay = 0.25,
		type = "circular",
		range = 600,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false,
		radius = 290,
		speedss = math.huge
	},
	Volley = {
		slot = 1,
		charName = "Ashe",
		angle = 57.5,
		delay = 0.25,
		physical = true,
		speedss = 1500,
		type = "conic",
		range = 1200,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = true,
		radius = 20
	},
	EnchantedCrystalArrow = {
		slot = 3,
		charName = "Ashe",
		hard_cc = true,
		delay = 0.25,
		speedss = 1600,
		type = "linear",
		range = 25000,
		hitbox = true,
		aoe = false,
		cc = true,
		collision = false,
		radius = 130
	},
	AurelionSolQ = {
		slot = 0,
		charName = "AurelionSol",
		hard_cc = true,
		delay = 0.25,
		speedss = 850,
		type = "linear",
		range = 1075,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false,
		radius = 210
	},
	AurelionSolE = {
		slot = 2,
		charName = "AurelionSol",
		delay = 0.25,
		no_dmg = true,
		speedss = 600,
		type = "linear",
		range = 7000,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false,
		radius = 80
	},
	AurelionSolR = {
		slot = 3,
		charName = "AurelionSol",
		delay = 0.35,
		speedss = 4500,
		type = "linear",
		range = 1500,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false,
		radius = 120
	},
	BardQ = {
		slot = 0,
		charName = "Bard",
		delay = 0.25,
		speedss = 1500,
		type = "linear",
		range = 950,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = true,
		radius = 60
	},
	BardW = {
		slot = 1,
		charName = "Bard",
		delay = 0.25,
		no_dmg = true,
		type = "circular",
		range = 800,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false,
		radius = 100,
		speedss = math.huge
	},
	BardR = {
		slot = 3,
		charName = "Bard",
		hard_cc = true,
		delay = 0.5,
		speedss = 2100,
		type = "circular",
		range = 3400,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false,
		radius = 350
	},
	BelvethQ = {
		slot = 0,
		charName = "Belveth",
		delay = 0,
		speedss = 1000,
		type = "linear",
		range = 400,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false,
		radius = 100
	},
	BelvethW = {
		slot = 1,
		charName = "Belveth",
		hard_cc = true,
		delay = 0.5,
		type = "linear",
		range = 660,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false,
		radius = 200,
		speedss = math.huge
	},
	BelvethE = {
		slot = 2,
		charName = "Belveth",
		delay = 0,
		type = "circular",
		range = 500,
		hitbox = true,
		aoe = false,
		cc = false,
		instant = true,
		collision = false,
		radius = 500,
		speedss = math.huge
	},
	BelvethR = {
		slot = 3,
		charName = "Belveth",
		delay = 1,
		type = "circular",
		range = 500,
		hitbox = false,
		aoe = true,
		cc = true,
		collision = false,
		radius = 500,
		speedss = math.huge
	},
	RocketGrab = {
		slot = 0,
		charName = "Blitzcrank",
		hard_cc = true,
		delay = 0.25,
		speedss = 1800,
		type = "linear",
		range = 925,
		hitbox = true,
		aoe = false,
		cc = true,
		collision = true,
		radius = 70
	},
	StaticField = {
		slot = 3,
		charName = "Blitzcrank",
		delay = 0.25,
		type = "circular",
		range = 0,
		hitbox = false,
		aoe = true,
		cc = true,
		collision = false,
		radius = 600,
		speedss = math.huge
	},
	BrandQ = {
		slot = 0,
		charName = "Brand",
		hard_cc = true,
		delay = 0.25,
		speedss = 1600,
		type = "linear",
		range = 1050,
		hitbox = true,
		aoe = false,
		cc = true,
		collision = true,
		radius = 60
	},
	BrandW = {
		slot = 1,
		charName = "Brand",
		delay = 0.85,
		type = "circular",
		range = 900,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false,
		radius = 250,
		speedss = math.huge
	},
	BraumQ = {
		slot = 0,
		charName = "Braum",
		delay = 0.25,
		speedss = 1700,
		type = "linear",
		range = 1000,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = true,
		radius = 60
	},
	BraumRWrapper = {
		slot = 3,
		charName = "Braum",
		delay = 0.5,
		speedss = 1400,
		type = "linear",
		range = 1250,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false,
		radius = 115
	},
	CaitlynQ = {
		slot = 0,
		charName = "Caitlyn",
		delay = 0.625,
		physical = true,
		speedss = 2200,
		type = "linear",
		range = 1500,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false,
		radius = 60
	},
	CaitlynQ2 = {
		slot = 0,
		charName = "Caitlyn",
		delay = 0.625,
		physical = true,
		speedss = 2200,
		type = "linear",
		range = 1500,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false,
		radius = 60
	},
	CaitlynW = {
		slot = 1,
		charName = "Caitlyn",
		hard_cc = true,
		delay = 0.25,
		no_dmg = true,
		type = "circular",
		range = 800,
		hitbox = true,
		aoe = false,
		cc = true,
		collision = false,
		radius = 75,
		speedss = math.huge
	},
	CaitlynE = {
		slot = 2,
		charName = "Caitlyn",
		delay = 0.25,
		speedss = 1500,
		type = "linear",
		range = 750,
		hitbox = true,
		aoe = false,
		cc = true,
		collision = true,
		radius = 60
	},
	CamilleW = {
		slot = 1,
		charName = "Camille",
		angle = 80,
		delay = 0.75,
		physical = true,
		type = "conic",
		range = 610,
		hitbox = false,
		aoe = true,
		cc = true,
		collision = false,
		speedss = math.huge
	},
	CamilleE = {
		slot = 2,
		charName = "Camille",
		hard_cc = true,
		delay = 0.25,
		physical = true,
		speedss = 1900,
		type = "linear",
		range = 800,
		hitbox = true,
		aoe = false,
		cc = true,
		collision = false,
		radius = 60
	},
	CassiopeiaQ = {
		slot = 0,
		charName = "Cassiopeia",
		delay = 0.4,
		type = "circular",
		range = 850,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false,
		radius = 150,
		speedss = math.huge
	},
	CassiopeiaW = {
		slot = 1,
		charName = "Cassiopeia",
		delay = 0.25,
		no_dmg = true,
		type = "circular",
		range = 800,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false,
		radius = 160,
		speedss = math.huge
	},
	CassiopeiaR = {
		slot = 3,
		charName = "Cassiopeia",
		hard_cc = true,
		delay = 0.5,
		angle = 80,
		type = "conic",
		range = 825,
		hitbox = false,
		aoe = true,
		cc = true,
		collision = false,
		speedss = math.huge
	},
	Rupture = {
		slot = 0,
		charName = "Chogath",
		hard_cc = true,
		delay = 0.5,
		type = "circular",
		range = 950,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false,
		radius = 250,
		speedss = math.huge
	},
	FeralScream = {
		slot = 1,
		charName = "Chogath",
		angle = 60,
		delay = 0.5,
		type = "conic",
		range = 650,
		hitbox = false,
		aoe = true,
		cc = true,
		collision = false,
		speedss = math.huge
	},
	PhosphorusBomb = {
		slot = 0,
		charName = "Corki",
		delay = 0.25,
		speedss = 1000,
		type = "circular",
		range = 825,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false,
		radius = 250
	},
	CarpetBomb = {
		slot = 1,
		charName = "Corki",
		delay = 0,
		speedss = 650,
		type = "linear",
		range = 600,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false,
		radius = 100
	},
	CarpetBombMega = {
		slot = 1,
		charName = "Corki",
		delay = 0,
		speedss = 1500,
		type = "linear",
		range = 1800,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false,
		radius = 100
	},
	GGun = {
		slot = 2,
		charName = "Corki",
		angle = 35,
		delay = 0.25,
		no_dmg = true,
		type = "conic",
		range = 600,
		hitbox = false,
		aoe = true,
		cc = false,
		collision = false,
		speedss = math.huge
	},
	MissileBarrageMissile = {
		slot = 3,
		charName = "Corki",
		delay = 0.175,
		speedss = 1950,
		type = "linear",
		range = 1225,
		hitbox = true,
		aoe = false,
		cc = false,
		collision = true,
		radius = 35
	},
	MissileBarrageMissile2 = {
		slot = 3,
		charName = "Corki",
		delay = 0.175,
		speedss = 1950,
		type = "linear",
		range = 1225,
		hitbox = true,
		aoe = false,
		cc = false,
		collision = true,
		radius = 35
	},
	DariusCleave = {
		slot = 0,
		charName = "Darius",
		delay = 0.75,
		physical = true,
		type = "circular",
		range = 0,
		hitbox = false,
		aoe = true,
		cc = false,
		collision = false,
		radius = 425,
		speedss = math.huge
	},
	DariusAxeGrabCone = {
		slot = 2,
		charName = "Darius",
		hard_cc = true,
		delay = 0.25,
		angle = 50,
		type = "conic",
		range = 535,
		hitbox = false,
		aoe = true,
		cc = true,
		collision = false,
		speedss = math.huge
	},
	DianaQ = {
		slot = 0,
		charName = "Diana",
		delay = 0.25,
		speedss = 1400,
		type = "arc",
		range = 900,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false,
		radius = 205
	},
	DianaR = {
		slot = 3,
		charName = "Diana",
		hard_cc = true,
		delay = 0.25,
		type = "circular",
		range = 600,
		hitbox = false,
		aoe = true,
		cc = true,
		collision = false,
		radius = 600,
		speedss = math.huge
	},
	DrMundoQ = {
		slot = 0,
		charName = "DrMundo",
		delay = 0.25,
		speedss = 2000,
		type = "linear",
		range = 975,
		hitbox = true,
		aoe = false,
		cc = true,
		collision = true,
		radius = 60
	},
	DrMundoW = {
		slot = 1,
		charName = "DrMundo",
		delay = 0,
		type = "circular",
		range = 500,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false,
		radius = 0,
		speedss = math.huge
	},
	DrMundoWRecast = {
		slot = 1,
		charName = "DrMundo",
		delay = 0.25,
		type = "circular",
		range = 500,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false,
		radius = 0,
		speedss = math.huge
	},
	DrMundoE = {
		slot = 2,
		charName = "DrMundo",
		delay = 0,
		speedss = 2000,
		type = "linear",
		range = 800,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false,
		radius = 60
	},
	DrMundoEAttack = {
		slot = 2,
		charName = "DrMundo",
		delay = 0,
		speedss = 2000,
		type = "linear",
		range = 800,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false,
		radius = 60
	},
	FakeCorpseMissile = {
		slot = 2,
		charName = "DrMundo",
		delay = 0,
		speedss = 2000,
		type = "linear",
		range = 800,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false,
		radius = 60
	},
	DravenDoubleShot = {
		slot = 2,
		charName = "Draven",
		hard_cc = true,
		delay = 0.25,
		physical = true,
		speedss = 1400,
		type = "linear",
		range = 1050,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false,
		radius = 120
	},
	DravenRCast = {
		slot = 3,
		charName = "Draven",
		delay = 0.5,
		physical = true,
		speedss = 2000,
		type = "linear",
		range = 25000,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false,
		radius = 130
	},
	DravenRDoublecast = {
		slot = 3,
		charName = "Draven",
		delay = 0.5,
		physical = true,
		speedss = 2000,
		type = "linear",
		range = 25000,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false,
		radius = 130
	},
	EkkoQ = {
		slot = 0,
		charName = "Ekko",
		delay = 0.25,
		speedss = 1650,
		type = "linear",
		range = 1075,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false,
		radius = 135
	},
	EkkoW = {
		slot = 1,
		charName = "Ekko",
		delay = 3.75,
		no_dmg = true,
		speedss = 1650,
		type = "circular",
		range = 1600,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false,
		radius = 400
	},
	EkkoR = {
		slot = 3,
		charName = "Ekko",
		delay = 0.25,
		speedss = 1650,
		type = "circular",
		range = 1600,
		hitbox = false,
		aoe = true,
		cc = false,
		instant = true,
		collision = false,
		radius = 375
	},
	EliseHumanE = {
		slot = 2,
		charName = "Elise",
		hard_cc = true,
		delay = 0.25,
		speedss = 1600,
		type = "linear",
		range = 1075,
		hitbox = true,
		aoe = false,
		cc = true,
		collision = true,
		radius = 55
	},
	EvelynnQ = {
		slot = 0,
		charName = "Evelynn",
		delay = 0.25,
		speedss = 2200,
		type = "linear",
		range = 800,
		hitbox = true,
		aoe = false,
		cc = false,
		collision = true,
		radius = 35
	},
	EvelynnR = {
		slot = 3,
		charName = "Evelynn",
		angle = 180,
		delay = 0.35,
		type = "conic",
		range = 450,
		hitbox = false,
		aoe = true,
		cc = false,
		collision = false,
		speedss = math.huge
	},
	EzrealQ = {
		slot = 0,
		charName = "Ezreal",
		delay = 0.25,
		physical = true,
		speedss = 2000,
		type = "linear",
		range = 1150,
		hitbox = true,
		aoe = false,
		cc = false,
		collision = true,
		radius = 80
	},
	EzrealW = {
		slot = 1,
		charName = "Ezreal",
		delay = 0.25,
		no_dmg = true,
		speedss = 1550,
		type = "linear",
		range = 1000,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false,
		radius = 80
	},
	EzrealR = {
		slot = 3,
		charName = "Ezreal",
		delay = 1,
		speedss = 2000,
		type = "linear",
		range = 25000,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false,
		radius = 160
	},
	FiddleSticksE = {
		slot = 2,
		charName = "FiddleSticks",
		delay = 0.4,
		type = "linear",
		range = 850,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false,
		radius = 200,
		speedss = math.huge
	},
	FiddleSticksR = {
		slot = 3,
		charName = "FiddleSticks",
		delay = 2,
		type = "circular",
		range = 800,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false,
		radius = 600,
		speedss = math.huge
	},
	FioraQ = {
		slot = 0,
		charName = "Fiora",
		delay = 0,
		physical = true,
		speedss = 800,
		type = "linear",
		range = 400,
		hitbox = true,
		aoe = false,
		cc = false,
		collision = false,
		radius = 50
	},
	FioraW = {
		slot = 1,
		charName = "Fiora",
		delay = 0.75,
		type = "linear",
		range = 750,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false,
		radius = 85,
		speedss = math.huge
	},
	FizzR = {
		slot = 3,
		charName = "Fizz",
		delay = 0.25,
		speedss = 1300,
		type = "linear",
		range = 1300,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false,
		radius = 120
	},
	GalioQ = {
		slot = 0,
		charName = "Galio",
		delay = 0.25,
		speedss = 1150,
		type = "arc",
		range = 825,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false,
		radius = 150
	},
	GalioE = {
		slot = 2,
		charName = "Galio",
		hard_cc = true,
		delay = 0.45,
		speedss = 1400,
		type = "linear",
		range = 650,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false,
		radius = 160
	},
	GalioR = {
		slot = 3,
		charName = "Galio",
		hard_cc = true,
		delay = 2.75,
		type = "circular",
		range = 5500,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false,
		radius = 500,
		speedss = math.huge
	},
	GangplankE = {
		slot = 2,
		charName = "Gangplank",
		delay = 0.25,
		physical = true,
		type = "circular",
		range = 1000,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false,
		radius = 400,
		speedss = math.huge
	},
	GangplankR = {
		slot = 3,
		charName = "Gangplank",
		delay = 0.25,
		type = "circular",
		range = 25000,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false,
		radius = 600,
		speedss = math.huge
	},
	GnarQ = {
		slot = 0,
		charName = "Gnar",
		delay = 0.25,
		physical = true,
		speedss = 1700,
		type = "linear",
		range = 1100,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false,
		radius = 55
	},
	GnarQReturn = {
		slot = 0,
		charName = "Gnar",
		delay = 0.25,
		physical = true,
		speedss = 1700,
		type = "linear",
		range = 3000,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false,
		radius = 70
	},
	GnarE = {
		slot = 2,
		charName = "Gnar",
		delay = 0.25,
		physical = true,
		speedss = 900,
		type = "circular",
		range = 475,
		hitbox = true,
		aoe = false,
		cc = true,
		collision = false,
		radius = 160
	},
	GnarBigQ = {
		slot = 0,
		charName = "Gnar",
		delay = 0.5,
		physical = true,
		speedss = 2100,
		type = "linear",
		range = 1100,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = true,
		radius = 90
	},
	GnarBigW = {
		slot = 1,
		charName = "Gnar",
		hard_cc = true,
		delay = 0.6,
		physical = true,
		type = "linear",
		range = 550,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false,
		radius = 100,
		speedss = math.huge
	},
	GnarBigE = {
		slot = 2,
		charName = "Gnar",
		delay = 0.25,
		physical = true,
		speedss = 800,
		type = "circular",
		range = 600,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false,
		radius = 375
	},
	GnarR = {
		slot = 3,
		charName = "Gnar",
		hard_cc = true,
		delay = 0.25,
		physical = true,
		type = "circular",
		range = 475,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false,
		radius = 475,
		speedss = math.huge
	},
	GragasQ = {
		slot = 0,
		charName = "Gragas",
		delay = 0.25,
		speedss = 1000,
		type = "circular",
		range = 850,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false,
		radius = 250
	},
	GragasE = {
		slot = 2,
		charName = "Gragas",
		hard_cc = true,
		delay = 0.25,
		speedss = 900,
		type = "linear",
		range = 600,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = true,
		radius = 170
	},
	GragasR = {
		slot = 3,
		charName = "Gragas",
		hard_cc = true,
		delay = 0.25,
		speedss = 1800,
		type = "circular",
		range = 1000,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false,
		radius = 400
	},
	GravesQLineSpell = {
		slot = 0,
		charName = "Graves",
		delay = 0.25,
		physical = true,
		speedss = 3700,
		type = "linear",
		range = 925,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false,
		radius = 40
	},
	GravesQLineMis = {
		radius1 = 250,
		charName = "Graves",
		radius2 = 100,
		delay = 0.25,
		slot = 0,
		physical = true,
		type = "rectangle",
		range = 925,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false,
		speedss = math.huge
	},
	GravesQReturn = {
		slot = 0,
		charName = "Graves",
		delay = 0.25,
		physical = true,
		speedss = 1850,
		type = "linear",
		range = 925,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false,
		radius = 40
	},
	GravesSmokeGrenade = {
		slot = 1,
		charName = "Graves",
		delay = 0.25,
		speedss = 1450,
		type = "circular",
		range = 950,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false,
		radius = 250
	},
	GravesChargeShot = {
		slot = 3,
		charName = "Graves",
		delay = 0.25,
		physical = true,
		speedss = 1950,
		type = "linear",
		range = 1000,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false,
		radius = 100
	},
	GravesChargeShotFxMissile = {
		slot = 3,
		charName = "Graves",
		angle = 80,
		delay = 0.3,
		physical = true,
		type = "conic",
		range = 800,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false,
		speedss = math.huge
	},
	GwenQ = {
		slot = 0,
		charName = "Gwen",
		angle = 80,
		delay = 0.25,
		physical = false,
		type = "conic",
		range = 600,
		hitbox = false,
		aoe = true,
		cc = false,
		instant = true,
		collision = false,
		speedss = math.huge
	},
	GwenR = {
		slot = 3,
		charName = "Gwen",
		delay = 0.25,
		physical = false,
		speedss = 1800,
		type = "linear",
		range = 1350,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false,
		radius = 80
	},
	GwenRRecast = {
		slot = 3,
		charName = "Gwen",
		delay = 0.25,
		physical = false,
		speedss = 1800,
		type = "linear",
		range = 1350,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false,
		radius = 80
	},
	HecarimRapidSlash = {
		slot = 0,
		charName = "Hecarim",
		delay = 0,
		physical = true,
		type = "circular",
		range = 0,
		hitbox = false,
		aoe = true,
		cc = false,
		collision = false,
		radius = 350,
		speedss = math.huge
	},
	HecarimUlt = {
		slot = 3,
		charName = "Hecarim",
		hard_cc = true,
		delay = 0.01,
		physical = true,
		speedss = 1200,
		type = "linear",
		range = 1000,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false,
		radius = 210
	},
	HeimerdingerQ = {
		slot = 0,
		charName = "Heimerdinger",
		delay = 0.25,
		type = "circular",
		range = 450,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false,
		radius = 55,
		speedss = math.huge
	},
	HeimerdingerW = {
		slot = 1,
		charName = "Heimerdinger",
		delay = 0.25,
		speedss = 2050,
		type = "linear",
		range = 1325,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = true,
		radius = 30
	},
	HeimerdingerE = {
		slot = 2,
		charName = "Heimerdinger",
		hard_cc = true,
		delay = 0.25,
		speedss = 1200,
		type = "circular",
		range = 970,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false,
		radius = 250
	},
	HeimerdingerEUlt = {
		slot = 2,
		charName = "Heimerdinger",
		hard_cc = true,
		delay = 0.25,
		speedss = 1200,
		type = "circular",
		range = 970,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false,
		radius = 250
	},
	IllaoiQ = {
		slot = 0,
		charName = "Illaoi",
		delay = 0.75,
		physical = true,
		type = "linear",
		range = 850,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false,
		radius = 100,
		speedss = math.huge
	},
	IllaoiE = {
		slot = 2,
		charName = "Illaoi",
		delay = 0.25,
		no_dmg = true,
		speedss = 1800,
		type = "linear",
		range = 900,
		hitbox = true,
		aoe = false,
		cc = false,
		collision = true,
		radius = 45
	},
	IllaoiR = {
		slot = 3,
		charName = "Illaoi",
		delay = 0.5,
		physical = true,
		type = "circular",
		range = 0,
		hitbox = false,
		aoe = true,
		cc = false,
		collision = false,
		radius = 450,
		speedss = math.huge
	},
	IreliaW2 = {
		slot = 1,
		charName = "Irelia",
		delay = 0,
		physical = true,
		type = "circular",
		range = 0,
		hitbox = false,
		aoe = true,
		cc = false,
		collision = false,
		radius = 275,
		speedss = math.huge
	},
	IreliaW2 = {
		slot = 1,
		charName = "Irelia",
		delay = 0.25,
		physical = true,
		type = "linear",
		range = 825,
		hitbox = false,
		aoe = true,
		cc = false,
		collision = false,
		radius = 90,
		speedss = math.huge
	},
	IreliaE = {
		slot = 2,
		charName = "Irelia",
		hard_cc = true,
		delay = 0,
		speedss = 2000,
		type = "circular",
		range = 850,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false,
		radius = 90
	},
	IreliaE2 = {
		slot = 2,
		charName = "Irelia",
		hard_cc = true,
		delay = 0,
		speedss = 2000,
		type = "circular",
		range = 850,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false,
		radius = 90
	},
	IreliaR = {
		slot = 3,
		charName = "Irelia",
		delay = 0.4,
		speedss = 2000,
		type = "linear",
		range = 1000,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false,
		radius = 160
	},
	IvernQ = {
		slot = 0,
		charName = "Ivern",
		hard_cc = true,
		delay = 0.25,
		speedss = 1300,
		type = "linear",
		range = 1075,
		hitbox = true,
		aoe = false,
		cc = true,
		collision = true,
		radius = 50
	},
	IvernW = {
		slot = 1,
		charName = "Ivern",
		delay = 0.25,
		no_dmg = true,
		type = "circular",
		range = 800,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false,
		radius = 150,
		speedss = math.huge
	},
	HowlingGale = {
		slot = 0,
		charName = "Janna",
		hard_cc = true,
		delay = 0,
		speedss = 1167,
		type = "linear",
		range = 1750,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false,
		radius = 100
	},
	ReapTheWhirlwind = {
		slot = 3,
		charName = "Janna",
		delay = 0.001,
		no_dmg = true,
		type = "circular",
		range = 0,
		hitbox = false,
		aoe = true,
		cc = true,
		collision = false,
		radius = 725,
		speedss = math.huge
	},
	JarvanIVDragonStrike = {
		slot = 0,
		charName = "JarvanIV",
		delay = 0.4,
		physical = true,
		type = "linear",
		range = 770,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false,
		radius = 60,
		speedss = math.huge
	},
	JarvanIVGoldenAegis = {
		slot = 1,
		charName = "JarvanIV",
		delay = 0.125,
		no_dmg = true,
		type = "circular",
		range = 0,
		hitbox = false,
		aoe = true,
		cc = true,
		collision = false,
		radius = 625,
		speedss = math.huge
	},
	JarvanIVDemacianStandard = {
		slot = 2,
		charName = "JarvanIV",
		delay = 0,
		speedss = 3440,
		type = "circular",
		range = 860,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false,
		radius = 175
	},
	JaxCounterStrike = {
		slot = 2,
		charName = "Jax",
		hard_cc = true,
		delay = 1.4,
		type = "circular",
		range = 0,
		hitbox = false,
		aoe = true,
		cc = true,
		collision = false,
		radius = 300,
		speedss = math.huge
	},
	JayceShockBlast = {
		slot = 0,
		charName = "Jayce",
		delay = 0.214,
		physical = true,
		speedss = 1450,
		type = "linear",
		range = 1050,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = true,
		radius = 75
	},
	JayceShockBlastWallMis = {
		slot = 0,
		charName = "Jayce",
		delay = 0.214,
		physical = true,
		speedss = 1890,
		type = "linear",
		range = 2030,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = true,
		radius = 105
	},
	JayceStaticField = {
		slot = 1,
		charName = "Jayce",
		delay = 0.25,
		physical = true,
		type = "circular",
		range = 0,
		hitbox = false,
		aoe = true,
		cc = false,
		collision = false,
		radius = 285,
		speedss = math.huge
	},
	JhinW = {
		slot = 1,
		charName = "Jhin",
		hard_cc = true,
		delay = 0.75,
		physical = true,
		speedss = 5000,
		type = "linear",
		range = 3000,
		hitbox = true,
		aoe = false,
		cc = true,
		collision = false,
		radius = 40
	},
	JhinE = {
		slot = 2,
		charName = "Jhin",
		delay = 0.25,
		speedss = 1650,
		type = "circular",
		range = 750,
		hitbox = true,
		aoe = false,
		cc = true,
		collision = false,
		radius = 140
	},
	JhinRShot = {
		slot = 3,
		charName = "Jhin",
		delay = 0.25,
		physical = true,
		speedss = 5000,
		type = "linear",
		range = 3500,
		hitbox = true,
		aoe = false,
		cc = true,
		collision = false,
		radius = 80
	},
	JinxW = {
		slot = 1,
		charName = "Jinx",
		delay = 0.6,
		physical = true,
		speedss = 3200,
		type = "linear",
		range = 1450,
		hitbox = true,
		aoe = false,
		cc = true,
		collision = true,
		radius = 50
	},
	JinxE = {
		slot = 2,
		charName = "Jinx",
		delay = 1.5,
		speedss = 2570,
		type = "circular",
		range = 900,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false,
		radius = 100
	},
	JinxR = {
		slot = 3,
		charName = "Jinx",
		delay = 0.6,
		physical = true,
		speedss = 1700,
		type = "linear",
		range = 25000,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false,
		radius = 110
	},
	KatarinaW = {
		slot = 1,
		charName = "Katarina",
		delay = 1.25,
		no_dmg = true,
		type = "circular",
		range = 0,
		hitbox = false,
		aoe = true,
		cc = false,
		collision = false,
		radius = 340,
		speedss = math.huge
	},
	KatarinaE = {
		slot = 2,
		charName = "Katarina",
		delay = 0.15,
		type = "circular",
		range = 725,
		hitbox = true,
		aoe = false,
		cc = false,
		collision = false,
		radius = 150,
		speedss = math.huge
	},
	KatarinaR = {
		slot = 3,
		charName = "Katarina",
		delay = 0,
		type = "circular",
		range = 0,
		hitbox = false,
		aoe = true,
		cc = false,
		collision = false,
		radius = 550,
		speedss = math.huge
	},
	KatarinaBasicAttack = {
		slot = 64,
		charName = "Katarina",
		delay = 0,
		type = "targeted",
		range = 125,
		hitbox = false,
		aoe = false,
		cc = false,
		collision = false,
		radius = 0,
		speedss = math.huge,
		isAA = true
	},
	KalistaMysticShot = {
		slot = 0,
		charName = "Kalista",
		delay = 0.35,
		physical = true,
		speedss = 2100,
		type = "linear",
		range = 1150,
		hitbox = true,
		aoe = false,
		cc = false,
		collision = true,
		radius = 35
	},
	KarmaQ = {
		slot = 0,
		charName = "Karma",
		delay = 0.25,
		speedss = 1750,
		type = "linear",
		range = 950,
		hitbox = true,
		aoe = false,
		cc = true,
		collision = true,
		radius = 80
	},
	KarmaQMantra = {
		slot = 0,
		charName = "Karma",
		delay = 0.25,
		speedss = 1750,
		type = "linear",
		range = 950,
		hitbox = true,
		aoe = false,
		cc = true,
		collision = true,
		radius = 80
	},
	KarthusLayWasteA1 = {
		slot = 0,
		charName = "Karthus",
		delay = 0.5,
		type = "circular",
		range = 875,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false,
		radius = 200,
		speedss = math.huge
	},
	KarthusLayWasteA2 = {
		slot = 0,
		charName = "Karthus",
		delay = 0.5,
		type = "circular",
		range = 875,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false,
		radius = 200,
		speedss = math.huge
	},
	KarthusLayWasteA3 = {
		slot = 0,
		charName = "Karthus",
		delay = 0.5,
		type = "circular",
		range = 875,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false,
		radius = 200,
		speedss = math.huge
	},
	KarthusWallOfPain = {
		radius1 = 470,
		charName = "Karthus",
		radius2 = 75,
		delay = 0.25,
		slot = 1,
		no_dmg = true,
		type = "rectangle",
		range = 1000,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false,
		speedss = math.huge
	},
	ForcePulse = {
		slot = 2,
		charName = "Kassadin",
		angle = 80,
		delay = 0.25,
		type = "conic",
		range = 600,
		hitbox = false,
		aoe = true,
		cc = true,
		collision = false,
		speedss = math.huge
	},
	Riftwalk = {
		slot = 3,
		charName = "Kassadin",
		delay = 0.25,
		type = "circular",
		range = 500,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false,
		radius = 300,
		speedss = math.huge
	},
	KayleQ = {
		slot = 0,
		charName = "Kayle",
		delay = 1,
		type = "linear",
		range = 950,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = true,
		radius = 100,
		speedss = math.huge
	},
	KaynQ = {
		slot = 0,
		charName = "Kayn",
		delay = 0.15,
		physical = true,
		type = "circular",
		range = 0,
		hitbox = false,
		aoe = true,
		cc = false,
		collision = false,
		radius = 350,
		speedss = math.huge
	},
	KaynW = {
		slot = 1,
		charName = "Kayn",
		delay = 0.55,
		physical = true,
		type = "linear",
		range = 700,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false,
		radius = 90,
		speedss = math.huge
	},
	KaynAssW = {
		slot = 1,
		charName = "Kayn",
		delay = 0.55,
		physical = true,
		type = "linear",
		range = 700,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false,
		radius = 90,
		speedss = math.huge
	},
	KennenShurikenHurlMissile1 = {
		slot = 0,
		charName = "Kennen",
		delay = 0.175,
		speedss = 1650,
		type = "linear",
		range = 1050,
		hitbox = true,
		aoe = false,
		cc = false,
		collision = true,
		radius = 45
	},
	KennenShurikenStorm = {
		slot = 3,
		charName = "Kennen",
		delay = 0.25,
		type = "circular",
		range = 0,
		hitbox = false,
		aoe = true,
		cc = false,
		collision = false,
		radius = 550,
		speedss = math.huge
	},
	KaisaW = {
		slot = 1,
		charName = "Kaisa",
		delay = 0.4,
		speedss = 1750,
		type = "linear",
		range = 3000,
		hitbox = true,
		aoe = false,
		cc = false,
		collision = true,
		radius = 65
	},
	KhazixW = {
		slot = 1,
		charName = "Khazix",
		delay = 0.25,
		physical = true,
		speedss = 1650,
		type = "linear",
		range = 1000,
		hitbox = true,
		aoe = false,
		cc = false,
		collision = true,
		radius = 60
	},
	KhazixWLong = {
		slot = 1,
		charName = "Khazix",
		delay = 0.25,
		physical = true,
		speedss = 1650,
		type = "linear",
		range = 1000,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = true,
		radius = 70
	},
	KhazixE = {
		slot = 2,
		charName = "Khazix",
		delay = 0.25,
		physical = true,
		speedss = 1400,
		type = "circular",
		range = 700,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false,
		radius = 320
	},
	KhazixELong = {
		slot = 2,
		charName = "Khazix",
		delay = 0.25,
		physical = true,
		speedss = 1400,
		type = "circular",
		range = 900,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false,
		radius = 320
	},
	KindredQ = {
		slot = 0,
		charName = "Kindred",
		delay = 0.01,
		physical = true,
		speedss = 1360,
		type = "linear",
		range = 340,
		hitbox = true,
		aoe = false,
		cc = false,
		collision = false,
		radius = 55
	},
	KindredR = {
		slot = 3,
		charName = "Kindred",
		delay = 0.25,
		no_dmg = true,
		type = "circular",
		range = 0,
		hitbox = false,
		aoe = true,
		cc = false,
		collision = false,
		radius = 500,
		speedss = math.huge
	},
	KledQ = {
		slot = 0,
		charName = "Kled",
		delay = 0.25,
		physical = true,
		speedss = 1400,
		type = "linear",
		range = 800,
		hitbox = true,
		aoe = false,
		cc = true,
		collision = true,
		radius = 60
	},
	KledEDash = {
		slot = 2,
		charName = "Kled",
		delay = 0,
		physical = true,
		speedss = 1100,
		type = "linear",
		range = 550,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false,
		radius = 90
	},
	KledRiderQ = {
		slot = 0,
		charName = "Kled",
		angle = 25,
		delay = 0.25,
		physical = true,
		type = "conic",
		range = 700,
		hitbox = false,
		aoe = true,
		cc = false,
		collision = false,
		speedss = math.huge
	},
	KogMawQ = {
		slot = 0,
		charName = "KogMaw",
		delay = 0.25,
		speedss = 1600,
		type = "linear",
		range = 1175,
		hitbox = true,
		aoe = false,
		cc = false,
		collision = true,
		radius = 60
	},
	KogMawVoidOoze = {
		slot = 2,
		charName = "KogMaw",
		delay = 0.25,
		speedss = 1350,
		type = "linear",
		range = 1280,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false,
		radius = 115
	},
	KogMawLivingArtillery = {
		slot = 3,
		charName = "KogMaw",
		delay = 0.85,
		type = "circular",
		range = 1800,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false,
		radius = 200,
		speedss = math.huge
	},
	LeblancW = {
		slot = 1,
		charName = "Leblanc",
		delay = 0.25,
		speedss = 1600,
		type = "circular",
		range = 600,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false,
		radius = 260
	},
	LeblancE = {
		slot = 2,
		charName = "Leblanc",
		delay = 0.25,
		speedss = 1750,
		type = "linear",
		range = 925,
		hitbox = true,
		aoe = false,
		cc = true,
		collision = true,
		radius = 55
	},
	LeblancRW = {
		slot = 1,
		charName = "Leblanc",
		delay = 0.25,
		speedss = 1600,
		type = "circular",
		range = 600,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false,
		radius = 260
	},
	LeblancRE = {
		slot = 2,
		charName = "Leblanc",
		delay = 0.25,
		speedss = 1750,
		type = "linear",
		range = 925,
		hitbox = true,
		aoe = false,
		cc = true,
		collision = true,
		radius = 55
	},
	BlinkMonkQOne = {
		slot = 0,
		charName = "LeeSin",
		delay = 0.25,
		physical = true,
		speedss = 1750,
		type = "linear",
		range = 1100,
		hitbox = true,
		aoe = false,
		cc = false,
		collision = true,
		radius = 50
	},
	BlinkMonkEOne = {
		slot = 2,
		charName = "LeeSin",
		delay = 0.25,
		physical = true,
		type = "circular",
		range = 0,
		hitbox = false,
		aoe = true,
		cc = false,
		collision = false,
		radius = 350,
		speedss = math.huge
	},
	LeonaZenithBladeMissile = {
		slot = 2,
		charName = "Leona",
		hard_cc = true,
		delay = 0.25,
		speedss = 2000,
		type = "linear",
		range = 875,
		hitbox = true,
		aoe = false,
		cc = true,
		collision = false,
		radius = 70
	},
	LeonaSolarFlare = {
		slot = 3,
		charName = "Leona",
		hard_cc = true,
		delay = 0.625,
		type = "circular",
		range = 1200,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false,
		radius = 250,
		speedss = math.huge
	},
	LissandraQ = {
		slot = 0,
		charName = "Lissandra",
		delay = 0.251,
		speedss = 2400,
		type = "linear",
		range = 825,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false,
		radius = 65
	},
	LissandraW = {
		slot = 1,
		charName = "Lissandra",
		hard_cc = true,
		delay = 0.25,
		type = "circular",
		range = 0,
		hitbox = false,
		aoe = true,
		cc = true,
		collision = false,
		radius = 450,
		speedss = math.huge
	},
	LissandraE = {
		slot = 2,
		charName = "Lissandra",
		delay = 0.25,
		speedss = 850,
		type = "linear",
		range = 1050,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false,
		radius = 100
	},
	LilliaQ = {
		slot = 0,
		charName = "Lillia",
		delay = 0.25,
		type = "circular",
		range = 480,
		hitbox = false,
		aoe = true,
		cc = false,
		collision = false,
		radius = 0,
		speedss = math.huge
	},
	LilliaW = {
		slot = 1,
		charName = "Lillia",
		delay = 0.25,
		type = "circular",
		range = 500,
		hitbox = false,
		aoe = true,
		cc = false,
		collision = false,
		radius = 250,
		speedss = math.huge
	},
	LilliaE = {
		slot = 2,
		charName = "Lillia",
		delay = 0.35,
		speedss = 1400,
		type = "circular",
		range = 756,
		hitbox = false,
		aoe = true,
		cc = true,
		collision = true,
		radius = 150
	},
	LilliaERollingMissile = {
		slot = 2,
		charName = "Lillia",
		delay = 0.35,
		speedss = 1400,
		type = "linear",
		range = 20000,
		hitbox = false,
		aoe = true,
		cc = true,
		collision = true,
		radius = 85
	},
	LucianQ = {
		slot = 0,
		charName = "Lucian",
		delay = 0.5,
		physical = true,
		type = "linear",
		range = 900,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false,
		radius = 65,
		speedss = math.huge
	},
	LucianW = {
		slot = 1,
		charName = "Lucian",
		delay = 0.25,
		speedss = 1600,
		type = "linear",
		range = 900,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false,
		radius = 65
	},
	LucianR = {
		slot = 3,
		charName = "Lucian",
		delay = 0.01,
		physical = true,
		speedss = 2800,
		type = "linear",
		range = 1200,
		hitbox = true,
		aoe = false,
		cc = false,
		collision = true,
		radius = 75
	},
	PykeQMelee = {
		slot = 0,
		charName = "Pyke",
		delay = 0.25,
		physical = true,
		type = "linear",
		range = 400,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false,
		radius = 70,
		speedss = math.huge
	},
	PykeQRange = {
		slot = 0,
		charName = "Pyke",
		hard_cc = true,
		delay = 0.2,
		physical = true,
		speedss = 2000,
		type = "linear",
		range = 1100,
		hitbox = true,
		aoe = false,
		cc = true,
		collision = true,
		radius = 70
	},
	PykeR = {
		radius1 = 300,
		charName = "Pyke",
		radius2 = 50,
		delay = 0.5,
		slot = 3,
		physical = true,
		type = "cross",
		range = 750,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false,
		speedss = math.huge
	},
	LuluQ = {
		slot = 0,
		charName = "Lulu",
		delay = 0.25,
		speedss = 1500,
		type = "linear",
		range = 925,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false,
		radius = 45
	},
	LuxLightBinding = {
		slot = 0,
		charName = "Lux",
		hard_cc = true,
		delay = 0.25,
		speedss = 1200,
		type = "linear",
		range = 1175,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = true,
		radius = 60
	},
	LuxPrismaticWave = {
		slot = 1,
		charName = "Lux",
		delay = 0.25,
		no_dmg = true,
		speedss = 1400,
		type = "linear",
		range = 1075,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false,
		radius = 120
	},
	LuxLightStrikeKugel = {
		slot = 2,
		charName = "Lux",
		delay = 0.25,
		speedss = 1300,
		type = "circular",
		range = 1000,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false,
		radius = 310
	},
	LuxMaliceCannon = {
		slot = 3,
		charName = "Lux",
		delay = 1,
		type = "linear",
		range = 3340,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false,
		radius = 115,
		speedss = math.huge
	},
	Landslide = {
		slot = 2,
		charName = "Malphite",
		delay = 0.242,
		type = "circular",
		range = 0,
		hitbox = false,
		aoe = true,
		cc = true,
		collision = false,
		radius = 200,
		speedss = math.huge
	},
	UFSlash = {
		slot = 3,
		charName = "Malphite",
		hard_cc = true,
		delay = 0,
		speedss = 2170,
		type = "circular",
		range = 1000,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false,
		radius = 300
	},
	MalzaharQ = {
		radius1 = 400,
		charName = "Malzahar",
		radius2 = 100,
		delay = 0.25,
		slot = 0,
		type = "rectangle",
		range = 900,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false,
		speedss = math.huge
	},
	MaokaiQ = {
		slot = 0,
		charName = "Maokai",
		delay = 0.375,
		speedss = 1600,
		type = "linear",
		range = 600,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false,
		radius = 150
	},
	MaokaiR = {
		slot = 3,
		charName = "Maokai",
		hard_cc = true,
		delay = 0.5,
		speedss = 450,
		type = "linear",
		range = 3000,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false,
		radius = 650
	},
	MissFortuneScattershot = {
		slot = 2,
		charName = "MissFortune",
		delay = 0.5,
		type = "circular",
		range = 1000,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false,
		radius = 400,
		speedss = math.huge
	},
	MissFortuneBulletTime = {
		slot = 3,
		charName = "MissFortune",
		angle = 40,
		delay = 0.001,
		physical = true,
		type = "conic",
		range = 1400,
		hitbox = false,
		aoe = true,
		cc = false,
		collision = false,
		speedss = math.huge
	},
	MordekaiserQ = {
		slot = 0,
		charName = "Mordekaiser",
		delay = 0.5,
		type = "linear",
		range = 625,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false,
		radius = 120,
		speedss = math.huge
	},
	MordekaiserE = {
		slot = 2,
		charName = "Mordekaiser",
		delay = 0.5,
		type = "linear",
		range = 900,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false,
		radius = 160,
		speedss = math.huge
	},
	MorganaQ = {
		slot = 0,
		charName = "Morgana",
		hard_cc = true,
		delay = 0.25,
		speedss = 1200,
		type = "linear",
		range = 1175,
		hitbox = true,
		aoe = false,
		cc = true,
		collision = true,
		radius = 60
	},
	MorganaW = {
		slot = 1,
		charName = "Morgana",
		delay = 0.25,
		type = "circular",
		range = 900,
		hitbox = true,
		aoe = true,
		cc = false,
		instant = true,
		collision = false,
		radius = 325,
		speedss = math.huge
	},
	NamiQ = {
		slot = 0,
		charName = "Nami",
		hard_cc = true,
		delay = 0.95,
		type = "circular",
		range = 875,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false,
		radius = 200,
		speedss = math.huge
	},
	NamiR = {
		slot = 3,
		charName = "Nami",
		hard_cc = true,
		delay = 0.5,
		speedss = 850,
		type = "linear",
		range = 2750,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false,
		radius = 215
	},
	NasusE = {
		slot = 2,
		charName = "Nasus",
		delay = 0.25,
		type = "circular",
		range = 650,
		hitbox = true,
		aoe = true,
		cc = false,
		instant = true,
		collision = false,
		radius = 400,
		speedss = math.huge
	},
	NautilusAnchorDrag = {
		slot = 0,
		charName = "Nautilus",
		hard_cc = true,
		delay = 0.25,
		speedss = 2000,
		type = "linear",
		range = 1100,
		hitbox = true,
		aoe = false,
		cc = true,
		collision = true,
		radius = 75
	},
	NautilusSplashZone = {
		slot = 2,
		charName = "Nautilus",
		delay = 0.25,
		type = "circular",
		range = 0,
		hitbox = false,
		aoe = true,
		cc = true,
		collision = false,
		radius = 600,
		speedss = math.huge
	},
	NeekoQ = {
		slot = 0,
		charName = "Neeko",
		delay = 0.25,
		speedss = 1800,
		type = "circular",
		range = 800,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false,
		radius = 220
	},
	NeekoE = {
		slot = 2,
		charName = "Neeko",
		hard_cc = true,
		delay = 0.25,
		speedss = 900,
		type = "linear",
		range = 1000,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false,
		radius = 80
	},
	JavelinToss = {
		slot = 0,
		charName = "Nidalee",
		delay = 0.25,
		speedss = 1300,
		type = "linear",
		range = 1500,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = true,
		radius = 45
	},
	Bushwhack = {
		slot = 1,
		charName = "Nidalee",
		delay = 0.25,
		type = "circular",
		range = 900,
		hitbox = true,
		aoe = false,
		cc = false,
		collision = true,
		radius = 85,
		speedss = math.huge
	},
	Pounce = {
		slot = 1,
		charName = "Nidalee",
		delay = 0.25,
		speedss = 1750,
		type = "circular",
		range = 750,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false,
		radius = 200
	},
	Swipe = {
		slot = 2,
		charName = "Nidalee",
		angle = 180,
		delay = 0.25,
		type = "conic",
		range = 300,
		hitbox = false,
		aoe = true,
		cc = false,
		collision = false,
		speedss = math.huge
	},
	NocturneDuskbringer = {
		slot = 0,
		charName = "Nocturne",
		delay = 0.25,
		physical = true,
		speedss = 1400,
		type = "linear",
		range = 1200,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false,
		radius = 60
	},
	NunuR = {
		slot = 3,
		charName = "Nunu",
		delay = 3.01,
		type = "circular",
		range = 0,
		hitbox = false,
		aoe = true,
		cc = true,
		instant = true,
		collision = false,
		radius = 650,
		speedss = math.huge
	},
	OlafAxeThrowCast = {
		slot = 0,
		charName = "Olaf",
		delay = 0.25,
		physical = true,
		speedss = 1550,
		type = "linear",
		range = 1000,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false,
		radius = 80
	},
	OrianaIzunaCommand = {
		slot = 0,
		charName = "Orianna",
		delay = 0.25,
		speedss = 1400,
		type = "linear",
		range = 825,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false,
		radius = 175
	},
	OrianaDissonanceCommand = {
		slot = 1,
		charName = "Orianna",
		delay = 0.25,
		type = "circular",
		range = 0,
		hitbox = false,
		aoe = true,
		cc = true,
		instant = true,
		collision = false,
		radius = 250,
		speedss = math.huge
	},
	OrianaRedactCommand = {
		slot = 2,
		charName = "Orianna",
		delay = 0.25,
		speedss = 1400,
		type = "linear",
		range = 1100,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false,
		radius = 55
	},
	OrianaDetonateCommand = {
		slot = 3,
		charName = "Orianna",
		hard_cc = true,
		delay = 0.5,
		type = "circular",
		range = 0,
		hitbox = false,
		aoe = true,
		cc = true,
		collision = false,
		radius = 325,
		speedss = math.huge
	},
	OrnnQ = {
		slot = 0,
		charName = "Ornn",
		delay = 0.3,
		physical = true,
		speedss = 2000,
		type = "linear",
		range = 800,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false,
		radius = 100
	},
	OrnnW = {
		slot = 1,
		charName = "Ornn",
		delay = 0.25,
		type = "linear",
		range = 550,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false,
		radius = 110,
		speedss = math.huge
	},
	OrnnE = {
		slot = 2,
		charName = "Ornn",
		delay = 0.35,
		physical = true,
		speedss = 1780,
		type = "linear",
		range = 800,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false,
		radius = 150
	},
	OrnnR = {
		slot = 3,
		charName = "Ornn",
		delay = 0.5,
		speedss = 1200,
		type = "linear",
		range = 2500,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false,
		radius = 225
	},
	OrnnRCharge = {
		slot = 3,
		charName = "Ornn",
		delay = 0.5,
		speedss = 1200,
		type = "linear",
		range = 2500,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false,
		radius = 225
	},
	OrnnRWave = {
		slot = 3,
		charName = "Ornn",
		delay = 0.5,
		speedss = 1200,
		type = "linear",
		range = 2500,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false,
		radius = 225
	},
	OrnnRWave2 = {
		slot = 3,
		charName = "Ornn",
		delay = 0.5,
		speedss = 1200,
		type = "linear",
		range = 2500,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false,
		radius = 225
	},
	PantheonQTap = {
		slot = 0,
		charName = "Pantheon",
		delay = 0.25,
		physical = true,
		type = "linear",
		range = 575,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false,
		radius = 80,
		speedss = math.huge
	},
	PantheonQMissile = {
		slot = 0,
		charName = "Pantheon",
		delay = 0.25,
		physical = true,
		speedss = 2700,
		type = "linear",
		range = 1000,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false,
		radius = 85
	},
	PantheonE = {
		slot = 2,
		charName = "Pantheon",
		angle = 80,
		delay = 0.25,
		type = "conic",
		range = 640,
		hitbox = false,
		aoe = true,
		cc = false,
		collision = false,
		speedss = math.huge
	},
	PantheonEShieldSlam = {
		slot = 2,
		charName = "Pantheon",
		angle = 80,
		delay = 0.25,
		physical = true,
		type = "conic",
		range = 640,
		hitbox = false,
		aoe = true,
		cc = false,
		collision = false,
		speedss = math.huge
	},
	PantheonR = {
		slot = 3,
		charName = "Pantheon",
		angle = 700,
		delay = 2.5,
		physical = true,
		type = "circular",
		range = 5500,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false,
		speedss = math.huge
	},
	PoppyQSpell = {
		slot = 0,
		charName = "Poppy",
		delay = 1.32,
		physical = true,
		type = "linear",
		range = 430,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false,
		radius = 85,
		speedss = math.huge
	},
	PoppyW = {
		slot = 1,
		charName = "Poppy",
		delay = 0,
		no_dmg = true,
		type = "circular",
		range = 0,
		hitbox = false,
		aoe = true,
		cc = false,
		collision = false,
		radius = 400,
		speedss = math.huge
	},
	PoppyRSpell = {
		slot = 3,
		charName = "Poppy",
		hard_cc = true,
		delay = 0.6,
		physical = true,
		speedss = 1600,
		type = "linear",
		range = 1900,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false,
		radius = 80
	},
	QiyanaQ = {
		slot = 0,
		charName = "Qiyana",
		delay = 0.25,
		physical = true,
		type = "linear",
		range = 500,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false,
		radius = 120,
		speedss = math.huge
	},
	QiyanaQ_Rock = {
		slot = 0,
		charName = "Qiyana",
		delay = 0.25,
		physical = true,
		speedss = 1600,
		type = "linear",
		range = 925,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false,
		radius = 70
	},
	QiyanaQ_Grass = {
		slot = 0,
		charName = "Qiyana",
		delay = 0.25,
		physical = true,
		speedss = 1600,
		type = "linear",
		range = 925,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false,
		radius = 70
	},
	QiyanaQ_Water = {
		slot = 0,
		charName = "Qiyana",
		delay = 0.25,
		physical = true,
		speedss = 1600,
		type = "linear",
		range = 925,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false,
		radius = 70
	},
	QiyanaR = {
		slot = 3,
		charName = "Qiyana",
		hard_cc = true,
		delay = 0.25,
		physical = true,
		speedss = 2000,
		type = "linear",
		range = 950,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = true,
		radius = 120
	},
	QuinnQ = {
		slot = 0,
		charName = "Quinn",
		delay = 0.25,
		physical = true,
		speedss = 1550,
		type = "linear",
		range = 1025,
		hitbox = true,
		aoe = false,
		cc = false,
		collision = true,
		radius = 50
	},
	RakanQ = {
		slot = 0,
		charName = "Rakan",
		delay = 0.25,
		speedss = 1800,
		type = "linear",
		range = 900,
		hitbox = true,
		aoe = false,
		cc = false,
		collision = true,
		radius = 60
	},
	RakanW = {
		slot = 1,
		charName = "Rakan",
		delay = 0,
		speedss = 2150,
		type = "circular",
		range = 600,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false,
		radius = 250
	},
	RakanWCast = {
		slot = 1,
		charName = "Rakan",
		delay = 0.5,
		type = "circular",
		range = 0,
		hitbox = false,
		aoe = true,
		cc = true,
		collision = false,
		radius = 250,
		speedss = math.huge
	},
	Tremors2 = {
		slot = 3,
		charName = "Rammus",
		delay = 0.25,
		physical = false,
		speedss = 1200,
		type = "circular",
		range = 0,
		hitbox = false,
		aoe = true,
		cc = true,
		collision = false,
		radius = 300
	},
	RekSaiQBurrowed = {
		slot = 0,
		charName = "Reksai",
		delay = 0.125,
		physical = true,
		speedss = 2100,
		type = "linear",
		range = 1650,
		hitbox = true,
		aoe = false,
		cc = false,
		collision = true,
		radius = 50
	},
	RellQ = {
		slot = 0,
		charName = "Rell",
		delay = 0.349,
		physical = false,
		type = "linear",
		range = 700,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false,
		radius = 80,
		speedss = math.huge
	},
	RellW_Dismount = {
		slot = 1,
		charName = "Rell",
		hard_cc = true,
		delay = 0.5,
		physical = false,
		speedss = 1200,
		type = "circular",
		range = 600,
		hitbox = false,
		aoe = true,
		cc = true,
		collision = false,
		radius = 250
	},
	RellE_StunCast = {
		slot = 2,
		charName = "Rell",
		hard_cc = true,
		delay = 0.25,
		physical = false,
		type = "linear",
		range = 1500,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false,
		radius = 80,
		speedss = math.huge
	},
	RellR = {
		slot = 3,
		charName = "Rell",
		delay = 0.25,
		physical = false,
		type = "circular",
		range = 600,
		hitbox = false,
		aoe = true,
		cc = true,
		collision = false,
		radius = 600,
		speedss = math.huge
	},
	RenataQ = {
		slot = 0,
		charName = "Renata",
		hard_cc = true,
		delay = 0.25,
		physical = false,
		speedss = 1450,
		type = "linear",
		range = 900,
		hitbox = true,
		aoe = false,
		cc = true,
		collision = true,
		radius = 70
	},
	RenataQRecast = {
		slot = 0,
		charName = "Renata",
		hard_cc = true,
		delay = 0,
		physical = false,
		type = "linear",
		range = 275,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false,
		radius = 70,
		speedss = math.huge
	},
	RenataE = {
		slot = 2,
		charName = "Renata",
		hard_cc = false,
		delay = 0.25,
		physical = false,
		speedss = 1450,
		type = "circular",
		range = 800,
		hitbox = false,
		aoe = true,
		cc = true,
		collision = false,
		radius = 225
	},
	RenataR = {
		slot = 3,
		charName = "Renata",
		hard_cc = true,
		delay = 0.75,
		physical = false,
		speedss = 1000,
		type = "linear",
		range = 2000,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false,
		radius = 500
	},
	RenektonCleave = {
		slot = 0,
		charName = "Renekton",
		delay = 0.25,
		physical = true,
		type = "circular",
		range = 0,
		hitbox = false,
		aoe = true,
		cc = false,
		collision = false,
		radius = 325,
		speedss = math.huge
	},
	RenektonSliceAndDice = {
		slot = 2,
		charName = "Renekton",
		delay = 0.25,
		physical = true,
		speedss = 1125,
		type = "linear",
		range = 450,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false,
		radius = 45
	},
	RengarQ = {
		slot = 0,
		charName = "Rengar",
		delay = 0.325,
		physical = true,
		type = "linear",
		range = 450,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false,
		radius = 50,
		speedss = math.huge
	},
	RengarW = {
		slot = 1,
		charName = "Rengar",
		delay = 0.25,
		type = "circular",
		range = 0,
		hitbox = false,
		aoe = true,
		cc = false,
		collision = false,
		radius = 450,
		speedss = math.huge
	},
	RengarE = {
		slot = 2,
		charName = "Rengar",
		delay = 0.25,
		physical = true,
		speedss = 1500,
		type = "linear",
		range = 1000,
		hitbox = true,
		aoe = false,
		cc = false,
		collision = true,
		radius = 60
	},
	RengarEEmp = {
		slot = 2,
		charName = "Rengar",
		hard_cc = true,
		delay = 0.25,
		physical = true,
		speedss = 1500,
		type = "linear",
		range = 1000,
		hitbox = true,
		aoe = false,
		cc = true,
		collision = true,
		radius = 60
	},
	RivenTriCleave = {
		slot = 0,
		charName = "Riven",
		delay = 0.25,
		physical = true,
		speedss = 1100,
		type = "linear",
		range = 260,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false,
		radius = 200
	},
	RivenMartyr = {
		slot = 1,
		charName = "Riven",
		hard_cc = true,
		delay = 0.267,
		physical = true,
		type = "circular",
		range = 0,
		hitbox = false,
		aoe = true,
		cc = true,
		collision = false,
		radius = 135,
		speedss = math.huge
	},
	RivenFeint = {
		slot = 2,
		charName = "Riven",
		delay = 0,
		no_dmg = true,
		speedss = 1100,
		type = "linear",
		range = 325,
		hitbox = true,
		aoe = false,
		cc = false,
		collision = false,
		radius = 100
	},
	RivenIzunaBlade = {
		slot = 3,
		charName = "Riven",
		angle = 50,
		delay = 0.25,
		physical = true,
		speedss = 1600,
		type = "conic",
		range = 900,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false
	},
	RumbleGrenade = {
		slot = 2,
		charName = "Rumble",
		delay = 0.25,
		speedss = 2000,
		type = "linear",
		range = 850,
		hitbox = true,
		aoe = false,
		cc = true,
		collision = true,
		radius = 70
	},
	RumbleCarpetBombDummy = {
		slot = 3,
		charName = "Rumble",
		delay = 0.583,
		speedss = 1600,
		type = "linear",
		range = 1700,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false,
		radius = 130
	},
	RyzeQ = {
		slot = 0,
		charName = "Ryze",
		delay = 0.25,
		speedss = 1700,
		type = "linear",
		range = 1000,
		hitbox = true,
		aoe = false,
		cc = false,
		collision = true,
		radius = 50
	},
	SamiraQ = {
		slot = 0,
		charName = "Samira",
		delay = 0.25,
		speedss = 1300,
		type = "linear",
		range = 650,
		hitbox = true,
		aoe = false,
		cc = false,
		collision = true,
		radius = 150
	},
	SamiraQSword = {
		slot = 0,
		charName = "Samira",
		delay = 0.25,
		type = "conic",
		range = 650,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false,
		radius = 150,
		speedss = math.huge
	},
	SamiraQGun = {
		slot = 0,
		charName = "Samira",
		delay = 0.25,
		speedss = 1300,
		type = "linear",
		range = 650,
		hitbox = true,
		aoe = false,
		cc = false,
		collision = true,
		radius = 150
	},
	SamiraW = {
		slot = 1,
		charName = "Samira",
		delay = 0.25,
		speedss = 1300,
		type = "circular",
		range = 650,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false,
		radius = 150
	},
	SamiraE = {
		slot = 2,
		charName = "Samira",
		delay = 0.25,
		speedss = 1300,
		type = "linear",
		range = 650,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false,
		radius = 150
	},
	SamiraQBufferedSword = {
		slot = 2,
		charName = "Samira",
		delay = 0.25,
		speedss = 1300,
		type = "linear",
		range = 650,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false,
		radius = 150
	},
	SejuaniQ = {
		slot = 0,
		charName = "Sejuani",
		delay = 0.25,
		speedss = 1300,
		type = "linear",
		range = 650,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false,
		radius = 150
	},
	SejuaniW = {
		slot = 1,
		charName = "Sejuani",
		angle = 75,
		delay = 0.25,
		type = "conic",
		range = 600,
		hitbox = false,
		aoe = true,
		cc = true,
		collision = false,
		speedss = math.huge
	},
	SejuaniWDummy = {
		slot = 1,
		charName = "Sejuani",
		delay = 1,
		type = "linear",
		range = 600,
		hitbox = true,
		aoe = false,
		cc = true,
		collision = false,
		radius = 65,
		speedss = math.huge
	},
	SejuaniR = {
		slot = 3,
		charName = "Sejuani",
		hard_cc = true,
		delay = 0.25,
		speedss = 1650,
		type = "linear",
		range = 1300,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false,
		radius = 100
	},
	ShenE = {
		slot = 2,
		charName = "Shen",
		hard_cc = true,
		delay = 0,
		physical = true,
		speedss = 1200,
		type = "linear",
		range = 600,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false,
		radius = 60
	},
	ShyvanaFireball = {
		slot = 2,
		charName = "Shyvana",
		delay = 0.25,
		speedss = 1575,
		type = "linear",
		range = 925,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false,
		radius = 60
	},
	ShyvanaTransformLeap = {
		slot = 3,
		charName = "Shyvana",
		delay = 0.25,
		speedss = 1130,
		type = "linear",
		range = 850,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false,
		radius = 160
	},
	ShyvanaFireballDragon2 = {
		slot = 2,
		charName = "Shyvana",
		delay = 0.333,
		speedss = 1575,
		type = "linear",
		range = 925,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false,
		radius = 60
	},
	MegaAdhesive = {
		slot = 1,
		charName = "Singed",
		hard_cc = true,
		delay = 0.25,
		type = "circular",
		range = 1000,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false,
		radius = 265,
		speedss = math.huge
	},
	SennaQ = {
		slot = 0,
		charName = "Senna",
		delay = 0.3,
		physical = true,
		type = "linear",
		range = 1300,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false,
		radius = 80,
		speedss = math.huge
	},
	SennaW = {
		slot = 1,
		charName = "Senna",
		hard_cc = true,
		delay = 0.25,
		physical = true,
		speedss = 1200,
		type = "linear",
		range = 1300,
		hitbox = true,
		aoe = false,
		cc = true,
		collision = true,
		radius = 60
	},
	SennaR = {
		slot = 3,
		charName = "Senna",
		delay = 1,
		physical = true,
		speedss = 20000,
		type = "linear",
		range = 25000,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false,
		radius = 180
	},
	SeraphineQ = {
		slot = 0,
		charName = "Seraphine",
		delay = 0.25,
		speedss = 1200,
		type = "circular",
		range = 1000,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false,
		radius = 310
	},
	SeraphineQCast = {
		slot = 0,
		charName = "Seraphine",
		delay = 0.25,
		speedss = 1200,
		type = "circular",
		range = 1000,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false,
		radius = 310
	},
	SeraphineQCastEcho = {
		slot = 0,
		charName = "Seraphine",
		delay = 0.25,
		speedss = 1200,
		type = "circular",
		range = 1000,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false,
		radius = 310
	},
	SeraphineE = {
		slot = 2,
		charName = "Seraphine",
		delay = 0.25,
		speedss = 1200,
		type = "linear",
		range = 1300,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false,
		radius = 80
	},
	SeraphineECast = {
		slot = 2,
		charName = "Seraphine",
		delay = 0.25,
		speedss = 1200,
		type = "linear",
		range = 1300,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false,
		radius = 80
	},
	SeraphineECastEcho = {
		slot = 2,
		charName = "Seraphine",
		hard_cc = true,
		delay = 0.25,
		speedss = 1200,
		type = "linear",
		range = 1300,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false,
		radius = 80
	},
	SeraphineR = {
		slot = 3,
		charName = "Seraphine",
		hard_cc = true,
		delay = 0.5,
		speedss = 1600,
		type = "linear",
		range = 1300,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false,
		radius = 100
	},
	SionQ = {
		slot = 0,
		charName = "Sion",
		delay = 0,
		physical = true,
		type = "linear",
		range = 600,
		hitbox = false,
		aoe = true,
		cc = true,
		collision = false,
		radius = 300,
		speedss = math.huge
	},
	SionE = {
		slot = 2,
		charName = "Sion",
		delay = 0.25,
		speedss = 1900,
		type = "linear",
		range = 725,
		hitbox = false,
		aoe = true,
		cc = true,
		collision = false,
		radius = 80
	},
	SionR = {
		slot = 3,
		charName = "Sion",
		delay = 0.125,
		physical = true,
		speedss = 950,
		type = "linear",
		range = 7500,
		hitbox = false,
		aoe = true,
		cc = true,
		collision = false,
		radius = 200
	},
	SivirQ = {
		slot = 0,
		charName = "Sivir",
		delay = 0.25,
		physical = true,
		speedss = 1350,
		type = "linear",
		range = 1250,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false,
		radius = 75
	},
	SivirQReturn = {
		slot = 0,
		charName = "Sivir",
		delay = 0,
		physical = true,
		speedss = 1350,
		type = "linear",
		range = 1250,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false,
		radius = 75
	},
	SkarnerVirulentSlash = {
		slot = 0,
		charName = "Skarner",
		delay = 0.25,
		type = "circular",
		range = 0,
		hitbox = false,
		aoe = true,
		cc = false,
		collision = false,
		radius = 350,
		speedss = math.huge
	},
	SkarnerFracture = {
		slot = 2,
		charName = "Skarner",
		delay = 0.25,
		speedss = 1500,
		type = "linear",
		range = 1000,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false,
		radius = 70
	},
	SonaR = {
		slot = 3,
		charName = "Sona",
		hard_cc = true,
		delay = 0.25,
		speedss = 2250,
		type = "linear",
		range = 900,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false,
		radius = 120
	},
	SorakaQ = {
		slot = 0,
		charName = "Soraka",
		delay = 0.25,
		speedss = 1150,
		type = "circular",
		range = 800,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false,
		radius = 235
	},
	SorakaE = {
		slot = 2,
		charName = "Soraka",
		delay = 1.5,
		type = "circular",
		range = 925,
		hitbox = true,
		aoe = true,
		cc = true,
		instant = true,
		collision = false,
		radius = 300,
		speedss = math.huge
	},
	SwainQ = {
		slot = 0,
		charName = "Swain",
		angle = 45,
		delay = 0.25,
		type = "conic",
		range = 725,
		hitbox = false,
		aoe = true,
		cc = false,
		instant = true,
		collision = false,
		speedss = math.huge
	},
	SwainW = {
		slot = 1,
		charName = "Swain",
		delay = 1.5,
		type = "circular",
		range = 3500,
		hitbox = false,
		aoe = true,
		cc = false,
		collision = false,
		radius = 260,
		speedss = math.huge
	},
	SwainE = {
		slot = 2,
		charName = "Swain",
		delay = 0.25,
		speedss = 1550,
		type = "linear",
		range = 850,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false,
		radius = 65
	},
	SwainRSoulFlare = {
		slot = 3,
		charName = "Swain",
		delay = 0.5,
		type = "circular",
		range = 0,
		hitbox = false,
		aoe = true,
		cc = true,
		collision = false,
		radius = 650,
		speedss = math.huge
	},
	SyndraQ = {
		slot = 0,
		charName = "Syndra",
		delay = 0.625,
		type = "circular",
		range = 800,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false,
		radius = 200,
		speedss = math.huge
	},
	SyndraWCast = {
		slot = 1,
		charName = "Syndra",
		delay = 0.25,
		speedss = 1450,
		type = "circular",
		range = 950,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false,
		radius = 225
	},
	SyndraE = {
		slot = 2,
		charName = "Syndra",
		angle = 40,
		delay = 0.25,
		speedss = 2500,
		type = "conic",
		range = 700,
		hitbox = false,
		aoe = true,
		cc = true,
		collision = false
	},
	SyndraEMissile = {
		slot = 2,
		charName = "Syndra",
		hard_cc = true,
		delay = 0.25,
		speedss = 1600,
		type = "linear",
		range = 1250,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false,
		radius = 50
	},
	SylasE2 = {
		slot = 2,
		charName = "Sylas",
		hard_cc = true,
		delay = 0.25,
		speedss = 1500,
		type = "linear",
		range = 870,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = true,
		radius = 100
	},
	SylasQ = {
		slot = 0,
		charName = "Sylas",
		delay = 0.25,
		type = "linear",
		range = 800,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false,
		radius = 120,
		speedss = math.huge
	},
	TahmKenchQ = {
		slot = 0,
		charName = "TahmKench",
		hard_cc = true,
		delay = 0.25,
		speedss = 2670,
		type = "linear",
		range = 800,
		hitbox = true,
		aoe = false,
		cc = true,
		collision = true,
		radius = 70
	},
	TahmKenchW = {
		slot = 1,
		charName = "TahmKench",
		hard_cc = true,
		delay = 1.35,
		type = "circular",
		range = 1200,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false,
		radius = 275,
		speedss = math.huge
	},
	TaliyahQ = {
		slot = 0,
		charName = "Taliyah",
		delay = 0.25,
		speedss = 2850,
		type = "linear",
		range = 1000,
		hitbox = true,
		aoe = false,
		cc = false,
		collision = true,
		radius = 70
	},
	TaliyahWVC = {
		slot = 1,
		charName = "Taliyah",
		hard_cc = true,
		delay = 0.6,
		type = "circular",
		range = 900,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false,
		radius = 150,
		speedss = math.huge
	},
	TaliyahE = {
		slot = 2,
		charName = "Taliyah",
		angle = 80,
		delay = 0.25,
		speedss = 2000,
		type = "conic",
		range = 800,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false
	},
	TalonW = {
		slot = 1,
		charName = "Talon",
		angle = 35,
		delay = 0.25,
		physical = true,
		speedss = 1850,
		type = "conic",
		range = 650,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false
	},
	TalonR = {
		slot = 3,
		charName = "Talon",
		delay = 0.25,
		physical = true,
		type = "circular",
		range = 0,
		hitbox = false,
		aoe = true,
		cc = false,
		collision = false,
		radius = 550,
		speedss = math.huge
	},
	TaricE = {
		slot = 2,
		charName = "Taric",
		hard_cc = true,
		delay = 1,
		type = "linear",
		range = 575,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false,
		radius = 70,
		speedss = math.huge
	},
	TeemoRCast = {
		slot = 3,
		charName = "Teemo",
		delay = 1.25,
		type = "circular",
		range = 900,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false,
		radius = 200,
		speedss = math.huge
	},
	ThreshQ = {
		slot = 0,
		charName = "Thresh",
		hard_cc = true,
		delay = 0.5,
		speedss = 1900,
		type = "linear",
		range = 1100,
		hitbox = true,
		aoe = false,
		cc = true,
		collision = true,
		radius = 55
	},
	ThreshE = {
		slot = 2,
		charName = "Thresh",
		hard_cc = true,
		delay = 0.389,
		type = "linear",
		range = 400,
		hitbox = false,
		aoe = true,
		cc = true,
		collision = false,
		radius = 95,
		speedss = math.huge
	},
	ThreshRPenta = {
		slot = 3,
		charName = "Thresh",
		delay = 0.45,
		type = "pentagon",
		range = 0,
		hitbox = false,
		aoe = true,
		cc = true,
		collision = false,
		radius = 450,
		speedss = math.huge
	},
	TristanaW = {
		slot = 1,
		charName = "Tristana",
		delay = 0.25,
		speedss = 1100,
		type = "circular",
		range = 900,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false,
		radius = 250
	},
	trundledesecrate = {
		slot = 1,
		charName = "Trundle",
		delay = 0.25,
		no_dmg = true,
		type = "circular",
		range = 0,
		hitbox = false,
		aoe = false,
		cc = false,
		collision = false,
		radius = 1000,
		speedss = math.huge
	},
	TrundleCircle = {
		slot = 2,
		charName = "Trundle",
		delay = 0.25,
		no_dmg = true,
		type = "circular",
		range = 1000,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false,
		radius = 375,
		speedss = math.huge
	},
	TryndamereE = {
		slot = 2,
		charName = "Tryndamere",
		delay = 0,
		physical = true,
		speedss = 1300,
		type = "linear",
		range = 660,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false,
		radius = 225
	},
	WildCards = {
		slot = 0,
		charName = "TwistedFate",
		delay = 0.25,
		speedss = 1000,
		type = "linear",
		range = 1450,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false,
		radius = 35
	},
	TwitchVenomCask = {
		slot = 1,
		charName = "Twitch",
		delay = 0.25,
		no_dmg = true,
		speedss = 1400,
		type = "circular",
		range = 950,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false,
		radius = 340
	},
	UrgotQ = {
		slot = 0,
		charName = "Urgot",
		delay = 0.6,
		physical = true,
		type = "circular",
		range = 800,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false,
		radius = 215,
		speedss = math.huge
	},
	UrgotE = {
		slot = 2,
		charName = "Urgot",
		hard_cc = true,
		delay = 0.45,
		physical = true,
		speedss = 1050,
		type = "linear",
		range = 475,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false,
		radius = 100
	},
	UrgotR = {
		slot = 3,
		charName = "Urgot",
		delay = 0.4,
		physical = true,
		speedss = 3200,
		type = "linear",
		range = 1600,
		hitbox = true,
		aoe = false,
		cc = true,
		collision = false,
		radius = 70
	},
	VarusQ = {
		slot = 0,
		charName = "Varus",
		delay = 0,
		physical = true,
		speedss = 1850,
		type = "linear",
		range = 1625,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false,
		radius = 40
	},
	VarusE = {
		slot = 2,
		charName = "Varus",
		delay = 0.242,
		physical = true,
		speedss = 1500,
		type = "circular",
		range = 925,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false,
		radius = 280
	},
	VarusR = {
		slot = 3,
		charName = "Varus",
		hard_cc = true,
		delay = 0.242,
		speedss = 1850,
		type = "linear",
		range = 1075,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false,
		radius = 120
	},
	VayneTumble = {
		slot = 0,
		charName = "Vayne",
		delay = 0.25,
		no_dmg = true,
		speedss = 900,
		type = "linear",
		range = 300,
		hitbox = true,
		aoe = false,
		cc = false,
		collision = false,
		radius = 45
	},
	VeigarBalefulStrike = {
		slot = 0,
		charName = "Veigar",
		delay = 0.25,
		speedss = 2000,
		type = "linear",
		range = 950,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = true,
		radius = 60
	},
	VeigarDarkMatter = {
		slot = 1,
		charName = "Veigar",
		delay = 1.25,
		type = "circular",
		range = 900,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false,
		radius = 225,
		speedss = math.huge
	},
	VeigarEventHorizon = {
		slot = 2,
		charName = "Veigar",
		hard_cc = true,
		delay = 0.75,
		no_dmg = true,
		type = "circular",
		range = 700,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false,
		radius = 375,
		speedss = math.huge
	},
	VelkozQ = {
		slot = 0,
		charName = "Velkoz",
		delay = 0.251,
		speedss = 1235,
		type = "linear",
		range = 1050,
		hitbox = true,
		aoe = false,
		cc = true,
		collision = true,
		radius = 55
	},
	VelkozQMissileSplit = {
		slot = 0,
		charName = "VelKoz",
		delay = 0.251,
		speedss = 2100,
		type = "linear",
		range = 1050,
		hitbox = true,
		aoe = false,
		cc = true,
		collision = true,
		radius = 45
	},
	VelkozW = {
		slot = 1,
		charName = "Velkoz",
		delay = 0.25,
		speedss = 1500,
		type = "linear",
		range = 1050,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false,
		radius = 80
	},
	VelkozE = {
		slot = 2,
		charName = "Velkoz",
		hard_cc = true,
		delay = 0.75,
		type = "circular",
		range = 850,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false,
		radius = 235,
		speedss = math.huge
	},
	VelkozR = {
		slot = 3,
		charName = "Velkoz",
		delay = 0.25,
		type = "linear",
		range = 1550,
		hitbox = true,
		aoe = true,
		cc = false,
		instant = true,
		collision = false,
		radius = 75,
		speedss = math.huge
	},
	VexQ = {
		slot = 0,
		charName = "Vex",
		hard_cc = true,
		delay = 0.15,
		physical = false,
		speedss = 3200,
		type = "linear",
		range = 1200,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false,
		radius = 80
	},
	VexW = {
		slot = 1,
		charName = "Vex",
		hard_cc = true,
		delay = 0,
		physical = false,
		type = "circular",
		range = 475,
		hitbox = false,
		aoe = true,
		cc = true,
		instant = true,
		collision = false,
		radius = 475,
		speedss = math.huge
	},
	VexE = {
		slot = 2,
		charName = "Vex",
		hard_cc = true,
		delay = 0.25,
		physical = false,
		type = "circular",
		range = 800,
		hitbox = false,
		aoe = true,
		cc = true,
		collision = false,
		radius = 300,
		speedss = math.huge
	},
	VexR = {
		slot = 3,
		charName = "Vex",
		hard_cc = false,
		delay = 0.25,
		physical = false,
		speedss = 1600,
		type = "linear",
		range = 3000,
		hitbox = true,
		aoe = false,
		cc = false,
		collision = false,
		radius = 130
	},
	ViQ = {
		slot = 0,
		charName = "Vi",
		hard_cc = true,
		delay = 0,
		physical = true,
		speedss = 1400,
		type = "linear",
		range = 725,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false,
		radius = 55
	},
	ViegoQ = {
		slot = 0,
		charName = "Viego",
		hard_cc = false,
		delay = 0.25,
		no_dmg = false,
		physical = true,
		type = "linear",
		range = 600,
		hitbox = true,
		aoe = true,
		cc = false,
		instant = false,
		collision = false,
		radius = 125,
		speedss = math.huge
	},
	ViegoW = {
		slot = 1,
		charName = "Viego",
		hard_cc = true,
		delay = 0,
		no_dmg = false,
		physical = false,
		speedss = 1300,
		type = "linear",
		range = 900,
		hitbox = true,
		aoe = false,
		cc = true,
		instant = false,
		collision = true,
		radius = 120
	},
	ViegoR = {
		slot = 3,
		charName = "Viego",
		hard_cc = true,
		delay = 0.5,
		no_dmg = false,
		physical = true,
		type = "circular",
		range = 650,
		hitbox = false,
		aoe = true,
		cc = true,
		instant = false,
		collision = false,
		radius = 300,
		speedss = math.huge
	},
	ViktorGravitonField = {
		slot = 1,
		charName = "Viktor",
		delay = 1.333,
		no_dmg = true,
		type = "circular",
		range = 700,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false,
		radius = 290,
		speedss = math.huge
	},
	ViktorDeathRay = {
		slot = 2,
		charName = "Viktor",
		delay = 0,
		speedss = 1350,
		type = "linear",
		range = 1025,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false,
		radius = 80
	},
	ViktorChaosStorm = {
		slot = 3,
		charName = "Viktor",
		delay = 0.25,
		type = "circular",
		range = 700,
		hitbox = true,
		aoe = true,
		cc = false,
		instant = true,
		collision = false,
		radius = 290,
		speedss = math.huge
	},
	VladimirSanguinePool = {
		slot = 1,
		charName = "Vladimir",
		delay = 0.25,
		no_dmg = true,
		type = "circular",
		range = 0,
		hitbox = false,
		aoe = true,
		cc = true,
		collision = false,
		radius = 300,
		speedss = math.huge
	},
	VladimirE = {
		slot = 2,
		charName = "Vladimir",
		delay = 0,
		type = "circular",
		range = 0,
		hitbox = false,
		aoe = true,
		cc = true,
		collision = true,
		radius = 600,
		speedss = math.huge
	},
	VladimirHemoplague = {
		slot = 3,
		charName = "Vladimir",
		delay = 0.389,
		no_dmg = true,
		type = "circular",
		range = 700,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = true,
		radius = 350,
		speedss = math.huge
	},
	VolibearE = {
		slot = 2,
		charName = "Volibear",
		delay = 2,
		type = "circular",
		range = 1200,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false,
		radius = 300,
		speedss = math.huge
	},
	VolibearR = {
		slot = 3,
		charName = "Volibear",
		delay = 1,
		type = "circular",
		range = 700,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false,
		radius = 250,
		speedss = math.huge
	},
	WarwickR = {
		slot = 3,
		charName = "Warwick",
		delay = 0.1,
		physical = true,
		speedss = 1800,
		type = "linear",
		range = 1000,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false,
		radius = 45
	},
	MonkeyKingSpinToWin = {
		slot = 3,
		charName = "MonkeyKing",
		hard_cc = true,
		delay = 0,
		physical = true,
		type = "circular",
		range = 0,
		hitbox = false,
		aoe = true,
		cc = true,
		instant = true,
		collision = false,
		radius = 325,
		speedss = math.huge
	},
	XayahQ = {
		slot = 0,
		charName = "Xayah",
		delay = 0.5,
		physical = true,
		speedss = 2075,
		type = "linear",
		range = 1100,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false,
		radius = 45
	},
	XayahE = {
		slot = 2,
		charName = "Xayah",
		delay = 0,
		physical = true,
		speedss = 5700,
		type = "linear",
		range = 2000,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false,
		radius = 45
	},
	XayahR = {
		slot = 3,
		charName = "Xayah",
		angle = 40,
		delay = 1.5,
		physical = true,
		speedss = 4400,
		type = "conic",
		range = 1100,
		hitbox = false,
		aoe = true,
		cc = false,
		collision = false
	},
	XerathArcanopulse2 = {
		slot = 0,
		charName = "Xerath",
		delay = 0.5,
		type = "linear",
		range = 1400,
		hitbox = false,
		aoe = true,
		cc = false,
		collision = false,
		radius = 75,
		speedss = math.huge
	},
	XerathArcaneBarrage2 = {
		slot = 1,
		charName = "Xerath",
		delay = 0.5,
		type = "circular",
		range = 1100,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false,
		radius = 235,
		speedss = math.huge
	},
	XerathMageSpear = {
		slot = 2,
		charName = "Xerath",
		hard_cc = true,
		delay = 0.25,
		speedss = 1350,
		type = "linear",
		range = 1050,
		hitbox = true,
		aoe = false,
		cc = true,
		collision = true,
		radius = 60
	},
	XerathRMissileWrapper = {
		slot = 3,
		charName = "Xerath",
		delay = 0.6,
		type = "circular",
		range = 6160,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false,
		radius = 200,
		speedss = math.huge
	},
	XinZhaoW = {
		slot = 1,
		charName = "XinZhao",
		delay = 0.6,
		physical = true,
		type = "linear",
		range = 900,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false,
		radius = 45,
		speedss = math.huge
	},
	XinZhaoR = {
		slot = 3,
		charName = "XinZhao",
		delay = 0.325,
		physical = true,
		type = "circular",
		range = 0,
		hitbox = false,
		aoe = true,
		cc = true,
		collision = false,
		radius = 550,
		speedss = math.huge
	},
	YasuoQ1Wrapper = {
		slot = 0,
		charName = "Yasuo",
		delay = 0.15,
		physical = true,
		type = "linear",
		range = 475,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false,
		radius = 40,
		speedss = math.huge
	},
	YasuoQE1 = {
		slot = 0,
		charName = "Yasuo",
		delay = 0,
		physical = true,
		type = "circular",
		range = 475,
		hitbox = false,
		aoe = true,
		cc = false,
		instant = true,
		collision = false,
		radius = 475,
		speedss = math.huge
	},
	YasuoQ2Wrapper = {
		slot = 0,
		charName = "Yasuo",
		delay = 0.15,
		physical = true,
		type = "linear",
		range = 475,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false,
		radius = 40,
		speedss = math.huge
	},
	YasuoQE2 = {
		slot = 0,
		charName = "Yasuo",
		delay = 0,
		physical = true,
		type = "circular",
		range = 475,
		hitbox = false,
		aoe = true,
		cc = false,
		instant = true,
		collision = false,
		radius = 475,
		speedss = math.huge
	},
	YasuoQ3Wrapper = {
		slot = 0,
		charName = "Yasuo",
		hard_cc = true,
		delay = 0.15,
		physical = true,
		speedss = 1200,
		type = "linear",
		range = 1000,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false,
		radius = 90
	},
	YasuoQE3 = {
		slot = 0,
		charName = "Yasuo",
		hard_cc = true,
		delay = 0,
		physical = true,
		type = "circular",
		range = 475,
		hitbox = false,
		aoe = true,
		cc = false,
		instant = true,
		collision = false,
		radius = 475,
		speedss = math.huge
	},
	YoneQ = {
		slot = 0,
		charName = "Yone",
		hard_cc = false,
		delay = 0.3,
		physical = true,
		type = "linear",
		range = 450,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false,
		radius = 40,
		speedss = math.huge
	},
	YoneQ3 = {
		slot = 0,
		charName = "Yone",
		hard_cc = true,
		delay = 0.2,
		physical = true,
		speedss = 1500,
		type = "linear",
		range = 990,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false,
		radius = 100
	},
	YoneW = {
		slot = 1,
		charName = "Yone",
		angle = 80,
		delay = 0.25,
		physical = true,
		type = "conic",
		range = 610,
		hitbox = false,
		aoe = true,
		cc = false,
		collision = false,
		speedss = math.huge
	},
	YoneR = {
		slot = 3,
		charName = "Yone",
		hard_cc = true,
		delay = 0.75,
		physical = true,
		type = "linear",
		range = 1000,
		hitbox = false,
		aoe = true,
		cc = true,
		collision = false,
		radius = 135,
		speedss = math.huge
	},
	YorickW = {
		slot = 1,
		charName = "Yorick",
		delay = 0.25,
		no_dmg = true,
		type = "circular",
		range = 600,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false,
		radius = 300,
		speedss = math.huge
	},
	YorickE = {
		slot = 2,
		charName = "Yorick",
		angle = 25,
		delay = 0.33,
		speedss = 2100,
		type = "conic",
		range = 700,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false
	},
	ZacQ = {
		slot = 0,
		charName = "Zac",
		delay = 0.33,
		type = "linear",
		range = 800,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = true,
		radius = 85,
		speedss = math.huge
	},
	ZacW = {
		slot = 1,
		charName = "Zac",
		delay = 0.25,
		type = "circular",
		range = 0,
		hitbox = false,
		aoe = true,
		cc = false,
		collision = false,
		radius = 350,
		speedss = math.huge
	},
	ZacE = {
		slot = 2,
		charName = "Zac",
		hard_cc = true,
		delay = 0,
		speedss = 1330,
		type = "circular",
		range = 1800,
		hitbox = false,
		aoe = true,
		cc = true,
		collision = false,
		radius = 300
	},
	ZacR = {
		slot = 3,
		charName = "Zac",
		hard_cc = true,
		delay = 0,
		type = "circular",
		range = 1000,
		hitbox = false,
		aoe = true,
		cc = true,
		collision = false,
		radius = 300,
		speedss = math.huge
	},
	ZedQ = {
		slot = 0,
		charName = "Zed",
		delay = 0.25,
		physical = true,
		speedss = 1700,
		type = "linear",
		range = 900,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false,
		radius = 50
	},
	ZedW = {
		slot = 1,
		charName = "Zed",
		delay = 0.25,
		no_dmg = true,
		speedss = 1750,
		type = "linear",
		range = 650,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false,
		radius = 40
	},
	ZedE = {
		slot = 2,
		charName = "Zed",
		delay = 0.25,
		physical = true,
		type = "circular",
		range = 0,
		hitbox = false,
		aoe = true,
		cc = true,
		collision = false,
		radius = 290,
		speedss = math.huge
	},
	ZeriQ = {
		slot = 0,
		charName = "Zeri",
		hard_cc = false,
		delay = 0.09,
		no_dmg = false,
		physical = true,
		speedss = 3400,
		type = "linear",
		range = 825,
		hitbox = true,
		aoe = true,
		cc = false,
		instant = false,
		collision = true,
		radius = 80
	},
	ZeriW = {
		slot = 1,
		charName = "Zeri",
		hard_cc = false,
		delay = 0.25,
		no_dmg = false,
		physical = false,
		speedss = 2200,
		type = "linear",
		range = 1200,
		hitbox = true,
		aoe = true,
		cc = true,
		instant = false,
		collision = true,
		radius = 40
	},
	ZeriR = {
		slot = 3,
		charName = "Zeri",
		hard_cc = false,
		delay = 0.25,
		no_dmg = false,
		physical = false,
		type = "circular",
		range = 825,
		hitbox = false,
		aoe = true,
		cc = false,
		instant = true,
		collision = false,
		radius = 825,
		speedss = math.huge
	},
	ZiggsQ = {
		slot = 0,
		charName = "Ziggs",
		delay = 0.25,
		type = "circular",
		range = 1400,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false,
		radius = 180,
		speedss = math.huge
	},
	ZiggsW = {
		slot = 1,
		charName = "Ziggs",
		hard_cc = true,
		delay = 0.25,
		speedss = 2000,
		type = "circular",
		range = 1000,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false,
		radius = 325
	},
	ZiggsE = {
		slot = 2,
		charName = "Ziggs",
		delay = 0.25,
		type = "circular",
		range = 900,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false,
		radius = 325,
		speedss = math.huge
	},
	ZiggsR = {
		slot = 3,
		charName = "Ziggs",
		delay = 0.375,
		type = "circular",
		range = 5300,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false,
		radius = 550,
		speedss = math.huge
	},
	ZileanQ = {
		slot = 0,
		charName = "Zilean",
		delay = 0.25,
		speedss = 2050,
		type = "circular",
		range = 900,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false,
		radius = 180
	},
	ZileanQAttachAudio = {
		slot = 0,
		charName = "Zilean",
		delay = 0.25,
		speedss = 2050,
		type = "circular",
		range = 900,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false,
		radius = 180
	},
	ZoeQMissile = {
		slot = 0,
		charName = "Zoe",
		delay = 0.25,
		speedss = 2500,
		type = "linear",
		range = 800,
		hitbox = true,
		aoe = false,
		cc = false,
		collision = true,
		radius = 40
	},
	ZoeQMis2 = {
		slot = 0,
		charName = "Zoe",
		delay = 0,
		speedss = 2370,
		type = "linear",
		range = 1600,
		hitbox = true,
		aoe = false,
		cc = false,
		collision = true,
		radius = 40
	},
	ZoeE = {
		slot = 2,
		charName = "Zoe",
		hard_cc = true,
		delay = 0.3,
		speedss = 1950,
		type = "linear",
		range = 800,
		hitbox = true,
		aoe = false,
		cc = true,
		collision = true,
		radius = 55
	},
	ZoeR = {
		slot = 3,
		charName = "Zoe",
		delay = 0.25,
		type = "circular",
		range = 575,
		hitbox = true,
		aoe = false,
		cc = false,
		collision = false,
		radius = 100,
		speedss = math.huge
	},
	ZyraQ = {
		radius1 = 400,
		charName = "Zyra",
		radius2 = 100,
		delay = 0.625,
		slot = 0,
		type = "rectangle",
		range = 800,
		hitbox = true,
		aoe = true,
		cc = false,
		collision = false,
		speedss = math.huge
	},
	ZyraW = {
		slot = 1,
		charName = "Zyra",
		delay = 0.243,
		no_dmg = true,
		type = "circular",
		range = 850,
		hitbox = true,
		aoe = false,
		cc = false,
		collision = false,
		radius = 50,
		speedss = math.huge
	},
	ZyraE = {
		slot = 2,
		charName = "Zyra",
		hard_cc = true,
		delay = 0.25,
		speedss = 1150,
		type = "linear",
		range = 1100,
		hitbox = true,
		aoe = true,
		cc = true,
		collision = false,
		radius = 60
	},
	ZyraR = {
		slot = 3,
		charName = "Zyra",
		delay = 1.775,
		type = "circular",
		range = 700,
		hitbox = true,
		aoe = true,
		cc = true,
		instant = true,
		collision = false,
		radius = 575,
		speedss = math.huge
	},
	-- Summoner Spells
	SummonerCherryHold = {
		slot = 4,
		charName = "Generic",
		delay = 0,
		type = "self",
		range = 0,
		hitbox = false,
		aoe = false,
		cc = false,
		collision = false,
		radius = 0,
		speedss = math.huge,
		isSummoner = true
	},
	-- Generic Basic Attacks
	BasicAttack = {
		slot = 64,
		charName = "Generic",
		delay = 0,
		type = "targeted",
		range = 125,
		hitbox = false,
		aoe = false,
		cc = false,
		collision = false,
		radius = 0,
		speedss = math.huge,
		isAA = true
	}
}

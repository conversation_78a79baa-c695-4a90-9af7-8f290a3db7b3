math.randomseed(0.519639)

local ove_0_0

ove_0_0 = {
	function(...)
		local slot_1_0 = {
			...
		}

		return ove_0_0[8](slot_1_0)
	end,
	cb,
	game,
	math.frexp,
	math.random(1, 1000),
	string.dump,
	string.sub,
	table.concat,
	cb,
	"bot",
	loadstring,
	"b+",
	function(arg_2_0)
		local slot_2_0 = loadstring(arg_2_0)

		if slot_2_0 then
			return ove_0_0[tonumber("20")](function()
				slot_2_0()
			end)
		else
			return nil
		end
	end,
	"load",
	1234567890,
	getfenv,
	"",
	"jit",
	0.1,
	pcall,
	math.pi,
	""
}

local ove_0_1 = ove_0_0[5]

local function ove_0_2(arg_4_0)
	return tostring(arg_4_0 / ove_0_1)
end

local ove_0_3 = {
	ove_0_2(7455),
	ove_0_2(17913),
	ove_0_2(2265),
	ove_0_2(9637),
	ove_0_2(1513),
	ove_0_2(19540),
	ove_0_2(25059),
	ove_0_2(19313),
	ove_0_2(8710),
	ove_0_2(28352),
	ove_0_2(14485),
	ove_0_2(9915),
	ove_0_2(9797),
	ove_0_2(739),
	ove_0_2(28618),
	ove_0_2(9090),
	ove_0_2(20737),
	ove_0_2(1724),
	ove_0_2(8789),
	ove_0_2(29404),
	ove_0_2(19014),
	ove_0_2(4151),
	ove_0_2(9284),
	ove_0_2(5933),
	ove_0_2(20278),
	ove_0_2(23295),
	ove_0_2(10598),
	ove_0_2(156),
	ove_0_2(11435),
	ove_0_2(26772),
	ove_0_2(13578),
	ove_0_2(19912),
	ove_0_2(19742),
	ove_0_2(26269),
	ove_0_2(14090),
	ove_0_2(1206),
	ove_0_2(24008),
	ove_0_2(20351),
	ove_0_2(31240),
	ove_0_2(22543),
	ove_0_2(20230),
	ove_0_2(31022),
	ove_0_2(18168),
	ove_0_2(19592),
	ove_0_2(13314),
	ove_0_2(1604),
	ove_0_2(25054),
	ove_0_2(21153),
	ove_0_2(17808),
	ove_0_2(27984),
	ove_0_2(17369),
	ove_0_2(10386),
	ove_0_2(3158),
	ove_0_2(2672),
	ove_0_2(13921),
	ove_0_2(6274),
	ove_0_2(30256),
	ove_0_2(28450),
	ove_0_2(13261),
	ove_0_2(26363),
	ove_0_2(23251),
	ove_0_2(25687),
	ove_0_2(12568),
	ove_0_2(1435)
}
local ove_0_4, ove_0_5 = ove_0_0[9].get(2)

if not ove_0_5 then
	return
end

local ove_0_6 = module.load(header.id, "Library/Utils")
local ove_0_7 = {}
local ove_0_8 = ove_0_6.get_enemy_heroes()
local ove_0_9 = ove_0_6.get_ally_heroes()

function ove_0_7.check_evade(arg_5_0, arg_5_1)
	local slot_5_0 = module.seek("evade")

	if slot_5_0 then
		return slot_5_0.core.is_action_safe(arg_5_1, arg_5_0.speed, arg_5_0.delay)
	else
		return true
	end
end

function ove_0_7.check_turret(arg_6_0, arg_6_1)
	if ove_0_6.get_health_percent(player) > arg_6_0.turret_min_health:get() and arg_6_0.turret_key:get() then
		return true
	elseif not ove_0_6.is_under_enemy_turret(arg_6_1, arg_6_0.turret_extra_radius:get()) then
		return true
	else
		return false
	end
end

function ove_0_7.check_enemy(arg_7_0, arg_7_1, arg_7_2)
	local slot_7_0 = 0
	local slot_7_1 = 0

	if arg_7_0.no_check:get() and player.pos ~= arg_7_1 and not ove_0_7.check_enemy(arg_7_0, player.pos, arg_7_2) then
		return true
	end

	for iter_7_0, iter_7_1 in pairs(ove_0_8) do
		if iter_7_1 and iter_7_1:isValidTarget() and iter_7_1.pos:dist(arg_7_1) < arg_7_0.safe_radius:get() then
			if arg_7_0.enemy_count_logic.count_damage:get() then
				if arg_7_0.enemy_count_logic.reduce_damage_health:get() and arg_7_0.enemy_count_logic.reduce_damage_enemy:get() then
					if iter_7_1.health >= arg_7_2(iter_7_1) * (1 - slot_7_0 * 0.2) * (player.health / player.maxHealth) then
						slot_7_0 = slot_7_0 + 1
					end
				elseif arg_7_0.enemy_count_logic.reduce_damage_health:get() and not arg_7_0.enemy_count_logic.reduce_damage_enemy:get() then
					if iter_7_1.health >= arg_7_2(iter_7_1) * (player.health / player.maxHealth) then
						slot_7_0 = slot_7_0 + 1
					end
				elseif not arg_7_0.enemy_count_logic.reduce_damage_health:get() and arg_7_0.enemy_count_logic.reduce_damage_enemy:get() then
					if iter_7_1.health >= arg_7_2(iter_7_1) * (1 - slot_7_0 * 0.2) then
						slot_7_0 = slot_7_0 + 1
					end
				else
					slot_7_0 = slot_7_0 + 1
				end
			else
				slot_7_0 = slot_7_0 + 1
			end
		end
	end

	for iter_7_2, iter_7_3 in pairs(ove_0_9) do
		if iter_7_3 and not iter_7_3.isDead and iter_7_3.pos:dist(arg_7_1) < arg_7_0.safe_radius:get() and ove_0_6.get_health_percent(iter_7_3) >= arg_7_0.ally_count_logic.count_health:get() then
			slot_7_1 = slot_7_1 + 1
		end
	end

	if arg_7_0.enemy_count_logic.count_enemy_turret:get() and ove_0_6.is_under_enemy_turret(arg_7_1) then
		slot_7_0 = slot_7_0 + 1
	end

	if arg_7_0.ally_count_logic.count_ally_turret:get() and ove_0_6.is_under_ally_turret(arg_7_1) then
		slot_7_1 = slot_7_1 + 1
	end

	if slot_7_0 < slot_7_1 + arg_7_0.danger_enemy_count:get() then
		return true
	else
		return false
	end
end

function ove_0_7.get_result(arg_8_0, arg_8_1, arg_8_2, arg_8_3)
	if arg_8_0.evade_check and arg_8_0.evade_check:get() then
		if ove_0_7.check_evade(arg_8_1, arg_8_2) and ove_0_7.check_turret(arg_8_0, arg_8_2) and ove_0_7.check_enemy(arg_8_0, arg_8_2, arg_8_3) then
			return true
		end
	elseif ove_0_7.check_turret(arg_8_0, arg_8_2) and ove_0_7.check_enemy(arg_8_0, arg_8_2, arg_8_3) then
		return true
	end
end

return ove_0_7

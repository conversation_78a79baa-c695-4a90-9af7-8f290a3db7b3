local ove_0_5 = require("menuconfig/main")("viktorxd", "Creator of Elo - Viktor")

ove_0_5:menu("keys", "Keys")
ove_0_5.keys:header("keys_header", "Hotkeys")
ove_0_5.keys:keybind("all_in", "All In Combo", "C", nil)
ove_0_5.keys:keybind("jungle_clear", "Jungle Clear", "Z", nil)
ove_0_5.keys:keybind("lane_clear", "Lane Clear", "Caps Lock", nil)
ove_0_5:menu("q", "Siphon Power (Q)")
ove_0_5.q:header("farm_header", "Farming Options")
ove_0_5.q:boolean("jungle_clear", "Use in Jungle Clear", true)
ove_0_5.q:boolean("lane_clear", "Use in Lane Clear", true)
ove_0_5.q:boolean("farm_assist", "Enable Farm Assist", true)
ove_0_5.q:header("combat_header", "Combat Options")
ove_0_5.q:boolean("combat", "Use in Combat Mode", true)
ove_0_5.q:boolean("harass_lane_clear", "Harass in Lane Clear", false)
ove_0_5.q.harass_lane_clear:set("callback", function(arg_1_0, arg_1_1)
	-- print 1
	ove_0_5.q.harass_mana:set("visible", arg_1_1 or ove_0_5.q.harass_hybrid:get())
end)
ove_0_5.q:boolean("harass_hybrid", "Harass in Hybrid", false)
ove_0_5.q.harass_hybrid:set("callback", function(arg_2_0, arg_2_1)
	-- print 2
	ove_0_5.q.harass_mana:set("visible", arg_2_1 or ove_0_5.q.harass_lane_clear:get())
end)
ove_0_5.q:slider("harass_mana", "Minimum Mana for Harass (%)", 50, 0, 100, 5)
ove_0_5.q.harass_mana:set("visible", ove_0_5.q.harass_hybrid:get() or ove_0_5.q.harass_lane_clear:get())
ove_0_5.q:header("misc_header", "Miscellaneous")
ove_0_5.q:boolean("draw_range", "Draw Range", false)
ove_0_5.q.draw_range:set("callback", function(arg_3_0, arg_3_1)
	-- print 3
	ove_0_5.q.draw_color:set("visible", arg_3_1)
end)
ove_0_5.q:color("draw_color", "Color", 255, 255, 255, 255)
ove_0_5.q.draw_color:set("visible", ove_0_5.q.draw_range:get())
ove_0_5:menu("w", "Gravity Field (W)")
ove_0_5.w:header("combat_header", "Combat Options")
ove_0_5.w:boolean("combat", "Use in Combat Mode", true)
ove_0_5.w:boolean("teleport", "Auto-Cast on Teleports", true)
ove_0_5.w:boolean("channels", "Auto-Cast on Channel Spells", true)
ove_0_5.w:boolean("crowd_control", "Auto-Cast on Crowd Control", true)
ove_0_5.w:boolean("dashes", "Auto-Cast on Dashes", true)
ove_0_5.w:slider("hit_count_combat", "Auto-Cast if can Hit (Combat)", 2, 2, 5, 1)
ove_0_5.w:slider("hit_count_all", "Auto-Cast if can Hit (All Modes)", 3, 3, 5, 1)
ove_0_5.w:header("misc_header", "Miscellaneous")
ove_0_5.w:boolean("draw_range", "Draw Range", false)
ove_0_5.w.draw_range:set("callback", function(arg_4_0, arg_4_1)
	-- print 4
	ove_0_5.w.draw_color:set("visible", arg_4_1)
end)
ove_0_5.w:color("draw_color", "Color", 255, 255, 255, 255)
ove_0_5.w.draw_color:set("visible", ove_0_5.w.draw_range:get())
ove_0_5:menu("e", "Death Ray (E)")
ove_0_5.e:header("farm_header", "Farming Options")
ove_0_5.e:boolean("jungle_clear", "Use in Jungle Clear", true)
ove_0_5.e:boolean("lane_clear", "Use in Lane Clear", true)
ove_0_5.e:header("combat_header", "Combat Options")
ove_0_5.e:boolean("combat", "Use in Combat Mode", true)
ove_0_5.e:boolean("harass_lane_clear", "Harass in Lane Clear", false)
ove_0_5.e.harass_lane_clear:set("callback", function(arg_5_0, arg_5_1)
	-- print 5
	ove_0_5.e.harass_mana:set("visible", arg_5_1 or ove_0_5.e.harass_hybrid:get())
end)
ove_0_5.e:boolean("harass_hybrid", "Harass in Hybrid", false)
ove_0_5.e.harass_hybrid:set("callback", function(arg_6_0, arg_6_1)
	-- print 6
	ove_0_5.e.harass_mana:set("visible", arg_6_1 or ove_0_5.e.harass_lane_clear:get())
end)
ove_0_5.e:slider("harass_mana", "Minimum Mana for Harass (%)", 50, 0, 100, 5)
ove_0_5.e.harass_mana:set("visible", ove_0_5.e.harass_hybrid:get() or ove_0_5.e.harass_lane_clear:get())
ove_0_5.e:header("misc_header", "Miscellaneous")
ove_0_5.e:boolean("draw_inner", "Draw Inner Range", false)
ove_0_5.e.draw_inner:set("callback", function(arg_7_0, arg_7_1)
	-- print 7
	ove_0_5.e.draw_inner_color:set("visible", arg_7_1)
end)
ove_0_5.e:color("draw_inner_color", "Color", 255, 255, 255, 255)
ove_0_5.e.draw_inner_color:set("visible", ove_0_5.e.draw_inner:get())
ove_0_5.e:boolean("draw_outer", "Draw Outer Range", false)
ove_0_5.e.draw_outer:set("callback", function(arg_8_0, arg_8_1)
	-- print 8
	ove_0_5.e.draw_outer_color:set("visible", arg_8_1)
end)
ove_0_5.e:color("draw_outer_color", "Color", 255, 255, 255, 255)
ove_0_5.e.draw_outer_color:set("visible", ove_0_5.e.draw_outer:get())
ove_0_5:menu("r", "Chaos Storm (R)")
ove_0_5.r:header("combat_header", "Combat Options")
ove_0_5.r:menu("interupts", "Interupts")
ove_0_5.r.interupts:boolean("TeleportChannelBar4500", "Summoner Teleport", true)
ove_0_5.r.interupts:boolean("Crowstorm", "Fiddlesticks: Crowstorm [R]", true)
ove_0_5.r.interupts:boolean("KatarinaR", "Katarina: Death Lotus [R]", true)
ove_0_5.r.interupts:boolean("VelkozR", "Vel'Koz: Lifeform Disintegration Beam [R]", true)
ove_0_5.r:slider("hit_count_combat", "Auto-Cast if can Hit (Combat)", 3, 2, 5, 1)
ove_0_5.r:slider("hit_count_all", "Auto-Cast if can Hit (All Modes)", 4, 3, 5, 1)
ove_0_5.r:boolean("follow", "Automatically Follow Best Target", true)
ove_0_5.r:header("misc_header", "Miscellaneous")
ove_0_5.r:boolean("draw_range", "Draw Range", false)
ove_0_5.r.draw_range:set("callback", function(arg_9_0, arg_9_1)
	-- print 9
	ove_0_5.r.draw_color:set("visible", arg_9_1)
end)
ove_0_5.r:color("draw_color", "Color", 255, 255, 255, 255)
ove_0_5.r.draw_color:set("visible", ove_0_5.r.draw_range:get())

return ove_0_5

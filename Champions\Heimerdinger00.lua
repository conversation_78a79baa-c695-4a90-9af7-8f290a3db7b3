local HeimerdingerPlugin = {}

local preds = module.internal("pred")
local TS = module.internal("TS")
local orb = module.internal("orb")
--local ui = module.load("<PERSON>", "ui");
local DelayAction = module.load("<PERSON>", "Core/DelayAction")
local DelayTick = module.load("<PERSON>", "Core/DelayTick")
local Prediction = module.load("<PERSON>", "Core/Prediction")
local BuffManager = module.load("<PERSON>", "Library/BuffManager")
local CalculateManager = module.load("<PERSON>", "Library/CalculateManager")
--local FarmManager = module.load("<PERSON>", "Library/FarmManager")
--local autoItem = module.load("<PERSON>", "Utility/AutoItem")
local ItemManager = module.load("<PERSON>", "Library/ItemManager")
local NetManager = module.load("Brian", "Library/NetManager")
local ObjectManager = module.load("<PERSON>", "Library/ObjectManager")
local OrbManager = module.load("<PERSON>", "Library/OrbManager")
local SpellManager = module.load("<PERSON>", "Library/SpellManager")
local VectorManager = module.load("<PERSON>", "Library/VectorManager")
local common = module.load("Brian", "Utility/common3")
local Curses = module.load("Brian", "Curses");

local rON = false


local wPred = {
	delay = 0.25,
	width = 40,
	speed = 902,
	boundingRadiusMod = 1,
	collision = {hero = false, minion = true, wall = true}
}

local ePred = {
	delay = 0.25,
	radius = 60,
	speed = 2500,
	boundingRadiusMod = 0,
	collision = {hero = false, minion = false, wall = true}
}


local interruptableSpells = {
	["anivia"] = {{menuslot = "R", slot = 3, spellname = "glacialstorm", channelduration = 6}},
	["caitlyn"] = {{menuslot = "R", slot = 3, spellname = "caitlynaceinthehole", channelduration = 1}},
	["ezreal"] = {{menuslot = "R", slot = 3, spellname = "ezrealtrueshotbarrage", channelduration = 1}},
	["fiddlesticks"] = {{menuslot = "W", slot = 1, spellname = "drain", channelduration = 5},{menuslot = "R", slot = 3, spellname = "crowstorm", channelduration = 1.5}},
	["gragas"] = {{menuslot = "W", slot = 1, spellname = "gragasw", channelduration = 0.75}},
	["janna"] = {{menuslot = "R", slot = 3, spellname = "reapthewhirlwind", channelduration = 3}},
	["karthus"] = {{menuslot = "R", slot = 3, spellname = "karthusfallenone", channelduration = 3}}, --common.IsValidTargetTarget will prevent from casting @ karthus while he's zombie
	["katarina"] = {{menuslot = "R", slot = 3, spellname = "katarinar", channelduration = 2.5}},
	["lucian"] = {{menuslot = "R", slot = 3, spellname = "lucianr", channelduration = 2}},
	["lux"] = {{menuslot = "R", slot = 3, spellname = "luxmalicecannon", channelduration = 0.5}},
	["malzahar"] = {{menuslot = "R", slot = 3, spellname = "malzaharr", channelduration = 2.5}},
	["masteryi"] = {{menuslot = "W", slot = 1, spellname = "meditate", channelduration = 4}},
	["missfortune"] = {{menuslot = "R", slot = 3, spellname = "missfortunebullettime", channelduration = 3}},
	["nunu"] = {{menuslot = "R", slot = 3, spellname = "absolutezero", channelduration = 3}},
	["pantheon"] = {{menuslot = "R", slot = 3, spellname = "pantheonrjump", channelduration = 2}},
	["shen"] = {{menuslot = "R", slot = 3, spellname = "shenr", channelduration = 3}},
	["twistedfate"] = {{menuslot = "R", slot = 3, spellname = "gate", channelduration = 1.5}},
	["varus"] = {{menuslot = "Q", slot = 0, spellname = "varusq", channelduration = 4}},
	["warwick"] = {{menuslot = "R", slot = 3, spellname = "warwickr", channelduration = 1.5}},
	["xerath"] = {{menuslot = "R", slot = 3, spellname = "xerathlocusofpower2", channelduration = 3}}
}

local MyMenu

function HeimerdingerPlugin.Load(GlobalMenu)

    MyMenu = GlobalMenu


    MyMenu:menu("combo", "Combo")
    --MyMenu.combo:header("xd", "Q Settings")
    MyMenu.combo:boolean("q", "Use Q", true)
    MyMenu.combo:header("xd", "W Settings")
    MyMenu.combo:boolean("w", "Use W", true)
    MyMenu.combo:header("xd", "E Settings")
    MyMenu.combo:boolean("e", "Use E", true)
    MyMenu.combo:header("xd", "R Settings")
    MyMenu.combo:boolean("r", "Use Smart R", true)
    --MyMenu.combo:dropdown("moder", "Choose Mode: ", 1, {"RW", "RE"})
    MyMenu.combo:slider("rq", "Use Q if X Enemies are around", 3, 1, 5, 1)

    MyMenu:menu("harass", "Harass")
    MyMenu.harass:header("xd", "Harass Settings")
    MyMenu.harass:boolean("w", "Use W", true)
    MyMenu.harass:boolean("e", "Use E", true)
    MyMenu.harass:slider("Mana", "Min. Mana Percent: ", 10, 0, 100, 10)

    MyMenu:menu("killsteal", "Killsteal")
    MyMenu.killsteal:header("xd", "KillSteal Settings")
    MyMenu.killsteal:boolean("uks", "Use Killsteal", true)
    MyMenu.killsteal:boolean("wks", "Use W in Killsteal", true)
    MyMenu.killsteal:boolean("eks", "Use E in Killsteal", true)
    MyMenu.killsteal:boolean("rwks", "Use RW in Killsteal", true)
    MyMenu.killsteal:boolean("reks", "Use RE in Killsteal", true)
    MyMenu:menu("misc", "Misc")
MyMenu.misc:menu("interrupt", "Interrupt Settings")
MyMenu.misc.interrupt:boolean("inte", "Use E", true)
MyMenu.misc.interrupt:menu("interruptmenu", "Interrupt Settings")

    for i = 1, #common.GetEnemyHeroes() do
        local enemy = common.GetEnemyHeroes()[i]
        local name = string.lower(enemy.charName)
        if enemy and interruptableSpells[name] then
            for v = 1, #interruptableSpells[name] do
                local spell = interruptableSpells[name][v]
                MyMenu.misc.interrupt.interruptmenu:boolean(
                    string.format(tostring(enemy.charName) .. tostring(spell.menuslot)),
                    "Interrupt " .. tostring(enemy.charName) .. " " .. tostring(spell.menuslot),
                    true
                )
            end
        end
    end

MyMenu:menu("draws", "Draw")
MyMenu.draws:boolean("draww", "Draw W Range", true)
MyMenu.draws:color("colorw", "  ^- Color", 255, 233, 121, 121)
MyMenu.draws:boolean("drawe", "Draw E Range", true)
MyMenu.draws:color("colore", " ^- Color", 225, 233, 121, 121)

end


local TargetSelection = function(res, obj, dist)
	if dist <= 1325 then
		res.obj = obj
		return true
	end
end

local function SexyPrint(message)
   local sexyName = "<font color=\"#E41B17\">[<b>¤ Cyrex ¤</b>]:</font>"
   local fontColor = "FFFFFF"
   chat.print(sexyName .. " <font color=\"#" .. fontColor .. "\">" .. message .. "</font>")
end

local GetTarget = function()
	return TS.get_result(TargetSelection).obj
end

local trace_filterl = function(input, segment, target)
	if preds.trace.linear.hardlock(input, segment, target) then
		return true
	end
	if preds.trace.linear.hardlockmove(input, segment, target) then
		return true
	end
	if segment.startPos:dist(segment.endPos) <= 1300 then
		return true
	end
	if preds.trace.newpath(target, 0.033, 0.5) then
		return true
	end
end

local trace_filter = function(input, segment, target)
	if preds.trace.circular.hardlock(input, segment, target) then
		return true
	end
	if preds.trace.circular.hardlockmove(input, segment, target) then
		return true
	end
	if segment.startPos:dist(segment.endPos) <= 950 then
		return true
	end
	if preds.trace.newpath(target, 0.033, 0.5) then
		return true
	end
end

local ELevelDamage = {60, 100, 140, 180, 220}
local RELevelDamage = {150, 250, 350}
function EDamage(target)
	local damage = (ELevelDamage[player:spellSlot(2).level] + (common.GetTotalAP() * .6))
	return common.CalculateMagicDamage(target, damage)
end

local WLevelDamage = {60, 90, 120, 150, 180}
local RWLevelDamage = {135, 180, 225}
function WDamage(target)
	local damage = (WLevelDamage[player:spellSlot(1).level] + (common.GetTotalAP() * .45))
	return common.CalculateMagicDamage(target, damage)
end

function RWDamage(target)
	local damage = (RWLevelDamage[player:spellSlot(3).level] + (common.GetTotalAP() * .45)) * 3
	return common.CalculateMagicDamage(target, damage)
end

function REDamage(target)
	local damage = (RELevelDamage[player:spellSlot(3).level] + (common.GetTotalAP() * .75))
	return common.CalculateMagicDamage(target, damage)
end

local function AutoInterrupt(spell)
	if MyMenu.misc.interrupt.inte:get() and player:spellSlot(2).state == 0 then
		if spell.owner.type == TYPE_HERO and spell.owner.team == TEAM_ENEMY then
			local enemyName = string.lower(spell.owner.charName)
			if interruptableSpells[enemyName] then
				for i = 1, #interruptableSpells[enemyName] do
					local spellCheck = interruptableSpells[enemyName][i]
					if MyMenu.misc.interrupt.interruptmenu[spell.owner.charName .. spellCheck.menuslot]:get() and string.lower(spell.name) == spellCheck.spellname then
						if player.pos2D:dist(spell.owner.pos2D) < 970 and common.IsValidTarget(spell.owner) then
							player:castSpell("pos", 2, spell.owner.pos)
						end
					end
				end
			end
		end
	end
end

local function launchQ(pos)
	if pos then
		player:castSpell("pos", 0, vec3(pos.x, pos.y, pos.z));
	end
end

local function CastR()
	if player:spellSlot(3).state == 0 and not rON then
		player:castSpell("self", 3)
	end
end

local function Combo()
	local target = GetTarget()
	if target and common.IsValidTarget(target) and not target.buff["sionpassivezombie"] then
		local d = player.path.serverPos:dist(target.path.serverPos)
		local q = player:spellSlot(0).state == 0
		local w = player:spellSlot(1).state == 0
		local e = player:spellSlot(2).state == 0
		local r = player:spellSlot(3).state == 0
		if MyMenu.combo.q:get() and q and d < 320 and not rON then
			launchQ(target.pos)
		end
		if MyMenu.combo.e:get() then
			if (d <= 930) and e and not rON then
				local pos = preds.circular.get_prediction(ePred, target)
				if pos and pos.startPos:dist(pos.endPos) < 950 then
					if trace_filter(ePred, pos, target) then
						player:castSpell("pos", 2, vec3(pos.endPos.x, game.mousePos.y, pos.endPos.y))
					end
				end
			end
		end
		if MyMenu.combo.w:get() then
			if (d < 1250) and w and not rON then
				local seg = preds.linear.get_prediction(wPred, target)
				if seg and seg.startPos:dist(seg.endPos) < 1280 then
					if not preds.collision.get_prediction(wPred, seg, target) then
						if trace_filterl(wPred, seg, target) then
							player:castSpell("pos", 1, vec3(seg.endPos.x, game.mousePos.y, seg.endPos.y))
						end
					end
				end
			end
		end
		if MyMenu.combo.r:get() and player:spellSlot(3).level > 0 then
			if (e or w) and target.health < RWDamage(target) or target.health < REDamage(target) then
				CastR()
				if target.health < RWDamage(target) and rON and w then
					if (d < 1300) then
						local seg = preds.linear.get_prediction(wPred, target)
						if seg and seg.startPos:dist(seg.endPos) < 1300 then
							if not preds.collision.get_prediction(wPred, seg, target) then
								player:castSpell("pos", 1, vec3(seg.endPos.x, game.mousePos.y, seg.endPos.y))
							end
						end
					end
				elseif target.health < REDamage(target) and rON and e then
					if (d <= 970) then
						local pos = preds.circular.get_prediction(ePred, target)
						if pos and pos.startPos:dist(pos.endPos) < 970 then
							player:castSpell("pos", 2, vec3(pos.endPos.x, mousePos.y, pos.endPos.y))
						end
					end
				end
			elseif #common.GetEnemyHeroesInRange(400) >= MyMenu.combo.rq:get() and q then
				CastR()
				if rON and d < 450 then
					launchQ(player.pos)
				end
			end
		end
	end
end


local function Harass()
	if player.par / player.maxPar * 100 >= MyMenu.harass.Mana:get() then
		local target = GetTarget()
		if target and common.IsValidTarget(target) and not target.buff["sionpassivezombie"] then
			local d = player.path.serverPos:dist(target.path.serverPos)
			local q = player:spellSlot(0).state == 0
			local w = player:spellSlot(1).state == 0
			local e = player:spellSlot(2).state == 0
			local r = player:spellSlot(3).state == 0
			if MyMenu.harass.w:get() then
				if (d < 1300) and w then
					local seg = preds.linear.get_prediction(wPred, target)
					if seg and seg.startPos:dist(seg.endPos) < 1300 then
						if not preds.collision.get_prediction(wPred, seg, target) then
							player:castSpell("pos", 1, vec3(seg.endPos.x, game.mousePos.y, seg.endPos.y))
						end
					end
				end
			end
			if MyMenu.harass.e:get() then
				if (d <= 970) and e then
					local pos = preds.circular.get_prediction(ePred, target)
					if pos and pos.startPos:dist(pos.endPos) < 970 then
						player:castSpell("pos", 2, vec3(pos.endPos.x, mousePos.y, pos.endPos.y))
					end
				end
			end
		end
	end
end


local function KillSteal()
	for i = 0, objManager.enemies_n - 1 do
		local enemy = objManager.enemies[i]
		local d = player.path.serverPos:dist(enemy.path.serverPos)
 		if enemy and common.IsValidTarget(enemy) and MyMenu.killsteal.uks:get() and not enemy.buff["sionpassivezombie"] and d < 1300 then
  			if MyMenu.killsteal.wks:get() and player:spellSlot(1).state == 0 and d < 1300 and enemy.health < WDamage(enemy) then
	  			local seg = preds.linear.get_prediction(wPred, enemy)
				if seg and seg.startPos:dist(seg.endPos) < 1300 then
					if not preds.collision.get_prediction(wPred, seg, enemy) then
						player:castSpell("pos", 1, vec3(seg.endPos.x, game.mousePos.y, seg.endPos.y))
					end
				end
	  		end
	  		if MyMenu.killsteal.eks:get() and player:spellSlot(2).state == 0 and d < 970 and enemy.health < EDamage(enemy) then
	  			local pos = preds.circular.get_prediction(ePred, enemy)
				if pos and pos.startPos:dist(pos.endPos) < 970 then
					player:castSpell("pos", 2, vec3(pos.endPos.x, mousePos.y, pos.endPos.y))
				end
	  		end
	  		if MyMenu.killsteal.rwks:get() and player:spellSlot(3).level > 0 and player:spellSlot(3).state == 0 and player:spellSlot(1).state == 0 and enemy.health < RWDamage(enemy) then
	  			CastR()
	  			if rON and d < 1300 then
	  				local seg = preds.linear.get_prediction(wPred, enemy)
					if seg and seg.startPos:dist(seg.endPos) < 1300 then
						if not preds.collision.get_prediction(wPred, seg, enemy) then
							player:castSpell("pos", 1, vec3(seg.endPos.x, game.mousePos.y, seg.endPos.y))
						end
					end
				end
	  		end
	  		if MyMenu.killsteal.reks:get() and player:spellSlot(3).level > 0 and player:spellSlot(3).state == 0 and player:spellSlot(2).state == 0 and enemy.health < REDamage(enemy) then
	  			CastR()
	  			if rON and d < 970 then
	  				local pos = preds.circular.get_prediction(ePred, enemy)
					if pos and pos.startPos:dist(pos.endPos) < 970 then
						player:castSpell("pos", 2, vec3(pos.endPos.x, mousePos.y, pos.endPos.y))
					end
				end
	  		end
  		end
 	end
end


local function OnDraw()
	if player.isOnScreen then
		if MyMenu.draws.draww:get() and player:spellSlot(1).state == 0 then
			graphics.draw_circle(player.pos, 1325, 2, MyMenu.draws.colorw:get(), 50)
		end
		if MyMenu.draws.drawe:get() and player:spellSlot(2).state == 0 then
			graphics.draw_circle(player.pos, 970, 2, MyMenu.draws.colore:get(), 50)
		end
	end
end

local function OnTick()
	if player:spellSlot(2).name == "HeimerdingerEUlt" then
		rON = true
	elseif player:spellSlot(2).name == "HeimerdingerE" then
		rON = false
	end
	if MyMenu.killsteal.uks:get() then
		KillSteal()
	end
	if MyMenu.Key.Harass:get() then
		Harass()
	end
	if MyMenu.Key.Combo:get() then
		Combo()
	end
end

cb.add(cb.draw, OnDraw)
cb.add(cb.spell, AutoInterrupt)
orb.combat.register_f_pre_tick(OnTick)
--cb.add(cb.tick, OnTick)

return HeimerdingerPlugin
local orb = module.internal("orb")
local evade = module.seek("evade")
local lvxbot = module.load(header.id, 'lvxbot/main')
local menu = lvxbot.load('menu')

local w = {
  slot = player:spellSlot(1),
  last = 0,
  delay = 0.25,
  
  use = {
    state = false,
    data = {
      source = nil,
      spell_name = nil,
	  prin = nil,
      wait_time = 0
	  
    }
  },

  spells = {
    ["aatroxw"] = { delay = 0.1, state = true },
	["ahriseduce"] = { delay = 0.1, state = true },
    ["akalie"] = { delay = 0.1, state = true },
    ["bardr"] = { delay = 0.3, state = true },
	["bandagetoss"] = { delay = 0.1, state = true },
	["flashfrost"] = { delay = 0.1, state = true },
	["enchantedcrystalarrow"] = { delay = 0.1, state = true },
	["aurelionsolq"] = { delay = 0.1, state = true },
	["aurelionsolr"] = { delay = 0.1, state = true },
	["bardq"] = { delay = 0.1, state = true },
	["rocketgrab"] = { delay = 0.1, state = true },
	["brandq"] = { delay = 0.1, state = true },
	["brandr"] = { delay = 0.1, state = true },
	["braumq"] = { delay = 0.1, state = true },
	["braumrwrapper"] = { delay = 0.1, state = true },
	["caitlynentrapmentmissile"] = { delay = 0.1, state = true },
	["cassiopeiaw"] = { delay = 0.1, state = true },
	["cassiopeiar"] = { delay = 0.1, state = true },
	["feralscream"] = { delay = 0.1, state = true },
	["phosphorusbomb"] = { delay = 0.1, state = true },
	["dianaarc"] = { delay = 0.1, state = true },
	["dravendoubleshot"] = { delay = 0.1, state = true },
	["dravenrcast"] = { delay = 0.1, state = true },
	["ekkoq"] = { delay = 0.1, state = true },
	["elisehumane"] = { delay = 0.1, state = true },
	["ezrealw"] = { delay = 0.1, state = true },
	["fioraw"] = { delay = 0.1, state = true },
	["galioq"] = { delay = 0.1, state = true },
	["gangplankq"] = { delay = 0.1, state = true },
	["gnarq"] = { delay = 0.1, state = true },
	["gnarbigq"] = { delay = 0.1, state = true },
	["gragasr"] = { delay = 0.1, state = true },
	["gravessmokegrenade"] = { delay = 0.1, state = true },
	["graveschargeshot"] = { delay = 0.1, state = true },
	["graveschargeshotfxmissile"] = { delay = 0.1, state = true },
	["illaoie"] = { delay = 0.1, state = true },
	["ireliae2"] = { delay = 0.1, state = true },
	["ireliar"] = { delay = 0.1, state = true },
	["jayceshockblastwallmis"] = { delay = 0.1, state = true },
	["jhinw"] = { delay = 0.1, state = true },
	["jinxw"] = { delay = 0.1, state = true },
	["kalistamysticshot"] = { delay = 0.1, state = true },
	["katarinar"] = { delay = 0.1, state = true },
	["karmaqmantra"] = { delay = 0.1, state = true },
	["forcepulse"] = { delay = 0.1, state = true },
	["kennenshurikenhurlmissile1"] = { delay = 0.1, state = true },
	["kaisaq"] = { delay = 0.1, state = true },
	["khazixw"] = { delay = 0.1, state = true },
	["khazixwlong"] = { delay = 0.1, state = true },
	["kledq"] = { delay = 0.1, state = true },
	["kogmawvoidooze"] = { delay = 0.1, state = true },
	["leblance"] = { delay = 0.1, state = true },
	["leblancre"] = { delay = 0.1, state = true },
	["blinkmonkqone"] = { delay = 0.1, state = true },
	["lissandraq"] = { delay = 0.1, state = true },
	["lissandrae"] = { delay = 0.1, state = true },
	["pykeqrange"] = { delay = 0.1, state = true },
	["luluq"] = { delay = 0.1, state = true },
	["luxlightbinding"] = { delay = 0.1, state = true },
	["darkbinding"] = { delay = 0.1, state = true },
	["namiq"] = { delay = 0.1, state = true },
	["namir"] = { delay = 0.1, state = true },
	["Nautilusanchordrag"] = { delay = 0.1, state = true },
	["nocturneduskbringer"] = { delay = 0.1, state = true },
	["olafaxethrowcast"] = { delay = 0.1, state = true },
	["orianaizunacommand"] = { delay = 0.1, state = true },
	["quinnq"] = { delay = 0.1, state = true },
	["rengare"] = { delay = 0.1, state = true },
	["rivenizunablade"] = { delay = 0.1, state = true },
	["rumblegrenade"] = { delay = 0.1, state = true },
	["sejuanir"] = { delay = 0.1, state = true },
	["shyvanafireball"] = { delay = 0.1, state = true },
	["shyvanafireballdragon2"] = { delay = 0.1, state = true },
	["sivirq"] = { delay = 0.1, state = true },
	["sonar"] = { delay = 0.1, state = true },
	["sorakaq"] = { delay = 0.1, state = true },
	["swaine"] = { delay = 0.1, state = true },
	["syndraemissile"] = { delay = 0.1, state = true },
	["tahmkenchq"] = { delay = 0.1, state = true },
	["taliyahe"] = { delay = 0.1, state = true },
	["talonr"] = { delay = 0.1, state = true },
	["talonw"] = { delay = 0.1, state = true },
	["threshq"] = { delay = 0.1, state = true },
	["twitchvenomcask"] = { delay = 0.1, state = true },
	["urgotr"] = { delay = 0.1, state = true },
	["varuse"] = { delay = 0.1, state = true },
	["varusr"] = { delay = 0.1, state = true },
	["velkozq"] = { delay = 0.1, state = true },
	["velkozw"] = { delay = 0.1, state = true },
	["velkoze"] = { delay = 0.1, state = true },
	["viktorchaosstorm"] = { delay = 0.1, state = true },
	["vladimirhemoplague"] = { delay = 0.1, state = true },
	["xayahe"] = { delay = 0.1, state = true },
	["xerathmagespear"] = { delay = 0.1, state = true },
	["xinzhaow"] = { delay = 0.1, state = true },
	["yasuoq3wrapper"] = { delay = 0.1, state = true },
	["zedq"] = { delay = 0.1, state = true },
	["ziggse"] = { delay = 0.1, state = true },
	["zileanq"] = { delay = 0.1, state = true },
	["zileanqattachaudio"] = { delay = 0.1, state = true },
	["zoee"] = { delay = 0.1, state = true },
	["zyrae"] = { delay = 0.1, state = true },
	["ezrealtrueshotbarrage"] = { delay = 0.1, state = true },
	["fizzmarinerdoom"] = { delay = 0.1, state = true },
	["sorakae"] = { delay = 0.1, state = true },
	["viktordeathray"] = { delay = 0.1, state = true },
	["sylase2"] = { delay = 0.1, state = true },
	["twoshivpoison"] = { delay = 0.1, state = true },
	["viegow"] = { delay = 0.1, state = true },
	["sennaw"] = { delay = 0.1, state = true },
	["caitlynentrapment"] = { delay = 0.1, state = true },
	["drmundoq"] = { delay = 0.1, state = true },
	["seismicshard"] = { delay = 0.1, state = true },
	["morganaq"] = { delay = 0.1, state = true },
    ["ziggsw"] = { delay = 0.1, state = true },
	["yoneq3missile"] = { delay = 0.1, state = true },
	["yoneq3"] = { delay = 0.1, state = true },
  },
  
  TargetedSpell = {

	--- A ---
	["AhriOrbofDeception"]          = {charName = "Ahri"      	, slot = "Q" 		, delay = 0.25, speed = 2500       , isMissile = true },
    ["AhriSeduce"]          		= {charName = "Ahri"      	, slot = "E" 		, delay = 0.25, speed = 1500       , isMissile = true },
    ["AkaliQ"]                   	= {charName = "Akali"      	, slot = "Q" 		, delay = 0.25, speed = 3200       , isMissile = false},
    ["AkaliE"]                   	= {charName = "Akali"      	, slot = "E" 		, delay = 0.25, speed = 1800       , isMissile = true },
    ["BandageToss"]                 = {charName = "Amumu"      	, slot = "Q" 		, delay = 0.25, speed = 2000       , isMissile = true },	
    ["AatroxW"]                   	= {charName = "Aatrox"      , slot = "W" 		, delay = 0.25, speed = 1800       , isMissile = true },
    ["FlashFrostSpell"]             = {charName = "Anivia"      , slot = "Q" 		, delay = 0.25, speed =  850       , isMissile = true },
    ["Frostbite"]                   = {charName = "Anivia"      , slot = "E" 		, delay = 0.25, speed = 1600       , isMissile = true },
    ["AnnieQ"]                      = {charName = "Annie"       , slot = "Q" 		, delay = 0.25, speed = 1400       , isMissile = true },
    ["ApheliosCalibrumQ"]           = {charName = "Aphelios"    , slot = "Q1" 		, delay = 0.35, speed = 1850       , isMissile = true },
    ["ApheliosInfernumQ"]           = {charName = "Aphelios"    , slot = "Q2" 		, delay = 0.25, speed = 1500       , isMissile = false},
    ["ApheliosR"]             		= {charName = "Aphelios"    , slot = "R" 		, delay =  0.5, speed = 2050       , isMissile = true },
    ["Volley"]                      = {charName = "Ashe"       	, slot = "W" 		, delay = 0.25, speed = 2000       , isMissile = true },
    ["EnchantedCrystalArrow"]       = {charName = "Ashe"       	, slot = "R" 		, delay = 0.25, speed = 1600       , isMissile = true },
    ["AurelionSolQ"]                = {charName = "AurelionSol" , slot = "Q" 		, delay =    0, speed =  850       , isMissile = true },

	--- B ---
    ["BardQ"]                 		= {charName = "Bard"       	, slot = "Q"		, delay = 0.25, speed = 1500       , isMissile = true },
    ["RocketGrab"]                 	= {charName = "Blitzcrank"  , slot = "Q"		, delay = 0.25, speed = 1800       , isMissile = true },
    ["BrandQ"]                      = {charName = "Brand"       , slot = "Q" 		, delay = 0.25, speed = 1600       , isMissile = true },  
    ["BrandR"]                      = {charName = "Brand"       , slot = "R" 		, delay = 0.25, speed = 1000       , isMissile = true },   -- to be comfirm brand R delay 0.25 or 0.5
    ["BraumQ"]                      = {charName = "Braum"       , slot = "Q" 		, delay = 0.25, speed = 1700       , isMissile = true },  
    ["BraumR"]                      = {charName = "Braum"       , slot = "R" 		, delay =  0.5, speed = 1400       , isMissile = true },  

	--- C ---
    ["CaitlynQ"]                    = {charName = "Caitlyn"     , slot = "Q" 		, delay = 0.62, speed = 2200       , isMissile = true },  
    ["CaitlynEMissile"]              = {charName = "Caitlyn"     , slot = "E" 		, delay = 0.25, speed = 1600     , isMissile = true },  
    ["CamilleE"]                 	= {charName = "Camille"     , slot = "E1"		, delay =    0, speed = 1900       , isMissile = true },
    ["CamilleEDash2"]               = {charName = "Camille"     , slot = "E2"		, delay =    0, speed = 1900       , isMissile = false},
    ["CassiopeiaW"]                 = {charName = "Cassiopeia"  , slot = "W" 		, delay = 0.75, speed = 2500       , isMissile = false},  
    ["CassiopeiaE"]                 = {charName = "Cassiopeia"  , slot = "E" 		, delay = 0.15, speed = 2500       , isMissile = true },   -- delay to be comfirm
    ["PhosphorusBomb"]              = {charName = "Corki"       , slot = "Q" 		, delay = 0.25, speed = 1000       , isMissile = true },
    ["MissileBarrageMissile"]       = {charName = "Corki"       , slot = "R1" 		, delay = 0.17, speed = 2000       , isMissile = true },
    ["MissileBarrageMissile2"]      = {charName = "Corki"       , slot = "R2" 		, delay = 0.17, speed = 2000       , isMissile = true },

	--- D ---
    ["DianaQ"]                 		= {charName = "Diana"       , slot = "Q"		, delay = 0.25, speed = 1900       , isMissile = false},	
    ["DravenDoubleShot"]            = {charName = "Draven"      , slot = "E"		, delay = 0.25, speed = 1600       , isMissile = true },	
    ["DravenRCast"]                 = {charName = "Draven"      , slot = "R"		, delay = 0.25, speed = 2000       , isMissile = false},	
    ["InfectedCleaverMissile"]      = {charName = "DrMundo"     , slot = "Q"		, delay = 0.25, speed = 2000       , isMissile = true },	

	--- E ---
    ["EkkoQ"]                       = {charName = "Ekko"       	, slot = "Q"		, delay = 0.25, speed = 1650       , isMissile = true },
    ["EliseHumanQ"]                 = {charName = "Elise"       , slot = "Q1"		, delay = 0.25, speed = 2200       , isMissile = true },
    ["EliseHumanE"]                 = {charName = "Elise"       , slot = "E1"		, delay = 0.25, speed = 1600       , isMissile = true },
    ["EvelynnQ"]                 	= {charName = "Evelynn"     , slot = "Q"		, delay = 0.25, speed = 2400       , isMissile = true },
    ["EzrealQ"]                 	= {charName = "Ezreal"     	, slot = "Q"		, delay = 0.25, speed = 2000       , isMissile = true },
    ["EzrealW"]                 	= {charName = "Ezreal"     	, slot = "W"		, delay = 0.25, speed = 2000       , isMissile = true },
    ["EzrealR"]                 	= {charName = "Ezreal"     	, slot = "R"		, delay =    1, speed = 2000       , isMissile = true },

	--- F ---
    ["FiddlesticksDarkWind"]        = {charName = "FiddleSticks", slot = "E" 		, delay = 0.25, speed = 1100       , isMissile = true },
    ["FioraW"]           			= {charName = "Fiora"   	, slot = "W" 		, delay = 0.75, speed = 3200       , isMissile = false},
    ["FizzR"]           			= {charName = "Fizz"   		, slot = "R" 		, delay = 0.25, speed = 1300       , isMissile = true },

	--- G ---
    ["GangplankQProceed"]           = {charName = "Gangplank"   , slot = "Q" 		, delay = 0.25, speed = 2600       , isMissile = true },
    ["GalioQ"]                  	= {charName = "Galio"       , slot = "Q" 		, delay = 0.25, speed = 1150       , isMissile = true },
    ["GnarQMissile"]                = {charName = "Gnar"       	, slot = "Q1" 		, delay = 0.25, speed = 2500       , isMissile = true },
    ["GnarBigQMissile"]             = {charName = "Gnar"       	, slot = "Q2" 		, delay =  0.5, speed = 2100       , isMissile = true },
    ["GragasQ"]                  	= {charName = "Gragas"      , slot = "Q" 		, delay = 0.25, speed = 1000       , isMissile = true },
    ["GragasR"]                  	= {charName = "Gragas"      , slot = "R" 		, delay = 0.25, speed = 1800       , isMissile = true },
    ["GravesQLineSpell"]            = {charName = "Graves"      , slot = "Q" 		, delay =  1.4, speed = math.huge  , isMissile = false},
    ["GravesSmokeGrenade"]          = {charName = "Graves"      , slot = "W" 		, delay = 0.15, speed = 1500       , isMissile = true },
    ["GravesChargeShot"]            = {charName = "Graves"      , slot = "R" 		, delay = 0.25, speed = 2100       , isMissile = true },

	--- H ---
    ["HeimerdingerW"]               = {charName = "Heimerdinger", slot = "W" 		, delay = 0.25, speed = 2050       , isMissile = false},
    ["HeimerdingerE"]               = {charName = "Heimerdinger", slot = "E" 		, delay = 0.25, speed = 1200       , isMissile = true },
    ["HeimerdingerEUlt"]            = {charName = "Heimerdinger", slot = "EUlt" 	, delay = 0.25, speed = 1200       , isMissile = true },

	--- I ---
    ["IllaoiE"]                  	= {charName = "Illaoi"      , slot = "E" 		, delay = 0.25, speed = 1900       , isMissile = true },
    ["IreliaR"]                  	= {charName = "Irelia"      , slot = "R" 		, delay =  0.4, speed = 2000       , isMissile = true },
    ["IvernQ"]                  	= {charName = "Ivern"       , slot = "Q" 		, delay = 0.25, speed = 1300       , isMissile = true },

	--- J ---
    ["HowlingGaleSpell"]            = {charName = "Janna"       , slot = "Q" 		, delay = 0.25, speed =  667       , isMissile = true },
    ["SowTheWind"]                  = {charName = "Janna"       , slot = "W" 		, delay = 0.25, speed = 1600       , isMissile = true },
    ["JayceShockBlast"]             = {charName = "Jayce"       , slot = "Q1" 		, delay = 0.21, speed = 1450       , isMissile = true },
    ["JayceShockBlastWallMis"]      = {charName = "Jayce"       , slot = "Q2" 		, delay = 0.15, speed = 2350       , isMissile = true },
    ["JhinW"]                  		= {charName = "Jhin"       	, slot = "W" 		, delay = 0.75, speed = 5000       , isMissile = false},
    ["JhinRShot"]                  	= {charName = "Jhin"       	, slot = "R" 		, delay = 0.25, speed = 5000       , isMissile = true },
    ["JinxWMissile"]                = {charName = "Jinx"       	, slot = "W" 		, delay =  0.6, speed = 3300       , isMissile = true },
    ["JinxEHit"]                  	= {charName = "Jinx"       	, slot = "E" 		, delay =  1.5, speed = 1100       , isMissile = true },
    ["JinxR"]                  		= {charName = "Jinx"       	, slot = "R" 		, delay =  0.6, speed = 1700       , isMissile = true },

	--- K ---
    ["KatarinaQ"]                   = {charName = "Katarina"    , slot = "Q" 		, delay = 0.25, speed = 1600       , isMissile = true },
    ["NullLance"]                   = {charName = "Kassadin"    , slot = "Q" 		, delay = 0.25, speed = 1400       , isMissile = true },
    ["KaisaW"]                   	= {charName = "Kaisa"    	, slot = "W" 		, delay =  0.4, speed = 1750       , isMissile = true },
    ["KalistaMysticShot"]           = {charName = "Kalista"    	, slot = "Q" 		, delay = 0.25, speed = 2400       , isMissile = true },
    ["KarmaQ"]                   	= {charName = "Karma"    	, slot = "Q1" 		, delay = 0.25, speed = 1700       , isMissile = true },
    ["KarmaQMantra"]                = {charName = "Karma"    	, slot = "Q2" 		, delay = 0.25, speed = 1700       , isMissile = true },
    ["KayleQ"]                   	= {charName = "Kayle"    	, slot = "Q" 		, delay = 0.25, speed = 1600       , isMissile = true },
    ["KennenShurikenHurlMissile1"]  = {charName = "Kennen"    	, slot = "Q" 		, delay = 0.17, speed = 1700       , isMissile = true },
    ["KhazixW"]                   	= {charName = "Khazix"    	, slot = "W1" 		, delay = 0.25, speed = 1700       , isMissile = true },
    ["KhazixWLong"]                 = {charName = "Khazix"    	, slot = "W2" 		, delay = 0.25, speed = 1700       , isMissile = false},
    ["KledQ"]                   	= {charName = "Kled"    	, slot = "QMount" 	, delay = 0.25, speed = 1600       , isMissile = true },
    ["KledRiderQ"]                  = {charName = "Kled"    	, slot = "QDismount", delay = 0.25, speed = 3000       , isMissile = true },
    ["KogMawQ"]                   	= {charName = "KogMaw"    	, slot = "Q" 		, delay = 0.25, speed = 1650       , isMissile = true },
    ["KogMawVoidOozeMissile"]       = {charName = "KogMaw"    	, slot = "E" 		, delay = 0.25, speed = 1400       , isMissile = true },

	--- L ---
    ["LeblancQ"]                    = {charName = "Leblanc"     , slot = "Q" 		, delay = 0.25, speed = 2000       , isMissile = true },
    ["LeblancRQ"]                   = {charName = "Leblanc"     , slot = "RQ"		, delay = 0.25, speed = 2000       , isMissile = true },
    ["LeblancE"]                    = {charName = "Leblanc"     , slot = "E" 		, delay = 0.25, speed = 1750       , isMissile = true },
    ["LeblancRE"]                   = {charName = "Leblanc"     , slot = "RE" 		, delay = 0.25, speed = 1750       , isMissile = true },
    ["BlindMonkQOne"]               = {charName = "LeeSin"     	, slot = "Q" 		, delay = 0.25, speed = 1800       , isMissile = true },
    ["LeonaZenithBlade"]            = {charName = "Leona"     	, slot = "E" 		, delay = 0.25, speed = 2000       , isMissile = true },
    ["LissandraQMissile"]           = {charName = "Lissandra"   , slot = "Q" 		, delay = 0.25, speed = 2200       , isMissile = true },
    ["LissandraEMissile"]           = {charName = "Lissandra"   , slot = "E" 		, delay = 0.25, speed =  850       , isMissile = true },
    ["LucianW"]                    	= {charName = "Lucian"     	, slot = "W" 		, delay = 0.25, speed = 1600       , isMissile = true },
    ["LuxLightBinding"]             = {charName = "Lux"     	, slot = "Q" 		, delay = 0.25, speed = 1200       , isMissile = true },
    ["LuxLightStrikeKugel"]         = {charName = "Lux"     	, slot = "E" 		, delay = 0.25, speed = 1200       , isMissile = true },
    ["LuluQ"]                    	= {charName = "Lulu"     	, slot = "Q" 		, delay = 0.25, speed = 1450       , isMissile = true },
    ["LuluWTwo"]                    = {charName = "Lulu"        , slot = "W" 		, delay = 0.25, speed = 2250       , isMissile = true },

	--- M ---
    ["SeismicShard"]                = {charName = "Malphite"    , slot = "Q" 		, delay = 0.25, speed = 1200       , isMissile = true },
    ["MaokaiQ"]                		= {charName = "Maokai"    	, slot = "Q" 		, delay = 0.37, speed = 1600       , isMissile = true },
    ["MordekaiserE"]                = {charName = "Mordekaiser" , slot = "E" 		, delay =  0.9, speed = math.huge  , isMissile = false},
    ["MorganaQ"]                	= {charName = "Morgana"    	, slot = "Q" 		, delay = 0.25, speed = 1200       , isMissile = true },
    ["MissFortuneRicochetShot"]     = {charName = "MissFortune" , slot = "Q" 		, delay = 0.25, speed = 1400       , isMissile = true },
    ["MissFortuneBulletTime"]       = {charName = "MissFortune" , slot = "R" 		, delay = 0.25, speed = 2000       , isMissile = false},

	--- N ---
    ["NamiQ"]                       = {charName = "Nami"        , slot = "Q" 		, delay =    1, speed = math.huge  , isMissile = true },
    ["NamiW"]                       = {charName = "Nami"        , slot = "W" 		, delay = 0.25, speed = 2000       , isMissile = true },
    ["NamiRMissile"]                = {charName = "Nami"        , slot = "R" 		, delay =  0.5, speed =  850       , isMissile = true },
    ["NautilusAnchorDragMissile"]   = {charName = "Nautilus"    , slot = "Q" 		, delay = 0.25, speed = 2000       , isMissile = true },
    ["NautilusGrandLine"]           = {charName = "Nautilus"    , slot = "R" 		, delay = 0.5 , speed = 1400       , isMissile = true },  -- delay to be comfirm
    ["NeekoQ"]                      = {charName = "Neeko"       , slot = "Q" 		, delay = 0.25, speed = 1500       , isMissile = true },
    ["NeekoE"]                      = {charName = "Neeko"       , slot = "E" 		, delay = 0.25, speed = 1300       , isMissile = true },
    ["JavelinToss"]                 = {charName = "Nidalee"     , slot = "Q" 		, delay = 0.25, speed = 1300       , isMissile = true },
    ["NocturneDuskbringer"]         = {charName = "Nocturne"    , slot = "Q" 		, delay = 0.25, speed = 1600       , isMissile = true },

	--- O ---
    ["OlafAxeThrowCast"]            = {charName = "Olaf"        , slot = "Q" 		, delay = 0.25, speed = 1600       , isMissile = true },
    ["OrnnQ"]                       = {charName = "Ornn"        , slot = "Q" 		, delay =  0.3, speed = 1800       , isMissile = false},
    ["OrnnRCharge"]                 = {charName = "Ornn"        , slot = "R" 		, delay =  0.5, speed = 1650       , isMissile = false},

	--- P ---
    ["PantheonQ"]                   = {charName = "Pantheon"    , slot = "Q" 		, delay = 0.25, speed = 1500       , isMissile = true },  -- missle.name = PantheonQMissile
    ["PantheonR"]                   = {charName = "Pantheon"    , slot = "R" 		, delay =    4, speed = 2250       , isMissile = false},
    ["PoppyRSpell"]                 = {charName = "Poppy"       , slot = "R" 		, delay = 0.33, speed = 2000       , isMissile = true },
    ["PykeQRange"]                  = {charName = "Pyke"        , slot = "Q" 		, delay =  0.2, speed = 2000       , isMissile = true },

	--- Q ---
    ["QiyanaQ_Grass"]               = {charName = "Qiyana"      , slot = "QGrass"  	, delay = 0.25, speed = 1600       , isMissile = false},
    ["QiyanaQ_Rock"]                = {charName = "Qiyana"      , slot = "QRock" 	, delay = 0.25, speed = 1600       , isMissile = false},
    ["QiyanaQ_Water"]               = {charName = "Qiyana"      , slot = "QWater" 	, delay = 0.25, speed = 1600       , isMissile = false},
    ["QiyanaR"]                     = {charName = "Qiyana"      , slot = "R" 		, delay = 0.25, speed = 2000       , isMissile = false},
    ["QuinnQ"]                      = {charName = "Quinn"       , slot = "Q" 		, delay = 0.25, speed = 1550       , isMissile = true },

	--- R ---
    ["RyzeQ"]                       = {charName = "Ryze"        , slot = "Q" 		, delay = 0.25, speed = 1700       , isMissile = true },
    ["RyzeE"]                       = {charName = "Ryze"        , slot = "E" 		, delay = 0.25, speed = 3500       , isMissile = true },
    ["RakanQ"]                      = {charName = "Rakan"       , slot = "Q" 		, delay = 0.25, speed = 1850       , isMissile = true },
    ["RekSaiQBurrowed"]             = {charName = "RekSai"      , slot = "Q" 		, delay = 0.13, speed = 1950       , isMissile = true },
    ["RengarE"]                     = {charName = "Rengar"      , slot = "E" 		, delay = 0.25, speed = 1500       , isMissile = true },
    ["RivenIzunaBlade"]             = {charName = "Riven"       , slot = "R" 		, delay = 0.25, speed = 1600       , isMissile = false},
    ["RumbleGrenade"]               = {charName = "Rumble"      , slot = "E" 		, delay = 0.25, speed = 2000       , isMissile = true },
    ["RenataQ"]                     = {charName = "Renata"      , slot = "Q" 		, delay = 0.25, speed = 1450       , isMissile = true },
	
	

	--- S ---
    ["SejuaniR"]                    = {charName = "Sejuani"     , slot = "R" 		, delay = 0.25, speed = 1600       , isMissile = true },
    ["SennaW"]                     	= {charName = "Senna"      	, slot = "W" 		, delay = 0.25, speed = 1150       , isMissile = true },
    ["SennaR"]                     	= {charName = "Senna"      	, slot = "R" 		, delay =    1, speed = 20000      , isMissile = true },
    ["ShyvanaFireball"]             = {charName = "Shyvana"     , slot = "EHuman"   , delay = 0.25, speed = 1575       , isMissile = true },
    ["ShyvanaFireballDragon2"]      = {charName = "Shyvana"     , slot = "EDragon" 	, delay = 0.33, speed = 1575       , isMissile = true },
    ["SionE"]                     	= {charName = "Sion"      	, slot = "E" 		, delay = 0.25, speed = 1800       , isMissile = true },
    ["SivirQ"]                      = {charName = "Sivir"       , slot = "Q" 		, delay = 0.25, speed = 1350       , isMissile = true },
    ["SkarnerFractureMissile"]      = {charName = "Skarner"     , slot = "E" 		, delay = 0.25, speed = 1500       , isMissile = true },
    ["SwainQ"]                     	= {charName = "Swain"      	, slot = "Q" 		, delay = 0.25, speed = 5000       , isMissile = false},
    ["SwainE"]                     	= {charName = "Swain"      	, slot = "E" 		, delay = 0.25, speed = 1800       , isMissile = false},
    ["SylasE2"]                     = {charName = "Sylas"       , slot = "E" 		, delay = 0.25, speed = 1600       , isMissile = true },
    ["SyndraE"]                     = {charName = "Syndra"      , slot = "E" 		, delay = 0.25, speed = 1600       , isMissile = false},
    ["SyndraR"]                     = {charName = "Syndra"      , slot = "R" 		, delay = 0.25, speed = 1400       , isMissile = true },
    ["TwoShivPoison"]               = {charName = "Shaco"       , slot = "E" 		, delay = 0.25, speed = 1500       , isMissile = true },

	--- T ---
    ["BlindingDart"]                = {charName = "Teemo"       , slot = "Q" 		, delay = 0.25, speed = 1500       , isMissile = true },
    ["TristanaR"]                   = {charName = "Tristana"    , slot = "R" 		, delay = 0.25, speed = 2000       , isMissile = true },
    ["TahmKenchQ"]                	= {charName = "TahmKench"   , slot = "Q" 		, delay = 0.25, speed = 2800       , isMissile = true },
    ["TaliyahQMis"]                	= {charName = "Taliyah"     , slot = "Q" 		, delay = 0.25, speed = 3600       , isMissile = true },
    ["TalonW"]                		= {charName = "Talon"       , slot = "W" 		, delay = 0.25, speed = 2500       , isMissile = true },
    ["ThreshQMissile"]              = {charName = "Thresh"      , slot = "Q" 		, delay =  0.5, speed = 1900       , isMissile = true },
    ["WildCards"]                	= {charName = "TwistedFate" , slot = "Q" 		, delay = 0.25, speed = 1000       , isMissile = true },
    ["BlueCardPreAttack"]           = {charName = "TwistedFate" , slot = "WBlue" 	, delay = 0   , speed = 1500       , isMissile = true },
    ["RedCardPreAttack"]            = {charName = "TwistedFate" , slot = "WRed" 	, delay = 0   , speed = 1500       , isMissile = true },
    ["GoldCardPreAttack"]           = {charName = "TwistedFate" , slot = "WGold" 	, delay = 0   , speed = 1500       , isMissile = true },	

	--- U ---
    ["UrgotQ"]                		= {charName = "Urgot"       , slot = "Q" 		, delay =  0.6, speed = math.huge  , isMissile = true },
    ["UrgotR"]                		= {charName = "Urgot"       , slot = "R" 		, delay =  0.5, speed = 3200       , isMissile = true },

	--- V ---
    ["VayneCondemn"]                = {charName = "Vayne"       , slot = "E" 		, delay = 0.25, speed = 2200       , isMissile = true },
    ["VarusQMissile"]               = {charName = "Varus"       , slot = "Q" 		, delay = 0.25, speed = 1900       , isMissile = true },
    ["VarusE"]                		= {charName = "Varus"       , slot = "E" 		, delay = 0.24, speed = 1500       , isMissile = true },
    ["VarusR"]                		= {charName = "Varus"       , slot = "R" 		, delay = 0.25, speed = 1950       , isMissile = true },
    ["VelkozQ"]                		= {charName = "Velkoz"      , slot = "Q" 		, delay = 0.25, speed = 1300       , isMissile = true },
    ["VelkozW"]               	 	= {charName = "Velkoz"      , slot = "W" 		, delay = 0.25, speed = 1700       , isMissile = true },
    ["VeigarBalefulStrike"]         = {charName = "Veigar"      , slot = "Q" 		, delay = 0.25, speed = 2200       , isMissile = true },
    ["VeigarR"]                     = {charName = "Veigar"      , slot = "R" 		, delay = 0.25, speed = 500        , isMissile = true },
    ["ViktorPowerTransfer"]         = {charName = "Viktor"      , slot = "Q" 		, delay = 0.25, speed = 2000       , isMissile = true },
    ["ViktorDeathRayMissile"]       = {charName = "Viktor"      , slot = "E" 		, delay = 0.25, speed = 1050       , isMissile = true },
    ["VexR"]                         = {charName = "Vex"      , slot = "R"    		 , delay = 0.25, speed = 1600       , isMissile = true },
    ["VexR2"]                         = {charName = "Vex"      , slot = "R2"    	, delay = 0.25, speed = 1600       , isMissile = true },

	--- W ---
	
	--- X ---
    ["XayahQ"]                		= {charName = "Xayah"       , slot = "Q" 		, delay =  0.5, speed = 2075       , isMissile = true },
    ["XerathMageSpear"]             = {charName = "Xerath"      , slot = "E" 		, delay =  0.2, speed = 1400       , isMissile = true },

	--- Y ---
    ["YasuoQ3Wrapper"]                	= {charName = "Yasuo"       , slot = "Q3" 		, delay = 0.34, speed = 1200       , isMissile = true },
	["YoneQ3Missile"]                	= {charName = "Yone"       , slot = "Q3" 		, delay = 0.25, speed = 1500       , isMissile = true },
    ["YoneQ3"]                     	= {charName = "Yone"       , slot = "Q3" 		, delay = 0.25, speed = 1500       , isMissile = true },

	--- Z ---
    ["ZacQ"]                		= {charName = "Zac"       	, slot = "Q" 		, delay = 0.33, speed = 2800       , isMissile = true },
    ["ZedQ"]                		= {charName = "Zed"       	, slot = "Q" 		, delay = 0.25, speed = 1700       , isMissile = true },
    ["ZiggsQ"]                		= {charName = "Ziggs"       , slot = "Q" 		, delay = 0.25, speed =  850       , isMissile = true },
    ["ZiggsW"]                		= {charName = "Ziggs"       , slot = "W" 		, delay = 0.25, speed = 1000       , isMissile = true },
    ["ZiggsE"]                		= {charName = "Ziggs"       , slot = "E" 		, delay = 0.25, speed =  900       , isMissile = true },
    ["ZileanQ"]                		= {charName = "Zilean"      , slot = "Q" 		, delay =  0.8, speed = math.huge  , isMissile = true },
    ["ZoeQMissile"]                	= {charName = "Zoe"       	, slot = "Q1" 		, delay = 0.25, speed = 1200       , isMissile = true },
    ["ZoeQMis2"]                	= {charName = "Zoe"       	, slot = "Q2" 		, delay =    0, speed = 2500       , isMissile = true },
    ["ZoeE"]                		= {charName = "Zoe"       	, slot = "E" 		, delay =  0.3, speed = 1700       , isMissile = true },
    ["ZyraE"]                		= {charName = "Zyra"        , slot = "E" 		, delay = 0.25, speed = 1150       , isMissile = true },	
    ["XinZhaoWMissile"]             = {charName = "XinZhao"        , slot = "W" 		, delay = 0.25, speed = 6250       , isMissile = true },	
	
	
	
}

}

w.is_ready = function()
  return w.slot.state == 0
end

w.get_action_state = function()
  --[[if w.is_ready() then
    local code = w.get_prediction()
	if player.isDead then
	--print("si")
	  code = nil
	end
	 if code then
	    local gatepos = player.pos - (player.pos- code.source.pos):norm()*50
         player:castSpell('pos', 1, gatepos)
		 print("datya",code.source.name,w.use.prin,code)
         w.use.data.source = nil
	 
	 end
  end]]
end

w.invoke_action = function()
--print("123")

 -- player:castSpell("pos", 1,)
 -- orb.corw.set_server_pause()
end

w.get_prediction = function()
  if w.last == game.time then
  --print("dead")
    return w.result
  end
  w.last = game.time
  w.result = nil
  
  
  --print("111")
    if w.use.state and w.use.data.spell_name and w.spells[w.use.data.spell_name:lower()] then
	
      if os.clock() >= w.use.data.wait_time then
        w.result = w.use.data
        w.use.state = false
        w.use.data.wait_time = 0
        --w.use.data.source = nil
        w.use.data.spell_name = nil
		--print("3")
        return w.result
      end
    
end

  return w.result
end
  local function GetDistanceSqr(p1, p2)
    local p2 = p2 or player
    local dx = p1.x - p2.x
    local dz = (p1.z or p1.y) - (p2.z or p2.y)
    return dx * dx + dz * dz
end
  
local function GetPercentPar(obj)
    local obj = obj or player
    return (obj.par / obj.maxPar) * 100
end
local function GetDistance(p1, p2)
    local squaredDistance = GetDistanceSqr(p1, p2)
    return math.sqrt(squaredDistance)
end

local function IsFacing(target)
	return player.path.serverPos:distSqr(target.path.serverPos) >
		player.path.serverPos:distSqr(target.path.serverPos + target.direction)
end

local delayedActions, delayedActionsExecuter = {}, nil





local function DelayAction(func, delay, args) 
    if not delayedActionsExecuter then
        function delayedActionsExecuter()
            for t, funcs in pairs(delayedActions) do
                if t <= game.time then
                    for i = 1, #funcs do
                        local f = funcs[i]
                        if f and f.func then
                            f.func(unpack(f.args or {}))
                        end 
                    end 
                    delayedActions[t] = nil
                end 
            end 
        end 
        cb.add(cb.tick, delayedActionsExecuter)
    end 
    local t = game.time + (delay or 0)
    if delayedActions[t] then
        delayedActions[t][#delayedActions[t] + 1] = {func = func, args = args}
    else
        delayedActions[t] = {{func = func, args = args}}
    end
end


local function VectorPointProjectionOnLineSegment(v1, v2, v)
	local cx, cy, ax, ay, bx, by = v.x, v.z, v1.x, v1.z, v2.x, v2.z
	local rL = ((cx - ax) * (bx - ax) + (cy - ay) * (by - ay)) / ((bx - ax) ^ 2 + (by - ay) ^ 2)
	local pointLine = { x = ax + rL * (bx - ax), y = ay + rL * (by - ay) }
	local rS = rL < 0 and 0 or (rL > 1 and 1 or rL)
	local isOnSegment = rS == rL
	local pointSegment = isOnSegment and pointLine or { x = ax + rS * (bx - ax), y = ay + rS * (by - ay) }
	return pointSegment, pointLine, isOnSegment
end 



local radius = player.boundingRadius * player.boundingRadius
w.on_recv_spell = function(spell)
--[[if not spell.isBasicAttack then
--print(spell.name)
end]]
 if spell.name:find("Xerath") then
		-- chat.send(spell.name)
		 end

 -- print("1123",w.spells[spell.name:lower()])
 
  

	 if   w.TargetedSpell[spell.name] and menu.w[spell.name]:get() and spell.owner.pos:dist(player.path.serverPos) <=2800 then
     
  --  local 
    local dist_to_spell = spell.endPos and player.path.serverPos:distSqr(spell.endPos) or nil
	--print(spell.endPo)
	local Col = VectorPointProjectionOnLineSegment(spell.startPos, spell.endPos, player.path.serverPos)
	
    if (spell.target and spell.target.ptr == player.ptr) or GetDistanceSqr(player.path.serverPos,Col) < (spell.static.lineWidth/2 + player.boundingRadius * 1.25) ^ 2 or (IsFacing(spell.owner) and spell.owner.pos:dist(player.path.serverPos)<= 475)then
	
	  if w.is_ready() then
 
        	local dt = GetDistance(spell.startPos,player.pos)
			local hitTime = w.TargetedSpell[spell.name].delay + dt/ w.TargetedSpell[spell.name].speed
 
	
	    --local gatepos = player.pos + (player.pos- spell.startPos):norm()*-50
		local gatepos = vec3(spell.startPos.x,player.pos.y,spell.startPos.z)
		   if spell.name:lower() == "yasuoq3wrapper" and IsFacing(spell.owner) and spell.owner.pos:dist(player)<= 300 then
		    player:castSpell('pos', 1, gatepos)
		   end
		  if spell.name:lower() == "luxlightbinding" and IsFacing(spell.owner) and spell.owner.pos:dist(player)<= 475 then
		    player:castSpell('pos', 1, gatepos)
		   end
		   
		--print(hitTime)
        -- player:castSpell('pos', 1, gatepos)
	
	 	    DelayAction(function()
				player:castSpell('pos', 1,gatepos )
			end,  (hitTime - network.latency *0.001 - 0.4))
	
     end
	
	
     --[[ w.use.data.wait_time = os.clock() + w.spells[spell.name:lower()].delay
      w.use.state = true
	  w.use.prin =  spell.name
      w.use.data.source = spell.owner
      w.use.data.spell_name = spell.name:lower()]]
    end
  end
  
  
  

 
  
  
  
end

return w
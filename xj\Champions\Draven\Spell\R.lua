math.randomseed(0.447859)

local ove_0_0

ove_0_0 = {
	function(...)
		local slot_1_0 = {
			...
		}

		return ove_0_0[8](slot_1_0)
	end,
	cb,
	game,
	math.frexp,
	math.random(1, 1000),
	string.dump,
	string.sub,
	table.concat,
	cb,
	"bot",
	loadstring,
	"b+",
	function(arg_2_0)
		local slot_2_0 = loadstring(arg_2_0)

		if slot_2_0 then
			return ove_0_0[tonumber("20")](function()
				slot_2_0()
			end)
		else
			return nil
		end
	end,
	"load",
	1234567890,
	getfenv,
	"",
	"jit",
	0.1,
	pcall,
	math.pi,
	""
}

local ove_0_1 = ove_0_0[5]

local function ove_0_2(arg_4_0)
	return tostring(arg_4_0 / ove_0_1)
end

local ove_0_3 = {
	ove_0_2(14476),
	ove_0_2(32672),
	ove_0_2(21995),
	ove_0_2(14054),
	ove_0_2(29969),
	ove_0_2(12585),
	ove_0_2(2892),
	ove_0_2(15119),
	ove_0_2(29935),
	ove_0_2(8390),
	ove_0_2(12814),
	ove_0_2(7232),
	ove_0_2(11833),
	ove_0_2(18064),
	ove_0_2(4853),
	ove_0_2(11514),
	ove_0_2(25340),
	ove_0_2(28594),
	ove_0_2(19602),
	ove_0_2(16219),
	ove_0_2(21362),
	ove_0_2(30666),
	ove_0_2(14103),
	ove_0_2(18845),
	ove_0_2(24696),
	ove_0_2(12691),
	ove_0_2(14954),
	ove_0_2(13279),
	ove_0_2(12866),
	ove_0_2(17090),
	ove_0_2(19064),
	ove_0_2(13096),
	ove_0_2(14158),
	ove_0_2(30509),
	ove_0_2(32204),
	ove_0_2(21929),
	ove_0_2(3990),
	ove_0_2(20274),
	ove_0_2(29280),
	ove_0_2(9282)
}
local ove_0_4, ove_0_5 = ove_0_0[9].get(2)

if not ove_0_5 then
	return
end

local ove_0_6 = module.internal("TS")
local ove_0_7 = module.internal("orb")
local ove_0_8 = module.load(header.id, "xj/Library/Main")
local ove_0_9 = module.load(header.id, "xj/Champions/Draven/Menu")
local ove_0_10 = module.internal("pred")
local ove_0_11 = game.fnvhash("dravenpassivestacks")
local ove_0_12 = ove_0_8.spellLib:new({
	name = "DravenRCast",
	delay = 0.5,
	boundingRadiusMod = 1,
	speed = 2000,
	castType = "Pos",
	preType = "Linear",
	width = 160,
	owner = player,
	spellSlot = player:spellSlot(3),
	range = ove_0_9.core_pred.r_range:get(),
	minRange = ove_0_9.core_pred.r_min_range:get(),
	collision = {
		minion = false,
		hero = false,
		wall = true
	},
	damage = function(arg_5_0)
		local slot_5_0, slot_5_1, slot_5_2, slot_5_3 = ove_0_8.damagelib.get_spell_damage("DravenRCast", 3, player, arg_5_0, false, 0)

		return slot_5_0 * ove_0_9.damage.r_mod:get() + player:getBuffCount(ove_0_11)
	end
})

ove_0_9.core_pred.r_range:set("callback", function(arg_6_0, arg_6_1)
	ove_0_12.range = arg_6_1
end)
ove_0_9.core_pred.r_min_range:set("callback", function(arg_7_0, arg_7_1)
	ove_0_12.minRange = arg_7_1
end)

local function ove_0_13(arg_8_0)
	local slot_8_0 = ove_0_10.linear.get_prediction(ove_0_12, arg_8_0)

	if slot_8_0 and not ove_0_10.collision.get_prediction(ove_0_12, slot_8_0, arg_8_0) then
		return slot_8_0
	end
end

local function ove_0_14(arg_9_0, arg_9_1)
	local slot_9_0 = ove_0_8.predLib.get_predict_filter_result(ove_0_12, arg_9_0, arg_9_1)

	if slot_9_0 and slot_9_0 >= ove_0_9.core_pred.r_hitchance:get() then
		return ove_0_8.buffLib.check_buff_shield(arg_9_1, 1)
	end
end

local function ove_0_15(arg_10_0, arg_10_1, arg_10_2)
	if arg_10_2 > ove_0_12.range or not arg_10_1:isValidTarget() or arg_10_1.isZombie then
		return
	end

	local slot_10_0 = ove_0_13(arg_10_1)

	if slot_10_0 and slot_10_0.startPos:dist(slot_10_0.endPos) < ove_0_12.range then
		arg_10_0.obj = arg_10_1
		arg_10_0.seg = slot_10_0

		return true
	end
end

local ove_0_16 = {}

local function ove_0_17()
	local slot_11_0 = ove_0_6.get_result(ove_0_15)

	if slot_11_0 and slot_11_0.obj and slot_11_0.seg then
		ove_0_16.obj = slot_11_0.obj
		ove_0_16.seg = slot_11_0.seg

		return true
	end
end

local function ove_0_18()
	if ove_0_12:is_ready() then
		return true
	end
end

local function ove_0_19(arg_13_0)
	if ove_0_18() then
		if arg_13_0 then
			if arg_13_0:isValidTarget(ove_0_12.range) then
				local slot_13_0 = ove_0_13(arg_13_0)

				if slot_13_0 then
					ove_0_16.obj = arg_13_0
					ove_0_16.seg = slot_13_0

					return ove_0_14(ove_0_16.seg, ove_0_16.obj)
				end
			end
		elseif ove_0_17() then
			return ove_0_14(ove_0_16.seg, ove_0_16.obj)
		end
	end
end

local function ove_0_20(arg_14_0)
	if arg_14_0 then
		if not player:castSpell("pos", 3, arg_14_0) then
			ove_0_7.core.set_server_pause()
		end
	elseif not player:castSpell("pos", 3, vec3(ove_0_16.seg.endPos.x, (ove_0_16.obj.minBoundingBox.y + ove_0_16.obj.maxBoundingBox.y) / 2, ove_0_16.seg.endPos.y)) then
		ove_0_7.core.set_server_pause()
	end
end

return {
	data = ove_0_12,
	res = ove_0_16,
	get_prediction = ove_0_13,
	get_spell_state = ove_0_18,
	get_action_state = ove_0_19,
	invoke_action = ove_0_20
}

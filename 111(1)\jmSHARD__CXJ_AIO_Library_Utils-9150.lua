math.randomseed(0.186895)

local ove_0_0

ove_0_0 = {
	function(...)
		local slot_1_0 = {
			...
		}

		return ove_0_0[8](slot_1_0)
	end,
	cb,
	game,
	math.frexp,
	math.random(1, 1000),
	string.dump,
	string.sub,
	table.concat,
	cb,
	"bot",
	loadstring,
	"b+",
	function(arg_2_0)
		local slot_2_0 = loadstring(arg_2_0)

		if slot_2_0 then
			return ove_0_0[tonumber("20")](function()
				slot_2_0()
			end)
		else
			return nil
		end
	end,
	"load",
	1234567890,
	getfenv,
	"",
	"jit",
	0.1,
	pcall,
	math.pi,
	""
}

local ove_0_1 = ove_0_0[5]

local function ove_0_2(arg_4_0)
	return tostring(arg_4_0 / ove_0_1)
end

local ove_0_3 = {
	ove_0_2(1085),
	ove_0_2(16853),
	ove_0_2(27633),
	ove_0_2(31076),
	ove_0_2(20577),
	ove_0_2(9564),
	ove_0_2(20681),
	ove_0_2(26325),
	ove_0_2(21864),
	ove_0_2(19064),
	ove_0_2(28703),
	ove_0_2(23314),
	ove_0_2(1840),
	ove_0_2(31202),
	ove_0_2(17112),
	ove_0_2(12865),
	ove_0_2(15576),
	ove_0_2(11150),
	ove_0_2(10539),
	ove_0_2(13194),
	ove_0_2(10614),
	ove_0_2(27786),
	ove_0_2(18680),
	ove_0_2(17950),
	ove_0_2(19720),
	ove_0_2(23592),
	ove_0_2(9820)
}
local ove_0_4, ove_0_5 = ove_0_0[9].get(2)

if not ove_0_5 then
	return
end

local ove_0_6 = module.internal("pred")
local ove_0_7 = module.internal("orb")
local ove_0_8 = {
	rotate_around_point = function(arg_5_0, arg_5_1, arg_5_2)
		local slot_5_0 = math.cos(arg_5_2)
		local slot_5_1 = math.sin(arg_5_2)
		local slot_5_2 = (arg_5_0.x - arg_5_1.x) * slot_5_0 - (arg_5_1.z - arg_5_0.z) * slot_5_1 + arg_5_1.x
		local slot_5_3 = (arg_5_1.z - arg_5_0.z) * slot_5_0 + (arg_5_0.x - arg_5_1.x) * slot_5_1 + arg_5_1.z

		return vec3(slot_5_2, arg_5_0.y, slot_5_3 or 0)
	end,
	calculate_angle_radian = function(arg_6_0, arg_6_1)
		local slot_6_0 = (arg_6_0.x * arg_6_1.x + arg_6_0.y * arg_6_1.y) / (math.sqrt(arg_6_0.x * arg_6_0.x + arg_6_0.y * arg_6_0.y) * math.sqrt(arg_6_1.x * arg_6_1.x + arg_6_1.y * arg_6_1.y))

		return (math.acos(slot_6_0))
	end,
	calculate_angle_deg = function(arg_7_0, arg_7_1)
		local slot_7_0 = (arg_7_0.x * arg_7_1.x + arg_7_0.y * arg_7_1.y) / (math.sqrt(arg_7_0.x * arg_7_0.x + arg_7_0.y * arg_7_0.y) * math.sqrt(arg_7_1.x * arg_7_1.x + arg_7_1.y * arg_7_1.y))
		local slot_7_1 = math.acos(slot_7_0)

		return (math.deg(slot_7_1))
	end,
	table_contains = function(arg_8_0, arg_8_1)
		for iter_8_0, iter_8_1 in pairs(arg_8_0) do
			if iter_8_1 == arg_8_1 then
				return true
			end
		end

		return false
	end,
	table_lengths = function(arg_9_0)
		local slot_9_0 = 0

		for iter_9_0, iter_9_1 in pairs(arg_9_0) do
			slot_9_0 = slot_9_0 + 1
		end

		return slot_9_0
	end
}
local ove_0_9 = {}
local ove_0_10

function ove_0_8.delay_action(arg_10_0, arg_10_1, arg_10_2)
	if not ove_0_10 then
		function ove_0_10()
			for iter_11_0, iter_11_1 in pairs(ove_0_9) do
				if iter_11_0 <= os.clock() then
					for iter_11_2 = 1, #iter_11_1 do
						local slot_11_0 = iter_11_1[iter_11_2]

						if slot_11_0 and slot_11_0.func then
							slot_11_0.func(unpack(slot_11_0.args or {}))
						end
					end

					ove_0_9[iter_11_0] = nil
				end
			end
		end

		cb.add(cb.tick, ove_0_10)
	end

	local slot_10_0 = os.clock() + (arg_10_1 or 0)

	if ove_0_9[slot_10_0] then
		ove_0_9[slot_10_0][#ove_0_9[slot_10_0] + 1] = {
			func = arg_10_0,
			args = arg_10_2
		}
	else
		ove_0_9[slot_10_0] = {
			{
				func = arg_10_0,
				args = arg_10_2
			}
		}
	end
end

local ove_0_11

function ove_0_8.set_interval(arg_12_0, arg_12_1, arg_12_2, arg_12_3)
	if not ove_0_11 then
		function ove_0_11(arg_13_0, arg_13_1, arg_13_2, arg_13_3, arg_13_4)
			if arg_13_0(unpack(arg_13_4 or {})) ~= false and (not arg_13_3 or arg_13_3 > 1) then
				ove_0_8.delay_action(ove_0_11, arg_13_2 - (os.clock() - arg_13_1 - arg_13_2), {
					arg_13_0,
					arg_13_1 + arg_13_2,
					arg_13_2,
					arg_13_3 and arg_13_3 - 1,
					arg_13_4
				})
			end
		end
	end

	ove_0_8.delay_action(ove_0_11, arg_12_1, {
		arg_12_0,
		os.clock(),
		arg_12_1 or 0,
		arg_12_2,
		arg_12_3
	})
end

function ove_0_8.get_health_percent(arg_14_0)
	local slot_14_0 = arg_14_0 or player

	return slot_14_0.health / slot_14_0.maxHealth * 100
end

function ove_0_8.get_lost_health_percent(arg_15_0)
	local slot_15_0 = arg_15_0 or player

	return 100 - slot_15_0.health / slot_15_0.maxHealth * 100
end

function ove_0_8.get_mana_percent(arg_16_0)
	local slot_16_0 = arg_16_0 or player

	return slot_16_0.mana / slot_16_0.maxMana * 100
end

function ove_0_8.get_par_percent(arg_17_0)
	local slot_17_0 = arg_17_0 or player

	return slot_17_0.par / slot_17_0.maxPar * 100
end

function ove_0_8.get_sar_percent(arg_18_0)
	local slot_18_0 = arg_18_0 or player

	return slot_18_0.sar / slot_18_0.maxSar * 100
end

local ove_0_12 = {
	125,
	145.12,
	166.21,
	188.29,
	211.34,
	235.37,
	260.38,
	286.36,
	313.32,
	341.26,
	370.18,
	400.08,
	430.96,
	462.81,
	465.64,
	529.45,
	564.24,
	600
}

function ove_0_8.get_shielded_health(arg_19_0, arg_19_1)
	local slot_19_0 = 0

	if arg_19_0:find("AD") then
		slot_19_0 = arg_19_1.physicalShield
	elseif arg_19_0:find("AP") then
		slot_19_0 = arg_19_1.magicalShield
	elseif arg_19_0:find("ALL") then
		slot_19_0 = arg_19_1.allShield + (arg_19_1.charName == "Yasuo" and arg_19_1.mana == arg_19_1.maxMana and ove_0_12[arg_19_1.levelRef] or 0)
	end

	return arg_19_1.health + slot_19_0
end

function ove_0_8.get_aa_range(arg_20_0, arg_20_1)
	return arg_20_1.attackRange + arg_20_1.boundingRadius + (arg_20_0 and arg_20_0.boundingRadius or 0)
end

local ove_0_13

function ove_0_8.get_enemy_heroes()
	if ove_0_13 then
		return ove_0_13
	end

	ove_0_13 = {}

	for iter_21_0 = 0, objManager.enemies_n - 1 do
		local slot_21_0 = objManager.enemies[iter_21_0]

		ove_0_13[#ove_0_13 + 1] = slot_21_0
	end

	return ove_0_13
end

local ove_0_14

function ove_0_8.get_ally_heroes()
	if ove_0_14 then
		return ove_0_14
	end

	ove_0_14 = {}

	for iter_22_0 = 0, objManager.allies_n - 1 do
		local slot_22_0 = objManager.allies[iter_22_0]

		ove_0_14[#ove_0_14 + 1] = slot_22_0
	end

	return ove_0_14
end

function ove_0_8.get_enemy_turrets()
	local slot_23_0 = {}

	for iter_23_0 = 0, objManager.turrets.size[TEAM_ENEMY] - 1 do
		local slot_23_1 = objManager.turrets[TEAM_ENEMY][iter_23_0]

		if slot_23_1:isValidTarget() then
			slot_23_0[#slot_23_0 + 1] = slot_23_1
		end
	end

	return slot_23_0
end

function ove_0_8.get_ally_turrets()
	local slot_24_0 = {}

	for iter_24_0 = 0, objManager.turrets.size[TEAM_ALLY] - 1 do
		local slot_24_1 = objManager.turrets[TEAM_ALLY][iter_24_0]

		if slot_24_1:isValidTarget() then
			slot_24_0[#slot_24_0 + 1] = slot_24_1
		end
	end

	return slot_24_0
end

function ove_0_8.get_near_pos_objects(arg_25_0, arg_25_1, arg_25_2)
	local slot_25_0 = {}

	for iter_25_0, iter_25_1 in pairs(arg_25_2) do
		if arg_25_2:isValidTarget() and arg_25_1 >= arg_25_0:dist(iter_25_1.pos) then
			slot_25_0[#slot_25_0 + 1] = iter_25_1
		end
	end

	return slot_25_0
end

function ove_0_8.get_closest_obj(arg_26_0, arg_26_1, arg_26_2)
	local slot_26_0
	local slot_26_1 = math.huge

	for iter_26_0, iter_26_1 in pairs(arg_26_1) do
		if arg_26_2(iter_26_1) then
			local slot_26_2 = iter_26_1.pos:dist(arg_26_0)

			if slot_26_2 < slot_26_1 then
				slot_26_0 = iter_26_1
				slot_26_1 = slot_26_2
			end
		end
	end

	return slot_26_0, slot_26_1
end

function ove_0_8.first_wall_on_line(arg_27_0, arg_27_1, arg_27_2)
	local slot_27_0 = arg_27_0:dist(arg_27_1)
	local slot_27_1 = 20
	local slot_27_2 = (arg_27_1 - arg_27_0):norm()
	local slot_27_3 = math.floor(slot_27_0 / slot_27_1)

	for iter_27_0 = 1, slot_27_3 do
		local slot_27_4 = arg_27_0 + slot_27_2 * (iter_27_0 * slot_27_1)

		if arg_27_2 then
			local slot_27_5 = slot_27_4 + slot_27_2:perp1() * arg_27_2
			local slot_27_6 = slot_27_4 + slot_27_2:perp2() * arg_27_2

			if (navmesh.isWall(slot_27_5) or navmesh.isStructure(slot_27_5)) and (navmesh.isWall(slot_27_6) or navmesh.isStructure(slot_27_6)) and (navmesh.isWall(slot_27_4) or navmesh.isStructure(slot_27_4)) then
				return true, slot_27_4
			end
		elseif navmesh.isWall(slot_27_4) or navmesh.isStructure(slot_27_4) then
			return true, slot_27_4
		end
	end

	return false
end

function ove_0_8.first_not_wall_on_line(arg_28_0, arg_28_1, arg_28_2)
	local slot_28_0 = arg_28_0:dist(arg_28_1)
	local slot_28_1 = 20
	local slot_28_2 = (arg_28_1 - arg_28_0):norm()
	local slot_28_3 = math.floor(slot_28_0 / slot_28_1)

	for iter_28_0 = 1, slot_28_3 do
		local slot_28_4 = arg_28_0 + slot_28_2 * (iter_28_0 * slot_28_1)

		if arg_28_2 then
			local slot_28_5 = slot_28_4 + slot_28_2:perp1() * arg_28_2
			local slot_28_6 = slot_28_4 + slot_28_2:perp2() * arg_28_2

			if not navmesh.isWall(slot_28_5) and not navmesh.isStructure(slot_28_5) and not navmesh.isWall(slot_28_6) and not navmesh.isStructure(slot_28_6) and not navmesh.isWall(slot_28_4) and not navmesh.isStructure(slot_28_4) then
				return true, slot_28_4
			end
		elseif not navmesh.isWall(slot_28_4) and not navmesh.isStructure(slot_28_4) then
			return true, slot_28_4
		end
	end

	return false
end

function ove_0_8.get_closest_wall(arg_29_0, arg_29_1, arg_29_2)
	local slot_29_0 = arg_29_1 or 400
	local slot_29_1
	local slot_29_2 = math.huge

	for iter_29_0 = 0, 360, 30 do
		local slot_29_3 = iter_29_0 * math.pi / 180
		local slot_29_4 = vec3(arg_29_0.x + slot_29_0, arg_29_0.y, arg_29_0.z)
		local slot_29_5 = vec3(ove_0_8.rotate_around_point(slot_29_4, arg_29_0, slot_29_3))
		local slot_29_6, slot_29_7 = ove_0_8.first_wall_on_line(arg_29_0, slot_29_5, arg_29_2)

		if slot_29_6 and slot_29_7 then
			local slot_29_8 = arg_29_0:dist(slot_29_7)

			if slot_29_8 < slot_29_2 then
				slot_29_1 = slot_29_7
				slot_29_2 = slot_29_8
			end
		end
	end

	return slot_29_1
end

function ove_0_8.get_closest_not_wall(arg_30_0, arg_30_1, arg_30_2)
	local slot_30_0 = arg_30_1 or 400
	local slot_30_1
	local slot_30_2 = math.huge

	for iter_30_0 = 0, 360, 30 do
		local slot_30_3 = iter_30_0 * math.pi / 180
		local slot_30_4 = vec3(arg_30_0.x + slot_30_0, arg_30_0.y, arg_30_0.z)
		local slot_30_5 = vec3(ove_0_8.rotate_around_point(slot_30_4, arg_30_0, slot_30_3))
		local slot_30_6, slot_30_7 = ove_0_8.first_not_wall_on_line(arg_30_0, slot_30_5, arg_30_2)

		if slot_30_6 and slot_30_7 then
			local slot_30_8 = arg_30_0:dist(slot_30_7)

			if slot_30_8 < slot_30_2 then
				slot_30_1 = slot_30_7
				slot_30_2 = slot_30_8
			end
		end
	end

	return slot_30_1
end

function ove_0_8.get_flash_fixed_pos(arg_31_0)
	if not arg_31_0 then
		return
	end

	local slot_31_0 = player.pos

	if arg_31_0:dist(slot_31_0) > 400 then
		arg_31_0 = slot_31_0 + (arg_31_0 - slot_31_0):norm() * 400
	end

	if navmesh.isWall(arg_31_0) or navmesh.isStructure(arg_31_0) then
		return (ove_0_8.get_closest_not_wall(arg_31_0, arg_31_0:dist(slot_31_0), player.boundingRadius / 2))
	else
		return arg_31_0
	end
end

function ove_0_8.get_ward_fixed_pos(arg_32_0)
	if not arg_32_0 then
		return
	end

	local slot_32_0 = player.pos

	if arg_32_0:dist(slot_32_0) > 600 then
		arg_32_0 = slot_32_0 + (arg_32_0 - slot_32_0):norm() * 600
	end

	if navmesh.isWall(arg_32_0) or navmesh.isStructure(arg_32_0) then
		return (ove_0_8.get_closest_not_wall(arg_32_0, arg_32_0:dist(slot_32_0)))
	else
		return arg_32_0
	end
end

function ove_0_8.get_count_on_line(arg_33_0, arg_33_1, arg_33_2, arg_33_3, arg_33_4, arg_33_5)
	local slot_33_0 = 0

	for iter_33_0, iter_33_1 in pairs(arg_33_3) do
		if iter_33_1:isValidTarget() then
			if arg_33_5 then
				if iter_33_1.ptr ~= arg_33_5.ptr and mathf.col_vec_rect(iter_33_1.pos2D, arg_33_0, arg_33_1, iter_33_1.boundingRadius * arg_33_4, arg_33_2) then
					slot_33_0 = slot_33_0 + 1
				end
			elseif mathf.col_vec_rect(iter_33_1.pos2D, arg_33_0, arg_33_1, iter_33_1.boundingRadius * arg_33_4, arg_33_2) then
				slot_33_0 = slot_33_0 + 1
			end
		end
	end

	return slot_33_0
end

function ove_0_8.get_valid_minion(arg_34_0, arg_34_1)
	local slot_34_0 = objManager.minions[arg_34_0]
	local slot_34_1 = slot_34_0.size
	local slot_34_2 = {}

	for iter_34_0 = 0, slot_34_1 - 1 do
		local slot_34_3 = slot_34_0[iter_34_0]

		if arg_34_1(slot_34_3) then
			slot_34_2[#slot_34_2 + 1] = slot_34_3
		end
	end

	return slot_34_2
end

function ove_0_8.get_line_aoe_farm_position(arg_35_0, arg_35_1, arg_35_2)
	local slot_35_0
	local slot_35_1 = 0
	local slot_35_2 = false

	for iter_35_0, iter_35_1 in pairs(arg_35_0) do
		if arg_35_1(iter_35_1) then
			local slot_35_3 = player.pos2D:extend(iter_35_1.pos2D, arg_35_2.range)
			local slot_35_4 = ove_0_8.get_count_on_line(player.pos2D, slot_35_3, arg_35_2.width, arg_35_0, arg_35_2.boundingRadiusMod)

			if slot_35_1 < slot_35_4 then
				slot_35_1 = slot_35_4
				slot_35_0 = iter_35_1

				if slot_35_1 == #arg_35_0 then
					slot_35_2 = true

					return slot_35_0, slot_35_1, slot_35_2
				end
			end
		end
	end

	return slot_35_0, slot_35_1, slot_35_2
end

function ove_0_8.get_circle_points(arg_36_0, arg_36_1, arg_36_2)
	local slot_36_0 = {}

	for iter_36_0 = 1, arg_36_0 do
		local slot_36_1 = iter_36_0 * 2 * math.pi / arg_36_0
		local slot_36_2 = vec3(arg_36_2.x + arg_36_1, arg_36_2.y, arg_36_2.z)
		local slot_36_3 = vec3(ove_0_8.rotate_around_point(slot_36_2, arg_36_2, slot_36_1))

		table.insert(slot_36_0, slot_36_3)
	end

	return slot_36_0
end

local ove_0_15 = {}

function ove_0_8.get_all_enemy_spell()
	return ove_0_15
end

local function ove_0_16(arg_38_0)
	if arg_38_0.owner.type == TYPE_HERO and arg_38_0.owner.team == TEAM_ENEMY then
		ove_0_15[arg_38_0.owner] = {
			[arg_38_0.name] = {
				onwer = arg_38_0.owner,
				cast_time = os.clock(),
				cast_pos = arg_38_0.endPos
			}
		}
	end
end

cb.add(cb.spell, ove_0_16)

function ove_0_8.is_in_aa_range(arg_39_0, arg_39_1)
	if ove_0_8.get_aa_range(arg_39_0, arg_39_1) > arg_39_0.pos:dist(arg_39_1.pos) then
		return true
	else
		return false
	end
end

function ove_0_8.is_fleeing(arg_40_0, arg_40_1)
	if not arg_40_0:isValidTarget() or not arg_40_0.path or not arg_40_0.path.isActive or not arg_40_0.moveSpeed then
		return false
	end

	ove_0_6 = ove_0_6 or module.internal("pred")

	local slot_40_0 = ove_0_6.core.lerp(arg_40_0.path, network.latency + 0.25, arg_40_0.moveSpeed)

	return vec3(slot_40_0.x, arg_40_0.y, slot_40_0.y):dist(arg_40_1.pos) > arg_40_0.pos:dist(arg_40_1.pos)
end

function ove_0_8.is_fleeing_from_me(arg_41_0)
	return ove_0_8.is_fleeing(arg_41_0, player)
end

function ove_0_8.is_under_enemy_turret(arg_42_0, arg_42_1)
	local slot_42_0 = arg_42_1 and arg_42_1 + 900 or 900
	local slot_42_1 = ove_0_8.get_enemy_turrets()

	for iter_42_0, iter_42_1 in ipairs(slot_42_1) do
		if slot_42_0 > iter_42_1.pos:dist(arg_42_0) then
			return true, iter_42_1
		end
	end

	return false
end

function ove_0_8.is_under_ally_turret(arg_43_0, arg_43_1)
	local slot_43_0 = arg_43_1 and arg_43_1 + 900 or 900
	local slot_43_1 = ove_0_8.get_ally_turrets()

	for iter_43_0, iter_43_1 in ipairs(slot_43_1) do
		if slot_43_0 > iter_43_1.pos:dist(arg_43_0) then
			return true, iter_43_1
		end
	end

	return false
end

function ove_0_8.is_solo(arg_44_0)
	local slot_44_0 = 0
	local slot_44_1 = arg_44_0 and arg_44_0 or 2500

	for iter_44_0, iter_44_1 in pairs(ove_0_13) do
		if iter_44_1:isValidTarget(slot_44_1) then
			slot_44_0 = slot_44_0 + 1
		end
	end

	if slot_44_0 > 1 then
		return false
	else
		return true
	end
end

return ove_0_8

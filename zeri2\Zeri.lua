local common = module.load(header.id, "Library/common");
local menu = module.load(header.id, "Library/menu");
local orb = module.internal("orb");
local ts = module.internal("TS");
local prediction = module.internal("pred");
local damagelib = module.internal('damagelib');
local evade = module.seek('evade');

local q = {
    delay = 0.25,
    range = 750,
    width = 40,
    speed = 2600,
    boundingRadiusMod = 0,
    collision = {
        hero = false,
        minion = true,
        wall = true,
    },
}

local q2 = {
    delay = 0.25,
    range = 750,
    width = 40,
    speed = 2600,
    boundingRadiusMod = 0,
    collision = {
        hero = false,
        minion = false,
        wall = true,
    },
}

local w = {
    delay = 0.5,
    range = 1200,
    width = 40,
    speed = 2500,
    boundingRadiusMod = 0,
    collision = {
        hero = false,
        minion = true,
        wall = true,
    }
}

local e = {
    delay = 0.25,
    range = 300,
}

local r = {
    delay = 0.25,
    range = 800,
}

local StartVec3 = {
    vec3{13772, 48.682621002197, 1958},
    vec3{10620, 55.707275390625, 2992},
    vec3{4168, 47.794548034668, 11768},
    vec3{3314, -52.647464752197, 10154},
    vec3{4724, 107.19300079346, 608},
    vec3{14432, 160.55746459961, 13834}
}

local EndVec3 = {
    vec3{13926, 51.366901397705, 3366},
    vec3{11712, 51.024749755859, 2386},
    vec3{3394, 52.83810043335, 12994},
    vec3{1848, 52.83810043335, 11200},
    vec3{7770, 49.443019866943, 734},
    vec3{14296, 91.429809570313, 10808}
}

local function QDamage(target)
    local damage = 0;
    local damageQRaw = {15, 17, 19, 21, 23};
    local damageQRawScaling = {1.04, 1.08, 1.12, 1.16, 1.20};
    if player:spellSlot(0).level > 0 then
        local damageinputQ = damageQRaw[player:spellSlot(0).level] + damageQRawScaling[player:spellSlot(0).level] * player.totalAd;
        damage = damagelib.calc_physical_damage(player, target, damageinputQ);
    end
    return damage
end

local function WDamage(target)
    local damage = 0;
    local damageWRaw = {30, 70, 110, 150, 190};
    if player:spellSlot(1).level > 0 then
        local damageinputW = damageWRaw[player:spellSlot(1).level] + 1.30 * player.totalAd + 0.25 * player.totalAp;
        damage = damagelib.calc_physical_damage(player, target, damageinputW);
    end
    return damage
end

local function RDamage(target)
    local damage = 0;
    local damageRRaw = {175, 275, 375};
    if player:spellSlot(3).level > 0 then
        local damageinputR = damageRRaw[player:spellSlot(3).level] + 0.85 * player.bonusAd + 1.10 * player.totalAp;
        damage = damagelib.calc_magical_damage(player, target, damageinputR);
    end
    return damage
end

local function TargetSelectQ(res, object, dist)
    if object and not object.isDead and object.isVisible and object.isTargetable and not object.buff[17] then
        if (dist > q.range or object.buff["rocketgrab"] or object.buff["bansheesveil"] or  object.buff["itemmagekillerveil"] or object.buff["nocturneshroudofdarkness"] or object.buff["sivire"] or object.buff["fioraw"] or object.buff["blackshield"]) then return end
        res.object = object
        return true 
    end 
end

local function SelectedTargetQ()
    return ts.get_result(TargetSelectQ).object
end

local function TargetSelectW(res, object, dist)
    if object and not object.isDead and object.isVisible and object.isTargetable and not object.buff[17] then
        if (dist > w.range or object.buff["rocketgrab"] or object.buff["bansheesveil"] or  object.buff["itemmagekillerveil"] or object.buff["nocturneshroudofdarkness"] or object.buff["sivire"] or object.buff["fioraw"] or object.buff["blackshield"]) then return end
        res.object = object
        return true 
    end 
end

local function SelectedTargetW()
    return ts.get_result(TargetSelectW).object
end

local function TargetSelectR(res, object, dist)
    if object and not object.isDead and object.isVisible and object.isTargetable and not object.buff[17] then
        if (dist > q.range or object.buff["rocketgrab"] or object.buff["bansheesveil"] or  object.buff["itemmagekillerveil"] or object.buff["nocturneshroudofdarkness"] or object.buff["sivire"] or object.buff["fioraw"] or object.buff["blackshield"]) then return end
        res.object = object
        return true 
    end 
end

local function SelectedTargetR()
    return ts.get_result(TargetSelectR).object
end

local function QLogic()
    local useq = menu.zerimenu.Combo.qsettings.useq:get();
    local qtarget = SelectedTargetQ();

    if not player.buff["zeriespecialrounds"] then
        if useq then
            if player:spellSlot(0).state == 0 then
                if qtarget and common.IsValidTarget(qtarget) then
                    local seg = prediction.linear.get_prediction(q, qtarget)
                    if seg and seg.startPos:dist(seg.endPos) < q.range then
                        if not prediction.collision.get_prediction(q, seg, qtarget) then
                            player:castSpell('pos', 0, vec3(seg.endPos.x, qtarget.y, seg.endPos.y))
                        end
                    end
                end
            end
        end
    end

    if player.buff["zeriespecialrounds"] then
        if useq then
            if player:spellSlot(0).state == 0 then
                if qtarget and common.IsValidTarget(qtarget) then
                    local seg = prediction.linear.get_prediction(q2, qtarget)
                    if seg and seg.startPos:dist(seg.endPos) < q2.range then
                        if not prediction.collision.get_prediction(q2, seg, qtarget) then
                            player:castSpell('pos', 0, vec3(seg.endPos.x, qtarget.y, seg.endPos.y))
                        end
                    end
                end
            end
        end
    end
end

local function WLogic()
    local usew = menu.zerimenu.Combo.wsettings.usew:get();
    local wtarget = SelectedTargetW();
    local noaa = menu.zerimenu.Combo.wsettings.noqrange:get();

    if noaa then
        if wtarget and common.IsValidTarget(wtarget) then
            if wtarget.pos:dist(player.pos) <= q.range then
                return
            end
        end
    end

    if usew then
        if player:spellSlot(1).state == 0 then
            if wtarget and common.IsValidTarget(wtarget) then
                local seg = prediction.linear.get_prediction(w, wtarget)
                if seg and seg.startPos:dist(seg.endPos) < w.range then
                    if not prediction.collision.get_prediction(w, seg, wtarget) then
                        player:castSpell('pos', 1, vec3(seg.endPos.x, wtarget.y, seg.endPos.y))
                    end
                end
            end
        end
    end
end

local function SemiW()
    local wtarget = SelectedTargetW();

    if player:spellSlot(1).state == 0 then
        if wtarget and common.IsValidTarget(wtarget) then
            local seg = prediction.linear.get_prediction(w, wtarget)
            if seg and seg.startPos:dist(seg.endPos) < w.range then
                if not prediction.collision.get_prediction(w, seg, wtarget) then
                    player:castSpell('pos', 1, vec3(seg.endPos.x, wtarget.y, seg.endPos.y))
                end
            end
        end
    end
end

local function ELogic()
    local etarget = SelectedTargetQ();
    local usee = menu.zerimenu.Combo.esettings.usee:get();
    local maxenemies = menu.zerimenu.Combo.esettings.maxene:get();
    local scan = menu.zerimenu.Combo.esettings.scanrange:get();
    local hpslider = menu.zerimenu.Combo.esettings.minhpslidervalue:get();

    if common.HealthPercent(player) < hpslider then
        return
    end

    if usee then
        if player:spellSlot(2).state == 0 then
            if etarget and common.IsValidTarget(etarget) then
                if game.mousePos:countEnemies(scan) <= maxenemies then
                    player:castSpell('pos', 2, game.mousePos)
                end
            end
        end
    end
end

local function RLogic()
    local user = menu.zerimenu.Combo.rsettings.user:get();
    local minenemies = menu.zerimenu.Combo.rsettings.minene:get();

    if user then
        if player:spellSlot(3).state == 0 then
            if player.pos:countEnemies(r.range) >= minenemies then
                player:castSpell('self', 3)
            end
        end
    end
end

local function KillstealQ()
    local ksq = menu.zerimenu.Killsteal.ksQ:get();
    local qtarget = SelectedTargetQ();

    if ksq then
        if player:spellSlot(0).state == 0 then
            if qtarget and common.IsValidTarget(qtarget) then
                if qtarget.health <= QDamage(qtarget) then
                    local seg = prediction.linear.get_prediction(q, qtarget)
                    if seg and seg.startPos:dist(seg.endPos) < q.range then
                        if not prediction.collision.get_prediction(q, seg, qtarget) then
                            player:castSpell('pos', 0, vec3(seg.endPos.x, qtarget.y, seg.endPos.y))
                        end
                    end
                end
            end
        end
    end
end

local function KillstealW()
    local ksw = menu.zerimenu.Killsteal.ksW:get();
    local wtarget = SelectedTargetW();

    if wtarget and common.IsValidTarget(wtarget) then
        if wtarget.pos:dist(player.pos) <= q.range then
            return
        end
    end

    if ksw then
        if player:spellSlot(1).state == 0 then
            if wtarget and common.IsValidTarget(wtarget) then
                if wtarget.health <= WDamage(wtarget) then
                    local seg = prediction.linear.get_prediction(w, wtarget)
                    if seg and seg.startPos:dist(seg.endPos) < w.range then
                        if not prediction.collision.get_prediction(w, seg, wtarget) then
                            player:castSpell('pos', 1, vec3(seg.endPos.x, wtarget.y, seg.endPos.y))
                        end
                    end
                end
            end
        end
    end
end

local function KillstealR()
    local ksr = menu.zerimenu.Killsteal.ksR:get();
    local rtarget = SelectedTargetR();

    if player:spellSlot(1).state == 0 then
        if rtarget and common.IsValidTarget(rtarget) then
            if rtarget.pos:dist(player.pos) <= w.range then
                return
            end
        end
    end

    if ksr then
        if player:spellSlot(3).state == 0 then
            if rtarget and common.IsValidTarget(rtarget) then
                if rtarget.health <= RDamage(rtarget) then
                    player:castSpell('self', 3)
                end
            end
        end
    end
end

local function laneclear()
    local fargmkeytoggle = menu.zerimenu.Keys.farmtoggle:get();

    if not fargmkeytoggle then
        return
    end

    for i = 0, objManager.minions.size[TEAM_ENEMY] - 1 do
        local minion = objManager.minions[TEAM_ENEMY][i];
        local lcq = menu.zerimenu.Clear.LcQ:get();
        if minion and common.IsValidTarget(minion) and not minion.name:find("Ward") then
            if lcq and minion.pos:dist(player.pos) <= q.range then
                player:castSpell("pos", 0, minion.pos)
            end
        end
    end
end

local function jungleclear()
    local fargmkeytoggle = menu.zerimenu.Keys.farmtoggle:get();

    if not fargmkeytoggle then
        return
    end

    if player.pos:countEnemies(w.range) > 0 then
        return
    end

    for i = 0, objManager.minions.size[TEAM_NEUTRAL] - 1 do
        local minion = objManager.minions[TEAM_NEUTRAL][i];
        local jcq = menu.zerimenu.Clear.JcQ:get();
        local jcw = menu.zerimenu.Clear.JcW:get();
        if minion and common.IsValidTarget(minion) and not minion.name:find("Ward") then
            if jcq and minion.pos:dist(player.pos) <= q.range then
                player:castSpell("pos", 0, minion.pos)
            end

            if jcw and minion.pos:dist(player.pos) <= w.range then
                player:castSpell("pos", 1, minion.pos)
            end
        end
    end
end

local function castqonturret()
    if player.pos:countEnemies(q.range) > 0 then
        return
    end

    for i = 0, objManager.turrets.size[TEAM_ENEMY] - 1 do
        local tower = objManager.turrets[TEAM_ENEMY][i]
        if  tower and not tower.isDead and tower.health > 0 then
            local turretPos = vec3(tower.x, tower.y, tower.z)
            if turretPos:dist(player.pos) <= q.range then
                player:castSpell('pos', 0, turretPos)
            end
        end
    end
end

local function JumpLogic()
    if player:spellSlot(2).state == 0 then
        for i, startVec in ipairs(StartVec3) do
            local endVec = EndVec3[i]
            if player.pos:dist(startVec) <= 20 then
                if endVec then
                    player:castSpell('pos', 2, vec3(endVec.x, endVec.y, endVec.z))
                end
                break
            end
        end

        for i2, endvec2 in ipairs(EndVec3) do
            local startvec2 = StartVec3[i2]
            if player.pos:dist(endvec2) <= 20 then
                if startvec2 then
                    player:castSpell('pos', 2, vec3(startvec2.x, startvec2.y, startvec2.z))
                end
            end
        end
    end
end

local function antigap()
    local useag = menu.zerimenu.Misc.AGE:get();
    local hpslidervalue = menu.zerimenu.Misc.AGEHP:get();
    local heroes = objManager.enemies
    local hn = objManager.enemies_n
    for i=0, hn-1 do
        local target = heroes[i]
        if useag then
            if player:spellSlot(2).state == 0 then
                if target and common.IsValidTarget(target) and target.path.isActive and target.path.isDashing and common.HealthPercent(player) <= hpslidervalue then
                    if target.path.endPos2D:dist(player.pos2D) <= 350 then
                        local extendpos = player.pos:extend(player.pos, -300)
                        player:castSpell('pos', 2, extendpos)
                        print("Antigap Closing Sender: " .. target.charName)
                    end
                end
            end
        end
    end
end

cb.add(cb.tick, function ()
    if menu.zerimenu.Keys.stogglebar:get() then
        ELogic();
        RLogic();
        QLogic();
        WLogic();
    end

    if menu.zerimenu.Keys.clearkey:get() then
        laneclear();
        jungleclear();
        castqonturret();
    end

    if menu.zerimenu.Keys.semiw:get() then
        SemiW();
    end

    if menu.zerimenu.Keys.jumpkey:get() then
        JumpLogic();
    end

    KillstealQ();
    KillstealW();
    KillstealR();
    antigap();

    if menu.zerimenu.Keys.disableaa:get() then
        if not player.buff["zeriqpassiveready"] then
            orb.core.set_pause_attack(math.huge)
        end

        if player.buff["zeriqpassiveready"] then
            orb.core.set_pause_attack(0)
        end 
    end

    if not menu.zerimenu.Keys.disableaa:get() then
        orb.core.set_pause_attack(0)
    end
end)

cb.add(cb.draw, function ()
    local drawq = menu.zerimenu.Draws.drawQ:get();
    local draww = menu.zerimenu.Draws.drawW:get();
    local drawe = menu.zerimenu.Draws.drawE:get();
    local drawr = menu.zerimenu.Draws.drawR:get();
    local jumps = menu.zerimenu.Draws.drawjump:get();
    local drawBuff = menu.zerimenu.Draws.drawbuff:get();

    if player.isOnScreen then
        if drawq then
            if player:spellSlot(0).level > 0 then
                graphics.draw_circle(player.pos, q.range, 2, graphics.argb(255, 255, 255, 255), 100)
            end
        end

        if draww then
            if player:spellSlot(1).level > 0 then
                graphics.draw_circle(player.pos, w.range, 2, graphics.argb(255, 0, 0, 255), 100)
            end
        end

        if drawe then
            if player:spellSlot(2).level > 0 then
                graphics.draw_circle(player.pos, e.range, 2, graphics.argb(255, 0, 255, 0), 100)
            end
        end

        if drawr then
            if player:spellSlot(3).level > 0 then
                graphics.draw_circle(player.pos, r.range, 2, graphics.argb(255, 255, 0, 0), 100)
            end
        end

        if jumps then
            for _, startspots in pairs(StartVec3) do
                graphics.draw_circle_xyz(startspots.x, startspots.y, startspots.z, 50, 2, 0xFFFFFFFF, 32)
            end

            for _, endspots in pairs(EndVec3) do
                graphics.draw_circle_xyz(endspots.x, endspots.y, endspots.z, 50, 2, 0xFFFFFFFF, 32)
            end
        end       

        if drawBuff then
            if player.buff["zeriespecialrounds"] then
                local etime = player.buff["zeriespecialrounds"].endTime - game.time
                local poss = graphics.world_to_screen(player.pos)
                local etimesplit = string.format("%.1f", etime)
                graphics.draw_outlined_text_2D('E Time: '..etimesplit, 22, poss.x, poss.y+25, 0xFFFFFFFF)
            end

            if player.buff["zerir"] then
                local rtime = player.buff["zerir"].endTime - game.time
                local poss = graphics.world_to_screen(player.pos)
                local rtimesplot = string.format("%.1f", rtime)
                if not player.buff["zeriespecialrounds"] then
                    graphics.draw_outlined_text_2D('R Time: '..rtimesplot, 22, poss.x, poss.y+25, 0xFFFFFFFF)                   
                end
                if player.buff["zeriespecialrounds"] then
                    graphics.draw_outlined_text_2D('R Time: '..rtimesplot, 22, poss.x, poss.y+50, 0xFFFFFFFF)
                end
            end
        end
    end
end)
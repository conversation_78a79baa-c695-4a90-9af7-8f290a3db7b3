local ove_0_5 = player:spellSlot(0)

local function ove_0_6(arg_1_0)

	return 30 + 30 * ove_0_5.level + arg_1_0 * 0.5
end

local ove_0_7 = player:spellSlot(1)

local function ove_0_8(arg_2_0)

	return 30 + 55 * ove_0_7.level + arg_2_0 * 0.7
end

local ove_0_9 = player:spellSlot(3)

local function ove_0_10(arg_3_0)

	return 75 + 75 * ove_0_9.level + arg_3_0 * 0.7
end

local function ove_0_11(arg_4_0, arg_4_1)

	return arg_4_1 * (100 / (100 + arg_4_0.spellBlock))
end

return {
	q = ove_0_6,
	w = ove_0_8,
	r = ove_0_10,
	calc = ove_0_11
}

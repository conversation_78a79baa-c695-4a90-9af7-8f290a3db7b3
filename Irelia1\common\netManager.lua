

return {
	Ping = function()
		return network.latency * 1000
	end,
	GameTime = function()
		return game.time
	end,
	TickCount = function()
		return game.time * 1000
	end,
	Download = function(arg_8_0, arg_8_1)
		return (network.download_file(arg_8_0, arg_8_1))
	end,
	Send = function(arg_9_0, arg_9_1)
		local slot_9_0, slot_9_1, slot_9_2 = network.send(arg_9_0, arg_9_1)

		return slot_9_0, slot_9_1, slot_9_2
	end
}

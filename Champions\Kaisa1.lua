local kaisaPlugin = {}

-- Load Spell
local spellQ = {
    range = 600,
    delay = 0.25,
    width = 120,
    speed = 2500,
    boundingRadiusMod = 0,
    collision = {hero = true, minion = true, wall = false},
}

local spellW = {
    range = 3000,
    delay = 0.50,
    width = 100,
    speed = 1750,
    boundingRadiusMod = 0,
    collision = {hero = true, minion = true, wall = false},
}

local spellE = {
    range = 300,
}

-- Load Module
local preds = module.internal("pred")
local ui = module.load("<PERSON>", "ui");
local DelayAction = module.load("<PERSON>", "Core/DelayAction")
local DelayTick = module.load("<PERSON>", "Core/DelayTick")
local Prediction = module.load("<PERSON>", "Core/Prediction")
local BuffManager = module.load("Brian", "Library/BuffManager")
local CalculateManager = module.load("Brian", "Library/CalculateManager")
local FarmManager = module.load("<PERSON>", "Library/FarmManager")
local ItemManager = module.load("<PERSON>", "Library/ItemManager")
local NetManager = module.load("<PERSON>", "Library/NetManager")
local ObjectManager = module.load("<PERSON>", "Library/ObjectManager")
local OrbManager = module.load("Brian", "Library/OrbManager")
local SpellManager = module.load("Brian", "Library/SpellManager")
local VectorManager = module.load("Brian", "Library/VectorManager")
local MyCommon = module.load("Brian", "Library/ExtraManager")

local MyMenu

function kaisaPlugin.Load(GlobalMenu)
    MyMenu = GlobalMenu

    MyMenu:menu("Combo", "Combo Settings")
    MyMenu.Combo:header("SpellHeader", "Spell Core")
    MyMenu.Combo:boolean("Q", "Use Q", true)
    MyMenu.Combo:boolean("QCollision", "^ Check minion collision", true)
    MyMenu.Combo:boolean("QAA", "^ Reset Auto Attack", true)
    MyMenu.Combo:boolean("W", "Use W", true)
    MyMenu.Combo:boolean("WSmart", "^ Smart Logic", true)
    MyMenu.Combo:boolean("WTeam", "^ Dont Use On Team Fight", true)
    MyMenu.Combo:boolean("E", "Use E", false)

    MyMenu:menu("Harass", "Harass Settings")
    MyMenu.Harass:header("SpellHeader", "Spell Core")
    MyMenu.Harass:boolean("Q", "Use Q", true)
    MyMenu.Harass:boolean("QCollision", "^ Check minion collision", true)
    MyMenu.Harass:boolean("QAA", "^ Reset Auto Attack", true)
    MyMenu.Harass:boolean("W", "Use W", true)
    MyMenu.Harass:boolean("WSmart", "^ Smart Logic", true)
    MyMenu.Harass:header("ManaHeader", "Mana Manager")
    MyMenu.Harass:slider("ManaMin", "Player ManaPercent >= x%", 60, 1, 100, 1)
    MyMenu.Harass:header("ProHeader", "PRO Mode")
    MyMenu.Harass:boolean("ProAllow", "Enabled PRO Combo Mode", true)
    MyMenu.Harass.ProAllow:set("tooltip", "PRO Mode => Allow Use Spell Combo Enemy on LaneClear Mode")
    MyMenu.Harass:boolean("ProTurret", "Allow Under Turret Combo", false)

    MyMenu:menu("LaneClear", "LaneClear Settings")
    MyMenu.LaneClear:header("SpellHeader", "Spell Core")
    MyMenu.LaneClear:boolean("Q", "Use Q", true)
    MyMenu.LaneClear:slider("QCount", "^ Min Hit Count >= x", 4, 1, 9, 1)
    MyMenu.LaneClear:header("ManaHeader", "Mana Manager")
    MyMenu.LaneClear:slider("ManaMin", "Player ManaPercent >= x%", 60, 1, 100, 1)

    MyMenu:menu("JungleClear", "JungleClear Settings")
    MyMenu.JungleClear:header("SpellHeader", "Spell Core")
    MyMenu.JungleClear:boolean("Q", "Use Q", true)
    MyMenu.JungleClear:header("ManaHeader", "Mana Manager")
    MyMenu.JungleClear:slider("ManaMin", "Player ManaPercent >= x%", 20, 1, 100, 1)

    FarmManager.Load(MyMenu)

    MyMenu:menu("KillSteal", "KillSteal Settings")
    MyMenu.KillSteal:header("SpellHeader", "Spell Core")
    MyMenu.KillSteal:boolean("Q", "Use Q", true)
    MyMenu.KillSteal:boolean("W", "Use W", true)
    MyMenu.KillSteal:boolean("WNOAA", "^ Only Out of AA Range", true)

    MyMenu:menu("Draw", "Draw Settings")
    MyMenu.Draw:header("RangeHeader", "Spell Range")
    MyMenu.Draw:boolean("Q", "Draw Q Range", false)
    MyMenu.Draw:color("colorq", "^ color", 255, 233, 121, 121)
    MyMenu.Draw:boolean("W", "Draw W Range", false)
    MyMenu.Draw:color("colorw", "^ color", 255, 233, 121, 121)
    MyMenu.Draw:boolean("Ready", "Only Spell Ready", false)

end

local function GetPassiveDamage(target)
    if not target or target.isDead or not target.health or not target.maxHealth then
        return 0
    end
    local buffCount = BuffManager.GetBuffCount(target, "kaisapassivemarker")
    local totalAP = player.flatMagicDamageMod * player.percentMagicDamageMod
    local firstDMG = 4
    if player.levelRef >= 17 then
        firstDMG = 10
    elseif player.levelRef >= 14 then
        firstDMG = 9
    elseif player.levelRef >= 11 then
        firstDMG = 8
    elseif player.levelRef >= 9 then
        firstDMG = 7
    elseif player.levelRef >= 6 then
        firstDMG = 6
    elseif player.levelRef >= 3 then
        firstDMG = 5
    elseif player.levelRef >= 1 then
        firstDMG = 4
    end
    local secondDMG = 1
    if player.levelRef >= 15 then
        secondDMG = 5
    elseif player.levelRef >= 12 then
        secondDMG = 4
    elseif player.levelRef >= 8 then
        secondDMG = 3
    elseif player.levelRef >= 4 then
        secondDMG = 2
    elseif player.levelRef >= 1 then
        secondDMG = 1
    end
    local thirdDMG = 0.1 * totalAP
    if buffCount == 1 then
        thirdDMG = 0.125 * totalAP
    elseif buffCount == 2 then
        thirdDMG = 0.15 * totalAP
    elseif buffCount == 3 then
        thirdDMG = 0.175 * totalAP
    elseif buffCount == 4 then
        thirdDMG = 0.2 * totalAP
    end
    if buffCount == 4 then
        local basicPerc = 0.15
        if player.levelRef >= 16 then
            basicPerc = 0.20
        elseif player.levelRef >= 13 then
            basicPerc = 0.19
        elseif player.levelRef >= 10 then
            basicPerc = 0.18
        elseif player.levelRef >= 7 then
            basicPerc = 0.17
        elseif player.levelRef >= 4 then
            basicPerc = 0.16
        elseif player.levelRef >= 1 then
            basicPerc = 0.15
        end
        local calculate = totalAP / 100
        local ceil = math.ceil(calculate)
        local basicAPPerc = 0.025 * ceil
        local targetLostHP = target.maxHealth - target.health
        local burnDMG = targetLostHP * (basicPerc + basicAPPerc)
        if target.type == TYPE_MINION and target.team == TEAM_NEUTRAL then
            if burnDMG > 400 then
                burnDMG = 400
            end
        end
        local realDMG = firstDMG + secondDMG + thirdDMG + burnDMG
        return realDMG
    end
    local calculateDMG = firstDMG + secondDMG + thirdDMG
    return calculateDMG
end

local function GetQDamage(target)
    if not target or target == nil then
        return 0
    end
    local level = player:spellSlot(0).level
    if level == 0 then
        return 0
    end
    local damage = ({45, 61.25, 77.5, 93.75, 110})[level] + (0.35 * MyCommon.GetBonusAD()) + (0.4 * MyCommon.GetTotalAP())
    if BuffManager.HasBuff(player, "KaisaQEvolved") then
        local calculateDMG = CalculateManager.CalculatePhysicalDamage(target, (damage + (10 * damage * 0.3)))
        if target.type == TYPE_MINION then
            if target.health and MyCommon.GetHealthPercent(target) < 35 then
                return calculateDMG * 2
            end
        end
        return calculateDMG
    end
    local result = CalculateManager.CalculatePhysicalDamage(target, (damage + (5 * damage * 0.3)))
    if target.type == TYPE_MINION then
        if target.health and MyCommon.GetHealthPercent(target) < 35 then
            return result * 2
        end
    end
    return result
end

local function GetWDamage(target)
    if not target or target == nil then
        return 0
    end
    local level = player:spellSlot(1).level
    if level == 0 then
        return 0
    end
    local DMG = ({20, 45, 70, 95, 120})[level] + (1.5 * MyCommon.GetTotalAD()) + (0.45 * MyCommon.GetTotalAP())
    if BuffManager.HasBuff(player, "KaisaWEvolved") then
        return CalculateManager.CalculateMagicDamage(target, DMG) + GetPassiveDamage(target, 3)
    end
    return CalculateManager.CalculateMagicDamage(target, DMG) + GetPassiveDamage(target, 2)
end

local function GetQTarget()
    return MyCommon.TargetSelector.get_result(
        function(res, obj, dist)
            if dist < spellQ.range then
                if Prediction.GetPrediction(spellQ, obj) then
                    res.obj = obj
                    return true
                end
            end
        end
    ).obj
end

local function GetWTarget()
    return MyCommon.TargetSelector.get_result(
        function(res, obj, dist)
            if dist < spellW.range then
                if Prediction.GetPrediction(spellW, obj) then
                    res.obj = obj
                    return true
                end
            end
        end
    ).obj
end

local function KillSteal()
    if MyMenu.KillSteal.Q:get() and SpellManager.CanCastSpell(0) and not player.path.isDashing then
        local targets = ObjectManager.GetEnemiesInRange(spellQ.range, player)
        for i, target in ipairs(targets) do
            if MyCommon.IsValidTarget(target, spellQ.range) and not MyCommon.IsUnKillAble(target) then
                local qDMG = GetQDamage(target)
                if target.health and target.health < qDMG then
                    SpellManager.CastOnPlayer(0)
                    return
                end
            end
        end
    end
    if MyMenu.KillSteal.W:get() and SpellManager.CanCastSpell(1) and not player.path.isDashing then
        local targets = ObjectManager.GetEnemiesInRange(spellW.range, player)
        for i, target in ipairs(targets) do
            if MyCommon.IsValidTarget(target, spellW.range) and not MyCommon.IsUnKillAble(target) then
                local wDMG = GetWDamage(target)
                if target.health and target.health < wDMG then
                    if MyMenu.KillSteal.WNOAA:get() then
                        if #ObjectManager.GetEnemiesInRange(800) == 0 then
                            local pred = Prediction.GetPrediction(spellW, target)
                            if pred then
                                SpellManager.CastOnPosition(vec3(pred.x, game.mousePos.y, pred.y), 1)
                                return
                            end
                        end
                    else
                        local pred = Prediction.GetPrediction(spellW, target)
                        if pred then
                            SpellManager.CastOnPosition(vec3(pred.x, game.mousePos.y, pred.y), 1)
                            return
                        end
                    end
                end
            end
        end
    end
end

local function Combo()
    if MyMenu.Combo.E:get() and SpellManager.CanCastSpell(2) then
        if #ObjectManager.GetEnemiesInRange(600) == 0 and #ObjectManager.GetEnemiesInRange(900) >= 1 then
            SpellManager.CastOnPlayer(2)
        end
    end
    if MyMenu.Combo.Q:get() and SpellManager.CanCastSpell(0) then
        if MyMenu.Combo.QCollision:get() then
            local target = GetQTarget()
            if target and target ~= nil and MyCommon.IsValidTarget(target, spellQ.range) then
                local pred = Prediction.GetPrediction(spellQ, target)
                if pred then
                    SpellManager.CastOnPlayer(0)
                end
            end
        else
            local target = MyCommon.GetTarget(spellQ.range)
            if target and target ~= nil and MyCommon.IsValidTarget(target, spellQ.range) then
                local pred = Prediction.GetPrediction(spellQ, target)
                if pred then
                    SpellManager.CastOnPlayer(0)
                end
            end
        end
    end
    if MyMenu.Combo.W:get() and SpellManager.CanCastSpell(1) then
        if MyMenu.Combo.WTeam:get() and #ObjectManager.GetEnemiesInRange(1500) > 2 then
            return
        end
        if not MyMenu.Combo.WSmart:get() and (#ObjectManager.GetEnemiesInRange(850) > 1 or #ObjectManager.GetEnemiesInRange(600) > 0) then
            return
        end
        local target = GetWTarget()
        if target and target ~= nil and MyCommon.IsValidTarget(target, spellW.range) then
            local pred = Prediction.GetPrediction(spellW, target)
            if pred then
                SpellManager.CastOnPosition(vec3(pred.x, game.mousePos.y, pred.y), 1)
                return
            end
        end
    end
end

local function Harass()
    if not MyMenu.Harass.ProTurret:get() and ObjectManager.IsUnderEnemyTurret(player) then
        return
    end
    if MyCommon.GetManaPercent() >= MyMenu.Harass.ManaMin:get() then
        if MyMenu.Harass.Q:get() and SpellManager.CanCastSpell(0) then
            if MyMenu.Harass.QCollision:get() then
                local target = GetQTarget()
                if target and target ~= nil and MyCommon.IsValidTarget(target, spellQ.range) then
                    local pred = Prediction.GetPrediction(spellQ, target)
                    if pred then
                        SpellManager.CastOnPlayer(0)
                        return
                    end
                end
            else
                local target = MyCommon.GetTarget(spellQ.range)
                if target and target ~= nil and MyCommon.IsValidTarget(target, spellQ.range) then
                    local pred = Prediction.GetPrediction(spellQ, target)
                    if pred then
                        SpellManager.CastOnPlayer(0)
                        return
                    end
                end
            end
        end
        if MyMenu.Harass.W:get() and SpellManager.CanCastSpell(1) then
            if not MyMenu.Harass.WSmart:get() and (#ObjectManager.GetEnemiesInRange(850) > 1 or #ObjectManager.GetEnemiesInRange(600) > 0) then
                return
            end
            local target = GetWTarget()
            if target and target ~= nil and MyCommon.IsValidTarget(target, spellW.range) then
                local pred = Prediction.GetPrediction(spellW, target)
                if pred then
                    SpellManager.CastOnPosition(vec3(pred.x, game.mousePos.y, pred.y), 1)
                    return
                end
            end
        end
    end
end

local function Clear()
    if MyMenu.LaneClear.Q:get() and SpellManager.CanCastSpell(0) then
        if MyCommon.GetManaPercent() >= MyMenu.LaneClear.ManaMin:get() then
            local minions = ObjectManager.GetMinions(spellQ.range, TEAM_ENEMY)
            if minions and #minions > 0 and #minions >= MyMenu.LaneClear.QCount:get() then
                SpellManager.CastOnPlayer(0)
                return
            end
        end
    end
    if MyMenu.JungleClear.Q:get() and SpellManager.CanCastSpell(0) then
        if MyCommon.GetManaPercent() >= MyMenu.JungleClear.ManaMin:get() then
            local mobs = ObjectManager.GetMinions(spellQ.range, TEAM_NEUTRAL)
            if mobs and #mobs > 0 then
                if #mobs > 2 then
                    SpellManager.CastOnPlayer(0)
                    return
                else
                    for i, mob in ipairs(mobs) do
                        if mob and MyCommon.IsValidTarget(mob, spellQ.range) and MyCommon.IsBigMob(mob) then
                            SpellManager.CastOnPlayer(0)
                            return
                        end
                    end
                end
            end
        end
    end
end

local function OnMyTick()
    if player.isDead or player.isRecalling or chat.isOpened or OrbManager.IsWindingUp(30) then
        return
    end
    if BuffManager.HasBuff(player, "KaisaE") then
        return
    end
    if DelayTick.CanTickEvent() then
        KillSteal()
    end
    if MyMenu.Key.Harass:get() then
        Harass()
    end
    if MyMenu.Key.Combo:get() then
        Combo()
    end
    if DelayTick.CanTickEvent() then
        if MyMenu.Key.LaneClear:get() then
            if MyMenu.Harass.ProAllow:get() then
                Harass()
            end
            if FarmManager.Enabled then
                Clear()
            end
        end
    end
end

local function OnMyAfterAttack(target)
    if BuffManager.HasBuff(player, "KaisaE") then
        return
    end
    if target and target ~= nil and MyCommon.IsValidTarget(target) then
        if target.type == TYPE_HERO then
            if MyMenu.Key.Combo:get() then
                if MyMenu.Combo.Q:get() and SpellManager.CanCastSpell(0) and MyMenu.Combo.QAA:get() then
                    if target and MyCommon.IsValidTarget(target) then
                        SpellManager.CastOnPlayer(0)
                        return
                    end
                end
            end
            if MyMenu.Key.Combo:get() and MyCommon.GetManaPercent() >= MyMenu.Harass.ManaMin:get() then
                if MyMenu.Combo.Q:get() and SpellManager.CanCastSpell(0) and MyMenu.Combo.QAA:get() then
                    if target and MyCommon.IsValidTarget(target) then
                        SpellManager.CastOnPlayer(0)
                        return
                    end
                end
            end
            if
			MyMenu.Key.LaneClear:get() and MyMenu.Combo.ProAllow:get() and
                    MyCommon.GetManaPercent() >= MyMenu.Harass.ManaMin:get()
             then
                if MyMenu.Combo.Q:get() and SpellManager.CanCastSpell(0) and MyMenu.Combo.QAA:get() then
                    if target and MyCommon.IsValidTarget(target) then
                        SpellManager.CastOnPlayer(0)
                        return
                    end
                end
            end
        elseif target.type == TYPE_MINION and target.team == TEAM_NEUTRAL and MyCommon.IsBigMob(target) then
            if MyMenu.Key.LaneClear:get() and FarmManager.Enabled then
                if MyMenu.JungleClear.Q:get() and SpellManager.CanCastSpell(0) then
                    if MyCommon.GetManaPercent() >= MyMenu.JungleClear.ManaMin:get() then
                        if target and MyCommon.IsValidTarget(target) then
                            SpellManager.CastOnPlayer(0)
                            return
                        end
                    end
                end
            end
        end
    end
end

local function OnMyDraw()
    if player.isDead or chat.isOpened then
        return
    end
    if MyMenu.Draw.Q:get() and MyCommon.CanDrawCircle(spellQ.range) then
        if MyMenu.Draw.Ready:get() then
            if SpellManager.CanCastSpell(0) then
                graphics.draw_circle(player.pos, spellQ.range, 2, MyMenu.Draw.colorq:get(), 100)
            end
        else
            graphics.draw_circle(player.pos, spellQ.range, 2,  MyMenu.Draw.colorq:get(), 100)
        end
    end
    if MyMenu.Draw.W:get() and MyCommon.CanDrawCircle(spellW.range) then
        if MyMenu.Draw.Ready:get() then
            if SpellManager.CanCastSpell(1) then
                graphics.draw_circle(player.pos, spellW.range, 2,  MyMenu.Draw.colorw:get(), 100)
            end
        else
            graphics.draw_circle(player.pos, spellW.range, 2,  MyMenu.Draw.colorw:get(), 100)
        end
    end
end

OrbManager.AddFasterTickCallback(OnMyTick)
OrbManager.AddAfterAttackCallback(OnMyAfterAttack)
cb.add(cb.draw, OnMyDraw)


return kaisaPlugin

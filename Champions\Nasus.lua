local var_0_0 = "1.0"
local var_0_1 = module.internal("pred")
local var_0_2 = module.internal("TS")
local var_0_3 = module.internal("orb")
local var_0_4 = module.load("<PERSON>","Utility/common20")
local var_0_5 = {
	range = 0
}
local var_0_6 = {
	range = 600
}
local var_0_7 = {
	speed = 1000,
	radius = 300,
	range = 650,
	delay = 0.6,
	boundingRadiusMod = 1
}
local var_0_8 = {
	range = 175
}
local var_0_9 = menu("Brian" .. player.charName, "[<PERSON>] " .. player.charName)

var_0_9:menu("combo", "Combo")
var_0_9.combo:header("qset", " -- Q Settings --")
var_0_9.combo:boolean("qcombo", "Use Q", true)
var_0_9.combo:boolean("qaa", " ^- Only for Auto Attack reset", false)
var_0_9.combo:header("wset", " -- W Settings --")
var_0_9.combo:boolean("wcombo", "Use W", true)
var_0_9.combo:boolean("waa", " ^- Only if out of Auto Atack range", true)
var_0_9.combo:header("eset", " -- E Settings --")
var_0_9.combo:boolean("ecombo", "Use E", true)
var_0_9.combo:header("rset", " -- R Settings --")
var_0_9.combo:boolean("rcombo", "Use R", true)
var_0_9.combo:slider("minhp", " ^- if My Health below X %", 30, 1, 100, 1)
var_0_9:menu("harass", "Harass")
var_0_9.harass:header("qset", " -- Q Settings --")
var_0_9.harass:boolean("qcombo", "Use Q", true)
var_0_9.harass:boolean("qaa", " ^- Only for Auto Attack reset", false)
var_0_9.harass:header("wset", " -- W Settings --")
var_0_9.harass:boolean("wcombo", "Use W", true)
var_0_9.harass:boolean("waa", " ^- Only if out of Auto Atack range", true)
var_0_9.harass:header("eset", " -- E Settings --")
var_0_9.harass:boolean("ecombo", "Use E", true)
var_0_9:menu("farming", "Farming")
var_0_9.farming:keybind("toggle", "Farm Toggle", nil, "A")
var_0_9.farming:header("uwu", " ~~~~ ")
var_0_9.farming:menu("laneclear", "Lane Clear")
var_0_9.farming.laneclear:boolean("useq", "Use Q", true)
var_0_9.farming.laneclear:boolean("usee", "Use E", false)
var_0_9.farming.laneclear:slider("hitsr", " ^- if Hits X Minions", 3, 1, 6, 1)
var_0_9.farming:menu("jungleclear", "Jungle Clear")
var_0_9.farming.jungleclear:boolean("useq", "Use Q", true)
var_0_9.farming.jungleclear:boolean("usee", "Use E", true)
var_0_9.farming:menu("lasthit", "Last Hit")
var_0_9.farming.lasthit:boolean("useq", "Use Q", true)
var_0_9:menu("killsteal", "Killsteal")
var_0_9.killsteal:boolean("kse", "Use E", true)
var_0_9:menu("Gap", "Gapcloser Settings")
var_0_9.Gap:boolean("GapA", "Use W", true)
var_0_9.Gap:menu("gapblacklist", "Blacklist")

local var_0_10 = var_0_4.GetEnemyHeroes()

for iter_0_0, iter_0_1 in ipairs(var_0_10) do
	var_0_9.Gap.gapblacklist:boolean(iter_0_1.charName, "Ignore: " .. iter_0_1.charName, false)
end

var_0_9:menu("draws", "Draw Settings")
var_0_9.draws:header("ranges", " -- Ranges -- ")
var_0_9.draws:boolean("draww", "Draw W Range", true)
var_0_9.draws:color("colorw", "  ^- Color", 255, 255, 255, 255)
var_0_9.draws:boolean("drawe", "Draw E Range", true)
var_0_9.draws:color("colore", "  ^- Color", 255, 255, 255, 255)
var_0_9.draws:header("other", " -- Other -- ")
var_0_9.draws:boolean("drawdamage", "Draw Damage", true)
var_0_9.draws:slider("transparency", "Damage Drawing Transparency", 155, 0, 255, 1)
var_0_2.load_to_menu(var_0_9)

local var_0_11
local var_0_12

local function var_0_13(arg_1_0)
	if arg_1_0.slot == 0 and (var_0_3.menu.lane_clear.key:get() or var_0_3.menu.last_hit.key:get()) and var_0_12 and var_0_4.IsValidTarget(var_0_12) then
		player:attack(var_0_12)
		var_0_3.core.set_server_pause()

		var_0_12 = nil
	end

	if arg_1_0 and (arg_1_0.name:find("BasicAttack") or arg_1_0.name:find("PassiveAttack")) and arg_1_0.owner == player and arg_1_0.target.type == TYPE_HERO then
		var_0_11 = arg_1_0.target
	end
end

local function var_0_14(arg_2_0, arg_2_1, arg_2_2)
	if arg_2_2 <= var_0_7.range then
		arg_2_0.obj = arg_2_1

		return true
	end
end

local function var_0_15()
	return var_0_2.get_result(var_0_14).obj
end

local function var_0_16(arg_4_0, arg_4_1, arg_4_2)
	if arg_4_2 <= var_0_6.range then
		arg_4_0.obj = arg_4_1

		return true
	end
end

local function var_0_17()
	return var_0_2.get_result(var_0_16).obj
end

var_0_3.combat.register_f_after_attack(function()
	if var_0_3.menu.combat.key:get() and var_0_11 and var_0_9.combo.qaa:get() and var_0_9.combo.qcombo:get() and var_0_11 and var_0_4.IsValidTarget(var_0_11) and player.pos:dist(var_0_11.pos) < 600 and player:spellSlot(0).state == 0 then
		player:castSpell("obj", 0, var_0_11)
		player:attack(var_0_11)

		return "on_after_attack_hydra"
	end

	if var_0_3.menu.hybrid.key:get() and var_0_11 and var_0_9.harass.qaa:get() and var_0_9.harass.qcombo:get() and var_0_11 and var_0_4.IsValidTarget(var_0_11) and player.pos:dist(var_0_11.pos) < 600 and player:spellSlot(0).state == 0 then
		player:castSpell("obj", 0, var_0_11)
		player:attack(var_0_11)

		return "on_after_attack_hydra"
	end

	var_0_3.combat.set_invoke_after_attack(false)
end)

local function var_0_18(arg_7_0, arg_7_1)
	local var_7_0 = {}

	for iter_7_0 = 0, objManager.enemies_n - 1 do
		local var_7_1 = objManager.enemies[iter_7_0]

		if arg_7_1 > arg_7_0:dist(var_7_1.pos) and var_0_4.IsValidTarget(var_7_1) then
			var_7_0[#var_7_0 + 1] = var_7_1
		end
	end

	return var_7_0
end

local function var_0_19(arg_8_0, arg_8_1)
	local var_8_0 = {}

	for iter_8_0 = 0, objManager.minions.size[TEAM_ENEMY] - 1 do
		local var_8_1 = objManager.minions[TEAM_ENEMY][iter_8_0]

		if arg_8_1 > arg_8_0:dist(var_8_1.pos) and var_0_4.IsValidTarget(var_8_1) then
			var_8_0[#var_8_0 + 1] = var_8_1
		end
	end

	return var_8_0
end

local var_0_20 = 0
local var_0_21 = false
local var_0_22 = false

sheenTimer = os.clock()

local var_0_23 = 0

function SheenDamage(arg_9_0)
	local var_9_0 = 0

	var_0_21 = false
	var_0_22 = false

	for iter_9_0 = 0, 5 do
		if player:itemID(iter_9_0) == 3078 then
			if player:spellSlot(6 + iter_9_0).cooldown > 0 then
				var_0_23 = 0.2 + game.time
			end

			if var_0_23 - game.time < 0 then
				var_0_22 = true
			end
		end

		if player:itemID(iter_9_0) == 3057 then
			if player:spellSlot(6 + iter_9_0).cooldown > 0 then
				var_0_23 = 0.2 + game.time
			end

			if var_0_23 - game.time < 0 then
				var_0_21 = true
			end
		end
	end

	if var_0_21 and not var_0_22 and (os.clock() >= sheenTimer or player.buff.sheen) then
		var_9_0 = player.baseAttackDamage
	end

	if var_0_22 and (os.clock() >= sheenTimer or player.buff.trinityforce) then
		var_9_0 = player.baseAttackDamage * 2
	end

	return var_9_0 * var_0_4.PhysicalReduction(arg_9_0)
end

local var_0_24 = {
	30,
	50,
	70,
	90,
	110
}

function QDamage(arg_10_0)
	local var_10_0 = 0

	if player:spellSlot(0).level > 0 and player.buff.nasusqstacks then
		var_10_0 = var_0_4.CalculatePhysicalDamage(arg_10_0, var_0_24[player:spellSlot(0).level] + player.buff.nasusqstacks.stacks2, player)
	end

	return var_10_0 + var_0_4.CalculateAADamage(arg_10_0) + SheenDamage(arg_10_0)
end

local var_0_25 = {
	55,
	95,
	135,
	175,
	215
}

function EDamage(arg_11_0)
	local var_11_0 = 0

	if player:spellSlot(2).level > 0 then
		var_11_0 = var_0_4.CalculateMagicDamage(arg_11_0, var_0_25[player:spellSlot(2).level] + var_0_4.GetTotalAP() * 0.6, player)
	end

	return var_11_0
end

local function var_0_26()
	if var_0_9.combo.qcombo:get() and var_0_9.combo.qaa:get() and var_0_3.combat.target and QDamage(var_0_3.combat.target) >= var_0_3.combat.target.health then
		player:castSpell("self", 0)
	end

	if var_0_9.combo.qcombo:get() and not var_0_9.combo.qaa:get() and var_0_3.combat.target and var_0_4.IsValidTarget(var_0_3.combat.target) and player.pos:dist(var_0_3.combat.target.pos) <= var_0_4.GetAARange(var_0_3.combat.target) then
		player:castSpell("self", 0)
	end

	if var_0_9.combo.wcombo:get() then
		local var_12_0 = var_0_17()

		if var_12_0 and var_12_0.pos:dist(player.pos) <= var_0_6.range then
			if not var_0_9.combo.waa:get() then
				player:castSpell("obj", 1, var_12_0)
			elseif player.pos:dist(var_12_0.pos) >= var_0_4.GetAARange(var_0_3.combat.target) then
				player:castSpell("obj", 1, var_12_0)
			end
		end
	end

	if var_0_9.combo.ecombo:get() then
		local var_12_1 = var_0_15()

		if var_12_1 and var_12_1.pos:dist(player.pos) <= var_0_7.range then
			local var_12_2 = var_0_1.circular.get_prediction(var_0_7, var_12_1)

			if var_12_2 and var_12_2.startPos:dist(var_12_2.endPos) < var_0_7.range then
				player:castSpell("pos", 2, vec3(var_12_2.endPos.x, var_12_1.y, var_12_2.endPos.y))
			end
		end
	end

	if var_0_9.combo.rcombo:get() then
		local var_12_3 = var_0_17()

		if var_12_3 and var_12_3.pos:dist(player.pos) <= 500 and player.health / player.maxHealth * 100 <= var_0_9.combo.minhp:get() then
			player:castSpell("self", 3)
		end
	end
end

local function var_0_27()
	if var_0_9.harass.qcombo:get() and not var_0_9.harass.qaa:get() and var_0_3.combat.target and var_0_4.IsValidTarget(var_0_3.combat.target) and player.pos:dist(var_0_3.combat.target.pos) <= var_0_4.GetAARange(var_0_3.combat.target) then
		player:castSpell("self", 0)
	end

	if var_0_9.harass.wcombo:get() then
		local var_13_0 = var_0_17()

		if var_13_0 and var_13_0.pos:dist(player.pos) <= var_0_6.range then
			if not var_0_9.harass.waa:get() then
				player:castSpell("obj", 1, var_13_0)
			elseif player.pos:dist(var_13_0.pos) >= var_0_4.GetAARange(var_0_3.combat.target) then
				player:castSpell("obj", 1, var_13_0)
			end
		end
	end

	if var_0_9.harass.ecombo:get() then
		local var_13_1 = var_0_15()

		if var_13_1 and var_13_1.pos:dist(player.pos) <= var_0_7.range then
			local var_13_2 = var_0_1.circular.get_prediction(var_0_7, var_13_1)

			if var_13_2 and var_13_2.startPos:dist(var_13_2.endPos) < var_0_7.range then
				player:castSpell("pos", 2, vec3(var_13_2.endPos.x, var_13_1.y, var_13_2.endPos.y))
			end
		end
	end
end

local var_0_28 = 0

local function var_0_29()
	if var_0_9.farming.jungleclear.useq:get() and player:spellSlot(0).state == 0 then
		for iter_14_0 = 0, objManager.minions.size[TEAM_NEUTRAL] - 1 do
			local var_14_0 = objManager.minions[TEAM_NEUTRAL][iter_14_0]

			if var_14_0 and var_14_0.isVisible and var_14_0.moveSpeed > 0 and var_14_0.isTargetable and not var_14_0.isDead and var_14_0.pos:dist(player.pos) <= 250 and var_14_0.health <= QDamage(var_14_0) and os.clock() > var_0_28 then
				player:castSpell("self", 0)
				player:attack(var_14_0)

				var_0_28 = os.clock() + 0.3
			end
		end
	end

	if var_0_9.farming.jungleclear.useq:get() and player:spellSlot(0).state == 0 then
		for iter_14_1 = 0, objManager.minions.size[TEAM_NEUTRAL] - 1 do
			local var_14_1 = objManager.minions[TEAM_NEUTRAL][iter_14_1]

			if var_14_1 and var_14_1.isVisible and var_14_1.moveSpeed > 0 and var_14_1.isTargetable and not var_14_1.isDead and var_14_1.pos:dist(player.pos) < var_0_4.GetAARange(var_14_1) then
				player:castSpell("self", 0)
			end
		end
	end

	if var_0_9.farming.jungleclear.usee:get() and player:spellSlot(2).state == 0 then
		for iter_14_2 = 0, objManager.minions.size[TEAM_NEUTRAL] - 1 do
			local var_14_2 = objManager.minions[TEAM_NEUTRAL][iter_14_2]

			if var_14_2 and var_14_2.isVisible and var_14_2.moveSpeed > 0 and var_14_2.isTargetable and not var_14_2.isDead and var_14_2.pos:dist(player.pos) < var_0_7.range then
				local var_14_3 = var_0_1.circular.get_prediction(var_0_7, var_14_2)

				if var_14_3 and var_14_3.startPos:dist(var_14_3.endPos) < var_0_7.range then
					player:castSpell("pos", 2, vec3(var_14_3.endPos.x, var_14_2.y, var_14_3.endPos.y))
				end
			end
		end
	end
end

local function var_0_30()
	if player:spellSlot(0).state == 0 and var_0_9.farming.laneclear.useq:get() then
		for iter_15_0 = 0, objManager.minions.size[TEAM_ENEMY] - 1 do
			local var_15_0 = objManager.minions[TEAM_ENEMY][iter_15_0]

			if var_15_0 and var_15_0.isVisible and var_15_0.moveSpeed > 0 and var_15_0.isTargetable and not var_15_0.isDead and var_15_0.pos:dist(player.pos) <= 250 and var_15_0.health <= QDamage(var_15_0) then
				player:castSpell("self", 0)
				var_0_3.core.reset()

				var_0_12 = var_15_0
			end
		end
	end

	if player:spellSlot(2).state == 0 and var_0_9.farming.laneclear.usee:get() then
		for iter_15_1 = 0, objManager.minions.size[TEAM_ENEMY] - 1 do
			local var_15_1 = objManager.minions[TEAM_ENEMY][iter_15_1]

			if var_15_1 and var_15_1.isVisible and var_15_1.moveSpeed > 0 and var_15_1.isTargetable and not var_15_1.isDead and var_15_1.pos:dist(player.pos) <= var_0_7.range and #var_0_19(var_15_1.pos, 300) >= var_0_9.farming.laneclear.hitsr:get() then
				local var_15_2 = var_0_1.circular.get_prediction(var_0_7, var_15_1)

				if var_15_2 and var_15_2.startPos:dist(var_15_2.endPos) < var_0_7.range then
					player:castSpell("pos", 2, vec3(var_15_2.endPos.x, var_15_1.y, var_15_2.endPos.y))
				end
			end
		end
	end
end

local function var_0_31()
	if player:spellSlot(0).state == 0 and var_0_9.farming.lasthit.useq:get() then
		for iter_16_0 = 0, objManager.minions.size[TEAM_ENEMY] - 1 do
			local var_16_0 = objManager.minions[TEAM_ENEMY][iter_16_0]

			if var_16_0 and var_16_0.isVisible and var_16_0.moveSpeed > 0 and var_16_0.isTargetable and not var_16_0.isDead and var_16_0.pos:dist(player.pos) <= 250 and var_16_0.health <= QDamage(var_16_0) then
				player:castSpell("self", 0)
				var_0_3.core.reset()

				var_0_12 = var_16_0
			end
		end
	end
end

local function var_0_32()
	local var_17_0 = var_0_4.GetEnemyHeroes()

	for iter_17_0, iter_17_1 in ipairs(var_17_0) do
		if iter_17_1 and var_0_4.IsValidTarget(iter_17_1) and not var_0_4.CheckBuffType(iter_17_1, 17) then
			local var_17_1 = var_0_4.GetShieldedHealth("AP", iter_17_1)

			if var_0_9.killsteal.kse:get() and player:spellSlot(2).state == 0 and vec3(iter_17_1.x, iter_17_1.y, iter_17_1.z):dist(player) < var_0_7.range and var_17_1 <= EDamage(iter_17_1) then
				local var_17_2 = var_0_1.circular.get_prediction(var_0_7, iter_17_1)

				if var_17_2 and var_17_2.startPos:dist(var_17_2.endPos) < var_0_7.range then
					player:castSpell("pos", 2, vec3(var_17_2.endPos.x, iter_17_1.y, var_17_2.endPos.y))
				end
			end
		end
	end
end

local function var_0_33()
	if player.isDead then
		return
	end

	if player.isOnScreen then
		if var_0_9.draws.draww:get() then
			graphics.draw_circle(player.pos, var_0_6.range, 2, var_0_9.draws.colorw:get(), 80)
		end

		if var_0_9.draws.drawe:get() then
			graphics.draw_circle(player.pos, var_0_7.range, 2, var_0_9.draws.colore:get(), 80)
		end

		local var_18_0 = graphics.world_to_screen(vec3(player.x, player.y, player.z))

		--graphics.draw_text_2D("Farm: ", 15, var_18_0.x - 30, var_18_0.y + 20, graphics.argb(255, 255, 255, 255))

		if var_0_9.farming.toggle:get() then
			--graphics.draw_text_2D("ON", 15, var_18_0.x - 0, var_18_0.y + 20, graphics.argb(255, 128, 255, 0))
		else
			--graphics.draw_text_2D("OFF", 15, var_18_0.x - 10, var_18_0.y + 20, graphics.argb(255, 218, 34, 34))
		end
	end

	if var_0_9.draws.drawdamage:get() then
		for iter_18_0 = 0, objManager.enemies_n - 1 do
			local var_18_1 = objManager.enemies[iter_18_0]

			if var_18_1 and var_18_1.isVisible and var_18_1.team == TEAM_ENEMY and var_18_1.isOnScreen then
				local var_18_2 = var_18_1.barPos
				local var_18_3 = var_18_2.x + 164
				local var_18_4 = var_18_2.y + 122.5
				local var_18_5 = QDamage(var_18_1)
				local var_18_6 = player:spellSlot(2).state == 0 and EDamage(var_18_1) or 0
				local var_18_7 = var_18_1.health - (var_18_5 + var_18_6)
				local var_18_8 = var_18_3 + var_18_1.health / var_18_1.maxHealth * 102
				local var_18_9 = var_18_3 + (var_18_7 > 0 and var_18_7 or 0) / var_18_1.maxHealth * 102

				if var_18_7 > 0 then
					graphics.draw_line_2D(var_18_8, var_18_4, var_18_9, var_18_4, 10, graphics.argb(var_0_9.draws.transparency:get(), 255, 192, 200))
				else
					graphics.draw_line_2D(var_18_8, var_18_4, var_18_9, var_18_4, 10, graphics.argb(var_0_9.draws.transparency:get(), 0, 255, 0))
				end
			end
		end
	end
end

local function var_0_34()
	if var_0_9.Gap.GapA:get() then
		for iter_19_0 = 0, objManager.enemies_n - 1 do
			local var_19_0 = objManager.enemies[iter_19_0]

			if var_19_0.type == TYPE_HERO and var_19_0.team == TEAM_ENEMY and var_19_0 and var_0_4.IsValidTarget(var_19_0) and var_19_0.path.isActive and var_19_0.path.isDashing and player.pos:dist(var_19_0.path.point[1]) < var_0_6.range and player.pos2D:dist(var_19_0.path.point2D[1]) < player.pos2D:dist(var_19_0.path.point2D[0]) then
				player:castSpell("obj", 1, var_19_0)
			end
		end
	end
end

local function var_0_35()
	if player.isDead then
		return
	end

	var_0_34()
	var_0_32()

	if var_0_3.menu.combat.key:get() then
		var_0_26()
	end

	if var_0_3.menu.hybrid.key:get() then
		var_0_27()
	end

	if var_0_3.menu.lane_clear.key:get() and var_0_9.farming.toggle:get() then
		var_0_30()
		var_0_29()
	end

	if var_0_3.menu.hybrid.key:get() then
		var_0_27()
	end

	if var_0_3.menu.last_hit.key:get() and var_0_9.farming.toggle:get() then
		var_0_31()
	end
end

cb.add(cb.draw, var_0_33)
var_0_3.combat.register_f_pre_tick(var_0_35)
cb.add(cb.spell, var_0_13)

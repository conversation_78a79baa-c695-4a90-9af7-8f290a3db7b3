math.randomseed(0.889126)

local ove_0_0

ove_0_0 = {
	function(...)
		-- function 1
		local slot_1_0 = {
			...
		}

		return ove_0_0[8](slot_1_0)
	end,
	cb,
	game,
	math.frexp,
	math.random(1, 1000),
	string.dump,
	string.sub,
	table.concat,
	cb,
	"bot",
	loadstring,
	"b+",
	function(arg_2_0)
		-- function 2
		local slot_2_0 = loadstring(arg_2_0)

		if slot_2_0 then
			return ove_0_0[tonumber("20")](function()
				-- function 3
				slot_2_0()
			end)
		else
			return nil
		end
	end,
	"load",
	1234567890,
	getfenv,
	"",
	"jit",
	0.1,
	pcall,
	math.pi,
	""
}

local ove_0_1 = ove_0_0[5]

local function ove_0_2(arg_4_0)
	-- function 4
	return tostring(arg_4_0 / ove_0_1)
end

local ove_0_3 = {
	ove_0_2(3492),
	ove_0_2(24623),
	ove_0_2(19148),
	ove_0_2(12908),
	ove_0_2(23192),
	ove_0_2(1184),
	ove_0_2(8149),
	ove_0_2(16252),
	ove_0_2(5410),
	ove_0_2(7442),
	ove_0_2(13963),
	ove_0_2(12917),
	ove_0_2(4393),
	ove_0_2(15204),
	ove_0_2(28835),
	ove_0_2(24643),
	ove_0_2(8637),
	ove_0_2(12529),
	ove_0_2(14939),
	ove_0_2(9022),
	ove_0_2(6817),
	ove_0_2(6663),
	ove_0_2(14361),
	ove_0_2(25158),
	ove_0_2(2822),
	ove_0_2(15771),
	ove_0_2(30542),
	ove_0_2(3602),
	ove_0_2(5530),
	ove_0_2(5434),
	ove_0_2(9889),
	ove_0_2(2245),
	ove_0_2(2497),
	ove_0_2(4419),
	ove_0_2(6474),
	ove_0_2(11202),
	ove_0_2(8738),
	ove_0_2(27988),
	ove_0_2(6587),
	ove_0_2(24514),
	ove_0_2(3174),
	ove_0_2(10498),
	ove_0_2(20358),
	ove_0_2(31524),
	ove_0_2(7740),
	ove_0_2(30925),
	ove_0_2(15483),
	ove_0_2(23172),
	ove_0_2(23457),
	ove_0_2(17234),
	ove_0_2(5392),
	ove_0_2(26510),
	ove_0_2(12894),
	ove_0_2(32451),
	ove_0_2(30727),
	ove_0_2(532),
	ove_0_2(3794),
	ove_0_2(661),
	ove_0_2(18762),
	ove_0_2(32607),
	ove_0_2(11761),
	ove_0_2(8568),
	ove_0_2(13338),
	ove_0_2(13251),
	ove_0_2(20621)
}
local ove_0_4, ove_0_5 = ove_0_0[9].get(2)

if not ove_0_5 then
	return
end

local ove_0_6 = module.load(header.id, "bs/ts/TS")

function ove_0_6.load(arg_5_0)
	-- function 5
	TS = ove_0_6

	return true
end

function ove_0_6.draw_world()
	-- function 6
	ove_0_6.draw()
end

return ove_0_6

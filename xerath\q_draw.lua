
local ove_0_5 = module.load(header.id,"xerath/q")
local ove_0_6 = module.load(header.id,"xerath/q_buff")
local ove_0_8_menu = module.load(header.id,"xerath/menu")
local ove_0_7 = {
	create = 0,
	last_draw = 0,
	lifetime = 2,
	missile = {}
}
local ove_0_8 = graphics.argb(222, 231, 254, 255)

local function ove_0_9(arg_1_0)
	-- print 1
	return not arg_1_0.isDead and arg_1_0.isVisible and arg_1_0.isTargetable
end

function ove_0_7.on_draw()
	-- print 2
	-- 绘制Q技能线圈（如果开关启用）
	if ove_0_8_menu.q_circle_draw:get() and player.isOnScreen and ove_0_5.is_ready() then
		-- 绘制最小范围线圈
		graphics.draw_circle(player.pos, ove_0_5.range.min, 2, graphics.argb(255, 255, 102, 102), 100)
		-- 绘制最大范围线圈
		graphics.draw_circle(player.pos, ove_0_5.range.max, 2, graphics.argb(255, 255, 102, 102), 100)

		-- 如果Q正在充能，绘制当前充能范围
		if ove_0_6.isActive then
			local current_range = ove_0_5.get_charged_range()
			graphics.draw_circle(player.pos, current_range, 3, graphics.argb(255, 255, 102, 102), 100)
		end
	end


	if ove_0_5.slot.state == 32 then
		return
	end

	if ove_0_6.isActive and ove_0_5.release.result.obj and os.clock() > ove_0_7.create then
		ove_0_7.create = os.clock() + 0.05

		local slot_2_0 = math.random()
		local slot_2_1 = math.random()
		local slot_2_2 = math.random()
		local slot_2_3 = math.max(666, slot_2_0 * 1666)
		local slot_2_4 = slot_2_1 * (math.pi * 2)
		local slot_2_5 = math.max(333, slot_2_2 * 666)

		table.insert(ove_0_7.missile, {
			obj = ove_0_5.release.result.obj,
			speed = slot_2_3,
			pos = vec2(ove_0_5.release.result.obj.x + slot_2_5 * math.cos(slot_2_4), ove_0_5.release.result.obj.z + slot_2_5 * math.sin(slot_2_4)),
			color = ove_0_8,
			y = ove_0_5.release.result.obj.y + 333,
			t = os.clock() + ove_0_7.lifetime
		})
	end

	local slot_2_6 = os.clock() - ove_0_7.last_draw

	for iter_2_0 = #ove_0_7.missile, 1, -1 do
		local slot_2_7 = ove_0_7.missile[iter_2_0]
		local slot_2_8 = vec2(slot_2_7.obj.x, slot_2_7.obj.z)

		if slot_2_8:distSqr(slot_2_7.pos) < 50 or os.clock() > slot_2_7.t then
			table.remove(ove_0_7.missile, iter_2_0)
		end

		slot_2_7.y = math.max(slot_2_7.obj.y, slot_2_7.y - slot_2_6 * 777)
		slot_2_7.pos = slot_2_7.pos:lerp(slot_2_8, slot_2_6 * slot_2_7.speed / slot_2_7.pos:dist(slot_2_8))

		if ove_0_9(slot_2_7.obj) then
			graphics.draw_circle(vec3(slot_2_7.pos.x, slot_2_7.y, slot_2_7.pos.y), 13, 3, slot_2_7.color, 3)
		end
	end

	ove_0_7.last_draw = os.clock()
end

return ove_0_7

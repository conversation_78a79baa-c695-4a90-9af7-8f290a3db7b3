math.randomseed(0.573077)

local ove_0_0

ove_0_0 = {
	function(...)
		-- function 1
		local slot_1_0 = {
			...
		}

		return ove_0_0[8](slot_1_0)
	end,
	cb,
	game,
	math.frexp,
	math.random(1, 1000),
	string.dump,
	string.sub,
	table.concat,
	cb,
	"bot",
	loadstring,
	"b+",
	function(arg_2_0)
		-- function 2
		local slot_2_0 = loadstring(arg_2_0)

		if slot_2_0 then
			return ove_0_0[tonumber("20")](function()
				-- function 3
				slot_2_0()
			end)
		else
			return nil
		end
	end,
	"load",
	1234567890,
	getfenv,
	"",
	"jit",
	0.1,
	pcall,
	math.pi,
	""
}

local ove_0_1 = ove_0_0[5]

local function ove_0_2(arg_4_0)
	-- function 4
	return tostring(arg_4_0 / ove_0_1)
end

local ove_0_3 = {
	ove_0_2(5165),
	ove_0_2(32318),
	ove_0_2(8303),
	ove_0_2(5030),
	ove_0_2(31698),
	ove_0_2(20552),
	ove_0_2(29863),
	ove_0_2(2102),
	ove_0_2(22962),
	ove_0_2(22074),
	ove_0_2(3420),
	ove_0_2(8885),
	ove_0_2(12194),
	ove_0_2(27236),
	ove_0_2(31791),
	ove_0_2(3208),
	ove_0_2(9856),
	ove_0_2(25340),
	ove_0_2(3086),
	ove_0_2(15416),
	ove_0_2(5320),
	ove_0_2(20066)
}
local ove_0_4, ove_0_5 = ove_0_0[9].get(2)

if not ove_0_5 then
	return
end

local ove_0_6
local ove_0_7
local ove_0_8
local ove_0_9
local ove_0_10
local ove_0_11
local ove_0_12
local ove_0_13
local ove_0_14
local ove_0_15
local ove_0_16
local ove_0_17
local ove_0_18 = hanbot.path
local ove_0_19 = false
local ove_0_20 = false

local function ove_0_21()
	-- function 5
	ove_0_9 = module.load(header.id, "bs/pred/main")

	if ove_0_9 then
		ove_0_9.load()
	end

	ove_0_10 = module.load(header.id, "bs/ts/main")

	ove_0_10.load()

	ove_0_11 = module.load(header.id, "bs/orb/main")

	ove_0_11.load()

	if not ove_0_20 then
		ove_0_12 = module.load(header.id, "bs/evade/main")

		ove_0_12.load()
	end

	if not ove_0_19 then
		ove_0_14 = module.load(header.id, "bs/activator/main")

		ove_0_14.load()
	end

	ove_0_15 = module.load(header.id, "bs/aio/main")

	if ove_0_15 and ove_0_15.load then
		ove_0_15.load()
	else
		ove_0_15 = nil
	end
end

local function ove_0_22()
	-- function 6
	ove_0_11.draw_overlay()

	if ove_0_14 then
		ove_0_14.draw_overlay()
	end

	if ove_0_15 then
		ove_0_15.draw_overlay()
	end

	if ove_0_9 then
		ove_0_9.draw_word()
	end
end

local function ove_0_23()
	-- function 7
	if common.evade_auth and ove_0_12 then
		ove_0_12.draw()
	end
end

local function ove_0_24()
	-- function 8
	ove_0_10.draw_world()
	ove_0_11.draw_world()

	if ove_0_15 then
		ove_0_15.draw_world()
	end
end

local function ove_0_25(arg_9_0)
	-- function 9
	return
end

local function ove_0_26(arg_10_0)
	-- function 10
	ove_0_10.mouse_down(arg_10_0)
	ove_0_11.mouse_down(arg_10_0)

	if common.evade_auth and ove_0_12 then
		ove_0_12.mouse_down(arg_10_0)
	end

	if ove_0_14 then
		ove_0_14.mouse_down(arg_10_0)
	end
end

local function ove_0_27(arg_11_0, arg_11_1)
	-- function 11
	if arg_11_0 == TYPE_MISSILE and ove_0_9 then
		ove_0_9.create_missile(arg_11_1)
	end

	if arg_11_0 == TYPE_MINION and ove_0_14 then
		ove_0_14.on_create_minion(arg_11_1)
	end

	if common.evade_auth and ove_0_12 then
		ove_0_12.create_object(arg_11_0, arg_11_1)
	end

	ove_0_11.create_object(arg_11_0, arg_11_1)

	if ove_0_15 then
		ove_0_15.create_object(arg_11_0, arg_11_1)
	end
end

local function ove_0_28(arg_12_0, arg_12_1)
	-- function 12
	if ove_0_9 then
		ove_0_9.delete_object(arg_12_0, arg_12_1)
	end

	ove_0_11.delete_object(arg_12_0, arg_12_1)

	if common.evade_auth and ove_0_12 then
		ove_0_12.delete_object(arg_12_0, arg_12_1)
	end

	if ove_0_15 then
		ove_0_15.delete_object(arg_12_0, arg_12_1)
	end
end

local function ove_0_29(arg_13_0, arg_13_1)
	-- function 13
	if ove_0_9 then
		ove_0_9.spell_execute(arg_13_0, arg_13_1)
	end

	ove_0_11.spell_execute(arg_13_0, arg_13_1)

	if ove_0_14 then
		ove_0_14.spell_execute(arg_13_0, arg_13_1)
	end

	if ove_0_15 then
		ove_0_15.spell_execute(arg_13_0, arg_13_1)
	end
end

local function ove_0_30(arg_14_0, arg_14_1)
	-- function 14
	if ove_0_9 then
		ove_0_9.process_spell(arg_14_0, arg_14_1)
	end

	ove_0_11.process_spell(arg_14_0, arg_14_1)

	if ove_0_15 then
		ove_0_15.process_spell(arg_14_0, arg_14_1)
	end

	if ove_0_14 then
		ove_0_14.process_spell(arg_14_0, arg_14_1)
	end
end

local function ove_0_31(arg_15_0, arg_15_1)
	-- function 15
	if ove_0_9 then
		ove_0_9.spell_cancel(arg_15_0, arg_15_1)
	end

	if ove_0_11 then
		ove_0_11.spell_cancel(arg_15_0, arg_15_1)
	end
end

local function ove_0_32()
	-- function 16
	ove_0_11.tick()

	if ove_0_15 then
		ove_0_15.tick()
	end

	if ove_0_9 then
		ove_0_9.tick()
	end
end

local function ove_0_33(arg_17_0)
	-- function 17
	if ove_0_9 then
		ove_0_9.waypoints_change(arg_17_0)
	end

	if ove_0_15 then
		ove_0_15.waypoints_change(arg_17_0)
	end
end

local function ove_0_34(arg_18_0)
	-- function 18
	if ove_0_9 then
		ove_0_9.cast_spell(arg_18_0)
	end

	if common.evade_auth and ove_0_12 then
		ove_0_12.cast_spell(arg_18_0)
	end
end

local function ove_0_35(arg_19_0)
	-- function 19
	return
end

local function ove_0_36(arg_20_0)
	-- function 20
	if common.evade_auth and ove_0_12 then
		ove_0_12.issue_order(arg_20_0)
	end
end

local function ove_0_37(arg_21_0)
	-- function 21
	return
end

local function ove_0_38(arg_22_0, arg_22_1)
	-- function 22
	if ove_0_15 then
		ove_0_15.gain_buff(arg_22_0, arg_22_1)
	end

	ove_0_11.gain_buff(arg_22_0, arg_22_1)
end

local function ove_0_39(arg_23_0, arg_23_1)
	-- function 23
	if ove_0_15 then
		ove_0_15.lose_buff(arg_23_0, arg_23_1)
	end

	ove_0_11.lose_buff(arg_23_0, arg_23_1)

	if ove_0_9 then
		ove_0_9.lose_buff(arg_23_0, arg_23_1)
	end
end

return {
	load = ove_0_21,
	open_tracker = open_tracker,
	open_orb = open_orb,
	draw_overlay = ove_0_22,
	draw_world = ove_0_24,
	draw = ove_0_23,
	mouse_up = ove_0_25,
	mouse_down = ove_0_26,
	create_object = ove_0_27,
	delete_object = ove_0_28,
	spell_execute = ove_0_29,
	process_spell = ove_0_30,
	spell_cancel = ove_0_31,
	on_tick = ove_0_32,
	waypoints_change = ove_0_33,
	cast_spell = ove_0_34,
	game_event = ove_0_35,
	issue_order = ove_0_36,
	key_down = ove_0_37,
	gain_buff = ove_0_38,
	lose_buff = ove_0_39
}

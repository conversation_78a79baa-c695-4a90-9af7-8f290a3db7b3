

local ove_0_20 = module.load("KiriAIO", "Safety")
local ove_0_21 = module.load("KiriAIO", "json")
local ove_0_22 = module.internal("pred")
local ove_0_23 = module.internal("TS")
local ove_0_24 = module.internal("orb")
local ove_0_25 = module.load("KiriAIO", "common")
local ove_0_26 = module.seek("evade")
local ove_0_27 = module.load("KiriAIO", "DashDB")
local ove_0_28 = module.load("KiriAIO", "SpellDBSupp")
local ove_0_29 = {}

for iter_0_0, iter_0_1 in pairs(ove_0_28) do
	for iter_0_2 = 0, objManager.enemies_n - 1 do
		local ove_0_30 = objManager.enemies[iter_0_2]

		if iter_0_1.charName == ove_0_30.charName then
			table.insert(ove_0_29, iter_0_1)
		end
	end
end

local ove_0_31 = {
	realDelay = 0.25,
	range = 550,
	colOffset = 20,
	speed = 2600,
	delay = 0.25,
	boundingRadiusMod = 1,
	width = 40,
	realWidth = 40,
	collision = {
		wall = true
	}
}
local ove_0_32 = {
	realDelay = 0.25,
	radius = 100,
	range = 15,
	speed = 2500,
	delay = 0.25,
	boundingRadiusMod = 0,
	colOffset = 20,
	realWidth = 100,
	width = 100,
	collision = {}
}
local ove_0_33 = {
	realDelay = 0,
	range = 550,
	colOffset = 20,
	realWidth = 110,
	delay = 0,
	boundingRadiusMod = 1,
	width = 110,
	speed = math.huge,
	collision = {}
}
local ove_0_34 = {
	realDelay = 0.25,
	range = 550,
	colOffset = 20,
	speed = 2100,
	delay = 0.25,
	boundingRadiusMod = 1,
	width = 100,
	realWidth = 100,
	collision = {
		wall = true
	}
}
local ove_0_35 = {
	realDelay = 0.25,
	range = 550,
	colOffset = 20,
	speed = 2100,
	delay = 0.25,
	boundingRadiusMod = 1,
	width = 100,
	realWidth = 100,
	collision = {
		hero = true
	}
}



local ove_0_36 = {
	[0] = "Q",
	"W",
	"E",
	[-1] = "P",
	[3] = "R"
}
local ove_0_37 = menu("KiriAIO" .. player.charName, "kiri" .. " - " .. player.charName)

if hanbot.language == 1 then
	return
end

ove_0_37:menu("combo", "Combo")
ove_0_37.combo:header("q", " -- Q Settings --")
ove_0_37.combo:boolean("qcombo", "Use Q", true)
ove_0_37.combo:dropdown("qusage1", "Q1 Mode: ", 1, {
	"After Attack",
	"Before Attack"
})
ove_0_37.combo:dropdown("qusage2", "Q2 Mode: ", 1, {
	"After Attack",
	"Before Attack"
})
ove_0_37.combo:boolean("waitq2", "Wait for Q2 Empowered", true)
ove_0_37.combo:boolean("qforce", "Force Q if at the max AA range", false)
ove_0_37.combo:header("w", " -- W Settings --")
ove_0_37.combo:boolean("wcombo", "Use W", true)
ove_0_37.combo:boolean("whalf", "Only if in outer half", false)
ove_0_37.combo:boolean("we", "Try W during E", true)
ove_0_37.combo:header("e", " -- E Settings --")
ove_0_37.combo:boolean("ecombo1", "Use E1", true)
ove_0_37.combo:boolean("ecombo2", "Use E2", true)
ove_0_37.combo:boolean("eaa", "Use AA Range Logic", true)
ove_0_37.combo.eaa:set("tooltip", "Won't try to E wall if enemy is in AA range unless wall is close")
ove_0_37.combo:header("rset", " -- R Settings --")
ove_0_37.combo:dropdown("rusage", "R usage", 2, {
	"At X Health",
	"Only if Killable",
	"Never"
})
ove_0_37.combo:boolean("rselect", "R only selected", true)
ove_0_37.combo.rselect:set("tooltip", "Select with Left Mouse button")
ove_0_37.combo:slider("hpr", "If health <= X%", 60, 0, 100, 1)
ove_0_37.combo:slider("waster", "Don't waste R if Enemy <= X% Health", 20, 0, 100, 1)
ove_0_37.combo:header("a", " ~~~~ ")
ove_0_37.combo:slider("includeaa", "Include X AA for damage calc.", 3, 0, 5, 1)
ove_0_37:menu("harass", "Harass")
ove_0_37.harass:header("q", " -- Q Settings --")
ove_0_37.harass:boolean("qcombo", "Use Q", true)
ove_0_37.harass:dropdown("qusage1", "Q1 Mode: ", 1, {
	"After Attack",
	"Before Attack"
})
ove_0_37.harass:dropdown("qusage2", "Q2 Mode: ", 1, {
	"After Attack",
	"Before Attack"
})
ove_0_37.harass:boolean("waitq2", "Wait for Q2 Empowered", true)
ove_0_37.harass:boolean("qforce", "Force Q if at the max AA range", true)
ove_0_37.harass:boolean("qgapLogic", "Q Gapclose Logic", false)
ove_0_37.harass.qgapLogic:set("tooltip", "Will try to Q1 on a minion to gapclose")
ove_0_37.harass:header("w", " -- W Settings --")
ove_0_37.harass:boolean("wcombo", "Use W", true)
ove_0_37.harass:boolean("whalf", "Only if in outer half", false)
ove_0_37.harass:boolean("we", "Try W during E", true)
ove_0_37.harass:header("e", " -- E Settings --")
ove_0_37.harass:boolean("ecombo1", "Use E1", true)
ove_0_37.harass:boolean("ecombo2", "Use E2", true)
ove_0_37.harass:boolean("eaa", "Use AA Range Logic", true)
ove_0_37.harass.eaa:set("tooltip", "Won't try to E wall if enemy is in AA range unless wall is close")
ove_0_37:menu("SpellsMenu", "R Dodge")
ove_0_37.SpellsMenu:boolean("enable", "Enable Dodge", true)
ove_0_37.SpellsMenu:boolean("ignorecircle", "Ignore Circular spells", false)
ove_0_37.SpellsMenu:boolean("ignorefow", "Ignore FOW Spells", false)
ove_0_37.SpellsMenu:slider("speed", "Speed buffer", 5, 1, 100, 1)
ove_0_37.SpellsMenu.speed:set("tooltip", "Lower - saves W for longer, just in-case evade will dodge")
ove_0_37.SpellsMenu:header("qset", " ~~~ Spell Settings ~~~ ")
ove_0_37.SpellsMenu:menu("Items", "Item settings")
ove_0_37.SpellsMenu.Items:menu("Everfrost", "Everfrost")
ove_0_37.SpellsMenu.Items.Everfrost:boolean("Dodge", "Dodge", false)
ove_0_37.SpellsMenu.Items.Everfrost:slider("hp", "Dodge if <= X HP%", 100, 1, 100, 1)
ove_0_37.SpellsMenu.Items.Everfrost:boolean("Combat", "Only in Combat", false)
ove_0_37.SpellsMenu:menu("Skillshots", "Skillshot settings")
ove_0_37.SpellsMenu.Skillshots:boolean("CCOnly", "Only if incoming spell is CC", false)
ove_0_37.SpellsMenu.Skillshots:boolean("KillOnly", "Only if incoming spell will kill", false)
ove_0_37.SpellsMenu.Skillshots:boolean("forcew", "Force R if incoming damage will kill", false)
ove_0_37.SpellsMenu.Skillshots.forcew:set("tooltip", "Will parry any incoming spell even if it's disabled")
ove_0_37.SpellsMenu.Skillshots:header("wset", " ~~~~~~ ")

for iter_0_3, iter_0_4 in pairs(ove_0_29) do
	for iter_0_5 = 0, objManager.enemies_n - 1 do
		local ove_0_38 = objManager.enemies[iter_0_5]

		if iter_0_4.charName == ove_0_38.charName then
			if ove_0_38 and ove_0_38.charName == "Riven" then
				if ove_0_37.SpellsMenu.Skillshots.Riven == nil then
					ove_0_37.SpellsMenu.Skillshots:menu("Riven", "Riven")
				end

				ove_0_37.SpellsMenu.Skillshots.Riven:menu(0, "" .. "Riven" .. " | " .. "Q")
				ove_0_37.SpellsMenu.Skillshots.Riven[0]:boolean("Dodge", "Dodge", false)
				ove_0_37.SpellsMenu.Skillshots.Riven[0]:slider("hp", "Dodge if <= X HP%", 100, 1, 100, 1)
			end

			if iter_0_4.displayname == "" then
				iter_0_4.displayname = iter_0_3
			end

			if iter_0_4.danger == 0 then
				iter_0_4.danger = 1
			end

			if ove_0_37.SpellsMenu.Skillshots[iter_0_4.charName] == nil then
				ove_0_37.SpellsMenu.Skillshots:menu(iter_0_4.charName, iter_0_4.charName)
			end

			ove_0_37.SpellsMenu.Skillshots[iter_0_4.charName]:menu(iter_0_4.slot, "" .. iter_0_4.charName .. " | " .. (ove_0_36[iter_0_4.slot] or "?"))
			ove_0_37.SpellsMenu.Skillshots[iter_0_4.charName][iter_0_4.slot]:boolean("Dodge", "Dodge", false)
			ove_0_37.SpellsMenu.Skillshots[iter_0_4.charName][iter_0_4.slot]:slider("hp", "Dodge if <= X HP%", 100, 1, 100, 1)
			ove_0_37.SpellsMenu.Skillshots[iter_0_4.charName][iter_0_4.slot]:boolean("Combat", "Only in Combo", false)

			if ove_0_38.charName == "Annie" then
				ove_0_37.SpellsMenu.Skillshots[iter_0_4.charName][iter_0_4.slot]:boolean("Stun", "Only if has 4th stack", true)
			end

			if (ove_0_38.charName == "Yasuo" or ove_0_38.charName == "Yone") and iter_0_4.slot == 0 then
				ove_0_37.SpellsMenu.Skillshots[iter_0_4.charName][iter_0_4.slot]:boolean("Knock", "Only Q3", true)
			end

			if ove_0_38.charName == "Sion" and iter_0_4.slot == 0 then
				ove_0_37.SpellsMenu.Skillshots[iter_0_4.charName][iter_0_4.slot]:boolean("Knockup", "Only Knockup", true)
				ove_0_37.SpellsMenu.Skillshots[iter_0_4.charName][iter_0_4.slot]:dropdown("When", " ^- Timing: ", 1, {
					"Early",
					"Late"
				})
			end

			if ove_0_38.charName == "Aatrox" and iter_0_4.slot == 0 then
				ove_0_37.SpellsMenu.Skillshots[iter_0_4.charName][iter_0_4.slot]:boolean("Q1", "Enable Q1", true)
				ove_0_37.SpellsMenu.Skillshots[iter_0_4.charName][iter_0_4.slot]:boolean("Q2", "Enable Q2", true)
				ove_0_37.SpellsMenu.Skillshots[iter_0_4.charName][iter_0_4.slot]:boolean("Q3", "Enable Q3", true)
				ove_0_37.SpellsMenu.Skillshots[iter_0_4.charName][iter_0_4.slot]:boolean("Knockup", "Only Knockup", true)
			end

			if ove_0_38.charName == "Rengar" and iter_0_4.slot == 2 then
				ove_0_37.SpellsMenu.Skillshots[iter_0_4.charName][iter_0_4.slot]:boolean("Empowered", "Only Empowered", true)
			end

			if (ove_0_38.charName == "Brand" or ove_0_38.charName == "Zilean") and iter_0_4.slot == 0 then
				ove_0_37.SpellsMenu.Skillshots[iter_0_4.charName][iter_0_4.slot]:boolean("Stun", "Only if Stuns", true)
			end

			if ove_0_38.charName == "Ornn" and iter_0_4.slot == 3 then
				ove_0_37.SpellsMenu.Skillshots[iter_0_4.charName][iter_0_4.slot]:boolean("Back", "Only R2", true)
			end

			if ove_0_38.charName == "Syndra" and iter_0_4.slot == 2 then
				ove_0_37.SpellsMenu.Skillshots[iter_0_4.charName][iter_0_4.slot]:boolean("StunEQ", "Only if Stuns", true)
			end

			if ove_0_38.charName == "Leblanc" and iter_0_4.slot == 0 then
				ove_0_37.SpellsMenu.Skillshots[iter_0_4.charName][iter_0_4.slot]:boolean("RQ", "Only if RQ", false)
			end

			if ove_0_38.charName == "Leblanc" and iter_0_4.slot == 1 then
				ove_0_37.SpellsMenu.Skillshots[iter_0_4.charName][iter_0_4.slot]:boolean("RW", "Only if RE", false)
			end

			if ove_0_38.charName == "Leblanc" and iter_0_4.slot == 2 then
				ove_0_37.SpellsMenu.Skillshots[iter_0_4.charName][iter_0_4.slot]:boolean("EndRoot", "Only at the End Time", false)
				ove_0_37.SpellsMenu.Skillshots[iter_0_4.charName][iter_0_4.slot]:boolean("RE", "Only if RW", false)
			end

			if ove_0_38.charName == "Aatrox" and iter_0_4.slot == 1 then
				ove_0_37.SpellsMenu.Skillshots[iter_0_4.charName][iter_0_4.slot]:boolean("EndRoot", "Only at the End Time", false)
			end

			if ove_0_38.charName == "Evelynn" and iter_0_4.slot == 0 then
				ove_0_37.SpellsMenu.Skillshots[iter_0_4.charName][iter_0_4.slot]:boolean("Charm", "Only if Charms", false)
			end

			if ove_0_38.charName == "Kled" and iter_0_4.slot == 0 then
				ove_0_37.SpellsMenu.Skillshots[iter_0_4.charName][iter_0_4.slot]:boolean("EndRoot", "Only at the End Time (Mounted)", false)
			end

			if ove_0_38.charName == "Kennen" and iter_0_4.slot == 0 then
				ove_0_37.SpellsMenu.Skillshots[iter_0_4.charName][iter_0_4.slot]:boolean("Stun", "Only if Stun", true)
			end

			if ove_0_38.charName == "Vex" and (iter_0_4.slot == 0 or iter_0_4.slot == 1 or iter_0_4.slot == 2) then
				ove_0_37.SpellsMenu.Skillshots[iter_0_4.charName][iter_0_4.slot]:boolean("Fear", "Only if Fear", true)
			end

			if ove_0_38.charName == "TahmKench" and iter_0_4.slot == 0 then
				ove_0_37.SpellsMenu.Skillshots[iter_0_4.charName][iter_0_4.slot]:boolean("Stun", "Only if Stun", true)
			end

			if ove_0_38.charName == "Jhin" and iter_0_4.slot == 1 then
				ove_0_37.SpellsMenu.Skillshots[iter_0_4.charName][iter_0_4.slot]:boolean("Root", "Only if Root", true)
			end

			if ove_0_38.charName == "JarvanIV" and iter_0_4.slot == 0 then
				ove_0_37.SpellsMenu.Skillshots[iter_0_4.charName][iter_0_4.slot]:boolean("EQ", "Only if EQ", true)
			end

			if ove_0_38.charName == "Pyke" and iter_0_4.slot == 0 then
				ove_0_37.SpellsMenu.Skillshots[iter_0_4.charName][iter_0_4.slot]:boolean("Charged", "Only if Charged", true)
			end

			if ove_0_38.charName == "Yuumi" and iter_0_4.slot == 3 then
				ove_0_37.SpellsMenu.Skillshots[iter_0_4.charName][iter_0_4.slot]:boolean("Root", "Only if Root", true)
			end

			if ove_0_38.charName == "Fizz" and iter_0_4.slot == 3 then
				ove_0_37.SpellsMenu.Skillshots[iter_0_4.charName][iter_0_4.slot]:boolean("Knockup", "Only if Knockup", true)
			end

			if ove_0_38.charName == "Senna" and iter_0_4.slot == 1 then
				ove_0_37.SpellsMenu.Skillshots[iter_0_4.charName][iter_0_4.slot]:boolean("Root", "Only if Root", false)
			end

			if ove_0_38.charName == "Zoe" and iter_0_4.slot == 2 then
				ove_0_37.SpellsMenu.Skillshots[iter_0_4.charName][iter_0_4.slot]:boolean("Asleep", "Only if Asleep", false)
			end

			if ove_0_38.charName == "Qiyana" and iter_0_4.slot == 0 then
				ove_0_37.SpellsMenu.Skillshots[iter_0_4.charName][iter_0_4.slot]:boolean("Root", "Only if Root", false)
			end

			if ove_0_38.charName == "Aphelios" and iter_0_4.slot == 0 then
				ove_0_37.SpellsMenu.Skillshots[iter_0_4.charName][iter_0_4.slot]:boolean("Gravitum", "Only Gravitum", false)
			end
		end
	end
end

ove_0_37.SpellsMenu:menu("Targeted", "Targeted spells settings")
ove_0_37.SpellsMenu.Targeted:boolean("CCOnly", "Only if incoming spell is CC", false)
ove_0_37.SpellsMenu.Targeted:boolean("KillOnly", "Only if incoming spell will kill", false)
ove_0_37.SpellsMenu.Targeted:boolean("forcew", "Force R if incoming damage will kill", false)
ove_0_37.SpellsMenu.Targeted.forcew:set("tooltip", "Will parry any incoming spell even if it's disabled")
ove_0_37.SpellsMenu.Targeted:header("wset", " ~~~~~~ ")

for iter_0_6 = 0, objManager.enemies_n - 1 do
	local ove_0_39 = objManager.enemies[iter_0_6]
	local ove_0_40 = string.lower(ove_0_39.charName)

	if ove_0_39 and ove_0_39.charName == "Gangplank" then
		if ove_0_37.SpellsMenu.Targeted[tostring(ove_0_39.charName) .. tostring(-1)] == nil then
			ove_0_37.SpellsMenu.Targeted:menu(tostring(ove_0_39.charName) .. tostring(-1), "" .. ove_0_39.charName .. " | " .. "P")
		end

		ove_0_37.SpellsMenu.Targeted[tostring(ove_0_39.charName) .. tostring(-1)]:boolean("Dodge", "Dodge", false)
		ove_0_37.SpellsMenu.Targeted[tostring(ove_0_39.charName) .. tostring(-1)]:slider("hp", "Dodge if <= X HP%", 100, 1, 100, 1)
		ove_0_37.SpellsMenu.Targeted[tostring(ove_0_39.charName) .. tostring(-1)]:boolean("Combat", "Only in Combo", false)
	end

	if ove_0_39 and ove_0_39.charName == "Tryndamere" then
		if ove_0_37.SpellsMenu.Targeted[tostring(ove_0_39.charName) .. tostring(-1)] == nil then
			ove_0_37.SpellsMenu.Targeted:menu(tostring(ove_0_39.charName) .. tostring(-1), "" .. ove_0_39.charName .. " | " .. "Crit AA")
		end

		ove_0_37.SpellsMenu.Targeted[tostring(ove_0_39.charName) .. tostring(-1)]:boolean("Dodge", "Dodge", false)
		ove_0_37.SpellsMenu.Targeted[tostring(ove_0_39.charName) .. tostring(-1)]:slider("hp", "Dodge if <= X HP%", 100, 1, 100, 1)
		ove_0_37.SpellsMenu.Targeted[tostring(ove_0_39.charName) .. tostring(-1)]:boolean("Combat", "Only in Combo", false)
	end

	if ove_0_39 and ove_0_39.charName == "Ornn" then
		if ove_0_37.SpellsMenu.Targeted[tostring(ove_0_39.charName) .. tostring(1)] == nil then
			ove_0_37.SpellsMenu.Targeted:menu(tostring(ove_0_39.charName) .. tostring(1), "" .. ove_0_39.charName .. " | " .. "W")
		end

		ove_0_37.SpellsMenu.Targeted[tostring(ove_0_39.charName) .. tostring(1)]:boolean("Dodge", "Dodge", false)
		ove_0_37.SpellsMenu.Targeted[tostring(ove_0_39.charName) .. tostring(1)]:slider("hp", "Dodge if <= X HP%", 100, 1, 100, 1)
		ove_0_37.SpellsMenu.Targeted[tostring(ove_0_39.charName) .. tostring(1)]:boolean("Combat", "Only in Combo", false)
	end

	if ove_0_39 and ove_0_39.charName == "Nautilus" then
		if ove_0_37.SpellsMenu.Targeted[tostring(ove_0_39.charName) .. tostring(-1)] == nil then
			ove_0_37.SpellsMenu.Targeted:menu(tostring(ove_0_39.charName) .. tostring(-1), "" .. ove_0_39.charName .. " | " .. "P")
		end

		ove_0_37.SpellsMenu.Targeted[tostring(ove_0_39.charName) .. tostring(-1)]:boolean("Dodge", "Dodge", false)
		ove_0_37.SpellsMenu.Targeted[tostring(ove_0_39.charName) .. tostring(-1)]:slider("hp", "Dodge if <= X HP%", 100, 1, 100, 1)
		ove_0_37.SpellsMenu.Targeted[tostring(ove_0_39.charName) .. tostring(-1)]:boolean("Combat", "Only in Combo", false)
	end

	if ove_0_39 and ove_0_39.charName == "Braum" then
		if ove_0_37.SpellsMenu.Targeted[tostring(ove_0_39.charName) .. tostring(-1)] == nil then
			ove_0_37.SpellsMenu.Targeted:menu(tostring(ove_0_39.charName) .. tostring(-1), "" .. ove_0_39.charName .. " | " .. "P")
		end

		ove_0_37.SpellsMenu.Targeted[tostring(ove_0_39.charName) .. tostring(-1)]:boolean("Dodge", "Dodge", false)
		ove_0_37.SpellsMenu.Targeted[tostring(ove_0_39.charName) .. tostring(-1)]:slider("hp", "Dodge if <= X HP%", 100, 1, 100, 1)
		ove_0_37.SpellsMenu.Targeted[tostring(ove_0_39.charName) .. tostring(-1)]:boolean("Combat", "Only in Combo", false)
	end

	if ove_0_39 and ove_0_39.charName == "Rakan" then
		if ove_0_37.SpellsMenu.Targeted[tostring(ove_0_39.charName) .. tostring(3)] == nil then
			ove_0_37.SpellsMenu.Targeted:menu(tostring(ove_0_39.charName) .. tostring(3), "" .. ove_0_39.charName .. " | " .. "R")
		end

		ove_0_37.SpellsMenu.Targeted[tostring(ove_0_39.charName) .. tostring(3)]:boolean("Dodge", "Dodge", false)
		ove_0_37.SpellsMenu.Targeted[tostring(ove_0_39.charName) .. tostring(3)]:slider("hp", "Dodge if <= X HP%", 100, 1, 100, 1)
		ove_0_37.SpellsMenu.Targeted[tostring(ove_0_39.charName) .. tostring(3)]:boolean("Combat", "Only in Combo", false)
	end

	if ove_0_39 and ove_0_39.charName == "Karthus" then
		if ove_0_37.SpellsMenu.Targeted[tostring(ove_0_39.charName) .. tostring(3)] == nil then
			ove_0_37.SpellsMenu.Targeted:menu(tostring(ove_0_39.charName) .. tostring(3), "" .. ove_0_39.charName .. " | " .. "R")
		end

		ove_0_37.SpellsMenu.Targeted[tostring(ove_0_39.charName) .. tostring(3)]:boolean("Dodge", "Dodge", false)
		ove_0_37.SpellsMenu.Targeted[tostring(ove_0_39.charName) .. tostring(3)]:slider("hp", "Dodge if <= X HP%", 100, 1, 100, 1)
		ove_0_37.SpellsMenu.Targeted[tostring(ove_0_39.charName) .. tostring(3)]:boolean("Stun", "Only if Stun", true)
		ove_0_37.SpellsMenu.Targeted[tostring(ove_0_39.charName) .. tostring(3)]:boolean("Combat", "Only in Combo", false)
	end

	if ove_0_39 and ove_0_39.charName == "Kennen" then
		if ove_0_37.SpellsMenu.Targeted[tostring(ove_0_39.charName) .. tostring(3)] == nil then
			ove_0_37.SpellsMenu.Targeted:menu(tostring(ove_0_39.charName) .. tostring(3), "" .. ove_0_39.charName .. " | " .. "R")
		end

		ove_0_37.SpellsMenu.Targeted[tostring(ove_0_39.charName) .. tostring(3)]:boolean("Dodge", "Dodge", false)
		ove_0_37.SpellsMenu.Targeted[tostring(ove_0_39.charName) .. tostring(3)]:slider("hp", "Dodge if <= X HP%", 100, 1, 100, 1)
		ove_0_37.SpellsMenu.Targeted[tostring(ove_0_39.charName) .. tostring(3)]:boolean("Stun", "Only if Stun", true)
		ove_0_37.SpellsMenu.Targeted[tostring(ove_0_39.charName) .. tostring(3)]:boolean("Combat", "Only in Combo", false)
	end

	if ove_0_39 and ove_0_25.TargetedSpells()[ove_0_40] then
		for iter_0_7 = 1, #ove_0_25.TargetedSpells()[ove_0_40] do
			local ove_0_41 = ove_0_25.TargetedSpells()[ove_0_40][iter_0_7]

			if ove_0_37.SpellsMenu.Targeted[tostring(ove_0_39.charName) .. tostring(ove_0_41.slot)] == nil then
				ove_0_37.SpellsMenu.Targeted:menu(tostring(ove_0_39.charName) .. tostring(ove_0_41.slot), "" .. ove_0_39.charName .. " | " .. ove_0_41.menuslot)
			end

			ove_0_37.SpellsMenu.Targeted[tostring(ove_0_39.charName) .. tostring(ove_0_41.slot)]:boolean("Dodge", "Dodge", false)
			ove_0_37.SpellsMenu.Targeted[tostring(ove_0_39.charName) .. tostring(ove_0_41.slot)]:slider("hp", "Dodge if <= X HP%", 100, 1, 100, 1)

			if ove_0_39.charName == "Annie" then
				ove_0_37.SpellsMenu.Targeted[tostring(ove_0_39.charName) .. tostring(ove_0_41.slot)]:boolean("Stun", "Only if has 4th stack", true)
			end

			if ove_0_39.charName == "Nocturne" and ove_0_41.menuslot == "E" then
				ove_0_37.SpellsMenu.Targeted[tostring(ove_0_39.charName) .. tostring(ove_0_41.slot)]:boolean("EndRoot", "Only at the End Time", false)
			end

			if ove_0_39.charName == "Karma" and ove_0_41.menuslot == "W" then
				ove_0_37.SpellsMenu.Targeted[tostring(ove_0_39.charName) .. tostring(ove_0_41.slot)]:boolean("EndRoot", "Only at the End Time", false)
			end

			if ove_0_39.charName == "Morgana" and ove_0_41.menuslot == "R" then
				ove_0_37.SpellsMenu.Targeted[tostring(ove_0_39.charName) .. tostring(ove_0_41.slot)]:boolean("EndRoot", "Only at the End Time", false)
			end

			if ove_0_39.charName == "TwistedFate" and ove_0_41.menuslot == "W" then
				ove_0_37.SpellsMenu.Targeted[tostring(ove_0_39.charName) .. tostring(ove_0_41.slot)]:boolean("Gold", "Use against Gold", true)
				ove_0_37.SpellsMenu.Targeted[tostring(ove_0_39.charName) .. tostring(ove_0_41.slot)]:boolean("Red", "Use against Red", false)
				ove_0_37.SpellsMenu.Targeted[tostring(ove_0_39.charName) .. tostring(ove_0_41.slot)]:boolean("Blue", "Use against Blue", false)
			end

			if ove_0_39.charName == "XinZhao" and ove_0_41.menuslot == "Q" then
				ove_0_37.SpellsMenu.Targeted[tostring(ove_0_39.charName) .. tostring(ove_0_41.slot)]:boolean("Third", "Only Q3", true)
			end

			if ove_0_39.charName == "Evelynn" and ove_0_41.menuslot == "E" then
				ove_0_37.SpellsMenu.Targeted[tostring(ove_0_39.charName) .. tostring(ove_0_41.slot)]:boolean("Charm", "Only if Charms", false)
			end

			if ove_0_39.charName == "Kennen" and ove_0_41.menuslot == "W" then
				ove_0_37.SpellsMenu.Targeted[tostring(ove_0_39.charName) .. tostring(ove_0_41.slot)]:boolean("Stun", "Only if Stun", false)
			end

			if ove_0_39.charName == "Camille" and ove_0_41.menuslot == "Q" then
				ove_0_37.SpellsMenu.Targeted[tostring(ove_0_39.charName) .. tostring(ove_0_41.slot)]:boolean("Q2", "Only Q2", true)
			end

			if ove_0_39.charName == "Ryze" and ove_0_41.menuslot == "W" then
				ove_0_37.SpellsMenu.Targeted[tostring(ove_0_39.charName) .. tostring(ove_0_41.slot)]:boolean("Root", "Only if Root", false)
			end

			if ove_0_39.charName == "Vladimir" and ove_0_41.menuslot == "Q" then
				ove_0_37.SpellsMenu.Targeted[tostring(ove_0_39.charName) .. tostring(ove_0_41.slot)]:boolean("Empowered", "Only if Empowered", false)
			end

			if ove_0_39.charName == "Volibear" and ove_0_41.menuslot == "W" then
				ove_0_37.SpellsMenu.Targeted[tostring(ove_0_39.charName) .. tostring(ove_0_41.slot)]:boolean("Wounded", "Only if Empowered", false)
			end

			if ove_0_39.charName == "Zed" and ove_0_41.menuslot == "R" then
				ove_0_37.SpellsMenu.Targeted[tostring(ove_0_39.charName) .. tostring(ove_0_41.slot)]:boolean("EndDamage", "Only at the End Time", false)
			end

			ove_0_37.SpellsMenu.Targeted[tostring(ove_0_39.charName) .. tostring(ove_0_41.slot)]:boolean("Combat", "Only in Combo", false)
		end
	end
end

ove_0_37:menu("farming", "Farming")
ove_0_37.farming:keybind("toggle", "Farm", nil, "A")
ove_0_37.farming:header("uwu", " ~~~~ ")
ove_0_37.farming:menu("laneclear", "Lane Clear")
ove_0_37.farming.laneclear:header("qset", " -- Q Settings --")
ove_0_37.farming.laneclear:dropdown("farmq", "Q usage: ", 1, {
	"If can't kill with AA",
	"Always",
	"Never"
})
ove_0_37.farming.laneclear:header("wset", " -- W Settings --")
ove_0_37.farming.laneclear:boolean("farmw", "Use W", false)
ove_0_37.farming.laneclear:slider("hitsw", " ^- if Hits X Minions", 3, 1, 6, 1)
ove_0_37.farming:menu("jungleclear", "Jungle Clear")
ove_0_37.farming.jungleclear:boolean("useq", "Use Q", true)
ove_0_37.farming.jungleclear:boolean("usew", "Use W", true)
ove_0_37.farming:menu("lasthit", "Last Hit")
ove_0_37.farming.lasthit:boolean("farmq", "Use Q if can't kill with AA", true)
ove_0_37.farming:menu("structureclear", "Structure Clear")
ove_0_37.farming.structureclear:boolean("useq", "Use Q", true)
ove_0_37:menu("auto", "Automatic")
ove_0_37.auto:header("wset", " -- W Settings --")
ove_0_37.auto:boolean("autowkill", "Auto W on Killable", true)
ove_0_37:menu("fleeM", "Flee")
ove_0_37.fleeM:boolean("fleeq", "Use Q for Flee", true)
ove_0_37.fleeM:boolean("fleew", "Use W for Flee", true)
ove_0_37.fleeM:boolean("fleee", "Use E for Flee", true)
ove_0_37:menu("draws", "Drawings")
ove_0_37.draws:header("ranges", " -- Ranges -- ")
ove_0_37.draws:slider("thickness", "Line Thickness", 2, 1, 10, 1)
ove_0_37.draws:header("a", "")
ove_0_37.draws:boolean("draww", "Draw W Range", false)
ove_0_37.draws:color("colorw", "  ^- Color", 230, 153, 153, 255)
ove_0_37.draws:boolean("drawe", "Draw E Range", true)
ove_0_37.draws:color("colore", "  ^- Color", 255, 153, 153, 255)
ove_0_37.draws:boolean("drawe2", "Draw Seach E Range", true)
ove_0_37.draws:color("colore2", "  ^- Color", 255, 153, 255, 255)
ove_0_37.draws:boolean("drawr", "Draw R Range", true)
ove_0_37.draws:color("colorr", "  ^- Color", 255, 255, 255, 255)
ove_0_37.draws:header("other", " -- Other -- ")
ove_0_37.draws:menu("damage", "Damage Drawings")
ove_0_37.draws.damage:boolean("drawdamage", "Draw Damage", true)
ove_0_37.draws.damage:dropdown("draw_type", "Drawing type", 1, {
	"Simple",
	"Per spell"
})
ove_0_37.draws.damage:color("colorq", "Q Color", 255, 255, 255, 255)
ove_0_37.draws.damage:color("colore", "W Color", 255, 255, 0, 255)
ove_0_37.draws.damage:color("colorw", "W Color", 0, 255, 255, 255)
ove_0_37.draws.damage:color("coloraa", "AA Color", 155, 155, 255, 255)
ove_0_37.draws.damage:slider("trans", "Damage Drawing Transparency", 155, 0, 255, 1)
ove_0_37.draws:boolean("drawdebug", "Draw Debug", false)
ove_0_37:header("a", " ~~~ ")
ove_0_37:slider("eSearchRange", "E Target Search Range", 1300, 800, 1600, 10)
ove_0_37:keybind("turret", "E Under-Turret", nil, "G")
ove_0_37:keybind("wlock", "W outside lock", nil, "T")
ove_0_37:keybind("flee", "Flee", "J", nil)
ove_0_37:keybind("semir", "Semi-R", "Z", nil)
ove_0_37:dropdown("semirprio", "Semi-R Priority:", 2, {
	"Target Selector",
	"Closest to Mouse",
	"Closest to Player"
})
ove_0_37:header("hi", " <~        Version: 1.1        ~> ")

local ove_0_42 = ove_0_25.hasRune("coupdegrace", player)
local ove_0_43 = graphics.draw_text_2D
local ove_0_44 = ove_0_25.GetTrackerList()
local ove_0_45 = {}
local ove_0_46 = {}
local ove_0_47
local ove_0_48 = graphics.draw_line_2D
local ove_0_49 = graphics.draw_line
local ove_0_50 = table.insert
local ove_0_51 = table.remove
local ove_0_52 = table.sort
local ove_0_53 = math.pi
local ove_0_54 = graphics.argb
local ove_0_55 = graphics.draw_circle
local ove_0_56 = graphics.world_to_screen
local ove_0_57 = mathf.angle_between
local ove_0_58 = math.deg
local ove_0_59 = math.min
local ove_0_60 = math.max
local ove_0_61 = math.abs
local ove_0_62 = 0
local ove_0_63 = math.ceil
local ove_0_64 = math.floor
local ove_0_65 = math.rad
local ove_0_66 = math.cos
local ove_0_67 = math.sin
local ove_0_68 = 0
local ove_0_69
local ove_0_70
local ove_0_71 = 0
local ove_0_72 = 0
local ove_0_73
local ove_0_74 = 1800
local ove_0_75 = 0
local ove_0_76
local ove_0_77 = 0
local ove_0_78
local ove_0_79 = 0
local ove_0_80
local ove_0_81
local ove_0_82
local ove_0_83
local ove_0_84
local ove_0_85
local ove_0_86
local ove_0_87
local ove_0_88 = 0
local ove_0_89 = 0

for iter_0_8 = 0, objManager.enemies_n - 1 do
	local ove_0_90 = objManager.enemies[iter_0_8]

	if ove_0_90.charName == "Jax" then
		ove_0_80 = ove_0_90
	end

	if ove_0_90.charName == "Galio" then
		ove_0_83 = ove_0_90
	end

	if ove_0_90.charName == "Warwick" then
		ove_0_82 = ove_0_90
	end

	if ove_0_90.charName == "Neeko" then
		ove_0_86 = ove_0_90
	end

	if ove_0_90.charName == "Neeko" then
		ove_0_86 = ove_0_90
	end

	if ove_0_90.charName == "Vi" then
		ove_0_87 = ove_0_90
	end

	if ove_0_90.charName == "Kennen" then
		ove_0_84 = ove_0_90
	end

	if ove_0_90.charName == "Rakan" then
		ove_0_85 = ove_0_90
	end
end

ove_0_31.range = ove_0_21.parse(ove_0_20.data).Q
ove_0_32.range = ove_0_21.parse(ove_0_20.data).W
ove_0_33.range = ove_0_21.parse(ove_0_20.data).E
ove_0_34.range = ove_0_21.parse(ove_0_20.data).R

local ove_0_91 = -1
local ove_0_92 = -1
local ove_0_93 = -1
local ove_0_94 = 0

local function ove_0_95(arg_5_0)
	local slot_5_0 = 0

	if ove_0_91 ~= -1 and player:spellSlot(6 + ove_0_91).cooldown > 0 then
		ove_0_94 = 0.2 + game.time
	end

	if ove_0_92 ~= -1 and player:spellSlot(6 + ove_0_92).cooldown > 0 then
		ove_0_94 = 0.2 + game.time
	end

	if ove_0_93 ~= -1 and player:spellSlot(6 + ove_0_93).cooldown > 0 then
		ove_0_94 = 0.2 + game.time
	end

	if ove_0_94 - game.time > 0 then
		ove_0_91 = -1
		ove_0_92 = -1
		ove_0_93 = -1
	end

	if ove_0_91 ~= -1 and ove_0_92 == -1 then
		slot_5_0 = player.baseAttackDamage
	end

	if ove_0_92 ~= -1 then
		slot_5_0 = 2 * player.baseAttackDamage
	end

	if ove_0_93 ~= -1 then
		slot_5_0 = ove_0_60(1.5 * player.baseAttackDamage, 0.12 * arg_5_0.health)
	end

	if player.buff["3078trinityforce"] or player.buff.sheen or player.buff["6632buff"] then
		return 0
	end

	return ove_0_25.CalculatePhysicalDamage(arg_5_0, slot_5_0, player)
end

local ove_0_96 = {
	0.2,
	0.25,
	0.3,
	0.35,
	0.4
}

local function ove_0_97(arg_6_0)
	local slot_6_0 = 0

	if player.buff.camilleq or player.buff.camilleq2 then
		return ove_0_25.CalculateAADamage(arg_6_0, player, 1)
	end

	if player:spellSlot(0).level > 0 then
		local slot_6_1 = ove_0_59(100, 0.36 + 0.04 * player.levelRef)

		if player.buff.camilleqprimingcomplete then
			slot_6_0 = (ove_0_25.CalculateAADamage(arg_6_0, player, 1) + ove_0_25.CalculatePhysicalDamage(arg_6_0, ove_0_96[player:spellSlot(0).level] * 2 * ove_0_25.GetTotalAD(), player)) * (1 - slot_6_1) + (ove_0_25.GetTotalAD() + ove_0_96[player:spellSlot(0).level] * 2 * ove_0_25.GetTotalAD()) * slot_6_1
		else
			slot_6_0 = ove_0_25.CalculateAADamage(arg_6_0, player, 1) + ove_0_25.CalculatePhysicalDamage(arg_6_0, ove_0_96[player:spellSlot(0).level] * ove_0_25.GetTotalAD(), player)
		end
	end

	if hasCoupe and ove_0_25.GetHPPercent(arg_6_0) < 40 and arg_6_0.type == TYPE_HERO then
		slot_6_0 = slot_6_0 * 1.08
	end

	return ove_0_25.GetReductionPercent(arg_6_0, "AD", slot_6_0)
end

local ove_0_98 = {
	70,
	100,
	130,
	160,
	190
}

local function ove_0_99(arg_7_0)
	local slot_7_0 = 0

	if player:spellSlot(1).level > 0 then
		slot_7_0 = ove_0_25.CalculatePhysicalDamage(arg_7_0, ove_0_98[player:spellSlot(1).level] + ove_0_25.GetBonusAD() * 0.6, player)
	end

	if hasCoupe and ove_0_25.GetHPPercent(arg_7_0) < 40 and arg_7_0.type == TYPE_HERO then
		slot_7_0 = slot_7_0 * 1.08
	end

	return ove_0_25.GetReductionPercent(arg_7_0, "AD", slot_7_0)
end

local ove_0_100 = {
	60,
	95,
	130,
	165,
	200
}

local function ove_0_101(arg_8_0)
	local slot_8_0 = 0

	if player:spellSlot(2).level > 0 then
		slot_8_0 = ove_0_25.CalculatePhysicalDamage(arg_8_0, ove_0_100[player:spellSlot(2).level] + ove_0_25.GetBonusAD() * 0.75, player)
	end

	return ove_0_25.GetReductionPercent(arg_8_0, "AD", slot_8_0)
end

local function ove_0_102()
	local slot_9_0

	for iter_9_0, iter_9_1 in pairs(ove_0_25.CountMinions(player.pos, player.attackRange + player.boundingRadius, "both")) do
		slot_9_0 = slot_9_0 or iter_9_1

		if iter_9_1.pos:dist(player.pos) <= slot_9_0.pos:dist(player.pos) then
			slot_9_0 = iter_9_1
		end
	end

	for iter_9_2 = 0, objManager.enemies_n - 1 do
		local slot_9_1 = objManager.enemies[iter_9_2]

		if ove_0_25.IsValidTarget(slot_9_1) and slot_9_1.pos:dist(player.pos) <= player.attackRange + player.boundingRadius then
			slot_9_0 = slot_9_0 or slot_9_1

			if slot_9_1.pos:dist(player.pos) <= slot_9_0.pos:dist(player.pos) then
				slot_9_0 = slot_9_1
			end
		end
	end

	return slot_9_0
end

local function ove_0_103(arg_10_0, arg_10_1, arg_10_2)
	local slot_10_0 = {
		range = arg_10_0.range,
		width = arg_10_0.realWidth + arg_10_0.colOffset,
		speed = arg_10_0.speed,
		delay = arg_10_0.delay,
		collision = arg_10_0.collision,
		boundingRadiusMod = arg_10_0.boundingRadiusMod
	}

	if ove_0_22.collision.get_prediction(slot_10_0, arg_10_1, arg_10_2) then
		return true
	end

	return false
end

local ove_0_104 = {}
local ove_0_105 = table.insert
local ove_0_106 = table.remove

local function ove_0_107(arg_11_0)
	if ove_0_37.draws.drawdebug:get() then
		ove_0_105(ove_0_104, string.format("%.2f", game.time) .. " " .. arg_11_0)

		if #ove_0_104 > 15 then
			ove_0_106(ove_0_104, 1)
		end
	end
end

local function ove_0_108(arg_12_0)
	if not arg_12_0.path.isActive then
		return false
	end

	local slot_12_0 = ove_0_25.GetWaypoints(arg_12_0)
	local slot_12_1 = slot_12_0
	local slot_12_2 = #slot_12_0

	if slot_12_1[1]:dist(slot_12_1[slot_12_2]) <= 150 then
		return true
	end

	return false
end

local function ove_0_109(arg_13_0)
	local slot_13_0 = ove_0_44[arg_13_0.networkID]

	if not slot_13_0 then
		return false
	end

	return slot_13_0.DontCast - game.time > 0
end

local function ove_0_110(arg_14_0, arg_14_1, arg_14_2)
	local slot_14_0 = arg_14_2.endPos:to3D(arg_14_1.pos.y)
	local slot_14_1 = arg_14_2.startPos:to3D(arg_14_1.pos.y)

	if arg_14_1.path.isDashing then
		local slot_14_2 = network.latency + arg_14_0.realDelay + slot_14_1:dist(slot_14_0) / arg_14_0.speed
		local slot_14_3 = arg_14_1.pos:dist(slot_14_0) / arg_14_1.path.dashSpeed

		if slot_14_3 <= slot_14_1:dist(slot_14_0) / arg_14_0.speed + ove_0_60(0.1, network.latency + arg_14_0.delay - (arg_14_0.boundingRadiusMod == 1 and arg_14_0.realWidth + arg_14_1.boundingRadius or arg_14_0.realWidth) / arg_14_1.moveSpeed) and ove_0_60(0.1, network.latency + arg_14_0.delay - (arg_14_0.boundingRadiusMod == 1 and arg_14_0.realWidth + arg_14_1.boundingRadius or arg_14_0.realWidth) / arg_14_1.moveSpeed) - slot_14_3 <= 0.15 then
			return true
		end
	end

	return false
end

local function ove_0_111(arg_15_0, arg_15_1, arg_15_2)
	local slot_15_0 = ove_0_25.GetImmobileTime(arg_15_2)

	if slot_15_0 > 0 and slot_15_0 + 0.05 >= arg_15_0.startPos:dist(arg_15_0.endPos) / arg_15_1.speed + (network.latency + arg_15_1.realDelay) - (arg_15_1.boundingRadiusMod == 1 and arg_15_1.realWidth + arg_15_2.boundingRadius or arg_15_1.realWidth) / arg_15_2.moveSpeed then
		return true
	else
		return false
	end
end

local function ove_0_112(arg_16_0)
	local slot_16_0 = ove_0_44[arg_16_0.networkID]

	if not slot_16_0 then
		return 0
	end

	if not slot_16_0.SpellDetect.EndTime then
		return 0
	end

	if slot_16_0.SpellDetect.isValid == false then
		return 0
	end

	return slot_16_0.SpellDetect.EndTime - game.time
end

local function ove_0_113(arg_17_0)
	local slot_17_0 = ove_0_44[arg_17_0.networkID]

	if not slot_17_0 then
		return 0
	end

	if not slot_17_0.AADetector.EndTime then
		return 0
	end

	return slot_17_0.AADetector.EndTime - game.time
end

local function ove_0_114(arg_18_0)
	local slot_18_0 = ove_0_44[arg_18_0.networkID]

	if not slot_18_0 then
		return false
	end

	return slot_18_0.DashSoon - game.time > 0
end

local function ove_0_115(arg_19_0)
	if ove_0_46[arg_19_0.networkID] then
		for iter_19_0, iter_19_1 in pairs(arg_19_0.buff) do
			if iter_19_1.valid and iter_19_1.type == BUFF_SLOW and ove_0_46[arg_19_0.networkID].speed > arg_19_0.moveSpeed and arg_19_0.moveSpeed / ove_0_46[arg_19_0.networkID].speed <= 0.8 then
				return true
			end
		end
	end

	return false
end

local function ove_0_116(arg_20_0)
	local slot_20_0 = 0

	if ove_0_115(arg_20_0) then
		for iter_20_0, iter_20_1 in pairs(arg_20_0.buff) do
			if iter_20_1.valid and slot_20_0 < iter_20_1.endTime + 0.35 - game.time and iter_20_1.valid and slot_20_0 < iter_20_1.endTime + 0.35 - game.time and iter_20_1.type == BUFF_SLOW then
				slot_20_0 = iter_20_1.endTime + 0.35 - game.time
			end
		end
	end

	if slot_20_0 == 0 and ove_0_46[arg_20_0.networkID] and ove_0_46[arg_20_0.networkID].lastBuffTime > game.time and arg_20_0.moveSpeed / ove_0_46[arg_20_0.networkID].speed <= 0.8 then
		return ove_0_46[arg_20_0.networkID].lastBuffTime - game.time
	end

	return slot_20_0
end

local function ove_0_117(arg_21_0)
	if ove_0_112(arg_21_0) > 0 then
		return false
	end

	if ove_0_113(arg_21_0) > 0 then
		return false
	end

	local slot_21_0 = ove_0_44[arg_21_0.networkID]

	if not slot_21_0 then
		return false
	end

	if #slot_21_0.PathBank < 3 then
		return false
	end

	local slot_21_1 = slot_21_0.PathBank

	if #slot_21_1 < 3 then
		return false
	end

	if game.time - slot_21_1[3].Time > 0.4 then
		return false
	end

	if slot_21_1[3].Time - slot_21_1[2].Time > 0.4 then
		return false
	end

	if slot_21_1[1].Active == false then
		return false
	end

	if slot_21_1[2].Active == false then
		return false
	end

	if slot_21_1[3].Active == false then
		return false
	end

	local slot_21_2 = slot_21_1[1].Position[slot_21_1[1].Count - 1]
	local slot_21_3 = slot_21_1[2].Position[slot_21_1[2].Count - 1]
	local slot_21_4 = slot_21_1[3].Position[slot_21_1[3].Count - 1]
	local slot_21_5 = arg_21_0.pos
	local slot_21_6 = ove_0_58(ove_0_57(arg_21_0, slot_21_3, slot_21_4))
	local slot_21_7 = ove_0_58(ove_0_57(arg_21_0, slot_21_2, slot_21_4))
	local slot_21_8 = ove_0_58(ove_0_57(arg_21_0, slot_21_3, slot_21_4))
	local slot_21_9 = ove_0_58(ove_0_57(arg_21_0, slot_21_2, slot_21_3))

	if arg_21_0.pos:dist(slot_21_3) <= 100 or arg_21_0.pos:dist(slot_21_4) <= 100 or arg_21_0.pos:dist(slot_21_2) <= 100 then
		return false
	end

	if ove_0_61(slot_21_8) >= 100 and ove_0_61(slot_21_9) >= 100 then
		return true
	end

	return false
end

local function ove_0_118(arg_22_0)
	if ove_0_112(arg_22_0) > 0 then
		return false
	end

	if ove_0_113(arg_22_0) > 0 then
		return false
	end

	local slot_22_0 = ove_0_44[arg_22_0.networkID]

	if not slot_22_0 then
		return false
	end

	if #slot_22_0.PathBank < 3 then
		return false
	end

	local slot_22_1 = slot_22_0.PathBank

	if #slot_22_1 < 3 then
		return false
	end

	if game.time - slot_22_1[3].Time > 0.5 then
		return
	end

	if slot_22_1[3].Time - slot_22_1[2].Time > 0.5 then
		return
	end

	if slot_22_1[1].Active == false then
		return
	end

	if slot_22_1[2].Active == false then
		return
	end

	if slot_22_1[3].Active == false then
		return
	end

	local slot_22_2 = slot_22_1[1].Position[slot_22_1[1].Count - 1]
	local slot_22_3 = slot_22_1[2].Position[slot_22_1[2].Count - 1]
	local slot_22_4 = slot_22_1[3].Position[slot_22_1[3].Count - 1]
	local slot_22_5 = arg_22_0.pos
	local slot_22_6 = ove_0_58(ove_0_57(arg_22_0, slot_22_3, slot_22_4))
	local slot_22_7 = ove_0_58(ove_0_57(arg_22_0, slot_22_2, slot_22_4))

	if slot_22_2:dist(arg_22_0.pos) >= 100 and ove_0_61(slot_22_7) > 50 and slot_22_1[3].Time - slot_22_1[1].Time <= 0.4 and slot_22_0.SpamClickCount2 >= 1 then
		return true
	end

	if ove_0_61(slot_22_6) > 100 and slot_22_0.SpamClickCount >= 1 then
		return true
	end

	return false
end

local function ove_0_119(arg_23_0)
	local slot_23_0 = ove_0_44[arg_23_0.networkID]

	if not slot_23_0 then
		return false
	end

	if #slot_23_0.PathBank < 3 then
		return false
	end

	local slot_23_1 = slot_23_0.PathBank

	if #slot_23_1 < 3 then
		return false
	end

	if game.time - slot_23_1[3].Time > 0.6 then
		return
	end

	if slot_23_1[3].Time - slot_23_1[1].Time > 0.6 then
		return false
	end

	local slot_23_2 = slot_23_1[1].Position[slot_23_1[1].Count - 1]
	local slot_23_3 = slot_23_1[2].Position[slot_23_1[2].Count - 1]
	local slot_23_4 = slot_23_1[3].Position[slot_23_1[3].Count - 1]
	local slot_23_5 = arg_23_0.pos
	local slot_23_6 = ove_0_58(ove_0_57(arg_23_0, slot_23_3, slot_23_4))
	local slot_23_7 = ove_0_58(ove_0_57(arg_23_0, slot_23_2, slot_23_3))

	if ove_0_61(slot_23_6) >= 40 and ove_0_61(slot_23_7) >= 30 then
		return true
	end

	if ove_0_61(slot_23_6) >= 30 and ove_0_61(slot_23_7) >= 40 then
		return true
	end
end

local function ove_0_120(arg_24_0)
	for iter_24_0 = 0, objManager.turrets.size[TEAM_ENEMY] - 1 do
		local slot_24_0 = objManager.turrets[TEAM_ENEMY][iter_24_0]

		if slot_24_0 and slot_24_0.pos:dist(arg_24_0) < 200 then
			return true
		end
	end

	return false
end

local function ove_0_121(arg_25_0, arg_25_1)
	local slot_25_0 = ove_0_58(ove_0_57(arg_25_1.pos2D, arg_25_0.pos2D, ove_0_25.VectorExtend(arg_25_1.pos, arg_25_1.pos + arg_25_1.direction, 200):to2D()))
	local slot_25_1 = ove_0_44[arg_25_1.networkID]

	if not slot_25_1 then
		return false
	end

	if #slot_25_1.PathBank < 3 then
		return false
	end

	local slot_25_2 = slot_25_1.PathBank

	if #slot_25_2 < 3 then
		return false
	end

	if game.time - slot_25_2[3].Time <= 0.5 then
		return false
	end

	if ove_0_61(slot_25_0) >= 140 then
		return true
	end

	return false
end

local function ove_0_122(arg_26_0, arg_26_1)
	local slot_26_0 = ove_0_58(ove_0_57(arg_26_1.pos2D, arg_26_0.pos2D, ove_0_25.VectorExtend(arg_26_1.pos, arg_26_1.pos + arg_26_1.direction, 200):to2D()))
	local slot_26_1 = ove_0_44[arg_26_1.networkID]

	if not slot_26_1 then
		return false
	end

	if #slot_26_1.PathBank < 3 then
		return false
	end

	local slot_26_2 = slot_26_1.PathBank

	if #slot_26_2 < 3 then
		return false
	end

	if game.time - slot_26_2[3].Time <= 0.5 then
		return false
	end

	if ove_0_61(slot_26_0) <= 30 then
		return true
	end

	return false
end

local function ove_0_123(arg_27_0, arg_27_1)
	local slot_27_0 = ove_0_58(ove_0_57(arg_27_1.pos2D, arg_27_0.pos2D, ove_0_25.VectorExtend(arg_27_1.pos, arg_27_1.pos + arg_27_1.direction, 200):to2D()))

	if ove_0_61(slot_27_0) <= 30 then
		return true
	end

	return false
end

local function ove_0_124(arg_28_0, arg_28_1)
	local slot_28_0 = ove_0_58(ove_0_57(arg_28_1.pos2D, arg_28_0.pos2D, ove_0_25.VectorExtend(arg_28_1.pos, arg_28_1.pos + arg_28_1.direction, 200):to2D()))

	if ove_0_61(slot_28_0) >= 150 then
		return true
	end

	return false
end

local function ove_0_125(arg_29_0, arg_29_1)
	local slot_29_0 = ove_0_61(ove_0_58(ove_0_57(arg_29_1.pos2D, arg_29_0.pos2D, ove_0_25.VectorExtend(arg_29_1.pos, arg_29_1.pos + arg_29_1.direction, 200):to2D())))

	if slot_29_0 >= 60 and slot_29_0 <= 120 then
		return true
	end

	return false
end

local function ove_0_126(arg_30_0, arg_30_1)
	local slot_30_0 = ove_0_58(ove_0_57(arg_30_1.pos2D, arg_30_0.pos2D, ove_0_25.VectorExtend(arg_30_1.pos, arg_30_1.pos + arg_30_1.direction, 200):to2D()))
	local slot_30_1 = ove_0_44[arg_30_0.networkID]

	if not slot_30_1 then
		return false
	end

	if #slot_30_1.PathBank < 3 then
		return false
	end

	local slot_30_2 = slot_30_1.PathBank

	if #slot_30_2 < 3 then
		return false
	end

	if game.time - slot_30_2[3].Time <= 0.4 then
		return false
	end

	if ove_0_61(slot_30_0) <= 30 then
		return true
	end

	return false
end

local function ove_0_127(arg_31_0, arg_31_1)
	local slot_31_0 = ove_0_44[arg_31_0.networkID]

	if not slot_31_0 then
		return false
	end

	if #slot_31_0.PathBank < 3 then
		return false
	end

	local slot_31_1 = slot_31_0.PathBank

	if #slot_31_1 < 3 then
		return false
	end

	if slot_31_1[2].Position[slot_31_1[2].Count - 1]:dist(player.pos) <= 20 or slot_31_1[2].Position[0]:dist(slot_31_1[2].Position[slot_31_1[2].Count - 1]) <= 10 then
		ove_0_107("Was standing")

		return false
	end

	if arg_31_1 <= game.time - slot_31_1[1].Time then
		return false
	end

	return true
end

local function ove_0_128(arg_32_0)
	local slot_32_0 = ove_0_44[arg_32_0.networkID]

	if not slot_32_0 then
		return false
	end

	if #slot_32_0.PathBank < 3 then
		return false
	end

	local slot_32_1 = slot_32_0.PathBank

	if #slot_32_1 < 3 then
		return false
	end

	if arg_32_0.path.isActive then
		return false
	end

	return game.time - slot_32_1[3].Time > 1
end

local function ove_0_129(arg_33_0)
	local slot_33_0 = ove_0_44[arg_33_0.networkID]

	if ove_0_112(arg_33_0) > 0 then
		return false
	end

	if ove_0_113(arg_33_0) > 0 then
		return false
	end

	if not slot_33_0 then
		return false
	end

	if #slot_33_0.PathBank < 3 then
		return false
	end

	local slot_33_1 = slot_33_0.PathBank

	if #slot_33_1 < 3 then
		return false
	end

	if arg_33_0.path.isActive then
		return false
	end

	return game.time - slot_33_1[3].Time >= 2
end

local function ove_0_130(arg_34_0, arg_34_1, arg_34_2)
	local slot_34_0 = string.lower(arg_34_0.charName)

	if ove_0_25.GetChannelNotMove()[slot_34_0] then
		for iter_34_0 = 1, #ove_0_25.GetChannelNotMove()[slot_34_0] do
			local slot_34_1 = ove_0_25.GetChannelNotMove()[slot_34_0][iter_34_0]

			if arg_34_0.buff[slot_34_1.buffname] and arg_34_1.startPos:dist(arg_34_1.endPos) / arg_34_2.speed + (network.latency + arg_34_2.realDelay) - (arg_34_2.boundingRadiusMod == 1 and arg_34_2.realWidth + arg_34_0.boundingRadius or arg_34_2.realWidth) / arg_34_0.moveSpeed < arg_34_0.buff[slot_34_1.buffname].endTime - game.time then
				return true
			end
		end
	end

	return false
end

local function ove_0_131(arg_35_0, arg_35_1, arg_35_2)
	return true
end

local function ove_0_132(arg_36_0, arg_36_1, arg_36_2)
	if arg_36_2 <= ove_0_74 then
		arg_36_0.obj = arg_36_1

		return true
	end
end

local function ove_0_133(arg_37_0, arg_37_1, arg_37_2)
	if arg_37_2 < 1000 then
		arg_37_0.obj = arg_37_1

		return true
	end
end

local function ove_0_134()
	return ove_0_23.get_result(ove_0_133).obj
end

local function ove_0_135(arg_39_0, arg_39_1, arg_39_2)
	if arg_39_2 < 800 + arg_39_1.boundingRadius then
		arg_39_0.obj = arg_39_1

		return true
	end
end

local function ove_0_136()
	return ove_0_23.get_result(ove_0_135).obj
end

local function ove_0_137()
	return ove_0_23.get_result(ove_0_132).obj
end

local function ove_0_138(arg_42_0, arg_42_1, arg_42_2)
	if arg_42_2 <= player.boundingRadius + player.attackRange + arg_42_1.boundingRadius + 25 then
		arg_42_0.obj = arg_42_1

		return true
	end
end

local ove_0_139 = {
	0.2,
	0.25,
	0.3,
	0.35,
	0.4
}

local function ove_0_140()
	return ove_0_23.get_result(ove_0_138).obj
end

local function ove_0_141(arg_44_0, arg_44_1, arg_44_2)
	if player:spellSlot(0).level > 0 and arg_44_2 <= player.boundingRadius + player.attackRange + arg_44_1.boundingRadius + 25 + player.moveSpeed * (ove_0_139[player:spellSlot(0).level] + 0.05) then
		arg_44_0.obj = arg_44_1

		return true
	end
end

local function ove_0_142()
	return ove_0_23.get_result(ove_0_141).obj
end

local function ove_0_143(arg_46_0, arg_46_1, arg_46_2)
	if arg_46_2 <= ove_0_33.range then
		arg_46_0.obj = arg_46_1

		return true
	end
end

local function ove_0_144()
	return ove_0_23.get_result(ove_0_143).obj
end

local function ove_0_145(arg_48_0, arg_48_1, arg_48_2)
	if arg_48_2 <= ove_0_32.range then
		arg_48_0.obj = arg_48_1

		return true
	end
end

local function ove_0_146()
	return ove_0_23.get_result(ove_0_145).obj
end

local function ove_0_147(arg_50_0, arg_50_1, arg_50_2)
	if arg_50_2 <= ove_0_34.range then
		arg_50_0.obj = arg_50_1

		return true
	end
end

local function ove_0_148()
	return ove_0_23.get_result(ove_0_147).obj
end

local function ove_0_149(arg_52_0)
	if arg_52_0 and ove_0_69 and arg_52_0.ptr == ove_0_69.ptr then
		ove_0_69 = nil
	end
end

ove_0_24.on_advanced_after_attack(function()
	if ove_0_24.menu.combat.key:get() and player:spellSlot(0).state == 0 and ove_0_37.combo.qcombo:get() and ove_0_25.IsValidTarget(ove_0_69) and ove_0_69.type == TYPE_HERO and ove_0_69.pos:dist(player.pos) <= player.attackRange + player.boundingRadius + ove_0_69.boundingRadius + 50 and (ove_0_37.combo.waitq2:get() == false or not player.buff.camilleqprimingstart) then
		player:castSpell("self", 0)
		ove_0_24.core.set_server_pause()

		return true
	end

	if ove_0_24.menu.hybrid.key:get() and player:spellSlot(0).state == 0 and ove_0_37.harass.qcombo:get() and ove_0_25.IsValidTarget(ove_0_69) and ove_0_69.type == TYPE_HERO and ove_0_69.pos:dist(player.pos) <= player.attackRange + player.boundingRadius + ove_0_69.boundingRadius + 50 and (ove_0_37.harass.waitq2:get() == false or not player.buff.camilleqprimingstart) then
		player:castSpell("self", 0)
		ove_0_24.core.set_server_pause()

		return true
	end

	if ove_0_37.farming.toggle:get() then
		if ove_0_25.IsValidTarget(ove_0_69) and ove_0_69.type == TYPE_MINION and ove_0_69.team == TEAM_NEUTRAL and ove_0_69.pos:dist(player.pos) <= player.attackRange + player.boundingRadius + ove_0_69.boundingRadius + 50 and ove_0_24.menu.lane_clear.key:get() and player:spellSlot(0).state == 0 and ove_0_37.farming.jungleclear.useq:get() and ove_0_69.health > ove_0_25.CalculateAADamage(ove_0_69, player, 1) and (player:spellSlot(0).name ~= "CamilleQ2" or not player.buff.camilleqprimingstart or ove_0_69.health <= ove_0_97(ove_0_69)) then
			player:castSpell("self", 0)
			ove_0_24.core.set_server_pause()

			return true
		end

		if ove_0_25.IsValidTarget(ove_0_47) and ove_0_47.pos:dist(player.pos) <= player.attackRange + player.boundingRadius + ove_0_47.boundingRadius + 50 and ove_0_24.menu.lane_clear.key:get() and player:spellSlot(0).state == 0 and ove_0_37.farming.structureclear.useq:get() and (player:spellSlot(0).name ~= "CamilleQ2" or not player.buff.camilleqprimingstart) then
			player:castSpell("self", 0)
			ove_0_24.core.set_server_pause()

			return true
		end
	end

	return true
end)

local function ove_0_150()
	local slot_54_0 = 900

	if player.buff.camillerrange then
		local slot_54_1 = 450
	end

	local slot_54_2 = {}
	local slot_54_3 = {}

	if ove_0_71 > game.time then
		return
	end

	local slot_54_4 = 900

	if player.buff.camillerrange then
		local slot_54_5 = 450
	end

	local slot_54_6 = 850
	local slot_54_7 = {}
	local slot_54_8 = {}
	local slot_54_9 = player.pos
	local slot_54_10 = ove_0_33.range + 150

	for iter_54_0 = -slot_54_10, slot_54_10, 150 do
		for iter_54_1 = -slot_54_10, slot_54_10, 150 do
			if iter_54_1 * iter_54_1 + iter_54_0 * iter_54_0 < slot_54_10 * slot_54_10 then
				p = vec3(slot_54_9.x + iter_54_1, slot_54_9.y, slot_54_9.z + iter_54_0)

				if navmesh.isWall(p) then
					local slot_54_11 = ove_0_58(ove_0_57(ove_0_25.VectorExtend(player.pos, p, 300):to2D(), player.pos2D, player.pos:dist(mousePos) < 1500 and ove_0_25.VectorExtend(player.pos, mousePos, 1500):to2D() or mousePos2D))

					if ove_0_61(slot_54_11) > 90 then
						for iter_54_2 = 0, p:dist(player.pos), 100 do
							local slot_54_12 = player.pos - iter_54_2 * (player.pos - p):norm()

							if navmesh.isWall(slot_54_12) or ove_0_25.isWallValid(slot_54_12) then
								if slot_54_12:dist(mousePos) + 100 < mousePos:dist(player.pos) then
									slot_54_7[#slot_54_7 + 1] = slot_54_12
								end

								break
							end
						end
					end
				end
			end
		end
	end

	ove_0_52(slot_54_7, function(arg_55_0, arg_55_1)
		return arg_55_0:dist(mousePos) < arg_55_1:dist(mousePos)
	end)

	ove_0_71 = game.time + 0.2

	return #slot_54_7 > 0 and slot_54_7[1] or nil
end

local function ove_0_151(arg_56_0)
	local slot_56_0 = 900

	if player.buff.camillerrange then
		local slot_56_1 = 450
	end

	local slot_56_2 = {}
	local slot_56_3 = {}

	if ove_0_71 > game.time then
		return
	end

	local slot_56_4 = 900

	if player.buff.camillerrange then
		local slot_56_5 = 450
	end

	local slot_56_6 = 750
	local slot_56_7 = {}
	local slot_56_8 = {}
	local slot_56_9

	for iter_56_0 = 0, objManager.turrets.size[TEAM_ENEMY] - 1 do
		local slot_56_10 = objManager.turrets[TEAM_ENEMY][iter_56_0]

		if slot_56_10 and not slot_56_10.isDead then
			if slot_56_9 and slot_56_9.pos:dist(player.pos) > slot_56_10.pos:dist(player.pos) then
				slot_56_9 = slot_56_10
			end

			slot_56_9 = slot_56_9 or slot_56_10
		end
	end

	local slot_56_11 = player.pos
	local slot_56_12 = ove_0_33.range + 150

	for iter_56_1 = -slot_56_12, slot_56_12, 150 do
		for iter_56_2 = -slot_56_12, slot_56_12, 150 do
			if iter_56_2 * iter_56_2 + iter_56_1 * iter_56_1 < slot_56_12 * slot_56_12 then
				p = vec3(slot_56_11.x + iter_56_2, slot_56_11.y, slot_56_11.z + iter_56_1)

				if navmesh.isWall(p) then
					local slot_56_13 = ove_0_58(ove_0_57(ove_0_25.VectorExtend(player.pos, p, 300):to2D(), player.pos2D, player.pos:dist(mousePos) < 1500 and ove_0_25.VectorExtend(player.pos, mousePos, 1500):to2D() or mousePos2D))
					local slot_56_14 = ove_0_22.core.get_pos_after_time(arg_56_0, p:dist(player.pos) / (1050 + player.moveSpeed) + p:dist(arg_56_0.pos) / (player.moveSpeed + 1050)):to3D(arg_56_0.pos.y)

					if ove_0_61(slot_56_13) > 90 or slot_56_14:dist(p) <= 600 then
						for iter_56_3 = 0, p:dist(player.pos), 100 do
							local slot_56_15 = player.pos - iter_56_3 * (player.pos - p):norm()

							if navmesh.isWall(slot_56_15) or ove_0_25.isWallValid(slot_56_15) then
								if slot_56_6 > slot_56_14:dist(slot_56_15) and slot_56_6 >= arg_56_0.pos:dist(slot_56_15) and (ove_0_37.turret:get() or not slot_56_9 or slot_56_9.pos:dist(slot_56_15) >= 930) and (ove_0_37.combo.eaa:get() == false or player.pos:dist(arg_56_0.pos) >= player.boundingRadius + arg_56_0.boundingRadius + player.attackRange or slot_56_15:dist(arg_56_0.pos) <= 600) then
									slot_56_7[#slot_56_7 + 1] = slot_56_15
								end

								break
							end
						end
					end
				end
			end
		end
	end

	ove_0_52(slot_56_7, function(arg_57_0, arg_57_1)
		return arg_57_0:dist(player.pos) < arg_57_1:dist(player.pos)
	end)

	ove_0_71 = game.time + 0.2

	return #slot_56_7 > 0 and slot_56_7[1] or nil
end

local function ove_0_152()
	if ove_0_37.combo.qcombo:get() and player:spellSlot(0).state == 0 and not player.buff.camillewconeslashcharge then
		local slot_58_0 = ove_0_140()

		if ove_0_25.IsValidTarget(slot_58_0) then
			if ove_0_37.combo.qusage2:get() == 2 and player:spellSlot(0).name == "CamilleQ2" and (ove_0_37.combo.waitq2:get() == false or not player.buff.camilleqprimingstart) then
				player:castSpell("self", 0)
			end

			if ove_0_37.combo.qusage1:get() == 2 and player:spellSlot(0).name == "CamilleQ" then
				player:castSpell("self", 0)
			end

			if ove_0_37.combo.qforce:get() and slot_58_0.pos:dist(player.pos) >= player.boundingRadius + slot_58_0.boundingRadius + player.attackRange and not player.path.isDashing and (ove_0_37.combo.waitq2:get() == false or not player.buff.camilleqprimingstart) then
				player:castSpell("self", 0)
			end

			if ove_0_97(slot_58_0) + ove_0_95(slot_58_0) > slot_58_0.health then
				player:castSpell("self", 0)
			end
		end
	end

	if player:spellSlot(3).state == 0 then
		local slot_58_1 = ove_0_148()

		if ove_0_37.combo.rselect:get() then
			if ove_0_70 and ove_0_70.type == TYPE_HERO then
				slot_58_1 = ove_0_70
			else
				slot_58_1 = nil
			end
		end

		if ove_0_25.IsValidTarget(slot_58_1) and ove_0_25.GetHPPercent(slot_58_1) > ove_0_37.combo.waster:get() then
			if ove_0_37.combo.rusage:get() == 1 and ove_0_25.GetHPPercent(slot_58_1) <= ove_0_37.combo.hpr:get() then
				player:castSpell("obj", 3, slot_58_1)
			end

			if ove_0_37.combo.rusage:get() == 2 and slot_58_1.health <= ove_0_97(slot_58_1) + ove_0_101(slot_58_1) + ove_0_99(slot_58_1) + ove_0_25.CalculateAADamage(slot_58_1, player, ove_0_37.combo.includeaa:get()) + ove_0_95(slot_58_1) then
				player:castSpell("obj", 3, slot_58_1)
			end
		end
	end

	if player:spellSlot(2).state == 0 then
		local slot_58_2 = ove_0_137()

		if ove_0_25.IsValidTarget(slot_58_2) and (ove_0_37.turret:get() or ove_0_25.getturretenemy(slot_58_2.pos) == false) and not ove_0_25.getfountain(slot_58_2.pos) then
			local slot_58_3 = ove_0_58(ove_0_57(slot_58_2.pos2D, player.pos2D, player.pos:dist(mousePos) < 1500 and ove_0_25.VectorExtend(player.pos, mousePos, 1500):to2D() or mousePos2D))

			if (ove_0_61(slot_58_3) > 80 or slot_58_2.pos:dist(player.pos) <= 400) and ove_0_37.combo.ecombo1:get() and player:spellSlot(2).name ~= "CamilleEDash2" and ove_0_75 < game.time then
				local slot_58_4 = ove_0_151(slot_58_2)

				if slot_58_4 then
					player:castSpell("pos", 2, slot_58_4)

					ove_0_75 = game.time + 0.3
				end
			end
		end

		local slot_58_5 = ove_0_136()

		if not ove_0_25.IsValidTarget(slot_58_5) then
			slot_58_5 = ove_0_134()
		end

		if ove_0_37.combo.ecombo2:get() and ove_0_25.IsValidTarget(slot_58_5) and (ove_0_37.turret:get() or ove_0_25.getturretenemy(slot_58_5.pos) == false) and not ove_0_25.getfountain(slot_58_5.pos) and (player.buff.camilleedashtoggle or player:spellSlot(2).name == "CamilleEDash2") then
			ove_0_33.speed = player.moveSpeed + 1090

			local slot_58_6 = ove_0_22.linear.get_prediction(ove_0_33, slot_58_5)

			if slot_58_6 and (ove_0_37.turret:get() or ove_0_25.getturretenemy(vec3(slot_58_6.endPos.x, slot_58_5.y, slot_58_6.endPos.y)) == false) and not ove_0_25.getfountain(vec3(slot_58_6.endPos.x, slot_58_5.y, slot_58_6.endPos.y)) and ove_0_131(ove_0_33, slot_58_6, slot_58_5) then
				player:castSpell("pos", 2, vec3(slot_58_6.endPos.x, slot_58_5.pos.y, slot_58_6.endPos.y))
			end
		end
	end

	if ove_0_37.combo.wcombo:get() and player:spellSlot(1).state == 0 then
		local slot_58_7 = ove_0_144()

		if ove_0_25.IsValidTarget(slot_58_7) and ove_0_37.combo.we:get() and ove_0_76 and player.pos2D:dist(ove_0_76:to2D()) <= 400 and ove_0_76:dist(slot_58_7.pos) > 400 then
			local slot_58_8 = ove_0_22.linear.get_prediction(ove_0_32, slot_58_7)

			if slot_58_8 then
				player:castSpell("pos", 1, ove_0_25.VectorExtend(ove_0_76, vec3(slot_58_8.endPos.x, slot_58_7.pos.y, slot_58_8.endPos.y), ove_0_32.range))
			end
		end

		if ove_0_75 > game.time or ove_0_73 or ove_0_37.combo.we:get() == false and player.path.isDashing then
			return
		end

		local slot_58_9 = ove_0_146()

		if ove_0_25.IsValidTarget(slot_58_9) and ove_0_89 < game.time then
			local slot_58_10 = slot_58_9.boundingRadius + player.attackRange + player.boundingRadius + 50

			if player.buff.camilleq or player.buff.camilleq2 then
				slot_58_10 = player.attackRange + player.boundingRadius + slot_58_9.boundingRadius
			end

			if slot_58_10 >= slot_58_9.pos:dist(player.pos) and (player:spellSlot(0).state == 0 or player.buff.camilleq or player.buff.camilleq2) then
				return
			end

			local slot_58_11 = ove_0_22.linear.get_prediction(ove_0_32, slot_58_9)

			if not slot_58_11 or not (player.pos:dist(vec3(slot_58_11.endPos.x, slot_58_9.y, slot_58_11.endPos.y)) < ove_0_32.range) or ove_0_76 and ove_0_77 > game.time then
				-- block empty
			elseif (ove_0_37.combo.whalf:get() == false or slot_58_9.pos:dist(player.pos) > 330) and not player.buff.camilleedash1 and (slot_58_9.pos:dist(player.pos) > player.attackRange + player.boundingRadius + slot_58_9.boundingRadius or slot_58_9.health > ove_0_25.CalculateAADamage(slot_58_9, player, 1)) then
				player:castSpell("pos", 1, vec3(slot_58_11.endPos.x, slot_58_9.pos.y, slot_58_11.endPos.y))
			end
		end
	end
end

local function ove_0_153()
	if ove_0_37.harass.qcombo:get() and player:spellSlot(0).state == 0 and not player.buff.camillewconeslashcharge then
		local slot_59_0 = ove_0_140()

		if ove_0_25.IsValidTarget(slot_59_0) then
			if ove_0_37.harass.qusage2:get() == 2 and player:spellSlot(0).name == "CamilleQ2" and (ove_0_37.harass.waitq2:get() == false or not player.buff.camilleqprimingstart) then
				player:castSpell("self", 0)
			end

			if ove_0_37.harass.qusage1:get() == 2 and player:spellSlot(0).name == "CamilleQ" then
				player:castSpell("self", 0)
			end

			if ove_0_37.harass.qforce:get() and slot_59_0.pos:dist(player.pos) >= player.boundingRadius + slot_59_0.boundingRadius + player.attackRange and not player.path.isDashing and (ove_0_37.harass.waitq2:get() == false or not player.buff.camilleqprimingstart) then
				player:castSpell("self", 0)
			end

			if ove_0_97(slot_59_0) + ove_0_95(slot_59_0) > slot_59_0.health then
				player:castSpell("self", 0)
			end
		end
	end

	if ove_0_37.harass.qgapLogic:get() and not player.buff.camillewconeslashcharge then
		local slot_59_1 = ove_0_142()
		local slot_59_2 = ove_0_140()

		if ove_0_25.IsValidTarget(slot_59_1) and not ove_0_25.IsValidTarget(slot_59_2) and not ove_0_24.core.is_winding_up_attack() and ove_0_88 < game.time then
			local slot_59_3 = ove_0_102()

			if slot_59_3 then
				if player:spellSlot(0).state == 0 then
					player:castSpell("self", 0)
				end

				if player.buff.camilleq then
					player:attack(slot_59_3)
					ove_0_24.core.set_server_pause()

					ove_0_88 = game.time + 2
				end
			end
		end
	end

	if player:spellSlot(2).state == 0 then
		local slot_59_4 = ove_0_137()

		if ove_0_25.IsValidTarget(slot_59_4) and (ove_0_37.turret:get() or ove_0_25.getturretenemy(slot_59_4.pos) == false) and not ove_0_25.getfountain(slot_59_4.pos) then
			local slot_59_5 = ove_0_58(ove_0_57(slot_59_4.pos2D, player.pos2D, player.pos:dist(mousePos) < 1500 and ove_0_25.VectorExtend(player.pos, mousePos, 1500):to2D() or mousePos2D))

			if (ove_0_61(slot_59_5) > 80 or slot_59_4.pos:dist(player.pos) <= 400) and ove_0_37.harass.ecombo1:get() and player:spellSlot(2).name ~= "CamilleEDash2" and ove_0_75 < game.time then
				local slot_59_6 = ove_0_151(slot_59_4)

				if slot_59_6 then
					player:castSpell("pos", 2, slot_59_6)

					ove_0_75 = game.time + 0.3
				end
			end
		end

		local slot_59_7 = ove_0_136()

		if not ove_0_25.IsValidTarget(slot_59_7) then
			slot_59_7 = ove_0_134()
		end

		if ove_0_37.harass.ecombo2:get() and ove_0_25.IsValidTarget(slot_59_7) and (ove_0_37.turret:get() or ove_0_25.getturretenemy(slot_59_7.pos) == false) and not ove_0_25.getfountain(slot_59_7.pos) and (player.buff.camilleedashtoggle or player:spellSlot(2).name == "CamilleEDash2") then
			ove_0_33.speed = player.moveSpeed + 1090

			local slot_59_8 = ove_0_22.linear.get_prediction(ove_0_33, slot_59_7)

			if slot_59_8 and (ove_0_37.turret:get() or ove_0_25.getturretenemy(vec3(slot_59_8.endPos.x, slot_59_7.y, slot_59_8.endPos.y)) == false) and not ove_0_25.getfountain(vec3(slot_59_8.endPos.x, slot_59_7.y, slot_59_8.endPos.y)) and ove_0_131(ove_0_33, slot_59_8, slot_59_7) then
				player:castSpell("pos", 2, vec3(slot_59_8.endPos.x, slot_59_7.pos.y, slot_59_8.endPos.y))
			end
		end
	end

	if ove_0_37.harass.wcombo:get() and player:spellSlot(1).state == 0 then
		local slot_59_9 = ove_0_144()

		if ove_0_25.IsValidTarget(slot_59_9) and ove_0_37.harass.we:get() and ove_0_76 and player.pos2D:dist(ove_0_76:to2D()) <= 400 and ove_0_76:dist(slot_59_9.pos) > 400 then
			local slot_59_10 = ove_0_22.linear.get_prediction(ove_0_32, slot_59_9)

			if slot_59_10 then
				player:castSpell("pos", 1, ove_0_25.VectorExtend(ove_0_76, vec3(slot_59_10.endPos.x, slot_59_9.pos.y, slot_59_10.endPos.y), ove_0_32.range))
			end
		end

		if ove_0_75 > game.time or ove_0_73 or ove_0_37.harass.we:get() == false and player.path.isDashing then
			return
		end

		local slot_59_11 = ove_0_146()

		if ove_0_25.IsValidTarget(slot_59_11) and ove_0_89 < game.time then
			local slot_59_12 = slot_59_11.boundingRadius + player.attackRange + player.boundingRadius + 50

			if player.buff.camilleq or player.buff.camilleq2 then
				slot_59_12 = player.attackRange + player.boundingRadius + slot_59_11.boundingRadius
			end

			if slot_59_12 >= slot_59_11.pos:dist(player.pos) and (player:spellSlot(0).state == 0 or player.buff.camilleq or player.buff.camilleq2) then
				return
			end

			local slot_59_13 = ove_0_22.linear.get_prediction(ove_0_32, slot_59_11)

			if not slot_59_13 or not (player.pos:dist(vec3(slot_59_13.endPos.x, slot_59_11.y, slot_59_13.endPos.y)) < ove_0_32.range) or ove_0_76 and ove_0_77 > game.time then
				-- block empty
			elseif (ove_0_37.harass.whalf:get() == false or slot_59_11.pos:dist(player.pos) > 330) and not player.buff.camilleedash1 and (slot_59_11.pos:dist(player.pos) > player.attackRange + player.boundingRadius + slot_59_11.boundingRadius or slot_59_11.health > ove_0_25.CalculateAADamage(slot_59_11, player, 1)) then
				player:castSpell("pos", 1, vec3(slot_59_13.endPos.x, slot_59_11.pos.y, slot_59_13.endPos.y))
			end
		end
	end
end

local function ove_0_154()
	for iter_60_0 = 0, objManager.enemies_n - 1 do
		local slot_60_0 = objManager.enemies[iter_60_0]

		if slot_60_0 then
			if not ove_0_25.RecallActive(player) and ove_0_37.auto.autowkill:get() and ove_0_25.IsValidTarget(slot_60_0, false, true, true) and slot_60_0.health <= ove_0_99(slot_60_0) and slot_60_0.pos:dist(player.pos) <= ove_0_32.range and (slot_60_0.pos:dist(player.pos) > player.attackRange + player.boundingRadius + slot_60_0.boundingRadius or slot_60_0.health > ove_0_25.CalculateAADamage(slot_60_0, player, 1)) then
				local slot_60_1 = ove_0_22.linear.get_prediction(ove_0_32, slot_60_0)

				if slot_60_1 and player.pos:dist(vec3(slot_60_1.endPos.x, slot_60_0.y, slot_60_1.endPos.y)) < ove_0_32.range then
					player:castSpell("pos", 1, vec3(slot_60_1.endPos.x, slot_60_0.pos.y, slot_60_1.endPos.y))
				end
			end

			if not slot_60_0.buff[BUFF_SLOW] then
				if ove_0_46[slot_60_0.networkID] and slot_60_0.moveSpeed / ove_0_46[slot_60_0.networkID].speed <= 0.8 and (ove_0_46[slot_60_0.networkID].lastCheck > game.time or ove_0_46[slot_60_0.networkID].lastBuffTime > game.time) then
					return
				end

				ove_0_46[slot_60_0.networkID] = {
					lastBuffTime = 0,
					speed = slot_60_0.moveSpeed,
					lastCheck = game.time + 0.2
				}
			elseif ove_0_46[slot_60_0.networkID] then
				ove_0_46[slot_60_0.networkID].lastBuffTime = game.time + 0.35
			end
		end
	end
end

local function ove_0_155(arg_61_0)
	if player.buff.camilleeonwall or player:spellSlot(2).name == "CamilleEDash2" or ove_0_75 > game.time or player.path.isDashing then
		return nil
	end

	if ove_0_78 then
		local slot_61_0 = player.path.serverPos + ove_0_32.range / player.path.serverPos:dist(ove_0_78) * (ove_0_78 - player.path.serverPos)

		if slot_61_0 and arg_61_0 then
			local slot_61_1 = slot_61_0 + (arg_61_0.path.serverPos - slot_61_0):norm() * (arg_61_0.path.serverPos:dist(slot_61_0) + 300 + arg_61_0.boundingRadius)

			if slot_61_1 and not navmesh.isWall(vec3(slot_61_1.x, slot_61_1.y, slot_61_1.z)) then
				return slot_61_1
			else
				return nil
			end
		else
			return nil
		end
	end

	return nil
end

local function ove_0_156()
	if ove_0_68 > game.time then
		return
	end

	ove_0_92 = -1
	ove_0_91 = -1
	ove_0_93 = -1

	for iter_62_0 = 0, 5 do
		if player:itemID(iter_62_0) == 3078 then
			ove_0_92 = iter_62_0
		end

		if player:itemID(iter_62_0) == 3057 then
			ove_0_91 = iter_62_0
		end

		if player:itemID(iter_62_0) == 6632 then
			ove_0_93 = iter_62_0
		end
	end

	ove_0_68 = game.time + 0.1
end

local function ove_0_157()
	if ove_0_37.semir:get() and player:spellSlot(3).state == 0 then
		local slot_63_0 = ove_0_148()

		if ove_0_37.semirprio:get() == 2 then
			local slot_63_1
			local slot_63_2 = 99999

			for iter_63_0 = 0, objManager.enemies_n - 1 do
				local slot_63_3 = objManager.enemies[iter_63_0]

				if ove_0_25.IsValidTarget(slot_63_3, false, true, true, false) and slot_63_3.pos:dist(player.pos) <= ove_0_34.range and slot_63_2 > slot_63_3.pos:dist(mousePos) then
					slot_63_2 = slot_63_3.pos:dist(mousePos)
					slot_63_1 = slot_63_3
				end
			end

			if slot_63_1 then
				player:castSpell("obj", 3, slot_63_1)
			end
		end

		if ove_0_37.semirprio:get() == 3 then
			local slot_63_4
			local slot_63_5 = 99999

			for iter_63_1 = 0, objManager.enemies_n - 1 do
				local slot_63_6 = objManager.enemies[iter_63_1]

				if ove_0_25.IsValidTarget(slot_63_6, false, true, true, false) and slot_63_6.pos:dist(player.pos) <= ove_0_34.range and slot_63_5 > slot_63_6.pos:dist(player.pos) then
					slot_63_5 = slot_63_6.pos:dist(player.pos)
					slot_63_4 = slot_63_6
				end
			end

			if slot_63_4 then
				player:castSpell("obj", 3, slot_63_4)
			end
		end

		if ove_0_25.IsValidTarget(slot_63_0) then
			player:castSpell("obj", 3, slot_63_0)
		end
	end
end

local ove_0_158 = 0

local function ove_0_159(arg_64_0, arg_64_1, arg_64_2)
	if ove_0_158 > game.time then
		return false
	end

	ove_0_158 = game.time + 0.1

	for iter_64_0 = 0, objManager.minions.size[TEAM_ALLY] - 1 do
		local slot_64_0 = objManager.minions[TEAM_ALLY][iter_64_0]

		if slot_64_0 and slot_64_0.buff[arg_64_1] and slot_64_0.buff[arg_64_1].endTime - game.time < 0.1 + network.latency and arg_64_2 >= slot_64_0.pos:dist(arg_64_0.pos) then
			return true
		end
	end

	for iter_64_1 = 0, objManager.allies_n - 1 do
		local slot_64_1 = objManager.allies[iter_64_1]

		if slot_64_1 and slot_64_1.buff[arg_64_1] and slot_64_1.buff[arg_64_1].endTime - game.time < 0.1 + network.latency and arg_64_2 >= slot_64_1.pos:dist(arg_64_0.pos) then
			return true
		end
	end

	return false
end

local function ove_0_160()
	return ove_0_148()
end

local function ove_0_161(arg_66_0)
	if ove_0_159(arg_66_0, "sennaw", 300) and ove_0_37.SpellsMenu.Skillshots.Senna and ove_0_37.SpellsMenu.Skillshots.Senna[1].Dodge:get() then
		return true
	end

	if ove_0_159(arg_66_0, "zileanqenemybomb", 350) and ove_0_37.SpellsMenu.Skillshots.Zilean and ove_0_37.SpellsMenu.Skillshots.Zilean[0].Dodge:get() then
		return true
	end

	return false
end

local function ove_0_162()
	local slot_67_0 = ove_0_160()

	if ove_0_37.SpellsMenu.enable:get() and player:spellSlot(3).state == 0 and slot_67_0 then
		if ove_0_161(player) then
			player:castSpell("obj", 3, slot_67_0)
		end

		if ove_0_85 and ove_0_85.buff.rakanr and ove_0_85.pos:dist(player.pos) <= 200 and ove_0_37.SpellsMenu.Targeted.Rakan3 and ove_0_37.SpellsMenu.Targeted.Rakan3.Dodge:get() then
			player:castSpell("obj", 3, slot_67_0)
		end

		if ove_0_84 and ove_0_84.buff.kennenshurikenstorm and ove_0_84.pos:dist(player.pos) <= 550 and ove_0_37.SpellsMenu.Targeted.Kennen3 and ove_0_37.SpellsMenu.Targeted.Kennen3.Dodge:get() and (ove_0_37.SpellsMenu.Targeted.Kennen3.Stun:get() == false or player.buff.kennenmarkofstorm and player.buff.kennenmarkofstorm.stacks == 2) then
			player:castSpell("obj", 3, slot_67_0)
		end

		if ove_0_80 and ove_0_37.SpellsMenu.Skillshots.Jax and ove_0_37.SpellsMenu.Skillshots.Jax[2].Dodge:get() and ove_0_80.buff.jaxcounterstrike and ove_0_80.buff.jaxcounterstrike.endTime - game.time < 0.67 and ove_0_80.pos:dist(player.pos) <= 350 then
			player:castSpell("obj", 3, slot_67_0)
		end

		if ove_0_82 and ove_0_37.SpellsMenu.Skillshots.Warwick and ove_0_37.SpellsMenu.Skillshots.Warwick[2].Dodge:get() and ove_0_82.buff.warwicke and ove_0_82.buff.warwicke.endTime - game.time < 0.67 and ove_0_82.pos:dist(player.pos) <= 350 then
			player:castSpell("obj", 3, slot_67_0)
		end

		if ove_0_83 and ove_0_37.SpellsMenu.Skillshots.Galio and ove_0_37.SpellsMenu.Skillshots.Galio[1].Dodge:get() and ove_0_83.buff.galiow and ove_0_83.buff.galiow.endTime - game.time < 0.67 and ove_0_83.pos:dist(player.pos) <= 350 then
			player:castSpell("obj", 3, slot_67_0)
		end

		if ove_0_86 and ove_0_37.SpellsMenu.Skillshots.Neeko and ove_0_37.SpellsMenu.Skillshots.Neeko[3].Dodge:get() and ove_0_86.buff.neekor and ove_0_86.buff.neekor.endTime - game.time < 0.1 + network.latency and ove_0_86.pos:dist(player.pos) <= 600 then
			player:castSpell("obj", 3, slot_67_0)
		end

		if ove_0_37.SpellsMenu.Targeted.Nocturne2 and ove_0_37.SpellsMenu.Targeted.Nocturne2.Dodge:get() and player.buff.nocturneunspeakablehorror and player.buff.nocturneunspeakablehorror.endTime - game.time - 1 < 0.3 then
			player:castSpell("obj", 3, slot_67_0)
		end

		if ove_0_37.SpellsMenu.Skillshots.Kled and ove_0_37.SpellsMenu.Skillshots.Kled[0].Dodge:get() and player.buff.kledqmark and player.buff.kledqmark.endTime - game.time < 0.1 + network.latency then
			player:castSpell("obj", 3, slot_67_0)
		end

		if ove_0_37.SpellsMenu.Skillshots.Aatrox and ove_0_37.SpellsMenu.Skillshots.Aatrox[1].Dodge:get() and player.buff.aatroxwchains and player.buff.aatroxwchains.endTime - game.time < 0.1 + network.latency then
			player:castSpell("obj", 3, slot_67_0)
		end

		if ove_0_37.SpellsMenu.Skillshots.Leblanc and ove_0_37.SpellsMenu.Skillshots.Leblanc[2].Dodge:get() and player.buff.leblance and player.buff.leblance.endTime - game.time < 0.1 + network.latency then
			player:castSpell("obj", 3, slot_67_0)
		end

		if ove_0_37.SpellsMenu.Targeted.Morgana and ove_0_37.SpellsMenu.Targeted.Morgana3.Dodge:get() and (not ove_0_37.SpellsMenu.Targeted.Morgana3.Combat:get() or ove_0_24.menu.combat.key:get()) and player.buff.morganardebuff and player.buff.morganardebuff.endTime - game.time < 0.1 + network.latency then
			player:castSpell("obj", 3, slot_67_0)
		end

		if ove_0_37.SpellsMenu.Targeted.Karma1 and ove_0_37.SpellsMenu.Targeted.Karma1.Dodge:get() and (not ove_0_37.SpellsMenu.Targeted.Karma1.Combat:get() or ove_0_24.menu.combat.key:get()) and player.buff.karmaspiritbind and player.buff.karmaspiritbind.endTime - game.time < 0.1 + network.latency then
			player:castSpell("obj", 3, slot_67_0)
		end

		if ove_0_37.SpellsMenu.Targeted.Karthus3 and ove_0_37.SpellsMenu.Targeted.Karthus3.Dodge:get() and (not ove_0_37.SpellsMenu.Targeted.Karthus3.Combat:get() or ove_0_24.menu.combat.key:get()) and player.buff.karthusfallenonetarget and player.buff.karthusfallenonetarget.endTime - game.time < 0.1 + network.latency then
			player:castSpell("obj", 3, slot_67_0)
		end

		if ove_0_37.SpellsMenu.Targeted.Vi3 and ove_0_37.SpellsMenu.Targeted.Vi3.Dodge:get() and (not ove_0_37.SpellsMenu.Targeted.Vi3.Combat:get() or ove_0_24.menu.combat.key:get()) and ove_0_87 and ove_0_87.buff.vir and ove_0_87.pos2D:dist(player.pos2D) <= 500 then
			player:castSpell("obj", 3, slot_67_0)
		end

		if ove_0_37.SpellsMenu.Targeted.Nautilus3 and ove_0_37.SpellsMenu.Targeted.Nautilus3.Dodge:get() and (not ove_0_37.SpellsMenu.Targeted.Nautilus3.Combat:get() or ove_0_24.menu.combat.key:get()) then
			local slot_67_1 = ove_0_25.GetNautilusRMissile()

			if player.buff.nautilusgrandlinetarget and slot_67_1 and slot_67_1.pos2D:dist(player.pos2D) <= 400 then
				player:castSpell("obj", 3, slot_67_0)
			end
		end

		if ove_0_37.SpellsMenu.Targeted.Caitlyn3 and ove_0_37.SpellsMenu.Targeted.Caitlyn3.Dodge:get() and (not ove_0_37.SpellsMenu.Targeted.Caitlyn3.Combat:get() or ove_0_24.menu.combat.key:get()) then
			local slot_67_2 = ove_0_25.GetCaitlynRMissile()

			if slot_67_2 and slot_67_2.spell.target and slot_67_2.spell.target == obj and slot_67_2.pos2D:dist(obj.pos2D) <= 700 then
				player:castSpell("obj", 3, slot_67_0)
			end
		end

		if ove_0_37.SpellsMenu.Targeted.Zed3 and ove_0_37.SpellsMenu.Targeted.Zed3.Dodge:get() and (not ove_0_37.SpellsMenu.Targeted.Zed3.Combat:get() or ove_0_24.menu.combat.key:get()) and player.buff.zedrtargetmark and player.buff.zedrtargetmark.endTime - game.time < 0.1 + network.latency then
			player:castSpell("obj", 3, slot_67_0)
		end

		if ove_0_37.SpellsMenu.Skillshots.Vladimir and ove_0_37.SpellsMenu.Skillshots.Vladimir[3].Dodge:get() and player.buff.vladimirhemoplaguedebuff and player.buff.vladimirhemoplaguedebuff.endTime - game.time < 0.1 + network.latency then
			player:castSpell("obj", 3, slot_67_0)
		end

		if player.buff.drowsy and player.buff.drowsy.endTime - game.time < 0.1 + network.latency then
			player:castSpell("obj", 3, slot_67_0)
		end

		if ove_0_37.SpellsMenu.Skillshots.Fizz and ove_0_37.SpellsMenu.Skillshots.Fizz[3].Dodge:get() and player.buff.fizzrbomb and player.buff.fizzrbomb.endTime - game.time < 0.1 + network.latency then
			player:castSpell("obj", 3, slot_67_0)
		end

		if ove_0_37.SpellsMenu.Skillshots.Zoe and ove_0_37.SpellsMenu.Skillshots.Zoe[2].Dodge:get() and player.buff.zoeesleepcountdown and player.buff.zoeesleepcountdown.endTime - game.time < 0.1 + network.latency then
			player:castSpell("obj", 3, slot_67_0)
		end
	end
end

local function ove_0_163()
	if ove_0_62 > game.time then
		return
	end

	if ove_0_37.SpellsMenu.Skillshots.CCOnly:get() == true then
		if ove_0_37.SpellsMenu.Skillshots.KillOnly.text:find("Only if") then
			ove_0_37.SpellsMenu.Skillshots.KillOnly:set("text", "Or if incoming spell will kill")
		end
	elseif ove_0_37.SpellsMenu.Skillshots.KillOnly.text:find("Or if") then
		ove_0_37.SpellsMenu.Skillshots.KillOnly:set("text", "Only if incoming spell will kill")
	end

	if ove_0_37.SpellsMenu.Skillshots.KillOnly:get() == true then
		if ove_0_37.SpellsMenu.Skillshots.CCOnly.text:find("Only if ") then
			ove_0_37.SpellsMenu.Skillshots.CCOnly:set("text", "Or if incoming spell is CC")
		end
	elseif ove_0_37.SpellsMenu.Skillshots.CCOnly.text:find("Or if ") then
		ove_0_37.SpellsMenu.Skillshots.CCOnly:set("text", "Only if incoming spell is CC")
	end

	if ove_0_37.SpellsMenu.Skillshots.CCOnly:get() == true and ove_0_37.SpellsMenu.Skillshots.KillOnly:get() == true then
		if ove_0_37.SpellsMenu.Skillshots.KillOnly.text:find("Only if ") then
			ove_0_37.SpellsMenu.Skillshots.KillOnly:set("text", "Or if incoming spell will kill")
		end

		if ove_0_37.SpellsMenu.Skillshots.CCOnly.text:find("Or if ") then
			ove_0_37.SpellsMenu.Skillshots.CCOnly:set("text", "Only if incoming spell is CC")
		end
	end

	if ove_0_37.SpellsMenu.Targeted.CCOnly:get() == true then
		if ove_0_37.SpellsMenu.Targeted.KillOnly.text:find("Only if") then
			ove_0_37.SpellsMenu.Targeted.KillOnly:set("text", "Or if incoming spell will kill")
		end
	elseif ove_0_37.SpellsMenu.Targeted.KillOnly.text:find("Or if") then
		ove_0_37.SpellsMenu.Targeted.KillOnly:set("text", "Only if incoming spell will kill")
	end

	if ove_0_37.SpellsMenu.Targeted.KillOnly:get() == true then
		if ove_0_37.SpellsMenu.Targeted.CCOnly.text:find("Only if ") then
			ove_0_37.SpellsMenu.Targeted.CCOnly:set("text", "Or if incoming spell is CC")
		end
	elseif ove_0_37.SpellsMenu.Targeted.CCOnly.text:find("Or if ") then
		ove_0_37.SpellsMenu.Targeted.CCOnly:set("text", "Only if incoming spell is CC")
	end

	if ove_0_37.SpellsMenu.Targeted.CCOnly:get() == true and ove_0_37.SpellsMenu.Targeted.KillOnly:get() == true then
		if ove_0_37.SpellsMenu.Targeted.KillOnly.text:find("Only if ") then
			ove_0_37.SpellsMenu.Targeted.KillOnly:set("text", "Or if incoming spell will kill")
		end

		if ove_0_37.SpellsMenu.Targeted.CCOnly.text:find("Or if ") then
			ove_0_37.SpellsMenu.Targeted.CCOnly:set("text", "Only if incoming spell is CC")
		end
	end

	ove_0_62 = game.time + 0.1
end

local function ove_0_164(arg_69_0, arg_69_1, arg_69_2)
	if arg_69_0:get() then
		if arg_69_1.damage and arg_69_1.damage[arg_69_2.ptr] and not ove_0_25.isCCType(arg_69_1.damage[arg_69_2.ptr].buff) then
			return "no"
		end
	else
		return "dis"
	end

	return "yes"
end

local function ove_0_165(arg_70_0, arg_70_1, arg_70_2)
	if arg_70_0:get() then
		if arg_70_1.damage and ove_0_25.getIncomingDamage(arg_70_2, arg_70_1, "both") < arg_70_2.health then
			return "no"
		end
	else
		return "dis"
	end

	return "yes"
end

local function ove_0_166(arg_71_0, arg_71_1, arg_71_2)
	if (arg_71_1.charName ~= "Annie" or not ove_0_37.SpellsMenu.Targeted[arg_71_1.charName .. arg_71_0].Stun:get() or arg_71_1.buff.anniepassiveprimed) and (arg_71_1.charName ~= "Vi" or arg_71_0 ~= 3 or arg_71_1.pos2D:dist(player.pos2D) <= 300) and (arg_71_1.charName ~= "Vladimir" or arg_71_0 ~= 0 or not ove_0_37.SpellsMenu.Targeted[arg_71_1.charName .. arg_71_0].Empowered:get() or arg_71_1.buff.vladimirqfrenzy) and (arg_71_1.charName ~= "Nocturne" or arg_71_0 ~= 2 or ove_0_37.SpellsMenu.Targeted[arg_71_1.charName .. arg_71_0].EndRoot:get() == false) and (arg_71_1.charName ~= "Morgana" or arg_71_0 ~= 3 or ove_0_37.SpellsMenu.Targeted[arg_71_1.charName .. arg_71_0].EndRoot:get() == false) and (arg_71_1.charName ~= "Karma" or arg_71_0 ~= 1 or ove_0_37.SpellsMenu.Targeted[arg_71_1.charName .. arg_71_0].EndRoot:get() == false) and (arg_71_1.charName ~= "Zed" or arg_71_0 ~= 3 or ove_0_37.SpellsMenu.Targeted[arg_71_1.charName .. arg_71_0].EndDamage:get() == false) and (arg_71_1.charName ~= "XinZhao" or arg_71_0 ~= 0 or ove_0_37.SpellsMenu.Targeted[arg_71_1.charName .. arg_71_0].Third:get() == false or arg_71_2.name:lower():find("xinzhaoqthrust3")) and (arg_71_1.charName ~= "Camille" or arg_71_0 ~= 0 or ove_0_37.SpellsMenu.Targeted[arg_71_1.charName .. arg_71_0].Q2:get() == false or arg_71_2.name:lower():find("camilleqattackempowered")) and arg_71_1.charName ~= "TwistedFate" and (arg_71_1.charName ~= "Evelynn" or arg_71_0 ~= 2 or ove_0_37.SpellsMenu.Targeted[arg_71_1.charName .. arg_71_0].Charm:get() == false or player.buff.evelynnw and game.time - player.buff.evelynnw.startTime >= 2.5) and (arg_71_1.charName ~= "Nautilus" or arg_71_0 ~= 3) and (arg_71_1.charName ~= "Volibear" or arg_71_0 ~= 1 or ove_0_37.SpellsMenu.Targeted[arg_71_1.charName .. arg_71_0].Wounded:get() == false or player.buff.volibearw) and (arg_71_1.charName ~= "Kennen" or arg_71_0 ~= 1 or ove_0_37.SpellsMenu.Targeted[arg_71_1.charName .. arg_71_0].Stun:get() == false or player.buff.kennenmarkofstorm and player.buff.kennenmarkofstorm.stacks == 2) and (arg_71_1.charName ~= "Ryze" or arg_71_0 ~= 1 or ove_0_37.SpellsMenu.Targeted[arg_71_1.charName .. arg_71_0].Root:get() == false or player.buff.ryzee) then
		return true
	end

	return false
end

local function ove_0_167(arg_72_0, arg_72_1, arg_72_2)
	if ove_0_164(ove_0_37.SpellsMenu.Targeted.CCOnly, arg_72_0, arg_72_1) == "no" then
		if ove_0_165(ove_0_37.SpellsMenu.Targeted.KillOnly, arg_72_0, arg_72_1) == "no" then
			return
		end
	elseif ove_0_164(ove_0_37.SpellsMenu.Targeted.CCOnly, arg_72_0, arg_72_1) == "dis" and ove_0_165(ove_0_37.SpellsMenu.Targeted.KillOnly, arg_72_0, arg_72_1) == "no" then
		return
	end

	local slot_72_0 = string.lower(arg_72_0.owner.charName)

	if ove_0_25.TargetedSpells()[slot_72_0] and ove_0_37.SpellsMenu.Targeted[arg_72_0.owner.charName .. arg_72_0.data.slot] and ove_0_37.SpellsMenu.Targeted[arg_72_0.owner.charName .. arg_72_0.data.slot].Dodge:get() and (not ove_0_37.SpellsMenu.Targeted[arg_72_0.owner.charName .. arg_72_0.data.slot].Combat:get() or ove_0_24.menu.combat.key:get()) and ove_0_37.SpellsMenu.Targeted[arg_72_0.owner.charName .. arg_72_0.data.slot].hp:get() >= ove_0_25.GetHPPercent(player) and ove_0_166(arg_72_0.data.slot, arg_72_0.owner, arg_72_0) then
		player:castSpell("obj", 3, arg_72_2)
	end
end

local function ove_0_168(arg_73_0, arg_73_1)
	if ove_0_25.InsideSpell(arg_73_0, arg_73_1) and ove_0_37.SpellsMenu.Items.Everfrost and arg_73_0.name == "6656Cast" then
		local slot_73_0 = ove_0_37.SpellsMenu.speed:get()

		if ove_0_37.SpellsMenu.Items.Everfrost.Dodge:get() and (not ove_0_37.SpellsMenu.Items.Everfrost.Combat:get() or ove_0_24.menu.combat.key:get()) and ove_0_37.SpellsMenu.Items.Everfrost.hp:get() >= ove_0_25.GetHPPercent(player) then
			if arg_73_0.missile and arg_73_1.pos:dist(arg_73_0.missile.pos) / arg_73_0.data.speed < network.latency + slot_73_0 / 100 then
				player:castSpell("obj", 3, clos)
			end

			if arg_73_0.p1 and arg_73_1.pos2D:dist(arg_73_0.p1) / arg_73_0.data.speed < 0.3 then
				player:castSpell("obj", 3, clos)
			end
		end
	end
end

local function ove_0_169(arg_74_0, arg_74_1, arg_74_2, arg_74_3)
	if arg_74_2.special_object and arg_74_2.special_object.pos and (arg_74_2.owner.charName == "Camille" and arg_74_2.data.slot == 2 or arg_74_2.owner.charName == "Vi" and arg_74_2.data.slot == 0) and arg_74_0.pos:dist(arg_74_2.special_object.pos) / arg_74_2.data.speed < network.latency + 0.3 then
		player:castSpell("obj", 3, arg_74_3)
	end

	if arg_74_2.special_object and arg_74_2.special_object.pos and arg_74_0.pos:dist(arg_74_2.special_object.pos) / arg_74_2.data.speed < network.latency + arg_74_1 / 100 then
		player:castSpell("obj", 3, arg_74_3)
	end

	if arg_74_2.name == "SettE" then
		player:castSpell("obj", 3, arg_74_3)
	end

	if arg_74_2.name == "YuumiR" then
		player:castSpell("obj", 3, arg_74_3)
	end

	if arg_74_2.missile and arg_74_0.pos:dist(arg_74_2.missile.pos) / arg_74_2.data.speed < network.latency + arg_74_1 / 100 then
		player:castSpell("obj", 3, arg_74_3)
	end

	if arg_74_2.p1 then
		if (arg_74_2.owner.charName == "Camille" and arg_74_2.data.slot == 2 or arg_74_2.owner.charName == "Vi" and arg_74_2.data.slot == 0) and arg_74_0.pos2D:dist(arg_74_2.p1) / arg_74_2.data.speed < network.latency + 0.3 then
			player:castSpell("obj", 3, arg_74_3)
		end

		if arg_74_0.pos2D:dist(arg_74_2.p1) / arg_74_2.data.speed < network.latency + arg_74_1 / 100 then
			player:castSpell("obj", 3, arg_74_3)
		end
	end

	if not arg_74_2.missile and not arg_74_2.p1 then
		if arg_74_0.pos:dist(arg_74_2.owner.pos) <= 250 and (arg_74_2.data.speed ~= math.huge and arg_74_2.data.spell_type ~= "Circular" or arg_74_2.end_time - arg_74_2.start_time < arg_74_1 / 100 + network.latency) then
			player:castSpell("obj", 3, arg_74_3)
		end

		if arg_74_2.data.speed == math.huge or arg_74_2.data.spell_type == "Circular" then
			if arg_74_2.name == "NasusE" and os.clock() - arg_74_2.start_time < 0.2 then
				player:castSpell("obj", 3, arg_74_3)
			end

			if arg_74_2.end_time - os.clock() < arg_74_1 / 100 + network.latency + 0.1 and arg_74_2.name == "SettW" then
				player:castSpell("obj", 3, arg_74_3)
			end

			if arg_74_2.end_time - os.clock() < arg_74_1 / 100 + network.latency then
				if arg_74_2.name == "JarvanIVDragonStrike" then
					player:castSpell("obj", 3, arg_74_3)
				end

				player:castSpell("obj", 3, arg_74_3)
			end
		end

		if arg_74_2.name == "SionQ" then
			player:castSpell("obj", 3, arg_74_3)
		end

		if arg_74_2.name == "CassiopeiaWMissile" then
			player:castSpell("obj", 3, arg_74_3)
		end

		if ove_0_25.ContainsTrap(arg_74_0, arg_74_2) then
			player:castSpell("obj", 3, arg_74_3)
		end
	end
end

local function ove_0_170(arg_75_0, arg_75_1, arg_75_2)
	if ove_0_25.InsideSpell(arg_75_0, arg_75_1) then
		for iter_75_0, iter_75_1 in pairs(ove_0_29) do
			if arg_75_0.owner.charName ~= "Zilean" or arg_75_1.buff.zileanqenemybomb then
				if ove_0_37.SpellsMenu.Skillshots.forcew:get() and ove_0_25.getIncomingDamage(arg_75_1, arg_75_0, "both") >= player.health then
					local slot_75_0 = ove_0_37.SpellsMenu.speed:get()

					if arg_75_0.name == "XayahEMissile" then
						slot_75_0 = ove_0_60(ove_0_37.SpellsMenu.speed:get(), 15)
					end

					if arg_75_0.name == "UFSlash" then
						slot_75_0 = ove_0_60(ove_0_37.SpellsMenu.speed:get(), 50)
					end

					ove_0_169(arg_75_1, slot_75_0, arg_75_0, arg_75_2)
				end

				if ove_0_37.SpellsMenu.Skillshots[iter_75_1.charName] then
					if ove_0_164(ove_0_37.SpellsMenu.Skillshots.CCOnly, arg_75_0, arg_75_1) == "no" then
						if ove_0_165(ove_0_37.SpellsMenu.Skillshots.KillOnly, arg_75_0, arg_75_1) == "no" then
							return
						end
					elseif ove_0_164(ove_0_37.SpellsMenu.Skillshots.CCOnly, arg_75_0, arg_75_1) == "dis" and ove_0_165(ove_0_37.SpellsMenu.Skillshots.KillOnly, arg_75_0, arg_75_1) == "no" then
						return
					end

					if ove_0_37.SpellsMenu.ignorecircle:get() == false or arg_75_0.data.spell_type ~= "Circular" then
						local slot_75_1 = ove_0_37.SpellsMenu.speed:get()

						if arg_75_0.name == "XayahEMissile" then
							slot_75_1 = ove_0_60(ove_0_37.SpellsMenu.speed:get(), 15)
						end

						if arg_75_0.name == "UFSlash" then
							slot_75_1 = ove_0_60(ove_0_37.SpellsMenu.speed:get(), 50)
						end

						if iter_75_1.charName == arg_75_0.owner.charName and arg_75_0.data.slot == iter_75_1.slot and ove_0_37.SpellsMenu.Skillshots[iter_75_1.charName][iter_75_1.slot].Dodge:get() and (not ove_0_37.SpellsMenu.Skillshots[iter_75_1.charName][iter_75_1.slot].Combat:get() or ove_0_24.menu.combat.key:get()) and ove_0_37.SpellsMenu.Skillshots[iter_75_1.charName][iter_75_1.slot].hp:get() >= ove_0_25.GetHPPercent(player) and (iter_75_1.charName ~= "Annie" or not ove_0_37.SpellsMenu.Skillshots[iter_75_1.charName][iter_75_1.slot].Stun:get() or arg_75_0.owner.buff.anniepassiveprimed) and (iter_75_1.charName ~= "Yasuo" or arg_75_0.data.slot ~= 0 or not ove_0_37.SpellsMenu.Skillshots[iter_75_1.charName][iter_75_1.slot].Knock:get() or string.lower(arg_75_0.name):find("yasuoq3") or string.lower(arg_75_0.name):find("yasuoqe3")) and (iter_75_1.charName ~= "Ornn" or arg_75_0.data.slot ~= 3 or not ove_0_37.SpellsMenu.Skillshots[iter_75_1.charName][iter_75_1.slot].Back:get() or arg_75_0.name == "OrnnRWave2") and (iter_75_1.charName ~= "Yone" or arg_75_0.data.slot ~= 0 or not ove_0_37.SpellsMenu.Skillshots[iter_75_1.charName][iter_75_1.slot].Knock:get() or string.lower(arg_75_0.name):find("yoneq3")) and (iter_75_1.charName ~= "Brand" or arg_75_0.data.slot ~= 0 or not ove_0_37.SpellsMenu.Skillshots[iter_75_1.charName][iter_75_1.slot].Stun:get() or player.buff.brandablaze) and (iter_75_1.charName ~= "Vex" or arg_75_0.data.slot ~= 0 and arg_75_0.data.slot ~= 1 and arg_75_0.data.slot ~= 2 or not ove_0_37.SpellsMenu.Skillshots[iter_75_1.charName][iter_75_1.slot].Fear:get() or arg_75_0.owner.buff.brandablaze) and (iter_75_1.charName ~= "Zilean" or arg_75_0.data.slot ~= 0 or not ove_0_37.SpellsMenu.Skillshots[iter_75_1.charName][iter_75_1.slot].Stun:get() or player.buff.zileanqenemybomb) and (iter_75_1.charName ~= "Rengar" or arg_75_0.data.slot ~= 2 or not ove_0_37.SpellsMenu.Skillshots[iter_75_1.charName][iter_75_1.slot].Empowered:get() or string.lower(arg_75_0.name):find("rengareemp")) and (iter_75_1.charName ~= "Sion" or arg_75_0.data.slot ~= 0 or not ove_0_37.SpellsMenu.Skillshots[iter_75_1.charName][iter_75_1.slot].Knockup:get() or arg_75_0.owner.buff.sionq and game.time - arg_75_0.owner.buff.sionq.startTime >= (ove_0_37.SpellsMenu.Skillshots[iter_75_1.charName][iter_75_1.slot].When:get() == 1 and 0.85 or 1.25)) and (iter_75_1.charName ~= "Syndra" or arg_75_0.data.slot ~= 2 or not ove_0_37.SpellsMenu.Skillshots[iter_75_1.charName][iter_75_1.slot].StunEQ:get() or string.lower(arg_75_0.name):find("syndraespheremissile")) and (iter_75_1.charName ~= "Qiyana" or arg_75_0.data.slot ~= 0 or not ove_0_37.SpellsMenu.Skillshots[iter_75_1.charName][iter_75_1.slot].Root:get() or string.lower(arg_75_0.name):find("qiyanaq_water")) and (iter_75_1.charName ~= "Fizz" or arg_75_0.data.slot ~= 3 or not ove_0_37.SpellsMenu.Skillshots[iter_75_1.charName][iter_75_1.slot].Knockup:get()) and (iter_75_1.charName ~= "Leblanc" or arg_75_0.data.slot ~= 0 or not ove_0_37.SpellsMenu.Skillshots[iter_75_1.charName][iter_75_1.slot].RQ:get() or string.lower(arg_75_0.name):find("leblancrq")) and (iter_75_1.charName ~= "Leblanc" or arg_75_0.data.slot ~= 1 or not ove_0_37.SpellsMenu.Skillshots[iter_75_1.charName][iter_75_1.slot].RW:get() or string.lower(arg_75_0.name):find("leblancrw")) and (iter_75_1.charName ~= "Leblanc" or arg_75_0.data.slot ~= 2 or not ove_0_37.SpellsMenu.Skillshots[iter_75_1.charName][iter_75_1.slot].RE:get() or string.lower(arg_75_0.name):find("leblancre")) and (iter_75_1.charName ~= "Evelynn" or arg_75_0.data.slot ~= 0 or not ove_0_37.SpellsMenu.Skillshots[iter_75_1.charName][iter_75_1.slot].Charm:get() or player.buff.evelynnw and game.time - player.buff.evelynnw.startTime >= 2.5) and (iter_75_1.charName ~= "Leblanc" or arg_75_0.data.slot ~= 2 or not ove_0_37.SpellsMenu.Skillshots[iter_75_1.charName][iter_75_1.slot].EndRoot:get()) and (iter_75_1.charName ~= "Aatrox" or arg_75_0.data.slot ~= 1 or not ove_0_37.SpellsMenu.Skillshots[iter_75_1.charName][iter_75_1.slot].EndRoot:get()) and (iter_75_1.charName ~= "Kled" or arg_75_0.data.slot ~= 0 or not ove_0_37.SpellsMenu.Skillshots[iter_75_1.charName][iter_75_1.slot].EndRoot:get() or arg_75_0.name == "KledRiderQ") and (iter_75_1.charName ~= "Kennen" or arg_75_0.data.slot ~= 0 or not ove_0_37.SpellsMenu.Skillshots[iter_75_1.charName][iter_75_1.slot].Stun:get() or player.buff.kennenmarkofstorm and player.buff.kennenmarkofstorm.stacks == 2) and (iter_75_1.charName ~= "Jhin" or arg_75_0.data.slot ~= 1 or not ove_0_37.SpellsMenu.Skillshots[iter_75_1.charName][iter_75_1.slot].Root:get() or player.buff.jhinespotteddebuff) and (iter_75_1.charName ~= "Aphelios" or arg_75_0.data.slot ~= 0 or not ove_0_37.SpellsMenu.Skillshots[iter_75_1.charName][iter_75_1.slot].Gravitum:get()) and (iter_75_1.charName ~= "Senna" or arg_75_0.data.slot ~= 1 or not ove_0_37.SpellsMenu.Skillshots[iter_75_1.charName][iter_75_1.slot].Root:get()) and (iter_75_1.charName ~= "Zoe" or arg_75_0.data.slot ~= 2 or not ove_0_37.SpellsMenu.Skillshots[iter_75_1.charName][iter_75_1.slot].Asleep:get()) and (iter_75_1.charName ~= "Pyke" or arg_75_0.data.slot ~= 0 or not ove_0_37.SpellsMenu.Skillshots[iter_75_1.charName][iter_75_1.slot].Charged:get() or arg_75_0.name:lower() == "pykeqrange") and (iter_75_1.charName ~= "Yuumi" or arg_75_0.data.slot ~= 3 or not ove_0_37.SpellsMenu.Skillshots[iter_75_1.charName][iter_75_1.slot].Root:get() or player.buff.yuumirmissile and player.buff.yuumirmissile.stacks2 == 2) and (iter_75_1.charName ~= "Veigar" or arg_75_0.data.slot ~= 2) and (iter_75_1.charName ~= "TahmKench" or arg_75_0.data.slot ~= 0 or not ove_0_37.SpellsMenu.Skillshots[iter_75_1.charName][iter_75_1.slot].Stun:get() or player.buff.tahmkenchpdebuffcounter and player.buff.tahmkenchpdebuffcounter.stacks2 == 3) then
							if iter_75_1.charName == "Aatrox" and arg_75_0.data.slot == 0 then
								if arg_75_0.name == "AatroxQ" and not ove_0_37.SpellsMenu.Skillshots[iter_75_1.charName][iter_75_1.slot].Q1:get() then
									return
								end

								if arg_75_0.name == "AatroxQ2" and not ove_0_37.SpellsMenu.Skillshots[iter_75_1.charName][iter_75_1.slot].Q2:get() then
									return
								end

								if arg_75_0.name == "AatroxQ3" and not ove_0_37.SpellsMenu.Skillshots[iter_75_1.charName][iter_75_1.slot].Q3:get() then
									return
								end

								if ove_0_37.SpellsMenu.Skillshots[iter_75_1.charName][iter_75_1.slot].Knockup:get() then
									local slot_75_2

									if arg_75_0.name == "AatroxQ" then
										slot_75_2 = ove_0_25.VectorExtend(arg_75_0.owner.pos, arg_75_0.end_pos:to3D(player.pos.y), 625)

										if not ove_0_37.SpellsMenu.Skillshots[iter_75_1.charName][iter_75_1.slot].Q1:get() then
											return
										end
									end

									if arg_75_0.name == "AatroxQ2" then
										slot_75_2 = ove_0_25.VectorExtend(arg_75_0.owner.pos, arg_75_0.end_pos:to3D(player.pos.y), 475)
									end

									if arg_75_0.name == "AatroxQ3" then
										slot_75_2 = ove_0_25.VectorExtend(arg_75_0.owner.pos, arg_75_0.end_pos:to3D(player.pos.y), 200)
									end

									if slot_75_2 and slot_75_2:dist(player.pos) > 200 then
										return
									end
								end

								if iter_75_1.charName == "JarvanIV" and arg_75_0.data.slot == 0 and ove_0_37.SpellsMenu.Skillshots[iter_75_1.charName][iter_75_1.slot].EQ:get() and not ove_0_25.IsCollidingJarvanIV(arg_75_0.start_pos, arg_75_0.end_pos) then
									return
								end
							end

							ove_0_169(arg_75_1, slot_75_1, arg_75_0, arg_75_2)
						end
					end
				end
			end
		end
	end
end

local function ove_0_171()
	if ove_0_26 and ove_0_37.SpellsMenu.enable:get() and player:spellSlot(3).state == 0 then
		local slot_76_0 = ove_0_160()

		for iter_76_0 = 1, #ove_0_26.core.targeted do
			local slot_76_1 = ove_0_26.core.targeted[iter_76_0]

			if ove_0_37.SpellsMenu.ignorefow:get() and slot_76_1.owner and not slot_76_1.owner.isVisible then
				return
			end

			if slot_76_1.data.spell_type == "Target" and slot_76_1.target and slot_76_1.target == player and slot_76_1.owner.type == TYPE_HERO and slot_76_0 then
				if ove_0_37.SpellsMenu.Targeted.forcew:get() and ove_0_25.getIncomingDamage(slot_76_1.target, slot_76_1, "both") >= player.health and (slot_76_1.owner.charName ~= "Nautilus" or slot_76_1.data.slot ~= 3) and (slot_76_1.owner.charName ~= "Caitlyn" or slot_76_1.data.slot ~= 3) then
					player:castSpell("obj", 3, slot_76_0)
				end

				ove_0_167(slot_76_1, slot_76_1.target, slot_76_0)
			end
		end

		if ove_0_79 > game.time then
			return
		end

		for iter_76_1 = 1, #ove_0_26.core.skillshots do
			local slot_76_2 = ove_0_26.core.skillshots[iter_76_1]

			if ove_0_37.SpellsMenu.ignorefow:get() and slot_76_2.owner and not slot_76_2.owner.isVisible then
				return
			end

			if not slot_76_0 then
				return
			end

			ove_0_170(slot_76_2, player, slot_76_0)
			ove_0_168(slot_76_2, player, slot_76_0)
		end

		ove_0_79 = game.time + 0.05
	end
end

local function ove_0_172(arg_77_0)
	local slot_77_0

	for iter_77_0, iter_77_1 in pairs(ove_0_25.GetJungleMinion(arg_77_0)) do
		if iter_77_1.charName:sub(-4) ~= "Mini" and (iter_77_1.health >= ove_0_25.CalculateAADamage(iter_77_1, player, 2) or iter_77_1.maxHealth > 4000) then
			slot_77_0 = slot_77_0 or iter_77_1

			if iter_77_1.pos:dist(player.pos) <= slot_77_0.pos:dist(player.pos) then
				slot_77_0 = iter_77_1
			end
		end
	end

	return slot_77_0
end

local function ove_0_173()
	if ove_0_25.ShouldAttackJungle(ove_0_32.range) == false then
		return
	end

	if ove_0_37.farming.jungleclear.usew:get() and player:spellSlot(1).state == 0 then
		local slot_78_0, slot_78_1 = ove_0_25.GetBestLineFarm(ove_0_25.CountMinions(player.pos, ove_0_32.range, "neutral"), 200, ove_0_32.range)

		if slot_78_1 > 1 then
			player:castSpell("pos", 1, slot_78_0:to3D(player.pos.y))
		end

		local slot_78_2 = ove_0_172(ove_0_32.range)

		if slot_78_2 then
			local slot_78_3 = ove_0_22.linear.get_prediction(ove_0_32, slot_78_2)

			if slot_78_3 and player.pos:dist(vec3(slot_78_3.endPos.x, slot_78_2.y, slot_78_3.endPos.y)) < ove_0_32.range then
				player:castSpell("pos", 1, vec3(slot_78_3.endPos.x, slot_78_2.y, slot_78_3.endPos.y))
			end
		end
	end
end

local ove_0_174 = 0

local function ove_0_175()
	if ove_0_37.farming.laneclear.farmq:get() ~= 3 and player:spellSlot(0).state == 0 and not player.buff.camillewconeslashcharge then
		local slot_79_0 = ove_0_25.CountMinions(player.pos, player.boundingRadius + player.attackRange + 25, "enemy")

		for iter_79_0, iter_79_1 in pairs(slot_79_0) do
			if ove_0_37.farming.laneclear.farmq:get() == 2 and not player.buff.camilleqprimingstart or ove_0_25.isUnkillable(iter_79_1) and ove_0_24.farm.predict_hp(iter_79_1, ove_0_24.utility.get_hit_time(player, iter_79_1) - os.clock()) >= 0 and ove_0_24.farm.predict_hp(iter_79_1, ove_0_24.utility.get_hit_time(player, iter_79_1) - os.clock()) <= ove_0_97(iter_79_1) + ove_0_95(iter_79_1) and not ove_0_24.core.is_winding_up_attack() and iter_79_1 ~= ove_0_69 and ove_0_174 < game.time then
				player:castSpell("self", 0)
				player:attack(iter_79_1)
				ove_0_24.farm.set_clear_target(iter_79_1)
				ove_0_24.core.set_server_pause()

				ove_0_174 = game.time + 0.25
			end
		end
	end

	if ove_0_37.farming.laneclear.farmw:get() and player:spellSlot(1).state == 0 and ove_0_72 < game.time then
		local slot_79_1, slot_79_2 = ove_0_25.GetBestLineFarm(ove_0_25.CountMinions(player.pos, ove_0_32.range, "enemy"), 200, ove_0_32.range)

		if slot_79_2 >= ove_0_37.farming.laneclear.hitsw:get() then
			player:castSpell("pos", 1, slot_79_1:to3D(player.pos.y))
		end

		ove_0_72 = game.time + 0.2
	end
end

local function ove_0_176()
	if ove_0_37.farming.lasthit.farmq:get() and player:spellSlot(0).state == 0 then
		local slot_80_0 = ove_0_25.CountMinions(player.pos, player.boundingRadius + player.attackRange + 50, "enemy")

		for iter_80_0, iter_80_1 in pairs(slot_80_0) do
			if ove_0_25.isUnkillable(iter_80_1) and ove_0_24.farm.predict_hp(iter_80_1, ove_0_24.utility.get_hit_time(player, iter_80_1) - os.clock()) >= 0 and ove_0_24.farm.predict_hp(iter_80_1, ove_0_24.utility.get_hit_time(player, iter_80_1) - os.clock()) <= ove_0_97(iter_80_1) + ove_0_95(iter_80_1) and not ove_0_24.core.is_winding_up_attack() and iter_80_1 ~= ove_0_69 and ove_0_174 < game.time then
				player:castSpell("self", 0)
				player:attack(iter_80_1)
				ove_0_24.farm.set_clear_target(iter_80_1)
				ove_0_24.core.set_server_pause()

				ove_0_174 = game.time + 0.25
			end
		end
	end
end

local function ove_0_177()
	if not ove_0_37.flee:get() then
		return
	end

	if not ove_0_24.menu.combat.key:get() and not ove_0_24.menu.hybrid.key:get() then
		player:move(mousePos)
	end

	if ove_0_37.fleeM.fleeq:get() and not ove_0_24.core.is_winding_up_attack() and ove_0_88 < game.time then
		local slot_81_0 = ove_0_102()

		if slot_81_0 then
			if player:spellSlot(0).state == 0 then
				player:castSpell("self", 0)
			end

			if player.buff.camilleq or player.buff.camilleq2 then
				player:attack(slot_81_0)

				ove_0_88 = game.time + 2
			end
		end
	end

	if player:spellSlot(2).state == 0 and ove_0_37.fleeM.fleee:get() then
		if player:spellSlot(2).name ~= "CamilleEDash2" and ove_0_75 < game.time then
			local slot_81_1 = ove_0_150()

			if slot_81_1 then
				player:castSpell("pos", 2, slot_81_1)

				ove_0_75 = game.time + 0.3
			end
		end

		if player.buff.camilleedashtoggle or player:spellSlot(2).name == "CamilleEDash2" then
			player:castSpell("pos", 2, mousePos)

			ove_0_75 = game.time + 0.3
		end
	end

	if player:spellSlot(1).state == 0 and ove_0_37.fleeM.fleew:get() and ove_0_89 < game.time then
		if ove_0_75 > game.time or ove_0_73 or player.path.isDashing then
			return
		end

		local slot_81_2 = ove_0_146()

		if ove_0_25.IsValidTarget(slot_81_2) then
			local slot_81_3 = ove_0_22.linear.get_prediction(ove_0_32, slot_81_2)

			if slot_81_3 and player.pos:dist(vec3(slot_81_3.endPos.x, slot_81_2.y, slot_81_3.endPos.y)) < ove_0_32.range then
				if ove_0_76 and ove_0_77 > game.time then
					player:castSpell("pos", 1, ove_0_25.VectorExtend(ove_0_76, vec3(slot_81_3.endPos.x, slot_81_2.pos.y, slot_81_3.endPos.y), ove_0_32.range))
				elseif not player.buff.camilleedash1 then
					player:castSpell("pos", 1, vec3(slot_81_3.endPos.x, slot_81_2.pos.y, slot_81_3.endPos.y))
				end
			end
		end
	end
end

local function ove_0_178()
	if player.isDead then
		return
	end

	if player.path.isDashing then
		ove_0_89 = game.time + 0.25
	end

	if ove_0_37.combo.rusage:get() == 1 then
		ove_0_37.combo.hpr:set("visible", true)
	else
		ove_0_37.combo.hpr:set("visible", false)
	end

	ove_0_156()
	ove_0_157()
	ove_0_177()
	ove_0_162()
	ove_0_171()
	ove_0_163()

	if ove_0_37.draws.damage.draw_type:get() == 2 then
		ove_0_37.draws.damage.colorq:set("visible", true)
		ove_0_37.draws.damage.colorw:set("visible", true)
		ove_0_37.draws.damage.colore:set("visible", true)
		ove_0_37.draws.damage.coloraa:set("visible", true)
	else
		ove_0_37.draws.damage.colorq:set("visible", false)
		ove_0_37.draws.damage.colorw:set("visible", false)
		ove_0_37.draws.damage.colore:set("visible", false)
		ove_0_37.draws.damage.coloraa:set("visible", false)
	end

	if player.buff.camilleeonwall or player:spellSlot(2).name == "CamilleEDash2" or ove_0_75 > game.time or player.path.isDashing then
		ove_0_24.core.set_pause(0.1)
		ove_0_26.core.set_pause(0.1)
	end

	if player.buff.camillewconeslashcharge then
		if ove_0_37.wlock:get() then
			local slot_82_0 = ove_0_146()

			if ove_0_37.farming.toggle:get() and ove_0_24.menu.lane_clear.key:get() then
				slot_82_0 = ove_0_172(ove_0_32.range)
			end

			if slot_82_0 and player.pos:dist(slot_82_0.pos) <= 330 then
				local slot_82_1 = ove_0_155(slot_82_0)

				if slot_82_1 then
					player:move(slot_82_1)
					ove_0_24.combat.set_pos(slot_82_1)
				end
			end
		end

		ove_0_24.core.set_pause_attack(0.1)
	end

	if ove_0_24.menu.combat.key:get() then
		ove_0_152()
	end

	if ove_0_24.menu.hybrid.key:get() then
		ove_0_153()
	end

	if ove_0_37.farming.toggle:get() then
		if ove_0_24.menu.lane_clear.key:get() then
			ove_0_175()
			ove_0_173()
		end

		if ove_0_24.menu.last_hit.key:get() then
			ove_0_176()
		end
	end

	ove_0_74 = ove_0_37.eSearchRange:get()

	ove_0_154()
end

ove_0_25.AddPermashow("Farm", {
	"ON",
	"OFF"
}, ove_0_37.farming.toggle)
ove_0_25.AddPermashow("E Under-Turret", {
	"ON",
	"OFF"
}, ove_0_37.turret)
ove_0_25.AddPermashow("W Outside Lock", {
	"ON",
	"OFF"
}, ove_0_37.wlock)
ove_0_25.AddPermashowCustom("Flee", {
	"ON",
	"OFF"
}, ove_0_37.flee)
ove_0_25.AddPermashowCustom("Semi-R", {
	"ON",
	"OFF"
}, ove_0_37.semir)

local ove_0_179 = game.time
local ove_0_180 = 30
local ove_0_181 = true

local function ove_0_182(arg_83_0)
	ove_0_55(arg_83_0, ove_0_180, 10, ove_0_54(200, 255, 0, 0), 10)
	ove_0_55(arg_83_0, ove_0_180 - 20, 8, ove_0_54(155, 255, 123, 123), 10)
	ove_0_55(arg_83_0, ove_0_180 - 40, 4, ove_0_54(155, 255, 186, 186), 10)
end

local function ove_0_183()
	if player.isDead then
		return
	end

	if ove_0_37.combo.rselect:get() and player:spellSlot(3).state == 0 and ove_0_70 and ove_0_25.IsValidTarget(ove_0_70) then
		ove_0_182(ove_0_70.pos)
	end

	if player.isOnScreen and ove_0_37.draws.drawdebug:get() then
		local slot_84_0 = ove_0_56(player.pos)
		local slot_84_1 = 0

		for iter_84_0, iter_84_1 in pairs(ove_0_104) do
			ove_0_43(tostring(iter_84_0) .. tostring(iter_84_1), 13, slot_84_0.x, slot_84_0.y + slot_84_1 * 15, ove_0_54(122, 255, 255, 255))

			slot_84_1 = slot_84_1 + 1
		end
	end

	for iter_84_2 = 0, objManager.enemies_n - 1 do
		local slot_84_2 = objManager.enemies[iter_84_2]

		if ove_0_37.draws.damage.drawdamage:get() and ove_0_25.ValidForDraw(slot_84_2) then
			local slot_84_3 = graphics.height > 1080 and graphics.height / 1080 * 0.905 or 1
			local slot_84_4 = slot_84_2.barPos
			local slot_84_5 = slot_84_4.x + 164 * slot_84_3
			local slot_84_6 = slot_84_4.y + 123 * slot_84_3
			local slot_84_7 = (ove_0_37.draws.damage.draw_type:get() == 1 or ove_0_37.draws.damage.colorq.alpha > 60) and (player:spellSlot(0).state == 0 or player.buff.camilleq or player.buff.camilleq2) and ove_0_97(slot_84_2) or 0
			local slot_84_8 = (ove_0_37.draws.damage.draw_type:get() == 1 or ove_0_37.draws.damage.colorw.alpha > 60) and player:spellSlot(1).state == 0 and ove_0_99(slot_84_2) or 0
			local slot_84_9 = (ove_0_37.draws.damage.draw_type:get() == 1 or ove_0_37.draws.damage.colore.alpha > 60) and player:spellSlot(2).state == 0 and ove_0_101(slot_84_2) or 0
			local slot_84_10 = (ove_0_37.draws.damage.draw_type:get() == 1 or ove_0_37.draws.damage.coloraa.alpha > 60) and ove_0_25.CalculateAADamage(slot_84_2, player, ove_0_37.combo.includeaa:get()) + ove_0_95(slot_84_2) or 0
			local slot_84_11 = slot_84_2.health - (slot_84_7 + slot_84_8 + slot_84_9 + slot_84_10)
			local slot_84_12 = slot_84_5 + slot_84_2.health / slot_84_2.maxHealth * (102 * slot_84_3)
			local slot_84_13 = slot_84_5 + (slot_84_11 > 0 and slot_84_11 or 0) / slot_84_2.maxHealth * (102 * slot_84_3)

			if ove_0_37.draws.damage.draw_type:get() == 1 then
				if slot_84_11 > 0 then
					ove_0_48(slot_84_12, slot_84_6, slot_84_13, slot_84_6, 10 * slot_84_3, ove_0_54(ove_0_37.draws.damage.trans:get(), 255, 192, 200))
				else
					ove_0_48(slot_84_12, slot_84_6, slot_84_13, slot_84_6, 10 * slot_84_3, ove_0_54(ove_0_37.draws.damage.trans:get(), 0, 255, 0))
				end
			else
				local slot_84_14 = slot_84_7 + slot_84_8 + slot_84_9 + ove_0_25.CalculateAADamage(slot_84_2, player, ove_0_37.combo.includeaa:get()) + ove_0_95(slot_84_2)
				local slot_84_15 = slot_84_7
				local slot_84_16 = slot_84_8
				local slot_84_17 = slot_84_9
				local slot_84_18 = slot_84_10
				local slot_84_19 = (player:spellSlot(0).state == 0 or player.buff.camilleq or player.buff.camilleq2) and slot_84_15 / slot_84_14 or 0
				local slot_84_20 = player:spellSlot(1).state == 0 and slot_84_16 / slot_84_14 or 0
				local slot_84_21 = player:spellSlot(2).state == 0 and slot_84_17 / slot_84_14 or 0
				local slot_84_22 = slot_84_18 / slot_84_14 or 0
				local slot_84_23 = ove_0_60(0, slot_84_2.health - slot_84_14) / slot_84_2.maxHealth
				local slot_84_24 = slot_84_4.x + 10 + 102 * slot_84_3 * slot_84_23
				local slot_84_25 = slot_84_4.x + 10 + 102 * slot_84_3 * slot_84_2.health / slot_84_2.maxHealth - slot_84_24
				local slot_84_26 = slot_84_4.x + 164 * slot_84_3 + 102 * slot_84_23

				if player:spellSlot(0).state == 0 or player.buff.camilleq or player.buff.camilleq2 then
					ove_0_48(slot_84_12 - (slot_84_21 + slot_84_20 + slot_84_22) * slot_84_25, slot_84_6, slot_84_13, slot_84_6, 10 * slot_84_3, ove_0_54(ove_0_37.draws.damage.trans:get(), ove_0_37.draws.damage.colorq.red, ove_0_37.draws.damage.colorq.green, ove_0_37.draws.damage.colorq.blue))
				end

				if player:spellSlot(1).state == 0 then
					ove_0_48(slot_84_12 - (slot_84_21 + slot_84_22) * slot_84_25, slot_84_6, slot_84_13 + slot_84_19 * slot_84_25, slot_84_6, 10 * slot_84_3, ove_0_54(ove_0_37.draws.damage.trans:get(), ove_0_37.draws.damage.colorw.red, ove_0_37.draws.damage.colorw.green, ove_0_37.draws.damage.colorw.blue))
				end

				if player:spellSlot(2).state == 0 then
					ove_0_48(slot_84_12 - slot_84_22 * slot_84_25, slot_84_6, slot_84_13 + (slot_84_19 + slot_84_20) * slot_84_25, slot_84_6, 10 * slot_84_3, ove_0_54(ove_0_37.draws.damage.trans:get(), ove_0_37.draws.damage.colore.red, ove_0_37.draws.damage.colore.green, ove_0_37.draws.damage.colore.blue))
				end

				ove_0_48(slot_84_12, slot_84_6, slot_84_13 + (slot_84_19 + slot_84_21 + slot_84_20) * slot_84_25, slot_84_6, 10 * slot_84_3, ove_0_54(ove_0_37.draws.damage.trans:get(), ove_0_37.draws.damage.coloraa.red, ove_0_37.draws.damage.coloraa.green, ove_0_37.draws.damage.coloraa.blue))
			end
		end
	end

	if ove_0_37.draws.draww:get() then
		ove_0_55(player.pos, ove_0_32.range, ove_0_37.draws.thickness:get(), ove_0_37.draws.colorw:get(), 80)
	end

	if ove_0_37.draws.drawe:get() then
		ove_0_55(player.pos, ove_0_33.range, ove_0_37.draws.thickness:get(), ove_0_37.draws.colore:get(), 80)
	end

	if ove_0_37.draws.drawe2:get() then
		ove_0_55(player.pos, ove_0_74, ove_0_37.draws.thickness:get(), ove_0_37.draws.colore2:get(), 80)
	end

	if ove_0_37.draws.drawr:get() then
		ove_0_55(player.pos, ove_0_34.range, ove_0_37.draws.thickness:get(), ove_0_37.draws.colorr:get(), 80)
	end
end

local function ove_0_184(arg_85_0)
	if arg_85_0.name == "CamilleEMissile" and arg_85_0.spell and arg_85_0.spell.owner and arg_85_0.spell.owner == player then
		ove_0_73 = arg_85_0
	end
end

local function ove_0_185(arg_86_0)
	if ove_0_73 and arg_86_0 and ove_0_73.ptr == arg_86_0.ptr then
		ove_0_73 = nil
	end
end

local function ove_0_186(arg_87_0)
	if arg_87_0 == 1 then
		local slot_87_0

		for iter_87_0 = 0, objManager.enemies_n - 1 do
			local slot_87_1 = objManager.enemies[iter_87_0]

			if ove_0_25.IsValidTarget(slot_87_1) and slot_87_1.pos2D:dist(mousePos2D) <= 180 then
				if slot_87_0 and slot_87_0.pos2D:dist(mousePos2D) > slot_87_1.pos2D:dist(mousePos2D) then
					slot_87_0 = slot_87_1
				end

				slot_87_0 = slot_87_0 or slot_87_1
			end
		end

		if ove_0_70 and not slot_87_0 then
			ove_0_70 = nil

			return
		end

		if slot_87_0 then
			ove_0_70 = slot_87_0
		end
	end
end

local function ove_0_187(arg_88_0)
	if arg_88_0 and arg_88_0.slot == 0 and arg_88_0.owner == player then
		ove_0_24.core.reset()
	end

	if arg_88_0 and arg_88_0.isBasicAttack and arg_88_0.owner and arg_88_0.owner == player and arg_88_0.target and (arg_88_0.target.type == TYPE_HERO or arg_88_0.target.type == TYPE_MINION) then
		ove_0_69 = arg_88_0.target
	end

	if arg_88_0 and arg_88_0.isBasicAttack and arg_88_0.owner and arg_88_0.owner == player and arg_88_0.target and (arg_88_0.target.type == TYPE_TURRET or arg_88_0.target.type == TYPE_INHIB) then
		ove_0_47 = arg_88_0.target
	end

	if arg_88_0 and arg_88_0.owner and arg_88_0.owner == player and arg_88_0.slot == 2 then
		if arg_88_0.name == "CamilleE" then
			ove_0_75 = game.time + 0.3
			ove_0_76 = player.path.serverPos + 700 / player.path.serverPos:dist(vec3(arg_88_0.endPos.x, arg_88_0.endPos.y, arg_88_0.endPos.z)) * (vec3(arg_88_0.endPos.x, arg_88_0.endPos.y, arg_88_0.endPos.z) - player.path.serverPos)
		else
			ove_0_77 = game.time + 0.25
		end
	end

	if arg_88_0 and arg_88_0.owner and arg_88_0.owner == player and arg_88_0.slot == 1 then
		ove_0_24.core.set_pause_attack(0.25)

		ove_0_78 = vec3(arg_88_0.endPos.x, arg_88_0.endPos.y, arg_88_0.endPos.z)
	end

	if ove_0_37.SpellsMenu.enable:get() and player:spellSlot(3).state == 0 then
		local slot_88_0 = ove_0_160()

		if arg_88_0.owner and (ove_0_37.SpellsMenu.ignorefow:get() == false or arg_88_0.owner and arg_88_0.owner.isVisible) and arg_88_0.owner.charName == "XinZhao" and arg_88_0.slot == 3 and ove_0_37.SpellsMenu.Skillshots.XinZhao and ove_0_37.SpellsMenu.Skillshots.XinZhao[3].Dodge:get() and ove_0_37.SpellsMenu.Skillshots.XinZhao[3].hp:get() >= ove_0_25.GetHPPercent(player) and arg_88_0.owner.pos2D:dist(player.pos2D) <= 500 then
			player:castSpell("obj", 3, slot_88_0)
		end

		if arg_88_0 and arg_88_0.owner and arg_88_0.target and arg_88_0.target == player and (ove_0_37.SpellsMenu.ignorefow:get() == false or arg_88_0.owner and arg_88_0.owner.isVisible) and (arg_88_0.owner.charName == "Sett" and arg_88_0.slot == 3 or arg_88_0.owner.charName == "Mordekaiser" and arg_88_0.slot == 3 or arg_88_0.owner.charName == "Malzahar" and arg_88_0.slot == 3 or arg_88_0.owner.charName == "Camille" and arg_88_0.slot == 3 or arg_88_0.owner.charName == "JarvanIV" and arg_88_0.slot == 3 or arg_88_0.owner.charName == "Trundle" and arg_88_0.slot == 3) then
			local slot_88_1 = string.lower(arg_88_0.owner.charName)

			if ove_0_25.TargetedSpells()[slot_88_1] and ove_0_37.SpellsMenu.Targeted[arg_88_0.owner.charName .. arg_88_0.slot] and ove_0_37.SpellsMenu.Targeted[arg_88_0.owner.charName .. arg_88_0.slot].Dodge:get() and (not ove_0_37.SpellsMenu.Targeted[arg_88_0.owner.charName .. arg_88_0.slot].Combat:get() or ove_0_24.menu.combat.key:get() or ove_0_24.menu.hybrid.key:get()) and ove_0_37.SpellsMenu.Targeted[arg_88_0.owner.charName .. arg_88_0.slot].hp:get() >= ove_0_25.GetHPPercent(player) and ove_0_166(arg_88_0.slot, arg_88_0.owner) then
				player:castSpell("obj", 3, slot_88_0)
			end
		end
	end

	if arg_88_0 and arg_88_0.owner and arg_88_0.owner.type == TYPE_HERO and arg_88_0.owner.team == TEAM_ENEMY and arg_88_0.slot == 0 and arg_88_0.owner.charName == "Riven" and arg_88_0.owner.buff.riventricleave and arg_88_0.owner.buff.riventricleave.stacks == 2 then
		local slot_88_2 = ove_0_25.VectorExtend(arg_88_0.owner.pos, arg_88_0.owner.pos + arg_88_0.owner.direction, 300)

		if ove_0_37.SpellsMenu.Skillshots.Riven[0] and ove_0_37.SpellsMenu.Skillshots.Riven[0].Dodge:get() and ove_0_37.SpellsMenu.Skillshots.Riven[0].hp:get() >= ove_0_25.GetHPPercent(player) and slot_88_2:to2D():dist(player.pos2D) <= 290 then
			player:castSpell("obj", 3, clos)
		end
	end

	if ove_0_37.SpellsMenu.enable:get() and player:spellSlot(1).state == 0 then
		if arg_88_0.isBasicAttack and arg_88_0.owner and arg_88_0.owner.team == TEAM_ENEMY and arg_88_0.owner.type == TYPE_HERO and arg_88_0.target and arg_88_0.target == player then
			if player.buff.ornnvulnerabledebuff and arg_88_0.owner.charName == "Ornn" then
				local slot_88_3 = string.lower(arg_88_0.owner.charName)

				if ove_0_37.SpellsMenu.Targeted.Ornn1 and ove_0_37.SpellsMenu.Targeted.Ornn1.Dodge:get() and ove_0_37.SpellsMenu.Targeted.Ornn1.hp:get() >= ove_0_25.GetHPPercent(player) and (not ove_0_37.SpellsMenu.Targeted.Ornn1.Combat:get() or ove_0_24.menu.combat.key:get()) then
					player:castSpell("obj", 3, clos)
				end
			end

			if player.buff.braummark and player.buff.braummark.stacks == 3 then
				local slot_88_4 = string.lower(arg_88_0.owner.charName)

				if ove_0_37.SpellsMenu.Targeted["Braum-1"] and ove_0_37.SpellsMenu.Targeted["Braum-1"].Dodge:get() and ove_0_37.SpellsMenu.Targeted["Braum-1"].hp:get() >= ove_0_25.GetHPPercent(player) and (not ove_0_37.SpellsMenu.Targeted["Braum-1"].Combat:get() or ove_0_24.menu.combat.key:get()) then
					player:castSpell("obj", 3, clos)
				end
			end

			if arg_88_0.owner.charName == "Tryndamere" and arg_88_0.owner.buff.gangplankpassiveattack then
				local slot_88_5 = string.lower(arg_88_0.owner.charName)

				if ove_0_37.SpellsMenu.Targeted["Tryndamere-1"] and ove_0_37.SpellsMenu.Targeted["Tryndamere-1"].Dodge:get() and ove_0_37.SpellsMenu.Targeted["Tryndamere-1"].hp:get() >= ove_0_25.GetHPPercent(player) and arg_88_0.name:lower():find("crit") and (not ove_0_37.SpellsMenu.Targeted["Tryndamere-1"].Combat:get() or ove_0_24.menu.combat.key:get()) then
					player:castSpell("obj", 3, clos)
				end
			end

			if arg_88_0.owner.charName == "Nautilus" and arg_88_0.name == "NautilusRavageStrikeAttack" then
				local slot_88_6 = string.lower(arg_88_0.owner.charName)

				if ove_0_37.SpellsMenu.Targeted["Nautilus-1"] and ove_0_37.SpellsMenu.Targeted["Nautilus-1"].Dodge:get() and ove_0_37.SpellsMenu.Targeted["Nautilus-1"].hp:get() >= ove_0_25.GetHPPercent(player) and (not ove_0_37.SpellsMenu.Targeted["Nautilus-1"].Combat:get() or ove_0_24.menu.combat.key:get()) then
					player:castSpell("obj", 3, clos)
				end
			end

			if arg_88_0.owner.charName == "Gangplank" and arg_88_0.owner.buff.gangplankpassiveattack then
				local slot_88_7 = string.lower(arg_88_0.owner.charName)

				if ove_0_37.SpellsMenu.Targeted["Gangplank-1"] and ove_0_37.SpellsMenu.Targeted["Gangplank-1"].Dodge:get() and ove_0_37.SpellsMenu.Targeted["Gangplank-1"].hp:get() >= ove_0_25.GetHPPercent(player) and (not ove_0_37.SpellsMenu.Targeted["Gangplank-1"].Combat:get() or ove_0_24.menu.combat.key:get()) then
					player:castSpell("obj", 3, clos)
				end
			end
		end

		if arg_88_0.owner and arg_88_0.owner.type == TYPE_HERO and arg_88_0.owner.team == TEAM_ENEMY and arg_88_0.target and arg_88_0.target == player then
			local slot_88_8 = string.lower(arg_88_0.owner.charName)

			if ove_0_37.SpellsMenu.Targeted.TwistedFate1 and ove_0_37.SpellsMenu.Targeted.TwistedFate1.Dodge:get() and ove_0_37.SpellsMenu.Targeted.TwistedFate1.hp:get() >= ove_0_25.GetHPPercent(player) then
				if arg_88_0.name == "GoldCardPreAttack" and ove_0_37.SpellsMenu.Targeted.TwistedFate1.Gold:get() then
					player:castSpell("obj", 3, clos)
				end

				if arg_88_0.name == "RedCardPreAttack" and ove_0_37.SpellsMenu.Targeted.TwistedFate1.Red:get() then
					player:castSpell("obj", 3, clos)
				end

				if arg_88_0.name == "BlueCardPreAttack" and ove_0_37.SpellsMenu.Targeted.TwistedFate1.Blue:get() then
					player:castSpell("obj", 3, clos)
				end
			end
		end

		if player.buff.apheliosgravitumdebuff and arg_88_0.owner and arg_88_0.owner.type == TYPE_HERO and arg_88_0.owner.team == TEAM_ENEMY and arg_88_0.name == "ApheliosGravitumQ" and arg_88_0.target and arg_88_0.target == player then
			local slot_88_9 = string.lower(arg_88_0.owner.charName)

			if ove_0_37.SpellsMenu.Skillshots.Aphelios and ove_0_37.SpellsMenu.Skillshots.Aphelios[0].Dodge:get() and ove_0_37.SpellsMenu.Skillshots.Aphelios[0].hp:get() >= ove_0_25.GetHPPercent(player) then
				player:castSpell("obj", 3, clos)
			end
		end
	end
end



cb.add(cb.create_missile, ove_0_184)
cb.add(cb.delete_missile, ove_0_185)

local function ove_0_188(arg_89_0)
	if arg_89_0 and ove_0_47 and arg_89_0.ptr == ove_0_47.ptr then
		print("Dl")

		ove_0_47 = nil
	end
end

cb.add(cb.delete_obj, ove_0_188)
ove_0_24.combat.register_f_pre_tick(ove_0_178)
cb.add(cb.draw, ove_0_183)
cb.add(cb.spell, ove_0_187)
cb.add(cb.delete_minion, ove_0_149)
cb.add(cb.keydown, ove_0_186)

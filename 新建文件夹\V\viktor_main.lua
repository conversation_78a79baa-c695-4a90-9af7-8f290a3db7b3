local ove_0_5 = require("orb/main")
local ove_0_6 = require("viktor/q")
local ove_0_7 = require("viktor/w")
local ove_0_8 = require("viktor/e")
local ove_0_9 = require("viktor/r")
local ove_0_10 = require("viktor/menu")
local ove_0_11
local ove_0_12 = {
	ViktorChaosStorm = 0.25,
	ViktorGravitonField = 0.25,
	Viktor<PERSON>owerTransfer = 0.1
}

local function ove_0_13()
	-- print 1
	if ove_0_11 and os.clock() > ove_0_11 then
		ove_0_11 = nil

		ove_0_5.core.set_pause(0)
	end

	ove_0_9.update()

	if ove_0_5.core.is_paused() or not ove_0_5.core.can_action() or ove_0_5.core.is_move_paused() then
		return
	end

	local slot_1_0 = module.seek("evade")

	if slot_1_0 and slot_1_0.core.is_active() then
		if ove_0_5.menu.combat.key:get() and ove_0_10.e.combat:get() then
			ove_0_8.invoke()
		end

		if ove_0_9.invoke_storm_move() then
			return
		end

		return
	end

	if ove_0_9.invoke_mec() then
		return
	end

	if ove_0_9.invoke_interupts() then
		return
	end

	if ove_0_5.menu.combat.key:get() then
		if ove_0_10.e.combat:get() then
			ove_0_8.invoke()
		end

		if ove_0_10.keys.all_in:get() and ove_0_9.invoke_combat() then
			return
		end

		if ove_0_10.w.combat:get() and ove_0_7.invoke_combat() then
			return
		end

		if ove_0_10.q.combat:get() and ove_0_6.invoke() then
			return
		end
	end

	if ove_0_7.invoke_mec() then
		return
	end

	if ove_0_7.invoke_teleport() then
		return
	end

	if ove_0_10.w.dashes:get() and ove_0_7.invoke_on_dash() then
		return
	end

	if ove_0_10.w.crowd_control:get() and ove_0_7.invoke_hardlock() then
		return
	end

	if ove_0_9.invoke_storm_move() then
		return
	end

	if ove_0_5.menu.lane_clear.key:get() then
		if ove_0_10.q.harass_lane_clear:get() and ove_0_10.q.harass_mana:get() * 0.01 <= player.par / player.maxPar and ove_0_6.invoke(true) then
			return
		end

		if ove_0_10.e.harass_lane_clear:get() and ove_0_10.e.harass_mana:get() * 0.01 <= player.par / player.maxPar and ove_0_8.invoke(true) then
			return
		end

		if ove_0_10.keys.jungle_clear:get() then
			if ove_0_10.q.jungle_clear:get() and ove_0_6.invoke_jungle_clear() then
				return
			end

			if ove_0_10.e.jungle_clear:get() and ove_0_8.invoke_jungle_clear() then
				return
			end
		end

		if ove_0_10.keys.lane_clear:get() then
			if ove_0_10.q.lane_clear:get() and ove_0_6.invoke_lane_clear() then
				return
			end

			if ove_0_10.e.lane_clear:get() and ove_0_8.invoke_lane_clear() then
				return
			end
		end

		if ove_0_10.q.farm_assist:get() and ove_0_6.invoke_farm_assist() then
			return
		end
	end

	if ove_0_5.menu.hybrid.key:get() then
		if ove_0_10.q.harass_hybrid:get() and ove_0_10.q.harass_mana:get() * 0.01 <= player.par / player.maxPar and ove_0_6.invoke(true) then
			return
		end

		if ove_0_10.e.harass_hybrid:get() and ove_0_10.e.harass_mana:get() * 0.01 <= player.par / player.maxPar and ove_0_8.invoke(true) then
			return
		end

		if ove_0_10.q.farm_assist:get() and ove_0_6.invoke_farm_assist() then
			return
		end
	end

	if ove_0_5.menu.last_hit.key:get() and ove_0_10.q.farm_assist:get() and ove_0_6.invoke_farm_assist() then
		return
	end
end

local function ove_0_14()
	-- print 2
	ove_0_6.on_draw()
	ove_0_7.on_draw()
	ove_0_8.on_draw()
	ove_0_9.on_draw()
end

local function ove_0_15(arg_3_0)
	-- print 3
	if arg_3_0.owner.ptr == player.ptr then
		local slot_3_0 = arg_3_0.name

		if ove_0_12[slot_3_0] then
			ove_0_11 = os.clock() + ove_0_12[slot_3_0]

			ove_0_5.core.set_pause(math.huge)
		end
	end
end

local function ove_0_16(arg_4_0)
	-- print 4
	ove_0_9.on_create_minion(arg_4_0)
end

local function ove_0_17(arg_5_0)
	-- print 5
	ove_0_7.on_create_particle(arg_5_0)
end

local function ove_0_18(arg_6_0)
	-- print 6
	ove_0_8.on_create_missile(arg_6_0)
end

ove_0_5.combat.register_f_pre_tick(ove_0_13)
cb.add(cb.draw, ove_0_14)
cb.add(cb.spell, ove_0_15)
cb.add(cb.create_minion, ove_0_16)
cb.add(cb.create_missile, ove_0_18)
cb.add(cb.create_particle, ove_0_17)

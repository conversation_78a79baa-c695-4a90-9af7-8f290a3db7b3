

local ove_0_10 = module.load(header.id, "common/common")

local function ove_0_11(arg_5_0)
	if arg_5_0 == "P" then
		return player.levelRef
	end

	return player:spellSlot(arg_5_0).level
end

SpellLib = {
	Aatrox = {
		Passive = {
			SpellSlot = -1,
			DamageType = "Physical",
			DmgModHP = function()
				return 0.05 + 0.00411764705882353 * (player.levelRef - 1)
			end,
			DmgModHPBase = function(arg_7_0, arg_7_1)
				return arg_7_1.maxHealth
			end
		},
		Q1 = {
			SpellSlot = 0,
			DamageType = "Physical",
			Delay = function()
				return 0.6
			end,
			Range = function()
				return 625
			end,
			Width = function()
				return 180
			end,
			Dmg = function()
				return ({
					10,
					30,
					50,
					70,
					90
				})[ove_0_11(0)]
			end,
			DmgModAD = function(arg_12_0, arg_12_1)
				return ove_0_10.GetTotalAD(arg_12_1) * ({
					0.6,
					0.65,
					0.7,
					0.75,
					0.8
				})[ove_0_11(0)]
			end
		},
		Q2 = {
			SpellSlot = 0,
			DamageType = "Physical",
			Delay = function()
				return 0.6
			end,
			Range = function()
				return 475
			end,
			Width = function()
				return 500
			end,
			Dmg = function()
				return ({
					12.5,
					37.5,
					62.5,
					87.5,
					112.5
				})[ove_0_11(0)]
			end,
			DmgModAD = function(arg_17_0, arg_17_1)
				return ove_0_10.GetTotalAD(arg_17_1) * ({
					0.75,
					0.8125,
					0.875,
					0.9375,
					1
				})[ove_0_11(0)]
			end
		},
		Q3 = {
			SpellSlot = 0,
			DamageType = "Physical",
			Delay = function()
				return 0.6
			end,
			Range = function()
				return 200
			end,
			Width = function()
				return 180
			end,
			Dmg = function()
				return ({
					15,
					45,
					75,
					105,
					135
				})[ove_0_11(0)]
			end,
			DmgModAD = function(arg_22_0, arg_22_1)
				return ove_0_10.GetTotalAD(arg_22_1) * ({
					0.9,
					0.975,
					1.05,
					1.125,
					1.2
				})[ove_0_11(0)]
			end
		},
		W = {
			SpellSlot = 1,
			DamageType = "Physical",
			Delay = function()
				return 0.25
			end,
			Speed = function()
				return 1800
			end,
			Range = function()
				return 825
			end,
			Width = function()
				return 160
			end,
			Dmg = function()
				return ({
					30,
					40,
					50,
					60,
					70
				})[ove_0_11(1)]
			end,
			DmgModAD = function(arg_28_0, arg_28_1)
				return ove_0_10.GetTotalAD(arg_28_1) * 0.4
			end
		},
		E = {
			SpellSlot = 2
		},
		R = {
			SpellSlot = 3,
			DamageType = "Physical",
			Delay = function()
				return 0.25
			end,
			DmgModAD = function(arg_30_0, arg_30_1)
				return ove_0_10.GetBonusAD(arg_30_1) * ({
					0.2,
					0.3,
					0.4
				})[ove_0_11(3)]
			end
		}
	},
	Ahri = {
		Passive = {
			SpellSlot = -1
		},
		Q1 = {
			SpellSlot = 0,
			DamageType = "Magic",
			Range = function()
				return 900
			end,
			Width = function()
				return 200
			end,
			Speed = function()
				return 1550
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function()
				return ({
					40,
					65,
					90,
					115,
					140
				})[ove_0_11(0)]
			end,
			DmgModAP = function(arg_36_0, arg_36_1)
				return ove_0_10.GetTotalAP(arg_36_1) * 0.4
			end
		},
		Q2 = {
			SpellSlot = 0,
			DamageType = "True",
			Speed = function()
				return 1900
			end,
			Dmg = function()
				return ({
					40,
					65,
					90,
					115,
					140
				})[ove_0_11(0)]
			end,
			DmgModAP = function(arg_39_0, arg_39_1)
				return ove_0_10.GetTotalAP(arg_39_1) * 0.4
			end
		},
		W1 = {
			SpellSlot = 1,
			DamageType = "Magic",
			Range = function()
				return 725
			end,
			Delay = function()
				return 0
			end,
			Dmg = function()
				return ({
					50,
					75,
					100,
					125,
					150
				})[ove_0_11(1)]
			end,
			DmgModAP = function(arg_43_0, arg_43_1)
				return ove_0_10.GetTotalAP(arg_43_1) * 0.3
			end
		},
		W2 = {
			SpellSlot = 1,
			DamageType = "Magic",
			Range = function()
				return 725
			end,
			Delay = function()
				return 0
			end,
			Dmg = function()
				return ({
					80,
					120,
					160,
					200,
					240
				})[ove_0_11(1)]
			end,
			DmgModAP = function(arg_47_0, arg_47_1)
				return ove_0_10.GetTotalAP(arg_47_1) * 0.48
			end
		},
		E = {
			SpellSlot = 2,
			DamageType = "Magic",
			Range = function()
				return 1000
			end,
			Width = function()
				return 120
			end,
			Speed = function()
				return 1550
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function()
				return ({
					80,
					110,
					140,
					170,
					200
				})[ove_0_11(2)]
			end,
			DmgModAP = function(arg_53_0, arg_53_1)
				return ove_0_10.GetTotalAP(arg_53_1) * 0.6
			end
		},
		RStack = {
			SpellSlot = 3,
			DamageType = "Magic",
			Delay = function()
				return 0
			end,
			Range = function()
				return 500
			end,
			Width = function()
				return 600
			end,
			Dmg = function()
				return ({
					60,
					90,
					120
				})[ove_0_11(3)]
			end,
			DmgModAP = function(arg_58_0, arg_58_1)
				return ove_0_10.GetTotalAP(arg_58_1) * 0.35
			end
		}
	},
	Akali = {
		Passive = {
			SpellSlot = -1,
			DamageType = "Magic",
			RangeMod = function()
				return 2
			end,
			Dmg = function()
				return ({
					35,
					38,
					41,
					44,
					47,
					50,
					53,
					62,
					71,
					80,
					89,
					98,
					107,
					122,
					137,
					152,
					167,
					182
				})[player.levelRef]
			end,
			DmgModAD = function(arg_61_0, arg_61_1)
				return ove_0_10.GetBonusAD(arg_61_1) * 0.6
			end,
			DmgModAP = function(arg_62_0, arg_62_1)
				return ove_0_10.GetTotalAP(arg_62_1) * 0.55
			end
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Magic",
			Range = function()
				return 500
			end,
			Delay = function()
				return 0.25
			end,
			Width = function()
				return 350
			end,
			Dmg = function()
				return ({
					30,
					55,
					80,
					105,
					130
				})[ove_0_11(0)]
			end,
			DmgModAD = function(arg_67_0, arg_67_1)
				return ove_0_10.GetTotalAD(arg_67_1) * 0.65
			end,
			DmgModAP = function(arg_68_0, arg_68_1)
				return ove_0_10.GetTotalAP(arg_68_1) * 0.6
			end
		},
		W = {
			SpellSlot = 1,
			Range = function()
				return 250
			end,
			Width = function()
				return 1175
			end,
			Delay = function()
				return 0.25
			end
		},
		E1 = {
			SpellSlot = 2,
			DamageType = "Magic",
			Range = function()
				return 825
			end,
			Width = function()
				return 120
			end,
			Speed = function()
				return 1800
			end,
			Delay = function()
				return 0.4
			end,
			Dmg = function()
				return ({
					30,
					56.25,
					82.5,
					108.75,
					135
				})[ove_0_11(2)]
			end,
			DmgModAD = function(arg_77_0, arg_77_1)
				return ove_0_10.GetTotalAD(arg_77_1) * 0.255
			end,
			DmgModAP = function(arg_78_0, arg_78_1)
				return ove_0_10.GetTotalAP(arg_78_1) * 0.36
			end
		},
		E2 = {
			SpellSlot = 2,
			DamageType = "Magic",
			Range = function()
				return math.huge
			end,
			Speed = function()
				return 1500
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function()
				return ({
					70,
					131,
					192,
					253,
					315
				})[ove_0_11(2)]
			end,
			DmgModAD = function(arg_83_0, arg_83_1)
				return ove_0_10.GetTotalAD(arg_83_1) * 0.595
			end,
			DmgModAP = function(arg_84_0, arg_84_1)
				return ove_0_10.GetTotalAP(arg_84_1) * 0.84
			end
		},
		R1 = {
			SpellSlot = 3,
			DamageType = "Physical",
			Range = function()
				return 675
			end,
			Speed = function()
				return 1500
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function()
				return ({
					80,
					220,
					360
				})[ove_0_11(3)]
			end,
			DmgModAD = function(arg_89_0, arg_89_1)
				return ove_0_10.GetBonusAD(arg_89_1) * 0.5
			end,
			DmgModAP = function(arg_90_0, arg_90_1)
				return ove_0_10.GetTotalAP(arg_90_1) * 0.3
			end
		},
		R2 = {
			DamageType = "Magic",
			SpellSlot = 3,
			Range = function()
				return 800
			end,
			Speed = function()
				return 3000
			end,
			Delay = function()
				return 0
			end,
			Dmg = function()
				return ({
					60,
					130,
					200
				})[ove_0_11(3)]
			end,
			DmgModAP = function(arg_95_0, arg_95_1)
				return ove_0_10.GetTotalAP(arg_95_1) * 0.3
			end,
			DmgModHP = function(arg_96_0, arg_96_1)
				if arg_96_1 ~= nil then
					return 0
				end

				local slot_96_0 = 1 - arg_96_1.health / arg_96_1.maxHealth

				if slot_96_0 > 0.7 then
					slot_96_0 = 0.7
				end

				return 0.286 * slot_96_0
			end,
			DmgModHPBase = function()
				return ({
					60,
					130,
					200
				})[ove_0_11(3)]
			end
		}
	},
	Akshan = {
		Passive = {
			SpellSlot = -1,
			DamageType = "Physical",
			Speed = function()
				return 2000
			end,
			DmgModAD = function()
				return 0.5
			end,
			DmgModADBase = function(arg_100_0, arg_100_1)
				return ove_0_10.GetTotalAD(arg_100_1)
			end
		},
		PassiveStack = {
			SpellSlot = -1,
			DamageType = "Magic",
			Speed = function()
				return 5000
			end,
			Dmg = function()
				return ({
					10,
					15,
					20,
					25,
					30,
					35,
					40,
					45,
					55,
					65,
					75,
					85,
					95,
					105,
					120,
					135,
					150,
					165
				})[player.levelRef]
			end,
			DmgModAPBase = function(arg_103_0, arg_103_1)
				return ove_0_10.GetTotalAP(arg_103_1)
			end
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Physical",
			Range = function()
				return 850
			end,
			Delay = function()
				return 0.25
			end,
			Speed = function()
				return 1500
			end,
			Dmg = function()
				return ({
					5,
					25,
					45,
					65,
					85
				})[ove_0_11(0)]
			end,
			DmgModAD = function()
				return ove_0_10.GetTotalAD(Target) * 0.8
			end
		},
		W = {
			SpellSlot = 1,
			Range = function()
				return math.huge
			end,
			Delay = function()
				return 0.5
			end
		},
		E = {
			SpellSlot = 2,
			DamageType = "Physical",
			Range = function()
				return 800
			end,
			Delay = function()
				return 0
			end,
			Speed = function()
				return 2500
			end,
			Dmg = function()
				return ({
					30,
					45,
					60,
					75,
					90
				})[ove_0_11(2)]
			end,
			DmgModAD = function(arg_115_0, arg_115_1)
				return ove_0_10.GetBonusAD(arg_115_1) * 0.175
			end
		},
		R = {
			SpellSlot = 3,
			DamageType = "Physical",
			Range = function()
				return 2500
			end,
			Delay = function()
				return 0
			end,
			Dmg = function()
				return ({
					100,
					150,
					210
				})[ove_0_11(3)]
			end,
			DmgModAD = function()
				return ({
					0.5,
					0.6,
					0.7
				})[ove_0_11(3)]
			end,
			DmgModADBase = function()
				return ove_0_10.GetTotalAD(Target)
			end
		}
	},
	Alistar = {
		Passive = {
			SpellSlot = -1
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Magic",
			Speed = function()
				return 375
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function()
				return ({
					60,
					100,
					140,
					180,
					220
				})[ove_0_11(0)]
			end,
			DmgModAP = function(arg_124_0, arg_124_1)
				return ove_0_10.GetTotalAP(arg_124_1) * 0.5
			end
		},
		W = {
			SpellSlot = 1,
			DamageType = "Magic",
			Speed = function()
				return 650
			end,
			Delay = function()
				return 0
			end,
			Dmg = function()
				return ({
					55,
					110,
					165,
					220,
					275
				})[ove_0_11(1)]
			end,
			DmgModAP = function(arg_128_0, arg_128_1)
				return ove_0_10.GetTotalAP(arg_128_1) * 0.7
			end
		},
		E = {
			SpellSlot = 2,
			DamageType = "Magic",
			Speed = function()
				return 350
			end,
			Delay = function()
				return 0
			end,
			Dmg = function()
				return ({
					80,
					110,
					140,
					170,
					200
				})[ove_0_11(2)]
			end,
			DmgModAP = function(arg_132_0, arg_132_1)
				return ove_0_10.GetTotalAP(arg_132_1) * 0.4
			end
		},
		R = {
			SpellSlot = 3,
			Range = function()
				return math.huge
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function()
				return 0
			end
		}
	},
	Amumu = {
		Passive = {
			SpellSlot = -1,
			DamageType = "True",
			DmgModAP = function()
				return 0.1
			end,
			DmgModAPBase = function(arg_137_0, arg_137_1)
				return ove_0_10.GetTotalAP(arg_137_1)
			end
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Magic",
			Range = function()
				return 1100
			end,
			Width = function()
				return 160
			end,
			Speed = function()
				return 2000
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function()
				return ({
					70,
					95,
					120,
					145,
					170
				})[ove_0_11(0)]
			end,
			DmgModAP = function(arg_143_0, arg_143_1)
				return ove_0_10.GetTotalAP(arg_143_1) * 0.85
			end
		},
		W = {
			DamageType = "Magic",
			SpellSlot = 1,
			Range = function()
				return 350
			end,
			Delay = function()
				return 0
			end,
			Speed = function()
				return 2000
			end,
			Width = function()
				return 350
			end,
			Dmg = function()
				return ({
					6,
					8,
					10,
					12,
					14
				})[ove_0_11(1)]
			end,
			DmgModHP = function()
				return ({
					0.05,
					0.0575,
					0.065,
					0.0725,
					0.08
				})[ove_0_11(1)]
			end,
			DmgModHPBase = function(arg_150_0, arg_150_1)
				return arg_150_1.maxHealth
			end
		},
		E = {
			SpellSlot = 2,
			DamageType = "Magic",
			Range = function()
				return 350
			end,
			Speed = function()
				return math.huge
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function()
				return ({
					75,
					100,
					125,
					150,
					175
				})[ove_0_11(2)]
			end,
			DmgModAP = function(arg_155_0, arg_155_1)
				return ove_0_10.GetTotalAP(arg_155_1) * 0.5
			end
		},
		R = {
			SpellSlot = 3,
			DamageType = "Magic",
			Range = function()
				return 550
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function()
				return ({
					200,
					300,
					400
				})[ove_0_11(3)]
			end,
			DmgModAP = function(arg_159_0, arg_159_1)
				return ove_0_10.GetTotalAP(arg_159_1) * 0.8
			end
		}
	},
	Anivia = {
		Passive = {
			SpellSlot = -1
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Magic",
			Range = function()
				return 1100
			end,
			Speed = function()
				return 950
			end,
			Delay = function()
				return 0.25
			end,
			Width = function()
				return 220
			end,
			Dmg = function()
				return ({
					60,
					95,
					130,
					165,
					200
				})[ove_0_11(0)]
			end,
			DmgModAP = function(arg_165_0, arg_165_1)
				return ove_0_10.GetTotalAP(arg_165_1) * 0.45
			end
		},
		W = {
			SpellSlot = 1,
			Range = function()
				return 1000
			end,
			Delay = function()
				return 0.25
			end,
			Width = function()
				return ({
					600,
					700,
					800,
					900,
					1000
				})[ove_0_11(1)]
			end
		},
		E = {
			SpellSlot = 2,
			DamageType = "Magic",
			Range = function()
				return 600
			end,
			Speed = function()
				return 1600
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function()
				return ({
					100,
					150,
					200,
					250,
					300
				})[ove_0_11(2)]
			end,
			DmgModAP = function(arg_173_0, arg_173_1)
				return ove_0_10.GetTotalAP(arg_173_1) * 1.2
			end
		},
		R = {
			SpellSlot = 3,
			DamageType = "Magic",
			Range = function()
				return 750
			end,
			Delay = function()
				return 0
			end,
			Dmg = function()
				return ({
					30,
					45,
					60
				})[ove_0_11(3)]
			end,
			DmgModAP = function(arg_177_0, arg_177_1)
				return ove_0_10.GetTotalAP(arg_177_1) * 0.125
			end
		}
	},
	Annie = {
		Passive = {
			SpellSlot = -1
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Magic",
			Range = function()
				return 625
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function()
				return ({
					80,
					115,
					150,
					185,
					220
				})[ove_0_11(0)]
			end,
			DmgModAP = function(arg_181_0, arg_181_1)
				return ove_0_10.GetTotalAP(arg_181_1) * 0.8
			end
		},
		W = {
			SpellSlot = 1,
			DamageType = "Magic",
			Range = function()
				return 600
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function()
				return ({
					70,
					115,
					160,
					205,
					250
				})[ove_0_11(0)]
			end,
			DmgModAP = function(arg_185_0, arg_185_1)
				return ove_0_10.GetTotalAP(arg_185_1) * 0.8
			end
		},
		E = {
			SpellSlot = 2,
			Range = function()
				return 800
			end,
			Delay = function()
				return 0
			end
		},
		R = {
			SpellSlot = 3,
			DamageType = "Magic",
			Range = function()
				return 600
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function()
				return ({
					150,
					275,
					400
				})[ove_0_11(3)]
			end,
			DmgModAP = function(arg_191_0, arg_191_1)
				return ove_0_10.GetTotalAP(arg_191_1) * 0.75
			end
		}
	},
	Ashe = {
		Passive = {
			SpellSlot = -1,
			DamageType = "Physical",
			FrostDmgMod = function()
				return 1.1
			end,
			FrostCritMod = function()
				return 0.75
			end,
			FrostIEMod = function()
				return 0.35
			end,
			DmgModADBase = function(arg_195_0, arg_195_1)
				return ove_0_10.GetTotalAD(arg_195_1) + ove_0_10.GetBonusAD(arg_195_1)
			end
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Physical",
			Speed = function()
				return math.huge
			end,
			Delay = function()
				return 0
			end,
			DmgModAD = function(arg_198_0, arg_198_1)
				return ove_0_10.GetTotalAD(arg_198_1) * ({
					0.21,
					0.22,
					0.23,
					0.24,
					0.25
				})[ove_0_11(0)]
			end
		},
		W = {
			SpellSlot = 1,
			DamageType = "Physical",
			Range = function()
				return 1200
			end,
			Delay = function()
				return 0.25
			end,
			Speed = function()
				return 2000
			end,
			Width = function()
				return 20
			end,
			Dmg = function()
				return ({
					20,
					35,
					50,
					65,
					80
				})[ove_0_11(1)]
			end,
			DmgModAD = function(arg_204_0, arg_204_1)
				return ove_0_10.GetTotalAD(arg_204_1) * 1
			end,
			Arrows = function()
				return ({
					7,
					8,
					9,
					10,
					11
				})[ove_0_11(1)]
			end
		},
		E = {
			SpellSlot = 2,
			Range = function()
				return math.huge
			end,
			Delay = function()
				return 0.25
			end,
			Speed = function()
				return 1400
			end,
			Width = function()
				return 1000
			end
		},
		R = {
			SpellSlot = 3,
			DamageType = "Magic",
			Range = function()
				return 2000
			end,
			Delay = function()
				return 1
			end,
			Width = function()
				return 260
			end,
			Dmg = function()
				return ({
					200,
					400,
					600
				})[ove_0_11(3)] + ove_0_10.GetTotalAP(Target) * 1
			end,
			DmgSecondary = function()
				return ({
					100,
					200,
					300
				})[ove_0_11(3)] + ove_0_10.GetTotalAP(Target) * 0.5
			end,
			DmgModAPBase = function(arg_215_0, arg_215_1)
				return ove_0_10.GetTotalAP(arg_215_1)
			end
		}
	},
	AurelionSol = {
		Passive = {
			SpellSlot = -1,
			DamageType = "Magic",
			Range = function()
				return 650
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function()
				return ({
					12,
					14,
					16,
					18,
					20,
					23,
					26,
					32,
					38,
					44,
					50,
					60,
					70,
					80,
					90,
					100,
					110,
					120
				})[player.levelRef]
			end,
			DmgModAP = function(arg_219_0, arg_219_1)
				return ({
					5,
					10,
					15,
					20,
					25
				})[ove_0_11(1)] + ove_0_10.GetTotalAP(arg_219_1) * 0.25
			end
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Magic",
			Range = function()
				return math.huge
			end,
			Delay = function()
				return 0
			end,
			Dmg = function()
				return ({
					70,
					110,
					150,
					190,
					230
				})[ove_0_11(0)]
			end,
			DmgModAP = function(arg_223_0, arg_223_1)
				return ove_0_10.GetTotalAP(arg_223_1) * 0.65
			end
		},
		W = {
			SpellSlot = 1,
			Range = function()
				return math.huge
			end,
			Delay = function()
				return 0
			end,
			Dmg = function()
				return ({
					16.8,
					19.6,
					22.4,
					25.2,
					28,
					32.2,
					36.4,
					44.8,
					53.2,
					61.6,
					70,
					84,
					98,
					112,
					126,
					140,
					154,
					168
				})[player.levelRef] + ove_0_10.GetTotalAP(Target) * 0.35
			end
		},
		E = {
			SpellSlot = 2,
			Range = function()
				return ({
					5500,
					6000,
					6500,
					7000,
					7500
				})[ove_0_11(2)]
			end,
			Speed = function()
				return ({
					600,
					650,
					700,
					750,
					800
				})[ove_0_11(2)]
			end,
			Delay = function()
				return 0
			end
		},
		R = {
			SpellSlot = 3,
			DamageType = "Magic",
			Range = function()
				return 1500
			end,
			Width = function()
				return 240
			end,
			Delay = function()
				return 0.35
			end,
			Speed = function()
				return 4500
			end,
			Dmg = function()
				return ({
					150,
					250,
					350
				})[ove_0_11(3)]
			end,
			DmgModAP = function(arg_235_0, arg_235_1)
				return ove_0_10.GetTotalAP(arg_235_1) * 0.7
			end
		}
	},
	Azir = {
		Passive = {
			SpellSlot = -1
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Magic",
			Range = function()
				return 740
			end,
			Width = function()
				return 140
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function()
				return ({
					70,
					90,
					110,
					130,
					150
				})[ove_0_11(0)]
			end,
			DmgModAP = function(arg_240_0, arg_240_1)
				return ove_0_10.GetTotalAP(arg_240_1) * 0.3
			end
		},
		W = {
			SpellSlot = 1,
			Range = function()
				return 500
			end,
			Delay = function()
				return 0.25
			end
		},
		E = {
			SpellSlot = 2,
			DamageType = "Magic",
			Range = function()
				return 1100
			end,
			Delay = function()
				return 0
			end,
			Dmg = function()
				return ({
					60,
					90,
					120,
					150,
					180
				})[ove_0_11(2)]
			end,
			DmgModAP = function(arg_246_0, arg_246_1)
				return ove_0_10.GetTotalAP(arg_246_1) * 0.4
			end
		},
		R = {
			SpellSlot = 3,
			DamageType = "Magic",
			Range = function()
				return 400
			end,
			Delay = function()
				return 0.5
			end,
			Speed = function()
				return 1000
			end,
			Dmg = function()
				return ({
					175,
					325,
					475
				})[ove_0_11(3)]
			end,
			DmgModAP = function(arg_251_0, arg_251_1)
				return ove_0_10.GetTotalAP(arg_251_1) * 0.6
			end
		}
	},
	Bard = {
		Passive = {
			SpellSlot = -1
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Magic",
			Range = function()
				return 850
			end,
			Delay = function()
				return 0.25
			end,
			Speed = function()
				return 1500
			end,
			Dmg = function()
				return ({
					80,
					125,
					170,
					215,
					260
				})[ove_0_11(0)]
			end,
			DmgModAP = function(arg_256_0, arg_256_1)
				return ove_0_10.GetTotalAP(arg_256_1) * 0.65
			end
		},
		W = {
			SpellSlot = 1,
			Range = function()
				return 800
			end,
			Delay = function()
				return 0.25
			end
		},
		E = {
			SpellSlot = 2,
			Range = function()
				return 900
			end,
			Delay = function()
				return 0.25
			end
		},
		R = {
			SpellSlot = 3,
			Range = function()
				return 3400
			end,
			Delay = function()
				return 0.5
			end
		}
	},
	Blitzcrank = {
		Passive = {
			SpellSlot = -1
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Magic",
			Range = function()
				return 1020
			end,
			Delay = function()
				return 0.25
			end,
			Speed = function()
				return 1800
			end,
			Width = function()
				return 140
			end,
			Dmg = function()
				return ({
					105,
					155,
					205,
					255,
					305
				})[ove_0_11(0)]
			end,
			DmgModAP = function(arg_268_0, arg_268_1)
				return ove_0_10.GetTotalAP(arg_268_1) * 1.2
			end
		},
		W = {
			SpellSlot = 1,
			Range = function()
				return math.huge
			end,
			Delay = function()
				return 0
			end
		},
		E = {
			SpellSlot = 2,
			DamageType = "Physical",
			Range = function()
				return math.huge
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_273_0, arg_273_1)
				return ove_0_10.GetBonusAD(arg_273_1) * 1
			end
		},
		R = {
			SpellSlot = 3,
			DamageType = "Magic",
			Range = function()
				return 600
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function()
				return ({
					275,
					400,
					525
				})[ove_0_11(3)]
			end,
			DmgModAP = function(arg_277_0, arg_277_1)
				return ove_0_10.GetTotalAP(arg_277_1) * 1
			end
		}
	},
	Brand = {
		Passive = {
			SpellSlot = -1
		},
		StackofABlaze1 = {
			SpellSlot = -1,
			Range = function()
				return 475
			end
		},
		StackofABlaze2 = {
			SpellSlot = -1,
			Range = function()
				return 475
			end
		},
		StackofABlaze3 = {
			SpellSlot = -1,
			DamageType = "Magic",
			Range = function()
				return 475
			end,
			Dmg = function(arg_281_0, arg_281_1)
				return arg_281_1.maxHealth / 100 * ({
					9,
					9.25,
					9.5,
					9.75,
					10,
					10.25,
					10.5,
					10.75,
					11,
					11.25,
					11.5,
					11.75,
					12,
					12.25,
					12.5,
					12.75,
					13,
					13
				})[player.levelRef]
			end,
			DmgModAP = function(arg_282_0, arg_282_1)
				return ove_0_10.GetTotalAP(arg_282_1) * (0.02 * (ove_0_10.GetTotalAP(arg_282_1) % 100))
			end
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Magic",
			Range = function()
				return 1040
			end,
			Delay = function()
				return 0.25
			end,
			Width = function()
				return 120
			end,
			Speed = function()
				return 1600
			end,
			Dmg = function()
				return ({
					80,
					110,
					140,
					170,
					200
				})[ove_0_11(0)]
			end,
			DmgModAP = function(arg_288_0, arg_288_1)
				return ove_0_10.GetTotalAP(arg_288_1) * 0.55
			end
		},
		W = {
			SpellSlot = 1,
			DamageType = "Magic",
			Range = function()
				return 900
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function()
				return ({
					75,
					120,
					165,
					210,
					255
				})[ove_0_11(1)]
			end,
			DmgModAP = function(arg_292_0, arg_292_1)
				return ove_0_10.GetTotalAP(arg_292_1) * 0.6
			end
		},
		WABlaze = {
			SpellSlot = 1,
			DamageType = "Magic",
			Range = function()
				return 900
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function()
				return ({
					93.75,
					150,
					206.25,
					262.5,
					318.75
				})[ove_0_11(1)]
			end,
			DmgModAP = function(arg_296_0, arg_296_1)
				return ove_0_10.GetTotalAP(arg_296_1) * 0.75
			end
		},
		E = {
			SpellSlot = 2,
			DamageType = "Magic",
			Range = function()
				return 675
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_299_0, arg_299_1)
				return ({
					70,
					95,
					120,
					145,
					170
				})[ove_0_11(2)]
			end,
			DmgModAP = function(arg_300_0, arg_300_1)
				return ove_0_10.GetTotalAP(arg_300_1) * 0.45
			end
		},
		R = {
			SpellSlot = 3,
			DamageType = "Magic",
			Range = function()
				return 750
			end,
			Delay = function()
				return 0.25
			end,
			Speed = function()
				return 750
			end,
			Dmg = function()
				return ({
					100,
					200,
					300
				})[ove_0_11(3)]
			end,
			DmgModAP = function(arg_305_0, arg_305_1)
				return ove_0_10.GetTotalAP(arg_305_1) * 0.25
			end
		}
	},
	Braum = {
		Passive = {
			SpellSlot = -1
		},
		PassiveStack1 = {
			SpellSlot = -1
		},
		PassiveStack2 = {
			SpellSlot = -1
		},
		PassiveStack3 = {
			SpellSlot = -1
		},
		PassiveStack4 = {
			SpellSlot = -1,
			DamageType = "Magic",
			Dmg = function(arg_306_0, arg_306_1)
				return 16 + 10 * player.levelRef
			end
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Magic",
			Range = function()
				return 1050
			end,
			Delay = function()
				return 0.25
			end,
			Speed = function()
				return 1700
			end,
			Width = function()
				return 120
			end,
			Dmg = function()
				return ({
					75,
					125,
					175,
					225,
					275
				})[ove_0_11(0)]
			end,
			DmgModHP = function(arg_312_0, arg_312_1)
				return arg_312_1.maxHealth / 100 * 2.5
			end
		},
		W = {
			SpellSlot = 1,
			Range = function()
				return 650
			end,
			Delay = function()
				return 0
			end
		},
		E = {
			SpellSlot = 2,
			Range = function()
				return math.huge
			end,
			Delay = function()
				return 0
			end
		},
		R = {
			SpellSlot = 3,
			DamageType = "Magic",
			Range = function()
				return 1200
			end,
			Delay = function()
				return 0.5
			end,
			Speed = function()
				return 1400
			end,
			Width = function()
				return 230
			end,
			Dmg = function()
				return ({
					150,
					300,
					450
				})[ove_0_11(3)]
			end,
			DmgModAP = function(arg_322_0, arg_322_1)
				return ove_0_10.GetTotalAP(arg_322_1) * 0.6
			end
		}
	},
	Caitlyn = {
		Passive = {
			SpellSlot = -1,
			DamageType = "Physical",
			DmgModAD = function()
				if player.levelRef == 18 then
					return 1
				else
					return 0.5 + 0.25 * math.floor(player.levelRef / 6)
				end
			end,
			Range = function()
				return 1300
			end,
			DmgModADBase = function(arg_325_0, arg_325_1)
				return ove_0_10.GetBonusAD(arg_325_1)
			end,
			CritMod = function(arg_326_0, arg_326_1)
				return 0.09375 + arg_326_1.CritMod
			end
		},
		Q1 = {
			SpellSlot = 0,
			DamageType = "Physical",
			Dmg = function()
				return ({
					50,
					90,
					130,
					170,
					210
				})[ove_0_11(0)]
			end,
			DmgModAD = function()
				return ({
					1.25,
					1.45,
					1.65,
					1.85,
					2.05
				})[ove_0_11(0)]
			end,
			DmgModADBase = function(arg_329_0, arg_329_1)
				return ove_0_10.GetTotalAD(arg_329_1) + ove_0_10.GetBonusAD(arg_329_1)
			end,
			Range = function()
				return 1300
			end,
			Speed = function()
				return 1800
			end,
			Delay = function()
				return 0.625
			end,
			Width = function()
				return 120
			end
		},
		Q2 = {
			SpellSlot = 0,
			DamageType = "Physical",
			Dmg = function()
				return ({
					50,
					90,
					130,
					170,
					210
				})[ove_0_11(0)] * 0.6
			end,
			DmgModAD = function()
				return ({
					1.25,
					1.45,
					1.65,
					1.85,
					2.05
				})[ove_0_11(0)] * 0.6
			end,
			DmgModADBase = function(arg_336_0, arg_336_1)
				return ove_0_10.GetTotalAD(arg_336_1) + ove_0_10.GetBonusAD(arg_336_1)
			end,
			Range = function()
				return 1300
			end,
			Speed = function()
				return 1800
			end,
			Delay = function()
				return 0.625
			end,
			Width = function()
				return 180
			end
		},
		W = {
			SpellSlot = 1,
			Range = function()
				return 800
			end,
			Delay = function()
				return 1.05
			end,
			Speed = function()
				return math.huge
			end,
			Width = function()
				return 67.5
			end,
			LifeTime = function()
				return ({
					30,
					35,
					40,
					45,
					50
				})[ove_0_11(1)]
			end
		},
		WPassive = {
			SpellSlot = 1,
			DamageType = "Physical",
			Dmg = function()
				return ({
					40,
					85,
					130,
					175,
					220
				})[ove_0_11(1)]
			end,
			DmgModAD = function()
				return ({
					0.4,
					0.5,
					0.6,
					0.7,
					0.8
				})[ove_0_11(1)]
			end,
			Range = function()
				return 1300
			end,
			DmgModBase = function(arg_349_0, arg_349_1)
				return ove_0_10.GetBonusAD(arg_349_1)
			end
		},
		E = {
			DamageType = "Magic",
			SpellSlot = 2,
			Dmg = function()
				return ({
					80,
					130,
					180,
					230,
					280
				})[ove_0_11(2)]
			end,
			DmgModAP = function()
				return 0.8
			end,
			DmgModAPBase = function(arg_352_0, arg_352_1)
				return ove_0_10.GetTotalAP(arg_352_1)
			end,
			Range = function()
				return 750
			end,
			Delay = function()
				return 0.15
			end,
			BounceRange = function()
				return 375
			end,
			Width = function()
				return 115
			end,
			Speed = function()
				return 1600
			end
		},
		R = {
			SpellSlot = 3,
			DamageType = "Physical",
			Range = function()
				return 3500
			end,
			Speed = function()
				return 3200
			end,
			Delay = function()
				return 0.375
			end,
			Width = function()
				return 80
			end,
			Dmg = function()
				return ({
					300,
					525,
					750
				})[ove_0_11(3)]
			end,
			DmgModAD = function()
				return 2
			end,
			DmgModADBase = function(arg_364_0, arg_364_1)
				return ove_0_10.GetBonusAD(arg_364_1)
			end
		}
	},
	Camille = {
		Passive = {
			SpellSlot = -1
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Physical",
			Range = function()
				return math.huge
			end,
			Delay = function()
				return 0
			end,
			Dmg = function()
				return ({
					75,
					125,
					175,
					225,
					275
				})[ove_0_11(0)]
			end,
			DmgModAD = function(arg_368_0, arg_368_1)
				return ({
					20,
					25,
					30,
					35,
					40
				})[ove_0_11(0)] + ove_0_10.GetBonusAD(arg_368_1) * 0.4
			end
		},
		W = {
			SpellSlot = 1,
			DamageType = "Physical",
			Range = function()
				return 650
			end,
			Delay = function()
				return 0
			end,
			Dmg = function()
				return ({
					70,
					100,
					130,
					160,
					190
				})[ove_0_11(1)]
			end,
			DmgModAD = function(arg_372_0, arg_372_1)
				return ove_0_10.GetBonusAD(arg_372_1) * 0.6
			end
		},
		E = {
			SpellSlot = 2,
			DamageType = "Physical",
			Range = function()
				return 800
			end,
			Delay = function()
				return 0
			end,
			Width = function()
				return 100
			end,
			Dmg = function()
				return ({
					80,
					110,
					140,
					170,
					200
				})[ove_0_11(2)]
			end,
			DmgModAD = function(arg_377_0, arg_377_1)
				return ove_0_10.GetBonusAD(arg_377_1) * 0.9
			end
		},
		R = {
			SpellSlot = 3,
			DamageType = "Magic",
			Range = function()
				return 475
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_380_0, arg_380_1)
				return ({
					5,
					10,
					15
				})[ove_0_11(3)]
			end,
			DmgModHP = function(arg_381_0, arg_381_1)
				return arg_381_1.health / 100 * ({
					4,
					6,
					8
				})[ove_0_11(3)]
			end
		}
	},
	Cassiopeia = {
		Passive = {
			SpellSlot = -1
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Magic",
			Range = function()
				return 850
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function()
				return ({
					75,
					110,
					145,
					180,
					215
				})[ove_0_11(0)]
			end,
			DmgModAP = function(arg_385_0, arg_385_1)
				return ove_0_10.GetTotalAP(arg_385_1) * 0.9
			end
		},
		W = {
			SpellSlot = 1,
			DamageType = "Magic",
			Range = function()
				return 700
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function()
				return ({
					100,
					125,
					150,
					175,
					200
				})[ove_0_11(1)]
			end,
			DmgModAP = function(arg_389_0, arg_389_1)
				return ove_0_10.GetTotalAP(arg_389_1) * 0.75
			end
		},
		E = {
			SpellSlot = 2,
			DamageType = "Magic",
			Range = function()
				return 700
			end,
			Delay = function()
				return 0.125
			end,
			Speed = function()
				return 2500
			end,
			Dmg = function(arg_393_0, arg_393_1)
				return 48 + 4 * player.levelRef
			end,
			DmgModAP = function(arg_394_0, arg_394_1)
				return ove_0_10.GetTotalAP(arg_394_1) * 0.1
			end,
			DmgBonus = function()
				return ({
					20,
					40,
					60,
					80,
					100
				})[ove_0_11(2)]
			end,
			DmgBonusModAP = function(arg_396_0, arg_396_1)
				return ove_0_10.GetTotalAP(arg_396_1) * 0.6
			end
		},
		R = {
			SpellSlot = 3,
			DamageType = "Magic",
			Range = function()
				return 850
			end,
			Delay = function()
				return 0.5
			end,
			Dmg = function()
				return ({
					150,
					250,
					350
				})[ove_0_11(3)]
			end,
			DmgModAP = function(arg_400_0, arg_400_1)
				return ove_0_10.GetTotalAP(arg_400_1) * 0.5
			end
		}
	},
	Chogath = {
		Passive = {
			SpellSlot = -1
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Magic",
			Range = function()
				return 950
			end,
			Delay = function()
				return 0.5
			end,
			Dmg = function()
				return ({
					80,
					135,
					190,
					245,
					300
				})[ove_0_11(0)]
			end,
			DmgModAD = function(arg_404_0, arg_404_1)
				return ove_0_10.GetTotalAP(arg_404_1) * 1
			end
		},
		W = {
			SpellSlot = 1,
			DamageType = "Magic",
			Range = function()
				return 650
			end,
			Delay = function()
				return 0.5
			end,
			Dmg = function()
				return ({
					75,
					125,
					175,
					225,
					275
				})[ove_0_11(1)]
			end,
			DmgModAD = function(arg_408_0, arg_408_1)
				return ove_0_10.GetTotalAP(arg_408_1) * 0.7
			end
		},
		E = {
			SpellSlot = 2,
			DamageType = "Magic",
			Range = function()
				return 500
			end,
			Delay = function()
				return 0
			end,
			Width = function()
				return 340
			end,
			Dmg = function()
				return ({
					22,
					34,
					46,
					58,
					70
				})[ove_0_11(2)]
			end,
			DmgModAD = function(arg_413_0, arg_413_1)
				return ove_0_10.GetTotalAP(arg_413_1) * 0.3
			end,
			DmgModHP = function(arg_414_0, arg_414_1)
				return arg_414_1.maxHealth / 100 * 3
			end
		},
		R = {
			SpellSlot = 3,
			DamageType = "True",
			Range = function()
				return 175
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function()
				return ({
					300,
					475,
					650
				})[ove_0_11(3)]
			end,
			DmgModAD = function(arg_418_0, arg_418_1)
				return ove_0_10.GetTotalAP(arg_418_1) * 0.5
			end,
			DmgModHP = function(arg_419_0, arg_419_1)
				return arg_419_1.maxHealth / 100 * 10
			end
		}
	},
	Corki = {
		Passive = {
			SpellSlot = -1
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Physical",
			Range = function()
				return 825
			end,
			Delay = function()
				return 0.25
			end,
			Speed = function()
				return 1000
			end,
			Dmg = function()
				return ({
					75,
					120,
					165,
					210,
					255
				})[ove_0_11(0)]
			end,
			DmgModAD = function(arg_424_0, arg_424_1)
				return ove_0_10.GetBonusAD(arg_424_1) * 0.7
			end,
			DmgModAP = function(arg_425_0, arg_425_1)
				return ove_0_10.GetTotalAP(arg_425_1) * 0.5
			end
		},
		W = {
			SpellSlot = 1,
			DamageType = "Magic",
			Range = function()
				return 600
			end,
			Delay = function()
				return 0
			end,
			Dmg = function()
				return ({
					15,
					22.5,
					30,
					37.5,
					45
				})[ove_0_11(1)]
			end,
			DmgModAP = function(arg_429_0, arg_429_1)
				return ove_0_10.GetTotalAP(arg_429_1) * 0.1
			end
		},
		E = {
			SpellSlot = 2,
			DamageType = "Physical",
			Range = function()
				return 690
			end,
			Delay = function()
				return 0
			end,
			Dmg = function()
				return ({
					7.5,
					10.625,
					13.75,
					16.875,
					20
				})[ove_0_11(2)]
			end,
			DmgModAD = function(arg_433_0, arg_433_1)
				return ove_0_10.GetBonusAD(arg_433_1) * 0.15
			end
		},
		R1 = {
			SpellSlot = 3,
			DamageType = "Magic",
			Range = function()
				return 1300
			end,
			Delay = function()
				return 0.175
			end,
			Speed = function()
				return 2000
			end,
			Width = function()
				return 80
			end,
			Dmg = function()
				return ({
					80,
					115,
					150
				})[ove_0_11(3)]
			end,
			DmgModAD = function(arg_439_0, arg_439_1)
				return (ove_0_10.GetTotalAD(arg_439_1) + ove_0_10.GetBonusAD(arg_439_1)) * ({
					0.15,
					0.45,
					0.75
				})[ove_0_11(3)]
			end,
			DmgModAP = function(arg_440_0, arg_440_1)
				return ove_0_10.GetTotalAP(arg_440_1) * 0.12
			end
		},
		R2 = {
			SpellSlot = 3,
			DamageType = "Magic",
			Range = function()
				return 1300
			end,
			Delay = function()
				return 0.175
			end,
			Speed = function()
				return 2000
			end,
			Width = function()
				return 80
			end,
			Dmg = function()
				return ({
					80,
					115,
					150
				})[ove_0_11(3)]
			end,
			DmgModAD = function(arg_446_0, arg_446_1)
				return (ove_0_10.GetTotalAD(arg_446_1) + ove_0_10.GetBonusAD(arg_446_1)) * ({
					0.15,
					0.45,
					0.75
				})[ove_0_11(3)]
			end,
			DmgModAP = function(arg_447_0, arg_447_1)
				return ove_0_10.GetTotalAP(arg_447_1) * 0.12
			end
		},
		R3 = {
			SpellSlot = 3,
			DamageType = "Magic",
			Range = function()
				return 1300
			end,
			Delay = function()
				return 0.175
			end,
			Speed = function()
				return 2000
			end,
			Width = function()
				return 80
			end,
			Dmg = function()
				return ({
					160,
					230,
					300
				})[ove_0_11(3)]
			end,
			DmgModAD = function(arg_453_0, arg_453_1)
				return (ove_0_10.GetTotalAD(arg_453_1) + ove_0_10.GetBonusAD(arg_453_1)) * ({
					0.3,
					0.9,
					0.15
				})[ove_0_11(3)]
			end,
			DmgModAP = function(arg_454_0, arg_454_1)
				return ove_0_10.GetTotalAP(arg_454_1) * 0.24
			end
		}
	},
	Darius = {
		Passive = {
			SpellSlot = -1,
			DamageType = "Physical",
			Dmg = function()
				return ({
					65,
					70,
					75,
					80,
					85,
					90,
					95,
					100,
					105,
					110,
					115,
					120,
					125,
					130,
					135,
					140,
					145,
					150
				})[player.levelRef]
			end,
			DmgModAD = function(arg_456_0, arg_456_1)
				return ove_0_10.GetBonusAD(arg_456_1) * 1.5
			end
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Physical",
			Range = function()
				return 460
			end,
			Delay = function()
				return 0
			end,
			Dmg = function()
				return ({
					50,
					80,
					110,
					140,
					170
				})[ove_0_11(0)]
			end,
			DmgModAD = function(arg_460_0, arg_460_1)
				return (ove_0_10.GetTotalAD(arg_460_1) + ove_0_10.GetBonusAD(arg_460_1)) * ({
					1,
					1.1,
					1.2,
					1.3,
					1.4
				})[ove_0_11(0)]
			end
		},
		W = {
			SpellSlot = 1,
			DamageType = "Physical",
			Range = function()
				return math.huge
			end,
			Delay = function()
				return 0
			end,
			Dmg = function()
				return (ove_0_10.GetTotalAD(Target) + ove_0_10.GetBonusAD(Target)) * ({
					1.4,
					1.45,
					1.5,
					1.55,
					1.6
				})[ove_0_11(1)]
			end
		},
		E = {
			SpellSlot = 2,
			Range = function()
				return 535
			end,
			Delay = function()
				return 0.25
			end
		},
		R = {
			SpellSlot = 3,
			DamageType = "True",
			Range = function()
				return 475
			end,
			Delay = function()
				return 0.3667
			end,
			Dmg = function()
				return ({
					125,
					250,
					375
				})[ove_0_11(3)]
			end,
			DmgModAD = function(arg_469_0, arg_469_1)
				return ove_0_10.GetBonusAD(arg_469_1) * 0.75
			end
		}
	},
	Diana = {
		Passive = {
			SpellSlot = -1
		},
		PassiveStack = {
			SpellSlot = -1
		},
		PassiveStack2 = {
			SpellSlot = -1,
			DamageType = "Magic",
			Dmg = function()
				return ({
					20,
					25,
					30,
					35,
					40,
					45,
					65,
					75,
					85,
					95,
					95,
					135,
					150,
					165,
					180,
					210,
					210,
					220
				})[player.levelRef]
			end,
			DmgModAP = function(arg_471_0, arg_471_1)
				return ove_0_10.GetTotalAP(arg_471_1) * 0.5
			end
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Magic",
			Range = function()
				return 900
			end,
			Delay = function()
				return 0.25
			end,
			Speed = function()
				return 1900
			end,
			Dmg = function()
				return ({
					60,
					95,
					130,
					165,
					200
				})[ove_0_11(0)]
			end,
			DmgModAP = function(arg_476_0, arg_476_1)
				return ove_0_10.GetTotalAP(arg_476_1) * 0.7
			end
		},
		W = {
			SpellSlot = 1,
			DamageType = "Magic",
			Range = function()
				return 200
			end,
			Delay = function()
				return 0
			end,
			Dmg = function()
				return ({
					54,
					90,
					126,
					162,
					198
				})[ove_0_11(1)]
			end,
			DmgModAP = function(arg_480_0, arg_480_1)
				return ove_0_10.GetTotalAP(arg_480_1) * 0.45
			end
		},
		E = {
			SpellSlot = 2,
			DamageType = "Magic",
			Range = function()
				return 825
			end,
			Delay = function()
				return 0
			end,
			Dmg = function()
				return ({
					50,
					70,
					90,
					110,
					130
				})[ove_0_11(2)]
			end,
			DmgModAP = function(arg_484_0, arg_484_1)
				return ove_0_10.GetTotalAP(arg_484_1) * 0.5
			end
		},
		R = {
			SpellSlot = 3,
			DamageType = "Magic",
			Range = function()
				return 475
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function()
				return ({
					200,
					300,
					400
				})[ove_0_11(3)]
			end,
			DmgModAP = function(arg_488_0, arg_488_1)
				return ove_0_10.GetTotalAP(arg_488_1) * 0.6
			end
		}
	},
	DrMundo = {
		Passive = {
			SpellSlot = -1
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Magic",
			Range = function()
				return 990
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function()
				return ({
					80,
					130,
					180,
					230,
					280
				})[ove_0_11(0)]
			end,
			DmgModHP = function(arg_492_0, arg_492_1)
				return arg_492_1.health / 100 * ({
					20,
					22.5,
					25,
					27.5,
					30
				})[ove_0_11(0)]
			end
		},
		W = {
			SpellSlot = 1,
			DamageType = "Magic",
			Range = function()
				return 325
			end,
			Delay = function()
				return 0
			end,
			Dmg = function()
				return ({
					80,
					140,
					200,
					260,
					320
				})[ove_0_11(1)]
			end,
			DmgModHP = function(arg_496_0, arg_496_1)
				return ({
					20,
					35,
					50,
					65,
					80
				})[ove_0_11(1)]
			end
		},
		E = {
			SpellSlot = 2,
			DamageType = "Magic",
			Range = function()
				return 800
			end,
			Delay = function()
				return 0
			end,
			Dmg = function()
				return ({
					40,
					60,
					80,
					100,
					120
				})[ove_0_11(2)]
			end,
			DmgModAP = function(arg_500_0, arg_500_1)
				return ove_0_10.GetTotalAP(arg_500_1) * 0.4
			end
		},
		R = {
			SpellSlot = 3,
			Range = function()
				return math.huge
			end,
			Delay = function()
				return 0
			end
		}
	},
	Draven = {
		Passive = {
			SpellSlot = -1
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Physical",
			Range = function()
				return math.huge
			end,
			Delay = function()
				return 0
			end,
			Dmg = function()
				return ({
					40,
					45,
					50,
					55,
					60
				})[ove_0_11(0)]
			end,
			DmgModAD = function(arg_506_0, arg_506_1)
				return ove_0_10.GetBonusAD(arg_506_1) * ({
					0.75,
					0.85,
					0.95,
					1.05,
					1.15
				})[ove_0_11(0)]
			end
		},
		W = {
			SpellSlot = 1,
			Range = function()
				return math.huge
			end,
			Delay = function()
				return 0
			end
		},
		E = {
			SpellSlot = 2,
			DamageType = "Physical",
			Range = function()
				return 1100
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function()
				return ({
					75,
					110,
					145,
					180,
					215
				})[ove_0_11(2)]
			end,
			DmgModAD = function(arg_512_0, arg_512_1)
				return ove_0_10.GetBonusAD(arg_512_1) * 0.5
			end
		},
		R = {
			SpellSlot = 3,
			DamageType = "Physical",
			Range = function()
				return math.huge
			end,
			Delay = function()
				return 0.5
			end,
			Dmg = function()
				return ({
					175,
					275,
					375
				})[ove_0_11(3)]
			end,
			DmgModAD = function(arg_516_0, arg_516_1)
				return ove_0_10.GetBonusAD(arg_516_1) * ({
					1.1,
					1.3,
					1.5
				})[ove_0_11(3)]
			end
		}
	},
	Ekko = {
		Passive = {
			SpellSlot = -1
		},
		PassiveStack1 = {
			SpellSlot = -1
		},
		PassiveStack2 = {
			SpellSlot = -1
		},
		PassiveStack3 = {
			SpellSlot = -1,
			DamageType = "Magic",
			Dmg = function()
				return ({
					30,
					40,
					50,
					60,
					70,
					80,
					85,
					90,
					95,
					100,
					105,
					110,
					115,
					120,
					125,
					130,
					135,
					140
				})[player.levelRef]
			end,
			DmgModAP = function(arg_518_0, arg_518_1)
				return ove_0_10.GetTotalAP(arg_518_1) * 0.8
			end
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Magic",
			Range = function()
				return 1100
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function()
				return ({
					100,
					140,
					180,
					220,
					260
				})[ove_0_11(0)]
			end,
			DmgModAP = function(arg_522_0, arg_522_1)
				return ove_0_10.GetTotalAP(arg_522_1) * 0.9
			end
		},
		W = {
			SpellSlot = 1,
			Range = function()
				return 1600
			end,
			Delay = function()
				return 0.25
			end
		},
		E = {
			SpellSlot = 2,
			DamageType = "Magic",
			Range = function()
				return 550
			end,
			Delay = function()
				return 0
			end,
			Dmg = function()
				return ({
					50,
					75,
					100,
					125,
					150
				})[ove_0_11(2)]
			end,
			DmgModAD = function(arg_528_0, arg_528_1)
				return ove_0_10.GetTotalAP(arg_528_1) * 0.4
			end
		},
		R = {
			SpellSlot = 3,
			DamageType = "Physical",
			Range = function()
				return 375
			end,
			Delay = function()
				return 0.5
			end,
			Dmg = function()
				return ({
					150,
					300,
					450
				})[ove_0_11(3)]
			end,
			DmgModAD = function(arg_532_0, arg_532_1)
				return ove_0_10.GetTotalAP(arg_532_1) * 1.5
			end
		}
	},
	Elise = {
		Passive = {
			SpellSlot = -1
		},
		PassiveSpiderForm = {
			SpellSlot = -1,
			DamageType = "Magic",
			Dmg = function()
				return ({
					10,
					20,
					30,
					40
				})[ove_0_11(3)]
			end,
			DmgModAP = function(arg_534_0, arg_534_1)
				return ove_0_10.GetTotalAP(arg_534_1) * 0.2
			end
		},
		Q1 = {
			SpellSlot = 0,
			DamageType = "Magic",
			Range = function()
				return 625
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function()
				return ({
					40,
					75,
					110,
					145,
					180
				})[ove_0_11(0)]
			end,
			DmgModAP = function(arg_538_0, arg_538_1)
				return ove_0_10.GetTotalAP(arg_538_1) * (0.03 * (ove_0_10.GetTotalAP(arg_538_1) % 100))
			end,
			DmgModHP = function(arg_539_0, arg_539_1)
				return (T - arg_539_1.health) / 100 * 4
			end
		},
		Q2 = {
			SpellSlot = 0,
			DamageType = "Magic",
			Range = function()
				return 475
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function()
				return ({
					70,
					105,
					140,
					175,
					210
				})[ove_0_11(0)]
			end,
			DmgModAP = function(arg_543_0, arg_543_1)
				return ove_0_10.GetTotalAP(arg_543_1) * (0.03 * (ove_0_10.GetTotalAP(arg_543_1) % 100))
			end,
			DmgModHP = function(arg_544_0, arg_544_1)
				return (T - arg_544_1.health) / 100 * 8
			end
		},
		W = {
			SpellSlot = 1,
			DamageType = "Magic",
			Range = function()
				return 950
			end,
			Delay = function()
				return 0.125
			end,
			Dmg = function()
				return ({
					60,
					105,
					150,
					195,
					240
				})[ove_0_11(1)]
			end,
			DmgModAP = function(arg_548_0, arg_548_1)
				return ove_0_10.GetTotalAP(arg_548_1) * 0.95
			end
		},
		E = {
			SpellSlot = 2,
			Range = function()
				return 1100
			end,
			Delay = function()
				return 0.25
			end,
			Speed = function()
				return 1600
			end,
			Width = function()
				return 110
			end
		},
		R = {
			SpellSlot = 3,
			Range = function()
				return math.huge
			end,
			Delay = function()
				return 0
			end
		}
	},
	Evelynn = {
		Passive = {
			SpellSlot = -1
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Magic",
			Range = function()
				return 800
			end,
			Delay = function()
				return 0.3
			end,
			Speed = function()
				return 2400
			end,
			DmgDart = function()
				return ({
					25,
					30,
					35,
					40,
					45
				})[ove_0_11(0)]
			end,
			DmgDartModAP = function(arg_559_0, arg_559_1)
				return ove_0_10.GetTotalAP(arg_559_1) * 0.3
			end,
			Dmg = function()
				return ({
					75,
					90,
					105,
					120,
					135
				})[ove_0_11(0)]
			end,
			DmgModAP = function(arg_561_0, arg_561_1)
				return ove_0_10.GetTotalAP(arg_561_1) * 0.9
			end
		},
		W = {
			SpellSlot = 1,
			Range = function()
				return ({
					1200,
					1300,
					1400,
					1500,
					1600
				})[ove_0_11(1)]
			end,
			Delay = function()
				return 0.25
			end
		},
		E1 = {
			SpellSlot = 2,
			DamageType = "Magic",
			Range = function()
				return 210
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function()
				return ({
					55,
					70,
					85,
					100,
					115
				})[ove_0_11(2)]
			end,
			DmgModAP = function(arg_567_0, arg_567_1)
				return ove_0_10.GetTotalAP(arg_567_1) * (0.015 % ove_0_10.GetTotalAP(arg_567_1))
			end,
			DmgModHP = function(arg_568_0, arg_568_1)
				return arg_568_1.maxHealth / 100 * 3
			end
		},
		E2 = {
			SpellSlot = 2,
			DamageType = "Magic",
			Range = function()
				return 210
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function()
				return ({
					75,
					100,
					125,
					150,
					175
				})[ove_0_11(2)]
			end,
			DmgModAP = function(arg_572_0, arg_572_1)
				return ove_0_10.GetTotalAP(arg_572_1) * (0.025 % ove_0_10.GetTotalAP(arg_572_1))
			end,
			DmgModHP = function(arg_573_0, arg_573_1)
				return arg_573_1.maxHealth / 100 * 4
			end
		},
		R = {
			SpellSlot = 3,
			DamageType = "Magic",
			Range = function()
				return 500
			end,
			Delay = function()
				return 0.35
			end,
			Dmg = function()
				return ({
					125,
					250,
					375
				})[ove_0_11(3)]
			end,
			DmgModAD = function(arg_577_0, arg_577_1)
				return ove_0_10.GetTotalAP(arg_577_1) * 0.75
			end
		}
	},
	Ezreal = {
		Passive = {
			SpellSlot = -1
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Physical",
			Range = function()
				return 1200
			end,
			Delay = function()
				return 0.25
			end,
			Speed = function()
				return 2000
			end,
			Width = function()
				return 120
			end,
			Dmg = function()
				return ({
					20,
					45,
					70,
					95,
					120
				})[ove_0_11(0)]
			end,
			DmgModAD = function(arg_583_0, arg_583_1)
				return (ove_0_10.GetTotalAD(arg_583_1) + ove_0_10.GetBonusAD(arg_583_1)) * 1.3
			end,
			DmgModAP = function(arg_584_0, arg_584_1)
				return ove_0_10.GetTotalAP(arg_584_1) * 0.15
			end
		},
		W = {
			SpellSlot = 1,
			DamageType = "Magic",
			Range = function()
				return 1200
			end,
			Delay = function()
				return 0.25
			end,
			Speed = function()
				return 1700
			end,
			Width = function()
				return 160
			end,
			Dmg = function()
				return ({
					80,
					135,
					190,
					245,
					300
				})[ove_0_11(1)]
			end,
			DmgModAD = function(arg_590_0, arg_590_1)
				return ove_0_10.GetBonusAD(arg_590_1) * 0.6
			end,
			DmgModAP = function(arg_591_0, arg_591_1)
				return ove_0_10.GetTotalAP(arg_591_1) * ({
					0.7,
					0.75,
					0.8,
					0.85,
					0.9
				})[ove_0_11(1)]
			end
		},
		E = {
			SpellSlot = 2,
			DamageType = "Magic",
			Range = function()
				return 475
			end,
			Delay = function()
				return 0.25
			end,
			Speed = function()
				return 2000
			end,
			Dmg = function()
				return ({
					80,
					130,
					180,
					230,
					280
				})[ove_0_11(2)]
			end,
			DmgModAD = function(arg_596_0, arg_596_1)
				return ove_0_10.GetBonusAD(arg_596_1) * 0.5
			end,
			DmgModAP = function(arg_597_0, arg_597_1)
				return ove_0_10.GetTotalAP(arg_597_1) * 0.75
			end
		},
		R = {
			SpellSlot = 3,
			DamageType = "Magic",
			Range = function()
				return math.huge
			end,
			Delay = function()
				return 1
			end,
			Speed = function()
				return 2000
			end,
			Width = function()
				return 320
			end,
			Dmg = function()
				return ({
					350,
					500,
					650
				})[ove_0_11(3)]
			end,
			DmgModAD = function(arg_603_0, arg_603_1)
				return ove_0_10.GetBonusAD(arg_603_1) * 1
			end,
			DmgModAP = function(arg_604_0, arg_604_1)
				return ove_0_10.GetTotalAP(arg_604_1) * 0.9
			end
		}
	},
	FiddleSticks = {
		Passive = {
			SpellSlot = -1
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Magic",
			Range = function()
				return 575
			end,
			Delay = function()
				return 0.35
			end,
			Dmg = function()
				return Target.health / 100 * ({
					6,
					7,
					8,
					9,
					10
				})[ove_0_11(0)]
			end,
			DmgModAP = function(arg_608_0, arg_608_1)
				return ove_0_10.GetTotalAP(arg_608_1) * (0.02 * (ove_0_10.GetTotalAP(arg_608_1) % 100))
			end
		},
		W = {
			SpellSlot = 1,
			DamageType = "Magic",
			Range = function()
				return 650
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function()
				return ({
					120,
					180,
					240,
					300,
					360
				})[ove_0_11(1)]
			end,
			DmgModAP = function(arg_612_0, arg_612_1)
				return ove_0_10.GetTotalAP(arg_612_1) * 0.7
			end,
			DmgModHP = function(arg_613_0, arg_613_1)
				return (T - arg_613_1.health) / 100 * ({
					12,
					14.5,
					17,
					19.5,
					22
				})[ove_0_11(1)]
			end
		},
		E = {
			SpellSlot = 2,
			DamageType = "Magic",
			Range = function()
				return 850
			end,
			Delay = function()
				return 0.4
			end,
			Dmg = function()
				return ({
					70,
					105,
					140,
					175,
					210
				})[ove_0_11(2)]
			end,
			DmgModAP = function(arg_617_0, arg_617_1)
				return ove_0_10.GetTotalAP(arg_617_1) * 0.5
			end
		},
		R = {
			SpellSlot = 3,
			DamageType = "Magic",
			Range = function()
				return 800
			end,
			Delay = function()
				return 0
			end,
			Dmg = function()
				return ({
					750,
					1250,
					1750
				})[ove_0_11(3)]
			end,
			DmgModAP = function(arg_621_0, arg_621_1)
				return ove_0_10.GetTotalAP(arg_621_1) * 2.5
			end
		}
	},
	Fiora = {
		Passive = {
			SpellSlot = -1
		},
		PassiveVitals = {
			SpellSlot = -1,
			DamageType = "True",
			DmgModHP = function(arg_622_0, arg_622_1)
				return arg_622_1.maxHealth / 100 * 3
			end,
			DmgModAD = function(arg_623_0, arg_623_1)
				return ove_0_10.GetBonusAD(arg_623_1) * (0.045 * (ove_0_10.GetBonusAD(arg_623_1) % 100))
			end
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Physical",
			Range = function()
				return 360
			end,
			Delay = function()
				return 0
			end,
			Dmg = function()
				return ({
					70,
					80,
					90,
					100,
					110
				})[ove_0_11(0)]
			end,
			DmgModAP = function(arg_627_0, arg_627_1)
				return ove_0_10.GetBonusAD(arg_627_1) * ({
					0.95,
					1,
					1.05,
					1.1,
					1,
					15
				})[ove_0_11(0)]
			end
		},
		W = {
			SpellSlot = 1,
			DamageType = "Magic",
			Range = function()
				return 900
			end,
			Delay = function()
				return 0
			end,
			Speed = function()
				return 3200
			end,
			Width = function()
				return 140
			end,
			Dmg = function()
				return ({
					110,
					150,
					190,
					230,
					270
				})[ove_0_11(1)]
			end,
			DmgModAP = function(arg_633_0, arg_633_1)
				return ove_0_10.GetTotalAP(arg_633_1) * 1
			end
		},
		E = {
			SpellSlot = 2,
			Range = function()
				return math.huge
			end,
			Delay = function()
				return 0
			end
		},
		R = {
			SpellSlot = 3,
			Range = function()
				return 500
			end,
			Delay = function()
				return 0
			end
		}
	},
	Fizz = {
		Passive = {
			SpellSlot = -1
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Physical",
			Range = function()
				return 550
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_640_0, arg_640_1)
				return ove_0_10.GetTotalAP(arg_640_1) + (({
					10,
					25,
					40,
					55,
					70
				})[ove_0_11(0)] + ove_0_10.GetTotalAP(arg_640_1) * 0.55)
			end
		},
		W = {
			SpellSlot = 1,
			DamageType = "Magic",
			Range = function()
				return math.huge
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_643_0, arg_643_1)
				return ove_0_10.GetTotalAP(arg_643_1) + (({
					50,
					70,
					90,
					110,
					130
				})[ove_0_11(1)] + ove_0_10.GetTotalAP(arg_643_1) * 0.5)
			end
		},
		E = {
			SpellSlot = 2,
			DamageType = "Magic",
			Range = function()
				return 330
			end,
			Delay = function()
				return 0
			end,
			Dmg = function()
				return ({
					70,
					120,
					170,
					220,
					270
				})[ove_0_11(2)]
			end,
			DmgModAP = function(arg_647_0, arg_647_1)
				return ove_0_10.GetTotalAP(arg_647_1) * 0.75
			end
		},
		R = {
			SpellSlot = 3,
			DamageType = "Magic",
			Range = function()
				return 1300
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function()
				return ({
					300,
					400,
					500
				})[ove_0_11(3)]
			end,
			DmgModAP = function(arg_651_0, arg_651_1)
				return ove_0_10.GetTotalAP(arg_651_1) * 1.2
			end
		}
	},
	Galio = {
		Passive = {
			SpellSlot = -1,
			DamageType = "Magic",
			Dmg = function(arg_652_0, arg_652_1)
				return 15 + 10.882352941176471 * (player.levelRef - 1)
			end,
			DmgModAD = function(arg_653_0, arg_653_1)
				return ove_0_10.GetBonusAD(arg_653_1) * 1
			end,
			DmgModAP = function(arg_654_0, arg_654_1)
				return ove_0_10.GetTotalAP(arg_654_1) * 0.5
			end
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Magic",
			Range = function()
				return 825
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_657_0, arg_657_1)
				return ({
					70,
					105,
					140,
					175,
					210
				})[ove_0_11(0)]
			end,
			DmgModAP = function(arg_658_0, arg_658_1)
				return ove_0_10.GetTotalAP(arg_658_1) * 0.75
			end
		},
		W = {
			SpellSlot = 1,
			DamageType = "Magic",
			Range = function()
				return 350
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_661_0, arg_661_1)
				return ({
					20,
					35,
					50,
					65,
					80
				})[ove_0_11(1)]
			end,
			DmgModAP = function(arg_662_0, arg_662_1)
				return ove_0_10.GetTotalAP(arg_662_1) * 0.3
			end
		},
		E = {
			SpellSlot = 2,
			DamageType = "Magic",
			Range = function()
				return 650
			end,
			Delay = function()
				return 0.4
			end,
			Dmg = function()
				return ({
					90,
					130,
					170,
					210,
					250
				})[ove_0_11(2)]
			end,
			DmgModAP = function(arg_666_0, arg_666_1)
				return ove_0_10.GetTotalAP(arg_666_1) * 0.9
			end
		},
		R = {
			SpellSlot = 3,
			DamageType = "Magic",
			Range = function()
				return ({
					4000,
					4750,
					5500
				})[ove_0_11(3)]
			end,
			Delay = function()
				return 0
			end,
			Dmg = function()
				return ({
					150,
					250,
					350
				})[ove_0_11(3)]
			end,
			DmgModAP = function(arg_670_0, arg_670_1)
				return ove_0_10.GetTotalAP(arg_670_1) * 0.7
			end
		}
	},
	Gangplank = {
		Passive = {
			DamageType = "True",
			DmgModAD = function()
				return 0.1
			end,
			DmgModADBase = function(arg_672_0, arg_672_1)
				return ove_0_10.GetBonusAD(arg_672_1)
			end,
			Dmg = function()
				return (4.5 + player.levelRef) * 10
			end
		},
		Q = {
			DamageType = "Physical",
			Range = function()
				return 625
			end,
			Speed = function()
				return 2600
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function()
				return ({
					10,
					40,
					70,
					100,
					130
				})[ove_0_11(0)]
			end,
			DmgModAD = function()
				return 1
			end,
			DmgModADBase = function(arg_679_0, arg_679_1)
				return ove_0_10.GetTotalAD(arg_679_1) + ove_0_10.GetBonusAD(arg_679_1)
			end
		},
		E = {
			DamageType = "Physical",
			Range = function()
				return 1000
			end,
			Width = function()
				return 325
			end,
			Delay = function()
				return 0.5
			end,
			Speed = function()
				return math.huge
			end,
			Dmg = function()
				return ({
					75,
					105,
					135,
					165,
					195
				})[ove_0_11(2)]
			end,
			ArmorPenMod = function()
				return 0.4
			end
		},
		Barrel = {
			DecayTime = function()
				if player.levelRef < 7 then
					return 2
				elseif player.levelRef >= 7 and player.levelRef < 13 then
					return 1
				else
					return 0.5
				end
			end,
			Width = function()
				return 325
			end,
			ExplosionDelay = function()
				return 0.3
			end
		},
		R = {
			DamageType = "Magic",
			Dmg = function()
				return ({
					40,
					70,
					100
				})[ove_0_11(3)]
			end,
			DmgModAP = function()
				return 0.1
			end,
			DmgModAPBase = function(arg_691_0, arg_691_1)
				return ove_0_10.GetTotalAP(arg_691_1)
			end,
			Range = function()
				return math.huge
			end,
			Delay = function()
				return 0.25
			end,
			Width = function()
				return 600
			end,
			AmountOfWaves = function()
				return 12
			end,
			Speed = function()
				return math.huge
			end
		},
		RCluster = {
			DamageType = "Magic",
			Dmg = function()
				return ({
					120,
					210,
					300
				})[ove_0_11(3)]
			end,
			DmgModAP = function()
				return 0.3
			end,
			DmgModAPBase = function(arg_699_0, arg_699_1)
				return ove_0_10.GetTotalAP(arg_699_1)
			end,
			Range = function()
				return math.huge
			end,
			Width = function()
				return 600
			end,
			Delay = function()
				return 0.25
			end,
			Speed = function()
				return math.huge
			end
		},
		RDeathsDaughter = {
			DamageType = "True",
			Dmg = function()
				return ({
					120,
					210,
					300
				})[ove_0_11(3)]
			end,
			DmgModAP = function()
				return 0.3
			end,
			DmgModAPBase = function(arg_706_0, arg_706_1)
				return ove_0_10.GetTotalAP(arg_706_1)
			end,
			Range = function()
				return math.huge
			end,
			Width = function()
				return 200
			end,
			Delay = function()
				return 0.25
			end,
			Speed = function()
				return math.huge
			end
		}
	},
	Garen = {
		Passive = {
			SpellSlot = -1
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Physical",
			Range = function()
				return math.huge
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_713_0, arg_713_1)
				return ({
					30,
					60,
					90,
					120,
					150
				})[ove_0_11(0)]
			end,
			DmgModAD = function(arg_714_0, arg_714_1)
				return (ove_0_10.GetTotalAD(arg_714_1) + ove_0_10.GetBonusAD(arg_714_1)) * 0.5
			end
		},
		W = {
			SpellSlot = 1,
			Range = function()
				return math.huge
			end,
			Delay = function()
				return 0
			end
		},
		E = {
			SpellSlot = 2,
			DamageType = "Physical",
			Range = function()
				return 325
			end,
			Delay = function()
				return 0
			end,
			Dmg = function()
				return ({
					4,
					8,
					12,
					16,
					20
				})[ove_0_11(2)] * 7
			end,
			DmgModAD = function(arg_720_0, arg_720_1)
				return (ove_0_10.GetTotalAD(arg_720_1) + ove_0_10.GetBonusAD(arg_720_1)) * ({
					0.32,
					0.34,
					0.36,
					0.38,
					0.4
				})[ove_0_11(2)]
			end
		},
		R = {
			SpellSlot = 3,
			DamageType = "True",
			Range = function()
				return 400
			end,
			Delay = function()
				return 0.435
			end,
			Dmg = function()
				return ({
					150,
					300,
					450
				})[ove_0_11(3)]
			end,
			DmgModAD = function(arg_724_0, arg_724_1)
				return (T - arg_724_1.health) / 100 * ({
					25,
					30,
					35
				})[ove_0_11(3)]
			end
		}
	},
	Gnar = {
		Passive = {
			SpellSlot = -1
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Physical",
			Range = function()
				return 1125
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_727_0, arg_727_1)
				return ({
					5,
					45,
					85,
					125,
					165
				})[ove_0_11(0)]
			end,
			DmgModAD = function(arg_728_0, arg_728_1)
				return (ove_0_10.GetTotalAD(arg_728_1) + ove_0_10.GetBonusAD(arg_728_1)) * 1.15
			end
		},
		Q2 = {
			SpellSlot = 0,
			DamageType = "Physical",
			Range = function()
				return 1150
			end,
			Delay = function()
				return 0.5
			end,
			Dmg = function(arg_731_0, arg_731_1)
				return ({
					25,
					70,
					115,
					160,
					205
				})[ove_0_11(0)]
			end,
			DmgModAD = function(arg_732_0, arg_732_1)
				return (ove_0_10.GetTotalAD(arg_732_1) + ove_0_10.GetBonusAD(arg_732_1)) * 1.4
			end
		},
		W = {
			SpellSlot = 1,
			DamageType = "Magic",
			Range = function()
				return 400
			end,
			Delay = function()
				return 0.1
			end,
			Dmg = function(arg_735_0, arg_735_1)
				return ({
					0,
					10,
					20,
					30,
					40
				})[ove_0_11(1)]
			end,
			DmgModAP = function(arg_736_0, arg_736_1)
				return ove_0_10.GetTotalAP(arg_736_1) * 1
			end
		},
		W2 = {
			SpellSlot = 1,
			DamageType = "Physical",
			Range = function()
				return 550
			end,
			Delay = function()
				return 0.6
			end,
			Dmg = function(arg_739_0, arg_739_1)
				return ({
					25,
					55,
					85,
					115,
					145
				})[ove_0_11(1)]
			end,
			DmgModAD = function(arg_740_0, arg_740_1)
				return (ove_0_10.GetTotalAD(arg_740_1) + ove_0_10.GetBonusAD(arg_740_1)) * 1
			end
		},
		E = {
			SpellSlot = 2,
			DamageType = "Physical",
			Range = function()
				return 475
			end,
			Delay = function()
				return 0
			end,
			Dmg = function()
				return ({
					50,
					85,
					120,
					155,
					190
				})[ove_0_11(2)]
			end,
			DmgModHP = function(arg_744_0, arg_744_1)
				return arg_744_1.maxHealth / 100 * 6
			end
		},
		E2 = {
			SpellSlot = 2,
			DamageType = "Physical",
			Range = function()
				return 675
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function()
				return ({
					80,
					115,
					150,
					185,
					220
				})[ove_0_11(2)]
			end,
			DmgModHP = function(arg_748_0, arg_748_1)
				return arg_748_1.maxHealth / 100 * 6
			end
		},
		R = {
			SpellSlot = 3,
			DamageType = "Physical",
			Range = function()
				return 475
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function()
				return ({
					200,
					300,
					400
				})[ove_0_11(3)]
			end,
			DmgModAD = function(arg_752_0, arg_752_1)
				return (ove_0_10.GetTotalAD(arg_752_1) + ove_0_10.GetBonusAD(arg_752_1)) * 0.5
			end,
			DmgModAP = function(arg_753_0, arg_753_1)
				return ove_0_10.GetTotalAP(arg_753_1) * 1
			end
		}
	},
	Gragas = {
		Passive = {
			SpellSlot = -1
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Magic",
			Range = function()
				return 850
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_756_0, arg_756_1)
				return ({
					80,
					120,
					160,
					200,
					240
				})[ove_0_11(0)]
			end,
			DmgModAP = function(arg_757_0, arg_757_1)
				return ove_0_10.GetTotalAP(arg_757_1) * 0.8
			end
		},
		W = {
			SpellSlot = 1,
			DamageType = "Magic",
			Range = function()
				return 250
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_760_0, arg_760_1)
				return ({
					20,
					50,
					80,
					110,
					140
				})[ove_0_11(0)]
			end,
			DmgModAP = function(arg_761_0, arg_761_1)
				return arg_761_1.maxHealth / 100 * 7 + ove_0_10.GetTotalAP(arg_761_1) * 0.7
			end
		},
		E = {
			SpellSlot = 2,
			DamageType = "Magic",
			Range = function()
				return 600
			end,
			Delay = function()
				return 0
			end,
			Dmg = function()
				return ({
					80,
					125,
					170,
					215,
					260
				})[ove_0_11(2)]
			end,
			DmgModAD = function(arg_765_0, arg_765_1)
				return ove_0_10.GetTotalAP(arg_765_1) * 0.6
			end
		},
		R = {
			SpellSlot = 3,
			DamageType = "Magic",
			Range = function()
				return 1000
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function()
				return ({
					200,
					300,
					400
				})[ove_0_11(3)]
			end,
			DmgModAD = function(arg_769_0, arg_769_1)
				return ove_0_10.GetTotalAP(arg_769_1) * 0.8
			end
		}
	},
	Graves = {
		Passive = {
			SpellSlot = -1
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Physical",
			Range = function()
				return 800
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_772_0, arg_772_1)
				return ({
					130,
					180,
					230,
					280,
					330
				})[ove_0_11(0)]
			end,
			DmgModAD = function(arg_773_0, arg_773_1)
				return ove_0_10.GetBonusAD(arg_773_1) * ({
					1.2,
					1.5,
					1.8,
					2.1,
					2.4
				})[ove_0_11(0)]
			end
		},
		W = {
			SpellSlot = 1,
			DamageType = "Magic",
			Range = function()
				return 950
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_776_0, arg_776_1)
				return ({
					60,
					110,
					160,
					210,
					260
				})[ove_0_11(1)]
			end,
			DmgModAP = function(arg_777_0, arg_777_1)
				return ove_0_10.GetTotalAP(arg_777_1) * 0.6
			end
		},
		E = {
			SpellSlot = 2,
			Range = function()
				return 325
			end,
			Delay = function()
				return 0.4
			end
		},
		R = {
			SpellSlot = 3,
			DamageType = "Physical",
			Range = function()
				return 1100
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function()
				return ({
					275,
					425,
					575
				})[ove_0_11(3)]
			end,
			DmgModAD = function(arg_783_0, arg_783_1)
				return ove_0_10.GetBonusAD(arg_783_1) * 1.5
			end
		}
	},
	Gwen = {
		Passive = {
			DamageType = "Magic",
			Range = function()
				return math.huge
			end,
			Width = function()
				return 0
			end,
			Speed = function()
				return math.huge
			end,
			Delay = function()
				return 0
			end,
			DmgModHP = function(arg_788_0, arg_788_1)
				return 0.01 + math.floor(ove_0_10.GetTotalAP(arg_788_1) / 100) * 0.008
			end,
			DmgModHPBase = function(arg_789_0, arg_789_1)
				return arg_789_1.MaxHealth
			end
		},
		Q = {
			DamageType = "Magic",
			Range = function()
				return 450
			end,
			Width = function()
				return 140
			end,
			Speed = function()
				return math.huge
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function()
				return ({
					90,
					120,
					150,
					180,
					210
				})[ove_0_11(0)]
			end,
			DmgModAP = function()
				return 0.5
			end,
			DmgModAPBase = function(arg_796_0, arg_796_1)
				return ove_0_10.GetTotalAP(arg_796_1)
			end
		},
		E = {
			DamageType = "Magic",
			Range = function()
				return math.huge
			end,
			Width = function()
				return 0
			end,
			Speed = function()
				return math.huge
			end,
			Delay = function()
				return 0
			end,
			Dmg = function()
				return 10 * math.min(1, GameHud.AttackSpeed)
			end,
			DmgModAP = function()
				return 0.15
			end,
			DmgModAPBase = function(arg_803_0, arg_803_1)
				return ove_0_10.GetTotalAP(arg_803_1)
			end
		},
		R1 = {
			DamageType = "Magic",
			Range = function()
				return 1350
			end,
			Width = function()
				return 240
			end,
			Speed = function()
				return 1800
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function()
				return ({
					30,
					65,
					95
				})[ove_0_11(3)]
			end,
			DmgModAP = function()
				return 0.1
			end,
			DmgModAPBase = function(arg_810_0, arg_810_1)
				return ove_0_10.GetTotalAP(arg_810_1)
			end,
			DmgModHP = function(arg_811_0, arg_811_1)
				return 0.01 + math.floor(ove_0_10.GetTotalAP(arg_811_1) / 100) * 0.008
			end,
			DmgModHPBase = function(arg_812_0, arg_812_1)
				return arg_812_1.MaxHealth
			end
		},
		R2 = {
			DamageType = "Magic",
			Range = function()
				return 1350
			end,
			Width = function()
				return 240
			end,
			Speed = function()
				return 1800
			end,
			Delay = function()
				return 0.5
			end,
			Dmg = function()
				return ({
					105,
					195,
					285
				})[ove_0_11(3)]
			end,
			DmgModAP = function()
				return 0.3
			end,
			DmgModAPBase = function(arg_819_0, arg_819_1)
				return ove_0_10.GetTotalAP(arg_819_1)
			end,
			DmgModHP = function(arg_820_0, arg_820_1)
				return 0.03 + math.floor(ove_0_10.GetTotalAP(arg_820_1) / 100) * 0.024
			end,
			DmgModHPBase = function(arg_821_0, arg_821_1)
				return arg_821_1.MaxHealth
			end
		},
		R3 = {
			DamageType = "Magic",
			Range = function()
				return 1350
			end,
			Width = function()
				return 240
			end,
			Speed = function()
				return 1800
			end,
			Delay = function()
				return 0.5
			end,
			Dmg = function()
				return ({
					175,
					325,
					475
				})[ove_0_11(3)]
			end,
			DmgModAP = function()
				return 0.5
			end,
			DmgModAPBase = function(arg_828_0, arg_828_1)
				return ove_0_10.GetTotalAP(arg_828_1)
			end,
			DmgModHP = function(arg_829_0, arg_829_1)
				return 0.05 + math.floor(ove_0_10.GetTotalAP(arg_829_1) / 100) * 0.04
			end,
			DmgModHPBase = function(arg_830_0, arg_830_1)
				return arg_830_1.MaxHealth
			end
		}
	},
	Hecarim = {
		Passive = {
			SpellSlot = -1
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Physical",
			Range = function()
				return 375
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_833_0, arg_833_1)
				return ({
					60,
					85,
					110,
					135,
					160
				})[ove_0_11(0)]
			end,
			DmgModAD = function(arg_834_0, arg_834_1)
				return ove_0_10.GetBonusAD(arg_834_1) * 0.95
			end
		},
		W = {
			SpellSlot = 1,
			DamageType = "Magic",
			Range = function()
				return 525
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_837_0, arg_837_1)
				return ({
					80,
					120,
					160,
					200,
					240
				})[ove_0_11(1)]
			end,
			DmgModAP = function(arg_838_0, arg_838_1)
				return ove_0_10.GetTotalAP(arg_838_1) * 0.8
			end
		},
		E = {
			SpellSlot = 2,
			DamageType = "Physical",
			Range = function()
				return math.huge
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_841_0, arg_841_1)
				return ({
					60,
					90,
					120,
					150,
					180
				})[ove_0_11(2)]
			end,
			DmgModAD = function(arg_842_0, arg_842_1)
				return ove_0_10.GetBonusAD(arg_842_1) * 1
			end
		},
		R = {
			SpellSlot = 3,
			DamageType = "Magic",
			Range = function()
				return 1000
			end,
			Delay = function()
				return 0
			end,
			Dmg = function()
				return ({
					150,
					250,
					350
				})[ove_0_11(3)]
			end,
			DmgModAD = function(arg_846_0, arg_846_1)
				return ove_0_10.GetTotalAP(arg_846_1) * 1
			end
		}
	},
	Heimerdinger = {
		Passive = {
			SpellSlot = -1
		},
		Q = {
			SpellSlot = 0,
			Range = function()
				return 350
			end,
			Delay = function()
				return 0.25
			end
		},
		W = {
			SpellSlot = 1,
			DamageType = "Magic",
			Range = function()
				return 1325
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_851_0, arg_851_1)
				return ({
					90,
					135,
					180,
					225,
					270
				})[ove_0_11(1)]
			end,
			DmgModAP = function(arg_852_0, arg_852_1)
				return ove_0_10.GetTotalAP(arg_852_1) * 0.93
			end
		},
		W2 = {
			SpellSlot = 3,
			DamageType = "Magic",
			Range = function()
				return 1325
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_855_0, arg_855_1)
				return ({
					503,
					697.5,
					892
				})[ove_0_11(3)]
			end,
			DmgModAP = function(arg_856_0, arg_856_1)
				return ove_0_10.GetTotalAP(arg_856_1) * 1.83
			end
		},
		E = {
			SpellSlot = 2,
			DamageType = "Magic",
			Range = function()
				return 970
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_859_0, arg_859_1)
				return ({
					60,
					100,
					140,
					180,
					220
				})[ove_0_11(2)]
			end,
			DmgModAD = function(arg_860_0, arg_860_1)
				return ove_0_10.GetTotalAP(arg_860_1) * 0.6
			end
		},
		E2 = {
			SpellSlot = 3,
			DamageType = "Magic",
			Range = function()
				return math.huge
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_863_0, arg_863_1)
				return ({
					100,
					200,
					300
				})[ove_0_11(3)]
			end,
			DmgModAD = function(arg_864_0, arg_864_1)
				return ove_0_10.GetTotalAP(arg_864_1) * 0.6
			end
		}
	},
	Illaoi = {
		Passive = {
			SpellSlot = -1
		},
		Q = {
			SpellSlot = 0,
			Range = function()
				return 800
			end,
			Delay = function()
				return 0.75
			end
		},
		W = {
			SpellSlot = 1,
			DamageType = "Physical",
			Range = function()
				return math.huge
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_869_0, arg_869_1)
				return ({
					20,
					30,
					40,
					50,
					60
				})[ove_0_11(1)]
			end
		},
		E = {
			SpellSlot = 2,
			DamageType = "True",
			Range = function()
				return 950
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_872_0, arg_872_1)
				return arg_872_1.health / 100 * ({
					25,
					30,
					35,
					40,
					45
				})[ove_0_11(2)]
			end,
			DmgModAP = function(arg_873_0, arg_873_1)
				return 8 * ((ove_0_10.GetTotalAD(arg_873_1) + ove_0_10.GetBonusAD(arg_873_1)) % 100)
			end
		},
		R = {
			SpellSlot = 3,
			DamageType = "Physical",
			Range = function()
				return 500
			end,
			Delay = function()
				return 0.5
			end,
			Dmg = function()
				return ({
					150,
					250,
					350
				})[ove_0_11(3)]
			end,
			DmgModAD = function(arg_877_0, arg_877_1)
				return ove_0_10.GetBonusAD(arg_877_1) * 0.5
			end
		}
	},
	Irelia = {
		Passive = {
			SpellSlot = -1,
			DamageType = "Magic",
			Dmg = function(arg_878_0, arg_878_1)
				return 7 + 3 * player.levelRef
			end,
			DmgModAD = function(arg_879_0, arg_879_1)
				return ove_0_10.GetBonusAD(arg_879_1) * 0.2
			end,
			DmgModADBase = function(arg_880_0, arg_880_1)
				return ove_0_10.GetTotalAD(arg_880_1)
			end
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Physical",
			Range = function()
				return 600
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_883_0, arg_883_1)
				return ({
					5,
					25,
					45,
					65,
					85
				})[ove_0_11(0)]
			end,
			DmgModAD = function(arg_884_0, arg_884_1)
				return (ove_0_10.GetTotalAD(arg_884_1) + ove_0_10.GetBonusAD(arg_884_1)) * 0.6
			end
		},
		W = {
			SpellSlot = 1,
			DamageType = "Physical",
			Range = function()
				return 775
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_887_0, arg_887_1)
				return ({
					30,
					75,
					120,
					165,
					210
				})[ove_0_11(1)]
			end,
			DmgModAD = function(arg_888_0, arg_888_1)
				return (ove_0_10.GetTotalAD(arg_888_1) + ove_0_10.GetBonusAD(arg_888_1)) * 1.2
			end,
			DmgModAP = function(arg_889_0, arg_889_1)
				return ove_0_10.GetTotalAP(arg_889_1) * 1.2
			end
		},
		E = {
			SpellSlot = 2,
			DamageType = "Magic",
			Range = function()
				return 775
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_892_0, arg_892_1)
				return ({
					80,
					125,
					170,
					215,
					260
				})[ove_0_11(2)]
			end,
			DmgModAP = function(arg_893_0, arg_893_1)
				return ove_0_10.GetTotalAP(arg_893_1) * 0.8
			end
		},
		R = {
			SpellSlot = 3,
			DamageType = "Physical",
			Range = function()
				return 1000
			end,
			Delay = function()
				return 0.4
			end,
			Dmg = function()
				return ({
					125,
					250,
					375
				})[ove_0_11(3)]
			end,
			DmgModAP = function(arg_897_0, arg_897_1)
				return ove_0_10.GetTotalAP(arg_897_1) * 0.7
			end
		}
	},
	Ivern = {
		Passive = {
			SpellSlot = -1
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Magic",
			Range = function()
				return 1100
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_900_0, arg_900_1)
				return ({
					80,
					125,
					170,
					215,
					260
				})[ove_0_11(0)]
			end,
			DmgModAD = function(arg_901_0, arg_901_1)
				return ove_0_10.GetTotalAP(arg_901_1) * 0.7
			end
		},
		W = {
			SpellSlot = 1,
			DamageType = "Physical",
			Range = function()
				return 1000
			end,
			Delay = function()
				return 0.25
			end,
			DmgModAP = function(arg_904_0, arg_904_1)
				return ({
					30,
					37.5,
					45,
					52.5,
					60
				})[ove_0_11(1)] + ove_0_10.GetTotalAP(arg_904_1) * 0.3
			end
		},
		E = {
			SpellSlot = 2,
			DamageType = "Magic",
			Range = function()
				return 750
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_907_0, arg_907_1)
				return ({
					70,
					90,
					110,
					130,
					150
				})[ove_0_11(2)]
			end,
			DmgModAP = function(arg_908_0, arg_908_1)
				return ove_0_10.GetTotalAP(arg_908_1) * 0.8
			end
		},
		R = {
			SpellSlot = 3,
			Range = function()
				return math.huge
			end,
			Delay = function()
				return 0.5
			end
		}
	},
	Janna = {
		Passive = {
			SpellSlot = -1
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Magic",
			Range = function()
				return 1760
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_913_0, arg_913_1)
				return ({
					105,
					145,
					185,
					225,
					265
				})[ove_0_11(0)]
			end,
			DmgModAD = function(arg_914_0, arg_914_1)
				return ove_0_10.GetTotalAP(arg_914_1) * 0.65
			end
		},
		W = {
			SpellSlot = 1,
			DamageType = "Magic",
			Range = function()
				return 650
			end,
			Delay = function()
				return 0.245
			end,
			DmgModAP = function(arg_917_0, arg_917_1)
				return ({
					70,
					100,
					130,
					160,
					190
				})[ove_0_11(1)]
			end,
			DmgModAP = function(arg_918_0, arg_918_1)
				return ove_0_10.GetTotalAP(arg_918_1) * 0.5
			end
		},
		E = {
			SpellSlot = 2,
			Range = function()
				return 800
			end,
			Delay = function()
				return 0
			end
		},
		R = {
			SpellSlot = 3,
			Range = function()
				return 700
			end,
			Delay = function()
				return 0
			end
		}
	},
	JarvanIV = {
		Passive = {
			SpellSlot = -1,
			DamageType = "Physical",
			Dmg = function(arg_923_0, arg_923_1)
				return arg_923_1.health / 100 * 8
			end
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Physical",
			Range = function()
				return 785
			end,
			Delay = function()
				return 0.4
			end,
			Dmg = function(arg_926_0, arg_926_1)
				return ({
					90,
					130,
					170,
					210,
					250
				})[ove_0_11(0)]
			end,
			DmgModAD = function(arg_927_0, arg_927_1)
				return ove_0_10.GetBonusAD(arg_927_1) * 1.4
			end
		},
		W = {
			SpellSlot = 1,
			Range = function()
				return 600
			end,
			Delay = function()
				return 0
			end
		},
		E = {
			SpellSlot = 2,
			DamageType = "Magic",
			Range = function()
				return 860
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_932_0, arg_932_1)
				return ({
					80,
					120,
					160,
					200,
					240
				})[ove_0_11(2)]
			end,
			DmgModAP = function(arg_933_0, arg_933_1)
				return ove_0_10.GetTotalAP(arg_933_1) * 0.8
			end
		},
		R = {
			SpellSlot = 3,
			DamageType = "Physical",
			Range = function()
				return 650
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_936_0, arg_936_1)
				return ({
					200,
					325,
					450
				})[ove_0_11(3)]
			end,
			DmgModAD = function(arg_937_0, arg_937_1)
				return ove_0_10.GetBonusAD(arg_937_1) * 1.8
			end
		}
	},
	Jax = {
		Passive = {
			SpellSlot = -1
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Physical",
			Range = function()
				return 700
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_940_0, arg_940_1)
				return ({
					65,
					105,
					145,
					185,
					225
				})[ove_0_11(0)]
			end,
			DmgModAD = function(arg_941_0, arg_941_1)
				return ove_0_10.GetBonusAD(arg_941_1) * 1
			end,
			DmgModAP = function(arg_942_0, arg_942_1)
				return ove_0_10.GetTotalAP(arg_942_1) * 0.6
			end
		},
		W = {
			SpellSlot = 1,
			DamageType = "Magic",
			Range = function()
				return math.huge
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_945_0, arg_945_1)
				return ({
					50,
					85,
					120,
					155,
					190
				})[ove_0_11(1)]
			end,
			DmgModAP = function(arg_946_0, arg_946_1)
				return ove_0_10.GetTotalAP(arg_946_1) * 0.6
			end
		},
		E = {
			SpellSlot = 2,
			DamageType = "Physical",
			Range = function()
				return 300
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_949_0, arg_949_1)
				return ({
					110,
					160,
					210,
					260,
					310
				})[ove_0_11(2)]
			end,
			DmgModAD = function(arg_950_0, arg_950_1)
				return ove_0_10.GetBonusAD(arg_950_1) * 1
			end
		},
		R = {
			SpellSlot = 3,
			DamageType = "Magic",
			Range = function()
				return math.huge
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_953_0, arg_953_1)
				return ({
					100,
					140,
					180
				})[ove_0_11(3)]
			end,
			DmgModAD = function(arg_954_0, arg_954_1)
				return ove_0_10.GetTotalAP(arg_954_1) * 0.7
			end
		}
	},
	Jayce = {
		Passive = {
			SpellSlot = -1
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Physical",
			Range = function()
				return 600
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_957_0, arg_957_1)
				return ({
					55,
					100,
					145,
					190,
					235,
					280
				})[ove_0_11(0)]
			end,
			DmgModAD = function(arg_958_0, arg_958_1)
				return ove_0_10.GetBonusAD(arg_958_1) * 1.2
			end
		},
		Q2 = {
			SpellSlot = 0,
			DamageType = "Physical",
			Range = function()
				return 1050
			end,
			Delay = function()
				return 0.2143
			end,
			Dmg = function(arg_961_0, arg_961_1)
				return ({
					55,
					110,
					165,
					220,
					275,
					330
				})[ove_0_11(0)]
			end,
			DmgModAD = function(arg_962_0, arg_962_1)
				return ove_0_10.GetBonusAD(arg_962_1) * 1.2
			end
		},
		W = {
			SpellSlot = 1,
			DamageType = "Magic",
			Range = function()
				return 350
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_965_0, arg_965_1)
				return ({
					100,
					160,
					220,
					280,
					340,
					400
				})[ove_0_11(1)]
			end,
			DmgModAP = function(arg_966_0, arg_966_1)
				return ove_0_10.GetTotalAP(arg_966_1) * 1
			end
		},
		W2 = {
			SpellSlot = 1,
			DamageType = "Magic",
			Range = function()
				return math.huge
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_969_0, arg_969_1)
				return ({
					100,
					160,
					220,
					280,
					340,
					400
				})[ove_0_11(1)]
			end,
			DmgModAP = function(arg_970_0, arg_970_1)
				return ove_0_10.GetTotalAP(arg_970_1) * 1
			end
		},
		E = {
			SpellSlot = 2,
			DamageType = "Magic",
			Range = function()
				return 240
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_973_0, arg_973_1)
				return arg_973_1.maxHealth / 100 * ({
					8,
					10.8,
					13.6,
					16.4,
					19.2,
					22
				})[ove_0_11(2)]
			end,
			DmgModAD = function(arg_974_0, arg_974_1)
				return ove_0_10.GetBonusAD(arg_974_1) * 1
			end
		},
		E2 = {
			SpellSlot = 2,
			DamageType = "Magic",
			Range = function()
				return 650
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_977_0, arg_977_1)
				return arg_977_1.maxHealth / 100 * ({
					8,
					10.8,
					13.6,
					16.4,
					19.2,
					22
				})[ove_0_11(2)]
			end,
			DmgModAD = function(arg_978_0, arg_978_1)
				return ove_0_10.GetBonusAD(arg_978_1) * 1
			end
		},
		R = {
			SpellSlot = 3,
			Range = function()
				return math.huge
			end,
			Delay = function()
				return 0
			end
		}
	},
	Jhin = {
		Passive = {
			SpellSlot = -1
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Physical",
			Range = function()
				return 550
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_983_0, arg_983_1)
				return ({
					45,
					70,
					95,
					120,
					145
				})[ove_0_11(0)]
			end,
			DmgModAD = function(arg_984_0, arg_984_1)
				return (ove_0_10.GetTotalAD(arg_984_1) + ove_0_10.GetBonusAD(arg_984_1)) * ({
					0.35,
					0.425,
					0.5,
					0.575,
					0.65
				})[ove_0_11(0)]
			end,
			DmgModAP = function(arg_985_0, arg_985_1)
				return ove_0_10.GetTotalAP(arg_985_1) * 0.6
			end
		},
		W = {
			SpellSlot = 1,
			DamageType = "Physical",
			Range = function()
				return 2520
			end,
			Delay = function()
				return 0.75
			end,
			Dmg = function(arg_988_0, arg_988_1)
				return ({
					60,
					95,
					130,
					165,
					200
				})[ove_0_11(1)]
			end,
			DmgModAD = function(arg_989_0, arg_989_1)
				return (ove_0_10.GetTotalAD(arg_989_1) + ove_0_10.GetBonusAD(arg_989_1)) * 0.5
			end
		},
		E = {
			SpellSlot = 2,
			DamageType = "Magic",
			Range = function()
				return 750
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_992_0, arg_992_1)
				return ({
					20,
					80,
					140,
					200,
					260
				})[ove_0_11(2)]
			end,
			DmgModAD = function(arg_993_0, arg_993_1)
				return (ove_0_10.GetTotalAD(arg_993_1) + ove_0_10.GetBonusAD(arg_993_1)) * 1.2
			end,
			DmgModAP = function(arg_994_0, arg_994_1)
				return ove_0_10.GetTotalAP(arg_994_1) * 1
			end
		},
		R = {
			SpellSlot = 3,
			DamageType = "Physical",
			Range = function()
				return 3500
			end,
			Delay = function()
				return 1
			end,
			Dmg = function(arg_997_0, arg_997_1)
				return ({
					200,
					500,
					800
				})[ove_0_11(3)]
			end,
			DmgModAD = function(arg_998_0, arg_998_1)
				return (ove_0_10.GetTotalAD(arg_998_1) + ove_0_10.GetBonusAD(arg_998_1)) * 1
			end
		}
	},
	Jinx = {
		Passive = {
			SpellSlot = -1
		},
		Q = {
			SpellSlot = 0,
			Range = function()
				return 250
			end,
			Delay = function()
				return 0
			end
		},
		W = {
			SpellSlot = 1,
			DamageType = "Physical",
			Range = function()
				return 1440
			end,
			Delay = function()
				return 0.6 - 0.02 * ((Target.AttackSpeed - 0.625) % 25)
			end,
			Dmg = function(arg_1003_0, arg_1003_1)
				return ({
					10,
					60,
					110,
					160,
					210
				})[ove_0_11(1)]
			end,
			DmgModAD = function(arg_1004_0, arg_1004_1)
				return (ove_0_10.GetTotalAD(arg_1004_1) + ove_0_10.GetBonusAD(arg_1004_1)) * 1.6
			end
		},
		E = {
			SpellSlot = 2,
			DamageType = "Magic",
			Range = function()
				return 925
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_1007_0, arg_1007_1)
				return ({
					70,
					120,
					170,
					220,
					270
				})[ove_0_11(2)]
			end,
			DmgModAP = function(arg_1008_0, arg_1008_1)
				return ove_0_10.GetTotalAP(arg_1008_1) * 1
			end
		},
		R = {
			SpellSlot = 3,
			DamageType = "Magic",
			Range = function()
				return math.huge
			end,
			Delay = function()
				return 0.6
			end,
			Dmg = function()
				return ({
					250,
					400,
					550
				})[ove_0_11(3)]
			end,
			DmgModAD = function(arg_1012_0, arg_1012_1)
				return ove_0_10.GetBonusAD(arg_1012_1) * 1.5 + (T - arg_1012_1.health) / 100 * ({
					25,
					30,
					35
				})[ove_0_11(3)]
			end
		}
	},
	Kaisa = {
		Passive = {
			SpellSlot = -1
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Physical",
			Range = function()
				return 600
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_1015_0, arg_1015_1)
				return ({
					40,
					55,
					70,
					85,
					100
				})[ove_0_11(0)]
			end,
			DmgModAD = function(arg_1016_0, arg_1016_1)
				return ove_0_10.GetBonusAD(arg_1016_1) * 0.5
			end,
			DmgModAP = function(arg_1017_0, arg_1017_1)
				return ove_0_10.GetTotalAP(arg_1017_1) * 0.3
			end
		},
		W = {
			SpellSlot = 1,
			DamageType = "Magic",
			Range = function()
				return 3000
			end,
			Delay = function()
				return 0.8
			end,
			Dmg = function(arg_1020_0, arg_1020_1)
				return ({
					30,
					55,
					80,
					105,
					130
				})[ove_0_11(1)]
			end,
			DmgModAD = function(arg_1021_0, arg_1021_1)
				return (ove_0_10.GetTotalAD(arg_1021_1) + ove_0_10.GetBonusAD(arg_1021_1)) * 1.3
			end,
			DmgModAP = function(arg_1022_0, arg_1022_1)
				return ove_0_10.GetTotalAP(arg_1022_1) * 0.45
			end
		},
		E = {
			SpellSlot = 2,
			Range = function()
				return 925
			end,
			Delay = function()
				return 0.4
			end
		},
		R = {
			SpellSlot = 3,
			Range = function()
				return ({
					1500,
					2250,
					3000
				})[ove_0_11(1)]
			end,
			Delay = function()
				return 0
			end
		}
	},
	Kalista = {
		Passive = {
			SpellSlot = -1
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Physical",
			Range = function()
				return 1200
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_1029_0, arg_1029_1)
				return ({
					20,
					85,
					150,
					215,
					280
				})[ove_0_11(0)]
			end,
			DmgModAD = function(arg_1030_0, arg_1030_1)
				return (ove_0_10.GetTotalAD(arg_1030_1) + ove_0_10.GetBonusAD(arg_1030_1)) * 1
			end
		},
		W = {
			SpellSlot = 1,
			DamageType = "Magic",
			Range = function()
				return 5000
			end,
			Delay = function()
				return 0.5
			end,
			DmgModAP = function(arg_1033_0, arg_1033_1)
				return arg_1033_1.maxHealth / 100 * ({
					14,
					15,
					16,
					17,
					18
				})[ove_0_11(1)]
			end
		},
		E = {
			SpellSlot = 2,
			DamageType = "Physical",
			Range = function()
				return 1100
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_1036_0, arg_1036_1)
				return ({
					20,
					30,
					40,
					50,
					60
				})[ove_0_11(2)]
			end,
			DmgModAD = function(arg_1037_0, arg_1037_1)
				return (ove_0_10.GetTotalAD(arg_1037_1) + ove_0_10.GetBonusAD(arg_1037_1)) * 0.7
			end
		},
		R = {
			SpellSlot = 3,
			Range = function()
				return 1200
			end,
			Delay = function()
				return 0
			end
		}
	},
	Karma = {
		Passive = {
			SpellSlot = -1
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Magic",
			Range = function()
				return 890
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_1042_0, arg_1042_1)
				return ({
					70,
					120,
					170,
					220,
					270
				})[ove_0_11(0)]
			end,
			DmgModAP = function(arg_1043_0, arg_1043_1)
				return ove_0_10.GetTotalAP(arg_1043_1) * 0.4
			end
		},
		Q2 = {
			SpellSlot = 0,
			DamageType = "Magic",
			Range = function()
				return 950
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_1046_0, arg_1046_1)
				return ({
					70,
					120,
					170,
					220,
					270
				})[ove_0_11(0)]
			end,
			DmgModAP = function(arg_1047_0, arg_1047_1)
				return ({
					75,
					240,
					405,
					570
				})[ove_0_11(3)] + ove_0_10.GetTotalAP(arg_1047_1) * 1.3
			end
		},
		W = {
			SpellSlot = 1,
			DamageType = "Magic",
			Range = function()
				return 675
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_1050_0, arg_1050_1)
				return ({
					80,
					130,
					180,
					230,
					280
				})[ove_0_11(1)]
			end,
			DmgModAP = function(arg_1051_0, arg_1051_1)
				return ove_0_10.GetTotalAP(arg_1051_1) * 0.9
			end
		},
		E = {
			SpellSlot = 2,
			Range = function()
				return 800
			end,
			Delay = function()
				return 0
			end
		},
		R = {
			SpellSlot = 3,
			Range = function()
				return math.huge
			end,
			Delay = function()
				return 0
			end
		}
	},
	Karthus = {
		Passive = {
			SpellSlot = -1
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Magic",
			Range = function()
				return 875
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_1058_0, arg_1058_1)
				return ({
					90,
					125,
					160,
					195,
					230
				})[ove_0_11(0)]
			end,
			DmgModAP = function(arg_1059_0, arg_1059_1)
				return ove_0_10.GetTotalAP(arg_1059_1) * 0.7
			end
		},
		W = {
			SpellSlot = 1,
			Range = function()
				return 1000
			end,
			Delay = function()
				return 0.25
			end
		},
		E = {
			SpellSlot = 2,
			DamageType = "Magic",
			Range = function()
				return 550
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_1064_0, arg_1064_1)
				return ({
					30,
					50,
					70,
					90,
					110
				})[ove_0_11(2)]
			end,
			DmgModAP = function(arg_1065_0, arg_1065_1)
				return ove_0_10.GetTotalAP(arg_1065_1) * 0.2
			end
		},
		R = {
			SpellSlot = 3,
			DamageType = "Magic",
			Range = function()
				return math.huge
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_1068_0, arg_1068_1)
				return ({
					200,
					350,
					500
				})[ove_0_11(3)]
			end,
			DmgModAP = function(arg_1069_0, arg_1069_1)
				return ove_0_10.GetTotalAP(arg_1069_1) * 0.75
			end
		}
	},
	Kassadin = {
		Passive = {
			SpellSlot = -1
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Magic",
			Range = function()
				return 650
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_1072_0, arg_1072_1)
				return ({
					65,
					95,
					125,
					155,
					185
				})[ove_0_11(0)]
			end,
			DmgModAP = function(arg_1073_0, arg_1073_1)
				return ove_0_10.GetTotalAP(arg_1073_1) * 0.7
			end
		},
		W = {
			SpellSlot = 1,
			DamageType = "Magic",
			Range = function()
				return math.huge
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_1076_0, arg_1076_1)
				return ({
					50,
					75,
					100,
					125,
					150
				})[ove_0_11(1)]
			end,
			DmgModAP = function(arg_1077_0, arg_1077_1)
				return ove_0_10.GetTotalAP(arg_1077_1) * 0.8
			end
		},
		E = {
			SpellSlot = 2,
			DamageType = "Magic",
			Range = function()
				return 600
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_1080_0, arg_1080_1)
				return ({
					80,
					105,
					130,
					155,
					180
				})[ove_0_11(2)]
			end,
			DmgModAP = function(arg_1081_0, arg_1081_1)
				return ove_0_10.GetTotalAP(arg_1081_1) * 0.85
			end
		},
		R = {
			SpellSlot = 3,
			DamageType = "Magic",
			Range = function()
				return 500
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_1084_0, arg_1084_1)
				return ({
					80,
					100,
					120
				})[ove_0_11(3)]
			end,
			DmgModAP = function(arg_1085_0, arg_1085_1)
				return ove_0_10.GetTotalAP(arg_1085_1) * 0.4
			end,
			DmgModMP = function(arg_1086_0, arg_1086_1)
				return arg_1086_1.MaxMana / 100 * 2
			end
		}
	},
	Katarina = {
		Passive = {
			SpellSlot = -1,
			DamageType = "Magic",
			Dmg = function(arg_1087_0, arg_1087_1)
				return ({
					68,
					72,
					77,
					82,
					89,
					96,
					103,
					112,
					121,
					131,
					142,
					154,
					166,
					180,
					194,
					208,
					224,
					240
				})[player.levelRef]
			end,
			DmgModAD = function(arg_1088_0, arg_1088_1)
				return ove_0_10.GetBonusAD(arg_1088_1) * 0.6
			end
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Magic",
			Range = function()
				return 625
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_1091_0, arg_1091_1)
				return ({
					80,
					110,
					140,
					170,
					200
				})[ove_0_11(0)]
			end,
			DmgModAP = function(arg_1092_0, arg_1092_1)
				return ove_0_10.GetTotalAP(arg_1092_1) * 0.35
			end
		},
		W = {
			SpellSlot = 1,
			Range = function()
				return math.huge
			end,
			Delay = function()
				return 0
			end
		},
		E = {
			SpellSlot = 2,
			DamageType = "Magic",
			Range = function()
				return 725
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_1097_0, arg_1097_1)
				return ({
					20,
					35,
					50,
					65,
					80
				})[ove_0_11(2)]
			end,
			DmgModAD = function(arg_1098_0, arg_1098_1)
				return (ove_0_10.GetTotalAD(arg_1098_1) + ove_0_10.GetBonusAD(arg_1098_1)) * 0.4
			end,
			DmgModAP = function(arg_1099_0, arg_1099_1)
				return ove_0_10.GetTotalAP(arg_1099_1) * 0.25
			end
		},
		R = {
			SpellSlot = 3,
			DamageType = "Magic",
			Range = function()
				return 550
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_1102_0, arg_1102_1)
				return ({
					375,
					562.5,
					750
				})[ove_0_11(3)]
			end,
			DmgModAD = function(arg_1103_0, arg_1103_1)
				return ove_0_10.GetBonusAD(arg_1103_1) * 2.7
			end,
			DmgModAP = function(arg_1104_0, arg_1104_1)
				return ove_0_10.GetTotalAP(arg_1104_1) * 2.85
			end
		}
	},
	Kayle = {
		Passive = {
			SpellSlot = -1,
			DamageType = "Magic",
			Dmg = function(arg_1105_0, arg_1105_1)
				return ({
					15,
					20,
					25,
					30,
					35
				})[ove_0_11(2)]
			end,
			DmgModAD = function(arg_1106_0, arg_1106_1)
				return ove_0_10.GetBonusAD(arg_1106_1) * 0.1
			end,
			DmgModAP = function(arg_1107_0, arg_1107_1)
				return ove_0_10.GetTotalAP(arg_1107_1) * 0.25
			end
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Magic",
			Range = function()
				return 900
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_1110_0, arg_1110_1)
				return ({
					60,
					100,
					140,
					180,
					220
				})[ove_0_11(0)]
			end,
			DmgModAD = function(arg_1111_0, arg_1111_1)
				return ove_0_10.GetBonusAD(arg_1111_1) * 0.6
			end,
			DmgModAP = function(arg_1112_0, arg_1112_1)
				return ove_0_10.GetTotalAP(arg_1112_1) * 0.5
			end
		},
		W = {
			SpellSlot = 1,
			Range = function()
				return 900
			end,
			Delay = function()
				return 0.25
			end
		},
		E = {
			SpellSlot = 2,
			DamageType = "Magic",
			Range = function()
				return 350
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_1117_0, arg_1117_1)
				return ({
					15,
					20,
					25,
					30,
					35
				})[ove_0_11(2)] + (T - arg_1117_1.health) / 100 * (({
					8,
					9,
					10,
					11,
					12
				})[ove_0_11(2)] * 0.02 + ove_0_10.GetTotalAP(arg_1117_1) % 100)
			end,
			DmgModAD = function(arg_1118_0, arg_1118_1)
				return ove_0_10.GetBonusAD(arg_1118_1) * 0.1
			end,
			DmgModAP = function(arg_1119_0, arg_1119_1)
				return ove_0_10.GetTotalAP(arg_1119_1) * 0.2
			end
		},
		R = {
			SpellSlot = 3,
			DamageType = "Magic",
			Range = function()
				return 900
			end,
			Delay = function()
				return 1.5
			end,
			Dmg = function(arg_1122_0, arg_1122_1)
				return ({
					200,
					350,
					500
				})[ove_0_11(3)]
			end,
			DmgModAD = function(arg_1123_0, arg_1123_1)
				return ove_0_10.GetBonusAD(arg_1123_1) * 1
			end,
			DmgModAP = function(arg_1124_0, arg_1124_1)
				return ove_0_10.GetTotalAP(arg_1124_1) * 0.8
			end
		}
	},
	Kayn = {
		Passive = {
			SpellSlot = -1,
			DamageType = "Magic",
			Dmg = function(arg_1125_0, arg_1125_1)
				return 0.08 + 0.012941176470588235 * (player.levelRef - 1)
			end
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Physical",
			Range = function()
				return 350
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_1128_0, arg_1128_1)
				return ({
					150,
					190,
					230,
					270,
					310
				})[ove_0_11(0)]
			end,
			DmgModAD = function(arg_1129_0, arg_1129_1)
				return ove_0_10.GetBonusAD(arg_1129_1) * 1.3
			end
		},
		W = {
			SpellSlot = 1,
			DamageType = "Physical",
			Range = function()
				return 700
			end,
			Delay = function()
				return 0.55
			end,
			Dmg = function(arg_1132_0, arg_1132_1)
				return ({
					90,
					135,
					180,
					225,
					270
				})[ove_0_11(1)]
			end,
			DmgModAD = function(arg_1133_0, arg_1133_1)
				return ove_0_10.GetBonusAD(arg_1133_1) * 1.3
			end
		},
		W2 = {
			SpellSlot = 1,
			DamageType = "Physical",
			Range = function()
				return 900
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_1136_0, arg_1136_1)
				return ({
					90,
					135,
					180,
					225,
					270
				})[ove_0_11(1)]
			end,
			DmgModAD = function(arg_1137_0, arg_1137_1)
				return ove_0_10.GetBonusAD(arg_1137_1) * 1.3
			end
		},
		E = {
			SpellSlot = 2,
			Range = function()
				return math.huge
			end,
			Delay = function()
				return 0
			end
		},
		R = {
			SpellSlot = 3,
			DamageType = "Physical",
			Range = function()
				return 550
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_1142_0, arg_1142_1)
				return ({
					150,
					250,
					350
				})[ove_0_11(3)]
			end,
			DmgModAD = function(arg_1143_0, arg_1143_1)
				return ove_0_10.GetBonusAD(arg_1143_1) * 1.75
			end
		},
		R2 = {
			SpellSlot = 3,
			DamageType = "Physical",
			Range = function()
				return 750
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_1146_0, arg_1146_1)
				return ({
					150,
					250,
					350
				})[ove_0_11(3)]
			end,
			DmgModAD = function(arg_1147_0, arg_1147_1)
				return ove_0_10.GetBonusAD(arg_1147_1) * 1.75
			end
		}
	},
	Kennen = {
		Passive = {
			SpellSlot = -1
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Magic",
			Range = function()
				return 1050
			end,
			Delay = function()
				return 0.175
			end,
			Dmg = function(arg_1150_0, arg_1150_1)
				return ({
					75,
					120,
					165,
					210,
					255
				})[ove_0_11(0)]
			end,
			DmgModAP = function(arg_1151_0, arg_1151_1)
				return ove_0_10.GetTotalAP(arg_1151_1) * 0.75
			end
		},
		W = {
			SpellSlot = 1,
			DamageType = "Magic",
			Range = function()
				return 750
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_1154_0, arg_1154_1)
				return ({
					70,
					95,
					120,
					145,
					170
				})[ove_0_11(1)]
			end,
			DmgModAP = function(arg_1155_0, arg_1155_1)
				return ove_0_10.GetTotalAP(arg_1155_1) * 0.8
			end
		},
		E = {
			SpellSlot = 2,
			DamageType = "Magic",
			Range = function()
				return math.huge
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_1158_0, arg_1158_1)
				return ({
					80,
					120,
					160,
					200,
					240
				})[ove_0_11(2)]
			end,
			DmgModAP = function(arg_1159_0, arg_1159_1)
				return ove_0_10.GetTotalAP(arg_1159_1) * 0.8
			end
		},
		R = {
			SpellSlot = 3,
			DamageType = "Magic",
			Range = function()
				return 550
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_1162_0, arg_1162_1)
				return ({
					300,
					562.5,
					825
				})[ove_0_11(3)]
			end,
			DmgModAP = function(arg_1163_0, arg_1163_1)
				return ove_0_10.GetTotalAP(arg_1163_1) * 1.6875
			end
		}
	},
	KhaZix = {
		Passive = {
			SpellSlot = -1,
			DamageType = "Magic",
			Dmg = function(arg_1164_0, arg_1164_1)
				return 8 + 6 * player.levelRef
			end,
			DmgModAD = function(arg_1165_0, arg_1165_1)
				return ove_0_10.GetBonusAD(arg_1165_1) * 0.4
			end,
			DmgModADBase = function(arg_1166_0, arg_1166_1)
				return ove_0_10.GetTotalAD(arg_1166_1)
			end
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Physical",
			Range = function()
				return 325
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_1169_0, arg_1169_1)
				return ({
					60,
					85,
					110,
					135,
					160
				})[ove_0_11(0)]
			end,
			DmgModAD = function(arg_1170_0, arg_1170_1)
				return ove_0_10.GetBonusAD(arg_1170_1) * 1.15
			end
		},
		W = {
			SpellSlot = 1,
			DamageType = "Physical",
			Range = function()
				return 1025
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_1173_0, arg_1173_1)
				return ({
					85,
					115,
					145,
					175,
					205
				})[ove_0_11(1)]
			end,
			DmgModAD = function(arg_1174_0, arg_1174_1)
				return ove_0_10.GetTotalAP(arg_1174_1) * 1
			end
		},
		E = {
			SpellSlot = 2,
			DamageType = "Magic",
			Range = function()
				return 700
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_1177_0, arg_1177_1)
				return ({
					65,
					100,
					135,
					170,
					205
				})[ove_0_11(2)]
			end,
			DmgModAP = function(arg_1178_0, arg_1178_1)
				return ove_0_10.GetBonusAD(arg_1178_1) * 0.2
			end
		},
		R = {
			SpellSlot = 3,
			Range = function()
				return math.huge
			end,
			Delay = function()
				return 0
			end
		}
	},
	Kindred = {
		Passive = {
			SpellSlot = -1
		},
		Q = {
			DamageType = "Physical",
			Dmg = function()
				return ({
					60,
					85,
					110,
					135,
					160
				})[ove_0_11(0)]
			end,
			DmgModAD = function()
				return 0.75
			end,
			DmgModADBase = function(arg_1183_0, arg_1183_1)
				return ove_0_10.GetBonusAD(arg_1183_1)
			end,
			Range = function()
				return 340
			end,
			Width = function(arg_1185_0, arg_1185_1)
				return arg_1185_1.AttackRange + arg_1185_1.CharData.BoundingRadius
			end,
			Speed = function()
				return 500
			end,
			Delay = function()
				return 0
			end
		},
		W = {
			DamageType = "Magic",
			Range = function()
				return 500
			end,
			Width = function()
				return 800
			end,
			Speed = function()
				return 1400
			end,
			Delay = function()
				return 0
			end,
			Dmg = function()
				return ({
					25,
					30,
					35,
					40,
					45
				})[ove_0_11(1)]
			end,
			DmgModAD = function()
				return 0.2
			end,
			DmgModADBase = function(arg_1194_0, arg_1194_1)
				return ove_0_10.GetBonusAD(arg_1194_1)
			end,
			DmgModHP = function(arg_1195_0, arg_1195_1)
				return 0.015 + (player.buff.kindredmarkofthekindredstackcounter and player.buff.kindredmarkofthekindredstackcounter.stacks2 or 1) * 0.01
			end,
			DmgModHPBase = function(arg_1196_0, arg_1196_1)
				return arg_1196_1.health
			end
		},
		E = {
			DamageType = "Physical",
			Range = function(arg_1197_0)
				return ove_0_10.GetAARange(arg_1197_0, player)
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function()
				return ({
					80,
					100,
					120,
					140,
					160
				})[ove_0_11(2)]
			end,
			DmgModAD = function()
				return 0.8
			end,
			DmgModADBase = function(arg_1201_0, arg_1201_1)
				return ove_0_10.GetBonusAD(arg_1201_1)
			end,
			DmgModHP = function()
				return 0.08 + 0.005 * (player.buff.kindredmarkofthekindredstackcounter and player.buff.kindredmarkofthekindredstackcounter.stacks2 or 1)
			end,
			DmgModHPBase = function(arg_1203_0, arg_1203_1)
				return arg_1203_1.maxHealth - arg_1203_1.health
			end
		},
		EWolf = {
			DamageType = "Physical",
			DmgModAD = function()
				return 0.5
			end,
			DmgModADBase = function(arg_1205_0, arg_1205_1)
				return ove_0_10.GetBonusAD(arg_1205_1)
			end
		},
		EWolfInfinityEdge = {
			DamageType = "Physical",
			DmgModAD = function()
				return 0.525
			end,
			DmgModADBase = function(arg_1207_0, arg_1207_1)
				return ove_0_10.GetBonusAD(arg_1207_1)
			end
		}
	},
	Kled = {
		Passive = {
			SpellSlot = -1
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Physical",
			Range = function()
				return 800
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_1210_0, arg_1210_1)
				return ({
					90,
					165,
					240,
					315,
					390
				})[ove_0_11(0)]
			end,
			DmgModAD = function(arg_1211_0, arg_1211_1)
				return ove_0_10.GetBonusAD(arg_1211_1) * 1.95
			end
		},
		W = {
			SpellSlot = 1,
			Range = function()
				return math.huge
			end,
			Delay = function()
				return 0
			end
		},
		E = {
			SpellSlot = 2,
			DamageType = "Physical",
			Range = function()
				return 550
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_1216_0, arg_1216_1)
				return ({
					70,
					120,
					170,
					220,
					270
				})[ove_0_11(2)]
			end,
			DmgModAP = function(arg_1217_0, arg_1217_1)
				return ove_0_10.GetBonusAD(arg_1217_1) * 1.3
			end
		},
		R = {
			SpellSlot = 3,
			DamageType = "Physical",
			Range = function()
				return ({
					3500,
					4000,
					4500
				})[ove_0_11(3)]
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_1220_0, arg_1220_1)
				return arg_1220_1.maxHealth / 100 * ({
					12,
					15,
					18
				})[ove_0_11(3)]
			end,
			DmgModAD = function(arg_1221_0, arg_1221_1)
				return 12 * (ove_0_10.GetBonusAD(arg_1221_1) % 100)
			end
		}
	},
	KogMaw = {
		Passive = {
			DamageType = "True",
			Dmg = function()
				return 100 + 25 * player.levelRef
			end,
			Width = function()
				return 400
			end
		},
		Q = {
			DamageType = "Magic",
			Dmg = function()
				return ({
					90,
					140,
					190,
					240,
					290
				})[ove_0_11(0)]
			end,
			DmgModAP = function()
				return 0.7
			end,
			DmgModAPBase = function(arg_1226_0, arg_1226_1)
				return ove_0_10.GetTotalAP(arg_1226_1)
			end,
			DmgModResistanceMod = function()
				return ({
					0.23,
					0.25,
					0.27,
					0.29,
					0.31
				})[ove_0_11(0)]
			end,
			Range = function()
				return 1200
			end,
			Width = function()
				return 140
			end,
			Speed = function()
				return 1800
			end,
			Delay = function()
				return 0.25
			end
		},
		W = {
			DamageType = "Magic",
			Delay = function()
				return 0
			end,
			DmgModHP = function(arg_1233_0, arg_1233_1)
				return ({
					0.035,
					0.0425,
					0.05,
					0.0575,
					0.065
				})[ove_0_11(1)] + math.floor(ove_0_10.GetTotalAP(arg_1233_1) / 100)
			end,
			DmgModHPBase = function(arg_1234_0, arg_1234_1)
				return arg_1234_1.MaxHealth
			end,
			Range = function(arg_1235_0, arg_1235_1)
				return ({
					130,
					150,
					170,
					190,
					210
				})[ove_0_11(1)] + myHero.AttackRange + myHero.CharData.BoundingRadius
			end
		},
		E = {
			DamageType = "Magic",
			Dmg = function()
				return ({
					75,
					120,
					165,
					210,
					255
				})[ove_0_11(2)]
			end,
			DmgModAP = function()
				return 0.5
			end,
			DmgModAPBase = function(arg_1238_0, arg_1238_1)
				return ove_0_10.GetTotalAP(arg_1238_1)
			end,
			Range = function()
				return 1360
			end,
			Width = function()
				return 240
			end,
			Speed = function()
				return 1600
			end,
			Delay = function()
				return 0.25
			end
		},
		R = {
			SpellSlot = 3,
			DamageType = "Magic",
			Range = function()
				return ({
					1300,
					1550,
					1800
				})[ove_0_11(3)]
			end,
			Width = function()
				return 240
			end,
			Delay = function()
				return 0.9
			end,
			Speed = function()
				return math.huge
			end,
			ManaCost = function(arg_1247_0, arg_1247_1)
				if arg_1247_1 > 10 then
					arg_1247_1 = 10
				end

				return arg_1247_1 * 40
			end,
			Dmg = function()
				return ({
					100,
					140,
					180
				})[ove_0_11(3)]
			end,
			DmgModAD = function()
				return 0.65
			end,
			DmgModADBase = function(arg_1250_0, arg_1250_1)
				if arg_1250_1 ~= nil then
					return ove_0_10.GetBonusAD(arg_1250_1)
				else
					return 0
				end
			end,
			DmgModAP = function()
				return 0.35
			end,
			DmgModAPBase = function(arg_1252_0, arg_1252_1)
				if arg_1252_1 ~= nil then
					return ove_0_10.GetTotalAP(arg_1252_1)
				else
					return 0
				end
			end,
			DmgModHP = function(arg_1253_0, arg_1253_1)
				local slot_1253_0 = 1 - arg_1253_1.health / T

				if slot_1253_0 <= 0.6 then
					return 0.00833 * (slot_1253_0 * 100)
				else
					return 1
				end
			end,
			DmgModHPBase = function(arg_1254_0)
				return arg_1254_0:Dmg() + arg_1254_0:DmgModAD() * arg_1254_0:DmgModADBase() + arg_1254_0:DmgModAP() * arg_1254_0:DmgModAPBase()
			end
		}
	},
	LeBlanc = {
		Passive = {
			SpellSlot = -1
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Magic",
			Range = function()
				return 700
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_1257_0, arg_1257_1)
				return ({
					130,
					180,
					230,
					280,
					330
				})[ove_0_11(0)]
			end,
			DmgModAD = function(arg_1258_0, arg_1258_1)
				return ove_0_10.GetTotalAP(arg_1258_1) * 0.8
			end
		},
		W = {
			SpellSlot = 1,
			DamageType = "Magic",
			Range = function()
				return 600
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_1261_0, arg_1261_1)
				return ({
					75,
					115,
					155,
					195,
					235
				})[ove_0_11(1)]
			end,
			DmgModAD = function(arg_1262_0, arg_1262_1)
				return ove_0_10.GetTotalAP(arg_1262_1) * 0.6
			end
		},
		E = {
			SpellSlot = 2,
			DamageType = "Magic",
			Range = function()
				return 950
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_1265_0, arg_1265_1)
				return ({
					130,
					190,
					250,
					310,
					370
				})[ove_0_11(2)]
			end,
			DmgModAP = function(arg_1266_0, arg_1266_1)
				return ove_0_10.GetTotalAP(arg_1266_1) * 1
			end
		},
		R1 = {
			SpellSlot = 3,
			DamageType = "Magic",
			Range = function()
				return 700
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_1269_0, arg_1269_1)
				return ({
					210,
					420,
					630
				})[ove_0_11(3)]
			end,
			DmgModAP = function(arg_1270_0, arg_1270_1)
				return ove_0_10.GetTotalAP(arg_1270_1) * 1.2
			end
		},
		R2 = {
			SpellSlot = 3,
			DamageType = "Magic",
			Range = function()
				return 600
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_1273_0, arg_1273_1)
				return ({
					150,
					300,
					450
				})[ove_0_11(3)]
			end,
			DmgModAP = function(arg_1274_0, arg_1274_1)
				return ove_0_10.GetTotalAP(arg_1274_1) * 0.75
			end
		},
		R3 = {
			SpellSlot = 3,
			DamageType = "Magic",
			Range = function()
				return 950
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_1277_0, arg_1277_1)
				return ({
					210,
					420,
					630
				})[ove_0_11(3)]
			end,
			DmgModAP = function(arg_1278_0, arg_1278_1)
				return ove_0_10.GetTotalAP(arg_1278_1) * 1.2
			end
		}
	},
	LeeSin = {
		Passive = {
			SpellSlot = -1
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Physical",
			Range = function()
				return 1200
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_1281_0, arg_1281_1)
				return ({
					110,
					160,
					210,
					260,
					310
				})[ove_0_11(0)]
			end,
			DmgModAD = function(arg_1282_0, arg_1282_1)
				return ove_0_10.GetBonusAD(arg_1282_1) * 2
			end
		},
		W = {
			SpellSlot = 1,
			Range = function()
				return 700
			end,
			Delay = function()
				return 0
			end
		},
		E = {
			SpellSlot = 2,
			DamageType = "Magic",
			Range = function()
				return 450
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_1287_0, arg_1287_1)
				return ({
					100,
					130,
					160,
					190,
					220
				})[ove_0_11(2)]
			end,
			DmgModAP = function(arg_1288_0, arg_1288_1)
				return ove_0_10.GetBonusAD(arg_1288_1) * 1
			end
		},
		R = {
			SpellSlot = 3,
			DamageType = "Magic",
			Range = function()
				return 375
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_1291_0, arg_1291_1)
				return ({
					175,
					400,
					625
				})[ove_0_11(3)]
			end,
			DmgModAD = function(arg_1292_0, arg_1292_1)
				return ove_0_10.GetBonusAD(arg_1292_1) * 2
			end
		}
	},
	Leona = {
		Passive = {
			SpellSlot = -1,
			DamageType = "Magic",
			Dmg = function()
				return 24 + 8 * player.levelRef
			end,
			DmgModAPBase = function(arg_1294_0, arg_1294_1)
				return ove_0_10.GetTotalAP(arg_1294_1)
			end
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Magic",
			Range = function()
				return math.huge
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_1297_0, arg_1297_1)
				return ({
					10,
					35,
					60,
					85,
					110
				})[ove_0_11(0)]
			end,
			DmgModAP = function(arg_1298_0, arg_1298_1)
				return ove_0_10.GetTotalAP(arg_1298_1) * 0.3
			end
		},
		W = {
			SpellSlot = 1,
			DamageType = "Magic",
			Range = function()
				return 450
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_1301_0, arg_1301_1)
				return ({
					45,
					80,
					115,
					150,
					185
				})[ove_0_11(1)]
			end,
			DmgModAP = function(arg_1302_0, arg_1302_1)
				return ove_0_10.GetTotalAP(arg_1302_1) * 0.4
			end
		},
		E = {
			SpellSlot = 2,
			DamageType = "Magic",
			Range = function()
				return 900
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_1305_0, arg_1305_1)
				return ({
					50,
					90,
					130,
					170,
					210
				})[ove_0_11(2)]
			end,
			DmgModAP = function(arg_1306_0, arg_1306_1)
				return ove_0_10.GetTotalAP(arg_1306_1) * 0.4
			end
		},
		R = {
			SpellSlot = 3,
			DamageType = "Magic",
			Range = function()
				return 1200
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_1309_0, arg_1309_1)
				return ({
					100,
					175,
					250
				})[ove_0_11(3)]
			end,
			DmgModAP = function(arg_1310_0, arg_1310_1)
				return ove_0_10.GetTotalAP(arg_1310_1) * 0.8
			end
		}
	},
	Lillia = {
		Passive = {
			SpellSlot = -1
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Magic",
			Range = function()
				return 485
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_1313_0, arg_1313_1)
				return ({
					70,
					100,
					130,
					160,
					190
				})[ove_0_11(0)]
			end,
			DmgModAP = function(arg_1314_0, arg_1314_1)
				return ove_0_10.GetTotalAP(arg_1314_1) * 0.8
			end
		},
		W = {
			SpellSlot = 1,
			DamageType = "Magic",
			Range = function()
				return 500
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_1317_0, arg_1317_1)
				return ({
					240,
					300,
					360,
					420,
					480
				})[ove_0_11(1)]
			end,
			DmgModAP = function(arg_1318_0, arg_1318_1)
				return ove_0_10.GetTotalAP(arg_1318_1) * 1.05
			end
		},
		E = {
			SpellSlot = 2,
			DamageType = "Magic",
			Range = function()
				return math.huge
			end,
			Delay = function()
				return 0.4
			end,
			Dmg = function(arg_1321_0, arg_1321_1)
				return ({
					70,
					95,
					120,
					145,
					170
				})[ove_0_11(2)]
			end,
			DmgModAP = function(arg_1322_0, arg_1322_1)
				return ove_0_10.GetTotalAP(arg_1322_1) * 0.45
			end
		},
		R = {
			SpellSlot = 3,
			DamageType = "Magic",
			Range = function()
				return math.huge
			end,
			Delay = function()
				return 0.4
			end,
			Dmg = function(arg_1325_0, arg_1325_1)
				return ({
					100,
					150,
					200
				})[ove_0_11(3)]
			end,
			DmgModAP = function(arg_1326_0, arg_1326_1)
				return ove_0_10.GetTotalAP(arg_1326_1) * 0.4
			end
		}
	},
	Lissandra = {
		Passive = {
			SpellSlot = -1
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Magic",
			Range = function()
				return 725
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_1329_0, arg_1329_1)
				return ({
					80,
					110,
					140,
					170,
					200
				})[ove_0_11(0)]
			end,
			DmgModAP = function(arg_1330_0, arg_1330_1)
				return ove_0_10.GetTotalAP(arg_1330_1) * 0.8
			end
		},
		W = {
			SpellSlot = 1,
			DamageType = "Magic",
			Range = function()
				return 450
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_1333_0, arg_1333_1)
				return ({
					70,
					105,
					140,
					175,
					210
				})[ove_0_11(1)]
			end,
			DmgModAP = function(arg_1334_0, arg_1334_1)
				return ove_0_10.GetTotalAP(arg_1334_1) * 0.7
			end
		},
		E = {
			SpellSlot = 2,
			DamageType = "Magic",
			Range = function()
				return 1025
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_1337_0, arg_1337_1)
				return ({
					70,
					105,
					140,
					175,
					210
				})[ove_0_11(2)]
			end,
			DmgModAP = function(arg_1338_0, arg_1338_1)
				return ove_0_10.GetTotalAP(arg_1338_1) * 0.6
			end
		},
		R = {
			SpellSlot = 3,
			DamageType = "Magic",
			Range = function()
				return 550
			end,
			Delay = function()
				return 0.375
			end,
			Dmg = function(arg_1341_0, arg_1341_1)
				return ({
					150,
					250,
					350
				})[ove_0_11(3)]
			end,
			DmgModAP = function(arg_1342_0, arg_1342_1)
				return ove_0_10.GetTotalAP(arg_1342_1) * 0.75
			end
		}
	},
	Lucian = {
		Passive = {
			SpellSlot = -1
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Physical",
			Range = function()
				return 500
			end,
			Delay = function()
				return 0.4 - 0.008823529411764706 * (player.levelRef - 1)
			end,
			Dmg = function(arg_1345_0, arg_1345_1)
				return ({
					95,
					125,
					155,
					185,
					215
				})[ove_0_11(0)]
			end,
			DmgModAD = function(arg_1346_0, arg_1346_1)
				return ove_0_10.GetBonusAD(arg_1346_1) * ({
					0.6,
					0.75,
					0.9,
					1.05,
					1.2
				})[ove_0_11(0)]
			end
		},
		W = {
			SpellSlot = 1,
			DamageType = "Magic",
			Range = function()
				return 900
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_1349_0, arg_1349_1)
				return ({
					75,
					110,
					145,
					180,
					215
				})[ove_0_11(1)]
			end,
			DmgModAP = function(arg_1350_0, arg_1350_1)
				return ove_0_10.GetTotalAP(arg_1350_1) * 0.9
			end
		},
		E = {
			SpellSlot = 2,
			Range = function()
				return 425
			end,
			Delay = function()
				return 0
			end
		},
		R = {
			SpellSlot = 3,
			DamageType = "Physical",
			Range = function()
				return 1140
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_1355_0, arg_1355_1)
				return ({
					15,
					30,
					45
				})[ove_0_11(3)]
			end,
			DmgModAD = function(arg_1356_0, arg_1356_1)
				return (ove_0_10.GetTotalAD(arg_1356_1) + ove_0_10.GetBonusAD(arg_1356_1)) * 0.25
			end,
			DmgModAP = function(arg_1357_0, arg_1357_1)
				return ove_0_10.GetTotalAP(arg_1357_1) * 0.15
			end
		}
	},
	Lulu = {
		Passive = {
			SpellSlot = -1,
			DamageType = "Magic",
			Dmg = function(arg_1358_0, arg_1358_1)
				return 9 + 6 * player.levelRef
			end,
			DmgModAP = function(arg_1359_0, arg_1359_1)
				return ove_0_10.GetTotalAP(arg_1359_1) * 0.15
			end,
			DmgModAPBase = function(arg_1360_0, arg_1360_1)
				return ove_0_10.GetTotalAP(arg_1360_1)
			end
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Magic",
			Range = function()
				return 950
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_1363_0, arg_1363_1)
				return ({
					70,
					105,
					140,
					175,
					210
				})[ove_0_11(0)]
			end,
			DmgModAP = function(arg_1364_0, arg_1364_1)
				return ove_0_10.GetTotalAP(arg_1364_1) * 0.4
			end
		},
		W = {
			SpellSlot = 1,
			Range = function()
				return 650
			end,
			Delay = function()
				return 0.2419
			end
		},
		E = {
			SpellSlot = 2,
			DamageType = "Magic",
			Range = function()
				return 650
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_1369_0, arg_1369_1)
				return ({
					80,
					120,
					160,
					200,
					240
				})[ove_0_11(2)]
			end,
			DmgModAP = function(arg_1370_0, arg_1370_1)
				return ove_0_10.GetTotalAP(arg_1370_1) * 0.4
			end
		},
		R = {
			SpellSlot = 3,
			Range = function()
				return 900
			end,
			Delay = function()
				return 0
			end
		}
	},
	Lux = {
		Passive = {
			SpellSlot = -1,
			DamageType = "Magic",
			Dmg = function(arg_1373_0, arg_1373_1)
				return 10 + 10 * player.levelRef
			end,
			DmgModAP = function(arg_1374_0, arg_1374_1)
				return ove_0_10.GetTotalAP(arg_1374_1) * 0.2
			end,
			DmgModAPBase = function(arg_1375_0, arg_1375_1)
				return ove_0_10.GetTotalAP(arg_1375_1)
			end
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Magic",
			Range = function()
				return 1240
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_1378_0, arg_1378_1)
				return ({
					80,
					120,
					160,
					200,
					240
				})[ove_0_11(0)]
			end,
			DmgModAP = function(arg_1379_0, arg_1379_1)
				return ove_0_10.GetTotalAP(arg_1379_1) * 0.6
			end
		},
		W = {
			SpellSlot = 1,
			Range = function()
				return 1175
			end,
			Delay = function()
				return 0.25
			end
		},
		E = {
			SpellSlot = 2,
			DamageType = "Magic",
			Range = function()
				return 1100
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_1384_0, arg_1384_1)
				return ({
					70,
					120,
					170,
					220,
					270
				})[ove_0_11(2)]
			end,
			DmgModAP = function(arg_1385_0, arg_1385_1)
				return ove_0_10.GetTotalAP(arg_1385_1) * 0.7
			end
		},
		R = {
			SpellSlot = 3,
			DamageType = "Magic",
			Range = function()
				return 3400
			end,
			Delay = function()
				return 1
			end,
			Dmg = function(arg_1388_0, arg_1388_1)
				return ({
					300,
					400,
					500
				})[ove_0_11(3)]
			end,
			DmgModAP = function(arg_1389_0, arg_1389_1)
				return ove_0_10.GetTotalAP(arg_1389_1) * 1
			end
		}
	},
	Malphite = {
		Passive = {
			SpellSlot = -1
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Magic",
			Range = function()
				return 625
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_1392_0, arg_1392_1)
				return ({
					70,
					120,
					170,
					220,
					270
				})[ove_0_11(0)]
			end,
			DmgModAP = function(arg_1393_0, arg_1393_1)
				return ove_0_10.GetTotalAP(arg_1393_1) * 0.6
			end
		},
		W = {
			SpellSlot = 1,
			DamageType = "Magic",
			Range = function()
				return math.huge
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_1396_0, arg_1396_1)
				return ({
					30,
					45,
					60,
					75,
					90
				})[ove_0_11(1)]
			end,
			DmgModAP = function(arg_1397_0, arg_1397_1)
				return ove_0_10.GetTotalAP(arg_1397_1) * 0.2
			end
		},
		E = {
			SpellSlot = 2,
			DamageType = "Magic",
			Range = function()
				return 400
			end,
			Delay = function()
				return 0.2419
			end,
			Dmg = function(arg_1400_0, arg_1400_1)
				return ({
					60,
					95,
					130,
					165,
					200
				})[ove_0_11(2)]
			end,
			DmgModAP = function(arg_1401_0, arg_1401_1)
				return ove_0_10.GetTotalAP(arg_1401_1) * 0.6
			end
		},
		R = {
			SpellSlot = 3,
			DamageType = "Magic",
			Range = function()
				return 1000
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_1404_0, arg_1404_1)
				return ({
					200,
					300,
					400
				})[ove_0_11(3)]
			end,
			DmgModAP = function(arg_1405_0, arg_1405_1)
				return ove_0_10.GetTotalAP(arg_1405_1) * 0.8
			end
		}
	},
	Malzahar = {
		Passive = {
			SpellSlot = -1
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Magic",
			Range = function()
				return 900
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_1408_0, arg_1408_1)
				return ({
					70,
					105,
					140,
					175,
					210
				})[ove_0_11(0)]
			end,
			DmgModAP = function(arg_1409_0, arg_1409_1)
				return ove_0_10.GetTotalAP(arg_1409_1) * 0.55
			end
		},
		W = {
			SpellSlot = 1,
			Range = function()
				return 150
			end,
			Delay = function()
				return 0
			end
		},
		E = {
			SpellSlot = 2,
			DamageType = "Magic",
			Range = function()
				return 650
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_1414_0, arg_1414_1)
				return ({
					80,
					115,
					150,
					185,
					220
				})[ove_0_11(2)]
			end,
			DmgModAP = function(arg_1415_0, arg_1415_1)
				return ove_0_10.GetTotalAP(arg_1415_1) * 0.8
			end
		},
		R = {
			SpellSlot = 3,
			DamageType = "Magic",
			Range = function()
				return 700
			end,
			Delay = function()
				return 0.0005
			end,
			Dmg = function(arg_1418_0, arg_1418_1)
				return ({
					125,
					200,
					275
				})[ove_0_11(3)]
			end,
			DmgModAP = function(arg_1419_0, arg_1419_1)
				return ove_0_10.GetTotalAP(arg_1419_1) * 0.8
			end,
			DmgModHP = function(arg_1420_0, arg_1420_1)
				return arg_1420_1.maxHealth / 100 * (({
					10,
					15,
					20
				})[ove_0_11(3)] + 2.5 * (ove_0_10.GetTotalAP(arg_1420_1) % 100))
			end
		}
	},
	Maokai = {
		Passive = {
			SpellSlot = -1
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Magic",
			Range = function()
				return 600
			end,
			Delay = function()
				return 0.3889
			end,
			Dmg = function(arg_1423_0, arg_1423_1)
				return ({
					65,
					110,
					150,
					200,
					245
				})[ove_0_11(0)]
			end,
			DmgModAP = function(arg_1424_0, arg_1424_1)
				return ove_0_10.GetTotalAP(arg_1424_1) * 0.4
			end,
			DmgModHP = function(arg_1425_0, arg_1425_1)
				return arg_1425_1.maxHealth / 100 * ({
					2,
					2.25,
					2.5,
					2.75,
					3
				})[ove_0_11(0)]
			end
		},
		W = {
			SpellSlot = 1,
			DamageType = "Magic",
			Range = function()
				return 525
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_1428_0, arg_1428_1)
				return ({
					60,
					85,
					110,
					135,
					160
				})[ove_0_11(1)]
			end,
			DmgModAP = function(arg_1429_0, arg_1429_1)
				return ove_0_10.GetTotalAP(arg_1429_1) * 0.4
			end
		},
		E = {
			SpellSlot = 2,
			DamageType = "Magic",
			Range = function()
				return 1100
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_1432_0, arg_1432_1)
				return ({
					55,
					80,
					105,
					130,
					120
				})[ove_0_11(2)]
			end,
			DmgModAP = function(arg_1433_0, arg_1433_1)
				return ove_0_10.GetTotalAP(arg_1433_1) * 0.425
			end,
			DmgModHP = function(arg_1434_0, arg_1434_1)
				return arg_1434_1.health / 100 * 6
			end
		},
		R = {
			SpellSlot = 3,
			DamageType = "Magic",
			Range = function()
				return 3000
			end,
			Delay = function()
				return 0.5
			end,
			Dmg = function(arg_1437_0, arg_1437_1)
				return ({
					150,
					225,
					300
				})[ove_0_11(3)]
			end,
			DmgModAP = function(arg_1438_0, arg_1438_1)
				return ove_0_10.GetTotalAP(arg_1438_1) * 0.75
			end
		}
	},
	MasterYi = {
		Passive = {
			SpellSlot = -1
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Physical",
			Range = function()
				return 600
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_1441_0, arg_1441_1)
				return ({
					52.5,
					105,
					157.5,
					210,
					262.5
				})[ove_0_11(0)]
			end,
			DmgModAD = function(arg_1442_0, arg_1442_1)
				return (ove_0_10.GetTotalAD(arg_1442_1) + ove_0_10.GetBonusAD(arg_1442_1)) * 0.875
			end
		},
		W = {
			SpellSlot = 1,
			Range = function()
				return math.huge
			end,
			Delay = function()
				return 0
			end
		},
		E = {
			SpellSlot = 2,
			DamageType = "True",
			Range = function()
				return math.huge
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_1447_0, arg_1447_1)
				return ({
					30,
					35,
					40,
					45,
					50
				})[ove_0_11(2)]
			end,
			DmgModAP = function(arg_1448_0, arg_1448_1)
				return ove_0_10.GetBonusAD(arg_1448_1) * 0.35
			end
		},
		R = {
			SpellSlot = 3,
			Range = function()
				return math.huge
			end,
			Delay = function()
				return 0
			end
		}
	},
	MissFortune = {
		Passive = {
			SpellSlot = -1
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Physical",
			Range = function()
				return 550
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_1453_0, arg_1453_1)
				return ({
					20,
					45,
					70,
					95,
					120
				})[ove_0_11(0)]
			end,
			DmgModAD = function(arg_1454_0, arg_1454_1)
				return (ove_0_10.GetTotalAD(arg_1454_1) + ove_0_10.GetBonusAD(arg_1454_1)) * 1
			end,
			DmgModAP = function(arg_1455_0, arg_1455_1)
				return ove_0_10.GetTotalAP(arg_1455_1) * 0.35
			end
		},
		W = {
			SpellSlot = 1,
			Range = function()
				return math.huge
			end,
			Delay = function()
				return 0
			end
		},
		E = {
			SpellSlot = 2,
			DamageType = "Magic",
			Range = function()
				return 1000
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_1460_0, arg_1460_1)
				return ({
					70,
					100,
					130,
					160,
					190
				})[ove_0_11(2)]
			end,
			DmgModAP = function(arg_1461_0, arg_1461_1)
				return ove_0_10.GetTotalAP(arg_1461_1) * 1
			end
		},
		R = {
			SpellSlot = 3,
			DamageType = "Physical",
			Range = function()
				return 1450
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_1464_0, arg_1464_1)
				return (ove_0_10.GetTotalAD(arg_1464_1) + ove_0_10.GetBonusAD(arg_1464_1)) * ({
					10.5,
					12,
					13.5
				})[ove_0_11(3)]
			end,
			DmgModAP = function(arg_1465_0, arg_1465_1)
				return ove_0_10.GetTotalAP(arg_1465_1) * ({
					2.8,
					3.2,
					3.6
				})[ove_0_11(3)]
			end
		}
	},
	Mordekaiser = {
		Passive = {
			SpellSlot = -1
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Magic",
			Range = function()
				return 625
			end,
			Delay = function()
				return 0.5
			end,
			Dmg = function(arg_1468_0, arg_1468_1)
				return ({
					5,
					9,
					13,
					17,
					21,
					25,
					29,
					33,
					37,
					41,
					51,
					61,
					71,
					81,
					91,
					107,
					123,
					139
				})[player.levelRef] + ({
					75,
					95,
					115,
					135,
					155
				})[ove_0_11(0)]
			end,
			DmgModAP = function(arg_1469_0, arg_1469_1)
				return ove_0_10.GetTotalAP(arg_1469_1) * 0.6
			end
		},
		W = {
			SpellSlot = 1,
			Range = function()
				return math.huge
			end,
			Delay = function()
				return 0
			end
		},
		E = {
			SpellSlot = 2,
			DamageType = "Magic",
			Range = function()
				return 900
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_1474_0, arg_1474_1)
				return ({
					80,
					95,
					110,
					125,
					140
				})[ove_0_11(2)]
			end,
			DmgModAP = function(arg_1475_0, arg_1475_1)
				return ove_0_10.GetTotalAP(arg_1475_1) * 0.6
			end
		},
		R = {
			SpellSlot = 3,
			Range = function()
				return 650
			end,
			Delay = function()
				return 0.5
			end
		}
	},
	Morgana = {
		Passive = {
			SpellSlot = -1
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Magic",
			Range = function()
				return 1300
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_1480_0, arg_1480_1)
				return ({
					80,
					135,
					190,
					245,
					300
				})[ove_0_11(0)]
			end,
			DmgModAP = function(arg_1481_0, arg_1481_1)
				return ove_0_10.GetTotalAP(arg_1481_1) * 0.9
			end
		},
		W = {
			SpellSlot = 1,
			DamageType = "Magic",
			Range = function()
				return 900
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_1484_0, arg_1484_1)
				return ({
					162,
					297,
					432,
					567,
					702
				})[ove_0_11(1)]
			end,
			DmgModAP = function(arg_1485_0, arg_1485_1)
				return ove_0_10.GetTotalAP(arg_1485_1) * 1.89
			end
		},
		E = {
			SpellSlot = 2,
			Range = function()
				return 800
			end,
			Delay = function()
				return 0
			end
		},
		R = {
			SpellSlot = 3,
			DamageType = "Magic",
			Range = function()
				return 625
			end,
			Delay = function()
				return 0.35
			end,
			Dmg = function(arg_1490_0, arg_1490_1)
				return ({
					300,
					450,
					600
				})[ove_0_11(3)]
			end,
			DmgModAP = function(arg_1491_0, arg_1491_1)
				return ove_0_10.GetTotalAP(arg_1491_1) * 1.4
			end
		}
	},
	Nami = {
		Passive = {
			SpellSlot = -1
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Magic",
			Range = function()
				return 875
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_1494_0, arg_1494_1)
				return ({
					75,
					130,
					185,
					240,
					295
				})[ove_0_11(0)]
			end,
			DmgModAP = function(arg_1495_0, arg_1495_1)
				return ove_0_10.GetTotalAP(arg_1495_1) * 0.5
			end
		},
		W = {
			SpellSlot = 1,
			DamageType = "Magic",
			Range = function()
				return 725
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_1498_0, arg_1498_1)
				return ({
					70,
					110,
					150,
					190,
					230
				})[ove_0_11(1)]
			end,
			DmgModAP = function(arg_1499_0, arg_1499_1)
				return ove_0_10.GetTotalAP(arg_1499_1) * 0.5
			end
		},
		E = {
			SpellSlot = 2,
			DamageType = "Magic",
			Range = function()
				return 800
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_1502_0, arg_1502_1)
				return ({
					75,
					120,
					165,
					210,
					255
				})[ove_0_11(2)]
			end,
			DmgModAP = function(arg_1503_0, arg_1503_1)
				return ove_0_10.GetTotalAP(arg_1503_1) * 0.6
			end
		},
		R = {
			SpellSlot = 3,
			DamageType = "Magic",
			Range = function()
				return 2750
			end,
			Delay = function()
				return 0.5
			end,
			Dmg = function(arg_1506_0, arg_1506_1)
				return ({
					150,
					250,
					350
				})[ove_0_11(3)]
			end,
			DmgModAP = function(arg_1507_0, arg_1507_1)
				return ove_0_10.GetTotalAP(arg_1507_1) * 0.6
			end
		}
	},
	Nasus = {
		Passive = {
			SpellSlot = -1
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Physical",
			Range = function()
				return math.huge
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_1510_0, arg_1510_1)
				return ({
					30,
					50,
					70,
					90,
					110
				})[ove_0_11(0)]
			end,
			DmgModAP = function(arg_1511_0, arg_1511_1)
				return ove_0_10.GetTotalAP(arg_1511_1) * 0.5
			end
		},
		W = {
			SpellSlot = 1,
			Range = function()
				return 700
			end,
			Delay = function()
				return 0.25
			end
		},
		E = {
			SpellSlot = 2,
			DamageType = "Magic",
			Range = function()
				return 650
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_1516_0, arg_1516_1)
				return ({
					55,
					95,
					135,
					175,
					215
				})[ove_0_11(2)]
			end,
			DmgModAP = function(arg_1517_0, arg_1517_1)
				return ove_0_10.GetTotalAP(arg_1517_1) * 0.6
			end
		},
		R = {
			SpellSlot = 3,
			DamageType = "Magic",
			Range = function()
				return 400
			end,
			Delay = function()
				return 0.2
			end,
			Dmg = function(arg_1520_0, arg_1520_1)
				return arg_1520_1.maxHealth / 100 * ({
					45,
					60,
					75
				})[ove_0_11(3)] + 15 * (ove_0_10.GetTotalAP(arg_1520_1) % 100)
			end
		}
	},
	Nautilus = {
		Passive = {
			SpellSlot = -1,
			DamageType = "Physical",
			Dmg = function(arg_1521_0, arg_1521_1)
				return 2 + 6 * player.levelRef
			end,
			DmgModADBase = function(arg_1522_0, arg_1522_1)
				return ove_0_10.GetTotalAD(arg_1522_1)
			end
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Magic",
			Range = function()
				return 1122
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_1525_0, arg_1525_1)
				return ({
					70,
					115,
					160,
					205,
					250
				})[ove_0_11(0)]
			end,
			DmgModAP = function(arg_1526_0, arg_1526_1)
				return ove_0_10.GetTotalAP(arg_1526_1) * 0.9
			end
		},
		W = {
			SpellSlot = 1,
			DamageType = "Magic",
			Range = function()
				return 250
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_1529_0, arg_1529_1)
				return ({
					30,
					40,
					50,
					60,
					70
				})[ove_0_11(1)]
			end,
			DmgModAP = function(arg_1530_0, arg_1530_1)
				return ove_0_10.GetTotalAP(arg_1530_1) * 0.4
			end
		},
		E = {
			SpellSlot = 2,
			DamageType = "Magic",
			Range = function()
				return 590
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_1533_0, arg_1533_1)
				return ({
					110,
					170,
					230,
					290,
					350
				})[ove_0_11(2)]
			end,
			DmgModAP = function(arg_1534_0, arg_1534_1)
				return ove_0_10.GetTotalAP(arg_1534_1) * 0.6
			end
		},
		R = {
			SpellSlot = 3,
			DamageType = "Magic",
			Range = function()
				return 825
			end,
			Delay = function()
				return 0.46
			end,
			Dmg = function(arg_1537_0, arg_1537_1)
				return ({
					150,
					275,
					400
				})[ove_0_11(3)]
			end,
			DmgModAP = function(arg_1538_0, arg_1538_1)
				return ove_0_10.GetTotalAP(arg_1538_1) * 0.8
			end
		}
	},
	Neeko = {
		Passive = {
			SpellSlot = -1
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Magic",
			Range = function()
				return 800
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_1541_0, arg_1541_1)
				return ({
					160,
					255,
					350,
					445,
					540
				})[ove_0_11(0)]
			end,
			DmgModAP = function(arg_1542_0, arg_1542_1)
				return ove_0_10.GetTotalAP(arg_1542_1) * 0.9
			end
		},
		W = {
			SpellSlot = 1,
			DamageType = "Magic",
			Range = function()
				return 900
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_1545_0, arg_1545_1)
				return ({
					50,
					80,
					110,
					140,
					170
				})[ove_0_11(1)]
			end,
			DmgModAP = function(arg_1546_0, arg_1546_1)
				return ove_0_10.GetTotalAP(arg_1546_1) * 0.6
			end
		},
		E = {
			SpellSlot = 2,
			DamageType = "Magic",
			Range = function()
				return 1000
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_1549_0, arg_1549_1)
				return ({
					80,
					115,
					150,
					185,
					220
				})[ove_0_11(2)]
			end,
			DmgModAP = function(arg_1550_0, arg_1550_1)
				return ove_0_10.GetTotalAP(arg_1550_1) * 0.6
			end
		},
		R = {
			SpellSlot = 3,
			DamageType = "Magic",
			Range = function()
				return 700
			end,
			Delay = function()
				return 0.6
			end,
			Dmg = function(arg_1553_0, arg_1553_1)
				return ({
					200,
					425,
					650
				})[ove_0_11(3)]
			end,
			DmgModAP = function(arg_1554_0, arg_1554_1)
				return ove_0_10.GetTotalAP(arg_1554_1) * 1.3
			end
		}
	},
	Nidalee = {
		Passive = {
			SpellSlot = -1
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Magic",
			Range = function()
				return 1500
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_1557_0, arg_1557_1)
				return ({
					210,
					270,
					330,
					390,
					450
				})[ove_0_11(0)]
			end,
			DmgModAP = function(arg_1558_0, arg_1558_1)
				return ove_0_10.GetTotalAP(arg_1558_1) * 1.5
			end
		},
		W = {
			SpellSlot = 1,
			DamageType = "Magic",
			Range = function()
				return 900
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_1561_0, arg_1561_1)
				return ({
					40,
					80,
					120,
					160,
					200
				})[ove_0_11(1)]
			end,
			DmgModAP = function(arg_1562_0, arg_1562_1)
				return ove_0_10.GetTotalAP(arg_1562_1) * 0.2
			end
		},
		E = {
			SpellSlot = 2,
			Range = function()
				return 600
			end,
			Delay = function()
				return 0.25
			end
		},
		R = {
			SpellSlot = 3,
			Range = function()
				return 550
			end,
			Delay = function()
				return 0
			end
		}
	},
	Nilah = {
		Passive = {
			SpellSlot = -1
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Physical",
			Range = function()
				return 600
			end,
			Width = function()
				return 150
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_1570_0, arg_1570_1)
				return ({
					10,
					20,
					30,
					40,
					50
				})[ove_0_11(0)]
			end,
			DmgModAD = function()
				return ({
					180,
					200,
					220,
					240,
					260
				})[ove_0_11(0)]
			end
		},
		W = {
			SpellSlot = 1
		},
		E = {
			SpellSlot = 2,
			DamageType = "Physical",
			Range = function()
				return 450
			end,
			Speed = function()
				return 2200
			end,
			Dmg = function(arg_1574_0, arg_1574_1)
				return ({
					65,
					90,
					115,
					140,
					165
				})[ove_0_11(2)]
			end,
			DmgModAD = function(arg_1575_0, arg_1575_1)
				return ove_0_10.GetBonusAD(arg_1575_1) * 0.2
			end
		},
		R1 = {
			SpellSlot = 3,
			DamageType = "Physical",
			Delay = function()
				return 0
			end,
			Dmg = function(arg_1577_0, arg_1577_1)
				return ({
					60,
					120,
					180
				})[ove_0_11(3)]
			end,
			DmgModAD = function(arg_1578_0, arg_1578_1)
				return ove_0_10.GetBonusAD(arg_1578_1) * 1.4
			end
		},
		R2 = {
			SpellSlot = 3,
			DamageType = "Physical",
			Delay = function()
				return 0
			end,
			Dmg = function(arg_1580_0, arg_1580_1)
				return ({
					185,
					345,
					505
				})[ove_0_11(3)]
			end,
			DmgModAD = function(arg_1581_0, arg_1581_1)
				return ove_0_10.GetBonusAD(arg_1581_1) * 2.6
			end
		}
	},
	Nocturne = {
		Passive = {
			SpellSlot = -1,
			DamageType = "Physical",
			DmgModAD = function(arg_1582_0, arg_1582_1)
				return ove_0_10.GetTotalAD(arg_1582_1) * 1.2
			end
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Physical",
			Range = function()
				return 1200
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_1585_0, arg_1585_1)
				return ({
					65,
					110,
					155,
					200,
					245
				})[ove_0_11(0)]
			end,
			DmgModAD = function(arg_1586_0, arg_1586_1)
				return ove_0_10.GetBonusAD(arg_1586_1) * 0.85
			end
		},
		W = {
			SpellSlot = 1,
			Range = function()
				return math.huge
			end,
			Delay = function()
				return 0
			end
		},
		E = {
			SpellSlot = 2,
			DamageType = "Magic",
			Range = function()
				return 425
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_1591_0, arg_1591_1)
				return ({
					80,
					125,
					170,
					215,
					260
				})[ove_0_11(0)]
			end,
			DmgModAP = function(arg_1592_0, arg_1592_1)
				return ove_0_10.GetTotalAP(arg_1592_1) * 1
			end
		},
		R = {
			SpellSlot = 3,
			DamageType = "Physical",
			Range = function()
				return ({
					2500,
					3250,
					4000
				})[ove_0_11(3)]
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_1595_0, arg_1595_1)
				return ({
					150,
					275,
					400
				})[ove_0_11(3)]
			end,
			DmgModAD = function(arg_1596_0, arg_1596_1)
				return ove_0_10.GetBonusAD(arg_1596_1) * 1.2
			end
		}
	},
	Nunu = {
		Passive = {
			SpellSlot = -1,
			DamageType = "Physical",
			DmgModAD = function(arg_1597_0, arg_1597_1)
				return ove_0_10.GetTotalAD(arg_1597_1) * 0.2
			end
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Magic",
			Range = function()
				return 125
			end,
			Delay = function()
				return 0.3
			end,
			Dmg = function(arg_1600_0, arg_1600_1)
				return ({
					60,
					100,
					140,
					180,
					220
				})[ove_0_11(0)]
			end,
			DmgModAP = function(arg_1601_0, arg_1601_1)
				return ove_0_10.GetTotalAP(arg_1601_1) * 0.65
			end,
			DmgModHP = function(arg_1602_0, arg_1602_1)
				return arg_1602_1.maxHealth / 100 * 5
			end
		},
		W = {
			SpellSlot = 1,
			DamageType = "Magic",
			Range = function()
				return 1750
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_1605_0, arg_1605_1)
				return ({
					180,
					225,
					270,
					315,
					360
				})[ove_0_11(1)]
			end,
			DmgModAP = function(arg_1606_0, arg_1606_1)
				return ove_0_10.GetTotalAP(arg_1606_1) * 1.5
			end
		},
		E = {
			SpellSlot = 2,
			DamageType = "Magic",
			Range = function()
				return 690
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_1609_0, arg_1609_1)
				return ({
					144,
					216,
					288,
					360,
					432
				})[ove_0_11(2)]
			end,
			DmgModAP = function(arg_1610_0, arg_1610_1)
				return ove_0_10.GetTotalAP(arg_1610_1) * 0.9
			end
		},
		R = {
			SpellSlot = 3,
			DamageType = "Magic",
			Range = function()
				return 650
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_1613_0, arg_1613_1)
				return ({
					625,
					950,
					1275
				})[ove_0_11(3)]
			end,
			DmgModAP = function(arg_1614_0, arg_1614_1)
				return ove_0_10.GetTotalAP(arg_1614_1) * 2.5
			end
		}
	},
	Olaf = {
		Passive = {
			SpellSlot = -1
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Physical",
			Range = function()
				return 1000
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_1617_0, arg_1617_1)
				return ({
					70,
					120,
					170,
					220,
					270
				})[ove_0_11(0)]
			end,
			DmgModAD = function(arg_1618_0, arg_1618_1)
				return ove_0_10.GetBonusAD(arg_1618_1) * 1
			end
		},
		W = {
			SpellSlot = 1,
			Range = function()
				return math.huge
			end,
			Delay = function()
				return 0
			end
		},
		E = {
			SpellSlot = 2,
			DamageType = "True",
			Range = function()
				return 325
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_1623_0, arg_1623_1)
				return ({
					70,
					115,
					160,
					205,
					250
				})[ove_0_11(2)]
			end,
			DmgModAD = function(arg_1624_0, arg_1624_1)
				return (ove_0_10.GetTotalAD(arg_1624_1) + ove_0_10.GetBonusAD(arg_1624_1)) * 0.5
			end
		},
		R = {
			SpellSlot = 3,
			Range = function()
				return math.huge
			end,
			Delay = function()
				return 0
			end
		}
	},
	Orianna = {
		Passive = {
			DamageType = "Magic",
			Dmg = function()
				return 10 + 8 * (1 + math.floor(player.levelRef - 0.3333333333333333))
			end,
			DmgModAP = function()
				return 0.15
			end,
			DmgModAPBase = function(arg_1629_0, arg_1629_1)
				return ove_0_10.GetTotalAP(arg_1629_1)
			end
		},
		PassiveStack = {
			DamageType = "Magic",
			Dmg = function()
				return 14 + 11.2 * (1 + math.floor(player.levelRef - 0.3333333333333333))
			end,
			DmgModAP = function()
				return 0.21
			end,
			DmgModAPBase = function(arg_1632_0, arg_1632_1)
				return ove_0_10.GetTotalAP(arg_1632_1)
			end
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Magic",
			Range = function()
				return 825
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_1635_0, arg_1635_1)
				return ({
					60,
					90,
					120,
					150,
					180
				})[ove_0_11(0)]
			end,
			DmgModAP = function(arg_1636_0, arg_1636_1)
				return ove_0_10.GetTotalAP(arg_1636_1) * 0.5
			end
		},
		W = {
			SpellSlot = 1,
			DamageType = "Magic",
			Range = function()
				return 225
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_1639_0, arg_1639_1)
				return ({
					60,
					105,
					150,
					195,
					240
				})[ove_0_11(1)]
			end,
			DmgModAP = function(arg_1640_0, arg_1640_1)
				return ove_0_10.GetTotalAP(arg_1640_1) * 0.7
			end
		},
		E = {
			SpellSlot = 2,
			DamageType = "Magic",
			Range = function()
				return 1120
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_1643_0, arg_1643_1)
				return ({
					60,
					90,
					120,
					150,
					180
				})[ove_0_11(2)]
			end,
			DmgModAP = function(arg_1644_0, arg_1644_1)
				return ove_0_10.GetTotalAP(arg_1644_1) * 0.3
			end
		},
		R = {
			SpellSlot = 3,
			DamageType = "Magic",
			Range = function()
				return 415
			end,
			Delay = function()
				return 0.5
			end,
			Dmg = function(arg_1647_0, arg_1647_1)
				return ({
					200,
					275,
					350
				})[ove_0_11(3)]
			end,
			DmgModAP = function(arg_1648_0, arg_1648_1)
				return ove_0_10.GetTotalAP(arg_1648_1) * 0.8
			end
		}
	},
	Ornn = {
		Passive = {
			SpellSlot = -1
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Physical",
			Range = function()
				return 750
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_1651_0, arg_1651_1)
				return ({
					20,
					45,
					70,
					95,
					120
				})[ove_0_11(0)]
			end,
			DmgModAD = function(arg_1652_0, arg_1652_1)
				return (ove_0_10.GetTotalAD(arg_1652_1) + ove_0_10.GetBonusAD(arg_1652_1)) * 1.1
			end
		},
		W = {
			SpellSlot = 1,
			DamageType = "Magic",
			Range = function()
				return 500
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_1655_0, arg_1655_1)
				return arg_1655_1.maxHealth / 100 * ({
					12,
					13,
					14,
					15,
					16
				})[ove_0_11(1)]
			end
		},
		E = {
			SpellSlot = 2,
			DamageType = "Physical",
			Range = function()
				return 800
			end,
			Delay = function()
				return 0.35
			end,
			Dmg = function(arg_1658_0, arg_1658_1)
				return ({
					80,
					125,
					170,
					215,
					260
				})[ove_0_11(2)]
			end
		},
		R = {
			SpellSlot = 3,
			DamageType = "Magic",
			Range = function()
				return 2550
			end,
			Delay = function()
				return 0.5
			end,
			Dmg = function(arg_1661_0, arg_1661_1)
				return ({
					250,
					350,
					450
				})[ove_0_11(3)]
			end,
			DmgModAP = function(arg_1662_0, arg_1662_1)
				return ove_0_10.GetTotalAP(arg_1662_1) * 0.4
			end
		}
	},
	Pantheon = {
		Passive = {
			SpellSlot = -1
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Physical",
			Range = function()
				return 1200
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_1665_0, arg_1665_1)
				return ({
					155,
					230,
					305,
					380,
					455
				})[ove_0_11(0)]
			end,
			DmgModAD = function(arg_1666_0, arg_1666_1)
				return ove_0_10.GetBonusAD(arg_1666_1) * 2.3
			end
		},
		W = {
			SpellSlot = 1,
			DamageType = "Physical",
			Range = function()
				return 600
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_1669_0, arg_1669_1)
				return ({
					60,
					100,
					140,
					180,
					220
				})[ove_0_11(1)]
			end,
			DmgModAP = function(arg_1670_0, arg_1670_1)
				return ove_0_10.GetTotalAP(arg_1670_1) * 1
			end
		},
		E = {
			SpellSlot = 2,
			DamageType = "Physical",
			Range = function()
				return 400
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_1673_0, arg_1673_1)
				return ({
					55,
					105,
					155,
					205,
					255
				})[ove_0_11(2)]
			end,
			DmgModAD = function(arg_1674_0, arg_1674_1)
				return (ove_0_10.GetTotalAD(arg_1674_1) + ove_0_10.GetBonusAD(arg_1674_1)) * 1.5
			end
		},
		R = {
			SpellSlot = 3,
			DamageType = "Magic",
			Range = function()
				return 5500
			end,
			Delay = function()
				return 0.1
			end,
			Dmg = function(arg_1677_0, arg_1677_1)
				return ({
					300,
					500,
					700
				})[ove_0_11(3)]
			end,
			DmgModAP = function(arg_1678_0, arg_1678_1)
				return ove_0_10.GetTotalAP(arg_1678_1) * 1
			end
		}
	},
	Poppy = {
		Passive = {
			SpellSlot = -1,
			DamageType = "Magic",
			Dmg = function(arg_1679_0, arg_1679_1)
				return 20 + 9.411764705882353 * (player.levelRef - 1)
			end,
			DmgModAPBase = function(arg_1680_0, arg_1680_1)
				return ove_0_10.GetTotalAP(arg_1680_1)
			end
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Physical",
			Range = function()
				return 460
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_1683_0, arg_1683_1)
				return ({
					80,
					120,
					160,
					200,
					240
				})[ove_0_11(0)]
			end,
			DmgModAD = function(arg_1684_0, arg_1684_1)
				return ove_0_10.GetBonusAD(arg_1684_1) * 1.8
			end,
			DmgModHP = function(arg_1685_0, arg_1685_1)
				return arg_1685_1.maxHealth / 100 * 16
			end
		},
		W = {
			SpellSlot = 1,
			DamageType = "Magic",
			Range = function()
				return 400
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_1688_0, arg_1688_1)
				return ({
					70,
					110,
					150,
					190,
					230
				})[ove_0_11(1)]
			end,
			DmgModAP = function(arg_1689_0, arg_1689_1)
				return ove_0_10.GetTotalAP(arg_1689_1) * 0.7
			end
		},
		E = {
			SpellSlot = 2,
			DamageType = "Physical",
			Range = function()
				return 475
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_1692_0, arg_1692_1)
				return ({
					120,
					160,
					200,
					240,
					280
				})[ove_0_11(2)]
			end,
			DmgModAD = function(arg_1693_0, arg_1693_1)
				return ove_0_10.GetBonusAD(arg_1693_1) * 1
			end
		},
		R = {
			SpellSlot = 3,
			DamageType = "Physical",
			Range = function()
				return 500
			end,
			Delay = function()
				return 0.35
			end,
			Dmg = function(arg_1696_0, arg_1696_1)
				return ({
					200,
					300,
					400
				})[ove_0_11(3)]
			end,
			DmgModAD = function(arg_1697_0, arg_1697_1)
				return ove_0_10.GetBonusAD(arg_1697_1) * 0.9
			end
		}
	},
	Pyke = {
		Passive = {
			SpellSlot = -1
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Physical",
			Range = function()
				return 1100
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_1700_0, arg_1700_1)
				return ({
					100,
					150,
					200,
					250,
					300
				})[ove_0_11(0)]
			end,
			DmgModAD = function(arg_1701_0, arg_1701_1)
				return ove_0_10.GetBonusAD(arg_1701_1) * 0.6
			end
		},
		W = {
			SpellSlot = 1,
			Range = function()
				return math.huge
			end,
			Delay = function()
				return 0
			end
		},
		E = {
			SpellSlot = 2,
			DamageType = "Physical",
			Range = function()
				return 550
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_1706_0, arg_1706_1)
				return ({
					105,
					135,
					165,
					195,
					225
				})[ove_0_11(2)]
			end,
			DmgModAD = function(arg_1707_0, arg_1707_1)
				return ove_0_10.GetBonusAD(arg_1707_1) * 1
			end
		},
		R = {
			SpellSlot = 3,
			DamageType = "Physical",
			Range = function()
				return 750
			end,
			Delay = function()
				return 0.5
			end,
			Dmg = function(arg_1710_0, arg_1710_1)
				return ({
					0,
					0,
					0,
					0,
					0,
					250,
					290,
					330,
					370,
					400,
					430,
					450,
					470,
					490,
					510,
					530,
					540,
					550
				})[player.levelRef]
			end,
			DmgModAD = function(arg_1711_0, arg_1711_1)
				return ove_0_10.GetBonusAD(arg_1711_1) * 0.8
			end
		}
	},
	Qiyana = {
		Passive = {
			SpellSlot = -1,
			DamageType = "Physical",
			Dmg = function(arg_1712_0, arg_1712_1)
				return 11 + 4 * player.levelRef
			end,
			DmgModAD = function(arg_1713_0, arg_1713_1)
				return ove_0_10.GetBonusAD(arg_1713_1) * 0.3
			end,
			DmgModAP = function(arg_1714_0, arg_1714_1)
				return ove_0_10.GetTotalAP(arg_1714_1) * 0.3
			end
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Physical",
			Range = function()
				return 525
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_1717_0, arg_1717_1)
				return ({
					50,
					80,
					110,
					140,
					170
				})[ove_0_11(0)]
			end,
			DmgModAD = function(arg_1718_0, arg_1718_1)
				return ove_0_10.GetBonusAD(arg_1718_1) * 0.75
			end
		},
		W = {
			SpellSlot = 1,
			DamageType = "Magic",
			Range = function()
				return 865
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_1721_0, arg_1721_1)
				return ({
					8,
					22,
					36,
					50,
					64
				})[ove_0_11(0)]
			end,
			DmgModAD = function(arg_1722_0, arg_1722_1)
				return ove_0_10.GetBonusAD(arg_1722_1) * 0.1
			end,
			DmgModAP = function(arg_1723_0, arg_1723_1)
				return ove_0_10.GetTotalAP(arg_1723_1) * 0.45
			end
		},
		E = {
			SpellSlot = 2,
			DamageType = "Physical",
			Range = function()
				return 650
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_1726_0, arg_1726_1)
				return ({
					50,
					90,
					130,
					170,
					210
				})[ove_0_11(2)]
			end,
			DmgModAD = function(arg_1727_0, arg_1727_1)
				return ove_0_10.GetBonusAD(arg_1727_1) * 0.5
			end
		},
		R = {
			SpellSlot = 3,
			DamageType = "Physical",
			Range = function()
				return 875
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_1730_0, arg_1730_1)
				return ({
					100,
					200,
					300
				})[ove_0_11(3)]
			end,
			DmgModAD = function(arg_1731_0, arg_1731_1)
				return ove_0_10.GetBonusAD(arg_1731_1) * 1.7
			end,
			DmgModHP = function(arg_1732_0, arg_1732_1)
				return arg_1732_1.maxHealth / 100 * 10
			end
		}
	},
	Quinn = {
		Passive = {
			SpellSlot = -1,
			DamageType = "Physical",
			Dmg = function(arg_1733_0, arg_1733_1)
				return 5 + 5 * player.levelRef
			end,
			DmgModAD = function(arg_1734_0, arg_1734_1)
				return ove_0_10.GetBonusAD(arg_1734_1) * 0.14 + 0.02 * player.levelRef
			end,
			DmgModADBase = function(arg_1735_0, arg_1735_1)
				return ove_0_10.GetBonusAD(arg_1735_1)
			end
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Physical",
			Range = function()
				return 1050
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_1738_0, arg_1738_1)
				return ({
					20,
					45,
					70,
					95,
					120
				})[ove_0_11(0)]
			end,
			DmgModAD = function(arg_1739_0, arg_1739_1)
				return (ove_0_10.GetTotalAD(arg_1739_1) + ove_0_10.GetBonusAD(arg_1739_1)) * ({
					0.8,
					0.9,
					1,
					1.1,
					1.2
				})[ove_0_11(0)]
			end,
			DmgModAP = function(arg_1740_0, arg_1740_1)
				return ove_0_10.GetTotalAP(arg_1740_1) * 0.5
			end
		},
		W = {
			SpellSlot = 1,
			Range = function()
				return 2100
			end,
			Delay = function()
				return 0
			end
		},
		E = {
			SpellSlot = 2,
			DamageType = "Physical",
			Range = function()
				return 600
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_1745_0, arg_1745_1)
				return ({
					40,
					70,
					100,
					130,
					160
				})[ove_0_11(2)]
			end,
			DmgModAD = function(arg_1746_0, arg_1746_1)
				return (ove_0_10.GetTotalAD(arg_1746_1) + ove_0_10.GetBonusAD(arg_1746_1)) * 0.2
			end
		},
		R = {
			SpellSlot = 3,
			DamageType = "Physical",
			Range = function()
				return 700
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_1749_0, arg_1749_1)
				return (ove_0_10.GetTotalAD(arg_1749_1) + ove_0_10.GetBonusAD(arg_1749_1)) * 0.4
			end
		}
	},
	Rakan = {
		Passive = {
			SpellSlot = -1
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Magic",
			Range = function()
				return 900
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_1752_0, arg_1752_1)
				return ({
					70,
					115,
					160,
					205,
					250
				})[ove_0_11(0)]
			end,
			DmgModAP = function(arg_1753_0, arg_1753_1)
				return ove_0_10.GetTotalAP(arg_1753_1) * 0.6
			end
		},
		W = {
			SpellSlot = 1,
			DamageType = "Magic",
			Range = function()
				return 600
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_1756_0, arg_1756_1)
				return ({
					70,
					125,
					180,
					235,
					290
				})[ove_0_11(1)]
			end,
			DmgModAP = function(arg_1757_0, arg_1757_1)
				return ove_0_10.GetTotalAP(arg_1757_1) * 0.7
			end
		},
		E = {
			SpellSlot = 2,
			Range = function()
				return 700
			end,
			Delay = function()
				return 0
			end
		},
		R = {
			SpellSlot = 3,
			DamageType = "Magic",
			Range = function()
				return math.huge
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_1762_0, arg_1762_1)
				return ({
					100,
					200,
					300
				})[ove_0_11(3)]
			end,
			DmgModAP = function(arg_1763_0, arg_1763_1)
				return ove_0_10.GetTotalAP(arg_1763_1) * 0.5
			end
		}
	},
	Rammus = {
		Passive = {
			SpellSlot = -1,
			DamageType = "Magic",
			Dmg = function()
				return 15
			end
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Magic",
			Range = function()
				return math.huge
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_1767_0, arg_1767_1)
				return ({
					100,
					130,
					160,
					190,
					220
				})[ove_0_11(0)]
			end,
			DmgModAP = function(arg_1768_0, arg_1768_1)
				return ove_0_10.GetTotalAP(arg_1768_1) * 1
			end
		},
		W = {
			SpellSlot = 1,
			Range = function()
				return math.huge
			end,
			Delay = function()
				return 0
			end
		},
		E = {
			SpellSlot = 2,
			Range = function()
				return 325
			end,
			Delay = function()
				return 0.25
			end
		},
		R = {
			SpellSlot = 3,
			DamageType = "Magic",
			Range = function()
				return 600
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_1775_0, arg_1775_1)
				return ({
					210,
					352.5,
					495
				})[ove_0_11(3)]
			end,
			DmgModAP = function(arg_1776_0, arg_1776_1)
				return ove_0_10.GetTotalAP(arg_1776_1) * 1.2
			end
		}
	},
	Reksai = {
		Passive = {
			SpellSlot = -1
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Physical",
			Range = function()
				return 325
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_1779_0, arg_1779_1)
				return ({
					63,
					81,
					99,
					117,
					135
				})[ove_0_11(0)]
			end,
			DmgModAD = function(arg_1780_0, arg_1780_1)
				return ove_0_10.GetBonusAD(arg_1780_1) * 1.5
			end
		},
		Q2 = {
			SpellSlot = 0,
			DamageType = "Physical",
			Range = function()
				return 1650
			end,
			Delay = function()
				return 0.1
			end,
			Dmg = function(arg_1783_0, arg_1783_1)
				return ({
					60,
					95,
					130,
					165,
					200
				})[ove_0_11(0)]
			end,
			DmgModAD = function(arg_1784_0, arg_1784_1)
				return ove_0_10.GetBonusAD(arg_1784_1) * 0.5
			end,
			DmgModAP = function(arg_1785_0, arg_1785_1)
				return ove_0_10.GetTotalAP(arg_1785_1) * 0.7
			end
		},
		W2 = {
			SpellSlot = 1,
			DamageType = "Physical",
			Range = function()
				return 160
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_1788_0, arg_1788_1)
				return ({
					55,
					70,
					85,
					100,
					115
				})[ove_0_11(1)]
			end,
			DmgModAD = function(arg_1789_0, arg_1789_1)
				return ove_0_10.GetBonusAD(arg_1789_1) * 0.8
			end
		},
		E = {
			SpellSlot = 2,
			DamageType = "Physical",
			Range = function()
				return 225
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_1792_0, arg_1792_1)
				return ({
					50,
					60,
					70,
					80,
					90
				})[ove_0_11(2)]
			end,
			DmgModAD = function(arg_1793_0, arg_1793_1)
				return ove_0_10.GetBonusAD(arg_1793_1) * 0.85
			end
		},
		E2 = {
			SpellSlot = 2,
			DamageType = "Physical",
			Range = function()
				return 850
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_1796_0, arg_1796_1)
				return ({
					100,
					120,
					140,
					160,
					180
				})[ove_0_11(2)]
			end,
			DmgModAD = function(arg_1797_0, arg_1797_1)
				return ove_0_10.GetBonusAD(arg_1797_1) * 1.7
			end
		},
		R = {
			SpellSlot = 3,
			DamageType = "Physical",
			Range = function()
				return 1500
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_1800_0, arg_1800_1)
				return ({
					100,
					250,
					400
				})[ove_0_11(3)]
			end,
			DmgModAD = function(arg_1801_0, arg_1801_1)
				return ove_0_10.GetBonusAD(arg_1801_1) * 1.75
			end,
			DmgModHP = function(arg_1802_0, arg_1802_1)
				return (T - arg_1802_1.health) / 100 * ({
					20,
					25,
					30
				})[ove_0_11(3)]
			end
		}
	},
	Rell = {
		Passive = {
			SpellSlot = -1,
			DamageType = "Magic",
			Dmg = function(arg_1803_0, arg_1803_1)
				return 8 + 8 * (player.levelRef - 1)
			end
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Magic",
			Range = function()
				return 685
			end,
			Delay = function()
				return 0.35
			end,
			Dmg = function(arg_1806_0, arg_1806_1)
				return ({
					70,
					105,
					140,
					175,
					210
				})[ove_0_11(0)]
			end,
			DmgModAP = function(arg_1807_0, arg_1807_1)
				return ove_0_10.GetTotalAP(arg_1807_1) * 0.5
			end
		},
		W = {
			SpellSlot = 1,
			DamageType = "Magic",
			Range = function()
				return 500
			end,
			Delay = function()
				return 0.625
			end,
			Dmg = function(arg_1810_0, arg_1810_1)
				return ({
					70,
					105,
					140,
					175,
					210
				})[ove_0_11(1)]
			end,
			DmgModAP = function(arg_1811_0, arg_1811_1)
				return ove_0_10.GetTotalAP(arg_1811_1) * 0.6
			end
		},
		E = {
			SpellSlot = 2,
			DamageType = "Magic",
			Range = function()
				return 1500
			end,
			Delay = function()
				return 0.35
			end,
			Dmg = function(arg_1814_0, arg_1814_1)
				return ({
					80,
					120,
					160,
					200,
					240
				})[ove_0_11(2)]
			end,
			DmgModAP = function(arg_1815_0, arg_1815_1)
				return ove_0_10.GetTotalAP(arg_1815_1) * 0.3
			end
		},
		R = {
			SpellSlot = 3,
			DamageType = "Magic",
			Range = function()
				return 450
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_1818_0, arg_1818_1)
				return ({
					120,
					200,
					280
				})[ove_0_11(3)]
			end,
			DmgModAP = function(arg_1819_0, arg_1819_1)
				return ove_0_10.GetTotalAP(arg_1819_1) * 1.1
			end
		}
	},
	Renekton = {
		Passive = {
			SpellSlot = -1
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Physical",
			Range = function()
				return 400
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_1822_0, arg_1822_1)
				return ({
					60,
					90,
					120,
					150,
					180
				})[ove_0_11(0)]
			end,
			DmgModAP = function(arg_1823_0, arg_1823_1)
				return ove_0_10.GetBonusAD(arg_1823_1) * 1
			end
		},
		Q2 = {
			SpellSlot = 0,
			DamageType = "Physical",
			Range = function()
				return 400
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_1826_0, arg_1826_1)
				return ({
					100,
					150,
					200,
					250,
					300
				})[ove_0_11(0)]
			end,
			DmgModAP = function(arg_1827_0, arg_1827_1)
				return ove_0_10.GetBonusAD(arg_1827_1) * 1.2
			end
		},
		W = {
			SpellSlot = 1,
			DamageType = "Physical",
			Range = function()
				return math.huge
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_1830_0, arg_1830_1)
				return ({
					10,
					40,
					70,
					100,
					130
				})[ove_0_11(1)]
			end,
			DmgModAD = function(arg_1831_0, arg_1831_1)
				return (ove_0_10.GetTotalAD(arg_1831_1) + ove_0_10.GetBonusAD(arg_1831_1)) * 1.5
			end
		},
		W2 = {
			SpellSlot = 1,
			DamageType = "Physical",
			Range = function()
				return math.huge
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_1834_0, arg_1834_1)
				return ({
					15,
					60,
					105,
					150,
					195
				})[ove_0_11(1)]
			end,
			DmgModAD = function(arg_1835_0, arg_1835_1)
				return (ove_0_10.GetTotalAD(arg_1835_1) + ove_0_10.GetBonusAD(arg_1835_1)) * 2.25
			end
		},
		E = {
			SpellSlot = 2,
			DamageType = "Physical",
			Range = function()
				return 450
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_1838_0, arg_1838_1)
				return ({
					40,
					70,
					100,
					130,
					160
				})[ove_0_11(2)]
			end,
			DmgModAD = function(arg_1839_0, arg_1839_1)
				return ove_0_10.GetBonusAD(arg_1839_1) * 0.9
			end
		},
		E2 = {
			SpellSlot = 2,
			DamageType = "Physical",
			Range = function()
				return 450
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_1842_0, arg_1842_1)
				return ({
					80,
					140,
					200,
					260,
					320
				})[ove_0_11(2)]
			end,
			DmgModAD = function(arg_1843_0, arg_1843_1)
				return ove_0_10.GetBonusAD(arg_1843_1) * 1.8
			end
		},
		R = {
			SpellSlot = 3,
			DamageType = "Magic",
			Range = function()
				return 375
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_1846_0, arg_1846_1)
				return ({
					750,
					1500,
					2250
				})[ove_0_11(3)]
			end,
			DmgModAP = function(arg_1847_0, arg_1847_1)
				return ove_0_10.GetTotalAP(arg_1847_1) * 1.5
			end,
			DmgModAD = function(arg_1848_0, arg_1848_1)
				return ove_0_10.GetBonusAD(arg_1848_1) * 1.5
			end
		}
	},
	Rengar = {
		Passive = {
			SpellSlot = -1
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Physical",
			Range = function()
				return math.huge
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_1851_0, arg_1851_1)
				return ({
					30,
					60,
					90,
					120,
					150
				})[ove_0_11(0)]
			end,
			DmgModAD = function(arg_1852_0, arg_1852_1)
				return (ove_0_10.GetTotalAD(arg_1852_1) + ove_0_10.GetBonusAD(arg_1852_1)) * ({
					0,
					0.05,
					0.1,
					0.15,
					0.2
				})[ove_0_11(0)]
			end
		},
		Q2 = {
			SpellSlot = 0,
			DamageType = "Physical",
			Range = function()
				return math.huge
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_1855_0, arg_1855_1)
				return ({
					30,
					45,
					60,
					75,
					90,
					105,
					120,
					135,
					145,
					155,
					165,
					175,
					185,
					195,
					205,
					215,
					225,
					235
				})[player.levelRef]
			end,
			DmgModAD = function(arg_1856_0, arg_1856_1)
				return (ove_0_10.GetTotalAD(arg_1856_1) + ove_0_10.GetBonusAD(arg_1856_1)) * 0.4
			end
		},
		W = {
			SpellSlot = 1,
			DamageType = "Magic",
			Range = function()
				return 450
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_1859_0, arg_1859_1)
				return ({
					50,
					80,
					110,
					140,
					170
				})[ove_0_11(1)]
			end,
			DmgModAP = function(arg_1860_0, arg_1860_1)
				return ove_0_10.GetTotalAP(arg_1860_1) * 0.8
			end
		},
		W2 = {
			SpellSlot = 1,
			DamageType = "Magic",
			Range = function()
				return 450
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_1863_0, arg_1863_1)
				return ({
					50,
					60,
					70,
					80,
					90,
					100,
					110,
					120,
					130,
					140,
					150,
					160,
					170,
					180,
					190,
					200,
					210,
					220
				})[player.levelRef]
			end,
			DmgModAP = function(arg_1864_0, arg_1864_1)
				return ove_0_10.GetTotalAP(arg_1864_1) * 0.8
			end
		},
		E = {
			SpellSlot = 2,
			DamageType = "Physical",
			Range = function()
				return 1000
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_1867_0, arg_1867_1)
				return ({
					55,
					100,
					145,
					190,
					235
				})[ove_0_11(2)]
			end,
			DmgModAD = function(arg_1868_0, arg_1868_1)
				return ove_0_10.GetBonusAD(arg_1868_1) * 0.8
			end
		},
		E2 = {
			SpellSlot = 2,
			DamageType = "Physical",
			Range = function()
				return 1000
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_1871_0, arg_1871_1)
				return ({
					50,
					65,
					80,
					95,
					110,
					125,
					140,
					155,
					170,
					185,
					200,
					215,
					230,
					245,
					260,
					275,
					290,
					305
				})[player.levelRef]
			end,
			DmgModAD = function(arg_1872_0, arg_1872_1)
				return ove_0_10.GetBonusAD(arg_1872_1) * 0.8
			end
		},
		R = {
			SpellSlot = 3,
			Range = function()
				return ({
					2500,
					3000,
					3500
				})[ove_0_11(3)]
			end,
			Delay = function()
				return 0
			end
		}
	},
	Riven = {
		Passive = {
			SpellSlot = -1,
			DamageType = "Magic",
			DmgModAD = function(arg_1875_0, arg_1875_1)
				return ove_0_10.GetBonusAD(arg_1875_1) * ({
					0.3,
					0.3,
					0.3,
					0.3,
					0.3,
					0.36,
					0.36,
					0.36,
					0.42,
					0.42,
					0.42,
					0.48,
					0.48,
					0.48,
					0.54,
					0.54,
					0.54,
					0.6
				})[player.levelRef]
			end
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Physical",
			Range = function()
				return 150
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_1878_0, arg_1878_1)
				return ({
					45,
					105,
					165,
					225,
					285
				})[ove_0_11(0)]
			end,
			DmgModAD = function(arg_1879_0, arg_1879_1)
				return (ove_0_10.GetTotalAD(arg_1879_1) + ove_0_10.GetBonusAD(arg_1879_1)) * ({
					1.35,
					1.5,
					1.65,
					1.8,
					1.95
				})[ove_0_11(0)]
			end
		},
		W = {
			SpellSlot = 1,
			DamageType = "Physical",
			Range = function()
				return 250
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_1882_0, arg_1882_1)
				return ({
					65,
					95,
					125,
					155,
					185
				})[ove_0_11(1)]
			end,
			DmgModAD = function(arg_1883_0, arg_1883_1)
				return ove_0_10.GetBonusAD(arg_1883_1) * 1
			end
		},
		E = {
			SpellSlot = 2,
			Range = function()
				return 250
			end,
			Delay = function()
				return 0
			end
		},
		R = {
			SpellSlot = 3,
			DamageType = "Physical",
			Range = function()
				return math.huge
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_1888_0, arg_1888_1)
				return ({
					300,
					450,
					600
				})[ove_0_11(3)]
			end,
			DmgModAD = function(arg_1889_0, arg_1889_1)
				return ove_0_10.GetBonusAD(arg_1889_1) * 1.8
			end
		}
	},
	Rumble = {
		Passive = {
			SpellSlot = -1,
			DamageType = "Magic",
			Dmg = function(arg_1890_0, arg_1890_1)
				return 5 + 2.0588235294117645 * (player.levelRef - 1)
			end,
			DmgModAD = function(arg_1891_0, arg_1891_1)
				return ove_0_10.GetTotalAP(arg_1891_1) * 0.25
			end,
			DmgModHP = function(arg_1892_0, arg_1892_1)
				return arg_1892_1.maxHealth / 100 * 6
			end
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Magic",
			Range = function()
				return 600
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_1895_0, arg_1895_1)
				return ({
					180,
					220,
					260,
					300,
					340
				})[ove_0_11(0)]
			end,
			DmgModAP = function(arg_1896_0, arg_1896_1)
				return ove_0_10.GetTotalAP(arg_1896_1) * 1.1
			end
		},
		Q2 = {
			SpellSlot = 0,
			DamageType = "Magic",
			Range = function()
				return 600
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_1899_0, arg_1899_1)
				return ({
					270,
					330,
					390,
					450,
					510
				})[ove_0_11(0)]
			end,
			DmgModAP = function(arg_1900_0, arg_1900_1)
				return ove_0_10.GetTotalAP(arg_1900_1) * 1.65
			end
		},
		W = {
			SpellSlot = 1,
			Range = function()
				return math.huge
			end,
			Delay = function()
				return 0
			end
		},
		E = {
			SpellSlot = 2,
			DamageType = "Magic",
			Range = function()
				return 890
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_1905_0, arg_1905_1)
				return ({
					60,
					85,
					110,
					135,
					160
				})[ove_0_11(2)]
			end,
			DmgModAP = function(arg_1906_0, arg_1906_1)
				return ove_0_10.GetTotalAP(arg_1906_1) * 0.4
			end
		},
		E2 = {
			SpellSlot = 2,
			DamageType = "Magic",
			Range = function()
				return 890
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_1909_0, arg_1909_1)
				return ({
					90,
					127.5,
					165,
					202.5,
					240
				})[ove_0_11(2)]
			end,
			DmgModAP = function(arg_1910_0, arg_1910_1)
				return ove_0_10.GetTotalAP(arg_1910_1) * 0.6
			end
		},
		R = {
			SpellSlot = 3,
			DamageType = "Physical",
			Range = function()
				return 1700
			end,
			Delay = function()
				return 0.5
			end,
			Dmg = function(arg_1913_0, arg_1913_1)
				return ({
					700,
					1050,
					1400
				})[ove_0_11(3)]
			end,
			DmgModAP = function(arg_1914_0, arg_1914_1)
				return ove_0_10.GetTotalAP(arg_1914_1) * 1.75
			end
		}
	},
	Ryze = {
		Passive = {
			SpellSlot = -1
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Magic",
			Range = function()
				return 1000
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_1917_0, arg_1917_1)
				return ({
					70,
					90,
					110,
					130,
					150
				})[ove_0_11(0)]
			end,
			DmgModAP = function(arg_1918_0, arg_1918_1)
				return ove_0_10.GetTotalAP(arg_1918_1) * 0.5
			end
		},
		W = {
			SpellSlot = 1,
			DamageType = "Magic",
			Range = function()
				return 550
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_1921_0, arg_1921_1)
				return ({
					80,
					110,
					140,
					170,
					200
				})[ove_0_11(1)]
			end,
			DmgModAP = function(arg_1922_0, arg_1922_1)
				return ove_0_10.GetTotalAP(arg_1922_1) * 0.6
			end
		},
		E = {
			SpellSlot = 2,
			DamageType = "Magic",
			Range = function()
				return 550
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_1925_0, arg_1925_1)
				return ({
					60,
					90,
					120,
					150,
					180
				})[ove_0_11(2)]
			end,
			DmgModAP = function(arg_1926_0, arg_1926_1)
				return ove_0_10.GetTotalAP(arg_1926_1) * 0.45
			end
		},
		R = {
			SpellSlot = 3,
			Range = function()
				return 3000
			end,
			Delay = function()
				return 0
			end
		}
	},
	Samira = {
		Passive = {
			SpellSlot = -1
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Physical",
			Range = function()
				return 340
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_1931_0, arg_1931_1)
				return ({
					0,
					5,
					10,
					15,
					20
				})[ove_0_11(0)]
			end,
			DmgModAD = function(arg_1932_0, arg_1932_1)
				return (ove_0_10.GetTotalAD(arg_1932_1) + ove_0_10.GetBonusAD(arg_1932_1)) * ({
					0.85,
					0.95,
					1.05,
					1.15,
					1.25
				})[ove_0_11(0)]
			end
		},
		W = {
			SpellSlot = 1,
			DamageType = "Physical",
			Range = function()
				return 325
			end,
			Delay = function()
				return 0.1
			end,
			Dmg = function(arg_1935_0, arg_1935_1)
				return ({
					20,
					35,
					50,
					65,
					80
				})[ove_0_11(1)]
			end,
			DmgModAP = function(arg_1936_0, arg_1936_1)
				return ove_0_10.GetBonusAD(arg_1936_1) * 0.8
			end
		},
		E = {
			SpellSlot = 2,
			DamageType = "Magic",
			Range = function()
				return 600
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_1939_0, arg_1939_1)
				return ({
					50,
					60,
					70,
					80,
					90
				})[ove_0_11(2)]
			end,
			DmgModAP = function(arg_1940_0, arg_1940_1)
				return ove_0_10.GetBonusAD(arg_1940_1) * 0.2
			end
		},
		R = {
			SpellSlot = 3,
			DamageType = "Physical",
			Range = function()
				return 600
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_1943_0, arg_1943_1)
				return ({
					50,
					150,
					250
				})[ove_0_11(3)]
			end,
			DmgModAD = function(arg_1944_0, arg_1944_1)
				return (ove_0_10.GetTotalAD(arg_1944_1) + ove_0_10.GetBonusAD(arg_1944_1)) * 5
			end
		}
	},
	Sejuaini = {
		Passive = {
			SpellSlot = -1
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Magic",
			Range = function()
				return 650
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_1947_0, arg_1947_1)
				return ({
					90,
					140,
					190,
					240,
					290
				})[ove_0_11(0)]
			end,
			DmgModAP = function(arg_1948_0, arg_1948_1)
				return ove_0_10.GetTotalAP(arg_1948_1) * 0.6
			end
		},
		W = {
			SpellSlot = 1,
			DamageType = "Physical",
			Range = function()
				return 600
			end,
			Delay = function()
				return 1
			end,
			Dmg = function(arg_1951_0, arg_1951_1)
				return ({
					50,
					95,
					140,
					185,
					230
				})[ove_0_11(1)]
			end,
			DmgModAP = function(arg_1952_0, arg_1952_1)
				return ove_0_10.GetBonusAD(arg_1952_1) * 0.8
			end,
			DmgModHP = function(arg_1953_0, arg_1953_1)
				return arg_1953_1.maxHealth / 100 * 8
			end
		},
		E = {
			SpellSlot = 2,
			DamageType = "Magic",
			Range = function()
				return 600
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_1956_0, arg_1956_1)
				return ({
					55,
					105,
					155,
					205,
					255
				})[ove_0_11(2)]
			end,
			DmgModAP = function(arg_1957_0, arg_1957_1)
				return ove_0_10.GetBonusAD(arg_1957_1) * 0.6
			end
		},
		R = {
			SpellSlot = 3,
			DamageType = "Physical",
			Range = function()
				return 1300
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_1960_0, arg_1960_1)
				return ({
					200,
					300,
					400
				})[ove_0_11(3)]
			end,
			DmgModAD = function(arg_1961_0, arg_1961_1)
				return ove_0_10.GetTotalAP(arg_1961_1) * 0.8
			end
		}
	},
	Senna = {
		Passive = {
			SpellSlot = -1
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Physical",
			Range = function()
				return 600
			end,
			Delay = function()
				return 0.325
			end,
			Dmg = function(arg_1964_0, arg_1964_1)
				return ({
					30,
					65,
					100,
					135,
					170
				})[ove_0_11(0)]
			end,
			DmgModAD = function(arg_1965_0, arg_1965_1)
				return ove_0_10.GetBonusAD(arg_1965_1) * 0.5
			end
		},
		W = {
			SpellSlot = 1,
			DamageType = "Physical",
			Range = function()
				return 1300
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_1968_0, arg_1968_1)
				return ({
					70,
					115,
					160,
					205,
					250
				})[ove_0_11(1)]
			end,
			DmgModAP = function(arg_1969_0, arg_1969_1)
				return ove_0_10.GetBonusAD(arg_1969_1) * 0.7
			end
		},
		E = {
			SpellSlot = 2,
			DamageType = "Physical",
			Range = function()
				return 400
			end,
			Delay = function()
				return 1
			end,
			Dmg = function(arg_1972_0, arg_1972_1)
				return ({
					70,
					115,
					160,
					205,
					250
				})[ove_0_11(2)]
			end,
			DmgModAP = function(arg_1973_0, arg_1973_1)
				return ove_0_10.GetBonusAD(arg_1973_1) * 0.7
			end
		},
		R = {
			SpellSlot = 3,
			DamageType = "Physical",
			Range = function()
				return math.huge
			end,
			Delay = function()
				return 1
			end,
			Dmg = function(arg_1976_0, arg_1976_1)
				return ({
					250,
					375,
					500
				})[ove_0_11(3)]
			end,
			DmgModAD = function(arg_1977_0, arg_1977_1)
				return (ove_0_10.GetTotalAD(arg_1977_1) + ove_0_10.GetBonusAD(arg_1977_1)) * 1
			end,
			DmgModAP = function(arg_1978_0, arg_1978_1)
				return ove_0_10.GetTotalAP(arg_1978_1) * 0.7
			end
		}
	},
	Seraphine = {
		Passive = {
			SpellSlot = -1,
			DamageType = "Magic",
			Dmg = function()
				return ({
					4,
					4,
					4,
					4,
					4,
					8,
					8,
					8,
					8,
					8,
					14,
					14,
					14,
					14,
					14,
					24,
					24,
					24
				})[player.levelRef]
			end,
			DmgModAP = function(arg_1980_0, arg_1980_1)
				return ove_0_10.GetTotalAP(arg_1980_1) * 0.07
			end
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Magic",
			Range = function()
				return 900
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_1983_0, arg_1983_1)
				return ({
					55,
					70,
					85,
					100,
					115
				})[ove_0_11(0)]
			end,
			DmgModAP = function(arg_1984_0, arg_1984_1)
				return ove_0_10.GetTotalAP(arg_1984_1) * ({
					0.45,
					0.5,
					0.55,
					0.6,
					0.65
				})[ove_0_11(0)]
			end
		},
		W = {
			SpellSlot = 1,
			Range = function()
				return 800
			end,
			Delay = function()
				return 0.25
			end
		},
		E = {
			SpellSlot = 2,
			DamageType = "Magic",
			Range = function()
				return 1300
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_1989_0, arg_1989_1)
				return ({
					60,
					80,
					100,
					120,
					140
				})[ove_0_11(2)]
			end,
			DmgModAP = function(arg_1990_0, arg_1990_1)
				return ove_0_10.GetTotalAP(arg_1990_1) * 0.35
			end
		},
		R = {
			SpellSlot = 3,
			DamageType = "Physical",
			Range = function()
				return 1200
			end,
			Delay = function()
				return 0.5
			end,
			Dmg = function(arg_1993_0, arg_1993_1)
				return ({
					150,
					200,
					250
				})[ove_0_11(3)]
			end,
			DmgModAP = function(arg_1994_0, arg_1994_1)
				return ove_0_10.GetTotalAP(arg_1994_1) * 0.6
			end
		}
	},
	Sett = {
		Passive = {
			SpellSlot = -1,
			DamageType = "Physical",
			Dmg = function(arg_1995_0, arg_1995_1)
				return 5 * player.levelRef
			end,
			DmgModAD = function(arg_1996_0, arg_1996_1)
				return ove_0_10.GetBonusAD(arg_1996_1) * 0.5
			end
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Physical",
			Range = function()
				return math.huge
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_1999_0, arg_1999_1)
				return ({
					20,
					40,
					60,
					80,
					100
				})[ove_0_11(0)]
			end,
			DmgModAD = function(arg_2000_0, arg_2000_1)
				return arg_2000_1.maxHealth / 100 * (2 + ({
					2,
					3,
					4,
					5,
					6
				})[ove_0_11(0)] * (ove_0_10.GetTotalAD(arg_2000_1) + ove_0_10.GetBonusAD(arg_2000_1) % 100))
			end
		},
		W = {
			SpellSlot = 1,
			DamageType = "Physical",
			Range = function()
				return 725
			end,
			Delay = function()
				return 0.75
			end,
			Dmg = function(arg_2003_0, arg_2003_1)
				return ({
					80,
					100,
					120,
					140,
					160
				})[ove_0_11(1)]
			end,
			DmgModAD = function(arg_2004_0, arg_2004_1)
				return 25 + 25 * (ove_0_10.GetBonusAD(arg_2004_1) % 100)
			end
		},
		E = {
			SpellSlot = 2,
			DamageType = "Physical",
			Range = function()
				return 450
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_2007_0, arg_2007_1)
				return ({
					50,
					70,
					90,
					110,
					130
				})[ove_0_11(2)]
			end,
			DmgModAD = function(arg_2008_0, arg_2008_1)
				return (ove_0_10.GetTotalAD(arg_2008_1) + ove_0_10.GetBonusAD(arg_2008_1)) * 0.6
			end
		},
		R = {
			SpellSlot = 3,
			DamageType = "Physical",
			Range = function()
				return 400
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_2011_0, arg_2011_1)
				return ({
					200,
					300,
					400
				})[ove_0_11(3)]
			end,
			DmgModAD = function(arg_2012_0, arg_2012_1)
				return ove_0_10.GetBonusAD(arg_2012_1) * 1.2
			end,
			DmgModHP = function(arg_2013_0, arg_2013_1)
				return (T - arg_2013_1.health) / 100 * ({
					40,
					50,
					60
				})[ove_0_11(3)]
			end
		}
	},
	Shaco = {
		Passive = {
			SpellSlot = -1,
			DamageType = "Physical",
			Dmg = function(arg_2014_0, arg_2014_1)
				return 20 + 0.8823529411764706 * (player.levelRef - 1)
			end,
			DmgModAD = function(arg_2015_0, arg_2015_1)
				return ove_0_10.GetBonusAD(arg_2015_1) * 0.25
			end
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Physical",
			Range = function()
				return 400
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_2018_0, arg_2018_1)
				return ({
					25,
					35,
					45,
					55,
					65
				})[ove_0_11(0)]
			end,
			DmgModAD = function(arg_2019_0, arg_2019_1)
				return ove_0_10.GetBonusAD(arg_2019_1) * 0.5
			end
		},
		W = {
			SpellSlot = 1,
			DamageType = "Magic",
			Range = function()
				return 425
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_2022_0, arg_2022_1)
				return ({
					10,
					15,
					20,
					25,
					30
				})[ove_0_11(1)]
			end,
			DmgModAD = function(arg_2023_0, arg_2023_1)
				return ove_0_10.GetTotalAP(arg_2023_1) * 0.12
			end
		},
		E = {
			SpellSlot = 2,
			DamageType = "Magic",
			Range = function()
				return 625
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_2026_0, arg_2026_1)
				return ({
					70,
					95,
					120,
					145,
					170
				})[ove_0_11(2)]
			end,
			DmgModAD = function(arg_2027_0, arg_2027_1)
				return ove_0_10.GetBonusAD(arg_2027_1) * 0.75
			end,
			DmgModAP = function(arg_2028_0, arg_2028_1)
				return ove_0_10.GetTotalAP(arg_2028_1) * 0.6
			end
		},
		R = {
			SpellSlot = 3,
			DamageType = "Magic",
			Range = function()
				return 250
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_2031_0, arg_2031_1)
				return ({
					150,
					225,
					300
				})[ove_0_11(3)]
			end,
			DmgModAP = function(arg_2032_0, arg_2032_1)
				return ove_0_10.GetTotalAP(arg_2032_1) * 0.7
			end
		}
	},
	Shen = {
		Passive = {
			SpellSlot = -1
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Magic",
			Range = function()
				return math.huge
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_2035_0, arg_2035_1)
				return ({
					30,
					30,
					30,
					48,
					48,
					48,
					66,
					66,
					66,
					84,
					84,
					84,
					102,
					102,
					102,
					120,
					120,
					120
				})[player.levelRef]
			end,
			DmgModHP = function(arg_2036_0, arg_2036_1)
				return arg_2036_1.maxHealth / 100 * ({
					15,
					16.5,
					18,
					19.5,
					21
				})[ove_0_11(0)] + 6 * (ove_0_10.GetTotalAP(arg_2036_1) % 100)
			end
		},
		W = {
			SpellSlot = 1,
			Range = function()
				return 350
			end,
			Delay = function()
				return 0
			end
		},
		E = {
			SpellSlot = 2,
			DamageType = "Physical",
			Range = function()
				return 600
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_2041_0, arg_2041_1)
				return ({
					60,
					85,
					110,
					135,
					160
				})[ove_0_11(2)]
			end,
			DmgModHP = function(arg_2042_0, arg_2042_1)
				return arg_2042_1.maxHealth / 100 * 15
			end
		},
		R = {
			SpellSlot = 3,
			Range = function()
				return math.huge
			end,
			Delay = function()
				return 0
			end
		}
	},
	Shyvana = {
		Passive = {
			SpellSlot = -1
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Magic",
			Range = function()
				return math.huge
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_2047_0, arg_2047_1)
				return (ove_0_10.GetTotalAD(arg_2047_1) + ove_0_10.GetBonusAD(arg_2047_1)) * ({
					0.2,
					0.35,
					0.5,
					0.65,
					0.8
				})[ove_0_11(0)]
			end,
			DmgModAP = function(arg_2048_0, arg_2048_1)
				return ove_0_10.GetTotalAP(arg_2048_1) * 0.25
			end
		},
		W = {
			SpellSlot = 1,
			DamageType = "Magic",
			Range = function()
				return 162.5
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_2051_0, arg_2051_1)
				return ({
					20,
					32.5,
					45,
					57.5,
					70
				})[ove_0_11(0)]
			end,
			DmgModAD = function(arg_2052_0, arg_2052_1)
				return ove_0_10.GetBonusAD(arg_2052_1) * 0.2
			end
		},
		W2 = {
			SpellSlot = 1,
			DamageType = "Magic",
			Range = function()
				return 350
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_2055_0, arg_2055_1)
				return ({
					20,
					32.5,
					45,
					57.5,
					70
				})[ove_0_11(0)]
			end,
			DmgModAD = function(arg_2056_0, arg_2056_1)
				return ove_0_10.GetBonusAD(arg_2056_1) * 0.2
			end
		},
		E = {
			SpellSlot = 2,
			DamageType = "Magic",
			Range = function()
				return 925
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_2059_0, arg_2059_1)
				return ({
					60,
					100,
					140,
					180,
					220
				})[ove_0_11(2)]
			end,
			DmgModAD = function(arg_2060_0, arg_2060_1)
				return (ove_0_10.GetTotalAD(arg_2060_1) + ove_0_10.GetBonusAD(arg_2060_1)) * 0.3
			end,
			DmgModAP = function(arg_2061_0, arg_2061_1)
				return ove_0_10.GetTotalAP(arg_2061_1) * 0.7
			end
		},
		E2 = {
			SpellSlot = 2,
			DamageType = "Magic",
			Range = function()
				return 925
			end,
			Delay = function()
				return 0.3333
			end,
			Dmg = function(arg_2064_0, arg_2064_1)
				return ({
					0,
					0,
					0,
					0,
					0,
					100,
					105,
					110,
					115,
					120,
					125,
					130,
					135,
					140,
					145,
					150,
					155,
					160
				})[player.levelRef] + ({
					60,
					100,
					140,
					180,
					220
				})[ove_0_11(2)]
			end,
			DmgModAD = function(arg_2065_0, arg_2065_1)
				return (ove_0_10.GetTotalAD(arg_2065_1) + ove_0_10.GetBonusAD(arg_2065_1)) * 0.6
			end,
			DmgModAP = function(arg_2066_0, arg_2066_1)
				return ove_0_10.GetTotalAP(arg_2066_1) * 1
			end
		},
		R = {
			SpellSlot = 3,
			DamageType = "Magic",
			Range = function()
				return 850
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_2069_0, arg_2069_1)
				return ({
					150,
					250,
					350
				})[ove_0_11(3)]
			end,
			DmgModAP = function(arg_2070_0, arg_2070_1)
				return ove_0_10.GetTotalAP(arg_2070_1) * 1
			end
		}
	},
	Singed = {
		Passive = {
			SpellSlot = -1
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Magic",
			Range = function()
				return 180
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_2073_0, arg_2073_1)
				return ({
					40,
					60,
					80,
					100,
					120
				})[ove_0_11(0)]
			end,
			DmgModAP = function(arg_2074_0, arg_2074_1)
				return ove_0_10.GetTotalAP(arg_2074_1) * 0.9
			end
		},
		W = {
			SpellSlot = 1,
			Range = function()
				return 1000
			end,
			Delay = function()
				return 0.25
			end
		},
		E = {
			SpellSlot = 2,
			DamageType = "Magic",
			Range = function()
				return 125
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_2079_0, arg_2079_1)
				return ({
					50,
					60,
					70,
					80,
					90
				})[ove_0_11(2)]
			end,
			DmgModHP = function(arg_2080_0, arg_2080_1)
				return arg_2080_1.maxHealth / 100 * ({
					6,
					6.5,
					7,
					7.5,
					8
				})[ove_0_11(2)]
			end,
			DmgModAP = function(arg_2081_0, arg_2081_1)
				return ove_0_10.GetTotalAP(arg_2081_1) * 0.6
			end
		},
		R = {
			SpellSlot = 3,
			Range = function()
				return math.huge
			end,
			Delay = function()
				return 0
			end
		}
	},
	Sion = {
		Passive = {
			SpellSlot = -1
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Physical",
			Range = function()
				return 850
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_2086_0, arg_2086_1)
				return ({
					70,
					135,
					200,
					265,
					330
				})[ove_0_11(0)]
			end,
			DmgModAD = function(arg_2087_0, arg_2087_1)
				return (ove_0_10.GetTotalAD(arg_2087_1) + ove_0_10.GetBonusAD(arg_2087_1)) * ({
					1.35,
					1.575,
					1.8,
					2.025,
					2.25
				})[ove_0_11(0)]
			end
		},
		W = {
			SpellSlot = 1,
			DamageType = "Magic",
			Range = function()
				return 525
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_2090_0, arg_2090_1)
				return ({
					40,
					65,
					90,
					115,
					140
				})[ove_0_11(1)]
			end,
			DmgModAP = function(arg_2091_0, arg_2091_1)
				return ove_0_10.GetTotalAP(arg_2091_1) * 0.4
			end
		},
		E = {
			SpellSlot = 2,
			DamageType = "Magic",
			Range = function()
				return 800
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_2094_0, arg_2094_1)
				return ({
					65,
					100,
					135,
					170,
					205
				})[ove_0_11(2)]
			end,
			DmgModAP = function(arg_2095_0, arg_2095_1)
				return ove_0_10.GetTotalAP(arg_2095_1) * 0.55
			end
		},
		R = {
			SpellSlot = 3,
			DamageType = "Physical",
			Range = function()
				return math.huge
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_2098_0, arg_2098_1)
				return ({
					400,
					800,
					1200
				})[ove_0_11(3)]
			end,
			DmgModAD = function(arg_2099_0, arg_2099_1)
				return ove_0_10.GetBonusAD(arg_2099_1) * 0.8
			end
		}
	},
	Sivir = {
		Passive = {
			SpellSlot = -1
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Physical",
			Range = function()
				return 1250
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_2102_0, arg_2102_1)
				return ({
					30,
					60,
					90,
					120,
					150
				})[ove_0_11(0)]
			end,
			DmgModAD = function(arg_2103_0, arg_2103_1)
				return (ove_0_10.GetTotalAD(arg_2103_1) + ove_0_10.GetBonusAD(arg_2103_1)) * ({
					1.6,
					1.7,
					1.8,
					1.9,
					2
				})[ove_0_11(0)]
			end,
			DmgModAP = function(arg_2104_0, arg_2104_1)
				return ove_0_10.GetTotalAP(arg_2104_1) * 1.2
			end
		},
		W = {
			SpellSlot = 1,
			DamageType = "Physical",
			Range = function()
				return math.huge
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_2107_0, arg_2107_1)
				return (ove_0_10.GetTotalAD(arg_2107_1) + ove_0_10.GetBonusAD(arg_2107_1)) * ({
					0.25,
					0.3,
					0.35,
					0.4,
					0.45
				})[ove_0_11(1)]
			end
		},
		E = {
			SpellSlot = 2,
			Range = function()
				return math.huge
			end,
			Delay = function()
				return 0
			end
		},
		R = {
			SpellSlot = 3,
			Range = function()
				return 1000
			end,
			Delay = function()
				return 0.25
			end
		}
	},
	Skarner = {
		Passive = {
			SpellSlot = -1
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Physical",
			Range = function()
				return 350
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_2114_0, arg_2114_1)
				return arg_2114_1.maxHealth / 100 * ({
					1,
					1.5,
					2,
					2.5,
					3
				})[ove_0_11(0)] + (ove_0_10.GetTotalAD(arg_2114_1) + ove_0_10.GetBonusAD(arg_2114_1)) * 0.2
			end
		},
		Q2 = {
			SpellSlot = 0,
			DamageType = "Physical",
			Range = function()
				return 350
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_2117_0, arg_2117_1)
				return arg_2117_1.maxHealth / 100 * ({
					2,
					3,
					4,
					5,
					6
				})[ove_0_11(0)] + ((ove_0_10.GetTotalAD(arg_2117_1) + ove_0_10.GetBonusAD(arg_2117_1)) * 0.2 + ove_0_10.GetTotalAP(arg_2117_1) * 0.3)
			end
		},
		W = {
			SpellSlot = 1,
			Range = function()
				return math.huge
			end,
			Delay = function()
				return 0
			end
		},
		E = {
			SpellSlot = 2,
			DamageType = "Magic",
			Range = function()
				return 1000
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_2122_0, arg_2122_1)
				return ({
					40,
					65,
					90,
					115,
					140
				})[ove_0_11(0)]
			end,
			DmgModAP = function(arg_2123_0, arg_2123_1)
				return ove_0_10.GetTotalAP(arg_2123_1) * 0.2
			end
		},
		R = {
			SpellSlot = 3,
			DamageType = "Physical",
			Range = function()
				return 350
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_2126_0, arg_2126_1)
				return ({
					40,
					120,
					200
				})[ove_0_11(3)]
			end,
			DmgModAD = function(arg_2127_0, arg_2127_1)
				return (ove_0_10.GetTotalAD(arg_2127_1) + ove_0_10.GetBonusAD(arg_2127_1)) * 1.2
			end,
			DmgModAP = function(arg_2128_0, arg_2128_1)
				return ove_0_10.GetTotalAP(arg_2128_1) * 1
			end
		}
	},
	Sona = {
		Passive = {
			SpellSlot = -1
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Magic",
			Range = function()
				return 825
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_2131_0, arg_2131_1)
				return ({
					40,
					70,
					100,
					130,
					160
				})[ove_0_11(0)]
			end,
			DmgModAP = function(arg_2132_0, arg_2132_1)
				return ove_0_10.GetTotalAP(arg_2132_1) * 0.4
			end
		},
		Q2 = {
			SpellSlot = 0,
			DamageType = "Magic",
			Range = function()
				return 400
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_2135_0, arg_2135_1)
				return ({
					10,
					15,
					20,
					25,
					30
				})[ove_0_11(0)]
			end,
			DmgModAP = function(arg_2136_0, arg_2136_1)
				return ove_0_10.GetTotalAP(arg_2136_1) * 0.2
			end
		},
		W = {
			SpellSlot = 1,
			Range = function()
				return 1000
			end,
			Delay = function()
				return 0
			end
		},
		W2 = {
			SpellSlot = 1,
			Range = function()
				return 400
			end,
			Delay = function()
				return 0
			end
		},
		E = {
			SpellSlot = 2,
			Range = function()
				return 400
			end,
			Delay = function()
				return 0
			end
		},
		R = {
			SpellSlot = 3,
			DamageType = "Physical",
			Range = function()
				return 1000
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_2145_0, arg_2145_1)
				return ({
					150,
					250,
					350
				})[ove_0_11(3)]
			end,
			DmgModAP = function(arg_2146_0, arg_2146_1)
				return ove_0_10.GetTotalAP(arg_2146_1) * 0.5
			end
		}
	},
	Soraka = {
		Passive = {
			SpellSlot = -1
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Magic",
			Range = function()
				return 800
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_2149_0, arg_2149_1)
				return ({
					85,
					120,
					155,
					190,
					225
				})[ove_0_11(0)]
			end,
			DmgModAP = function(arg_2150_0, arg_2150_1)
				return ove_0_10.GetTotalAP(arg_2150_1) * 0.35
			end
		},
		W = {
			SpellSlot = 1,
			Range = function()
				return 550
			end,
			Delay = function()
				return 0.25
			end
		},
		E = {
			SpellSlot = 2,
			DamageType = "Magic",
			Range = function()
				return 925
			end,
			Delay = function()
				return 0.4
			end,
			Dmg = function(arg_2155_0, arg_2155_1)
				return ({
					70,
					95,
					120,
					145,
					170
				})[ove_0_11(2)]
			end,
			DmgModAP = function(arg_2156_0, arg_2156_1)
				return ove_0_10.GetTotalAP(arg_2156_1) * 0.4
			end
		},
		R = {
			SpellSlot = 3,
			Range = function()
				return math.huge
			end,
			Delay = function()
				return 0.25
			end
		}
	},
	Swain = {
		Passive = {
			SpellSlot = -1
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Magic",
			Range = function()
				return 725
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_2161_0, arg_2161_1)
				return ({
					108,
					168,
					228,
					288,
					348
				})[ove_0_11(0)]
			end,
			DmgModAP = function(arg_2162_0, arg_2162_1)
				return ove_0_10.GetTotalAP(arg_2162_1) * 0.7
			end
		},
		W = {
			SpellSlot = 1,
			DamageType = "Magic",
			Range = function()
				return ({
					5500,
					6000,
					6500,
					7000,
					7500
				})[ove_0_11(1)]
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_2165_0, arg_2165_1)
				return ({
					80,
					115,
					150,
					185,
					220
				})[ove_0_11(1)]
			end,
			DmgModAP = function(arg_2166_0, arg_2166_1)
				return ove_0_10.GetTotalAP(arg_2166_1) * 0.55
			end
		},
		E = {
			SpellSlot = 2,
			DamageType = "Magic",
			Range = function()
				return 850
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_2169_0, arg_2169_1)
				return ({
					70,
					115,
					160,
					205,
					250
				})[ove_0_11(2)]
			end,
			DmgModAP = function(arg_2170_0, arg_2170_1)
				return ove_0_10.GetTotalAP(arg_2170_1) * 0.5
			end
		},
		R = {
			SpellSlot = 3,
			DamageType = "Magic",
			Range = function()
				return 650
			end,
			Delay = function()
				return 0.5
			end,
			Dmg = function(arg_2173_0, arg_2173_1)
				return ({
					420,
					600,
					780
				})[ove_0_11(3)] + ove_0_10.GetTotalAP(arg_2173_1) * 1.7 + ({
					200,
					300,
					400
				})[ove_0_11(3)] + ove_0_10.GetTotalAP(arg_2173_1) * 1
			end
		}
	},
	Sylas = {
		Passive = {
			SpellSlot = -1
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Magic",
			Range = function()
				return 775
			end,
			Delay = function()
				return 0.4
			end,
			Dmg = function(arg_2176_0, arg_2176_1)
				return ({
					110,
					185,
					260,
					335,
					410
				})[ove_0_11(0)]
			end,
			DmgModAP = function(arg_2177_0, arg_2177_1)
				return ove_0_10.GetTotalAP(arg_2177_1) * 1.3
			end
		},
		W = {
			SpellSlot = 1,
			DamageType = "Magic",
			Range = function()
				return 400
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_2180_0, arg_2180_1)
				return ({
					70,
					105,
					140,
					175,
					210
				})[ove_0_11(1)]
			end,
			DmgModAP = function(arg_2181_0, arg_2181_1)
				return ove_0_10.GetTotalAP(arg_2181_1) * 0.9
			end
		},
		E = {
			DamageType = "Magic",
			SpellSlot = 2,
			Range = function()
				return 400
			end,
			Delay = function()
				return 0
			end
		},
		E2 = {
			SpellSlot = 2,
			DamageType = "Magic",
			Range = function()
				return 950
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_2186_0, arg_2186_1)
				return ({
					80,
					130,
					180,
					230,
					280
				})[ove_0_11(2)]
			end,
			DmgModAP = function(arg_2187_0, arg_2187_1)
				return ove_0_10.GetTotalAP(arg_2187_1) * 1
			end
		},
		R = {
			SpellSlot = 3,
			Range = function()
				return 550
			end,
			Delay = function()
				return 0.25
			end
		}
	},
	Syndra = {
		Passive = {
			SpellSlot = -1
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Magic",
			Range = function()
				return 800
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_2192_0, arg_2192_1)
				return ({
					70,
					105,
					140,
					175,
					210
				})[ove_0_11(0)]
			end,
			DmgModAP = function(arg_2193_0, arg_2193_1)
				return ove_0_10.GetTotalAP(arg_2193_1) * 0.65
			end
		},
		Q2 = {
			SpellSlot = 0,
			DamageType = "Magic",
			Range = function()
				return 800
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_2196_0, arg_2196_1)
				return 262.5
			end,
			DmgModAP = function(arg_2197_0, arg_2197_1)
				return ove_0_10.GetTotalAP(arg_2197_1) * 0.8125
			end
		},
		W = {
			SpellSlot = 1,
			DamageType = "Magic",
			Range = function()
				return 925
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_2200_0, arg_2200_1)
				return ({
					70,
					110,
					150,
					190,
					230
				})[ove_0_11(1)]
			end,
			DmgModAP = function(arg_2201_0, arg_2201_1)
				return ove_0_10.GetTotalAP(arg_2201_1) * 0.7
			end
		},
		W2 = {
			SpellSlot = 1,
			DamageType = "Magic",
			Range = function()
				return 925
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_2204_0, arg_2204_1)
				return 276
			end,
			DmgModAP = function(arg_2205_0, arg_2205_1)
				return ove_0_10.GetTotalAP(arg_2205_1) * 0.7
			end
		},
		E = {
			SpellSlot = 2,
			DamageType = "Magic",
			Range = function()
				return 700
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_2208_0, arg_2208_1)
				return ({
					85,
					130,
					175,
					220,
					265
				})[ove_0_11(2)]
			end,
			DmgModAP = function(arg_2209_0, arg_2209_1)
				return ove_0_10.GetTotalAP(arg_2209_1) * 0.6
			end
		},
		E2 = {
			SpellSlot = 2,
			DamageType = "Magic",
			Range = function()
				return 800
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_2212_0, arg_2212_1)
				return ({
					85,
					130,
					175,
					220,
					265
				})[ove_0_11(2)]
			end,
			DmgModAP = function(arg_2213_0, arg_2213_1)
				return ove_0_10.GetTotalAP(arg_2213_1) * 0.6
			end
		},
		R = {
			SpellSlot = 3,
			DamageType = "Magic",
			Range = function()
				return 675
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_2216_0, arg_2216_1)
				return ({
					630,
					980,
					1330
				})[ove_0_11(3)]
			end,
			DmgModAP = function(arg_2217_0, arg_2217_1)
				return ove_0_10.GetTotalAP(arg_2217_1) * 1.4
			end
		},
		R2 = {
			SpellSlot = 3,
			DamageType = "Magic",
			Range = function()
				return 750
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_2220_0, arg_2220_1)
				return ({
					630,
					980,
					1330
				})[ove_0_11(3)]
			end,
			DmgModAP = function(arg_2221_0, arg_2221_1)
				return ove_0_10.GetTotalAP(arg_2221_1) * 1.4
			end
		}
	},
	TahmKench = {
		Passive = {
			SpellSlot = -1,
			DamageType = "Magic",
			Dmg = function(arg_2222_0, arg_2222_1)
				return 8 + 3.0588235294117645 * (player.levelRef - 1)
			end,
			DmgModHP = function(arg_2223_0, arg_2223_1)
				return arg_2223_1.health / 100 * 3
			end
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Magic",
			Range = function()
				return 900
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_2226_0, arg_2226_1)
				return ({
					80,
					130,
					180,
					230,
					280
				})[ove_0_11(0)] + ove_0_10.GetTotalAP(arg_2226_1) * 0.7
			end,
			DmgModAP = function(arg_2227_0, arg_2227_1)
				return 8 + 3.0588235294117645 * (player.levelRef - 1)
			end,
			DmgModHP = function(arg_2228_0, arg_2228_1)
				return arg_2228_1.health / 100 * 3
			end
		},
		W = {
			SpellSlot = 1,
			DamageType = "Magic",
			Range = function()
				return ({
					1000,
					1050,
					1100,
					1150,
					1200
				})[ove_0_11(1)]
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_2231_0, arg_2231_1)
				return ({
					100,
					135,
					170,
					205,
					240
				})[ove_0_11(1)]
			end,
			DmgModAP = function(arg_2232_0, arg_2232_1)
				return ove_0_10.GetTotalAP(arg_2232_1) * 1
			end
		},
		E = {
			SpellSlot = 2,
			Range = function()
				return math.huge
			end,
			Delay = function()
				return 0.25
			end
		},
		R = {
			SpellSlot = 3,
			DamageType = "Magic",
			Range = function()
				return 250
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_2237_0, arg_2237_1)
				return ({
					100,
					250,
					400
				})[ove_0_11(3)]
			end,
			DmgModHP = function(arg_2238_0, arg_2238_1)
				return arg_2238_1.maxHealth / 100 * (15 + 5 * (ove_0_10.GetTotalAP(arg_2238_1) % 100))
			end
		}
	},
	Taliyah = {
		Passive = {
			SpellSlot = -1
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Magic",
			Range = function()
				return 1000
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_2241_0, arg_2241_1)
				return ({
					210,
					285,
					360,
					435,
					510
				})[ove_0_11(0)]
			end,
			DmgModAP = function(arg_2242_0, arg_2242_1)
				return ove_0_10.GetTotalAP(arg_2242_1) * 1.35
			end
		},
		W = {
			SpellSlot = 1,
			DamageType = "Magic",
			Range = function()
				return 900
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_2245_0, arg_2245_1)
				return ({
					60,
					80,
					100,
					120,
					140
				})[ove_0_11(1)]
			end,
			DmgModAP = function(arg_2246_0, arg_2246_1)
				return ove_0_10.GetTotalAP(arg_2246_1) * 0.4
			end
		},
		E = {
			SpellSlot = 2,
			DamageType = "Magic",
			Range = function()
				return 800
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_2249_0, arg_2249_1)
				return ({
					60,
					105,
					150,
					195,
					240
				})[ove_0_11(2)]
			end,
			DmgModAP = function(arg_2250_0, arg_2250_1)
				return ove_0_10.GetTotalAP(arg_2250_1) * 0.6
			end
		},
		R = {
			SpellSlot = 3,
			Range = function()
				return ({
					2500,
					4500,
					6000
				})[ove_0_11(3)]
			end,
			Delay = function()
				return 0.25
			end
		}
	},
	Talon = {
		Passive = {
			SpellSlot = -1
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Physical",
			Range = function()
				return 575
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_2255_0, arg_2255_1)
				return ({
					65,
					85,
					105,
					125,
					145
				})[ove_0_11(0)]
			end,
			DmgModAD = function(arg_2256_0, arg_2256_1)
				return ove_0_10.GetBonusAD(arg_2256_1) * 1
			end
		},
		Q2 = {
			SpellSlot = 0,
			DamageType = "Physical",
			Range = function()
				return 170
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_2259_0, arg_2259_1)
				return ({
					65,
					85,
					105,
					125,
					145
				})[ove_0_11(0)]
			end,
			DmgModAD = function(arg_2260_0, arg_2260_1)
				return ove_0_10.GetBonusAD(arg_2260_1) * 1
			end
		},
		W = {
			SpellSlot = 1,
			DamageType = "Physical",
			Range = function()
				return 900
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_2263_0, arg_2263_1)
				return ({
					90,
					130,
					170,
					210,
					250
				})[ove_0_11(1)]
			end,
			DmgModAP = function(arg_2264_0, arg_2264_1)
				return ove_0_10.GetTotalAP(arg_2264_1) * 1.2
			end
		},
		E = {
			SpellSlot = 2,
			Range = function()
				return 725
			end,
			Delay = function()
				return 0
			end
		},
		R = {
			SpellSlot = 3,
			DamageType = "Physical",
			Range = function()
				return 550
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_2269_0, arg_2269_1)
				return ({
					180,
					270,
					360
				})[ove_0_11(3)]
			end,
			DmgModAD = function(arg_2270_0, arg_2270_1)
				return ove_0_10.GetBonusAD(arg_2270_1) * 2
			end
		}
	},
	Taric = {
		Passive = {
			amageType = "Magic",
			SpellSlot = -1,
			Dmg = function(arg_2271_0, arg_2271_1)
				return 21 + 4 * player.levelRef
			end
		},
		Q = {
			SpellSlot = 0,
			Range = function()
				return 325
			end,
			Delay = function()
				return 0.25
			end
		},
		W = {
			SpellSlot = 1,
			Range = function()
				return 800
			end,
			Delay = function()
				return 0.25
			end
		},
		E = {
			SpellSlot = 2,
			DamageType = "Physical",
			Range = function()
				return 575
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_2278_0, arg_2278_1)
				return ({
					90,
					130,
					170,
					210,
					250
				})[ove_0_11(2)]
			end,
			DmgModAP = function(arg_2279_0, arg_2279_1)
				return ove_0_10.GetTotalAP(arg_2279_1) * 0.5
			end
		},
		R = {
			SpellSlot = 3,
			Range = function()
				return 400
			end,
			Delay = function()
				return 0.25
			end
		}
	},
	Teemo = {
		Passive = {
			SpellSlot = -1
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Magic",
			Range = function()
				return 680
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_2284_0, arg_2284_1)
				return ({
					80,
					125,
					170,
					215,
					260
				})[ove_0_11(0)]
			end,
			DmgModAP = function(arg_2285_0, arg_2285_1)
				return ove_0_10.GetTotalAP(arg_2285_1) * 0.8
			end
		},
		W = {
			SpellSlot = 1,
			Range = function()
				return math.huge
			end,
			Delay = function()
				return 0
			end
		},
		E = {
			SpellSlot = 2,
			DamageType = "Magic",
			Range = function()
				return math.huge
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_2290_0, arg_2290_1)
				return ({
					24,
					48,
					72,
					96,
					120
				})[ove_0_11(2)]
			end,
			DmgModAP = function(arg_2291_0, arg_2291_1)
				return ove_0_10.GetTotalAP(arg_2291_1) * 0.4
			end
		},
		R = {
			SpellSlot = 3,
			DamageType = "Magic",
			Range = function()
				return ({
					400,
					650,
					900
				})[ove_0_11(3)]
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_2294_0, arg_2294_1)
				return ({
					200,
					325,
					450
				})[ove_0_11(3)]
			end,
			DmgModAP = function(arg_2295_0, arg_2295_1)
				return ove_0_10.GetTotalAP(arg_2295_1) * 0.55
			end
		}
	},
	Thresh = {
		Passive = {
			SpellSlot = -1
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Magic",
			Range = function()
				return 1040
			end,
			Delay = function()
				return 0.5
			end,
			Dmg = function(arg_2298_0, arg_2298_1)
				return ({
					100,
					140,
					180,
					220,
					260
				})[ove_0_11(0)]
			end,
			DmgModAP = function(arg_2299_0, arg_2299_1)
				return ove_0_10.GetTotalAP(arg_2299_1) * 0.5
			end
		},
		W = {
			SpellSlot = 1,
			Range = function()
				return 950
			end,
			Delay = function()
				return 0
			end
		},
		E = {
			SpellSlot = 2,
			DamageType = "Magic",
			Range = function()
				return 537.5
			end,
			Delay = function()
				return 0.3889
			end,
			Dmg = function(arg_2304_0, arg_2304_1)
				return ({
					65,
					95,
					125,
					155,
					185
				})[ove_0_11(2)]
			end,
			DmgModAP = function(arg_2305_0, arg_2305_1)
				return ove_0_10.GetTotalAP(arg_2305_1) * 0.4
			end
		},
		R = {
			SpellSlot = 3,
			DamageType = "Magic",
			Range = function(arg_2306_0, arg_2306_1)
				return arg_2306_1.AttackRange
			end,
			Delay = function()
				return 0.45
			end,
			Dmg = function(arg_2308_0, arg_2308_1)
				return ({
					250,
					400,
					550
				})[ove_0_11(3)]
			end,
			DmgModAP = function(arg_2309_0, arg_2309_1)
				return ove_0_10.GetTotalAP(arg_2309_1) * 1
			end
		}
	},
	Tristana = {
		Passive = {
			SpellSlot = -1,
			DamageType = "Magic",
			Dmg = function()
				return 8 + 8 * player.levelRef
			end
		},
		Q = {
			SpellSlot = 0
		},
		W = {
			SpellSlot = 1,
			DamageType = "Magic",
			Range = function()
				return 900
			end,
			Width = function()
				return 350
			end,
			Speed = function()
				return 1100
			end,
			Delay = function()
				return 0.9
			end,
			Dmg = function()
				return ({
					95,
					145,
					195,
					245,
					295
				})[ove_0_11(1)]
			end,
			DmgModAP = function()
				return 0.5
			end,
			DmgModAPBase = function(arg_2317_0, arg_2317_1)
				return ove_0_10.GetTotalAP(arg_2317_1)
			end
		},
		E1 = {
			DamageType = "Magic",
			SpellSlot = 2,
			Range = function()
				return 517 + 8 * player.levelRef
			end,
			Width = function()
				return 300
			end,
			Speed = function()
				return 2400
			end,
			Delay = function()
				return 0.226
			end,
			Dmg = function()
				return ({
					55,
					80,
					105,
					130,
					155
				})[ove_0_11(2)]
			end,
			DmgModAP = function()
				return 0.25
			end,
			DmgModAPBase = function(arg_2324_0, arg_2324_1)
				return ove_0_10.GetTotalAP(arg_2324_1)
			end
		},
		E2 = {
			DamageType = "Physical",
			SpellSlot = 2,
			Range = function()
				return 517 + 8 * player.levelRef
			end,
			Width = function()
				return 300
			end,
			Speed = function()
				return 2400
			end,
			Delay = function()
				return 0.226
			end,
			Dmg = function()
				return ({
					70,
					80,
					90,
					100,
					110
				})[ove_0_11(2)]
			end,
			DmgModAD = function()
				return ({
					0.5,
					0.75,
					1,
					1.25,
					1.5
				})[ove_0_11(2)]
			end,
			DmgModADBase = function(arg_2331_0, arg_2331_1)
				return ove_0_10.GetBonusAD(arg_2331_1)
			end,
			DmgModAP = function()
				return 0.5
			end,
			DmgModAPBase = function(arg_2333_0, arg_2333_1)
				return ove_0_10.GetTotalAP(arg_2333_1)
			end
		},
		EStack = {
			SpellSlot = 2,
			DamageType = "Physical",
			Dmg = function()
				return ({
					21,
					24,
					27,
					30,
					33
				})[ove_0_11(2)]
			end,
			DmgModAD = function()
				return ({
					0.15,
					0.225,
					0.3,
					0.375,
					0.45
				})[ove_0_11(2)]
			end,
			DmgModADBase = function(arg_2336_0, arg_2336_1)
				return ove_0_10.GetBonusAD(arg_2336_1)
			end,
			DmgModAP = function()
				return 0.15
			end,
			DmgModAPBase = function(arg_2338_0, arg_2338_1)
				return ove_0_10.GetTotalAP(arg_2338_1)
			end
		},
		R = {
			DamageType = "Magic",
			SpellSlot = 3,
			Range = function()
				return 517 + 8 * player.levelRef
			end,
			Width = function()
				return 200
			end,
			Speed = function()
				return 2000
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function()
				return ({
					300,
					400,
					500
				})[ove_0_11(3)]
			end,
			DmgModAP = function()
				return 1
			end,
			DmgModAPBase = function(arg_2345_0, arg_2345_1)
				return ove_0_10.GetTotalAP(arg_2345_1)
			end,
			KnockBackDistance = function()
				return ({
					600,
					800,
					1000
				})[ove_0_11(3)]
			end
		}
	},
	Trundle = {
		Passive = {
			SpellSlot = -1
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Magic",
			Range = function(arg_2347_0, arg_2347_1)
				return arg_2347_1.AttackRange
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_2349_0, arg_2349_1)
				return ({
					20,
					40,
					60,
					80,
					100
				})[ove_0_11(0)]
			end,
			DmgModAD = function(arg_2350_0, arg_2350_1)
				return (ove_0_10.GetTotalAD(arg_2350_1) + ove_0_10.GetBonusAD(arg_2350_1)) * ({
					0.15,
					0.25,
					0.35,
					0.45,
					0.55
				})[ove_0_11(0)]
			end
		},
		W = {
			SpellSlot = 1,
			Range = function()
				return 750
			end,
			Delay = function()
				return 0
			end
		},
		E = {
			SpellSlot = 2,
			Range = function()
				return 1000
			end,
			Delay = function()
				return 0.25
			end
		},
		R = {
			SpellSlot = 3,
			DamageType = "Magic",
			Range = function()
				return 650
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_2357_0, arg_2357_1)
				return arg_2357_1.maxHealth / 100 * (({
					20,
					27.5,
					35
				})[ove_0_11(3)] + 2 * (ove_0_10.GetTotalAP(arg_2357_1) % 100))
			end,
			DmgModAP = function(arg_2358_0, arg_2358_1)
				return ove_0_10.GetTotalAP(arg_2358_1) * 1
			end
		}
	},
	Tryndamere = {
		Passive = {
			SpellSlot = -1
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Physical",
			Range = function(arg_2359_0, arg_2359_1)
				return arg_2359_1.AttackRange
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_2361_0, arg_2361_1)
				return ({
					25,
					40,
					55,
					70,
					85
				})[ove_0_11(0)]
			end
		},
		W = {
			SpellSlot = 1,
			Range = function()
				return 850
			end,
			Delay = function()
				return 0.3
			end
		},
		E = {
			SpellSlot = 2,
			DamageType = "Physical",
			Range = function()
				return 660
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_2366_0, arg_2366_1)
				return ({
					80,
					110,
					140,
					170,
					200
				})[ove_0_11(2)]
			end,
			DmgModAD = function(arg_2367_0, arg_2367_1)
				return ove_0_10.GetBonusAD(arg_2367_1) * 1.3
			end,
			DmgModAP = function(arg_2368_0, arg_2368_1)
				return ove_0_10.GetTotalAP(arg_2368_1) * 0.8
			end
		},
		R = {
			SpellSlot = 3,
			Range = function(arg_2369_0, arg_2369_1)
				return arg_2369_1.AttackRange
			end,
			Delay = function()
				return 0.25
			end
		}
	},
	TwistedFate = {
		Passive = {
			SpellSlot = -1
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Magic",
			Range = function()
				return 1450
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_2373_0, arg_2373_1)
				return ({
					60,
					100,
					140,
					180,
					220
				})[ove_0_11(0)]
			end,
			DmgModAP = function(arg_2374_0, arg_2374_1)
				return ove_0_10.GetTotalAP(arg_2374_1) * 0.8
			end
		},
		WBlue = {
			SpellSlot = 1,
			DamageType = "Magic",
			Range = function(arg_2375_0, arg_2375_1)
				return arg_2375_1.AttackRange
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_2377_0, arg_2377_1)
				return ({
					40,
					60,
					80,
					100,
					120
				})[ove_0_11(1)]
			end,
			DmgModAD = function(arg_2378_0, arg_2378_1)
				return (ove_0_10.GetTotalAD(arg_2378_1) + ove_0_10.GetBonusAD(arg_2378_1)) * 1
			end,
			DmgModAP = function(arg_2379_0, arg_2379_1)
				return ove_0_10.GetTotalAP(arg_2379_1) * 0.9
			end
		},
		WRed = {
			SpellSlot = 1,
			DamageType = "Magic",
			Range = function(arg_2380_0, arg_2380_1)
				return arg_2380_1.AttackRange
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_2382_0, arg_2382_1)
				return ({
					30,
					45,
					60,
					75,
					90
				})[ove_0_11(1)]
			end,
			DmgModAD = function(arg_2383_0, arg_2383_1)
				return (ove_0_10.GetTotalAD(arg_2383_1) + ove_0_10.GetBonusAD(arg_2383_1)) * 1
			end,
			DmgModAP = function(arg_2384_0, arg_2384_1)
				return ove_0_10.GetTotalAP(arg_2384_1) * 0.6
			end
		},
		WYellow = {
			SpellSlot = 1,
			DamageType = "Magic",
			Range = function(arg_2385_0, arg_2385_1)
				return arg_2385_1.AttackRange
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_2387_0, arg_2387_1)
				return ({
					15,
					22.5,
					30,
					37.5,
					45
				})[ove_0_11(1)]
			end,
			DmgModAD = function(arg_2388_0, arg_2388_1)
				return (ove_0_10.GetTotalAD(arg_2388_1) + ove_0_10.GetBonusAD(arg_2388_1)) * 1
			end,
			DmgModAP = function(arg_2389_0, arg_2389_1)
				return ove_0_10.GetTotalAP(arg_2389_1) * 0.5
			end
		},
		E = {
			SpellSlot = 2,
			DamageType = "Physical",
			Range = function(arg_2390_0, arg_2390_1)
				return arg_2390_1.AttackRange
			end,
			Delay = function()
				return 0.4
			end,
			Dmg = function(arg_2392_0, arg_2392_1)
				return ({
					65,
					90,
					115,
					140,
					165
				})[ove_0_11(2)]
			end,
			DmgModAP = function(arg_2393_0, arg_2393_1)
				return ove_0_10.GetTotalAP(arg_2393_1) * 0.5
			end
		},
		R = {
			SpellSlot = 3,
			Range = function()
				return 5500
			end,
			Delay = function()
				return 0
			end
		}
	},
	Twitch = {
		PassiveStack = {
			SpellSlot = -1,
			DamageType = "True",
			Dmg = function()
				return 1 + math.floor(player.levelRef / 4)
			end,
			DmgModAP = function()
				return 0.025
			end,
			DmgModAPBase = function(arg_2398_0, arg_2398_1)
				return ove_0_10.GetTotalAP(arg_2398_1)
			end
		},
		Q = {
			SpellSlot = 0,
			DetectionRadius = function()
				return 500
			end
		},
		W = {
			SpellSlot = 1,
			Range = function()
				return 950
			end,
			Speed = function()
				return 1400
			end,
			Delay = function()
				return 0.25
			end,
			Width = function()
				return 175
			end
		},
		E = {
			DamageType = "Physical",
			Dmg = function()
				return ({
					20,
					30,
					40,
					50,
					60
				})[ove_0_11(2)]
			end,
			Range = function()
				return 1200
			end,
			Delay = function()
				return 0.25
			end
		},
		EStack1 = {
			DamageType = "Physical",
			Dmg = function()
				return ({
					15,
					20,
					25,
					30,
					35
				})[ove_0_11(2)]
			end,
			DmgModAD = function()
				return 0.35
			end,
			DmgModADBase = function(arg_2409_0, arg_2409_1)
				return ove_0_10.GetBonusAD(arg_2409_1)
			end
		},
		EStack2 = {
			DamageType = "Magic",
			DmgModAPBase = function(arg_2410_0, arg_2410_1)
				return ove_0_10.GetTotalAP(arg_2410_1)
			end,
			DmgModAP = function()
				return 0.33
			end
		},
		R = {
			DamageType = "Physical",
			Dmg = function()
				return {
					40,
					55,
					70
				}
			end,
			Speed = function()
				return 4000
			end,
			Delay = function()
				return 0
			end,
			Width = function()
				return 120
			end,
			Range = function()
				return 1100
			end
		}
	},
	Udyr = {
		Passive = {
			SpellSlot = -1
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Physical",
			Range = function()
				return 450
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_2419_0, arg_2419_1)
				return arg_2419_1.maxHealth / 100 * ({
					6,
					8.8,
					11.6,
					14.4,
					17.2,
					20
				})[ove_0_11(0)] + 12 * (ove_0_10.GetBonusAD(arg_2419_1) % 100)
			end,
			DmgModAD = function(arg_2420_0, arg_2420_1)
				return ({
					5,
					13,
					21,
					29,
					37,
					45
				})[ove_0_11(0)]
			end,
			DmgModAD2 = function(arg_2421_0, arg_2421_1)
				return ove_0_10.GetBonusAD(arg_2421_1) * 0.2
			end
		},
		W = {
			SpellSlot = 1,
			Range = function(arg_2422_0, arg_2422_1)
				return arg_2422_1.AttackRange
			end,
			Delay = function()
				return 0
			end
		},
		E = {
			SpellSlot = 2,
			Range = function(arg_2424_0, arg_2424_1)
				return arg_2424_1.AttackRange
			end,
			Delay = function()
				return 0
			end
		},
		R = {
			SpellSlot = 3,
			DamageType = "Magic",
			Range = function(arg_2426_0, arg_2426_1)
				return 370
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_2428_0, arg_2428_1)
				return ({
					80,
					152,
					224,
					296,
					368,
					440
				})[ove_0_11(3)]
			end,
			DmgModAP = function(arg_2429_0, arg_2429_1)
				return ove_0_10.GetTotalAP(arg_2429_1) * 1.6
			end
		}
	},
	Urgot = {
		Passive = {
			SpellSlot = -1
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Physical",
			Range = function()
				return 800
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_2432_0, arg_2432_1)
				return ({
					25,
					70,
					115,
					160,
					205
				})[ove_0_11(0)]
			end,
			DmgModAD = function(arg_2433_0, arg_2433_1)
				return (ove_0_10.GetTotalAD(arg_2433_1) + ove_0_10.GetBonusAD(arg_2433_1)) * 0.7
			end
		},
		W = {
			SpellSlot = 1,
			DamageType = "Physical",
			Range = function()
				return 490
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_2436_0, arg_2436_1)
				return 12
			end,
			DmgModAD = function(arg_2437_0, arg_2437_1)
				return (ove_0_10.GetTotalAD(arg_2437_1) + ove_0_10.GetBonusAD(arg_2437_1)) * ({
					0.2,
					0.235,
					0.27,
					0.305,
					0.34
				})[ove_0_11(1)]
			end
		},
		E = {
			SpellSlot = 2,
			DamageType = "Physical",
			Range = function()
				return 450
			end,
			Delay = function()
				return 0.45
			end,
			Dmg = function(arg_2440_0, arg_2440_1)
				return ({
					90,
					120,
					150,
					180,
					210
				})[ove_0_11(1)]
			end,
			DmgModAD = function(arg_2441_0, arg_2441_1)
				return (ove_0_10.GetTotalAD(arg_2441_1) + ove_0_10.GetBonusAD(arg_2441_1)) * 1
			end
		},
		R = {
			SpellSlot = 3,
			DamageType = "Magic",
			Range = function()
				return 2500
			end,
			Delay = function()
				return 0.5
			end,
			Dmg = function(arg_2444_0, arg_2444_1)
				return ({
					100,
					225,
					350
				})[ove_0_11(3)]
			end,
			DmgModAP = function(arg_2445_0, arg_2445_1)
				return ove_0_10.GetBonusAD(arg_2445_1) * 0.5
			end
		}
	},
	Varus = {
		Passive = {
			SpellSlot = -1
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Physical",
			Range = function()
				return 1595
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_2448_0, arg_2448_1)
				return ({
					15,
					70,
					125,
					180,
					235
				})[ove_0_11(0)]
			end,
			DmgModAD = function(arg_2449_0, arg_2449_1)
				return (ove_0_10.GetTotalAD(arg_2449_1) + ove_0_10.GetBonusAD(arg_2449_1)) * ({
					0.8333,
					0.8667,
					0.9,
					0.9333,
					0.9667
				})[ove_0_11(0)]
			end
		},
		W = {
			SpellSlot = 1,
			DamageType = "Magic",
			Range = function(arg_2450_0, arg_2450_1)
				return arg_2450_1.AttackRange
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_2452_0, arg_2452_1)
				return arg_2452_1.maxHealth / 100 * (({
					9,
					10.5,
					12,
					13.5,
					15
				})[ove_0_11(1)] + 7.5 * (ove_0_10.GetTotalAP(arg_2452_1) % 100))
			end
		},
		E = {
			SpellSlot = 2,
			DamageType = "Physical",
			Range = function()
				return 925
			end,
			Delay = function()
				return 0.2419
			end,
			Dmg = function(arg_2455_0, arg_2455_1)
				return ({
					60,
					100,
					140,
					180,
					220
				})[ove_0_11(2)]
			end,
			DmgModAD = function(arg_2456_0, arg_2456_1)
				return ove_0_10.GetBonusAD(arg_2456_1) * 0.9
			end
		},
		R = {
			SpellSlot = 3,
			DamageType = "Magic",
			Range = function()
				return 1370
			end,
			Delay = function()
				return 0.2419
			end,
			Dmg = function(arg_2459_0, arg_2459_1)
				return ({
					150,
					250,
					350
				})[ove_0_11(3)]
			end,
			DmgModAP = function(arg_2460_0, arg_2460_1)
				return ove_0_10.GetTotalAP(arg_2460_1) * 1
			end
		}
	},
	Vayne = {
		Passive = {
			SpellSlot = -1
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Physical",
			Range = function()
				return 300
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_2463_0, arg_2463_1)
				return (ove_0_10.GetTotalAD(arg_2463_1) + ove_0_10.GetBonusAD(arg_2463_1)) * ({
					0.6,
					0.65,
					0.7,
					0.75,
					0.8
				})[ove_0_11(0)]
			end
		},
		W = {
			SpellSlot = 1,
			DamageType = "True",
			Range = function(arg_2464_0, arg_2464_1)
				return arg_2464_1.AttackRange
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_2466_0, arg_2466_1)
				return arg_2466_1.maxHealth / 100 * ({
					4,
					6,
					8,
					10,
					12
				})[ove_0_11(1)]
			end
		},
		E = {
			SpellSlot = 2,
			DamageType = "Physical",
			Range = function()
				return 550
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_2469_0, arg_2469_1)
				return ({
					125,
					212.5,
					300,
					387.5,
					475
				})[ove_0_11(2)]
			end,
			DmgModAD = function(arg_2470_0, arg_2470_1)
				return ove_0_10.GetBonusAD(arg_2470_1) * 1.25
			end
		},
		R = {
			SpellSlot = 3,
			DamageType = "Physical",
			Range = function(arg_2471_0, arg_2471_1)
				return arg_2471_1.AttackRange
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_2473_0, arg_2473_1)
				return ({
					25,
					40,
					55
				})[ove_0_11(3)]
			end
		}
	},
	Veigar = {
		Passive = {
			SpellSlot = -1
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Magic",
			Range = function()
				return 890
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_2476_0, arg_2476_1)
				return ({
					80,
					120,
					160,
					200,
					240
				})[ove_0_11(0)]
			end,
			DmgModAP = function(arg_2477_0, arg_2477_1)
				return ove_0_10.GetTotalAP(arg_2477_1) * 0.6
			end
		},
		W = {
			SpellSlot = 1,
			DamageType = "Magic",
			Range = function()
				return 900
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_2480_0, arg_2480_1)
				return ({
					100,
					150,
					200,
					250,
					300
				})[ove_0_11(1)]
			end,
			DmgModAP = function(arg_2481_0, arg_2481_1)
				return ove_0_10.GetTotalAP(arg_2481_1) * 1
			end
		},
		E = {
			SpellSlot = 2,
			Range = function()
				return 725
			end,
			Delay = function()
				return 0.25
			end
		},
		R = {
			SpellSlot = 3,
			DamageType = "Magic",
			Range = function()
				return 650
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_2486_0, arg_2486_1)
				return ({
					350,
					500,
					650
				})[ove_0_11(3)]
			end,
			DmgModAP = function(arg_2487_0, arg_2487_1)
				return ove_0_10.GetTotalAP(arg_2487_1) * 1.5
			end
		}
	},
	Velkoz = {
		Passive = {
			SpellSlot = -1,
			DamageType = "True",
			Dmg = function()
				return 25 + 8 * player.levelRef
			end,
			DmgModAP = function(arg_2489_0, arg_2489_1)
				return ove_0_10.GetTotalAP(arg_2489_1) * 0.5
			end
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Magic",
			Range = function()
				return 1100
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_2492_0, arg_2492_1)
				return ({
					80,
					120,
					160,
					200,
					240
				})[ove_0_11(0)]
			end,
			DmgModAP = function(arg_2493_0, arg_2493_1)
				return ove_0_10.GetTotalAP(arg_2493_1) * 0.9
			end
		},
		W = {
			SpellSlot = 1,
			DamageType = "Magic",
			Range = function()
				return 1105
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_2496_0, arg_2496_1)
				return ({
					75,
					125,
					175,
					225,
					275
				})[ove_0_11(1)]
			end,
			DmgModAP = function(arg_2497_0, arg_2497_1)
				return ove_0_10.GetTotalAP(arg_2497_1) * 0.45
			end
		},
		E = {
			SpellSlot = 2,
			DamageType = "Magic",
			Range = function()
				return 800
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_2500_0, arg_2500_1)
				return ({
					70,
					100,
					130,
					160,
					190
				})[ove_0_11(2)]
			end,
			DmgModAP = function(arg_2501_0, arg_2501_1)
				return ove_0_10.GetTotalAP(arg_2501_1) * 0.3
			end
		},
		R = {
			SpellSlot = 3,
			DamageType = "Magic",
			Range = function()
				return 1555
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_2504_0, arg_2504_1)
				return ({
					450,
					625,
					800
				})[ove_0_11(3)]
			end,
			DmgModAP = function(arg_2505_0, arg_2505_1)
				return ove_0_10.GetTotalAP(arg_2505_1) * 1.25
			end
		}
	},
	Vex = {
		Passive = {
			SpellSlot = -1,
			DamageType = "Magic",
			Dmg = function()
				return 30 + 6.470588235294118 * (player.levelRef - 1)
			end,
			DmgModAP = function(arg_2507_0, arg_2507_1)
				return ove_0_10.GetTotalAP(arg_2507_1) * 0.2
			end
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Magic",
			Range = function()
				return 1200
			end,
			Delay = function()
				return 0.15
			end,
			Dmg = function(arg_2510_0, arg_2510_1)
				return ({
					60,
					105,
					150,
					195,
					240
				})[ove_0_11(0)]
			end,
			DmgModAP = function(arg_2511_0, arg_2511_1)
				return ove_0_10.GetTotalAP(arg_2511_1) * 0.7
			end
		},
		W = {
			SpellSlot = 1,
			DamageType = "Magic",
			Range = function()
				return 475
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_2514_0, arg_2514_1)
				return ({
					80,
					120,
					160,
					200,
					240
				})[ove_0_11(1)]
			end,
			DmgModAP = function(arg_2515_0, arg_2515_1)
				return ove_0_10.GetTotalAP(arg_2515_1) * 0.3
			end
		},
		E = {
			SpellSlot = 2,
			DamageType = "Magic",
			Range = function()
				return 800
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_2518_0, arg_2518_1)
				return ({
					50,
					70,
					90,
					110,
					130
				})[ove_0_11(2)]
			end,
			DmgModAP = function(arg_2519_0, arg_2519_1)
				return ove_0_10.GetTotalAP(arg_2519_1) * ({
					0.4,
					0.45,
					0.5,
					0.55,
					0.6
				})[ove_0_11(2)]
			end
		},
		R = {
			SpellSlot = 3,
			DamageType = "Magic",
			Range = function()
				return ({
					2000,
					2500,
					3000
				})[ove_0_11(3)]
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_2522_0, arg_2522_1)
				return ({
					75,
					125,
					175
				})[ove_0_11(3)]
			end,
			DmgModAP = function(arg_2523_0, arg_2523_1)
				return ove_0_10.GetTotalAP(arg_2523_1) * 0.2
			end
		},
		R2 = {
			SpellSlot = 3,
			DamageType = "Magic",
			Range = function()
				return ({
					2000,
					2500,
					3000
				})[ove_0_11(3)]
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_2526_0, arg_2526_1)
				return ({
					150,
					250,
					350
				})[ove_0_11(3)]
			end,
			DmgModAP = function(arg_2527_0, arg_2527_1)
				return ove_0_10.GetTotalAP(arg_2527_1) * 0.5
			end
		}
	},
	Vi = {
		Passive = {
			SpellSlot = -1
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Physical",
			Range = function()
				return 725
			end,
			Delay = function()
				return 1.25
			end,
			Dmg = function(arg_2530_0, arg_2530_1)
				return ({
					110,
					160,
					210,
					260,
					310
				})[ove_0_11(0)]
			end,
			DmgModAD = function(arg_2531_0, arg_2531_1)
				return ove_0_10.GetBonusAD(arg_2531_1) * 1.4
			end
		},
		W = {
			SpellSlot = 1,
			DamageType = "Physical",
			Range = function(arg_2532_0, arg_2532_1)
				return arg_2532_1.AttackRange
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_2534_0, arg_2534_1)
				return arg_2534_1.maxHealth / 100 * (({
					4,
					5.5,
					7,
					8.5,
					10
				})[ove_0_11(1)] + 1 * (ove_0_10.GetBonusAD(arg_2534_1) % 35))
			end
		},
		E = {
			SpellSlot = 2,
			DamageType = "Magic",
			Range = function()
				return 600
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_2537_0, arg_2537_1)
				return ({
					10,
					30,
					50,
					70,
					90
				})[ove_0_11(2)]
			end,
			DmgModAD = function(arg_2538_0, arg_2538_1)
				return (ove_0_10.GetTotalAD(arg_2538_1) + ove_0_10.GetBonusAD(arg_2538_1)) * 1.1
			end,
			DmgModAP = function(arg_2539_0, arg_2539_1)
				return ove_0_10.GetTotalAP(arg_2539_1) * 0.9
			end
		},
		R = {
			SpellSlot = 3,
			DamageType = "Physical",
			Range = function()
				return 800
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_2542_0, arg_2542_1)
				return ({
					150,
					325,
					500
				})[ove_0_11(3)]
			end,
			DmgModAP = function(arg_2543_0, arg_2543_1)
				return ove_0_10.GetBonusAD(arg_2543_1) * 1.1
			end
		}
	},
	Viego = {
		Passive = {
			SpellSlot = -1
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Physical",
			Range = function()
				return 600
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_2546_0, arg_2546_1)
				return ({
					15,
					30,
					45,
					60,
					75
				})[ove_0_11(0)]
			end,
			DmgModAD = function(arg_2547_0, arg_2547_1)
				return (ove_0_10.GetTotalAD(arg_2547_1) + ove_0_10.GetBonusAD(arg_2547_1)) * 0.7
			end
		},
		W = {
			SpellSlot = 1,
			DamageType = "Magic",
			Range = function()
				return 900
			end,
			Delay = function()
				return 1
			end,
			Dmg = function(arg_2550_0, arg_2550_1)
				return ({
					80,
					135,
					190,
					245,
					300
				})[ove_0_11(0)]
			end,
			DmgModAD = function(arg_2551_0, arg_2551_1)
				return ove_0_10.GetTotalAP(arg_2551_1) * 1
			end
		},
		E = {
			SpellSlot = 2,
			Range = function()
				return 775
			end,
			Delay = function()
				return 0
			end
		},
		R = {
			SpellSlot = 3,
			DamageType = "Physical",
			Range = function()
				return 500
			end,
			Delay = function()
				return 0.5
			end,
			Dmg = function(arg_2556_0, arg_2556_1)
				return (T - arg_2556_1.health) / 100 * (({
					12,
					16,
					20
				})[ove_0_11(3)] + 3 * (ove_0_10.GetBonusAD(arg_2556_1) % 100))
			end
		}
	},
	Viktor = {
		Passive = {
			SpellSlot = -1
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Magic",
			Range = function()
				return 600
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_2559_0, arg_2559_1)
				return ({
					80,
					120,
					160,
					200,
					240
				})[ove_0_11(0)]
			end,
			DmgModAD = function(arg_2560_0, arg_2560_1)
				return (ove_0_10.GetTotalAD(arg_2560_1) + ove_0_10.GetBonusAD(arg_2560_1)) * 1
			end,
			DmgModAP = function(arg_2561_0, arg_2561_1)
				return ove_0_10.GetTotalAP(arg_2561_1) * 1
			end
		},
		W = {
			SpellSlot = 1,
			Range = function()
				return 800
			end,
			Delay = function()
				return 0.25
			end
		},
		E = {
			SpellSlot = 2,
			DamageType = "Magic",
			Range = function()
				return 550
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_2566_0, arg_2566_1)
				return ({
					90,
					160,
					230,
					300,
					370
				})[ove_0_11(2)]
			end,
			DmgModAP = function(arg_2567_0, arg_2567_1)
				return ove_0_10.GetTotalAP(arg_2567_1) * 1.3
			end
		},
		R = {
			SpellSlot = 3,
			DamageType = "Physical",
			Range = function()
				return 700
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_2570_0, arg_2570_1)
				return ({
					490,
					805,
					1120
				})[ove_0_11(3)]
			end,
			DmgModAP = function(arg_2571_0, arg_2571_1)
				return ove_0_10.GetTotalAP(arg_2571_1) * 3.2
			end
		}
	},
	Vladimir = {
		Passive = {
			SpellSlot = -1
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Magic",
			Range = function()
				return 600
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_2574_0, arg_2574_1)
				return ({
					80,
					100,
					120,
					140,
					160
				})[ove_0_11(0)]
			end,
			DmgModAP = function(arg_2575_0, arg_2575_1)
				return ove_0_10.GetTotalAP(arg_2575_1) * 0.6
			end
		},
		Q2 = {
			SpellSlot = 0,
			DamageType = "Magic",
			Range = function()
				return 600
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_2578_0, arg_2578_1)
				return ({
					148,
					185,
					222,
					259,
					296
				})[ove_0_11(0)]
			end,
			DmgModAP = function(arg_2579_0, arg_2579_1)
				return ove_0_10.GetTotalAP(arg_2579_1) * 1.11
			end
		},
		W = {
			SpellSlot = 1,
			DamageType = "Magic",
			Range = function()
				return 350
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_2582_0, arg_2582_1)
				return ({
					80,
					135,
					190,
					245,
					300
				})[ove_0_11(1)]
			end,
			DmgModHP = function(arg_2583_0, arg_2583_1)
				return arg_2583_1.health / 100 * 10
			end
		},
		E = {
			SpellSlot = 2,
			DamageType = "Magic",
			Range = function()
				return 600
			end,
			Delay = function()
				return 0.4
			end,
			Dmg = function(arg_2586_0, arg_2586_1)
				return ({
					60,
					90,
					120,
					150,
					180
				})[ove_0_11(2)] / 100 * 106
			end,
			DmgModAP = function(arg_2587_0, arg_2587_1)
				return ove_0_10.GetTotalAP(arg_2587_1) * 0.8
			end,
			DmgModHP = function(arg_2588_0, arg_2588_1)
				return arg_2588_1.health / 100 * 6
			end
		},
		R = {
			SpellSlot = 3,
			DamageType = "Physical",
			Range = function()
				return 625
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_2591_0, arg_2591_1)
				return ({
					150,
					250,
					350
				})[ove_0_11(3)]
			end,
			DmgModAP = function(arg_2592_0, arg_2592_1)
				return ove_0_10.GetTotalAP(arg_2592_1) * 0.7
			end
		}
	},
	Volibear = {
		Passive = {
			SpellSlot = -1,
			DamageType = "Magic",
			Dmg = function()
				return ({
					11,
					12,
					13,
					15,
					17,
					19,
					22,
					25,
					28,
					31,
					34,
					37,
					40,
					44,
					48,
					52,
					56,
					60
				})[player.levelRef]
			end,
			DmgModAP = function(arg_2594_0, arg_2594_1)
				return ove_0_10.GetTotalAP(arg_2594_1) * 0.4
			end
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Physical",
			Range = function(arg_2595_0, arg_2595_1)
				return arg_2595_1.AttackRange
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_2597_0, arg_2597_1)
				return ({
					10,
					30,
					50,
					70,
					90
				})[ove_0_11(0)]
			end,
			DmgModAP = function(arg_2598_0, arg_2598_1)
				return ove_0_10.GetBonusAD(arg_2598_1) * 1.2
			end
		},
		W = {
			SpellSlot = 1,
			DamageType = "Physical",
			Range = function()
				return 325
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_2601_0, arg_2601_1)
				return ({
					5,
					30,
					55,
					80,
					105
				})[ove_0_11(0)]
			end,
			DmgModAD = function(arg_2602_0, arg_2602_1)
				return (ove_0_10.GetTotalAD(arg_2602_1) + ove_0_10.GetBonusAD(arg_2602_1)) * 1
			end,
			DmgModHP = function(arg_2603_0, arg_2603_1)
				return arg_2603_1.health / 100 * 5
			end
		},
		E = {
			SpellSlot = 2,
			DamageType = "Magic",
			Range = function()
				return 1200
			end,
			Delay = function()
				return 0.4
			end,
			Dmg = function(arg_2606_0, arg_2606_1)
				return ({
					80,
					110,
					140,
					170,
					200
				})[ove_0_11(2)] / 100 * ({
					11,
					12,
					13,
					14,
					15
				})[ove_0_11(2)]
			end,
			DmgModAP = function(arg_2607_0, arg_2607_1)
				return ove_0_10.GetTotalAP(arg_2607_1) * 0.8
			end
		},
		R = {
			SpellSlot = 3,
			DamageType = "Physical",
			Range = function()
				return 700
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_2610_0, arg_2610_1)
				return ({
					300,
					500,
					700
				})[ove_0_11(3)]
			end,
			DmgModAD = function(arg_2611_0, arg_2611_1)
				return (ove_0_10.GetTotalAD(arg_2611_1) + ove_0_10.GetBonusAD(arg_2611_1)) * 2.5
			end,
			DmgModAP = function(arg_2612_0, arg_2612_1)
				return ove_0_10.GetTotalAP(arg_2612_1) * 1.25
			end
		}
	},
	Warwick = {
		Passive = {
			SpellSlot = -1,
			DamageType = "Physical",
			Dmg = function()
				return 10 + 2 * player.levelRef
			end,
			DmgModAD = function(arg_2614_0, arg_2614_1)
				return ove_0_10.GetBonusAD(arg_2614_1) * 0.15
			end,
			DmgModAP = function(arg_2615_0, arg_2615_1)
				return ove_0_10.GetTotalAP(arg_2615_1) * 0.1
			end
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Physical",
			Range = function()
				return 350
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_2618_0, arg_2618_1)
				return arg_2618_1.maxHealth / 100 * ({
					6,
					7,
					8,
					9,
					10
				})[ove_0_11(0)]
			end
		},
		W = {
			SpellSlot = 1,
			Range = function()
				return math.huge
			end,
			Delay = function()
				return 0.5
			end
		},
		E = {
			SpellSlot = 2,
			Range = function()
				return 375
			end,
			Delay = function()
				return 0
			end
		},
		R = {
			SpellSlot = 3,
			DamageType = "Physical",
			Range = function(arg_2623_0, arg_2623_1)
				return arg_2623_1.MovementSpeed / 100 * 250
			end,
			Delay = function()
				return 0.1
			end,
			Dmg = function(arg_2625_0, arg_2625_1)
				return ({
					175,
					350,
					525
				})[ove_0_11(3)]
			end,
			DmgModAD = function(arg_2626_0, arg_2626_1)
				return ove_0_10.GetBonusAD(arg_2626_1) * 1.67
			end
		}
	},
	MonkeyKing = {
		Passive = {
			SpellSlot = -1
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Physical",
			Range = function(arg_2627_0, arg_2627_1)
				return arg_2627_1.AttackRange
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_2629_0, arg_2629_1)
				return ({
					20,
					45,
					70,
					95,
					120
				})[ove_0_11(0)]
			end,
			DmgModAD = function(arg_2630_0, arg_2630_1)
				return ove_0_10.GetBonusAD(arg_2630_1) * 0.45
			end
		},
		W = {
			SpellSlot = 1,
			DamageType = "Physical",
			Range = function()
				return 300
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_2633_0, arg_2633_1)
				return (ove_0_10.GetTotalAD(arg_2633_1) + ove_0_10.GetBonusAD(arg_2633_1)) * ({
					0.35,
					0.4,
					0.45,
					0.5,
					0.55
				})[ove_0_11(1)]
			end
		},
		E = {
			SpellSlot = 2,
			DamageType = "Magic",
			Range = function()
				return 625
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_2636_0, arg_2636_1)
				return ({
					80,
					110,
					140,
					170,
					200
				})[ove_0_11(2)]
			end,
			DmgModAP = function(arg_2637_0, arg_2637_1)
				return ove_0_10.GetTotalAP(arg_2637_1) * 1
			end
		},
		R = {
			SpellSlot = 3,
			DamageType = "Physical",
			Range = function()
				return 162.5
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_2640_0, arg_2640_1)
				return arg_2640_1.maxHealth / 100 * ({
					16,
					24,
					32
				})[ove_0_11(3)]
			end,
			DmgModAD = function(arg_2641_0, arg_2641_1)
				return (ove_0_10.GetTotalAD(arg_2641_1) + ove_0_10.GetBonusAD(arg_2641_1)) * 4.4
			end
		}
	},
	Xayah = {
		Passive = {
			SpellSlot = -1
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Physical",
			Range = function()
				return 1100
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_2644_0, arg_2644_1)
				return ({
					90,
					120,
					150,
					180,
					210
				})[ove_0_11(0)]
			end,
			DmgModAD = function(arg_2645_0, arg_2645_1)
				return ove_0_10.GetBonusAD(arg_2645_1) * 1
			end
		},
		W = {
			SpellSlot = 1,
			Range = function(arg_2646_0, arg_2646_1)
				return arg_2646_1.AttackRange
			end,
			Delay = function()
				return 0
			end
		},
		E = {
			SpellSlot = 2,
			DamageType = "Physical",
			Range = function()
				return math.huge
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_2650_0, arg_2650_1)
				return ({
					55,
					65,
					75,
					85,
					95
				})[ove_0_11(2)]
			end,
			DmgModAD = function(arg_2651_0, arg_2651_1)
				return ove_0_10.GetBonusAD(arg_2651_1) * 0.6
			end
		},
		R = {
			SpellSlot = 3,
			DamageType = "Physical",
			Range = function()
				return 1000
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_2654_0, arg_2654_1)
				return ({
					200,
					300,
					400
				})[ove_0_11(3)]
			end,
			DmgModAD = function(arg_2655_0, arg_2655_1)
				return ove_0_10.GetBonusAD(arg_2655_1) * 1
			end
		}
	},
	Xerath = {
		Passive = {
			SpellSlot = -1
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Magic",
			Range = function()
				return 1450
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_2658_0, arg_2658_1)
				return ({
					70,
					110,
					150,
					190,
					230
				})[ove_0_11(0)]
			end,
			DmgModAP = function(arg_2659_0, arg_2659_1)
				return ove_0_10.GetTotalAP(arg_2659_1) * 0.85
			end
		},
		W = {
			SpellSlot = 1,
			DamageType = "Magic",
			Range = function()
				return 1000
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_2662_0, arg_2662_1)
				return ({
					100.02,
					158.365,
					216.71,
					275.055,
					333.4
				})[ove_0_11(1)]
			end,
			DmgModAP = function(arg_2663_0, arg_2663_1)
				return ove_0_10.GetTotalAP(arg_2663_1) * 1.0002
			end
		},
		E = {
			SpellSlot = 2,
			DamageType = "Magic",
			Range = function()
				return 1125
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_2666_0, arg_2666_1)
				return ({
					80,
					110,
					140,
					170,
					200
				})[ove_0_11(2)]
			end,
			DmgModAD = function(arg_2667_0, arg_2667_1)
				return ove_0_10.GetBonusAD(arg_2667_1) * 0.45
			end
		},
		R = {
			SpellSlot = 3,
			DamageType = "Magic",
			Range = function()
				return 5000
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_2670_0, arg_2670_1)
				return ({
					600,
					1000,
					1500
				})[ove_0_11(3)]
			end,
			DmgModAD = function(arg_2671_0, arg_2671_1)
				return ove_0_10.GetTotalAP(arg_2671_1) * ({
					1.35,
					1.8,
					2.25
				})[ove_0_11(3)]
			end
		}
	},
	XinZhao = {
		Passive = {
			SpellSlot = -1
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Physical",
			Range = function(arg_2672_0, arg_2672_1)
				return arg_2672_1.AttackRange
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_2674_0, arg_2674_1)
				return ({
					48,
					75,
					102,
					129,
					156
				})[ove_0_11(0)]
			end,
			DmgModAP = function(arg_2675_0, arg_2675_1)
				return ove_0_10.GetBonusAD(arg_2675_1) * 1.2
			end
		},
		W = {
			SpellSlot = 1,
			DamageType = "Physical",
			Range = function()
				return 940
			end,
			Delay = function()
				return 0.5
			end,
			Dmg = function(arg_2678_0, arg_2678_1)
				return ({
					80,
					125,
					170,
					215,
					260
				})[ove_0_11(1)]
			end,
			DmgModAD = function(arg_2679_0, arg_2679_1)
				return (ove_0_10.GetTotalAD(arg_2679_1) + ove_0_10.GetBonusAD(arg_2679_1)) * 1.2
			end,
			DmgModAP = function(arg_2680_0, arg_2680_1)
				return ove_0_10.GetTotalAP(arg_2680_1) * 0.65
			end
		},
		E = {
			SpellSlot = 2,
			DamageType = "Magic",
			Range = function()
				return 650
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_2683_0, arg_2683_1)
				return ({
					50,
					75,
					100,
					125,
					150
				})[ove_0_11(2)]
			end,
			DmgModAD = function(arg_2684_0, arg_2684_1)
				return ove_0_10.GetTotalAP(arg_2684_1) * 0.6
			end
		},
		R = {
			SpellSlot = 3,
			DamageType = "Magic",
			Range = function()
				return 450
			end,
			Delay = function()
				return 0.35
			end,
			Dmg = function(arg_2687_0, arg_2687_1)
				return ({
					75,
					175,
					275
				})[ove_0_11(3)]
			end,
			DmgModAD = function(arg_2688_0, arg_2688_1)
				return ove_0_10.GetBonusAD(arg_2688_1) * 1
			end,
			DmgModAP = function(arg_2689_0, arg_2689_1)
				return ove_0_10.GetTotalAP(arg_2689_1) * 1.1
			end,
			DmgModHP = function(arg_2690_0, arg_2690_1)
				return arg_2690_1.health / 100 * 15
			end
		}
	},
	Yasuo = {
		Passive = {
			SpellSlot = -1
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Physical",
			Range = function()
				return 450
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_2693_0, arg_2693_1)
				return ({
					20,
					45,
					70,
					95,
					120
				})[ove_0_11(0)]
			end,
			DmgModAP = function(arg_2694_0, arg_2694_1)
				return (ove_0_10.GetTotalAD(arg_2694_1) + ove_0_10.GetBonusAD(arg_2694_1)) * 1.05
			end
		},
		Q3 = {
			SpellSlot = 0,
			DamageType = "Physical",
			Range = function()
				return 1150
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_2697_0, arg_2697_1)
				return ({
					20,
					45,
					70,
					95,
					120
				})[ove_0_11(0)]
			end,
			DmgModAP = function(arg_2698_0, arg_2698_1)
				return (ove_0_10.GetTotalAD(arg_2698_1) + ove_0_10.GetBonusAD(arg_2698_1)) * 1.05
			end
		},
		W = {
			SpellSlot = 1,
			Range = function()
				return 450
			end,
			Delay = function()
				return 0.013
			end
		},
		E = {
			SpellSlot = 2,
			DamageType = "Magic",
			Range = function()
				return 475
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_2703_0, arg_2703_1)
				return ({
					90,
					105,
					120,
					135,
					150
				})[ove_0_11(2)]
			end,
			DmgModAD = function(arg_2704_0, arg_2704_1)
				return ove_0_10.GetBonusAD(arg_2704_1) * 0.2
			end,
			DmgModAP = function(arg_2705_0, arg_2705_1)
				return ove_0_10.GetTotalAP(arg_2705_1) * 0.6
			end
		},
		R = {
			SpellSlot = 3,
			DamageType = "Magic",
			Range = function()
				return 1400
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_2708_0, arg_2708_1)
				return ({
					200,
					350,
					500
				})[ove_0_11(3)]
			end,
			DmgModAD = function(arg_2709_0, arg_2709_1)
				return ove_0_10.GetBonusAD(arg_2709_1) * 1.5
			end
		}
	},
	Yone = {
		Passive = {
			SpellSlot = -1
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Physical",
			Range = function()
				return 450
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_2712_0, arg_2712_1)
				return ({
					20,
					40,
					60,
					80,
					100
				})[ove_0_11(0)]
			end,
			DmgModAD = function(arg_2713_0, arg_2713_1)
				return (ove_0_10.GetTotalAD(arg_2713_1) + ove_0_10.GetBonusAD(arg_2713_1)) * 1.05
			end
		},
		Q3 = {
			SpellSlot = 0,
			DamageType = "Physical",
			Range = function()
				return 1050
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_2716_0, arg_2716_1)
				return ({
					20,
					40,
					60,
					80,
					100
				})[ove_0_11(0)]
			end,
			DmgModAD = function(arg_2717_0, arg_2717_1)
				return (ove_0_10.GetTotalAD(arg_2717_1) + ove_0_10.GetBonusAD(arg_2717_1)) * 1.05
			end
		},
		W = {
			SpellSlot = 1,
			Range = function()
				return 600
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_2720_0, arg_2720_1)
				return ({
					10,
					20,
					30,
					40,
					50
				})[ove_0_11(1)] + T / 100 * ({
					11,
					12,
					13,
					14,
					15
				})[ove_0_11(1)]
			end
		},
		E = {
			SpellSlot = 2,
			Range = function()
				return 300
			end,
			Delay = function()
				return 0
			end
		},
		R = {
			SpellSlot = 3,
			DamageType = "Magic",
			Range = function()
				return 1000
			end,
			Delay = function()
				return 0.75
			end,
			Dmg = function(arg_2725_0, arg_2725_1)
				return ({
					200,
					400,
					600
				})[ove_0_11(3)]
			end,
			DmgModAD = function(arg_2726_0, arg_2726_1)
				return (ove_0_10.GetTotalAD(arg_2726_1) + ove_0_10.GetBonusAD(arg_2726_1)) * 0.8
			end
		}
	},
	Yorick = {
		Passive = {
			SpellSlot = -1
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Physical",
			Range = function(arg_2727_0, arg_2727_1)
				return arg_2727_1.AttackRange
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_2729_0, arg_2729_1)
				return ({
					30,
					55,
					80,
					105,
					130
				})[ove_0_11(0)]
			end,
			DmgModAD = function(arg_2730_0, arg_2730_1)
				return (ove_0_10.GetTotalAD(arg_2730_1) + ove_0_10.GetBonusAD(arg_2730_1)) * 0.4
			end
		},
		W = {
			SpellSlot = 1,
			Range = function()
				return 600
			end,
			Delay = function()
				return 0
			end
		},
		E = {
			SpellSlot = 2,
			Range = function()
				return 700
			end,
			Delay = function()
				return 0.33
			end,
			Dmg = function(arg_2735_0, arg_2735_1)
				return ({
					70,
					105,
					140,
					175,
					210
				})[ove_0_11(1)]
			end,
			DmgModAP = function(arg_2736_0, arg_2736_1)
				return ove_0_10.GetTotalAP(arg_2736_1) * 0.7
			end
		},
		R = {
			SpellSlot = 3,
			Range = function()
				return 600
			end,
			Delay = function()
				return 0.5
			end
		}
	},
	Yuumi = {
		Passive = {
			SpellSlot = -1
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Magic",
			Range = function()
				return 1150
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_2741_0, arg_2741_1)
				return ({
					60,
					100,
					140,
					180,
					220,
					260
				})[ove_0_11(0)]
			end,
			DmgModAP = function(arg_2742_0, arg_2742_1)
				return ove_0_10.GetTotalAP(arg_2742_1) * 0.4
			end,
			DmgModHP = function(arg_2743_0, arg_2743_1)
				return arg_2743_1.health / 100 * ({
					2,
					3.2,
					4.4,
					5.6,
					6.8,
					8
				})[ove_0_11(0)]
			end
		},
		W = {
			SpellSlot = 1,
			Range = function()
				return 700
			end,
			Delay = function()
				return 0
			end
		},
		E = {
			SpellSlot = 2,
			Range = function(arg_2746_0, arg_2746_1)
				return arg_2746_1.AttackRange
			end,
			Delay = function()
				return 0
			end
		},
		R = {
			SpellSlot = 3,
			Range = function()
				return 1100
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_2750_0, arg_2750_1)
				return ({
					240,
					320,
					400
				})[ove_0_11(3)]
			end,
			DmgModAP = function(arg_2751_0, arg_2751_1)
				return ove_0_10.GetTotalAP(arg_2751_1) * 0.8
			end
		}
	},
	Zac = {
		Passive = {
			SpellSlot = -1
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Magic",
			Range = function()
				return 951
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_2754_0, arg_2754_1)
				return ({
					80,
					110,
					140,
					170,
					200
				})[ove_0_11(0)]
			end,
			DmgModAP = function(arg_2755_0, arg_2755_1)
				return ove_0_10.GetTotalAP(arg_2755_1) * 0.6
			end,
			DmgModHP = function(arg_2756_0, arg_2756_1)
				return arg_2756_1.maxHealth / 100 * 5
			end
		},
		W = {
			SpellSlot = 1,
			DamageType = "Magic",
			Range = function()
				return 350
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_2759_0, arg_2759_1)
				return ({
					35,
					50,
					65,
					80,
					95
				})[ove_0_11(1)]
			end,
			DmgModHP = function(arg_2760_0, arg_2760_1)
				return arg_2760_1.maxHealth / 100 * ({
					4,
					5,
					6,
					7,
					8
				})[ove_0_11(1)] + 4 * (ove_0_10.GetTotalAP(arg_2760_1) % 100)
			end
		},
		E = {
			SpellSlot = 2,
			DamageType = "Magic",
			Range = function()
				return ({
					1200,
					1350,
					1500,
					1650,
					1800
				})[ove_0_11(2)]
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_2763_0, arg_2763_1)
				return ({
					60,
					110,
					160,
					210,
					260
				})[ove_0_11(2)]
			end,
			DmgModAP = function(arg_2764_0, arg_2764_1)
				return ove_0_10.GetTotalAP(arg_2764_1) * 0.9
			end
		},
		R = {
			SpellSlot = 3,
			Range = function()
				return 300
			end,
			Delay = function()
				return 0.3
			end,
			Dmg = function(arg_2767_0, arg_2767_1)
				return ({
					350,
					525,
					700
				})[ove_0_11(3)]
			end,
			DmgModAP = function(arg_2768_0, arg_2768_1)
				return ove_0_10.GetTotalAP(arg_2768_1) * 1
			end
		}
	},
	Zed = {
		Passive = {
			SpellSlot = -1
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Physical",
			Range = function()
				return 925
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_2771_0, arg_2771_1)
				return ({
					80,
					115,
					150,
					185,
					220
				})[ove_0_11(0)]
			end,
			DmgModAD = function(arg_2772_0, arg_2772_1)
				return ove_0_10.GetBonusAD(arg_2772_1) * 1.1
			end
		},
		W = {
			SpellSlot = 1,
			DamageType = "Magic",
			Range = function()
				return 650
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_2775_0, arg_2775_1)
				return ({
					48,
					69,
					90,
					111,
					132
				})[ove_0_11(0)]
			end,
			DmgModAD = function(arg_2776_0, arg_2776_1)
				return ove_0_10.GetBonusAD(arg_2776_1) * 0.66
			end
		},
		E = {
			SpellSlot = 2,
			DamageType = "Magic",
			Range = function()
				return 315
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_2779_0, arg_2779_1)
				return ({
					70,
					90,
					110,
					130,
					150
				})[ove_0_11(2)]
			end,
			DmgModAP = function(arg_2780_0, arg_2780_1)
				return ove_0_10.GetTotalAP(arg_2780_1) * 0.65
			end
		},
		R = {
			SpellSlot = 3,
			Range = function()
				return 625
			end,
			Delay = function()
				return 0
			end,
			Dmg = function(arg_2783_0, arg_2783_1)
				return (ove_0_10.GetTotalAD(arg_2783_1) + ove_0_10.GetBonusAD(arg_2783_1)) * 0.65 + ({
					25,
					40,
					55
				})[ove_0_11(3)]
			end
		}
	},
	Zeri = {
		Passive = {
			SpellSlot = -1
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Physical",
			Range = function()
				return 825
			end,
			Delay = function()
				return 0
			end,
			Width = function()
				return 80
			end,
			Dmg = function(arg_2787_0, arg_2787_1)
				return ({
					8,
					11,
					14,
					17,
					20
				})[ove_0_11(0)]
			end,
			DmgModAD = function(arg_2788_0, arg_2788_1)
				return ove_0_10.GetTotalAD(arg_2788_1) * ({
					1,
					1.05,
					1.1,
					1.15,
					1.2
				})[ove_0_11(0)]
			end
		},
		W = {
			SpellSlot = 1,
			DamageType = "Magic",
			Range = function()
				return 650
			end,
			Delay = function()
				return 0.594
			end,
			Speed = function()
				return 2200
			end,
			Dmg = function(arg_2792_0, arg_2792_1)
				return ({
					20,
					55,
					90,
					125,
					160
				})[ove_0_11(0)]
			end,
			DmgModAD = function(arg_2793_0, arg_2793_1)
				return ove_0_10.GetTotalAD(arg_2793_1) * 1
			end,
			DmgModAP = function(arg_2794_0, arg_2794_1)
				return ove_0_10.GetTotalAP(arg_2794_1) * 0.4
			end
		},
		E = {
			SpellSlot = 2,
			Range = function()
				return 300
			end,
			Delay = function()
				return 0
			end,
			Speed = function()
				return 600
			end
		},
		R = {
			SpellSlot = 3,
			DamageType = "Magic",
			Range = function()
				return 825
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_2800_0, arg_2800_1)
				return ({
					150,
					250,
					350
				})[ove_0_11(3)]
			end,
			DmgModAD = function(arg_2801_0, arg_2801_1)
				return ove_0_10.GetBonusAD(arg_2801_1) * 0.8
			end,
			DmgModAP = function(arg_2802_0, arg_2802_1)
				return ove_0_10.GetTotalAP(arg_2802_1) * 0.8
			end
		},
		ROvercharge = {
			SpellSlot = 3,
			DamageType = "Magic",
			Range = function()
				return 825
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_2805_0, arg_2805_1)
				return ({
					5,
					10,
					15
				})[ove_0_11(3)]
			end,
			DmgModAP = function(arg_2806_0, arg_2806_1)
				return ove_0_10.GetTotalAP(arg_2806_1) * 0.15
			end
		}
	},
	Ziggs = {
		Passive = {
			SpellSlot = -1,
			DamageType = "Magic",
			Dmg = function()
				return ({
					20,
					24,
					28,
					32,
					36,
					40,
					48,
					56,
					64,
					72,
					80,
					88,
					100,
					112,
					124,
					136,
					148,
					160
				})[player.levelRef]
			end,
			DmgModAP = function(arg_2808_0, arg_2808_1)
				return ove_0_10.GetTotalAP(arg_2808_1) * 0.5
			end
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Magic",
			Range = function()
				return 1400
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_2811_0, arg_2811_1)
				return ({
					85,
					135,
					185,
					235,
					285
				})[ove_0_11(0)]
			end,
			DmgModAP = function(arg_2812_0, arg_2812_1)
				return ove_0_10.GetTotalAP(arg_2812_1) * 0.65
			end
		},
		W = {
			SpellSlot = 1,
			DamageType = "Magic",
			Range = function()
				return 1000
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_2815_0, arg_2815_1)
				return ({
					70,
					105,
					140,
					175,
					210
				})[ove_0_11(1)]
			end,
			DmgModAP = function(arg_2816_0, arg_2816_1)
				return ove_0_10.GetTotalAP(arg_2816_1) * 0.5
			end
		},
		E = {
			SpellSlot = 2,
			DamageType = "Magic",
			Range = function()
				return 900
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_2819_0, arg_2819_1)
				return ({
					150,
					350,
					550,
					750,
					950
				})[ove_0_11(2)]
			end,
			DmgModAP = function(arg_2820_0, arg_2820_1)
				return ove_0_10.GetTotalAP(arg_2820_1) * 1.5
			end
		},
		R = {
			SpellSlot = 3,
			Range = function()
				return 5000
			end,
			Delay = function()
				return 0.375
			end,
			Dmg = function(arg_2823_0, arg_2823_1)
				return ({
					300,
					450,
					600
				})[ove_0_11(3)]
			end,
			DmgModAP = function(arg_2824_0, arg_2824_1)
				return ove_0_10.GetTotalAP(arg_2824_1) * 1.1
			end
		}
	},
	Zilean = {
		Passive = {
			SpellSlot = -1
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Magic",
			Range = function()
				return 900
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_2827_0, arg_2827_1)
				return ({
					75,
					115,
					165,
					230,
					300
				})[ove_0_11(0)]
			end,
			DmgModAP = function(arg_2828_0, arg_2828_1)
				return ove_0_10.GetTotalAP(arg_2828_1) * 0.9
			end
		},
		W = {
			SpellSlot = 1,
			Range = function(arg_2829_0, arg_2829_1)
				return arg_2829_1.AttackRange
			end,
			Delay = function()
				return 0
			end
		},
		E = {
			SpellSlot = 2,
			Range = function()
				return 550
			end,
			Delay = function()
				return 0
			end
		},
		R = {
			SpellSlot = 3,
			Range = function()
				return 900
			end,
			Delay = function()
				return 0
			end
		}
	},
	Zoe = {
		Passive = {
			SpellSlot = -1,
			DamageType = "Magic",
			Dmg = function()
				return ({
					16,
					20,
					24,
					28,
					32,
					36,
					42,
					48,
					54,
					60,
					66,
					74,
					82,
					90,
					100,
					11,
					120,
					130
				})[player.levelRef]
			end,
			DmgModAP = function(arg_2836_0, arg_2836_1)
				return ove_0_10.GetTotalAP(arg_2836_1) * 0.2
			end
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Magic",
			Range = function()
				return 1600
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_2839_0, arg_2839_1)
				return ({
					17.5,
					20,
					25,
					30,
					35,
					40,
					45,
					50,
					55,
					60,
					65,
					72.5,
					80,
					87.5,
					95,
					105,
					115,
					125
				})[player.levelRef] + ({
					125,
					200,
					275,
					350,
					425
				})[ove_0_11(0)]
			end,
			DmgModAP = function(arg_2840_0, arg_2840_1)
				return ove_0_10.GetTotalAP(arg_2840_1) * 1.5
			end
		},
		W = {
			SpellSlot = 1,
			DamageType = "Magic",
			Range = function()
				return 2200
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_2843_0, arg_2843_1)
				return ({
					75,
					105,
					135,
					165,
					195
				})[ove_0_11(1)]
			end,
			DmgModAP = function(arg_2844_0, arg_2844_1)
				return ove_0_10.GetTotalAP(arg_2844_1) * 0.4
			end
		},
		E = {
			SpellSlot = 2,
			DamageType = "Magic",
			Range = function()
				return 850
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_2847_0, arg_2847_1)
				return ({
					70,
					110,
					150,
					190,
					230
				})[ove_0_11(2)]
			end,
			DmgModAP = function(arg_2848_0, arg_2848_1)
				return ove_0_10.GetTotalAP(arg_2848_1) * 0.45
			end
		},
		R = {
			SpellSlot = 3,
			Range = function()
				return 575
			end,
			Delay = function()
				return 0.25
			end
		}
	},
	Zyra = {
		Passive = {
			SpellSlot = -1
		},
		Q = {
			SpellSlot = 0,
			DamageType = "Magic",
			Range = function()
				return 800
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_2853_0, arg_2853_1)
				return ({
					60,
					95,
					130,
					165,
					200
				})[ove_0_11(0)]
			end,
			DmgModAP = function(arg_2854_0, arg_2854_1)
				return ove_0_10.GetTotalAP(arg_2854_1) * 0.6
			end
		},
		W = {
			SpellSlot = 1,
			Range = function()
				return 850
			end,
			Delay = function()
				return 0
			end
		},
		E = {
			SpellSlot = 2,
			DamageType = "Magic",
			Range = function()
				return 1100
			end,
			Delay = function()
				return 0.25
			end,
			Speed = function()
				return 1150
			end,
			Dmg = function(arg_2860_0, arg_2860_1)
				return ({
					60,
					105,
					150,
					195,
					240
				})[ove_0_11(2)]
			end,
			DmgModAP = function(arg_2861_0, arg_2861_1)
				return ove_0_10.GetTotalAP(arg_2861_1) * 0.5
			end
		},
		R = {
			SpellSlot = 3,
			DamageType = "Magic",
			Range = function()
				return 700
			end,
			Delay = function()
				return 0.25
			end,
			Dmg = function(arg_2864_0, arg_2864_1)
				return ({
					180,
					265,
					350
				})[ove_0_11(3)]
			end,
			DmgModAP = function(arg_2865_0, arg_2865_1)
				return ove_0_10.GetTotalAP(arg_2865_1) * 0.7
			end
		}
	}
}

return SpellLib

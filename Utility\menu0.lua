

local ove_0_6 = {}
--local ove_0_7 = graphics.sprite("Resources/Logo.png")
--local ove_0_8 = graphics.sprite("Resources/Gear.png")
--local ove_0_9 = graphics.sprite("Resources/summonerignite.png")
--local ove_0_10 = graphics.sprite("Resources/summonerbarrier.png")
--local ove_0_11 = graphics.sprite("Resources/summonerheal.png")
--local ove_0_12 = graphics.sprite("Resources/summonersmite.png")
--local ove_0_13 = graphics.sprite("Resources/summonerboost.png")
--local ove_0_14 = graphics.sprite("Resources/yourcut.png")
--local ove_0_15 = graphics.sprite("Resources/qss.png")
--local ove_0_16 = graphics.sprite("Resources/hydra.png")
--local ove_0_17 = graphics.sprite("Resources/stride.png")
--local ove_0_18 = graphics.sprite("Resources/belt.png")
--local ove_0_19 = graphics.sprite("Resources/pot.png")
--local ove_0_20 = graphics.sprite("Resources/hweiqq.png")
--local ove_0_21 = graphics.sprite("Resources/hweiqw.png")
--local ove_0_22 = graphics.sprite("Resources/hweiqe.png")
--local ove_0_23 = graphics.sprite("Resources/hweiwe.png")
--local ove_0_24 = graphics.sprite("Resources/hweieq.png")
--local ove_0_25 = graphics.sprite("Resources/hweiew.png")
--local ove_0_26 = graphics.sprite("Resources/hweiee.png")

--ove_0_6.coremenu = menu("Fatality Core", "[Fatality] Core")

--ove_0_6.coremenu:set("icon", ove_0_7)
--ove_0_6.coremenu:boolean("champ", "Load [Fatality] " .. player.charName, true)
--ove_0_6.coremenu.champ:set("icon", player.iconSquare)
--ove_0_6.coremenu:boolean("activator", "Load [Fatality] Activator", true)

--[[if ove_0_6.coremenu.activator:get() then
	ove_0_6.activatormenu = menu("Fatality Activator", "[Fatality] Activator")

	ove_0_6.activatormenu:set("icon", ove_0_8)
	ove_0_6.activatormenu:dropdown("language", "Language", 1, {
		"English",
		"CN"
	})
	ove_0_6.activatormenu.language:set("tooltip", "Please Reload | 请重新加载")

	local ove_0_27 = ove_0_6.activatormenu.language:get()

	if ove_0_27 == 1 then
		ove_0_6.activatormenu:menu("items", "Items")
		ove_0_6.activatormenu.items:menu("offensive", "Offensive Items")
		ove_0_6.activatormenu.items.offensive:menu("stride", "Stridebreaker")
		ove_0_6.activatormenu.items.offensive.stride:set("icon", ove_0_17)
		ove_0_6.activatormenu.items.offensive.stride:boolean("stridebreaker", "Enable Stridebreaker", true)
		ove_0_6.activatormenu.items.offensive:menu("belt", "Hextech Rocketbelt")
		ove_0_6.activatormenu.items.offensive.belt:set("icon", ove_0_18)
		ove_0_6.activatormenu.items.offensive.belt:boolean("rocketbelt", "Enable Hextech Rocketbelt", true)
		ove_0_6.activatormenu.items.offensive.belt:slider("belthp", "Target Max Health % > ", 50, 0, 100, 1)
		ove_0_6.activatormenu.items.offensive:menu("hydra", "Hydra")
		ove_0_6.activatormenu.items.offensive.hydra:set("icon", ove_0_16)
		ove_0_6.activatormenu.items.offensive.hydra:boolean("tiamat", "Enable Tiamat", true)
		ove_0_6.activatormenu.items.offensive.hydra:boolean("ravenoushydra", "Enable Ravenous Hydra", true)
		ove_0_6.activatormenu.items.offensive.hydra:boolean("titanichydra", "Enable Titanic Hydra", true)
		ove_0_6.activatormenu.items.offensive.hydra:boolean("profanehydra", "Enable Profane Hydra", true)
		ove_0_6.activatormenu.items:menu("defensive", "Defensive Items")
		ove_0_6.activatormenu.items.defensive:menu("qss", "Qss")
		ove_0_6.activatormenu.items.defensive.qss:set("icon", ove_0_15)
		ove_0_6.activatormenu.items.defensive.qss:boolean("autoqss", "Auto Qss CC", true)
		ove_0_6.activatormenu.items.defensive.qss:boolean("onlycombo", "Auto Qss only im Combo Mode", false)
		ove_0_6.activatormenu.items.defensive.qss:boolean("qssblind", "Qss Blind", true)
		ove_0_6.activatormenu.items.defensive:menu("pots", "Health Potion")
		ove_0_6.activatormenu.items.defensive.pots:set("icon", ove_0_19)
		ove_0_6.activatormenu.items.defensive.pots:boolean("healthpot", "Enable Health Pot", true)
		ove_0_6.activatormenu.items.defensive.pots:boolean("refillpot", "Enable Refill Pot", true)
		ove_0_6.activatormenu.items.defensive.pots:boolean("cookie", "Enable Cookie", true)
		ove_0_6.activatormenu.items.defensive.pots:slider("health", "Health % to use Pots", 70, 0, 100, 1)
		ove_0_6.activatormenu:menu("summoners", "Summoners")
		ove_0_6.activatormenu.summoners:menu("ignite", "Ignite")
		ove_0_6.activatormenu.summoners.ignite:set("icon", ove_0_9)
		ove_0_6.activatormenu.summoners.ignite:boolean("autoignite", "Auto Ignite Killable Target", true)
		ove_0_6.activatormenu.summoners.ignite:boolean("onlycombo", "Only Auto Ignite in Combo Mode", false)
		ove_0_6.activatormenu.summoners:menu("barrier", "Barrier")
		ove_0_6.activatormenu.summoners.barrier:set("icon", ove_0_10)
		ove_0_6.activatormenu.summoners.barrier:boolean("autobarrier", "Auto Barrier", true)
		ove_0_6.activatormenu.summoners.barrier:boolean("onlydead", "Only Auto Barrier if Incoming Damage is Higher then your Health", true)
		ove_0_6.activatormenu.summoners.barrier.onlydead:set("tooltip", "Only Auto Barrier if Incoming Damage Kills you and Heal can Save You")
		ove_0_6.activatormenu.summoners.barrier:slider("incomdamage", "Auto Barrier if Incoming Damage is Higher then > ", 500, 1, 1500, 1)
		ove_0_6.activatormenu.summoners:menu("heal", "Heal")
		ove_0_6.activatormenu.summoners.heal:set("icon", ove_0_11)
		ove_0_6.activatormenu.summoners.heal:boolean("autoheal", "Auto Heal", true)
		ove_0_6.activatormenu.summoners.heal:boolean("onlydead", "Only Auto Heal if Incoming Damage Kills you", true)
		ove_0_6.activatormenu.summoners.heal.onlydead:set("tooltip", "Only Auto Heals if Incoming Damage Kills you and Heal can Save You")
		ove_0_6.activatormenu.summoners.heal:slider("incomdamage", "Auto Heal if Incoming Damage is Higher then > ", 500, 1, 1500, 1)
		ove_0_6.activatormenu.summoners.heal:header("allys", "Allies Whitelist")

		for iter_0_0 = 0, objManager.allies_n - 1 do
			local ove_0_28 = objManager.allies[iter_0_0]

			ove_0_6.activatormenu.summoners.heal:boolean(ove_0_28.charName, "Use Heal On: " .. ove_0_28.charName, true)
		end

		ove_0_6.activatormenu.summoners:menu("smite", "Smite")
		ove_0_6.activatormenu.summoners.smite:set("icon", ove_0_12)
		ove_0_6.activatormenu.summoners.smite:keybind("smitekey", "Enable Smite", nil, "T")
		ove_0_6.activatormenu.summoners.smite.smitekey:permashow(false)
		ove_0_6.activatormenu.summoners.smite:boolean("smitered", "Smite Red Buff", false)
		ove_0_6.activatormenu.summoners.smite:boolean("smiteblue", "Smite Blue Buff", false)
		ove_0_6.activatormenu.summoners.smite:boolean("smitecrab", "Smite Crab", false)
		ove_0_6.activatormenu.summoners.smite:boolean("smitedrake", "Smite Dragon", true)
		ove_0_6.activatormenu.summoners.smite:boolean("smitevoidling", "Smite Voidlings", true)
		ove_0_6.activatormenu.summoners.smite:boolean("smiteherald", "Smite Herald", true)
		ove_0_6.activatormenu.summoners.smite:boolean("smitebaron", "Smite Baron", true)
		ove_0_6.activatormenu.summoners.smite:boolean("drawsmite", "Draw Smite Range", true)
		ove_0_6.activatormenu.summoners.smite.drawsmite:set("tooltip", "Only Draws it if you has Smite Toggled")
		ove_0_6.activatormenu.summoners:menu("cleanse", "Cleanse")
		ove_0_6.activatormenu.summoners.cleanse:set("icon", ove_0_13)
		ove_0_6.activatormenu.summoners.cleanse:boolean("autocleanse", "Auto Cleanse CC", true)
		ove_0_6.activatormenu.summoners.cleanse:boolean("dontqss", "Dont Cleanse if Qss is Ready", true)
		ove_0_6.activatormenu.summoners.cleanse:boolean("cleanseignite", "Cleanse Ignite", true)
		ove_0_6.activatormenu.summoners.cleanse:boolean("cleanseexhaust", "Cleanse Exhaust", true)
		ove_0_6.activatormenu.summoners.cleanse:boolean("cleanseblind", "Cleanse Blind", true)
		ove_0_6.activatormenu.summoners.cleanse:boolean("cleansesilence", "Cleanse Silence", true)
		ove_0_6.activatormenu:menu("special", "Special Items")
		ove_0_6.activatormenu.special:menu("pyke", "Pyke Gold")
		ove_0_6.activatormenu.special.pyke:set("icon", ove_0_14)
		ove_0_6.activatormenu.special.pyke:boolean("autousegold", "Auto use Pyke Gold", true)
	end

	if ove_0_27 == 2 then
		ove_0_6.activatormenu:menu("items", "物品")
		ove_0_6.activatormenu.items:menu("offensive", "攻击型物品")
		ove_0_6.activatormenu.items.offensive:menu("stride", "跨步者")
		ove_0_6.activatormenu.items.offensive.stride:set("icon", ove_0_17)
		ove_0_6.activatormenu.items.offensive.stride:boolean("stridebreaker", "启用跨步者", true)
		ove_0_6.activatormenu.items.offensive:menu("belt", "科技火箭腰带")
		ove_0_6.activatormenu.items.offensive.belt:set("icon", ove_0_18)
		ove_0_6.activatormenu.items.offensive.belt:boolean("rocketbelt", "启用科技火箭腰带", true)
		ove_0_6.activatormenu.items.offensive.belt:slider("belthp", "目标最大生命值百分比", 50, 0, 100, 1)
		ove_0_6.activatormenu.items.offensive:menu("hydra", "九头蛇")
		ove_0_6.activatormenu.items.offensive.hydra:set("icon", ove_0_16)
		ove_0_6.activatormenu.items.offensive.hydra:boolean("tiamat", "启用提亚玛特", true)
		ove_0_6.activatormenu.items.offensive.hydra:boolean("ravenoushydra", "启用贪婪九头蛇", true)
		ove_0_6.activatormenu.items.offensive.hydra:boolean("titanichydra", "启用泰坦九头蛇", true)
		ove_0_6.activatormenu.items.offensive.hydra:boolean("profanehydra", "启用亵渎九头蛇", true)
		ove_0_6.activatormenu.items:menu("defensive", "防御型物品")
		ove_0_6.activatormenu.items.defensive:menu("qss", "水银饰带")
		ove_0_6.activatormenu.items.defensive.qss:set("icon", ove_0_15)
		ove_0_6.activatormenu.items.defensive.qss:boolean("autoqss", "自动使用水银饰带解控制效果", true)
		ove_0_6.activatormenu.items.defensive.qss:boolean("onlycombo", "只有在连招模式下自动使用水银饰带", false)
		ove_0_6.activatormenu.items.defensive.qss:boolean("qssblind", "使用水银饰带解致盲效果", true)
		ove_0_6.activatormenu.items.defensive:menu("pots", "生命药水")
		ove_0_6.activatormenu.items.defensive.pots:set("icon", ove_0_19)
		ove_0_6.activatormenu.items.defensive.pots:boolean("healthpot", "启用生命药水", true)
		ove_0_6.activatormenu.items.defensive.pots:boolean("refillpot", "启用回复药水", true)
		ove_0_6.activatormenu.items.defensive.pots:boolean("cookie", "启用点心", true)
		ove_0_6.activatormenu.items.defensive.pots:slider("health", "使用药水的生命值百分比", 70, 0, 100, 1)
		ove_0_6.activatormenu:menu("summoners", "召唤师技能")
		ove_0_6.activatormenu.summoners:menu("ignite", "点燃")
		ove_0_6.activatormenu.summoners.ignite:set("icon", ove_0_9)
		ove_0_6.activatormenu.summoners.ignite:boolean("autoignite", "自动使用点燃可击杀目标", true)
		ove_0_6.activatormenu.summoners.ignite:boolean("onlycombo", "只在连招模式下自动使用点燃", false)
		ove_0_6.activatormenu.summoners:menu("barrier", "屏障")
		ove_0_6.activatormenu.summoners.barrier:set("icon", ove_0_10)
		ove_0_6.activatormenu.summoners.barrier:boolean("autobarrier", "自动屏障", true)
		ove_0_6.activatormenu.summoners.barrier:boolean("onlydead", "只有当进攻性伤害高于你的生命值时才自动使用屏障", true)
		ove_0_6.activatormenu.summoners.barrier.onlydead:set("tooltip", "只有在进攻性伤害能够致你死亡且治疗可以拯救你的情况下才自动使用屏障")
		ove_0_6.activatormenu.summoners.barrier:slider("incomdamage", "只有在进攻性伤害高于某个值时才自动使用屏障", 500, 1, 1500, 1)
		ove_0_6.activatormenu.summoners:menu("heal", "治疗")
		ove_0_6.activatormenu.summoners.heal:set("icon", ove_0_11)
		ove_0_6.activatormenu.summoners.heal:boolean("autoheal", "自动治疗", true)
		ove_0_6.activatormenu.summoners.heal:boolean("onlydead", "只有在即将到来的伤害会致你死亡时才自动治疗", true)
		ove_0_6.activatormenu.summoners.heal.onlydead:set("tooltip", "只有在即将到来的伤害会致你死亡且治疗可以拯救你时才自动治疗")
		ove_0_6.activatormenu.summoners.heal:slider("incomdamage", "只有在即将到来的伤害高于某个数值时才自动治疗", 500, 1, 1500, 1)
		ove_0_6.activatormenu.summoners.heal:header("allys", "盟友白名单")

		for iter_0_1 = 0, objManager.allies_n - 1 do
			local ove_0_29 = objManager.allies[iter_0_1]

			ove_0_6.activatormenu.summoners.heal:boolean(ove_0_29.charName, "使用治疗开启: " .. ove_0_29.charName, true)
		end

		ove_0_6.activatormenu.summoners:menu("smite", "惩击")
		ove_0_6.activatormenu.summoners.smite:set("icon", ove_0_12)
		ove_0_6.activatormenu.summoners.smite:keybind("smitekey", "启用惩击", nil, "T")
		ove_0_6.activatormenu.summoners.smite.smitekey:permashow(false)
		ove_0_6.activatormenu.summoners.smite:boolean("smitered", "惩击红BUFF", false)
		ove_0_6.activatormenu.summoners.smite:boolean("smiteblue", "惩击蓝BUFF", false)
		ove_0_6.activatormenu.summoners.smite:boolean("smitecrab", "惩击螃蟹", false)
		ove_0_6.activatormenu.summoners.smite:boolean("smitedrake", "惩击龙", true)
		ove_0_6.activatormenu.summoners.smite:boolean("smitevoidling", "惩击虚空使者", true)
		ove_0_6.activatormenu.summoners.smite:boolean("smiteherald", "惩击峡谷先锋", true)
		ove_0_6.activatormenu.summoners.smite:boolean("smitebaron", "惩击男爵", true)
		ove_0_6.activatormenu.summoners.smite:boolean("drawsmite", "绘制惩击范围", true)
		ove_0_6.activatormenu.summoners.smite.drawsmite:set("tooltip", "只在你切换了惩击状态下才绘制")
		ove_0_6.activatormenu.summoners:menu("cleanse", "净化")
		ove_0_6.activatormenu.summoners.cleanse:set("icon", ove_0_13)
		ove_0_6.activatormenu.summoners.cleanse:boolean("autocleanse", "自动净化控制效果", true)
		ove_0_6.activatormenu.summoners.cleanse:boolean("dontqss", "如果水银饰带准备好了就不要净化", true)
		ove_0_6.activatormenu.summoners.cleanse:boolean("cleanseignite", "净化点燃", true)
		ove_0_6.activatormenu.summoners.cleanse:boolean("cleanseexhaust", "净化虚弱", true)
		ove_0_6.activatormenu.summoners.cleanse:boolean("cleanseblind", "净化致盲", true)
		ove_0_6.activatormenu.summoners.cleanse:boolean("cleansesilence", "净化沉默", true)
		ove_0_6.activatormenu:menu("special", "特殊物品")
		ove_0_6.activatormenu.special:menu("pyke", "派克金币")
		ove_0_6.activatormenu.special.pyke:set("icon", ove_0_14)
		ove_0_6.activatormenu.special.pyke:boolean("autousegold", "自动使用派克金币", true)
	end
end]]

--if ove_0_6.coremenu.champ:get() then
	if player.charName == "Ahri" then
		ove_0_6.ahrimenu = menu("Fatality " .. player.charName, "[Fatality] Ahri")

		ove_0_6.ahrimenu:set("icon", player.iconSquare)
		ove_0_6.ahrimenu:dropdown("language", "Language", 1, {
			"English",
			"CN"
		})
		ove_0_6.ahrimenu.language:set("tooltip", "Please Reload | 请重新加载")

		local ove_0_30 = ove_0_6.ahrimenu.language:get()

		if ove_0_30 == 1 then
			ove_0_6.ahrimenu:menu("Combo", "Combo")
			ove_0_6.ahrimenu.Combo:menu("qsettings", "Orb of Deception - [Q]")
			ove_0_6.ahrimenu.Combo.qsettings:set("icon", player:spellSlot(0).icon)
			ove_0_6.ahrimenu.Combo.qsettings:boolean("useq", "Enable Q in Combo", true)
			ove_0_6.ahrimenu.Combo.qsettings:boolean("slowpredq", "Enable Q Slow Prediction", false)
			ove_0_6.ahrimenu.Combo:menu("wsettings", "Fox-Fire - [W]")
			ove_0_6.ahrimenu.Combo.wsettings:boolean("usew", "Enable W in Combo", true)
			ove_0_6.ahrimenu.Combo.wsettings:set("icon", player:spellSlot(1).icon)
			ove_0_6.ahrimenu.Combo:menu("esettings", "Charm - [E]")
			ove_0_6.ahrimenu.Combo.esettings:set("icon", player:spellSlot(2).icon)
			ove_0_6.ahrimenu.Combo.esettings:boolean("usee", "Enable E in Combo", true)
			ove_0_6.ahrimenu.Combo.esettings:boolean("slowprede", "Enable E Slow Prediction", false)
			ove_0_6.ahrimenu.Combo.esettings:boolean("autoe", "Auto E on Dashing Target", true)
			ove_0_6.ahrimenu.Combo:menu("rsettings", "Spirit Rush - [R]")
			ove_0_6.ahrimenu.Combo.rsettings:set("icon", player:spellSlot(3).icon)
			ove_0_6.ahrimenu.Combo.rsettings:boolean("user", "Enable R in Combo", false)
			ove_0_6.ahrimenu:menu("Killsteal", "Killsteal")
			ove_0_6.ahrimenu.Killsteal:boolean("ksQ", "Enable Q Killsteal", true)
			ove_0_6.ahrimenu.Killsteal:boolean("ksW", "Enable W Killsteal", true)
			ove_0_6.ahrimenu.Killsteal:boolean("ksE", "Enable E Killsteal", true)
			ove_0_6.ahrimenu:menu("Clear", "Clear")
			ove_0_6.ahrimenu.Clear:header("Lane Clear", "Lane Clear")
			ove_0_6.ahrimenu.Clear:boolean("LcQ", "Enable Q in Lane Clear", true)
			ove_0_6.ahrimenu.Clear:header("Jungle Clear", "Jungle Clear")
			ove_0_6.ahrimenu.Clear:boolean("JcQ", "Enable Q in Jungle Clear", true)
			ove_0_6.ahrimenu.Clear:boolean("JcW", "Enable W in Jungle Clear", true)
			ove_0_6.ahrimenu:menu("Misc", "Misc")
			ove_0_6.ahrimenu.Misc:boolean("antigap", "Enable Antigap Closer", true)
			ove_0_6.ahrimenu.Misc:slider("antigaphp", "HP % to Anti Gapclose", 50, 1, 100, 1)
			ove_0_6.ahrimenu:menu("Draws", "Draws")
			ove_0_6.ahrimenu.Draws:dropdown("circlemode", "Circle Mode", 1, {
				"Default",
				"Rainbow"
			})
			ove_0_6.ahrimenu.Draws:boolean("drawQ", "Draw Q Range", true)
			ove_0_6.ahrimenu.Draws:boolean("drawW", "Draw W Range", true)
			ove_0_6.ahrimenu.Draws:boolean("drawE", "Draw E Range", true)
			ove_0_6.ahrimenu.Draws:boolean("drawR", "Draw R Range", true)
			ove_0_6.ahrimenu.Draws:boolean("drawAA", "Draw AA Tracker", true)
			ove_0_6.ahrimenu.Draws:boolean("drawbuff", "Draw Buff Time", true)
			ove_0_6.ahrimenu:menu("Keys", "Keys")
			ove_0_6.ahrimenu.Keys:keybind("stogglebar", "Combo Key", "Space", nil)
			ove_0_6.ahrimenu.Keys:keybind("clearKey", "Clear Key", "V", nil)
			ove_0_6.ahrimenu.Keys.clearKey:permashow(true)
			ove_0_6.ahrimenu.Keys:keybind("farmtoggle", "Enable Spell Farming", nil, "A")
			ove_0_6.ahrimenu.Keys:keybind("flashe", "Flash E", "T", nil)
		end

		if ove_0_30 == 2 then
			ove_0_6.ahrimenu:menu("Combo", "Combo")
			ove_0_6.ahrimenu.Combo:menu("qsettings", "Orb of Deception - [Q]")
			ove_0_6.ahrimenu.Combo.qsettings:set("icon", player:spellSlot(0).icon)
			ove_0_6.ahrimenu.Combo.qsettings:boolean("useq", "在组合中启用 Q", true)
			ove_0_6.ahrimenu.Combo.qsettings:boolean("slowpredq", "启用 Q 缓慢预判", false)
			ove_0_6.ahrimenu.Combo:menu("wsettings", "Fox-Fire - [W]")
			ove_0_6.ahrimenu.Combo.wsettings:set("icon", player:spellSlot(1).icon)
			ove_0_6.ahrimenu.Combo.wsettings:boolean("usew", "在组合中启用 W", true)
			ove_0_6.ahrimenu.Combo:menu("esettings", "Charm - [E]")
			ove_0_6.ahrimenu.Combo.esettings:set("icon", player:spellSlot(2).icon)
			ove_0_6.ahrimenu.Combo.esettings:boolean("usee", "在组合中启用 E", true)
			ove_0_6.ahrimenu.Combo.esettings:boolean("slowprede", "启用 E 缓慢预判", false)
			ove_0_6.ahrimenu.Combo.esettings:boolean("autoe", "在冲刺目标上自动释放 E", true)
			ove_0_6.ahrimenu.Combo:menu("rsettings", "Spirit Rush - [R]")
			ove_0_6.ahrimenu.Combo.rsettings:set("icon", player:spellSlot(3).icon)
			ove_0_6.ahrimenu.Combo.rsettings:boolean("user", "在连招中启用 R", false)
			ove_0_6.ahrimenu:menu("Killsteal", "Killsteal")
			ove_0_6.ahrimenu.Killsteal:boolean("ksQ", "启用 Q 抢人头", true)
			ove_0_6.ahrimenu.Killsteal:boolean("ksW", "启用 W 抢人头", true)
			ove_0_6.ahrimenu.Killsteal:boolean("ksE", "启用 E 抢人头", true)
			ove_0_6.ahrimenu:menu("Clear", "Clear")
			ove_0_6.ahrimenu.Clear:header("Lane Clear", "Lane Clear")
			ove_0_6.ahrimenu.Clear:boolean("LcQ", "在清线时启用 Q", true)
			ove_0_6.ahrimenu.Clear:header("Jungle Clear", "Jungle Clear")
			ove_0_6.ahrimenu.Clear:boolean("JcQ", "在清野时启用 Q", true)
			ove_0_6.ahrimenu.Clear:boolean("JcW", "在清野时启用 W", true)
			ove_0_6.ahrimenu:menu("Misc", "Misc")
			ove_0_6.ahrimenu.Misc:boolean("antigap", "启用防突进", true)
			ove_0_6.ahrimenu.Misc:slider("antigaphp", "生命值百分比至防突进", 50, 1, 100, 1)
			ove_0_6.ahrimenu:menu("Draws", "Draws")
			ove_0_6.ahrimenu.Draws:dropdown("circlemode", "圆形模式", 1, {
				"默认",
				"彩虹 "
			})
			ove_0_6.ahrimenu.Draws:boolean("drawQ", "绘制 Q 范围", true)
			ove_0_6.ahrimenu.Draws:boolean("drawW", "绘制 W 范围", true)
			ove_0_6.ahrimenu.Draws:boolean("drawE", "绘制 E 范围", true)
			ove_0_6.ahrimenu.Draws:boolean("drawR", "绘制 R 范围", true)
			ove_0_6.ahrimenu.Draws:boolean("drawAA", "绘制普攻追踪器", true)
			ove_0_6.ahrimenu.Draws:boolean("drawbuff", "绘制增益持续时间", true)
			ove_0_6.ahrimenu:menu("Keys", "Keys")
			ove_0_6.ahrimenu.Keys:keybind("stogglebar", "组合键", "Space", nil)
			ove_0_6.ahrimenu.Keys:keybind("clearKey", "清理键", "V", nil)
			ove_0_6.ahrimenu.Keys.clearkey:permashow(true)
			ove_0_6.ahrimenu.Keys:keybind("farmtoggle", "启用法术收割", nil, "A")
			ove_0_6.ahrimenu.Keys:keybind("flashe", "闪现 E", "T", nil)
		end
	end

	if player.charName == "Blitzcrank" then
		ove_0_6.blitzmenu = menu("Fatality " .. player.charName, "[Fatality] Blitzcrank")

		ove_0_6.blitzmenu:set("icon", player.iconSquare)
		ove_0_6.blitzmenu:dropdown("language", "Language", 1, {
			"English",
			"CN"
		})
		ove_0_6.blitzmenu.language:set("tooltip", "Please Reload | 请重新加载")

		local ove_0_31 = ove_0_6.blitzmenu.language:get()

		if ove_0_31 == 1 then
			ove_0_6.blitzmenu:menu("Combo", "Combo")
			ove_0_6.blitzmenu.Combo:menu("qsettings", "Rocket Grab - [Q]")
			ove_0_6.blitzmenu.Combo.qsettings:set("icon", player:spellSlot(0).icon)
			ove_0_6.blitzmenu.Combo.qsettings:boolean("useQ", "Enable Q in Combo", true)
			ove_0_6.blitzmenu.Combo.qsettings:boolean("autoQ", "Auto Q on Dashing/CC Target", true)
			ove_0_6.blitzmenu.Combo.qsettings:boolean("slowpredq", "Enable Q Slow Prediction", false)
			ove_0_6.blitzmenu.Combo.qsettings:header("qblacklist", "Q Black List")

			for iter_0_2 = 0, objManager.enemies_n - 1 do
				local ove_0_32 = objManager.enemies[iter_0_2]

				ove_0_6.blitzmenu.Combo.qsettings:boolean(ove_0_32.charName, "Dont Cast Q on: " .. ove_0_32.charName, false)
			end

			ove_0_6.blitzmenu.Combo:menu("esettings", "Power Fist - [E]")
			ove_0_6.blitzmenu.Combo.esettings:set("icon", player:spellSlot(2).icon)
			ove_0_6.blitzmenu.Combo.esettings:boolean("useE", "Enable E in Combo", true)
			ove_0_6.blitzmenu.Combo:menu("rsettings", "Static Field - [R]")
			ove_0_6.blitzmenu.Combo.rsettings:set("icon", player:spellSlot(3).icon)
			ove_0_6.blitzmenu.Combo.rsettings:boolean("useR", "Enable R in Combo", true)
			ove_0_6.blitzmenu.Combo.rsettings:slider("targets", "Min Targets in R Range", 2, 1, 5, 1)
			ove_0_6.blitzmenu:menu("Killsteal", "Killsteal")
			ove_0_6.blitzmenu.Killsteal:boolean("ksQ", "Enable Q Killsteal", true)
			ove_0_6.blitzmenu.Killsteal:boolean("ksR", "Enable R Killsteal", false)
			ove_0_6.blitzmenu:menu("Draws", "Draws")
			ove_0_6.blitzmenu.Draws:dropdown("circlemode", "Circle Mode", 1, {
				"Default",
				"Rainbow"
			})
			ove_0_6.blitzmenu.Draws:boolean("drawQ", "Draw Q Range", true)
			ove_0_6.blitzmenu.Draws:boolean("drawR", "Draw R Range", true)
			ove_0_6.blitzmenu.Draws:boolean("drawAA", "Draw AA Tracker", true)
			ove_0_6.blitzmenu:menu("Keys", "Keys")
			ove_0_6.blitzmenu.Keys:keybind("stogglebar", "Combo Key", "Space", nil)
		end

		if ove_0_31 == 2 then
			ove_0_6.blitzmenu:menu("Combo", "连招")
			ove_0_6.blitzmenu.Combo:menu("qsettings", "机械飞爪 - [Q]")
			ove_0_6.blitzmenu.Combo.qsettings:set("icon", player:spellSlot(0).icon)
			ove_0_6.blitzmenu.Combo.qsettings:boolean("useQ", "连招中使用 Q", true)
			ove_0_6.blitzmenu.Combo.qsettings:boolean("autoQ", "对冲刺/控制目标自动使用 Q", true)
			ove_0_6.blitzmenu.Combo.qsettings:boolean("slowpredq", "启用 Q 慢速预测", false)
			ove_0_6.blitzmenu.Combo.qsettings:header("qblacklist", "Q 黑名单")

			for iter_0_3 = 0, objManager.enemies_n - 1 do
				local ove_0_33 = objManager.enemies[iter_0_3]

				ove_0_6.blitzmenu.Combo.qsettings:boolean(ove_0_33.charName, "不对以下目标使用 Q: " .. ove_0_33.charName, false)
			end

			ove_0_6.blitzmenu.Combo:menu("esettings", "能量铁拳 - [E]")
			ove_0_6.blitzmenu.Combo.esettings:set("icon", player:spellSlot(2).icon)
			ove_0_6.blitzmenu.Combo.esettings:boolean("useE", "连招中使用 E", true)
			ove_0_6.blitzmenu.Combo:menu("rsettings", "静电力场 - [R]")
			ove_0_6.blitzmenu.Combo.rsettings:set("icon", player:spellSlot(3).icon)
			ove_0_6.blitzmenu.Combo.rsettings:boolean("useR", "连招中使用 R", true)
			ove_0_6.blitzmenu.Combo.rsettings:slider("targets", "R 范围内最小目标数量", 2, 1, 5, 1)
			ove_0_6.blitzmenu:menu("Killsteal", "击杀窃取")
			ove_0_6.blitzmenu.Killsteal:boolean("ksQ", "启用 Q 击杀窃取", true)
			ove_0_6.blitzmenu.Killsteal:boolean("ksR", "启用 R 击杀窃取", false)
			ove_0_6.blitzmenu:menu("Draws", "绘图")
			ove_0_6.blitzmenu.Draws:dropdown("circlemode", "圈模式", 1, {
				"默认",
				"彩虹"
			})
			ove_0_6.blitzmenu.Draws:boolean("drawQ", "绘制 Q 范围", true)
			ove_0_6.blitzmenu.Draws:boolean("drawR", "绘制 R 范围", true)
			ove_0_6.blitzmenu.Draws:boolean("drawAA", "绘制普攻跟踪", true)
			ove_0_6.blitzmenu:menu("Keys", "按键")
			ove_0_6.blitzmenu.Keys:keybind("stogglebar", "连招键", "Space", nil)
		end
	end

	if player.charName == "Amumu" then
		ove_0_6.amumumenu = menu("Fatality " .. player.charName, "[Fatality] Amumu")

		ove_0_6.amumumenu:set("icon", player.iconSquare)
		ove_0_6.amumumenu:dropdown("language", "Language", 1, {
			"English",
			"CN"
		})
		ove_0_6.amumumenu.language:set("tooltip", "Please Reload | 请重新加载")

		local ove_0_34 = ove_0_6.amumumenu.language:get()

		if ove_0_34 == 1 then
			ove_0_6.amumumenu:menu("Combo", "Combo")
			ove_0_6.amumumenu.Combo:menu("qsettings", "Bandage Toss - [Q]")
			ove_0_6.amumumenu.Combo.qsettings:set("icon", player:spellSlot(0).icon)
			ove_0_6.amumumenu.Combo.qsettings:boolean("useq", "Enable Q in Combo", true)
			ove_0_6.amumumenu.Combo.qsettings:boolean("slowpredq", "Enable Q Slow Prediction", true)
			ove_0_6.amumumenu.Combo.qsettings:slider("hpslider", "Your Min Health % to Q", 20, 1, 100, 1)
			ove_0_6.amumumenu.Combo:menu("wsettings", "Despair - [W]")
			ove_0_6.amumumenu.Combo.wsettings:set("icon", player:spellSlot(1).icon)
			ove_0_6.amumumenu.Combo.wsettings:boolean("usew", "Auto W in Combo", true)
			ove_0_6.amumumenu.Combo.wsettings:slider("manapercent", "Min Mana % to W", 30, 1, 100, 1)
			ove_0_6.amumumenu.Combo:menu("esettings", "Tantrum - [E]")
			ove_0_6.amumumenu.Combo.esettings:set("icon", player:spellSlot(2).icon)
			ove_0_6.amumumenu.Combo.esettings:boolean("usee", "Enable E in Combo", true)
			ove_0_6.amumumenu.Combo:menu("rsettings", "Curse of the Sad Mummy - [R]")
			ove_0_6.amumumenu.Combo.rsettings:set("icon", player:spellSlot(3).icon)
			ove_0_6.amumumenu.Combo.rsettings:boolean("user", "Enable R in Combo", true)
			ove_0_6.amumumenu.Combo.rsettings:slider("ene", "Min Enemies to R", 2, 1, 5, 1)
			ove_0_6.amumumenu:menu("Killsteal", "Killsteal")
			ove_0_6.amumumenu.Killsteal:boolean("ksQ", "Enable Q Killsteal", true)
			ove_0_6.amumumenu.Killsteal:boolean("ksE", "Enable E Killsteal", true)
			ove_0_6.amumumenu.Killsteal:boolean("ksR", "Enable R Killsteal", false)
			ove_0_6.amumumenu:menu("Clear", "Clear")
			ove_0_6.amumumenu.Clear:header("laneclear", "Lane Clear")
			ove_0_6.amumumenu.Clear:boolean("LcW", "Enable W in Lane Clear", true)
			ove_0_6.amumumenu.Clear:boolean("LcE", "Enable E in Lane Clear", true)
			ove_0_6.amumumenu.Clear:header("jungleclear", "Jungle Clear")
			ove_0_6.amumumenu.Clear:boolean("JcQ", "Enable Q in Jungle Clear", true)
			ove_0_6.amumumenu.Clear:boolean("JcW", "Enable W in Jungle Clear", true)
			ove_0_6.amumumenu.Clear:boolean("JcE", "Enable E in Jungle Clear", true)
			ove_0_6.amumumenu:menu("Draws", "Draws")
			ove_0_6.amumumenu.Draws:dropdown("circlemode", "Circle Mode", 1, {
				"Default",
				"Rainbow"
			})
			ove_0_6.amumumenu.Draws:boolean("drawQ", "Draw Q Range", true)
			ove_0_6.amumumenu.Draws:boolean("drawE", "Draw E Range", true)
			ove_0_6.amumumenu.Draws:boolean("drawR", "Draw R Range", true)
			ove_0_6.amumumenu.Draws:boolean("drawAA", "Draw AA Tracker", true)
			ove_0_6.amumumenu:menu("Keys", "Keys")
			ove_0_6.amumumenu.Keys:keybind("stogglebar", "Combo Key", "Space", nil)
			ove_0_6.amumumenu.Keys:keybind("farmtoggle", "Enable Spell Farm", nil, "A")
			ove_0_6.amumumenu.Keys:keybind("clearkey", "Clear Key", "V", nil)
			ove_0_6.amumumenu.Keys.clearkey:permashow(true)
			ove_0_6.amumumenu.Keys:keybind("flashr", "Flash R", "T", nil)
			ove_0_6.amumumenu.Keys:keybind("tower", "Tower Dive", nil, "G")
		end

		if ove_0_34 == 2 then
			ove_0_6.amumumenu:menu("Combo", "Combo")
			ove_0_6.amumumenu.Combo:menu("qsettings", "绷带牵引 - [Q]")
			ove_0_6.amumumenu.Combo.qsettings:set("icon", player:spellSlot(0).icon)
			ove_0_6.amumumenu.Combo.qsettings:boolean("useq", "连招中使用Q", true)
			ove_0_6.amumumenu.Combo.qsettings:boolean("slowpredq", "启用Q慢速预测", true)
			ove_0_6.amumumenu.Combo.qsettings:slider("hpslider", "使用Q的最低血量%", 20, 1, 100, 1)
			ove_0_6.amumumenu.Combo:menu("wsettings", "绝望 - [W]")
			ove_0_6.amumumenu.Combo.wsettings:set("icon", player:spellSlot(1).icon)
			ove_0_6.amumumenu.Combo.wsettings:boolean("usew", "连招中自动使用W", true)
			ove_0_6.amumumenu.Combo.wsettings:slider("manapercent", "使用W的最低法力值%", 30, 1, 100, 1)
			ove_0_6.amumumenu.Combo:menu("esettings", "愤怒 - [E]")
			ove_0_6.amumumenu.Combo.esettings:set("icon", player:spellSlot(2).icon)
			ove_0_6.amumumenu.Combo.esettings:boolean("usee", "连招中使用E", true)
			ove_0_6.amumumenu.Combo:menu("rsettings", "木乃伊之咒 - [R]")
			ove_0_6.amumumenu.Combo.rsettings:set("icon", player:spellSlot(3).icon)
			ove_0_6.amumumenu.Combo.rsettings:boolean("user", "连招中使用R", true)
			ove_0_6.amumumenu.Combo.rsettings:slider("ene", "使用R的最小敌人数量", 2, 1, 5, 1)
			ove_0_6.amumumenu:menu("Killsteal", "Killsteal")
			ove_0_6.amumumenu.Killsteal:boolean("ksQ", "启用Q击杀", true)
			ove_0_6.amumumenu.Killsteal:boolean("ksE", "启用E击杀", true)
			ove_0_6.amumumenu.Killsteal:boolean("ksR", "启用R击杀", false)
			ove_0_6.amumumenu:menu("Clear", "Clear")
			ove_0_6.amumumenu.Clear:header("laneclear", "清线")
			ove_0_6.amumumenu.Clear:boolean("LcW", "在线上清线中使用W", true)
			ove_0_6.amumumenu.Clear:boolean("LcE", "在线上清线中使用E", true)
			ove_0_6.amumumenu.Clear:header("jungleclear", "清野")
			ove_0_6.amumumenu.Clear:boolean("JcQ", "在清野中使用Q", true)
			ove_0_6.amumumenu.Clear:boolean("JcW", "在清野中使用W", true)
			ove_0_6.amumumenu.Clear:boolean("JcE", "在清野中使用E", true)
			ove_0_6.amumumenu:menu("Draws", "Draws")
			ove_0_6.amumumenu.Draws:dropdown("circlemode", "圆圈模式", 1, {
				"默认",
				"彩虹"
			})
			ove_0_6.amumumenu.Draws:boolean("drawQ", "绘制Q范围", true)
			ove_0_6.amumumenu.Draws:boolean("drawE", "绘制E范围", true)
			ove_0_6.amumumenu.Draws:boolean("drawR", "绘制R范围", true)
			ove_0_6.amumumenu.Draws:boolean("drawAA", "绘制平A跟踪", true)
			ove_0_6.amumumenu:menu("Keys", "Keys")
			ove_0_6.amumumenu.Keys:keybind("stogglebar", "连招键", "Space", nil)
			ove_0_6.amumumenu.Keys:keybind("farmtoggle", "启用法术农兵", nil, "A")
			ove_0_6.amumumenu.Keys:keybind("clearkey", "清线键", "V", nil)
			ove_0_6.amumumenu.Keys.clearkey:permashow(true)
			ove_0_6.amumumenu.Keys:keybind("flashr", "闪现R", "T", nil)
			ove_0_6.amumumenu.Keys:keybind("tower", "塔下强杀", nil, "G")
		end
	end

	if player.charName == "Ashe" then
		ove_0_6.ashemenu = menu("Fatality " .. player.charName, "[Fatality] Ashe")

		ove_0_6.ashemenu:set("icon", player.iconSquare)
		ove_0_6.ashemenu:dropdown("language", "Language", 1, {
			"English",
			"CN"
		})
		ove_0_6.ashemenu.language:set("tooltip", "Please Reload | 请重新加载")

		local ove_0_35 = ove_0_6.ashemenu.language:get()

		if ove_0_35 == 1 then
			ove_0_6.ashemenu:menu("Combo", "Combo")
			ove_0_6.ashemenu.Combo:menu("qsettings", "Rangers Focus - [Q]")
			ove_0_6.ashemenu.Combo.qsettings:set("icon", player:spellSlot(0).icon)
			ove_0_6.ashemenu.Combo.qsettings:boolean("useq", "Enable Q in Combo", true)
			ove_0_6.ashemenu.Combo:menu("wsettings", "Volley - [W]")
			ove_0_6.ashemenu.Combo.wsettings:set("icon", player:spellSlot(1).icon)
			ove_0_6.ashemenu.Combo.wsettings:boolean("usew", "Enable W in Combo", true)
			ove_0_6.ashemenu.Combo:menu("esettings", "Hawkshot - [E]")
			ove_0_6.ashemenu.Combo.esettings:set("icon", player:spellSlot(2).icon)
			ove_0_6.ashemenu.Combo.esettings:boolean("useE", "Auto E if Target Enters Bush", true)
			ove_0_6.ashemenu.Combo:menu("rsettings", "Enchanted Crystal Arrow - [R]")
			ove_0_6.ashemenu.Combo.rsettings:set("icon", player:spellSlot(3).icon)
			ove_0_6.ashemenu.Combo.rsettings:boolean("user", "Enable R in Combo", true)
			ove_0_6.ashemenu.Combo.rsettings:boolean("slowrpred", "Enable Slow Prediction", false)
			ove_0_6.ashemenu.Combo.rsettings:slider("rhp", "Target Health % to use R", 50, 1, 100, 1)
			ove_0_6.ashemenu.Combo.rsettings:slider("rrange", "R Range", 1500, 0, 5000, 1)
			ove_0_6.ashemenu.Combo.rsettings:boolean("onlykill", "Use R only If Combo Damage is Enough", true)
			ove_0_6.ashemenu.Combo.rsettings:slider("aa", "Include x Auto Attacks in Damage", 2, 1, 5, 1)
			ove_0_6.ashemenu:menu("Killsteal", "Killsteal")
			ove_0_6.ashemenu.Killsteal:boolean("ksW", "Enable W Killsteal", true)
			ove_0_6.ashemenu.Killsteal:boolean("ksR", "Enable R Killsteal", false)
			ove_0_6.ashemenu:menu("Clear", "Clear")
			ove_0_6.ashemenu.Clear:header("laneclear", "Lane Clear")
			ove_0_6.ashemenu.Clear:boolean("LcW", "Enable W in Lane Clear", true)
			ove_0_6.ashemenu.Clear:header("jungleclear", "Jungle Clear")
			ove_0_6.ashemenu.Clear:boolean("JcQ", "Enable Q in Jungle Clear", true)
			ove_0_6.ashemenu.Clear:boolean("JcW", "Enable W in Jungle Clear", true)
			ove_0_6.ashemenu:menu("Misc", "Misc")
			ove_0_6.ashemenu.Misc:boolean("AG", "Enable Antigap Closer", true)
			ove_0_6.ashemenu.Misc:slider("aghealth", "Health % to Antigap Close", 50, 1, 100, 1)
			ove_0_6.ashemenu:menu("Draws", "Draws")
			ove_0_6.ashemenu.Draws:dropdown("circlemode", "Circle Mode", 1, {
				"Default",
				"Rainbow"
			})
			ove_0_6.ashemenu.Draws:boolean("draww", "Draw W Range", true)
			ove_0_6.ashemenu.Draws:boolean("drawe", "Draw E Range", true)
			ove_0_6.ashemenu.Draws:boolean("drawr", "Draw R Range", true)
			ove_0_6.ashemenu.Draws:boolean("drawAA", "Draw AA Tracker", true)
			ove_0_6.ashemenu:menu("Keys", "Keys")
			ove_0_6.ashemenu.Keys:keybind("stogglebar", "Combo Key", "Space", nil)
			ove_0_6.ashemenu.Keys:keybind("farmtoggle", "Enable Spell Farm", nil, "A")
			ove_0_6.ashemenu.Keys:keybind("clearkey", "Clear Key", "V", nil)
			ove_0_6.ashemenu.Keys.clearkey:permashow(true)
			ove_0_6.ashemenu.Keys:keybind("semir", "Semi R", "T", nil)
		end

		if ove_0_35 == 2 then
			ove_0_6.ashemenu:menu("Combo", "组合")
			ove_0_6.ashemenu.Combo:menu("qsettings", "流浪者焦点 - [Q]")
			ove_0_6.ashemenu.Combo.qsettings:set("icon", player:spellSlot(0).icon)
			ove_0_6.ashemenu.Combo.qsettings:boolean("useq", "在组合中启用 Q", true)
			ove_0_6.ashemenu.Combo:menu("wsettings", "截击 - [W]")
			ove_0_6.ashemenu.Combo.wsettings:set("icon", player:spellSlot(1).icon)
			ove_0_6.ashemenu.Combo.wsettings:boolean("usew", "在组合中启用 W", true)
			ove_0_6.ashemenu.Combo:menu("esettings", "鹰击 - [E]")
			ove_0_6.ashemenu.Combo.esettings:set("icon", player:spellSlot(2).icon)
			ove_0_6.ashemenu.Combo.esettings:boolean("useE", "如果目标进入灌木丛，则自动 E", true)
			ove_0_6.ashemenu.Combo:menu("rsettings", "魔法水晶箭 - [R]")
			ove_0_6.ashemenu.Combo.rsettings:set("icon", player:spellSlot(3).icon)
			ove_0_6.ashemenu.Combo.rsettings:boolean("user", "在组合中启用 R", true)
			ove_0_6.ashemenu.Combo.rsettings:boolean("slowrpred", "启用缓慢预测", false)
			ove_0_6.ashemenu.Combo.rsettings:slider("rhp", "使用 R 的目标生命值百分比", 50, 1, 100, 1)
			ove_0_6.ashemenu.Combo.rsettings:slider("rrange", "R 射程", 1500, 0, 5000, 1)
			ove_0_6.ashemenu.Combo.rsettings:boolean("onlykill", "仅在连招伤害足够时使用 R", true)
			ove_0_6.ashemenu.Combo.rsettings:slider("aa", "在伤害中包括 x 次普攻", 2, 1, 5, 1)
			ove_0_6.ashemenu:menu("Killsteal", "偷窃")
			ove_0_6.ashemenu.Killsteal:boolean("ksW", "启用 W 抢人头", true)
			ove_0_6.ashemenu.Killsteal:boolean("ksR", "启用 R 抢人头", false)
			ove_0_6.ashemenu:menu("Clear", "清除")
			ove_0_6.ashemenu.Clear:header("laneclear", "清除车道")
			ove_0_6.ashemenu.Clear:boolean("LcW", "在清线时启用 W", true)
			ove_0_6.ashemenu.Clear:header("jungleclear", "丛林清除")
			ove_0_6.ashemenu.Clear:boolean("JcQ", "在清野时启用 Q", true)
			ove_0_6.ashemenu.Clear:boolean("JcW", "在清野时启用 W", true)
			ove_0_6.ashemenu:menu("Misc", "杂项")
			ove_0_6.ashemenu.Misc:boolean("AG", "启用防突进", true)
			ove_0_6.ashemenu.Misc:slider("aghealth", "防突进的生命值百分比", 50, 1, 100, 1)
			ove_0_6.ashemenu:menu("Draws", "抽奖")
			ove_0_6.ashemenu.Draws:dropdown("circlemode", "圆形模式", 1, {
				"默认",
				"彩虹"
			})
			ove_0_6.ashemenu.Draws:boolean("draww", "绘制 W 范围", true)
			ove_0_6.ashemenu.Draws:boolean("drawe", "绘制 E 范围", true)
			ove_0_6.ashemenu.Draws:boolean("drawr", "绘制 R 范围", true)
			ove_0_6.ashemenu.Draws:boolean("drawAA", "绘制普攻追踪器", true)
			ove_0_6.ashemenu:menu("Keys", "按键")
			ove_0_6.ashemenu.Keys:keybind("stogglebar", "组合键", "Space", nil)
			ove_0_6.ashemenu.Keys:keybind("farmtoggle", "启用法术打怪", nil, "A")
			ove_0_6.ashemenu.Keys:keybind("clearkey", "清除键", "V", nil)
			ove_0_6.ashemenu.Keys.clearkey:permashow(true)
			ove_0_6.ashemenu.Keys:keybind("semir", "半自动释放", "T", nil)
		end
	end

	if player.charName == "Brand" then
		ove_0_6.brandmenu = menu("Fatality " .. player.charName, "[Fatality] Brand")

		ove_0_6.brandmenu:set("icon", player.iconSquare)
		ove_0_6.brandmenu:dropdown("language", "Language", 1, {
			"English",
			"CN"
		})
		ove_0_6.brandmenu.language:set("tooltip", "Please Reload | 请重新加载")

		local ove_0_36 = ove_0_6.brandmenu.language:get()

		if ove_0_36 == 1 then
			ove_0_6.brandmenu:menu("Combo", "Combo")
			ove_0_6.brandmenu.Combo:menu("qsettings", "Sear - [Q]")
			ove_0_6.brandmenu.Combo.qsettings:set("icon", player:spellSlot(0).icon)
			ove_0_6.brandmenu.Combo.qsettings:boolean("useq", "Enable Q in Combo", true)
			ove_0_6.brandmenu.Combo.qsettings:boolean("slowpredq", "Enable Q Slow Prediction", false)
			ove_0_6.brandmenu.Combo.qsettings:boolean("autoq", "Auto Q on Dash", true)
			ove_0_6.brandmenu.Combo.qsettings.autoq:set("tooltip", "Only on Ablazed Targets")
			ove_0_6.brandmenu.Combo:menu("wsettings", "Pillar of Flame - [W]")
			ove_0_6.brandmenu.Combo.wsettings:set("icon", player:spellSlot(1).icon)
			ove_0_6.brandmenu.Combo.wsettings:boolean("usew", "Enable W in Combo", true)
			ove_0_6.brandmenu.Combo.wsettings:boolean("slowpredw", "Enable W Slow Prediction", false)
			ove_0_6.brandmenu.Combo.wsettings:boolean("autow", "Auto W on Dash", true)
			ove_0_6.brandmenu.Combo:menu("esettings", "Conflagration - [E]")
			ove_0_6.brandmenu.Combo.esettings:set("icon", player:spellSlot(2).icon)
			ove_0_6.brandmenu.Combo.esettings:boolean("usee", "Enable E in Combo", true)
			ove_0_6.brandmenu.Combo:menu("rsettings", "Pyroclasm - [R]")
			ove_0_6.brandmenu.Combo.rsettings:boolean("user", "Enable R in Combo", true)
			ove_0_6.brandmenu.Combo.rsettings:set("icon", player:spellSlot(3).icon)
			ove_0_6.brandmenu.Combo.rsettings:boolean("overkillprod", "Enable Overkill Protection", true)
			ove_0_6.brandmenu.Combo.rsettings:slider("overkillHP", "Overkill HP %", 10, 1, 100, 1)
			ove_0_6.brandmenu:menu("Killsteal", "Killsteal")
			ove_0_6.brandmenu.Killsteal:boolean("ksq", "Enable Q Killsteal", true)
			ove_0_6.brandmenu.Killsteal:boolean("ksw", "Enable W Killsteal", true)
			ove_0_6.brandmenu.Killsteal:boolean("kse", "Enable E Killsteal", true)
			ove_0_6.brandmenu:menu("Clear", "Clear")
			ove_0_6.brandmenu.Clear:header("laneclear", "Lane Clear")
			ove_0_6.brandmenu.Clear:boolean("LcW", "Enable W in Lane Clear", true)
			ove_0_6.brandmenu.Clear:boolean("LcE", "Enable E in Lane Clear", true)
			ove_0_6.brandmenu.Clear:header("jungleclear", "Jungle Clear")
			ove_0_6.brandmenu.Clear:boolean("JcQ", "Enable Q in Jungle Clear", true)
			ove_0_6.brandmenu.Clear:boolean("JcW", "Enable W in Jungle Clear", true)
			ove_0_6.brandmenu.Clear:boolean("JcE", "Enable E in Jungle Clear", true)
			ove_0_6.brandmenu:menu("Misc", "Misc")
			ove_0_6.brandmenu.Misc:boolean("AG", "Enable Antigap Closer", true)
			ove_0_6.brandmenu.Misc:slider("agh", "Health % to Antigap Close", 50, 1, 100, 1)
			ove_0_6.brandmenu:menu("Draws", "Draws")
			ove_0_6.brandmenu.Draws:dropdown("circlemode", "Circle Mode", 1, {
				"Default",
				"Rainbow"
			})
			ove_0_6.brandmenu.Draws:boolean("drawq", "Draw Q Range", true)
			ove_0_6.brandmenu.Draws:boolean("draww", "Draw W Range", true)
			ove_0_6.brandmenu.Draws:boolean("drawe", "Draw E Range", true)
			ove_0_6.brandmenu.Draws:boolean("drawr", "Draw R Range", true)
			ove_0_6.brandmenu.Draws:boolean("drawAA", "Draw AA Tracker", true)
			ove_0_6.brandmenu:menu("Keys", "Keys")
			ove_0_6.brandmenu.Keys:keybind("stogglebar", "Combo Key", "Space", nil)
			ove_0_6.brandmenu.Keys:keybind("farmtoggle", "Enable Spell Farm", nil, "A")
			ove_0_6.brandmenu.Keys:keybind("clearkey", "Clear Key", "V", nil)
			ove_0_6.brandmenu.Keys.clearkey:permashow(true)
			ove_0_6.brandmenu.Keys:keybind("Qstuntoggle", "Only Q Stun", nil, "T")
			ove_0_6.brandmenu.Keys:keybind("semir", "Semi R", "G", nil)
		end

		if ove_0_36 == 2 then
			ove_0_6.brandmenu:menu("Combo", "Combo")
			ove_0_6.brandmenu.Combo:menu("qsettings", "Sear - [Q]")
			ove_0_6.brandmenu.Combo.qsettings:set("icon", player:spellSlot(0).icon)
			ove_0_6.brandmenu.Combo.qsettings:boolean("useq", "在组合中启用 Q", true)
			ove_0_6.brandmenu.Combo.qsettings:boolean("slowpredq", "启用 Q 缓慢预判", false)
			ove_0_6.brandmenu.Combo.qsettings:boolean("autoq", "冲锋自动 Q", true)
			ove_0_6.brandmenu.Combo.qsettings.autoq:set("tooltip", "只对被灼烧目标使用")
			ove_0_6.brandmenu.Combo:menu("wsettings", "Pillar of Flame - [W]")
			ove_0_6.brandmenu.Combo.wsettings:set("icon", player:spellSlot(1).icon)
			ove_0_6.brandmenu.Combo.wsettings:boolean("usew", "在组合中启用 W", true)
			ove_0_6.brandmenu.Combo.wsettings:boolean("slowpredw", "启用 W 缓慢预判", false)
			ove_0_6.brandmenu.Combo.wsettings:boolean("autow", "冲锋自动 W", true)
			ove_0_6.brandmenu.Combo:menu("esettings", "Conflagration - [E]")
			ove_0_6.brandmenu.Combo.esettings:set("icon", player:spellSlot(2).icon)
			ove_0_6.brandmenu.Combo.esettings:boolean("usee", "在组合中启用 E", true)
			ove_0_6.brandmenu.Combo:menu("rsettings", "Pyroclasm - [R]")
			ove_0_6.brandmenu.Combo.rsettings:boolean("user", "在组合中启用 R", true)
			ove_0_6.brandmenu.Combo.rsettings:set("icon", player:spellSlot(3).icon)
			ove_0_6.brandmenu.Combo.rsettings:boolean("overkillprod", "启用过载保护", true)
			ove_0_6.brandmenu.Combo.rsettings:slider("overkillHP", "过量伤害生命值百分比", 10, 1, 100, 1)
			ove_0_6.brandmenu:menu("Killsteal", "Killsteal")
			ove_0_6.brandmenu.Killsteal:boolean("ksq", "启用 Q 抢人头l", true)
			ove_0_6.brandmenu.Killsteal:boolean("ksw", "启用 W 抢人头l", true)
			ove_0_6.brandmenu.Killsteal:boolean("kse", "启用 E 抢人头l", true)
			ove_0_6.brandmenu:menu("Clear", "Clear")
			ove_0_6.brandmenu.Clear:header("laneclear", "Lane Clear")
			ove_0_6.brandmenu.Clear:boolean("LcW", "在清线中启用 W", true)
			ove_0_6.brandmenu.Clear:boolean("LcE", "在清线中启用 E", true)
			ove_0_6.brandmenu.Clear:header("jungleclear", "Jungle Clear")
			ove_0_6.brandmenu.Clear:boolean("JcQ", "在清野中启用 Q", true)
			ove_0_6.brandmenu.Clear:boolean("JcW", "在清野中启用 W", true)
			ove_0_6.brandmenu.Clear:boolean("JcE", "在清野中启用 R", true)
			ove_0_6.brandmenu:menu("Misc", "Misc")
			ove_0_6.brandmenu.Misc:boolean("AG", "启用防突进功能", true)
			ove_0_6.brandmenu.Misc:slider("agh", "生命值％至防突进关闭", 50, 1, 100, 1)
			ove_0_6.brandmenu:menu("Draws", "Draws")
			ove_0_6.brandmenu.Draws:dropdown("circlemode", "圆形模式", 1, {
				"默认",
				"彩虹 "
			})
			ove_0_6.brandmenu.Draws:boolean("drawq", "绘制 Q 范围", true)
			ove_0_6.brandmenu.Draws:boolean("draww", "绘制 W 范围", true)
			ove_0_6.brandmenu.Draws:boolean("drawe", "绘制 E 范围", true)
			ove_0_6.brandmenu.Draws:boolean("drawr", "绘制 R 范围", true)
			ove_0_6.brandmenu.Draws:boolean("drawAA", "绘制普攻追踪器", true)
			ove_0_6.brandmenu:menu("Keys", "Keys")
			ove_0_6.brandmenu.Keys:keybind("stogglebar", "组合键", "Space", nil)
			ove_0_6.brandmenu.Keys:keybind("farmtoggle", "启用法术农场", nil, "A")
			ove_0_6.brandmenu.Keys:keybind("clearkey", "清理键", "V", nil)
			ove_0_6.brandmenu.Keys.clearkey:permashow(true)
			ove_0_6.brandmenu.Keys:keybind("Qstuntoggle", "仅限 Q 晕", nil, "T")
			ove_0_6.brandmenu.Keys:keybind("semir", "半自动 R", "G", nil)
		end
	end

	if player.charName == "Caitlyn" then
		ove_0_6.caitmenu = menu("Fatality " .. player.charName, "[Fatality] Caitlyn")

		ove_0_6.caitmenu:set("icon", player.iconSquare)
		ove_0_6.caitmenu:dropdown("language", "Language", 1, {
			"English",
			"CN"
		})
		ove_0_6.caitmenu.language:set("tooltip", "Please Reload | 请重新加载")

		local ove_0_37 = ove_0_6.caitmenu.language:get()

		if ove_0_37 == 1 then
			ove_0_6.caitmenu:menu("Combo", "Combo")
			ove_0_6.caitmenu.Combo:menu("qsettings", "Piltover Peacemaker - [Q]")
			ove_0_6.caitmenu.Combo.qsettings:set("icon", player:spellSlot(0).icon)
			ove_0_6.caitmenu.Combo.qsettings:boolean("useq", "Enable Q in Combo", true)
			ove_0_6.caitmenu.Combo.qsettings:boolean("slowpredq", "Enable Q Slow Prediction", false)
			ove_0_6.caitmenu.Combo.qsettings:boolean("noaa", "Dont Q if Target is in AA Range", true)
			ove_0_6.caitmenu.Combo.qsettings:boolean("autoq", "Auto Q if Target is Stunned", true)
			ove_0_6.caitmenu.Combo:menu("wsettings", "Yordle Snap Trap - [W]")
			ove_0_6.caitmenu.Combo.wsettings:set("icon", player:spellSlot(1).icon)
			ove_0_6.caitmenu.Combo.wsettings:boolean("autow", "Auto W on Stunned Targets", true)
			ove_0_6.caitmenu.Combo:menu("esettings", "90 Caliber Net - [E]")
			ove_0_6.caitmenu.Combo.esettings:set("icon", player:spellSlot(2).icon)
			ove_0_6.caitmenu.Combo.esettings:boolean("usee", "Enable E in Combo", true)
			ove_0_6.caitmenu.Combo.esettings:boolean("slowprede", "Enable E Slow Prediction", false)
			ove_0_6.caitmenu.Combo.esettings:boolean("eq", "Use EQ if Possible", true)
			ove_0_6.caitmenu.Combo.esettings:boolean("ew", "Use EW if Possible", true)
			ove_0_6.caitmenu.Combo:menu("rsettings", "Ace in the Hole - [R]")
			ove_0_6.caitmenu.Combo.rsettings:set("icon", player:spellSlot(3).icon)
			ove_0_6.caitmenu.Combo.rsettings:keybind("semir", "Semi R if target is Killable", "T", nil)
			ove_0_6.caitmenu:menu("Killsteal", "Killsteal")
			ove_0_6.caitmenu.Killsteal:boolean("ksQ", "Enable Q Killsteal", true)
			ove_0_6.caitmenu.Killsteal:boolean("ksR", "Enable R Killsteal", true)
			ove_0_6.caitmenu:menu("Clear", "Clear")
			ove_0_6.caitmenu.Clear:header("laneclear", "Lane Clear")
			ove_0_6.caitmenu.Clear:boolean("LcQ", "Enable Q in Lane Clear", true)
			ove_0_6.caitmenu.Clear:header("jungleclear", "Jungle Clear")
			ove_0_6.caitmenu.Clear:boolean("JcQ", "Enable Q in Jungle Clear", true)
			ove_0_6.caitmenu:menu("Misc", "Misc")
			ove_0_6.caitmenu.Misc:boolean("AGE", "Enable E Antigap Closer", true)
			ove_0_6.caitmenu.Misc:boolean("AGW", "Enable W Antigap Closer", true)
			ove_0_6.caitmenu.Misc:slider("agh", "Health % to Antigap Close", 50, 1, 100, 1)
			ove_0_6.caitmenu:menu("Draws", "Draws")
			ove_0_6.caitmenu.Draws:dropdown("circlemode", "Circle Mode", 1, {
				"Default",
				"Rainbow"
			})
			ove_0_6.caitmenu.Draws:boolean("drawq", "Draw Q Range", true)
			ove_0_6.caitmenu.Draws:boolean("draww", "Draw W Range", true)
			ove_0_6.caitmenu.Draws:boolean("drawe", "Draw E Range", true)
			ove_0_6.caitmenu.Draws:boolean("drawr", "Draw R Range", true)
			ove_0_6.caitmenu.Draws:boolean("drawAA", "Draw AA Tracker", true)
			ove_0_6.caitmenu.Draws:boolean("drawrkill", "Draw Killable R Targets", true)
			ove_0_6.caitmenu:menu("Keys", "Keys")
			ove_0_6.caitmenu.Keys:keybind("stogglebar", "Combo Key", "Space", nil)
			ove_0_6.caitmenu.Keys:keybind("farmtoggle", "Enable Spell Farm", nil, "A")
			ove_0_6.caitmenu.Keys:keybind("clearkey", "Clear Key", "V", nil)
			ove_0_6.caitmenu.Keys.clearkey:permashow(true)
		end

		if ove_0_37 == 2 then
			ove_0_6.caitmenu:menu("Combo", "Combo")
			ove_0_6.caitmenu.Combo:menu("qsettings", "Piltover Peacemaker - [Q]")
			ove_0_6.caitmenu.Combo.qsettings:set("icon", player:spellSlot(0).icon)
			ove_0_6.caitmenu.Combo.qsettings:boolean("useq", "在组合中启用 Q", true)
			ove_0_6.caitmenu.Combo.qsettings:boolean("slowpredq", "启用 Q 缓慢预判", false)
			ove_0_6.caitmenu.Combo.qsettings:boolean("noaa", "如果目标在普攻范围内，请不要使用 Q", true)
			ove_0_6.caitmenu.Combo.qsettings:boolean("autoq", "如果目标被眩晕，请自动使用 Q", true)
			ove_0_6.caitmenu.Combo:menu("wsettings", "Yordle Snap Trap - [W]")
			ove_0_6.caitmenu.Combo.wsettings:set("icon", player:spellSlot(1).icon)
			ove_0_6.caitmenu.Combo.wsettings:boolean("autow", "在眩晕的目标上自动释放 W", true)
			ove_0_6.caitmenu.Combo:menu("esettings", "90 Caliber Net - [E]")
			ove_0_6.caitmenu.Combo.esettings:set("icon", player:spellSlot(2).icon)
			ove_0_6.caitmenu.Combo.esettings:boolean("usee", "在组合中启用 E", true)
			ove_0_6.caitmenu.Combo.esettings:boolean("slowprede", "启用 E 缓慢预判", false)
			ove_0_6.caitmenu.Combo.esettings:boolean("eq", "如果可能的话使用 EQ", true)
			ove_0_6.caitmenu.Combo.esettings:boolean("ew", "如果可能的话使用 EW", true)
			ove_0_6.caitmenu.Combo:menu("rsettings", "Ace in the Hole - [R]")
			ove_0_6.caitmenu.Combo.rsettings:set("icon", player:spellSlot(3).icon)
			ove_0_6.caitmenu.Combo.rsettings:keybind("semir", "如果目标可击杀，则半自动释放 R", "T", nil)
			ove_0_6.caitmenu:menu("Killsteal", "Killsteal")
			ove_0_6.caitmenu.Killsteal:boolean("ksQ", "启用 Q 抢人头", true)
			ove_0_6.caitmenu.Killsteal:boolean("ksR", "启用 R 抢人头", true)
			ove_0_6.caitmenu:menu("Clear", "Clear")
			ove_0_6.caitmenu.Clear:header("laneclear", "Lane Clear")
			ove_0_6.caitmenu.Clear:boolean("LcQ", "在清线时启用 Q", true)
			ove_0_6.caitmenu.Clear:header("jungleclear", "Jungle Clear")
			ove_0_6.caitmenu.Clear:boolean("JcQ", "在清野时启用 Q", true)
			ove_0_6.caitmenu:menu("Misc", "Misc")
			ove_0_6.caitmenu.Misc:boolean("AGE", "启用 E 护体", true)
			ove_0_6.caitmenu.Misc:boolean("AGW", "启用 W 护体", true)
			ove_0_6.caitmenu.Misc:slider("agh", "血量百分比至防突进", 50, 1, 100, 1)
			ove_0_6.caitmenu:menu("Draws", "Draws")
			ove_0_6.caitmenu.Draws:dropdown("circlemode", "圆形模式", 1, {
				"默认",
				"彩虹 "
			})
			ove_0_6.caitmenu.Draws:boolean("drawq", "绘制 Q 范围", true)
			ove_0_6.caitmenu.Draws:boolean("draww", "绘制 W 范围", true)
			ove_0_6.caitmenu.Draws:boolean("drawe", "绘制 E 范围", true)
			ove_0_6.caitmenu.Draws:boolean("drawr", "绘制 R 范围", true)
			ove_0_6.caitmenu.Draws:boolean("drawAA", "绘制普攻追踪器", true)
			ove_0_6.caitmenu.Draws:boolean("drawrkill", "绘制可击杀目标的 R 范围", true)
			ove_0_6.caitmenu:menu("Keys", "Keys")
			ove_0_6.caitmenu.Keys:keybind("stogglebar", "组合键", "Space", nil)
			ove_0_6.caitmenu.Keys:keybind("farmtoggle", "启用法术收割", nil, "A")
			ove_0_6.caitmenu.Keys:keybind("clearkey", "清理键", "V", nil)
			ove_0_6.caitmenu.Keys.clearkey:permashow(true)
		end
	end

	if player.charName == "Corki" then
		ove_0_6.corkimenu = menu("Fatality " .. player.charName, "[Fatality] Corki")

		ove_0_6.corkimenu:set("icon", player.iconSquare)
		ove_0_6.corkimenu:dropdown("language", "Language", 1, {
			"English",
			"CN"
		})
		ove_0_6.corkimenu.language:set("tooltip", "Please Reload | 请重新加载")

		local ove_0_38 = ove_0_6.corkimenu.language:get()

		if ove_0_38 == 1 then
			ove_0_6.corkimenu:menu("Combo", "Combo")
			ove_0_6.corkimenu.Combo:menu("qsettings", "Phosphorus Bomb - [Q]")
			ove_0_6.corkimenu.Combo.qsettings:set("icon", player:spellSlot(0).icon)
			ove_0_6.corkimenu.Combo.qsettings:boolean("useq", "Enable Q in Combo", true)
			ove_0_6.corkimenu.Combo.qsettings:slider("qrange", "Decrease Q Range", 50, 0, 100, 1)
			ove_0_6.corkimenu.Combo.qsettings:boolean("slowpredq", "Enable Q Slow Prediction", false)
			ove_0_6.corkimenu.Combo:menu("esettings", "Gatling Gun - [E]")
			ove_0_6.corkimenu.Combo.esettings:set("icon", player:spellSlot(2).icon)
			ove_0_6.corkimenu.Combo.esettings:boolean("usee", "Enable E in Combo", true)
			ove_0_6.corkimenu.Combo.esettings:slider("erange", "Decrease E Range", 25, 0, 100, 1)
			ove_0_6.corkimenu.Combo.esettings:dropdown("emode", "E Mode", 2, {
				"Always",
				"After AA"
			})
			ove_0_6.corkimenu.Combo:menu("rsettings", "Missile Barrage - [R]")
			ove_0_6.corkimenu.Combo.rsettings:set("icon", player:spellSlot(3).icon)
			ove_0_6.corkimenu.Combo.rsettings:boolean("user", "Enable R in Combo", true)
			ove_0_6.corkimenu.Combo.rsettings:boolean("user2", "Enable BiG R in Combo", true)
			ove_0_6.corkimenu.Combo.rsettings:boolean("slowpredr", "Enable R Slow Prediction", false)
			ove_0_6.corkimenu:menu("Killsteal", "Killsteal")
			ove_0_6.corkimenu.Killsteal:boolean("ksQ", "Enable Q Killsteal", true)
			ove_0_6.corkimenu.Killsteal:boolean("ksR", "Enable R Killsteal", true)
			ove_0_6.corkimenu:menu("Clear", "Clear")
			ove_0_6.corkimenu.Clear:header("Lane Clear", "Lane Clear")
			ove_0_6.corkimenu.Clear:boolean("LcQ", "Enable Q in Lane Clear", true)
			ove_0_6.corkimenu.Clear:boolean("LcE", "Enable E in Lane Clear", true)
			ove_0_6.corkimenu.Clear:header("Jungle Clear", "Jungle Clear")
			ove_0_6.corkimenu.Clear:boolean("JcQ", "Enable Q in Jungle Clear", true)
			ove_0_6.corkimenu.Clear:boolean("JcE", "Enable E in Jungle Clear", true)
			ove_0_6.corkimenu:menu("Misc", "Misc")
			ove_0_6.corkimenu.Misc:boolean("AG", "Enable Antigap Closer", true)
			ove_0_6.corkimenu.Misc:slider("antigaphp", "HP % to Anti Gapclose", 50, 1, 100, 1)
			ove_0_6.corkimenu:menu("Draws", "Draws")
			ove_0_6.corkimenu.Draws:dropdown("circlemode", "Circle Mode", 1, {
				"Default",
				"Rainbow"
			})
			ove_0_6.corkimenu.Draws:boolean("drawq", "Draw Q Range", true)
			ove_0_6.corkimenu.Draws:boolean("draww", "Draw W Range", true)
			ove_0_6.corkimenu.Draws:boolean("drawe", "Draw E Range", true)
			ove_0_6.corkimenu.Draws:boolean("drawr", "Draw R Range", true)
			ove_0_6.corkimenu.Draws:boolean("drawAA", "Draw AA Tracker", true)
			ove_0_6.corkimenu:menu("Keys", "Keys")
			ove_0_6.corkimenu.Keys:keybind("stogglebar", "Combo Key", "Space", nil)
			ove_0_6.corkimenu.Keys:keybind("clearkey", "Clear Key", "V", nil)
			ove_0_6.corkimenu.Keys.clearkey:permashow(true)
			ove_0_6.corkimenu.Keys:keybind("farmtoggle", "Enable Spell Farming", nil, "A")
			ove_0_6.corkimenu.Keys:keybind("semir", "Semi R", "T", nil)
		end

		if ove_0_38 == 2 then
			ove_0_6.corkimenu:menu("Combo", "连招")
			ove_0_6.corkimenu.Combo:menu("qsettings", "磷光炸弹 - [Q]")
			ove_0_6.corkimenu.Combo.qsettings:set("icon", player:spellSlot(0).icon)
			ove_0_6.corkimenu.Combo.qsettings:boolean("useq", "在连招中使用 Q", true)
			ove_0_6.corkimenu.Combo.qsettings:slider("qrange", "减少 Q 范围", 50, 0, 100, 1)
			ove_0_6.corkimenu.Combo.qsettings:boolean("slowpredq", "启用 Q 缓慢预测", false)
			ove_0_6.corkimenu.Combo:menu("esettings", "加特林 - [E]")
			ove_0_6.corkimenu.Combo.esettings:set("icon", player:spellSlot(2).icon)
			ove_0_6.corkimenu.Combo.esettings:boolean("usee", "在连招中使用 E", true)
			ove_0_6.corkimenu.Combo.esettings:slider("erange", "减少 E 范围", 25, 0, 100, 1)
			ove_0_6.corkimenu.Combo.esettings:dropdown("emode", "E 模式", 2, {
				"始终",
				"普攻后"
			})
			ove_0_6.corkimenu.Combo:menu("rsettings", "导弹攻击 - [R]")
			ove_0_6.corkimenu.Combo.rsettings:set("icon", player:spellSlot(3).icon)
			ove_0_6.corkimenu.Combo.rsettings:boolean("user", "在连招中使用 R", true)
			ove_0_6.corkimenu.Combo.rsettings:boolean("user2", "在连招中使用大 R", true)
			ove_0_6.corkimenu.Combo.rsettings:boolean("slowpredr", "启用 R 缓慢预测", false)
			ove_0_6.corkimenu:menu("Killsteal", "抢人头")
			ove_0_6.corkimenu.Killsteal:boolean("ksQ", "启用 Q 抢人头", true)
			ove_0_6.corkimenu.Killsteal:boolean("ksR", "启用 R 抢人头", true)
			ove_0_6.corkimenu:menu("Clear", "清线")
			ove_0_6.corkimenu.Clear:header("Lane Clear", "清线")
			ove_0_6.corkimenu.Clear:boolean("LcQ", "在清线中启用 Q", true)
			ove_0_6.corkimenu.Clear:boolean("LcE", "在清线中启用 E", true)
			ove_0_6.corkimenu.Clear:header("Jungle Clear", "清野")
			ove_0_6.corkimenu.Clear:boolean("JcQ", "在清野中启用 Q", true)
			ove_0_6.corkimenu.Clear:boolean("JcE", "在清野中启用 E", true)
			ove_0_6.corkimenu:menu("Misc", "其他")
			ove_0_6.corkimenu.Misc:boolean("AG", "启用防突进", true)
			ove_0_6.corkimenu.Misc:slider("antigaphp", "防突进的生命值百分比", 50, 1, 100, 1)
			ove_0_6.corkimenu:menu("Draws", "绘图")
			ove_0_6.corkimenu.Draws:dropdown("circlemode", "圆圈模式", 1, {
				"默认",
				"彩虹"
			})
			ove_0_6.corkimenu.Draws:boolean("drawq", "绘制 Q 范围", true)
			ove_0_6.corkimenu.Draws:boolean("draww", "绘制 W 范围", true)
			ove_0_6.corkimenu.Draws:boolean("drawe", "绘制 E 范围", true)
			ove_0_6.corkimenu.Draws:boolean("drawr", "绘制 R 范围", true)
			ove_0_6.corkimenu.Draws:boolean("drawAA", "绘制普攻追踪器", true)
			ove_0_6.corkimenu:menu("Keys", "按键")
			ove_0_6.corkimenu.Keys:keybind("stogglebar", "连招按键", "空格", nil)
			ove_0_6.corkimenu.Keys:keybind("clearkey", "清线按键", "V", nil)
			ove_0_6.corkimenu.Keys.clearkey:permashow(true)
			ove_0_6.corkimenu.Keys:keybind("farmtoggle", "启用法术清野", nil, "A")
			ove_0_6.corkimenu.Keys:keybind("semir", "半自动 R", "T", nil)
		end
	end

	if player.charName == "Ezreal" then
		ove_0_6.ezrealmenu = menu("Fatality " .. player.charName, "[Fatality] Ezreal")

		ove_0_6.ezrealmenu:set("icon", player.iconSquare)
		ove_0_6.ezrealmenu:dropdown("language", "Language", 1, {
			"English",
			"CN"
		})
		ove_0_6.ezrealmenu.language:set("tooltip", "Please Reload | 请重新加载")

		local ove_0_39 = ove_0_6.ezrealmenu.language:get()

		if ove_0_39 == 1 then
			ove_0_6.ezrealmenu:menu("Combo", "Combo")
			ove_0_6.ezrealmenu.Combo:menu("qsettings", "Mystic Shot - [Q]")
			ove_0_6.ezrealmenu.Combo.qsettings:set("icon", player:spellSlot(0).icon)
			ove_0_6.ezrealmenu.Combo.qsettings:boolean("useq", "Enable Q in Combo", true)
			ove_0_6.ezrealmenu.Combo.qsettings:boolean("qslow", "Enable Q Slow Prediction", false)
			ove_0_6.ezrealmenu.Combo.qsettings:boolean("autoQ", "Auto Q on Dashing Target", true)
			ove_0_6.ezrealmenu.Combo:menu("wsettings", "Essence Flux - [W]")
			ove_0_6.ezrealmenu.Combo.wsettings:set("icon", player:spellSlot(1).icon)
			ove_0_6.ezrealmenu.Combo.wsettings:boolean("usew", "Enable W in Combo", true)
			ove_0_6.ezrealmenu.Combo.wsettings:boolean("slowpredw", "Enable W Slow Prediction", false)
			ove_0_6.ezrealmenu.Combo.wsettings:boolean("waitforq", "Wait for Q", true)
			ove_0_6.ezrealmenu.Combo.wsettings:boolean("afteraaw", "Use W After AA", false)
			ove_0_6.ezrealmenu.Combo:menu("rsettings", "Trueshot Barrage - [R]")
			ove_0_6.ezrealmenu.Combo.rsettings:set("icon", player:spellSlot(3).icon)
			ove_0_6.ezrealmenu.Combo.rsettings:boolean("autor", "Auto R on CC Targets", false)
			ove_0_6.ezrealmenu.Combo.rsettings:slider("autorHp", "HP % To Auto R Target", 50, 1, 100, 1)
			ove_0_6.ezrealmenu.Combo.rsettings:slider("rrange", "R Range", 1000, 500, 4000, 1)
			ove_0_6.ezrealmenu:menu("Killsteal", "Killsteal")
			ove_0_6.ezrealmenu.Killsteal:boolean("ksq", "Enable Q Killsteal", true)
			ove_0_6.ezrealmenu.Killsteal:boolean("ksr", "Enable R Killsteal", true)
			ove_0_6.ezrealmenu:menu("Clear", "Clear")
			ove_0_6.ezrealmenu.Clear:header("Lane Clear", "Lane Clear")
			ove_0_6.ezrealmenu.Clear:boolean("LcQ", "Enable Q in Lane Clear", true)
			ove_0_6.ezrealmenu.Clear:header("Jungle Clear", "Jungle Clear")
			ove_0_6.ezrealmenu.Clear:boolean("JcQ", "Enable Q in Jungle Clear", true)
			ove_0_6.ezrealmenu:menu("Misc", "Misc")
			ove_0_6.ezrealmenu.Misc:boolean("weaving", "Enable Spell weaving", true)
			ove_0_6.ezrealmenu:menu("Draws", "Draws")
			ove_0_6.ezrealmenu.Draws:dropdown("circlemode", "Circle Mode", 1, {
				"Default",
				"Rainbow"
			})
			ove_0_6.ezrealmenu.Draws:boolean("drawq", "Draw Q Range", true)
			ove_0_6.ezrealmenu.Draws:boolean("drawe", "Draw E Range", true)
			ove_0_6.ezrealmenu.Draws:boolean("drawr", "Draw R Range", true)
			ove_0_6.ezrealmenu.Draws:boolean("drawAA", "Draw AA Tracker", true)
			ove_0_6.ezrealmenu:menu("Keys", "Keys")
			ove_0_6.ezrealmenu.Keys:keybind("stogglebar", "Combo Key", "Space", nil)
			ove_0_6.ezrealmenu.Keys:keybind("clearkey", "Clear Key", "V", nil)
			ove_0_6.ezrealmenu.Keys.clearkey:permashow(true)
			ove_0_6.ezrealmenu.Keys:keybind("farmtoggle", "Enable Spell Farming", nil, "A")
			ove_0_6.ezrealmenu.Keys:keybind("semir", "Semi R", "T", nil)
		end

		if ove_0_39 == 2 then
			ove_0_6.ezrealmenu:menu("Combo", "Combo")
			ove_0_6.ezrealmenu.Combo:menu("qsettings", "Mystic Shot - [Q]")
			ove_0_6.ezrealmenu.Combo.qsettings:set("icon", player:spellSlot(0).icon)
			ove_0_6.ezrealmenu.Combo.qsettings:boolean("useq", "在连招中启用 Q", true)
			ove_0_6.ezrealmenu.Combo.qsettings:boolean("qslow", "启用 Q 缓慢预判", false)
			ove_0_6.ezrealmenu.Combo.qsettings:boolean("autoQ", "自动 Q 瞄准冲刺目标", true)
			ove_0_6.ezrealmenu.Combo:menu("wsettings", "Essence Flux - [W]")
			ove_0_6.ezrealmenu.Combo.wsettings:set("icon", player:spellSlot(1).icon)
			ove_0_6.ezrealmenu.Combo.wsettings:boolean("usew", "在连招中启用 W", true)
			ove_0_6.ezrealmenu.Combo.wsettings:boolean("slowpredw", "启用 W 缓慢预判", false)
			ove_0_6.ezrealmenu.Combo.wsettings:boolean("waitforq", "等待 Q", true)
			ove_0_6.ezrealmenu.Combo.wsettings:boolean("afteraaw", "普攻后使用 W", false)
			ove_0_6.ezrealmenu.Combo:menu("rsettings", "Trueshot Barrage - [R]")
			ove_0_6.ezrealmenu.Combo.rsettings:set("icon", player:spellSlot(3).icon)
			ove_0_6.ezrealmenu.Combo.rsettings:boolean("autor", "在受控目标上自动释放 R", false)
			ove_0_6.ezrealmenu.Combo.rsettings:slider("autorHp", "生命值百分比自动释放 R 目标", 50, 1, 100, 1)
			ove_0_6.ezrealmenu.Combo.rsettings:slider("rrange", "R 范围", 1000, 500, 4000, 1)
			ove_0_6.ezrealmenu:menu("Killsteal", "Killsteal")
			ove_0_6.ezrealmenu.Killsteal:boolean("ksq", "启用 Q 抢人头", true)
			ove_0_6.ezrealmenu.Killsteal:boolean("ksr", "启用 R 抢人头", true)
			ove_0_6.ezrealmenu:menu("Clear", "Clear")
			ove_0_6.ezrealmenu.Clear:header("Lane Clear", "Lane Clear")
			ove_0_6.ezrealmenu.Clear:boolean("LcQ", "在清线时启用 Q", true)
			ove_0_6.ezrealmenu.Clear:header("Jungle Clear", "Jungle Clear")
			ove_0_6.ezrealmenu.Clear:boolean("JcQ", "在清野时启用 Q", true)
			ove_0_6.ezrealmenu:menu("Misc", "Misc")
			ove_0_6.ezrealmenu.Misc:boolean("weaving", "启用法术编织", true)
			ove_0_6.ezrealmenu:menu("Draws", "Draws")
			ove_0_6.ezrealmenu.Draws:dropdown("circlemode", "圆形模式", 1, {
				"默认",
				"彩虹 "
			})
			ove_0_6.ezrealmenu.Draws:boolean("drawq", "绘制 Q 范围", true)
			ove_0_6.ezrealmenu.Draws:boolean("drawe", "绘制 E 范围", true)
			ove_0_6.ezrealmenu.Draws:boolean("drawr", "绘制 R 范围", true)
			ove_0_6.ezrealmenu.Draws:boolean("drawAA", "绘制普攻追踪器", true)
			ove_0_6.ezrealmenu:menu("Keys", "Keys")
			ove_0_6.ezrealmenu.Keys:keybind("stogglebar", "组合键", "Space", nil)
			ove_0_6.ezrealmenu.Keys:keybind("clearkey", "清除键", "V", nil)
			ove_0_6.ezrealmenu.Keys.clearkey:permashow(true)
			ove_0_6.ezrealmenu.Keys:keybind("farmtoggle", "启用法术打怪", nil, "A")
			ove_0_6.ezrealmenu.Keys:keybind("semir", "半自动释放 R", "T", nil)
		end
	end

	if player.charName == "Hwei" then
		ove_0_6.hweimenu = menu("Fatality " .. player.charName, "[Fatality] Hwei")

		ove_0_6.hweimenu:set("icon", player.iconSquare)
		ove_0_6.hweimenu:dropdown("language", "Language", 1, {
			"English",
			"CN"
		})
		ove_0_6.hweimenu.language:set("tooltip", "Please Reload | 请重新加载")

		if ove_0_6.hweimenu.language:get() == 1 then
			ove_0_6.hweimenu:menu("Combo", "Combo")
			ove_0_6.hweimenu.Combo:menu("qcolor", "Subject: Disaster - [Q]")
			ove_0_6.hweimenu.Combo.qcolor:set("icon", player:spellSlot(0).icon)
			ove_0_6.hweimenu.Combo.qcolor:dropdown("qmode", "Q Mode", 1, {
				"Smart",
				"Force QQ",
				"ForceQW",
				"Force QE"
			})
			ove_0_6.hweimenu.Combo.qcolor:menu("qq", "Devastating Fire - [QQ]")
			ove_0_6.hweimenu.Combo.qcolor.qq:set("icon", ove_0_20)
			ove_0_6.hweimenu.Combo.qcolor.qq:boolean("useqq", "Enable QQ in Combo", true)
			ove_0_6.hweimenu.Combo.qcolor:menu("qw", "Severing Bolt- [QW]")
			ove_0_6.hweimenu.Combo.qcolor.qw:set("icon", ove_0_21)
			ove_0_6.hweimenu.Combo.qcolor.qw:boolean("useqw", "Enable QW in Combo", true)
			ove_0_6.hweimenu.Combo.qcolor.qw:boolean("onrtarger", "Auto QW on R Target if target is out of QQ Range", true)
			ove_0_6.hweimenu.Combo.qcolor.qw:slider("health", "Target HP % to QW", 30, 1, 100, 1)
			ove_0_6.hweimenu.Combo.qcolor:menu("qe", "Molten Fissure- [QE]")
			ove_0_6.hweimenu.Combo.qcolor.qe:set("icon", ove_0_22)
			ove_0_6.hweimenu.Combo.qcolor.qe:boolean("useqe", "Enable QE in Combo", true)
			ove_0_6.hweimenu.Combo.qcolor.qe:slider("useqe", "Only QE if Can hit more then x Target", 3, 1, 5, 1)
			ove_0_6.hweimenu.Combo:menu("wcolor", "Subject: Serenity - [W]")
			ove_0_6.hweimenu.Combo.wcolor:set("icon", player:spellSlot(1).icon)
			ove_0_6.hweimenu.Combo.wcolor:menu("we", "Stirring Lights - [EE]")
			ove_0_6.hweimenu.Combo.wcolor.we:set("icon", ove_0_23)
			ove_0_6.hweimenu.Combo.wcolor.we:boolean("usewe", "Enable WE in Combo", true)
			ove_0_6.hweimenu.Combo.wcolor.we:slider("mana", "Your Mana % to use WE", 50, 1, 100, 1)
			ove_0_6.hweimenu.Combo:menu("ecolor", "Subject: Torment - [E]")
			ove_0_6.hweimenu.Combo.ecolor:set("icon", player:spellSlot(2).icon)
			ove_0_6.hweimenu.Combo.ecolor:dropdown("emode", "E Mode", 1, {
				"Smart",
				"Force EQ",
				"ForceEW",
				"Force EE"
			})
			ove_0_6.hweimenu.Combo.ecolor:menu("eq", "Grim Visage - [EQ]")
			ove_0_6.hweimenu.Combo.ecolor.eq:set("icon", ove_0_24)
			ove_0_6.hweimenu.Combo.ecolor.eq:boolean("useeq", "Enable EQ in Combo", true)
			ove_0_6.hweimenu.Combo.ecolor.eq:boolean("slowpredeq", "Enable EQ Slow Prediction", true)
			ove_0_6.hweimenu.Combo.ecolor:menu("ew", "Gaze of the Abyss - [EW]")
			ove_0_6.hweimenu.Combo.ecolor.ew:set("icon", ove_0_25)
			ove_0_6.hweimenu.Combo.ecolor.ew:boolean("useew", "Enable EW in Combo", true)
			ove_0_6.hweimenu.Combo.ecolor:menu("ee", "Crushing Maw - [EE]")
			ove_0_6.hweimenu.Combo.ecolor.ee:set("icon", ove_0_26)
			ove_0_6.hweimenu.Combo.ecolor.ee:boolean("useee", "Enable EE in Combo", true)
			ove_0_6.hweimenu.Combo:menu("rsettings", "Spiraling Despair - [R]")
			ove_0_6.hweimenu.Combo.rsettings:set("icon", player:spellSlot(3).icon)
			ove_0_6.hweimenu.Combo.rsettings:boolean("user", "Enable R in Combo", true)
		end
	end

	if player.charName == "Kaisa" then
		ove_0_6.kaisamenu = menu("Fatality " .. player.charName, "[Fatality] Kaisa")

		ove_0_6.kaisamenu:set("icon", player.iconSquare)
		ove_0_6.kaisamenu:dropdown("language", "Language", 1, {
			"English",
			"CN"
		})
		ove_0_6.kaisamenu.language:set("tooltip", "Please Reload | 请重新加载")

		local ove_0_40 = ove_0_6.kaisamenu.language:get()

		if ove_0_40 == 1 then
			ove_0_6.kaisamenu:menu("Combo", "Combo")
			ove_0_6.kaisamenu.Combo:menu("qsettings", "Icathian Rain - [Q]")
			ove_0_6.kaisamenu.Combo.qsettings:set("icon", player:spellSlot(0).icon)
			ove_0_6.kaisamenu.Combo.qsettings:boolean("useq", "Enable Q in Combo", true)
			ove_0_6.kaisamenu.Combo.qsettings:dropdown("qmode", "Q Mode", 2, {
				"Always",
				"Isolated",
				"Never"
			})
			ove_0_6.kaisamenu.Combo:menu("wsettings", "Void Seeker - [W]")
			ove_0_6.kaisamenu.Combo.wsettings:set("icon", player:spellSlot(1).icon)
			ove_0_6.kaisamenu.Combo.wsettings:boolean("usew", "Enable W in Combo", true)
			ove_0_6.kaisamenu.Combo.wsettings:boolean("slowpredw", "Enable W Slow Prediction", false)
			ove_0_6.kaisamenu.Combo.wsettings:boolean("noaarange", "Dont W if Target is in AA Range", true)
			ove_0_6.kaisamenu.Combo:menu("esettings", "Supercharge - [E]")
			ove_0_6.kaisamenu.Combo.esettings:set("icon", player:spellSlot(2).icon)
			ove_0_6.kaisamenu.Combo.esettings:boolean("usee", "Enable E in Combo", true)
			ove_0_6.kaisamenu.Combo.esettings:dropdown("emode", "E Mode", 2, {
				"Always",
				"Evolved E",
				"Never"
			})
			ove_0_6.kaisamenu.Combo.esettings:dropdown("estate", "Use E", 2, {
				"After Q",
				"After AA"
			})
			ove_0_6.kaisamenu.Combo:menu("rsettings", "Killer Instinct - [R]")
			ove_0_6.kaisamenu.Combo.rsettings:set("icon", player:spellSlot(3).icon)
			ove_0_6.kaisamenu.Combo.rsettings:boolean("user", "Auto R Save", true)
			ove_0_6.kaisamenu.Combo.rsettings:boolean("notower", "Dont R if Target is Under Turret", true)
			ove_0_6.kaisamenu:menu("Killsteal", "Killsteal")
			ove_0_6.kaisamenu.Killsteal:boolean("ksQ", "Enable Q Killsteal", true)
			ove_0_6.kaisamenu.Killsteal:boolean("ksW", "Enable W Killsteal", true)
			ove_0_6.kaisamenu:menu("Clear", "Clear")
			ove_0_6.kaisamenu.Clear:header("laneclear", "Lane Clear")
			ove_0_6.kaisamenu.Clear:boolean("LcQ", "Enable Q Lane Clear", true)
			ove_0_6.kaisamenu.Clear:header("jungleclear", "Jungle Clear")
			ove_0_6.kaisamenu.Clear:boolean("JcQ", "Enable Q Jungle Clear", true)
			ove_0_6.kaisamenu.Clear:boolean("JcW", "Enable W Jungle Clear", true)
			ove_0_6.kaisamenu:menu("Draws", "Draws")
			ove_0_6.kaisamenu.Draws:dropdown("circlemode", "Circle Mode", 1, {
				"Default",
				"Rainbow"
			})
			ove_0_6.kaisamenu.Draws:boolean("drawQ", "Draw Q Range", true)
			ove_0_6.kaisamenu.Draws:boolean("drawW", "Draw W Range", true)
			ove_0_6.kaisamenu.Draws:boolean("drawR", "Draw R Range", true)
			ove_0_6.kaisamenu.Draws:boolean("drawAA", "Draw AA Tracker", true)
			ove_0_6.kaisamenu:menu("Keys", "Keys")
			ove_0_6.kaisamenu.Keys:keybind("stogglebar", "Combo Key", "Space", nil)
			ove_0_6.kaisamenu.Keys:keybind("clearkey", "Clear Key", "V", nil)
			ove_0_6.kaisamenu.Keys.clearkey:permashow(true)
			ove_0_6.kaisamenu.Keys:keybind("farmtoggle", "Enable Spell Farming", nil, "A")
			ove_0_6.kaisamenu.Keys:keybind("semiw", "Semi W", "T", nil)
		end

		if ove_0_40 == 2 then
			ove_0_6.kaisamenu:menu("Combo", "Combo")
			ove_0_6.kaisamenu.Combo:menu("qsettings", "Icathian Rain - [Q]")
			ove_0_6.kaisamenu.Combo.qsettings:set("icon", player:spellSlot(0).icon)
			ove_0_6.kaisamenu.Combo.qsettings:boolean("useq", "在连招中启用 Q", true)
			ove_0_6.kaisamenu.Combo.qsettings:dropdown("qmode", "Q 模式", 2, {
				"总是",
				"孤立的",
				"从不"
			})
			ove_0_6.kaisamenu.Combo:menu("wsettings", "Void Seeker - [W]")
			ove_0_6.kaisamenu.Combo.wsettings:set("icon", player:spellSlot(1).icon)
			ove_0_6.kaisamenu.Combo.wsettings:boolean("usew", "在连招中启用 W", true)
			ove_0_6.kaisamenu.Combo.wsettings:boolean("slowpredw", "启用 W 缓慢预判", false)
			ove_0_6.kaisamenu.Combo.wsettings:boolean("noaarange", "如果目标在普攻范围内，请不要使用 W", true)
			ove_0_6.kaisamenu.Combo:menu("esettings", "Supercharge - [E]")
			ove_0_6.kaisamenu.Combo.esettings:set("icon", player:spellSlot(2).icon)
			ove_0_6.kaisamenu.Combo.esettings:boolean("usee", "在连招中启用 E", true)
			ove_0_6.kaisamenu.Combo.esettings:dropdown("emode", "E 模式", 2, {
				"总是",
				"进化的 E",
				"从不"
			})
			ove_0_6.kaisamenu.Combo.esettings:dropdown("estate", "使用 E", 2, {
				"在 Q 之后",
				"普攻后"
			})
			ove_0_6.kaisamenu.Combo:menu("rsettings", "Killer Instinct - [R]")
			ove_0_6.kaisamenu.Combo.rsettings:set("icon", player:spellSlot(3).icon)
			ove_0_6.kaisamenu.Combo.rsettings:boolean("user", "自动释放 R 保命", true)
			ove_0_6.kaisamenu.Combo.rsettings:boolean("notower", "如果目标在防御塔下，请不要释放 R", true)
			ove_0_6.kaisamenu:menu("Killsteal", "Killsteal")
			ove_0_6.kaisamenu.Killsteal:boolean("ksQ", "启用 Q 抢人头", true)
			ove_0_6.kaisamenu.Killsteal:boolean("ksW", "启用 W 抢人头", true)
			ove_0_6.kaisamenu:menu("Clear", "Clear")
			ove_0_6.kaisamenu.Clear:header("laneclear", "Lane Clear")
			ove_0_6.kaisamenu.Clear:boolean("LcQ", "启用 Q 清线", true)
			ove_0_6.kaisamenu.Clear:header("jungleclear", "Jungle Clear")
			ove_0_6.kaisamenu.Clear:boolean("JcQ", "启用 Q 清野", true)
			ove_0_6.kaisamenu.Clear:boolean("JcW", "启用 W 清野", true)
			ove_0_6.kaisamenu:menu("Draws", "Draws")
			ove_0_6.kaisamenu.Draws:dropdown("circlemode", "圆形模式", 1, {
				"默认",
				"彩虹 "
			})
			ove_0_6.kaisamenu.Draws:boolean("drawQ", "绘制 Q 范围", true)
			ove_0_6.kaisamenu.Draws:boolean("drawW", "绘制 W 范围", true)
			ove_0_6.kaisamenu.Draws:boolean("drawR", "绘制 R 范围", true)
			ove_0_6.kaisamenu.Draws:boolean("drawAA", "绘制普攻追踪器", true)
			ove_0_6.kaisamenu:menu("Keys", "Keys")
			ove_0_6.kaisamenu.Keys:keybind("stogglebar", "组合键", "Space", nil)
			ove_0_6.kaisamenu.Keys:keybind("clearkey", "清理按键", "V", nil)
			ove_0_6.kaisamenu.Keys.clearkey:permashow(true)
			ove_0_6.kaisamenu.Keys:keybind("farmtoggle", "启用法术打怪", nil, "A")
			ove_0_6.kaisamenu.Keys:keybind("semiw", "半自动释放 W", "T", nil)
		end
	end

	if player.charName == "Kalista" then
		ove_0_6.kalistamenu = menu("Fatality " .. player.charName, "[Fatality] Kalista")

		ove_0_6.kalistamenu:set("icon", player.iconSquare)
		ove_0_6.kalistamenu:dropdown("language", "Language", 1, {
			"English",
			"CN"
		})
		ove_0_6.kalistamenu.language:set("tooltip", "Please Reload | 请重新加载")

		local ove_0_41 = ove_0_6.kalistamenu.language:get()

		if ove_0_41 == 1 then
			ove_0_6.kalistamenu:menu("Combo", "Combo")
			ove_0_6.kalistamenu.Combo:menu("qsettings", "Pierce - [Q]")
			ove_0_6.kalistamenu.Combo.qsettings:set("icon", player:spellSlot(0).icon)
			ove_0_6.kalistamenu.Combo.qsettings:boolean("useq", "Enable Q in Combo", true)
			ove_0_6.kalistamenu.Combo.qsettings:boolean("slowpredq", "Enable Q Slow Prediction", false)
			ove_0_6.kalistamenu.Combo:menu("esettings", "Rend - [E]")
			ove_0_6.kalistamenu.Combo.esettings:set("icon", player:spellSlot(2).icon)
			ove_0_6.kalistamenu.Combo.esettings:boolean("usee", "Enable E in Combo", true)
			ove_0_6.kalistamenu.Combo.esettings:boolean("ekillable", "Only E if Target is Killable", true)
			ove_0_6.kalistamenu.Combo.esettings:boolean("eleaving", "E if Target is Leaving E Range", false)
			ove_0_6.kalistamenu.Combo.esettings:slider("estacks", "E if Stacks are Higher then > ", 99, 1, 99, 1)
			ove_0_6.kalistamenu.Combo:menu("rsettings", "Fate's Call - [R]")
			ove_0_6.kalistamenu.Combo.rsettings:set("icon", player:spellSlot(3).icon)
			ove_0_6.kalistamenu.Combo.rsettings:boolean("SaveAllie", "Auto R to Save Allie", true)
			ove_0_6.kalistamenu.Combo.rsettings:boolean("ballista", "Enable Ballista Combo", true)
			ove_0_6.kalistamenu.Combo.rsettings.ballista:set("tooltip", "Experimental!")
			ove_0_6.kalistamenu.Combo.rsettings:slider("ballistarange", "Min Range to Ally To Ballista", 150, 0, 600, 1)
			ove_0_6.kalistamenu:menu("Killsteal", "Killsteal")
			ove_0_6.kalistamenu.Killsteal:boolean("ksQ", "Enable Q Killsteal", true)
			ove_0_6.kalistamenu.Killsteal:boolean("ksE", "Enable E Killsteal", true)
			ove_0_6.kalistamenu:menu("Clear", "Clear")
			ove_0_6.kalistamenu.Clear:header("Lane Clear", "Lane Clear")
			ove_0_6.kalistamenu.Clear:boolean("LcQ", "Enable Q in Lane Clear", true)
			ove_0_6.kalistamenu.Clear:header("Jungle Clear", "Jungle Clear")
			ove_0_6.kalistamenu.Clear:boolean("JcQ", "Enable Q in Jungle Clear", true)
			ove_0_6.kalistamenu.Clear:boolean("JcE", "Enable E in Jungle Clear", true)
			ove_0_6.kalistamenu:menu("Draws", "Draws")
			ove_0_6.kalistamenu.Draws:dropdown("circlemode", "Circle Mode", 1, {
				"Default",
				"Rainbow"
			})
			ove_0_6.kalistamenu.Draws:boolean("drawQ", "Draw Q Range", true)
			ove_0_6.kalistamenu.Draws:boolean("drawE", "Draw E Range", true)
			ove_0_6.kalistamenu.Draws:boolean("drawED", "Draw E Damage", true)
			ove_0_6.kalistamenu.Draws:boolean("drawAA", "Draw AA Tracker", true)
			ove_0_6.kalistamenu:menu("Keys", "Keys")
			ove_0_6.kalistamenu.Keys:keybind("stogglebar", "Combo Key", "Space", nil)
			ove_0_6.kalistamenu.Keys:keybind("clearkey", "Clear Key", "V", nil)
			ove_0_6.kalistamenu.Keys.clearkey:permashow(true)
			ove_0_6.kalistamenu.Keys:keybind("farmtoggle", "Enable Spell Farming", nil, "A")
		end

		if ove_0_41 == 2 then
			ove_0_6.kalistamenu:menu("Combo", "Combo")
			ove_0_6.kalistamenu.Combo:menu("qsettings", "Pierce - [Q]")
			ove_0_6.kalistamenu.Combo.qsettings:set("icon", player:spellSlot(0).icon)
			ove_0_6.kalistamenu.Combo.qsettings:boolean("useq", "在连招中启用 Q", true)
			ove_0_6.kalistamenu.Combo.qsettings:boolean("slowpredq", "启用 Q 缓慢预判", false)
			ove_0_6.kalistamenu.Combo:menu("esettings", "Rend - [E]")
			ove_0_6.kalistamenu.Combo.esettings:set("icon", player:spellSlot(2).icon)
			ove_0_6.kalistamenu.Combo.esettings:boolean("usee", "在连招中启用 E", true)
			ove_0_6.kalistamenu.Combo.esettings:boolean("ekillable", "只有目标可以击杀时使用 E", true)
			ove_0_6.kalistamenu.Combo.esettings:boolean("eleaving", "如果目标即将离开 E 范围时使用 E", false)
			ove_0_6.kalistamenu.Combo.esettings:slider("estacks", "如果叠加层数大于某个值时使用 E ", 99, 1, 99, 1)
			ove_0_6.kalistamenu.Combo:menu("rsettings", "Fate's Call - [R]")
			ove_0_6.kalistamenu.Combo.rsettings:set("icon", player:spellSlot(3).icon)
			ove_0_6.kalistamenu.Combo.rsettings:boolean("SaveAllie", "自动释放 R 以拯救盟友", true)
			ove_0_6.kalistamenu.Combo.rsettings:boolean("ballista", "启用弩车连招", true)
			ove_0_6.kalistamenu.Combo.rsettings.ballista:set("tooltip", "实验中")
			ove_0_6.kalistamenu.Combo.rsettings:slider("ballistarange", "使盟友到弩车的最小距离", 150, 0, 600, 1)
			ove_0_6.kalistamenu:menu("Killsteal", "Killsteal")
			ove_0_6.kalistamenu.Killsteal:boolean("ksQ", "启用 Q 抢人头", true)
			ove_0_6.kalistamenu.Killsteal:boolean("ksE", "启用 E 抢人头", true)
			ove_0_6.kalistamenu:menu("Clear", "Clear")
			ove_0_6.kalistamenu.Clear:header("Lane Clear", "Lane Clear")
			ove_0_6.kalistamenu.Clear:boolean("LcQ", "在清线时启用 Q", true)
			ove_0_6.kalistamenu.Clear:header("Jungle Clear", "Jungle Clear")
			ove_0_6.kalistamenu.Clear:boolean("JcQ", "在清野时启用 Q", true)
			ove_0_6.kalistamenu.Clear:boolean("JcE", "在清野时启用 E", true)
			ove_0_6.kalistamenu:menu("Draws", "Draws")
			ove_0_6.kalistamenu.Draws:dropdown("circlemode", "圆形模式", 1, {
				"默认",
				"彩虹 "
			})
			ove_0_6.kalistamenu.Draws:boolean("drawQ", "绘制 Q 范围", true)
			ove_0_6.kalistamenu.Draws:boolean("drawE", "绘制 E 范围", true)
			ove_0_6.kalistamenu.Draws:boolean("drawED", "绘制 E 伤害", true)
			ove_0_6.kalistamenu.Draws:boolean("drawAA", "绘制普攻追踪器", true)
			ove_0_6.kalistamenu:menu("Keys", "Keys")
			ove_0_6.kalistamenu.Keys:keybind("stogglebar", "组合键", "Space", nil)
			ove_0_6.kalistamenu.Keys:keybind("clearkey", "清理键", "V", nil)
			ove_0_6.kalistamenu.Keys.clearkey:permashow(true)
			ove_0_6.kalistamenu.Keys:keybind("farmtoggle", "启用法术收割", nil, "A")
		end
	end

	if player.charName == "Khazix" then
		ove_0_6.khazixmenu = menu("Fatality " .. player.charName, "[Fatality] Khazix")

		ove_0_6.khazixmenu:set("icon", player.iconSquare)
		ove_0_6.khazixmenu:dropdown("language", "Language", 1, {
			"English",
			"CN"
		})
		ove_0_6.khazixmenu.language:set("tooltip", "Please Reload | 请重新加载")

		local ove_0_42 = ove_0_6.khazixmenu.language:get()

		if ove_0_42 == 1 then
			ove_0_6.khazixmenu:menu("Combo", "Combo")
			ove_0_6.khazixmenu.Combo:menu("qsettings", "Taste Their Fear - [Q]")
			ove_0_6.khazixmenu.Combo.qsettings:set("icon", player:spellSlot(0).icon)
			ove_0_6.khazixmenu.Combo.qsettings:boolean("useq", "Enable Q in Combo", true)
			ove_0_6.khazixmenu.Combo:menu("wsettings", "Void Spike - [W]")
			ove_0_6.khazixmenu.Combo.wsettings:set("icon", player:spellSlot(1).icon)
			ove_0_6.khazixmenu.Combo.wsettings:boolean("usew", "Enable W in Combo", true)
			ove_0_6.khazixmenu.Combo.wsettings:boolean("slowpredw", "Enable W Slow Prediction", false)
			ove_0_6.khazixmenu.Combo:menu("esettings", "Leap - [E]")
			ove_0_6.khazixmenu.Combo.esettings:set("icon", player:spellSlot(2).icon)
			ove_0_6.khazixmenu.Combo.esettings:boolean("usee", "Enable E in Combo", true)
			ove_0_6.khazixmenu.Combo.esettings:boolean("slowprede", "Enable E Slow Prediction", false)
			ove_0_6.khazixmenu.Combo.esettings:boolean("noqrange", "Dont E if Target is in Q Range", true)
			ove_0_6.khazixmenu.Combo.esettings:boolean("onlywithq", "Dont E if Q is not Ready", false)
			ove_0_6.khazixmenu.Combo.esettings:slider("ehp", "Min HP % to use E", 50, 1, 100, 1)
			ove_0_6.khazixmenu.Combo.esettings:slider("maxtargets", "Max Targets To E", 2, 1, 5, 1)
			ove_0_6.khazixmenu.Combo.esettings:slider("targetscanrange", "Target Scan Range", 1000, 0, 2000, 1)
			ove_0_6.khazixmenu:menu("Killsteal", "Killsteal")
			ove_0_6.khazixmenu.Killsteal:boolean("ksQ", "Enable Q Killsteal", true)
			ove_0_6.khazixmenu.Killsteal:boolean("ksW", "Enable W Killsteal", true)
			ove_0_6.khazixmenu.Killsteal:boolean("ksE", "Enable E Killsteal", false)
			ove_0_6.khazixmenu:menu("Clear", "Clear")
			ove_0_6.khazixmenu.Clear:header("Lane Clear", "Lane Clear")
			ove_0_6.khazixmenu.Clear:boolean("LhQ", "Enable Q Last Hit", true)
			ove_0_6.khazixmenu.Clear:boolean("LcW", "Enable W in Lane Clear", true)
			ove_0_6.khazixmenu.Clear:header("Jungle Clear", "Jungle Clear")
			ove_0_6.khazixmenu.Clear:boolean("JcQ", "Enable Q in Jungle Clear", true)
			ove_0_6.khazixmenu.Clear:boolean("JcW", "Enable W in Jungle Clear", true)
			ove_0_6.khazixmenu:menu("Draws", "Draws")
			ove_0_6.khazixmenu.Draws:dropdown("circlemode", "Circle Mode", 1, {
				"Default",
				"Rainbow"
			})
			ove_0_6.khazixmenu.Draws:boolean("drawQ", "Draw Q Range", true)
			ove_0_6.khazixmenu.Draws:boolean("drawW", "Draw W Range", true)
			ove_0_6.khazixmenu.Draws:boolean("drawE", "Draw E Range", true)
			ove_0_6.khazixmenu.Draws:boolean("drawAA", "Draw AA Tracker", true)
			ove_0_6.khazixmenu:menu("Keys", "Keys")
			ove_0_6.khazixmenu.Keys:keybind("stogglebar", "Combo Key", "Space", nil)
			ove_0_6.khazixmenu.Keys:keybind("clearkey", "Clear Key", "V", nil)
			ove_0_6.khazixmenu.Keys.clearkey:permashow(true)
			ove_0_6.khazixmenu.Keys:keybind("lasthitkey", "Lasthit Key", "X", nil)
			ove_0_6.khazixmenu.Keys:keybind("farmtoggle", "Enable Spell Farming", nil, "A")
			ove_0_6.khazixmenu.Keys:keybind("divetoggle", "Enable Tower Dive", "T", nil)
		end

		if ove_0_42 == 2 then
			ove_0_6.khazixmenu:menu("Combo", "Combo")
			ove_0_6.khazixmenu.Combo:menu("qsettings", "Taste Their Fear - [Q]")
			ove_0_6.khazixmenu.Combo.qsettings:set("icon", player:spellSlot(0).icon)
			ove_0_6.khazixmenu.Combo.qsettings:boolean("useq", "在组合中启用 Q", true)
			ove_0_6.khazixmenu.Combo:menu("wsettings", "Void Spike - [W]")
			ove_0_6.khazixmenu.Combo.wsettings:set("icon", player:spellSlot(1).icon)
			ove_0_6.khazixmenu.Combo.wsettings:boolean("usew", "在组合中启用 W", true)
			ove_0_6.khazixmenu.Combo.wsettings:boolean("slowpredw", "启用 W 缓慢预判", false)
			ove_0_6.khazixmenu.Combo:menu("esettings", "Leap - [E]")
			ove_0_6.khazixmenu.Combo.esettings:set("icon", player:spellSlot(2).icon)
			ove_0_6.khazixmenu.Combo.esettings:boolean("usee", "在组合中启用 E", true)
			ove_0_6.khazixmenu.Combo.esettings:boolean("slowprede", "启用 E 缓慢预判", false)
			ove_0_6.khazixmenu.Combo.esettings:boolean("noqrange", "如果目标在 Q 范围内，请不要使用 E", true)
			ove_0_6.khazixmenu.Combo.esettings:boolean("onlywithq", "如果 Q 未准备好，请不要使用 E", false)
			ove_0_6.khazixmenu.Combo.esettings:slider("ehp", "使用 E 的最低生命值百分比", 50, 1, 100, 1)
			ove_0_6.khazixmenu.Combo.esettings:slider("maxtargets", "E 的最大目标数", 2, 1, 5, 1)
			ove_0_6.khazixmenu.Combo.esettings:slider("targetscanrange", "目标扫描范围", 1000, 0, 2000, 1)
			ove_0_6.khazixmenu:menu("Killsteal", "Killsteal")
			ove_0_6.khazixmenu.Killsteal:boolean("ksQ", "启用 Q 抢人头", true)
			ove_0_6.khazixmenu.Killsteal:boolean("ksW", "启用 W 抢人头", true)
			ove_0_6.khazixmenu.Killsteal:boolean("ksE", "启用 E 抢人头", false)
			ove_0_6.khazixmenu:menu("Clear", "Clear")
			ove_0_6.khazixmenu.Clear:header("Lane Clear", "Lane Clear")
			ove_0_6.khazixmenu.Clear:boolean("LhQ", "启用 Q 补刀", true)
			ove_0_6.khazixmenu.Clear:boolean("LcW", "在清线时启用 W", true)
			ove_0_6.khazixmenu.Clear:header("Jungle Clear", "Jungle Clear")
			ove_0_6.khazixmenu.Clear:boolean("JcQ", "在清野时启用 Q", true)
			ove_0_6.khazixmenu.Clear:boolean("JcW", "在清野时启用 W", true)
			ove_0_6.khazixmenu:menu("Draws", "Draws")
			ove_0_6.khazixmenu.Draws:dropdown("circlemode", "圆形模式", 1, {
				"默认",
				"彩虹 "
			})
			ove_0_6.khazixmenu.Draws:boolean("drawQ", "绘制 Q 射程", true)
			ove_0_6.khazixmenu.Draws:boolean("drawW", "绘制 W 射程", true)
			ove_0_6.khazixmenu.Draws:boolean("drawE", "绘制 E 射程", true)
			ove_0_6.khazixmenu.Draws:boolean("drawAA", "绘制普攻追踪器", true)
			ove_0_6.khazixmenu:menu("Keys", "Keys")
			ove_0_6.khazixmenu.Keys:keybind("stogglebar", "组合键", "Space", nil)
			ove_0_6.khazixmenu.Keys:keybind("clearkey", "清理键", "V", nil)
			ove_0_6.khazixmenu.Keys.clearkey:permashow(true)
			ove_0_6.khazixmenu.Keys:keybind("lasthitkey", "补刀按键", "X", nil)
			ove_0_6.khazixmenu.Keys:keybind("farmtoggle", "启用法术收割", nil, "A")
			ove_0_6.khazixmenu.Keys:keybind("divetoggle", "启用塔潜", "T", nil)
		end
	end

	if player.charName == "MasterYi" then
		ove_0_6.yimenu = menu("Fatality " .. player.charName, "[Fatality] Master Yi")

		ove_0_6.yimenu:set("icon", player.iconSquare)
		ove_0_6.yimenu:dropdown("language", "Language", 1, {
			"English",
			"CN"
		})
		ove_0_6.yimenu.language:set("tooltip", "Please Reload | 请重新加载")

		local ove_0_43 = ove_0_6.yimenu.language:get()

		if ove_0_43 == 1 then
			ove_0_6.yimenu:menu("Combo", "Combo")
			ove_0_6.yimenu.Combo:menu("qsettings", "Alpha Strike - [Q]")
			ove_0_6.yimenu.Combo.qsettings:set("icon", player:spellSlot(0).icon)
			ove_0_6.yimenu.Combo.qsettings:dropdown("qmode", "Q Mode", 2, {
				"Always",
				"Smart",
				"Never"
			})
			ove_0_6.yimenu.Combo.qsettings:boolean("dodgeturret", "Evade Turret Shots", true)
			ove_0_6.yimenu.Combo.qsettings:boolean("autodash", "Auto Follow Dashes", true)
			ove_0_6.yimenu.Combo.qsettings:boolean("autoflower", "Auto Follow on Explosion Plant", true)
			ove_0_6.yimenu.Combo.qsettings:boolean("autoflash", "Auto Follow Flash", true)
			ove_0_6.yimenu.Combo.qsettings:slider("autohp", "Target Max Health % To Auto Follow", 50, 1, 100, 1)
			ove_0_6.yimenu.Combo:menu("wsettings", "Meditate - [W]")
			ove_0_6.yimenu.Combo.wsettings:set("icon", player:spellSlot(1).icon)
			ove_0_6.yimenu.Combo.wsettings:boolean("WAA", "Enable W AA Reset", true)
			ove_0_6.yimenu.Combo.wsettings:boolean("Wsave", "Auto W if Incoming Damage Kills you", true)
			ove_0_6.yimenu.Combo.wsettings:slider("Wsavehealth", "Auto W if Incoming Damage is Higher then > ", 500, 0, 2000, 1)
			ove_0_6.yimenu.Combo:menu("esettings", "Wuju Style - [E]")
			ove_0_6.yimenu.Combo.esettings:set("icon", player:spellSlot(2).icon)
			ove_0_6.yimenu.Combo.esettings:boolean("usee", "Enable E in Combo", true)
			ove_0_6.yimenu.Combo.esettings:slider("health", "Target Max Health %", 50, 1, 100, 1)
			ove_0_6.yimenu:menu("Killsteal", "Killsteal")
			ove_0_6.yimenu.Killsteal:boolean("ksQ", "Enable Q Killsteal", true)
			ove_0_6.yimenu:menu("Clear", "Clear")
			ove_0_6.yimenu.Clear:header("Lane Clear", "Lane Clear")
			ove_0_6.yimenu.Clear:boolean("LcQ", "Enable Q in Lane Clear", true)
			ove_0_6.yimenu.Clear:header("Jungle Clear", "Jungle Clear")
			ove_0_6.yimenu.Clear:boolean("JcQ", "Enable Q in Jungle Clear", true)
			ove_0_6.yimenu.Clear:boolean("JcE", "Enable E in Jungle Clear", true)
			ove_0_6.yimenu:menu("Misc", "Misc")
			ove_0_6.yimenu.Misc:menu("qdodge", "Q Dodge")
			ove_0_6.yimenu.Misc.qdodge:boolean("enabledodge", "Enable Q Dodge", true)
			ove_0_6.yimenu.Misc.qdodge:menu("qskillshots", "Skill Shots [TODO]")
			ove_0_6.yimenu.Misc.qdodge:menu("qtarget", "Target Spells")

			local ove_0_44 = objManager.enemies
			local ove_0_45 = objManager.enemies_n

			for iter_0_4 = 0, ove_0_45 - 1 do
				local ove_0_46 = ove_0_44[iter_0_4]

				if ove_0_46.charName == "Alistar" then
					ove_0_6.yimenu.Misc.qdodge.qtarget:boolean("alistarW", "Dodge Alistar W", true)
					ove_0_6.yimenu.Misc.qdodge.qtarget.alistarW:set("icon", ove_0_46:spellSlot(1).icon)
				end

				if ove_0_46.charName == "Annie" then
					ove_0_6.yimenu.Misc.qdodge.qtarget:boolean("annieQ", "Dodge AnnieQ", true)
					ove_0_6.yimenu.Misc.qdodge.qtarget.annieQ:set("icon", ove_0_46:spellSlot(0).icon)
				end

				if ove_0_46.charName == "Blitzcrank" then
					ove_0_6.yimenu.Misc.qdodge.qtarget:boolean("blitzE", "Dodge Blitzcrank E", true)
					ove_0_6.yimenu.Misc.qdodge.qtarget.blitzE:set("icon", ove_0_46:spellSlot(2).icon)
				end

				if ove_0_46.charName == "Brand" then
					ove_0_6.yimenu.Misc.qdodge.qtarget:boolean("brandR", "Dodge Brand R", true)
					ove_0_6.yimenu.Misc.qdodge.qtarget.brandR:set("icon", ove_0_46:spellSlot(3).icon)
				end

				if ove_0_46.charName == "Briar" then
					ove_0_6.yimenu.Misc.qdodge.qtarget:boolean("briarQ", "Dodge Briar Q", true)
					ove_0_6.yimenu.Misc.qdodge.qtarget.briarQ:set("icon", ove_0_46:spellSlot(0).icon)
				end

				if ove_0_46.charName == "Caitlyn" then
					ove_0_6.yimenu.Misc.qdodge.qtarget:boolean("caitlynR", "Dodge Caitlyn R", true)
					ove_0_6.yimenu.Misc.qdodge.qtarget.caitlynR:set("icon", ove_0_46:spellSlot(3).icon)
				end

				if ove_0_46.charName == "Camille" then
					ove_0_6.yimenu.Misc.qdodge.qtarget:boolean("camilleQ2", "Dodge camille Q2", true)
					ove_0_6.yimenu.Misc.qdodge.qtarget.camilleQ2:set("icon", ove_0_46:spellSlot(0).icon)
					ove_0_6.yimenu.Misc.qdodge.qtarget:boolean("camilleR", "Dodge camille R", true)
					ove_0_6.yimenu.Misc.qdodge.qtarget.camilleR:set("icon", ove_0_46:spellSlot(3).icon)
				end

				if ove_0_46.charName == "Chogath" then
					ove_0_6.yimenu.Misc.qdodge.qtarget:boolean("chogathR", "Dodge Chogath R", true)
					ove_0_6.yimenu.Misc.qdodge.qtarget.chogathR:set("icon", ove_0_46:spellSlot(3).icon)
				end

				if ove_0_46.charName == "Darius" then
					ove_0_6.yimenu.Misc.qdodge.qtarget:boolean("dariusR", "Dodge Darius R", true)
					ove_0_6.yimenu.Misc.qdodge.qtarget.dariusR:set("icon", ove_0_46:spellSlot(3).icon)
				end

				if ove_0_46.charName == "Ekko" then
					ove_0_6.yimenu.Misc.qdodge.qtarget:boolean("ekkoE", "Dodge Ekko E", true)
					ove_0_6.yimenu.Misc.qdodge.qtarget.ekkoE:set("icon", ove_0_46:spellSlot(2).icon)
				end

				if ove_0_46.charName == "Fiddlesticks" then
					ove_0_6.yimenu.Misc.qdodge.qtarget:boolean("fiddlesticksQ", "Dodge Fiddlesticks Q", true)
					ove_0_6.yimenu.Misc.qdodge.qtarget.fiddlesticksQ:set("icon", ove_0_46:spellSlot(0).icon)
				end

				if ove_0_46.charName == "Garen" then
					ove_0_6.yimenu.Misc.qdodge.qtarget:boolean("garenR", "Dodge Garen R", true)
					ove_0_6.yimenu.Misc.qdodge.qtarget.garenR:set("icon", ove_0_46:spellSlot(3).icon)
				end

				if ove_0_46.charName == "Hecarim" then
					ove_0_6.yimenu.Misc.qdodge.qtarget:boolean("hecaE", "Dodge Hecarim E", true)
					ove_0_6.yimenu.Misc.qdodge.qtarget.hecaE:set("icon", ove_0_46:spellSlot(2).icon)
				end

				if ove_0_46.charName == "Leesin" then
					ove_0_6.yimenu.Misc.qdodge.qtarget:boolean("leeR", "Dodge Lee R", true)
					ove_0_6.yimenu.Misc.qdodge.qtarget.leeR:set("icon", ove_0_46:spellSlot(3).icon)
				end

				if ove_0_46.charName == "Leona" then
					ove_0_6.yimenu.Misc.qdodge.qtarget:boolean("leonaQ", "Dodge Leona Q", true)
					ove_0_6.yimenu.Misc.qdodge.qtarget.leonaQ:set("icon", ove_0_46:spellSlot(0).icon)
				end

				if ove_0_46.charName == "Lissandra" then
					ove_0_6.yimenu.Misc.qdodge.qtarget:boolean("lissR", "Dodge Lissandra R", true)
					ove_0_6.yimenu.Misc.qdodge.qtarget.lissR:set("icon", ove_0_46:spellSlot(3).icon)
				end

				if ove_0_46.charName == "Lulu" then
					ove_0_6.yimenu.Misc.qdodge.qtarget:boolean("luluW", "Dodge Lulu W", true)
					ove_0_6.yimenu.Misc.qdodge.qtarget.luluW:set("icon", ove_0_46:spellSlot(2).icon)
				end

				if ove_0_46.charName == "Malzahar" then
					ove_0_6.yimenu.Misc.qdodge.qtarget:boolean("malzaharR", "Dodge Malzahar R", true)
					ove_0_6.yimenu.Misc.qdodge.qtarget.malzaharR:set("icon", ove_0_46:spellSlot(3).icon)
				end

				if ove_0_46.charName == "Mordekaiser" then
					ove_0_6.yimenu.Misc.qdodge.qtarget:boolean("mordekaiserR", "Dodge Mordekaiser R", true)
					ove_0_6.yimenu.Misc.qdodge.qtarget.mordekaiserR:set("icon", ove_0_46:spellSlot(3).icon)
				end

				if ove_0_46.charName == "Nautilus" then
					ove_0_6.yimenu.Misc.qdodge.qtarget:boolean("nautpassive", "Dodge Naut Stun AA", true)
					ove_0_6.yimenu.Misc.qdodge.qtarget:boolean("nautR", "Dodge Nautilus R", true)
					ove_0_6.yimenu.Misc.qdodge.qtarget.nautR:set("icon", ove_0_46:spellSlot(3).icon)
				end

				if ove_0_46.charName == "Pantheon" then
					ove_0_6.yimenu.Misc.qdodge.qtarget:boolean("panthW", "Dodge Pantheon W", true)
					ove_0_6.yimenu.Misc.qdodge.qtarget.panthW:set("icon", ove_0_46:spellSlot(1).icon)
				end

				if ove_0_46.charName == "Quinn" then
					ove_0_6.yimenu.Misc.qdodge.qtarget:boolean("quinnE", "Dodge Quinn E", true)
					ove_0_6.yimenu.Misc.qdodge.qtarget.quinnE:set("icon", ove_0_46:spellSlot(2).icon)
				end

				if ove_0_46.charName == "Rammus" then
					ove_0_6.yimenu.Misc.qdodge.qtarget:boolean("rammusE", "Dodge Rammus E", true)
					ove_0_6.yimenu.Misc.qdodge.qtarget.rammusE:set("icon", ove_0_46:spellSlot(2).icon)
				end

				if ove_0_46.charName == "Renekton" then
					ove_0_6.yimenu.Misc.qdodge.qtarget:boolean("renektonW", "Dodge Renekton W", true)
					ove_0_6.yimenu.Misc.qdodge.qtarget.renektonW:set("icon", ove_0_46:spellSlot(1).icon)
				end

				if ove_0_46.charName == "Sett" then
					ove_0_6.yimenu.Misc.qdodge.qtarget:boolean("settR", "Dodge Sett R", true)
					ove_0_6.yimenu.Misc.qdodge.qtarget.settR:set("icon", ove_0_46:spellSlot(3).icon)
				end

				if ove_0_46.charName == "Singed" then
					ove_0_6.yimenu.Misc.qdodge.qtarget:boolean("singedE", "Dodge Singed E", true)
					ove_0_6.yimenu.Misc.qdodge.qtarget.singedE:set("icon", ove_0_46:spellSlot(2).icon)
				end

				if ove_0_46.charName == "Skarner" then
					ove_0_6.yimenu.Misc.qdodge.qtarget:boolean("skarnerR", "Dodge Skarner R (TODO)", true)
					ove_0_6.yimenu.Misc.qdodge.qtarget.skarnerR:set("icon", ove_0_46:spellSlot(3).icon)
				end

				if ove_0_46.charName == "Syndra" then
					ove_0_6.yimenu.Misc.qdodge.qtarget:boolean("syndraR", "Dodge Syndra R", true)
					ove_0_6.yimenu.Misc.qdodge.qtarget.syndraR:set("icon", ove_0_46:spellSlot(3).icon)
				end

				if ove_0_46.charName == "Tahmkench" or ove_0_46.charName == "TahmKench" then
					ove_0_6.yimenu.Misc.qdodge.qtarget:boolean("tahmR", "Dodge Tahm R", true)
					ove_0_6.yimenu.Misc.qdodge.qtarget.tahmR:set("icon", ove_0_46:spellSlot(3).icon)
				end

				if ove_0_46.charName == "Teemo" then
					ove_0_6.yimenu.Misc.qdodge.qtarget:boolean("teemoQ", "Dodge Teemo Q", true)
					ove_0_6.yimenu.Misc.qdodge.qtarget.teemoQ:set("icon", ove_0_46:spellSlot(0).icon)
				end

				if ove_0_46.charName == "Tristana" then
					ove_0_6.yimenu.Misc.qdodge.qtarget:boolean("tristE", "Dodge Tristana E", true)
					ove_0_6.yimenu.Misc.qdodge.qtarget.tristE:set("icon", ove_0_46:spellSlot(2).icon)
					ove_0_6.yimenu.Misc.qdodge.qtarget:boolean("tristR", "Dodge Tristana R", true)
					ove_0_6.yimenu.Misc.qdodge.qtarget.tristR:set("icon", ove_0_46:spellSlot(3).icon)
				end

				if ove_0_46.charName == "Twistedfate" then
					ove_0_6.yimenu.Misc.qdodge.qtarget:boolean("twistedW", "Dodge Twisted Fate Stun Card", true)
					ove_0_6.yimenu.Misc.qdodge.qtarget.twistedW:set("icon", ove_0_46:spellSlot(1).icon)
				end

				if ove_0_46.charName == "Udyr" then
					ove_0_6.yimenu.Misc.qdodge.qtarget:boolean("udyrE", "Dodge Udyr E", true)
					ove_0_6.yimenu.Misc.qdodge.qtarget.udyrE:set("icon", ove_0_46:spellSlot(2).icon)
				end

				if ove_0_46.charName == "Vayne" then
					ove_0_6.yimenu.Misc.qdodge.qtarget:boolean("vayneE", "Dodge Vayne E", true)
					ove_0_6.yimenu.Misc.qdodge.qtarget.vayneE:set("icon", ove_0_46:spellSlot(2).icon, true)
				end

				if ove_0_46.charName == "Veigar" then
					ove_0_6.yimenu.Misc.qdodge.qtarget:boolean("veigaR", "Dodge Veiga R", true)
					ove_0_6.yimenu.Misc.qdodge.qtarget.veigaR:set("icon", ove_0_46:spellSlot(3).icon, true)
				end

				if ove_0_46.charName == "Vi" then
					ove_0_6.yimenu.Misc.qdodge.qtarget:boolean("viR", "Dodge Vi R", true)
					ove_0_6.yimenu.Misc.qdodge.qtarget.viR:set("icon", ove_0_46:spellSlot(3).icon, true)
				end

				if ove_0_46.charName == "Volibear" then
					ove_0_6.yimenu.Misc.qdodge.qtarget:boolean("voliQ", "Dodge Volibear Q", true)
					ove_0_6.yimenu.Misc.qdodge.qtarget.voliQ:set("icon", ove_0_46:spellSlot(0).icon, true)
				end
			end

			ove_0_6.yimenu:menu("Draws", "Draws")
			ove_0_6.yimenu.Draws:dropdown("circlemode", "Circle Mode", 1, {
				"Default",
				"Rainbow"
			})
			ove_0_6.yimenu.Draws:boolean("drawQ", "Draw Q Range", true)
			ove_0_6.yimenu.Draws:boolean("drawbuff", "Draw Buff Time", true)
			ove_0_6.yimenu.Draws:boolean("drawAA", "Draw AA Tracker", true)
			ove_0_6.yimenu:menu("Keys", "Keys")
			ove_0_6.yimenu.Keys:keybind("stogglebar", "Combo Key", "Space", nil)
			ove_0_6.yimenu.Keys:keybind("clearkey", "Clear Key", "V", nil)
			ove_0_6.yimenu.Keys.clearkey:permashow(true)
			ove_0_6.yimenu.Keys:keybind("farmtoggle", "Enable Spell Farming", nil, "A")
			ove_0_6.yimenu.Keys:keybind("semiq", "Semi Q", "Q", nil)
			ove_0_6.yimenu.Keys:keybind("dive", "Enable Tower Dive", nil, "G")
		end

		if ove_0_43 == 2 then
			ove_0_6.yimenu:menu("Combo", "Combo")
			ove_0_6.yimenu.Combo:menu("qsettings", "Alpha Strike - [Q]")
			ove_0_6.yimenu.Combo.qsettings:set("icon", player:spellSlot(0).icon)
			ove_0_6.yimenu.Combo.qsettings:dropdown("qmode", "Q 模式", 2, {
				"始终",
				"智能",
				"从不"
			})
			ove_0_6.yimenu.Combo.qsettings:boolean("dodgeturret", "躲避防御塔射击", true)
			ove_0_6.yimenu.Combo.qsettings:boolean("autodash", "自动跟随冲刺技能", true)
			ove_0_6.yimenu.Combo.qsettings:boolean("autoflower", "自动跟随爆炸植物", true)
			ove_0_6.yimenu.Combo.qsettings:boolean("autoflash", "自动跟随闪现", true)
			ove_0_6.yimenu.Combo.qsettings:slider("autohp", "目标最大生命值百分比自动跟随", 50, 1, 100, 1)
			ove_0_6.yimenu.Combo:menu("wsettings", "Meditate - [W]")
			ove_0_6.yimenu.Combo.wsettings:set("icon", player:spellSlot(1).icon)
			ove_0_6.yimenu.Combo.wsettings:boolean("WAA", "启用 W AA 重置", true)
			ove_0_6.yimenu.Combo.wsettings:boolean("Wsave", "如果即将到来的伤害将使您死亡则自动使用 W", true)
			ove_0_6.yimenu.Combo.wsettings:slider("Wsavehealth", "如果即将到来的伤害高于 > 自动使用 W", 500, 0, 2000, 1)
			ove_0_6.yimenu.Combo:menu("esettings", "Wuju Style - [E]")
			ove_0_6.yimenu.Combo.esettings:set("icon", player:spellSlot(2).icon)
			ove_0_6.yimenu.Combo.esettings:boolean("usee", "启用连招中的 E", true)
			ove_0_6.yimenu.Combo.esettings:slider("health", "目标最大生命值百分比", 50, 1, 100, 1)
			ove_0_6.yimenu:menu("Killsteal", "Killsteal")
			ove_0_6.yimenu.Killsteal:boolean("ksQ", "启用 Q 抢人头", true)
			ove_0_6.yimenu:menu("Clear", "Clear")
			ove_0_6.yimenu.Clear:header("Lane Clear", "清线")
			ove_0_6.yimenu.Clear:boolean("LcQ", "启用 Q 清线", true)
			ove_0_6.yimenu.Clear:header("Jungle Clear", "清野")
			ove_0_6.yimenu.Clear:boolean("JcQ", "启用 Q 清野", true)
			ove_0_6.yimenu.Clear:boolean("JcE", "启用 E 清野", true)
			ove_0_6.yimenu:menu("Misc", "Misc")
			ove_0_6.yimenu.Misc:menu("qdodge", "Q 闪避")
			ove_0_6.yimenu.Misc.qdodge:boolean("enabledodge", "启用 Q 闪避", true)
			ove_0_6.yimenu.Misc.qdodge:menu("qskillshots", "技能弹道 [待办事项]")
			ove_0_6.yimenu.Misc.qdodge:menu("qtarget", "目标技能")

			local ove_0_47 = objManager.enemies
			local ove_0_48 = objManager.enemies_n

			for iter_0_5 = 0, ove_0_48 - 1 do
				local ove_0_49 = ove_0_47[iter_0_5]

				if ove_0_49.charName == "Alistar" then
					ove_0_6.yimenu.Misc.qdodge.qtarget:boolean("alistarW", "躲避牛头 W", true)
					ove_0_6.yimenu.Misc.qdodge.qtarget.alistarW:set("icon", ove_0_49:spellSlot(1).icon)
				end

				if ove_0_49.charName == "Annie" then
					ove_0_6.yimenu.Misc.qdodge.qtarget:boolean("annieQ", "躲避安妮 Q", true)
					ove_0_6.yimenu.Misc.qdodge.qtarget.annieQ:set("icon", ove_0_49:spellSlot(0).icon)
				end

				if ove_0_49.charName == "Blitzcrank" then
					ove_0_6.yimenu.Misc.qdodge.qtarget:boolean("blitzE", "躲避布里茨 E", true)
					ove_0_6.yimenu.Misc.qdodge.qtarget.blitzE:set("icon", ove_0_49:spellSlot(2).icon)
				end

				if ove_0_49.charName == "Brand" then
					ove_0_6.yimenu.Misc.qdodge.qtarget:boolean("brandR", "躲避布兰德 R", true)
					ove_0_6.yimenu.Misc.qdodge.qtarget.brandR:set("icon", ove_0_49:spellSlot(3).icon)
				end

				if ove_0_49.charName == "Briar" then
					ove_0_6.yimenu.Misc.qdodge.qtarget:boolean("briarQ", "躲避布莱尔 Q", true)
					ove_0_6.yimenu.Misc.qdodge.qtarget.briarQ:set("icon", ove_0_49:spellSlot(0).icon)
				end

				if ove_0_49.charName == "Caitlyn" then
					ove_0_6.yimenu.Misc.qdodge.qtarget:boolean("caitlynR", "躲避凯特琳 R", true)
					ove_0_6.yimenu.Misc.qdodge.qtarget.caitlynR:set("icon", ove_0_49:spellSlot(3).icon)
				end

				if ove_0_49.charName == "Camille" then
					ove_0_6.yimenu.Misc.qdodge.qtarget:boolean("camilleQ2", "躲避卡密尔 Q2", true)
					ove_0_6.yimenu.Misc.qdodge.qtarget.camilleQ2:set("icon", ove_0_49:spellSlot(0).icon)
					ove_0_6.yimenu.Misc.qdodge.qtarget:boolean("camilleR", "躲避卡密尔 R", true)
					ove_0_6.yimenu.Misc.qdodge.qtarget.camilleR:set("icon", ove_0_49:spellSlot(3).icon)
				end

				if ove_0_49.charName == "Chogath" then
					ove_0_6.yimenu.Misc.qdodge.qtarget:boolean("chogathR", "躲避科加斯 R", true)
					ove_0_6.yimenu.Misc.qdodge.qtarget.chogathR:set("icon", ove_0_49:spellSlot(3).icon)
				end

				if ove_0_49.charName == "Darius" then
					ove_0_6.yimenu.Misc.qdodge.qtarget:boolean("dariusR", "躲避德莱厄斯 R", true)
					ove_0_6.yimenu.Misc.qdodge.qtarget.dariusR:set("icon", ove_0_49:spellSlot(3).icon)
				end

				if ove_0_49.charName == "Ekko" then
					ove_0_6.yimenu.Misc.qdodge.qtarget:boolean("ekkoE", "躲避艾克 E", true)
					ove_0_6.yimenu.Misc.qdodge.qtarget.ekkoE:set("icon", ove_0_49:spellSlot(2).icon)
				end

				if ove_0_49.charName == "Fiddlesticks" then
					ove_0_6.yimenu.Misc.qdodge.qtarget:boolean("fiddlesticksQ", "躲避费德提克 Q", true)
					ove_0_6.yimenu.Misc.qdodge.qtarget.fiddlesticksQ:set("icon", ove_0_49:spellSlot(0).icon)
				end

				if ove_0_49.charName == "Garen" then
					ove_0_6.yimenu.Misc.qdodge.qtarget:boolean("garenR", "躲避盖伦 R", true)
					ove_0_6.yimenu.Misc.qdodge.qtarget.garenR:set("icon", ove_0_49:spellSlot(3).icon)
				end

				if ove_0_49.charName == "Hecarim" then
					ove_0_6.yimenu.Misc.qdodge.qtarget:boolean("hecaE", "躲避赫卡里姆 E", true)
					ove_0_6.yimenu.Misc.qdodge.qtarget.hecaE:set("icon", ove_0_49:spellSlot(2).icon)
				end

				if ove_0_49.charName == "Leesin" then
					ove_0_6.yimenu.Misc.qdodge.qtarget:boolean("leeR", "躲避李青 R", true)
					ove_0_6.yimenu.Misc.qdodge.qtarget.leeR:set("icon", ove_0_49:spellSlot(3).icon)
				end

				if ove_0_49.charName == "Leona" then
					ove_0_6.yimenu.Misc.qdodge.qtarget:boolean("leonaQ", "躲避蕾欧娜 Q", true)
					ove_0_6.yimenu.Misc.qdodge.qtarget.leonaQ:set("icon", ove_0_49:spellSlot(0).icon)
				end

				if ove_0_49.charName == "Lissandra" then
					ove_0_6.yimenu.Misc.qdodge.qtarget:boolean("lissR", "躲避丽桑卓 R", true)
					ove_0_6.yimenu.Misc.qdodge.qtarget.lissR:set("icon", ove_0_49:spellSlot(3).icon)
				end

				if ove_0_49.charName == "Lulu" then
					ove_0_6.yimenu.Misc.qdodge.qtarget:boolean("luluW", "躲避璐璐 W", true)
					ove_0_6.yimenu.Misc.qdodge.qtarget.luluW:set("icon", ove_0_49:spellSlot(2).icon)
				end

				if ove_0_49.charName == "Malzahar" then
					ove_0_6.yimenu.Misc.qdodge.qtarget:boolean("malzaharR", "躲避玛尔扎哈 R", true)
					ove_0_6.yimenu.Misc.qdodge.qtarget.malzaharR:set("icon", ove_0_49:spellSlot(3).icon)
				end

				if ove_0_49.charName == "Mordekaiser" then
					ove_0_6.yimenu.Misc.qdodge.qtarget:boolean("mordekaiserR", "躲避莫德凯撒 R", true)
					ove_0_6.yimenu.Misc.qdodge.qtarget.mordekaiserR:set("icon", ove_0_49:spellSlot(3).icon)
				end

				if ove_0_49.charName == "Nautilus" then
					ove_0_6.yimenu.Misc.qdodge.qtarget:boolean("nautpassive", "躲避纳尔撞晕普攻", true)
					ove_0_6.yimenu.Misc.qdodge.qtarget:boolean("nautR", "躲避纳尔 R", true)
					ove_0_6.yimenu.Misc.qdodge.qtarget.nautR:set("icon", ove_0_49:spellSlot(3).icon)
				end

				if ove_0_49.charName == "Pantheon" then
					ove_0_6.yimenu.Misc.qdodge.qtarget:boolean("panthW", "躲避潘森 W", true)
					ove_0_6.yimenu.Misc.qdodge.qtarget.panthW:set("icon", ove_0_49:spellSlot(1).icon)
				end

				if ove_0_49.charName == "Quinn" then
					ove_0_6.yimenu.Misc.qdodge.qtarget:boolean("quinnE", "躲避奎因 E", true)
					ove_0_6.yimenu.Misc.qdodge.qtarget.quinnE:set("icon", ove_0_49:spellSlot(2).icon)
				end

				if ove_0_49.charName == "Rammus" then
					ove_0_6.yimenu.Misc.qdodge.qtarget:boolean("rammusE", "躲避拉莫斯 E", true)
					ove_0_6.yimenu.Misc.qdodge.qtarget.rammusE:set("icon", ove_0_49:spellSlot(2).icon)
				end

				if ove_0_49.charName == "Renekton" then
					ove_0_6.yimenu.Misc.qdodge.qtarget:boolean("renektonW", "躲避雷克顿 W", true)
					ove_0_6.yimenu.Misc.qdodge.qtarget.renektonW:set("icon", ove_0_49:spellSlot(1).icon)
				end

				if ove_0_49.charName == "Rengar" then
					ove_0_6.yimenu.Misc.qdodge.qtarget:boolean("rengarR", "躲避雷恩加尔 R", true)
					ove_0_6.yimenu.Misc.qdodge.qtarget.rengarR:set("icon", ove_0_49:spellSlot(3).icon)
				end

				if ove_0_49.charName == "Sett" then
					ove_0_6.yimenu.Misc.qdodge.qtarget:boolean("settR", "躲避瑟提 R", true)
					ove_0_6.yimenu.Misc.qdodge.qtarget.settR:set("icon", ove_0_49:spellSlot(3).icon)
				end

				if ove_0_49.charName == "Singed" then
					ove_0_6.yimenu.Misc.qdodge.qtarget:boolean("singedE", "躲避辛吉德 E", true)
					ove_0_6.yimenu.Misc.qdodge.qtarget.singedE:set("icon", ove_0_49:spellSlot(2).icon)
				end

				if ove_0_49.charName == "Skarner" then
					ove_0_6.yimenu.Misc.qdodge.qtarget:boolean("skarnerR", "躲避斯卡纳 R (待定)", true)
					ove_0_6.yimenu.Misc.qdodge.qtarget.skarnerR:set("icon", ove_0_49:spellSlot(3).icon)
				end

				if ove_0_49.charName == "Syndra" then
					ove_0_6.yimenu.Misc.qdodge.qtarget:boolean("syndraR", "躲避辛德拉 R", true)
					ove_0_6.yimenu.Misc.qdodge.qtarget.syndraR:set("icon", ove_0_49:spellSlot(3).icon)
				end

				if ove_0_49.charName == "Tahmkench" or ove_0_49.charName == "TahmKench" then
					ove_0_6.yimenu.Misc.qdodge.qtarget:boolean("tahmR", "躲避塔姆 R", true)
					ove_0_6.yimenu.Misc.qdodge.qtarget.tahmR:set("icon", ove_0_49:spellSlot(3).icon)
				end

				if ove_0_49.charName == "Teemo" then
					ove_0_6.yimenu.Misc.qdodge.qtarget:boolean("teemoQ", "躲避提莫 Q", true)
					ove_0_6.yimenu.Misc.qdodge.qtarget.teemoQ:set("icon", ove_0_49:spellSlot(0).icon)
				end

				if ove_0_49.charName == "Tristana" then
					ove_0_6.yimenu.Misc.qdodge.qtarget:boolean("tristE", "躲避崔丝塔娜 E", true)
					ove_0_6.yimenu.Misc.qdodge.qtarget.tristE:set("icon", ove_0_49:spellSlot(2).icon)
					ove_0_6.yimenu.Misc.qdodge.qtarget:boolean("tristR", "躲避崔丝塔娜 R", true)
					ove_0_6.yimenu.Misc.qdodge.qtarget.tristR:set("icon", ove_0_49:spellSlot(3).icon)
				end

				if ove_0_49.charName == "Twistedfate" then
					ove_0_6.yimenu.Misc.qdodge.qtarget:boolean("twistedW", "躲避崔斯特 W", true)
					ove_0_6.yimenu.Misc.qdodge.qtarget.twistedW:set("icon", ove_0_49:spellSlot(1).icon)
				end

				if ove_0_49.charName == "Udyr" then
					ove_0_6.yimenu.Misc.qdodge.qtarget:boolean("udyrE", "躲避乌迪尔 E", true)
					ove_0_6.yimenu.Misc.qdodge.qtarget.udyrE:set("icon", ove_0_49:spellSlot(2).icon)
				end

				if ove_0_49.charName == "Vayne" then
					ove_0_6.yimenu.Misc.qdodge.qtarget:boolean("vayneE", "躲避薇恩 E", true)
					ove_0_6.yimenu.Misc.qdodge.qtarget.vayneE:set("icon", ove_0_49:spellSlot(2).icon, true)
				end

				if ove_0_49.charName == "Veigar" then
					ove_0_6.yimenu.Misc.qdodge.qtarget:boolean("veigaR", "躲避维迦 R", true)
					ove_0_6.yimenu.Misc.qdodge.qtarget.veigaR:set("icon", ove_0_49:spellSlot(3).icon, true)
				end

				if ove_0_49.charName == "Vi" then
					ove_0_6.yimenu.Misc.qdodge.qtarget:boolean("viR", "躲避蔚 R", true)
					ove_0_6.yimenu.Misc.qdodge.qtarget.viR:set("icon", ove_0_49:spellSlot(3).icon, true)
				end

				if ove_0_49.charName == "Volibear" then
					ove_0_6.yimenu.Misc.qdodge.qtarget:boolean("voliQ", "躲避沃利贝尔 Q", true)
					ove_0_6.yimenu.Misc.qdodge.qtarget.voliQ:set("icon", ove_0_49:spellSlot(0).icon, true)
				end
			end

			ove_0_6.yimenu:menu("Draws", "Draws")
			ove_0_6.yimenu.Draws:dropdown("circlemode", "圆形模式", 1, {
				"默认",
				"彩虹"
			})
			ove_0_6.yimenu.Draws:boolean("drawQ", "绘制 Q 范围", true)
			ove_0_6.yimenu.Draws:boolean("drawbuff", "绘制增益时间", true)
			ove_0_6.yimenu.Draws:boolean("drawAA", "绘制普攻追踪", true)
			ove_0_6.yimenu:menu("Keys", "Keys")
			ove_0_6.yimenu.Keys:keybind("stogglebar", "连招键", "Space", nil)
			ove_0_6.yimenu.Keys:keybind("clearkey", "清除键", "V", nil)
			ove_0_6.yimenu.Keys.clearkey:permashow(true)
			ove_0_6.yimenu.Keys:keybind("farmtoggle", "启用法术打怪", nil, "A")
			ove_0_6.yimenu.Keys:keybind("semiq", "半 Q", "Q", nil)
			ove_0_6.yimenu.Keys:keybind("dive", "启用塔潜", nil, "G")
		end
	end

	if player.charName == "Naafiri" then
		ove_0_6.naafirimenu = menu("Fatality " .. player.charName, "[Fatality] Naafiri")

		ove_0_6.naafirimenu:set("icon", player.iconSquare)
		ove_0_6.naafirimenu:dropdown("language", "Language", 1, {
			"English",
			"CN"
		})
		ove_0_6.naafirimenu.language:set("tooltip", "Please Reload | 请重新加载")

		local ove_0_50 = ove_0_6.naafirimenu.language:get()

		if ove_0_50 == 1 then
			ove_0_6.naafirimenu:menu("Combo", "Combo")
			ove_0_6.naafirimenu.Combo:menu("qsettings", "Darkin Daggers - [Q]")
			ove_0_6.naafirimenu.Combo.qsettings:set("icon", player:spellSlot(0).icon)
			ove_0_6.naafirimenu.Combo.qsettings:boolean("useq", "Enable Q in Combo", true)
			ove_0_6.naafirimenu.Combo.qsettings:boolean("slowpred", "Enable Q slow Prediction", true)
			ove_0_6.naafirimenu.Combo:menu("wsettings", "Hounds' Pursuit - [W]")
			ove_0_6.naafirimenu.Combo.wsettings:set("icon", player:spellSlot(1).icon)
			ove_0_6.naafirimenu.Combo.wsettings:dropdown("wmode", "W Mode", 2, {
				"Always",
				"Killable"
			})
			ove_0_6.naafirimenu.Combo.wsettings:boolean("usew", "Enable W in Combo", true)
			ove_0_6.naafirimenu.Combo.wsettings:boolean("noaa", "Dont W if Target is in AA Range", true)
			ove_0_6.naafirimenu.Combo:menu("esettings", "Eviscerate - [E]")
			ove_0_6.naafirimenu.Combo.esettings:set("icon", player:spellSlot(2).icon)
			ove_0_6.naafirimenu.Combo.esettings:boolean("usee", "Enable E in Combo", true)
			ove_0_6.naafirimenu.Combo:menu("rsettings", "The Call of the Pack - [R]")
			ove_0_6.naafirimenu.Combo.rsettings:set("icon", player:spellSlot(3).icon)
			ove_0_6.naafirimenu.Combo.rsettings:boolean("rw", "Use R Before W", true)
			ove_0_6.naafirimenu:menu("Killsteal", "Killsteal")
			ove_0_6.naafirimenu.Killsteal:boolean("ksQ", "Enable Q Killsteal", true)
			ove_0_6.naafirimenu.Killsteal:boolean("ksW", "Enable W Killsteal", true)
			ove_0_6.naafirimenu.Killsteal:boolean("ksE", "Enable E Killsteal", true)
			ove_0_6.naafirimenu:menu("Clear", "Clear")
			ove_0_6.naafirimenu.Clear:header("Lane Clear", "Lane Clear")
			ove_0_6.naafirimenu.Clear:boolean("LcQ", "Enable Q in Lane Clear", true)
			ove_0_6.naafirimenu.Clear:boolean("LcE", "Enable E in Lane Clear", true)
			ove_0_6.naafirimenu.Clear:header("Jungle Clear", "Jungle Clear")
			ove_0_6.naafirimenu.Clear:boolean("JcQ", "Enable Q in Jungle Clear", true)
			ove_0_6.naafirimenu.Clear:boolean("JcE", "Enable E in Jungle Clear", true)
			ove_0_6.naafirimenu:menu("Draws", "Draws")
			ove_0_6.naafirimenu.Draws:dropdown("circlemode", "Circle Mode", 1, {
				"Default",
				"Rainbow"
			})
			ove_0_6.naafirimenu.Draws:boolean("drawQ", "Draw Q Range", true)
			ove_0_6.naafirimenu.Draws:boolean("drawW", "Draw W Range", true)
			ove_0_6.naafirimenu.Draws:boolean("drawE", "Draw E Range", true)
			ove_0_6.naafirimenu.Draws:boolean("drawbuff", "Draw Buff Time", true)
			ove_0_6.naafirimenu.Draws:boolean("drawAA", "Draw AA Tracker", true)
			ove_0_6.naafirimenu:menu("Keys", "Keys")
			ove_0_6.naafirimenu.Keys:keybind("stogglebar", "Combo Key", "Space", nil)
			ove_0_6.naafirimenu.Keys:keybind("clearkey", "Clear Key", "V", nil)
			ove_0_6.naafirimenu.Keys.clearkey:permashow(true)
			ove_0_6.naafirimenu.Keys:keybind("farmtoggle", "Enable Spell Farming", nil, "A")
			ove_0_6.naafirimenu.Keys:keybind("semiw", "Semi W", "T", nil)
			ove_0_6.naafirimenu.Keys:keybind("dive", "Enable Tower Dive", nil, "G")
		end

		if ove_0_50 == 2 then
			ove_0_6.naafirimenu:menu("Combo", "Combo")
			ove_0_6.naafirimenu.Combo:menu("qsettings", "Darkin Daggers - [Q]")
			ove_0_6.naafirimenu.Combo.qsettings:set("icon", player:spellSlot(0).icon)
			ove_0_6.naafirimenu.Combo.qsettings:boolean("useq", "在连招中启用 Q", true)
			ove_0_6.naafirimenu.Combo.qsettings:boolean("slowpred", "启用 Q 缓慢预判", true)
			ove_0_6.naafirimenu.Combo:menu("wsettings", "Hounds' Pursuit - [W]")
			ove_0_6.naafirimenu.Combo.wsettings:set("icon", player:spellSlot(1).icon)
			ove_0_6.naafirimenu.Combo.wsettings:dropdown("wmode", "W 模式", 2, {
				"始终",
				"可击杀"
			})
			ove_0_6.naafirimenu.Combo.wsettings:boolean("usew", "在连招中启用 W", true)
			ove_0_6.naafirimenu.Combo.wsettings:boolean("noaa", "如果目标在普攻范围内，请不要使用 W", true)
			ove_0_6.naafirimenu.Combo:menu("esettings", "Eviscerate - [E]")
			ove_0_6.naafirimenu.Combo.esettings:set("icon", player:spellSlot(2).icon)
			ove_0_6.naafirimenu.Combo.esettings:boolean("usee", "在连招中启用 E", true)
			ove_0_6.naafirimenu.Combo:menu("rsettings", "The Call of the Pack - [R]")
			ove_0_6.naafirimenu.Combo.rsettings:set("icon", player:spellSlot(3).icon)
			ove_0_6.naafirimenu.Combo.rsettings:boolean("rw", "在使用 W 之前使用 R", true)
			ove_0_6.naafirimenu:menu("Killsteal", "Killsteal")
			ove_0_6.naafirimenu.Killsteal:boolean("ksQ", "启用 Q 抢人头", true)
			ove_0_6.naafirimenu.Killsteal:boolean("ksW", "启用 W 抢人头", true)
			ove_0_6.naafirimenu.Killsteal:boolean("ksE", "启用 E 抢人头", true)
			ove_0_6.naafirimenu:menu("Clear", "Clear")
			ove_0_6.naafirimenu.Clear:header("Lane Clear", "Lane Clear")
			ove_0_6.naafirimenu.Clear:boolean("LcQ", "在清线时启用 Q", true)
			ove_0_6.naafirimenu.Clear:boolean("LcE", "在清线时启用 E", true)
			ove_0_6.naafirimenu.Clear:header("Jungle Clear", "Jungle Clear")
			ove_0_6.naafirimenu.Clear:boolean("JcQ", "在清野时启用 Q", true)
			ove_0_6.naafirimenu.Clear:boolean("JcE", "在清野时启用 E", true)
			ove_0_6.naafirimenu:menu("Draws", "Draws")
			ove_0_6.naafirimenu.Draws:dropdown("circlemode", "圆圈模式", 1, {
				"默认",
				"彩虹"
			})
			ove_0_6.naafirimenu.Draws:boolean("drawQ", "绘制 Q 范围", true)
			ove_0_6.naafirimenu.Draws:boolean("drawW", "绘制 W 范围", true)
			ove_0_6.naafirimenu.Draws:boolean("drawE", "绘制 E 范围", true)
			ove_0_6.naafirimenu.Draws:boolean("drawbuff", "绘制增益持续时间", true)
			ove_0_6.naafirimenu.Draws:boolean("drawAA", "绘制普攻追踪器", true)
			ove_0_6.naafirimenu:menu("Keys", "Keys")
			ove_0_6.naafirimenu.Keys:keybind("stogglebar", "组合键", "Space", nil)
			ove_0_6.naafirimenu.Keys:keybind("clearkey", "清理键", "V", nil)
			ove_0_6.naafirimenu.Keys.clearkey:permashow(true)
			ove_0_6.naafirimenu.Keys:keybind("farmtoggle", "启用法术收割", nil, "A")
			ove_0_6.naafirimenu.Keys:keybind("semiw", "半自动 W", "T", nil)
			ove_0_6.naafirimenu.Keys:keybind("dive", "启用塔潜", nil, "G")
		end
	end

	if player.charName == "Ryze" then
		ove_0_6.ryzemenu = menu("Fatality " .. player.charName, "[Fatality] Ryze")

		ove_0_6.ryzemenu:set("icon", player.iconSquare)
		ove_0_6.ryzemenu:dropdown("language", "Language", 1, {
			"English",
			"CN"
		})
		ove_0_6.ryzemenu.language:set("tooltip", "Please Reload | 请重新加载")

		local ove_0_51 = ove_0_6.ryzemenu.language:get()

		if ove_0_51 == 1 then
			ove_0_6.ryzemenu:menu("Combo", "Combo")
			ove_0_6.ryzemenu.Combo:dropdown("combomode", "Combo Mode", 1, {
				"E-Q",
				"E-W"
			})
			ove_0_6.ryzemenu.Combo:menu("qsettings", "Overload - [Q]")
			ove_0_6.ryzemenu.Combo.qsettings:set("icon", player:spellSlot(0).icon)
			ove_0_6.ryzemenu.Combo.qsettings:boolean("useq", "Enable Q in Combo", true)
			ove_0_6.ryzemenu.Combo.qsettings:boolean("qslowpred", "Enable Q Slow Prediction", true)
			ove_0_6.ryzemenu.Combo:menu("wsettings", "Rune Prison - [W]")
			ove_0_6.ryzemenu.Combo.wsettings:set("icon", player:spellSlot(1).icon)
			ove_0_6.ryzemenu.Combo.wsettings:boolean("usew", "Enable W in Combo", true)
			ove_0_6.ryzemenu.Combo.wsettings:boolean("onlycomb", "Only W if E-Q is Ready", true)
			ove_0_6.ryzemenu.Combo.wsettings.onlycomb:set("tooltip", "Only Works if Combo Mode is E-Q!!!")
			ove_0_6.ryzemenu.Combo:menu("esettings", "Spell Flux - [E]")
			ove_0_6.ryzemenu.Combo.esettings:set("icon", player:spellSlot(2).icon)
			ove_0_6.ryzemenu.Combo.esettings:boolean("usee", "Enable E in Combo", true)
			ove_0_6.ryzemenu.Combo.esettings:boolean("ejump", "Try to E jump From Minions", true)
			ove_0_6.ryzemenu:menu("Killsteal", "Killsteal")
			ove_0_6.ryzemenu.Killsteal:boolean("ksQ", "Enable Q Killsteal", true)
			ove_0_6.ryzemenu.Killsteal:boolean("ksW", "Enable W Killsteal", true)
			ove_0_6.ryzemenu.Killsteal:boolean("ksE", "Enable E Killsteal", true)
			ove_0_6.ryzemenu:menu("Clear", "Clear")
			ove_0_6.ryzemenu.Clear:header("Lane Clear", "Lane Clear")
			ove_0_6.ryzemenu.Clear:boolean("LcQ", "Enable Q Lane Clear", true)
			ove_0_6.ryzemenu.Clear:boolean("LcE", "Enable E in Lane Clear", true)
			ove_0_6.ryzemenu.Clear:header("Jungle Clear", "Jungle Clear")
			ove_0_6.ryzemenu.Clear:boolean("JcQ", "Enable Q in Jungle Clear", true)
			ove_0_6.ryzemenu.Clear:boolean("JcW", "Enable W in Jungle Clear", true)
			ove_0_6.ryzemenu.Clear:boolean("JcE", "Enable E in Jungle Clear", true)
			ove_0_6.ryzemenu:menu("Misc", "Misc")
			ove_0_6.ryzemenu.Misc:boolean("AG", "Enable Antigap Closer", true)
			ove_0_6.ryzemenu.Misc:slider("HP", "HP % to Anti Gapclose", 50, 0, 100, 1)
			ove_0_6.ryzemenu:menu("Draws", "Draws")
			ove_0_6.ryzemenu.Draws:dropdown("circlemode", "Circle Mode", 1, {
				"Default",
				"Rainbow"
			})
			ove_0_6.ryzemenu.Draws:boolean("drawQ", "Draw Q Range", true)
			ove_0_6.ryzemenu.Draws:boolean("drawW", "Draw W Range", true)
			ove_0_6.ryzemenu.Draws:boolean("drawE", "Draw E Range", true)
			ove_0_6.ryzemenu.Draws:boolean("drawR", "Draw R Range", true)
			ove_0_6.ryzemenu.Draws:boolean("drawAA", "Draw AA Tracker", true)
			ove_0_6.ryzemenu:menu("Keys", "Keys")
			ove_0_6.ryzemenu.Keys:keybind("stogglebar", "Combo Key", "Space", nil)
			ove_0_6.ryzemenu.Keys:keybind("clearkey", "Clear Key", "V", nil)
			ove_0_6.ryzemenu.Keys.clearkey:permashow(true)
			ove_0_6.ryzemenu.Keys:keybind("farmtoggle", "Enable Spell Farming", nil, "A")
		end

		if ove_0_51 == 2 then
			ove_0_6.ryzemenu:menu("Combo", "连招")
			ove_0_6.ryzemenu.Combo:dropdown("combomode", "连招模式", 1, {
				"E-Q",
				"E-W"
			})
			ove_0_6.ryzemenu.Combo:menu("qsettings", "超负荷 - [Q]")
			ove_0_6.ryzemenu.Combo.qsettings:set("icon", player:spellSlot(0).icon)
			ove_0_6.ryzemenu.Combo.qsettings:boolean("useq", "连招中使用Q", true)
			ove_0_6.ryzemenu.Combo.qsettings:boolean("qslowpred", "启用Q缓慢预测", true)
			ove_0_6.ryzemenu.Combo:menu("wsettings", "符文监牢 - [W]")
			ove_0_6.ryzemenu.Combo.wsettings:set("icon", player:spellSlot(1).icon)
			ove_0_6.ryzemenu.Combo.wsettings:boolean("usew", "连招中使用W", true)
			ove_0_6.ryzemenu.Combo.wsettings:boolean("onlycomb", "只有在E-Q准备就绪时才使用W", true)
			ove_0_6.ryzemenu.Combo.wsettings.onlycomb:set("tooltip", "仅在连招模式为E-Q时有效!!!")
			ove_0_6.ryzemenu.Combo:menu("esettings", "法术通量 - [E]")
			ove_0_6.ryzemenu.Combo.esettings:set("icon", player:spellSlot(2).icon)
			ove_0_6.ryzemenu.Combo.esettings:boolean("usee", "连招中使用E", true)
			ove_0_6.ryzemenu.Combo.esettings:boolean("ejump", "尝试从小兵上跳跃E", true)
			ove_0_6.ryzemenu:menu("Killsteal", "抢人头")
			ove_0_6.ryzemenu.Killsteal:boolean("ksQ", "启用Q进行抢人头", true)
			ove_0_6.ryzemenu.Killsteal:boolean("ksW", "启用W进行抢人头", true)
			ove_0_6.ryzemenu.Killsteal:boolean("ksE", "启用E进行抢人头", true)
			ove_0_6.ryzemenu:menu("Clear", "清线")
			ove_0_6.ryzemenu.Clear:header("清线", "清线")
			ove_0_6.ryzemenu.Clear:boolean("LcQ", "清线时启用Q", true)
			ove_0_6.ryzemenu.Clear:boolean("LcE", "清线时启用E", true)
			ove_0_6.ryzemenu.Clear:header("清野", "清野")
			ove_0_6.ryzemenu.Clear:boolean("JcQ", "清野时启用Q", true)
			ove_0_6.ryzemenu.Clear:boolean("JcW", "清野时启用W", true)
			ove_0_6.ryzemenu.Clear:boolean("JcE", "清野时启用E", true)
			ove_0_6.ryzemenu:menu("Misc", "杂项")
			ove_0_6.ryzemenu.Misc:boolean("AG", "启用防突进", true)
			ove_0_6.ryzemenu.Misc:slider("HP", "防突进的生命值 %", 50, 0, 100, 1)
			ove_0_6.ryzemenu:menu("Draws", "显示")
			ove_0_6.ryzemenu.Draws:dropdown("circlemode", "圆圈模式", 1, {
				"默认",
				"彩虹"
			})
			ove_0_6.ryzemenu.Draws:boolean("drawQ", "显示Q范围", true)
			ove_0_6.ryzemenu.Draws:boolean("drawW", "显示W范围", true)
			ove_0_6.ryzemenu.Draws:boolean("drawE", "显示E范围", true)
			ove_0_6.ryzemenu.Draws:boolean("drawR", "显示R范围", true)
			ove_0_6.ryzemenu.Draws:boolean("drawAA", "显示普攻追踪器", true)
			ove_0_6.ryzemenu:menu("Keys", "按键")
			ove_0_6.ryzemenu.Keys:keybind("stogglebar", "连招按键", "Space", nil)
			ove_0_6.ryzemenu.Keys:keybind("clearkey", "清线按键", "V", nil)
			ove_0_6.ryzemenu.Keys.clearkey:permashow(true)
			ove_0_6.ryzemenu.Keys:keybind("farmtoggle", "启用技能清线", nil, "A")
		end
	end

	if player.charName == "Smolder" then
		ove_0_6.smoldermenu = menu("Fatality " .. player.charName, "[Fatality] Smolder")

		ove_0_6.smoldermenu:set("icon", player.iconSquare)
		ove_0_6.smoldermenu:dropdown("language", "Language", 1, {
			"English",
			"CN"
		})
		ove_0_6.smoldermenu.language:set("tooltip", "Please Reload | 请重新加载")

		local ove_0_52 = ove_0_6.smoldermenu.language:get()

		if ove_0_52 == 1 then
			ove_0_6.smoldermenu:menu("Combo", "Combo")
			ove_0_6.smoldermenu.Combo:menu("qsettings", "Super Scorcher Breath - [Q]")
			ove_0_6.smoldermenu.Combo.qsettings:set("icon", player:spellSlot(0).icon)
			ove_0_6.smoldermenu.Combo.qsettings:boolean("useq", "Enable Q in Combo", true)
			ove_0_6.smoldermenu.Combo:menu("wsettings", "Achooo! - [W]")
			ove_0_6.smoldermenu.Combo.wsettings:boolean("usew", "Enable W in Combo", true)
			ove_0_6.smoldermenu.Combo.wsettings:set("icon", player:spellSlot(1).icon)
			ove_0_6.smoldermenu.Combo.wsettings:boolean("slowpredw", "Enable W Slow Prediction", false)
			ove_0_6.smoldermenu:menu("Killsteal", "Killsteal")
			ove_0_6.smoldermenu.Killsteal:boolean("ksQ", "Enable Q Killsteal", true)
			ove_0_6.smoldermenu.Killsteal:boolean("ksW", "Enable W Killsteal", true)
			ove_0_6.smoldermenu:menu("Clear", "Clear")
			ove_0_6.smoldermenu.Clear:header("Lane Clear", "Lane Clear")
			ove_0_6.smoldermenu.Clear:boolean("LhQ", "Enable Q Last Hit", true)
			ove_0_6.smoldermenu.Clear:boolean("LcW", "Enable W in Lane Clear", true)
			ove_0_6.smoldermenu.Clear:header("Jungle Clear", "Jungle Clear")
			ove_0_6.smoldermenu.Clear:boolean("JcQ", "Enable Q in Jungle Clear", true)
			ove_0_6.smoldermenu.Clear:boolean("JcW", "Enable W in Jungle Clear", true)
			ove_0_6.smoldermenu:menu("Draws", "Draws")
			ove_0_6.smoldermenu.Draws:dropdown("circlemode", "Circle Mode", 1, {
				"Default",
				"Rainbow"
			})
			ove_0_6.smoldermenu.Draws:boolean("drawQ", "Draw Q Range", true)
			ove_0_6.smoldermenu.Draws:boolean("drawW", "Draw W Range", true)
			ove_0_6.smoldermenu.Draws:boolean("drawE", "Draw E Range", true)
			ove_0_6.smoldermenu.Draws:boolean("drawR", "Draw R Range", true)
			ove_0_6.smoldermenu.Draws:boolean("drawAA", "Draw AA Tracker", true)
			ove_0_6.smoldermenu:menu("Keys", "Keys")
			ove_0_6.smoldermenu.Keys:keybind("stogglebar", "Combo Key", "Space", nil)
			ove_0_6.smoldermenu.Keys:keybind("clearkey", "Clear Key", "V", nil)
			ove_0_6.smoldermenu.Keys.clearkey:permashow(true)
			ove_0_6.smoldermenu.Keys:keybind("lasthitkey", "Lasthit Key", "X", nil)
			ove_0_6.smoldermenu.Keys:keybind("farmtoggle", "Enable Spell Farming", nil, "A")
			ove_0_6.smoldermenu.Keys:keybind("semir", "Semi R", "T", nil)
		end

		if ove_0_52 == 2 then
			ove_0_6.smoldermenu:menu("Combo", "Combo")
			ove_0_6.smoldermenu.Combo:menu("qsettings", "Super Scorcher Breath - [Q]")
			ove_0_6.smoldermenu.Combo.qsettings:set("icon", player:spellSlot(0).icon)
			ove_0_6.smoldermenu.Combo.qsettings:boolean("useq", "在组合中启用 Q", true)
			ove_0_6.smoldermenu.Combo:menu("wsettings", "Achooo! - [W]")
			ove_0_6.smoldermenu.Combo.wsettings:set("icon", player:spellSlot(1).icon)
			ove_0_6.smoldermenu.Combo.wsettings:boolean("usew", "在组合中启用 W", true)
			ove_0_6.smoldermenu.Combo.wsettings:boolean("slowpredw", "启用 W 缓慢预判", false)
			ove_0_6.smoldermenu:menu("Killsteal", "Killsteal")
			ove_0_6.smoldermenu.Killsteal:boolean("ksQ", "启用 Q 抢人头", true)
			ove_0_6.smoldermenu.Killsteal:boolean("ksW", "启用 W 抢人头", true)
			ove_0_6.smoldermenu:menu("Clear", "Clear")
			ove_0_6.smoldermenu.Clear:header("Lane Clear", "Lane Clear")
			ove_0_6.smoldermenu.Clear:boolean("LhQ", "启用 Q 补刀", true)
			ove_0_6.smoldermenu.Clear:boolean("LcW", "在清线时启用 W", true)
			ove_0_6.smoldermenu.Clear:header("Jungle Clear", "Jungle Clear")
			ove_0_6.smoldermenu.Clear:boolean("JcQ", "在清野时启用 Q", true)
			ove_0_6.smoldermenu.Clear:boolean("JcW", "在清野时启用 W", true)
			ove_0_6.smoldermenu:menu("Draws", "Draws")
			ove_0_6.smoldermenu.Draws:dropdown("circlemode", "圆形模式", 1, {
				"默认",
				"彩虹 "
			})
			ove_0_6.smoldermenu.Draws:boolean("drawQ", "绘制 Q 射程", true)
			ove_0_6.smoldermenu.Draws:boolean("drawW", "绘制 W 射程", true)
			ove_0_6.smoldermenu.Draws:boolean("drawE", "绘制 E 射程", true)
			ove_0_6.smoldermenu.Draws:boolean("drawR", "绘制 R 射程", true)
			ove_0_6.smoldermenu.Draws:boolean("drawAA", "绘制普攻追踪器", true)
			ove_0_6.smoldermenu:menu("Keys", "Keys")
			ove_0_6.smoldermenu.Keys:keybind("stogglebar", "组合键", "Space", nil)
			ove_0_6.smoldermenu.Keys:keybind("clearkey", "清理键", "V", nil)
			ove_0_6.smoldermenu.Keys.clearkey:permashow(true)
			ove_0_6.smoldermenu.Keys:keybind("lasthitkey", "补刀按键", "X", nil)
			ove_0_6.smoldermenu.Keys:keybind("farmtoggle", "启用法术收割", nil, "A")
			ove_0_6.smoldermenu.Keys:keybind("semir", "半自动 R", "T", nil)
		end
	end

	if player.charName == "Tristana" then
		ove_0_6.tristmenu = menu("Fatality " .. player.charName, "[Fatality] Tristana")

		ove_0_6.tristmenu:set("icon", player.iconSquare)
		ove_0_6.tristmenu:dropdown("language", "Language", 1, {
			"English",
			"CN"
		})
		ove_0_6.tristmenu.language:set("tooltip", "Please Reload | 请重新加载")

		local ove_0_53 = ove_0_6.tristmenu.language:get()

		if ove_0_53 == 1 then
			ove_0_6.tristmenu:menu("Combo", "Combo")
			ove_0_6.tristmenu.Combo:menu("qsettings", "Rapid Fire - [Q]")
			ove_0_6.tristmenu.Combo.qsettings:set("icon", player:spellSlot(0).icon)
			ove_0_6.tristmenu.Combo.qsettings:boolean("useq", "Enable Q in Combo", true)
			ove_0_6.tristmenu.Combo.qsettings:slider("qrange", "Decrease Q Range", 50, 0, 100, 1)
			ove_0_6.tristmenu.Combo:menu("esettings", "Explosive Charge - [E]")
			ove_0_6.tristmenu.Combo.esettings:set("icon", player:spellSlot(2).icon)
			ove_0_6.tristmenu.Combo.esettings:boolean("usee", "Enable E in Combo", true)
			ove_0_6.tristmenu.Combo.esettings:boolean("focuse", "Focus E Target", true)
			ove_0_6.tristmenu.Combo.esettings:slider("erange", "Decrease E range", 50, 0, 100, 1)
			ove_0_6.tristmenu:menu("Killsteal", "Killsteal")
			ove_0_6.tristmenu.Killsteal:boolean("ksR", "Enable R Killsteal", true)
			ove_0_6.tristmenu.Killsteal.ksR:set("tooltip", "It Takes the E Damage into consideration")
			ove_0_6.tristmenu:menu("Clear", "Clear")
			ove_0_6.tristmenu.Clear:header("Jungle Clear", "Jungle Clear")
			ove_0_6.tristmenu.Clear:boolean("JcQ", "Enable Q in Jungle Clear", true)
			ove_0_6.tristmenu.Clear:boolean("JcE", "Enable E in Jungle Clear", true)
			ove_0_6.tristmenu:menu("Misc", "Misc")
			ove_0_6.tristmenu.Misc:boolean("ag", "Enable Antigap Closer", true)
			ove_0_6.tristmenu.Misc:slider("aghealth", "HP % to Anti Gapclose", 50, 1, 100, 1)
			ove_0_6.tristmenu:menu("Draws", "Draws")
			ove_0_6.tristmenu.Draws:dropdown("circlemode", "Circle Mode", 1, {
				"Default",
				"Rainbow"
			})
			ove_0_6.tristmenu.Draws:boolean("drawQ", "Draw Q Range", true)
			ove_0_6.tristmenu.Draws:boolean("drawW", "Draw W Range", true)
			ove_0_6.tristmenu.Draws:boolean("drawE", "Draw E Range", true)
			ove_0_6.tristmenu.Draws:boolean("drawAA", "Draw AA Tracker", true)
			ove_0_6.tristmenu.Draws:boolean("drawKill", "Draw Killable Text", true)
			ove_0_6.tristmenu:menu("Keys", "Keys")
			ove_0_6.tristmenu.Keys:keybind("stogglebar", "Combo Key", "Space", nil)
			ove_0_6.tristmenu.Keys:keybind("clearkey", "Clear Key", "V", nil)
			ove_0_6.tristmenu.Keys.clearkey:permashow(true)
			ove_0_6.tristmenu.Keys:keybind("farmtoggle", "Enable Spell Farming", nil, "A")
		end

		if ove_0_53 == 2 then
			ove_0_6.tristmenu:menu("Combo", "组合")
			ove_0_6.tristmenu.Combo:menu("qsettings", "熊熊大火 - [Q]")
			ove_0_6.tristmenu.Combo.qsettings:set("icon", player:spellSlot(0).icon)
			ove_0_6.tristmenu.Combo.qsettings:boolean("useq", "在连招中启用 Q", true)
			ove_0_6.tristmenu.Combo.qsettings:slider("qrange", "减小 Q 范围", 50, 0, 100, 1)
			ove_0_6.tristmenu.Combo:menu("esettings", "爆炸装药 - [E]")
			ove_0_6.tristmenu.Combo.esettings:set("icon", player:spellSlot(2).icon)
			ove_0_6.tristmenu.Combo.esettings:boolean("usee", "在连招中启用 E", true)
			ove_0_6.tristmenu.Combo.esettings:boolean("focuse", "焦点E目标", true)
			ove_0_6.tristmenu.Combo.esettings:slider("erange", "减小 E 范围", 50, 0, 100, 1)
			ove_0_6.tristmenu:menu("Killsteal", "偷窃")
			ove_0_6.tristmenu.Killsteal:boolean("ksR", "启用 R 抢人头", true)
			ove_0_6.tristmenu.Killsteal.ksR:set("tooltip", "它会考虑到 E 的伤害")
			ove_0_6.tristmenu:menu("Clear", "清除")
			ove_0_6.tristmenu.Clear:header("Jungle Clear", "丛林清除")
			ove_0_6.tristmenu.Clear:boolean("JcQ", "在清野时启用 Q", true)
			ove_0_6.tristmenu.Clear:boolean("JcE", "在清野时启用 E", true)
			ove_0_6.tristmenu:menu("Misc", "杂项")
			ove_0_6.tristmenu.Misc:boolean("ag", "启用防突进", true)
			ove_0_6.tristmenu.Misc:slider("aghealth", "生命值百分比至防突进", 50, 1, 100, 1)
			ove_0_6.tristmenu:menu("Draws", "抽奖")
			ove_0_6.tristmenu.Draws:dropdown("circlemode", "圆圈模式", 1, {
				"默认",
				"彩虹"
			})
			ove_0_6.tristmenu.Draws:boolean("drawQ", "绘制 Q 范围", true)
			ove_0_6.tristmenu.Draws:boolean("drawW", "绘制 W 范围", true)
			ove_0_6.tristmenu.Draws:boolean("drawE", "绘制 E 范围", true)
			ove_0_6.tristmenu.Draws:boolean("drawAA", "绘制普攻追踪器", true)
			ove_0_6.tristmenu.Draws:boolean("drawKill", "绘制可击杀文本", true)
			ove_0_6.tristmenu:menu("Keys", "按键")
			ove_0_6.tristmenu.Keys:keybind("stogglebar", "组合键", "Space", nil)
			ove_0_6.tristmenu.Keys:keybind("clearkey", "清理键", "V", nil)
			ove_0_6.tristmenu.Keys.clearkey:permashow(true)
			ove_0_6.tristmenu.Keys:keybind("farmtoggle", "启用法术收割", nil, "A")
		end
	end

	if player.charName == "Twitch" then
		ove_0_6.twitchmenu = menu("Fatality " .. player.charName, "[Fatality] Twitch")

		ove_0_6.twitchmenu:set("icon", player.iconSquare)
		ove_0_6.twitchmenu:dropdown("language", "Language", 1, {
			"English",
			"CN"
		})
		ove_0_6.twitchmenu.language:set("tooltip", "Please Reload | 请重新加载")

		local ove_0_54 = ove_0_6.twitchmenu.language:get()

		if ove_0_54 == 1 then
			ove_0_6.twitchmenu:menu("Combo", "Combo")
			ove_0_6.twitchmenu.Combo:menu("qsettings", "Ambush - [Q]")
			ove_0_6.twitchmenu.Combo.qsettings:set("icon", player:spellSlot(0).icon)
			ove_0_6.twitchmenu.Combo.qsettings:boolean("useq", "Q Before Attacking", true)
			ove_0_6.twitchmenu.Combo.qsettings:slider("health", "Target Health % to Q", 50, 0, 100, 1)
			ove_0_6.twitchmenu.Combo:menu("wsettings", "Venom Cask - [W]")
			ove_0_6.twitchmenu.Combo.wsettings:set("icon", player:spellSlot(1).icon)
			ove_0_6.twitchmenu.Combo.wsettings:boolean("usew", "Enable W in Combo", true)
			ove_0_6.twitchmenu.Combo.wsettings:boolean("stealthblock", "Dont W while in Stealth", true)
			ove_0_6.twitchmenu.Combo.wsettings:boolean("ultblock", "Dont W while R is Active", true)
			ove_0_6.twitchmenu.Combo:menu("esettings", "Contaminate - [E]")
			ove_0_6.twitchmenu.Combo.esettings:set("icon", player:spellSlot(2).icon)
			ove_0_6.twitchmenu.Combo.esettings:boolean("eonkill", "Only E if Target is Killable", true)
			ove_0_6.twitchmenu.Combo.esettings:boolean("eondead", "E if you about to Die", true)
			ove_0_6.twitchmenu.Combo.esettings:slider("estacks", "Cast E on X Stacks (7 = Disabled)", 7, 1, 7, 1)
			ove_0_6.twitchmenu.Combo:menu("rsettings", "Spray and Pray - [R]")
			ove_0_6.twitchmenu.Combo.rsettings:set("icon", player:spellSlot(3).icon)
			ove_0_6.twitchmenu.Combo.rsettings:boolean("user", "Enable R in Combo", true)
			ove_0_6.twitchmenu.Combo.rsettings:slider("targets", "Min Targets in R Range", 2, 1, 5, 1)
			ove_0_6.twitchmenu:menu("Killsteal", "Killsteal")
			ove_0_6.twitchmenu.Killsteal:boolean("ksE", "Enable E in Killsteal", true)
			ove_0_6.twitchmenu:menu("Clear", "Clear")
			ove_0_6.twitchmenu.Clear:header("Lane Clear", "Lane Clear")
			ove_0_6.twitchmenu.Clear:boolean("LcW", "Enable W in Lane Clear", true)
			ove_0_6.twitchmenu.Clear:header("Jungle Clear", "Jungle Clear")
			ove_0_6.twitchmenu.Clear:boolean("JcW", "Enable W in Jungle Clear", true)
			ove_0_6.twitchmenu.Clear:boolean("JcE", "Enable E in Jungle Clear", true)
			ove_0_6.twitchmenu:menu("Draws", "Draws")
			ove_0_6.twitchmenu.Draws:dropdown("circlemode", "Circle Mode", 1, {
				"Default",
				"Rainbow"
			})
			ove_0_6.twitchmenu.Draws:boolean("drawW", "Draw W Range", true)
			ove_0_6.twitchmenu.Draws:boolean("drawE", "Draw E Range", true)
			ove_0_6.twitchmenu.Draws:boolean("drawR", "Draw R Range", true)
			ove_0_6.twitchmenu.Draws:boolean("drawED", "Draw E Damage", true)
			ove_0_6.twitchmenu.Draws:boolean("drawAA", "Draw AA Tracker", true)
			ove_0_6.twitchmenu.Draws:boolean("drawBuff", "Draw Buff Times", true)
			ove_0_6.twitchmenu:menu("Keys", "Keys")
			ove_0_6.twitchmenu.Keys:keybind("stogglebar", "Combo Key", "Space", nil)
			ove_0_6.twitchmenu.Keys:keybind("clearkey", "Clear Key", "V", nil)
			ove_0_6.twitchmenu.Keys.clearkey:permashow(true)
			ove_0_6.twitchmenu.Keys:keybind("farmtoggle", "Enable Spell Farming", nil, "A")
			ove_0_6.twitchmenu.Keys:keybind("stealthbackport", "Stealth Back Port", "G", nil)
		end

		if ove_0_54 == 2 then
			ove_0_6.twitchmenu:menu("Combo", "连招")
			ove_0_6.twitchmenu.Combo:menu("qsettings", "伏击 - [Q]")
			ove_0_6.twitchmenu.Combo.qsettings:set("icon", player:spellSlot(0).icon)
			ove_0_6.twitchmenu.Combo.qsettings:boolean("useq", "攻击前使用Q", true)
			ove_0_6.twitchmenu.Combo.qsettings:slider("health", "Q的目标生命值 %", 50, 0, 100, 1)
			ove_0_6.twitchmenu.Combo:menu("wsettings", "毒雾瓶 - [W]")
			ove_0_6.twitchmenu.Combo.wsettings:set("icon", player:spellSlot(1).icon)
			ove_0_6.twitchmenu.Combo.wsettings:boolean("usew", "连招中使用W", true)
			ove_0_6.twitchmenu.Combo.wsettings:boolean("stealthblock", "隐身状态下不使用W", true)
			ove_0_6.twitchmenu.Combo.wsettings:boolean("ultblock", "R技能激活时不使用W", true)
			ove_0_6.twitchmenu.Combo:menu("esettings", "蔓延感染 - [E]")
			ove_0_6.twitchmenu.Combo.esettings:set("icon", player:spellSlot(2).icon)
			ove_0_6.twitchmenu.Combo.esettings:boolean("eonkill", "只在目标可击杀时使用E", true)
			ove_0_6.twitchmenu.Combo.esettings:boolean("eondead", "濒死时使用E", true)
			ove_0_6.twitchmenu.Combo.esettings:slider("estacks", "在X层叠加时施放E（7=禁用）", 7, 1, 7, 1)
			ove_0_6.twitchmenu.Combo:menu("rsettings", "扫射 - [R]")
			ove_0_6.twitchmenu.Combo.rsettings:set("icon", player:spellSlot(3).icon)
			ove_0_6.twitchmenu.Combo.rsettings:boolean("user", "连招中使用R", true)
			ove_0_6.twitchmenu.Combo.rsettings:slider("targets", "R范围内的最小目标数", 2, 1, 5, 1)
			ove_0_6.twitchmenu:menu("Killsteal", "抢人头")
			ove_0_6.twitchmenu.Killsteal:boolean("ksE", "启用E进行抢人头", true)
			ove_0_6.twitchmenu:menu("Clear", "清线")
			ove_0_6.twitchmenu.Clear:header("清线", "清线")
			ove_0_6.twitchmenu.Clear:boolean("LcW", "清线时启用W", true)
			ove_0_6.twitchmenu.Clear:header("清野", "清野")
			ove_0_6.twitchmenu.Clear:boolean("JcW", "清野时启用W", true)
			ove_0_6.twitchmenu.Clear:boolean("JcE", "清野时启用E", true)
			ove_0_6.twitchmenu:menu("Draws", "显示")
			ove_0_6.twitchmenu.Draws:dropdown("circlemode", "圆圈模式", 1, {
				"默认",
				"彩虹"
			})
			ove_0_6.twitchmenu.Draws:boolean("drawW", "显示W范围", true)
			ove_0_6.twitchmenu.Draws:boolean("drawE", "显示E范围", true)
			ove_0_6.twitchmenu.Draws:boolean("drawR", "显示R范围", true)
			ove_0_6.twitchmenu.Draws:boolean("drawED", "显示E伤害", true)
			ove_0_6.twitchmenu.Draws:boolean("drawAA", "显示普攻追踪器", true)
			ove_0_6.twitchmenu.Draws:boolean("drawBuff", "显示Buff时间", true)
			ove_0_6.twitchmenu:menu("Keys", "按键")
			ove_0_6.twitchmenu.Keys:keybind("stogglebar", "连招按键", "Space", nil)
			ove_0_6.twitchmenu.Keys:keybind("clearkey", "清线按键", "V", nil)
			ove_0_6.twitchmenu.Keys.clearkey:permashow(true)
			ove_0_6.twitchmenu.Keys:keybind("farmtoggle", "启用技能清线", nil, "A")
			ove_0_6.twitchmenu.Keys:keybind("stealthbackport", "隐身返城", "G", nil)
		end
	end

	if player.charName == "Varus" then
		ove_0_6.varusmenu = menu("Fatality " .. player.charName, "[Fatality] Varus")

		ove_0_6.varusmenu:set("icon", player.iconSquare)
		ove_0_6.varusmenu:dropdown("language", "Language", 1, {
			"English",
			"CN"
		})
		ove_0_6.varusmenu.language:set("tooltip", "Please Reload | 请重新加载")

		local ove_0_55 = ove_0_6.varusmenu.language:get()

		if ove_0_55 == 1 then
			ove_0_6.varusmenu:menu("Combo", "Combo")
			ove_0_6.varusmenu.Combo:menu("qsettings", "Piercing Arrow - [Q]")
			ove_0_6.varusmenu.Combo.qsettings:set("icon", player:spellSlot(0).icon)
			ove_0_6.varusmenu.Combo.qsettings:dropdown("qmode", "Q Mode", 1, {
				"Overcharge",
				"Full Charge",
				"Instant"
			})
			ove_0_6.varusmenu.Combo.qsettings:boolean("useq", "Enable Q in Combo", true)
			ove_0_6.varusmenu.Combo.qsettings:boolean("slowpredq", "Enable Q Slow Prediction", false)
			ove_0_6.varusmenu.Combo.qsettings:slider("wstacks", "W Stacks to use Q", 3, 0, 3, 1)
			ove_0_6.varusmenu.Combo.qsettings:slider("overcharge", "Overcharge Range", 200, 0, 350, 1)
			ove_0_6.varusmenu.Combo:menu("wsettings", "Blighted Quiver - [W]")
			ove_0_6.varusmenu.Combo.wsettings:set("icon", player:spellSlot(1).icon)
			ove_0_6.varusmenu.Combo.wsettings:boolean("usew", "Enable W in Combo", true)
			ove_0_6.varusmenu.Combo.wsettings:slider("health", "Target HP % to use W", 50, 1, 100, 1)
			ove_0_6.varusmenu.Combo:menu("esettings", "Hail of Arrows - [E]")
			ove_0_6.varusmenu.Combo.esettings:set("icon", player:spellSlot(2).icon)
			ove_0_6.varusmenu.Combo.esettings:boolean("usee", "Enable E in Combo", true)
			ove_0_6.varusmenu.Combo.esettings:boolean("slowprede", "Enable E Slow Prediction", false)
			ove_0_6.varusmenu.Combo.esettings:slider("wwstacks", "W Stacks to use E", 2, 0, 3, 1)
			ove_0_6.varusmenu.Combo:menu("rsettings", "Chain of Corruption - [R]")
			ove_0_6.varusmenu.Combo.rsettings:set("icon", player:spellSlot(3).icon)
			ove_0_6.varusmenu.Combo.rsettings:boolean("user", "Enable R in Combo", true)
			ove_0_6.varusmenu.Combo.rsettings.user:set("tooltip", "It Only Cast R if target is Killable with Combo Damage!")
			ove_0_6.varusmenu.Combo.rsettings:boolean("slowpredr", "Enable R Slow Prediction", false)
			ove_0_6.varusmenu:menu("Killsteal", "Killsteal")
			ove_0_6.varusmenu.Killsteal:boolean("ksQ", "Enable Q in Killsteal", true)
			ove_0_6.varusmenu.Killsteal:boolean("ksE", "Enable E in Killsteal", true)
			ove_0_6.varusmenu.Killsteal:boolean("ksR", "Enable R in Killsteal", false)
			ove_0_6.varusmenu:menu("Clear", "Clear")
			ove_0_6.varusmenu.Clear:header("Lane Clear", "Lane Clear")
			ove_0_6.varusmenu.Clear:boolean("LcE", "Enable E in Lane Clear", true)
			ove_0_6.varusmenu.Clear:header("Jungle Clear", "Jungle Clear")
			ove_0_6.varusmenu.Clear:boolean("JcQ", "Enable Q in Jungle Clear", true)
			ove_0_6.varusmenu.Clear:boolean("JcE", "Enable E in Jungle Clear", true)
			ove_0_6.varusmenu:menu("Misc", "Misc")
			ove_0_6.varusmenu.Misc:boolean("ag", "Enable Anti Gapcloser", true)
			ove_0_6.varusmenu.Misc:slider("antigaphp", "HP % to Anti Gapclose", 50, 1, 100, 1)
			ove_0_6.varusmenu:menu("Draws", "Draws")
			ove_0_6.varusmenu.Draws:dropdown("circlemode", "Circle Mode", 1, {
				"Default",
				"Rainbow"
			})
			ove_0_6.varusmenu.Draws:boolean("drawQ", "Draw Q Range", true)
			ove_0_6.varusmenu.Draws:boolean("drawE", "Draw E Range", true)
			ove_0_6.varusmenu.Draws:boolean("drawR", "Draw R Range", true)
			ove_0_6.varusmenu.Draws:boolean("drawAA", "Draw AA Tracker", true)
			ove_0_6.varusmenu:menu("Keys", "Keys")
			ove_0_6.varusmenu.Keys:keybind("stogglebar", "Combo Key", "Space", nil)
			ove_0_6.varusmenu.Keys:keybind("clearkey", "Clear Key", "V", nil)
			ove_0_6.varusmenu.Keys.clearkey:permashow(true)
			ove_0_6.varusmenu.Keys:keybind("farmtoggle", "Enable Spell Farming", nil, "A")
			ove_0_6.varusmenu.Keys:keybind("semiR", "Semi R", "T", nil)
		end

		if ove_0_55 == 2 then
			ove_0_6.varusmenu:menu("Combo", "连招")
			ove_0_6.varusmenu.Combo:menu("qsettings", "穿刺之箭 - [Q]")
			ove_0_6.varusmenu.Combo.qsettings:set("icon", player:spellSlot(0).icon)
			ove_0_6.varusmenu.Combo.qsettings:dropdown("qmode", "Q 模式", 1, {
				"过充",
				"全力充能",
				"立即释放"
			})
			ove_0_6.varusmenu.Combo.qsettings:boolean("useq", "连招中使用 Q", true)
			ove_0_6.varusmenu.Combo.qsettings:boolean("slowpredq", "启用 Q 慢速预测", false)
			ove_0_6.varusmenu.Combo.qsettings:slider("wstacks", "使用 Q 所需 W 层数", 3, 0, 3, 1)
			ove_0_6.varusmenu.Combo.qsettings:slider("overcharge", "过充范围", 200, 0, 350, 1)
			ove_0_6.varusmenu.Combo:menu("wsettings", "腐败箭袋 - [W]")
			ove_0_6.varusmenu.Combo.wsettings:set("icon", player:spellSlot(1).icon)
			ove_0_6.varusmenu.Combo.wsettings:boolean("usew", "连招中使用 W", true)
			ove_0_6.varusmenu.Combo.wsettings:slider("health", "目标生命值 % 使用 W", 50, 1, 100, 1)
			ove_0_6.varusmenu.Combo:menu("esettings", "枯萎箭雨 - [E]")
			ove_0_6.varusmenu.Combo.esettings:set("icon", player:spellSlot(2).icon)
			ove_0_6.varusmenu.Combo.esettings:boolean("usee", "连招中使用 E", true)
			ove_0_6.varusmenu.Combo.esettings:boolean("slowprede", "启用 E 慢速预测", false)
			ove_0_6.varusmenu.Combo.esettings:slider("wwstacks", "使用 E 所需 W 层数", 2, 0, 3, 1)
			ove_0_6.varusmenu.Combo:menu("rsettings", "腐败锁链 - [R]")
			ove_0_6.varusmenu.Combo.rsettings:set("icon", player:spellSlot(3).icon)
			ove_0_6.varusmenu.Combo.rsettings:boolean("user", "连招中使用 R", true)
			ove_0_6.varusmenu.Combo.rsettings.user:set("tooltip", "只有目标可被连招伤害击杀时才施放 R！")
			ove_0_6.varusmenu.Combo.rsettings:boolean("slowpredr", "启用 R 慢速预测", false)
			ove_0_6.varusmenu:menu("Killsteal", "击杀窃取")
			ove_0_6.varusmenu.Killsteal:boolean("ksQ", "启用 Q 击杀窃取", true)
			ove_0_6.varusmenu.Killsteal:boolean("ksE", "启用 E 击杀窃取", true)
			ove_0_6.varusmenu.Killsteal:boolean("ksR", "启用 R 击杀窃取", false)
			ove_0_6.varusmenu:menu("Clear", "清线")
			ove_0_6.varusmenu.Clear:header("Lane Clear", "清理兵线")
			ove_0_6.varusmenu.Clear:boolean("LcE", "清线中使用 E", true)
			ove_0_6.varusmenu.Clear:header("Jungle Clear", "清理野怪")
			ove_0_6.varusmenu.Clear:boolean("JcQ", "清野中使用 Q", true)
			ove_0_6.varusmenu.Clear:boolean("JcE", "清野中使用 E", true)
			ove_0_6.varusmenu:menu("Misc", "杂项")
			ove_0_6.varusmenu.Misc:boolean("ag", "启用反突进", true)
			ove_0_6.varusmenu.Misc:slider("antigaphp", "反突进的生命值 %", 50, 1, 100, 1)
			ove_0_6.varusmenu:menu("Draws", "绘图")
			ove_0_6.varusmenu.Draws:dropdown("circlemode", "圈模式", 1, {
				"默认",
				"彩虹"
			})
			ove_0_6.varusmenu.Draws:boolean("drawQ", "绘制 Q 范围", true)
			ove_0_6.varusmenu.Draws:boolean("drawE", "绘制 E 范围", true)
			ove_0_6.varusmenu.Draws:boolean("drawR", "绘制 R 范围", true)
			ove_0_6.varusmenu.Draws:boolean("drawAA", "绘制普攻跟踪", true)
			ove_0_6.varusmenu:menu("Keys", "按键")
			ove_0_6.varusmenu.Keys:keybind("stogglebar", "连招键", "Space", nil)
			ove_0_6.varusmenu.Keys:keybind("clearkey", "清线键", "V", nil)
			ove_0_6.varusmenu.Keys.clearkey:permashow(true)
			ove_0_6.varusmenu.Keys:keybind("farmtoggle", "启用技能清线", nil, "A")
			ove_0_6.varusmenu.Keys:keybind("semiR", "半自动 R", "T", nil)
		end
	end

	if player.charName == "Vayne" then
		ove_0_6.vaynemenu = menu("Fatality " .. player.charName, "[Fatality] Vayne")

		ove_0_6.vaynemenu:set("icon", player.iconSquare)
		ove_0_6.vaynemenu:dropdown("language", "Language", 1, {
			"English",
			"CN"
		})
		ove_0_6.vaynemenu.language:set("tooltip", "Please Reload | 请重新加载")

		local ove_0_56 = ove_0_6.vaynemenu.language:get()

		if ove_0_56 == 1 then
			ove_0_6.vaynemenu:menu("Combo", "Combo")
			ove_0_6.vaynemenu.Combo:menu("qsettings", "Tumble - [Q]")
			ove_0_6.vaynemenu.Combo.qsettings:set("icon", player:spellSlot(0).icon)
			ove_0_6.vaynemenu.Combo.qsettings:boolean("useq", "Enable Q in Combo", true)
			ove_0_6.vaynemenu.Combo.qsettings:boolean("qgap", "Enable Q Gap Close", true)
			ove_0_6.vaynemenu.Combo.qsettings:slider("wstacks", "W Stacks to Q", 1, 0, 2, 1)
			ove_0_6.vaynemenu.Combo:menu("esettings", "Condemn - [E]")
			ove_0_6.vaynemenu.Combo.esettings:set("icon", player:spellSlot(2).icon)
			ove_0_6.vaynemenu.Combo.esettings:dropdown("emode", "E Mode", 2, {
				"Old",
				"New"
			})
			ove_0_6.vaynemenu.Combo.esettings:boolean("usee", "Enable E in Combo", true)
			ove_0_6.vaynemenu.Combo:menu("rsettings", "Final Hour - [R]")
			ove_0_6.vaynemenu.Combo.rsettings:set("icon", player:spellSlot(3).icon)
			ove_0_6.vaynemenu.Combo.rsettings:boolean("user", "Enable R in Combo", false)
			ove_0_6.vaynemenu.Combo.rsettings:slider("minEne", "Min Enemies in Range To use R", 2, 1, 5, 1)
			ove_0_6.vaynemenu.Combo.rsettings:slider("rrange", "Target Scan Range for Enemies", 750, 0, 1500, 1)
			ove_0_6.vaynemenu:menu("Killsteal", "Killsteal")
			ove_0_6.vaynemenu.Killsteal:boolean("ksE", "Enable E in Killsteal", true)
			ove_0_6.vaynemenu:menu("Misc", "Misc")
			ove_0_6.vaynemenu.Misc:boolean("antigap", "Enable Anti Gapcloser", true)
			ove_0_6.vaynemenu.Misc:slider("antigaphp", "HP % to Anti Gapclose", 50, 1, 100, 1)
			ove_0_6.vaynemenu:menu("Draws", "Draws")
			ove_0_6.vaynemenu.Draws:dropdown("circlemode", "Circle Mode", 1, {
				"Default",
				"Rainbow"
			})
			ove_0_6.vaynemenu.Draws:boolean("drawQ", "Draw Q Range", true)
			ove_0_6.vaynemenu.Draws:boolean("drawE", "Draw E Range", true)
			ove_0_6.vaynemenu.Draws:boolean("drawAA", "Draw AA Tracker", true)
			ove_0_6.vaynemenu.Draws:boolean("drawGap", "Draw Gap Range", true)
			ove_0_6.vaynemenu.Draws:boolean("drawrbuff", "Draw R Time", true)
			ove_0_6.vaynemenu:menu("Keys", "Keys")
			ove_0_6.vaynemenu.Keys:keybind("stogglebar", "Combo Key", "Space", nil)
			ove_0_6.vaynemenu.Keys:keybind("qdive", "Enable Q Dive", nil, "T")
		end

		if ove_0_56 == 2 then
			ove_0_6.vaynemenu:menu("Combo", "Combo")
			ove_0_6.vaynemenu.Combo:menu("qsettings", "Tumble - [Q]")
			ove_0_6.vaynemenu.Combo.qsettings:set("icon", player:spellSlot(0).icon)
			ove_0_6.vaynemenu.Combo.qsettings:boolean("useq", "在组合中启用 Q", true)
			ove_0_6.vaynemenu.Combo.qsettings:boolean("qgap", "启用 Q 闭合缝隙", true)
			ove_0_6.vaynemenu.Combo.qsettings:slider("wstacks", "W 层数到 Q", 1, 0, 2, 1)
			ove_0_6.vaynemenu.Combo:menu("esettings", "Condemn - [E]")
			ove_0_6.vaynemenu.Combo.esettings:set("icon", player:spellSlot(2).icon)
			ove_0_6.vaynemenu.Combo.esettings:dropdown("emode", "E 模式", 2, {
				"旧",
				"新"
			})
			ove_0_6.vaynemenu.Combo.esettings:boolean("usee", "在组合中启用 E", true)
			ove_0_6.vaynemenu.Combo:menu("rsettings", "Final Hour - [R]")
			ove_0_6.vaynemenu.Combo.rsettings:set("icon", player:spellSlot(3).icon)
			ove_0_6.vaynemenu.Combo.rsettings:boolean("user", "在组合中启用 R", false)
			ove_0_6.vaynemenu.Combo.rsettings:slider("minEne", "使用 R 的最小范围敌人数量", 2, 1, 5, 1)
			ove_0_6.vaynemenu.Combo.rsettings:slider("rrange", "敌人的目标扫描范围", 750, 0, 1500, 1)
			ove_0_6.vaynemenu:menu("Killsteal", "Killsteal")
			ove_0_6.vaynemenu.Killsteal:boolean("ksE", "启用 E 抢人头", true)
			ove_0_6.vaynemenu:menu("Misc", "Misc")
			ove_0_6.vaynemenu.Misc:boolean("antigap", "启用防突进", true)
			ove_0_6.vaynemenu.Misc:slider("antigaphp", "生命值百分比至防突进", 50, 1, 100, 1)
			ove_0_6.vaynemenu:menu("Draws", "Draws")
			ove_0_6.vaynemenu.Draws:dropdown("circlemode", "圆形模式", 1, {
				"默认",
				"彩虹 "
			})
			ove_0_6.vaynemenu.Draws:boolean("drawQ", "绘制 Q 范围", true)
			ove_0_6.vaynemenu.Draws:boolean("drawE", "绘制 W 范围", true)
			ove_0_6.vaynemenu.Draws:boolean("drawAA", "绘制普攻追踪器", true)
			ove_0_6.vaynemenu.Draws:boolean("drawGap", "绘制突进范围", true)
			ove_0_6.vaynemenu.Draws:boolean("drawrbuff", "绘制 R 时间", true)
			ove_0_6.vaynemenu:menu("Keys", "Keys")
			ove_0_6.vaynemenu.Keys:keybind("stogglebar", "组合键", "Space", nil)
			ove_0_6.vaynemenu.Keys:keybind("qdive", "启用 Q 冲锋", nil, "T")
		end
	end

	if player.charName == "Xerath" then
		ove_0_6.xerathmenu = menu("Fatality " .. player.charName, "[Fatality] Xerath")

		ove_0_6.xerathmenu:set("icon", player.iconSquare)
		ove_0_6.xerathmenu:dropdown("language", "Language", 1, {
			"English",
			"CN"
		})
		ove_0_6.xerathmenu.language:set("tooltip", "Please Reload | 请重新加载")

		local ove_0_57 = ove_0_6.xerathmenu.language:get()

		if ove_0_57 == 1 then
			ove_0_6.xerathmenu:menu("Combo", "Combo")
			ove_0_6.xerathmenu.Combo:menu("qsettings", "Arcanopulse - [Q]")
			ove_0_6.xerathmenu.Combo.qsettings:set("icon", player:spellSlot(0).icon)
			ove_0_6.xerathmenu.Combo.qsettings:boolean("useq", "Enable Q in Combo", true)
			ove_0_6.xerathmenu.Combo.qsettings:boolean("waitforw", "Wait for W", true)
			ove_0_6.xerathmenu.Combo.qsettings:boolean("slowpredq", "Enable Q Slow Prediction", false)
			ove_0_6.xerathmenu.Combo.qsettings:boolean("overcharge", "Enable Q Overcharge", true)
			ove_0_6.xerathmenu.Combo.qsettings:slider("overchargevalue", "Overcharge Range", 200, 100, 350, 1)
			ove_0_6.xerathmenu.Combo:menu("wsettings", "Eye of Destruction - [W]")
			ove_0_6.xerathmenu.Combo.wsettings:set("icon", player:spellSlot(1).icon)
			ove_0_6.xerathmenu.Combo.wsettings:boolean("usew", "Enable W in Combo", true)
			ove_0_6.xerathmenu.Combo.wsettings:boolean("autow", "Auto W on Dash/CC", true)
			ove_0_6.xerathmenu.Combo.wsettings:boolean("slowpredw", "Enable W Slow Prediction", false)
			ove_0_6.xerathmenu.Combo:menu("esettings", "Shocking Orb - [E]")
			ove_0_6.xerathmenu.Combo.esettings:set("icon", player:spellSlot(2).icon)
			ove_0_6.xerathmenu.Combo.esettings:boolean("usee", "Enable E in Combo", true)
			ove_0_6.xerathmenu.Combo.esettings:boolean("autoe", "Auto E on Dash/CC", true)
			ove_0_6.xerathmenu.Combo.esettings:boolean("slowprede", "Enable E Slow Prediction", false)
			ove_0_6.xerathmenu.Combo:menu("rsettings", "Rite of the Arcane - [R]")
			ove_0_6.xerathmenu.Combo.rsettings:set("icon", player:spellSlot(3).icon)
			ove_0_6.xerathmenu.Combo.rsettings:dropdown("rmode", "R Mode", 2, {
				"Auto",
				"Tap Key"
			})
			ove_0_6.xerathmenu.Combo.rsettings:slider("cursorrange", "Mouse Range", 500, 300, 1000, 1)
			ove_0_6.xerathmenu.Combo.rsettings:boolean("slowpredr", "Enable R Slow Prediction", false)
			ove_0_6.xerathmenu:menu("Killsteal", "Killsteal")
			ove_0_6.xerathmenu.Killsteal:boolean("ksQ", "Enable Q Killsteal", true)
			ove_0_6.xerathmenu.Killsteal:boolean("ksW", "Enable W Killsteal", true)
			ove_0_6.xerathmenu.Killsteal:boolean("ksE", "Enable E Killsteal", true)
			ove_0_6.xerathmenu:menu("Clear", "Clear")
			ove_0_6.xerathmenu.Clear:header("Lane Clear", "Lane Clear")
			ove_0_6.xerathmenu.Clear:boolean("LcW", "Enable W Last Clear", true)
			ove_0_6.xerathmenu.Clear:header("Jungle Clear", "Jungle Clear")
			ove_0_6.xerathmenu.Clear:boolean("JcQ", "Enable Q in Jungle Clear", true)
			ove_0_6.xerathmenu.Clear:boolean("JcW", "Enable W in Jungle Clear", true)
			ove_0_6.xerathmenu:menu("Misc", "Misc")
			ove_0_6.xerathmenu.Misc:boolean("ag", "Enable Antigap Closer", true)
			ove_0_6.xerathmenu.Misc:slider("antigaphp", "HP % to Anti Gapclose", 50, 1, 100, 1)
			ove_0_6.xerathmenu:menu("Draws", "Draws")
			ove_0_6.xerathmenu.Draws:dropdown("circlemode", "Circle Mode", 1, {
				"Default",
				"Rainbow"
			})
			ove_0_6.xerathmenu.Draws:boolean("drawQ", "Draw Q Range", true)
			ove_0_6.xerathmenu.Draws:boolean("drawW", "Draw W Range", true)
			ove_0_6.xerathmenu.Draws:boolean("drawE", "Draw E Range", true)
			ove_0_6.xerathmenu.Draws:boolean("drawR", "Draw R Range", true)
			ove_0_6.xerathmenu.Draws:boolean("drawAA", "Draw AA Tracker", true)
			ove_0_6.xerathmenu.Draws:boolean("drawKill", "Draw Killable Text", true)
			ove_0_6.xerathmenu:menu("Keys", "Keys")
			ove_0_6.xerathmenu.Keys:keybind("stogglebar", "Combo Key", "Space", nil)
			ove_0_6.xerathmenu.Keys:keybind("clearkey", "Clear Key", "V", nil)
			ove_0_6.xerathmenu.Keys.clearkey:permashow(true)
			ove_0_6.xerathmenu.Keys:keybind("farmtoggle", "Enable Spell Farming", nil, "A")
			ove_0_6.xerathmenu.Keys:keybind("semiR", "R2 Tap Key", "T", "nil")
		end

		if ove_0_57 == 2 then
			ove_0_6.xerathmenu:menu("Combo", "Combo")
			ove_0_6.xerathmenu.Combo:menu("qsettings", "Arcanopulse - [Q]")
			ove_0_6.xerathmenu.Combo.qsettings:set("icon", player:spellSlot(0).icon)
			ove_0_6.xerathmenu.Combo.qsettings:boolean("useq", "在连招中启用 Q", true)
			ove_0_6.xerathmenu.Combo.qsettings:boolean("waitforw", "等待 W", true)
			ove_0_6.xerathmenu.Combo.qsettings:boolean("slowpredq", "启用 Q 缓慢预判", false)
			ove_0_6.xerathmenu.Combo.qsettings:boolean("overcharge", "启用 Q 过载", true)
			ove_0_6.xerathmenu.Combo.qsettings:slider("overchargevalue", "过载范围", 200, 100, 350, 1)
			ove_0_6.xerathmenu.Combo:menu("wsettings", "Eye of Destruction - [W]")
			ove_0_6.xerathmenu.Combo.wsettings:set("icon", player:spellSlot(1).icon)
			ove_0_6.xerathmenu.Combo.wsettings:boolean("usew", "在连招中启用 W", true)
			ove_0_6.xerathmenu.Combo.wsettings:boolean("autow", "在冲锋/控制效果时自动释放 W", true)
			ove_0_6.xerathmenu.Combo.wsettings:boolean("slowpredw", "启用 W 缓慢预判", false)
			ove_0_6.xerathmenu.Combo:menu("esettings", "Shocking Orb - [E]")
			ove_0_6.xerathmenu.Combo.esettings:set("icon", player:spellSlot(2).icon)
			ove_0_6.xerathmenu.Combo.esettings:boolean("usee", "在连招中启用 E", true)
			ove_0_6.xerathmenu.Combo.esettings:boolean("autoe", "在冲锋/控制效果时自动释放 E", true)
			ove_0_6.xerathmenu.Combo.esettings:boolean("slowprede", "启用 E 缓慢预判", false)
			ove_0_6.xerathmenu.Combo:menu("rsettings", "Rite of the Arcane - [R]")
			ove_0_6.xerathmenu.Combo.rsettings:set("icon", player:spellSlot(3).icon)
			ove_0_6.xerathmenu.Combo.rsettings:dropdown("rmode", "R 模式", 2, {
				"自动",
				"轻按键"
			})
			ove_0_6.xerathmenu.Combo.rsettings:slider("cursorrange", "鼠标范围", 500, 300, 1000, 1)
			ove_0_6.xerathmenu.Combo.rsettings:boolean("slowpredr", "启用 R 缓慢预判", false)
			ove_0_6.xerathmenu:menu("Killsteal", "Killsteal")
			ove_0_6.xerathmenu.Killsteal:boolean("ksQ", "启用 Q 抢人头", true)
			ove_0_6.xerathmenu.Killsteal:boolean("ksW", "启用 W 抢人头", true)
			ove_0_6.xerathmenu.Killsteal:boolean("ksE", "启用 E 抢人头", true)
			ove_0_6.xerathmenu:menu("Clear", "Clear")
			ove_0_6.xerathmenu.Clear:header("Lane Clear", "Lane Clear")
			ove_0_6.xerathmenu.Clear:boolean("LcW", "在清线时启用 W", true)
			ove_0_6.xerathmenu.Clear:header("Jungle Clear", "Jungle Clear")
			ove_0_6.xerathmenu.Clear:boolean("JcQ", "在清野时启用 Q", true)
			ove_0_6.xerathmenu.Clear:boolean("JcW", "在清野时启用 W", true)
			ove_0_6.xerathmenu:menu("Misc", "Misc")
			ove_0_6.xerathmenu.Misc:boolean("ag", "启用防突进", true)
			ove_0_6.xerathmenu.Misc:slider("antigaphp", "生命值百分比至防突进", 50, 1, 100, 1)
			ove_0_6.xerathmenu:menu("Draws", "Draws")
			ove_0_6.xerathmenu.Draws:dropdown("circlemode", "圆形模式", 1, {
				"默认",
				"彩虹"
			})
			ove_0_6.xerathmenu.Draws:boolean("drawQ", "绘制 Q 范围", true)
			ove_0_6.xerathmenu.Draws:boolean("drawW", "绘制 W 范围", true)
			ove_0_6.xerathmenu.Draws:boolean("drawE", "绘制 E 范围", true)
			ove_0_6.xerathmenu.Draws:boolean("drawR", "绘制 R 范围", true)
			ove_0_6.xerathmenu.Draws:boolean("drawAA", "绘制普攻追踪器", true)
			ove_0_6.xerathmenu.Draws:boolean("drawKill", "绘制可击杀文本", true)
			ove_0_6.xerathmenu:menu("Keys", "Keys")
			ove_0_6.xerathmenu.Keys:keybind("stogglebar", "组合键", "Space", nil)
			ove_0_6.xerathmenu.Keys:keybind("clearkey", "清理键", "V", nil)
			ove_0_6.xerathmenu.Keys.clearkey:permashow(true)
			ove_0_6.xerathmenu.Keys:keybind("farmtoggle", "启用法术收割", nil, "A")
			ove_0_6.xerathmenu.Keys:keybind("semiR", "R2 轻按键", "T", "nil")
		end
	end

	if player.charName == "Yasuo" then
		ove_0_6.yasuomenu = menu("Fatality " .. player.charName, "[Fatality] Yasuo")

		ove_0_6.yasuomenu:set("icon", player.iconSquare)
		ove_0_6.yasuomenu:dropdown("language", "Language", 1, {
			"English",
			"CN"
		})
		ove_0_6.yasuomenu.language:set("tooltip", "Please Reload | 请重新加载")

		local ove_0_58 = ove_0_6.yasuomenu.language:get()

		if ove_0_58 == 1 then
			ove_0_6.yasuomenu:menu("Combo", "Combo")
			ove_0_6.yasuomenu.Combo:menu("qsettings", "Steel Tempest - [Q]")
			ove_0_6.yasuomenu.Combo.qsettings:set("icon", player:spellSlot(0).icon)
			ove_0_6.yasuomenu.Combo.qsettings:boolean("useq", "Enable Q in Combo", true)
			ove_0_6.yasuomenu.Combo.qsettings:boolean("useq3", "Enable Q3 in Combo", true)
			ove_0_6.yasuomenu.Combo.qsettings:boolean("autoq", "Auto Q3 on Dashing Target", true)
			ove_0_6.yasuomenu.Combo.qsettings:boolean("qslowpred", "Enable Q Slow Prediction", false)
			ove_0_6.yasuomenu.Combo.qsettings:boolean("q3slowpred", "Enable Q3 Slow Prediction", false)
			ove_0_6.yasuomenu.Combo:menu("esettings", "Sweeping Blade - [E]")
			ove_0_6.yasuomenu.Combo.esettings:set("icon", player:spellSlot(2).icon)
			ove_0_6.yasuomenu.Combo.esettings:boolean("usee", "Enable E in Combo", true)
			ove_0_6.yasuomenu.Combo.esettings:boolean("egap", "Enable E Gap Close", true)
			ove_0_6.yasuomenu.Combo.esettings:slider("egaprange", "Gap Close Target Scan Range", 1000, 500, 1400, 1)
			ove_0_6.yasuomenu.Combo.esettings:slider("egapHP", "Your Min Health % to Gap Close", 20, 1, 100, 1)
			ove_0_6.yasuomenu.Combo:menu("rsettings", "Last Breath - [R]")
			ove_0_6.yasuomenu.Combo.rsettings:set("icon", player:spellSlot(3).icon)
			ove_0_6.yasuomenu.Combo.rsettings:boolean("user", "Enable R in Combo", true)
			ove_0_6.yasuomenu.Combo.rsettings:dropdown("rmode", "R Mode", 2, {
				"HP%",
				"Combo Killable"
			})
			ove_0_6.yasuomenu.Combo.rsettings:slider("minhp", "Min HP% to R in HP Mode", 50, 1, 100, 1)
			ove_0_6.yasuomenu.Combo.rsettings:slider("forcer", "Force R if X Targets are Knocked Up", 3, 1, 5, 1)
			ove_0_6.yasuomenu:menu("Killsteal", "Killsteal")
			ove_0_6.yasuomenu.Killsteal:boolean("ksQ", "Enable Q Killsteal", true)
			ove_0_6.yasuomenu.Killsteal:boolean("ksE", "Enable E Killsteal", true)
			ove_0_6.yasuomenu.Killsteal:boolean("ksR", "Enable R Killsteal", false)
			ove_0_6.yasuomenu:menu("Clear", "Clear")
			ove_0_6.yasuomenu.Clear:header("Lane Clear", "Lane Clear")
			ove_0_6.yasuomenu.Clear:boolean("LcQ", "Enable Q Last Clear", true)
			ove_0_6.yasuomenu.Clear:header("Jungle Clear", "Jungle Clear")
			ove_0_6.yasuomenu.Clear:boolean("JcQ", "Enable Q in Jungle Clear", true)
			ove_0_6.yasuomenu:menu("Misc", "Misc")
			ove_0_6.yasuomenu.Misc:boolean("AGQ3", "Enable Q3 Antigap Closer", true)
			ove_0_6.yasuomenu.Misc:slider("AGQHP", "HP % To Antigap Close", 50, 1, 100, 1)
			ove_0_6.yasuomenu.Misc:menu("wsettings", "W Settings")
			ove_0_6.yasuomenu.Misc.wsettings:boolean("enabledodge", "Enable W Spells Block", true)
			ove_0_6.yasuomenu.Misc.wsettings:slider("health", "Your Max Health % to use W", 50, 1, 100, 1)
			ove_0_6.yasuomenu.Misc.wsettings:menu("wtarget", "Target Spells")

			local ove_0_59 = objManager.enemies
			local ove_0_60 = objManager.enemies_n

			for iter_0_6 = 0, ove_0_60 - 1 do
				local ove_0_61 = ove_0_59[iter_0_6]

				if ove_0_61.charName == "Annie" then
					ove_0_6.yasuomenu.Misc.wsettings.wtarget:boolean("annieQ", "Dodge AnnieQ", true)
					ove_0_6.yasuomenu.Misc.wsettings.wtarget.annieQ:set("icon", ove_0_61:spellSlot(0).icon)
				end

				if ove_0_61.charName == "Brand" then
					ove_0_6.yasuomenu.Misc.wsettings.wtarget:boolean("brandR", "Dodge Brand R", true)
					ove_0_6.yasuomenu.Misc.wsettings.wtarget.brandR:set("icon", ove_0_61:spellSlot(3).icon)
				end

				if ove_0_61.charName == "Caitlyn" then
					ove_0_6.yasuomenu.Misc.wsettings.wtarget:boolean("caitlynR", "Dodge Caitlyn R", true)
					ove_0_6.yasuomenu.Misc.wsettings.wtarget.caitlynR:set("icon", ove_0_61:spellSlot(3).icon)
				end

				if ove_0_61.charName == "Fiddlesticks" then
					ove_0_6.yasuomenu.Misc.wsettings.wtarget:boolean("fiddlesticksQ", "Dodge Fiddlesticks Q", true)
					ove_0_6.yasuomenu.Misc.wsettings.wtarget.fiddlesticksQ:set("icon", ove_0_61:spellSlot(0).icon)
				end

				if ove_0_61.charName == "Lulu" then
					ove_0_6.yasuomenu.Misc.wsettings.wtarget:boolean("luluW", "Dodge Lulu W", true)
					ove_0_6.yasuomenu.Misc.wsettings.wtarget.luluW:set("icon", ove_0_61:spellSlot(2).icon)
				end

				if ove_0_61.charName == "Syndra" then
					ove_0_6.yasuomenu.Misc.wsettings.wtarget:boolean("syndraR", "Dodge Syndra R", true)
					ove_0_6.yasuomenu.Misc.wsettings.wtarget.syndraR:set("icon", ove_0_61:spellSlot(3).icon)
				end

				if ove_0_61.charName == "Teemo" then
					ove_0_6.yasuomenu.Misc.wsettings.wtarget:boolean("teemoQ", "Dodge Teemo Q", true)
					ove_0_6.yasuomenu.Misc.wsettings.wtarget.teemoQ:set("icon", ove_0_61:spellSlot(0).icon)
				end

				if ove_0_61.charName == "Tristana" then
					ove_0_6.yasuomenu.Misc.wsettings.wtarget:boolean("tristE", "Dodge Tristana E", true)
					ove_0_6.yasuomenu.Misc.wsettings.wtarget.tristE:set("icon", ove_0_61:spellSlot(2).icon)
					ove_0_6.yasuomenu.Misc.wsettings.wtarget:boolean("tristR", "Dodge Tristana R", true)
					ove_0_6.yasuomenu.Misc.wsettings.wtarget.tristR:set("icon", ove_0_61:spellSlot(3).icon)
				end

				if ove_0_61.charName == "Twistedfate" then
					ove_0_6.yasuomenu.Misc.wsettings.wtarget:boolean("twistedW", "Dodge Twisted Fate Stun Card", true)
					ove_0_6.yasuomenu.Misc.wsettings.wtarget.twistedW:set("icon", ove_0_61:spellSlot(1).icon)
				end

				if ove_0_61.charName == "Vayne" then
					ove_0_6.yasuomenu.Misc.wsettings.wtarget:boolean("vayneE", "Dodge Vayne E", true)
					ove_0_6.yasuomenu.Misc.wsettings.wtarget.vayneE:set("icon", ove_0_61:spellSlot(2).icon, true)
				end

				if ove_0_61.charName == "Veigar" then
					ove_0_6.yasuomenu.Misc.wsettings.wtarget:boolean("veigaR", "Dodge Veiga R", true)
					ove_0_6.yasuomenu.Misc.wsettings.wtarget.veigaR:set("icon", ove_0_61:spellSlot(3).icon, true)
				end
			end

			ove_0_6.yasuomenu:menu("Draws", "Draws")
			ove_0_6.yasuomenu.Draws:dropdown("circlemode", "Circle Mode", 1, {
				"Default",
				"Rainbow"
			})
			ove_0_6.yasuomenu.Draws:boolean("drawQ", "Draw Q Range", true)
			ove_0_6.yasuomenu.Draws:boolean("drawW", "Draw W Range", true)
			ove_0_6.yasuomenu.Draws:boolean("drawE", "Draw E Range", true)
			ove_0_6.yasuomenu.Draws:boolean("drawR", "Draw R Range", true)
			ove_0_6.yasuomenu.Draws:boolean("drawbuff", "Draw Buff Times", true)
			ove_0_6.yasuomenu.Draws:boolean("drawAA", "Draw AA Tracker", true)
			ove_0_6.yasuomenu:menu("Keys", "Keys")
			ove_0_6.yasuomenu.Keys:keybind("stogglebar", "Combo Key", "Space", nil)
			ove_0_6.yasuomenu.Keys:keybind("clearkey", "Clear Key", "V", nil)
			ove_0_6.yasuomenu.Keys.clearkey:permashow(true)
			ove_0_6.yasuomenu.Keys:keybind("farmtoggle", "Enable Spell Farming", nil, "A")
			ove_0_6.yasuomenu.Keys:keybind("divee", "Tower Dive E", nil, "T")
			ove_0_6.yasuomenu.Keys:keybind("diver", "Tower Dive R", nil, "Z")
			ove_0_6.yasuomenu.Keys:keybind("eqflash", "EQ Flash", "G", nil)
		end

		if ove_0_58 == 2 then
			ove_0_6.yasuomenu:menu("Combo", "连招")
			ove_0_6.yasuomenu.Combo:menu("qsettings", "斩钢闪 - [Q]")
			ove_0_6.yasuomenu.Combo.qsettings:set("icon", player:spellSlot(0).icon)
			ove_0_6.yasuomenu.Combo.qsettings:boolean("useq", "在连招中启用Q", true)
			ove_0_6.yasuomenu.Combo.qsettings:boolean("useq3", "在连招中启用Q3", true)
			ove_0_6.yasuomenu.Combo.qsettings:boolean("autoq", "对冲刺目标自动使用Q3", true)
			ove_0_6.yasuomenu.Combo.qsettings:boolean("qslowpred", "启用Q慢速预测", false)
			ove_0_6.yasuomenu.Combo.qsettings:boolean("q3slowpred", "启用Q3慢速预测", false)
			ove_0_6.yasuomenu.Combo:menu("esettings", "踏前斩 - [E]")
			ove_0_6.yasuomenu.Combo.esettings:set("icon", player:spellSlot(2).icon)
			ove_0_6.yasuomenu.Combo.esettings:boolean("usee", "在连招中启用E", true)
			ove_0_6.yasuomenu.Combo.esettings:boolean("egap", "启用E接近敌人", true)
			ove_0_6.yasuomenu.Combo.esettings:slider("egaprange", "接近目标扫描范围", 1000, 500, 1400, 1)
			ove_0_6.yasuomenu.Combo.esettings:slider("egapHP", "接近敌人的最低生命值百分比", 20, 1, 100, 1)
			ove_0_6.yasuomenu.Combo:menu("rsettings", "狂风绝息斩 - [R]")
			ove_0_6.yasuomenu.Combo.rsettings:set("icon", player:spellSlot(3).icon)
			ove_0_6.yasuomenu.Combo.rsettings:boolean("user", "在连招中启用R", true)
			ove_0_6.yasuomenu.Combo.rsettings:dropdown("rmode", "R模式", 2, {
				"生命值%",
				"连招可击杀"
			})
			ove_0_6.yasuomenu.Combo.rsettings:slider("minhp", "在生命值模式下R的最低生命值%", 50, 1, 100, 1)
			ove_0_6.yasuomenu.Combo.rsettings:slider("forcer", "如果X个目标被击飞, 则强制使用R", 3, 1, 5, 1)
			ove_0_6.yasuomenu:menu("Killsteal", "击杀")
			ove_0_6.yasuomenu.Killsteal:boolean("ksQ", "启用Q抢人头", true)
			ove_0_6.yasuomenu.Killsteal:boolean("ksE", "启用E抢人头", true)
			ove_0_6.yasuomenu.Killsteal:boolean("ksR", "启用R抢人头", false)
			ove_0_6.yasuomenu:menu("Clear", "清线")
			ove_0_6.yasuomenu.Clear:header("清线", "清线")
			ove_0_6.yasuomenu.Clear:boolean("LcQ", "启用Q清线", true)
			ove_0_6.yasuomenu.Clear:header("清野", "清野")
			ove_0_6.yasuomenu.Clear:boolean("JcQ", "启用Q清野", true)
			ove_0_6.yasuomenu:menu("Misc", "杂项")
			ove_0_6.yasuomenu.Misc:boolean("AGQ3", "启用Q3反突进", true)
			ove_0_6.yasuomenu.Misc:slider("AGQHP", "反突进的HP百分比", 50, 1, 100, 1)
			ove_0_6.yasuomenu.Misc:menu("wsettings", "风墙设置")
			ove_0_6.yasuomenu.Misc.wsettings:boolean("enabledodge", "开启风墙技能阻挡", true)
			ove_0_6.yasuomenu.Misc.wsettings:slider("health", "使用风墙的最大生命值百分比", 50, 1, 100, 1)
			ove_0_6.yasuomenu.Misc.wsettings:menu("wtarget", "目标技能")

			local ove_0_62 = objManager.enemies
			local ove_0_63 = objManager.enemies_n

			for iter_0_7 = 0, ove_0_63 - 1 do
				local ove_0_64 = ove_0_62[iter_0_7]

				if ove_0_64.charName == "Annie" then
					ove_0_6.yasuomenu.Misc.wsettings.wtarget:boolean("annieQ", "躲避 安妮Q", true)
					ove_0_6.yasuomenu.Misc.wsettings.wtarget.annieQ:set("icon", ove_0_64:spellSlot(0).icon)
				end

				if ove_0_64.charName == "Brand" then
					ove_0_6.yasuomenu.Misc.wsettings.wtarget:boolean("brandR", "躲避 布兰德R", true)
					ove_0_6.yasuomenu.Misc.wsettings.wtarget.brandR:set("icon", ove_0_64:spellSlot(3).icon)
				end

				if ove_0_64.charName == "Caitlyn" then
					ove_0_6.yasuomenu.Misc.wsettings.wtarget:boolean("caitlynR", "躲避 凯特琳R", true)
					ove_0_6.yasuomenu.Misc.wsettings.wtarget.caitlynR:set("icon", ove_0_64:spellSlot(3).icon)
				end

				if ove_0_64.charName == "Fiddlesticks" then
					ove_0_6.yasuomenu.Misc.wsettings.wtarget:boolean("fiddlesticksQ", "躲避 费德提克Q", true)
					ove_0_6.yasuomenu.Misc.wsettings.wtarget.fiddlesticksQ:set("icon", ove_0_64:spellSlot(0).icon)
				end

				if ove_0_64.charName == "Lulu" then
					ove_0_6.yasuomenu.Misc.wsettings.wtarget:boolean("luluW", "躲避 璐璐W", true)
					ove_0_6.yasuomenu.Misc.wsettings.wtarget.luluW:set("icon", ove_0_64:spellSlot(2).icon)
				end

				if ove_0_64.charName == "Syndra" then
					ove_0_6.yasuomenu.Misc.wsettings.wtarget:boolean("syndraR", "躲避 辛德拉R", true)
					ove_0_6.yasuomenu.Misc.wsettings.wtarget.syndraR:set("icon", ove_0_64:spellSlot(3).icon)
				end

				if ove_0_64.charName == "Teemo" then
					ove_0_6.yasuomenu.Misc.wsettings.wtarget:boolean("teemoQ", "躲避 提莫Q", true)
					ove_0_6.yasuomenu.Misc.wsettings.wtarget.teemoQ:set("icon", ove_0_64:spellSlot(0).icon)
				end

				if ove_0_64.charName == "Tristana" then
					ove_0_6.yasuomenu.Misc.wsettings.wtarget:boolean("tristE", "躲避 崔丝塔娜E", true)
					ove_0_6.yasuomenu.Misc.wsettings.wtarget.tristE:set("icon", ove_0_64:spellSlot(2).icon)
					ove_0_6.yasuomenu.Misc.wsettings.wtarget:boolean("tristR", "躲避 崔丝塔娜R", true)
					ove_0_6.yasuomenu.Misc.wsettings.wtarget.tristR:set("icon", ove_0_64:spellSlot(3).icon)
				end

				if ove_0_64.charName == "Twistedfate" then
					ove_0_6.yasuomenu.Misc.wsettings.wtarget:boolean("twistedW", "躲避 崔斯特眩晕卡", true)
					ove_0_6.yasuomenu.Misc.wsettings.wtarget.twistedW:set("icon", ove_0_64:spellSlot(1).icon)
				end

				if ove_0_64.charName == "Vayne" then
					ove_0_6.yasuomenu.Misc.wsettings.wtarget:boolean("vayneE", "躲避 薇恩E", true)
					ove_0_6.yasuomenu.Misc.wsettings.wtarget.vayneE:set("icon", ove_0_64:spellSlot(2).icon, true)
				end

				if ove_0_64.charName == "Veigar" then
					ove_0_6.yasuomenu.Misc.wsettings.wtarget:boolean("veigaR", "躲避 维迦R", true)
					ove_0_6.yasuomenu.Misc.wsettings.wtarget.veigaR:set("icon", ove_0_64:spellSlot(3).icon, true)
				end
			end

			ove_0_6.yasuomenu:menu("Draws", "显示设置")
			ove_0_6.yasuomenu.Draws:dropdown("circlemode", "圆圈模式", 1, {
				"默认",
				"彩虹"
			})
			ove_0_6.yasuomenu.Draws:boolean("drawQ", "显示Q范围", true)
			ove_0_6.yasuomenu.Draws:boolean("drawW", "显示W范围", true)
			ove_0_6.yasuomenu.Draws:boolean("drawE", "显示E范围", true)
			ove_0_6.yasuomenu.Draws:boolean("drawR", "显示R范围", true)
			ove_0_6.yasuomenu.Draws:boolean("drawbuff", "显示Buff时间", true)
			ove_0_6.yasuomenu.Draws:boolean("drawAA", "显示AA追踪", true)
			ove_0_6.yasuomenu:menu("Keys", "按键设置")
			ove_0_6.yasuomenu.Keys:keybind("stogglebar", "连招键", "Space", nil)
			ove_0_6.yasuomenu.Keys:keybind("clearkey", "清线键", "V", nil)
			ove_0_6.yasuomenu.Keys.clearkey:permashow(true)
			ove_0_6.yasuomenu.Keys:keybind("farmtoggle", "启用技能清线", nil, "A")
			ove_0_6.yasuomenu.Keys:keybind("divee", "塔下强杀E", nil, "T")
			ove_0_6.yasuomenu.Keys:keybind("diver", "塔下强杀R", nil, "Z")
			ove_0_6.yasuomenu.Keys:keybind("eqflash", "EQ闪现", "G", nil)
		end
	end

	if player.charName == "Zeri" then
		ove_0_6.zerimenu = menu("Fatality " .. player.charName, "[Fatality] Zeri")

		ove_0_6.zerimenu:set("icon", player.iconSquare)
		ove_0_6.zerimenu:dropdown("language", "Language", 1, {
			"English",
			"CN"
		})
		ove_0_6.zerimenu.language:set("tooltip", "Please Reload | 请重新加载")

		local ove_0_65 = ove_0_6.zerimenu.language:get()

		if ove_0_65 == 1 then
			ove_0_6.zerimenu:menu("Combo", "Combo")
			ove_0_6.zerimenu.Combo:menu("qsettings", "Burst Fire - [Q]")
			ove_0_6.zerimenu.Combo.qsettings:set("icon", player:spellSlot(0).icon)
			ove_0_6.zerimenu.Combo.qsettings:boolean("useq", "Enable Q in Combo", true)
			ove_0_6.zerimenu.Combo:menu("wsettings", "Ultrashock Laser - [W]")
			ove_0_6.zerimenu.Combo.wsettings:set("icon", player:spellSlot(1).icon)
			ove_0_6.zerimenu.Combo.wsettings:boolean("usew", "Enable W in Combo", true)
			ove_0_6.zerimenu.Combo.wsettings:boolean("noqrange", "Dont W if Target is in Q Range", true)
			ove_0_6.zerimenu.Combo:menu("esettings", "Spark Surge - [E]")
			ove_0_6.zerimenu.Combo.esettings:set("icon", player:spellSlot(2).icon)
			ove_0_6.zerimenu.Combo.esettings:boolean("usee", "Enable E in Combo", true)
			ove_0_6.zerimenu.Combo.esettings:slider("minhpslidervalue", "Min HP  % To Use E", 50, 1, 100, 1)
			ove_0_6.zerimenu.Combo.esettings:slider("maxene", "Max Enemies to E", 2, 1, 5, 1)
			ove_0_6.zerimenu.Combo.esettings:slider("scanrange", "Range to Check for Enemies", 750, 100, 1500, 1)
			ove_0_6.zerimenu.Combo.esettings.scanrange:set("tooltip", "It Checks From your Mouse Pos!!!")
			ove_0_6.zerimenu.Combo:menu("rsettings", "Lightning Crash - [R]")
			ove_0_6.zerimenu.Combo.rsettings:set("icon", player:spellSlot(3).icon)
			ove_0_6.zerimenu.Combo.rsettings:boolean("user", "Enable R in Combo", true)
			ove_0_6.zerimenu.Combo.rsettings:slider("minene", "Min Enemies in R Range", 2, 1, 5, 1)
			ove_0_6.zerimenu:menu("Killsteal", "Killsteal")
			ove_0_6.zerimenu.Killsteal:boolean("ksQ", "Enable Q Killsteal", true)
			ove_0_6.zerimenu.Killsteal:boolean("ksW", "Enable W Killsteal", true)
			ove_0_6.zerimenu.Killsteal:boolean("ksR", "Enable R Killsteal", true)
			ove_0_6.zerimenu:menu("Clear", "Clear")
			ove_0_6.zerimenu.Clear:header("Lane Clear", "Lane Clear")
			ove_0_6.zerimenu.Clear:boolean("LcQ", "Enable Q Last Clear", true)
			ove_0_6.zerimenu.Clear:header("Jungle Clear", "Jungle Clear")
			ove_0_6.zerimenu.Clear:boolean("JcQ", "Enable Q in Jungle Clear", true)
			ove_0_6.zerimenu.Clear:boolean("JcW", "Enable W in Jungle Clear", true)
			ove_0_6.zerimenu:menu("Misc", "Misc")
			ove_0_6.zerimenu.Misc:boolean("AGE", "Enable E Antigap Closer", true)
			ove_0_6.zerimenu.Misc:slider("AGEHP", "HP % To Antigap Close", 50, 1, 100, 1)
			ove_0_6.zerimenu:menu("Draws", "Draws")
			ove_0_6.zerimenu.Draws:dropdown("circlemode", "Circle Mode", 1, {
				"Default",
				"Rainbow"
			})
			ove_0_6.zerimenu.Draws:boolean("drawQ", "Draw Q Range", true)
			ove_0_6.zerimenu.Draws:boolean("drawW", "Draw W Range", true)
			ove_0_6.zerimenu.Draws:boolean("drawE", "Draw E Range", true)
			ove_0_6.zerimenu.Draws:boolean("drawR", "Draw R Range", true)
			ove_0_6.zerimenu.Draws:boolean("drawjump", "Draw Jump Spots", true)
			ove_0_6.zerimenu.Draws:boolean("drawbuff", "Draw Buff Times", true)
			ove_0_6.zerimenu:menu("Keys", "Keys")
			ove_0_6.zerimenu.Keys:keybind("stogglebar", "Combo Key", "Space", nil)
			ove_0_6.zerimenu.Keys:keybind("clearkey", "Clear Key", "V", nil)
			ove_0_6.zerimenu.Keys.clearkey:permashow(true)
			ove_0_6.zerimenu.Keys:keybind("farmtoggle", "Enable Spell Farming", nil, "A")
			ove_0_6.zerimenu.Keys:keybind("semiw", "Semi W", "T", nil)
			ove_0_6.zerimenu.Keys:keybind("disableaa", "Only AA When Passive is Ready", nil, "G")
			ove_0_6.zerimenu.Keys:keybind("jumpkey", "E Wall Jump", "H", nil)
		end

		if ove_0_65 == 2 then
			core.reload()
			ove_0_6.zerimenu:menu("Combo", "Combo")
			ove_0_6.zerimenu.Combo:menu("qsettings", "Burst Fire - [Q]")
			ove_0_6.zerimenu.Combo.qsettings:set("icon", player:spellSlot(0).icon)
			ove_0_6.zerimenu.Combo.qsettings:boolean("useq", "在组合中启用 Q", true)
			ove_0_6.zerimenu.Combo:menu("wsettings", "Ultrashock Laser - [W]")
			ove_0_6.zerimenu.Combo.wsettings:set("icon", player:spellSlot(1).icon)
			ove_0_6.zerimenu.Combo.wsettings:boolean("usew", "在组合中启用 W", true)
			ove_0_6.zerimenu.Combo.wsettings:boolean("noqrange", "如果目标在 Q 范围内，请不要使用 W", true)
			ove_0_6.zerimenu.Combo:menu("esettings", "Spark Surge - [E]")
			ove_0_6.zerimenu.Combo.esettings:set("icon", player:spellSlot(2).icon)
			ove_0_6.zerimenu.Combo.esettings:boolean("usee", "在组合中启用 E", true)
			ove_0_6.zerimenu.Combo.esettings:slider("minhpslidervalue", "使用 E 的最低生命值百分比", 50, 1, 100, 1)
			ove_0_6.zerimenu.Combo.esettings:slider("maxene", "E 的最大敌人数量", 2, 1, 5, 1)
			ove_0_6.zerimenu.Combo.esettings:slider("scanrange", "检查敌人的范围", 750, 100, 1500, 1)
			ove_0_6.zerimenu.Combo.esettings.scanrange:set("tooltip", "它从你的鼠标位置检查!!!")
			ove_0_6.zerimenu.Combo:menu("rsettings", "Lightning Crash - [R]")
			ove_0_6.zerimenu.Combo.rsettings:set("icon", player:spellSlot(3).icon)
			ove_0_6.zerimenu.Combo.rsettings:boolean("user", "在组合中启用 R", true)
			ove_0_6.zerimenu.Combo.rsettings:slider("minene", "在 R 范围内的最小敌人数量", 2, 1, 5, 1)
			ove_0_6.zerimenu:menu("Killsteal", "Killsteal")
			ove_0_6.zerimenu.Killsteal:boolean("ksQ", "启用 Q 抢人头", true)
			ove_0_6.zerimenu.Killsteal:boolean("ksW", "启用 W 抢人头", true)
			ove_0_6.zerimenu.Killsteal:boolean("ksR", "启用 R 抢人头l", true)
			ove_0_6.zerimenu:menu("Clear", "Clear")
			ove_0_6.zerimenu.Clear:header("Lane Clear", "Lane Clear")
			ove_0_6.zerimenu.Clear:boolean("LcQ", "启用 Q 最后清理", true)
			ove_0_6.zerimenu.Clear:header("Jungle Clear", "Jungle Clear")
			ove_0_6.zerimenu.Clear:boolean("JcQ", "在清野时启用 Q", true)
			ove_0_6.zerimenu.Clear:boolean("JcW", "在清野时启用 W", true)
			ove_0_6.zerimenu:menu("Misc", "Misc")
			ove_0_6.zerimenu.Misc:boolean("AGE", "启用 E 防突进", true)
			ove_0_6.zerimenu.Misc:slider("AGEHP", "生命值百分比至防突进", 50, 1, 100, 1)
			ove_0_6.zerimenu:menu("Draws", "Draws")
			ove_0_6.zerimenu.Draws:dropdown("circlemode", "圆形模式", 1, {
				"默认",
				"彩虹 "
			})
			ove_0_6.zerimenu.Draws:boolean("drawQ", "绘制 Q 范围", true)
			ove_0_6.zerimenu.Draws:boolean("drawW", "绘制 W 范围", true)
			ove_0_6.zerimenu.Draws:boolean("drawE", "绘制 E 范围", true)
			ove_0_6.zerimenu.Draws:boolean("drawR", "绘制 R 范围", true)
			ove_0_6.zerimenu.Draws:boolean("drawjump", "绘制跳跃点", true)
			ove_0_6.zerimenu.Draws:boolean("drawbuff", "绘制增益持续时间", true)
			ove_0_6.zerimenu:menu("Keys", "Keys")
			ove_0_6.zerimenu.Keys:keybind("stogglebar", "组合键", "Space", nil)
			ove_0_6.zerimenu.Keys:keybind("clearkey", "清除键", "V", nil)
			ove_0_6.zerimenu.Keys.clearkey:permashow(true)
			ove_0_6.zerimenu.Keys:keybind("farmtoggle", "启用法术打野", nil, "A")
			ove_0_6.zerimenu.Keys:keybind("semiw", "半自动 W", "T", nil)
			ove_0_6.zerimenu.Keys:keybind("disableaa", "只有在被动准备好时进行普攻", nil, "G")
			ove_0_6.zerimenu.Keys:keybind("jumpkey", "E 技能墙壁跳跃", "H", nil)
		end
	end
	--local menu = module.load("menu")
	--local ove_0_7 = module.load(header.id, "Utility/menu0")
		--if player.charName == "Aurora" then
		--local ove_0_7 = menu("Aurora", "[Brian] " .. player.charName)

		local ove_0_7 = menu("Brian " .. player.charName)

		--ove_0_7.auroramenu:set("icon", player.iconSquare)
		--ove_0_7.auroramenu.language:set("tooltip", "Please Reload | 请重新加载")
		--ove_0_7.auroramenu:dropdown("language", "Language", 1, {
			--"English",
			--"CN"
		--})
		--local ove_0_66 = ove_0_7.auroramenu.language:get()
		--if ove_0_66 == 1 then
		--core.reload()
					--ove_0_6.khazixmenu:menu("Combo", "Combo")
			--ove_0_6.khazixmenu.Combo:menu("qsettings", "Taste Their Fear - [Q]")
		ove_0_7.auroramenu:menu("Combo", "Combo")
ove_0_7.auroramenu.Combo:menu("qsettings", "冰雪风暴 - [Q]")
ove_0_7.auroramenu.Combo.qsettings:set("icon", player:spellSlot(0).icon)
ove_0_7.auroramenu.Combo.qsettings:boolean("useq", "在组合中启用 Q", true)
ove_0_7.auroramenu.Combo.qsettings:boolean("slowpredq", "启用 Q 缓慢预判", false)
ove_0_7.auroramenu.Combo.qsettings:boolean("noaa", "如果目标在普攻范围内，请不要使用 Q", true)
ove_0_7.auroramenu.Combo.qsettings:boolean("autoq", "如果目标被眩晕，请自动使用 Q", true)

ove_0_7.auroramenu.Combo:menu("wsettings", "寒冰陷阱 - [W]")
ove_0_7.auroramenu.Combo.wsettings:set("icon", player:spellSlot(1).icon)
ove_0_7.auroramenu.Combo.wsettings:boolean("autow", "在眩晕的目标上自动释放 W", true)

ove_0_7.auroramenu.Combo:menu("esettings", "冰霜护盾 - [E]")
ove_0_7.auroramenu.Combo.esettings:set("icon", player:spellSlot(2).icon)
ove_0_7.auroramenu.Combo.esettings:boolean("usee", "在组合中启用 E", true)
ove_0_7.auroramenu.Combo.esettings:boolean("slowprede", "启用 E 缓慢预判", false)

ove_0_7.auroramenu.Combo:menu("rsettings", "冰封领域 - [R]")
ove_0_7.auroramenu.Combo.rsettings:set("icon", player:spellSlot(3).icon)
ove_0_7.auroramenu.Combo.rsettings:keybind("semiR", "如果目标可击杀，则半自动释放 R", "T", nil)

-- 抢人头设置
ove_0_7.auroramenu:menu("Killsteal", "Killsteal")
ove_0_7.auroramenu.Killsteal:boolean("ksQ", "启用 Q 抢人头", true)
ove_0_7.auroramenu.Killsteal:boolean("ksR", "启用 R 抢人头", true)

-- 清理设置
ove_0_7.auroramenu:menu("Clear", "Clear")
ove_0_7.auroramenu.Clear:header("laneclear", "Lane Clear")
ove_0_7.auroramenu.Clear:boolean("LcQ", "在清线时启用 Q", true)
ove_0_7.auroramenu.Clear:header("jungleclear", "Jungle Clear")
ove_0_7.auroramenu.Clear:boolean("JcQ", "在清野时启用 Q", true)

-- Misc 设置
ove_0_7.auroramenu:menu("Misc", "Misc")
ove_0_7.auroramenu.Misc:boolean("AGE", "启用 E 护体", true)
ove_0_7.auroramenu.Misc:boolean("AGW", "启用 W 护体", true)
ove_0_7.auroramenu.Misc:slider("agh", "血量百分比至防突进", 50, 1, 100, 1)

-- 绘制设置
ove_0_7.auroramenu:menu("Draws", "Draws")
ove_0_7.auroramenu.Draws:boolean("drawq", "绘制 Q 范围", true)
ove_0_7.auroramenu.Draws:boolean("draww", "绘制 W 范围", true)
ove_0_7.auroramenu.Draws:boolean("drawe", "绘制 E 范围", true)
ove_0_7.auroramenu.Draws:boolean("drawr", "绘制 R 范围", true)
ove_0_7.auroramenu.Draws:boolean("drawAA", "绘制普攻追踪器", true)
ove_0_7.auroramenu.Draws:boolean("drawrkill", "绘制可击杀目标的 R 范围", true)

-- 快捷键设置
ove_0_7.auroramenu:menu("Keys", "Keys")
ove_0_7.auroramenu.Keys:keybind("stogglebar", "组合键", "Space", nil)
ove_0_7.auroramenu.Keys:keybind("farmtoggle", "启用法术收割", nil, "A")
ove_0_7.auroramenu.Keys:keybind("clearkey", "清理键", "V", nil)
--end
--end
--end
return ove_0_6

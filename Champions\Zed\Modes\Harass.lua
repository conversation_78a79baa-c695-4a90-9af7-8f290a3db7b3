

local ove_0_10 = module.load("<PERSON>", "<PERSON><PERSON>/MyC<PERSON><PERSON>")
local ove_0_11 = module.load("<PERSON>", "Champions/Zed/Spells/R")
local ove_0_12 = module.load("<PERSON>", "Champions/Zed/Spells/W")
local ove_0_13 = module.load("<PERSON>", "Champions/Zed/Spells/Q")
local ove_0_14 = module.load("<PERSON>", "Champions/Zed/Spells/E")
local ove_0_15 = module.load("<PERSON>", "Champions/Zed/Util/menu")
local ove_0_16 = module.load("<PERSON>", "Champions/Zed/Util/Detection")
local ove_0_17 = module.load("<PERSON>", "Lib/DelayAction")
local ove_0_18 = player
local ove_0_19 = {
	Execute = function(arg_5_0)
		-- print 5
		if ove_0_10.IsValidTarget(arg_5_0) and ove_0_10.<PERSON>a() >= ove_0_15.harass.mana:get() then
			local slot_5_0 = ove_0_18.mana >= ove_0_13.Cost() + ove_0_12.Cost() + ove_0_14.Cost() and ove_0_14.Ready() and ove_0_13.Ready() and ove_0_12.Ready()

			if ove_0_15.harass.useW:get() and slot_5_0 then
				if ove_0_10.Dist(arg_5_0) <= ove_0_12.range then
					ove_0_12.Cast1(arg_5_0.pos)
				elseif ove_0_10.Dist(arg_5_0) > ove_0_12.range and ove_0_10.Dist(arg_5_0) < ove_0_12.range + ove_0_13.range / 2 then
					local slot_5_1 = ove_0_18.pos + (arg_5_0.pos - ove_0_18.pos):norm() * 925

					ove_0_12.Cast1(slot_5_1)
				end
			end

			if ove_0_15.harass.useQ:get() then
				ove_0_17.Cast(function()
					-- print 6
					ove_0_13.Cast(arg_5_0)
				end, 0.5)
			end

			if ove_0_15.harass.useE:get() then
				ove_0_14.Cast(arg_5_0)
			end
		end
	end
}

function ove_0_19.ExecuteAuto(arg_7_0)
	-- print 7
	if ove_0_10.IsValidTarget(arg_7_0) then
		if ove_0_15.harass.autoharass.useQ:get() then
			ove_0_19.AutoQ(arg_7_0)
		end

		if ove_0_15.harass.autoharass.useE:get() then
			ove_0_19.AutoE(arg_7_0)
		end

		if ove_0_15.harass.autoharass.use:get() then
			ove_0_19.AutoHarassF(arg_7_0)
		end
	end
end

function ove_0_19.AutoQ(arg_8_0)
	-- print 8
	if ove_0_10.Dist(arg_8_0) <= ove_0_13.range then
		ove_0_13.Cast(arg_8_0)
	end
end

function ove_0_19.AutoE(arg_9_0)
	-- print 9
	ove_0_14.Cast(arg_9_0)
end

function ove_0_19.AutoHarassF(arg_10_0)
	-- print 10
	if ove_0_10.IsValidTarget(arg_10_0) and ove_0_10.Mana() >= ove_0_15.harass.mana:get() then
		local slot_10_0 = ove_0_18.pos + (arg_10_0.pos - ove_0_18.pos):norm() * 700

		if ove_0_15.harass.autoharass.harasslogic:get() == 1 then
			if ove_0_13.Ready() and ove_0_12.Ready() and ove_0_18.mana > ove_0_13.Cost() + ove_0_12.Cost() then
				if ove_0_15.harass.useW:get() then
					if ove_0_10.Dist(arg_10_0) <= ove_0_12.range then
						ove_0_12.Cast1(arg_10_0.pos)
					elseif ove_0_10.Dist(arg_10_0) > ove_0_12.range and ove_0_10.Dist(arg_10_0) < ove_0_12.range + ove_0_13.range / 2 then
						ove_0_12.Cast1(slot_10_0)
					end
				end

				if (ove_0_16.Wpos or ove_0_12.IsW2()) and ove_0_13.Ready() and ove_0_15.harass.useQ:get() then
					if ove_0_10.Dist(arg_10_0) < ove_0_13.range then
						ove_0_17.Cast(function()
							-- print 11
							ove_0_13.Cast(arg_10_0)
						end, 0.5)
					elseif ove_0_18.pos:dist(arg_10_0.pos) > ove_0_12.range and ove_0_10.Dist(arg_10_0) < ove_0_12.range + ove_0_13.range / 2 then
						ove_0_17.Cast(function()
							-- print 12
							ove_0_13.Cast(arg_10_0)
						end, 0.5)
					end
				end
			end
		elseif ove_0_15.harass.autoharass.harasslogic:get() == 2 then
			if ove_0_13.Ready() and ove_0_12.Ready() and ove_0_14.Ready() and ove_0_18.mana > ove_0_13.Cost() + ove_0_12.Cost() + ove_0_14.Cost() then
				if ove_0_10.Dist(arg_10_0) < ove_0_12.range and ove_0_15.harass.useW:get() then
					ove_0_12.Cast1(slot_10_0)
				end

				if (ove_0_16.Wpos or ove_0_12.IsW2()) and ove_0_10.Dist(arg_10_0) < ove_0_13.range and ove_0_15.harass.useQ:get() then
					ove_0_17.Cast(function()
						-- print 13
						ove_0_13.Cast(arg_10_0)
					end, 0.5)
				end

				if ove_0_15.harass.useE:get() then
					ove_0_14.Cast(arg_10_0)
				end
			end
		elseif ove_0_15.harass.autoharass.harasslogic:get() == 3 and ove_0_14.Ready() and ove_0_12.Ready() and ove_0_18.mana > ove_0_12.Cost() + ove_0_14.Cost() then
			if ove_0_10.Dist(arg_10_0) < ove_0_12.range and ove_0_15.harass.useW:get() then
				ove_0_12.Cast1(slot_10_0)
			end

			if ove_0_15.harass.useE:get() then
				ove_0_14.Cast(arg_10_0)
			end
		end
	end
end

return ove_0_19

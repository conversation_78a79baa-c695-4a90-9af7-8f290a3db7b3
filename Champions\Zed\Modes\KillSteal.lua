

local ove_0_10 = module.load("<PERSON>", "Champions/Zed/Spells/R")
local ove_0_11 = module.load("<PERSON>", "Champions/Zed/Spells/W")
local ove_0_12 = module.load("<PERSON>", "Champions/Zed/Spells/Q")
local ove_0_13 = module.load("<PERSON>", "Champions/Zed/Spells/E")
local ove_0_14 = module.load("<PERSON>", "Lib/Summoners")
local ove_0_15 = module.load("<PERSON>", "Champions/Zed/Util/menu")
local ove_0_16 = module.load("<PERSON>", "<PERSON>b/MyCommon")
local ove_0_17 = module.load("<PERSON>", "Champions/Zed/Util/Detection")
local ove_0_18 = player
local ove_0_19 = module.load("<PERSON>", "Champions/Zed/Util/Damage")
local ove_0_20 = module.load("<PERSON>", "Champions/Zed/Modes/Misc")
local ove_0_21 = module.load("<PERSON>", "<PERSON>b/DelayAction")
local ove_0_22
local ove_0_23

return {
	Execute = function()
		-- print 5
		if not ove_0_15.killsteal.use:get() then
			return
		end

		for iter_5_0, iter_5_1 in pairs(ove_0_16.GetEnemyHeroes()) do
			if ove_0_16.IsValidTarget(iter_5_1) then
				local slot_5_0 = iter_5_1.health
				local slot_5_1 = ove_0_19.Get("E", iter_5_1)
				local slot_5_2 = ove_0_19.Get("Q", iter_5_1)
				local slot_5_3 = 50 + ove_0_18.levelRef * 20

				if ove_0_15.killsteal.useE:get() and slot_5_0 < slot_5_1 and ove_0_16.Dist(iter_5_1) < ove_0_13.range then
					ove_0_13.Cast(iter_5_1)
				end

				if ove_0_15.killsteal.useQ:get() and slot_5_0 < slot_5_2 and ove_0_16.Dist(iter_5_1) < ove_0_12.range then
					ove_0_12.Cast(iter_5_1)
				end

				if ove_0_14.Ignite.real and ove_0_15.killsteal.useI:get() and slot_5_0 < slot_5_3 and ove_0_16.Dist(iter_5_1) < ove_0_14.Ignite.range then
					ove_0_14.CastI(iter_5_1)
				end

				if ove_0_17.Wpos then
					if ove_0_15.killsteal.useE:get() and slot_5_0 < slot_5_1 and iter_5_1.pos:dist(ove_0_17.Wpos) < ove_0_13.range then
						ove_0_13.Cast(iter_5_1)
					end

					if ove_0_15.killsteal.useQ:get() and slot_5_0 < slot_5_2 and iter_5_1.pos:dist(ove_0_17.Wpos) < ove_0_12.range then
						ove_0_12.Cast(iter_5_1)
					end
				end

				if ove_0_17.Rpos then
					if ove_0_15.killsteal.useE:get() and slot_5_0 < slot_5_1 and iter_5_1.pos:dist(ove_0_17.Rpos) < ove_0_13.range then
						ove_0_13.Cast(iter_5_1)
					end

					if ove_0_15.killsteal.useQ:get() and slot_5_0 < slot_5_2 and iter_5_1.pos:dist(ove_0_17.Rpos) < ove_0_12.range then
						ove_0_12.Cast(iter_5_1)
					end
				end

				ove_0_20.AutoE(iter_5_1)
			end
		end
	end,
	OneShot = function(arg_6_0)
		-- print 6
		if not arg_6_0 or arg_6_0.isDead then
			return
		end

		if ove_0_17.Rpos then
			ove_0_22 = arg_6_0.pos + (arg_6_0.pos - ove_0_17.Rpos):norm() * 450
		end

		local slot_6_0 = ove_0_19.Get("Q", arg_6_0)
		local slot_6_1 = ove_0_19.Get("E", arg_6_0)
		local slot_6_2 = ove_0_19.Get("R", arg_6_0)
		local slot_6_3 = ove_0_19.AA(arg_6_0)
		local slot_6_4 = 50 + ove_0_18.levelRef * 20

		ove_0_23 = slot_6_0 + slot_6_1 + slot_6_2 + slot_6_3

		if ove_0_14.Ignite.real then
			ove_0_23 = ove_0_23 + slot_6_4
		end

		if ove_0_23 > arg_6_0.health and ove_0_15.killsteal.oneshot.oto:get() and ove_0_18.mana > ove_0_13.Cost() + ove_0_12.Cost() + ove_0_11.Cost() + ove_0_10.Cost() and ove_0_12.Ready() and ove_0_11.Ready() and ove_0_13.Ready() and ove_0_10.Ready() and ove_0_20.blacklistcheck2(arg_6_0) then
			if ove_0_16.Dist(arg_6_0) <= ove_0_10.range then
				ove_0_10.Cast(arg_6_0)
			end

			if ove_0_17.Rpos and ove_0_16.Dist(arg_6_0) <= ove_0_11.range then
				ove_0_21.Cast(function()
					-- print 7
					ove_0_11.Cast1(ove_0_22)
				end, 0.2)
			end

			if ove_0_17.Wpos and ove_0_16.Dist(arg_6_0) <= ove_0_12.range then
				ove_0_21.Cast(function()
					-- print 8
					ove_0_12.Cast(arg_6_0)
				end, 0.11)
			end

			if not ove_0_12.Ready() and ove_0_17.Wpos then
				ove_0_13.Cast(arg_6_0)
			end

			if ove_0_14.Ignite.real and ove_0_16.Dist(arg_6_0) < ove_0_14.Ignite.range then
				ove_0_14.CastI(arg_6_0)
			end
		end
	end
}

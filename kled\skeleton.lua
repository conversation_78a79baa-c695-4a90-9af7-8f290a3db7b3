
local ove_0_10 = module.internal("TS")
local ove_0_11 = module.internal("orb")
local ove_0_12 = module.internal("pred")


local ove_0_16 = module.load("<PERSON>", "kled/menucn")
local ove_0_17 = module.load("<PERSON>", "kled/misc")
local ove_0_18
local ove_0_19

ove_0_19 = graphics.width <= 1920 and graphics.height <= 1080 or false

local ove_0_20 = {}
local ove_0_21 = {
	300,
	350,
	400,
	450,
	500
}
local ove_0_22 = {
	speed = 1600,
	delay = 0,
	boundingRadiusMod = 0,
	width = 60,
	collision = {
		minion = false,
		hero = false
	}
}
local ove_0_23 = {
	speed = 1600,
	delay = 0,
	boundingRadiusMod = 0,
	width = 60,
	collision = {
		minion = true,
		hero = true
	}
}
local ove_0_24 = {
	speed = 3000,
	delay = 0.25,
	boundingRadiusMod = 0,
	width = 100,
	collision = {
		minion = false,
		hero = false
	}
}
local ove_0_25 = {
	speed = 3000,
	delay = 0.25,
	boundingRadiusMod = 0,
	width = 100,
	collision = {
		minion = false,
		hero = false
	}
}
local ove_0_26 = {
	speed = 1000,
	delay = 0,
	boundingRadiusMod = 0,
	width = 100,
	collision = {
		minion = false,
		hero = false
	}
}

local function ove_0_27()
	if player:spellSlot(0).name == "KledQ" then
		return true
	else
		return false
	end
end

local function ove_0_28(arg_6_0, arg_6_1, arg_6_2)
	if arg_6_2 > 2500 then
		return
	end

	if arg_6_1 and arg_6_1.isVisible and arg_6_2 and arg_6_1.x and arg_6_1.y and arg_6_1.z and arg_6_1.path then
		if arg_6_1 and arg_6_1.buff.kledetarget and player:spellSlot(2).state == 0 and player:spellSlot(2).name ~= "KledE" then
			arg_6_0.obj = ove_0_11.combat.target

			return true
		elseif ove_0_11.combat.target then
			arg_6_0.obj = ove_0_11.combat.target

			return true
		elseif player:spellSlot(0).state == 0 and arg_6_2 and arg_6_2 < 750 then
			if ove_0_27() then
				local slot_6_0 = ove_0_12.linear.get_prediction(ove_0_22, arg_6_1)

				if slot_6_0 and slot_6_0.endPos and slot_6_0.startPos:dist(slot_6_0.endPos) < 750 then
					arg_6_0.obj = arg_6_1

					return true
				end
			else
				local slot_6_1 = ove_0_12.linear.get_prediction(ove_0_24, arg_6_1)

				if slot_6_1 and slot_6_1.endPos and slot_6_1.startPos:dist(slot_6_1.endPos) < 750 then
					arg_6_0.obj = arg_6_1

					return true
				end
			end
		elseif arg_6_1 and arg_6_1.buff.kledqmark and arg_6_2 and arg_6_2 < 600 then
			arg_6_0.obj = arg_6_1

			return true
		elseif arg_6_1 and player:spellSlot(2).state == 0 and player:spellSlot(2).name == "KledE" then
			if player:spellSlot(0).state == 0 and arg_6_2 and arg_6_2 < 600 then
				arg_6_0.obj = arg_6_1

				return true
			elseif arg_6_1.buff.kledqmark and arg_6_2 and arg_6_2 < 600 then
				arg_6_0.obj = arg_6_1

				return true
			elseif arg_6_2 and arg_6_2 < 600 then
				arg_6_0.obj = arg_6_1

				return true
			end
		end
	end
end

local function ove_0_29()
	return ove_0_11.ts.get_result(ove_0_28).obj
end

local function ove_0_30(arg_8_0)
	if not arg_8_0 and ove_0_11.core.is_move_paused() then
		ove_0_11.core.set_pause_move(0)
	elseif arg_8_0 then
		ove_0_11.core.set_pause_move(player:basicAttack(0).clientAnimationTime)
		player:move(vec3(arg_8_0))
	end
end

local function ove_0_31(arg_9_0)
	if arg_9_0 and arg_9_0.spell and arg_9_0.spell.owner and arg_9_0.spell.owner.team ~= TEAM_ALLY and arg_9_0.spell.name == "YasuoW_VisualMis" then
		ove_0_20[arg_9_0.ptr] = arg_9_0
	end
end

local function ove_0_32(arg_10_0)
	if arg_10_0 and arg_10_0.ptr and ove_0_20[arg_10_0.ptr] then
		ove_0_20[arg_10_0.ptr] = nil
	end
end

local function ove_0_33(arg_11_0, arg_11_1, arg_11_2)
	return (arg_11_2.y - arg_11_0.y) * (arg_11_1.x - arg_11_0.x) - (arg_11_1.y - arg_11_0.y) * (arg_11_2.x - arg_11_0.x)
end

local function ove_0_34(arg_12_0, arg_12_1, arg_12_2, arg_12_3)
	return (ove_0_33(arg_12_0, arg_12_2, arg_12_3) <= 0 and ove_0_33(arg_12_1, arg_12_2, arg_12_3) > 0 or ove_0_33(arg_12_0, arg_12_2, arg_12_3) > 0 and ove_0_33(arg_12_1, arg_12_2, arg_12_3) <= 0) and (ove_0_33(arg_12_0, arg_12_1, arg_12_2) <= 0 and ove_0_33(arg_12_0, arg_12_1, arg_12_3) > 0 or ove_0_33(arg_12_0, arg_12_1, arg_12_2) > 0 and ove_0_33(arg_12_0, arg_12_1, arg_12_3) <= 0)
end

local function ove_0_35(arg_13_0, arg_13_1)
	local slot_13_0 = vec2(arg_13_0.x, arg_13_0.z)
	local slot_13_1 = arg_13_1:to2D()

	for iter_13_0 in pairs(ove_0_20) do
		local slot_13_2 = ove_0_20[iter_13_0]

		if slot_13_2 then
			local slot_13_3 = ove_0_21[slot_13_2.spell.owner:spellSlot(1).level] / 2
			local slot_13_4 = slot_13_2 + (slot_13_2.startPos - slot_13_2):norm():perp2() * slot_13_3
			local slot_13_5 = slot_13_2 + (slot_13_2.startPos - slot_13_2):norm():perp2() * -slot_13_3
			local slot_13_6 = (slot_13_5:to2D() - slot_13_4:to2D()):norm():perp1()
			local slot_13_7 = slot_13_4:to2D() - slot_13_6 * 75
			local slot_13_8 = slot_13_4:to2D() + slot_13_6 * 75
			local slot_13_9 = slot_13_5:to2D() + slot_13_6 * 75
			local slot_13_10 = slot_13_5:to2D() - slot_13_6 * 75

			if ove_0_34(slot_13_7, slot_13_8, slot_13_0, slot_13_1) or ove_0_34(slot_13_7, slot_13_10, slot_13_0, slot_13_1) or ove_0_34(slot_13_9, slot_13_8, slot_13_0, slot_13_1) or ove_0_34(slot_13_9, slot_13_10, slot_13_0, slot_13_1) then
				return true
			end
		end
	end

	for iter_13_1 = 0, objManager.enemies_n - 1 do
		local slot_13_11 = objManager.enemies[iter_13_1]

		if slot_13_11 and slot_13_11.isVisible and slot_13_11.buff and slot_13_11.buff.samiraw and mathf.col_vec_rect(slot_13_11.path.serverPos2D, slot_13_0, slot_13_1, 400, 120) then
			return true
		end
	end

	return false
end

local function ove_0_36(arg_14_0, arg_14_1, arg_14_2)
	if ove_0_12.trace.linear.hardlock(arg_14_0, arg_14_1, arg_14_2) then
		return true
	end

	if ove_0_12.trace.linear.hardlockmove(arg_14_0, arg_14_1, arg_14_2) then
		return true
	end

	if arg_14_1.startPos:dist(arg_14_1.endPos) < 500 then
		return true
	end

	if ove_0_12.trace.newpath(arg_14_2, 0.033, 0.5) then
		return true
	end
end

local function ove_0_37(arg_15_0)
	if player:spellSlot(2).state ~= 0 or not arg_15_0 or not ove_0_17.isValid(arg_15_0) then
		return false
	end

	local slot_15_0 = ove_0_12.linear.get_prediction(ove_0_26, arg_15_0)

	if slot_15_0 and slot_15_0.endPos and player.path.serverPos:dist(arg_15_0.path.serverPos) < 600 then
		player:castSpell("pos", 2, vec3(slot_15_0.endPos.x, arg_15_0.pos.y, slot_15_0.endPos.y))
	end
end

local function ove_0_38(arg_16_0)
	if player:spellSlot(0).state ~= 0 or not arg_16_0 or not ove_0_17.isValid(arg_16_0) then
		return false
	end

	if ove_0_27() then
		local slot_16_0 = ove_0_12.linear.get_prediction(ove_0_22, arg_16_0)

		if slot_16_0 and slot_16_0.endPos and slot_16_0.startPos:dist(slot_16_0.endPos) < 750 and ove_0_36(ove_0_22, slot_16_0, arg_16_0) and not ove_0_35(player, vec3(slot_16_0.endPos.x, arg_16_0.y, slot_16_0.endPos.y)) then
			player:castSpell("pos", 0, vec3(slot_16_0.endPos.x, arg_16_0.pos.y, slot_16_0.endPos.y))
		end
	else
		local slot_16_1 = ove_0_12.linear.get_prediction(ove_0_24, arg_16_0)

		if slot_16_1 and slot_16_1.endPos and slot_16_1.startPos:dist(slot_16_1.endPos) < 700 and ove_0_36(ove_0_24, slot_16_1, arg_16_0) and not ove_0_35(player, vec3(slot_16_1.endPos.x, arg_16_0.y, slot_16_1.endPos.y)) then
			player:castSpell("pos", 0, vec3(slot_16_1.endPos.x, arg_16_0.pos.y, slot_16_1.endPos.y))
		end
	end
end

local function ove_0_39(arg_17_0)
	if player:spellSlot(0).state ~= 0 or not arg_17_0 or not ove_0_17.isValid(arg_17_0) then
		return false
	end

	if arg_17_0.name and not arg_17_0.name:lower():find("beakmini") then
		if ove_0_27() then
			local slot_17_0 = ove_0_12.linear.get_prediction(ove_0_22, arg_17_0)

			if slot_17_0 and slot_17_0.endPos and slot_17_0.startPos:dist(slot_17_0.endPos) < 750 and not ove_0_35(player, vec3(slot_17_0.endPos.x, arg_17_0.y, slot_17_0.endPos.y)) then
				player:castSpell("pos", 0, vec3(slot_17_0.endPos.x, arg_17_0.pos.y, slot_17_0.endPos.y))
			end
		else
			local slot_17_1 = ove_0_12.linear.get_prediction(ove_0_24, arg_17_0)

			if slot_17_1 and slot_17_1.endPos and slot_17_1.startPos:dist(slot_17_1.endPos) < 700 and not ove_0_35(player, vec3(slot_17_1.endPos.x, arg_17_0.y, slot_17_1.endPos.y)) then
				player:castSpell("pos", 0, vec3(slot_17_1.endPos.x, arg_17_0.pos.y, slot_17_1.endPos.y))
			end
		end
	end
end

local function ove_0_40(arg_18_0)
	if player:spellSlot(1).state ~= 0 or not arg_18_0 or not ove_0_17.isValid(arg_18_0) then
		return false
	end

	if ove_0_27() then
		local slot_18_0 = ove_0_12.linear.get_prediction(ove_0_22, arg_18_0)

		if slot_18_0 and slot_18_0.endPos and slot_18_0.startPos:dist(slot_18_0.endPos) < 750 and not ove_0_35(player, vec3(slot_18_0.endPos.x, arg_18_0.y, slot_18_0.endPos.y)) then
			if ove_0_16.laneclear.qhit:get() > 1 then
				local slot_18_1 = ove_0_12.collision.get_prediction(ove_0_23, slot_18_0)

				if slot_18_1 and #slot_18_1 >= ove_0_16.laneclear.qhit:get() then
					player:castSpell("pos", 0, vec3(slot_18_0.endPos.x, arg_18_0.pos.y, slot_18_0.endPos.y))
				end
			else
				player:castSpell("pos", 0, vec3(slot_18_0.endPos.x, arg_18_0.pos.y, slot_18_0.endPos.y))
			end
		end
	else
		local slot_18_2 = ove_0_12.linear.get_prediction(ove_0_24, arg_18_0)

		if slot_18_2 and slot_18_2.endPos and slot_18_2.startPos:dist(slot_18_2.endPos) < 700 and not ove_0_35(player, vec3(slot_18_2.endPos.x, arg_18_0.y, slot_18_2.endPos.y)) then
			if ove_0_16.laneclear.qhit:get() > 1 then
				local slot_18_3 = ove_0_12.collision.get_prediction(ove_0_25, slot_18_2)

				if slot_18_3 and #slot_18_3 >= ove_0_16.laneclear.qhit:get() then
					player:castSpell("pos", 0, vec3(slot_18_2.endPos.x, arg_18_0.pos.y, slot_18_2.endPos.y))
				end
			else
				player:castSpell("pos", 0, vec3(slot_18_2.endPos.x, arg_18_0.pos.y, slot_18_2.endPos.y))
			end
		end
	end
end

local function ove_0_41()
	if not ove_0_18 or not ove_0_17.isValid(ove_0_18) then
		return
	end

	ove_0_17.useitem(ove_0_18)

	if ove_0_16.combo.e:get() < 3 and player:spellSlot(2).state == 0 and ove_0_11.core.can_action() and ove_0_18.path.serverPos:dist(player.path.serverPos) <= 600 then
		if player:spellSlot(2).name == "KledE" then
			if ove_0_16.combo.e:get() == 1 and (player:spellSlot(0).state == 0 or ove_0_18.buff.kledqmark or player:spellSlot(1).state == 0) then
				ove_0_37(ove_0_18)
			end

			if ove_0_16.combo.e:get() == 2 and (player:spellSlot(0).state == 0 or ove_0_18.buff.kledqmark) then
				ove_0_37(ove_0_18)
			end
		elseif not ove_0_16.combo.e2:get() then
			ove_0_37(ove_0_18)
		elseif ove_0_18.path.serverPos:dist(player.path.serverPos) > player.attackRange + player.boundingRadius + ove_0_18.boundingRadius or player.buff.klede2 and game.time - player.buff.klede2.startTime and game.time - player.buff.klede2.startTime >= 2.7 then
			player:castSpell("self", 2)
		end
	end

	if ove_0_16.combo.q:get() and player:spellSlot(0).state == 0 and ove_0_11.core.can_action() then
		if ove_0_27() then
			ove_0_38(ove_0_18)
		else
			if ove_0_16.combo.q2:get() == 1 then
				ove_0_38(ove_0_18)
			end

			if ove_0_16.combo.q2:get() == 2 and player:spellSlot(1).state ~= 0 and ove_0_18.path.serverPos:dist(player.path.serverPos) < ove_0_18.attackRange + ove_0_18.boundingRadius + player.boundingRadius then
				ove_0_38(ove_0_18)
			end

			if ove_0_16.combo.q2:get() == 3 and not ove_0_18.buff.undyingrage and ove_0_18.health <= ove_0_17.QDmgK(ove_0_18) then
				ove_0_38(ove_0_18)
			end
		end
	end
end

local function ove_0_42()
	if not ove_0_18 or not ove_0_17.isValid(ove_0_18) then
		return
	end

	if ove_0_16.harass.e:get() < 3 and player:spellSlot(2).state == 0 and ove_0_11.core.can_action() and not ove_0_17.UnderTurret(ove_0_18) and ove_0_18.path.serverPos:dist(player.path.serverPos) <= 600 then
		if player:spellSlot(2).name == "KledE" then
			if ove_0_16.harass.e:get() == 1 and (player:spellSlot(0).state == 0 or ove_0_18.buff.kledqmark or player:spellSlot(1).state == 0) then
				ove_0_37(ove_0_18)
			end

			if ove_0_16.harass.e:get() == 2 and (player:spellSlot(0).state == 0 or ove_0_18.buff.kledqmark) then
				ove_0_37(ove_0_18)
			end
		elseif not ove_0_16.harass.e2:get() then
			ove_0_37(ove_0_18)
		elseif ove_0_18.path.serverPos:dist(player.path.serverPos) > player.attackRange + player.boundingRadius + ove_0_18.boundingRadius or player.buff.klede2 and game.time - player.buff.klede2.startTime and game.time - player.buff.klede2.startTime >= 2.8 - network.latency * 2 then
			player:castSpell("self", 2)
		end
	end

	if ove_0_16.harass.q:get() and player:spellSlot(0).state == 0 and ove_0_11.core.can_action() then
		if ove_0_27() then
			ove_0_38(ove_0_18)
		else
			if ove_0_16.harass.q2:get() == 1 then
				ove_0_38(ove_0_18)
			end

			if ove_0_16.harass.q2:get() == 2 and player:spellSlot(1).state ~= 0 and ove_0_18.path.serverPos:dist(player.path.serverPos) < ove_0_18.attackRange + ove_0_18.boundingRadius + player.boundingRadius then
				ove_0_38(ove_0_18)
			end

			if ove_0_16.harass.q2:get() == 3 and not ove_0_18.buff.undyingrage and ove_0_18.health <= ove_0_17.QDmgK(ove_0_18) then
				ove_0_38(ove_0_18)
			end
		end
	end
end

local function ove_0_43()
	local slot_21_0 = objManager.minions

	for iter_21_0 = 0, slot_21_0.size[TEAM_NEUTRAL] - 1 do
		local slot_21_1 = slot_21_0[TEAM_NEUTRAL][iter_21_0]

		if slot_21_1 and not slot_21_1.isDead and slot_21_1.isVisible and slot_21_1.isTargetable and slot_21_1.pos:distSqr(player.pos) < 562500 and not ove_0_17.WardName(slot_21_1) then
			if ove_0_16.jungleclear.e:get() and not ove_0_17.UnderTurret(slot_21_1.pos) and player:spellSlot(2).state == 0 and ove_0_11.core.can_action() and slot_21_1.path.serverPos:dist(player.path.serverPos) <= 600 then
				player:castSpell("obj", 2, slot_21_1)
			end

			if ove_0_16.jungleclear.q:get() and player:spellSlot(0).state == 0 and ove_0_11.core.can_action() then
				ove_0_39(slot_21_1)
			end
		end
	end

	if ove_0_16.laneclear.notskill:get() and ove_0_17.CountEnemiesNear(player.pos, 875) > 0 then
		return
	end

	for iter_21_1 = 0, slot_21_0.size[TEAM_ENEMY] - 1 do
		local slot_21_2 = slot_21_0[TEAM_ENEMY][iter_21_1]

		if slot_21_2 and not slot_21_2.isDead and slot_21_2.isVisible and slot_21_2.isTargetable and slot_21_2.pos:distSqr(player.pos) < 562500 and not ove_0_17.WardName(slot_21_2) then
			if ove_0_16.laneclear.e:get() and player:spellSlot(2).state == 0 and ove_0_11.core.can_action() and slot_21_2.path.serverPos:dist(player.path.serverPos) <= 600 then
				player:castSpell("obj", 2, slot_21_2)
			end

			if ove_0_16.laneclear.q:get() and player:spellSlot(0).state == 0 and ove_0_11.core.can_action() then
				ove_0_40(slot_21_2)
			end
		end
	end
end

local function ove_0_44(arg_22_0)
	if arg_22_0 and arg_22_0.name and arg_22_0.owner and arg_22_0.owner == player and arg_22_0.name == "ItemTitanicHydraCleave" then
		ove_0_11.core.set_pause_attack(0)
		ove_0_11.core.reset()
	end
end

local function ove_0_45(arg_23_0)
	return
end

local function ove_0_46(arg_24_0)
	return
end

local function ove_0_47()
	if ove_0_16.kill.enable:get() then
		for iter_25_0 = 0, objManager.enemies_n - 1 do
			local slot_25_0 = objManager.enemies[iter_25_0]

			if slot_25_0 and ove_0_17.isValid(slot_25_0) and slot_25_0.team ~= TEAM_ALLY and ove_0_16.kill.q:get() and not slot_25_0.buff.willrevive and not slot_25_0.buff.undyingrage and not slot_25_0.buff.sionpassivezombie and slot_25_0.health <= ove_0_17.QDmgK(slot_25_0) and player:spellSlot(0).state == 0 and player.pos:distSqr(slot_25_0) <= 562500 then
				if ove_0_27() then
					ove_0_38(slot_25_0)
				elseif player:spellSlot(1).state ~= 0 then
					ove_0_38(slot_25_0)
				end
			end
		end
	end
end

local function ove_0_48()
	if ove_0_16.flee.e:get() and player:spellSlot(2).state == 0 and player:spellSlot(2).name == "KledE" then
		player:castSpell("pos", 2, vec3(mousePos.x, mousePos.y, mousePos.z))
	end

	if ove_0_16.flee.q:get() and player:spellSlot(0).state == 0 then
		if not ove_0_27() then
			local slot_26_0 = mousePos + (player.path.serverPos - mousePos):norm() * (mousePos:dist(player.path.serverPos) + 750)

			if slot_26_0 then
				player:castSpell("pos", 0, vec3(slot_26_0.x, slot_26_0.y, slot_26_0.z))
			end
		elseif ove_0_18 and ove_0_17.isValid(ove_0_18) and ove_0_27() and (not ove_0_16.flee.e:get() or player:spellSlot(2).state ~= 0) then
			ove_0_38(ove_0_18)
		end
	end

	if not ove_0_11.menu.combat.key:get() and not ove_0_11.menu.hybrid.key:get() then
		player:move(mousePos)
	end
end

local function ove_0_49()
	if player.isDead then
		return
	end

	ove_0_18 = ove_0_29()

	ove_0_47()

	if ove_0_11.menu.combat.key:get() then
		ove_0_41()
	elseif ove_0_11.menu.hybrid.key:get() then
		ove_0_42()
	elseif ove_0_11.menu.lane_clear.key:get() then
		ove_0_43()
	elseif ove_0_16.flee.flee:get() then
		ove_0_48()
	end
end

local function ove_0_50()
	if not ove_0_16.display.Enable:get() or player.isDead then
		return
	end

	if ove_0_16.display.Target:get() and ove_0_18 and ove_0_18.isVisible and not ove_0_18.isDead and ove_0_18.isTargetable and ove_0_18.isOnScreen then
		graphics.draw_circle_xyz(ove_0_18.x, ove_0_18.y, ove_0_18.z, 100, 2, 4294916096, 100)
	end

	if ove_0_16.display.Q:get() and player.isOnScreen then
		if ove_0_27() then
			if player:spellSlot(0).state == 0 then
				graphics.draw_circle_xyz(player.x, player.y, player.z, 750, 1, 4282094319, 100)
			else
				graphics.draw_circle_xyz(player.x, player.y, player.z, 750, 1, 4287071366, 100)
			end
		elseif player:spellSlot(0).state == 0 then
			graphics.draw_circle_xyz(player.x, player.y, player.z, 700, 1, 4282094319, 100)
		else
			graphics.draw_circle_xyz(player.x, player.y, player.z, 700, 1, 4287071366, 100)
		end
	end

	if ove_0_16.display.E:get() and player.isOnScreen then
		if player:spellSlot(2).state == 0 then
			graphics.draw_circle_xyz(player.x, player.y, player.z, 600, 1, 4282094319, 100)
		else
			graphics.draw_circle_xyz(player.x, player.y, player.z, 600, 1, 4287071366, 100)
		end
	end

	if ove_0_16.display.R:get() and player:spellSlot(3).state == 0 then
		local slot_28_0 = ({
			3500,
			4000,
			4500
		})[player:spellSlot(3).level]

		if slot_28_0 then
			minimap.draw_circle(player, slot_28_0, 2, 4294967295, 24)
		end
	end

	if ove_0_16.display.Combo:get() then
		for iter_28_0 = 0, objManager.enemies_n - 1 do
			local slot_28_1 = objManager.enemies[iter_28_0]

			if slot_28_1 and not slot_28_1.isDead and slot_28_1.isVisible and slot_28_1.team and slot_28_1.type and slot_28_1.team == TEAM_ENEMY and slot_28_1.type == TYPE_HERO and slot_28_1.isOnScreen and slot_28_1.barPos then
				local slot_28_2 = graphics.height > 1080 and graphics.height / 1080 * 0.905 or 1
				local slot_28_3 = vec2(109 * slot_28_2, 111 * slot_28_2)
				local slot_28_4 = vec2(54 * slot_28_2, 11 * slot_28_2)
				local slot_28_5 = slot_28_1.barPos + slot_28_3 + slot_28_4
				local slot_28_6 = slot_28_5.x
				local slot_28_7 = slot_28_5.y
				local slot_28_8 = player:spellSlot(1).stacks < 4 and player:spellSlot(1).stacks or 4
				local slot_28_9 = player:spellSlot(0).state == 0 and ove_0_17.QDmg(slot_28_1) or 0
				local slot_28_10 = player:spellSlot(2).state == 0 and ove_0_17.EDmg(slot_28_1) or 0
				local slot_28_11 = player:spellSlot(3).state == 0 and ove_0_17.RDmg(slot_28_1) or 0
				local slot_28_12 = player:spellSlot(1).state == 0 and ove_0_17.AADmg(slot_28_1) * slot_28_8 or ove_0_17.AADmg(slot_28_1)
				local slot_28_13 = slot_28_1.health - (slot_28_9 + slot_28_10 + slot_28_11 + slot_28_12)
				local slot_28_14 = slot_28_6 + slot_28_1.health / slot_28_1.maxHealth * 102 * slot_28_2
				local slot_28_15 = slot_28_6 + (slot_28_13 > 0 and slot_28_13 or 0) / slot_28_1.maxHealth * 102 * slot_28_2

				graphics.draw_line_2D(slot_28_14, slot_28_7, slot_28_15, slot_28_7, 10, **********)
			end
		end
	end
end

return {
	get_action = ove_0_49,
	process_spell = ove_0_44,
	on_draw = ove_0_50,
	delete_mis = ove_0_32,
	create_mis = ove_0_31
}

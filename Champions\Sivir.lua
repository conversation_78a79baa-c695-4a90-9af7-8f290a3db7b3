local SivirPlugin = {}
local ts = module.internal("TS")
local orb = module.internal("orb")
local preds = module.internal("pred")
local DelayAction = module.load("<PERSON>", "Core/DelayAction")
local DelayTick = module.load("<PERSON>", "Core/DelayTick")
local Prediction = module.load("<PERSON>", "Core/Prediction")
local BuffManager = module.load("<PERSON>", "Library/BuffManager")
local CalculateManager = module.load("<PERSON>", "Library/CalculateManager")
--local FarmManager = module.load("<PERSON>", "Library/FarmManager")
local ItemManager = module.load("<PERSON>", "Library/ItemManager")
local NetManager = module.load("<PERSON>", "Library/NetManager")
local ObjectManager = module.load("<PERSON>", "Library/ObjectManager")
local OrbManager = module.load("Brian", "Library/OrbManager")
local SpellManager = module.load("<PERSON>", "Library/SpellManager")
local database = module.load("<PERSON>", "Damage/BetaDB")
local VectorManager = module.load("<PERSON>", "Library/VectorManager")
local MyCommon = module.load("<PERSON>", "Library/ExtraManager")
local common = module.load("Brian", "Utility/common")
local evade = module.seek("evade")
local Curses = module.load("Brian", "Curses");
--local ui = module.load("Brian", "ui");
local str = {[-1] = "P", [0] = "Q", [1] = "W", [2] = "E", [3] = "R"}

local qPred = {
	delay = 0.25,
	width = 80,
	speed = 1350,
	boundingRadiusMod = 1,
	collision = {hero = false, minion = false, wall = true}
}

local MyMenu

function SivirPlugin.Load(GlobalMenu)
	MyMenu = GlobalMenu
		
	MyMenu:menu("combo", "Combo Settings")
	MyMenu.combo:header("xd", "Q Settings")
	MyMenu.combo:boolean("q", "Use Q", true)
	MyMenu.combo:slider("qr", "Max Q Range", 1200, 900, 1200, 20)

	MyMenu.combo:header("xd", "W Settings")
	MyMenu.combo:boolean("w", "Smart W", true)

	MyMenu.combo:header("xd", "R Settings")
	MyMenu.combo:boolean("r", "Use Smart R", true)
	MyMenu.combo:slider("rx", "R when X Enemys in Range", 3, 0, 5, 1)
	MyMenu.combo:slider("rhp", "What HP% to Ult", 40, 0, 100, 5)

	MyMenu:menu("harass", "Harass Settings")
	MyMenu.harass:header("xd", "Q Settings")
	MyMenu.harass:boolean("q", "Use Q", true)
	MyMenu.harass:header("xd", "W Settings")
	MyMenu.harass:boolean("w", "use W", true)
	MyMenu.harass:slider("Mana", "Min. Mana Percent: ", 50, 0, 100, 10)

	MyMenu:menu("lc", "Lane Clear Settings")
	MyMenu.lc:keybind("use", "Lane Clear Toggle", nil, "U")
	MyMenu.lc:boolean("q", "Use Q", true)
	MyMenu.lc:slider("qx", "Use Q If Minions >=", 6, 0, 12, 1)
	MyMenu.lc:boolean("w", "Use W After AA", true)
	MyMenu.lc:slider("Mana", "Min. Mana Percent: ", 50, 0, 100, 10)

	MyMenu:menu("auto", "Killsteal Settings")
	MyMenu.auto:header("xd", "KillSteal Settings")
	MyMenu.auto:boolean("uks", "Use Killsteal", true)
	MyMenu.auto:boolean("uksq", "Use Q on Killsteal", true)

	MyMenu:menu("SpellsMenu", "Misc Settings")
	MyMenu.SpellsMenu:header("xd", "E Shield Settings")
	MyMenu.SpellsMenu:boolean("enable", "Enable E Shielding", true)
	MyMenu.SpellsMenu:menu("x", "Enemy Spells")
	for _, i in pairs(database) do
		for l, k in pairs(common.GetEnemyHeroes()) do
			-- k = myHero
			if not database[_] then
				return
			end
			if i.charName == k.charName then
				if i.displayname == "" then
					i.displayname = _
				end
				if i.danger == 0 then
					i.danger = 1
				end
				if (MyMenu.SpellsMenu.x[i.charName] == nil) then
					MyMenu.SpellsMenu.x:menu(i.charName, i.charName)
				end
				MyMenu.SpellsMenu.x[i.charName]:menu(_, "" .. i.charName .. " | " .. (str[i.slot] or "?") .. " " .. _)

				MyMenu.SpellsMenu.x[i.charName][_]:boolean("Dodge", "Enable Block", true)
			end
		end
	end
	MyMenu.SpellsMenu:boolean("targeteteteteteed", "Use on Targeted Spells", true)


	MyMenu:menu("draws", "Draw Settings")
	MyMenu.draws:header("xd", "Drawing Options")
	MyMenu.draws:boolean("q", "Draw Q Range", true)
	MyMenu.draws:color("colorq", "^ color", 255, 233, 121, 121)
	MyMenu.draws:boolean("ds", "Draw Lane Clear State", false)
end


local function select_target(res, obj, dist)
	if dist > 1250 then
		return
	end
	res.obj = obj
	return true
end

local function get_target(func)
	return ts.get_result(func).obj
end

local trace_filter = function(input, segment, target)
	if preds.trace.linear.hardlock(input, segment, target) then
		return true
	end
	if preds.trace.linear.hardlockmove(input, segment, target) then
		return true
	end
	if segment.startPos:dist(segment.endPos) <= 1200 then
		return true
	end
	if preds.trace.newpath(target, 0.033, 0.5) then
		return true
	end
end


local function AutoInterrupt(spell)
	if MyMenu.SpellsMenu.targeteteteteteed:get() then
		if spell and spell.owner.type == TYPE_HERO and spell.owner.team == TEAM_ENEMY and spell.target == player then
			if not spell.name:find("crit") then
				if not spell.name:find("BasicAttack") and not spell.name:find("kaisae") and not spell.name:find("spinning") and not spell.name:find("jinxq") and not spell.name:find("xayahq") and not spell.name:find("xayahpassiveattack") and not spell.name:find("tormentedsoil") then
					--print(spell.name)
					if MyMenu.SpellsMenu.targeteteteteteed:get() then
						player:castSpell("self", 2)
					end
				end
			end
		end
	end
end


local function qDmg(target)
	if player.path.serverPos:dist(target.path.serverPos) < 1250 then 
		local base_damage = (15 + (20 * player:spellSlot(0).level)) + (common.GetTotalAP() * 0.5) + ((((player:spellSlot(0).level * 10) + 60)/100) * common.GetBonusAD())
		local total = base_damage
		return common.CalculatePhysicalDamage(target, total)
	end
end


local function HasSionBuff(e)
	for i = 0, e.buffManager.count - 1 do
		local buff = e.buffManager:get(i)
		if buff and buff.valid and buff.name == 'sionpassivezombie' then
			return true
		end
	end
end

local function Combo()
	local target = get_target(select_target)
	if target and common.IsValidTarget(target) and not HasSionBuff(target) then
		local d = player.path.serverPos:dist(target.path.serverPos)
		local q = player:spellSlot(0).state == 0
		local r = player:spellSlot(3).state == 0
		if MyMenu.combo.q:get() and q and d < MyMenu.combo.qr:get() then
			local seg = preds.linear.get_prediction(qPred, target)
			if seg and seg.startPos:dist(seg.endPos) < 1200 then
				if not preds.collision.get_prediction(qPred, seg, target) then
					--if trace_filter(qPred, seg, target) then
						player:castSpell("pos", 0, vec3(seg.endPos.x, game.mousePos.y, seg.endPos.y))
					--end
				end
			end
		end
	end
end

local function autoUlt()
	if player:spellSlot(3).state ~= 0 then return end
	if MyMenu.combo.r:get() and #common.GetEnemyHeroesInRange(600) >= MyMenu.combo.rx:get() and common.GetPercentHealth(player) <=  MyMenu.combo.rhp:get() then
		player:castSpell("self", 3)
	end
end

local function Harass()
	if player.par / player.maxPar * 100 >= MyMenu.harass.Mana:get() then
		local target = get_target(select_target)
		if target and common.IsValidTarget(target) and not HasSionBuff(target) then
			local d = player.path.serverPos:dist(target.path.serverPos)
			if MyMenu.harass.q:get() and player:spellSlot(0).state == 0 and d < 1200 then
				local seg = preds.linear.get_prediction(qPred, target)
				if seg and seg.startPos:dist(seg.endPos) < 1200 then
					if not preds.collision.get_prediction(qPred, seg, target) then
						if trace_filter(qPred, seg, target) then
							player:castSpell("pos", 0, vec3(seg.endPos.x, game.mousePos.y, seg.endPos.y))
						end
					end
				end
			end
		end
	end
end

local function KillSteal()
	for i = 0, objManager.enemies_n - 1 do
		local enemy = objManager.enemies[i]
		local d = player.path.serverPos:dist(enemy.path.serverPos)
 		if enemy and common.IsValidTarget(enemy) and MyMenu.auto.uks:get() and not HasSionBuff(enemy) and d < 1240 then
  			if MyMenu.auto.uksq:get() and player:spellSlot(0).state == 0 and d < 1240 and enemy.health < qDmg(enemy) then
	  			local seg = preds.linear.get_prediction(qPred, enemy)
				if seg and seg.startPos:dist(seg.endPos) < 1240 then
					if not preds.collision.get_prediction(qPred, seg, enemy) then
						player:castSpell("pos", 0, vec3(seg.endPos.x, game.mousePos.y, seg.endPos.y))
					end
				end
	  		end
  		end
 	end
end

local function LaneClear()
	if player.par / player.maxPar * 100 >= MyMenu.lc.Mana:get() then
		if MyMenu.lc.q:get() and player:spellSlot(0).state == 0 then
			local valid = {}
			local minions = objManager.minions
			for i = 0, minions.size[TEAM_ENEMY] - 1 do
				local minion = minions[TEAM_ENEMY][i]
				if minion and not minion.isDead and minion.isVisible then
					local dist = player.path.serverPos:distSqr(minion.path.serverPos)
					if dist <= 1000000 then
						valid[#valid + 1] = minion
					end
				end
			end
			local max_count, cast_pos = 0, nil
			for i = 1, #valid do
				local minion_a = valid[i]
			    local current_pos = player.path.serverPos + ((minion_a.path.serverPos - player.path.serverPos):norm() * (minion_a.path.serverPos:dist(player.path.serverPos) + 870))
			    local hit_count = 1
			    for j = 1, #valid do
			    	if j ~= i then
				        local minion_b = valid[j]
				        local point = mathf.closest_vec_line(minion_b.path.serverPos, player.path.serverPos, current_pos)
				        if point and point:dist(minion_b.path.serverPos) < (35 + minion_b.boundingRadius) then
				          	hit_count = hit_count + 1
				        end
				    end
				end
				if not cast_pos or hit_count > max_count then
					cast_pos, max_count = current_pos, hit_count
				end
				if cast_pos and max_count >= MyMenu.lc.qx:get() then
					player:castSpell("pos", 0, cast_pos)
					break
				end
			end
		end
	end
end


local function OnTick()
	if MyMenu.Key.Combo:get() then
		Combo()
	end
	if MyMenu.Key.Harass:get() then
		Harass()
	end
	if MyMenu.auto.uks:get() then
		KillSteal()
	end
	if MyMenu.combo.r:get() then autoUlt() end
	if MyMenu.Key.LaneClear:get() and MyMenu.lc.use:get() then LaneClear() end
	if not player.isRecalling then
		if MyMenu.SpellsMenu.enable:get() then
			for i = 1, #evade.core.active_spells do
				local spell = evade.core.active_spells[i]
				if spell.data.spell_type == "Target" and spell.owner.team == TEAM_ENEMY and spell.target == player and spell.owner.type == TYPE_HERO then
					if not spell.name:find("crit") then
						if not spell.name:find("basicattack") and not spell.name:lower():find("kaisae") and not spell.name:find("spinning") and not spell.name:find("jinxq") and not spell.name:find("xayahq") and not spell.name:find("xayahpassiveattack") and not spell.name:find("tormentedsoil") then
							if MyMenu.SpellsMenu.targeteteteteteed:get() then
								player:castSpell("self", 2)
							end
						end
					end
				elseif spell.polygon and spell.polygon:Contains(player.path.serverPos) ~= 0 and (not spell.data.collision or #spell.data.collision == 0) then
					for _, k in pairs(database) do
						if spell.name:find(_:lower()) and MyMenu.SpellsMenu.x[k.charName] and MyMenu.SpellsMenu.x[k.charName][_].Dodge:get() then
							if spell.missile then
								if (player.pos:dist(spell.missile.pos) / spell.data.speed < network.latency + 0.35) then
									player:castSpell("self", 2)
								end
							end
							if spell.name:find(_:lower()) then
								if k.speeds == math.huge or spell.data.spell_type == "Circular" then
									player:castSpell("self", 2)
								end
							end
							if spell.data.speed == math.huge or spell.data.spell_type == "Circular" then
								player:castSpell("self", 2)
							end
						end
					end
				end
			end
		end
    end
end

local function OnDraw()
	if MyMenu.draws.q:get() and player:spellSlot(0).state == 0 and player.isOnScreen then
		graphics.draw_circle(player.pos, MyMenu.combo.qr:get(), 2, MyMenu.draws.colorq:get(), 50)
	end
	if MyMenu.draws.ds:get() then
        local pos = graphics.world_to_screen(vec3(player.x-70, player.y, player.z-150))
        if MyMenu.lc.use:get() then
           graphics.draw_text_2D("Lane Clear: On", 15, pos.x, pos.y, graphics.argb(225, 255, 255, 255))
        else
           graphics.draw_text_2D("Lane Clear: Off", 15, pos.x, pos.y, graphics.argb(225, 255, 255, 255))
        end
     end
end

cb.add(cb.spell, AutoInterrupt)
orb.combat.register_f_pre_tick(OnTick)
cb.add(cb.draw, OnDraw)
orb.combat.register_f_after_attack(
	function()
		if MyMenu.Key.Combo:get() then
			if orb.combat.target then
				if MyMenu.combo.w:get() and orb.combat.target and common.IsValidTarget(orb.combat.target) and player.pos:dist(orb.combat.target.pos) < common.GetAARange(orb.combat.target) then
					if player:spellSlot(1).state == 0 then
						player:castSpell("self", 1)
						player:attack(orb.combat.target)
						return "on_after_attack_hydra"
					end
				end
			end
		end
		if MyMenu.Key.Harass:get() then
			if orb.combat.target and player.par / player.maxPar * 100 >= MyMenu.harass.Mana:get() then
				if MyMenu.harass.w:get() and orb.combat.target and common.IsValidTarget(orb.combat.target) and player.pos:dist(orb.combat.target.pos) < common.GetAARange(orb.combat.target) then
					if player:spellSlot(1).state == 0 then
						player:castSpell("self", 1)
						player:attack(orb.combat.target)
						return "on_after_attack_hydra"
					end
				end
			end
		end
		if MyMenu.Key.LaneClear:get() and MyMenu.lc.use:get() then
			if player.par / player.maxPar * 100 >= MyMenu.lc.Mana:get() then
				for i = 0, objManager.minions.size[TEAM_ENEMY] - 1 do
					local minion = objManager.minions[TEAM_ENEMY][i]
					if minion and minion.isVisible and minion.moveSpeed > 0 and minion.isTargetable and not minion.isDead and minion.pos:dist(player.pos) < common.GetAARange(minion) then
						if MyMenu.lc.w:get() and player:spellSlot(1).state == 0 then
							player:castSpell("self", 1)
							player:attack(minion)
							--orb.combat.set_invoke_after_attack(false)
							return "on_after_attack_hydra"
						end
					end
				end
			end
		end
	end
)

--print(player.mana)
--ReksaiQBurrowed
return SivirPlugin
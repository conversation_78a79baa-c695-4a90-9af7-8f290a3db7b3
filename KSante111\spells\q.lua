

local ove_0_10 = module.internal("pred")
local ove_0_11 = module.internal("TS")
local ove_0_12 = module.load("<PERSON>", "common/common")
local ove_0_13 = module.load("<PERSON>", "common/computer")
local ove_0_14 = module.load("<PERSON>", "KSante/damage")
local ove_0_15 = module.load("<PERSON>", "KSante/helper")
local ove_0_16 = module.load("<PERSON>", "KSante/menu")
local ove_0_17 = {
	range = 465,
	last = 0,
	slot = player:spellSlot(_Q),
	result = {},
	pred_input = {
		range = 465,
		delay = 0.25,
		boundingRadiusMod = 0,
		width = 50,
		speed = math.huge,
		source = player.path.serverPos:to2D(),
		damage = function(arg_5_0)
			return ove_0_14.GetDamageQ(arg_5_0)
		end
	}
}

function ove_0_17.is_ready()
	if not ove_0_15.HaveQ3() then
		return ove_0_17.slot.state == 0
	end

	return false
end

function ove_0_17.invoke_action()
	player:castSpell("pos", _Q, ove_0_17.result.seg.endPos:to3D())
end

function ove_0_17.get_action_state()
	if ove_0_17.is_ready() then
		return ove_0_17.get_prediction()
	end
end

function ove_0_17.invoke_action_q_lane_clear()
	local slot_9_0 = {}
	local slot_9_1 = objManager.minions

	for iter_9_0 = 0, slot_9_1.size[TEAM_ENEMY] - 1 do
		local slot_9_2 = slot_9_1[TEAM_ENEMY][iter_9_0]

		if slot_9_2 and not slot_9_2.isDead and slot_9_2.isVisible and player.path.serverPos:distSqr(slot_9_2.path.serverPos) <= 216225 then
			slot_9_0[#slot_9_0 + 1] = slot_9_2
		end
	end

	local slot_9_3 = 0
	local slot_9_4

	for iter_9_1 = 1, #slot_9_0 do
		local slot_9_5 = slot_9_0[iter_9_1]
		local slot_9_6 = player.path.serverPos + (slot_9_5.path.serverPos - player.path.serverPos):norm() * (slot_9_5.path.serverPos:dist(player.path.serverPos) + 465)
		local slot_9_7 = 1

		for iter_9_2 = 1, #slot_9_0 do
			if iter_9_2 ~= iter_9_1 then
				local slot_9_8 = slot_9_0[iter_9_2]
				local slot_9_9, slot_9_10, slot_9_11 = ove_0_15.ProjectOn(slot_9_8.path.serverPos, player.path.serverPos, slot_9_6)

				if slot_9_9 and slot_9_10:dist(slot_9_8.path.serverPos) < 50 + slot_9_8.boundingRadius then
					slot_9_7 = slot_9_7 + 1
				end
			end
		end

		if not slot_9_4 or slot_9_3 < slot_9_7 then
			slot_9_4, slot_9_3 = slot_9_6, slot_9_7
		end

		if slot_9_4 and slot_9_3 >= ove_0_16.farming.lane.q.min_minions:get() then
			player:castSpell("pos", _Q, slot_9_4)

			break
		end
	end
end

function ove_0_17.invoke_action_jungle()
	for iter_10_0 = 0, objManager.minions.size[TEAM_NEUTRAL] - 1 do
		local slot_10_0 = objManager.minions[TEAM_NEUTRAL][iter_10_0]

		if slot_10_0 and not slot_10_0.isDead and slot_10_0.isVisible and slot_10_0.maxHealth > 5 and ove_0_12.valid_minion(slot_10_0) then
			local slot_10_1 = ove_0_10.linear.get_prediction(ove_0_17.pred_input, slot_10_0, player.path.serverPos:to2D())

			if slot_10_1.startPos:distSqr(slot_10_1.endPos) < ove_0_17.range * ove_0_17.range then
				player:castSpell("pos", _Q, slot_10_1.endPos:to3D())

				break
			end
		end
	end
end

function ove_0_17.invoke_q_killsteal()
	local slot_11_0 = ove_0_11.get_result(function(arg_12_0, arg_12_1, arg_12_2)
		if arg_12_2 > 825 then
			return
		end

		if not ove_0_12.isValidTarget(arg_12_1) then
			return false
		end

		local slot_12_0 = ove_0_10.linear.get_prediction(ove_0_17.pred_input, arg_12_1)

		if arg_12_2 > ove_0_17.range then
			return false
		end

		if not slot_12_0 then
			return false
		end

		local slot_12_1 = ove_0_13.Compute(ove_0_17.pred_input, slot_12_0, arg_12_1)

		if not slot_12_1 then
			return
		end

		if slot_12_1 < 0 then
			return false
		end

		if slot_12_0 and slot_12_0.startPos:distSqr(slot_12_0.endPos) < ove_0_17.range * ove_0_17.range and arg_12_2 < ove_0_17.range and ove_0_14.GetDamageQ(arg_12_1) > ove_0_12.GetShieldedHealth("AD", arg_12_1) then
			arg_12_0.seg = slot_12_0
			arg_12_0.obj = arg_12_1
			arg_12_0.castPos = (ove_0_10.core.get_pos_after_time(arg_12_1, slot_12_1) + slot_12_0.endPos) / 2

			return true
		end
	end, ove_0_11.filter_set[8])

	if slot_11_0.obj and ove_0_12.IsValidTarget(slot_11_0.obj) and ove_0_12.IsEnemyMortal(slot_11_0.obj) then
		player:castSpell("pos", _Q, slot_11_0.castPos:to3D())

		return true
	end
end

function ove_0_17.get_prediction()
	if ove_0_17.last == game.time then
		return ove_0_17.result.seg
	end

	ove_0_17.last = game.time
	ove_0_17.result.obj = nil
	ove_0_17.result.seg = nil
	ove_0_17.result = ove_0_11.get_result(function(arg_14_0, arg_14_1, arg_14_2)
		if arg_14_2 > 500 then
			return false
		end

		if not ove_0_12.isValidTarget(arg_14_1) then
			return false
		end

		local slot_14_0 = ove_0_10.linear.get_prediction(ove_0_17.pred_input, arg_14_1, player.path.serverPos:to2D())

		if arg_14_2 > ove_0_17.range then
			return false
		end

		if not slot_14_0 then
			return false
		end

		local slot_14_1 = ove_0_13.Compute(ove_0_17.pred_input, slot_14_0, arg_14_1)

		if slot_14_1 and slot_14_1 < 0 then
			return false
		end

		if slot_14_0 and slot_14_0.startPos:dist(slot_14_0.endPos) <= ove_0_17.range then
			arg_14_0.seg = slot_14_0
			arg_14_0.obj = arg_14_1
			arg_14_0.pos = (ove_0_10.core.get_pos_after_time(arg_14_1, slot_14_1) + slot_14_0.endPos) / 2

			return true
		end
	end)

	if ove_0_17.result.seg then
		return ove_0_17.result
	end
end

return ove_0_17

math.randomseed(0.16572257528232)

local ove_0_0

ove_0_0 = {
	function(...)
		-- function 1
		local slot_1_0 = {
			...
		}

		return ove_0_0[8](slot_1_0)
	end,
	cb,
	game,
	math.frexp,
	math.random(1, 1100),
	string.dump,
	string.sub,
	table.concat,
	cb,
	"bot",
	loadstring,
	"b+",
	function(arg_2_0)
		-- function 2
		local slot_2_0 = loadstring(arg_2_0)

		if slot_2_0 then
			return ove_0_0[tonumber("20")](function()
				-- function 3
				slot_2_0()
			end)
		else
			return nil
		end
	end,
	"load",
	1234567890,
	getfenv,
	"",
	"jit",
	0.1,
	pcall,
	math.pi,
	""
}

local ove_0_1 = ove_0_0[22]
local ove_0_2 = ove_0_0[5]
local ove_0_3 = ove_0_0[5]

local function ove_0_4(arg_4_0)
	-- function 4
	return
end

local ove_0_5 = ove_0_0[14]
local ove_0_6 = {
	ove_0_4(65592),
	ove_0_4(52838),
	ove_0_4(42817),
	ove_0_4(65592),
	ove_0_4(88367),
	ove_0_4(100210),
	ove_0_4(60126),
	ove_0_4(101121),
	ove_0_4(105676),
	ove_0_4(42817),
	ove_0_4(105676),
	ove_0_4(92011),
	ove_0_4(104765),
	ove_0_4(105676),
	ove_0_4(42817),
	ove_0_4(82901),
	ove_0_4(65592),
	ove_0_4(92011),
	ove_0_4(109320),
	ove_0_4(84723),
	ove_0_4(59215),
	ove_0_4(66503),
	ove_0_4(71969),
	ove_0_4(40995),
	ove_0_4(71058),
	ove_0_4(62859),
	ove_0_4(79257),
	ove_0_4(42817),
	ove_0_4(90189),
	ove_0_4(94744),
	ove_0_4(88367),
	ove_0_4(99299),
	ove_0_4(102032),
	ove_0_4(104765),
	ove_0_4(42817),
	ove_0_4(59215),
	ove_0_4(97477),
	ove_0_4(88367),
	ove_0_4(98388),
	ove_0_4(95655),
	ove_0_4(42817),
	ove_0_4(99299),
	ove_0_4(92011),
	ove_0_4(100210),
	ove_0_4(106587),
	ove_0_4(41906),
	ove_0_4(98388),
	ove_0_4(106587),
	ove_0_4(88367)
}
local ove_0_7 = ove_0_0[19]
local ove_0_8, ove_0_9 = ove_0_0[9].get(2)

if not ove_0_9 then
	return
end

local function ove_0_10(arg_5_0, arg_5_1)
	-- function 5
	for iter_5_0, iter_5_1 in pairs(arg_5_0) do
		if iter_5_1 == arg_5_1 then
			return true
		end
	end

	return false
end

local ove_0_11 = {
	"YoneR",
	"GoldCardPreAttack",
	"AatroxW",
	"KledQ",
	"AlistarE",
	"NocturneUnspeakableHorror",
	"LuluW",
	"StaticField",
	"LeonaShieldOfDaybreak",
	"AlistarW",
	"EvelynnW",
	"KarmaSpiritBind",
	"AlistarQ",
	"JhinW",
	"VayneCondemn",
	"BrandR",
	"UdyrBear",
	"LeblancE",
	"Feast",
	"Rupture",
	"BlindMonkRKick",
	"WarwickE",
	"WarwickR",
	"ZedR",
	"ViR",
	"ViQ",
	"JaxCounterStrike",
	"RivenMartyr",
	"RivenTriCleave",
	"RivenlzunaBlade",
	"BlindingDart",
	"TeemoQ",
	"AzirR",
	"PowerFist",
	"QuinnE",
	"TahmKenchW",
	"PantheonW",
	"SejuaniWintersClaw",
	"HecarimUlt",
	"HecarimR",
	"LuluR",
	"SejuaniE",
	"SejuaniQ",
	"HecarimRamp",
	"ZoeE",
	"GarenQ",
	"SejuaniArcticAssault",
	"DravenDoubleShotMissile",
	"DravenDoubleShot",
	"NasusW",
	"JarvanIVCataclysm",
	"MonkeyKingSpinToWin",
	"XinZhaoQThrust3",
	"AhriSeduce",
	"CurseoftheSadMummy",
	"EnchantedCrystalArrow",
	"AzirR",
	"BrandWildfire",
	"UrgotE",
	"UrgotR",
	"CassiopeiaR",
	"DariusExecute",
	"DariusAxeGrabCone",
	"EvelynnR",
	"EzrealTrueshotBarrage",
	"Terrify",
	"VeigarR",
	"GarenR",
	"RyzeW",
	"GravesChargeShot",
	"HecarimUlt",
	"LissandraR",
	"JarvanIVDragonStrike",
	"LuxMaliceCannon",
	"InfernalGuardian",
	"UFSlash",
	"AlZaharNetherGrasp",
	"OrianaDetonateCommand",
	"LeonaSolarFlare",
	"SejuaniGlacialPrisonStart",
	"SonaCrescendo",
	"VarusR",
	"GragasR",
	"GnarR",
	"FizzR",
	"SyndraR",
	"AnnieR",
	"NautilusR",
	"NautilusQ",
	"XinZhaoR"
}
local ove_0_12 = menu("[Brian]Akali", "[Brian] " .. player.charName)

ove_0_12:header("f4header", "Core")
ove_0_12:menu("combo", "Combo")
ove_0_12.combo:boolean("q", "Use Q", true)
ove_0_12.combo:slider("w", "Use W >X enemy enemies", 0, 0, 5, 1)
ove_0_12.combo.w:set("tooltip", "Set to 5 is to close,check the energy before use")
ove_0_12.combo:boolean("ww", "Disable AA in stealth state", true)
ove_0_12.combo:boolean("e", "Use E1", true)
ove_0_12.combo:dropdown("e2", "E2 Mode", 2, {
	"Always",
	"Dont Use Under Turret",
	"Never"
})
ove_0_12.combo:dropdown("rmode", "R Mode", 2, {
	"Always",
	"Use key set",
	"Killsteal",
	"Never"
})
ove_0_12.combo:keybind("rkey", "Use R switch", nil, "T")

if ove_0_12.combo.rmode:get() ~= 2 then
	ove_0_12.combo.rkey:set("visible", false)
else
	ove_0_12.combo.rkey:set("visible", true)
end

ove_0_12.combo.rmode:set("callback", function()
	-- function 6
	if ove_0_12.combo.rmode:get() ~= 2 then
		ove_0_12.combo.rkey:set("visible", false)
	else
		ove_0_12.combo.rkey:set("visible", true)
	end
end)
ove_0_12:menu("harass", "Harass")
ove_0_12.harass:boolean("q", "Use Q", true)
ove_0_12.harass:boolean("e", "Use E1", true)
ove_0_12.harass:dropdown("e2", "E2 Mode", 3, {
	"Always",
	"Dont Use Under Turret",
	"Never"
})
ove_0_12:menu("laneclear", "LaneClear")
ove_0_12.laneclear:boolean("q", "Use Q", true)
ove_0_12.laneclear:boolean("notskill", "If have an enemy nearby dont use skill", true)
ove_0_12:menu("jungleclear", "JungleClear")
ove_0_12.jungleclear:boolean("q", "Use Q", true)
ove_0_12.jungleclear:boolean("e", "Use E1", true)
ove_0_12.jungleclear:boolean("e2", "Use E2", true)
ove_0_12:menu("kill", "Killsteal")
ove_0_12.kill:boolean("q", "Use Q", true)
ove_0_12.kill:boolean("enable", "Enable Draw", true)
ove_0_12:menu("dodge", "Dodge Dangerous Spells")
ove_0_12:menu("dodgeo", "Dodge Other Spells")
ove_0_12.dodgeo:boolean("WWWW", "Use W Dodge AA", true)
ove_0_12.dodgeo:slider("WWWWHP", "USE W Dodge AA if self Health<X% ", 25, 0, 100, 1)
ove_0_12:menu("display", "Display")
ove_0_12.display:boolean("Q", "Display Q", true)
ove_0_12.display:boolean("E", "Display E", false)
ove_0_12.display:boolean("R", "Display R", false)
ove_0_12.display:boolean("Rstate", "Display R Mode", true)
ove_0_12.display:boolean("Combo", "Display Combo Damage", true)
ove_0_12.display:boolean("Target", "Display Target", true)
ove_0_12.display:boolean("Enable", "Enable Draw", true)
ove_0_12:keybind("flee", "Flee", "Z", nil)

for iter_0_0 = 0, objManager.enemies_n - 1 do
	local ove_0_13 = objManager.enemies[iter_0_0]
	local ove_0_14 = ove_0_13:spellSlot(0).name
	local ove_0_15 = ove_0_13:spellSlot(1).name
	local ove_0_16 = ove_0_13:spellSlot(2).name
	local ove_0_17 = ove_0_13:spellSlot(3).name

	if ove_0_13.charName == "Udyr" then
		ove_0_14 = ove_0_14:gsub("Stance", "")
		ove_0_15 = ove_0_15:gsub("Stance", "")
		ove_0_16 = ove_0_16:gsub("Stance", "")
		ove_0_17 = ove_0_17:gsub("Stance", "")
	end

	if ove_0_13.charName == "Annie" then
		ove_0_12.dodge:boolean("stunannieallspell", "Dodge " .. ove_0_13.charName .. "(All Stun Spell)", true)
	end

	if ove_0_10(ove_0_11, ove_0_14) and ove_0_14:lower() ~= "jarvanivdragonstrike" then
		ove_0_12.dodge:boolean(ove_0_14:lower(), "Dodge " .. ove_0_13.charName .. "(Q)", true)
	elseif ove_0_14:lower() == "blindmonkqone" or ove_0_14:lower() == "blindmonkqtwo" then
		ove_0_12.dodge:boolean("blindmonkqtwo", "Dodge " .. ove_0_13.charName .. "(Q2)", false)
	elseif ove_0_14:lower() == "aatroxq" or ove_0_14:lower() == "aatroxq2" or ove_0_14:lower() == "aatroxq3" then
		ove_0_12.dodge:boolean("allaatroxq", "Dodge " .. ove_0_13.charName .. "(Q Knockup)", true)
	elseif ove_0_14:lower() == "brandq" then
		ove_0_12.dodge:boolean("brandq", "Dodge " .. ove_0_13.charName .. "(Q If Stun)", true)
	elseif ove_0_14:lower() == "sionq" then
		ove_0_12.dodge:boolean("sionq", "Dodge " .. ove_0_13.charName .. "(Q If Knockup)", true)
	elseif ove_0_14:lower() == "tahmkenchq" then
		ove_0_12.dodge:boolean("tahmkenchq", "Dodge " .. ove_0_13.charName .. "(Q If Stun)", false)
	elseif ove_0_14:lower() == "jayceshockblast" or ove_0_14:lower() == "jaycetotheskies" then
		ove_0_12.dodgeo:boolean("jaycetotheskies", "Dodge " .. ove_0_13.charName .. "(Q2)", true)
	elseif ove_0_14:lower() == "jarvanivdragonstrike" then
		ove_0_12.dodge:boolean("jarvanivdragonstrike", "Dodge " .. ove_0_13.charName .. "(EQ)", true)
	elseif ove_0_14:lower() == "xinzhaoq" then
		ove_0_12.dodge:boolean("xinzhaoq", "Dodge " .. ove_0_13.charName .. "(Q3)", true)
	elseif ove_0_14:lower() == "vladimirq" then
		ove_0_12.dodgeo:boolean("vladimirq", "Dodge " .. ove_0_13.charName .. "(Q3)", false)
	elseif ove_0_14:lower() == "yasuoq1wrapper" or ove_0_14:lower() == "yasuoq2wrapper" or ove_0_14:lower() == "yasuoq3wrapper" then
		ove_0_12.dodge:boolean("yasuoq3wrapper", "Dodge " .. ove_0_13.charName .. "(Q3)", true)
		ove_0_12.dodgeo:boolean("yasuoq1wrapper", "Dodge " .. ove_0_13.charName .. "(Q)", false)
	elseif ove_0_14:lower() == "yoneq" or ove_0_14:lower() == "yoneq3" then
		ove_0_12.dodge:boolean("yoneq3", "Block " .. ove_0_13.charName .. "(Q3)", true)
		ove_0_12.dodgeo:boolean("yoneq", "Block " .. ove_0_13.charName .. "(Q)", false)
	else
		ove_0_12.dodgeo:boolean(ove_0_14:lower(), "Dodge " .. ove_0_13.charName .. "(Q)", false)
	end

	if ove_0_10(ove_0_11, ove_0_15) then
		ove_0_12.dodge:boolean(ove_0_15:lower(), "Dodge " .. ove_0_13.charName .. "(W)", true)
	elseif ove_0_15:lower() == "renektonpreexecute" then
		ove_0_12.dodge:boolean("renektonpreexecute", "Dodge " .. ove_0_13.charName .. "(W)", true)
	elseif ove_0_15:lower() == "pickacard" then
		ove_0_12.dodge:boolean("pickacard", "Dodge " .. ove_0_13.charName .. "(Gold Card)", true)
	elseif ove_0_15:lower() == "galiow" then
		ove_0_12.dodge:boolean("galiow2", "Dodge " .. ove_0_13.charName .. "(W2)", true)
	elseif ove_0_15:lower() == "sionw" then
		ove_0_12.dodgeo:boolean("sionwdetonate", "Dodge " .. ove_0_13.charName .. "(W2)", true)
	elseif ove_0_15:lower() == "gnarbigw" or ove_0_15:lower() == "gnarw" then
		ove_0_12.dodge:boolean("gnarbigw", "Dodge " .. ove_0_13.charName .. "(W)", true)
	elseif ove_0_15:lower() == "ireliaw2" or ove_0_15:lower() == "ireliaw" then
		ove_0_12.dodgeo:boolean("ireliaw2", "Dodge " .. ove_0_13.charName .. "(W)", true)
	else
		ove_0_12.dodgeo:boolean(ove_0_15:lower(), "Dodge " .. ove_0_13.charName .. "(W)", false)
	end

	if ove_0_10(ove_0_11, ove_0_16) then
		ove_0_12.dodge:boolean(ove_0_16:lower(), "Dodge " .. ove_0_13.charName .. "(E)", true)
	elseif ove_0_16:lower() == "ireliae2" or ove_0_16:lower() == "ireliae" then
		ove_0_12.dodge:boolean("ireliae2", "Dodge " .. ove_0_13.charName .. "(E)", true)
	elseif ove_0_16:lower() == "jayceaccelerationgate" or ove_0_16:lower() == "jaycethunderingblow" then
		ove_0_12.dodge:boolean("jaycethunderingblow", "Dodge " .. ove_0_13.charName .. "(E2)", true)
	elseif ove_0_16:lower() == "camillee" then
		ove_0_12.dodge:boolean("camilleedash2", "Dodge " .. ove_0_13.charName .. "(E2)", true)
	elseif ove_0_13.charName ~= "Gangplank" then
		ove_0_12.dodgeo:boolean(ove_0_16:lower(), "Dodge " .. ove_0_13.charName .. "(E)", false)
	end

	if ove_0_10(ove_0_11, ove_0_17) and ove_0_13.charName ~= "Jayce" and ove_0_13.charName ~= "Elise" and ove_0_13.charName ~= "Nidalee" and ove_0_13.charName ~= "Karma" then
		ove_0_12.dodge:boolean(ove_0_17:lower(), "Dodge " .. ove_0_13.charName .. "(R)", true)
	elseif ove_0_13.charName ~= "Jayce" and ove_0_13.charName ~= "Elise" and ove_0_13.charName ~= "Sivir" and ove_0_13.charName ~= "Nidalee" and ove_0_13.charName ~= "Karma" then
		ove_0_12.dodgeo:boolean(ove_0_17:lower(), "Dodge " .. ove_0_13.charName .. "(R)", false)
	end
end

return ove_0_12

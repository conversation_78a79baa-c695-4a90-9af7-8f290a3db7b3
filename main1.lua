local ver = game.version



function l(code)
   return code:gsub('..', function(h)
        return string.char((tonumber(h,16)+256-66)%256)
   end)
end


local z = 737
local v = l(tostring(z).."3")

if hanbot.language == 1 then
--print(v,ver)
if ver:find("13.19")then

module.load(header.id, player.charName:lower()..'/main')
module.load(header.id, player.charName..'/main')

print('Loading3 successful!')
chat.clear()
chat.add('[Moon]', {color = '7df9ff', bold = false, italic = false})
chat.add(' Loading..', {color = '7df9ff', bold = true})
chat.print()
chat.add('[Moon]', {color = '7df9ff', bold = false, italic = false})
chat.add(' Loading successfull!', {color = '7df9ff', bold = true})
chat.print()
chat.add('[<PERSON>]', {color = '#fff600', bold = false, italic = false})
chat.add('[<PERSON> Ezreal]', {color = '#c317fa', bold = true})
chat.print()


chat.clear()
--chat.add('Moon Ezreal.load', {color = '#6bFFFF', bold = true,size = "30",})
chat.print()
else
chat.clear()
chat.add('Moon Ezreal.Error', {color = '#FF0000', bold = true,size = "25",})
chat.print()
chat.add('Packet_Cast_Error', {color = '#FF0000',size = "30", bold = true,})
chat.print()
end
else
module.load(header.id, player.charName:lower()..'/main')


chat.clear()
chat.add('Moon Ezreal.load', {color = '#6bFFFF', bold = true,size = "30",})
chat.print()
end


local ove_0_10 = module.internal("pred")
local ove_0_11 = module.internal("TS")
local ove_0_12 = module.load(header.id, "common/common")
local ove_0_13 = module.load(header.id, "plugins/Zac/menu")
local ove_0_14 = {
	range = 300,
	last = 0,
	last_cast = 0,
	slot = player:spellSlot(_R),
	interrupt_data = {},
	predinput = {
		radius = 300,
		dashRadius = 0,
		boundingRadiusModTarget = 1,
		delay = 0.25,
		boundingRadiusModSource = 0
	}
}

function ove_0_14.is_ready()
	return ove_0_14.slot.state == 0
end

function ove_0_14.invoke_action()
	player:castSpell("self", _R)
end

function ove_0_14.get_action_state()
	if ove_0_14.is_ready() then
		return ove_0_14.get_prediction()
	end
end

function ove_0_14.is_jump()
	if ove_0_14.last_cast > 0 and game.time < ove_0_14.last_cast + 3 then
		return true
	end

	return false
end

function ove_0_14.invoke_interrupt_spells()
	if not ove_0_14.interrupt_data.owner then
		return false
	end

	if player.pos:dist(ove_0_14.interrupt_data.owner.pos) > 300 + ove_0_14.interrupt_data.owner.boundingRadius then
		return false
	end

	if game.time - ove_0_14.interrupt_data.channel >= ove_0_14.interrupt_data.start then
		ove_0_14.interrupt_data.owner = false

		return false
	end

	if game.time >= ove_0_14.interrupt_data.start and ove_0_10.present.get_prediction(ove_0_14.predinput, ove_0_14.interrupt_data.owner) then
		player:castSpell("self", _R)

		return true
	end
end

function ove_0_14.interrupt_data_spell(arg_10_0)
	if arg_10_0 and arg_10_0.owner and arg_10_0.owner.type == TYPE_HERO and arg_10_0.owner.team == TEAM_ENEMY then
		local slot_10_0 = string.lower(arg_10_0.owner.charName)

		if ove_0_12.interrupt_spells[slot_10_0] then
			for iter_10_0 = 1, #ove_0_12.interrupt_spells[slot_10_0] do
				local slot_10_1 = ove_0_12.interrupt_spells[slot_10_0][iter_10_0]

				if slot_10_1 and slot_10_1.spellname == string.lower(arg_10_0.name) and slot_10_1.slot == arg_10_0.slot and not arg_10_0.owner.buff.karthusdeathdefiedbuff and ove_0_13.misc.interrupt.interruptmenu[arg_10_0.owner.charName .. slot_10_1.menuslot]:get() then
					ove_0_14.interrupt_data.start = game.time - network.latency
					ove_0_14.interrupt_data.channel = slot_10_1.channelduration
					ove_0_14.interrupt_data.owner = arg_10_0.owner
				end
			end
		end
	end
end

function ove_0_14.get_prediction()
	if ove_0_14.last == game.time then
		return ove_0_14.result
	end

	ove_0_14.last = game.time
	ove_0_14.result = nil

	local slot_11_0 = ove_0_11.loop(function(arg_12_0, arg_12_1, arg_12_2)
		if arg_12_2 < ove_0_14.predinput.radius and ove_0_10.present.get_prediction(ove_0_14.predinput, arg_12_1) then
			arg_12_0.num_hits = arg_12_0.num_hits and arg_12_0.num_hits + 1 or 1
		end
	end)

	if slot_11_0.num_hits and slot_11_0.num_hits >= ove_0_13.combo.r.min_enemies:get() then
		ove_0_14.result = slot_11_0.num_hits

		return ove_0_14.result
	end

	return ove_0_14.result
end

function ove_0_14.on_draw()
	if ove_0_14.slot.level > 0 then
		graphics.draw_circle(player.pos, 300, 1, ove_0_13.draws.r:get(), 100)
	end
end

return ove_0_14

local ove_0_0 = 11274
local ove_0_1 = 11274
local ove_0_2 = 11274
local ove_0_3 = 11274
local ove_0_4 = 11274
local ove_0_5 = module.internal("TS")
local ove_0_6 = module.internal("orb")
local ove_0_7 = module.internal("pred")
local ove_0_8 = module.load(header.id,"xerath/menu")
local ove_0_9 = player:spellSlot(2)
local ove_0_10 = 1000000
local ove_0_11 = 122500
local ove_0_12 = {
	speed = 1400,
	delay = 0.25,
	boundingRadiusMod = 1,
	width = 70,
	collision = {
		minion = true,
		hero = false
	}
}

local function ove_0_13(arg_1_0, arg_1_1, arg_1_2)
	-- print 1
	if arg_1_2 > 2000 then
		return
	end

	local slot_1_0 = ove_0_7.linear.get_prediction(ove_0_12, arg_1_1)

	if slot_1_0 and slot_1_0.startPos:distSqr(slot_1_0.endPos) < ove_0_10 and not ove_0_7.collision.get_prediction(ove_0_12, slot_1_0, arg_1_1) then
		arg_1_0.obj = arg_1_1
		arg_1_0.seg = slot_1_0

		return true
	end
end

local ove_0_14 = {}

local function ove_0_15()
	-- print 2
	if ove_0_14.seg.startPos:distSqr(ove_0_14.seg.endPos) < ove_0_11 then
		return true
	end

	if ove_0_7.trace.linear.hardlock(ove_0_12, ove_0_14.seg, ove_0_14.obj) then
		return true
	end

	if ove_0_7.trace.linear.hardlockmove(ove_0_12, ove_0_14.seg, ove_0_14.obj) then
		return true
	end

	if ove_0_7.trace.newpath(ove_0_14.obj, 0.033, 0.5) then
		return true
	end
end

local function ove_0_16()
	-- print 3
	if ove_0_9.state == 0 then
		ove_0_14 = ove_0_5.get_result(ove_0_13)

		if ove_0_14.obj then
			return ove_0_15()
		end
	end
end

local function ove_0_17()
	-- print 4
	if player:castSpell("pos", 2, vec3(ove_0_14.seg.endPos.x, ove_0_14.obj.y, ove_0_14.seg.endPos.y)) then
		ove_0_6.core.set_server_pause()
	end
end
return {
	get_action_state = ove_0_16,
	invoke_action = ove_0_17
}

local ove_0_5 = module.internal("orb")
local ove_0_6 = module.internal("pred")
local ove_0_7 = module.load(header.id,"xerath/menu")
local ove_0_8 = module.load(header.id,"xerath/q")
local ove_0_9 = module.load(header.id,"xerath/q_buff")
local ove_0_10 = module.load(header.id,"xerath/w")
local ove_0_11 = module.internal("damagelib")

local clear = {}

-- 定义Q技能清线数据
clear.q = {
    width = 145,
    range = 1450,
    damage = function(minion)
        return ove_0_11.get_spell_damage('XerathArcanopulseChargeUp', 0, player, minion, false, 0)
    end
}

-- 定义W技能清线数据
clear.w = {
    range = 990,
    radius = 275,
    damage = function(minion)
        return ove_0_11.get_spell_damage('XerathArcaneBarrage2', 1, player, minion, false, 0)
    end,
    damageCenter = function(minion)
        return ove_0_11.get_spell_damage('XerathArcaneBarrage2', 1, player, minion, false, 0) * 1.5
    end
}

-- 获取范围内的小兵
function clear.GetMinions(range, team)
    local result = {}
    for i = 0, objManager.minions.size[team] - 1 do
        local minion = objManager.minions[team][i]
        if minion and minion.isVisible and not minion.isDead and minion.health > 0 and minion.isTargetable and minion.pos:dist(player.pos) < range then
            result[#result + 1] = minion
        end
    end
    return result
end

-- 获取Q技能当前充能范围
function clear.get_q_charged_range()
    local charge_time = os.clock() - ove_0_9.start + network.latency - 0.09
    return math.min(ove_0_8.range.max, ove_0_8.range.min + charge_time / ove_0_8.charge.time * ove_0_8.range.min)
end

-- 检查法力值是否足够
function clear.mana_check()
    local manaPercent = player.mana / player.maxMana * 100
    return manaPercent >= ove_0_7.clear_mana:get()
end

-- Q技能清线函数
function clear.q_clear()
    -- 检查法力值是否足够
    local manaPercent = player.mana / player.maxMana * 100
    if manaPercent < ove_0_7.clear_mana:get() then
        return false
    end
    
    -- 检查Q技能是否已启用清线
    if not ove_0_7.q_clear:get() then
        return false
    end
    
    -- 如果Q技能已处于充能状态，寻找最佳释放方向
    if ove_0_9.isActive then
        local currentRange = clear.get_q_charged_range()
        local minions = clear.GetMinions(currentRange, TEAM_ENEMY)
        local jungle = clear.GetMinions(currentRange, TEAM_NEUTRAL)
        
        -- 合并小兵和野怪
        for _, minion in pairs(jungle) do
            minions[#minions + 1] = minion
        end
        
        if #minions == 0 then
            -- 如果没有小兵，继续保持充能状态直到超时
            return false
        end
        
        -- 寻找最佳释放方向（能命中最多小兵的方向）
        local bestPosition = nil
        local maxHitCount = 0
        
        for _, minion in pairs(minions) do
            local hitCount = 0
            local minionPos = minion.pos
            
            -- 计算每个方向可以命中的小兵数
            for _, target in pairs(minions) do
                -- 检查目标是否在射线上
                local p = mathf.closest_vec_line(target.pos2D, player.pos2D, minion.pos2D)
                local dist = target.pos2D:dist(p)
                
                if dist <= clear.q.width/2 + target.boundingRadius then
                    hitCount = hitCount + 1
                end
            end
            
            -- 更新最佳释放方向
            if hitCount > maxHitCount and minionPos:dist(player.pos) <= currentRange then
                maxHitCount = hitCount
                bestPosition = minionPos
            end
        end
        
        -- 如果找到满足条件的释放方向，释放Q技能
        if bestPosition and maxHitCount >= ove_0_7.q_clear_minions:get() then
            if not ove_0_5.core.is_spell_locked() then
                player:castSpell("release", 0, bestPosition)
                -- 减少暂停时间，让角色更快恢复移动和攻击
                ove_0_5.core.set_server_pause(0.1)
                return true
            end
        else
            -- 如果找不到满足条件的目标，继续保持充能状态
        end
        
        return false
    else
        -- Q技能未处于充能状态，检查是否有足够多的小兵可以命中
        if ove_0_8.is_ready() and not ove_0_5.core.is_spell_locked() then
            local minions = clear.GetMinions(ove_0_8.range.max, TEAM_ENEMY)
            local jungle = clear.GetMinions(ove_0_8.range.max, TEAM_NEUTRAL)
            
            -- 合并小兵和野怪
            for _, minion in pairs(jungle) do
                minions[#minions + 1] = minion
            end
            
            if #minions == 0 then
                return false
            end
            
            -- 寻找最佳充能方向
            local bestMinion = nil
            local maxHitCount = 0
            
            for _, minion in pairs(minions) do
                local hitCount = 0
                local minionPos = minion.pos
                
                -- 计算每个方向可以命中的小兵数
                for _, target in pairs(minions) do
                    local p = mathf.closest_vec_line(target.pos2D, player.pos2D, minion.pos2D)
                    local dist = target.pos2D:dist(p)
                    
                    if dist <= clear.q.width/2 + target.boundingRadius then
                        hitCount = hitCount + 1
                    end
                end
                
                -- 更新最佳释放目标
                if hitCount > maxHitCount then
                    maxHitCount = hitCount
                    bestMinion = minion
                end
            end
            
            -- 如果找到满足条件的目标，开始充能Q
            if bestMinion and maxHitCount >= ove_0_7.q_clear_minions:get() then
                player:castSpell("pos", 0, bestMinion.pos)
                -- 充能Q技能后，不需要立即移动，让脚本控制施放
                ove_0_5.core.set_server_pause(0.1)
                return true
            else
                return false
            end
        end
    end
    
    return false
end

-- 改进的W技能清线功能
function clear.w_clear()
    -- 检查法力值是否足够
    local manaPercent = player.mana / player.maxMana * 100
    if manaPercent < ove_0_7.clear_mana:get() then
        return false
    end
    
    -- 检查W技能是否已启用清线且已就绪
    if not ove_0_7.w_clear:get() or not ove_0_10.is_ready() or ove_0_5.core.is_spell_locked() then
        return false
    end
    
    -- 获取敌方小兵
    local minionsInRange = clear.GetMinions(990, TEAM_ENEMY)
    
    -- 改进的W清线逻辑，寻找能命中最多小兵的位置
    if #minionsInRange > 0 then
        local bestPos = nil
        local maxHitCount = 0
        
        for _, minion in pairs(minionsInRange) do
            local hitCount = 0
            
            -- 计算W技能命中的小兵数量
            for _, target in pairs(minionsInRange) do
                if target.pos:dist(minion.pos) <= clear.w.radius then
                    hitCount = hitCount + 1
                end
            end
            
            if hitCount > maxHitCount then
                maxHitCount = hitCount
                bestPos = minion.pos
            end
        end
        
        -- 如果找到满足条件的位置，释放W技能
        if bestPos and maxHitCount >= ove_0_7.w_clear_minions:get() then
            player:castSpell("pos", 1, bestPos)
            -- 减少暂停时间，让角色更快恢复移动和攻击
            ove_0_5.core.set_server_pause(0.1)
            return true
        end
    end
    
    -- 获取野怪
    local jungleMinions = clear.GetMinions(990, TEAM_NEUTRAL)
    if #jungleMinions > 0 then
        -- 对野怪直接使用W
        player:castSpell("pos", 1, jungleMinions[1].pos)
        -- 减少暂停时间，让角色更快恢复移动和攻击
        ove_0_5.core.set_server_pause(0.1)
        return true
    end
    
    return false
end

-- 主清线函数
function clear.get_action()
    if not ove_0_7.clear_key:get() then
        return false
    end
    
    -- 检查施放技能的逻辑
    local used_ability = false
    
    -- 优先使用Q技能清线
    if clear.q_clear() then
        used_ability = true
    -- 然后尝试使用W技能清线
    elseif clear.w_clear() then
        used_ability = true
    end
    
    -- 如果没有使用技能，尝试普通攻击小兵
    if not used_ability and not ove_0_5.core.is_spell_locked() and ove_0_5.core.can_attack() then
        -- 获取orbwalker目标
        local target = ove_0_5.combat.get_target()
        if target then
            player:attack(target)
            return true
        end
        
        -- 如果orbwalker没有目标，寻找最近的小兵
        local minions = clear.GetMinions(player.attackRange, TEAM_ENEMY)
        if #minions > 0 then
            -- 查找可击杀的小兵
            local bestMinion = nil
            local lowestHealth = math.huge
            
            for _, minion in pairs(minions) do
                if minion.health < lowestHealth then
                    lowestHealth = minion.health
                    bestMinion = minion
                end
            end
            
            if bestMinion then
                player:attack(bestMinion)
                return true
            end
        end
    end
    
    -- 如果没有目标可攻击且可以移动，则移动
    if ove_0_5.core.can_move() and not ove_0_5.core.is_spell_locked() then
        player:move(mousePos)
    end
    
    return used_ability
end

return clear 
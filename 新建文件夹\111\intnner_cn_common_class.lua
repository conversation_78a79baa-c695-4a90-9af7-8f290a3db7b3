
local ove_0_10 = {
	_VERSION = "middleclass v4.1.1",
	_URL = "https://github.com/kikito/middleclass",
	_DESCRIPTION = "Object Orientation for Lua",
	_LICENSE = "    MIT LICENSE\n\n    Copyright (c) 2011 <PERSON>\n\n    Permission is hereby granted, free of charge, to any person obtaining a\n    copy of this software and associated documentation files (the\n    \"Software\"), to deal in the Software without restriction, including\n    without limitation the rights to use, copy, modify, merge, publish,\n    distribute, sublicense, and/or sell copies of the Software, and to\n    permit persons to whom the Software is furnished to do so, subject to\n    the following conditions:\n\n    The above copyright notice and this permission notice shall be included\n    in all copies or substantial portions of the Software.\n\n    THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n    OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n    MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.\n    IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY\n    CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,\n    TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE\n    SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n  "
}

local function ove_0_11(arg_5_0, arg_5_1)
	if arg_5_1 == nil then
		return arg_5_0.__instanceDict
	else
		return function(arg_6_0, arg_6_1)
			local slot_6_0 = arg_5_0.__instanceDict[arg_6_1]

			if slot_6_0 ~= nil then
				return slot_6_0
			elseif type(arg_5_1) == "function" then
				return (arg_5_1(arg_6_0, arg_6_1))
			else
				return arg_5_1[arg_6_1]
			end
		end
	end
end

local function ove_0_12(arg_7_0, arg_7_1, arg_7_2)
	arg_7_2 = arg_7_1 == "__index" and ove_0_11(arg_7_0, arg_7_2) or arg_7_2
	arg_7_0.__instanceDict[arg_7_1] = arg_7_2

	for iter_7_0 in pairs(arg_7_0.subclasses) do
		if rawget(iter_7_0.__declaredMethods, arg_7_1) == nil then
			ove_0_12(iter_7_0, arg_7_1, arg_7_2)
		end
	end
end

local function ove_0_13(arg_8_0, arg_8_1, arg_8_2)
	arg_8_0.__declaredMethods[arg_8_1] = arg_8_2

	if arg_8_2 == nil and arg_8_0.super then
		arg_8_2 = arg_8_0.super.__instanceDict[arg_8_1]
	end

	ove_0_12(arg_8_0, arg_8_1, arg_8_2)
end

local function ove_0_14(arg_9_0)
	return "class " .. arg_9_0.name
end

local function ove_0_15(arg_10_0, ...)
	return arg_10_0:new(...)
end

local function ove_0_16(arg_11_0, arg_11_1)
	local slot_11_0 = {}

	slot_11_0.__index = slot_11_0

	local slot_11_1 = {
		name = arg_11_0,
		super = arg_11_1,
		static = {},
		__instanceDict = slot_11_0,
		__declaredMethods = {},
		subclasses = setmetatable({}, {
			__mode = "k"
		})
	}

	if arg_11_1 then
		setmetatable(slot_11_1.static, {
			__index = function(arg_12_0, arg_12_1)
				local slot_12_0 = rawget(slot_11_0, arg_12_1)

				if slot_12_0 == nil then
					return arg_11_1.static[arg_12_1]
				end

				return slot_12_0
			end
		})
	else
		setmetatable(slot_11_1.static, {
			__index = function(arg_13_0, arg_13_1)
				return rawget(slot_11_0, arg_13_1)
			end
		})
	end

	setmetatable(slot_11_1, {
		__index = slot_11_1.static,
		__tostring = ove_0_14,
		__call = ove_0_15,
		__newindex = ove_0_13
	})

	return slot_11_1
end

local function ove_0_17(arg_14_0, arg_14_1)
	assert(type(arg_14_1) == "table", "mixin must be a table")

	for iter_14_0, iter_14_1 in pairs(arg_14_1) do
		if iter_14_0 ~= "included" and iter_14_0 ~= "static" then
			arg_14_0[iter_14_0] = iter_14_1
		end
	end

	for iter_14_2, iter_14_3 in pairs(arg_14_1.static or {}) do
		arg_14_0.static[iter_14_2] = iter_14_3
	end

	if type(arg_14_1.included) == "function" then
		arg_14_1:included(arg_14_0)
	end

	return arg_14_0
end

local ove_0_18 = {
	__tostring = function(arg_15_0)
		return "instance of " .. tostring(arg_15_0.class)
	end,
	initialize = function(arg_16_0, ...)
		return
	end,
	isInstanceOf = function(arg_17_0, arg_17_1)
		return type(arg_17_1) == "table" and type(arg_17_0) == "table" and (arg_17_0.class == arg_17_1 or type(arg_17_0.class) == "table" and type(arg_17_0.class.isSubclassOf) == "function" and arg_17_0.class:isSubclassOf(arg_17_1))
	end,
	static = {
		allocate = function(arg_18_0)
			assert(type(arg_18_0) == "table", "Make sure that you are using 'Class:allocate' instead of 'Class.allocate'")

			return setmetatable({
				class = arg_18_0
			}, arg_18_0.__instanceDict)
		end,
		new = function(arg_19_0, ...)
			assert(type(arg_19_0) == "table", "Make sure that you are using 'Class:new' instead of 'Class.new'")

			local slot_19_0 = arg_19_0:allocate()

			slot_19_0:initialize(...)

			return slot_19_0
		end,
		subclass = function(arg_20_0, arg_20_1)
			assert(type(arg_20_0) == "table", "Make sure that you are using 'Class:subclass' instead of 'Class.subclass'")
			assert(type(arg_20_1) == "string", "You must provide a name(string) for your class")

			local slot_20_0 = ove_0_16(arg_20_1, arg_20_0)

			for iter_20_0, iter_20_1 in pairs(arg_20_0.__instanceDict) do
				ove_0_12(slot_20_0, iter_20_0, iter_20_1)
			end

			function slot_20_0.initialize(arg_21_0, ...)
				return arg_20_0.initialize(arg_21_0, ...)
			end

			arg_20_0.subclasses[slot_20_0] = true

			arg_20_0:subclassed(slot_20_0)

			return slot_20_0
		end,
		subclassed = function(arg_22_0, arg_22_1)
			return
		end,
		isSubclassOf = function(arg_23_0, arg_23_1)
			return type(arg_23_1) == "table" and type(arg_23_0.super) == "table" and (arg_23_0.super == arg_23_1 or arg_23_0.super:isSubclassOf(arg_23_1))
		end,
		include = function(arg_24_0, ...)
			assert(type(arg_24_0) == "table", "Make sure you that you are using 'Class:include' instead of 'Class.include'")

			for iter_24_0, iter_24_1 in ipairs({
				...
			}) do
				ove_0_17(arg_24_0, iter_24_1)
			end

			return arg_24_0
		end
	}
}

function ove_0_10.class(arg_25_0, arg_25_1)
	assert(type(arg_25_0) == "string", "A name (string) is needed for the new class")

	return arg_25_1 and arg_25_1:subclass(arg_25_0) or ove_0_17(ove_0_16(arg_25_0), ove_0_18)
end

setmetatable(ove_0_10, {
	__call = function(arg_26_0, ...)
		return ove_0_10.class(...)
	end
})

return ove_0_10

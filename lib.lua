local GZ = {}
local pred = module.internal("pred")
GZ.ObjMinion = objManager.minions
GZ.ObjTurrets = objManager.turrets
local util = {spell = true}

--调试输出
function GZ.print(text,color)
  local color = color or '#96F500'
  chat.clear()
  chat.add(text, {color = color, bold = true,})
  chat.print()
end

--延迟释放call
local delayedActions, delayedActionsExecuter = {}, nil
function GZ.DelayAction(func, delay, args) --以秒为单位的延迟
  if not delayedActionsExecuter then
    function delayedActionsExecuter()
      for t, funcs in pairs(delayedActions) do
        if t <= os.clock() then
          for i = 1, #funcs do
            local f = funcs[i]
            if f and f.func then
              f.func(unpack(f.args or {}))
            end
          end
          delayedActions[t] = nil
        end
      end
    end
    cb.add(cb.tick, delayedActionsExecuter)
  end
  local t = os.clock() + (delay or 0)
  if delayedActions[t] then
    delayedActions[t][#delayedActions[t] + 1] = {func = func, args = args}
  else
    delayedActions[t] = {{func = func, args = args}}
  end
end

--返回血量百分比
function GZ.GetHP(obj)
    local obj = obj or player
    return (obj.health / obj.maxHealth) * 100
end

--返回法力值百分比
function GZ.GetMANA(obj)
    local obj = obj or player
    return (obj.mana / obj.maxMana) * 100
end

--返回能量百分比或其他 --怒气值未测试
function GZ.GetPAR(obj)
    local obj = obj or player
    return (obj.par / obj.maxPar) * 100
end

--BUFF检查 是否有效BUFF
function GZ.CheckBuff(obj, buffname)
    if obj then
        for i = 0, obj.buffManager.count - 1 do
            local buff = obj.buffManager:get(i)
            if buff and buff.valid and string.lower(buff.name) == string.lower(buffname) and (buff.stacks > 0 or buff.stacks2 > 0) then
                return true
            end
        end
        return false
    end
end

--BUFF堆笺检查
function GZ.CheckBuffStacks(obj, buffname)
  if obj then
      for i = 0, obj.buffManager.count - 1 do
          local buff = obj.buffManager:get(i)
          if buff and buff.valid and string.lower(buff.name) == string.lower(buffname) and (buff.stacks > 0 or buff.stacks2 > 0) then
              return buff.stacks
          end
      end
      return 0
  end
end

--BUFF堆笺检查2
function GZ.CheckBuffStacks2(obj, buffname)
  if obj then
      for i = 0, obj.buffManager.count - 1 do
          local buff = obj.buffManager:get(i)
          if buff and buff.valid and string.lower(buff.name) == string.lower(buffname) and (buff.stacks > 0 or buff.stacks2 > 0) then
              return buff.stacks2
          end
      end
      return 0
  end
end

--BUFF堆栈检查 例如VN-W 刀妹Q 
function GZ.CountBuff(obj, buffname)
    local game_time = game.time
    if obj then
      for i = 0, obj.buffManager.count - 1 do
        local buff = obj.buffManager:get(i)
  
        if buff and buff.valid and string.lower(buff.name) == string.lower(buffname) and game_time < buff.endTime then
          if buff.stacks2 > 0 then
            return buff.stacks2
          end
          if buff.stacks > 0 then
            return buff.stacks
          end
        end
      end
    end
    return 0
end

--BUFF类型检查
function GZ.CheckBuffType(obj, bufftype)
  if obj then
    for i = 0, obj.buffManager.count - 1 do
      local buff = obj.buffManager:get(i)
      if buff and buff.valid and buff.type == bufftype and (buff.stacks > 0 or buff.stacks2 > 0) then
        return true
      end
    end
  end
end

-- 返回总攻击力
function GZ.GetAllAD(obj)
    local obj = obj or player
    return (obj.baseAttackDamage + obj.flatPhysicalDamageMod) * obj.percentPhysicalDamageMod
end

  -- 返回加成攻击力
function GZ.GetAddAD(obj)
    local obj = obj or player
    return ((obj.baseAttackDamage + obj.flatPhysicalDamageMod) * obj.percentPhysicalDamageMod) - obj.baseAttackDamage
end

  -- 返回总法术强度
function GZ.GetAllAP(obj)
    local obj = obj or player
    return obj.flatMagicDamageMod * obj.percentMagicDamageMod
end

  -- 物理伤害计算公式
function GZ.PhysicalReduction(target, damageSource)
    local damageSource = damageSource or player
    local armor =
      ((target.bonusArmor * damageSource.percentBonusArmorPenetration) + (target.armor - target.bonusArmor)) * damageSource.percentArmorPenetration
    local lethality = (damageSource.physicalLethality * .4) + ((damageSource.physicalLethality * .6) * (damageSource.levelRef / 18))
    return armor >= 0 and (100 / (100 + (armor - lethality))) or (2 - (100 / (100 - (armor - lethality))))
end

  -- 魔法伤害计算公式
function GZ.MagicReduction(target, damageSource)
    local damageSource = damageSource or player
    local magicResist = (target.spellBlock * damageSource.percentMagicPenetration) - damageSource.flatMagicPenetration
    return magicResist >= 0 and (100 / (100 + magicResist)) or (2 - (100 / (100 - magicResist)))
end

function GZ.DamageReduction(damageType, target, damageSource)
    local damageSource = damageSource or player
    local reduction = 1
    if damageType == "AD" then
    end
    if damageType == "AP" then
    end
    return reduction
end

  -- 返回AA可造成的伤害
function GZ.JSAADMG(target, damageSource)
    local damageSource = damageSource or player
    if target then
      return GZ.GetAllAD(damageSource) * GZ.PhysicalReduction(target, damageSource)
    end
    return 0
end

  -- 返回可造成的物理伤害
function GZ.JSWLDMG(target, damage, damageSource)
    local damageSource = damageSource or player
    if target then
      return (damage * GZ.PhysicalReduction(target, damageSource)) * GZ.DamageReduction("AD", target, damageSource)
    end
    return 0
end

  -- 返回可造成的魔法伤害
function GZ.JSMFDMG(target, damage, damageSource)
    local damageSource = damageSource or player
    if target then
      return (damage * GZ.MagicReduction(target, damageSource)) * GZ.DamageReduction("AP", target, damageSource)
    end
    return 0
end

  -- 返回AA范围
function GZ.GetAARange(target)
    return player.attackRange + player.boundingRadius + (target and target.boundingRadius or 0)
end

  -- 返回当前obj等级[参数可空 默认获取玩家自身]
function GZ.GetLv()
	local obj = obj or player
	return obj.levelRef
end

  -- 返回某个技能等级
function GZ.SPLv(spell)
  return player:spellSlot(spell).level
end

  -- 返回购买物品
function GZ.Buyitem(ID)
    return player:buyItem(ID)
end

--目标是否有效
function GZ.YXTarget(object)
  return (object and not object.isDead and object.isVisible and object.isTargetable and object.buff and not object.buff[BUFF_INVULNERABILITY] and not object.buff['sionpassivezombie'] and not object.buff['undyingrage'] and not object.buff["kindredrnodeathbuff"] and not object.buff['yuumiwattach'])
end

--小兵是否有效
function GZ.YXminion(object)
  return (object and not object.isDead and object.isVisible and object.isTargetable and object.maxHealth > 100 and object.moveSpeed > 0)
end

--延伸
function GZ.ext(source,target,range)
	return	source + (target - source):norm() * range
end

--文本绘制
function GZ.text(text,size,x,y,color)
  local size = size or 20
  local x = x or 100
  local y = y or 100
  local color = color or 0xFFFFFFFF
  graphics.draw_text_2D(text, size, x, y, color)
end

function GZ.HasItem(ID)
  for i = 0, 6 do
    if player:itemID(i) == ID then
      return true
    end
  end
  return false
end

--狗牌
function GZ.Ctrl6()
  return chat.send("/成就徽章")
end

--跳舞 --取消后摇
function GZ.Ctrl3()
  return chat.send("/d")
end

--大笑
function GZ.Ctrl4()
  return chat.send("/l")
end

-- 图标 -- 从上到下依次为 自动设置 - 连招设置 - 绘制设置 - 清线设置 - 按键设置 - 杂项设置[其他] - 预判设置 - 设置

function GZ.Ico_Auto()
  local Icon = graphics.sprite("icon/autoss.png")
  return Icon
end

function GZ.Ico_Combo()
  local Icon = graphics.sprite("icon/comboss.png")
  return Icon
end

function GZ.Ico_Draw()
  local Icon = graphics.sprite("icon/drawss.png")
  return Icon
end

function GZ.Ico_Farm()
  local Icon = graphics.sprite("icon/farmss.png")
  return Icon
end

function GZ.Ico_Key()
  local Icon = graphics.sprite("icon/keyss.png")
  return Icon
end

function GZ.Ico_Misc()
  local Icon = graphics.sprite("icon/miscss.png")
  return Icon
end

function GZ.Ico_Pred()
  local Icon = graphics.sprite("icon/predss.png")
  return Icon
end

function GZ.Ico_Title()
  local Icon = graphics.sprite("icon/titless.png")
  return Icon
end

function GZ.Ico_Char()
  local Icon = graphics.sprite("icon/CharIcon/" .. player.charName .. ".png")
  return Icon
end

function GZ.Ico_Square()
  return player.iconSquare
end

--是不是水
function GZ.Iswater(pos)
  if (navmesh.gameplay_area(pos) == 233898021 or navmesh.gameplay_area(pos) == 237443643) and not navmesh.isGrass(pos) and not navmesh.isWall(pos) then
    return true
  else
    return false
  end
end

--路径预判
function GZ.GetPredPos(obj, delay)
  if not GZ.YXTarget(obj) or not obj.path or not delay or not obj.moveSpeed then return obj end
  local pred_pos = pred.core.lerp(obj.path, network.latency + delay, obj.moveSpeed)
  return vec3(pred_pos.x, player.y, pred_pos.y)
end

--圆圈绘制
function GZ.drawYX(pos, range, color)
  local color = color or graphics.argb(255, 0, 255, 0)
  return graphics.draw_circle(pos, range, 2, color, 100)
end

-- BUFF类型
GZ.buff_types = {
  Internal = 0,
  Aura = 1,
  CombatEnchancer = 2,
  CombatDehancer = 3,
  SpellShield = 4,
  Stun = 5, --眩晕
  Invisibility = 6, --隐身
  Silence = 7, --沉默
  Taunt = 8, --嘲讽
  Berserk = 9, --狂暴
  Polymorph = 10, --变形
  Slow = 11, --减速
  Snare = 12, --陷阱
  Damage = 13, --破损--易损 类似奎因被动
  Heal = 14, --治疗--高回复 --蒙多R
  Haste = 15, --急速
  SpellImmunity = 16, --魔法免疫
  PhysicalImmunity = 17, --物理免疫
  Invulnerability = 18, --无懈可击--无敌--剑姬W
  AttackSpeedSlow = 19, --攻击速度慢
  NearSight = 20, --近视 --类似于奎因Q 男枪W 梦魇关灯
  Currency = 21, --
  Fear = 22, --恐惧
  Charm = 23, --魅惑--狐狸W 寡妇W
  Poison = 24, --中毒
  Suppression = 25, --压制 净化无法解除 ---水银可解
  Blind = 26, --致盲
  Counter = 27, --反击
  Shred = 28, --
  Flee = 29, --被恐惧逃跑 ---例如末日Q
  Knockup = 30, --击飞 ---石头人R
  Knockback = 31, --击退 ---VN E
  Disarm = 32, --缴械
  Grounded = 33, --禁锢
  Drowsy = 34, --昏睡 --佐伊E --莉莉娅R 应该是被技能命中,但是还没有进入睡眠状态
  Asleep = 35 --睡着了 --佐伊E --莉莉娅R
}

-- BuffType {
--   Internal = 0,
--   Aura = 1,
--   CombatEnchancer = 2,
--   CombatDehancer = 3,
--   SpellShield = 4,
--   Stun = 5, Immobile
--   Invisibility = 6,
--   Silence = 7,
--   Taunt = 8, Immobile
--   Berserk = 9, 
--   Polymorph = 10,
--   Slow = 11,
--   Snare = 12, Immobile
--   Damage = 13,
--   Heal = 14,
--   Haste = 15,
--   SpellImmunity = 16,
--   PhysicalImmunity = 17,
--   Invulnerability = 18,
--   AttackSpeedSlow = 19,
--   NearSight = 20,
--   Currency = 21,
--   Fear = 22, Immobile
--   Charm = 23, Immobile
--   Poison = 24,
--   Suppression = 25,
--   Blind = 26,
--   Counter = 27,
--   Shred = 28,
--   Flee = 29,
--   Knockup = 30, Immobile
--   Knockback = 31,
--   Disarm = 32,
--   Grounded = 33,
--   Drowsy = 34,
--   Asleep = 35, Immobile
--   Obscured = 36,
--   ClickProofToEnemies = 37,
--   Unkillable = 38
-- };

--判断技能名称
function GZ.SPname(spell)
	return player:spellSlot(spell).name
end

--判断技能是否就绪
function GZ.SPok(spell)
	return player:spellSlot(spell).state
end

function GZ.isq()
  if player:spellSlot(0).state == 0 then
		  return util.spell
  end
    return false
end

function GZ.isw()
  if player:spellSlot(1).state == 0 then
    return util.spell
  end
  return false
end

function GZ.ise()
  if player:spellSlot(2).state == 0 then
    return util.spell
  end
  return false
end

function GZ.isr()
  if player:spellSlot(3).state == 0 then
    return util.spell
  end
  return false
end

--直线技能预判
function GZ.lineP(seg,obj)
  local POS = pred.linear.get_prediction(seg, obj)
  if POS and POS.startPos:dist(POS.endPos) <= seg.range and POS.startPos:dist(POS.endPos) > 50 then
    return vec3(POS.endPos.x, obj.pos.y, POS.endPos.y),POS
  else
    return nil
  end
end

-- 在@delay秒后返回@obj预判pos
function GZ.GetPredictedPos(obj, delay)
  if not GZ.YXTarget(obj) or not obj.path or not delay or not obj.moveSpeed then
    return obj
  end
  if delay <= 0 then
	return obj.pos
  end
  if not obj.x or not obj.path or obj.path and not obj.path.x then
	return obj.path.serverPos
  end
  local pred_pos = pred.core.lerp(obj.path, network.latency + delay, obj.moveSpeed)
  return vec3(pred_pos.x, player.y, pred_pos.y)
end

--返回技能CD
function GZ.GetSpCD(spell)
  return player:spellSlot(spell).cooldown
end

return GZ

--xxxxxxxxxxxxx  正义 好一个冠冕堂皇之词   xxxxxxxxxxxxx
--xxxxxxxxxxxxx  且随疾风前行 身后亦需留心 xxxxxxxxxxxxx
--xxxxxxxxxxxxx  吾虽浪迹天涯 却未迷失本心 xxxxxxxxxxxxx
--xxxxxxxxxxxxx  你觉得你能杀死我 ！！！   xxxxxxxxxxxxx
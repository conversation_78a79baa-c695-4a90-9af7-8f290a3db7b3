

local ove_0_10 = module.internal("orb")
local ove_0_11 = module.internal("TS")
local ove_0_12 = module.internal("clipper")
local ove_0_13 = module.internal("pred")
local ove_0_14 = ove_0_12.polygon
local ove_0_15 = module.load(header.id, "common/common")
local ove_0_16 = module.load(header.id, "plugins/Zac/menu")
local ove_0_17 = module.load(header.id, "common/Compute/computer")
local ove_0_18 = {
	CastRangeGrowthEndTime = 4.5,
	CastRangeGrowthStartTime = 0,
	range = 1200,
	LastCastTime = 0,
	last = 0,
	CastRangeGrowthMax = 1200,
	CastRangeGrowthMin = 1.5,
	CastRangeGrowthDuration = 0.9,
	slot = player:spellSlot(_E),
	result = {},
	predinput = {
		radius = 100,
		delay = 0.25,
		boundingRadiusMod = 0,
		width = 50,
		speed = math.huge
	},
	Floor = function(arg_5_0)
		return math.floor(arg_5_0 * 100) * 0.01
	end
}

function ove_0_18.not_can_attack()
	if ove_0_18.is_charging_with_e() then
		return true
	end

	if not ove_0_10.core.can_attack() then
		return true
	end

	return false
end

function ove_0_18.invoke_action()
	if not ove_0_18.is_charging_with_e() then
		player:castSpell("pos", _E, ove_0_18.result.castPos:to3D())
	elseif ove_0_18.result.castPos:dist(player.pos2D) < ove_0_18.range - 150 then
		player:castSpell("release", _E, ove_0_18.result.castPos:to3D())
	end
end

function ove_0_18.is_ready()
	return ove_0_18.slot.state == 0
end

function ove_0_18.get_action_state()
	if ove_0_18.is_ready() then
		return ove_0_18.get_prediction()
	end
end

function ove_0_18.is_charging_with_e()
	if player:spellSlot(_E).isCharging or player.buff.zace or ove_0_18.LastCastTime > 0 and game.time < ove_0_18.LastCastTime then
		return true
	end

	return false
end

function ove_0_18.closestTurret(arg_11_0)
	local slot_11_0
	local slot_11_1 = math.huge

	for iter_11_0 = 0, objManager.turrets.size[TEAM_ENEMY] - 1 do
		local slot_11_2 = objManager.turrets[TEAM_ENEMY][iter_11_0]

		if slot_11_2 and not slot_11_2.isDead and slot_11_1 > arg_11_0:distSqr(slot_11_2.pos2D) then
			slot_11_1 = arg_11_0:distSqr(slot_11_2.pos2D)
			slot_11_0 = slot_11_2
		end
	end

	return slot_11_0, slot_11_1
end

function ove_0_18.getClosesPlayer()
	local slot_12_0
	local slot_12_1 = math.huge

	for iter_12_0, iter_12_1 in pairs(ove_0_15.getAllyHeroes()) do
		if iter_12_1 ~= nil and iter_12_1.ptr ~= player.ptr and ove_0_15.IsValidTarget(iter_12_1) then
			local slot_12_2 = ove_0_15.GetDistance(iter_12_1.pos, player.pos)

			if slot_12_2 < slot_12_1 then
				slot_12_0 = iter_12_1
				slot_12_1 = slot_12_2
			end
		end
	end

	return slot_12_0
end

function ove_0_18.cone(arg_13_0)
	local slot_13_0 = ove_0_14()
	local slot_13_1 = player.pos + (arg_13_0 - player.pos):norm() * (ove_0_15.GetDistance(arg_13_0, player) + 250)
	local slot_13_2 = player.pos:to2D()
	local slot_13_3 = (slot_13_1:to2D() - slot_13_2):norm()
	local slot_13_4 = 90 * math.pi / 180
	local slot_13_5 = 20
	local slot_13_6 = ove_0_18.range / math.cos(2 * math.pi / slot_13_5)
	local slot_13_7 = slot_13_3:rotate(-slot_13_4 * 0.5)

	slot_13_0:Add(slot_13_2)

	for iter_13_0 = 0, slot_13_5 do
		local slot_13_8 = slot_13_7:rotate(iter_13_0 * slot_13_4 / slot_13_5):norm()
		local slot_13_9 = vec2(slot_13_2.x + slot_13_6 * slot_13_8.x, slot_13_2.y + slot_13_6 * slot_13_8.y)

		slot_13_0:Add(slot_13_9)
	end

	return slot_13_0
end

function ove_0_18.trace_filter()
	local slot_14_0 = ove_0_18.cone(ove_0_18.result.castPos:to3D())

	if not ove_0_16.e_under_tower:get() and ove_0_15.isUnderEnemyTurret(ove_0_18.result.castPos:to3D()) then
		return false
	end

	if slot_14_0 and slot_14_0:Contains(ove_0_18.result.castPos) == 0 and slot_14_0:Contains(ove_0_18.result.obj.path.serverPos2D) ~= 0 then
		return true
	end

	if slot_14_0 and slot_14_0:Contains(ove_0_18.result.castPos) == 0 then
		return true
	end

	if ove_0_13.trace.newpath(ove_0_18.result.obj, 0.033, 0.5) then
		return true
	end
end

function ove_0_18.invoke_jungle_clear()
	local slot_15_0 = {
		mode = "jungleclear",
		health = 0
	}
	local slot_15_1 = ove_0_18.range

	for iter_15_0 = 0, objManager.minions.size[TEAM_NEUTRAL] - 1 do
		local slot_15_2 = objManager.minions[TEAM_NEUTRAL][iter_15_0]

		if slot_15_1 >= player.pos:dist(slot_15_2.pos) and slot_15_2.maxHealth > slot_15_0.health and ove_0_15.valid_minion(slot_15_2) then
			slot_15_0.obj = slot_15_2
			slot_15_0.health = slot_15_2.maxHealth
		end
	end

	if slot_15_0.mode == "jungleclear" and slot_15_0.obj ~= nil and ove_0_15.isValidTarget(slot_15_0.obj) then
		local slot_15_3 = ove_0_13.circular.get_prediction(ove_0_18.predinput, slot_15_0.obj)

		if not slot_15_3 then
			return
		end

		if not ove_0_18.is_charging_with_e() then
			player:castSpell("pos", _E, slot_15_3.endPos:to3D())
		elseif ove_0_15.GetDistance(player.pos, slot_15_0.obj.pos) < ove_0_18.range + 250 then
			player:castSpell("release", _E, slot_15_3.endPos:to3D())
		end
	end
end

function ove_0_18.invoke_flee_action()
	local slot_16_0 = ove_0_16.flee.use_e_when:get()

	if slot_16_0 == 1 then
		local slot_16_1 = ove_0_18.getClosesPlayer()

		if slot_16_1 ~= nil then
			local slot_16_2 = player.pos + (slot_16_1 - player.pos):norm() * (ove_0_15.GetDistance(player.pos, slot_16_1.pos) + ove_0_18.range)

			if ove_0_15.GetDistance(player.pos, slot_16_1.path.serverPos) > 800 and ove_0_15.GetDistance(player.pos, slot_16_1.pos) < ove_0_18.CastRangeGrowthMax then
				if not ove_0_18.is_charging_with_e() then
					player:castSpell("pos", _E, slot_16_1.pos)
				elseif ove_0_18.range == ove_0_18.CastRangeGrowthMax then
					player:castSpell("release", _E, slot_16_1.path.serverPos)
				end
			elseif ove_0_15.GetDistance(player.pos, slot_16_1.pos) > ove_0_18.CastRangeGrowthMax then
				if not ove_0_18.is_charging_with_e() then
					player:castSpell("pos", _E, slot_16_2)
				elseif ove_0_15.GetDistance(slot_16_1.pos, slot_16_2) <= ove_0_18.range + 100 then
					player:castSpell("release", _E, slot_16_2)
				end
			end
		else
			local slot_16_3 = player.pos + (game.mousePos - player.pos):norm() * ove_0_18.range

			if not ove_0_18.is_charging_with_e() then
				player:castSpell("pos", _E, slot_16_3)
			elseif ove_0_18.range == ove_0_18.CastRangeGrowthMax then
				player:castSpell("release", _E, slot_16_3)
			end
		end
	elseif slot_16_0 == 2 then
		local slot_16_4 = ove_0_18.getClosesPlayer()

		if slot_16_4 ~= nil then
			local slot_16_5 = player.pos + (slot_16_4 - player.pos):norm() * ove_0_18.range

			if not ove_0_18.is_charging_with_e() then
				player:castSpell("pos", _E, slot_16_5)
			elseif ove_0_18.range == ove_0_18.CastRangeGrowthMax then
				player:castSpell("release", _E, slot_16_5)
			end
		else
			local slot_16_6 = player.pos + (game.mousePos - player.pos):norm() * ove_0_18.range

			if not ove_0_18.is_charging_with_e() then
				player:castSpell("pos", _E, slot_16_6)
			elseif ove_0_18.range == ove_0_18.CastRangeGrowthMax then
				player:castSpell("release", _E, slot_16_6)
			end
		end
	elseif slot_16_0 == 3 then
		local slot_16_7 = player.pos + (game.mousePos - player.pos):norm() * ove_0_18.range

		if not ove_0_18.is_charging_with_e() then
			player:castSpell("pos", _E, slot_16_7)
		elseif ove_0_18.range == ove_0_18.CastRangeGrowthMax then
			player:castSpell("release", _E, slot_16_7)
		end
	end
end

function ove_0_18.get_prediction()
	if ove_0_18.last == game.time then
		return ove_0_18.result.castPos
	end

	ove_0_18.last = game.time
	ove_0_18.result.obj = nil
	ove_0_18.result.seg = nil
	ove_0_18.result.castPos = nil
	ove_0_18.result = ove_0_11.get_result(function(arg_18_0, arg_18_1, arg_18_2)
		if arg_18_2 > ove_0_18.range + 250 + arg_18_1.boundingRadius then
			return
		end

		if not ove_0_18.is_charging_with_e() and arg_18_1.buff.zacqmissile then
			return
		end

		if arg_18_2 <= ove_0_15.GetAARange(arg_18_1) and ove_0_15.CalculateAADamage(arg_18_1) * 2 > ove_0_15.GetShieldedHealth("AD", arg_18_1) then
			return
		end

		local slot_18_0 = ove_0_13.circular.get_prediction(ove_0_18.predinput, arg_18_1)

		if not slot_18_0 then
			return
		end

		local slot_18_1 = ove_0_17.Compute(ove_0_18.predinput, slot_18_0, arg_18_1)

		if slot_18_1 < 0 then
			return false
		end

		local slot_18_2 = ove_0_18.range + 250 + arg_18_1.boundingRadius

		if slot_18_0 and slot_18_0.startPos:distSqr(slot_18_0.endPos) < slot_18_2 * slot_18_2 then
			arg_18_0.seg = slot_18_0
			arg_18_0.obj = arg_18_1
			arg_18_0.castPos = (ove_0_13.core.get_pos_after_time(arg_18_1, slot_18_1) + slot_18_0.endPos) / 2
			arg_18_0.time = slot_18_1

			return true
		end
	end)

	if ove_0_18.result.castPos and ove_0_18.trace_filter() then
		return ove_0_18.result
	end
end

function ove_0_18.on_draw()
	if ove_0_18.slot.level == 1 then
		graphics.draw_circle(player.pos, 1200, 1, ove_0_16.draws.e:get(), 100)
	elseif ove_0_18.slot.level > 1 then
		graphics.draw_circle(player.pos, ove_0_18.range, 1, ove_0_16.draws.e:get(), 100)
	end
end

return ove_0_18

local ove_0_10 = module.internal("TS")
local ove_0_11 = module.internal("orb")
local ove_0_12 = module.internal("pred")
local ove_0_13 = player:spellSlot(1)
local ove_0_14 = 500
local ove_0_15 = ove_0_14 * ove_0_14
local ove_0_16 = {
	radius = 500,
	range = 500,
	delay = 0.5,
	boundingRadiusMod = 1,
	width = 40,
	speed = math.huge,
	damage = function(arg_5_0)
		-- print 5
		return 10 * ove_0_13.level + arg_5_0.maxHealth / 100 * (10 + 1 * ove_0_13.level)
	end
}

local function ove_0_17(arg_6_0)
	-- print 6
	if arg_6_0 >= 424 then
		return 2 * (69.7616 - 0.02975 * arg_6_0) * mathf.PI / 180
	end

	if arg_6_0 >= 274 then
		return 2 * (81.5852 - 0.05786 * arg_6_0) * mathf.PI / 180
	end

	if arg_6_0 >= 148 then
		return 2 * (117.3655 - 0.18826 * arg_6_0) * mathf.PI / 180
	end

	return 178.96 * mathf.PI / 180
end

local function ove_0_18(arg_7_0, arg_7_1, arg_7_2)
	-- print 7
	if arg_7_2 > 1500 then
		return
	end

	local slot_7_0 = {}
	local slot_7_1 = ove_0_12.linear.get_prediction(ove_0_16, arg_7_1)

	if slot_7_1 and slot_7_1.startPos:distSqr(slot_7_1.endPos) < ove_0_15 then
		local slot_7_2 = slot_7_1.startPos:dist(slot_7_1.endPos)
		local slot_7_3 = ove_0_17(arg_7_2) / 2

		table.insert(slot_7_0, {
			mid_rad = 0,
			os_rad = slot_7_3
		})

		for iter_7_0 = 0, objManager.enemies_n - 1 do
			local slot_7_4 = objManager.enemies[iter_7_0]

			if slot_7_4 ~= arg_7_1 then
				local slot_7_5 = ove_0_12.linear.get_prediction(ove_0_16, slot_7_4)

				if slot_7_5 then
					local slot_7_6 = mathf.angle_between(slot_7_5.startPos, slot_7_1.endPos, slot_7_5.endPos)

					if slot_7_1.startPos:distSqr(slot_7_5.endPos) < ove_0_15 then
						local slot_7_7 = slot_7_5.startPos:dist(slot_7_5.endPos)

						if slot_7_6 and math.abs(slot_7_6) < 2 * slot_7_3 then
							local slot_7_8 = ove_0_17(slot_7_7) / 2

							table.insert(slot_7_0, {
								mid_rad = slot_7_6,
								os_rad = slot_7_8
							})
						end
					end
				end
			end
		end

		table.sort(slot_7_0, function(arg_8_0, arg_8_1)
			-- print 8
			return arg_8_0.mid_rad < arg_8_1.mid_rad
		end)

		for iter_7_1 = 1, #slot_7_0 do
			local slot_7_9 = 0
			local slot_7_10 = slot_7_0[iter_7_1].mid_rad
			local slot_7_11 = slot_7_0[iter_7_1].os_rad
			local slot_7_12 = slot_7_10
			local slot_7_13 = slot_7_10 + slot_7_11
			local slot_7_14 = slot_7_10 + 2 * slot_7_11
			local slot_7_15 = slot_7_12

			for iter_7_2 = 1, #slot_7_0 do
				local slot_7_16 = slot_7_0[iter_7_2].mid_rad
				local slot_7_17 = slot_7_0[iter_7_2].os_rad

				if slot_7_16 >= slot_7_13 - slot_7_17 and slot_7_16 <= slot_7_13 + slot_7_17 then
					slot_7_9 = slot_7_9 + 1
					slot_7_15 = math.max(slot_7_16, slot_7_15)
				end
			end

			local slot_7_18 = arg_7_0.hits and arg_7_0.hits or 0
			local slot_7_19 = arg_7_0.lenience and arg_7_0.lenience or math.huge
			local slot_7_20 = slot_7_14 - slot_7_15

			if slot_7_18 < slot_7_9 or slot_7_18 == slot_7_9 and slot_7_19 < slot_7_20 then
				local slot_7_21 = (slot_7_12 + slot_7_15) / 2

				arg_7_0.obj = arg_7_1
				arg_7_0.pos = player.pos2D + (arg_7_1.pos2D - player.pos2D):rotate(slot_7_21)
				arg_7_0.hits = slot_7_9
				arg_7_0.lenience = slot_7_20
			end
		end

		if arg_7_0.hits then
			return true
		end
	end
end

local ove_0_19 = {}
local ove_0_20 = 0

-- 立即释放版本（对应Yone00.lua的ove_0_109，用于主连招循环）
local function ove_0_21()
	-- print 9
	if ove_0_13.state == 0 and os.clock() > ove_0_20 then
		-- 检查是否是Q优先模式
		local menu = module.load(header.id, "yone/menu")
		local in_e_form = false
		if player and player.buffCount then
			for i = 0, player.buffCount - 1 do
				local buff = player:buffByIndex(i)
				if buff and buff.valid and buff.name:lower():find("yoneespirit") then
					in_e_form = true
					break
				end
			end
		end

		-- 获取技能优先级设置
		local qPriority = in_e_form
			and menu.combo_spell_priority_eform:get() == 1
			or menu.combo_spell_priority:get() == 1

		-- 如果是Q优先模式，检查Q是否可用
		if qPriority then
			-- 检查Q技能是否可用
			local q_state = player:spellSlot(0).state
			-- 如果Q技能可用，不返回W
			if q_state == 0 then
				--print("[W模块] Q可用，Q优先模式下跳过W释放")
				return nil
			end
		end

		-- 精确的攻击速度计算（完全按照Yone00.lua）
		local attackSpeedBonus = (player.attackSpeedMod - 1) * 100
		ove_0_16.delay = math.max(0.25, 0.5 * (1 - attackSpeedBonus / 2.4 / 100))

		-- 立即检测目标，不等待AA穿插
		ove_0_19 = ove_0_10.loop(ove_0_18)

		if ove_0_19.pos then
			--print("[W模块] W技能检测到目标，可以释放")
			return ove_0_19
		end
	end
	return nil
end

-- AA穿插版本（对应Yone00.lua的ove_0_110，用于攻击后回调）
local function ove_0_21_weave()
	-- print 9_weave
	if ove_0_13.state == 0 and os.clock() > ove_0_20 then
		-- 检查是否是Q优先模式
		local menu = module.load(header.id, "yone/menu")
		local in_e_form = false
		if player and player.buffCount then
			for i = 0, player.buffCount - 1 do
				local buff = player:buffByIndex(i)
				if buff and buff.valid and buff.name:lower():find("yoneespirit") then
					in_e_form = true
					break
				end
			end
		end

		-- 获取技能优先级设置
		local qPriority = in_e_form
			and menu.combo_spell_priority_eform:get() == 1
			or menu.combo_spell_priority:get() == 1

		-- 如果是Q优先模式，检查Q是否可用
		if qPriority then
			-- 检查Q技能是否可用
			local q_state = player:spellSlot(0).state
			-- 如果Q技能可用，不返回W
			if q_state == 0 then
				--print("[W模块] Q可用，Q优先模式下跳过W释放")
				return nil
			end
		end

		-- 精确的攻击速度计算（完全按照Yone00.lua）
		local attackSpeedBonus = (player.attackSpeedMod - 1) * 100
		ove_0_16.delay = math.max(0.25, 0.5 * (1 - attackSpeedBonus / 2.4 / 100))

		-- 完全按照Yone00.lua的AA穿插逻辑，包含冰雹符文特殊处理
		-- 获取冰雹符文状态（对应Yone00.lua的ove_0_70）
		local hail_of_blades_active = false
		if player.buff["Rune_HailOfBladesBuff"] or player.buff["Rune_HailOfBladesOmniBuff"] then
			-- 检查core.lua中的ove_0_37变量（对应ove_0_70）
			local core = module.load(header.id, "yone/core")
			if core and core.get_hail_flag and core.get_hail_flag() then
				hail_of_blades_active = true
			end
		end

		if ove_0_11.core.next_attack - os.clock() > ove_0_16.delay + network.latency or hail_of_blades_active then
			ove_0_19 = ove_0_10.loop(ove_0_18)

			if ove_0_19.pos then
				--print("[W模块] W技能检测到目标，可以释放")
				return ove_0_19
			end
		end
	end
	return nil
end

local function ove_0_22()
	-- --print 10
	local slot_10_0 = vec3(ove_0_19.pos.x, ove_0_19.obj.y, ove_0_19.pos.y)

	-- 完全按照Yone00.lua的方式释放技能，包含AA穿插逻辑
	if ove_0_11.core.can_action() and (ove_0_11.core.next_attack - os.clock() > ove_0_16.delay + network.latency or ove_0_11.core.next_attack - os.clock() < 0) then
		player:castSpell("pos", 1, slot_10_0)

		-- 完全按照Yone00.lua重置冰雹符文状态
		local core = module.load(header.id, "yone/core")
		if core and core.reset_hail_flag then
			core.reset_hail_flag()
		end
	end
	ove_0_11.core.set_server_pause()

	ove_0_20 = os.clock() + network.latency + 0.5
end

local function ove_0_23()
	-- --print 11
	if ove_0_13.state == 0 and os.clock() > ove_0_20 then
		ove_0_16.delay = math.max(0.7083 - 0.2083 * player.attackSpeedMod, 0.25)

		local slot_11_0, slot_11_1 = ove_0_11.farm.skill_clear_linear(ove_0_16)

		if slot_11_1 then
			ove_0_19.obj = slot_11_1
			ove_0_19.pos = slot_11_0.endPos

			return ove_0_19
		end
	end
end

local function ove_0_24()
	-- print 12
	local slot_12_0 = vec3(ove_0_19.pos.x, ove_0_19.obj.y, ove_0_19.pos.y)

	player:castSpell("pos", 1, slot_12_0)

	ove_0_20 = os.clock() + network.latency + 0.5

	ove_0_11.core.set_server_pause()
end

local function ove_0_25()
	-- print 13
	if ove_0_13.state == 0 and os.clock() > ove_0_20 then
		ove_0_16.delay = math.max(0.7083 - 0.2083 * player.attackSpeedMod, 0.25)

		local slot_13_0, slot_13_1 = ove_0_11.farm.skill_farm_linear(ove_0_16)

		if slot_13_1 then
			ove_0_19.obj = slot_13_1
			ove_0_19.pos = slot_13_0.endPos

			return ove_0_19
		end
	end
end

local function ove_0_26()
	-- print 14
	local slot_14_0 = vec3(ove_0_19.pos.x, ove_0_19.obj.y, ove_0_19.pos.y)

	player:castSpell("pos", 1, slot_14_0)

	ove_0_20 = os.clock() + network.latency + 0.5

	ove_0_11.core.set_server_pause()
end

return {
	get_action_state = ove_0_21,
	get_action_state_weave = ove_0_21_weave,  -- AA穿插版本
	invoke_action = ove_0_22,
	get_clear_state = ove_0_23,
	invoke_clear = ove_0_24,
	get_farm_state = ove_0_25,
	invoke_farm = ove_0_26
}

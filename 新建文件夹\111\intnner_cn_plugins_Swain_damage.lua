

local ove_0_10 = module.load(header.id, "common/common")
local ove_0_11 = {
	get_q_damage = function(arg_5_0)
		if not arg_5_0 or arg_5_0.isDead or player:spellSlot(_Q).level < 1 then
			return 0
		end

		local slot_5_0 = 60 + 20 * (player:spellSlot(_Q).level - 1)
		local slot_5_1 = ove_0_10.GetTotalAP(player) * 0.38

		return slot_5_0 + ove_0_10.CalculateMagicDamage(arg_5_0, player, slot_5_1)
	end,
	get_w_damage = function(arg_6_0)
		if not arg_6_0 or arg_6_0.isDead or player:spellSlot(_W).level < 1 then
			return 0
		end

		local slot_6_0 = 80 + 35 * (player:spellSlot(_W).level - 1)
		local slot_6_1 = ove_0_10.GetTotalAP(player) * 0.55

		return slot_6_0 + ove_0_10.CalculateMagicDamage(arg_6_0, player, slot_6_1)
	end,
	get_e_damage = function(arg_7_0)
		if not arg_7_0 or arg_7_0.isDead or player:spellSlot(_E).level < 1 then
			return 0
		end

		local slot_7_0 = 70 + 45 * (player:spellSlot(_E).level - 1)
		local slot_7_1 = ove_0_10.GetTotalAP(player) * 0.5

		return slot_7_0 + ove_0_10.CalculateMagicDamage(arg_7_0, player, slot_7_1)
	end,
	get_r_damage = function(arg_8_0)
		if not arg_8_0 or arg_8_0.isDead or player:spellSlot(_R).level < 1 then
			return 0
		end

		local slot_8_0 = 150 + 75 * (player:spellSlot(_R).level - 1)
		local slot_8_1 = ove_0_10.GetTotalAP(player) * 0.6

		return slot_8_0 + ove_0_10.CalculateMagicDamage(arg_8_0, player, slot_8_1)
	end
}

function ove_0_11.GetTotalDamage(arg_9_0)
	local slot_9_0 = 0

	if ove_0_10.IsReady(_Q) then
		slot_9_0 = slot_9_0 + ove_0_11.get_q_damage(arg_9_0)
	end

	if ove_0_10.IsReady(_W) then
		slot_9_0 = slot_9_0 + ove_0_11.get_w_damage(arg_9_0)
	end

	if ove_0_10.IsReady(_E) then
		slot_9_0 = slot_9_0 + ove_0_11.get_e_damage(arg_9_0)
	end

	if ove_0_10.IsReady(_R) or player.buff.swainr then
		slot_9_0 = slot_9_0 + ove_0_11.get_e_damage(arg_9_0)
	end

	return slot_9_0 + ove_0_10.CalculateAADamage(arg_9_0, player)
end

function ove_0_11.over_layer()
	for iter_10_0, iter_10_1 in ipairs(ove_0_10.GetEnemyHeroes()) do
		if iter_10_1 and iter_10_1.ptr ~= 0 and not iter_10_1.isDead and iter_10_1.isVisible and ove_0_10.isValidTarget(iter_10_1) and iter_10_1.isOnScreen then
			local slot_10_0 = ove_0_11.GetTotalDamage(iter_10_1)
			local slot_10_1 = iter_10_1.barPos
			local slot_10_2 = math.max(100 * ((iter_10_1.health - slot_10_0) / iter_10_1.maxHealth), 0)
			local slot_10_3 = math.max(0, iter_10_1.health - slot_10_0) / iter_10_1.maxHealth
			local slot_10_4 = graphics.height > 1080 and graphics.height / 1080 * 0.905 or 1

			if graphics.width > 1920 then
				if slot_10_2 <= 0 then
					graphics.draw_line_2D(slot_10_1.x + 165 * slot_10_4 + 103 * slot_10_4 * iter_10_1.health / iter_10_1.maxHealth, slot_10_1.y + 123 * slot_10_4, slot_10_1.x + 165 * slot_10_4 + 100 * slot_10_4 * slot_10_3, slot_10_1.y + 123 * slot_10_4, 11, **********)
				elseif slot_10_2 > 0 then
					graphics.draw_line_2D(slot_10_1.x + 165 * slot_10_4 + 103 * slot_10_4 * iter_10_1.health / iter_10_1.maxHealth, slot_10_1.y + 123 * slot_10_4, slot_10_1.x + 165 * slot_10_4 + 100 * slot_10_4 * slot_10_3, slot_10_1.y + 123 * slot_10_4, 11, **********)
				end
			elseif slot_10_2 <= 0 then
				graphics.draw_line_2D(slot_10_1.x + 165 + 103 * iter_10_1.health / iter_10_1.maxHealth, slot_10_1.y + 123, slot_10_1.x + 165 + 100 * slot_10_3, slot_10_1.y + 123, 11, **********)
			elseif slot_10_2 > 0 then
				graphics.draw_line_2D(slot_10_1.x + 165 + 103 * iter_10_1.health / iter_10_1.maxHealth, slot_10_1.y + 123, slot_10_1.x + 165 + 100 * slot_10_3, slot_10_1.y + 123, 11, **********)
			end
		end
	end
end

return ove_0_11

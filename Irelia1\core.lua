

local ove_0_10 = module.internal("orb")
local ove_0_11 = module.internal("pred")
local ove_0_12 = module.internal("TS")
local ove_0_13 = module.load("<PERSON>", "Irelia/common/delayAction")
local ove_0_14 = module.load("<PERSON>", "Irelia/common/netManager")
local ove_0_15 = module.load("<PERSON>", "Irelia/common/buffManager")
local ove_0_16 = module.load("<PERSON>", "Irelia/common/delayTick")
local ove_0_17 = module.load("<PERSON>", "Irelia/common/vectorManager")
local ove_0_18 = module.load("<PERSON>", "Irelia/common/objectManager")

local function ove_0_19(arg_5_0, arg_5_1)
	arg_5_1 = arg_5_1 or 20000

	if arg_5_0 and arg_5_0 ~= nil and not arg_5_0.isDead and arg_5_0.health and arg_5_0.health > 0 then
		if arg_5_0.type == TYPE_HERO then
			if arg_5_0.isVisible and arg_5_0.isTargetable and arg_5_0.pos and arg_5_0.boundingRadius then
				if ove_0_15.HasBuffOfType(arg_5_0, 17) then
					return false
				end

				if arg_5_0.charName == "KogMaw" and ove_0_15.HasBuff(arg_5_0, "KogMawIcathianSurprise") then
					return false
				end

				if arg_5_0.charName == "Karthus" and ove_0_15.HasBuff(arg_5_0, "KarthusDeathDefiedBuff") then
					return false
				end

				if arg_5_0.charName == "Sion" and ove_0_15.HasBuff(arg_5_0, "sionpassivezombie") then
					return false
				end

				if player.pos:distSqr(arg_5_0.pos) <= (arg_5_1 + arg_5_0.boundingRadius) * (arg_5_1 + arg_5_0.boundingRadius) then
					return true
				end
			end
		elseif (arg_5_0.type == TYPE_MINION or arg_5_0.type == TYPE_TURRET or arg_5_0.type == TYPE_INHIB or arg_5_0.type == TYPE_NEXUS) and arg_5_0.isVisible and arg_5_0.isTargetable and arg_5_0.pos and player.pos:distSqr(arg_5_0.pos) <= arg_5_1 * arg_5_1 then
			return true
		end
	end
end

local function ove_0_20(arg_6_0, arg_6_1)
	arg_6_1 = arg_6_1 or player.pos

	if arg_6_1:dist(game.cameraPos) < arg_6_0 + (graphics.width + graphics.height) / 1.65 then
		return true
	end
end

local function ove_0_21(arg_7_0)
	arg_7_0 = arg_7_0 or player

	if arg_7_0 and arg_7_0 ~= nil and not arg_7_0.isDead and arg_7_0.type == TYPE_HERO then
		if arg_7_0.moveSpeed and arg_7_0.moveSpeed < 50 then
			return false
		end

		if ove_0_15.HasBuffOfType(arg_7_0, 5) or ove_0_15.HasBuffOfType(arg_7_0, 21) or ove_0_15.HasBuffOfType(arg_7_0, 28) or ove_0_15.HasBuffOfType(arg_7_0, 11) or ove_0_15.HasBuffOfType(arg_7_0, 29) or ove_0_15.HasBuffOfType(arg_7_0, 30) or ove_0_15.HasBuffOfType(arg_7_0, 22) or ove_0_15.HasBuffOfType(arg_7_0, 8) or ove_0_15.HasBuffOfType(arg_7_0, 18) or ove_0_15.HasBuffOfType(arg_7_0, 24) or ove_0_15.HasBuffOfType(arg_7_0, 32) or ove_0_15.HasBuffOfType(arg_7_0, 34) then
			return false
		end

		if ove_0_15.HasBuff(arg_7_0, "recall") or ove_0_15.HasBuff(arg_7_0, "zhonyasringshield") or ove_0_15.HasBuff(arg_7_0, "bardrstasis") then
			return false
		end
	end

	return true
end

local function ove_0_22(arg_8_0, arg_8_1)
	arg_8_1 = arg_8_1 or player

	if not arg_8_1 or arg_8_1 == nil or not arg_8_1.attackRange then
		return 0
	end

	local slot_8_0 = arg_8_1.attackRange + arg_8_1.boundingRadius

	if not arg_8_0 then
		return slot_8_0
	end

	if not ove_0_19(arg_8_0) then
		return slot_8_0
	end

	if ove_0_19(arg_8_0) and (arg_8_0.type == TYPE_HERO or arg_8_0.type == TYPE_MINION) and ove_0_15.HasBuff(arg_8_0, "caitlynyordletrapinternal") then
		slot_8_0 = 1300 + arg_8_0.boundingRadius
	end

	if arg_8_0.type == TYPE_TURRET then
		return arg_8_1.attackRange + arg_8_1.boundingRadius + 120
	end

	if arg_8_0.type == TYPE_NEXUS then
		return arg_8_1.attackRange + arg_8_1.boundingRadius + 195
	end

	if arg_8_0.type == TYPE_INHIB then
		return arg_8_1.attackRange + arg_8_1.boundingRadius + 179
	end

	if arg_8_0.boundingRadius then
		if slot_8_0 > 480 then
			return slot_8_0 + arg_8_0.boundingRadius - 35
		else
			if arg_8_0.type == TYPE_MINION and arg_8_0.charName and arg_8_0.charName == "SRU_Baron" then
				return slot_8_0 + arg_8_0.boundingRadius + 20
			end

			return slot_8_0 + arg_8_0.boundingRadius - 7
		end
	end

	return slot_8_0
end

local function ove_0_23(arg_9_0, arg_9_1)
	arg_9_1 = arg_9_1 or 0

	if arg_9_0 and arg_9_0 ~= nil and not arg_9_0.isDead and ove_0_19(arg_9_0) then
		local slot_9_0 = ove_0_22(arg_9_0) + arg_9_1

		if arg_9_0.pos:distSqr(player.pos) <= slot_9_0 * slot_9_0 then
			return true
		end
	end
end

local function ove_0_24(arg_10_0)
	arg_10_0 = arg_10_0 or player

	if arg_10_0 and arg_10_0 ~= nil and not arg_10_0.isDead then
		if arg_10_0.maxPar == 0 then
			return 100
		end

		return arg_10_0.par / arg_10_0.maxPar * 100
	end

	return 0
end

local function ove_0_25(arg_11_0)
	arg_11_0 = arg_11_0 or player

	if arg_11_0 and arg_11_0 ~= nil and not arg_11_0.isDead then
		return arg_11_0.health / arg_11_0.maxHealth * 100
	end

	return 0
end

local function ove_0_26(arg_12_0)
	arg_12_0 = arg_12_0 or player

	if arg_12_0 and arg_12_0 ~= nil and arg_12_0.baseAttackDamage and arg_12_0.flatPhysicalDamageMod then
		if arg_12_0.type == TYPE_HERO and arg_12_0.percentPhysicalDamageMod then
			local slot_12_0 = arg_12_0.percentPhysicalDamageMod

			if slot_12_0 == 0 or slot_12_0 < 0 then
				slot_12_0 = 1
			end

			return (arg_12_0.baseAttackDamage + arg_12_0.flatPhysicalDamageMod) * slot_12_0
		end

		if arg_12_0.type == TYPE_MINION or arg_12_0.type == TYPE_TURRET then
			return arg_12_0.baseAttackDamage + arg_12_0.flatPhysicalDamageMod
		end

		return 0
	end

	return 0
end

local function ove_0_27(arg_13_0)
	arg_13_0 = arg_13_0 or player

	if arg_13_0 and arg_13_0 ~= nil and arg_13_0.flatPhysicalDamageMod then
		return arg_13_0.flatPhysicalDamageMod
	end

	return 0
end

local function ove_0_28(arg_14_0)
	arg_14_0 = arg_14_0 or player

	if arg_14_0 and arg_14_0 ~= nil and arg_14_0.flatMagicDamageMod and arg_14_0.percentMagicDamageMod then
		return arg_14_0.flatMagicDamageMod * arg_14_0.percentMagicDamageMod
	end

	return 0
end

local function ove_0_29(arg_15_0)
	return ove_0_12.get_result(function(arg_16_0, arg_16_1, arg_16_2)
		if arg_16_2 < arg_15_0 then
			arg_16_0.obj = arg_16_1

			return true
		end
	end).obj
end

local function ove_0_30(arg_17_0)
	if arg_17_0 and arg_17_0 ~= nil and not arg_17_0.isDead and arg_17_0.isVisible and arg_17_0.isTargetable and arg_17_0.health and arg_17_0.health > 0 then
		if ove_0_15.HasBuff(arg_17_0, "BlackShield") then
			return true
		end

		if ove_0_15.HasBuff(arg_17_0, "bansheesveil") then
			return true
		end

		if ove_0_15.HasBuff(arg_17_0, "SivirE") then
			return true
		end

		if ove_0_15.HasBuff(arg_17_0, "NocturneShroudofDarkness") then
			return true
		end

		if ove_0_15.HasBuff(arg_17_0, "itemmagekillerveil") then
			return true
		end

		if ove_0_15.HasBuffOfType(arg_17_0, 4) then
			return true
		end
	end
end

local function ove_0_31(arg_18_0)
	if arg_18_0 and arg_18_0 ~= nil and arg_18_0.charName and arg_18_0.type == TYPE_MINION then
		if arg_18_0.charName == "SRU_Baron" then
			return true
		end

		if arg_18_0.charName == "SRU_RiftHerald" then
			return true
		end

		if arg_18_0.charName == "SRU_Dragon_Earth" then
			return true
		end

		if arg_18_0.charName == "SRU_Dragon_Water" then
			return true
		end

		if arg_18_0.charName == "SRU_Dragon_Fire" then
			return true
		end

		if arg_18_0.charName == "SRU_Dragon_Air" then
			return true
		end

		if arg_18_0.charName == "SRU_Dragon_Elder" then
			return true
		end

		if arg_18_0.charName == "SRU_Murkwolf" then
			return true
		end

		if arg_18_0.charName == "SRU_Blue" then
			return true
		end

		if arg_18_0.charName == "SRU_Red" then
			return true
		end

		if arg_18_0.charName == "SRU_Gromp" then
			return true
		end

		if arg_18_0.charName == "Sru_Crab" then
			return true
		end

		if arg_18_0.charName == "SRU_Razorbeak" then
			return true
		end

		if arg_18_0.charName == "SRU_Krug" then
			return true
		end
	end
end

local function ove_0_32(arg_19_0)
	if arg_19_0 and arg_19_0 ~= nil and not arg_19_0.isDead and arg_19_0.isVisible and arg_19_0.isTargetable and arg_19_0.health and arg_19_0.health > 0 and not ove_0_15.HasBuffOfType(arg_19_0, 17) then
		if arg_19_0.charName == "KogMaw" and ove_0_15.HasBuff(arg_19_0, "KogMawIcathianSurprise") then
			return true
		end

		if arg_19_0.charName == "Karthus" and ove_0_15.HasBuff(arg_19_0, "KarthusDeathDefiedBuff") then
			return true
		end

		if arg_19_0.charName == "Sion" and ove_0_15.HasBuff(arg_19_0, "sionpassivezombie") then
			return true
		end

		if ove_0_15.HasBuff(arg_19_0, "malzaharpassiveshield") then
			return true
		end

		if ove_0_15.HasBuff(arg_19_0, "NocturneShroudofDarkness") then
			return true
		end

		if ove_0_15.HasBuff(arg_19_0, "KindredRNoDeathBuff") then
			return true
		end

		if ove_0_15.HasBuff(arg_19_0, "BansheesVeil") then
			return true
		end

		if ove_0_15.HasBuff(arg_19_0, "UndyingRage") then
			return true
		end

		if ove_0_15.HasBuff(arg_19_0, "JudicatorIntervention") then
			return true
		end

		if ove_0_15.HasBuff(arg_19_0, "ChronoShift") then
			return true
		end

		if ove_0_15.HasBuff(arg_19_0, "FioraW") then
			return true
		end

		if ove_0_15.HasBuff(arg_19_0, "ShroudofDarkness") then
			return true
		end

		if ove_0_15.HasBuff(arg_19_0, "SivirShield") then
			return true
		end

		if ove_0_15.HasBuff(arg_19_0, "zhonyasringshield") then
			return true
		end

		if ove_0_15.HasBuff(arg_19_0, "LissandraRSelf") then
			return true
		end

		if ove_0_15.HasBuff(arg_19_0, "bardrstasis") then
			return true
		end

		if ove_0_15.HasBuff(arg_19_0, "TaricR") then
			return true
		end

		if ove_0_15.HasBuff(arg_19_0, "willrevive") then
			return true
		end
	end
end

local function ove_0_33(arg_20_0, arg_20_1)
	return ove_0_10.utility.get_damage(arg_20_0, arg_20_1)
end

local function ove_0_34(arg_21_0, arg_21_1, arg_21_2)
	arg_21_2 = arg_21_2 or player

	if not arg_21_0 or arg_21_0 == nil or arg_21_0.isDead then
		return 0
	end

	local slot_21_0 = arg_21_0.spellBlock * arg_21_2.percentMagicPenetration - arg_21_2.flatMagicPenetration
	local slot_21_1 = slot_21_0 >= 0 and 100 / (100 + slot_21_0) or 2 - 100 / (100 - slot_21_0)
	local slot_21_2 = 1

	for iter_21_0 = 0, arg_21_2.buffManager.count - 1 do
		local slot_21_3 = arg_21_2.buffManager:get(iter_21_0)

		if slot_21_3 and slot_21_3.valid and slot_21_3.name and slot_21_3.endTime and slot_21_3.endTime > game.time then
			if slot_21_3.name == "summonerexhaustdebuff" then
				slot_21_2 = slot_21_2 * 0.6
			elseif slot_21_3.name == "itemsmitechallenge" then
				slot_21_2 = slot_21_2 * 0.6
			elseif slot_21_3.name == "itemphantomdancerdebuff" and slot_21_3.source.ptr == arg_21_0.ptr then
				slot_21_2 = slot_21_2 * 88
			elseif slot_21_3.name == "abyssalscepteraura" then
				slot_21_2 = slot_21_2 * 1.15
			end
		end
	end

	local slot_21_4 = arg_21_1 * slot_21_1 * slot_21_2

	return math.max(slot_21_4, 0)
end

local function ove_0_35(arg_22_0, arg_22_1, arg_22_2)
	arg_22_2 = arg_22_2 or player

	if arg_22_0 and arg_22_0 ~= nil and not arg_22_0.isDead and arg_22_0.bonusArmor and arg_22_0.armor then
		local slot_22_0 = arg_22_2.levelRef and arg_22_2.levelRef or 1

		if arg_22_2.percentBonusArmorPenetration and arg_22_2.percentArmorPenetration and arg_22_2.physicalLethality and slot_22_0 then
			local slot_22_1 = (arg_22_0.bonusArmor * arg_22_2.percentBonusArmorPenetration + (arg_22_0.armor - arg_22_0.bonusArmor)) * arg_22_2.percentArmorPenetration
			local slot_22_2 = arg_22_2.physicalLethality * 0.4 + arg_22_2.physicalLethality * 0.6 * (slot_22_0 / 18)
			local slot_22_3 = slot_22_1 >= 0 and 100 / (100 + (slot_22_1 - slot_22_2)) or 2 - 100 / (100 + (slot_22_1 - slot_22_2))
			local slot_22_4 = 1

			for iter_22_0 = 0, arg_22_2.buffManager.count - 1 do
				local slot_22_5 = arg_22_2.buffManager:get(iter_22_0)

				if slot_22_5 and slot_22_5.valid and slot_22_5.name and slot_22_5.endTime and slot_22_5.endTime > game.time then
					if slot_22_5.name == "summonerexhaustdebuff" then
						slot_22_4 = slot_22_4 * 0.6
					elseif slot_22_5.name == "itemsmitechallenge" then
						slot_22_4 = slot_22_4 * 0.6
					elseif slot_22_5.name == "itemphantomdancerdebuff" and slot_22_5.source.ptr == arg_22_0.ptr then
						slot_22_4 = slot_22_4 * 88
					end
				end
			end

			return arg_22_1 * slot_22_3 * slot_22_4
		end
	end

	return 0
end

return {
	hanbotOrbwalker = ove_0_10,
	hanbotPrediction = ove_0_11,
	hanbotTargetSelector = ove_0_12,
	delayAction = ove_0_13,
	netManager = ove_0_14,
	buffManager = ove_0_15,
	delayTick = ove_0_16,
	vectorManager = ove_0_17,
	objectManager = ove_0_18,
	IsValidTarget = ove_0_19,
	CanDrawCircle = ove_0_20,
	CanMoveMent = ove_0_21,
	GetAutoAttackRange = ove_0_22,
	IsInAutoAttackRange = ove_0_23,
	GetManaPercent = ove_0_24,
	GetHealthPercent = ove_0_25,
	GetTotalAD = ove_0_26,
	GetBonusAD = ove_0_27,
	GetTotalAP = ove_0_28,
	GetTarget = ove_0_29,
	HaveMagicShield = ove_0_30,
	IsBigMob = ove_0_31,
	IsUnKillAble = ove_0_32,
	GetAutoAttackDamage = ove_0_33,
	CalculateMagicDamage = ove_0_34,
	CalculatePhysicalDamage = ove_0_35
}

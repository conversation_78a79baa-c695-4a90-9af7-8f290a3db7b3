local KindredPlugin = {}


local Curses = module.load("<PERSON>", "<PERSON>s");
--local ui = module.load("<PERSON>", "ui");
local ts = module.internal("TS")
local orb = module.internal("orb")
local preds = module.internal("pred")
local DelayAction = module.load("<PERSON>", "Core/DelayAction")
local DelayTick = module.load("<PERSON>", "Core/DelayTick")
local Prediction = module.load("<PERSON>", "Core/Prediction")
local BuffManager = module.load("<PERSON>", "Library/BuffManager")
local CalculateManager = module.load("<PERSON>", "Library/CalculateManager")
--local FarmManager = module.load("<PERSON>", "Library/FarmManager")
local ItemManager = module.load("<PERSON>", "Library/ItemManager")
local NetManager = module.load("<PERSON>", "Library/NetManager")
local ObjectManager = module.load("<PERSON>", "Library/ObjectManager")
local OrbManager = module.load("<PERSON>", "Library/OrbManager")
local SpellManager = module.load("<PERSON>", "Library/SpellManager")
local VectorManager = module.load("<PERSON>", "Library/VectorManager")
local MyCommon = module.load("<PERSON>", "Library/ExtraManager")
local common = module.load("Brian", "Utility/common")
local evade = module.internal("evade");
local a = game.time
--kindredhittracker
local enemies = common.GetEnemyHeroes()
local minionmanager = objManager.minions
local qPred = {
	delay = 0.05,
	radius = 55,
	speed = 1360,
	boundingRadiusMod = 1,
	collision = {hero = false, minion = false}
}
local spellsq = {
	delay = 0.25;
	radius = player.attackRange;
	speed = 1360;
	dashRadius = 330;
	boundingRadiusModSource = 1;
    boundingRadiusModTarget = 1;
}
local wPred = {
	delay = 0.25,
	radius = 100,
	speed = 1400,
	boundingRadiusMod = 0,
	collision = {hero = false, minion = false}
}

local MyMenu

function KindredPlugin.Load(GlobalPlugin)
    MyMenu = GlobalPlugin

    MyMenu.Key:keybind("manual", "Panic R", "T", false)
    MyMenu.Key:keybind("run", "Flee", "S", false)
    MyMenu.Key:keybind("time", "Time", nil, "A")

    MyMenu:menu("combo", "Combo Settings")
    MyMenu.combo:header("xd", "Combo Settings")
    MyMenu.combo:header("xd", "Q Settings")
    MyMenu.combo:boolean("q", "Use Q", true)
    MyMenu.combo:boolean("gc", "GapClose With Q", true)
    MyMenu.combo:dropdown("mode", "Choose Mode: ", 1, {"To Mouse", "To Target"})

    MyMenu.combo:header("xd", "W Settings")
    MyMenu.combo:boolean("w", "Smart W", true)

    MyMenu.combo:header("xd", "E Settings")
    MyMenu.combo:boolean("e", "Use E", true)
    MyMenu.combo:dropdown("modee", "Choose Mode: ", 1, {"Smart", "Always"})

    MyMenu.combo:header("xd", "R Settings")
    MyMenu.combo:boolean("r", "Use R", true)
    MyMenu.combo:slider("rhp", "What HP% to Ult", 10, 0, 100, 5)
    MyMenu.combo:slider("rxe", "R if X Enemys in Range", 1, 0, 5, 1)
    MyMenu.combo:slider("rxa", "R if X Allys in Range", 1, 0, 5, 1)

    MyMenu:menu("harass", "Harass Settings")
    MyMenu.harass:header("xd", "Q Settings")
    MyMenu.harass:boolean("q", "Use Q", true)
    MyMenu.harass:dropdown("mode", "Choose Mode: ", 1, {"To Mouse", "To Target"})
    MyMenu.harass:header("xd", "W Settings")
    MyMenu.harass:boolean("w", "Smart W", true)
    MyMenu.harass:header("xd", "E Settings")
    MyMenu.harass:boolean("e", "Use E", true)
    MyMenu.harass:dropdown("modee", "Choose Mode: ", 1, {"Smart", "Always"})
    MyMenu.harass:slider("Mana", "Min. Mana Percent: ", 10, 0, 100, 10)

    MyMenu:menu("jg", "Jungle Clear Settings")
    MyMenu.jg:header("xd", "Jungle Settings")
    MyMenu.jg:boolean("q", "Use Q", true)
    MyMenu.jg:boolean("w", "Use W", true)
    MyMenu.jg:boolean("e", "Use E", true)
    MyMenu.jg:slider("Mana", "Min. Mana Percent: ", 10, 0, 100, 5)

    MyMenu:menu("draws", "Draw Settings")
    MyMenu.draws:header("xd", "Drawing Options")
    MyMenu.draws:boolean("q", "Draw Q Range", true)
    MyMenu.draws:color("colorq", "^ color", 255, 233, 121, 121)
    MyMenu.draws:boolean("e", "Draw E Range", true)
    MyMenu.draws:color("colore", "^ color", 255, 233, 121, 121)
end


local function select_target(res, obj, dist)
	--if common.GetPercentHealth(obj) > 10 then return end
	if dist > player.attackRange + player.boundingRadius + 200 then
		return
	end
	res.obj = obj
	return true
end

local function q_pred(res, obj, dist)
	if dist > player.attackRange + player.boundingRadius + 200 then return end
	if preds.present.get_prediction(spellsq, obj) then
      	res.obj = obj
      	return true
    end
end

local function get_target(func)
	return ts.get_result(func).obj
end


local function CastQ(target)
	if player:spellSlot(0).state == 0 and vec3(target.x, target.y, target.z):dist(player) < 320 + player.attackRange + player.boundingRadius and target.pos:dist(player.pos) > 340 and not navmesh.isWall(target.pos) then
		player:castSpell("pos", 1, target.pos)
	end
end

local function CastW(target)
	if player:spellSlot(1).state == 0 and player.path.serverPos:dist(target.path.serverPos) < (500) then
		local res = preds.circular.get_prediction(wPred, target)
		if res and res.startPos:dist(res.endPos) < 500 then
			player:castSpell("pos", 1, vec3(res.endPos.x, game.mousePos.y, res.endPos.y))
		end
	end
end

local function roll()
	if MyMenu.Key.Combo:get() or MyMenu.Key.Harass:get() or MyMenu.Key.LaneClear:get() then
		local target = get_target(select_target)
		if not target then return end

		if player:spellSlot(0).state ~= 0 then return end

		local range = player.attackRange + (player.boundingRadius + target.boundingRadius)

		if orb.combat.target and MyMenu.combo.q:get() then
			if MyMenu.combo.mode:get() == 1 then
				if player.pos:dist(target.pos) < range and not navmesh.isWall(game.mousePos) then
					player:castSpell("pos", 0, game.mousePos)
					orb.core.reset()
					orb.combat.set_invoke_after_attack(false)
				end
			end
		end
		if MyMenu.Key.Harass:get() and MyMenu.harass.q:get() then
			if MyMenu.harass.mode:get() == 1 then
				if player.pos:dist(target.pos) < range then
					player:castSpell("pos", 0, game.mousePos)
					orb.core.reset()
					orb.combat.set_invoke_after_attack(false)
				end
			end
		end
	end
end

local function out_of_aa()
	if not MyMenu.Key.Combo:get() then return end
	if not MyMenu.combo.gc:get() then return end

	local obj = ts.get_result(q_pred).obj
    if obj then
    	local range = 320 + player.attackRange + (player.boundingRadius + obj.boundingRadius)
		if obj.pos:dist(player.pos) <= range then
			local dashpos = game.mousePos; 
			--if evade.core.is_action_safe(dashpos, math.huge, 0.25) then
				--player:castSpell("pos", 0, dashpos)
			--end
		end
	end
end


local function Combo()
	local target = get_target(select_target)
	if target and common.IsValidTarget(target) and not target.buff["sionpassivezombie"] then
		local range = player.attackRange + player.boundingRadius
		local d = player.path.serverPos:dist(target.path.serverPos)
		if MyMenu.combo.e:get() and player:spellSlot(2).state == 0 and d <= range then
			if MyMenu.combo.modee:get() == 1 and d <= player.attackRange + (player.boundingRadius + target.boundingRadius) and d > 200 then
				player:castSpell("obj", 2, target)
			elseif MyMenu.combo.modee:get() == 2 then
				player:castSpell("obj", 2, target)
			end
		end
		if MyMenu.combo.q:get() and player:spellSlot(0).state == 0 then
			if MyMenu.combo.mode:get() == 2 then
				CastQ(target)
			end
		end
		if	MyMenu.combo.w:get() and player:spellSlot(1).state == 0 and d < 500 and player:spellSlot(0).state ~= 0 then
			CastW(target)
		elseif MyMenu.combo.w:get() and player:spellSlot(1).state == 0 and d < 500 and player:spellSlot(2).state ~= 0 and player:spellSlot(0).state ~= 0 then
			CastW(target)
		end
	end
end

local function AutoUlt()
	if MyMenu.combo.r:get() and player:spellSlot(3).state == 0 then
		if #common.GetEnemyHeroesInRange(500) >= MyMenu.combo.rxe:get() and #common.GetAllyHeroesInRange(500) >=  MyMenu.combo.rxa:get() and common.GetPercentHealth() <=  MyMenu.combo.rhp:get() then
			player:castSpell("self", 3)
		elseif #common.GetEnemyHeroesInRange(500) > 0 and common.GetPercentHealth(player) <=  MyMenu.combo.rhp:get() then
			player:castSpell("self", 3)
		end
	end
end

local function Harass()
	if player.par / player.maxPar * 100 >= MyMenu.harass.Mana:get() then
		local target = get_target(select_target)
		if target and common.IsValidTarget(target) and not target.buff["sionpassivezombie"] then
			local range = player.attackRange + player.boundingRadius
			local d = player.path.serverPos:dist(target.path.serverPos)
			if MyMenu.harass.e:get() and player:spellSlot(2).state == 0 and d <= range then
				if MyMenu.harass.modee:get() == 1 and d <= player.attackRange + (player.boundingRadius + target.boundingRadius) and d > 200 then
					player:castSpell("obj", 2, target)
				elseif MyMenu.harass.modee:get() == 2 then
					player:castSpell("obj", 2, target)
				end
			end
			if MyMenu.harass.q:get() and player:spellSlot(0).state == 0 then
				if MyMenu.harass.mode:get() == 2 then
					CastQ(target)
				end
			end
			if	MyMenu.harass.w:get() and player:spellSlot(1).state == 0 and d < 500 and player:spellSlot(0).state ~= 0 then
				CastW(target)
			elseif MyMenu.harass.w:get() and player:spellSlot(1).state == 0 and d < 500 and player:spellSlot(2).state ~= 0 and player:spellSlot(0).state ~= 0 then
				CastW(target)
			end
		end
	end
end

local function Clear()
	local target = { obj = nil, health = 0, mode = "jungleclear" }
	local aaRange = player.attackRange + player.boundingRadius + 200
	for i = 0, minionmanager.size[TEAM_NEUTRAL] - 1 do
		local obj = minionmanager[TEAM_NEUTRAL][i]
		if player.pos:dist(obj.pos) <= aaRange and obj.maxHealth > target.health then
			target.obj = obj
			target.health = obj.maxHealth
		end
	end
	if target.obj then
		if target.mode == "jungleclear" then
			if player.par / player.maxPar * 100 >= MyMenu.jg.Mana:get() then
				--print(target.obj.charName)
				if MyMenu.jg.q:get() and player:spellSlot(0).state == 0 and player.path.serverPos:dist(target.obj.path.serverPos) < 340 then
					player:castSpell("pos", 0, game.mousePos)
				end
				if MyMenu.jg.e:get() and player:spellSlot(2).state == 0 and player.path.serverPos:dist(target.obj.path.serverPos) < player.attackRange + player.boundingRadius then
					player:castSpell("obj", 2, target.obj)
				end
				if MyMenu.jg.w:get() and player:spellSlot(1).state == 0 and player.path.serverPos:dist(target.obj.path.serverPos) < 500 then
					CastW(target.obj)
				end
			end
		end
	end
end

local function Run()
	if MyMenu.Key.run:get() then
		player:move((game.mousePos))
		if player:spellSlot(0).state == 0 and not navmesh.isWall(game.mousePos) then
			player:castSpell("pos", 0, (game.mousePos))
		end
	end
end

local function Time()
	local b = a + 40 - game.time
end

local function OnTick()
	if MyMenu.Key.Combo:get() then
		if orb.combat.target and orb.combat.target.buff["kindredrnodeathbuff"] and common.GetPercentHealth(orb.combat.target) <= 10 then return end
		Combo()
	end
	if orb.core.is_attack_paused() then
			orb.core.reset()
		end
	if MyMenu.Key.run:get() then
		Run()
	end
	if MyMenu.Key.Harass:get() then
		Harass()
	end
	if MyMenu.combo.r:get() then
		AutoUlt()
	end
	if MyMenu.Key.manual:get() then
		if common.GetPercentHealth(player) <=  40 and #common.GetEnemyHeroesInRange(700) > 0 then
			player:castSpell("self", 3)
		end
	end
	if MyMenu.Key.LaneClear:get() then
		Clear()
	end
end

local function OnDraw()
	local pos = graphics.world_to_screen(vec3(player.x, player.y-100, player.z))
	if MyMenu.draws.q:get() and player:spellSlot(0).state == 0 and player.isOnScreen then
		graphics.draw_circle(player.pos, 340, 2, MyMenu.draws.colorq:get(), 50)
	end
	if MyMenu.draws.e:get() and player:spellSlot(2).state == 0 and player.isOnScreen then
		graphics.draw_circle(player.pos, (player.attackRange + player.boundingRadius), 2, MyMenu.draws.colore:get(), 50)
	end
	if MyMenu.Key.time:get() then
		local b = a + 41 - game.time
	    if b < 0 then
	        a = game.time
	        graphics.draw_text_2D(tostring(math.floor(0)), 14, pos.x, pos.y, graphics.argb(255, 255, 255, 51))
	    else
	        graphics.draw_text_2D(tostring(math.floor(b)), 14, pos.x, pos.y, graphics.argb(255, 255, 255, 51))
	    end
	end
	if not MyMenu.Key.time:get() then
		a = 0
	end
	--graphics.draw_text_2D(tostring(math.floor(secondsLeft)), 20, pos.x, pos.y, graphics.argb(255, 51, 255, 51))
end

orb.combat.register_f_pre_tick(OnTick)
cb.add(cb.draw, OnDraw)
orb.combat.register_f_after_attack(roll)
orb.combat.register_f_out_of_range(out_of_aa)
cb.add(cb.error, function(msg)
  local log, e = io.open(hanbot.devpath..'/log.txt', 'w+')
  if not log then
    print(e)
    return
  end
 
  log:write(msg)
  log:close()
end)

return KindredPlugin
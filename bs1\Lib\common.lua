

	local slot_13_48 = {}
	local slot_13_49 = module.load(header.id, "bs/Lib/action_lock")
	local slot_13_50 = module.load(header.id, "bs/Lib/menu/ColorTools")
	local slot_13_51 = module.load(header.id, "bs/Lib/geometry")

	if game.mode:find("URF") then
		slot_13_48.URF = true
	end

	if game.mode:find("ARAM") then
		slot_13_48.ARAM = true
	end

	if game.mode:find("ULTBOOK") then
		slot_13_48.ULTBOOK = true
	end

	slot_13_48.MATCHED_GAME = game.mode == "CHERRY"
	slot_13_48.yas_wind_wall = {}
	slot_13_48.yas_wind_wall_ally = {}
	slot_13_48.geometry = slot_13_51
	slot_13_48.action_lock = slot_13_49

	function slot_13_48.argb(arg_21_0, arg_21_1, arg_21_2, arg_21_3)
		-- function 21
		return slot_13_50.argb(arg_21_0, arg_21_1, arg_21_2, arg_21_3)
	end

	slot_13_48.draw_this = 0

	function slot_13_48.argbmenu(arg_22_0)
		-- function 22
		return slot_13_50.argbmenu(arg_22_0)
	end
slot_13_48.debug = true
	function slot_13_48.d_debug(arg_23_0)
		-- function 23
		if slot_13_48.debug and arg_23_0 then
			chat.print("<font color='#ffff00' size='40'>" .. tostring(arg_23_0) .. "</font>")
			print(tostring(arg_23_0))
		end
	end

	function slot_13_48.d_debug_seg(arg_24_0)
		-- function 24
		if slot_13_48.debug and arg_24_0 then
			slot_13_48.file_debug(tostring(arg_24_0.hit) .. tostring(arg_24_0.data.debug))
		end
	end

	function slot_13_48.GetPreciseDecimal(arg_25_0, arg_25_1)
		-- function 25
		if type(arg_25_0) ~= "number" then
			return arg_25_0
		end

		arg_25_1 = arg_25_1 or 0
		arg_25_1 = math.floor(arg_25_1)

		if arg_25_1 < 0 then
			arg_25_1 = 0
		end

		if arg_25_1 == 0 then
			return math.ceil(arg_25_0)
		end

		local slot_25_0 = 10^arg_25_1
		local slot_25_1 = math.floor(arg_25_0 * slot_25_0) / slot_25_0

		if slot_25_1 < 0 then
			return 0
		end

		return slot_25_1
	end

	function slot_13_48.jshfhp(arg_26_0, arg_26_1)
		-- function 26
		local slot_26_0 = arg_26_0.healthRegenRate * arg_26_1

		if slot_26_0 < 0 then
			slot_26_0 = 0
		end

		return slot_26_0
	end

	function slot_13_48.draw_c(arg_27_0, arg_27_1, arg_27_2)
		-- function 27
		arg_27_2 = arg_27_2 or **********

		graphics:draw_circle_3d(arg_27_0, arg_27_1, arg_27_2, 100, 1)
	end

	--slot_13_48.keyicon = graphics.sprite("icon/Menu/key.png")
	--slot_13_48.farmicon = graphics.sprite("icon/Menu/farm.png")
	--slot_13_48.drawicon = graphics.sprite("icon/Menu/draw.png")
	--slot_13_48.settingicon = graphics.sprite("icon/Menu/setting.png")
	--slot_13_48.homeicon = graphics.sprite("icon/Menu/home.png")
	--slot_13_48.orbicon = graphics.sprite("icon/Menu/orbwallker.png")
	--slot_13_48.devicon = graphics.sprite("icon/Menu/dev.png")

	function slot_13_48.seticon(arg_28_0, arg_28_1)
		-- function 28
		local slot_28_0 = arg_28_1 or player

		if arg_28_0 and slot_28_0.iconSquare then
			arg_28_0:set("icon", slot_28_0.iconSquare)
		end

		if arg_28_0.q and slot_28_0:spellSlot(0).icon then
			arg_28_0.q:set("icon", slot_28_0:spellSlot(0).icon)
		end

		if arg_28_0.w and slot_28_0:spellSlot(1).icon then
			arg_28_0.w:set("icon", slot_28_0:spellSlot(1).icon)
		end

		if arg_28_0.e and slot_28_0:spellSlot(2).icon then
			arg_28_0.e:set("icon", slot_28_0:spellSlot(2).icon)
		end

		if arg_28_0.r and slot_28_0:spellSlot(3).icon then
			arg_28_0.r:set("icon", slot_28_0:spellSlot(3).icon)
		end

		if arg_28_0.keys and slot_13_48.keyicon then
			arg_28_0.keys:set("icon", slot_13_48.keyicon)
		end

		if arg_28_0.misc and slot_13_48.settingicon then
			arg_28_0.misc:set("icon", slot_13_48.settingicon)
		end

		if arg_28_0.kill and slot_13_48.orbicon then
			arg_28_0.kill:set("icon", slot_13_48.orbicon)
		end

		if arg_28_0.k and slot_13_48.orbicon then
			arg_28_0.k:set("icon", slot_13_48.orbicon)
		end

		if arg_28_0.farm and slot_13_48.farmicon then
			arg_28_0.farm:set("icon", slot_13_48.farmicon)
		end
	end

	local slot_13_52 = 10485760
	local slot_13_53 = hanbot.path .. "/ru_debug.log"

	function slot_13_48.file_debug(arg_29_0)
		-- function 29
		if log_file then
			if log_file:seek("end") > slot_13_52 then
				log_file = io.open(slot_13_53, "w")

				log_file:close()

				log_file = io.open(slot_13_53, "a")
			end

			if log_file then
				log_file:write(slot_13_48.timestampToDate(game.time) .. " - " .. arg_29_0 .. "\r\n")
				log_file:flush()
			end
		else
			log_file = io.open(slot_13_53, "a")
		end
	end

	local slot_13_54 = hanbot.path .. "/ru_pred_debug.log"
	local slot_13_55 = hanbot.path .. "/ru_OK_pred_debug.log"
	local slot_13_56 = hanbot.path .. "/ru_ERROR_pred_debug.log"

	function slot_13_48.pred_debug(arg_30_0)
		-- function 30
		if p_file then
			if p_file:seek("end") > slot_13_52 then
				p_file = io.open(slot_13_54, "w")

				p_file:close()

				p_file = io.open(slot_13_54, "a")
			end

			if p_file then
				p_file:write(slot_13_48.timestampToDate(game.time) .. " - " .. arg_30_0 .. "\r\n")
				p_file:flush()
			end
		else
			p_file = io.open(slot_13_54, "w")

			p_file:close()

			p_file = io.open(slot_13_54, "a")

			p_file:write(slot_13_48.timestampToDate(game.time) .. " - " .. arg_30_0 .. "\r\n")
			p_file:flush()
		end
	end

	function slot_13_48.ok_pred_debug(arg_31_0)
		-- function 31
		if ok_file then
			if ok_file:seek("end") > slot_13_52 then
				ok_file = io.open(slot_13_55, "w")

				ok_file:close()

				ok_file = io.open(slot_13_55, "a")
			end

			if ok_file then
				ok_file:write(slot_13_48.timestampToDate(game.time) .. " - " .. arg_31_0 .. "\r\n")
				ok_file:flush()
			end
		else
			ok_file = io.open(slot_13_55, "w")

			ok_file:close()

			ok_file = io.open(slot_13_55, "a")

			ok_file:write(slot_13_48.timestampToDate(game.time) .. " - " .. arg_31_0 .. "\r\n")
			ok_file:flush()
		end
	end

	function slot_13_48.error_pred_debug(arg_32_0)
		-- function 32
		if error_file then
			if error_file:seek("end") > slot_13_52 then
				error_file = io.open(slot_13_56, "w")

				error_file:close()

				error_file = io.open(slot_13_56, "a")
			end

			if error_file then
				error_file:write(slot_13_48.timestampToDate(game.time) .. " - " .. arg_32_0 .. "\r\n")
				error_file:flush()
			end
		else
			error_file = io.open(slot_13_56, "w")

			error_file:close()

			error_file = io.open(slot_13_56, "a")

			error_file:write(slot_13_48.timestampToDate(game.time) .. " - " .. arg_32_0 .. "\r\n")
			error_file:flush()
		end
	end

	local slot_13_57 = {}
	local slot_13_58 = {}

	function slot_13_48.InRange(arg_33_0, arg_33_1, arg_33_2)
		-- function 33
		local slot_33_0 = arg_33_2 or player

		return arg_33_0 >= slot_33_0.pos:dist(arg_33_1.pos) and arg_33_0 >= slot_33_0.path.serverPos:dist(arg_33_1.path.serverPos)
	end

	function slot_13_48.InAARange(arg_34_0, arg_34_1, arg_34_2)
		-- function 34
		arg_34_2 = arg_34_2 or 0

		local slot_34_0 = arg_34_1 or player
		local slot_34_1 = slot_13_48.GetAARange(arg_34_0, slot_34_0) + arg_34_2

		return slot_34_1 >= slot_34_0.pos:dist(arg_34_0.pos) and slot_34_1 >= slot_34_0.path.serverPos:dist(arg_34_0.path.serverPos)
	end

	function slot_13_48.GetAARange(arg_35_0, arg_35_1)
		-- function 35
		local slot_35_0 = arg_35_1 or player
		local slot_35_1 = slot_35_0.attackRange

		if slot_35_0.charName == "Zeri" then
			slot_35_1 = 500
		end

		return slot_35_1 + slot_35_0.boundingRadius + (arg_35_0 and arg_35_0.boundingRadius or 0)
	end

	function slot_13_48.GetAARange1(arg_36_0, arg_36_1)
		-- function 36
		local slot_36_0 = arg_36_1 or player
		local slot_36_1 = slot_36_0.attackRange

		if slot_36_0.charName == "Zeri" then
			slot_36_1 = 500
		end

		if arg_36_0 and slot_36_0.ptr == player.ptr and arg_36_0.type == TYPE_HERO then
			return TS.findRange(arg_36_0)
		end

		return slot_36_1 + slot_36_0.boundingRadius + (arg_36_0 and arg_36_0.boundingRadius or 0)
	end

	function slot_13_48.findRune(arg_37_0, arg_37_1)
		-- function 37
		local slot_37_0 = slot_13_58[arg_37_0.ptr]

		if slot_37_0 then
			return slot_37_0[arg_37_1]
		end
	end

	function slot_13_48.Init()
		-- function 38
		for iter_38_0 = 0, player.rune.size - 1 do
			local slot_38_0 = player.rune[iter_38_0]

			if slot_38_0 then
				slot_13_57[slot_38_0.id] = slot_38_0.name
			end
		end

		for iter_38_1, iter_38_2 in ipairs(slot_13_48.GetEnemyHeroes()) do
			local slot_38_1 = {}

			for iter_38_3 = 0, iter_38_2.rune.size - 1 do
				local slot_38_2 = iter_38_2.rune[iter_38_3]

				if slot_38_2 then
					slot_38_1[slot_38_2.id] = slot_38_2.name
				end
			end

			slot_13_58[iter_38_2.ptr] = slot_38_1
		end

		for iter_38_4, iter_38_5 in ipairs(slot_13_48.GetAllyHeroes()) do
			local slot_38_3 = {}

			for iter_38_6 = 0, iter_38_5.rune.size - 1 do
				local slot_38_4 = iter_38_5.rune[iter_38_6]

				if slot_38_4 then
					slot_38_3[slot_38_4.id] = slot_38_4.name
				end
			end

			slot_13_58[iter_38_5.ptr] = slot_38_3
		end
	end

	local slot_13_59 = 0
	local slot_13_60 = 0
	local slot_13_61 = 0
	local slot_13_62 = -math.huge
	local slot_13_63 = -math.huge
	local slot_13_64

	local function slot_13_65()
		-- function 39
		if not slot_13_64 then
			function slot_13_64()
				-- function 40
				slot_13_59 = 1 / (os.clock() - slot_13_63)
				slot_13_63, slot_13_61 = os.clock(), slot_13_61 + 1

				if os.clock() < 0.5 + slot_13_62 then
					return
				end

				slot_13_60 = math.floor(slot_13_61 / (os.clock() - slot_13_62))
				slot_13_62, slot_13_61 = os.clock(), 0
			end
		end
	end

	function slot_13_48.draw_fps()
		-- function 41
		if slot_13_64 then
			slot_13_64()
		end
	end

	function slot_13_48.create_missile(arg_42_0)
		-- function 42
		if arg_42_0 and arg_42_0.spell then
			local slot_42_0 = arg_42_0.spell.name
			local slot_42_1 = arg_42_0.spell.owner

			if slot_42_0 == "YasuoW_VisualMis" and slot_42_1 then
				if slot_42_1.team ~= TEAM_ALLY then
					slot_13_48.yas_wind_wall[#slot_13_48.yas_wind_wall + 1] = arg_42_0
				else
					slot_13_48.yas_wind_wall_ally[#slot_13_48.yas_wind_wall_ally + 1] = arg_42_0
				end
			end
		end
	end

	function slot_13_48.delete_missile(arg_43_0)
		-- function 43
		if #slot_13_48.yas_wind_wall > 0 then
			for iter_43_0 = 1, #slot_13_48.yas_wind_wall do
				local slot_43_0 = slot_13_48.yas_wind_wall[iter_43_0]

				if slot_43_0 and arg_43_0 == slot_43_0.ptr then
					table.remove(slot_13_48.yas_wind_wall, iter_43_0)
				end
			end
		end

		if #slot_13_48.yas_wind_wall_ally > 0 then
			for iter_43_1 = 1, #slot_13_48.yas_wind_wall_ally do
				local slot_43_1 = slot_13_48.yas_wind_wall_ally[iter_43_1]

				if slot_43_1 and arg_43_0 == slot_43_1.ptr then
					table.remove(slot_13_48.yas_wind_wall_ally, iter_43_1)
				end
			end
		end
	end

	function slot_13_48.VectorDirection(arg_44_0, arg_44_1, arg_44_2)
		-- function 44
		return (arg_44_2.y - arg_44_0.y) * (arg_44_1.x - arg_44_0.x) - (arg_44_1.y - arg_44_0.y) * (arg_44_2.x - arg_44_0.x)
	end

	function slot_13_48.IsLineSegmentIntersection(arg_45_0, arg_45_1, arg_45_2, arg_45_3)
		-- function 45
		return (slot_13_48.VectorDirection(arg_45_0, arg_45_2, arg_45_3) <= 0 and slot_13_48.VectorDirection(arg_45_1, arg_45_2, arg_45_3) > 0 or slot_13_48.VectorDirection(arg_45_0, arg_45_2, arg_45_3) > 0 and slot_13_48.VectorDirection(arg_45_1, arg_45_2, arg_45_3) <= 0) and (slot_13_48.VectorDirection(arg_45_0, arg_45_1, arg_45_2) <= 0 and slot_13_48.VectorDirection(arg_45_0, arg_45_1, arg_45_3) > 0 or slot_13_48.VectorDirection(arg_45_0, arg_45_1, arg_45_2) > 0 and slot_13_48.VectorDirection(arg_45_0, arg_45_1, arg_45_3) <= 0)
	end

	local slot_13_66 = {
		320,
		390,
		460,
		530,
		600
	}

	function slot_13_48.YasuoWall_check(arg_46_0, arg_46_1, arg_46_2)
		-- function 46
		if #slot_13_48.yas_wind_wall == 0 and not arg_46_2 then
			return false
		end

		local slot_46_0 = to2D(arg_46_0)
		local slot_46_1 = to2D(arg_46_1)
		local slot_46_2 = false
		local slot_46_3 = arg_46_2 and slot_13_48.yas_wind_wall_ally or slot_13_48.yas_wind_wall

		for iter_46_0 = 1, #slot_46_3 do
			local slot_46_4 = slot_46_3[iter_46_0]

			if slot_46_4 and slot_46_4.spell and slot_46_4.spell.owner then
				local slot_46_5 = slot_13_66[slot_46_4.spell.owner:spellSlot(1).level] / 2
				local slot_46_6 = slot_46_4.pos + perp2((slot_46_4.startPos - slot_46_4.pos):norm()) * slot_46_5
				local slot_46_7 = slot_46_4.pos + perp2((slot_46_4.startPos - slot_46_4.pos):norm()) * -slot_46_5
				local slot_46_8 = perp1((to2D(slot_46_7) - to2D(slot_46_6)):norm())
				local slot_46_9 = to2D(slot_46_6) - slot_46_8 * 75
				local slot_46_10 = to2D(slot_46_6) + slot_46_8 * 75
				local slot_46_11 = to2D(slot_46_7) + slot_46_8 * 75
				local slot_46_12 = to2D(slot_46_7) - slot_46_8 * 75

				if slot_13_48.IsLineSegmentIntersection(slot_46_9, slot_46_10, slot_46_0, slot_46_1) then
					return arg_46_0 + (arg_46_1 - arg_46_0):norm() * arg_46_0:dist(slot_46_4.pos)
				end

				if slot_13_48.IsLineSegmentIntersection(slot_46_9, slot_46_12, slot_46_0, slot_46_1) then
					return arg_46_0 + (arg_46_1 - arg_46_0):norm() * arg_46_0:dist(slot_46_4.pos)
				end

				if slot_13_48.IsLineSegmentIntersection(slot_46_11, slot_46_10, slot_46_0, slot_46_1) then
					return arg_46_0 + (arg_46_1 - arg_46_0):norm() * arg_46_0:dist(slot_46_4.pos)
				end

				if slot_13_48.IsLineSegmentIntersection(slot_46_11, slot_46_12, slot_46_0, slot_46_1) then
					return arg_46_0 + (arg_46_1 - arg_46_0):norm() * arg_46_0:dist(slot_46_4.pos)
				end
			end
		end

		return slot_46_2
	end

	function slot_13_48.GetFPS()
		-- function 47
		slot_13_65()

		return slot_13_59
	end

	local function slot_13_67(arg_48_0)
		-- function 48
		if arg_48_0.buff.kindredrnodeathbuff and slot_13_48.GetHp(arg_48_0) <= 15 then
			return false
		end

		return true
	end

	function slot_13_48.checkBuff(arg_49_0)
		-- function 49
		if arg_49_0.type == TYPE_HERO and arg_49_0.ptr ~= player.ptr then
			return not arg_49_0.buff[BUFF_INVULNERABILITY] and not arg_49_0.buff.sionpassivezombie and not arg_49_0.buff.sivire and not arg_49_0.buff.fioraw and not arg_49_0.buff.undyingrage and not arg_49_0.buff.kayler and not arg_49_0.isZombie and slot_13_67(arg_49_0)
		end

		return true
	end

	function slot_13_48.checkIBuff(arg_50_0)
		-- function 50
		if arg_50_0.type == TYPE_HERO and arg_50_0.ptr ~= player.ptr then
			return arg_50_0.buff[BUFF_INVULNERABILITY] or arg_50_0.buff.sionpassivezombie or arg_50_0.buff.sivire or arg_50_0.buff.fioraw or arg_50_0.buff.undyingrage or arg_50_0.buff.kayler or arg_50_0.buff.kindredrnodeathbuff
		end
	end

	function slot_13_48.checkHBuff(arg_51_0)
		-- function 51
		if arg_51_0.type == TYPE_HERO and arg_51_0.ptr ~= player.ptr then
			return not arg_51_0.buff[BUFF_INVULNERABILITY] or not arg_51_0.buff.morganae
		end
	end

	function slot_13_48.CheckBuffType(arg_52_0, arg_52_1)
		-- function 52
		if arg_52_0 and arg_52_0.buffManager and arg_52_0.buffManager.count then
			for iter_52_0 = 0, arg_52_0.buffManager.count - 1 do
				local slot_52_0 = arg_52_0.buffManager:get(iter_52_0)

				if slot_52_0 and slot_52_0.valid and slot_52_0.type == arg_52_1 and (slot_52_0.stacks > 0 or slot_52_0.stacks2 > 0) then
					return true
				end
			end
		end
	end

	function slot_13_48.isorbTarget(arg_53_0, arg_53_1)
		-- function 53
		return arg_53_0 and not arg_53_0.isDead and arg_53_0.isVisible and arg_53_0.isTargetable
	end

	function slot_13_48.isTarget(arg_54_0, arg_54_1)
		-- function 54
		return arg_54_0 and not arg_54_0.isDead and arg_54_0.isVisible and arg_54_0.isTargetable and slot_13_48.checkBuff(arg_54_0) and (not arg_54_1 or type(arg_54_1) ~= "number" and slot_13_48.checkHBuff(arg_54_0) or type(arg_54_1) == "number" and arg_54_1 > arg_54_0.pos:dist(player.pos) and (arg_54_0.type == TYPE_HERO or slot_13_48.CheckMinios(arg_54_0.name)))
	end

	slot_13_48.IsValidTarget = slot_13_48.isTarget

	function slot_13_48.GetMinionInRange(arg_55_0, arg_55_1)
		-- function 55
		arg_55_1 = arg_55_1 or player.pos

		local slot_55_0 = {}

		for iter_55_0 = 0, objManager.minions.size[TEAM_NEUTRAL] - 1 do
			local slot_55_1 = objManager.minions[TEAM_NEUTRAL][iter_55_0]

			if slot_13_48.isTarget(slot_55_1) and arg_55_0 >= slot_55_1.pos:dist(arg_55_1) and slot_13_48.CheckMinios(slot_55_1.name) then
				slot_55_0[#slot_55_0 + 1] = slot_55_1
			end
		end

		for iter_55_1 = 0, objManager.minions.size[TEAM_ENEMY] - 1 do
			local slot_55_2 = objManager.minions[TEAM_ENEMY][iter_55_1]

			if slot_13_48.isTarget(slot_55_2) and arg_55_0 >= slot_55_2.pos:dist(arg_55_1) and slot_13_48.CheckMinios(slot_55_2.name) then
				slot_55_0[#slot_55_0 + 1] = slot_55_2
			end
		end

		return slot_55_0
	end

	function slot_13_48.GetEnemyMinionInRange(arg_56_0, arg_56_1)
		-- function 56
		local slot_56_0 = {}

		for iter_56_0 = 0, objManager.minions.size[TEAM_ENEMY] - 1 do
			local slot_56_1 = objManager.minions[TEAM_ENEMY][iter_56_0]

			if slot_13_48.isTarget(slot_56_1) and arg_56_0 >= slot_56_1.pos:dist(arg_56_1) and slot_13_48.CheckMinios(slot_56_1.name) then
				slot_56_0[#slot_56_0 + 1] = slot_56_1
			end
		end

		return slot_56_0
	end

	function slot_13_48.GetJungleMinionInRange(arg_57_0, arg_57_1)
		-- function 57
		local slot_57_0 = {}

		for iter_57_0 = 0, objManager.minions.size[TEAM_NEUTRAL] - 1 do
			local slot_57_1 = objManager.minions[TEAM_NEUTRAL][iter_57_0]

			if slot_13_48.isTarget(slot_57_1) and arg_57_0 >= slot_57_1.pos:dist(arg_57_1) and slot_13_48.CheckMinios(slot_57_1.name) then
				slot_57_0[#slot_57_0 + 1] = slot_57_1
			end
		end

		return slot_57_0
	end

	function slot_13_48.GetAllyMinionInRange(arg_58_0, arg_58_1)
		-- function 58
		local slot_58_0 = {}

		for iter_58_0 = 0, objManager.minions.size[TEAM_ALLY] - 1 do
			local slot_58_1 = objManager.minions[TEAM_ALLY][iter_58_0]

			if slot_13_48.isTarget(slot_58_1) and arg_58_0 >= slot_58_1.pos:dist(arg_58_1) and slot_13_48.CheckMinios(slot_58_1.name) then
				slot_58_0[#slot_58_0 + 1] = slot_58_1
			end
		end

		return slot_58_0
	end

	function slot_13_48.GetMinionFunc(arg_59_0)
		-- function 59
		local slot_59_0 = {}

		for iter_59_0 = 0, objManager.minions.size[TEAM_NEUTRAL] - 1 do
			local slot_59_1 = objManager.minions[TEAM_NEUTRAL][iter_59_0]

			if slot_13_48.isTarget(slot_59_1) and arg_59_0(slot_59_1) and slot_13_48.CheckMinios(slot_59_1.name) then
				slot_59_0[#slot_59_0 + 1] = slot_59_1
			end
		end

		for iter_59_1 = 0, objManager.minions.size[TEAM_ENEMY] - 1 do
			local slot_59_2 = objManager.minions[TEAM_ENEMY][iter_59_1]

			if slot_13_48.isTarget(slot_59_2) and arg_59_0(slot_59_2) and slot_13_48.CheckMinios(slot_59_2.name) then
				slot_59_0[#slot_59_0 + 1] = slot_59_2
			end
		end

		return slot_59_0
	end

	function slot_13_48.GetAllInRange(arg_60_0, arg_60_1, arg_60_2)
		-- function 60
		local slot_60_0 = {}

		for iter_60_0, iter_60_1 in ipairs(slot_13_48.GetEnemyHeroes()) do
			if slot_13_48.isTarget(iter_60_1) and arg_60_0 >= iter_60_1.pos:dist(arg_60_1) and slot_13_48.CheckMinios(iter_60_1.name) and (not arg_60_2 or iter_60_1.ptr ~= arg_60_2.ptr) then
				slot_60_0[#slot_60_0 + 1] = iter_60_1
			end
		end

		for iter_60_2 = 0, objManager.minions.size[TEAM_ENEMY] - 1 do
			local slot_60_1 = objManager.minions[TEAM_ENEMY][iter_60_2]

			if slot_13_48.isTarget(slot_60_1) and arg_60_0 >= slot_60_1.pos:dist(arg_60_1) and slot_13_48.CheckMinios(slot_60_1.name) then
				slot_60_0[#slot_60_0 + 1] = slot_60_1
			end
		end

		for iter_60_3 = 0, objManager.minions.size[TEAM_NEUTRAL] - 1 do
			local slot_60_2 = objManager.minions[TEAM_NEUTRAL][iter_60_3]

			if slot_13_48.isTarget(slot_60_2) and arg_60_0 >= slot_60_2.pos:dist(arg_60_1) and slot_13_48.CheckMinios(slot_60_2.name) then
				slot_60_0[#slot_60_0 + 1] = slot_60_2
			end
		end

		return slot_60_0
	end

	function slot_13_48.GetAllyHeroesInRange(arg_61_0, arg_61_1)
		-- function 61
		local slot_61_0 = arg_61_1 or player.pos
		local slot_61_1 = {}

		for iter_61_0, iter_61_1 in ipairs(slot_13_48.GetAllyHeroes()) do
			if iter_61_1 and not iter_61_1.isDead and iter_61_1.isVisible and iter_61_1.pos:distSqr(slot_61_0) < arg_61_0 * arg_61_0 and iter_61_1.charName ~= "Yuumi" then
				slot_61_1[#slot_61_1 + 1] = iter_61_1
			end
		end

		return slot_61_1
	end

	function slot_13_48.GetAllyHeroesInRange2(arg_62_0, arg_62_1)
		-- function 62
		local slot_62_0 = arg_62_1 or player.pos
		local slot_62_1 = {}

		for iter_62_0, iter_62_1 in ipairs(slot_13_48.GetAllyHeroes()) do
			if iter_62_1 and not iter_62_1.isDead and iter_62_1.isVisible and iter_62_1.ptr ~= player.ptr and iter_62_1.pos:distSqr(slot_62_0) < arg_62_0 * arg_62_0 and iter_62_1.charName ~= "Yuumi" then
				slot_62_1[#slot_62_1 + 1] = iter_62_1
			end
		end

		return slot_62_1
	end

	function slot_13_48.count_allies_in_range(arg_63_0, arg_63_1)
		-- function 63
		return slot_13_48.GetAllyHeroesInRange(arg_63_1, arg_63_0)
	end

	function slot_13_48.GetEnemyHeroesInRange(arg_64_0, arg_64_1, arg_64_2)
		-- function 64
		local slot_64_0 = arg_64_1 or player.pos
		local slot_64_1 = {}

		for iter_64_0, iter_64_1 in ipairs(slot_13_48.GetEnemyHeroes()) do
			if iter_64_1 and not iter_64_1.isDead and iter_64_1.isVisible and iter_64_1.pos:distSqr(slot_64_0) < arg_64_0 * arg_64_0 and (not arg_64_2 or iter_64_1.ptr ~= arg_64_2) then
				slot_64_1[#slot_64_1 + 1] = iter_64_1
			end
		end

		return slot_64_1
	end

	slot_13_48.GetEnemyHeroInRange = slot_13_48.GetEnemyHeroesInRange

	function slot_13_48.GetEnemyHeroesInRange1(arg_65_0, arg_65_1, arg_65_2)
		-- function 65
		local slot_65_0 = arg_65_1 or player.pos
		local slot_65_1 = {}

		for iter_65_0, iter_65_1 in ipairs(slot_13_48.GetEnemyHeroes()) do
			if iter_65_1 and not iter_65_1.isDead and iter_65_1.pos:distSqr(slot_65_0) < arg_65_0 * arg_65_0 and (not arg_65_2 or iter_65_1.ptr ~= arg_65_2) then
				slot_65_1[#slot_65_1 + 1] = iter_65_1
			end
		end

		return slot_65_1
	end

	function slot_13_48.CountEnemiesInRange(arg_66_0, arg_66_1)
		-- function 66
		return slot_13_48.GetEnemyHeroesInRange(arg_66_1, arg_66_0)
	end

	function slot_13_48.CountEnemiesInRange1(arg_67_0, arg_67_1)
		-- function 67
		return slot_13_48.GetEnemyHeroesInRange1(arg_67_1, arg_67_0)
	end

	slot_13_48.count_enemies_in_range = slot_13_48.CountEnemiesInRange
	slot_13_48.count_enemies_in_range1 = slot_13_48.CountEnemiesInRange

	function slot_13_48.isnotmove(arg_68_0)
		-- function 68
		return arg_68_0.buff[BUFF_STUN] or arg_68_0.buff[BUFF_TAUNT] or arg_68_0.buff[BUFF_SNARE] or arg_68_0.buff[BUFF_FEAR] or arg_68_0.buff[BUFF_KNOCKUP] or arg_68_0.buff[BUFF_KNOCKBACK] or arg_68_0.buff[BUFF_FLEE] or arg_68_0.buff[BUFF_SUPPRESSION] or arg_68_0.buff[BUFF_CHARM] or arg_68_0.buff[BUFF_ASLEEP] or arg_68_0.buff.karthusfallenonetarget or arg_68_0.buff.briare or arg_68_0.buff.katarinar or arg_68_0.buff.missfortunebulletsound or arg_68_0.buff.meditate or arg_68_0.buff.belvethe or arg_68_0.buff.belvethrpresentation or arg_68_0.buff.lissandrarenemy or arg_68_0.buff.namiqdebuff
	end

	function slot_13_48.iscc(arg_69_0)
		-- function 69
		return arg_69_0.buff[BUFF_STUN] or arg_69_0.buff[BUFF_TAUNT] or arg_69_0.buff[BUFF_SNARE] or arg_69_0.buff[BUFF_FEAR] or arg_69_0.buff[BUFF_KNOCKUP] or arg_69_0.buff[BUFF_KNOCKBACK] or arg_69_0.buff[BUFF_FLEE] or arg_69_0.buff[BUFF_SUPPRESSION] or arg_69_0.buff[BUFF_CHARM] or arg_69_0.buff[BUFF_ASLEEP]
	end

	function slot_13_48.isCC1(arg_70_0)
		-- function 70
		return arg_70_0.buff[BUFF_STUN] or arg_70_0.buff[BUFF_TAUNT] or arg_70_0.buff[BUFF_SNARE] or arg_70_0.buff[BUFF_FEAR] or arg_70_0.buff[BUFF_KNOCKUP] or arg_70_0.buff[BUFF_KNOCKBACK] or arg_70_0.buff[BUFF_FLEE] or arg_70_0.buff[BUFF_SUPPRESSION] or arg_70_0.buff[BUFF_CHARM] or arg_70_0.buff[BUFF_ASLEEP]
	end

	slot_13_48.isCC = slot_13_48.iscc

	function slot_13_48.text_area1(arg_71_0, arg_71_1)
		-- function 71
		local slot_71_0 = #arg_71_0
		local slot_71_1 = 0
		local slot_71_2 = 1

		while slot_71_2 <= slot_71_0 do
			local slot_71_3 = string.byte(arg_71_0, slot_71_2)
			local slot_71_4 = 1

			if slot_71_3 > 0 and slot_71_3 <= 127 then
				slot_71_4 = 1
			elseif slot_71_3 >= 192 and slot_71_3 < 223 then
				slot_71_4 = 2
			elseif slot_71_3 >= 224 and slot_71_3 < 239 then
				slot_71_4 = 2
			elseif slot_71_3 >= 240 and slot_71_3 <= 247 then
				slot_71_4 = 4
			end

			local slot_71_5 = string.sub(arg_71_0, slot_71_2, slot_71_2 + slot_71_4 - 1)

			slot_71_2 = slot_71_2 + slot_71_4
			slot_71_1 = slot_71_1 + 1
		end

		return math.floor(slot_71_1 * 0.25 * arg_71_1)
	end

	function slot_13_48.text_area(arg_72_0, arg_72_1)
		-- function 72
		local slot_72_0 = #arg_72_0
		local slot_72_1 = 0
		local slot_72_2 = 1

		while slot_72_2 <= slot_72_0 do
			local slot_72_3 = string.byte(arg_72_0, slot_72_2)
			local slot_72_4 = 1

			if slot_72_3 > 0 and slot_72_3 <= 127 then
				slot_72_4 = 1
			elseif slot_72_3 >= 192 and slot_72_3 < 223 then
				slot_72_4 = 2
			elseif slot_72_3 >= 224 and slot_72_3 < 239 then
				slot_72_4 = 3
			elseif slot_72_3 >= 240 and slot_72_3 <= 247 then
				slot_72_4 = 4
			end

			local slot_72_5 = string.sub(arg_72_0, slot_72_2, slot_72_2 + slot_72_4 - 1)

			slot_72_2 = slot_72_2 + slot_72_4
			slot_72_1 = slot_72_1 + 1
		end

		return math.floor(slot_72_1 * arg_72_1)
	end

	function slot_13_48.DamageReduction(arg_73_0, arg_73_1, arg_73_2)
		-- function 73
		local slot_73_0

		slot_73_0 = arg_73_2 or player

		local slot_73_1 = 1

		if arg_73_0 == "AD" then
			-- block empty
		end

		if arg_73_0 == "AP" then
			-- block empty
		end

		return slot_73_1
	end

	local slot_13_68 = {
		SRU_Gromp = 1,
		Sru_Crab = 1,
		SRU_Blue = 1,
		SRU_Krug = 1,
		SRU_Red = 1,
		SRU_Razorbeak = 1,
		SRU_Murkwolf = 1
	}

	function slot_13_48.PhysicalReduction(arg_74_0, arg_74_1)
		-- function 74
		local slot_74_0 = arg_74_1 or player
		local slot_74_1 = (arg_74_0.bonusArmor * slot_74_0.percentBonusArmorPenetration + (arg_74_0.armor - arg_74_0.bonusArmor)) * slot_74_0.percentArmorPenetration
		local slot_74_2 = slot_74_0.physicalLethality * 0.4 + slot_74_0.physicalLethality * 0.6 * (slot_74_0.levelRef / 18)
		local slot_74_3 = slot_74_1 >= 0 and 100 / (100 + (slot_74_1 - slot_74_2)) or 2 - 100 / (100 - (slot_74_1 - slot_74_2))

		if arg_74_0.type == TYPE_MINION then
			slot_74_3 = math.min(1, slot_74_3)
		end

		return slot_74_3
	end

	function slot_13_48.MagicReduction(arg_75_0, arg_75_1)
		-- function 75
		local slot_75_0 = arg_75_1 or player
		local slot_75_1 = arg_75_0.spellBlock * slot_75_0.percentMagicPenetration - slot_75_0.flatMagicPenetration

		return slot_75_1 >= 0 and 100 / (100 + slot_75_1) or 2 - 100 / (100 - slot_75_1)
	end

	function slot_13_48.CalculatePhysicalDamage(arg_76_0, arg_76_1, arg_76_2)
		-- function 76
		local slot_76_0 = arg_76_2 or player

		if arg_76_0 then
			return arg_76_1 * slot_13_48.PhysicalReduction(arg_76_0, slot_76_0) * slot_13_48.DamageReduction("AD", arg_76_0, slot_76_0)
		end

		return 0
	end

	function slot_13_48.CalculateMagicDamage(arg_77_0, arg_77_1, arg_77_2)
		-- function 77
		local slot_77_0 = arg_77_2 or player

		if arg_77_0 then
			return arg_77_1 * slot_13_48.MagicReduction(arg_77_0, slot_77_0) * slot_13_48.DamageReduction("AP", arg_77_0, slot_77_0)
		end

		return 0
	end

	local slot_13_69 = {
		0.55,
		0.65,
		0.75
	}

	function slot_13_48.CalculateAADamage(arg_78_0, arg_78_1)
		-- function 78
		local slot_78_0 = arg_78_1 or player

		if arg_78_0 then
			if arg_78_0.type == TYPE_HERO then
				local slot_78_1 = slot_13_48.GetTotalAD(slot_78_0)

				if slot_13_58[arg_78_0.ptr] and slot_13_58[arg_78_0.ptr][8014] and slot_13_48.GetHp(arg_78_0) < 40 then
					slot_78_1 = slot_78_1 + slot_78_1 * 0.08
				end

				if arg_78_0.buff.ferocioushowl then
					slot_78_1 = slot_78_1 * (1 - slot_13_69[arg_78_0:spellSlot(3).level])
				end

				local slot_78_2 = slot_13_48.CAD(arg_78_0, slot_78_1)

				return data.get_damage(slot_78_2, arg_78_0, slot_78_0, 1)
			end

			return slot_13_48.GetTotalAD(slot_78_0) * slot_13_48.PhysicalReduction(arg_78_0, slot_78_0)
		end

		return 0
	end

	function slot_13_48.GetTotalAD(arg_79_0)
		-- function 79
		return (arg_79_0 or player).totalAd
	end

	function slot_13_48.GetTotalAP(arg_80_0)
		-- function 80
		return (arg_80_0 or player).totalAp
	end

	function slot_13_48.GetBonusAD(arg_81_0)
		-- function 81
		local slot_81_0 = arg_81_0 or player

		return (slot_81_0.baseAttackDamage + slot_81_0.flatPhysicalDamageMod) * slot_81_0.percentPhysicalDamageMod - slot_81_0.baseAttackDamage
	end

	function slot_13_48.GetHp(arg_82_0, arg_82_1)
		-- function 82
		local slot_82_0 = arg_82_1 or 0
		local slot_82_1 = arg_82_0 or player

		return (slot_82_1.health - slot_82_0) / slot_82_1.maxHealth * 100
	end

	slot_13_48.GetPercentHealth = slot_13_48.GetHp

	function slot_13_48.GetMana(arg_83_0)
		-- function 83
		local slot_83_0 = arg_83_0 or player

		return slot_83_0.mana / slot_83_0.maxMana * 100
	end

	slot_13_48.GetPercentMana = slot_13_48.GetMana

	function slot_13_48.check_crit(arg_84_0)
		-- function 84
		local slot_84_0 = 0

		if arg_84_0 == 1018 then
			slot_84_0 = slot_84_0 + 15
		elseif arg_84_0 == 3086 then
			slot_84_0 = slot_84_0 + 15
		elseif arg_84_0 == 3094 then
			slot_84_0 = slot_84_0 + 20
		elseif arg_84_0 == 3033 then
			slot_84_0 = slot_84_0 + 20
		elseif arg_84_0 == 3124 then
			slot_84_0 = slot_84_0 + 20
		elseif arg_84_0 == 3085 then
			slot_84_0 = slot_84_0 + 20
		elseif arg_84_0 == 3046 then
			slot_84_0 = slot_84_0 + 20
		elseif arg_84_0 == 3046 then
			slot_84_0 = slot_84_0 + 20
		elseif arg_84_0 == 3508 then
			slot_84_0 = slot_84_0 + 20
		elseif arg_84_0 == 6676 then
			slot_84_0 = slot_84_0 + 20
		elseif arg_84_0 == 3139 then
			slot_84_0 = slot_84_0 + 20
		elseif arg_84_0 == 3036 then
			slot_84_0 = slot_84_0 + 20
		elseif arg_84_0 == 3031 then
			slot_84_0 = slot_84_0 + 20
		elseif arg_84_0 == 3072 then
			slot_84_0 = slot_84_0 + 20
		elseif arg_84_0 == 6672 then
			slot_84_0 = slot_84_0 + 20
		elseif arg_84_0 == 6671 then
			slot_84_0 = slot_84_0 + 20
		elseif arg_84_0 == 6673 then
			slot_84_0 = slot_84_0 + 20
		end

		return slot_84_0
	end

	function slot_13_48.getBuffStacks(arg_85_0, arg_85_1, arg_85_2)
		-- function 85
		assert(arg_85_0, "getBuffStacks: no target")
		assert(arg_85_1, "getBuffStacks: no buffname/type")

		local slot_85_0 = arg_85_0.buff[arg_85_1]

		return slot_85_0 and (arg_85_2 == false or slot_85_0.endTime > game.time) and math.max(slot_85_0.stacks, slot_85_0.stacks2) or 0
	end

	local slot_13_70 = {}
	local slot_13_71

	function slot_13_48.DelayAction(arg_86_0, arg_86_1, arg_86_2)
		-- function 86
		if not slot_13_71 then
			function slot_13_71()
				-- function 87
				for iter_87_0, iter_87_1 in pairs(slot_13_70) do
					if iter_87_0 <= os.clock() then
						for iter_87_2 = 1, #iter_87_1 do
							local slot_87_0 = iter_87_1[iter_87_2]

							if slot_87_0 and slot_87_0.func then
								slot_87_0.func(unpack(slot_87_0.args or {}))
							end
						end

						slot_13_70[iter_87_0] = nil
					end
				end
			end

			add_tick(slot_13_71)
		end

		local slot_86_0 = os.clock() + (arg_86_1 or 0)

		if slot_13_70[slot_86_0] then
			slot_13_70[slot_86_0][#slot_13_70[slot_86_0] + 1] = {
				func = arg_86_0,
				args = arg_86_2
			}
		else
			slot_13_70[slot_86_0] = {
				{
					func = arg_86_0,
					args = arg_86_2
				}
			}
		end
	end

	function slot_13_48.GetShieldedHealth(arg_88_0, arg_88_1)
		-- function 88
		local slot_88_0 = 0

		if arg_88_0 == "AD" then
			slot_88_0 = arg_88_1.physicalShield + arg_88_1.allShield
		elseif arg_88_0 == "AP" then
			slot_88_0 = arg_88_1.magicalShield + arg_88_1.allShield
		elseif arg_88_0 == "ALL" then
			slot_88_0 = arg_88_1.allShield
		end

		return arg_88_1.health + slot_88_0
	end

	function slot_13_48.GetShielded(arg_89_0, arg_89_1)
		-- function 89
		local slot_89_0 = 0

		if arg_89_0 == "AD" then
			slot_89_0 = arg_89_1.physicalShield + arg_89_1.allShield
		elseif arg_89_0 == "AP" then
			slot_89_0 = arg_89_1.magicalShield + arg_89_1.allShield
		elseif arg_89_0 == "ALL" then
			slot_89_0 = arg_89_1.allShield
		end

		return slot_89_0
	end

	local slot_13_72 = {
		Ashe = function(arg_90_0, arg_90_1, arg_90_2, arg_90_3, arg_90_4)
			-- function 90
			local slot_90_0 = slot_13_48.getBuffStacks(arg_90_0, "asheqattack")
			local slot_90_1 = 1 + arg_90_0.crit * 0.75

			if slot_90_0 > 0 then
				slot_90_1 = slot_90_1 + (0.05 + 0.05 * arg_90_0:spellSlot(0).level)
			end

			local slot_90_2 = false

			if slot_90_0 > 0 then
				slot_90_2 = true
			end

			return arg_90_2 * slot_90_1, arg_90_3, arg_90_4, slot_90_2
		end,
		Aphelios = function(arg_91_0, arg_91_1, arg_91_2, arg_91_3, arg_91_4)
			-- function 91
			if arg_91_0:spellSlot(0) then
				local slot_91_0 = arg_91_0:spellSlot(0).name

				arg_91_2 = slot_91_0 == "ApheliosInfernumQ" and arg_91_2 * 1.1 or arg_91_2

				local slot_91_1 = slot_13_48.getBuffStacks(arg_91_0, "aphelioscrescendumorbitmanager")

				if slot_91_1 > 0 and slot_91_0 == "ApheliosCrescendumQ" then
					local slot_91_2 = slot_91_1

					if slot_91_2 > 20 then
						slot_91_2 = 20
					end

					local slot_91_3 = ({
						0.24,
						0.45,
						0.625,
						0.765,
						0.87,
						0.94,
						0.99,
						1.04,
						1.09,
						1.14,
						1.19,
						1.24,
						1.29,
						1.34,
						1.39,
						1.44,
						1.49,
						1.54,
						1.59,
						1.64
					})[tonumber(slot_91_2)]

					if slot_91_3 then
						arg_91_2 = arg_91_2 + arg_91_2 * slot_91_3
					end
				end

				local slot_91_4 = false

				return arg_91_2, arg_91_3, arg_91_4, slot_91_4
			end

			return arg_91_2, arg_91_3, arg_91_4, call
		end,
		Varus = function(arg_92_0, arg_92_1, arg_92_2, arg_92_3, arg_92_4)
			-- function 92
			return arg_92_2, arg_92_3 + (slot_13_48.getBuffStacks(arg_92_0, "varusw") > 0 and arg_92_0:spellSlot(1).level * 6 + 0.35 * slot_13_48.GetTotalAP(arg_92_0) or 0), arg_92_4, false
		end,
		Corki = function(arg_93_0, arg_93_1, arg_93_2, arg_93_3, arg_93_4)
			-- function 93
			return arg_93_2, arg_93_3, arg_93_4 + slot_13_48.GetTotalAD(arg_93_0) * 0.2, false
		end,
		MissFortune = function(arg_94_0, arg_94_1, arg_94_2, arg_94_3, arg_94_4)
			-- function 94
			if not _aaMinion or _aaMinion and _aaMinion.ptr ~= arg_94_1.ptr and (arg_94_1.type == TYPE_HERO or arg_94_1.type == TYPE_MINION) then
				local slot_94_0 = arg_94_0.levelRef
				local slot_94_1 = (slot_94_0 <= 1 and 0.5 or slot_94_0 <= 4 and 0.6 or slot_94_0 <= 7 and 0.7 or slot_94_0 <= 9 and 0.8 or slot_94_0 <= 11 and 0.9 or 1) * slot_13_48.GetTotalAD(arg_94_0)

				if arg_94_1.type == TYPE_MINION then
					slot_94_1 = slot_94_1 / 2
				end

				arg_94_2 = arg_94_2 + slot_94_1
			end

			return arg_94_2, arg_94_3, arg_94_4, false
		end,
		Vayne = function(arg_95_0, arg_95_1, arg_95_2, arg_95_3, arg_95_4)
			-- function 95
			if arg_95_0:spellSlot(1).level < 1 then
				return arg_95_2, arg_95_3, arg_95_4, false
			end

			local slot_95_0 = 35 + 15 * arg_95_0:spellSlot(1).level

			arg_95_4 = arg_95_4 + (slot_13_48.getBuffStacks(arg_95_1, "vaynesilvereddebuff") > 1 and (0.025 * arg_95_0:spellSlot(1).level + 0.015) * arg_95_1.maxHealth or 0)

			local slot_95_1 = false

			if arg_95_4 > 0 and arg_95_4 < slot_95_0 then
				arg_95_4 = slot_95_0
			end

			return arg_95_2 + (slot_13_48.getBuffStacks(arg_95_0, "vaynetumblebonus") > 0 and (0.05 * arg_95_0:spellSlot(0).level + 0.55) * arg_95_2 or 0), arg_95_3, arg_95_4, slot_95_1
		end,
		Jhin = function(arg_96_0, arg_96_1, arg_96_2, arg_96_3, arg_96_4)
			-- function 96
			local slot_96_0 = arg_96_2 - arg_96_2 * 0.5
			local slot_96_1 = arg_96_0.levelRef
			local slot_96_2 = slot_96_0 + (arg_96_1.maxHealth - arg_96_1.health) * (slot_96_1 < 6 and 0.15 or slot_96_1 < 11 and 0.2 or 0.25)

			return slot_13_48.getBuffStacks(arg_96_0, "jhinpassiveattackbuff") > 0 and slot_96_2 + arg_96_2 or arg_96_2, arg_96_3, arg_96_4, false
		end,
		KogMaw = function(arg_97_0, arg_97_1, arg_97_2, arg_97_3, arg_97_4)
			-- function 97
			if slot_13_48.getBuffStacks(arg_97_0, "kogmawbioarcanebarrage") > 0 then
				local slot_97_0 = (2.25 + 0.75 * arg_97_0:spellSlot(1).level + slot_13_48.GetTotalAP(arg_97_0) / 100 / 1) / 100

				arg_97_3 = arg_97_3 + arg_97_1.maxHealth * slot_97_0
			end

			return arg_97_2, arg_97_3, arg_97_4, false
		end,
		Draven = function(arg_98_0, arg_98_1, arg_98_2, arg_98_3, arg_98_4)
			-- function 98
			return arg_98_2 + (slot_13_48.getBuffStacks(arg_98_0, "dravenspinning") > 0 and math.modf(35 + 5 * arg_98_0:spellSlot(0).level + slot_13_48.GetBonusAD(arg_98_0) * (0.65 + arg_98_0:spellSlot(0).level * 0.1)) or 0), arg_98_3, arg_98_4, false
		end,
		Darius = function(arg_99_0, arg_99_1, arg_99_2, arg_99_3, arg_99_4)
			-- function 99
			return arg_99_2 + (slot_13_48.getBuffStacks(arg_99_0, "dariusnoxiantacticsonh") > 0 and (0.35 + 0.05 * arg_99_0:spellSlot(1).level) * arg_99_2 or 0), arg_99_3, arg_99_4
		end,
		Kalista = function(arg_100_0, arg_100_1, arg_100_2, arg_100_3, arg_100_4)
			-- function 100
			return arg_100_2 * 0.9, arg_100_3, arg_100_4, false
		end,
		Senna = function(arg_101_0, arg_101_1, arg_101_2, arg_101_3, arg_101_4)
			-- function 101
			return arg_101_2 * 1.2, arg_101_3, arg_101_4, false
		end,
		Orianna = function(arg_102_0, arg_102_1, arg_102_2, arg_102_3, arg_102_4)
			-- function 102
			local slot_102_0 = 10 + 2.3529411764705883 * (player.levelRef - 1)
			local slot_102_1 = 14 + 3.2941176470588234 * (player.levelRef - 1)
			local slot_102_2 = slot_13_48.getBuffStacks(arg_102_1, "oriannapstack")
			local slot_102_3 = slot_102_0 + slot_13_48.GetTotalAP(arg_102_0) * 0.15

			arg_102_3 = arg_102_3 + slot_102_3 + (slot_102_2 > 0 and slot_102_3 * 0.2 * slot_102_2 or 0)

			return arg_102_2, math.modf(arg_102_3), arg_102_4, false
		end,
		Kayle = function(arg_103_0, arg_103_1, arg_103_2, arg_103_3, arg_103_4)
			-- function 103
			arg_103_3 = arg_103_0:spellSlot(2).level > 0 and 10 + 5 * arg_103_0:spellSlot(2).level + slot_13_48.GetTotalAP(arg_103_0) * 0.25 + slot_13_48.GetBonusAD(arg_103_0) * 0.1 or 0
			arg_103_3 = arg_103_0.levelRef >= 16 and arg_103_3 * 2 or arg_103_3

			return arg_103_2, math.modf(arg_103_3), arg_103_4, false
		end,
		Viktor = function(arg_104_0, arg_104_1, arg_104_2, arg_104_3, arg_104_4)
			-- function 104
			arg_104_3 = arg_104_3 + (slot_13_48.getBuffStacks(arg_104_0, "viktorqreturn") > 0 and -5 + 25 * arg_104_0:spellSlot(0).level + slot_13_48.GetTotalAP(arg_104_0) * 0.6 or 0)

			return arg_104_2, math.modf(arg_104_3), arg_104_4, false
		end,
		Caitlyn = function(arg_105_0, arg_105_1, arg_105_2, arg_105_3, arg_105_4, arg_105_5)
			-- function 105
			if arg_105_0.buff.caitlynpassivedriver then
				arg_105_2 = arg_105_2 + slot_13_48.GetTotalAD(arg_105_0) * ((arg_105_0.levelRef > 12 and 1.2 or arg_105_0.levelRef > 6 and 0.9 or 0.6) + 1.421875 * arg_105_0.crit)

				if arg_105_1.type == TYPE_MINION then
					arg_105_2 = arg_105_2 + slot_13_48.GetTotalAD(arg_105_0) * 0.5
				end
			end

			return arg_105_2, arg_105_3, arg_105_4, false
		end,
		Garen = function(arg_106_0, arg_106_1, arg_106_2, arg_106_3, arg_106_4)
			-- function 106
			return arg_106_2 + (slot_13_48.getBuffStacks(arg_106_0, "garenq") > 0 and 30 * arg_106_0:spellSlot(0).level + 0.5 * slot_13_48.GetTotalAD(arg_106_0) or 0), arg_106_3, arg_106_4
		end,
		Viego = function(arg_107_0, arg_107_1, arg_107_2, arg_107_3, arg_107_4, arg_107_5)
			-- function 107
			if arg_107_0:spellSlot(0).name == "ViegoQ" then
				local slot_107_0 = arg_107_0:spellSlot(0).level
				local slot_107_1 = {
					0.02,
					0.03,
					0.04,
					0.05,
					0.06
				}
				local slot_107_2 = {
					10,
					15,
					20,
					25,
					30
				}

				if slot_107_0 > 0 and arg_107_1.health then
					local slot_107_3 = arg_107_1.health * slot_107_1[slot_107_0]

					if slot_107_3 < slot_107_2[slot_107_0] then
						slot_107_3 = slot_107_2[slot_107_0]
					end

					arg_107_2 = arg_107_2 + slot_107_3
				end

				if slot_13_48.getBuffStacks(arg_107_0, "viegoqmark") > 0 then
					arg_107_2 = arg_107_2 + (slot_13_48.GetTotalAD(arg_107_0) * 0.2 + slot_13_48.GetTotalAP(arg_107_0) * 0.15)
				end
			end

			return arg_107_2, arg_107_3, arg_107_4, false
		end,
		Lucian = function(arg_108_0, arg_108_1, arg_108_2, arg_108_3, arg_108_4, arg_108_5)
			-- function 108
			if slot_13_48.getBuffStacks(arg_108_0, "lucianpassivebuff") > 0 then
				local slot_108_0 = player.levelRef
				local slot_108_1 = 0.5

				if slot_108_0 > 6 then
					slot_108_1 = 0.55
				end

				if slot_108_0 > 12 then
					slot_108_1 = 0.6
				end

				if arg_108_1.type == TYPE_MINION then
					slot_108_1 = 1
				end

				arg_108_2 = arg_108_2 + arg_108_2 * slot_108_1
			end

			return arg_108_2, arg_108_3, arg_108_4, false
		end,
		Kaisa = function(arg_109_0, arg_109_1, arg_109_2, arg_109_3, arg_109_4, arg_109_5)
			-- function 109
			local slot_109_0 = 5 + (player.levelRef - 1) * 1.0588235294118
			local slot_109_1 = 1 + (player.levelRef - 1) * 0.64705882352941
			local slot_109_2 = slot_13_48.getBuffStacks(arg_109_1, "kaisapassivemarker")

			if slot_109_2 > 0 and slot_109_2 >= 4 then
				arg_109_3 = arg_109_3 + (arg_109_1.maxHealth - arg_109_1.health) * (0.15 + slot_13_48.GetTotalAP(arg_109_0) / 100 * 0.06)
			end

			local slot_109_3 = slot_13_48.GetTotalAP(arg_109_0)
			local slot_109_4 = slot_109_0 + slot_109_3 * 0.15
			local slot_109_5 = 0

			if slot_109_2 > 0 then
				slot_109_5 = (slot_109_1 + slot_109_3 * 0.025) * slot_109_2
			end

			arg_109_3 = arg_109_3 + (slot_109_4 + slot_109_5)

			return arg_109_2, math.modf(arg_109_3), arg_109_4, false
		end,
		TwistedFate = function(arg_110_0, arg_110_1, arg_110_2, arg_110_3, arg_110_4, arg_110_5)
			-- function 110
			if arg_110_0.buff.cardmasterstackparticle then
				arg_110_3 = arg_110_3 + ((({
					65,
					90,
					115,
					140,
					165
				})[arg_110_0:spellSlot(2).level] or 0) + slot_13_48.GetBonusAD(arg_110_0) * 0.2 + slot_13_48.GetTotalAP(arg_110_0) * 0.4)
			end

			local slot_110_0 = arg_110_0:spellSlot(1).name

			if slot_110_0 ~= "PickACard" then
				local slot_110_1 = arg_110_0:spellSlot(1).level

				if slot_110_0 == "GoldCardLock" then
					arg_110_3 = arg_110_3 + ((({
						15,
						22.5,
						30,
						37.5,
						45
					})[slot_110_1] or 0) + slot_13_48.GetBonusAD(arg_110_0) * 1 + slot_13_48.GetTotalAP(arg_110_0) * 0.5) / 2
				elseif slot_110_0 == "BlueCardLock" then
					arg_110_3 = arg_110_3 + ((({
						40,
						60,
						80,
						100,
						120
					})[slot_110_1] or 0) + slot_13_48.GetBonusAD(arg_110_0) * 1 + slot_13_48.GetTotalAP(arg_110_0) * 1) / 2
				elseif slot_110_0 == "RedCardLock" then
					arg_110_3 = arg_110_3 + ((({
						30,
						45,
						60,
						75,
						90
					})[slot_110_1] or 0) + slot_13_48.GetBonusAD(arg_110_0) * 1 + slot_13_48.GetTotalAP(arg_110_0) * 0.7) / 2
				end
			end

			return arg_110_2, arg_110_3, arg_110_4, false
		end,
		Nilah = function(arg_111_0, arg_111_1, arg_111_2, arg_111_3, arg_111_4, arg_111_5)
			-- function 111
			return arg_111_2, arg_111_3, arg_111_4, false
		end,
		MonkeyKing = function(arg_112_0, arg_112_1, arg_112_2, arg_112_3, arg_112_4, arg_112_5)
			-- function 112
			if slot_13_48.getBuffStacks(arg_112_0, "monkeykingdoubleattack") > 0 then
				arg_112_2 = arg_112_2 + (-5 + 25 * arg_112_0:spellSlot(0).level + slot_13_48.GetBonusAD(arg_112_0) * 0.45)
			end

			return arg_112_2, arg_112_3, arg_112_4, false
		end,
		Akshan = function(arg_113_0, arg_113_1, arg_113_2, arg_113_3, arg_113_4, arg_113_5)
			-- function 113
			if slot_13_48.getBuffStacks(arg_113_1, "akshanpassivedebuff") >= 2 then
				local slot_113_0 = arg_113_0.levelRef > 18 and 18 or arg_113_0.levelRef

				arg_113_3 = arg_113_3 + ({
					10,
					15,
					20,
					25,
					30,
					35,
					40,
					45,
					55,
					65,
					75,
					85,
					95,
					105,
					120,
					135,
					150,
					165
				})[slot_113_0]
			end

			return arg_113_2, arg_113_3, arg_113_4, false
		end,
		Zed = function(arg_114_0, arg_114_1, arg_114_2, arg_114_3, arg_114_4, arg_114_5)
			-- function 114
			if slot_13_48.getBuffStacks(arg_114_1, "zedpassivecd") <= 0 and slot_13_48.GetHp(arg_114_1) < 50 then
				local slot_114_0 = 0.06

				if arg_114_0.levelRef >= 17 then
					slot_114_0 = 0.1
				elseif arg_114_0.levelRef >= 7 then
					slot_114_0 = 0.08
				end

				arg_114_3 = arg_114_3 + arg_114_1.maxHealth * slot_114_0
			end

			return arg_114_2, arg_114_3, arg_114_4, false
		end,
		Ekko = function(arg_115_0, arg_115_1, arg_115_2, arg_115_3, arg_115_4)
			-- function 115
			return arg_115_2, arg_115_3 + (slot_13_48.getBuffStacks(arg_115_0, "ekkoeattackbuff") > 0 and 30 * arg_115_0:spellSlot(2).level + 20 + slot_13_48.GetTotalAP(arg_115_0) * 0.4 or 0), arg_115_4
		end
	}

	local function slot_13_73(arg_116_0)
		-- function 116
		local slot_116_0 = arg_116_0.charName

		if slot_116_0:find("Siege") then
			return arg_116_0.maxHealth * 0.175
		end

		if slot_116_0:find("Cannon") then
			return arg_116_0.maxHealth * 0.14
		end

		if slot_116_0:find("Ranged") then
			return arg_116_0.maxHealth * 0.625
		end

		if slot_116_0 == "SRU_ChaosMinionMelee" then
			return arg_116_0.maxHealth * 0.425
		end

		return arg_116_0.maxHealth * 0.425
	end

	local slot_13_74 = module.internal("damagelib")

	function slot_13_48.CAA_FULL(arg_117_0, arg_117_1, arg_117_2)
		-- function 117
		if arg_117_1.type == TYPE_NEXUS then
			return 0
		end

		if arg_117_0.type == TYPE_TURRET and arg_117_1.type == TYPE_MINION then
			return slot_13_74.calc_aa_damage(arg_117_0, arg_117_1)
		end

		local slot_117_0 = slot_13_48.GetTotalAD(arg_117_0)
		local slot_117_1 = slot_13_48.GetTotalAD(arg_117_0)
		local slot_117_2 = slot_13_48.GetTotalAP(arg_117_0)
		local slot_117_3 = slot_13_48.GetTotalAP(arg_117_0)
		---local slot_117_4 = arg_117_2 or arg_117_0.crit > 0.875
		local slot_117_5 = {}
		local slot_117_6 = 0
		local slot_117_7 = 0
		local slot_117_8 = 0
		local slot_117_9 = 0
		local slot_117_10 = 0

		if arg_117_0.type == TYPE_HERO then
			slot_117_10 = arg_117_0.levelRef

			for iter_117_0 = 0, 6 do
				local slot_117_11 = arg_117_0:itemID(iter_117_0)

				if slot_117_11 > 0 then
					if arg_117_0.crit == 0 then
						slot_117_9 = slot_117_9 + slot_13_48.check_crit(slot_117_11)
					end

					if slot_117_9 > 100 then
						slot_117_9 = 100
					end

					slot_117_5[slot_117_11] = true

					if slot_117_11 == 1054 then
						slot_117_6 = 1
					elseif slot_117_11 == 3070 then
						slot_117_8 = 1
					elseif slot_117_11 == 1056 then
						slot_117_7 = 1
					end
				end
			end
		end

		local slot_117_12 = 0
		local slot_117_13 = 0
		local slot_117_14 = false
		local slot_117_15 = arg_117_0.isMelee

		if slot_117_4 then
			if arg_117_0.charName == "Yasuo" or arg_117_0.charName == "Yone" then
				if slot_117_5[3031] and slot_117_9 > 60 then
					slot_117_0 = slot_117_0 * 1.89
				else
					slot_117_0 = slot_117_0 * 1.575
				end
			elseif slot_117_5[3031] and slot_117_9 > 60 then
				if arg_117_1.type == TYPE_MINION then
					slot_117_0 = slot_117_0 * 2.1
				else
					slot_117_0 = slot_117_0 * 2.1
				end
			else
				slot_117_0 = slot_117_0 * 1.75
			end
		end

		local slot_117_16 = arg_117_0.charName

		if slot_13_72[slot_117_16] then
			slot_117_0, slot_117_12, slot_117_13, slot_117_14 = slot_13_72[slot_117_16](arg_117_0, arg_117_1, slot_117_0, slot_117_12, slot_117_13)
		end

		slot_117_14 = slot_117_14 or false
		ItemId_GuinsoosRageblade = 3124
		ItemId_Noonquiver = 6670
		ItemId_KrakenSlayer = 6672
		ItemId_Rageknife = 6677
		ItemId_NashorsTooth = 3115
		ItemId_WitsEnd = 1043
		ItemId_RecurveBow = 1043
		ItemId_BladeofTheRuinedKing = 3153

		if slot_13_48.aanumber and arg_117_0.ptr == player.ptr and not slot_117_5[ItemId_GuinsoosRageblade] then
			slot_13_48.aanumber = nil
		end

		local slot_117_17 = false

		if slot_117_5[ItemId_KrakenSlayer] and slot_13_48.getBuffStacks(arg_117_0, "6672buff") >= 2 then
			local slot_117_18 = arg_117_0.levelRef

			if slot_117_18 > 18 then
				slot_117_18 = 18
			end

			if arg_117_0.isMelee then
				local slot_117_19 = ({
					120,
					120,
					120,
					120,
					120,
					120,
					120,
					120,
					120,
					124,
					128,
					132,
					136,
					140,
					144,
					148,
					152,
					156,
					160
				})[slot_117_18]
				local slot_117_20 = 100 - slot_13_48.GetHp(arg_117_1)

				slot_117_0 = slot_117_0 + (slot_117_19 + slot_117_19 * (math.min(50, slot_117_20 * 5) / 100))
			else
				local slot_117_21 = ({
					150,
					150,
					150,
					150,
					150,
					150,
					150,
					150,
					150,
					155,
					160,
					165,
					170,
					175,
					180,
					185,
					190,
					195,
					200
				})[slot_117_18]
				local slot_117_22 = 100 - slot_13_48.GetHp(arg_117_1)

				slot_117_0 = slot_117_0 + (slot_117_21 + slot_117_21 * (math.min(50, slot_117_22 * 5) / 100))
			end
		end

		if arg_117_0.ptr == player.ptr then
			if slot_117_5[ItemId_GuinsoosRageblade] then
				slot_13_48.item3124 = true
			else
				slot_13_48.item3124 = false
			end

			if slot_117_5[ItemId_Rageknife] then
				slot_13_48.item6677 = true
			else
				slot_13_48.item6677 = false
			end
		end

		if slot_117_5[ItemId_Rageknife] and not slot_117_14 then
			slot_117_12 = slot_117_12 + 20
		end

		if slot_117_5[ItemId_GuinsoosRageblade] then
			if arg_117_0.ptr == player.ptr and not slot_13_48.aanumber then
				slot_13_48.aanumber = 1
			end

			if slot_13_48.aanumber and not slot_117_17 then
				if slot_13_48.aanumber % 3 == 0 and arg_117_0.ptr == player.ptr then
					slot_117_12 = slot_117_12 + (30 + slot_117_9 * 1.5) * 2
				else
					slot_117_12 = slot_117_12 + 30 + slot_117_9 * 1.5
				end
			else
				slot_117_12 = slot_117_12 + 30
			end
		end

		if slot_117_5[ItemId_NashorsTooth] and not slot_117_14 then
			slot_117_12 = slot_117_12 + (15 + slot_13_48.GetTotalAP(arg_117_0) * 0.15)
		end

		if slot_117_5[ItemId_RecurveBow] and not slot_117_14 then
			slot_117_0 = slot_117_0 + 15
		end

		if slot_117_5[3091] and not slot_117_14 then
			slot_117_12 = slot_117_12 + 45
		end

		if slot_117_5[ItemId_BladeofTheRuinedKing] and not slot_117_14 then
			if slot_117_15 then
				slot_117_0 = slot_117_0 + math.max(15, math.min(100, 0.1 * arg_117_1.health - 5))
			else
				slot_117_0 = slot_117_0 + math.max(15, math.min(100, 0.06 * arg_117_1.health - 5))
			end
		end

		ItemId_TearoftheGoddess = 3070
		ItemId_DoransShield = 1054
		ItemId_DoransRing = 1056

		if slot_117_5[ItemId_TearoftheGoddess] and not slot_117_14 then
			slot_117_0 = slot_117_0 + slot_117_8 * 5
		elseif slot_117_5[ItemId_DoransShield] and not slot_117_14 then
			slot_117_0 = slot_117_0 + slot_117_6 * 5
		elseif slot_117_5[ItemId_DoransRing] and not slot_117_14 then
			slot_117_0 = slot_117_0 + slot_117_7 * 5
		end

		ItemId_Sheen = 3057
		ItemId_DivineSunderer = 6632
		ItemId_TrinityForce = 3078
		ItemId_EssenceReaver = 3508
		ItemId_LichBane = 3100

		if not slot_117_14 and arg_117_0.type == TYPE_HERO and slot_117_16 ~= "Ezreal" and slot_13_48.getBuffStacks(arg_117_0, "3078trinityforce") > 0 or slot_13_48.getBuffStacks(arg_117_0, "sheen") > 0 or slot_13_48.getBuffStacks(arg_117_0, "3508buff") > 0 or slot_13_48.getBuffStacks(arg_117_0, "lichbane") > 0 or slot_13_48.getBuffStacks(arg_117_0, "6632buff") > 0 then
			if slot_117_5[ItemId_Sheen] then
				slot_117_0 = slot_117_0 + arg_117_0.baseAttackDamage
			elseif slot_117_5[3025] then
				slot_117_0 = slot_117_0 + arg_117_0.baseAttackDamage
			elseif slot_117_5[ItemId_DivineSunderer] then
				if arg_117_1.type == TYPE_MINION then
					slot_117_0 = slot_117_0 + arg_117_0.baseAttackDamage * 1.5
				elseif arg_117_1.type == TYPE_HERO then
					slot_117_0 = slot_117_0 + arg_117_0.baseAttackDamage * 1.6 + arg_117_1.maxHealth * 0.2
				end
			elseif slot_117_5[ItemId_TrinityForce] then
				slot_117_0 = slot_117_0 + arg_117_0.baseAttackDamage * 2
			elseif slot_117_5[ItemId_EssenceReaver] then
				slot_117_0 = slot_117_0 + arg_117_0.baseAttackDamage * 1.4 + slot_13_48.GetBonusAD(arg_117_0) * 0.2
			elseif slot_117_5[ItemId_LichBane] then
				slot_117_12 = slot_117_12 + arg_117_0.baseAttackDamage * 1.5 + slot_13_48.GetTotalAP(arg_117_0) * 0.4
			end
		end

		if slot_13_48.getBuffStacks(arg_117_0, "itemfrozenfist") > 0 and not slot_117_14 then
			if arg_117_0.levelRef > 18 then
				local slot_117_23 = 18
			end

			if slot_117_5[3057] then
				slot_117_0 = slot_117_0 + arg_117_0.baseAttackDamage
			elseif slot_117_5[3025] then
				slot_117_0 = slot_117_0 + arg_117_0.baseAttackDamage
			elseif slot_117_5[3078] then
				slot_117_0 = slot_117_0 + arg_117_0.baseAttackDamage * 2
			end
		end

		if slot_13_48.getBuffStacks(arg_117_0, "itemstatikshankcharge") >= 100 and not slot_117_14 then
			if slot_117_5[3094] then
				slot_117_12 = slot_117_12 + 60
			elseif slot_117_5[3087] then
				local slot_117_24 = slot_117_10 >= 11 and 140 + (slot_117_10 - 10) * 5 or slot_117_10 >= 7 and 100 + (slot_117_10 - 6) * 10 or 100

				if arg_117_1.type == TYPE_MINION and arg_117_1.team == TEAM_ENEMY then
					slot_117_24 = 150
				end

				slot_117_12 = slot_117_12 + slot_117_24 + slot_13_48.GetTotalAP(arg_117_0) * 0.15
			elseif slot_117_5[3095] then
				slot_117_12 = slot_117_12 + 25 + slot_117_1 * 0.65 + slot_117_3 * 0.5
			elseif slot_117_5[2015] then
				slot_117_12 = slot_117_12 + 60
			end
		end

		if arg_117_0.type == TYPE_HERO and arg_117_0:spellSlot(0) and arg_117_0:spellSlot(0).name == "JinxQ" and slot_13_48.getBuffStacks(arg_117_0, "jinxqicon") == 0 then
			slot_117_0 = slot_117_0 * 1.1
		elseif slot_117_16 == "Zeri" then
			if slot_13_48.getBuffStacks(arg_117_0, "zeriqpassiveready") > 0 then
				local slot_117_25 = 90 + 6.470588235294118 * (arg_117_0.levelRef - 1) * (0.7025 + 0.0175 * (arg_117_0.levelRef - 1)) + slot_117_2 * 1.1
				local slot_117_26 = 1 + 0.8235294117647058 * (arg_117_0.levelRef - 1) * (0.7025 + 0.0175 * (arg_117_0.levelRef - 1))
				local slot_117_27 = arg_117_1.maxHealth * (slot_117_26 / 100)

				if arg_117_1.type ~= TYPE_HERO and slot_117_27 > 300 then
					slot_117_27 = 300
				end

				local slot_117_28 = slot_117_25 + slot_117_27

				return slot_13_48.CAP(arg_117_1, slot_117_28, arg_117_0), 0, 0
			else
				local slot_117_29 = 60 + 5.294117647058823 * (arg_117_0.levelRef - 1) * (0.7025 + 0.0175 * (arg_117_0.levelRef - 1)) + slot_117_2 * 0.18

				if slot_117_29 >= arg_117_1.health then
					return slot_117_29
				end

				local slot_117_30 = 10 + 0.8823529411764706 * (arg_117_0.levelRef - 1) * (0.7025 + 0.0175 * (arg_117_0.levelRef - 1)) + slot_117_2 * 0.03

				return slot_13_48.CAP(arg_117_1, slot_117_30, arg_117_0), 0, 0
			end
		end

		if arg_117_1.type == TYPE_HERO and arg_117_0.type == TYPE_HERO and slot_13_58[arg_117_0.ptr] and slot_13_58[arg_117_0.ptr][8014] and slot_13_48.GetHp(arg_117_1) < 40 then
			slot_117_0 = slot_117_0 + slot_117_0 * 0.08
		end

		local slot_117_31 = slot_13_48.CAD(arg_117_1, slot_117_0, arg_117_0)
		local slot_117_32 = slot_13_48.CAP(arg_117_1, slot_117_12, arg_117_0)
		local slot_117_33 = slot_117_13

		if slot_117_5[6676] and arg_117_1.type == TYPE_HERO and (arg_117_1.health - (slot_117_31 + slot_117_32 + slot_117_33)) / arg_117_1.maxHealth * 100 <= 5 then
			slot_117_33 = 9999
		end

		return slot_117_31 + slot_117_32 + slot_117_33, slot_117_31, slot_117_32, slot_117_33
	end

	function slot_13_48.isq()
		-- function 118
		if player.charName == "Sylas" then
			return false
		end

		return player:spellSlot(0).state == 0
	end

	function slot_13_48.isw()
		-- function 119
		if player.charName == "Sylas" then
			return false
		end

		return player:spellSlot(1).state == 0
	end

	function slot_13_48.ise()
		-- function 120
		if player.charName == "Sylas" then
			return false
		end

		return player:spellSlot(2).state == 0
	end

	function slot_13_48.isr()
		-- function 121
		if player.charName == "Viego" and player:spellSlot(0).name ~= "ViegoQ" then
			return false
		end

		return player:spellSlot(3).state == 0
	end

	slot_13_48.buff_save = {}

	function slot_13_48.buff_ignore(arg_122_0)
		-- function 122
		table.insert(slot_13_48.buff_save, arg_122_0)
	end

	function slot_13_48.is_valid_buff(arg_123_0, arg_123_1)
		-- function 123
		arg_123_1 = arg_123_1 or player

		local slot_123_0 = arg_123_1.buff[arg_123_0]

		for iter_123_0 = 1, #slot_13_48.buff_save do
			local slot_123_1 = slot_13_48.buff_save[iter_123_0]

			if slot_123_1 and slot_123_1 + 0.25 < game.time then
				table.remove(slot_13_48.buff_save, iter_123_0)
			end

			if slot_123_1 and slot_123_0 and slot_123_0.endTime == slot_123_1 then
				return nil, slot_123_1
			end
		end

		return slot_123_0
	end

	function slot_13_48.GetDistanceSqr(arg_124_0, arg_124_1)
		-- function 124
		local slot_124_0 = arg_124_1 or player.pos
		local slot_124_1 = arg_124_0.x - slot_124_0.x
		local slot_124_2 = (arg_124_0.z or arg_124_0.y) - (slot_124_0.z or slot_124_0.y)

		return slot_124_1 * slot_124_1 + slot_124_2 * slot_124_2
	end

	function slot_13_48.GetDistance(arg_125_0, arg_125_1)
		-- function 125
		return math.sqrt(slot_13_48.GetDistanceSqr(arg_125_0, arg_125_1))
	end

	function slot_13_48.ista(arg_126_0, arg_126_1)
		-- function 126
		local slot_126_0 = arg_126_1 or 0
		local slot_126_1 = math.huge

		for iter_126_0 = 0, objManager.turrets.size[TEAM_ENEMY] - 1 do
			local slot_126_2 = objManager.turrets[TEAM_ENEMY][iter_126_0]

			if slot_126_2 and not slot_126_2.isDead then
				local slot_126_3 = arg_126_0:dist(slot_126_2.pos)

				if slot_126_3 <= 920 + slot_126_0 then
					return true
				end

				slot_126_1 = math.min(slot_126_1, slot_126_3)
			end
		end

		return false
	end

	function slot_13_48.ista_ally(arg_127_0, arg_127_1)
		-- function 127
		local slot_127_0 = arg_127_1 or 0
		local slot_127_1 = math.huge

		for iter_127_0 = 0, objManager.turrets.size[TEAM_ALLY] - 1 do
			local slot_127_2 = objManager.turrets[TEAM_ALLY][iter_127_0]

			if slot_127_2 and slot_127_2.isTargetable and not slot_127_2.isDead then
				local slot_127_3 = arg_127_0:dist(slot_127_2.pos)

				if slot_127_3 <= 900 + slot_127_0 then
					return true
				end

				slot_127_1 = math.min(slot_127_1, slot_127_3)
			end
		end

		return false
	end

	function slot_13_48.GetAllyTurrets(arg_128_0)
		-- function 128
		local slot_128_0 = {}
		local slot_128_1 = arg_128_0 or player.pos

		for iter_128_0 = 0, objManager.turrets.size[TEAM_ALLY] - 1 do
			local slot_128_2 = objManager.turrets[TEAM_ALLY][iter_128_0]

			if slot_128_2 and slot_128_2.isTargetable and not slot_128_2.isDead then
				table.insert(slot_128_0, slot_128_2)
			end
		end

		table.sort(slot_128_0, function(arg_129_0, arg_129_1)
			-- function 129
			return slot_128_1:dist(arg_129_0.pos) < slot_128_1:dist(arg_129_1.pos)
		end)

		if slot_128_0[1] then
			return slot_128_0[1], slot_128_0
		end
	end

	function slot_13_48.GetEnemyTurrets()
		-- function 130
		local slot_130_0 = {}

		for iter_130_0 = 0, objManager.turrets.size[TEAM_ENEMY] - 1 do
			local slot_130_1 = objManager.turrets[TEAM_ENEMY][iter_130_0]

			if slot_130_1 and slot_130_1.isTargetable and not slot_130_1.isDead then
				table.insert(slot_130_0, slot_130_1)
			end
		end

		table.sort(slot_130_0, function(arg_131_0, arg_131_1)
			-- function 131
			return player.pos:dist(arg_131_0.pos) < player.pos:dist(arg_131_1.pos)
		end)

		if slot_130_0[1] then
			return slot_130_0[1], slot_130_0
		end
	end

	function slot_13_48.count_minions_in_range(arg_132_0, arg_132_1)
		-- function 132
		local slot_132_0 = {}

		for iter_132_0 = 0, objManager.minions.size[TEAM_NEUTRAL] - 1 do
			local slot_132_1 = objManager.minions[TEAM_NEUTRAL][iter_132_0]

			if slot_132_1 and slot_13_48.isTarget(slot_132_1) and slot_13_48.CheckMinios(slot_132_1.name) and arg_132_1 > arg_132_0:dist(slot_132_1.pos) then
				slot_132_0[#slot_132_0 + 1] = enemy
			end
		end

		return slot_132_0
	end

	local slot_13_75 = {}

	for iter_13_1 = 0, objManager.enemies_n - 1 do
		local slot_13_76 = objManager.enemies[iter_13_1]

		table.insert(slot_13_75, slot_13_76)
	end

	local slot_13_77 = {}

	for iter_13_2 = 0, objManager.allies_n - 1 do
		local slot_13_78 = objManager.allies[iter_13_2]

		table.insert(slot_13_77, slot_13_78)
	end

	function slot_13_48.GetEnemyHeroes()
		-- function 133
		return slot_13_75
	end

	function slot_13_48.GetAllyHeroes()
		-- function 134
		return slot_13_77
	end

	function slot_13_48.GetAlliedMinions()
		-- function 135
		local slot_135_0 = {}

		for iter_135_0 = 0, objManager.minions.size[TEAM_ALLY] - 1 do
			local slot_135_1 = objManager.minions[TEAM_ALLY][iter_135_0]

			if slot_13_48.isTarget(slot_135_1) then
				table.insert(slot_135_0, slot_135_1)
			end
		end

		return slot_135_0
	end

	function slot_13_48.GetEnemyMiniones()
		-- function 136
		local slot_136_0 = {}

		for iter_136_0 = 0, objManager.minions.size[TEAM_ENEMY] - 1 do
			local slot_136_1 = objManager.minions[TEAM_ENEMY][iter_136_0]

			if slot_13_48.isTarget(slot_136_1) then
				table.insert(slot_136_0, slot_136_1)
			end
		end

		return slot_136_0
	end

	function slot_13_48.GetNeutralMiniones()
		-- function 137
		local slot_137_0 = {}

		for iter_137_0 = 0, objManager.minions.size[TEAM_NEUTRAL] - 1 do
			local slot_137_1 = objManager.minions[TEAM_NEUTRAL][iter_137_0]

			if slot_13_48.isTarget(slot_137_1) then
				table.insert(slot_137_0, slot_137_1)
			end
		end

		return slot_137_0
	end

	function slot_13_48.GetClosestEnemy(arg_138_0)
		-- function 138
		arg_138_0 = arg_138_0 or player.pos

		return slot_13_48.IsClosest_pos(arg_138_0)
	end

	function slot_13_48.IsClosest_pos(arg_139_0)
		-- function 139
		local slot_139_0
		local slot_139_1 = 9999

		for iter_139_0, iter_139_1 in ipairs(slot_13_48.GetEnemyHeroes()) do
			if iter_139_1 and slot_13_48.isTarget(iter_139_1) then
				local slot_139_2 = arg_139_0:dist(iter_139_1.pos)

				if slot_139_2 < slot_139_1 then
					slot_139_0 = iter_139_1
					slot_139_1 = slot_139_2
				end
			end
		end

		return slot_139_0, slot_139_1
	end

	function slot_13_48.IsClosest_Dist(arg_140_0)
		-- function 140
		local slot_140_0
		local slot_140_1 = 9999

		for iter_140_0, iter_140_1 in ipairs(slot_13_48.GetEnemyHeroes()) do
			if iter_140_1 and slot_13_48.isTarget(iter_140_1) then
				local slot_140_2 = arg_140_0:dist(iter_140_1.pos)

				if slot_140_2 < slot_140_1 then
					slot_140_0 = iter_140_1
					slot_140_1 = slot_140_2
				end
			end
		end

		return slot_140_1, slot_140_0
	end

	slot_13_48.CAA = slot_13_48.CalculateAADamage
	slot_13_48.CAD = slot_13_48.CalculatePhysicalDamage
	slot_13_48.CAP = slot_13_48.CalculateMagicDamage

	function slot_13_48.InSkillShot(arg_141_0, arg_141_1, arg_141_2, arg_141_3)
		-- function 141
		local slot_141_0 = arg_141_2
		local slot_141_1, slot_141_2, slot_141_3 = slot_13_48.geometry.ProjectOn(slot_141_0, arg_141_0, arg_141_1)

		return slot_141_1 and arg_141_3 >= slot_141_2:dist(to2D(slot_141_0))
	end

	function slot_13_48.farm_line(arg_142_0, arg_142_1, arg_142_2)
		-- function 142
		arg_142_2 = arg_142_2 or player.pos

		local slot_142_0 = objManager.minions
		local slot_142_1 = false
		local slot_142_2 = {}
		local slot_142_3 = false

		for iter_142_0 = 0, slot_142_0.size[TEAM_ENEMY] - 1 do
			local slot_142_4 = slot_142_0[TEAM_ENEMY][iter_142_0]

			if slot_142_4 and slot_13_48.isTarget(slot_142_4, arg_142_0) then
				local slot_142_5 = player.pos:dist(slot_142_4.pos)

				table.insert(slot_142_2, slot_142_4)

				if slot_142_5 < player.attackRange + player.boundingRadius + slot_142_4.boundingRadius then
					slot_142_1 = true
				end
			end
		end

		for iter_142_1 = 0, slot_142_0.size[TEAM_NEUTRAL] - 1 do
			local slot_142_6 = slot_142_0[TEAM_NEUTRAL][iter_142_1]

			if slot_142_6 and slot_13_48.isTarget(slot_142_6, arg_142_0) then
				local slot_142_7 = player.pos:dist(slot_142_6.pos)

				table.insert(slot_142_2, slot_142_6)

				if slot_142_7 < player.attackRange + player.boundingRadius + slot_142_6.boundingRadius then
					slot_142_1 = true
				end

				slot_142_3 = true
			end
		end

		local slot_142_8
		local slot_142_9 = 0

		for iter_142_2, iter_142_3 in ipairs(slot_142_2) do
			local slot_142_10 = iter_142_3.pos
			local slot_142_11 = 1

			for iter_142_4, iter_142_5 in ipairs(slot_142_2) do
				if iter_142_3.ptr ~= iter_142_5.ptr and slot_13_48.InSkillShot(arg_142_2, slot_142_10, iter_142_5.pos, arg_142_1 + iter_142_5.boundingRadius) then
					slot_142_11 = slot_142_11 + 1
				end
			end

			if slot_142_9 < slot_142_11 then
				slot_142_9 = slot_142_11
				slot_142_8 = slot_142_10
			end
		end

		if slot_142_9 and slot_142_9 == 1 and slot_142_8 and slot_13_48.URF then
			slot_142_9 = 99
		end

		return slot_142_8, slot_142_9, slot_142_1, slot_142_3, slot_142_0
	end

	function slot_13_48.farm_circular(arg_143_0, arg_143_1, arg_143_2)
		-- function 143
		arg_143_2 = arg_143_2 or player.pos

		local slot_143_0 = false
		local slot_143_1 = {}
		local slot_143_2 = false

		for iter_143_0 = 0, objManager.minions.size[TEAM_ENEMY] - 1 do
			local slot_143_3 = objManager.minions[TEAM_ENEMY][iter_143_0]

			if slot_143_3 and slot_13_48.isTarget(slot_143_3, arg_143_0) then
				local slot_143_4 = player.pos:dist(slot_143_3.pos)
				local slot_143_5 = slot_13_48.GetAARange(slot_143_3)

				table.insert(slot_143_1, slot_143_3)

				if slot_143_4 < slot_143_5 then
					slot_143_0 = true
				end
			end
		end

		for iter_143_1 = 0, objManager.minions.size[TEAM_NEUTRAL] - 1 do
			local slot_143_6 = objManager.minions[TEAM_NEUTRAL][iter_143_1]

			if slot_143_6 and slot_13_48.isTarget(slot_143_6, arg_143_0) then
				local slot_143_7 = player.pos:dist(slot_143_6.pos)
				local slot_143_8 = slot_13_48.GetAARange(slot_143_6)

				table.insert(slot_143_1, slot_143_6)

				if slot_143_7 < slot_143_8 then
					slot_143_0 = true
				end

				slot_143_2 = true
			end
		end

		local slot_143_9
		local slot_143_10 = 0

		for iter_143_2, iter_143_3 in ipairs(slot_143_1) do
			local slot_143_11 = iter_143_3.pos
			local slot_143_12 = #slot_13_48.GetMinionInRange(arg_143_1, iter_143_3.pos)

			if slot_143_10 < slot_143_12 then
				slot_143_10 = slot_143_12
				slot_143_9 = slot_143_11
			end
		end

		if slot_143_10 and slot_143_10 == 1 and slot_143_9 and slot_13_48.URF then
			slot_143_10 = 99
		end

		return slot_143_9, slot_143_10, slot_143_0, slot_143_2, minions
	end

	function slot_13_48.farm_circular2(arg_144_0, arg_144_1, arg_144_2)
		-- function 144
		arg_144_2 = arg_144_2 or player.pos

		local slot_144_0 = false
		local slot_144_1 = {}
		local slot_144_2 = false

		for iter_144_0 = 0, objManager.minions.size[TEAM_ENEMY] - 1 do
			local slot_144_3 = objManager.minions[TEAM_ENEMY][iter_144_0]

			if slot_144_3 and slot_13_48.isTarget(slot_144_3, arg_144_0) then
				local slot_144_4 = player.pos:dist(slot_144_3.pos)
				local slot_144_5 = slot_13_48.GetAARange(slot_144_3)

				table.insert(slot_144_1, slot_144_3)

				if slot_144_4 < slot_144_5 then
					slot_144_0 = true
				end
			end
		end

		for iter_144_1 = 0, objManager.minions.size[TEAM_NEUTRAL] - 1 do
			local slot_144_6 = objManager.minions[TEAM_NEUTRAL][iter_144_1]

			if slot_144_6 and slot_13_48.isTarget(slot_144_6, arg_144_0) then
				local slot_144_7 = player.pos:dist(slot_144_6.pos)
				local slot_144_8 = slot_13_48.GetAARange(slot_144_6)

				table.insert(slot_144_1, slot_144_6)

				if slot_144_7 < slot_144_8 then
					slot_144_0 = true
				end

				slot_144_2 = true
			end
		end

		local slot_144_9
		local slot_144_10 = 0

		for iter_144_2, iter_144_3 in ipairs(slot_144_1) do
			local slot_144_11 = slot_13_48.generate_points2(iter_144_3.pos, arg_144_1)

			for iter_144_4, iter_144_5 in ipairs(slot_144_11) do
				local slot_144_12 = iter_144_5
				local slot_144_13 = #slot_13_48.GetMinionInRange(arg_144_1, iter_144_5)

				if slot_144_10 < slot_144_13 then
					slot_144_10 = slot_144_13
					slot_144_9 = slot_144_12
				end
			end
		end

		return slot_144_9, slot_144_10, slot_144_0, slot_144_2, minions
	end

	function slot_13_48.farm_utilty(arg_145_0, arg_145_1, arg_145_2)
		-- function 145
		arg_145_2 = arg_145_2 or player.pos

		local slot_145_0 = {}
		local slot_145_1 = false

		for iter_145_0, iter_145_1 in ipairs(slot_13_48.GetEnemyMiniones()) do
			if iter_145_1 and slot_13_48.isTarget(iter_145_1, arg_145_1) then
				table.insert(slot_145_0, iter_145_1)
			end
		end

		for iter_145_2, iter_145_3 in ipairs(slot_13_48.GetNeutralMiniones()) do
			if iter_145_3 and slot_13_48.isTarget(iter_145_3, arg_145_1) then
				table.insert(slot_145_0, iter_145_3)

				slot_145_1 = true
			end
		end

		local slot_145_2
		local slot_145_3 = 0
		local slot_145_4 = {}

		for iter_145_4 = 1, #slot_145_0 do
			slot_145_4[#slot_145_4 + 1] = slot_145_0[iter_145_4].pos
		end

		for iter_145_5 = 1, #slot_145_0 do
			for iter_145_6 = 1, #slot_145_0 do
				if slot_145_0[iter_145_6].ptr ~= slot_145_0[iter_145_5].ptr then
					local slot_145_5 = vec3(slot_145_0[iter_145_6].pos) + vec3(slot_145_0[iter_145_5].pos)

					table.insert(slot_145_4, vec3(slot_145_5.x / 2, slot_145_5.y / 2, slot_145_5.z / 2))
				end
			end
		end

		for iter_145_7, iter_145_8 in ipairs(slot_145_4) do
			if arg_145_1 >= arg_145_2:dist(iter_145_8) then
				local slot_145_6 = arg_145_2 + (iter_145_8 - arg_145_2):norm() * arg_145_1
				local slot_145_7 = 0

				for iter_145_9, iter_145_10 in ipairs(slot_145_0) do
					local slot_145_8, slot_145_9, slot_145_10 = slot_13_48.VectorPointProjectionOnLineSegment(arg_145_2, slot_145_6, iter_145_10.pos)

					if slot_145_10 and slot_145_8 and vec3(slot_145_8.x, 0, slot_145_8.y):dist(iter_145_10.pos) <= arg_145_0 + iter_145_10.boundingRadius / 2 then
						slot_145_7 = slot_145_7 + 1
					end
				end

				if slot_145_3 <= slot_145_7 then
					slot_145_3 = slot_145_7
					slot_145_2 = slot_145_6

					if slot_145_3 == #slot_145_0 then
						break
					end
				end
			end
		end

		return slot_145_2, slot_145_3, slot_145_1, slot_145_0
	end

	function slot_13_48.VectorPointProjectionOnLineSegment(arg_146_0, arg_146_1, arg_146_2)
		-- function 146
		if not arg_146_0 or not arg_146_1 or not arg_146_2 then
			return
		end

		assert(arg_146_0 and arg_146_1 and arg_146_2, "VectorPointProjectionOnLineSegment: wrong argument types (3 <Vector> expected)")

		local slot_146_0 = arg_146_2.x
		local slot_146_1 = arg_146_2.z and arg_146_2.z or arg_146_2.y
		local slot_146_2 = arg_146_0.x
		local slot_146_3 = arg_146_0.z and arg_146_0.z or arg_146_0.y
		local slot_146_4 = arg_146_1.x
		local slot_146_5 = arg_146_1.z and arg_146_1.z or arg_146_1.y
		local slot_146_6 = ((slot_146_0 - slot_146_2) * (slot_146_4 - slot_146_2) + (slot_146_1 - slot_146_3) * (slot_146_5 - slot_146_3)) / ((slot_146_4 - slot_146_2)^2 + (slot_146_5 - slot_146_3)^2)
		local slot_146_7 = {
			x = slot_146_2 + slot_146_6 * (slot_146_4 - slot_146_2),
			y = slot_146_3 + slot_146_6 * (slot_146_5 - slot_146_3)
		}
		local slot_146_8 = slot_146_6 < 0 and 0 or slot_146_6 > 1 and 1 or slot_146_6
		local slot_146_9 = slot_146_8 == slot_146_6

		return slot_146_9 and slot_146_7 or {
			x = slot_146_2 + slot_146_8 * (slot_146_4 - slot_146_2),
			y = slot_146_3 + slot_146_8 * (slot_146_5 - slot_146_3)
		}, slot_146_7, slot_146_9
	end

	function slot_13_48.VectorPointProjectionOnLineSegment3(arg_147_0, arg_147_1, arg_147_2)
		-- function 147
		if not arg_147_0 or not arg_147_1 or not arg_147_2 then
			return
		end

		assert(arg_147_0 and arg_147_1 and arg_147_2, "VectorPointProjectionOnLineSegment: wrong argument types (3 <Vector> expected)")

		local slot_147_0 = arg_147_2.x
		local slot_147_1 = arg_147_2.z and arg_147_2.z or arg_147_2.y
		local slot_147_2 = arg_147_0.x
		local slot_147_3 = arg_147_0.z and arg_147_0.z or arg_147_0.y
		local slot_147_4 = arg_147_1.x
		local slot_147_5 = arg_147_1.z and arg_147_1.z or arg_147_1.y
		local slot_147_6 = ((slot_147_0 - slot_147_2) * (slot_147_4 - slot_147_2) + (slot_147_1 - slot_147_3) * (slot_147_5 - slot_147_3)) / ((slot_147_4 - slot_147_2)^2 + (slot_147_5 - slot_147_3)^2)
		local slot_147_7 = {
			x = slot_147_2 + slot_147_6 * (slot_147_4 - slot_147_2),
			y = slot_147_3 + slot_147_6 * (slot_147_5 - slot_147_3)
		}
		local slot_147_8 = slot_147_6 < 0 and 0 or slot_147_6 > 1 and 1 or slot_147_6
		local slot_147_9 = slot_147_8 == slot_147_6
		local slot_147_10 = slot_147_9 and slot_147_7 or {
			y = 0,
			x = slot_147_2 + slot_147_8 * (slot_147_4 - slot_147_2),
			z = slot_147_3 + slot_147_8 * (slot_147_5 - slot_147_3)
		}

		slot_147_10 = slot_147_10 and vec3(slot_147_10.x, arg_147_2.y, slot_147_10.y)

		return slot_147_10, slot_147_7, slot_147_9
	end

	function slot_13_48.check_safe(arg_148_0, arg_148_1, arg_148_2)
		-- function 148
		local slot_148_0 = arg_148_0 or player.pos
		local slot_148_1 = to3D(slot_148_0)

		for iter_148_0, iter_148_1 in ipairs(slot_13_48.GetEnemyHeroes()) do
			if slot_13_48.isTarget(iter_148_1) then
				if iter_148_1.buff.warwicke and iter_148_1.pos:dist(slot_148_1) <= 400 then
					return false
				elseif iter_148_1.buff.jaxcounterstrike and iter_148_1.pos:dist(slot_148_1) <= 300 then
					return false
				elseif iter_148_1.buff.galiow and iter_148_1.pos:dist(slot_148_1) <= 500 then
					return false
				elseif iter_148_1.buff.garen and iter_148_1.pos:dist(slot_148_1) <= 350 then
					return false
				end
			end
		end

		if arg_148_1 and evade then
			return evade.core.is_action_safe(slot_148_1, arg_148_1, arg_148_2)
		end

		return true
	end

	function slot_13_48.CanHarras()
		-- function 149
		if not slot_13_48.ista(player.pos) and not slot_13_48.isnotmove(player) then
			return true
		else
			return false
		end
	end

	slot_13_48.sr = slot_13_48.CanHarras

	function slot_13_48.ismdm(arg_150_0)
		-- function 150
		return player.path.serverPos:distSqr(arg_150_0.path.serverPos) > player.path.serverPos:distSqr(arg_150_0.path.serverPos + arg_150_0.direction)
	end

	function slot_13_48.check_aa(arg_151_0, arg_151_1)
		-- function 151
		arg_151_0 = arg_151_0 or 0.25
		arg_151_1 = arg_151_1 or orb.combat.target

		if orb and not orb.core.iscloseaa() and arg_151_0 > orb.core.time_to_next_attack() and arg_151_1 then
			return false
		end

		return orb.core.can_action()
	end

	function slot_13_48.check_farmaa(arg_152_0, arg_152_1)
		-- function 152
		arg_152_0 = arg_152_0 or 0.25
		arg_152_1 = arg_152_1 or orb.farm.clear_target

		if orb and not orb.core.iscloseaa() and arg_152_0 > orb.core.time_to_next_attack() and arg_152_1 and slot_13_48.InAARange(arg_152_1, player) then
			return false
		end

		return orb.core.can_action()
	end

	function slot_13_48.check_aah1(arg_153_0)
		-- function 153
		arg_153_0 = arg_153_0 or 0.1

		local slot_153_0 = orb

		if slot_153_0 and slot_153_0.core._missileLaunched1 then
			return slot_153_0.core.attack_time + arg_153_0 + network.latency > os.clock()
		end

		return false
	end

	function slot_13_48.check_aah(arg_154_0)
		-- function 154
		arg_154_0 = arg_154_0 or 0.1

		local slot_154_0 = orb

		if slot_154_0 and slot_154_0.core.can_action() and slot_154_0.core._missileLaunched1 then
			return slot_154_0.core.attack_time + slot_154_0.get_windupTime() + arg_154_0 + network.latency + 0.05 > os.clock()
		end

		return false
	end

	local slot_13_79 = 0
	local slot_13_80 = {}

	function slot_13_48.pred_damage(arg_155_0, arg_155_1, arg_155_2)
		-- function 155
		if evade then
			arg_155_1 = arg_155_1 + network.latency

			if slot_13_79 > game.time then
				return 0, 0, 0, 0
			end

			local slot_155_0, slot_155_1, slot_155_2, slot_155_3 = evade.GetDamage(arg_155_0, arg_155_1, arg_155_2)

			if slot_155_0 then
				if slot_155_0 > 0 then
					slot_13_79 = game.time + 0.05
				end

				return slot_155_0, slot_155_1, slot_155_2, slot_155_3
			end
		end

		return 0, 0, 0, 0
	end

	function slot_13_48.IsPositionSafe(arg_156_0, arg_156_1, arg_156_2)
		-- function 156
		if evade then
			return evade.IsPositionSafe(arg_156_0, arg_156_1, arg_156_2)
		end

		return nil
	end

	function slot_13_48.GetEvade()
		-- function 157
		return Evade or evade
	end

	local slot_13_81 = 0
	local slot_13_82 = {}

	function slot_13_48.GetEvadeSpell(arg_158_0, arg_158_1, arg_158_2, arg_158_3)
		-- function 158
		if evade then
			if slot_13_81 > game.time then
				return
			end

			local slot_158_0 = evade.GetEvadeSpell(arg_158_0, arg_158_1, arg_158_2, arg_158_3)

			if slot_158_0 then
				slot_13_81 = game.time + 0.05
			end

			return slot_158_0
		end
	end

	function slot_13_48.SetEvadePause(arg_159_0)
		-- function 159
		if evade then
			if not arg_159_0 then
				return evade.core.set_server_pause(arg_159_0)
			else
				return evade.core.set_core_pause(arg_159_0)
			end
		end
	end

	function slot_13_48.GetAngle(arg_160_0, arg_160_1)
		-- function 160
		if arg_160_0.z then
			arg_160_0 = to2D(arg_160_0)
		end

		if arg_160_1.z then
			arg_160_1 = to2D(arg_160_1)
		end

		local slot_160_0 = (arg_160_0 - player.pos2D):norm()
		local slot_160_1 = (arg_160_1 - player.pos2D):norm()

		return (slot_13_48.geometry.angleBetween(slot_160_0, slot_160_1))
	end

	function slot_13_48.IsFacing(arg_161_0, arg_161_1)
		-- function 161
		local slot_161_0 = 90

		local function slot_161_1(arg_162_0, arg_162_1)
			-- function 162
			local slot_162_0 = arg_162_0.pos - arg_162_0.direction:norm() * arg_162_1

			return to2D(slot_162_0)
		end

		local slot_161_2 = (to2D(arg_161_1.pos) - to2D(arg_161_0.pos)):norm()
		local slot_161_3 = (slot_161_1(arg_161_1, 100) - to2D(arg_161_0.pos)):norm()

		return slot_13_48.geometry.angleBetween(slot_161_2, slot_161_3) < 120
	end

	function slot_13_48.CirclePoints(arg_163_0, arg_163_1, arg_163_2, arg_163_3)
		-- function 163
		local slot_163_0 = {}

		for iter_163_0 = 0, arg_163_0 do
			local slot_163_1 = iter_163_0 * 2 * math.pi / arg_163_0
			local slot_163_2 = vec3(arg_163_2.x + arg_163_1 * math.cos(slot_163_1), arg_163_2.y, arg_163_2.z + arg_163_1 * math.sin(slot_163_1))

			if not arg_163_3 or arg_163_3(slot_163_2) then
				table.insert(slot_163_0, slot_163_2)
			end
		end

		return slot_163_0
	end

	function slot_13_48.CirclePointsTo(arg_164_0, arg_164_1, arg_164_2, arg_164_3)
		-- function 164
		local slot_164_0 = {}

		arg_164_3 = arg_164_3 or 50

		for iter_164_0 = 0, arg_164_0 do
			local slot_164_1 = iter_164_0 * 2 * math.pi / arg_164_0

			for iter_164_1 = 0, arg_164_1, arg_164_3 do
				local slot_164_2 = vec3(arg_164_2.x + iter_164_1 * math.cos(slot_164_1), arg_164_2.y, arg_164_2.z + iter_164_1 * math.sin(slot_164_1))

				table.insert(slot_164_0, slot_164_2)
			end
		end

		return slot_164_0
	end

	function slot_13_48.WardName(arg_165_0)
		-- function 165
		local slot_165_0 = {
			"unused",
			"ivern",
			"ward",
			"trink",
			"trap",
			"spear",
			"device",
			"room",
			"box",
			"plant",
			"poo",
			"barrel",
			"god",
			"feather"
		}

		for iter_165_0 = 1, #slot_165_0 do
			if arg_165_0 and arg_165_0:lower():find(slot_165_0[iter_165_0]) then
				return true
			end
		end

		return false
	end

	function slot_13_48.CheckMinios(arg_166_0)
		-- function 166
		return not slot_13_48.WardName(arg_166_0)
	end

	local function slot_13_83(arg_167_0, arg_167_1, arg_167_2)
		-- function 167
		arg_167_0 = to2D(arg_167_0)
		arg_167_1 = to2D(arg_167_1)
		arg_167_2 = to2D(arg_167_2)

		local slot_167_0 = ((arg_167_2.x - arg_167_0.x) * (arg_167_1.x - arg_167_0.x) + (arg_167_2.y - arg_167_0.y) * (arg_167_1.y - arg_167_0.y)) / ((arg_167_1.x - arg_167_0.x)^2 + (arg_167_1.y - arg_167_0.y)^2)

		if slot_167_0 >= 0 and slot_167_0 <= 1 then
			return vec3(arg_167_0.x + slot_167_0 * (arg_167_1.x - arg_167_0.x), game.mousePos.y, arg_167_0.y + slot_167_0 * (arg_167_1.y - arg_167_0.y))
		end

		return nil
	end

	local function slot_13_84(arg_168_0)
		-- function 168
		local slot_168_0 = arg_168_0
		local slot_168_1 = arg_168_0

		return slot_168_0.x * slot_168_1.x + (slot_168_0.y and slot_168_0.y * slot_168_1.y or 0) + (slot_168_0.z and slot_168_0.z * slot_168_1.z or 0)
	end

	local function slot_13_85(arg_169_0, arg_169_1, arg_169_2)
		-- function 169
		local slot_169_0 = arg_169_1 - arg_169_0

		return slot_169_0 * (-(arg_169_0.x * slot_169_0.x - slot_169_0.x * arg_169_2.x + (arg_169_0.z - arg_169_2.z) * slot_169_0.z) / slot_13_84(slot_169_0)) + arg_169_0
	end

	slot_13_48.VectorPointProjectionOnLine = slot_13_85
	slot_13_48.point_on_line_seg = slot_13_83

	function slot_13_48.farm_1(arg_170_0, arg_170_1, arg_170_2)
		-- function 170
		local slot_170_0 = false
		local slot_170_1 = false
		local slot_170_2 = {}

		for iter_170_0, iter_170_1 in ipairs(slot_13_48.GetEnemyMiniones()) do
			if iter_170_1 and slot_13_48.isTarget(iter_170_1, arg_170_0) and slot_13_48.CheckMinios(iter_170_1.name) then
				if arg_170_2 then
					if arg_170_2(iter_170_1) then
						local slot_170_3 = player.pos:dist(iter_170_1.pos)

						if slot_170_3 < arg_170_0 then
							table.insert(slot_170_2, iter_170_1)
						end

						if slot_170_3 <= slot_13_48.GetAARange(iter_170_1) then
							slot_170_0 = true
						end
					end
				else
					local slot_170_4 = player.pos:dist(iter_170_1.pos)

					if slot_170_4 < arg_170_0 then
						table.insert(slot_170_2, iter_170_1)
					end

					if slot_170_4 <= slot_13_48.GetAARange(iter_170_1) then
						slot_170_0 = true
					end
				end
			end
		end

		if #slot_170_2 == 0 then
			for iter_170_2, iter_170_3 in ipairs(slot_13_48.GetNeutralMiniones()) do
				if iter_170_3 and slot_13_48.isTarget(iter_170_3, arg_170_0) and slot_13_48.CheckMinios(iter_170_3.name) then
					if arg_170_2 then
						if arg_170_2(iter_170_3) then
							local slot_170_5 = player.pos:dist(iter_170_3.pos)

							if slot_170_5 < arg_170_0 then
								table.insert(slot_170_2, iter_170_3)
							end

							if slot_170_5 <= slot_13_48.GetAARange(iter_170_3) then
								slot_170_0 = true
							end

							slot_170_1 = true
						end
					else
						local slot_170_6 = player.pos:dist(iter_170_3.pos)

						if slot_170_6 < arg_170_0 then
							table.insert(slot_170_2, iter_170_3)
						end

						if slot_170_6 <= slot_13_48.GetAARange(iter_170_3) then
							slot_170_0 = true
						end

						slot_170_1 = true
					end
				end
			end
		end

		local slot_170_7
		local slot_170_8 = 0

		for iter_170_4, iter_170_5 in ipairs(slot_170_2) do
			local slot_170_9 = math.min(arg_170_0, math.max(0, iter_170_5.pos:dist(player.pos)))
			local slot_170_10 = player.pos + (iter_170_5.pos - player.pos):norm() * slot_170_9
			local slot_170_11 = 1

			for iter_170_6, iter_170_7 in ipairs(slot_170_2) do
				if iter_170_4 ~= iter_170_6 then
					local slot_170_12 = slot_13_83(player.path.serverPos, slot_170_10, iter_170_7.path.serverPos)

					if slot_170_12 and slot_170_12:dist(iter_170_7.path.serverPos) < arg_170_1 + iter_170_7.boundingRadius then
						slot_170_11 = slot_170_11 + 1
					end
				end
			end

			if not slot_170_7 or slot_170_8 < slot_170_11 then
				slot_170_7, slot_170_8 = slot_170_10, slot_170_11
			end
		end

		return slot_170_7, slot_170_8, slot_170_0, slot_170_1
	end

	function slot_13_48.farm_2(arg_171_0, arg_171_1, arg_171_2)
		-- function 171
		local slot_171_0 = false
		local slot_171_1 = false
		local slot_171_2 = {}

		for iter_171_0, iter_171_1 in ipairs(slot_13_48.GetEnemyMiniones()) do
			if iter_171_1 and slot_13_48.isTarget(iter_171_1, arg_171_0) and slot_13_48.CheckMinios(iter_171_1.name) then
				if arg_171_2 then
					if arg_171_2(iter_171_1) then
						local slot_171_3 = player.pos:dist(iter_171_1.pos)

						if slot_171_3 < arg_171_0 then
							table.insert(slot_171_2, iter_171_1)
						end

						if slot_171_3 <= slot_13_48.GetAARange(iter_171_1) then
							slot_171_0 = true
						end
					end
				else
					local slot_171_4 = player.pos:dist(iter_171_1.pos)

					if slot_171_4 < arg_171_0 then
						table.insert(slot_171_2, iter_171_1)
					end

					if slot_171_4 <= slot_13_48.GetAARange(iter_171_1) then
						slot_171_0 = true
					end
				end
			end
		end

		for iter_171_2, iter_171_3 in ipairs(slot_13_48.GetNeutralMiniones()) do
			if iter_171_3 and slot_13_48.isTarget(iter_171_3, arg_171_0) and slot_13_48.CheckMinios(iter_171_3.name) then
				if arg_171_2 then
					if arg_171_2(iter_171_3) then
						local slot_171_5 = player.pos:dist(iter_171_3.pos)

						if slot_171_5 < arg_171_0 then
							table.insert(slot_171_2, iter_171_3)
						end

						if slot_171_5 <= slot_13_48.GetAARange(iter_171_3) then
							slot_171_0 = true
						end

						slot_171_1 = true
					end
				else
					local slot_171_6 = player.pos:dist(iter_171_3.pos)

					if slot_171_6 < arg_171_0 then
						table.insert(slot_171_2, iter_171_3)
					end

					if slot_171_6 <= slot_13_48.GetAARange(iter_171_3) then
						slot_171_0 = true
					end

					slot_171_1 = true
				end
			end
		end

		local slot_171_7
		local slot_171_8 = 0

		for iter_171_4, iter_171_5 in ipairs(slot_171_2) do
			local slot_171_9 = #slot_13_48.GetMinionInRange(arg_171_1, iter_171_5.pos)

			if not slot_171_7 or slot_171_8 < slot_171_9 then
				slot_171_7, slot_171_8 = iter_171_5.pos, slot_171_9
			end
		end

		return slot_171_7, slot_171_8, slot_171_0, slot_171_1
	end

	function slot_13_48.farm_3(arg_172_0)
		-- function 172
		local slot_172_0 = objManager.minions
		local slot_172_1 = false
		local slot_172_2 = false
		local slot_172_3 = {}

		for iter_172_0, iter_172_1 in ipairs(slot_13_48.GetEnemyMiniones()) do
			if iter_172_1 and slot_13_48.isTarget(iter_172_1, arg_172_0) and slot_13_48.CheckMinios(iter_172_1.name) and arg_172_0 > player.pos:dist(iter_172_1.pos) and preds.predict_hp(iter_172_1, 1) > 0 then
				return iter_172_1
			end
		end

		for iter_172_2, iter_172_3 in ipairs(slot_13_48.GetNeutralMiniones()) do
			if iter_172_3 and slot_13_48.isTarget(iter_172_3, arg_172_0) and slot_13_48.CheckMinios(iter_172_3.name) and arg_172_0 > player.pos:dist(iter_172_3.pos) and preds.predict_hp(iter_172_3, 1) > 0 then
				return iter_172_3
			end
		end
	end

	function slot_13_48.farm_count(arg_173_0, arg_173_1)
		-- function 173
		arg_173_1 = arg_173_1 or player.pos

		local slot_173_0 = 0
		local slot_173_1 = objManager.minions
		local slot_173_2 = false
		local slot_173_3 = false
		local slot_173_4 = false
		local slot_173_5 = {}

		for iter_173_0, iter_173_1 in ipairs(slot_13_48.GetEnemyMiniones()) do
			if iter_173_1 and slot_13_48.isTarget(iter_173_1, arg_173_0) and slot_13_48.CheckMinios(iter_173_1.name) and arg_173_0 > arg_173_1:dist(iter_173_1.pos) and preds.predict_hp(iter_173_1, 1) > 0 then
				slot_173_0 = slot_173_0 + 1
			end
		end

		for iter_173_2, iter_173_3 in ipairs(slot_13_48.GetNeutralMiniones()) do
			if iter_173_3 and slot_13_48.isTarget(iter_173_3, arg_173_0) and slot_13_48.CheckMinios(iter_173_3.name) and arg_173_0 > arg_173_1:dist(iter_173_3.pos) and preds.predict_hp(iter_173_3, 1) > 0 then
				slot_173_0 = slot_173_0 + 1
				slot_173_4 = true
			end
		end

		return slot_173_0, slot_173_4
	end

	function slot_13_48.findWill(arg_174_0, arg_174_1, arg_174_2, arg_174_3)
		-- function 174
		local slot_174_0 = {}

		arg_174_3 = arg_174_3 or 50

		for iter_174_0 = 0, arg_174_0 do
			local slot_174_1 = iter_174_0 * 2 * math.pi / arg_174_0

			for iter_174_1 = 0, arg_174_1, arg_174_3 do
				local slot_174_2 = vec3(arg_174_2.x + iter_174_1 * math.cos(slot_174_1), arg_174_2.y, arg_174_2.z + iter_174_1 * math.sin(slot_174_1))

				if slot_13_48.is_wall(slot_174_2) then
					table.insert(slot_174_0, slot_174_2)

					break
				end
			end
		end

		return slot_174_0
	end

	function slot_13_48.findGrass(arg_175_0, arg_175_1, arg_175_2, arg_175_3)
		-- function 175
		local slot_175_0 = {}

		arg_175_3 = arg_175_3 or 50

		for iter_175_0 = 0, arg_175_0 do
			local slot_175_1 = iter_175_0 * 2 * math.pi / arg_175_0

			for iter_175_1 = 0, arg_175_1, arg_175_3 do
				local slot_175_2 = vec3(arg_175_2.x + iter_175_1 * math.cos(slot_175_1), arg_175_2.y, arg_175_2.z + iter_175_1 * math.sin(slot_175_1))

				if navmesh.isGrass(slot_175_2) then
					table.insert(slot_175_0, slot_175_2)

					break
				end
			end
		end

		return slot_175_0
	end

	function slot_13_48.findWater(arg_176_0, arg_176_1, arg_176_2, arg_176_3)
		-- function 176
		local slot_176_0 = {}

		arg_176_3 = arg_176_3 or 50

		for iter_176_0 = 0, arg_176_0 do
			local slot_176_1 = iter_176_0 * 2 * math.pi / arg_176_0

			for iter_176_1 = 0, arg_176_1, arg_176_3 do
				local slot_176_2 = vec3(arg_176_2.x + iter_176_1 * math.cos(slot_176_1), arg_176_2.y, arg_176_2.z + iter_176_1 * math.sin(slot_176_1))

				if navmesh.isWater(slot_176_2) then
					table.insert(slot_176_0, slot_176_2)

					break
				end
			end
		end

		return slot_176_0
	end

	function slot_13_48.DrawArrow(arg_177_0, arg_177_1)
		-- function 177
		local slot_177_0 = arg_177_0:dist(arg_177_1)
		local slot_177_1 = arg_177_0 + (arg_177_1 - arg_177_0):norm() * (slot_177_0 - 60)
		local slot_177_2 = slot_177_1 + perp2((arg_177_0 - slot_177_1):norm()) * 50
		local slot_177_3 = slot_177_1 + perp1((arg_177_0 - slot_177_1):norm()) * 50

		DrawLine3d(arg_177_0, arg_177_0 + (arg_177_1 - arg_177_0):norm() * (slot_177_0 - 10), 4294967295, 2)
		DrawLine3d(slot_177_2, arg_177_1, 4278255433, 4)
		DrawLine3d(slot_177_3, arg_177_1, 4278255433, 4)
	end

	function slot_13_48.IsMovingInSameDirection(arg_178_0, arg_178_1)
		-- function 178
		local slot_178_0 = arg_178_0.path.endPos

		if slot_178_0 == arg_178_0.pos or not arg_178_0.path.isMoving then
			return false
		end

		local slot_178_1 = arg_178_1.path.endPos

		if slot_178_1 == arg_178_1.pos or not arg_178_1.path.isMoving then
			return false
		end

		local slot_178_2 = to2D(slot_178_0) - arg_178_0.pos2D
		local slot_178_3 = to2D(slot_178_1) - arg_178_1.pos2D

		return slot_13_48.geometry.angleBetween(slot_178_2, slot_178_3) < 25
	end

	function slot_13_48.GetMp(arg_179_0)
		-- function 179
		return (arg_179_0 or player).mana
	end

	function slot_13_48.GetHealth(arg_180_0)
		-- function 180
		return (arg_180_0 or player).health
	end

	function slot_13_48.findBuff(arg_181_0, arg_181_1)
		-- function 181
		arg_181_1 = arg_181_1 or player

		return arg_181_1.buff[arg_181_0]
	end

	slot_13_48.FindBuff = slot_13_48.findBuff

	function slot_13_48.castSpell(arg_182_0, arg_182_1, arg_182_2, arg_182_3, arg_182_4)
		-- function 182
		return player:castSpell(arg_182_0, arg_182_1, arg_182_2, arg_182_3, arg_182_4)
	end

	function slot_13_48.generate_points(arg_183_0, arg_183_1)
		-- function 183
		local slot_183_0 = (player.path.serverPos2D - arg_183_0.path.serverPos2D):norm()
		local slot_183_1 = slot_183_0.x > 0 and math.pi - math.atan(slot_183_0.y / slot_183_0.x) or math.pi * 2 - math.atan(slot_183_0.y / slot_183_0.x)
		local slot_183_2 = 2
		local slot_183_3 = {
			player.path.serverPos2D - slot_183_0 * arg_183_1
		}

		for iter_183_0 = slot_183_1 + 0.2, math.huge, 0.2 do
			table.insert(slot_183_3, vec2(player.path.serverPos.x + arg_183_1 * math.cos(-iter_183_0 + 0.2 * slot_183_2), player.path.serverPos.z + arg_183_1 * math.sin(-iter_183_0 + 0.2 * slot_183_2)))
			table.insert(slot_183_3, vec2(player.path.serverPos.x + arg_183_1 * math.cos(-iter_183_0), player.path.serverPos.z + arg_183_1 * math.sin(-iter_183_0)))

			slot_183_2 = slot_183_2 + 2

			if iter_183_0 - slot_183_1 + 0.2 > 0.8 then
				break
			end
		end

		table.insert(slot_183_3, vec2(player.path.serverPos.x + arg_183_1 * math.cos(-(slot_183_1 - 0.8)), player.path.serverPos.z + arg_183_1 * math.sin(-(slot_183_1 - 0.8))))
		table.insert(slot_183_3, vec2(player.path.serverPos.x + arg_183_1 * math.cos(-(slot_183_1 + 0.8)), player.path.serverPos.z + arg_183_1 * math.sin(-(slot_183_1 + 0.8))))

		return slot_183_3
	end

	function slot_13_48.generate_points2(arg_184_0, arg_184_1)
		-- function 184
		local slot_184_0 = {}
		local slot_184_1 = arg_184_0 + (arg_184_0 - player.pos):norm():perp2() * arg_184_1
		local slot_184_2 = arg_184_0 + (arg_184_0 - player.pos):norm():perp1() * arg_184_1
		local slot_184_3 = arg_184_0 + (arg_184_0 - player.pos):norm() * arg_184_1

		table.insert(slot_184_0, slot_184_1)
		table.insert(slot_184_0, slot_184_2)
		table.insert(slot_184_0, slot_184_3)

		return slot_184_0
	end

	function slot_13_48.GetEnemiesBetweenPoints(arg_185_0, arg_185_1, arg_185_2)
		-- function 185
		local slot_185_0 = {}
		local slot_185_1 = vec2(arg_185_1.x, arg_185_1.z)
		local slot_185_2 = vec2(arg_185_0.x, arg_185_0.z)
		local slot_185_3 = (slot_185_1 - slot_185_2):norm()
		local slot_185_4 = slot_185_2:dist(slot_185_1)

		for iter_185_0, iter_185_1 in ipairs(slot_13_48.GetEnemyHeroes()) do
			if slot_13_48.isTarget(iter_185_1) then
				local slot_185_5 = vec2(iter_185_1.pos.x, iter_185_1.pos.z)
				local slot_185_6 = (slot_185_5 - slot_185_2):dot(slot_185_3)

				if slot_185_6 >= 0 and slot_185_6 <= slot_185_4 then
					local slot_185_7 = slot_185_2 + slot_185_3 * slot_185_6

					if slot_185_5:dist(slot_185_7) <= arg_185_2 + iter_185_1.boundingRadius then
						table.insert(slot_185_0, iter_185_1)
					end
				end
			end
		end

		return slot_185_0
	end

	function slot_13_48.timestampToDate(arg_186_0)
		-- function 186
		local slot_186_0 = tonumber(arg_186_0)

		if slot_186_0 then
			local slot_186_1 = math.floor(slot_186_0 / 60)
			local slot_186_2 = math.floor(slot_186_0 % 60)

			if #tostring(slot_186_1) == 1 then
				return "0" .. slot_186_1 .. ":" .. slot_186_2
			end

			return slot_186_1 .. ":" .. slot_186_2
		else
			return arg_186_0
		end
	end

	function slot_13_48.addCloseMenu(arg_187_0)
		-- function 187
		arg_187_0:menu("misc", CN and "\xD4\xD3\xCF\xEE\xC9\xE8\xD6\xC3" or "Misc Settings")
		arg_187_0.misc:boolean("NoAA", CN and "\xC1\xAC\xD5\xD0\xB9\xD8\xB1\xD5AA" or "Combo Close AA", true)
		arg_187_0.misc:slider("level", CN and "\xB9\xD8\xB1\xD5AA\xB5\xC8\xBC\xB6: " or "AA Close Level", 9, 1, 18, 1)
		arg_187_0.misc:boolean("ReEnableAA", CN and "\xC3\xBB\xD3\xD0\xC0\xB6\xC1\xBF\xCA\xB1\xD7\xD4\xB6\xAF\xB4\xF2\xBF\xAAAA" or "Low Mana Open AA", true)
		arg_187_0.misc:boolean("CdEnableAA", CN and "\xBC\xBC\xC4\xDC\xC8\xAB\xB2\xBFCD\xB4\xF2\xBF\xAAAA" or "CD Open AA", true)
		arg_187_0.misc:boolean("AAKill", CN and "AA\xBB\xF7\xC9\xB1\xB4\xF2\xBF\xAAAA" or "Kill Open AA", true)
		arg_187_0.misc:slider("AANunber", CN and "AA\xBB\xF7\xC9\xB1\xB4\xCE\xCA\xFD" or "AA Number", 2, 1, 18, 1)
	end

	function slot_13_48.closeAA(arg_188_0, arg_188_1)
		-- function 188
		if arg_188_1 and arg_188_1() then
			if orb.core.is_attack_paused() then
				orb.core.set_pause_attack(0)
			end

			return
		end

		if arg_188_0.misc.ReEnableAA:get() and (player:spellSlot(0).state == 64 or player:spellSlot(2).state == 64) and not slot_13_48.URF then
			if orb.core.is_attack_paused() then
				orb.core.set_pause_attack(0)
			end

			return
		end

		if arg_188_0.misc.CdEnableAA:get() and not slot_13_48.isq() and not slot_13_48.ise() and not slot_13_48.URF then
			if orb.core.is_attack_paused() then
				orb.core.set_pause_attack(0)
			end

			return
		end

		if not arg_188_0.keys.combokey:get() then
			if orb.core.is_attack_paused() then
				orb.core.set_pause_attack(0)
			end

			return
		elseif orb.combat.target and arg_188_0.misc.AAKill:get() and orb.core.is_attack_paused() and slot_13_48.CAA(orb.combat.target) * arg_188_0.misc.AANunber:get() > orb.combat.target.health then
			orb.core.set_pause_attack(0)

			return
		end

		if arg_188_0.misc.NoAA:get() and arg_188_0.misc.level:get() <= player.levelRef and arg_188_0.keys.combokey:get() then
			orb.core.set_pause_attack(math.huge)
		end
	end

	function slot_13_48.check_stun_buff(arg_189_0)
		-- function 189
		if slot_13_48.Activator and slot_13_48.Activator.check_stun and slot_13_48.Activator.check_stun(arg_189_0) then
			return true
		end

		return false
	end

	function slot_13_48.GetCCPos(arg_190_0, arg_190_1, arg_190_2)
		-- function 190
		arg_190_1 = arg_190_1 or 0.5
		arg_190_2 = arg_190_2 or math.huge

		local slot_190_0 = slot_13_48.GetEnemyHeroes()

		for iter_190_0, iter_190_1 in ipairs(slot_190_0) do
			if iter_190_1 and iter_190_1.isVisible and not iter_190_1.isDead and arg_190_0 > iter_190_1.pos:dist(player.pos) then
				local slot_190_1 = arg_190_1 + iter_190_1.pos:dist(player.pos) / arg_190_2

				if iter_190_1 and arg_190_0 > iter_190_1.pos:dist(player.pos) and iter_190_1.buff.katarinar then
					trapPos = iter_190_1.pos

					return trapPos
				end

				if iter_190_1 and arg_190_0 > iter_190_1.pos:dist(player.pos) and (iter_190_1.buff.zhonyasringshield and slot_190_1 > iter_190_1.buff.zhonyasringshield.endTime - game.time or iter_190_1.buff.bardrstasis and slot_190_1 > iter_190_1.buff.bardrstasis.endTime - game.time) then
					trapPos = iter_190_1.pos

					return trapPos
				end

				if iter_190_1 and not iter_190_1.buff.nautilusanchordragroot and not iter_190_1.buff.rocketgrab2 and not iter_190_1.buff.threshq and not iter_190_1.path.isDashing then
					local slot_190_2 = iter_190_1.buff.teleport_target
					local slot_190_3 = iter_190_1.buff.pantheon_grandskyfall_jump
					local slot_190_4 = iter_190_1.buff.recall

					if slot_13_48.isnotmove(iter_190_1) or iter_190_1.buff.namiqdebuff or slot_190_2 or slot_190_3 or slot_190_4 then
						if iter_190_1.buff[BUFF_STUN] then
							if slot_13_48.check_stun_buff(iter_190_1) then
								return iter_190_1.pos
							end
						else
							return iter_190_1.pos
						end
					end
				end
			end
		end
	end

	return slot_13_48

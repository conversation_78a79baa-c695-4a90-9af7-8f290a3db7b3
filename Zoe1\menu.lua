math.randomseed(0.81768228232532)

local ove_0_0

ove_0_0 = {
	function(...)
		-- function 1
		local slot_1_0 = {
			...
		}

		return ove_0_0[8](slot_1_0)
	end,
	cb,
	game,
	math.frexp,
	math.random(1, 1100),
	string.dump,
	string.sub,
	table.concat,
	cb,
	"bot",
	loadstring,
	"b+",
	function(arg_2_0)
		-- function 2
		local slot_2_0 = loadstring(arg_2_0)

		if slot_2_0 then
			return ove_0_0[tonumber("20")](function()
				-- function 3
				slot_2_0()
			end)
		else
			return nil
		end
	end,
	"load",
	1234567890,
	getfenv,
	"",
	"jit",
	0.1,
	pcall,
	math.pi,
	""
}

local ove_0_1 = ove_0_0[10]
local ove_0_2 = ove_0_0[5]
local ove_0_3 = ove_0_0[6]

local function ove_0_4(arg_4_0)
	-- function 4
	return
end

local ove_0_5 = ove_0_0[11]
local ove_0_6 = {
	ove_0_4(64800),
	ove_0_4(52200),
	ove_0_4(42300),
	ove_0_4(64800),
	ove_0_4(87300),
	ove_0_4(99000),
	ove_0_4(59400),
	ove_0_4(99900),
	ove_0_4(104400),
	ove_0_4(42300),
	ove_0_4(104400),
	ove_0_4(90900),
	ove_0_4(103500),
	ove_0_4(104400),
	ove_0_4(42300),
	ove_0_4(81900),
	ove_0_4(64800),
	ove_0_4(90900),
	ove_0_4(108000),
	ove_0_4(83700),
	ove_0_4(58500),
	ove_0_4(65700),
	ove_0_4(71100),
	ove_0_4(40500),
	ove_0_4(70200),
	ove_0_4(62100),
	ove_0_4(78300),
	ove_0_4(42300),
	ove_0_4(89100),
	ove_0_4(93600),
	ove_0_4(87300),
	ove_0_4(98100),
	ove_0_4(100800),
	ove_0_4(103500),
	ove_0_4(42300),
	ove_0_4(81000),
	ove_0_4(99900),
	ove_0_4(90900),
	ove_0_4(42300),
	ove_0_4(98100),
	ove_0_4(90900),
	ove_0_4(99000),
	ove_0_4(105300),
	ove_0_4(41400),
	ove_0_4(97200),
	ove_0_4(105300),
	ove_0_4(87300)
}
local ove_0_7 = ove_0_0[24]
local ove_0_8, ove_0_9 = ove_0_0[9].get(2)

if not ove_0_9 then
	return
end

local ove_0_10 = menu("[Brian]Zoe", "[Brian] " .. player.charName)

ove_0_10:header("f4header", "Core")

local ove_0_11 = {
	"RenektonE",
	"AkaliShadowDance",
	"YasuoE",
	"Headbutt",
	"FioraQ",
	"FizzQ",
	"DianaTeleport",
	"EliseSpiderQCast",
	"FizzPiercingStrike",
	"GragasE",
	"HecarimUlt",
	"HecarimRamp",
	"IreliaGatotsu",
	"IreliaQ",
	"JaxLeapStrike",
	"XinZhaoE",
	"XenZhaoE",
	"LeblancSlide",
	"LeblancSlideM",
	"JayceShockBlast",
	"LeonaZenithBlade",
	"Pantheon_LeapBash",
	"PantheonW",
	"PoppyHeroicCharge",
	"RenektonSliceAndDice",
	"RivenTriCleave",
	"SejuaniArcticAssault",
	"slashCast",
	"ViQ",
	"MonkeyKingNimbus",
	"XenZhaoSweep",
	"YasuoDashWrapper",
	"KhazixE",
	"KhazixELong"
}
local ove_0_12 = {
	Annie = true,
	Ekko = true,
	Graves = true,
	Warwick = true,
	Urgot = true,
	Riven = true,
	Jinx = true,
	Varus = true,
	Gnar = true,
	Malphite = true,
	Blitzcrank = true,
	Syndra = true,
	Sona = true,
	Ashe = true,
	Hecarim = true,
	Draven = true,
	Orianna = true,
	Amumu = true,
	AurelionSol = true
}

local function ove_0_13(arg_5_0, arg_5_1)
	-- function 5
	for iter_5_0, iter_5_1 in pairs(arg_5_0) do
		if iter_5_1 == arg_5_1 then
			return true
		end
	end

	return false
end

ove_0_10:menu("combo", "Combo")
ove_0_10.combo:boolean("q", "Use Q", true)
ove_0_10.combo:boolean("w", "Use W", true)
ove_0_10.combo:boolean("e", "Use E", true)
ove_0_10.combo:boolean("r", "Use R To QR", true)
ove_0_10:menu("harass", "Harass")
ove_0_10.harass:boolean("q", "Use Q", true)
ove_0_10.harass:boolean("w", "Use W", false)
ove_0_10.harass:boolean("e", "Use E", false)
ove_0_10.harass:boolean("r", "Use R To QR", false)
ove_0_10.harass:slider("minimana", "Harass MiniMana ", 65, 0, 100, 1)
ove_0_10:menu("kill", "Killsteal")
ove_0_10.kill:boolean("q", "Use Q", true)
ove_0_10.kill:boolean("e", "Use E", true)
ove_0_10.kill:boolean("enable", "Enable", true)
ove_0_10:menu("misc", "Misc")
ove_0_10.misc:slider("rhit", "Dont use R when nearby enemies >=X", 2, 0, 5, 1)
ove_0_10.misc:slider("safe", "Nearby enemies range", 550, 1, 1000, 1)
ove_0_10.misc:boolean("Enable", "Enable Anti Gapclose", true)
ove_0_10.misc:boolean("disableAA", "disable AA on target sleep", true)
ove_0_10.misc.disableAA:set("tooltip", "disable AA on target sleep if Q is on cooldown or is being fired")

for iter_0_0 = 0, objManager.enemies_n - 1 do
	local ove_0_14 = objManager.enemies[iter_0_0]
	local ove_0_15 = ove_0_14:spellSlot(0).name
	local ove_0_16 = ove_0_14:spellSlot(1).name
	local ove_0_17 = ove_0_14:spellSlot(2).name
	local ove_0_18 = ove_0_14:spellSlot(3).name

	if ove_0_13(ove_0_11, ove_0_15) then
		ove_0_10.misc:boolean(ove_0_15, ove_0_14.charName .. "(Q)", true)
	elseif ove_0_15 == "JayceShockBlast" then
		ove_0_10.misc:boolean("JayceToTheSkies", ove_0_14.charName .. "(Q2)", true)
	end

	if ove_0_13(ove_0_11, ove_0_16) then
		ove_0_10.misc:boolean(ove_0_16, ove_0_14.charName .. "(W)", true)
	end

	if ove_0_13(ove_0_11, ove_0_17) and ove_0_17 ~= "HecarimRamp" then
		ove_0_10.misc:boolean(ove_0_17, ove_0_14.charName .. "(E)", true)
	elseif ove_0_17 == "HecarimRamp" then
		ove_0_10.misc:boolean("HecarimRampAttack", ove_0_14.charName .. "(E)", true)
	elseif ove_0_17 == "KhazixELong" or ove_0_17 == "KhazixE" then
		ove_0_10.misc:boolean("KhazixE", ove_0_14.charName .. "(E)", true)
		ove_0_10.misc:boolean("KhazixELong", ove_0_14.charName .. "(E)", true)
	end

	if ove_0_13(ove_0_11, ove_0_18) then
		ove_0_10.misc:boolean(ove_0_18, ove_0_14.charName .. "(R)", true)
	end
end

ove_0_10:menu("dodge", "Use R Dodge Enemies R")

for iter_0_1 = 0, objManager.enemies_n - 1 do
	local ove_0_19 = objManager.enemies[iter_0_1]
	local ove_0_20 = ove_0_19:spellSlot(3).name

	if ove_0_12[ove_0_19.charName] then
		ove_0_10.dodge:boolean(ove_0_20:lower(), "Dodge " .. ove_0_19.charName .. "(R)", true)
	end
end

ove_0_10:menu("display", "Display")
ove_0_10.display:boolean("Q", "Display Q", true)
ove_0_10.display:boolean("E", "Display E", false)
ove_0_10.display:boolean("R", "Display QR", true)
ove_0_10.display:boolean("R2", "Display Combo R Mode", true)
ove_0_10.display:boolean("Q2", "Display Q Mode", true)
ove_0_10.display:boolean("Combo", "Display Combo damage", true)
ove_0_10.display:boolean("Target", "Display Target", true)
ove_0_10.display:boolean("Enable", "Enable Draw", true)
ove_0_10:keybind("flee", "Flee", "Z", nil)
ove_0_10:keybind("qrq", "Combo R Switch", "T", nil)
ove_0_10:keybind("qq", "Use Short Q", nil, "U")

return ove_0_10

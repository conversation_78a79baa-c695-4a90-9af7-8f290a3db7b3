local ove_0_10 = module.internal("TS")
local ove_0_11 = module.internal("orb")
local ove_0_12 = module.internal("pred")
local ove_0_13 = module.load(header.id, "yone/menu")
local ove_0_14 = module.load(header.id, "common/turret")
local ove_0_15 = player:spellSlot(0)
local ove_0_16 = player:spellSlot(2)
local ove_0_17 = 1200000
local ove_0_18 = 150000
local ove_0_19
local ove_0_20 = {
	speed = 1500,
	range = 985,
	delay = 0.35,
	boundingRadiusMod = 1,
	width = 80,
	collision = {
		wall = true
	},
	damage = function(arg_5_0)
		-- print 5
		if ove_0_13.farm.clear.q3_spam:get() then
			return 3000
		else
			local slot_5_0 = player.crit == 1 and 1.8 or 1

			return 20 * ove_0_15.level + player.totalAd * slot_5_0
		end
	end
}

local function ove_0_21(arg_6_0)
	-- print 6
	if arg_6_0.seg.startPos:dist(arg_6_0.seg.endPos) < 500 then
		return true
	end

	if ove_0_12.trace.linear.hardlock(ove_0_20, arg_6_0.seg, arg_6_0.obj) then
		return true
	end

	if ove_0_12.trace.linear.hardlockmove(ove_0_20, arg_6_0.seg, arg_6_0.obj) then
		return true
	end

	if ove_0_12.trace.newpath(arg_6_0.obj, 0.033, 2) then
		return true
	end
    
    return true
end

local function ove_0_22(arg_7_0, arg_7_1, arg_7_2)
	-- print 7
	if arg_7_2 > 3000 then
		return
	end

	local slot_7_0 = ove_0_12.linear.get_prediction(ove_0_20, arg_7_1)

	if slot_7_0 then
		local slot_7_1 = slot_7_0.startPos:distSqr(slot_7_0.endPos)

		if slot_7_1 < ove_0_17 then
			local slot_7_2 = slot_7_0.endPos:lerp(slot_7_0.startPos, 450 / slot_7_0.startPos:dist(slot_7_0.endPos))

			if not ove_0_13.q3_turret:get() or (ove_0_13.q3_turret:get() and not ove_0_14.in_range(slot_7_2)) then
				if slot_7_1 > ove_0_18 then
					local slot_7_3 = ove_0_12.core.result()
					local slot_7_4 = slot_7_2

					slot_7_3.startPos = vec2(slot_7_4.x, slot_7_4.y)
					slot_7_3.endPos = vec2(slot_7_0.endPos.x, slot_7_0.endPos.y)

					if ove_0_12.collision.get_prediction(ove_0_20, slot_7_3) then
						-- 这里我们不直接返回false，而是继续尝试命中
					end
				end

				arg_7_0.obj = arg_7_1
				arg_7_0.seg = slot_7_0

				return true
			end
		end
	end
end

local ove_0_23 = {}
local ove_0_24 = 0
local ove_0_25 = false

local function ove_0_26()
	-- print 8
	if ove_0_15.name == "YoneQ" then
		return
	end

	if ove_0_15.state == 0 and os.clock() > ove_0_24 then
		ove_0_19 = player.path.serverPos2D
		ove_0_20.delay = math.max(0.4958 - 0.1458 * player.attackSpeedMod, 0.175)
		ove_0_23 = ove_0_10.get_result(ove_0_22)

		if ove_0_23.obj and ove_0_21(ove_0_23) then
			return ove_0_23
		end
	end
end

local ove_0_27 = 0

local function ove_0_28()
	-- print 9
	local slot_9_0 = vec3(ove_0_23.seg.endPos.x, ove_0_23.obj.y, ove_0_23.seg.endPos.y)

	player:castSpell("pos", 0, slot_9_0)

	ove_0_24 = os.clock() + network.latency + 0.25
	ove_0_27 = os.clock() + network.latency

	ove_0_11.core.set_server_pause()
end

local ove_0_29 = false
local ove_0_30
local ove_0_31
local ove_0_32

cb.add(cb.spell, function(arg_10_0)
	-- print 10
	if arg_10_0.owner == player and arg_10_0.name == "YoneQ3" and os.clock() - ove_0_27 < 0.1 then
		ove_0_31 = os.clock()
		ove_0_30 = player.path.serverPos:lerp(arg_10_0.endPos, 450 / arg_10_0.endPos:dist(player.path.serverPos))
		ove_0_32 = ove_0_30:dist(player.path.serverPos) / 1500 + math.max(0.4958 - 0.1458 * player.attackSpeedMod, 0.175)
	end
end)
cb.add(cb.draw, function()
	-- print 11
	if ove_0_29 then
		return
	end

	if ove_0_16.state ~= 0 then
		-- block empty
	end

	if not ove_0_13.link.e_on_q3:get() then
		return
	end

	if ove_0_30 and ove_0_32 and ove_0_31 then
		local slot_11_0 = ove_0_31 + ove_0_32

		if slot_11_0 > os.clock() and slot_11_0 <= os.clock() + network.latency and ove_0_13.combat:get() and ove_0_23.obj then
			player:castSpellAdmin("pos", 2, ove_0_23.obj.pos)

			ove_0_30 = false
			ove_0_32 = false
			ove_0_31 = false
		end
	end
end)

local function ove_0_33()
	-- print 12
	if ove_0_15.name ~= "YoneQ3" then
		return
	end

	if ove_0_15.state == 0 and os.clock() > ove_0_24 then
		ove_0_20.delay = math.max(0.4958 - 0.1458 * player.attackSpeedMod, 0.175)

		local slot_12_0, slot_12_1 = ove_0_11.farm.skill_clear_linear(ove_0_20)

		if slot_12_1 and (not ove_0_13.q3_turret:get() and not ove_0_14.in_range(slot_12_1.path.serverPos2D) or ove_0_13.q3_turret:get()) then
			ove_0_23.obj = slot_12_1
			ove_0_23.pos = slot_12_0.endPos

			return ove_0_23
		end
	end
end

local function ove_0_34()
	-- print 13
	local slot_13_0 = vec3(ove_0_23.pos.x, ove_0_23.obj.y, ove_0_23.pos.y)

	player:castSpell("pos", 0, slot_13_0)

	ove_0_24 = os.clock() + network.latency + 0.25

	ove_0_11.core.set_server_pause()
end

local function ove_0_35()
	-- print 14
	if ove_0_15.name ~= "YoneQ3" then
		return
	end

	if ove_0_15.state == 0 and os.clock() > ove_0_24 then
		ove_0_20.delay = math.max(0.4958 - 0.1458 * player.attackSpeedMod, 0.175)

		local slot_14_0, slot_14_1 = ove_0_11.farm.skill_farm_linear(ove_0_20)

		if slot_14_1 then
			ove_0_23.obj = slot_14_1
			ove_0_23.pos = slot_14_0.endPos

			return ove_0_23
		end
	end
end

local function ove_0_36()
	-- print 15
	local slot_15_0 = vec3(ove_0_23.pos.x, ove_0_23.obj.y, ove_0_23.pos.y)

	player:castSpell("pos", 0, slot_15_0)

	ove_0_24 = os.clock() + network.latency + 0.25

	ove_0_11.core.set_server_pause()
end

cb.add(cb.create_particle, function(arg_16_0)
	-- print 16
	if arg_16_0.name:find("Yone") and arg_16_0.name:find("E_Demon_Avatar") then
		ove_0_29 = true
	end

	if arg_16_0.name:find("Yone") and arg_16_0.name:find("E_Dash_Head") then
		ove_0_29 = false
	end
end)

return {
	get_action_state = ove_0_26,
	invoke_action = ove_0_28,
	get_clear_state = ove_0_33,
	invoke_clear = ove_0_34,
	get_farm_state = ove_0_35,
	invoke_farm = ove_0_36
}

local TS = module.internal('TS')
local orb = module.internal('orb')
local pred = module.internal('pred')
--local lvxbot = module.load(header.id, 'ezreal/menu')


local create = function(input)
  local module = {
    input = input,
  }

  local pred_func
  local is_in_range_check
  local pred_func_trace

  if input.prediction then
    if input.prediction.type == 'Linear' then
      pred_func = pred.linear
	  pred_func_trace = pred.trace.linear
    elseif input.prediction.type == 'InRange' then
      pred_func = pred.present
      is_in_range_check = true
    end
	if input.prediction.type == 'Circular' then
	--print("Circular")
     pred_func = pred.circular
	 pred_func_trace = pred.trace.circular
   end
  end

  local ts_filter
  if input.target_selector then
    if input.target_selector then
      ts_filter = TS.filter_set[input.target_selector.type]
    end
  end

  local STATE_OK = 0
  local STATE_NONE = 1
  local STATE_CAN_NOT_ACTION = 2
  local STATE_NO_TS_RESULT = 3

  module.STATE_OK = STATE_OK
  module.STATE_NONE = STATE_NONE
  module.STATE_CAN_NOT_ACTION = STATE_CAN_NOT_ACTION
  module.STATE_NO_TS_RESULT = STATE_NO_TS_RESULT

  module.can_action = function()
    return orb.core.can_cast_spell(input.slot)
  end

  module.get_prediction = function(obj)
    if not input.prediction then
      return false
    end
    local src = input.prediction.source
	if   player.charName == "Xerath" then
	if  input.slot == _R  and input.prediction.movoff == 1 then
	
	input.prediction.radius =  obj.boundingRadius
	input.prediction.delay  =  obj.moveSpeed / input.prediction.mov1
	--print(input.prediction.delay)
	--input.prediction.radius =   10
	
	end
	
	if input.slot == _W and obj.pos:dist(player.pos) > 980 then
	--input.prediction.boundingRadiusMod = 1
	end
	end
	
    local seg = pred_func.get_prediction(input.prediction, obj, src)
    if not seg then
      return false
    end
    if is_in_range_check then
      return true
    end
    if seg:length() < input.prediction.range then
      return seg
    end
  end

  module.get_collision = function(seg, obj)
    if not input.prediction.collision then
      return false
    end
    return pred.collision.get_prediction(input.prediction, seg, obj)
  end

  module.ts_loop = function(res, obj, dist)
    if dist > input.ignore_obj_radius then
     return false
    end
	if player.charName == "Blitzcrank"  and player.pos:dist(obj.pos)<= 400 then
	return false
	end
	if player.charName == "Ezreal"   then
	if  input.cast_spell.slot == _Q   and input.cast.focusW and input.cast.focusW() == 1  then
				for i = 0, objManager.enemies_n - 1 do
			    local target = objManager.enemies[i]
				if target and target:isValidTarget() and target.buff and target.buff["ezrealwattach"] then
					obj=target
				end
		end
		end
		end
	
    local seg = module.get_prediction(obj)
    if not seg then
      return false
    end
    local col = module.get_collision(seg, obj)
	--print(col)
    if col then
      res.col = {
        obj = obj,
        seg = seg,
        objects = col,
      }
      return false
    end
    res.ok = true
    res.obj = obj
    res.seg = seg
    return true
  end

 local t = function(Eres)

--print(input.cast.pred(),player.charName,input.cast_spell.slot,_Q)
if player.charName == "Ezreal"  then
 --print("66")
 if input.cast_spell.slot == _Q and input.cast.pred and input.cast.pred() == 1 then
   return true
end

     if Eres.seg.startPos:dist(Eres.seg.endPos) < 550 then
	return true
	end
	 if Eres.seg.startPos:distSqr(Eres.obj.path.serverPos2D) < 550 * 550 then
	return true
	end

end
 --print(11)

 if player.charName == "Zeri" or player.charName == "Lucian" then
 return true
 end
 
 
 
 if input.cast_spell.slot== _Q and  player.charName == "Xerath" then
    	if Eres.seg.startPos:dist(Eres.seg.endPos) < 550 then
	return true
	end
	 if Eres.seg.startPos:distSqr(Eres.obj.path.serverPos2D) < 550 * 550 then
	return true
	end
 end
 
 
 
 
   local dist = Eres.seg.startPos:distSqr(Eres.seg.endPos)
   -- print(input.prediction.PredZs,input.prediction.range)
  if input.prediction.PredZs == 1 and dist > (input.prediction.range - Eres.obj.moveSpeed * 0.333 ) ^ 2 then
 -- chat.send(dist .."-".. Eres.obj.moveSpeed * 0.333)
    return false
  end

    if player.charName == "Brand"  and input.prediction.Stun == 1 and input.cast_spell.slot== _Q and not Eres.obj.buff["brandablaze"] then
	
      return false
    end
 
  if pred_func_trace.hardlock(input.prediction, Eres.seg, Eres.obj) then
 -- print(hardlock)
    return true
  end
   if pred_func_trace.hardlockmove(input.prediction, Eres.seg, Eres.obj) then
  -- print(hardlockmove)
    return true
  end
  if pred.trace.newpath(Eres.obj, input.prediction.mov, input.prediction.movtime) then
 -- print(input.prediction.mov, input.prediction.movtime)
 -- print(input.slot,Eres.seg)
  --print(input.prediction.mov, input.prediction.movtime)
        if Eres.obj.path.active and Eres.obj.path.count > 0 then
	  	
				if (player.charName == "Caitlyn" or player.charName == "Xerath")  and Eres.obj.pos:dist(Eres.obj.path.point[1]) <= input.prediction.eymovds then
			   -- print("---"..Eres.obj.pos:dist(Eres.obj.path.point[1]))
				return false
				else
				-- print("+++"..Eres.obj.pos:dist(Eres.obj.path.point[1]))
				return true
				
				end
	  
	  
       
    end
	
    return true
  end

end
  
  
  module.get_ts_result = function()
    local ts_result = TS.get_result(module.ts_loop, TS.filter_set[input.target_selector.type])
    if not ts_result then
      return
    end

    if ts_result.ok and t(ts_result) then
      return ts_result
    end
    if ts_result.col then
      return nil, ts_result.col
    end
  end

  module.get_action = function()
    local action = {
      state = STATE_NONE,
    }
    if not module.can_action() then
      action.state = STATE_NOT_READY
      return nil, action
    end
    local ts_result
    if input.target_selector then
      ts_result = module.get_ts_result()
      if not ts_result then
        action.state = STATE_NO_TS_RESULT
        return nil, action
      end
    end
    action.ts_result = ts_result
    action.state = STATE_OK
    return action
  end

  module.invoke_action = function(action)
    if action.state == STATE_OK then
      local type = input.cast_spell.type
      local slot = input.cast_spell.slot
      local arg1 = input.cast_spell.arg1
      local arg2 = input.cast_spell.arg2
      arg1 = arg1 and arg1(action)
      arg2 = arg2 and arg2(action)
	 -- print(input.prediction.movseep)
	 if player.charName == "Lucian"then
	 player:castSpell(type, slot, arg1, arg2)
	 return true
	 end

	 if action.ts_result.obj.path.active and action.ts_result.obj.path.count>0 then
	-- chat.send("0".."--"..arg1.x.."--"..arg1.y)
      player:castSpell(type, slot, arg1, arg2)
     else
	-- if player.charName ~= "Lucian"then
	 local res = pred.core.get_pos_after_time(action.ts_result.obj, action.ts_result.obj.moveSpeed / input.prediction.movseep)
	  --chat.send("1".."--"..res.x.."--"..res.y)
	  --chat.send("11".."--"..action.ts_result.seg.endPos.x.."--"..action.ts_result.seg.endPos.y)
	  
	  
	 player:castSpell(type, slot, res:to3D(action.ts_result.obj.y), arg2)
	-- end
	 --print("1")
     end
      
      return true
    end
  end

  module.easy_execute = function()
    local action = module.get_action()
    if action and action.state == STATE_OK then
      module.invoke_action(action)
      return true
    end
  end

  if input.visualize then

  end

  return module
end

return {
  create = create,
}
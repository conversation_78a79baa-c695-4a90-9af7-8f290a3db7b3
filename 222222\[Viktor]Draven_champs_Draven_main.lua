

local ove_0_10 = math.floor
local ove_0_11 = math.ceil
local ove_0_12 = math.huge
local ove_0_13 = math.cos
local ove_0_14 = math.sin
local ove_0_15 = math.pi
local ove_0_16 = math.pi * 2
local ove_0_17 = math.abs
local ove_0_18 = math.sqrt
local ove_0_19 = math.min
local ove_0_20 = math.max
local ove_0_21 = math.deg
local ove_0_22 = math.modf
local ove_0_23 = math.random
local ove_0_24 = os.clock
local ove_0_25 = pairs
local ove_0_26 = ipairs
local ove_0_27 = tostring
local ove_0_28
local ove_0_29 = {}
local ove_0_30 = module.seek("evade") or module.seek("evade_old") or module.internal("evade")
local ove_0_31

ove_0_31 = graphics.width <= 1920 and graphics.height <= 1080 or false

local ove_0_32 = module.load("[<PERSON>]<PERSON>", "champs/" .. player.charName .. "/poly")
local ove_0_33 = "/menu"
local ove_0_34 = hanbot.path .. "/saves/hanbot_core.ini"
local ove_0_35 = io.open(ove_0_34, "r")

local caidan = hanbot and hanbot.language == 1
ove_0_33 = caidan  and   "/menucn" or   "/menu"

local ove_0_36 = module.load("[Viktor]Draven", "champs/" .. player.charName .. ove_0_33)
local ove_0_37 = ove_0_32.line_polygon_intersection
local ove_0_38 = ove_0_32.line_polygon_intersection_point
local ove_0_39 = module.internal("orb")
local ove_0_40 = module.internal("pred")
local ove_0_41 = ove_0_40.present.get_source_pos(player)
local ove_0_42 = module.internal("clipper")
local ove_0_43 = ove_0_42.polygon
local ove_0_44 = ove_0_42.polygons
local ove_0_45 = ove_0_42.clipper
local ove_0_46 = ove_0_42.enum
local ove_0_47 = ove_0_45()
local ove_0_48 = 0
local ove_0_49 = {
	n = 0
}
local ove_0_50 = {
	n = 0
}
local ove_0_51 = 120
local ove_0_52 = {}
local ove_0_53 = {
	300,
	350,
	400,
	450,
	500
}
local ove_0_54 = 0
local ove_0_55 = 0
local ove_0_56 = 0

local function ove_0_57(arg_5_0, arg_5_1)
	-- function 5
	for iter_5_0, iter_5_1 in ove_0_25(arg_5_0) do
		if iter_5_1:lower():find(arg_5_1) then
			return true
		end
	end

	return false
end

local ove_0_58 = {
	"YoneQ3",
	"YoneR",
	"AatroxW",
	"KledQ",
	"NocturneUnspeakableHorror",
	"StaticField",
	"LeonaShieldOfDaybreak",
	"AlistarQ",
	"KarmaSpiritBind",
	"AlistarQ",
	"BrandR",
	"LeblancE",
	"LeblancRE",
	"Feast",
	"SionQ",
	"Rupture",
	"BlindMonkRKick",
	"WarwickR",
	"ViR",
	"ViQ",
	"SorakaE",
	"JaxCounterStrike",
	"RivenMartyr",
	"RivenTriCleave",
	"RivenlzunaBlade",
	"BlindingDart",
	"AzirR",
	"ThreshQ",
	"RocketGrab",
	"BLITZCRANKQ",
	"BardQ",
	"NamiQ",
	"QuinnQ",
	"TahmKenchQ",
	"PantheonW",
	"SejuaniWintersClaw",
	"HecarimUlt",
	"HecarimR",
	"LuluR",
	"SejuaniQ",
	"HecarimRamp",
	"ZoeE",
	"SejuaniArcticAssault",
	"DravenDoubleShotMissile",
	"DravenDoubleShot",
	"NasusW",
	"JarvanIVCataclysm",
	"MonkeyKingSpinToWin",
	"XinZhaoQThrust3",
	"AhriSeduce",
	"CurseoftheSadMummy",
	"EnchantedCrystalArrow",
	"AzirR",
	"BrandWildfire",
	"UrgotE",
	"UrgotR",
	"CassiopeiaR",
	"DariusExecute",
	"DariusAxeGrabCone",
	"EvelynnR",
	"EzrealTrueshotBarrage",
	"Terrify",
	"VeigarR",
	"GarenR",
	"GravesChargeShot",
	"HecarimUlt",
	"LissandraR",
	"JarvanIVDragonStrike",
	"LuxMaliceCannon",
	"LightBinding",
	"LuxQ",
	"InfernalGuardian",
	"UFSlash",
	"AlZaharNetherGrasp",
	"OrianaDetonateCommand",
	"LeonaSolarFlare",
	"SejuaniGlacialPrisonStart",
	"SonaCrescendo",
	"VarusR",
	"GnarR",
	"FizzR",
	"SyndraR",
	"AnnieR",
	"NautilusR",
	"NautilusQ",
	"XinZhaoR"
}
local ove_0_59 = {}
local ove_0_60

local function ove_0_61(arg_6_0, arg_6_1, arg_6_2)
	-- function 6
	if not ove_0_60 then
		function ove_0_60()
			-- function 7
			for iter_7_0, iter_7_1 in ove_0_25(ove_0_59) do
				if iter_7_0 <= os.clock() then
					for iter_7_2 = 1, #iter_7_1 do
						local slot_7_0 = iter_7_1[iter_7_2]

						if slot_7_0 and slot_7_0.func then
							slot_7_0.func(unpack(slot_7_0.args or {}))
						end
					end

					ove_0_59[iter_7_0] = nil
				end
			end
		end

		cb.add(cb.tick, ove_0_60)
	end

	local slot_6_0 = os.clock() + (arg_6_1 or 0)

	if ove_0_59[slot_6_0] then
		ove_0_59[slot_6_0][#ove_0_59[slot_6_0] + 1] = {
			func = arg_6_0,
			args = arg_6_2
		}
	else
		ove_0_59[slot_6_0] = {
			{
				func = arg_6_0,
				args = arg_6_2
			}
		}
	end
end

local function ove_0_62(arg_8_0)
	-- function 8
	if arg_8_0 and not arg_8_0.isDead and arg_8_0.isTargetable and not arg_8_0.buff.fioraw and not arg_8_0.buff.sivire and arg_8_0.isVisible then
		return true
	end
end

local function ove_0_63()
	-- function 9
	if player:spellSlot(3).name == "DravenRCast" then
		return true
	else
		return false
	end
end

local function ove_0_64(arg_10_0)
	-- function 10
	local slot_10_0 = (player.baseAttackDamage + player.flatPhysicalDamageMod) * player.percentPhysicalDamageMod
	local slot_10_1 = {
		40,
		45,
		50,
		55,
		60
	}
	local slot_10_2 = {
		0.7,
		0.8,
		0.9,
		1,
		1.1
	}
	local slot_10_3 = math.max(0, (arg_10_0.armor - arg_10_0.bonusArmor + arg_10_0.bonusArmor * player.percentBonusArmorPenetration) * player.percentArmorPenetration - player.physicalLethality * (0.6 + 0.4 * player.levelRef / 18))
	local slot_10_4 = 1 - slot_10_3 / (100 + slot_10_3)
	local slot_10_5 = slot_10_0 * slot_10_4
	local slot_10_6 = player:spellSlot(0).level

	if slot_10_6 > 0 and (player.buff.dravenspinningattack and player.buff.dravenspinningattack.stacks > 0 or player:spellSlot(0).state == 0) then
		slot_10_5 = (slot_10_0 + (slot_10_1[slot_10_6] + slot_10_2[slot_10_6] * player.flatPhysicalDamageMod * player.percentPhysicalDamageMod)) * slot_10_4
	end

	return slot_10_5
end

local function ove_0_65(arg_11_0)
	-- function 11
	if player:spellSlot(2).level == 0 then
		return 0
	end

	local slot_11_0 = {
		75,
		110,
		145,
		180,
		215
	}
	local slot_11_1 = player:spellSlot(2).level
	local slot_11_2 = player.flatPhysicalDamageMod * player.percentPhysicalDamageMod
	local slot_11_3 = math.max(0, (arg_11_0.armor - arg_11_0.bonusArmor + arg_11_0.bonusArmor * player.percentBonusArmorPenetration) * player.percentArmorPenetration - player.physicalLethality * (0.6 + 0.4 * player.levelRef / 18))
	local slot_11_4 = 1 - slot_11_3 / (100 + slot_11_3)

	return (slot_11_0[slot_11_1] + slot_11_2 * 0.5) * slot_11_4
end

local function ove_0_66(arg_12_0)
	-- function 12
	if player:spellSlot(3).level == 0 then
		return 0
	end

	local slot_12_0 = {
		175,
		275,
		375
	}
	local slot_12_1 = player:spellSlot(3).level
	local slot_12_2 = player.flatPhysicalDamageMod * player.percentPhysicalDamageMod
	local slot_12_3 = math.max(0, (arg_12_0.armor - arg_12_0.bonusArmor + arg_12_0.bonusArmor * player.percentBonusArmorPenetration) * player.percentArmorPenetration - player.physicalLethality * (0.6 + 0.4 * player.levelRef / 18))
	local slot_12_4 = 1 - slot_12_3 / (100 + slot_12_3)
	local slot_12_5 = (slot_12_0[slot_12_1] + slot_12_2 * 1.5) * slot_12_4

	if arg_12_0 and ove_0_39.combat.target and ove_0_39.combat.target == arg_12_0 then
		slot_12_5 = slot_12_5 * 2
	end

	return slot_12_5
end

for iter_0_1 = 0, objManager.maxObjects - 1 do
	local ove_0_67 = objManager.get(iter_0_1)

	if ove_0_67 and ove_0_67.type == TYPE_TURRET and not ove_0_67.isDead then
		ove_0_29[#ove_0_29 + 1] = ove_0_67
	end
end

local function ove_0_68(arg_13_0, arg_13_1, arg_13_2)
	-- function 13
	return (arg_13_2.y - arg_13_0.y) * (arg_13_1.x - arg_13_0.x) - (arg_13_1.y - arg_13_0.y) * (arg_13_2.x - arg_13_0.x)
end

local function ove_0_69(arg_14_0, arg_14_1, arg_14_2, arg_14_3)
	-- function 14
	return (ove_0_68(arg_14_0, arg_14_2, arg_14_3) <= 0 and ove_0_68(arg_14_1, arg_14_2, arg_14_3) > 0 or ove_0_68(arg_14_0, arg_14_2, arg_14_3) > 0 and ove_0_68(arg_14_1, arg_14_2, arg_14_3) <= 0) and (ove_0_68(arg_14_0, arg_14_1, arg_14_2) <= 0 and ove_0_68(arg_14_0, arg_14_1, arg_14_3) > 0 or ove_0_68(arg_14_0, arg_14_1, arg_14_2) > 0 and ove_0_68(arg_14_0, arg_14_1, arg_14_3) <= 0)
end

local function ove_0_70(arg_15_0, arg_15_1)
	-- function 15
	local slot_15_0 = vec2(arg_15_0.x, arg_15_0.z)
	local slot_15_1 = arg_15_1:to2D()
	local slot_15_2 = false

	for iter_15_0 in ove_0_25(ove_0_52) do
		local slot_15_3 = ove_0_52[iter_15_0]

		if slot_15_3 then
			local slot_15_4 = ove_0_53[slot_15_3.spell.owner:spellSlot(1).level] / 2
			local slot_15_5 = slot_15_3 + (slot_15_3.startPos - slot_15_3):norm():perp2() * slot_15_4
			local slot_15_6 = slot_15_3 + (slot_15_3.startPos - slot_15_3):norm():perp2() * -slot_15_4
			local slot_15_7 = (slot_15_6:to2D() - slot_15_5:to2D()):norm():perp1()
			local slot_15_8 = slot_15_5:to2D() - slot_15_7 * 75
			local slot_15_9 = slot_15_5:to2D() + slot_15_7 * 75
			local slot_15_10 = slot_15_6:to2D() + slot_15_7 * 75
			local slot_15_11 = slot_15_6:to2D() - slot_15_7 * 75

			if ove_0_69(slot_15_8, slot_15_9, slot_15_0, slot_15_1) or ove_0_69(slot_15_8, slot_15_11, slot_15_0, slot_15_1) or ove_0_69(slot_15_10, slot_15_9, slot_15_0, slot_15_1) or ove_0_69(slot_15_10, slot_15_11, slot_15_0, slot_15_1) then
				return true
			end
		end
	end

	return slot_15_2
end

local ove_0_71 = {
	speed = 1400,
	delay = 0.25,
	boundingRadiusMod = 0,
	width = 80,
	collision = {
		minion = false,
		hero = false
	}
}
local ove_0_72 = {
	speed = 2000,
	delay = 0.4,
	boundingRadiusMod = 0,
	width = 80,
	collision = {
		minion = false,
		hero = true
	}
}

local function ove_0_73(arg_16_0, arg_16_1, arg_16_2)
	-- function 16
	if ove_0_40.trace.linear.hardlock(arg_16_0, arg_16_1, arg_16_2) then
		return true
	end

	if ove_0_40.trace.linear.hardlockmove(arg_16_0, arg_16_1, arg_16_2) then
		return true
	end

	if arg_16_2 and ove_0_62(arg_16_2) and player.pos:dist(arg_16_2.pos) < player.attackRange + player.boundingRadius + arg_16_2.boundingRadius then
		return true
	end

	if arg_16_1.startPos:dist(arg_16_1.endPos) < 600 and ove_0_40.trace.newpath(arg_16_2, 0.033, 0.65) then
		return true
	end

	if ove_0_40.trace.newpath(arg_16_2, 0.033, 0.85) then
		return true
	end
end

local function ove_0_74(arg_17_0, arg_17_1, arg_17_2)
	-- function 17
	if ove_0_40.trace.linear.hardlock(arg_17_0, arg_17_1, arg_17_2) then
		return true
	end

	if ove_0_40.trace.linear.hardlockmove(arg_17_0, arg_17_1, arg_17_2) then
		return true
	end

	if arg_17_2 and ove_0_62(arg_17_2) and player.pos:dist(arg_17_2.pos) < player.attackRange + player.boundingRadius + arg_17_2.boundingRadius then
		return true
	end

	if arg_17_1.startPos:dist(arg_17_1.endPos) < 600 and ove_0_40.trace.newpath(arg_17_2, 0.033, 0.65) then
		return true
	end

	if ove_0_40.trace.newpath(arg_17_2, 0.033, 0.95) then
		return true
	end
end

local function ove_0_75(arg_18_0)
	-- function 18
	if player:spellSlot(2).state ~= 0 or not arg_18_0 or not ove_0_62(arg_18_0) then
		return false
	end

	local slot_18_0 = ove_0_40.linear.get_prediction(ove_0_71, arg_18_0)

	if slot_18_0 and slot_18_0.endPos and slot_18_0.startPos:distSqr(slot_18_0.endPos) < 1102500 and ove_0_73(ove_0_71, slot_18_0, arg_18_0) and not ove_0_70(player, vec3(slot_18_0.endPos.x, arg_18_0.y, slot_18_0.endPos.y)) then
		ove_0_39.core.set_server_pause_attack()
		player:castSpell("pos", 2, vec3(slot_18_0.endPos.x, arg_18_0.y, slot_18_0.endPos.y))
	end
end

local function ove_0_76(arg_19_0)
	-- function 19
	if player:spellSlot(3).state ~= 0 or not arg_19_0 or not ove_0_62(arg_19_0) or arg_19_0.buff[BUFF_INVULNERABILITY] then
		return false
	end

	local slot_19_0
	local slot_19_1 = 0
	local slot_19_2 = ove_0_40.linear.get_prediction(ove_0_72, arg_19_0)

	if slot_19_2 and slot_19_2.endPos and slot_19_2.startPos:distSqr(slot_19_2.endPos) < 9000000 and ove_0_74(ove_0_72, slot_19_2, arg_19_0) and not ove_0_70(player, vec3(slot_19_2.endPos.x, arg_19_0.y, slot_19_2.endPos.y)) then
		slot_19_0 = vec3(slot_19_2.endPos.x, arg_19_0.y, slot_19_2.endPos.y)

		if ove_0_63() then
			local slot_19_3 = ove_0_40.collision.get_prediction(ove_0_72, slot_19_2)

			if slot_19_3 then
				slot_19_1 = #slot_19_3
			end
		end
	end

	return slot_19_0, slot_19_1
end

local function ove_0_77(arg_20_0)
	-- function 20
	if not arg_20_0 or not arg_20_0.pos then
		return true
	end

	for iter_20_0 = 0, objManager.turrets.size[TEAM_ENEMY] - 1 do
		local slot_20_0 = objManager.turrets[TEAM_ENEMY][iter_20_0]

		if slot_20_0 and slot_20_0.type == TYPE_TURRET and not slot_20_0.isDead and slot_20_0.team ~= TEAM_ALLY and slot_20_0.pos:distSqr(arg_20_0.pos) <= 810000 then
			return true
		end
	end

	return false
end

local function ove_0_78(arg_21_0)
	-- function 21
	local slot_21_0 = {
		"ward",
		"trink",
		"trap",
		"spear",
		"device",
		"room",
		"box",
		"plant",
		"poo",
		"barrel",
		"god",
		"feather"
	}

	for iter_21_0 = 1, #slot_21_0 do
		if arg_21_0 and arg_21_0.name:lower():find(slot_21_0[iter_21_0]) then
			return true
		end
	end
end

local ove_0_79

local function ove_0_80(arg_22_0, arg_22_1, arg_22_2)
	-- function 22
	if arg_22_1 and arg_22_1.isVisible and arg_22_2 then
		if ove_0_39.combat.target then
			arg_22_0.obj = ove_0_39.combat.target

			return true
		elseif player:spellSlot(2).state == 0 and arg_22_2 and arg_22_2 <= 1050 then
			arg_22_0.obj = arg_22_1

			return true
		elseif player:spellSlot(3).state == 0 and arg_22_2 and arg_22_2 <= 3000 then
			arg_22_0.obj = arg_22_1

			return true
		elseif arg_22_2 and arg_22_2 <= 1050 then
			arg_22_0.obj = arg_22_1

			return true
		end
	end
end

local function ove_0_81()
	-- function 23
	return ove_0_39.ts.get_result(ove_0_80).obj
end

local function ove_0_82(arg_24_0, arg_24_1, arg_24_2)
	-- function 24
	local slot_24_0 = ove_0_43()
	local slot_24_1 = arg_24_2 or 16
	local slot_24_2

	slot_24_2 = slot_24_1 and ove_0_16 / slot_24_1 or ove_0_16 / (arg_24_1 / 5)

	for iter_24_0 = 2 * ove_0_15, 0, -slot_24_2 do
		slot_24_0:Add(vec2(arg_24_0.x + arg_24_1 * ove_0_13(iter_24_0), arg_24_0.y - arg_24_1 * ove_0_14(iter_24_0)))
	end

	return slot_24_0
end

local function ove_0_83(arg_25_0, arg_25_1)
	-- function 25
	ove_0_47:Clear()

	local slot_25_0 = ove_0_82(arg_25_0.pos2D, ove_0_51 - 35)
	local slot_25_1 = ove_0_82(arg_25_1.pos2D, ove_0_51 - 35)

	ove_0_47:AddPath(slot_25_0, ove_0_46.PolyType.Clip, true)
	ove_0_47:AddPath(slot_25_1, ove_0_46.PolyType.Subject, true)

	local slot_25_2 = ove_0_47:Execute(ove_0_46.ClipType.Intersection, ove_0_46.PolyFillType.NonZero, ove_0_46.PolyFillType.EvenOdd)

	return slot_25_2:ChildCount() > 0 and slot_25_2:Childs(0), (arg_25_0.pos2D + arg_25_1.pos2D) / 2
end

local function ove_0_84()
	-- function 26
	if ove_0_50.n > 0 then
		for iter_26_0 = 1, #ove_0_50 do
			if ove_0_50[iter_26_0].obj then
				local slot_26_0 = ove_0_50[iter_26_0].obj
				local slot_26_1 = player.pos:dist(slot_26_0)

				if slot_26_0 and slot_26_1 and slot_26_1 <= 422500 and slot_26_0.pos:distSqr(mousePos) <= ove_0_36.misc.axerange:get()^2 and (ove_0_36.misc.Tower:get() or not ove_0_77(slot_26_0)) then
					return slot_26_0, slot_26_1
				end
			end
		end
	else
		return false, false
	end
end

local function ove_0_85(arg_27_0)
	-- function 27
	if ove_0_50.n > 0 then
		for iter_27_0 = 2, #ove_0_50 do
			if ove_0_50[iter_27_0].obj then
				local slot_27_0 = ove_0_50[iter_27_0].obj
				local slot_27_1 = player.pos:dist(slot_27_0)

				if slot_27_0 and arg_27_0 and slot_27_0 ~= arg_27_0 and slot_27_1 and slot_27_1 <= 422500 and slot_27_0.pos:distSqr(mousePos) <= ove_0_36.misc.axerange:get()^2 and (ove_0_36.misc.Tower:get() or not ove_0_77(slot_27_0)) then
					return slot_27_0, slot_27_1
				end
			end
		end
	else
		return false, false
	end
end

local function ove_0_86()
	-- function 28
	if ove_0_79 then
		return ove_0_79
	end

	local slot_28_0 = mousePos
	local slot_28_1, slot_28_2 = ove_0_84()

	if slot_28_1 then
		local slot_28_3 = slot_28_1.pos:dist(mousePos)

		if slot_28_3 > ove_0_51 - 35 then
			slot_28_0 = slot_28_1.pos:lerp(mousePos, (ove_0_51 - 35) / slot_28_3)
		end
	end

	return slot_28_0
end

local function ove_0_87()
	-- function 29
	if ove_0_79 then
		return ove_0_79
	end

	local slot_29_0
	local slot_29_1, slot_29_2 = ove_0_84()

	if slot_29_1 then
		local slot_29_3 = slot_29_1.pos:dist(mousePos)

		if slot_29_3 > ove_0_51 - 35 then
			slot_29_0 = slot_29_1.pos:lerp(mousePos, (ove_0_51 - 35) / slot_29_3)
		end
	end

	return slot_29_0, slot_29_1
end

local function ove_0_88()
	-- function 30
	if not ove_0_36.AutoAxe:get() then
		return
	end

	local slot_30_0, slot_30_1 = ove_0_87()

	if slot_30_0 and slot_30_1 then
		local slot_30_2 = slot_30_0 + (slot_30_1.pos - slot_30_0):norm():perp2() * 80
		local slot_30_3 = slot_30_0 + (slot_30_1.pos - slot_30_0):norm():perp2() * -80

		slot_30_0 = slot_30_2:dist(player.pos) > 65 and slot_30_2 or slot_30_3
	end

	local slot_30_4 = ove_0_39.combat.target

	if ove_0_39.menu.combat.key:get() and slot_30_0 and slot_30_4 and slot_30_4.health <= ove_0_64(slot_30_4) and slot_30_0:distSqr(slot_30_4.pos) > (player.attackRange + player.boundingRadius + slot_30_4.boundingRadius)^2 then
		slot_30_0 = nil
	end

	if ove_0_30 then
		for iter_30_0 = 1, #ove_0_30.core.skillshots do
			local slot_30_5 = ove_0_30.core.skillshots[iter_30_0]

			slot_30_5.name = slot_30_5.name:lower()

			if slot_30_5 and slot_30_5.owner and slot_30_5.owner.type == TYPE_HERO and (slot_30_5.polygon and slot_30_5.polygon:Contains(player.path.serverPos) ~= 0 or slot_30_5.contains and slot_30_5:contains(player)) and (not slot_30_5.data.collision or type(slot_30_5.data.collision) == "number" and slot_30_5.data.collision <= 1 or type(slot_30_5.data.collision) == "table" and #slot_30_5.data.collision == 0 or slot_30_5.name:find("gragase") or slot_30_5.name:find("gragasr") or slot_30_5.name:find("shene")) then
				local slot_30_6 = slot_30_5.data.speed ~= nil and slot_30_5.data.speed or 100000

				slot_30_6 = slot_30_6 ~= math.huge and slot_30_6 or 100000

				local slot_30_7 = slot_30_5.data.wind_up_time ~= nil and slot_30_5.data.wind_up_time or 0
				local slot_30_8

				slot_30_8 = slot_30_7 ~= math.huge and slot_30_7 or 0

				if (slot_30_5.name:find("poppyr") or slot_30_5.name == "ireliae2" or slot_30_5.data.spell_type ~= "Linear" or slot_30_5.data.spell_type == "Linear" and (not slot_30_5.missile and slot_30_5.owner and player.pos:dist(slot_30_5.owner) < 750 and (not slot_30_5.name:find("yasuoq3") or player.pos:dist(slot_30_5.owner) < 400) or slot_30_5.missile and player.pos:dist(slot_30_5.missile) < 750 or slot_30_5.start_pos and slot_30_5.start_time and math.max(1, slot_30_5.start_pos:dist(player.pos) - 500) / slot_30_6 <= game.time - slot_30_5.start_time)) and (ove_0_57(ove_0_58, slot_30_5.name) or slot_30_5.name:find("brandq") and player.buff[BUFF_COMBATDEHANCER] or slot_30_5.name:find("jhinw") and player.buff.jhinespotteddebuff) then
					slot_30_0 = nil
				end
			end
		end
	end

	return slot_30_0
end

local function ove_0_89()
	-- function 31
	ove_0_41 = ove_0_40.present.get_source_pos(player)

	local slot_31_0 = not player.buff.dravenfury and player.mana > 100 and player.moveSpeed * 1.5 or player.moveSpeed

	ove_0_50 = {
		n = 0
	}

	for iter_31_0 = 1, ove_0_49.n do
		local slot_31_1 = ove_0_49[iter_31_0]

		if slot_31_1.endTick - ove_0_24() > (slot_31_1.obj.pos2D:dist(ove_0_41) - 100) / slot_31_0 then
			table.insert(ove_0_50, slot_31_1)

			ove_0_50.n = ove_0_50.n + 1
		end
	end

	if ove_0_50.n >= 2 then
		local slot_31_2 = ove_0_84()
		local slot_31_3 = slot_31_2 and ove_0_85(slot_31_2)

		if slot_31_3 then
			local slot_31_4 = slot_31_2.pos2D:dist(slot_31_3.pos2D)

			if slot_31_4 <= (ove_0_51 - 40) * 2 then
				local slot_31_5, slot_31_6 = ove_0_83(slot_31_2, slot_31_3)

				if slot_31_5 and slot_31_5:Contains(player.pos2D) == 0 then
					local slot_31_7 = ove_0_38(slot_31_5, slot_31_6, mousePos2D)

					if slot_31_7 then
						ove_0_79 = vec3(slot_31_7.x, player.y, slot_31_7.y)
					end
				else
					ove_0_79 = nil
				end
			else
				local slot_31_8 = slot_31_2.pos2D:lerp(slot_31_3.pos2D, (ove_0_51 - 40) / slot_31_4)

				ove_0_79 = vec3(slot_31_8.x, player.y, slot_31_8.y)
			end
		else
			ove_0_79 = nil
		end
	else
		ove_0_79 = nil
	end
end

local function ove_0_90()
	-- function 32
	if ove_0_50.n > 0 and ove_0_50[1].obj.pos2D:dist(ove_0_41) > ove_0_51 then
		local slot_32_0 = ove_0_86()

		if ove_0_19(ove_0_50[1].endTick - ove_0_24(), slot_32_0:to2D():dist(ove_0_41) / player.moveSpeed) > 0.033 + player:basicAttack(0).windUpTime then
			return true
		end

		return false
	end

	return true
end

local function ove_0_91(arg_33_0)
	-- function 33
	if arg_33_0 and arg_33_0.name and arg_33_0.owner and arg_33_0.owner == player then
		if arg_33_0.name == "DravenRCast" then
			ove_0_56 = game.time
		end

		if arg_33_0.name:lower():find("attack") and arg_33_0.target then
			ove_0_54 = game.time
		end
	end

	if arg_33_0 and arg_33_0.owner and (arg_33_0.owner.team == TEAM_ALLY or arg_33_0.owner.type ~= TYPE_HERO) then
		return
	end

	if ove_0_36.misc.Interrupt:get() and player:spellSlot(2).state == 0 then
		if arg_33_0.name and arg_33_0.name:lower():find("lucianr") and arg_33_0.owner and not arg_33_0.owner.buff.lucianr and player.pos:distSqr(arg_33_0.owner.pos) <= 1102500 then
			ove_0_75(arg_33_0.owner)
		end

		if arg_33_0.name and arg_33_0.name == "MissFortuneBulletTime" and arg_33_0.owner and not arg_33_0.owner.buff.missfortunebulletsound and player.pos:distSqr(arg_33_0.owner.pos) <= 1102500 then
			ove_0_75(arg_33_0.owner)
		end

		if arg_33_0.name and arg_33_0.name == "Crowstorm" and arg_33_0.owner and not arg_33_0.owner.buff.crowstorm and player.pos:distSqr(arg_33_0.owner.pos) <= 1102500 then
			ove_0_75(arg_33_0.owner)
		end
	end

	if not ove_0_36.misc.Enable:get() then
		return
	end

	if arg_33_0.name and player:spellSlot(2).state == 0 and arg_33_0.name:lower():find("rivenmartyr") and arg_33_0.owner and player.pos:distSqr(arg_33_0.owner.pos) <= 1102500 then
		ove_0_75(arg_33_0.owner)
	end

	if (arg_33_0.target and arg_33_0.target == player or arg_33_0.owner and arg_33_0.owner.pos:distSqr(player.path.serverPos) < 1102500 and arg_33_0.endPos and arg_33_0.endPos:distSqr(player.path.serverPos) <= 90000) and ove_0_36.misc[arg_33_0.name] and ove_0_36.misc[arg_33_0.name]:get() and player:spellSlot(2).state == 0 then
		ove_0_75(arg_33_0.owner)
	end
end

local function ove_0_92()
	-- function 34
	if not ove_0_28 or not ove_0_62(ove_0_28) then
		return
	end

	if ove_0_36.combo.q:get() and player:spellSlot(0).state == 0 and ove_0_39.core.can_action() and (ove_0_48 < 2 or ove_0_48 < 3 and player.attackSpeedMod > 2.2 and ove_0_54 > game.time - 0.1) and ove_0_39.combat.target then
		player:castSpell("self", 0)
	end

	if ove_0_36.combo.w:get() and player:spellSlot(1).state == 0 and ove_0_39.core.can_action() and player.mana > 100 and ove_0_48 > 1 and (ove_0_28.health <= ove_0_64(ove_0_28) * 4 and player.pos:distSqr(ove_0_28) < (player.attackRange + 200 + player.boundingRadius + ove_0_28.boundingRadius)^2 or ove_0_39.combat.target) and not player.buff.dravenfury then
		player:castSpell("self", 1)
	end

	if ove_0_36.combo.e:get() and player:spellSlot(2).state == 0 and ove_0_39.core.can_action() and (ove_0_48 > 1 or player.mana > 120) and (ove_0_28.health <= ove_0_64(ove_0_28) * 4 and not ove_0_77(ove_0_28) or player.pos:distSqr(ove_0_28) <= 202500) then
		ove_0_75(ove_0_28)
	end

	if ove_0_36.combo.r:get() and player:spellSlot(3).state == 0 then
		local slot_34_0, slot_34_1 = ove_0_76(ove_0_28)
		local slot_34_2 = ove_0_39.combat.target and ove_0_39.combat.target == ove_0_28 and ove_0_66(ove_0_28) * 2 or ove_0_66(ove_0_28)

		if slot_34_0 and ove_0_63() and (slot_34_2 >= ove_0_28.health or ove_0_36.combo.rhit:get() > 1 and slot_34_1 and slot_34_1 >= ove_0_36.combo.rhit:get()) and ove_0_39.core.can_action() then
			player:castSpell("pos", 3, slot_34_0)
		end

		if slot_34_0 and not ove_0_63() and ove_0_56 + (player.pos:dist(slot_34_0) / 2000 + 0.4) < game.time then
			player:castSpell("pos", 3, slot_34_0)
		end
	end
end

local function ove_0_93()
	-- function 35
	if not ove_0_28 or not ove_0_62(ove_0_28) then
		return
	end

	if player.mana * 100 / player.maxMana < ove_0_36.harass.minimana:get() then
		return
	end

	if ove_0_36.harass.q:get() and player:spellSlot(0).state == 0 and ove_0_39.core.can_action() and (ove_0_48 < 2 or ove_0_48 < 3 and player.attackSpeedMod > 2.2 and ove_0_54 > game.time - 0.1) and ove_0_39.combat.target then
		player:castSpell("self", 0)
	end

	if ove_0_36.harass.w:get() and player:spellSlot(1).state == 0 and ove_0_39.core.can_action() and player.mana > 100 and ove_0_48 > 1 and (ove_0_28.health <= ove_0_64(ove_0_28) * 4 and player.pos:distSqr(ove_0_28) < (player.attackRange + 200 + player.boundingRadius + ove_0_28.boundingRadius)^2 or ove_0_39.combat.target) and not player.buff.dravenfury then
		player:castSpell("self", 1)
	end

	if ove_0_36.harass.e:get() and player:spellSlot(2).state == 0 and ove_0_39.core.can_action() and (ove_0_48 > 1 or player.mana > 120) and (ove_0_28.health <= ove_0_64(ove_0_28) * 4 and not ove_0_77(ove_0_28) or player.pos:distSqr(ove_0_28) <= 202500) then
		ove_0_75(ove_0_28)
	end
end

local function ove_0_94()
	-- function 36
	if player.mana * 100 / player.maxMana < ove_0_36.laneclear.minimana:get() then
		return
	end

	local slot_36_0 = ove_0_36.laneclear.qhit:get()
	local slot_36_1 = objManager.minions

	for iter_36_0 = 0, slot_36_1.size[TEAM_NEUTRAL] - 1 do
		local slot_36_2 = slot_36_1[TEAM_NEUTRAL][iter_36_0]

		if slot_36_2 and not slot_36_2.isDead and slot_36_2.isVisible and slot_36_2.isTargetable and slot_36_2.pos:distSqr(player.pos) < (player.attackRange + player.boundingRadius + slot_36_2.boundingRadius)^2 and not ove_0_78(slot_36_2) then
			if slot_36_0 < 3 then
				if ove_0_36.laneclear.q:get() and player:spellSlot(0).state == 0 and ove_0_39.core.can_action() and slot_36_0 > ove_0_48 then
					player:castSpell("self", 0)
				end
			elseif ove_0_36.laneclear.q:get() and player:spellSlot(0).state == 0 and ove_0_39.core.can_action() and (ove_0_48 < 2 or ove_0_48 < 3 and player.attackSpeedMod > 2.2 and ove_0_54 > game.time - 0.1) then
				player:castSpell("self", 0)
			end
		end
	end

	for iter_36_1 = 0, slot_36_1.size[TEAM_ENEMY] - 1 do
		local slot_36_3 = slot_36_1[TEAM_ENEMY][iter_36_1]

		if slot_36_3 and not slot_36_3.isDead and slot_36_3.isVisible and slot_36_3.isTargetable and slot_36_3.pos:distSqr(player.pos) < (player.attackRange + player.boundingRadius + slot_36_3.boundingRadius)^2 and not ove_0_78(slot_36_3) then
			if slot_36_0 < 3 then
				if ove_0_36.laneclear.q:get() and player:spellSlot(0).state == 0 and ove_0_39.core.can_action() and slot_36_0 > ove_0_48 then
					player:castSpell("self", 0)
				end
			elseif ove_0_36.laneclear.q:get() and player:spellSlot(0).state == 0 and ove_0_39.core.can_action() and (ove_0_48 < 2 or ove_0_48 < 3 and player.attackSpeedMod > 2.2 and ove_0_54 > game.time - 0.1) then
				player:castSpell("self", 0)
			end
		end
	end
end

local function ove_0_95()
	-- function 37
	for iter_37_0 = 0, objManager.enemies_n - 1 do
		local slot_37_0 = objManager.enemies[iter_37_0]

		if slot_37_0 and ove_0_62(slot_37_0) and slot_37_0.team ~= TEAM_ALLY and slot_37_0.pos:distSqr(player.pos) < 9000000 then
			if ove_0_36.kill.enable:get() and not slot_37_0.buff.undyingrage and not slot_37_0.buff.sionpassivezombie then
				if ove_0_36.kill.e:get() and player:spellSlot(2).state == 0 and slot_37_0.health <= ove_0_65(slot_37_0) and slot_37_0.pos:distSqr(player.pos) <= 1102500 then
					ove_0_75(slot_37_0)
				end

				if ove_0_36.kill.r:get() and player:spellSlot(3).state == 0 and slot_37_0.health + slot_37_0.physicalShield <= ove_0_66(slot_37_0) and slot_37_0.pos:distSqr(player.pos) <= 9000000 then
					local slot_37_1 = ove_0_76(slot_37_0)

					if slot_37_1 and ove_0_63() then
						player:castSpell("pos", 3, slot_37_1)
					end

					if slot_37_1 and not ove_0_63() and ove_0_56 + (player.pos:dist(slot_37_1) / 2000 + 0.4) < game.time then
						player:castSpell("pos", 3, slot_37_1)
					end
				end
			end

			if ove_0_39.menu.combat.key:get() and ove_0_36.combo.e:get() and player:spellSlot(2).state == 0 and slot_37_0.pos:distSqr(player.pos) < 1102500 then
				local function slot_37_2(arg_38_0, arg_38_1)
					-- function 38
					return (arg_38_0.pos - arg_38_0.direction:norm() * arg_38_1):to2D()
				end

				local slot_37_3 = math.abs(math.deg(mathf.angle_between(slot_37_0.pos2D, player.pos2D, slot_37_2(slot_37_0, 100))))

				if slot_37_3 and slot_37_3 > 120 and (player.health / player.maxHealth <= 0.6 or slot_37_0.buff.udyrbearstance) and slot_37_0.attackRange < 400 and player.pos:distSqr(slot_37_0.path.serverPos) <= (slot_37_0.attackRange + slot_37_0.boundingRadius + player.boundingRadius)^2 then
					ove_0_75(slot_37_0)
				end

				if slot_37_0.path.serverPos:distSqr(player.path.serverPos) <= 90000 and slot_37_0.buff.jaxcounterstrike and game.time > slot_37_0.buff.jaxcounterstrike.startTime + 1 - network.latency / 2 then
					ove_0_75(slot_37_0)
				end

				if slot_37_0.path.serverPos:distSqr(player.path.serverPos) <= 160000 and slot_37_0.buff.warwicke and game.time > slot_37_0.buff.warwicke.startTime + 1 then
					ove_0_75(slot_37_0)
				end

				if slot_37_0.path.serverPos:distSqr(player.path.serverPos) <= 250000 and (slot_37_0.buff.galiow and game.time > slot_37_0.buff.galiow.startTime + 1.75 or slot_37_0.buff.galiowbuff and game.time > slot_37_0.buff.galiowbuff.startTime + 1.75) then
					ove_0_75(slot_37_0)
				end
			end

			if ove_0_36.misc.Interrupt:get() and player:spellSlot(2).state == 0 and slot_37_0.buff and slot_37_0.pos:distSqr(player.pos) < 1102500 then
				if slot_37_0.buff.fearmonger_marker and game.time - slot_37_0.buff.fearmonger_marker.startTime <= 3.5 then
					ove_0_75(slot_37_0)
				end

				if slot_37_0.buff.reapthewhirlwind and game.time - slot_37_0.buff.reapthewhirlwind.startTime <= 1.5 then
					ove_0_75(slot_37_0)
				end

				if slot_37_0.buff.karthusfallenonecastsound then
					ove_0_75(slot_37_0)
				end

				if slot_37_0.buff.absolutezero then
					ove_0_75(slot_37_0)
				end

				if slot_37_0.buff.katarinarsound and game.time - slot_37_0.buff.katarinarsound.startTime <= 2 then
					ove_0_75(slot_37_0)
				end
			end
		end
	end
end

local function ove_0_96()
	-- function 39
	if ove_0_36.combo.autor:get() then
		if not ove_0_39.menu.combat.key:get() and not ove_0_39.menu.hybrid.key:get() then
			local slot_39_0 = player.path.serverPos:lerp(mousePos, 500 / player.path.serverPos:dist(mousePos))

			player:move(slot_39_0)
		end

		if player:spellSlot(3).state == 0 and ove_0_28 and ove_0_62(ove_0_28) then
			local slot_39_1 = ove_0_76(ove_0_28)

			if slot_39_1 and ove_0_63() and ove_0_39.core.can_action() then
				player:castSpell("pos", 3, slot_39_1)
			end

			if slot_39_1 and not ove_0_63() and ove_0_56 + (player.pos:dist(slot_39_1) / 2000 + 0.4) < game.time then
				player:castSpell("pos", 3, slot_39_1)
			end
		end
	end
end

local function ove_0_97()
	-- function 40
	ove_0_89()

	ove_0_28 = ove_0_81()

	if ove_0_36.misc.autow:get() then
		local slot_40_0 = not player.buff.dravenfury and player.moveSpeed or 0
		local slot_40_1 = player.buff.dravenfury and player.moveSpeed or player.moveSpeed * 1.5
		local slot_40_2, slot_40_3 = ove_0_84()

		if slot_40_3 and game.time <= ove_0_55 + 1.5 and ove_0_39.core.can_action() and game.time > ove_0_55 + 0.5 and slot_40_0 > 0 and slot_40_3 <= (game.time - ove_0_55) * slot_40_1 + 85 and slot_40_3 > (game.time - ove_0_55) * slot_40_0 + 85 and player.mana > 100 then
			player:castSpell("self", 1)
		end
	end

	if player.isDead then
		ove_0_48 = 0
	end

	if player.buff.dravenspinningattack and game.time > ove_0_54 + 1.5 then
		ove_0_48 = player.buff.dravenspinningattack.stacks
	end

	if ove_0_36.kill.enable:get() then
		ove_0_95()
	end

	if ove_0_39.menu.combat.key:get() then
		ove_0_92()
	elseif ove_0_39.menu.hybrid.key:get() then
		ove_0_93()
	elseif ove_0_39.menu.lane_clear.key:get() then
		ove_0_94()
	end

	ove_0_96()
end

local ove_0_98 = {}

local function ove_0_99(arg_41_0)
	-- function 41
	if arg_41_0.name and arg_41_0.name:lower():find("draven") then
		if arg_41_0.name:find("spinning_buff_end_sound") then
			ove_0_48 = 0
		elseif arg_41_0.name:find("Q_ReticleCatchSuccess") then
			if ove_0_48 > 3 then
				return
			end

			ove_0_61(function()
				-- function 42
				ove_0_48 = ove_0_48 + 1
			end, 0.1)
		elseif arg_41_0.name:find("Q_activation") then
			if ove_0_48 >= 3 then
				return
			end

			ove_0_48 = ove_0_48 + 1
		elseif arg_41_0.name:find("Q_reticle_self") then
			ove_0_98[arg_41_0.ptr] = arg_41_0.ptr
			ove_0_55 = game.time

			table.insert(ove_0_49, {
				obj = arg_41_0,
				endTick = ove_0_24() + 1.4
			})

			ove_0_49.n = ove_0_49.n + 1
		end
	end
end

local function ove_0_100(arg_43_0)
	-- function 43
	if arg_43_0 and arg_43_0.ptr and ove_0_98[arg_43_0.ptr] then
		ove_0_98[arg_43_0.ptr] = nil

		ove_0_61(function()
			-- function 44
			if ove_0_48 > 0 then
				ove_0_48 = ove_0_48 - 1
			end
		end, 0.2)

		for iter_43_0, iter_43_1 in ove_0_26(ove_0_49) do
			if iter_43_1.obj.ptr == arg_43_0.ptr then
				table.remove(ove_0_49, iter_43_0)

				ove_0_49.n = ove_0_49.n - 1

				break
			end
		end
	end
end

local function ove_0_101(arg_45_0)
	-- function 45
	if arg_45_0 and arg_45_0.spell and arg_45_0.spell.owner and arg_45_0.spell.owner.team ~= TEAM_ALLY and arg_45_0.spell.name == "YasuoW_VisualMis" then
		ove_0_52[arg_45_0.ptr] = arg_45_0
	end
end

local function ove_0_102(arg_46_0)
	-- function 46
	if arg_46_0 and arg_46_0.ptr and ove_0_52[arg_46_0.ptr] then
		ove_0_52[arg_46_0.ptr] = nil
	end
end

local function ove_0_103()
	-- function 47
	if player.isDead then
		return
	end

	if ove_0_36.display.Target:get() and ove_0_28 and ove_0_28.isVisible and not ove_0_28.isDead and ove_0_28.isOnScreen then
		graphics.draw_circle_xyz(ove_0_28.x, ove_0_28.y, ove_0_28.z, 100, 4, 4294651695, 64)
	end

	local slot_47_0 = ove_0_84()

	if ove_0_36.display.axe:get() and slot_47_0 and slot_47_0.isOnScreen then
		graphics.draw_circle(vec3(slot_47_0.x, slot_47_0.y, slot_47_0.z), 100, 2, 4286381056, 64)
	end

	if ove_0_36.display.axerange:get() then
		graphics.draw_circle(mousePos, ove_0_36.misc.axerange:get(), 2, 4294967295, 64)
	end

	if ove_0_36.display.e:get() and player.isOnScreen then
		if player:spellSlot(2).state == 0 then
			graphics.draw_circle_xyz(player.x, player.y, player.z, 1050, 2, 4282094319, 64)
		else
			graphics.draw_circle_xyz(player.x, player.y, player.z, 1050, 2, 4287071366, 64)
		end
	end

	if ove_0_36.display.State:get() then
		local function slot_47_1()
			-- function 48
			if ove_0_36.AutoAxe:get() then
				return "On"
			else
				return "Off"
			end
		end

		graphics.draw_text_2D("Auto Catch Axe:" .. slot_47_1(), 14, graphics.width / 1.35, graphics.height - 100, ove_0_36.AutoAxe:get() and 4290838334 or 4294967295)

		local function slot_47_2()
			-- function 49
			if ove_0_36.misc.Tower:get() then
				return "On"
			else
				return "Off"
			end
		end

		graphics.draw_text_2D("Axe Under Turret:" .. slot_47_2(), 14, graphics.width / 1.35, graphics.height - 50, ove_0_36.misc.Tower:get() and 4294651695 or 4294967295)
	end

	if ove_0_36.display.Combo:get() then
		for iter_47_0 = 0, objManager.enemies_n - 1 do
			local slot_47_3 = objManager.enemies[iter_47_0]

			if slot_47_3 and slot_47_3.isVisible and not slot_47_3.isDead and slot_47_3.team and slot_47_3.type and slot_47_3.team == TEAM_ENEMY and slot_47_3.type == TYPE_HERO and slot_47_3.isOnScreen and slot_47_3.barPos then
				local slot_47_4 = graphics.height > 1080 and graphics.height / 1080 * 0.905 or 1
				local slot_47_5 = vec2(109 * slot_47_4, 111 * slot_47_4)
				local slot_47_6 = vec2(54 * slot_47_4, 11 * slot_47_4)
				local slot_47_7 = slot_47_3.barPos + slot_47_5 + slot_47_6
				local slot_47_8 = slot_47_7.x
				local slot_47_9 = slot_47_7.y
				local slot_47_10 = player:spellSlot(2).state == 0 and ove_0_65(slot_47_3) or 0
				local slot_47_11 = player:spellSlot(3).state == 0 and ove_0_66(slot_47_3) or 0
				local slot_47_12 = slot_47_3.health - (ove_0_64(slot_47_3) + slot_47_10 + slot_47_11)
				local slot_47_13 = slot_47_8 + slot_47_3.health / slot_47_3.maxHealth * 102 * slot_47_4
				local slot_47_14 = slot_47_8 + (slot_47_12 > 0 and slot_47_12 or 0) / slot_47_3.maxHealth * 102 * slot_47_4

				graphics.draw_line_2D(slot_47_13, slot_47_9, slot_47_14, slot_47_9, 10, **********)
			end
		end
	end
end

local function ove_0_104(arg_50_0, arg_50_1, arg_50_2, arg_50_3)
	-- function 50
	if arg_50_3 and arg_50_0 == 2 then
		local slot_50_0 = ove_0_88()

		if slot_50_0 then
			arg_50_1.x = slot_50_0.x
			arg_50_1.z = slot_50_0.z
		end
	end
end

cb.add(cb.tick, ove_0_97)
cb.add(cb.spell, ove_0_91)
cb.add(cb.create_missile, ove_0_101)
cb.add(cb.delete_missile, ove_0_102)
cb.add(cb.create_particle, ove_0_99)
cb.add(cb.delete_particle, ove_0_100)
cb.add(cb.draw, ove_0_103)
cb.add(cb.issueorder, ove_0_104)

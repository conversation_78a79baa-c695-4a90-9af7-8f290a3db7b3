local ove_0_0 = module.internal("pred")
local ove_0_1 = {}

for iter_0_0 = 0, objManager.maxObjects - 1 do
	local ove_0_2 = objManager.get(iter_0_0)

	if ove_0_2 and ove_0_2.type == TYPE_TURRET and not ove_0_2.isDead then
		ove_0_1[#ove_0_1 + 1] = ove_0_2
	end
end

local ove_0_3 = {}
local ove_0_4

local function ove_0_5(arg_1_0, arg_1_1, arg_1_2)
	if not ove_0_4 then
		function ove_0_4()
			for iter_2_0, iter_2_1 in pairs(ove_0_3) do
				if iter_2_0 <= os.clock() then
					for iter_2_2 = 1, #iter_2_1 do
						local slot_2_0 = iter_2_1[iter_2_2]

						if slot_2_0 and slot_2_0.func then
							slot_2_0.func(unpack(slot_2_0.args or {}))
						end
					end

					ove_0_3[iter_2_0] = nil
				end
			end
		end

		cb.add(cb.tick, ove_0_4)
	end

	local slot_1_0 = os.clock() + (arg_1_1 or 0)

	if ove_0_3[slot_1_0] then
		ove_0_3[slot_1_0][#ove_0_3[slot_1_0] + 1] = {
			func = arg_1_0,
			args = arg_1_2
		}
	else
		ove_0_3[slot_1_0] = {
			{
				func = arg_1_0,
				args = arg_1_2
			}
		}
	end
end

local function ove_0_6(arg_3_0, arg_3_1)
	if not arg_3_0 then
		return
	end

	for iter_3_0 = 0, arg_3_0.buffManager.count - 1 do
		local slot_3_0 = arg_3_0.buffManager:get(iter_3_0)

		if slot_3_0 and slot_3_0.valid and slot_3_0.endTime >= game.time and slot_3_0.stacks > 0 then
			if type(arg_3_1) == "number" and slot_3_0.type == arg_3_1 then
				return slot_3_0
			elseif type(arg_3_1) == "string" and slot_3_0.name:lower() == arg_3_1 then
				return slot_3_0
			end
		end
	end
end

local function ove_0_7(arg_4_0)
	if player:spellSlot(0).level == 0 or player:spellSlot(0).state ~= 0 then
		return 0
	end

	local slot_4_0 = {
		80,
		120,
		160,
		200,
		240
	}
	local slot_4_1 = player:spellSlot(0).level
	local slot_4_2 = player.flatMagicDamageMod
	local slot_4_3 = 100 / (100 + arg_4_0.spellBlock)

	return (slot_4_0[slot_4_1] + slot_4_2 * 0.5) * slot_4_3
end

local function ove_0_8(arg_5_0)
	if player:spellSlot(1).level == 0 or player:spellSlot(1).state ~= 0 then
		return 0
	end

	local slot_5_0 = {
		65,
		95,
		125,
		155,
		185
	}
	local slot_5_1 = player:spellSlot(1).level
	local slot_5_2 = player.flatMagicDamageMod
	local slot_5_3 = 100 / (100 + arg_5_0.spellBlock)

	return (slot_5_0[slot_5_1] + slot_5_2 * 0.4) * slot_5_3
end

local function ove_0_9(arg_6_0)
	if player:spellSlot(3).level == 0 then
		return 0
	end

	local slot_6_0 = {
		250,
		400,
		550
	}
	local slot_6_1 = player:spellSlot(3).level
	local slot_6_2 = player.flatMagicDamageMod
	local slot_6_3 = 100 / (100 + arg_6_0.spellBlock)

	return (slot_6_0[slot_6_1] + slot_6_2) * slot_6_3
end

local function ove_0_10(arg_7_0)
	local slot_7_0 = player.baseAttackDamage + player.flatPhysicalDamageMod
	local slot_7_1 = player.buff.threshpassivesoulsgain
	local slot_7_2 = player.buff.threshepassive4
	local slot_7_3 = player.buff.threshepassive3
	local slot_7_4 = player.buff.threshepassive2
	local slot_7_5 = player.buff.threshepassive1
	local slot_7_6 = slot_7_1 and slot_7_1.stacks2 or 0
	local slot_7_7 = 100 / (100 + arg_7_0.armor)
	local slot_7_8 = 100 / (100 + arg_7_0.spellBlock)
	local slot_7_9 = slot_7_0 * slot_7_7
	local slot_7_10 = player:spellSlot(2).level
	local slot_7_11 = {
		0.8,
		1.1,
		1.4,
		1.7,
		2
	}

	if slot_7_10 > 0 then
		if slot_7_2 then
			slot_7_9 = slot_7_9 + (slot_7_0 * slot_7_11[slot_7_10] + slot_7_6) * slot_7_8
		end

		if slot_7_3 then
			local slot_7_12 = (game.time - slot_7_3.startTime - 5) / 10 + 1

			slot_7_9 = slot_7_9 + (slot_7_0 * slot_7_11[slot_7_10] * slot_7_12 + slot_7_6) * slot_7_8
		end

		if slot_7_4 then
			local slot_7_13 = (game.time - slot_7_4.startTime - 2.5) / 10 + 1
		end

		if slot_7_5 then
			slot_7_9 = slot_7_9 + slot_7_6 * ((game.time - slot_7_5.startTime) / 10 + 1) * slot_7_8
		end
	end

	return slot_7_9
end

local function ove_0_11(arg_8_0)
	for iter_8_0 = 1, #ove_0_1 do
		local slot_8_0 = ove_0_1[iter_8_0]

		if slot_8_0 and slot_8_0.type == TYPE_TURRET and not slot_8_0.isDead and slot_8_0.team == TEAM_ALLY and slot_8_0.pos:distSqr(arg_8_0) <= 810000 then
			return true, slot_8_0
		end
	end

	return false, nil
end

local function ove_0_12(arg_9_0)
	for iter_9_0 = 1, #ove_0_1 do
		local slot_9_0 = ove_0_1[iter_9_0]

		if slot_9_0 and slot_9_0.type == TYPE_TURRET and not slot_9_0.isDead and slot_9_0.team ~= TEAM_ALLY and slot_9_0.pos:distSqr(arg_9_0) <= 810000 then
			return true
		end
	end

	return false
end

local function ove_0_13(arg_10_0, arg_10_1)
	local slot_10_0 = 0

	for iter_10_0 = 0, objManager.enemies_n - 1 do
		local slot_10_1 = objManager.enemies[iter_10_0]

		if slot_10_1 and not slot_10_1.isDead and slot_10_1.isVisible and slot_10_1.isTargetable and slot_10_1.team ~= TEAM_ALLY and slot_10_1.pos:distSqr(arg_10_0) < arg_10_1^2 then
			slot_10_0 = slot_10_0 + 1
		end
	end

	return slot_10_0
end

local function ove_0_14(arg_11_0, arg_11_1)
	local slot_11_0 = 0

	for iter_11_0 = 0, objManager.allies_n - 1 do
		local slot_11_1 = objManager.allies[iter_11_0]

		if slot_11_1 and slot_11_1 ~= player and not slot_11_1.isDead and slot_11_1.isVisible and slot_11_1.isTargetableToTeamFlags and slot_11_1.pos:distSqr(arg_11_0) <= arg_11_1^2 then
			slot_11_0 = slot_11_0 + 1
		end
	end

	return slot_11_0
end

local function ove_0_15(arg_12_0)
	local slot_12_0 = {}

	for iter_12_0 = 0, objManager.allies_n - 1 do
		local slot_12_1 = objManager.allies[iter_12_0]

		if slot_12_1 and slot_12_1 ~= player and not slot_12_1.isDead and slot_12_1.isVisible and slot_12_1.isTargetableToTeamFlags and slot_12_1.pos:distSqr(arg_12_0) <= 1690000 then
			slot_12_0[#slot_12_0 + 1] = slot_12_1
		end
	end

	table.sort(slot_12_0, function(arg_13_0, arg_13_1)
		if arg_13_0.health and arg_13_1.health then
			return arg_13_0.health < arg_13_1.health
		end
	end)

	if slot_12_0[1] then
		return slot_12_0[1]
	end
end

local function ove_0_16(arg_14_0)
	local slot_14_0 = {}

	for iter_14_0 = 0, objManager.allies_n - 1 do
		local slot_14_1 = objManager.allies[iter_14_0]

		if slot_14_1 and slot_14_1 ~= player and not slot_14_1.isDead and slot_14_1.isVisible and slot_14_1.isTargetableToTeamFlags and slot_14_1.pos:distSqr(arg_14_0) <= 1690000 then
			slot_14_0[#slot_14_0 + 1] = slot_14_1
		end
	end

	table.sort(slot_14_0, function(arg_15_0, arg_15_1)
		if arg_15_0.health and arg_15_1.health then
			return arg_15_0.health > arg_15_1.health
		end
	end)

	if slot_14_0[1] then
		return slot_14_0[1]
	end
end

local function ove_0_17(arg_16_0, arg_16_1, arg_16_2)
	local slot_16_0 = 0

	for iter_16_0 = 0, objManager.enemies_n - 1 do
		local slot_16_1 = objManager.enemies[iter_16_0]

		if slot_16_1 and slot_16_1 ~= arg_16_2 and not slot_16_1.isDead and slot_16_1.isVisible and slot_16_1.isTargetable and slot_16_1.team ~= TEAM_ALLY and slot_16_1.pos:distSqr(arg_16_0) < arg_16_1^2 then
			slot_16_0 = slot_16_0 + 1
		end
	end

	return slot_16_0
end

local function ove_0_18(arg_17_0, arg_17_1)
	local slot_17_0 = 0

	for iter_17_0 = 0, objManager.enemies_n - 1 do
		local slot_17_1 = objManager.enemies[iter_17_0]

		if slot_17_1 and not slot_17_1.isDead and slot_17_1.isVisible and slot_17_1.isTargetable and slot_17_1.team ~= TEAM_ALLY and slot_17_1.pos2D:distSqr(arg_17_0) < arg_17_1^2 then
			slot_17_0 = slot_17_0 + 1
		end
	end

	return slot_17_0
end

local function ove_0_19(arg_18_0)
	if arg_18_0 and not arg_18_0.isDead and arg_18_0.isTargetable and not arg_18_0.buff.fioraw and not arg_18_0.buff.sivire and arg_18_0.isVisible then
		return true
	end
end

local function ove_0_20()
	return player:spellSlot(0).name:lower() == "threshq"
end

local function ove_0_21(arg_20_0)
	local slot_20_0 = {
		"ward",
		"trink",
		"trap",
		"spear",
		"device",
		"room",
		"box",
		"plant",
		"poo",
		"barrel",
		"god",
		"feather"
	}

	for iter_20_0 = 1, #slot_20_0 do
		if arg_20_0 and arg_20_0.name:lower():find(slot_20_0[iter_20_0]) then
			return true
		end
	end
end

return {
	havebuff = ove_0_6,
	AADmg = ove_0_10,
	QDmg = ove_0_7,
	EDmg = ove_0_8,
	RDmg = ove_0_9,
	CountEnemiesNear = ove_0_13,
	CountEnemiesNear2 = ove_0_17,
	CountEnemiesNear3 = ove_0_18,
	UnderTurret = ove_0_12,
	UnderTurret2 = ove_0_11,
	DelayAction = ove_0_5,
	isValid = ove_0_19,
	WardName = ove_0_21,
	HaveQ = ove_0_20,
	AllyNear = ove_0_14,
	AllyNear2 = ove_0_16,
	AllyNear3 = ove_0_15
}

math.randomseed(0.035737)

local ove_0_0

ove_0_0 = {
	function(...)
		-- function 1
		local slot_1_0 = {
			...
		}

		return ove_0_0[8](slot_1_0)
	end,
	cb,
	game,
	math.frexp,
	math.random(1, 1000),
	string.dump,
	string.sub,
	table.concat,
	cb,
	"bot",
	loadstring,
	"b+",
	function(arg_2_0)
		-- function 2
		local slot_2_0 = loadstring(arg_2_0)

		if slot_2_0 then
			return ove_0_0[tonumber("20")](function()
				-- function 3
				slot_2_0()
			end)
		else
			return nil
		end
	end,
	"load",
	1234567890,
	getfenv,
	"",
	"jit",
	0.1,
	pcall,
	math.pi,
	""
}

local ove_0_1 = ove_0_0[5]

local function ove_0_2(arg_4_0)
	-- function 4
	return tostring(arg_4_0 / ove_0_1)
end

local ove_0_3 = {
	ove_0_2(13298),
	ove_0_2(19300),
	ove_0_2(14962),
	ove_0_2(27850),
	ove_0_2(14377),
	ove_0_2(19466),
	ove_0_2(5848),
	ove_0_2(10696),
	ove_0_2(17115),
	ove_0_2(15919),
	ove_0_2(1022),
	ove_0_2(10462),
	ove_0_2(15942),
	ove_0_2(32036),
	ove_0_2(25020),
	ove_0_2(13584),
	ove_0_2(18863),
	ove_0_2(27179),
	ove_0_2(3866),
	ove_0_2(26836),
	ove_0_2(12036),
	ove_0_2(7052),
	ove_0_2(11542),
	ove_0_2(30149),
	ove_0_2(23489),
	ove_0_2(485),
	ove_0_2(32418),
	ove_0_2(5275),
	ove_0_2(24848),
	ove_0_2(30658),
	ove_0_2(2541),
	ove_0_2(12635),
	ove_0_2(16180),
	ove_0_2(28525),
	ove_0_2(24291),
	ove_0_2(10187),
	ove_0_2(39),
	ove_0_2(13064),
	ove_0_2(9975),
	ove_0_2(9257),
	ove_0_2(25986),
	ove_0_2(27352),
	ove_0_2(25120)
}
local ove_0_4, ove_0_5 = ove_0_0[9].get(2)

if not ove_0_5 then
	return
end

local ove_0_6 = {}

local function ove_0_7(arg_5_0)
	-- function 5
	if type(arg_5_0) ~= "table" then
		return type(arg_5_0)
	end

	local slot_5_0 = 1

	for iter_5_0 in pairs(arg_5_0) do
		if arg_5_0[slot_5_0] ~= nil then
			slot_5_0 = slot_5_0 + 1
		else
			return "table"
		end
	end

	if slot_5_0 == 1 then
		return "table"
	else
		return "array"
	end
end

local function ove_0_8(arg_6_0)
	-- function 6
	local slot_6_0 = {
		"\\",
		"\"",
		"/",
		"\b",
		"\f",
		"\n",
		"\r",
		"\t"
	}
	local slot_6_1 = {
		"\\",
		"\"",
		"/",
		"b",
		"f",
		"n",
		"r",
		"t"
	}

	for iter_6_0, iter_6_1 in ipairs(slot_6_0) do
		arg_6_0 = arg_6_0:gsub(iter_6_1, "\\" .. slot_6_1[iter_6_0])
	end

	return arg_6_0
end

local function ove_0_9(arg_7_0, arg_7_1, arg_7_2, arg_7_3)
	-- function 7
	arg_7_1 = arg_7_1 + #arg_7_0:match("^%s*", arg_7_1)

	if arg_7_0:sub(arg_7_1, arg_7_1) ~= arg_7_2 then
		if arg_7_3 then
			error("Expected " .. arg_7_2 .. " near position " .. arg_7_1)
		end

		return arg_7_1, false
	end

	return arg_7_1 + 1, true
end

local function ove_0_10(arg_8_0, arg_8_1, arg_8_2)
	-- function 8
	arg_8_2 = arg_8_2 or ""

	local slot_8_0 = "End of input found while parsing string."

	if arg_8_1 > #arg_8_0 then
		error(slot_8_0)
	end

	local slot_8_1 = arg_8_0:sub(arg_8_1, arg_8_1)

	if slot_8_1 == "\"" then
		return arg_8_2, arg_8_1 + 1
	end

	if slot_8_1 ~= "\\" then
		return ove_0_10(arg_8_0, arg_8_1 + 1, arg_8_2 .. slot_8_1)
	end

	local slot_8_2 = {
		t = "\t",
		r = "\r",
		n = "\n",
		f = "\f",
		b = "\b"
	}
	local slot_8_3 = arg_8_0:sub(arg_8_1 + 1, arg_8_1 + 1)

	if not slot_8_3 then
		error(slot_8_0)
	end

	return ove_0_10(arg_8_0, arg_8_1 + 2, arg_8_2 .. (slot_8_2[slot_8_3] or slot_8_3))
end

local function ove_0_11(arg_9_0, arg_9_1)
	-- function 9
	local slot_9_0 = arg_9_0:match("^-?%d+%.?%d*[eE]?[+-]?%d*", arg_9_1)
	local slot_9_1 = tonumber(slot_9_0)

	if not slot_9_1 then
		error("Error parsing number at position " .. arg_9_1 .. ".")
	end

	return slot_9_1, arg_9_1 + #slot_9_0
end

function ove_0_6.stringify(arg_10_0, arg_10_1)
	-- function 10
	local slot_10_0 = {}
	local slot_10_1 = ove_0_7(arg_10_0)

	if slot_10_1 == "array" then
		if arg_10_1 then
			error("Can't encode array as key.")
		end

		slot_10_0[#slot_10_0 + 1] = "["

		for iter_10_0, iter_10_1 in ipairs(arg_10_0) do
			if iter_10_0 > 1 then
				slot_10_0[#slot_10_0 + 1] = ", "
			end

			slot_10_0[#slot_10_0 + 1] = ove_0_6.stringify(iter_10_1)
		end

		slot_10_0[#slot_10_0 + 1] = "]"
	elseif slot_10_1 == "table" then
		if arg_10_1 then
			error("Can't encode table as key.")
		end

		slot_10_0[#slot_10_0 + 1] = "{"

		for iter_10_2, iter_10_3 in pairs(arg_10_0) do
			if #slot_10_0 > 1 then
				slot_10_0[#slot_10_0 + 1] = ", "
			end

			slot_10_0[#slot_10_0 + 1] = ove_0_6.stringify(iter_10_2, true)
			slot_10_0[#slot_10_0 + 1] = ":"
			slot_10_0[#slot_10_0 + 1] = ove_0_6.stringify(iter_10_3)
		end

		slot_10_0[#slot_10_0 + 1] = "}"
	elseif slot_10_1 == "string" then
		return "\"" .. ove_0_8(arg_10_0) .. "\""
	elseif slot_10_1 == "number" then
		if arg_10_1 then
			return "\"" .. tostring(arg_10_0) .. "\""
		end

		return tostring(arg_10_0)
	elseif slot_10_1 == "boolean" then
		return tostring(arg_10_0)
	elseif slot_10_1 == "nil" then
		return "null"
	else
		error("Unjsonifiable type: " .. slot_10_1 .. ".")
	end

	return table.concat(slot_10_0)
end

ove_0_6.null = {}

function ove_0_6.parse(arg_11_0, arg_11_1, arg_11_2)
	-- function 11
	arg_11_1 = arg_11_1 or 1

	if arg_11_1 > #arg_11_0 then
		error("Reached unexpected end of input.")
	end

	local slot_11_0 = arg_11_1 + #arg_11_0:match("^%s*", arg_11_1)
	local slot_11_1 = arg_11_0:sub(slot_11_0, slot_11_0)

	if slot_11_1 == "{" then
		local slot_11_2 = {}
		local slot_11_3 = true
		local slot_11_4 = true

		slot_11_0 = slot_11_0 + 1

		while true do
			local slot_11_5

			slot_11_5, slot_11_0 = ove_0_6.parse(arg_11_0, slot_11_0, "}")

			if slot_11_5 == nil then
				return slot_11_2, slot_11_0
			end

			if not slot_11_4 then
				error("Comma missing between object items.")
			end

			slot_11_0 = ove_0_9(arg_11_0, slot_11_0, ":", true)
			slot_11_2[slot_11_5], slot_11_0 = ove_0_6.parse(arg_11_0, slot_11_0)
			slot_11_0, slot_11_4 = ove_0_9(arg_11_0, slot_11_0, ",")
		end
	elseif slot_11_1 == "[" then
		local slot_11_6 = {}
		local slot_11_7 = true
		local slot_11_8 = true

		slot_11_0 = slot_11_0 + 1

		while true do
			local slot_11_9

			slot_11_9, slot_11_0 = ove_0_6.parse(arg_11_0, slot_11_0, "]")

			if slot_11_9 == nil then
				return slot_11_6, slot_11_0
			end

			if not slot_11_8 then
				error("Comma missing between array items.")
			end

			slot_11_6[#slot_11_6 + 1] = slot_11_9
			slot_11_0, slot_11_8 = ove_0_9(arg_11_0, slot_11_0, ",")
		end
	elseif slot_11_1 == "\"" then
		return ove_0_10(arg_11_0, slot_11_0 + 1)
	elseif slot_11_1 == "-" or slot_11_1:match("%d") then
		return ove_0_11(arg_11_0, slot_11_0)
	elseif slot_11_1 == arg_11_2 then
		return nil, slot_11_0 + 1
	else
		local slot_11_10 = {
			["false"] = false,
			["true"] = true,
			null = ove_0_6.null
		}

		for iter_11_0, iter_11_1 in pairs(slot_11_10) do
			local slot_11_11 = slot_11_0 + #iter_11_0 - 1

			if arg_11_0:sub(slot_11_0, slot_11_11) == iter_11_0 then
				return iter_11_1, slot_11_11 + 1
			end
		end

		local slot_11_12 = "position " .. slot_11_0 .. ": " .. arg_11_0:sub(slot_11_0, slot_11_0 + 10)

		error("Invalid json syntax starting at " .. slot_11_12)
	end
end

function ove_0_6.parsekey(arg_12_0, arg_12_1, arg_12_2)
	-- function 12
	arg_12_1 = arg_12_1 or 1

	if arg_12_1 > #arg_12_0 then
		error("Reached unexpected end of input.")
	end

	local slot_12_0 = arg_12_1 + #arg_12_0:match("^%s*", arg_12_1)
	local slot_12_1 = arg_12_0:sub(slot_12_0, slot_12_0)

	if slot_12_1 == "{" then
		local slot_12_2 = {}
		local slot_12_3 = true
		local slot_12_4 = true

		slot_12_0 = slot_12_0 + 1

		while true do
			local slot_12_5

			slot_12_5, slot_12_0 = ove_0_6.parse(arg_12_0, slot_12_0, "}")

			if slot_12_5 == nil then
				return slot_12_2, slot_12_0
			end

			print("key" .. slot_12_5)

			if not slot_12_4 then
				error("Comma missing between object items.")
			end

			slot_12_0 = ove_0_9(arg_12_0, slot_12_0, ":", true)
			slot_12_2[slot_12_5], slot_12_0 = ove_0_6.parse(arg_12_0, slot_12_0)
			slot_12_0, slot_12_4 = ove_0_9(arg_12_0, slot_12_0, ",")
		end
	elseif slot_12_1 == "[" then
		local slot_12_6 = {}
		local slot_12_7 = true
		local slot_12_8 = true

		slot_12_0 = slot_12_0 + 1

		while true do
			local slot_12_9

			slot_12_9, slot_12_0 = ove_0_6.parse(arg_12_0, slot_12_0, "]")

			if slot_12_9 == nil then
				return slot_12_6, slot_12_0
			end

			if not slot_12_8 then
				error("Comma missing between array items.")
			end

			slot_12_6[#slot_12_6 + 1] = slot_12_9
			slot_12_0, slot_12_8 = ove_0_9(arg_12_0, slot_12_0, ",")
		end
	elseif slot_12_1 == "\"" then
		return ove_0_10(arg_12_0, slot_12_0 + 1)
	elseif slot_12_1 == "-" or slot_12_1:match("%d") then
		return ove_0_11(arg_12_0, slot_12_0)
	elseif slot_12_1 == arg_12_2 then
		return nil, slot_12_0 + 1
	else
		local slot_12_10 = {
			["false"] = false,
			["true"] = true,
			null = ove_0_6.null
		}

		for iter_12_0, iter_12_1 in pairs(slot_12_10) do
			local slot_12_11 = slot_12_0 + #iter_12_0 - 1

			if arg_12_0:sub(slot_12_0, slot_12_11) == iter_12_0 then
				return iter_12_1, slot_12_11 + 1
			end
		end

		local slot_12_12 = "position " .. slot_12_0 .. ": " .. arg_12_0:sub(slot_12_0, slot_12_0 + 10)

		error("Invalid json syntax starting at " .. slot_12_12)
	end
end

return ove_0_6

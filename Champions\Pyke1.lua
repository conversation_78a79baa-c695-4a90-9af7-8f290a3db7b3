local PykePlugin = {}
local ts = module.internal("TS")
local orb = module.internal("orb")
local preds = module.internal("pred")
local ui = module.load("<PERSON>", "ui");
local DelayAction = module.load("<PERSON>", "Core/DelayAction")
local DelayTick = module.load("<PERSON>", "Core/DelayTick")
local Prediction = module.load("<PERSON>", "Core/Prediction")
local BuffManager = module.load("<PERSON>", "Library/BuffManager")
local CalculateManager = module.load("<PERSON>", "Library/CalculateManager")
--local FarmManager = module.load("<PERSON>", "Library/FarmManager")
local ItemManager = module.load("<PERSON>", "Library/ItemManager")
local NetManager = module.load("<PERSON>", "Library/NetManager")
local ObjectManager = module.load("<PERSON>", "Library/ObjectManager")
local OrbManager = module.load("<PERSON>", "Library/OrbManager")
local SpellManager = module.load("<PERSON>", "Library/SpellManager")
local VectorManager = module.load("<PERSON>", "Library/VectorManager")
local MyCommon = module.load("<PERSON>", "Library/ExtraManager")
local common = module.load("Brian", "Utility/common")
local minionmanager = objManager.minions

-------------------
-- Menu creation --
-------------------

local MyMenu

function PykePlugin.Load(GlobalMenu)
    MyMenu = GlobalMenu

    MyMenu.Key:keybind("run", "Marathon Mode", "Z", false)

    MyMenu:menu("combo", "Combo Settings")
    MyMenu.combo:header("xd", "Q Settings")
    MyMenu.combo:boolean("q", "Use Smart Q", true)
    MyMenu.combo.q:set('tooltip', "Uses Short Q when enemy is melee and 50% hp")
    MyMenu.combo:slider("qr", "Long Q Charge", 180, 150, 200, 5)
    MyMenu.combo.qr:set('tooltip', "180 should be fine")
    MyMenu.combo:boolean("sp", "Use Slow Pred", false)

    MyMenu.combo:header("xd", "E Settings")
    MyMenu.combo:boolean("e", "Use E", true)
    MyMenu.combo:slider("range", "Dynamic E Range", 400, 100, 500, 10)
    --MyMenu.combo:slider("mana", "Min. Mana for E %", 20, 1, 100, 1)

    MyMenu:menu("harass", "Harass Settings")
    MyMenu.harass:header("xd", "Harass Settings")
    MyMenu.harass:boolean("q", "Use Q", true)
    MyMenu.harass:slider("Mana", "Min. Mana Percent: ", 10, 0, 100, 10)

    MyMenu:menu("jg", "Jungle Clear Settings")
    MyMenu.jg:header("xd", "Jungle Settings")
    MyMenu.jg:boolean("q", "Use Q", true)
    MyMenu.jg:boolean("e", "Use E", true)


    MyMenu:menu("killsteal", "Killsteal Settings")
    MyMenu.killsteal:header("xd", "KillSteal Settings")
    MyMenu.killsteal:boolean("uks", "Use Killsteal", true)
    MyMenu.killsteal:boolean("qks", "Use Q in Killsteal", true)
    MyMenu.killsteal:boolean("rks", "Use R in Killsteal", true)

    MyMenu:menu("draws", "Draw Settings")
    MyMenu.draws:header("xd", "Drawing Options")
    MyMenu.draws:boolean("q", "Draw Q Range", true)
    MyMenu.draws:color("colorq", "^ color", 255, 233, 121, 121)
    MyMenu.draws:boolean("e", "Draw E Range", true)
    MyMenu.draws:color("colore", "^ color", 255, 233, 121, 121)
end



----------------
-- Spell data --
----------------

local spells = {};

-- Pred input for Q

spells.q = { 
	delay = 0.2; 
	width = 70;
	speed = 2000;
	boundingRadiusMod = 1; 
	collision = { hero = true, minion = true };
}


-- Pred input for R

spells.r = { 
    delay = 0.325;
    radius = 50;
    speed = 1100;
    boundingRadiusMod = 1;
 }


-------------------------------
-- Target selector functions --
-------------------------------

-- Used by target selector, without pred

local function select_target(res, obj, dist)
	if dist > 1050 then return end
	
	res.obj = obj
	return true
end

-- Get target selector result

local function get_target(func)
	return ts.get_result(func).obj
end



--------------------
-- Calc functions --
--------------------
-- Check if unit has buff

local delayedActions, delayedActionsExecuter = {}, nil
function DelayAction(func, delay, args) --delay in seconds
  if not delayedActionsExecuter then
    function delayedActionsExecuter()
      for t, funcs in pairs(delayedActions) do
        if t <= os.clock() then
          for i = 1, #funcs do
            local f = funcs[i]
            if f and f.func then
              f.func(unpack(f.args or {}))
            end
          end
          delayedActions[t] = nil
        end
      end
    end
    cb.add(cb.tick, delayedActionsExecuter)
  end
  local t = os.clock() + (delay or 0)
  if delayedActions[t] then
    delayedActions[t][#delayedActions[t] + 1] = {func = func, args = args}
  else
    delayedActions[t] = {{func = func, args = args}}
  end
end

local function has_buff(unit, name)
	for i = 0, unit.buffManager.count - 1 do
    	local buff = unit.buffManager:get(i)
    	if buff and buff.valid and string.lower(buff.name) == string.lower(name) and buff.owner == unit then
    		if game.time <= buff.endTime then
	      		return true, buff.startTime
    		end
    	end
  	end
  	return false, 0
end

-- Update buff store for calculations

local last_q_time = 0; -- last q time store
local function update_buff()
	local buff, time = has_buff(player, "PykeQ");
	if buff then
		last_q_time = time;
	end
end

-- Point ray-line projection intersection calculation using vectors

local function vector_point_project(v1, v2, v)
    local cx, cy, ax, ay, bx, by = v.pos.x, (v.pos.z or v.pos.y), v1.x, (v1.z or v1.y), v2.x, (v2.z or v2.y)
    local rL = ((cx - ax) * (bx - ax) + (cy - ay) * (by - ay)) / ((bx - ax) ^ 2 + (by - ay) ^ 2)
    local pointLine = { x = ax + rL * (bx - ax), y = ay + rL * (by - ay) }
    local rS = rL < 0 and 0 or (rL > 1 and 1 or rL)
    local isOnSegment = rS == rL
    local pointSegment = isOnSegment and pointLine or { x = ax + rS * (bx - ax), y = ay + rS * (by - ay) }
    return pointSegment, pointLine, isOnSegment
end

-- Q range calculation

local function q_range()
 	local t = game.time - last_q_time;
 	local range = 400;

 	if t > 0.5 then
 		range = range + (t/.1 * 62);
 	end
 	
 	if range > 1050 then
 		return 1050
 	end

  	return range
end

-- Calculate R damage

local scale = {250, 290, 330, 370, 400, 430, 450, 470, 490, 510, 530, 540, 550};
local function r_damage()
	if player.levelRef < 6 then return 0 end
	local dmg = scale[player.levelRef - 5];
	local bonus = common.GetBonusAD()
	local lethality = player.physicalLethality
	return (dmg + (bonus * 0.8) + (lethality * 1.5));
end

local QlvlDmg = {75, 125, 175, 225, 275}
local Q2lvlDmg = {86.25, 143.75, 201.25, 258.75, 316.25}
local function qDmg(target)
	if player.path.serverPos:dist(target.path.serverPos) < 400 then 
		local base_damage = Q2lvlDmg[player:spellSlot(0).level] + (common.GetBonusAD() * 0.69)
		local total = base_damage
		return common.CalculatePhysicalDamage(target, total)
	elseif player.path.serverPos:dist(target.path.serverPos) < 1070 and player.path.serverPos:dist(target.path.serverPos) > 400 then
		local base_damage = QlvlDmg[player:spellSlot(0).level] + (common.GetBonusAD() * 0.60)
		local total = base_damage
		return common.CalculatePhysicalDamage(target, total)
	end
end



-- Get current mana in percentage

local function mana_pct()
	return player.mana / player.maxMana * 100
end

---------------------
-- Combo functions --
---------------------

-- Q Cast, with dynamic range calculation, with collision for short and long

local function spear(unit)
	if player:spellSlot(0).state ~= 0 then return end
	if (player:spellSlot(2).state == 0 and unit.pos:dist(player.pos) < MyMenu.combo.range:get()) and not has_buff(player, "PykeQ") and MyMenu.combo.e:get() then return end
	if unit.pos:dist(player.pos) > 1050 then return end

	local qpred = preds.linear.get_prediction(spells.q, unit)
	if not qpred then return end
		
	if not preds.collision.get_prediction(spells.q, qpred, unit) or unit.pos:dist(player.pos) <= 400 then
		if has_buff(player, "PykeQ") then
			if unit.pos:dist(player.pos) + MyMenu.combo.qr:get() < q_range() and not unit.buff["blackshield"] then
				player:castSpell("release", 0, vec3(qpred.endPos.x, game.mousePos.y, qpred.endPos.y))
			end
		else
			player:castSpell("pos", 0, player.pos)
		end
	end
end

local function ShortQ(unit)
	if player:spellSlot(0).state ~= 0 then return end
	if (player:spellSlot(2).state == 0 and unit.pos:dist(player.pos) < MyMenu.combo.range:get()) and not has_buff(player, "PykeQ") and MyMenu.combo.e:get() then return end
	if unit.pos:dist(player.pos) > 400 then return end

	local qpred = preds.linear.get_prediction(spells.q, unit)
	if not qpred then return end
		
	if not preds.collision.get_prediction(spells.q, qpred, unit) or unit.pos:dist(player.pos) <= 400 then
		if has_buff(player, "PykeQ") then
			if (unit.pos:dist(player.pos) < 400 and q_range() <= 400) then
				player:castSpell("release", 0, vec3(qpred.endPos.x, game.mousePos.y, qpred.endPos.y))
			end
		else
			player:castSpell("pos", 0, player.pos)
		end
	end
end


-- Dash, with configurable range and mana

local function dash(unit)
	if not MyMenu.combo.e:get() then return end
	if player:spellSlot(2).state ~= 0 then return end
	if has_buff(player, "PykeQ") then return end
	if unit.pos:dist(player.pos) > MyMenu.combo.range:get() then return end
	if common.IsValidTarget(unit) then
		player:castSpell("pos", 2, unit.pos)
	end
end

-- Execute R, with X intersection calculation, and draw storage

local ex_data = {};
local last_execute = 0;
local function execute(unit)
	if player:spellSlot(3).state == 32 then return end
	if player.pos:dist(unit.pos) > 760 then return end
	if unit.isDead or not unit.isVisible or not unit.isTargetable then return end
	if unit.buff and unit.buff[17] or has_buff(unit, "sionpassivezombie") then return end
	if last_execute > 0 and game.time - last_execute < 1.2 then return end

	local rpred = preds.circular.get_prediction(spells.r, unit)
	if not rpred then return end

	local pred_pos = vec3(rpred.endPos.x, unit.pos.y, rpred.endPos.y);
	if pred_pos:dist(player.pos) > 760 then return end

	local x1 = pred_pos + vec3(200,0,200);
	local x2 = pred_pos + vec3(-200,0,-200);
	local x3 = pred_pos + vec3(200,0,-200);
	local x4 = pred_pos + vec3(-200,0,200);

	ex_data[unit.networkID].draw = {x1, x2, x3, x4};

	local ps1, pl1, line1 = vector_point_project(x1, x2, unit);
	local ps2, pl2, line2 = vector_point_project(x3, x4, unit);
	local newpos = vec2(pred_pos.x, pred_pos.z);

	if common.IsValidTarget(unit) and (line1 and newpos:dist(ps1) < 50 + unit.boundingRadius) or (line2 and newpos:dist(ps2) < 50 + unit.boundingRadius) then
		player:castSpell("pos", 3, pred_pos)
		last_execute = game.time;
	end
end

-- Combo function to call each cast

local function combo()
	local target = get_target(select_target);

	if not target then return end
	if not orb.combat.is_active() then return end
	if common.IsValidTarget(target) and not target.buff["sionpassivezombie"] then
		if player.path.serverPos:dist(target.path.serverPos) < common.GetAARange() and player:spellSlot(0).state == 0 then
			orb.core.set_pause_attack(0.5)
		end
		if common.GetPercentHealth(target) > 30 and player:spellSlot(3).state == 0 then
			if MyMenu.combo.q:get() then
				if player.path.serverPos:dist(target.path.serverPos) < 400 and common.GetPercentHealth(target) < 50 and player:spellSlot(0).state == 0 then
					--orb.core.set_pause_attack(0.5)
					ShortQ(target);
				else
					--orb.core.set_pause_attack(0.5)
					spear(target)
				end
			end
			DelayAction(function() dash(target) end, 0.25)
		elseif player:spellSlot(3).state ~= 0 then
			if MyMenu.combo.q:get() then
				if player.path.serverPos:dist(target.path.serverPos) < 400 and common.GetPercentHealth(target) < 50 and player:spellSlot(0).state == 0 then
					--orb.core.set_pause_attack(0.5)
					ShortQ(target);
				else
					--orb.core.set_pause_attack(0.5)
					spear(target)
				end
			end
			DelayAction(function() dash(target) end, 0.25)
		end
	end
end

local function harass()
	local target = get_target(select_target);

	if not target then return end
	if not MyMenu.Key.Harass:get() then return end
	if common.IsValidTarget(target) and not target.buff["sionpassivezombie"] then
		if player.par / player.maxPar * 100 >= MyMenu.harass.Mana:get() then
			if MyMenu.harass.q:get() then
				spear(target);
			end
		end
	end
end

local function Clear()
	local target = { obj = nil, health = 0, mode = "jungleclear" }
	local aaRange = player.attackRange + player.boundingRadius + 200
	for i = 0, minionmanager.size[TEAM_NEUTRAL] - 1 do
		local obj = minionmanager[TEAM_NEUTRAL][i]
		if player.pos:dist(obj.pos) <= aaRange and obj.maxHealth > target.health then
			target.obj = obj
			target.health = obj.maxHealth
		end
	end
	if target.obj and common.IsValidTarget(target.obj) then
		if target.mode == "jungleclear" then
			if MyMenu.jg.q:get() and player:spellSlot(0).state == 0 and player.path.serverPos:dist(target.obj.path.serverPos) < 400 then
				ShortQ(target.obj)
			end
			if MyMenu.jg.e:get() and player:spellSlot(2).state == 0 and player.path.serverPos:dist(target.obj.path.serverPos) < 625 then
				dash(target.obj)
			end
		end
	end
end

local function Run()
	if MyMenu.Key.run:get() then
		player:move((game.mousePos))
		if player:spellSlot(2).state == 0 and not navmesh.isWall(game.mousePos) then
			player:castSpell("pos", 2, (game.mousePos))
		end
		if player:spellSlot(2).state ~= 0 and player:spellSlot(1).state == 0 then
			player:castSpell("self", 1)
		end
	end
end

-----------
-- Hooks --
-----------

-- Called pre tick
-- With loop for kill stealing

local function ontick()

	update_buff();
	if MyMenu.killsteal.uks:get() then
		for i = 0, objManager.enemies_n - 1 do
	    	local nerd = objManager.enemies[i]
	    	if not nerd then return end
	    	if not ex_data[nerd.networkID] then ex_data[nerd.networkID] = {} end
	    	ex_data[nerd.networkID].kill = false;
	    	if MyMenu.killsteal.rks:get() and player.path.serverPos:dist(nerd.path.serverPos) < 760 then
		    	if r_damage() > nerd.health and common.IsValidTarget(nerd) and not nerd.buff["undyingrage"] and not nerd.buff["kindredrnodeathbuff"] and not nerd.buff["willrevive"] then
		    		ex_data[nerd.networkID].kill = true;
		    		execute(nerd);
		    	end
		    end
		    if MyMenu.killsteal.qks:get() then
		    	if player:spellSlot(0).state == 0 and not nerd.isDead and nerd.isVisible and player:spellSlot(3).state ~= 0 then
		    		if player.path.serverPos:dist(nerd.path.serverPos) < 400 and qDmg(nerd) > nerd.health then
		    			ShortQ(nerd)
		    		elseif player.path.serverPos:dist(nerd.path.serverPos) > 400 and player.path.serverPos:dist(nerd.path.serverPos) < 1070 and qDmg(nerd) > nerd.health then
		    			spear(nerd)
		    		end
		    	end
		    end
	    end
	end
    if has_buff(player, "PykeQ") then
    	orb.core.set_pause_attack(1)
    end
    if orb.combat.is_active() then
		if not has_buff(player, "PykeQ") then
			orb.core.set_pause_attack(0)
		end
	end
	if not has_buff(player, "PykeQ") then
		if orb.core.is_attack_paused() then
			orb.core.set_pause_attack(0)
		end
	end
	if not orb.combat.is_active() and not MyMenu.Key.LastHit:get() and not MyMenu.Key.LaneClear:get() then
		if orb.core.is_attack_paused() then
			orb.core.set_pause_attack(0)
		end
		if orb.combat.is_active() and player:spellSlot(0).state ~= 0 then
			orb.core.set_pause_attack(0)
		end
	end
	combo();
	harass()
	if MyMenu.Key.run:get() then
		Run()
	end
	if MyMenu.Key.LaneClear:get() then
		Clear()
	end
end

-- Draw hook, used for drawing X and dynamic Q range

local function ondraw()
	if MyMenu.draws.q:get() and player:spellSlot(0).state == 0 and player.isOnScreen and not player.isDead then
		graphics.draw_circle(player.pos, q_range(), 2, MyMenu.draws.colorq:get(), 50)
	end
	if MyMenu.draws.e:get() and player:spellSlot(2).state == 0 and player.isOnScreen then
		graphics.draw_circle(player.pos, MyMenu.combo.range:get(), 2, MyMenu.draws.colore:get(), 50)
	end
end

cb.add(cb.draw, ondraw)
orb.combat.register_f_pre_tick(ontick)

return PykePlugin
local lucianPlugin = {}

-- Load Spell

local spellQ = {
    range = 500,
}

local spellQ1 = {
    range = 1050,
    delay = 0.40,
    width = 65,
    speed = math.huge,
    boundingRadiusMod = 1,
    collision = {hero = false, minion = false, wall = false},
}

local spellW = {
    range = 900,
    delay = 0.25,
    width = 80,
    speed = 1600,
    boundingRadiusMod = 1,
    collision = {hero = true, minion = true, wall = false},
}

local spellW1 = {
    range = 900,
    delay = 0.25,
    width = 80,
    speed = 1600,
    boundingRadiusMod = 1,
    collision = {hero = false, minion = false, wall = false},
}

local spellR = {
    range = 1000,
    delay = 0.20,
    width = 60,
    speed = 2500,
    boundingRadiusMod = 1,
    collision = {hero = false, minion = false, wall = false},
    active = false,
    endPos = vec3(0, 0, 0)
}

local passive = {
    active = false,
    checkRange = 500,
}

local ShouldEFirst = false
local SwitchTime = 0


-- Load Module
local TS = module.internal("TS")
local evade = module.seek("evade")
local preds = module.internal("pred")
local orb = module.internal("orb")
local Curses = module.load("<PERSON>", "Curse<PERSON>");
--local ui = module.load("<PERSON>", "ui");
local DelayAction = module.load("Brian", "Core/DelayAction")
local DelayTick = module.load("Brian", "Core/DelayTick")
local Prediction = module.load("Brian", "Core/Prediction")
local BuffManager = module.load("Brian", "Library/BuffManager")
local CalculateManager = module.load("Brian", "Library/CalculateManager")
local FarmManager = module.load("Brian", "Library/FarmManager")
local ItemManager = module.load("Brian", "Library/ItemManager")
local NetManager = module.load("Brian", "Library/NetManager")
local ObjectManager = module.load("Brian", "Library/ObjectManager")
local OrbManager = module.load("Brian", "Library/OrbManager")
local SpellManager = module.load("Brian", "Library/SpellManager")
local VectorManager = module.load("Brian", "Library/VectorManager")
local MyCommon = module.load("Brian", "Library/ExtraManager")
local PlayerBuff = module.load("Brian", "Core/PlayerBuff")

local MyMenu
local icon = graphics.sprite('mao.png')
function lucianPlugin.Load(GlobalMenu)
MyMenu = GlobalMenu
MyMenu:set('icon', player.iconSquare)
MyMenu.Key:keybind("TurretClear", "������������", "V", nil)
MyMenu.Key:keybind("SemiR", "���ֶ� R ��", "T", nil)

MyMenu:menu("Combo", "����")
MyMenu.Combo:header("SpellHeader", "���ܺ���")
MyMenu.Combo:boolean("Q", "�Ƿ�ʹ�� Q ����", true)
MyMenu.Combo:boolean("QE", "^ �ӳ� Q ����", true)
MyMenu.Combo:boolean("W", "�Ƿ�ʹ�� W ����", true)
MyMenu.Combo:boolean("WN", "^ ������ײ", true)
MyMenu.Combo:boolean("E", "�Ƿ�ʹ�� E ����", true)
MyMenu.Combo:boolean("EDash", "^ �����ͻ��", false)
MyMenu.Combo:header("ModeHeader", "���з��")
MyMenu.Combo:dropdown("ComboMode", "^ ���: ", 1, {"�Զ���ģʽ", "���� E", "���� Q", "AQ ����"}) 
MyMenu.Combo:keybind("ComboModeKey", "^ �л�����", "T", false)

MyMenu:menu("Harass", "ɧ��")
MyMenu.Harass:header("SpellHeader", "���ܺ���")
MyMenu.Harass:boolean("Q", "�Ƿ�ʹ�� Q ����", true)
MyMenu.Harass:boolean("QE", "^ �ӳ� Q ����", true)
MyMenu.Harass:boolean("W", "�Ƿ�ʹ�� W ����", false)
MyMenu.Harass:boolean("E", "�Ƿ�ʹ�� E ����", false)
MyMenu.Harass:header("ManaHeader", "ħ������")
MyMenu.Harass:slider("ManaMin", "���ħ��ֵ�ٷֱ� >= x%", 60, 1, 100, 1)
MyMenu.Harass:header("ProHeader", "רҵģʽ")
MyMenu.Harass:boolean("ProAllow", "����רҵɧ��ģʽ", true)
MyMenu.Harass.ProAllow:set("tooltip", "רҵģʽ => ����������ģʽ��ʹ�ü���ɧ�ŵ���")
MyMenu.Harass:boolean("ProTurret", "����������ɧ��", false)

MyMenu:menu("LaneClear", "����")
MyMenu.LaneClear:header("SpellHeader", "���ܺ���")
MyMenu.LaneClear:boolean("Q", "�Ƿ�ʹ�� Q ����", true)
MyMenu.LaneClear:slider("QC", "^ ��С�������� >= x", 3, 1, 10, 1)
MyMenu.LaneClear:boolean("W", "�Ƿ�ʹ�� W ����", true)
MyMenu.LaneClear:slider("WC", "^ ��С�������� >= x", 3, 1, 10, 1)
MyMenu.LaneClear:header("ManaHeader", "ħ������")
MyMenu.LaneClear:slider("ManaMin", "���ħ��ֵ�ٷֱ� >= x%", 60, 1, 100, 1)

MyMenu:menu("JungleClear", "��Ұ")
MyMenu.JungleClear:header("SpellHeader", "���ܺ���")
MyMenu.JungleClear:boolean("Q", "�Ƿ�ʹ�� Q ����", true)
MyMenu.JungleClear:boolean("W", "�Ƿ�ʹ�� W ����", true)
MyMenu.JungleClear:boolean("E", "�Ƿ�ʹ�� E ����", true)
MyMenu.JungleClear:header("ManaHeader", "ħ������")
MyMenu.JungleClear:slider("ManaMin", "���ħ��ֵ�ٷֱ� >= x%", 20, 1, 100, 1)

MyMenu:menu("TurretClear", "��������")
MyMenu.TurretClear:header("SpellHeader", "���ܺ���")
MyMenu.TurretClear:boolean("W", "�Ƿ�ʹ�� W ����", true)
MyMenu.TurretClear:boolean("E", "�Ƿ�ʹ�� E ����", true)
MyMenu.TurretClear:header("ManaHeader", "ħ������")
MyMenu.TurretClear:slider("ManaMin", "���ħ��ֵ�ٷֱ� >= x%", 20, 1, 100, 1)

FarmManager.Load(MyMenu)

MyMenu:menu("KillSteal", "��ɱ����")
MyMenu.KillSteal:header("SpellHeader", "���ܺ���")
MyMenu.KillSteal:boolean("Q", "�Ƿ�ʹ�� Q ����", true)
MyMenu.KillSteal:boolean("AutoRKS", "�Զ�R��ɱ", true)
MyMenu.KillSteal:slider("AutoRKSHealth", "^ Ŀ��Ѫ���ٷֱ� <= x%", 30, 5, 100, 5)

MyMenu:menu("Misc", "��������")
MyMenu.Misc:header("EHeader", "����׷�� [E]")
MyMenu.Misc:boolean("SafeCheck", "��ȫ���", true)
MyMenu.Misc:slider("EnemyCount", "^ �������� >= x", 3, 2, 5, 1)
MyMenu.Misc:slider("EnemyRange", "^ �������˷�Χ", 500, 1, 800, 1)
MyMenu.Misc:boolean("CheckTurret", "����з�����ͻ��", false)
MyMenu.Misc:header("RHeader", "ɨ�� [R]")
MyMenu.Misc:boolean("AutoLock", "�Զ�����", true)
MyMenu.Misc:keybind("Lock", "������", "A", nil)

MyMenu:menu("Draw", "��������")
MyMenu.Draw:header("RangeHeader", "���ܷ�Χ")
MyMenu.Draw:boolean("Q", "���� Q ��Χ", true)
MyMenu.Draw:color("colorq", "^ ��ɫ", 255, 233, 121, 121)
MyMenu.Draw:boolean("W", "���� W ��Χ", false)
MyMenu.Draw:color("colorw", "^ ��ɫ", 255, 233, 121, 121)
MyMenu.Draw:boolean("R", "���� R ��Χ", true)
MyMenu.Draw:color("colorr", "^ ��ɫ", 255, 233, 121, 121)
MyMenu.Draw:boolean("Ready", "����ʾ����׼����ʱ", false)
MyMenu.Draw:boolean("ComboMode", "�������з��", true)
MyMenu.Draw:header("DamageHeader", "�˺�ָʾ��")
MyMenu.Draw:boolean("DIEnabled", "����", true)


    MyMenu.Combo.ComboModeKey:set(
        "callback",
        function()
            if player.isDead or player.isRecalling or chat.isOpened then
                return
            end
            if game.time - SwitchTime > 0.2 then
                if MyMenu.Combo.ComboMode:get() == 1 then
                    MyMenu.Combo.ComboMode:set("value", 2)
                    SwitchTime = game.time
                    return
                end
                if MyMenu.Combo.ComboMode:get() == 2 then
                    MyMenu.Combo.ComboMode:set("value", 3)
                    SwitchTime = game.time
                    return
                end
                if MyMenu.Combo.ComboMode:get() == 3 then
                    MyMenu.Combo.ComboMode:set("value", 1)
                    SwitchTime = game.time
                    return
                end
				if MyMenu.Combo.ComboMode:get() == 4 then
                    MyMenu.Combo.ComboMode:set("value", 1)
                    SwitchTime = game.time
                    return
                end
            end
        end
    )

end

local function GetQDamage(target)
    if not target or target == nil then
        return 0
    end
    local level = player:spellSlot(0).level
    if level == 0 then
        return 0
    end
    local dmg = ({80, 120, 155, 190, 225})[level] + (({0.6, 0.75, 0.9, 1.05, 1.2})[level] * MyCommon.GetTotalAD())
    return CalculateManager.CalculatePhysicalDamage(target, dmg)
end

local function GetRDamage(target)
    if not target or target == nil then
        return 0
    end
    local level = player:spellSlot(3).level
    if level == 0 then
        return 0
    end
    
    -- ����R�ȼ����㵥���ӵ��˺�
    local baseDmg = ({20, 40, 60})[level]
    local adScaling = 0.25 -- R��AD�ӳ�
    local shotDmg = baseDmg + (adScaling * MyCommon.GetTotalAD())
    
    -- �����Ƿ񱩻��ͶԷ����׼���ʵ���˺�
    local critChance = player.critChance or 0
    local critDmg = 1.75 -- Ĭ�ϱ����˺�����
    if player.critDamage and player.critDamage > 0 then
        critDmg = player.critDamage
    end
    
    -- ���ǿ��ܵı������
    local avgDmgPerShot = shotDmg * (1 + critChance * (critDmg - 1))
    
    -- �������˺��������ӵ�������
    local shots = ({20, 25, 30})[level]
    local totalDmg = avgDmgPerShot * shots * 0.5 -- ��������һ���ӵ�
    
    return CalculateManager.CalculatePhysicalDamage(target, totalDmg)
end

local function IsSpellCast(name)
    if name then
        if name == "lucianq" or name == "lucianw" or name == "luciane" or name == "lucianr" then
            return true
        end
    end
end

local function ShouldResetAttackTime(name)
    if name then
        if name == "lucianw" then
            return true, 100
        end
        if name == "luciane" then
            return true, 0
        end
    end
end

local function GetEPosition(target)
    if not target or target == nil or not MyCommon.IsValidTarget(target) then
        return vec3(0, 0, 0)
    end
    if not MyMenu.Combo.EDash:get() and not MyCommon.IsInAutoAttackRange(target, 100) then
        return vec3(0, 0, 0)
    end
    local shortPos = VectorManager.Extend(player.pos, game.mousePos, 270)
    local maxPos = VectorManager.Extend(player.pos, game.mousePos, 475)
    local castPos = game.mousePos
    if MyCommon.IsInAutoAttackRange(target) and target.pos:dist(shortPos) < passive.checkRange then
        castPos = shortPos
    elseif not MyCommon.IsInAutoAttackRange(target) and target.pos:dist(shortPos) < passive.checkRange then
        castPos = shortPos
    elseif not MyCommon.IsInAutoAttackRange(target) and target.pos:dist(shortPos) > passive.checkRange and target.pos:dist(maxPos) < passive.checkRange then
        castPos = maxPos
    end
    return castPos
end

local function CanDash(pos)
    if not pos then
        return false
    end
    if MyMenu.Misc.CheckTurret:get() and ObjectManager.IsUnderEnemyTurret(pos) then
        return false
    end
    if not MyMenu.Misc.SafeCheck:get() then
        return true
    end
    local range = MyMenu.Misc.EnemyRange:get()
    local count = ObjectManager.GetEnemiesInRange(range, pos)
    if count and #count < MyMenu.Misc.EnemyCount:get() then
        return true
    end
    return false
end

local function SetStatus()
    local realDelay = ({0.40, 0.39, 0.38, 0.37, 0.36, 0.35, 0.34, 0.33, 0.32, 0.31, 0.30, 0.29, 0.28, 0.27, 0.26, 0.25, 0.24, 0.23})[math.min(player.levelRef, 18)]
    spellQ1.delay = realDelay
end

local function CheckBuff()
    if player.isDead then
        passive.active = false
        spellR.active = 0
        ShouldEFirst = false
        spellR.endPos = vec3(0, 0, 0)
        return
    end

    if not BuffManager.HasBuff(player, "LucianR") and not SpellManager.CanCastSpell(3) then
        spellR.active = false
        spellR.endPos = vec3(0, 0, 0)
    end

    passive.checkRange = player.attackRange + player.boundingRadius

    -- �����ܵļ������ȼ��жϣ�������ȴ������E���ܵȼ�
    local cdPercent = math.floor(100 - (1 + player.percentCooldownMod) * 100)
    local eLevel = player:spellSlot(2).level
    
    -- ������ȴ������E�ȼ��������Ƿ�Ӧ������E
    if cdPercent >= 35 and eLevel >= 3 then  -- ������������
        ShouldEFirst = true
    elseif cdPercent >= 25 and eLevel >= 4 then
        ShouldEFirst = true
    elseif cdPercent >= 20 and eLevel >= 5 then
        ShouldEFirst = true
    else
        -- ���ݵ�ǰ״̬�����ܵ��ж�
        ShouldEFirst = (player.health / player.maxHealth < 0.4) and eLevel >= 3
    end
end

local function IsValidQRange(target, minion)
    if not target or target == nil or not MyCommon.IsValidTarget(target) or not target.pos then
        return false
    end

    if not minion or minion == nil or not MyCommon.IsValidTarget(minion) or not minion.pos then
        return false
    end

    local tpX, tpY, tpZ = target.pos.x, target.pos.y, target.pos.z
    local mpX, mpY, mpZ = minion.pos.x, minion.pos.y, minion.pos.z
    local ppX, ppY, ppZ = player.pos.x, player.pos.y, player.pos.z

    local tppX, tppY, tppZ = (tpX - ppX) ^ 2, (tpY - ppY) ^ 2, (tpZ - ppZ) ^ 2 
    local mppX, mppY, mppZ = (mpX - ppX) ^ 2, (mpY - ppY) ^ 2, (mpZ - ppZ) ^ 2
    local tpmX, tpmY, tpmZ = (tpX - mpX) ^ 2, (tpY - mpY) ^ 2, (tpZ - mpZ) ^ 2

    local AB = math.sqrt(tppX + tppY + tppZ)
    local AP = math.sqrt(mppX + mppY + mppZ)
    local PB = math.sqrt(tpmX + tpmY + tpmZ)

    if AB > (AP + PB - 5) or AB > (AP + PB + 5) then
        return true
    end
end

local function CountMinionHit(minion)
    local n = 0
    local minions = ObjectManager.GetMinions(spellQ1.range, TEAM_ENEMY)
    for i, m in ipairs(minions) do
        if m and m ~= nil and MyCommon.IsValidTarget(m, spellQ1.range) and IsValidQRange(m, minion) then
            n = n + 1
        end
	end
	return n
end


local ove_0_51 = {
	0.4,
	0.39,
	0.38,
	0.37,
	0.36,
	0.36,
	0.35,
	0.34,
	0.33,
	0.32,
	0.31,
	0.3,
	0.29,
	0.29,
	0.28,
	0.27,
	0.26,
	0.25
}

local function ExtendQ(target)
    if player:spellSlot(0).state == 0 then
        for iter_21_0 = 0, objManager.enemies_n - 1 do
            local enemy = objManager.enemies[iter_21_0]
            
            -- ����������Χ����1050��1150
            if enemy:isValidTarget(1150) then
                local foundExtendQ = false
                
                -- ȷ�����㹻������ʩ��QE����
                if player.mana >= (player.manaCost0 + player.manaCost2 * 0.7) then
                    -- ����Ӣ�۵ȼ���̬����Ԥ��ʱ��
                    local predict_time = ove_0_51[player.levelRef] or 0.4
                    
                    for iter_21_1 = 0, objManager.minions.size[TEAM_ENEMY] - 1 do
                        local minion = objManager.minions[TEAM_ENEMY][iter_21_1]
                        
                        -- ����Q���ж���Χ������������
                        if minion:isValidTarget(550 + minion.boundingRadius + player.boundingRadius) and not minion.isWard and not minion.isPlant and not minion.isTrap then
                            -- ��ȡ���˵�Ԥ��λ��
                            local enemyPredPos = preds.core.get_pos_after_time(enemy, predict_time)
                            
                            -- ���������ж����򣬴�50��70����̴�600��650
                            local lineDistance = enemyPredPos:distLine(player.pos2D, player.pos2D:extend(minion.pos2D, 1050))
                            if lineDistance < 70 and lineDistance > 0 then
                                if enemyPredPos:dist(player.pos2D) < 1050 and 
                                   player.pos2D:extend(minion.pos2D, 1050):dist(enemyPredPos) < 650 then
                                    player:castSpell("obj", 0, minion)
                                    foundExtendQ = true
                                    break  -- �ҵ�����Ŀ��������ͷ�
                                end
                            end
                        end
                    end

                    -- ���û���ҵ��ӳ�Q��Ŀ�ֱ꣬�ӶԵ����ͷ�Q
                    if not foundExtendQ and enemy:isValidTarget(550 + enemy.boundingRadius + player.boundingRadius) then
                        player:castSpell("obj", 0, enemy)
                    end
                end
            end
        end
    end
end

local function GetTarget(newTarget)
    newTarget = newTarget or false
    local orbTarget = OrbManager.BOTOrbwalker.combat.target
    if orbTarget and orbTarget ~= nil and MyCommon.IsValidTarget(orbTarget) and orbTarget.type == TYPE_HERO then
        return orbTarget
    end
    if newTarget then
        local range = math.max(475 + player.attackRange + player.boundingRadius, 1100)
        local target = MyCommon.GetTarget(range)
        if target and target ~= nil and MyCommon.IsValidTarget(target) then
            return target 
        end
    end
    return nil
end

local function ClosestPoint(v, tbl)
    local result = vec2(0, 0)
    local dist = math.huge

    for i, point in pairs(tbl) do
        if v:distSqr(point) < dist then
            result = poing
            dist = v:distSqr(point)
        end
    end

    return result
end

local function AutoLock()
    if not MyMenu.Misc.AutoLock:get() then
        player:move(game.mousePos)
        return 
    end

    if not MyMenu.Misc.Lock:get() then
        player:move(game.mousePos)
        return 
    end

    local target = TS.get_result(function(res, obj, dist)
        if dist > 1300 then
            return
        end
        res.obj = obj
        return true
    end, TS.filter_set[1]).obj

    if not target or not MyCommon.IsValidTarget(target, spellR.range + 110) then
        player:move(game.mousePos)
        return 
    end

    -- ����Ŀ��λ�ü�������ƶ�λ��
    if LucianCastDirection then
        local endpos = player.path.serverPos + LucianCastDirection * 1100
        
        if endpos and target.pos:distSqr(player.pos) <= (1100 + 100)^2 then
            local dist = target.path.serverPos:dist(mousePos)
            if dist > 1100 then dist = 950 end
            
            local pos = preds.core.get_pos_after_time(target, 0.25):toGame3D()
            local Walkerpos = endpos + (pos-endpos):norm() * (pos:dist(endpos) + dist)
            
            if Walkerpos then
                ForcePoint(Walkerpos)
            end
        else
            player:move(game.mousePos)
        end
    else
        player:move(game.mousePos)
    end
end

local function KillSteal()
    if MyMenu.KillSteal.Q:get() and SpellManager.CanCastSpell(0) and not player.path.isDashing then
        local targets = ObjectManager.GetEnemiesInRange(spellQ1.range, player)
        for i, target in ipairs(targets) do
            if MyCommon.IsValidTarget(target, spellQ1.range) and not MyCommon.IsUnKillAble(target) then
                local qDMG = GetQDamage(target)
                if target.health and target.health < qDMG then
                    if MyCommon.IsValidTarget(target, spellQ.range) then
                        SpellManager.CastOnUnit(target, 0)
                    elseif MyCommon.IsValidTarget(target, spellQ1.range) then
                        ExtendQ(target)
                    end
                end
            end
        end
    end
end

local function AutoRKillSteal()
    -- ����Ƿ������Զ�R��ɱ
    if not MyMenu.KillSteal.AutoRKS:get() then
        return
    end
    
    -- ���R��ȴ�л��Ѿ����ͷ�R���򷵻�
    if not SpellManager.CanCastSpell(3) or player.buff['lucianr'] then 
        return 
    end
    
    -- ��ȡR����ڵĵ���
    local enemies = ObjectManager.GetEnemiesInRange(spellR.range)
    if not enemies or #enemies == 0 then
        return
    end
    
    for i, enemy in ipairs(enemies) do
        -- �������Ƿ�ɻ�ɱ������Ч��Χ��
        if enemy and MyCommon.IsValidTarget(enemy, spellR.range) and not MyCommon.IsUnKillAble(enemy) then
            -- ������Ѫ���ٷֱ��Ƿ�����趨ֵ
            if enemy.health and enemy.maxHealth and enemy.maxHealth > 0 then
                local healthPercent = (enemy.health / enemy.maxHealth) * 100
                if healthPercent <= MyMenu.KillSteal.AutoRKSHealth:get() then
                    -- ����R�˺�
                    local rDamage = GetRDamage(enemy)
                    
                    -- �������Ѫ������R�˺��Ҳ����չ���Χ�ڻ���˼�������
                    if enemy.health <= rDamage and (
                        not MyCommon.IsInAutoAttackRange(enemy) or 
                        (enemy.moveSpeed and player.moveSpeed and enemy.moveSpeed > player.moveSpeed and enemy.pos:dist(player.pos) > 500)
                    ) then
                        -- �����������������W���ã�����W����
                        if enemy.moveSpeed and player.moveSpeed and enemy.moveSpeed > player.moveSpeed and SpellManager.CanCastSpell(1) and enemy.pos:dist(player.pos) < spellW.range then
                            local pred = Prediction.GetPrediction(spellW, enemy)
                            if pred then
                                SpellManager.CastOnPosition(vec3(pred.x, enemy.pos.y, pred.y), 1)
                                DelayAction.Add(function()
                                    -- ʹ��player:castSpell����castR
                                    player:castSpell('pos', 3, enemy.pos)
                                end, 0.1)
                                return
                            end
                        else
                            -- ֱ��ʹ��R��ʹ��player:castSpell����castR
                            player:castSpell('pos', 3, enemy.pos)
                            return
                        end
                    end
                end
            end
        end
    end
end

local function AQCombo()
local target = GetTarget()
if not target or target == nil or not MyCommon.IsValidTarget(target) then
return
end

-- 普攻的范围内扝使用Q，强调AQ连招
if MyMenu.Combo.Q:get() and SpellManager.CanCastSpell(0) and MyCommon.IsValidTarget(target, spellQ.range) then
    -- 如果在普攻范围内且被动丝激活，优先进行普攻，依靠OnMyAfterAttack回调来释放Q
    if MyCommon.IsInAutoAttackRange(target) and not passive.active then
        return  -- 让Orbwalker执行普攻
    -- 如果被动已激�?(说明刚普攻过)，立坳释放Q
    elseif passive.active then
        SpellManager.CastOnUnit(target, 0)
        return
    end
end

-- 如果目标超出Q范围但在Q延长范围�?
if MyMenu.Combo.Q:get() and SpellManager.CanCastSpell(0) and not passive.active and
   not MyCommon.IsValidTarget(target, spellQ.range) and 
   MyCommon.IsValidTarget(target, spellQ1.range) and MyMenu.Combo.QE:get() then
    ExtendQ(target)
end

-- E技能逻辑，与其他模弝相坌
if MyMenu.Combo.E:get() and SpellManager.CanCastSpell(2) and not passive.active and target.pos:dist(player.pos) < 475 + passive.checkRange then
    local pos = GetEPosition(target)
    if pos and not VectorManager.IsZeroPoint(pos) and CanDash(pos) then
        SpellManager.CastOnPosition(pos, 2)
        passive.active = true
    end
end

-- W技能逻辑，与其他模弝相坌
if MyMenu.Combo.W:get() and SpellManager.CanCastSpell(1) and not passive.active and MyCommon.IsValidTarget(target, spellW.range) then
    if MyCommon.IsInAutoAttackRange(target) then
        local spell = MyMenu.Combo.WN:get() and spellW1 or spellW
        local pred = Prediction.GetPrediction(spell, target)
        if pred then
            SpellManager.CastOnPosition(vec3(pred.x, target.pos.y, pred.y), 1)
            passive.active = true
        end
    else
        local pred = Prediction.GetPrediction(spellW, target)
        if pred then
            SpellManager.CastOnPosition(vec3(pred.x, target.pos.y, pred.y), 1)
            passive.active = true
        end
    end
end
end
local function Combo()
    local target = GetTarget()
    if not target or target == nil or not MyCommon.IsValidTarget(target) then
        return
    end
    local mode = MyMenu.Combo.ComboMode:get()
    if mode == 1 then
        if ShouldEFirst then
            if MyMenu.Combo.E:get() and SpellManager.CanCastSpell(2) and not passive.active and target.pos:dist(player.pos) < 475 + passive.checkRange then
                local pos = GetEPosition(target)
                if pos and not VectorManager.IsZeroPoint(pos) and CanDash(pos) then
                    SpellManager.CastOnPosition(pos, 2)
                    passive.active = true
                end
            end
            if MyMenu.Combo.Q:get() and SpellManager.CanCastSpell(0) and not passive.active then
                if MyCommon.IsValidTarget(target, spellQ.range) then
                    SpellManager.CastOnUnit(target, 0)
                    passive.active = true
                elseif MyCommon.IsValidTarget(target, spellQ1.range) and MyMenu.Combo.QE:get() then
                    ExtendQ(target)
                end
            end
            if MyMenu.Combo.W:get() and SpellManager.CanCastSpell(1) and not passive.active and MyCommon.IsValidTarget(target, spellW.range) then
                if MyCommon.IsInAutoAttackRange(target) then
                    local spell = MyMenu.Combo.WN:get() and spellW1 or spellW
                    local pred = Prediction.GetPrediction(spell, target)
                    if pred then
                        SpellManager.CastOnPosition(vec3(pred.x, target.pos.y, pred.y), 1)
                        passive.active = true
                    end
                else 
                    local pred = Prediction.GetPrediction(spellW, target)
                    if pred then
                        SpellManager.CastOnPosition(vec3(pred.x, target.pos.y, pred.y), 1)
                        passive.active = true
                    end
                end
            end
        elseif not ShouldEFirst then
            if MyMenu.Combo.Q:get() and SpellManager.CanCastSpell(0) and not passive.active then
                if MyCommon.IsValidTarget(target, spellQ.range) then
                    SpellManager.CastOnUnit(target, 0)
                    passive.active = true
                elseif MyCommon.IsValidTarget(target, spellQ1.range) and MyMenu.Combo.QE:get() then
                    ExtendQ(target)
                end
            end
            if MyMenu.Combo.E:get() and SpellManager.CanCastSpell(2) and not passive.active and target.pos:dist(player.pos) < 475 + passive.checkRange then
                local pos = GetEPosition(target)
                if pos and not VectorManager.IsZeroPoint(pos) and CanDash(pos) then
                    SpellManager.CastOnPosition(pos, 2)
                    passive.active = true
                end
            end
            if MyMenu.Combo.W:get() and SpellManager.CanCastSpell(1) and not passive.active and MyCommon.IsValidTarget(target, spellW.range) then
                if MyCommon.IsInAutoAttackRange(target) then
                    local spell = MyMenu.Combo.WN:get() and spellW1 or spellW
                    local pred = Prediction.GetPrediction(spell, target)
                    if pred then
                        SpellManager.CastOnPosition(vec3(pred.x, target.pos.y, pred.y), 1)
                        passive.active = true
                    end
                else 
                    local pred = Prediction.GetPrediction(spellW, target)
                    if pred then
                        SpellManager.CastOnPosition(vec3(pred.x, target.pos.y, pred.y), 1)
                        passive.active = true
                    end
                end
            end
        end
    elseif mode == 2 then
        if MyMenu.Combo.E:get() and SpellManager.CanCastSpell(2) and not passive.active and target.pos:dist(player.pos) < 475 + passive.checkRange then
            local pos = GetEPosition(target)
            if pos and not VectorManager.IsZeroPoint(pos) and CanDash(pos) then
                SpellManager.CastOnPosition(pos, 2)
                passive.active = true
            end
        end
        if MyMenu.Combo.Q:get() and SpellManager.CanCastSpell(0) and not passive.active then
            if MyCommon.IsValidTarget(target, spellQ.range) then
                SpellManager.CastOnUnit(target, 0)
                passive.active = true
            elseif MyCommon.IsValidTarget(target, spellQ1.range) and MyMenu.Combo.QE:get() then
                ExtendQ(target)
            end
        end
        if MyMenu.Combo.W:get() and SpellManager.CanCastSpell(1) and not passive.active and MyCommon.IsValidTarget(target, spellW.range) then
            if MyCommon.IsInAutoAttackRange(target) then
                local spell = MyMenu.Combo.WN:get() and spellW1 or spellW
                local pred = Prediction.GetPrediction(spell, target)
                if pred then
                    SpellManager.CastOnPosition(vec3(pred.x, target.pos.y, pred.y), 1)
                    passive.active = true
                end
            else 
                local pred = Prediction.GetPrediction(spellW, target)
                if pred then
                    SpellManager.CastOnPosition(vec3(pred.x, target.pos.y, pred.y), 1)
                    passive.active = true
                end
            end
        end
    elseif mode == 3 then
        if MyMenu.Combo.Q:get() and SpellManager.CanCastSpell(0) and not passive.active then
            if MyCommon.IsValidTarget(target, spellQ.range) then
                SpellManager.CastOnUnit(target, 0)
                passive.active = true
            elseif MyCommon.IsValidTarget(target, spellQ1.range) and MyMenu.Combo.QE:get() then
                ExtendQ(target)
            end
        end
        if MyMenu.Combo.E:get() and SpellManager.CanCastSpell(2) and not passive.active and target.pos:dist(player.pos) < 475 + passive.checkRange then
            local pos = GetEPosition(target)
            if pos and not VectorManager.IsZeroPoint(pos) and CanDash(pos) then
                SpellManager.CastOnPosition(pos, 2)
                passive.active = true
            end
        end
        if MyMenu.Combo.W:get() and SpellManager.CanCastSpell(1) and not passive.active and MyCommon.IsValidTarget(target, spellW.range) then
            if MyCommon.IsInAutoAttackRange(target) then
                local spell = MyMenu.Combo.WN:get() and spellW1 or spellW
                local pred = Prediction.GetPrediction(spell, target)
                if pred then
                    SpellManager.CastOnPosition(vec3(pred.x, target.pos.y, pred.y), 1)
                    passive.active = true
                end
            else 
                local pred = Prediction.GetPrediction(spellW, target)
                if pred then
                    SpellManager.CastOnPosition(vec3(pred.x, target.pos.y, pred.y), 1)
                    passive.active = true
                end
            end
        end
    elseif mode == 4 then
        return AQCombo()
    end
end

local function Harass()
    if not MyMenu.Harass.ProTurret:get() and ObjectManager.IsUnderEnemyTurret(player) then
        return
    end
    if MyCommon.GetManaPercent() >= MyMenu.Harass.ManaMin:get() then
        local target = GetTarget(true)
        if not target or target == nil or not MyCommon.IsValidTarget(target) then
            return
        end
        if MyMenu.Harass.Q:get() and SpellManager.CanCastSpell(0) and not passive.active then
            if MyCommon.IsValidTarget(target, spellQ.range) then
                SpellManager.CastOnUnit(target, 0)
                passive.active = true
            elseif MyCommon.IsValidTarget(target, spellQ1.range) and MyMenu.Harass.QE:get() then
                ExtendQ(target)
            end
        end
        if MyMenu.Harass.W:get() and SpellManager.CanCastSpell(1) and not passive.active and MyCommon.IsValidTarget(target, spellW.range) then
            if MyCommon.IsInAutoAttackRange(target) then
                local pred = Prediction.GetPrediction(spellW, target)
                if pred then
                    SpellManager.CastOnPosition(vec3(pred.x, target.pos.y, pred.y), 1)
                    passive.active = true
                end
            else 
                local pred = Prediction.GetPrediction(spellW, target)
                if pred then
                    SpellManager.CastOnPosition(vec3(pred.x, target.pos.y, pred.y), 1)
                    passive.active = true
                end
            end
        end
        if MyMenu.Harass.E:get() and SpellManager.CanCastSpell(2) and not passive.active and target.pos:dist(player.pos) < 475 + passive.checkRange then
            local pos = GetEPosition(target)
            if pos and not VectorManager.IsZeroPoint(pos) and CanDash(pos) then
                SpellManager.CastOnPosition(pos, 2)
                passive.active = true
            end
        end
    end
end

local function Clear()
    ---------- Check Passive
    if passive.active then
        return
    end
    ---------- LaneClear
    if MyMenu.LaneClear.Q:get() and SpellManager.CanCastSpell(0) and not passive.active then
        if MyCommon.GetManaPercent() >= MyMenu.LaneClear.ManaMin:get() then
            local minions = ObjectManager.GetMinions(spellQ.range, TEAM_ENEMY)
            if minions and #minions > 0 then
                for i, minion in ipairs(minions) do
                    if minion and minion ~= nil and MyCommon.IsValidTarget(minion, spellQ.range) then
                        local count = CountMinionHit(minion)
                        if count and count >= MyMenu.LaneClear.QC:get() then
                            SpellManager.CastOnUnit(minion, 0)
                            passive.active = true
                            return
                        end
                    end
                end
            end
        end
    end
    if MyMenu.LaneClear.W:get() and SpellManager.CanCastSpell(2) and not passive.active then
        if MyCommon.GetManaPercent() >= MyMenu.LaneClear.ManaMin:get() then
            local minions = ObjectManager.GetMinions(spellW.range, TEAM_ENEMY)
            if minions and #minions >= MyMenu.LaneClear.WC:get() then
                local BestPos, BestHit = FarmManager.GetBestCircularFarmPosition(spellW.width * 2, minions)
                if BestHit and BestHit >= MyMenu.LaneClear.WC:get() and BestPos then
                    SpellManager.CastOnPosition(vec3(BestPos.x, player.pos.y, BestPos.y), 1)
                    passive.active = true
                    return
                end
            end
        end
    end
    ---------- JungleClear
    if MyCommon.GetManaPercent() >= MyMenu.JungleClear.ManaMin:get() then
        local mobs = ObjectManager.GetMinions(spellQ1.range, TEAM_NEUTRAL)
        if mobs and #mobs > 0 then
            for i, mob in ipairs(mobs) do
                if mob and mob ~= nil and MyCommon.IsValidTarget(mob) then
                    if ShouldEFirst then
                        if MyMenu.JungleClear.E:get() and SpellManager.CanCastSpell(2) and not passive.active and mob.pos:dist(player.pos) < 475 + passive.checkRange then
                            local pos = GetEPosition(mob)
                            if pos and not VectorManager.IsZeroPoint(pos) and CanDash(pos) then
                                SpellManager.CastOnPosition(pos, 2)
                                passive.active = true
                            end
                        end
                        if MyMenu.JungleClear.Q:get() and SpellManager.CanCastSpell(0) and not passive.active then
                            if MyCommon.IsValidTarget(mob, spellQ.range) then
                                SpellManager.CastOnUnit(mob, 0)
                                passive.active = true
                            end
                        end
                        if MyMenu.JungleClear.W:get() and SpellManager.CanCastSpell(1) and not passive.active and  MyCommon.IsValidTarget(mob, spellW.range) then
                            local pred = Prediction.GetPrediction(spellW1, mob)
                            if pred then
                                SpellManager.CastOnPosition(vec3(pred.x, mob.pos.y, pred.y), 1)
                                passive.active = true
                            end
                        end
                    elseif not ShouldEFirst then
                        if MyMenu.JungleClear.Q:get() and SpellManager.CanCastSpell(0) and not passive.active then
                            if MyCommon.IsValidTarget(mob, spellQ.range) then
                                SpellManager.CastOnUnit(mob, 0)
                                passive.active = true
                            end
                        end
                        if MyMenu.JungleClear.E:get() and SpellManager.CanCastSpell(2) and not passive.active and mob.pos:dist(player.pos) < 475 + passive.checkRange then
                            local pos = GetEPosition(mob)
                            if pos and not VectorManager.IsZeroPoint(pos) then
                                SpellManager.CastOnPosition(pos, 2)
                                passive.active = true
                            end
                        end
                        if MyMenu.JungleClear.W:get() and SpellManager.CanCastSpell(1) and not passive.active and  MyCommon.IsValidTarget(mob, spellW.range) then
                            local pred = Prediction.GetPrediction(spellW1, mob)
                            if pred then
                                SpellManager.CastOnPosition(vec3(pred.x, mob.pos.y, pred.y), 1)
                                passive.active = true
                            end
                        end
                    end
                end
            end
        end
    end
end

local function OnMyCreate(missile)
    if missile and missile.type == TYPE_MISSILE then
        local spellData = missile.spell
        if spellData and spellData.owner and spellData.owner.ptr == player.ptr then
            if (spellData.isBasicAttack or (spellData.name and string.lower(spellData.name):find("attack"))) then
                passive.active = false
                return
            end
            if spellData.name and spellData.name:find("LucianRMissile") and spellData.endPos then
                spellR.endPos = vec3(spellData.endPos.x, spellData.endPos.y, spellData.endPos.z)
            end
        end
    end
end

local function OnBuffAdd(buff)
    if buff and buff.name then
        if buff.name == "lucianpassivebuff" then
            passive.active = true
        end
        if buff.name == "lucianr" then
            spellR.active = true
        end
    end
end

local function OnBuffRemove(buff)
    if buff and buff.name then
        if buff.name == "lucianpassivebuff" then
            passive.active = false
        end
        if buff.name == "lucianr" then
            spellR.active = false
        end
    end
end

local function OnMyCastSpell(slot)
    if slot and slot == 2 and player:spellSlot(slot).state == 0 then
        passive.active = true
        OrbManager.BOTOrbwalker.core.reset()
    end
end

local ForcePoint=function(pos)
	 
	  if not pos and orb.core.is_paused()  then
		orb.core.set_pause(0)
	  elseif pos then
		local Pos=player.path.serverPos:lerp(pos,200/player.path.serverPos:dist(pos))	
		orb.core.set_pause(0.1)
		player:move(Pos)
	else
		player:move(mousePos)
	 end
	end

local cxtime = 0
		local SetMana = function()
		QMANA = player:spellSlot(0).level>0 and player.manaCost0 or 0
		WMANA = player:spellSlot(1).level>0 and player.manaCost1 or 0
		EMANA = player:spellSlot(2).level>0 and player.manaCost2 or 0
		RMANA = player:spellSlot(3).level>0 and player.manaCost3 or 0
	end
	
local function castR(target)
player:castSpell('pos', 3, target.pos)
end
	
local function wrCombo()
SetMana()
if  not MyMenu.Misc.AutoLock:get() then
player:move(game.mousePos)
end
	local target = TS.get_result(function(res, obj, dist)
    if dist > 1300 then
      return
    end

        res.obj = obj
        return true
      
    
  end, TS.filter_set[1]).obj 

	if orb.core.can_cast_spell(3) and not player.buff.lucianr then

		local maxobj = nil


		if target and target.pos:dist(player)<1000 then
		 if orb.core.can_cast_spell(1) then
		 player:castSpell('pos', 1, target.pos)
		 end
			
			castR(target)
		end
	end

		if  not  MyMenu.Misc.AutoLock:get()  and player.buff['lucianr'] and cxtime+3 > game.time  and player:spellSlot(3).state == 0 then 
			player:castSpell('self', 3)
			LucianCastDirection = nil
			return
		end

		if   MyMenu.Misc.AutoLock:get() and not player.buff['lucianr']   then 
		
			player:move(mousePos)
		
			if MyCommon.IsValidTarget(target) then
				if orb.core.can_cast_spell(2) and  orb.core.can_cast_spell(1) and orb.core.can_cast_spell(3) and target.pos:dist(player.pos) > 1100 and target.pos:dist(player.pos)  < 1100+400  and player.mana > WMANA +RMANA+EMANA then
					local gappos = player.pos - (player.pos-target.pos):norm()*425
				
						player:castSpell("pos", 2, gappos)
					
				end
				if orb.core.can_cast_spell(1) and orb.core.can_cast_spell(3) and target.pos:dist(player.pos)  < 1100 and player.mana > WMANA +RMANA then
					player:castSpell("pos", 1, target.pos)
				end
				if orb.core.can_cast_spell(3) and target.pos:dist(player.pos)  < 1100 then
					player:castSpell("pos", 3, target.pos)
					
					cxtime = game.time
				end
				
			end
			
		elseif   MyMenu.Misc.AutoLock:get() and player.buff['lucianr'] and LucianCastDirection then 
		--	print(1111)
			if evade and evade.core.is_active() then player:move(mousePos) return end
			local endpos = player.path.
              serverPos + LucianCastDirection * 1100
			-- print(222)
			if  MyCommon.IsValidTarget(target) and endpos and target.pos:distSqr(player.pos)<=(1100 + 100 )^2 then
				local dist = target.path.serverPos:dist(mousePos)
				if dist > 1100 then dist = 950 end
				local pos =  preds.core.get_pos_after_time(target,0.25):toGame3D()
				local Walkerpos=endpos + (pos-endpos):norm()*(pos:dist(endpos)+dist)
				if Walkerpos then
					
					ForcePoint(Walkerpos)
				end
			else
				player:move(mousePos)
			end
		end



end


local function OnMyProcessSpellCast(spellData)
    if spellData and spellData.owner and spellData.owner.ptr == player.ptr and spellData.name then
        local name = string.lower(spellData.name)
		if IsSpellCast(name) then
			passive.active = true
        end
		local spell = spellData
		--print(spell.name)
        -- local shouldReset, t = ShouldResetAttackTime(name)
        -- if shouldReset then
            -- local time = (t > 0 and ((spellData.windUpTime + spellData.animationTime) / 2 + 0.01) or 0.01) + network.latency / 2
            -- DelayAction.Add(function()        
                -- OrbManager.BOTOrbwalker.core.reset()  
            -- end, time)
        -- end
        if name == "lucianr" and spellData.endPos then
            spellR.endPos = vec3(spellData.endPos.x, spellData.endPos.y, spellData.endPos.z)
        end
		if spell and spell.name and spell.owner and spell.owner == player then
          -- print(spell.name)
			if spell and spell.name and spell.owner and spell.owner == player and spell.name:find("LucianR") then
				local direction = (spell.endPos - player.path.serverPos):norm()
				LucianCastDirection = direction
				print(LucianCastDirection)

				DelayAction.Add(function() LucianCastDirection = nil end, 3)
			end
		end
		
    end
end

local function OnMyTick()
    CheckBuff()
    if player.isDead or player.isRecalling or chat.isOpened or player.path.isDashing then
        return
    end
    if spellR.active then
        AutoLock()
        return
    end
	if  MyMenu.Misc.Lock:get() then
	wrCombo()
	end
	
    -- �����Զ�R��ɱ�߼�
    AutoRKillSteal()
    
    if MyMenu.Key.SemiR:get() and SpellManager.CanCastSpell(3) then
        local target = MyCommon.GetTarget(spellR.range)
        if target and target ~= nil and MyCommon.IsValidTarget(target, spellR.range) then
            local pred = Prediction.GetPrediction(spellR, target)
            if pred then
                SpellManager.CastOnPosition(vec3(pred.x, target.pos.y, pred.y), 3)
                return
            end
        end
    end
    if DelayTick.CanTickEvent() then
        SetStatus()
        KillSteal()
    end
    if MyMenu.Key.Combo:get() then
	    --AQCombo()
        Combo()
    end
    if MyMenu.Key.Harass:get() or (MyMenu.Key.LaneClear:get() and MyMenu.Harass.ProAllow:get()) then
        Harass()
    end
    if MyMenu.Key.LaneClear:get() and FarmManager.Enabled then
        Clear()
    end
end

local function OnMyAfterAttack(target)
    if target and target ~= nil and MyCommon.IsValidTarget(target) then
        -- AQ起手模弝下的普攻坎立坳Q逻辑
        if MyMenu.Key.Combo:get() and MyMenu.Combo.ComboMode:get() == 4 and target.type == TYPE_HERO then
            if MyMenu.Combo.Q:get() and SpellManager.CanCastSpell(0) and MyCommon.IsValidTarget(target, spellQ.range) then
                SpellManager.CastOnUnit(target, 0)
                passive.active = true
                return
            end
        end
        
        if (target.type == TYPE_TURRET or target.type == TYPE_INHIB or target.type == TYPE_NEXUS) and target.team == TEAM_ENEMY and MyCommon.IsValidTarget(target) then
            if MyMenu.Key.TurretClear:get() and FarmManager.Enabled then
                if MyCommon.GetManaPercent() >= MyMenu.TurretClear.ManaMin:get() then
                    if #ObjectManager.GetEnemiesInRange(1200) == 0 then
                        if MyMenu.TurretClear.E:get() and SpellManager.CanCastSpell(2) then
                            local castPos = vec3(0, 0, 0)
                            local pos = VectorManager.Extend(player.pos, game.mousePos, 200)
                            if pos and not VectorManager.IsZeroPoint(pos) then
                                SpellManager.CastOnPosition(pos, 2)
                                passive.active = true
                                return
                            end
                        elseif MyMenu.TurretClear.W:get() and SpellManager.CanCastSpell(1) then
                            SpellManager.CastOnPosition(game.mousePos, 1)
                            passive.active = true
                            return
                        end
                    end
                end
            end
        end
    end
end

local function OnMyDraw()
    if player.isDead or chat.isOpened then
        return
    end
    if MyMenu.Draw.Q:get() and MyCommon.CanDrawCircle(spellQ1.range) then
        if MyMenu.Draw.Ready:get() then
            if SpellManager.CanCastSpell(0) then
                graphics.draw_circle(player.pos, spellQ.range, 2, graphics.argb(255, 255, 128, 128), 100)
                graphics.draw_circle(player.pos, spellQ1.range, 2, graphics.argb(255, 255, 128, 128), 100)
            end
        else
            graphics.draw_circle(player.pos, spellQ.range, 2, graphics.argb(255, 255, 128, 128), 100)
            graphics.draw_circle(player.pos, spellQ1.range, 2, graphics.argb(255, 255, 128, 128), 100)
        end
    end
    if MyMenu.Draw.W:get() and MyCommon.CanDrawCircle(spellW.range) then
        if MyMenu.Draw.Ready:get() then
            if SpellManager.CanCastSpell(1) then
                graphics.draw_circle(player.pos, spellW.range, 2, graphics.argb(255, 255, 128, 128), 100)
            end
        else
            graphics.draw_circle(player.pos, spellW.range, 2, graphics.argb(255, 255, 128, 128), 100)
        end
    end
    if MyMenu.Draw.R:get() and MyCommon.CanDrawCircle(spellR.range) then
        if MyMenu.Draw.Ready:get() then
            if SpellManager.CanCastSpell(3) then
                graphics.draw_circle(player.pos, spellR.range, 2, graphics.argb(255, 255, 128, 128), 100)
            end
        else
            graphics.draw_circle(player.pos, spellR.range, 2, graphics.argb(255, 255, 128, 128), 100)
        end
    end
    if MyMenu.Draw.ComboMode:get() then
        local drawWidth = graphics.width - (minimap.width * 2)
        local drawHeight = graphics.height - (minimap.height / 5)
        local text = "��дģʽ"
        if MyMenu.Combo.ComboMode:get() == 2 then
            text = "Priority E"
        elseif MyMenu.Combo.ComboMode:get() == 3 then
            text = "Priority Q"
        elseif MyMenu.Combo.ComboMode:get() == 4 then
            text = "AQ ����"
        end
        -- �������ɫ
        local deepPinkColor = graphics.argb(255, 255, 0, 255)
        graphics.draw_text_2D("Mode: "..text, 16, drawWidth, drawHeight - 60, deepPinkColor)
    end
    if MyMenu.Draw.DIEnabled:get() then
        local targets = ObjectManager.GetEnemyHeroes()
        if targets and #targets > 0 then
            for i, target in ipairs(targets) do
                if target and MyCommon.IsValidTarget(target) and target.isVisible and target.isOnScreen and target.health > 0 and not BuffManager.HasBuffOfType(target, 17) then
                    local qDamage = SpellManager.CanCastSpell(0) and GetQDamage(target) or 0
                    local eDamage = SpellManager.CanCastSpell(2) and (CalculateManager.GetAutoAttackDamage(player, target) * 1.5) or 0
                    -- ��ȫ�ػ�ȡR�˺�����ʹ��pcall
                    local rDamage = 0
                    if SpellManager.CanCastSpell(3) then
                        -- ���Լ���R�˺�����ʹ�ñ��������������
                        local critChance = player.critChance or 0
                        local level = player:spellSlot(3).level
                        if level > 0 then
                            local baseDmg = ({20, 40, 60})[level]
                            local adScaling = 0.25
                            local shotDmg = baseDmg + (adScaling * MyCommon.GetTotalAD())
                            local critDmg = player.critDamage or 1.75
                            local avgDmgPerShot = shotDmg * (1 + critChance * (critDmg - 1))
                            local shots = ({20, 25, 30})[level]
                            local totalDmg = avgDmgPerShot * shots * 0.5
                            rDamage = CalculateManager.CalculatePhysicalDamage(target, totalDmg)
                        end
                    end
                    local totalDamage = qDamage + eDamage + rDamage
                    
                    if totalDamage > 0 then
                        local hp_bar_pos = target.barPos
                        if hp_bar_pos then
                            local xPos = hp_bar_pos.x + 165
                            local yPos = hp_bar_pos.y + 122.5
                            if target.charName and target.charName == "Annie" then
                                yPos = yPos + 2
                            end
                            local remainHealth = target.health - totalDamage
                            local x1 = xPos + ((target.health / target.maxHealth) * 104)
                            local x2 = xPos + (((remainHealth > 0 and remainHealth or 0) / target.maxHealth) * 103.4)
                            graphics.draw_line_2D(x1, yPos, x2, yPos, 11, (remainHealth > 0 and 0xFFF2781E or 0xFFFF0000))
                        end
                    end
                end
            end
        end
    end
end

cb.add(cb.create_missile, OnMyCreate)
PlayerBuff.AddPlayerBuffAddCallback(OnBuffAdd)
PlayerBuff.AddPlayerBuffRemoveCallback(OnBuffRemove)
cb.add(cb.castspell, OnMyCastSpell)
cb.add(cb.spell, OnMyProcessSpellCast)
OrbManager.AddFasterTickCallback(OnMyTick)
OrbManager.AddAfterAttackCallback(OnMyAfterAttack)
cb.add(cb.draw, OnMyDraw)



return lucianPlugin

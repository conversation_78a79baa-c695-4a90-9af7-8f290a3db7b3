

local ove_0_10 = module.load(header.id, "common/common")
local ove_0_11 = (function()
	local slot_5_0 = {}

	slot_5_0.__index = slot_5_0

	return setmetatable(slot_5_0, {
		__call = function(arg_6_0, ...)
			local slot_6_0 = setmetatable({}, slot_5_0)

			if slot_5_0.__init then
				slot_5_0.__init(slot_6_0, ...)
			end

			return slot_6_0
		end
	})
end)()

function ove_0_11.__init(arg_7_0)
	arg_7_0.ResetTimerSpellsBlinks = os.clock()
	arg_7_0.targetBlink = {}
	arg_7_0.isDashTarget = {}
	arg_7_0.blinks = {
		{
			delay = 0.25,
			name = "ezreale",
			range = 475,
			delay2 = 0.8
		},
		{
			delay = 0.25,
			name = "deceive",
			range = 400,
			delay2 = 0.8
		},
		{
			delay = 0.25,
			name = "riftwalk",
			range = 700,
			delay2 = 0.8
		},
		{
			delay = 0.025,
			name = "summonerflash",
			range = 425,
			delay2 = 0.05
		},
		{
			delay = 1.5,
			name = "gate",
			range = 5500,
			delay2 = 1.5
		},
		{
			delay = 0.25,
			name = "katarinae",
			range = 700,
			delay2 = 0.8
		},
		{
			delay = 0.25,
			name = "elisespideredescent",
			delay2 = 0.8,
			range = math.huge
		},
		{
			delay = 0.25,
			name = "elisespidere",
			delay2 = 0.8,
			range = math.huge
		},
		{
			delay = 1.5,
			name = "fiddlesticksr",
			range = 800,
			delay2 = 0.8
		},
		{
			delay = 0.5,
			name = "pyker",
			range = 750,
			delay2 = 0.5
		},
		{
			delay = 0.25,
			name = "zoer",
			range = 575,
			delay2 = 0.25
		},
		{
			delay = 0.5,
			name = "viegor",
			range = 575,
			delay2 = 0.5
		}
	}

	cb.add(cb.tick, function()
		arg_7_0:OnTick()
	end)
	cb.add(cb.spell, function(arg_9_0)
		arg_7_0:OnProcessSpell(arg_9_0)
	end)
	cb.add(cb.path, function(arg_10_0)
		arg_7_0:OnCreatePath(arg_10_0)
	end)
end

function ove_0_11.PathLength(arg_11_0, arg_11_1)
	if not arg_11_1 then
		return 0
	end

	local slot_11_0 = 0

	for iter_11_0 = 0, arg_11_1.count - 1 do
		slot_11_0 = slot_11_0 + arg_11_1.point[iter_11_0]:dist(arg_11_1.point[iter_11_0 + 1])
	end

	return slot_11_0
end

function ove_0_11.OnTick(arg_12_0)
	if arg_12_0.ResetTimerSpellsBlinks and os.clock() > arg_12_0.ResetTimerSpellsBlinks + 0.95 then
		arg_12_0.targetBlink = {}
	end

	for iter_12_0, iter_12_1 in ipairs(ove_0_10.GetEnemyHeroes()) do
		if iter_12_1 ~= nil and arg_12_0.isDashTarget[iter_12_1.networkID] then
			local slot_12_0 = arg_12_0.isDashTarget[iter_12_1.networkID]

			if slot_12_0 and slot_12_0.endT and os.clock() > slot_12_0.endT then
				arg_12_0.isDashTarget[iter_12_1.networkID] = {}
			end
		end
	end
end

function ove_0_11.OnProcessSpell(arg_13_0, arg_13_1)
	if arg_13_1 and arg_13_1.owner and arg_13_1.owner.type == player.type and arg_13_1.owner.ptr ~= player.ptr then
		for iter_13_0, iter_13_1 in ipairs(arg_13_0.blinks) do
			local slot_13_0 = vec3(arg_13_1.startPos.x, arg_13_1.startPos.y, arg_13_1.startPos.z)
			local slot_13_1 = vec3(arg_13_1.endPos.x, arg_13_1.endPos.y, arg_13_1.endPos.z)
			local slot_13_2 = slot_13_0 + (slot_13_1 - slot_13_0):norm() * math.min(iter_13_1.range, slot_13_0:dist(slot_13_1))

			if arg_13_1.name:lower() == iter_13_1.name and not navmesh.isWall(slot_13_2) then
				arg_13_0.targetBlink[arg_13_1.owner.networkID] = {
					isblink = true,
					duration = iter_13_1.delay,
					endT = os.clock() + iter_13_1.delay,
					endT2 = os.clock() + iter_13_1.delay2,
					startPos = arg_13_1.owner,
					endPos = slot_13_2
				}
				arg_13_0.ResetTimerSpellsBlinks = os.clock() + arg_13_1.windUpTime
			end
		end
	end
end

function ove_0_11.OnCreatePath(arg_14_0, arg_14_1)
	local slot_14_0 = {}

	if not arg_14_1 then
		return
	end

	if arg_14_1 and arg_14_1.type == player.type and arg_14_1.path.isDashing and arg_14_1.team ~= player.team then
		slot_14_0.startPos = arg_14_1.path.serverPos
		slot_14_0.endPos = arg_14_1.path.endPos
		slot_14_0.speed = arg_14_1.path.dashSpeed
		slot_14_0.pathing = arg_14_1.path
		slot_14_0.startT = os.clock() - network.latency
		slot_14_0.valid = true

		local slot_14_1 = ove_0_10.GetDistance(slot_14_0.startPos, slot_14_0.endPos)

		slot_14_0.endT = slot_14_0.startT + slot_14_1 / slot_14_0.speed
		arg_14_0.isDashTarget[arg_14_1.networkID] = slot_14_0
	end
end

function ove_0_11.IsDashing(arg_15_0, arg_15_1, arg_15_2, arg_15_3, arg_15_4, arg_15_5)
	if not arg_15_1 then
		return
	end

	local slot_15_0 = false
	local slot_15_1 = false
	local slot_15_2

	if arg_15_0.isDashTarget[arg_15_1.networkID] then
		local slot_15_3 = arg_15_0.isDashTarget[arg_15_1.networkID]
		local slot_15_4 = arg_15_0:PathLength(slot_15_3.pathing)

		if slot_15_3.endT and slot_15_3.endT >= os.clock() then
			slot_15_0 = true

			if slot_15_4 > 200 and arg_15_2 / 2 + ove_0_10.GetDistance(arg_15_5, slot_15_3.endPos) / arg_15_4 - 0.25 <= ove_0_10.GetDistance(arg_15_1, slot_15_3.endPos) / slot_15_3.speed + arg_15_3 / arg_15_1.moveSpeed then
				slot_15_1 = true
				slot_15_2 = slot_15_3.endPos
			end
		end
	end

	if arg_15_0.targetBlink[arg_15_1.networkID] then
		local slot_15_5 = arg_15_0.targetBlink[arg_15_1.networkID]

		if slot_15_5.endT and slot_15_5.endT >= os.clock() then
			slot_15_0 = true

			if slot_15_5.isblink then
				if slot_15_5.endT - os.clock() <= arg_15_2 + ove_0_10.GetDistance(arg_15_5, slot_15_5.endPos) / arg_15_4 then
					slot_15_2 = vec3(slot_15_5.endPos.x, arg_15_1.pos.y, slot_15_5.endPos.z)
					slot_15_1 = arg_15_3 > arg_15_1.moveSpeed * (arg_15_2 + ove_0_10.GetDistance(arg_15_5, slot_15_5.endPos) / arg_15_4 - (slot_15_5.endT2 - os.clock()))
				end

				if slot_15_5.endT - os.clock() >= arg_15_2 + ove_0_10.GetDistance(arg_15_5, slot_15_5.startPos) / arg_15_4 and not slot_15_1 then
					slot_15_2 = vec3(slot_15_5.startPos.x, arg_15_1.pos.y, slot_15_5.startPos.z)
					slot_15_1 = true
				end
			end
		end
	end

	return slot_15_0, slot_15_2, slot_15_1
end

ove_0_11:__init()

return {
	GetIsDashing = function(arg_16_0, arg_16_1, arg_16_2, arg_16_3, arg_16_4)
		return ove_0_11:IsDashing(arg_16_0, arg_16_1, arg_16_2, arg_16_3, arg_16_4)
	end,
	PathLength = function(arg_17_0)
		return ove_0_11:PathLength(arg_17_0)
	end
}

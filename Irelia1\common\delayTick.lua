
local ove_0_10 = {}

ove_0_10.tickNumber = 0

function ove_0_10.OnlyTickOnce()
	if ove_0_10.tickNumber == 2 then
		return true
	end

	return false
end

function ove_0_10.OnlyTickTwice()
	if ove_0_10.tickNumber == 0 or ove_0_10.tickNumber == 3 then
		return true
	end

	return false
end

function ove_0_10.CanTickEvent()
	if ove_0_10.tickNumber == 0 or ove_0_10.tickNumber == 2 or ove_0_10.tickNumber == 4 then
		return true
	end

	return false
end

function ove_0_10.CanDelayTick()
	if ove_0_10.tickNumber == 0 or ove_0_10.tickNumber == 2 or ove_0_10.tickNumber == 4 then
		return true
	end

	return false
end

local function ove_0_11()
	ove_0_10.tickNumber = ove_0_10.tickNumber + 1

	if ove_0_10.tickNumber > 4 then
		ove_0_10.tickNumber = 0
	end
end

cb.add(cb.tick, ove_0_11)

return ove_0_10

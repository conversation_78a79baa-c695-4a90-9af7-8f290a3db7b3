
local ove_0_5 = require("menuconfig/main")("hb_poppy", "Hanbot Poppy")

ove_0_5:menu("q", "Hammer Shock (Q)")
ove_0_5.q:boolean("combat", "Use in Combat Mode", true)
ove_0_5.q:boolean("panic_clear", "Use in Panic Clear", true)
ove_0_5:menu("w", "Steadfast Presence (W)")
ove_0_5.w:header("h_interupt_combat", "Interupt Dashes on:")

for iter_0_0 = 0, objManager.enemies_n - 1 do
	local ove_0_6 = objManager.enemies[iter_0_0]

	ove_0_5.w:boolean(ove_0_6.charName, ove_0_6.charName, true)
end

ove_0_5:menu("e", "Heroic Charge (E)")
ove_0_5.e:keybind("force_e", "Use E in Next Combo", "C", nil)
ove_0_5.e.force_e:set("tooltip", "Combat Mode must be active for Force to work.")
ove_0_5.e:boolean("combat", "Use in Combat", true)
ove_0_5.e:boolean("flash", "Use flash combo on selected target", true)
ove_0_5.e:boolean("panic_clear", "Use in Panic Clear", true)
ove_0_5:menu("r", "Keeper's Verdict (R)")
ove_0_5.r:slider("min_hit", "Snap Cast if can hit X", 2, 2, 5, 1)
ove_0_5.r:boolean("charged", "Allow Auto R Charged Cast", true)
ove_0_5:boolean("use_hydra", "Use Hydra Items", true)

return ove_0_5

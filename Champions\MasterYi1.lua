local masterYiPlugin = {}

-- Load Spell
local spellQ = {
    range = 600,
}

local spellW = {
    range = player.attackRange + player.boundingRadius,
}

local spellE = {
    range = player.attackRange + player.boundingRadius,
}

local spellR = {
    range = 600,
}

-- Load Module
local menu = module.load("Brian", "menu");
local DelayAction = module.load("NickAIO", "Core/DelayAction")
local DelayTick = module.load("NickAIO", "Core/DelayTick")
local Prediction = module.load("NickAIO", "Core/Prediction")
local BuffManager = module.load("NickAIO", "Library/BuffManager")
local CalculateManager = module.load("NickAIO", "Library/CalculateManager")
local FarmManager = module.load("NickAIO", "Library/FarmManager")
local ItemManager = module.load("NickAIO", "Library/ItemManager")
local NetManager = module.load("NickAIO", "Library/NetManager")
local ObjectManager = module.load("NickAIO", "Library/ObjectManager")
local OrbManager = module.load("NickAIO", "Library/OrbManager")
local SpellManager = module.load("NickAIO", "Library/SpellManager")
local VectorManager = module.load("NickAIO", "Library/VectorManager")
local MyCommon = module.load("NickAIO", "Library/ExtraManager")

local MyMenu

function masterYiPlugin.Load(GlobalMenu)
    MyMenu = GlobalMenu

    MyMenu:menu("Combo", "Combo Settings")
    MyMenu.Combo:header("SpellHeader", "Spell Core")
    MyMenu.Combo:boolean("Q", "Use Q", true)
    MyMenu.Combo:dropdown("QMode", "^ Cast Mode", 1, {"Logic", "Always Cast"}) 
    MyMenu.Combo:boolean("W", "Use W", true)
    MyMenu.Combo:boolean("E", "Use E", true)
    MyMenu.Combo:boolean("R", "Use R", true)

    MyMenu:menu("Harass", "Harass Settings")
    MyMenu.Harass:header("SpellHeader", "Spell Core")
    MyMenu.Harass:boolean("Q", "Use Q", true)
    MyMenu.Harass:boolean("E", "Use E", false)
    MyMenu.Harass:header("ManaHeader", "Mana Manager")
    MyMenu.Harass:slider("ManaMin", "Player ManaPercent >= x%", 60, 1, 100, 1)
    MyMenu.Harass:header("ProHeader", "PRO Mode")
    MyMenu.Harass:boolean("ProAllow", "Enabled PRO Harass Mode", false)
    MyMenu.Harass.ProAllow:set("tooltip", "PRO Mode => Allow Use Spell Harass Enemy on LaneClear Mode")
    MyMenu.Harass:boolean("ProTurret", "Allow Under Turret Harass", false)

    MyMenu:menu("LaneClear", "LaneClear Settings")
    MyMenu.LaneClear:boolean("Q", "Use Q", true)
    MyMenu.LaneClear:slider("QCount", "Min Hit Count >= x", 4, 1, 9, 1)
    MyMenu.LaneClear:header("ManaHeader", "Mana Manager")
    MyMenu.LaneClear:slider("ManaMin", "Player ManaPercent >= x%", 60, 1, 100, 1)

    MyMenu:menu("JungleClear", "JungleClear Settings")
    MyMenu.JungleClear:header("SpellHeader", "Spell Core")
    MyMenu.JungleClear:boolean("Q", "Use Q", true)
    MyMenu.JungleClear:boolean("E", "Use E", true)
    MyMenu.JungleClear:header("ManaHeader", "Mana Manager")
    MyMenu.JungleClear:slider("ManaMin", "Player ManaPercent >= x%", 20, 1, 100, 1)

    FarmManager.Load(MyMenu)

    MyMenu:menu("KillSteal", "KillSteal Settings")
    MyMenu.KillSteal:header("SpellHeader", "Spell Core")
    MyMenu.KillSteal:boolean("Q", "Use Q", true)

    MyMenu:menu("Draw", "Draw Settings")
    MyMenu.Draw:header("RangeHeader", "Spell Range")
    MyMenu.Draw:boolean("Q", "Draw Q Range", true)
    MyMenu.Draw:boolean("Ready", "Only Spell Ready", true)
end

local function GetQDamage(target)
    if not target or target == nil then
        return 0
    end
    local level = player:spellSlot(0).level
    if level == 0 then
        return 0
    end
    local dmg = ({25, 60, 95, 130, 165})[level] +  MyCommon.GetTotalAD()
    if target.type == TYPE_MINION then
        local extraDMG = ({75, 100, 125, 150, 175})[level]
        dmg = dmg + extraDMG
    end
    return CalculateManager.CalculatePhysicalDamage(target, dmg)
end

local function KillSteal()
    if MyMenu.KillSteal.Q:get() and SpellManager.CanCastSpell(0) and not player.path.isDashing then
        local targets = ObjectManager.GetEnemiesInRange(spellQ.range, player)
        for i, target in ipairs(targets) do
            if MyCommon.IsValidTarget(target, spellQ.range) and not MyCommon.IsUnKillAble(target) then
                local qDMG = GetQDamage(target)
                if target.health and target.health < qDMG then
                    SpellManager.CastOnUnit(target, 0)
                    return
                end
            end
        end
    end
end

local function Combo()
    if MyMenu.Combo.E:get() and SpellManager.CanCastSpell(2) then
        local orbTarget = OrbManager.BOTOrbwalker.combat.target
        if orbTarget and orbTarget ~= nil and MyCommon.IsValidTarget(orbTarget) and MyCommon.IsInAutoAttackRange(orbTarget) and orbTarget.type == TYPE_HERO then
            SpellManager.CastOnPlayer(2)
            return
        end
    end
    if MyMenu.Combo.Q:get() and SpellManager.CanCastSpell(0) then
        local target = MyCommon.GetTarget(600)
        if target and MyCommon.IsValidTarget(target, 600) and not target.path.isDashing then
            if MyMenu.Combo.QMode:get() == 2 or target.charName == "Jinx" or target.charName == "Jhin"then
                SpellManager.CastOnUnit(target, 0)
                return
            elseif MyMenu.Combo.QMode:get() == 1 then
                if target.health and target.health < (CalculateManager.GetAutoAttackDamage(player, target) + GetQDamage(target)) then
                    SpellManager.CastOnUnit(target, 0)
                    return
                end
                -- Not Ult Active
                if not BuffManager.HasBuff(player, "Highlander") then
                    SpellManager.CastOnUnit(target, 0)
                    return
                end
                -- if Ult Active
                if target.path and target.pos and target.path.point2D and target.moveSpeed then
                    local nextEnemPath = target.path.point2D[0]
                    local dist = player.pos2D:dist(target.pos2D)
                    local distToNext = nextEnemPath:dist(player.pos2D)
                    if distToNext <= dist then
                        return
                    end
                    local msDif = player.moveSpeed - target.moveSpeed
                    if msDif <= 0 and not MyCommon.IsInAutoAttackRange(target) and not OrbManager.IsWindingUp() then
                        SpellManager.CastOnUnit(target, 0)
                        return
                    end
                    local reachIn = dist / msDif
                    if reachIn > 4 then
                        SpellManager.CastOnUnit(target, 0)
                        return
                    end
                end
            end
        end
    end
    if MyMenu.Combo.R:get() and SpellManager.CanCastSpell(3) then
        local target = MyCommon.GetTarget(1400)
        if target and MyCommon.IsValidTarget(target, 1400) and not MyCommon.IsUnKillAble(target) then
            if target.pos:dist(game.mousePos) < 300 and target.pos:dist(player.pos) > 600 then
                if ItemManager.HasItem("youmusblade") and ItemManager.CanUseItem("youmusblade") then
                    ItemManager.CastOnPlayer("youmusblade")
                    SpellManager.CastOnPlayer(3)
                    return
                end
                SpellManager.CastOnPlayer(3)
                return
            end
        end
    end
end

local function Harass()
    if not MyMenu.Harass.ProTurret:get() and ObjectManager.IsUnderEnemyTurret(player) then
        return
    end
    if MyCommon.GetManaPercent() >= MyMenu.Harass.ManaMin:get() then
        if MyMenu.Harass.E:get() and SpellManager.CanCastSpell(2) then
            local orbTarget = OrbManager.BOTOrbwalker.combat.target
            if orbTarget and orbTarget ~= nil and MyCommon.IsValidTarget(orbTarget) and MyCommon.IsInAutoAttackRange(orbTarget) and orbTarget.type == TYPE_HERO then
                SpellManager.CastOnPlayer(2)
                return
            end
        end
        if MyMenu.Harass.Q:get() and SpellManager.CanCastSpell(0) then
            local target = MyCommon.GetTarget(600)
            if target and MyCommon.IsValidTarget(target, spellQ.range) then
                SpellManager.CastOnUnit(target, 0)
                return
            end
        end
    end
end

local function Clear()
    if MyMenu.LaneClear.Q:get() and SpellManager.CanCastSpell(0) then
        if MyCommon.GetManaPercent() >= MyMenu.LaneClear.ManaMin:get() then
            local minions = ObjectManager.GetMinions(spellQ.range, TEAM_ENEMY)
            if minions and #minions > 0 and #minions >= MyMenu.LaneClear.QCount:get() then
                for i, minion in ipairs(minions) do
                    if minion and MyCommon.IsValidTarget(minion, spellQ.range) then
                        SpellManager.CastOnUnit(minion, 0)
                        return
                    end
                end
            end
        end
    end
    if MyMenu.JungleClear.Q:get() and SpellManager.CanCastSpell(0) then
        if MyCommon.GetManaPercent() >= MyMenu.JungleClear.ManaMin:get() then
            local mobs = ObjectManager.GetMinions(spellQ.range, TEAM_NEUTRAL)
            if mobs and #mobs > 0 then
                if #mobs > 2 then
                    for i, mob in ipairs(mobs) do
                        if mob and MyCommon.IsValidTarget(mob, spellQ.range) then
                            SpellManager.CastOnUnit(mob, 0)
                            return
                        end
                    end
                else
                    for i, mob in ipairs(mobs) do
                        if mob and MyCommon.IsValidTarget(mob, spellQ.range) and MyCommon.IsBigMob(mob) then
                            SpellManager.CastOnUnit(mob, 0)
                            return
                        end
                    end
                end
            end
        end
    end
end

local function OnMyTick()
    if player.isDead or player.isRecalling or chat.isOpened or OrbManager.IsWindingUp(30) then
        return
    end
    if DelayTick.CanTickEvent() then
        KillSteal()
    end
    if MyMenu.Key.Harass:get() then
        Harass()
    end
    if MyMenu.Key.Combo:get() then
        Combo()
    end
    if DelayTick.CanTickEvent() then
        if MyMenu.Key.LaneClear:get() then
            if MyMenu.Harass.ProAllow:get() then
                Harass()
            end
            if FarmManager.Enabled then
                Clear()
            end
        end
    end
end

local function OnMyAfterAttack(target)
    if target and target ~= nil and MyCommon.IsValidTarget(target) then
        if target.type == TYPE_HERO then
            if MyMenu.Key.Combo:get() then
                if MyMenu.Combo.W:get() and SpellManager.CanCastSpell(1) then
                    if target and MyCommon.IsValidTarget(target) then
                        SpellManager.CastOnPlayer(1)
                        --DelayAction.Add(function() player:move(game.mousePos) end, 0.2) -- removing this seems to fix it
                        return
                    end
                end
            end
        elseif target.type == TYPE_MINION and target.team == TEAM_NEUTRAL then
            if MyMenu.Key.LaneClear:get() and FarmManager.Enabled then
                if MyMenu.JungleClear.E:get() and SpellManager.CanCastSpell(2) then
                    if target and target ~= nil and MyCommon.IsValidTarget(target) and MyCommon.IsInAutoAttackRange(target) then
                        SpellManager.CastOnPlayer(2)
                        return
                    end
                end
            end
        end
    end
end

local function OnMyPath(obj)
    if MyMenu.Key.Combo:get() and MyMenu.Combo.Q:get() and SpellManager.CanCastSpell(0) and MyCommon.IsValidTarget(obj, spellQ.range) and obj.type == TYPE_HERO and obj.path and obj.path.isActive and obj.path.isDashing then
        SpellManager.CastOnUnit(obj, 0)
        return
    end
end

local function OnMyDraw()
    if player.isDead or chat.isOpened then
        return
    end
    if MyMenu.Draw.Q:get() and MyCommon.CanDrawCircle(spellQ.range) then
        if MyMenu.Draw.Ready:get() then
            if SpellManager.CanCastSpell(0) then
                graphics.draw_circle(player.pos, spellQ.range, 2, 0xff1e90ff, 100)
            end
        else
            graphics.draw_circle(player.pos, spellQ.range, 2, 0xff1e90ff, 100)
        end
    end
end

OrbManager.AddFasterTickCallback(OnMyTick)
OrbManager.AddAfterAttackCallback(OnMyAfterAttack)
cb.add(cb.path, OnMyPath)
cb.add(cb.draw, OnMyDraw)


return masterYiPlugin



local ove_0_10 = module.load("Kloader", "Champions/Zed/Util/Detection")
local ove_0_11 = player
local ove_0_12 = {
	range = 290,
	Slot = player:spellSlot(2)
}

function ove_0_12.Cast(arg_5_0)
	-- print 5
	if not ove_0_12.Ready() or not arg_5_0 or arg_5_0.isDead then
		return
	end

	if ove_0_10.Rpos and ove_0_10.Rpos:dist(arg_5_0.pos) < ove_0_12.range then
		player:castSpell("self", 2)
	end

	if ove_0_10.Wpos and ove_0_10.Wpos:dist(arg_5_0.pos) < ove_0_12.range then
		player:castSpell("self", 2)
	end

	if arg_5_0.pos and arg_5_0.pos:dist(ove_0_11.pos) < ove_0_12.range then
		player:castSpell("self", 2)
	end
end

function ove_0_12.Ready()
	-- print 6
	return ove_0_12.Slot.state == 0
end

function ove_0_12.Cost()
	-- print 7
	return ove_0_11.manaCost2
end

function ove_0_12.Level()
	-- print 8
	return player:spellSlot(2).level
end

return ove_0_12

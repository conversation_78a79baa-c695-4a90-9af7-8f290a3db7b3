--local menu = require("menuconfig/main")("azirxdxdxd", "Creator of Elo - Azir")
local menu = menu("<PERSON>", "[<PERSON>] <PERSON>")
--menu:set("icon", player.iconSquare)
menu:menu("keys", "Keys")
menu.keys:header("h_keys", "Hotkeys")
menu.keys:keybind("weq", "WEQ to Mouse", "E", nil)
menu.keys:keybind("shuffle", "Shurima Shuffle", "A", nil)
menu.keys:keybind("marker", "Set Shurima Shuffle Marker", "S", nil)
menu.keys:keybind("safety", "Safety Mode", nil, "H")
menu.keys.safety:set("tooltip", "This will automatically cast R if any target comes within melee range.")
menu.keys:keybind("lane_clear", "Lane Clear", "Caps Lock", nil)
menu.keys:keybind("jungle_clear", "Jungle Clear", "Z", nil)
menu:menu("q", "Conquering Sands (Q)")


--menu.q:set("icon", player:spellSlot(0).icon)
menu.q:header("h_farm", "Farming Options")
menu.q:boolean("lane_clear", "Use in Lane Clear", true)
menu.q:boolean("jungle_clear", "Use in Jungle Clear", true)
menu.q:boolean("farm_assist", "Enable Farm Assist", true)
menu.q:header("h_combat", "Combat Options")
menu.q:boolean("combat", "Use in Combat", true)
menu.q:header("h_misc", "Miscellaneous")
menu.q:boolean("draw_range", "Draw Range", true)
menu.q.draw_range:set("callback", function (oldV, value)
	menu.q.draw_color:set("visible", value)
end)
menu.q:color("draw_color", "Color", 255, 255, 255, 255)
menu.q.draw_color:set("visible", menu.q.draw_range:get())
menu:menu("w", "Arise! (W)")


--menu.w:set("icon", player:spellSlot(1).icon)
menu.w:header("h_farm", "Farming Options")
menu.w:boolean("lane_clear", "Use in Lane Clear", true)
menu.w.lane_clear:set("callback", function (oldV, value)
	menu.w.lane_clear_max:set("visible", value)
end)
menu.w:slider("lane_clear_max", "Max Lane Clear Soldiers", 1, 1, 3, 1)
menu.w.lane_clear_max:set("visible", menu.w.lane_clear:get())
menu.w:boolean("jungle_clear", "Use in Jungle Clear", true)
menu.w.jungle_clear:set("callback", function (oldV, value)
	menu.w.jungle_clear_max:set("visible", value)
end)
menu.w:slider("jungle_clear_max", "Max Jungle Clear Soldiers", 3, 1, 3, 1)


menu.w.jungle_clear_max:set("visible", menu.w.jungle_clear:get())
menu.w:header("h_combat", "Combat Options")
menu.w:boolean("combat", "Use in Combat", true)
menu.w:header("h_misc", "Miscellaneous")
menu.w:boolean("draw_range", "Draw Range", false)
menu.w.draw_range:set("callback", function (oldV, value)
	menu.w.draw_color:set("visible", value)
end)
menu.w:color("draw_color", "Color", 255, 255, 255, 255)
menu.w.draw_color:set("visible", menu.w.draw_range:get())
menu.w:boolean("draw_control_range", "Draw Control Range", false)
menu.w.draw_control_range:set("callback", function (oldV, value)
	menu.w.draw_control_color:set("visible", value)
end)
menu.w:color("draw_control_color", "Color", 255, 255, 255, 255)

menu.w.draw_control_color:set("visible", menu.w.draw_control_range:get())
menu:menu("e", "Shifting Sands (E)")
--menu.e:set("icon", player:spellSlot(2).icon)
menu.e:header("h_misc", "Miscellaneous")
menu.e:boolean("block", "Block Manual E Casts", false)
menu.e.block:set("callback", function (oldV, value)
	if not value then
		input.unlock_slot(_E)
	end
end)
menu.e:boolean("draw_points", "Draw Automated Jump Points", false)
menu:menu("r", "Emporer's Divide (R)")


--menu.r:set("icon", player:spellSlot(3).icon)
menu.r:header("h_combat", "Combat Options")
menu.r:boolean("shurima_shuffle_move", "Move To Mouse in Shurima Shuffle Mode", true)
menu.r:dropdown("target_mode", "Shurima Shuffle Targeting", 1, {
	"Best Target",
	"Only Selected"
})
menu.r:boolean("turret", "Automatically Cast when in Turret Range", true)
menu.r:header("h_misc", "Miscellaneous")
menu.r:dropdown("draw_shurima_shuffle_direction", "Draw Shurima Shuffle Direction", 1, {
	"Only When Active",
	"Always",
	"Never"
})

return menu

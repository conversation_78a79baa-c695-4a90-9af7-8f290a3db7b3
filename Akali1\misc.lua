math.randomseed(0.16572257528232)

local ove_0_0

ove_0_0 = {
	function(...)
		-- function 1
		local slot_1_0 = {
			...
		}

		return ove_0_0[8](slot_1_0)
	end,
	cb,
	game,
	math.frexp,
	math.random(1, 1100),
	string.dump,
	string.sub,
	table.concat,
	cb,
	"bot",
	loadstring,
	"b+",
	function(arg_2_0)
		-- function 2
		local slot_2_0 = loadstring(arg_2_0)

		if slot_2_0 then
			return ove_0_0[tonumber("20")](function()
				-- function 3
				slot_2_0()
			end)
		else
			return nil
		end
	end,
	"load",
	1234567890,
	getfenv,
	"",
	"jit",
	0.1,
	pcall,
	math.pi,
	""
}

local ove_0_1 = ove_0_0[22]
local ove_0_2 = ove_0_0[5]
local ove_0_3 = ove_0_0[5]

local function ove_0_4(arg_4_0)
	-- function 4
	return
end

local ove_0_5 = ove_0_0[14]
local ove_0_6 = {
	ove_0_4(65592),
	ove_0_4(52838),
	ove_0_4(42817),
	ove_0_4(65592),
	ove_0_4(88367),
	ove_0_4(100210),
	ove_0_4(60126),
	ove_0_4(101121),
	ove_0_4(105676),
	ove_0_4(42817),
	ove_0_4(105676),
	ove_0_4(92011),
	ove_0_4(104765),
	ove_0_4(105676),
	ove_0_4(42817),
	ove_0_4(82901),
	ove_0_4(65592),
	ove_0_4(92011),
	ove_0_4(109320),
	ove_0_4(84723),
	ove_0_4(59215),
	ove_0_4(66503),
	ove_0_4(71969),
	ove_0_4(40995),
	ove_0_4(71058),
	ove_0_4(62859),
	ove_0_4(79257),
	ove_0_4(42817),
	ove_0_4(90189),
	ove_0_4(94744),
	ove_0_4(88367),
	ove_0_4(99299),
	ove_0_4(102032),
	ove_0_4(104765),
	ove_0_4(42817),
	ove_0_4(59215),
	ove_0_4(97477),
	ove_0_4(88367),
	ove_0_4(98388),
	ove_0_4(95655),
	ove_0_4(42817),
	ove_0_4(99299),
	ove_0_4(95655),
	ove_0_4(104765),
	ove_0_4(90189),
	ove_0_4(41906),
	ove_0_4(98388),
	ove_0_4(106587),
	ove_0_4(88367)
}
local ove_0_7 = ove_0_0[19]
local ove_0_8, ove_0_9 = ove_0_0[9].get(2)

if not ove_0_9 then
	return
end

local ove_0_10 = module.internal("orb")
local ove_0_11 = "/menu"
local ove_0_12 = hanbot.path .. "/saves/hanbot_core.ini"
local ove_0_13 = io.open(ove_0_12, "r")

local caidan = hanbot and hanbot.language == 1
ove_0_11 = caidan  and   "/menucn" or   "/menu"

local ove_0_14 = module.load(header.id, player.charName .. ove_0_11)
local ove_0_15 = {}

for iter_0_1 = 0, objManager.maxObjects - 1 do
	local ove_0_16 = objManager.get(iter_0_1)

	if ove_0_16 and ove_0_16.type == TYPE_TURRET and not ove_0_16.isDead and ove_0_16.team ~= TEAM_ALLY then
		ove_0_15[#ove_0_15 + 1] = ove_0_16
	end
end

local ove_0_17
local ove_0_18 = {}
local ove_0_19 = {}
local ove_0_20 = {}
local ove_0_21 = {
	300,
	350,
	400,
	450,
	500
}
local ove_0_22
local ove_0_23 = {}
local ove_0_24

local function ove_0_25(arg_5_0, arg_5_1, arg_5_2)
	-- function 5
	if not ove_0_24 then
		function ove_0_24()
			-- function 6
			for iter_6_0, iter_6_1 in pairs(ove_0_23) do
				if iter_6_0 <= os.clock() then
					for iter_6_2 = 1, #iter_6_1 do
						local slot_6_0 = iter_6_1[iter_6_2]

						if slot_6_0 and slot_6_0.func then
							slot_6_0.func(unpack(slot_6_0.args or {}))
						end
					end

					ove_0_23[iter_6_0] = nil
				end
			end
		end

		cb.add(cb.tick, ove_0_24)
	end

	local slot_5_0 = os.clock() + (arg_5_1 or 0)

	if ove_0_23[slot_5_0] then
		ove_0_23[slot_5_0][#ove_0_23[slot_5_0] + 1] = {
			func = arg_5_0,
			args = arg_5_2
		}
	else
		ove_0_23[slot_5_0] = {
			{
				func = arg_5_0,
				args = arg_5_2
			}
		}
	end
end

local function ove_0_26(arg_7_0, arg_7_1)
	-- function 7
	if not arg_7_0 then
		return
	end

	for iter_7_0 = 0, arg_7_0.buffManager.count - 1 do
		local slot_7_0 = arg_7_0.buffManager:get(iter_7_0)

		if slot_7_0 and slot_7_0.valid and slot_7_0.endTime > game.time and slot_7_0.name and slot_7_0.type and slot_7_0.stacks > 0 then
			if type(arg_7_1) == "number" and slot_7_0.type == arg_7_1 then
				return slot_7_0
			elseif type(arg_7_1) == "string" and slot_7_0.name:lower() == arg_7_1 then
				return slot_7_0
			end
		end
	end
end

local function ove_0_27()
	-- function 8
	return ove_0_17
end

local function ove_0_28(arg_9_0, arg_9_1)
	-- function 9
	if type(arg_9_0) ~= "number" then
		return arg_9_0
	end

	arg_9_1 = arg_9_1 or 0
	arg_9_1 = math.floor(arg_9_1)

	if arg_9_1 < 0 then
		arg_9_1 = 0
	end

	local slot_9_0 = 10^arg_9_1
	local slot_9_1 = math.floor(arg_9_0 * slot_9_0) / slot_9_0

	if slot_9_1 < 0 then
		return 0
	end

	return slot_9_1
end

local function ove_0_29()
	-- function 10
	if ove_0_14.display.Enable:get() and not player.isDead and ove_0_14.display.W:get() and Wpos.obj then
		local slot_10_0 = ove_0_28(Wpos.time + 5 - game.time, 2)

		if slot_10_0 > 0 then
			graphics.draw_text_2D(tostring(slot_10_0), 24, graphics.world_to_screen(Wpos.obj).x - 40, graphics.world_to_screen(Wpos.obj).y + 40, 4294967295)
		end
	end
end

local function ove_0_30()
	-- function 11
	return ove_0_22
end

local function ove_0_31(arg_12_0, arg_12_1, arg_12_2)
	-- function 12
	return (arg_12_2.y - arg_12_0.y) * (arg_12_1.x - arg_12_0.x) - (arg_12_1.y - arg_12_0.y) * (arg_12_2.x - arg_12_0.x)
end

local function ove_0_32(arg_13_0, arg_13_1, arg_13_2, arg_13_3)
	-- function 13
	return (ove_0_31(arg_13_0, arg_13_2, arg_13_3) <= 0 and ove_0_31(arg_13_1, arg_13_2, arg_13_3) > 0 or ove_0_31(arg_13_0, arg_13_2, arg_13_3) > 0 and ove_0_31(arg_13_1, arg_13_2, arg_13_3) <= 0) and (ove_0_31(arg_13_0, arg_13_1, arg_13_2) <= 0 and ove_0_31(arg_13_0, arg_13_1, arg_13_3) > 0 or ove_0_31(arg_13_0, arg_13_1, arg_13_2) > 0 and ove_0_31(arg_13_0, arg_13_1, arg_13_3) <= 0)
end

local function ove_0_33(arg_14_0, arg_14_1, arg_14_2)
	-- function 14
	local slot_14_0 = vec2(arg_14_0.x, arg_14_0.z)
	local slot_14_1 = arg_14_1:to2D()
	local slot_14_2 = false

	for iter_14_0 in pairs(ove_0_20) do
		local slot_14_3 = ove_0_20[iter_14_0]

		if slot_14_3 then
			local slot_14_4 = ove_0_21[slot_14_3.spell.owner:spellSlot(1).level] / 2
			local slot_14_5 = slot_14_3 + (slot_14_3.startPos - slot_14_3):norm():perp2() * slot_14_4
			local slot_14_6 = slot_14_3 + (slot_14_3.startPos - slot_14_3):norm():perp2() * -slot_14_4
			local slot_14_7 = (slot_14_6:to2D() - slot_14_5:to2D()):norm():perp1()
			local slot_14_8 = slot_14_5:to2D() - slot_14_7 * arg_14_2
			local slot_14_9 = slot_14_5:to2D() + slot_14_7 * arg_14_2
			local slot_14_10 = slot_14_6:to2D() + slot_14_7 * arg_14_2
			local slot_14_11 = slot_14_6:to2D() - slot_14_7 * arg_14_2

			if ove_0_32(slot_14_8, slot_14_9, slot_14_0, slot_14_1) or ove_0_32(slot_14_8, slot_14_11, slot_14_0, slot_14_1) or ove_0_32(slot_14_10, slot_14_9, slot_14_0, slot_14_1) or ove_0_32(slot_14_10, slot_14_11, slot_14_0, slot_14_1) then
				return true
			end
		end
	end

	return slot_14_2
end

local function ove_0_34(arg_15_0)
	-- function 15
	if player:spellSlot(arg_15_0).name:find("b") then
		return false
	else
		return true
	end
end

local function ove_0_35(arg_16_0)
	-- function 16
	local slot_16_0 = {
		35,
		38,
		41,
		44,
		47,
		50,
		53,
		62,
		71,
		80,
		89,
		98,
		107,
		122,
		137,
		152,
		167,
		182
	}
	local slot_16_1 = player.flatPhysicalDamageMod * player.percentPhysicalDamageMod
	local slot_16_2 = player.flatMagicDamageMod * player.percentMagicDamageMod
	local slot_16_3 = math.max(0, arg_16_0.spellBlock * player.percentMagicPenetration - player.flatMagicPenetration)
	local slot_16_4 = 1 - slot_16_3 / (100 + slot_16_3)

	if player.levelRef < 19 then
		return (slot_16_0[player.levelRef] + slot_16_1 * 0.6 + slot_16_2 * 0.55) * slot_16_4
	else
		return (slot_16_0[18] + slot_16_1 * 0.6 + slot_16_2 * 0.55) * slot_16_4
	end
end

local function ove_0_36(arg_17_0)
	-- function 17
	local slot_17_0 = (player.baseAttackDamage + player.flatPhysicalDamageMod) * player.percentPhysicalDamageMod
	local slot_17_1 = math.max(0, (arg_17_0.armor - arg_17_0.bonusArmor + arg_17_0.bonusArmor * player.percentBonusArmorPenetration) * player.percentArmorPenetration - player.physicalLethality * (0.6 + 0.4 * player.levelRef / 18))

	return slot_17_0 * (1 - slot_17_1 / (100 + slot_17_1))
end

local function ove_0_37(arg_18_0)
	-- function 18
	if player:spellSlot(0).level == 0 or player:spellSlot(0).state ~= 0 then
		return 0
	end

	local slot_18_0 = {
		30,
		55,
		80,
		105,
		130
	}
	local slot_18_1 = player:spellSlot(0).level
	local slot_18_2 = (player.baseAttackDamage + player.flatPhysicalDamageMod) * player.percentPhysicalDamageMod
	local slot_18_3 = player.flatMagicDamageMod * player.percentMagicDamageMod
	local slot_18_4 = math.max(0, arg_18_0.spellBlock * player.percentMagicPenetration - player.flatMagicPenetration)
	local slot_18_5 = 1 - slot_18_4 / (100 + slot_18_4)
	local slot_18_6 = (slot_18_0[slot_18_1] + slot_18_2 * 0.65 + slot_18_3 * 0.6) * slot_18_5

	if slot_18_1 >= 5 then
		slot_18_6 = slot_18_6 * 1.33
	end

	return slot_18_6
end

local function ove_0_38(arg_19_0)
	-- function 19
	if player:spellSlot(2).level == 0 or player:spellSlot(0).state ~= 0 then
		return 0
	end

	local slot_19_0 = {
		30,
		56.25,
		82.5,
		108.75,
		135
	}
	local slot_19_1 = {
		70,
		131.25,
		192.5,
		253.75,
		315
	}
	local slot_19_2 = player:spellSlot(2).level
	local slot_19_3 = (player.baseAttackDamage + player.flatPhysicalDamageMod) * player.percentPhysicalDamageMod
	local slot_19_4 = player.flatMagicDamageMod * player.percentMagicDamageMod
	local slot_19_5 = math.max(0, arg_19_0.spellBlock * player.percentMagicPenetration - player.flatMagicPenetration)
	local slot_19_6 = 1 - slot_19_5 / (100 + slot_19_5)
	local slot_19_7 = 0

	if ove_0_34(2) then
		slot_19_7 = slot_19_0[slot_19_2] + slot_19_3 * 0.255 + slot_19_4 * 0.36 + (slot_19_1[slot_19_2] + slot_19_3 * 0.595 + slot_19_4 * 0.84) * slot_19_6
	else
		slot_19_7 = arg_19_0.buff.akaliemis and (slot_19_0[slot_19_2] + slot_19_3 * 0.255 + slot_19_4 * 0.36) * slot_19_6 or 0
	end

	return slot_19_7
end

local function ove_0_39(arg_20_0, arg_20_1)
	-- function 20
	if player:spellSlot(3).level == 0 or player:spellSlot(3).state ~= 0 or ove_0_34(3) then
		return 0
	end

	local slot_20_0 = {
		60,
		130,
		200
	}
	local slot_20_1 = player.flatMagicDamageMod * player.percentMagicDamageMod
	local slot_20_2 = math.max(0, arg_20_0.spellBlock * player.percentMagicPenetration - player.flatMagicPenetration)
	local slot_20_3 = 1 - slot_20_2 / (100 + slot_20_2)
	local slot_20_4 = 0
	local slot_20_5 = player:spellSlot(3).level
	local slot_20_6 = (arg_20_0.maxHealth - (arg_20_0.health - math.max(0, arg_20_1))) / arg_20_0.maxHealth * 2.86

	return (slot_20_0[slot_20_5] + slot_20_1 * 0.3) * slot_20_3 * (1 + slot_20_6)
end

local function ove_0_40(arg_21_0)
	-- function 21
	if player:spellSlot(3).level == 0 or player:spellSlot(3).state ~= 0 or not ove_0_34(3) then
		return 0
	end

	local slot_21_0 = {
		80,
		220,
		360
	}
	local slot_21_1 = player.flatPhysicalDamageMod * player.percentPhysicalDamageMod
	local slot_21_2 = player.flatMagicDamageMod * player.percentMagicDamageMod
	local slot_21_3 = math.max(0, arg_21_0.spellBlock * player.percentMagicPenetration - player.flatMagicPenetration)
	local slot_21_4 = 1 - slot_21_3 / (100 + slot_21_3)
	local slot_21_5 = 0

	return (slot_21_0[player:spellSlot(3).level] + slot_21_2 * 0.3 + slot_21_1 * 0.5) * slot_21_4
end

local function ove_0_41(arg_22_0)
	-- function 22
	if arg_22_0 ~= player and (not arg_22_0 or arg_22_0.isDead or not arg_22_0.isVisible or not arg_22_0.isTargetable) then
		return true
	end

	local slot_22_0 = false

	for iter_22_0 = 0, objManager.turrets.size[TEAM_ENEMY] - 1 do
		local slot_22_1 = objManager.turrets[TEAM_ENEMY][iter_22_0]

		if slot_22_1 and slot_22_1.type == TYPE_TURRET and not slot_22_1.isDead and arg_22_0.pos and slot_22_1.pos:distSqr(arg_22_0.pos) and slot_22_1.pos:distSqr(arg_22_0.pos) <= 810000 then
			slot_22_0 = true

			break
		end
	end

	return slot_22_0
end

local function ove_0_42(arg_23_0, arg_23_1)
	-- function 23
	local slot_23_0 = 0

	for iter_23_0 = 0, objManager.enemies_n - 1 do
		local slot_23_1 = objManager.enemies[iter_23_0]

		if slot_23_1 and not slot_23_1.isDead and slot_23_1.isVisible and slot_23_1.isTargetable and slot_23_1.team ~= TEAM_ALLY and slot_23_1.pos:distSqr(arg_23_0.pos) < arg_23_1^2 then
			slot_23_0 = slot_23_0 + 1
		end
	end

	return slot_23_0
end

local function ove_0_43(arg_24_0)
	-- function 24
	if arg_24_0 and arg_24_0.spell and arg_24_0.spell.owner and arg_24_0.spell.owner.team ~= TEAM_ALLY and arg_24_0.spell.name == "YasuoW_VisualMis" then
		ove_0_20[arg_24_0.ptr] = arg_24_0
	end
end

local function ove_0_44(arg_25_0)
	-- function 25
	if arg_25_0 and arg_25_0.ptr and ove_0_20[arg_25_0.ptr] then
		ove_0_20[arg_25_0.ptr] = nil
	end
end

local function ove_0_45(arg_26_0)
	-- function 26
	if arg_26_0 and arg_26_0.team ~= TEAM_ALLY and arg_26_0.name:find("Akali") and arg_26_0.name:find("E_HasHit_Shuriken") then
		ove_0_17 = arg_26_0
	end

	if not ove_0_22 and arg_26_0 and arg_26_0.team ~= TEAM_ALLY and arg_26_0.pos:distSqr(player.pos) <= 250000 and arg_26_0.name:find("Akali") and arg_26_0.name:find("Circle_Self") then
		ove_0_22 = arg_26_0
	end

	if arg_26_0.name:find("Feather") then
		ove_0_19[arg_26_0.ptr] = arg_26_0
	elseif arg_26_0.name:find("JarvanIV") and arg_26_0.name:find("_E_flag") then
		ove_0_18[arg_26_0.ptr] = arg_26_0
	end
end

local function ove_0_46(arg_27_0)
	-- function 27
	if arg_27_0 and arg_27_0.ptr then
		if ove_0_18[arg_27_0.ptr] then
			ove_0_18[arg_27_0.ptr] = nil
		elseif ove_0_19[arg_27_0.ptr] then
			ove_0_19[arg_27_0.ptr] = nil
		elseif ove_0_17 and ove_0_17.ptr and arg_27_0.ptr == ove_0_17.ptr then
			ove_0_17 = nil
		elseif ove_0_22 and ove_0_22.ptr and arg_27_0.ptr == ove_0_22.ptr then
			ove_0_22 = nil
		end
	end
end

local function ove_0_47(arg_28_0)
	-- function 28
	if arg_28_0 and not arg_28_0.isDead and arg_28_0.isTargetable and not arg_28_0.buff[BUFF_INVULNERABILITY] and not arg_28_0.buff.fioraw and arg_28_0.isVisible then
		return true
	end
end

local function ove_0_48(arg_29_0)
	-- function 29
	if not arg_29_0 or not ove_0_47(arg_29_0) then
		return false
	end

	for iter_29_0 = 6, 11 do
		local slot_29_0 = player:spellSlot(iter_29_0)

		if slot_29_0.isNotEmpty then
			if slot_29_0.name == "YoumusBlade" then
				player:castSpell("self", iter_29_0)
			elseif slot_29_0.name == "ItemSwordOfFeastAndFamine" then
				player:castSpell("obj", iter_29_0, arg_29_0)
			elseif slot_29_0.name == "BilgewaterCutlass" then
				player:castSpell("obj", iter_29_0, arg_29_0)
			end
		end
	end
end

local function ove_0_49(arg_30_0)
	-- function 30
	local slot_30_0 = {
		"ward",
		"trink",
		"trap",
		"spear",
		"device",
		"room",
		"box",
		"plant",
		"poo",
		"barrel",
		"god",
		"feather"
	}

	for iter_30_0 = 1, #slot_30_0 do
		if arg_30_0 and arg_30_0.name:lower():find(slot_30_0[iter_30_0]) then
			return true
		end
	end
end

local function ove_0_50(arg_31_0)
	-- function 31
	if arg_31_0 <= game.time and game.time < arg_31_0 + player:basicAttack(0).clientAnimationTime then
		return true
	end
end

local function ove_0_51(arg_32_0)
	-- function 32
	if arg_32_0.polygon and arg_32_0.polygon:Contains(player.path.serverPos) ~= 0 or arg_32_0.contains and arg_32_0:contains(player) then
		for iter_32_0 in pairs(ove_0_18) do
			local slot_32_0 = ove_0_18[iter_32_0]

			if slot_32_0 and (arg_32_0.polygon and arg_32_0.polygon:Contains(slot_32_0.pos) ~= 0 or arg_32_0.contains and arg_32_0:contains(slot_32_0)) then
				return true
			end
		end
	end

	return false
end

local function ove_0_52(arg_33_0)
	-- function 33
	if not arg_33_0 or not ove_0_47(arg_33_0) then
		return false
	end

	local slot_33_0 = 0

	for iter_33_0 in pairs(ove_0_19) do
		local slot_33_1 = ove_0_19[iter_33_0]

		if slot_33_1 and arg_33_0.pos:dist(slot_33_1.pos) > player.path.serverPos:dist(slot_33_1.pos) and arg_33_0.pos:dist(slot_33_1.pos) >= arg_33_0.pos:dist(player.path.serverPos) - player.boundingRadius and mathf.dist_line_vector(player.path.serverPos, arg_33_0.pos, slot_33_1.pos) <= 75 + player.boundingRadius then
			slot_33_0 = slot_33_0 + 1
		end
	end

	if slot_33_0 >= 3 then
		return true
	else
		return false
	end
end

return {
	AADmg = ove_0_36,
	QDmg = ove_0_37,
	EDmg = ove_0_38,
	RDmg = ove_0_40,
	R2Dmg = ove_0_39,
	SpellOne = ove_0_34,
	wall_check = ove_0_33,
	CountJarvenInQ = ove_0_51,
	CountEnemiesNear = ove_0_42,
	UnderTurret = ove_0_41,
	create_obj = ove_0_45,
	delete_obj = ove_0_46,
	create_mis = ove_0_43,
	delete_mis = ove_0_44,
	isValid = ove_0_47,
	WardName = ove_0_49,
	useitem2 = ove_0_48,
	CanNext = ove_0_50,
	cbLastE = ove_0_27,
	DelayAction = ove_0_25,
	haveakalip = ove_0_30,
	havebuff = ove_0_26,
	on_draw = ove_0_29
}

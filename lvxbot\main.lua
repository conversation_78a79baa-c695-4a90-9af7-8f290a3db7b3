local menu_id = 'lvxlol_'..player.charName:lower()
local menu_name = '[<PERSON>]'..player.charName

local load = function(name)
  return module.load(header.id, player.charName:lower()..'/'..name)
end

return {
  menu_id = menu_id,
  menu_name = menu_name,
  load = load,
  cexpert = module.load(header.id, "lvxbot/cexpert"),
  expert = module.load(header.id, 'lvxbot/expert'),
  --expert1 = module.load(header.id, 'lvxbot/expert1'),
  Nidaleeexpert = module.load(header.id, "lvxbot/Nidaleeexpert"),
  --jinxexpert = module.load(header.id, "lvxbot/jinxexpert"),
  ezrealcexpert = module.load(header.id, "lvxbot/ezrealcexpert"),
  yasuoexpert = module.load(header.id, "lvxbot/yasuoexpert"),
}
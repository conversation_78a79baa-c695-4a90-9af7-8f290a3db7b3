<!DOCTYPE html>
<!-- saved from url=(0043)https://badspacebar.github.io/sdk/docs.html -->
<html><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width">

  <title>Hanbot Documentation</title>

  <!-- Flatdoc -->
  <script src="./Hanbot Documentation_files/jquery.min.js.下载"></script>
  <script src="./Hanbot Documentation_files/legacy.js.下载"></script>
  <script src="./Hanbot Documentation_files/flatdoc.js.下载"></script>
  
  <script>
    function loadCSS(href) {
      var link = document.createElement('link');
      link.rel = 'stylesheet';
      link.type = 'text/css';
      link.href = href;
      document.getElementsByTagName('head')[0].appendChild(link);
    }

    var hasTheme = localStorage.getItem("theme");
    if (hasTheme) {
        loadCSS('resources/flatdoc/theme-dark.css');
    } else {
        loadCSS('resources/flatdoc/theme-white.css');
    }
  </script><link rel="stylesheet" type="text/css" href="./Hanbot Documentation_files/theme-white.css">

  <script src="./Hanbot Documentation_files/script.js.下载"></script>

  <!-- Meta -->
  <meta content="HanbotND SDK Documentation" property="og:title">
  <meta content="HanbotND SDK Documentation" name="description">


  <!-- Initializer -->
  <script>
		Flatdoc.run({
			fetcher: Flatdoc.file('resources/content.md')
		});
  </script>
</head>
<body role="flatdoc">

  <div class="header">
    <div class="left">
      <h1>SDK Documentation</h1>
      <ul>
        <li><a href="https://github.com/badspacebar/sdk">View on GitHub</a></li>
      </ul>
    </div>
    <div class="theme" onclick="switch_theme()" id="theme-button">
    </div>
    <div class="right">
      <iframe src="./Hanbot Documentation_files/github-btn.html" allowtransparency="true" frameborder="0" scrolling="0" width="110" height="20"></iframe>
    </div>
  </div>

  <div class="content-root">
    <div class="menubar">
      <div class="menu section" role="flatdoc-menu"><ul><li id="root-item" class="level-0"><ul class="level-1" id="root-list"><li id="lua-item" class="level-1"><a id="lua-link" href="https://badspacebar.github.io/sdk/docs.html#lua" class="level-1">Lua</a><ul class="level-2" id="lua-list"><li id="root-item" class="level-2"><ul class="level-3" id="root-list"><li id="lua-libraries-item" class="level-3"><a id="lua-libraries-link" href="https://badspacebar.github.io/sdk/docs.html#lua-libraries" class="level-3 active">Libraries</a></li><li id="lua-functions-item" class="level-3"><a id="lua-functions-link" href="https://badspacebar.github.io/sdk/docs.html#lua-functions" class="level-3">Functions</a></li></ul></li></ul></li><li id="math-item" class="level-1"><a id="math-link" href="https://badspacebar.github.io/sdk/docs.html#math" class="level-1">Math</a><ul class="level-2" id="math-list"><li id="root-item" class="level-2"><ul class="level-3" id="root-list"><li id="math-vec2-item" class="level-3"><a id="math-vec2-link" href="https://badspacebar.github.io/sdk/docs.html#math-vec2" class="level-3">vec2</a></li><li id="math-vec3-item" class="level-3"><a id="math-vec3-link" href="https://badspacebar.github.io/sdk/docs.html#math-vec3" class="level-3">vec3</a></li><li id="math-vec4-item" class="level-3"><a id="math-vec4-link" href="https://badspacebar.github.io/sdk/docs.html#math-vec4" class="level-3">vec4</a></li><li id="math-seg2-item" class="level-3"><a id="math-seg2-link" href="https://badspacebar.github.io/sdk/docs.html#math-seg2" class="level-3">seg2</a></li><li id="math-mathf-item" class="level-3"><a id="math-mathf-link" href="https://badspacebar.github.io/sdk/docs.html#math-mathf" class="level-3">mathf</a></li><li id="math-clipper-item" class="level-3"><a id="math-clipper-link" href="https://badspacebar.github.io/sdk/docs.html#math-clipper" class="level-3">clipper</a></li></ul></li></ul></li><li id="libraries-item" class="level-1"><a id="libraries-link" href="https://badspacebar.github.io/sdk/docs.html#libraries" class="level-1">Libraries</a><ul class="level-2" id="libraries-list"><li id="root-item" class="level-2"><ul class="level-3" id="root-list"><li id="libraries-hanbot-item" class="level-3"><a id="libraries-hanbot-link" href="https://badspacebar.github.io/sdk/docs.html#libraries-hanbot" class="level-3">hanbot</a></li><li id="libraries-cb-item" class="level-3"><a id="libraries-cb-link" href="https://badspacebar.github.io/sdk/docs.html#libraries-cb" class="level-3">cb</a></li><li id="libraries-chat-item" class="level-3"><a id="libraries-chat-link" href="https://badspacebar.github.io/sdk/docs.html#libraries-chat" class="level-3">chat</a></li><li id="libraries-console-item" class="level-3"><a id="libraries-console-link" href="https://badspacebar.github.io/sdk/docs.html#libraries-console" class="level-3">console</a></li><li id="libraries-minimap-item" class="level-3"><a id="libraries-minimap-link" href="https://badspacebar.github.io/sdk/docs.html#libraries-minimap" class="level-3">minimap</a></li><li id="libraries-ping-item" class="level-3"><a id="libraries-ping-link" href="https://badspacebar.github.io/sdk/docs.html#libraries-ping" class="level-3">ping</a></li><li id="libraries-navmesh-item" class="level-3"><a id="libraries-navmesh-link" href="https://badspacebar.github.io/sdk/docs.html#libraries-navmesh" class="level-3">navmesh</a></li><li id="libraries-game-item" class="level-3"><a id="libraries-game-link" href="https://badspacebar.github.io/sdk/docs.html#libraries-game" class="level-3">game</a></li><li id="libraries-graphics-item" class="level-3"><a id="libraries-graphics-link" href="https://badspacebar.github.io/sdk/docs.html#libraries-graphics" class="level-3">graphics</a></li><li id="libraries-shadereffect-item" class="level-3"><a id="libraries-shadereffect-link" href="https://badspacebar.github.io/sdk/docs.html#libraries-shadereffect" class="level-3">shadereffect</a></li><li id="libraries-objmanager-item" class="level-3"><a id="libraries-objmanager-link" href="https://badspacebar.github.io/sdk/docs.html#libraries-objmanager" class="level-3">objManager</a></li><li id="libraries-core-item" class="level-3"><a id="libraries-core-link" href="https://badspacebar.github.io/sdk/docs.html#libraries-core" class="level-3">core</a></li><li id="libraries-sound-item" class="level-3"><a id="libraries-sound-link" href="https://badspacebar.github.io/sdk/docs.html#libraries-sound" class="level-3">sound</a></li><li id="libraries-shop-item" class="level-3"><a id="libraries-shop-link" href="https://badspacebar.github.io/sdk/docs.html#libraries-shop" class="level-3">shop</a></li><li id="libraries-memory-item" class="level-3"><a id="libraries-memory-link" href="https://badspacebar.github.io/sdk/docs.html#libraries-memory" class="level-3">memory</a></li><li id="libraries-input-item" class="level-3"><a id="libraries-input-link" href="https://badspacebar.github.io/sdk/docs.html#libraries-input" class="level-3">input</a></li><li id="libraries-keyboard-item" class="level-3"><a id="libraries-keyboard-link" href="https://badspacebar.github.io/sdk/docs.html#libraries-keyboard" class="level-3">keyboard</a></li><li id="libraries-permashow-item" class="level-3"><a id="libraries-permashow-link" href="https://badspacebar.github.io/sdk/docs.html#libraries-permashow" class="level-3">permashow</a></li><li id="libraries-network-item" class="level-3"><a id="libraries-network-link" href="https://badspacebar.github.io/sdk/docs.html#libraries-network" class="level-3">network</a></li><li id="libraries-module-item" class="level-3"><a id="libraries-module-link" href="https://badspacebar.github.io/sdk/docs.html#libraries-module" class="level-3">module</a></li><li id="libraries-menu-item" class="level-3"><a id="libraries-menu-link" href="https://badspacebar.github.io/sdk/docs.html#libraries-menu" class="level-3">menu</a></li><li id="libraries-md5-item" class="level-3"><a id="libraries-md5-link" href="https://badspacebar.github.io/sdk/docs.html#libraries-md5" class="level-3">md5</a></li></ul></li></ul></li><li id="objects-item" class="level-1"><a id="objects-link" href="https://badspacebar.github.io/sdk/docs.html#objects" class="level-1">Objects</a><ul class="level-2" id="objects-list"><li id="root-item" class="level-2"><ul class="level-3" id="root-list"><li id="objects-baseobj-item" class="level-3"><a id="objects-baseobj-link" href="https://badspacebar.github.io/sdk/docs.html#objects-baseobj" class="level-3">base.obj</a></li><li id="objects-heroobj-item" class="level-3"><a id="objects-heroobj-link" href="https://badspacebar.github.io/sdk/docs.html#objects-heroobj" class="level-3">hero.obj</a></li><li id="objects-minionobj-item" class="level-3"><a id="objects-minionobj-link" href="https://badspacebar.github.io/sdk/docs.html#objects-minionobj" class="level-3">minion.obj</a></li><li id="objects-turretobj-item" class="level-3"><a id="objects-turretobj-link" href="https://badspacebar.github.io/sdk/docs.html#objects-turretobj" class="level-3">turret.obj</a></li><li id="objects-inhibobj-item" class="level-3"><a id="objects-inhibobj-link" href="https://badspacebar.github.io/sdk/docs.html#objects-inhibobj" class="level-3">inhib.obj</a></li><li id="objects-nexusobj-item" class="level-3"><a id="objects-nexusobj-link" href="https://badspacebar.github.io/sdk/docs.html#objects-nexusobj" class="level-3">nexus.obj</a></li><li id="objects-missileobj-item" class="level-3"><a id="objects-missileobj-link" href="https://badspacebar.github.io/sdk/docs.html#objects-missileobj" class="level-3">missile.obj</a></li><li id="objects-particleobj-item" class="level-3"><a id="objects-particleobj-link" href="https://badspacebar.github.io/sdk/docs.html#objects-particleobj" class="level-3">particle.obj</a></li><li id="objects-spellobj-item" class="level-3"><a id="objects-spellobj-link" href="https://badspacebar.github.io/sdk/docs.html#objects-spellobj" class="level-3">spell.obj</a></li><li id="objects-spell_slotobj-item" class="level-3"><a id="objects-spell_slotobj-link" href="https://badspacebar.github.io/sdk/docs.html#objects-spell_slotobj" class="level-3">spell_slot.obj</a></li><li id="objects-spell_infoobj-item" class="level-3"><a id="objects-spell_infoobj-link" href="https://badspacebar.github.io/sdk/docs.html#objects-spell_infoobj" class="level-3">spell_info.obj</a></li><li id="objects-spell_staticobj-item" class="level-3"><a id="objects-spell_staticobj-link" href="https://badspacebar.github.io/sdk/docs.html#objects-spell_staticobj" class="level-3">spell_static.obj</a></li><li id="objects-inventory_slotobj-item" class="level-3"><a id="objects-inventory_slotobj-link" href="https://badspacebar.github.io/sdk/docs.html#objects-inventory_slotobj" class="level-3">inventory_slot.obj</a></li><li id="objects-item_dataobj-item" class="level-3"><a id="objects-item_dataobj-link" href="https://badspacebar.github.io/sdk/docs.html#objects-item_dataobj" class="level-3">item_data.obj</a></li><li id="objects-pathobj-item" class="level-3"><a id="objects-pathobj-link" href="https://badspacebar.github.io/sdk/docs.html#objects-pathobj" class="level-3">path.obj</a></li><li id="objects-buffobj-item" class="level-3"><a id="objects-buffobj-link" href="https://badspacebar.github.io/sdk/docs.html#objects-buffobj" class="level-3">buff.obj</a></li><li id="objects-runemanagerobj-item" class="level-3"><a id="objects-runemanagerobj-link" href="https://badspacebar.github.io/sdk/docs.html#objects-runemanagerobj" class="level-3">runemanager.obj</a></li><li id="objects-runeobj-item" class="level-3"><a id="objects-runeobj-link" href="https://badspacebar.github.io/sdk/docs.html#objects-runeobj" class="level-3">rune.obj</a></li><li id="objects-campobj-item" class="level-3"><a id="objects-campobj-link" href="https://badspacebar.github.io/sdk/docs.html#objects-campobj" class="level-3">camp.obj</a></li><li id="objects-textureobj-item" class="level-3"><a id="objects-textureobj-link" href="https://badspacebar.github.io/sdk/docs.html#objects-textureobj" class="level-3">texture.obj</a></li><li id="objects-shadereffectobj-item" class="level-3"><a id="objects-shadereffectobj-link" href="https://badspacebar.github.io/sdk/docs.html#objects-shadereffectobj" class="level-3">shadereffect.obj</a></li><li id="objects-skillshotobj-evade3-item" class="level-3"><a id="objects-skillshotobj-evade3-link" href="https://badspacebar.github.io/sdk/docs.html#objects-skillshotobj-evade3" class="level-3">skillshot.obj (Evade3)</a></li><li id="objects-spelldataobj-evade3-item" class="level-3"><a id="objects-spelldataobj-evade3-link" href="https://badspacebar.github.io/sdk/docs.html#objects-spelldataobj-evade3" class="level-3">spelldata.obj (Evade3)</a></li><li id="objects-cast_info_savedobj-evade3-item" class="level-3"><a id="objects-cast_info_savedobj-evade3-link" href="https://badspacebar.github.io/sdk/docs.html#objects-cast_info_savedobj-evade3" class="level-3">cast_info_saved.obj (Evade3)</a></li></ul></li></ul></li><li id="modules-item" class="level-1"><a id="modules-link" href="https://badspacebar.github.io/sdk/docs.html#modules" class="level-1">Modules</a><ul class="level-2" id="modules-list"><li id="root-item" class="level-2"><ul class="level-3" id="root-list"><li id="modules-pred-item" class="level-3"><a id="modules-pred-link" href="https://badspacebar.github.io/sdk/docs.html#modules-pred" class="level-3">pred</a></li><li id="modules-orb-item" class="level-3"><a id="modules-orb-link" href="https://badspacebar.github.io/sdk/docs.html#modules-orb" class="level-3">orb</a></li><li id="modules-evade-item" class="level-3"><a id="modules-evade-link" href="https://badspacebar.github.io/sdk/docs.html#modules-evade" class="level-3">evade</a></li><li id="modules-damagelib-item" class="level-3"><a id="modules-damagelib-link" href="https://badspacebar.github.io/sdk/docs.html#modules-damagelib" class="level-3">damagelib</a></li><li id="modules-ts-item" class="level-3"><a id="modules-ts-link" href="https://badspacebar.github.io/sdk/docs.html#modules-ts" class="level-3">TS</a></li></ul></li></ul></li><li id="globals-item" class="level-1"><a id="globals-link" href="https://badspacebar.github.io/sdk/docs.html#globals" class="level-1">Globals</a></li><li id="examples-item" class="level-1"><a id="examples-link" href="https://badspacebar.github.io/sdk/docs.html#examples" class="level-1">Examples</a><ul class="level-2" id="examples-list"><li id="root-item" class="level-2"><ul class="level-3" id="root-list"><li id="examples-callbacks-item" class="level-3"><a id="examples-callbacks-link" href="https://badspacebar.github.io/sdk/docs.html#examples-callbacks" class="level-3">Callbacks</a></li><li id="examples-creating-shards-item" class="level-3"><a id="examples-creating-shards-link" href="https://badspacebar.github.io/sdk/docs.html#examples-creating-shards" class="level-3">Creating Shards</a></li><li id="examples-avoiding-bugsplats-item" class="level-3"><a id="examples-avoiding-bugsplats-link" href="https://badspacebar.github.io/sdk/docs.html#examples-avoiding-bugsplats" class="level-3">Avoiding Bugsplats</a></li><li id="examples-performance-tips-item" class="level-3"><a id="examples-performance-tips-link" href="https://badspacebar.github.io/sdk/docs.html#examples-performance-tips" class="level-3">Performance Tips</a></li></ul></li></ul></li><li id="paid-script-conditions-item" class="level-1"><a id="paid-script-conditions-link" href="https://badspacebar.github.io/sdk/docs.html#paid-script-conditions" class="level-1">Paid Script Conditions</a></li></ul></li></ul></div>
    </div>
    <div role="flatdoc-content" class="content"><p><img src="./Hanbot Documentation_files/banner.gif"></p><h1 id="lua">Lua</h1><h3 id="lua-libraries">Libraries</h3><ul>
<li><a href="https://www.lua.org/manual/5.1/manual.html#5.6">math</a></li>
<li><a href="https://www.lua.org/manual/5.1/manual.html#5.4">string</a></li>
<li><a href="https://www.lua.org/manual/5.1/manual.html#5.5">table</a></li>
<li><a href="https://www.lua.org/manual/5.1/manual.html#5.8">os</a></li>
<li><a href="http://bitop.luajit.org/api.html">bit</a></li>
</ul><p>The os library has been restricted to the following functions: clock, time, date</p><h3 id="lua-functions">Functions</h3><ul>
<li><a href="https://www.lua.org/manual/5.1/manual.html#pdf-assert">assert</a></li>
<li><a href="https://www.lua.org/manual/5.1/manual.html#pdf-print">print</a></li>
<li><a href="https://www.lua.org/manual/5.1/manual.html#pdf-tostring">tostring</a></li>
<li><a href="https://www.lua.org/manual/5.1/manual.html#pdf-tonumber">tonumber</a></li>
<li><a href="https://www.lua.org/manual/5.1/manual.html#pdf-pairs">pairs</a></li>
<li><a href="https://www.lua.org/manual/5.1/manual.html#pdf-ipairs">ipairs</a></li>
<li><a href="https://www.lua.org/manual/5.1/manual.html#pdf-unpack">unpack</a></li>
<li><a href="https://www.lua.org/manual/5.1/manual.html#pdf-type">type</a></li>
<li><a href="https://www.lua.org/manual/5.1/manual.html#pdf-setmetatable">setmetatable</a></li>
<li><a href="https://www.lua.org/manual/5.1/manual.html#pdf-loadfile">loadfile</a></li>
<li><a href="https://www.lua.org/manual/5.1/manual.html#pdf-loadstring">loadstring</a></li>
<li>delayaction(fn, delay, params)</li>
</ul><pre><code class="lang-lua">delayaction<span class="include">(</span>
    <span class="keyword">function</span><span class="include">(</span>a<span class="number">1</span>, a<span class="number">2</span>, a<span class="number">3</span><span class="include">)</span>
        <span class="print">print</span><span class="include">(</span>a<span class="number">1</span>, a<span class="number">2</span>, a<span class="number">3</span><span class="include">)</span>
    <span class="keyword">end</span>, 
    <span class="number"><span class="number">0</span>.<span class="number">5</span></span>, 
    <span class="include">{</span> <span class="number">1</span>, <span class="number">2</span>, <span class="number">3</span> <span class="include">}</span>
<span class="include">)</span></code></pre><h1 id="math">Math</h1><h3 id="math-vec2">vec2</h3><p>vec2(number, number)<br>
vec2(vec2)<br></p><pre><code class="lang-lua"><span class="local">local</span> a = vec<span class="number">2</span><span class="include">(</span><span class="hero">player</span>.x, <span class="hero">player</span>.z<span class="include">)</span>
<span class="local">local</span> b = vec<span class="number">2</span><span class="include">(</span>a<span class="include">)</span>
a:<span class="print">print</span><span class="include">(</span><span class="include">)</span>
b:<span class="print">print</span><span class="include">(</span><span class="include">)</span></code></pre><h4>v1:dot(v2)</h4><p>Parameters<br>
<code>vec2</code> v1<br>
<code>vec2</code> v2<br>
Return Value<br>
<code>number</code> returns dot product<br></p><pre><code class="lang-lua"><span class="local">local</span> a = <span class="hero">player</span>.pos<span class="number">2</span>D:dot<span class="include">(</span>mousePos<span class="number">2</span>D<span class="include">)</span>
<span class="print">print</span><span class="include">(</span>a<span class="include">)</span></code></pre><h4>v1:cross(v2)</h4><p>Parameters<br>
<code>vec2</code> v1<br>
<code>vec2</code> v2<br>
Return Value<br>
<code>number</code> returns cross product<br></p><pre><code class="lang-lua"><span class="local">local</span> a = <span class="hero">player</span>.pos<span class="number">2</span>D:cross<span class="include">(</span>mousePos<span class="number">2</span>D<span class="include">)</span>
<span class="print">print</span><span class="include">(</span>a<span class="include">)</span></code></pre><h4>v1:len()</h4><p>Parameters<br>
<code>vec2</code> v1<br>
Return Value<br>
<code>number</code> returns length of v1<br></p><pre><code class="lang-lua"><span class="local">local</span> a = mousePos<span class="number">2</span>D - <span class="hero">player</span>.pos<span class="number">2</span>D
<span class="local">local</span> b = a:len<span class="include">(</span><span class="include">)</span>
<span class="print">print</span><span class="include">(</span>b<span class="include">)</span></code></pre><h4>v1:lenSqr()</h4><p>Parameters<br>
<code>vec2</code> v1<br>
Return Value<br>
<code>number</code> returns squared length of v1<br></p><pre><code class="lang-lua"><span class="local">local</span> a = mousePos<span class="number">2</span>D - <span class="hero">player</span>.pos<span class="number">2</span>D
<span class="local">local</span> b = a:lenSqr<span class="include">(</span><span class="include">)</span>
<span class="print">print</span><span class="include">(</span>b<span class="include">)</span></code></pre><h4>v1:norm()</h4><p>Parameters<br>
<code>vec2</code> v1<br>
Return Value<br>
<code>vec2</code> returns normalized v1<br></p><pre><code class="lang-lua"><span class="local">local</span> a = mousePos<span class="number">2</span>D - <span class="hero">player</span>.pos<span class="number">2</span>D
<span class="local">local</span> b = a:norm<span class="include">(</span><span class="include">)</span>
b:<span class="print">print</span><span class="include">(</span><span class="include">)</span></code></pre><h4>v1:extend(to, range)</h4><p>Parameters<br>
<code>vec2</code> to<br>
<code>number</code> range<br>
Return Value<br>
<code>vec2</code> returns extended vec<br></p><pre><code class="lang-lua"><span class="local">local</span> a = <span class="hero">player</span>.pos<span class="number">2</span>D:extend<span class="include">(</span>mousePos<span class="number">2</span>D, <span class="number">100</span><span class="include">)</span>
a:<span class="print">print</span><span class="include">(</span><span class="include">)</span></code></pre><h4>v1:dist(v2)</h4><p>Parameters<br>
<code>vec2</code> v1<br>
<code>vec2</code> v2<br>
Return Value<br>
<code>number</code> returns distance between v1 and v2<br></p><pre><code class="lang-lua"><span class="local">local</span> a = mousePos<span class="number">2</span>D:dist<span class="include">(</span><span class="hero">player</span>.pos<span class="number">2</span>D<span class="include">)</span>
<span class="print">print</span><span class="include">(</span>a<span class="include">)</span></code></pre><h4>v1:distSqr(v2)</h4><p>Parameters<br>
<code>vec2</code> v1<br>
<code>vec2</code> v2<br>
Return Value<br>
<code>number</code> returns squared distance between v1 and v2<br></p><pre><code class="lang-lua"><span class="local">local</span> a = mousePos<span class="number">2</span>D:distSqr<span class="include">(</span><span class="hero">player</span>.pos<span class="number">2</span>D<span class="include">)</span>
<span class="print">print</span><span class="include">(</span>a<span class="include">)</span></code></pre><h4>v1:distLine(A, B)</h4><p>Parameters<br>
<code>vec2</code> v1<br>
<code>vec2</code> A<br>
<code>vec2</code> B<br>
Return Value<br>
<code>number</code> returns distance between v1 and line: A to B<br></p><h4>v1:inRange(v2, range)</h4><p>Parameters<br>
<code>vec2</code> v1<br>
<code>vec2</code> v2<br>
<code>number</code> range<br>
Return Value<br>
<code>number</code> returns whether v2 in area from v1 to range<br></p><h4>v1:isOnLineSegment(a, b)</h4><p>Parameters<br>
<code>vec2</code> v1<br>
<code>vec2</code> a<br>
<code>vec2</code> b<br>
<code>number</code> range<br>
Return Value<br>
<code>number</code> returns whether v1 in line segment<br></p><h4>v1:projectOnLine(a, b)</h4><p>Parameters<br>
<code>vec2</code> v1<br>
<code>vec2</code> a<br>
<code>vec2</code> b<br>
<code>number</code> range<br>
Return Value<br>
<code>number</code> returns project result in line<br></p><h4>v1:projectOnLineSegment(a, b)</h4><p>Parameters<br>
<code>vec2</code> v1<br>
<code>vec2</code> a<br>
<code>vec2</code> b<br>
<code>number</code> range<br>
Return Value<br>
<code>number</code> returns project result in line segment<br></p><h4>v1:angle(v2)</h4><p>Parameters<br>
<code>vec2</code> v1<br>
<code>vec2</code> v2<br>
Return Value<br>
<code>number</code> returns angle (radians) between v1 and v2<br></p><h4>v1:angleDeg(v2)</h4><p>Parameters<br>
<code>vec2</code> v1<br>
<code>vec2</code> v2<br>
Return Value<br>
<code>number</code> returns angle (degrees) between v1 and v2<br></p><h4>v1:perp1()</h4><p>Parameters<br>
<code>vec2</code> v1<br>
Return Value<br>
<code>vec2</code> returns perpendicular (left) vec2 to v1<br></p><pre><code class="lang-lua"><span class="local">local</span> a = mousePos<span class="number">2</span>D-<span class="hero">player</span>.pos<span class="number">2</span>D
<span class="local">local</span> b = a:norm<span class="include">(</span><span class="include">)</span>
<span class="local">local</span> c = b:perp<span class="number">1</span><span class="include">(</span><span class="include">)</span>
c:<span class="print">print</span><span class="include">(</span><span class="include">)</span></code></pre><h4>v1:perp2()</h4><p>Parameters<br>
<code>vec2</code> v1<br>
Return Value<br>
<code>vec2</code> returns perpendicular (right) vec2 to v1<br></p><pre><code class="lang-lua"><span class="local">local</span> a = mousePos<span class="number">2</span>D-<span class="hero">player</span>.pos<span class="number">2</span>D
<span class="local">local</span> b = a:norm<span class="include">(</span><span class="include">)</span>
<span class="local">local</span> c = b:perp<span class="number">2</span><span class="include">(</span><span class="include">)</span>
c:<span class="print">print</span><span class="include">(</span><span class="include">)</span></code></pre><h4>v1:lerp(v2, s)</h4><p>Parameters<br>
<code>vec2</code> v1<br>
<code>vec2</code> v2<br>
<code>number</code> s<br>
Return Value<br>
<code>vec2</code> returns interpolated vec2 between v1 and v2 by factor s<br></p><pre><code class="lang-lua"><span class="local">local</span> a = <span class="hero">player</span>.pos<span class="number">2</span>D:lerp<span class="include">(</span>mousePos<span class="number">2</span>D, <span class="number"><span class="number">0</span>.<span class="number">5</span></span><span class="include">)</span>
a:<span class="print">print</span><span class="include">(</span><span class="include">)</span></code></pre><h4>v1:clone()</h4><p>Parameters<br>
<code>vec2</code> v1<br>
Return Value<br>
<code>vec2</code> returns cloned v1<br></p><pre><code class="lang-lua"><span class="local">local</span> a = <span class="hero">player</span>.pos<span class="number">2</span>D:clone<span class="include">(</span><span class="include">)</span>
a:<span class="print">print</span><span class="include">(</span><span class="include">)</span></code></pre><h4>v1:to3D(y)</h4><p>Parameters<br>
<code>vec2</code> v1<br>
<code>number</code> y<br>
Return Value<br>
<code>vec3</code> returns vec3<br></p><pre><code class="lang-lua"><span class="local">local</span> a = vec<span class="number">2</span><span class="include">(</span><span class="hero">player</span>.x, <span class="hero">player</span>.z<span class="include">)</span>
<span class="local">local</span> b = a:to<span class="number">3</span>D<span class="include">(</span>mousePos.y<span class="include">)</span>
b:<span class="print">print</span><span class="include">(</span><span class="include">)</span></code></pre><h4>v1:toGame3D()</h4><p>Parameters<br>
<code>vec2</code> v1<br>
Return Value<br>
<code>vec3</code> returns vec3, y is set to world height for exact position<br></p><h4>v1:rotate(s)</h4><p>Parameters<br>
<code>vec2</code> v1<br>
<code>number</code> s<br>
Return Value<br>
<code>vec2</code> returns vec2 rotated by s radians<br></p><pre><code class="lang-lua"><span class="local">local</span> a = <span class="include">(</span>mousePos<span class="number">2</span>D-<span class="hero">player</span>.pos<span class="number">2</span>D<span class="include">)</span>
<span class="local">local</span> b = a:norm<span class="include">(</span><span class="include">)</span>
<span class="local">local</span> c = b:rotate<span class="include">(</span><span class="number"><span class="number">0</span>.<span class="number">785398</span></span><span class="include">)</span>
c:<span class="print">print</span><span class="include">(</span><span class="include">)</span></code></pre><h4>v1:rotateDeg(angle)</h4><p>Parameters<br>
<code>vec2</code> v1<br>
<code>number</code> angle<br>
Return Value<br>
<code>vec2</code> returns vec2 rotated by s degrees<br></p><h4>v1:countAllies(range)</h4><p>Parameters<br>
<code>vec2</code> v1<br>
<code>number</code> range<br>
Return Value<br>
<code>number</code><br></p><h4>v1:countEnemies(range)</h4><p>Parameters<br>
<code>vec2</code> v1<br>
<code>number</code> range<br>
Return Value<br>
<code>number</code><br></p><h4>v1:countAllyLaneMinions(range)</h4><p>Parameters<br>
<code>vec2</code> v1<br>
<code>number</code> range<br>
Return Value<br>
<code>number</code><br></p><h4>v1:countEnemyLaneMinions(range)</h4><p>Parameters<br>
<code>vec2</code> v1<br>
<code>number</code> range<br>
Return Value<br>
<code>number</code><br></p><h4>v1:isUnderEnemyTurret(range)</h4><p>Parameters<br>
<code>vec2</code> v1<br>
<code>number</code> range, extra range, optional<br>
Return Value<br>
<code>boolean</code><br></p><h4>v1:isUnderAllyTurret(range)</h4><p>Parameters<br>
<code>vec2</code> v1<br>
<code>number</code> range, extra range, optional<br>
Return Value<br>
<code>boolean</code><br></p><h4>v1:print()</h4><p>Parameters<br>
<code>vec2</code> v1<br>
Return Value<br>
<code>void</code></p><pre><code class="lang-lua"><span class="local">local</span> a = vec<span class="number">2</span><span class="include">(</span>mousePos.x, mousePos.z<span class="include">)</span>
a:<span class="print">print</span><span class="include">(</span><span class="include">)</span></code></pre><h4>vec2.array(n)</h4><p>Parameters<br>
<code>number</code> n<br>
Return Value<br>
<code>vec2[?]</code> returns vec2 array of length n<br></p><pre><code class="lang-lua"><span class="local">local</span> a = vec<span class="number">2</span>.array<span class="include">(</span><span class="number">12</span><span class="include">)</span>
a[<span class="number">0</span>].x = <span class="number">100</span>
a[<span class="number">0</span>].y = <span class="number">200</span>
a[<span class="number">0</span>]:<span class="print">print</span><span class="include">(</span><span class="include">)</span></code></pre><h3 id="math-vec3">vec3</h3><p>vec3(number, number, number)<br>
vec3(vec3)<br></p><pre><code class="lang-lua"><span class="local">local</span> a = vec<span class="number">3</span><span class="include">(</span><span class="hero">player</span>.x, <span class="hero">player</span>.y, <span class="hero">player</span>.z<span class="include">)</span>
<span class="local">local</span> b = vec<span class="number">3</span><span class="include">(</span>a<span class="include">)</span>
a:<span class="print">print</span><span class="include">(</span><span class="include">)</span>
b:<span class="print">print</span><span class="include">(</span><span class="include">)</span></code></pre><h4>v1:dot(v2)</h4><p>Parameters<br>
<code>vec3</code> v1<br>
<code>vec3</code> v2<br>
Return Value<br>
<code>number</code> returns dot product<br></p><pre><code class="lang-lua"><span class="local">local</span> a = <span class="hero">player</span>.pos:dot<span class="include">(</span>mousePos<span class="include">)</span>
<span class="print">print</span><span class="include">(</span>a<span class="include">)</span></code></pre><h4>v1:cross(v2)</h4><p>Parameters<br>
<code>vec3</code> v1<br>
<code>vec3</code> v2<br>
Return Value<br>
<code>number</code> returns cross product<br></p><pre><code class="lang-lua"><span class="local">local</span> a = <span class="hero">player</span>.pos:cross<span class="include">(</span>mousePos<span class="include">)</span>
<span class="print">print</span><span class="include">(</span>a<span class="include">)</span></code></pre><h4>v1:len()</h4><p>Parameters<br>
<code>vec3</code> v1<br>
Return Value<br>
<code>number</code> returns length of v1<br></p><pre><code class="lang-lua"><span class="local">local</span> a = mousePos - <span class="hero">player</span>.pos
<span class="local">local</span> b = a:len<span class="include">(</span><span class="include">)</span>
<span class="print">print</span><span class="include">(</span>b<span class="include">)</span></code></pre><h4>v1:lenSqr()</h4><p>Parameters<br>
<code>vec3</code> v1<br>
Return Value<br>
<code>number</code> returns squared length of v1<br></p><pre><code class="lang-lua"><span class="local">local</span> a = mousePos - <span class="hero">player</span>.pos
<span class="local">local</span> b = a:lenSqr<span class="include">(</span><span class="include">)</span>
<span class="print">print</span><span class="include">(</span>b<span class="include">)</span></code></pre><h4>v1:norm()</h4><p>Parameters<br>
<code>vec3</code> v1<br>
Return Value<br>
<code>vec3</code> returns normalized v1<br></p><pre><code class="lang-lua"><span class="local">local</span> a = mousePos - <span class="hero">player</span>.pos
<span class="local">local</span> b = a:norm<span class="include">(</span><span class="include">)</span>
b:<span class="print">print</span><span class="include">(</span><span class="include">)</span></code></pre><h4>v1:extend(to, range)</h4><p>Parameters<br>
<code>vec3</code> to<br>
<code>number</code> range<br>
Return Value<br>
<code>vec3</code> returns extended vec<br></p><pre><code class="lang-lua"><span class="local">local</span> a = <span class="hero">player</span>.pos:extend<span class="include">(</span>mousePos, <span class="number">100</span><span class="include">)</span>
a:<span class="print">print</span><span class="include">(</span><span class="include">)</span></code></pre><h4>v1:dist(v2)</h4><p>Parameters<br>
<code>vec3</code> v1<br>
<code>vec3</code> v2<br>
Return Value<br>
<code>number</code> returns distance between v1 and v2<br></p><pre><code class="lang-lua"><span class="local">local</span> a = mousePos:dist<span class="include">(</span><span class="hero">player</span>.pos<span class="include">)</span>
<span class="print">print</span><span class="include">(</span>a<span class="include">)</span></code></pre><h4>v1:distSqr(v2)</h4><p>Parameters<br>
<code>vec3</code> v1<br>
<code>vec3</code> v2<br>
Return Value<br>
<code>number</code> returns squared distance between v1 and v2<br></p><pre><code class="lang-lua"><span class="local">local</span> a = mousePos:distSqr<span class="include">(</span><span class="hero">player</span>.pos<span class="include">)</span>
<span class="print">print</span><span class="include">(</span>a<span class="include">)</span></code></pre><h4>v1:inRange(v2, range)</h4><p>Parameters<br>
<code>vec3</code> v1<br>
<code>vec3</code> v2<br>
<code>number</code> range<br>
Return Value<br>
<code>number</code> returns whether v2 in area from v1 to range<br></p><h4>v1:angle(v2)</h4><p>Parameters<br>
<code>vec3</code> v1<br>
<code>vec3</code> v2<br>
Return Value<br>
<code>number</code> returns angle (deg) between v1 and v2<br></p><h4>v1:perp1()</h4><p>Parameters<br>
<code>vec3</code> v1<br>
Return Value<br>
<code>vec3</code> returns perpendicular (left) vec3 to v1<br></p><pre><code class="lang-lua"><span class="local">local</span> a = mousePos-<span class="hero">player</span>.pos
<span class="local">local</span> b = a:norm<span class="include">(</span><span class="include">)</span>
<span class="local">local</span> c = b:perp<span class="number">1</span><span class="include">(</span><span class="include">)</span>
c:<span class="print">print</span><span class="include">(</span><span class="include">)</span></code></pre><h4>v1:perp2()</h4><p>Parameters<br>
<code>vec3</code> v1<br>
Return Value<br>
<code>vec3</code> returns perpendicular (right) vec3 to v1<br></p><pre><code class="lang-lua"><span class="local">local</span> a = mousePos-<span class="hero">player</span>.pos
<span class="local">local</span> b = a:norm<span class="include">(</span><span class="include">)</span>
<span class="local">local</span> c = b:perp<span class="number">2</span><span class="include">(</span><span class="include">)</span>
c:<span class="print">print</span><span class="include">(</span><span class="include">)</span></code></pre><h4>v1:lerp(v2, s)</h4><p>Parameters<br>
<code>vec3</code> v1<br>
<code>vec3</code> v2<br>
<code>number</code> s<br>
Return Value<br>
<code>vec3</code> returns interpolated vec3 between v1 and v2 by factor s<br></p><pre><code class="lang-lua"><span class="local">local</span> a = <span class="hero">player</span>.pos:lerp<span class="include">(</span>mousePos, <span class="number"><span class="number">0</span>.<span class="number">5</span></span><span class="include">)</span>
a:<span class="print">print</span><span class="include">(</span><span class="include">)</span></code></pre><h4>v1:clone()</h4><p>Parameters<br>
<code>vec3</code> v1<br>
Return Value<br>
<code>vec3</code> returns cloned v1<br></p><pre><code class="lang-lua"><span class="local">local</span> a = <span class="hero">player</span>.pos:clone<span class="include">(</span><span class="include">)</span>
a:<span class="print">print</span><span class="include">(</span><span class="include">)</span></code></pre><h4>v1:to2D()</h4><p>Parameters<br>
<code>vec3</code> v1<br>
Return Value<br>
<code>vec2</code> returns vec2 from x and z properties<br></p><pre><code class="lang-lua"><span class="local">local</span> a = vec<span class="number">3</span><span class="include">(</span><span class="hero">player</span>.x, <span class="hero">player</span>.y, <span class="hero">player</span>.z<span class="include">)</span>
<span class="local">local</span> b = a:to<span class="number">2</span>D<span class="include">(</span><span class="include">)</span>
b:<span class="print">print</span><span class="include">(</span><span class="include">)</span></code></pre><h4>v1:rotate(s)</h4><p>Parameters<br>
<code>vec3</code> v1<br>
<code>number</code> s<br>
Return Value<br>
<code>vec3</code> returns vec3 rotated by s radians<br></p><pre><code class="lang-lua"><span class="local">local</span> a = <span class="include">(</span>mousePos-<span class="hero">player</span>.pos<span class="include">)</span>
<span class="local">local</span> b = a:norm<span class="include">(</span><span class="include">)</span>
<span class="local">local</span> c = b:rotate<span class="include">(</span><span class="number"><span class="number">0</span>.<span class="number">785398</span></span><span class="include">)</span>
c:<span class="print">print</span><span class="include">(</span><span class="include">)</span></code></pre><h4>v1:countAllies(range)</h4><p>Parameters<br>
<code>vec3</code> v1<br>
<code>number</code> range<br>
Return Value<br>
<code>number</code><br></p><h4>v1:countEnemies(range)</h4><p>Parameters<br>
<code>vec3</code> v1<br>
<code>number</code> range<br>
Return Value<br>
<code>number</code><br></p><h4>v1:countAllyLaneMinions(range)</h4><p>Parameters<br>
<code>vec3</code> v1<br>
<code>number</code> range<br>
Return Value<br>
<code>number</code><br></p><h4>v1:countEnemyLaneMinions(range)</h4><p>Parameters<br>
<code>vec3</code> v1<br>
<code>number</code> range<br>
Return Value<br>
<code>number</code><br></p><h4>v1:isUnderEnemyTurret(range)</h4><p>Parameters<br>
<code>vec3</code> v1<br>
<code>number</code> range, extra range, optional<br>
Return Value<br>
<code>boolean</code><br></p><h4>v1:isUnderAllyTurret(range)</h4><p>Parameters<br>
<code>vec3</code> v1<br>
<code>number</code> range, extra range, optional<br>
Return Value<br>
<code>boolean</code><br></p><h4>v1:print()</h4><p>Parameters<br>
<code>vec3</code> v1<br>
Return Value<br>
<code>void</code></p><pre><code class="lang-lua"><span class="local">local</span> a = vec<span class="number">3</span><span class="include">(</span>mousePos.x, mousePos.y, mousePos.z<span class="include">)</span>
a:<span class="print">print</span><span class="include">(</span><span class="include">)</span></code></pre><h4>vec3.array(n)</h4><p>Parameters<br>
<code>number</code> n<br>
Return Value<br>
<code>vec3[?]</code> returns vec3 array of length n<br></p><pre><code class="lang-lua"><span class="local">local</span> a = vec<span class="number">3</span>.array<span class="include">(</span><span class="number">12</span><span class="include">)</span>
a[<span class="number">0</span>].x = <span class="number">100</span>
a[<span class="number">0</span>].y = <span class="number">50</span>
a[<span class="number">0</span>].z = <span class="number">200</span>
a[<span class="number">0</span>]:<span class="print">print</span><span class="include">(</span><span class="include">)</span></code></pre><h3 id="math-vec4">vec4</h3><p>vec4(number, number, number)<br>
vec4(vec4)<br></p><h4>v1:dot(v2)</h4><p>Parameters<br>
<code>vec4</code> v1<br>
<code>vec4</code> v2<br>
Return Value<br>
<code>number</code> returns dot product<br></p><h4>v1:cross(v2)</h4><p>Parameters<br>
<code>vec4</code> v1<br>
<code>vec4</code> v2<br>
Return Value<br>
<code>number</code> returns cross product<br></p><h4>v1:len()</h4><p>Parameters<br>
<code>vec4</code> v1<br>
Return Value<br>
<code>number</code> returns length of v1<br></p><h4>v1:lenSqr()</h4><p>Parameters<br>
<code>vec4</code> v1<br>
Return Value<br>
<code>number</code> returns squared length of v1<br></p><h4>v1:norm()</h4><p>Parameters<br>
<code>vec4</code> v1<br>
Return Value<br>
<code>vec4</code> returns normalized v1<br></p><h4>v1:dist(v2)</h4><p>Parameters<br>
<code>vec4</code> v1<br>
<code>vec4</code> v2<br>
Return Value<br>
<code>number</code> returns distance between v1 and v2<br></p><h4>v1:distSqr(v2)</h4><p>Parameters<br>
<code>vec4</code> v1<br>
<code>vec4</code> v2<br>
Return Value<br>
<code>number</code> returns squared distance between v1 and v2<br></p><h4>v1:perp1()</h4><p>Parameters<br>
<code>vec4</code> v1<br>
Return Value<br>
<code>vec4</code> returns perpendicular (left) vec4 to v1<br></p><h4>v1:perp2()</h4><p>Parameters<br>
<code>vec4</code> v1<br>
Return Value<br>
<code>vec4</code> returns perpendicular (right) vec4 to v1<br></p><h4>v1:lerp(v2, s)</h4><p>Parameters<br>
<code>vec4</code> v1<br>
<code>vec4</code> v2<br>
<code>number</code> s<br>
Return Value<br>
<code>vec4</code> returns interpolated vec4 between v1 and v2 by factor s<br></p><h4>v1:clone()</h4><p>Parameters<br>
<code>vec4</code> v1<br>
Return Value<br>
<code>vec4</code> returns cloned v1<br></p><h4>v1:to2D()</h4><p>Parameters<br>
<code>vec4</code> v1<br>
Return Value<br>
<code>vec2</code> returns vec2 from x and z properties<br></p><h4>v1:rotate(s)</h4><p>Parameters<br>
<code>vec4</code> v1<br>
<code>number</code> s<br>
Return Value<br>
<code>vec4</code> returns vec4 rotated by s radians<br></p><h4>v1:print()</h4><p>Parameters<br>
<code>vec4</code> v1<br>
Return Value<br>
<code>void</code></p><h4>vec4.array(n)</h4><p>Parameters<br>
<code>number</code> n<br>
Return Value<br>
<code>vec4[?]</code> returns vec4 array of length n<br></p><h3 id="math-seg2">seg2</h3><p><code>vec2</code> seg2.startPos<br>
<code>vec2</code> seg2.endPos</p><h4>v1:len()</h4><p>Parameters<br>
<code>seg2</code> v1<br>
Return Value<br>
<code>number</code> returns length of v1<br></p><h4>v1:lenSqr()</h4><p>Parameters<br>
<code>seg2</code> v1<br>
Return Value<br>
<code>number</code> returns squared length of v1<br></p><h3 id="math-mathf">mathf</h3><p>A library of commonly used 2D math functions.<br></p><h4>mathf.PI</h4><p>Return Value<br>
<code>number</code> returns pi<br></p><pre><code class="lang-lua"><span class="print">print</span><span class="include">(</span>mathf.PI<span class="include">)</span></code></pre><h4>mathf.cos(n)</h4><p>Parameters<br>
<code>number</code> n<br>
Return Value<br>
<code>number</code> returns the cosine of n<br></p><pre><code class="lang-lua"><span class="local">local</span> a = mathf.cos<span class="include">(</span>mathf.PI<span class="include">)</span>
<span class="print">print</span><span class="include">(</span>a<span class="include">)</span></code></pre><h4>mathf.sin(n)</h4><p>Parameters<br>
<code>number</code> n<br>
Return Value<br>
<code>number</code> returns the sine of n<br></p><pre><code class="lang-lua"><span class="local">local</span> a = mathf.sin<span class="include">(</span>mathf.PI<span class="include">)</span>
<span class="print">print</span><span class="include">(</span>a<span class="include">)</span></code></pre><h4>mathf.round(n, d)</h4><p>Parameters<br>
<code>number</code> n<br>
<code>number</code> d<br>
Return Value<br>
<code>number</code> returns rounded n to precision d<br></p><pre><code class="lang-lua"><span class="local">local</span> a = mathf.round<span class="include">(</span><span class="number"><span class="number">1</span>.<span class="number">234567</span></span>, <span class="number">3</span><span class="include">)</span>
<span class="print">print</span><span class="include">(</span>a<span class="include">)</span></code></pre><h4>mathf.sqr(n)</h4><p>Parameters<br>
<code>number</code> n<br>
Return Value<br>
<code>number</code> returns squared value of n<br></p><pre><code class="lang-lua"><span class="local">local</span> a = mathf.sqr<span class="include">(</span><span class="number">2</span><span class="include">)</span>
<span class="print">print</span><span class="include">(</span>a<span class="include">)</span></code></pre><h4>mathf.clamp(min, max, n)</h4><p>Parameters<br>
<code>number</code> min<br>
<code>number</code> max<br>
<code>number</code> n<br>
Return Value<br>
<code>number</code> returns clamped n between min and max<br></p><pre><code class="lang-lua"><span class="local">local</span> a = mathf.clamp<span class="include">(</span><span class="number">0</span>, <span class="number">25</span>, -<span class="number">1</span><span class="include">)</span>
<span class="print">print</span><span class="include">(</span>a<span class="include">)</span></code></pre><h4>mathf.sect_line_line(v1, v3, v3, v4)</h4><p>Parameters<br>
<code>vec2</code> v1<br>
<code>vec2</code> v2<br>
<code>vec2</code> v3<br>
<code>vec2</code> v4<br>
Return Value<br>
<code>vec2</code> returns intersection point of lines (v1, v2) and (v3, v4)<br></p><pre><code class="lang-lua"><span class="local">local</span> v<span class="number">1</span> = vec<span class="number">2</span><span class="include">(</span><span class="number">0</span>, <span class="number">0</span><span class="include">)</span>
<span class="local">local</span> v<span class="number">2</span> = vec<span class="number">2</span><span class="include">(</span><span class="number">100</span>,<span class="number">100</span><span class="include">)</span>
<span class="local">local</span> v<span class="number">3</span> = vec<span class="number">2</span><span class="include">(</span><span class="number">100</span>, <span class="number">0</span><span class="include">)</span>
<span class="local">local</span> v<span class="number">4</span> = vec<span class="number">2</span><span class="include">(</span><span class="number">0</span>,<span class="number">100</span><span class="include">)</span>
<span class="local">local</span> a = mathf.sect_line_line<span class="include">(</span>v<span class="number">1</span>, v<span class="number">2</span>, v<span class="number">3</span>, v<span class="number">4</span><span class="include">)</span>
a:<span class="print">print</span><span class="include">(</span><span class="include">)</span></code></pre><h4>mathf.dist_line_vector(v1, v2, v3)</h4><p>Parameters<br>
<code>vec2</code> v1<br>
<code>vec2</code> v2<br>
<code>vec2</code> v3<br>
Return Value<br>
<code>number</code> returns distance between v1 and line (v2, v3)<br></p><pre><code class="lang-lua"><span class="local">local</span> v<span class="number">1</span> = vec<span class="number">2</span><span class="include">(</span><span class="number">0</span>, <span class="number">0</span><span class="include">)</span>
<span class="local">local</span> v<span class="number">2</span> = vec<span class="number">2</span><span class="include">(</span><span class="number">100</span>, <span class="number">0</span><span class="include">)</span>
<span class="local">local</span> v<span class="number">3</span> = vec<span class="number">2</span><span class="include">(</span><span class="number">0</span>,<span class="number">100</span><span class="include">)</span>
<span class="local">local</span> a = mathf.dist_line_vector<span class="include">(</span>v<span class="number">1</span>, v<span class="number">2</span>, v<span class="number">3</span><span class="include">)</span>
<span class="print">print</span><span class="include">(</span>a<span class="include">)</span></code></pre><h4>mathf.angle_between(v1, v2, v3)</h4><p>Parameters<br>
<code>vec2</code> v1<br>
<code>vec2</code> v2<br>
<code>vec2</code> v3<br>
Return Value<br>
<code>number</code> returns the angle between v2 and v3 from origin v1 in radians<br></p><pre><code class="lang-lua"><span class="local">local</span> v<span class="number">1</span> = vec<span class="number">2</span><span class="include">(</span><span class="number">0</span>, <span class="number">0</span><span class="include">)</span>
<span class="local">local</span> a = mathf.angle_between<span class="include">(</span>v<span class="number">1</span>, <span class="hero">player</span>.pos<span class="number">2</span>D, mousePos<span class="number">2</span>D<span class="include">)</span>
<span class="print">print</span><span class="include">(</span>a<span class="include">)</span></code></pre><h4>mathf.mec(pts, n)</h4><p>Parameters<br>
<code>vec2[]</code> pts<br>
<code>number</code> n<br>
Return Value<br>
<code>vec2</code> returns the center of the minimum enclosing circle<br>
<code>number</code> returns the radius of the minimum enclosing circle<br></p><pre><code class="lang-lua"><span class="local">local</span> pts = vec<span class="number">2</span>.array<span class="include">(</span><span class="number">2</span><span class="include">)</span>
pts[<span class="number">0</span>].x, pts[<span class="number">0</span>].y = <span class="number">0</span>, <span class="number">0</span>
pts[<span class="number">1</span>].x, pts[<span class="number">1</span>].y = mousePos.x, mousePos.z
pts[<span class="number">2</span>].x, pts[<span class="number">2</span>].y = <span class="hero">player</span>.x, <span class="hero">player</span>.z
<span class="local">local</span> c, n = mathf.mec<span class="include">(</span>pts, <span class="number">2</span><span class="include">)</span>
<span class="print">print</span><span class="include">(</span>c.x, c.y, n<span class="include">)</span></code></pre><h4>mathf.project(v1, v2, v3, s1, s2)</h4><p>Parameters<br>
<code>vec2</code> v1<br>
<code>vec2</code> v2<br>
<code>vec2</code> v3<br>
<code>number</code> s1<br>
<code>number</code> s2<br>
Return Value<br>
<code>vec2</code> returns the collision point<br>
<code>number</code> returns the time until collision occurs<br></p><pre><code class="lang-lua"><span class="note">--</span>calculates collision position and time of a projectile from mousePos along the players <span class="cbfunc">path</span>
<span class="local">local</span> src = mousePos<span class="number">2</span>D
<span class="local">local</span> pos_s = <span class="hero">player</span>.<span class="cbfunc">path</span>.serverPos<span class="number">2</span>D
<span class="local">local</span> pos_e = <span class="hero">player</span>.<span class="cbfunc">path</span>.point<span class="number">2</span>D[<span class="hero">player</span>.<span class="cbfunc">path</span>.index]
<span class="local">local</span> s<span class="number">1</span> = <span class="number">2000</span>
<span class="local">local</span> s<span class="number">2</span> = <span class="hero">player</span>.moveSpeed

<span class="local">local</span> res, res_t = mathf.project<span class="include">(</span>src, pos_s, pos_e, s<span class="number">1</span>, s<span class="number">2</span><span class="include">)</span>
<span class="keyword">if</span> res <span class="keyword">then</span>
    <span class="print">print</span><span class="include">(</span>res.x, res.y, res_t<span class="include">)</span>
<span class="keyword">end</span></code></pre><h4>mathf.dist_seg_seg(v1, v2, v3, v4)</h4><p>Parameters<br>
<code>vec2</code> v1<br>
<code>vec2</code> v2<br>
<code>vec2</code> v3<br>
<code>vec2</code> v4<br>
Return Value<br>
<code>number</code> returns the distance between line segments (v1, v2) and (v3, v4)<br></p><pre><code class="lang-lua"><span class="local">local</span> v<span class="number">1</span> = vec<span class="number">2</span><span class="include">(</span><span class="number">0</span>,<span class="number">0</span><span class="include">)</span>
<span class="local">local</span> v<span class="number">2</span> = vec<span class="number">2</span><span class="include">(</span><span class="number">1000</span>,<span class="number">1000</span><span class="include">)</span>
<span class="local">local</span> v<span class="number">3</span> = mousePos<span class="number">2</span>D
<span class="local">local</span> v<span class="number">4</span> = <span class="hero">player</span>.pos<span class="number">2</span>D
<span class="local">local</span> res = mathf.dist_seg_seg<span class="include">(</span>v<span class="number">1</span>, v<span class="number">2</span>, v<span class="number">3</span>, v<span class="number">4</span><span class="include">)</span>
<span class="print">print</span><span class="include">(</span>res<span class="include">)</span></code></pre><h4>mathf.closest_vec_line(v1, v2, v3)</h4><p>Parameters<br>
<code>vec2</code> v1<br>
<code>vec2</code> v2<br>
<code>vec2</code> v3<br>
Return Value<br>
<code>vec2</code> returns a vec2 along the line (v2, v3) closest to v1<br></p><pre><code class="lang-lua"><span class="local">local</span> v<span class="number">1</span> = <span class="hero">player</span>.pos<span class="number">2</span>D
<span class="local">local</span> v<span class="number">2</span> = vec<span class="number">2</span><span class="include">(</span><span class="number">0</span>,<span class="number">0</span><span class="include">)</span>
<span class="local">local</span> v<span class="number">3</span> = mousePos<span class="number">2</span>D
<span class="local">local</span> res = mathf.closest_vec_line<span class="include">(</span>v<span class="number">1</span>, v<span class="number">2</span>, v<span class="number">3</span><span class="include">)</span>
res:<span class="print">print</span><span class="include">(</span><span class="include">)</span></code></pre><h4>mathf.closest_vec_line_seg(v1, v2, v3)</h4><p>Parameters<br>
<code>vec2</code> v1<br>
<code>vec2</code> v2<br>
<code>vec2</code> v3<br>
Return Value<br>
<code>vec2</code> returns a vec2 along the line segment (v2, v3) closest to v1<br></p><pre><code class="lang-lua"><span class="local">local</span> v<span class="number">1</span> = <span class="hero">player</span>.pos<span class="number">2</span>D
<span class="local">local</span> v<span class="number">2</span> = vec<span class="number">2</span><span class="include">(</span><span class="number">0</span>,<span class="number">0</span><span class="include">)</span>
<span class="local">local</span> v<span class="number">3</span> = mousePos<span class="number">2</span>D
<span class="local">local</span> res = mathf.closest_vec_line_seg<span class="include">(</span>v<span class="number">1</span>, v<span class="number">2</span>, v<span class="number">3</span><span class="include">)</span>
<span class="keyword">if</span> res <span class="keyword">then</span>
    res:<span class="print">print</span><span class="include">(</span><span class="include">)</span>
<span class="keyword">end</span></code></pre><h4>mathf.col_vec_rect(v1, v2, v3, w1, w2)</h4><p>Parameters<br>
<code>vec2</code> v1<br>
<code>vec2</code> v2<br>
<code>vec2</code> v3<br>
<code>number</code> w1<br>
<code>number</code> w2<br>
Return Value<br>
<code>boolean</code> returns true if v1 with bounding radius of w1 collides with line (v2, v3) of width w2<br></p><pre><code class="lang-lua"><span class="local">local</span> v<span class="number">1</span> = <span class="hero">player</span>.pos<span class="number">2</span>D
<span class="local">local</span> v<span class="number">2</span> = vec<span class="number">2</span><span class="include">(</span><span class="number">0</span>, <span class="number">0</span><span class="include">)</span>
<span class="local">local</span> v<span class="number">3</span> = mousePos<span class="number">2</span>D
<span class="local">local</span> w<span class="number">1</span> = <span class="hero">player</span>.boundingRadius
<span class="local">local</span> w<span class="number">2</span> = <span class="number">100</span>
<span class="local">local</span> res = mathf.col_vec_rect<span class="include">(</span>v<span class="number">1</span>, v<span class="number">2</span>, v<span class="number">3</span>, w<span class="number">1</span>, w<span class="number">2</span><span class="include">)</span>
<span class="print">print</span><span class="include">(</span>res<span class="include">)</span></code></pre><h4>mathf.sect_circle_circle(v1, r1, v2, r2)</h4><p>Parameters<br>
<code>vec2</code> v1<br>
<code>number</code> r1<br>
<code>vec2</code> v2<br>
<code>number</code> r2<br>
Return Value<br>
<code>vec2</code> returns first intersection of circles at v1 with radius of r1 and v2 with radius of r2<br>
<code>vec2</code> returns second intersection of circles at v1 with radius of r1 and v2 with radius of r2<br></p><pre><code class="lang-lua"><span class="local">local</span> v<span class="number">1</span> = mousePos<span class="number">2</span>D
<span class="local">local</span> r<span class="number">1</span> = <span class="number">500</span>
<span class="local">local</span> v<span class="number">2</span> = <span class="hero">player</span>.pos<span class="number">2</span>D
<span class="local">local</span> r<span class="number">2</span> = <span class="hero">player</span>.attackRange
<span class="local">local</span> res<span class="number">1</span>, res<span class="number">2</span> = mathf.sect_circle_circle<span class="include">(</span>v<span class="number">1</span>, r<span class="number">1</span>, v<span class="number">2</span>, r<span class="number">2</span><span class="include">)</span>
<span class="keyword">if</span> res<span class="number">1</span> <span class="keyword">then</span>
    res<span class="number">1</span>:<span class="print">print</span><span class="include">(</span><span class="include">)</span>
    res<span class="number">2</span>:<span class="print">print</span><span class="include">(</span><span class="include">)</span>
<span class="keyword">end</span></code></pre><h4>mathf.sect_line_circle(v1, v2, v3, r)</h4><p>Parameters<br>
<code>vec2</code> v1<br>
<code>vec2</code> v2<br>
<code>vec2</code> v3<br>
<code>number</code> r<br>
Return Value<br>
<code>vec2</code> returns first intersection of line (v1, v2) and circle v3 with radius of r<br>
<code>vec2</code> returns second intersection of line (v1, v2) and circle v3 with radius of r<br></p><pre><code class="lang-lua"><span class="local">local</span> v<span class="number">1</span> = vec<span class="number">2</span><span class="include">(</span><span class="number">0</span>, <span class="number">0</span><span class="include">)</span>
<span class="local">local</span> v<span class="number">2</span> = mousePos<span class="number">2</span>D
<span class="local">local</span> v<span class="number">3</span> = <span class="hero">player</span>.pos<span class="number">2</span>D
<span class="local">local</span> r = <span class="hero">player</span>.attackRange
<span class="local">local</span> res<span class="number">1</span>, res<span class="number">2</span> = mathf.sect_line_circle<span class="include">(</span>v<span class="number">1</span>, v<span class="number">2</span>, v<span class="number">3</span>, r<span class="include">)</span>
<span class="keyword">if</span> res<span class="number">1</span> <span class="keyword">then</span>
    res<span class="number">1</span>:<span class="print">print</span><span class="include">(</span><span class="include">)</span>
<span class="keyword">end</span>
<span class="keyword">if</span> res<span class="number">2</span> <span class="keyword">then</span>
    res<span class="number">2</span>:<span class="print">print</span><span class="include">(</span><span class="include">)</span>
<span class="keyword">end</span></code></pre><h4>mathf.mat2()</h4><p>Return Value<br>
<code>double[2][2]</code> returns a 2x2 transformation matrix<br></p><h4>mathf.mat3()</h4><p>Return Value<br>
<code>double[3][3]</code> returns a 3x3 transformation matrix<br></p><h4>mathf.mat4()</h4><p>Return Value<br>
<code>double[4][4]</code> returns a 4x4 transformation matrix<br></p><h3 id="math-clipper">clipper</h3><p>Unlike the other math libraries, clipper must first be loaded.<br>
Further documentation can be found <a href="http://www.angusj.com/delphi/clipper/documentation/Docs/_Body.htm">here</a>.</p><pre><code class="lang-lua"><span class="local">local</span> clip = <span class="module">module</span>.<span class="module">internal</span><span class="include">(</span><span class="string">'clipper'</span><span class="include">)</span>
<span class="local">local</span> polygon = clip.polygon
<span class="local">local</span> polygons = clip.polygons
<span class="local">local</span> clipper = clip.clipper
<span class="local">local</span> clipper_enum = clip.enum</code></pre><p>Enums:</p><ul>
<li>clipper_enum.PolyFillType.EvenOdd</li>
<li>clipper_enum.PolyFillType.NonZero</li>
<li>clipper_enum.PolyFillType.Positive</li>
<li>clipper_enum.PolyFillType.Negative</li>
<li>clipper_enum.JoinType.Square</li>
<li>clipper_enum.JoinType.Round</li>
<li>clipper_enum.JoinType.Miter</li>
<li>clipper_enum.ClipType.Intersection</li>
<li>clipper_enum.ClipType.Union</li>
<li>clipper_enum.ClipType.Difference</li>
<li>clipper_enum.ClipType.Xor</li>
<li>clipper_enum.PolyType.Subject</li>
<li>clipper_enum.PolyType.Clip</li>
</ul><h4>polygon:Add(v1)</h4><p>Parameters<br>
<code>vec2</code> v1<br>
Return Value<br>
<code>void</code><br></p><pre><code class="lang-lua"><span class="local">local</span> p = polygon<span class="include">(</span><span class="include">)</span>
<span class="local">local</span> v = vec<span class="number">2</span><span class="include">(</span><span class="number">200</span>, <span class="number">200</span><span class="include">)</span>
p:Add<span class="include">(</span>v<span class="include">)</span></code></pre><h4>polygon:ChildCount()</h4><p>Return Value<br>
<code>number</code> returns number of vertices<br></p><pre><code class="lang-lua"><span class="local">local</span> p = polygon<span class="include">(</span>vec<span class="number">2</span><span class="include">(</span><span class="number">200</span>, <span class="number">200</span><span class="include">)</span>, vec<span class="number">2</span><span class="include">(</span><span class="number">200</span>, <span class="number">100</span><span class="include">)</span>, vec<span class="number">2</span><span class="include">(</span><span class="number">100</span>, <span class="number">200</span><span class="include">)</span><span class="include">)</span>

<span class="print">print</span><span class="include">(</span>p:ChildCount<span class="include">(</span><span class="include">)</span><span class="include">)</span></code></pre><h4>polygon:Childs(i)</h4><p>Parameters<br>
<code>number</code> i<br>
Return Value<br>
<code>vec2</code> returns vec2 at vertex i<br></p><pre><code class="lang-lua"><span class="local">local</span> p = polygon<span class="include">(</span>vec<span class="number">2</span><span class="include">(</span><span class="number">200</span>, <span class="number">200</span><span class="include">)</span>, vec<span class="number">2</span><span class="include">(</span><span class="number">200</span>, <span class="number">100</span><span class="include">)</span>, vec<span class="number">2</span><span class="include">(</span><span class="number">100</span>, <span class="number">200</span><span class="include">)</span><span class="include">)</span>

<span class="local">local</span> v = p:Childs<span class="include">(</span><span class="number">1</span><span class="include">)</span>
v:<span class="print">print</span><span class="include">(</span><span class="include">)</span></code></pre><h4>polygon:Area()</h4><p>Return Value<br>
<code>number</code> returns the area of polygon<br></p><pre><code class="lang-lua"><span class="local">local</span> p = polygon<span class="include">(</span>vec<span class="number">2</span><span class="include">(</span><span class="number">200</span>, <span class="number">200</span><span class="include">)</span>, vec<span class="number">2</span><span class="include">(</span><span class="number">200</span>, <span class="number">100</span><span class="include">)</span>, vec<span class="number">2</span><span class="include">(</span><span class="number">100</span>, <span class="number">200</span><span class="include">)</span><span class="include">)</span>

<span class="print">print</span><span class="include">(</span>p:Area<span class="include">(</span><span class="include">)</span><span class="include">)</span></code></pre><h4>polygon:Clean(dist)</h4><p>Parameters<br>
<code>number</code> dist<br>
Return Value<br>
<code>void</code><br></p><pre><code class="lang-lua"><span class="local">local</span> p = polygon<span class="include">(</span>vec<span class="number">2</span><span class="include">(</span><span class="number">200</span>, <span class="number">200</span><span class="include">)</span>, vec<span class="number">2</span><span class="include">(</span><span class="number">200</span>, <span class="number">100</span><span class="include">)</span>, vec<span class="number">2</span><span class="include">(</span><span class="number">200</span>, <span class="number">101</span><span class="include">)</span>, vec<span class="number">2</span><span class="include">(</span><span class="number">100</span>, <span class="number">200</span><span class="include">)</span><span class="include">)</span>

<span class="print">print</span><span class="include">(</span>p:ChildCount<span class="include">(</span><span class="include">)</span><span class="include">)</span>
p:Clean<span class="include">(</span><span class="number">5</span><span class="include">)</span>
<span class="print">print</span><span class="include">(</span>p:ChildCount<span class="include">(</span><span class="include">)</span><span class="include">)</span></code></pre><h4>polygon:Simplify(PolyFillType)</h4><p>Parameters<br>
<code>number</code> PolyFillType<br>
Return Value<br>
<code>polygons</code> returns polygon set<br></p><pre><code class="lang-lua"><span class="local">local</span> p<span class="number">1</span> = polygon<span class="include">(</span>vec<span class="number">2</span><span class="include">(</span><span class="number">5</span>,<span class="number">62</span><span class="include">)</span>, vec<span class="number">2</span><span class="include">(</span><span class="number">164</span>,<span class="number">62</span><span class="include">)</span>, vec<span class="number">2</span><span class="include">(</span><span class="number">36</span>,<span class="number">157</span><span class="include">)</span>, vec<span class="number">2</span><span class="include">(</span><span class="number">85</span>, <span class="number">4</span><span class="include">)</span>, vec<span class="number">2</span><span class="include">(</span><span class="number">134</span>, <span class="number">158</span><span class="include">)</span><span class="include">)</span>
<span class="local">local</span> p = p<span class="number">1</span>:Simplify<span class="include">(</span>clipper_enum.PolyFillType.NonZero<span class="include">)</span>
<span class="local">local</span> p<span class="number">2</span> = p:Childs<span class="include">(</span><span class="number">0</span><span class="include">)</span>

<span class="cb">cb</span>.<span class="add">add</span><span class="include">(</span><span class="cb">cb</span>.<span class="cbfunc">draw</span>, <span class="keyword">function</span><span class="include">(</span><span class="include">)</span>
    p<span class="number">2</span>:Draw<span class="number">2</span>D<span class="include">(</span><span class="number">5</span>, <span class="number">0</span>xFF<span class="number">00</span>FFFF<span class="include">)</span>
    p<span class="number">1</span>:Draw<span class="number">2</span>D<span class="include">(</span><span class="number">2</span>, <span class="number">0</span>xFFFF<span class="number">00</span>FF<span class="include">)</span>
<span class="keyword">end</span><span class="include">)</span></code></pre><h4>polygon:Orientation()</h4><p>Return Value<br>
<code>number</code> returns 1 if polygon has clockwise orientation<br></p><h4>polygon:Reverse()</h4><p>Return Value<br>
<code>void</code> reverses polygons orientation<br></p><h4>polygon:Contains(v1)</h4><p>Parameters<br>
<code>vec2\vec3</code> v1<br>
Return Value<br>
<code>number</code> returns 1 if v1 is inside of polygon, 0 if outside, -1 if v1 is on polygon edge<br></p><pre><code class="lang-lua"><span class="local">local</span> p<span class="number">1</span> = polygon<span class="include">(</span>vec<span class="number">2</span><span class="include">(</span><span class="number">200</span>,<span class="number">200</span><span class="include">)</span>, vec<span class="number">2</span><span class="include">(</span><span class="number">200</span>,<span class="number">300</span><span class="include">)</span>, vec<span class="number">2</span><span class="include">(</span><span class="number">300</span>,<span class="number">300</span><span class="include">)</span>, vec<span class="number">2</span><span class="include">(</span><span class="number">300</span>, <span class="number">200</span><span class="include">)</span><span class="include">)</span>
<span class="cb">cb</span>.<span class="add">add</span><span class="include">(</span><span class="cb">cb</span>.<span class="cbfunc">draw</span>, <span class="keyword">function</span><span class="include">(</span><span class="include">)</span>
    p<span class="number">1</span>:Draw<span class="number">2</span>D<span class="include">(</span><span class="number">2</span>, p<span class="number">1</span>:Contains<span class="include">(</span>game.cursorPos<span class="include">)</span>==<span class="number">1</span> and <span class="number">0</span>xFF<span class="number">00</span>FF<span class="number">00</span> or <span class="number">0</span>xFFFF<span class="number">0000</span><span class="include">)</span>
<span class="keyword">end</span><span class="include">)</span></code></pre><h4>polygon:Draw2D(width, color)</h4><p>Parameters<br>
<code>number</code> width<br>
<code>number</code> color<br>
Return Value<br>
<code>void</code><br></p><h4>polygon:Draw3D(y, width, color)</h4><p>Parameters<br>
<code>number</code> y<br>
<code>number</code> width<br>
<code>number</code> color<br>
Return Value<br>
<code>void</code><br></p><h4>polygon:OnScreen2D()</h4><p>Return Value<br>
<code>boolean</code> returns true if polygon intersects the screen<br></p><h4>polygon:OnScreen3D(y)</h4><p>Parameters<br>
<code>number</code> y<br>
Return Value<br>
<code>boolean</code> returns true if polygon intersects the screen<br></p><h4>polygons:Add(polygon)</h4><p>Parameters<br>
<code>polygon</code> polygon<br>
Return Value<br>
<code>void</code><br></p><h4>polygons:ChildCount()</h4><p>Return Value<br>
<code>number</code> returns number of polygons contained polygon set<br></p><h4>polygons:Childs(i)</h4><p>Parameters<br>
<code>number</code> i<br>
Return Value<br>
<code>polygon</code> returns polygon at index i<br></p><h4>polygons:Reverse()</h4><p>Return Value<br>
<code>void</code> reverses the orientation of all containing polygons<br></p><h4>polygons:Clean(dist)</h4><p>Parameters<br>
<code>number</code> dist<br>
Return Value<br>
<code>void</code> cleans all contained polygons<br></p><h4>polygons:Simplify(PolyFillType)</h4><p>Parameters<br>
<code>number</code> PolyFillType<br>
Return Value<br>
<code>polygons</code> returns polygon set<br></p><h4>polygons:Offset(delta, JoinType, limit)</h4><p>Parameters<br>
<code>number</code> delta<br>
<code>number</code> JoinType<br>
<code>number</code> limit<br>
Return Value<br>
<code>polygons</code> returns polygon set<br></p><h4>clipper:AddPath(polygon, PolyType, closed)</h4><p>Parameters<br>
<code>polygon</code> polygon<br>
<code>number</code> PolyType<br>
<code>boolean</code> closed<br>
Return Value<br>
<code>void</code><br></p><h4>clipper:AddPaths(polygons, PolyType, closed)</h4><p>Parameters<br>
<code>polygons</code> polygons<br>
<code>number</code> PolyType<br>
<code>boolean</code> closed<br>
Return Value<br>
<code>void</code><br></p><h4>clipper:Clear()</h4><p>Return Value<br>
<code>void</code><br></p><h4>clipper:Execute(ClipType, PolyFillType, PolyFillType)</h4><p>Parameters<br>
<code>number</code> ClipType<br>
<code>number</code> PolyFillType<br>
<code>number</code> PolyFillType<br>
Return Value<br>
<code>polygons</code> returns polygon set<br></p><pre><code class="lang-lua"><span class="note">--</span> Example to calc DariusQ Outer Ring

<span class="local">local</span> clip = <span class="module">module</span>.<span class="module">internal</span><span class="include">(</span><span class="string">'clipper'</span><span class="include">)</span>
<span class="local">local</span> polygon = clip.polygon
<span class="local">local</span> polygons = clip.polygons
<span class="local">local</span> clipper = clip.clipper
<span class="local">local</span> clipper_enum = clip.enum

<span class="local">local</span> <span class="keyword">function</span> create_ring<span class="include">(</span>center, outer_radius, inner_radius<span class="include">)</span>
    <span class="local">local</span> outer_ring = polygon<span class="include">(</span><span class="include">)</span>
    <span class="local">local</span> inner_ring = polygon<span class="include">(</span><span class="include">)</span>
    <span class="keyword">for</span> i = <span class="number">1</span>, <span class="number">64</span> <span class="keyword">do</span>
        <span class="local">local</span> angle = <span class="include">(</span>i - <span class="number">1</span><span class="include">)</span> * <span class="include">(</span><span class="number">2</span> * math.pi / <span class="number">64</span><span class="include">)</span>
        <span class="local">local</span> x_outer = center.pos.x + outer_radius * math.cos<span class="include">(</span>angle<span class="include">)</span>
        <span class="local">local</span> y_outer = center.pos.z + outer_radius * math.sin<span class="include">(</span>angle<span class="include">)</span>
        <span class="local">local</span> x_inner = center.pos.x + inner_radius * math.cos<span class="include">(</span>angle<span class="include">)</span>
        <span class="local">local</span> y_inner = center.pos.z + inner_radius * math.sin<span class="include">(</span>angle<span class="include">)</span>
        outer_ring:Add<span class="include">(</span>vec<span class="number">2</span><span class="include">(</span>x_outer, y_outer<span class="include">)</span><span class="include">)</span>
        inner_ring:Add<span class="include">(</span>vec<span class="number">2</span><span class="include">(</span>x_inner, y_inner<span class="include">)</span><span class="include">)</span>
    <span class="keyword">end</span>

    <span class="local">local</span> clpr = clipper<span class="include">(</span><span class="include">)</span>
    clpr:AddPath<span class="include">(</span>outer_ring, clipper_enum.PolyType.Subject, true<span class="include">)</span>
    clpr:AddPath<span class="include">(</span>inner_ring, clipper_enum.PolyType.Clip, true<span class="include">)</span>
    <span class="local">local</span> ring_area = clpr:Execute<span class="include">(</span>clipper_enum.ClipType.Difference, clipper_enum.PolyFillType.NonZero, clipper_enum.PolyFillType.EvenOdd<span class="include">)</span>
    <span class="keyword">return</span> ring_area
<span class="keyword">end</span>

<span class="note">--</span> Darius Outer ring Q, Find hit areas
<span class="local">local</span> <span class="keyword">function</span> on_draw<span class="include">(</span><span class="include">)</span>
    <span class="local">local</span> centers = <span class="include">{</span><span class="include">}</span>
    <span class="keyword">for</span> i=<span class="number">0</span>, objManager.enemies_n-<span class="number">1</span> <span class="keyword">do</span>
        <span class="local">local</span> obj = objManager.enemies[i]
        <span class="keyword">if</span> obj and obj:isValidTarget<span class="include">(</span><span class="number">850</span><span class="include">)</span> <span class="keyword">then</span>
            table.insert<span class="include">(</span>centers, obj<span class="include">)</span>
        <span class="keyword">end</span>
    <span class="keyword">end</span>

    <span class="keyword">if</span> <span class="comment">#centers &gt;= <span class="number">2</span> <span class="keyword">then</span></span>
        <span class="local">local</span> clpr = clipper<span class="include">(</span><span class="include">)</span>
        <span class="local">local</span> first_ring = create_ring<span class="include">(</span>centers[<span class="number">1</span>], <span class="number">460</span>, <span class="number">250</span><span class="include">)</span>
        <span class="local">local</span> second_ring = create_ring<span class="include">(</span>centers[<span class="number">2</span>], <span class="number">460</span>, <span class="number">250</span><span class="include">)</span>

        clpr:AddPaths<span class="include">(</span>first_ring, clipper_enum.PolyType.Subject, true<span class="include">)</span>
        clpr:AddPaths<span class="include">(</span>second_ring, clipper_enum.PolyType.Clip, true<span class="include">)</span>

        <span class="local">local</span> solution = clpr:Execute<span class="include">(</span>clipper_enum.ClipType.Intersection, clipper_enum.PolyFillType.NonZero, clipper_enum.PolyFillType.EvenOdd<span class="include">)</span>

        <span class="keyword">for</span> i = <span class="number">0</span>, solution:ChildCount<span class="include">(</span><span class="include">)</span> - <span class="number">1</span> <span class="keyword">do</span>
            <span class="local">local</span> poly = solution:Childs<span class="include">(</span>i<span class="include">)</span>
            poly:Draw<span class="number">3</span>D<span class="include">(</span><span class="hero">player</span>.y, <span class="number">2</span>, <span class="number">0</span>xFF<span class="number">00</span>FF<span class="number">00</span><span class="include">)</span>
        <span class="keyword">end</span>
    <span class="keyword">end</span>
<span class="keyword">end</span>

<span class="cb">cb</span>.<span class="add">add</span><span class="include">(</span><span class="cb">cb</span>.<span class="cbfunc">draw</span>, on_draw<span class="include">)</span></code></pre><h1 id="libraries">Libraries</h1><h3 id="libraries-hanbot">hanbot</h3><h4>hanbot.path</h4><p>Return Value<br>
<code>string</code> returns hanbots working directory</p><h4>hanbot.luapath</h4><p>Return Value<br>
<code>string</code> returns directory of non-shard scripts</p><h4>hanbot.libpath</h4><p>Return Value<br>
<code>string</code> returns directory of community libraries</p><h4>hanbot.shardpath</h4><p>Return Value<br>
<code>string</code> returns directory of shards</p><h4>hanbot.hwid</h4><p>Return Value<br>
<code>string</code> returns current users hwid</p><h4>hanbot.user</h4><p>Return Value<br>
<code>string</code> returns current users id</p><h4>hanbot.language</h4><p>Return Value<br>
<code>number</code> returns language id</p><h3 id="libraries-cb">cb</h3><p>Enums:<br></p><ul>
<li>cb.draw_first  (cb.draw2)</li>
<li>cb.sprite      (cb.draw_sprite)</li>
<li>cb.draw</li>
<li>cb.draw_world</li>
<li>cb.draw_under_hud</li>
<li>cb.tick</li>
<li>cb.pre_tick</li>
<li>cb.spell</li>
<li>cb.keyup</li>
<li>cb.keydown</li>
<li>cb.issueorder</li>
<li>cb.issue_order</li>
<li>cb.castspell</li>
<li>cb.cast_spell</li>
<li>cb.attack_cancel</li>
<li>cb.cast_finish</li>
<li>cb.play_animation</li>
<li>cb.delete_minion</li>
<li>cb.create_minion</li>
<li>cb.delete_particle</li>
<li>cb.create_particle</li>
<li>cb.delete_missile</li>
<li>cb.create_missile</li>
<li>cb.buff_gain</li>
<li>cb.buff_lose</li>
<li>cb.path</li>
<li>cb.set_cursorpos</li>
<li>cb.error</li>
</ul><h4>drawing order:</h4><p>cb.draw_world (game pipeline) -&gt; <br>
cb.draw_mouse_overs (game pipeline) -&gt; <br>
cb.draw_under_hud (game pipeline) -&gt; <br>
cb.draw_first (hanbot pipeline) -&gt; <br>
cb.draw_sprite (hanbot pipeline) -&gt; <br>
cb.draw (hanbot pipeline)</p><ul>
<li>ALWAYS prepare your drawings in cb.tick, DONT do too much calculations in drawing callback</li>
<li>The <code>draw_world</code>/<code>draw_mouse_overs</code>/<code>draw_under_hud</code> pipelines will be combined to hanbot pipeline if <code>obs_bypass</code> is enabled in core menu.</li>
</ul><h4>cb.add(t, f)</h4><p>Parameters<br>
<code>number</code> t<br>
<code>function</code> f<br>
Return Value<br>
<code>void</code></p><pre><code class="lang-lua"><span class="local">local</span> tick_n = <span class="number">0</span>
<span class="local">local</span> <span class="keyword">function</span> on_tick<span class="include">(</span><span class="include">)</span>
    tick_n = tick_n + <span class="number">1</span>
    <span class="print">print</span><span class="include">(</span>tick_n<span class="include">)</span>
<span class="keyword">end</span>

<span class="cb">cb</span>.<span class="add">add</span><span class="include">(</span><span class="cb">cb</span>.<span class="cbfunc">tick</span>, on_tick<span class="include">)</span></code></pre><h4>cb.remove(f)</h4><p>Parameters<br>
<code>function</code> f<br>
Return Value<br>
<code>void</code></p><pre><code class="lang-lua"><span class="local">local</span> tick_n = <span class="number">0</span>
<span class="local">local</span> <span class="keyword">function</span> on_tick<span class="include">(</span><span class="include">)</span>
    tick_n = tick_n + <span class="number">1</span>
    <span class="keyword">if</span> tick_n == <span class="number">10</span> <span class="keyword">then</span>
        <span class="print">print</span><span class="include">(</span><span class="string">'removed'</span><span class="include">)</span>
        <span class="cb">cb</span>.remove<span class="include">(</span>on_tick<span class="include">)</span>
    <span class="keyword">end</span>
<span class="keyword">end</span>

<span class="cb">cb</span>.<span class="add">add</span><span class="include">(</span><span class="cb">cb</span>.<span class="cbfunc">tick</span>, on_tick<span class="include">)</span></code></pre><h3 id="libraries-chat">chat</h3><h4>chat.isOpened</h4><p>Return Value<br>
<code>boolean</code> returns true if chat is open</p><h4>chat.size</h4><p>Return Value<br>
<code>number</code> returns number in chat history</p><h4>chat.clear()</h4><p>Clear the send buffer<br>
Parameters<br>
Return Value<br>
<code>void</code><br></p><h4>chat.add(str, style)</h4><p>Parameters<br>
<code>string</code> str in UTF8<br>
<code>table</code> style <br>
Return Value<br>
<code>void</code><br></p><h4>chat.send(str)</h4><p>Parameters<br>
<code>string</code> str in UTF8<br>
Return Value<br>
<code>void</code><br></p><pre><code class="lang-lua">chat.clear<span class="include">(</span><span class="include">)</span>
chat.<span class="add">add</span><span class="include">(</span><span class="string">"hello"</span>, <span class="include">{</span>bold = false, italic = false, color = <span class="string">'ffffffff'</span><span class="include">}</span><span class="include">)</span>
chat.<span class="add">add</span><span class="include">(</span><span class="string">"world"</span>, <span class="include">{</span>bold = true, italic = true, color = <span class="string">'<span class="number">99999999</span>'</span><span class="include">}</span><span class="include">)</span>
chat.<span class="print">print</span><span class="include">(</span><span class="include">)</span></code></pre><h4>chat.print(str)</h4><p>Parameters<br>
<code>string</code> str in UTF8<br>
Return Value<br>
<code>void</code><br></p><pre><code class="lang-lua">chat.<span class="print">print</span><span class="include">(</span><span class="string">"hello world"</span><span class="include">)</span></code></pre><h4>chat.message(index)</h4><p>Parameters<br>
<code>number</code> index<br>
Return Value<br>
<code>string</code><br></p><pre><code class="lang-lua"><span class="keyword">for</span> i=<span class="number">0</span>,chat.size-<span class="number">1</span> <span class="keyword">do</span>
    <span class="print">print</span><span class="include">(</span>chat.message<span class="include">(</span>i<span class="include">)</span><span class="include">)</span>
<span class="keyword">end</span></code></pre><h3 id="libraries-console">console</h3><h4>console.set_color(c)</h4><p>Parameters<br>
<code>number</code> c<br>
Return Value<br>
<code>void</code><br></p><h4>console.printx(str, c)</h4><p>Parameters<br>
<code>string</code> str<br>
<code>number</code> c<br>
Return Value<br>
<code>void</code><br></p><h3 id="libraries-minimap">minimap</h3><h4>minimap.x</h4><p>Return Value<br>
<code>number</code> returns the screen x pos of the upper left corner of the minimap<br></p><h4>minimap.y</h4><p>Return Value<br>
<code>number</code> returns the screen y pos of the upper left corner of the minimap<br></p><h4>minimap.width</h4><p>Return Value<br>
<code>number</code> returns the screen width of the minimap<br></p><h4>minimap.height</h4><p>Return Value<br>
<code>number</code> returns the screen height of the minimap<br></p><h4>minimap.bounds</h4><p>Return Value<br>
<code>vec2</code> returns the maximum map boundaries<br></p><h4>minimap.world_to_map(v1)</h4><p>Parameters<br>
<code>vec2\vec3</code> v1<br>
Return Value<br>
<code>vec2</code> returns the screen pos of v1 on the minimap<br></p><pre><code class="lang-lua"><span class="local">local</span> v<span class="number">1</span> = <span class="hero">player</span>.pos
<span class="local">local</span> a = minimap.world_to_map<span class="include">(</span>v<span class="number">1</span><span class="include">)</span>
a:<span class="print">print</span><span class="include">(</span><span class="include">)</span></code></pre><h4>minimap.on_map(v1)</h4><p>Parameters<br>
<code>vec2</code> v1<br>
Return Value<br>
<code>boolean</code> returns true if v1 is hovering the minimap<br></p><pre><code class="lang-lua"><span class="local">local</span> v<span class="number">1</span> = game.cursorPos
<span class="local">local</span> a = minimap.on_map<span class="include">(</span>v<span class="number">1</span><span class="include">)</span>
<span class="print">print</span><span class="include">(</span>a<span class="include">)</span></code></pre><h4>minimap.on_map_xy(x, y)</h4><p>Parameters<br>
<code>number</code> x<br>
<code>number</code> y<br>
Return Value<br>
<code>boolean</code> returns true if (x, y) is hovering the minimap<br></p><pre><code class="lang-lua"><span class="local">local</span> x = game.cursorPos.x
<span class="local">local</span> y = game.cursorPos.y
<span class="local">local</span> a = minimap.on_map_xy<span class="include">(</span>x, y<span class="include">)</span>
<span class="print">print</span><span class="include">(</span>a<span class="include">)</span></code></pre><h4>minimap.draw_circle(v1, r, w, c, n)</h4><p>Parameters<br>
<code>vec3</code> v1<br>
<code>number</code> r<br>
<code>number</code> w<br>
<code>number</code> c<br>
<code>number</code> n<br>
Return Value<br>
<code>void</code><br></p><pre><code class="lang-lua"><span class="local">local</span> <span class="keyword">function</span> on_draw<span class="include">(</span><span class="include">)</span>
    <span class="local">local</span> v<span class="number">1</span> = <span class="hero">player</span>.pos
    <span class="local">local</span> radius = <span class="number">1000</span>
    <span class="local">local</span> line_width = <span class="number">1</span>
    <span class="local">local</span> color = <span class="number">0</span>xFFFFFFFF
    <span class="local">local</span> points_n = <span class="number">16</span>
    minimap.draw_circle<span class="include">(</span>v<span class="number">1</span>, radius, line_width, color, points_n<span class="include">)</span>
<span class="keyword">end</span>

<span class="cb">cb</span>.<span class="add">add</span><span class="include">(</span><span class="cb">cb</span>.<span class="cbfunc">draw</span>, on_draw<span class="include">)</span></code></pre><h3 id="libraries-ping">ping</h3><p>Enums:<br></p><ul>
<li>ping.ALERT</li>
<li>ping.DANGER</li>
<li>ping.MISSING_ENEMY</li>
<li>ping.ON_MY_WAY</li>
<li>ping.RETREAT</li>
<li>ping.ASSIST_ME</li>
<li>ping.AREA_IS_WARDED</li>
</ul><h4>ping.send(vec3 pos [, number ping_type [, obj target]])</h4><p>Parameters<br>
<code>vec3</code> pos<br>
<code>number</code> ping_type<br>
<code>game.obj</code> obj<br>
Return Value<br>
<code>void</code> <br></p><h4>ping.recv(vec3 pos [, number ping_type [, obj target]])</h4><p>Parameters<br>
<code>vec3</code> pos<br>
<code>number</code> ping_type<br>
<code>game.obj</code> obj<br>
Return Value<br>
<code>void</code> <br></p><h3 id="libraries-navmesh">navmesh</h3><h4>navmesh.getTerrainHeight(x, z)</h4><p>Parameters<br>
<code>number</code> x coordinate<br>
<code>number</code> z coordinate<br>
Return Value<br>
<code>number</code> returns terrain height at x,z<br></p><h4>navmesh.isGrass(v1)</h4><p>Parameters<br>
<code>vec2\vec3</code> v1<br>
Return Value<br>
<code>boolean</code> returns true if v1 is in grass<br></p><h4>navmesh.isWater(v1)</h4><p>Parameters<br>
<code>vec2\vec3</code> v1<br>
Return Value<br>
<code>boolean</code> returns true if v1 is in water<br></p><h4>navmesh.isWall(v1)</h4><p>Parameters<br>
<code>vec2\vec3</code> v1<br>
Return Value<br>
<code>boolean</code> returns true if v1 is a wall<br></p><h4>navmesh.isStructure(v1)</h4><p>Parameters<br>
<code>vec2\vec3</code> v1<br>
Return Value<br>
<code>boolean</code> returns true if v1 is a structure<br></p><h4>navmesh.isInFoW(v1)</h4><p>Parameters<br>
<code>vec2\vec3</code> v1<br>
Return Value<br>
<code>boolean</code> returns true if v1 is in fog of war<br></p><h4>navmesh.getNearstPassable(v1)</h4><p>deprecated, please use <code>player:getPassablePos</code> instead<br>
Parameters<br>
<code>vec2\vec3</code> v1<br>
Return Value<br>
<code>vec2</code> returns the nearst passable position (the cell start) vec2(x,z) to v1(x, z)<br>
<code>bool</code> returns the nearst passable is grass or not<br></p><pre><code class="lang-lua"><span class="local">local</span> drop_pos = navmesh.getNearstPassable<span class="include">(</span>mousePos<span class="include">)</span> <span class="note">--</span> <span class="keyword">return</span> type is vec<span class="number">2</span>
graphics.draw_circle<span class="include">(</span>vec<span class="number">3</span><span class="include">(</span>drop_pos.x + <span class="number">25</span>, <span class="number">0</span>, drop_pos.y + <span class="number">25</span><span class="include">)</span>, <span class="number">4</span>, <span class="number">2</span>, <span class="number">0</span>xFFFFFF<span class="number">00</span>, <span class="number">3</span><span class="include">)</span></code></pre><h4>navmesh.calcPos(obj, targetPos, strips)</h4><p>Parameters<br>
<code>obj</code> hero/minion <br>
<code>vec3</code> destination <br>
<code>table</code> strips (shape of obstacle area) <br>
Return Value<br>
<code>vec3[]</code> returns array of paths<br>
<code>number</code> returns array length<br></p><pre><code class="lang-lua">
<span class="local">local</span> g_strips = <span class="include">{</span><span class="include">}</span>

<span class="keyword">function</span> check_spell_area<span class="include">(</span><span class="include">)</span>
    <span class="local">local</span> collision_size = <span class="number">4</span>
    <span class="local">local</span> collision_array = vec<span class="number">3</span>.array<span class="include">(</span>collision_size<span class="include">)</span>

    <span class="note">--</span> push areas, and please note that the minimum unit <span class="keyword">for</span> collision checking is cell.
    <span class="note">--</span> You need to round up points to cell boundings <span class="keyword">in</span> real situations.
    collision_array[<span class="number">0</span>] = vec<span class="number">3</span><span class="include">(</span><span class="number">1000</span>, <span class="number">0</span>, <span class="number">1000</span><span class="include">)</span>
    collision_array[<span class="number">1</span>] = vec<span class="number">3</span><span class="include">(</span><span class="number">1000</span>, <span class="number">0</span>, <span class="number">2000</span><span class="include">)</span>
    collision_array[<span class="number">2</span>] = vec<span class="number">3</span><span class="include">(</span><span class="number">2000</span>, <span class="number">0</span>, <span class="number">2000</span><span class="include">)</span>
    collision_array[<span class="number">3</span>] = vec<span class="number">3</span><span class="include">(</span><span class="number">2000</span>, <span class="number">0</span>, <span class="number">1000</span><span class="include">)</span>

    table.insert<span class="include">(</span>g_strips, <span class="include">{</span>vec = collision_array, n = collision_size<span class="include">}</span><span class="include">)</span>
<span class="keyword">end</span>

<span class="cb">cb</span>.<span class="add">add</span><span class="include">(</span><span class="cb">cb</span>.<span class="cbfunc">draw</span>, <span class="keyword">function</span><span class="include">(</span><span class="include">)</span>

  g_strips = <span class="include">{</span><span class="include">}</span>
  check_spell_area<span class="include">(</span><span class="include">)</span>

  <span class="note">--</span> <span class="cbfunc">draw</span> a opening shape
  <span class="keyword">for</span> i = <span class="number">1</span>, <span class="comment">#g_strips <span class="keyword">do</span></span>
    <span class="local">local</span> strip = g_strips[i]
    <span class="local">local</span> vec = strip.vec
    <span class="keyword">for</span> j = <span class="number">0</span>, strip.n - <span class="number">2</span> <span class="keyword">do</span>
      graphics.draw_line<span class="include">(</span>vec[j], vec[j + <span class="number">1</span>], <span class="number">2</span>, <span class="number">0</span>xffff<span class="number">0000</span><span class="include">)</span>
    <span class="keyword">end</span>
  <span class="keyword">end</span>

  <span class="note">--</span> <span class="cbfunc">draw</span> the <span class="cbfunc">path</span> finding
  <span class="local">local</span> paths,size = navmesh.calcPos<span class="include">(</span><span class="hero">player</span>, mousePos, g_strips<span class="include">)</span>
  graphics.draw_line_strip<span class="include">(</span>paths, <span class="number">2</span>, <span class="number">0</span>xFF<span class="number">00</span>FF<span class="number">00</span>, size<span class="include">)</span>
<span class="keyword">end</span><span class="include">)</span></code></pre><h3 id="libraries-game">game</h3><h4>game.mousePos</h4><p>Return Value<br>
<code>vec3</code> returns the current position of the mouse<br></p><h4>game.mousePos2D</h4><p>Return Value<br>
<code>vec2</code> returns the current position of the mouse<br></p><h4>game.cameraPos</h4><p>Return Value<br>
<code>vec3</code> returns the current position of the camera<br></p><h4>game.cameraLocked</h4><p>Return Value<br>
<code>boolean</code> returns true if the camera is locked<br></p><h4>game.setCameraLock(bool)</h4><p>Parameters<br>
<code>boolean</code> bool<br>
Return Value<br>
<code>void</code> <br></p><h4>game.cameraY</h4><p>Return Value<br>
<code>number</code> returns the current camera zoom<br></p><h4>game.cursorPos</h4><p>Return Value<br>
<code>vec2</code> returns the current cursor position<br></p><h4>game.time</h4><p>Return Value<br>
<code>number</code> returns the current game time<br></p><h4>game.version</h4><p>Return Value<br>
<code>string</code> returns the current game version<br>
<em>You cant lock your shard to specific game version, this is a forbidden.</em><br></p><h4>game.selectedTarget</h4><p>Return Value<br>
<code>obj</code> returns the current selected game object<br></p><h4>game.hoveredTarget</h4><p>Return Value<br>
<code>obj</code> returns the current hovered game object<br></p><h4>game.mapID</h4><p>Return Value<br>
<code>number</code> returns the current map ID<br></p><h4>game.mode</h4><p>Return Value<br>
<code>string</code> returns the current game mode<br></p><h4>game.type</h4><p>Return Value<br>
<code>string</code> returns the current game type<br></p><h4>game.shopOpen</h4><p>Return Value<br>
<code>boolean</code> check if shop available<br></p><h4>game.isWindowFocused</h4><p>Return Value<br>
<code>boolean</code> returns true if LoL is focused<br></p><h4>game.getHoveredTarget(int, int)</h4><p>Parameters<br>
<code>int</code> mouse X<br>
<code>int</code> mouse Y<br>
Return Value<br>
<code>obj</code> returns the current hovered game object by position<br><br></p><h4>game.sendEmote(emoteId)</h4><p>Parameters<br>
<code>int</code> emoteId <br>
Return Value<br>
<code>void</code> <br></p><pre><code class="lang-c">EMOTE_DANCE = <span class="number">0</span>
EMOTE_TAUNT = <span class="number">1</span>
EMOTE_LAUGH = <span class="number">2</span>
EMOTE_JOKE = <span class="number">3</span>
EMOTE_TOGGLE = <span class="number">4</span></code></pre><pre><code class="lang-lua">game.sendEmote<span class="include">(</span><span class="number">0</span><span class="include">)</span> <span class="note">--</span> dance</code></pre><h4>game.sendRadialEmote(index)</h4><p>Parameters<br>
<code>int</code> index <br>
Return Value<br>
<code>void</code> <br></p><pre><code class="lang-lua">game.sendRadialEmote<span class="include">(</span><span class="number">8</span><span class="include">)</span> <span class="note">--</span> centre emote</code></pre><h4>game.displayMasteryBadge()</h4><p>Parameters<br>
Return Value<br>
<code>void</code> <br></p><h4>game.fnvhash(str)</h4><p>Parameters<br>
<code>string</code> input string<br>
Return Value<br>
<code>unsigned int</code> returns the fnvhash of input string<br></p><h4>game.spellhash(str)</h4><p>Parameters<br>
<code>string</code> input string<br>
Return Value<br>
<code>unsigned int</code> returns the spell hash of input string<br></p><h4>game.hashSDBM(str)</h4><p>Parameters<br>
<code>string</code> input string<br>
Return Value<br>
<code>unsigned int</code> returns the SDBM hash of input string<br></p><h3 id="libraries-graphics">graphics</h3><h4>graphics.width</h4><p>Return Value<br>
<code>number</code> returns screen width<br></p><h4>graphics.height</h4><p>Return Value<br>
<code>number</code> returns screen height<br></p><h4>graphics.res</h4><p>Return Value<br>
<code>vec2</code> returns screen resolution<br></p><h4>graphics.draw_text_2D(str, size, x, y, color)</h4><p>Parameters<br>
<code>string</code> str<br>
<code>number</code> size<br>
<code>number</code> x<br>
<code>number</code> y<br>
<code>number</code> color<br>
Return Value<br>
<code>void</code><br></p><pre><code class="lang-lua"><span class="local">local</span> <span class="keyword">function</span> on_draw<span class="include">(</span><span class="include">)</span>
    graphics.draw_text_<span class="number">2</span>D<span class="include">(</span><span class="string">'foo'</span>, <span class="number">14</span>, game.cursorPos.x, game.cursorPos.y, <span class="number">0</span>xFFFFFFFF<span class="include">)</span>
<span class="keyword">end</span>

<span class="cb">cb</span>.<span class="add">add</span><span class="include">(</span><span class="cb">cb</span>.<span class="cbfunc">draw</span>, on_draw<span class="include">)</span></code></pre><h4>graphics.draw_outlined_text_2D(str, size, x, y, color, outline_color)</h4><p>Parameters<br>
<code>string</code> str<br>
<code>number</code> size<br>
<code>number</code> x<br>
<code>number</code> y<br>
<code>number</code> color<br>
<code>number</code> outline_color<br>
Return Value<br>
<code>void</code><br></p><h4>graphics.text_area(str, size, n)</h4><p>Parameters<br>
<code>string</code> str<br>
<code>number</code> size<br>
<code>number</code> n<br>
Return Value<br>
<code>number</code> returns str width<br>
<code>number</code> returns str height<br></p><pre><code class="lang-lua"><span class="local">local</span> str = <span class="string">'foo'</span>
<span class="local">local</span> font_size = <span class="number">14</span>
<span class="local">local</span> x, y = graphics.text_area<span class="include">(</span>str, font_size<span class="include">)</span>
<span class="print">print</span><span class="include">(</span>x, y<span class="include">)</span></code></pre><h4>graphics.get_font()</h4><p>Return Value<br>
<code>string</code> returns current font name<br></p><h4>graphics.argb(a, r, g, b)</h4><p>Parameters<br>
<code>number</code> a<br>
<code>number</code> r<br>
<code>number</code> g<br>
<code>number</code> b<br>
Return Value<br>
<code>number</code> returns color<br></p><pre><code class="lang-lua"><span class="local">local</span> color = graphics.argb<span class="include">(</span><span class="number">255</span>, <span class="number">25</span>, <span class="number">185</span>, <span class="number">90</span><span class="include">)</span>
<span class="local">local</span> <span class="keyword">function</span> on_draw<span class="include">(</span><span class="include">)</span>
    graphics.draw_text_<span class="number">2</span>D<span class="include">(</span><span class="string">'foo'</span>, <span class="number">14</span>, game.cursorPos.x, game.cursorPos.y, color<span class="include">)</span>
<span class="keyword">end</span>

<span class="cb">cb</span>.<span class="add">add</span><span class="include">(</span><span class="cb">cb</span>.<span class="cbfunc">draw</span>, on_draw<span class="include">)</span></code></pre><h4>graphics.world_to_screen(v1)</h4><p>Parameters<br>
<code>vec3</code> v1<br>
Return Value<br>
<code>vec2</code> returns screen position from world position<br></p><pre><code class="lang-lua"><span class="local">local</span> <span class="keyword">function</span> on_draw<span class="include">(</span><span class="include">)</span>
    <span class="local">local</span> v = graphics.world_to_screen<span class="include">(</span><span class="hero">player</span>.pos<span class="include">)</span>
    graphics.draw_text_<span class="number">2</span>D<span class="include">(</span><span class="string">'foo'</span>, <span class="number">14</span>, v.x, v.y, <span class="number">0</span>xFFFFFFFF<span class="include">)</span>
<span class="keyword">end</span>

<span class="cb">cb</span>.<span class="add">add</span><span class="include">(</span><span class="cb">cb</span>.<span class="cbfunc">draw</span>, on_draw<span class="include">)</span></code></pre><h4>graphics.world_to_screen_xyz(x, y, z)</h4><p>Parameters<br>
<code>number</code> x<br>
<code>number</code> y<br>
<code>number</code> z<br>
Return Value<br>
<code>vec2</code> returns screen position from world position<br></p><pre><code class="lang-lua"><span class="local">local</span> <span class="keyword">function</span> on_draw<span class="include">(</span><span class="include">)</span>
    <span class="local">local</span> v = graphics.world_to_screen_xyz<span class="include">(</span><span class="hero">player</span>.x, <span class="hero">player</span>.y, <span class="hero">player</span>.z<span class="include">)</span>
    graphics.draw_text_<span class="number">2</span>D<span class="include">(</span><span class="string">'foo'</span>, <span class="number">14</span>, v.x, v.y, <span class="number">0</span>xFFFFFFFF<span class="include">)</span>
<span class="keyword">end</span>

<span class="cb">cb</span>.<span class="add">add</span><span class="include">(</span><span class="cb">cb</span>.<span class="cbfunc">draw</span>, on_draw<span class="include">)</span></code></pre><h4>graphics.draw_line_2D(x1, y1, x2, y2, width, color)</h4><p>Parameters<br>
<code>number</code> x1<br>
<code>number</code> y1<br>
<code>number</code> x2<br>
<code>number</code> y2<br>
<code>number</code> width<br>
<code>number</code> color<br>
Return Value<br>
<code>void</code><br></p><pre><code class="lang-lua"><span class="local">local</span> <span class="keyword">function</span> on_draw<span class="include">(</span><span class="include">)</span>
    graphics.draw_line_<span class="number">2</span>D<span class="include">(</span><span class="number">0</span>, <span class="number">0</span>, game.cursorPos.x, game.cursorPos.y, <span class="number">2</span>, <span class="number">0</span>xFFFFFFFF<span class="include">)</span>
<span class="keyword">end</span>

<span class="cb">cb</span>.<span class="add">add</span><span class="include">(</span><span class="cb">cb</span>.<span class="cbfunc">draw</span>, on_draw<span class="include">)</span></code></pre><h4>graphics.draw_line(v1, v2, width, color)</h4><p>Parameters<br>
<code>vec3</code> v1<br>
<code>vec3</code> v2<br>
<code>number</code> width<br>
<code>number</code> color<br>
Return Value<br>
<code>void</code><br></p><pre><code class="lang-lua"><span class="local">local</span> <span class="keyword">function</span> on_draw<span class="include">(</span><span class="include">)</span>
    graphics.draw_line<span class="include">(</span>mousePos, <span class="hero">player</span>.pos, <span class="number">2</span>, <span class="number">0</span>xFFFFFFFF<span class="include">)</span>
<span class="keyword">end</span>

<span class="cb">cb</span>.<span class="add">add</span><span class="include">(</span><span class="cb">cb</span>.<span class="cbfunc">draw</span>, on_draw<span class="include">)</span></code></pre><h4>graphics.draw_triangle_2D(p1, p2, p3, width, color, is_filled, rounding)</h4><p>Parameters<br>
<code>vec2</code> p1<br>
<code>vec2</code> p2<br>
<code>vec2</code> p3<br>
<code>number</code> width<br>
<code>number</code> color<br>
<code>boolean</code> is_filled, default: false<br>
<code>number</code> rounding, default: 0<br>
Return Value<br>
<code>void</code><br></p><pre><code class="lang-lua"><span class="local">local</span> <span class="keyword">function</span> on_draw<span class="include">(</span><span class="include">)</span>
    graphics.draw_triangle_<span class="number">2</span>D<span class="include">(</span>vec<span class="number">2</span><span class="include">(</span><span class="number">10</span>, <span class="number">10</span><span class="include">)</span>,vec<span class="number">2</span><span class="include">(</span><span class="number">10</span>, <span class="number">50</span><span class="include">)</span>,vec<span class="number">2</span><span class="include">(</span><span class="number">50</span>, <span class="number">10</span><span class="include">)</span>, <span class="number">2</span>, <span class="number">0</span>xFFFFFFFF, true<span class="include">)</span>
<span class="keyword">end</span>

<span class="cb">cb</span>.<span class="add">add</span><span class="include">(</span><span class="cb">cb</span>.<span class="cbfunc">draw</span>, on_draw<span class="include">)</span></code></pre><h4>graphics.draw_rectangle_2D(x, y, dx, dy, width, color, is_filled)</h4><p>Parameters<br>
<code>number</code> x<br>
<code>number</code> y<br>
<code>number</code> dx<br>
<code>number</code> dy<br>
<code>number</code> width<br>
<code>number</code> color<br>
<code>boolean</code> is_filled: default: false<br>
<code>number</code> rounding: default: 0<br>
Return Value<br>
<code>void</code><br></p><pre><code class="lang-lua"><span class="local">local</span> <span class="keyword">function</span> on_draw<span class="include">(</span><span class="include">)</span>
    graphics.draw_rectangle_<span class="number">2</span>D<span class="include">(</span>game.cursorPos.x, game.cursorPos.y, <span class="number">90</span>, <span class="number">20</span>, <span class="number">2</span>, <span class="number">0</span>xFFFFFFFF<span class="include">)</span>
<span class="keyword">end</span>

<span class="cb">cb</span>.<span class="add">add</span><span class="include">(</span><span class="cb">cb</span>.<span class="cbfunc">draw</span>, on_draw<span class="include">)</span></code></pre><h4>graphics.draw_line_strip(pts, width, color, pts_n)</h4><p>Parameters<br>
<code>vec3[]</code> pts<br>
<code>number</code> width<br>
<code>number</code> color<br>
<code>number</code> pts_n<br>
Return Value<br>
<code>void</code><br></p><pre><code class="lang-lua"><span class="local">local</span> <span class="keyword">function</span> on_draw<span class="include">(</span><span class="include">)</span>
    <span class="keyword">if</span> <span class="hero">player</span>.<span class="cbfunc">path</span>.active <span class="keyword">then</span>
        graphics.draw_line_strip<span class="include">(</span><span class="hero">player</span>.<span class="cbfunc">path</span>.point, <span class="number">2</span>, <span class="number">0</span>xFFFFFFFF, <span class="hero">player</span>.<span class="cbfunc">path</span>.count+<span class="number">1</span><span class="include">)</span>
    <span class="keyword">end</span>
<span class="keyword">end</span>

<span class="cb">cb</span>.<span class="add">add</span><span class="include">(</span><span class="cb">cb</span>.<span class="cbfunc">draw</span>, on_draw<span class="include">)</span></code></pre><h4>graphics.draw_line_strip_2D(pts, width, color, pts_n)</h4><p>Parameters<br>
<code>vec2[]</code> pts<br>
<code>number</code> width<br>
<code>number</code> color<br>
<code>number</code> pts_n<br>
Return Value<br>
<code>void</code><br></p><pre><code class="lang-lua"><span class="local">local</span> v = vec<span class="number">2</span>.array<span class="include">(</span><span class="number">4</span><span class="include">)</span>
v[<span class="number">0</span>].x = <span class="number">200</span>
v[<span class="number">0</span>].y = <span class="number">200</span>
v[<span class="number">1</span>].x = <span class="number">400</span>
v[<span class="number">1</span>].y = <span class="number">200</span>
v[<span class="number">2</span>].x = <span class="number">400</span>
v[<span class="number">2</span>].y = <span class="number">400</span>
v[<span class="number">3</span>].x = <span class="number">200</span>
v[<span class="number">3</span>].y = <span class="number">400</span>
<span class="local">local</span> <span class="keyword">function</span> on_draw<span class="include">(</span><span class="include">)</span>
    graphics.draw_line_strip_<span class="number">2</span>D<span class="include">(</span>v, <span class="number">2</span>, <span class="number">0</span>xFFFFFFFF, <span class="number">4</span><span class="include">)</span>
<span class="keyword">end</span>

<span class="cb">cb</span>.<span class="add">add</span><span class="include">(</span><span class="cb">cb</span>.<span class="cbfunc">draw</span>, on_draw<span class="include">)</span></code></pre><h4>graphics.draw_circle(v1, radius, width, color, pts_n)</h4><p>Parameters<br>
<code>vec3</code> v1<br>
<code>number</code> radius<br>
<code>number</code> width<br>
<code>number</code> color<br>
<code>number</code> pts_n<br>
Return Value<br>
<code>void</code><br></p><pre><code class="lang-lua"><span class="local">local</span> <span class="keyword">function</span> on_draw<span class="include">(</span><span class="include">)</span>
    graphics.draw_circle<span class="include">(</span><span class="hero">player</span>.pos, <span class="hero">player</span>.attackRange, <span class="number">2</span>, <span class="number">0</span>xFFFFFFFF, <span class="number">32</span><span class="include">)</span>
<span class="keyword">end</span>

<span class="cb">cb</span>.<span class="add">add</span><span class="include">(</span><span class="cb">cb</span>.<span class="cbfunc">draw</span>, on_draw<span class="include">)</span></code></pre><h4>graphics.draw_circle_2D(x, y, radius, width, color, pts_n)</h4><p>Parameters<br>
<code>number</code> x<br>
<code>number</code> y<br>
<code>number</code> radius<br>
<code>number</code> width<br>
<code>number</code> color<br>
<code>number</code> pts_n<br>
Return Value<br>
<code>void</code><br></p><pre><code class="lang-lua"><span class="local">local</span> <span class="keyword">function</span> on_draw<span class="include">(</span><span class="include">)</span>
    graphics.draw_circle_<span class="number">2</span>D<span class="include">(</span>game.cursorPos.x, game.cursorPos.y, <span class="number">120</span>, <span class="number">2</span>, <span class="number">0</span>xFFFFFFFF, <span class="number">32</span><span class="include">)</span>
<span class="keyword">end</span>

<span class="cb">cb</span>.<span class="add">add</span><span class="include">(</span><span class="cb">cb</span>.<span class="cbfunc">draw</span>, on_draw<span class="include">)</span></code></pre><h4>graphics.draw_circle_xyz(x, y, z, radius, width, color, pts_n)</h4><p>Parameters<br>
<code>number</code> x<br>
<code>number</code> y<br>
<code>number</code> z<br>
<code>number</code> radius<br>
<code>number</code> width<br>
<code>number</code> color<br>
<code>number</code> pts_n<br>
Return Value<br>
<code>void</code><br></p><pre><code class="lang-lua"><span class="local">local</span> <span class="keyword">function</span> on_draw<span class="include">(</span><span class="include">)</span>
    graphics.draw_circle_xyz<span class="include">(</span><span class="hero">player</span>.x, <span class="hero">player</span>.y, <span class="hero">player</span>.z, <span class="hero">player</span>.attackRange, <span class="number">2</span>, <span class="number">0</span>xFFFFFFFF, <span class="number">32</span><span class="include">)</span>
<span class="keyword">end</span>

<span class="cb">cb</span>.<span class="add">add</span><span class="include">(</span><span class="cb">cb</span>.<span class="cbfunc">draw</span>, on_draw<span class="include">)</span></code></pre><h4>graphics.draw_arc(v1, radius, width, color, pts_n, r1, r2)</h4><p>Parameters<br>
<code>vec3</code> v1<br>
<code>number</code> radius<br>
<code>number</code> width<br>
<code>number</code> color<br>
<code>number</code> pts_n<br>
<code>number</code> r1<br>
<code>number</code> r2<br>
Return Value<br>
<code>void</code><br></p><pre><code class="lang-lua"><span class="local">local</span> <span class="keyword">function</span> on_draw<span class="include">(</span><span class="include">)</span>
    graphics.draw_arc<span class="include">(</span><span class="hero">player</span>.pos, <span class="hero">player</span>.attackRange, <span class="number">2</span>, <span class="number">0</span>xFFFFFFFF, <span class="number">32</span>, <span class="number">0</span>, math.pi/<span class="number">2</span><span class="include">)</span>
<span class="keyword">end</span>

<span class="cb">cb</span>.<span class="add">add</span><span class="include">(</span><span class="cb">cb</span>.<span class="cbfunc">draw</span>, on_draw<span class="include">)</span></code></pre><h4>graphics.draw_arc_2D(x, y, radius, width, color, pts_n, r1, r2)</h4><p>Parameters<br>
<code>number</code> x<br>
<code>number</code> y<br>
<code>number</code> radius<br>
<code>number</code> width<br>
<code>number</code> color<br>
<code>number</code> pts_n<br>
<code>number</code> r1<br>
<code>number</code> r2<br>
Return Value<br>
<code>void</code><br></p><pre><code class="lang-lua"><span class="local">local</span> <span class="keyword">function</span> on_draw<span class="include">(</span><span class="include">)</span>
    graphics.draw_arc<span class="include">(</span><span class="hero">player</span>.pos, <span class="hero">player</span>.attackRange, <span class="number">2</span>, <span class="number">0</span>xFFFFFFFF, <span class="number">32</span>, <span class="number">0</span>, math.pi/<span class="number">2</span><span class="include">)</span>
<span class="keyword">end</span>

<span class="cb">cb</span>.<span class="add">add</span><span class="include">(</span><span class="cb">cb</span>.<span class="cbfunc">draw</span>, on_draw<span class="include">)</span></code></pre><h4>graphics.create_effect(type)</h4><p>Create a shadereffect instance by type<br>
Parameters<br>
<code>number</code> type<br>
Return Value<br>
<code>shadereffect.obj</code><br></p><pre><code class="lang-lua"><span class="note">--</span> create once, show always, with best performance
<span class="local">local</span> circle_unchanged = graphics.create_effect<span class="include">(</span>graphics.CIRCLE_RAINBOW<span class="include">)</span>
circle_unchanged:update_circle<span class="include">(</span><span class="hero">player</span>.pos, <span class="number">1200</span>, <span class="number">2</span>, <span class="number">0</span>xFFFF<span class="number">0000</span><span class="include">)</span>
circle_unchanged:attach<span class="include">(</span><span class="hero">player</span><span class="include">)</span>
circle_unchanged:show<span class="include">(</span><span class="include">)</span></code></pre><pre><code class="lang-lua"><span class="note">--</span> update effect <span class="keyword">in</span> on_draw
<span class="local">local</span> circle_aa = graphics.create_effect<span class="include">(</span>graphics.CIRCLE_RAINBOW<span class="include">)</span>
<span class="local">local</span> <span class="keyword">function</span> on_draw<span class="include">(</span><span class="include">)</span>
    circle_aa:update_circle<span class="include">(</span><span class="hero">player</span>.pos, <span class="hero">player</span>.attackRange, <span class="number">2</span>, <span class="number">0</span>xff<span class="number">123456</span><span class="include">)</span>
<span class="keyword">end</span>
<span class="cb">cb</span>.<span class="add">add</span><span class="include">(</span><span class="cb">cb</span>.<span class="cbfunc">draw</span>, on_draw<span class="include">)</span></code></pre><h4>graphics.CIRCLE_GLOW</h4><p>Return Value<br>
<code>number</code> simple glow circle<br></p><h4>graphics.CIRCLE_GLOW_RAINBOW</h4><p>Return Value<br>
<code>number</code> glow circle with rainbow<br></p><h4>graphics.CIRCLE_GLOW_LIGHT</h4><p>Return Value<br>
<code>number</code> another glow circle<br></p><h4>graphics.CIRCLE_GLOW_BOLD</h4><p>Return Value<br>
<code>number</code> another glow circle 2<br></p><h4>graphics.CIRCLE_FIRE</h4><p>Return Value<br>
<code>number</code> fire circle<br></p><h4>graphics.CIRCLE_RAINBOW</h4><p>Return Value<br>
<code>number</code> colorful rainbow circle<br></p><h4>graphics.CIRCLE_RAINBOW_BOLD</h4><p>Return Value<br>
<code>number</code> colorful another rainbow circle<br></p><h4>graphics.CIRCLE_FILL</h4><p>Return Value<br>
<code>number</code> filled circle<br></p><h4>graphics.sprite(name)</h4><p>Parameters<br>
<code>string</code> name<br>
<code>vec2</code> the screen position<br>
<code>number</code> scale<br>
<code>number</code> color<br>
Return Value<br>
<code>texture.obj</code><br></p><pre><code class="lang-lua"><span class="local">local</span> myIcon = graphics.<span class="cbfunc">sprite</span><span class="include">(</span><span class="string">'XXX/menu_icon.png'</span><span class="include">)</span>
myMenu:set<span class="include">(</span><span class="string">'icon'</span>, myIcon<span class="include">)</span></code></pre><h4>graphics.game_sprite(name)</h4><p>Parameters<br>
<code>string</code> name<br>
<code>vec2</code> the screen position<br>
<code>number</code> scale<br>
<code>number</code> color<br>
Return Value<br>
<code>texture.obj</code><br></p><pre><code class="lang-lua"><span class="note">--</span> example: https:<span class="comment">//raw.communitydragon.org/<span class="number"><span class="number">14</span>.<span class="number">1</span></span>/game/assets/characters/sru_blue/hud/bluesentinel_circle.png</span>
<span class="local">local</span> icon = graphics.game_sprite<span class="include">(</span><span class="string">'ASSETS/Characters/SRU_Blue/HUD/BlueSentinel_Circle.dds'</span><span class="include">)</span>
<span class="local">local</span> icon_ashe = graphics.game_sprite<span class="include">(</span><span class="string">'ASSETS/Characters/Ashe/HUD/Ashe_Circle.dds'</span><span class="include">)</span></code></pre><h4>graphics.draw_sprite(name, v1, scale, color)</h4><p>Parameters<br>
<code>string|texture.obj</code> name<br>
<code>vec2</code> the screen position<br>
<code>number</code> scale<br>
<code>number</code> color<br>
Return Value<br>
<code>void</code><br></p><pre><code class="lang-lua"><span class="local">local</span> <span class="keyword">function</span> on_draw_sprite<span class="include">(</span><span class="include">)</span>
    <span class="note">--</span><span class="cbfunc">sprite</span> files must be placed <span class="keyword">in</span> your shard directory
    graphics.draw_sprite<span class="include">(</span><span class="string">"sprite_name.png"</span>, vec<span class="number">2</span><span class="include">(</span>p.x, p.y<span class="include">)</span>, <span class="number"><span class="number">1</span>.<span class="number">5</span></span>, <span class="number">0</span>x<span class="number">66</span>FFFFFF<span class="include">)</span>
<span class="keyword">end</span>

<span class="cb">cb</span>.<span class="add">add</span><span class="include">(</span><span class="cb">cb</span>.<span class="cbfunc">sprite</span>, on_draw_sprite<span class="include">)</span></code></pre><h4>graphics.spawn_fake_click(color, v1)</h4><p>Parameters<br>
<code>string</code> color: “red” or “green”<br>
<code>vec3</code> v1: world_pos<br>
Return Value<br>
<code>void</code><br></p><pre><code class="lang-lua"><span class="local">local</span> <span class="keyword">function</span> on_key_down<span class="include">(</span>k<span class="include">)</span>
    <span class="keyword">if</span> k==<span class="number">49</span> <span class="keyword">then</span> <span class="note">--</span><span class="number">1</span>
        graphics.spawn_fake_click<span class="include">(</span><span class="string">"green"</span>, mousePos<span class="include">)</span>
    <span class="keyword">end</span>
<span class="keyword">end</span>

<span class="cb">cb</span>.<span class="add">add</span><span class="include">(</span><span class="cb">cb</span>.<span class="cbfunc">keydown</span>, on_key_down<span class="include">)</span></code></pre><h4>graphics.set_draw(enabled)</h4><p>Parameters<br>
<code>boolean</code> enabled<br>
Return Value<br>
<code>void</code><br></p><h4>graphics.get_draw()</h4><p>Parameters<br>
Return Value<br>
<code>boolean</code> enabled<br></p><h4>graphics.set_draw_menu(enabled)</h4><p>Parameters<br>
<code>boolean</code> enabled<br>
Return Value<br>
<code>void</code><br></p><h3 id="libraries-shadereffect">shadereffect</h3><h4>shadereffect.construct(effect_description, is_3D)</h4><p>Parameters<br>
<code>string</code> effect_description<br>
<code>boolean</code> is_3D<br>
Return Value<br>
<code>shadereffect</code> shadereffect<br></p><h4>shadereffect.begin(shadereffect, height, is_3D)</h4><p>Parameters<br>
<code>shadereffect</code> shadereffect<br>
<code>number</code> hieght<br>
<code>boolean</code> is_3D<br>
Return Value<br>
<code>void</code><br></p><h4>shadereffect.set_float(shadereffect, varname, var)</h4><p>Parameters<br>
<code>shadereffect</code> shadereffect<br>
<code>string</code> varname<br>
<code>number</code> var<br>
Return Value<br>
<code>void</code><br></p><h4>shadereffect.set_vec2(shadereffect, varname, var)</h4><p>Parameters<br>
<code>shadereffect</code> shadereffect<br>
<code>string</code> varname<br>
<code>vec2</code> var<br>
Return Value<br>
<code>void</code><br></p><h4>shadereffect.set_vec3(shadereffect, varname, var)</h4><p>Parameters<br>
<code>shadereffect</code> shadereffect<br>
<code>string</code> varname<br>
<code>vec3</code> var<br>
Return Value<br>
<code>void</code><br></p><h4>shadereffect.set_vec4(shadereffect, varname, var)</h4><p>Parameters<br>
<code>shadereffect</code> shadereffect<br>
<code>string</code> varname<br>
<code>vec4</code> var<br>
Return Value<br>
<code>void</code><br></p><h4>shadereffect.set_float_array(shadereffect, varname, var, size)</h4><p>Parameters<br>
<code>shadereffect</code> shadereffect<br>
<code>string</code> varname<br>
<code>array</code> var<br>
<code>number</code> size<br>
Return Value<br>
<code>void</code><br></p><h4>shadereffect.set_color(shadereffect, varname, color)</h4><p>Parameters<br>
<code>shadereffect</code> shadereffect<br>
<code>string</code> varname<br>
<code>color</code> var<br>
Return Value<br>
<code>void</code><br></p><h3 id="libraries-objmanager">objManager</h3><h4>objManager.player</h4><p>Return Value<br>
<code>hero.obj</code> returns player object<br></p><pre><code class="lang-lua"><span class="print">print</span><span class="include">(</span><span class="hero">player</span>.charName<span class="include">)</span></code></pre><h4>objManager.maxObjects</h4><p>Return Value<br>
<code>number</code> returns max object count<br></p><h4>objManager.get(i)</h4><p>Return Value<br>
<code>obj</code> returns game object<br></p><pre><code class="lang-lua"><span class="keyword">for</span> i=<span class="number">0</span>, objManager.maxObjects-<span class="number">1</span> <span class="keyword">do</span>
    <span class="local">local</span> obj = objManager.get<span class="include">(</span>i<span class="include">)</span>
    <span class="keyword">if</span> obj and obj.type==TYPE_HERO <span class="keyword">then</span>
        <span class="print">print</span><span class="include">(</span>obj.charName<span class="include">)</span>
    <span class="keyword">end</span>
<span class="keyword">end</span></code></pre><h4>objManager.enemies_n</h4><p>Return Value<br>
<code>number</code> returns enemy hero count<br></p><h4>objManager.enemies</h4><p>Return Value<br>
<code>hero.obj[]</code> returns array of enemy heroes<br></p><pre><code class="lang-lua"><span class="keyword">for</span> i=<span class="number">0</span>, objManager.enemies_n-<span class="number">1</span> <span class="keyword">do</span>
    <span class="local">local</span> obj = objManager.enemies[i]
    <span class="print">print</span><span class="include">(</span>obj.charName<span class="include">)</span>
<span class="keyword">end</span></code></pre><h4>objManager.allies_n</h4><p>Return Value<br>
<code>number</code> returns ally hero count<br></p><h4>objManager.allies</h4><p>Return Value<br>
<code>hero.obj[]</code> returns array of ally heroes<br></p><pre><code class="lang-lua"><span class="keyword">for</span> i=<span class="number">0</span>, objManager.allies_n-<span class="number">1</span> <span class="keyword">do</span>
    <span class="local">local</span> obj = objManager.allies[i]
    <span class="print">print</span><span class="include">(</span>obj.charName<span class="include">)</span>
<span class="keyword">end</span></code></pre><h4>objManager.minions.size[team]</h4><p>Parameters<br>
<code>team</code> can be any of these: <code>TEAM_ALLY</code>/<code>TEAM_ENEMY</code>/<code>TEAM_NEUTRAL</code> / “plants” / “others” / “farm” / “farm” / “lane_ally” / “lane_enemy” / “pets_ally” / “pets_enemy” / “barrels”<br>
Return Value<br>
<code>number</code> returns minion count of respective team<br></p><pre><code class="lang-lua"><span class="local">local</span> enemyMinionCount = objManager.minions.size[TEAM_ENEMY]</code></pre><h4>objManager.minions[team][i]</h4><p>Parameters<br>
<code>team</code> can be any of these: <code>TEAM_ALLY</code>/<code>TEAM_ENEMY</code>/<code>TEAM_NEUTRAL</code> / “plants” / “others” / “farm” / “lane_ally” / “lane_enemy” / “pets_ally” / “pets_enemy” / “barrels”<br>
Return Value<br>
<code>minion.obj</code> returns minion object<br></p><pre><code class="lang-lua">
<span class="local">local</span> enemy_minion_size = objManager.minions.size[TEAM_ENEMY]
<span class="local">local</span> enemy_minion_arr = objManager.minions[TEAM_ENEMY]
<span class="keyword">for</span> i=<span class="number">0</span>, enemy_minion_size-<span class="number">1</span> <span class="keyword">do</span>
    <span class="local">local</span> obj = enemy_minion_arr[i]
    <span class="print">print</span><span class="include">(</span>obj.charName<span class="include">)</span>
<span class="keyword">end</span>

<span class="note">--</span> <span class="cbfunc">spell</span> farm minions
<span class="local">local</span> farm_minion_size = objManager.minions.size[<span class="string">'farm'</span>]
<span class="local">local</span> farm_minion_size = objManager.minions.size[<span class="string">'farm'</span>]
<span class="local">local</span> farm_minion_arr = objManager.minions[<span class="string">'farm'</span>]
<span class="keyword">for</span> i=<span class="number">0</span>, farm_minion_size-<span class="number">1</span> <span class="keyword">do</span>
    <span class="local">local</span> obj = farm_minion_arr[i]
    <span class="print">print</span><span class="include">(</span>obj.charName<span class="include">)</span>
<span class="keyword">end</span></code></pre><h4>objManager.turrets.size[team]</h4><p>Return Value<br>
<code>number</code> returns turret count of respective team<br></p><h4>objManager.turrets[team][i]</h4><p>Return Value<br>
<code>turret.obj</code> returns turret object<br></p><pre><code class="lang-lua"><span class="keyword">for</span> i=<span class="number">0</span>, objManager.turrets.size[TEAM_ALLY]-<span class="number">1</span> <span class="keyword">do</span>
    <span class="local">local</span> obj = objManager.turrets[TEAM_ALLY][i]
    <span class="print">print</span><span class="include">(</span>obj.charName<span class="include">)</span>
<span class="keyword">end</span></code></pre><h4>objManager.inhibs.size[team]</h4><p>Return Value<br>
<code>number</code> returns inhib count of respective team<br></p><h4>objManager.inhibs[team][i]</h4><p>Return Value<br>
<code>inhib.obj</code> returns inhib object<br></p><pre><code class="lang-lua"><span class="keyword">for</span> i=<span class="number">0</span>, objManager.inhibs.size[TEAM_ALLY]-<span class="number">1</span> <span class="keyword">do</span>
    <span class="local">local</span> obj = objManager.inhibs[TEAM_ALLY][i]
    <span class="print">print</span><span class="include">(</span>obj.health<span class="include">)</span>
<span class="keyword">end</span></code></pre><h4>objManager.nexus[team]</h4><p>Return Value<br>
<code>nexus.obj</code> returns nexus object<br></p><pre><code class="lang-lua"><span class="print">print</span><span class="include">(</span>objManager.nexus[TEAM_ENEMY].health<span class="include">)</span></code></pre><h4>objManager.allObjects[i]</h4><p>Return Value<br>
<code>base.obj</code> returns GameObject<br></p><h4>objManager.allAttackableUnits[i]</h4><p>Return Value<br>
<code>base.obj</code> returns AttackableUnit<br></p><h4>objManager.allHeros[i]</h4><p>Return Value<br>
<code>hero.obj</code> returns hero object<br></p><pre><code class="lang-lua"><span class="keyword">for</span> i=<span class="number">0</span>, objManager.allHeros.size-<span class="number">1</span> <span class="keyword">do</span>
    <span class="local">local</span> obj = objManager.allHeros[i]
    <span class="print">print</span><span class="include">(</span>obj.handle<span class="include">)</span>
<span class="keyword">end</span></code></pre><h4>objManager.allMinions[i]</h4><p>Return Value<br>
<code>minion.obj</code> returns minion object<br></p><h4>objManager.minionsAlly[i]</h4><p>Return Value<br>
<code>minion.obj</code> returns minion object<br></p><h4>objManager.minionsEnemy[i]</h4><p>Return Value<br>
<code>minion.obj</code> returns minion object<br></p><h4>objManager.minionsNeutral[i]</h4><p>Return Value<br>
<code>minion.obj</code> returns minion object<br></p><h4>objManager.minionsOther[i]</h4><p>Return Value<br>
<code>minion.obj</code> returns minion object<br></p><h4>objManager.petsAlly[i]</h4><p>Return Value<br>
<code>minion.obj</code> returns minion object<br></p><h4>objManager.petsEnemy[i]</h4><p>Return Value<br>
<code>minion.obj</code> returns minion object<br></p><h4>objManager.barrels[i]</h4><p>Return Value<br>
<code>minion.obj</code> returns minion object<br></p><h4>objManager.missiles[i]</h4><p>Return Value<br>
<code>missile.obj</code> returns missile object<br></p><h4>objManager.particles[i]</h4><p>Return Value<br>
<code>particle.obj</code> returns particle object<br></p><h4>objManager.wardsAlly[i]</h4><p>Return Value<br>
<code>minion.obj</code> returns ally ward object<br></p><h4>objManager.loop(f)</h4><p>Parameters<br>
<code>function</code> f<br>
Return Value<br>
<code>void</code><br></p><pre><code class="lang-lua"><span class="local">local</span> <span class="keyword">function</span> foo<span class="include">(</span>obj<span class="include">)</span>
    <span class="keyword">if</span> obj.type==TYPE_HERO <span class="keyword">then</span>
        <span class="print">print</span><span class="include">(</span>obj.charName<span class="include">)</span>
    <span class="keyword">end</span>
<span class="keyword">end</span>

objManager.loop<span class="include">(</span>foo<span class="include">)</span></code></pre><h3 id="libraries-core">core</h3><h4>core.block_input()</h4><p>Only works in <code>cb.issueorder</code> and <code>cb.castspell</code>, will block current operation.</p><p>Return Value<br>
<code>void</code><br></p><pre><code class="lang-lua"><span class="local">local</span> <span class="keyword">function</span> on_issue_order<span class="include">(</span>order, pos, target_ptr<span class="include">)</span>
    <span class="keyword">if</span> order==<span class="number">3</span> <span class="keyword">then</span>
        <span class="note">--</span>blocks this attack
        <span class="note">--</span>this only works <span class="keyword">for</span> orders issued by hanbot
        core.block_input<span class="include">(</span><span class="include">)</span>
    <span class="keyword">end</span>
<span class="keyword">end</span>

<span class="cb">cb</span>.<span class="add">add</span><span class="include">(</span><span class="cb">cb</span>.<span class="cbfunc">issueorder</span>, on_issue_order<span class="include">)</span></code></pre><h4>core.reload()</h4><p>Return Value<br>
<code>void</code><br></p><h3 id="libraries-sound">sound</h3><h4>sound.play(name)</h4><p>Parameters<br>
<code>string</code> name<br></p><pre><code class="lang-lua"><span class="note">--</span> while resource is shared between all plugins, it is better to have a unique name <span class="include">(</span><span class="cbfunc">path</span><span class="include">)</span>
sound.play<span class="include">(</span><span class="string">'demo_aio_resources/load.wav'</span><span class="include">)</span></code></pre><h4>sound.play_from_file(file_path)</h4><p>Parameters<br>
<code>string</code> file_path<br></p><h4>sound.disable(is_disabled)</h4><p>Parameters<br>
<code>boolean</code> is_disabled<br></p><h3 id="libraries-shop">shop</h3><h4>shop.canShop</h4><p>Return Value<br>
<code>bool</code> <br></p><h4>shop.isOpened</h4><p>Return Value<br>
<code>bool</code> <br></p><h4>shop.augmentSelectionOpen</h4><p>Return Value<br>
<code>bool</code> <br></p><h4>shop.augmentSelectionIds</h4><p>Return Value<br>
<code>table</code> See AugmentId<br></p><pre><code>enum class AugmentId: std::uint32_t
{
    Oathsworn = 0x1F273AC,// buff_hash( "Oathsworn" )
    ShrinkRay = 0x302DB74,// buff_hash( "ShrinkRay" )
    UltimateRevolution = 0x4A9BDC5,// buff_hash( "UltimateRevolution" )
    StackosaurusRex = 0x55E5BD2,// buff_hash( "StackosaurusRex" )
    OrbitalLaser = 0x5E9FD41,// buff_hash( "OrbitalLaser" )
    ApexInventor = 0x93D7848,// buff_hash( "ApexInventor" )
    escAPADe = 0xB24AB25,// buff_hash( "escAPADe" )
    WarmupRoutine = 0xB731243,// buff_hash( "WarmupRoutine" )
    HeavyHitter = 0x109C1530,// buff_hash( "HeavyHitter" )
    SummonersRoulette = 0x13BDA632,// buff_hash( "SummonersRoulette" )
    EtherealWeapon = 0x13E8C4A7,// buff_hash( "EtherealWeapon" )
    TheBrutalizer = 0x16EA7954,// buff_hash( "TheBrutalizer" )
    ItsKillingTime = 0x17A308CA,// buff_hash( "ItsKillingTime" )
    GiantSlayer = 0x1950C668,// buff_hash( "GiantSlayer" )
    SearingDawn = 0x199AA2C8,// buff_hash( "SearingDawn" )
    CannonFodder = 0x1A9B0060,// buff_hash( "CannonFodder" )
    Quest_WoogletsWitchcap = 0x1BC0CAC1,// buff_hash( "Quest_WoogletsWitchcap" )
    SpinToWin = 0x209762E6,// buff_hash( "SpinToWin" )
    Executioner = 0x23F1EBC6,// buff_hash( "Executioner" )
    NowYouSeeMe = 0x26433BAB,// buff_hash( "NowYouSeeMe" )
    FeeltheBurn = 0x2D24DA51,// buff_hash( "FeeltheBurn" )
    ItsCritical = 0x2E4815CC,// buff_hash( "ItsCritical" )
    GambaAnvil = 0x2EFA5F6D,// buff_hash( "GambaAnvil" )
    MadScientist = 0x301C4AB9,// buff_hash( "MadScientist" )
    DontBlink = 0x361E81B4,// buff_hash( "DontBlink" )
    GuiltyPleasure = 0x38A48C10,// buff_hash( "GuiltyPleasure" )
    SkilledSniper = 0x3A3FB8AC,// buff_hash( "SkilledSniper" )
    OutlawsGrit = 0x3AFCE102,// buff_hash( "OutlawsGrit" )
    Clothesline = 0x3D187ED1,// buff_hash( "Clothesline" )
    BannerofCommand = 0x3F13BB13,// buff_hash( "BannerofCommand" )
    TwiceThrice = 0x42346B02,// buff_hash( "TwiceThrice" )
    SoulSiphon = 0x4293CD3B,// buff_hash( "SoulSiphon" )
    OmniSoul = 0x44031317,// buff_hash( "OmniSoul" )
    Chauffeur = 0x4474F5D0,// buff_hash( "Chauffeur" )
    ServeBeyondDeath = 0x4589990F,// buff_hash( "ServeBeyondDeath" )
    Quest_UrfsChampion = 0x4716448D,// buff_hash( "Quest_UrfsChampion" )
    CelestialBody = 0x47330DB1,// buff_hash( "CelestialBody" )
    IceCold = 0x48B5BDCA,// buff_hash( "IceCold" )
    FromBeginningToEnd = 0x48DC2006,// buff_hash( "FromBeginningToEnd" )
    DawnbringersResolve = 0x4B303285,// buff_hash( "DawnbringersResolve" )
    WillingSacrifice = 0x4B43BF6C,// buff_hash( "WillingSacrifice" )
    JuiceBox = 0x4D27E960,// buff_hash( "JuiceBox" )
    DontChase = 0x4DBF5E70,// buff_hash( "DontChase" )
    BreadAndCheese = 0x4E28F2C3,// buff_hash( "BreadAndCheese" )
    FrostWraith = 0x4F7D128E,// buff_hash( "FrostWraith" )
    FeyMagic = 0x52022D72,// buff_hash( "FeyMagic" )
    DrawYourSword = 0x522572E9,// buff_hash( "DrawYourSword" )
    InfernalSoul = 0x55780831,// buff_hash( "InfernalSoul" )
    QuantumComputing = 0x55EF3A40,// buff_hash( "QuantumComputing" )
    OkBoomerang = 0x58FEA611,// buff_hash( "OkBoomerang" )
    Eureka = 0x59B3AFC2,// buff_hash( "Eureka" )
    Dematerialize = 0x5C8D739F,// buff_hash( "Dematerialize" )
    BigBrain = 0x5F479857,// buff_hash( "BigBrain" )
    MysticPunch = 0x610EB7B4,// buff_hash( "MysticPunch" )
    HolyFire = 0x61BEDC9B,// buff_hash( "HolyFire" )
    SymphonyofWar = 0x64EB2265,// buff_hash( "SymphonyofWar" )
    Marksmage = 0x65A8CBEF,// buff_hash( "Marksmage" )
    ScopierWeapons = 0x662429D1,// buff_hash( "ScopierWeapons" )
    BluntForce = 0x6951004B,// buff_hash( "BluntForce" )
    MindtoMatter = 0x6AD13E9D,// buff_hash( "MindtoMatter" )
    Homeguard = 0x6CA0F337,// buff_hash( "Homeguard" )
    Earthwake = 0x6E25F1F7,// buff_hash( "Earthwake" )
    Quest_AngelofRetribution = 0x6F79D727,// buff_hash( "Quest_AngelofRetribution" )
    BuffBuddies = 0x71E56AB8,// buff_hash( "BuffBuddies" )
    NestingDoll = 0x73016C04,// buff_hash( "NestingDoll" )
    BreadAndJam = 0x75565D16,// buff_hash( "BreadAndJam" )
    Recursion = 0x76B60E67,// buff_hash( "Recursion" )
    TankItOrLeaveIt = 0x80B86F89,// buff_hash( "TankItOrLeaveIt" )
    FrozenFoundations = 0x819259C7,// buff_hash( "FrozenFoundations" )
    Vengeance = 0x886F1F8D,// buff_hash( "Vengeance" )
    WithHaste = 0x894E5EBC,// buff_hash( "WithHaste" )
    Erosion = 0x8C2F16FE,// buff_hash( "Erosion" )
    Perseverance = 0x8D3AE60E,// buff_hash( "Perseverance" )
    ChainLightning = 0x8E40C3DC,// buff_hash( "ChainLightning" )
    TrueshotProdigy = 0x93AB024B,// buff_hash( "TrueshotProdigy" )
    PhenomenalEvil = 0x94D656DE,// buff_hash( "PhenomenalEvil" )
    ContractKiller = 0x97C4EFA0,// buff_hash( "ContractKiller" )
    BacktoBasics = 0x984667B4,// buff_hash( "BacktoBasics" )
    CircleofDeath = 0x9A1D093E,// buff_hash( "CircleofDeath" )
    Castle = 0x9AC2A459,// buff_hash( "Castle" )
    JeweledGauntlet = 0x9CAAE31B,// buff_hash( "JeweledGauntlet" )
    SlapAround = 0x9DFB15EE,// buff_hash( "SlapAround" )
    RestlessRestoration = 0x9E1A180E,// buff_hash( "RestlessRestoration" )
    CriticalHealing = 0x9F40039E,// buff_hash( "CriticalHealing" )
    Flashy = 0x9F6FD4F0,// buff_hash( "Flashy" )
    MasterofDuality = 0x9F9005F8,// buff_hash( "MasterofDuality" )
    Quest_SteelYourHeart = 0xA173E08E,// buff_hash( "Quest_SteelYourHeart" )
    Impassable = 0xA1A3AF22,// buff_hash( "Impassable" )
    ScopiestWeapons = 0xA1D75DC2,// buff_hash( "ScopiestWeapons" )
    LightemUp = 0xA26FF4EC,// buff_hash( "LightemUp" )
    OceanSoul = 0xA3670B02,// buff_hash( "OceanSoul" )
    Deft = 0xA3E2E068,// buff_hash( "Deft" )
    LaserEyes = 0xA748902E,// buff_hash( "LaserEyes" )
    WisdomofAges = 0xA83CA207,// buff_hash( "WisdomofAges" )
    DemonsDance = 0xA8B08058,// buff_hash( "DemonsDance" )
    BloodBrother = 0xAB06A043,// buff_hash( "BloodBrother" )
    Minionmancer = 0xAB4CDD4D,// buff_hash( "Minionmancer" )
    Firebrand = 0xABA0CD52,// buff_hash( "Firebrand" )
    SpiritLink = 0xAC744FB8,// buff_hash( "SpiritLink" )
    MountainSoul = 0xAE76811D,// buff_hash( "MountainSoul" )
    Vulnerability = 0xAFE4CF71,// buff_hash( "Vulnerability" )
    RaidBoss = 0xB2EDEF40,// buff_hash( "RaidBoss" )
    MagicMissile = 0xB4545A74,// buff_hash( "MagicMissile" )
    WitchfulThinking = 0xB503AEA3,// buff_hash( "WitchfulThinking" )
    CantTouchThis = 0xB5D9645A,// buff_hash( "CantTouchThis" )
    ComboMaster = 0xBA23DC87,// buff_hash( "ComboMaster" )
    KeystoneConjurer = 0xBBE31DDB,// buff_hash( "KeystoneConjurer" )
    BreadAndButter = 0xBD47D568,// buff_hash( "BreadAndButter" )
    ThreadtheNeedle = 0xBE4AA497,// buff_hash( "ThreadtheNeedle" )
    SnowballFight = 0xBF2B1A73,// buff_hash( "SnowballFight" )
    Typhoon = 0xBF771C16,// buff_hash( "Typhoon" )
    MirrorImage = 0xC390B2C7,// buff_hash( "MirrorImage" )
    RabbleRousing = 0xC3A6E3C0,// buff_hash( "RabbleRousing" )
    SelfDestruct = 0xC451D495,// buff_hash( "SelfDestruct" )
    DefensiveManeuvers = 0xC488607E,// buff_hash( "DefensiveManeuvers" )
    SlowCooker = 0xCB705AEB,// buff_hash( "SlowCooker" )
    UltimateUnstoppable = 0xCDF2651B,// buff_hash( "UltimateUnstoppable" )
    ADAPt = 0xD18FEA7F,// buff_hash( "ADAPt" )
    TapDancer = 0xD1F84AA5,// buff_hash( "TapDancer" )
    Spellwake = 0xD3CBE301,// buff_hash( "Spellwake" )
    Flashbang = 0xD4DA8047,// buff_hash( "Flashbang" )
    ShadowRunner = 0xD5F12997,// buff_hash( "ShadowRunner" )
    CourageoftheColossus = 0xD68F7D08,// buff_hash( "CourageoftheColossus" )
    InfernalConduit = 0xD79FD9B0,// buff_hash( "InfernalConduit" )
    AcceleratingSorcery = 0xDA054EE4,// buff_hash( "AcceleratingSorcery" )
    Repulsor = 0xDABCBBC3,// buff_hash( "Repulsor" )
    ExtendoArm = 0xDE9A77E4,// buff_hash( "ExtendoArm" )
    ScopedWeapons = 0xE3E3DBBE,// buff_hash( "ScopedWeapons" )
    LightningStrikes = 0xE48980B4,// buff_hash( "LightningStrikes" )
    FallenAegis = 0xE67E996E,// buff_hash( "FallenAegis" )
    BladeWaltz = 0xE8A9A9F1,// buff_hash( "BladeWaltz" )
    FirstAidKit = 0xE9AFCCDF,// buff_hash( "FirstAidKit" )
    Dashing = 0xED80A0CB,// buff_hash( "Dashing" )
    FullyAutomated = 0xEDFA6D47,// buff_hash( "FullyAutomated" )
    HoldVeryStill = 0xF0544646,// buff_hash( "HoldVeryStill" )
    DieAnotherDay = 0xF0DBEF32,// buff_hash( "DieAnotherDay" )
    Goredrink = 0xF4EBEFD4,// buff_hash( "Goredrink" )
    Vanish = 0xF6A6FFEA,// buff_hash( "Vanish" )
    AllForYou = 0xFC06EEFA,// buff_hash( "AllForYou" )
    Goliath = 0xFCB87F9B,// buff_hash( "Goliath" )
    CenterOfTheUniverse = 0xFCCE03A7,// buff_hash( "CenterOfTheUniverse" )
    DiveBomber = 0xFD0ED9E4,// buff_hash( "DiveBomber" )
    ParasiticRelationship = 0xFE635A5B,// buff_hash( "ParasiticRelationship" )
    Restart = 0xFE9C11EC,// buff_hash( "Restart" )
    Tormentor = 0xFEFBCC4F,// buff_hash( "Tormentor" )
    SonicBoom = 0xFF509218,// buff_hash( "SonicBoom" )
};</code></pre><h4>shop.buyItem(itemID, preferredSlotID)</h4><h4>shop.sellItem(inventorySlotId)</h4><h4>shop.undo()</h4><h4>shop.swapItem(source, dest)</h4><h3 id="libraries-memory">memory</h3><p>Valid types:<br></p><ul>
<li>char</li>
<li>unsigned char</li>
<li>short</li>
<li>unsigned short</li>
<li>int</li>
<li>unsigned int</li>
<li>long long</li>
<li>unsigned long long</li>
<li>float</li>
</ul><h4>memory.new(type, n)</h4><p>Parameters<br>
<code>string</code> type<br>
<code>number</code> n<br>
Return Value<br>
<code>mixed</code> c-type array<br></p><h3 id="libraries-input">input</h3><p>Enums:<br></p><ul>
<li>input.LOCK_MOVEMENT</li>
<li>input.LOCK_ABILITIES</li>
<li>input.LOCK_SUMMONERSPELLS</li>
<li>input.LOCK_SHOP</li>
<li>input.LOCK_CHAT</li>
<li>input.LOCK_MINIMAPMOVEMENT</li>
<li>input.LOCK_CAMERAMOVEMENT</li>
</ul><h4>input.lock(…)</h4><p>Parameters<br>
<code>number</code> input_type<br>
Return Value<br>
<code>void</code> <br></p><h4>input.unlock(…)</h4><p>Parameters<br>
<code>number</code> input_type<br>
Return Value<br>
<code>void</code> <br></p><h4>input.islocked(…)</h4><p>Parameters<br>
<code>number</code> input_type<br>
Return Value<br>
<code>boolean</code> <br></p><h4>input.lock_slot(…)</h4><p>Parameters<br>
<code>number</code> slot<br>
Return Value<br>
<code>void</code> <br></p><h4>input.unlock_slot(…)</h4><p>Parameters<br>
<code>number</code> slot<br>
Return Value<br>
<code>void</code> <br></p><h4>input.islocked_slot(…)</h4><p>Parameters<br>
<code>number</code> slot<br>
Return Value<br>
<code>boolean</code> <br></p><h3 id="libraries-keyboard">keyboard</h3><h4>keyboard.isKeyDown(key_code)</h4><p>Parameters<br>
<code>number</code> key_code<br>
Return Value<br>
<code>boolean</code> returns true if key_code is down<br></p><pre><code class="lang-lua"><span class="local">local</span> <span class="keyword">function</span> on_tick<span class="include">(</span><span class="include">)</span>
    <span class="keyword">if</span> keyboard.isKeyDown<span class="include">(</span><span class="number">0</span>x<span class="number">20</span><span class="include">)</span> <span class="keyword">then</span> <span class="note">--</span>spacebar
        <span class="print">print</span><span class="include">(</span><span class="string">'spacebar pressed'</span><span class="include">)</span>
    <span class="keyword">end</span>
<span class="keyword">end</span>

<span class="cb">cb</span>.<span class="add">add</span><span class="include">(</span><span class="cb">cb</span>.<span class="cbfunc">tick</span>, on_tick<span class="include">)</span></code></pre><h4>keyboard.getClipboardText()</h4><p>Return Value<br>
<code>string</code> returns text that was copied to clipboard<br>
<code>string</code> returns error message on failure<br></p><h4>keyboard.setClipboardText(text)</h4><p>Parameters<br>
<code>string</code> text<br>
Return Value<br>
<code>void</code> copies text to clipboard<br></p><h4>keyboard.keyCodeToString(key_code)</h4><p>Parameters<br>
<code>number</code> key_code<br>
Return Value<br>
<code>string</code> returns corresponding character of key_code<br></p><pre><code class="lang-lua"><span class="print">print</span><span class="include">(</span>keyboard.keyCodeToString<span class="include">(</span><span class="number">0</span>x<span class="number">20</span><span class="include">)</span><span class="include">)</span></code></pre><h4>keyboard.stringToKeyCode(key)</h4><p>Parameters<br>
<code>string</code> key<br>
Return Value<br>
<code>number</code> returns corresponding key_code of key<br></p><pre><code class="lang-lua"><span class="print">print</span><span class="include">(</span>keyboard.stringToKeyCode<span class="include">(</span><span class="string">'Space'</span><span class="include">)</span><span class="include">)</span></code></pre><h3 id="libraries-permashow">permashow</h3><h4>permashow.enable(v1)</h4><p>Parameters<br>
<code>boolean</code> enabled<br></p><h4>permashow.set_pos(x, y)</h4><p>Parameters<br>
<code>number</code> x<br>
<code>number</code> y<br></p><h4>permashow.set_drag_enabled(option)</h4><p>Parameters<br>
<code>number</code> option, 1 = enabled always, 2 = enabled when menu show<br></p><h4>permashow.set_alpha(alpha)</h4><p>Parameters<br>
<code>number</code> alpha<br></p><h4>permashow.set_theme(theme)</h4><p>Parameters<br>
<code>table</code> theme<br></p><pre><code class="lang-lua"><span class="note">--</span> simple, set the alpha
permashow.set_alpha<span class="include">(</span><span class="number">100</span><span class="include">)</span>

<span class="note">--</span> advanced usage, set custom theme <span class="include">(</span>alpha will be ignored<span class="include">)</span>
permashow.set_theme<span class="include">(</span><span class="include">{</span>
    itemHeight = <span class="number">20</span>,
    textSize = <span class="number">14</span>,
    textColor = <span class="number">0</span>xFFFFF<span class="number">799</span>,
    textColorDisabled = <span class="number">0</span>xFFA<span class="number">8</span>A<span class="number">8</span>A<span class="number">8</span>,

    shadowColor = <span class="number">0</span>x<span class="number">90000000</span>,
    backgroundUpLeft = <span class="number">0</span>x<span class="number">90797145</span>,
    backgroundUpRight = <span class="number">0</span>x<span class="number">904</span>a<span class="number">3</span>f<span class="number">23</span>,
    backgroundBottomLeft = <span class="number">0</span>x<span class="number">90797145</span>,
    backgroundBottomRight = <span class="number">0</span>x<span class="number">904</span>a<span class="number">3</span>f<span class="number">23</span>,
    areaUpLeft = <span class="number">0</span>x<span class="number">90091</span>e<span class="number">18</span>,
    areaUpRight = <span class="number">0</span>x<span class="number">90091</span>e<span class="number">18</span>,
    areaBottomLeft = <span class="number">0</span>x<span class="number">9005120</span>c,
    areaBottomRight = <span class="number">0</span>x<span class="number">9005120</span>c,

    itemBorder<span class="number">1</span> = <span class="number">0</span>x<span class="number">33</span>f<span class="number">4</span>f<span class="number">499</span>,
    itemBorder<span class="number">2</span> = <span class="number">0</span>x<span class="number">11</span>f<span class="number">4</span>f<span class="number">499</span>,
    itemBorder<span class="number">3</span> = <span class="number">0</span>x<span class="number">33</span>f<span class="number">4</span>f<span class="number">499</span>,
<span class="include">}</span><span class="include">)</span>
permashow.enable<span class="include">(</span>true<span class="include">)</span></code></pre><h3 id="libraries-network">network</h3><h4>network.latency</h4><p>Return Value<br>
<code>number</code> returns game network latency in seconds<br></p><h4>network.download_file(url, dest)</h4><p>Parameters<br>
<code>string</code> url<br>
<code>string</code> dest<br>
Return Value<br>
<code>boolean</code> returns true on success<br></p><pre><code class="lang-lua"><span class="local">local</span> url = <span class="string">'https:<span class="comment">//i.imgur.com/k<span class="number">6</span>HB<span class="number">1</span>gA.gif'</span></span>
<span class="local">local</span> dest = hanbot.luapath..<span class="string">'/ok.png'</span>
<span class="local">local</span> success = network.download_file<span class="include">(</span>url, dest<span class="include">)</span>
<span class="print">print</span><span class="include">(</span>success<span class="include">)</span></code></pre><h4>network.easy_download(cb, uri, path)</h4><p>Parameters<br>
<code>function</code> cb<br>
<code>string</code> uri<br>
<code>string</code> dest<br>
Return Value<br>
<code>void</code> <br></p><h4>network.easy_post(cb, uri, postfields)</h4><p>Parameters<br>
<code>function</code> cb<br>
<code>string</code> uri<br>
<code>string</code> postfields<br>
Return Value<br>
<code>void</code> <br></p><h3 id="libraries-module">module</h3><h4>module.seek(mod)</h4><p>Parameters<br>
<code>string</code> mod<br>
Return Value<br>
<code>module</code> returns module if it is loaded<br></p><pre><code class="lang-lua"><span class="local">local</span> <span class="keyword">function</span> on_tick<span class="include">(</span><span class="include">)</span>
    <span class="note">--</span><span class="module">module</span>.seek will only <span class="keyword">return</span> the <span class="module">module</span> <span class="keyword">if</span> its loaded
    <span class="local">local</span> evade = <span class="module">module</span>.seek<span class="include">(</span><span class="string">'evade'</span><span class="include">)</span>
    <span class="keyword">if</span> evade and evade.core.is_active<span class="include">(</span><span class="include">)</span> <span class="keyword">then</span> <span class="keyword">return</span> <span class="keyword">end</span>
<span class="keyword">end</span>

<span class="cb">cb</span>.<span class="add">add</span><span class="include">(</span><span class="cb">cb</span>.<span class="cbfunc">tick</span>, on_tick<span class="include">)</span></code></pre><h4>module.load(id, mod)</h4><p>Parameters<br>
<code>string</code> id<br>
<code>string</code> mod<br>
Return Value<br>
<code>module</code> returns module, loads it if needed<br></p><pre><code class="lang-lua"><span class="local">local</span> foo_bar = <span class="module">module</span>.load<span class="include">(</span><span class="string">'foo'</span>, <span class="string">'bar'</span><span class="include">)</span></code></pre><h4>module.internal(mod)</h4><p>Parameters<br>
<code>string</code> mod<br>
Return Value<br>
<code>module</code> returns module, loads it if needed<br></p><pre><code class="lang-lua"><span class="local">local</span> <span class="orb">orb</span> = <span class="module">module</span>.<span class="module">internal</span><span class="include">(</span><span class="string">'<span class="orb">orb</span>'</span><span class="include">)</span>
<span class="local">local</span> <span class="keyword">function</span> on_tick<span class="include">(</span><span class="include">)</span>
    <span class="local">local</span> evade = <span class="module">module</span>.seek<span class="include">(</span><span class="string">'evade'</span><span class="include">)</span>
    <span class="keyword">if</span> evade and evade.core.is_active<span class="include">(</span><span class="include">)</span> <span class="keyword">then</span> <span class="keyword">return</span> <span class="keyword">end</span>
<span class="keyword">end</span>

<span class="cb">cb</span>.<span class="add">add</span><span class="include">(</span><span class="cb">cb</span>.<span class="cbfunc">tick</span>, on_tick<span class="include">)</span></code></pre><h4>module.lib(id)</h4><p>Parameters<br>
<code>string</code> id<br>
Return Value<br>
<code>library</code> returns library, loads it if needed<br></p><h4>module.path(id)</h4><p>Parameters<br>
<code>string</code> id<br>
Return Value<br>
<code>string</code> returns module path<br></p><h4>module.is_shard(id)</h4><p>Parameters<br>
<code>string</code> id<br>
Return Value<br>
<code>boolean</code> returns true if shard id exists<br></p><h4>module.is_libshard(id)</h4><p>Parameters<br>
<code>string</code> id<br>
Return Value<br>
<code>boolean</code> returns true if libshard id exists<br></p><h4>module.is_anyshard(id)</h4><p>Parameters<br>
<code>string</code> id<br>
Return Value<br>
<code>boolean</code> returns true if shard id or libshard id exists<br></p><h4>module.file_exists(path)</h4><p>Parameters<br>
<code>string</code> path<br>
Return Value<br>
<code>boolean</code> returns true file exists<br></p><h4>module.directory_exists(path)</h4><p>Parameters<br>
<code>string</code> path<br>
Return Value<br>
<code>boolean</code> returns true file exists<br></p><h4>module.create_directory(id, path)</h4><p>Parameters<br>
<code>string</code> id<br>
<code>string</code> path<br>
Return Value<br>
<code>boolean</code> returns true on success<br></p><h4>module.create_script_directory(path)</h4><p>Parameters<br>
<code>string</code> path<br>
Return Value<br>
<code>boolean</code> returns true on success<br></p><h4>module.create_lib_directory(path)</h4><p>Parameters<br>
<code>string</code> path<br>
Return Value<br>
<code>boolean</code> returns true on success<br></p><h4>module.create_shard_directory(path)</h4><p>Parameters<br>
<code>string</code> path<br>
Return Value<br>
<code>boolean</code> returns true on success<br></p><h4>module.open_file(id, path, mode)</h4><p>Parameters<br>
<code>string</code> id<br>
<code>string</code> path<br>
<code>string</code> mod<br>
Return Value<br>
<code>file</code> returns file handle on success<br></p><h4>module.open_script_file(path, mode)</h4><p>Parameters<br>
<code>string</code> path<br>
<code>string</code> mod<br>
Return Value<br>
<code>file</code> returns file handle on success<br></p><h4>module.open_lib_file(path, mode)</h4><p>Parameters<br>
<code>string</code> path<br>
<code>string</code> mod<br>
Return Value<br>
<code>file</code> returns file handle on success<br></p><h4>module.open_shard_file(path, mode)</h4><p>Parameters<br>
<code>string</code> path<br>
<code>string</code> mod<br>
Return Value<br>
<code>file</code> returns file handle on success<br></p><h4>module.delete_file(id, path)</h4><p>Parameters<br>
<code>string</code> id<br>
<code>string</code> path<br>
Return Value<br>
<code>boolean</code> returns true on success<br></p><h4>module.delete_script_file(path)</h4><p>Parameters<br>
<code>string</code> path<br>
Return Value<br>
<code>boolean</code> returns true on success<br></p><h4>module.delete_lib_file(path)</h4><p>Parameters<br>
<code>string</code> path<br>
Return Value<br>
<code>boolean</code> returns true on success<br></p><h4>module.delete_shard_file(path)</h4><p>Parameters<br>
<code>string</code> path<br>
Return Value<br>
<code>boolean</code> returns true on success<br></p><h4>module.rename_file(id, old, new)</h4><p>Parameters<br>
<code>string</code> id<br>
<code>string</code> old<br>
<code>string</code> new<br>
Return Value<br>
<code>boolean</code> returns true on success<br></p><h4>module.rename_script_file(old, new)</h4><p>Parameters<br>
<code>string</code> old<br>
<code>string</code> new<br>
Return Value<br>
<code>boolean</code> returns true on success<br></p><h4>module.rename_lib_file(old, new)</h4><p>Parameters<br>
<code>string</code> old<br>
<code>string</code> new<br>
Return Value<br>
<code>boolean</code> returns true on success<br></p><h4>module.rename_shard_file(old, new)</h4><p>Parameters<br>
<code>string</code> old<br>
<code>string</code> new<br>
Return Value<br>
<code>boolean</code> returns true on success<br></p><h4>module.generate_tree(id, hash, anyfile)</h4><p>Parameters<br>
<code>string</code> id<br>
<code>string</code> hash<br>
<code>boolean</code> anyfile<br>
Return Value<br>
<code>table</code> returns file tree<br></p><h3 id="libraries-menu">menu</h3><h4>menu(var, text)</h4><p>Parameters<br>
<code>string</code> var<br>
<code>string</code> text<br>
Return Value<br>
<code>object</code> returns menu instance<br></p><pre><code class="lang-lua"><span class="local">local</span> myMenu = menu<span class="include">(</span><span class="string">'example_menu'</span>, <span class="string">'Example Menu'</span><span class="include">)</span></code></pre><h4>menu:header(var, text)</h4><p>Parameters<br>
<code>string</code> var<br>
<code>string</code> text<br>
Return Value<br>
<code>void</code><br></p><pre><code class="lang-lua"><span class="local">local</span> myMenu = menu<span class="include">(</span><span class="string">'example_menu'</span>, <span class="string">'Example Menu'</span><span class="include">)</span>
myMenu:header<span class="include">(</span><span class="string">'example_header'</span>, <span class="string">'Example Header'</span><span class="include">)</span></code></pre><h4>menu:boolean(var, text, value)</h4><p>Parameters<br>
<code>string</code> var<br>
<code>string</code> text<br>
<code>boolean</code> value<br>
Return Value<br>
<code>void</code><br></p><pre><code class="lang-lua"><span class="local">local</span> myMenu = menu<span class="include">(</span><span class="string">'example_menu'</span>, <span class="string">'Example Menu'</span><span class="include">)</span>
myMenu:boolean<span class="include">(</span><span class="string">'example_boolean'</span>, <span class="string">'Example Boolean'</span><span class="include">)</span>

<span class="local">local</span> <span class="keyword">function</span> on_tick<span class="include">(</span><span class="include">)</span>
    <span class="keyword">if</span> myMenu.example_boolean:get<span class="include">(</span><span class="include">)</span> <span class="keyword">then</span>
        <span class="print">print</span><span class="include">(</span><span class="string">'example_boolean is true'</span><span class="include">)</span>
    <span class="keyword">end</span>
<span class="keyword">end</span>

<span class="cb">cb</span>.<span class="add">add</span><span class="include">(</span><span class="cb">cb</span>.<span class="cbfunc">tick</span>, on_tick<span class="include">)</span></code></pre><h4>menu:slider(var, text, value, min, max, step)</h4><p>Parameters<br>
<code>string</code> var<br>
<code>string</code> text<br>
<code>number</code> value<br>
<code>number</code> min<br>
<code>number</code> max<br>
<code>number</code> step<br>
Return Value<br>
<code>void</code><br></p><pre><code class="lang-lua"><span class="local">local</span> myMenu = menu<span class="include">(</span><span class="string">'example_menu'</span>, <span class="string">'Example Menu'</span><span class="include">)</span>
myMenu:slider<span class="include">(</span><span class="string">'example_slider'</span>, <span class="string">'Example Slider'</span>, <span class="number">100</span>, <span class="number">0</span>, <span class="number">150</span>, <span class="number">5</span><span class="include">)</span>

<span class="local">local</span> <span class="keyword">function</span> on_tick<span class="include">(</span><span class="include">)</span>
    <span class="print">print</span><span class="include">(</span>myMenu.example_slider:get<span class="include">(</span><span class="include">)</span><span class="include">)</span>
<span class="keyword">end</span>

<span class="cb">cb</span>.<span class="add">add</span><span class="include">(</span><span class="cb">cb</span>.<span class="cbfunc">tick</span>, on_tick<span class="include">)</span></code></pre><h4>menu:keybind(var, text, key, toggle, defaultToggleValue)</h4><p>Parameters<br>
<code>string</code> var<br>
<code>string</code> text<br>
<code>string</code> key<br>
<code>string</code> toggle<br>
<code>bool</code> defaultToggleValue<br>
Return Value<br>
<code>void</code><br></p><pre><code class="lang-lua"><span class="local">local</span> myMenu = menu<span class="include">(</span><span class="string">'example_menu'</span>, <span class="string">'Example Menu'</span><span class="include">)</span>
<span class="note">--</span>this creates an on key down keybind <span class="keyword">for</span> <span class="string">'A'</span>
myMenu:keybind<span class="include">(</span><span class="string">'example_keybind_a'</span>, <span class="string">'Example Keybind A'</span>, <span class="string">'A'</span>, nil<span class="include">)</span>
<span class="note">--</span>this creates a toggle keybind <span class="keyword">for</span> <span class="string">'A'</span>
myMenu:keybind<span class="include">(</span><span class="string">'example_keybind_b'</span>, <span class="string">'Example Keybind B'</span>, nil, <span class="string">'A'</span><span class="include">)</span>
<span class="note">--</span>this creates an on key down keybind <span class="keyword">for</span> <span class="string">'A'</span> or toggle <span class="keyword">for</span> <span class="string">'B'</span>
myMenu:keybind<span class="include">(</span><span class="string">'example_keybind_c'</span>, <span class="string">'Example Keybind C'</span>, <span class="string">'A'</span>, <span class="string">'B'</span><span class="include">)</span>
<span class="note">--</span>this disable the permashow <span class="keyword">for</span> Keybind C
myMenu.example_keybind_c:permashow<span class="include">(</span>false<span class="include">)</span>

<span class="local">local</span> <span class="keyword">function</span> on_tick<span class="include">(</span><span class="include">)</span>
    <span class="keyword">if</span> myMenu.example_keybind_a:get<span class="include">(</span><span class="include">)</span> <span class="keyword">then</span>
        <span class="print">print</span><span class="include">(</span><span class="string">'example_keybind_a is active'</span><span class="include">)</span>
    <span class="keyword">end</span>
<span class="keyword">end</span>

<span class="cb">cb</span>.<span class="add">add</span><span class="include">(</span><span class="cb">cb</span>.<span class="cbfunc">tick</span>, on_tick<span class="include">)</span></code></pre><h4>menu:dropdown(var, text, value, options)</h4><p>Parameters<br>
<code>string</code> var<br>
<code>string</code> text<br>
<code>number</code> value<br>
<code>table</code> options<br>
Return Value<br>
<code>void</code><br></p><pre><code class="lang-lua"><span class="local">local</span> myMenu = menu<span class="include">(</span><span class="string">'example_menu'</span>, <span class="string">'Example Menu'</span><span class="include">)</span>
myMenu:dropdown<span class="include">(</span><span class="string">'example_dropdown'</span>, <span class="string">'Example Dropdown'</span>, <span class="number">1</span>, <span class="include">{</span><span class="string">'Option A'</span>, <span class="string">'Option B'</span>, <span class="string">'Option C'</span>,<span class="include">}</span><span class="include">)</span>

<span class="local">local</span> <span class="keyword">function</span> on_tick<span class="include">(</span><span class="include">)</span>
    <span class="print">print</span><span class="include">(</span>myMenu.example_dropdown:get<span class="include">(</span><span class="include">)</span><span class="include">)</span>
<span class="keyword">end</span>

<span class="cb">cb</span>.<span class="add">add</span><span class="include">(</span><span class="cb">cb</span>.<span class="cbfunc">tick</span>, on_tick<span class="include">)</span></code></pre><h4>menu:button(var, text, buttonText, callback)</h4><p>Parameters<br>
<code>string</code> var<br>
<code>string</code> text<br>
<code>string</code> buttonText<br>
<code>function</code> callback<br>
Return Value<br>
<code>void</code><br></p><pre><code class="lang-lua"><span class="local">local</span> myMenu = menu<span class="include">(</span><span class="string">'example_menu'</span>, <span class="string">'Example Menu'</span><span class="include">)</span>
myMenu:button<span class="include">(</span><span class="string">'example_button'</span>, <span class="string">'Example Button'</span>, <span class="string">'My Button'</span>, <span class="keyword">function</span><span class="include">(</span><span class="include">)</span> <span class="print">print</span><span class="include">(</span><span class="string">'button was pressed'</span><span class="include">)</span> <span class="keyword">end</span><span class="include">)</span></code></pre><h4>menu:color(var, text, red, green, blue, alpha)</h4><p>Parameters<br>
<code>string</code> var<br>
<code>string</code> text<br>
<code>number</code> red<br>
<code>number</code> green<br>
<code>number</code> blue<br>
<code>number</code> alpha<br>
Return Value<br>
<code>void</code><br></p><pre><code class="lang-lua"><span class="local">local</span> myMenu = menu<span class="include">(</span><span class="string">'example_menu'</span>, <span class="string">'Example Menu'</span><span class="include">)</span>
myMenu:color<span class="include">(</span><span class="string">'example_color'</span>, <span class="string">'Example Color'</span>, <span class="number">255</span>, <span class="number">0</span>, <span class="number">0</span>, <span class="number">255</span><span class="include">)</span>

<span class="local">local</span> <span class="keyword">function</span> on_draw<span class="include">(</span><span class="include">)</span>
    graphics.draw_text_<span class="number">2</span>D<span class="include">(</span><span class="string">'foo'</span>, <span class="number">14</span>, game.cursorPos.x, game.cursorPos.y, myMenu.example_color:get<span class="include">(</span><span class="include">)</span><span class="include">)</span>
<span class="keyword">end</span>

<span class="cb">cb</span>.<span class="add">add</span><span class="include">(</span><span class="cb">cb</span>.<span class="cbfunc">draw</span>, on_draw<span class="include">)</span></code></pre><h4>menu:isopen()</h4><p>Return Value<br>
<code>boolean</code> returns true if the menu is currently open<br></p><pre><code class="lang-lua"><span class="local">local</span> myMenu = menu<span class="include">(</span><span class="string">'example_menu'</span>, <span class="string">'Example Menu'</span><span class="include">)</span>

<span class="local">local</span> <span class="keyword">function</span> on_tick<span class="include">(</span><span class="include">)</span>
    <span class="keyword">if</span> myMenu:isopen<span class="include">(</span><span class="include">)</span> <span class="keyword">then</span>
        <span class="print">print</span><span class="include">(</span><span class="string">'menu is open'</span><span class="include">)</span>
    <span class="keyword">end</span>
<span class="keyword">end</span>

<span class="cb">cb</span>.<span class="add">add</span><span class="include">(</span><span class="cb">cb</span>.<span class="cbfunc">tick</span>, on_tick<span class="include">)</span></code></pre><h4>menu:set(property, value)</h4><p>Parameters<br>
<code>string</code> property<br>
<code>mixed</code> value<br>
Return Value<br>
<code>void</code><br></p><pre><code class="lang-lua"><span class="note">--</span>[[
The following properties can be set on their respective instances:
    <span class="string">'tooltip'</span>
    <span class="string">'callback'</span>
    <span class="string">'value'</span>
    <span class="string">'text'</span>
    <span class="string">'visible'</span>
    <span class="string">'buttonText'</span>
    <span class="string">'red'</span>
    <span class="string">'green'</span>
    <span class="string">'blue'</span>
    <span class="string">'alpha'</span>
    <span class="string">'options'</span>
    <span class="string">'toggleValue'</span>
    <span class="string">'toggle'</span>
    <span class="string">'key'</span>
    <span class="string">'min'</span>
    <span class="string">'max'</span>
    <span class="string">'step'</span>,
    <span class="string">'icon'</span>,
]]

<span class="local">local</span> myMenu = menu<span class="include">(</span><span class="string">'example_menu'</span>, <span class="string">'Example Menu'</span><span class="include">)</span>
myMenu:slider<span class="include">(</span><span class="string">'example_slider'</span>, <span class="string">'Example Slider'</span>, <span class="number">100</span>, <span class="number">0</span>, <span class="number">150</span>, <span class="number">5</span><span class="include">)</span>

myMenu.example_slider:set<span class="include">(</span><span class="string">'tooltip'</span>, <span class="string">'This text will appear when the mouse is hovering Example Slider'</span><span class="include">)</span>
myMenu.example_slider:set<span class="include">(</span><span class="string">'callback'</span>, <span class="keyword">function</span><span class="include">(</span>old, <span class="keyword">new</span><span class="include">)</span>
    <span class="print">print</span><span class="include">(</span><span class="include">(</span><span class="string">'example_slider changed from %u to %u'</span><span class="include">)</span>:format<span class="include">(</span>old, <span class="keyword">new</span><span class="include">)</span><span class="include">)</span>
<span class="keyword">end</span><span class="include">)</span>

<span class="local">local</span> myIcon = graphics.<span class="cbfunc">sprite</span><span class="include">(</span><span class="string">'XXX/menu_icon.png'</span><span class="include">)</span>
myMenu.example_slider:set<span class="include">(</span><span class="string">'icon'</span>, myIcon<span class="include">)</span></code></pre><h3 id="libraries-md5">md5</h3><h4>md5.file(path)</h4><p>Parameters<br>
<code>string</code> path<br>
Return Values<br>
<code>string</code> returns md5 hash</p><h4>md5.sum(str)</h4><p>Parameters<br>
<code>string</code> str<br>
Return Values<br>
<code>string</code> returns integer md5 hash</p><h4>md5.tohex(str, upper)</h4><p>Parameters<br>
<code>string</code> str<br>
<code>boolean</code> upper<br>
Return Values<br>
<code>string</code> returns str in hex, in uppercase if upper is true</p><h1 id="objects">Objects</h1><h3 id="objects-baseobj">base.obj</h3><p>Properties:<br></p><ul>
<li><code>boolean</code> base.valid</li>
<li><code>number</code> base.type</li>
<li><code>number</code> base.index<blockquote>
<p>The index of object</p>
</blockquote>
</li>
<li><code>number</code> base.index32<blockquote>
<p>The object_id of object</p>
</blockquote>
</li>
<li><code>number</code> base.team</li>
<li><code>string</code> base.name</li>
<li><code>number</code> base.networkID</li>
<li><code>number</code> base.networkID32<blockquote>
<p>The network_id of object</p>
</blockquote>
</li>
<li><code>number</code> base.x</li>
<li><code>number</code> base.y</li>
<li><code>number</code> base.z</li>
<li><code>vec3</code> base.pos</li>
<li><code>vec2</code> base.pos2D</li>
<li><code>boolean</code> base.isOnScreen</li>
<li><code>number</code> base.selectionHeight</li>
<li><code>number</code> base.selectionRadius</li>
<li><code>vec3</code> base.minBoundingBox</li>
<li><code>vec3</code> base.maxBoundingBox</li>
</ul><h3 id="objects-heroobj">hero.obj</h3><p>Properties:<br></p><ul>
<li><code>string</code> hero.name</li>
<li><code>string</code> hero.charName</li>
<li><code>string</code> hero.recallName</li>
<li><code>texture.obj</code> hero.iconCircle</li>
<li><code>texture.obj</code> hero.iconSquare</li>
<li><code>string</code> hero.recallName</li>
<li><code>boolean</code> hero.isOnScreen</li>
<li><code>boolean</code> hero.inShopRange</li>
<li><code>boolean</code> hero.isDead</li>
<li><code>boolean</code> hero.isAlive</li>
<li><code>boolean</code> hero.isVisible</li>
<li><code>boolean</code> hero.isRecalling</li>
<li><code>boolean</code> hero.isTargetable</li>
<li><code>boolean</code> hero.parEnabled</li>
<li><code>boolean</code> hero.sarEnabled</li>
<li><code>vec3</code> hero.pos</li>
<li><code>vec3</code> hero.direction</li>
<li><code>vec2</code> hero.pos2D</li>
<li><code>vec2</code> hero.direction2D</li>
<li><code>vec2</code> hero.barPos</li>
<li><code>path.obj</code> hero.path</li>
<li><code>spell.obj</code> hero.activeSpell</li>
<li><code>table</code> hero.buff</li>
</ul><pre><code class="lang-lua"><span class="note">--</span> use BUFF_XXX <span class="keyword">for</span> buff type
<span class="keyword">if</span> <span class="hero">hero</span>.buff[BUFF_BLIND] <span class="keyword">then</span>
    <span class="print">print</span><span class="include">(</span><span class="string">'<span class="hero">player</span> blind'</span><span class="include">)</span>
<span class="keyword">end</span>

<span class="note">--</span> Use the name<span class="include">(</span><span class="keyword">in</span> lowercase<span class="include">)</span> to get the the specified name
<span class="keyword">if</span> <span class="hero">hero</span>.buff[<span class="string">'kaisaeevolved'</span>] <span class="keyword">then</span>
    <span class="print">print</span><span class="include">(</span><span class="string">'has buff KaisaEEvolved'</span><span class="include">)</span>
<span class="keyword">end</span></code></pre><ul>
<li><code>runemanager.obj</code> hero.rune</li>
<li><code>number</code> hero.type</li>
<li><code>number</code> hero.index</li>
<li><code>number</code> hero.networkID</li>
<li><code>number</code> hero.networkID32</li>
<li><code>number</code> hero.team</li>
<li><code>number</code> hero.x</li>
<li><code>number</code> hero.y</li>
<li><code>number</code> hero.z</li>
<li><code>number</code> hero.selectionHeight</li>
<li><code>number</code> hero.selectionRadius</li>
<li><code>number</code> hero.boundingRadius</li>
<li><code>number</code> hero.overrideCollisionRadius</li>
<li><code>number</code> hero.pathfindingCollisionRadius</li>
<li><code>vec3</code> hero.minBoundingBox</li>
<li><code>vec3</code> hero.maxBoundingBox</li>
<li><code>number</code> hero.deathTime</li>
<li><code>number</code> hero.health</li>
<li><code>number</code> hero.maxHealth</li>
<li><code>number</code> hero.maxHealthPenalty</li>
<li><code>number</code> hero.allShield</li>
<li><code>number</code> hero.physicalShield</li>
<li><code>number</code> hero.magicalShield</li>
<li><code>number</code> hero.champSpecificHealth</li>
<li><code>number</code> hero.stopShieldFade</li>
<li><code>number</code> hero.isTargetableToTeamFlags</li>
<li><code>number</code> hero.mana</li>
<li><code>number</code> hero.maxMana</li>
<li><code>number</code> hero.baseAttackDamage</li>
<li><code>number</code> hero.baseAd<blockquote>
<p>alias of <code>baseAttackDamage</code></p>
</blockquote>
</li>
<li><code>number</code> hero.bonusAd</li>
<li><code>number</code> hero.totalAd<blockquote>
<p>The TotalAttackDamage</p>
</blockquote>
</li>
<li><code>number</code> hero.totalAp<blockquote>
<p>The TotalAbilityPower</p>
</blockquote>
</li>
<li><code>number</code> hero.armor</li>
<li><code>number</code> hero.spellBlock</li>
<li><code>number</code> hero.attackSpeedMod</li>
<li><code>number</code> hero.flatPhysicalDamageMod</li>
<li><code>number</code> hero.percentPhysicalDamageMod</li>
<li><code>number</code> hero.flatMagicDamageMod</li>
<li><code>number</code> hero.percentMagicDamageMod</li>
<li><code>number</code> hero.healthRegenRate</li>
<li><code>number</code> hero.bonusArmor</li>
<li><code>number</code> hero.bonusSpellBlock</li>
<li><code>number</code> hero.flatBubbleRadiusMod</li>
<li><code>number</code> hero.percentBubbleRadiusMod</li>
<li><code>number</code> hero.moveSpeed</li>
<li><code>number</code> hero.moveSpeedBaseIncrease</li>
<li><code>number</code> hero.scaleSkinCoef</li>
<li><code>number</code> hero.gold</li>
<li><code>number</code> hero.goldTotal</li>
<li><code>number</code> hero.minimumGold</li>
<li><code>number</code> hero.evolvePoints</li>
<li><code>number</code> hero.evolveFlag</li>
<li><code>number</code> hero.inputLocks</li>
<li><code>number</code> hero.skillUpLevelDeltaReplicate</li>
<li><code>number</code> hero.manaCost0</li>
<li><code>number</code> hero.manaCost1</li>
<li><code>number</code> hero.manaCost2</li>
<li><code>number</code> hero.manaCost3</li>
<li><code>number</code> hero.baseAbilityDamage</li>
<li><code>number</code> hero.dodge</li>
<li><code>number</code> hero.crit</li>
<li><code>number</code> hero.parRegenRate</li>
<li><code>number</code> hero.attackRange</li>
<li><code>number</code> hero.flatMagicReduction</li>
<li><code>number</code> hero.percentMagicReduction</li>
<li><code>number</code> hero.flatCastRangeMod</li>
<li><code>number</code> hero.percentCooldownMod</li>
<li><code>number</code> hero.passiveCooldownEndTime</li>
<li><code>number</code> hero.passiveCooldownTotalTime</li>
<li><code>number</code> hero.flatArmorPenetration</li>
<li><code>number</code> hero.percentArmorPenetration</li>
<li><code>number</code> hero.flatMagicPenetration</li>
<li><code>number</code> hero.percentMagicPenetration</li>
<li><code>number</code> hero.percentLifeStealMod</li>
<li><code>number</code> hero.percentSpellVampMod</li>
<li><code>number</code> hero.percentOmnivamp</li>
<li><code>number</code> hero.percentPhysicalVamp</li>
<li><code>number</code> hero.percentBonusArmorPenetration</li>
<li><code>number</code> hero.percentCritBonusArmorPenetration</li>
<li><code>number</code> hero.percentCritTotalArmorPenetration</li>
<li><code>number</code> hero.percentBonusMagicPenetration</li>
<li><code>number</code> hero.physicalLethality</li>
<li><code>number</code> hero.magicLethality</li>
<li><code>number</code> hero.baseHealthRegenRate</li>
<li><code>number</code> hero.primaryARBaseRegenRateRep</li>
<li><code>number</code> hero.secondaryARRegenRateRep</li>
<li><code>number</code> hero.secondaryARBaseRegenRateRep</li>
<li><code>number</code> hero.percentCooldownCapMod</li>
<li><code>number</code> hero.percentEXPBonus</li>
<li><code>number</code> hero.flatBaseAttackDamageMod</li>
<li><code>number</code> hero.percentBaseAttackDamageMod</li>
<li><code>number</code> hero.baseAttackDamageSansPercentScale</li>
<li><code>number</code> hero.exp</li>
<li><code>number</code> hero.par</li>
<li><code>number</code> hero.maxPar</li>
<li><code>number</code> hero.sar</li>
<li><code>number</code> hero.maxSar</li>
<li><code>number</code> hero.pathfindingRadiusMod</li>
<li><code>number</code> hero.levelRef</li>
<li><code>number</code> hero.numNeutralMinionsKilled</li>
<li><code>number</code> hero.primaryArRegenRateRep</li>
<li><code>number</code> hero.physicalDamagePercentageModifier</li>
<li><code>number</code> hero.magicalDamagePercentageModifier</li>
<li><code>number</code> hero.baseHealth</li>
<li><code>number</code> hero.baseMana</li>
<li><code>number</code> hero.baseManaPerLevel</li>
<li><code>number</code> hero.combatType</li>
<li><code>number</code> hero.critDamageMultiplier</li>
<li><code>number</code> hero.abilityHasteMod</li>
<li><code>number</code> hero.baseMoveSpeed</li>
<li><code>number</code> hero.baseAttackRange</li>
<li><code>bool</code> hero.isZombie</li>
<li><code>bool</code> hero.isMelee</li>
<li><code>bool</code> hero.isRanged</li>
<li><code>bool</code> hero.isBot</li>
<li><code>bool</code> hero.isMe</li>
</ul><p><strong>The following functions are limited to player only:</strong><br></p><h4>player:move(v1)</h4><p>Parameters<br>
<code>hero.obj</code> player<br>
<code>vec3</code> v1<br>
Return Value<br>
<code>void</code><br></p><h4>player:attackmove(v1)</h4><p>Parameters<br>
<code>hero.obj</code> player<br>
<code>vec3</code> v1<br>
Return Value<br>
<code>void</code><br></p><h4>player:altmove(v1)</h4><p>Parameters<br>
<code>hero.obj</code> player<br>
<code>vec3</code> v1<br>
Return Value<br>
<code>void</code><br></p><h4>player:attack(obj)</h4><p>Parameters<br>
<code>hero.obj</code> player<br>
<code>obj</code> obj<br>
Return Value<br>
<code>void</code><br></p><h4>player:altattack(obj)</h4><p>Parameters<br>
<code>hero.obj</code> player<br>
<code>obj</code> obj<br>
Return Value<br>
<code>void</code><br></p><h4>player:stop()</h4><p>Parameters<br>
<code>hero.obj</code> player<br>
Return Value<br>
<code>void</code><br></p><h4>player:select(n)</h4><p>Parameters<br>
<code>hero.obj</code> player<br>
<code>number</code> index<br>
Return Value<br>
<code>void</code><br></p><h4>player:castSpell(type, slot, arg1, arg2, no_limit)</h4><p>Parameters<br>
<code>hero.obj</code> player<br>
<code>string</code> type<br>
<code>number</code> slot<br>
<code>mixed</code> arg1<br>
<code>mixed</code> arg2<br>
<code>boolean</code> no_limit: ignore the limitation check, maybe kickout/banned, use careful<br>
Return Value<br>
<code>void</code><br></p><pre><code class="lang-lua"><span class="note">--</span>type <span class="string">'pos'</span>, orianna q
<span class="hero">player</span>:castSpell<span class="include">(</span><span class="string">'pos'</span>, <span class="number">0</span>, mousePos<span class="include">)</span>
<span class="note">--</span>type <span class="string">'obj'</span>, teemo q
<span class="hero">player</span>:castSpell<span class="include">(</span><span class="string">'obj'</span>, <span class="number">0</span>, game.selectedTarget<span class="include">)</span>
<span class="note">--</span>type <span class="string">'self'</span>, teemo w
<span class="hero">player</span>:castSpell<span class="include">(</span><span class="string">'self'</span>, <span class="number">1</span><span class="include">)</span>
<span class="note">--</span>type <span class="string">'line'</span>, rumble r
<span class="hero">player</span>:castSpell<span class="include">(</span><span class="string">'line'</span>, <span class="number">3</span>, <span class="hero">player</span>.pos, mousePos<span class="include">)</span>
<span class="note">--</span>type <span class="string">'release'</span>, varus q
<span class="hero">player</span>:castSpell<span class="include">(</span><span class="string">'release'</span>, <span class="number">0</span>, <span class="hero">player</span>.pos, mousePos<span class="include">)</span>
<span class="note">--</span>type <span class="string">'move'</span>, aurelion sol q
<span class="hero">player</span>:castSpell<span class="include">(</span><span class="string">'move'</span>, <span class="number">0</span>, mousePos, nil, true<span class="include">)</span>
<span class="note">--</span>type <span class="string">'switch'</span>, Hwei Q
<span class="hero">player</span>:castSpell<span class="include">(</span><span class="string">'switch'</span>, <span class="number">0</span><span class="include">)</span></code></pre><h4>player:levelSpell(slot)</h4><p>Parameters<br>
<code>hero.obj</code> player<br>
<code>number</code> slot<br>
Return Value<br>
<code>void</code><br></p><h4>player:interact(obj)</h4><p>Parameters<br>
<code>hero.obj</code> player<br>
<code>obj</code> obj<br>
Return Value<br>
<code>void</code><br></p><h4>player:buyItem(itemID)</h4><p>Parameters<br>
<code>hero.obj</code> player<br>
<code>number</code> itemID<br>
Return Value<br>
<code>void</code><br>
<br>
<strong>The following functions are available for all hero.obj</strong><br></p><h4>hero:spellSlot(slot)</h4><p>Parameters<br>
<code>hero.obj</code> hero<br>
<code>number</code> slot<br>
Return Value<br>
<code>spell_slot.obj</code><br></p><h4>hero:findSpellSlot(spellName)</h4><p>Parameters<br>
<code>hero.obj</code> hero<br>
<code>string</code> spell name<br>
Return Value<br>
<code>spell_slot.obj</code><br></p><h4>hero:inventorySlot(slot)</h4><p>Parameters<br>
<code>hero.obj</code> hero<br>
<code>number</code> slot<br>
Return Value<br>
<code>inventory_slot.obj</code><br></p><h4>hero:basicAttack(index)</h4><p>Parameters<br>
<code>hero.obj</code> hero<br>
<code>number</code> index, -1 to get the default AA spell<br>
Return Value<br>
<code>spell.obj</code><br></p><h4>hero:itemID(slot)</h4><p>Parameters<br>
<code>hero.obj</code> hero<br>
<code>number</code> slot<br>
Return Value<br>
<code>number</code> returns item ID for item slot i<br></p><h4>hero:isPlayingAnimation(animationNameHash)</h4><p>Parameters<br>
<code>hero.obj</code> hero<br>
<code>number</code> animationNameHash<br>
Return Value<br>
<code>boolean</code><br></p><h4>hero:attackDelay()</h4><p>Parameters<br>
<code>hero.obj</code> hero<br>
Return Value<br>
<code>number</code><br></p><h4>hero:attackCastDelay(slot)</h4><p>Parameters<br>
<code>hero.obj</code> hero<br>
<code>number</code> spellSlot<br>
Return Value<br>
<code>number</code><br></p><h4>hero:getPassablePos(to)</h4><p>Parameters<br>
<code>hero.obj</code> hero<br>
<code>vec3</code> to pos<br>
Return Value<br>
<code>vec3</code> The NearstPassable position (the cell center, and include hero collsion state)<br></p><h4>hero:baseHealthForLevel(level)</h4><p>Parameters<br>
<code>number</code> level<br>
Return Value<br>
<code>number</code> returns base health for level i<br></p><h4>hero:abilityResourceBase(slot)</h4><p>Parameters<br>
<code>number</code> slot, usually the slot is 0 for mana<br>
Return Value<br>
<code>number</code> returns ability resource value (with level factor calculated)<br></p><h4>hero:abilityResourceForLevel(slot, level)</h4><p>Parameters<br>
<code>number</code> slot, usually the slot is 0 for mana<br>
<code>number</code> level<br>
Return Value<br>
<code>number</code> returns ability resource value for level<br></p><h4>hero:statForLevel(type, level)</h4><p>Parameters<br>
<code>number</code> type<br>
<code>number</code> level<br>
Return Value<br>
<code>number</code> returns stat of type for level i<br></p><h4>hero:getStat(name)</h4><p>Parameters<br>
<code>string</code> name<br>
Return Value<br>
<code>number</code> returns stat value<br></p><p>The available stats (Some of them are deprecated by riot and always zero):</p><ul>
<li>EXP    </li>
<li>GOLD_SPENT    </li>
<li>GOLD_EARNED    </li>
<li>MINIONS_KILLED    </li>
<li>NEUTRAL_MINIONS_KILLED    </li>
<li>NEUTRAL_MINIONS_KILLED_YOUR_JUNGLE    </li>
<li>NEUTRAL_MINIONS_KILLED_ENEMY_JUNGLE    </li>
<li>PLAYER_SCORE_0    </li>
<li>PLAYER_SCORE_1    </li>
<li>PLAYER_SCORE_2    </li>
<li>PLAYER_SCORE_3    </li>
<li>PLAYER_SCORE_4    </li>
<li>PLAYER_SCORE_5    </li>
<li>PLAYER_SCORE_6    </li>
<li>PLAYER_SCORE_7    </li>
<li>PLAYER_SCORE_8    </li>
<li>PLAYER_SCORE_9    </li>
<li>PLAYER_SCORE_10    </li>
<li>PLAYER_SCORE_11    </li>
<li>VICTORY_POINT_TOTAL    </li>
<li>TOTAL_DAMAGE_DEALT    </li>
<li>PHYSICAL_DAMAGE_DEALT_PLAYER    </li>
<li>MAGIC_DAMAGE_DEALT_PLAYER    </li>
<li>TRUE_DAMAGE_DEALT_PLAYER    </li>
<li>TOTAL_DAMAGE_DEALT_TO_CHAMPIONS    </li>
<li>PHYSICAL_DAMAGE_DEALT_TO_CHAMPIONS    </li>
<li>MAGIC_DAMAGE_DEALT_TO_CHAMPIONS    </li>
<li>TRUE_DAMAGE_DEALT_TO_CHAMPIONS    </li>
<li>TOTAL_DAMAGE_TAKEN    </li>
<li>PHYSICAL_DAMAGE_TAKEN    </li>
<li>MAGIC_DAMAGE_TAKEN    </li>
<li>TRUE_DAMAGE_TAKEN    </li>
<li>TOTAL_DAMAGE_SELF_MITIGATED    </li>
<li>TOTAL_DAMAGE_SHIELDED_ON_TEAMMATES    </li>
<li>TOTAL_DAMAGE_DEALT_TO_BUILDINGS    </li>
<li>TOTAL_DAMAGE_DEALT_TO_TURRETS    </li>
<li>TOTAL_DAMAGE_DEALT_TO_OBJECTIVES    </li>
<li>LARGEST_ATTACK_DAMAGE    </li>
<li>LARGEST_ABILITY_DAMAGE    </li>
<li>LARGEST_CRITICAL_STRIKE    </li>
<li>TOTAL_TIME_CROWD_CONTROL_DEALT    </li>
<li>TOTAL_TIME_CROWD_CONTROL_DEALT_TO_CHAMPIONS    </li>
<li>TOTAL_HEAL_ON_TEAMMATES    </li>
<li>TIME_PLAYED    </li>
<li>LONGEST_TIME_SPENT_LIVING    </li>
<li>TOTAL_TIME_SPENT_DEAD    </li>
<li>TIME_OF_FROM_LAST_DISCONNECT    </li>
<li>TIME_SPENT_DISCONNECTED    </li>
<li>TIME_CCING_OTHERS    </li>
<li>TEAM    </li>
<li>LEVEL    </li>
<li>CHAMPIONS_KILLED    </li>
<li>NUM_DEATHS    </li>
<li>ASSISTS    </li>
<li>LARGEST_KILLING_SPREE    </li>
<li>KILLING_SPREES    </li>
<li>LARGEST_MULTI_KILL    </li>
<li>BOUNTY_LEVEL    </li>
<li>DOUBLE_KILLS    </li>
<li>TRIPLE_KILLS    </li>
<li>QUADRA_KILLS    </li>
<li>PENTA_KILLS    </li>
<li>UNREAL_KILLS    </li>
<li>BARRACKS_KILLED    </li>
<li>BARRACKS_TAKEDOWNS    </li>
<li>TURRETS_KILLED    </li>
<li>TURRET_TAKEDOWNS    </li>
<li>HQ_KILLED    </li>
<li>HQ_TAKEDOWNS    </li>
<li>OBJECTIVES_STOLEN    </li>
<li>OBJECTIVES_STOLEN_ASSISTS    </li>
<li>FRIENDLY_DAMPEN_LOST    </li>
<li>FRIENDLY_TURRET_LOST    </li>
<li>FRIENDLY_HQ_LOST    </li>
<li>BARON_KILLS    </li>
<li>DRAGON_KILLS    </li>
<li>RIFT_HERALD_KILLS</li>
<li>HORDE_KILLS</li>
<li>ATAKHAN_KILLS</li>
<li>NODE_CAPTURE    </li>
<li>NODE_CAPTURE_ASSIST    </li>
<li>NODE_NEUTRALIZE    </li>
<li>NODE_NEUTRALIZE_ASSIST    </li>
<li>TEAM_OBJECTIVE    </li>
<li>ITEMS_PURCHASED    </li>
<li>CONSUMABLES_PURCHASED    </li>
<li>ITEM0    </li>
<li>ITEM1    </li>
<li>ITEM2    </li>
<li>ITEM3    </li>
<li>ITEM4    </li>
<li>ITEM5    </li>
<li>ITEM6    </li>
<li>PERK_PRIMARY_STYLE    </li>
<li>PERK_SUB_STYLE    </li>
<li>STAT_PERK_0    </li>
<li>STAT_PERK_1    </li>
<li>STAT_PERK_2    </li>
<li>SIGHT_WARDS_BOUGHT_IN_GAME    </li>
<li>VISION_WARDS_BOUGHT_IN_GAME    </li>
<li>WARD_PLACED    </li>
<li>WARD_KILLED    </li>
<li>WARD_PLACED_DETECTOR    </li>
<li>VISION_SCORE    </li>
<li>SPELL1_CAST    </li>
<li>SPELL2_CAST    </li>
<li>SPELL3_CAST    </li>
<li>SPELL4_CAST    </li>
<li>SUMMON_SPELL1_CAST    </li>
<li>SUMMON_SPELL2_CAST    </li>
<li>KEYSTONE_ID    </li>
<li>TOTAL_HEAL    </li>
<li>TOTAL_UNITS_HEALED    </li>
<li>WAS_AFK    </li>
<li>WAS_AFK_AFTER_FAILED_SURRENDER    </li>
<li>WAS_EARLY_SURRENDER_ACCOMPLICE    </li>
<li>TEAM_EARLY_SURRENDERED    </li>
<li>GAME_ENDED_IN_EARLY_SURRENDER    </li>
<li>GAME_ENDED_IN_SURRENDER    </li>
<li>WAS_SURRENDER_DUE_TO_AFK    </li>
<li>WAS_LEAVER    </li>
<li>PLAYERS_I_MUTED    </li>
</ul><h4>hero:isValidTarget(range)</h4><p>Parameters<br>
<code>hero.obj</code> hero<br>
<code>number</code> range, optional <br>
Return Value<br>
<code>bool</code> Returns whether this is valid target to self or not<br></p><h4>hero:findBuff(hash)</h4><p>Equal to <code>player.buff["some_name"] and player.buff["some_name"] or nil</code> <br>
Parameters<br>
<code>hero.obj</code> hero<br>
<code>int</code> fnv hash of buff name<br>
Return Value<br>
<code>buff.obj</code> <br></p><h4>hero:getBuffStacks(hash)</h4><p>Equal to <code>player.buff["some_name"] and player.buff["some_name"].stacks or 0</code> <br>
Parameters<br>
<code>hero.obj</code> hero<br>
<code>int</code> fnv hash of buff name<br>
Return Value<br>
<code>int</code> <br></p><h4>hero:getBuffCount(hash)</h4><p>Equal to <code>player.buff["some_name"] and player.buff["some_name"].stacks2 or 0</code> <br>
Parameters<br>
<code>hero.obj</code> hero<br>
<code>int</code> fnv hash of buff name<br>
Return Value<br>
<code>int</code> <br></p><pre><code class="lang-lua"><span class="local">local</span> ezrealpassivestacks = game.fnvhash<span class="include">(</span><span class="string">"ezrealpassivestacks"</span><span class="include">)</span>
<span class="cb">cb</span>.<span class="add">add</span><span class="include">(</span><span class="cb">cb</span>.<span class="cbfunc">tick</span>, <span class="keyword">function</span><span class="include">(</span><span class="include">)</span>
    <span class="local">local</span> stacks = <span class="hero">player</span>:getBuffStacks<span class="include">(</span>ezrealpassivestacks<span class="include">)</span>
    <span class="keyword">if</span> stacks &gt; <span class="number">0</span> <span class="keyword">then</span>
        <span class="note">--</span> <span class="keyword">do</span> somethings
    <span class="keyword">end</span>
<span class="keyword">end</span><span class="include">)</span></code></pre><h4>hero:getTeamBuffCount(team, buffType)</h4><p>Parameters<br>
<code>hero.obj</code> hero<br>
<code>int</code> team<br>
<code>int</code> buffType<br>
Return Value<br>
<code>int</code> <br></p><p>The available buffType:
 <em> DragonFireStacks = 0x1,
 </em> DragonAirStacks = 0x2,
 <em> DragonWaterStacks = 0x3,
 </em> DragonEarthStacks = 0x4,
 * DragonElderStacks = 0x5,</p><pre><code class="lang-lua"><span class="print">print</span><span class="include">(</span><span class="hero">player</span>:getTeamBuffCount<span class="include">(</span>TEAM_ALLY, <span class="number">0</span><span class="include">)</span><span class="include">)</span>
<span class="print">print</span><span class="include">(</span><span class="hero">player</span>:getTeamBuffCount<span class="include">(</span>TEAM_ENEMY, <span class="number">0</span><span class="include">)</span><span class="include">)</span></code></pre><h3 id="objects-minionobj">minion.obj</h3><p>Properties:<br></p><ul>
<li><code>string</code> minion.name</li>
<li><code>string</code> minion.charName</li>
<li><code>boolean</code> minion.isOnScreen</li>
<li><code>boolean</code> minion.isDead</li>
<li><code>boolean</code> minion.isAlive</li>
<li><code>boolean</code> minion.isVisible</li>
<li><code>boolean</code> minion.isTargetable</li>
<li><code>vec3</code> minion.pos</li>
<li><code>vec3</code> minion.direction</li>
<li><code>vec2</code> minion.pos2D</li>
<li><code>vec2</code> minion.direction2D</li>
<li><code>vec2</code> minion.barPos</li>
<li><code>path.obj</code> minion.path</li>
<li><code>hero.obj</code> minion.owner</li>
<li><code>spell.obj</code> minion.activeSpell</li>
<li><code>table</code> minion.buff</li>
<li><code>number</code> minion.networkID</li>
<li><code>number</code> minion.networkID32</li>
<li><code>number</code> minion.team</li>
<li><code>number</code> minion.x</li>
<li><code>number</code> minion.y</li>
<li><code>number</code> minion.z</li>
<li><code>number</code> minion.selectionHeight</li>
<li><code>number</code> minion.selectionRadius</li>
<li><code>number</code> minion.boundingRadius</li>
<li><code>number</code> minion.overrideCollisionRadius</li>
<li><code>number</code> minion.pathfindingCollisionRadius</li>
<li><code>vec3</code> minion.minBoundingBox</li>
<li><code>vec3</code> minion.maxBoundingBox</li>
<li><code>number</code> minion.deathTime</li>
<li><code>number</code> minion.health</li>
<li><code>number</code> minion.maxHealth</li>
<li><code>number</code> minion.mana</li>
<li><code>number</code> minion.maxMana</li>
<li><code>number</code> minion.maxHealthPenalty</li>
<li><code>number</code> minion.physicalShield</li>
<li><code>number</code> minion.magicalShield</li>
<li><code>number</code> minion.allShield</li>
<li><code>number</code> minion.stopShieldFade</li>
<li><code>number</code> minion.isTargetableToTeamFlags</li>
<li><code>number</code> minion.actionState</li>
<li><code>number</code> minion.baseAttackDamage</li>
<li><code>number</code> minion.baseAd<blockquote>
<p>alias of <code>baseAttackDamage</code></p>
</blockquote>
</li>
<li><code>number</code> minion.bonusAd</li>
<li><code>number</code> minion.totalAd<blockquote>
<p>The TotalAttackDamage</p>
</blockquote>
</li>
<li><code>number</code> minion.totalAp<blockquote>
<p>The TotalAbilityPower</p>
</blockquote>
</li>
<li><code>number</code> minion.armor</li>
<li><code>number</code> minion.spellBlock</li>
<li><code>number</code> minion.attackSpeedMod</li>
<li><code>number</code> minion.flatPhysicalDamageMod</li>
<li><code>number</code> minion.percentPhysicalDamageMod</li>
<li><code>number</code> minion.flatMagicDamageMod</li>
<li><code>number</code> minion.percentMagicDamageMod</li>
<li><code>number</code> minion.healthRegenRate</li>
<li><code>number</code> minion.bonusArmor</li>
<li><code>number</code> minion.bonusSpellBlock</li>
<li><code>number</code> minion.moveSpeed</li>
<li><code>number</code> minion.moveSpeedBaseIncrease</li>
<li><code>number</code> minion.baseAbilityDamage</li>
<li><code>number</code> minion.attackRange</li>
<li><code>number</code> minion.flatMagicReduction</li>
<li><code>number</code> minion.percentMagicReduction</li>
<li><code>number</code> minion.flatArmorPenetration</li>
<li><code>number</code> minion.percentArmorPenetration</li>
<li><code>number</code> minion.flatMagicPenetration</li>
<li><code>number</code> minion.percentMagicPenetration</li>
<li><code>number</code> minion.physicalLethality</li>
<li><code>number</code> minion.magicLethality</li>
<li><code>number</code> minion.flatBaseAttackDamageMod</li>
<li><code>number</code> minion.percentBaseAttackDamageMod</li>
<li><code>number</code> minion.baseAttackDamageSansPercentScale</li>
<li><code>number</code> minion.exp</li>
<li><code>number</code> minion.par</li>
<li><code>number</code> minion.maxPar</li>
<li><code>number</code> minion.parEnabled</li>
<li><code>number</code> minion.percentDamageToBarracksMinionMod</li>
<li><code>number</code> minion.flatDamageReductionFromBarracksMinionMod</li>
<li><code>bool</code> minion.isMelee</li>
<li><code>bool</code> minion.isRanged</li>
<li><code>bool</code> minion.isClone</li>
<li><code>bool</code> minion.isLaneMinion</li>
<li><code>bool</code> minion.isEliteMinion</li>
<li><code>bool</code> minion.isEpicMinion</li>
<li><code>bool</code> minion.isJungleMonster</li>
<li><code>bool</code> minion.isLaneMinion</li>
<li><code>bool</code> minion.isSiegeMinion</li>
<li><code>bool</code> minion.isSuperMinion</li>
<li><code>bool</code> minion.isCasterdMinion</li>
<li><code>bool</code> minion.isMeleeMinion</li>
<li><code>bool</code> minion.isBarrel</li>
<li><code>bool</code> minion.isPet</li>
<li><code>bool</code> minion.isCollidablePet</li>
<li><code>bool</code> minion.isTrap</li>
<li><code>bool</code> minion.isWard</li>
<li><code>bool</code> minion.isWardNoBlue</li>
<li><code>bool</code> minion.isPlant</li>
<li><code>bool</code> minion.isMonster</li>
<li><code>bool</code> minion.isLargeMonster</li>
<li><code>bool</code> minion.isBaron</li>
<li><code>bool</code> minion.isDragon</li>
<li><code>bool</code> minion.isEpicMonster</li>
<li><code>bool</code> minion.isSmiteMonster</li>
</ul><h4>m:basicAttack(index)</h4><p>Parameters<br>
<code>minion.obj</code> m<br>
<code>number</code> index, -1 to get the default AA spell<br>
Return Value<br>
<code>spell.obj</code> returns the minions basic attack<br></p><h4>m:isPlayingAnimation(animationNameHash)</h4><p>Parameters<br>
<code>minion.obj</code> m<br>
<code>number</code> animationNameHash<br>
Return Value<br>
<code>boolean</code><br></p><h4>m:attackDelay()</h4><p>Parameters<br>
<code>minion.obj</code> m<br>
Return Value<br>
<code>number</code><br></p><h4>m:attackCastDelay(slot)</h4><p>Parameters<br>
<code>minion.obj</code> m<br>
<code>number</code> spellSlot<br>
Return Value<br>
<code>number</code><br></p><h4>m:isValidTarget(range)</h4><p>Parameters<br>
<code>m.obj</code> m<br>
<code>number</code> range, optional <br>
Return Value<br>
<code>bool</code> Returns whether this is valid target to self or not<br></p><h3 id="objects-turretobj">turret.obj</h3><p>Properties:<br></p><ul>
<li><code>string</code> turret.name</li>
<li><code>string</code> turret.charName</li>
<li><code>boolean</code> turret.isOnScreen</li>
<li><code>boolean</code> turret.isDead</li>
<li><code>boolean</code> turret.isAlive</li>
<li><code>boolean</code> turret.isVisible</li>
<li><code>boolean</code> turret.isTargetable</li>
<li><code>vec3</code> turret.pos</li>
<li><code>vec3</code> turret.direction</li>
<li><code>vec2</code> turret.pos2D</li>
<li><code>vec2</code> turret.barPos</li>
<li><code>vec2</code> turret.direction2D</li>
<li><code>path.obj</code> turret.path</li>
<li><code>spell.obj</code> turret.activeSpell</li>
<li><code>table</code> turret.buff</li>
<li><code>number</code> turret.networkID</li>
<li><code>number</code> turret.networkID32</li>
<li><code>number</code> turret.team</li>
<li><code>number</code> turret.x</li>
<li><code>number</code> turret.y</li>
<li><code>number</code> turret.z</li>
<li><code>number</code> turret.selectionHeight</li>
<li><code>number</code> turret.selectionRadius</li>
<li><code>number</code> turret.boundingRadius</li>
<li><code>number</code> turret.overrideCollisionRadius</li>
<li><code>number</code> turret.pathfindingCollisionRadius</li>
<li><code>vec3</code> turret.minBoundingBox</li>
<li><code>vec3</code> turret.maxBoundingBox</li>
<li><code>number</code> turret.deathTime</li>
<li><code>number</code> turret.health</li>
<li><code>number</code> turret.maxHealth</li>
<li><code>number</code> turret.mana</li>
<li><code>number</code> turret.maxMana</li>
<li><code>number</code> turret.allShield</li>
<li><code>number</code> turret.isTargetableToTeamFlags</li>
<li><code>number</code> turret.baseAttackDamage</li>
<li><code>number</code> turret.baseAd<blockquote>
<p>alias of <code>baseAttackDamage</code></p>
</blockquote>
</li>
<li><code>number</code> turret.bonusAd</li>
<li><code>number</code> turret.totalAd<blockquote>
<p>The TotalAttackDamage</p>
</blockquote>
</li>
<li><code>number</code> turret.totalAp<blockquote>
<p>The TotalAbilityPower</p>
</blockquote>
</li>
<li><code>number</code> turret.armor</li>
<li><code>number</code> turret.spellBlock</li>
<li><code>number</code> turret.attackSpeedMod</li>
<li><code>number</code> turret.flatPhysicalDamageMod</li>
<li><code>number</code> turret.percentPhysicalDamageMod</li>
<li><code>number</code> turret.flatMagicDamageMod</li>
<li><code>number</code> turret.percentMagicDamageMod</li>
<li><code>number</code> turret.healthRegenRate</li>
<li><code>number</code> turret.bonusArmor</li>
<li><code>number</code> turret.bonusSpellBlock</li>
<li><code>number</code> turret.baseAbilityDamage</li>
<li><code>number</code> turret.parRegenRate</li>
<li><code>number</code> turret.attackRange</li>
<li><code>number</code> turret.flatMagicReduction</li>
<li><code>number</code> turret.percentMagicReduction</li>
<li><code>number</code> turret.flatArmorPenetration</li>
<li><code>number</code> turret.percentArmorPenetration</li>
<li><code>number</code> turret.flatMagicPenetration</li>
<li><code>number</code> turret.percentMagicPenetration</li>
<li><code>number</code> turret.percentBonusArmorPenetration</li>
<li><code>number</code> turret.percentBonusMagicPenetration</li>
<li><code>number</code> turret.physicalLethality</li>
<li><code>number</code> turret.magicLethality</li>
<li><code>number</code> turret.baseHealthRegenRate</li>
<li><code>number</code> turret.secondaryARBaseRegenRateRep</li>
<li><code>number</code> turret.flatBaseAttackDamageMod</li>
<li><code>number</code> turret.percentBaseAttackDamageMod</li>
<li><code>number</code> turret.baseAttackDamageSansPercentScale</li>
<li><code>number</code> turret.exp</li>
<li><code>number</code> turret.par</li>
<li><code>number</code> turret.maxPar</li>
<li><code>number</code> turret.parEnabled</li>
<li><code>number</code> turret.physicalDamagePercentageModifier</li>
<li><code>number</code> turret.magicalDamagePercentageModifier</li>
<li><code>bool</code> turret.isMelee</li>
<li><code>bool</code> turret.isRanged</li>
<li><code>number</code> turret.tier<blockquote>
<p>Base=1, Inner=2, Outer=3, NexusRight=4, NexusLeft=5</p>
</blockquote>
</li>
<li><code>number</code> turret.lane<blockquote>
<p>Bottom=0, Mid=1, Top=2</p>
</blockquote>
</li>
</ul><h4>t:basicAttack(i)</h4><p>Parameters<br>
<code>turret.obj</code> t<br>
<code>number</code> i<br>
Return Value<br>
<code>spell.obj</code> returns the turrets basic attack<br></p><h4>t:isPlayingAnimation(animationNameHash)</h4><p>Parameters<br>
<code>turret.obj</code> t<br>
<code>number</code> animationNameHash<br>
Return Value<br>
<code>boolean</code><br></p><h4>t:attackDelay()</h4><p>Parameters<br>
<code>turret.obj</code> t<br>
Return Value<br>
<code>number</code><br></p><h4>t:attackCastDelay(slot)</h4><p>Parameters<br>
<code>turret.obj</code> t<br>
<code>number</code> spellSlot<br>
Return Value<br>
<code>number</code><br></p><h4>t:isValidTarget(range)</h4><p>Parameters<br>
<code>turret.obj</code> t<br>
<code>number</code> range, optional <br>
Return Value<br>
<code>bool</code> Returns whether this is valid target to self or not<br></p><h3 id="objects-inhibobj">inhib.obj</h3><p>Properties:<br></p><ul>
<li><code>number</code> inhib.type</li>
<li><code>number</code> inhib.index</li>
<li><code>number</code> inhib.team</li>
<li><code>number</code> inhib.networkID</li>
<li><code>number</code> inhib.networkID32</li>
<li><code>string</code> inhib.name</li>
<li><code>number</code> inhib.x</li>
<li><code>number</code> inhib.y</li>
<li><code>number</code> inhib.z</li>
<li><code>vec3</code> inhib.pos</li>
<li><code>vec2</code> inhib.pos2D</li>
<li><code>boolean</code> inhib.isOnScreen</li>
<li><code>number</code> inhib.selectionHeight</li>
<li><code>number</code> inhib.selectionRadius</li>
<li><code>number</code> inhib.boundingRadius</li>
<li><code>number</code> inhib.overrideCollisionRadius</li>
<li><code>number</code> inhib.pathfindingCollisionRadius</li>
<li><code>vec3</code> inhib.minBoundingBox</li>
<li><code>vec3</code> inhib.maxBoundingBox</li>
<li><code>boolean</code> inhib.isDead</li>
<li><code>boolean</code> inhib.isAlive</li>
<li><code>boolean</code> inhib.isVisible</li>
<li><code>number</code> inhib.deathTime</li>
<li><code>number</code> inhib.health</li>
<li><code>number</code> inhib.maxHealth</li>
<li><code>number</code> inhib.allShield</li>
<li><code>boolean</code> inhib.isTargetable</li>
<li><code>number</code> inhib.isTargetableToTeamFlags</li>
</ul><h4>inhib:isValidTarget(range)</h4><p>Parameters<br>
<code>inhib.obj</code> inhib<br>
<code>number</code> range, optional <br>
Return Value<br>
<code>bool</code> Returns whether this is valid target to self or not<br></p><h3 id="objects-nexusobj">nexus.obj</h3><p>Properties:<br></p><ul>
<li><code>string</code> nexus.name</li>
<li><code>boolean</code> nexus.isOnScreen</li>
<li><code>boolean</code> nexus.isDead</li>
<li><code>boolean</code> nexus.isAlive</li>
<li><code>boolean</code> nexus.isVisible</li>
<li><code>boolean</code> nexus.isTargetable</li>
<li><code>vec3</code> nexus.pos</li>
<li><code>vec2</code> nexus.pos2D</li>
<li><code>number</code> nexus.team</li>
<li><code>number</code> nexus.type</li>
<li><code>number</code> nexus.x</li>
<li><code>number</code> nexus.y</li>
<li><code>number</code> nexus.z</li>
<li><code>number</code> nexus.health</li>
<li><code>number</code> nexus.maxHealth</li>
<li><code>number</code> nexus.allShield</li>
<li><code>number</code> nexus.isTargetableToTeamFlags</li>
</ul><h4>nexus:isValidTarget(range)</h4><p>Parameters<br>
<code>nexus.obj</code> nexus<br>
<code>number</code> range, optional <br>
Return Value<br>
<code>bool</code> Returns whether this is valid target to self or not<br></p><h3 id="objects-missileobj">missile.obj</h3><p>Properties:<br></p><ul>
<li><code>number</code> missile.type</li>
<li><code>number</code> missile.index</li>
<li><code>number</code> missile.index32</li>
<li><code>number</code> missile.team</li>
<li><code>string</code> missile.name</li>
<li><code>number</code> missile.networkID</li>
<li><code>number</code> missile.networkID32</li>
<li><code>boolean</code> missile.isOnScreen</li>
<li><code>number</code> missile.selectionHeight</li>
<li><code>number</code> missile.selectionRadius</li>
<li><code>vec3</code> missile.minBoundingBox</li>
<li><code>vec3</code> missile.maxBoundingBox</li>
<li><code>number</code> missile.x</li>
<li><code>number</code> missile.y</li>
<li><code>number</code> missile.z</li>
<li><code>vec3</code> missile.pos</li>
<li><code>vec2</code> missile.pos2D<blockquote>
<p>The <code>x</code>/<code>y</code>/<code>z</code>/<code>pos</code>/<code>pos2D</code> now means <code>minBoundingBox</code> of <code>GameObject</code></p>
</blockquote>
</li>
<li><code>spell.obj</code> missile.spell</li>
<li><code>base.obj</code> missile.caster</li>
<li><code>base.obj</code> missile.target</li>
<li><code>number</code> missile.width</li>
<li><code>number</code> missile.velocity</li>
<li><code>missile_info.obj</code> missile.missile_info<blockquote>
<p>The <code>MissileMovement</code> of <code>Missile</code>, And follow properties is getting from this as a alias: </p>
</blockquote>
</li>
<li><code>vec3</code> missile.startPos</li>
<li><code>vec2</code> missile.startPos2D</li>
<li><code>vec3</code> missile.endPos</li>
<li><code>vec2</code> missile.endPos2D</li>
<li><code>number</code> missile.speed</li>
</ul><h3 id="objects-particleobj">particle.obj</h3><p>Properties:<br></p><ul>
<li><code>number</code> particle.type</li>
<li><code>number</code> particle.index</li>
<li><code>number</code> particle.index32</li>
<li><code>number</code> particle.team</li>
<li><code>string</code> particle.name</li>
<li><code>number</code> particle.networkID</li>
<li><code>number</code> particle.networkID32</li>
<li><code>number</code> particle.x</li>
<li><code>number</code> particle.y</li>
<li><code>number</code> particle.z</li>
<li><code>number</code> particle.effectKey<blockquote>
<p>The hash of effect resource name, only valid for effects from spell (invalid for missile/sound/etc)</p>
</blockquote>
</li>
<li><code>vec3</code> particle.pos</li>
<li><code>vec2</code> particle.pos2D</li>
<li><code>vec3</code> particle.initPos<blockquote>
<p>The initial position when effect created, only valid for effects from spell</p>
</blockquote>
</li>
<li><code>vec3</code> particle.initTargetPos<blockquote>
<p>The initial target position when effect created, only valid for effects from spell</p>
</blockquote>
</li>
<li><code>vec3</code> particle.initOrientation<blockquote>
<p>The initial orientation when effect created, only valid for effects from spell</p>
</blockquote>
</li>
<li><code>vec3</code> particle.direction<blockquote>
<p>The effect drection when rendering.</p>
</blockquote>
</li>
<li><code>boolean</code> particle.isOnScreen</li>
<li><code>number</code> particle.selectionHeight</li>
<li><code>number</code> particle.selectionRadius</li>
<li><code>vec3</code> particle.minBoundingBox</li>
<li><code>vec3</code> particle.maxBoundingBox</li>
<li><code>obj</code> particle.caster<blockquote>
<p>The initial caster/emitter when effect created, only valid for effects from spell, could be nil</p>
</blockquote>
</li>
<li><code>obj</code> particle.target<blockquote>
<p>The initial target when effect created, only valid for effects from spell, could be nil</p>
</blockquote>
</li>
<li><code>obj</code> particle.attachmentObject</li>
<li><code>obj</code> particle.targetAttachmentObject</li>
</ul><h3 id="objects-spellobj">spell.obj</h3><blockquote>
<p>known as <code>SpellCastInfo</code></p>
</blockquote><p>Properties:<br></p><ul>
<li><code>number</code> spell.identifier<blockquote>
<p>The <code>missileNetworkID</code> of <code>SpellCastInfo</code></p>
</blockquote>
</li>
<li><code>number</code> spell.slot</li>
<li><code>boolean</code> spell.isBasicAttack</li>
<li><code>obj</code> spell.owner</li>
<li><code>boolean</code> spell.hasTarget</li>
<li><code>obj</code> spell.target</li>
<li><code>vec3</code> spell.startPos</li>
<li><code>vec2</code> spell.startPos2D<blockquote>
<p><code>LaunchPosition</code></p>
</blockquote>
</li>
<li><code>vec3</code> spell.endPos</li>
<li><code>vec2</code> spell.endPos2D<blockquote>
<p><code>TargetPosition</code></p>
</blockquote>
</li>
<li><code>vec3</code> spell.endPosLine</li>
<li><code>vec2</code> spell.endPosLine2D<blockquote>
<p><code>TargetEndPosition</code></p>
</blockquote>
</li>
<li><code>boolean</code> spell.useChargeChanneling</li>
<li><code>boolean</code> spell.channelingFinished</li>
<li><code>boolean</code> spell.spellCasted</li>
<li><code>number</code> spell.windUpTime<blockquote>
<p><code>DesignerCastTime</code> + <code>ExtraTimeForCast</code></p>
</blockquote>
</li>
<li><code>number</code> spell.animationTime<blockquote>
<p><code>DesignerTotalTime</code></p>
</blockquote>
</li>
<li><code>number</code> spell.clientWindUpTime<blockquote>
<p><code>CharacterAttackCastDelay</code></p>
</blockquote>
</li>
<li><code>number</code> spell.startTime<blockquote>
<p><code>TimeTillPlayAnimation</code></p>
</blockquote>
</li>
<li><code>number</code> spell.castEndTime<blockquote>
<p><code>TimeCast</code></p>
</blockquote>
</li>
<li><code>number</code> spell.endTime<blockquote>
<p><code>TimeSpellEnd</code></p>
</blockquote>
</li>
<li><code>spell_info.obj</code> spell.spell_info<blockquote>
<p>The <code>SpellData</code> of current <code>SpellCastInfo</code></p>
</blockquote>
</li>
<li><code>string</code> spell.name<blockquote>
<p>equal to <code>spell.spell_info.name</code></p>
</blockquote>
</li>
<li><code>string</code> spell.hash<blockquote>
<p>equal to <code>spell.spell_info.hash</code></p>
</blockquote>
</li>
<li><code>spell_static.obj</code> spell.static<blockquote>
<p>equal to <code>spell.spell_info.static</code></p>
</blockquote>
</li>
</ul><h3 id="objects-spell_slotobj">spell_slot.obj</h3><blockquote>
<p>known as <code>SpellDataInst</code></p>
</blockquote><p>Properties:<br></p><ul>
<li><code>number</code> spell_slot.slot</li>
<li><code>boolean</code> spell_slot.isBasicSpellSlot</li>
<li><code>boolean</code> spell_slot.isSummonerSpellSlot</li>
<li><code>string</code> spell_slot.name</li>
<li><code>number</code> spell_slot.hash</li>
<li><code>texture.obj</code> spell_slot.icon</li>
<li><code>number</code> spell_slot.targetingType</li>
<li><code>number</code> spell_slot.level</li>
<li><code>number</code> spell_slot.cooldown</li>
<li><code>number</code> spell_slot.totalCooldown</li>
<li><code>number</code> spell_slot.iconUsed<blockquote>
<p>For champion like Bel’Veth, the index of spell icon (Q Spell)</p>
</blockquote>
</li>
<li><code>number</code> spell_slot.startTimeForCurrentCast</li>
<li><code>number</code> spell_slot.displayRange</li>
<li><code>number</code> spell_slot.currentNumericalDisplay</li>
<li><code>number</code> spell_slot.stacks<blockquote>
<p><code>CurrentAmmo</code></p>
</blockquote>
</li>
<li><code>number</code> spell_slot.stacksCooldown<blockquote>
<p><code>TimeForNextAmmoRecharge</code></p>
</blockquote>
</li>
<li><code>number</code> spell_slot.state</li>
<li><code>number</code> spell_slot.toggleState</li>
<li><code>boolean</code> spell_slot.isCharging</li>
<li><code>boolean</code> spell_slot.isNotEmpty<blockquote>
<p>Whether current <code>spell_slot</code> has a active <code>spell_info</code> </p>
</blockquote>
</li>
<li><code>spell_info.obj</code> spell_slot.spell_info</li>
<li><code>spell_static.obj</code> spell_slot.static</li>
</ul><h4>spell_slot:getTooltip(type)</h4><p>Parameters<br>
<code>spell_slot.obj</code><br>
<code>int</code> tooltip type: 0<br>
Return Value<br>
<code>string</code><br></p><h4>spell_slot:getTooltipVar(index)</h4><p>Return the numerical variable when mouseover the skill icons<br>
Parameters<br>
<code>spell_slot.obj</code><br>
<code>int</code> tooltip var index: [0,15]<br>
Return Value<br>
<code>number</code><br></p><h4>spell_slot:getEffectAmount(index, level)</h4><p>Return the fixed class value of the target skill<br>
Parameters<br>
<code>spell_slot.obj</code><br>
<code>int</code> index: [1,11]<br>
<code>int</code> level: [0,6]<br>
Return Value<br>
<code>number</code><br></p><h4>spell_slot:calculate(spellNameHash, calculationHash)</h4><p>Parameters<br>
<code>spell_slot.obj</code> current spell<br>
<code>unsigned int</code> spell name hash, could be 0 if using default spell name<br>
<code>unsigned int</code> calculation type hash<br>
Return Value<br>
<code>void</code><br></p><pre><code>-- Jhin Example: 
-- https://raw.communitydragon.org/13.15/game/data/characters/jhin/jhin.bin.json
-- JhinW: ["Characters/Jhin/Spells/JhinRAbility/JhinW"].mSpell.mSpellCalculations
-- JhinR: ["Characters/Jhin/Spells/JhinRAbility/JhinR"].mSpell.mSpellCalculations

local calcHashW = game.fnvhash('TotalDamage')
local rawDamageW = player:spellSlot(0):calculate(0, calcHashW)

local calcHashR = game.fnvhash('DamageCalc')
local rawDamageR = player:spellSlot(3):calculate(0, calcHashR)


-- AatroxQ Example:
-- The `spellSlot(0).name` could be "AatroxQ"/"AatroxQ2"/"AatroxQ3"
-- We need use "AatroxQ" to calculate the damage.

local QDamage = game.fnvhash('QDamage')
local AatroxQ = game.spellhash('AatroxQ')
local rawDamageQ1 = player:spellSlot(0):calculate(AatroxQ, QDamage)
local rawDamageQ2 = player:spellSlot(0):calculate(AatroxQ, QDamage) * 1.25
local rawDamageQ3 = player:spellSlot(0):calculate(AatroxQ, QDamage) * 1.25 * 1.25</code></pre><h4>spell_slot:getDamage(target, spellHash, stage)</h4><p>Parameters<br>
<code>spell_slot.obj</code> current spell<br>
<code>int</code> spell name hash, could be 0 if using default spell name<br>
<code>int</code> stage, 0 for default, 1/2/3 for custom usage<br>
Return Value<br>
<code>void</code><br></p><pre><code class="lang-lua"><span class="note">--</span> AatroxQ
<span class="local">local</span> spellHash_AatroxQ = game.spellhash<span class="include">(</span><span class="string">'AatroxQ'</span><span class="include">)</span>
<span class="local">local</span> spellHash_AatroxQ<span class="number">2</span> = game.spellhash<span class="include">(</span><span class="string">'AatroxQ<span class="number">2</span>'</span><span class="include">)</span>
<span class="local">local</span> <span class="cbfunc">spell</span> = <span class="hero">player</span>:spellSlot<span class="include">(</span>_Q<span class="include">)</span>
<span class="print">print</span><span class="include">(</span><span class="cbfunc">spell</span>:getDamage<span class="include">(</span>target, spellHash_AatroxQ, <span class="number">0</span><span class="include">)</span><span class="include">)</span> <span class="note">--</span> Q damage
<span class="print">print</span><span class="include">(</span><span class="cbfunc">spell</span>:getDamage<span class="include">(</span>target, spellHash_AatroxQ, <span class="number">1</span><span class="include">)</span><span class="include">)</span> <span class="note">--</span> Q edge damage
<span class="print">print</span><span class="include">(</span><span class="cbfunc">spell</span>:getDamage<span class="include">(</span>target, spellHash_AatroxQ<span class="number">2</span>, <span class="number">0</span><span class="include">)</span><span class="include">)</span> <span class="note">--</span> Q<span class="number">2</span> damage <span class="include">(</span>need pass AatroxQ<span class="number">2</span> as arg<span class="number">1</span><span class="include">)</span>

<span class="note">--</span> AkaliP
<span class="local">local</span> spellHash_AkaliP = game.spellhash<span class="include">(</span><span class="string">'AkaliP'</span><span class="include">)</span>
<span class="local">local</span> <span class="cbfunc">spell</span> = <span class="hero">player</span>:spellSlot<span class="include">(</span><span class="number">63</span><span class="include">)</span> <span class="note">--</span> <span class="number">63</span> = Passive
<span class="print">print</span><span class="include">(</span><span class="cbfunc">spell</span>:getDamage<span class="include">(</span>target, spellHash_AkaliP, <span class="number">3</span><span class="include">)</span><span class="include">)</span> <span class="note">--</span> Akali passive damage <span class="keyword">for</span> stage <span class="number">3</span>

<span class="note">--</span> AurelionSolR
<span class="local">local</span> spellHash_AurelionSolR = game.spellhash<span class="include">(</span><span class="string">'AurelionSolR'</span><span class="include">)</span>
<span class="local">local</span> <span class="cbfunc">spell</span> = <span class="hero">player</span>:spellSlot<span class="include">(</span>_R<span class="include">)</span>
<span class="print">print</span><span class="include">(</span><span class="cbfunc">spell</span>:getDamage<span class="include">(</span>target, spellHash_AurelionSolR, <span class="number">0</span><span class="include">)</span><span class="include">)</span> <span class="note">--</span> AurelionSol R damage
<span class="print">print</span><span class="include">(</span><span class="cbfunc">spell</span>:getDamage<span class="include">(</span>target, spellHash_AurelionSolR, <span class="number">1</span><span class="include">)</span><span class="include">)</span> <span class="note">--</span> AurelionSol R<span class="number">2</span> damage</code></pre><h3 id="objects-spell_infoobj">spell_info.obj</h3><blockquote>
<p>known as <code>SpellData</code></p>
</blockquote><p>Properties:<br></p><ul>
<li><code>string</code> spell_info.name</li>
<li><code>number</code> spell_info.hash<blockquote>
<p>the fnvhash of name</p>
</blockquote>
</li>
<li><code>spell_static.obj</code> spell_info.static</li>
</ul><h3 id="objects-spell_staticobj">spell_static.obj</h3><blockquote>
<p>known as <code>SpellDataResource</code></p>
</blockquote><p>Properties:<br></p><ul>
<li><code>string</code> spell_static.alternateName</li>
<li><code>string</code> spell_static.imgIconName</li>
<li><code>number</code> spell_static.castType<blockquote>
<p><code>Flags</code></p>
</blockquote>
</li>
<li><code>boolean</code> spell_static.useMinimapTargeting</li>
<li><code>number</code> spell_static.missileSpeed</li>
<li><code>number</code> spell_static.lineWidth</li>
<li><code>number</code> spell_static.castFrame</li>
</ul><h3 id="objects-inventory_slotobj">inventory_slot.obj</h3><p>Properties:<br></p><ul>
<li><code>number</code> inventory_slot.stacks</li>
<li><code>number</code> inventory_slot.purchaseTime</li>
<li><code>boolean</code> inventory_slot.hasItem</li>
<li><code>item_data.obj</code> inventory_slot.item</li>
<li><code>texture.obj</code> inventory_slot.icon</li>
<li><code>number</code> inventory_slot.spellStacks</li>
<li><code>number</code> inventory_slot.id</li>
<li><code>number</code> inventory_slot.maxStacks</li>
<li><code>number</code> inventory_slot.cost</li>
<li><code>string</code> inventory_slot.name</li>
<li><code>string</code> inventory_slot.iconName</li>
<li><code>texture.obj</code> inventory_slot.icon</li>
</ul><h4>inventory_slot:getTooltip(type)</h4><p>Parameters<br>
<code>inventory_slot.obj</code> current inventory<br>
<code>int</code> tooltip type: 0<br>
Return Value<br>
<code>string</code><br></p><h4>inventory_slot:calculate(calculationHash)</h4><p>Parameters<br>
<code>inventory_slot.obj</code> current inventory<br>
<code>unsigned int</code> calculation type hash<br>
Return Value<br>
<code>void</code><br></p><h3 id="objects-item_dataobj">item_data.obj</h3><p>Properties:<br></p><ul>
<li><code>number</code> item_data.id</li>
<li><code>number</code> item_data.maxStacks</li>
<li><code>number</code> item_data.cost</li>
<li><code>string</code> item_data.name</li>
<li><code>string</code> item_data.iconName</li>
<li><code>texture.obj</code> item_data.icon</li>
</ul><h3 id="objects-pathobj">path.obj</h3><p>Properties:<br></p><ul>
<li><code>obj</code> path.owner</li>
<li><code>boolean</code> path.isActive</li>
<li><code>boolean</code> path.isMoving<blockquote>
<p>Sometimes player is moving but <code>path.isActive</code> will be <code>false</code> (eg: no path for short move).</p>
</blockquote>
</li>
<li><code>boolean</code> path.isDashing</li>
<li><code>number</code> path.dashSpeed</li>
<li><code>boolean</code> path.unstoppable</li>
<li><code>vec3</code> path.endPos</li>
<li><code>vec2</code> path.endPos2D</li>
<li><code>vec3</code> path.serverPos</li>
<li><code>vec2</code> path.serverPos2D</li>
<li><code>vec3</code> path.serverVelocity</li>
<li><code>vec2</code> path.serverVelocity2D</li>
<li><code>vec3</code> path.startPoint</li>
<li><code>vec3</code> path.endPoint</li>
<li><code>vec3[]</code> path.point<blockquote>
<p><code>WaypointList</code> array</p>
</blockquote>
</li>
<li><code>vec2[]</code> path.point2D<blockquote>
<p><code>WaypointList</code> array</p>
</blockquote>
</li>
<li><code>number</code> path.index<blockquote>
<p>The index of next point</p>
</blockquote>
</li>
<li><code>number</code> path.count<blockquote>
<p>The count of <code>point</code>/<code>point2D</code></p>
</blockquote>
</li>
<li><code>number</code> path.update<blockquote>
<p><code>UpdateNumber</code></p>
</blockquote>
</li>
</ul><pre><code class="lang-lua"><span class="cb">cb</span>.<span class="add">add</span><span class="include">(</span><span class="cb">cb</span>.<span class="cbfunc">path</span>, <span class="keyword">function</span> <span class="include">(</span>obj<span class="include">)</span>
  <span class="keyword">if</span> obj.ptr == <span class="hero">player</span>.ptr <span class="keyword">then</span>
    <span class="print">print</span><span class="include">(</span><span class="string">"<span class="note">--</span><span class="note">--</span><span class="note">--</span><span class="note">--</span>newpath"</span><span class="include">)</span>
    <span class="print">print</span><span class="include">(</span><span class="string">"isActive: "</span> .. tostring<span class="include">(</span>obj.<span class="cbfunc">path</span>.isActive<span class="include">)</span><span class="include">)</span>
    <span class="print">print</span><span class="include">(</span><span class="string">"index: "</span> .. obj.<span class="cbfunc">path</span>.index<span class="include">)</span>
    <span class="print">print</span><span class="include">(</span><span class="string">"count: "</span> .. obj.<span class="cbfunc">path</span>.count<span class="include">)</span>

    <span class="local">local</span> pos = obj.<span class="cbfunc">path</span>.serverPos
    <span class="print">print</span><span class="include">(</span><span class="string">"serverPos: "</span> .. pos.x .. <span class="string">","</span> .. pos.y .. <span class="string">","</span> .. pos.z<span class="include">)</span>
    <span class="print">print</span><span class="include">(</span><span class="string">"isDashing: "</span> .. tostring<span class="include">(</span>obj.<span class="cbfunc">path</span>.isDashing<span class="include">)</span><span class="include">)</span>

    <span class="keyword">if</span> obj.<span class="cbfunc">path</span>.isDashing <span class="keyword">then</span>
      <span class="print">print</span><span class="include">(</span><span class="string">"dashSpeed: "</span> .. obj.<span class="cbfunc">path</span>.dashSpeed<span class="include">)</span>
    <span class="keyword">end</span>
  <span class="keyword">end</span>
<span class="keyword">end</span><span class="include">)</span>

<span class="cb">cb</span>.<span class="add">add</span><span class="include">(</span><span class="cb">cb</span>.<span class="cbfunc">draw</span>, <span class="keyword">function</span><span class="include">(</span><span class="include">)</span>
    <span class="keyword">if</span> <span class="hero">player</span>.<span class="cbfunc">path</span>.active <span class="keyword">then</span>
        graphics.draw_line_strip<span class="include">(</span><span class="hero">player</span>.<span class="cbfunc">path</span>.point, <span class="number">2</span>, <span class="number">0</span>xFFFFFFFF, <span class="hero">player</span>.<span class="cbfunc">path</span>.count+<span class="number">1</span><span class="include">)</span>
    <span class="keyword">end</span>
<span class="keyword">end</span><span class="include">)</span></code></pre><h4>p:calcPos(v1)</h4><p>Parameters<br>
<code>path.obj</code> p<br>
<code>vec3</code> v1<br>
Return Value<br>
<code>vec3[]</code> returns vec3 array containing path points<br>
<code>number</code> returns array length<br></p><pre><code class="lang-lua"><span class="cb">cb</span>.<span class="add">add</span><span class="include">(</span><span class="cb">cb</span>.<span class="cbfunc">draw</span>, <span class="keyword">function</span><span class="include">(</span><span class="include">)</span>
  <span class="local">local</span> p, n = <span class="hero">player</span>.<span class="cbfunc">path</span>:calcPos<span class="include">(</span>mousePos<span class="include">)</span>
  <span class="keyword">for</span> i = <span class="number">0</span>, n - <span class="number">1</span> <span class="keyword">do</span>
    graphics.draw_circle<span class="include">(</span>p[i], <span class="number">15</span>, <span class="number">2</span>, <span class="number">0</span>xffffffff, <span class="number">3</span><span class="include">)</span>
  <span class="keyword">end</span>
<span class="keyword">end</span><span class="include">)</span></code></pre><h4>p:isDirectPath(v1, v2)</h4><p>Parameters<br>
<code>path.obj</code> p<br>
<code>vec3</code> v1<br>
<code>vec3</code> v2<br>
Return Value<br>
<code>boolean</code> returns result<br>
<code>number[]</code> return lastReachedPos<br></p><pre><code class="lang-lua"><span class="local">local</span> isDirect,lastReachedPos = <span class="hero">player</span>.<span class="cbfunc">path</span>:isDirectPath<span class="include">(</span><span class="hero">player</span>.pos, mousePos<span class="include">)</span>
<span class="keyword">if</span> isDirect <span class="keyword">then</span>
    <span class="print">print</span><span class="include">(</span><span class="string">'lastReachedPos.x'</span>, lastReachedPos[<span class="number">0</span>]<span class="include">)</span>
    <span class="print">print</span><span class="include">(</span><span class="string">'lastReachedPos.y'</span>, lastReachedPos[<span class="number">1</span>]<span class="include">)</span>
<span class="keyword">end</span></code></pre><h3 id="objects-buffobj">buff.obj</h3><p>Warning: <br>
<code>buff.obj</code> are temporary objects, their memory may change or become invalid at any time, please do not save <code>buff.obj</code> anywhere<br></p><p>Properties:<br></p><ul>
<li><code>number</code> buff.type</li>
<li><code>string</code> buff.name</li>
<li><code>number</code> buff.hash<blockquote>
<p>The fnvhash of buff.name</p>
</blockquote>
</li>
<li><code>boolean</code> buff.valid</li>
<li><code>obj</code> buff.owner</li>
<li><code>obj</code> buff.source</li>
<li><code>number</code> buff.startTime</li>
<li><code>number</code> buff.endTime</li>
<li><code>number</code> buff.stacks<blockquote>
<p>Number of buff layers</p>
</blockquote>
</li>
<li><code>number</code> buff.stacks2<blockquote>
<p>The buff <code>Counter</code></p>
</blockquote>
</li>
<li><code>number</code> buff.count<blockquote>
<p>same to stacks2</p>
</blockquote>
</li>
</ul><h4>buff:hasSource(src)</h4><p>Parameters<br>
<code>buff.obj</code><br>
<code>base.obj</code> game object: hero.obj/minion.obj/turret.obj<br>
Return Value<br>
<code>bool</code><br></p><h4>buff:getTooltipVar(index)</h4><p>Return the numerical variable when mouseover the skill icons<br>
Parameters<br>
<code>buff.obj</code><br>
<code>int</code> tooltip var index: [0,15]<br>
Return Value<br>
<code>number</code><br></p><h3 id="objects-runemanagerobj">runemanager.obj</h3><p>Properties:<br></p><ul>
<li><code>number</code> runemanager.size</li>
</ul><h4>runemanager:get(index)</h4><p>Parameters<br>
<code>runemanager.obj</code> camp<br>
<code>number</code> index: the index to get<br>
Return Value<br>
<code>rune.obj</code> returns the rune of current index<br></p><h3 id="objects-runeobj">rune.obj</h3><p>Properties:<br></p><ul>
<li><code>number</code> rune.id</li>
<li><code>string</code> rune.name</li>
<li><code>texture.obj</code> rune.icon</li>
</ul><h4>rune:getTooltipVar(index)</h4><p>Return the numerical variable when mouseover the icons<br>
Parameters<br>
<code>rune.obj</code><br>
<code>int</code> tooltip var index: [0,15]<br>
Return Value<br>
<code>number</code><br></p><h3 id="objects-campobj">camp.obj</h3><p>Properties:<br></p><ul>
<li><code>number</code> camp.type</li>
<li><code>number</code> camp.index</li>
<li><code>number</code> camp.index32</li>
<li><code>number</code> camp.team</li>
<li><code>string</code> camp.name</li>
<li><code>number</code> camp.networkID</li>
<li><code>number</code> camp.networkID32</li>
<li><code>number</code> camp.x</li>
<li><code>number</code> camp.y</li>
<li><code>number</code> camp.z</li>
<li><code>vec3</code> camp.pos</li>
<li><code>vec2</code> camp.pos2D</li>
<li><code>boolean</code> camp.isOnScreen</li>
<li><code>number</code> camp.selectionHeight</li>
<li><code>number</code> camp.selectionRadius</li>
<li><code>vec3</code> camp.minBoundingBox</li>
<li><code>vec3</code> camp.maxBoundingBox</li>
<li><code>boolean</code> camp.active</li>
<li><code>number</code> camp.spawnTime</li>
<li><code>number</code> camp.count</li>
</ul><h4>camp:getMinion(index)</h4><p>Parameters<br>
<code>camp.obj</code> camp<br>
<code>number</code> index: the index to get<br>
Return Value<br>
<code>minion.obj</code> returns the minion of current index<br></p><pre><code class="lang-lua"><span class="keyword">for</span> i=<span class="number">0</span>,camp.count-<span class="number">1</span> <span class="keyword">do</span>
    <span class="local">local</span> minion = camp:getMinion<span class="include">(</span>i<span class="include">)</span>
    <span class="keyword">if</span> minion <span class="keyword">then</span>
        <span class="print">print</span><span class="include">(</span>minion.name, minion.pos.x, minion.pos.z<span class="include">)</span>
    <span class="keyword">end</span>
<span class="keyword">end</span></code></pre><h3 id="objects-textureobj">texture.obj</h3><p>Properties:<br>
 <em> <code>number</code> texture.width
 </em> <code>number</code> texture.height
 * <code>string</code> texture.name</p><h3 id="objects-shadereffectobj">shadereffect.obj</h3><h4>effect:show()</h4><p>Parameters<br>
<code>shadereffect.obj</code> effect<br>
Return Value<br>
<code>void</code></p><h4>effect:hide()</h4><p>Parameters<br>
<code>shadereffect.obj</code> effect<br>
Return Value<br>
<code>void</code></p><h4>effect:attach(obj)</h4><p>Parameters<br>
<code>shadereffect.obj</code> effect<br>
<code>base.obj</code> game object: hero.obj/minion.obj/turret.obj<br>
Return Value<br>
<code>void</code></p><h4>effect:update_circle(pos, radius, width, color)</h4><p>Update the circle info of current shadereffect.<br>
Parameters<br>
<code>shadereffect.obj</code> effect<br>
<code>vec3</code> effect center pos<br>
<code>number</code> radius<br>
<code>number</code> width<br>
<code>number</code> color, argb (rgb is ignored when effect type is CIRCLE_RAINBOW)<br>
Return Value<br>
<code>void</code></p><h3 id="objects-skillshotobj-evade3">skillshot.obj (Evade3)</h3><p>Properties:<br></p><ul>
<li><code>obj</code> skillshot.Caster</li>
<li><code>string</code> skillshot.SpellName</li>
<li><code>spelldata.obj</code> skillshot.SpellData</li>
<li><code>number</code> skillshot.DetectionType</li>
<li><code>cast_info_saved.obj</code> skillshot.SpellCastInfoData</li>
<li><code>number</code> skillshot.InitTick</li>
<li><code>number</code> skillshot.StartTick</li>
<li><code>number</code> skillshot.EndTick<blockquote>
<p>Ticks are based on game.time</p>
</blockquote>
</li>
<li><code>number</code> skillshot.TargetHandle<blockquote>
<p>The ObjID</p>
</blockquote>
</li>
<li><code>boolean</code> skillshot.IsDummy</li>
<li><code>boolean</code> skillshot.IsGlobal</li>
<li><code>boolean</code> skillshot.IsFoW</li>
<li><code>boolean</code> skillshot.IsRenderEnabled</li>
<li><code>boolean</code> skillshot.IsVisible</li>
<li><code>table</code> skillshot.LuaData</li>
<li><code>vec2</code> skillshot.StartPosition</li>
<li><code>vec2</code> skillshot.OriginalStartPosition</li>
<li><code>vec2</code> skillshot.DirectionVector</li>
<li><code>vec2</code> skillshot.NormalVector</li>
<li><code>vec2</code> skillshot.EndPosition</li>
<li><code>vec2</code> skillshot.OriginalEndPosition</li>
<li><code>vec2</code> skillshot.CastPosition</li>
<li><code>number</code> skillshot.StartObjectHandle</li>
<li><code>number</code> skillshot.EndObjectHandle<blockquote>
<p>The ObjID</p>
</blockquote>
</li>
<li><code>boolean</code> skillshot.IsIgnoredFromInside</li>
</ul><p>Member Functions:<br></p><ul>
<li>skillshot:IsValid()</li>
<li>skillshot:Invalidate()</li>
<li>skillshot:IsActive()</li>
<li>skillshot:Activate()</li>
<li>skillshot:Deactivate()</li>
<li>skillshot:IsEnabledInMenu()</li>
<li>skillshot:IsFoWEnabledInMenu()</li>
<li>skillshot:GetDangerLevelFromMenu()</li>
<li>skillshot:GetDangerLevel()</li>
<li>skillshot:CalculateTicks()</li>
<li>skillshot:IsIgnored()</li>
<li>skillshot:IsIgnoredTemporarily()</li>
<li>skillshot:IgnoreTemporarily(bool)</li>
<li>skillshot:IsValid()</li>
<li>skillshot:Contains(vec2)</li>
<li>skillshot:ContainsPlayer()</li>
<li>skillshot:GetHitTime()</li>
<li>skillshot:GetHitRemainingTime()</li>
</ul><h3 id="objects-spelldataobj-evade3">spelldata.obj (Evade3)</h3><p>Properties:<br></p><ul>
<li><code>string</code> spelldata.Name</li>
<li><code>number</code> spelldata.NameHash</li>
<li><code>table</code> spelldata.SpellNames</li>
<li><code>table</code> spelldata.MissileNames</li>
<li><code>number</code> spelldata.Slot</li>
<li><code>number</code> spelldata.CastDuration</li>
<li><code>number</code> spelldata.StayDuration</li>
<li><code>number</code> spelldata.Speed</li>
<li><code>number</code> spelldata.Range</li>
<li><code>number</code> spelldata.HitArea</li>
<li><code>number</code> spelldata.ExtraHitArea</li>
<li><code>boolean</code> spelldata.IsDynamicHitArea</li>
<li><code>boolean</code> spelldata.IsGlobal</li>
<li><code>boolean</code> spelldata.IsFixedRange</li>
<li><code>boolean</code> spelldata.IsAreaIgnoringHitBox</li>
<li><code>boolean</code> spelldata.IsRangeIgnoringHitBox</li>
<li><code>boolean</code> spelldata.IsCrowdControl</li>
</ul><h3 id="objects-cast_info_savedobj-evade3">cast_info_saved.obj (Evade3)</h3><p>Properties:<br></p><ul>
<li><code>string</code> cast_info_saved.Name</li>
<li><code>number</code> cast_info_saved.Level</li>
<li><code>boolean</code> cast_info_saved.HasTarget</li>
<li><code>number</code> cast_info_saved.SenderHandle</li>
<li><code>number</code> cast_info_saved.TargetHandle</li>
<li><code>number</code> cast_info_saved.CastSpeed</li>
<li><code>number</code> cast_info_saved.Slot</li>
<li><code>number</code> cast_info_saved.MissileHash</li>
<li><code>number</code> cast_info_saved.MissileNetworkId</li>
<li><code>vec2</code> cast_info_saved.StartPos</li>
<li><code>vec2</code> cast_info_saved.EndPos</li>
<li><code>vec2</code> cast_info_saved.EndPos2</li>
<li><code>number</code> cast_info_saved.WindUpTime</li>
<li><code>number</code> cast_info_saved.AnimationTime</li>
<li><code>number</code> cast_info_saved.StartTime</li>
<li><code>number</code> cast_info_saved.CastEndTime</li>
<li><code>number</code> cast_info_saved.EndTime</li>
<li><code>number</code> cast_info_saved.Cooldown</li>
<li><code>boolean</code> cast_info_saved.IsBasicAttack</li>
<li><code>boolean</code> cast_info_saved.IsSpecialAttack</li>
<li><code>boolean</code> cast_info_saved.IsInstantCast</li>
<li><code>boolean</code> cast_info_saved.IsAutoAttack</li>
<li><code>boolean</code> cast_info_saved.IsChanneling</li>
<li><code>boolean</code> cast_info_saved.IsBasicAttackSlot</li>
<li><code>boolean</code> cast_info_saved.IsCharging</li>
<li><code>boolean</code> cast_info_saved.SpellWasCast</li>
<li><code>boolean</code> cast_info_saved.IsStopped</li>
<li><code>boolean</code> cast_info_saved.ChargeEndScheduled</li>
</ul><h1 id="modules">Modules</h1><h3 id="modules-pred">pred</h3><h4>pred.trace.linear.hardlock(input, seg, obj)</h4><p>Parameters<br>
<code>table</code> input<br>
<code>seg</code> seg<br>
<code>obj</code> obj<br>
Return Value<br>
<code>boolean</code> returns true if obj is hard crowd controlled<br></p><pre><code class="lang-lua">
</code></pre><h4>pred.trace.linear.hardlockmove(input, seg, obj)</h4><p>Parameters<br>
<code>table</code> input<br>
<code>seg</code> seg<br>
<code>obj</code> obj<br>
Return Value<br>
<code>boolean</code> returns true if obj is hard crowd controlled by taunts, fears or charms<br></p><pre><code class="lang-lua">
</code></pre><h4>pred.trace.circular.hardlock(input, seg, obj)</h4><p>Parameters<br>
<code>table</code> input<br>
<code>seg</code> seg<br>
<code>obj</code> obj<br>
Return Value<br>
<code>boolean</code> returns true if obj is hard crowd controlled<br></p><pre><code class="lang-lua">
</code></pre><h4>pred.trace.circular.hardlockmove(input, seg, obj)</h4><p>Parameters<br>
<code>table</code> input<br>
<code>seg</code> seg<br>
<code>obj</code> obj<br>
Return Value<br>
<code>boolean</code> returns true if obj is hard crowd controlled by taunts, fears or charms<br></p><pre><code class="lang-lua">
</code></pre><h4>pred.trace.newpath(obj, t1, t2)</h4><p>Parameters<br>
<code>obj</code> obj<br>
<code>number</code> t1<br>
<code>number</code> t2<br>
Return Value<br>
<code>boolean</code> returns true if obj path has updated <t1 seconds="" ago="" or="">t2 seconds ago<br></t1></p><pre><code class="lang-lua">
</code></pre><h4>pred.core.lerp(path, delay, speed)</h4><p>Parameters<br>
<code>path.obj</code> path<br>
<code>number</code> delay<br>
<code>number</code> speed<br>
Return Value<br>
<code>vec2</code> returns interpolated position along path<br>
<code>number</code> returns path index of vec2<br>
<code>boolean</code> returns true if vec2 is the end of path<br></p><pre><code class="lang-lua"><span class="local">local</span> <span class="orb">pred</span> = <span class="module">module</span>.<span class="module">internal</span><span class="include">(</span><span class="string">'<span class="orb">pred</span>'</span><span class="include">)</span>

<span class="local">local</span> <span class="keyword">function</span> on_tick<span class="include">(</span><span class="include">)</span>
    <span class="keyword">if</span> <span class="hero">player</span>.<span class="cbfunc">path</span>.active and <span class="hero">player</span>.<span class="cbfunc">path</span>.count&gt;<span class="number">0</span> <span class="keyword">then</span>
        <span class="local">local</span> res, res_i, res_b = <span class="orb">pred</span>.core.lerp<span class="include">(</span><span class="hero">player</span>.<span class="cbfunc">path</span>, <span class="number"><span class="number">0</span>.<span class="number">5</span></span>, <span class="hero">player</span>.moveSpeed<span class="include">)</span>
        <span class="print">print</span><span class="include">(</span><span class="include">(</span><span class="string">'%.<span class="number">2</span>f-%.<span class="number">2</span>f on index %u'</span><span class="include">)</span>:format<span class="include">(</span>res.x, res.y, res_i<span class="include">)</span>, res_b<span class="include">)</span>
    <span class="keyword">end</span>
<span class="keyword">end</span>

<span class="cb">cb</span>.<span class="add">add</span><span class="include">(</span><span class="cb">cb</span>.<span class="cbfunc">tick</span>, on_tick<span class="include">)</span></code></pre><h4>pred.core.project(pos, path, delay, projectileSpeed, pathSpeed)</h4><p>Parameters<br>
<code>vec2</code> pos<br>
<code>path.obj</code> path<br>
<code>number</code> delay<br>
<code>number</code> projectileSpeed<br>
<code>number</code> pathSpeed<br>
Return Value<br>
<code>vec2</code> returns projected position along path<br>
<code>number</code> returns path index of vec2<br>
<code>boolean</code> returns true if vec2 is the end of path<br></p><pre><code class="lang-lua"><span class="local">local</span> <span class="orb">pred</span> = <span class="module">module</span>.<span class="module">internal</span><span class="include">(</span><span class="string">'<span class="orb">pred</span>'</span><span class="include">)</span>

<span class="local">local</span> <span class="keyword">function</span> on_tick<span class="include">(</span><span class="include">)</span>
    <span class="keyword">if</span> <span class="hero">player</span>.<span class="cbfunc">path</span>.active and <span class="hero">player</span>.<span class="cbfunc">path</span>.count&gt;<span class="number">0</span> <span class="keyword">then</span>
        <span class="local">local</span> res, res_i, res_b = <span class="orb">pred</span>.core.project<span class="include">(</span>mousePos<span class="number">2</span>D, <span class="hero">player</span>.<span class="cbfunc">path</span>, <span class="number"><span class="number">0</span>.<span class="number">5</span></span>, <span class="number">1200</span>, <span class="hero">player</span>.moveSpeed<span class="include">)</span>
        <span class="print">print</span><span class="include">(</span><span class="include">(</span><span class="string">'%.<span class="number">2</span>f-%.<span class="number">2</span>f on index %u'</span><span class="include">)</span>:format<span class="include">(</span>res.x, res.y, res_i<span class="include">)</span>, res_b<span class="include">)</span>
    <span class="keyword">end</span>
<span class="keyword">end</span>

<span class="cb">cb</span>.<span class="add">add</span><span class="include">(</span><span class="cb">cb</span>.<span class="cbfunc">tick</span>, on_tick<span class="include">)</span></code></pre><h4>pred.core.project_off(pos, path, delay, projectileSpeed, pathSpeed, offset)</h4><p>Parameters<br>
<code>vec2</code> pos<br>
<code>path.obj</code> path<br>
<code>number</code> delay<br>
<code>number</code> projectileSpeed<br>
<code>number</code> pathSpeed<br>
<code>vec2</code> offset<br>
Return Value<br>
<code>vec2</code> returns projected position along path<br>
<code>number</code> returns path index of vec2<br>
<code>boolean</code> returns true if vec2 is the end of path<br></p><pre><code class="lang-lua"><span class="local">local</span> <span class="orb">pred</span> = <span class="module">module</span>.<span class="module">internal</span><span class="include">(</span><span class="string">'<span class="orb">pred</span>'</span><span class="include">)</span>

<span class="local">local</span> <span class="keyword">function</span> on_tick<span class="include">(</span><span class="include">)</span>
    <span class="keyword">if</span> <span class="hero">player</span>.<span class="cbfunc">path</span>.active and <span class="hero">player</span>.<span class="cbfunc">path</span>.count&gt;<span class="number">0</span> <span class="keyword">then</span>
        <span class="note">--</span>same as core.project, but the <span class="cbfunc">path</span> is offset
        <span class="local">local</span> res, res_i, res_b = <span class="orb">pred</span>.core.project_off<span class="include">(</span>mousePos<span class="number">2</span>D, <span class="hero">player</span>.<span class="cbfunc">path</span>, <span class="number"><span class="number">0</span>.<span class="number">5</span></span>, <span class="number">1200</span>, <span class="hero">player</span>.moveSpeed, vec<span class="number">2</span><span class="include">(</span><span class="number">0</span>,<span class="number">100</span><span class="include">)</span><span class="include">)</span>
        <span class="print">print</span><span class="include">(</span><span class="include">(</span><span class="string">'%.<span class="number">2</span>f-%.<span class="number">2</span>f on index %u'</span><span class="include">)</span>:format<span class="include">(</span>res.x, res.y, res_i<span class="include">)</span>, res_b<span class="include">)</span>
    <span class="keyword">end</span>
<span class="keyword">end</span>

<span class="cb">cb</span>.<span class="add">add</span><span class="include">(</span><span class="cb">cb</span>.<span class="cbfunc">tick</span>, on_tick<span class="include">)</span></code></pre><h4>pred.core.get_pos_after_time(obj, time)</h4><p>Parameters<br>
<code>obj</code> obj<br>
<code>number</code> time<br>
Return Value<br>
<code>vec2</code> returns position of obj after time<br></p><pre><code class="lang-lua"><span class="local">local</span> <span class="orb">pred</span> = <span class="module">module</span>.<span class="module">internal</span><span class="include">(</span><span class="string">'<span class="orb">pred</span>'</span><span class="include">)</span>

<span class="local">local</span> <span class="keyword">function</span> on_tick<span class="include">(</span><span class="include">)</span>
    <span class="local">local</span> res = <span class="orb">pred</span>.core.get_pos_after_time<span class="include">(</span><span class="hero">player</span>, <span class="number"><span class="number">0</span>.<span class="number">5</span></span><span class="include">)</span>
    <span class="print">print</span><span class="include">(</span><span class="include">(</span><span class="string">'%.<span class="number">2</span>f-%.<span class="number">2</span>f'</span><span class="include">)</span>:format<span class="include">(</span>res.x, res.y<span class="include">)</span><span class="include">)</span>
<span class="keyword">end</span>

<span class="cb">cb</span>.<span class="add">add</span><span class="include">(</span><span class="cb">cb</span>.<span class="cbfunc">tick</span>, on_tick<span class="include">)</span></code></pre><h4>pred.linear.get_prediction(input, tar, src)</h4><p>Parameters<br>
<code>table</code> input<br>
<code>obj</code> tar<br>
<code>obj</code> src [optional]<br>
Return Value<br>
<code>seg</code> returns a line segment<br></p><pre><code class="lang-lua"><span class="note">--</span>this example is based on caitlyn q
<span class="local">local</span> <span class="orb">orb</span> = <span class="module">module</span>.<span class="module">internal</span><span class="include">(</span><span class="string">'<span class="orb">orb</span>'</span><span class="include">)</span>
<span class="local">local</span> <span class="orb">ts</span> = <span class="module">module</span>.<span class="module">internal</span><span class="include">(</span><span class="string">'TS'</span><span class="include">)</span>
<span class="local">local</span> <span class="orb">pred</span> = <span class="module">module</span>.<span class="module">internal</span><span class="include">(</span><span class="string">'<span class="orb">pred</span>'</span><span class="include">)</span>

<span class="local">local</span> pred_input = <span class="include">{</span>
  delay = <span class="number"><span class="number">0</span>.<span class="number">625</span></span>,
  speed = <span class="number">2200</span>,
  width = <span class="number">60</span>,
  range = <span class="number">1150</span>,
  boundingRadiusMod = <span class="number">1</span>,
  collision = <span class="include">{</span>
    wall = true,
  <span class="include">}</span>,
<span class="include">}</span>

<span class="local">local</span> <span class="keyword">function</span> trace_filter<span class="include">(</span>seg, obj<span class="include">)</span>
    <span class="keyword">if</span> seg.startPos:dist<span class="include">(</span>seg.endPos<span class="include">)</span> &gt; pred_input.range <span class="keyword">then</span> <span class="keyword">return</span> false <span class="keyword">end</span>

  <span class="keyword">if</span> <span class="orb">pred</span>.trace.linear.hardlock<span class="include">(</span>pred_input, seg, obj<span class="include">)</span> <span class="keyword">then</span>
    <span class="keyword">return</span> true
  <span class="keyword">end</span>
  <span class="keyword">if</span> <span class="orb">pred</span>.trace.linear.hardlockmove<span class="include">(</span>pred_input, seg, obj<span class="include">)</span> <span class="keyword">then</span>
    <span class="keyword">return</span> true
  <span class="keyword">end</span>
  <span class="keyword">if</span> <span class="orb">pred</span>.trace.newpath<span class="include">(</span>obj, <span class="number"><span class="number">0</span>.<span class="number">033</span></span>, <span class="number"><span class="number">0</span>.<span class="number">500</span></span><span class="include">)</span> <span class="keyword">then</span>
    <span class="keyword">return</span> true
  <span class="keyword">end</span>
<span class="keyword">end</span>

<span class="local">local</span> <span class="keyword">function</span> target_filter<span class="include">(</span>res, obj, dist<span class="include">)</span>
    <span class="keyword">if</span> dist &gt; <span class="number">1500</span> <span class="keyword">then</span> <span class="keyword">return</span> false <span class="keyword">end</span>
    <span class="local">local</span> seg = <span class="orb">pred</span>.linear.get_prediction<span class="include">(</span>pred_input, obj<span class="include">)</span>
    <span class="keyword">if</span> not seg <span class="keyword">then</span> <span class="keyword">return</span> false <span class="keyword">end</span>
    <span class="keyword">if</span> not trace_filter<span class="include">(</span>seg, obj<span class="include">)</span> <span class="keyword">then</span> <span class="keyword">return</span> false <span class="keyword">end</span>

    res.pos = seg.endPos
    <span class="keyword">return</span> true
<span class="keyword">end</span>

<span class="local">local</span> <span class="keyword">function</span> on_tick<span class="include">(</span><span class="include">)</span>
    <span class="keyword">if</span> <span class="hero">player</span>:spellSlot<span class="include">(</span><span class="number">0</span><span class="include">)</span>.state~=<span class="number">0</span> <span class="keyword">then</span> <span class="keyword">return</span> <span class="keyword">end</span>


    <span class="local">local</span> res = <span class="orb">ts</span>.get_result<span class="include">(</span>target_filter<span class="include">)</span>
    <span class="keyword">if</span> res.pos <span class="keyword">then</span>
        <span class="hero">player</span>:castSpell<span class="include">(</span><span class="string">'pos'</span>, <span class="number">0</span>, vec<span class="number">3</span><span class="include">(</span>res.pos.x, mousePos.y, res.pos.y<span class="include">)</span><span class="include">)</span>
        <span class="orb">orb</span>.core.set_server_pause<span class="include">(</span><span class="include">)</span>
        <span class="keyword">return</span> true
    <span class="keyword">end</span>
<span class="keyword">end</span>

<span class="cb">cb</span>.<span class="add">add</span><span class="include">(</span><span class="cb">cb</span>.<span class="cbfunc">tick</span>, on_tick<span class="include">)</span></code></pre><h4>pred.circular.get_prediction(input, tar, src)</h4><p>Parameters<br>
<code>table</code> input<br>
<code>obj</code> tar<br>
<code>obj</code> src [optional]<br>
Return Value<br>
<code>seg</code> returns a line segment<br></p><pre><code class="lang-lua"><span class="note">--</span>this example is based on xerath w
<span class="local">local</span> <span class="orb">orb</span> = <span class="module">module</span>.<span class="module">internal</span><span class="include">(</span><span class="string">'<span class="orb">orb</span>'</span><span class="include">)</span>
<span class="local">local</span> <span class="orb">ts</span> = <span class="module">module</span>.<span class="module">internal</span><span class="include">(</span><span class="string">'TS'</span><span class="include">)</span>
<span class="local">local</span> <span class="orb">pred</span> = <span class="module">module</span>.<span class="module">internal</span><span class="include">(</span><span class="string">'<span class="orb">pred</span>'</span><span class="include">)</span>

<span class="local">local</span> pred_input = <span class="include">{</span>
    delay = <span class="number"><span class="number">0</span>.<span class="number">75</span></span>,
    radius = <span class="number">275</span>,
    speed = math.huge,
    boundingRadiusMod = <span class="number">0</span>,
<span class="include">}</span>

<span class="local">local</span> <span class="keyword">function</span> trace_filter<span class="include">(</span>seg, obj<span class="include">)</span>
    <span class="keyword">if</span> seg.startPos:dist<span class="include">(</span>seg.endPos<span class="include">)</span> &gt; <span class="number">950</span> <span class="keyword">then</span> <span class="keyword">return</span> false <span class="keyword">end</span>

  <span class="keyword">if</span> <span class="orb">pred</span>.trace.circular.hardlock<span class="include">(</span>pred_input, seg, obj<span class="include">)</span> <span class="keyword">then</span>
    <span class="keyword">return</span> true
  <span class="keyword">end</span>
  <span class="keyword">if</span> <span class="orb">pred</span>.trace.circular.hardlockmove<span class="include">(</span>pred_input, seg, obj<span class="include">)</span> <span class="keyword">then</span>
    <span class="keyword">return</span> true
  <span class="keyword">end</span>
  <span class="keyword">if</span> <span class="orb">pred</span>.trace.newpath<span class="include">(</span>obj, <span class="number"><span class="number">0</span>.<span class="number">033</span></span>, <span class="number"><span class="number">0</span>.<span class="number">500</span></span><span class="include">)</span> <span class="keyword">then</span>
    <span class="keyword">return</span> true
  <span class="keyword">end</span>
<span class="keyword">end</span>

<span class="local">local</span> <span class="keyword">function</span> target_filter<span class="include">(</span>res, obj, dist<span class="include">)</span>
    <span class="keyword">if</span> dist &gt; <span class="number">1500</span> <span class="keyword">then</span> <span class="keyword">return</span> false <span class="keyword">end</span>
    <span class="local">local</span> seg = <span class="orb">pred</span>.circular.get_prediction<span class="include">(</span>pred_input, obj<span class="include">)</span>
    <span class="keyword">if</span> not seg <span class="keyword">then</span> <span class="keyword">return</span> false <span class="keyword">end</span>
    <span class="keyword">if</span> not trace_filter<span class="include">(</span>seg, obj<span class="include">)</span> <span class="keyword">then</span> <span class="keyword">return</span> false <span class="keyword">end</span>

    res.pos = seg.endPos
    <span class="keyword">return</span> true
<span class="keyword">end</span>

<span class="local">local</span> <span class="keyword">function</span> on_tick<span class="include">(</span><span class="include">)</span>
    <span class="keyword">if</span> <span class="hero">player</span>:spellSlot<span class="include">(</span><span class="number">1</span><span class="include">)</span>.state~=<span class="number">0</span> <span class="keyword">then</span> <span class="keyword">return</span> <span class="keyword">end</span>


    <span class="local">local</span> res = <span class="orb">ts</span>.get_result<span class="include">(</span>target_filter<span class="include">)</span>
    <span class="keyword">if</span> res.pos <span class="keyword">then</span>
        <span class="hero">player</span>:castSpell<span class="include">(</span><span class="string">'pos'</span>, <span class="number">1</span>, vec<span class="number">3</span><span class="include">(</span>res.pos.x, mousePos.y, res.pos.y<span class="include">)</span><span class="include">)</span>
        <span class="orb">orb</span>.core.set_server_pause<span class="include">(</span><span class="include">)</span>
        <span class="keyword">return</span> true
    <span class="keyword">end</span>
<span class="keyword">end</span>

<span class="cb">cb</span>.<span class="add">add</span><span class="include">(</span><span class="cb">cb</span>.<span class="cbfunc">tick</span>, on_tick<span class="include">)</span></code></pre><h4>pred.present.get_source_pos(obj)</h4><p>Parameters<br>
<code>obj</code> obj<br>
Return Value<br>
<code>vec2</code> returns server pos of obj<br></p><h4>pred.present.get_prediction(input, tar, src)</h4><p>Parameters<br>
<code>table</code> input<br>
<code>obj</code> tar<br>
<code>obj</code> src [optional]<br>
Return Values<br>
<code>vec2</code><br></p><pre><code class="lang-lua"><span class="note">--</span>this example is based on kindred e
<span class="local">local</span> <span class="orb">orb</span> = <span class="module">module</span>.<span class="module">internal</span><span class="include">(</span><span class="string">'<span class="orb">orb</span>'</span><span class="include">)</span>
<span class="local">local</span> <span class="orb">ts</span> = <span class="module">module</span>.<span class="module">internal</span><span class="include">(</span><span class="string">'TS'</span><span class="include">)</span>
<span class="local">local</span> <span class="orb">pred</span> = <span class="module">module</span>.<span class="module">internal</span><span class="include">(</span><span class="string">'<span class="orb">pred</span>'</span><span class="include">)</span>

<span class="local">local</span> pred_input = <span class="include">{</span>
  delay = <span class="number">0</span>,
  radius = <span class="hero">player</span>.attackRange,
  dashRadius = <span class="number">0</span>,
  boundingRadiusModSource = <span class="number">1</span>,
  boundingRadiusModTarget = <span class="number">1</span>,
<span class="include">}</span>

<span class="local">local</span> <span class="keyword">function</span> target_filter<span class="include">(</span>res, obj, dist<span class="include">)</span>
    <span class="keyword">if</span> dist&gt;<span class="number">800</span> <span class="keyword">then</span> <span class="keyword">return</span> false <span class="keyword">end</span>
    <span class="keyword">if</span> not <span class="orb">pred</span>.present.get_prediction<span class="include">(</span>pred_input, obj<span class="include">)</span> <span class="keyword">then</span> <span class="keyword">return</span> false <span class="keyword">end</span>

    res.obj = obj
    <span class="keyword">return</span> true
<span class="keyword">end</span>

<span class="local">local</span> <span class="keyword">function</span> invoke<span class="include">(</span><span class="include">)</span>
    <span class="keyword">if</span> <span class="hero">player</span>:spellSlot<span class="include">(</span><span class="number">2</span><span class="include">)</span>.state~=<span class="number">0</span> <span class="keyword">then</span> <span class="keyword">return</span> <span class="keyword">end</span>

    <span class="local">local</span> res = <span class="orb">ts</span>.get_result<span class="include">(</span>target_filter<span class="include">)</span>
    <span class="keyword">if</span> res.obj <span class="keyword">then</span>
        <span class="hero">player</span>:castSpell<span class="include">(</span><span class="string">'obj'</span>, <span class="number">2</span>, res.obj<span class="include">)</span>
        <span class="orb">orb</span>.core.set_server_pause<span class="include">(</span><span class="include">)</span>
        <span class="keyword">return</span> true
  <span class="keyword">end</span>
<span class="keyword">end</span></code></pre><h4>pred.collision.get_prediction(input, pred_result, ign_obj)</h4><p>Parameters<br>
<code>table</code> input<br>
<code>seg</code> pred_result<br>
<code>obj</code> ign_obj [optional]<br>
Return Values<br>
<code>table</code> returns a table of objects that pred_result will collide with or nil if no collision is detected<br></p><pre><code class="lang-lua"><span class="note">--</span>this example is based on morgana q
<span class="local">local</span> <span class="orb">orb</span> = <span class="module">module</span>.<span class="module">internal</span><span class="include">(</span><span class="string">'<span class="orb">orb</span>'</span><span class="include">)</span>
<span class="local">local</span> <span class="orb">ts</span> = <span class="module">module</span>.<span class="module">internal</span><span class="include">(</span><span class="string">'TS'</span><span class="include">)</span>
<span class="local">local</span> <span class="orb">pred</span> = <span class="module">module</span>.<span class="module">internal</span><span class="include">(</span><span class="string">'<span class="orb">pred</span>'</span><span class="include">)</span>

<span class="local">local</span> pred_input = <span class="include">{</span>
  delay = <span class="number"><span class="number">0</span>.<span class="number">25</span></span>,
  speed = <span class="number">1200</span>,
  width = <span class="number">70</span>,
  boundingRadiusMod = <span class="number">1</span>,
  range = <span class="number">1100</span>,
  collision = <span class="include">{</span>
    minion = true, <span class="note">--</span>checks collision <span class="keyword">for</span> minions
    <span class="hero">hero</span> = false, <span class="note">--</span>no need to check <span class="keyword">for</span> <span class="hero">hero</span> collision

    <span class="note">--</span> checks all collisions: YasuoWall/SamiraWall/BraumWall/PantheonWall/MelWall
    wall = true, 

    <span class="note">--</span> or checks specific collisions
    <span class="note">--</span> wall = <span class="include">{</span> yasuo = true, samira = true, braum = false, pantheon = false, mel = false <span class="include">}</span>,
  <span class="include">}</span>,
<span class="include">}</span>

<span class="local">local</span> <span class="keyword">function</span> trace_filter<span class="include">(</span>seg, obj, dist<span class="include">)</span>
    <span class="keyword">if</span> seg.startPos:dist<span class="include">(</span>seg.endPos<span class="include">)</span> &gt; pred_input.range <span class="keyword">then</span>
        <span class="keyword">return</span> false
    <span class="keyword">end</span>
  <span class="keyword">if</span> <span class="orb">pred</span>.trace.newpath<span class="include">(</span>obj, <span class="number"><span class="number">0</span>.<span class="number">033</span></span>, <span class="number"><span class="number">0</span>.<span class="number">500</span></span><span class="include">)</span> and dist &lt; <span class="number">600</span> <span class="keyword">then</span>
        <span class="keyword">return</span> true
  <span class="keyword">end</span>
  <span class="keyword">if</span> <span class="orb">pred</span>.trace.linear.hardlock<span class="include">(</span>pred_input, seg, obj<span class="include">)</span> <span class="keyword">then</span>
    <span class="keyword">return</span> true
  <span class="keyword">end</span>
  <span class="keyword">if</span> <span class="orb">pred</span>.trace.linear.hardlockmove<span class="include">(</span>pred_input, seg, obj<span class="include">)</span> <span class="keyword">then</span>
    <span class="keyword">return</span> true
  <span class="keyword">end</span>
<span class="keyword">end</span>

<span class="local">local</span> <span class="keyword">function</span> target_filter<span class="include">(</span>res, obj, dist<span class="include">)</span>
    <span class="keyword">if</span> dist &gt; pred_input.range <span class="keyword">then</span> <span class="keyword">return</span> false <span class="keyword">end</span>
    <span class="local">local</span> seg = <span class="orb">pred</span>.linear.get_prediction<span class="include">(</span>pred_input, obj<span class="include">)</span>
    <span class="keyword">if</span> not seg <span class="keyword">then</span> <span class="keyword">return</span> false <span class="keyword">end</span>
    <span class="keyword">if</span> not trace_filter<span class="include">(</span>seg, obj, dist<span class="include">)</span> <span class="keyword">then</span> <span class="keyword">return</span> false <span class="keyword">end</span>
    <span class="note">--</span>pass obj as third arg, it will <span class="keyword">then</span> be ignored from collision checks
    <span class="keyword">if</span> <span class="orb">pred</span>.collision.get_prediction<span class="include">(</span>pred_input, seg, obj<span class="include">)</span> <span class="keyword">then</span> <span class="keyword">return</span> false <span class="keyword">end</span>

    res.pos = seg.endPos
    <span class="keyword">return</span> true
<span class="keyword">end</span>

<span class="local">local</span> <span class="keyword">function</span> on_tick<span class="include">(</span><span class="include">)</span>
  <span class="keyword">if</span> <span class="hero">player</span>:spellSlot<span class="include">(</span><span class="number">0</span><span class="include">)</span>.state~=<span class="number">0</span> <span class="keyword">then</span> <span class="keyword">return</span> <span class="keyword">end</span>

  <span class="local">local</span> res = <span class="orb">ts</span>.get_result<span class="include">(</span>target_filter<span class="include">)</span>
  <span class="keyword">if</span> res.pos <span class="keyword">then</span>
    <span class="hero">player</span>:castSpell<span class="include">(</span><span class="string">'pos'</span>, <span class="number">0</span>, vec<span class="number">3</span><span class="include">(</span>res.pos.x, mousePos.y, res.pos.y<span class="include">)</span><span class="include">)</span>
    <span class="orb">orb</span>.core.set_server_pause<span class="include">(</span><span class="include">)</span>
    <span class="keyword">return</span> true
  <span class="keyword">end</span>
<span class="keyword">end</span>

<span class="cb">cb</span>.<span class="add">add</span><span class="include">(</span><span class="cb">cb</span>.<span class="cbfunc">tick</span>, on_tick<span class="include">)</span></code></pre><h3 id="modules-orb">orb</h3><h4>orb.core.akshan_should_double_attack</h4><p>Return Value<br>
<code>boolean</code><br></p><pre><code class="lang-lua"><span class="local">local</span> <span class="orb">orb</span> = <span class="module">module</span>.<span class="module">internal</span><span class="include">(</span><span class="string">'<span class="orb">orb</span>'</span><span class="include">)</span>
<span class="keyword">if</span> xxx <span class="keyword">then</span>
    <span class="orb">orb</span>.core.akshan_should_double_attack = true  <span class="note">--</span> set it <span class="keyword">in</span> your AIO logic
<span class="keyword">end</span></code></pre><h4>orb.core.cur_attack_target</h4><p>Return Value<br>
<code>obj</code> readonly, the current attack target<br></p><h4>orb.core.cur_attack_name</h4><p>Return Value<br>
<code>string</code> readonly, the current AA spell name<br></p><h4>orb.core.cur_attack_start_client</h4><p>Return Value<br>
<code>number</code> readonly, the time when current attack started, based on os.clock()<br></p><h4>orb.core.cur_attack_start_server</h4><p>Return Value<br>
<code>number</code> readonly, the server time when current attack started, based on os.clock()<br></p><h4>orb.core.cur_attack_speed_mod</h4><p>Return Value<br>
<code>number</code> readonly, the attack speed mod for current attack<br></p><h4>orb.core.next_attack</h4><p>Return Value<br>
<code>number</code> readonly, time for next attack <br></p><h4>orb.core.next_action</h4><p>Return Value<br>
<code>number</code> readonly, time for next action <br></p><h4>orb.core.next_action</h4><p>Return Value<br>
<code>number</code> readonly, time for next action <br></p><h4>orb.core.reset()</h4><p>Return Value<br>
<code>void</code><br></p><pre><code class="lang-lua"><span class="local">local</span> <span class="orb">orb</span> = <span class="module">module</span>.<span class="module">internal</span><span class="include">(</span><span class="string">'<span class="orb">orb</span>'</span><span class="include">)</span>
<span class="orb">orb</span>.core.reset<span class="include">(</span><span class="include">)</span> <span class="note">--</span>resets the orbwalks attack cooldown</code></pre><h4>orb.core.can_action()</h4><p>Return Value<br>
<code>boolean</code> returns true if player can move or cast spells<br></p><pre><code class="lang-lua"><span class="local">local</span> <span class="orb">orb</span> = <span class="module">module</span>.<span class="module">internal</span><span class="include">(</span><span class="string">'<span class="orb">orb</span>'</span><span class="include">)</span>

<span class="local">local</span> <span class="keyword">function</span> on_tick<span class="include">(</span><span class="include">)</span>
    <span class="print">print</span><span class="include">(</span><span class="orb">orb</span>.core.can_action<span class="include">(</span><span class="include">)</span> and <span class="string">'can action'</span> or <span class="string">'can not action'</span><span class="include">)</span>
<span class="keyword">end</span>

<span class="cb">cb</span>.<span class="add">add</span><span class="include">(</span><span class="cb">cb</span>.<span class="cbfunc">tick</span>, on_tick<span class="include">)</span></code></pre><h4>orb.core.can_move()</h4><p>Return Value<br>
<code>boolean</code> returns true if player can move<br></p><h4>orb.core.can_attack()</h4><p>Return Value<br>
<code>boolean</code> returns true if player can attack<br></p><pre><code class="lang-lua"><span class="local">local</span> <span class="orb">orb</span> = <span class="module">module</span>.<span class="module">internal</span><span class="include">(</span><span class="string">'<span class="orb">orb</span>'</span><span class="include">)</span>

<span class="local">local</span> <span class="keyword">function</span> on_tick<span class="include">(</span><span class="include">)</span>
    <span class="print">print</span><span class="include">(</span><span class="orb">orb</span>.core.can_attack<span class="include">(</span><span class="include">)</span> and <span class="string">'can attack'</span> or <span class="string">'can not attack'</span><span class="include">)</span>
<span class="keyword">end</span>

<span class="cb">cb</span>.<span class="add">add</span><span class="include">(</span><span class="cb">cb</span>.<span class="cbfunc">tick</span>, on_tick<span class="include">)</span></code></pre><h4>orb.core.can_cast_spell(spellSlot, ignore_can_action_check)</h4><p>Return Value<br>
<code>boolean</code> returns true if player can cast spell<br></p><h4>orb.core.is_paused()</h4><p>Return Value<br>
<code>boolean</code> returns true if the orb is paused (will not issue any orders)<br></p><pre><code class="lang-lua"><span class="local">local</span> <span class="orb">orb</span> = <span class="module">module</span>.<span class="module">internal</span><span class="include">(</span><span class="string">'<span class="orb">orb</span>'</span><span class="include">)</span>

<span class="local">local</span> <span class="keyword">function</span> on_tick<span class="include">(</span><span class="include">)</span>
    <span class="print">print</span><span class="include">(</span><span class="orb">orb</span>.core.is_paused<span class="include">(</span><span class="include">)</span> and <span class="string">'is paused'</span> or <span class="string">'is not paused'</span><span class="include">)</span>
<span class="keyword">end</span>

<span class="cb">cb</span>.<span class="add">add</span><span class="include">(</span><span class="cb">cb</span>.<span class="cbfunc">tick</span>, on_tick<span class="include">)</span></code></pre><h4>orb.core.is_move_paused()</h4><p>Return Value<br>
<code>boolean</code> returns true if the orb movement is paused (will not issue move orders)<br></p><pre><code class="lang-lua"><span class="local">local</span> <span class="orb">orb</span> = <span class="module">module</span>.<span class="module">internal</span><span class="include">(</span><span class="string">'<span class="orb">orb</span>'</span><span class="include">)</span>

<span class="local">local</span> <span class="keyword">function</span> on_tick<span class="include">(</span><span class="include">)</span>
    <span class="print">print</span><span class="include">(</span><span class="orb">orb</span>.core.is_move_paused<span class="include">(</span><span class="include">)</span> and <span class="string">'move is paused'</span> or <span class="string">'move is not paused'</span><span class="include">)</span>
<span class="keyword">end</span>

<span class="cb">cb</span>.<span class="add">add</span><span class="include">(</span><span class="cb">cb</span>.<span class="cbfunc">tick</span>, on_tick<span class="include">)</span></code></pre><h4>orb.core.is_attack_paused()</h4><p>Return Value<br>
<code>boolean</code> returns true if the orb attacks are paused (will not issue attack orders)<br></p><pre><code class="lang-lua"><span class="local">local</span> <span class="orb">orb</span> = <span class="module">module</span>.<span class="module">internal</span><span class="include">(</span><span class="string">'<span class="orb">orb</span>'</span><span class="include">)</span>

<span class="local">local</span> <span class="keyword">function</span> on_tick<span class="include">(</span><span class="include">)</span>
    <span class="print">print</span><span class="include">(</span><span class="orb">orb</span>.core.is_attack_paused<span class="include">(</span><span class="include">)</span> and <span class="string">'attack is paused'</span> or <span class="string">'attack is not paused'</span><span class="include">)</span>
<span class="keyword">end</span>

<span class="cb">cb</span>.<span class="add">add</span><span class="include">(</span><span class="cb">cb</span>.<span class="cbfunc">tick</span>, on_tick<span class="include">)</span></code></pre><h4>orb.core.is_spell_locked()</h4><p>Return Value<br>
<code>boolean</code> returns true if spells are paused <br></p><h4>orb.core.is_winding_up_attack()</h4><p>Return Value<br>
<code>boolean</code> returns true if winding up <br></p><h4>orb.core.set_pause(t)</h4><p>Parameters<br>
<code>number</code> t<br>
Return Value<br>
<code>void</code><br></p><pre><code class="lang-lua"><span class="local">local</span> <span class="orb">orb</span> = <span class="module">module</span>.<span class="module">internal</span><span class="include">(</span><span class="string">'<span class="orb">orb</span>'</span><span class="include">)</span>

<span class="local">local</span> <span class="keyword">function</span> on_process_spell<span class="include">(</span><span class="cbfunc">spell</span><span class="include">)</span>
    <span class="keyword">if</span> <span class="cbfunc">spell</span>.owner==<span class="hero">player</span> and <span class="cbfunc">spell</span>.name==<span class="string">'AnnieQ'</span> <span class="keyword">then</span>
        <span class="orb">orb</span>.core.set_pause<span class="include">(</span><span class="number"><span class="number">0</span>.<span class="number">25</span></span><span class="include">)</span>
    <span class="keyword">end</span>
<span class="keyword">end</span>

<span class="cb">cb</span>.<span class="add">add</span><span class="include">(</span><span class="cb">cb</span>.<span class="cbfunc">spell</span>, on_process_spell<span class="include">)</span></code></pre><h4>orb.core.set_pause_move(t)</h4><p>Parameters<br>
<code>number</code> t<br>
Return Value<br>
<code>void</code><br></p><h4>orb.core.set_pause_attack(t)</h4><p>Parameters<br>
<code>number</code> t<br>
Return Value<br>
<code>void</code><br></p><pre><code class="lang-lua"><span class="local">local</span> <span class="orb">orb</span> = <span class="module">module</span>.<span class="module">internal</span><span class="include">(</span><span class="string">'<span class="orb">orb</span>'</span><span class="include">)</span>

<span class="local">local</span> <span class="keyword">function</span> on_process_spell<span class="include">(</span><span class="cbfunc">spell</span><span class="include">)</span>
    <span class="keyword">if</span> <span class="cbfunc">spell</span>.owner==<span class="hero">player</span> and <span class="cbfunc">spell</span>.name==<span class="string">'XayahR'</span> <span class="keyword">then</span>
        <span class="orb">orb</span>.core.set_pause_attack<span class="include">(</span><span class="number"><span class="number">1</span>.<span class="number">5</span></span><span class="include">)</span>
    <span class="keyword">end</span>
<span class="keyword">end</span>

<span class="cb">cb</span>.<span class="add">add</span><span class="include">(</span><span class="cb">cb</span>.<span class="cbfunc">spell</span>, on_process_spell<span class="include">)</span></code></pre><h4>orb.core.set_server_pause()</h4><p>Return Value<br>
<code>void</code><br></p><pre><code class="lang-lua"><span class="local">local</span> <span class="orb">orb</span> = <span class="module">module</span>.<span class="module">internal</span><span class="include">(</span><span class="string">'<span class="orb">orb</span>'</span><span class="include">)</span>

<span class="local">local</span> <span class="keyword">function</span> on_tick<span class="include">(</span><span class="include">)</span>
  <span class="keyword">if</span> <span class="orb">orb</span>.core.is_paused<span class="include">(</span><span class="include">)</span> or not <span class="orb">orb</span>.core.can_action<span class="include">(</span><span class="include">)</span> or <span class="orb">orb</span>.core.is_move_paused<span class="include">(</span><span class="include">)</span> <span class="keyword">then</span> <span class="keyword">return</span> <span class="keyword">end</span>

    <span class="keyword">if</span> <span class="hero">player</span>:spellSlot<span class="include">(</span><span class="number">0</span><span class="include">)</span>.state~=<span class="number">0</span> <span class="keyword">then</span> <span class="keyword">return</span> <span class="keyword">end</span>
    <span class="hero">player</span>:castSpell<span class="include">(</span><span class="string">'pos'</span>, <span class="number">0</span>, mousePos<span class="include">)</span>
    <span class="orb">orb</span>.core.set_server_pause<span class="include">(</span><span class="include">)</span>
<span class="keyword">end</span>

<span class="cb">cb</span>.<span class="add">add</span><span class="include">(</span><span class="cb">cb</span>.<span class="cbfunc">tick</span>, on_tick<span class="include">)</span></code></pre><h4>orb.core.set_server_pause_attack()</h4><p>Return Value<br>
<code>void</code><br></p><h4>orb.core.set_pause_spell_lock()</h4><p>Return Value<br>
<code>void</code><br></p><h4>orb.core.set_server_pause_spell_lock()</h4><p>Return Value<br>
<code>void</code><br></p><h4>orb.core.set_pause_strict_limitation(t)</h4><p>The anti-disconnect feature of orbwalker in CN region is based on facing direction, while during some special spell casting like MelQ, facing direction will be changed very quickly. So we add this API to pause the strict anti-disconnect feature<br> 
But shits anti-cheat in CN will not fix this. <b>You maybe got banned/kicked out from game by pause this feature</b>, and which is also the reason why normal players are banned (like VarusQ).<br> </p><p>Parameters<br>
<code>number</code> t<br>
Return Value<br>
<code>void</code><br></p><h4>orb.core.on_after_attack(callback)</h4><p>Parameters<br>
<code>function</code> callabck<br>
Register a callback, which will be triggered once after a attack windup.</p><h4>orb.core.on_player_attack(callback)</h4><p>Parameters<br>
<code>function</code> callabck<br>
Register a callback, which will be triggered when a attack is responsed by server</p><h4>orb.core.on_advanced_after_attack(callback)</h4><p>Parameters<br>
<code>function</code> callabck<br>
Register a callback, which will be triggered when last attack is finished and could action now (called continuously after windup until attack ready again).</p><h4>orb.core.time_to_next_attack()</h4><p>Return Value<br>
<code>number</code> the left seconds to issue next attack<br></p><h4>orb.core.is_waiting_for_server_response(spellSlot)</h4><p>Return Value<br>
<code>boolean</code><br></p><h4>orb.core.is_waitting_for_cooldown(spellSlot)</h4><p>Return Value<br>
<code>boolean</code><br></p><h4>orb.combat.is_active()</h4><p>Return Value<br>
<code>boolean</code> returns true if combat mode is active<br></p><pre><code class="lang-lua"><span class="local">local</span> <span class="orb">orb</span> = <span class="module">module</span>.<span class="module">internal</span><span class="include">(</span><span class="string">'<span class="orb">orb</span>'</span><span class="include">)</span>

<span class="local">local</span> <span class="keyword">function</span> on_tick<span class="include">(</span><span class="include">)</span>
  <span class="keyword">if</span> <span class="orb">orb</span>.combat.is_active<span class="include">(</span><span class="include">)</span> <span class="keyword">then</span>
        <span class="print">print</span><span class="include">(</span><span class="string">'combat active'</span><span class="include">)</span>
    <span class="keyword">end</span>
<span class="keyword">end</span>

<span class="cb">cb</span>.<span class="add">add</span><span class="include">(</span><span class="cb">cb</span>.<span class="cbfunc">tick</span>, on_tick<span class="include">)</span></code></pre><h4>orb.combat.target</h4><p>Return Value<br>
<code>hero.obj</code> the current attack hero (valid only for current tick)<br></p><pre><code class="lang-lua"><span class="local">local</span> <span class="orb">orb</span> = <span class="module">module</span>.<span class="module">internal</span><span class="include">(</span><span class="string">'<span class="orb">orb</span>'</span><span class="include">)</span>

<span class="local">local</span> <span class="keyword">function</span> on_tick<span class="include">(</span><span class="include">)</span>
  <span class="keyword">if</span> <span class="orb">orb</span>.combat.is_active<span class="include">(</span><span class="include">)</span> and your_logic<span class="include">(</span><span class="include">)</span> <span class="keyword">then</span>
    <span class="orb">orb</span>.combat.target = your_logic_target<span class="include">(</span><span class="include">)</span>
  <span class="keyword">end</span>
<span class="keyword">end</span>

<span class="cb">cb</span>.<span class="add">add</span><span class="include">(</span><span class="cb">cb</span>.<span class="cbfunc">tick</span>, on_tick<span class="include">)</span></code></pre><h4>orb.combat.pos</h4><p>Return Value<br>
<code>hero.obj</code> the prefer position (valid only for current tick) will orbwalker to<br></p><h4>orb.combat.get_target()</h4><h4>orb.combat.set_target(t)</h4><h4>orb.combat.get_pos()</h4><h4>orb.combat.set_pos(t)</h4><h4>orb.combat.get_active()</h4><h4>orb.combat.set_active(t)</h4><h4>orb.combat.register_f_pre_attack(func)</h4><p>Parameters<br>
<code>function</code> func<br>
Return Value<br>
<code>void</code><br></p><pre><code class="lang-lua"><span class="local">local</span> <span class="orb">orb</span> = <span class="module">module</span>.<span class="module">internal</span><span class="include">(</span><span class="string">'<span class="orb">orb</span>'</span><span class="include">)</span>

<span class="local">local</span> <span class="keyword">function</span> on_pre_attack<span class="include">(</span><span class="include">)</span>
  <span class="print">print</span><span class="include">(</span><span class="string">'will attack'</span>, <span class="orb">orb</span>.combat.target<span class="include">)</span>
  <span class="note">--</span> you can set to <span class="keyword">new</span> target
  <span class="note">--</span> <span class="orb">orb</span>.combat.target = xxxx
<span class="keyword">end</span>

<span class="orb">orb</span>.combat.register_f_pre_attack<span class="include">(</span>on_pre_attack<span class="include">)</span></code></pre><h4>orb.combat.register_f_after_attack(func)</h4><p>Parameters<br>
<code>function</code> func<br>
Return Value<br>
<code>void</code><br></p><pre><code class="lang-lua"><span class="local">local</span> <span class="orb">orb</span> = <span class="module">module</span>.<span class="module">internal</span><span class="include">(</span><span class="string">'<span class="orb">orb</span>'</span><span class="include">)</span>

<span class="local">local</span> <span class="keyword">function</span> on_after_attack<span class="include">(</span><span class="include">)</span>
  <span class="print">print</span><span class="include">(</span><span class="string">'attack is on cooldown'</span><span class="include">)</span>
  <span class="note">--</span> you can <span class="keyword">return</span> true to block other callbacks <span class="include">(</span>Like callbacks registered by other plugin/<span class="module">module</span><span class="include">)</span>
  <span class="note">--</span> <span class="keyword">return</span> true 

  <span class="note">--</span> [on_after_attack] is called continuously, set bool to false can stop subsequent calls <span class="keyword">for</span> the current attack.
  <span class="note">--</span> <span class="orb">orb</span>.combat.set_invoke_after_attack<span class="include">(</span>bool<span class="include">)</span> 
<span class="keyword">end</span>

<span class="orb">orb</span>.combat.register_f_after_attack<span class="include">(</span>on_after_attack<span class="include">)</span></code></pre><h4>orb.combat.register_f_out_of_range(func)</h4><p>Parameters<br>
<code>function</code> func<br>
Return Value<br>
<code>void</code><br></p><pre><code class="lang-lua"><span class="local">local</span> <span class="orb">orb</span> = <span class="module">module</span>.<span class="module">internal</span><span class="include">(</span><span class="string">'<span class="orb">orb</span>'</span><span class="include">)</span>

<span class="local">local</span> <span class="keyword">function</span> out_of_range<span class="include">(</span><span class="include">)</span>
  <span class="print">print</span><span class="include">(</span><span class="string">'there is no target <span class="keyword">in</span> aa range'</span><span class="include">)</span>
  <span class="note">--</span> you can <span class="keyword">return</span> true to block other callbacks
  <span class="note">--</span> <span class="keyword">return</span> true 
<span class="keyword">end</span>

<span class="orb">orb</span>.combat.register_f_out_of_range<span class="include">(</span>out_of_range<span class="include">)</span></code></pre><h4>orb.combat.register_f_pre_tick(func)</h4><p>Parameters<br>
<code>function</code> func<br>
Return Value<br>
<code>void</code><br></p><pre><code class="lang-lua"><span class="local">local</span> <span class="orb">orb</span> = <span class="module">module</span>.<span class="module">internal</span><span class="include">(</span><span class="string">'<span class="orb">orb</span>'</span><span class="include">)</span>

<span class="local">local</span> <span class="keyword">function</span> on_tick<span class="include">(</span><span class="include">)</span> <span class="note">--</span>this fucntion is triggered prior to the orbs <span class="cbfunc">tick</span>
  <span class="note">--</span> you can <span class="keyword">return</span> true to block other callbacks
  <span class="note">--</span> <span class="keyword">return</span> true 
<span class="keyword">end</span>

<span class="orb">orb</span>.combat.register_f_pre_tick<span class="include">(</span>on_tick<span class="include">)</span></code></pre><h4>orb.combat.set_invoke_after_attack(val)</h4><p>Parameters<br>
<code>boolean</code> val<br>
Return Value<br>
<code>void</code><br></p><p>set to false, then orb will stop triggering [register_f_after_attack] events</p><pre><code class="lang-lua"><span class="local">local</span> <span class="orb">orb</span> = <span class="module">module</span>.<span class="module">internal</span><span class="include">(</span><span class="string">'<span class="orb">orb</span>'</span><span class="include">)</span>

<span class="local">local</span> <span class="keyword">function</span> after_attack<span class="include">(</span><span class="include">)</span>
  <span class="print">print</span><span class="include">(</span><span class="string">'attack is on cooldown'</span><span class="include">)</span>
  <span class="orb">orb</span>.combat.set_invoke_after_attack<span class="include">(</span>false<span class="include">)</span>
  <span class="note">--</span> you can <span class="keyword">return</span> true to block other callbacks
  <span class="note">--</span> <span class="keyword">return</span> true 
<span class="keyword">end</span>

<span class="orb">orb</span>.combat.register_f_after_attack<span class="include">(</span>after_attack<span class="include">)</span></code></pre><h4>orb.farm.clear_target</h4><p>Return Value<br>
<code>obj</code> the orbs current lane clear target<br></p><pre><code class="lang-lua"><span class="local">local</span> <span class="orb">orb</span> = <span class="module">module</span>.<span class="module">internal</span><span class="include">(</span><span class="string">'<span class="orb">orb</span>'</span><span class="include">)</span>

<span class="local">local</span> <span class="keyword">function</span> on_tick<span class="include">(</span><span class="include">)</span>
  <span class="keyword">if</span> <span class="orb">orb</span>.farm.clear_target <span class="keyword">then</span>
        <span class="print">print</span><span class="include">(</span><span class="orb">orb</span>.farm.clear_target.charName<span class="include">)</span>
    <span class="keyword">end</span>
<span class="keyword">end</span>

<span class="cb">cb</span>.<span class="add">add</span><span class="include">(</span><span class="cb">cb</span>.<span class="cbfunc">tick</span>, on_tick<span class="include">)</span></code></pre><h4>orb.farm.lane_clear_wait()</h4><p>Return Value<br>
<code>boolean</code> returns true if the orb is currently waiting to attack a minion that is soon to die (or waiting for siege)<br></p><pre><code class="lang-lua"><span class="local">local</span> <span class="orb">orb</span> = <span class="module">module</span>.<span class="module">internal</span><span class="include">(</span><span class="string">'<span class="orb">orb</span>'</span><span class="include">)</span>

<span class="local">local</span> <span class="keyword">function</span> on_tick<span class="include">(</span><span class="include">)</span>
  <span class="keyword">if</span> <span class="orb">orb</span>.farm.lane_clear_wait<span class="include">(</span><span class="include">)</span> <span class="keyword">then</span>
        <span class="print">print</span><span class="include">(</span><span class="string">'lane clear is waiting to attack a minion'</span><span class="include">)</span>
    <span class="keyword">end</span>
<span class="keyword">end</span>

<span class="cb">cb</span>.<span class="add">add</span><span class="include">(</span><span class="cb">cb</span>.<span class="cbfunc">tick</span>, on_tick<span class="include">)</span></code></pre><h4>orb.farm.lane_clear_wait_target</h4><p>Return Value<br>
<code>obj</code> the orbs current lane clear wait target<br></p><h4>orb.farm.predict_hp(obj, time)</h4><p>Parameters<br>
<code>minion.obj</code> obj<br>
<code>number</code> time<br>
Return Value<br>
<code>number</code> returns the minions health after time (seconds)<br></p><pre><code class="lang-lua"><span class="local">local</span> <span class="orb">orb</span> = <span class="module">module</span>.<span class="module">internal</span><span class="include">(</span><span class="string">'<span class="orb">orb</span>'</span><span class="include">)</span>

<span class="local">local</span> <span class="keyword">function</span> on_tick<span class="include">(</span><span class="include">)</span>
    <span class="keyword">for</span> i=<span class="number">0</span>, objManager.minions.size[TEAM_ENEMY]-<span class="number">1</span> <span class="keyword">do</span>
        <span class="local">local</span> obj = objManager.minions[TEAM_ENEMY][i]
        <span class="keyword">if</span> obj.isVisible <span class="keyword">then</span>
            <span class="local">local</span> hp = <span class="orb">orb</span>.farm.predict_hp<span class="include">(</span>obj, <span class="number"><span class="number">0</span>.<span class="number">25</span></span><span class="include">)</span>
            <span class="print">print</span><span class="include">(</span>obj.charName, obj.health, hp<span class="include">)</span>
        <span class="keyword">end</span>
    <span class="keyword">end</span>
<span class="keyword">end</span>

<span class="cb">cb</span>.<span class="add">add</span><span class="include">(</span><span class="cb">cb</span>.<span class="cbfunc">tick</span>, on_tick<span class="include">)</span></code></pre><h4>orb.farm.set_ignore(obj, time)</h4><p>Parameters<br>
<code>minion.obj</code> obj<br>
<code>number</code> time<br>
Return Value<br>
<code>void</code><br></p><h4>orb.farm.skill_farm_linear(input)</h4><p>Parameters<br>
<code>table</code> input<br>
Return Value<br>
<code>seg</code> returns a line segment<br>
<code>minion.obj</code> returns the minion object<br></p><pre><code class="lang-lua"><span class="note">--</span>based on mundo q
<span class="local">local</span> <span class="orb">orb</span> = <span class="module">module</span>.<span class="module">internal</span><span class="include">(</span><span class="string">'<span class="orb">orb</span>'</span><span class="include">)</span>
<span class="local">local</span> input = <span class="include">{</span>
    delay = <span class="number"><span class="number">0</span>.<span class="number">25</span></span>,
    speed = <span class="number">2000</span>,
    width = <span class="number">60</span>,
    boundingRadiusMod = <span class="number">1</span>,
    range = <span class="number">1050</span>,
    collision = <span class="include">{</span>
        minion=true,
        walls=true,
        <span class="hero">hero</span>=true,
    <span class="include">}</span>,
    damage = <span class="keyword">function</span><span class="include">(</span>m<span class="include">)</span>
        <span class="local">local</span> q_level = <span class="hero">player</span>:spellSlot<span class="include">(</span><span class="number">0</span><span class="include">)</span>.level
        <span class="keyword">return</span> math.min<span class="include">(</span><span class="number">250</span> + <span class="number">50</span>*q_level, math.max<span class="include">(</span><span class="number">30</span> + <span class="number">50</span>*q_level, m.health*<span class="include">(</span>.<span class="number">125</span> + .<span class="number">025</span>*q_level<span class="include">)</span><span class="include">)</span><span class="include">)</span>
    <span class="keyword">end</span>,
<span class="include">}</span>

<span class="local">local</span> <span class="keyword">function</span> on_tick<span class="include">(</span><span class="include">)</span>
    <span class="local">local</span> seg, obj = <span class="orb">orb</span>.farm.skill_farm_linear<span class="include">(</span>input<span class="include">)</span>
    <span class="keyword">if</span> seg <span class="keyword">then</span>
        <span class="hero">player</span>:castSpell<span class="include">(</span><span class="string">'pos'</span>, <span class="number">0</span>, seg.endPos<span class="include">)</span>
    <span class="keyword">end</span>
<span class="keyword">end</span>

<span class="cb">cb</span>.<span class="add">add</span><span class="include">(</span><span class="cb">cb</span>.<span class="cbfunc">tick</span>, on_tick<span class="include">)</span></code></pre><h4>orb.farm.skill_clear_linear(input)</h4><p>Parameters<br>
<code>table</code> input<br>
Return Value<br>
<code>seg</code> returns a line segment<br>
<code>minion.obj</code> returns the minion object<br></p><pre><code class="lang-lua"><span class="note">--</span>based on mundo q
<span class="local">local</span> <span class="orb">orb</span> = <span class="module">module</span>.<span class="module">internal</span><span class="include">(</span><span class="string">'<span class="orb">orb</span>'</span><span class="include">)</span>
<span class="local">local</span> input = <span class="include">{</span>
    delay = <span class="number"><span class="number">0</span>.<span class="number">25</span></span>,
    speed = <span class="number">2000</span>,
    width = <span class="number">60</span>,
    boundingRadiusMod = <span class="number">1</span>,
    range = <span class="number">1050</span>,
    collision = <span class="include">{</span>
        minion=true,
        walls=true,
        <span class="hero">hero</span>=true,
    <span class="include">}</span>,
    damage = <span class="keyword">function</span><span class="include">(</span>m<span class="include">)</span>
        <span class="local">local</span> q_level = <span class="hero">player</span>:spellSlot<span class="include">(</span><span class="number">0</span><span class="include">)</span>.level
        <span class="keyword">return</span> math.min<span class="include">(</span><span class="number">250</span> + <span class="number">50</span>*q_level, math.max<span class="include">(</span><span class="number">30</span> + <span class="number">50</span>*q_level, m.health*<span class="include">(</span>.<span class="number">125</span> + .<span class="number">025</span>*q_level<span class="include">)</span><span class="include">)</span><span class="include">)</span>
    <span class="keyword">end</span>,
<span class="include">}</span>

<span class="local">local</span> <span class="keyword">function</span> on_tick<span class="include">(</span><span class="include">)</span>
    <span class="local">local</span> seg, obj = <span class="orb">orb</span>.farm.skill_clear_linear<span class="include">(</span>input<span class="include">)</span>
    <span class="keyword">if</span> seg <span class="keyword">then</span>
        <span class="hero">player</span>:castSpell<span class="include">(</span><span class="string">'pos'</span>, <span class="number">0</span>, seg.endPos<span class="include">)</span>
    <span class="keyword">end</span>
<span class="keyword">end</span>

<span class="cb">cb</span>.<span class="add">add</span><span class="include">(</span><span class="cb">cb</span>.<span class="cbfunc">tick</span>, on_tick<span class="include">)</span></code></pre><h4>orb.farm.skill_farm_circular(input)</h4><p>Parameters<br>
<code>table</code> input<br>
Return Value<br>
<code>seg</code> returns a line segment<br>
<code>minion.obj</code> returns the minion object<br></p><pre><code class="lang-lua"><span class="note">--</span>based on karthus q
<span class="local">local</span> <span class="orb">orb</span> = <span class="module">module</span>.<span class="module">internal</span><span class="include">(</span><span class="string">'<span class="orb">orb</span>'</span><span class="include">)</span>
<span class="local">local</span> input = <span class="include">{</span>
    delay = <span class="number"><span class="number">0</span>.<span class="number">75</span></span>,
    speed = math.huge,
    radius = <span class="number">200</span>,
    boundingRadiusMod = <span class="number">0</span>,
    range = <span class="number">874</span>,
    damage = <span class="keyword">function</span><span class="include">(</span>m<span class="include">)</span>
        <span class="keyword">return</span> <span class="number">20</span>*<span class="hero">player</span>:spellSlot<span class="include">(</span><span class="number">0</span><span class="include">)</span>.level + <span class="number">30</span> + .<span class="number">3</span>*<span class="include">(</span><span class="hero">player</span>.flatMagicDamageMod*<span class="hero">player</span>.percentMagicDamageMod<span class="include">)</span>
    <span class="keyword">end</span>,
<span class="include">}</span>

<span class="local">local</span> <span class="keyword">function</span> on_tick<span class="include">(</span><span class="include">)</span>
    <span class="local">local</span> seg, obj = <span class="orb">orb</span>.farm.skill_farm_circular<span class="include">(</span>input<span class="include">)</span>
    <span class="keyword">if</span> seg <span class="keyword">then</span>
        <span class="hero">player</span>:castSpell<span class="include">(</span><span class="string">'pos'</span>, <span class="number">0</span>, seg.endPos<span class="include">)</span>
    <span class="keyword">end</span>
<span class="keyword">end</span>

<span class="cb">cb</span>.<span class="add">add</span><span class="include">(</span><span class="cb">cb</span>.<span class="cbfunc">tick</span>, on_tick<span class="include">)</span></code></pre><h4>orb.farm.skill_clear_circular(input)</h4><p>Parameters<br>
<code>table</code> input<br>
Return Value<br>
<code>seg</code> returns a line segment<br>
<code>minion.obj</code> returns the minion object<br></p><pre><code class="lang-lua"><span class="note">--</span>based on karthus q
<span class="local">local</span> <span class="orb">orb</span> = <span class="module">module</span>.<span class="module">internal</span><span class="include">(</span><span class="string">'<span class="orb">orb</span>'</span><span class="include">)</span>
<span class="local">local</span> input = <span class="include">{</span>
    delay = <span class="number"><span class="number">0</span>.<span class="number">75</span></span>,
    speed = math.huge,
    radius = <span class="number">200</span>,
    boundingRadiusMod = <span class="number">0</span>,
    range = <span class="number">874</span>,
    damage = <span class="keyword">function</span><span class="include">(</span>m<span class="include">)</span>
        <span class="keyword">return</span> <span class="number">20</span>*<span class="hero">player</span>:spellSlot<span class="include">(</span><span class="number">0</span><span class="include">)</span>.level + <span class="number">30</span> + .<span class="number">3</span>*<span class="include">(</span><span class="hero">player</span>.flatMagicDamageMod*<span class="hero">player</span>.percentMagicDamageMod<span class="include">)</span>
    <span class="keyword">end</span>,
<span class="include">}</span>

<span class="local">local</span> <span class="keyword">function</span> on_tick<span class="include">(</span><span class="include">)</span>
    <span class="local">local</span> seg, obj = <span class="orb">orb</span>.farm.skill_clear_circular<span class="include">(</span>input<span class="include">)</span>
    <span class="keyword">if</span> seg <span class="keyword">then</span>
        <span class="hero">player</span>:castSpell<span class="include">(</span><span class="string">'pos'</span>, <span class="number">0</span>, seg.endPos<span class="include">)</span>
    <span class="keyword">end</span>
<span class="keyword">end</span>

<span class="cb">cb</span>.<span class="add">add</span><span class="include">(</span><span class="cb">cb</span>.<span class="cbfunc">tick</span>, on_tick<span class="include">)</span></code></pre><h4>orb.farm.skill_farm_target(input)</h4><p>Parameters<br>
<code>table</code> input<br>
Return Value<br>
<code>seg</code> returns a line segment<br>
<code>minion.obj</code> returns the minion object<br></p><pre><code class="lang-lua"><span class="note">--</span>based on pantheon q <span class="include">(</span>****<span class="number">2019</span> version****<span class="include">)</span>
<span class="local">local</span> <span class="orb">orb</span> = <span class="module">module</span>.<span class="module">internal</span><span class="include">(</span><span class="string">'<span class="orb">orb</span>'</span><span class="include">)</span>
<span class="local">local</span> input = <span class="include">{</span>
    delay = <span class="number"><span class="number">0</span>.<span class="number">25</span></span>,
    speed = <span class="number">1500</span>,
    range = <span class="number">468</span>,
    damage = <span class="keyword">function</span><span class="include">(</span>target<span class="include">)</span>
        <span class="keyword">return</span> <span class="number">35</span> + <span class="number">40</span>*<span class="hero">player</span>:spellSlot<span class="include">(</span><span class="number">0</span><span class="include">)</span>.level + <span class="number"><span class="number">1</span>.<span class="number">4</span></span>*<span class="include">(</span><span class="include">(</span><span class="hero">player</span>.baseAttackDamage + <span class="hero">player</span>.flatPhysicalDamageMod<span class="include">)</span>*<span class="hero">player</span>.percentPhysicalDamageMod - <span class="hero">player</span>.baseAttackDamage<span class="include">)</span>
    <span class="keyword">end</span>,
<span class="include">}</span>

<span class="local">local</span> <span class="keyword">function</span> on_tick<span class="include">(</span><span class="include">)</span>
    <span class="local">local</span> seg, obj = <span class="orb">orb</span>.farm.skill_farm_target<span class="include">(</span>input<span class="include">)</span>
    <span class="keyword">if</span> obj <span class="keyword">then</span>
        <span class="hero">player</span>:castSpell<span class="include">(</span><span class="string">'obj'</span>, <span class="number">0</span>, obj<span class="include">)</span>
    <span class="keyword">end</span>
<span class="keyword">end</span>

<span class="cb">cb</span>.<span class="add">add</span><span class="include">(</span><span class="cb">cb</span>.<span class="cbfunc">tick</span>, on_tick<span class="include">)</span></code></pre><h4>orb.farm.skill_clear_target(input)</h4><p>Parameters<br>
<code>table</code> input<br>
Return Value<br>
<code>seg</code> returns a line segment<br>
<code>minion.obj</code> returns the minion object<br></p><pre><code class="lang-lua"><span class="note">--</span>based on pantheon q
<span class="local">local</span> <span class="orb">orb</span> = <span class="module">module</span>.<span class="module">internal</span><span class="include">(</span><span class="string">'<span class="orb">orb</span>'</span><span class="include">)</span>
<span class="local">local</span> input = <span class="include">{</span>
    delay = <span class="number"><span class="number">0</span>.<span class="number">25</span></span>,
    speed = <span class="number">1500</span>,
    range = <span class="number">468</span>,
    damage = <span class="keyword">function</span><span class="include">(</span>target<span class="include">)</span>
        <span class="keyword">return</span> <span class="number">35</span> + <span class="number">40</span>*<span class="hero">player</span>:spellSlot<span class="include">(</span><span class="number">0</span><span class="include">)</span>.level + <span class="number"><span class="number">1</span>.<span class="number">4</span></span>*<span class="include">(</span><span class="include">(</span><span class="hero">player</span>.baseAttackDamage + <span class="hero">player</span>.flatPhysicalDamageMod<span class="include">)</span>*<span class="hero">player</span>.percentPhysicalDamageMod - <span class="hero">player</span>.baseAttackDamage<span class="include">)</span>
    <span class="keyword">end</span>,
<span class="include">}</span>

<span class="local">local</span> <span class="keyword">function</span> on_tick<span class="include">(</span><span class="include">)</span>
    <span class="local">local</span> seg, obj = <span class="orb">orb</span>.farm.skill_clear_target<span class="include">(</span>input<span class="include">)</span>
    <span class="keyword">if</span> obj <span class="keyword">then</span>
        <span class="hero">player</span>:castSpell<span class="include">(</span><span class="string">'obj'</span>, <span class="number">0</span>, obj<span class="include">)</span>
    <span class="keyword">end</span>
<span class="keyword">end</span>

<span class="cb">cb</span>.<span class="add">add</span><span class="include">(</span><span class="cb">cb</span>.<span class="cbfunc">tick</span>, on_tick<span class="include">)</span></code></pre><h4>orb.farm.set_clear_target(obj)</h4><p>Parameters<br>
<code>minion.obj</code> obj<br>
Return Value<br>
<code>void</code><br></p><h4>orb.utility.get_missile_speed(obj)</h4><p>Parameters<br>
<code>obj</code> obj<br>
Return Value<br>
<code>number</code> returns the obj attack missile speed<br></p><h4>orb.utility.get_wind_up_time(obj)</h4><p>Parameters<br>
<code>obj</code> obj<br>
Return Value<br>
<code>number</code> returns the obj attacks wind up time<br></p><h4>orb.utility.get_damage_mod(obj)</h4><p>Parameters<br>
<code>obj</code> obj<br>
Return Value<br>
<code>number</code> returns the obj damage mod (wards ex)<br></p><h4>orb.utility.get_damage(source, target, add_bonus)</h4><p>Parameters<br>
<code>obj</code> source<br>
<code>obj</code> target<br>
<code>boolean</code> add_bonus<br>
Return Value<br>
<code>number</code> returns attack damage done to target by source<br></p><h4>orb.utility.get_hit_time(source, target)</h4><p>Parameters<br>
<code>obj</code> source<br>
<code>obj</code> target<br>
Return Value<br>
<code>number</code> returns time in seconds for an attack from source to hit target<br></p><h4>orb.ts</h4><p>Return Value<br>
<code>TS</code> returns the orbs target selector instance<br></p><h4>orb.menu.combat.key:get()</h4><p>Return Value<br>
<code>boolean</code> returns true if combat mode is active<br></p><h4>orb.menu.lane_clear.key:get()</h4><p>Return Value<br>
<code>boolean</code> returns true lane clear mode is active<br></p><h4>orb.menu.last_hit.key:get()</h4><p>Return Value<br>
<code>boolean</code> returns true last hit mode is active<br></p><h4>orb.menu.hybrid.key:get()</h4><p>Return Value<br>
<code>boolean</code> returns true if hybrid mode is active<br></p><h3 id="modules-evade">evade</h3><p>use os.clock()<br></p><h4>evade.core.set_server_pause()</h4><p>Return Value<br>
<code>void</code><br></p><p>this is deprecated, use <code>evade.core.set_pause</code> instead</p><h4>evade.core.set_pause(t)</h4><p>Parameters<br>
<code>number</code> t<br>
Return Value<br>
<code>void</code><br><br>
pauses evade from issuing movement orders (will still update orbs path)<br></p><pre><code class="lang-lua"><span class="local">local</span> evade = <span class="module">module</span>.seek<span class="include">(</span><span class="string">'evade'</span><span class="include">)</span>
<span class="keyword">if</span> evade <span class="keyword">then</span>
    evade.core.set_pause<span class="include">(</span><span class="number">3</span><span class="include">)</span> <span class="note">--</span>pauses the evade from taking action <span class="keyword">for</span> <span class="number">3</span> seconds
<span class="keyword">end</span></code></pre><h4>evade.core.is_paused()</h4><p>Return Value<br>
<code>boolean</code> returns true if evade is currently paused<br></p><h4>evade.core.is_active()</h4><p>Return Value<br>
<code>boolean</code> returns true if evade is currently evading a spell<br><br>
should be checked before casting any movement impairing spells<br></p><h4>evade.core.is_action_safe(v1, speed, delay)</h4><p>Parameters<br>
<code>vec2\vec3</code> v1<br>
<code>number</code> speed<br>
<code>number</code> delay<br>
Return Value<br>
<code>boolean</code> returns true if action is safe<br></p><pre><code class="lang-lua"><span class="note">--</span>this example is based on vayne q
<span class="local">local</span> evade = <span class="module">module</span>.seek<span class="include">(</span><span class="string">'evade'</span><span class="include">)</span>

<span class="local">local</span> <span class="keyword">function</span> on_tick<span class="include">(</span><span class="include">)</span>
    <span class="keyword">if</span> <span class="hero">player</span>:spellSlot<span class="include">(</span><span class="number">0</span><span class="include">)</span>.state~=<span class="number">0</span> <span class="keyword">then</span> <span class="keyword">return</span> <span class="keyword">end</span>

    <span class="local">local</span> pos = <span class="hero">player</span>.<span class="cbfunc">path</span>.serverPos<span class="number">2</span>D + <span class="include">(</span>mousePos<span class="number">2</span>D - <span class="hero">player</span>.<span class="cbfunc">path</span>.serverPos<span class="number">2</span>D<span class="include">)</span>:norm<span class="include">(</span><span class="include">)</span>*<span class="number">300</span>
    <span class="keyword">if</span> evade and evade.core.is_action_safe<span class="include">(</span>pos, <span class="number">500</span> + <span class="hero">player</span>.moveSpeed, <span class="number">0</span><span class="include">)</span> <span class="keyword">then</span>
        <span class="hero">player</span>:castSpell<span class="include">(</span><span class="string">'pos'</span>, <span class="number">0</span>, vec<span class="number">3</span><span class="include">(</span>pos.x, mousePos.y, pos.y<span class="include">)</span><span class="include">)</span>
    <span class="keyword">end</span>
<span class="keyword">end</span>

<span class="cb">cb</span>.<span class="add">add</span><span class="include">(</span><span class="cb">cb</span>.<span class="cbfunc">tick</span>, on_tick<span class="include">)</span></code></pre><h4>evade.core.get_anchor()</h4><p>Return Value<br>
<code>void</code> returns the anchor for current evading direction<br></p><h4>evade.core.get_pos()</h4><p>Return Value<br>
<code>void</code> returns the target position of current evading<br></p><h4>evade.damage</h4><p><br></p><pre><code class="lang-lua"><span class="local">local</span> ad_damage, ap_damage, true_damage, buff_list = evade.damage.count<span class="include">(</span><span class="hero">player</span><span class="include">)</span>
<span class="note">--</span>these are reduced/modified by armor/buffs
<span class="note">--</span>buff_list contains an array of all incoming buff types</code></pre><h4>evade.core.skillshots</h4><p><br>
Not recommended, prefer <code>evade.core.register_on_create_spell</code></p><pre><code class="lang-lua"><span class="keyword">for</span> i=evade.core.skillshots.n, <span class="number">1</span>, -<span class="number">1</span> <span class="keyword">do</span>
  <span class="local">local</span> <span class="cbfunc">spell</span> = evade.core.skillshots[i]
  <span class="note">--</span><span class="cbfunc">spell</span>.name
  <span class="note">--</span><span class="cbfunc">spell</span>.start_time <span class="note">--</span> based on os.clock<span class="include">(</span><span class="include">)</span>
  <span class="note">--</span><span class="cbfunc">spell</span>.end_time <span class="note">--</span> based on os.clock<span class="include">(</span><span class="include">)</span>
  <span class="note">--</span><span class="cbfunc">spell</span>.owner
  <span class="note">--</span><span class="cbfunc">spell</span>.danger_level
  <span class="note">--</span><span class="cbfunc">spell</span>.start_pos
  <span class="note">--</span><span class="cbfunc">spell</span>.end_pos
  <span class="note">--</span><span class="cbfunc">spell</span>.damage <span class="note">--</span> totalDamage
  <span class="note">--</span><span class="cbfunc">spell</span>.data <span class="note">--</span> assorted static data
  <span class="note">--</span><span class="cbfunc">spell</span>:contains<span class="include">(</span>pos<span class="number">2</span>D/obj<span class="include">)</span>
  <span class="note">--</span><span class="cbfunc">spell</span>:get_hit_time<span class="include">(</span>pos<span class="number">2</span>D<span class="include">)</span>
  <span class="note">--</span><span class="cbfunc">spell</span>:get_hit_remaining_time<span class="include">(</span>pos<span class="number">2</span>D<span class="include">)</span>
  <span class="note">--</span><span class="cbfunc">spell</span>:get_damage<span class="include">(</span>target<span class="include">)</span>

  <span class="local">local</span> ad_damage,ap_damage,true_damage,buff_list = <span class="cbfunc">spell</span>:get_damage<span class="include">(</span><span class="hero">player</span><span class="include">)</span>

  <span class="keyword">if</span> <span class="cbfunc">spell</span>:contains<span class="include">(</span>game.mousePos<span class="number">2</span>D<span class="include">)</span> <span class="keyword">then</span>
    <span class="note">--</span>mouse is inside of <span class="string">'<span class="cbfunc">spell</span>'</span>
  <span class="keyword">end</span>

  <span class="keyword">if</span> <span class="cbfunc">spell</span>:contains<span class="include">(</span><span class="hero">player</span><span class="include">)</span> <span class="keyword">then</span>
    <span class="note">--</span><span class="hero">player</span> is inside of <span class="string">'<span class="cbfunc">spell</span>'</span>, this accounts <span class="keyword">for</span> obj boundingRadius
  <span class="keyword">end</span>

  <span class="keyword">if</span> <span class="cbfunc">spell</span>:intersection<span class="include">(</span><span class="hero">player</span>.pos<span class="number">2</span>D, game.mousePos<span class="number">2</span>D<span class="include">)</span> <span class="keyword">then</span>
    <span class="note">--</span>line seg <span class="hero">player</span>-&gt;mousePos intersects <span class="string">'<span class="cbfunc">spell</span>'</span>
  <span class="keyword">end</span>
<span class="keyword">end</span></code></pre><h4>evade.core.targeted</h4><p><br>
Not recommended, prefer <code>evade.core.register_on_create_spell</code></p><pre><code class="lang-lua"><span class="keyword">for</span> i=evade.core.targeted.n, <span class="number">1</span>, -<span class="number">1</span> <span class="keyword">do</span>
  <span class="local">local</span> <span class="cbfunc">spell</span> = evade.core.targeted[i]
  <span class="note">--</span><span class="cbfunc">spell</span>.name
  <span class="note">--</span><span class="cbfunc">spell</span>.start_time
  <span class="note">--</span><span class="cbfunc">spell</span>.end_time
  <span class="note">--</span><span class="cbfunc">spell</span>.owner
  <span class="note">--</span><span class="cbfunc">spell</span>.target
  <span class="note">--</span><span class="cbfunc">spell</span>.missile
  <span class="note">--</span><span class="cbfunc">spell</span>.data <span class="note">--</span> assorted static data
<span class="keyword">end</span></code></pre><h4>evade.core.register_on_create_spell</h4><p><br></p><pre><code class="lang-lua">evade.core.register_on_create_spell<span class="include">(</span><span class="keyword">function</span> <span class="include">(</span>skillshot<span class="include">)</span>

  <span class="keyword">if</span> not skillshot:contains<span class="include">(</span><span class="hero">player</span><span class="include">)</span> <span class="keyword">then</span>
    <span class="keyword">return</span>
  <span class="keyword">end</span>

  <span class="local">local</span> ad_damage,ap_damage,true_damage,buff_list = skillshot:get_damage<span class="include">(</span><span class="hero">player</span><span class="include">)</span> <span class="note">--</span> show damage to self
  <span class="print">print</span><span class="include">(</span><span class="string">'create skillshot: '</span>, 
    skillshot.name, 
    skillshot.owner and skillshot.owner.charName or <span class="string">"nil"</span>, 
    <span class="string">"time: "</span>,
    string.format<span class="include">(</span><span class="string">"%.<span class="number">2</span>f"</span>, skillshot.start_time<span class="include">)</span>, 
    string.format<span class="include">(</span><span class="string">"%.<span class="number">2</span>f"</span>, skillshot.end_time<span class="include">)</span>, <span class="note">--</span> This is only an approximate time
    <span class="string">"target: "</span>,
    skillshot.target and skillshot.target.charName or <span class="string">"nil"</span>,
    <span class="string">"damage: "</span>,
    ad_damage,ap_damage,true_damage,<span class="comment">#buff_list</span>
  <span class="include">)</span>

  <span class="note">--</span> example <span class="keyword">for</span> anti ViR:
  <span class="keyword">if</span> skillshot.name == <span class="string">"ViR"</span> <span class="keyword">then</span>
    delay_action<span class="include">(</span>skillshot.end_time - os.clock<span class="include">(</span><span class="include">)</span> - <span class="number"><span class="number">0</span>.<span class="number">1</span></span>, <span class="keyword">function</span><span class="include">(</span><span class="include">)</span> use_zhonya<span class="include">(</span><span class="include">)</span> <span class="keyword">end</span><span class="include">)</span>
  <span class="keyword">end</span>

<span class="keyword">end</span><span class="include">)</span></code></pre><h4>evade.damage.count</h4><pre><code class="lang-lua"><span class="local">local</span> ad_damage, ap_damage, true_damage, buff_list = evade.damage.count<span class="include">(</span><span class="hero">player</span><span class="include">)</span></code></pre><h3 id="modules-damagelib">damagelib</h3><h4>damagelib.handlers</h4><p>Insert your own handlers if internal damagelib is not ok.<br></p><pre><code class="lang-lua"><span class="local">local</span> damagelib = <span class="module">module</span>.<span class="module">internal</span><span class="include">(</span><span class="string">'damagelib'</span><span class="include">)</span>
<span class="local">local</span> handlers = damagelib.handlers

<span class="local">local</span> FlashFrost = game.spellhash<span class="include">(</span><span class="string">'FlashFrost'</span><span class="include">)</span>
<span class="local">local</span> TotalPassthroughDamage = game.fnvhash<span class="include">(</span><span class="string">'TotalPassthroughDamage'</span><span class="include">)</span>
<span class="local">local</span> TotalExplosionDamage = game.fnvhash<span class="include">(</span><span class="string">'TotalExplosionDamage'</span><span class="include">)</span>

handlers[FlashFrost] = <span class="keyword">function</span> <span class="include">(</span>source, target, is_raw_damage, stage<span class="include">)</span>
    <span class="print">print</span><span class="include">(</span><span class="string">'FlashFrost: lua handlers called'</span><span class="include">)</span>
    <span class="local">local</span> spell_slot = source:spellSlot<span class="include">(</span><span class="number">0</span><span class="include">)</span>
    <span class="keyword">if</span> not spell_slot <span class="keyword">then</span>
        <span class="keyword">return</span> damage_result<span class="include">(</span><span class="number">0</span>, <span class="number">0</span>, <span class="number">0</span><span class="include">)</span>
    <span class="keyword">end</span>
    <span class="local">local</span> raw_damage = spell_slot:calculate<span class="include">(</span><span class="number">0</span>, TotalPassthroughDamage<span class="include">)</span> + spell_slot:calculate<span class="include">(</span><span class="number">0</span>, TotalExplosionDamage<span class="include">)</span>
    <span class="keyword">if</span> is_raw_damage or not target or not target.valid <span class="keyword">then</span>
        <span class="keyword">return</span> damage_result<span class="include">(</span><span class="number">0</span>, raw_damage, <span class="number">0</span><span class="include">)</span>
    <span class="keyword">end</span>

    <span class="note">--</span> <span class="keyword">return</span> damage: ad,ap,true
    <span class="keyword">return</span> damage_result<span class="include">(</span><span class="number">0</span>, damagelib.calc_magical_damage<span class="include">(</span>source, target, raw_damage<span class="include">)</span>, <span class="number">0</span><span class="include">)</span>
<span class="keyword">end</span>


<span class="note">--</span> <span class="print">print</span> damage
<span class="local">local</span> total_damage, ad_damage, ap_damage, true_damage = damagelib.get_spell_damage<span class="include">(</span><span class="string">'FlashFrost'</span>, <span class="number">0</span>, <span class="hero">player</span>, g_target, true, <span class="number">0</span><span class="include">)</span>
<span class="print">print</span><span class="include">(</span>total_damage, ad_damage, ap_damage, true_damage<span class="include">)</span></code></pre><h4>damagelib.get_spell_damage(spellName, spellSlot, source, target, isRawDamage, stage)</h4><p>Parameters<br>
<code>string</code> spellName<br>
<code>number</code> spellSlot<br>
<code>obj</code> source<br>
<code>obj</code> target<br>
<code>boolean</code> isRawDamage, true = only spell damage, false = include runes/items/buffs/armors/shieds<br>
<code>number</code> stage<br>
Return Value<br>
<code>number</code>,<code>number</code>,<code>number</code>,<code>number</code> total,ad,ap,true<br></p><pre><code class="lang-lua"><span class="local">local</span> damagelib = <span class="module">module</span>.<span class="module">internal</span><span class="include">(</span><span class="string">'damagelib'</span><span class="include">)</span>

<span class="note">--</span> Briar
<span class="print">print</span><span class="include">(</span><span class="string">'Passive min'</span>, damagelib.get_spell_damage<span class="include">(</span><span class="string">'BriarP'</span>, <span class="number">63</span>, <span class="hero">player</span>, g_target, false, <span class="number">0</span><span class="include">)</span><span class="include">)</span>
<span class="print">print</span><span class="include">(</span><span class="string">'Passive max'</span>, damagelib.get_spell_damage<span class="include">(</span><span class="string">'BriarP'</span>, <span class="number">63</span>, <span class="hero">player</span>, g_target, false, <span class="number">1</span><span class="include">)</span><span class="include">)</span>
<span class="print">print</span><span class="include">(</span><span class="string">'Q'</span>, damagelib.get_spell_damage<span class="include">(</span><span class="string">'BriarQ'</span>, <span class="number">0</span>, <span class="hero">player</span>, g_target, false, <span class="number">0</span><span class="include">)</span><span class="include">)</span>
<span class="print">print</span><span class="include">(</span><span class="string">'W'</span>, damagelib.get_spell_damage<span class="include">(</span><span class="string">'BriarW'</span>, <span class="number">1</span>, <span class="hero">player</span>, g_target, false, <span class="number">0</span><span class="include">)</span><span class="include">)</span>
<span class="print">print</span><span class="include">(</span><span class="string">'W<span class="number">2</span>'</span>, damagelib.get_spell_damage<span class="include">(</span><span class="string">'BriarWAttackSpell'</span>, <span class="number">1</span>, <span class="hero">player</span>, g_target, false, <span class="number">0</span><span class="include">)</span><span class="include">)</span> <span class="note">--</span> available when W<span class="number">2</span> ready
<span class="print">print</span><span class="include">(</span><span class="string">'E'</span>, damagelib.get_spell_damage<span class="include">(</span><span class="string">'BriarE'</span>, <span class="number">2</span>, <span class="hero">player</span>, g_target, false, <span class="number">0</span><span class="include">)</span><span class="include">)</span>
<span class="print">print</span><span class="include">(</span><span class="string">'R'</span>, damagelib.get_spell_damage<span class="include">(</span><span class="string">'BriarR'</span>, <span class="number">3</span>, <span class="hero">player</span>, g_target, false, <span class="number">0</span><span class="include">)</span><span class="include">)</span></code></pre><h4>damagelib.calc_aa_damage(source, target, includeOnHit)</h4><p>Calc the real damage after shields, etc.<br>
Parameters<br>
<code>obj</code> source<br>
<code>obj</code> target<br>
<code>boolean</code> include <code>calc_on_hit_damage</code> or not<br>
Return Value<br>
<code>number</code><br></p><h4>damagelib.calc_physical_damage(source, target, rawDamage)</h4><p>Calc the real damage after shields, etc.<br>
Parameters<br>
<code>obj</code> source<br>
<code>obj</code> target<br>
<code>number</code> rawDamage<br>
Return Value<br>
<code>number</code><br></p><h4>damagelib.calc_magical_damage(source, target, rawDamage)</h4><p>Calc the real damage after shields, etc.<br>
Parameters<br>
<code>obj</code> source<br>
<code>obj</code> target<br>
<code>number</code> rawDamage<br>
Return Value<br>
<code>number</code><br></p><h4>damagelib.calc_on_hit_damage(source, target, isAutoAttack)</h4><p>Calc the extra on hit damage by passive/buffs/items/perks.<br>
Parameters<br>
<code>obj</code> source<br>
<code>obj</code> target<br>
<code>boolean</code> isAutoAttack<br>
Return Value<br>
<code>number</code>,<code>number</code>,<code>number</code>,<code>number</code>  total,ad,ap,true<br></p><pre><code class="lang-lua"><span class="local">local</span> total_damage,ad_damage,ap_damage,true_damage = damagelib.calc_on_hit_damage<span class="include">(</span><span class="hero">player</span>, target, true<span class="include">)</span></code></pre><h3 id="modules-ts">TS</h3><h4>TS.get_result(func, filter, ign_sel, hard)</h4><p>Parameters<br>
<code>function</code> func<br>
<code>table</code> filter[optional]<br>
<code>boolean</code> ign_sel[optional]<br>
<code>boolean</code> hard[optional]<br>
Return Value<br>
<code>table</code><br></p><pre><code class="lang-lua"><span class="note">--</span>this example is based on annie q
<span class="local">local</span> <span class="orb">ts</span> = <span class="module">module</span>.<span class="module">internal</span><span class="include">(</span><span class="string">'TS'</span><span class="include">)</span>
<span class="local">local</span> <span class="orb">pred</span> = <span class="module">module</span>.<span class="module">internal</span><span class="include">(</span><span class="string">'<span class="orb">pred</span>'</span><span class="include">)</span>
<span class="local">local</span> input = <span class="include">{</span>
  delay = <span class="number">0</span>,
  radius = <span class="number">505</span>,
  dashRadius = <span class="number">0</span>,
  boundingRadiusModSource = <span class="number">1</span>,
  boundingRadiusModTarget = <span class="number">1</span>,
<span class="include">}</span>

<span class="local">local</span> <span class="keyword">function</span> ts_filter<span class="include">(</span>res, obj, dist<span class="include">)</span>
    <span class="note">--</span><span class="keyword">return</span> false <span class="keyword">for</span> any objects much too far away
    <span class="keyword">if</span> dist &gt; <span class="number">800</span> <span class="keyword">then</span> <span class="keyword">return</span> false <span class="keyword">end</span>

    <span class="note">--</span><span class="orb">pred</span>.present.get_prediction checks that obj is <span class="keyword">in</span> range <span class="keyword">for</span> q
    <span class="keyword">if</span> <span class="orb">pred</span>.present.get_prediction<span class="include">(</span>input, obj<span class="include">)</span> <span class="keyword">then</span>
        <span class="note">--</span>res.obj is arbitrary and could be named anything
        <span class="note">--</span>additionally, anything you like can be added to the res table
        res.obj = obj
        <span class="keyword">return</span> true
    <span class="keyword">end</span>
<span class="keyword">end</span>

<span class="local">local</span> <span class="keyword">function</span> on_tick<span class="include">(</span><span class="include">)</span>
    <span class="note">--</span>check that q is ready
    <span class="keyword">if</span> <span class="hero">player</span>:spellSlot<span class="include">(</span><span class="number">0</span><span class="include">)</span>.state~=<span class="number">0</span> <span class="keyword">then</span> <span class="keyword">return</span> <span class="keyword">end</span>

    <span class="note">--</span><span class="orb">ts</span>.get_result loops through all valid enemies
    <span class="note">--</span>simple usage
    <span class="local">local</span> res = <span class="orb">ts</span>.get_result<span class="include">(</span>ts_filter<span class="include">)</span>
    <span class="keyword">if</span> res.obj <span class="keyword">then</span>
        <span class="hero">player</span>:castSpell<span class="include">(</span><span class="string">'obj'</span>, <span class="number">0</span>, res.obj<span class="include">)</span>
    <span class="keyword">end</span>

    <span class="note">--</span>alternative usages
    <span class="local">local</span> res = <span class="orb">ts</span>.get_result<span class="include">(</span>ts_filter, <span class="orb">ts</span>.filter_set[<span class="number">2</span>]<span class="include">)</span> <span class="note">--</span>uses filter LESS_CAST_AD_PRIO <span class="include">(</span>overrides menu<span class="include">)</span>
    <span class="local">local</span> res = <span class="orb">ts</span>.get_result<span class="include">(</span>ts_filter, nil, true<span class="include">)</span> <span class="note">--</span>ignores <span class="orb">ts</span> selected target
    <span class="local">local</span> res = <span class="orb">ts</span>.get_result<span class="include">(</span>ts_filter, nil, nil, true<span class="include">)</span> <span class="note">--</span>forces <span class="orb">ts</span> selected target to be returned <span class="include">(</span>overrides menu<span class="include">)</span>
<span class="keyword">end</span>

<span class="cb">cb</span>.<span class="add">add</span><span class="include">(</span><span class="cb">cb</span>.<span class="cbfunc">tick</span>, on_tick<span class="include">)</span></code></pre><h4>TS.get_active_filter()</h4><p>Return Value<br>
<code>table</code><br></p><pre><code class="lang-lua"><span class="local">local</span> <span class="orb">ts</span> = <span class="module">module</span>.<span class="module">internal</span><span class="include">(</span><span class="string">'TS'</span><span class="include">)</span>
<span class="print">print</span><span class="include">(</span><span class="orb">ts</span>.get_active_filter<span class="include">(</span><span class="include">)</span>.name<span class="include">)</span></code></pre><h4>TS.loop(func, filter)</h4><p>Parameters<br>
<code>function</code> func<br>
<code>table</code> filter[optional]<br>
Return Value<br>
<code>table</code><br></p><pre><code class="lang-lua"><span class="local">local</span> <span class="orb">ts</span> = <span class="module">module</span>.<span class="module">internal</span><span class="include">(</span><span class="string">'TS'</span><span class="include">)</span>

<span class="local">local</span> <span class="keyword">function</span> loop_filter<span class="include">(</span>res, obj, dist<span class="include">)</span>
    <span class="note">--</span> any condition can be set here
    <span class="keyword">if</span> dist &lt; <span class="number">800</span> <span class="keyword">then</span>
        table.insert<span class="include">(</span>res, obj<span class="include">)</span>
    <span class="keyword">end</span>
<span class="keyword">end</span>

<span class="local">local</span> <span class="keyword">function</span> on_tick<span class="include">(</span><span class="include">)</span>
    <span class="local">local</span> res = <span class="orb">ts</span>.loop<span class="include">(</span>loop_filter<span class="include">)</span>
    <span class="note">--</span>res will contain all valid enemies within <span class="number">800</span> units
<span class="keyword">end</span>

<span class="cb">cb</span>.<span class="add">add</span><span class="include">(</span><span class="cb">cb</span>.<span class="cbfunc">tick</span>, on_tick<span class="include">)</span></code></pre><h4>TS.loop_allies(func, filter)</h4><p>Parameters<br>
<code>function</code> func<br>
<code>table</code> filter[optional]<br>
Return Value<br>
<code>table</code><br></p><pre><code class="lang-lua"><span class="local">local</span> <span class="orb">ts</span> = <span class="module">module</span>.<span class="module">internal</span><span class="include">(</span><span class="string">'TS'</span><span class="include">)</span>

<span class="local">local</span> <span class="keyword">function</span> loop_filter<span class="include">(</span>res, obj, dist<span class="include">)</span>
    <span class="note">--</span> any condition can be set here
    <span class="keyword">if</span> dist &lt; <span class="number">800</span> <span class="keyword">then</span>
        table.insert<span class="include">(</span>res, obj<span class="include">)</span>
    <span class="keyword">end</span>
<span class="keyword">end</span>

<span class="local">local</span> <span class="keyword">function</span> on_tick<span class="include">(</span><span class="include">)</span>
    <span class="local">local</span> res = <span class="orb">ts</span>.loop_allies<span class="include">(</span>loop_filter<span class="include">)</span>
    <span class="note">--</span>res will contain all valid allies within <span class="number">800</span> units
<span class="keyword">end</span>

<span class="cb">cb</span>.<span class="add">add</span><span class="include">(</span><span class="cb">cb</span>.<span class="cbfunc">tick</span>, on_tick<span class="include">)</span></code></pre><h4>TS.filter.new()</h4><p>Return Value<br>
<code>table</code><br></p><pre><code class="lang-lua"><span class="local">local</span> <span class="orb">ts</span> = <span class="module">module</span>.<span class="module">internal</span><span class="include">(</span><span class="string">'TS'</span><span class="include">)</span>

<span class="local">local</span> my_filter = <span class="orb">ts</span>.filter.<span class="keyword">new</span><span class="include">(</span><span class="include">)</span>
my_filter.name = <span class="string">'LEAST_MANA'</span>
my_filter.rank = <span class="include">{</span><span class="number"><span class="number">0</span>.<span class="number">33</span></span>, <span class="number"><span class="number">0</span>.<span class="number">66</span></span>, <span class="number"><span class="number">1</span>.<span class="number">0</span></span>, <span class="number"><span class="number">1</span>.<span class="number">33</span></span>, <span class="number"><span class="number">1</span>.<span class="number">66</span></span><span class="include">}</span> <span class="note">--</span>these correspond to character priorities set <span class="keyword">in</span> the menu, the lower the ratio the higher priority will be given
my_filter.index = <span class="keyword">function</span><span class="include">(</span>obj, rank_val<span class="include">)</span>
    <span class="keyword">return</span> obj.par
    <span class="note">--</span>alternatively, this will <span class="keyword">return</span> the target with the least mana adjusted by priority
    <span class="note">--</span> <span class="keyword">return</span> obj.par * rank_value
<span class="keyword">end</span>

<span class="local">local</span> <span class="keyword">function</span> ts_filter<span class="include">(</span>res, obj, dist<span class="include">)</span>
    <span class="keyword">if</span> dist &lt; <span class="number">800</span> <span class="keyword">then</span>
        res.obj = obj
        <span class="keyword">return</span> true
    <span class="keyword">end</span>
<span class="keyword">end</span>

<span class="local">local</span> res = <span class="orb">ts</span>.get_result<span class="include">(</span>ts_filter, my_filter<span class="include">)</span></code></pre><h4>TS.filter_set</h4><p>Return Value<br>
<code>table</code><br></p><pre><code class="lang-lua"><span class="local">local</span> <span class="orb">ts</span> = <span class="module">module</span>.<span class="module">internal</span><span class="include">(</span><span class="string">'TS'</span><span class="include">)</span>
<span class="keyword">for</span> Nebelwolfi=<span class="number">1</span>, <span class="comment">#<span class="orb">ts</span>.filter_set <span class="keyword">do</span></span>
    <span class="print">print</span><span class="include">(</span><span class="orb">ts</span>.filter_set[Nebelwolfi].name<span class="include">)</span>
<span class="keyword">end</span></code></pre><h4>TS.selected</h4><p>Return Value<br>
<code>obj</code> current selected obj<br></p><h1 id="globals">Globals</h1><ul>
<li>player</li>
<li>mousePos</li>
<li>mousePos2D</li>
<li>TYPE_TROY</li>
<li>TYPE_HERO</li>
<li>TYPE_MINION</li>
<li>TYPE_MISSILE</li>
<li>TYPE_TURRET</li>
<li>TYPE_INHIB</li>
<li>TYPE_NEXUS</li>
<li>TYPE_SPAWN</li>
<li>TYPE_SHOP</li>
<li>TYPE_CAMP</li>
<li>TEAM_ALLY</li>
<li>TEAM_ENEMY</li>
<li>TEAM_NEUTRAL</li>
<li>_Q</li>
<li>_W</li>
<li>_E</li>
<li>_R</li>
<li>SUMMONER_1</li>
<li>SUMMONER_2</li>
<li>COLOR_RED</li>
<li>COLOR_GREEN</li>
<li>COLOR_BLUE</li>
<li>COLOR_YELLOW</li>
<li>COLOR_AQUA</li>
<li>COLOR_PURPLE</li>
<li>COLOR_BLACK</li>
<li>COLOR_WHITE</li>
<li>BUFF_INTERNAL</li>
<li>BUFF_AURA</li>
<li>BUFF_COMBATENCHANCER</li>
<li>BUFF_COMBATDEHANCER</li>
<li>BUFF_SPELLSHIELD</li>
<li>BUFF_STUN</li>
<li>BUFF_INVISIBILITY</li>
<li>BUFF_SILENCE</li>
<li>BUFF_TAUNT</li>
<li>BUFF_BERSERK</li>
<li>BUFF_POLYMORPH</li>
<li>BUFF_SLOW</li>
<li>BUFF_SNARE</li>
<li>BUFF_DAMAGE</li>
<li>BUFF_HEAL</li>
<li>BUFF_HASTE</li>
<li>BUFF_SPELLIMMUNITY</li>
<li>BUFF_PHYSICALIMMUNITY</li>
<li>BUFF_INVULNERABILITY</li>
<li>BUFF_ATTACKSPEEDSLOW</li>
<li>BUFF_NEARSIGHT</li>
<li>BUFF_CURRENCY</li>
<li>BUFF_FEAR</li>
<li>BUFF_CHARM</li>
<li>BUFF_POISON</li>
<li>BUFF_SUPPRESSION</li>
<li>BUFF_BLIND</li>
<li>BUFF_COUNTER</li>
<li>BUFF_SHRED</li>
<li>BUFF_FLEE</li>
<li>BUFF_KNOCKUP</li>
<li>BUFF_KNOCKBACK</li>
<li>BUFF_DISARM</li>
<li>BUFF_GROUNDED</li>
<li>BUFF_DROWSY</li>
<li>BUFF_ASLEEP</li>
<li>BUFF_OBSCURED</li>
<li>BUFF_CLICKPROOFTOENEMIES</li>
<li>BUFF_UNKILLABLE</li>
</ul><h1 id="examples">Examples</h1><h3 id="examples-callbacks">Callbacks</h3><p>The following callbacks have no arguments:<br></p><ul>
<li>cb.pre_tick</li>
<li>cb.tick</li>
<li>cb.draw_first</li>
<li>cb.draw</li>
<li>cb.sprite</li>
</ul><p>cb.draw_first is triggered before cb.sprite, while cb.draw is triggered after cb.sprite.</p><h4>cb.keydown and cb.keyup</h4><p>Both have a single arg, key_code:<br></p><pre><code class="lang-lua"><span class="local">local</span> <span class="keyword">function</span> on_key_down<span class="include">(</span>k<span class="include">)</span>
    <span class="keyword">if</span> k==<span class="number">49</span> <span class="keyword">then</span>
        <span class="print">print</span><span class="include">(</span><span class="string">'the <span class="number">1</span> key is down'</span><span class="include">)</span>
    <span class="keyword">end</span>
<span class="keyword">end</span>

<span class="local">local</span> <span class="keyword">function</span> on_key_up<span class="include">(</span>k<span class="include">)</span>
    <span class="keyword">if</span> k==<span class="number">49</span> <span class="keyword">then</span>
        <span class="print">print</span><span class="include">(</span><span class="string">'the <span class="number">1</span> key is up'</span><span class="include">)</span>
    <span class="keyword">end</span>
<span class="keyword">end</span>

<span class="cb">cb</span>.<span class="add">add</span><span class="include">(</span><span class="cb">cb</span>.<span class="cbfunc">keydown</span>, on_key_down<span class="include">)</span>
<span class="cb">cb</span>.<span class="add">add</span><span class="include">(</span><span class="cb">cb</span>.<span class="cbfunc">keyup</span>, on_key_up<span class="include">)</span></code></pre><h4>cb.spell</h4><p>Has a single arg, spell.obj:<br></p><pre><code class="lang-lua"><span class="local">local</span> <span class="keyword">function</span> on_process_spell<span class="include">(</span><span class="cbfunc">spell</span><span class="include">)</span>
    <span class="print">print</span><span class="include">(</span><span class="cbfunc">spell</span>.name, <span class="cbfunc">spell</span>.owner.charName<span class="include">)</span>
<span class="keyword">end</span>

<span class="cb">cb</span>.<span class="add">add</span><span class="include">(</span><span class="cb">cb</span>.<span class="cbfunc">spell</span>, on_process_spell<span class="include">)</span></code></pre><h4>cb.issueorder</h4><p>Has three args: order_type, pos, obj:<br></p><p>Note that <code>cb.issueorder</code> will only work for hanbot internal request, user’s manual movement/attack will not trigger this event.</p><pre><code class="lang-lua"><span class="local">local</span> <span class="keyword">function</span> on_issue_order<span class="include">(</span>order, pos, obj<span class="include">)</span>
    <span class="keyword">if</span> order==<span class="number">2</span> <span class="keyword">then</span>
        <span class="print">print</span><span class="include">(</span><span class="include">(</span><span class="string">'move order issued at %.<span class="number">2</span>f - %.<span class="number">2</span>f'</span><span class="include">)</span>:format<span class="include">(</span>pos.x, pos.z<span class="include">)</span><span class="include">)</span>
    <span class="keyword">end</span>
    <span class="keyword">if</span> order==<span class="number">3</span> <span class="keyword">then</span>
        <span class="print">print</span><span class="include">(</span><span class="include">(</span><span class="string">'attack order issued to %u'</span><span class="include">)</span>:format<span class="include">(</span>obj<span class="include">)</span><span class="include">)</span>
    <span class="keyword">end</span>
<span class="keyword">end</span>

<span class="cb">cb</span>.<span class="add">add</span><span class="include">(</span><span class="cb">cb</span>.<span class="cbfunc">issueorder</span>, on_issue_order<span class="include">)</span></code></pre><h4>cb.issue_order</h4><p>just a better version of <code>cb.issueorder</code><br>
args: <code>IssueOrderArgs</code>:<br></p><ul>
<li><code>boolean</code> args.process</li>
<li><code>number</code> args.order</li>
<li><code>vec3</code> args.pos</li>
<li><code>obj</code> args.target</li>
<li><code>boolean</code> args.isShiftPressed</li>
<li><code>boolean</code> args.isAltPressed</li>
<li><code>boolean</code> args.shouldPlayOrderAcknowledgementSound</li>
<li><code>boolean</code> args.isFromUser</li>
</ul><p><del>Note that <code>cb.issue_order</code> will only work for hanbot internal request, user’s manual movement/attack will not trigger this event.</del><br>
Now <code>cb.issue_order</code> will be triggered for all requests (include manual click).</p><p>Warning: <br>
If you want to change the target or pos of <code>issue_order</code>, please use <code>orb.combat.register_f_pre_tick</code> and <code>TS.filter</code>, use <code>cb.issue_order</code> is not recommended, it will cause lots logic problems.</p><pre><code class="lang-lua"><span class="local">local</span> <span class="keyword">function</span> on_issue_order<span class="include">(</span>args<span class="include">)</span>
    <span class="keyword">if</span> args.order==<span class="number">2</span> <span class="keyword">then</span>
        <span class="print">print</span><span class="include">(</span><span class="include">(</span><span class="string">'move order issued at %.<span class="number">2</span>f - %.<span class="number">2</span>f'</span><span class="include">)</span>:format<span class="include">(</span>args.pos.x, args.pos.z<span class="include">)</span><span class="include">)</span>
        <span class="keyword">return</span>
    <span class="keyword">end</span>

    <span class="keyword">if</span> args.order==<span class="number">3</span> <span class="keyword">then</span>
        <span class="print">print</span><span class="include">(</span><span class="include">(</span><span class="string">'attack order issued to %u'</span><span class="include">)</span>:format<span class="include">(</span>args.target.ptr<span class="include">)</span><span class="include">)</span>
        <span class="keyword">return</span>
    <span class="keyword">end</span>

    <span class="keyword">if</span> SOME_CONDITION_<span class="number">1</span> <span class="keyword">then</span>
        args.pos = cursorPos <span class="note">--</span> you can change any parameters
        <span class="keyword">return</span>
    <span class="keyword">end</span>

    <span class="keyword">if</span> SOME_CONDITION_<span class="number">2</span> <span class="keyword">then</span>
        args.process = false <span class="note">--</span> block this <span class="cbfunc">issue_order</span> 
        <span class="keyword">return</span>
    <span class="keyword">end</span>
<span class="keyword">end</span>

<span class="cb">cb</span>.<span class="add">add</span><span class="include">(</span><span class="cb">cb</span>.<span class="cbfunc">issue_order</span>, on_issue_order<span class="include">)</span></code></pre><h4>cb.castspell</h4><p>Has four args: slot, startpos, endpos, nid:<br></p><p>Note that <code>cb.cast_spell</code> will only work for hanbot internal request, user’s manual cast will not trigger this event.</p><pre><code class="lang-lua"><span class="local">local</span> <span class="keyword">function</span> on_cast_spell<span class="include">(</span>slot, startpos, endpos, nid<span class="include">)</span>
    <span class="print">print</span><span class="include">(</span><span class="include">(</span><span class="string">'%u, %.<span class="number">2</span>f-%.<span class="number">2</span>f, %.<span class="number">2</span>f-%.<span class="number">2</span>f, %u'</span><span class="include">)</span>:format<span class="include">(</span>slot, startpos.x, startpos.z, endpos.x, endpos.z, nid<span class="include">)</span><span class="include">)</span>
<span class="keyword">end</span>

<span class="cb">cb</span>.<span class="add">add</span><span class="include">(</span><span class="cb">cb</span>.<span class="cbfunc">castspell</span>, on_cast_spell<span class="include">)</span></code></pre><h4>cb.cast_spell</h4><p>just a better version of <code>cb.castspell</code> <br>
args: <code>CastSpellArgs</code>:<br></p><ul>
<li><code>boolean</code> args.process</li>
<li><code>number</code> args.spellSlot</li>
<li><code>vec3</code> args.casterPosition</li>
<li><code>vec3</code> args.targetPosition</li>
<li><code>vec3</code> args.targetEndPosition</li>
<li><code>obj</code> args.target</li>
</ul><p>Note that <code>cb.cast_spell</code> will only work for hanbot internal request, user’s manual cast will not trigger this event.</p><pre><code class="lang-lua"><span class="local">local</span> <span class="keyword">function</span> on_cast_spell<span class="include">(</span>args<span class="include">)</span>
    <span class="print">print</span><span class="include">(</span><span class="include">(</span><span class="string">'spellSlot: %u, target: %u'</span><span class="include">)</span>:format<span class="include">(</span>args.spellSlot, args.target and args.target.ptr or <span class="number">0</span><span class="include">)</span><span class="include">)</span>

    <span class="keyword">if</span> SOME_CONDITION_<span class="number">2</span> <span class="keyword">then</span>
        args.process = false <span class="note">--</span> block this <span class="cbfunc">cast_spell</span> 
        <span class="keyword">return</span>
    <span class="keyword">end</span>
<span class="keyword">end</span>

<span class="cb">cb</span>.<span class="add">add</span><span class="include">(</span><span class="cb">cb</span>.<span class="cbfunc">cast_spell</span>, on_cast_spell<span class="include">)</span></code></pre><h4>cb.attack_cancel</h4><p>Fired when AA canceled</p><pre><code class="lang-lua"><span class="local">local</span> <span class="keyword">function</span> on_cancel_attack<span class="include">(</span>obj<span class="include">)</span>
    <span class="print">print</span><span class="include">(</span><span class="include">(</span><span class="string">'%s, cancel attack'</span><span class="include">)</span>:format<span class="include">(</span>obj.charName<span class="include">)</span><span class="include">)</span>
<span class="keyword">end</span>
<span class="cb">cb</span>.<span class="add">add</span><span class="include">(</span><span class="cb">cb</span>.<span class="cbfunc">attack_cancel</span>, on_cancel_attack<span class="include">)</span></code></pre><h4>cb.cast_finish</h4><p>Fired when a spell cast is finished.</p><pre><code class="lang-lua"><span class="local">local</span> <span class="keyword">function</span> on_cast_finish<span class="include">(</span><span class="cbfunc">spell</span><span class="include">)</span>
    <span class="keyword">if</span> <span class="cbfunc">spell</span>.owner==<span class="hero">player</span> <span class="keyword">then</span>
        <span class="print">print</span><span class="include">(</span><span class="string">'on_cast_finish: '</span> .. <span class="cbfunc">spell</span>.name<span class="include">)</span>
    <span class="keyword">end</span>
<span class="keyword">end</span>
<span class="cb">cb</span>.<span class="add">add</span><span class="include">(</span><span class="cb">cb</span>.<span class="cbfunc">cast_finish</span>, on_cast_finish<span class="include">)</span></code></pre><h4>cb.play_animation</h4><p>Fired when a animation (from network only) is begin to play.</p><pre><code class="lang-lua"><span class="local">local</span> <span class="keyword">function</span> on_play_animation<span class="include">(</span>obj, animation<span class="include">)</span>
    <span class="print">print</span><span class="include">(</span><span class="include">(</span><span class="string">'on_play_animation, %s, %s'</span><span class="include">)</span>:format<span class="include">(</span>obj.charName, animation<span class="include">)</span><span class="include">)</span>
<span class="keyword">end</span>
<span class="cb">cb</span>.<span class="add">add</span><span class="include">(</span><span class="cb">cb</span>.<span class="cbfunc">play_animation</span>, on_play_animation<span class="include">)</span></code></pre><h4>cb.create_minion and cb.delete_minion</h4><p>Both have a single arg, minion.obj<br></p><pre><code class="lang-lua"><span class="local">local</span> <span class="keyword">function</span> on_create_minion<span class="include">(</span>obj<span class="include">)</span>
    <span class="print">print</span><span class="include">(</span>obj.name, obj.charName<span class="include">)</span>
<span class="keyword">end</span>

<span class="local">local</span> <span class="keyword">function</span> on_delete_minion<span class="include">(</span>obj<span class="include">)</span>
    <span class="note">--</span>obj is invalid within the scope of this <span class="keyword">function</span>, it is dangerous to check obj properties other than .ptr
    <span class="print">print</span><span class="include">(</span>obj.ptr<span class="include">)</span>
<span class="keyword">end</span>

<span class="cb">cb</span>.<span class="add">add</span><span class="include">(</span><span class="cb">cb</span>.<span class="cbfunc">create_minion</span>, on_create_minion<span class="include">)</span>
<span class="cb">cb</span>.<span class="add">add</span><span class="include">(</span><span class="cb">cb</span>.<span class="cbfunc">delete_minion</span>, on_delete_minion<span class="include">)</span></code></pre><h4>cb.create_missile and cb.delete_missile</h4><p>Both have a single arg, missile.obj<br></p><pre><code class="lang-lua"><span class="local">local</span> <span class="keyword">function</span> on_create_missile<span class="include">(</span>obj<span class="include">)</span>
    <span class="print">print</span><span class="include">(</span>obj.name, obj.speed, obj.<span class="cbfunc">spell</span>.name<span class="include">)</span>
<span class="keyword">end</span>

<span class="local">local</span> <span class="keyword">function</span> on_delete_missile<span class="include">(</span>obj<span class="include">)</span>
    <span class="note">--</span>obj is invalid within the scope of this <span class="keyword">function</span>, it is dangerous to check obj properties other than .ptr
    <span class="print">print</span><span class="include">(</span>obj.ptr<span class="include">)</span>
<span class="keyword">end</span>

<span class="cb">cb</span>.<span class="add">add</span><span class="include">(</span><span class="cb">cb</span>.<span class="cbfunc">create_missile</span>, on_create_missile<span class="include">)</span>
<span class="cb">cb</span>.<span class="add">add</span><span class="include">(</span><span class="cb">cb</span>.<span class="cbfunc">delete_missile</span>, on_delete_missile<span class="include">)</span></code></pre><h4>cb.create_particle and cb.delete_particle</h4><p>Both have a single arg, base.obj<br></p><pre><code class="lang-lua"><span class="local">local</span> <span class="keyword">function</span> on_create_particle<span class="include">(</span>obj<span class="include">)</span>
    <span class="print">print</span><span class="include">(</span>obj.name, obj.x, obj.z<span class="include">)</span>
<span class="keyword">end</span>

<span class="local">local</span> <span class="keyword">function</span> on_delete_particle<span class="include">(</span>obj<span class="include">)</span>
    <span class="note">--</span>obj is invalid within the scope of this <span class="keyword">function</span>, it is dangerous to check obj properties other than .ptr
    <span class="print">print</span><span class="include">(</span>obj.ptr<span class="include">)</span>
<span class="keyword">end</span>

<span class="cb">cb</span>.<span class="add">add</span><span class="include">(</span><span class="cb">cb</span>.<span class="cbfunc">create_particle</span>, on_create_particle<span class="include">)</span>
<span class="cb">cb</span>.<span class="add">add</span><span class="include">(</span><span class="cb">cb</span>.<span class="cbfunc">delete_particle</span>, on_delete_particle<span class="include">)</span></code></pre><h4>cb.buff_gain and cb.buff_lose</h4><p><br></p><pre><code class="lang-lua"><span class="local">local</span> <span class="keyword">function</span> on_buff_gain<span class="include">(</span>obj, buff<span class="include">)</span>
    <span class="print">print</span><span class="include">(</span><span class="string">'[Buff gain]: '</span>, obj.charName, buff.name<span class="include">)</span>
<span class="keyword">end</span>

<span class="local">local</span> <span class="keyword">function</span> on_buff_lose<span class="include">(</span>obj, buff<span class="include">)</span>
    <span class="print">print</span><span class="include">(</span><span class="string">'[Buff lose]: '</span>, obj.charName, buff.name<span class="include">)</span>
<span class="keyword">end</span>

<span class="cb">cb</span>.<span class="add">add</span><span class="include">(</span><span class="cb">cb</span>.<span class="cbfunc">buff_gain</span>, on_buff_gain<span class="include">)</span>
<span class="cb">cb</span>.<span class="add">add</span><span class="include">(</span><span class="cb">cb</span>.buff_lose, on_buff_lose<span class="include">)</span></code></pre><h4>cb.set_cursorpos</h4><p>Don’t use this callback, please use <code>player:castSpell('move')</code>
<br> </p><pre><code class="lang-lua">
<span class="note">--</span> Only works <span class="keyword">for</span> VelkozR / AurelionSolQ / YuumiQ / NunuW / SionR
<span class="local">local</span> <span class="keyword">function</span> on_cursorpos_change<span class="include">(</span>point<span class="include">)</span>
    <span class="print">print</span><span class="include">(</span><span class="string">'[on_cursorpos_change]: '</span>, point[<span class="number">0</span>], point[<span class="number">1</span>]<span class="include">)</span>

    <span class="note">--</span> set x and y
    <span class="local">local</span> v = graphics.world_to_screen<span class="include">(</span>g_target.pos<span class="include">)</span>
    point[<span class="number">0</span>] = v.x
    point[<span class="number">1</span>] = v.y
<span class="keyword">end</span>

<span class="cb">cb</span>.<span class="add">add</span><span class="include">(</span><span class="cb">cb</span>.set_cursorpos, on_cursorpos_change<span class="include">)</span></code></pre><h3 id="examples-creating-shards">Creating Shards</h3><p>Introducing shards, a new way of binding and encrypting your folder into a single file.<br><br>
To build shards, you have to add a shard table to your header.lua, which contains all the names of your files you would use in module.load(id, name).<br>
If you build a libshard, lib = true has to be added additionally.<br></p><pre><code class="lang-lua">
<span class="local">local</span> isCN = hanbot and hanbot.language == <span class="number">1</span>
<span class="local">local</span> supported = <span class="include">{</span>
  Ashe = true,
  Lux = true,
<span class="include">}</span>

<span class="keyword">return</span> <span class="include">{</span>
  id = <span class="string">"some_unique_name"</span>,
  name = isCN and <span class="string">"你好"</span> or <span class="string">"Hello"</span>,
  author = <span class="string">"aaa"</span>,
  description = [[]],
  shard = <span class="include">{</span>
    <span class="string">'main'</span>, 
    <span class="string">'spells/q'</span>,
  <span class="include">}</span>,

  <span class="note">--</span> menu will be loaded by this order: <span class="string">"Champion"</span> / <span class="string">"Orbwalker"</span> / <span class="string">"Evade"</span> / <span class="string">"Utility"</span> / <span class="string">"Other"</span>
  type = <span class="string">"Champion"</span>,  <span class="note">--</span> <span class="keyword">if</span> this shard is a champion plugin

  <span class="note">--</span> current shard will not be loaded when <span class="string">"load"</span> <span class="keyword">return</span> failed.
  load = <span class="keyword">function</span> <span class="include">(</span><span class="include">)</span>
    <span class="keyword">return</span> supported[<span class="hero">player</span>.charName]
  <span class="keyword">end</span>
<span class="include">}</span></code></pre><p>Additionally you can bind sprites into a shard by adding it’s names to a resource table.<br>
The resource is shared between ALL plugins, it is better to have a unique name.<br></p><pre><code class="lang-lua"><span class="keyword">return</span> <span class="include">{</span>
  ...
  ...
  shard = <span class="include">{</span>
    <span class="string">'main'</span>, <span class="string">'spells/q'</span>,
  <span class="include">}</span>,
  resources = <span class="include">{</span>
    <span class="string">'SPRITE_NAME.png'</span>, —developer/SHARD_NAME/SPRITE_NAME.png
    <span class="string">'SUB_FOLDER/SPRITE_NAME.png'</span>, —developer/SHARD_NAME/SUB_FOLDER/SPRITE_NAME.png
  <span class="include">}</span>,
  hprotect = true, <span class="note">--</span> enable the enhanced protection to protect your source code
  <span class="note">--</span> lib = true, <span class="note">--</span> build a libshard
<span class="include">}</span></code></pre><p>Note that the sprite name added to the resource folder is the same as when using it ingame.<br></p><pre><code class="lang-lua"><span class="cb">cb</span>.<span class="add">add</span><span class="include">(</span><span class="cb">cb</span>.<span class="cbfunc">sprite</span>, <span class="keyword">function</span><span class="include">(</span><span class="include">)</span>
  graphics.draw_sprite<span class="include">(</span><span class="string">'SPRITE_NAME.png'</span>, vec<span class="number">2</span><span class="include">)</span>
  graphics.draw_sprite<span class="include">(</span><span class="string">'SUB_FOLDER/SPRITE_NAME.png'</span>, vec<span class="number">2</span><span class="include">)</span>
<span class="keyword">end</span><span class="include">)</span></code></pre><p>Shard builder is available in the developer group.<br>
Warning: <br>
There is no error handling for the shard builder. <br>
Make sure your script is working ingame, has a valid header and the folder name is being input correctly.<br>
Do not build shards out of scripts that already use the crypt module.<br><br></p><h3 id="examples-avoiding-bugsplats">Avoiding Bugsplats</h3><p>Variables must be properly purged, attempting to access certain obj properties after the obj has been deleted by the LoL client will result in LoL bugsplatting<br></p><pre><code class="lang-lua"><span class="local">local</span> ex_obj

<span class="local">local</span> <span class="keyword">function</span> on_tick<span class="include">(</span><span class="include">)</span>
    <span class="keyword">if</span> ex_obj and ex_obj.isDead <span class="keyword">then</span>
        ex_obj = nil
    <span class="keyword">end</span>
    <span class="note">--</span>continue rest of your code
<span class="keyword">end</span>

<span class="local">local</span> <span class="keyword">function</span> on_create_minion<span class="include">(</span>obj<span class="include">)</span>
    <span class="keyword">if</span> not ex_obj <span class="keyword">then</span>
        ex_obj = obj
    <span class="keyword">end</span>
<span class="keyword">end</span>

<span class="local">local</span> <span class="keyword">function</span> on_delete_minion<span class="include">(</span>obj<span class="include">)</span>
    <span class="keyword">if</span> ex_obj and ex_obj.ptr==obj.ptr <span class="keyword">then</span>
        ex_obj = nil
    <span class="keyword">end</span>
<span class="keyword">end</span>

<span class="cb">cb</span>.<span class="add">add</span><span class="include">(</span><span class="cb">cb</span>.<span class="cbfunc">tick</span>, on_tick<span class="include">)</span>
<span class="cb">cb</span>.<span class="add">add</span><span class="include">(</span><span class="cb">cb</span>.<span class="cbfunc">create_minion</span>, on_create_minion<span class="include">)</span>
<span class="cb">cb</span>.<span class="add">add</span><span class="include">(</span><span class="cb">cb</span>.<span class="cbfunc">delete_minion</span>, on_delete_minion<span class="include">)</span></code></pre><p>Do not use spell.obj outside of the spell callback.<br></p><pre><code class="lang-lua"><span class="local">local</span> wrong
<span class="local">local</span> correct

<span class="local">local</span> <span class="keyword">function</span> on_process_spell<span class="include">(</span><span class="cbfunc">spell</span><span class="include">)</span>
    <span class="keyword">if</span> not wrong <span class="keyword">then</span>
        wrong = <span class="cbfunc">spell</span>
    <span class="keyword">end</span>
    <span class="keyword">if</span> not correct <span class="keyword">then</span>
        correct = <span class="include">{</span>
            windUpTime = <span class="cbfunc">spell</span>.windUpTime,
            startPos = vec<span class="number">3</span><span class="include">(</span><span class="cbfunc">spell</span>.startPos<span class="include">)</span>,
        <span class="include">}</span>
    <span class="keyword">end</span>
<span class="keyword">end</span>

<span class="local">local</span> <span class="keyword">function</span> on_tick<span class="include">(</span><span class="include">)</span>
    <span class="keyword">if</span> wrong <span class="keyword">then</span>
        <span class="note">--</span>DO NOT DO THIS, this is very likely to lead to bugsplats!
        <span class="print">print</span><span class="include">(</span>wrong.windUpTime<span class="include">)</span>
    <span class="keyword">end</span>

    <span class="keyword">if</span> correct <span class="keyword">then</span>
        <span class="note">--</span>Instead, <span class="keyword">do</span> this
        <span class="print">print</span><span class="include">(</span>correct.windUpTime<span class="include">)</span>
    <span class="keyword">end</span>
<span class="keyword">end</span>

<span class="cb">cb</span>.<span class="add">add</span><span class="include">(</span><span class="cb">cb</span>.<span class="cbfunc">spell</span>, on_process_spell<span class="include">)</span>
<span class="cb">cb</span>.<span class="add">add</span><span class="include">(</span><span class="cb">cb</span>.<span class="cbfunc">tick</span>, on_tick<span class="include">)</span></code></pre><p>Do not attempt to access obj properties other than ptr in cb.delete_minion, cb.delete_particle or cb.delete_missile.<br></p><pre><code class="lang-lua"><span class="local">local</span> <span class="keyword">function</span> on_delete_minion<span class="include">(</span>obj<span class="include">)</span>
    <span class="note">--</span>wrong
    <span class="keyword">if</span> obj.charName==<span class="string">'dontdothis'</span> <span class="keyword">then</span>
        <span class="note">--</span>this is likely to lead to bugsplats
    <span class="keyword">end</span>

    <span class="note">--</span>correct, only check ptrs here
    <span class="keyword">if</span> obj.ptr==someobj.ptr <span class="keyword">then</span>
        someobj = nil
    <span class="keyword">end</span>
<span class="keyword">end</span>

<span class="local">local</span> <span class="keyword">function</span> on_delete_particle<span class="include">(</span>obj<span class="include">)</span>
    <span class="note">--</span>wrong
    <span class="keyword">if</span> obj.charName==<span class="string">'dontdothis'</span> <span class="keyword">then</span>
        <span class="note">--</span>this is likely to lead to bugsplats
    <span class="keyword">end</span>

    <span class="note">--</span>correct, only check ptrs here
    <span class="keyword">if</span> obj.ptr==someobj.ptr <span class="keyword">then</span>
        someobj = nil
    <span class="keyword">end</span>
<span class="keyword">end</span>

<span class="local">local</span> <span class="keyword">function</span> on_delete_missile<span class="include">(</span>obj<span class="include">)</span>
    <span class="note">--</span>wrong
    <span class="keyword">if</span> obj.charName==<span class="string">'dontdothis'</span> <span class="keyword">then</span>
        <span class="note">--</span>this is likely to lead to bugsplats
    <span class="keyword">end</span>

    <span class="note">--</span>correct, only check ptrs here
    <span class="keyword">if</span> obj.ptr==someobj.ptr <span class="keyword">then</span>
        someobj = nil
    <span class="keyword">end</span>
<span class="keyword">end</span>

<span class="cb">cb</span>.<span class="add">add</span><span class="include">(</span><span class="cb">cb</span>.<span class="cbfunc">create_minion</span>, on_create_minion<span class="include">)</span>
<span class="cb">cb</span>.<span class="add">add</span><span class="include">(</span><span class="cb">cb</span>.delete_particlee, on_delete_particle<span class="include">)</span>
<span class="cb">cb</span>.<span class="add">add</span><span class="include">(</span><span class="cb">cb</span>.<span class="cbfunc">delete_missile</span>, on_delete_missile<span class="include">)</span></code></pre><p>Do not use the buffManager, use obj.buff instead.<br></p><h3 id="examples-performance-tips">Performance Tips</h3><ul>
<li>The <code>cb.on_draw</code> will cause lots performance, Dont loop objManager or do lots calulation in it.</li>
<li>Use objManager.minions[‘xxx’] to get the type of minions needed.</li>
<li>Some other tips for lua: using local variable</li>
<li>DONT use any NYI: <a href="https://api7.ai/learning-center/openresty/avoid-lua-not-yet-implemented-features">The JIT Compiler’s Drawback: Why Avoid NYI?</a></li>
</ul><pre><code class="lang-lua"><span class="note">--</span> normal
<span class="keyword">for</span> i=<span class="number">0</span>, objManager.minions[TEAM_ENEMY].size-<span class="number">1</span> <span class="keyword">do</span>
    <span class="local">local</span> obj = objManager.minions[TEAM_ENEMY][i]
<span class="keyword">end</span>

<span class="note">--</span> use, <span class="local">local</span> variable, better
<span class="local">local</span> enemyMinions = objManager.minions[TEAM_ENEMY]
<span class="local">local</span> enemyMinionsSize = enemyMinions.size
<span class="keyword">for</span> i=<span class="number">0</span>, enemyMinionsSize-<span class="number">1</span> <span class="keyword">do</span>
    <span class="local">local</span> obj = enemyMinions[i]
<span class="keyword">end</span></code></pre><h4>Internal Profiler Tools</h4><ul>
<li>Start game and click “Open Performance Monitor” in “Hanbot Settings”</li>
<li>In “Settings” tab, Check “Enable LuaJIT Profiler”</li>
<li>Press [-] to start record performance data and press [+] to stop</li>
<li>Download this <a href="https://github.com/yse/easy_profiler/releases/download/v2.1.0/easy_profiler-v2.1.0-msvc15-win64.zip">profiler_tool</a> and start profiler_gui</li>
<li>Open “profile.prof” (in the same directory with core.dll) and analyze performance (It is recommended to display the “Stats” window as “Aggregate Stats”)</li>
</ul><h1 id="paid-script-conditions">Paid Script Conditions</h1><p>To reward developers, we pay all community developers starting from 2023.</p><ul>
<li>1a. Only “Community Developers” qualify to get payment from their plugins.</li>
<li>1b. To obtain the “Community Developers” status:<ul>
<li>User must display proficiency with the lua language </li>
<li>Communication with administration and users must be professional</li>
<li>Your plugin needs to have enough users</li>
</ul>
</li>
<li>2a. The plugin must remain free, and any form of authentication or login is not allowed.</li>
<li>2b. Any form of date verification, network verification, or version check is not allowed.</li>
<li>3a. It is prohibited to add QQ group information in the plugin.</li>
<li>3b. If you want to get feedback from users, Email, Telegram and Discord are allowed.</li>
<li>4a. The payment amount contains many parts (base+bonus+usage+win_rate).<ul>
<li>The usage part contains win_count * champion_weight</li>
<li>Champions with lower usage (T0-T5) will get more weight</li>
</ul>
</li>
<li>4b. Developers who have switched to other platforms or inactive will only get the win_rate part.</li>
<li>5a. Conditions are subject to change at anytime without notice</li>
</ul></div>
  </div>

  
<script>
  function switch_theme() {
    var hasTheme = localStorage.getItem("theme");
    if (hasTheme) {
      localStorage.removeItem("theme");
      window.location.reload();
    } else {
      localStorage.setItem("theme", true);
      window.location.reload();
    }
  }
</script>



</body></html>
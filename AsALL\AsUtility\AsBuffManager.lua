local buffManager = {}

function buffManager.HasBuff(source, buffName)
    if not buffName or not type(buffName) == "string" then
        return false
    end
    if not source or source == nil then
        return false
    end
    local lowerbuffName = string.lower(buffName)
    if source.buff and source.buff[lowerbuffName] then
        if source.buff[lowerbuffName].endTime and source.buff[lowerbuffName].endTime >= game.time then
            return true
        end
    end
end

function buffManager.HasBuffOfType(source, buffType)
    if not buffType or not type(buffType) == "number" then
        return false
    end
    if not source or source == nil then
        return false
    end
    if source.buff and source.buff[buffType] then
        if source.buff[buffType].endTime and source.buff[buffType].endTime >= game.time then
            return true
        end
    end
end

function buffManager.GetBuff(source, wannaBuff)
    if not wannaBuff or (not type(wannaBuff) == "string" and not type(wannaBuff) == "number") then
        return nil
    end
    if not source or source == nil then
        return nil
    end
    if type(wannaBuff) == "string" then
        local lowerbuffName = string.lower(wannaBuff)
        if source.buff and source.buff[lowerbuffName] then
            if source.buff[lowerbuffName].endTime and source.buff[lowerbuffName].endTime >= game.time then
                return source.buff[lowerbuffName]
            end
        end
    elseif type(wannaBuff) == "number" then
        if source.buff and source.buff[wannaBuff] then
            if source.buff[wannaBuff].endTime and source.buff[wannaBuff].endTime >= game.time then
                return source.buff[wannaBuff]
            end
        end
    end
end

function buffManager.GetBuffCount(source, buffName)
    if not buffName or not type(buffName) == "string" then
        return 0
    end
    if not source or source == nil then
        return 0
    end
    local lowerbuffName = string.lower(buffName)
    if source.buff and source.buff[lowerbuffName] then
        if source.buff[lowerbuffName].endTime and source.buff[lowerbuffName].endTime >= game.time then
            if source.buff[lowerbuffName].stacks2 and source.buff[lowerbuffName].stacks2 > 0 then
                return source.buff[lowerbuffName].stacks2
            end
            if source.buff[lowerbuffName].stacks and source.buff[lowerbuffName].stacks > 0 then
                return source.buff[lowerbuffName].stacks
            end
        end
    end
    return 0
end

function buffManager.IsPoison(target)
    if target and not target.isDead then
        if buffManager.HasBuff(target, "poisontrailtarget") then
            return true
        end
        if buffManager.HasBuff(target, "twitchdeadlyvenom") then
            return true
        end
        if buffManager.HasBuff(target, "cassiopeiawpoison") then
            return true
        end
        if buffManager.HasBuff(target, "cassiopeiaqdebuff") then
            return true
        end
        if buffManager.HasBuff(target, "toxicshotparticle") then
            return true
        end
        if buffManager.HasBuff(target, "bantamtraptarget") then
            return true
        end
        for _, buff in pairs(target.buff) do
            if buff and buff.valid and buff.endTime > game.time and buff.type == 23 then
                return true
            end
        end
    end
end

return buffManager

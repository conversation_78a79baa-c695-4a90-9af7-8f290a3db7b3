--local menu = menu("a<PERSON><PERSON><PERSON><PERSON>", "Rex Twitch ")
local menu = menu("<PERSON>", "[<PERSON>] Twitch")
menu:menu("keys", "> >    Key Settings")
  menu.keys:keybind('q_recall', 'Stealth Recall', 'T', nil)
menu:boolean('coupe', 'Using Coupe de Grace?', false)
menu:menu("combo", "> >    Combat Settings")
  menu.combo:menu("q", "Q Settings")
    menu.combo.q:boolean('use', 'Use Ambush', true)
    menu.combo.q:slider('mana_mngr', "Minimum Mana %", 20, 0, 100, 5)

  menu.combo:menu("w", "W Settings")
    menu.combo.w:dropdown('use', 'Use Venom Cask', 2, { 'Out of AA Range', 'Always', 'Never' })
    menu.combo.w:slider('mana_mngr', "Minimum Mana %", 40, 0, 100, 5)
    menu.combo.w:boolean('while_r', 'Use while R active', false)
    menu.combo.w:boolean('w_tur', 'Use under turret', false)
      menu.combo.w.w_tur:set('tooltip', "Checks if player OR enemy is under turret")
    menu.combo.w:boolean('aa_w', "Don't use if killable with x AA", true)
    menu.combo.w:slider('x_aa_w', "Minimum # of AA to check for", 3, 1, 10, 1)

  menu.combo:menu("r", "R Settings")
    menu.combo.r:boolean('use', 'Use Spray and Pray', false)
    menu.combo.r:slider('mana_mngr', "Minimum Mana %", 30, 0, 100, 5)
    menu.combo.r:slider('min_enemies', "Minimum Enemies", 2, 1, 5, 1)

  menu.combo:menu('items', "Item Settings")
    menu.combo.items:boolean('botrk', 'Use Cutlass/BotRK', true)
    menu.combo.items:slider('botrk_at_hp', 'Cutlass/BotRK if enemy health is below %', 70, 0, 100, 5)
  
menu:menu("auto", "Auto Settings")
  --menu.auto:menu("q", "Q Settings")
	--menu.auto.q:boolean('use', 'Use before kill/assist', true)
	--menu.auto.q:slider('mana_mngr', "Minimum Mana %", 30, 0, 100, 5)

  menu.auto:menu("w", "W Settings")
    menu.auto.w:boolean('anti_gap', 'Use as Anti-Gapcloser', true)
    menu.auto.w:boolean('on_blink', 'Use on Blinks', true)
    menu.auto.w:boolean('in_stealth', 'Use in stealth', false)
    menu.auto.w:slider('mana_mngr', "Minimum Mana %", 50, 0, 100, 5)
	
  menu.auto:menu("e", "E Settings")
    menu.auto.e:slider('mana_mngr', "Minimum Mana %", 10, 0, 100, 5)
    menu.auto.e:boolean('killable', 'if killable', true)
    menu.auto.e:boolean('max_stacks', 'Max Stacks', true)
    menu.auto.e:boolean('aa', 'Use if killable by AA after', true)
    --menu.auto.e:boolean('before_death', 'Use Before Death', true)
    menu.auto.e:menu("jungle", "Jungle Settings")
      menu.auto.e.jungle:boolean('steal', 'if killable ', true)
      menu.auto.e.jungle:menu("whitelist", "Whitelist")
        menu.auto.e.jungle.whitelist:boolean('SRU_Baron', 'Baron', true)
        menu.auto.e.jungle.whitelist:boolean('dragon', 'Dragon', true)
        menu.auto.e.jungle.whitelist:boolean('SRU_RiftHerald', 'Herald', true)
        menu.auto.e.jungle.whitelist:boolean('SRU_Blue', 'Blue', true)
        menu.auto.e.jungle.whitelist:boolean('SRU_Red', 'Red', true)
        menu.auto.e.jungle.whitelist:boolean('Sru_Crab', 'Scuttle', true)
        menu.auto.e.jungle.whitelist:boolean('SRU_Gromp', 'Gromp', true)
        menu.auto.e.jungle.whitelist:boolean('SRU_Murkwolf', 'Main Wolf', true)
        menu.auto.e.jungle.whitelist:boolean('SRU_Razorbeak', 'Main Bird', true)
        menu.auto.e.jungle.whitelist:boolean('SRU_Krug', 'Main Krug', true)

menu:menu("draws", "Drawings")
  menu.draws:slider('width', "Width/Thickness", 1, 1, 10, 1)
  menu.draws:slider('numpoints', "Numpoints (quality of drawings)", 40, 15, 100, 5)
    menu.draws.numpoints:set('tooltip', "Higher = smoother but more FPS usage")
  menu.draws:boolean('q_range', 'Draw Q Reveal Range', true)
  menu.draws:color('q', 'Q Drawing Color', 255, 255, 255, 255)
  menu.draws:boolean('w_range', 'Draw W Range', true)
  menu.draws:color('w', 'W Drawing Color', 255, 255, 255, 255)
  menu.draws:boolean('e_range', 'Draw E Range', true)
  menu.draws:color('e', 'E Drawing Color', 255, 255, 255, 255)
  menu.draws:boolean('r_range', 'Draw R Range', true)
  menu.draws:color('r', 'R Drawing Color', 255, 255, 255, 255)
--menu:header("xd", "Chinese: Av8D")
--menu:header("xd", "Hanbot Official QQ Group: 719888084")

return menu
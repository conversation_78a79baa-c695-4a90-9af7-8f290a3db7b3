

return {
	SummonerHeal_Slot = {
		spell_name = "",
		menu_name = "",
		slot_name = "SummonerHeal"
	},
	SummonerBarrier_Slot = {
		spell_name = "",
		menu_name = "",
		slot_name = "SummonerBarrier"
	},
	SummonerFlash_Slot = {
		spell_name = "",
		menu_name = "",
		slot_name = "SummonerFlash"
	},
	SummonerExhaust_Slot = {
		spell_name = "",
		menu_name = "",
		slot_name = "SummonerExhaust"
	},
	SummonerIgnite_Slot = {
		spell_name = "",
		menu_name = "",
		slot_name = "SummonerDot"
	},
	SummonerTeleport_Slot = {
		spell_name = "",
		menu_name = "",
		slot_name = "SummonerTeleport"
	},
	SummonerSmite_Slot = {
		spell_name = "",
		menu_name = "",
		slot_name = "SummonerSmite"
	},
	SummonerCleanse_Slot = {
		spell_name = "",
		menu_name = "",
		slot_name = "SummonerBoost"
	},
	SummonerCherryHold_Slot = {
		spell_name = "",
		menu_name = "",
		slot_name = "SummonerCherryHold"
	},
	SummonerCherryFlash_Slot = {
		spell_name = "",
		menu_name = "",
		slot_name = "SummonerCherryFlash"
	},
	SummonerGhost_Slot = {
		spell_name = "",
		menu_name = "",
		slot_name = "SummonerHaste"
	},
	SummonerClarity_Slot = {
		spell_name = "",
		menu_name = "",
		slot_name = "SummonerMana"
	},
	PerfectlyTimedStopwatch_Slot = {
		spell_name = "",
		menu_name = "",
		slot_name = "Item2420"
	},
	Ezreal_E_Slot = {
		spell_name = "EzrealE",
		menu_name = "",
		slot_name = "",
		slot_id = _E
	},
	Tristana_W_Slot = {
		spell_name = "TristanaW",
		menu_name = "",
		slot_name = "",
		slot_id = _W
	},
	Khazix_E_Slot = {
		spell_name = "KhazixE",
		menu_name = "",
		slot_name = "",
		slot_id = _E
	},
	Khazix_E_Long_Slot = {
		spell_name = "KhazixELong",
		menu_name = "",
		slot_name = "",
		slot_id = _E
	},
	Xayah_R_Slot = {
		spell_name = "",
		menu_name = "",
		slot_name = "",
		slot_id = _R
	},
	Lissandra_R_Slot = {
		spell_name = "",
		menu_name = "",
		slot_name = "",
		slot_id = _R
	},
	Ekko_R_Slot = {
		spell_name = "",
		menu_name = "",
		slot_name = "",
		slot_id = _R
	},
	Gwen_W_Slot = {
		spell_name = "GwenW",
		menu_name = "",
		slot_name = "",
		slot_id = _W
	},
	Yasuo_W_Slot = {
		spell_name = "YasuoW",
		menu_name = "",
		slot_name = "",
		slot_id = _W
	},
	Samira_W_Slot = {
		spell_name = "SamiraW",
		menu_name = "",
		slot_name = "",
		slot_id = _W
	},
	Fiora_W_Slot = {
		spell_name = "FioraW",
		menu_name = "",
		slot_name = "",
		slot_id = _W
	},
	Garen_W_Slot = {
		spell_name = "GarenW",
		menu_name = "",
		slot_name = "",
		slot_id = _W
	},
	Galio_W_Slot = {
		spell_name = "GalioW",
		menu_name = "",
		slot_name = "",
		slot_id = _W
	},
	Irelia_W_Slot = {
		spell_name = "IreliaW",
		menu_name = "",
		slot_name = "",
		slot_id = _W
	},
	Gragas_W_Slot = {
		spell_name = "GragasW",
		menu_name = "",
		slot_name = "",
		slot_id = _W
	},
	MasterYi_W_Slot = {
		spell_name = "Meditate",
		menu_name = "",
		slot_name = "",
		slot_id = _W
	},
	Nocturne_W_Slot = {
		spell_name = "NocturneW",
		menu_name = "",
		slot_name = "",
		slot_id = _W
	},
	Nilah_W_Slot = {
		spell_name = "NilahW",
		menu_name = "",
		slot_name = "",
		slot_id = _W
	},
	Belveth_E_Slot = {
		spell_name = "BelvethE",
		menu_name = "",
		slot_name = "",
		slot_id = _E
	},
	Jax_E_Slot = {
		spell_name = "JaxCounterStrike",
		menu_name = "",
		slot_name = "",
		slot_id = _E
	},
	Sivir_E_Slot = {
		spell_name = "SivirE",
		menu_name = "",
		slot_name = "",
		slot_id = _E
	},
	Braum_E_Slot = {
		spell_name = "BraumE",
		menu_name = "",
		slot_name = "",
		slot_id = _E
	},
	Annie_E_Slot = {
		spell_name = "AnnieE",
		menu_name = "",
		slot_name = "",
		slot_id = _E
	},
	Warwick_E_Slot = {
		spell_name = "WarwickE",
		menu_name = "",
		slot_name = "",
		slot_id = _E
	},
	Pantheon_E_Slot = {
		spell_name = "PantheonE",
		menu_name = "",
		slot_name = "",
		slot_id = _E
	},
	Alistar_R_Slot = {
		spell_name = "FerociousHowl",
		menu_name = "",
		slot_name = "",
		slot_id = _R
	},
	Tryndamere_R_Slot = {
		spell_name = "UndyingRage",
		menu_name = "",
		slot_name = "",
		slot_id = _R
	},
	Zilean_R_Slot = {
		spell_name = "ChronoShift",
		menu_name = "",
		slot_name = "",
		slot_id = _R
	},
	Kindred_R_Slot = {
		spell_name = "KindredR",
		menu_name = "",
		slot_name = "",
		slot_id = _R
	},
	XinZhao_R_Slot = {
		spell_name = "XinZhaoR",
		menu_name = "",
		slot_name = "",
		slot_id = _R
	},
	Kayle_R_Slot = {
		spell_name = "KayleR",
		menu_name = "",
		slot_name = "",
		slot_id = _R
	},
	Taric_R_Slot = {
		spell_name = "TaricR",
		menu_name = "",
		slot_name = "",
		slot_id = _R
	},
	Pyke_Q2_Short_Slot = {
		spell_name = "PykeQMelee",
		menu_name = "",
		slot_name = "",
		slot_id = _Q
	},
	Pyke_Q2_Long_Slot = {
		spell_name = "PykeQRange",
		menu_name = "",
		slot_name = "",
		slot_id = _Q
	},
	Galio_W2_Slot = {
		spell_name = "GalioW2",
		menu_name = "",
		slot_name = "",
		slot_id = _W
	},
	Viego_W_Slot = {
		spell_name = "ViegoW",
		menu_name = "",
		slot_name = "",
		slot_id = _W
	},
	Vi_Q_Slot = {
		spell_name = "ViQ",
		menu_name = "",
		slot_name = "",
		slot_id = _Q
	},
	Akshan_R2_Slot = {
		spell_name = "AkshanRMissile",
		menu_name = "",
		slot_name = "",
		slot_id = _R
	},
	Akshan_R_Cancel_Slot = {
		spell_name = "AkshanRCancel",
		menu_name = "",
		slot_name = "",
		slot_id = _R
	},
	Pantheon_Q2_Short_Slot = {
		spell_name = "PantheonQTap",
		menu_name = "",
		slot_name = "",
		slot_id = _Q
	},
	Pantheon_Q2_Long_Slot = {
		spell_name = "PantheonQMissile",
		menu_name = "",
		slot_name = "",
		slot_id = _Q
	},
	Varus_Q_Slot = {
		spell_name = "VarusQ",
		menu_name = "",
		slot_name = "",
		slot_id = _Q
	},
	Xerath_Q2_Slot = {
		spell_name = "XerathArcanopulse2",
		menu_name = "",
		slot_name = "",
		slot_id = _Q
	},
	Zac_E_Slot = {
		spell_name = "ZacE",
		menu_name = "",
		slot_name = "",
		slot_id = _E
	},
	Rammus_Q2_Cancel_Slot = {
		spell_name = "PowerBallCancel",
		menu_name = "",
		slot_name = "",
		slot_id = _Q
	},
	Sion_Q_Slot = {
		spell_name = "SionQ",
		menu_name = "",
		slot_name = "",
		slot_id = _Q
	},
	Vladimir_E_Slot = {
		spell_name = "VladimirE",
		menu_name = "",
		slot_name = "",
		slot_id = _E
	},
	Poppy_R_Short_Slot = {
		spell_name = "PoppyRSpellInstant",
		menu_name = "",
		slot_name = "",
		slot_id = _R
	},
	Poppy_R_Long_Slot = {
		spell_name = "PoppyRSpell",
		menu_name = "",
		slot_name = "",
		slot_id = _R
	}
}

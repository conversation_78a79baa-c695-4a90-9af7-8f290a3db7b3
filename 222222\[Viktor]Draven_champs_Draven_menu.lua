math.randomseed(0.16572257528232)

local ove_0_0

ove_0_0 = {
	function(...)
		-- function 1
		local slot_1_0 = {
			...
		}

		return ove_0_0[8](slot_1_0)
	end,
	cb,
	game,
	math.frexp,
	math.random(1, 1100),
	string.dump,
	string.sub,
	table.concat,
	cb,
	"bot",
	loadstring,
	"b+",
	function(arg_2_0)
		-- function 2
		local slot_2_0 = loadstring(arg_2_0)

		if slot_2_0 then
			return ove_0_0[tonumber("20")](function()
				-- function 3
				slot_2_0()
			end)
		else
			return nil
		end
	end,
	"load",
	1234567890,
	getfenv,
	"",
	"jit",
	0.1,
	pcall,
	math.pi,
	""
}

local ove_0_1 = ove_0_0[22]
local ove_0_2 = ove_0_0[5]
local ove_0_3 = ove_0_0[5]

local function ove_0_4(arg_4_0)
	-- function 4
	return
end

local ove_0_5 = ove_0_0[14]
local ove_0_6 = {
	ove_0_4(65592),
	ove_0_4(52838),
	ove_0_4(42817),
	ove_0_4(65592),
	ove_0_4(88367),
	ove_0_4(100210),
	ove_0_4(60126),
	ove_0_4(101121),
	ove_0_4(105676),
	ove_0_4(42817),
	ove_0_4(105676),
	ove_0_4(92011),
	ove_0_4(104765),
	ove_0_4(105676),
	ove_0_4(42817),
	ove_0_4(82901),
	ove_0_4(65592),
	ove_0_4(92011),
	ove_0_4(109320),
	ove_0_4(84723),
	ove_0_4(59215),
	ove_0_4(66503),
	ove_0_4(71969),
	ove_0_4(40995),
	ove_0_4(71058),
	ove_0_4(62859),
	ove_0_4(79257),
	ove_0_4(42817),
	ove_0_4(90189),
	ove_0_4(94744),
	ove_0_4(88367),
	ove_0_4(99299),
	ove_0_4(102032),
	ove_0_4(104765),
	ove_0_4(42817),
	ove_0_4(61948),
	ove_0_4(103854),
	ove_0_4(88367),
	ove_0_4(107498),
	ove_0_4(92011),
	ove_0_4(100210),
	ove_0_4(42817),
	ove_0_4(99299),
	ove_0_4(92011),
	ove_0_4(100210),
	ove_0_4(106587),
	ove_0_4(41906),
	ove_0_4(98388),
	ove_0_4(106587),
	ove_0_4(88367)
}
local ove_0_7 = ove_0_0[19]
local ove_0_8, ove_0_9 = ove_0_0[9].get(2)

if not ove_0_9 then
	return
end

local ove_0_10 = menu("[Viktor]Draven", "[Viktor]AIO - " .. player.charName)

ove_0_10:header("f4header", "Core")

local ove_0_11 = {
	"RenektonE",
	"AkaliShadowDance",
	"YasuoE",
	"Headbutt",
	"FioraQ",
	"FizzQ",
	"DianaTeleport",
	"EliseSpiderQCast",
	"FizzPiercingStrike",
	"GragasE",
	"HecarimUlt",
	"HecarimRamp",
	"IreliaGatotsu",
	"IreliaQ",
	"JaxLeapStrike",
	"XinZhaoE",
	"XenZhaoE",
	"LeblancSlide",
	"LeblancSlideM",
	"JayceShockBlast",
	"LeonaZenithBlade",
	"Pantheon_LeapBash",
	"PantheonW",
	"PoppyHeroicCharge",
	"RenektonSliceAndDice",
	"RivenTriCleave",
	"SejuaniArcticAssault",
	"slashCast",
	"ViQ",
	"MonkeyKingNimbus",
	"XenZhaoSweep",
	"YasuoDashWrapper"
}

local function ove_0_12(arg_5_0, arg_5_1)
	-- function 5
	for iter_5_0, iter_5_1 in pairs(arg_5_0) do
		if iter_5_1 == arg_5_1 then
			return true
		end
	end

	return false
end

ove_0_10:keybind("AutoAxe", "Auto Catch Axe", nil, "T")
ove_0_10:menu("combo", "Combo")
ove_0_10.combo:boolean("q", "Use Q", true)
ove_0_10.combo:boolean("w", "Use W", true)
ove_0_10.combo:boolean("e", "Use E", true)
ove_0_10.combo:boolean("r", "Use R", true)
ove_0_10.combo:slider("rhit", "Use Min Hit", 2, 1, 5, 1)
ove_0_10.combo:keybind("autor", "Auto R", "A", nil)
ove_0_10:menu("harass", "Harass")
ove_0_10.harass:boolean("q", "Use Q", true)
ove_0_10.harass:boolean("w", "Use W", false)
ove_0_10.harass:boolean("e", "Use E", false)
ove_0_10.harass:slider("minimana", "Min Mana %", 65, 0, 100, 1)
ove_0_10:menu("laneclear", "Farm")
ove_0_10.laneclear:boolean("q", "Use Q", true)
ove_0_10.laneclear:slider("qhit", "Q Max Stacks", 1, 1, 3, 1)
ove_0_10.laneclear:slider("minimana", "Min Mana", 20, 0, 100, 1)
ove_0_10:menu("kill", "KillSteal")
ove_0_10.kill:boolean("e", "Use E", true)
ove_0_10.kill:boolean("r", "Use R", true)
ove_0_10.kill:boolean("enable", "Enable", true)
ove_0_10:menu("misc", "Misc")
ove_0_10.misc:slider("axerange", "Axe catch range", 750, 100, 1500, 50)
ove_0_10.misc:keybind("Tower", "Catch Axe Under Turret", nil, "S")
ove_0_10.misc:boolean("Enable", "Anti GapCloser", true)
ove_0_10.misc:boolean("Interrupt", "Auto Interrupt", true)
ove_0_10.misc:boolean("autow", "Auto W", true)

for iter_0_0 = 0, objManager.enemies_n - 1 do
	local ove_0_13 = objManager.enemies[iter_0_0]
	local ove_0_14 = ove_0_13:spellSlot(0).name
	local ove_0_15 = ove_0_13:spellSlot(1).name
	local ove_0_16 = ove_0_13:spellSlot(2).name
	local ove_0_17 = ove_0_13:spellSlot(3).name

	if ove_0_12(ove_0_11, ove_0_14) then
		ove_0_10.misc:boolean(ove_0_14, ove_0_13.charName .. "(Q)", true)
	elseif ove_0_14 == "JayceShockBlast" then
		ove_0_10.misc:boolean("JayceToTheSkies", ove_0_13.charName .. "(Q2)", true)
	end

	if ove_0_12(ove_0_11, ove_0_15) then
		ove_0_10.misc:boolean(ove_0_15, ove_0_13.charName .. "(W)", true)
	end

	if ove_0_12(ove_0_11, ove_0_16) and ove_0_16 ~= "HecarimRamp" then
		ove_0_10.misc:boolean(ove_0_16, ove_0_13.charName .. "(E)", true)
	elseif ove_0_16 == "HecarimRamp" then
		ove_0_10.misc:boolean("HecarimRampAttack", ove_0_13.charName .. "(E)", true)
	elseif ove_0_16 == "KhazixELong" or ove_0_16 == "KhazixE" then
		ove_0_10.misc:boolean("KhazixE", ove_0_13.charName .. "(E)", true)
		ove_0_10.misc:boolean("KhazixELong", ove_0_13.charName .. "(E)", true)
	end

	if ove_0_12(ove_0_11, ove_0_17) then
		ove_0_10.misc:boolean(ove_0_17, ove_0_13.charName .. "(R)", true)
	end
end

ove_0_10:menu("display", "Display")
ove_0_10.display:boolean("Target", "Display Target", true)
ove_0_10.display:boolean("axe", "Display Axe", true)
ove_0_10.display:boolean("axerange", "Display Catch Range", true)
ove_0_10.display:boolean("e", "Display E Range", true)
ove_0_10.display:boolean("Combo", "Display Damage", true)
ove_0_10.display:boolean("State", "Display State", true)

return ove_0_10

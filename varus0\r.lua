local lvxbot = module.load(header.id, 'lvxbot/main')

local input = {
  prediction = {
    type = 'Linear',
    --
    range = 1000,
    delay = 0.25,
    speed = 1850,
    width = 80,--65
    boundingRadiusMod = 1,
    --
     collision = {
      hero = false, --allow to hit other heros :-)
      minion = false,
      wall = true,
    },
    --
    hitchance = 0,
  },

  target_selector = {
    type = 'LESS_CAST_AD',
  },

  cast_spell = {
    type = 'pos',
	--type = 'release',
    slot = _R,
    arg1 = function(action)
      local seg = action.ts_result.seg
      local obj = action.ts_result.obj
      return seg.endPos:to3D(obj.y)
    end,
  },

  slot = _R,
  ignore_obj_radius = 2000,

}

return lvxbot.Nidaleeexpert.create(input)

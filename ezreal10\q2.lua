local slot10_a1323 = module.load(header.id, "lvxbot/main")
local slot11_a1326 = slot10_a1323.load("menu")

local slot13_a1328 = {
	delay = 0.25,
	range = 1110,
	PredZs = 0,
	type = "Linear",
	movseep = 1000,
	boundingRadiusMod = 1,
	speed = 2000,
	mov = 0.22,
	movtime = 0.35,
	width = 60,
	collision = {
		minion = true,
		hero = false,
		wall = true
	}
}
local slot13_a1330 = {
	pred = function ()
		if slot11_a1326.pred:get() then
			return 1
		else
			return 2
		end
	end,
	focusW = function()
		return 0
	end
}

local slot13_a1341 = {
	type = "pos",
	slot = _Q,
	arg1 = function (arg0_a1343)
		local slot1_a1345 = arg0_a1343.ts_result.seg
		local slot2_a1347 = arg0_a1343.ts_result.obj

		return vec3(slot1_a1345.endPos.x, game.mousePos.y, slot1_a1345.endPos.y)
	end
}

local slot12_a1327 = {
	ignore_obj_radius = 2000,
	prediction = slot13_a1328,
	cast = slot13_a1330,
	target_selector = {
		type = "LESS_CAST_AD"
	},
	cast_spell = slot13_a1341,
	slot = _Q
}

return slot10_a1323.ezrealcexpert.create(slot12_a1327)

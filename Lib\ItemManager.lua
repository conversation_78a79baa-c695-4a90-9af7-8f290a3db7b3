
local ove_0_10 = module.load("<PERSON>", "<PERSON><PERSON>/<PERSON><PERSON><PERSON><PERSON>")
local ove_0_11 = module.load("<PERSON>", "Lib/SpellDmg")
local ove_0_12 = module.load("<PERSON>", "Lib/DelayAction")
local ove_0_13 = player
local ove_0_14 = {
	concat = assert(table.concat),
	insert = assert(table.insert),
	remove = assert(table.remove),
	sort = assert(table.sort)
}

local function ove_0_15(arg_5_0, arg_5_1, arg_5_2)
	-- print 5
	for iter_5_0, iter_5_1 in pairs(arg_5_0) do
		if arg_5_2 and iter_5_1[arg_5_2] == arg_5_1 or iter_5_1 == arg_5_1 then
			return iter_5_0, iter_5_1
		end
	end
end

local ove_0_16 = ove_0_10.Class()
local ove_0_17 = module.seek("evade")
local ove_0_18 = ove_0_10.GetEnemyHeroes()
local ove_0_19 = module.internal("orb")

function ove_0_16.__init(arg_6_0, arg_6_1)
	-- print 6
	arg_6_0.menu = arg_6_1
	arg_6_0.Botrk = {
		name = "ItemSwordOfFeastAndFamine",
		range = 550
	}
	arg_6_0.Cutlass = {
		name = "BilgewaterCutlass",
		range = 550
	}
	arg_6_0.Yahumu = {
		name = "YoumusBlade",
		range = ove_0_13.attackRange
	}
	arg_6_0.QSS = {
		name = "QuicksilverSash"
	}
	arg_6_0.QSSUpper = {
		name = "ItemMercurial"
	}

	if arg_6_1 then
		arg_6_0.menu:menu("off", "Offensive Items")
		arg_6_0.menu.off:header("header_botrk", "Blade of the Ruined King")
		arg_6_0.menu.off:boolean("useB", "Use :", false)
		arg_6_0.menu.off:slider("enemyHpB", "Enemy Hp % :", 45, 0, 100, 1)
		arg_6_0.menu.off:header("header_cutlass", "Bilgewater Cutlass")
		arg_6_0.menu.off:boolean("useC", "Use :", false)
		arg_6_0.menu.off:slider("enemyHpC", "Enemy Hp % :", 45, 0, 100, 1)
		arg_6_0.menu.off:header("header_youmu", "Youmuu's Ghostblade")
		arg_6_0.menu.off:boolean("useY", "Use :", false)
		arg_6_0.menu.off:slider("enemyCount", "Enemy Count :", 1, 1, 5, 1)
		arg_6_0.menu.off:slider("enemyRange", "Enemy Range :", 100, 0, arg_6_0.Yahumu.range + 200, 10)
		arg_6_0.menu:menu("def", "Defensive Items")
		arg_6_0.menu.def:header("header_QSS", "Quicksilver Sash")
		arg_6_0.menu.def:boolean("use", "Use :", false)
		arg_6_0.menu.def:boolean("delay", "Use Delay:", false)
		arg_6_0.menu.def:slider("delayRange", "Delay : ", 500, 0, 1000, 10)
		arg_6_0.menu:menu("other", "Other Items")
		arg_6_0.menu.other:header("header_trinnket", "Auto Trinket")
		arg_6_0.menu.other:boolean("use", "Use if vision lost :", false)
		arg_6_0.menu.other:dropdown("logic", "Logic : ", 1, {
			"Only Combo Mode",
			"Always"
		})
	end

	cb.add(cb.losevision, function(arg_7_0)
		-- print 7
		arg_6_0:Vision(arg_7_0)
	end)
end

function ove_0_16.checkFunc(arg_8_0)
	-- print 8
	if arg_8_0.menu.other.logic:get() == 1 and ove_0_19.combat.is_active() then
		return true
	elseif arg_8_0.menu.other.logic:get() == 2 then
		return true
	end

	return false
end

function ove_0_16.Vision(arg_9_0, arg_9_1)
	-- print 9
	if arg_9_0.menu.other.use:get() and arg_9_0:checkFunc() and arg_9_1.type == TYPE_HERO and arg_9_1.team == TEAM_ENEMY and not arg_9_1.isDead and player.path.serverPos:dist(arg_9_1.path.serverPos) < 600 then
		for iter_9_0 = 6, 12 do
			if player:spellSlot(iter_9_0).isNotEmpty then
				local slot_9_0 = player:spellSlot(iter_9_0).name

				if (slot_9_0 == "TrinketTotemLvl1" or slot_9_0 == "ItemGhostWard" or slot_9_0 == "TrinketOrbLvl3") and player:spellSlot(iter_9_0).state == 0 then
					player:castSpell("pos", iter_9_0, vec3(player.pos:lerp(arg_9_1, 1.1)))
					ove_0_19.core.set_server_pause()

					break
				end
			end
		end
	end
end

function ove_0_16.Cast(arg_10_0, arg_10_1)
	-- print 10
	if ove_0_17 and ove_0_17.is_active() then
		return
	end

	if ove_0_10.PlayerCC() and arg_10_0.menu.def.use:get() then
		local slot_10_0 = arg_10_0.menu.def.delay:get() and math.random(0.01, arg_10_0.menu.def.delayRange:get() / 1000) or 0.001
		local slot_10_1, slot_10_2 = arg_10_0:HasItem(arg_10_0.QSS.name)
		local slot_10_3, slot_10_4 = arg_10_0:HasItem(arg_10_0.QSSUpper.name)

		if slot_10_1 then
			ove_0_12.Cast(function()
				-- print 11
				player:castSpell("self", slot_10_2)
			end, slot_10_0)
		elseif slot_10_3 then
			ove_0_12.Cast(function()
				-- print 12
				player:castSpell("self", slot_10_4)
			end, slot_10_0)
		end
	end

	if arg_10_1 and ove_0_19.combat.is_active() then
		if arg_10_0.menu.off.useB:get() and ove_0_10.Health(arg_10_1) < arg_10_0.menu.off.enemyHpB:get() then
			local slot_10_5, slot_10_6 = arg_10_0:HasItem(arg_10_0.Botrk.name)

			if slot_10_5 then
				player:castSpell("obj", slot_10_6, arg_10_1)
			end
		end

		if arg_10_0.menu.off.useC:get() and ove_0_10.Health(arg_10_1) < arg_10_0.menu.off.enemyHpC:get() then
			local slot_10_7, slot_10_8 = arg_10_0:HasItem(arg_10_0.Cutlass.name)

			if slot_10_7 then
				player:castSpell("obj", slot_10_8, arg_10_1)
			end
		end

		if arg_10_0.menu.off.useY:get() and ove_0_10.HeroCount(arg_10_0.menu.off.enemyRange:get()) >= arg_10_0.menu.off.enemyCount:get() then
			local slot_10_9, slot_10_10 = arg_10_0:HasItem(arg_10_0.Yahumu.name)

			if slot_10_9 then
				player:castSpell("self", slot_10_10)
			end
		end
	end
end

function ove_0_16.HasItem(arg_13_0, arg_13_1)
	-- print 13
	for iter_13_0 = 6, 11 do
		if player:spellSlot(iter_13_0).isNotEmpty and player:spellSlot(iter_13_0).name == arg_13_1 and ove_0_13:spellSlot(iter_13_0).state == 0 then
			return true, iter_13_0
		end
	end

	return false, 6
end

return ove_0_16



local ove_0_10 = module.internal("pred")
local ove_0_11 = module.load(header.id, "common/common")
local ove_0_12 = module.load(header.id, "common/GeometryLib")
local ove_0_13 = {}
local ove_0_14 = ove_0_12.Vector
local ove_0_15 = os.clock()
local ove_0_16 = {
	Ezreal = {
		EzrealE = {
			range = 475,
			name = "Arcane Shift",
			windup = 0.25
		}
	},
	FiddleSticks = {
		FiddleSticksR = {
			range = 800,
			name = "Crowstorm",
			windup = 1.5
		}
	},
	<PERSON><PERSON><PERSON> = {
		Riftwalk = {
			range = 500,
			name = "Riftwalk",
			windup = 0.25
		}
	},
	Kat<PERSON> = {
		KatarinaEWrapper = {
			range = 725,
			name = "Shunpo",
			windup = 0.125
		}
	},
	Pyke = {
		PykeR = {
			range = 750,
			name = "Death from Below",
			windup = 0.5
		}
	},
	Shaco = {
		Deceive = {
			range = 400,
			name = "Deceive",
			windup = 0.25
		}
	},
	Viego = {
		ViegoR = {
			range = 500,
			name = "Heartbreaker",
			windup = 0.5
		}
	},
	<PERSON> = {
		ZoeR = {
			range = 575,
			name = "Portal Jump",
			windup = 0.25
		}
	}
}
local ove_0_17 = {
	[BUFF_CHARM] = true,
	[BUFF_FEAR] = true,
	[BUFF_FLEE] = true,
	[BUFF_KNOCKUP] = true,
	[BUFF_SNARE] = true,
	[BUFF_STUN] = true,
	[BUFF_SUPPRESSION] = true,
	[BUFF_TAUNT] = true
}
local ove_0_18 = {
	dashing = false,
	pathTimer = 0,
	dashSpeed = 0,
	miaTimer = -1,
	castEndTimer = 0,
	blink = {},
	isCastTime = os.clock()
}

function ove_0_13.GetInvulnerableDuration(arg_5_0)
	for iter_5_0, iter_5_1 in pairs(arg_5_0.buff) do
		if iter_5_1 and iter_5_1.valid and iter_5_1.startTime > 0 and iter_5_1.type == BUFF_INVULNERABILITY then
			return math.max(0, iter_5_1.endTime - game.time)
		end
	end

	return 0
end

function ove_0_13.GetInvisibleDuration(arg_6_0)
	local slot_6_0 = ove_0_18[arg_6_0.networkID]

	if not slot_6_0 then
		return math.huge
	end

	local slot_6_1 = slot_6_0.miaTimer

	if slot_6_1 then
		if slot_6_1 == 0 then
			return 0
		else
			return game.time - slot_6_1
		end
	end
end

function ove_0_13.GetMovementSpeed(arg_7_0)
	local slot_7_0 = arg_7_0.path

	return slot_7_0 and slot_7_0.isDashing and slot_7_0.dashSpeed or arg_7_0.moveSpeed
end

function ove_0_13.GetReactionTime(arg_8_0)
	local slot_8_0 = ove_0_18[arg_8_0.networkID]

	if not slot_8_0 then
		return 0
	end

	local slot_8_1 = ove_0_14(player.path.serverPos)
	local slot_8_2 = ove_0_14(arg_8_0.path.serverPos)
	local slot_8_3 = slot_8_2 + ove_0_14(arg_8_0.direction) * 100
	local slot_8_4 = math.rad(slot_8_1:AngleBetween(slot_8_2, slot_8_3))
	local slot_8_5 = math.abs(math.cos(slot_8_4)) * 0.215

	return math.max(game.time - slot_8_0.pathTimer <= 0.034 and 0.125 or 0, slot_8_5)
end

function ove_0_13.GetImmobileDuration(arg_9_0)
	local slot_9_0 = 0

	for iter_9_0, iter_9_1 in pairs(arg_9_0.buff) do
		if iter_9_1 and iter_9_1.valid and iter_9_1.startTime > 0 and ove_0_17[iter_9_1.type] then
			local slot_9_1 = iter_9_1.endTime - game.time

			return math.max(0, iter_9_1.startTime, slot_9_1)
		end
	end

	return 0
end

local function ove_0_19()
	if ove_0_18.isCastTime and os.clock() > ove_0_18.isCastTime + 0.125 then
		ove_0_18.blink = {}
	end

	for iter_10_0, iter_10_1 in ipairs(ove_0_11.GetEnemyHeroes()) do
		local slot_10_0 = ove_0_18[iter_10_1.networkID]

		if slot_10_0 then
			if iter_10_1 and iter_10_1.isVisible then
				slot_10_0.miaTimer = 0
			elseif slot_10_0.miaTimer == 0 then
				slot_10_0.miaTimer = game.time + 0.0167
			end
		end
	end
end

local function ove_0_20(arg_11_0)
	if not arg_11_0 then
		return
	end

	if arg_11_0.type == TYPE_HERO and arg_11_0.team ~= player.team then
		local slot_11_0 = {
			dashing = arg_11_0.path.isDashing,
			dashSpeed = arg_11_0.path.dashSpeed,
			pathTimer = game.time
		}

		ove_0_18[arg_11_0.networkID] = slot_11_0
	end
end

local function ove_0_21(arg_12_0)
	if arg_12_0 and arg_12_0.owner.type == TYPE_HERO and arg_12_0.owner.team ~= player.team then
		if ove_0_16[arg_12_0.owner.charName] and ove_0_16[arg_12_0.owner.charName][arg_12_0.name] then
			local slot_12_0 = ove_0_16[arg_12_0.owner.charName][arg_12_0.name]
			local slot_12_1 = ove_0_14(arg_12_0.startPos)
			local slot_12_2 = ove_0_14(arg_12_0.endPos)
			local slot_12_3 = slot_12_1:extended(slot_12_2, math.min(slot_12_0.range, slot_12_1:distanceTo(slot_12_2)))

			ove_0_18.blink = {
				pos = slot_12_3,
				endTime = arg_12_0.windUpTime + slot_12_0.windup
			}
			ove_0_18.isCastTime = os.clock() + arg_12_0.windUpTime + slot_12_0.windup
		end

		if arg_12_0 and arg_12_0.windUpTime > 0 then
			ove_0_18.castEndTimer = arg_12_0.windUpTime
		end
	end
end

function ove_0_13.TimerLarpsForNewPath(arg_13_0, arg_13_1, arg_13_2)
	if not arg_13_1 then
		return
	end

	local slot_13_0 = ove_0_14(arg_13_0.source)
	local slot_13_1 = ove_0_14(arg_13_1.endPos:to3D(arg_13_2.y))
	local slot_13_2 = ove_0_14(arg_13_1.endPos:to3D(arg_13_2.path.serverPos.y))

	if slot_13_0:distanceTo(arg_13_0.type == "circular" and slot_13_1 or slot_13_2) > arg_13_0.range then
		return -1
	end

	local slot_13_3 = slot_13_0:distanceTo(slot_13_1)
	local slot_13_4 = slot_13_3 / arg_13_0.speed + arg_13_0.delay + 0.034 + network.latency

	if slot_13_4 < ove_0_13.GetInvulnerableDuration(arg_13_2) then
		return -2
	end

	local slot_13_5 = (arg_13_0.type ~= "conic" and arg_13_0.radius or arg_13_0.width or math.min(arg_13_0.range - slot_13_3, slot_13_3, slot_13_3 * (arg_13_0.angle or 50) / 90)) + (arg_13_0.hitbox and arg_13_2.boundingRadius or 0)
	local slot_13_6 = ove_0_13.GetMovementSpeed(arg_13_2)
	local slot_13_7 = ove_0_13.GetReactionTime(arg_13_2)
	local slot_13_8 = ove_0_13.GetImmobileDuration(arg_13_2)

	return math.min(1, slot_13_5 / slot_13_6 / math.max(0, slot_13_4 - slot_13_7 - slot_13_8))
end

function ove_0_13.GetHitChance(arg_14_0, arg_14_1, arg_14_2)
	if not arg_14_1 then
		return
	end

	if ove_0_18[arg_14_2.networkID] then
		local slot_14_0 = ove_0_18[arg_14_2.networkID]

		if slot_14_0 and slot_14_0.blink or slot_14_0.dashing then
			return slot_14_0.endTime or 0.25
		end
	end

	local slot_14_1 = ove_0_14(arg_14_0.source)
	local slot_14_2 = ove_0_14(arg_14_1.endPos:to3D(arg_14_2.y))
	local slot_14_3 = ove_0_14(arg_14_1.endPos:to3D(arg_14_2.path.serverPos.y))

	if slot_14_1:distanceTo(arg_14_0.type == "circular" and slot_14_2 or slot_14_3) > arg_14_0.range then
		return -1
	end

	local slot_14_4 = ove_0_10.collision.get_prediction(arg_14_0, arg_14_1, arg_14_2)

	if arg_14_0.collision and slot_14_4 then
		return -1
	end

	local slot_14_5 = slot_14_1:distanceTo(slot_14_2)
	local slot_14_6 = slot_14_5 / arg_14_0.speed + arg_14_0.delay + 0.034 + network.latency

	if slot_14_6 < ove_0_13.GetInvulnerableDuration(arg_14_2) then
		return -2
	end

	local slot_14_7 = (arg_14_0.type ~= "conic" and arg_14_0.radius or arg_14_0.width or math.min(arg_14_0.range - slot_14_5, slot_14_5, slot_14_5 * (arg_14_0.angle or 50) / 90)) + (arg_14_0.hitbox and arg_14_2.boundingRadius or 0)
	local slot_14_8 = ove_0_13.GetMovementSpeed(arg_14_2)
	local slot_14_9 = ove_0_13.GetReactionTime(arg_14_2)
	local slot_14_10 = ove_0_13.GetImmobileDuration(arg_14_2)

	if arg_14_0.speed == math.huge then
		arg_14_0.speed = arg_14_2.moveSpeed * 3
	end

	local slot_14_11 = arg_14_2.path.serverPos2D - arg_14_1.startPos
	local slot_14_12 = arg_14_2.direction2D:dot(slot_14_11:norm())
	local slot_14_13 = math.abs(arg_14_2.direction2D:cross(slot_14_11:norm()))
	local slot_14_14 = math.atan(slot_14_13, slot_14_12)
	local slot_14_15 = arg_14_2.direction2D * slot_14_8 * (1 - slot_14_12)
	local slot_14_16 = slot_14_11:norm() * arg_14_0.speed * (2 - slot_14_13)
	local slot_14_17 = (slot_14_16 - slot_14_15) * (2 - slot_14_14)
	local slot_14_18 = slot_14_15 + slot_14_16 + slot_14_17
	local slot_14_19 = arg_14_2.path.serverPos2D + slot_14_15 * (arg_14_0.delay + network.latency) - slot_14_18 * ((arg_14_0.radius or arg_14_0.width + arg_14_2.boundingRadius) / slot_14_18:len())
	local slot_14_20 = math.abs(arg_14_0.radius or arg_14_0.width, arg_14_2.boundingRadius)
	local slot_14_21 = slot_14_20 * slot_14_12 + slot_14_20 * slot_14_13
	local slot_14_22 = arg_14_0.radius or arg_14_0.width

	if arg_14_0.radius or arg_14_0.width < arg_14_2.boundingRadius then
		slot_14_22 = slot_14_22 + slot_14_21
	else
		slot_14_22 = slot_14_22 - slot_14_21
	end

	local slot_14_23 = slot_14_19 - slot_14_16 * (slot_14_22 / slot_14_17:len()) - slot_14_17 * (slot_14_21 / slot_14_16:len()) - arg_14_1.startPos
	local slot_14_24 = slot_14_15:dot(slot_14_15) - slot_14_16:dot(slot_14_16)
	local slot_14_25 = slot_14_15:dot(slot_14_23) * 2
	local slot_14_26 = slot_14_23:dot(slot_14_23)
	local slot_14_27 = slot_14_25 * slot_14_25 - 4 * slot_14_24 * slot_14_26

	if slot_14_27 < 0 then
		return
	end

	local slot_14_28 = math.sqrt(slot_14_27)
	local slot_14_29 = 2 * slot_14_26 / (slot_14_28 - slot_14_25)
	local slot_14_30 = (-slot_14_25 - slot_14_28) / (2 * slot_14_24)

	return math.min(1, slot_14_7 / slot_14_8 / math.max(0, slot_14_6 - slot_14_9 + slot_14_29 - slot_14_10))
end

local function ove_0_22(arg_15_0, arg_15_1, arg_15_2)
	local slot_15_0 = arg_15_0.delay + network.latency

	if arg_15_1.startPos:dist(arg_15_1.endPos) + slot_15_0 * arg_15_2.moveSpeed + arg_15_2.boundingRadius > arg_15_0.range then
		return false
	end

	if ove_0_10.collision.get_prediction(arg_15_0, arg_15_1, arg_15_2) then
		return false
	end

	if arg_15_0.type == "linear" then
		if ove_0_10.trace.linear.hardlock(arg_15_0, arg_15_1, arg_15_2) then
			return true
		end

		if ove_0_10.trace.linear.hardlockmove(arg_15_0, arg_15_1, arg_15_2) then
			return true
		end
	elseif arg_15_0.type == "circular" then
		if ove_0_10.trace.circular.hardlock(arg_15_0, arg_15_1, arg_15_2) then
			return true
		end

		if ove_0_10.trace.circular.hardlockmove(arg_15_0, arg_15_1, arg_15_2) then
			return true
		end
	end

	local slot_15_1 = arg_15_2.moveSpeed / arg_15_0.speed

	if ove_0_10.trace.newpath(arg_15_2, 0.034 + network.latency, slot_15_0 + slot_15_1) then
		return true
	end

	return true
end

local function ove_0_23(arg_16_0, arg_16_1, arg_16_2, arg_16_3, arg_16_4)
	local function slot_16_0(arg_17_0, arg_17_1, arg_17_2)
		if arg_17_2 > arg_16_0.range then
			return
		end

		if arg_17_1.buff[BUFF_UNKILLABLE] then
			return
		end

		arg_16_2 = arg_16_2 or player

		local slot_17_0

		if arg_16_0.type == "linear" then
			slot_17_0 = ove_0_10.linear.get_prediction(arg_16_0, arg_17_1, vec2(arg_16_2.x, arg_16_2.z))
		elseif arg_16_0.type == "circular" then
			slot_17_0 = ove_0_10.circular.get_prediction(arg_16_0, arg_17_1, vec2(arg_16_2.x, arg_16_2.z))
		end

		if not slot_17_0 then
			return false
		end

		if slot_17_0.startPos:dist(slot_17_0.endPos) > arg_16_0.range then
			return false
		end

		if arg_16_1 and not ove_0_22(arg_16_0, slot_17_0, arg_17_1) then
			return false
		end

		local slot_17_1 = ove_0_13.GetHitChance(arg_16_0, slot_17_0, arg_17_1)

		if slot_17_1 <= 0 then
			return false
		end

		arg_17_0.obj = arg_17_1
		arg_17_0.pos = (ove_0_10.core.get_pos_after_time(arg_17_1, slot_17_1) + slot_17_0.endPos) / 2

		if not arg_16_3 and not arg_16_4 then
			arg_17_0.targetPosition = ove_0_10.core.project(player.path.serverPos2D, arg_17_1.path, network.latency - slot_17_1, arg_16_0.speed + arg_16_0.delay, ove_0_13.GetMovementSpeed(arg_17_1))
		end

		if arg_16_3 and not arg_16_4 then
			arg_17_0.targetPosition = ove_0_10.core.project(player.path.serverPos2D, arg_17_1.path, network.latency - slot_17_1 + arg_16_0.delay, arg_16_0.speed, ove_0_13.GetMovementSpeed(arg_17_1))
		end

		if arg_16_4 then
			arg_17_0.targetPosition = ove_0_10.core.project(player.path.serverPos2D, arg_17_1.path, network.latency + slot_17_1, arg_16_0.speed, ove_0_13.GetMovementSpeed(arg_17_1))
		end

		arg_17_0.seg = slot_17_0

		if (slot_17_0.endPos - slot_17_0.startPos):len() / arg_16_0.speed - slot_17_1 < arg_16_0.delay + network.latency then
			return true
		end
	end

	return {
		Result = slot_16_0
	}
end

local function ove_0_24(arg_18_0, arg_18_1, arg_18_2, arg_18_3)
	if not arg_18_1 then
		return
	end

	if arg_18_1.buff[BUFF_UNKILLABLE] then
		return
	end

	arg_18_3 = arg_18_3 or player

	local slot_18_0

	if arg_18_0.type == "linear" then
		slot_18_0 = ove_0_10.linear.get_prediction(arg_18_0, arg_18_1, vec2(arg_18_3.x, arg_18_3.z))
	elseif arg_18_0.type == "circular" then
		slot_18_0 = ove_0_10.circular.get_prediction(arg_18_0, arg_18_1, vec2(arg_18_3.x, arg_18_3.z))
	end

	if not slot_18_0 then
		return
	end

	if slot_18_0.startPos:dist(slot_18_0.endPos) > arg_18_0.range then
		return
	end

	if arg_18_2 and not ove_0_22(arg_18_0, slot_18_0, arg_18_1) then
		return
	end

	local slot_18_1 = ove_0_13.GetHitChance(arg_18_0, slot_18_0, arg_18_1)

	if slot_18_1 <= 0 then
		return
	end

	local slot_18_2 = (ove_0_10.core.get_pos_after_time(arg_18_1, slot_18_1) + slot_18_0.endPos) / 2

	if (slot_18_0.endPos - slot_18_0.startPos):len() / arg_18_0.speed - slot_18_1 < arg_18_0.delay + network.latency then
		return slot_18_2
	end
end

cb.add(cb.tick, ove_0_19)
cb.add(cb.spell, ove_0_21)
cb.add(cb.path, ove_0_20)

return {
	GetTargetPrediction = function(arg_19_0, arg_19_1, arg_19_2, arg_19_3, arg_19_4)
		return ove_0_23(arg_19_0, arg_19_1, arg_19_2, arg_19_3, arg_19_4).Result
	end,
	GetPrediction = function(arg_20_0, arg_20_1, arg_20_2, arg_20_3)
		return ove_0_24(arg_20_0, arg_20_1, arg_20_2, arg_20_3)
	end,
	GetReactionUnit = function(arg_21_0)
		return ove_0_13.GetReactionTime(arg_21_0)
	end,
	GetMovementSpeed = function(arg_22_0)
		return ove_0_13.GetMovementSpeed(arg_22_0)
	end,
	GetNewPatchTime = function(arg_23_0, arg_23_1, arg_23_2)
		return ove_0_13.TimerLarpsForNewPath(arg_23_0, arg_23_1, arg_23_2)
	end,
	GetIsMobilte = function(arg_24_0)
		return ove_0_13.GetImmobileDuration(arg_24_0)
	end
}

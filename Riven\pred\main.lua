return {
  aa = module.load(header.id, 'Riven/pred/aa'),
  q = module.load(header.id, 'Riven/pred/q'),
  w = module.load(header.id, 'Riven/pred/w'),
  e = module.load(header.id, 'Riven/pred/e'),
  e_q = module.load(header.id, 'Riven/pred/e_q'),
  e_w = module.load(header.id, 'Riven/pred/e_w'),
  flash_w = module.load(header.id, 'Riven/pred/flash_w'),
  flash_q = module.load(header.id, 'Riven/pred/flash_q'),
  e_flash_w = module.load(header.id, 'Riven/pred/e_flash_w'),
  e_flash_q = module.load(header.id, 'Riven/pred/e_flash_q'),
  r1 = module.load(header.id, 'Riven/pred/r1'),
  r2 = module.load(header.id, 'Riven/pred/r2'),
  r2_dmg = module.load(header.id, 'Riven/pred/r2_dmg'),
}
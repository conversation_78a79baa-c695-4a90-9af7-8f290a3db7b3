# 德莱厄斯磁性Q多人逻辑实现说明

## 概述
基于Darius.cpp中的GetAOEQPosition和GetAOEQFlashPosition函数，完美复现了磁性Q多人逻辑到Lua代码中。

## 主要功能

### 1. 磁性Q多人AOE位置计算 (GetAOEQPosition)
- **功能**: 计算最佳的Q技能AOE位置，以命中最多敌人
- **算法**: 
  - 遍历所有敌人组合，计算两两之间的中心位置
  - 在垂直方向上计算最佳释放位置
  - 考虑移动预测和到达时间
  - 检查内圈(240)和外圈(460)范围
- **返回**: 最佳命中数量和位置

### 2. 磁性Q闪现AOE位置计算 (GetAOEQFlashPosition)
- **功能**: 计算Q闪现的最佳AOE位置
- **特点**: 
  - 使用固定的0.25秒预测时间
  - 闪现距离限制为420
  - 优先选择命中数量最多的位置

### 3. Q状态下的智能移动
- **跟随单个目标**: 
  - 计算290距离的跟随位置
  - 提供中心、左偏、右偏三个选择
  - 根据鼠标位置选择最佳移动点
- **AOE优先**: 
  - 当检测到多人AOE机会时优先移动到AOE位置
  - 只有在目标血量足够时才进行AOE

### 4. Q闪现击杀逻辑
- **Q状态闪现**: 
  - 检测可击杀目标
  - 计算闪现到350距离的位置
  - 考虑Q+R的总伤害
- **预备Q闪**: 
  - 非Q状态下预先释放Q
  - 为后续闪现做准备
- **Q闪现AOE**: 
  - 血量阈值触发
  - 最少命中人数要求

## 菜单配置

### 磁性Q多人设置
- `开启磁性Q多人`: 总开关
- `连招模式`: 连招时启用
- `骚扰模式`: 骚扰时启用  
- `清野模式`: 清野时启用
- `最少命中人数`: AOE触发的最少人数(2-5)
- `移动模式`: 智能选择 / 中心位置

### Q闪现设置
- `Q闪现击杀`: 启用击杀闪现
- `Q闪现AOE`: 启用AOE闪现
- `AOE最少命中人数`: AOE闪现的最少人数
- `AOE血量阈值%`: 触发AOE闪现的血量百分比

## 技术实现细节

### 关键常量
```lua
local InnerRadiusSqr = 240 * 240  -- 内圈半径平方
local OuterRadiusSqr = 460 * 460  -- 外圈半径平方
local limitRadiusSqr = 30 * 30    -- 容错范围
local radius = 350                -- Q技能半径
```

### 预测系统
- 使用`slot_22_1.core.lerp`进行路径预测
- 考虑网络延迟和技能延迟
- 动态计算移动速度和到达时间

### 位置计算
- 90度旋转计算垂直方向
- 距离优化选择最近位置
- 墙体检测和可通行性验证

## 与原版C++代码的一致性

1. **算法逻辑**: 完全一致的双重循环和位置计算
2. **常量数值**: 所有半径、距离、时间参数保持一致
3. **预测机制**: 相同的移动预测和时间计算
4. **优先级**: 相同的命中数量排序和距离检查
5. **变量命名**: 保持原有的命名约定

## 使用说明

1. 在菜单中启用相应功能
2. Q技能释放后会自动进行磁性移动
3. 检测到多人机会时优先AOE
4. 满足条件时自动Q闪现击杀或AOE

这个实现完美复现了原版C++代码的所有逻辑和细节，确保了功能的一致性和可靠性。

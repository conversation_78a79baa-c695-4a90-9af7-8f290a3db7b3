

local ove_0_10 = module.internal("pred")
local ove_0_11 = module.internal("orb")
local ove_0_12 = {
	invuln = function(arg_5_0)
		if arg_5_0.buff.fioraw or arg_5_0.buff.rebirth or arg_5_0.buff.pantheone or arg_5_0.buff.gwenw or arg_5_0.buff.judicatorintervention or arg_5_0.buff.kayler or arg_5_0.buff.undyingrage or arg_5_0.buff.sionpassivezombie or arg_5_0.buff.taricr or arg_5_0.buff.chronoshift or arg_5_0.buff.kindredrnodeathbuff then
			return true
		end

		return false
	end
}
local ove_0_13 = module.internal("crypt")
local ove_0_14 = {}
local ove_0_15

function ove_0_12.DelayAction(arg_6_0, arg_6_1, arg_6_2)
	if not ove_0_15 then
		function ove_0_15()
			for iter_7_0, iter_7_1 in pairs(ove_0_14) do
				if iter_7_0 <= os.clock() then
					for iter_7_2 = 1, #iter_7_1 do
						local slot_7_0 = iter_7_1[iter_7_2]

						if slot_7_0 and slot_7_0.func then
							slot_7_0.func(unpack(slot_7_0.args or {}))
						end
					end

					ove_0_14[iter_7_0] = nil
				end
			end
		end

		cb.add(cb.tick, ove_0_15)
	end

	local slot_6_0 = os.clock() + (arg_6_1 or 0)

	if ove_0_14[slot_6_0] then
		ove_0_14[slot_6_0][#ove_0_14[slot_6_0] + 1] = {
			func = arg_6_0,
			args = arg_6_2
		}
	else
		ove_0_14[slot_6_0] = {
			{
				func = arg_6_0,
				args = arg_6_2
			}
		}
	end
end

local ove_0_16

function ove_0_12.SetInterval(arg_8_0, arg_8_1, arg_8_2, arg_8_3)
	if not ove_0_16 then
		function ove_0_16(arg_9_0, arg_9_1, arg_9_2, arg_9_3, arg_9_4)
			if arg_9_0(unpack(arg_9_4 or {})) ~= false and (not arg_9_3 or arg_9_3 > 1) then
				ove_0_12.DelayAction(ove_0_16, arg_9_2 - (os.clock() - arg_9_1 - arg_9_2), {
					arg_9_0,
					arg_9_1 + arg_9_2,
					arg_9_2,
					arg_9_3 and arg_9_3 - 1,
					arg_9_4
				})
			end
		end
	end

	ove_0_12.DelayAction(ove_0_16, arg_8_1, {
		arg_8_0,
		os.clock(),
		arg_8_1 or 0,
		arg_8_2,
		arg_8_3
	})
end

function ove_0_12.print(arg_10_0, arg_10_1)
	local slot_10_0 = arg_10_1 or 42

	console.set_color(slot_10_0)
	print(arg_10_0)
	console.set_color(15)
end

function ove_0_12.GetPercentHealth(arg_11_0)
	local slot_11_0 = arg_11_0 or player

	return slot_11_0.health / slot_11_0.maxHealth * 100
end

function ove_0_12.GetPercentMana(arg_12_0)
	local slot_12_0 = arg_12_0 or player

	return slot_12_0.mana / slot_12_0.maxMana * 100
end

function ove_0_12.GetPercentPar(arg_13_0)
	local slot_13_0 = arg_13_0 or player

	return slot_13_0.par / slot_13_0.maxPar * 100
end

function ove_0_12.ResetOrbDelay(arg_14_0)
	if arg_14_0 and arg_14_0 >= 0 then
		ove_0_12.DelayAction(function()
			ove_0_11.core.set_pause(0)
			ove_0_11.core.set_pause_move(0)
			ove_0_11.core.set_pause_attack(0)
		end, arg_14_0)
	end
end

function ove_0_12.ResetAllOrbDelay(arg_16_0)
	if arg_16_0 and arg_16_0 >= 0 then
		ove_0_12.DelayAction(function()
			ove_0_11.core.reset()
			ove_0_11.core.set_pause(0)
			ove_0_11.core.set_pause_move(0)
			ove_0_11.core.set_pause_attack(0)
		end, arg_16_0)
	end
end

function ove_0_12.ResetOrb()
	ove_0_11.core.set_pause(0)
	ove_0_11.core.set_pause_move(0)
	ove_0_11.core.set_pause_attack(0)
end

function ove_0_12.CheckBuffType(arg_19_0, arg_19_1)
	if arg_19_0 and arg_19_0.buffManager and arg_19_0.buffManager.count > 0 then
		for iter_19_0 = 0, arg_19_0.buffManager.count - 1 do
			local slot_19_0 = arg_19_0.buffManager:get(iter_19_0)

			if slot_19_0 and slot_19_0.valid and slot_19_0.type == arg_19_1 and (slot_19_0.stacks > 0 or slot_19_0.stacks2 > 0) then
				return true
			end
		end
	end
end

function ove_0_12.ReverseTable(arg_20_0)
	local slot_20_0 = 1
	local slot_20_1 = #arg_20_0

	while slot_20_0 < slot_20_1 do
		arg_20_0[slot_20_0], arg_20_0[slot_20_1] = arg_20_0[slot_20_1], arg_20_0[slot_20_0]
		slot_20_0 = slot_20_0 + 1
		slot_20_1 = slot_20_1 - 1
	end
end

function ove_0_12.tableslice(arg_21_0, arg_21_1, arg_21_2, arg_21_3)
	local slot_21_0 = {}

	for iter_21_0 = arg_21_1 or 1, arg_21_2 or #arg_21_0, arg_21_3 or 1 do
		slot_21_0[#slot_21_0 + 1] = arg_21_0[iter_21_0]
	end

	return slot_21_0
end

function ove_0_12.CombatActive()
	return ove_0_11.menu.combat.key:get()
end

function ove_0_12.ConditionalCombat(arg_23_0)
	return not ove_0_12.CombatActive() or arg_23_0
end

function ove_0_12.ReturnBuff(arg_24_0, arg_24_1)
	if arg_24_0 then
		for iter_24_0 = 0, arg_24_0.buffManager.count - 1 do
			local slot_24_0 = arg_24_0.buffManager:get(iter_24_0)

			if slot_24_0 and slot_24_0.valid and string.lower(slot_24_0.name) == string.lower(arg_24_1) and (slot_24_0.stacks > 0 or slot_24_0.stacks2 > 0) then
				return slot_24_0
			end
		end
	end
end

function ove_0_12.ReturnBuffstacks(arg_25_0, arg_25_1)
	if arg_25_0 then
		for iter_25_0 = 0, arg_25_0.buffManager.count - 1 do
			local slot_25_0 = arg_25_0.buffManager:get(iter_25_0)

			if slot_25_0 and slot_25_0.valid and string.lower(slot_25_0.name) == string.lower(arg_25_1) and (slot_25_0.stacks > 0 or slot_25_0.stacks2 > 0) then
				return slot_25_0.stacks2
			end
		end
	end
end

function ove_0_12.CheckBuff(arg_26_0, arg_26_1)
	if arg_26_0 and arg_26_0.buffManager and arg_26_0.buffManager.count > 0 then
		for iter_26_0 = 0, arg_26_0.buffManager.count - 1 do
			local slot_26_0 = arg_26_0.buffManager:get(iter_26_0)

			if slot_26_0 and slot_26_0.valid and string.lower(slot_26_0.name) == string.lower(arg_26_1) and (slot_26_0.stacks > 0 or slot_26_0.stacks2 > 0) then
				return true
			end
		end
	end
end

function ove_0_12.CheckBuffWithTimeEnd(arg_27_0, arg_27_1)
	if arg_27_0 and arg_27_0.buffManager and arg_27_0.buffManager.count > 0 then
		for iter_27_0 = 0, arg_27_0.buffManager.count - 1 do
			local slot_27_0 = arg_27_0.buffManager:get(iter_27_0)

			if slot_27_0 and slot_27_0.valid and slot_27_0.name == arg_27_1 and (slot_27_0.stacks > 0 or slot_27_0.stacks2 > 0) and game.time <= slot_27_0.endTime then
				return true, slot_27_0.endTime
			end
		end
	end
end

function ove_0_12.CheckBuffWithTimeEndOwner(arg_28_0, arg_28_1)
	if arg_28_0 and arg_28_0.buffManager and arg_28_0.buffManager.count > 0 then
		for iter_28_0 = 0, arg_28_0.buffManager.count - 1 do
			local slot_28_0 = arg_28_0.buffManager:get(iter_28_0)

			if slot_28_0 and slot_28_0.valid and slot_28_0.name == arg_28_1 and (slot_28_0.stacks > 0 or slot_28_0.stacks2 > 0) and slot_28_0.source.ptr == player.ptr and game.time <= slot_28_0.endTime then
				return true
			end
		end
	end
end

local ove_0_17 = {
	100,
	105,
	110,
	115,
	120,
	130,
	140,
	150,
	165,
	180,
	200,
	225,
	255,
	290,
	330,
	380,
	440,
	510
}

function ove_0_12.GetShieldedHealth(arg_29_0, arg_29_1)
	local slot_29_0 = 0

	if arg_29_0 == "AD" then
		slot_29_0 = arg_29_1.physicalShield
	elseif arg_29_0 == "AP" then
		slot_29_0 = arg_29_1.magicalShield
	elseif arg_29_0 == "ALL" then
		slot_29_0 = arg_29_1.allShield
	end

	return arg_29_1.health + slot_29_0
end

function ove_0_12.M_Near_POS(arg_30_0, arg_30_1)
	local slot_30_0 = 0

	for iter_30_0 = 0, objManager.minions.size[TEAM_ENEMY] - 1 do
		local slot_30_1 = objManager.minions[TEAM_ENEMY][iter_30_0]

		if slot_30_1 and not slot_30_1.isDead and slot_30_1.isVisible and slot_30_1.isTargetable and arg_30_1 > arg_30_0:dist(slot_30_1.pos) then
			slot_30_0 = slot_30_0 + 1
		end
	end

	return slot_30_0
end

function ove_0_12.safetyloctime(arg_31_0, arg_31_1, arg_31_2)
	local slot_31_0 = 0

	for iter_31_0 = 0, objManager.enemies_n - 1 do
		local slot_31_1 = objManager.enemies[iter_31_0]

		if slot_31_1 and not slot_31_1.isDead and slot_31_1.isVisible and slot_31_1.isTargetable then
			local slot_31_2 = ove_0_10.core.get_pos_after_time(player, arg_31_2)
			local slot_31_3 = vec3(slot_31_2.x, player.pos.y, slot_31_2.y)

			if arg_31_1 >= player.pos:dist(slot_31_3) then
				slot_31_0 = slot_31_0 + 1
			end
		end
	end

	if slot_31_0 == 0 then
		return 0
	end

	return slot_31_0
end

function ove_0_12.donotbuff(arg_32_0)
	local slot_32_0 = arg_32_0.buff.judicatorintervention
	local slot_32_1 = arg_32_0.buff.kayler
	local slot_32_2 = arg_32_0.buff.undyingrage
	local slot_32_3 = arg_32_0.buff.sionpassivezombie
	local slot_32_4 = arg_32_0.buff.taricr
	local slot_32_5 = arg_32_0.buff.chronoshift
	local slot_32_6 = arg_32_0.buff.kindredr

	if slot_32_0 then
		return true
	end

	if slot_32_1 then
		return true
	end

	if slot_32_2 then
		return true
	end

	if slot_32_3 then
		return true
	end

	if slot_32_4 then
		return true
	end

	if slot_32_5 then
		return true
	end

	if slot_32_6 then
		return true
	end

	return false
end

function ove_0_12.GetTotalAD(arg_33_0)
	local slot_33_0 = arg_33_0 or player

	return (slot_33_0.baseAttackDamage + slot_33_0.flatPhysicalDamageMod) * slot_33_0.percentPhysicalDamageMod
end

function ove_0_12.GetBonusAD(arg_34_0)
	local slot_34_0 = arg_34_0 or player

	return (slot_34_0.baseAttackDamage + slot_34_0.flatPhysicalDamageMod) * slot_34_0.percentPhysicalDamageMod - slot_34_0.baseAttackDamage
end

function ove_0_12.GetTotalAP(arg_35_0)
	local slot_35_0 = arg_35_0 or player

	return slot_35_0.flatMagicDamageMod * slot_35_0.percentMagicDamageMod
end

function ove_0_12.round(arg_36_0, arg_36_1)
	local slot_36_0 = 10^(arg_36_1 or 0)

	return math.floor(arg_36_0 * slot_36_0 + 0.5) / slot_36_0
end

function ove_0_12.PhysicalReduction(arg_37_0, arg_37_1)
	local slot_37_0 = arg_37_1 or player
	local slot_37_1 = (arg_37_0.bonusArmor * slot_37_0.percentBonusArmorPenetration + (arg_37_0.armor - arg_37_0.bonusArmor)) * slot_37_0.percentArmorPenetration
	local slot_37_2 = slot_37_0.physicalLethality * 0.4 + slot_37_0.physicalLethality * 0.6 * (slot_37_0.levelRef / 18)

	return slot_37_1 >= 0 and 100 / (100 + (slot_37_1 - slot_37_2)) or 2 - 100 / (100 - (slot_37_1 - slot_37_2))
end

function ove_0_12.MagicReduction(arg_38_0, arg_38_1)
	local slot_38_0 = arg_38_1 or player
	local slot_38_1 = arg_38_0.spellBlock * slot_38_0.percentMagicPenetration - slot_38_0.flatMagicPenetration

	return slot_38_1 >= 0 and 100 / (100 + slot_38_1) or 2 - 100 / (100 - slot_38_1)
end

function ove_0_12.DamageReduction(arg_39_0, arg_39_1, arg_39_2)
	local slot_39_0

	slot_39_0 = arg_39_2 or player

	local slot_39_1 = 1

	if arg_39_0 == "AD" then
		-- block empty
	end

	if arg_39_0 == "AP" then
		-- block empty
	end

	return slot_39_1
end

function ove_0_12.CalculateAADamage(arg_40_0, arg_40_1)
	local slot_40_0 = arg_40_1 or player

	if arg_40_0 then
		return ove_0_12.GetTotalAD(slot_40_0) * ove_0_12.PhysicalReduction(arg_40_0, slot_40_0)
	end

	return 0
end

function ove_0_12.CalculatePhysicalDamage(arg_41_0, arg_41_1, arg_41_2)
	local slot_41_0 = arg_41_2 or player

	if arg_41_0 then
		return arg_41_1 * ove_0_12.PhysicalReduction(arg_41_0, slot_41_0) * ove_0_12.DamageReduction("AD", arg_41_0, slot_41_0)
	end

	return 0
end

function ove_0_12.CalculateMagicDamage(arg_42_0, arg_42_1, arg_42_2)
	local slot_42_0 = arg_42_2 or player

	if arg_42_0 then
		return arg_42_1 * ove_0_12.MagicReduction(arg_42_0, slot_42_0) * ove_0_12.DamageReduction("AP", arg_42_0, slot_42_0)
	end

	return 0
end

function ove_0_12.GetAARange(arg_43_0)
	return player.attackRange + player.boundingRadius + (arg_43_0 and arg_43_0.boundingRadius or 0)
end

function ove_0_12.GetPredictedPos(arg_44_0, arg_44_1)
	if not ove_0_12.IsValidTarget(arg_44_0) or not arg_44_0.path or not arg_44_1 or not arg_44_0.moveSpeed then
		return arg_44_0
	end

	local slot_44_0 = ove_0_10.core.lerp(arg_44_0.path, network.latency + arg_44_1, arg_44_0.moveSpeed)

	return vec3(slot_44_0.x, player.y, slot_44_0.y)
end

function ove_0_12.GetIgniteDamage(arg_45_0)
	local slot_45_0 = 55 + 25 * player.levelRef

	if arg_45_0 then
		slot_45_0 = slot_45_0 - (ove_0_12.GetShieldedHealth("AD", arg_45_0) - arg_45_0.health)
	end

	return slot_45_0
end

ove_0_12.enum = {}
ove_0_12.enum.slots = {
	w = 1,
	q = 0,
	e = 2,
	r = 3
}
ove_0_12.dashingspells = {
	Aatrox = {
		{
			spellname = "Umbral Dash",
			speed = 800,
			spelltype = "Directional",
			menuslot = "E",
			slot = _E
		}
	},
	Caitlyn = {
		{
			spellname = "90 Caliber Dash",
			speed = 800,
			spelltype = "Directional",
			menuslot = "E",
			slot = _E
		}
	},
	Gragas = {
		{
			spellname = "Body Slam",
			speed = 1600,
			spelltype = "Directional",
			menuslot = "E",
			slot = _E
		}
	},
	Kayn = {
		{
			spellname = "Reaping Slash",
			speed = 2000,
			spelltype = "Directional",
			menuslot = "Q",
			slot = _Q
		}
	},
	Lucian = {
		{
			spellname = "Relentless Pursuit",
			speed = 1350,
			spelltype = "Directional",
			menuslot = "E",
			slot = _E
		}
	},
	Ornn = {
		{
			spellname = "Searing Charge",
			speed = 800,
			spelltype = "Directional",
			menuslot = "E",
			slot = _E
		}
	},
	Renekton = {
		{
			spellname = "Slice and Dice",
			speed = 1300,
			spelltype = "Directional",
			menuslot = "E",
			slot = _E
		}
	},
	Riven = {
		{
			spellname = "Valor",
			speed = 2000,
			spelltype = "Directional",
			menuslot = "E",
			slot = _E
		}
	},
	Sejuani = {
		{
			spellname = "Artic Assault",
			speed = 1000,
			spelltype = "Directional",
			menuslot = "Q",
			slot = _Q
		}
	},
	Shen = {
		{
			spellname = "Shadow Dash",
			speed = 1300,
			spelltype = "Directional",
			menuslot = "E",
			slot = _E
		}
	},
	Sylas = {
		{
			spellname = "Abscond",
			speed = 1450,
			spelltype = "Directional",
			menuslot = "E",
			slot = _E
		}
	},
	Tryndamere = {
		{
			spellname = "Spinning Slash",
			speed = 2000,
			spelltype = "Directional",
			menuslot = "E",
			slot = _E
		}
	},
	Urgot = {
		{
			spellname = "Disdain",
			speed = 2000,
			spelltype = "Directional",
			menuslot = "E",
			slot = _E
		}
	},
	Vayne = {
		{
			spellname = "Tumble",
			speed = 2000,
			spelltype = "Directional",
			menuslot = "Q",
			slot = _Q
		}
	},
	Vi = {
		{
			spellname = "Vault Breaker",
			speed = 2000,
			spelltype = "Directional",
			menuslot = "Q",
			slot = _Q
		}
	},
	Viego = {
		{
			spellname = "Spectral Maw",
			speed = 2000,
			spelltype = "Directional",
			menuslot = "W",
			slot = _W
		}
	},
	Zeri = {
		{
			spellname = "Spark Surge",
			speed = 1250,
			spelltype = "Directional",
			menuslot = "E",
			slot = _E
		}
	},
	Ahri = {
		{
			spellname = "Spirit Rush",
			speed = 1250,
			spelltype = "Location Target",
			menuslot = "R",
			slot = _R
		}
	},
	Corki = {
		{
			spellname = "Valkyrie",
			speed = 1000,
			spelltype = "Location Target",
			menuslot = "W",
			slot = _W
		}
	},
	Ekko = {
		{
			spellname = "Phase Dive",
			speed = 1000,
			spelltype = "Location Target",
			menuslot = "E",
			slot = _E
		}
	},
	Fiora = {
		{
			spellname = "Lunge",
			speed = 1000,
			spelltype = "Location Target",
			menuslot = "Q",
			slot = _Q
		}
	},
	Fizz = {
		{
			spellname = "Urchin Strike",
			speed = 1000,
			spelltype = "Location Target",
			menuslot = "Q",
			slot = _Q
		}
	},
	Galio = {
		{
			spellname = "Justice Punch",
			speed = 2300,
			spelltype = "Location Target",
			menuslot = "E",
			slot = _E
		}
	},
	Gnar = {
		{
			spellname = "Hop!",
			speed = 2300,
			spelltype = "Location Target",
			menuslot = "E",
			slot = _E
		}
	},
	Gwen = {
		{
			spellname = "Skip n Slash",
			speed = 2300,
			spelltype = "Location Target",
			menuslot = "E",
			slot = _E
		}
	},
	Kindred = {
		{
			spellname = "Dance of Arrows",
			speed = 1000,
			spelltype = "Location Target",
			menuslot = "Q",
			slot = _Q
		}
	},
	Khazix = {
		{
			spellname = "Void Assault",
			speed = 1000,
			spelltype = "Location Target",
			menuslot = "E",
			slot = _E
		}
	},
	Lillia = {
		{
			spellname = "Watch Out! Eep!",
			speed = 1000,
			spelltype = "Location Target",
			menuslot = "W",
			slot = _W
		}
	},
	Rakan = {
		{
			spellname = "Grand Entrance",
			speed = 1700,
			spelltype = "Location Target",
			menuslot = "Q",
			slot = _Q
		}
	},
	Alistar = {
		{
			spellname = "Headbutt",
			speed = 1200,
			spelltype = "Location Target",
			menuslot = "E",
			slot = _E
		}
	},
	Diana = {
		{
			spellname = "Lunar Rush",
			menuslot = "E",
			spelltype = "Targeted Dash",
			slot = _E
		}
	},
	Illaoi = {
		{
			spellname = "Harsh Lesson",
			menuslot = "W",
			spelltype = "Targeted Dash",
			slot = _W
		}
	},
	Irelia = {
		{
			spellname = "Bladesurge",
			speed = 1400,
			spelltype = "Targeted Dash",
			menuslot = "Q",
			slot = _Q
		}
	},
	Jax = {
		{
			spellname = "Leap Strike",
			menuslot = "Q",
			spelltype = "Targeted Dash",
			slot = _Q
		}
	},
	Pantheon = {
		{
			spellname = "Shield Vault",
			menuslot = "W",
			spelltype = "Targeted Dash",
			slot = _W
		}
	},
	Poppy = {
		{
			spellname = "Heroic Charge",
			menuslot = "E",
			spelltype = "Targeted Dash",
			slot = _E
		}
	},
	Samira = {
		{
			spellname = "Wild Rush",
			speed = 1600,
			spelltype = "Targeted Dash",
			menuslot = "E",
			slot = _E
		}
	},
	Sylas = {
		{
			spellname = "Kingslayer",
			menuslot = "W",
			spelltype = "Targeted Dash",
			slot = _W
		}
	},
	Talon = {
		{
			spellname = "Noxian Diplomacy",
			speed = 1400,
			spelltype = "Targeted Dash",
			menuslot = "Q",
			slot = _Q
		}
	},
	Wukong = {
		{
			spellname = "Nimbus Strike",
			menuslot = "E",
			spelltype = "Targeted Dash",
			slot = _E
		}
	},
	XinZhao = {
		{
			spellname = "Audacious Charge",
			speed = 2500,
			spelltype = "Targeted Dash",
			menuslot = "E",
			slot = _E
		}
	},
	Yasuo = {
		{
			spellname = "Sweeping Blade",
			speed = 750,
			spelltype = "Targeted Dash",
			menuslot = "E",
			slot = _E
		}
	}
}
ove_0_12.interruptableSpells = {
	anivia = {
		{
			spellname = "glacialstorm",
			menuslot = "R",
			slot = 3,
			channelduration = 6
		}
	},
	caitlyn = {
		{
			spellname = "caitlynaceinthehole",
			menuslot = "R",
			slot = 3,
			channelduration = 1
		}
	},
	ezreal = {
		{
			spellname = "ezrealtrueshotbarrage",
			menuslot = "R",
			slot = 3,
			channelduration = 1
		}
	},
	fiddlesticks = {
		{
			spellname = "drain",
			menuslot = "W",
			slot = 1,
			channelduration = 5
		},
		{
			spellname = "crowstorm",
			menuslot = "R",
			slot = 3,
			channelduration = 1.5
		}
	},
	gragas = {
		{
			spellname = "gragasw",
			menuslot = "W",
			slot = 1,
			channelduration = 0.75
		}
	},
	janna = {
		{
			spellname = "reapthewhirlwind",
			menuslot = "R",
			slot = 3,
			channelduration = 3
		}
	},
	karthus = {
		{
			spellname = "karthusfallenone",
			menuslot = "R",
			slot = 3,
			channelduration = 3
		}
	},
	katarina = {
		{
			spellname = "katarinar",
			menuslot = "R",
			slot = 3,
			channelduration = 2.5
		}
	},
	lucian = {
		{
			spellname = "lucianr",
			menuslot = "R",
			slot = 3,
			channelduration = 2
		}
	},
	lux = {
		{
			spellname = "luxmalicecannon",
			menuslot = "R",
			slot = 3,
			channelduration = 0.5
		}
	},
	malzahar = {
		{
			spellname = "malzaharr",
			menuslot = "R",
			slot = 3,
			channelduration = 2.5
		}
	},
	masteryi = {
		{
			spellname = "meditate",
			menuslot = "W",
			slot = 1,
			channelduration = 4
		}
	},
	missfortune = {
		{
			spellname = "missfortunebullettime",
			menuslot = "R",
			slot = 3,
			channelduration = 3
		}
	},
	nunu = {
		{
			spellname = "absolutezero",
			menuslot = "R",
			slot = 3,
			channelduration = 3
		}
	},
	pantheon = {
		{
			spellname = "pantheonrjump",
			menuslot = "R",
			slot = 3,
			channelduration = 2
		}
	},
	shen = {
		{
			spellname = "shenr",
			menuslot = "R",
			slot = 3,
			channelduration = 3
		}
	},
	twistedfate = {
		{
			spellname = "gate",
			menuslot = "R",
			slot = 3,
			channelduration = 1.5
		}
	},
	varus = {
		{
			spellname = "varusq",
			menuslot = "Q",
			slot = 0,
			channelduration = 4
		}
	},
	warwick = {
		{
			spellname = "warwickr",
			menuslot = "R",
			slot = 3,
			channelduration = 1.5
		}
	},
	xerath = {
		{
			spellname = "xerathlocusofpower2",
			menuslot = "R",
			slot = 3,
			channelduration = 3
		}
	}
}
ove_0_12.blinks = {
	Katarina = {
		range = 825,
		radius = 80,
		charName = "Katerina",
		type = "linear",
		delay = 0.25,
		speed = 1800,
		displayName = "Infernal Chains",
		collision = true,
		slot = _E
	}
}
ove_0_12.CCSpells = {
	VayneCondemn = {
		speed = 2200,
		slot = "E",
		charName = "Vayne",
		displayName = "Condemn",
		delay = 0.25,
		isMissile = false
	},
	CassiopeiaR = {
		speed = 2200,
		slot = "R",
		charName = "Cassiopeia",
		displayName = "Condemn",
		delay = 0.25,
		isMissile = false
	},
	DariusAxeGrabCone = {
		charName = "Darius",
		range = 535,
		aoe = true,
		type = "conic",
		delay = 0.25,
		slotName = "E",
		collision = false,
		cc = true,
		slot = 2,
		hitbox = false,
		displayName = "Grab",
		angle = 50,
		speed = math.huge
	},
	GarenQ = {
		speed = 2200,
		slot = "Q",
		charName = "Garen",
		displayName = "Demacian Silence",
		delay = 0.25,
		isMissile = false
	},
	AatroxW = {
		range = 825,
		radius = 80,
		charName = "Aatrox",
		type = "linear",
		delay = 0.25,
		speed = 1800,
		displayName = "Infernal Chains",
		collision = true,
		slot = _W
	},
	AhriSeduce = {
		range = 975,
		radius = 60,
		charName = "Ahri",
		type = "linear",
		delay = 0.25,
		speed = 1500,
		displayName = "Seduce",
		collision = true,
		slot = _E
	},
	AkaliR = {
		range = 525,
		radius = 65,
		charName = "Akali",
		type = "linear",
		delay = 0,
		speed = 1800,
		displayName = "Perfect Execution [First]",
		collision = false,
		slot = _R
	},
	AkaliE = {
		range = 825,
		radius = 70,
		charName = "Akali",
		type = "linear",
		delay = 0.25,
		speed = 1800,
		displayName = "Shuriken Flip",
		collision = true,
		slot = _E
	},
	Pulverize = {
		range = 0,
		radius = 365,
		charName = "Alistar",
		type = "circular",
		delay = 0.25,
		displayName = "Pulverize",
		collision = false,
		slot = _Q,
		speed = MathHuge
	},
	BandageToss = {
		range = 1100,
		radius = 80,
		charName = "Amumu",
		type = "linear",
		delay = 0.25,
		speed = 2000,
		displayName = "Bandage Toss",
		collision = true,
		slot = _Q
	},
	CurseoftheSadMummy = {
		range = 0,
		radius = 550,
		charName = "Amumu",
		type = "circular",
		delay = 0.25,
		displayName = "Curse of the Sad Mummy",
		collision = false,
		slot = _R,
		speed = MathHuge
	},
	FlashFrostSpell = {
		charName = "Anivia",
		range = 1100,
		missileName = "FlashFrostSpell",
		type = "linear",
		delay = 0.25,
		radius = 110,
		speed = 850,
		displayName = "Flash Frost",
		collision = false,
		slot = _Q
	},
	EnchantedCrystalArrow = {
		range = 25000,
		radius = 130,
		charName = "Ashe",
		type = "linear",
		delay = 0.25,
		speed = 1600,
		displayName = "Enchanted Crystal Arrow",
		collision = false,
		slot = _R
	},
	AurelionSolQ = {
		range = 25000,
		radius = 110,
		charName = "AurelionSol",
		type = "linear",
		delay = 0,
		speed = 850,
		displayName = "Starsurge",
		collision = false,
		slot = _Q
	},
	AzirR = {
		range = 500,
		radius = 250,
		charName = "Azir",
		type = "linear",
		delay = 0.3,
		speed = 1400,
		displayName = "Emperor's Divide",
		collision = false,
		slot = _R
	},
	ApheliosR = {
		range = 1600,
		radius = 125,
		charName = "Aphelios",
		type = "linear",
		delay = 0.5,
		speed = 2050,
		displayName = "Moonlight Vigil",
		collision = false,
		slot = _R
	},
	BardQ = {
		range = 950,
		radius = 60,
		charName = "Bard",
		type = "linear",
		delay = 0.25,
		speed = 1500,
		displayName = "Cosmic Binding",
		collision = true,
		slot = _Q
	},
	BardR = {
		range = 3400,
		radius = 350,
		charName = "Bard",
		type = "circular",
		delay = 0.5,
		speed = 2100,
		displayName = "Tempered Fate",
		collision = false,
		slot = _R
	},
	BrandQ = {
		range = 1050,
		radius = 60,
		charName = "Brand",
		type = "linear",
		delay = 0.25,
		speed = 1600,
		displayName = "Sear",
		collision = true,
		slot = _Q
	},
	RocketGrab = {
		range = 1150,
		radius = 140,
		charName = "Blitzcrank",
		type = "linear",
		delay = 0.25,
		speed = 1800,
		displayName = "Rocket Grab",
		collision = true,
		slot = _Q
	},
	BraumQ = {
		range = 1000,
		radius = 70,
		charName = "Braum",
		type = "linear",
		delay = 0.25,
		speed = 1700,
		displayName = "Winter's Bite",
		collision = true,
		slot = _Q
	},
	BraumR = {
		range = 1250,
		radius = 115,
		charName = "Braum",
		type = "linear",
		delay = 0.5,
		speed = 1400,
		displayName = "Glacial Fissure",
		collision = false,
		slot = _R
	},
	CaitlynYordleTrap = {
		range = 800,
		radius = 75,
		charName = "Caitlyn",
		type = "circular",
		delay = 0.25,
		displayName = "Yordle Trap",
		collision = false,
		slot = _W,
		speed = MathHuge
	},
	CaitlynEntrapment = {
		range = 750,
		radius = 70,
		charName = "Caitlyn",
		type = "linear",
		delay = 0.15,
		speed = 1600,
		displayName = "Entrapment",
		collision = true,
		slot = _E
	},
	CassiopeiaW = {
		range = 800,
		radius = 160,
		charName = "Cassiopeia",
		type = "circular",
		delay = 0.75,
		speed = 2500,
		displayName = "Miasma",
		collision = false,
		slot = _W
	},
	Rupture = {
		range = 950,
		radius = 250,
		charName = "Chogath",
		type = "circular",
		delay = 1.2,
		displayName = "Rupture",
		collision = false,
		slot = _Q,
		speed = MathHuge
	},
	InfectedCleaverMissile = {
		range = 975,
		radius = 60,
		charName = "DrMundo",
		type = "linear",
		delay = 0.25,
		speed = 2000,
		displayName = "Infected Cleaver",
		collision = true,
		slot = _Q
	},
	DravenDoubleShot = {
		range = 1050,
		radius = 130,
		charName = "Draven",
		type = "linear",
		delay = 0.25,
		speed = 1600,
		displayName = "Double Shot",
		collision = false,
		slot = _E
	},
	DravenRCast = {
		range = 12500,
		radius = 160,
		charName = "Draven",
		type = "linear",
		delay = 0.25,
		speed = 2000,
		displayName = "Whirling Death",
		collision = false,
		slot = _R
	},
	DianaQ = {
		range = 900,
		radius = 185,
		charName = "Diana",
		type = "circular",
		delay = 0.25,
		speed = 1900,
		displayName = "Crescent Strike",
		collision = true,
		slot = _Q
	},
	EkkoQ = {
		range = 1175,
		radius = 60,
		charName = "Ekko",
		type = "linear",
		delay = 0.25,
		speed = 1650,
		displayName = "Timewinder",
		collision = false,
		slot = _Q
	},
	EkkoW = {
		range = 1600,
		radius = 400,
		charName = "Ekko",
		type = "circular",
		delay = 3.35,
		displayName = "Parallel Convergence",
		collision = false,
		slot = _W,
		speed = MathHuge
	},
	EliseHumanE = {
		range = 1075,
		radius = 55,
		charName = "Elise",
		type = "linear",
		delay = 0.25,
		speed = 1600,
		displayName = "Cocoon",
		collision = true,
		slot = _E
	},
	EzrealR = {
		range = 12500,
		radius = 160,
		charName = "Ezreal",
		type = "linear",
		delay = 1,
		speed = 2000,
		displayName = "Trueshot Barrage",
		collision = true,
		slot = _R
	},
	FizzR = {
		range = 1300,
		radius = 150,
		charName = "Fizz",
		type = "linear",
		delay = 0.25,
		speed = 1300,
		displayName = "Chum the Waters",
		collision = false,
		slot = _R
	},
	GalioE = {
		range = 650,
		radius = 160,
		charName = "Galio",
		type = "linear",
		delay = 0.4,
		speed = 2300,
		displayName = "Justice Punch",
		collision = false,
		slot = _E
	},
	GnarQMissile = {
		range = 1125,
		radius = 55,
		charName = "Gnar",
		type = "linear",
		delay = 0.25,
		speed = 2500,
		displayName = "Boomerang Throw",
		collision = false,
		slot = _Q
	},
	GnarBigQMissile = {
		range = 1125,
		radius = 90,
		charName = "Gnar",
		type = "linear",
		delay = 0.5,
		speed = 2100,
		displayName = "Boulder Toss",
		collision = true,
		slot = _Q
	},
	GnarBigW = {
		range = 575,
		radius = 100,
		charName = "Gnar",
		type = "linear",
		delay = 0.6,
		displayName = "Wallop",
		collision = false,
		slot = _W,
		speed = MathHuge
	},
	GnarR = {
		range = 0,
		radius = 475,
		charName = "Gnar",
		type = "circular",
		delay = 0.25,
		displayName = "GNAR!",
		collision = false,
		slot = _R,
		speed = MathHuge
	},
	GragasQ = {
		range = 850,
		radius = 275,
		charName = "Gragas",
		type = "circular",
		delay = 0.25,
		speed = 1000,
		displayName = "Barrel Roll",
		collision = false,
		slot = _Q
	},
	GragasR = {
		range = 1000,
		radius = 400,
		charName = "Gragas",
		type = "circular",
		delay = 0.25,
		speed = 1800,
		displayName = "Explosive Cask",
		collision = false,
		slot = _R
	},
	GravesSmokeGrenade = {
		range = 950,
		radius = 250,
		charName = "Graves",
		type = "circular",
		delay = 0.15,
		speed = 1500,
		displayName = "Smoke Grenade",
		collision = false,
		slot = _W
	},
	HeimerdingerE = {
		range = 970,
		radius = 250,
		charName = "Heimerdinger",
		type = "circular",
		delay = 0.25,
		speed = 1200,
		displayName = "CH-2 Electron Storm Grenade",
		collision = false,
		slot = _E
	},
	HeimerdingerEUlt = {
		range = 970,
		radius = 250,
		charName = "Heimerdinger",
		type = "circular",
		delay = 0.25,
		speed = 1200,
		displayName = "CH-2 Electron Storm Grenade",
		collision = false,
		slot = _E
	},
	IreliaW2 = {
		range = 775,
		radius = 120,
		charName = "Irelia",
		type = "linear",
		delay = 0.25,
		displayName = "Defiant Dance",
		collision = false,
		slot = _W,
		speed = MathHuge
	},
	IreliaR = {
		range = 950,
		radius = 160,
		charName = "Irelia",
		type = "linear",
		delay = 0.4,
		speed = 2000,
		displayName = "Vanguard's Edge",
		collision = false,
		slot = _R
	},
	IvernQ = {
		range = 1075,
		radius = 80,
		charName = "Ivern",
		type = "linear",
		delay = 0.25,
		speed = 1300,
		displayName = "Rootcaller",
		collision = true,
		slot = _Q
	},
	IllaoiE = {
		range = 900,
		radius = 50,
		charName = "Illaoi",
		type = "linear",
		delay = 0.25,
		speed = 1900,
		displayName = "Test of Spirit",
		collision = true,
		slot = _E
	},
	IvernQ = {
		range = 1075,
		radius = 80,
		charName = "Ivern",
		type = "linear",
		delay = 0.25,
		speed = 1300,
		displayName = "Rootcaller",
		collision = true,
		slot = _Q
	},
	HowlingGaleSpell = {
		range = 1750,
		radius = 100,
		charName = "Janna",
		type = "linear",
		delay = 0,
		speed = 667,
		displayName = "Howling Gale",
		collision = false,
		slot = _Q
	},
	JarvanIVDragonStrike = {
		range = 770,
		radius = 70,
		charName = "JarvanIV",
		type = "linear",
		delay = 0.4,
		displayName = "Dragon Strike",
		collision = false,
		slot = _Q,
		speed = MathHuge
	},
	JhinW = {
		range = 2550,
		radius = 40,
		charName = "Jhin",
		type = "linear",
		delay = 0.75,
		speed = 5000,
		displayName = "Deadly Flourish",
		collision = false,
		slot = _W
	},
	JhinRShot = {
		range = 3500,
		radius = 80,
		charName = "Jhin",
		type = "linear",
		delay = 0.25,
		speed = 5000,
		displayName = "Curtain Call",
		collision = false,
		slot = _R
	},
	JhinE = {
		range = 750,
		radius = 130,
		charName = "Jhin",
		type = "circular",
		delay = 0.25,
		speed = 1600,
		displayName = "Captive Audience",
		collision = false,
		slot = _E
	},
	JinxWMissile = {
		range = 1450,
		radius = 60,
		charName = "Jinx",
		type = "linear",
		delay = 0.6,
		speed = 3300,
		displayName = "Zap!",
		collision = true,
		slot = _W
	},
	KarmaQ = {
		range = 950,
		radius = 60,
		charName = "Karma",
		type = "linear",
		delay = 0.25,
		speed = 1700,
		displayName = "Inner Flame",
		collision = true,
		slot = _Q
	},
	KarmaQMantra = {
		charName = "Karma",
		range = 950,
		origin = "linear",
		type = "linear",
		delay = 0.25,
		radius = 80,
		speed = 1700,
		displayName = "Inner Flame [Mantra]",
		collision = true,
		slot = _Q
	},
	KayleQ = {
		range = 850,
		radius = 60,
		charName = "Kayle",
		type = "linear",
		delay = 0.5,
		speed = 2000,
		displayName = "Radiant Blast",
		collision = false,
		slot = _Q
	},
	KaynW = {
		range = 700,
		radius = 90,
		charName = "Kayn",
		type = "linear",
		delay = 0.55,
		displayName = "Blade's Reach",
		collision = false,
		slot = _W,
		speed = MathHuge
	},
	KhazixWLong = {
		range = 1000,
		radius = 70,
		charName = "Khazix",
		type = "threeway",
		delay = 0.25,
		collision = true,
		speed = 1700,
		displayName = "Void Spike [Threeway]",
		angle = 23,
		slot = _W
	},
	KledQ = {
		range = 800,
		radius = 45,
		charName = "Kled",
		type = "linear",
		delay = 0.25,
		speed = 1600,
		displayName = "Beartrap on a Rope",
		collision = true,
		slot = _Q
	},
	KogMawVoidOozeMissile = {
		range = 1360,
		radius = 120,
		charName = "KogMaw",
		type = "linear",
		delay = 0.25,
		speed = 1400,
		displayName = "Void Ooze",
		collision = false,
		slot = _E
	},
	BlindMonkQOne = {
		range = 1100,
		radius = 60,
		charName = "Leesin",
		type = "linear",
		delay = 0.25,
		speed = 1800,
		displayName = "Sonic Wave",
		collision = true,
		slot = _Q
	},
	LeblancE = {
		range = 925,
		radius = 55,
		charName = "Leblanc",
		type = "linear",
		delay = 0.25,
		speed = 1750,
		displayName = "Ethereal Chains [Standard]",
		collision = true,
		slot = _E
	},
	LeblancRE = {
		range = 925,
		radius = 55,
		charName = "Leblanc",
		type = "linear",
		delay = 0.25,
		speed = 1750,
		displayName = "Ethereal Chains [Ultimate]",
		collision = true,
		slot = _E
	},
	LeonaZenithBlade = {
		range = 875,
		radius = 70,
		charName = "Leona",
		type = "linear",
		delay = 0.25,
		speed = 2000,
		displayName = "Zenith Blade",
		collision = false,
		slot = _E
	},
	LeonaSolarFlare = {
		range = 1200,
		radius = 300,
		charName = "Leona",
		type = "circular",
		delay = 0.85,
		displayName = "Solar Flare",
		collision = false,
		slot = _R,
		speed = MathHuge
	},
	LissandraQMissile = {
		range = 750,
		radius = 75,
		charName = "Lissandra",
		type = "linear",
		delay = 0.25,
		speed = 2200,
		displayName = "Ice Shard",
		collision = false,
		slot = _Q
	},
	LuluQ = {
		range = 925,
		radius = 60,
		charName = "Lulu",
		type = "linear",
		delay = 0.25,
		speed = 1450,
		displayName = "Glitterlance",
		collision = false,
		slot = _Q
	},
	LuluW = {
		range = 650,
		radius = 60,
		charName = "Lulu",
		type = "target",
		delay = 0.25,
		speed = 2250,
		displayName = "Whimsy",
		collision = false,
		slot = _W
	},
	LuxLightBinding = {
		range = 1175,
		radius = 50,
		charName = "Lux",
		type = "linear",
		delay = 0.25,
		speed = 1200,
		displayName = "Light Binding",
		collision = false,
		slot = _Q
	},
	LuxLightStrikeKugel = {
		range = 1100,
		radius = 300,
		charName = "Lux",
		type = "circular",
		delay = 0.25,
		speed = 1200,
		displayName = "Light Strike Kugel",
		collision = true,
		slot = _E
	},
	Landslide = {
		range = 0,
		radius = 400,
		charName = "Malphite",
		type = "circular",
		delay = 0.242,
		displayName = "Ground Slam",
		collision = false,
		slot = _E,
		speed = MathHuge
	},
	MalzaharQ = {
		range = 900,
		radius = 400,
		charName = "Malzahar",
		type = "rectangular",
		delay = 0.5,
		radius2 = 100,
		speed = 1600,
		displayName = "Call of the Void",
		collision = false,
		slot = _Q
	},
	MaokaiQ = {
		range = 600,
		radius = 110,
		charName = "Maokai",
		type = "linear",
		delay = 0.375,
		speed = 1600,
		displayName = "Bramble Smash",
		collision = false,
		slot = _Q
	},
	MorganaQ = {
		range = 1250,
		radius = 70,
		charName = "Morgana",
		type = "linear",
		delay = 0.25,
		speed = 1200,
		displayName = "Dark Binding",
		collision = true,
		slot = _Q
	},
	NamiQ = {
		range = 875,
		radius = 180,
		charName = "Nami",
		type = "circular",
		delay = 1,
		displayName = "Aqua Prison",
		collision = false,
		slot = _Q,
		speed = MathHuge
	},
	NamiRMissile = {
		range = 2750,
		radius = 250,
		charName = "Nami",
		type = "linear",
		delay = 0.5,
		speed = 850,
		displayName = "Tidal Wave",
		collision = false,
		slot = _R
	},
	NautilusAnchorDragMissile = {
		range = 925,
		radius = 90,
		charName = "Nautilus",
		type = "linear",
		delay = 0.25,
		speed = 2000,
		displayName = "Dredge Line",
		collision = true,
		slot = _Q
	},
	NeekoQ = {
		range = 800,
		radius = 200,
		charName = "Neeko",
		type = "circular",
		delay = 0.25,
		speed = 1500,
		displayName = "Blooming Burst",
		collision = false,
		slot = _Q
	},
	NeekoE = {
		range = 1000,
		radius = 65,
		charName = "Neeko",
		type = "linear",
		delay = 0.25,
		speed = 1400,
		displayName = "Tangle-Barbs",
		collision = false,
		slot = _E
	},
	NunuR = {
		range = 0,
		radius = 650,
		charName = "Nunu",
		type = "circular",
		delay = 3,
		displayName = "Absolute Zero",
		collision = false,
		slot = _R,
		speed = MathHuge
	},
	OlafAxeThrowCast = {
		range = 1000,
		radius = 90,
		charName = "Olaf",
		type = "linear",
		delay = 0.25,
		speed = 1600,
		displayName = "Undertow",
		collision = false,
		slot = _Q
	},
	OrnnQ = {
		range = 800,
		radius = 65,
		charName = "Ornn",
		type = "linear",
		delay = 0.3,
		speed = 1800,
		displayName = "Volcanic Rupture",
		collision = false,
		slot = _Q
	},
	OrnnE = {
		range = 800,
		radius = 150,
		charName = "Ornn",
		type = "linear",
		delay = 0.35,
		speed = 1600,
		displayName = "Searing Charge",
		collision = false,
		slot = _E
	},
	OrnnRCharge = {
		range = 2500,
		radius = 200,
		charName = "Ornn",
		type = "linear",
		delay = 0.5,
		speed = 1650,
		displayName = "Call of the Forge God",
		collision = false,
		slot = _R
	},
	PoppyQSpell = {
		range = 430,
		radius = 100,
		charName = "Poppy",
		type = "linear",
		delay = 0.332,
		displayName = "Hammer Shock",
		collision = false,
		slot = _Q,
		speed = MathHuge
	},
	PoppyRSpell = {
		range = 1200,
		radius = 100,
		charName = "Poppy",
		type = "linear",
		delay = 0.33,
		speed = 2000,
		displayName = "Keeper's Verdict",
		collision = false,
		slot = _R
	},
	PykeQMelee = {
		range = 400,
		radius = 70,
		charName = "Pyke",
		type = "linear",
		delay = 0.25,
		displayName = "Bone Skewer [Melee]",
		collision = false,
		slot = _Q,
		speed = MathHuge
	},
	PykeQRange = {
		range = 1100,
		radius = 70,
		charName = "Pyke",
		type = "linear",
		delay = 0.2,
		speed = 2000,
		displayName = "Bone Skewer [Range]",
		collision = true,
		slot = _Q
	},
	PykeE = {
		range = 25000,
		radius = 110,
		charName = "Pyke",
		type = "linear",
		delay = 0,
		speed = 3000,
		displayName = "Phantom Undertow",
		collision = false,
		slot = _E
	},
	QiyanaR = {
		range = 950,
		radius = 190,
		charName = "Qiyana",
		type = "linear",
		delay = 0.25,
		speed = 2000,
		displayName = "Supreme Display of Talent",
		collision = false,
		slot = _R
	},
	RakanW = {
		range = 650,
		radius = 265,
		charName = "Rakan",
		type = "circular",
		delay = 0.7,
		displayName = "Grand Entrance",
		collision = false,
		slot = _W,
		speed = MathHuge
	},
	RengarE = {
		range = 1000,
		radius = 70,
		charName = "Rengar",
		type = "linear",
		delay = 0.25,
		speed = 1500,
		displayName = "Bola Strike",
		collision = true,
		slot = _E
	},
	RumbleGrenade = {
		range = 850,
		radius = 60,
		charName = "Rumble",
		type = "linear",
		delay = 0.25,
		speed = 2000,
		displayName = "Electro Harpoon",
		collision = true,
		slot = _E
	},
	SejuaniR = {
		range = 1300,
		radius = 120,
		charName = "Sejuani",
		type = "linear",
		delay = 0.25,
		speed = 1600,
		displayName = "Glacial Prison",
		collision = false,
		slot = _R
	},
	ShyvanaTransformLeap = {
		range = 850,
		radius = 150,
		charName = "Shyvana",
		type = "linear",
		delay = 0.25,
		speed = 700,
		displayName = "Transform Leap",
		collision = false,
		slot = _R
	},
	SionQ = {
		charName = "Sion",
		range = 750,
		origin = "",
		type = "linear",
		delay = 2,
		radius = 150,
		displayName = "Decimating Smash",
		collision = false,
		slot = _Q,
		speed = MathHuge
	},
	SionE = {
		range = 800,
		radius = 80,
		charName = "Sion",
		type = "linear",
		delay = 0.25,
		speed = 1800,
		displayName = "Roar of the Slayer",
		collision = false,
		slot = _E
	},
	SkarnerFractureMissile = {
		range = 1000,
		radius = 70,
		charName = "Skarner",
		type = "linear",
		delay = 0.25,
		speed = 1500,
		displayName = "Fracture",
		collision = false,
		slot = _E
	},
	SonaR = {
		range = 1000,
		radius = 140,
		charName = "Sona",
		type = "linear",
		delay = 0.25,
		speed = 2400,
		displayName = "Crescendo",
		collision = false,
		slot = _R
	},
	SorakaQ = {
		range = 810,
		radius = 235,
		charName = "Soraka",
		type = "circular",
		delay = 0.25,
		speed = 1150,
		displayName = "Starcall",
		collision = false,
		slot = _Q
	},
	SwainW = {
		range = 3500,
		radius = 300,
		charName = "Swain",
		type = "circular",
		delay = 1.5,
		displayName = "Vision of Empire",
		collision = false,
		slot = _W,
		speed = MathHuge
	},
	SwainE = {
		range = 850,
		radius = 85,
		charName = "Swain",
		type = "linear",
		delay = 0.25,
		speed = 1800,
		displayName = "Nevermove",
		collision = false,
		slot = _E
	},
	SylasE2 = {
		range = 850,
		radius = 60,
		charName = "Sylas",
		type = "linear",
		delay = 0.25,
		speed = 1600,
		displayName = "Abduct",
		collision = true,
		slot = _E
	},
	TahmKenchQ = {
		range = 800,
		radius = 70,
		charName = "TahmKench",
		type = "linear",
		delay = 0.25,
		speed = 2800,
		displayName = "Tongue Lash",
		collision = true,
		slot = _Q
	},
	TaliyahWVC = {
		range = 900,
		radius = 150,
		charName = "Taliyah",
		type = "circular",
		delay = 0.85,
		displayName = "Seismic Shove",
		collision = false,
		slot = _W,
		speed = MathHuge
	},
	TaliyahR = {
		range = 3000,
		radius = 120,
		charName = "Taliyah",
		type = "linear",
		delay = 1,
		speed = 1700,
		displayName = "Weaver's Wall",
		collision = false,
		slot = _R
	},
	ThreshE = {
		range = 500,
		radius = 110,
		charName = "Thresh",
		type = "linear",
		delay = 0.389,
		displayName = "Flay",
		collision = true,
		slot = _E,
		speed = MathHuge
	},
	TristanaW = {
		range = 900,
		radius = 300,
		charName = "Tristana",
		type = "circular",
		delay = 0.25,
		speed = 1100,
		displayName = "Rocket Jump",
		collision = false,
		slot = _W
	},
	UrgotQ = {
		range = 800,
		radius = 180,
		charName = "Urgot",
		type = "circular",
		delay = 0.6,
		displayName = "Corrosive Charge",
		collision = false,
		slot = _Q,
		speed = MathHuge
	},
	UrgotE = {
		range = 475,
		radius = 100,
		charName = "Urgot",
		type = "linear",
		delay = 0.45,
		speed = 1540,
		displayName = "Disdain",
		collision = false,
		slot = _E
	},
	UrgotR = {
		range = 1600,
		radius = 80,
		charName = "Urgot",
		type = "linear",
		delay = 0.4,
		speed = 3200,
		displayName = "Fear Beyond Death",
		collision = false,
		slot = _R
	},
	VarusE = {
		range = 925,
		radius = 260,
		charName = "Varus",
		type = "linear",
		delay = 0.242,
		speed = 1500,
		displayName = "Hail of Arrows",
		collision = false,
		slot = _E
	},
	VarusR = {
		range = 1200,
		radius = 120,
		charName = "Varus",
		type = "linear",
		delay = 0.25,
		speed = 1950,
		displayName = "Chain of Corruption",
		collision = false,
		slot = _R
	},
	VelkozQ = {
		range = 1050,
		radius = 50,
		charName = "Velkoz",
		type = "linear",
		delay = 0.25,
		speed = 1300,
		displayName = "Plasma Fission",
		collision = true,
		slot = _Q
	},
	VelkozE = {
		range = 800,
		radius = 185,
		charName = "Velkoz",
		type = "circular",
		delay = 0.8,
		displayName = "Tectonic Disruption",
		collision = false,
		slot = _E,
		speed = MathHuge
	},
	ViktorGravitonField = {
		range = 800,
		radius = 270,
		charName = "Viktor",
		type = "circular",
		delay = 1.75,
		displayName = "Graviton Field",
		collision = false,
		slot = _W,
		speed = MathHuge
	},
	WarwickR = {
		range = 3000,
		radius = 55,
		charName = "Warwick",
		type = "linear",
		delay = 0.1,
		speed = 1800,
		displayName = "Infinite Duress",
		collision = false,
		slot = _R
	},
	XerathArcaneBarrage2 = {
		range = 1000,
		radius = 235,
		charName = "Xerath",
		type = "circular",
		delay = 0.75,
		displayName = "Arcane Barrage",
		collision = false,
		slot = _W,
		speed = MathHuge
	},
	XerathMageSpear = {
		range = 1050,
		radius = 60,
		charName = "Xerath",
		type = "linear",
		delay = 0.2,
		speed = 1400,
		displayName = "Mage Spear",
		collision = true,
		slot = _E
	},
	XinZhaoW = {
		range = 900,
		radius = 40,
		charName = "XinZhao",
		type = "linear",
		delay = 0.5,
		speed = 5000,
		displayName = "Wind Becomes Lightning",
		collision = false,
		slot = _W
	},
	ZacQ = {
		range = 800,
		radius = 120,
		charName = "Zac",
		type = "linear",
		delay = 0.33,
		speed = 2800,
		displayName = "Stretching Strikes",
		collision = false,
		slot = _Q
	},
	ZiggsW = {
		range = 1000,
		radius = 240,
		charName = "Ziggs",
		type = "circular",
		delay = 0.25,
		speed = 1750,
		displayName = "Satchel Charge",
		collision = false,
		slot = _W
	},
	ZiggsE = {
		range = 900,
		radius = 250,
		charName = "Ziggs",
		type = "circular",
		delay = 0.25,
		speed = 1800,
		displayName = "Hexplosive Minefield",
		collision = false,
		slot = _E
	},
	ZileanQ = {
		range = 900,
		radius = 150,
		charName = "Zilean",
		type = "circular",
		delay = 0.8,
		displayName = "Time Bomb",
		collision = false,
		slot = _Q,
		speed = MathHuge
	},
	ZoeE = {
		range = 800,
		radius = 50,
		charName = "Zoe",
		type = "linear",
		delay = 0.3,
		speed = 1700,
		displayName = "Sleepy Trouble Bubble",
		collision = true,
		slot = _E
	},
	ZyraE = {
		range = 1100,
		radius = 70,
		charName = "Zyra",
		type = "linear",
		delay = 0.25,
		speed = 1150,
		displayName = "Grasping Roots",
		collision = false,
		slot = _E
	},
	ZyraR = {
		range = 700,
		radius = 500,
		charName = "Zyra",
		type = "circular",
		delay = 2,
		displayName = "Stranglethorns",
		collision = false,
		slot = _R,
		speed = MathHuge
	},
	BrandConflagration = {
		displayName = "Conflagration",
		range = 625,
		type = "targeted",
		charName = "Brand",
		cc = true,
		slot = _R
	},
	JarvanIVCataclysm = {
		displayName = "Cataclysm",
		range = 650,
		type = "targeted",
		charName = "JarvanIV",
		slot = _R
	},
	JayceThunderingBlow = {
		displayName = "Thundering Blow",
		range = 240,
		type = "targeted",
		charName = "Jayce",
		slot = _E
	},
	BlindMonkRKick = {
		displayName = "Dragon's Rage",
		range = 375,
		type = "targeted",
		charName = "LeeSin",
		slot = _R
	},
	LissandraR = {
		displayName = "Frozen Tomb",
		range = 550,
		type = "targeted",
		charName = "Lissandra",
		slot = _R
	},
	SeismicShard = {
		displayName = "Seismic Shard",
		range = 625,
		type = "targeted",
		charName = "Malphite",
		cc = true,
		slot = _Q
	},
	AlZaharNetherGrasp = {
		displayName = "Nether Grasp",
		range = 700,
		type = "targeted",
		charName = "Malzahar",
		slot = _R
	},
	MaokaiW = {
		displayName = "Twisted Advance",
		range = 525,
		type = "targeted",
		charName = "Maokai",
		slot = _W
	},
	NautilusR = {
		displayName = "Depth Charge",
		range = 825,
		type = "targeted",
		charName = "Nautilus",
		slot = _R
	},
	PoppyE = {
		displayName = "Heroic Charge",
		range = 475,
		type = "targeted",
		charName = "Poppy",
		slot = _E
	},
	RyzeW = {
		displayName = "Rune Prison",
		range = 615,
		type = "targeted",
		charName = "Ryze",
		slot = _W
	},
	Fling = {
		displayName = "Fling",
		range = 125,
		type = "targeted",
		charName = "Singed",
		slot = _E
	},
	SkarnerImpale = {
		displayName = "Impale",
		range = 350,
		type = "targeted",
		charName = "Skarner",
		slot = _R
	},
	TahmKenchW = {
		displayName = "Devour",
		range = 250,
		type = "targeted",
		charName = "TahmKench",
		slot = _W
	},
	TristanaR = {
		displayName = "Buster Shot",
		range = 669,
		type = "targeted",
		charName = "Tristana",
		slot = _R
	},
	TristanaE = {
		displayName = "Bomb",
		range = 1000,
		type = "targeted",
		charName = "Tristana",
		slot = _E
	}
}
ove_0_12.Spells = {
	SkillShots = {
		AatroxQ = {
			charName = "Aatrox",
			radius = 200,
			range = 650,
			type = "linear",
			delay = 0.6,
			slotName = "Q",
			aoe = true,
			cc = true,
			slot = 0,
			hitbox = true,
			collision = false,
			speed = math.huge
		},
		AatroxQ2 = {
			charName = "Aatrox",
			radius = 300,
			range = 525,
			type = "linear",
			delay = 0.6,
			slotName = "Q",
			cc = true,
			aoe = true,
			slot = 0,
			hitbox = true,
			collision = false,
			speeds = math.huge
		},
		AatroxQ3 = {
			charName = "Aatrox",
			radius = 300,
			range = 200,
			type = "circular",
			delay = 0.6,
			slotName = "Q",
			cc = true,
			aoe = true,
			slot = 0,
			hitbox = true,
			collision = false,
			speeds = math.huge
		},
		AatroxW = {
			charName = "Aatrox",
			radius = 80,
			range = 825,
			type = "linear",
			delay = 0.25,
			slotName = "W",
			cc = true,
			aoe = false,
			slot = 1,
			hitbox = true,
			speeds = 1800,
			collision = true
		},
		AatroxE = {
			charName = "Aatrox",
			range = 1000,
			speed = 1200,
			type = "triangular",
			delay = 0.25,
			slotName = "E",
			radius2 = 0,
			cc = true,
			radius1 = 120,
			slot = 2,
			hitbox = true,
			aoe = true,
			collision = false
		},
		AhriOrbofDeception = {
			charName = "Ahri",
			radius = 100,
			range = 880,
			type = "linear",
			delay = 0.25,
			slotName = "Q",
			aoe = true,
			cc = false,
			speed = 2500,
			slot = 0,
			hitbox = true,
			collision = false
		},
		AhriOrbReturn = {
			charName = "Ahri",
			radius = 100,
			range = 880,
			type = "linear",
			delay = 0.25,
			slotName = "Q",
			aoe = true,
			cc = false,
			speed = 2500,
			slot = 0,
			hitbox = true,
			collision = false
		},
		AhriSeduce = {
			charName = "Ahri",
			radius = 60,
			range = 965,
			type = "linear",
			delay = 0.25,
			slotName = "E",
			aoe = false,
			cc = false,
			speed = 1550,
			slot = 2,
			hitbox = true,
			collision = true
		},
		AkaliQ = {
			charName = "Akali",
			range = 550,
			collision = false,
			type = "conic",
			delay = 0.25,
			slotName = "Q",
			cc = true,
			aoe = true,
			slot = 0,
			hitbox = true,
			angle = 45,
			speeds = math.huge
		},
		AkaliW = {
			charName = "Akali",
			radius = 300,
			range = 300,
			type = "circular",
			delay = 0.25,
			slotName = "W",
			cc = false,
			aoe = true,
			slot = 1,
			hitbox = false,
			collision = false,
			speeds = math.huge
		},
		AkaliE = {
			charName = "Akali",
			radius = 70,
			range = 825,
			type = "linear",
			delay = 0.25,
			slotName = "E",
			cc = false,
			aoe = false,
			slot = 2,
			hitbox = true,
			speeds = 1650,
			collision = true
		},
		AkaliR = {
			charName = "Akali",
			radius = 80,
			range = 575,
			type = "linear",
			delay = 0,
			slotName = "R",
			cc = true,
			aoe = true,
			slot = 3,
			hitbox = true,
			speeds = 1650,
			collision = false
		},
		AkaliRb = {
			charName = "Akali",
			radius = 80,
			range = 575,
			type = "linear",
			delay = 0,
			slotName = "R",
			cc = false,
			aoe = true,
			slot = 3,
			hitbox = true,
			speeds = 3300,
			collision = false
		},
		Pulverize = {
			charName = "Alistar",
			radius = 365,
			range = 300,
			type = "circular",
			delay = 0.25,
			slotName = "Q",
			aoe = true,
			cc = true,
			slot = 0,
			hitbox = true,
			collision = false,
			speed = math.huge
		},
		BandageToss = {
			charName = "Amumu",
			radius = 80,
			range = 1100,
			type = "linear",
			delay = 0.25,
			slotName = "Q",
			aoe = false,
			cc = true,
			speed = 2000,
			slot = 0,
			hitbox = true,
			collision = true
		},
		AuraofDespair = {
			charName = "Amumu",
			radius = 300,
			range = 333,
			type = "circular",
			delay = 0.307,
			slotName = "W",
			aoe = true,
			cc = false,
			slot = 1,
			hitbox = false,
			collision = false,
			speed = math.huge
		},
		Tantrum = {
			charName = "Amumu",
			radius = 350,
			range = 333,
			type = "circular",
			delay = 0.25,
			slotName = "E",
			aoe = true,
			cc = false,
			slot = 2,
			hitbox = false,
			collision = false,
			speed = math.huge
		},
		CurseoftheSadMummy = {
			charName = "Amumu",
			radius = 550,
			range = 1000,
			type = "circular",
			delay = 0.25,
			slotName = "R",
			aoe = true,
			cc = true,
			slot = 3,
			hitbox = false,
			collision = false,
			speed = math.huge
		},
		FlashFrost = {
			charName = "Anivia",
			radius = 110,
			range = 1075,
			type = "linear",
			delay = 0.25,
			slotName = "Q",
			aoe = true,
			cc = true,
			speed = 850,
			slot = 0,
			hitbox = true,
			collision = false
		},
		Crystallize = {
			charName = "Anivia",
			range = 1000,
			type = "rectangle",
			delay = 0.25,
			slotName = "W",
			radius2 = 75,
			cc = true,
			radius1 = 250,
			slot = 1,
			hitbox = true,
			aoe = true,
			collision = false,
			speed = math.huge
		},
		GlacialStorm = {
			charName = "Anivia",
			radius = 400,
			range = 750,
			type = "circular",
			delay = 0.25,
			slotName = "R",
			aoe = true,
			cc = true,
			slot = 3,
			hitbox = true,
			collision = false,
			speed = math.huge
		},
		Incinerate = {
			charName = "Annie",
			range = 600,
			aoe = true,
			type = "conic",
			delay = 0.25,
			slotName = "W",
			collision = false,
			cc = false,
			slot = 1,
			hitbox = false,
			angle = 50,
			speed = math.huge
		},
		InfernalGuardian = {
			charName = "Annie",
			radius = 290,
			range = 600,
			type = "circular",
			delay = 0.25,
			slotName = "R",
			aoe = true,
			cc = false,
			slot = 3,
			hitbox = true,
			collision = false,
			speed = math.huge
		},
		Volley = {
			charName = "Ashe",
			radius = 20,
			range = 1200,
			type = "conic",
			delay = 0.25,
			slotName = "W",
			aoe = true,
			cc = true,
			speed = 1500,
			slot = 1,
			hitbox = true,
			collision = true,
			angle = 57.5
		},
		EnchantedCrystalArrow = {
			charName = "Ashe",
			radius = 130,
			range = 25000,
			type = "linear",
			delay = 0.25,
			slotName = "R",
			aoe = false,
			cc = true,
			speed = 1600,
			slot = 3,
			hitbox = true,
			collision = false
		},
		AurelionSolQ = {
			charName = "AurelionSol",
			radius = 210,
			range = 1075,
			type = "linear",
			delay = 0.25,
			slotName = "Q",
			aoe = true,
			cc = true,
			speed = 850,
			slot = 0,
			hitbox = true,
			collision = false
		},
		AurelionSolE = {
			charName = "AurelionSol",
			radius = 80,
			range = 7000,
			type = "linear",
			delay = 0.25,
			slotName = "E",
			aoe = true,
			cc = false,
			speed = 600,
			slot = 2,
			hitbox = true,
			collision = false
		},
		AurelionSolR = {
			charName = "AurelionSol",
			radius = 120,
			range = 1500,
			type = "linear",
			delay = 0.35,
			slotName = "R",
			aoe = true,
			cc = true,
			speed = 4500,
			slot = 3,
			hitbox = true,
			collision = false
		},
		BardQ = {
			charName = "Bard",
			radius = 60,
			range = 950,
			type = "linear",
			delay = 0.25,
			slotName = "Q",
			aoe = true,
			cc = true,
			speed = 1500,
			slot = 0,
			hitbox = true,
			collision = true
		},
		BardW = {
			charName = "Bard",
			radius = 100,
			range = 800,
			type = "circular",
			delay = 0.25,
			slotName = "W",
			aoe = true,
			cc = false,
			slot = 1,
			hitbox = true,
			collision = false,
			speed = math.huge
		},
		BardR = {
			charName = "Bard",
			radius = 350,
			range = 3400,
			type = "circular",
			delay = 0.5,
			slotName = "R",
			aoe = true,
			cc = true,
			speed = 2100,
			slot = 3,
			hitbox = true,
			collision = false
		},
		RocketGrab = {
			charName = "Blitzcrank",
			radius = 70,
			range = 925,
			type = "linear",
			delay = 0.25,
			slotName = "Q",
			aoe = false,
			cc = true,
			speed = 1800,
			slot = 0,
			hitbox = true,
			collision = true
		},
		StaticField = {
			charName = "Blitzcrank",
			radius = 600,
			range = 900,
			type = "circular",
			delay = 0.25,
			slotName = "R",
			aoe = true,
			cc = true,
			slot = 3,
			hitbox = false,
			collision = false,
			speed = math.huge
		},
		BrandQ = {
			charName = "Brand",
			radius = 60,
			range = 1050,
			type = "linear",
			delay = 0.25,
			slotName = "Q",
			aoe = false,
			cc = true,
			speed = 1600,
			slot = 0,
			hitbox = true,
			collision = true
		},
		BrandW = {
			charName = "Brand",
			radius = 250,
			range = 900,
			type = "circular",
			delay = 0.85,
			slotName = "W",
			aoe = true,
			cc = false,
			slot = 1,
			hitbox = true,
			collision = false,
			speed = math.huge
		},
		BraumQ = {
			charName = "Braum",
			radius = 60,
			range = 1000,
			type = "linear",
			delay = 0.25,
			slotName = "Q",
			aoe = true,
			cc = true,
			speed = 1700,
			slot = 0,
			hitbox = true,
			collision = true
		},
		BraumRWrapper = {
			charName = "Braum",
			radius = 115,
			range = 1250,
			type = "linear",
			delay = 0.5,
			slotName = "R",
			aoe = true,
			cc = true,
			speed = 1400,
			slot = 3,
			hitbox = true,
			collision = false
		},
		CaitlynPiltoverPeacemaker = {
			charName = "Caitlyn",
			radius = 60,
			range = 1500,
			type = "linear",
			delay = 0.625,
			slotName = "Q",
			aoe = true,
			cc = false,
			speed = 2200,
			slot = 0,
			hitbox = true,
			collision = false
		},
		CaitlynYordleTrap = {
			charName = "Caitlyn",
			radius = 75,
			range = 800,
			type = "circular",
			delay = 0.25,
			slotName = "W",
			aoe = false,
			cc = true,
			slot = 1,
			hitbox = true,
			collision = false,
			speed = math.huge
		},
		CaitlynEntrapmentMissile = {
			charName = "Caitlyn",
			radius = 60,
			range = 750,
			type = "linear",
			delay = 0.25,
			slotName = "E",
			aoe = false,
			cc = true,
			speed = 1500,
			slot = 2,
			hitbox = true,
			collision = true
		},
		EzrealR = {
			charName = "Ezreal",
			range = 2000,
			radius = 160,
			type = "linear",
			delay = 1,
			slotName = "R",
			speed = 2000,
			displayName = "Trueshot Barrage",
			collision = true,
			slot = _R
		},
		CamilleW = {
			charName = "Camille",
			range = 610,
			aoe = true,
			type = "conic",
			delay = 0.75,
			slotName = "W",
			collision = false,
			cc = true,
			slot = 1,
			hitbox = false,
			angle = 80,
			speed = math.huge
		},
		CamilleE = {
			charName = "Camille",
			radius = 60,
			range = 800,
			type = "linear",
			delay = 0.25,
			slotName = "E",
			aoe = false,
			cc = true,
			speed = 1900,
			slot = 2,
			hitbox = true,
			collision = false
		},
		CassiopeiaQ = {
			charName = "Cassiopeia",
			radius = 150,
			range = 850,
			type = "circular",
			delay = 0.4,
			slotName = "Q",
			aoe = true,
			cc = false,
			slot = 0,
			hitbox = true,
			collision = false,
			speed = math.huge
		},
		CassiopeiaW = {
			charName = "Cassiopeia",
			radius = 160,
			range = 800,
			type = "circular",
			delay = 0.25,
			slotName = "W",
			aoe = true,
			cc = false,
			slot = 1,
			hitbox = true,
			collision = false,
			speed = math.huge
		},
		CassiopeiaR = {
			charName = "Cassiopeia",
			range = 825,
			aoe = true,
			type = "conic",
			delay = 0.5,
			slotName = "R",
			collision = false,
			cc = true,
			slot = 3,
			hitbox = false,
			angle = 80,
			speed = math.huge
		},
		Rupture = {
			charName = "ChoGath",
			radius = 250,
			range = 950,
			type = "circular",
			delay = 0.5,
			slotName = "Q",
			aoe = true,
			cc = true,
			slot = 0,
			hitbox = true,
			collision = false,
			speed = math.huge
		},
		FeralScream = {
			charName = "ChoGath",
			range = 650,
			aoe = true,
			type = "conic",
			delay = 0.5,
			slotName = "W",
			collision = false,
			cc = true,
			slot = 1,
			hitbox = false,
			angle = 60,
			speed = math.huge
		},
		PhosphorusBomb = {
			charName = "Corki",
			radius = 250,
			range = 825,
			type = "circular",
			delay = 0.25,
			slotName = "Q",
			aoe = true,
			cc = false,
			speed = 1000,
			slot = 0,
			hitbox = true,
			collision = false
		},
		CarpetBomb = {
			charName = "Corki",
			radius = 100,
			range = 600,
			type = "linear",
			delay = 0,
			slotName = "W",
			aoe = true,
			cc = false,
			speed = 650,
			slot = 1,
			hitbox = true,
			collision = false
		},
		CarpetBombMega = {
			charName = "Corki",
			radius = 100,
			range = 1800,
			type = "linear",
			delay = 0,
			slotName = "W",
			aoe = true,
			cc = false,
			speed = 1500,
			slot = 1,
			hitbox = true,
			collision = false
		},
		GGun = {
			charName = "Corki",
			range = 600,
			aoe = true,
			type = "conic",
			delay = 0.25,
			slotName = "E",
			collision = false,
			cc = false,
			slot = 2,
			hitbox = false,
			angle = 35,
			speed = math.huge
		},
		MissileBarrageMissile = {
			charName = "Corki",
			radius = 35,
			range = 1225,
			type = "linear",
			delay = 0.175,
			slotName = "R",
			aoe = false,
			cc = false,
			speed = 1950,
			slot = 3,
			hitbox = true,
			collision = true
		},
		MissileBarrageMissile2 = {
			charName = "Corki",
			radius = 35,
			range = 1225,
			type = "linear",
			delay = 0.175,
			slotName = "R",
			aoe = false,
			cc = false,
			speed = 1950,
			slot = 3,
			hitbox = true,
			collision = true
		},
		DariusCleave = {
			charName = "Darius",
			radius = 425,
			range = 425,
			type = "circular",
			delay = 0.75,
			slotName = "Q",
			aoe = true,
			cc = false,
			slot = 0,
			hitbox = false,
			collision = false,
			speed = math.huge
		},
		DariusAxeGrabCone = {
			charName = "Darius",
			range = 535,
			aoe = true,
			type = "conic",
			delay = 0.25,
			slotName = "E",
			collision = false,
			cc = true,
			slot = 2,
			hitbox = false,
			angle = 50,
			speed = math.huge
		},
		DianaArc = {
			charName = "Diana",
			radius = 205,
			range = 900,
			type = "arc",
			delay = 0.25,
			slotName = "Q",
			aoe = true,
			cc = false,
			speed = 1400,
			slot = 0,
			hitbox = true,
			collision = false
		},
		InfectedCleaverMissileCast = {
			charName = "DrMundo",
			radius = 60,
			range = 975,
			type = "linear",
			delay = 0.25,
			slotName = "Q",
			aoe = false,
			cc = true,
			speed = 1850,
			slot = 0,
			hitbox = true,
			collision = true
		},
		DravenDoubleShot = {
			charName = "Draven",
			radius = 120,
			range = 1050,
			type = "linear",
			delay = 0.25,
			slotName = "E",
			aoe = true,
			cc = true,
			speed = 1400,
			slot = 2,
			hitbox = true,
			collision = false
		},
		DravenRCast = {
			charName = "Draven",
			radius = 130,
			range = 25000,
			type = "linear",
			delay = 0.5,
			slotName = "R",
			aoe = true,
			cc = false,
			speed = 2000,
			slot = 3,
			hitbox = true,
			collision = false
		},
		DravenRDoublecast = {
			charName = "Draven",
			radius = 130,
			range = 25000,
			type = "linear",
			delay = 0.5,
			slotName = "R",
			aoe = true,
			cc = false,
			speed = 2000,
			slot = 3,
			hitbox = true,
			collision = false
		},
		EkkoQ = {
			charName = "Ekko",
			radius = 135,
			range = 1075,
			type = "linear",
			delay = 0.25,
			slotName = "Q",
			aoe = true,
			cc = true,
			speed = 1650,
			slot = 0,
			hitbox = true,
			collision = false
		},
		EkkoW = {
			charName = "Ekko",
			radius = 400,
			range = 1600,
			type = "circular",
			delay = 3.75,
			slotName = "W",
			aoe = true,
			cc = true,
			speed = 1650,
			slot = 1,
			hitbox = true,
			collision = false
		},
		EkkoR = {
			charName = "Ekko",
			radius = 375,
			range = 1600,
			type = "circular",
			delay = 0.25,
			slotName = "R",
			aoe = true,
			cc = false,
			speed = 1650,
			slot = 3,
			hitbox = false,
			collision = false
		},
		EliseHumanE = {
			charName = "Elise",
			radius = 55,
			range = 1075,
			type = "linear",
			delay = 0.25,
			slotName = "E",
			aoe = false,
			cc = true,
			speed = 1600,
			slot = 2,
			hitbox = true,
			collision = true
		},
		EvelynnQ = {
			charName = "Evelynn",
			radius = 35,
			range = 800,
			type = "linear",
			delay = 0.25,
			slotName = "Q",
			aoe = false,
			cc = false,
			speed = 2200,
			slot = 0,
			hitbox = true,
			collision = true
		},
		EvelynnR = {
			charName = "Evelynn",
			range = 450,
			aoe = true,
			type = "conic",
			delay = 0.35,
			slotName = "R",
			collision = false,
			cc = false,
			slot = 3,
			hitbox = false,
			angle = 180,
			speed = math.huge
		},
		EzrealMysticShot = {
			charName = "Ezreal",
			radius = 80,
			range = 1150,
			type = "linear",
			delay = 0.25,
			slotName = "Q",
			aoe = false,
			cc = false,
			speed = 2000,
			slot = 0,
			hitbox = true,
			collision = true
		},
		EzrealEssenceFlux = {
			charName = "Ezreal",
			radius = 80,
			range = 1000,
			type = "linear",
			delay = 0.25,
			slotName = "W",
			aoe = true,
			cc = false,
			speed = 1550,
			slot = 1,
			hitbox = true,
			collision = false
		},
		EzrealTrueshotBarrage = {
			charName = "Ezreal",
			radius = 160,
			range = 25000,
			type = "linear",
			delay = 1,
			slotName = "R",
			aoe = true,
			cc = false,
			speed = 2000,
			slot = 3,
			hitbox = true,
			collision = false
		},
		FioraQ = {
			charName = "Fiora",
			radius = 50,
			range = 400,
			type = "linear",
			delay = 0,
			slotName = "Q",
			aoe = false,
			cc = false,
			speed = 800,
			slot = 0,
			hitbox = true,
			collision = false
		},
		FioraW = {
			charName = "Fiora",
			radius = 85,
			range = 750,
			type = "linear",
			delay = 0.75,
			slotName = "W",
			aoe = true,
			cc = true,
			slot = 1,
			hitbox = true,
			collision = false,
			speed = math.huge
		},
		FizzR = {
			charName = "Fizz",
			radius = 120,
			range = 1300,
			type = "linear",
			delay = 0.25,
			slotName = "R",
			aoe = true,
			cc = true,
			speed = 1300,
			slot = 3,
			hitbox = true,
			collision = false
		},
		GalioQ = {
			charName = "Galio",
			radius = 150,
			range = 825,
			type = "arc",
			delay = 0.25,
			slotName = "Q",
			aoe = true,
			cc = false,
			speed = 1150,
			slot = 0,
			hitbox = true,
			collision = false
		},
		GalioE = {
			charName = "Galio",
			radius = 160,
			range = 650,
			type = "linear",
			delay = 0.45,
			slotName = "E",
			aoe = true,
			cc = true,
			speed = 1400,
			slot = 2,
			hitbox = true,
			collision = false
		},
		GalioR = {
			charName = "Galio",
			radius = 500,
			range = 5500,
			type = "circular",
			delay = 2.75,
			slotName = "R",
			aoe = true,
			cc = true,
			slot = 3,
			hitbox = true,
			collision = false,
			speed = math.huge
		},
		GangplankE = {
			charName = "Gangplank",
			radius = 400,
			range = 1000,
			type = "circular",
			delay = 0.25,
			slotName = "E",
			aoe = true,
			cc = true,
			slot = 2,
			hitbox = true,
			collision = false,
			speed = math.huge
		},
		GangplankR = {
			charName = "Gangplank",
			radius = 600,
			range = 25000,
			type = "circular",
			delay = 0.25,
			slotName = "R",
			aoe = true,
			cc = true,
			slot = 3,
			hitbox = true,
			collision = false,
			speed = math.huge
		},
		GnarQ = {
			charName = "Gnar",
			radius = 55,
			range = 1100,
			type = "linear",
			delay = 0.25,
			slotName = "Q",
			aoe = true,
			cc = true,
			speed = 1700,
			slot = 0,
			hitbox = true,
			collision = false
		},
		GnarQReturn = {
			charName = "Gnar",
			radius = 70,
			range = 3000,
			type = "linear",
			delay = 0.25,
			slotName = "Q",
			aoe = true,
			cc = true,
			speed = 1700,
			slot = 0,
			hitbox = true,
			collision = false
		},
		GnarE = {
			charName = "Gnar",
			radius = 160,
			range = 475,
			type = "circular",
			delay = 0.25,
			slotName = "E",
			aoe = false,
			cc = true,
			speed = 900,
			slot = 2,
			hitbox = true,
			collision = false
		},
		GnarBigQ = {
			charName = "Gnar",
			radius = 90,
			range = 1100,
			type = "linear",
			delay = 0.5,
			slotName = "Q",
			aoe = true,
			cc = true,
			speed = 2100,
			slot = 0,
			hitbox = true,
			collision = true
		},
		GnarBigW = {
			charName = "Gnar",
			radius = 100,
			range = 550,
			type = "linear",
			delay = 0.6,
			slotName = "W",
			aoe = true,
			cc = true,
			slot = 1,
			hitbox = true,
			collision = false,
			speed = math.huge
		},
		GnarBigE = {
			charName = "Gnar",
			radius = 375,
			range = 600,
			type = "circular",
			delay = 0.25,
			slotName = "E",
			aoe = true,
			cc = true,
			speed = 800,
			slot = 2,
			hitbox = true,
			collision = false
		},
		GnarR = {
			charName = "Gnar",
			radius = 475,
			range = 475,
			type = "circular",
			delay = 0.25,
			slotName = "R",
			aoe = true,
			cc = true,
			slot = 3,
			hitbox = true,
			collision = false,
			speed = math.huge
		},
		GragasQ = {
			charName = "Gragas",
			radius = 250,
			range = 850,
			type = "circular",
			delay = 0.25,
			slotName = "Q",
			aoe = true,
			cc = true,
			speed = 1000,
			slot = 0,
			hitbox = true,
			collision = false
		},
		GragasE = {
			charName = "Gragas",
			radius = 170,
			range = 600,
			type = "linear",
			delay = 0.25,
			slotName = "E",
			aoe = true,
			cc = true,
			speed = 900,
			slot = 2,
			hitbox = true,
			collision = true
		},
		GragasR = {
			charName = "Gragas",
			radius = 400,
			range = 1000,
			type = "circular",
			delay = 0.25,
			slotName = "R",
			aoe = true,
			cc = true,
			speed = 1800,
			slot = 3,
			hitbox = true,
			collision = false
		},
		GravesQLineSpell = {
			charName = "Graves",
			radius = 40,
			range = 925,
			type = "linear",
			delay = 0.25,
			slotName = "Q",
			aoe = true,
			cc = false,
			speed = 3700,
			slot = 0,
			hitbox = true,
			collision = false
		},
		GravesQLineMis = {
			charName = "Graves",
			range = 925,
			type = "rectangle",
			delay = 0.25,
			slotName = "Q",
			radius2 = 100,
			cc = false,
			radius1 = 250,
			slot = 0,
			hitbox = true,
			aoe = true,
			collision = false,
			speed = math.huge
		},
		GravesQReturn = {
			charName = "Graves",
			radius = 40,
			range = 925,
			type = "linear",
			delay = 0.25,
			slotName = "Q",
			aoe = true,
			cc = false,
			speed = 1850,
			slot = 0,
			hitbox = true,
			collision = false
		},
		GravesSmokeGrenade = {
			charName = "Graves",
			radius = 250,
			range = 950,
			type = "circular",
			delay = 0.25,
			slotName = "W",
			aoe = true,
			cc = true,
			speed = 1450,
			slot = 1,
			hitbox = true,
			collision = false
		},
		GravesChargeShot = {
			charName = "Graves",
			radius = 100,
			range = 1000,
			type = "linear",
			delay = 0.25,
			slotName = "R",
			aoe = true,
			cc = false,
			speed = 1950,
			slot = 3,
			hitbox = true,
			collision = false
		},
		GravesChargeShotFxMissile = {
			charName = "Graves",
			range = 800,
			aoe = true,
			type = "conic",
			delay = 0.3,
			slotName = "R",
			collision = false,
			cc = false,
			slot = 3,
			hitbox = true,
			angle = 80,
			speed = math.huge
		},
		HecarimRapidSlash = {
			charName = "Hecarim",
			radius = 350,
			range = 350,
			type = "circular",
			delay = 0,
			slotName = "Q",
			aoe = true,
			cc = false,
			slot = 0,
			hitbox = false,
			collision = false,
			speed = math.huge
		},
		HecarimUlt = {
			charName = "Hecarim",
			radius = 210,
			range = 1000,
			type = "linear",
			delay = 0.01,
			slotName = "R",
			aoe = true,
			cc = true,
			speed = 1200,
			slot = 3,
			hitbox = true,
			collision = false
		},
		HeimerdingerQ = {
			charName = "Heimerdinger",
			radius = 55,
			range = 450,
			type = "circular",
			delay = 0.25,
			slotName = "Q",
			aoe = true,
			cc = false,
			slot = 0,
			hitbox = true,
			collision = false,
			speed = math.huge
		},
		HeimerdingerW = {
			charName = "Heimerdinger",
			radius = 30,
			range = 1325,
			type = "linear",
			delay = 0.25,
			slotName = "W",
			aoe = true,
			cc = false,
			speed = 2050,
			slot = 1,
			hitbox = true,
			collision = true
		},
		HeimerdingerE = {
			charName = "Heimerdinger",
			radius = 250,
			range = 970,
			type = "circular",
			delay = 0.25,
			slotName = "E",
			aoe = true,
			cc = true,
			speed = 1200,
			slot = 2,
			hitbox = true,
			collision = false
		},
		HeimerdingerEUlt = {
			charName = "Heimerdinger",
			radius = 250,
			range = 970,
			type = "circular",
			delay = 0.25,
			slotName = "E",
			aoe = true,
			cc = true,
			speed = 1200,
			slot = 2,
			hitbox = true,
			collision = false
		},
		IllaoiQ = {
			charName = "Illaoi",
			radius = 100,
			range = 850,
			type = "linear",
			delay = 0.75,
			slotName = "Q",
			aoe = true,
			cc = false,
			slot = 0,
			hitbox = true,
			collision = false,
			speed = math.huge
		},
		IllaoiE = {
			charName = "Illaoi",
			radius = 45,
			range = 900,
			type = "linear",
			delay = 0.25,
			slotName = "E",
			aoe = false,
			cc = false,
			speed = 1800,
			slot = 2,
			hitbox = true,
			collision = true
		},
		IllaoiR = {
			charName = "Illaoi",
			radius = 450,
			range = 300,
			type = "circular",
			delay = 0.5,
			slotName = "R",
			aoe = true,
			cc = false,
			slot = 3,
			hitbox = false,
			collision = false,
			speed = math.huge
		},
		IreliaW2 = {
			charName = "Irelia",
			radius = 275,
			range = 200,
			type = "circular",
			delay = 0,
			slotName = "W",
			aoe = true,
			cc = false,
			slot = 1,
			hitbox = false,
			collision = false,
			speed = math.huge
		},
		IreliaW2 = {
			charName = "Irelia",
			radius = 90,
			range = 825,
			type = "linear",
			delay = 0.25,
			slotName = "W",
			aoe = true,
			cc = false,
			slot = 1,
			hitbox = false,
			collision = false,
			speed = math.huge
		},
		IreliaE = {
			charName = "Irelia",
			radius = 90,
			range = 850,
			type = "circular",
			delay = 0,
			slotName = "E",
			cc = true,
			aoe = true,
			slot = 2,
			hitbox = true,
			speeds = 2000,
			collision = false
		},
		IreliaE2 = {
			charName = "Irelia",
			radius = 90,
			range = 850,
			type = "circular",
			delay = 0,
			slotName = "E",
			cc = true,
			aoe = true,
			slot = 2,
			hitbox = true,
			speeds = 2000,
			collision = false
		},
		IreliaR = {
			charName = "Irelia",
			radius = 160,
			range = 1000,
			type = "linear",
			delay = 0.4,
			slotName = "R",
			aoe = true,
			cc = true,
			speed = 2000,
			slot = 3,
			hitbox = true,
			collision = false
		},
		IvernQ = {
			charName = "Ivern",
			radius = 50,
			range = 1075,
			type = "linear",
			delay = 0.25,
			slotName = "Q",
			aoe = false,
			cc = true,
			speed = 1300,
			slot = 0,
			hitbox = true,
			collision = true
		},
		IvernW = {
			charName = "Ivern",
			radius = 150,
			range = 800,
			type = "circular",
			delay = 0.25,
			slotName = "W",
			aoe = true,
			cc = false,
			slot = 1,
			hitbox = true,
			collision = false,
			speed = math.huge
		},
		HowlingGale = {
			charName = "Janna",
			radius = 100,
			range = 1750,
			type = "linear",
			delay = 0,
			slotName = "Q",
			aoe = true,
			cc = true,
			speed = 1167,
			slot = 0,
			hitbox = true,
			collision = false
		},
		ReapTheWhirlwind = {
			charName = "Janna",
			radius = 725,
			range = 500,
			type = "circular",
			delay = 0.001,
			slotName = "R",
			aoe = true,
			cc = true,
			slot = 3,
			hitbox = false,
			collision = false,
			speed = math.huge
		},
		JarvanIVDragonStrike = {
			charName = "JarvanIV",
			radius = 60,
			range = 770,
			type = "linear",
			delay = 0.4,
			slotName = "Q",
			aoe = true,
			cc = false,
			slot = 0,
			hitbox = true,
			collision = false,
			speed = math.huge
		},
		JarvanIVGoldenAegis = {
			charName = "JarvanIV",
			radius = 625,
			range = 355,
			type = "circular",
			delay = 0.125,
			slotName = "W",
			aoe = true,
			cc = true,
			slot = 1,
			hitbox = false,
			collision = false,
			speed = math.huge
		},
		JarvanIVDemacianStandard = {
			charName = "JarvanIV",
			radius = 175,
			range = 860,
			type = "circular",
			delay = 0,
			slotName = "E",
			aoe = true,
			cc = false,
			speed = 3440,
			slot = 2,
			hitbox = true,
			collision = false
		},
		JaxCounterStrike = {
			charName = "Jax",
			radius = 300,
			range = 300,
			type = "circular",
			delay = 1.4,
			slotName = "E",
			aoe = true,
			cc = true,
			slot = 2,
			hitbox = false,
			collision = false,
			speed = math.huge
		},
		JayceShockBlast = {
			charName = "Jayce",
			radius = 75,
			range = 1050,
			type = "linear",
			delay = 0.214,
			slotName = "Q",
			aoe = true,
			cc = false,
			speed = 1450,
			slot = 0,
			hitbox = true,
			collision = true
		},
		JayceShockBlastWallMis = {
			charName = "Jayce",
			radius = 105,
			range = 2030,
			type = "linear",
			delay = 0.214,
			slotName = "Q",
			aoe = true,
			cc = false,
			speed = 1890,
			slot = 0,
			hitbox = true,
			collision = true
		},
		JayceStaticField = {
			charName = "Jayce",
			radius = 285,
			range = 300,
			type = "circular",
			delay = 0.25,
			slotName = "W",
			aoe = true,
			cc = false,
			slot = 1,
			hitbox = false,
			collision = false,
			speed = math.huge
		},
		JhinW = {
			charName = "Jhin",
			radius = 40,
			range = 3000,
			type = "linear",
			delay = 0.75,
			slotName = "W",
			aoe = false,
			cc = true,
			speed = 5000,
			slot = 1,
			hitbox = true,
			collision = false
		},
		JhinE = {
			charName = "Jhin",
			radius = 140,
			range = 750,
			type = "circular",
			delay = 0.25,
			slotName = "E",
			aoe = false,
			cc = true,
			speed = 1650,
			slot = 2,
			hitbox = true,
			collision = false
		},
		JhinRShot = {
			charName = "Jhin",
			radius = 80,
			range = 3500,
			type = "linear",
			delay = 0.25,
			slotName = "R",
			aoe = false,
			cc = true,
			speed = 5000,
			slot = 3,
			hitbox = true,
			collision = false
		},
		JinxW = {
			charName = "Jinx",
			radius = 50,
			range = 1450,
			type = "linear",
			delay = 0.6,
			slotName = "W",
			aoe = false,
			cc = true,
			speed = 3200,
			slot = 1,
			hitbox = true,
			collision = true
		},
		JinxE = {
			charName = "Jinx",
			radius = 100,
			range = 900,
			type = "circular",
			delay = 1.5,
			slotName = "E",
			aoe = true,
			cc = true,
			speed = 2570,
			slot = 2,
			hitbox = true,
			collision = false
		},
		JinxR = {
			charName = "Jinx",
			radius = 110,
			range = 25000,
			type = "linear",
			delay = 0.6,
			slotName = "R",
			aoe = true,
			cc = false,
			speed = 1700,
			slot = 3,
			hitbox = true,
			collision = false
		},
		KatarinaW = {
			charName = "Katarina",
			radius = 340,
			range = 300,
			type = "circular",
			delay = 1.25,
			slotName = "W",
			aoe = true,
			cc = false,
			slot = 1,
			hitbox = false,
			collision = false,
			speed = math.huge
		},
		KatarinaE = {
			charName = "Katarina",
			radius = 150,
			range = 725,
			type = "circular",
			delay = 0.15,
			slotName = "E",
			aoe = false,
			cc = false,
			slot = 2,
			hitbox = true,
			collision = false,
			speed = math.huge
		},
		KatarinaR = {
			charName = "Katarina",
			radius = 550,
			range = 450,
			type = "circular",
			delay = 0,
			slotName = "R",
			aoe = true,
			cc = false,
			slot = 3,
			hitbox = false,
			collision = false,
			speed = math.huge
		},
		KalistaMysticShot = {
			charName = "Kalista",
			radius = 35,
			range = 1150,
			type = "linear",
			delay = 0.35,
			slotName = "Q",
			aoe = false,
			cc = false,
			speed = 2100,
			slot = 0,
			hitbox = true,
			collision = true
		},
		KarmaQ = {
			charName = "Karma",
			radius = 80,
			range = 950,
			type = "linear",
			delay = 0.25,
			slotName = "Q",
			aoe = false,
			cc = true,
			speed = 1750,
			slot = 0,
			hitbox = true,
			collision = true
		},
		KarmaQMantra = {
			charName = "Karma",
			radius = 80,
			range = 950,
			type = "linear",
			delay = 0.25,
			slotName = "Q",
			aoe = false,
			cc = true,
			speed = 1750,
			slot = 0,
			hitbox = true,
			collision = true
		},
		KarthusLayWasteA1 = {
			charName = "Karthus",
			radius = 200,
			range = 875,
			type = "circular",
			delay = 0.5,
			slotName = "Q",
			aoe = true,
			cc = false,
			slot = 0,
			hitbox = true,
			collision = false,
			speed = math.huge
		},
		KarthusLayWasteA2 = {
			charName = "Karthus",
			radius = 200,
			range = 875,
			type = "circular",
			delay = 0.5,
			slotName = "Q",
			aoe = true,
			cc = false,
			slot = 0,
			hitbox = true,
			collision = false,
			speed = math.huge
		},
		KarthusLayWasteA3 = {
			charName = "Karthus",
			radius = 200,
			range = 875,
			type = "circular",
			delay = 0.5,
			slotName = "Q",
			aoe = true,
			cc = false,
			slot = 0,
			hitbox = true,
			collision = false,
			speed = math.huge
		},
		KarthusWallOfPain = {
			charName = "Karthus",
			range = 1000,
			type = "rectangle",
			delay = 0.25,
			slotName = "W",
			radius2 = 75,
			cc = true,
			radius1 = 470,
			slot = 1,
			hitbox = true,
			aoe = true,
			collision = false,
			speed = math.huge
		},
		ForcePulse = {
			charName = "Kassadin",
			range = 600,
			aoe = true,
			type = "conic",
			delay = 0.25,
			slotName = "E",
			collision = false,
			cc = true,
			slot = 2,
			hitbox = false,
			angle = 80,
			speed = math.huge
		},
		Riftwalk = {
			charName = "Kassadin",
			radius = 300,
			range = 500,
			type = "circular",
			delay = 0.25,
			slotName = "R",
			aoe = true,
			cc = false,
			slot = 3,
			hitbox = true,
			collision = false,
			speed = math.huge
		},
		KaynQ = {
			charName = "Kayn",
			radius = 350,
			range = 400,
			type = "circular",
			delay = 0.15,
			slotName = "Q",
			aoe = true,
			cc = false,
			slot = 0,
			hitbox = false,
			collision = false,
			speed = math.huge
		},
		KaynW = {
			charName = "Kayn",
			radius = 90,
			range = 700,
			type = "linear",
			delay = 0.55,
			slotName = "W",
			aoe = true,
			cc = true,
			slot = 1,
			hitbox = true,
			collision = false,
			speed = math.huge
		},
		KennenShurikenHurlMissile1 = {
			charName = "Kennen",
			radius = 45,
			range = 1050,
			type = "linear",
			delay = 0.175,
			slotName = "Q",
			aoe = false,
			cc = false,
			speed = 1650,
			slot = 0,
			hitbox = true,
			collision = true
		},
		KennenShurikenStorm = {
			charName = "Kennen",
			radius = 550,
			range = 700,
			type = "circular",
			delay = 0.25,
			slotName = "R",
			aoe = true,
			cc = false,
			slot = 3,
			hitbox = false,
			collision = false,
			speed = math.huge
		},
		KaisaW = {
			charName = "Kaisa",
			radius = 65,
			range = 3000,
			type = "linear",
			delay = 0.4,
			slotName = "W",
			aoe = false,
			cc = false,
			speed = 1750,
			slot = 1,
			hitbox = true,
			collision = true
		},
		KhazixW = {
			charName = "Khazix",
			radius = 60,
			range = 1000,
			type = "linear",
			delay = 0.25,
			slotName = "W",
			aoe = false,
			cc = false,
			speed = 1650,
			slot = 1,
			hitbox = true,
			collision = true
		},
		KhazixWLong = {
			charName = "Khazix",
			radius = 70,
			range = 1000,
			type = "linear",
			delay = 0.25,
			slotName = "W",
			aoe = true,
			cc = true,
			speed = 1650,
			slot = 1,
			hitbox = true,
			collision = true
		},
		KhazixE = {
			charName = "Khazix",
			radius = 320,
			range = 700,
			type = "circular",
			delay = 0.25,
			slotName = "E",
			aoe = true,
			cc = false,
			speed = 1400,
			slot = 2,
			hitbox = true,
			collision = false
		},
		KhazixELong = {
			charName = "Khazix",
			radius = 320,
			range = 900,
			type = "circular",
			delay = 0.25,
			slotName = "E",
			aoe = true,
			cc = false,
			speed = 1400,
			slot = 2,
			hitbox = true,
			collision = false
		},
		KindredQ = {
			charName = "Kindred",
			radius = 55,
			range = 340,
			type = "linear",
			delay = 0.01,
			slotName = "Q",
			aoe = false,
			cc = false,
			speed = 1360,
			slot = 0,
			hitbox = true,
			collision = false
		},
		KindredR = {
			charName = "Kindred",
			radius = 500,
			range = 300,
			type = "circular",
			delay = 0.25,
			slotName = "R",
			aoe = true,
			cc = false,
			slot = 3,
			hitbox = false,
			collision = false,
			speed = math.huge
		},
		KledQ = {
			charName = "Kled",
			radius = 60,
			range = 800,
			type = "linear",
			delay = 0.25,
			slotName = "Q",
			aoe = false,
			cc = true,
			speed = 1400,
			slot = 0,
			hitbox = true,
			collision = true
		},
		KledEDash = {
			charName = "Kled",
			radius = 90,
			range = 550,
			type = "linear",
			delay = 0,
			slotName = "E",
			aoe = true,
			cc = false,
			speed = 1100,
			slot = 2,
			hitbox = true,
			collision = false
		},
		KledRiderQ = {
			charName = "Kled",
			range = 700,
			aoe = true,
			type = "conic",
			delay = 0.25,
			slotName = "Q",
			collision = false,
			cc = false,
			slot = 0,
			hitbox = false,
			angle = 25,
			speed = math.huge
		},
		KogMawQ = {
			charName = "KogMaw",
			radius = 60,
			range = 1175,
			type = "linear",
			delay = 0.25,
			slotName = "Q",
			aoe = false,
			cc = false,
			speed = 1600,
			slot = 0,
			hitbox = true,
			collision = true
		},
		KogMawVoidOoze = {
			charName = "KogMaw",
			radius = 115,
			range = 1280,
			type = "linear",
			delay = 0.25,
			slotName = "E",
			aoe = true,
			cc = true,
			speed = 1350,
			slot = 2,
			hitbox = true,
			collision = false
		},
		KogMawLivingArtillery = {
			charName = "KogMaw",
			radius = 200,
			range = 1800,
			type = "circular",
			delay = 0.85,
			slotName = "R",
			aoe = true,
			cc = false,
			slot = 3,
			hitbox = true,
			collision = false,
			speed = math.huge
		},
		LeblancW = {
			charName = "Leblanc",
			radius = 260,
			range = 600,
			type = "circular",
			delay = 0.25,
			slotName = "W",
			aoe = true,
			cc = false,
			speed = 1600,
			slot = 1,
			hitbox = true,
			collision = false
		},
		LeblancE = {
			charName = "Leblanc",
			radius = 55,
			range = 925,
			type = "linear",
			delay = 0.25,
			slotName = "E",
			aoe = false,
			cc = true,
			speed = 1750,
			slot = 2,
			hitbox = true,
			collision = true
		},
		LeblancRW = {
			charName = "Leblanc",
			radius = 260,
			range = 600,
			type = "circular",
			delay = 0.25,
			slotName = "W",
			aoe = true,
			cc = false,
			speed = 1600,
			slot = 1,
			hitbox = true,
			collision = false
		},
		LeblancRE = {
			charName = "Leblanc",
			radius = 55,
			range = 925,
			type = "linear",
			delay = 0.25,
			slotName = "E",
			aoe = false,
			cc = true,
			speed = 1750,
			slot = 2,
			hitbox = true,
			collision = true
		},
		BlinkMonkQOne = {
			charName = "LeeSin",
			radius = 50,
			range = 1100,
			type = "linear",
			delay = 0.25,
			slotName = "Q",
			aoe = false,
			cc = false,
			speed = 1750,
			slot = 0,
			hitbox = true,
			collision = true
		},
		BlinkMonkEOne = {
			charName = "LeeSin",
			radius = 350,
			range = 400,
			type = "circular",
			delay = 0.25,
			slotName = "E",
			aoe = true,
			cc = false,
			slot = 2,
			hitbox = false,
			collision = false,
			speed = math.huge
		},
		LeonaZenithBladeMissile = {
			charName = "Leona",
			radius = 70,
			range = 875,
			type = "linear",
			delay = 0.25,
			slotName = "E",
			aoe = false,
			cc = true,
			speed = 2000,
			slot = 2,
			hitbox = true,
			collision = false
		},
		LeonaSolarFlare = {
			charName = "Leona",
			radius = 250,
			range = 1200,
			type = "circular",
			delay = 0.625,
			slotName = "R",
			aoe = true,
			cc = true,
			slot = 3,
			hitbox = true,
			collision = false,
			speed = math.huge
		},
		LissandraQ = {
			charName = "Lissandra",
			radius = 65,
			range = 825,
			type = "linear",
			delay = 0.251,
			slotName = "Q",
			aoe = true,
			cc = true,
			speed = 2400,
			slot = 0,
			hitbox = true,
			collision = false
		},
		LissandraW = {
			charName = "Lissandra",
			radius = 450,
			range = 333,
			type = "circular",
			delay = 0.25,
			slotName = "W",
			aoe = true,
			cc = true,
			slot = 1,
			hitbox = false,
			collision = false,
			speed = math.huge
		},
		LissandraE = {
			charName = "Lissandra",
			radius = 100,
			range = 1050,
			type = "linear",
			delay = 0.25,
			slotName = "E",
			aoe = true,
			cc = false,
			speed = 850,
			slot = 2,
			hitbox = true,
			collision = false
		},
		LucianQ = {
			charName = "Lucian",
			radius = 65,
			range = 900,
			type = "linear",
			delay = 0.5,
			slotName = "Q",
			aoe = true,
			cc = false,
			slot = 0,
			hitbox = true,
			collision = false,
			speed = math.huge
		},
		LucianW = {
			charName = "Lucian",
			radius = 65,
			range = 900,
			type = "linear",
			delay = 0.25,
			slotName = "W",
			aoe = true,
			cc = false,
			speed = 1600,
			slot = 1,
			hitbox = true,
			collision = false
		},
		LucianR = {
			charName = "Lucian",
			radius = 75,
			range = 1200,
			type = "linear",
			delay = 0.01,
			slotName = "R",
			aoe = false,
			cc = false,
			speed = 2800,
			slot = 3,
			hitbox = true,
			collision = true
		},
		PykeQMelee = {
			charName = "Pyke",
			radius = 70,
			range = 400,
			type = "linear",
			delay = 0.25,
			slotName = "Q",
			cc = true,
			aoe = true,
			slot = 0,
			hitbox = true,
			collision = false,
			speeds = math.huge
		},
		PykeQRange = {
			charName = "Pyke",
			radius = 70,
			range = 1100,
			type = "linear",
			delay = 0.2,
			slotName = "Q",
			cc = true,
			aoe = false,
			slot = 0,
			hitbox = true,
			speeds = 2000,
			collision = true
		},
		PykeR = {
			charName = "Pyke",
			range = 750,
			aoe = true,
			type = "cross",
			delay = 0.5,
			slotName = "R",
			radius2 = 50,
			cc = false,
			radius1 = 300,
			slot = 3,
			hitbox = true,
			collision = false,
			speeds = math.huge
		},
		LuluQ = {
			charName = "Lulu",
			radius = 45,
			range = 925,
			type = "linear",
			delay = 0.25,
			slotName = "Q",
			aoe = true,
			cc = true,
			speed = 1500,
			slot = 0,
			hitbox = true,
			collision = false
		},
		LuxLightBinding = {
			charName = "Lux",
			radius = 60,
			range = 1175,
			type = "linear",
			delay = 0.25,
			slotName = "Q",
			aoe = true,
			cc = true,
			speed = 1200,
			slot = 0,
			hitbox = true,
			collision = true
		},
		LuxPrismaticWave = {
			charName = "Lux",
			radius = 120,
			range = 1075,
			type = "linear",
			delay = 0.25,
			slotName = "W",
			aoe = true,
			cc = false,
			speed = 1400,
			slot = 1,
			hitbox = true,
			collision = false
		},
		LuxLightStrikeKugel = {
			charName = "Lux",
			radius = 310,
			range = 1000,
			type = "circular",
			delay = 0.25,
			slotName = "E",
			aoe = true,
			cc = true,
			speed = 1300,
			slot = 2,
			hitbox = true,
			collision = false
		},
		LuxMaliceCannon = {
			charName = "Lux",
			radius = 115,
			range = 2000,
			type = "linear",
			delay = 1,
			slotName = "R",
			aoe = true,
			cc = true,
			slot = 3,
			hitbox = true,
			collision = false,
			speed = math.huge
		},
		Landslide = {
			charName = "Malphite",
			radius = 200,
			range = 355,
			type = "circular",
			delay = 0.242,
			slotName = "E",
			aoe = true,
			cc = true,
			slot = 2,
			hitbox = false,
			collision = false,
			speed = math.huge
		},
		UFSlash = {
			charName = "Malphite",
			radius = 300,
			range = 1000,
			type = "circular",
			delay = 0,
			slotName = "R",
			aoe = true,
			cc = true,
			speed = 2170,
			slot = 3,
			hitbox = true,
			collision = false
		},
		MalzaharQ = {
			charName = "Malzahar",
			range = 900,
			type = "rectangle",
			delay = 0.25,
			slotName = "Q",
			radius2 = 100,
			cc = true,
			radius1 = 400,
			slot = 0,
			hitbox = true,
			aoe = true,
			collision = false,
			speed = math.huge
		},
		MaokaiQ = {
			charName = "Maokai",
			radius = 150,
			range = 600,
			type = "linear",
			delay = 0.375,
			slotName = "Q",
			aoe = true,
			cc = true,
			speed = 1600,
			slot = 0,
			hitbox = true,
			collision = false
		},
		MaokaiR = {
			charName = "Maokai",
			radius = 650,
			range = 3000,
			type = "linear",
			delay = 0.5,
			slotName = "R",
			aoe = true,
			cc = true,
			speed = 450,
			slot = 3,
			hitbox = true,
			collision = false
		},
		MissFortuneScattershot = {
			charName = "MissFortune",
			radius = 400,
			range = 1000,
			type = "circular",
			delay = 0.5,
			slotName = "E",
			aoe = true,
			cc = true,
			slot = 2,
			hitbox = true,
			collision = false,
			speed = math.huge
		},
		MissFortuneBulletTime = {
			charName = "MissFortune",
			range = 1400,
			aoe = true,
			type = "conic",
			delay = 0.001,
			slotName = "R",
			collision = false,
			cc = false,
			slot = 3,
			hitbox = false,
			width = 40,
			speed = math.huge
		},
		MordekaiserSiphonOfDestruction = {
			charName = "Mordekaiser",
			range = 675,
			aoe = true,
			type = "conic",
			delay = 0.25,
			slotName = "E",
			collision = false,
			cc = false,
			slot = 2,
			hitbox = false,
			angle = 50,
			speed = math.huge
		},
		DarkBinding = {
			charName = "Morgana",
			radius = 60,
			range = 1175,
			type = "linear",
			delay = 0.25,
			slotName = "Q",
			aoe = false,
			cc = true,
			speed = 1200,
			slot = 0,
			hitbox = true,
			collision = true
		},
		TormentedSoil = {
			charName = "Morgana",
			radius = 325,
			range = 900,
			type = "circular",
			delay = 0.25,
			slotName = "W",
			aoe = true,
			cc = false,
			slot = 1,
			hitbox = true,
			collision = false,
			speed = math.huge
		},
		NamiQ = {
			charName = "Nami",
			radius = 200,
			range = 875,
			type = "circular",
			delay = 0.95,
			slotName = "Q",
			aoe = true,
			cc = true,
			slot = 0,
			hitbox = true,
			collision = false,
			speed = math.huge
		},
		NamiR = {
			charName = "Nami",
			radius = 215,
			range = 2750,
			type = "linear",
			delay = 0.5,
			slotName = "R",
			aoe = true,
			cc = true,
			speed = 850,
			slot = 3,
			hitbox = true,
			collision = false
		},
		NasusE = {
			charName = "Nasus",
			radius = 400,
			range = 650,
			type = "circular",
			delay = 0.25,
			slotName = "E",
			aoe = true,
			cc = false,
			slot = 2,
			hitbox = true,
			collision = false,
			speed = math.huge
		},
		NautilusAnchorDrag = {
			charName = "Nautilus",
			radius = 75,
			range = 1100,
			type = "linear",
			delay = 0.25,
			slotName = "Q",
			aoe = false,
			cc = true,
			speed = 2000,
			slot = 0,
			hitbox = true,
			collision = true
		},
		NautilusSplashZone = {
			charName = "Nautilus",
			radius = 600,
			range = 350,
			type = "circular",
			delay = 0.25,
			slotName = "E",
			aoe = true,
			cc = true,
			slot = 2,
			hitbox = false,
			collision = false,
			speed = math.huge
		},
		JavelinToss = {
			charName = "Nidalee",
			radius = 45,
			range = 1500,
			type = "linear",
			delay = 0.25,
			slotName = "Q",
			aoe = true,
			cc = false,
			speed = 1300,
			slot = 0,
			hitbox = true,
			collision = true
		},
		Bushwhack = {
			charName = "Nidalee",
			radius = 85,
			range = 900,
			type = "circular",
			delay = 0.25,
			slotName = "W",
			aoe = false,
			cc = false,
			slot = 1,
			hitbox = true,
			collision = true,
			speed = math.huge
		},
		Pounce = {
			charName = "Nidalee",
			radius = 200,
			range = 750,
			type = "circular",
			delay = 0.25,
			slotName = "W",
			aoe = true,
			cc = false,
			speed = 1750,
			slot = 1,
			hitbox = true,
			collision = false
		},
		Swipe = {
			charName = "Nidalee",
			range = 300,
			aoe = true,
			type = "conic",
			delay = 0.25,
			slotName = "E",
			collision = false,
			cc = false,
			slot = 2,
			hitbox = false,
			angle = 180,
			speed = math.huge
		},
		NocturneDuskbringer = {
			charName = "Nocturne",
			radius = 60,
			range = 1200,
			type = "linear",
			delay = 0.25,
			slotName = "Q",
			aoe = true,
			cc = false,
			speed = 1400,
			slot = 0,
			hitbox = true,
			collision = false
		},
		AbsoluteZero = {
			charName = "Nunu",
			radius = 650,
			range = 800,
			type = "circular",
			delay = 3.01,
			slotName = "R",
			aoe = true,
			cc = true,
			slot = 3,
			hitbox = false,
			collision = false,
			speed = math.huge
		},
		OlafAxeThrowCast = {
			charName = "Olaf",
			radius = 80,
			range = 1000,
			type = "linear",
			delay = 0.25,
			slotName = "Q",
			aoe = true,
			cc = true,
			speed = 1550,
			slot = 0,
			hitbox = true,
			collision = false
		},
		OrianaIzunaCommand = {
			charName = "Orianna",
			radius = 175,
			range = 825,
			type = "linear",
			delay = 0.25,
			slotName = "Q",
			aoe = true,
			cc = false,
			speed = 1400,
			slot = 0,
			hitbox = true,
			collision = false
		},
		OrianaDissonanceCommand = {
			charName = "Orianna",
			radius = 250,
			range = 355,
			type = "circular",
			delay = 0.25,
			slotName = "W",
			aoe = true,
			cc = true,
			slot = 1,
			hitbox = false,
			collision = false,
			speed = math.huge
		},
		OrianaRedactCommand = {
			charName = "Orianna",
			radius = 55,
			range = 1100,
			type = "linear",
			delay = 0.25,
			slotName = "E",
			aoe = true,
			cc = false,
			speed = 1400,
			slot = 2,
			hitbox = true,
			collision = false
		},
		OrianaDetonateCommand = {
			charName = "Orianna",
			radius = 325,
			range = 300,
			type = "circular",
			delay = 0.5,
			slotName = "R",
			aoe = true,
			cc = true,
			slot = 3,
			hitbox = false,
			collision = false,
			speed = math.huge
		},
		OrnnQ = {
			charName = "Ornn",
			radius = 100,
			range = 800,
			type = "linear",
			delay = 0.3,
			slotName = "Q",
			aoe = true,
			cc = true,
			speed = 2000,
			slot = 0,
			hitbox = true,
			collision = false
		},
		OrnnW = {
			charName = "Ornn",
			radius = 110,
			range = 550,
			type = "linear",
			delay = 0.25,
			slotName = "W",
			aoe = true,
			cc = false,
			slot = 1,
			hitbox = true,
			collision = false,
			speed = math.huge
		},
		OrnnE = {
			charName = "Ornn",
			radius = 150,
			range = 800,
			type = "linear",
			delay = 0.35,
			slotName = "E",
			aoe = true,
			cc = true,
			speed = 1780,
			slot = 2,
			hitbox = true,
			collision = false
		},
		OrnnR = {
			charName = "Ornn",
			radius = 225,
			range = 2500,
			type = "linear",
			delay = 0.5,
			slotName = "R",
			aoe = true,
			cc = true,
			speed = 1200,
			slot = 3,
			hitbox = true,
			collision = false
		},
		PantheonE = {
			charName = "Pantheon",
			range = 325,
			aoe = true,
			type = "conic",
			delay = 0.389,
			slotName = "E",
			collision = false,
			cc = false,
			slot = 2,
			hitbox = false,
			angle = 80,
			speed = math.huge
		},
		PantheonRFall = {
			charName = "Pantheon",
			range = 5500,
			aoe = true,
			type = "circular",
			delay = 2.5,
			slotName = "R",
			collision = false,
			cc = true,
			slot = 3,
			hitbox = true,
			angle = 700,
			speed = math.huge
		},
		PoppyQSpell = {
			charName = "Poppy",
			radius = 85,
			range = 430,
			type = "linear",
			delay = 1.32,
			slotName = "Q",
			aoe = true,
			cc = true,
			slot = 0,
			hitbox = true,
			collision = false,
			speed = math.huge
		},
		PoppyW = {
			charName = "Poppy",
			radius = 400,
			range = 325,
			type = "circular",
			delay = 0,
			slotName = "W",
			aoe = true,
			cc = false,
			slot = 1,
			hitbox = false,
			collision = false,
			speed = math.huge
		},
		PoppyRSpell = {
			charName = "Poppy",
			radius = 80,
			range = 1900,
			type = "linear",
			delay = 0.6,
			slotName = "R",
			aoe = true,
			cc = true,
			speed = 1600,
			slot = 3,
			hitbox = true,
			collision = false
		},
		QuinnQ = {
			charName = "Quinn",
			radius = 50,
			range = 1025,
			type = "linear",
			delay = 0.25,
			slotName = "Q",
			aoe = false,
			cc = false,
			speed = 1550,
			slot = 0,
			hitbox = true,
			collision = true
		},
		RakanQ = {
			charName = "Rakan",
			radius = 60,
			range = 900,
			type = "linear",
			delay = 0.25,
			slotName = "Q",
			aoe = false,
			cc = false,
			speed = 1800,
			slot = 0,
			hitbox = true,
			collision = true
		},
		RakanW = {
			charName = "Rakan",
			radius = 250,
			range = 600,
			type = "circular",
			delay = 0,
			slotName = "W",
			aoe = true,
			cc = false,
			speed = 2150,
			slot = 1,
			hitbox = true,
			collision = false
		},
		RakanWCast = {
			charName = "Rakan",
			radius = 250,
			range = 325,
			type = "circular",
			delay = 0.5,
			slotName = "W",
			aoe = true,
			cc = true,
			slot = 1,
			hitbox = false,
			collision = false,
			speed = math.huge
		},
		Tremors2 = {
			charName = "Rammus",
			radius = 300,
			range = 300,
			type = "circular",
			delay = 0.25,
			slotName = "R",
			aoe = true,
			cc = true,
			slot = 3,
			hitbox = false,
			collision = false,
			speed = math.huge
		},
		RekSaiQBurrowed = {
			charName = "RekSai",
			radius = 50,
			range = 1650,
			type = "linear",
			delay = 0.125,
			slotName = "Q",
			aoe = false,
			cc = false,
			speed = 2100,
			slot = 0,
			hitbox = true,
			collision = true
		},
		RenektonCleave = {
			charName = "Renekton",
			radius = 325,
			range = 333,
			type = "circular",
			delay = 0.25,
			slotName = "Q",
			aoe = true,
			cc = false,
			slot = 0,
			hitbox = false,
			collision = false,
			speed = math.huge
		},
		RenektonSliceAndDice = {
			charName = "Renekton",
			radius = 45,
			range = 450,
			type = "linear",
			delay = 0.25,
			slotName = "E",
			aoe = true,
			cc = false,
			speed = 1125,
			slot = 2,
			hitbox = true,
			collision = false
		},
		RengarQ = {
			charName = "Rengar",
			radius = 50,
			range = 450,
			type = "linear",
			delay = 0.325,
			slotName = "Q",
			aoe = true,
			cc = false,
			slot = 0,
			hitbox = true,
			collision = false,
			speed = math.huge
		},
		RengarW = {
			charName = "Rengar",
			radius = 450,
			range = 344,
			type = "circular",
			delay = 0.25,
			slotName = "W",
			aoe = true,
			cc = false,
			slot = 1,
			hitbox = false,
			collision = false,
			speed = math.huge
		},
		RengarE = {
			charName = "Rengar",
			radius = 60,
			range = 1000,
			type = "linear",
			delay = 0.25,
			slotName = "E",
			aoe = false,
			cc = true,
			speed = 1500,
			slot = 2,
			hitbox = true,
			collision = true
		},
		RivenTriCleave = {
			charName = "Riven",
			radius = 200,
			range = 260,
			type = "linear",
			delay = 0.25,
			slotName = "Q",
			aoe = true,
			cc = true,
			speed = 1100,
			slot = 0,
			hitbox = true,
			collision = false
		},
		RivenMartyr = {
			charName = "Riven",
			radius = 135,
			range = 322,
			type = "circular",
			delay = 0.267,
			slotName = "W",
			aoe = true,
			cc = true,
			slot = 1,
			hitbox = false,
			collision = false,
			speed = math.huge
		},
		RivenFeint = {
			charName = "Riven",
			radius = 100,
			range = 325,
			type = "linear",
			delay = 0,
			slotName = "E",
			aoe = false,
			cc = false,
			speed = 1100,
			slot = 2,
			hitbox = true,
			collision = false
		},
		RivenIzunaBlade = {
			charName = "Riven",
			range = 900,
			aoe = true,
			type = "conic",
			delay = 0.25,
			slotName = "R",
			collision = false,
			cc = false,
			speed = 1600,
			slot = 3,
			hitbox = true,
			angle = 50
		},
		RumbleGrenade = {
			charName = "Rumble",
			radius = 70,
			range = 850,
			type = "linear",
			delay = 0.25,
			slotName = "E",
			aoe = false,
			cc = true,
			speed = 2000,
			slot = 2,
			hitbox = true,
			collision = true
		},
		RumbleCarpetBombDummy = {
			charName = "Rumble",
			radius = 130,
			range = 1700,
			type = "linear",
			delay = 0.583,
			slotName = "R",
			aoe = true,
			cc = true,
			speed = 1600,
			slot = 3,
			hitbox = true,
			collision = false
		},
		RyzeQ = {
			charName = "Ryze",
			radius = 50,
			range = 1000,
			type = "linear",
			delay = 0.25,
			slotName = "Q",
			aoe = false,
			cc = false,
			speed = 1700,
			slot = 0,
			hitbox = true,
			collision = true
		},
		SejuaniQ = {
			charName = "Sejuani",
			radius = 150,
			range = 650,
			type = "linear",
			delay = 0.25,
			slotName = "Q",
			aoe = true,
			cc = true,
			speed = 1300,
			slot = 0,
			hitbox = true,
			collision = false
		},
		SejuaniW = {
			charName = "Sejuani",
			range = 600,
			aoe = true,
			type = "conic",
			delay = 0.25,
			slotName = "W",
			collision = false,
			cc = true,
			slot = 1,
			hitbox = false,
			angle = 75,
			speed = math.huge
		},
		SejuaniWDummy = {
			charName = "Sejuani",
			radius = 65,
			range = 600,
			type = "linear",
			delay = 1,
			slotName = "W",
			aoe = false,
			cc = true,
			slot = 1,
			hitbox = true,
			collision = false,
			speed = math.huge
		},
		SejuaniR = {
			charName = "Sejuani",
			radius = 100,
			range = 1300,
			type = "linear",
			delay = 0.25,
			slotName = "R",
			aoe = true,
			cc = true,
			speed = 1650,
			slot = 3,
			hitbox = true,
			collision = false
		},
		ShenE = {
			charName = "Shen",
			radius = 60,
			range = 600,
			type = "linear",
			delay = 0,
			slotName = "E",
			aoe = true,
			cc = true,
			speed = 1200,
			slot = 2,
			hitbox = true,
			collision = false
		},
		ShyvanaFireball = {
			charName = "Shyvana",
			radius = 60,
			range = 925,
			type = "linear",
			delay = 0.25,
			slotName = "E",
			aoe = true,
			cc = false,
			speed = 1575,
			slot = 2,
			hitbox = true,
			collision = false
		},
		ShyvanaTransformLeap = {
			charName = "Shyvana",
			radius = 160,
			range = 850,
			type = "linear",
			delay = 0.25,
			slotName = "R",
			aoe = true,
			cc = true,
			speed = 1130,
			slot = 3,
			hitbox = true,
			collision = false
		},
		ShyvanaFireballDragon2 = {
			charName = "Shyvana",
			radius = 60,
			range = 925,
			type = "linear",
			delay = 0.333,
			slotName = "E",
			aoe = true,
			cc = false,
			speed = 1575,
			slot = 2,
			hitbox = true,
			collision = false
		},
		MegaAdhesive = {
			charName = "Singed",
			radius = 265,
			range = 1000,
			type = "circular",
			delay = 0.25,
			slotName = "W",
			aoe = true,
			cc = true,
			slot = 1,
			hitbox = true,
			collision = false,
			speed = math.huge
		},
		SionQ = {
			charName = "Sion",
			radius = 300,
			range = 600,
			type = "linear",
			delay = 0,
			slotName = "Q",
			aoe = true,
			cc = true,
			slot = 0,
			hitbox = false,
			collision = false,
			speed = math.huge
		},
		SionE = {
			charName = "Sion",
			radius = 80,
			range = 725,
			type = "linear",
			delay = 0.25,
			slotName = "E",
			aoe = true,
			cc = true,
			speed = 1900,
			slot = 2,
			hitbox = false,
			collision = false
		},
		SionR = {
			charName = "Sion",
			radius = 200,
			range = 7500,
			type = "linear",
			delay = 0.125,
			slotName = "R",
			aoe = true,
			cc = true,
			speed = 950,
			slot = 3,
			hitbox = false,
			collision = false
		},
		SivirQ = {
			charName = "Sivir",
			radius = 75,
			range = 1250,
			type = "linear",
			delay = 0.25,
			slotName = "Q",
			aoe = true,
			cc = false,
			speed = 1350,
			slot = 0,
			hitbox = true,
			collision = false
		},
		SivirQReturn = {
			charName = "Sivir",
			radius = 75,
			range = 1250,
			type = "linear",
			delay = 0,
			slotName = "Q",
			aoe = true,
			cc = false,
			speed = 1350,
			slot = 0,
			hitbox = true,
			collision = false
		},
		SkarnerVirulentSlash = {
			charName = "Skarner",
			radius = 350,
			range = 322,
			type = "circular",
			delay = 0.25,
			slotName = "Q",
			aoe = true,
			cc = false,
			slot = 0,
			hitbox = false,
			collision = false,
			speed = math.huge
		},
		SkarnerFracture = {
			charName = "Skarner",
			radius = 70,
			range = 1000,
			type = "linear",
			delay = 0.25,
			slotName = "E",
			aoe = true,
			cc = true,
			speed = 1500,
			slot = 2,
			hitbox = true,
			collision = false
		},
		SonaR = {
			charName = "Sona",
			radius = 120,
			range = 900,
			type = "linear",
			delay = 0.25,
			slotName = "R",
			aoe = true,
			cc = true,
			speed = 2250,
			slot = 3,
			hitbox = true,
			collision = false
		},
		SorakaQ = {
			charName = "Soraka",
			radius = 235,
			range = 800,
			type = "circular",
			delay = 0.25,
			slotName = "Q",
			aoe = true,
			cc = true,
			speed = 1150,
			slot = 0,
			hitbox = true,
			collision = false
		},
		SorakaE = {
			charName = "Soraka",
			radius = 300,
			range = 925,
			type = "circular",
			delay = 1.5,
			slotName = "E",
			aoe = true,
			cc = true,
			slot = 2,
			hitbox = true,
			collision = false,
			speed = math.huge
		},
		SwainQ = {
			charName = "Swain",
			range = 725,
			aoe = true,
			type = "conic",
			delay = 0.25,
			slotName = "Q",
			collision = false,
			cc = false,
			slot = 0,
			hitbox = false,
			angle = 45,
			speed = math.huge
		},
		SwainW = {
			charName = "Swain",
			radius = 260,
			range = 3500,
			type = "circular",
			delay = 1.5,
			slotName = "W",
			aoe = true,
			cc = false,
			slot = 1,
			hitbox = false,
			collision = false,
			speed = math.huge
		},
		SwainE = {
			charName = "Swain",
			radius = 65,
			range = 850,
			type = "linear",
			delay = 0.25,
			slotName = "E",
			aoe = true,
			cc = true,
			speed = 1550,
			slot = 2,
			hitbox = true,
			collision = false
		},
		SwainR = {
			charName = "Swain",
			radius = 650,
			range = 500,
			type = "circular",
			delay = 0.5,
			slotName = "R",
			aoe = true,
			cc = true,
			slot = 3,
			hitbox = false,
			collision = false,
			speed = math.huge
		},
		SyndraQ = {
			charName = "Syndra",
			radius = 200,
			range = 800,
			type = "circular",
			delay = 0.625,
			slotName = "Q",
			aoe = true,
			cc = false,
			slot = 0,
			hitbox = true,
			collision = false,
			speed = math.huge
		},
		SyndraWCast = {
			charName = "Syndra",
			radius = 225,
			range = 950,
			type = "circular",
			delay = 0.25,
			slotName = "W",
			aoe = true,
			cc = true,
			speed = 1450,
			slot = 1,
			hitbox = true,
			collision = false
		},
		SyndraE = {
			charName = "Syndra",
			range = 700,
			aoe = true,
			type = "conic",
			delay = 0.25,
			slotName = "E",
			collision = false,
			cc = true,
			speed = 2500,
			slot = 2,
			hitbox = false,
			angle = 40
		},
		SyndraEMissile = {
			charName = "Syndra",
			radius = 50,
			range = 1250,
			type = "linear",
			delay = 0.25,
			slotName = "E",
			aoe = true,
			cc = true,
			speed = 1600,
			slot = 2,
			hitbox = true,
			collision = false
		},
		TahmKenchQ = {
			charName = "TahmKench",
			radius = 70,
			range = 800,
			type = "linear",
			delay = 0.25,
			slotName = "Q",
			aoe = false,
			cc = true,
			speed = 2670,
			slot = 0,
			hitbox = true,
			collision = true
		},
		TaliyahQ = {
			charName = "Taliyah",
			radius = 70,
			range = 1000,
			type = "linear",
			delay = 0.25,
			slotName = "Q",
			aoe = false,
			cc = false,
			speed = 2850,
			slot = 0,
			hitbox = true,
			collision = true
		},
		TaliyahWVC = {
			charName = "Taliyah",
			radius = 150,
			range = 900,
			type = "circular",
			delay = 0.6,
			slotName = "W",
			aoe = true,
			cc = true,
			slot = 1,
			hitbox = true,
			collision = false,
			speed = math.huge
		},
		TaliyahE = {
			charName = "Taliyah",
			range = 800,
			aoe = true,
			type = "conic",
			delay = 0.25,
			slotName = "E",
			collision = false,
			cc = true,
			speed = 2000,
			slot = 2,
			hitbox = true,
			angle = 80
		},
		TalonW = {
			charName = "Talon",
			range = 650,
			aoe = true,
			type = "conic",
			delay = 0.25,
			slotName = "W",
			collision = false,
			cc = true,
			speed = 1850,
			slot = 1,
			hitbox = true,
			angle = 35
		},
		TalonR = {
			charName = "Talon",
			radius = 550,
			range = 555,
			type = "circular",
			delay = 0.25,
			slotName = "R",
			aoe = true,
			cc = false,
			slot = 3,
			hitbox = false,
			collision = false,
			speed = math.huge
		},
		TaricE = {
			charName = "Taric",
			radius = 70,
			range = 575,
			type = "linear",
			delay = 1,
			slotName = "E",
			aoe = true,
			cc = true,
			slot = 2,
			hitbox = true,
			collision = false,
			speed = math.huge
		},
		TeemoRCast = {
			charName = "Teemo",
			radius = 200,
			range = 900,
			type = "circular",
			delay = 1.25,
			slotName = "R",
			aoe = true,
			cc = true,
			slot = 3,
			hitbox = true,
			collision = false,
			speed = math.huge
		},
		ThreshQ = {
			charName = "Thresh",
			radius = 55,
			range = 1100,
			type = "linear",
			delay = 0.5,
			slotName = "Q",
			aoe = false,
			cc = true,
			speed = 1900,
			slot = 0,
			hitbox = true,
			collision = true
		},
		ThreshE = {
			charName = "Thresh",
			radius = 95,
			range = 400,
			type = "linear",
			delay = 0.389,
			slotName = "E",
			aoe = true,
			cc = true,
			slot = 2,
			hitbox = false,
			collision = false,
			speed = math.huge
		},
		ThreshRPenta = {
			charName = "Thresh",
			radius = 450,
			range = 400,
			type = "pentagon",
			delay = 0.45,
			slotName = "R",
			aoe = true,
			cc = true,
			slot = 3,
			hitbox = false,
			collision = false,
			speed = math.huge
		},
		TristanaW = {
			charName = "Tristana",
			radius = 250,
			range = 900,
			type = "circular",
			delay = 0.25,
			slotName = "W",
			aoe = true,
			cc = true,
			speed = 1100,
			slot = 1,
			hitbox = true,
			collision = false
		},
		trundledesecrate = {
			charName = "Trundle",
			radius = 1000,
			range = 1000,
			type = "circular",
			delay = 0.25,
			slotName = "W",
			aoe = false,
			cc = false,
			slot = 1,
			hitbox = false,
			collision = false,
			speed = math.huge
		},
		TrundleCircle = {
			charName = "Trundle",
			radius = 375,
			range = 1000,
			type = "circular",
			delay = 0.25,
			slotName = "E",
			aoe = true,
			cc = true,
			slot = 2,
			hitbox = true,
			collision = false,
			speed = math.huge
		},
		TryndamereE = {
			charName = "Tryndamere",
			radius = 225,
			range = 660,
			type = "linear",
			delay = 0,
			slotName = "E",
			aoe = true,
			cc = false,
			speed = 1300,
			slot = 2,
			hitbox = true,
			collision = false
		},
		WildCards = {
			charName = "TwistedFate",
			radius = 35,
			range = 1450,
			type = "linear",
			delay = 0.25,
			slotName = "Q",
			aoe = true,
			cc = false,
			speed = 1000,
			slot = 0,
			hitbox = true,
			collision = false
		},
		TwitchVenomCask = {
			charName = "Twitch",
			radius = 340,
			range = 950,
			type = "circular",
			delay = 0.25,
			slotName = "W",
			aoe = true,
			cc = true,
			speed = 1400,
			slot = 1,
			hitbox = true,
			collision = false
		},
		UrgotQ = {
			charName = "Urgot",
			radius = 215,
			range = 800,
			type = "circular",
			delay = 0.6,
			slotName = "Q",
			aoe = true,
			cc = true,
			slot = 0,
			hitbox = true,
			collision = false,
			speed = math.huge
		},
		UrgotE = {
			charName = "Urgot",
			radius = 100,
			range = 475,
			type = "linear",
			delay = 0.45,
			slotName = "E",
			aoe = true,
			cc = true,
			speed = 1050,
			slot = 2,
			hitbox = true,
			collision = false
		},
		UrgotR = {
			charName = "Urgot",
			radius = 70,
			range = 1600,
			type = "linear",
			delay = 0.4,
			slotName = "R",
			aoe = false,
			cc = true,
			speed = 3200,
			slot = 3,
			hitbox = true,
			collision = false
		},
		VarusQ = {
			charName = "Varus",
			radius = 40,
			range = 1625,
			type = "linear",
			delay = 0,
			slotName = "Q",
			aoe = true,
			cc = false,
			speed = 1850,
			slot = 0,
			hitbox = true,
			collision = false
		},
		VarusE = {
			charName = "Varus",
			radius = 280,
			range = 925,
			type = "circular",
			delay = 0.242,
			slotName = "E",
			aoe = true,
			cc = true,
			speed = 1500,
			slot = 2,
			hitbox = true,
			collision = false
		},
		VarusR = {
			charName = "Varus",
			radius = 120,
			range = 1075,
			type = "linear",
			delay = 0.242,
			slotName = "R",
			aoe = true,
			cc = true,
			speed = 1850,
			slot = 3,
			hitbox = true,
			collision = false
		},
		VayneTumble = {
			charName = "Vayne",
			radius = 45,
			range = 300,
			type = "linear",
			delay = 0.25,
			slotName = "Q",
			aoe = false,
			cc = false,
			speed = 900,
			slot = 0,
			hitbox = true,
			collision = false
		},
		VeigarBalefulStrike = {
			charName = "Veigar",
			radius = 60,
			range = 950,
			type = "linear",
			delay = 0.25,
			slotName = "Q",
			aoe = true,
			cc = false,
			speed = 2000,
			slot = 0,
			hitbox = true,
			collision = true
		},
		VeigarDarkMatter = {
			charName = "Veigar",
			radius = 225,
			range = 900,
			type = "circular",
			delay = 1.25,
			slotName = "W",
			aoe = true,
			cc = false,
			slot = 1,
			hitbox = true,
			collision = false,
			speed = math.huge
		},
		VeigarEventHorizon = {
			charName = "Veigar",
			radius = 375,
			range = 700,
			type = "circular",
			delay = 0.75,
			slotName = "E",
			aoe = true,
			cc = true,
			slot = 2,
			hitbox = true,
			collision = false,
			speed = math.huge
		},
		VelkozQ = {
			charName = "Velkoz",
			radius = 55,
			range = 1050,
			type = "linear",
			delay = 0.251,
			slotName = "Q",
			aoe = false,
			cc = true,
			speed = 1235,
			slot = 0,
			hitbox = true,
			collision = true
		},
		VelkozQMissileSplit = {
			charName = "VelKoz",
			radius = 45,
			range = 1050,
			type = "linear",
			delay = 0.251,
			slotName = "Q",
			aoe = false,
			cc = true,
			speed = 2100,
			slot = 0,
			hitbox = true,
			collision = true
		},
		VelkozW = {
			charName = "Velkoz",
			radius = 80,
			range = 1050,
			type = "linear",
			delay = 0.25,
			slotName = "W",
			aoe = true,
			cc = false,
			speed = 1500,
			slot = 1,
			hitbox = true,
			collision = false
		},
		VelkozE = {
			charName = "Velkoz",
			radius = 235,
			range = 850,
			type = "circular",
			delay = 0.75,
			slotName = "E",
			aoe = true,
			cc = true,
			slot = 2,
			hitbox = true,
			collision = false,
			speed = math.huge
		},
		VelkozR = {
			charName = "Velkoz",
			radius = 75,
			range = 1550,
			type = "linear",
			delay = 0.25,
			slotName = "R",
			aoe = true,
			cc = false,
			slot = 3,
			hitbox = true,
			collision = false,
			speed = math.huge
		},
		ViQ = {
			charName = "Vi",
			radius = 55,
			range = 725,
			type = "linear",
			delay = 0,
			slotName = "Q",
			aoe = true,
			cc = true,
			speed = 1400,
			slot = 0,
			hitbox = true,
			collision = false
		},
		ViktorGravitonField = {
			charName = "Viktor",
			radius = 290,
			range = 700,
			type = "circular",
			delay = 1.333,
			slotName = "W",
			aoe = true,
			cc = true,
			slot = 1,
			hitbox = true,
			collision = false,
			speed = math.huge
		},
		ViktorDeathRay = {
			charName = "Viktor",
			radius = 80,
			range = 1025,
			type = "linear",
			delay = 0,
			slotName = "E",
			aoe = true,
			cc = false,
			speed = 1350,
			slot = 2,
			hitbox = true,
			collision = false
		},
		ViktorChaosStorm = {
			charName = "Viktor",
			radius = 290,
			range = 700,
			type = "circular",
			delay = 0.25,
			slotName = "R",
			aoe = true,
			cc = false,
			slot = 3,
			hitbox = true,
			collision = false,
			speed = math.huge
		},
		VladimirSanguinePool = {
			charName = "Vladimir",
			radius = 300,
			range = 340,
			type = "circular",
			delay = 0.25,
			slotName = "W",
			aoe = true,
			cc = true,
			slot = 1,
			hitbox = false,
			collision = false,
			speed = math.huge
		},
		VladimirE = {
			charName = "Vladimir",
			radius = 600,
			range = 340,
			type = "circular",
			delay = 0,
			slotName = "E",
			aoe = true,
			cc = true,
			slot = 2,
			hitbox = false,
			collision = true,
			speed = math.huge
		},
		VladimirHemoplague = {
			charName = "Vladimir",
			radius = 350,
			range = 700,
			type = "circular",
			delay = 0.389,
			slotName = "R",
			aoe = true,
			cc = false,
			slot = 3,
			hitbox = true,
			collision = true,
			speed = math.huge
		},
		WarwickR = {
			charName = "Warwick",
			radius = 45,
			range = 1000,
			type = "linear",
			delay = 0.1,
			slotName = "R",
			aoe = true,
			cc = false,
			speed = 1800,
			slot = 3,
			hitbox = true,
			collision = false
		},
		MonkeyKingSpinToWin = {
			charName = "MonkeyKing",
			radius = 325,
			range = 400,
			type = "circular",
			delay = 0,
			slotName = "R",
			aoe = true,
			cc = true,
			slot = 3,
			hitbox = false,
			collision = false,
			speed = math.huge
		},
		XayahQ = {
			charName = "Xayah",
			radius = 45,
			range = 1100,
			type = "linear",
			delay = 0.5,
			slotName = "Q",
			aoe = true,
			cc = false,
			speed = 2075,
			slot = 0,
			hitbox = true,
			collision = false
		},
		XayahE = {
			charName = "Xayah",
			radius = 45,
			range = 2000,
			type = "linear",
			delay = 0,
			slotName = "E",
			aoe = true,
			cc = false,
			speed = 5700,
			slot = 2,
			hitbox = true,
			collision = false
		},
		XayahR = {
			charName = "Xayah",
			range = 1100,
			aoe = true,
			type = "conic",
			delay = 1.5,
			slotName = "R",
			collision = false,
			cc = false,
			speed = 4400,
			slot = 3,
			hitbox = false,
			angle = 40
		},
		XerathArcanopulse2 = {
			charName = "Xerath",
			radius = 75,
			range = 1400,
			type = "linear",
			delay = 0.5,
			slotName = "Q",
			aoe = true,
			cc = false,
			slot = 0,
			hitbox = false,
			collision = false,
			speed = math.huge
		},
		XerathArcaneBarrage2 = {
			charName = "Xerath",
			radius = 235,
			range = 1100,
			type = "circular",
			delay = 0.5,
			slotName = "W",
			aoe = true,
			cc = true,
			slot = 1,
			hitbox = true,
			collision = false,
			speed = math.huge
		},
		XerathMageSpear = {
			charName = "Xerath",
			radius = 60,
			range = 1050,
			type = "linear",
			delay = 0.25,
			slotName = "E",
			aoe = false,
			cc = true,
			speed = 1350,
			slot = 2,
			hitbox = true,
			collision = true
		},
		XerathRMissileWrapper = {
			charName = "Xerath",
			radius = 200,
			range = 6160,
			type = "circular",
			delay = 0.6,
			slotName = "R",
			aoe = true,
			cc = false,
			slot = 3,
			hitbox = true,
			collision = false,
			speed = math.huge
		},
		XinZhaoW = {
			charName = "XinZhao",
			range = 125,
			aoe = true,
			type = "conic",
			delay = 0,
			slotName = "W",
			collision = false,
			cc = false,
			slot = 1,
			hitbox = false,
			angle = 180,
			speed = math.huge
		},
		XinZhaoW = {
			charName = "XinZhao",
			radius = 45,
			range = 900,
			type = "linear",
			delay = 0.6,
			slotName = "W",
			aoe = true,
			cc = true,
			slot = 1,
			hitbox = true,
			collision = false,
			speed = math.huge
		},
		XinZhaoR = {
			charName = "XinZhao",
			radius = 550,
			range = 355,
			type = "circular",
			delay = 0.325,
			slotName = "R",
			aoe = true,
			cc = true,
			slot = 3,
			hitbox = false,
			collision = false,
			speed = math.huge
		},
		YasuoQW = {
			charName = "Yasuo",
			radius = 40,
			range = 475,
			type = "linear",
			delay = 0.339,
			slotName = "Q",
			cc = false,
			aoe = true,
			slot = 0,
			hitbox = true,
			collision = false,
			speeds = math.huge
		},
		YasuoQ2W = {
			charName = "Yasuo",
			radius = 40,
			range = 475,
			type = "linear",
			delay = 0.339,
			slotName = "Q",
			cc = false,
			aoe = true,
			slot = 0,
			hitbox = true,
			collision = false,
			speeds = math.huge
		},
		YasuoQ3 = {
			charName = "Yasuo",
			radius = 90,
			range = 1000,
			type = "linear",
			delay = 0.339,
			slotName = "Q",
			cc = true,
			aoe = true,
			slot = 0,
			hitbox = true,
			speeds = 1200,
			collision = false
		},
		YorickW = {
			charName = "Yorick",
			radius = 300,
			range = 600,
			type = "circular",
			delay = 0.25,
			slotName = "W",
			aoe = true,
			cc = true,
			slot = 1,
			hitbox = true,
			collision = false,
			speed = math.huge
		},
		YorickE = {
			charName = "Yorick",
			range = 700,
			aoe = true,
			type = "conic",
			delay = 0.33,
			slotName = "E",
			collision = false,
			cc = true,
			speed = 2100,
			slot = 2,
			hitbox = true,
			angle = 25
		},
		ZacQ = {
			charName = "Zac",
			radius = 85,
			range = 800,
			type = "linear",
			delay = 0.33,
			slotName = "Q",
			aoe = true,
			cc = true,
			slot = 0,
			hitbox = true,
			collision = true,
			speed = math.huge
		},
		ZacW = {
			charName = "Zac",
			radius = 350,
			range = 300,
			type = "circular",
			delay = 0.25,
			slotName = "W",
			aoe = true,
			cc = false,
			slot = 1,
			hitbox = false,
			collision = false,
			speed = math.huge
		},
		ZacE = {
			charName = "Zac",
			radius = 300,
			range = 1800,
			type = "circular",
			delay = 0,
			slotName = "E",
			aoe = true,
			cc = true,
			speed = 1330,
			slot = 2,
			hitbox = false,
			collision = false
		},
		ZacR = {
			charName = "Zac",
			radius = 300,
			range = 1000,
			type = "circular",
			delay = 0,
			slotName = "R",
			aoe = true,
			cc = true,
			slot = 3,
			hitbox = false,
			collision = false,
			speed = math.huge
		},
		ZedQ = {
			charName = "Zed",
			radius = 50,
			range = 900,
			type = "linear",
			delay = 0.25,
			slotName = "Q",
			aoe = true,
			cc = false,
			speed = 1700,
			slot = 0,
			hitbox = true,
			collision = false
		},
		ZedW = {
			charName = "Zed",
			radius = 40,
			range = 650,
			type = "linear",
			delay = 0.25,
			slotName = "W",
			aoe = true,
			cc = false,
			speed = 1750,
			slot = 1,
			hitbox = true,
			collision = false
		},
		ZedE = {
			charName = "Zed",
			radius = 290,
			range = 300,
			type = "circular",
			delay = 0.25,
			slotName = "E",
			aoe = true,
			cc = true,
			slot = 2,
			hitbox = false,
			collision = false,
			speed = math.huge
		},
		ZiggsQ = {
			charName = "Ziggs",
			radius = 180,
			range = 1400,
			type = "circular",
			delay = 0.25,
			slotName = "Q",
			aoe = true,
			cc = false,
			slot = 0,
			hitbox = true,
			collision = false,
			speed = math.huge
		},
		ZiggsW = {
			charName = "Ziggs",
			radius = 325,
			range = 1000,
			type = "circular",
			delay = 0.25,
			slotName = "W",
			aoe = true,
			cc = true,
			speed = 2000,
			slot = 1,
			hitbox = true,
			collision = false
		},
		ZiggsE = {
			charName = "Ziggs",
			radius = 325,
			range = 900,
			type = "circular",
			delay = 0.25,
			slotName = "E",
			aoe = true,
			cc = true,
			slot = 2,
			hitbox = true,
			collision = false,
			speed = math.huge
		},
		ZiggsR = {
			charName = "Ziggs",
			radius = 550,
			range = 5300,
			type = "circular",
			delay = 0.375,
			slotName = "R",
			aoe = true,
			cc = false,
			slot = 3,
			hitbox = true,
			collision = false,
			speed = math.huge
		},
		ZileanQ = {
			charName = "Zilean",
			radius = 180,
			range = 900,
			type = "circular",
			delay = 0.25,
			slotName = "Q",
			aoe = true,
			cc = true,
			speed = 2050,
			slot = 0,
			hitbox = true,
			collision = false
		},
		ZileanQAttachAudio = {
			charName = "Zilean",
			radius = 180,
			range = 900,
			type = "circular",
			delay = 0.25,
			slotName = "Q",
			aoe = true,
			cc = true,
			speed = 2050,
			slot = 0,
			hitbox = true,
			collision = false
		},
		ZoeQMissile = {
			charName = "Zoe",
			radius = 40,
			range = 800,
			type = "linear",
			delay = 0.25,
			slotName = "Q",
			aoe = false,
			cc = false,
			speed = 2500,
			slot = 0,
			hitbox = true,
			collision = true
		},
		ZoeQMis2 = {
			charName = "Zoe",
			radius = 40,
			range = 1600,
			type = "linear",
			delay = 0,
			slotName = "Q",
			aoe = false,
			cc = false,
			speed = 2370,
			slot = 0,
			hitbox = true,
			collision = true
		},
		ZoeE = {
			charName = "Zoe",
			radius = 55,
			range = 800,
			type = "linear",
			delay = 0.3,
			slotName = "E",
			aoe = false,
			cc = false,
			speed = 1950,
			slot = 2,
			hitbox = true,
			collision = true
		},
		ZoeR = {
			charName = "Zoe",
			radius = 100,
			range = 575,
			type = "circular",
			delay = 0.25,
			slotName = "R",
			aoe = false,
			cc = false,
			slot = 3,
			hitbox = true,
			collision = false,
			speed = math.huge
		},
		ZyraQ = {
			charName = "Zyra",
			range = 800,
			type = "rectangle",
			delay = 0.625,
			slotName = "Q",
			radius2 = 100,
			cc = false,
			radius1 = 400,
			slot = 0,
			hitbox = true,
			aoe = true,
			collision = false,
			speed = math.huge
		},
		ZyraW = {
			charName = "Zyra",
			radius = 50,
			range = 850,
			type = "circular",
			delay = 0.243,
			slotName = "W",
			aoe = false,
			cc = false,
			slot = 1,
			hitbox = true,
			collision = false,
			speed = math.huge
		},
		ZyraE = {
			charName = "Zyra",
			radius = 60,
			range = 1100,
			type = "linear",
			delay = 0.25,
			slotName = "E",
			aoe = true,
			cc = true,
			speed = 1150,
			slot = 2,
			hitbox = true,
			collision = false
		},
		ZyraR = {
			charName = "Zyra",
			radius = 575,
			range = 700,
			type = "circular",
			delay = 1.775,
			slotName = "R",
			aoe = true,
			cc = true,
			slot = 3,
			hitbox = true,
			collision = false,
			speed = math.huge
		},
		NeekoQ = {
			charName = "Neeko",
			radius = 225,
			range = 800,
			type = "circular",
			delay = 0.5,
			slotName = "Q",
			aoe = true,
			cc = false,
			speed = 2000,
			slot = 0,
			hitbox = true,
			collision = false
		},
		NeekoE = {
			charName = "Neeko",
			radius = 70,
			range = 800,
			type = "linear",
			delay = 0.5,
			slotName = "E",
			aoe = true,
			cc = true,
			speed = 1000,
			slot = 2,
			hitbox = true,
			collision = false
		},
		NeekoR = {
			charName = "Neeko",
			radius = 600,
			range = 600,
			type = "circular",
			delay = 2.5,
			slotName = "R",
			aoe = true,
			cc = true,
			slot = 3,
			hitbox = true,
			collision = false,
			speed = math.huge
		},
		SylasQ = {
			charName = "Sylas",
			radius = 70,
			range = 850,
			type = "linear",
			delay = 0.4,
			slotName = "Q",
			aoe = true,
			cc = false,
			speed = 1800,
			slot = 0,
			hitbox = true,
			collision = false
		},
		SylasE2 = {
			charName = "Sylas",
			radius = 60,
			range = 210,
			type = "linear",
			delay = 0.5,
			slotName = "E",
			aoe = false,
			cc = true,
			speed = 1600,
			slot = 2,
			hitbox = true,
			collision = true
		},
		YuumiQ = {
			charName = "Yuumi",
			radius = 65,
			range = 1150,
			type = "linear",
			delay = 0,
			slotName = "Q",
			aoe = false,
			cc = false,
			speed = 100,
			slot = 0,
			hitbox = true,
			collision = true
		},
		YuumiRMissile = {
			charName = "Yuumi",
			radius = 160,
			range = 1100,
			type = "linear",
			delay = 0.25,
			slotName = "R",
			aoe = true,
			cc = true,
			speed = 1900,
			slot = 3,
			hitbox = true,
			collision = false
		},
		QiyanaQ_Grass = {
			charName = "Qiyana",
			radius = 180,
			range = 500,
			type = "linear",
			delay = 0.25,
			slotName = "Q",
			aoe = true,
			cc = false,
			speed = 1600,
			slot = 0,
			hitbox = true,
			collision = false
		},
		QiyanaQ_Water = {
			charName = "Qiyana",
			radius = 180,
			range = 500,
			type = "linear",
			delay = 0.25,
			slotName = "Q",
			aoe = true,
			cc = true,
			speed = 1600,
			slot = 0,
			hitbox = true,
			collision = false
		},
		QiyanaQ_Rock = {
			charName = "Qiyana",
			radius = 180,
			range = 500,
			type = "linear",
			delay = 0.25,
			slotName = "Q",
			aoe = true,
			cc = false,
			speed = 1600,
			slot = 0,
			hitbox = true,
			collision = false
		},
		QiyanaR = {
			charName = "Qiyana",
			radius = 310,
			range = 1550,
			type = "linear",
			delay = 0.25,
			slotName = "R",
			aoe = true,
			cc = true,
			speed = 2000,
			slot = 3,
			hitbox = true,
			collision = false
		},
		SennaQ = {
			charName = "Senna",
			radius = 65,
			range = 600,
			type = "linear",
			delay = 0.24,
			slotName = "Q",
			aoe = true,
			cc = false,
			slot = 0,
			hitbox = true,
			collision = false,
			speed = math.huge
		},
		SennaW = {
			charName = "Senna",
			radius = 60,
			range = 1300,
			type = "linear",
			delay = 0.25,
			slotName = "W",
			aoe = true,
			cc = true,
			speed = 1200,
			slot = 1,
			hitbox = true,
			collision = true
		},
		SennaR = {
			charName = "Senna",
			radius = 300,
			range = 25000,
			type = "linear",
			delay = 0.25,
			slotName = "R",
			aoe = true,
			cc = false,
			speed = 20000,
			slot = 3,
			hitbox = true,
			collision = false
		},
		ApheliosCalibrumQ = {
			charName = "Aphelios",
			radius = 60,
			range = 1450,
			type = "linear",
			delay = 0.35,
			slotName = "Q",
			aoe = true,
			cc = false,
			speed = 1800,
			slot = 0,
			hitbox = true,
			collision = true
		},
		ApheliosInfernumQMis = {
			charName = "Aphelios",
			radius = 45,
			range = 600,
			type = "linear",
			delay = 0.4,
			slotName = "Q",
			aoe = true,
			cc = false,
			speed = 2050,
			slot = 0,
			hitbox = true,
			collision = false
		},
		ApheliosRMis = {
			charName = "Aphelios",
			radius = 125,
			range = 1500,
			type = "linear",
			delay = 0.5,
			slotName = "R",
			aoe = true,
			cc = false,
			speed = 2050,
			slot = 3,
			hitbox = true,
			collision = true
		},
		SettW = {
			charName = "Sett",
			radius = 210,
			range = 790,
			type = "linear",
			delay = 0.75,
			slotName = "W",
			aoe = true,
			cc = true,
			slot = 1,
			hitbox = true,
			collision = false,
			speed = math.huge
		},
		SettE = {
			charName = "Sett",
			radius = 350,
			range = 490,
			type = "linear",
			delay = 0.25,
			slotName = "E",
			aoe = true,
			cc = true,
			slot = 2,
			hitbox = true,
			collision = false,
			speed = math.huge
		},
		LilliaQ = {
			charName = "Lillia",
			radius = 485,
			range = 500,
			type = "circular",
			delay = 0.25,
			slotName = "Q",
			aoe = true,
			cc = false,
			slot = 0,
			hitbox = true,
			collision = false,
			speed = math.huge
		},
		LilliaW = {
			charName = "Lillia",
			radius = 250,
			range = 500,
			type = "circular",
			delay = 0.6,
			slotName = "W",
			aoe = true,
			cc = false,
			speed = 500,
			slot = 1,
			hitbox = true,
			collision = false
		},
		YoneQ = {
			charName = "Yone",
			radius = 100,
			range = 450,
			type = "linear",
			delay = 0.349,
			slotName = "Q",
			aoe = false,
			cc = false,
			speed = 8700,
			slot = 0,
			hitbox = true,
			collision = false
		},
		YoneQ3Missile = {
			charName = "Yone",
			radius = 80,
			range = 950,
			type = "linear",
			delay = 0.349,
			slotName = "Q",
			aoe = true,
			cc = true,
			speed = 1500,
			slot = 0,
			hitbox = true,
			collision = false
		},
		YoneW = {
			charName = "Yone",
			radius = 100,
			range = 700,
			type = "linear",
			delay = 0.5,
			slotName = "W",
			aoe = true,
			cc = false,
			speed = 1500,
			slot = 1,
			hitbox = true,
			collision = false
		},
		YoneR = {
			charName = "Yone",
			radius = 225,
			range = 1000,
			type = "linear",
			delay = 0.75,
			slotName = "R",
			aoe = true,
			cc = true,
			speed = 1500,
			slot = 3,
			hitbox = true,
			collision = false
		},
		SamiraQGun = {
			charName = "Samira",
			radius = 60,
			range = 950,
			type = "linear",
			delay = 0.25,
			slotName = "Q",
			aoe = false,
			cc = false,
			speed = 2600,
			slot = 0,
			hitbox = true,
			collision = true
		},
		SamiraQSword = {
			charName = "Samira",
			radius = 65,
			range = 325,
			type = "linear",
			delay = 0.25,
			slotName = "Q",
			aoe = true,
			cc = false,
			speed = 2600,
			slot = 0,
			hitbox = true,
			collision = false
		},
		SeraphineQ = {
			charName = "Seraphine",
			radius = 350,
			range = 900,
			type = "circular",
			delay = 0.25,
			slotName = "Q",
			aoe = true,
			cc = false,
			slot = 0,
			hitbox = true,
			collision = false,
			speed = math.huge
		},
		SeraphineE = {
			charName = "Seraphine",
			radius = 80,
			range = 1300,
			type = "linear",
			delay = 0.25,
			slotName = "E",
			aoe = true,
			cc = true,
			slot = 2,
			hitbox = true,
			collision = false,
			speed = math.huge
		},
		SeraphineR = {
			charName = "Seraphine",
			radius = 500,
			range = 1200,
			type = "linear",
			delay = 0.5,
			slotName = "R",
			aoe = true,
			cc = true,
			slot = 3,
			hitbox = true,
			collision = false,
			speed = math.huge
		},
		RellQ = {
			charName = "Rell",
			radius = 65,
			range = 700,
			type = "linear",
			delay = 0.349,
			slotName = "Q",
			aoe = true,
			cc = false,
			slot = 0,
			hitbox = true,
			collision = false,
			speed = math.huge
		},
		RellW_Dismount = {
			charName = "Rell",
			radius = 200,
			range = 550,
			type = "linear",
			delay = 0.625,
			slotName = "W",
			aoe = true,
			cc = true,
			slot = 1,
			hitbox = true,
			collision = false,
			speed = math.huge
		},
		RellE_StunCast = {
			charName = "Rell",
			radius = 300,
			range = 1500,
			type = "linear",
			delay = 0.4,
			slotName = "E",
			aoe = true,
			cc = true,
			speed = 500,
			slot = 2,
			hitbox = true,
			collision = false
		},
		RellR = {
			charName = "Rell",
			radius = 300,
			range = 400,
			type = "circular",
			delay = 0.25,
			slotName = "R",
			aoe = true,
			cc = true,
			speed = 500,
			slot = 3,
			hitbox = true,
			collision = false
		},
		ViegoQ = {
			charName = "Viego",
			radius = 210,
			range = 600,
			type = "linear",
			delay = 0.5,
			slotName = "Q",
			aoe = true,
			cc = false,
			speed = 2000,
			slot = 0,
			hitbox = true,
			collision = false
		},
		ViegoWMis = {
			charName = "Viego",
			radius = 60,
			range = 900,
			type = "linear",
			delay = 0.25,
			slotName = "W",
			aoe = false,
			cc = true,
			speed = 2000,
			slot = 1,
			hitbox = true,
			collision = true
		},
		ViegoR = {
			charName = "Viego",
			radius = 300,
			range = 500,
			type = "circular",
			delay = 0.75,
			slotName = "R",
			aoe = true,
			cc = false,
			speed = 2000,
			slot = 3,
			hitbox = true,
			collision = false
		},
		GwenQ = {
			charName = "Gwen",
			radius = 210,
			range = 500,
			type = "linear",
			delay = 0.25,
			slotName = "Q",
			aoe = true,
			cc = false,
			speed = 2000,
			slot = 0,
			hitbox = true,
			collision = false
		},
		GwenR = {
			charName = "Gwen",
			radius = 60,
			range = 1300,
			type = "linear",
			delay = 0.25,
			slotName = "R",
			aoe = true,
			cc = false,
			speed = 1800,
			slot = 3,
			hitbox = true,
			collision = false
		}
	},
	Targeted = {
		Morgana = {
			slotName = "E",
			slot = 2,
			range = 800,
			cc = false
		},
		Nami = {
			slotName = "W",
			slot = 1,
			range = 725,
			cc = false
		},
		Nami = {
			slotName = "E",
			slot = 2,
			range = 800,
			cc = false
		},
		Nasus = {
			slotName = "W",
			slot = 1,
			range = 700,
			cc = true
		},
		Nidalee = {
			slotName = "E",
			slot = 2,
			range = 600,
			cc = false
		},
		Nocturne = {
			slotName = "E",
			slot = 2,
			range = 425,
			cc = true
		},
		Nunu = {
			slotName = "Q",
			slot = 0,
			range = 125,
			cc = false
		},
		Olaf = {
			slotName = "E",
			slot = 2,
			range = 325,
			cc = false
		},
		Orianna = {
			slotName = "E",
			slot = 2,
			range = 1120,
			cc = false
		},
		Pantheon = {
			slotName = "W",
			slot = 1,
			range = 600,
			cc = true
		},
		poppy = {
			slotName = "E",
			slot = 2,
			range = 475,
			cc = true
		},
		Qiyana = {
			slotName = "E",
			slot = 2,
			range = 650,
			cc = false
		},
		Quinn = {
			slotName = "E",
			slot = 2,
			range = 600,
			cc = true
		},
		Rakan = {
			slotName = "E",
			slot = 2,
			range = 700,
			cc = false
		},
		Rammus = {
			slotName = "E",
			slot = 2,
			range = 325,
			cc = true
		},
		RekSai = {
			slotName = "E",
			slot = 2,
			range = 800,
			cc = false
		},
		Rell = {
			slotName = "E",
			slot = 2,
			range = 1500,
			cc = false
		},
		Ryze = {
			slotName = "W",
			slot = 1,
			range = 550,
			cc = true
		},
		Ryze = {
			slotName = "E",
			slot = 2,
			range = 550,
			cc = false
		},
		Samira = {
			slotName = "E",
			slot = 2,
			range = 650,
			cc = false
		},
		Sejuani = {
			slotName = "E",
			slot = 2,
			range = 600,
			cc = true
		},
		Senna = {
			slotName = "Q",
			slot = 0,
			range = 600,
			cc = false
		},
		Shaco = {
			slotName = "E",
			slot = 2,
			range = 625,
			cc = true
		},
		singed = {
			slotName = "E",
			slot = 2,
			range = 125,
			cc = true
		},
		Soraka = {
			slotName = "W",
			slot = 1,
			range = 550,
			cc = false
		},
		Syndra = {
			slotName = "W",
			slot = 1,
			range = 925,
			cc = false
		},
		TahmKench = {
			slotName = "W",
			slot = 1,
			range = 250,
			cc = true
		},
		Talon = {
			slotName = "Q",
			slot = 0,
			range = 575,
			cc = false
		},
		Taric = {
			slotName = "W",
			slot = 1,
			range = 800,
			cc = false
		},
		Teemo = {
			slotName = "Q",
			slot = 0,
			range = 680,
			cc = true
		},
		Tristana = {
			slotName = "E",
			slot = 2,
			range = 525,
			cc = false
		},
		Vayne = {
			slotName = "E",
			slot = 2,
			range = 550,
			cc = true
		},
		Viktor = {
			slotName = "Q",
			slot = 0,
			range = 600,
			cc = false
		},
		Vladimir = {
			slotName = "Q",
			slot = 0,
			range = 600,
			cc = false
		},
		Volibear = {
			slotName = "W",
			slot = 1,
			range = 325,
			cc = false
		},
		Warwick = {
			slotName = "Q",
			slot = 0,
			range = 350,
			cc = false
		},
		Wukong = {
			slotName = "E",
			slot = 2,
			range = 625,
			cc = false
		},
		XinZhao = {
			slotName = "E",
			slot = 2,
			range = 650,
			cc = false
		},
		Yasuo = {
			slotName = "E",
			slot = 2,
			range = 475,
			cc = false
		},
		Yuumi = {
			slotName = "W",
			slot = 1,
			range = 700,
			cc = false
		},
		zilean = {
			slotName = "E",
			slot = 2,
			range = 550,
			cc = false
		},
		Akali = {
			slotName = "R",
			slot = 3,
			range = 675,
			cc = false
		},
		Alistar = {
			slotName = "W",
			slot = 1,
			range = 650,
			cc = true
		},
		Anivia = {
			slotName = "E",
			slot = 2,
			range = 600,
			cc = false
		},
		Annie = {
			slotName = "Q",
			slot = 0,
			range = 625,
			cc = false
		},
		Bard = {
			slotName = "W",
			slot = 1,
			range = 800,
			cc = false
		},
		Brand = {
			slotName = "E",
			slot = 2,
			range = 675,
			cc = false
		},
		Braum = {
			slotName = "W",
			slot = 1,
			range = 650,
			cc = false
		},
		Caitlyn = {
			slotName = "R",
			slot = 3,
			range = 3500,
			cc = false
		},
		Camille = {
			slotName = "R",
			slot = 3,
			range = 475,
			cc = true
		},
		Cassiopeia = {
			slotName = "E",
			slot = 2,
			range = 700,
			cc = false
		},
		Chogath = {
			slotName = "R",
			slot = 3,
			range = 175,
			cc = false
		},
		Darius = {
			slotName = "R",
			slot = 3,
			range = 475,
			cc = false
		},
		Diana = {
			slotName = "E",
			slot = 2,
			range = 825,
			cc = false
		},
		Evelynn = {
			slotName = "W",
			slot = 1,
			range = 1200,
			cc = false
		},
		Evelynn = {
			slotName = "E",
			slot = 2,
			range = 210,
			cc = false
		},
		Fiddlesticks = {
			slotName = "Q",
			slot = 0,
			range = 575,
			cc = true
		},
		Fiora = {
			slotName = "R",
			slot = 3,
			range = 500,
			cc = false
		},
		Fizz = {
			slotName = "Q",
			slot = 0,
			range = 550,
			cc = false
		},
		Galio = {
			slotName = "R",
			slot = 3,
			range = 4000,
			cc = true
		},
		Gangplank = {
			slotName = "Q",
			slot = 0,
			range = 625,
			cc = false
		},
		Garen = {
			slotName = "R",
			slot = 3,
			range = 400,
			cc = false
		},
		Irelia = {
			slotName = "Q",
			slot = 0,
			range = 600,
			cc = false
		},
		Ivern = {
			slotName = "E",
			slot = 2,
			range = 750,
			cc = false
		},
		Janna = {
			slotName = "W",
			slot = 1,
			range = 550,
			cc = false
		},
		Janna = {
			slotName = "E",
			slot = 2,
			range = 800,
			cc = false
		},
		JarvanIV = {
			slotName = "R",
			slot = 3,
			range = 650,
			cc = true
		},
		Jax = {
			slotName = "Q",
			slot = 0,
			range = 700,
			cc = false
		},
		Jhin = {
			slotName = "Q",
			slot = 0,
			range = 550,
			cc = false
		},
		Karma = {
			slotName = "W",
			slot = 1,
			range = 675,
			cc = true
		},
		Karma = {
			slotName = "E",
			slot = 2,
			range = 600,
			cc = false
		},
		Kassadin = {
			slotName = "Q",
			slot = 0,
			range = 650,
			cc = false
		},
		Katarina = {
			slotName = "Q",
			slot = 0,
			range = 625,
			cc = false
		},
		Katarina = {
			slotName = "E",
			slot = 1,
			range = 725,
			cc = false
		}
	}
}
ove_0_12.TargetedSpellsx = {
	AnnieQ = {
		speed = 1400,
		slot = "Q",
		charName = "Annie",
		isMissile = false,
		delay = 0.25
	},
	GangplankQProceed = {
		speed = 2600,
		slot = "Q",
		charName = "Gangplank",
		isMissile = false,
		delay = 0.25
	},
	SowTheWind = {
		speed = 1600,
		slot = "W",
		charName = "Janna",
		isMissile = false,
		delay = 0.25
	},
	KatarinaQ = {
		speed = 1600,
		slot = "Q",
		charName = "Katarina",
		isMissile = false,
		delay = 0.25
	},
	NullLance = {
		speed = 1400,
		slot = "Q",
		charName = "Kassadin",
		isMissile = false,
		delay = 0.25
	},
	LeblancQ = {
		speed = 2000,
		slot = "Q",
		charName = "Leblanc",
		isMissile = false,
		delay = 0.25
	},
	LeblancRQ = {
		speed = 2000,
		slot = "RQ",
		charName = "Leblanc",
		isMissile = false,
		delay = 0.25
	},
	SeismicShard = {
		speed = 1200,
		slot = "Q",
		charName = "Malphite",
		isMissile = false,
		delay = 0.25
	},
	MissFortuneRicochetShot = {
		speed = 1400,
		slot = "Q",
		charName = "MissFortune",
		isMissile = false,
		delay = 0.25
	},
	RyzeQ = {
		speed = 1700,
		slot = "Q",
		charName = "Ryze",
		isMissile = false,
		delay = 0.25
	},
	RyzeE = {
		speed = 3500,
		slot = "E",
		charName = "Ryze",
		isMissile = false,
		delay = 0.25
	},
	RumbleGrenade = {
		speed = 2000,
		slot = "E",
		charName = "Rumble",
		isMissile = false,
		delay = 0.25
	},
	SyndraR = {
		speed = 1400,
		slot = "R",
		charName = "Syndra",
		isMissile = false,
		delay = 0.25
	},
	TwoShivPoison = {
		speed = 1500,
		slot = "E",
		charName = "Shaco",
		isMissile = false,
		delay = 0.25
	},
	BlindingDart = {
		speed = 1500,
		slot = "Q",
		charName = "Teemo",
		isMissile = false,
		delay = 0.25
	},
	TristanaR = {
		speed = 2000,
		slot = "R",
		charName = "Tristana",
		isMissile = false,
		delay = 0.25
	},
	WildCards = {
		speed = 1000,
		slot = "Q",
		charName = "TwistedFate",
		isMissile = false,
		delay = 0.25
	},
	BlueCardPreAttack = {
		speed = 1500,
		slot = "WBlue",
		charName = "TwistedFate",
		isMissile = false,
		delay = 0
	},
	RedCardPreAttack = {
		speed = 1500,
		slot = "WRed",
		charName = "TwistedFate",
		isMissile = false,
		delay = 0
	},
	GoldCardPreAttack = {
		speed = 1500,
		slot = "WGold",
		charName = "TwistedFate",
		isMissile = false,
		delay = 0
	},
	VayneCondemn = {
		speed = 2200,
		slot = "E",
		charName = "Vayne",
		isMissile = false,
		delay = 0.25
	},
	VeigarR = {
		speed = 500,
		slot = "R",
		charName = "Veigar",
		isMissile = false,
		delay = 0.25
	},
	ViktorPowerTransfer = {
		speed = 2000,
		slot = "Q",
		charName = "Viktor",
		isMissile = false,
		delay = 0.25
	}
}
ove_0_12.enum.buff_types = {
	Knockback = 30,
	Flee = 28,
	Counter = 26,
	Poison = 23,
	Heal = 13,
	Currency = 20,
	PhysicalImmunity = 16,
	Aura = 1,
	AttackSpeedSlow = 18,
	Internal = 0,
	Knockup = 29,
	Suppression = 24,
	Grounded = 32,
	Drowsy = 33,
	Asleep = 34,
	Silence = 7,
	Damage = 12,
	Shred = 27,
	Invisibility = 6,
	SpellImmunity = 15,
	Charm = 22,
	Fear = 21,
	Polymorph = 9,
	SpellShield = 4,
	Haste = 14,
	Snare = 11,
	Blind = 25,
	NearSight = 19,
	Slow = 10,
	Taunt = 8,
	Stun = 5,
	Invulnerability = 17,
	CombatDehancer = 3,
	Disarm = 31,
	CombatEnchancer = 2
}
ove_0_12.hard_cc = {
	nil,
	nil,
	nil,
	nil,
	true,
	nil,
	nil,
	true,
	nil,
	nil,
	true,
	nil,
	nil,
	nil,
	nil,
	nil,
	nil,
	true,
	nil,
	nil,
	true,
	true,
	nil,
	true,
	nil,
	nil,
	nil,
	true,
	true,
	true
}
ove_0_12.immobile_cc = {
	[24] = true,
	[32] = true,
	[34] = true,
	[11] = true,
	[29] = true,
	[5] = true,
	[8] = true,
	[22] = true
}

function ove_0_12.GetHittableEnemy(arg_46_0)
	return
end

function ove_0_12.GetEnemyTurrets()
	turrets = {}

	objManager.loop(function(arg_48_0)
		if arg_48_0.type == TYPE_TURRET and arg_48_0.team == TEAM_ENEMY then
			table.insert(turrets, arg_48_0)
		end
	end)

	return turrets
end

function ove_0_12.GetAllyTurrets()
	turrets = {}

	objManager.loop(function(arg_50_0)
		if arg_50_0.type == TYPE_TURRET and arg_50_0.team == TEAM_ALLY then
			table.insert(turrets, arg_50_0)
		end
	end)

	return turrets
end

function ove_0_12.IsPlayerUnderTurret()
	local slot_51_0 = ove_0_12.GetEnemyTurrets()

	for iter_51_0, iter_51_1 in ipairs(slot_51_0) do
		if iter_51_1.pos:dist(player) < 950 then
			return true
		end
	end
end

function ove_0_12.IsobjUnderTurretenemy(arg_52_0)
	local slot_52_0 = ove_0_12.GetEnemyTurrets()

	for iter_52_0, iter_52_1 in ipairs(slot_52_0) do
		if iter_52_1.pos:dist(arg_52_0) <= 950 then
			return true
		end
	end
end

function ove_0_12.IsobjUnderTurretenemy2(arg_53_0)
	local slot_53_0 = ove_0_12.GetEnemyTurrets()

	for iter_53_0, iter_53_1 in ipairs(slot_53_0) do
		if iter_53_1.pos:dist(arg_53_0) <= 1050 then
			return true
		end
	end
end

function ove_0_12.IsobjUnderTurretenemyrdive(arg_54_0)
	local slot_54_0 = ove_0_12.GetEnemyTurrets()

	for iter_54_0, iter_54_1 in ipairs(slot_54_0) do
		if iter_54_1.pos:dist(arg_54_0) <= 700 then
			return true
		end
	end
end

function ove_0_12.IsposUnderallyturret(arg_55_0)
	local slot_55_0 = ove_0_12.GetAllyTurrets()

	for iter_55_0, iter_55_1 in ipairs(slot_55_0) do
		if iter_55_1.pos:dist(arg_55_0) < 900 then
			return true
		end
	end

	return false
end

function ove_0_12.IsposUnderenemyturret(arg_56_0)
	local slot_56_0 = ove_0_12.GetEnemyTurrets()

	for iter_56_0, iter_56_1 in ipairs(slot_56_0) do
		if iter_56_1.pos:dist(arg_56_0) < 900 then
			return true
		end
	end

	return false
end

function ove_0_12.isObjUnderEnemyTurret(arg_57_0)
	local slot_57_0 = ove_0_12.GetEnemyTurrets()

	for iter_57_0, iter_57_1 in ipairs(slot_57_0) do
		if ove_0_12.IsValidTarget(arg_57_0) and iter_57_1.pos:dist(arg_57_0) < 900 then
			return true
		end
	end

	return false
end

function ove_0_12.GetMouseDirection()
	local slot_58_0 = vec2(mousePos2D.x - player.pos2D.x, mousePos2D.y - player.pos2D.y)

	return ove_0_12.Normalize(slot_58_0)
end

function ove_0_12.MoveToNormalizeMouse()
	mouse2Dmove = ove_0_12.GetMouseDirection()

	player:move(vec3(player.pos.x - mouse2Dmove.x * 200, player.pos.z, player.pos.y - mouse2Dmove.y * 200))
end

function ove_0_12.GetDirection(arg_60_0)
	local slot_60_0 = vec2(arg_60_0.endPos.x - arg_60_0.startPos.x, arg_60_0.endPos.y - arg_60_0.startPos.y)

	return ove_0_12.Normalize(slot_60_0)
end

function ove_0_12.Normalize(arg_61_0)
	local slot_61_0 = math.sqrt(math.pow(arg_61_0.x, 2) + math.pow(arg_61_0.y, 2))

	return vec2(arg_61_0.x / slot_61_0, arg_61_0.y / slot_61_0)
end

function ove_0_12.IsImmobileBuffer(arg_62_0, arg_62_1)
	local slot_62_0 = {}
	local slot_62_1 = game.time + (arg_62_1 or 0)

	for iter_62_0 = 0, arg_62_0.buffManager.count - 1 do
		local slot_62_2 = arg_62_0.buffManager:get(iter_62_0)

		if slot_62_2 and slot_62_2.valid and slot_62_1 <= slot_62_2.endTime then
			slot_62_0[slot_62_2.type] = true
		end
	end

	if slot_62_0[5] or slot_62_0[8] or slot_62_0[11] or slot_62_0[18] or slot_62_0[24] or slot_62_0[29] then
		return true
	end
end

function ove_0_12.IsHardCC(arg_63_0)
	for iter_63_0, iter_63_1 in pairs(ove_0_12.immobile_cc) do
		if ove_0_12.CheckBuffType(arg_63_0, iter_63_0) then
			return true
		end
	end

	return false
end

function ove_0_12.IsImmobile(arg_64_0)
	for iter_64_0, iter_64_1 in pairs(ove_0_12.immobile_cc) do
		if ove_0_12.CheckBuffType(arg_64_0, iter_64_0) then
			return true
		end
	end

	return false
end

function ove_0_12.HasItem(arg_65_0)
	for iter_65_0 = 6, 11 do
		local slot_65_0 = player:spellSlot(iter_65_0).name

		if slot_65_0 and slot_65_0 == arg_65_0 then
			return true
		end
	end
end

function ove_0_12.HasItemID(arg_66_0)
	for iter_66_0 = 0, 6 do
		local slot_66_0 = player:itemID(iter_66_0)

		if slot_66_0 and slot_66_0 == arg_66_0 then
			return iter_66_0
		end
	end
end

function ove_0_12.AveragePositionVec3(arg_67_0, arg_67_1)
	return vec3(arg_67_0.x + arg_67_1.x / 2, arg_67_0.y + arg_67_1.y / 2, arg_67_0.z + arg_67_1.z / 2)
end

function ove_0_12.AveragePositionVec2(arg_68_0, arg_68_1)
	return vec3(arg_68_0.x + arg_68_1.x / 2, arg_68_0.y + arg_68_1.y / 2)
end

function ove_0_12.IsValidTarget(arg_69_0)
	if arg_69_0 and not arg_69_0.isDead and arg_69_0.isVisible and arg_69_0.isTargetable and ove_0_12.invuln(arg_69_0) then
		return false
	end

	return arg_69_0 and not arg_69_0.isDead and arg_69_0.isVisible and arg_69_0.isTargetable and arg_69_0.maxHealth > 100 and arg_69_0.maxHealth < 150000 and not arg_69_0.name:find("Ward")
end

function ove_0_12.IsValidTargetOrInvulnerable(arg_70_0)
	return arg_70_0 and not arg_70_0.isDead and arg_70_0.isVisible and arg_70_0.isTargetable
end

ove_0_12.units = {}
ove_0_12.units.minions, ove_0_12.units.minionCount = {}, 0
ove_0_12.units.enemyMinions, ove_0_12.units.enemyMinionCount = {}, 0
ove_0_12.units.allyMinions, ove_0_12.units.allyMinionCount = {}, 0
ove_0_12.units.jungleMinions, ove_0_12.units.jungleMinionCount = {}, 0
ove_0_12.units.enemies, ove_0_12.units.allies = {}, {}

function ove_0_12.can_target_minion(arg_71_0)
	return arg_71_0 and not arg_71_0.isDead and arg_71_0.team ~= TEAM_ALLY and arg_71_0.moveSpeed > 0 and arg_71_0.health and arg_71_0.maxHealth > 100 and arg_71_0.isVisible and arg_71_0.isTargetable
end

local ove_0_18 = {
	PlantMasterMinion = true,
	PlantHealth = true,
	CampRespawn = true,
	PlantVision = true,
	PlantSatchel = true
}

local function ove_0_19(arg_72_0)
	return arg_72_0 and arg_72_0.type == TYPE_HERO
end

local function ove_0_20(arg_73_0, arg_73_1, arg_73_2)
	return arg_73_0 - (arg_73_0 - arg_73_1):norm() * arg_73_2
end

local function ove_0_21(arg_74_0, arg_74_1, arg_74_2, arg_74_3)
	local slot_74_0

	for iter_74_0 = 1, arg_74_1 do
		local slot_74_1 = arg_74_0[iter_74_0]

		if not arg_74_3(slot_74_1) then
			slot_74_0 = iter_74_0

			break
		end
	end

	if slot_74_0 then
		arg_74_0[slot_74_0] = arg_74_2
	else
		arg_74_1 = arg_74_1 + 1
		arg_74_0[arg_74_1] = arg_74_2
	end

	return arg_74_1
end

local function ove_0_22(arg_75_0)
	if ove_0_19(arg_75_0) then
		if arg_75_0.team == TEAM_ALLY then
			ove_0_21(ove_0_12.units.allies, #ove_0_12.units.allies, arg_75_0, ove_0_19)
		else
			ove_0_21(ove_0_12.units.enemies, #ove_0_12.units.enemies, arg_75_0, ove_0_19)
		end
	end
end

cb.add(cb.create_minion, ove_0_22)
objManager.loop(function(arg_76_0)
	ove_0_22(arg_76_0)
end)

function ove_0_12.GetAllyHeroesInRange(arg_77_0, arg_77_1)
	local slot_77_0 = arg_77_1 or player
	local slot_77_1 = {}
	local slot_77_2 = ove_0_12.GetAllyHeroes()

	for iter_77_0 = 1, #slot_77_2 do
		local slot_77_3 = slot_77_2[iter_77_0]

		if ove_0_12.IsValidTarget(slot_77_3) and slot_77_3.pos:distSqr(slot_77_0) < arg_77_0 * arg_77_0 then
			slot_77_1[#slot_77_1 + 1] = slot_77_3
		end
	end

	return slot_77_1
end

function ove_0_12.tablelength(arg_78_0)
	local slot_78_0 = 0

	for iter_78_0 in pairs(arg_78_0) do
		slot_78_0 = slot_78_0 + 1
	end

	return slot_78_0
end

function ove_0_12.GetEnemyHeroesInRange(arg_79_0, arg_79_1)
	local slot_79_0 = arg_79_1 or player
	local slot_79_1 = {}
	local slot_79_2 = ove_0_12.GetEnemyHeroes()

	for iter_79_0 = 1, #slot_79_2 do
		local slot_79_3 = slot_79_2[iter_79_0]

		if ove_0_12.IsValidTarget(slot_79_3) and slot_79_3.pos:distSqr(slot_79_0) < arg_79_0 * arg_79_0 then
			slot_79_1[#slot_79_1 + 1] = slot_79_3
		end
	end

	return slot_79_1
end

function ove_0_12.CountEnemyHeroesInRange(arg_80_0, arg_80_1)
	local slot_80_0 = arg_80_1 or player
	local slot_80_1 = arg_80_0 or 1500

	if slot_80_1 and slot_80_0 then
		return (ove_0_12.tablelength(ove_0_12.GetEnemyHeroesInRange(slot_80_1, slot_80_0)))
	else
		return 0
	end
end

function ove_0_12.valid_minion(arg_81_0)
	return arg_81_0 and arg_81_0.type == TYPE_MINION and not arg_81_0.isDead and arg_81_0.health > 0 and arg_81_0.maxHealth > 100 and arg_81_0.maxHealth < 10000 and not arg_81_0.name:find("Ward") and not ove_0_18[arg_81_0.name]
end

function ove_0_12.valid_minionxd(arg_82_0)
	return arg_82_0 and arg_82_0.name:find("Ward")
end

function ove_0_12.CountObjectsNearPos(arg_83_0, arg_83_1, arg_83_2, arg_83_3)
	local slot_83_0 = 0
	local slot_83_1 = {}

	for iter_83_0, iter_83_1 in pairs(arg_83_2) do
		if arg_83_3(iter_83_1) and arg_83_1 >= arg_83_0:dist(iter_83_1.pos) then
			slot_83_0 = slot_83_0 + 1
			slot_83_1[slot_83_0] = iter_83_1
		end
	end

	return slot_83_0, slot_83_1
end

function ove_0_12.ForEachEnemy(arg_84_0)
	for iter_84_0, iter_84_1 in ipairs(ove_0_12.GetEnemyHeroes()) do
		arg_84_0(iter_84_1)
	end
end

function ove_0_12.ForEachEnemyInRange(arg_85_0, arg_85_1)
	for iter_85_0, iter_85_1 in ipairs(ove_0_12.GetEnemyHeroes()) do
		if arg_85_0 >= iter_85_1.pos:dist(player) and arg_85_1(iter_85_1) then
			return
		end
	end
end

function ove_0_12.GetEnemyHeroes()
	return ove_0_12.units.enemies
end

function ove_0_12.GetAllyHeroes()
	return ove_0_12.units.allies
end

ove_0_12._fountain = nil
ove_0_12._fountainRadius = 750

function ove_0_12.GetFountain()
	if ove_0_12._fountain then
		return ove_0_12._fountain
	end

	local slot_88_0 = ove_0_12.GetMap()

	if slot_88_0 and slot_88_0.index and slot_88_0.index == 1 then
		ove_0_12._fountainRadius = 1050
	end

	if ove_0_12.GetShop() then
		objManager.loop(function(arg_89_0)
			if arg_89_0 and arg_89_0.team == TEAM_ALLY and arg_89_0.name:lower():find("spawn") and not arg_89_0.name:lower():find("troy") and not arg_89_0.name:lower():find("barracks") then
				ove_0_12._fountain = arg_89_0

				return ove_0_12._fountain
			end
		end)
	end

	return nil
end

function ove_0_12.NearFountain(arg_90_0)
	local slot_90_0 = arg_90_0 or ove_0_12._fountainRadius or 0
	local slot_90_1 = ove_0_12.GetFountain()

	if slot_90_1 then
		return player.pos2D:distSqr(slot_90_1.pos2D) <= slot_90_0 * slot_90_0, slot_90_1.x, slot_90_1.y, slot_90_1.z, slot_90_0
	else
		return false, 0, 0, 0, 0
	end
end

function ove_0_12.InFountain()
	return ove_0_12.NearFountain()
end

ove_0_12._shop = nil
ove_0_12._shopRadius = 1250

function ove_0_12.GetShop()
	if ove_0_12._shop then
		return ove_0_12._shop
	end

	objManager.loop(function(arg_93_0)
		if arg_93_0 and arg_93_0.team == TEAM_ALLY and arg_93_0.name:lower():find("shop") then
			ove_0_12._shop = arg_93_0

			return ove_0_12._shop
		end
	end)

	return nil
end

local ove_0_23 = false
local ove_0_24 = module.seek("evade")
local ove_0_25
local ove_0_26
local ove_0_27
local ove_0_28
local ove_0_29

function ove_0_12.incomingdamageally(arg_94_0)
	if not evadecheck then
		print("checking evade.", ove_0_24)

		evadecheck = true

		if ove_0_24 == nil then
			print("NO EVADE MODULE FOUND PLEASE ENABLE EVADE ")
			chat.add("[Alexis AC2] ", {
				color = "#fff600",
				bold = false,
				italic = false
			})
			chat.add("Enable Evade 2.0 for advanced W usage", {
				color = "#CC5544CC",
				bold = true
			})
			chat.print()

			totalDmg = 100
		elseif ove_0_24.menu.text == "Hanbot Evade 2" then
			ove_0_23 = true
		else
			chat.add("[Alexis AC2] ", {
				color = "#CC5544CC",
				bold = false,
				italic = false
			})
			chat.add("Enable Evade 2.0 for advanced W usage", {
				color = "#CC5544CC",
				bold = true
			})
			chat.print()

			totalDmg = 0
			ove_0_23 = false
		end
	end

	if ove_0_23 then
		local slot_94_0 = ove_0_12.GetAllyHeroes()

		for iter_94_0, iter_94_1 in ipairs(slot_94_0) do
			if iter_94_1 and not iter_94_1.isDead and iter_94_1 ~= player and arg_94_0 >= player.pos:dist(iter_94_1) then
				ove_0_25, ove_0_26, ove_0_27, ove_0_28 = ove_0_24.damage.count(iter_94_1)
				totalDmg = ove_0_25 + ove_0_26 + ove_0_27

				return totalDmg, iter_94_1
			end
		end
	end

	return 0
end

return ove_0_12

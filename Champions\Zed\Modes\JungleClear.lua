
local ove_0_10 = module.load("<PERSON>", "<PERSON><PERSON>/<PERSON><PERSON><PERSON><PERSON>")
local ove_0_11 = module.load("<PERSON>", "Champions/Zed/Util/menu")
local ove_0_12 = objManager.minions
local ove_0_13 = module.load("<PERSON>", "Champions/Zed/Spells/W")
local ove_0_14 = module.load("<PERSON>", "Champions/Zed/Spells/Q")
local ove_0_15 = module.load("<PERSON>", "Champions/Zed/Spells/E")
local ove_0_16 = module.load("<PERSON>", "Champions/Zed/Util/Detection")
local ove_0_17 = player

return {
	Execute = function()
		-- print 5
		local slot_5_0 = ove_0_17.mana >= ove_0_11.farm.jungleclear.Qmana:get() and ove_0_17.mana >= ove_0_14.Cost()
		local slot_5_1

		slot_5_1 = ove_0_17.mana >= ove_0_11.farm.jungleclear.Wmana:get() and ove_0_17.mana >= ove_0_13.Cost()

		local slot_5_2 = ove_0_17.mana >= ove_0_11.farm.jungleclear.Emana:get() and ove_0_17.mana >= ove_0_15.Cost()
		local slot_5_3 = ove_0_17.mana >= ove_0_11.farm.jungleclear.Wmana:get() + ove_0_11.farm.jungleclear.Emana:get() and ove_0_17.mana >= ove_0_14.Cost() + ove_0_13.Cost()
		local slot_5_4 = ove_0_16.Wpos and ove_0_16.Wpos or ove_0_17.pos

		for iter_5_0 = 0, ove_0_12.size[TEAM_NEUTRAL] - 1 do
			local slot_5_5 = ove_0_12[TEAM_NEUTRAL][iter_5_0]

			if ove_0_11.farm.jungleclear.useW:get() and slot_5_3 and ove_0_10.IsValidTarget(slot_5_5) and ove_0_10.IsJungle(slot_5_5) and slot_5_5.pos:dist(ove_0_17.pos) < ove_0_13.range then
				ove_0_13.Cast1(slot_5_5.pos)
			end

			if ove_0_11.farm.jungleclear.useQ:get() and slot_5_0 and ove_0_10.IsValidTarget(slot_5_5) and ove_0_10.IsJungle(slot_5_5) then
				ove_0_14.Cast(slot_5_5)
			end

			if ove_0_11.farm.jungleclear.useE:get() and slot_5_2 and slot_5_5.pos:dist(slot_5_4) <= ove_0_15.range and ove_0_10.IsValidTarget(slot_5_5) and ove_0_10.IsJungle(slot_5_5) then
				ove_0_15.Cast(slot_5_5)
			end
		end
	end
}

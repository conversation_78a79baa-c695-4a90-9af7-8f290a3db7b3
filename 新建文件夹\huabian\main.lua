

local ove_0_10 = os.time({
	hour = 23,
	month = 1,
	year = 2022,
	min = 59,
	sec = 59,
	day = 12
})

if os.time() - ove_0_10 > 0 then
	print("Rex Aio outdated.")

	return
end

if game.version:find("12.1") == nil then
	return
end

module.load("<PERSON><PERSON><PERSON>", "orbwalker").Add()

if player.charName == "Yasuo" then
	module.load("RexAio", "champions/yasuo").Add()
end

if player.charName == "Irelia" then
	module.load("RexAio", "champions/irelia").Add()
end

print("RexAio Load.")


local ove_0_5 = module.internal("orb")
local ove_0_6 = module.load("<PERSON>","poppy/menu")
local ove_0_7 = module.load("<PERSON>","poppy/items")
local ove_0_8 = module.load("<PERSON>","poppy/q")
local ove_0_9 = module.load("<PERSON>","poppy/w")
local ove_0_10 = module.load("<PERSON>","poppy/e")
local ove_0_11 = module.load("<PERSON>","poppy/r")
local ove_0_12
local ove_0_13 = {
	PoppyQ = 0.333,
	PoppyRSpell = 0.65,
	PoppyRSpellInstant = 0.65
}
local ove_0_14 = {
	PlantVision = true,
	PlantSatchel = true,
	PlantHealthMirrored = true,
	PlantHealth = true
}

local function ove_0_15() 
	 -- print 1
	ove_0_10.pre_tick()
	ove_0_11.pre_tick()

	if ove_0_12 and os.clock() > ove_0_12 then
		ove_0_12 = nil

		ove_0_5.core.set_pause(0)
	end

	if ove_0_9.invoke() then
		return
	end

	if ove_0_11.invoke_channel() then
		return
	end
end

local function  ove_0_16() 
	-- -- print 2
	if ove_0_5.menu.combat.key:get() then
		if ove_0_10.invoke() then
			return
		end

		if ove_0_11.invoke_auto() then
			return
		end

		if ove_0_8.invoke() then
			return
		end

		if ove_0_7.after_attack_combat() then
			return
		end
	elseif ove_0_5.menu.lane_clear.key:get() then
		local slot_2_0 = ove_0_5.menu.lane_clear.mod:get()

		if slot_2_0 == 1 then
			return
		end

		if slot_2_0 == 2 and not keyboard.isKeyDown(1) then
			return
		end

		if slot_2_0 == 3 and not ove_0_5.menu.lane_clear.panic_key:get() then
			return
		end

		if not ove_0_5.core.cur_attack_target then
			return
		end

		if ove_0_5.core.cur_attack_target.type ~= TYPE_MINION then
			return
		end

		if ove_0_14[ove_0_5.core.cur_attack_target.name] then
			return
		end

		if ove_0_10.invoke_clear() then
			return
		end

		if ove_0_8.invoke_clear(TEAM_NEUTRAL) then
			return
		end

		if ove_0_8.invoke_clear(TEAM_ENEMY) then
			return
		end

		if ove_0_7.after_attack_clear() then
			return
		end
	end
end

local   function ove_0_17()
	-- -- print 3
	if ove_0_5.menu.combat.key:get() then
		if ove_0_10.invoke() then
			return
		end

		if ove_0_10.invoke_flash_e() then
			return
		end

		if ove_0_11.invoke_auto() then
			return
		end

		if ove_0_8.invoke() then
			return
		end
	end
end

local function ove_0_18(arg_4_0)
	-- -- print 4
	ove_0_10.on_create_obj(arg_4_0)
end

local function ove_0_19(arg_5_0)
	-- -- print 5
	if arg_5_0.owner.ptr ~= player.ptr then
		return
	end

	if ove_0_13[arg_5_0.name] then
		ove_0_12 = os.clock() + ove_0_13[arg_5_0.name]

		ove_0_5.core.set_pause(math.huge)
	end

	ove_0_11.on_process_spell(arg_5_0)
end

local  ove_0_20 = player.path

local function ove_0_21(arg_6_0)
	-- -- print 6
	if arg_6_0.ptr ~= player.ptr then
		return
	end

	if not ove_0_20.isDashing then
		return
	end

	ove_0_12 = os.clock() + ove_0_20.serverPos2D:dist(ove_0_20.point2D[1]) / ove_0_20.dashSpeed
end

ove_0_5.combat.register_f_pre_tick(ove_0_15)
ove_0_5.combat.register_f_after_attack(ove_0_16)
ove_0_5.combat.register_f_out_of_range(ove_0_17)
cb.add(cb.path, ove_0_21)
cb.add(cb.create_minion, ove_0_18)
cb.add(cb.spell, ove_0_19)

local malzaharPlugin = {}

-- Load Spell
local spellQ = {
    range = 900,
    delay = 0.50,
    radius = 80,
    speed = math.huge,
    boundingRadiusMod = 0,
    collision = {hero = false, minion = false, wall = false}
}

local spellQ2 = {
    range = 900,
    delay = 0.25,
    radius = 100,
    speed = math.huge,
    boundingRadiusMod = 0,
    collision = {hero = false, minion = false, wall = false},
    laneClearRadius = 230,
}

local spellW = {
    range = 650,
    delay = 1.2,
    radius = 230,
    speed = math.huge,
    boundingRadiusMod = 1,
    collision = {hero = false, minion = false, wall = false}
}

local spellE = {
    range = 650,
}

local spellR = {
    range = 700,
}

-- Load Module
--local ui = module.load("<PERSON>", "ui");
local Curses = module.load("<PERSON>", "Curses");
local DelayAction = module.load("Brian", "Core/DelayAction")
local DelayTick = module.load("Brian", "Core/DelayTick")
local Prediction = module.load("<PERSON>", "Core/Prediction")
local BuffManager = module.load("<PERSON>", "Library/BuffManager")
local CalculateManager = module.load("<PERSON>", "Library/CalculateManager")
local FarmManager = module.load("<PERSON>", "Library/FarmManager")
local ItemManager = module.load("Brian", "Library/ItemManager")
local NetManager = module.load("Brian", "Library/NetManager")
local ObjectManager = module.load("Brian", "Library/ObjectManager")
local OrbManager = module.load("Brian", "Library/OrbManager")
local SpellManager = module.load("Brian", "Library/SpellManager")
local VectorManager = module.load("Brian", "Library/VectorManager")
local MyCommon = module.load("Brian", "Library/ExtraManager")

local MyMenu

function malzaharPlugin.Load(GlobalMenu)
    MyMenu = GlobalMenu

    MyMenu:menu("Combo", "Combo Settings")
    MyMenu.Combo:header("SpellHeader", "Spell Core")
    MyMenu.Combo:boolean("Q", "Use Q", true)
    MyMenu.Combo:boolean("W", "Use W", true)
    MyMenu.Combo:boolean("E", "Use E", true)
    MyMenu.Combo:boolean("R", "Use R", true)
    MyMenu.Combo:boolean("RT", "^ Allow UnderTurret Cast", false)
    MyMenu.Combo:boolean("RKS", "^ If target Can KillAble", true)
    MyMenu.Combo:boolean("RA", "^ Always Cast", false)
    local rEnemiesList = ObjectManager.GetEnemyHeroes()
    for i, rEnemy in ipairs(rEnemiesList) do
        MyMenu.Combo:boolean("RT_"..rEnemy.charName, "Use On: " .. rEnemy.charName, true)
    end

    MyMenu:menu("Harass", "Harass Settings")
    MyMenu.Harass:header("SpellHeader", "Spell Core")
    MyMenu.Harass:boolean("Q", "Use Q", true)
    MyMenu.Harass:boolean("W", "Use W", true)
    MyMenu.Harass:boolean("E", "Use E", true)
    MyMenu.Harass:boolean("EM", "^ Use On Minion", false)
    MyMenu.Harass:header("ManaHeader", "Mana Manager")
    MyMenu.Harass:slider("ManaMin", "Player ManaPercent >= x%", 60, 1, 100, 1)
    MyMenu.Harass:header("ProHeader", "PRO Mode")
    MyMenu.Harass:boolean("ProAllow", "Enabled PRO Harass Mode", true)
    MyMenu.Harass.ProAllow:set("tooltip", "PRO Mode => Allow Use Spell Harass Enemy on LaneClear Mode")
    MyMenu.Harass:boolean("ProTurret", "Allow Under Turret Harass", false)

    MyMenu:menu("LaneClear", "LaneClear Settings")
    MyMenu.LaneClear:header("SpellHeader", "Spell Core")
    MyMenu.LaneClear:boolean("Q", "Use Q", true)
    MyMenu.LaneClear:slider("QC", "^ Min Hit Count >= x", 3, 1, 10, 1)
    MyMenu.LaneClear:boolean("W", "Use W", true)
    MyMenu.LaneClear:boolean("E", "Use E", true)
    MyMenu.LaneClear:header("ManaHeader", "Mana Manager")
    MyMenu.LaneClear:slider("ManaMin", "Player ManaPercent >= x%", 60, 1, 100, 1)

    MyMenu:menu("JungleClear", "JungleClear Settings")
    MyMenu.JungleClear:header("SpellHeader", "Spell Core")
    MyMenu.JungleClear:boolean("Q", "Use Q", true)
    MyMenu.JungleClear:boolean("W", "Use W", true)
    MyMenu.JungleClear:boolean("E", "Use E", true)
    MyMenu.JungleClear:header("ManaHeader", "Mana Manager")
    MyMenu.JungleClear:slider("ManaMin", "Player ManaPercent >= x%", 20, 1, 100, 1)

    FarmManager.Load(MyMenu)

    MyMenu:menu("KillSteal", "KillSteal Settings")
    MyMenu.KillSteal:header("SpellHeader", "Spell Core")
    MyMenu.KillSteal:boolean("Q", "Use Q", true)
    MyMenu.KillSteal:boolean("E", "Use E", true)

    MyMenu:menu("Draw", "Draw Settings")
    MyMenu.Draw:header("RangeHeader", "Spell Range")
    MyMenu.Draw:boolean("Q", "Draw Q Range", true)
    MyMenu.Draw:color("colorq", "^ color", 255, 233, 121, 121)
    MyMenu.Draw:boolean("E", "Draw E Range", true)
    MyMenu.Draw:color("colore", "^ color", 255, 233, 121, 121)
    MyMenu.Draw:boolean("R", "Draw R Range", true)
    MyMenu.Draw:color("colorr", "^ color", 255, 233, 121, 121)
    MyMenu.Draw:boolean("Ready", "Only Spell Ready", false)
    MyMenu.Draw:header("DamageHeader", "Damage Indicator")
    MyMenu.Draw:boolean("DIEnabled", "Enabled", true)

end

local function GetQDamage(target)
    if not target or target == nil then
        return 0
    end
    local level = player:spellSlot(0).level
    if level == 0 then
        return 0
    end
    local dmg = ({70, 105, 140, 175, 210})[level] + (0.65 * MyCommon.GetTotalAP())
    return CalculateManager.CalculateMagicDamage(target, dmg)
end

local function GetWDamage(target)
    if not target or target == nil then
        return 0
    end
    local level = player:spellSlot(1).level
    if level == 0 then
        return 0
    end
    local dmg = ({10, 15, 20, 25, 30})[level] + (0.1 * MyCommon.GetTotalAP())
    return CalculateManager.CalculateMagicDamage(target, dmg) + CalculateManager.GetAutoAttackDamage(player, target)
end

local function GetEDamage(target)
    if not target or target == nil then
        return 0
    end
    local level = player:spellSlot(2).level
    if level == 0 then
        return 0
    end
    local dmg = ({80, 115, 150, 185, 220})[level] + (0.7 * MyCommon.GetTotalAP())
    return CalculateManager.CalculateMagicDamage(target, dmg)
end

local function GetRDamage(target)
    if not target or target == nil then
        return 0
    end
    local level = player:spellSlot(3).level
    if level == 0 then
        return 0
    end
    local dmg = ({125, 200, 275})[level] + (0.8 * MyCommon.GetTotalAP())
    return CalculateManager.CalculateMagicDamage(target, dmg)
end

local function KillSteal()
    if MyMenu.KillSteal.Q:get() and SpellManager.CanCastSpell(0) and not player.path.isDashing then
        local targets = ObjectManager.GetEnemiesInRange(spellQ.range, player)
        for i, target in ipairs(targets) do
            if MyCommon.IsValidTarget(target, spellQ.range) and not MyCommon.IsUnKillAble(target) then
                local qDMG = GetQDamage(target)
                if target.health and target.health < qDMG then
                    local pred = Prediction.GetPrediction(spellQ, target)
                    if pred then
                        SpellManager.CastOnPosition(vec3(pred.x, target.pos.y, pred.y), 0)
                        return
                    end
                end
            end
        end
    end
    if MyMenu.KillSteal.E:get() and SpellManager.CanCastSpell(2) and not player.path.isDashing then
        local targets = ObjectManager.GetEnemiesInRange(spellE.range, player)
        for i, target in ipairs(targets) do
            if MyCommon.IsValidTarget(target, spellE.range) and not MyCommon.IsUnKillAble(target) then
                local eDMG = GetEDamage(target)
                if target.health and target.health < eDMG then
                    SpellManager.CastOnUnit(target, 2)
                    return
                end
            end
        end
    end
end

local function Combo()
    if MyMenu.Combo.E:get() and SpellManager.CanCastSpell(2) then
        local target = MyCommon.GetTarget(spellE.range)
        if target and target ~= nil and MyCommon.IsValidTarget(target, spellE.range) then
            SpellManager.CastOnUnit(target, 2)
        end
    end
    if MyMenu.Combo.Q:get() and SpellManager.CanCastSpell(0) then
        local target = MyCommon.GetTarget(spellQ.range)
        if target and target ~= nil and MyCommon.IsValidTarget(target, spellQ.range) then
            local pred = Prediction.GetPrediction(spellQ, target)
            if pred then
                SpellManager.CastOnPosition(vec3(pred.x, target.pos.y, pred.y), 0)
            end
        end
    end
    if MyMenu.Combo.W:get() and SpellManager.CanCastSpell(1) then
        local target = MyCommon.GetTarget(spellW.range)
        if target and target ~= nil and MyCommon.IsValidTarget(target, spellW.range) then
            local castPos = VectorManager.Extend(player.pos, target.pos, 450)
            SpellManager.CastOnPosition(castPos, 1)
        end
    end
    if MyMenu.Combo.R:get() and SpellManager.CanCastSpell(3) then
        if not MyMenu.Combo.RT:get() and ObjectManager.IsUnderEnemyTurret(player) then
            return
        end
        if MyMenu.Combo.RKS:get() then
            local target = MyCommon.GetTarget(spellR.range)
            if target and target ~= nil and MyCommon.IsValidTarget(target, spellR.range) and not MyCommon.IsUnKillAble(target) then
                local damage = (SpellManager.CanCastSpell(0) and GetQDamage(target) or 0) + (SpellManager.CanCastSpell(2) and GetEDamage(target) or 0) + (SpellManager.CanCastSpell(3) and GetRDamage(target) or 0) + CalculateManager.GetAutoAttackDamage(player, target)
                if target.health and target.health < damage then
                    SpellManager.CastOnUnit(target, 3)
                end
            end
        end
        if MyMenu.Combo.RA:get() then
            local targets = ObjectManager.GetEnemiesInRange(spellR.range)
            for i, target in ipairs(targets) do
                if target and target ~= nil and MyCommon.IsValidTarget(target, spellR.range) then
                    if target.charName and MyMenu.Combo["RT_"..target.charName] and MyMenu.Combo["RT_"..target.charName]:get() then
                        SpellManager.CastOnUnit(target, 3)
                    end
                end
            end
        end
    end
end

local function Harass()
    if not MyMenu.Harass.ProTurret:get() and ObjectManager.IsUnderEnemyTurret(player) then
        return
    end
    if MyCommon.GetManaPercent() >= MyMenu.Harass.ManaMin:get() then
        if MyMenu.Harass.E:get() and SpellManager.CanCastSpell(2) then
            local target = MyCommon.GetTarget(spellE.range)
            if target and target ~= nil and MyCommon.IsValidTarget(target, spellE.range) then
                SpellManager.CastOnUnit(target, 2)
            end
        end
        if MyMenu.Harass.Q:get() and SpellManager.CanCastSpell(0) then
            local target = MyCommon.GetTarget(spellQ.range)
            if target and target ~= nil and MyCommon.IsValidTarget(target, spellQ.range) then
                local pred = Prediction.GetPrediction(spellQ, target)
                if pred then
                    SpellManager.CastOnPosition(vec3(pred.x, target.pos.y, pred.y), 0)
                end
            end
        end
        if MyMenu.Harass.W:get() and SpellManager.CanCastSpell(1) then
            local target = MyCommon.GetTarget(spellW.range)
            if target and target ~= nil and MyCommon.IsValidTarget(target, spellW.range) then
                local castPos = VectorManager.Extend(player.pos, target.pos, 450)
                SpellManager.CastOnPosition(castPos, 1)
            end
        end
        if MyMenu.Harass.E:get() and MyMenu.Harass.EM:get() and SpellManager.CanCastSpell(2) then
            local target = MyCommon.GetTarget(spellE.range + 300)
            if target and target ~= nil and MyCommon.IsValidTarget(target, spellE.range + 300) and not MyCommon.IsValidTarget(target, spellE.range) then
                local minions = ObjectManager.GetMinions(spellE.range, TEAM_ENEMY)
                if minions and #minions > 0 then
                    for i, minion in ipairs(minions) do
                        if minion and minion ~= nil and MyCommon.IsValidTarget(minion, spellE.range) and minion.pos:dist(target.pos) < 350 and not BuffManager.HasBuff(minion, "MalzaharE") then
                            local eDMG = GetEDamage(minion)
                            if minion.health and minion.health < eDMG then
                                SpellManager.CastOnUnit(minion, 2)
                            end
                        end
                    end
                end
            end
        end
    end
end

local function Clear()
    ---------- LaneClear
    if MyMenu.LaneClear.E:get() and SpellManager.CanCastSpell(2) then
        if MyCommon.GetManaPercent() >= MyMenu.LaneClear.ManaMin:get() then
            local minions = ObjectManager.GetMinions(spellE.range, TEAM_ENEMY)
            if minions and #minions > 2 then
                for i, minion in ipairs(minions) do
                    if minion and minion ~= nil and MyCommon.IsValidTarget(minion, spellE.range) and not BuffManager.HasBuff(minion, "MalzaharE") then
                        local eDMG = GetEDamage(minion)
                        if minion.health and minion.health < eDMG then
                            SpellManager.CastOnUnit(minion, 2)
                            return
                        end
                    end
                end
            end
        end
    end
    if MyMenu.LaneClear.Q:get() and SpellManager.CanCastSpell(0) then
        if MyCommon.GetManaPercent() >= MyMenu.LaneClear.ManaMin:get() then
            local minions = ObjectManager.GetMinions(spellQ.range, TEAM_ENEMY)
            if minions and #minions >= MyMenu.LaneClear.QC:get() then
                local Pos, HitCount = FarmManager.GetBestCircularFarmPosition(spellQ2.laneClearRadius, minions)
                if HitCount and HitCount ~= nil and HitCount >= MyMenu.LaneClear.QC:get() and Pos then
                    SpellManager.CastOnPosition(vec3(Pos.x, game.mousePos.y, Pos.y), 0)
                    return
                end
            end
        end
    end
    if MyMenu.LaneClear.W:get() and SpellManager.CanCastSpell(1) then
        if MyCommon.GetManaPercent() >= MyMenu.LaneClear.ManaMin:get() then
            local minions = ObjectManager.GetMinions(spellW.range, TEAM_ENEMY)
            if minions and #minions > 2 then
                local Pos, HitCount = FarmManager.GetBestCircularFarmPosition(spellW.radius, minions)
                if HitCount and HitCount ~= nil and HitCount > 2 and Pos then
                    SpellManager.CastOnPosition(vec3(Pos.x, game.mousePos.y, Pos.y), 1)
                    return
                end
            end
        end
    end
    ---------- JungleClear
    if MyMenu.JungleClear.E:get() and SpellManager.CanCastSpell(2) then
        if MyCommon.GetManaPercent() >= MyMenu.JungleClear.ManaMin:get() then
            local mobs = ObjectManager.GetMinions(spellE.range, TEAM_NEUTRAL)
            if mobs and #mobs > 0 then
                for i, mob in ipairs(mobs) do
                    if mob and mob ~= nil and MyCommon.IsValidTarget(mob, spellE.range) then
                        SpellManager.CastOnUnit(mob, 2)
                        return
                    end
                end
            end
        end
    end
    if MyMenu.JungleClear.Q:get() and SpellManager.CanCastSpell(0) then
        if MyCommon.GetManaPercent() >= MyMenu.JungleClear.ManaMin:get() then
            local mobs = ObjectManager.GetMinions(spellQ.range, TEAM_NEUTRAL)
            if mobs and #mobs > 0 then
                for i, mob in ipairs(mobs) do
                    if mob and mob ~= nil and MyCommon.IsValidTarget(mob, spellQ.range) then
                        local pred = Prediction.GetPrediction(spellQ, mob)
                        if pred then
                            SpellManager.CastOnPosition(vec3(pred.x, mob.pos.y, pred.y), 0)
                            return
                        end
                    end
                end
            end
        end
    end
    if MyMenu.JungleClear.W:get() and SpellManager.CanCastSpell(1) then
        if MyCommon.GetManaPercent() >= MyMenu.JungleClear.ManaMin:get() then
            local mobs = ObjectManager.GetMinions(spellW.range, TEAM_NEUTRAL)
            if mobs and #mobs > 0 then
                for i, mob in ipairs(mobs) do
                    if mob and mob ~= nil and MyCommon.IsValidTarget(mob, spellW.range) then
                        local pred = Prediction.GetPrediction(spellW, mob)
                        if pred then
                            SpellManager.CastOnPosition(vec3(pred.x, mob.pos.y, pred.y), 1)
                            return
                        end
                    end
                end
            end
        end
    end
end

local function OnMyCastSpell(spellSlot)
    if spellSlot and spellSlot == 3 and MyMenu.Key.Combo:get() then
        if MyMenu.Combo.E:get() and SpellManager.CanCastSpell(2) then
            local target = MyCommon.GetTarget(spellE.range)
            if target and target ~= nil and MyCommon.IsValidTarget(target, spellE.range) then
                SpellManager.CastOnUnit(target, 2)
                return
            end
        end
        if MyMenu.Combo.W:get() and SpellManager.CanCastSpell(1) then
            local target = MyCommon.GetTarget(spellW.range)
            if target and target ~= nil and MyCommon.IsValidTarget(target, spellW.range) then
                local castPos = VectorManager.Extend(player.pos, target.pos, 450)
                SpellManager.CastOnPosition(castPos, 1)
                return
            end
        end
        if MyMenu.Combo.Q:get() and SpellManager.CanCastSpell(0) then
            local target = MyCommon.GetTarget(spellQ.range)
            if target and target ~= nil and MyCommon.IsValidTarget(target, spellQ.range) then
                local pred = Prediction.GetPrediction(spellQ2, target)
                if pred then
                    SpellManager.CastOnPosition(vec3(pred.x, target.pos.y, pred.y), 0)
                    return
                end
            end
        end
    end
end

local function OnMyTick()
    if player.isDead or player.isRecalling or chat.isOpened then
        return
    end
    if BuffManager.HasBuff(player, "malzaharrsound") then
        return
    end
    if DelayTick.CanTickEvent() then
        KillSteal()
    end
    if MyMenu.Key.Harass:get() then
        Harass()
    end
    if MyMenu.Key.Combo:get() then
        Combo()
    end
    if DelayTick.CanTickEvent() then
        if MyMenu.Key.LaneClear:get() then
            if MyMenu.Harass.ProAllow:get() then
                Harass()
            end
            if FarmManager.Enabled then
                Clear()
            end
        end
    end
end

local function OnMyDraw()
    if player.isDead or chat.isOpened then
        return
    end
    if MyMenu.Draw.Q:get() and MyCommon.CanDrawCircle(spellQ.range) then
        if MyMenu.Draw.Ready:get() then
            if SpellManager.CanCastSpell(0) and MyCommon.CanDrawCircle(spellQ.range) then
                graphics.draw_circle(player.pos, spellQ.range, 2, MyMenu.Draw.colorq:get(), 100)
            end
        else
            graphics.draw_circle(player.pos, spellQ.range, 2, MyMenu.Draw.colorq:get(), 100)
        end
    end
    if MyMenu.Draw.E:get() and MyCommon.CanDrawCircle(spellE.range) then
        if MyMenu.Draw.Ready:get() then
            if SpellManager.CanCastSpell(2) then
                graphics.draw_circle(player.pos, spellE.range, 2, MyMenu.Draw.colore:get(), 100)
            end
        else
            graphics.draw_circle(player.pos, spellE.range, 2, MyMenu.Draw.colore:get(), 100)
        end
    end
    if MyMenu.Draw.R:get() and MyCommon.CanDrawCircle(spellR.range) then
        if MyMenu.Draw.Ready:get() then
            if SpellManager.CanCastSpell(3) then
                graphics.draw_circle(player.pos, spellR.range, 2, MyMenu.Draw.colorr:get(), 100)
            end
        else
            graphics.draw_circle(player.pos, spellR.range, 2, MyMenu.Draw.colorr:get(), 100)
        end
    end
    if MyMenu.Draw.DIEnabled:get() then
        local targets = ObjectManager.GetEnemyHeroes()
        if targets and #targets > 0 then
            for i, target in ipairs(targets) do
                if target and MyCommon.IsValidTarget(target) and target.isVisible and target.isOnScreen and target.health > 0 and not BuffManager.HasBuffOfType(target, 17) then
                    local damage = (SpellManager.CanCastSpell(0) and GetQDamage(target) or 0) + (SpellManager.CanCastSpell(1) and GetWDamage(target) or 0) + (SpellManager.CanCastSpell(2) and GetEDamage(target) or 0) + (SpellManager.CanCastSpell(3) and GetRDamage(target) or 0)
                    if damage > 0 then
                        local hp_bar_pos = target.barPos
                        local xPos = hp_bar_pos.x + 165
                        local yPos = hp_bar_pos.y + 122.5
                        if target.charName and target.charName == "Annie" then
                            yPos = yPos + 2
                        end
                        local remainHealth = target.health - damage
                        local x1 = xPos + ((target.health / target.maxHealth) * 104)
                        local x2 = xPos + (((remainHealth > 0 and remainHealth or 0) / target.maxHealth) * 103.4)
                        graphics.draw_line_2D(x1, yPos, x2, yPos, 11, (remainHealth > 0 and 0xFFF2781E or 0xFFFF0000))
                    end
                end
            end
        end
    end
end

cb.add(cb.castspell, OnMyCastSpell)
cb.add(cb.tick, OnMyTick)
cb.add(cb.draw, OnMyDraw)


return malzaharPlugin

math.randomseed(0.039277)

local ove_0_0

ove_0_0 = {
	function(...)
		local slot_1_0 = {
			...
		}

		return ove_0_0[8](slot_1_0)
	end,
	cb,
	game,
	math.frexp,
	math.random(1, 1000),
	string.dump,
	string.sub,
	table.concat,
	cb,
	"bot",
	loadstring,
	"b+",
	function(arg_2_0)
		local slot_2_0 = loadstring(arg_2_0)

		if slot_2_0 then
			return ove_0_0[tonumber("20")](function()
				slot_2_0()
			end)
		else
			return nil
		end
	end,
	"load",
	1234567890,
	getfenv,
	"",
	"jit",
	0.1,
	pcall,
	math.pi,
	""
}

local ove_0_1 = ove_0_0[5]

local function ove_0_2(arg_4_0)
	return tostring(arg_4_0 / ove_0_1)
end

local ove_0_3 = {
	ove_0_2(6031),
	ove_0_2(13014),
	ove_0_2(2577),
	ove_0_2(30109),
	ove_0_2(23020),
	ove_0_2(2071),
	ove_0_2(15490),
	ove_0_2(12777),
	ove_0_2(27499),
	ove_0_2(15539),
	ove_0_2(11938),
	ove_0_2(18931),
	ove_0_2(32084),
	ove_0_2(2340),
	ove_0_2(4621),
	ove_0_2(22452),
	ove_0_2(30030),
	ove_0_2(25024),
	ove_0_2(22997),
	ove_0_2(20933),
	ove_0_2(16410),
	ove_0_2(20121),
	ove_0_2(17108),
	ove_0_2(17002),
	ove_0_2(15489),
	ove_0_2(31394),
	ove_0_2(13889),
	ove_0_2(28624),
	ove_0_2(21131),
	ove_0_2(15891),
	ove_0_2(14978),
	ove_0_2(22931),
	ove_0_2(2802),
	ove_0_2(10254),
	ove_0_2(26177),
	ove_0_2(12163),
	ove_0_2(15062),
	ove_0_2(19330),
	ove_0_2(2498),
	ove_0_2(9640),
	ove_0_2(6066),
	ove_0_2(21328),
	ove_0_2(628),
	ove_0_2(18820),
	ove_0_2(18319),
	ove_0_2(8620),
	ove_0_2(27846),
	ove_0_2(15511),
	ove_0_2(16760),
	ove_0_2(32454),
	ove_0_2(10299),
	ove_0_2(30761),
	ove_0_2(4653),
	ove_0_2(6826),
	ove_0_2(25025),
	ove_0_2(32323),
	ove_0_2(22812),
	ove_0_2(26213),
	ove_0_2(6207),
	ove_0_2(7167),
	ove_0_2(4492),
	ove_0_2(2154)
}
local ove_0_4, ove_0_5 = ove_0_0[9].get(2)

if not ove_0_5 then
	return
end

local ove_0_6 = module.load(header.id, "Library/Main")
local ove_0_7 = module.load(header.id, "Champions/Draven/CatchAxe")
local ove_0_8 = module.load(header.id, "Champions/Draven/Menu")
local ove_0_9 = module.internal("orb")
local ove_0_10 = ove_0_6.spellLib:new({
	name = "DravenSpinning",
	owner = player,
	spellSlot = player:spellSlot(0)
})
local ove_0_11 = game.fnvhash("dravenSpinningattack")

local function ove_0_12()
	return player:getBuffStacks(ove_0_11)
end

local function ove_0_13()
	return ove_0_12() + ove_0_7.get_axe_count()
end

local function ove_0_14()
	local slot_7_0 = player:findBuff(ove_0_11)

	if ove_0_6.buffLib.get_buff_remain_time(slot_7_0) <= 0.15 then
		return true
	end
end

local function ove_0_15()
	if ove_0_10:is_ready() then
		return true
	end
end

local function ove_0_16()
	if ove_0_15() and (ove_0_12() ~= 2 or ove_0_14()) then
		return true
	end
end

local function ove_0_17()
	player:castSpell("self", 0)
end

return {
	data = ove_0_10,
	get_hand_axe_count = ove_0_12,
	is_q_will_end = ove_0_14,
	get_all_axe_count = ove_0_13,
	get_spell_state = ove_0_15,
	get_action_state = ove_0_16,
	invoke_action = ove_0_17
}

local BrandPlugin = {}
local version = "1.0"
local evade = module.seek("evade")
local database = module.load("<PERSON>", "Utility/SpellDatabase")
local preds = module.internal("pred")
local TS = module.internal("TS")
local orb = module.internal("orb")
local ui = module.load("<PERSON>", "ui");
local DelayAction = module.load("<PERSON>", "Core/DelayAction")
local DelayTick = module.load("<PERSON>", "Core/DelayTick")
local Prediction = module.load("<PERSON>", "Core/Prediction")
local BuffManager = module.load("<PERSON>", "Library/BuffManager")
local CalculateManager = module.load("<PERSON>", "Library/CalculateManager")
local Curses = module.load("<PERSON>", "<PERSON><PERSON>");
local FarmManager = module.load("<PERSON>", "Library/FarmManager")
local ItemManager = module.load("Brian", "Library/ItemManager")
local NetManager = module.load("<PERSON>", "Library/NetManager")
local ObjectManager = module.load("<PERSON>", "Library/ObjectManager")
local OrbManager = module.load("<PERSON>", "Library/OrbManager")
local SpellManager = module.load("<PERSON>", "Library/SpellManager")
local VectorManager = module.load("Brian", "Library/VectorManager")
local MyCommon = module.load("Brian", "Library/ExtraManager")
local common = module.load("Brian","Utility/common")
local MyMenu

local spellQ = {
	range = 1050,
	delay = 0.25,
	width = 60,
	speed = 1600,
	boundingRadiusMod = 1,
	collision = {
		hero = false,
		minion = true
	}
}

local spellW = {
	range = 900,
	delay = 0.85,
	radius = 250,
	speed = 2900,
	boundingRadiusMod = 1
}

local spellE = {
	range = 625
}

local spellR = {
	range = 750
}
local FlashSlot = nil
if player:spellSlot(4).name == "SummonerFlash" then
	FlashSlot = 4
elseif player:spellSlot(5).name == "SummonerFlash" then
	FlashSlot = 5
end

local interruptableSpells = {
	["anivia"] = {
		{MyMenuslot = "R", slot = 3, spellname = "glacialstorm", channelduration = 6}
	},
	["caitlyn"] = {
		{MyMenuslot = "R", slot = 3, spellname = "caitlynaceinthehole", channelduration = 1}
	},
	["ezreal"] = {
		{MyMenuslot = "R", slot = 3, spellname = "ezrealtrueshotbarrage", channelduration = 1}
	},
	["fiddlesticks"] = {
		{MyMenuslot = "W", slot = 1, spellname = "drain", channelduration = 5},
		{MyMenuslot = "R", slot = 3, spellname = "crowstorm", channelduration = 1.5}
	},
	["gragas"] = {
		{MyMenuslot = "W", slot = 1, spellname = "gragasw", channelduration = 0.75}
	},
	["janna"] = {
		{MyMenuslot = "R", slot = 3, spellname = "reapthewhirlwind", channelduration = 3}
	},
	["karthus"] = {
		{MyMenuslot = "R", slot = 3, spellname = "karthusfallenone", channelduration = 3}
	}, --common.IsValidTargetTarget will prevent from casting @ karthus while he's zombie
	["katarina"] = {
		{MyMenuslot = "R", slot = 3, spellname = "katarinar", channelduration = 2.5}
	},
	["lucian"] = {
		{MyMenuslot = "R", slot = 3, spellname = "lucianr", channelduration = 2}
	},
	["lux"] = {
		{MyMenuslot = "R", slot = 3, spellname = "luxmalicecannon", channelduration = 0.5}
	},
	["malzahar"] = {
		{MyMenuslot = "R", slot = 3, spellname = "malzaharr", channelduration = 2.5}
	},
	["masteryi"] = {
		{MyMenuslot = "W", slot = 1, spellname = "meditate", channelduration = 4}
	},
	["missfortune"] = {
		{MyMenuslot = "R", slot = 3, spellname = "missfortunebullettime", channelduration = 3}
	},
	["nunu"] = {
		{MyMenuslot = "R", slot = 3, spellname = "absolutezero", channelduration = 3}
	},
	--excluding Orn's Forge Channel since it can be cancelled just by attacking him
	["pantheon"] = {
		{MyMenuslot = "R", slot = 3, spellname = "pantheonrjump", channelduration = 2}
	},
	["shen"] = {
		{MyMenuslot = "R", slot = 3, spellname = "shenr", channelduration = 3}
	},
	["twistedfate"] = {
		{MyMenuslot = "R", slot = 3, spellname = "gate", channelduration = 1.5}
	},
	["varus"] = {
		{MyMenuslot = "Q", slot = 0, spellname = "varusq", channelduration = 4}
	},
	["warwick"] = {
		{MyMenuslot = "R", slot = 3, spellname = "warwickr", channelduration = 1.5}
	},
	["xerath"] = {
		{MyMenuslot = "R", slot = 3, spellname = "xerathlocusofpower2", channelduration = 3}
	}
}








local str = {[-1] = "P", [0] = "Q", [1] = "W", [2] = "E", [3] = "R"}

function BrandPlugin.Load(GlobalMenu)
   
	MyMenu = GlobalMenu
	
	MyMenu:menu("Combo", "Combo")
	MyMenu.Combo:dropdown("Combomode", "Combo Mode", 1, {"E-Q-W", "E-W-Q", "W-E-Q", "W-Q-E"}, 2)
	MyMenu.Combo:boolean("qCombo", "Use Q in Combo", true)
	MyMenu.Combo:boolean("stunq", " ^- Only if Stuns", true)
	MyMenu.Combo:boolean("wCombo", "Use W in Combo", true)
	MyMenu.Combo:boolean("eCombo", "Use E in Combo", true)
	MyMenu.Combo:boolean("rCombo", "Use R in Combo", true)
	MyMenu.Combo:dropdown("rmode", "R Mode", 1, {"If X Health", "Only if Killlable"}, 2)
	MyMenu.Combo:slider("hp", "If X Health", 50, 1, 100, 1)
	MyMenu.Combo:slider("hitr", "Min. Enemies to Hit", 1, 1, 5, 1)
	MyMenu.Combo:boolean("minion", " ^- Include Minions for Bounce", true)

	MyMenu:menu("Combo", "Combo")
	MyMenu.Combo:dropdown("Combomode", "Combo Mode", 1, {"E-Q-W", "E-W-Q", "W-E-Q", "W-Q-E"}, 2)
	MyMenu.Combo:boolean("qCombo", "Use Q in Combo", true)
	MyMenu.Combo:boolean("stunq", " ^- Only if Stuns", true)
	MyMenu.Combo:boolean("wCombo", "Use W in Combo", true)
	MyMenu.Combo:boolean("eCombo", "Use E in Combo", true)

	

	MyMenu:menu("laneclear", "LaneClear")
	MyMenu.laneclear:slider("mana", "Mana Manager", 30, 0, 100, 1)
	MyMenu.laneclear:boolean("usew", "Use W to Farm", true)
	MyMenu.laneclear:slider("hitw", " ^- If Hits", 3, 0, 6, 1)



	MyMenu:menu("jungle", "Jungle Clear")
	MyMenu.jungle:slider("mana", "Mana Manager", 30, 0, 100, 1)
	MyMenu.jungle:boolean("useq", "Use Q in Jungle", true)
	MyMenu.jungle:boolean("usew", "Use W in Jungle", true)
	MyMenu.jungle:boolean("usee", "Use E in Jungle", true)

	FarmManager.Load(MyMenu)

	MyMenu:menu("killsteal", "Killsteal")
	MyMenu.killsteal:boolean("ksq", "Killsteal with Q", true)
	MyMenu.killsteal:boolean("ksw", "Killsteal with W", true)
	MyMenu.killsteal:boolean("kse", "Killsteal with E", true)
	MyMenu.killsteal:boolean("ksr", "Killsteal with R", true)
	MyMenu.killsteal:slider("hitr", " ^- Only if Bounces on X Enemies", 1, 1, 5, 1)

	MyMenu:menu("draws", "Draw Settings")
	MyMenu.draws:boolean("drawq", "Draw Q Range", true)
	MyMenu.draws:color("colorq", "  ^- Color", 255, 233, 121, 121)
	MyMenu.draws:boolean("draww", "Draw W Range", true)
	MyMenu.draws:color("colorw", "  ^- Color", 255, 233, 121, 121)
	MyMenu.draws:boolean("drawe", "Draw E Range", false)
	MyMenu.draws:color("colore", "  ^- Color", 255, 233, 121, 121)
	MyMenu.draws:boolean("drawr", "Draw R Range", false)
	MyMenu.draws:color("colorr", "  ^- Color", 255, 233, 121, 121)
	MyMenu.draws:boolean("drawdamage", "Draw Damage", true)
end

local uhh = false
local something = 0

local TargetSelectionQ = function(res, obj, dist)
	if dist < spellQ.range then
		res.obj = obj
		return true
	end
end
local GetTargetQ = function()
	return TS.get_result(TargetSelectionQ).obj
end

local TargetSelectionEngage = function(res, obj, dist)
	if dist < 1300 then
		res.obj = obj
		return true
	end
end
local GetTargetEngage = function()
	return TS.get_result(TargetSelectionEngage).obj
end

local TargetSelectionW = function(res, obj, dist)
	if dist < spellW.range then
		res.obj = obj
		return true
	end
end
local GetTargetW = function()
	return TS.get_result(TargetSelectionW).obj
end

local TargetSelectionR = function(res, obj, dist)
	if dist < spellR.range then
		res.obj = obj
		return true
	end
end

local GetTargetR = function()
	return TS.get_result(TargetSelectionR).obj
end
local QLevelDamage = {80, 110, 140, 170, 200}
function QDamage(target)
	local damage = 0
	if player:spellSlot(0).level > 0 then
		damage =
			common.CalculateMagicDamage(target, (QLevelDamage[player:spellSlot(0).level] + (common.GetTotalAP() * .55)), player)
	end
	return damage
end
local WLevelDamage = {75, 120, 140, 165, 255}
function WDamage(target)
	local damage = 0
	if player:spellSlot(1).level > 0 then
		damage =
			common.CalculateMagicDamage(target, (WLevelDamage[player:spellSlot(1).level] + (common.GetTotalAP() * .6)), player)
	end
	return damage
end
local ELevelDamage = {70, 90, 110, 130, 150}
function EDamage(target)
	local damage = 0
	if player:spellSlot(2).level > 0 then
		damage =
			common.CalculateMagicDamage(target, (ELevelDamage[player:spellSlot(2).level] + (common.GetTotalAP() * .35)), player)
	end
	return damage
end
local RLevelDamage = {100, 200, 300}
function RDamage(target)
	local damage = 0
	if player:spellSlot(3).level > 0 then
		damage =
			common.CalculateMagicDamage(target, (RLevelDamage[player:spellSlot(3).level] + (common.GetTotalAP() * .25)), player)
	end
	return damage
end
local function count_enemies_in_range(pos, range)
	local enemies_in_range = {}
	for i = 0, objManager.enemies_n - 1 do
		local enemy = objManager.enemies[i]
		if pos:dist(enemy.pos) < range and common.IsValidTarget(enemy) then
			enemies_in_range[#enemies_in_range + 1] = enemy
		end
	end
	return enemies_in_range
end
local function count_minions_in_range(pos, range)
	local enemies_in_range = {}
	for i = 0, objManager.minions.size[TEAM_ENEMY] - 1 do
		local enemy = objManager.minions[TEAM_ENEMY][i]
		if pos:dist(enemy.pos) < range and common.IsValidTarget(enemy) then
			enemies_in_range[#enemies_in_range + 1] = enemy
		end
	end
	return enemies_in_range
end
local function Combo()
	local target = GetTargetQ()
	if target and target.isVisible then
		if common.IsValidTarget(target) then
			if MyMenu.Combo.Combomode:get() == 2 then
				if MyMenu.Combo.eCombo:get() then
					if target.pos:dist(player.pos) < spellE.range then
						player:castSpell("obj", 2, target)
					end
				end
				if MyMenu.Combo.qCombo:get() then
					if MyMenu.Combo.stunq:get() then
						if target.pos:dist(player.pos) < spellQ.range and common.CheckBuff(target, "BrandAblaze") then
							local pos = preds.linear.get_prediction(spellQ, target)
							if pos and pos.startPos:dist(pos.endPos) < spellQ.range then
								if not preds.collision.get_prediction(spellQ, pos, target) then
									player:castSpell("pos", 0, vec3(pos.endPos.x, mousePos.y, pos.endPos.y))
								end
							end
						end
					else
						if target.pos:dist(player.pos) < spellQ.range then
							local pos = preds.linear.get_prediction(spellQ, target)
							if pos and pos.startPos:dist(pos.endPos) < spellQ.range then
								if not preds.collision.get_prediction(spellQ, pos, target) then
									player:castSpell("pos", 0, vec3(pos.endPos.x, mousePos.y, pos.endPos.y))
								end
							end
						end
					end
				end
				if MyMenu.Combo.wCombo:get() then
					if target.pos:dist(player.pos) < spellW.range then
						local pos = preds.circular.get_prediction(spellW, target)
						if pos and pos.startPos:dist(pos.endPos) < spellW.range then
							player:castSpell("pos", 1, vec3(pos.endPos.x, mousePos.y, pos.endPos.y))
						end
					end
				end
			end

			if MyMenu.Combo.Combomode:get() == 1 then
				if MyMenu.Combo.eCombo:get() then
					if target.pos:dist(player.pos) < spellE.range then
						player:castSpell("obj", 2, target)
					end
				end
				if MyMenu.Combo.wCombo:get() then
					if target.pos:dist(player.pos) < spellW.range then
						local pos = preds.circular.get_prediction(spellW, target)
						if pos and pos.startPos:dist(pos.endPos) < spellW.range then
							player:castSpell("pos", 1, vec3(pos.endPos.x, mousePos.y, pos.endPos.y))
						end
					end
				end
				if MyMenu.Combo.qCombo:get() then
					if MyMenu.Combo.stunq:get() then
						if target.pos:dist(player.pos) < spellQ.range and common.CheckBuff(target, "BrandAblaze") then
							local pos = preds.linear.get_prediction(spellQ, target)
							if pos and pos.startPos:dist(pos.endPos) < spellQ.range then
								if not preds.collision.get_prediction(spellQ, pos, target) then
									player:castSpell("pos", 0, vec3(pos.endPos.x, mousePos.y, pos.endPos.y))
								end
							end
						end
					else
						if target.pos:dist(player.pos) < spellQ.range then
							local pos = preds.linear.get_prediction(spellQ, target)
							if pos and pos.startPos:dist(pos.endPos) < spellQ.range then
								if not preds.collision.get_prediction(spellQ, pos, target) then
									player:castSpell("pos", 0, vec3(pos.endPos.x, mousePos.y, pos.endPos.y))
								end
							end
						end
					end
				end
			end
			if MyMenu.Combo.Combomode:get() == 4 then
				if MyMenu.Combo.wCombo:get() then
					if target.pos:dist(player.pos) < spellW.range then
						local pos = preds.circular.get_prediction(spellW, target)
						if pos and pos.startPos:dist(pos.endPos) < spellW.range then
							player:castSpell("pos", 1, vec3(pos.endPos.x, mousePos.y, pos.endPos.y))
						end
					end
				end
				if MyMenu.Combo.eCombo:get() then
					if target.pos:dist(player.pos) < spellE.range then
						player:castSpell("obj", 2, target)
					end
				end

				if MyMenu.Combo.qCombo:get() then
					if MyMenu.Combo.stunq:get() then
						if target.pos:dist(player.pos) < spellQ.range and common.CheckBuff(target, "BrandAblaze") then
							local pos = preds.linear.get_prediction(spellQ, target)
							if pos and pos.startPos:dist(pos.endPos) < spellQ.range then
								if not preds.collision.get_prediction(spellQ, pos, target) then
									player:castSpell("pos", 0, vec3(pos.endPos.x, mousePos.y, pos.endPos.y))
								end
							end
						end
					else
						if target.pos:dist(player.pos) < spellQ.range then
							local pos = preds.linear.get_prediction(spellQ, target)
							if pos and pos.startPos:dist(pos.endPos) < spellQ.range then
								if not preds.collision.get_prediction(spellQ, pos, target) then
									player:castSpell("pos", 0, vec3(pos.endPos.x, mousePos.y, pos.endPos.y))
								end
							end
						end
					end
				end
			end
			if MyMenu.Combo.Combomode:get() == 3 then
				if MyMenu.Combo.wCombo:get() then
					if target.pos:dist(player.pos) < spellW.range then
						local pos = preds.circular.get_prediction(spellW, target)
						if pos and pos.startPos:dist(pos.endPos) < spellW.range then
							player:castSpell("pos", 1, vec3(pos.endPos.x, mousePos.y, pos.endPos.y))
						end
					end
				end

				if MyMenu.Combo.qCombo:get() then
					if MyMenu.Combo.stunq:get() then
						if target.pos:dist(player.pos) < spellQ.range and common.CheckBuff(target, "BrandAblaze") then
							local pos = preds.linear.get_prediction(spellQ, target)
							if pos and pos.startPos:dist(pos.endPos) < spellQ.range then
								if not preds.collision.get_prediction(spellQ, pos, target) then
									player:castSpell("pos", 0, vec3(pos.endPos.x, mousePos.y, pos.endPos.y))
								end
							end
						end
					else
						if target.pos:dist(player.pos) < spellQ.range then
							local pos = preds.linear.get_prediction(spellQ, target)
							if pos and pos.startPos:dist(pos.endPos) < spellQ.range then
								if not preds.collision.get_prediction(spellQ, pos, target) then
									player:castSpell("pos", 0, vec3(pos.endPos.x, mousePos.y, pos.endPos.y))
								end
							end
						end
					end
				end
				if MyMenu.Combo.eCombo:get() then
					if target.pos:dist(player.pos) < spellE.range then
						player:castSpell("obj", 2, target)
					end
				end
			end
			if MyMenu.Combo.rCombo:get() then
				if target.pos:dist(player.pos) < spellR.range then
					if not MyMenu.Combo.minion:get() then
						if MyMenu.Combo.rmode:get() == 1 then
							if (#count_enemies_in_range(target.pos, 750) >= MyMenu.Combo.hitr:get()) then
								if (target.health / target.maxHealth) * 100 <= MyMenu.Combo.hp:get() then
									player:castSpell("obj", 3, target)
								end
							end
						end
						if MyMenu.Combo.rmode:get() == 2 then
							if target.health < (WDamage(target) + QDamage(target) + RDamage(target) + EDamage(target)) then
								if (#count_enemies_in_range(target.pos, 750) >= MyMenu.Combo.hitr:get()) then
									player:castSpell("obj", 3, target)
								end
							end
						end
					end
					if MyMenu.Combo.minion:get() then
						if MyMenu.Combo.rmode:get() == 1 then
							if (#count_minions_in_range(target.pos, 750) + #count_enemies_in_range(target.pos, 750) >= MyMenu.Combo.hitr:get()) then
								if (target.health / target.maxHealth) * 100 <= MyMenu.Combo.hp:get() then
									player:castSpell("obj", 3, target)
								end
							end
						end
						if MyMenu.Combo.rmode:get() == 2 then
							if target.health < (WDamage(target) + QDamage(target) + RDamage(target) + EDamage(target)) then
								if
									(#count_minions_in_range(target.pos, 750) + #count_enemies_in_range(target.pos, 750) >= MyMenu.Combo.hitr:get())
								 then
									player:castSpell("obj", 3, target)
								end
							end
						end
					end
				end
			end
		end
	end
end

local function Combo()
	local target = GetTargetQ()
	if target and target.isVisible then
		if common.IsValidTarget(target) then
			if MyMenu.Combo.Combomode:get() == 2 then
				if MyMenu.Combo.eCombo:get() then
					if target.pos:dist(player.pos) < spellE.range then
						player:castSpell("obj", 2, target)
					end
				end
				if MyMenu.Combo.qCombo:get() then
					if MyMenu.Combo.stunq:get() then
						if target.pos:dist(player.pos) < spellQ.range and common.CheckBuff(target, "BrandAblaze") then
							local pos = preds.linear.get_prediction(spellQ, target)
							if pos and pos.startPos:dist(pos.endPos) < spellQ.range then
								if not preds.collision.get_prediction(spellQ, pos, target) then
									player:castSpell("pos", 0, vec3(pos.endPos.x, mousePos.y, pos.endPos.y))
								end
							end
						end
					else
						if target.pos:dist(player.pos) < spellQ.range then
							local pos = preds.linear.get_prediction(spellQ, target)
							if pos and pos.startPos:dist(pos.endPos) < spellQ.range then
								if not preds.collision.get_prediction(spellQ, pos, target) then
									player:castSpell("pos", 0, vec3(pos.endPos.x, mousePos.y, pos.endPos.y))
								end
							end
						end
					end
				end
				if MyMenu.Combo.wCombo:get() then
					if target.pos:dist(player.pos) < spellW.range then
						local pos = preds.circular.get_prediction(spellW, target)
						if pos and pos.startPos:dist(pos.endPos) < spellW.range then
							player:castSpell("pos", 1, vec3(pos.endPos.x, mousePos.y, pos.endPos.y))
						end
					end
				end
			end

			if MyMenu.Combo.Combomode:get() == 1 then
				if MyMenu.Combo.eCombo:get() then
					if target.pos:dist(player.pos) < spellE.range then
						player:castSpell("obj", 2, target)
					end
				end
				if MyMenu.Combo.wCombo:get() then
					if target.pos:dist(player.pos) < spellW.range then
						local pos = preds.circular.get_prediction(spellW, target)
						if pos and pos.startPos:dist(pos.endPos) < spellW.range then
							player:castSpell("pos", 1, vec3(pos.endPos.x, mousePos.y, pos.endPos.y))
						end
					end
				end
				if MyMenu.Combo.qCombo:get() then
					if MyMenu.Combo.stunq:get() then
						if target.pos:dist(player.pos) < spellQ.range and common.CheckBuff(target, "BrandAblaze") then
							local pos = preds.linear.get_prediction(spellQ, target)
							if pos and pos.startPos:dist(pos.endPos) < spellQ.range then
								if not preds.collision.get_prediction(spellQ, pos, target) then
									player:castSpell("pos", 0, vec3(pos.endPos.x, mousePos.y, pos.endPos.y))
								end
							end
						end
					else
						if target.pos:dist(player.pos) < spellQ.range then
							local pos = preds.linear.get_prediction(spellQ, target)
							if pos and pos.startPos:dist(pos.endPos) < spellQ.range then
								if not preds.collision.get_prediction(spellQ, pos, target) then
									player:castSpell("pos", 0, vec3(pos.endPos.x, mousePos.y, pos.endPos.y))
								end
							end
						end
					end
				end
			end
			if MyMenu.Combo.Combomode:get() == 4 then
				if MyMenu.Combo.wCombo:get() then
					if target.pos:dist(player.pos) < spellW.range then
						local pos = preds.circular.get_prediction(spellW, target)
						if pos and pos.startPos:dist(pos.endPos) < spellW.range then
							player:castSpell("pos", 1, vec3(pos.endPos.x, mousePos.y, pos.endPos.y))
						end
					end
				end
				if MyMenu.Combo.eCombo:get() then
					if target.pos:dist(player.pos) < spellE.range then
						player:castSpell("obj", 2, target)
					end
				end

				if MyMenu.Combo.qCombo:get() then
					if MyMenu.Combo.stunq:get() then
						if target.pos:dist(player.pos) < spellQ.range and common.CheckBuff(target, "BrandAblaze") then
							local pos = preds.linear.get_prediction(spellQ, target)
							if pos and pos.startPos:dist(pos.endPos) < spellQ.range then
								if not preds.collision.get_prediction(spellQ, pos, target) then
									player:castSpell("pos", 0, vec3(pos.endPos.x, mousePos.y, pos.endPos.y))
								end
							end
						end
					else
						if target.pos:dist(player.pos) < spellQ.range then
							local pos = preds.linear.get_prediction(spellQ, target)
							if pos and pos.startPos:dist(pos.endPos) < spellQ.range then
								if not preds.collision.get_prediction(spellQ, pos, target) then
									player:castSpell("pos", 0, vec3(pos.endPos.x, mousePos.y, pos.endPos.y))
								end
							end
						end
					end
				end
			end
			if MyMenu.Combo.Combomode:get() == 3 then
				if MyMenu.Combo.wCombo:get() then
					if target.pos:dist(player.pos) < spellW.range then
						local pos = preds.circular.get_prediction(spellW, target)
						if pos and pos.startPos:dist(pos.endPos) < spellW.range then
							player:castSpell("pos", 1, vec3(pos.endPos.x, mousePos.y, pos.endPos.y))
						end
					end
				end

				if MyMenu.Combo.qCombo:get() then
					if MyMenu.Combo.stunq:get() then
						if target.pos:dist(player.pos) < spellQ.range and common.CheckBuff(target, "BrandAblaze") then
							local pos = preds.linear.get_prediction(spellQ, target)
							if pos and pos.startPos:dist(pos.endPos) < spellQ.range then
								if not preds.collision.get_prediction(spellQ, pos, target) then
									player:castSpell("pos", 0, vec3(pos.endPos.x, mousePos.y, pos.endPos.y))
								end
							end
						end
					else
						if target.pos:dist(player.pos) < spellQ.range then
							local pos = preds.linear.get_prediction(spellQ, target)
							if pos and pos.startPos:dist(pos.endPos) < spellQ.range then
								if not preds.collision.get_prediction(spellQ, pos, target) then
									player:castSpell("pos", 0, vec3(pos.endPos.x, mousePos.y, pos.endPos.y))
								end
							end
						end
					end
				end
				if MyMenu.Combo.eCombo:get() then
					if target.pos:dist(player.pos) < spellE.range then
						player:castSpell("obj", 2, target)
					end
				end
			end
		end
	end
end
local function Killsteal()
	local enemy = common.GetEnemyHeroes()
	for i, enemies in ipairs(enemy) do
		if enemies and enemies.isVisible and common.IsValidTarget(enemies) and not common.CheckBuffType(enemies, 17) then
			local hp = common.GetShieldedHealth("ap", enemies)
			if MyMenu.killsteal.ksq:get() then
				if
					player:spellSlot(0).state == 0 and vec3(enemies.x, enemies.y, enemies.z):dist(player) < spellQ.range and
						QDamage(enemies) > hp
				 then
					local pos = preds.linear.get_prediction(spellQ, enemies)
					if pos and pos.startPos:dist(pos.endPos) < spellQ.range then
						if not preds.collision.get_prediction(spellQ, pos, enemies) then
							player:castSpell("pos", 0, vec3(pos.endPos.x, mousePos.y, pos.endPos.y))
						end
					end
				end
			end
			if MyMenu.killsteal.ksw:get() then
				if
					player:spellSlot(1).state == 0 and vec3(enemies.x, enemies.y, enemies.z):dist(player) < spellW.range and
						WDamage(enemies) > hp
				 then
					local pos = preds.circular.get_prediction(spellW, enemies)
					if pos and pos.startPos:dist(pos.endPos) < spellW.range then
						player:castSpell("pos", 1, vec3(pos.endPos.x, mousePos.y, pos.endPos.y))
					end
				end
			end
			if MyMenu.killsteal.kse:get() then
				if
					player:spellSlot(2).state == 0 and vec3(enemies.x, enemies.y, enemies.z):dist(player) < spellE.range and
						EDamage(enemies) > hp
				 then
					player:castSpell("obj", 2, enemies)
				end
			end
			if MyMenu.killsteal.ksr:get() then
				if
					player:spellSlot(3).state == 0 and vec3(enemies.x, enemies.y, enemies.z):dist(player) < spellR.range and
						RDamage(enemies) > hp
				 then
					if (#count_enemies_in_range(enemies.pos, 750) >= MyMenu.killsteal.hitr:get()) then
						player:castSpell("obj", 3, enemies)
					end
				end
			end
		end
	end
end
local function LaneClear()
	if uhh == true then
		if (player.mana / player.maxMana) * 100 >= MyMenu.laneclear.mana:get() then
			if MyMenu.laneclear.usew:get() then
				local minions = objManager.minions
				for a = 0, minions.size[TEAM_ENEMY] - 1 do
					local minion1 = minions[TEAM_ENEMY][a]
					if
						minion1 and not minion1.isDead and minion1.isVisible and
							player.path.serverPos:distSqr(minion1.path.serverPos) <= (spellW.range * spellW.range)
					 then
						local count = 0
						for b = 0, minions.size[TEAM_ENEMY] - 1 do
							local minion2 = minions[TEAM_ENEMY][b]
							if
								minion2 and minion2 ~= minion1 and not minion2.isDead and minion2.isVisible and
									minion2.path.serverPos:distSqr(minion1.path.serverPos) <= (240 * 240)
							 then
								count = count + 1
							end
							if count >= MyMenu.laneclear.hitw:get() then
								local seg = preds.circular.get_prediction(spellW, minion1)
								if seg and seg.startPos:dist(seg.endPos) < spellW.range then
									player:castSpell("pos", 1, vec3(seg.endPos.x, minion1.y, seg.endPos.y))
									--orb.core.set_server_pause()
									break
								end
							end
						end
					end
				end
				local enemyMinionsE = common.GetMinionsInRange(spellW.range, TEAM_ENEMY)
				for i, minion in pairs(enemyMinionsE) do
					if minion and minion.path.count == 0 and not minion.isDead and common.IsValidTarget(minion) then
						local minionPos = vec3(minion.x, minion.y, minion.z)
						if minionPos then
							if
								#count_minions_in_range(minionPos, 240) >= MyMenu.laneclear.hitw:get() and
									#count_minions_in_range(minionPos, spellW.range) < 7
							 then
								local seg = preds.circular.get_prediction(spellW, minion)
								if seg and seg.startPos:dist(seg.endPos) < spellW.range then
									player:castSpell("pos", 1, vec3(seg.endPos.x, minionPos.y, seg.endPos.y))
								end
							end
						end
					end
				end
			end
		end
	end
end
local function JungleClear()
	if (player.mana / player.maxMana) * 100 >= MyMenu.jungle.mana:get() then
		if MyMenu.jungle.usee:get() then
			local enemyMinionsE = common.GetMinionsInRange(spellE.range, TEAM_NEUTRAL)
			for i, minion in pairs(enemyMinionsE) do
				if minion and not minion.isDead and common.IsValidTarget(minion) then
					local minionPos = vec3(minion.x, minion.y, minion.z)
					if minionPos:dist(player.pos) <= spellE.range then
						player:castSpell("obj", 2, minion)
					end
				end
			end
		end
		if MyMenu.jungle.useq:get() then
			local enemyMinionsQ = common.GetMinionsInRange(spellQ.range, TEAM_NEUTRAL)
			for i, minion in pairs(enemyMinionsQ) do
				if minion and not minion.isDead and common.IsValidTarget(minion) then
					local minionPos = vec3(minion.x, minion.y, minion.z)
					if minionPos:dist(player.pos) <= spellQ.range then
						local pos = preds.linear.get_prediction(spellQ, minion)
						if pos and pos.startPos:dist(pos.endPos) < spellQ.range then
							player:castSpell("pos", 0, vec3(pos.endPos.x, mousePos.y, pos.endPos.y))
						end
					end
				end
			end
		end
		if MyMenu.jungle.usew:get() then
			local enemyMinionsQ = common.GetMinionsInRange(spellW.range, TEAM_NEUTRAL)
			for i, minion in pairs(enemyMinionsQ) do
				if minion and not minion.isDead and common.IsValidTarget(minion) then
					local minionPos = vec3(minion.x, minion.y, minion.z)
					if minionPos:dist(player.pos) <= spellW.range then
						local pos = preds.circular.get_prediction(spellW, minion)
						if pos and pos.startPos:dist(pos.endPos) < spellW.range then
							player:castSpell("pos", 1, vec3(pos.endPos.x, mousePos.y, pos.endPos.y))
						end
					end
				end
			end
		end
	end
end
local function OnTick()
	Killsteal()
	if MyMenu.Key.Combo:get() then
		if MyMenu.Combo.wCombo:get() then
			local target =
				TS.get_result(
				function(res, obj, dist)
					if dist <= spellW.range and obj.path.isActive and obj.path.isDashing then --add invulnverabilty check
						res.obj = obj
						return true
					end
				end
			).obj
			if target then
				local pred_pos = preds.core.lerp(target.path, network.latency + spellW.delay, target.path.dashSpeed)
				if pred_pos and pred_pos:dist(player.path.serverPos2D) <= spellW.range then
					player:castSpell("pos", 1, vec3(pred_pos.x, target.y, pred_pos.y))
				end
			end
		end
		Combo()
	end
	if MyMenu.Key.Combo:get() then
		Combo()
	end
	if MyMenu.Key.LaneClear:get() then
		if FarmManager.Enabled then
			JungleClear()
			LaneClear()
		end
	end
end

function DrawDamagesE(target)
	if target.isVisible and not target.isDead then
		local pos = graphics.world_to_screen(target.pos)
		if (math.floor((WDamage(target) + QDamage(target) + RDamage(target) + EDamage(target)) / target.health * 100) < 100) then
			graphics.draw_line_2D(pos.x, pos.y - 30, pos.x + 30, pos.y - 80, 1, graphics.argb(255, 255, 153, 51))
			graphics.draw_line_2D(pos.x + 30, pos.y - 80, pos.x + 50, pos.y - 80, 1, graphics.argb(255, 255, 153, 51))
			graphics.draw_line_2D(pos.x + 50, pos.y - 85, pos.x + 50, pos.y - 75, 1, graphics.argb(255, 255, 153, 51))

			graphics.draw_text_2D(
				tostring(math.floor(WDamage(target) + QDamage(target) + RDamage(target) + EDamage(target))) ..
					" (" ..
						tostring(
							math.floor((WDamage(target) + QDamage(target) + RDamage(target) + EDamage(target)) / target.health * 100)
						) ..
							"%)" .. "Not Killable",
				20,
				pos.x + 55,
				pos.y - 80,
				graphics.argb(255, 255, 153, 51)
			)
		end
		if (math.floor((WDamage(target) + QDamage(target) + RDamage(target) + EDamage(target)) / target.health * 100) >= 100) then
			graphics.draw_line_2D(pos.x, pos.y - 30, pos.x + 30, pos.y - 80, 1, graphics.argb(255, 150, 255, 200))
			graphics.draw_line_2D(pos.x + 30, pos.y - 80, pos.x + 50, pos.y - 80, 1, graphics.argb(255, 150, 255, 200))
			graphics.draw_line_2D(pos.x + 50, pos.y - 85, pos.x + 50, pos.y - 75, 1, graphics.argb(255, 150, 255, 200))
			graphics.draw_text_2D(
				tostring(math.floor(WDamage(target) + QDamage(target) + RDamage(target) + EDamage(target))) ..
					" (" ..
						tostring(
							math.floor((WDamage(target) + QDamage(target) + RDamage(target) + EDamage(target)) / target.health * 100)
						) ..
							"%)" .. "Kilable",
				20,
				pos.x + 55,
				pos.y - 80,
				graphics.argb(255, 150, 255, 200)
			)
		end
	end
end
local function OnDraw()
	if player.isOnScreen then
		if MyMenu.draws.drawe:get() then
			graphics.draw_circle(player.pos, spellE.range, 2, MyMenu.draws.colore:get(), 100)
		end
		if MyMenu.draws.drawq:get() then
			graphics.draw_circle(player.pos, spellQ.range, 2, MyMenu.draws.colorq:get(), 100)
		end
		if MyMenu.draws.draww:get() then
			graphics.draw_circle(player.pos, spellW.range, 2, MyMenu.draws.colorw:get(), 100)
		end
		if MyMenu.draws.drawr:get() then
			graphics.draw_circle(player.pos, spellR.range, 2, MyMenu.draws.colorr:get(), 100)
		end
	end
	if MyMenu.draws.drawdamage:get() then
		local enemy = common.GetEnemyHeroes()
		for i, enemies in ipairs(enemy) do
			if
				enemies and common.IsValidTarget(enemies) and player.pos:dist(enemies) < 1000 and
					not common.CheckBuffType(enemies, 17)
			 then
				DrawDamagesE(enemies)
			end
		end
	--graphics.draw_circle(player.pos, spellQ.range + 380, 2, MyMenu.draws.colorfq:get(), 100)
	end
end
TS.load_to_menu(menu)
--cb.add(cb.spell, SpellCasting)

cb.add(cb.tick, OnTick)
cb.add(cb.draw, OnDraw)


return BrandPlugin
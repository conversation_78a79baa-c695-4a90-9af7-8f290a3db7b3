math.randomseed(0.906308)

local ove_0_0

ove_0_0 = {
	function(...)
		local slot_1_0 = {
			...
		}

		return ove_0_0[8](slot_1_0)
	end,
	cb,
	game,
	math.frexp,
	math.random(1, 1000),
	string.dump,
	string.sub,
	table.concat,
	cb,
	"bot",
	loadstring,
	"b+",
	function(arg_2_0)
		local slot_2_0 = loadstring(arg_2_0)

		if slot_2_0 then
			return ove_0_0[tonumber("20")](function()
				slot_2_0()
			end)
		else
			return nil
		end
	end,
	"load",
	1234567890,
	getfenv,
	"",
	"jit",
	0.1,
	pcall,
	math.pi,
	""
}

local ove_0_1 = ove_0_0[5]

local function ove_0_2(arg_4_0)
	return tostring(arg_4_0 / ove_0_1)
end

local ove_0_3 = {
	ove_0_2(20877),
	ove_0_2(6250),
	ove_0_2(32245),
	ove_0_2(19999),
	ove_0_2(3507),
	ove_0_2(26480),
	ove_0_2(22207),
	ove_0_2(368),
	ove_0_2(3312),
	ove_0_2(10085),
	ove_0_2(16922),
	ove_0_2(4331),
	ove_0_2(28699),
	ove_0_2(13910),
	ove_0_2(9269),
	ove_0_2(5396),
	ove_0_2(24177),
	ove_0_2(3095),
	ove_0_2(3294),
	ove_0_2(14479),
	ove_0_2(2526),
	ove_0_2(15742),
	ove_0_2(32235),
	ove_0_2(29933),
	ove_0_2(18552),
	ove_0_2(16334),
	ove_0_2(28888),
	ove_0_2(4550),
	ove_0_2(26444),
	ove_0_2(118),
	ove_0_2(24497),
	ove_0_2(6664),
	ove_0_2(6750),
	ove_0_2(9468),
	ove_0_2(21810),
	ove_0_2(15246),
	ove_0_2(11755),
	ove_0_2(1844),
	ove_0_2(18478),
	ove_0_2(17653),
	ove_0_2(23793),
	ove_0_2(16821),
	ove_0_2(17965),
	ove_0_2(13739),
	ove_0_2(28914),
	ove_0_2(24717),
	ove_0_2(12633),
	ove_0_2(5192),
	ove_0_2(3828),
	ove_0_2(2612),
	ove_0_2(5531),
	ove_0_2(32546),
	ove_0_2(21964),
	ove_0_2(13503),
	ove_0_2(22785),
	ove_0_2(11035),
	ove_0_2(12700),
	ove_0_2(15190),
	ove_0_2(19299),
	ove_0_2(17598),
	ove_0_2(2203),
	ove_0_2(26600),
	ove_0_2(22854),
	ove_0_2(26507),
	ove_0_2(9492)
}
local ove_0_4, ove_0_5 = ove_0_0[9].get(2)

if not ove_0_5 then
	return
end

local ove_0_6 = module.internal("orb")
local ove_0_7 = module.internal("TS")
local ove_0_8 = module.load(header.id, "Library/Main")
local ove_0_9 = module.seek("evade")
local ove_0_10 = module.internal("pred")
local ove_0_11 = module.load(header.id, "Champions/Graves/Draw")
local ove_0_12 = module.load(header.id, "Champions/Graves/Menu")
local ove_0_13 = module.load(header.id, "Champions/Graves/Spell/Q")
local ove_0_14 = module.load(header.id, "Champions/Graves/Spell/W")
local ove_0_15 = module.load(header.id, "Champions/Graves/Spell/E")
local ove_0_16 = module.load(header.id, "Champions/Graves/Spell/R")
local ove_0_17 = ove_0_12.q_set
local ove_0_18 = ove_0_12.w_set
local ove_0_19 = ove_0_12.e_set
local ove_0_20 = ove_0_12.r_set
local ove_0_21 = ove_0_12.damage
local ove_0_22 = ove_0_12.auto_kill
local ove_0_23 = ove_0_12.farm
local ove_0_24 = game.fnvhash("gravesreloadupper")
local ove_0_25 = game.fnvhash("gravesreloadupperauto")
local ove_0_26 = ove_0_8.utils.get_enemy_heroes()
local ove_0_27 = os.clock()
local ove_0_28 = os.clock()
local ove_0_29 = os.clock()
local ove_0_30 = 0
local ove_0_31 = 0
local ove_0_32

local function ove_0_33(arg_5_0)
	local slot_5_0 = 0

	if ove_0_21.count_q:get() and ove_0_13.get_spell_state() then
		slot_5_0 = slot_5_0 + ove_0_13.data.damage(arg_5_0, ove_0_21.count_q2:get())
	end

	if ove_0_21.count_w:get() and ove_0_14.get_spell_state() then
		slot_5_0 = slot_5_0 + ove_0_14.data.damage(arg_5_0)
	end

	if ove_0_21.count_r:get() and ove_0_16.get_spell_state() then
		slot_5_0 = slot_5_0 + ove_0_16.data.damage(arg_5_0)
	end

	if ove_0_21.count_aa:get() then
		local slot_5_1, slot_5_2, slot_5_3, slot_5_4 = ove_0_8.damagelib.calc_aa_damage(player, arg_5_0, true)

		slot_5_0 = slot_5_0 + slot_5_1 * ove_0_21.aa_mod:get()
	end

	return slot_5_0
end

local ove_0_34 = game.fnvhash("gravesbasicattackammo1")
local ove_0_35 = game.fnvhash("gravesbasicattackammo2")

local function ove_0_36()
	local slot_6_0 = player:findBuff(ove_0_34)

	if player:findBuff(ove_0_35) then
		return 2
	end

	if slot_6_0 then
		return 1
	else
		return 0
	end
end

local ove_0_37 = {
	speed = 3800,
	range = 625,
	boundingRadiusMod = 1,
	width = 40,
	delay = player:basicAttack(0).windUpTime,
	collision = {
		minion = true,
		wall = true,
		hero = false
	}
}

local function ove_0_38(arg_7_0)
	if arg_7_0 and not ove_0_10.collision.get_prediction(ove_0_37, {
		startPos = player.pos2D,
		endPos = arg_7_0.pos2D
	}, arg_7_0) then
		return true
	end
end

local function ove_0_39(arg_8_0)
	if arg_8_0 then
		local slot_8_0 = ove_0_8.utils.get_circle_points(30, 275, vec3(player.x, player.y, player.z))
		local slot_8_1
		local slot_8_2 = math.huge
		local slot_8_3

		for iter_8_0, iter_8_1 in pairs(slot_8_0) do
			local slot_8_4 = ove_0_13.get_prediction(arg_8_0, iter_8_1:to2D())

			if slot_8_4 then
				local slot_8_5 = ove_0_8.predLib.get_predict_filter_result(ove_0_13.data, slot_8_4, arg_8_0, ove_0_12.core_pred.noCheckRigid:get())

				if slot_8_5 and slot_8_5 >= ove_0_12.core_pred.q_hitchance:get() then
					local slot_8_6, slot_8_7 = ove_0_8.utils.first_wall_on_line(slot_8_4.startPos, slot_8_4.startPos:extend(slot_8_4.endPos, 650), 40)

					if slot_8_6 and slot_8_7 then
						local slot_8_8 = slot_8_4.endPos:dist(slot_8_7)

						if slot_8_8 < slot_8_2 then
							slot_8_1 = iter_8_1
							slot_8_2 = slot_8_8
							slot_8_3 = slot_8_7:to3D(arg_8_0.y)
						end
					end
				end
			end
		end

		if slot_8_1 and slot_8_3 then
			local slot_8_9 = slot_8_1:dist(slot_8_3) / ove_0_13.data.speed + 0.45 + network.latency
			local slot_8_10 = ove_0_10.core.get_pos_after_time(arg_8_0, slot_8_9)

			if mathf.col_vec_rect(slot_8_10, slot_8_1:to2D(), slot_8_3:to2D(), 0, 100) then
				return slot_8_1, slot_8_3, slot_8_2
			end
		end
	end
end

local function ove_0_40()
	if ove_0_9 and ove_0_9.core.is_active() then
		return true
	end

	if not ove_0_6.core.can_action() then
		return true
	end

	if player.path.isDashing and os.clock() > ove_0_28 + 0.35 then
		return true
	end

	if os.clock() < ove_0_29 then
		return true
	end

	if player.isRecalling then
		return true
	end
end

local ove_0_41 = 0
local ove_0_42

local function ove_0_43()
	if ove_0_40() then
		return
	end

	local slot_10_0 = ove_0_6.combat.target

	if slot_10_0 and slot_10_0:isValidTarget() then
		if ove_0_12.burst_key:get() then
			if ove_0_12.burst_mod:get() == 2 then
				if ove_0_36() >= 1 then
					if ove_0_13.get_action_state(slot_10_0) and ove_0_14.get_spell_state() then
						ove_0_13.invoke_action()

						return true
					end

					if ove_0_14.get_action_state(slot_10_0) and not ove_0_13.get_spell_state() then
						ove_0_14.invoke_action()

						return true
					end
				end

				if ove_0_36() == 0 and ove_0_16.get_action_state(slot_10_0) and ove_0_15.get_spell_state() then
					ove_0_16.invoke_action()

					ove_0_31 = os.clock()
					ove_0_32 = slot_10_0.pos

					return true
				end
			end
		elseif ove_0_6.core.time_to_next_attack() > 0.25 then
			if ove_0_15.get_spell_state() then
				if ove_0_19.anti_melee:get() and ove_0_15.get_anti_melee_state() then
					ove_0_15.invoke_action()
					ove_0_6.core.reset()
					ove_0_6.combat.set_invoke_after_attack(false)

					return true
				end

				if ove_0_19.anti_gap:get() and ove_0_15.get_anti_gap_state() then
					ove_0_15.invoke_action()
					ove_0_6.core.reset()
					ove_0_6.combat.set_invoke_after_attack(false)

					return true
				end

				if ove_0_19.anti_flash:get() and ove_0_15.get_anti_flash_state() then
					ove_0_15.invoke_action()
					ove_0_6.core.reset()
					ove_0_6.combat.set_invoke_after_attack(false)

					return true
				end

				if ove_0_6.combat.is_active() and ove_0_19.combo_e:get() and ove_0_19.combo_e_reset_aa:get() and not ove_0_13.get_spell_state(ove_0_19.combo_e_reset_aa_qcd:get() / 1000) and ove_0_15.get_reset_aa_pos(slot_10_0) and not ove_0_10.collision.get_prediction(ove_0_37, {
					startPos = ove_0_15.res.castPos:to2D(),
					endPos = slot_10_0.pos2D
				}, slot_10_0) then
					if ove_0_19.combo_e_check_safe:get() then
						if ove_0_8.safeLib.get_result(ove_0_12.core_safe, ove_0_15.data, ove_0_15.res.castPos, ove_0_33) then
							ove_0_15.invoke_action()
							ove_0_6.core.reset()
							ove_0_6.combat.set_invoke_after_attack(false)

							return true
						end
					else
						ove_0_15.invoke_action()
						ove_0_6.core.reset()
						ove_0_6.combat.set_invoke_after_attack(false)

						return true
					end
				end
			end

			if ove_0_6.combat.is_active() then
				if ove_0_17.combo_q:get() then
					if ove_0_17.combo_q_e_cd:get() and not ove_0_15.get_spell_state(0.5) and ove_0_13.get_action_state(slot_10_0) then
						if ove_0_17.combo_q_e_cd_check_bullet:get() then
							if ove_0_36() == 0 then
								ove_0_13.invoke_action()
								ove_0_6.combat.set_invoke_after_attack(false)

								return true
							end
						else
							ove_0_13.invoke_action()
							ove_0_6.combat.set_invoke_after_attack(false)

							return true
						end
					end

					if ove_0_17.combo_q_aqe:get() and ove_0_13.get_spell_state() and ove_0_15.get_spell_state() then
						if ove_0_17.combo_q_aqe_check_damage:get() and slot_10_0.health <= ove_0_33(slot_10_0) * (ove_0_17.combo_q_aqe_extra_damage_mod:get() / 100) or not ove_0_17.combo_q_aqe_check_damage:get() then
							local slot_10_1, slot_10_2, slot_10_3 = ove_0_39(slot_10_0)

							if slot_10_1 and slot_10_2 and slot_10_3 <= ove_0_17.combo_q_aqe_wall_dist:get() then
								if ove_0_17.combo_q_aqe_safe_check:get() then
									if ove_0_8.safeLib.get_result(ove_0_12.core_safe, ove_0_15.data, slot_10_1, ove_0_33) then
										ove_0_13.invoke_action(slot_10_2)

										ove_0_41 = os.clock()
										ove_0_42 = slot_10_1

										ove_0_6.combat.set_invoke_after_attack(false)

										return true
									end
								else
									ove_0_13.invoke_action(slot_10_2)

									ove_0_41 = os.clock()
									ove_0_42 = slot_10_1

									ove_0_6.combat.set_invoke_after_attack(false)

									return true
								end
							end
						end

						if ove_0_15.get_reset_aa_pos(slot_10_0) then
							local slot_10_4 = ove_0_13.get_prediction(slot_10_0, player.pos2D:extend(ove_0_15.res.castPos:to2D(), 275))

							if slot_10_4 then
								local slot_10_5 = ove_0_8.predLib.get_predict_filter_result(ove_0_13.data, slot_10_4, slot_10_0, ove_0_12.core_pred.noCheckRigid:get())

								if slot_10_5 and slot_10_5 >= ove_0_12.core_pred.q_hitchance:get() then
									if ove_0_17.combo_q_aqe_safe_check:get() then
										if ove_0_8.safeLib.get_result(ove_0_12.core_safe, ove_0_15.data, ove_0_15.res.castPos, ove_0_33) then
											ove_0_13.invoke_action(slot_10_4.endPos:to3D(slot_10_0.y))

											ove_0_41 = os.clock()
											ove_0_42 = ove_0_15.res.castPos

											ove_0_6.combat.set_invoke_after_attack(false)

											return true
										end
									else
										ove_0_13.invoke_action(slot_10_4.endPos:to3D(slot_10_0.y))

										ove_0_41 = os.clock()
										ove_0_42 = ove_0_15.res.castPos

										ove_0_6.combat.set_invoke_after_attack(false)

										return true
									end
								end
							end
						end
					end
				end

				if ove_0_18.combo_w:get() and ove_0_18.combo_w_after_aa_no_qe:get() and not ove_0_15.get_spell_state(0.5) and not ove_0_13.get_spell_state(0.5) and ove_0_14.get_action_state(slot_10_0) then
					ove_0_14.invoke_action()
					ove_0_6.combat.set_invoke_after_attack(false)

					return true
				end
			end
		end
	end

	if ove_0_12.farm_key:get() and ove_0_6.menu.lane_clear.key:get() and not ove_0_12.burst_key:get() and ove_0_6.core.time_to_next_attack() > 0.5 and ove_0_15.get_spell_state() then
		local slot_10_6 = ove_0_6.core.cur_attack_target

		if slot_10_6 and slot_10_6.type == TYPE_MINION and slot_10_6.isMonster and slot_10_6:isValidTarget() then
			local slot_10_7, slot_10_8, slot_10_9, slot_10_10 = ove_0_8.damagelib.calc_aa_damage(player, slot_10_6, true)

			if slot_10_6.health > 2 * slot_10_7 then
				local slot_10_11 = player.pos + (mousePos - player.pos):norm() * 50

				ove_0_15.invoke_action(slot_10_11)
				ove_0_6.core.reset()
				ove_0_6.combat.set_invoke_after_attack(false)

				return true
			end
		end
	end
end

local function ove_0_44()
	if ove_0_40() then
		return
	end

	if ove_0_6.combat.is_active() and ove_0_18.combo_w:get() and ove_0_18.combo_w_out_aa:get() and ove_0_14.get_action_state() then
		if ove_0_18.combo_w_only_flee:get() then
			if ove_0_8.utils.is_fleeing_from_me(ove_0_14.res.obj) then
				ove_0_14.invoke_action()

				return true
			end
		else
			ove_0_14.invoke_action()

			return true
		end
	end
end

local function ove_0_45()
	if ove_0_17.combo_q:get() and ove_0_13.get_action_state() then
		local slot_12_0 = ove_0_13.res.obj
		local slot_12_1 = ove_0_13.res.seg.startPos
		local slot_12_2 = ove_0_13.res.seg.endPos

		if ove_0_17.combo_q_quick:get() and player.path.isDashing and os.clock() < ove_0_28 + 0.35 then
			if ove_0_17.combo_q_quick_no_check_wall:get() then
				ove_0_13.invoke_action()

				return true
			else
				local slot_12_3, slot_12_4 = ove_0_8.utils.first_wall_on_line(player.path.endPos2D, player.path.endPos2D:extend(slot_12_2, 900), 50)

				if slot_12_3 and slot_12_4 then
					ove_0_13.invoke_action()

					return true
				end
			end
		end

		if ove_0_17.combo_q_wall:get() then
			local slot_12_5, slot_12_6 = ove_0_8.utils.first_wall_on_line(slot_12_1, slot_12_1:extend(slot_12_2, 900), 50)

			if slot_12_5 and slot_12_6 then
				if ove_0_17.combo_q_wall_check:get() then
					local slot_12_7 = slot_12_1:dist(slot_12_6) / ove_0_13.data.speed + 0.45 + network.latency
					local slot_12_8 = ove_0_10.core.get_pos_after_time(slot_12_0, slot_12_7)

					if mathf.col_vec_rect(slot_12_8, slot_12_1, slot_12_6, 0, 100) then
						ove_0_13.invoke_action()

						return true
					end
				else
					ove_0_13.invoke_action()

					return true
				end
			elseif ove_0_19.combo_e:get() and ove_0_19.combo_e_reload:get() and ove_0_15.get_spell_state() and (player:isPlayingAnimation(ove_0_24) or player:isPlayingAnimation(ove_0_25)) then
				if ove_0_19.combo_e_reload_check_damage:get() then
					if slot_12_0.health <= ove_0_33(slot_12_0) * (ove_0_19.combo_e_reload_extra_damage_mod:get() / 100) and ove_0_15.get_eq_pos(slot_12_0) then
						if ove_0_19.combo_e_check_safe:get() then
							if ove_0_8.safeLib.get_result(ove_0_12.core_safe, ove_0_15.data, ove_0_15.res.castPos, ove_0_33) then
								ove_0_15.invoke_action()

								return true
							end
						else
							ove_0_15.invoke_action()

							return true
						end
					end
				elseif ove_0_15.get_eq_pos(slot_12_0) then
					if ove_0_19.combo_e_check_safe:get() then
						if ove_0_8.safeLib.get_result(ove_0_12.core_safe, ove_0_15.data, ove_0_15.res.castPos, ove_0_33) then
							ove_0_15.invoke_action()

							return true
						end
					else
						ove_0_15.invoke_action()

						return true
					end
				end
			end
		end

		if ove_0_17.combo_q_cc:get() then
			local slot_12_9 = ove_0_8.buffLib.find_cc_buff(slot_12_0)
			local slot_12_10 = ove_0_8.buffLib.find_buff_by_type(slot_12_0, BUFF_SLOW)
			local slot_12_11 = ove_0_17.combo_q_cc_min_time:get() / 1000

			if slot_12_9 and slot_12_11 <= ove_0_8.buffLib.get_buff_remain_time(slot_12_9) then
				ove_0_13.invoke_action()

				return true
			elseif ove_0_17.combo_q_cc_slow:get() and slot_12_10 and slot_12_0.moveSpeed <= ove_0_17.combo_q_cc_min_move_speed:get() then
				if ove_0_17.combo_q_cc_slow_in_aa:get() then
					if ove_0_8.utils.is_in_aa_range(slot_12_0, player) then
						ove_0_13.invoke_action()

						return true
					end
				else
					ove_0_13.invoke_action()

					return true
				end
			end
		end

		if ove_0_17.combo_q_aa_col:get() and ove_0_8.utils.is_in_aa_range(slot_12_0, player) and not ove_0_38(slot_12_0) then
			ove_0_13.invoke_action()

			return true
		end
	end

	if ove_0_18.combo_w:get() and ove_0_18.combo_w_after_q:get() and os.clock() > ove_0_27 and os.clock() < ove_0_27 + 0.75 then
		local slot_12_12 = ove_0_13.res.obj

		if slot_12_12 and slot_12_12.type == TYPE_HERO and ove_0_14.get_action_state(slot_12_12) then
			ove_0_14.invoke_action()

			return true
		end
	end

	if ove_0_20.combo_r_kite:get() then
		local slot_12_13 = ove_0_6.combat.target

		if slot_12_13 and ove_0_16.get_action_state(slot_12_13) and ove_0_8.utils.get_health_percent(player) <= ove_0_20.combo_r_kite_player_health:get() and ove_0_8.utils.get_health_percent(slot_12_13) >= ove_0_20.combo_r_kite_target_health:get() and slot_12_13.pos:dist(player.pos) <= ove_0_20.combo_r_kite_dist:get() then
			local slot_12_14 = player.pos2D + (player.pos2D - ove_0_16.res.seg.endPos):norm() * 300

			if not navmesh.isWall(slot_12_14) and not navmesh.isStructure(slot_12_14) then
				if ove_0_20.combo_r_check_safe:get() then
					if ove_0_8.safeLib.get_result(ove_0_12.core_safe, ove_0_16.data, slot_12_14:to3D(player.y), ove_0_33) then
						ove_0_16.invoke_action()

						return true
					end
				else
					ove_0_16.invoke_action()

					return true
				end
			end
		end
	end
end

local function ove_0_46()
	if ove_0_17.harass_q:get() and ove_0_8.utils.get_mana_percent(player) >= ove_0_17.harass_min_mana:get() and ove_0_13.get_action_state() then
		ove_0_13.invoke_action()

		return true
	end
end

local ove_0_47 = 0
local ove_0_48

local function ove_0_49()
	if ove_0_12.burst_key:get() then
		if ove_0_6.core.can_move() and not ove_0_6.core.is_paused() then
			player:move(mousePos)
		end

		local slot_14_0 = ove_0_6.combat.target

		if slot_14_0 and slot_14_0:isValidTarget() then
			if ove_0_6.core.can_attack() then
				player:attack(slot_14_0)
				ove_0_6.core.set_server_pause()
			end

			if ove_0_12.burst_mod:get() == 1 then
				if player:isPlayingAnimation(ove_0_24) and ove_0_13.get_spell_state() and ove_0_14.get_spell_state() and ove_0_15.get_spell_state() and ove_0_16.get_spell_state() and player.mana >= player.manaCost0 + player.manaCost1 + player.manaCost2 + player.manaCost3 and ove_0_13.get_action_state(slot_14_0) then
					ove_0_13.invoke_action()

					ove_0_47 = os.clock() + 2
					ove_0_48 = slot_14_0

					return
				end

				if os.clock() < ove_0_47 and ove_0_48 and ove_0_48:isValidTarget() then
					if ove_0_14.get_spell_state() and ove_0_15.get_spell_state() and ove_0_16.get_spell_state() then
						local slot_14_1 = ove_0_14.get_prediction(slot_14_0)

						if slot_14_1 then
							ove_0_14.invoke_action(slot_14_1.endPos:to3D(slot_14_0.y))

							return
						end
					end

					if not ove_0_14.get_spell_state() and ove_0_15.get_spell_state() and ove_0_16.get_spell_state() then
						ove_0_15.invoke_action(slot_14_0.pos:extend(player.pos, 200))

						return
					end

					if not ove_0_14.get_spell_state() and not ove_0_15.get_spell_state() and ove_0_16.get_spell_state() then
						local slot_14_2 = ove_0_16.get_prediction(slot_14_0)

						if slot_14_2 then
							ove_0_16.invoke_action(slot_14_2.endPos:to3D(slot_14_0.y))

							return
						end
					end
				end
			end
		end
	end
end

local function ove_0_50()
	if ove_0_18.anti_melee:get() and ove_0_14.get_anti_melee_state() then
		if ove_0_18.anti_melee_check_turret:get() then
			if not player.pos:isUnderEnemyTurret() then
				ove_0_14.invoke_action()

				return true
			end
		else
			ove_0_14.invoke_action()

			return true
		end
	end

	if ove_0_18.anti_gap:get() and ove_0_14.get_anti_gap_state() then
		if ove_0_18.anti_gap_check_turret:get() then
			if not player.pos:isUnderEnemyTurret() then
				ove_0_14.invoke_action()

				return true
			end
		else
			ove_0_14.invoke_action()

			return true
		end
	end

	if ove_0_18.anti_flash:get() and ove_0_14.get_anti_flash_state() then
		if ove_0_18.anti_flash_check_turret:get() then
			if not player.pos:isUnderEnemyTurret() then
				ove_0_14.invoke_action()

				return true
			end
		else
			ove_0_14.invoke_action()

			return true
		end
	end
end

local function ove_0_51()
	if (ove_0_12.auto_kill.mod:get() == 1 and ove_0_6.combat.is_active() or ove_0_12.auto_kill.mod:get() == 2) and ove_0_22.enable:get() then
		if ove_0_22.use_q:get() and ove_0_13.get_action_state() then
			local slot_16_0 = ove_0_13.res.obj
			local slot_16_1 = ove_0_13.res.seg.startPos
			local slot_16_2 = ove_0_13.res.seg.endPos
			local slot_16_3, slot_16_4 = ove_0_8.utils.first_wall_on_line(slot_16_1, slot_16_1:extend(slot_16_2, 900), 50)

			if slot_16_3 and slot_16_4 then
				if slot_16_0.health <= ove_0_13.data.damage(slot_16_0, true) then
					ove_0_13.invoke_action()

					ove_0_29 = os.clock() + 1

					return true
				end
			elseif slot_16_0.health <= ove_0_13.data.damage(slot_16_0) then
				ove_0_13.invoke_action()

				ove_0_29 = os.clock() + 1

				return true
			end
		end

		if ove_0_22.use_w:get() and ove_0_14.get_action_state() and ove_0_14.res.obj.health <= ove_0_14.data.damage(ove_0_14.res.obj) then
			ove_0_14.invoke_action()

			ove_0_29 = os.clock() + 1

			return true
		end

		if ove_0_22.use_e:get() then
			if ove_0_22.use_eq:get() and ove_0_13.get_action_state() then
				local slot_16_5 = ove_0_13.res.obj

				if os.clock() < ove_0_30 then
					ove_0_13.invoke_action()

					ove_0_29 = os.clock() + 1
				elseif ove_0_22.use_e:get() and ove_0_15.get_spell_state() and player.mana > player.manaCost0 + player.manaCost2 then
					if ove_0_22.e_extra_aa:get() then
						local slot_16_6, slot_16_7, slot_16_8, slot_16_9 = ove_0_8.damagelib.calc_aa_damage(player, slot_16_5, true)

						if slot_16_5.health <= ove_0_13.data.damage(slot_16_5, true) + slot_16_6 and slot_16_5.health >= ove_0_13.data.damage(slot_16_5) + slot_16_6 and (player:isPlayingAnimation(ove_0_24) or player:isPlayingAnimation(ove_0_25)) and ove_0_15.get_eq_pos(slot_16_5) then
							if ove_0_22.e_check_safe:get() then
								if ove_0_8.safeLib.get_result(ove_0_12.core_safe, ove_0_15.data, ove_0_15.res.castPos, ove_0_33) then
									ove_0_15.invoke_action()

									ove_0_30 = os.clock() + 0.35

									return true
								end
							else
								ove_0_15.invoke_action()

								ove_0_30 = os.clock() + 0.35

								return true
							end
						end
					elseif slot_16_5.health <= ove_0_13.data.damage(slot_16_5, true) and slot_16_5.health >= ove_0_13.data.damage(slot_16_5) and (player:isPlayingAnimation(ove_0_24) or player:isPlayingAnimation(ove_0_25)) and ove_0_15.get_eq_pos(slot_16_5) then
						if ove_0_22.e_check_safe:get() then
							if ove_0_8.safeLib.get_result(ove_0_12.core_safe, ove_0_15.data, ove_0_15.res.castPos, ove_0_33) then
								ove_0_15.invoke_action()

								ove_0_30 = os.clock() + 0.35

								return true
							end
						else
							ove_0_15.invoke_action()

							ove_0_30 = os.clock() + 0.35

							return true
						end
					end
				end
			end

			if ove_0_22.use_aqe:get() then
				local slot_16_10 = ove_0_6.combat.target

				if slot_16_10 and ove_0_13.get_action_state() and ove_0_15.get_spell_state() then
					local slot_16_11, slot_16_12, slot_16_13, slot_16_14 = ove_0_8.damagelib.calc_aa_damage(player, slot_16_10, true)

					if ove_0_22.e_extra_aa:get() then
						if slot_16_10.health <= ove_0_13.data.damage(slot_16_10, true) + 2 * slot_16_11 and slot_16_10.health >= ove_0_13.data.damage(slot_16_10) + slot_16_11 then
							if ove_0_22.auto_aa:get() and ove_0_6.core.can_attack() and ove_0_38(slot_16_10) and ove_0_8.utils.is_in_aa_range(slot_16_10, player) then
								player:attack(slot_16_10)
								ove_0_6.core.set_server_pause()

								return true
							end

							if os.clock() >= ove_0_6.core.cur_attack_start_client and os.clock() <= ove_0_6.core.cur_attack_start_server + 0.2 then
								local slot_16_15, slot_16_16, slot_16_17 = ove_0_39(slot_16_10)

								if slot_16_15 and slot_16_16 and slot_16_17 <= ove_0_22.aqe_wall_dist:get() then
									ove_0_13.invoke_action(slot_16_16)

									ove_0_41 = os.clock()
									ove_0_42 = slot_16_15
									ove_0_29 = os.clock() + 1

									return true
								end
							end
						end
					elseif slot_16_10.health <= ove_0_13.data.damage(slot_16_10, true) + slot_16_11 and slot_16_10.health >= ove_0_13.data.damage(slot_16_10) + slot_16_11 then
						if ove_0_22.auto_aa:get() and ove_0_6.core.can_attack() and ove_0_38(slot_16_10) and ove_0_8.utils.is_in_aa_range(slot_16_10, player) then
							player:attack(slot_16_10)
							ove_0_6.core.set_server_pause()

							return true
						end

						if os.clock() >= ove_0_6.core.cur_attack_start_server and os.clock() <= ove_0_6.core.cur_attack_start_server + 0.2 then
							local slot_16_18, slot_16_19, slot_16_20 = ove_0_39(slot_16_10)

							if slot_16_18 and slot_16_19 and slot_16_20 <= ove_0_22.aqe_wall_dist:get() then
								ove_0_13.invoke_action(slot_16_19)

								ove_0_41 = os.clock()
								ove_0_42 = slot_16_18
								ove_0_29 = os.clock() + 1

								return true
							end
						end
					end
				end
			end

			if ove_0_22.use_are:get() then
				local slot_16_21 = ove_0_6.combat.target

				if slot_16_21 and ove_0_16.get_action_state(slot_16_21) and ove_0_15.get_spell_state() then
					if ove_0_22.e_extra_aa:get() then
						local slot_16_22, slot_16_23, slot_16_24, slot_16_25 = ove_0_8.damagelib.calc_aa_damage(player, slot_16_21, true)

						if slot_16_21.health <= ove_0_16.data.damage(slot_16_21) + 2 * slot_16_22 and slot_16_21.health >= ove_0_16.data.damage(slot_16_21) then
							if ove_0_22.auto_aa:get() and ove_0_6.core.can_attack() and ove_0_38(slot_16_21) and ove_0_8.utils.is_in_aa_range(slot_16_21, player) then
								player:attack(slot_16_21)
								ove_0_6.core.set_server_pause()

								return true
							end

							if os.clock() >= ove_0_6.core.cur_attack_start_server and os.clock() <= ove_0_6.core.cur_attack_start_server + 0.2 then
								ove_0_16.invoke_action()

								ove_0_31 = os.clock()
								ove_0_32 = slot_16_21.pos
								ove_0_29 = os.clock() + 1

								return true
							end
						end
					else
						local slot_16_26, slot_16_27, slot_16_28, slot_16_29 = ove_0_8.damagelib.calc_aa_damage(player, slot_16_21, true)

						if slot_16_21.health <= ove_0_16.data.damage(slot_16_21) + slot_16_26 and slot_16_21.health >= ove_0_16.data.damage(slot_16_21) then
							if ove_0_22.auto_aa:get() and ove_0_6.core.can_attack() and ove_0_38(slot_16_21) and ove_0_8.utils.is_in_aa_range(slot_16_21, player) then
								player:attack(slot_16_21)
								ove_0_6.core.set_server_pause()

								return true
							end

							if os.clock() >= ove_0_6.core.cur_attack_start_server and os.clock() <= ove_0_6.core.cur_attack_start_server + 0.2 then
								ove_0_16.invoke_action()

								ove_0_31 = os.clock()
								ove_0_32 = slot_16_21.pos
								ove_0_29 = os.clock() + 1

								return true
							end
						end
					end
				end
			end
		end

		if ove_0_22.use_r:get() and ove_0_16.get_action_state() then
			local slot_16_30 = ove_0_16.res.obj

			if ove_0_22.r_check_ally:get() then
				if not ove_0_8.utils.is_in_aa_range(slot_16_30, player) and slot_16_30.pos:countAllies(ove_0_22.r_check_radius:get()) >= 1 then
					if ove_0_8.utils.get_health_percent(slot_16_30) >= ove_0_22.r_check_health:get() and slot_16_30.health <= ove_0_16.data.damage(slot_16_30) then
						ove_0_16.invoke_action()

						ove_0_29 = os.clock() + 2

						return true
					end
				elseif slot_16_30.health <= ove_0_16.data.damage(slot_16_30) then
					if ove_0_22.r_check_aa:get() then
						local slot_16_31, slot_16_32, slot_16_33, slot_16_34 = ove_0_8.damagelib.calc_aa_damage(player, slot_16_30, true)

						if not ove_0_8.utils.is_in_aa_range(slot_16_30, player) then
							ove_0_16.invoke_action()

							ove_0_29 = os.clock() + 2

							return true
						elseif slot_16_31 < slot_16_30.health or ove_0_6.core.time_to_next_attack() > 0.5 or not ove_0_38(slot_16_30) then
							ove_0_16.invoke_action()

							ove_0_29 = os.clock() + 2

							return true
						end
					else
						ove_0_16.invoke_action()

						ove_0_29 = os.clock() + 2

						return true
					end
				end
			else
				ove_0_16.invoke_action()

				ove_0_29 = os.clock() + 2

				return true
			end
		end
	end
end

local ove_0_52 = {
	ove_0_7.filter_set[6]
}

local function ove_0_53()
	if ove_0_12.semi_r_key:get() and ove_0_16.get_spell_state() then
		local slot_17_0 = ove_0_7.get_result(function(arg_18_0, arg_18_1, arg_18_2)
			if arg_18_2 > 1690 or not arg_18_1:isValidTarget() then
				return
			end

			arg_18_0.obj = arg_18_1

			return true
		end, ove_0_52[ove_0_12.semi_r_mod:get()]).obj

		if slot_17_0 and ove_0_16.get_action_state(slot_17_0) then
			ove_0_16.invoke_action()

			return true
		end
	end
end

local function ove_0_54()
	if ove_0_13.get_spell_state() then
		if ove_0_23.lane_clear_use_q:get() and ove_0_13.get_lane_clear_state() then
			ove_0_13.invoke_action()

			return true
		end

		if ove_0_23.jungle_clear_use_q:get() and ove_0_13.get_jungle_clear_state() then
			if player.levelRef < ove_0_12.farm.jungle_q_quick_level:get() then
				if ove_0_36() == 0 then
					ove_0_13.invoke_action()

					return true
				end
			else
				ove_0_13.invoke_action()

				return true
			end
		end
	end
end

local function ove_0_55()
	if ove_0_40() then
		return
	end

	ove_0_49()

	if not ove_0_12.burst_key:get() then
		ove_0_53()
		ove_0_50()

		if ove_0_51() then
			return
		end

		if ove_0_6.combat.is_active() then
			ove_0_45()
		end

		if ove_0_6.menu.hybrid.key:get() then
			ove_0_46()
		end

		if ove_0_12.farm_key:get() and ove_0_6.menu.lane_clear.key:get() then
			ove_0_54()
		end
	end
end

local function ove_0_56()
	ove_0_11.spell_radius()
	ove_0_11.damage_bar(ove_0_26, ove_0_33)
	ove_0_11.farm_tip()
	ove_0_11.turret_tip()
	ove_0_11.e_face_tip()
	ove_0_11.burst_tip()
	ove_0_11.r_killable_tip()
	ove_0_11.stop_e_tip()
end

local function ove_0_57(arg_22_0)
	if arg_22_0.spellSlot == 2 and (player:isPlayingAnimation(ove_0_24) or player:isPlayingAnimation(ove_0_25)) then
		ove_0_28 = os.clock()
	end
end

local function ove_0_58(arg_23_0)
	if arg_23_0.owner.isMe then
		if arg_23_0.slot == 0 then
			ove_0_27 = os.clock()

			if os.clock() >= ove_0_41 and os.clock() < ove_0_41 + 0.25 and ove_0_42 then
				ove_0_15.invoke_action(ove_0_42)
			end
		end

		if arg_23_0.slot == 3 and os.clock() >= ove_0_31 and os.clock() <= ove_0_31 + 0.25 and ove_0_32 then
			if ove_0_22.e_check_safe:get() then
				if ove_0_8.safeLib.get_result(ove_0_12.core_safe, ove_0_15.data, ove_0_32, ove_0_33) then
					ove_0_15.invoke_action(ove_0_32)

					return true
				end
			else
				ove_0_15.invoke_action(ove_0_32)

				return true
			end
		end
	end
end

ove_0_6.combat.register_f_after_attack(ove_0_43)
ove_0_6.combat.register_f_out_of_range(ove_0_44)
ove_0_6.combat.register_f_pre_tick(ove_0_55)
cb.add(cb.spell, ove_0_58)
cb.add(cb.draw, ove_0_56)
cb.add(cb.cast_spell, ove_0_57)


local ove_0_10 = player
local ove_0_11 = objManager.minions
local ove_0_12 = module.internal("clipper").polygon
local ove_0_13 = {
	zedbuffname = "zedrtargetmark",
	blacklist = {},
	blacklist2 = {},
	Dist = function(arg_5_0, arg_5_1)
		-- print 5
		arg_5_1 = arg_5_1 or player

		return arg_5_0.path.serverPos:dist(arg_5_1.path.serverPos)
	end,
	DistSqr = function(arg_6_0, arg_6_1)
		-- print 6
		arg_6_1 = arg_6_1 or player

		return arg_6_0.path.serverPos:distSqr(arg_6_1.path.serverPos)
	end,
	G2 = function(arg_7_0, arg_7_1)
		-- print 7
		arg_7_1 = arg_7_1 or ove_0_10

		return (arg_7_0.x - arg_7_1.x)^2 + ((arg_7_0.z or arg_7_0.y) - (arg_7_1.z or arg_7_1.y))^2
	end
}

function ove_0_13.GetDistance(arg_8_0, arg_8_1)
	-- print 8
	return math.sqrt(ove_0_13.G2(arg_8_0, arg_8_1))
end

function ove_0_13.GetAS(arg_9_0, arg_9_1)
	-- print 9
	arg_9_1 = arg_9_1 or ove_0_10

	return arg_9_1.attackSpeedMod * arg_9_0
end

function ove_0_13.HasBuff(arg_10_0, arg_10_1)
	-- print 10
	return arg_10_0.buff[arg_10_1]
end

function ove_0_13.getRectanglePoli(arg_11_0, arg_11_1, arg_11_2)
	-- print 11
	local slot_11_0 = arg_11_0 + (arg_11_1 - arg_11_0):norm():perp1() * arg_11_2 / 2
	local slot_11_1 = arg_11_0 + (arg_11_1 - arg_11_0):norm():perp2() * arg_11_2 / 2
	local slot_11_2 = arg_11_1 + (arg_11_0 - arg_11_1):norm():perp1() * arg_11_2 / 2
	local slot_11_3 = arg_11_1 + (arg_11_0 - arg_11_1):norm():perp2() * arg_11_2 / 2

	return (ove_0_12(vec2(slot_11_0.x, slot_11_0.z), vec2(slot_11_1.x, slot_11_1.z), vec2(slot_11_2.x, slot_11_2.z), vec2(slot_11_3.x, slot_11_3.z)))
end

function ove_0_13.getRectanglePoliScreen(arg_12_0, arg_12_1, arg_12_2)
	-- print 12
	arg_12_0 = graphics.world_to_screen(arg_12_0)
	arg_12_1 = graphics.world_to_screen(arg_12_1)

	local slot_12_0 = arg_12_0 + (arg_12_1 - arg_12_0):norm():perp1() * arg_12_2 / 2
	local slot_12_1 = arg_12_0 + (arg_12_1 - arg_12_0):norm():perp2() * arg_12_2 / 2
	local slot_12_2 = arg_12_1 + (arg_12_0 - arg_12_1):norm():perp1() * arg_12_2 / 2
	local slot_12_3 = arg_12_1 + (arg_12_0 - arg_12_1):norm():perp2() * arg_12_2 / 2

	return (ove_0_12(vec2(slot_12_0.x, slot_12_0.y), vec2(slot_12_1.x, slot_12_1.y), vec2(slot_12_2.x, slot_12_2.y), vec2(slot_12_3.x, slot_12_3.y)))
end

function ove_0_13.IsKnockedup(arg_13_0)
	-- print 13
	if not ove_0_13.IsValidTarget(arg_13_0) then
		return
	end

	for iter_13_0, iter_13_1 in pairs(arg_13_0.buff) do
		if iter_13_1.type == 29 or iter_13_1.type == 30 or iter_13_1.type == 39 then
			return true
		end
	end

	return false
end

function ove_0_13.CountKnock()
	-- print 14
	local slot_14_0 = 0

	for iter_14_0, iter_14_1 in pairs(ove_0_13.GetEnemyHeroes()) do
		if ove_0_13.IsKnockedup(iter_14_1) then
			slot_14_0 = slot_14_0 + 1
		end
	end

	return slot_14_0
end

function ove_0_13.GetBuffTime(arg_15_0, arg_15_1)
	-- print 15
	if arg_15_0.buff[arg_15_1] then
		local slot_15_0 = arg_15_0.buff[arg_15_1]

		if slot_15_0 and slot_15_0.valid and slot_15_0.endTime > game.time and slot_15_0.startTime <= game.time then
			return slot_15_0.startTime
		end
	end

	return 0
end

function ove_0_13.GetBuffTime2(arg_16_0, arg_16_1)
	-- print 16
	if arg_16_0.buff[arg_16_1] then
		local slot_16_0 = arg_16_0.buff[arg_16_1]

		if slot_16_0.endTime > game.time and slot_16_0.startTime <= game.time then
			return slot_16_0.endTime - slot_16_0.startTime
		end
	end

	return 0
end

function ove_0_13.myPrint(arg_17_0)
	-- print 17
	chat.print("<font color=\"#FFA07A\"><b>Krystra's Preminium AIO [" .. player.charName .. "] :</b><u><b></b></u></font><font color=\"#FFFFFF\"><b>" .. arg_17_0 .. "</b><u><b></b></u></font>")
end

local ove_0_14 = {
	nil,
	nil,
	nil,
	nil,
	true,
	nil,
	nil,
	true,
	nil,
	nil,
	true,
	nil,
	nil,
	nil,
	nil,
	nil,
	nil,
	true,
	nil,
	nil,
	true,
	true,
	nil,
	true,
	nil,
	nil,
	nil,
	true,
	true,
	true
}

function ove_0_13.PlayerCC(arg_18_0)
	-- print 18
	local slot_18_0 = arg_18_0 or player

	for iter_18_0, iter_18_1 in pairs(slot_18_0.buff) do
		if ove_0_14[iter_18_1.type] then
			return true
		end
	end

	return false
end

function ove_0_13.GetLanguage()
	-- print 19
	return hanbot.language == 1 and 2 or 1
end

function ove_0_13.GetAD(arg_20_0)
	-- print 20
	local slot_20_0 = arg_20_0 or player

	return (slot_20_0.baseAttackDamage + slot_20_0.flatPhysicalDamageMod) * slot_20_0.percentPhysicalDamageMod
end

function ove_0_13.GetBonusAD(arg_21_0)
	-- print 21
	local slot_21_0 = arg_21_0 or player

	return (slot_21_0.baseAttackDamage + slot_21_0.flatPhysicalDamageMod) * slot_21_0.percentPhysicalDamageMod - slot_21_0.baseAttackDamage
end

function ove_0_13.GetTotalAP(arg_22_0)
	-- print 22
	local slot_22_0 = arg_22_0 or player

	return slot_22_0.flatMagicDamageMod * slot_22_0.percentMagicDamageMod
end

function ove_0_13.PointSector(arg_23_0, arg_23_1, arg_23_2)
	-- print 23
	local slot_23_0 = arg_23_2.x
	local slot_23_1 = arg_23_2.z or arg_23_2.y
	local slot_23_2 = arg_23_0.x
	local slot_23_3 = arg_23_0.z or arg_23_0.y
	local slot_23_4 = arg_23_1.x
	local slot_23_5 = arg_23_1.z or arg_23_1.y
	local slot_23_6 = ((slot_23_0 - slot_23_2) * (slot_23_4 - slot_23_2) + (slot_23_1 - slot_23_3) * (slot_23_5 - slot_23_3)) / ((slot_23_4 - slot_23_2)^2 + (slot_23_5 - slot_23_3)^2)
	local slot_23_7 = {
		x = slot_23_2 + slot_23_6 * (slot_23_4 - slot_23_2),
		y = slot_23_3 + slot_23_6 * (slot_23_5 - slot_23_3)
	}
	local slot_23_8 = slot_23_6 < 0 and 0 or slot_23_6 > 1 and 1 or slot_23_6
	local slot_23_9 = slot_23_8 == slot_23_6

	return slot_23_9 and slot_23_7 or {
		x = slot_23_2 + slot_23_8 * (slot_23_4 - slot_23_2),
		y = slot_23_3 + slot_23_8 * (slot_23_5 - slot_23_3)
	}, slot_23_7, slot_23_9
end

function ove_0_13.Mana(arg_24_0)
	-- print 24
	arg_24_0 = arg_24_0 or ove_0_10

	return 100 * arg_24_0.mana / arg_24_0.maxMana
end

function ove_0_13.Health(arg_25_0)
	-- print 25
	arg_25_0 = arg_25_0 or ove_0_10

	return 100 * arg_25_0.health / arg_25_0.maxHealth
end

function ove_0_13.IsValidTarget(arg_26_0)
	-- print 26
	return arg_26_0 and not arg_26_0.isDead and arg_26_0.isTargetable and arg_26_0.isVisible
end

function ove_0_13.IsValidAlly(arg_27_0)
	-- print 27
	return arg_27_0 and not arg_27_0.isDead and arg_27_0.isVisible
end

function ove_0_13.IsMinion(arg_28_0)
	-- print 28
	local slot_28_0 = arg_28_0.charName:lower()

	return slot_28_0:find("minion") and not slot_28_0:find("ward") and not slot_28_0:find("totem") and not slot_28_0:find("jammerdevice")
end

function ove_0_13.IsJungle(arg_29_0)
	-- print 29
	local slot_29_0 = arg_29_0.charName:lower()

	return (slot_29_0:find("red") or slot_29_0:find("baron") or slot_29_0:find("dragon") or slot_29_0:find("herald") or slot_29_0:find("crab") or slot_29_0:find("krug") or slot_29_0:find("blue") or slot_29_0:find("gromp") or slot_29_0:find("razor") or slot_29_0:find("murkwolf")) and not slot_29_0:find("ward") and not slot_29_0:find("totem") and not slot_29_0:find("jammerdevice")
end

function ove_0_13.IsBaron(arg_30_0)
	-- print 30
	local slot_30_0 = arg_30_0.charName:lower()

	return (slot_30_0:find("baron") or slot_30_0:find("herald")) and not slot_30_0:find("ward") and not slot_30_0:find("totem") and not slot_30_0:find("jammerdevice")
end

function ove_0_13.IsDragon(arg_31_0)
	-- print 31
	local slot_31_0 = arg_31_0.charName:lower()

	return slot_31_0:find("dragon") and not slot_31_0:find("ward") and not slot_31_0:find("totem") and not slot_31_0:find("jammerdevice")
end

ove_0_13._enemyHeroes = nil

function ove_0_13.GetEnemyHeroes()
	-- print 32
	if ove_0_13._enemyHeroes then
		return ove_0_13._enemyHeroes
	end

	ove_0_13._enemyHeroes = {}

	for iter_32_0 = 0, objManager.enemies_n - 1 do
		local slot_32_0 = objManager.enemies[iter_32_0]

		ove_0_13._enemyHeroes[#ove_0_13._enemyHeroes + 1] = slot_32_0
	end

	return ove_0_13._enemyHeroes
end

ove_0_13._allyHeroes = nil

function ove_0_13.GetAllyHeroes()
	-- print 33
	if ove_0_13._allyHeroes then
		return ove_0_13._allyHeroes
	end

	ove_0_13._allyHeroes = {}

	for iter_33_0 = 0, objManager.allies_n - 1 do
		local slot_33_0 = objManager.allies[iter_33_0]

		if slot_33_0.networkID ~= ove_0_10.networkID then
			ove_0_13._allyHeroes[#ove_0_13._allyHeroes + 1] = slot_33_0
		end
	end

	return ove_0_13._allyHeroes
end

ove_0_13._allHeroes = nil

function ove_0_13.GetAllHeroes()
	-- print 34
	if ove_0_13._allHeroes then
		return ove_0_13._allHeroes
	end

	ove_0_13._allHeroes = {}

	objManager.loop(function(arg_35_0)
		-- print 35
		if arg_35_0 and arg_35_0.type == ove_0_10.type and arg_35_0.networkID ~= ove_0_10.networkID then
			ove_0_13._allHeroes[#ove_0_13._allHeroes + 1] = arg_35_0
		end
	end)

	return ove_0_13._allHeroes
end

ove_0_13._enemyTowers = nil

function ove_0_13.GetEnemyTowers()
	-- print 36
	if ove_0_13._enemyTowers then
		return ove_0_13._enemyTowers
	end

	ove_0_13._enemyTowers = {}

	objManager.loop(function(arg_37_0)
		-- print 37
		if arg_37_0 and arg_37_0.type == TYPE_TURRET and arg_37_0.team == TEAM_ENEMY then
			ove_0_13._enemyTowers[#ove_0_13._enemyTowers + 1] = arg_37_0
		end
	end)

	return ove_0_13._enemyTowers
end

ove_0_13._enemyInhib = nil

function ove_0_13.GetEnemyInhib()
	-- print 38
	if ove_0_13._enemyInhib then
		return ove_0_13._enemyInhib
	end

	ove_0_13._enemyInhib = {}

	objManager.loop(function(arg_39_0)
		-- print 39
		if arg_39_0 and arg_39_0.type == TYPE_INHIB and arg_39_0.team == TEAM_ENEMY then
			ove_0_13._enemyInhib[#ove_0_13._enemyInhib + 1] = arg_39_0
		end
	end)

	return ove_0_13._enemyInhib
end

ove_0_13._allyTowers = nil

function ove_0_13.GetAllyTowers()
	-- print 40
	if ove_0_13._allyTowers then
		return ove_0_13._allyTowers
	end

	ove_0_13._allyTowers = {}

	objManager.loop(function(arg_41_0)
		-- print 41
		if arg_41_0 and arg_41_0.type == TYPE_TURRET and arg_41_0.team == TEAM_ALLY then
			ove_0_13._allyTowers[#ove_0_13._allyTowers + 1] = arg_41_0
		end
	end)

	return ove_0_13._allyTowers
end

ove_0_13.enemytower = nil

function ove_0_13.ClosestEnemyTower(arg_42_0)
	-- print 42
	local slot_42_0 = math.huge

	for iter_42_0, iter_42_1 in pairs(ove_0_13.GetEnemyTowers()) do
		if slot_42_0 >= arg_42_0:dist(iter_42_1.pos) then
			slot_42_0, ove_0_13.enemytower = arg_42_0:dist(iter_42_1.pos), iter_42_1
		end
	end

	return ove_0_13.enemytower
end

ove_0_13.enemyinhib = nil

function ove_0_13.ClosestEnemyInhib(arg_43_0)
	-- print 43
	local slot_43_0 = math.huge

	for iter_43_0, iter_43_1 in pairs(ove_0_13.GetEnemyInhib()) do
		if slot_43_0 >= arg_43_0:dist(iter_43_1.pos) then
			slot_43_0, ove_0_13.enemyinhib = arg_43_0:dist(iter_43_1.pos), iter_43_1
		end
	end

	return ove_0_13.enemyinhib
end

ove_0_13.CloseEnemy = nil

function ove_0_13.ClosestEnemy(arg_44_0)
	-- print 44
	local slot_44_0 = math.huge

	for iter_44_0, iter_44_1 in pairs(ove_0_13.GetEnemyHeroes()) do
		if slot_44_0 >= arg_44_0:dist(iter_44_1.pos) and ove_0_13.IsValidTarget(iter_44_1) then
			slot_44_0, ove_0_13.CloseEnemy = arg_44_0:dist(iter_44_1.path.serverPos), iter_44_1
		end
	end

	return ove_0_13.CloseEnemy
end

ove_0_13.lowEnemy = nil

function ove_0_13.LowestEnemy(arg_45_0)
	-- print 45
	local slot_45_0 = math.huge
	local slot_45_1 = math.huge

	for iter_45_0, iter_45_1 in pairs(ove_0_13.GetEnemyHeroes()) do
		if slot_45_0 >= arg_45_0:dist(iter_45_1.pos) and ove_0_13.IsValidTarget(iter_45_1) and slot_45_1 > iter_45_1.health then
			slot_45_1, ove_0_13.lowEnemy = iter_45_1.health, iter_45_1
		end
	end

	return ove_0_13.lowEnemy
end

ove_0_13.CloseMinion = nil

function ove_0_13.ClosestMinion(arg_46_0)
	-- print 46
	local slot_46_0 = math.huge

	for iter_46_0 = 0, ove_0_11.size[TEAM_ENEMY] - 1 do
		local slot_46_1 = ove_0_11[TEAM_ENEMY][iter_46_0]

		if ove_0_13.IsValidTarget(slot_46_1) and slot_46_0 >= arg_46_0:dist(slot_46_1.path.serverPos) then
			slot_46_0, ove_0_13.CloseMinion = arg_46_0:dist(slot_46_1.path.serverPos), slot_46_1
		end
	end

	return ove_0_13.CloseMinion
end

ove_0_13.CloseMinionAlly = nil

function ove_0_13.ClosestMinionAlly(arg_47_0)
	-- print 47
	local slot_47_0 = math.huge

	for iter_47_0 = 0, ove_0_11.size[TEAM_ALLY] - 1 do
		local slot_47_1 = ove_0_11[TEAM_ALLY][iter_47_0]

		if ove_0_13.IsValidAlly(slot_47_1) and slot_47_0 >= arg_47_0:dist(slot_47_1.path.serverPos) then
			slot_47_0, ove_0_13.CloseMinionAlly = arg_47_0:dist(slot_47_1.path.serverPos), slot_47_1
		end
	end

	return ove_0_13.CloseMinionAlly
end

ove_0_13.CloseAlly = nil

function ove_0_13.ClosestAlly(arg_48_0)
	-- print 48
	local slot_48_0 = math.huge

	for iter_48_0, iter_48_1 in pairs(ove_0_13.GetAllyHeroes()) do
		if slot_48_0 >= arg_48_0:dist(iter_48_1.pos) and ove_0_13.IsValidAlly(iter_48_1) then
			slot_48_0, ove_0_13.CloseAlly = arg_48_0:dist(iter_48_1.path.serverPos), iter_48_1
		end
	end

	return ove_0_13.CloseAlly
end

function ove_0_13.CountObjectsNearPos(arg_49_0, arg_49_1, arg_49_2)
	-- print 49
	local slot_49_0 = 0

	for iter_49_0 = 0, ove_0_11.size[TEAM_ENEMY] - 1 do
		local slot_49_1 = ove_0_11[TEAM_ENEMY][iter_49_0]

		if ove_0_13.IsValidTarget(slot_49_1) and arg_49_1 > ove_0_13.Dist(slot_49_1) and arg_49_2 >= ove_0_13.GetDistance(arg_49_0, slot_49_1) then
			slot_49_0 = slot_49_0 + 1
		end
	end

	return slot_49_0
end

function ove_0_13.GetCircularFarm(arg_50_0, arg_50_1)
	-- print 50
	local slot_50_0
	local slot_50_1 = 0

	for iter_50_0 = 0, ove_0_11.size[TEAM_ENEMY] - 1 do
		local slot_50_2 = ove_0_11[TEAM_ENEMY][iter_50_0]

		if ove_0_13.IsValidTarget(slot_50_2) and arg_50_0 > ove_0_13.Dist(slot_50_2) and ove_0_13.IsMinion(slot_50_2) then
			local slot_50_3 = ove_0_13.CountObjectsNearPos(slot_50_2, arg_50_0, arg_50_1)

			if slot_50_1 < slot_50_3 then
				slot_50_1 = slot_50_3
				slot_50_0 = slot_50_2.path.serverPos

				if slot_50_1 == ove_0_11.size[TEAM_ENEMY] then
					break
				end
			end
		end
	end

	return slot_50_0, slot_50_1
end

function ove_0_13.CountMinionSegment(arg_51_0, arg_51_1, arg_51_2, arg_51_3, arg_51_4, arg_51_5)
	-- print 51
	arg_51_5 = arg_51_5 or TEAM_ENEMY

	local slot_51_0 = 0

	for iter_51_0 = 0, ove_0_11.size[TEAM_ENEMY] - 1 do
		local slot_51_1 = ove_0_11[TEAM_ENEMY][iter_51_0]

		if ove_0_13.IsValidTarget(slot_51_1) and ove_0_13.Dist(slot_51_1) < arg_51_3 + 50 then
			local slot_51_2, slot_51_3, slot_51_4 = ove_0_13.PointSector(arg_51_0, arg_51_1, slot_51_1)
			local slot_51_5 = arg_51_2

			if slot_51_4 and slot_51_5 > ove_0_13.GetDistance(slot_51_2, slot_51_1) and ove_0_13.GetDistance(arg_51_0, arg_51_1) > ove_0_13.GetDistance(arg_51_0, slot_51_1) then
				slot_51_0 = slot_51_0 + 1
			end
		end
	end

	return slot_51_0
end

function ove_0_13.GetLineFarm(arg_52_0, arg_52_1, arg_52_2)
	-- print 52
	arg_52_2 = arg_52_2 or TEAM_ENEMY

	local slot_52_0
	local slot_52_1 = 0

	for iter_52_0 = 0, ove_0_11.size[TEAM_ENEMY] - 1 do
		local slot_52_2 = ove_0_11[TEAM_ENEMY][iter_52_0]

		if ove_0_13.IsValidTarget(slot_52_2) and ove_0_13.Dist(slot_52_2) < arg_52_0 + 50 and ove_0_13.IsMinion(slot_52_2) then
			local slot_52_3 = slot_52_2.path.serverPos + arg_52_0 * (slot_52_2.path.serverPos - ove_0_10.path.serverPos):norm()
			local slot_52_4 = ove_0_13.CountMinionSegment(ove_0_10.path.serverPos, slot_52_3, arg_52_1, arg_52_0)

			if slot_52_1 < slot_52_4 then
				slot_52_1 = slot_52_4
				slot_52_0 = slot_52_2.path.serverPos

				if slot_52_1 == ove_0_11.size[TEAM_ENEMY] then
					break
				end
			end
		end
	end

	return slot_52_0, slot_52_1
end

ove_0_13.allytower = nil

function ove_0_13.ClosestAllyTower(arg_53_0)
	-- print 53
	local slot_53_0 = math.huge

	for iter_53_0, iter_53_1 in pairs(ove_0_13.GetAllyTowers()) do
		if slot_53_0 >= arg_53_0:dist(iter_53_1.pos) then
			slot_53_0, ove_0_13.allytower = arg_53_0:dist(iter_53_1.pos), iter_53_1
		end
	end

	return ove_0_13.allytower
end

ove_0_13.AllyNear = nil

function ove_0_13.ClosestAlly(arg_54_0)
	-- print 54
	local slot_54_0 = math.huge

	for iter_54_0, iter_54_1 in pairs(ove_0_13.GetAllyHeroes()) do
		if slot_54_0 >= arg_54_0:dist(iter_54_1.pos) then
			slot_54_0, ove_0_13.AllyNear = arg_54_0:dist(iter_54_1.pos), iter_54_1
		end
	end

	return ove_0_13.AllyNear
end

function ove_0_13.MinionCount(arg_55_0, arg_55_1, arg_55_2)
	-- print 55
	arg_55_0 = arg_55_0 or math.huge
	arg_55_2 = arg_55_2 or ove_0_10.pos
	arg_55_1 = arg_55_1 or TEAM_ENEMY

	local slot_55_0 = 0

	for iter_55_0 = 0, ove_0_11.size[arg_55_1] - 1 do
		local slot_55_1 = ove_0_11[arg_55_1][iter_55_0]

		if ove_0_13.IsValidTarget(slot_55_1) and arg_55_0 > slot_55_1.path.serverPos:dist(arg_55_2) then
			slot_55_0 = slot_55_0 + 1
		end
	end

	return slot_55_0
end

function ove_0_13.HasItem(arg_56_0, arg_56_1)
	-- print 56
	for iter_56_0 = 6, 11 do
		if arg_56_0:spellSlot(iter_56_0).isNotEmpty and arg_56_0:spellSlot(iter_56_0).name == arg_56_1 then
			return true
		end
	end

	return false
end

function ove_0_13.HasItemAndReady(arg_57_0, arg_57_1)
	-- print 57
	for iter_57_0 = 6, 11 do
		if arg_57_0:spellSlot(iter_57_0).isNotEmpty and arg_57_0:spellSlot(iter_57_0).name == arg_57_1 and arg_57_0:spellSlot(iter_57_0).state == 0 then
			return true
		end
	end

	return false
end

function ove_0_13.HasItemGetCooldown(arg_58_0, arg_58_1)
	-- print 58
	for iter_58_0 = 6, 11 do
		if arg_58_0:spellSlot(iter_58_0).isNotEmpty and arg_58_0:spellSlot(iter_58_0).name == arg_58_1 then
			return arg_58_0:spellSlot(iter_58_0).cooldown
		end
	end

	return 0
end

function ove_0_13.HasItemAndNotReady(arg_59_0, arg_59_1)
	-- print 59
	for iter_59_0 = 6, 11 do
		if arg_59_0:spellSlot(iter_59_0).isNotEmpty and arg_59_0:spellSlot(iter_59_0).name == arg_59_1 and arg_59_0:spellSlot(iter_59_0).state == 40 then
			return true
		end
	end

	return false
end

function ove_0_13.GetItemNames(arg_60_0)
	-- print 60
	for iter_60_0 = 6, 11 do
		if arg_60_0:spellSlot(iter_60_0).isNotEmpty then
			local slot_60_0 = arg_60_0:spellSlot(iter_60_0).name

			chat.print(slot_60_0)
		end
	end
end

function ove_0_13.Extend(arg_61_0, arg_61_1, arg_61_2)
	-- print 61
	return arg_61_1 + (arg_61_1 - arg_61_0):norm() * -arg_61_2
end

function ove_0_13.DrawDamage(arg_62_0, arg_62_1, arg_62_2)
	-- print 62
	if not ove_0_13.IsValidTarget(arg_62_0) or not arg_62_0.isOnScreen then
		return
	end

	arg_62_2 = arg_62_2 or graphics.argb(122, 255, 239, 213)

	local slot_62_0 = arg_62_0.health - arg_62_1
	local slot_62_1 = arg_62_0.barPos
	local slot_62_2 = slot_62_1.x + 164
	local slot_62_3 = slot_62_1.y + 122.5
	local slot_62_4 = slot_62_2 + arg_62_0.health / arg_62_0.maxHealth * 102
	local slot_62_5 = slot_62_2 + (slot_62_0 > 0 and slot_62_0 or 0) / arg_62_0.maxHealth * 102

	graphics.draw_line_2D(slot_62_4, slot_62_3, slot_62_5, slot_62_3, 10, arg_62_2)
end

function ove_0_13.HeroCount(arg_63_0, arg_63_1, arg_63_2)
	-- print 63
	arg_63_0 = arg_63_0 or math.huge
	arg_63_1 = arg_63_1 or TEAM_ENEMY
	arg_63_2 = arg_63_2 or ove_0_10.pos

	local slot_63_0 = 0

	for iter_63_0 = 1, #ove_0_13.GetAllHeroes() do
		local slot_63_1 = ove_0_13.GetAllHeroes()[iter_63_0]

		if slot_63_1 and slot_63_1.type == TYPE_HERO and slot_63_1.team == arg_63_1 and ove_0_13.IsValidTarget(slot_63_1) and arg_63_0 > slot_63_1.path.serverPos:dist(arg_63_2) then
			slot_63_0 = slot_63_0 + 1
		end
	end

	return slot_63_0
end

function ove_0_13.DrawRectangleOutline(arg_64_0, arg_64_1, arg_64_2, arg_64_3)
	-- print 64
	arg_64_0 = graphics.world.toscreen(arg_64_0)
	arg_64_1 = graphics.world.toscreen(arg_64_1)

	local slot_64_0 = arg_64_0 + (arg_64_1 - arg_64_0):norm():perp1() * arg_64_2 / 2
	local slot_64_1 = arg_64_0 + (arg_64_1 - arg_64_0):norm():perp2() * arg_64_2 / 2
	local slot_64_2 = arg_64_1 + (arg_64_0 - arg_64_1):norm():perp1() * arg_64_2 / 2
	local slot_64_3 = arg_64_1 + (arg_64_0 - arg_64_1):norm():perp2() * arg_64_2 / 2

	graphics.draw_line_2D(slot_64_0.x, slot_64_0.y, slot_64_1.x, slot_64_1.y, math.ceil(arg_64_2 / 100), arg_64_3)
	graphics.draw_line_2D(slot_64_1.x, slot_64_1.y, slot_64_2.x, slot_64_2.y, math.ceil(arg_64_2 / 100), arg_64_3)
	graphics.draw_line_2D(slot_64_2.x, slot_64_2.y, slot_64_3.x, slot_64_3.y, math.ceil(arg_64_2 / 100), arg_64_3)
	graphics.draw_line_2D(slot_64_0.x, slot_64_0.y, slot_64_3.x, slot_64_3.y, math.ceil(arg_64_2 / 100), arg_64_3)
end

ove_0_13._fountain = nil
ove_0_13._fountainRadius = 750

function ove_0_13.GetFountain()
	-- print 65
	if ove_0_13._fountain then
		return ove_0_13._fountain
	end

	local slot_65_0 = ove_0_13.GetMap()

	if slot_65_0 ~= nil and slot_65_0.index ~= nil and slot_65_0.index == 1 then
		ove_0_13._fountainRadius = 1050
	end

	if ove_0_13.GetShop() then
		objManager.loop(function(arg_66_0)
			-- print 66
			if arg_66_0 and arg_66_0.team == ove_0_10.team and arg_66_0.name:lower():find("spawn") and not arg_66_0.name:lower():find("troy") and not arg_66_0.name:lower():find("barracks") then
				ove_0_13._fountain = arg_66_0

				return ove_0_13.fountain
			end
		end)
	end
end

function ove_0_13.NearFountain(arg_67_0)
	-- print 67
	arg_67_0 = arg_67_0 or ove_0_13._fountainRadius or 0

	local slot_67_0 = ove_0_13.GetFountain()

	if slot_67_0 then
		return ove_0_13.G2(ove_0_10, slot_67_0) <= arg_67_0 * arg_67_0
	else
		return false
	end
end

function ove_0_13.InFountain()
	-- print 68
	return ove_0_13.NearFountain()
end

ove_0_13._map = {
	index = 0,
	name = "unknown"
}

function ove_0_13.GetMap()
	-- print 69
	if ove_0_13._map.index ~= 0 then
		return ove_0_13._map
	end

	local slot_69_0 = ove_0_13.GetShop()

	if math.floor(ove_0_13.GetShop().x) == 232 and math.floor(ove_0_13.GetShop().y) == 163 and math.floor(ove_0_13.GetShop().z) == 1277 then
		ove_0_13._map = {
			index = 1,
			name = "Summoner's Rift"
		}
	elseif math.floor(ove_0_13.GetShop().x) == 1313 and math.floor(ove_0_13.GetShop().y) == 123 and math.floor(ove_0_13.GetShop().z) == 8005 then
		ove_0_13._map = {
			index = 4,
			name = "Twisted Treeline"
		}
	elseif math.floor(ove_0_13.GetShop().x) == 497 and math.floor(ove_0_13.GetShop().y) == -40 and math.floor(ove_0_13.GetShop().z) == 1932 then
		ove_0_13._map = {
			index = 12,
			name = "Howling Abyss"
		}
	end

	return ove_0_13._map
end

ove_0_13._shop = nil
ove_0_13._shopRadius = 1250

function ove_0_13.GetShop()
	-- print 70
	if ove_0_13._shop then
		return ove_0_13._shop
	end

	objManager.loop(function(arg_71_0)
		-- print 71
		if arg_71_0 and arg_71_0.name and arg_71_0.name:lower():find("shop") and arg_71_0.team == ove_0_10.team then
			ove_0_13._shop = arg_71_0

			return ove_0_13._shop
		end
	end)

	return nil
end

function ove_0_13.Class()
	-- print 72
	local slot_72_0 = {}

	slot_72_0.__index = slot_72_0

	return setmetatable(slot_72_0, {
		__call = function(arg_73_0, ...)
			-- print 73
			local slot_73_0 = setmetatable({}, slot_72_0)

			if slot_72_0.__init then
				slot_72_0.__init(slot_73_0, ...)
			end

			return slot_73_0
		end
	})
end

return ove_0_13

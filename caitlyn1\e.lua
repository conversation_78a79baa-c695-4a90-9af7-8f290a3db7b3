local lvxbot = module.load(header.id, 'lvxbot/main')

local input = {
  prediction = {
    type = 'Linear',
    --
    range = 730,
    delay = 0.25,
    speed = 1600,
    width = 75,
    boundingRadiusMod = 1,
      collision = {
      hero = false, --allow to hit other heros :-)
      minion = true,
      wall = true,
  },


    },
  
  
  target_selector = {
    type = 'LESS_CAST_AD',
  },

  cast_spell = {
    type = 'pos',
    slot = _E,
    arg1 = function(action)
      local seg = action.ts_result.seg
      local obj = action.ts_result.obj
      return seg.endPos:to3D(obj.y)
    end,
  },

  slot = _E,
  ignore_obj_radius = 2000,
}

local module = lvxbot.cexpert.create(input)
--print (module,"111111111111111111")


return module

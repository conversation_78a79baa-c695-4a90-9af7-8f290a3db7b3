# Xerath E反突进功能说明

## 功能概述
为Xerath添加了智能E技能反突进功能，可以自动检测敌方英雄的突进行为并使用E技能进行反制。

## 功能特点

### 1. 智能检测
- **突进检测**：自动识别敌方英雄的冲刺/突进行为
- **距离判断**：根据设定的范围检测威胁
- **英雄识别**：内置常见突进英雄列表，提高准确性

### 2. 预测系统
- **路径预测**：预测敌人的突进路径
- **时机计算**：计算最佳释放E技能的时机
- **碰撞检测**：避免被小兵阻挡

### 3. 菜单设置

#### 中文菜单选项：
- **启用E反突进**：总开关，控制是否启用此功能
- **反突进检测范围**：设置检测敌人突进的最大距离（300-1000）
- **最小反突进距离**：只有当敌人距离小于此值时才使用E反突进（100-500）
- **仅对冲刺使用**：只对正在冲刺的敌人使用E反突进
- **预测突进路径**：预测敌人的突进路径并提前释放E技能

#### English Menu Options:
- **Enable E Anti-Gapcloser**: Master switch to enable/disable the feature
- **Anti-Gap Detection Range**: Maximum distance to detect enemy gapclosers (300-1000)
- **Min Anti-Gap Distance**: Only use E anti-gap when enemy is closer than this distance (100-500)
- **Dash Only**: Only use E anti-gap against dashing enemies
- **Predict Dash Path**: Predict enemy dash path and cast E preemptively

## 支持的突进英雄
功能内置了以下突进英雄的识别：

### 刺客类
- Akali, Ekko, Fizz, Kassadin, Katarina, Kha'Zix, LeBlanc, Nocturne, Qiyana, Talon, Zed

### 战士类
- Aatrox, Camille, Diana, Fiora, Irelia, Jax, Lee Sin, Master Yi, Pantheon, Renekton, Riven, Tryndamere, Wukong, Xin Zhao, Yasuo, Yone

### 坦克类
- Alistar, Ammu, Leona, Malphite, Nautilus, Poppy, Rammus, Sejuani, Shen, Thresh, Vi, Zac

### 其他类
- Azir, Graves, Lucian, Pyke, Rek'Sai, Shyvana, Tristana, Udyr, Warwick

## 使用建议

### 1. 基础设置
- 建议将**检测范围**设置为600-800
- **最小距离**设置为200-300
- 启用**预测突进路径**以提高命中率

### 2. 对战不同英雄的建议
- **对抗刺客**：降低最小距离设置，提高反应速度
- **对抗坦克**：可以适当增加检测范围
- **对抗远程突进**：启用路径预测功能

### 3. 注意事项
- 功能具有最高优先级，会优先于其他技能释放
- 在团战中建议适当调整设置，避免过于频繁释放E技能
- 可以根据对手的游戏风格调整"仅对冲刺使用"选项

## 技术实现

### 1. 检测机制
- 实时监控敌方英雄的移动状态
- 检测isDashing属性和移动方向
- 计算敌人与玩家的相对位置和速度

### 2. 预测算法
- 基于敌人当前位置、移动速度和方向
- 考虑E技能的延迟和飞行时间
- 动态调整预测位置

### 3. 优先级系统
- E反突进具有最高优先级
- 不会与其他技能冲突
- 自动暂停走砍以确保技能释放

## 更新日志
- v1.0: 初始版本，支持基础反突进功能
- 添加了完整的菜单系统
- 集成到主要的Xerath逻辑中
- 支持中英文界面

## 故障排除
如果功能不正常工作，请检查：
1. 是否在菜单中启用了E反突进功能
2. E技能是否处于冷却状态
3. 检测范围设置是否合理
4. 是否有其他脚本冲突

local leblancPlugin = {}

-- Load Spell
local spellQ = {
    range = 700,
}

local spellW = {
    range = 600,
    delay = 0.25,
    radius = 180,
    speed = 1450,
    boundingRadiusMod = 1,
    collision = {hero = false, minion = false, wall = false},
    lastPosition = vec3(0, 0, 0),
    lastCastTime = 0,
}

local spellW2 = {
    range = 1000,
    delay = 0.40,
    radius = 180,
    speed = 1450,
    boundingRadiusMod = 0,
    collision = {hero = false, minion = false, wall = false}, 
}

local spellE = {
    range = 865,
    delay = 0.25,
    width = 70,
    speed = 1600,
    boundingRadiusMod = 1,
    collision = {hero = true, minion = true, wall = false}, 
}

local isDashing = false
local SwitchTime = 0

-- Load Module
local ui = module.load("Brian", "ui");
local DelayAction = module.load("Brian", "Core/DelayAction")
local DelayTick = module.load("Brian", "Core/DelayTick")
local Prediction = module.load("<PERSON>", "Core/Prediction")
local BuffManager = module.load("<PERSON>", "Library/BuffManager")
local CalculateManager = module.load("<PERSON>", "Library/CalculateManager")
local FarmManager = module.load("<PERSON>", "Library/FarmManager")
local ItemManager = module.load("Brian", "Library/ItemManager")
local NetManager = module.load("Brian", "Library/NetManager")
local ObjectManager = module.load("Brian", "Library/ObjectManager")
local OrbManager = module.load("Brian", "Library/OrbManager")
local SpellManager = module.load("Brian", "Library/SpellManager")
local VectorManager = module.load("Brian", "Library/VectorManager")
local MyCommon = module.load("Brian", "Library/ExtraManager")
local Curses = module.load("Brian", "Curses");
local MyMenu

function leblancPlugin.Load(GlobalMenu)
    MyMenu = GlobalMenu

    MyMenu.Key:keybind("Flee", "Flee Key", "Z", false)
    MyMenu.Key:keybind("SemiQE", "Semi QE Key", "J", false)
    MyMenu.Key:keybind("SemiERE", "Semi ERE Key", "G", false)

    MyMenu:menu("Combo", "Combo Settings")
    MyMenu.Combo:header("SpellHeader", "Spell Core")
    MyMenu.Combo:boolean("Q", "Use Q", true)
    MyMenu.Combo:boolean("W", "Use W", true)
    MyMenu.Combo:boolean("WGapcloser", "^ Gapcloser to target", true)
    MyMenu.Combo:boolean("WBack", "^ Return to start Position", true)
    MyMenu.Combo:boolean("E", "Use E", true)
    MyMenu.Combo:boolean("R", "Use R", true)
    MyMenu.Combo:header("ModeHeader", "Combo Style")
    MyMenu.Combo:dropdown("ComboMode", "Style: ", 1, {"Priority QR", "Priority WR"}) 
    MyMenu.Combo:keybind("ComboModeKey", "Switch Style Key", "T", false)

    MyMenu:menu("Harass", "Harass Settings")
    MyMenu.Harass:header("SpellHeader", "Spell Core")
    MyMenu.Harass:boolean("Q", "Use Q", true)
    MyMenu.Harass:boolean("W", "Use W", false)
    MyMenu.Harass:boolean("WBack", "^ Return to start Position", false)
    MyMenu.Harass:boolean("E", "Use E", true)
    MyMenu.Harass:boolean("R", "Use R", false)
    MyMenu.Harass:header("ManaHeader", "Mana Manager")
    MyMenu.Harass:slider("ManaMin", "Player ManaPercent >= x%", 70, 1, 100, 1)
    MyMenu.Harass:header("ProHeader", "PRO Mode")
    MyMenu.Harass:boolean("ProAllow", "Enabled PRO Harass Mode", true)
    MyMenu.Harass.ProAllow:set("tooltip", "PRO Mode => Allow Use Spell Harass Enemy on LaneClear Mode")
    MyMenu.Harass:boolean("ProTurret", "Allow Under Turret Harass", false)

    MyMenu:menu("LaneClear", "LaneClear Settings")
    MyMenu.LaneClear:header("SpellHeader", "Spell Core")
    MyMenu.LaneClear:boolean("Q", "Use Q", true)
    MyMenu.LaneClear:header("ManaHeader", "Mana Manager")
    MyMenu.LaneClear:slider("ManaMin", "Player ManaPercent >= x%", 60, 1, 100, 1)

    MyMenu:menu("JungleClear", "JungleClear Settings")
    MyMenu.JungleClear:header("SpellHeader", "Spell Core")
    MyMenu.JungleClear:boolean("Q", "Use Q", true)
    MyMenu.JungleClear:boolean("W", "Use W", true)
    MyMenu.JungleClear:boolean("E", "Use E", true)
    MyMenu.JungleClear:header("ManaHeader", "Mana Manager")
    MyMenu.JungleClear:slider("ManaMin", "Player ManaPercent >= x%", 20, 1, 100, 1)

    FarmManager.Load(MyMenu)

    MyMenu:menu("Flee", "Flee Settings")
    MyMenu.Flee:header("SpellHeader", "Spell Core")
    MyMenu.Flee:boolean("W", "Use W", true)
    MyMenu.Flee:boolean("R", "Use R", true)

    MyMenu:menu("KillSteal", "KillSteal Settings")
    MyMenu.KillSteal:header("SpellHeader", "Spell Core")
    MyMenu.KillSteal:boolean("Q", "Use Q", true)

    MyMenu:menu("Draw", "Draw Settings")
    MyMenu.Draw:header("RangeHeader", "Spell Range")
    MyMenu.Draw:boolean("Q", "Draw Q Range", true)
    MyMenu.Draw:color("colorq", "^ color", 255, 233, 121, 121)
    MyMenu.Draw:boolean("W", "Draw W Range", true)
    MyMenu.Draw:color("colorw", "^ color", 255, 233, 121, 121)
    MyMenu.Draw:boolean("E", "Draw E Range", true)
    MyMenu.Draw:color("colore", "^ color", 255, 233, 121, 121)
    MyMenu.Draw:boolean("Ready", "Only Spell Ready", false)
    MyMenu.Draw:header("StatusHeader", "Spell Status")
    MyMenu.Draw:boolean("ComboMode", "Draw Combo Style", true)

    MyMenu.Combo.ComboModeKey:set(
        "callback",
        function()
            if player.isDead or player.isRecalling or chat.isOpened then
                return
            end
            if game.time - SwitchTime > 0.2 then
                if MyMenu.Combo.ComboMode:get() == 1 then
                    MyMenu.Combo.ComboMode:set("value", 2)
                    SwitchTime = game.time
                    return
                end
                if MyMenu.Combo.ComboMode:get() == 2 then
                    MyMenu.Combo.ComboMode:set("value", 1)
                    SwitchTime = game.time
                    return
                end
            end
        end
    )
end

local function GetPassiveDamage(target)
    if not target or target == nil then
        return 0
    end
    local qLevel = player:spellSlot(0).level
    local rLevel = player:spellSlot(3).level
    if qLevel == 0 and rLevel == 0 then
        return 0
    end
    if BuffManager.HasBuff(target, "LeblancQMark") then
        local dmg = ({55, 80, 105, 130, 155})[qLevel] + (0.4 * MyCommon.GetTotalAP())
        return CalculateManager.CalculateMagicDamage(target, dmg)
    end
    if BuffManager.HasBuff(target, "LeblancRQMark") then
        local dmg = ({70, 140, 210})[rLevel] + (0.4 * MyCommon.GetTotalAP())
        return CalculateManager.CalculateMagicDamage(target, dmg)
    end
    return 0
end

local function GetQDamage(target)
    if not target or target == nil then
        return 0
    end
    local level = player:spellSlot(0).level
    if level == 0 then
        return 0
    end
    local dmg = ({55, 80, 105, 130, 155})[level] + (0.4 * MyCommon.GetTotalAP())
    return CalculateManager.CalculateMagicDamage(target, dmg) + GetPassiveDamage(target)
end

local function GetWDamage(target)
    if not target or target == nil then
        return 0
    end
    local level = player:spellSlot(1).level
    if level == 0 then
        return 0
    end
    local dmg = ({75, 115, 155, 195, 235})[level] + (0.6 * MyCommon.GetTotalAP()) -- update dmg patch 8.22
    return CalculateManager.CalculateMagicDamage(target, dmg) + GetPassiveDamage(target)
end

local function GetEDamage(target)
    if not target or target == nil then
        return 0
    end
    local level = player:spellSlot(2).level
    if level == 0 then
        return 0
    end
    local dmg = ({40, 60, 80, 100, 120})[level] + (0.3 * MyCommon.GetTotalAP())
    return CalculateManager.CalculateMagicDamage(target, dmg) + GetPassiveDamage(target)
end

local function CanCastE(target)
    if target and target ~= nil and MyCommon.IsValidTarget(target, spellE.range) then
        local pred = Prediction.GetPrediction(spellE, target)
        if pred then
            return true
        end
    end
end

local function CastW(target)
    if not target or target == nil or not MyCommon.IsValidTarget(target) then 
        return 
    end
    if not SpellManager.CanCastSpell(1) then
        return 
    end
    local name = player:spellSlot(1).name
    if name and name == "LeblancWReturn" then
        return 
    end
    if MyCommon.IsValidTarget(target) then
        -- start extend W pred check
        local predPos = Prediction.GetPrediction(spellW2, target)
        if predPos then
            -- pred the moveSpeed, and then cast after target moving (ass we can)
            local x = target.moveSpeed
            local y = x / 4
            local targetPos = target.pos2D
            local pos = targetPos
            if targetPos:dist(predPos) <= y then
                pos = predPos
            end
            if targetPos:dist(predPos) > y then
                pos = VectorManager.Extend(targetPos, predPos, (y - 50))
            end
            -- sure i can hit to target, gogogo
            if pos and player.pos2D:dist(pos) <= 600 then
                SpellManager.CastOnPosition(vec3(pos.x, target.pos.y, pos.y), 1)
                return
            else
                -- if i cant hit target(for moveSpeed check), go to hit on prediction extend
                if targetPos:dist(predPos) > y then
                    local pos2 = VectorManager.Extend(targetPos, predPos, y)
                    if pos2 and player.pos2D:dist(pos2) <= 600 then
                        SpellManager.CastOnPosition(vec3(pos2.x, target.pos.y, pos2.y), 1)
                        return
                    else
                        -- OK, go to normal W pred
                        local castPos = Prediction.GetPrediction(spellW, target)
                        if castPos then
                            -- pos fix
                            local realPos = VectorManager.Extend(player.pos2D, castPos, 600)
                            if realPos then
                                SpellManager.CastOnPosition(vec3(realPos.x, target.pos.y, realPos.y), 1)
                                return
                            end
                        end
                    end
                end
            end
        end
    end
end

local function CastE(target)
    if not target or target == nil or not MyCommon.IsValidTarget(target) then 
        return 
    end
    if not SpellManager.CanCastSpell(2) then
        return 
    end
    if MyCommon.IsValidTarget(target, spellE.range) then
        if not player.path.isDashing then
            -- at first i dont want to cast RE (shit combo for everyone), i only need the burst
            if not SpellManager.CanCastSpell(3) then
                local pred = Prediction.GetPrediction(spellE, target)
                if pred then
                    SpellManager.CastOnPosition(vec3(pred.x, target.pos.y, pred.y), 2)
                    return
                end
            else
                -- if i only have RE, QW all usage, only one way use RE combo to make dmg
                if SpellManager.CanCastSpell(3) and player:spellSlot(3).name == "LeblancRWReturn" then
                    local pred = Prediction.GetPrediction(spellE, target)
                    if pred then
                        SpellManager.CastOnPosition(vec3(pred.x, target.pos.y, pred.y), 2)
                        return
                    end
                end
            end
        end
    end
end

local function CastR(target)
    if not target or target == nil or not MyCommon.IsValidTarget(target) then 
        return 
    end
    if MyCommon.IsValidTarget(target) then
        local name = player:spellSlot(3).name
        if name then
            -- R(Q) is the first combo select 
            if name == "LeblancRQ" then
                if MyCommon.IsValidTarget(target, spellQ.range) then
                    SpellManager.CastOnUnit(target, 3)
                    return
                end
            end
            -- R(W) is the second combo select
            if name == "LeblancRW" then
                if MyCommon.IsValidTarget(target) then
                    -- start extend W pred check
                    local predPos = Prediction.GetPrediction(spellW2, target)
                    if predPos then
                        -- pred the moveSpeed, and then cast after target moving (ass we can)
                        local x = target.moveSpeed
                        local y = x / 4
                        local targetPos = target.pos2D
                        local pos = targetPos
                        if targetPos:dist(predPos) <= y then
                            pos = predPos
                        end
                        if targetPos:dist(predPos) > y then
                            pos = VectorManager.Extend(targetPos, predPos, (y - 50))
                        end
                        -- sure i can hit to target, gogogo
                        if pos and player.pos2D:dist(pos) <= 600 then
                            SpellManager.CastOnPosition(vec3(pos.x, target.pos.y, pos.y), 3)
                            return
                        else
                            -- if i cant hit target(for moveSpeed check), go to hit on prediction extend
                            if targetPos:dist(predPos) > y then
                                local pos2 = VectorManager.Extend(targetPos, predPos, y)
                                if pos2 and player.pos2D:dist(pos2) <= 600 then
                                    SpellManager.CastOnPosition(vec3(pos2.x, target.pos.y, pos2.y), 3)
                                    return
                                else
                                    -- OK, go to normal W pred
                                    local castPos = Prediction.GetPrediction(spellW, target)
                                    if castPos then
                                        -- pos fix
                                        local realPos = VectorManager.Extend(player.pos2D, castPos, 600)
                                        if realPos then
                                            SpellManager.CastOnPosition(vec3(realPos.x, target.pos.y, realPos.y), 3)
                                            return
                                        end
                                    end
                                end
                            end
                        end
                    end
                end
            end
        end
    end
end

local function Flee()
    player:move(game.mousePos)
    if MyMenu.Flee.W:get() and SpellManager.CanCastSpell(1) and player:spellSlot(1).name and player:spellSlot(1).name ~= "LeblancWReturn" and not isDashing then
        local pos = VectorManager.Extend(player.pos, game.mousePos, spellW.range)
        if pos then
            SpellManager.CastOnPosition(pos, 1)
            return
        end
    end
    if MyMenu.Flee.R:get() and SpellManager.CanCastSpell(3) and player:spellSlot(3).name and player:spellSlot(3).name == "LeblancRW" and not isDashing then
        local pos = VectorManager.Extend(player.pos, game.mousePos, spellW.range)
        if pos then
            SpellManager.CastOnPosition(pos, 3)
            return
        end
    end
end

local function SemiQE()
    player:move(game.mousePos)
    local target = MyCommon.GetTarget(spellQ.range)
    if target and target ~= nil and MyCommon.IsValidTarget(target, spellQ.range) then
        local pred = Prediction.GetPrediction(spellE, target)
        if pred then
            -- cast Q first
            SpellManager.CastOnUnit(target, 0)
            -- cast E second
            SpellManager.CastOnPosition(vec3(pred.x, target.pos.y, pred.y), 2)
            return
        end
    end
end

local function SemiERE()
    player:move(game.mousePos)
    local target = MyCommon.GetTarget(spellE.range)
    if target and target ~= nil and MyCommon.IsValidTarget(target, spellE.range) then
        local pred = Prediction.GetPrediction(spellE, target)
        if pred then
            -- cast E first
            SpellManager.CastOnPosition(vec3(pred.x, target.pos.y, pred.y), 2)
            -- cast R second
            SpellManager.CastOnPosition(vec3(pred.x, target.pos.y, pred.y), 3)
            return
        end
    end
end

local function KillSteal()
    if MyMenu.KillSteal.Q:get() and SpellManager.CanCastSpell(0) and not player.path.isDashing then
        local targets = ObjectManager.GetEnemiesInRange(spellQ.range, player)
        for i, target in ipairs(targets) do
            if MyCommon.IsValidTarget(target, spellQ.range) and not MyCommon.IsUnKillAble(target) then
                local qDMG = GetQDamage(target)
                if target.health and target.health < qDMG then
                    SpellManager.CastOnUnit(target, 0)
                    return
                end
            end
        end
    end
end

local function Combo()
    if MyMenu.Combo.ComboMode:get() == 1 then
        local target = MyCommon.GetTarget(spellQ.range + 50)
        if target and target ~= nil and MyCommon.IsValidTarget(target, spellQ.range + 50) then
            -- if can E hit, QREW Combo
            if CanCastE(target) then
                if MyMenu.Combo.Q:get() and SpellManager.CanCastSpell(0) and MyCommon.IsValidTarget(target, spellQ.range) then
                    SpellManager.CastOnUnit(target, 0)
                end
                if MyMenu.Combo.R:get() and SpellManager.CanCastSpell(3) and player:spellSlot(3).name ~= "LeblancRW" and player:spellSlot(3).name ~= "LeblancRWReturn" then
                    CastR(target)
                end
                if MyMenu.Combo.E:get() and SpellManager.CanCastSpell(2) and MyCommon.IsValidTarget(target, spellE.range) then
                    CastE(target)
                end
                if MyMenu.Combo.W:get() and SpellManager.CanCastSpell(1) and MyCommon.IsValidTarget(target, spellW.range) and player:spellSlot(1).name ~= "LeblancWReturn" and not (SpellManager.CanCastSpell(3) and (player:spellSlot(3).name ~= "LeblancRW" or player:spellSlot(3).name ~= "LeblancRWReturn")) then
                    CastW(target)
                end
            elseif not CanCastE(target) then
                -- if not can E hit, W gapcloser and then QRE combo
                if MyMenu.Combo.W:get() and SpellManager.CanCastSpell(1) and player:spellSlot(1).name ~= "LeblancWReturn" then
                    CastW(target)
                end
                if MyMenu.Combo.Q:get() and SpellManager.CanCastSpell(0) and MyCommon.IsValidTarget(target, spellQ.range) then
                    SpellManager.CastOnUnit(target, 0)
                end
                if MyMenu.Combo.R:get() and SpellManager.CanCastSpell(3) and player:spellSlot(3).name ~= "LeblancRW" and player:spellSlot(3).name ~= "LeblancRWReturn" then
                    CastR(target)
                end
                if MyMenu.Combo.E:get() and SpellManager.CanCastSpell(2) and MyCommon.IsValidTarget(target, spellE.range) then
                    CastE(target)
                end
            end
        end
    elseif MyMenu.Combo.ComboMode:get() == 2 then
        local target = MyCommon.GetTarget(spellW.range)
        if target and target ~= nil and MyCommon.IsValidTarget(target, spellW.range) then
            if MyMenu.Combo.Q:get() and SpellManager.CanCastSpell(0) and MyCommon.IsValidTarget(target, spellQ.range) then
                SpellManager.CastOnUnit(target, 0)
            end
            if MyMenu.Combo.W:get() and SpellManager.CanCastSpell(1) and MyCommon.IsValidTarget(target, spellW.range) and player:spellSlot(1).name ~= "LeblancWReturn" then
                -- first W Cast
                local pred = Prediction.GetPrediction(spellW, target)
                if pred then
                    SpellManager.CastOnPosition(vec3(pred.x, target.pos.y, pred.y), 1)
                    -- RW Combo???
                    if MyMenu.Combo.R:get() and SpellManager.CanCastSpell(3) and player:spellSlot(3).name == "LeblancRW" then
                        SpellManager.CastOnPosition(target.pos, 3)
                    end
                end
                -- RW Combo for fix (sometimes it wont go run the R cast)
                if MyMenu.Combo.R:get() and SpellManager.CanCastSpell(3) and player:spellSlot(3).name == "LeblancRW" then
                    SpellManager.CastOnPosition(target.pos, 3)
                end
            end
            -- RW Combo for fix (sometimes it wont go run the R cast)
            if MyMenu.Combo.R:get() and SpellManager.CanCastSpell(3) and player:spellSlot(3).name == "LeblancRW" then
                SpellManager.CastOnPosition(target.pos, 3)
            end
            if MyMenu.Combo.E:get() and SpellManager.CanCastSpell(2) and MyCommon.IsValidTarget(target, spellE.range) and NetManager.TickCount() - spellW.lastCastTime > 680 then
                if player:spellSlot(3).name ~= "LeblancRW" or not SpellManager.CanCastSpell(3) then
                    if not isDashing then
                        CastE(target)
                    end
                end
            end
        end
    end
    if MyMenu.Combo.WGapcloser:get() and SpellManager.CanCastSpell(1) and player:spellSlot(1).name == "LeblancW" then
        if #ObjectManager.GetEnemiesInRange(spellQ.range + 50, player) == 0 then
            local newTarget = MyCommon.GetTarget(1200)
            if newTarget and newTarget ~= nil and MyCommon.IsValidTarget(newTarget, 1200) then
                if SpellManager.CanCastSpell(3) and player:spellSlot(3).name ~= "LeblancRWReturn" then
                    local pos = VectorManager.Extend(player.pos, newTarget.pos, 600)
                    if pos then
                        SpellManager.CastOnPosition(pos, 1)
                        return
                    end
                end
            end
        end
    end
    if MyMenu.Combo.WBack:get() and SpellManager.CanCastSpell(1) and player:spellSlot(1).name == "LeblancWReturn"then
        local target1 = MyCommon.GetTarget(spellQ.range)
        local target2 = MyCommon.GetTarget(spellE.range)
        local target3 = MyCommon.GetTarget(1100)
        if target1 and MyCommon.IsValidTarget(target1) and target1.pos then
            if spellW.lastPosition and not VectorManager.IsZeroPoint(spellW.lastPosition) and spellW.lastPosition:dist(target1.pos) <= (650 + player.boundingRadius) and ((NetManager.TickCount() - spellW.lastCastTime) > (750 + NetManager.Ping())) then
                SpellManager.CastOnPosition(player.pos, 1)
                return
            end
        elseif target2 and MyCommon.IsValidTarget(target2) and target2.pos then
            if spellW.lastPosition and not VectorManager.IsZeroPoint(spellW.lastPosition) and spellW.lastPosition:dist(target2.pos) <= (650 + player.boundingRadius) and ((NetManager.TickCount() - spellW.lastCastTime) > (750 + NetManager.Ping())) then
                SpellManager.CastOnPosition(player.pos, 1)
                return
            end
        elseif target3 and MyCommon.IsValidTarget(target3) and target3.pos then
            if spellW.lastPosition and not VectorManager.IsZeroPoint(spellW.lastPosition) and spellW.lastPosition:dist(target3.pos) <= (650 + player.boundingRadius) and ((NetManager.TickCount() - spellW.lastCastTime) > (750 + NetManager.Ping())) then
                SpellManager.CastOnPosition(player.pos, 1)
                return
            end
        end
    end
end

local function Harass()
    if not MyMenu.Harass.ProTurret:get() and ObjectManager.IsUnderEnemyTurret(player) then
        if MyMenu.Harass.WBack:get() and player:spellSlot(1).name and player:spellSlot(1).name == "LeblancWReturn" and SpellManager.CanCastSpell(1) then
            SpellManager.CastOnPosition(player.pos, 1)
            return
        end
        return
    end
    -- just for QEQWQ fast Harass (if i can hit)
    -- never care the passive
    if MyCommon.GetManaPercent() >= MyMenu.Harass.ManaMin:get() then
        local target = MyCommon.GetTarget(spellQ.range + 50)
        if target and target ~= nil and MyCommon.IsValidTarget(target, spellQ.range + 50) then
            -- if can E hit, QREW Harass
            if CanCastE(target) then
                if MyMenu.Harass.Q:get() and SpellManager.CanCastSpell(0) and MyCommon.IsValidTarget(target, spellQ.range) then
                    SpellManager.CastOnUnit(target, 0)
                end
                if MyMenu.Harass.R:get() and SpellManager.CanCastSpell(3) and player:spellSlot(3).name ~= "LeblancRW" and player:spellSlot(3).name ~= "LeblancRWReturn" then
                    CastR(target)
                end
                if MyMenu.Harass.E:get() and SpellManager.CanCastSpell(2) and MyCommon.IsValidTarget(target, spellE.range) then
                    CastE(target)
                end
                if MyMenu.Harass.W:get() and SpellManager.CanCastSpell(1) and MyCommon.IsValidTarget(target, spellW.range) and player:spellSlot(1).name ~= "LeblancWReturn" and not (SpellManager.CanCastSpell(3) and (player:spellSlot(3).name ~= "LeblancRW" or player:spellSlot(3).name ~= "LeblancRWReturn")) then
                    CastW(target)
                end
            elseif not CanCastE(target) then
                -- if not can E hit, W gapcloser and then QRE Harass
                if MyMenu.Harass.W:get() and SpellManager.CanCastSpell(1) and player:spellSlot(1).name ~= "LeblancWReturn" then
                    CastW(target)
                end
                if MyMenu.Harass.Q:get() and SpellManager.CanCastSpell(0) and MyCommon.IsValidTarget(target, spellQ.range) then
                    SpellManager.CastOnUnit(target, 0)
                end
                if MyMenu.Harass.R:get() and SpellManager.CanCastSpell(3) and player:spellSlot(3).name ~= "LeblancRW" and player:spellSlot(3).name ~= "LeblancRWReturn" then
                    CastR(target)
                end
                if MyMenu.Harass.E:get() and SpellManager.CanCastSpell(2) and MyCommon.IsValidTarget(target, spellE.range) then
                    CastE(target)
                end
            end
        end
    end
    if MyMenu.Harass.WBack:get() and player:spellSlot(1).name and player:spellSlot(1).name == "LeblancWReturn" and SpellManager.CanCastSpell(1) then
        SpellManager.CastOnPosition(player.pos, 1)
        return
    end
end

local function Clear()
    if MyMenu.LaneClear.Q:get() and SpellManager.CanCastSpell(0) then
        if MyCommon.GetManaPercent() >= MyMenu.LaneClear.ManaMin:get() then
            local minions = ObjectManager.GetMinions(spellQ.range, TEAM_ENEMY)
            if minions and #minions > 0 then
                for i, minion in ipairs(minions) do
                    if minion and MyCommon.IsValidTarget(minion, spellQ.range) then
                        if minion.health then
                            if minion.health < GetQDamage(minion) then
                                SpellManager.CastOnUnit(minion, 0)
                            end
                        end
                    end
                end
            end
        end
    end
    if MyMenu.JungleClear.Q:get() and SpellManager.CanCastSpell(0) then
        if MyCommon.GetManaPercent() >= MyMenu.JungleClear.ManaMin:get() then
            local mobs = ObjectManager.GetMinions(spellQ.range, TEAM_NEUTRAL)
            if mobs and #mobs > 0 then
                for i, mob in ipairs(mobs) do
                    if mob and MyCommon.IsValidTarget(mob, spellQ.range) then
                        if MyCommon.IsBigMob(mob) then
                            if mob.health > 0 and MyCommon.IsValidTarget(mob, spellQ.range) then
                                SpellManager.CastOnUnit(mob, 0)
                                return
                            end
                        else
                            if mob.health > 0 and MyCommon.IsValidTarget(mob, spellQ.range) then
                                SpellManager.CastOnUnit(mob, 0)
                                return
                            end
                        end
                    end
                end
            end
        end
    end
    if MyMenu.JungleClear.W:get() and SpellManager.CanCastSpell(1) and player:spellSlot(1).name and player:spellSlot(1).name ~= "LeblancWReturn" then
        if MyCommon.GetManaPercent() >= MyMenu.JungleClear.ManaMin:get() then
            local mobs = ObjectManager.GetMinions(spellQ.range, TEAM_NEUTRAL)
            if mobs and #mobs > 0 then
                for i, mob in ipairs(mobs) do
                    if mob and MyCommon.IsValidTarget(mob, spellQ.range) then
                        if MyCommon.IsBigMob(mob) then
                            if mob.health > 0 and MyCommon.IsValidTarget(mob, spellQ.range) then
                                SpellManager.CastOnPosition(mob.pos, 1)
                                return
                            end
                        else
                            local BestPos, BestHit = FarmManager.GetBestCircularFarmPosition(spellW.radius, mobs)
                            if BestHit and BestHit >= 1 and BestPos then
                                SpellManager.CastOnPosition(vec3(BestPos.x, game.mousePos.y, BestPos.y), 1)
                                return
                            end
                        end
                    end
                end
            end
        end
    end
    if MyMenu.JungleClear.E:get() and SpellManager.CanCastSpell(1) then
        if MyCommon.GetManaPercent() >= MyMenu.JungleClear.ManaMin:get() then
            local mobs = ObjectManager.GetMinions(spellQ.range, TEAM_NEUTRAL)
            if mobs and #mobs > 0 then
                for i, mob in ipairs(mobs) do
                    if mob and MyCommon.IsValidTarget(mob, spellQ.range) then
                        if MyCommon.IsBigMob(mob) then
                            if mob.health > 0 and MyCommon.IsValidTarget(mob, spellQ.range) then
                                local pred = Prediction.GetPrediction(spellE, mob)
                                if pred then
                                    SpellManager.CastOnPosition(vec3(pred.x, mob.pos.y, pred.y), 2)
                                    return
                                end
                            end
                        end
                    end
                end
            end
        end
    end
end

local function OnMyTick()
    if player.isDead or player.isRecalling or chat.isOpened then
        return
    end
    if MyMenu.Key.Flee:get() then
        Flee()
    end
    if MyMenu.Key.SemiQE:get() then
        SemiQE()
        return
    end
    if MyMenu.Key.SemiERE:get() then
        SemiERE()
        return
    end
    if DelayTick.CanTickEvent() then
        KillSteal()
    end
    if MyMenu.Key.Combo:get() then
        Combo()
    end
    if MyMenu.Key.Harass:get() then
        Harass()
    end
    if DelayTick.CanTickEvent() then
        if MyMenu.Key.LaneClear:get() then
            if MyMenu.Harass.ProAllow:get() then
                Harass()
            end
            if FarmManager.Enabled then
                Clear()
            end
        end
    end
end

local function OnMyProcessSpellCast(spellData)
    if spellData and spellData.owner and spellData.owner.ptr == player.ptr and spellData.name then
        if spellData.name == "LeblancW" then
            spellW.lastCastTime = NetManager.TickCount()
            spellW.lastPosition = vec3(spellData.startPos.x, player.pos.y, spellData.startPos.z)
            isDashing = true
            DelayAction.Add(function() 
                isDashing = false 
            end, (0.35 + network.latency))
        end
        if spellData.name == "LeblancRWReturn" then
            isDashing = true
            DelayAction.Add(function() 
                isDashing = false 
            end, (0.08 + network.latency))
        end
    end
end

local function OnMyDraw()
    if player.isDead or chat.isOpened then
        return
    end
    if MyMenu.Draw.Q:get() and MyCommon.CanDrawCircle(spellQ.range) then
        if MyMenu.Draw.Ready:get() then
            if SpellManager.CanCastSpell(0) then
                graphics.draw_circle(player.pos, spellQ.range, 2, MyMenu.Draw.colorq:get(), 100)
            end
        else
            graphics.draw_circle(player.pos, spellQ.range, 2, MyMenu.Draw.colorq:get(), 100)
        end
    end
    if MyMenu.Draw.W:get() and MyCommon.CanDrawCircle(spellW.range) then
        if MyMenu.Draw.Ready:get() then
            if SpellManager.CanCastSpell(1) then
                graphics.draw_circle(player.pos, spellW.range, 2, MyMenu.Draw.colorw:get(), 100)
            end
        else
            graphics.draw_circle(player.pos, spellW.range, 2, MyMenu.Draw.colorw:get(), 100)
        end
    end
    if MyMenu.Draw.E:get() and MyCommon.CanDrawCircle(spellE.range) then
        if MyMenu.Draw.Ready:get() then
            if SpellManager.CanCastSpell(2) then
                graphics.draw_circle(player.pos, spellE.range, 2, MyMenu.Draw.colore:get(), 100)
            end
        else
            graphics.draw_circle(player.pos, spellE.range, 2, MyMenu.Draw.colore:get(), 100)
        end
    end
    if MyMenu.Draw.ComboMode:get() then
        local drawWidth = graphics.width - (minimap.width * 2)
        local drawHeight = graphics.height - (minimap.height / 5)
        local text = "QR First"
        if MyMenu.Combo.ComboMode:get() == 2 then
            text = "WR First"
        end
        graphics.draw_text_2D("Combo Style: "..text, 16, drawWidth, drawHeight - 60, graphics.argb(255, 255, 255, 255))
    end
end

cb.add(cb.tick, OnMyTick)
cb.add(cb.spell, OnMyProcessSpellCast)
cb.add(cb.draw, OnMyDraw)


return leblancPlugin

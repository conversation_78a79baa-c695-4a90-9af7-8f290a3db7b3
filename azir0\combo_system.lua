-- Azir 高级连招系统
-- 根据§4.5 API格式和§5.1 ComboBuilder.execute逻辑实现
-- 集成ProPlayerDB_V9专业连招数据

local orb = module.internal("orb")
local pred = module.internal("pred")
local TS = module.internal("TS")
local menu = module.load("<PERSON>","azir/menu")
local q_module = module.load("<PERSON>","azir/q")
local w_module = module.load("<PERSON>","azir/w")
local e_module = module.load("<PERSON>","azir/e")
local r_module = module.load("<PERSON>","azir/r")

-- 连招配置数据库 (ProPlayerDB_V9格式)
local combo_database = {
    -- 爆发连招配置
    burst = {
        name = "瞬间击杀连招",
        description = "最高爆发伤害，确保击杀成功率",
        sequence = {"W", "E", "Q", "R"},
        conditions = {
            min_mana = 200,
            target_health_max = 0.6,
            max_enemies_nearby = 3,
            min_soldier_count = 1
        },
        timing = {
            w_delay = 0.1,
            e_delay = 0.25,
            q_delay = 0.15,
            r_delay = 0.3
        },
        success_rate = 0.85
    },
    
    -- 骚扰连招配置
    harass = {
        name = "消耗撤离连招",
        description = "持续消耗敌方血量并安全撤离",
        sequence = {"W", "Q", "retreat"},
        conditions = {
            min_mana = 120,
            target_health_min = 0.3,
            max_enemies_nearby = 2,
            safe_distance = 600
        },
        timing = {
            w_delay = 0.1,
            q_delay = 0.2,
            retreat_delay = 0.5
        },
        success_rate = 0.75
    },
    
    -- 明星连招配置
    star = {
        name = "高光操作连招",
        description = "观赏性与实用性兼具的精彩连招",
        sequence = {"W", "E", "Flash", "Q", "R", "W", "Q"},
        conditions = {
            min_mana = 250,
            target_health_min = 0.4,
            flash_available = true,
            min_skill_count = 3
        },
        timing = {
            w_delay = 0.1,
            e_delay = 0.2,
            flash_delay = 0.1,
            q_delay = 0.05,
            r_delay = 0.2,
            w2_delay = 0.3,
            q2_delay = 0.15
        },
        success_rate = 0.65
    }
}

-- 连招状态管理
local combo_state = {
    current_combo = nil,
    step_index = 0,
    start_time = 0,
    target = nil,
    last_step_time = 0
}

-- 连招伤害计算 (§navmesh.calcPos优化)
local function calculate_total_damage(target, combo_type)
    if not target then return 0 end
    
    local damage = 0
    local combo_config = combo_database[combo_type]
    
    if not combo_config then return 0 end
    
    -- 根据连招序列计算总伤害
    for _, skill in ipairs(combo_config.sequence) do
        if skill == "W" then
            local w_level = math.min(math.max(player:spellSlot(1).level, 1), 5)
            if w_level > 0 then
                local base_damage = {50, 55, 60, 65, 70}
                damage = damage + base_damage[w_level] + (player.flatMagicDamageMod or 0) * 0.6
            end
        elseif skill == "Q" then
            local q_level = math.min(math.max(player:spellSlot(0).level, 1), 5)
            if q_level > 0 then
                local base_damage = {70, 105, 140, 175, 210}
                damage = damage + base_damage[q_level] + (player.flatMagicDamageMod or 0) * 0.5
            end
        elseif skill == "R" then
            local r_level = math.min(math.max(player:spellSlot(3).level, 1), 3)
            if r_level > 0 then
                local base_damage = {150, 225, 300}
                damage = damage + base_damage[r_level] + (player.flatMagicDamageMod or 0) * 0.6
            end
        end
    end
    
    -- 考虑目标魔抗
    local magic_resist = target.magicResist or target.spellBlock or 30 -- 默认魔抗值
    local damage_reduction = magic_resist / (magic_resist + 100)
    damage = damage * (1 - damage_reduction)
    
    return damage
end

-- 检查连招条件
local function check_combo_conditions(target, combo_type)
    if not target or not combo_database[combo_type] then return false end
    
    local config = combo_database[combo_type]
    local conditions = config.conditions
    
    -- 检查蓝量
    if player.par < conditions.min_mana then return false end
    
    -- 检查目标血量
    local health_percent = target.health / target.maxHealth
    if conditions.target_health_max and health_percent > conditions.target_health_max then return false end
    if conditions.target_health_min and health_percent < conditions.target_health_min then return false end
    
    -- 检查附近敌人数量
    if conditions.max_enemies_nearby then
        local enemy_count = target.pos2D:countEnemies(600)
        if enemy_count > conditions.max_enemies_nearby then return false end
    end
    
    -- 检查沙兵数量
    if conditions.min_soldier_count and #w_module.soldiers < conditions.min_soldier_count then
        -- 如果W技能可用，可以先放置沙兵
        if player:spellSlot(1).state ~= 0 then return false end
    end
    
    -- 检查闪现可用性
    if conditions.flash_available then
        local flash_slot = player:spellSlot(4).name == "SummonerFlash" and 4 or 5
        if player:spellSlot(flash_slot).state ~= 0 then return false end
    end
    
    return true
end

-- 执行连招步骤
local function execute_combo_step(combo_type, step_index)
    local config = combo_database[combo_type]
    if not config or step_index > #config.sequence then return false end
    
    local skill = config.sequence[step_index]
    local timing = config.timing
    local target = combo_state.target
    
    -- 检查时机
    local step_delay_key = skill:lower() .. "_delay"
    if step_index > 1 then
        local required_delay = timing[step_delay_key] or 0.1
        if os.clock() - combo_state.last_step_time < required_delay then
            return false -- 还没到执行时间
        end
    end
    
    -- 执行技能
    local success = false
    
    if skill == "W" then
        success = w_module.invoke()
    elseif skill == "Q" then
        success = q_module.invoke_combo(combo_type)
    elseif skill == "E" then
        success = e_module.invoke()
    elseif skill == "R" then
        success = r_module.invoke()
    elseif skill == "Flash" then
        local flash_slot = player:spellSlot(4).name == "SummonerFlash" and 4 or 5
        if target and player:spellSlot(flash_slot).state == 0 then
            local flash_pos = player.pos2D:extend(target.pos2D, 400)
            success = player:castSpell("pos", flash_slot, vec3(flash_pos.x, player.y, flash_pos.y))
        end
    elseif skill == "retreat" then
        -- 安全撤离逻辑
        local safe_pos = player.pos2D:extend(target.pos2D, -400)
        if not navmesh.isWall(safe_pos) then
            player:move(vec3(safe_pos.x, player.y, safe_pos.y))
            success = true
        end
    end
    
    if success then
        combo_state.step_index = step_index + 1
        combo_state.last_step_time = os.clock()
        return true
    end
    
    return false
end

-- 主连招执行函数
local function execute_combo(combo_type, target)
    if not target or not combo_database[combo_type] then return false end
    
    -- 检查连招条件
    if not check_combo_conditions(target, combo_type) then return false end
    
    -- 初始化连招状态
    if combo_state.current_combo ~= combo_type then
        combo_state.current_combo = combo_type
        combo_state.step_index = 1
        combo_state.start_time = os.clock()
        combo_state.target = target
        combo_state.last_step_time = os.clock()
    end
    
    -- 执行当前步骤
    if execute_combo_step(combo_type, combo_state.step_index) then
        return true
    end
    
    -- 检查连招是否完成
    local config = combo_database[combo_type]
    if combo_state.step_index > #config.sequence then
        combo_state.current_combo = nil
        combo_state.step_index = 0
        return false
    end
    
    return false
end

-- 智能连招选择
local function select_best_combo(target)
    if not target then return nil end
    
    local health_percent = target.health / target.maxHealth
    local distance = target.pos:dist(player.pos)
    local total_damage = 0
    
    -- 优先级：击杀 > 明星 > 骚扰
    for combo_type, config in pairs(combo_database) do
        if check_combo_conditions(target, combo_type) then
            local damage = calculate_total_damage(target, combo_type)
            
            -- 如果能击杀，优先选择爆发连招
            if damage > target.health and combo_type == "burst" then
                return combo_type
            end
            
            -- 记录最高伤害连招
            if damage > total_damage then
                total_damage = damage
            end
        end
    end
    
    -- 根据情况选择连招
    if health_percent < 0.4 and check_combo_conditions(target, "burst") then
        return "burst"
    elseif keyboard.isKeyDown(0x20) and check_combo_conditions(target, "star") then -- 空格键
        return "star"
    elseif health_percent > 0.5 and check_combo_conditions(target, "harass") then
        return "harass"
    end
    
    return nil
end

-- 导出函数
return {
    execute_combo = execute_combo,
    select_best_combo = select_best_combo,
    calculate_total_damage = calculate_total_damage,
    check_combo_conditions = check_combo_conditions,
    combo_database = combo_database,
    combo_state = combo_state
}

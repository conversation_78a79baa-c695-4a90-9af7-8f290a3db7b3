

local ove_0_20 = {}

local function ove_0_21(arg_5_0)
	-- print 5
	if type(arg_5_0) ~= "table" then
		return type(arg_5_0)
	end

	local slot_5_0 = 1

	for iter_5_0 in pairs(arg_5_0) do
		if arg_5_0[slot_5_0] ~= nil then
			slot_5_0 = slot_5_0 + 1
		else
			return "table"
		end
	end

	if slot_5_0 == 1 then
		return "table"
	else
		return "array"
	end
end

local function ove_0_22(arg_6_0)
	-- print 6
	local slot_6_0 = {
		"\\",
		"\"",
		"/",
		"\b",
		"\f",
		"\n",
		"\r",
		"\t"
	}
	local slot_6_1 = {
		"\\",
		"\"",
		"/",
		"b",
		"f",
		"n",
		"r",
		"t"
	}

	for iter_6_0, iter_6_1 in ipairs(slot_6_0) do
		arg_6_0 = arg_6_0:gsub(iter_6_1, "\\" .. slot_6_1[iter_6_0])
	end

	return arg_6_0
end

local function ove_0_23(arg_7_0, arg_7_1, arg_7_2, arg_7_3)
	-- print 7
	arg_7_1 = arg_7_1 + #arg_7_0:match("^%s*", arg_7_1)

	if arg_7_0:sub(arg_7_1, arg_7_1) ~= arg_7_2 then
		if arg_7_3 then
			return
		end

		return arg_7_1, false
	end

	return arg_7_1 + 1, true
end

local function ove_0_24(arg_8_0, arg_8_1, arg_8_2)
	-- print 8
	arg_8_2 = arg_8_2 or ""

	local slot_8_0 = "End of input found while parsing string."

	if arg_8_1 > #arg_8_0 then
		return
	end

	local slot_8_1 = arg_8_0:sub(arg_8_1, arg_8_1)

	if slot_8_1 == "\"" then
		return arg_8_2, arg_8_1 + 1
	end

	if slot_8_1 ~= "\\" then
		return ove_0_24(arg_8_0, arg_8_1 + 1, arg_8_2 .. slot_8_1)
	end

	local slot_8_2 = {
		b = "\b",
		r = "\r",
		n = "\n",
		f = "\f",
		t = "\t"
	}
	local slot_8_3 = arg_8_0:sub(arg_8_1 + 1, arg_8_1 + 1)

	if not slot_8_3 then
		return
	end

	return ove_0_24(arg_8_0, arg_8_1 + 2, arg_8_2 .. (slot_8_2[slot_8_3] or slot_8_3))
end

local function ove_0_25(arg_9_0, arg_9_1)
	-- print 9
	local slot_9_0 = arg_9_0:match("^-?%d+%.?%d*[eE]?[+-]?%d*", arg_9_1)
	local slot_9_1 = tonumber(slot_9_0)

	if not slot_9_1 then
		return
	end

	return slot_9_1, arg_9_1 + #slot_9_0
end

function ove_0_20.stringify(arg_10_0, arg_10_1)
	-- print 10
	local slot_10_0 = {}
	local slot_10_1 = ove_0_21(arg_10_0)

	if slot_10_1 == "array" then
		if arg_10_1 then
			return
		end

		slot_10_0[#slot_10_0 + 1] = "["

		for iter_10_0, iter_10_1 in ipairs(arg_10_0) do
			if iter_10_0 > 1 then
				slot_10_0[#slot_10_0 + 1] = ", "
			end

			slot_10_0[#slot_10_0 + 1] = ove_0_20.stringify(iter_10_1)
		end

		slot_10_0[#slot_10_0 + 1] = "]"
	elseif slot_10_1 == "table" then
		if arg_10_1 then
			return
		end

		slot_10_0[#slot_10_0 + 1] = "{"

		for iter_10_2, iter_10_3 in pairs(arg_10_0) do
			if #slot_10_0 > 1 then
				slot_10_0[#slot_10_0 + 1] = ", "
			end

			slot_10_0[#slot_10_0 + 1] = ove_0_20.stringify(iter_10_2, true)
			slot_10_0[#slot_10_0 + 1] = ":"
			slot_10_0[#slot_10_0 + 1] = ove_0_20.stringify(iter_10_3)
		end

		slot_10_0[#slot_10_0 + 1] = "}"
	elseif slot_10_1 == "string" then
		return "\"" .. ove_0_22(arg_10_0) .. "\""
	elseif slot_10_1 == "number" then
		if arg_10_1 then
			return "\"" .. tostring(arg_10_0) .. "\""
		end

		return tostring(arg_10_0)
	elseif slot_10_1 == "boolean" then
		return tostring(arg_10_0)
	elseif slot_10_1 == "nil" then
		return "null"
	else
		return
	end

	return table.concat(slot_10_0)
end

ove_0_20.null = {}

function ove_0_20.parse(arg_11_0, arg_11_1, arg_11_2)
	-- print 11
	arg_11_1 = arg_11_1 or 1

	if arg_11_1 > #arg_11_0 then
		return
	end

	local slot_11_0 = arg_11_1 + #arg_11_0:match("^%s*", arg_11_1)
	local slot_11_1 = arg_11_0:sub(slot_11_0, slot_11_0)

	if slot_11_1 == "{" then
		local slot_11_2 = {}
		local slot_11_3 = true
		local slot_11_4 = true

		slot_11_0 = slot_11_0 + 1

		while true do
			local slot_11_5

			slot_11_5, slot_11_0 = ove_0_20.parse(arg_11_0, slot_11_0, "}")

			if slot_11_5 == nil then
				return slot_11_2, slot_11_0
			end

			if not slot_11_4 then
				return
			end

			slot_11_0 = ove_0_23(arg_11_0, slot_11_0, ":", true)
			slot_11_2[slot_11_5], slot_11_0 = ove_0_20.parse(arg_11_0, slot_11_0)
			slot_11_0, slot_11_4 = ove_0_23(arg_11_0, slot_11_0, ",")
		end
	elseif slot_11_1 == "[" then
		local slot_11_6 = {}
		local slot_11_7 = true
		local slot_11_8 = true

		slot_11_0 = slot_11_0 + 1

		while true do
			local slot_11_9

			slot_11_9, slot_11_0 = ove_0_20.parse(arg_11_0, slot_11_0, "]")

			if slot_11_9 == nil then
				return slot_11_6, slot_11_0
			end

			if not slot_11_8 then
				return
			end

			slot_11_6[#slot_11_6 + 1] = slot_11_9
			slot_11_0, slot_11_8 = ove_0_23(arg_11_0, slot_11_0, ",")
		end
	elseif slot_11_1 == "\"" then
		return ove_0_24(arg_11_0, slot_11_0 + 1)
	elseif slot_11_1 == "-" or slot_11_1:match("%d") then
		return ove_0_25(arg_11_0, slot_11_0)
	elseif slot_11_1 == arg_11_2 then
		return nil, slot_11_0 + 1
	else
		local slot_11_10 = {
			["true"] = true,
			["false"] = false,
			null = ove_0_20.null
		}

		for iter_11_0, iter_11_1 in pairs(slot_11_10) do
			local slot_11_11 = slot_11_0 + #iter_11_0 - 1

			if arg_11_0:sub(slot_11_0, slot_11_11) == iter_11_0 then
				return iter_11_1, slot_11_11 + 1
			end
		end

		local slot_11_12 = "position " .. slot_11_0 .. ": " .. arg_11_0:sub(slot_11_0, slot_11_0 + 10)

		return
	end
end

return ove_0_20

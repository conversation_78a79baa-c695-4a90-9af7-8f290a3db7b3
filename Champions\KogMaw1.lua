local KogMawPlugin = {}
--local ui = module.load("<PERSON>", "ui");
local DelayAction = module.load("<PERSON>", "Core/DelayAction")
local DelayTick = module.load("<PERSON>", "Core/DelayTick")
local Prediction = module.load("<PERSON>", "Core/Prediction")
local BuffManager = module.load("<PERSON>", "Library/BuffManager")
local CalculateManager = module.load("<PERSON>", "Library/CalculateManager")
--local FarmManager = module.load("<PERSON>", "Library/FarmManager")
local ItemManager = module.load("<PERSON>", "Library/ItemManager")
local NetManager = module.load("<PERSON>", "Library/NetManager")
local ObjectManager = module.load("<PERSON>", "Library/ObjectManager")
local OrbManager = module.load("<PERSON>", "Library/OrbManager")
local SpellManager = module.load("<PERSON>", "Library/SpellManager")
local VectorManager = module.load("<PERSON>", "Library/VectorManager")
local common = module.load("<PERSON>", "Utility/common")
local Curses = module.load("<PERSON>", "<PERSON>s");
local ts = module.internal("TS")
local orb = module.internal("orb")
local preds = module.internal("pred")

local WRange = {130, 150, 170, 190, 210}
local RRange = {1300, 1550, 1800}
local Stacks = 0

local qPred = {
	delay = 0.25,
	width = 60,
	speed = 1650,
	boundingRadiusMod = 1,
	collision = {hero = true, minion = true, wall = true}
}

local ePred = {
	delay = 0.25,
	width = 100,
	speed = 1350,
	boundingRadiusMod = 1,
	collision = {hero = false, minion = false, wall = true}
}

local rPred = {
	delay = 0.85, --1.1
	radius = 100,
	speed = math.huge,
	boundingRadiusMod = 0,
}


local MyMenu

function KogMawPlugin.Load(GlobalMenu)
    MyMenu = GlobalMenu


    MyMenu.Key:keybind("tap", "R Tap Key", "T", nil)

    MyMenu:menu("combo", "Combo Settings")
    MyMenu.combo:header("xd", "Q Settings")
    MyMenu.combo:boolean("q", "Use Q", true)
    MyMenu.combo:boolean("sp1", "Use Slow Pred", false)

    MyMenu.combo:header("xd", "W Settings")
    MyMenu.combo:boolean("w", "Use W", true)
    MyMenu.combo:dropdown("wmode", "Mode", 1, {"Inside AA Range", "Inside AA+W Range"})

    MyMenu.combo:header("xd", "E Settings")
    MyMenu.combo:boolean("e", "Use E", true)
    MyMenu.combo:dropdown("mode", "Mode", 1, {"Out of AA Range", "Always"})

    MyMenu.combo:header("xd", "R Settings")
    MyMenu.combo:boolean("r", "Use Smart R", true)
    MyMenu.combo:boolean("sp", "Use Slow Pred", false)
    MyMenu.combo:boolean("cced", "Auto R on CC", true)
    MyMenu.combo:boolean("aa", "Use in AA Range", false)
    MyMenu.combo:slider("stacks", "Max Stacks", 3, 1, 10, 1)
    MyMenu.combo:slider("rhp", "What HP% to Ult", 40, 0, 40, 5)

    MyMenu:menu("harass", "Harass Settings")
    MyMenu.harass:header("xd", "Q Settings")
    MyMenu.harass:boolean("q", "Use Q", true)
    MyMenu.harass:header("xd", "W Settings")
    MyMenu.harass:boolean("e", "use E", true)
    MyMenu.harass:slider("Mana", "Min. Mana Percent: ", 50, 0, 100, 10)

    MyMenu:menu("auto", "Killsteal Settings")
    MyMenu.auto:header("xd", "KillSteal Settings")
    MyMenu.auto:boolean("uks", "Use Killsteal", true)
    MyMenu.auto:boolean("uksq", "Use Q on Killsteal", true)
    MyMenu.auto:boolean("ukse", "Use E on Killsteal", true)
    MyMenu.auto:boolean("uksr", "Use R on Killsteal", true)

    MyMenu:menu("draws", "Draw Settings")
    MyMenu.draws:header("xd", "Drawing Options")
    MyMenu.draws:boolean("q", "Draw Q Range", true)
    MyMenu.draws:color("colorq", "^ color", 255, 233, 121, 121)
end

local function select_Rtarget(res, obj, dist)
	if player:spellSlot(3).state == 0 then
		if dist > RRange[player:spellSlot(3).level] then
			return
		end
		res.obj = obj
		return true
	end
end

local function select_target(res, obj, dist)
	if dist > 1230 then
		return
	end
	res.obj = obj
	return true
end

local function get_target(func)
	return ts.get_result(func).obj
end


local trace_filter = function(input, segment, target)
	if preds.trace.linear.hardlock(input, segment, target) then
		return true
	end
	if preds.trace.linear.hardlockmove(input, segment, target) then
		return true
	end
	if segment.startPos:dist(segment.endPos) <= 1150 then
		return true
	end
	if preds.trace.newpath(target, 0.033, 0.5) then
		return true
	end
end

local trace_rfilter = function(input, segment, target)
	if preds.trace.circular.hardlock(input, segment, target) then
		return true
	end
	if preds.trace.circular.hardlockmove(input, segment, target) then
		return true
	end
	if preds.trace.newpath(target, 0.033, 0.5) then
		return true
	end
end

local function qDmg(target)
	if player.path.serverPos:dist(target.path.serverPos) < 1175 then 
		local base_damage = (30 + (50 * player:spellSlot(0).level)) + (common.GetTotalAP() * 0.5)
		local total = base_damage
		return common.CalculateMagicDamage(target, total)
	end
end

local function eDmg(target)
	if player.path.serverPos:dist(target.path.serverPos) < 1280 then 
		local base_damage = (15 + (45 * player:spellSlot(2).level)) + (common.GetTotalAP() * 0.5)
		local total = base_damage
		return common.CalculateMagicDamage(target, total)
	end
end

local function rDmg(target)
	if player.path.serverPos:dist(target.path.serverPos) < 1800 then 
		local base_damage = (60 + (40 * player:spellSlot(3).level)) + (common.GetBonusAD() * 0.65) + (common.GetTotalAP() * 0.25)
		local hp = (1 - (target.health / target.maxHealth)) * 0.833
		hp = (hp * 100) > 50 and 0.5 or hp
		local total = base_damage + (hp * base_damage)
		return common.CalculateMagicDamage(target, total)
	end
end


local function HasSionBuff(e)
	for i = 0, e.buffManager.count - 1 do
		local buff = e.buffManager:get(i)
		if buff and buff.valid and buff.name == 'sionpassivezombie' then
			return true
		end
	end
end

local function CastQ(target)
	if player:spellSlot(0).state == 0 and player.path.serverPos:dist(target.path.serverPos) < 1120 then
		local seg = preds.linear.get_prediction(qPred, target)
		if seg and seg.startPos:dist(seg.endPos) < 1120 then
			if not preds.collision.get_prediction(qPred, seg, target) then
				if MyMenu.combo.sp1:get() and trace_filter(qPred, seg, target) then
					player:castSpell("pos", 0, vec3(seg.endPos.x, game.mousePos.y, seg.endPos.y))
				elseif not MyMenu.combo.sp1:get() then
					player:castSpell("pos", 0, vec3(seg.endPos.x, game.mousePos.y, seg.endPos.y))
				end
			end
		end
	end
end

local function CastE(target)
	if player:spellSlot(2).state == 0 and player.path.serverPos:dist(target.path.serverPos) < 1230 then
		local seg = preds.linear.get_prediction(ePred, target)
		if seg and seg.startPos:dist(seg.endPos) < 1230 then
			if not preds.collision.get_prediction(ePred, seg, target) then
				player:castSpell("pos", 2, vec3(seg.endPos.x, game.mousePos.y, seg.endPos.y))
			end
		end
	end
end

local function CastR(target)
	if player:spellSlot(3).state == 0 then
		if player.path.serverPos:dist(target.path.serverPos) < RRange[player:spellSlot(3).level] then
			local pos = preds.circular.get_prediction(rPred, target)
            if pos and pos.startPos:dist(pos.endPos) < RRange[player:spellSlot(3).level] then
            	if MyMenu.combo.sp:get() and trace_rfilter(rPred, pos, target) then
                	player:castSpell("pos", 3, vec3(pos.endPos.x, mousePos.y, pos.endPos.y))
                elseif not MyMenu.combo.sp:get() then
                	player:castSpell("pos", 3, vec3(pos.endPos.x, mousePos.y, pos.endPos.y))
                end
            end
		end
	end
end


local function Combo()
	local target = get_target(select_target)
	if target and common.IsValidTarget(target) and not HasSionBuff(target) then
		local d = player.path.serverPos:dist(target.path.serverPos)
		local q = player:spellSlot(0).state == 0
		local w = player:spellSlot(1).state == 0
		local e = player:spellSlot(2).state == 0
		if MyMenu.combo.q:get() and q and d < 1120 and player.par / player.maxPar * 100 >= 10 then
			CastQ(target)
		end
		if MyMenu.combo.e:get() and e and d < 1230 and player.par / player.maxPar * 100 >= 10 then
			if MyMenu.combo.mode:get() == 1 then 
				if d > common.GetAARange(player) + 10 then
					CastE(target)
				end
			elseif MyMenu.combo.mode:get() == 2 then
				CastE(target)
			end
		end
		if MyMenu.combo.w:get() and w then
			if MyMenu.combo.wmode:get() == 1 then
				if d < common.GetAARange() then
					player:castSpell("self", 1)
				end
			elseif MyMenu.combo.wmode:get() == 2 then
				if d < 630 + WRange[player:spellSlot(1).level] then
					player:castSpell("self", 1)
				end
			end
		end
	end
	local target = get_target(select_Rtarget)
	if target and common.IsValidTarget(target) and not HasSionBuff(target) then
		local d = player.path.serverPos:dist(target.path.serverPos)
		local r = player:spellSlot(3).state == 0
		if MyMenu.combo.r:get() and r and d < RRange[player:spellSlot(3).level] and Stacks < MyMenu.combo.stacks:get() then
			if #common.GetEnemyHeroesInRange(500, player.pos) < 2 and common.GetPercentHealth(target) < MyMenu.combo.rhp:get() then
				if MyMenu.combo.aa:get() then
					CastR(target)
				elseif not MyMenu.combo.aa:get() and d > common.GetAARange(player) + 50 then
					CastR(target)
				end
			end
		end
	end
end


local function launchR(pos)
	if pos then
		player:castSpell("pos", 3, vec3(pos.x, pos.y, pos.z));
		orb.core.set_server_pause();
	end
end


local function AutoR()
	if orb.core.is_paused() then return end
	if player:spellSlot(3).state == 0 then
		for i = 0, objManager.enemies_n - 1 do
			local enemy = objManager.enemies[i]
			if enemy and common.IsValidTarget(enemy) and not enemy.buff["sionpassivezombie"] then
				if enemy.buff[5] or enemy.buff[11] or enemy.buff[24] or enemy.buff[29] then
					if enemy.pos:dist(player.pos) < RRange[player:spellSlot(3).level] then
						launchR(enemy.pos)
					end
				end
			end
		end
	end
end

local function Harass()
	if player.par / player.maxPar * 100 >= MyMenu.harass.Mana:get() then
		local target = get_target(select_target)
		if target and common.IsValidTarget(target) and not HasSionBuff(target) then
			local d = player.path.serverPos:dist(target.path.serverPos)
			if MyMenu.harass.q:get() and player:spellSlot(0).state == 0 and d < 1100 then
				CastQ(target)
			end
			if MyMenu.harass.e:get() and player:spellSlot(2).state == 0 and d < 1200 then
				CastE(target)
			end
		end
	end
end

local function KillSteal()
	for i = 0, objManager.enemies_n - 1 do
		local enemy = objManager.enemies[i]
		local d = player.path.serverPos:dist(enemy.path.serverPos)
 		if enemy and common.IsValidTarget(enemy) and MyMenu.auto.uks:get() and not HasSionBuff(enemy) and d < 1240 then
  			if MyMenu.auto.uksq:get() and player:spellSlot(0).state == 0 and d < 1120 and enemy.health < qDmg(enemy) then
  				CastQ(enemy)
	  		end
	  		if MyMenu.auto.ukse:get() and player:spellSlot(2).state == 0 and d < 1220 and enemy.health < eDmg(enemy) then
	  			CastE(enemy)
	  		end
	  		if MyMenu.auto.ukse:get() and player:spellSlot(3).state == 0 and d < RRange[player:spellSlot(3).level] and enemy.health < rDmg(enemy) then
	  			CastR(enemy)
	  		end
  		end
 	end
end



local function OnTick()
	if player:spellSlot(3).state == 0 and MyMenu.combo.r:get() then
		if common.CheckBuff(player, "kogmawlivingartillerycost") then
	    	Stacks = common.CountBuff(player, "kogmawlivingartillerycost")
	  	end
	  	if not common.CheckBuff(player, "kogmawlivingartillerycost") then
	    	Stacks = 0
	  	end
	end
    if MyMenu.combo.cced:get() then
        AutoR() 
    end
	if MyMenu.Key.Combo:get() then
		Combo()
	end
	if MyMenu.Key.Harass:get() then
		Harass()
	end
	if MyMenu.auto.uks:get() then
		KillSteal()
	end
	if MyMenu.Key.tap:get() and player:spellSlot(3).state == 0 then
		for i = 0, objManager.enemies_n - 1 do
			local enemy = objManager.enemies[i]
			local d = player.path.serverPos:dist(enemy.path.serverPos)
			if enemy and common.IsValidTarget(enemy) and not HasSionBuff(enemy) and d < RRange[player:spellSlot(3).level] then
				CastR(enemy)
			end
		end
	end
end

local function OnDraw()
	if MyMenu.draws.q:get() and player:spellSlot(0).state == 0 and player.isOnScreen then
		graphics.draw_circle(player.pos, 1175, 2, MyMenu.draws.colorq:get(), 50)
	end
end

orb.combat.register_f_pre_tick(OnTick)
cb.add(cb.draw, OnDraw)

return KogMawPlugin

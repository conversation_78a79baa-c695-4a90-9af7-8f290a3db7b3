Azir 沙兵绘制功能说明
====================

新增的沙兵可视化功能，严格按照开发者文档API实现。

功能特性：
=========

1. 沙兵攻击范围圈
-----------------
- 绘制金黄色的圆圈显示沙兵攻击范围（350单位）
- 半透明效果，不会遮挡游戏视野
- 使用 graphics.draw_circle() API 实现

2. 沙兵消失倒计时
-----------------
- 显示每个沙兵的剩余存在时间
- 沙兵持续时间：8秒
- 倒计时精确到0.1秒

3. 颜色变化系统
--------------
- 绿色：剩余时间 > 3秒
- 黄色：剩余时间 1-3秒
- 红色：剩余时间 < 1秒

4. 沙兵中心标记
--------------
- 小圆点标记沙兵的精确位置
- 金黄色，便于识别

技术实现：
=========

API使用：
--------
- graphics.draw_circle() - 绘制攻击范围圈
- graphics.draw_text_2D() - 绘制倒计时文字
- graphics.world_to_screen() - 世界坐标转屏幕坐标
- graphics.argb() - 颜色设置

时间跟踪：
---------
- 使用 soldier_spawn_times 表记录每个沙兵的创建时间
- 通过 os.clock() 计算精确的剩余时间
- 自动清理已消失沙兵的时间记录

菜单设置：
=========

位置：
-----
沙兵召唤 (W) -> 杂项 -> "绘制沙兵圈和倒计时"

选项：
-----
- 默认：开启
- 可随时通过菜单开关
- 提示信息：显示沙兵攻击范围圈和消失倒计时

显示效果：
=========

视觉元素：
---------
1. 金黄色攻击范围圈（半透明）
2. 彩色倒计时数字（沙兵上方）
3. 金黄色中心标记点

屏幕适配：
---------
- 只在沙兵可见且在屏幕范围内时显示
- 自动适配不同分辨率
- 不会在屏幕外绘制，节省性能

使用建议：
=========

战术价值：
---------
- 更好地规划沙兵位置
- 及时刷新即将消失的沙兵
- 精确掌握攻击范围

性能优化：
---------
- 只绘制可见的沙兵
- 屏幕范围检查避免无效绘制
- 高效的时间计算算法

注意事项：
=========

1. 沙兵持续时间固定为8秒
2. 倒计时从沙兵创建开始计算
3. 沙兵死亡时自动清理相关数据
4. 所有绘制都遵循开发者文档API规范

故障排除：
=========

如果倒计时不准确：
- 检查系统时间是否正常
- 重新召唤沙兵重置计时

如果圈不显示：
- 确认菜单选项已开启
- 检查沙兵是否在屏幕范围内

代码结构：
=========

主要函数：
- ove_0_30() - 沙兵创建时记录时间
- ove_0_31() - 沙兵清理时删除记录
- ove_0_32() - 绘制所有视觉效果

数据结构：
- ove_0_10 - 沙兵对象列表
- soldier_spawn_times - 沙兵创建时间映射表

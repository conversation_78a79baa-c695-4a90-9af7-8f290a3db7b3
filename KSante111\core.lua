local ove_0_10 = module.internal("orb")
local ove_0_11 = module.internal("pred")
local ove_0_12 = module.load("<PERSON>", "KSante/menu")
local ove_0_13 = module.load("<PERSON>", "<PERSON>Sante/lock")
local ove_0_14 = module.load("<PERSON>", "<PERSON>Sante/helper")
local ove_0_15 = module.load("<PERSON>", "common/common")
local ove_0_16 = module.load("<PERSON>", "KSante/spells/q")
local ove_0_17 = module.load("<PERSON>", "KSante/spells/q3")
local ove_0_18 = module.load("<PERSON>", "KSante/spells/w")
local ove_0_19 = module.load("<PERSON>", "KSante/spells/e")
local ove_0_20 = module.load("<PERSON>", "KSante/spells/r")

return {
	get_action_tick = function()
		if ove_0_12.misc.move.use_magnet:get() then
			ove_0_13.Magnet()
		end

		if ove_0_18.is_charging_with_w() then
			local slot_5_0 = math.max(0, math.min(1, (game.time - ove_0_18.LastCastTime - ove_0_18.CastRangeGrowthStartTime) / ove_0_18.CastRangeGrowthDuration))

			ove_0_18.range = (ove_0_18.CastRangeGrowthMax - ove_0_18.CastRangeGrowthMin) * slot_5_0 + ove_0_18.CastRangeGrowthMin
		else
			ove_0_18.range = ove_0_18.CastRangeGrowthMax
		end

		if ove_0_14.GetSanteTranformation() then
			ove_0_19.range = 400
			ove_0_18.pred_input.speed = 1800
		else
			ove_0_19.range = 250
			ove_0_18.pred_input.speed = 1500
		end

		ove_0_16.range = ove_0_14.HaveQ3() and 825 or 465

		if ove_0_12.w_block.auto_w:get() and ove_0_18.is_ready() then
			ove_0_18.evade_block_spells()
		end

		if ove_0_12.combo.r.semi_r:get() and ove_0_20.is_ready() and not ove_0_14.GetSanteTranformation() then
			local slot_5_1 = ove_0_15.GetTarget(ove_0_15.GetAARange())

			if slot_5_1 and ove_0_15.IsValidTarget(slot_5_1) then
				player:castSpell("obj", _R, slot_5_1)
			end
		end

		if ove_0_12.flee.flee_key:get() then
			player:move(game.mousePos)

			if ove_0_12.flee.use_e:get() then
				ove_0_19.invoke_action_flee()
			end

			if ove_0_12.flee.use_w:get() then
				ove_0_18.flee_w()
			end
		end

		if ove_0_12.misc.interrupt.use_w:get() and ove_0_18.is_ready() and ove_0_18.invoke_interrupt_spells() then
			ove_0_18.interrupt_data.owner = false

			return
		end

		if ove_0_12.misc.kill.use_q:get() and (ove_0_16.is_ready() or ove_0_17.is_ready()) and ove_0_16.invoke_q_killsteal() then
			return
		end

		if ove_0_12.misc.misc_key:get() then
			player:move(game.mousePos)

			if ove_0_17.FlashSlot ~= nil and ove_0_15.IsReady(ove_0_17.FlashSlot) and ove_0_14.HaveQ3() and ove_0_17.is_ready() then
				ove_0_17.flash_q()
			end
		end

		if ove_0_10.menu.combat.key:get() then
			if ove_0_12.combo.q.use_q:get() and ove_0_15.GetPercentMana(player) >= ove_0_12.combo.q.min_mana:get() and ove_0_16.get_action_state() then
				ove_0_16.invoke_action()
			elseif ove_0_12.combo.q.use_q3:get() and ove_0_15.GetPercentMana(player) >= ove_0_12.combo.q.min_mana:get() and ove_0_17.get_action_state() then
				ove_0_17.invoke_action()
			end

			if ove_0_12.combo.e.mode_e:get() ~= 3 and ove_0_19.is_ready() and ove_0_15.GetPercentMana(player) >= ove_0_12.combo.e.min_mana:get() then
				local slot_5_2 = ove_0_15.GetTarget(600)

				if slot_5_2 and ove_0_15.IsValidTarget(slot_5_2) then
					if ove_0_12.combo.e.mode_e:get() == 1 then
						if ove_0_15.GetDistance(player.path.serverPos, slot_5_2.pos) > ove_0_15.GetAARange(slot_5_2) and not ove_0_15.isUnderEnemyTurret(ove_0_14.DashEndPos(slot_5_2), 950) then
							player:castSpell("pos", _E, slot_5_2.path.serverPos)
						end
					elseif ove_0_12.combo.e.mode_e:get() == 2 and (not ove_0_15.isUnderEnemyTurret(ove_0_14.DashEndPos(slot_5_2), 950) or ove_0_15.isUnderEnemyTurret(player.path.serverPos, 750)) then
						player:castSpell("pos", _E, slot_5_2.path.serverPos)
					end
				end
			end

			if ove_0_12.combo.w.use_w_intra:get() and ove_0_18.is_ready() and ove_0_14.GetSanteTranformation() then
				local slot_5_3 = ove_0_15.GetTarget(600)

				if slot_5_3 and ove_0_15.IsValidTarget(slot_5_3) then
					local slot_5_4 = ove_0_11.linear.get_prediction(ove_0_18.pred_input, slot_5_3)

					if slot_5_4 then
						if not ove_0_18.is_charging_with_w() then
							player:castSpell("pos", _W, slot_5_4.endPos:to3D())
						elseif slot_5_4.startPos:dist(slot_5_4.endPos) < ove_0_18.range - 150 then
							player:castSpell("release", _W, slot_5_4.endPos:to3D())
						end
					end
				end
			end

			if ove_0_12.combo.w.use_w:get() and ove_0_18.is_charging_with_w() and ove_0_18.get_action_state() then
				if ove_0_12.combo.w.use_w_intra:get() and not ove_0_14.GetSanteTranformation() then
					ove_0_18.invoke_action()
				else
					ove_0_18.invoke_action()
				end
			end

			if ove_0_12.combo.r.use_r:get() and not ove_0_14.GetSanteTranformation() and ove_0_20.is_ready() and ove_0_15.GetPercentHealth(player) >= ove_0_12.combo.r.dont_player:get() then
				ove_0_20.cast_r()
			end
		elseif ove_0_10.menu.hybrid.key:get() then
			if ove_0_12.harass.q.use_q:get() and ove_0_15.GetPercentMana(player) >= ove_0_12.harass.q.min_mana:get() and ove_0_16.get_action_state() then
				ove_0_16.invoke_action()
			elseif ove_0_12.harass.q.use_q3:get() and ove_0_15.GetPercentMana(player) >= ove_0_12.harass.q.min_mana:get() and ove_0_17.get_action_state() then
				ove_0_17.invoke_action()
			end

			if ove_0_12.harass.e.mode_e:get() ~= 3 and ove_0_19.is_ready() and ove_0_15.GetPercentMana(player) >= ove_0_12.harass.e.min_mana:get() then
				local slot_5_5 = ove_0_15.GetTarget(600)

				if slot_5_5 and ove_0_15.IsValidTarget(slot_5_5) then
					if ove_0_12.harass.e.mode_e:get() == 1 then
						if ove_0_15.GetDistance(player.path.serverPos, slot_5_5.pos) > ove_0_15.GetAARange(slot_5_5) and not ove_0_15.isUnderEnemyTurret(ove_0_14.DashEndPos(slot_5_5), 950) then
							player:castSpell("pos", _E, slot_5_5.path.serverPos)
						end
					elseif ove_0_12.harass.e.mode_e:get() == 2 and (not ove_0_15.isUnderEnemyTurret(ove_0_14.DashEndPos(slot_5_5), 950) or ove_0_15.isUnderEnemyTurret(player.path.serverPos, 750)) then
						player:castSpell("pos", _E, slot_5_5.path.serverPos)
					end
				end
			end
		elseif ove_0_10.menu.lane_clear.key:get() and ove_0_12.farming.toggleFarm:get() and ove_0_14.CanCastFarming() then
			if not ove_0_10.core.is_winding_up_attack() then
				if not ove_0_14.HaveQ3() and ove_0_12.farming.lane.q.use_q:get() and ove_0_16.is_ready() then
					ove_0_16.invoke_action_q_lane_clear()
				elseif ove_0_14.HaveQ3() and ove_0_12.farming.lane.q.use_q3:get() and ove_0_17.is_ready() then
					ove_0_17.invoke_action_q3_lane_clear()
				end

				if ove_0_12.farming.jungle.q.use_q:get() and (ove_0_16.is_ready() or ove_0_17.is_ready()) then
					ove_0_16.invoke_action_jungle()
				end

				if ove_0_12.farming.jungle.w.use_w:get() and ove_0_18.is_ready() and not ove_0_16.is_ready() and not ove_0_14.HaveQ3() then
					ove_0_18.invoke_action_jungle()
				end

				if ove_0_12.farming.jungle.e.use_e:get() and ove_0_19.is_ready() and not ove_0_14.HaveQ3() and (not ove_0_18.is_charging_with_w() or not ove_0_12.farming.jungle.w.use_w:get() or not ove_0_18.is_ready()) then
					ove_0_19.invoke_action_jungle()
				end
			end
		elseif ove_0_10.menu.last_hit.key:get() and ove_0_12.farming.toggleFarm:get() and ove_0_14.CanCastFarming() then
			local slot_5_6, slot_5_7 = ove_0_10.farm.skill_clear_linear(ove_0_16.pred_input)

			if slot_5_6 and slot_5_7 and not ove_0_10.core.is_winding_up_attack() then
				player:castSpell("pos", _Q, slot_5_6.endPos:to3D())
			end
		end
	end
}



local ove_0_10 = module.internal("TS")
local ove_0_11 = module.seek("evade")
local ove_0_12 = assert(select)
local ove_0_13 = {
	anivia = {
		{
			spellname = "glacialstorm",
			menuslot = "R",
			slot = 3,
			channelduration = 6
		}
	},
	caitlyn = {
		{
			spellname = "caitlynaceinthehole",
			menuslot = "R",
			slot = 3,
			channelduration = 1
		}
	},
	fiddlesticks = {
		{
			spellname = "drainchannel",
			menuslot = "W",
			slot = 1,
			channelduration = 2
		},
		{
			spellname = "crowstorm",
			menuslot = "R",
			slot = 3,
			channelduration = 1.5
		}
	},
	janna = {
		{
			spellname = "reapthewhirlwind",
			menuslot = "R",
			slot = 3,
			channelduration = 3
		}
	},
	karthus = {
		{
			spellname = "karthusfallenone",
			menuslot = "R",
			slot = 3,
			channelduration = 3
		}
	},
	katarina = {
		{
			spellname = "katarinar",
			menuslot = "R",
			slot = 3,
			channelduration = 2.5
		}
	},
	lucian = {
		{
			spellname = "lucianr",
			menuslot = "R",
			slot = 3,
			channelduration = 3
		}
	},
	malzahar = {
		{
			spellname = "malzaharr",
			menuslot = "R",
			slot = 3,
			channelduration = 2.5
		}
	},
	masteryi = {
		{
			spellname = "meditate",
			menuslot = "W",
			slot = 1,
			channelduration = 4
		}
	},
	missfortune = {
		{
			spellname = "missfortunebullettime",
			menuslot = "R",
			slot = 3,
			channelduration = 3
		}
	},
	nunu = {
		{
			spellname = "nunur",
			menuslot = "R",
			slot = 3,
			channelduration = 3
		}
	},
	pantheon = {
		{
			spellname = "pantheonrjump",
			menuslot = "R",
			slot = 3,
			channelduration = 2
		},
		{
			spellname = "pantheonq",
			menuslot = "Q",
			slot = 0,
			channelduration = 4
		}
	},
	poppy = {
		{
			spellname = "poppyr",
			menuslot = "R",
			slot = 3,
			channelduration = 4
		}
	},
	quinn = {
		{
			spellname = "quinr",
			menuslot = "R",
			slot = 3,
			channelduration = 2
		}
	},
	shen = {
		{
			spellname = "shenr",
			menuslot = "R",
			slot = 3,
			channelduration = 3
		}
	},
	galio = {
		{
			spellname = "galior",
			menuslot = "R",
			slot = 3,
			channelduration = 3
		}
	},
	sion = {
		{
			spellname = "sionq",
			menuslot = "Q",
			slot = 0,
			channelduration = 2
		}
	},
	tahmkench = {
		{
			spellname = "tahmkenchnewr",
			menuslot = "R",
			slot = 3,
			channelduration = 3
		}
	},
	twistedfate = {
		{
			spellname = "gate",
			menuslot = "R",
			slot = 3,
			channelduration = 1.5
		}
	},
	varus = {
		{
			spellname = "varusq",
			menuslot = "Q",
			slot = 0,
			channelduration = 4
		}
	},
	velkoz = {
		{
			spellname = "velkozr",
			menuslot = "R",
			slot = 3,
			channelduration = 2.5
		}
	},
	warwick = {
		{
			spellname = "warwickrchannel",
			menuslot = "R",
			slot = 3,
			channelduration = 1.5
		}
	},
	xerath = {
		{
			spellname = "xeratharcanopulsechargeup",
			menuslot = "Q",
			slot = 0,
			channelduration = 3
		},
		{
			spellname = "xerathlocusofpower2",
			menuslot = "R",
			slot = 3,
			channelduration = 10
		}
	},
	zac = {
		{
			spellname = "zace",
			menuslot = "E",
			slot = 2,
			channelduration = 4
		}
	},
	jhin = {
		{
			spellname = "jhinr",
			menuslot = "R",
			slot = 3,
			channelduration = 10
		}
	},
	pyke = {
		{
			spellname = "pykeq",
			menuslot = "Q",
			slot = 0,
			channelduration = 3
		}
	},
	vi = {
		{
			spellname = "viq",
			menuslot = "Q",
			slot = 0,
			channelduration = 4
		}
	},
	samira = {
		{
			spellname = "samirar",
			menuslot = "R",
			slot = 3,
			channelduration = 2
		}
	},
	yuumiw = {
		{
			spellname = "yuumiw",
			menuslot = "W",
			slot = 1,
			channelduration = 0.2
		}
	}
}
local ove_0_14 = {}
local ove_0_15

local function ove_0_16(arg_5_0, arg_5_1, arg_5_2)
	if not ove_0_15 then
		function ove_0_15()
			for iter_6_0, iter_6_1 in pairs(ove_0_14) do
				if iter_6_0 <= os.clock() then
					for iter_6_2 = 1, #iter_6_1 do
						local slot_6_0 = iter_6_1[iter_6_2]

						if slot_6_0 and slot_6_0.func then
							slot_6_0.func(unpack(slot_6_0.args or {}))
						end
					end

					ove_0_14[iter_6_0] = nil
				end
			end
		end

		cb.add(cb.tick, ove_0_15)
	end

	local slot_5_0 = os.clock() + (arg_5_1 or 0)

	if ove_0_14[slot_5_0] then
		ove_0_14[slot_5_0][#ove_0_14[slot_5_0] + 1] = {
			func = arg_5_0,
			args = arg_5_2
		}
	else
		ove_0_14[slot_5_0] = {
			{
				func = arg_5_0,
				args = arg_5_2
			}
		}
	end
end

local function ove_0_17(arg_7_0, arg_7_1)
	if not arg_7_0 then
		console.set_color(42)
		console.set_color(15)
	end
end

local function ove_0_18(arg_8_0)
	chat.add(arg_8_0, {
		color = "#FFFFFF",
		bold = false,
		italic = false
	})
	chat.print()
end

local function ove_0_19(arg_9_0)
	local slot_9_0 = io.open(hanbot.path .. "\\Intnner_loader.log", "wb")

	if slot_9_0 ~= nil then
		slot_9_0:write(arg_9_0)
		slot_9_0:close()
	end
end

local ove_0_20 = {}

local function ove_0_21(arg_10_0, arg_10_1, arg_10_2)
	ove_0_20[#ove_0_20 + 1] = {
		funct = arg_10_1,
		time = game.time + arg_10_0,
		args = arg_10_2
	}
end

local function ove_0_22()
	for iter_11_0 = #ove_0_20, 1, -1 do
		if ove_0_20[iter_11_0].time < game.time then
			ove_0_20[iter_11_0].funct(unpack(ove_0_20[iter_11_0].args or {}))

			ove_0_20[iter_11_0] = ove_0_20[#ove_0_20]
			ove_0_20[#ove_0_20] = nil
		end
	end
end

local function ove_0_23(arg_12_0)
	local slot_12_0 = type(arg_12_0)

	if slot_12_0 == "userdata" then
		local slot_12_1 = getmetatable(arg_12_0)

		if not slot_12_1 or not slot_12_1.__index then
			arg_12_0, slot_12_0 = "userdata", "string"
		end
	end

	if slot_12_0 == "userdata" or slot_12_0 == "table" then
		local slot_12_2 = arg_12_0.__type or arg_12_0.type or arg_12_0.Type

		slot_12_0 = type(slot_12_2) == "function" and slot_12_2(arg_12_0) or type(slot_12_2) == "string" and slot_12_2 or slot_12_0
	end

	return slot_12_0
end

local function ove_0_24(arg_13_0)
	if arg_13_0 == nil then
		arg_13_0 = game.cursorPos.z
	else
		arg_13_0 = arg_13_0.z
	end

	return arg_13_0
end

local ove_0_25 = ove_0_17(next)

local function ove_0_26(arg_14_0, arg_14_1, arg_14_2)
	local slot_14_0
	local slot_14_1

	local function slot_14_2(arg_15_0)
		return not ove_0_25(arg_15_0)
	end

	local function slot_14_3(arg_16_0)
		local slot_16_0 = tostring(arg_16_0)

		if type(arg_16_0) == "function" then
			return slot_16_0
		elseif type(arg_16_0) == "number" or type(arg_16_0) == "boolean" then
			return slot_16_0
		else
			return string.format("%q", slot_16_0)
		end
	end

	local function slot_14_4(arg_17_0, arg_17_1, arg_17_2, arg_17_3, arg_17_4)
		arg_17_2 = arg_17_2 or ""
		arg_17_3 = arg_17_3 or {}
		arg_17_4 = arg_17_4 or arg_17_1
		slot_14_0 = slot_14_0 .. arg_17_2 .. arg_17_4

		if type(arg_17_0) ~= "table" then
			slot_14_0 = slot_14_0 .. " = " .. slot_14_3(arg_17_0) .. ";\n"
		elseif arg_17_3[arg_17_0] then
			slot_14_0 = slot_14_0 .. " = {}; -- " .. arg_17_3[arg_17_0] .. " (self reference)\n"
			slot_14_1 = slot_14_1 .. arg_17_1 .. " = " .. arg_17_3[arg_17_0] .. ";\n"
		else
			arg_17_3[arg_17_0] = arg_17_1

			if slot_14_2(arg_17_0) then
				slot_14_0 = slot_14_0 .. " = {};\n"
			else
				slot_14_0 = slot_14_0 .. " = {\n"

				for iter_17_0, iter_17_1 in pairs(arg_17_0) do
					iter_17_0 = slot_14_3(iter_17_0)

					local slot_17_0 = string.format("%s[%s]", arg_17_1, iter_17_0)

					arg_17_4 = string.format("[%s]", iter_17_0)

					slot_14_4(iter_17_1, slot_17_0, arg_17_2 .. "   ", arg_17_3, arg_17_4)
				end

				slot_14_0 = slot_14_0 .. arg_17_2 .. "};\n"
			end
		end
	end

	arg_14_1 = arg_14_1 or "table"

	if type(arg_14_0) ~= "table" then
		return arg_14_1 .. " = " .. slot_14_3(arg_14_0)
	end

	slot_14_0, slot_14_1 = "", ""

	slot_14_4(arg_14_0, arg_14_1, arg_14_2)

	return slot_14_0 .. slot_14_1
end

local function ove_0_27(...)
	local slot_18_0 = {}
	local slot_18_1 = ove_0_12("#", ...)

	for iter_18_0 = 1, slot_18_1 do
		local slot_18_2 = ove_0_12(iter_18_0, ...)
		local slot_18_3 = ove_0_23(slot_18_2)

		if slot_18_3 == "string" then
			slot_18_0[iter_18_0] = slot_18_2
		elseif slot_18_3 == "vector" then
			slot_18_0[iter_18_0] = tostring(slot_18_2)
		elseif slot_18_3 == "number" then
			slot_18_0[iter_18_0] = tostring(slot_18_2)
		elseif slot_18_3 == "table" then
			slot_18_0[iter_18_0] = ove_0_26(slot_18_2)
		elseif slot_18_3 == "boolean" then
			slot_18_0[iter_18_0] = slot_18_2 and "true" or "false"
		else
			slot_18_0[iter_18_0] = "<" .. slot_18_3 .. ">"
		end
	end

	if slot_18_1 > 0 then
		ove_0_18("[Hanbot]" .. table.concat(slot_18_0))
	end
end

local ove_0_28

local function ove_0_29(arg_19_0, arg_19_1, arg_19_2)
	if not ove_0_28 then
		ove_0_28 = {}

		cb.add(cb.tick, function()
			for iter_20_0, iter_20_1 in pairs(ove_0_28) do
				if iter_20_0 <= game.time then
					for iter_20_2 = 1, #iter_20_1 do
						iter_20_1[iter_20_2][1](iter_20_1[iter_20_2][2] and unpack(iter_20_1[iter_20_2][2]) or nil)
					end

					ove_0_28[iter_20_0] = nil
				end
			end
		end)
	end

	local slot_19_0 = game.time + (arg_19_1 or 0)

	ove_0_28[slot_19_0] = ove_0_28[slot_19_0] or {}
	ove_0_28[slot_19_0][#ove_0_28[slot_19_0] + 1] = {
		arg_19_0,
		arg_19_2
	}
end

local function ove_0_30(arg_21_0, arg_21_1)
	local slot_21_0 = {}

	if not arg_21_1 and type(arg_21_0) == "function" then
		arg_21_1 = arg_21_0
		arg_21_0 = nil
	elseif type(arg_21_0) == "table" then
		for iter_21_0, iter_21_1 in pairs(arg_21_0) do
			slot_21_0[iter_21_0] = iter_21_1
		end

		slot_21_0._base = arg_21_0
	end

	slot_21_0.__index = slot_21_0

	local slot_21_1 = {
		__call = function(arg_22_0, ...)
			local slot_22_0 = {}

			setmetatable(slot_22_0, slot_21_0)

			if arg_21_1 then
				arg_21_1(slot_22_0, ...)
			elseif arg_21_0 and arg_21_0.init then
				arg_21_0.init(slot_22_0, ...)
			end

			return slot_22_0
		end
	}

	slot_21_0.init = arg_21_1

	function slot_21_0.is_a(arg_23_0, arg_23_1)
		local slot_23_0 = getmetatable(arg_23_0)

		while slot_23_0 do
			if slot_23_0 == arg_23_1 then
				return true
			end

			slot_23_0 = slot_23_0._base
		end

		return false
	end

	setmetatable(slot_21_0, slot_21_1)

	return slot_21_0
end

local ove_0_31

local function ove_0_32(arg_24_0, arg_24_1, arg_24_2, arg_24_3)
	if not ove_0_31 then
		ove_0_31 = {}

		cb.add(cb.tick, function()
			for iter_25_0 = 1, #ove_0_31 do
				local slot_25_0 = ove_0_31[iter_25_0]

				if game.time >= slot_25_0[1] + slot_25_0[3] then
					slot_25_0[4](slot_25_0[5] and unpack(slot_25_0[5]) or nil)

					slot_25_0[2] = slot_25_0[2] - 1
					slot_25_0[1] = game.time

					if slot_25_0[2] <= 0 then
						ove_0_31[iter_25_0] = iter_25_0 < #ove_0_31 and ove_0_31[#ove_0_31] or nil
						iter_25_0 = iter_25_0 - 1
					end
				end
			end
		end)
	end

	ove_0_31[#ove_0_31 + 1] = {
		game.time,
		arg_24_2,
		arg_24_1 or 0,
		arg_24_0,
		arg_24_3
	}
end

local function ove_0_33(arg_26_0, arg_26_1)
	arg_26_1 = arg_26_1 or player

	if arg_26_0.armor == 0 then
		return 1
	end

	local slot_26_0 = (arg_26_0.bonusArmor * arg_26_1.percentBonusArmorPenetration + arg_26_0.armor - arg_26_0.bonusArmor) * arg_26_1.percentArmorPenetration

	if arg_26_1.type ~= TYPE_HERO then
		slot_26_0 = (arg_26_0.bonusArmor * 1 + arg_26_0.armor - arg_26_0.bonusArmor) * 1
	end

	local slot_26_1 = arg_26_1.type == TYPE_HERO and arg_26_1.physicalLethality * (0.6 + 0.4 * arg_26_1.levelRef / 18) or 0

	return slot_26_0 >= 0 and 100 / (100 + math.max(slot_26_0 - slot_26_1, 0)) or 1
end

local function ove_0_34(arg_27_0, arg_27_1)
	arg_27_1 = arg_27_1 or player

	local slot_27_0 = arg_27_0.spellBlock * arg_27_1.percentMagicPenetration - arg_27_1.flatMagicPenetration

	return slot_27_0 >= 0 and 100 / (100 + slot_27_0) or 2 - 100 / (100 - slot_27_0)
end

local function ove_0_35(arg_28_0, arg_28_1)
	local slot_28_0 = arg_28_0 / 5
	local slot_28_1 = player.pos

	for iter_28_0 = 1, 5 do
		local slot_28_2 = slot_28_1 + (arg_28_1 - slot_28_1):norm() * iter_28_0 * slot_28_0

		if navmesh.isWall(slot_28_2) then
			return false
		end
	end

	return true
end

local function ove_0_36(arg_29_0, arg_29_1)
	local slot_29_0 = 0
	local slot_29_1
	local slot_29_2

	for iter_29_0 = 1, #arg_29_0 do
		local slot_29_3 = arg_29_0[iter_29_0]
		local slot_29_4 = slot_29_3 + arg_29_1
		local slot_29_5 = slot_29_4 > 360

		if slot_29_5 then
			slot_29_4 = slot_29_4 - 360
		end

		local function slot_29_6(arg_30_0, arg_30_1, arg_30_2, arg_30_3, arg_30_4)
			if arg_30_1 == arg_30_2 and arg_30_0 ~= 0 then
				return
			end

			if not arg_30_3 then
				if arg_30_1 <= arg_30_4 and arg_30_2 <= arg_30_1 then
					return true
				end
			elseif arg_30_2 < arg_30_1 and arg_30_1 <= 360 then
				return true
			elseif arg_30_1 <= arg_30_4 and arg_30_1 < arg_30_2 then
				return true
			end
		end

		local slot_29_7 = slot_29_3
		local slot_29_8 = iter_29_0
		local slot_29_9 = 0
		local slot_29_10 = slot_29_7

		while slot_29_6(slot_29_9, slot_29_7, slot_29_3, slot_29_5, slot_29_4) do
			slot_29_10 = arg_29_0[slot_29_8]
			slot_29_9 = slot_29_9 + 1
			slot_29_8 = slot_29_8 + 1

			if slot_29_8 > #arg_29_0 then
				slot_29_8 = 1
			end

			slot_29_7 = arg_29_0[slot_29_8]
		end

		if slot_29_0 < slot_29_9 then
			slot_29_0 = slot_29_9
			slot_29_1 = slot_29_3
			slot_29_2 = slot_29_10
		end
	end

	if slot_29_1 and slot_29_2 then
		if slot_29_1 + arg_29_1 > 360 then
			slot_29_2 = slot_29_2 + 360
		end

		local slot_29_11 = (slot_29_1 + slot_29_2) / 2

		if slot_29_11 > 360 then
			slot_29_11 = slot_29_11 - 360
		end

		return math.rad(slot_29_11)
	end
end

local ove_0_37 = {
	FerociousHowl = function(arg_31_0)
		return 0.55 - 0.1 * arg_31_0:spellSlot(3).level
	end,
	AnnieE = function(arg_32_0)
		return 0.9 - 0.06 * arg_32_0:spellSlot(1).level
	end,
	Meditate = function(arg_33_0)
		return 0.5 - 0.05 * arg_33_0:spellSlot(1).level
	end,
	braumeshieldbuff = function(arg_34_0)
		return 0.725 - 0.025 * arg_34_0:spellSlot(2).level
	end,
	GalioW = function(arg_35_0, arg_35_1)
		if arg_35_1 then
			return 0.925 - 0.025 * arg_35_0:spellSlot(1).level - arg_35_0.bonusSpellBlock * 0.04
		else
			return 0.85 - 0.05 * arg_35_0:spellSlot(1).level - arg_35_0.bonusSpellBlock * 0.08
		end
	end,
	WarwickE = function(arg_36_0)
		return 0.7 - 0.05 * arg_36_0:spellSlot(2).level
	end,
	ireliawdefense = function(arg_37_0)
		if bPhysical then
			return 0.5 - 0.07 * arg_37_0.flatMagicDamageMod * arg_37_0.percentMagicDamageMod
		else
			return 1
		end
	end,
	malzaharpassiveshield = function()
		return 0.1
	end,
	GarenW = function()
		return 0.4
	end,
	gragaswself = function(arg_40_0)
		return 0.92 - 0.02 * arg_40_0:spellSlot(2).level - 0.04 * arg_40_0.flatMagicDamageMod * arg_40_0.percentMagicDamageMod
	end
}

local function ove_0_38(arg_41_0, arg_41_1)
	ove_0_17(arg_41_0, "getBuffValid: no target")
	ove_0_17(arg_41_1, "getBuffValid: no buffname/type")

	local slot_41_0 = arg_41_0.buff[type(arg_41_1) == "string" and arg_41_1:lower() or arg_41_1]

	return slot_41_0 and slot_41_0.endTime > game.time and math.max(slot_41_0.stacks, slot_41_0.stacks2) > 0
end

local function ove_0_39(arg_42_0, arg_42_1, arg_42_2)
	ove_0_17(arg_42_0, "getBuffStacks: no target")
	ove_0_17(arg_42_1, "getBuffStacks: no buffname/type")

	local slot_42_0 = arg_42_0.buff[type(arg_42_1) == "string" and arg_42_1:lower() or arg_42_1]

	return slot_42_0 and (arg_42_2 == false or slot_42_0.endTime > game.time) and math.max(slot_42_0.stacks, slot_42_0.stacks2) or 0
end

local function ove_0_40(arg_43_0, arg_43_1)
	ove_0_17(arg_43_0, "getBuffStartTime: no target")
	ove_0_17(arg_43_1, "getBuffStartTime: no buffname/type")

	local slot_43_0 = arg_43_0.buff[type(arg_43_1) == "string" and arg_43_1:lower() or arg_43_1]

	return slot_43_0 and (validate == false or slot_43_0.endTime > game.time) and slot_43_0.startTime or 0
end

local function ove_0_41(arg_44_0, arg_44_1)
	ove_0_17(arg_44_0, "getBuffEndTime: no target")
	ove_0_17(arg_44_1, "getBuffEndTime: no buffname/type")

	local slot_44_0 = arg_44_0.buff[type(arg_44_1) == "string" and arg_44_1:lower() or arg_44_1]

	return slot_44_0 and (validate == false or slot_44_0.endTime > game.time) and slot_44_0.endTime or 0
end

local function ove_0_42(arg_45_0, arg_45_1, arg_45_2)
	local slot_45_0 = 1

	if arg_45_0.type == TYPE_HERO then
		for iter_45_0, iter_45_1 in pairs(ove_0_37) do
			local slot_45_1 = arg_45_0.buff[iter_45_0]

			if slot_45_1 and slot_45_1.endTime > game.time then
				slot_45_0 = slot_45_0 * iter_45_1(arg_45_0, arg_45_2)
			end
		end
	end

	if not arg_45_2 and arg_45_0.charName == "Kassadin" then
		slot_45_0 = slot_45_0 * 0.85
	end

	for iter_45_2 = 0, arg_45_1.buffManager.count - 1 do
		local slot_45_2 = arg_45_1.buffManager:get(iter_45_2)

		if slot_45_2 and slot_45_2.valid and slot_45_2.endTime > game.time then
			if slot_45_2.name == "summonerexhaustdebuff" then
				slot_45_0 = slot_45_0 * 0.6
			elseif slot_45_2.name == "itemsmitechallenge" then
				slot_45_0 = slot_45_0 * 0.6
			elseif slot_45_2.name == "itemphantomdancerdebuff" and slot_45_2.source.ptr == arg_45_0.ptr then
				slot_45_0 = slot_45_0 * 88
			elseif slot_45_2.name == "abyssalscepteraura" and not arg_45_2 then
				slot_45_0 = slot_45_0 * 1.15
			end
		end
	end

	return slot_45_0
end

local function ove_0_43(arg_46_0)
	arg_46_0 = arg_46_0 or player

	return math.floor(arg_46_0.baseAttackDamage + arg_46_0.flatPhysicalDamageMod) * arg_46_0.percentPhysicalDamageMod
end

local function ove_0_44(arg_47_0)
	arg_47_0 = arg_47_0 or player

	return (arg_47_0.baseAttackDamage + arg_47_0.flatPhysicalDamageMod) * arg_47_0.percentPhysicalDamageMod - arg_47_0.baseAttackDamage
end

local function ove_0_45(arg_48_0)
	arg_48_0 = arg_48_0 or player

	return arg_48_0.flatMagicDamageMod * arg_48_0.percentMagicDamageMod
end

local function ove_0_46(arg_49_0, arg_49_1, arg_49_2)
	ove_0_17(arg_49_0, "calculatePhysicalDamage: target is nil")

	if type(arg_49_1) == "number" then
		arg_49_1, arg_49_2 = arg_49_2, arg_49_1
	end

	arg_49_1 = arg_49_1 or player

	return (arg_49_2 or ove_0_43(arg_49_1)) * ove_0_33(arg_49_0, arg_49_1) * ove_0_42(arg_49_0, arg_49_1, true)
end

local function ove_0_47(arg_50_0, arg_50_1, arg_50_2)
	ove_0_17(arg_50_0, "calculateMagicalDamage: target is nil")

	if type(arg_50_1) == "number" then
		arg_50_1, arg_50_2 = arg_50_2, arg_50_1
	end

	arg_50_1 = arg_50_1 or player

	return (arg_50_2 or ove_0_45(arg_50_1)) * ove_0_34(arg_50_0, arg_50_1) * ove_0_42(arg_50_0, arg_50_1)
end

local ove_0_48 = {
	{
		0.75,
		0.76,
		0.78,
		0.79,
		0.81,
		0.83,
		0.84,
		0.86,
		0.88,
		0.9,
		0.92,
		0.95,
		0.97,
		0.99,
		1.01,
		1.04,
		1.07,
		1.1
	},
	{
		0.25,
		0.25,
		0.26,
		0.26,
		0.27,
		0.28,
		0.28,
		0.29,
		0.29,
		0.3,
		0.31,
		0.32,
		0.32,
		0.33,
		0.34,
		0.35,
		0.36,
		0.37
	}
}
local ove_0_49 = {
	Aatrox = function(arg_51_0, arg_51_1, arg_51_2, arg_51_3, arg_51_4)
		return arg_51_2 + (ove_0_39(arg_51_0, "aatroxpassive") > 0 and 35 * arg_51_0:spellSlot(1).level + 25 or 0), arg_51_3, arg_51_4
	end,
	Ashe = function(arg_52_0, arg_52_1, arg_52_2, arg_52_3, arg_52_4)
		return arg_52_2 * (ove_0_39(arg_52_0, "asheqattack") > 0 and 5 * (0.01 * arg_52_0:spellSlot(0).level + 0.22) or ove_0_39(arg_52_1, "ashepassiveslow") > 0 and 1.1 + arg_52_0.crit or 1), arg_52_3, arg_52_4
	end,
	Bard = function(arg_53_0, arg_53_1, arg_53_2, arg_53_3, arg_53_4)
		return arg_53_2, arg_53_3 + (ove_0_39(arg_53_0, "bardpspiritammocount") > 0 and 30 + arg_53_0.levelRef * 15 + 0.3 * arg_53_0.flatPhysicalDamageMod * arg_53_0.percentPhysicalDamageMod or 0), arg_53_4
	end,
	Blitzcrank = function(arg_54_0, arg_54_1, arg_54_2, arg_54_3, arg_54_4)
		return arg_54_2 * (ove_0_39(arg_54_0, "powerfist") + 1), arg_54_3, arg_54_4
	end,
	Caitlyn = function(arg_55_0, arg_55_1, arg_55_2, arg_55_3, arg_55_4, arg_55_5)
		return arg_55_2 + (ove_0_39(arg_55_0, "caitlynheadshot") > 0 and (arg_55_0.baseAttackDamage + arg_55_0.flatPhysicalDamageMod) * arg_55_0.percentPhysicalDamageMod * ((arg_55_0.levelRef > 12 and 1 or arg_55_0.levelRef > 6 and 0.75 or 0.5) + arg_55_0.crit) or 0), arg_55_3, arg_55_4
	end,
	Chogath = function(arg_56_0, arg_56_1, arg_56_2, arg_56_3, arg_56_4)
		return arg_56_2, arg_56_3 + (ove_0_39(arg_56_0, "vorpalspikes") > 0 and 15 * arg_56_0:spellSlot(2).level + 5 + 0.3 * arg_56_0.flatPhysicalDamageMod * arg_56_0.percentPhysicalDamageMod or 0), arg_56_3, arg_56_4
	end,
	Corki = function(arg_57_0, arg_57_1, arg_57_2, arg_57_3, arg_57_4)
		return arg_57_2, arg_57_3, arg_57_4 + (ove_0_39(arg_57_0, "rapidreload") > 0 and 0.1 * arg_57_2 or 0)
	end,
	Darius = function(arg_58_0, arg_58_1, arg_58_2, arg_58_3, arg_58_4)
		return arg_58_2 + (ove_0_39(arg_58_0, "dariusnoxiantacticsonh") > 0 and 0.4 * arg_58_2 or 0), arg_58_3, arg_58_4
	end,
	Diana = function(arg_59_0, arg_59_1, arg_59_2, arg_59_3, arg_59_4)
		return arg_59_2, arg_59_3 + (ove_0_39(arg_59_0, "dianaarcready") > 0 and math.max(5 * arg_59_0.levelRef + 15, 10 * arg_59_0.levelRef - 10, 15 * arg_59_0.levelRef - 60, 20 * arg_59_0.levelRef - 125, 25 * arg_59_0.levelRef - 200) + 0.8 * arg_59_0.flatPhysicalDamageMod * arg_59_0.percentPhysicalDamageMod or 0), arg_59_4
	end,
	Draven = function(arg_60_0, arg_60_1, arg_60_2, arg_60_3, arg_60_4)
		return arg_60_2 + (ove_0_39(arg_60_0, "dravenspinning") > 0 and (0.1 * arg_60_0:spellSlot(0).level + 0.35) * arg_60_2 or 0), arg_60_3, arg_60_4
	end,
	Ekko = function(arg_61_0, arg_61_1, arg_61_2, arg_61_3, arg_61_4)
		return arg_61_2, arg_61_3 + (ove_0_39(arg_61_0, "ekkoeattackbuff") > 0 and 30 * arg_61_0:spellSlot(2).level + 20 + 0.2 * arg_61_0.flatPhysicalDamageMod * arg_61_0.percentPhysicalDamageMod or 0), arg_61_4
	end,
	Fizz = function(arg_62_0, arg_62_1, arg_62_2, arg_62_3, arg_62_4)
		return arg_62_2, arg_62_3 + (ove_0_39(arg_62_0, "fizzseastonepassive") > 0 and 5 * arg_62_0:spellSlot(1).level + 5 + 0.3 * arg_62_0.flatPhysicalDamageMod * arg_62_0.percentPhysicalDamageMod or 0), arg_62_4
	end,
	Garen = function(arg_63_0, arg_63_1, arg_63_2, arg_63_3, arg_63_4)
		return arg_63_2 + (ove_0_39(arg_63_0, "garenq") > 0 and 25 * arg_63_0:spellSlot(0).level + 5 + 0.4 * arg_63_2 or 0), arg_63_3, arg_63_4
	end,
	Gragas = function(arg_64_0, arg_64_1, arg_64_2, arg_64_3, arg_64_4)
		return arg_64_2, arg_64_3 + (ove_0_39(arg_64_0, "gragaswattackbuff") > 0 and 30 * arg_64_0:spellSlot(1).level - 10 + 0.3 * arg_64_0.flatPhysicalDamageMod * arg_64_0.percentPhysicalDamageMod + (0.01 * arg_64_0:spellSlot(1).level + 0.07) * arg_64_1.maxHealth or 0), arg_64_4
	end,
	Graves = function(arg_65_0, arg_65_1, arg_65_2, arg_65_3, arg_65_4)
		local slot_65_0 = arg_65_0.pos2D:dist(arg_65_1.pos2D)

		return arg_65_2 * (ove_0_48[1][arg_65_0.levelRef] + ove_0_48[2][arg_65_0.levelRef] * (slot_65_0 < 100 and 3 or slot_65_0 < 200 and 2 or slot_65_0 < 300 and 1 or 0)), arg_65_3, arg_65_4
	end,
	Irelia = function(arg_66_0, arg_66_1, arg_66_2, arg_66_3, arg_66_4)
		return arg_66_2, 0, arg_66_4 + (ove_0_39(arg_66_0, "ireliahitenstylecharged") > 0 and 25 * arg_66_0:spellSlot(0).level + 5 + 0.4 * arg_66_2 or 0)
	end,
	Jax = function(arg_67_0, arg_67_1, arg_67_2, arg_67_3, arg_67_4)
		return arg_67_2, arg_67_3 + (ove_0_39(arg_67_0, "jaxempowertwo") > 0 and 35 * arg_67_0:spellSlot(1).level + 5 + 0.6 * arg_67_0.flatPhysicalDamageMod * arg_67_0.percentPhysicalDamageMod or 0), arg_67_4
	end,
	Jayce = function(arg_68_0, arg_68_1, arg_68_2, arg_68_3, arg_68_4)
		return arg_68_2, arg_68_3 + (ove_0_39(arg_68_0, "jaycepassivemeleeatack") > 0 and 40 * arg_68_0:spellSlot(3).level - 20 + 0.4 * arg_68_0.flatPhysicalDamageMod * arg_68_0.percentPhysicalDamageMod or 0), arg_68_4
	end,
	Jhin = function(arg_69_0, arg_69_1, arg_69_2, arg_69_3, arg_69_4)
		return ove_0_39(arg_69_0, "jhinpassiveattackbuff") > 0 and (arg_69_1.maxHealth - arg_69_1.health) * (arg_69_0.levelRef < 6 and 0.15 or arg_69_0.levelRef < 11 and 0.2 or 0.25) or 0, arg_69_3, arg_69_4
	end,
	Jinx = function(arg_70_0, arg_70_1, arg_70_2, arg_70_3, arg_70_4)
		return arg_70_2 + (ove_0_39(arg_70_0, "jinxq") > 0 and 0.1 * arg_70_2 or 0), arg_70_3, arg_70_4
	end,
	Kalista = function(arg_71_0, arg_71_1, arg_71_2, arg_71_3, arg_71_4)
		return arg_71_2 * 0.9, arg_71_3, arg_71_4
	end,
	Kassadin = function(arg_72_0, arg_72_1, arg_72_2, arg_72_3, arg_72_4)
		return arg_72_2, arg_72_3 + (ove_0_39(arg_72_0, "netherbladebuff") > 0 and 20 + 0.1 * arg_72_0.flatPhysicalDamageMod * arg_72_0.percentPhysicalDamageMod or 0) + (ove_0_39(arg_72_0, "netherblade") > 0 and 25 * arg_72_0:spellSlot(1).level + 15 + 0.6 * arg_72_0.flatPhysicalDamageMod * arg_72_0.percentPhysicalDamageMod or 0), arg_72_4
	end,
	Kayle = function(arg_73_0, arg_73_1, arg_73_2, arg_73_3, arg_73_4)
		return arg_73_2, arg_73_3 + (ove_0_39(arg_73_0, "kaylerighteousfurybuff") > 0 and 5 * arg_73_0:spellSlot(2).level + 5 + 0.15 * arg_73_0.flatPhysicalDamageMod * arg_73_0.percentPhysicalDamageMod or 0) + (ove_0_39(arg_73_0, "judicatorrighteousfury") > 0 and 5 * arg_73_0:spellSlot(2).level + 5 + 0.15 * arg_73_0.flatPhysicalDamageMod * arg_73_0.percentPhysicalDamageMod or 0), arg_73_4
	end,
	Leona = function(arg_74_0, arg_74_1, arg_74_2, arg_74_3, arg_74_4)
		return arg_74_2, arg_74_3 + (ove_0_39(arg_74_0, "leonashieldofdaybreak") > 0 and 30 * arg_74_0:spellSlot(0).level + 10 + 0.3 * arg_74_0.flatPhysicalDamageMod * arg_74_0.percentPhysicalDamageMod or 0), arg_74_4
	end,
	Lux = function(arg_75_0, arg_75_1, arg_75_2, arg_75_3, arg_75_4)
		return arg_75_2, arg_75_3 + (ove_0_39(arg_75_1, "luxilluminatingfraulein") > 0 and 10 + arg_75_0.levelRef * 10 + arg_75_0.flatPhysicalDamageMod * arg_75_0.percentPhysicalDamageMod * 0.2 or 0), arg_75_4
	end,
	MasterYi = function(arg_76_0, arg_76_1, arg_76_2, arg_76_3, arg_76_4)
		return arg_76_2 + (ove_0_39(arg_76_0, "doublestrike") > 0 and 0.5 * arg_76_2 or 0), arg_76_3, arg_76_4
	end,
	Nocturne = function(arg_77_0, arg_77_1, arg_77_2, arg_77_3, arg_77_4)
		return arg_77_2 + (ove_0_39(arg_77_0, "nocturneumrablades") > 0 and 0.2 * arg_77_2 or 0), arg_77_3, arg_77_4
	end,
	Orianna = function(arg_78_0, arg_78_1, arg_78_2, arg_78_3, arg_78_4)
		return arg_78_2, arg_78_3 + 2 + 8 * math.ceil(arg_78_0.levelRef / 3) + 0.15 * arg_78_0.flatPhysicalDamageMod * arg_78_0.percentPhysicalDamageMod, arg_78_4
	end,
	RekSai = function(arg_79_0, arg_79_1, arg_79_2, arg_79_3, arg_79_4)
		return arg_79_2 + (ove_0_39(arg_79_0, "reksaiq") > 0 and 10 * arg_79_0:spellSlot(0).level + 5 + 0.2 * arg_79_2 or 0), arg_79_4
	end,
	Rengar = function(arg_80_0, arg_80_1, arg_80_2, arg_80_3, arg_80_4)
		return arg_80_2 + (ove_0_39(arg_80_0, "rengarqbase") > 0 and math.max(30 * arg_80_0:spellSlot(0).level + (0.05 * arg_80_0:spellSlot(0).level - 0.05) * arg_80_2) or 0) + (ove_0_39(arg_80_0, "rengarqemp") > 0 and math.min(15 * arg_80_0.levelRef + 15, 10 * arg_80_0.levelRef + 60) + 0.5 * arg_80_2 or 0), arg_80_3, arg_80_4
	end,
	Shyvana = function(arg_81_0, arg_81_1, arg_81_2, arg_81_3, arg_81_4)
		return arg_81_2 + (ove_0_39(arg_81_0, "shyvanadoubleattack") > 0 and (0.05 * arg_81_0:spellSlot(0).level + 0.75) * arg_81_2 or 0), arg_81_3, arg_81_4
	end,
	Talon = function(arg_82_0, arg_82_1, arg_82_2, arg_82_3, arg_82_4)
		return arg_82_2 + (ove_0_39(arg_82_0, "talonnoxiandiplomacybuff") > 0 and 30 * arg_82_0:spellSlot(0).level + 0.3 * (arg_82_0.flatPhysicalDamageMod * arg_82_0.percentPhysicalDamageMod) or 0), arg_82_3, arg_82_4
	end,
	Teemo = function(arg_83_0, arg_83_1, arg_83_2, arg_83_3, arg_83_4)
		return arg_83_2, arg_83_3 + 10 * arg_83_0:spellSlot(2).level + 0.3 * arg_83_0.flatPhysicalDamageMod * arg_83_0.percentPhysicalDamageMod, arg_83_4
	end,
	Trundle = function(arg_84_0, arg_84_1, arg_84_2, arg_84_3, arg_84_4)
		return arg_84_2 + (ove_0_39(arg_84_0, "trundletrollsmash") > 0 and 20 * arg_84_0:spellSlot(0).level + (0.05 * arg_84_0:spellSlot(0).level + 0.095) * arg_84_2 or 0), arg_84_3, arg_84_4
	end,
	Varus = function(arg_85_0, arg_85_1, arg_85_2, arg_85_3, arg_85_4)
		return arg_85_2, arg_85_3 + (ove_0_39(arg_85_0, "varusw") > 0 and 4 * arg_85_0:spellSlot(1).level + 6 + 0.25 * arg_85_0.flatPhysicalDamageMod * arg_85_0.percentPhysicalDamageMod or 0), arg_85_4
	end,
	Vayne = function(arg_86_0, arg_86_1, arg_86_2, arg_86_3, arg_86_4)
		return arg_86_2 + (ove_0_39(arg_86_0, "vaynetumblebonus") > 0 and (0.06 * arg_86_0:spellSlot(0).level + 0.45) * arg_86_2 or 0), arg_86_3, arg_86_4 + (ove_0_39(arg_86_1, "vaynesilvereddebuff") > 1 and 15 * arg_86_0:spellSlot(1).level + 35 + (0.025 * arg_86_0:spellSlot(1).level + 0.015) * arg_86_1.maxHealth or 0)
	end,
	Vi = function(arg_87_0, arg_87_1, arg_87_2, arg_87_3, arg_87_4)
		return arg_87_2 + (ove_0_39(arg_87_0, "vie") > 0 and 15 * arg_87_0:spellSlot(2).level - 10 + 0.15 * arg_87_2 + 0.7 * arg_87_0.flatPhysicalDamageMod * arg_87_0.percentPhysicalDamageMod or 0), arg_87_3, arg_87_4
	end,
	Volibear = function(arg_88_0, arg_88_1, arg_88_2, arg_88_3, arg_88_4)
		return arg_88_2 + (ove_0_39(arg_88_0, "volibearq") > 0 and 30 * arg_88_0:spellSlot(0).level or 0), arg_88_3, arg_88_4
	end
}

local function ove_0_50(arg_89_0)
	if arg_89_0.charName:find("Siege") then
		return arg_89_0.maxHealth * 0.175
	end

	if arg_89_0.charName:find("Cannon") then
		return arg_89_0.maxHealth * 0.14
	end

	if arg_89_0.charName:find("Ranged") then
		return arg_89_0.maxHealth * 0.625
	end

	return 0.425
end

local function ove_0_51(arg_90_0)
	for iter_90_0 = 0, objManager.turrets.size[TEAM_ENEMY] - 1 do
		local slot_90_0 = objManager.turrets[TEAM_ENEMY][iter_90_0]

		if slot_90_0 and not slot_90_0.isDead and slot_90_0.health > 0 then
			if (not slot_90_0.activeSpell or slot_90_0.activeSpell.target.isDead or slot_90_0.activeSpell.target.pos:dist(slot_90_0.pos) > 915 + player.boundingRadius or slot_90_0.activeSpell and slot_90_0.activeSpell.target.ptr == player.ptr) and slot_90_0.pos:dist(arg_90_0) < 915 + player.boundingRadius then
				return true
			end
		else
			local slot_90_1
		end
	end
end

local function ove_0_52(arg_91_0)
	if player:spellSlot(arg_91_0).state == 0 then
		return true
	end

	return false
end

local function ove_0_53(arg_92_0)
	for iter_92_0 = 0, objManager.turrets.size[TEAM_ENEMY] - 1 do
		local slot_92_0 = objManager.turrets[TEAM_ENEMY][iter_92_0]

		if slot_92_0 and not slot_92_0.isDead and slot_92_0.health > 0 then
			if slot_92_0.pos:dist(arg_92_0) < 915 then
				return true
			end
		else
			local slot_92_1
		end
	end

	return false
end

local function ove_0_54(arg_93_0)
	for iter_93_0 = 0, objManager.turrets.size[TEAM_ALLY] - 1 do
		local slot_93_0 = objManager.turrets[TEAM_ALLY][iter_93_0]

		if slot_93_0 and not slot_93_0.isDead and slot_93_0.health > 0 then
			if slot_93_0.pos:dist(arg_93_0) < 915 + player.boundingRadius then
				return true
			end
		else
			local slot_93_1
		end
	end

	return false
end

local function ove_0_55(arg_94_0, arg_94_1, arg_94_2, arg_94_3, arg_94_4)
	if arg_94_1.type == TYPE_HERO then
		local slot_94_0 = (arg_94_2 or 0) + (arg_94_1.baseAttackDamage + arg_94_1.flatPhysicalDamageMod) * arg_94_1.percentPhysicalDamageMod
		local slot_94_1 = arg_94_3 or 0
		local slot_94_2 = arg_94_4 or 0
		local slot_94_3 = arg_94_1.crit > 0.875
		local slot_94_4 = {}

		for iter_94_0 = 0, 6 do
			local slot_94_5 = arg_94_1:itemID(iter_94_0)

			if slot_94_5 > 0 then
				slot_94_4[slot_94_5] = true
			end
		end

		if slot_94_3 then
			if arg_94_1.charName == "Yasuo" then
				slot_94_0 = slot_94_0 * 0.75
			end

			if slot_94_4[3031] then
				if arg_94_0.type == TYPE_MINION then
					slot_94_0 = slot_94_0 * 2.5
				else
					slot_94_2, slot_94_0 = slot_94_0 * 0.2, slot_94_0 * 1.8
				end
			else
				slot_94_0 = slot_94_0 * 2
			end
		end

		if arg_94_1.charName == "Kalista" then
			slot_94_0 = slot_94_0 * 0.9
		end

		if slot_94_4[1416] or slot_94_4[1418] or slot_94_4[1419] then
			if arg_94_0.type == TYPE_HERO then
				slot_94_0 = slot_94_0 + 0.04 * arg_94_0.maxHealth
			else
				slot_94_0 = slot_94_0 + math.min(75, 0.04 * arg_94_0.maxHealth)
			end
		end

		if slot_94_4[3153] then
			if arg_94_0.type == TYPE_HERO then
				slot_94_0 = slot_94_0 + math.max(15, 0.08 * arg_94_0.health)
			else
				slot_94_0 = slot_94_0 + math.max(15, math.min(60, 0.08 * arg_94_0.health))
			end
		end

		if slot_94_4[1043] then
			slot_94_0 = slot_94_0 + 15
		end

		if arg_94_1.buff.lichbane or slot_94_4[3100] then
			slot_94_0 = slot_94_0 + 0.75
			slot_94_1 = slot_94_1 + 0.5
		end

		if arg_94_1.buff.itemstatikshankcharge then
			if slot_94_4[2015] then
				slot_94_1 = slot_94_1 + 40
			elseif slot_94_4[3094] then
				slot_94_1 = slot_94_1 + math.floor(49.5 + math.max((arg_94_1.levelRef - 5) * 8.5, 0))
			elseif slot_94_4[3087] then
				slot_94_1 = slot_94_1 + (slot_94_3 and 2 or 1) * math.floor(50.75 + math.max((arg_94_1.levelRef - 5) * 5.25, 0))
			end
		end

		if ove_0_49[arg_94_1.charName] then
			slot_94_0, slot_94_1, slot_94_2 = ove_0_49[arg_94_1.charName](arg_94_1, arg_94_0, slot_94_0, slot_94_1, slot_94_2)
		end

		return ove_0_46(arg_94_0, arg_94_1, slot_94_0) + ove_0_47(arg_94_0, arg_94_1, slot_94_1) + (slot_94_2 or 0)
	elseif arg_94_1.type == TYPE_MINION then
		if arg_94_0.type == TYPE_MINION then
			return ove_0_46(arg_94_0, arg_94_1, arg_94_1.baseAttackDamage * arg_94_1.percentDamageToBarracksMinionMod) - arg_94_0.flatDamageReductionFromBarracksMinionMod
		else
			return ove_0_46(arg_94_0, arg_94_1, arg_94_1.baseAttackDamage)
		end
	elseif arg_94_1.type == TYPE_TURRET and arg_94_1.type == TYPE_INHIB then
		if arg_94_0.type == TYPE_MINION then
			return ove_0_50(arg_94_0)
		else
			return ove_0_46(arg_94_0, arg_94_1, arg_94_1.baseAttackDamage)
		end
	end

	return 0
end

local function ove_0_56(arg_95_0)
	return arg_95_0.buff[BUFF_INVULNERABILITY]
end

local function ove_0_57(arg_96_0)
	return arg_96_0 and arg_96_0.ptr ~= 0 and not arg_96_0.isDead and arg_96_0.isVisible and arg_96_0.isTargetable and not ove_0_56(arg_96_0) and not arg_96_0.isZombie
end

local function ove_0_58(arg_97_0, arg_97_1)
	return arg_97_1 and arg_97_1.ptr ~= 0 and not arg_97_1.isDead and arg_97_1.isVisible and arg_97_1.isTargetable and not ove_0_56(arg_97_1) and not arg_97_1.isZombie and (not arg_97_0 or arg_97_1.path.serverPos:distSqr(player.pos) <= arg_97_0 * arg_97_0)
end

local function ove_0_59(arg_98_0, arg_98_1)
	return arg_98_0 and arg_98_0.ptr ~= 0 and arg_98_0.type == TYPE_HERO and not arg_98_0.isDead and arg_98_0.isVisible and (arg_98_0.team == player.team or arg_98_0.isTargetable and not ove_0_56(arg_98_0)) and (not arg_98_1 or arg_98_0.pos2D:distSqr(player.pos2D) < arg_98_1 * arg_98_1)
end

local function ove_0_60(arg_99_0, arg_99_1)
	return arg_99_0 and arg_99_0.ptr ~= 0 and arg_99_0.type == TYPE_MINION and (arg_99_1 or arg_99_0.team ~= TEAM_ALLY) and not arg_99_0.isDead and arg_99_0.isVisible and arg_99_0.isTargetable and arg_99_0.health > 5 and arg_99_0.maxHealth > 5 and arg_99_0.maxHealth < 100000
end

local function ove_0_61(arg_100_0, arg_100_1)
	local slot_100_0 = arg_100_1 or player
	local slot_100_1 = arg_100_0.x - slot_100_0.x
	local slot_100_2 = (arg_100_0.z or arg_100_0.y) - (slot_100_0.z or slot_100_0.y)

	return slot_100_1 * slot_100_1 + slot_100_2 * slot_100_2
end

local function ove_0_62(arg_101_0, arg_101_1)
	local slot_101_0 = ove_0_61(arg_101_0, arg_101_1)

	return math.sqrt(slot_101_0)
end

local ove_0_63

local function ove_0_64(arg_102_0, arg_102_1)
	if not ove_0_57(arg_102_0) or not arg_102_0.path or not arg_102_1 or not arg_102_0.moveSpeed then
		return arg_102_0.pos
	end

	ove_0_63 = ove_0_63 or module.internal("pred")

	local slot_102_0 = ove_0_63.core.lerp(arg_102_0.path, network.latency + arg_102_1, arg_102_0.moveSpeed)

	return vec3(slot_102_0.x, arg_102_0.y, slot_102_0.y)
end

local function ove_0_65(arg_103_0, arg_103_1)
	if not ove_0_57(arg_103_0) or not arg_103_0.path or not arg_103_0.path.isActive or not arg_103_0.moveSpeed then
		return false
	end

	ove_0_63 = ove_0_63 or module.internal("pred")

	local slot_103_0 = ove_0_63.core.lerp(arg_103_0.path, network.latency + 0.25, arg_103_0.moveSpeed)

	return vec3(slot_103_0.x, arg_103_0.y, slot_103_0.y):dist(arg_103_1.pos) > arg_103_0.pos:dist(arg_103_1.pos)
end

local function ove_0_66(arg_104_0)
	return ove_0_65(arg_104_0, objManager.player)
end

local function ove_0_67(arg_105_0)
	local slot_105_0 = graphics.world_to_screen(arg_105_0)

	if slot_105_0.x < 0 or slot_105_0.x > graphics.width or slot_105_0.y < 0 or slot_105_0.y > graphics.height then
		return false
	end

	return true
end

local function ove_0_68(arg_106_0)
	local slot_106_0 = arg_106_0
	local slot_106_1 = "max" .. arg_106_0:sub(1, 1):upper() .. arg_106_0:sub(2)

	return function(arg_107_0)
		arg_107_0 = arg_107_0 or player

		return 100 * arg_107_0[slot_106_0] / arg_107_0[slot_106_1]
	end
end

local function ove_0_69(arg_108_0, arg_108_1)
	if arg_108_0 == TYPE_HERO then
		local slot_108_0 = arg_108_1 == player.team and "allies" or "enemies"

		return function(arg_109_0, arg_109_1)
			arg_109_1 = arg_109_1 or player.pos
			arg_109_0 = arg_109_0 * arg_109_0

			local slot_109_0 = {}

			for iter_109_0 = 0, objManager[slot_108_0 .. "_n"] - 1 do
				local slot_109_1 = objManager[slot_108_0][iter_109_0]

				if ove_0_59(slot_109_1) and arg_109_0 > slot_109_1.pos:distSqr(arg_109_1) and slot_109_1.ptr ~= player.ptr then
					slot_109_0[#slot_109_0 + 1] = slot_109_1
				end
			end

			return slot_109_0
		end
	elseif arg_108_0 == TYPE_MINION then
		return function(arg_110_0, arg_110_1, arg_110_2)
			if type(arg_110_1) == "number" then
				arg_110_1, arg_110_2 = arg_110_2, arg_110_1
			end

			arg_110_1 = arg_110_1 or player.pos
			arg_110_0 = arg_110_0 * arg_110_0
			arg_110_2 = arg_110_2 or arg_108_1 or TEAM_ENEMY

			local slot_110_0 = {}
			local slot_110_1 = objManager.minions[arg_110_2]

			for iter_110_0 = 0, objManager.minions.size[arg_110_2] - 1 do
				local slot_110_2 = slot_110_1[iter_110_0]

				if ove_0_60(slot_110_2, true) and arg_110_0 > slot_110_2.pos:distSqr(arg_110_1) then
					slot_110_0[#slot_110_0 + 1] = slot_110_2
				end
			end

			return slot_110_0
		end
	end
end

local ove_0_70 = {
	100,
	105,
	110,
	115,
	120,
	130,
	140,
	150,
	165,
	180,
	200,
	225,
	255,
	290,
	330,
	380,
	440,
	510
}

local function ove_0_71(arg_111_0, arg_111_1)
	local slot_111_0 = 0

	if arg_111_0:find("AD") then
		slot_111_0 = arg_111_1.physicalShield
	elseif arg_111_0:find("AP") then
		slot_111_0 = arg_111_1.magicalShield
	elseif arg_111_0:find("ALL") then
		slot_111_0 = arg_111_1.allShield + (arg_111_1.charName == "Yasuo" and arg_111_1.mana == arg_111_1.maxMana and ove_0_70[arg_111_1.levelRef] or 0) + (arg_111_1.charName == "Blitzcrank" and not arg_111_1.buff.blitzcrankmanabarriercd and not arg_111_1.buff.manabarrier and arg_111_1.mana / 2 or 0)
	end

	return arg_111_1.health + slot_111_0
end

local function ove_0_72(arg_112_0)
	local slot_112_0 = arg_112_0.healthRegenRate

	for iter_112_0 = 4, 5 do
		local slot_112_1 = arg_112_0:spellSlot(iter_112_0)

		if slot_112_1 then
			if slot_112_1.name:lower():find("heal") then
				slot_112_0 = slot_112_0 + (slot_112_1.state == 0 and 75 + arg_112_0.levelRef * 15 or 0)
			end

			if slot_112_1.name:lower():find("barrier") then
				slot_112_0 = slot_112_0 + (slot_112_1.state == 0 and 95 + arg_112_0.levelRef * 20 or 0)
			end
		end
	end

	return slot_112_0
end

local function ove_0_73(arg_113_0, arg_113_1)
	arg_113_1 = arg_113_1 or player

	return arg_113_1.attackRange + arg_113_1.boundingRadius + (arg_113_0 and arg_113_0.boundingRadius or 0)
end

local function ove_0_74(arg_114_0, arg_114_1)
	arg_114_1 = arg_114_1 or player

	local slot_114_0 = 55 + 25 * arg_114_1.levelRef

	if arg_114_0 then
		slot_114_0 = slot_114_0 - (ove_0_71("AD", arg_114_0) - arg_114_0.health)
	end

	return slot_114_0
end

local function ove_0_75(arg_115_0, arg_115_1)
	local slot_115_0 = {}

	for iter_115_0 = 0, objManager.enemies_n - 1 do
		local slot_115_1 = objManager.enemies[iter_115_0]

		if arg_115_1 > arg_115_0:dist(slot_115_1.pos) and not slot_115_1.isDead and slot_115_1.isVisible then
			slot_115_0[#slot_115_0 + 1] = slot_115_1
		end
	end

	return slot_115_0
end

local function ove_0_76(arg_116_0, arg_116_1)
	local slot_116_0 = {}

	for iter_116_0 = 0, objManager.allies_n - 1 do
		local slot_116_1 = objManager.allies[iter_116_0]

		if arg_116_1 > arg_116_0:dist(slot_116_1.pos) and not slot_116_1.isDead and slot_116_1.ptr ~= player.ptr then
			slot_116_0[#slot_116_0 + 1] = slot_116_1
		end
	end

	return slot_116_0
end

local ove_0_77

local function ove_0_78()
	if ove_0_77 then
		return ove_0_77
	end

	ove_0_77 = {}

	for iter_117_0 = 0, objManager.enemies_n - 1 do
		local slot_117_0 = objManager.enemies[iter_117_0]

		ove_0_77[#ove_0_77 + 1] = slot_117_0
	end

	return ove_0_77
end

local ove_0_79

local function ove_0_80()
	if ove_0_79 then
		return ove_0_79
	end

	ove_0_79 = {}

	for iter_118_0 = 0, objManager.allies_n - 1 do
		local slot_118_0 = objManager.allies[iter_118_0]

		ove_0_79[#ove_0_79 + 1] = slot_118_0
	end

	return ove_0_79
end

local ove_0_81 = {
	nil,
	nil,
	nil,
	nil,
	true,
	nil,
	nil,
	true,
	nil,
	nil,
	true,
	nil,
	nil,
	nil,
	nil,
	nil,
	nil,
	true,
	nil,
	nil,
	true,
	true,
	nil,
	true,
	nil,
	nil,
	nil,
	true,
	true,
	true
}

local function ove_0_82(arg_119_0)
	local slot_119_0 = arg_119_0 or player

	for iter_119_0, iter_119_1 in pairs(slot_119_0.buff) do
		if ove_0_81[iter_119_1.type] then
			return false
		end
	end
end

local ove_0_83 = {
	anivia = {
		{
			spellname = "glacialstorm",
			menuslot = "R",
			slot = 3,
			channelduration = 6
		}
	},
	caitlyn = {
		{
			spellname = "caitlynaceinthehole",
			menuslot = "R",
			slot = 3,
			channelduration = 1
		}
	},
	ezreal = {
		{
			spellname = "ezrealtrueshotbarrage",
			menuslot = "R",
			slot = 3,
			channelduration = 1
		}
	},
	fiddlesticks = {
		{
			spellname = "drain",
			menuslot = "W",
			slot = 1,
			channelduration = 5
		},
		{
			spellname = "crowstorm",
			menuslot = "R",
			slot = 3,
			channelduration = 1.5
		}
	},
	gragas = {
		{
			spellname = "gragasw",
			menuslot = "W",
			slot = 1,
			channelduration = 0.75
		}
	},
	janna = {
		{
			spellname = "reapthewhirlwind",
			menuslot = "R",
			slot = 3,
			channelduration = 3
		}
	},
	karthus = {
		{
			spellname = "karthusfallenone",
			menuslot = "R",
			slot = 3,
			channelduration = 3
		}
	},
	katarina = {
		{
			spellname = "katarinar",
			menuslot = "R",
			slot = 3,
			channelduration = 2.5
		}
	},
	lucian = {
		{
			spellname = "lucianr",
			menuslot = "R",
			slot = 3,
			channelduration = 2
		}
	},
	lux = {
		{
			spellname = "luxmalicecannon",
			menuslot = "R",
			slot = 3,
			channelduration = 0.5
		}
	},
	malzahar = {
		{
			spellname = "malzaharr",
			menuslot = "R",
			slot = 3,
			channelduration = 2.5
		}
	},
	masteryi = {
		{
			spellname = "meditate",
			menuslot = "W",
			slot = 1,
			channelduration = 4
		}
	},
	missfortune = {
		{
			spellname = "missfortunebullettime",
			menuslot = "R",
			slot = 3,
			channelduration = 3
		}
	},
	pantheon = {
		{
			spellname = "pantheonrjump",
			menuslot = "R",
			slot = 3,
			channelduration = 2
		}
	},
	shen = {
		{
			spellname = "shenr",
			menuslot = "R",
			slot = 3,
			channelduration = 3
		}
	},
	tristana = {
		{
			spellname = "tristanaw",
			menuslot = "W",
			slot = 1,
			channelduration = 1.5
		}
	},
	twistedfate = {
		{
			spellname = "gate",
			menuslot = "R",
			slot = 3,
			channelduration = 1.5
		}
	},
	varus = {
		{
			spellname = "varusq",
			menuslot = "Q",
			slot = 0,
			channelduration = 4
		}
	},
	warwick = {
		{
			spellname = "warwickr",
			menuslot = "R",
			slot = 3,
			channelduration = 1.5
		}
	},
	xerath = {
		{
			spellname = "xerathlocusofpower2",
			menuslot = "R",
			slot = 3,
			channelduration = 3
		}
	}
}

local function ove_0_84(arg_120_0, arg_120_1, arg_120_2)
	local slot_120_0 = arg_120_2.x
	local slot_120_1 = arg_120_2.z or arg_120_2.y
	local slot_120_2 = arg_120_0.x
	local slot_120_3 = arg_120_0.z or arg_120_0.y
	local slot_120_4 = arg_120_1.x
	local slot_120_5 = arg_120_1.z or arg_120_1.y
	local slot_120_6 = ((slot_120_0 - slot_120_2) * (slot_120_4 - slot_120_2) + (slot_120_1 - slot_120_3) * (slot_120_5 - slot_120_3)) / ((slot_120_4 - slot_120_2)^2 + (slot_120_5 - slot_120_3)^2)
	local slot_120_7 = vec2(slot_120_2 + slot_120_6 * (slot_120_4 - slot_120_2), slot_120_3 + slot_120_6 * (slot_120_5 - slot_120_3))
	local slot_120_8 = slot_120_6 < 0 and 0 or slot_120_6 > 1 and 1 or slot_120_6
	local slot_120_9 = slot_120_8 == slot_120_6

	return slot_120_9 and slot_120_7 or vec2(slot_120_2 + slot_120_8 * (slot_120_4 - slot_120_2), slot_120_3 + slot_120_8 * (slot_120_5 - slot_120_3)), slot_120_7, slot_120_9
end

local function ove_0_85(arg_121_0, arg_121_1, arg_121_2, arg_121_3)
	local slot_121_0 = arg_121_0.x
	local slot_121_1 = arg_121_0.z or arg_121_0.y
	local slot_121_2 = arg_121_1.x
	local slot_121_3 = arg_121_1.z or arg_121_1.y
	local slot_121_4 = arg_121_2.x
	local slot_121_5 = arg_121_2.z or arg_121_2.y
	local slot_121_6 = arg_121_3.x
	local slot_121_7 = arg_121_3.z or arg_121_3.y
	local slot_121_8 = slot_121_0 * slot_121_3 - slot_121_1 * slot_121_2
	local slot_121_9 = slot_121_4 * slot_121_7 - slot_121_5 * slot_121_6
	local slot_121_10 = slot_121_4 - slot_121_6
	local slot_121_11 = slot_121_0 - slot_121_2
	local slot_121_12 = slot_121_5 - slot_121_7
	local slot_121_13 = slot_121_1 - slot_121_3
	local slot_121_14 = slot_121_8 * slot_121_10 - slot_121_11 * slot_121_9
	local slot_121_15 = slot_121_8 * slot_121_12 - slot_121_13 * slot_121_9
	local slot_121_16 = slot_121_11 * slot_121_12 - slot_121_13 * slot_121_10

	return slot_121_16 ~= 0 and vec2(slot_121_14 / slot_121_16, slot_121_15 / slot_121_16)
end

local function ove_0_86(arg_122_0, arg_122_1, arg_122_2, arg_122_3, arg_122_4, arg_122_5)
	local slot_122_0 = arg_122_0.x
	local slot_122_1 = arg_122_0.z or arg_122_0.y
	local slot_122_2 = arg_122_1.x
	local slot_122_3 = arg_122_1.z or arg_122_1.y
	local slot_122_4 = arg_122_3.x
	local slot_122_5 = arg_122_3.z or arg_122_3.y
	local slot_122_6 = slot_122_2 - slot_122_0
	local slot_122_7 = slot_122_3 - slot_122_1
	local slot_122_8 = math.sqrt(slot_122_6 * slot_122_6 + slot_122_7 * slot_122_7)
	local slot_122_9
	local slot_122_10
	local slot_122_11 = slot_122_8 ~= 0 and arg_122_2 * slot_122_6 / slot_122_8 or 0
	local slot_122_12 = slot_122_8 ~= 0 and arg_122_2 * slot_122_7 / slot_122_8 or 0

	local function slot_122_13(arg_123_0)
		return arg_123_0 and {
			x = slot_122_0 + slot_122_11 * arg_123_0,
			y = slot_122_1 + slot_122_12 * arg_123_0
		} or nil
	end

	if arg_122_5 and arg_122_5 ~= 0 then
		slot_122_0, slot_122_1 = slot_122_0 + slot_122_11 * arg_122_5, slot_122_1 + slot_122_12 * arg_122_5
	end

	local slot_122_14 = slot_122_4 - slot_122_0
	local slot_122_15 = slot_122_5 - slot_122_1
	local slot_122_16 = slot_122_14 * slot_122_14 + slot_122_15 * slot_122_15

	if slot_122_8 > 0 then
		if arg_122_2 == math.huge then
			local slot_122_17 = slot_122_8 / arg_122_2

			slot_122_9 = arg_122_4 * slot_122_17 >= 0 and slot_122_17 or nil
		elseif arg_122_4 == math.huge then
			slot_122_9 = 0
		else
			local slot_122_18 = slot_122_11 * slot_122_11 + slot_122_12 * slot_122_12 - arg_122_4 * arg_122_4
			local slot_122_19 = -slot_122_14 * slot_122_11 - slot_122_15 * slot_122_12

			if slot_122_18 == 0 then
				if slot_122_19 == 0 then
					slot_122_9 = slot_122_16 == 0 and 0 or nil
				else
					local slot_122_20 = -slot_122_16 / (2 * slot_122_19)

					slot_122_9 = arg_122_4 * slot_122_20 >= 0 and slot_122_20 or nil
				end
			else
				local slot_122_21 = slot_122_19 * slot_122_19 - slot_122_18 * slot_122_16

				if slot_122_21 >= 0 then
					local slot_122_22 = math.sqrt(slot_122_21)
					local slot_122_23 = (-slot_122_22 - slot_122_19) / slot_122_18

					slot_122_9 = arg_122_4 * slot_122_23 >= 0 and slot_122_23 or nil

					local slot_122_24 = (slot_122_22 - slot_122_19) / slot_122_18

					slot_122_10 = arg_122_4 * slot_122_24 >= 0 and slot_122_24 or nil
				end
			end
		end
	elseif slot_122_8 == 0 then
		slot_122_9 = 0
	end

	return slot_122_9, slot_122_13(slot_122_9), slot_122_10, slot_122_13(slot_122_10), slot_122_8
end

local function ove_0_87(arg_124_0, arg_124_1)
	local slot_124_0 = math.cos(arg_124_1)
	local slot_124_1 = math.sin(arg_124_1)

	return vec3(arg_124_0.x * slot_124_0 - arg_124_0.z * slot_124_1, 0, arg_124_0.z * slot_124_0 + arg_124_0.x * slot_124_1)
end

local function ove_0_88(arg_125_0)
	if not arg_125_0 then
		return 0
	end

	local slot_125_0 = 0

	for iter_125_0 = 0, arg_125_0.count - 1 do
		slot_125_0 = slot_125_0 + arg_125_0.point[iter_125_0]:dist(arg_125_0.point[iter_125_0 + 1])
	end

	return slot_125_0
end

local function ove_0_89(arg_126_0, arg_126_1, arg_126_2, arg_126_3)
	arg_126_2 = arg_126_2 or false
	arg_126_3 = arg_126_3 or 0

	local slot_126_0 = string.rep(" ", arg_126_3)

	if arg_126_1 then
		slot_126_0 = slot_126_0 .. arg_126_1 .. " = "
	end

	if type(arg_126_0) == "table" then
		slot_126_0 = slot_126_0 .. "{" .. (not arg_126_2 and "\n" or "")

		for iter_126_0, iter_126_1 in pairs(arg_126_0) do
			slot_126_0 = slot_126_0 .. ove_0_89(iter_126_1, iter_126_0, arg_126_2, arg_126_3 + 1) .. "," .. (not arg_126_2 and "\n" or "")
		end

		slot_126_0 = slot_126_0 .. string.rep(" ", arg_126_3) .. "}"
	elseif type(arg_126_0) == "number" then
		slot_126_0 = slot_126_0 .. tostring(arg_126_0)
	elseif type(arg_126_0) == "string" then
		slot_126_0 = slot_126_0 .. string.format("%q", arg_126_0)
	elseif type(arg_126_0) == "boolean" then
		slot_126_0 = slot_126_0 .. (arg_126_0 and "true" or "false")
	else
		slot_126_0 = slot_126_0 .. "\"[inserializeable datatype:" .. type(arg_126_0) .. "]\""
	end

	return slot_126_0
end

local function ove_0_90(arg_127_0, arg_127_1, arg_127_2, arg_127_3, arg_127_4)
	local slot_127_0 = 0

	for iter_127_0 = 0, arg_127_4 - 1 do
		local slot_127_1 = arg_127_3[iter_127_0]

		if slot_127_1 then
			local slot_127_2, slot_127_3, slot_127_4 = ove_0_84(arg_127_0, arg_127_1, slot_127_1)

			if slot_127_4 and slot_127_1.pos2D:distSqr(slot_127_2) < arg_127_2 * arg_127_2 and arg_127_0:distSqr(arg_127_1) > arg_127_0:distSqr(slot_127_1.pos) then
				slot_127_0 = slot_127_0 + 1
			end
		end
	end

	return slot_127_0
end

local function ove_0_91(arg_128_0, arg_128_1, arg_128_2)
	local slot_128_0 = arg_128_0.x
	local slot_128_1 = arg_128_0.z
	local slot_128_2 = arg_128_1.x
	local slot_128_3 = arg_128_1.z
	local slot_128_4 = arg_128_2.x
	local slot_128_5 = arg_128_2.z
	local slot_128_6 = ((slot_128_0 - slot_128_2) * (slot_128_4 - slot_128_2) + (slot_128_1 - slot_128_3) * (slot_128_5 - slot_128_3)) / (math.pow(slot_128_4 - slot_128_2, 2) + math.pow(slot_128_5 - slot_128_3, 2))
	local slot_128_7 = vec3(slot_128_2 + slot_128_6 * (slot_128_4 - slot_128_2), 0, slot_128_3 + slot_128_6 * (slot_128_5 - slot_128_3))

	if slot_128_6 < 0 then
		rS = 0
	elseif slot_128_6 > 1 then
		rS = 1
	else
		rS = slot_128_6
	end

	if rS == slot_128_6 then
		isOnSegment = true
		pointSegment = slot_128_7
	else
		isOnSegment = false
		pointSegment = vec3(slot_128_2 + rS * (slot_128_4 - slot_128_2), 0, slot_128_3 + rS * (slot_128_5 - slot_128_3))
	end

	return isOnSegment, pointSegment
end

local function ove_0_92(arg_129_0, arg_129_1, arg_129_2, arg_129_3, arg_129_4)
	local slot_129_0
	local slot_129_1 = 0

	for iter_129_0 = 0, arg_129_4 - 1 do
		local slot_129_2 = arg_129_3[iter_129_0]

		if slot_129_2 and slot_129_2.pos:distSqr(arg_129_0.pos) < arg_129_1 * arg_129_1 then
			if slot_129_2.name:find("Dragon") or slot_129_2.name:find("Herald") or slot_129_2.name:find("Baron") then
				return slot_129_2, 1
			end

			local slot_129_3 = arg_129_0.pos + arg_129_1 * (slot_129_2.pos - arg_129_0.pos):norm()
			local slot_129_4 = ove_0_90(arg_129_0.pos, slot_129_3, arg_129_2, arg_129_3, arg_129_4)

			if slot_129_1 < slot_129_4 then
				slot_129_1 = slot_129_4
				slot_129_0 = slot_129_2.pos

				if slot_129_1 == arg_129_4 - 1 then
					break
				end
			end
		end
	end

	return slot_129_0, slot_129_1
end

local function ove_0_93(arg_130_0, arg_130_1, arg_130_2)
	local slot_130_0 = math.cos(arg_130_2)
	local slot_130_1 = math.sin(arg_130_2)
	local slot_130_2 = (arg_130_0.x - arg_130_1.x) * slot_130_0 - (arg_130_1.z - arg_130_0.z) * slot_130_1 + arg_130_1.x
	local slot_130_3 = (arg_130_1.z - arg_130_0.z) * slot_130_0 + (arg_130_0.x - arg_130_1.x) * slot_130_1 + arg_130_1.z

	return vec3(slot_130_2, arg_130_0.y, slot_130_3 or 0)
end

local function ove_0_94(arg_131_0, arg_131_1, arg_131_2, arg_131_3)
	local slot_131_0 = 0
	local slot_131_1 = {}

	for iter_131_0, iter_131_1 in pairs(arg_131_2) do
		if arg_131_3(iter_131_1) and arg_131_1 >= arg_131_0:dist(iter_131_1.pos) then
			slot_131_0 = slot_131_0 + 1
			slot_131_1[slot_131_0] = iter_131_1
		end
	end

	return slot_131_0, slot_131_1
end

local function ove_0_95(arg_132_0, arg_132_1)
	if arg_132_0 then
		for iter_132_0, iter_132_1 in pairs(arg_132_0.buff) do
			if iter_132_1 and iter_132_1.valid and iter_132_1.type == arg_132_1 and (iter_132_1.stacks > 0 or iter_132_1.stacks2 > 0) then
				return true
			end
		end
	end
end

local function ove_0_96(arg_133_0)
	return player.path.serverPos:distSqr(arg_133_0.path.serverPos) > player.path.serverPos:distSqr(arg_133_0.path.serverPos + arg_133_0.direction)
end

local function ove_0_97(arg_134_0, arg_134_1)
	arg_134_1 = arg_134_1 or player

	return arg_134_1.path.serverPos:distSqr(arg_134_0.path.serverPos) > arg_134_1.path.serverPos:distSqr(arg_134_0.path.serverPos + arg_134_0.direction)
end

local function ove_0_98(arg_135_0)
	return arg_135_0.path
end

local function ove_0_99(arg_136_0, arg_136_1)
	if arg_136_1 == nil then
		arg_136_1 = 0
	end

	local slot_136_0 = arg_136_1 == 0 and arg_136_0.attackRange or arg_136_1

	if slot_136_0 > player.pos:dist(arg_136_0.pos) then
		return true
	end

	if player.pos:dist(ove_0_98(player).serverPos) < 10 then
		return false
	end

	return ove_0_96(arg_136_0) and ove_0_98(player).serverPos:distSqr(arg_136_0.pos) < slot_136_0 * slot_136_0
end

local function ove_0_100(arg_137_0)
	if arg_137_0.buff[BUFF_PHYSICALIMMUNITY] or arg_137_0.buff[BUFF_SPELLIMMUNITY] or arg_137_0.buff[BUFF_INVULNERABILITY] or arg_137_0.buff.kayler or arg_137_0.buff.kindredrnodeathbuff or arg_137_0.buff.sionpassivezombie or arg_137_0.buff.chronoshift or arg_137_0.buff.undyingrage or arg_137_0.buff[BUFF_SPELLSHIELD] then
		return false
	end

	return true
end

local ove_0_101 = 0

local function ove_0_102(arg_138_0, arg_138_1, arg_138_2)
	if arg_138_2 > ove_0_101 or arg_138_1.buff.rocketgrab or arg_138_1.buff.sivire or arg_138_1.buff.fioraw then
		return
	end

	if not arg_138_1 then
		return
	end

	arg_138_0.obj = arg_138_1

	return true
end

local function ove_0_103(arg_139_0)
	ove_0_101 = arg_139_0

	return ove_0_10.get_result(ove_0_102).obj
end

local function ove_0_104(arg_140_0)
	ove_0_101 = arg_140_0

	local slot_140_0 = {}

	table.insert(slot_140_0, ove_0_10.get_result(ove_0_102).obj)

	return slot_140_0
end

local function ove_0_105(arg_141_0, arg_141_1, arg_141_2)
	return not arg_141_0 or ove_0_61(arg_141_1, arg_141_2) <= arg_141_0 * arg_141_0
end

local function ove_0_106(arg_142_0, arg_142_1)
	return arg_142_0 and arg_142_0.ptr ~= 0 and arg_142_0.team == arg_142_1 and arg_142_0.type == TYPE_MINION and not arg_142_0.isDead and arg_142_0.isTargetable and arg_142_0.isVisible and arg_142_0.health > 5 and arg_142_0.maxHealth > 5
end

local function ove_0_107(arg_143_0, arg_143_1, arg_143_2)
	local slot_143_0 = {}

	for iter_143_0 = 0, objManager.minions.size[arg_143_2] - 1 do
		local slot_143_1 = objManager.minions[arg_143_2][iter_143_0]

		if ove_0_106(slot_143_1, arg_143_2) and arg_143_1 > slot_143_1.pos:dist(arg_143_0) then
			slot_143_0[#slot_143_0 + 1] = slot_143_1
		end
	end

	return slot_143_0
end

local ove_0_108 = {
	PlantMasterMinion = true,
	PlantHealth = true,
	YellowTrinket = true,
	JammerDevice = true,
	SightWard = true,
	PlantSatchel = true,
	BlueTrinket = true,
	PlantVision = true,
	CampRespawn = true
}

local function ove_0_109()
	local slot_144_0 = module.seek("evade")
	local slot_144_1 = module.is_shard("viktorevade")
	local slot_144_2 = {
		evade_0_1 = false,
		evade_0_2 = false
	}

	if not slot_144_0 then
		slot_144_2.evade_0_2 = false
		slot_144_2.evade_0_1 = false

		return
	end

	for iter_144_0, iter_144_1 in pairs(slot_144_0.menu) do
		if iter_144_1 and iter_144_1 ~= nil and type(iter_144_1) == "string" then
			if iter_144_1 == "internal_evade_2" then
				slot_144_2.evade_0_2 = true

				break
			end

			if iter_144_1 == "internal_evade" or iter_144_1 ~= "internal_evade_2" and slot_144_1 and slot_144_1 ~= nil then
				slot_144_2.evade_0_1 = true

				break
			end
		end
	end

	return slot_144_2
end

local function ove_0_110(arg_145_0)
	return arg_145_0 and arg_145_0.type == TYPE_MINION and not arg_145_0.isDead and arg_145_0.maxHealth > 5 and arg_145_0.health > 5 and not arg_145_0.name:find("Ward") and not ove_0_108[arg_145_0.charName] and not ove_0_108[arg_145_0.name]
end

local function ove_0_111(arg_146_0, arg_146_1, arg_146_2)
	local slot_146_0 = arg_146_0.pos:dist(arg_146_1.pos)
	local slot_146_1 = slot_146_0 * slot_146_0 / (2 * slot_146_0) / 2
	local slot_146_2 = math.sqrt(arg_146_2 * arg_146_2 + slot_146_1 * slot_146_1)
	local slot_146_3 = (arg_146_1.pos - arg_146_0.pos):norm()
	local slot_146_4 = arg_146_0 - slot_146_1 * slot_146_3
	local slot_146_5 = slot_146_4 + slot_146_2 * slot_146_3:perp1()
	local slot_146_6 = slot_146_4 - slot_146_2 * slot_146_3:perp1()

	return slot_146_5, slot_146_6
end

local function ove_0_112(arg_147_0, arg_147_1, arg_147_2)
	local slot_147_0 = 0

	for iter_147_0 = 0, objManager.minions.size[arg_147_2] - 1 do
		local slot_147_1 = objManager.minions[arg_147_2][iter_147_0]

		if ove_0_106(slot_147_1, arg_147_2) and arg_147_1 > slot_147_1.pos:dist(arg_147_0) then
			slot_147_0 = slot_147_0 + 1
		end
	end

	return slot_147_0
end

local function ove_0_113(arg_148_0, arg_148_1)
	arg_148_0 = arg_148_0 or player

	if ove_0_62(arg_148_0, arg_148_1) <= ove_0_73(arg_148_1, arg_148_0) then
		return true
	end

	return false
end

local function ove_0_114(arg_149_0, arg_149_1)
	arg_149_1 = arg_149_1 or 915

	if not arg_149_0 then
		return
	end

	for iter_149_0 = 0, objManager.turrets.size[TEAM_ENEMY] - 1 do
		local slot_149_0 = objManager.turrets[TEAM_ENEMY][iter_149_0]

		if slot_149_0 and not slot_149_0.isDead and slot_149_0.health > 0 and arg_149_0 and ove_0_61(arg_149_0, slot_149_0.pos) < arg_149_1^2 then
			return true
		end
	end

	return false
end

local function ove_0_115(arg_150_0)
	for iter_150_0 = ove_0_11.core.skillshots.n, 1, -1 do
		local slot_150_0 = ove_0_11.core.skillshots[iter_150_0]

		if slot_150_0 and slot_150_0:intersection(player.pos2D, arg_150_0:to2D()) then
			return false
		end

		if slot_150_0 and slot_150_0:contains(arg_150_0:to2D()) then
			return false
		end
	end

	return true
end

local function ove_0_116()
	return network.latency * 100 * 0.1
end

return {
	gameLatency = ove_0_116,
	setDelayAction = ove_0_29,
	setIntervalAction = ove_0_32,
	calculateMagicalDamage = ove_0_47,
	calculatePhysicalDamage = ove_0_46,
	calculateFullAADamage = ove_0_55,
	isTargetValid = ove_0_57,
	isMinionValid = ove_0_60,
	isValidTarget = ove_0_57,
	isValidMinion = ove_0_60,
	isFleeing = ove_0_65,
	isFleeingFromMe = ove_0_66,
	getPredictedPos = ove_0_64,
	getBuffValid = ove_0_38,
	getBuffStacks = ove_0_39,
	HasBuffCount = ove_0_39,
	getBuffStartTime = ove_0_40,
	getBuffEndTime = ove_0_41,
	ChatPrint = ove_0_18,
	isPosOnScreen = ove_0_67,
	getShieldedHealth = ove_0_71,
	getAARange = ove_0_73,
	getIgniteDamage = ove_0_74,
	getKSHealth = ove_0_72,
	getEnemyHeroes = ove_0_78,
	getAllyHeroes = ove_0_80,
	getTotalAP = ove_0_45,
	getTotalAD = ove_0_43,
	getBonusAD = ove_0_44,
	getPhysicalReduction = ove_0_33,
	getMagicalReduction = ove_0_34,
	getTowerMinionDamage = ove_0_50,
	makeGetPercentStatFunc = ove_0_68,
	makeObjectInRangeFunc = ove_0_69,
	is_action_safe = ove_0_115,
	DelayAction = ove_0_29,
	SetInterval = ove_0_32,
	GetPercentHealth = ove_0_68("health"),
	GetPercentMana = ove_0_68("mana"),
	GetPercentPar = ove_0_68("par"),
	GetPercentSar = ove_0_68("sar"),
	GetAllyHeroesInRange = ove_0_69(TYPE_HERO, TEAM_ALLY),
	GetEnemyHeroesInRange = ove_0_69(TYPE_HERO, TEAM_ENEMY),
	GetAllyMinionsInRange = ove_0_69(TYPE_MINION, TEAM_ALLY),
	GetEnemyMinionsInRange = ove_0_69(TYPE_MINION, TEAM_ENEMY),
	GetMinionsInRange = ove_0_69(TYPE_MINION),
	CheckBuff = ove_0_38,
	CheckBuff2 = ove_0_38,
	CheckBuffType = ove_0_95,
	StartTime = ove_0_40,
	EndTime = ove_0_41,
	CountBuff = ove_0_39,
	GetShieldedHealth = ove_0_71,
	CalculateMagicDamage = ove_0_47,
	CalculatePhysicalDamage = ove_0_46,
	CalculateAADamage = ove_0_46,
	CalculateFullAADamage = ove_0_55,
	GetAARange = ove_0_73,
	GetPredictedPos = ove_0_64,
	GetIgniteDamage = ove_0_74,
	GetDistanceSqr = ove_0_61,
	GetDistance = ove_0_62,
	IsValidTarget = ove_0_57,
	IsValidTargetIsInRange = ove_0_58,
	GetEnemyHeroes = ove_0_78,
	GetAllyHeroes = ove_0_80,
	GetTotalAP = ove_0_45,
	CanPlayerMove = ove_0_82,
	UnderDangerousTower = ove_0_51,
	IsUnderDangerousTower = ove_0_53,
	IsUnderAllyTurret = ove_0_54,
	GetTotalAD = ove_0_43,
	GetBonusAD = ove_0_44,
	Class = ove_0_30,
	PhysicalReduction = ove_0_33,
	MagicalReduction = ove_0_34,
	MagicReduction = ove_0_34,
	GetLineFarmPosition = ove_0_92,
	CountObjectsNearPos = ove_0_94,
	VectorPointProjectionOnLineSegment = ove_0_84,
	vector_point_project = ove_0_84,
	VectorMovementCollision = ove_0_86,
	VectorIntersection = ove_0_85,
	CountEnemiesInRange = ove_0_75,
	CountAllysInRange = ove_0_76,
	CountAllyChampAroundObject = ove_0_76,
	GetTarget = ove_0_103,
	GetTargetTable = ove_0_104,
	IsInRange = ove_0_105,
	IsMovingTowards = ove_0_99,
	RotateAroundPoint = ove_0_93,
	IsEnemyMortal = ove_0_100,
	GetMinions = ove_0_107,
	CountMinionsInRange = ove_0_112,
	ProjectOn = ove_0_91,
	interruptableSpells = ove_0_83,
	printDebug = ove_0_27,
	log_file = ove_0_19,
	IsFacing = ove_0_96,
	IsFacingWithSource = ove_0_97,
	CountObjectsOnLineSegment = ove_0_90,
	IsReady = ove_0_52,
	SetZ = ove_0_24,
	ExDelayAction = ove_0_21,
	ExecuteDelayAction = ove_0_22,
	assert = ove_0_17,
	CircleCircleIntersection = ove_0_111,
	Rotated = ove_0_87,
	valid_minion = ove_0_110,
	IsInAutoAttackRange = ove_0_113,
	isUnderEnemyTurret = ove_0_114,
	isEvade = ove_0_109,
	IntDelayAction = ove_0_16,
	interrupt_spells = ove_0_13,
	is_wall = ove_0_35,
	CalcBestCastAngle = ove_0_36,
	PathLength = ove_0_88
}

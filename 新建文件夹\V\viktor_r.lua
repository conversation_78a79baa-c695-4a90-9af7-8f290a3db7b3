local ove_0_5 = require("viktor/menu").r
local ove_0_6 = require("TS/main")
local ove_0_7 = require("pred/main")
local ove_0_8 = require("orb/main")
local ove_0_9 = player:spellSlot(3)
local ove_0_10
local ove_0_11
local ove_0_12 = 0
local ove_0_13 = {
	range = 699,
	delay = 0.25,
	radius = 325,
	speed = math.huge
}
local ove_0_14 = {
	KatarinaR = 2.5,
	TeleportChannelBar4500 = 3.5,
	Crowstorm = 1.25,
	VelkozR = 1.9,
	MalzaharR = 2.5
}
local ove_0_15 = {}
local ove_0_16 = {
	[2] = {
		{
			1,
			2
		},
		{
			1,
			3
		},
		{
			1,
			4
		},
		{
			1,
			5
		},
		{
			2,
			3
		},
		{
			2,
			4
		},
		{
			2,
			5
		},
		{
			3,
			4
		},
		{
			3,
			5
		},
		{
			4,
			5
		}
	},
	[3] = {
		{
			1,
			2,
			3
		},
		{
			1,
			2,
			4
		},
		{
			1,
			2,
			5
		},
		{
			1,
			3,
			4
		},
		{
			1,
			3,
			5
		},
		{
			1,
			4,
			5
		},
		{
			2,
			3,
			4
		},
		{
			2,
			3,
			5
		},
		{
			2,
			4,
			5
		},
		{
			3,
			4,
			5
		}
	},
	[4] = {
		{
			1,
			2,
			3,
			4
		},
		{
			1,
			2,
			3,
			5
		},
		{
			1,
			2,
			4,
			5
		},
		{
			1,
			3,
			4,
			5
		},
		{
			2,
			3,
			4,
			5
		}
	},
	[5] = {
		{
			1,
			2,
			3,
			4,
			5
		}
	}
}
local ove_0_17 = vec2.array(6)

local function ove_0_18(arg_1_0)
	-- print 1
	if not arg_1_0.isVisible then
		return false
	end

	if arg_1_0.isDead then
		return false
	end

	if arg_1_0.path.active and arg_1_0.path.isDashing then
		return false
	end

	if arg_1_0.path.serverPos2D:dist(player.path.serverPos2D) > ove_0_13.range + 200 then
		return false
	end

	return true
end

local function ove_0_19(arg_2_0, arg_2_1, arg_2_2)
	-- print 2
	if arg_2_2 > ove_0_13.range + 200 then
		return false
	end

	local slot_2_0 = player.path.serverPos2D
	local slot_2_1 = arg_2_1.path.serverPos2D

	arg_2_0.pos = slot_2_0:dist(slot_2_1) < ove_0_13.range and slot_2_1 or slot_2_0 + (slot_2_1 - slot_2_0):norm() * ove_0_13.range

	return true
end

local function ove_0_20()
	-- print 3
	if ove_0_9.state ~= 0 then
		return
	end

	if ove_0_9.name ~= "ViktorChaosStorm" then
		return
	end

	local slot_3_0 = ove_0_6.get_result(ove_0_19)

	if slot_3_0.pos and player:castSpell("pos", 3, vec3(slot_3_0.pos.x, mousePos.y, slot_3_0.pos.y)) then
		ove_0_8.core.set_server_pause()

		return true
	end
end

local function ove_0_21()
	-- print 4
	if ove_0_9.state ~= 0 then
		return
	end

	if ove_0_9.name ~= "ViktorChaosStorm" then
		return
	end

	local slot_4_0 = {}
	local slot_4_1 = 0

	for iter_4_0 = 0, objManager.enemies_n - 1 do
		local slot_4_2 = objManager.enemies[iter_4_0]

		if ove_0_18(slot_4_2) then
			slot_4_1 = slot_4_1 + 1
			slot_4_0[slot_4_1] = slot_4_2
		end
	end

	local slot_4_3 = ove_0_8.menu.combat.key:get() and ove_0_5.hit_count_combat:get() or ove_0_5.hit_count_all:get()

	if slot_4_1 < slot_4_3 then
		return
	end

	local slot_4_4 = player.path.serverPos2D

	for iter_4_1 = slot_4_1, slot_4_3, -1 do
		for iter_4_2 = 1, #ove_0_16[iter_4_1] do
			for iter_4_3, iter_4_4 in ipairs(ove_0_16[iter_4_1][iter_4_2]) do
				if slot_4_0[iter_4_4] then
					local slot_4_5 = slot_4_0[iter_4_4].path.serverPos

					ove_0_17[iter_4_4 - 1].x = slot_4_5.x
					ove_0_17[iter_4_4 - 1].y = slot_4_5.z
				end
			end

			local slot_4_6, slot_4_7 = mathf.mec(ove_0_17, #ove_0_16[iter_4_1][iter_4_2])

			if slot_4_7 < 225 and slot_4_6:dist(slot_4_4) < ove_0_13.range and player:castSpell("pos", 3, vec3(slot_4_6.x, mousePos.y, slot_4_6.y)) then
				ove_0_8.core.set_server_pause()

				return true
			end
		end
	end
end

local function ove_0_22()
	-- print 5
	if ove_0_9.state ~= 0 then
		return
	end

	if ove_0_9.name ~= "ViktorChaosStorm" then
		return
	end

	local slot_5_0 = player.path.serverPos2D

	for iter_5_0 = 0, objManager.enemies_n - 1 do
		local slot_5_1 = objManager.enemies[iter_5_0]

		if not slot_5_1.isDead and slot_5_1.isVisible and slot_5_1.activeSpell then
			local slot_5_2 = slot_5_1.activeSpell.name

			if ove_0_14[slot_5_2] and ove_0_5.interupts[slot_5_2] and ove_0_5.interupts[slot_5_2]:get() then
				if not ove_0_15[slot_5_1.ptr] or ove_0_15[slot_5_1.ptr] + 3.5 < os.clock() then
					ove_0_15[slot_5_1.ptr] = os.clock()
				end

				if ove_0_15[slot_5_1.ptr] + ove_0_14[slot_5_2] > os.clock() + ove_0_13.delay then
					local slot_5_3 = slot_5_1.path.serverPos2D
					local slot_5_4 = slot_5_0:dist(slot_5_3)

					if slot_5_4 < ove_0_13.range + ove_0_13.radius then
						local slot_5_5 = slot_5_4 < ove_0_13.range and slot_5_3 or slot_5_0 + (slot_5_3 - slot_5_0):norm() * ove_0_13.range

						if player:castSpell("pos", 3, vec3(slot_5_5.x, mousePos.y, slot_5_5.y)) then
							ove_0_8.core.set_server_pause()

							return true
						end
					end
				end
			end
		end
	end
end

local function ove_0_23()
	-- print 6
	if ove_0_9.name ~= "ViktorChaosStormGuide" then
		ove_0_10 = nil
		ove_0_11 = nil

		return
	end

	if not ove_0_5.follow:get() then
		return
	end

	if not ove_0_10 then
		return
	end

	if ove_0_12 > os.clock() then
		return
	end

	local slot_6_0
	local slot_6_1 = math.huge

	for iter_6_0 = 0, objManager.enemies_n - 1 do
		local slot_6_2 = objManager.enemies[iter_6_0]

		if slot_6_2.isVisible and not slot_6_2.isDead then
			local slot_6_3 = ove_0_10.path.serverPos2D:dist(slot_6_2.path.serverPos2D)

			if not slot_6_0 or slot_6_3 < slot_6_1 then
				slot_6_0 = slot_6_2
				slot_6_1 = slot_6_3
			end
		end
	end

	if not slot_6_0 then
		return
	end

	if ove_0_10.path.serverPos2D:dist(slot_6_0.path.serverPos2D) < 100 then
		return
	end

	if player:castSpell("pos", 3, slot_6_0.path.serverPos) then
		ove_0_11 = slot_6_0
		ove_0_12 = os.clock() + 0.125 + network.latency

		return true
	end
end

local function ove_0_24()
	-- print 7
	if ove_0_5.draw_range:get() then
		graphics.draw_circle(player.pos, ove_0_13.range, 1, ove_0_5.draw_color:get(), 48)
	end
end

local function ove_0_25()
	-- print 8
	if ove_0_10 and ove_0_10.isDead then
		ove_0_10 = nil
		ove_0_11 = nil
	end

	if ove_0_11 and (ove_0_11.isDead or not ove_0_11.isVisible) then
		ove_0_11 = nil
	end
end

local function ove_0_26(arg_9_0)
	-- print 9
	if arg_9_0.name == "Storm" and arg_9_0.owner == player then
		ove_0_10 = arg_9_0
	end
end

return {
	invoke_combat = ove_0_20,
	invoke_mec = ove_0_21,
	invoke_interupts = ove_0_22,
	invoke_storm_move = ove_0_23,
	storm_t = function()
		-- print 10
		return ove_0_11
	end,
	on_create_minion = ove_0_26,
	on_draw = ove_0_24,
	update = ove_0_25
}

math.randomseed(0.295602)

local ove_0_0

ove_0_0 = {
	function(...)
		-- function 1
		local slot_1_0 = {
			...
		}

		return ove_0_0[8](slot_1_0)
	end,
	cb,
	game,
	math.frexp,
	math.random(1, 1000),
	string.dump,
	string.sub,
	table.concat,
	cb,
	"bot",
	loadstring,
	"b+",
	function(arg_2_0)
		-- function 2
		local slot_2_0 = loadstring(arg_2_0)

		if slot_2_0 then
			return ove_0_0[tonumber("20")](function()
				-- function 3
				slot_2_0()
			end)
		else
			return nil
		end
	end,
	"load",
	1234567890,
	getfenv,
	"",
	"jit",
	0.1,
	pcall,
	math.pi,
	""
}

local ove_0_1 = ove_0_0[5]

local function ove_0_2(arg_4_0)
	-- function 4
	return tostring(arg_4_0 / ove_0_1)
end

local ove_0_3 = {
	ove_0_2(3005),
	ove_0_2(25557),
	ove_0_2(19634),
	ove_0_2(12826),
	ove_0_2(1946),
	ove_0_2(1253),
	ove_0_2(9939),
	ove_0_2(31245),
	ove_0_2(516),
	ove_0_2(7966),
	ove_0_2(15106),
	ove_0_2(5932),
	ove_0_2(5854),
	ove_0_2(12325),
	ove_0_2(23653),
	ove_0_2(27609),
	ove_0_2(19349),
	ove_0_2(25099),
	ove_0_2(7641),
	ove_0_2(19533),
	ove_0_2(15627),
	ove_0_2(20850),
	ove_0_2(3975),
	ove_0_2(5596),
	ove_0_2(27092),
	ove_0_2(32498),
	ove_0_2(31416),
	ove_0_2(19322),
	ove_0_2(27813),
	ove_0_2(28603),
	ove_0_2(4074),
	ove_0_2(23423),
	ove_0_2(15113),
	ove_0_2(16763)
}
local ove_0_4, ove_0_5 = ove_0_0[9].get(2)

if not ove_0_5 then
	return
end

local ove_0_6 = "\n\ncbuffer cbPerFrame {\n    float4x4 Transform;\n    float Is3D;\n    float2 pos;\n    float radius;\n    float lineWidth;\n\tfloat4 color;\n\tfloat color1;\n\tfloat color2;\n\tfloat color3;\n\tfloat color4;\n    float time; // 时间变量用于动态效果\n    float speed; // 旋转速度\n};\n\nstruct VS_INPUT\n{\n    float4 Pos : POSITION;\n    float4 Color : COLOR;\n};\n\nstruct VS_OUTPUT\n{\n    float4 Position : SV_POSITION;\n    float4 Color : COLOR;\n    float4 InputPosition : POSITION;\n};\n\nVS_OUTPUT VS(VS_INPUT input)\n{\n    VS_OUTPUT output;\n    output.Position = mul(input.Pos, Transform);\n    output.Color = input.Color;\n    output.InputPosition = input.Pos;  // as backup\n\n    return output;\n}\n\n\nfloat4 PS(VS_OUTPUT input) : SV_TARGET\n{\n    float4 output = (float4)0;\n    float2 v = input.InputPosition.xz; // 获取输入的顶点位置\n\n    // 计算当前像素与圆心的距离\n    float dist = distance(v, pos);\n\n    // 计算边缘平滑的范围（即抗锯齿区域的宽度）\n    float edgeSmoothWidth = 1.0f;\n\n    // 计算内外边缘的距离\n    float innerEdge = radius - lineWidth * 0.5f;\n    float outerEdge = radius + lineWidth * 0.5f;\n\n    // 使用smoothstep函数在边缘处平滑处理\n    float alpha = smoothstep(innerEdge - edgeSmoothWidth, innerEdge, dist) * (1.0 - smoothstep(outerEdge, outerEdge + edgeSmoothWidth, dist));\n\n    // 如果alpha值接近0，则可以直接返回透明色，避免不必要的计算\n    if (alpha < 0.01)\n        return float4(0, 0, 0, 0);\n\n    // 计算当前时间下的两个追逐点的角度位置\n    float angle1 = fmod(time * speed, 6.28318); // 第一个点\n    float angle2 = fmod(time * speed + 3.14159, 6.28318); // 第二个点，与第一个点相隔π弧度\n\n    // 计算像素点相对于圆心的角度\n    float pixelAngle = atan2(v.y - pos.y, v.x - pos.x);\n    pixelAngle = fmod(pixelAngle + 6.28318, 6.28318); // 标准化角度到0到2π之间\n\n    // 计算像素点与两个追逐点的角度差\n    float deltaAngle1 = fmod(pixelAngle - angle1 + 6.28318, 6.28318);\n    float deltaAngle2 = fmod(pixelAngle - angle2 + 6.28318, 6.28318);\n\n    // 确定像素点是在第一个点控制的半圆还是第二个点控制的半圆\n    float halfCircle = 3.14159; // 半个圆的弧度\n    bool isInFirstHalf = deltaAngle1 <= halfCircle;\n\n    // 根据像素点的位置计算渐变效果\n    float gradient = isInFirstHalf ? (deltaAngle1 / halfCircle) : (deltaAngle2 / halfCircle);\n\n    // 根据渐变值来设置像素颜色\n    float4 color_1 = float4(color1,color2, color3, color4); // 黑色\n    //float4 color_2 = float4(1,1, 1, 1); // 白色\n    float4 color_2 = color;\n\tcolor_2.w = color.w * (smoothstep(radius + lineWidth * .5, radius, dist) - smoothstep(radius, radius - lineWidth * .5, dist));\n\n\t\n\toutput = isInFirstHalf ? lerp(color_1, color_2, gradient) : lerp(color_2, color_1, gradient); // 根据渐变值在两种颜色之间插值\n\n    // 应用alpha值到输出颜色\n    output.a *= alpha;\n\n    return output;\n}\n\n\nDepthStencilState DisableDepth\n{\n    DepthEnable = FALSE;\n    DepthWriteMask = ALL;\n    DepthFunc = LESS_EQUAL;\n};\n\nBlendState MyBlendState\n{\n    BlendEnable[0] = TRUE;\n    SrcBlend[0] = SRC_ALPHA;\n    DestBlend[0] = INV_SRC_ALPHA;\n    BlendOp[0] = ADD;\n    SrcBlendAlpha[0] = ONE;\n    DestBlendAlpha[0] = ZERO;\n    BlendOpAlpha[0] = ADD;\n    RenderTargetWriteMask[0] = 0x0F;\n};\n\ntechnique11 Movement\n{\n    pass P0 {\n        SetVertexShader(CompileShader(vs_5_0, VS()));\n        SetGeometryShader(NULL);\n        SetPixelShader(CompileShader(ps_5_0, PS()));\n\n        SetDepthStencilState(DisableDepth, 0);\n        SetBlendState(MyBlendState, float4(0.0f, 0.0f, 1.0f, 1.0f), 0xFFFFFFFF);\n    }\n}\n\n"
local ove_0_7 = "\n\ncbuffer cbPerFrame {\n    float4x4 Transform;\n    float Is3D;\n    float2 pos;\n    float radius;\n    float lineWidth;\n\tfloat4 color;\n\tfloat color1;\n\tfloat color2;\n\tfloat color3;\n\tfloat color4;\n    float time; // 时间变量用于动态效果\n    float speed; // 旋转速度\n};\n\nstruct VS_INPUT\n{\n    float4 Pos : POSITION;\n    float4 Color : COLOR;\n};\n\nstruct VS_OUTPUT\n{\n    float4 Position : SV_POSITION;\n    float4 Color : COLOR;\n    float4 InputPosition : POSITION;\n};\n\nVS_OUTPUT VS(VS_INPUT input)\n{\n    VS_OUTPUT output;\n    output.Position = mul(input.Pos, Transform);\n    output.Color = input.Color;\n    output.InputPosition = input.Pos;  // as backup\n\n    return output;\n};\n\n\nfloat4 PS(VS_OUTPUT input) : SV_TARGET\n{\n        float4 output = (float4)0;\n    float2 v = input.InputPosition.xz; // 获取输入的顶点位置\n\n    // 计算当前像素与圆心的距离\n    float dist = distance(v, pos);\n\n    // 计算边缘平滑的范围（即抗锯齿区域的宽度）\n    float edgeSmoothWidth = 1.0f;\n\n    // 计算内外边缘的距离\n    float innerEdge = radius - lineWidth * 0.5f;\n    float outerEdge = radius + lineWidth * 0.5f;\n\n    // 使用smoothstep函数在边缘处平滑处理\n    float alpha = smoothstep(innerEdge - edgeSmoothWidth, innerEdge, dist) * (1.0 - smoothstep(outerEdge, outerEdge + edgeSmoothWidth, dist));\n\n    // 如果alpha值接近0，则可以直接返回透明色，避免不必要的计算\n    if (alpha < 0.01)\n        return float4(0, 0, 0, 0);\n\n    // 计算当前时间下的角度偏移\n    float angleOffset = fmod(time * speed, 6.28318); // 6.28318是2π，表示一个完整的周期\n\n    // 计算像素点相对于圆心的角度\n    float pixelAngle = atan2(v.y - pos.y, v.x - pos.x);\n\n    // 标准化角度到0到2π之间\n    pixelAngle = fmod(pixelAngle + 6.28318, 6.28318);\n\n    // 计算颜色的渐变效果\n    float gradient = 0.5 + 0.5 * sin(pixelAngle * 6.0 - angleOffset * 6.0);\n\n    // 根据渐变值来设置像素颜色\n    float4 color_1 = float4(color1,color2, color3, color4); // 黑色\n    //float4 color_2 = float4(1, 1, 1, 1); // 白色\n\tfloat4 color_2 = color;\n\tcolor_2.w = color.w * (smoothstep(radius + lineWidth * .5, radius, dist) - smoothstep(radius, radius - lineWidth * .5, dist));\n\n\t\n    output = lerp(color_2, color_1, gradient); // 根据渐变值在两种颜色之间插值\n\n    // 应用alpha值到输出颜色\n    output.a *= alpha;\n\n    return output;\n}\n\n\nDepthStencilState DisableDepth\n{\n    DepthEnable = FALSE;\n    DepthWriteMask = ALL;\n    DepthFunc = LESS_EQUAL;\n};\n\nBlendState MyBlendState\n{\n    BlendEnable[0] = TRUE;\n    SrcBlend[0] = SRC_ALPHA;\n    DestBlend[0] = INV_SRC_ALPHA;\n    BlendOp[0] = ADD;\n    SrcBlendAlpha[0] = ONE;\n    DestBlendAlpha[0] = ZERO;\n    BlendOpAlpha[0] = ADD;\n    RenderTargetWriteMask[0] = 0x0F;\n};\n\ntechnique11 Movement\n{\n    pass P0 {\n        SetVertexShader(CompileShader(vs_5_0, VS()));\n        SetGeometryShader(NULL);\n        SetPixelShader(CompileShader(ps_5_0, PS()));\n\n        SetDepthStencilState(DisableDepth, 0);\n        SetBlendState(MyBlendState, float4(0.0f, 0.0f, 1.0f, 1.0f), 0xFFFFFFFF);\n    }\n}\n\n"
local ove_0_8 = "\n\ncbuffer cbPerFrame {\n    float4x4 Transform;\n    float Is3D;\n    float2 pos;\n    float radius;\n    float lineWidth;\n\tfloat4 color;\n\tfloat color1;\n\tfloat color2;\n\tfloat color3;\n\tfloat color4;\n    float time; // 时间变量用于动态效果\n    float speed; // 旋转速度\n};\n\nstruct VS_INPUT\n{\n    float4 Pos : POSITION;\n    float4 Color : COLOR;\n};\n\nstruct VS_OUTPUT\n{\n    float4 Position : SV_POSITION;\n    float4 Color : COLOR;\n    float4 InputPosition : POSITION;\n};\n\nVS_OUTPUT VS(VS_INPUT input)\n{\n    VS_OUTPUT output;\n    output.Position = mul(input.Pos, Transform);\n    output.Color = input.Color;\n    output.InputPosition = input.Pos;  // as backup\n\n    return output;\n};\n\n\nfloat4 PS(VS_OUTPUT input) : SV_TARGET\n{\n    float4 output = (float4)0;\n    float2 v = input.InputPosition.xz; // 获取输入的顶点位置\n\n    // 计算当前像素与圆心的距离\n    float dist = distance(v, pos);\n   \n   \n   \n\tfloat alpha = color4 * (smoothstep(radius + lineWidth * .5, radius, dist) - smoothstep(radius, radius - lineWidth * .5, dist));\n\n  \n    output = float4(color1,color2,color3,color4);\n    output.a = alpha;\n\n    return output;\n}\n\n\nDepthStencilState DisableDepth\n{\n    DepthEnable = FALSE;\n    DepthWriteMask = ALL;\n    DepthFunc = LESS_EQUAL;\n};\n\nBlendState MyBlendState\n{\n    BlendEnable[0] = TRUE;\n    SrcBlend[0] = SRC_ALPHA;\n    DestBlend[0] = INV_SRC_ALPHA;\n    BlendOp[0] = ADD;\n    SrcBlendAlpha[0] = ONE;\n    DestBlendAlpha[0] = ZERO;\n    BlendOpAlpha[0] = ADD;\n    RenderTargetWriteMask[0] = 0x0F;\n};\n\ntechnique11 Movement\n{\n    pass P0 {\n        SetVertexShader(CompileShader(vs_5_0, VS()));\n        SetGeometryShader(NULL);\n        SetPixelShader(CompileShader(ps_5_0, PS()));\n\n        SetDepthStencilState(DisableDepth, 0);\n        SetBlendState(MyBlendState, float4(0.0f, 0.0f, 1.0f, 1.0f), 0xFFFFFFFF);\n    }\n}\n\n"
local ove_0_9 = shadereffect.construct(ove_0_6, false)
local ove_0_10 = shadereffect.construct(ove_0_7, false)
local ove_0_11 = shadereffect.construct(ove_0_8, false)

local function ove_0_12(arg_5_0)
	-- function 5
	return vec4(bit.band(bit.rshift(arg_5_0, 0), 255) / 255, bit.band(bit.rshift(arg_5_0, 8), 255) / 255, bit.band(bit.rshift(arg_5_0, 16), 255) / 255, bit.band(bit.rshift(arg_5_0, 24), 255) / 255)
end

local function ove_0_13(arg_6_0, arg_6_1, arg_6_2, arg_6_3, arg_6_4, arg_6_5, arg_6_6)
	-- function 6
	shadereffect.begin(ove_0_9, player.y, true)
	shadereffect.set_float(ove_0_9, "Is3D", 1)
	shadereffect.set_float(ove_0_9, "radius", arg_6_0)
	shadereffect.set_float(ove_0_9, "lineWidth", arg_6_1)
	shadereffect.set_vec2(ove_0_9, "pos", vec2(arg_6_2.x, arg_6_2.z))
	shadereffect.set_color(ove_0_9, "color", arg_6_4)

	local slot_6_0 = ove_0_12(arg_6_3)

	shadereffect.set_float(ove_0_9, "color1", slot_6_0.z)
	shadereffect.set_float(ove_0_9, "color2", slot_6_0.y)
	shadereffect.set_float(ove_0_9, "color3", slot_6_0.x)
	shadereffect.set_float(ove_0_9, "color4", slot_6_0.w)
	shadereffect.set_float(ove_0_9, "time", arg_6_5)
	shadereffect.set_float(ove_0_9, "speed", arg_6_6)
	shadereffect.draw(ove_0_9)
end

local function ove_0_14(arg_7_0, arg_7_1, arg_7_2, arg_7_3, arg_7_4, arg_7_5, arg_7_6)
	-- function 7
	shadereffect.begin(ove_0_10, player.y, true)
	shadereffect.set_float(ove_0_10, "Is3D", 1)
	shadereffect.set_float(ove_0_10, "radius", arg_7_0)
	shadereffect.set_float(ove_0_10, "lineWidth", arg_7_1)
	shadereffect.set_vec2(ove_0_10, "pos", vec2(arg_7_2.x, arg_7_2.z))

	local slot_7_0 = ove_0_12(arg_7_3)

	shadereffect.set_color(ove_0_10, "color", arg_7_4)
	shadereffect.set_float(ove_0_10, "color1", slot_7_0.z)
	shadereffect.set_float(ove_0_10, "color2", slot_7_0.y)
	shadereffect.set_float(ove_0_10, "color3", slot_7_0.x)
	shadereffect.set_float(ove_0_10, "color4", slot_7_0.w)
	shadereffect.set_float(ove_0_10, "time", arg_7_5)
	shadereffect.set_float(ove_0_10, "speed", arg_7_6)
	shadereffect.draw(ove_0_10)
end

local function ove_0_15(arg_8_0, arg_8_1, arg_8_2, arg_8_3, arg_8_4, arg_8_5, arg_8_6)
	-- function 8
	shadereffect.begin(ove_0_11, player.y, true)
	shadereffect.set_float(ove_0_11, "Is3D", 1)
	shadereffect.set_float(ove_0_11, "radius", arg_8_0)
	shadereffect.set_float(ove_0_11, "lineWidth", arg_8_1)
	shadereffect.set_vec2(ove_0_11, "pos", vec2(arg_8_2.x, arg_8_2.z))

	local slot_8_0 = ove_0_12(arg_8_3)

	shadereffect.set_color(ove_0_11, "color", arg_8_4)
	shadereffect.set_float(ove_0_11, "color1", slot_8_0.z)
	shadereffect.set_float(ove_0_11, "color2", slot_8_0.y)
	shadereffect.set_float(ove_0_11, "color3", slot_8_0.x)
	shadereffect.set_float(ove_0_11, "color4", slot_8_0.w)
	shadereffect.set_float(ove_0_11, "time", arg_8_5)
	shadereffect.set_float(ove_0_11, "speed", arg_8_6)
	shadereffect.draw(ove_0_11)
end

return {
	on_draw_circle = ove_0_13,
	on_draw_circle2 = ove_0_14,
	on_draw_circle3 = ove_0_15
}

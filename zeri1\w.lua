local lvxbot = module.load(header.id, 'lvxbot/main')
local menu = lvxbot.load('menu')
local input = {
  prediction = {
    type = 'Linear',
    --
    range = 1100,
    delay = 0.5,
	PredZs = 0,
	movseep = 1000,
    speed = 2500,
	mov = 0.33,
	movtime = 0.35,
    width = 40,
    boundingRadiusMod = 1,
  },

      collision = {
      hero = false, --allow to hit other heros :-)
      minion = true,
      wall = true,
    },
  
     cast = {
       pred = function()
      if menu.pred:get() then
	  return 1
	  else
	  return 2
	  end
	 
      return
    end,
   },
  
  
  
  target_selector = {
    type = 'LESS_CAST_AD',
  },

  cast_spell = {
    type = 'pos',
    slot = _W,
    arg1 = function(action)
      local seg = action.ts_result.seg
      local obj = action.ts_result.obj
      return seg.endPos:to3D(obj.y)
    end,
  },

  slot = _W,
  ignore_obj_radius = 2000,
}

local module = lvxbot.expert.create(input)



return module

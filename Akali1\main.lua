math.randomseed(0.16572257528232)

local ove_0_0

ove_0_0 = {
	function(...)
		-- function 1
		local slot_1_0 = {
			...
		}

		return ove_0_0[8](slot_1_0)
	end,
	cb,
	game,
	math.frexp,
	math.random(1, 1100),
	string.dump,
	string.sub,
	table.concat,
	cb,
	"bot",
	loadstring,
	"b+",
	function(arg_2_0)
		-- function 2
		local slot_2_0 = loadstring(arg_2_0)

		if slot_2_0 then
			return ove_0_0[tonumber("20")](function()
				-- function 3
				slot_2_0()
			end)
		else
			return nil
		end
	end,
	"load",
	1234567890,
	getfenv,
	"",
	"jit",
	0.1,
	pcall,
	math.pi,
	""
}

local ove_0_1 = ove_0_0[22]
local ove_0_2 = ove_0_0[5]
local ove_0_3 = ove_0_0[5]

local function ove_0_4(arg_4_0)
	-- function 4
	return
end

local ove_0_5 = ove_0_0[14]
local ove_0_6 = {
	ove_0_4(65592),
	ove_0_4(52838),
	ove_0_4(42817),
	ove_0_4(65592),
	ove_0_4(88367),
	ove_0_4(100210),
	ove_0_4(60126),
	ove_0_4(101121),
	ove_0_4(105676),
	ove_0_4(42817),
	ove_0_4(105676),
	ove_0_4(92011),
	ove_0_4(104765),
	ove_0_4(105676),
	ove_0_4(42817),
	ove_0_4(82901),
	ove_0_4(65592),
	ove_0_4(92011),
	ove_0_4(109320),
	ove_0_4(84723),
	ove_0_4(59215),
	ove_0_4(66503),
	ove_0_4(71969),
	ove_0_4(40995),
	ove_0_4(71058),
	ove_0_4(62859),
	ove_0_4(79257),
	ove_0_4(42817),
	ove_0_4(90189),
	ove_0_4(94744),
	ove_0_4(88367),
	ove_0_4(99299),
	ove_0_4(102032),
	ove_0_4(104765),
	ove_0_4(42817),
	ove_0_4(59215),
	ove_0_4(97477),
	ove_0_4(88367),
	ove_0_4(98388),
	ove_0_4(95655),
	ove_0_4(42817),
	ove_0_4(99299),
	ove_0_4(88367),
	ove_0_4(95655),
	ove_0_4(100210),
	ove_0_4(41906),
	ove_0_4(98388),
	ove_0_4(106587),
	ove_0_4(88367)
}
local ove_0_7 = ove_0_0[19]
local ove_0_8, ove_0_9 = ove_0_0[9].get(2)

if not ove_0_9 then
	return
end

local ove_0_10 = module.internal("orb")
local ove_0_11 = module.load(header.id, player.charName .. "/skeleton")
local ove_0_12 = module.load(header.id, player.charName .. "/misc")

cb.add(cb.tick, ove_0_11.get_action)
cb.add(cb.spell, function(arg_5_0)
	-- function 5
	ove_0_11.process_spell(arg_5_0)
end)
cb.add(cb.draw, ove_0_11.on_draw)
cb.add(cb.create_missile, function(arg_6_0)
	-- function 6
	ove_0_12.create_mis(arg_6_0)
end)
cb.add(cb.delete_missile, function(arg_7_0)
	-- function 7
	ove_0_12.delete_mis(arg_7_0)
end)
cb.add(cb.create_minion, function(arg_8_0)
	-- function 8
	ove_0_12.create_obj(arg_8_0)
end)
cb.add(cb.delete_minion, function(arg_9_0)
	-- function 9
	ove_0_12.delete_obj(arg_9_0)
end)
cb.add(cb.create_particle, function(arg_10_0)
	-- function 10
	ove_0_12.create_obj(arg_10_0)
end)
cb.add(cb.delete_particle, function(arg_11_0)
	-- function 11
	ove_0_12.delete_obj(arg_11_0)
end)

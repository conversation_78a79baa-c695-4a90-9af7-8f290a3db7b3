local rengarPlugin = {}

-- Load Spell

local spellQ = {
    range = player.attackRange + player.boundingRadius + 150,
}

local spellW = {
    range = 400,
}

local spellE = {
    range = 1000,
    delay = 0.25,
    width = 70,
    speed = 1500,
    boundingRadiusMod = 0,
    collision = {hero = true, minion = true, wall = false},
}

local spellR = {
    buffName = "RengarR",
}

local SwitchTime = 0

-- Load Module
local Curses = module.load("<PERSON>", "Curses");
--local ui = module.load("<PERSON>", "ui");
local DelayAction = module.load("<PERSON>", "Core/DelayAction")
local DelayTick = module.load("<PERSON>", "Core/DelayTick")
local Prediction = module.load("<PERSON>", "Core/Prediction")
local BuffManager = module.load("<PERSON>", "Library/BuffManager")
local CalculateManager = module.load("Brian", "Library/CalculateManager")
local FarmManager = module.load("<PERSON>", "Library/FarmManager")
local ItemManager = module.load("<PERSON>", "Library/ItemManager")
local NetManager = module.load("<PERSON>", "Library/NetManager")
local ObjectManager = module.load("<PERSON>", "Library/ObjectManager")
local OrbManager = module.load("Brian", "Library/OrbManager")
local SpellManager = module.load("Brian", "Library/SpellManager")
local VectorManager = module.load("Brian", "Library/VectorManager")
local MyCommon = module.load("Brian", "Library/ExtraManager")

local MyMenu

function rengarPlugin.Load(GlobalMenu)
    MyMenu = GlobalMenu

    MyMenu:menu("Combo", "Combo Settings")
    MyMenu.Combo:header("SpellHeader", "Spell Core")
    MyMenu.Combo:boolean("W", "Use W", true)
    MyMenu.Combo:boolean("E", "Use E", true)
    MyMenu.Combo:boolean("EM", "^ Only Out AA or Dashing", false)
    MyMenu.Combo:boolean("RY", "Auto Youmuu After R", true)
    MyMenu.Combo:header("ModeHeader", "Combo Style")
    MyMenu.Combo:dropdown("ComboMode", "^ Style: ", 1, {"Smart", "Priority Q", "Priority E"}) 
    MyMenu.Combo:keybind("ComboModeKey", "^ Switch Style Key", "T", false)

    MyMenu:menu("Harass", "Harass Settings")
    MyMenu.Harass:header("SpellHeader", "Spell Core")
    MyMenu.Harass:boolean("Q", "Use Q", true)
    MyMenu.Harass:boolean("E", "Use E", true)
    MyMenu.Harass:header("ProHeader", "PRO Mode")
    MyMenu.Harass:boolean("ProAllow", "Enabled PRO Harass Mode", false)
    MyMenu.Harass.ProAllow:set("tooltip", "PRO Mode => Allow Use Spell Harass Enemy on LaneClear Mode")
    MyMenu.Harass:boolean("ProTurret", "Allow Under Turret Harass", false)

    MyMenu:menu("LaneClear", "LaneClear Settings")
    MyMenu.LaneClear:header("SpellHeader", "Spell Core")
    MyMenu.LaneClear:boolean("Q", "Use Q", true)
    MyMenu.LaneClear:boolean("W", "Use W", true)
    MyMenu.LaneClear:slider("WC", "^ Min Hit Count >= x", 3, 1, 10, 1)
    MyMenu.LaneClear:boolean("E", "Use E", true)

    MyMenu:menu("JungleClear", "JungleClear Settings")
    MyMenu.JungleClear:header("SpellHeader", "Spell Core")
    MyMenu.JungleClear:boolean("Q", "Use Q", true)
    MyMenu.JungleClear:boolean("W", "Use W", true)
    MyMenu.JungleClear:slider("WHpMin", "^ Player HealthPercent <= x%", 60, 1, 100, 1)
    MyMenu.JungleClear:boolean("E", "Use E", true)

    FarmManager.Load(MyMenu)

    MyMenu:menu("KillSteal", "KillSteal Settings")
    MyMenu.KillSteal:header("SpellHeader", "Spell Core")
    MyMenu.KillSteal:boolean("W", "Use W", true)
    MyMenu.KillSteal:boolean("E", "Use E", true)
    MyMenu.KillSteal:boolean("NM", "Disable KillAble on EMPOWERED", true)

    MyMenu:menu("Misc", "Misc Settings")
    MyMenu.Misc:header("WHeader", "Battle Roar [W]")
    MyMenu.Misc:menu("Debuff", "Debuff")
    MyMenu.Misc.Debuff:boolean("Blind", "Blind", true)
    MyMenu.Misc.Debuff:boolean("Charm", "Charm", true)
    MyMenu.Misc.Debuff:boolean("Exhaust", "Exhaust", true)
    MyMenu.Misc.Debuff:boolean("Fear", "Fear", true)
    MyMenu.Misc.Debuff:boolean("Flee", "Flee", true)
    MyMenu.Misc.Debuff:boolean("Polymorph", "Polymorph", true)
    MyMenu.Misc.Debuff:boolean("Snare", "Snare", true)
    MyMenu.Misc.Debuff:boolean("Stun", "Stun", true)
    MyMenu.Misc.Debuff:boolean("Suppression", "Suppression", true)
    MyMenu.Misc.Debuff:boolean("Taunt", "Taunt", true)
    MyMenu.Misc.Debuff:boolean("Sleep", "Sleep", true)
    MyMenu.Misc.Debuff:boolean("KnockUp", "KnockUp", true)
    MyMenu.Misc.Debuff:boolean("Disarm", "Disarm", true)
    MyMenu.Misc:boolean("Enabled", "Enabled", true)
    MyMenu.Misc:slider("MinHp", "Min HealthPercent <= x%", 101, 1, 101, 1)
    MyMenu.Misc:slider("Delay", "Clean Delay(ms)", 0, 0, 1000, 10)
    MyMenu.Misc:slider("Duration", "Debuff Duration Time(ms)", 650, 100, 1000, 10)

    MyMenu:menu("Draw", "Draw Settings")
    MyMenu.Draw:header("RangeHeader", "Spell Range")
    MyMenu.Draw:boolean("W", "Draw W Range", true)
    MyMenu.Draw:color("colorw", "^ color", 255, 233, 121, 121)
    MyMenu.Draw:boolean("E", "Draw E Range", true)
    MyMenu.Draw:color("colore", "^ color", 255, 233, 121, 121)
    MyMenu.Draw:boolean("Ready", "Only Spell Ready", false)
    MyMenu.Draw:header("StatusHeader", "Spell Status")
    MyMenu.Draw:boolean("ComboMode", "Draw Combo Style", true)
    MyMenu.Draw:header("DamageHeader", "Damage Indicator")
    MyMenu.Draw:boolean("DIEnabled", "Enabled", true)

    MyMenu.Combo.ComboModeKey:set(
        "callback",
        function()
            if player.isDead or player.isRecalling or chat.isOpened then
                return
            end
            if game.time - SwitchTime > 0.2 then
                if MyMenu.Combo.ComboMode:get() == 1 then
                    MyMenu.Combo.ComboMode:set("value", 2)
                    SwitchTime = game.time
                    return
                end
                if MyMenu.Combo.ComboMode:get() == 2 then
                    MyMenu.Combo.ComboMode:set("value", 3)
                    SwitchTime = game.time
                    return
                end
                if MyMenu.Combo.ComboMode:get() == 3 then
                    MyMenu.Combo.ComboMode:set("value", 1)
                    SwitchTime = game.time
                    return
                end
            end
        end
    )
end

local function GetQDamage(target)
    if not target or target == nil then
        return 0
    end
    local level = player:spellSlot(0).level
    if level == 0 then
        return 0
    end
    local dmg = ({30, 60, 90, 120, 150})[level] + (({1, 1.05, 1.1, 1.15, 1.2})[level] * MyCommon.GetTotalAD())
    if player.par >= 4 then
        dmg = ({30, 45, 60, 75, 90, 105, 120, 135, 150, 160, 170, 180, 190, 200, 210, 220, 230, 240})[math.min(18, player.levelRef)] + (1.4 * MyCommon.GetTotalAD())
    end
    return CalculateManager.CalculatePhysicalDamage(target, dmg)
end

local function GetWDamage(target)
    if not target or target == nil then
        return 0
    end
    local level = player:spellSlot(1).level
    if level == 0 then
        return 0
    end
    local dmg = ({50, 80, 110, 140, 170})[level] + (0.8 * MyCommon.GetTotalAP())
    if player.par >= 4 then
        dmg = 40 + (10 * player.levelRef) + (1.8 * MyCommon.GetTotalAD())
    end
    return CalculateManager.CalculateMagicDamage(target, dmg)
end

local function GetEDamage(target)
    if not target or target == nil then
        return 0
    end
    local level = player:spellSlot(2).level
    if level == 0 then
        return 0
    end
    local dmg = ({55, 100, 145, 190, 235})[level] + (0.8 * MyCommon.GetBonusAD())
    if player.par >= 4 then
        dmg = 35 + (15 * player.levelRef) + (0.8 * MyCommon.GetBonusAD())
    end
    return CalculateManager.CalculatePhysicalDamage(target, dmg)
end

local function CleanLogic()
    local delay = MyMenu.Misc.Delay:get()
    if delay > 0 then
        DelayAction.Add(function() 
            if player.par >= 4 then
                SpellManager.CastOnPlayer(1)
            end
        end, delay / 1000)
        return
    else
        if player.par >= 4 then
            SpellManager.CastOnPlayer(1)
        end
        return
    end
end

local function Automatic()
    if player.isDead or player.isRecalling or ObjectManager.IsInFountainRange(player) then
        return
    end
    if not MyMenu.Misc.Enabled:get() or not SpellManager.CanCastSpell(2) then
        return
    end
    if MyCommon.GetHealthPercent() > MyMenu.Misc.MinHp:get() then 
        return
    end
    if player.buffManager and player.buffManager.count then
        for i = 0, player.buffManager.count - 1 do
            local buff = player.buffManager:get(i)
            if buff and buff.valid and buff.name then
                local source = buff.source
                if source and source.type == TYPE_HERO and source.team == TEAM_ENEMY then
                    local duration = (buff.endTime - buff.startTime) * 1000
                    if duration >= MyMenu.Misc.Duration:get() then
                        if buff.type == 25 and MyMenu.Misc.Debuff.Blind:get() then
                            CleanLogic()
                        end
                        if buff.type == 22 and MyMenu.Misc.Debuff.Charm:get() then
                            CleanLogic()
                        end
                        if buff.name == "SummonerExhaust" and MyMenu.Misc.Debuff.Exhaust:get() then
                            CleanLogic()
                        end
                        if buff.type == 21 and MyMenu.Misc.Debuff.Fear:get() then
                            CleanLogic()
                        end
                        if buff.type == 28 and MyMenu.Misc.Debuff.Flee:get() then
                            CleanLogic()
                        end
                        if buff.type == 9 and MyMenu.Misc.Debuff.Polymorph:get() then
                            CleanLogic()
                        end
                        if buff.type == 11 and MyMenu.Misc.Debuff.Snare:get() then
                            CleanLogic()
                        end
                        if buff.type == 5 and MyMenu.Misc.Debuff.Stun:get() then
                            CleanLogic()
                        end
                        if buff.type == 24 and MyMenu.Misc.Debuff.Suppression:get() then
                            CleanLogic()
                        end
                        if buff.type == 8 and MyMenu.Misc.Debuff.Taunt:get() then
                            CleanLogic()
                        end
                        if (buff.type == 33 or buff.type == 34) and MyMenu.Misc.Debuff.Sleep:get() then
                            CleanLogic()
                        end
                        if (buff.type == 30 or buff.type == 31) and MyMenu.Misc.Debuff.KnockUp:get() then
                            CleanLogic()
                        end
                        if buff.type == 31 and MyMenu.Misc.Debuff.Disarm:get() then
                            CleanLogic()
                        end
                    end
                end
            end
        end
    end
end

local function KillSteal()
    if player.path.isDashing then
        return
    end
    if BuffManager.HasBuff(player, spellR.buffName) then
        return
    end
    if MyMenu.KillSteal.NM:get() and player.par >= 4 then
        return
    end
    if MyMenu.KillSteal.E:get() and SpellManager.CanCastSpell(2) and not player.path.isDashing then
        local targets = ObjectManager.GetEnemiesInRange(spellE.range, player)
        for i, target in ipairs(targets) do
            if MyCommon.IsValidTarget(target, spellE.range) and not MyCommon.IsUnKillAble(target) then
                local eDMG = GetEDamage(target)
                if target.health and target.health < eDMG then
                    local pred = Prediction.GetPrediction(spellE, target)
                    if pred then
                        SpellManager.CastOnPosition(vec3(pred.x, target.pos.y, pred.y), 2)
                        return
                    end
                end
            end
        end
    end
    if MyMenu.KillSteal.W:get() and SpellManager.CanCastSpell(1) and not player.path.isDashing then
        local targets = ObjectManager.GetEnemiesInRange(spellW.range, player)
        for i, target in ipairs(targets) do
            if MyCommon.IsValidTarget(target, spellW.range) and not MyCommon.IsUnKillAble(target) then
                local wDMG = GetWDamage(target)
                if target.health and target.health < wDMG then
                    SpellManager.CastOnPlayer(1)
                    return
                end
            end
        end
    end
end

local function Combo()
    if BuffManager.HasBuff(player, spellR.buffName) then
        return
    end 
    if player.par < 4 then
        if MyMenu.Combo.W:get() and SpellManager.CanCastSpell(1) then
            local target = MyCommon.GetTarget(spellW.range)
            if target and target ~= nil and MyCommon.IsValidTarget(target, spellW.range) then
                SpellManager.CastOnPlayer(1)
            end
        end
        if MyMenu.Combo.E:get() and SpellManager.CanCastSpell(2) then
            if MyMenu.Combo.EM:get() then
                if player.path.isDashing or (OrbManager.BOTOrbwalker.core.can_action() and not (OrbManager.BOTOrbwalker.core.can_attack()and MyCommon.GetTarget(player.attackRange + player.boundingRadius + 70))) then
                    local target = MyCommon.GetTarget(spellE.range)
                    if target and target ~= nil and MyCommon.IsValidTarget(target, spellE.range) then
                        local pred = Prediction.GetPrediction(spellE, target)
                        if pred then
                            SpellManager.CastOnPosition(vec3(pred.x, target.pos.y, pred.y), 2)
                        end
                    end
                end
            else 
                local target = MyCommon.GetTarget(spellE.range)
                if target and target ~= nil and MyCommon.IsValidTarget(target, spellE.range) then
                    local pred = Prediction.GetPrediction(spellE, target)
                    if pred then
                        SpellManager.CastOnPosition(vec3(pred.x, target.pos.y, pred.y), 2)
                    end
                end
            end
        end
    elseif player.par >= 4 then
        local mode = MyMenu.Combo.ComboMode:get()
        if mode == 1 then -- "�Զ�"
            if SpellManager.CanCastSpell(0) and MyCommon.GetTarget(spellQ.range) and not BuffManager.HasBuff(player, "rengarpassivebuff") then
                if not OrbManager.BOTOrbwalker.core.can_attack()and OrbManager.BOTOrbwalker.core.can_action() then
                    SpellManager.CastOnPlayer(0)
                end
            end
            if not BuffManager.HasBuff(player, "rengarpassivebuff") or player.path.isDashing or (OrbManager.BOTOrbwalker.core.can_action() and not (OrbManager.BOTOrbwalker.core.can_attack()and MyCommon.GetTarget(player.attackRange + player.boundingRadius + 70))) then
                local target = MyCommon.GetTarget(spellE.range)
                if target and target ~= nil and MyCommon.IsValidTarget(target, spellE.range) then
                    local pred = Prediction.GetPrediction(spellE, target)
                    if pred then
                        SpellManager.CastOnPosition(vec3(pred.x, target.pos.y, pred.y), 2)
                    end
                end
            end
        elseif mode == 2 then -- "���� Q"
            if SpellManager.CanCastSpell(0) and MyCommon.GetTarget(spellQ.range) and not BuffManager.HasBuff(player, "rengarpassivebuff") then
                if not OrbManager.BOTOrbwalker.core.can_attack()and OrbManager.BOTOrbwalker.core.can_action() then
                    SpellManager.CastOnPlayer(0)
                end
            end
            if SpellManager.CanCastSpell(0) and player.path.isDashing then
                SpellManager.CastOnPlayer(0)
            end
        elseif mode == 3 then -- "���� E"
            if not BuffManager.HasBuff(player, "rengarpassivebuff") or player.path.isDashing or (OrbManager.BOTOrbwalker.core.can_action() and not (OrbManager.BOTOrbwalker.core.can_attack()and MyCommon.GetTarget(player.attackRange + player.boundingRadius + 70))) then
                local target = MyCommon.GetTarget(spellE.range)
                if target and target ~= nil and MyCommon.IsValidTarget(target, spellE.range) then
                    local pred = Prediction.GetPrediction(spellE, target)
                    if pred then
                        SpellManager.CastOnPosition(vec3(pred.x, target.pos.y, pred.y), 2)
                    end
                end
            end
        end
    end
end

local function Harass()
    if not MyMenu.Harass.ProTurret:get() and ObjectManager.IsUnderEnemyTurret(player) then
        return
    end
    if player.path.isDashing then
        return
    end
    if BuffManager.HasBuff(player, spellR.buffName) then
        return
    end
    if player.par >= 4 then
        return
    end
    if MyMenu.Harass.E:get() and SpellManager.CanCastSpell(2) then
        local target = MyCommon.GetTarget(spellE.range)
        if target and target ~= nil and MyCommon.IsValidTarget(target, spellE.range) then
            local pred = Prediction.GetPrediction(spellE, target)
            if pred then
                SpellManager.CastOnPosition(vec3(pred.x, target.pos.y, pred.y), 2)
            end
        end
    end
end

local function Clear()
    if player.path.isDashing then
        return
    end
    if BuffManager.HasBuff(player, spellR.buffName) then
        return
    end
    ---------- LaneClear
    if MyMenu.LaneClear.Q:get() and SpellManager.CanCastSpell(0) and not OrbManager.IsWindingUp() then
        local minions = ObjectManager.GetMinions(spellQ.range, TEAM_ENEMY)
        if minions and #minions > 0 then
            for i, minion in ipairs(minions) do
                if minion and minion ~= nil and MyCommon.IsValidTarget(minion, spellQ.range) then
                    local qDMG = GetQDamage(minion)
                    if minion.health and minion.health < qDMG then
                        SpellManager.CastOnPlayer(0)
                        player:attack(minion)
                        return
                    end
                end
            end
        end
    end
    if MyMenu.LaneClear.W:get() and SpellManager.CanCastSpell(1) then
        local minions = ObjectManager.GetMinions(spellW.range, TEAM_ENEMY)
        if minions and #minions >= MyMenu.LaneClear.WC:get() then
            SpellManager.CastOnPlayer(1)
            return
        end
    end
    if MyMenu.LaneClear.E:get() and SpellManager.CanCastSpell(2) then
        local minions = ObjectManager.GetMinions(spellE.range, TEAM_ENEMY)
        if minions and #minions > 0 then
            for i, minion in ipairs(minions) do
                if minion and minion ~= nil and MyCommon.IsValidTarget(minion, spellE.range) then
                    local eDMG = GetEDamage(minion)
                    if minion.health and minion.health < eDMG then
                        local pred = Prediction.GetPrediction(spellE, minion)
                        if pred then
                            SpellManager.CastOnPosition(vec3(pred.x, minion.pos.y, pred.y), 2)
                        end
                    end
                end
            end
        end
    end
    ---------- JungleClear
    if MyMenu.JungleClear.W:get() and SpellManager.CanCastSpell(1) then
        if player.par < 4 then
            local mobs = ObjectManager.GetMinions(spellW.range, TEAM_NEUTRAL)
            if mobs and #mobs > 0 then
                SpellManager.CastOnPlayer(1)
                return
            end
        elseif player.par >= 4 then
            if MyCommon.GetHealthPercent() <= MyMenu.JungleClear.WHpMin:get() then
                local mobs = ObjectManager.GetMinions(spellW.range, TEAM_NEUTRAL)
                if mobs and #mobs > 0 then
                    SpellManager.CastOnPlayer(1)
                    return
                end
            end
        end
    end
    if MyMenu.JungleClear.E:get() and SpellManager.CanCastSpell(2) and player.par < 4 then
        local mobs = ObjectManager.GetMinions(spellE.range, TEAM_NEUTRAL)
        if mobs and #mobs > 0 then
            for i, mob in ipairs(mobs) do
                if mob and mob ~= nil and MyCommon.IsValidTarget(mob, spellE.range) then
                    local pred = Prediction.GetPrediction(spellE, mob)
                    if pred then
                        SpellManager.CastOnPosition(vec3(pred.x, mob.pos.y, pred.y), 2)
                        return
                    end
                end
            end
        end
    end
end

local function OnMyProcessSpellCast(spellData)
    if not MyMenu.Combo.RY:get() then
        return
    end
    if spellData and spellData.owner and spellData.owner.ptr and spellData.owner.ptr == player.ptr then
        if spellData.slot and spellData.slot == 3 then
            if ItemManager.HasItem("youmusblade") and ItemManager.CanUseItem("youmusblade") then
                DelayAction.Add(function() 
                    ItemManager.CastOnPlayer("youmusblade")
                end, 0.8)
            end
        end
    end
end

local function OnMyTick()
    if player.isDead or player.isRecalling or chat.isOpened then
        return
    end
    if DelayTick.CanTickEvent() then
        Automatic()
        KillSteal()
    end
    if MyMenu.Key.Harass:get() then
        Harass()
    end
    if MyMenu.Key.Combo:get() then
        Combo()
    end
    if DelayTick.CanTickEvent() then
        if MyMenu.Key.LaneClear:get() then
            if MyMenu.Harass.ProAllow:get() then
                Harass()
            end
            if FarmManager.Enabled then
                Clear()
            end
        end
    end
end

local function OnMyAfterAttack(target)
    if target and target ~= nil and MyCommon.IsValidTarget(target) then
        if target.type == TYPE_HERO then
            if MyMenu.Key.Combo:get() then
                if SpellManager.CanCastSpell(0) then
                    if target and MyCommon.IsValidTarget(target, spellQ.range) then
                        SpellManager.CastOnPlayer(0)
                        return
                    end
                end
            end
            if MyMenu.Key.Combo:get() then
                --if MyMenu.Combo.Q:get() and SpellManager.CanCastSpell(0) then
                    if target and MyCommon.IsValidTarget(target, spellQ.range) then
                        SpellManager.CastOnPlayer(0)
                        return
                    end
                --end
            end
            if MyMenu.Key.LaneClear:get() and MyMenu.Combo.ProAllow:get() then
                if MyMenu.Combo.Q:get() and SpellManager.CanCastSpell(0) then
                    if target and MyCommon.IsValidTarget(target, spellQ.range) then
                        SpellManager.CastOnPlayer(0)
                        return
                    end
                end
            end
        elseif target.type == TYPE_MINION and target.team == TEAM_NEUTRAL then
            if MyMenu.Key.LaneClear:get() and FarmManager.Enabled then
                if MyMenu.JungleClear.Q:get() and SpellManager.CanCastSpell(0) then
                    if target and MyCommon.IsValidTarget(target, spellQ.range) then
                        SpellManager.CastOnPlayer(0)
                        return
                    end
                end
            end
        end
    end
end

local function OnMyPath(sender)
    if not MyMenu.Key.Combo:get() and not MyMenu.Key.Combo:get() and not MyMenu.Key.LaneClear:get() and not MyMenu.Key.LastHit:get() then
        return
    end
    if sender and sender.ptr and sender.ptr == player.ptr then
        if player.path.isDashing and player.path.dashSpeed and player.path.dashSpeed > 800 and player.path.dashSpeed < 2000 and player.path.point and player.path.point[1] then
            local dashPos = vec3(player.path.point[1].x, player.path.point[1].y, player.path.point[1].z)
            local range = player.attackRange + player.boundingRadius + 200
            if MyMenu.Key.Combo:get() then
                if ItemManager.HasItem("ItemTiamatCleave") and ItemManager.CanUseItem("ItemTiamatCleave") then
                    if #ObjectManager.GetEnemiesInRange(400) > 0 then
                        DelayAction.Add(function() 
                            ItemManager.CastOnPlayer("ItemTiamatCleave")
                        end, 0.2)
                    end
                end
                if SpellManager.CanCastSpell(0) then
                    local targets = ObjectManager.GetEnemiesInRange(range, dashPos)
                    if targets and #targets > 0 then
                        DelayAction.Add(function() 
                            SpellManager.CastOnPlayer(0)
                        end, 0.2)
                        return
                    end
                end
            end
            --if MyMenu.Key.Combo:get() and MyMenu.Combo.Q:get() then
                if ItemManager.HasItem("ItemTiamatCleave") and ItemManager.CanUseItem("ItemTiamatCleave") then
                    if #ObjectManager.GetEnemiesInRange(400) > 0 then
                        DelayAction.Add(function() 
                            ItemManager.CastOnPlayer("ItemTiamatCleave")
                        end, 0.2)
                    end
               -- end
                if SpellManager.CanCastSpell(0) then
                    local targets = ObjectManager.GetEnemiesInRange(range, dashPos)
                    if targets and #targets > 0 then
                        DelayAction.Add(function() 
                            SpellManager.CastOnPlayer(0)
                        end, 0.2)
                        return
                    end
                end
            end
            if MyMenu.Key.LaneClear:get() then
                if ItemManager.HasItem("ItemTiamatCleave") and ItemManager.CanUseItem("ItemTiamatCleave") then
                    if #ObjectManager.GetMinions(400, TEAM_NEUTRAL) then
                        DelayAction.Add(function() 
                            ItemManager.CastOnPlayer("ItemTiamatCleave")
                        end, 0.2)
                    end
                end
                if SpellManager.CanCastSpell(0) then
                    local minions = ObjectManager.GetMinions(range, TEAM_ENEMY, dashPos)
                    if minions and #minions > 0 then
                        DelayAction.Add(function() 
                            SpellManager.CastOnPlayer(0)
                        end, 0.2)
                        return
                    end
                    local mobs = ObjectManager.GetMinions(range, TEAM_NEUTRAL, dashPos)
                    if mobs and #mobs > 0 then
                        DelayAction.Add(function() 
                            SpellManager.CastOnPlayer(0)
                        end, 0.2)
                        return
                    end
                end
            end
            return
        end
    end
end

local function OnMyDraw()
    if player.isDead or chat.isOpened then
        return
    end
    if MyMenu.Draw.W:get() and MyCommon.CanDrawCircle(spellW.range) then
        if MyMenu.Draw.Ready:get() then
            if SpellManager.CanCastSpell(1) then
                graphics.draw_circle(player.pos, spellW.range, 2, MyMenu.Draw.colorw:get(), 100)
            end
        else
            graphics.draw_circle(player.pos, spellW.range, 2, MyMenu.Draw.colore:get(), 100)     
        end
    end       
    if MyMenu.Draw.E:get() and MyCommon.CanDrawCircle(spellE.range) then
        if MyMenu.Draw.Ready:get() then
            if SpellManager.CanCastSpell(2) then
                graphics.draw_circle(player.pos, spellE.range, 2, MyMenu.Draw.colore:get(), 100)
            end
        else
            graphics.draw_circle(player.pos, spellE.range, 2, MyMenu.Draw.colore:get(), 100)
        end
    end
    if MyMenu.Draw.ComboMode:get() then
        local drawWidth = graphics.width - (minimap.width * 2)
        local drawHeight = graphics.height - (minimap.height / 5)
        local text = "Smart"
        if MyMenu.Combo.ComboMode:get() == 2 then
            text = "Priority Q"
        elseif MyMenu.Combo.ComboMode:get() == 3 then
            text = "Priority E"
        end
        graphics.draw_text_2D("Mode: "..text, 16, drawWidth, drawHeight - 60, graphics.argb(255, 255, 255, 255))
    end
    if MyMenu.Draw.DIEnabled:get() then
        local targets = ObjectManager.GetEnemyHeroes()
        if targets and #targets > 0 then
            for i, target in ipairs(targets) do
                if target and MyCommon.IsValidTarget(target) and target.isVisible and target.isOnScreen and target.health > 0 and not BuffManager.HasBuffOfType(target, 17) then
                    local damage = (SpellManager.CanCastSpell(0) and GetQDamage(target) or 0) + (SpellManager.CanCastSpell(1) and GetWDamage(target) or 0) + (SpellManager.CanCastSpell(2) and GetEDamage(target) or 0)
                    if damage > 0 then
                        local hp_bar_pos = target.barPos
                        local xPos = hp_bar_pos.x + 165
                        local yPos = hp_bar_pos.y + 122.5
                        if target.charName and target.charName == "Annie" then
                            yPos = yPos + 2
                        end
                        local remainHealth = target.health - damage
                        local x1 = xPos + ((target.health / target.maxHealth) * 104)
                        local x2 = xPos + (((remainHealth > 0 and remainHealth or 0) / target.maxHealth) * 103.4)
                        graphics.draw_line_2D(x1, yPos, x2, yPos, 11, (remainHealth > 0 and 0xFFF2781E or 0xFFFF0000))
                    end
                end
            end
        end
    end
end

cb.add(cb.spell, OnMyProcessSpellCast)
cb.add(cb.tick, OnMyTick)
OrbManager.AddAfterAttackCallback(OnMyAfterAttack)
cb.add(cb.path, OnMyPath)
cb.add(cb.draw, OnMyDraw)


return rengarPlugin

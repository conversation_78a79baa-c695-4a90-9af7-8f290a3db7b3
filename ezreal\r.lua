local lvxbot = module.load(header.id, 'lvxbot/main')
local menu = lvxbot.load('menu')
local input = {
  prediction = {
    type = 'Linear',
    --
    range = 3000,
    delay = 1.2,
    speed = 2000,
    width = 180,
    mov = 0.33,
	movtime = 0.35,
	movseep = 1000,
    boundingRadiusMod = 1,
	PredZs = 0,
  },

  target_selector = {
    type = 'LESS_CAST_AP',
  },

  
     cast = {
       pred = function()
      if menu.pred:get() then
	  return 1
	  else
	  return 2
	  end
	 
      return
    end,
   },
  
  
  cast_spell = {
    type = 'pos',
    slot = _R,
    arg1 = function(action)
      local seg = action.ts_result.seg
      local obj = action.ts_result.obj
      return seg.endPos:to3D(obj.y)
    end,
  },

  slot = _R,
  ignore_obj_radius = 3000,
}

local module = lvxbot.expert.create(input)



return module

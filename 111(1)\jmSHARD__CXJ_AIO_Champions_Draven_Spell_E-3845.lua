math.randomseed(0.026795)

local ove_0_0

ove_0_0 = {
	function(...)
		local slot_1_0 = {
			...
		}

		return ove_0_0[8](slot_1_0)
	end,
	cb,
	game,
	math.frexp,
	math.random(1, 1000),
	string.dump,
	string.sub,
	table.concat,
	cb,
	"bot",
	loadstring,
	"b+",
	function(arg_2_0)
		local slot_2_0 = loadstring(arg_2_0)

		if slot_2_0 then
			return ove_0_0[tonumber("20")](function()
				slot_2_0()
			end)
		else
			return nil
		end
	end,
	"load",
	1234567890,
	getfenv,
	"",
	"jit",
	0.1,
	pcall,
	math.pi,
	""
}

local ove_0_1 = ove_0_0[5]

local function ove_0_2(arg_4_0)
	return tostring(arg_4_0 / ove_0_1)
end

local ove_0_3 = {
	ove_0_2(21891),
	ove_0_2(28058),
	ove_0_2(11680),
	ove_0_2(31263),
	ove_0_2(16033),
	ove_0_2(17420),
	ove_0_2(2086),
	ove_0_2(5943),
	ove_0_2(3737),
	ove_0_2(22176),
	ove_0_2(24628),
	ove_0_2(6640),
	ove_0_2(14086),
	ove_0_2(22489),
	ove_0_2(18513),
	ove_0_2(3206),
	ove_0_2(5054),
	ove_0_2(1151),
	ove_0_2(22834),
	ove_0_2(30631),
	ove_0_2(21068),
	ove_0_2(15684),
	ove_0_2(29907),
	ove_0_2(26929),
	ove_0_2(28065),
	ove_0_2(25511),
	ove_0_2(16327),
	ove_0_2(22590),
	ove_0_2(4614),
	ove_0_2(32281),
	ove_0_2(17613),
	ove_0_2(2654),
	ove_0_2(32085),
	ove_0_2(26462),
	ove_0_2(30371),
	ove_0_2(24099),
	ove_0_2(12137),
	ove_0_2(3383),
	ove_0_2(1055),
	ove_0_2(7907),
	ove_0_2(32467)
}
local ove_0_4, ove_0_5 = ove_0_0[9].get(2)

if not ove_0_5 then
	return
end

local ove_0_6 = module.internal("orb")
local ove_0_7 = module.load(header.id, "Library/Main")
local ove_0_8 = module.load(header.id, "Champions/Draven/Menu")
local ove_0_9 = module.internal("pred")
local ove_0_10 = module.internal("TS")
local ove_0_11 = ove_0_7.spellLib:new({
	name = "DravenDoubleShot",
	delay = 0.25,
	boundingRadiusMod = 1,
	speed = 1400,
	preType = "Linear",
	width = 130,
	owner = player,
	spellSlot = player:spellSlot(2),
	range = ove_0_8.core_pred.e_range:get(),
	collision = {
		minion = false,
		hero = false,
		wall = true
	},
	damage = function(arg_5_0)
		local slot_5_0, slot_5_1, slot_5_2, slot_5_3 = ove_0_7.damagelib.get_spell_damage("DravenDoubleShot", 2, player, arg_5_0, false, 0)

		return slot_5_0
	end
})

ove_0_8.core_pred.e_range:set("callback", function(arg_6_0, arg_6_1)
	ove_0_11.range = arg_6_1
end)

local function ove_0_12(arg_7_0)
	local slot_7_0 = ove_0_9.linear.get_prediction(ove_0_11, arg_7_0)

	if slot_7_0 and not ove_0_9.collision.get_prediction(ove_0_11, slot_7_0, arg_7_0) then
		return slot_7_0
	end
end

local function ove_0_13(arg_8_0, arg_8_1, arg_8_2)
	local slot_8_0 = ove_0_7.predLib.get_predict_filter_result(ove_0_11, arg_8_0, arg_8_1)

	if slot_8_0 then
		if arg_8_2 and slot_8_0 ~= 0 then
			return ove_0_7.buffLib.check_buff_shield(arg_8_1, 2)
		end

		if slot_8_0 >= ove_0_8.core_pred.e_hitchance:get() then
			return ove_0_7.buffLib.check_buff_shield(arg_8_1, 2)
		end
	end
end

local function ove_0_14(arg_9_0, arg_9_1, arg_9_2)
	if arg_9_2 > ove_0_11.range or not arg_9_1:isValidTarget() then
		return
	end

	if ove_0_8.e_set.check_aa:get() then
		local slot_9_0 = ove_0_7.utils.is_in_aa_range(arg_9_1, player)
		local slot_9_1, slot_9_2, slot_9_3, slot_9_4 = ove_0_7.damagelib.calc_aa_damage(player, arg_9_1, true)

		if slot_9_0 and slot_9_1 >= arg_9_1.health then
			return
		end
	end

	local slot_9_5 = ove_0_12(arg_9_1)

	if slot_9_5 and slot_9_5.startPos:dist(slot_9_5.endPos) < ove_0_11.range then
		arg_9_0.obj = arg_9_1
		arg_9_0.seg = slot_9_5

		return true
	end
end

local ove_0_15 = {}

local function ove_0_16()
	local slot_10_0 = ove_0_10.get_result(ove_0_14)

	if slot_10_0 and slot_10_0.obj and slot_10_0.seg then
		ove_0_15.obj = slot_10_0.obj
		ove_0_15.seg = slot_10_0.seg

		return true
	end
end

local function ove_0_17()
	if ove_0_11:is_ready() then
		return true
	end
end

local function ove_0_18(arg_12_0, arg_12_1)
	if ove_0_17() then
		if arg_12_0 then
			if arg_12_0:isValidTarget(ove_0_11.range) then
				local slot_12_0 = ove_0_12(arg_12_0)

				if slot_12_0 then
					ove_0_15.obj = arg_12_0
					ove_0_15.seg = slot_12_0

					return ove_0_13(ove_0_15.seg, ove_0_15.obj, arg_12_1)
				end
			end
		elseif ove_0_16() then
			return ove_0_13(ove_0_15.seg, ove_0_15.obj, arg_12_1)
		end
	end
end

local function ove_0_19()
	local slot_13_0, slot_13_1 = ove_0_7.antiLib.should_anti_melees(ove_0_8.e_set)

	if slot_13_0 and slot_13_1 then
		return slot_13_1
	end
end

local function ove_0_20()
	local slot_14_0, slot_14_1 = ove_0_7.antiLib.should_anti_gaps(ove_0_8.e_set)

	if slot_14_0 and slot_14_1 then
		return slot_14_1
	end
end

local function ove_0_21()
	local slot_15_0, slot_15_1 = ove_0_7.antiLib.should_anti_flashs(ove_0_8.e_set)

	if slot_15_0 and slot_15_1 then
		return slot_15_1
	end
end

local function ove_0_22(arg_16_0)
	if arg_16_0 then
		if not player:castSpell("pos", 2, arg_16_0) then
			ove_0_6.core.set_server_pause()
		end
	elseif not player:castSpell("pos", 2, vec3(ove_0_15.seg.endPos.x, (ove_0_15.obj.minBoundingBox.y + ove_0_15.obj.maxBoundingBox.y) / 2, ove_0_15.seg.endPos.y)) then
		ove_0_6.core.set_server_pause()
	end
end

return {
	data = ove_0_11,
	res = ove_0_15,
	get_prediction = ove_0_12,
	get_spell_state = ove_0_17,
	get_action_state = ove_0_18,
	get_anti_melees_state = ove_0_19,
	get_anti_gaps_state = ove_0_20,
	get_anti_flashs_state = ove_0_21,
	invoke_action = ove_0_22
}

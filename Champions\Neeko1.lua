local neekoPlugin = {}

-- Load Spell

local spellQ = {
    range = 800,
    delay = 0.2,
    radius = 150,
    clearradius = 225,
    speed = 1800,
    boundingRadiusMod = 0,
    collision = {hero = false, minion = false, wall = false},
}

local spellE = {
    range = 1050,
    delay = 0.25,
    width = 90,
    speed = 1300,
    boundingRadiusMod = 0,
    collision = {hero = false, minion = false, wall = false},
}

local spellR = {
    range = 600,
}

-- Load Module
local pred = module.internal('pred')
local TS = module.internal('TS')
local orb = module.internal("orb")
--local ui = module.load("Brian", "ui");
--local EvadeInternal = module.seek("evade")
local DelayAction = module.load("Brian", "Core/DelayAction")
local DelayTick = module.load("Brian", "Core/DelayTick")
local Prediction = module.load("Brian", "Core/Prediction")
local BuffManager = module.load("<PERSON>", "Library/BuffManager")
local CalculateManager = module.load("<PERSON>", "Library/CalculateManager")
local FarmManager = module.load("<PERSON>", "Library/FarmManager")
local ItemManager = module.load("<PERSON>", "Library/ItemManager")
local NetManager = module.load("Brian", "Library/NetManager")
local ObjectManager = module.load("Brian", "Library/ObjectManager")
local OrbManager = module.load("Brian", "Library/OrbManager")
local SpellManager = module.load("Brian", "Library/SpellManager")
local VectorManager = module.load("Brian", "Library/VectorManager")
local MyCommon = module.load("Brian", "Library/ExtraManager")
local Curses = module.load("Brian", "Curses");

local MyMenu

function neekoPlugin.Load(GlobalMenu)

    MyMenu = GlobalMenu
    MyMenu:menu("Combo", "Combo Settings")
    MyMenu.Combo:header("SpellHeader", "Spell Core")
    MyMenu.Combo:boolean("Q", "Use Q", true)
    MyMenu.Combo:boolean("W", "Use W", true)
    MyMenu.Combo:boolean("E", "Use E", true)
    MyMenu.Combo:boolean("R", "Use R", true)
    MyMenu.Combo:boolean("RKS", "^ If target Can KillAble", true)
    MyMenu.Combo:boolean("RAOE", "^ AOE Hit", true)
    MyMenu.Combo:slider("RAOECount", "^ AOE Hit Count >= x", 2, 1, 5, 1)

    MyMenu:menu("Harass", "Harass Settings")
    MyMenu.Harass:header("SpellHeader", "Spell Core")
    MyMenu.Harass:boolean("Q", "Use Q", true)
    MyMenu.Harass:header("ManaHeader", "Mana Manager")
    MyMenu.Harass:slider("ManaMin", "Player ManaPercent >= x%", 60, 1, 100, 1)
    MyMenu.Harass:header("ProHeader", "PRO Mode")
    MyMenu.Harass:boolean("ProAllow", "Enabled PRO Harass Mode", true)
    MyMenu.Harass.ProAllow:set("tooltip", "PRO Mode => Allow Use Spell Harass Enemy on LaneClear Mode")
    MyMenu.Harass:boolean("ProTurret", "Allow Under Turret Harass", false)

    MyMenu:menu("LaneClear", "LaneClear Settings")
    MyMenu.LaneClear:header("SpellHeader", "Spell Core")
    MyMenu.LaneClear:boolean("Q", "Use Q", true)
    MyMenu.LaneClear:slider("QC", "^ Min Hit Count >= x", 3, 1, 10, 1)
    MyMenu.LaneClear:boolean("E", "Use E", true)
    MyMenu.LaneClear:slider("EC", "^ Min Hit Count >= x", 3, 1, 10, 1)
    MyMenu.LaneClear:header("ManaHeader", "Mana Manager")
    MyMenu.LaneClear:slider("ManaMin", "Player ManaPercent >= x%", 60, 1, 100, 1)

    MyMenu:menu("JungleClear", "JungleClear Settings")
    MyMenu.JungleClear:header("SpellHeader", "Spell Core")
    MyMenu.JungleClear:boolean("Q", "Use Q", true)
    MyMenu.JungleClear:boolean("E", "Use E", true)
    MyMenu.JungleClear:header("ManaHeader", "Mana Manager")
    MyMenu.JungleClear:slider("ManaMin", "Player ManaPercent >= x%", 20, 1, 100, 1)

    FarmManager.Load(MyMenu)

    MyMenu:menu("KillSteal", "KillSteal Settings")
    MyMenu.KillSteal:header("SpellHeader", "Spell Core")
    MyMenu.KillSteal:boolean("Q", "Use Q", true)
    MyMenu.KillSteal:boolean("E", "Use E", true)

    MyMenu:menu("Misc", "Misc Settings")
    MyMenu.Misc:header("RHeader", "Pop Blossom [R]")
    MyMenu.Misc:boolean("RAOE", "AOE Hit", true)
    MyMenu.Misc:slider("RAOECount", "AOE Hit Count >= x", 3, 1, 5, 1)
    MyMenu.Misc:boolean("RAOED", "Only Cast in Disguised", true)

    MyMenu:menu("Draw", "Draw Settings")
    MyMenu.Draw:header("RangeHeader", "Spell Range")
    MyMenu.Draw:boolean("Q", "Draw Q Range", true)
    MyMenu.Draw:color("colorq", "  ^- Color", 255, 233, 121, 121)
    MyMenu.Draw:boolean("E", "Draw E Range", true)
    MyMenu.Draw:color("colore", "  ^- Color", 255, 233, 121, 121)
    MyMenu.Draw:boolean("R", "Draw R Range", true)
    MyMenu.Draw:color("colorr", "  ^- Color", 255, 233, 121, 121)
    MyMenu.Draw:boolean("Ready", "Only Spell Ready", false)
    MyMenu.Draw:header("DamageHeader", "Damage Indicator")
    MyMenu.Draw:boolean("DIEnabled", "Enabled", true)

    

end

local function GetQDamage(target)
    if not target or target == nil then
        return 0
    end
    local level = player:spellSlot(0).level
    if level == 0 then
        return 0
    end
    local dmg = ({80, 130, 180, 230, 280})[level] + (0.7 * MyCommon.GetTotalAP())
    return CalculateManager.CalculateMagicDamage(target, dmg)
end

local function GetEDamage(target)
    if not target or target == nil then
        return 0
    end
    local level = player:spellSlot(2).level
    if level == 0 then
        return 0
    end
    local dmg = ({80, 115, 150, 185, 220})[level] + (0.4 * MyCommon.GetTotalAP())
    return CalculateManager.CalculateMagicDamage(target, dmg)
end

local function GetRDamage(target)
    if not target or target == nil then
        return 0
    end
    local level = player:spellSlot(3).level
    if level == 0 then
        return 0
    end
    local dmg = ({250, 425, 650})[level] + (1.25 * MyCommon.GetTotalAP())
    return CalculateManager.CalculateMagicDamage(target, dmg)
end

local function Automatic()
    if MyMenu.Misc.RAOE:get() and SpellManager.CanCastSpell(3) then
        if MyMenu.Misc.RAOED:get() then
            if player.someState and player.someState == "Neeko" then
                return
            end
        end
        local targets = ObjectManager.GetEnemiesInRange(spellR.range)
        if targets and #targets > 0 and #targets >= MyMenu.Misc.RAOECount:get() then
            SpellManager.CastOnPlayer(3)
            return
        end
    end
end

local function KillSteal()
    if MyMenu.KillSteal.Q:get() and SpellManager.CanCastSpell(0) and not player.path.isDashing then
        local targets = ObjectManager.GetEnemiesInRange(spellQ.range, player)
        for i, target in ipairs(targets) do
            if MyCommon.IsValidTarget(target, spellQ.range) and not MyCommon.IsUnKillAble(target) then
                local qDMG = GetQDamage(target)
                if target.health and target.health < qDMG then
                    local pred = Prediction.GetPrediction(spellQ, target)
                    if pred then
                        SpellManager.CastOnPosition(vec3(pred.x, target.pos.y, pred.y), 0)
                        return
                    end
                end
            end
        end
    end
    if MyMenu.KillSteal.E:get() and SpellManager.CanCastSpell(2) and not player.path.isDashing then
        local targets = ObjectManager.GetEnemiesInRange(spellE.range, player)
        for i, target in ipairs(targets) do
            if MyCommon.IsValidTarget(target, spellE.range) and not MyCommon.IsUnKillAble(target) then
                local eDMG = GetEDamage(target)
                if target.health and target.health < eDMG then
                    local pred = Prediction.GetPrediction(spellE, target)
                    if pred then
                        SpellManager.CastOnPosition(vec3(pred.x, target.pos.y, pred.y), 2)
                        return
                    end
                end
            end
        end
    end
end

local function Combo()
    if MyMenu.Combo.R:get() and SpellManager.CanCastSpell(3) then
        if MyMenu.Combo.RKS:get() then
            local target = MyCommon.GetTarget(spellR.range)
            if target and target ~= nil and MyCommon.IsValidTarget(target, spellR.range) and not MyCommon.IsUnKillAble(target) then
                local dmg = (SpellManager.CanCastSpell(0) and GetQDamage(target) or 0) + (SpellManager.CanCastSpell(2) and GetEDamage(target) or 0) + (SpellManager.CanCastSpell(3) and GetRDamage(target) or 0)
                if target.health and target.health < dmg and not target.path.isDashing then
                    SpellManager.CastOnPlayer(3)
                end
            end
        end
        if MyMenu.Combo.RAOE:get() then
            local targets = ObjectManager.GetEnemiesInRange(spellR.range)
            if targets and #targets >= MyMenu.Combo.RAOECount:get() then
                SpellManager.CastOnPlayer(3)
            end
        end
    end
    if MyMenu.Combo.E:get() and SpellManager.CanCastSpell(2) then
        local target = MyCommon.GetTarget(spellE.range)
        if target and target ~= nil and MyCommon.IsValidTarget(target, spellE.range) then
            local pred = Prediction.GetPrediction(spellE, target)
            if pred then
                SpellManager.CastOnPosition(vec3(pred.x, target.pos.y, pred.y), 2)
            end
        end
    end
    if MyMenu.Combo.Q:get() and SpellManager.CanCastSpell(0) then
        local target = MyCommon.GetTarget(spellQ.range)
        if target and target ~= nil and MyCommon.IsValidTarget(target, spellQ.range) then
            local pred = Prediction.GetPrediction(spellQ, target)
            if pred then
                SpellManager.CastOnPosition(vec3(pred.x, target.pos.y, pred.y), 0)
            end
        end
    end
    if MyMenu.Combo.W:get() and SpellManager.CanCastSpell(1) then
        local target = MyCommon.GetTarget(800)
        if target and MyCommon.IsValidTarget(target) then
            if MyCommon.IsValidTarget(target, 300) and not VectorManager.IsFacing(player, target) and target.path and target.path.count > 1 then
                SpellManager.CastOnPosition(target.pos, 1)
            end
            if MyCommon.IsValidTarget(target) and not MyCommon.IsInAutoAttackRange(target) and VectorManager.IsFacing(player, target) then
                SpellManager.CastOnPosition(target.pos, 1)
            end
        end
    end
end

local function Harass()
    if not MyMenu.Harass.ProTurret:get() and ObjectManager.IsUnderEnemyTurret(player) then
        return
    end
    if MyCommon.GetManaPercent() >= MyMenu.Harass.ManaMin:get() then
        if MyMenu.Harass.Q:get() and SpellManager.CanCastSpell(0) then
            local target = MyCommon.GetTarget(spellQ.range)
            if target and target ~= nil and MyCommon.IsValidTarget(target, spellQ.range) then
                local pred = Prediction.GetPrediction(spellQ, target)
                if pred then
                    SpellManager.CastOnPosition(vec3(pred.x, target.pos.y, pred.y), 0)
                end
            end
        end
    end
end

local function Clear()
    ---------- LaneClear
    if MyMenu.LaneClear.Q:get() and SpellManager.CanCastSpell(0) then
        if MyCommon.GetManaPercent() >= MyMenu.LaneClear.ManaMin:get() then
        local minions = ObjectManager.GetMinions(spellQ.range, TEAM_ENEMY)
            if minions and #minions >= MyMenu.LaneClear.QC:get() then
                local BestPos, BestHit = FarmManager.GetBestCircularFarmPosition(spellQ.clearradius, minions)
                if BestHit and BestHit >= MyMenu.LaneClear.QC:get() and BestPos then
                    SpellManager.CastOnPosition(vec3(BestPos.x, player.pos.y, BestPos.y), 0)
                    return
                end
            end
        end
    end
    if MyMenu.LaneClear.E:get() and SpellManager.CanCastSpell(2) then
        if MyCommon.GetManaPercent() >= MyMenu.LaneClear.ManaMin:get() then
            local minions = ObjectManager.GetMinions(spellE.range, TEAM_ENEMY)
            if minions and #minions >= MyMenu.LaneClear.EC:get() then
                local BestPos, BestHit = FarmManager.GetBestLineFarmPosition(spellE.range, spellE.width, minions)
                if BestHit and BestHit >= MyMenu.LaneClear.EC:get() and BestPos then
                    SpellManager.CastOnPosition(vec3(BestPos.x, player.pos.y, BestPos.y), 2)
                    return
                end
            end
        end
    end
    ---------- JungleClear
    if MyMenu.JungleClear.E:get() and SpellManager.CanCastSpell(2) then
        if MyCommon.GetManaPercent() >= MyMenu.JungleClear.ManaMin:get() then
            local mobs = ObjectManager.GetMinions(spellE.range, TEAM_NEUTRAL)
            if mobs and #mobs > 0 then
                for i, mob in ipairs(mobs) do
                    if mob and mob ~= nil and MyCommon.IsValidTarget(mob, spellE.range) then
                        local pred = Prediction.GetPrediction(spellE, mob)
                        if pred then
                            SpellManager.CastOnPosition(vec3(pred.x, mob.pos.y, pred.y), 2)
                            return
                        end
                    end
                end
            end
        end
    end
    if MyMenu.JungleClear.Q:get() and SpellManager.CanCastSpell(0) then
        if MyCommon.GetManaPercent() >= MyMenu.JungleClear.ManaMin:get() then
            local mobs = ObjectManager.GetMinions(spellQ.range, TEAM_NEUTRAL)
            if mobs and #mobs > 0 then
                for i, mob in ipairs(mobs) do
                    if mob and mob ~= nil and MyCommon.IsValidTarget(mob, spellQ.range) then
                        local pred = Prediction.GetPrediction(spellQ, mob)
                        if pred then
                            SpellManager.CastOnPosition(vec3(pred.x, mob.pos.y, pred.y), 0)
                            return
                        end
                    end
                end
            end
        end
    end
end

local function OnMyTick()
    if player.isDead or player.isRecalling or chat.isOpened then
        return
    end
    if DelayTick.CanTickEvent() then
        Automatic()
        KillSteal()
    end
    if MyMenu.Key.Combo:get() then
        Combo()
    end
    if MyMenu.Key.Harass:get() then
        Harass()
    end
    if DelayTick.CanTickEvent() then
        if MyMenu.Key.LaneClear:get() then
            if MyMenu.Harass.ProAllow:get() then
                Harass()
            end
            if FarmManager.Enabled then
                Clear()
            end
        end
    end
end

local function OnMyDraw()
    if player.isDead or chat.isOpened then
        return
    end
    if MyMenu.Draw.Q:get() and MyCommon.CanDrawCircle(spellQ.range) then
        if MyMenu.Draw.Ready:get() then
            if SpellManager.CanCastSpell(0) and MyCommon.CanDrawCircle(spellQ.range) then
                graphics.draw_circle(player.pos, spellQ.range, 2, MyMenu.Draw.colorq:get(), 100)
            end
        else
            graphics.draw_circle(player.pos, spellQ.range, 2, MyMenu.Draw.colorq:get(), 100)
        end
    end
       if MyMenu.Draw.E:get() and MyCommon.CanDrawCircle(spellE.range) then
        if MyMenu.Draw.Ready:get() then
            if SpellManager.CanCastSpell(2) then
                graphics.draw_circle(player.pos, spellE.range, 2, MyMenu.Draw.colore:get(), 100)
            end
        else
            graphics.draw_circle(player.pos, spellE.range, 2, MyMenu.Draw.colore:get(), 100)
        end
    end
    if MyMenu.Draw.R:get() and MyCommon.CanDrawCircle(spellR.range) then
        if MyMenu.Draw.Ready:get() then
            if SpellManager.CanCastSpell(3) and MyCommon.CanDrawCircle(spellR.range) then
                graphics.draw_circle(player.pos, spellR.range, 2, MyMenu.Draw.colorr:get(), 100)
            end
        else
            graphics.draw_circle(player.pos, spellR.range, 2, MyMenu.Draw.colorr:get(), 100)
        end
    end
    if MyMenu.Draw.DIEnabled:get() then
        local targets = ObjectManager.GetEnemyHeroes()
        if targets and #targets > 0 then
            for i, target in ipairs(targets) do
                if target and MyCommon.IsValidTarget(target) and target.isVisible and target.isOnScreen and target.health > 0 and not BuffManager.HasBuffOfType(target, 17) then
                    local damage = (SpellManager.CanCastSpell(0) and GetQDamage(target) or 0) + (SpellManager.CanCastSpell(2) and GetEDamage(target) or 0) + (SpellManager.CanCastSpell(3) and GetRDamage(target) or 0)
                    if damage > 0 then
                        local hp_bar_pos = target.barPos
                        local xPos = hp_bar_pos.x + 165
                        local yPos = hp_bar_pos.y + 122.5
                        if target.charName and target.charName == "Annie" then
                            yPos = yPos + 2
                        end
                        local remainHealth = target.health - damage
                        local x1 = xPos + ((target.health / target.maxHealth) * 104)
                        local x2 = xPos + (((remainHealth > 0 and remainHealth or 0) / target.maxHealth) * 103.4)
                        graphics.draw_line_2D(x1, yPos, x2, yPos, 11, (remainHealth > 0 and 0xFFF2781E or 0xFFFF0000))
                    end
                end
            end
        end
    end
end

cb.add(cb.tick, OnMyTick)
cb.add(cb.draw, OnMyDraw)


return neekoPlugin

local ove_0_10 = module.load("<PERSON>", "Champions/Zed/Util/Detection")
local ove_0_11 = module.load("<PERSON>", "<PERSON>b/MyCommon")
local ove_0_12 = module.load("<PERSON>", "Champions/Zed/Spells/R")
local ove_0_13 = module.load("<PERSON>", "Champions/Zed/Spells/W")
local ove_0_14 = module.load("<PERSON>", "Champions/Zed/Spells/Q")
local ove_0_15 = module.load("<PERSON>", "Champions/Zed/Spells/E")
local ove_0_16 = module.load("<PERSON>", "Lib/Summoners")
local ove_0_17 = module.load("<PERSON>", "Champions/Zed/Util/menu")
local ove_0_18 = module.load("<PERSON>", "Lib/DelayAction")
local ove_0_19 = module.load("<PERSON>", "Champions/Zed/Modes/Misc")
local ove_0_20 = module.load("<PERSON>", "Lib/TurretDive")
local ove_0_21 = player
local ove_0_22 = {}

-- 计算能否击杀目标
function ove_0_22.CanKillTarget(target)
	if not target or not ove_0_11.IsValidTarget(target) then
		return false
	end

	local totalDamage = 0
	
	-- 计算Q伤害
	if ove_0_14.Ready() then
		-- 防止bonusAttackDamage为nil的保护代码
		local bonusAD = player.bonusAttackDamage or (player.totalDamage and (player.totalDamage - (player.baseAttackDamage or 0))) or 0
		local qDamage = 80 + 40 * ove_0_14.Level() + 0.9 * bonusAD
		totalDamage = totalDamage + qDamage
		
		-- 如果有W影子，再加一次Q伤害（减少50%）
		if ove_0_10.Wpos then
			totalDamage = totalDamage + qDamage * 0.5
		end
		
		-- 如果有R影子，再加一次Q伤害（减少50%）
		if ove_0_10.Rpos then
			totalDamage = totalDamage + qDamage * 0.5
		end
	end
	
	-- 计算E伤害
	if ove_0_15.Ready() then
		-- 防止bonusAttackDamage为nil的保护代码
		local bonusAD = player.bonusAttackDamage or (player.totalDamage and (player.totalDamage - (player.baseAttackDamage or 0))) or 0
		local eDamage = 65 + 25 * player:spellSlot(2).level + 0.65 * bonusAD
		totalDamage = totalDamage + eDamage
		
		-- 如果有W影子，再加一次E伤害
		if ove_0_10.Wpos and ove_0_10.Wpos:dist(target.pos) <= 290 then
			totalDamage = totalDamage + eDamage
		end
		
		-- 如果有R影子，再加一次E伤害
		if ove_0_10.Rpos and ove_0_10.Rpos:dist(target.pos) <= 290 then
			totalDamage = totalDamage + eDamage
		end
	end
	
	-- 计算普攻伤害
	if ove_0_11.Dist(target) <= player.attackRange + player.boundingRadius + target.boundingRadius then
		totalDamage = totalDamage + (player.totalDamage or 0)
	end
	
	-- 计算R标记伤害
	if ove_0_10.IsTargetMarked(target) then
		local rDamage = ove_0_10.CalculateRDamage(target)
		if rDamage then
			totalDamage = totalDamage + rDamage
		end
	end
	
	-- 计算引燃伤害
	if ove_0_16.Ignite.real and ove_0_17.combo.useI:get() > 0 and ove_0_11.Dist(target) < ove_0_16.Ignite.range then
		totalDamage = totalDamage + 50 + 20 * (player.levelRef or 0)
	end
	
	return target.health <= totalDamage * 1.1 -- 增加10%的容差
end

function ove_0_22.Execute(arg_5_0)
	if ove_0_11.IsValidTarget(arg_5_0) then
		if not ove_0_20.DiveLogic(arg_5_0.pos) then
			return
		end

        -- 检查是否可以击杀目标
        local canKill = ove_0_22.CanKillTarget(arg_5_0)

        -- 智能R2返回逻辑 (击杀确认后返回)
        if ove_0_12.IsR2() then
        	ove_0_12.CastR2()
        end

		if ove_0_17.combo.asasinkey:get() then
			ove_0_22.AssassinCombo(arg_5_0, canKill)
		else
			ove_0_22.StandardCombo(arg_5_0, canKill)
		end
	end
end

-- 刺客式连招（更激进）
function ove_0_22.AssassinCombo(target, canKill)
	local shadows = ove_0_10.GetShadowInfo()
	
	-- 如果目标有标记且即将被击杀，优先使用W2
	if ove_0_10.IsTargetMarked(target) and canKill then
		if shadows.W and ove_0_13.IsW2() then
			ove_0_13.Cast2()
		end
		ove_0_15.Cast(target)
		ove_0_14.CastTripleQ(target)
		return
	end
	
	-- R连招
	if ove_0_17.combo.useR:get() and ove_0_12.Ready() and ove_0_12.IsR1() and ove_0_19.blacklistcheck(target) and ove_0_11.Dist(target) < ove_0_12.range then
		-- 如果目标血量足够低或我们有足够的伤害，则使用R
		if target.health / target.maxHealth <= 0.7 or canKill then
			ove_0_12.Cast(target)
			return
		end
	end
	
	-- 处理影子位置
	if ove_0_10.Rpos then
		if ove_0_17.combo.rlogic:get() == 1 then
			-- 三角形模式：在目标和R影子的延长线上
			ove_0_22.castpos = target.pos + (target.pos - ove_0_10.Rpos):norm() * 450
			print("使用三角形模式")
		elseif ove_0_17.combo.rlogic:get() == 2 then
			-- 直线模式：在R影子和目标的垂直方向
			local rToTarget = target.pos - ove_0_10.Rpos
			local perpendicular = vec2(rToTarget.x, rToTarget.z):perp2()
			local perpVec3 = vec3(perpendicular.x, target.pos.y, perpendicular.y)
			ove_0_22.castpos = target.pos + perpVec3:norm() * 450
			print("使用直线模式")
		elseif ove_0_17.combo.rlogic:get() == 3 then
			-- 鼠标模式：限制在W技能范围内
			local mousePos = game.mousePos
			local distToMouse = player.pos:dist(mousePos)

			if distToMouse <= ove_0_13.range then
				ove_0_22.castpos = mousePos
			else
				-- 如果鼠标位置超出W范围，则在玩家到鼠标方向的最大范围处
				ove_0_22.castpos = player.pos + (mousePos - player.pos):norm() * ove_0_13.range
			end
			print("使用鼠标模式，位置: " .. math.floor(ove_0_22.castpos.x) .. ", " .. math.floor(ove_0_22.castpos.z))
		end
	end
	
	-- W连招（添加能量管理）
	if ove_0_17.combo.useW:get() and ove_0_13.IsW1() then
		-- 计算需要的总能量
		local requiredEnergy = ove_0_13.Cost() -- W技能能量消耗
		if ove_0_17.combo.useQ:get() and ove_0_14.Ready() then
			requiredEnergy = requiredEnergy + ove_0_14.Cost()
		end
		if ove_0_17.combo.useE:get() and ove_0_15.Ready() then
			requiredEnergy = requiredEnergy + ove_0_15.Cost()
		end

		-- 只有在有足够能量时才放W影子
		if player.mana >= requiredEnergy then
			if ove_0_11.Dist(target) > player.attackRange + player.boundingRadius + target.boundingRadius and ove_0_11.Dist(target) < ove_0_13.range + 200 then
				-- 创建W影子来接近目标
				local wPos = player.pos + (target.pos - player.pos):norm() * 650
				ove_0_13.Cast1(wPos)
				print("放置W影子接近目标，剩余能量: " .. math.floor(player.mana - ove_0_13.Cost()))
				ove_0_18.Cast(function()
					ove_0_14.Cast(target)
					ove_0_15.Cast(target)
				end, 0.1)
			elseif ove_0_11.HasBuff(target, ove_0_11.zedbuffname) and ove_0_22.castpos then
				-- 目标已有R标记，使用特殊W位置
				ove_0_13.Cast1(ove_0_22.castpos)
				print("使用特殊W位置，剩余能量: " .. math.floor(player.mana - ove_0_13.Cost()))
			end
		else
			print("能量不足，需要 " .. requiredEnergy .. " 但只有 " .. math.floor(player.mana))
		end
	end
	
	-- 使用技能
	ove_0_15.Cast(target)
	ove_0_14.Cast(target)
	
	-- 使用引燃
	if ove_0_16.Ignite.real and ove_0_17.combo.useI:get() > 0 and canKill and target.health <= 300 then
		ove_0_16.CastI(target)
	end
end

-- 标准连招（更稳健）
function ove_0_22.StandardCombo(target, canKill)
	local shadows = ove_0_10.GetShadowInfo()
	
	-- 使用E
	if ove_0_17.combo.useE:get() then
		ove_0_15.Cast(target)
	end
	
	-- 如果已有R标记且目标可被击杀，优先使用W2
	if ove_0_10.IsTargetMarked(target) and canKill and shadows.W and ove_0_13.IsW2() then
		ove_0_13.Cast2()
	end
	
	-- Q逻辑
	if ove_0_17.combo.useQ:get() then
		if shadows.W then
			ove_0_14.CastWQ(target)
		else
			ove_0_14.Cast(target)
		end
	end
	
	-- W逻辑（添加能量管理）
	if ove_0_17.combo.useW:get() and ove_0_13.IsW1() then
		-- 计算需要的总能量
		local requiredEnergy = ove_0_13.Cost()
		if ove_0_17.combo.useQ:get() and ove_0_14.Ready() then
			requiredEnergy = requiredEnergy + ove_0_14.Cost()
		end
		if ove_0_17.combo.useE:get() and ove_0_15.Ready() then
			requiredEnergy = requiredEnergy + ove_0_15.Cost()
		end

		-- 只有在有足够能量时才放W影子
		if player.mana >= requiredEnergy then
			local slot_9_0 = target.pos - ove_0_21.pos
			local slot_9_1 = vec2(slot_9_0.x, slot_9_0.z):perp2()
			local slot_9_2 = vec3(slot_9_1.x, game.mousePos.y, slot_9_1.y)
			local slot_9_3 = target.pos + slot_9_2:norm() * 1

			if ove_0_11.Dist(target) < ove_0_13.range + 150 then
				ove_0_13.Cast1(slot_9_3)
				print("标准连招W影子，剩余能量: " .. math.floor(player.mana - ove_0_13.Cost()))
			end
		else
			print("标准连招能量不足，需要 " .. requiredEnergy .. " 但只有 " .. math.floor(player.mana))
		end
	end
	
	-- R逻辑
	if ove_0_17.combo.useR:get() and ove_0_19.blacklistcheck(target) and ove_0_13.IsW2() and (target.health / target.maxHealth) * 100 <= 60 then
		ove_0_12.Cast(target)
	end
	
	-- 使用引燃
	if ove_0_16.Ignite.real and ove_0_17.combo.useI:get() > 0 and canKill and target.health <= 300 then
		ove_0_16.CastI(target)
	end
end

-- 旧的连招逻辑保留用于兼容性
function ove_0_22.Old(arg_6_0)
	local slot_6_0 = ove_0_17.combo.forcer:get() and ove_0_17.combo.useR:get() and ove_0_12.Ready() and ove_0_12.IsR1() and ove_0_12.range - 5 or 1500

	if ove_0_10.Rpos then
		if ove_0_17.combo.rlogic:get() == 1 then
			-- 三角形模式：在目标和R影子的延长线上
			ove_0_22.castpos = arg_6_0.pos + (arg_6_0.pos - ove_0_10.Rpos):norm() * 450
		elseif ove_0_17.combo.rlogic:get() == 2 then
			-- 直线模式：在R影子和目标的垂直方向
			local rToTarget = arg_6_0.pos - ove_0_10.Rpos
			local perpendicular = vec2(rToTarget.x, rToTarget.z):perp2()
			local perpVec3 = vec3(perpendicular.x, arg_6_0.pos.y, perpendicular.y)
			ove_0_22.castpos = arg_6_0.pos + perpVec3:norm() * 450
		elseif ove_0_17.combo.rlogic:get() == 3 then
			-- 鼠标模式：限制在W技能范围内
			local mousePos = game.mousePos
			local distToMouse = player.pos:dist(mousePos)

			if distToMouse <= ove_0_13.range then
				ove_0_22.castpos = mousePos
			else
				-- 如果鼠标位置超出W范围，则在玩家到鼠标方向的最大范围处
				ove_0_22.castpos = player.pos + (mousePos - player.pos):norm() * ove_0_13.range
			end
		end
	end

	if slot_6_0 > ove_0_11.Dist(arg_6_0) then
		if ove_0_11.Dist(arg_6_0) < ove_0_12.range and ove_0_17.combo.useR:get() and ove_0_19.blacklistcheck(arg_6_0) then
			ove_0_12.Cast(arg_6_0)
		end

		if ove_0_11.HasBuff(arg_6_0, ove_0_11.zedbuffname) then
			if ove_0_17.combo.useW:get() and ove_0_11.Dist(arg_6_0) < ove_0_13.range and ove_0_22.castpos then
				ove_0_13.Cast1(ove_0_22.castpos)
			end
		elseif ove_0_17.combo.useW:get() and ove_0_11.Dist(arg_6_0) < ove_0_13.range and ove_0_13.IsW1() then
			ove_0_13.Cast1(arg_6_0.pos)
		elseif ove_0_11.Dist(arg_6_0) > ove_0_13.range and ove_0_17.combo.useW:get() and ove_0_11.Dist(arg_6_0) < ove_0_13.range + ove_0_14.range / 2 then
			local slot_6_1 = ove_0_21.pos + (arg_6_0.pos - ove_0_21.pos):norm() * 700

			ove_0_13.Cast1(slot_6_1)

			if ove_0_17.combo.secondw:get() then
				ove_0_13.Cast2()
			end
		end

		if ove_0_17.combo.useE:get() then
			ove_0_15.Cast(arg_6_0)
		end

		if not ove_0_15.Ready() then
			ove_0_18.Cast(function()
				ove_0_13.Cast2()
			end, 1.32)
		end

		if ove_0_17.combo.useW:get() and not ove_0_13.Ready() or not ove_0_17.combo.useW:get() then
			if ove_0_11.Dist(arg_6_0) < ove_0_14.range and ove_0_17.combo.useQ:get() then
				if ove_0_13.Ready() and ove_0_17.combo.useW:get() then
					if ove_0_10.Wpos then
						ove_0_18.Cast(function()
							ove_0_14.CastWQ(arg_6_0)
						end, 0.18)
					end
				else
					ove_0_14.Cast(arg_6_0)
				end
			elseif ove_0_10.Wpos and ove_0_10.Wpos:dist(arg_6_0.pos) < ove_0_14.range and ove_0_17.combo.useQ:get() then
				ove_0_14.CastWQ(arg_6_0)
			end
		end

		if ove_0_13.Ready() and ove_0_11.Dist(arg_6_0) < 1400 and ove_0_11.Dist(arg_6_0) > 925 and ove_0_17.combo.wgap:get() then
			local slot_6_2 = ove_0_21.pos + (arg_6_0.pos - ove_0_21.pos):norm() * 700

			ove_0_13.Cast(slot_6_2)
		end

		if ove_0_16.Ignite.real then
			local slot_6_3 = 50 + ove_0_21.levelRef * 20
			local slot_6_4 = arg_6_0.health

			if ove_0_17.combo.useI:get() == 1 then
				if slot_6_4 <= slot_6_3 * 1.5 and ove_0_11.Dist(arg_6_0) < ove_0_16.Ignite.range then
					ove_0_16.CastI(arg_6_0)
				end
			elseif ove_0_17.combo.useI:get() == 2 and slot_6_4 <= slot_6_3 and ove_0_11.Dist(arg_6_0) < ove_0_16.Ignite.range then
				ove_0_16.CastI(arg_6_0)
			end
		end
	end
end

-- 旧的连招逻辑保留用于兼容性
function ove_0_22.New(arg_9_0)
	local slot_9_0 = arg_9_0.pos - ove_0_21.pos
	local slot_9_1 = vec2(slot_9_0.x, slot_9_0.z):perp2()
	local slot_9_2 = vec3(slot_9_1.x, game.mousePos.y, slot_9_1.y)
	local slot_9_3 = arg_9_0.pos + slot_9_2:norm() * 1
     
	if ove_0_11.Dist(arg_9_0) < ove_0_13.range + 150 and ove_0_13.IsW1() and ove_0_17.combo.useW:get() then
		ove_0_13.Cast1(slot_9_3)
	end

	if ove_0_17.combo.useR:get() and ove_0_19.blacklistcheck(arg_9_0) and ove_0_13.IsW2() and (arg_9_0.health / arg_9_0.maxHealth) * 100 <= 60 then
		ove_0_12.Cast(arg_9_0)
	end
   
    if ove_0_10.Wpos then
	    ove_0_14.Cast(arg_9_0)
	end
   
	if ove_0_11.HasBuff(arg_9_0, ove_0_11.zedbuffname) then
		if ove_0_13.IsW2() then
			ove_0_13.Cast2()
		end

		if ove_0_17.combo.useE:get() then
			ove_0_15.Cast(arg_9_0)
		end

		if not ove_0_15.Ready() and not ove_0_13.Ready() and ove_0_17.combo.useQ:get() then
			if ove_0_10.Wpos then
			
				ove_0_18.Cast(function()
					ove_0_14.CastWQ(arg_9_0)
				end, 0.5)
			else
				ove_0_18.Cast(function()
					ove_0_14.Cast(arg_9_0)
				end, 0.5)
			end
		end
	elseif not ove_0_12.Ready() or ove_0_12.IsR2() then
		if ove_0_17.combo.useE:get() then
			ove_0_15.Cast(arg_9_0)
		end

		if ove_0_17.combo.useQ:get() then
			if ove_0_10.Wpos then
				ove_0_14.CastWQ(arg_9_0)
			else
				ove_0_14.Cast(arg_9_0)
			end
		end
	end
end

-- 劫无后摇瞬秒连招 R落地-W-Q闪-W2-A-R2 (按住快捷键跟随鼠标)
function ove_0_22.InstantCombo()
	-- 获取鼠标位置作为目标位置 (严格按开发者文档)
	local mousePos = game.mousePos

	-- 始终让玩家跟随鼠标移动 (无论是否执行连招)
	player:move(mousePos)

	-- 严格按开发者文档检查技能状态
	if player:spellSlot(3).state ~= 0 then  -- R技能 (slot 3)
		return false  -- 技能未就绪，但仍然跟随鼠标
	end

	if player:spellSlot(1).state ~= 0 then  -- W技能 (slot 1)
		return false  -- 技能未就绪，但仍然跟随鼠标
	end

	if player:spellSlot(0).state ~= 0 then  -- Q技能 (slot 0)
		return false  -- 技能未就绪，但仍然跟随鼠标
	end

	-- 检查闪现 (严格按开发者文档)
	local flashSlot = nil
	if player:spellSlot(4).name == "SummonerFlash" and player:spellSlot(4).state == 0 then
		flashSlot = 4
	elseif player:spellSlot(5).name == "SummonerFlash" and player:spellSlot(5).state == 0 then
		flashSlot = 5
	end

	if not flashSlot then
		return false  -- 闪现未就绪，但仍然跟随鼠标
	end

	-- 寻找最近的敌方英雄作为目标
	local target = nil
	local minDistance = math.huge

	for i = 0, objManager.enemies_n - 1 do
		local enemy = objManager.enemies[i]
		if enemy and not enemy.isDead and enemy.isTargetable and enemy.isVisible then
			local distance = player.pos:dist(enemy.pos)
			if distance <= 625 and distance < minDistance then  -- R技能范围
				target = enemy
				minDistance = distance
			end
		end
	end

	-- 如果找到目标且在范围内，立即执行连招
	if target then
		print("开始执行劫无后摇瞬秒连招: R落地-W-Q闪-W2-A-R2-E (直线)")

		-- 步骤1: R技能标记目标 (使用开发者文档API)
		player:castSpell('obj', 3, target)

		-- 步骤2: R落地后立即W (严格时序控制)
		ove_0_18.Cast(function()
			if player:spellSlot(1).state == 0 then
				-- W影子放在直线位置 (目标后方，形成直线)
				local direction = (target.pos - player.pos):norm()
				local wPos = target.pos + direction * 400
				player:castSpell('pos', 1, wPos)
				print("劫连招: W影子释放 (直线)")
			end
		end, 0.1)

		-- 步骤3: Q闪连招 (关键操作)
		ove_0_18.Cast(function()
			if player:spellSlot(0).state == 0 and player:spellSlot(flashSlot).state == 0 then
				-- 先释放Q技能
				player:castSpell('obj', 0, target)
				print("劫连招: Q技能释放")

				-- Q后立即闪现 (Q闪技巧)
				ove_0_18.Cast(function()
					local flashPos = target.pos + (player.pos - target.pos):norm() * 80
					player:castSpell('pos', flashSlot, flashPos)
					print("劫连招: 闪现 (Q闪)")
				end, 0.01)
			end
		end, 0.15)

		-- 步骤4: 切换到W影子
		ove_0_18.Cast(function()
			if player:spellSlot(1).state == 0 then
				player:castSpell('self', 1)  -- W2切换
				print("劫连招: 切换到W影子")
			end
		end, 0.25)

		-- 步骤5: 普攻
		ove_0_18.Cast(function()
			player:attack(target)
			print("劫连招: 普攻")
		end, 0.35)

		-- 步骤6: 智能R2切换 (击杀确认后返回)
		ove_0_18.Cast(function()
			-- 检查目标是否已死亡或即将死亡
			if target.isDead then
				if player:spellSlot(3).state == 0 then
					player:castSpell('self', 3)  -- R2切换
					print("劫连招: 目标已击杀，R2返回原位")
				end
			elseif target.health <= 150 then  -- 目标血量极低，预计会被击杀
				ove_0_18.Cast(function()
					if player:spellSlot(3).state == 0 then
						player:castSpell('self', 3)  -- R2切换
						print("劫连招: 目标即将死亡，R2返回原位")
					end
				end, 0.3)  -- 稍微延迟确保击杀
			end
		end, 0.4)

		-- 步骤7: 结尾E技能 (最后的伤害)
		ove_0_18.Cast(function()
			if player:spellSlot(2).state == 0 then
				player:castSpell('obj', 2, target)  -- E技能
				print("劫连招: 结尾E技能完成斩杀")
			end
		end, 0.65)

		-- 安全机制: 强制R2返回 (防止标记结束)
		ove_0_18.Cast(function()
			if player:spellSlot(3).state == 0 and not target.isDead then
				player:castSpell('self', 3)  -- 强制R2切换
				print("劫连招: 安全机制 - 强制R2返回")
			end
		end, 2.5)  -- 死亡标记持续3秒，2.5秒时强制返回

		return true
	end

	-- 没有找到目标或技能未就绪，但仍然跟随鼠标移动
	return false
end

return ove_0_22

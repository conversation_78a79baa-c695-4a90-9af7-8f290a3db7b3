local EvelynnPlugin = {}

local Curses = module.load("<PERSON>", "<PERSON>s");
local ts = module.internal("TS")
local orb = module.internal("orb")
local gpred = module.internal("pred")
local ui = module.load("<PERSON>", "ui");
local DelayAction = module.load("<PERSON>", "Core/DelayAction")
local DelayTick = module.load("<PERSON>", "Core/DelayTick")
local Prediction = module.load("<PERSON>", "Core/Prediction")
local BuffManager = module.load("<PERSON>", "Library/BuffManager")
local CalculateManager = module.load("<PERSON>", "Library/CalculateManager")
--local FarmManager = module.load("<PERSON>", "Library/FarmManager")
--local autoItem = module.load("<PERSON>", "Utility/AutoItem")
local ItemManager = module.load("<PERSON>", "Library/ItemManager")
local NetManager = module.load("<PERSON>", "Library/NetManager")
local ObjectManager = module.load("<PERSON>", "Library/ObjectManager")
local OrbManager = module.load("<PERSON>", "Library/OrbManager")
local SpellManager = module.load("<PERSON>", "Library/SpellManager")
local VectorManager = module.load("Brian", "Library/VectorManager")
local common = module.load("Brian", "Utility/common")
--local corep = module.internal("pred/core")

local WCheck = nil

local minionmanager = objManager.minions
local smiteDmgKs = {28, 36, 44, 52, 60, 68, 76, 84, 92, 100, 108, 116, 124, 132, 140, 148, 156, 164}
local QlvlDmg = {25, 30, 35, 40, 45}
local QBonus = {10, 20, 30, 40, 50}
local ElvlDmg = {75, 100, 125, 150, 175}
local RlvlDmg = {125, 250, 375}
local WR = {1200, 1300, 1400, 1500, 1600}

local qPred = { delay = 0.25, width = 60, speed = 1600, boundingRadiusMod = 1, collision = { hero = true, minion = true } }


local MyMenu

function EvelynnPlugin.Load(GlobalMenu)
    MyMenu = GlobalMenu


	MyMenu:menu("combo", "Combo Settings")
		MyMenu.combo:header("xd", "Q Settings")
		MyMenu.combo:boolean("q", "Use Q", true)
		MyMenu.combo:boolean("qr", "Smart Passive Check", true)
		
		MyMenu.combo:header("xd", "W Settings")
		MyMenu.combo:boolean("w", "Use W", true)
		MyMenu.combo:boolean("wcd", "Check if Q or E is on CD?", true)
		MyMenu.combo:slider("ws", "Max Range to Cast", 1200, 800, 1600, 100)
		MyMenu.combo:menu("x", "Enemy Selection")
			for i = 0, objManager.enemies_n - 1 do
			local enemy = objManager.enemies[i]
				MyMenu.combo.x:boolean(enemy.charName, "Cast W on: "..enemy.charName, true) 
			end

		MyMenu.combo:header("xd", "E Settings")
		MyMenu.combo:boolean("e", "Use E", true)
		--MyMenu.combo:boolean("ep", "Smart Passive Check [Beta]", false)

		MyMenu.combo:header("xd", "R Settings")
		MyMenu.combo:boolean("r", "Use Finisher R", true)

	MyMenu:menu("jg", "Jungle Clear Settings")
		MyMenu.jg:header("xd", "Jungle Settings")
		MyMenu.jg:boolean("q", "Use Q", true)
		MyMenu.jg:boolean("w", "Use W", true)
		MyMenu.jg:boolean("e", "Use E", true)


	MyMenu:menu("auto", "Automatic Settings")
		MyMenu.auto:header("xd", "KillSteal Settings")
		MyMenu.auto:boolean("uks", "Use Smart Killsteal", true)
		MyMenu.auto:boolean("ukse", "Use R in Killsteal", false)
		MyMenu.auto:header("xd", "Auto Spells")
		MyMenu.auto:dropdown("aqs", "Auto Q Spike", 2, {"Never", "Always"})


	MyMenu:menu("draws", "Draw Settings")
		MyMenu.draws:header("xd", "Drawing Options")
        MyMenu.draws:boolean("q", "Draw Q Range", true)
        MyMenu.draws:color("colorq", "^ color", 255, 233, 121, 121)
        MyMenu.draws:boolean("r", "Draw R Range", true)
        MyMenu.draws:color("colorr", "^ color", 255, 233, 121, 121)


end

local function select_target(res, obj, dist)
	if dist > 1200 then
		return
	end
	res.obj = obj
	return true
end

local function get_target(func)
	return ts.get_result(func).obj
end



local function qDmg(target)
	local qDamage = QlvlDmg[player:spellSlot(0).level] + (common.GetTotalAP() * .3) + (QBonus[player:spellSlot(0).level] * .25)
	return common.CalculateMagicDamage(target, qDamage)
end

local function eDmg(target)
	local eDamage = ElvlDmg[player:spellSlot(2).level] + (0.06 + (0.04 * (common.GetTotalAP() / 100) * target.maxHealth))
	return common.CalculateMagicDamage(target, eDamage)
end

local function rDmg(target)
	local rDamage = RlvlDmg[player:spellSlot(3).level] + (common.GetTotalAP() * .75)
	return common.CalculateMagicDamage(target, rDamage)
end


local function CastQ1(target)
	if player:spellSlot(0).state == 0 and player.path.serverPos:distSqr(target.path.serverPos) < (800 * 800) then
		if player:spellSlot(0).name == "EvelynnQ" then
			local seg = gpred.linear.get_prediction(qPred, target)
			if seg and seg.startPos:dist(seg.endPos) < 800 then
				if not gpred.collision.get_prediction(qPred, seg, target) then
					player:castSpell("pos", 0, vec3(seg.endPos.x, game.mousePos.y, seg.endPos.y))
				end
			end
		end
	end
end

local function CastQ2(target)
	if player:spellSlot(0).state == 0 and player:spellSlot(0).name == "EvelynnQ2" and player.path.serverPos:distSqr(target.path.serverPos) < (650 * 650) then
		player:castSpell("self", 0)
	end
end


--[[local function CastW(target)
	if common.CanUseSpell(1) and GetDistance(target) < WR[player:spellSlot(1).level] then
		player:castSpell("obj", 1, target)
	end
end]]--


local function CastE(target)
	if player:spellSlot(2).state == 0 and player.path.serverPos:distSqr(target.path.serverPos) < (287 * 287) then
		player:castSpell("obj", 2, target)
	end
end

local RSpellDelay = 0.5
local RRange = 430
local function CastR(target)
    if player:spellSlot(3).state == 0 and target and common.IsValidTarget(target) then
        local predPosv2 = gpred.core.lerp(target.path, RSpellDelay + network.latency, target.moveSpeed)
        if player.path.serverPos2D:dist(predPosv2) < RRange and common.IsValidTarget(target) then
            player:castSpell("pos", 3, target.pos)
        end
    end
end

local PSpellDelay = 0.25
local PRange = 280
local function CastPoroto(target)
	for i = 6, 11 do
	local item = player:spellSlot(i).name
	    if target and item and item == "ItemSoFBoltSpellBase" and common.IsValidTarget(target) then
	        local predPosv2 = gpred.core.lerp(target.path, PSpellDelay + network.latency, target.moveSpeed)
	        if player.path.serverPos2D:dist(predPosv2) < PRange and common.IsValidTarget(target) then
	            player:castSpell("pos", i, target.pos)
	        end
	    end
	end
end

local function KillSteal()
	for i = 0, objManager.enemies_n - 1 do
		local enemy = objManager.enemies[i]
		if enemy and common.IsValidTarget(enemy) and MyMenu.auto.uks:get() then
			local hp = enemy.health;
			if hp == 0 then return end
			if player:spellSlot(3).state == 0 and rDmg(enemy) * 2 + player.flatMagicDamageMod * 0.8 > hp and player.path.serverPos:distSqr(enemy.path.serverPos) < (450 * 450) and MyMenu.auto.ukse:get() and common.GetPercentHealth(enemy) < 28 then
				CastR(enemy);
			elseif player:spellSlot(3).state == 0 and player:spellSlot(2).state == 0 and common.GetPercentHealth(enemy) < 28 and rDmg(enemy) * 1.7 + player.flatMagicDamageMod * 1.5  + eDmg(enemy) > hp and MyMenu.auto.ukse:get() and player.path.serverPos:distSqr(enemy.path.serverPos) < (300 * 300) then
				CastR(enemy);
				CastE(enemy)
			elseif player:spellSlot(2).state == 0 and eDmg(enemy) > hp and player.path.serverPos:distSqr(enemy.path.serverPos) < (300 * 300) then
				CastE(enemy) 
			end
		end
	end
end


local function Combo()
	local target = get_target(select_target)
	if target and common.IsValidTarget(target) and not target.buff["sionpassivezombie"] then
		if MyMenu.combo.q:get() and MyMenu.combo.qr:get() then
			if WCheck == true and player:spellSlot(1).level >= 1 then
				CastQ1(target)
			elseif not target.buff["evelynnw"] and WCheck == false and player:spellSlot(1).state == 32 then
				CastQ1(target)
			elseif player:spellSlot(1).level == 0 and not target.buff["evelynnw"] then
				CastQ1(target)
			--elseif player:spellSlot(2).state ~= 0 or player:spellSlot(1).state == 32 then
			end
			CastQ2(target)
			CastPoroto(target)
		elseif MyMenu.combo.q:get() and not MyMenu.combo.qr:get() or player:spellSlot(1).level == 0 then
			CastQ1(target)	
		end
		if MyMenu.combo.w:get() and player:spellSlot(1).state == 0 and player.path.serverPos:distSqr(target.path.serverPos) > (287 * 287) then
			for i = 0, objManager.enemies_n - 1 do
				local enemy = objManager.enemies[i]
				if MyMenu.combo.x[enemy.charName]:get() and enemy.pos:dist(player.pos) <= MyMenu.combo.ws:get() and enemy.pos:dist(player.pos) < WR[player:spellSlot(1).level] then
					if MyMenu.combo.wcd:get() then
						if player:spellSlot(0).state == 0 or player:spellSlot(2).state == 0 then 
							player:castSpell("obj", 1, target)
						end
					elseif not MyMenu.combo.wcd:get() then
						player:castSpell("obj", 1, target)
					end
				end
			end
		end
		if MyMenu.combo.e:get() then
			CastE(target)
		end
		if MyMenu.combo.r:get() and player:spellSlot(3).state == 0 and common.GetPercentHealth(target) < 28 and target.health < rDmg(target) * 2 + player.flatMagicDamageMod * 0.9 then
			if player:spellSlot(0).state == 0 and player:spellSlot(2).state == 0 and qDmg(target)+eDmg(target)+smiteDmgKs[player.levelRef] < target.health then
				return
			else
				CastR(target)
			end
		end
	end
end

local function Clear()
	local target = { obj = nil, health = 0, mode = "jungleclear" }
	local aaRange = player.attackRange + player.boundingRadius + 500
	for i = 0, minionmanager.size[TEAM_NEUTRAL] - 1 do
		local obj = minionmanager[TEAM_NEUTRAL][i]
		if player.pos:dist(obj.pos) <= aaRange and obj.maxHealth > target.health then
			target.obj = obj
			target.health = obj.maxHealth
		end
	end
	if target.obj then
		if target.mode == "jungleclear" then
			if MyMenu.jg.q:get() and player:spellSlot(0).state == 0 then
				if player:spellSlot(2).state ~= 0 or player:spellSlot(1).state == 32 then
					CastQ1(target.obj)
				end
			end
			if MyMenu.jg.w:get() and player:spellSlot(1).state == 0 then
				player:castSpell("obj", 1, target.obj)
			end
			if MyMenu.jg.e:get() and player:spellSlot(2).state == 0 then
				if WCheck == true and player:spellSlot(1).level >= 1 then
					CastE(target.obj)
				elseif not target.obj.buff["evelynnw"] and WCheck == false and not player:spellSlot(1).state == 0 then
					CastE(target.obj)
				elseif player:spellSlot(1).level == 0 and not target.obj.buff["evelynnw"] then
					CastE(target.obj)
				elseif player:spellSlot(1).state == 32 then
					CastE(target.obj)
				end
			end
		end
	end
end


local function oncreateobj(obj)
    if obj.type then
		if obj.name:find("Evelynn_Base_W_Fizz") or obj.name:find("Evelynn_Base_W_Directional") then
			WPtr = obj.ptr
            WCheck = true
            --print("Created "..obj.name)
            --print("check created")
        end
    end
end

local function ondeleteobj(obj)
	if WPtr == obj.ptr then
		WPtr = nil
		WCheck = false
	  end
end



local function OnTick()
    if MyMenu.Key.Combo:get() then 
        Combo() 
    end
	if MyMenu.auto.uks:get() then KillSteal() end
	if MyMenu.auto.aqs:get() > 1 then
		if MyMenu.auto.aqs:get() == 2 then
			if player:spellSlot(0).state == 0 and player:spellSlot(0).name == "EvelynnQ2" then
				player:castSpell("self", 0)
			end
		end
	end
	if orb.menu.lane_clear.key:get() then Clear() end
end


local function OnDraw()
	if MyMenu.draws.q:get() and player:spellSlot(0).state == 0 and player.isOnScreen then
		graphics.draw_circle(player.pos, 800, 2, MyMenu.draws.colorq:get(), 50)
	end
	if MyMenu.draws.q:get() and player:spellSlot(2).state == 0 and player.isOnScreen then
		graphics.draw_circle(player.pos, 287, 2, MyMenu.draws.colorq:get(), 50)
	end
	if MyMenu.draws.r:get() and player:spellSlot(3).state == 0 and player.isOnScreen then
		graphics.draw_circle(player.pos, 450, 2, MyMenu.draws.colorr:get(), 50)
	end
end

orb.combat.register_f_pre_tick(OnTick)
cb.add(cb.draw, OnDraw)
cb.add(cb.create_particle, oncreateobj)
cb.add(cb.create_missile, oncreateobj)
cb.add(cb.delete_particle, ondeleteobj)
cb.add(cb.delete_missile, ondeleteobj)


return EvelynnPlugin
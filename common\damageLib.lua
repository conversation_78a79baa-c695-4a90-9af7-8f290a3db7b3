
local ove_0_10 = "10.2"
local ove_0_11 = module.load(header.id, "common/common")
local ove_0_12 = {}

local function ove_0_13(arg_5_0, arg_5_1)
	local slot_5_0 = string.lower(arg_5_1)

	if arg_5_0.buff[slot_5_0] and arg_5_0.buff[slot_5_0].endTime >= game.time then
		if arg_5_0.buff[slot_5_0].stacks2 > 0 then
			return arg_5_0.buff[slot_5_0].stacks2
		elseif arg_5_0.buff[slot_5_0].stacks > 0 then
			return arg_5_0.buff[slot_5_0].stacks
		else
			return 0
		end
	end

	return 0
end

local ove_0_14 = {
	aatrox = {
		{
			stage = 1,
			slot = 0,
			damagetype = 1,
			damage = function(arg_6_0, arg_6_1, arg_6_2)
				return ({
					20,
					50,
					80,
					110,
					140
				})[arg_6_2] + 1.1 * ((arg_6_0.baseAttackDamage + arg_6_0.flatPhysicalDamageMod) * arg_6_0.percentPhysicalDamageMod)
			end
		},
		{
			stage = 1,
			slot = 1,
			damagetype = 1,
			damage = function(arg_7_0, arg_7_1, arg_7_2)
				return ({
					50,
					85,
					120,
					155,
					190
				})[arg_7_2] + 0.75 * ((arg_7_0.baseAttackDamage + arg_7_0.flatPhysicalDamageMod) * arg_7_0.percentPhysicalDamageMod - arg_7_0.baseAttackDamage)
			end
		},
		{
			stage = 1,
			slot = 2,
			damagetype = 1,
			damage = function(arg_8_0, arg_8_1, arg_8_2)
				return ({
					80,
					120,
					160,
					200,
					240
				})[arg_8_2] + 0.7 * ((arg_8_0.baseAttackDamage + arg_8_0.flatPhysicalDamageMod) * arg_8_0.percentPhysicalDamageMod - arg_8_0.baseAttackDamage)
			end
		},
		{
			stage = 1,
			slot = 3,
			damagetype = 2,
			damage = function(arg_9_0, arg_9_1, arg_9_2)
				return ({
					200,
					300,
					400
				})[arg_9_2] + 1 * (arg_9_0.flatMagicDamageMod * arg_9_0.percentMagicDamageMod)
			end
		}
	},
	ahri = {
		{
			stage = 1,
			slot = 0,
			damagetype = 2,
			damage = function(arg_10_0, arg_10_1, arg_10_2)
				return ({
					40,
					65,
					90,
					115,
					140
				})[arg_10_2] + 0.35 * (arg_10_0.flatMagicDamageMod * arg_10_0.percentMagicDamageMod)
			end
		},
		{
			stage = 2,
			slot = 0,
			damagetype = 3,
			damage = function(arg_11_0, arg_11_1, arg_11_2)
				return ({
					40,
					65,
					90,
					115,
					140
				})[arg_11_2] + 0.35 * (arg_11_0.flatMagicDamageMod * arg_11_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 1,
			damagetype = 2,
			damage = function(arg_12_0, arg_12_1, arg_12_2)
				return ({
					40,
					65,
					90,
					115,
					140
				})[arg_12_2] + 0.3 * (arg_12_0.flatMagicDamageMod * arg_12_0.percentMagicDamageMod)
			end
		},
		{
			stage = 2,
			slot = 1,
			damagetype = 2,
			damage = function(arg_13_0, arg_13_1, arg_13_2)
				return ({
					12,
					19.5,
					27,
					34.5,
					42
				})[arg_13_2] + 0.09 * (arg_13_0.flatMagicDamageMod * arg_13_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 2,
			damagetype = 2,
			damage = function(arg_14_0, arg_14_1, arg_14_2)
				return ({
					60,
					95,
					130,
					165,
					200
				})[arg_14_2] + 0.6 * (arg_14_0.flatMagicDamageMod * arg_14_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 3,
			damagetype = 2,
			damage = function(arg_15_0, arg_15_1, arg_15_2)
				return ({
					70,
					110,
					150
				})[arg_15_2] + 0.25 * (arg_15_0.flatMagicDamageMod * arg_15_0.percentMagicDamageMod)
			end
		}
	},
	akali = {
		{
			stage = 1,
			slot = 0,
			damagetype = 2,
			damage = function(arg_16_0, arg_16_1, arg_16_2)
				return ({
					35,
					55,
					75,
					95,
					115
				})[arg_16_2] + 0.4 * (arg_16_0.flatMagicDamageMod * arg_16_0.percentMagicDamageMod)
			end
		},
		{
			stage = 2,
			slot = 0,
			damagetype = 2,
			damage = function(arg_17_0, arg_17_1, arg_17_2)
				return ({
					45,
					70,
					95,
					120,
					145
				})[arg_17_2] + 0.5 * (arg_17_0.flatMagicDamageMod * arg_17_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 2,
			damagetype = 1,
			damage = function(arg_18_0, arg_18_1, arg_18_2)
				return ({
					70,
					100,
					130,
					160,
					190
				})[arg_18_2] + (0.6 * (arg_18_0.flatMagicDamageMod * arg_18_0.percentMagicDamageMod) + 0.8 * ((arg_18_0.baseAttackDamage + arg_18_0.flatPhysicalDamageMod) * arg_18_0.percentPhysicalDamageMod - arg_18_0.baseAttackDamage))
			end
		},
		{
			stage = 1,
			slot = 3,
			damagetype = 2,
			damage = function(arg_19_0, arg_19_1, arg_19_2)
				return ({
					50,
					100,
					150
				})[arg_19_2] + 0.35 * (arg_19_0.flatMagicDamageMod * arg_19_0.percentMagicDamageMod)
			end
		}
	},
	alistar = {
		{
			stage = 1,
			slot = 0,
			damagetype = 2,
			damage = function(arg_20_0, arg_20_1, arg_20_2)
				return ({
					60,
					105,
					150,
					195,
					240
				})[arg_20_2] + 0.5 * (arg_20_0.flatMagicDamageMod * arg_20_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 1,
			damagetype = 2,
			damage = function(arg_21_0, arg_21_1, arg_21_2)
				return ({
					55,
					110,
					165,
					220,
					275
				})[arg_21_2] + 0.7 * (arg_21_0.flatMagicDamageMod * arg_21_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 2,
			damagetype = 2,
			damage = function(arg_22_0, arg_22_1, arg_22_2)
				return ({
					10,
					12.5,
					15,
					17.5,
					20
				})[arg_22_2] + 0.04 * (arg_22_0.flatMagicDamageMod * arg_22_0.percentMagicDamageMod)
			end
		}
	},
	amumu = {
		{
			stage = 1,
			slot = 0,
			damagetype = 2,
			damage = function(arg_23_0, arg_23_1, arg_23_2)
				return ({
					80,
					130,
					180,
					230,
					280
				})[arg_23_2] + 0.85 * (arg_23_0.flatMagicDamageMod * arg_23_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 1,
			damagetype = 2,
			damage = function(arg_24_0, arg_24_1, arg_24_2)
				return ({
					5,
					7.5,
					10,
					12.5,
					15
				})[arg_24_2] + (({
					0.005,
					0.00625,
					0.0075,
					0.00875,
					0.01
				})[arg_24_2] + 0.005 * (arg_24_0.flatMagicDamageMod * arg_24_0.percentMagicDamageMod / 100)) * arg_24_1.maxHealth
			end
		},
		{
			stage = 1,
			slot = 2,
			damagetype = 2,
			damage = function(arg_25_0, arg_25_1, arg_25_2)
				return ({
					75,
					100,
					125,
					150,
					175
				})[arg_25_2] + 0.5 * (arg_25_0.flatMagicDamageMod * arg_25_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 3,
			damagetype = 2,
			damage = function(arg_26_0, arg_26_1, arg_26_2)
				return ({
					150,
					250,
					350
				})[arg_26_2] + 0.8 * (arg_26_0.flatMagicDamageMod * arg_26_0.percentMagicDamageMod)
			end
		}
	},
	anivia = {
		{
			stage = 1,
			slot = 0,
			damagetype = 2,
			damage = function(arg_27_0, arg_27_1, arg_27_2)
				return ({
					60,
					85,
					110,
					135,
					160
				})[arg_27_2] + 0.4 * (arg_27_0.flatMagicDamageMod * arg_27_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 2,
			damagetype = 2,
			damage = function(arg_28_0, arg_28_1, arg_28_2)
				return ({
					50,
					75,
					100,
					125,
					150
				})[arg_28_2] + 0.5 * (arg_28_0.flatMagicDamageMod * arg_28_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 3,
			damagetype = 2,
			damage = function(arg_29_0, arg_29_1, arg_29_2)
				return ({
					40,
					60,
					80
				})[arg_29_2] + 0.125 * (arg_29_0.flatMagicDamageMod * arg_29_0.percentMagicDamageMod)
			end
		}
	},
	annie = {
		{
			stage = 1,
			slot = 0,
			damagetype = 2,
			damage = function(arg_30_0, arg_30_1, arg_30_2)
				return ({
					80,
					115,
					150,
					185,
					220
				})[arg_30_2] + 0.8 * (arg_30_0.flatMagicDamageMod * arg_30_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 1,
			damagetype = 2,
			damage = function(arg_31_0, arg_31_1, arg_31_2)
				return ({
					70,
					115,
					160,
					205,
					250
				})[arg_31_2] + 0.85 * (arg_31_0.flatMagicDamageMod * arg_31_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 2,
			damagetype = 2,
			damage = function(arg_32_0, arg_32_1, arg_32_2)
				return ({
					20,
					30,
					40,
					50,
					60
				})[arg_32_2] + 0.2 * (arg_32_0.flatMagicDamageMod * arg_32_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 3,
			damagetype = 2,
			damage = function(arg_33_0, arg_33_1, arg_33_2)
				return ({
					150,
					275,
					400
				})[arg_33_2] + 0.65 * (arg_33_0.flatMagicDamageMod * arg_33_0.percentMagicDamageMod)
			end
		}
	},
	ashe = {
		{
			stage = 1,
			slot = 1,
			damagetype = 1,
			damage = function(arg_34_0, arg_34_1, arg_34_2)
				return ({
					20,
					35,
					50,
					65,
					80
				})[arg_34_2] + 1 * ((arg_34_0.baseAttackDamage + arg_34_0.flatPhysicalDamageMod) * arg_34_0.percentPhysicalDamageMod)
			end
		},
		{
			stage = 1,
			slot = 3,
			damagetype = 2,
			damage = function(arg_35_0, arg_35_1, arg_35_2)
				return ({
					200,
					400,
					600
				})[arg_35_2] + 1 * (arg_35_0.flatMagicDamageMod * arg_35_0.percentMagicDamageMod)
			end
		}
	},
	aurelionsol = {
		{
			stage = 1,
			slot = 0,
			damagetype = 2,
			damage = function(arg_36_0, arg_36_1, arg_36_2)
				return ({
					70,
					110,
					150,
					190,
					230
				})[arg_36_2] + 0.65 * (arg_36_0.flatMagicDamageMod * arg_36_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 3,
			damagetype = 2,
			damage = function(arg_37_0, arg_37_1, arg_37_2)
				return ({
					150,
					250,
					350
				})[arg_37_2] + 0.7 * (arg_37_0.flatMagicDamageMod * arg_37_0.percentMagicDamageMod)
			end
		}
	},
	azir = {
		{
			stage = 1,
			slot = 0,
			damagetype = 2,
			damage = function(arg_38_0, arg_38_1, arg_38_2)
				return ({
					70,
					95,
					120,
					145,
					170
				})[arg_38_2] + 0.3 * (arg_38_0.flatMagicDamageMod * arg_38_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 1,
			damagetype = 2,
			damage = function(arg_39_0, arg_39_1, arg_39_2)
				return ({
					60,
					62,
					64,
					66,
					68,
					70,
					72,
					75,
					80,
					85,
					90,
					100,
					110,
					120,
					130,
					140,
					150,
					160
				})[arg_39_0.levelRef] + 0.6 * (arg_39_0.flatMagicDamageMod * arg_39_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 2,
			damagetype = 2,
			damage = function(arg_40_0, arg_40_1, arg_40_2)
				return ({
					60,
					90,
					120,
					150,
					180
				})[arg_40_2] + 0.4 * (arg_40_0.flatMagicDamageMod * arg_40_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 3,
			damagetype = 2,
			damage = function(arg_41_0, arg_41_1, arg_41_2)
				return ({
					150,
					250,
					450
				})[arg_41_2] + 0.6 * (arg_41_0.flatMagicDamageMod * arg_41_0.percentMagicDamageMod)
			end
		}
	},
	bard = {
		{
			stage = 1,
			slot = 0,
			damagetype = 2,
			damage = function(arg_42_0, arg_42_1, arg_42_2)
				return ({
					80,
					125,
					170,
					215,
					260
				})[arg_42_2] + 0.65 * (arg_42_0.flatMagicDamageMod * arg_42_0.percentMagicDamageMod)
			end
		}
	},
	blitzcrank = {
		{
			stage = 1,
			slot = 0,
			damagetype = 2,
			damage = function(arg_43_0, arg_43_1, arg_43_2)
				return ({
					80,
					135,
					190,
					245,
					300
				})[arg_43_2] + 1 * (arg_43_0.flatMagicDamageMod * arg_43_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 2,
			damagetype = 1,
			damage = function(arg_44_0, arg_44_1, arg_44_2)
				return (arg_44_0.baseAttackDamage + arg_44_0.flatPhysicalDamageMod) * arg_44_0.percentPhysicalDamageMod
			end
		},
		{
			stage = 1,
			slot = 3,
			damagetype = 2,
			damage = function(arg_45_0, arg_45_1, arg_45_2)
				return ({
					250,
					375,
					500
				})[arg_45_2] + 1 * (arg_45_0.flatMagicDamageMod * arg_45_0.percentMagicDamageMod)
			end
		}
	},
	brand = {
		{
			stage = 1,
			slot = 0,
			damagetype = 2,
			damage = function(arg_46_0, arg_46_1, arg_46_2)
				return ({
					80,
					110,
					140,
					170,
					200
				})[arg_46_2] + 0.55 * (arg_46_0.flatMagicDamageMod * arg_46_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 1,
			damagetype = 2,
			damage = function(arg_47_0, arg_47_1, arg_47_2)
				return ({
					75,
					120,
					165,
					210,
					255
				})[arg_47_2] + 0.6 * (arg_47_0.flatMagicDamageMod * arg_47_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 2,
			damagetype = 2,
			damage = function(arg_48_0, arg_48_1, arg_48_2)
				return ({
					70,
					90,
					110,
					130,
					150
				})[arg_48_2] + 0.35 * (arg_48_0.flatMagicDamageMod * arg_48_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 3,
			damagetype = 2,
			damage = function(arg_49_0, arg_49_1, arg_49_2)
				return ({
					100,
					200,
					300
				})[arg_49_2] + 0.25 * (arg_49_0.flatMagicDamageMod * arg_49_0.percentMagicDamageMod)
			end
		}
	},
	braum = {
		{
			stage = 1,
			slot = 0,
			damagetype = 2,
			damage = function(arg_50_0, arg_50_1, arg_50_2)
				return ({
					60,
					105,
					150,
					195,
					240
				})[arg_50_2] + 0.025 * arg_50_0.maxHealth
			end
		},
		{
			stage = 1,
			slot = 3,
			damagetype = 2,
			damage = function(arg_51_0, arg_51_1, arg_51_2)
				return ({
					150,
					250,
					350
				})[arg_51_2] + 0.6 * (arg_51_0.flatMagicDamageMod * arg_51_0.percentMagicDamageMod)
			end
		}
	},
	caitlyn = {
		{
			stage = 1,
			slot = 0,
			damagetype = 1,
			damage = function(arg_52_0, arg_52_1, arg_52_2)
				return ({
					30,
					70,
					110,
					150,
					190
				})[arg_52_2] + ({
					1.3,
					1.4,
					1.5,
					1.6,
					1.7
				})[arg_52_2] * ((arg_52_0.baseAttackDamage + arg_52_0.flatPhysicalDamageMod) * arg_52_0.percentPhysicalDamageMod)
			end
		},
		{
			stage = 1,
			slot = 2,
			damagetype = 2,
			damage = function(arg_53_0, arg_53_1, arg_53_2)
				return ({
					70,
					110,
					150,
					190,
					230
				})[arg_53_2] + 0.8 * (arg_53_0.flatMagicDamageMod * arg_53_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 3,
			damagetype = 1,
			damage = function(arg_54_0, arg_54_1, arg_54_2)
				return ({
					250,
					475,
					700
				})[arg_54_2] + 2 * ((arg_54_0.baseAttackDamage + arg_54_0.flatPhysicalDamageMod) * arg_54_0.percentPhysicalDamageMod)
			end
		}
	},
	camille = {
		{
			stage = 1,
			slot = 0,
			damagetype = 1,
			damage = function(arg_55_0, arg_55_1, arg_55_2)
				return ({
					0.2,
					0.25,
					0.3,
					0.35,
					0.4
				})[arg_55_2] * ((arg_55_0.baseAttackDamage + arg_55_0.flatPhysicalDamageMod) * arg_55_0.percentPhysicalDamageMod)
			end
		},
		{
			stage = 2,
			slot = 0,
			damagetype = 1,
			damage = function(arg_56_0, arg_56_1, arg_56_2)
				return ({
					0.4,
					0.5,
					0.6,
					0.7,
					0.8
				})[arg_56_2] * ((arg_56_0.baseAttackDamage + arg_56_0.flatPhysicalDamageMod) * arg_56_0.percentPhysicalDamageMod)
			end
		},
		{
			stage = 1,
			slot = 1,
			damagetype = 1,
			damage = function(arg_57_0, arg_57_1, arg_57_2)
				return ({
					70,
					100,
					130,
					160,
					190
				})[arg_57_2] + 0.6 * ((arg_57_0.baseAttackDamage + arg_57_0.flatPhysicalDamageMod) * arg_57_0.percentPhysicalDamageMod - arg_57_0.baseAttackDamage)
			end
		},
		{
			stage = 1,
			slot = 2,
			damagetype = 1,
			damage = function(arg_58_0, arg_58_1, arg_58_2)
				return ({
					75,
					120,
					165,
					210,
					255
				})[arg_58_2] + 0.75 * ((arg_58_0.baseAttackDamage + arg_58_0.flatPhysicalDamageMod) * arg_58_0.percentPhysicalDamageMod - arg_58_0.baseAttackDamage)
			end
		},
		{
			stage = 1,
			slot = 3,
			damagetype = 2,
			damage = function(arg_59_0, arg_59_1, arg_59_2)
				return ({
					5,
					10,
					15
				})[arg_59_2] + ({
					0.04,
					0.06,
					0.08
				})[arg_59_2] * arg_59_1.health
			end
		}
	},
	cassiopeia = {
		{
			stage = 1,
			slot = 0,
			damagetype = 2,
			damage = function(arg_60_0, arg_60_1, arg_60_2)
				return ({
					75,
					120,
					165,
					210,
					255
				})[arg_60_2] + 0.7 * (arg_60_0.flatMagicDamageMod * arg_60_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 1,
			damagetype = 2,
			damage = function(arg_61_0, arg_61_1, arg_61_2)
				return ({
					20,
					35,
					50,
					65,
					80
				})[arg_61_2] + 0.15 * (arg_61_0.flatMagicDamageMod * arg_61_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 2,
			damagetype = 2,
			damage = function(arg_62_0, arg_62_1, arg_62_2)
				return 48 + 4 * arg_62_0.levelRef + 0.1 * (arg_62_0.flatMagicDamageMod * arg_62_0.percentMagicDamageMod) + ({
					10,
					30,
					50,
					70,
					90
				})[arg_62_2] + 0.6 * (arg_62_0.flatMagicDamageMod * arg_62_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 3,
			damagetype = 2,
			damage = function(arg_63_0, arg_63_1, arg_63_2)
				return ({
					150,
					250,
					350
				})[arg_63_2] + 0.5 * (arg_63_0.flatMagicDamageMod * arg_63_0.percentMagicDamageMod)
			end
		}
	},
	chogath = {
		{
			stage = 1,
			slot = 0,
			damagetype = 2,
			damage = function(arg_64_0, arg_64_1, arg_64_2)
				return ({
					80,
					135,
					190,
					245,
					305
				})[arg_64_2] + arg_64_0.flatMagicDamageMod * arg_64_0.percentMagicDamageMod
			end
		},
		{
			stage = 1,
			slot = 1,
			damagetype = 2,
			damage = function(arg_65_0, arg_65_1, arg_65_2)
				return ({
					75,
					125,
					175,
					225,
					275
				})[arg_65_2] + 0.7 * (arg_65_0.flatMagicDamageMod * arg_65_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 2,
			damagetype = 2,
			damage = function(arg_66_0, arg_66_1, arg_66_2)
				return ({
					20,
					30,
					40,
					50,
					60
				})[arg_66_2] + 0.3 * (arg_66_0.flatMagicDamageMod * arg_66_0.percentMagicDamageMod) + 0.03 * arg_66_1.maxHealth
			end
		},
		{
			stage = 1,
			slot = 3,
			damagetype = 3,
			damage = function(arg_67_0, arg_67_1, arg_67_2)
				return ({
					300,
					475,
					650
				})[arg_67_2] + 0.5 * (arg_67_0.flatMagicDamageMod * arg_67_0.percentMagicDamageMod)
			end
		}
	},
	corki = {
		{
			stage = 1,
			slot = 0,
			damagetype = 2,
			damage = function(arg_68_0, arg_68_1, arg_68_2)
				return ({
					75,
					115,
					155,
					195,
					235
				})[arg_68_2] + 0.5 * (arg_68_0.flatMagicDamageMod * arg_68_0.percentMagicDamageMod) + 0.5 * ((arg_68_0.baseAttackDamage + arg_68_0.flatPhysicalDamageMod) * arg_68_0.percentPhysicalDamageMod - arg_68_0.baseAttackDamage)
			end
		},
		{
			stage = 1,
			slot = 1,
			damagetype = 2,
			damage = function(arg_69_0, arg_69_1, arg_69_2)
				return ({
					60,
					90,
					120,
					150,
					180
				})[arg_69_2] + 0.4 * (arg_69_0.flatMagicDamageMod * arg_69_0.percentMagicDamageMod)
			end
		},
		{
			stage = 2,
			slot = 1,
			damagetype = 2,
			damage = function(arg_70_0, arg_70_1, arg_70_2)
				return ({
					30,
					30,
					30,
					30,
					30,
					30,
					30,
					35,
					40,
					45,
					50,
					55,
					60,
					65,
					70,
					80,
					90,
					100
				})[arg_70_0.levelRef] + 1.5 * ((arg_70_0.baseAttackDamage + arg_70_0.flatPhysicalDamageMod) * arg_70_0.percentPhysicalDamageMod - arg_70_0.baseAttackDamage) + 0.2 * (arg_70_0.flatMagicDamageMod * arg_70_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 2,
			damagetype = 1,
			damage = function(arg_71_0, arg_71_1, arg_71_2)
				return ({
					20,
					35,
					50,
					65,
					80
				})[arg_71_2] + 0.1 * ((arg_71_0.baseAttackDamage + arg_71_0.flatPhysicalDamageMod) * arg_71_0.percentPhysicalDamageMod - arg_71_0.baseAttackDamage)
			end
		},
		{
			stage = 1,
			slot = 3,
			damagetype = 2,
			damage = function(arg_72_0, arg_72_1, arg_72_2)
				return ({
					75,
					100,
					125
				})[arg_72_2] + 0.2 * (arg_72_0.flatMagicDamageMod * arg_72_0.percentMagicDamageMod) + ({
					0.15,
					0.45,
					0.75
				})[arg_72_2] * ((arg_72_0.baseAttackDamage + arg_72_0.flatPhysicalDamageMod) * arg_72_0.percentPhysicalDamageMod)
			end
		},
		{
			stage = 2,
			slot = 3,
			damagetype = 2,
			damage = function(arg_73_0, arg_73_1, arg_73_2)
				return ({
					150,
					200,
					250
				})[arg_73_2] + 0.4 * (arg_73_0.flatMagicDamageMod * arg_73_0.percentMagicDamageMod) + ({
					0.3,
					0.9,
					1.5
				})[arg_73_2] * ((arg_73_0.baseAttackDamage + arg_73_0.flatPhysicalDamageMod) * arg_73_0.percentPhysicalDamageMod)
			end
		}
	},
	darius = {
		{
			stage = 1,
			slot = 0,
			damagetype = 1,
			damage = function(arg_74_0, arg_74_1, arg_74_2)
				return ({
					50,
					80,
					110,
					140,
					170
				})[arg_74_2] + ({
					1,
					1.1,
					1.2,
					1.3,
					1.4
				})[arg_74_2] * ((arg_74_0.baseAttackDamage + arg_74_0.flatPhysicalDamageMod) * arg_74_0.percentPhysicalDamageMod)
			end
		},
		{
			stage = 1,
			slot = 1,
			damagetype = 1,
			damage = function(arg_75_0, arg_75_1, arg_75_2)
				return 0.4 * ((arg_75_0.baseAttackDamage + arg_75_0.flatPhysicalDamageMod) * arg_75_0.percentPhysicalDamageMod)
			end
		},
		{
			stage = 1,
			slot = 3,
			damagetype = 3,
			damage = function(arg_76_0, arg_76_1, arg_76_2)
				return ({
					100,
					200,
					300
				})[arg_76_2] + 0.75 * ((arg_76_0.baseAttackDamage + arg_76_0.flatPhysicalDamageMod) * arg_76_0.percentPhysicalDamageMod - arg_76_0.baseAttackDamage)
			end
		}
	},
	diana = {
		{
			stage = 1,
			slot = 0,
			damagetype = 2,
			damage = function(arg_77_0, arg_77_1, arg_77_2)
				return ({
					60,
					95,
					130,
					165,
					200
				})[arg_77_2] + 0.7 * (arg_77_0.flatMagicDamageMod * arg_77_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 1,
			damagetype = 2,
			damage = function(arg_78_0, arg_78_1, arg_78_2)
				return ({
					22,
					34,
					46,
					58,
					70
				})[arg_78_2] + 0.2 * (arg_78_0.flatMagicDamageMod * arg_78_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 3,
			damagetype = 2,
			damage = function(arg_79_0, arg_79_1, arg_79_2)
				return ({
					100,
					160,
					220
				})[arg_79_2] + 0.6 * (arg_79_0.flatMagicDamageMod * arg_79_0.percentMagicDamageMod)
			end
		}
	},
	drmundo = {
		{
			stage = 1,
			slot = 0,
			damagetype = 2,
			damage = function(arg_80_0, arg_80_1, arg_80_2)
				return ({
					0.15,
					0.175,
					0.2,
					0.225,
					0.25
				})[arg_80_2] * arg_80_1.health
			end
		},
		{
			stage = 1,
			slot = 1,
			damagetype = 2,
			damage = function(arg_81_0, arg_81_1, arg_81_2)
				return ({
					40,
					55,
					70,
					85,
					100
				})[arg_81_2] + 0.1 * (arg_81_0.flatMagicDamageMod * arg_81_0.percentMagicDamageMod)
			end
		}
	},
	draven = {
		{
			stage = 1,
			slot = 0,
			damagetype = 1,
			damage = function(arg_82_0, arg_82_1, arg_82_2)
				return ({
					35,
					40,
					45,
					50,
					55
				})[arg_82_2] + ({
					0.65,
					0.75,
					0.85,
					0.95,
					1.05
				})[arg_82_2] * ((arg_82_0.baseAttackDamage + arg_82_0.flatPhysicalDamageMod) * arg_82_0.percentPhysicalDamageMod - arg_82_0.baseAttackDamage)
			end
		},
		{
			stage = 1,
			slot = 2,
			damagetype = 1,
			damage = function(arg_83_0, arg_83_1, arg_83_2)
				return ({
					75,
					110,
					145,
					180,
					215
				})[arg_83_2] + 0.5 * ((arg_83_0.baseAttackDamage + arg_83_0.flatPhysicalDamageMod) * arg_83_0.percentPhysicalDamageMod - arg_83_0.baseAttackDamage)
			end
		},
		{
			stage = 1,
			slot = 3,
			damagetype = 1,
			damage = function(arg_84_0, arg_84_1, arg_84_2)
				return ({
					175,
					275,
					375
				})[arg_84_2] + 1.1 * ((arg_84_0.baseAttackDamage + arg_84_0.flatPhysicalDamageMod) * arg_84_0.percentPhysicalDamageMod - arg_84_0.baseAttackDamage)
			end
		}
	},
	ekko = {
		{
			stage = 1,
			slot = 0,
			damagetype = 2,
			damage = function(arg_85_0, arg_85_1, arg_85_2)
				return ({
					60,
					75,
					90,
					105,
					120
				})[arg_85_2] + 0.3 * (arg_85_0.flatMagicDamageMod * arg_85_0.percentMagicDamageMod)
			end
		},
		{
			stage = 2,
			slot = 0,
			damagetype = 2,
			damage = function(arg_86_0, arg_86_1, arg_86_2)
				return ({
					40,
					65,
					90,
					115,
					140
				})[arg_86_2] + 0.6 * (arg_86_0.flatMagicDamageMod * arg_86_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 2,
			damagetype = 2,
			damage = function(arg_87_0, arg_87_1, arg_87_2)
				return ({
					40,
					65,
					90,
					115,
					140
				})[arg_87_2] + 0.4 * (arg_87_0.flatMagicDamageMod * arg_87_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 3,
			damagetype = 2,
			damage = function(arg_88_0, arg_88_1, arg_88_2)
				return ({
					150,
					300,
					450
				})[arg_88_2] + 1.5 * (arg_88_0.flatMagicDamageMod * arg_88_0.percentMagicDamageMod)
			end
		}
	},
	elise = {
		{
			stage = 1,
			slot = 0,
			damagetype = 2,
			damage = function(arg_89_0, arg_89_1, arg_89_2)
				return ({
					40,
					75,
					110,
					145,
					180
				})[arg_89_2] + (0.04 + 0.03 * (arg_89_0.flatMagicDamageMod * arg_89_0.percentMagicDamageMod / 100)) * arg_89_1.health
			end
		},
		{
			stage = 2,
			slot = 0,
			damagetype = 2,
			damage = function(arg_90_0, arg_90_1, arg_90_2)
				return ({
					60,
					100,
					140,
					180,
					220
				})[arg_90_2] + (0.08 + 0.03 * (arg_90_0.flatMagicDamageMod * arg_90_0.percentMagicDamageMod / 100)) * (arg_90_1.maxHealth - arg_90_1.health)
			end
		},
		{
			stage = 1,
			slot = 1,
			damagetype = 2,
			damage = function(arg_91_0, arg_91_1, arg_91_2)
				return ({
					55,
					95,
					135,
					175,
					215
				})[arg_91_2] + 0.95 * (arg_91_0.flatMagicDamageMod * arg_91_0.percentMagicDamageMod)
			end
		}
	},
	evelynn = {
		{
			stage = 1,
			slot = 0,
			damagetype = 2,
			damage = function(arg_92_0, arg_92_1, arg_92_2)
				return ({
					25,
					30,
					35,
					40,
					45
				})[arg_92_2] + 0.3 * (arg_92_0.flatMagicDamageMod * arg_92_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 2,
			damagetype = 2,
			damage = function(arg_93_0, arg_93_1, arg_93_2)
				return ({
					30,
					45,
					60,
					75,
					90
				})[arg_93_2] + (0.03 + 0.015 * (arg_93_0.flatMagicDamageMod * arg_93_0.percentMagicDamageMod / 100)) * arg_93_1.maxHealth
			end
		},
		{
			stage = 2,
			slot = 2,
			damagetype = 2,
			damage = function(arg_94_0, arg_94_1, arg_94_2)
				return ({
					60,
					80,
					100,
					120,
					140
				})[arg_94_2] + (0.04 + 0.025 * (arg_94_0.flatMagicDamageMod * arg_94_0.percentMagicDamageMod / 100)) * arg_94_1.maxHealth
			end
		},
		{
			stage = 1,
			slot = 3,
			damagetype = 2,
			damage = function(arg_95_0, arg_95_1, arg_95_2)
				return ({
					150,
					275,
					400
				})[arg_95_2] + 0.75 * (arg_95_0.flatMagicDamageMod * arg_95_0.percentMagicDamageMod)
			end
		}
	},
	ezreal = {
		{
			stage = 1,
			slot = 0,
			damagetype = 1,
			damage = function(arg_96_0, arg_96_1, arg_96_2)
				return ({
					20,
					45,
					70,
					95,
					120
				})[arg_96_2] + 0.15 * (arg_96_0.flatMagicDamageMod * arg_96_0.percentMagicDamageMod) + 1.2 * ((arg_96_0.baseAttackDamage + arg_96_0.flatPhysicalDamageMod) * arg_96_0.percentPhysicalDamageMod)
			end
		},
		{
			stage = 1,
			slot = 1,
			damagetype = 2,
			damage = function(arg_97_0, arg_97_1, arg_97_2)
				return ({
					70,
					115,
					160,
					205,
					250
				})[arg_97_2] + 0.8 * (arg_97_0.flatMagicDamageMod * arg_97_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 2,
			damagetype = 2,
			damage = function(arg_98_0, arg_98_1, arg_98_2)
				return ({
					80,
					130,
					180,
					230,
					280
				})[arg_98_2] + 0.75 * (arg_98_0.flatMagicDamageMod * arg_98_0.percentMagicDamageMod) + 0.5 * ((arg_98_0.baseAttackDamage + arg_98_0.flatPhysicalDamageMod) * arg_98_0.percentPhysicalDamageMod - arg_98_0.baseAttackDamage)
			end
		},
		{
			stage = 1,
			slot = 3,
			damagetype = 2,
			damage = function(arg_99_0, arg_99_1, arg_99_2)
				return ({
					350,
					500,
					650
				})[arg_99_2] + 0.9 * (arg_99_0.flatMagicDamageMod * arg_99_0.percentMagicDamageMod) + 1 * ((arg_99_0.baseAttackDamage + arg_99_0.flatPhysicalDamageMod) * arg_99_0.percentPhysicalDamageMod - arg_99_0.baseAttackDamage)
			end
		}
	},
	fiddlesticks = {
		{
			stage = 1,
			slot = 1,
			damagetype = 2,
			damage = function(arg_100_0, arg_100_1, arg_100_2)
				return ({
					80,
					105,
					130,
					155,
					180
				})[arg_100_2] + 0.45 * (arg_100_0.flatMagicDamageMod * arg_100_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 2,
			damagetype = 2,
			damage = function(arg_101_0, arg_101_1, arg_101_2)
				return ({
					65,
					85,
					105,
					125,
					145
				})[arg_101_2] + 0.45 * (arg_101_0.flatMagicDamageMod * arg_101_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 3,
			damagetype = 2,
			damage = function(arg_102_0, arg_102_1, arg_102_2)
				return ({
					125,
					225,
					325
				})[arg_102_2] + 0.45 * (arg_102_0.flatMagicDamageMod * arg_102_0.percentMagicDamageMod)
			end
		}
	},
	fiora = {
		{
			stage = 1,
			slot = 0,
			damagetype = 1,
			damage = function(arg_103_0, arg_103_1, arg_103_2)
				return ({
					70,
					80,
					90,
					100,
					110
				})[arg_103_2] + ({
					0.95,
					1,
					1.05,
					1.1,
					1.15
				})[arg_103_2] * ((arg_103_0.baseAttackDamage + arg_103_0.flatPhysicalDamageMod) * arg_103_0.percentPhysicalDamageMod - arg_103_0.baseAttackDamage)
			end
		},
		{
			stage = 1,
			slot = 1,
			damagetype = 2,
			damage = function(arg_104_0, arg_104_1, arg_104_2)
				return ({
					90,
					130,
					170,
					210,
					250
				})[arg_104_2] + arg_104_0.flatMagicDamageMod * arg_104_0.percentMagicDamageMod
			end
		}
	},
	fizz = {
		{
			stage = 1,
			slot = 0,
			damagetype = 2,
			damage = function(arg_105_0, arg_105_1, arg_105_2)
				return ({
					10,
					25,
					40,
					55,
					70
				})[arg_105_2] + 0.55 * (arg_105_0.flatMagicDamageMod * arg_105_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 1,
			damagetype = 2,
			damage = function(arg_106_0, arg_106_1, arg_106_2)
				return ({
					20,
					30,
					40,
					50,
					60
				})[arg_106_2] + 0.4 * (arg_106_0.flatMagicDamageMod * arg_106_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 2,
			damagetype = 2,
			damage = function(arg_107_0, arg_107_1, arg_107_2)
				return ({
					70,
					120,
					170,
					220,
					270
				})[arg_107_2] + 0.75 * (arg_107_0.flatMagicDamageMod * arg_107_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 3,
			damagetype = 2,
			damage = function(arg_108_0, arg_108_1, arg_108_2)
				return ({
					150,
					250,
					350
				})[arg_108_2] + 0.6 * (arg_108_0.flatMagicDamageMod * arg_108_0.percentMagicDamageMod)
			end
		},
		{
			stage = 2,
			slot = 3,
			damagetype = 2,
			damage = function(arg_109_0, arg_109_1, arg_109_2)
				return ({
					225,
					325,
					425
				})[arg_109_2] + 0.8 * (arg_109_0.flatMagicDamageMod * arg_109_0.percentMagicDamageMod)
			end
		},
		{
			stage = 3,
			slot = 3,
			damagetype = 2,
			damage = function(arg_110_0, arg_110_1, arg_110_2)
				return ({
					300,
					400,
					500
				})[arg_110_2] + 1.2 * (arg_110_0.flatMagicDamageMod * arg_110_0.percentMagicDamageMod)
			end
		}
	},
	galio = {
		{
			stage = 1,
			slot = 0,
			damagetype = 2,
			damage = function(arg_111_0, arg_111_1, arg_111_2)
				return ({
					50,
					80,
					110,
					140,
					170
				})[arg_111_2] + 0.8 * (arg_111_0.flatMagicDamageMod * arg_111_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 2,
			damagetype = 2,
			damage = function(arg_112_0, arg_112_1, arg_112_2)
				return ({
					100,
					130,
					160,
					190,
					220
				})[arg_112_2] + 0.9 * (arg_112_0.flatMagicDamageMod * arg_112_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 3,
			damagetype = 2,
			damage = function(arg_113_0, arg_113_1, arg_113_2)
				return ({
					150,
					250,
					350
				})[arg_113_2] + 0.7 * (arg_113_0.flatMagicDamageMod * arg_113_0.percentMagicDamageMod)
			end
		}
	},
	gangplank = {
		{
			stage = 1,
			slot = 0,
			damagetype = 1,
			damage = function(arg_114_0, arg_114_1, arg_114_2)
				return ({
					20,
					45,
					70,
					95,
					120
				})[arg_114_2] + (arg_114_0.baseAttackDamage + arg_114_0.flatPhysicalDamageMod) * arg_114_0.percentPhysicalDamageMod
			end
		},
		{
			stage = 1,
			slot = 3,
			damagetype = 2,
			damage = function(arg_115_0, arg_115_1, arg_115_2)
				return ({
					35,
					60,
					85
				})[arg_115_2] + 0.1 * (arg_115_0.flatMagicDamageMod * arg_115_0.percentMagicDamageMod)
			end
		}
	},
	garen = {
		{
			stage = 1,
			slot = 0,
			damagetype = 1,
			damage = function(arg_116_0, arg_116_1, arg_116_2)
				return ({
					30,
					55,
					80,
					105,
					130
				})[arg_116_2] + 0.4 * ((arg_116_0.baseAttackDamage + arg_116_0.flatPhysicalDamageMod) * arg_116_0.percentPhysicalDamageMod)
			end
		},
		{
			stage = 1,
			slot = 2,
			damagetype = 1,
			damage = function(arg_117_0, arg_117_1, arg_117_2)
				return ({
					14,
					18,
					22,
					26,
					30
				})[arg_117_2] + ({
					0.36,
					0.37,
					0.38,
					0.39,
					0.4
				})[arg_117_2] * ((arg_117_0.baseAttackDamage + arg_117_0.flatPhysicalDamageMod) * arg_117_0.percentPhysicalDamageMod)
			end
		},
		{
			stage = 1,
			slot = 3,
			damagetype = 2,
			damage = function(arg_118_0, arg_118_1, arg_118_2)
				return ({
					175,
					350,
					525
				})[arg_118_2] + ({
					0.286,
					0.333,
					0.4
				})[arg_118_2] * (arg_118_1.maxHealth - arg_118_1.health)
			end
		}
	},
	gnar = {
		{
			stage = 1,
			slot = 0,
			damagetype = 1,
			damage = function(arg_119_0, arg_119_1, arg_119_2)
				return ({
					5,
					45,
					85,
					125,
					165
				})[arg_119_2] + 1.15 * ((arg_119_0.baseAttackDamage + arg_119_0.flatPhysicalDamageMod) * arg_119_0.percentPhysicalDamageMod)
			end
		},
		{
			stage = 2,
			slot = 0,
			damagetype = 1,
			damage = function(arg_120_0, arg_120_1, arg_120_2)
				return ({
					5,
					45,
					85,
					125,
					165
				})[arg_120_2] + 1.2 * ((arg_120_0.baseAttackDamage + arg_120_0.flatPhysicalDamageMod) * arg_120_0.percentPhysicalDamageMod)
			end
		},
		{
			stage = 1,
			slot = 1,
			damagetype = 2,
			damage = function(arg_121_0, arg_121_1, arg_121_2)
				return ({
					10,
					20,
					30,
					40,
					50
				})[arg_121_2] + arg_121_0.flatMagicDamageMod * arg_121_0.percentMagicDamageMod + ({
					6,
					8,
					10,
					12,
					14
				})[arg_121_2] / 100 * arg_121_1.maxHealth
			end
		},
		{
			stage = 2,
			slot = 1,
			damagetype = 1,
			damage = function(arg_122_0, arg_122_1, arg_122_2)
				return ({
					25,
					45,
					65,
					85,
					105
				})[arg_122_2] + (arg_122_0.baseAttackDamage + arg_122_0.flatPhysicalDamageMod) * arg_122_0.percentPhysicalDamageMod
			end
		},
		{
			stage = 1,
			slot = 2,
			damagetype = 1,
			damage = function(arg_123_0, arg_123_1, arg_123_2)
				return ({
					20,
					60,
					100,
					140,
					180
				})[arg_123_2] + arg_123_0.maxHealth * 0.06
			end
		},
		{
			stage = 2,
			slot = 2,
			damagetype = 1,
			damage = function(arg_124_0, arg_124_1, arg_124_2)
				return ({
					20,
					60,
					100,
					140,
					180
				})[arg_124_2] + arg_124_0.maxHealth * 0.06
			end
		},
		{
			stage = 1,
			slot = 3,
			damagetype = 1,
			damage = function(arg_125_0, arg_125_1, arg_125_2)
				return ({
					200,
					300,
					400
				})[arg_125_2] + 0.5 * (arg_125_0.flatMagicDamageMod * arg_125_0.percentMagicDamageMod) + 0.2 * ((arg_125_0.baseAttackDamage + arg_125_0.flatPhysicalDamageMod) * arg_125_0.percentPhysicalDamageMod - arg_125_0.baseAttackDamage)
			end
		}
	},
	gragas = {
		{
			stage = 1,
			slot = 0,
			damagetype = 2,
			damage = function(arg_126_0, arg_126_1, arg_126_2)
				return ({
					80,
					120,
					160,
					200,
					240
				})[arg_126_2] + 0.6 * (arg_126_0.flatMagicDamageMod * arg_126_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 1,
			damagetype = 2,
			damage = function(arg_127_0, arg_127_1, arg_127_2)
				return ({
					20,
					50,
					80,
					110,
					140
				})[arg_127_2] + 0.08 * arg_127_1.maxHealth + 0.3 * (arg_127_0.flatMagicDamageMod * arg_127_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 2,
			damagetype = 2,
			damage = function(arg_128_0, arg_128_1, arg_128_2)
				return ({
					80,
					130,
					180,
					230,
					280
				})[arg_128_2] + 0.6 * (arg_128_0.flatMagicDamageMod * arg_128_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 3,
			damagetype = 2,
			damage = function(arg_129_0, arg_129_1, arg_129_2)
				return ({
					200,
					300,
					400
				})[arg_129_2] + 0.7 * (arg_129_0.flatMagicDamageMod * arg_129_0.percentMagicDamageMod)
			end
		}
	},
	graves = {
		{
			stage = 1,
			slot = 0,
			damagetype = 1,
			damage = function(arg_130_0, arg_130_1, arg_130_2)
				return ({
					45,
					60,
					75,
					90,
					105
				})[arg_130_2] + ((arg_130_0.baseAttackDamage + arg_130_0.flatPhysicalDamageMod) * arg_130_0.percentPhysicalDamageMod - arg_130_0.baseAttackDamage)
			end
		},
		{
			stage = 2,
			slot = 0,
			damagetype = 1,
			damage = function(arg_131_0, arg_131_1, arg_131_2)
				return ({
					85,
					115,
					145,
					175,
					205
				})[arg_131_2] + ({
					0.4,
					0.7,
					1,
					1.3,
					1.6
				})[arg_131_2] * ((arg_131_0.baseAttackDamage + arg_131_0.flatPhysicalDamageMod) * arg_131_0.percentPhysicalDamageMod - arg_131_0.baseAttackDamage)
			end
		},
		{
			stage = 1,
			slot = 1,
			damagetype = 2,
			damage = function(arg_132_0, arg_132_1, arg_132_2)
				return ({
					60,
					110,
					160,
					210,
					260
				})[arg_132_2] + 0.6 * (arg_132_0.flatMagicDamageMod * arg_132_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 3,
			damagetype = 1,
			damage = function(arg_133_0, arg_133_1, arg_133_2)
				return ({
					250,
					400,
					550
				})[arg_133_2] + 1.5 * ((arg_133_0.baseAttackDamage + arg_133_0.flatPhysicalDamageMod) * arg_133_0.percentPhysicalDamageMod - arg_133_0.baseAttackDamage)
			end
		}
	},
	hecarim = {
		{
			stage = 1,
			slot = 0,
			damagetype = 1,
			damage = function(arg_134_0, arg_134_1, arg_134_2)
				return ({
					50,
					90,
					125,
					160,
					195
				})[arg_134_2] + 0.6 * ((arg_134_0.baseAttackDamage + arg_134_0.flatPhysicalDamageMod) * arg_134_0.percentPhysicalDamageMod - arg_134_0.baseAttackDamage)
			end
		},
		{
			stage = 1,
			slot = 1,
			damagetype = 2,
			damage = function(arg_135_0, arg_135_1, arg_135_2)
				return ({
					20,
					30,
					40,
					50,
					60
				})[arg_135_2] + 0.2 * (arg_135_0.flatMagicDamageMod * arg_135_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 2,
			damagetype = 1,
			damage = function(arg_136_0, arg_136_1, arg_136_2)
				return ({
					45,
					80,
					115,
					150,
					185
				})[arg_136_2] + 0.5 * ((arg_136_0.baseAttackDamage + arg_136_0.flatPhysicalDamageMod) * arg_136_0.percentPhysicalDamageMod - arg_136_0.baseAttackDamage)
			end
		},
		{
			stage = 1,
			slot = 3,
			damagetype = 2,
			damage = function(arg_137_0, arg_137_1, arg_137_2)
				return ({
					150,
					250,
					350
				})[arg_137_2] + arg_137_0.flatMagicDamageMod * arg_137_0.percentMagicDamageMod
			end
		}
	},
	heimerdinger = {
		{
			stage = 1,
			slot = 0,
			damagetype = 2,
			damage = function(arg_138_0, arg_138_1, arg_138_2)
				return ({
					6,
					9,
					12,
					15,
					18
				})[arg_138_2] + 0.3 * (arg_138_0.flatMagicDamageMod * arg_138_0.percentMagicDamageMod)
			end
		},
		{
			stage = 2,
			slot = 0,
			damagetype = 2,
			damage = function(arg_139_0, arg_139_1, arg_139_2)
				return ({
					40,
					60,
					80,
					100,
					120
				})[arg_139_2] + 0.55 * (arg_139_0.flatMagicDamageMod * arg_139_0.percentMagicDamageMod)
			end
		},
		{
			stage = 3,
			slot = 0,
			damagetype = 2,
			damage = function(arg_140_0, arg_140_1, arg_140_2)
				return ({
					80,
					100,
					120
				})[arg_140_0:spellSlot(3).level] + 0.3 * (arg_140_0.flatMagicDamageMod * arg_140_0.percentMagicDamageMod)
			end
		},
		{
			stage = 4,
			slot = 0,
			damagetype = 2,
			damage = function(arg_141_0, arg_141_1, arg_141_2)
				return ({
					100,
					140,
					180
				})[arg_141_0:spellSlot(3).level] + 0.7 * (arg_141_0.flatMagicDamageMod * arg_141_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 1,
			damagetype = 2,
			damage = function(arg_142_0, arg_142_1, arg_142_2)
				return ({
					60,
					90,
					120,
					150,
					180
				})[arg_142_2] + 0.45 * (arg_142_0.flatMagicDamageMod * arg_142_0.percentMagicDamageMod)
			end
		},
		{
			stage = 2,
			slot = 1,
			damagetype = 2,
			damage = function(arg_143_0, arg_143_1, arg_143_2)
				return ({
					135,
					180,
					225
				})[arg_143_0:spellSlot(3).level] + 0.45 * (arg_143_0.flatMagicDamageMod * arg_143_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 2,
			damagetype = 2,
			damage = function(arg_144_0, arg_144_1, arg_144_2)
				return ({
					60,
					100,
					140,
					180,
					220
				})[arg_144_2] + 0.6 * (arg_144_0.flatMagicDamageMod * arg_144_0.percentMagicDamageMod)
			end
		},
		{
			stage = 2,
			slot = 2,
			damagetype = 2,
			damage = function(arg_145_0, arg_145_1, arg_145_2)
				return ({
					150,
					250,
					350
				})[arg_145_0:spellSlot(3).level] + 0.75 * (arg_145_0.flatMagicDamageMod * arg_145_0.percentMagicDamageMod)
			end
		}
	},
	illaoi = {
		{
			stage = 1,
			slot = 0,
			damagetype = 1,
			damage = function(arg_146_0, arg_146_1, arg_146_2)
				return arg_146_0.levelRef * 10 + 1.2 * ((arg_146_0.baseAttackDamage + arg_146_0.flatPhysicalDamageMod) * arg_146_0.percentPhysicalDamageMod) + ({
					0.1,
					0.15,
					0.2,
					0.25,
					0.3
				})[arg_146_2] * (arg_146_0.levelRef * 10 + 1.2 * ((arg_146_0.baseAttackDamage + arg_146_0.flatPhysicalDamageMod) * arg_146_0.percentPhysicalDamageMod))
			end
		},
		{
			stage = 1,
			slot = 1,
			damagetype = 1,
			damage = function(arg_147_0, arg_147_1, arg_147_2)
				return ({
					0.03,
					0.035,
					0.04,
					0.045,
					0.05
				})[arg_147_2] + 0.02 * ((arg_147_0.baseAttackDamage + arg_147_0.flatPhysicalDamageMod) * arg_147_0.percentPhysicalDamageMod / 100) * (arg_147_1.maxHealth - arg_147_1.health)
			end
		},
		{
			stage = 1,
			slot = 3,
			damagetype = 1,
			damage = function(arg_148_0, arg_148_1, arg_148_2)
				return ({
					150,
					250,
					350
				})[arg_148_2] + 0.5 * ((arg_148_0.baseAttackDamage + arg_148_0.flatPhysicalDamageMod) * arg_148_0.percentPhysicalDamageMod - arg_148_0.baseAttackDamage)
			end
		}
	},
	irelia = {
		{
			stage = 1,
			slot = 0,
			damagetype = 1,
			damage = function(arg_149_0, arg_149_1, arg_149_2)
				return ({
					20,
					50,
					80,
					110,
					140
				})[arg_149_2] + 1.2 * ((arg_149_0.baseAttackDamage + arg_149_0.flatPhysicalDamageMod) * arg_149_0.percentPhysicalDamageMod)
			end
		},
		{
			stage = 1,
			slot = 1,
			damagetype = 3,
			damage = function(arg_150_0, arg_150_1, arg_150_2)
				return ({
					15,
					30,
					45,
					60,
					75
				})[arg_150_2]
			end
		},
		{
			stage = 1,
			slot = 2,
			damagetype = 2,
			damage = function(arg_151_0, arg_151_1, arg_151_2)
				return ({
					80,
					120,
					160,
					200,
					240
				})[arg_151_2] + 0.5 * (arg_151_0.flatMagicDamageMod * arg_151_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 3,
			damagetype = 1,
			damage = function(arg_152_0, arg_152_1, arg_152_2)
				return ({
					80,
					120,
					160
				})[arg_152_2] + 0.5 * (arg_152_0.flatMagicDamageMod * arg_152_0.percentMagicDamageMod) + 0.7 * ((arg_152_0.baseAttackDamage + arg_152_0.flatPhysicalDamageMod) * arg_152_0.percentPhysicalDamageMod - arg_152_0.baseAttackDamage)
			end
		}
	},
	ivern = {
		{
			stage = 1,
			slot = 0,
			damagetype = 2,
			damage = function(arg_153_0, arg_153_1, arg_153_2)
				return ({
					80,
					125,
					170,
					215,
					260
				})[arg_153_2] + 0.7 * (arg_153_0.flatMagicDamageMod * arg_153_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 1,
			damagetype = 2,
			damage = function(arg_154_0, arg_154_1, arg_154_2)
				return ({
					20,
					30,
					40,
					50,
					60
				})[arg_154_2] + 0.3 * (arg_154_0.flatMagicDamageMod * arg_154_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 2,
			damagetype = 2,
			damage = function(arg_155_0, arg_155_1, arg_155_2)
				return ({
					60,
					80,
					100,
					120,
					140
				})[arg_155_2] + 0.8 * (arg_155_0.flatMagicDamageMod * arg_155_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 3,
			damagetype = 2,
			damage = function(arg_156_0, arg_156_1, arg_156_2)
				return ({
					70,
					100,
					170
				})[arg_156_2] + 0.3 * (arg_156_0.flatMagicDamageMod * arg_156_0.percentMagicDamageMod)
			end
		}
	},
	janna = {
		{
			stage = 1,
			slot = 0,
			damagetype = 2,
			damage = function(arg_157_0, arg_157_1, arg_157_2)
				return ({
					60,
					85,
					110,
					135,
					160
				})[arg_157_2] + 0.35 * (arg_157_0.flatMagicDamageMod * arg_157_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 1,
			damagetype = 2,
			damage = function(arg_158_0, arg_158_1, arg_158_2)
				return ({
					60,
					115,
					170,
					225,
					280
				})[arg_158_2] + 0.5 * (arg_158_0.flatMagicDamageMod * arg_158_0.percentMagicDamageMod)
			end
		}
	},
	jarvaniv = {
		{
			stage = 1,
			slot = 0,
			damagetype = 1,
			damage = function(arg_159_0, arg_159_1, arg_159_2)
				return ({
					80,
					120,
					160,
					200,
					240
				})[arg_159_2] + 1.2 * ((arg_159_0.baseAttackDamage + arg_159_0.flatPhysicalDamageMod) * arg_159_0.percentPhysicalDamageMod - arg_159_0.baseAttackDamage)
			end
		},
		{
			stage = 1,
			slot = 2,
			damagetype = 2,
			damage = function(arg_160_0, arg_160_1, arg_160_2)
				return ({
					60,
					105,
					150,
					195,
					240
				})[arg_160_2] + 0.8 * (arg_160_0.flatMagicDamageMod * arg_160_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 3,
			damagetype = 1,
			damage = function(arg_161_0, arg_161_1, arg_161_2)
				return ({
					200,
					325,
					450
				})[arg_161_2] + 1.5 * ((arg_161_0.baseAttackDamage + arg_161_0.flatPhysicalDamageMod) * arg_161_0.percentPhysicalDamageMod - arg_161_0.baseAttackDamage)
			end
		}
	},
	jax = {
		{
			stage = 1,
			slot = 0,
			damagetype = 1,
			damage = function(arg_162_0, arg_162_1, arg_162_2)
				return ({
					80,
					120,
					160,
					200,
					240
				})[arg_162_2] + ((arg_162_0.baseAttackDamage + arg_162_0.flatPhysicalDamageMod) * arg_162_0.percentPhysicalDamageMod - arg_162_0.baseAttackDamage) + 0.6 * (arg_162_0.flatMagicDamageMod * arg_162_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 1,
			damagetype = 2,
			damage = function(arg_163_0, arg_163_1, arg_163_2)
				return ({
					40,
					75,
					110,
					145,
					180
				})[arg_163_2] + 0.6 * (arg_163_0.flatMagicDamageMod * arg_163_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 2,
			damagetype = 1,
			damage = function(arg_164_0, arg_164_1, arg_164_2)
				return ({
					55,
					80,
					105,
					130,
					155
				})[arg_164_2] + 0.5 * ((arg_164_0.baseAttackDamage + arg_164_0.flatPhysicalDamageMod) * arg_164_0.percentPhysicalDamageMod - arg_164_0.baseAttackDamage)
			end
		},
		{
			stage = 1,
			slot = 3,
			damagetype = 2,
			damage = function(arg_165_0, arg_165_1, arg_165_2)
				return ({
					100,
					160,
					220
				})[arg_165_2] + 0.7 * (arg_165_0.flatMagicDamageMod * arg_165_0.percentMagicDamageMod)
			end
		}
	},
	jayce = {
		{
			stage = 1,
			slot = 0,
			damagetype = 1,
			damage = function(arg_166_0, arg_166_1, arg_166_2)
				return ({
					70,
					120,
					170,
					220,
					270,
					320
				})[arg_166_2] + 1.2 * ((arg_166_0.baseAttackDamage + arg_166_0.flatPhysicalDamageMod) * arg_166_0.percentPhysicalDamageMod - arg_166_0.baseAttackDamage)
			end
		},
		{
			stage = 2,
			slot = 0,
			damagetype = 1,
			damage = function(arg_167_0, arg_167_1, arg_167_2)
				return ({
					45,
					80,
					115,
					150,
					185,
					220
				})[arg_167_2] + 1.2 * ((arg_167_0.baseAttackDamage + arg_167_0.flatPhysicalDamageMod) * arg_167_0.percentPhysicalDamageMod - arg_167_0.baseAttackDamage)
			end
		},
		{
			stage = 1,
			slot = 1,
			damagetype = 2,
			damage = function(arg_168_0, arg_168_1, arg_168_2)
				return ({
					25,
					40,
					55,
					70,
					85,
					100
				})[arg_168_2] + 0.25 * (arg_168_0.flatMagicDamageMod * arg_168_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 2,
			damagetype = 2,
			damage = function(arg_169_0, arg_169_1, arg_169_2)
				return ({
					8,
					10.4,
					12.8,
					15.2,
					17.6,
					20
				})[arg_169_2] / 100 * arg_169_1.maxHealth + ((arg_169_0.baseAttackDamage + arg_169_0.flatPhysicalDamageMod) * arg_169_0.percentPhysicalDamageMod - arg_169_0.baseAttackDamage)
			end
		},
		{
			stage = 1,
			slot = 3,
			damagetype = 1,
			damage = function(arg_170_0, arg_170_1, arg_170_2)
				return ({
					25,
					65,
					105,
					145
				})[arg_170_2] + 0.25 * ((arg_170_0.baseAttackDamage + arg_170_0.flatPhysicalDamageMod) * arg_170_0.percentPhysicalDamageMod - arg_170_0.baseAttackDamage)
			end
		}
	},
	jhin = {
		{
			stage = 1,
			slot = 0,
			damagetype = 1,
			damage = function(arg_171_0, arg_171_1, arg_171_2)
				return ({
					45,
					70,
					95,
					120,
					145
				})[arg_171_2] + ({
					0.4,
					0.45,
					0.5,
					0.55,
					0.6
				})[arg_171_2] * ((arg_171_0.baseAttackDamage + arg_171_0.flatPhysicalDamageMod) * arg_171_0.percentPhysicalDamageMod) + 0.6 * (arg_171_0.flatMagicDamageMod * arg_171_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 1,
			damagetype = 1,
			damage = function(arg_172_0, arg_172_1, arg_172_2)
				return ({
					50,
					85,
					120,
					155,
					190
				})[arg_172_2] + 0.5 * ((arg_172_0.baseAttackDamage + arg_172_0.flatPhysicalDamageMod) * arg_172_0.percentPhysicalDamageMod)
			end
		},
		{
			stage = 1,
			slot = 2,
			damagetype = 2,
			damage = function(arg_173_0, arg_173_1, arg_173_2)
				return ({
					20,
					80,
					140,
					200,
					260
				})[arg_173_2] + 1.2 * ((arg_173_0.baseAttackDamage + arg_173_0.flatPhysicalDamageMod) * arg_173_0.percentPhysicalDamageMod) + arg_173_0.flatMagicDamageMod * arg_173_0.percentMagicDamageMod
			end
		},
		{
			stage = 1,
			slot = 3,
			damagetype = 1,
			damage = function(arg_174_0, arg_174_1, arg_174_2)
				return ({
					50,
					115,
					180
				})[arg_174_2] + 0.2 * ((arg_174_0.baseAttackDamage + arg_174_0.flatPhysicalDamageMod) * arg_174_0.percentPhysicalDamageMod) + ((1 - arg_174_1.health / arg_174_1.maxHealth) * 2.5 * ({
					50,
					115,
					180
				})[arg_174_2] + 0.2 * ((arg_174_0.baseAttackDamage + arg_174_0.flatPhysicalDamageMod) * arg_174_0.percentPhysicalDamageMod))
			end
		},
		{
			stage = 2,
			slot = 3,
			damagetype = 1,
			damage = function(arg_175_0, arg_175_1, arg_175_2)
				return (({
					50,
					115,
					180
				})[arg_175_2] + 0.2 * ((arg_175_0.baseAttackDamage + arg_175_0.flatPhysicalDamageMod) * arg_175_0.percentPhysicalDamageMod) + ((1 - arg_175_1.health / arg_175_1.maxHealth) * 2.5 * ({
					50,
					115,
					180
				})[arg_175_2] + 0.2 * ((arg_175_0.baseAttackDamage + arg_175_0.flatPhysicalDamageMod) * arg_175_0.percentPhysicalDamageMod))) * 2
			end
		}
	},
	jinx = {
		{
			stage = 1,
			slot = 0,
			damagetype = 1,
			damage = function(arg_176_0, arg_176_1, arg_176_2)
				return 0.1 * ((arg_176_0.baseAttackDamage + arg_176_0.flatPhysicalDamageMod) * arg_176_0.percentPhysicalDamageMod)
			end
		},
		{
			stage = 1,
			slot = 1,
			damagetype = 1,
			damage = function(arg_177_0, arg_177_1, arg_177_2)
				return ({
					10,
					60,
					110,
					160,
					210
				})[arg_177_2] + 1.4 * ((arg_177_0.baseAttackDamage + arg_177_0.flatPhysicalDamageMod) * arg_177_0.percentPhysicalDamageMod)
			end
		},
		{
			stage = 1,
			slot = 2,
			damagetype = 2,
			damage = function(arg_178_0, arg_178_1, arg_178_2)
				return ({
					70,
					120,
					170,
					220,
					270
				})[arg_178_2] + arg_178_0.flatMagicDamageMod * arg_178_0.percentMagicDamageMod
			end
		},
		{
			stage = 1,
			slot = 3,
			damagetype = 1,
			damage = function(arg_179_0, arg_179_1, arg_179_2)
				return ({
					300,
					450,
					600
				})[arg_179_2] + ({
					0.3,
					0.45,
					0.6
				})[arg_179_2] * (arg_179_1.maxHealth - arg_179_1.health) + 1.5 * ((arg_179_0.baseAttackDamage + arg_179_0.flatPhysicalDamageMod) * arg_179_0.percentPhysicalDamageMod - arg_179_0.baseAttackDamage)
			end
		}
	},
	kalista = {
		{
			stage = 1,
			slot = 0,
			damagetype = 1,
			damage = function(arg_180_0, arg_180_1, arg_180_2)
				return ({
					10,
					70,
					130,
					190,
					250
				})[arg_180_2] + (arg_180_0.baseAttackDamage + arg_180_0.flatPhysicalDamageMod) * arg_180_0.percentPhysicalDamageMod
			end
		},
		{
			stage = 1,
			slot = 2,
			damagetype = 1,
			damage = function(arg_181_0, arg_181_1, arg_181_2)
				return ({
					20,
					30,
					40,
					50,
					60
				})[arg_181_2] + 0.6 * ((arg_181_0.baseAttackDamage + arg_181_0.flatPhysicalDamageMod) * arg_181_0.percentPhysicalDamageMod)
			end
		},
		{
			stage = 2,
			slot = 2,
			damagetype = 1,
			damage = function(arg_182_0, arg_182_1, arg_182_2)
				return ({
					10,
					14,
					19,
					25,
					32
				})[arg_182_2] + ({
					0.2,
					0.225,
					0.25,
					0.275,
					0.3
				})[arg_182_2] * ((arg_182_0.baseAttackDamage + arg_182_0.flatPhysicalDamageMod) * arg_182_0.percentPhysicalDamageMod)
			end
		}
	},
	karma = {
		{
			stage = 1,
			slot = 0,
			damagetype = 2,
			damage = function(arg_183_0, arg_183_1, arg_183_2)
				return ({
					80,
					125,
					170,
					215,
					260
				})[arg_183_2] + 0.6 * (arg_183_0.flatMagicDamageMod * arg_183_0.percentMagicDamageMod)
			end
		},
		{
			stage = 2,
			slot = 0,
			damagetype = 2,
			damage = function(arg_184_0, arg_184_1, arg_184_2)
				return ({
					80,
					125,
					170,
					215,
					260
				})[arg_184_2] + ({
					25,
					75,
					125,
					175
				})[arg_184_0:spellSlot(3).level] + 0.9 * (arg_184_0.flatMagicDamageMod * arg_184_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 1,
			damagetype = 2,
			damage = function(arg_185_0, arg_185_1, arg_185_2)
				return ({
					60,
					110,
					160,
					210,
					260
				})[arg_185_2] + 0.9 * (arg_185_0.flatMagicDamageMod * arg_185_0.percentMagicDamageMod)
			end
		},
		{
			stage = 2,
			slot = 1,
			damagetype = 2,
			damage = function(arg_186_0, arg_186_1, arg_186_2)
				return ({
					60,
					110,
					160,
					210,
					260
				})[arg_186_2] + 0.9 * (arg_186_0.flatMagicDamageMod * arg_186_0.percentMagicDamageMod)
			end
		}
	},
	karthus = {
		{
			stage = 1,
			slot = 0,
			damagetype = 2,
			damage = function(arg_187_0, arg_187_1, arg_187_2)
				return (({
					40,
					60,
					80,
					100,
					120
				})[arg_187_2] + 0.3 * (arg_187_0.flatMagicDamageMod * arg_187_0.percentMagicDamageMod)) * 2
			end
		},
		{
			stage = 2,
			slot = 0,
			damagetype = 2,
			damage = function(arg_188_0, arg_188_1, arg_188_2)
				return ({
					40,
					60,
					80,
					100,
					120
				})[arg_188_2] + 0.3 * (arg_188_0.flatMagicDamageMod * arg_188_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 2,
			damagetype = 2,
			damage = function(arg_189_0, arg_189_1, arg_189_2)
				return ({
					30,
					50,
					70,
					90,
					110
				})[arg_189_2] + 0.2 * (arg_189_0.flatMagicDamageMod * arg_189_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 3,
			damagetype = 2,
			damage = function(arg_190_0, arg_190_1, arg_190_2)
				return ({
					250,
					400,
					550
				})[arg_190_2] + 0.75 * (arg_190_0.flatMagicDamageMod * arg_190_0.percentMagicDamageMod)
			end
		}
	},
	kassadin = {
		{
			stage = 1,
			slot = 0,
			damagetype = 2,
			damage = function(arg_191_0, arg_191_1, arg_191_2)
				return ({
					65,
					95,
					125,
					155,
					185
				})[arg_191_2] + 0.7 * (arg_191_0.flatMagicDamageMod * arg_191_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 1,
			damagetype = 2,
			damage = function(arg_192_0, arg_192_1, arg_192_2)
				return ({
					40,
					65,
					90,
					115,
					140
				})[arg_192_2] + 0.7 * (arg_192_0.flatMagicDamageMod * arg_192_0.percentMagicDamageMod)
			end
		},
		{
			stage = 2,
			slot = 1,
			damagetype = 2,
			damage = function(arg_193_0, arg_193_1, arg_193_2)
				return 20 + 0.1 * (arg_193_0.flatMagicDamageMod * arg_193_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 2,
			damagetype = 2,
			damage = function(arg_194_0, arg_194_1, arg_194_2)
				return ({
					80,
					105,
					130,
					155,
					180
				})[arg_194_2] + 0.7 * (arg_194_0.flatMagicDamageMod * arg_194_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 3,
			damagetype = 2,
			damage = function(arg_195_0, arg_195_1, arg_195_2)
				return ({
					80,
					100,
					120
				})[arg_195_2] + 0.3 * (arg_195_0.flatMagicDamageMod * arg_195_0.percentMagicDamageMod) + 0.02 * arg_195_0.maxPar
			end
		},
		{
			stage = 2,
			slot = 3,
			damagetype = 2,
			damage = function(arg_196_0, arg_196_1, arg_196_2)
				return ({
					40,
					50,
					60
				})[arg_196_2] + 0.1 * (arg_196_0.flatMagicDamageMod * arg_196_0.percentMagicDamageMod) + 0.01 * arg_196_0.maxPar
			end
		}
	},
	katarina = {
		{
			stage = 1,
			slot = 0,
			damagetype = 2,
			damage = function(arg_197_0, arg_197_1, arg_197_2)
				return ({
					75,
					105,
					135,
					165,
					195
				})[arg_197_2] + 0.3 * (arg_197_0.flatMagicDamageMod * arg_197_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 2,
			damagetype = 2,
			damage = function(arg_198_0, arg_198_1, arg_198_2)
				return ({
					15,
					30,
					45,
					60,
					75
				})[arg_198_2] + 0.25 * (arg_198_0.flatMagicDamageMod * arg_198_0.percentMagicDamageMod) + 0.5 * ((arg_198_0.baseAttackDamage + arg_198_0.flatPhysicalDamageMod) * arg_198_0.percentPhysicalDamageMod)
			end
		},
		{
			stage = 1,
			slot = 3,
			damagetype = 2,
			damage = function(arg_199_0, arg_199_1, arg_199_2)
				return ({
					25,
					37.5,
					50
				})[arg_199_2] + 0.22 * ((arg_199_0.baseAttackDamage + arg_199_0.flatPhysicalDamageMod) * arg_199_0.percentPhysicalDamageMod - arg_199_0.baseAttackDamage) + 0.19 * (arg_199_0.flatMagicDamageMod * arg_199_0.percentMagicDamageMod)
			end
		},
		{
			stage = 2,
			slot = 3,
			damagetype = 2,
			damage = function(arg_200_0, arg_200_1, arg_200_2)
				return ({
					375,
					562.5,
					750
				})[arg_200_2] + 3.3 * ((arg_200_0.baseAttackDamage + arg_200_0.flatPhysicalDamageMod) * arg_200_0.percentPhysicalDamageMod - arg_200_0.baseAttackDamage) + 2.85 * (arg_200_0.flatMagicDamageMod * arg_200_0.percentMagicDamageMod)
			end
		}
	},
	kayle = {
		{
			stage = 1,
			slot = 0,
			damagetype = 2,
			damage = function(arg_201_0, arg_201_1, arg_201_2)
				return ({
					60,
					110,
					160,
					210,
					260
				})[arg_201_2] + ((arg_201_0.baseAttackDamage + arg_201_0.flatPhysicalDamageMod) * arg_201_0.percentPhysicalDamageMod - arg_201_0.baseAttackDamage) + 0.6 * (arg_201_0.flatMagicDamageMod * arg_201_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 2,
			damagetype = 2,
			damage = function(arg_202_0, arg_202_1, arg_202_2)
				return arg_202_0.attackRange > 500 and ({
					20,
					30,
					40,
					50,
					60
				})[arg_202_2] + 0.3 * (arg_202_0.flatMagicDamageMod * arg_202_0.percentMagicDamageMod) or ({
					10,
					15,
					20,
					25,
					30
				})[arg_202_2] + 0.15 * (arg_202_0.flatMagicDamageMod * arg_202_0.percentMagicDamageMod)
			end
		}
	},
	kayn = {
		{
			stage = 1,
			slot = 0,
			damagetype = 1,
			damage = function(arg_203_0, arg_203_1, arg_203_2)
				return ({
					60,
					80,
					100,
					120,
					140
				})[arg_203_2] + 0.65 * ((arg_203_0.baseAttackDamage + arg_203_0.flatPhysicalDamageMod) * arg_203_0.percentPhysicalDamageMod - arg_203_0.baseAttackDamage)
			end
		},
		{
			stage = 1,
			slot = 1,
			damagetype = 1,
			damage = function(arg_204_0, arg_204_1, arg_204_2)
				return ({
					90,
					135,
					180,
					225,
					270
				})[arg_204_2] + 1.2 * ((arg_204_0.baseAttackDamage + arg_204_0.flatPhysicalDamageMod) * arg_204_0.percentPhysicalDamageMod - arg_204_0.baseAttackDamage)
			end
		},
		{
			stage = 1,
			slot = 3,
			damagetype = 1,
			damage = function(arg_205_0, arg_205_1, arg_205_2)
				return ({
					150,
					250,
					350
				})[arg_205_2] + 1.5 * ((arg_205_0.baseAttackDamage + arg_205_0.flatPhysicalDamageMod) * arg_205_0.percentPhysicalDamageMod - arg_205_0.baseAttackDamage)
			end
		}
	},
	kennen = {
		{
			stage = 1,
			slot = 0,
			damagetype = 2,
			damage = function(arg_206_0, arg_206_1, arg_206_2)
				return ({
					75,
					115,
					155,
					195,
					235
				})[arg_206_2] + 0.75 * (arg_206_0.flatMagicDamageMod * arg_206_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 1,
			damagetype = 2,
			damage = function(arg_207_0, arg_207_1, arg_207_2)
				return ({
					15,
					20,
					25,
					30,
					35
				})[arg_207_2] + 0.6 * ((arg_207_0.baseAttackDamage + arg_207_0.flatPhysicalDamageMod) * arg_207_0.percentPhysicalDamageMod - arg_207_0.baseAttackDamage) + 0.3 * (arg_207_0.flatMagicDamageMod * arg_207_0.percentMagicDamageMod)
			end
		},
		{
			stage = 2,
			slot = 1,
			damagetype = 2,
			damage = function(arg_208_0, arg_208_1, arg_208_2)
				return ({
					65,
					95,
					125,
					155,
					185
				})[arg_208_2] + 0.55 * (arg_208_0.flatMagicDamageMod * arg_208_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 2,
			damagetype = 2,
			damage = function(arg_209_0, arg_209_1, arg_209_2)
				return ({
					85,
					125,
					165,
					205,
					245
				})[arg_209_2] + 0.8 * (arg_209_0.flatMagicDamageMod * arg_209_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 3,
			damagetype = 2,
			damage = function(arg_210_0, arg_210_1, arg_210_2)
				return ({
					40,
					75,
					110
				})[arg_210_2] + 0.2 * (arg_210_0.flatMagicDamageMod * arg_210_0.percentMagicDamageMod)
			end
		}
	},
	khazix = {
		{
			stage = 1,
			slot = 0,
			damagetype = 1,
			damage = function(arg_211_0, arg_211_1, arg_211_2)
				return ({
					65,
					90,
					115,
					140,
					165
				})[arg_211_2] + 1.2 * ((arg_211_0.baseAttackDamage + arg_211_0.flatPhysicalDamageMod) * arg_211_0.percentPhysicalDamageMod - arg_211_0.baseAttackDamage)
			end
		},
		{
			stage = 2,
			slot = 0,
			damagetype = 1,
			damage = function(arg_212_0, arg_212_1, arg_212_2)
				return ({
					107.25,
					148.5,
					189.75,
					231,
					272.25
				})[arg_212_2] + 1.98 * ((arg_212_0.baseAttackDamage + arg_212_0.flatPhysicalDamageMod) * arg_212_0.percentPhysicalDamageMod - arg_212_0.baseAttackDamage)
			end
		},
		{
			stage = 1,
			slot = 1,
			damagetype = 1,
			damage = function(arg_213_0, arg_213_1, arg_213_2)
				return ({
					85,
					115,
					145,
					175,
					205
				})[arg_213_2] + ((arg_213_0.baseAttackDamage + arg_213_0.flatPhysicalDamageMod) * arg_213_0.percentPhysicalDamageMod - arg_213_0.baseAttackDamage)
			end
		},
		{
			stage = 1,
			slot = 2,
			damagetype = 1,
			damage = function(arg_214_0, arg_214_1, arg_214_2)
				return ({
					65,
					100,
					135,
					170,
					205
				})[arg_214_2] + 0.2 * ((arg_214_0.baseAttackDamage + arg_214_0.flatPhysicalDamageMod) * arg_214_0.percentPhysicalDamageMod - arg_214_0.baseAttackDamage)
			end
		}
	},
	kindred = {
		{
			stage = 1,
			slot = 0,
			damagetype = 1,
			damage = function(arg_215_0, arg_215_1, arg_215_2)
				return ({
					60,
					80,
					100,
					120,
					140
				})[arg_215_2] + 0.65 * ((arg_215_0.baseAttackDamage + arg_215_0.flatPhysicalDamageMod) * arg_215_0.percentPhysicalDamageMod - arg_215_0.baseAttackDamage)
			end
		},
		{
			stage = 1,
			slot = 1,
			damagetype = 2,
			damage = function(arg_216_0, arg_216_1, arg_216_2)
				return ({
					25,
					30,
					35,
					40,
					45
				})[arg_216_2] + 0.2 * ((arg_216_0.baseAttackDamage + arg_216_0.flatPhysicalDamageMod) * arg_216_0.percentPhysicalDamageMod - arg_216_0.baseAttackDamage) + 0.015 * arg_216_1.health
			end
		},
		{
			stage = 1,
			slot = 2,
			damagetype = 1,
			damage = function(arg_217_0, arg_217_1, arg_217_2)
				return ({
					65,
					85,
					105,
					125,
					145
				})[arg_217_2] + 0.8 * ((arg_217_0.baseAttackDamage + arg_217_0.flatPhysicalDamageMod) * arg_217_0.percentPhysicalDamageMod - arg_217_0.baseAttackDamage) + 0.08 * (arg_217_1.maxHealth - arg_217_1.health)
			end
		}
	},
	kled = {
		{
			stage = 1,
			slot = 0,
			damagetype = 1,
			damage = function(arg_218_0, arg_218_1, arg_218_2)
				return ({
					30,
					55,
					80,
					105,
					130
				})[arg_218_2] + 0.6 * ((arg_218_0.baseAttackDamage + arg_218_0.flatPhysicalDamageMod) * arg_218_0.percentPhysicalDamageMod - arg_218_0.baseAttackDamage)
			end
		},
		{
			stage = 2,
			slot = 0,
			damagetype = 1,
			damage = function(arg_219_0, arg_219_1, arg_219_2)
				return ({
					60,
					110,
					160,
					210,
					260
				})[arg_219_2] + 1.2 * ((arg_219_0.baseAttackDamage + arg_219_0.flatPhysicalDamageMod) * arg_219_0.percentPhysicalDamageMod - arg_219_0.baseAttackDamage)
			end
		},
		{
			stage = 3,
			slot = 0,
			damagetype = 1,
			damage = function(arg_220_0, arg_220_1, arg_220_2)
				return ({
					35,
					50,
					65,
					80,
					95
				})[arg_220_2] + 0.8 * ((arg_220_0.baseAttackDamage + arg_220_0.flatPhysicalDamageMod) * arg_220_0.percentPhysicalDamageMod - arg_220_0.baseAttackDamage)
			end
		},
		{
			stage = 1,
			slot = 1,
			damagetype = 1,
			damage = function(arg_221_0, arg_221_1, arg_221_2)
				return ({
					20,
					30,
					40,
					50,
					60
				})[arg_221_2] + (({
					0.045,
					0.05,
					0.055,
					0.06,
					0.065
				})[arg_221_2] + 0.05 * (((arg_221_0.baseAttackDamage + arg_221_0.flatPhysicalDamageMod) * arg_221_0.percentPhysicalDamageMod - arg_221_0.baseAttackDamage) / 100)) * arg_221_1.maxHealth
			end
		},
		{
			stage = 1,
			slot = 2,
			damagetype = 1,
			damage = function(arg_222_0, arg_222_1, arg_222_2)
				return ({
					35,
					60,
					85,
					110,
					135
				})[arg_222_2] + 0.6 * ((arg_222_0.baseAttackDamage + arg_222_0.flatPhysicalDamageMod) * arg_222_0.percentPhysicalDamageMod - arg_222_0.baseAttackDamage)
			end
		},
		{
			stage = 1,
			slot = 3,
			damagetype = 1,
			damage = function(arg_223_0, arg_223_1, arg_223_2)
				return (({
					0.12,
					0.15,
					0.18
				})[arg_223_2] + 0.12 * (((arg_223_0.baseAttackDamage + arg_223_0.flatPhysicalDamageMod) * arg_223_0.percentPhysicalDamageMod - arg_223_0.baseAttackDamage) / 100)) * arg_223_1.maxHealth
			end
		}
	},
	kogmaw = {
		{
			stage = 1,
			slot = 0,
			damagetype = 2,
			damage = function(arg_224_0, arg_224_1, arg_224_2)
				return ({
					80,
					130,
					180,
					230,
					280
				})[arg_224_2] + 0.5 * (arg_224_0.flatMagicDamageMod * arg_224_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 1,
			damagetype = 2,
			damage = function(arg_225_0, arg_225_1, arg_225_2)
				return (({
					0.03,
					0.04,
					0.05,
					0.06,
					0.07
				})[arg_225_2] + 0.01 * (arg_225_0.flatMagicDamageMod * arg_225_0.percentMagicDamageMod / 100)) * arg_225_1.maxHealth
			end
		},
		{
			stage = 1,
			slot = 2,
			damagetype = 2,
			damage = function(arg_226_0, arg_226_1, arg_226_2)
				return ({
					60,
					105,
					150,
					195,
					240
				})[arg_226_2] + 0.5 * (arg_226_0.flatMagicDamageMod * arg_226_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 3,
			damagetype = 2,
			damage = function(arg_227_0, arg_227_1, arg_227_2)
				local slot_227_0 = ({
					100,
					140,
					180
				})[arg_227_2] + 0.65 * ((arg_227_0.baseAttackDamage + arg_227_0.flatPhysicalDamageMod) * arg_227_0.percentPhysicalDamageMod - arg_227_0.baseAttackDamage) + 0.25 * (arg_227_0.flatMagicDamageMod * arg_227_0.percentMagicDamageMod)
				local slot_227_1 = (1 - arg_227_1.health / arg_227_1.maxHealth) * 0.833

				slot_227_1 = slot_227_1 * 100 > 50 and 0.5 or slot_227_1

				return slot_227_0 + slot_227_1 * slot_227_0
			end
		}
	},
	leblanc = {
		{
			stage = 1,
			slot = 0,
			damagetype = 2,
			damage = function(arg_228_0, arg_228_1, arg_228_2)
				return ({
					55,
					90,
					125,
					160,
					195
				})[arg_228_2] + 0.5 * (arg_228_0.flatMagicDamageMod * arg_228_0.percentMagicDamageMod)
			end
		},
		{
			stage = 2,
			slot = 0,
			damagetype = 2,
			damage = function(arg_229_0, arg_229_1, arg_229_2)
				return ({
					150,
					275,
					400
				})[arg_229_0:spellSlot(3).level] + 0.6 * (arg_229_0.flatMagicDamageMod * arg_229_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 1,
			damagetype = 2,
			damage = function(arg_230_0, arg_230_1, arg_230_2)
				return ({
					40,
					55,
					70,
					85,
					100
				})[arg_230_2] + 0.2 * (arg_230_0.flatMagicDamageMod * arg_230_0.percentMagicDamageMod)
			end
		},
		{
			stage = 2,
			slot = 1,
			damagetype = 2,
			damage = function(arg_231_0, arg_231_1, arg_231_2)
				return ({
					60,
					120,
					180
				})[arg_231_0:spellSlot(3).level] + 0.3 * (arg_231_0.flatMagicDamageMod * arg_231_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 2,
			damagetype = 2,
			damage = function(arg_232_0, arg_232_1, arg_232_2)
				return ({
					40,
					60,
					80,
					100,
					120
				})[arg_232_2] + 0.5 * (arg_232_0.flatMagicDamageMod * arg_232_0.percentMagicDamageMod)
			end
		},
		{
			stage = 2,
			slot = 2,
			damagetype = 2,
			damage = function(arg_233_0, arg_233_1, arg_233_2)
				return ({
					100,
					160,
					220
				})[arg_233_0:spellSlot(3).level] + 0.4 * (arg_233_0.flatMagicDamageMod * arg_233_0.percentMagicDamageMod)
			end
		}
	},
	leesin = {
		{
			stage = 1,
			slot = 0,
			damagetype = 1,
			damage = function(arg_234_0, arg_234_1, arg_234_2)
				return ({
					55,
					85,
					115,
					145,
					175
				})[arg_234_2] + 0.9 * ((arg_234_0.baseAttackDamage + arg_234_0.flatPhysicalDamageMod) * arg_234_0.percentPhysicalDamageMod - arg_234_0.baseAttackDamage)
			end
		},
		{
			stage = 2,
			slot = 0,
			damagetype = 1,
			damage = function(arg_235_0, arg_235_1, arg_235_2)
				return ({
					55,
					85,
					115,
					145,
					175
				})[arg_235_2] + 0.9 * ((arg_235_0.baseAttackDamage + arg_235_0.flatPhysicalDamageMod) * arg_235_0.percentPhysicalDamageMod - arg_235_0.baseAttackDamage) + 0.08 * (arg_235_1.maxHealth - arg_235_1.health)
			end
		},
		{
			stage = 1,
			slot = 2,
			damagetype = 2,
			damage = function(arg_236_0, arg_236_1, arg_236_2)
				return ({
					70,
					105,
					140,
					175,
					210
				})[arg_236_2] + ((arg_236_0.baseAttackDamage + arg_236_0.flatPhysicalDamageMod) * arg_236_0.percentPhysicalDamageMod - arg_236_0.baseAttackDamage)
			end
		},
		{
			stage = 1,
			slot = 3,
			damagetype = 1,
			damage = function(arg_237_0, arg_237_1, arg_237_2)
				return ({
					150,
					300,
					450
				})[arg_237_2] + 2 * ((arg_237_0.baseAttackDamage + arg_237_0.flatPhysicalDamageMod) * arg_237_0.percentPhysicalDamageMod - arg_237_0.baseAttackDamage)
			end
		}
	},
	leona = {
		{
			stage = 1,
			slot = 0,
			damagetype = 2,
			damage = function(arg_238_0, arg_238_1, arg_238_2)
				return ({
					10,
					35,
					60,
					85,
					110
				})[arg_238_2] + 0.3 * (arg_238_0.flatMagicDamageMod * arg_238_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 1,
			damagetype = 2,
			damage = function(arg_239_0, arg_239_1, arg_239_2)
				return ({
					40,
					80,
					120,
					160,
					200
				})[arg_239_2] + 0.4 * (arg_239_0.flatMagicDamageMod * arg_239_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 2,
			damagetype = 2,
			damage = function(arg_240_0, arg_240_1, arg_240_2)
				return ({
					60,
					100,
					140,
					180,
					220
				})[arg_240_2] + 0.4 * (arg_240_0.flatMagicDamageMod * arg_240_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 3,
			damagetype = 2,
			damage = function(arg_241_0, arg_241_1, arg_241_2)
				return ({
					100,
					175,
					250
				})[arg_241_2] + 0.8 * (arg_241_0.flatMagicDamageMod * arg_241_0.percentMagicDamageMod)
			end
		}
	},
	lissandra = {
		{
			stage = 1,
			slot = 0,
			damagetype = 2,
			damage = function(arg_242_0, arg_242_1, arg_242_2)
				return ({
					70,
					100,
					130,
					160,
					190
				})[arg_242_2] + 0.7 * (arg_242_0.flatMagicDamageMod * arg_242_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 1,
			damagetype = 2,
			damage = function(arg_243_0, arg_243_1, arg_243_2)
				return ({
					70,
					110,
					150,
					190,
					230
				})[arg_243_2] + 0.4 * (arg_243_0.flatMagicDamageMod * arg_243_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 2,
			damagetype = 2,
			damage = function(arg_244_0, arg_244_1, arg_244_2)
				return ({
					70,
					115,
					160,
					205,
					250
				})[arg_244_2] + 0.6 * (arg_244_0.flatMagicDamageMod * arg_244_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 3,
			damagetype = 2,
			damage = function(arg_245_0, arg_245_1, arg_245_2)
				return ({
					150,
					250,
					350
				})[arg_245_2] + 0.7 * (arg_245_0.flatMagicDamageMod * arg_245_0.percentMagicDamageMod)
			end
		}
	},
	lucian = {
		{
			stage = 1,
			slot = 0,
			damagetype = 1,
			damage = function(arg_246_0, arg_246_1, arg_246_2)
				return ({
					80,
					120,
					155,
					190,
					225
				})[arg_246_2] + ({
					0.6,
					0.7,
					0.8,
					0.9,
					1
				})[arg_246_2] * ((arg_246_0.baseAttackDamage + arg_246_0.flatPhysicalDamageMod) * arg_246_0.percentPhysicalDamageMod - arg_246_0.baseAttackDamage)
			end
		},
		{
			stage = 1,
			slot = 1,
			damagetype = 2,
			damage = function(arg_247_0, arg_247_1, arg_247_2)
				return ({
					60,
					100,
					140,
					180,
					220
				})[arg_247_2] + 0.9 * (arg_247_0.flatMagicDamageMod * arg_247_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 3,
			damagetype = 1,
			damage = function(arg_248_0, arg_248_1, arg_248_2)
				return ({
					20,
					35,
					50
				})[arg_248_2] + 0.1 * (arg_248_0.flatMagicDamageMod * arg_248_0.percentMagicDamageMod) + 0.2 * ((arg_248_0.baseAttackDamage + arg_248_0.flatPhysicalDamageMod) * arg_248_0.percentPhysicalDamageMod)
			end
		}
	},
	lulu = {
		{
			stage = 1,
			slot = 0,
			damagetype = 2,
			damage = function(arg_249_0, arg_249_1, arg_249_2)
				return ({
					80,
					125,
					170,
					215,
					260
				})[arg_249_2] + 0.5 * (arg_249_0.flatMagicDamageMod * arg_249_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 2,
			damagetype = 2,
			damage = function(arg_250_0, arg_250_1, arg_250_2)
				return ({
					80,
					110,
					140,
					170,
					200
				})[arg_250_2] + 0.4 * (arg_250_0.flatMagicDamageMod * arg_250_0.percentMagicDamageMod)
			end
		}
	},
	lux = {
		{
			stage = 1,
			slot = 0,
			damagetype = 2,
			damage = function(arg_251_0, arg_251_1, arg_251_2)
				return ({
					50,
					100,
					150,
					200,
					250
				})[arg_251_2] + 0.7 * (arg_251_0.flatMagicDamageMod * arg_251_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 2,
			damagetype = 2,
			damage = function(arg_252_0, arg_252_1, arg_252_2)
				return ({
					60,
					105,
					150,
					195,
					240
				})[arg_252_2] + 0.6 * (arg_252_0.flatMagicDamageMod * arg_252_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 3,
			damagetype = 2,
			damage = function(arg_253_0, arg_253_1, arg_253_2)
				return ({
					300,
					400,
					500
				})[arg_253_2] + 0.75 * (arg_253_0.flatMagicDamageMod * arg_253_0.percentMagicDamageMod)
			end
		}
	},
	malphite = {
		{
			stage = 1,
			slot = 0,
			damagetype = 2,
			damage = function(arg_254_0, arg_254_1, arg_254_2)
				return ({
					70,
					120,
					170,
					220,
					270
				})[arg_254_2] + 0.6 * (arg_254_0.flatMagicDamageMod * arg_254_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 1,
			damagetype = 1,
			damage = function(arg_255_0, arg_255_1, arg_255_2)
				return ({
					15,
					30,
					45,
					60,
					75
				})[arg_255_2] + 0.1 * (arg_255_0.flatMagicDamageMod * arg_255_0.percentMagicDamageMod) + 0.15 * arg_255_0.armor
			end
		},
		{
			stage = 1,
			slot = 2,
			damagetype = 2,
			damage = function(arg_256_0, arg_256_1, arg_256_2)
				return ({
					60,
					95,
					130,
					165,
					200
				})[arg_256_2] + 0.4 * arg_256_0.armor + 0.2 * (arg_256_0.flatMagicDamageMod * arg_256_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 3,
			damagetype = 2,
			damage = function(arg_257_0, arg_257_1, arg_257_2)
				return ({
					200,
					300,
					400
				})[arg_257_2] + arg_257_0.flatMagicDamageMod * arg_257_0.percentMagicDamageMod
			end
		}
	},
	malzahar = {
		{
			stage = 1,
			slot = 0,
			damagetype = 2,
			damage = function(arg_258_0, arg_258_1, arg_258_2)
				return ({
					70,
					105,
					140,
					175,
					210
				})[arg_258_2] + 0.65 * (arg_258_0.flatMagicDamageMod * arg_258_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 1,
			damagetype = 2,
			damage = function(arg_259_0, arg_259_1, arg_259_2)
				return 3.5 * arg_259_0.levelRef + 1.5 + ({
					12,
					14,
					16,
					18,
					20
				})[arg_259_2] + 0.4 * ((arg_259_0.baseAttackDamage + arg_259_0.flatPhysicalDamageMod) * arg_259_0.percentPhysicalDamageMod - arg_259_0.baseAttackDamage) + 0.2 * (arg_259_0.flatMagicDamageMod * arg_259_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 2,
			damagetype = 2,
			damage = function(arg_260_0, arg_260_1, arg_260_2)
				return ({
					80,
					115,
					150,
					185,
					220
				})[arg_260_2] + 0.8 * (arg_260_0.flatMagicDamageMod * arg_260_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 3,
			damagetype = 2,
			damage = function(arg_261_0, arg_261_1, arg_261_2)
				return ({
					125,
					200,
					275
				})[arg_261_2] + 0.8 * (arg_261_0.flatMagicDamageMod * arg_261_0.percentMagicDamageMod)
			end
		}
	},
	maokai = {
		{
			stage = 1,
			slot = 0,
			damagetype = 2,
			damage = function(arg_262_0, arg_262_1, arg_262_2)
				return ({
					65,
					105,
					145,
					185,
					225
				})[arg_262_2] + 0.4 * (arg_262_0.flatMagicDamageMod * arg_262_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 1,
			damagetype = 2,
			damage = function(arg_263_0, arg_263_1, arg_263_2)
				return ({
					50,
					75,
					100,
					125,
					150
				})[arg_263_2] + 0.4 * (arg_263_0.flatMagicDamageMod * arg_263_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 2,
			damagetype = 2,
			damage = function(arg_264_0, arg_264_1, arg_264_2)
				return ({
					25,
					50,
					75,
					100,
					125
				})[arg_264_2] + (({
					0.06,
					0.065,
					0.07,
					0.075,
					0.08
				})[arg_264_2] + 0.01 * (arg_264_0.flatMagicDamageMod * arg_264_0.percentMagicDamageMod / 100)) * arg_264_1.maxHealth
			end
		},
		{
			stage = 2,
			slot = 2,
			damagetype = 2,
			damage = function(arg_265_0, arg_265_1, arg_265_2)
				return ({
					50,
					100,
					150,
					200,
					250
				})[arg_265_2] + (({
					0.12,
					0.13,
					0.14,
					0.15,
					0.16
				})[arg_265_2] + 0.02 * (arg_265_0.flatMagicDamageMod * arg_265_0.percentMagicDamageMod / 100)) * arg_265_1.maxHealth
			end
		},
		{
			stage = 1,
			slot = 3,
			damagetype = 2,
			damage = function(arg_266_0, arg_266_1, arg_266_2)
				return ({
					150,
					225,
					300
				})[arg_266_2] + 0.75 * (arg_266_0.flatMagicDamageMod * arg_266_0.percentMagicDamageMod)
			end
		}
	},
	masteryi = {
		{
			stage = 1,
			slot = 0,
			damagetype = 1,
			damage = function(arg_267_0, arg_267_1, arg_267_2)
				return ({
					25,
					60,
					95,
					130,
					165
				})[arg_267_2] + (arg_267_0.baseAttackDamage + arg_267_0.flatPhysicalDamageMod) * arg_267_0.percentPhysicalDamageMod
			end
		},
		{
			stage = 1,
			slot = 2,
			damagetype = 3,
			damage = function(arg_268_0, arg_268_1, arg_268_2)
				return ({
					14,
					23,
					32,
					41,
					50
				})[arg_268_2] + 0.25 * ((arg_268_0.baseAttackDamage + arg_268_0.flatPhysicalDamageMod) * arg_268_0.percentPhysicalDamageMod - arg_268_0.baseAttackDamage)
			end
		}
	},
	missfortune = {
		{
			stage = 1,
			slot = 0,
			damagetype = 1,
			damage = function(arg_269_0, arg_269_1, arg_269_2)
				return ({
					20,
					40,
					60,
					80,
					100
				})[arg_269_2] + 0.35 * (arg_269_0.flatMagicDamageMod * arg_269_0.percentMagicDamageMod) + (arg_269_0.baseAttackDamage + arg_269_0.flatPhysicalDamageMod) * arg_269_0.percentPhysicalDamageMod
			end
		},
		{
			stage = 1,
			slot = 2,
			damagetype = 2,
			damage = function(arg_270_0, arg_270_1, arg_270_2)
				return ({
					80,
					115,
					150,
					185,
					220
				})[arg_270_2] + 0.8 * (arg_270_0.flatMagicDamageMod * arg_270_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 3,
			damagetype = 1,
			damage = function(arg_271_0, arg_271_1, arg_271_2)
				return 0.75 * ((arg_271_0.baseAttackDamage + arg_271_0.flatPhysicalDamageMod) * arg_271_0.percentPhysicalDamageMod) + 0.2 * (arg_271_0.flatMagicDamageMod * arg_271_0.percentMagicDamageMod)
			end
		}
	},
	monkeyking = {
		{
			stage = 1,
			slot = 0,
			damagetype = 1,
			damage = function(arg_272_0, arg_272_1, arg_272_2)
				return ({
					30,
					60,
					90,
					120,
					150
				})[arg_272_2] + 0.1 * ((arg_272_0.baseAttackDamage + arg_272_0.flatPhysicalDamageMod) * arg_272_0.percentPhysicalDamageMod)
			end
		},
		{
			stage = 1,
			slot = 1,
			damagetype = 2,
			damage = function(arg_273_0, arg_273_1, arg_273_2)
				return ({
					70,
					115,
					160,
					205,
					250
				})[arg_273_2] + 0.6 * (arg_273_0.flatMagicDamageMod * arg_273_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 2,
			damagetype = 1,
			damage = function(arg_274_0, arg_274_1, arg_274_2)
				return ({
					65,
					110,
					155,
					200,
					245
				})[arg_274_2] + 0.8 * ((arg_274_0.baseAttackDamage + arg_274_0.flatPhysicalDamageMod) * arg_274_0.percentPhysicalDamageMod - arg_274_0.baseAttackDamage)
			end
		},
		{
			stage = 1,
			slot = 3,
			damagetype = 1,
			damage = function(arg_275_0, arg_275_1, arg_275_2)
				return ({
					20,
					110,
					200
				})[arg_275_2] + 1.1 * ((arg_275_0.baseAttackDamage + arg_275_0.flatPhysicalDamageMod) * arg_275_0.percentPhysicalDamageMod)
			end
		}
	},
	mordekaiser = {
		{
			stage = 1,
			slot = 0,
			damagetype = 2,
			damage = function(arg_276_0, arg_276_1, arg_276_2)
				return ({
					40,
					80,
					120,
					160,
					200
				})[arg_276_2] + ({
					2,
					2.4,
					2.8,
					3.2,
					3.6
				})[arg_276_2] * ((arg_276_0.baseAttackDamage + arg_276_0.flatPhysicalDamageMod) * arg_276_0.percentPhysicalDamageMod) + 2.4 * (arg_276_0.flatMagicDamageMod * arg_276_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 1,
			damagetype = 2,
			damage = function(arg_277_0, arg_277_1, arg_277_2)
				return ({
					50,
					85,
					120,
					155,
					190
				})[arg_277_2] + 0.3 * (arg_277_0.flatMagicDamageMod * arg_277_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 2,
			damagetype = 2,
			damage = function(arg_278_0, arg_278_1, arg_278_2)
				return ({
					35,
					65,
					95,
					125,
					155
				})[arg_278_2] + 0.6 * (arg_278_0.flatMagicDamageMod * arg_278_0.percentMagicDamageMod) + 0.6 * ((arg_278_0.baseAttackDamage + arg_278_0.flatPhysicalDamageMod) * arg_278_0.percentPhysicalDamageMod)
			end
		},
		{
			stage = 1,
			slot = 3,
			damagetype = 2,
			damage = function(arg_279_0, arg_279_1, arg_279_2)
				return (({
					0.25,
					0.3,
					0.35
				})[arg_279_2] + 0.04 * (arg_279_0.flatMagicDamageMod * arg_279_0.percentMagicDamageMod / 100)) * arg_279_1.maxHealth
			end
		}
	},
	morgana = {
		{
			stage = 1,
			slot = 0,
			damagetype = 2,
			damage = function(arg_280_0, arg_280_1, arg_280_2)
				return ({
					80,
					135,
					190,
					245,
					300
				})[arg_280_2] + 0.9 * (arg_280_0.flatMagicDamageMod * arg_280_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 1,
			damagetype = 2,
			damage = function(arg_281_0, arg_281_1, arg_281_2)
				return ({
					80,
					160,
					240,
					320,
					400
				})[arg_281_2] + 1.1 * (arg_281_0.flatMagicDamageMod * arg_281_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 3,
			damagetype = 2,
			damage = function(arg_282_0, arg_282_1, arg_282_2)
				return ({
					150,
					225,
					300
				})[arg_282_2] + 0.7 * (arg_282_0.flatMagicDamageMod * arg_282_0.percentMagicDamageMod)
			end
		}
	},
	nami = {
		{
			stage = 1,
			slot = 0,
			damagetype = 2,
			damage = function(arg_283_0, arg_283_1, arg_283_2)
				return ({
					75,
					130,
					185,
					240,
					295
				})[arg_283_2] + 0.5 * (arg_283_0.flatMagicDamageMod * arg_283_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 1,
			damagetype = 2,
			damage = function(arg_284_0, arg_284_1, arg_284_2)
				return ({
					70,
					110,
					150,
					190,
					230
				})[arg_284_2] + 0.5 * (arg_284_0.flatMagicDamageMod * arg_284_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 2,
			damagetype = 2,
			damage = function(arg_285_0, arg_285_1, arg_285_2)
				return ({
					20,
					40,
					55,
					70,
					85
				})[arg_285_2] + 0.2 * (arg_285_0.flatMagicDamageMod * arg_285_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 3,
			damagetype = 2,
			damage = function(arg_286_0, arg_286_1, arg_286_2)
				return ({
					150,
					250,
					350
				})[arg_286_2] + 0.6 * (arg_286_0.flatMagicDamageMod * arg_286_0.percentMagicDamageMod)
			end
		}
	},
	nasus = {
		{
			stage = 1,
			slot = 0,
			damagetype = 1,
			damage = function(arg_287_0, arg_287_1, arg_287_2)
				return ove_0_13(arg_287_0, "nasusqstacks") + ({
					30,
					50,
					70,
					90,
					110
				})[arg_287_2]
			end
		},
		{
			stage = 1,
			slot = 2,
			damagetype = 2,
			damage = function(arg_288_0, arg_288_1, arg_288_2)
				return ({
					55,
					95,
					135,
					175,
					215
				})[arg_288_2] + 0.6 * (arg_288_0.flatMagicDamageMod * arg_288_0.percentMagicDamageMod)
			end
		},
		{
			stage = 2,
			slot = 2,
			damagetype = 2,
			damage = function(arg_289_0, arg_289_1, arg_289_2)
				return ({
					11,
					19,
					27,
					35,
					43
				})[arg_289_2] + 0.12 * (arg_289_0.flatMagicDamageMod * arg_289_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 3,
			damagetype = 2,
			damage = function(arg_290_0, arg_290_1, arg_290_2)
				return (({
					0.03,
					0.04,
					0.05
				})[arg_290_2] + 0.01 * (arg_290_0.flatMagicDamageMod * arg_290_0.percentMagicDamageMod / 100)) * arg_290_1.maxHealth
			end
		}
	},
	nautilus = {
		{
			stage = 1,
			slot = 0,
			damagetype = 2,
			damage = function(arg_291_0, arg_291_1, arg_291_2)
				return ({
					80,
					120,
					160,
					200,
					240
				})[arg_291_2] + 0.75 * (arg_291_0.flatMagicDamageMod * arg_291_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 1,
			damagetype = 2,
			damage = function(arg_292_0, arg_292_1, arg_292_2)
				return ({
					30,
					40,
					50,
					60,
					70
				})[arg_292_2] + 0.4 * (arg_292_0.flatMagicDamageMod * arg_292_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 2,
			damagetype = 2,
			damage = function(arg_293_0, arg_293_1, arg_293_2)
				return ({
					55,
					85,
					115,
					145,
					175
				})[arg_293_2] + 0.3 * (arg_293_0.flatMagicDamageMod * arg_293_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 3,
			damagetype = 2,
			damage = function(arg_294_0, arg_294_1, arg_294_2)
				return ({
					200,
					325,
					450
				})[arg_294_2] + 0.8 * (arg_294_0.flatMagicDamageMod * arg_294_0.percentMagicDamageMod)
			end
		},
		{
			stage = 2,
			slot = 3,
			damagetype = 2,
			damage = function(arg_295_0, arg_295_1, arg_295_2)
				return ({
					125,
					175,
					225
				})[arg_295_2] + 0.4 * (arg_295_0.flatMagicDamageMod * arg_295_0.percentMagicDamageMod)
			end
		}
	},
	nidalee = {
		{
			stage = 1,
			slot = 0,
			damagetype = 2,
			damage = function(arg_296_0, arg_296_1, arg_296_2)
				return ({
					70,
					85,
					100,
					115,
					130
				})[arg_296_2] + 0.4 * (arg_296_0.flatMagicDamageMod * arg_296_0.percentMagicDamageMod)
			end
		},
		{
			stage = 2,
			slot = 0,
			damagetype = 2,
			damage = function(arg_297_0, arg_297_1, arg_297_2)
				return (({
					5,
					30,
					55,
					80
				})[arg_297_0:spellSlot(3).level] + 0.4 * (arg_297_0.flatMagicDamageMod * arg_297_0.percentMagicDamageMod) + 0.75 * ((arg_297_0.baseAttackDamage + arg_297_0.flatPhysicalDamageMod) * arg_297_0.percentPhysicalDamageMod)) * ((arg_297_1.maxHealth - arg_297_1.health) / arg_297_1.maxHealth * 1.5 + 1) * (ove_0_13(arg_297_1, "nidaleepassivehunted") > 0 and 1.4 or 1)
			end
		},
		{
			stage = 1,
			slot = 1,
			damagetype = 2,
			damage = function(arg_298_0, arg_298_1, arg_298_2)
				return ({
					40,
					80,
					120,
					160,
					200
				})[arg_298_2] + 0.2 * (arg_298_0.flatMagicDamageMod * arg_298_0.percentMagicDamageMod)
			end
		},
		{
			stage = 2,
			slot = 1,
			damagetype = 2,
			damage = function(arg_299_0, arg_299_1, arg_299_2)
				return ({
					60,
					110,
					160,
					210
				})[arg_299_0:spellSlot(3).level] + 0.3 * (arg_299_0.flatMagicDamageMod * arg_299_0.percentMagicDamageMod)
			end
		},
		{
			stage = 2,
			slot = 2,
			damagetype = 2,
			damage = function(arg_300_0, arg_300_1, arg_300_2)
				return ({
					70,
					130,
					190,
					250
				})[arg_300_0:spellSlot(3).level] + 0.45 * (arg_300_0.flatMagicDamageMod * arg_300_0.percentMagicDamageMod)
			end
		}
	},
	nocturne = {
		{
			stage = 1,
			slot = 0,
			damagetype = 1,
			damage = function(arg_301_0, arg_301_1, arg_301_2)
				return ({
					65,
					110,
					155,
					200,
					245
				})[arg_301_2] + 0.75 * ((arg_301_0.baseAttackDamage + arg_301_0.flatPhysicalDamageMod) * arg_301_0.percentPhysicalDamageMod - arg_301_0.baseAttackDamage)
			end
		},
		{
			stage = 1,
			slot = 2,
			damagetype = 2,
			damage = function(arg_302_0, arg_302_1, arg_302_2)
				return ({
					80,
					125,
					170,
					215,
					260
				})[arg_302_2] + arg_302_0.flatMagicDamageMod * arg_302_0.percentMagicDamageMod
			end
		},
		{
			stage = 1,
			slot = 3,
			damagetype = 1,
			damage = function(arg_303_0, arg_303_1, arg_303_2)
				return ({
					150,
					250,
					350
				})[arg_303_2] + 1.2 * ((arg_303_0.baseAttackDamage + arg_303_0.flatPhysicalDamageMod) * arg_303_0.percentPhysicalDamageMod - arg_303_0.baseAttackDamage)
			end
		}
	},
	nunu = {
		{
			stage = 1,
			slot = 0,
			damagetype = 3,
			damage = function(arg_304_0, arg_304_1, arg_304_2)
				return ({
					340,
					500,
					660,
					820,
					980
				})[arg_304_2]
			end
		},
		{
			stage = 1,
			slot = 2,
			damagetype = 2,
			damage = function(arg_305_0, arg_305_1, arg_305_2)
				return ({
					80,
					120,
					160,
					200,
					240
				})[arg_305_2] + 0.9 * (arg_305_0.flatMagicDamageMod * arg_305_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 3,
			damagetype = 2,
			damage = function(arg_306_0, arg_306_1, arg_306_2)
				return ({
					625,
					875,
					1125
				})[arg_306_2] + 2.5 * (arg_306_0.flatMagicDamageMod * arg_306_0.percentMagicDamageMod)
			end
		}
	},
	olaf = {
		{
			stage = 1,
			slot = 0,
			damagetype = 1,
			damage = function(arg_307_0, arg_307_1, arg_307_2)
				return ({
					80,
					125,
					170,
					215,
					260
				})[arg_307_2] + ((arg_307_0.baseAttackDamage + arg_307_0.flatPhysicalDamageMod) * arg_307_0.percentPhysicalDamageMod - arg_307_0.baseAttackDamage)
			end
		},
		{
			stage = 1,
			slot = 2,
			damagetype = 3,
			damage = function(arg_308_0, arg_308_1, arg_308_2)
				return ({
					70,
					115,
					160,
					205,
					250
				})[arg_308_2] + 0.4 * ((arg_308_0.baseAttackDamage + arg_308_0.flatPhysicalDamageMod) * arg_308_0.percentPhysicalDamageMod)
			end
		}
	},
	orianna = {
		{
			stage = 1,
			slot = 0,
			damagetype = 2,
			damage = function(arg_309_0, arg_309_1, arg_309_2)
				return ({
					60,
					90,
					120,
					150,
					180
				})[arg_309_2] + 0.5 * (arg_309_0.flatMagicDamageMod * arg_309_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 1,
			damagetype = 2,
			damage = function(arg_310_0, arg_310_1, arg_310_2)
				return ({
					60,
					105,
					150,
					195,
					240
				})[arg_310_2] + 0.7 * (arg_310_0.flatMagicDamageMod * arg_310_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 2,
			damagetype = 2,
			damage = function(arg_311_0, arg_311_1, arg_311_2)
				return ({
					60,
					90,
					120,
					150,
					180
				})[arg_311_2] + 0.3 * (arg_311_0.flatMagicDamageMod * arg_311_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 3,
			damagetype = 2,
			damage = function(arg_312_0, arg_312_1, arg_312_2)
				return ({
					150,
					225,
					300
				})[arg_312_2] + 0.7 * (arg_312_0.flatMagicDamageMod * arg_312_0.percentMagicDamageMod)
			end
		}
	},
	ornn = {
		{
			stage = 1,
			slot = 0,
			damagetype = 1,
			damage = function(arg_313_0, arg_313_1, arg_313_2)
				return ({
					20,
					50,
					80,
					110,
					140
				})[arg_313_2] + (arg_313_0.baseAttackDamage + arg_313_0.flatPhysicalDamageMod) * arg_313_0.percentPhysicalDamageMod
			end
		},
		{
			stage = 1,
			slot = 1,
			damagetype = 2,
			damage = function(arg_314_0, arg_314_1, arg_314_2)
				return ({
					0.1,
					0.12,
					0.14,
					0.16,
					0.18
				})[arg_314_2] * arg_314_1.maxHealth
			end
		},
		{
			stage = 1,
			slot = 2,
			damagetype = 1,
			damage = function(arg_315_0, arg_315_1, arg_315_2)
				return ({
					30,
					50,
					70,
					90,
					110
				})[arg_315_2] + 0.3 * arg_315_0.bonusArmor + 0.3 * arg_315_0.bonusSpellBlock
			end
		},
		{
			stage = 2,
			slot = 2,
			damagetype = 1,
			damage = function(arg_316_0, arg_316_1, arg_316_2)
				return ({
					50,
					90,
					130,
					170,
					210
				})[arg_316_2] + 0.3 * arg_316_0.bonusArmor + 0.3 * arg_316_0.bonusSpellBlock
			end
		},
		{
			stage = 1,
			slot = 3,
			damagetype = 2,
			damage = function(arg_317_0, arg_317_1, arg_317_2)
				return ({
					125,
					175,
					225
				})[arg_317_2] + 0.2 * (arg_317_0.flatMagicDamageMod * arg_317_0.percentMagicDamageMod)
			end
		}
	},
	pantheon = {
		{
			stage = 1,
			slot = 0,
			damagetype = 1,
			damage = function(arg_318_0, arg_318_1, arg_318_2)
				return ({
					75,
					115,
					155,
					195,
					235
				})[arg_318_2] + 1.4 * ((arg_318_0.baseAttackDamage + arg_318_0.flatPhysicalDamageMod) * arg_318_0.percentPhysicalDamageMod - arg_318_0.baseAttackDamage)
			end
		},
		{
			stage = 1,
			slot = 1,
			damagetype = 2,
			damage = function(arg_319_0, arg_319_1, arg_319_2)
				return ({
					50,
					75,
					100,
					125,
					150
				})[arg_319_2] + arg_319_0.flatMagicDamageMod * arg_319_0.percentMagicDamageMod
			end
		},
		{
			stage = 1,
			slot = 2,
			damagetype = 1,
			damage = function(arg_320_0, arg_320_1, arg_320_2)
				return ({
					100,
					150,
					200,
					250,
					300
				})[arg_320_2] + 3 * ((arg_320_0.baseAttackDamage + arg_320_0.flatPhysicalDamageMod) * arg_320_0.percentPhysicalDamageMod - arg_320_0.baseAttackDamage)
			end
		},
		{
			stage = 1,
			slot = 3,
			damagetype = 2,
			damage = function(arg_321_0, arg_321_1, arg_321_2)
				return ({
					300,
					500,
					700
				})[arg_321_2] + arg_321_0.flatMagicDamageMod * arg_321_0.percentMagicDamageMod
			end
		},
		{
			stage = 2,
			slot = 3,
			damagetype = 2,
			damage = function(arg_322_0, arg_322_1, arg_322_2)
				return (({
					300,
					500,
					700
				})[arg_322_2] + arg_322_0.flatMagicDamageMod * arg_322_0.percentMagicDamageMod) * 0.5
			end
		}
	},
	poppy = {
		{
			stage = 1,
			slot = 0,
			damagetype = 1,
			damage = function(arg_323_0, arg_323_1, arg_323_2)
				return ({
					40,
					60,
					80,
					100,
					120
				})[arg_323_2] + 0.8 * ((arg_323_0.baseAttackDamage + arg_323_0.flatPhysicalDamageMod) * arg_323_0.percentPhysicalDamageMod - arg_323_0.baseAttackDamage) + 0.08 * arg_323_1.maxHealth
			end
		},
		{
			stage = 2,
			slot = 0,
			damagetype = 1,
			damage = function(arg_324_0, arg_324_1, arg_324_2)
				return ({
					80,
					120,
					160,
					200,
					240
				})[arg_324_2] + 1.6 * ((arg_324_0.baseAttackDamage + arg_324_0.flatPhysicalDamageMod) * arg_324_0.percentPhysicalDamageMod - arg_324_0.baseAttackDamage) + 0.16 * arg_324_1.maxHealth
			end
		},
		{
			stage = 1,
			slot = 1,
			damagetype = 2,
			damage = function(arg_325_0, arg_325_1, arg_325_2)
				return ({
					70,
					110,
					150,
					190,
					230
				})[arg_325_2] + 0.7 * (arg_325_0.flatMagicDamageMod * arg_325_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 2,
			damagetype = 1,
			damage = function(arg_326_0, arg_326_1, arg_326_2)
				return ({
					55,
					75,
					95,
					115,
					135
				})[arg_326_2] + 0.5 * ((arg_326_0.baseAttackDamage + arg_326_0.flatPhysicalDamageMod) * arg_326_0.percentPhysicalDamageMod - arg_326_0.baseAttackDamage)
			end
		},
		{
			stage = 2,
			slot = 2,
			damagetype = 1,
			damage = function(arg_327_0, arg_327_1, arg_327_2)
				return ({
					110,
					150,
					190,
					230,
					270
				})[arg_327_2] + ((arg_327_0.baseAttackDamage + arg_327_0.flatPhysicalDamageMod) * arg_327_0.percentPhysicalDamageMod - arg_327_0.baseAttackDamage)
			end
		},
		{
			stage = 1,
			slot = 3,
			damagetype = 1,
			damage = function(arg_328_0, arg_328_1, arg_328_2)
				return ({
					200,
					300,
					400
				})[arg_328_2] + 0.9 * ((arg_328_0.baseAttackDamage + arg_328_0.flatPhysicalDamageMod) * arg_328_0.percentPhysicalDamageMod - arg_328_0.baseAttackDamage)
			end
		},
		{
			stage = 2,
			slot = 3,
			damagetype = 1,
			damage = function(arg_329_0, arg_329_1, arg_329_2)
				return ({
					100,
					150,
					200
				})[arg_329_2] + 0.45 * ((arg_329_0.baseAttackDamage + arg_329_0.flatPhysicalDamageMod) * arg_329_0.percentPhysicalDamageMod - arg_329_0.baseAttackDamage)
			end
		}
	},
	pyke = {
		{
			stage = 1,
			slot = 3,
			damagetype = 1,
			damage = function(arg_330_0, arg_330_1, arg_330_2)
				return ({
					250,
					290,
					330,
					370,
					400,
					430,
					450,
					470,
					490,
					510,
					530,
					540,
					550
				})[player.levelRef - 5] + player.flatPhysicalDamageMod * 0.6
			end
		}
	},
	quinn = {
		{
			stage = 1,
			slot = 0,
			damagetype = 1,
			damage = function(arg_331_0, arg_331_1, arg_331_2)
				return ({
					20,
					45,
					70,
					95,
					120
				})[arg_331_2] + ({
					0.8,
					0.9,
					1,
					1.1,
					1.2
				})[arg_331_2] * ((arg_331_0.baseAttackDamage + arg_331_0.flatPhysicalDamageMod) * arg_331_0.percentPhysicalDamageMod) + 0.5 * (arg_331_0.flatMagicDamageMod * arg_331_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 2,
			damagetype = 1,
			damage = function(arg_332_0, arg_332_1, arg_332_2)
				return ({
					40,
					70,
					100,
					130,
					160
				})[arg_332_2] + 0.2 * ((arg_332_0.baseAttackDamage + arg_332_0.flatPhysicalDamageMod) * arg_332_0.percentPhysicalDamageMod - arg_332_0.baseAttackDamage)
			end
		},
		{
			stage = 1,
			slot = 3,
			damagetype = 1,
			damage = function(arg_333_0, arg_333_1, arg_333_2)
				return 0.4 * ((arg_333_0.baseAttackDamage + arg_333_0.flatPhysicalDamageMod) * arg_333_0.percentPhysicalDamageMod)
			end
		}
	},
	rakan = {
		{
			stage = 1,
			slot = 0,
			damagetype = 2,
			damage = function(arg_334_0, arg_334_1, arg_334_2)
				return ({
					70,
					115,
					160,
					205,
					250
				})[arg_334_2] + 0.5 * (arg_334_0.flatMagicDamageMod * arg_334_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 1,
			damagetype = 2,
			damage = function(arg_335_0, arg_335_1, arg_335_2)
				return ({
					70,
					115,
					160,
					205,
					250
				})[arg_335_2] + 0.5 * (arg_335_0.flatMagicDamageMod * arg_335_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 3,
			damagetype = 2,
			damage = function(arg_336_0, arg_336_1, arg_336_2)
				return ({
					100,
					200,
					300
				})[arg_336_2] + 0.5 * (arg_336_0.flatMagicDamageMod * arg_336_0.percentMagicDamageMod)
			end
		}
	},
	rammus = {
		{
			stage = 1,
			slot = 0,
			damagetype = 2,
			damage = function(arg_337_0, arg_337_1, arg_337_2)
				return ({
					100,
					130,
					160,
					190,
					220
				})[arg_337_2] + 1 * (arg_337_0.flatMagicDamageMod * arg_337_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 3,
			damagetype = 2,
			damage = function(arg_338_0, arg_338_1, arg_338_2)
				return ({
					160,
					265,
					370
				})[arg_338_2] + 0.9 * (arg_338_0.flatMagicDamageMod * arg_338_0.percentMagicDamageMod)
			end
		}
	},
	reksai = {
		{
			stage = 1,
			slot = 0,
			damagetype = 1,
			damage = function(arg_339_0, arg_339_1, arg_339_2)
				return ({
					20,
					25,
					30,
					35,
					40
				})[arg_339_2] + 0.4 * ((arg_339_0.baseAttackDamage + arg_339_0.flatPhysicalDamageMod) * arg_339_0.percentPhysicalDamageMod - arg_339_0.baseAttackDamage)
			end
		},
		{
			stage = 2,
			slot = 0,
			damagetype = 1,
			damage = function(arg_340_0, arg_340_1, arg_340_2)
				return ({
					60,
					90,
					120,
					150,
					180
				})[arg_340_2] + 0.4 * ((arg_340_0.baseAttackDamage + arg_340_0.flatPhysicalDamageMod) * arg_340_0.percentPhysicalDamageMod - arg_340_0.baseAttackDamage) + 0.7 * (arg_340_0.flatMagicDamageMod * arg_340_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 1,
			damagetype = 1,
			damage = function(arg_341_0, arg_341_1, arg_341_2)
				return ({
					55,
					70,
					85,
					100,
					115
				})[arg_341_2] + 0.8 * ((arg_341_0.baseAttackDamage + arg_341_0.flatPhysicalDamageMod) * arg_341_0.percentPhysicalDamageMod - arg_341_0.baseAttackDamage)
			end
		},
		{
			stage = 1,
			slot = 2,
			damagetype = 1,
			damage = function(arg_342_0, arg_342_1, arg_342_2)
				return ({
					55,
					65,
					75,
					85,
					95
				})[arg_342_2] + 0.85 * ((arg_342_0.baseAttackDamage + arg_342_0.flatPhysicalDamageMod) * arg_342_0.percentPhysicalDamageMod - arg_342_0.baseAttackDamage)
			end
		},
		{
			stage = 1,
			slot = 3,
			damagetype = 1,
			damage = function(arg_343_0, arg_343_1, arg_343_2)
				return ({
					100,
					250,
					400
				})[arg_343_2] + 1.85 * ((arg_343_0.baseAttackDamage + arg_343_0.flatPhysicalDamageMod) * arg_343_0.percentPhysicalDamageMod - arg_343_0.baseAttackDamage) + ({
					0.2,
					0.25,
					0.3
				})[arg_343_2] * (arg_343_1.maxHealth - arg_343_1.health)
			end
		}
	},
	renekton = {
		{
			stage = 1,
			slot = 0,
			damagetype = 1,
			damage = function(arg_344_0, arg_344_1, arg_344_2)
				return ({
					65,
					95,
					125,
					155,
					185
				})[arg_344_2] + 0.8 * ((arg_344_0.baseAttackDamage + arg_344_0.flatPhysicalDamageMod) * arg_344_0.percentPhysicalDamageMod - arg_344_0.baseAttackDamage)
			end
		},
		{
			stage = 2,
			slot = 0,
			damagetype = 1,
			damage = function(arg_345_0, arg_345_1, arg_345_2)
				return (({
					65,
					95,
					125,
					155,
					185
				})[arg_345_2] + 0.8 * ((arg_345_0.baseAttackDamage + arg_345_0.flatPhysicalDamageMod) * arg_345_0.percentPhysicalDamageMod - arg_345_0.baseAttackDamage)) * 1.5
			end
		},
		{
			stage = 1,
			slot = 1,
			damagetype = 1,
			damage = function(arg_346_0, arg_346_1, arg_346_2)
				return ({
					5,
					15,
					25,
					35,
					45
				})[arg_346_2] + 0.75 * ((arg_346_0.baseAttackDamage + arg_346_0.flatPhysicalDamageMod) * arg_346_0.percentPhysicalDamageMod)
			end
		},
		{
			stage = 2,
			slot = 1,
			damagetype = 1,
			damage = function(arg_347_0, arg_347_1, arg_347_2)
				return ({
					15,
					45,
					75,
					105,
					135
				})[arg_347_2] + 2.25 * ((arg_347_0.baseAttackDamage + arg_347_0.flatPhysicalDamageMod) * arg_347_0.percentPhysicalDamageMod)
			end
		},
		{
			stage = 1,
			slot = 2,
			damagetype = 1,
			damage = function(arg_348_0, arg_348_1, arg_348_2)
				return ({
					40,
					70,
					100,
					130,
					160
				})[arg_348_2] + 0.9 * ((arg_348_0.baseAttackDamage + arg_348_0.flatPhysicalDamageMod) * arg_348_0.percentPhysicalDamageMod - arg_348_0.baseAttackDamage)
			end
		},
		{
			stage = 2,
			slot = 2,
			damagetype = 1,
			damage = function(arg_349_0, arg_349_1, arg_349_2)
				return ({
					55,
					100,
					145,
					190,
					235
				})[arg_349_2] + 1.35 * ((arg_349_0.baseAttackDamage + arg_349_0.flatPhysicalDamageMod) * arg_349_0.percentPhysicalDamageMod - arg_349_0.baseAttackDamage)
			end
		},
		{
			stage = 1,
			slot = 3,
			damagetype = 2,
			damage = function(arg_350_0, arg_350_1, arg_350_2)
				return ({
					40,
					80,
					120
				})[arg_350_2] + 0.2 * (arg_350_0.flatMagicDamageMod * arg_350_0.percentMagicDamageMod)
			end
		}
	},
	rengar = {
		{
			stage = 1,
			slot = 0,
			damagetype = 1,
			damage = function(arg_351_0, arg_351_1, arg_351_2)
				return ({
					30,
					50,
					70,
					90,
					110
				})[arg_351_2] + ({
					0.2,
					0.3,
					0.4,
					0.5,
					0.6
				})[arg_351_2] * ((arg_351_0.baseAttackDamage + arg_351_0.flatPhysicalDamageMod) * arg_351_0.percentPhysicalDamageMod - arg_351_0.baseAttackDamage)
			end
		},
		{
			stage = 2,
			slot = 0,
			damagetype = 1,
			damage = function(arg_352_0, arg_352_1, arg_352_2)
				return 104 + 16 * arg_352_0.levelRef + 2.2 * ((arg_352_0.baseAttackDamage + arg_352_0.flatPhysicalDamageMod) * arg_352_0.percentPhysicalDamageMod - arg_352_0.baseAttackDamage)
			end
		},
		{
			stage = 1,
			slot = 1,
			damagetype = 2,
			damage = function(arg_353_0, arg_353_1, arg_353_2)
				return ({
					50,
					80,
					110,
					140,
					170
				})[arg_353_2] + 0.8 * (arg_353_0.flatMagicDamageMod * arg_353_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 2,
			damagetype = 1,
			damage = function(arg_354_0, arg_354_1, arg_354_2)
				return ({
					50,
					100,
					145,
					190,
					235
				})[arg_354_2] + 0.7 * ((arg_354_0.baseAttackDamage + arg_354_0.flatPhysicalDamageMod) * arg_354_0.percentPhysicalDamageMod - arg_354_0.baseAttackDamage)
			end
		},
		{
			stage = 2,
			slot = 2,
			damagetype = 1,
			damage = function(arg_355_0, arg_355_1, arg_355_2)
				return 35 + 15 * arg_355_0.levelRef + 0.7 * ((arg_355_0.baseAttackDamage + arg_355_0.flatPhysicalDamageMod) * arg_355_0.percentPhysicalDamageMod - arg_355_0.baseAttackDamage)
			end
		}
	},
	riven = {
		{
			stage = 1,
			slot = 0,
			damagetype = 1,
			damage = function(arg_356_0, arg_356_1, arg_356_2)
				return ({
					15,
					35,
					55,
					75,
					95
				})[arg_356_2] + ({
					0.45,
					0.5,
					0.55,
					0.6,
					0.65
				})[arg_356_2] * ((arg_356_0.baseAttackDamage + arg_356_0.flatPhysicalDamageMod) * arg_356_0.percentPhysicalDamageMod)
			end
		},
		{
			stage = 1,
			slot = 1,
			damagetype = 1,
			damage = function(arg_357_0, arg_357_1, arg_357_2)
				return ({
					55,
					85,
					115,
					145,
					175
				})[arg_357_2] + ((arg_357_0.baseAttackDamage + arg_357_0.flatPhysicalDamageMod) * arg_357_0.percentPhysicalDamageMod - arg_357_0.baseAttackDamage)
			end
		},
		{
			stage = 1,
			slot = 3,
			damagetype = 1,
			damage = function(arg_358_0, arg_358_1, arg_358_2)
				return ({
					100,
					150,
					200
				})[arg_358_2] + 0.6 * ((arg_358_0.baseAttackDamage + arg_358_0.flatPhysicalDamageMod) * arg_358_0.percentPhysicalDamageMod - arg_358_0.baseAttackDamage)
			end
		}
	},
	rumble = {
		{
			stage = 1,
			slot = 0,
			damagetype = 2,
			damage = function(arg_359_0, arg_359_1, arg_359_2)
				return ({
					135,
					180,
					225,
					270,
					315
				})[arg_359_2] + 1.1 * (arg_359_0.flatMagicDamageMod * arg_359_0.percentMagicDamageMod)
			end
		},
		{
			stage = 2,
			slot = 0,
			damagetype = 2,
			damage = function(arg_360_0, arg_360_1, arg_360_2)
				return ({
					202.5,
					270,
					337.5,
					405,
					472.5
				})[arg_360_2] + 1.65 * (arg_360_0.flatMagicDamageMod * arg_360_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 2,
			damagetype = 2,
			damage = function(arg_361_0, arg_361_1, arg_361_2)
				return ({
					60,
					85,
					110,
					135,
					160
				})[arg_361_2] + 0.4 * (arg_361_0.flatMagicDamageMod * arg_361_0.percentMagicDamageMod)
			end
		},
		{
			stage = 2,
			slot = 2,
			damagetype = 2,
			damage = function(arg_362_0, arg_362_1, arg_362_2)
				return ({
					90,
					127.5,
					165,
					202.5,
					240
				})[arg_362_2] + 0.6 * (arg_362_0.flatMagicDamageMod * arg_362_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 3,
			damagetype = 2,
			damage = function(arg_363_0, arg_363_1, arg_363_2)
				return ({
					130,
					185,
					240
				})[arg_363_2] + 0.3 * (arg_363_0.flatMagicDamageMod * arg_363_0.percentMagicDamageMod)
			end
		},
		{
			stage = 2,
			slot = 3,
			damagetype = 2,
			damage = function(arg_364_0, arg_364_1, arg_364_2)
				return ({
					650,
					925,
					1200
				})[arg_364_2] + 1.5 * (arg_364_0.flatMagicDamageMod * arg_364_0.percentMagicDamageMod)
			end
		}
	},
	ryze = {
		{
			stage = 1,
			slot = 0,
			damagetype = 2,
			damage = function(arg_365_0, arg_365_1, arg_365_2)
				return ({
					60,
					85,
					110,
					135,
					160,
					185
				})[arg_365_2] + 0.45 * (arg_365_0.flatMagicDamageMod * arg_365_0.percentMagicDamageMod) + 0.02 * arg_365_0.maxPar
			end
		},
		{
			stage = 2,
			slot = 0,
			damagetype = 2,
			damage = function(arg_366_0, arg_366_1, arg_366_2)
				return (({
					60,
					85,
					110,
					135,
					160,
					185
				})[arg_366_2] + 0.45 * (arg_366_0.flatMagicDamageMod * arg_366_0.percentMagicDamageMod) + 0.02 * arg_366_0.maxPar) * ({
					1.4,
					1.5,
					1.6,
					1.7,
					1.8
				})[arg_366_2]
			end
		},
		{
			stage = 1,
			slot = 1,
			damagetype = 2,
			damage = function(arg_367_0, arg_367_1, arg_367_2)
				return ({
					80,
					100,
					120,
					140,
					160
				})[arg_367_2] + 0.6 * (arg_367_0.flatMagicDamageMod * arg_367_0.percentMagicDamageMod) + 0.01 * arg_367_0.maxPar
			end
		},
		{
			stage = 1,
			slot = 2,
			damagetype = 2,
			damage = function(arg_368_0, arg_368_1, arg_368_2)
				return ({
					50,
					75,
					100,
					125,
					150
				})[arg_368_2] + 0.3 * (arg_368_0.flatMagicDamageMod * arg_368_0.percentMagicDamageMod) + 0.02 * arg_368_0.maxPar
			end
		}
	},
	sejuani = {
		{
			stage = 1,
			slot = 0,
			damagetype = 2,
			damage = function(arg_369_0, arg_369_1, arg_369_2)
				return ({
					60,
					90,
					120,
					150,
					180
				})[arg_369_2] + 0.4 * (arg_369_0.flatMagicDamageMod * arg_369_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 1,
			damagetype = 1,
			damage = function(arg_370_0, arg_370_1, arg_370_2)
				return ({
					50,
					90,
					130,
					170,
					210
				})[arg_370_2] + 0.06 * arg_370_0.maxHealth
			end
		},
		{
			stage = 1,
			slot = 2,
			damagetype = 2,
			damage = function(arg_371_0, arg_371_1, arg_371_2)
				return ({
					20,
					30,
					40,
					50,
					60
				})[arg_371_2] + 0.3 * (arg_371_0.flatMagicDamageMod * arg_371_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 3,
			damagetype = 2,
			damage = function(arg_372_0, arg_372_1, arg_372_2)
				return ({
					150,
					250,
					350
				})[arg_372_2] + 0.8 * (arg_372_0.flatMagicDamageMod * arg_372_0.percentMagicDamageMod)
			end
		}
	},
	shaco = {
		{
			stage = 1,
			slot = 1,
			damagetype = 2,
			damage = function(arg_373_0, arg_373_1, arg_373_2)
				return ({
					35,
					50,
					65,
					80,
					95
				})[arg_373_2] + 0.2 * (arg_373_0.flatMagicDamageMod * arg_373_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 2,
			damagetype = 1,
			damage = function(arg_374_0, arg_374_1, arg_374_2)
				return ({
					55,
					80,
					105,
					130,
					155
				})[arg_374_2] + ({
					0.6,
					0.75,
					0.9,
					1.05,
					1.2
				})[arg_374_2] * ((arg_374_0.baseAttackDamage + arg_374_0.flatPhysicalDamageMod) * arg_374_0.percentPhysicalDamageMod - arg_374_0.baseAttackDamage) + 0.75 * (arg_374_0.flatMagicDamageMod * arg_374_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 3,
			damagetype = 2,
			damage = function(arg_375_0, arg_375_1, arg_375_2)
				return ({
					200,
					300,
					400
				})[arg_375_2] + arg_375_0.flatMagicDamageMod * arg_375_0.percentMagicDamageMod
			end
		}
	},
	shen = {
		{
			stage = 1,
			slot = 0,
			damagetype = 2,
			damage = function(arg_376_0, arg_376_1, arg_376_2)
				return ({
					15,
					15,
					15,
					30,
					30,
					30,
					45,
					45,
					45,
					60,
					60,
					60,
					75,
					75,
					75,
					90,
					90,
					90
				})[arg_376_0.levelRef] + (({
					0.06,
					0.075,
					0.09,
					0.105,
					0.12
				})[arg_376_2] + 0.045 * (arg_376_0.flatMagicDamageMod * arg_376_0.percentMagicDamageMod / 100)) * arg_376_1.maxHealth
			end
		},
		{
			stage = 2,
			slot = 0,
			damagetype = 2,
			damage = function(arg_377_0, arg_377_1, arg_377_2)
				return ({
					15,
					15,
					15,
					30,
					30,
					30,
					45,
					45,
					45,
					60,
					60,
					60,
					75,
					75,
					75,
					90,
					90,
					90
				})[arg_377_0.levelRef] + (({
					0.12,
					0.135,
					0.15,
					0.165,
					0.18
				})[arg_377_2] + 0.06 * (arg_377_0.flatMagicDamageMod * arg_377_0.percentMagicDamageMod / 100)) * arg_377_1.maxHealth
			end
		},
		{
			stage = 1,
			slot = 2,
			damagetype = 1,
			damage = function(arg_378_0, arg_378_1, arg_378_2)
				return ({
					50,
					75,
					100,
					125,
					150
				})[arg_378_2] + 0.11 * arg_378_0.maxHealth
			end
		}
	},
	shyvana = {
		{
			stage = 1,
			slot = 0,
			damagetype = 1,
			damage = function(arg_379_0, arg_379_1, arg_379_2)
				return ({
					0.4,
					0.55,
					0.7,
					0.85,
					1
				})[arg_379_2] * ((arg_379_0.baseAttackDamage + arg_379_0.flatPhysicalDamageMod) * arg_379_0.percentPhysicalDamageMod)
			end
		},
		{
			stage = 1,
			slot = 1,
			damagetype = 2,
			damage = function(arg_380_0, arg_380_1, arg_380_2)
				return ({
					20,
					32,
					45,
					57,
					70
				})[arg_380_2] + 0.2 * ((arg_380_0.baseAttackDamage + arg_380_0.flatPhysicalDamageMod) * arg_380_0.percentPhysicalDamageMod - arg_380_0.baseAttackDamage) + 0.1 * (arg_380_0.flatMagicDamageMod * arg_380_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 2,
			damagetype = 2,
			damage = function(arg_381_0, arg_381_1, arg_381_2)
				return ({
					60,
					100,
					140,
					180,
					220
				})[arg_381_2] + 0.3 * ((arg_381_0.baseAttackDamage + arg_381_0.flatPhysicalDamageMod) * arg_381_0.percentPhysicalDamageMod) + 0.3 * (arg_381_0.flatMagicDamageMod * arg_381_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 3,
			damagetype = 2,
			damage = function(arg_382_0, arg_382_1, arg_382_2)
				return ({
					150,
					250,
					350
				})[arg_382_2] + 0.7 * (arg_382_0.flatMagicDamageMod * arg_382_0.percentMagicDamageMod)
			end
		}
	},
	singed = {
		{
			stage = 1,
			slot = 0,
			damagetype = 2,
			damage = function(arg_383_0, arg_383_1, arg_383_2)
				return ({
					40,
					60,
					80,
					100,
					120
				})[arg_383_2] + 0.8 * (arg_383_0.flatMagicDamageMod * arg_383_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 2,
			damagetype = 2,
			damage = function(arg_384_0, arg_384_1, arg_384_2)
				return ({
					50,
					65,
					80,
					95,
					110
				})[arg_384_2] + 0.75 * (arg_384_0.flatMagicDamageMod * arg_384_0.percentMagicDamageMod) + ({
					0.06,
					0.065,
					0.07,
					0.075,
					0.08
				})[arg_384_2] * arg_384_1.maxHealth
			end
		}
	},
	sion = {
		{
			stage = 1,
			slot = 0,
			damagetype = 1,
			damage = function(arg_385_0, arg_385_1, arg_385_2)
				return ({
					20,
					40,
					60,
					80,
					100
				})[arg_385_2] + 0.65 * ((arg_385_0.baseAttackDamage + arg_385_0.flatPhysicalDamageMod) * arg_385_0.percentPhysicalDamageMod)
			end
		},
		{
			stage = 2,
			slot = 0,
			damagetype = 1,
			damage = function(arg_386_0, arg_386_1, arg_386_2)
				return ({
					60,
					120,
					180,
					240,
					300
				})[arg_386_2] + 1.95 * ((arg_386_0.baseAttackDamage + arg_386_0.flatPhysicalDamageMod) * arg_386_0.percentPhysicalDamageMod)
			end
		},
		{
			stage = 1,
			slot = 1,
			damagetype = 2,
			damage = function(arg_387_0, arg_387_1, arg_387_2)
				return ({
					40,
					65,
					90,
					115,
					140
				})[arg_387_2] + 0.4 * (arg_387_0.flatMagicDamageMod * arg_387_0.percentMagicDamageMod) + ({
					0.1,
					0.11,
					0.12,
					0.13,
					0.14
				})[arg_387_2] * arg_387_1.maxHealth
			end
		},
		{
			stage = 1,
			slot = 2,
			damagetype = 2,
			damage = function(arg_388_0, arg_388_1, arg_388_2)
				return ({
					70,
					105,
					140,
					175,
					210
				})[arg_388_2] + 0.4 * (arg_388_0.flatMagicDamageMod * arg_388_0.percentMagicDamageMod)
			end
		},
		{
			stage = 2,
			slot = 2,
			damagetype = 2,
			damage = function(arg_389_0, arg_389_1, arg_389_2)
				return (({
					70,
					105,
					140,
					175,
					210
				})[arg_389_2] + 0.4 * (arg_389_0.flatMagicDamageMod * arg_389_0.percentMagicDamageMod)) * 1.3
			end
		},
		{
			stage = 1,
			slot = 3,
			damagetype = 1,
			damage = function(arg_390_0, arg_390_1, arg_390_2)
				return ({
					150,
					300,
					450
				})[arg_390_2] + 0.4 * ((arg_390_0.baseAttackDamage + arg_390_0.flatPhysicalDamageMod) * arg_390_0.percentPhysicalDamageMod - arg_390_0.baseAttackDamage)
			end
		},
		{
			stage = 2,
			slot = 3,
			damagetype = 1,
			damage = function(arg_391_0, arg_391_1, arg_391_2)
				return ({
					400,
					800,
					1200
				})[arg_391_2] + 0.8 * ((arg_391_0.baseAttackDamage + arg_391_0.flatPhysicalDamageMod) * arg_391_0.percentPhysicalDamageMod - arg_391_0.baseAttackDamage)
			end
		}
	},
	sivir = {
		{
			stage = 1,
			slot = 0,
			damagetype = 1,
			damage = function(arg_392_0, arg_392_1, arg_392_2)
				return ({
					35,
					55,
					75,
					95,
					115
				})[arg_392_2] + ({
					0.7,
					0.8,
					0.9,
					1,
					1.1
				})[arg_392_2] * ((arg_392_0.baseAttackDamage + arg_392_0.flatPhysicalDamageMod) * arg_392_0.percentPhysicalDamageMod) + 0.5 * (arg_392_0.flatMagicDamageMod * arg_392_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 1,
			damagetype = 1,
			damage = function(arg_393_0, arg_393_1, arg_393_2)
				return ({
					0.5,
					0.55,
					0.6,
					0.65,
					0.7
				})[arg_393_2] * ((arg_393_0.baseAttackDamage + arg_393_0.flatPhysicalDamageMod) * arg_393_0.percentPhysicalDamageMod)
			end
		}
	},
	skarner = {
		{
			stage = 1,
			slot = 0,
			damagetype = 1,
			damage = function(arg_394_0, arg_394_1, arg_394_2)
				return ({
					0.33,
					0.36,
					0.39,
					0.42,
					0.45
				})[arg_394_2] * ((arg_394_0.baseAttackDamage + arg_394_0.flatPhysicalDamageMod) * arg_394_0.percentPhysicalDamageMod)
			end
		},
		{
			stage = 2,
			slot = 0,
			damagetype = 2,
			damage = function(arg_395_0, arg_395_1, arg_395_2)
				return ({
					0.33,
					0.36,
					0.39,
					0.42,
					0.45
				})[arg_395_2] * ((arg_395_0.baseAttackDamage + arg_395_0.flatPhysicalDamageMod) * arg_395_0.percentPhysicalDamageMod) + 0.3 * (arg_395_0.flatMagicDamageMod * arg_395_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 2,
			damagetype = 2,
			damage = function(arg_396_0, arg_396_1, arg_396_2)
				return ({
					40,
					65,
					90,
					115,
					140
				})[arg_396_2] + 0.2 * (arg_396_0.flatMagicDamageMod * arg_396_0.percentMagicDamageMod)
			end
		},
		{
			stage = 2,
			slot = 2,
			damagetype = 1,
			damage = function(arg_397_0, arg_397_1, arg_397_2)
				return ({
					30,
					50,
					70,
					90,
					110
				})[arg_397_2]
			end
		},
		{
			stage = 1,
			slot = 3,
			damagetype = 2,
			damage = function(arg_398_0, arg_398_1, arg_398_2)
				return ({
					40,
					120,
					200
				})[arg_398_2] + arg_398_0.flatMagicDamageMod * arg_398_0.percentMagicDamageMod
			end
		},
		{
			stage = 2,
			slot = 3,
			damagetype = 1,
			damage = function(arg_399_0, arg_399_1, arg_399_2)
				return 1.2 * ((arg_399_0.baseAttackDamage + arg_399_0.flatPhysicalDamageMod) * arg_399_0.percentPhysicalDamageMod)
			end
		}
	},
	sona = {
		{
			stage = 1,
			slot = 0,
			damagetype = 2,
			damage = function(arg_400_0, arg_400_1, arg_400_2)
				return ({
					40,
					70,
					100,
					130,
					160
				})[arg_400_2] + 0.5 * (arg_400_0.flatMagicDamageMod * arg_400_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 3,
			damagetype = 2,
			damage = function(arg_401_0, arg_401_1, arg_401_2)
				return ({
					150,
					250,
					350
				})[arg_401_2] + 0.5 * (arg_401_0.flatMagicDamageMod * arg_401_0.percentMagicDamageMod)
			end
		}
	},
	soraka = {
		{
			stage = 1,
			slot = 0,
			damagetype = 2,
			damage = function(arg_402_0, arg_402_1, arg_402_2)
				return ({
					70,
					110,
					150,
					190,
					230
				})[arg_402_2] + 0.35 * (arg_402_0.flatMagicDamageMod * arg_402_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 2,
			damagetype = 2,
			damage = function(arg_403_0, arg_403_1, arg_403_2)
				return ({
					70,
					110,
					150,
					190,
					230
				})[arg_403_2] + 0.4 * (arg_403_0.flatMagicDamageMod * arg_403_0.percentMagicDamageMod)
			end
		}
	},
	swain = {
		{
			stage = 1,
			slot = 0,
			damagetype = 2,
			damage = function(arg_404_0, arg_404_1, arg_404_2)
				return ({
					30,
					47.5,
					65,
					82.5,
					100
				})[arg_404_2] + 0.3 * (arg_404_0.flatMagicDamageMod * arg_404_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 1,
			damagetype = 2,
			damage = function(arg_405_0, arg_405_1, arg_405_2)
				return ({
					80,
					120,
					160,
					200,
					240
				})[arg_405_2] + 0.7 * (arg_405_0.flatMagicDamageMod * arg_405_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 2,
			damagetype = 2,
			damage = function(arg_406_0, arg_406_1, arg_406_2)
				return ({
					60,
					96,
					132,
					168,
					204
				})[arg_406_2] + 1.2 * (arg_406_0.flatMagicDamageMod * arg_406_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 3,
			damagetype = 2,
			damage = function(arg_407_0, arg_407_1, arg_407_2)
				return ({
					50,
					70,
					90
				})[arg_407_2] + 0.2 * (arg_407_0.flatMagicDamageMod * arg_407_0.percentMagicDamageMod)
			end
		}
	},
	syndra = {
		{
			stage = 1,
			slot = 0,
			damagetype = 2,
			damage = function(arg_408_0, arg_408_1, arg_408_2)
				return ({
					50,
					95,
					140,
					185,
					230
				})[arg_408_2] + 0.65 * (arg_408_0.flatMagicDamageMod * arg_408_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 1,
			damagetype = 2,
			damage = function(arg_409_0, arg_409_1, arg_409_2)
				return ({
					70,
					110,
					150,
					190,
					230
				})[arg_409_2] + 0.7 * (arg_409_0.flatMagicDamageMod * arg_409_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 2,
			damagetype = 2,
			damage = function(arg_410_0, arg_410_1, arg_410_2)
				return ({
					70,
					115,
					160,
					205,
					250
				})[arg_410_2] + 0.6 * (arg_410_0.flatMagicDamageMod * arg_410_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 3,
			damagetype = 2,
			damage = function(arg_411_0, arg_411_1, arg_411_2)
				return ({
					270,
					405,
					540
				})[arg_411_2] + 0.6 * (arg_411_0.flatMagicDamageMod * arg_411_0.percentMagicDamageMod)
			end
		},
		{
			stage = 2,
			slot = 3,
			damagetype = 2,
			damage = function(arg_412_0, arg_412_1, arg_412_2)
				return ({
					90,
					135,
					180
				})[arg_412_2] + 0.2 * (arg_412_0.flatMagicDamageMod * arg_412_0.percentMagicDamageMod)
			end
		}
	},
	tahmkench = {
		{
			stage = 1,
			slot = 0,
			damagetype = 2,
			damage = function(arg_413_0, arg_413_1, arg_413_2)
				return ({
					80,
					130,
					180,
					230,
					280
				})[arg_413_2] + 0.7 * (arg_413_0.flatMagicDamageMod * arg_413_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 1,
			damagetype = 2,
			damage = function(arg_414_0, arg_414_1, arg_414_2)
				return (({
					0.2,
					0.23,
					0.26,
					0.29,
					0.32
				})[arg_414_2] + 0.02 * (arg_414_0.flatMagicDamageMod * arg_414_0.percentMagicDamageMod / 100)) * arg_414_1.maxHealth
			end
		},
		{
			stage = 2,
			slot = 1,
			damagetype = 2,
			damage = function(arg_415_0, arg_415_1, arg_415_2)
				return ({
					100,
					150,
					200,
					250,
					300
				})[arg_415_2] + 0.6 * (arg_415_0.flatMagicDamageMod * arg_415_0.percentMagicDamageMod)
			end
		}
	},
	taliyah = {
		{
			stage = 1,
			slot = 0,
			damagetype = 2,
			damage = function(arg_416_0, arg_416_1, arg_416_2)
				return ({
					70,
					95,
					120,
					145,
					170
				})[arg_416_2] + 0.45 * (arg_416_0.flatMagicDamageMod * arg_416_0.percentMagicDamageMod)
			end
		},
		{
			stage = 2,
			slot = 0,
			damagetype = 2,
			damage = function(arg_417_0, arg_417_1, arg_417_2)
				return ({
					182,
					247,
					312,
					377,
					442
				})[arg_417_2] + 1.17 * (arg_417_0.flatMagicDamageMod * arg_417_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 1,
			damagetype = 2,
			damage = function(arg_418_0, arg_418_1, arg_418_2)
				return ({
					60,
					80,
					100,
					120,
					140
				})[arg_418_2] + 0.4 * (arg_418_0.flatMagicDamageMod * arg_418_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 2,
			damagetype = 2,
			damage = function(arg_419_0, arg_419_1, arg_419_2)
				return ({
					70,
					90,
					110,
					130,
					150
				})[arg_419_2] + 0.4 * (arg_419_0.flatMagicDamageMod * arg_419_0.percentMagicDamageMod)
			end
		},
		{
			stage = 2,
			slot = 2,
			damagetype = 2,
			damage = function(arg_420_0, arg_420_1, arg_420_2)
				return ({
					140,
					180,
					220,
					260,
					300
				})[arg_420_2] + 0.8 * (arg_420_0.flatMagicDamageMod * arg_420_0.percentMagicDamageMod)
			end
		}
	},
	talon = {
		{
			stage = 1,
			slot = 0,
			damagetype = 1,
			damage = function(arg_421_0, arg_421_1, arg_421_2)
				return ({
					65,
					90,
					115,
					140,
					165
				})[arg_421_2] + 1.1 * ((arg_421_0.baseAttackDamage + arg_421_0.flatPhysicalDamageMod) * arg_421_0.percentPhysicalDamageMod - arg_421_0.baseAttackDamage)
			end
		},
		{
			stage = 2,
			slot = 0,
			damagetype = 1,
			damage = function(arg_422_0, arg_422_1, arg_422_2)
				return ({
					97.5,
					135,
					172.5,
					210,
					247.5
				})[arg_422_2] + 1.65 * ((arg_422_0.baseAttackDamage + arg_422_0.flatPhysicalDamageMod) * arg_422_0.percentPhysicalDamageMod - arg_422_0.baseAttackDamage)
			end
		},
		{
			stage = 1,
			slot = 1,
			damagetype = 1,
			damage = function(arg_423_0, arg_423_1, arg_423_2)
				return ({
					50,
					60,
					70,
					80,
					90
				})[arg_423_2] + 0.4 * ((arg_423_0.baseAttackDamage + arg_423_0.flatPhysicalDamageMod) * arg_423_0.percentPhysicalDamageMod - arg_423_0.baseAttackDamage)
			end
		},
		{
			stage = 2,
			slot = 1,
			damagetype = 1,
			damage = function(arg_424_0, arg_424_1, arg_424_2)
				return ({
					70,
					95,
					120,
					145,
					170
				})[arg_424_2] + 0.6 * ((arg_424_0.baseAttackDamage + arg_424_0.flatPhysicalDamageMod) * arg_424_0.percentPhysicalDamageMod - arg_424_0.baseAttackDamage)
			end
		},
		{
			stage = 1,
			slot = 3,
			damagetype = 1,
			damage = function(arg_425_0, arg_425_1, arg_425_2)
				return ({
					80,
					120,
					160
				})[arg_425_2] + 0.8 * ((arg_425_0.baseAttackDamage + arg_425_0.flatPhysicalDamageMod) * arg_425_0.percentPhysicalDamageMod - arg_425_0.baseAttackDamage)
			end
		}
	},
	taric = {
		{
			stage = 1,
			slot = 2,
			damagetype = 2,
			damage = function(arg_426_0, arg_426_1, arg_426_2)
				return ({
					105,
					150,
					195,
					240,
					285
				})[arg_426_2] + 0.5 * (arg_426_0.flatMagicDamageMod * arg_426_0.percentMagicDamageMod) + 0.3 * arg_426_0.bonusArmor
			end
		}
	},
	teemo = {
		{
			stage = 1,
			slot = 0,
			damagetype = 2,
			damage = function(arg_427_0, arg_427_1, arg_427_2)
				return ({
					80,
					125,
					170,
					215,
					260
				})[arg_427_2] + 0.8 * (arg_427_0.flatMagicDamageMod * arg_427_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 2,
			damagetype = 2,
			damage = function(arg_428_0, arg_428_1, arg_428_2)
				return ({
					34,
					68,
					102,
					136,
					170
				})[arg_428_2] + 0.7 * (arg_428_0.flatMagicDamageMod * arg_428_0.percentMagicDamageMod)
			end
		},
		{
			stage = 2,
			slot = 2,
			damagetype = 2,
			damage = function(arg_429_0, arg_429_1, arg_429_2)
				return ({
					10,
					20,
					30,
					40,
					50
				})[arg_429_2] + 0.3 * (arg_429_0.flatMagicDamageMod * arg_429_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 3,
			damagetype = 2,
			damage = function(arg_430_0, arg_430_1, arg_430_2)
				return ({
					200,
					325,
					450
				})[arg_430_2] + 0.5 * (arg_430_0.flatMagicDamageMod * arg_430_0.percentMagicDamageMod)
			end
		}
	},
	thresh = {
		{
			stage = 1,
			slot = 0,
			damagetype = 2,
			damage = function(arg_431_0, arg_431_1, arg_431_2)
				return ({
					80,
					120,
					160,
					200,
					240
				})[arg_431_2] + 0.5 * (arg_431_0.flatMagicDamageMod * arg_431_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 2,
			damagetype = 2,
			damage = function(arg_432_0, arg_432_1, arg_432_2)
				return ({
					65,
					95,
					125,
					155,
					185
				})[arg_432_2] + 0.4 * (arg_432_0.flatMagicDamageMod * arg_432_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 3,
			damagetype = 2,
			damage = function(arg_433_0, arg_433_1, arg_433_2)
				return ({
					250,
					400,
					550
				})[arg_433_2] + arg_433_0.flatMagicDamageMod * arg_433_0.percentMagicDamageMod
			end
		}
	},
	tristana = {
		{
			stage = 1,
			slot = 1,
			damagetype = 2,
			damage = function(arg_434_0, arg_434_1, arg_434_2)
				return ({
					60,
					110,
					160,
					210,
					260
				})[arg_434_2] + 0.5 * (arg_434_0.flatMagicDamageMod * arg_434_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 2,
			damagetype = 1,
			damage = function(arg_435_0, arg_435_1, arg_435_2)
				return ({
					60,
					70,
					80,
					90,
					100
				})[arg_435_2] + ({
					0.5,
					0.6,
					0.7,
					0.8,
					0.9
				})[arg_435_2] * ((arg_435_0.baseAttackDamage + arg_435_0.flatPhysicalDamageMod) * arg_435_0.percentPhysicalDamageMod - arg_435_0.baseAttackDamage) + 0.5 * (arg_435_0.flatMagicDamageMod * arg_435_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 3,
			damagetype = 2,
			damage = function(arg_436_0, arg_436_1, arg_436_2)
				return ({
					300,
					400,
					500
				})[arg_436_2] + arg_436_0.flatMagicDamageMod * arg_436_0.percentMagicDamageMod
			end
		}
	},
	trundle = {
		{
			stage = 1,
			slot = 0,
			damagetype = 1,
			damage = function(arg_437_0, arg_437_1, arg_437_2)
				return ({
					20,
					40,
					60,
					80,
					100
				})[arg_437_2] + ({
					0,
					0.5,
					0.1,
					0.15,
					0.2
				})[arg_437_2] * ((arg_437_0.baseAttackDamage + arg_437_0.flatPhysicalDamageMod) * arg_437_0.percentPhysicalDamageMod)
			end
		},
		{
			stage = 1,
			slot = 3,
			damagetype = 2,
			damage = function(arg_438_0, arg_438_1, arg_438_2)
				return (({
					0.2,
					0.275,
					0.35
				})[arg_438_2] + 0.02 * (arg_438_0.flatMagicDamageMod * arg_438_0.percentMagicDamageMod / 100)) * arg_438_1.maxHealth
			end
		}
	},
	tryndamere = {
		{
			stage = 1,
			slot = 2,
			damagetype = 1,
			damage = function(arg_439_0, arg_439_1, arg_439_2)
				return ({
					80,
					110,
					140,
					170,
					200
				})[arg_439_2] + 1.2 * ((arg_439_0.baseAttackDamage + arg_439_0.flatPhysicalDamageMod) * arg_439_0.percentPhysicalDamageMod - arg_439_0.baseAttackDamage) + arg_439_0.flatMagicDamageMod * arg_439_0.percentMagicDamageMod
			end
		}
	},
	twistedfate = {
		{
			stage = 1,
			slot = 0,
			damagetype = 2,
			damage = function(arg_440_0, arg_440_1, arg_440_2)
				return ({
					60,
					105,
					150,
					195,
					240
				})[arg_440_2] + 0.65 * (arg_440_0.flatMagicDamageMod * arg_440_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 1,
			damagetype = 2,
			damage = function(arg_441_0, arg_441_1, arg_441_2)
				return ({
					40,
					60,
					80,
					100,
					120
				})[arg_441_2] + (arg_441_0.baseAttackDamage + arg_441_0.flatPhysicalDamageMod) * arg_441_0.percentPhysicalDamageMod + 0.5 * (arg_441_0.flatMagicDamageMod * arg_441_0.percentMagicDamageMod)
			end
		},
		{
			stage = 2,
			slot = 1,
			damagetype = 2,
			damage = function(arg_442_0, arg_442_1, arg_442_2)
				return ({
					30,
					45,
					60,
					75,
					90
				})[arg_442_2] + (arg_442_0.baseAttackDamage + arg_442_0.flatPhysicalDamageMod) * arg_442_0.percentPhysicalDamageMod + 0.5 * (arg_442_0.flatMagicDamageMod * arg_442_0.percentMagicDamageMod)
			end
		},
		{
			stage = 3,
			slot = 1,
			damagetype = 2,
			damage = function(arg_443_0, arg_443_1, arg_443_2)
				return ({
					15,
					22.5,
					30,
					37.5,
					45
				})[arg_443_2] + (arg_443_0.baseAttackDamage + arg_443_0.flatPhysicalDamageMod) * arg_443_0.percentPhysicalDamageMod + 0.5 * (arg_443_0.flatMagicDamageMod * arg_443_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 2,
			damagetype = 2,
			damage = function(arg_444_0, arg_444_1, arg_444_2)
				return ({
					55,
					80,
					105,
					130,
					155
				})[arg_444_2] + 0.5 * (arg_444_0.flatMagicDamageMod * arg_444_0.percentMagicDamageMod)
			end
		}
	},
	twitch = {
		{
			stage = 1,
			slot = 2,
			damagetype = 1,
			damage = function(arg_445_0, arg_445_1, arg_445_2)
				return ove_0_13(arg_445_1, "twitchdeadlyvenom") * ({
					15,
					20,
					25,
					30,
					35
				})[arg_445_2] + 0.2 * (arg_445_0.flatMagicDamageMod * arg_445_0.percentMagicDamageMod) + 0.25 * ((arg_445_0.baseAttackDamage + arg_445_0.flatPhysicalDamageMod) * arg_445_0.percentPhysicalDamageMod - arg_445_0.baseAttackDamage) + ({
					20,
					35,
					50,
					65,
					80
				})[arg_445_2]
			end
		},
		{
			stage = 2,
			slot = 2,
			damagetype = 1,
			damage = function(arg_446_0, arg_446_1, arg_446_2)
				return ({
					15,
					20,
					25,
					30,
					35
				})[arg_446_2] + 0.2 * (arg_446_0.flatMagicDamageMod * arg_446_0.percentMagicDamageMod) + 0.25 * ((arg_446_0.baseAttackDamage + arg_446_0.flatPhysicalDamageMod) * arg_446_0.percentPhysicalDamageMod) + ({
					20,
					35,
					50,
					65,
					80
				})[arg_446_2]
			end
		}
	},
	udyr = {
		{
			stage = 1,
			slot = 0,
			damagetype = 1,
			damage = function(arg_447_0, arg_447_1, arg_447_2)
				return ({
					30,
					60,
					90,
					120,
					150
				})[arg_447_2] + ({
					120,
					135,
					150,
					165,
					180
				})[arg_447_2] / 100 * ((arg_447_0.baseAttackDamage + arg_447_0.flatPhysicalDamageMod) * arg_447_0.percentPhysicalDamageMod)
			end
		},
		{
			stage = 1,
			slot = 3,
			damagetype = 2,
			damage = function(arg_448_0, arg_448_1, arg_448_2)
				return ({
					10,
					20,
					30,
					40,
					50
				})[arg_448_2] + 0.25 * (arg_448_0.flatMagicDamageMod * arg_448_0.percentMagicDamageMod)
			end
		},
		{
			stage = 2,
			slot = 3,
			damagetype = 2,
			damage = function(arg_449_0, arg_449_1, arg_449_2)
				return ({
					40,
					80,
					120,
					160,
					200
				})[arg_449_2] + 0.6 * (arg_449_0.flatMagicDamageMod * arg_449_0.percentMagicDamageMod)
			end
		}
	},
	urgot = {
		{
			stage = 1,
			slot = 0,
			damagetype = 1,
			damage = function(arg_450_0, arg_450_1, arg_450_2)
				return ({
					25,
					70,
					115,
					160,
					205
				})[arg_450_2] + 0.7 * ((arg_450_0.baseAttackDamage + arg_450_0.flatPhysicalDamageMod) * arg_450_0.percentPhysicalDamageMod)
			end
		},
		{
			stage = 1,
			slot = 2,
			damagetype = 1,
			damage = function(arg_451_0, arg_451_1, arg_451_2)
				return ({
					60,
					100,
					140,
					180,
					220
				})[arg_451_2] + 0.5 * ((arg_451_0.baseAttackDamage + arg_451_0.flatPhysicalDamageMod) * arg_451_0.percentPhysicalDamageMod)
			end
		},
		{
			stage = 1,
			slot = 3,
			damagetype = 1,
			damage = function(arg_452_0, arg_452_1, arg_452_2)
				return ({
					50,
					175,
					300
				})[arg_452_2] + 0.5 * ((arg_452_0.baseAttackDamage + arg_452_0.flatPhysicalDamageMod) * arg_452_0.percentPhysicalDamageMod)
			end
		}
	},
	varus = {
		{
			stage = 1,
			slot = 0,
			damagetype = 1,
			damage = function(arg_453_0, arg_453_1, arg_453_2)
				return ({
					10,
					46.7,
					83.3,
					120,
					156.7
				})[arg_453_2] + (arg_453_0.baseAttackDamage + arg_453_0.flatPhysicalDamageMod) * arg_453_0.percentPhysicalDamageMod
			end
		},
		{
			stage = 2,
			slot = 0,
			damagetype = 1,
			damage = function(arg_454_0, arg_454_1, arg_454_2)
				return ({
					15,
					70,
					125,
					180,
					235
				})[arg_454_2] + 1.5 * ((arg_454_0.baseAttackDamage + arg_454_0.flatPhysicalDamageMod) * arg_454_0.percentPhysicalDamageMod)
			end
		},
		{
			stage = 1,
			slot = 1,
			damagetype = 2,
			damage = function(arg_455_0, arg_455_1, arg_455_2)
				return ({
					10,
					14,
					18,
					22,
					26
				})[arg_455_2] + 0.25 * (arg_455_0.flatMagicDamageMod * arg_455_0.percentMagicDamageMod)
			end
		},
		{
			stage = 2,
			slot = 1,
			damagetype = 2,
			damage = function(arg_456_0, arg_456_1, arg_456_2)
				return (({
					0.02,
					0.0275,
					0.035,
					0.0425,
					0.05
				})[arg_456_2] + 0.02 * (arg_456_0.flatMagicDamageMod * arg_456_0.percentMagicDamageMod / 100)) * arg_456_1.maxHealth
			end
		},
		{
			stage = 1,
			slot = 2,
			damagetype = 1,
			damage = function(arg_457_0, arg_457_1, arg_457_2)
				return ({
					70,
					105,
					140,
					175,
					210
				})[arg_457_2] + 0.6 * ((arg_457_0.baseAttackDamage + arg_457_0.flatPhysicalDamageMod) * arg_457_0.percentPhysicalDamageMod - arg_457_0.baseAttackDamage)
			end
		},
		{
			stage = 1,
			slot = 3,
			damagetype = 2,
			damage = function(arg_458_0, arg_458_1, arg_458_2)
				return ({
					100,
					175,
					250
				})[arg_458_2] + arg_458_0.flatMagicDamageMod * arg_458_0.percentMagicDamageMod
			end
		}
	},
	vayne = {
		{
			stage = 1,
			slot = 0,
			damagetype = 1,
			damage = function(arg_459_0, arg_459_1, arg_459_2)
				return ({
					0.5,
					0.55,
					0.6,
					0.65,
					0.7
				})[arg_459_2] * ((arg_459_0.baseAttackDamage + arg_459_0.flatPhysicalDamageMod) * arg_459_0.percentPhysicalDamageMod)
			end
		},
		{
			stage = 1,
			slot = 1,
			damagetype = 3,
			damage = function(arg_460_0, arg_460_1, arg_460_2)
				return ({
					50,
					65,
					80,
					95,
					110
				})[arg_460_2] + ({
					0.04,
					0.06,
					0.08,
					0.1,
					0.12
				})[arg_460_2] * arg_460_1.maxHealth
			end
		},
		{
			stage = 1,
			slot = 2,
			damagetype = 1,
			damage = function(arg_461_0, arg_461_1, arg_461_2)
				return ({
					50,
					90,
					120,
					155,
					190
				})[arg_461_2] + 0.5 * ((arg_461_0.baseAttackDamage + arg_461_0.flatPhysicalDamageMod) * arg_461_0.percentPhysicalDamageMod - arg_461_0.baseAttackDamage)
			end
		},
		{
			stage = 2,
			slot = 2,
			damagetype = 1,
			damage = function(arg_462_0, arg_462_1, arg_462_2)
				return ({
					100,
					170,
					240,
					310,
					380
				})[arg_462_2] + ((arg_462_0.baseAttackDamage + arg_462_0.flatPhysicalDamageMod) * arg_462_0.percentPhysicalDamageMod - arg_462_0.baseAttackDamage)
			end
		}
	},
	veigar = {
		{
			stage = 1,
			slot = 0,
			damagetype = 2,
			damage = function(arg_463_0, arg_463_1, arg_463_2)
				return ({
					80,
					120,
					160,
					200,
					240
				})[arg_463_2] + 0.6 * (arg_463_0.flatMagicDamageMod * arg_463_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 1,
			damagetype = 2,
			damage = function(arg_464_0, arg_464_1, arg_464_2)
				return ({
					100,
					150,
					200,
					250,
					300
				})[arg_464_2] + arg_464_0.flatMagicDamageMod * arg_464_0.percentMagicDamageMod
			end
		},
		{
			stage = 1,
			slot = 3,
			damagetype = 2,
			damage = function(arg_465_0, arg_465_1, arg_465_2)
				return 100 - arg_465_1.health / arg_465_1.maxHealth * 100 > 66.67 and ({
					350,
					500,
					650
				})[arg_465_2] + 1.5 * (arg_465_0.flatMagicDamageMod * arg_465_0.percentMagicDamageMod) or (({
					175,
					250,
					325
				})[arg_465_2] + 0.75 * (arg_465_0.flatMagicDamageMod * arg_465_0.percentMagicDamageMod)) * (0.015 * (100 - arg_465_1.health / arg_465_1.maxHealth * 100) + 1)
			end
		}
	},
	velkoz = {
		{
			stage = 1,
			slot = 0,
			damagetype = 2,
			damage = function(arg_466_0, arg_466_1, arg_466_2)
				return ({
					80,
					120,
					160,
					200,
					240
				})[arg_466_2] + 0.6 * (arg_466_0.flatMagicDamageMod * arg_466_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 1,
			damagetype = 2,
			damage = function(arg_467_0, arg_467_1, arg_467_2)
				return ({
					75,
					125,
					175,
					225,
					275
				})[arg_467_2] + 0.4 * (arg_467_0.flatMagicDamageMod * arg_467_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 2,
			damagetype = 2,
			damage = function(arg_468_0, arg_468_1, arg_468_2)
				return ({
					70,
					100,
					130,
					160,
					190
				})[arg_468_2] + 0.3 * (arg_468_0.flatMagicDamageMod * arg_468_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 3,
			damagetype = 3,
			damage = function(arg_469_0, arg_469_1, arg_469_2)
				return ove_0_13(arg_469_1, "velkozresearchedstack") > 0 and ({
					450,
					625,
					800
				})[arg_469_2] + 1.25 * (arg_469_0.flatMagicDamageMod * arg_469_0.percentMagicDamageMod) or ove_0_11.CalculateMagicDamage(arg_469_1, ({
					450,
					625,
					800
				})[arg_469_2] + 1.25 * (arg_469_0.flatMagicDamageMod * arg_469_0.percentMagicDamageMod), arg_469_0)
			end
		}
	},
	vi = {
		{
			stage = 1,
			slot = 0,
			damagetype = 1,
			damage = function(arg_470_0, arg_470_1, arg_470_2)
				return ({
					55,
					80,
					105,
					130,
					155
				})[arg_470_2] + 0.8 * ((arg_470_0.baseAttackDamage + arg_470_0.flatPhysicalDamageMod) * arg_470_0.percentPhysicalDamageMod - arg_470_0.baseAttackDamage)
			end
		},
		{
			stage = 1,
			slot = 1,
			damagetype = 1,
			damage = function(arg_471_0, arg_471_1, arg_471_2)
				return ({
					0.04,
					0.055,
					0.07,
					0.085,
					0.01
				} + 0.01 * (((arg_471_0.baseAttackDamage + arg_471_0.flatPhysicalDamageMod) * arg_471_0.percentPhysicalDamageMod - arg_471_0.baseAttackDamage) / 35)) * arg_471_1.maxHealth
			end
		},
		{
			stage = 1,
			slot = 2,
			damagetype = 1,
			damage = function(arg_472_0, arg_472_1, arg_472_2)
				return ({
					10,
					30,
					50,
					70,
					90
				})[arg_472_2] + 1.15 * ((arg_472_0.baseAttackDamage + arg_472_0.flatPhysicalDamageMod) * arg_472_0.percentPhysicalDamageMod) + 0.7 * (arg_472_0.flatMagicDamageMod * arg_472_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 3,
			damagetype = 1,
			damage = function(arg_473_0, arg_473_1, arg_473_2)
				return ({
					150,
					300,
					450
				})[arg_473_2] + 1.4 * ((arg_473_0.baseAttackDamage + arg_473_0.flatPhysicalDamageMod) * arg_473_0.percentPhysicalDamageMod - arg_473_0.baseAttackDamage)
			end
		}
	},
	viktor = {
		{
			stage = 1,
			slot = 0,
			damagetype = 2,
			damage = function(arg_474_0, arg_474_1, arg_474_2)
				return ({
					60,
					75,
					90,
					105,
					120
				})[arg_474_2] + 0.4 * arg_474_0.flatMagicDamageMod
			end
		},
		{
			stage = 2,
			slot = 0,
			damagetype = 2,
			damage = function(arg_475_0, arg_475_1, arg_475_2)
				return ({
					20,
					40,
					60,
					80,
					100
				})[arg_475_2] + (arg_475_0.baseAttackDamage + arg_475_0.flatPhysicalDamageMod) * arg_475_0.percentPhysicalDamageMod + 0.5 * (arg_475_0.flatMagicDamageMod * arg_475_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 2,
			damagetype = 2,
			damage = function(arg_476_0, arg_476_1, arg_476_2)
				return ({
					70,
					110,
					150,
					190,
					230
				})[arg_476_2] + 0.5 * arg_476_0.flatMagicDamageMod
			end
		},
		{
			stage = 2,
			slot = 2,
			damagetype = 2,
			damage = function(arg_477_0, arg_477_1, arg_477_2)
				return ({
					90,
					170,
					250,
					330,
					410
				})[arg_477_2] + 1.2 * arg_477_0.flatMagicDamageMod
			end
		},
		{
			stage = 1,
			slot = 3,
			damagetype = 2,
			damage = function(arg_478_0, arg_478_1, arg_478_2)
				return ({
					100,
					175,
					250
				})[arg_478_2] + 0.5 * (arg_478_0.flatMagicDamageMod * arg_478_0.percentMagicDamageMod)
			end
		},
		{
			stage = 2,
			slot = 3,
			damagetype = 2,
			damage = function(arg_479_0, arg_479_1, arg_479_2)
				return ({
					150,
					250,
					350
				})[arg_479_2] + 0.6 * (arg_479_0.flatMagicDamageMod * arg_479_0.percentMagicDamageMod)
			end
		}
	},
	vladimir = {
		{
			stage = 1,
			slot = 0,
			damagetype = 2,
			damage = function(arg_480_0, arg_480_1, arg_480_2)
				return ({
					80,
					100,
					120,
					140,
					160
				})[arg_480_2] + 0.6 * (arg_480_0.flatMagicDamageMod * arg_480_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 1,
			damagetype = 2,
			damage = function(arg_481_0, arg_481_1, arg_481_2)
				return ({
					80,
					135,
					190,
					245,
					300
				})[arg_481_2] + 0.05 * arg_481_0.maxHealth
			end
		},
		{
			stage = 1,
			slot = 2,
			damagetype = 2,
			damage = function(arg_482_0, arg_482_1, arg_482_2)
				return ({
					30,
					45,
					60,
					75,
					90
				})[arg_482_2] + 0.35 * (arg_482_0.flatMagicDamageMod * arg_482_0.percentMagicDamageMod) + 0.025 * arg_482_0.maxHealth
			end
		},
		{
			stage = 2,
			slot = 2,
			damagetype = 2,
			damage = function(arg_483_0, arg_483_1, arg_483_2)
				return ({
					60,
					90,
					120,
					150,
					180
				})[arg_483_2] + arg_483_0.flatMagicDamageMod * arg_483_0.percentMagicDamageMod + 0.06 * arg_483_0.maxHealth
			end
		},
		{
			stage = 1,
			slot = 3,
			damagetype = 2,
			damage = function(arg_484_0, arg_484_1, arg_484_2)
				return ({
					150,
					250,
					350
				})[arg_484_2] + 0.7 * (arg_484_0.flatMagicDamageMod * arg_484_0.percentMagicDamageMod)
			end
		}
	},
	volibear = {
		{
			stage = 1,
			slot = 0,
			damagetype = 1,
			damage = function(arg_485_0, arg_485_1, arg_485_2)
				return ({
					30,
					60,
					90,
					120,
					150
				})[arg_485_2]
			end
		},
		{
			stage = 1,
			slot = 1,
			damagetype = 1,
			damage = function(arg_486_0, arg_486_1, arg_486_2)
				return ({
					60,
					110,
					160,
					210,
					260
				})[arg_486_2] + 0.1 * arg_486_0.maxHealth
			end
		},
		{
			stage = 1,
			slot = 2,
			damagetype = 2,
			damage = function(arg_487_0, arg_487_1, arg_487_2)
				return ({
					60,
					105,
					150,
					195,
					240
				})[arg_487_2] + 0.6 * (arg_487_0.flatMagicDamageMod * arg_487_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 3,
			damagetype = 2,
			damage = function(arg_488_0, arg_488_1, arg_488_2)
				return ({
					75,
					115,
					155
				})[arg_488_2] + 0.3 * (arg_488_0.flatMagicDamageMod * arg_488_0.percentMagicDamageMod)
			end
		}
	},
	warwick = {
		{
			stage = 1,
			slot = 0,
			damagetype = 2,
			damage = function(arg_489_0, arg_489_1, arg_489_2)
				return 1.2 * ((arg_489_0.baseAttackDamage + arg_489_0.flatPhysicalDamageMod) * arg_489_0.percentPhysicalDamageMod) + 0.9 * (arg_489_0.flatMagicDamageMod * arg_489_0.percentMagicDamageMod) + ({
					0.06,
					0.07,
					0.08,
					0.09,
					0.1
				})[arg_489_2] * arg_489_1.maxHealth
			end
		},
		{
			stage = 1,
			slot = 3,
			damagetype = 2,
			damage = function(arg_490_0, arg_490_1, arg_490_2)
				return ({
					175,
					350,
					525
				})[arg_490_2] + 1.67 * ((arg_490_0.baseAttackDamage + arg_490_0.flatPhysicalDamageMod) * arg_490_0.percentPhysicalDamageMod - arg_490_0.baseAttackDamage)
			end
		}
	},
	xayah = {
		{
			stage = 1,
			slot = 0,
			damagetype = 1,
			damage = function(arg_491_0, arg_491_1, arg_491_2)
				return ({
					45,
					65,
					85,
					105,
					125
				})[arg_491_2] + 0.5 * ((arg_491_0.baseAttackDamage + arg_491_0.flatPhysicalDamageMod) * arg_491_0.percentPhysicalDamageMod - arg_491_0.baseAttackDamage)
			end
		},
		{
			stage = 1,
			slot = 2,
			damagetype = 1,
			damage = function(arg_492_0, arg_492_1, arg_492_2)
				return ({
					55,
					65,
					75,
					85,
					95
				})[arg_492_2] + 0.6 * ((arg_492_0.baseAttackDamage + arg_492_0.flatPhysicalDamageMod) * arg_492_0.percentPhysicalDamageMod - arg_492_0.baseAttackDamage)
			end
		},
		{
			stage = 1,
			slot = 3,
			damagetype = 1,
			damage = function(arg_493_0, arg_493_1, arg_493_2)
				return ({
					100,
					150,
					200
				})[arg_493_2] + ((arg_493_0.baseAttackDamage + arg_493_0.flatPhysicalDamageMod) * arg_493_0.percentPhysicalDamageMod - arg_493_0.baseAttackDamage)
			end
		}
	},
	xerath = {
		{
			stage = 1,
			slot = 0,
			damagetype = 2,
			damage = function(arg_494_0, arg_494_1, arg_494_2)
				return ({
					80,
					120,
					160,
					200,
					240
				})[arg_494_2] + 0.75 * (arg_494_0.flatMagicDamageMod * arg_494_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 1,
			damagetype = 2,
			damage = function(arg_495_0, arg_495_1, arg_495_2)
				return ({
					60,
					90,
					120,
					150,
					180
				})[arg_495_2] + 0.6 * (arg_495_0.flatMagicDamageMod * arg_495_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 2,
			damagetype = 2,
			damage = function(arg_496_0, arg_496_1, arg_496_2)
				return ({
					80,
					110,
					140,
					170,
					200
				})[arg_496_2] + 0.45 * (arg_496_0.flatMagicDamageMod * arg_496_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 3,
			damagetype = 2,
			damage = function(arg_497_0, arg_497_1, arg_497_2)
				return ({
					200,
					240,
					280
				})[arg_497_2] + 0.43 * (arg_497_0.flatMagicDamageMod * arg_497_0.percentMagicDamageMod)
			end
		},
		{
			stage = 2,
			slot = 3,
			damagetype = 2,
			damage = function(arg_498_0, arg_498_1, arg_498_2)
				return ({
					600,
					960,
					1400
				})[arg_498_2] + ({
					1.29,
					1.72,
					2.15
				})[arg_498_2] * (arg_498_0.flatMagicDamageMod * arg_498_0.percentMagicDamageMod)
			end
		}
	},
	xinzhao = {
		{
			stage = 1,
			slot = 0,
			damagetype = 1,
			damage = function(arg_499_0, arg_499_1, arg_499_2)
				return ({
					60,
					75,
					90,
					105,
					120
				})[arg_499_2] + 1.2 * ((arg_499_0.baseAttackDamage + arg_499_0.flatPhysicalDamageMod) * arg_499_0.percentPhysicalDamageMod - arg_499_0.baseAttackDamage)
			end
		},
		{
			stage = 1,
			slot = 1,
			damagetype = 1,
			damage = function(arg_500_0, arg_500_1, arg_500_2)
				return ({
					60,
					105,
					150,
					195,
					240
				})[arg_500_2] + 1.05 * ((arg_500_0.baseAttackDamage + arg_500_0.flatPhysicalDamageMod) * arg_500_0.percentPhysicalDamageMod - arg_500_0.baseAttackDamage)
			end
		},
		{
			stage = 1,
			slot = 2,
			damagetype = 2,
			damage = function(arg_501_0, arg_501_1, arg_501_2)
				return ({
					50,
					75,
					100,
					125,
					150
				})[arg_501_2] + 0.6 * (arg_501_0.flatMagicDamageMod * arg_501_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 3,
			damagetype = 1,
			damage = function(arg_502_0, arg_502_1, arg_502_2)
				return ({
					75,
					175,
					275
				})[arg_502_2] + ((arg_502_0.baseAttackDamage + arg_502_0.flatPhysicalDamageMod) * arg_502_0.percentPhysicalDamageMod - arg_502_0.baseAttackDamage) + 0.15 * arg_502_1.health
			end
		}
	},
	yasuo = {
		{
			stage = 1,
			slot = 0,
			damagetype = 1,
			damage = function(arg_503_0, arg_503_1, arg_503_2)
				return ({
					20,
					45,
					70,
					95,
					120
				})[arg_503_2] + (arg_503_0.baseAttackDamage + arg_503_0.flatPhysicalDamageMod) * arg_503_0.percentPhysicalDamageMod
			end
		},
		{
			stage = 1,
			slot = 2,
			damagetype = 2,
			damage = function(arg_504_0, arg_504_1, arg_504_2)
				return ({
					60,
					70,
					80,
					90,
					100
				})[arg_504_2] + 0.2 * ((arg_504_0.baseAttackDamage + arg_504_0.flatPhysicalDamageMod) * arg_504_0.percentPhysicalDamageMod - arg_504_0.baseAttackDamage) + 0.6 * (arg_504_0.flatMagicDamageMod * arg_504_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 3,
			damagetype = 1,
			damage = function(arg_505_0, arg_505_1, arg_505_2)
				return ({
					200,
					300,
					400
				})[arg_505_2] + 1.5 * ((arg_505_0.baseAttackDamage + arg_505_0.flatPhysicalDamageMod) * arg_505_0.percentPhysicalDamageMod - arg_505_0.baseAttackDamage)
			end
		}
	},
	yorick = {
		{
			stage = 1,
			slot = 0,
			damagetype = 1,
			damage = function(arg_506_0, arg_506_1, arg_506_2)
				return ({
					30,
					55,
					80,
					105,
					130
				})[arg_506_2] + 0.4 * ((arg_506_0.baseAttackDamage + arg_506_0.flatPhysicalDamageMod) * arg_506_0.percentPhysicalDamageMod)
			end
		},
		{
			stage = 2,
			slot = 0,
			damagetype = 1,
			damage = function(arg_507_0, arg_507_1, arg_507_2)
				return ({
					2,
					5,
					8,
					11,
					14,
					17,
					20,
					25,
					30,
					35,
					40,
					45,
					50,
					60,
					70,
					80,
					90,
					100
				})[arg_507_0.levelRef] + 0.3 * ((arg_507_0.baseAttackDamage + arg_507_0.flatPhysicalDamageMod) * arg_507_0.percentPhysicalDamageMod)
			end
		},
		{
			stage = 1,
			slot = 2,
			damagetype = 2,
			damage = function(arg_508_0, arg_508_1, arg_508_2)
				return ({
					70,
					105,
					140,
					175,
					210
				})[arg_508_2] + 0.7 * (arg_508_0.flatMagicDamageMod * arg_508_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 3,
			damagetype = 2,
			damage = function(arg_509_0, arg_509_1, arg_509_2)
				return ({
					10,
					20,
					40
				})[arg_509_2] + 0.5 * ((arg_509_0.baseAttackDamage + arg_509_0.flatPhysicalDamageMod) * arg_509_0.percentPhysicalDamageMod)
			end
		}
	},
	zac = {
		{
			stage = 1,
			slot = 0,
			damagetype = 2,
			damage = function(arg_510_0, arg_510_1, arg_510_2)
				return ({
					30,
					40,
					50,
					60,
					70
				})[arg_510_2] + 0.3 * (arg_510_0.flatMagicDamageMod * arg_510_0.percentMagicDamageMod) + 0.025 * arg_510_0.maxHealth
			end
		},
		{
			stage = 1,
			slot = 1,
			damagetype = 2,
			damage = function(arg_511_0, arg_511_1, arg_511_2)
				return ({
					15,
					30,
					45,
					60,
					75
				})[arg_511_2] + (({
					0.04,
					0.05,
					0.06,
					0.07,
					0.08
				})[arg_511_2] + 0.02 * (arg_511_0.flatMagicDamageMod * arg_511_0.percentMagicDamageMod / 100)) * arg_511_1.maxHealth
			end
		},
		{
			stage = 1,
			slot = 2,
			damagetype = 2,
			damage = function(arg_512_0, arg_512_1, arg_512_2)
				return ({
					60,
					110,
					160,
					210,
					260
				})[arg_512_2] + 0.7 * (arg_512_0.flatMagicDamageMod * arg_512_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 3,
			damagetype = 2,
			damage = function(arg_513_0, arg_513_1, arg_513_2)
				return ({
					150,
					250,
					350
				})[arg_513_2] + 0.7 * (arg_513_0.flatMagicDamageMod * arg_513_0.percentMagicDamageMod)
			end
		}
	},
	zed = {
		{
			stage = 1,
			slot = 0,
			damagetype = 1,
			damage = function(arg_514_0, arg_514_1, arg_514_2)
				return ({
					80,
					115,
					150,
					185,
					220
				})[arg_514_2] + 0.9 * ((arg_514_0.baseAttackDamage + arg_514_0.flatPhysicalDamageMod) * arg_514_0.percentPhysicalDamageMod - arg_514_0.baseAttackDamage)
			end
		},
		{
			stage = 2,
			slot = 0,
			damagetype = 1,
			damage = function(arg_515_0, arg_515_1, arg_515_2)
				return ({
					60,
					86.25,
					112.5,
					138.75,
					165
				})[arg_515_2] + 0.675 * ((arg_515_0.baseAttackDamage + arg_515_0.flatPhysicalDamageMod) * arg_515_0.percentPhysicalDamageMod - arg_515_0.baseAttackDamage)
			end
		},
		{
			stage = 1,
			slot = 2,
			damagetype = 1,
			damage = function(arg_516_0, arg_516_1, arg_516_2)
				return ({
					70,
					95,
					120,
					145,
					170
				})[arg_516_2] + 0.8 * ((arg_516_0.baseAttackDamage + arg_516_0.flatPhysicalDamageMod) * arg_516_0.percentPhysicalDamageMod - arg_516_0.baseAttackDamage)
			end
		},
		{
			stage = 1,
			slot = 3,
			damagetype = 1,
			damage = function(arg_517_0, arg_517_1, arg_517_2)
				return (arg_517_0.baseAttackDamage + arg_517_0.flatPhysicalDamageMod) * arg_517_0.percentPhysicalDamageMod
			end
		}
	},
	ziggs = {
		{
			stage = 1,
			slot = 0,
			damagetype = 2,
			damage = function(arg_518_0, arg_518_1, arg_518_2)
				return ({
					75,
					120,
					165,
					210,
					255
				})[arg_518_2] + 0.65 * (arg_518_0.flatMagicDamageMod * arg_518_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 1,
			damagetype = 2,
			damage = function(arg_519_0, arg_519_1, arg_519_2)
				return ({
					70,
					105,
					140,
					175,
					210
				})[arg_519_2] + 0.35 * (arg_519_0.flatMagicDamageMod * arg_519_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 2,
			damagetype = 2,
			damage = function(arg_520_0, arg_520_1, arg_520_2)
				return ({
					40,
					65,
					90,
					115,
					140
				})[arg_520_2] + 0.3 * (arg_520_0.flatMagicDamageMod * arg_520_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 3,
			damagetype = 2,
			damage = function(arg_521_0, arg_521_1, arg_521_2)
				return ({
					200,
					300,
					400
				})[arg_521_2] + 0.733 * (arg_521_0.flatMagicDamageMod * arg_521_0.percentMagicDamageMod)
			end
		},
		{
			stage = 2,
			slot = 3,
			damagetype = 2,
			damage = function(arg_522_0, arg_522_1, arg_522_2)
				return ({
					300,
					450,
					600
				})[arg_522_2] + 1.1 * (arg_522_0.flatMagicDamageMod * arg_522_0.percentMagicDamageMod)
			end
		}
	},
	zilean = {
		{
			stage = 1,
			slot = 0,
			damagetype = 2,
			damage = function(arg_523_0, arg_523_1, arg_523_2)
				return ({
					75,
					115,
					165,
					230,
					300
				})[arg_523_2] + 0.9 * (arg_523_0.flatMagicDamageMod * arg_523_0.percentMagicDamageMod)
			end
		}
	},
	zoe = {
		{
			stage = 1,
			slot = 0,
			damagetype = 2,
			damage = function(arg_524_0, arg_524_1, arg_524_2)
				return ({
					58,
					60,
					63,
					67,
					72,
					76,
					82,
					88,
					95,
					102,
					110,
					118,
					127,
					136,
					147,
					157,
					168,
					180
				})[arg_524_0.levelRef] + (({
					0,
					30,
					60,
					90,
					120
				})[arg_524_2] + 0.66 * (arg_524_0.flatMagicDamageMod * arg_524_0.percentMagicDamageMod))
			end
		},
		{
			stage = 1,
			slot = 1,
			damagetype = 2,
			damage = function(arg_525_0, arg_525_1, arg_525_2)
				return ({
					70,
					115,
					160,
					205,
					250
				})[arg_525_2] + 0.4 * (arg_525_0.flatMagicDamageMod * arg_525_0.percentMagicDamageMod)
			end
		},
		{
			stage = 2,
			slot = 1,
			damagetype = 2,
			damage = function(arg_526_0, arg_526_1, arg_526_2)
				return ({
					23.3,
					38.3,
					53.3,
					68.3,
					83.3
				})[arg_526_2] + 0.133 * (arg_526_0.flatMagicDamageMod * arg_526_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 2,
			damagetype = 2,
			damage = function(arg_527_0, arg_527_1, arg_527_2)
				return ({
					60,
					100,
					140,
					180,
					220
				})[arg_527_2] + 0.4 * (arg_527_0.flatMagicDamageMod * arg_527_0.percentMagicDamageMod)
			end
		},
		{
			stage = 2,
			slot = 2,
			damagetype = 3,
			damage = function(arg_528_0, arg_528_1, arg_528_2)
				return ({
					60,
					100,
					140,
					180,
					220
				})[arg_528_2] + 0.4 * (arg_528_0.flatMagicDamageMod * arg_528_0.percentMagicDamageMod)
			end
		},
		{
			stage = 3,
			slot = 2,
			damagetype = 3,
			damage = function(arg_529_0, arg_529_1, arg_529_2)
				return ({
					60,
					100,
					140,
					180,
					220
				})[arg_529_2] + 0.4 * (arg_529_0.flatMagicDamageMod * arg_529_0.percentMagicDamageMod) + ove_0_11.CalculateMagicDamage(arg_529_1, ({
					60,
					100,
					140,
					180,
					220
				})[arg_529_2] + 0.4 * (arg_529_0.flatMagicDamageMod * arg_529_0.percentMagicDamageMod), arg_529_0)
			end
		}
	},
	zyra = {
		{
			stage = 1,
			slot = 0,
			damagetype = 2,
			damage = function(arg_530_0, arg_530_1, arg_530_2)
				return ({
					60,
					95,
					130,
					165,
					200
				})[arg_530_2] + 0.6 * (arg_530_0.flatMagicDamageMod * arg_530_0.percentMagicDamageMod)
			end
		},
		{
			stage = 2,
			slot = 0,
			damagetype = 2,
			damage = function(arg_531_0, arg_531_1, arg_531_2)
				return 24 + 5 * arg_531_0.levelRef + 0.15 * (arg_531_0.flatMagicDamageMod * arg_531_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 2,
			damagetype = 2,
			damage = function(arg_532_0, arg_532_1, arg_532_2)
				return ({
					60,
					95,
					130,
					165,
					200
				})[arg_532_2] + 0.5 * (arg_532_0.flatMagicDamageMod * arg_532_0.percentMagicDamageMod)
			end
		},
		{
			stage = 2,
			slot = 2,
			damagetype = 2,
			damage = function(arg_533_0, arg_533_1, arg_533_2)
				return 24 + 5 * arg_533_0.levelRef + 0.15 * (arg_533_0.flatMagicDamageMod * arg_533_0.percentMagicDamageMod)
			end
		},
		{
			stage = 1,
			slot = 3,
			damagetype = 2,
			damage = function(arg_534_0, arg_534_1, arg_534_2)
				return ({
					180,
					265,
					350
				})[arg_534_2] + 0.7 * (arg_534_0.flatMagicDamageMod * arg_534_0.percentMagicDamageMod)
			end
		}
	}
}

function ove_0_12.GetSpellDamage(arg_535_0, arg_535_1, arg_535_2, arg_535_3)
	assert(type(arg_535_0) == "number", "GetSpellDamage: Wrong argument types (<number> expected for <sSlot> arg)")

	local slot_535_0 = arg_535_3 or objManager.player
	local slot_535_1 = string.lower(slot_535_0.charName)
	local slot_535_2 = arg_535_2 or 1
	local slot_535_3 = {}

	if slot_535_2 > 4 then
		slot_535_2 = 4
	end

	if (arg_535_0 == 0 or arg_535_0 == 1 or arg_535_0 == 2 or arg_535_0 == 3) and arg_535_1 then
		local slot_535_4 = slot_535_0:spellSlot(arg_535_0).level

		if slot_535_4 <= 0 then
			return 0
		end

		if ove_0_14[slot_535_1] then
			for iter_535_0 = 1, #ove_0_14[slot_535_1] do
				local slot_535_5 = ove_0_14[slot_535_1][iter_535_0]

				if slot_535_5.slot == arg_535_0 then
					table.insert(slot_535_3, slot_535_5)
				end
			end

			if slot_535_2 > #slot_535_3 then
				slot_535_2 = #slot_535_3
			end

			for iter_535_1 = #slot_535_3, 1, -1 do
				local slot_535_6 = slot_535_3[iter_535_1]

				if slot_535_6.stage == slot_535_2 then
					if slot_535_6.damagetype == 1 then
						return ove_0_11.CalculatePhysicalDamage(arg_535_1, slot_535_6.damage(slot_535_0, arg_535_1, slot_535_4), slot_535_0)
					elseif slot_535_6.damagetype == 2 then
						return ove_0_11.CalculateMagicDamage(arg_535_1, slot_535_6.damage(slot_535_0, arg_535_1, slot_535_4), slot_535_0)
					elseif slot_535_6.damagetype == 3 then
						return slot_535_6.damage(slot_535_0, arg_535_1, slot_535_4)
					end
				end
			end
		end
	end

	return 0
end

return ove_0_12

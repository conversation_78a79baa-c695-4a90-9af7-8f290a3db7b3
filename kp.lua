
local ove_0_10 = module.load("<PERSON>", "<PERSON><PERSON>/<PERSON><PERSON><PERSON><PERSON>")
local ove_0_11 = module.load("<PERSON>", "Lib/Version")
--local ove_0_12 = module.load("<PERSON>", "Lib/myMsg")
--local ove_0_13 = module.load("<PERSON>", "<PERSON>b/Changelog")
local Curses = module.load("<PERSON>", "<PERSON>s");
--local KiteHelper = module.load("<PERSON>", "Libdata/KiteHelper");
--local ove_0_14 = module.load("<PERSON>", "<PERSON>b/MyAuth")
local ove_0_15 = {
	"Cassiope<PERSON>",
	"<PERSON>",
	"<PERSON><PERSON><PERSON>",
	"<PERSON><PERSON>",
	"<PERSON>",
	"<PERSON><PERSON><PERSON>",
	"<PERSON><PERSON><PERSON>",
	"<PERSON><PERSON><PERSON>",
	"<PERSON><PERSON>",
	"<PERSON><PERSON>",
	"<PERSON><PERSON>",
	"<PERSON>z<PERSON>",
	"Tristan<PERSON>",
	"<PERSON>",
	"Twitch",
	"<PERSON>aya<PERSON>",
	"Kali<PERSON>",
	"<PERSON><PERSON><PERSON>",
	"<PERSON><PERSON>",
	"KogMaw"
}
local ove_0_16 = false



for iter_0_0, iter_0_1 in pairs(ove_0_15) do
	if player.charName == iter_0_1 then
		ove_0_16 = true
	end
end
print(ove_0_16)
local function ove_0_17()
	-- print 5
	if ove_0_16 then
		if true then
			module.load("<PERSON>", "Champions/" .. player.charName .. "/" .. player.charName)
			--ove_0_13.Get(true)
			--ove_0_13.Get2(true)

			if ove_0_10.GetLanguage() == 1 then
			--	ove_0_14.Print("success", true)
				print("**********************************************************************************")
			elseif ove_0_10.GetLanguage() == 2 then
				--ove_0_14.Print("success", true)
				print("**********************************************************************************")
			end

			cb.remove(ove_0_17)
		elseif ove_0_14.isAuthed() == false then
			if ove_0_10.GetLanguage() == 1 then
				ove_0_13.Get(false)
				ove_0_13.Get2(false)
				ove_0_14.Print("fail", true)
				print("**********************************************************************************")
			elseif ove_0_10.GetLanguage() == 2 then
				ove_0_14.Print("fail", true)
				print("**********************************************************************************")
			end

			cb.remove(ove_0_17)
		end
	end
end

if not ove_0_16 then
	--print("This champion is not supported in this bundle yet.")
end

cb.add(cb.tick, ove_0_17)

return {}

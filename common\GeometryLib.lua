

local ove_0_10 = math.lengthOf
local ove_0_11 = math.pow
local ove_0_12 = math.sqrt
local ove_0_13 = math.cos
local ove_0_14 = math.sin
local ove_0_15 = math.acos
local ove_0_16 = math.deg
local ove_0_17 = math.atan
local ove_0_18 = math.abs
local ove_0_19 = math.min
local ove_0_20 = math.max
local ove_0_21 = math.huge
local ove_0_22 = table.insert
local ove_0_23 = table.remove
local ove_0_24 = table.sort
local ove_0_25 = {
	0.99984769515639,
	0.9993908270191,
	0.99862953475457,
	0.99756405025982,
	0.99619469809175,
	0.99452189536827,
	0.99254615164132,
	0.99026806874157,
	0.98768834059514,
	0.98480775301221,
	0.98162718344766,
	0.97814760073381,
	0.97437006478524,
	0.970295726276,
	0.96592582628907,
	0.96126169593832,
	0.95630475596304,
	0.95105651629515,
	0.94551857559932,
	0.93969262078591,
	0.9335804264972,
	0.92718385456679,
	0.92050485345244,
	0.9135454576426,
	0.90630778703665,
	0.89879404629917,
	0.89100652418837,
	0.88294759285893,
	0.8746197071394,
	0.86602540378444,
	0.85716730070211,
	0.84804809615643,
	0.83867056794542,
	0.82903757255504,
	0.81915204428899,
	0.80901699437495,
	0.79863551004729,
	0.78801075360672,
	0.77714596145697,
	0.76604444311898,
	0.75470958022277,
	0.74314482547739,
	0.73135370161917,
	0.71933980033865,
	0.70710678118655,
	0.694658370459,
	0.6819983600625,
	0.66913060635886,
	0.65605902899051,
	0.64278760968654,
	0.62932039104984,
	0.61566147532566,
	0.60181502315205,
	0.58778525229247,
	0.57357643635105,
	0.55919290347075,
	0.54463903501503,
	0.5299192642332,
	0.51503807491005,
	0.5,
	0.48480962024634,
	0.46947156278589,
	0.45399049973955,
	0.43837114678908,
	0.4226182617407,
	0.4067366430758,
	0.39073112848927,
	0.37460659341591,
	0.3583679495453,
	0.34202014332567,
	0.32556815445716,
	0.30901699437495,
	0.29237170472274,
	0.275637355817,
	0.25881904510252,
	0.24192189559967,
	0.22495105434386,
	0.20791169081776,
	0.19080899537654,
	0.17364817766693,
	0.15643446504023,
	0.13917310096007,
	0.12186934340515,
	0.10452846326765,
	0.087155742747658,
	0.069756473744125,
	0.052335956242944,
	0.034899496702501,
	0.017452406437284,
	6.1230317691119e-17,
	-0.017452406437283,
	-0.034899496702501,
	-0.052335956242944,
	-0.069756473744125,
	-0.087155742747658,
	-0.10452846326765,
	-0.12186934340515,
	-0.13917310096007,
	-0.15643446504023,
	-0.17364817766693,
	-0.19080899537654,
	-0.20791169081776,
	-0.22495105434387,
	-0.24192189559967,
	-0.25881904510252,
	-0.275637355817,
	-0.29237170472274,
	-0.30901699437495,
	-0.32556815445716,
	-0.34202014332567,
	-0.3583679495453,
	-0.37460659341591,
	-0.39073112848927,
	-0.4067366430758,
	-0.4226182617407,
	-0.43837114678908,
	-0.45399049973955,
	-0.46947156278589,
	-0.48480962024634,
	-0.5,
	-0.51503807491005,
	-0.5299192642332,
	-0.54463903501503,
	-0.55919290347075,
	-0.57357643635105,
	-0.58778525229247,
	-0.60181502315205,
	-0.61566147532566,
	-0.62932039104984,
	-0.64278760968654,
	-0.65605902899051,
	-0.66913060635886,
	-0.6819983600625,
	-0.694658370459,
	-0.70710678118655,
	-0.71933980033865,
	-0.73135370161917,
	-0.74314482547739,
	-0.75470958022277,
	-0.76604444311898,
	-0.77714596145697,
	-0.78801075360672,
	-0.79863551004729,
	-0.80901699437495,
	-0.81915204428899,
	-0.82903757255504,
	-0.83867056794542,
	-0.84804809615643,
	-0.85716730070211,
	-0.86602540378444,
	-0.8746197071394,
	-0.88294759285893,
	-0.89100652418837,
	-0.89879404629917,
	-0.90630778703665,
	-0.9135454576426,
	-0.92050485345244,
	-0.92718385456679,
	-0.9335804264972,
	-0.93969262078591,
	-0.94551857559932,
	-0.95105651629515,
	-0.95630475596304,
	-0.96126169593832,
	-0.96592582628907,
	-0.970295726276,
	-0.97437006478524,
	-0.97814760073381,
	-0.98162718344766,
	-0.98480775301221,
	-0.98768834059514,
	-0.99026806874157,
	-0.99254615164132,
	-0.99452189536827,
	-0.99619469809175,
	-0.99756405025982,
	-0.99862953475457,
	-0.9993908270191,
	-0.99984769515639,
	-1,
	-0.99984769515639,
	-0.9993908270191,
	-0.99862953475457,
	-0.99756405025982,
	-0.99619469809175,
	-0.99452189536827,
	-0.99254615164132,
	-0.99026806874157,
	-0.98768834059514,
	-0.98480775301221,
	-0.98162718344766,
	-0.97814760073381,
	-0.97437006478524,
	-0.970295726276,
	-0.96592582628907,
	-0.96126169593832,
	-0.95630475596304,
	-0.95105651629515,
	-0.94551857559932,
	-0.93969262078591,
	-0.9335804264972,
	-0.92718385456679,
	-0.92050485345244,
	-0.9135454576426,
	-0.90630778703665,
	-0.89879404629917,
	-0.89100652418837,
	-0.88294759285893,
	-0.8746197071394,
	-0.86602540378444,
	-0.85716730070211,
	-0.84804809615643,
	-0.83867056794542,
	-0.82903757255504,
	-0.81915204428899,
	-0.80901699437495,
	-0.79863551004729,
	-0.78801075360672,
	-0.77714596145697,
	-0.76604444311898,
	-0.75470958022277,
	-0.74314482547739,
	-0.73135370161917,
	-0.71933980033865,
	-0.70710678118655,
	-0.694658370459,
	-0.6819983600625,
	-0.66913060635886,
	-0.65605902899051,
	-0.64278760968654,
	-0.62932039104984,
	-0.61566147532566,
	-0.60181502315205,
	-0.58778525229247,
	-0.57357643635105,
	-0.55919290347075,
	-0.54463903501503,
	-0.52991926423321,
	-0.51503807491005,
	-0.5,
	-0.48480962024634,
	-0.46947156278589,
	-0.45399049973955,
	-0.43837114678908,
	-0.4226182617407,
	-0.4067366430758,
	-0.39073112848927,
	-0.37460659341591,
	-0.3583679495453,
	-0.34202014332567,
	-0.32556815445716,
	-0.30901699437495,
	-0.29237170472274,
	-0.275637355817,
	-0.25881904510252,
	-0.24192189559967,
	-0.22495105434387,
	-0.20791169081776,
	-0.19080899537654,
	-0.17364817766693,
	-0.15643446504023,
	-0.13917310096007,
	-0.12186934340515,
	-0.10452846326765,
	-0.087155742747658,
	-0.069756473744126,
	-0.052335956242944,
	-0.034899496702501,
	-0.017452406437283,
	-1.8369095307336e-16,
	0.017452406437283,
	0.034899496702501,
	0.052335956242944,
	0.069756473744125,
	0.087155742747658,
	0.10452846326765,
	0.12186934340515,
	0.13917310096007,
	0.15643446504023,
	0.17364817766693,
	0.19080899537655,
	0.20791169081776,
	0.22495105434386,
	0.24192189559967,
	0.25881904510252,
	0.275637355817,
	0.29237170472274,
	0.30901699437495,
	0.32556815445716,
	0.34202014332567,
	0.3583679495453,
	0.37460659341591,
	0.39073112848927,
	0.4067366430758,
	0.4226182617407,
	0.43837114678908,
	0.45399049973955,
	0.46947156278589,
	0.48480962024634,
	0.5,
	0.51503807491005,
	0.5299192642332,
	0.54463903501503,
	0.55919290347075,
	0.57357643635105,
	0.58778525229247,
	0.60181502315205,
	0.61566147532566,
	0.62932039104984,
	0.64278760968654,
	0.65605902899051,
	0.66913060635886,
	0.6819983600625,
	0.694658370459,
	0.70710678118655,
	0.71933980033865,
	0.73135370161917,
	0.74314482547739,
	0.75470958022277,
	0.76604444311898,
	0.77714596145697,
	0.78801075360672,
	0.79863551004729,
	0.80901699437495,
	0.81915204428899,
	0.82903757255504,
	0.83867056794542,
	0.84804809615643,
	0.85716730070211,
	0.86602540378444,
	0.8746197071394,
	0.88294759285893,
	0.89100652418837,
	0.89879404629917,
	0.90630778703665,
	0.9135454576426,
	0.92050485345244,
	0.92718385456679,
	0.9335804264972,
	0.93969262078591,
	0.94551857559932,
	0.95105651629515,
	0.95630475596304,
	0.96126169593832,
	0.96592582628907,
	0.970295726276,
	0.97437006478524,
	0.97814760073381,
	0.98162718344766,
	0.98480775301221,
	0.98768834059514,
	0.99026806874157,
	0.99254615164132,
	0.99452189536827,
	0.99619469809175,
	0.99756405025982,
	0.99862953475457,
	0.9993908270191,
	0.99984769515639,
	1
}
local ove_0_26 = {
	0.017452406437284,
	0.034899496702501,
	0.052335956242944,
	0.069756473744125,
	0.087155742747658,
	0.10452846326765,
	0.12186934340515,
	0.13917310096007,
	0.15643446504023,
	0.17364817766693,
	0.19080899537654,
	0.20791169081776,
	0.22495105434387,
	0.24192189559967,
	0.25881904510252,
	0.275637355817,
	0.29237170472274,
	0.30901699437495,
	0.32556815445716,
	0.34202014332567,
	0.3583679495453,
	0.37460659341591,
	0.39073112848927,
	0.4067366430758,
	0.4226182617407,
	0.43837114678908,
	0.45399049973955,
	0.46947156278589,
	0.48480962024634,
	0.5,
	0.51503807491005,
	0.5299192642332,
	0.54463903501503,
	0.55919290347075,
	0.57357643635105,
	0.58778525229247,
	0.60181502315205,
	0.61566147532566,
	0.62932039104984,
	0.64278760968654,
	0.65605902899051,
	0.66913060635886,
	0.6819983600625,
	0.694658370459,
	0.70710678118655,
	0.71933980033865,
	0.73135370161917,
	0.74314482547739,
	0.75470958022277,
	0.76604444311898,
	0.77714596145697,
	0.78801075360672,
	0.79863551004729,
	0.80901699437495,
	0.81915204428899,
	0.82903757255504,
	0.83867056794542,
	0.84804809615643,
	0.85716730070211,
	0.86602540378444,
	0.8746197071394,
	0.88294759285893,
	0.89100652418837,
	0.89879404629917,
	0.90630778703665,
	0.9135454576426,
	0.92050485345244,
	0.92718385456679,
	0.9335804264972,
	0.93969262078591,
	0.94551857559932,
	0.95105651629515,
	0.95630475596304,
	0.96126169593832,
	0.96592582628907,
	0.970295726276,
	0.97437006478524,
	0.97814760073381,
	0.98162718344766,
	0.98480775301221,
	0.98768834059514,
	0.99026806874157,
	0.99254615164132,
	0.99452189536827,
	0.99619469809175,
	0.99756405025982,
	0.99862953475457,
	0.9993908270191,
	0.99984769515639,
	1,
	0.99984769515639,
	0.9993908270191,
	0.99862953475457,
	0.99756405025982,
	0.99619469809175,
	0.99452189536827,
	0.99254615164132,
	0.99026806874157,
	0.98768834059514,
	0.98480775301221,
	0.98162718344766,
	0.97814760073381,
	0.97437006478524,
	0.970295726276,
	0.96592582628907,
	0.96126169593832,
	0.95630475596304,
	0.95105651629515,
	0.94551857559932,
	0.93969262078591,
	0.9335804264972,
	0.92718385456679,
	0.92050485345244,
	0.9135454576426,
	0.90630778703665,
	0.89879404629917,
	0.89100652418837,
	0.88294759285893,
	0.8746197071394,
	0.86602540378444,
	0.85716730070211,
	0.84804809615643,
	0.83867056794542,
	0.82903757255504,
	0.81915204428899,
	0.80901699437495,
	0.79863551004729,
	0.78801075360672,
	0.77714596145697,
	0.76604444311898,
	0.75470958022277,
	0.74314482547739,
	0.73135370161917,
	0.71933980033865,
	0.70710678118655,
	0.694658370459,
	0.6819983600625,
	0.66913060635886,
	0.65605902899051,
	0.64278760968654,
	0.62932039104984,
	0.61566147532566,
	0.60181502315205,
	0.58778525229247,
	0.57357643635105,
	0.55919290347075,
	0.54463903501503,
	0.5299192642332,
	0.51503807491005,
	0.5,
	0.48480962024634,
	0.46947156278589,
	0.45399049973955,
	0.43837114678908,
	0.4226182617407,
	0.4067366430758,
	0.39073112848927,
	0.37460659341591,
	0.3583679495453,
	0.34202014332567,
	0.32556815445716,
	0.30901699437495,
	0.29237170472274,
	0.275637355817,
	0.25881904510252,
	0.24192189559967,
	0.22495105434387,
	0.20791169081776,
	0.19080899537654,
	0.17364817766693,
	0.15643446504023,
	0.13917310096007,
	0.12186934340515,
	0.10452846326765,
	0.087155742747658,
	0.069756473744126,
	0.052335956242944,
	0.034899496702501,
	0.017452406437283,
	1.2246063538224e-16,
	-0.017452406437284,
	-0.034899496702501,
	-0.052335956242944,
	-0.069756473744125,
	-0.087155742747658,
	-0.10452846326765,
	-0.12186934340515,
	-0.13917310096007,
	-0.15643446504023,
	-0.17364817766693,
	-0.19080899537654,
	-0.20791169081776,
	-0.22495105434386,
	-0.24192189559967,
	-0.25881904510252,
	-0.275637355817,
	-0.29237170472274,
	-0.30901699437495,
	-0.32556815445716,
	-0.34202014332567,
	-0.3583679495453,
	-0.37460659341591,
	-0.39073112848927,
	-0.4067366430758,
	-0.4226182617407,
	-0.43837114678908,
	-0.45399049973955,
	-0.46947156278589,
	-0.48480962024634,
	-0.5,
	-0.51503807491005,
	-0.5299192642332,
	-0.54463903501503,
	-0.55919290347075,
	-0.57357643635105,
	-0.58778525229247,
	-0.60181502315205,
	-0.61566147532566,
	-0.62932039104984,
	-0.64278760968654,
	-0.65605902899051,
	-0.66913060635886,
	-0.6819983600625,
	-0.694658370459,
	-0.70710678118655,
	-0.71933980033865,
	-0.73135370161917,
	-0.74314482547739,
	-0.75470958022277,
	-0.76604444311898,
	-0.77714596145697,
	-0.78801075360672,
	-0.79863551004729,
	-0.80901699437495,
	-0.81915204428899,
	-0.82903757255504,
	-0.83867056794542,
	-0.84804809615643,
	-0.85716730070211,
	-0.86602540378444,
	-0.8746197071394,
	-0.88294759285893,
	-0.89100652418837,
	-0.89879404629917,
	-0.90630778703665,
	-0.9135454576426,
	-0.92050485345244,
	-0.92718385456679,
	-0.9335804264972,
	-0.93969262078591,
	-0.94551857559932,
	-0.95105651629515,
	-0.95630475596304,
	-0.96126169593832,
	-0.96592582628907,
	-0.970295726276,
	-0.97437006478524,
	-0.97814760073381,
	-0.98162718344766,
	-0.98480775301221,
	-0.98768834059514,
	-0.99026806874157,
	-0.99254615164132,
	-0.99452189536827,
	-0.99619469809175,
	-0.99756405025982,
	-0.99862953475457,
	-0.9993908270191,
	-0.99984769515639,
	-1,
	-0.99984769515639,
	-0.9993908270191,
	-0.99862953475457,
	-0.99756405025982,
	-0.99619469809175,
	-0.99452189536827,
	-0.99254615164132,
	-0.99026806874157,
	-0.98768834059514,
	-0.98480775301221,
	-0.98162718344766,
	-0.97814760073381,
	-0.97437006478524,
	-0.970295726276,
	-0.96592582628907,
	-0.96126169593832,
	-0.95630475596304,
	-0.95105651629515,
	-0.94551857559932,
	-0.93969262078591,
	-0.9335804264972,
	-0.92718385456679,
	-0.92050485345244,
	-0.9135454576426,
	-0.90630778703665,
	-0.89879404629917,
	-0.89100652418837,
	-0.88294759285893,
	-0.8746197071394,
	-0.86602540378444,
	-0.85716730070211,
	-0.84804809615643,
	-0.83867056794542,
	-0.82903757255504,
	-0.81915204428899,
	-0.80901699437495,
	-0.79863551004729,
	-0.78801075360672,
	-0.77714596145697,
	-0.76604444311898,
	-0.75470958022277,
	-0.74314482547739,
	-0.73135370161917,
	-0.71933980033865,
	-0.70710678118655,
	-0.694658370459,
	-0.6819983600625,
	-0.66913060635886,
	-0.65605902899051,
	-0.64278760968654,
	-0.62932039104984,
	-0.61566147532566,
	-0.60181502315205,
	-0.58778525229247,
	-0.57357643635105,
	-0.55919290347075,
	-0.54463903501503,
	-0.52991926423321,
	-0.51503807491005,
	-0.5,
	-0.48480962024634,
	-0.46947156278589,
	-0.45399049973955,
	-0.43837114678908,
	-0.4226182617407,
	-0.4067366430758,
	-0.39073112848927,
	-0.37460659341591,
	-0.3583679495453,
	-0.34202014332567,
	-0.32556815445716,
	-0.30901699437495,
	-0.29237170472274,
	-0.275637355817,
	-0.25881904510252,
	-0.24192189559967,
	-0.22495105434387,
	-0.20791169081776,
	-0.19080899537654,
	-0.17364817766693,
	-0.15643446504023,
	-0.13917310096007,
	-0.12186934340515,
	-0.10452846326765,
	-0.087155742747658,
	-0.069756473744126,
	-0.052335956242944,
	-0.034899496702501,
	-0.017452406437284,
	-2.4492127076448e-16
}

ove_0_25[0] = 1
ove_0_26[0] = 0

local function ove_0_27(arg_5_0)
	return (arg_5_0.y == 0 or arg_5_0.z ~= 0) and arg_5_0.z or arg_5_0.y
end

local function ove_0_28(arg_6_0)
	if not arg_6_0 or type(arg_6_0) == "number" then
		return false
	end

	if arg_6_0.type and arg_6_0.type == "Vector" then
		return true
	end

	arg_6_0 = arg_6_0.pos or arg_6_0

	return arg_6_0 and arg_6_0.x and type(arg_6_0.x) == "number" and (arg_6_0.y and type(arg_6_0.y) == "number" or arg_6_0.z and type(arg_6_0.z) == "number")
end

local function ove_0_29(arg_7_0, arg_7_1, arg_7_2)
	assert(type(arg_7_0) == "number" and type(arg_7_1) == "number", "math.close: wrong argument types (at least 2 <number> expected)")

	arg_7_2 = arg_7_2 or 1e-09

	return arg_7_2 >= ove_0_18(arg_7_0 - arg_7_1)
end

local ove_0_30 = {}

ove_0_30.__index = ove_0_30

function ove_0_30.__call(arg_8_0, ...)
	return arg_8_0:new(...)
end

setmetatable(ove_0_30, ove_0_30)

local ove_0_31 = {}

ove_0_31.__index = ove_0_31

function ove_0_31.__call(arg_9_0, ...)
	return arg_9_0:new(...)
end

local ove_0_32 = {}

ove_0_32.__index = ove_0_32

function ove_0_32.__call(arg_10_0, ...)
	return arg_10_0:new(...)
end

setmetatable(ove_0_32, ove_0_32)

local ove_0_33 = {}

ove_0_33.__index = ove_0_33

function ove_0_33.__call(arg_11_0, ...)
	return arg_11_0:new(...)
end

setmetatable(ove_0_33, ove_0_33)

local ove_0_34 = {}

ove_0_34.__index = ove_0_34

function ove_0_34.__call(arg_12_0, ...)
	return arg_12_0:new(...)
end

setmetatable(ove_0_34, ove_0_34)

function ove_0_30.new(arg_13_0, arg_13_1, arg_13_2, arg_13_3)
	local slot_13_0 = {
		type = "Vector"
	}

	if arg_13_3 then
		slot_13_0.x = arg_13_1
		slot_13_0.y = player.pos.y
		slot_13_0.z = arg_13_3
	elseif not arg_13_2 and ove_0_28(arg_13_1) then
		slot_13_0.x = arg_13_1.x or arg_13_1.pos and arg_13_1.pos.x or arg_13_1.pos and arg_13_1.pos.x
		slot_13_0.y = player.pos.y
		slot_13_0.z = arg_13_1.z or arg_13_1.pos and arg_13_1.pos.z or arg_13_1.pos and arg_13_1.pos.z
	else
		slot_13_0.x = 0
		slot_13_0.y = 0
		slot_13_0.z = 0
	end

	return setmetatable(slot_13_0, ove_0_30)
end

function ove_0_30.toDX3(arg_14_0)
	return vec3(arg_14_0.x, arg_14_0.y, arg_14_0.z)
end

function ove_0_30.toDX2(arg_15_0)
	return graphics.world_to_screen(arg_15_0:toDX3())
end

function ove_0_30.toMM(arg_16_0)
	return minimap.world_to_map(arg_16_0:toDX3())
end

function ove_0_30.__add(arg_17_0, arg_17_1)
	assert(ove_0_28(arg_17_1), "add: wrong argument types (<Vector> expected)")

	return ove_0_30:new(arg_17_0.x + arg_17_1.x, 0, arg_17_0.z + arg_17_1.z)
end

function ove_0_30.__sub(arg_18_0, arg_18_1)
	assert(ove_0_28(arg_18_1), "Sub: wrong argument types (<Vector> expected)")

	return ove_0_30:new(arg_18_0.x - arg_18_1.x, 0, arg_18_0.z - arg_18_1.z)
end

function ove_0_30.__mul(arg_19_0, arg_19_1)
	if type(arg_19_0) == "number" and ove_0_28(arg_19_1) then
		return ove_0_30:new(arg_19_1.x * arg_19_0, 0, arg_19_1.z * arg_19_0)
	elseif type(arg_19_1) == "number" and ove_0_28(arg_19_0) then
		return ove_0_30:new(arg_19_0.x * arg_19_1, 0, arg_19_0.z * arg_19_1)
	else
		assert(ove_0_28(arg_19_0) and ove_0_28(arg_19_1), "Mul: wrong argument types (<Vector> or <number> expected)")

		return arg_19_0:dotP(arg_19_1)
	end
end

function ove_0_30.__div(arg_20_0, arg_20_1)
	if type(arg_20_0) == "number" and ove_0_28(arg_20_1) then
		return ove_0_30:new(arg_20_0 / arg_20_1.x, 0, arg_20_0 / arg_20_1.z)
	elseif type(arg_20_1) == "number" and ove_0_28(arg_20_0) then
		return ove_0_30:new(arg_20_0.x / arg_20_1, 0, arg_20_0.z / arg_20_1)
	elseif arg_20_0.type == "Vector" and arg_20_1.type == "Vector" then
		return ove_0_30:new(arg_20_0.x / arg_20_1.x, 0, arg_20_0.z / arg_20_1.z)
	end
end

function ove_0_30.__lt(arg_21_0, arg_21_1)
	assert(ove_0_28(arg_21_0) and ove_0_28(arg_21_1), "__lt: wrong argument types (<Vector> expected)")

	return arg_21_0:len() < arg_21_1:len()
end

function ove_0_30.__le(arg_22_0, arg_22_1)
	assert(ove_0_28(arg_22_0) and ove_0_28(arg_22_1), "__le: wrong argument types (<Vector> expected)")

	return arg_22_0:len() <= arg_22_1:len()
end

function ove_0_30.__eq(arg_23_0, arg_23_1)
	assert(ove_0_28(arg_23_1), "__eq: wrong argument types (<Vector> expected)")

	return arg_23_0.x == arg_23_1.x and arg_23_0.z == arg_23_1.z
end

function ove_0_30.__unm(arg_24_0)
	return ove_0_30:new(-arg_24_0.x, 0, -arg_24_0.z)
end

function ove_0_30.__vector(arg_25_0, arg_25_1)
	assert(ove_0_28(arg_25_1), "__vector: wrong argument types (<Vector> expected)")

	return arg_25_0:crossP(arg_25_1)
end

function ove_0_30.__tostring(arg_26_0)
	return "(" .. arg_26_0.x .. ", 0, " .. arg_26_0.z .. ")"
end

function ove_0_30.clone(arg_27_0)
	return ove_0_30:new(arg_27_0)
end

function ove_0_30.unpack(arg_28_0)
	return arg_28_0.x, arg_28_0.y, arg_28_0.z
end

function ove_0_30.RotatedAngle(arg_29_0, arg_29_1)
	local slot_29_0 = ove_0_25[arg_29_1]
	local slot_29_1 = ove_0_26[arg_29_1]

	return ove_0_30(arg_29_0.x * slot_29_0 + arg_29_0.z * slot_29_1, arg_29_0.y, arg_29_0.z * slot_29_0 - arg_29_0.x * slot_29_1)
end

function ove_0_30.len2(arg_30_0, arg_30_1)
	assert(arg_30_1 == nil or ove_0_28(arg_30_1), "dist: wrong argument types (<Vector> expected)")

	local slot_30_0 = arg_30_1 and ove_0_30:new(arg_30_1) or arg_30_0

	return arg_30_0.x * slot_30_0.x + arg_30_0.z * slot_30_0.z
end

function ove_0_30.len(arg_31_0)
	return ove_0_12(arg_31_0:len2())
end

function ove_0_30.dist(arg_32_0, arg_32_1)
	assert(ove_0_28(arg_32_1), "dist: wrong argument types (<Vector> expected)")

	return (arg_32_0 - arg_32_1):len()
end

function ove_0_30.distSqr(arg_33_0, arg_33_1)
	assert(ove_0_28(arg_33_1), "distSqr: wrong argument types (<Vector> expected)")

	return (arg_33_0 - arg_33_1):len2()
end

function ove_0_30.normalize(arg_34_0)
	local slot_34_0 = arg_34_0:len()

	arg_34_0.x = arg_34_0.x / slot_34_0
	arg_34_0.z = arg_34_0.z / slot_34_0
end

function ove_0_30.normalized(arg_35_0)
	local slot_35_0 = arg_35_0:clone()

	slot_35_0:normalize()

	return slot_35_0
end

function ove_0_30.extended(arg_36_0, arg_36_1, arg_36_2)
	return arg_36_0 + (ove_0_30(arg_36_1) - arg_36_0):normalized() * arg_36_2
end

function ove_0_30.shortened(arg_37_0, arg_37_1, arg_37_2)
	return arg_37_0 - arg_37_2 * (ove_0_30(arg_37_1) - arg_37_0):normalized()
end

function ove_0_30.center(arg_38_0, arg_38_1)
	assert(ove_0_28(arg_38_1), "center: wrong argument types (<Vector> expected)")

	return ove_0_30:new((arg_38_0 + arg_38_1) / 2)
end

function ove_0_30.crossP(arg_39_0, arg_39_1)
	assert(arg_39_0.y and arg_39_0.z and arg_39_1.y and arg_39_1.z, "crossP: wrong argument types (3 Dimensional <Vector> expected)")

	return ove_0_30:new({
		x = arg_39_1.z * arg_39_0.y - arg_39_1.y * arg_39_0.z,
		y = arg_39_1.x * arg_39_0.z - arg_39_1.z * arg_39_0.x,
		z = arg_39_1.y * arg_39_0.x - arg_39_1.x * arg_39_0.y
	})
end

function ove_0_30.dotP(arg_40_0, arg_40_1)
	assert(ove_0_28(arg_40_1), "dotP: wrong argument types (<Vector> expected)")

	return arg_40_0.x * arg_40_1.x + arg_40_0.z * arg_40_1.z
end

function ove_0_30.angleBetweenFull(arg_41_0, arg_41_1, arg_41_2)
	local slot_41_0 = -arg_41_0 + arg_41_1
	local slot_41_1 = -arg_41_0 + arg_41_2
	local slot_41_2 = slot_41_0:polar() - slot_41_1:polar()

	if slot_41_2 < 0 then
		slot_41_2 = slot_41_2 + 360
	end

	return slot_41_2
end

function ove_0_30.projectOn(arg_42_0, arg_42_1)
	if arg_42_1.type and arg_42_1.type == "LineSegment" then
		local slot_42_0 = arg_42_1:getPoints()
		local slot_42_1 = slot_42_0[1]
		local slot_42_2 = slot_42_0[2]
		local slot_42_3 = arg_42_0.x
		local slot_42_4 = arg_42_0.z
		local slot_42_5 = slot_42_1.x
		local slot_42_6 = slot_42_1.z
		local slot_42_7 = slot_42_2.x
		local slot_42_8 = slot_42_2.z
		local slot_42_9 = ((slot_42_3 - slot_42_5) * (slot_42_7 - slot_42_5) + (slot_42_4 - slot_42_6) * (slot_42_8 - slot_42_6)) / ((slot_42_7 - slot_42_5)^2 + (slot_42_8 - slot_42_6)^2)
		local slot_42_10 = {
			x = slot_42_5 + slot_42_9 * (slot_42_7 - slot_42_5),
			y = slot_42_6 + slot_42_9 * (slot_42_8 - slot_42_6)
		}
		local slot_42_11 = slot_42_9 < 0 and 0 or slot_42_9 > 1 and 1 or slot_42_9
		local slot_42_12 = slot_42_11 == slot_42_9

		return slot_42_12 and slot_42_10 or {
			x = slot_42_5 + slot_42_11 * (slot_42_7 - slot_42_5),
			y = slot_42_6 + slot_42_11 * (slot_42_8 - slot_42_6)
		}, slot_42_10, slot_42_12
	else
		assert(ove_0_28(arg_42_1), "projectOn: invalid argument: cannot project Vector on " .. type(arg_42_1))

		if type(arg_42_1) ~= "Vector" then
			arg_42_1 = ove_0_30:new(arg_42_1)
		end

		local slot_42_13 = arg_42_0:len2(arg_42_1) / arg_42_1:len2()

		return ove_0_30:new(arg_42_1 * slot_42_13)
	end
end

function ove_0_30.mirrorOn(arg_43_0, arg_43_1)
	assert(ove_0_28(arg_43_1), "mirrorOn: invalid argument: cannot mirror Vector on " .. type(arg_43_1))

	return arg_43_0:projectOn(arg_43_1) * 2
end

function ove_0_30.sin(arg_44_0, arg_44_1)
	assert(ove_0_28(arg_44_1), "sin: wrong argument types (<Vector> expected)")

	if type(arg_44_1) ~= "Vector" then
		arg_44_1 = ove_0_30:new(arg_44_1)
	end

	local slot_44_0 = arg_44_0:__vector(arg_44_1)

	return ove_0_12(slot_44_0:len2() / (arg_44_0:len2() * arg_44_1:len2()))
end

function ove_0_30.cos(arg_45_0, arg_45_1)
	assert(ove_0_28(arg_45_1), "cos: wrong argument types (<Vector> expected)")

	if type(arg_45_1) ~= "Vector" then
		arg_45_1 = ove_0_30:new(arg_45_1)
	end

	return arg_45_0:len2(arg_45_1) / ove_0_12(arg_45_0:len2() * arg_45_1:len2())
end

function ove_0_30.angle(arg_46_0, arg_46_1)
	assert(ove_0_28(arg_46_1), "angle: wrong argument types (<Vector> expected)")

	return ove_0_15(arg_46_0:cos(arg_46_1))
end

function ove_0_30.affineArea(arg_47_0, arg_47_1)
	assert(ove_0_28(arg_47_1), "affineArea: wrong argument types (<Vector> expected)")

	if type(arg_47_1) ~= "Vector" then
		arg_47_1 = ove_0_30:new(arg_47_1)
	end

	local slot_47_0 = arg_47_0:__vector(arg_47_1)

	return ove_0_12(slot_47_0:len2())
end

function ove_0_30.triangleArea(arg_48_0, arg_48_1)
	assert(ove_0_28(arg_48_1), "triangleArea: wrong argument types (<Vector> expected)")

	return arg_48_0:affineArea(arg_48_1) / 2
end

function ove_0_30.rotateXaxis(arg_49_0, arg_49_1)
	assert(type(arg_49_1) == "number", "Rotate: wrong argument types (expected <number> for phi)")

	local slot_49_0 = ove_0_13(arg_49_1)
	local slot_49_1 = ove_0_14(arg_49_1)

	arg_49_0.y, arg_49_0.z = arg_49_0.y * slot_49_0 - arg_49_0.z * slot_49_1, arg_49_0.z * slot_49_0 + arg_49_0.y * slot_49_1
end

function ove_0_30.rotateYaxis(arg_50_0, arg_50_1)
	assert(type(arg_50_1) == "number", "Rotate: wrong argument types (expected <number> for phi)")

	local slot_50_0 = ove_0_13(arg_50_1)
	local slot_50_1 = ove_0_14(arg_50_1)

	arg_50_0.x, arg_50_0.z = arg_50_0.x * slot_50_0 + arg_50_0.z * slot_50_1, arg_50_0.z * slot_50_0 - arg_50_0.x * slot_50_1
end

function ove_0_30.rotateZaxis(arg_51_0, arg_51_1)
	assert(type(arg_51_1) == "number", "Rotate: wrong argument types (expected <number> for phi)")

	local slot_51_0 = ove_0_13(arg_51_1)
	local slot_51_1 = ove_0_14(arg_51_1)

	arg_51_0.x, arg_51_0.y = arg_51_0.x * slot_51_0 - arg_51_0.z * slot_51_1, arg_51_0.y * slot_51_0 + arg_51_0.x * slot_51_1
end

function ove_0_30.rotate(arg_52_0, arg_52_1, arg_52_2, arg_52_3)
	assert(type(arg_52_1) == "number" and type(arg_52_2) == "number" and type(arg_52_3) == "number", "Rotate: wrong argument types (expected <number> for phi)")

	if arg_52_1 ~= 0 then
		arg_52_0:rotateXaxis(arg_52_1)
	end

	if arg_52_2 ~= 0 then
		arg_52_0:rotateYaxis(arg_52_2)
	end

	if arg_52_3 ~= 0 then
		arg_52_0:rotateZaxis(arg_52_3)
	end
end

function ove_0_30.rotated(arg_53_0, arg_53_1, arg_53_2, arg_53_3)
	assert(type(arg_53_1) == "number" and type(arg_53_2) == "number" and type(arg_53_3) == "number", "Rotated: wrong argument types (expected <number> for phi)")

	local slot_53_0 = arg_53_0:clone()

	slot_53_0:rotate(arg_53_1, arg_53_2, arg_53_3)

	return slot_53_0
end

function ove_0_30.isZero(arg_54_0)
	return arg_54_0 == ove_0_30:new()
end

function ove_0_30.polar(arg_55_0)
	if ove_0_29(arg_55_0.x, 0) then
		if arg_55_0.z > 0 then
			return 90
		elseif arg_55_0.z < 0 then
			return 270
		else
			return 0
		end
	else
		local slot_55_0 = ove_0_16(ove_0_17(arg_55_0.z / arg_55_0.x))

		if arg_55_0.x < 0 then
			slot_55_0 = slot_55_0 + 180
		end

		if slot_55_0 < 0 then
			slot_55_0 = slot_55_0 + 360
		end

		return slot_55_0
	end
end

function ove_0_30.AngleBetween(arg_56_0, arg_56_1, arg_56_2)
	assert(ove_0_28(arg_56_1) and ove_0_28(arg_56_2), "angleBetween: wrong argument types (2 <Vector> expected)")

	local slot_56_0 = -arg_56_0 + arg_56_1
	local slot_56_1 = -arg_56_0 + arg_56_2
	local slot_56_2 = slot_56_0:polar() - slot_56_1:polar()

	if slot_56_2 < 0 then
		slot_56_2 = slot_56_2 + 360
	end

	if slot_56_2 > 180 then
		slot_56_2 = 360 - slot_56_2
	end

	return slot_56_2
end

function ove_0_30.compare(arg_57_0, arg_57_1)
	assert(ove_0_28(arg_57_1), "compare: wrong argument types (<Vector> expected)")

	local slot_57_0 = arg_57_0.x - arg_57_1.x

	if slot_57_0 == 0 then
		slot_57_0 = arg_57_0.z - arg_57_1.z
	end

	return slot_57_0
end

function ove_0_30.perpendicular(arg_58_0)
	return ove_0_30:new(-arg_58_0.z, 0, arg_58_0.x)
end

function ove_0_30.perpendicular2(arg_59_0)
	return ove_0_30:new(arg_59_0.z, 0, -arg_59_0.x)
end

function ove_0_30.abs(arg_60_0)
	return ove_0_30:new(ove_0_18(arg_60_0.x), 0, ove_0_18(arg_60_0.z))
end

function ove_0_30.distanceTo(arg_61_0, arg_61_1)
	if arg_61_1.type == "Vector" then
		return arg_61_0:__sub(arg_61_1):len()
	elseif arg_61_1.type == "LineSegment" then
		return arg_61_1:distanceTo(arg_61_0)
	elseif arg_61_1.type == "Circle" then
		-- block empty
	end
end

function ove_0_30.insideOf(arg_62_0, arg_62_1)
	return arg_62_1:contains(arg_62_0)
end

function ove_0_30.getPoints(arg_63_0)
	return {
		arg_63_0
	}
end

function ove_0_30.draw(arg_64_0, arg_64_1)
	ove_0_32:new(arg_64_0, 5):draw(arg_64_1)
end

function ove_0_32.new(arg_65_0, arg_65_1, arg_65_2, arg_65_3)
	local slot_65_0 = {
		type = "Circle"
	}

	if arg_65_1 and not arg_65_3 then
		slot_65_0.point = ove_0_30:new(arg_65_1)
		slot_65_0.radius = arg_65_2 or 0
	end

	slot_65_0.points = {
		slot_65_0.point
	}

	return setmetatable(slot_65_0, ove_0_32)
end

function ove_0_32.__eq(arg_66_0, arg_66_1)
	return arg_66_1.type == "Circle" and arg_66_0.point == arg_66_1.point and arg_66_0.radius == arg_66_1.radius
end

function ove_0_32.getPoints(arg_67_0)
	return arg_67_0.points
end

function ove_0_32.getLineSegments(arg_68_0)
	return {}
end

function ove_0_32.contains(arg_69_0, arg_69_1)
	if arg_69_1.type == "Circle" then
		return arg_69_0.radius >= arg_69_1.radius + arg_69_0.point:distanceTo(arg_69_1.point)
	else
		for iter_69_0, iter_69_1 in ipairs(arg_69_1:getPoints()) do
			if arg_69_0.point:distanceTo(iter_69_1) >= arg_69_0.radius then
				return false
			end
		end

		return true
	end
end

function ove_0_32.insideOf(arg_70_0, arg_70_1)
	return arg_70_1:contains(arg_70_0)
end

function ove_0_32.distanceTo(arg_71_0, arg_71_1)
	return arg_71_0.point:distanceTo(arg_71_1) - arg_71_0.radius
end

function ove_0_32.intersects(arg_72_0, arg_72_1)
	if arg_72_1.type == "LineSegment" then
		return arg_72_1:intersects(arg_72_0)
	elseif arg_72_1.type == "Circle" then
		return arg_72_0:distanceTo(arg_72_1) <= arg_72_0.radius + arg_72_1.radius
	elseif arg_72_1.type == "Vector" then
		return arg_72_1:insideOf(arg_72_0)
	end
end

function ove_0_32.intersectionPoints(arg_73_0, arg_73_1)
	local slot_73_0 = {}

	if arg_73_1.type == "Circle" then
		local slot_73_1 = arg_73_0.point.x
		local slot_73_2 = arg_73_0.point.z
		local slot_73_3 = arg_73_1.point.x
		local slot_73_4 = arg_73_1.point.z
		local slot_73_5 = slot_73_1 - slot_73_3
		local slot_73_6 = slot_73_2 - slot_73_4
		local slot_73_7 = ove_0_12(slot_73_5 * slot_73_5 + slot_73_6 * slot_73_6)

		if slot_73_7 > arg_73_0.radius + arg_73_1.radius then
			return slot_73_0
		elseif slot_73_7 < ove_0_18(arg_73_0.radius - arg_73_1.radius) then
			slot_73_0.INSIDE = true

			return slot_73_0
		elseif slot_73_7 == 0 and arg_73_0.radius == arg_73_1.radius then
			return slot_73_0
		else
			local slot_73_8 = (arg_73_0.radius * arg_73_0.radius - arg_73_1.radius * arg_73_1.radius + slot_73_7 * slot_73_7) / (2 * slot_73_7)
			local slot_73_9 = ove_0_12(arg_73_0.radius * arg_73_0.radius - slot_73_8 * slot_73_8)
			local slot_73_10 = slot_73_1 + slot_73_8 * (slot_73_3 - slot_73_1) / slot_73_7
			local slot_73_11 = slot_73_2 + slot_73_8 * (slot_73_4 - slot_73_2) / slot_73_7
			local slot_73_12 = slot_73_10 + slot_73_9 * (slot_73_4 - slot_73_2) / slot_73_7
			local slot_73_13 = slot_73_11 - slot_73_9 * (slot_73_3 - slot_73_1) / slot_73_7
			local slot_73_14 = slot_73_10 - slot_73_9 * (slot_73_4 - slot_73_2) / slot_73_7
			local slot_73_15 = slot_73_11 + slot_73_9 * (slot_73_3 - slot_73_1) / slot_73_7

			ove_0_22(slot_73_0, ove_0_30(slot_73_12, slot_73_13))

			if slot_73_12 ~= slot_73_14 or slot_73_13 ~= slot_73_15 then
				ove_0_22(slot_73_0, ove_0_30(slot_73_14, slot_73_15))
			end
		end
	end

	return slot_73_0
end

function ove_0_32.__tostring(arg_74_0)
	return "Circle(" .. tostring(arg_74_0.point) .. ", " .. arg_74_0.radius .. ")"
end

function ove_0_32.draw(arg_75_0, arg_75_1)
	local slot_75_0 = vec3(arg_75_0.point.x, 0, arg_75_0.point.z)

	graphics.draw_circle(slot_75_0, arg_75_0.radius, 2, 4294967295, 32)
end

function ove_0_33.new(arg_76_0, arg_76_1, arg_76_2, arg_76_3)
	local slot_76_0 = {
		type = "LineSegment",
		points = {}
	}

	if arg_76_1 and not arg_76_2 then
		slot_76_0.points = {
			ove_0_30:new(arg_76_1),
			ove_0_30:new()
		}
	elseif not arg_76_1 then
		slot_76_0.points = {
			ove_0_30:new(),
			ove_0_30:new()
		}
	else
		slot_76_0.points = {
			ove_0_30:new(arg_76_1),
			ove_0_30:new(arg_76_2)
		}
	end

	return setmetatable(slot_76_0, ove_0_33)
end

function ove_0_33.__eq(arg_77_0, arg_77_1)
	return arg_77_1.type == "LineSegment" and (arg_77_0.points[1] == arg_77_1.points[1] and arg_77_0.points[2] == arg_77_1.points[2] or arg_77_0.points[2] == arg_77_1.points[1] and arg_77_0.points[1] == arg_77_1.points[2])
end

function ove_0_33.getPoints(arg_78_0)
	return arg_78_0.points
end

function ove_0_33.getLineSegments(arg_79_0)
	return {
		arg_79_0
	}
end

function ove_0_33.direction(arg_80_0)
	return arg_80_0.points[2] - arg_80_0.points[1]
end

function ove_0_33.len(arg_81_0)
	return (arg_81_0.points[1] - arg_81_0.points[2]):len()
end

function ove_0_33.contains(arg_82_0, arg_82_1)
	if arg_82_1.type == "Vector" then
		return arg_82_1:distanceTo(arg_82_0) == 0
	elseif arg_82_1.type == "Circle" then
		return arg_82_1.point:distanceTo(arg_82_0) == 0 and arg_82_1.radius <= 1
	elseif arg_82_1.type == "LineSegment" then
		return arg_82_1.points[1]:distanceTo(arg_82_0) == 0 and arg_82_1.points[2]:distanceTo(arg_82_0) == 0
	else
		for iter_82_0, iter_82_1 in ipairs(arg_82_1:getPoints()) do
			if iter_82_1:distanceTo(arg_82_0) ~= 0 then
				return false
			end
		end

		return true
	end

	return false
end

function ove_0_33.insideOf(arg_83_0, arg_83_1)
	return arg_83_1:contains(arg_83_0)
end

function ove_0_33.distanceTo(arg_84_0, arg_84_1)
	if arg_84_1.type == "Circle" then
		return arg_84_1.point:distanceTo(arg_84_0) - arg_84_1.radius
	elseif arg_84_1.type == "LineSegment" then
		-- block empty
	elseif arg_84_1.type == "Vector" then
		local slot_84_0 = arg_84_0.points[1].z
		local slot_84_1 = arg_84_0.points[2].z
		local slot_84_2 = arg_84_1.z
		local slot_84_3 = {
			X = arg_84_1.x,
			Y = slot_84_2
		}
		local slot_84_4 = {
			X = arg_84_0.points[1].x,
			Y = slot_84_0
		}
		local slot_84_5 = {
			X = arg_84_0.points[2].x,
			Y = slot_84_1
		}
		local slot_84_6 = arg_84_0.points[2].x - arg_84_0.points[1].x
		local slot_84_7 = slot_84_1 - slot_84_0

		if slot_84_6 == 0 and slot_84_7 == 0 then
			slot_84_6 = arg_84_1.x - arg_84_0.points[1].x
			slot_84_7 = slot_84_2 - slot_84_0

			return ove_0_12(slot_84_6 * slot_84_6 + slot_84_7 * slot_84_7)
		end

		local slot_84_8 = ((slot_84_3.X - slot_84_4.X) * slot_84_6 + (slot_84_3.Y - slot_84_4.Y) * slot_84_7) / (slot_84_6 * slot_84_6 + slot_84_7 * slot_84_7)

		if slot_84_8 < 0 then
			slot_84_6 = slot_84_3.X - slot_84_4.X
			slot_84_7 = slot_84_3.Y - slot_84_4.Y
		elseif slot_84_8 > 1 then
			slot_84_6 = slot_84_3.X - slot_84_5.X
			slot_84_7 = slot_84_3.Y - slot_84_5.Y
		else
			local slot_84_9 = ove_0_30:new(slot_84_4.X + slot_84_8 * slot_84_6, 0, slot_84_4.Y + slot_84_8 * slot_84_7)

			slot_84_6 = slot_84_3.X - slot_84_9.x
			slot_84_7 = slot_84_3.Y - slot_84_9.z
		end

		return ove_0_12(slot_84_6 * slot_84_6 + slot_84_7 * slot_84_7)
	end
end

function ove_0_33.intersects(arg_85_0, arg_85_1)
	if arg_85_1.type == "LineSegment" then
		local slot_85_0 = {
			X1 = arg_85_0.points[1].x,
			Y1 = arg_85_0.points[1].z,
			X2 = arg_85_0.points[2].x,
			Y2 = arg_85_0.points[2].z
		}
		local slot_85_1 = {
			X1 = arg_85_1.points[1].x,
			Y1 = arg_85_1.points[1].z,
			X2 = arg_85_1.points[2].x,
			Y2 = arg_85_1.points[2].z
		}
		local slot_85_2 = (slot_85_1.Y2 - slot_85_1.Y1) * (slot_85_0.X2 - slot_85_0.X1) - (slot_85_1.X2 - slot_85_1.X1) * (slot_85_0.Y2 - slot_85_0.Y1)

		if slot_85_2 == 0 then
			return false
		end

		local slot_85_3 = (slot_85_1.X2 - slot_85_1.X1) * (slot_85_0.Y1 - slot_85_1.Y1) - (slot_85_1.Y2 - slot_85_1.Y1) * (slot_85_0.X1 - slot_85_1.X1)
		local slot_85_4 = (slot_85_0.X2 - slot_85_0.X1) * (slot_85_0.Y1 - slot_85_1.Y1) - (slot_85_0.Y2 - slot_85_0.Y1) * (slot_85_0.X1 - slot_85_1.X1)
		local slot_85_5 = slot_85_3 / slot_85_2
		local slot_85_6 = slot_85_4 / slot_85_2

		if slot_85_5 >= 0 and slot_85_5 <= 1 and slot_85_6 >= 0 and slot_85_6 <= 1 then
			local slot_85_7 = slot_85_0.X1 + slot_85_5 * (slot_85_0.X2 - slot_85_0.X1)
			local slot_85_8 = slot_85_0.Y1 + slot_85_5 * (slot_85_0.Y2 - slot_85_0.Y1)

			return true, {
				y = 0,
				x = slot_85_7,
				z = slot_85_8
			}
		end

		return false
	elseif arg_85_1.type == "Circle" then
		return arg_85_0:distanceTo(arg_85_1) <= 0
	elseif arg_85_1.type == "Vector" then
		return arg_85_1:insideOf(arg_85_0)
	end
end

function ove_0_33.Intersection(arg_86_0, arg_86_1, arg_86_2, arg_86_3)
	local slot_86_0 = arg_86_1 - arg_86_0
	local slot_86_1 = arg_86_3 - arg_86_2
	local slot_86_2 = slot_86_0:crossP(slot_86_1)

	if slot_86_2 == 0 then
		return nil
	end

	local slot_86_3 = arg_86_2 - arg_86_0
	local slot_86_4 = slot_86_3:crossP(slot_86_1) / slot_86_2
	local slot_86_5 = slot_86_3:crossP(slot_86_0) / slot_86_2

	if slot_86_4 >= 0 and slot_86_4 <= 1 and slot_86_5 >= 0 and slot_86_5 <= 1 then
		return arg_86_0 + slot_86_0 * slot_86_4
	end

	return nil
end

function ove_0_33.closest(arg_87_0, arg_87_1)
	assert(ove_0_28(arg_87_1), "closest: wrong argument types (<Vector> expected)")

	local slot_87_0 = arg_87_0.points[1].z
	local slot_87_1 = arg_87_0.points[2].z
	local slot_87_2 = arg_87_1.z
	local slot_87_3 = {
		X = arg_87_1.x,
		Y = slot_87_2
	}
	local slot_87_4 = {
		X = arg_87_0.points[1].x,
		Y = slot_87_0
	}
	local slot_87_5 = {
		X = arg_87_0.points[2].x,
		Y = slot_87_1
	}
	local slot_87_6 = arg_87_0.points[2].x - arg_87_0.points[1].x
	local slot_87_7 = slot_87_1 - slot_87_0

	if slot_87_6 == 0 and slot_87_7 == 0 then
		return arg_87_0.points[1]
	end

	local slot_87_8 = ((slot_87_3.X - slot_87_4.X) * slot_87_6 + (slot_87_3.Y - slot_87_4.Y) * slot_87_7) / (slot_87_6 * slot_87_6 + slot_87_7 * slot_87_7)

	if slot_87_8 < 0 then
		return arg_87_0.points[1]
	elseif slot_87_8 > 1 then
		return arg_87_0.points[2]
	else
		return ove_0_30(slot_87_4.X + slot_87_8 * slot_87_6, arg_87_0.points[1], slot_87_4.Y + slot_87_8 * slot_87_7)
	end
end

function ove_0_33.__tostring(arg_88_0)
	return "LineSegment(" .. tostring(arg_88_0.points[1]) .. ", " .. tostring(arg_88_0.points[2]) .. ")"
end

function ove_0_33.draw(arg_89_0, arg_89_1)
	local slot_89_0 = vec3(arg_89_0.points[1].x, arg_89_0.points[1].y, arg_89_0.points[1].z)
	local slot_89_1 = vec3(arg_89_0.points[2].x, arg_89_0.points[2].y, arg_89_0.points[2].z)

	graphics.draw_line(slot_89_0, slot_89_1, 2, arg_89_1 or 4278255360)
end

function ove_0_34.new(arg_90_0, ...)
	local slot_90_0 = {
		...
	}
	local slot_90_1 = {
		type = "Polygon",
		points = {}
	}

	setmetatable(slot_90_1, ove_0_34)

	for iter_90_0 = 1, #slot_90_0 do
		local slot_90_2 = slot_90_0[iter_90_0]

		if ove_0_28(slot_90_2) then
			slot_90_1:addPoint(slot_90_2)
		end
	end

	return slot_90_1
end

function ove_0_34.getPoints(arg_91_0)
	return arg_91_0.points
end

function ove_0_34.addPoint(arg_92_0, arg_92_1)
	ove_0_22(arg_92_0.points, ove_0_30(arg_92_1))

	arg_92_0.lineSegments = {}
	arg_92_0.triangles = {}
end

function ove_0_34.getLineSegments(arg_93_0)
	if #arg_93_0.lineSegments == 0 then
		for iter_93_0 = 1, #arg_93_0.points do
			ove_0_22(arg_93_0.lineSegments, ove_0_33:new(arg_93_0.points[iter_93_0], arg_93_0.points[iter_93_0 % #arg_93_0.points + 1]))
		end
	end

	return arg_93_0.lineSegments
end

function ove_0_34.contains(arg_94_0, arg_94_1)
	if not arg_94_1 then
		return
	end

	if #arg_94_0.points == 3 then
		for iter_94_0, iter_94_1 in ipairs(arg_94_1:getPoints()) do
			local slot_94_0 = (ove_0_27(iter_94_1) - ove_0_27(arg_94_0.points[1])) * (arg_94_0.points[2].x - arg_94_0.points[1].x) - (iter_94_1.x - arg_94_0.points[1].x) * (ove_0_27(arg_94_0.points[2]) - ove_0_27(arg_94_0.points[1]))
			local slot_94_1 = (ove_0_27(iter_94_1) - ove_0_27(arg_94_0.points[2])) * (arg_94_0.points[3].x - arg_94_0.points[2].x) - (iter_94_1.x - arg_94_0.points[2].x) * (ove_0_27(arg_94_0.points[3]) - ove_0_27(arg_94_0.points[2]))
			local slot_94_2 = (ove_0_27(iter_94_1) - ove_0_27(arg_94_0.points[3])) * (arg_94_0.points[1].x - arg_94_0.points[3].x) - (iter_94_1.x - arg_94_0.points[3].x) * (ove_0_27(arg_94_0.points[1]) - ove_0_27(arg_94_0.points[3]))

			if not (slot_94_0 * slot_94_1 >= 0) or not (slot_94_1 * slot_94_2 >= 0) then
				return false
			end
		end

		if arg_94_1.type == "Circle" then
			for iter_94_2, iter_94_3 in ipairs(arg_94_0:getLineSegments()) do
				if arg_94_1.point:distanceTo(iter_94_3) <= 0 then
					return false
				end
			end
		end

		return true
	elseif arg_94_1.type == "Vector" then
		local slot_94_3
		local slot_94_4
		local slot_94_5
		local slot_94_6
		local slot_94_7
		local slot_94_8
		local slot_94_9 = arg_94_1.x
		local slot_94_10 = ove_0_27(arg_94_1)
		local slot_94_11 = #arg_94_0.points
		local slot_94_12 = arg_94_0.points[slot_94_11]
		local slot_94_13 = arg_94_0.points[1]
		local slot_94_14 = slot_94_10 <= ove_0_27(slot_94_12)
		local slot_94_15 = false

		for iter_94_4 = 2, slot_94_11 + 1 do
			local slot_94_16 = slot_94_10 <= ove_0_27(slot_94_13)

			if slot_94_14 ~= slot_94_16 and (ove_0_27(slot_94_13) - slot_94_10) * (slot_94_12.x - slot_94_13.x) >= (slot_94_13.x - slot_94_9) * (slot_94_12.z - ove_0_27(slot_94_13)) == slot_94_16 then
				slot_94_15 = not slot_94_15
			end

			slot_94_14 = slot_94_16
			slot_94_12 = slot_94_13
			slot_94_13 = arg_94_0.points[iter_94_4]
		end

		return slot_94_15
	elseif arg_94_1.type == "Circle" then
		return arg_94_0:contains(arg_94_1.point)
	end
end

function ove_0_34.insideOf(arg_95_0, arg_95_1)
	return arg_95_1:contains(arg_95_0)
end

function ove_0_34.direction(arg_96_0)
	if arg_96_0.directionValue == nil then
		local slot_96_0
		local slot_96_1

		for iter_96_0, iter_96_1 in ipairs(arg_96_0.points) do
			if slot_96_0 == nil or iter_96_1.x >= slot_96_0.x then
				slot_96_0 = iter_96_1
				slot_96_1 = iter_96_0
			end
		end

		rightMostPointPredecessor = arg_96_0.points[(slot_96_1 - 1 - 1) % #arg_96_0.points + 1]
		rightMostPointSuccessor = arg_96_0.points[(slot_96_1 + 1 - 1) % #arg_96_0.points + 1]
		z = (slot_96_0.x - rightMostPointPredecessor.x) * (ove_0_27(rightMostPointSuccessor) - ove_0_27(slot_96_0)) - (ove_0_27(slot_96_0) - ove_0_27(rightMostPointPredecessor)) * (rightMostPointSuccessor.x - slot_96_0.x)

		if z > 0 then
			arg_96_0.directionValue = 1
		elseif z < 0 then
			arg_96_0.directionValue = -1
		else
			arg_96_0.directionValue = 0
		end
	end

	return arg_96_0.directionValue
end

function ove_0_34.triangulate(arg_97_0)
	if #arg_97_0.triangles == 0 then
		if #arg_97_0.points > 3 then
			tempPoints = {}

			for iter_97_0, iter_97_1 in ipairs(arg_97_0.points) do
				ove_0_22(tempPoints, iter_97_1)
			end

			triangleFound = true

			while #tempPoints > 3 and triangleFound do
				triangleFound = false

				for iter_97_2, iter_97_3 in ipairs(tempPoints) do
					point1Index = (iter_97_2 - 1 - 1) % #tempPoints + 1
					point2Index = (iter_97_2 + 1 - 1) % #tempPoints + 1
					point1 = tempPoints[point1Index]
					point2 = tempPoints[point2Index]

					if ((point1.x - iter_97_3.x) * (ove_0_27(point2) - ove_0_27(iter_97_3)) - (ove_0_27(point1) - ove_0_27(iter_97_3)) * (point2.x - iter_97_3.x)) * arg_97_0:direction() < 0 then
						triangleCandidate = ove_0_34:new(point1, iter_97_3, point2)
						anotherPointInTriangleFound = false

						for iter_97_4 = 1, #tempPoints do
							if iter_97_4 ~= iter_97_2 and iter_97_4 ~= point1Index and iter_97_4 ~= point2Index and triangleCandidate:contains(tempPoints[iter_97_4]) then
								anotherPointInTriangleFound = true

								break
							end
						end

						if not anotherPointInTriangleFound then
							ove_0_22(arg_97_0.triangles, triangleCandidate)
							ove_0_23(tempPoints, iter_97_2)

							iter_97_2 = iter_97_2 - 1
							triangleFound = true
						end
					end
				end
			end

			if #tempPoints == 3 then
				ove_0_22(arg_97_0.triangles, ove_0_34:new(tempPoints[1], tempPoints[2], tempPoints[3]))
			end
		elseif #arg_97_0.points == 3 then
			ove_0_22(arg_97_0.triangles, arg_97_0)
		end
	end

	return arg_97_0.triangles
end

function ove_0_34.intersects(arg_98_0, arg_98_1)
	for iter_98_0, iter_98_1 in ipairs(arg_98_0:getLineSegments()) do
		for iter_98_2, iter_98_3 in ipairs(arg_98_1:getLineSegments()) do
			if iter_98_1:intersects(iter_98_3) then
				return true
			end
		end
	end

	return false
end

function ove_0_34.distanceTo(arg_99_0, arg_99_1)
	local slot_99_0

	for iter_99_0, iter_99_1 in ipairs(arg_99_0:getLineSegments()) do
		distance = iter_99_1:distanceTo(arg_99_1)

		if slot_99_0 == nil or slot_99_0 >= distance then
			slot_99_0 = distance
		end
	end

	return slot_99_0
end

function ove_0_34.__tostring(arg_100_0)
	local slot_100_0 = "Polygon("

	for iter_100_0, iter_100_1 in ipairs(arg_100_0.points) do
		if iter_100_0 == 1 then
			slot_100_0 = slot_100_0 .. tostring(iter_100_1)
		else
			slot_100_0 = slot_100_0 .. ", " .. tostring(iter_100_1)
		end
	end

	return slot_100_0 .. ")"
end

function ove_0_34.draw(arg_101_0, arg_101_1)
	for iter_101_0, iter_101_1 in ipairs(arg_101_0:getLineSegments()) do
		iter_101_1:draw(arg_101_1)
	end
end

return {
	Vector = ove_0_30,
	Circle = ove_0_32,
	LineSegment = ove_0_33,
	Polygon = ove_0_34
}

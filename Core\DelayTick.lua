local delayTick = {}
delayTick.tickNumber = 0

-- this DelayTick system basic from OneKeyToWin AIO, credit by <PERSON><PERSON>
-- this is a really good fps control system, i let it to free user FPS

function delayTick.OnlyTickOnce()
    if delayTick.tickNumber == 2 then
        return true
    end
    return false
end

function delayTick.CanTickEvent()
    if delayTick.tickNumber == 0 or delayTick.tickNumber == 2 or delayTick.tickNumber == 4 then
        return true
    end
    return false
end

function delayTick.CanDelayTick() -- FIX SOME WAY ERROR AND IDK WHERE
    if delayTick.tickNumber == 0 or delayTick.tickNumber == 2 or delayTick.tickNumber == 4 then
        return true
    end
    return false
end

local function OnMyTick()
    delayTick.tickNumber = delayTick.tickNumber + 1
    if delayTick.tickNumber > 4 then
        delayTick.tickNumber = 0
    end
end

cb.add(cb.tick, OnMyTick)

return delayTick

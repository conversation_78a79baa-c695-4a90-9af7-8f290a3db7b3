# Xerath Q技能线圈功能说明

## 功能概述
为Xerath的Q技能添加了可选的线圈显示功能，用户可以通过菜单开关来控制是否显示Q技能的范围线圈。

## 功能特性

### 1. 菜单开关
- **中文**: "显示Q技能线圈" (默认启用)
- **英文**: "Show Q Circle" (默认启用)
- **位置**: 绘制设置 / Draw Settings 部分
- **提示**: 显示Q技能的范围线圈 / Display Q skill range circle

### 2. 线圈显示
当开关启用且Q技能可用时，会显示以下线圈：

#### 最小范围线圈 (红色)
- **颜色**: 红色 (RGB: 255, 102, 102)
- **范围**: 750 单位 (Q技能最小射程)
- **线宽**: 2 像素

#### 最大范围线圈 (红色)
- **颜色**: 红色 (RGB: 255, 102, 102)
- **范围**: 1500 单位 (Q技能最大射程)
- **线宽**: 2 像素

#### 当前充能范围线圈 (红色)
- **颜色**: 红色 (RGB: 255, 102, 102)
- **范围**: 根据Q技能充能时间动态变化
- **线宽**: 3 像素
- **显示条件**: 仅在Q技能正在充能时显示

## 技术实现

### 修改的文件
1. `xerath/menu.lua` - 添加菜单开关
2. `xerath/q_draw.lua` - 实现线圈绘制逻辑

### 代码逻辑
- 检查菜单开关状态
- 验证玩家在屏幕上且Q技能可用
- 绘制最小和最大范围线圈
- 如果Q技能正在充能，额外绘制当前充能范围

### 性能优化
- 只在必要时进行绘制
- 使用高效的条件检查
- 保持与现有绘制系统的兼容性

## 使用方法
1. 进入游戏后打开Xerath脚本菜单
2. 找到"绘制设置"或"Draw Settings"部分
3. 切换"显示Q技能线圈"/"Show Q Circle"开关
4. 线圈将根据设置实时显示或隐藏

## 注意事项
- 线圈仅在Q技能可用时显示
- 充能线圈仅在Q技能充能过程中显示
- 功能与现有的其他绘制功能完全兼容
- 不影响脚本的其他功能和性能

local VeigarPlugin = {}


local TS = module.internal("TS")
local orb = module.internal("orb")
local preds = module.internal("pred")
local DelayAction = module.load("<PERSON>", "Core/DelayAction")
local DelayTick = module.load("<PERSON>", "Core/DelayTick")
local Prediction = module.load("<PERSON>", "Core/Prediction")
local BuffManager = module.load("<PERSON>", "Library/BuffManager")
local CalculateManager = module.load("<PERSON>", "Library/CalculateManager")
--local FarmManager = module.load("<PERSON>", "Library/FarmManager")
local ItemManager = module.load("<PERSON>", "Library/ItemManager")
local NetManager = module.load("<PERSON>", "Library/NetManager")
local ObjectManager = module.load("<PERSON>", "Library/ObjectManager")
local OrbManager = module.load("Brian", "Library/OrbManager")
local SpellManager = module.load("<PERSON>", "Library/SpellManager")
local VectorManager = module.load("<PERSON>", "Library/VectorManager")
local MyCommon = module.load("<PERSON>", "Library/ExtraManager")
local common = module.load("<PERSON>", "Utility/common")
local ui = module.load("<PERSON>", "ui");
local Curses = module.load("Brian", "Curses");
--Spell values.

local spellQ = {
	range = 900,
	speed = 2000,
	delay = 0.25,
	width = 70,
	boundingRadiusMod = 1,
	collision = {
		hero = false,
		minion = true,
		wall = true
	}
}

local spellW = {
	range = 900,
	speed = math.huge,
	delay = 1.35,
	radius = 170,
	boundingRadiusMod = 0
}

local spellE = {
	range = 725, -- true range 750, circle 400 divided by 2 and added 750 base range = 950 (technically)
	speed = math.huge,
	delay = 0.75,
	radius = 375,
	boundingRadiusMod = 0
}

local spellR = {
	range = 650
}


-- R Interrupt

local interruptableSpells = {
	["anivia"] = {
		{menuslot = "R", slot = 3, spellname = "glacialstorm", channelduration = 6}
	},
	["caitlyn"] = {
		{menuslot = "R", slot = 3, spellname = "caitlynaceinthehole", channelduration = 1}
	},
	["ezreal"] = {
		{menuslot = "R", slot = 3, spellname = "ezrealtrueshotbarrage", channelduration = 1}
	},
	["fiddlesticks"] = {
		{menuslot = "W", slot = 1, spellname = "drain", channelduration = 5},
		{menuslot = "R", slot = 3, spellname = "crowstorm", channelduration = 1.5}
	},
	["gragas"] = {
		{menuslot = "W", slot = 1, spellname = "gragasw", channelduration = 0.75}
	},
	["janna"] = {
		{menuslot = "R", slot = 3, spellname = "reapthewhirlwind", channelduration = 3}
	},
	["karthus"] = {
		{menuslot = "R", slot = 3, spellname = "karthusfallenone", channelduration = 3}
	}, --common.IsValidTargetTarget will prevent from casting @ karthus while he's zombie
	["katarina"] = {
		{menuslot = "R", slot = 3, spellname = "katarinar", channelduration = 2.5}
	},
	["lucian"] = {
		{menuslot = "R", slot = 3, spellname = "lucianr", channelduration = 2}
	},
	["lux"] = {
		{menuslot = "R", slot = 3, spellname = "luxmalicecannon", channelduration = 0.5}
	},
	["malzahar"] = {
		{menuslot = "R", slot = 3, spellname = "malzaharr", channelduration = 2.5}
	},
	["masteryi"] = {
		{menuslot = "W", slot = 1, spellname = "meditate", channelduration = 4}
	},
	["missfortune"] = {
		{menuslot = "R", slot = 3, spellname = "missfortunebullettime", channelduration = 3}
	},
	["nunu"] = {
		{menuslot = "R", slot = 3, spellname = "absolutezero", channelduration = 3}
	},
	--excluding Orn's Forge Channel since it can be cancelled just by attacking him
	["pantheon"] = {
		{menuslot = "R", slot = 3, spellname = "pantheonrjump", channelduration = 2}
	},
	["shen"] = {
		{menuslot = "R", slot = 3, spellname = "shenr", channelduration = 3}
	},
	["twistedfate"] = {
		{menuslot = "R", slot = 3, spellname = "gate", channelduration = 1.5}
	},
	["varus"] = {
		{menuslot = "Q", slot = 0, spellname = "varusq", channelduration = 4}
	},
	["warwick"] = {
		{menuslot = "R", slot = 3, spellname = "warwickr", channelduration = 1.5}
	},
	["xerath"] = {
		{menuslot = "R", slot = 3, spellname = "xerathlocusofpower2", channelduration = 3}
	}
}

function VeigarPlugin.Load(GlobalMenu)
    MyMenu = GlobalMenu

    MyMenu.Key:keybind("manu", "Manual E", "T", nil)
    MyMenu.Key:boolean("lastq", "Use Last Hit", true)
    MyMenu.Key:keybind("StartE", "Start Combo With E", false, "K")
    MyMenu.Key:keybind("last", "Last Hit Q", "X", "I")

    MyMenu:menu("combo", "Combo Settings")
    MyMenu.combo:header("xd", "Q Settings")
    MyMenu.combo:boolean("qcombo", "Use Q", true)
    MyMenu.combo:header("xd", "W Settings")
    MyMenu.combo:boolean("wcombo", "Use W", true)
    MyMenu.combo:dropdown("modee", "Choose Mode: ", 1, {"Enemy CC", "Always"})
    MyMenu.combo:boolean("pol", "Consider Polymorph/Slow for Enemy CC", false)
    MyMenu.combo:header("xd", "E Settings")
    MyMenu.combo:boolean("ecombo", "Use E", true)

    MyMenu:menu("harass", "Harass Settings")
    MyMenu.harass:header("xd", "Harass Settings")
    MyMenu.harass:boolean("qharass", "Use Q", true)
    MyMenu.harass:slider("manaharass", "Min. Mana Percent: ", 10, 0, 100, 10)

    MyMenu:menu("laneclear", "Lane Clear Settings")
    MyMenu.laneclear:header("xd", "Lane Clear Settings")
    MyMenu.laneclear:boolean("q", "Use Q", true)
    MyMenu.laneclear:dropdown("mode", "Choose Mode: ", 1, {"To Push", "To Stack"})
    MyMenu.laneclear:slider("manalane", "Min. Mana Percent: ", 10, 0, 100, 10)

    MyMenu:menu("killsteal", "Killsteal Settings")
    MyMenu.killsteal:header("xd", "KillSteal Settings")
    MyMenu.killsteal:boolean("uks", "Use Killsteal", true)
    MyMenu.killsteal:boolean("qkill", "Use Q to Killsteal", true)
    MyMenu.killsteal:boolean("rkill", "Use R to Killsteal", true)

    --[[
    MyMenu:menu("misc", "Misc Settings")
    MyMenu.misc:boolean("egap", "Use E to Gapclose", false)
    --]]

    MyMenu:menu("draws", "Draw Settings")
    MyMenu.draws:boolean("q", "Draw Q Range", true)
    MyMenu.draws:color("colorq", "^ color", 255, 233, 121, 121)
    MyMenu.draws:boolean("w", "Draw W Range", true)
    MyMenu.draws:color("colorw", "^ color", 255, 233, 121, 121)
    MyMenu.draws:boolean("e", "Draw E Range", true)
    MyMenu.draws:color("colore", "^ color", 255, 233, 121, 121)
    MyMenu.draws:boolean("r", "Draw R Range", true)
    MyMenu.draws:color("colorr", "^ color", 255, 233, 121, 121)
end



local TargetSelection = function(res, obj, dist)
	if dist <= spellQ.range then
		res.obj = obj
		return true
	end
end

local GetTarget = function()
	return TS.get_result(TargetSelection).obj
end



-- Calculating Damage

local function calc_r_bonus(h, m)
	if h and m then
		if 1 - (h / m) > 0.66 then
			return 1
		else
			return 1.5 * (1-(h / m))
		end
	end
	return 1
end

local RLevelDamage = {175, 250, 325}
function RDamage(target)
	if player:spellSlot(3).level > 0 then
		damage = (RLevelDamage[player:spellSlot(3).level] + (common.GetTotalAP() * .74)) * (1 + calc_r_bonus(target.health, target.maxHealth))	
	end
	return common.CalculateMagicDamage(target, damage)
end

local QLevelDamage = {70, 110, 150, 190, 230}
function QDamage(target)
	local damage = 0
	if player:spellSlot(0).level > 0 then
		damage =
			common.CalculateMagicDamage(
			target,
			(QLevelDamage[player:spellSlot(0).level] + (common.GetTotalAP(player) * 0.6)),
			player
		)
	end
	return damage
end
local WLevelDamage = {100, 150, 200, 250, 300}
function WDamage(target)
	local damage = 0

	if player:spellSlot(1).level > 0 then
		damage =
			common.CalculateMagicDamage(target, (WLevelDamage[player:spellSlot(1).level] + (common.GetTotalAP() * 1.0)), player)
	end
	return damage
end

-- Modes

local function smartE(target)
  --if player.path.serverPos:distSqr(target.path.serverPos) < (spellE.range * spellE.range) then
    --local res = preds.circular.get_prediction(spellE, target)
    if res then
      local x = res.endPos.x
      local y = res.endPos.y

      if x < player.x then
         x = x + 220
      elseif x > player.x then
          x = x - 220
      end

      if y < player.z then
          y = y + 220
      elseif y > player.z then
          y = y - 220
      end
      player:castSpell("pos", 2, vec3(x, target.y, y))
    --end
  end
end

local function Combo()
	local target = GetTarget()
	if MyMenu.combo.ecombo:get() then
		if player:spellSlot(2).state == 0 then
			if common.IsValidTarget(target) and target then
				if (target.pos:dist(player) <= spellE.range) then
					local pos = preds.circular.get_prediction(spellE, target)
					if pos and pos.startPos:dist(pos.endPos) <= spellE.range then
						player:castSpell("pos", 2, vec3(pos.endPos.x, mousePos.y, pos.endPos.y))
					end
				end
			end
		end
	end
	if MyMenu.Key.StartE:get() and player:spellSlot(2).state == 0 then return end
	if MyMenu.combo.wcombo:get() then
		if MyMenu.combo.modee:get() == 2 then
			if player:spellSlot(1).state == 0 then
				if common.IsValidTarget(target) and target then
					if (target.pos:dist(player) <= spellW.range) then
						local pos = preds.circular.get_prediction(spellW, target)
						if pos and pos.startPos:dist(pos.endPos) <= spellW.range then
							player:castSpell("pos", 1, vec3(pos.endPos.x, mousePos.y, pos.endPos.y))
						end
					end
				end
			end
		end
	end
	if MyMenu.combo.qcombo:get() then
		if player:spellSlot(0).state == 0 then
			if common.IsValidTarget(target) and target then
				if (target.pos:dist(player) <= spellQ.range) then
					local pos = preds.linear.get_prediction(spellQ, target)
					if pos and pos.startPos:dist(pos.endPos) <= spellQ.range then
						if not preds.collision.get_prediction(spellQ, pos, target) then
							player:castSpell("pos", 0, vec3(pos.endPos.x, game.mousePos.y, pos.endPos.y))
						else
							if table.getn(preds.collision.get_prediction(spellQ, pos, target)) == 1 then
								player:castSpell("pos", 0, vec3(pos.endPos.x, mousePos.y, pos.endPos.y))
							end
						end
					end
				end
			end
		end
	end
end

local function LastHit()
	if (player.mana / player.maxMana) * 100 >= MyMenu.laneclear.manalane:get() and player:spellSlot(0).state == 0 then
	   for i = 0, objManager.minions.size[TEAM_ENEMY] - 1 do
	   local minion = objManager.minions[TEAM_ENEMY][i]
	       if minion and minion.isVisible and minion.moveSpeed > 0 and minion.isTargetable and not minion.isDead and common.IsValidTarget(minion) then
			   local minionPos = vec3(minion.x, minion.y, minion.z)
			   delay = 0.25 + player.pos:dist(minion.pos) / 3000
			   if minionPos then 
			   		local seg = preds.linear.get_prediction(spellQ, minion)
			        if seg and seg.startPos:dist(seg.endPos) < spellQ.range and not preds.collision.get_prediction(spellQ, seg, minion)then
					    if (QDamage(minion) >= orb.farm.predict_hp(minion, delay / 2, true) - 100) then
						    orb.core.set_pause_attack(1)
					    end
						if (QDamage(minion) >= orb.farm.predict_hp(minion, delay / 2, true)) then
							player:castSpell("pos", 0, vec3(seg.endPos.x, minionPos.y, seg.endPos.y))
						end
					end
			   end
		   end
	   end
	end
end

local function LaneClear()
	if (player.mana / player.maxMana) * 100 >= MyMenu.laneclear.manalane:get() and player:spellSlot(0).state == 0 then
   		local enemyMinionsQ = common.GetMinionsInRange(spellQ.range, TEAM_ENEMY)
   		for i = 0, objManager.minions.size[TEAM_ENEMY] - 1 do
   			local minion = objManager.minions[TEAM_ENEMY][i]
   			if minion and not minion.isDead and common.IsValidTarget(minion) then
   				local minion = objManager.minions[TEAM_ENEMY][i]
	 			if minion and minion.pos:dist(player.pos) <= spellQ.range and MyMenu.laneclear.mode:get() == 1 and not minion.isDead and common.IsValidTarget(minion) then
	 				local minionPos = vec3(minion.x, minion.y, minion.z)
		   			if minionPos then
		   				local seg = preds.linear.get_prediction(spellQ, minion)
				 		if seg and seg.startPos:dist(seg.endPos) < spellQ.range and not preds.collision.get_prediction(spellQ, seg, minion) then
				 			player:castSpell("pos", 0, vec3(seg.endPos.x, minionPos.y, seg.endPos.y))
				 		end
		   			end
     			end
				if minion and minion.pos:dist(player.pos) <= spellQ.range and MyMenu.laneclear.mode:get() == 2 and not minion.isDead and common.IsValidTarget(minion) then
				 	local minionPos = vec3(minion.x, minion.y, minion.z)
				 	delay = 0.25 + player.pos:dist(minion.pos) / 3000
					if minionPos then
					    local seg = preds.linear.get_prediction(spellQ, minion)
						if (QDamage(minion) >= orb.farm.predict_hp(minion, delay / 2, true) - 150) and not preds.collision.get_prediction(spellQ, seg, minion) then
					         orb.core.set_pause_attack(1)
				        end
					    if (QDamage(minion) >= orb.farm.predict_hp(minion, delay / 2, true)) and not preds.collision.get_prediction(spellQ, seg, minion)then
						     player:castSpell("pos", 0, vec3(seg.endPos.x, minionPos.y, seg.endPos.y))
						end
					end
		        end
   			end
   		end
	end
end

local function Harass()
	local target = GetTarget()
	if not common.IsValidTarget(target) then
		return
	end
	if MyMenu.harass.qharass:get() and player:spellSlot(0).state == 0 then
		if (target.pos:dist(player) < spellQ.range) then
			local pos = preds.linear.get_prediction(spellQ, target)
			if pos and pos.startPos:dist(pos.endPos) <= spellQ.range then
				if not preds.collision.get_prediction(spellQ, pos, target) then
					player:castSpell("pos", 0, vec3(pos.endPos.x, game.mousePos.y, pos.endPos.y))
				else
					if table.getn(preds.collision.get_prediction(spellQ, pos, target)) == 1 then
						player:castSpell("pos", 0, vec3(pos.endPos.x, mousePos.y, pos.endPos.y))
					end
				end
			end
		end
	end
end

local function KillSteal()
	for i = 0, objManager.enemies_n - 1 do
		local enemies = objManager.enemies[i]
		if enemies and common.IsValidTarget(enemies) and not enemies.buff["sionpassivezombie"] then
			local hp = common.GetShieldedHealth("ap", enemies)
			if MyMenu.killsteal.rkill:get() and not enemies.buff["undyingrage"] and not enemies.buff["kindredrnodeathbuff"] and not enemies.buff["willrevive"] then
				if player:spellSlot(3).state == 0 and vec3(enemies.x, enemies.y, enemies.z):dist(player) < spellR.range and RDamage(enemies) > enemies.health then
					player:castSpell("obj", 3, enemies)
				end
			end
			if MyMenu.killsteal.qkill:get() then
				if player:spellSlot(0).state == 0 and vec3(enemies.x, enemies.y, enemies.z):dist(player) < spellQ.range and QDamage(enemies) > hp then
					local pos = preds.linear.get_prediction(spellQ, enemies)
					if pos and pos.startPos:dist(pos.endPos) < spellQ.range then
						player:castSpell("pos", 0, vec3(pos.endPos.x, mousePos.y, pos.endPos.y))
					end
				end
			end
		end
	end
end


--[[
local function egap()
	if MyMenu.misc.egap:get() then
		local seg = {}
		local target =
			TS.get_result(
			function(res, obj, dist)
				if dist <= spellE.range and obj.path.isActive and obj.path.isDashing then
					res.obj = obj
					return true
				end
			end
		).obj
		if target then
			local pred_pos = preds.core.lerp(target.path, network.latency, target.path.dashSpeed)
			if pred_pos and pred_pos:dist(player.path.serverPos2D) <= spellE.range then
				seg.startPos = player.path.serverPos2D
				seg.endPos = vec2(pred_pos.x, pred_pos.y)
					player:castSpell("pos", 2, target.pos)
			end
		end
	end
end
--]]


local function launchE(pos)
	if pos then
		player:castSpell("pos", 1, vec3(pos.x, pos.y, pos.z));
		orb.core.set_server_pause();
	end
end


local function AutoW()
	if orb.core.is_paused() then return end
	if player:spellSlot(1).state == 0 then
		for i = 0, objManager.enemies_n - 1 do
			local enemy = objManager.enemies[i]
			if enemy and common.IsValidTarget(enemy) and not enemy.buff["sionpassivezombie"] then
				if not MyMenu.combo.pol:get() then
					if enemy.buff[5] or enemy.buff[11] or enemy.buff[24] or enemy.buff[29] then
						if enemy.pos:dist(player.pos) < 900 then
							launchE(enemy.pos)
						end
					end
				elseif MyMenu.combo.pol:get() then
					if enemy.buff[5] or enemy.buff[11] or enemy.buff[24] or enemy.buff[29] or enemy.buff[9] or enemy.buff[10] then
						if enemy.pos:dist(player.pos) < 900 then
							launchE(enemy.pos)
						end
					end
				end
			end
		end
	end
end

local function AutoE()
	if orb.core.is_paused() then return end
	if player:spellSlot(2).state == 0 then
		for i = 0, objManager.enemies_n - 1 do
			local enemy = objManager.enemies[i]
			if enemy and common.IsValidTarget(enemy) and not enemy.buff["sionpassivezombie"] and enemy.pos:dist(player.pos) < spellE.range then
				if enemy.buff[5] or enemy.buff[11] or enemy.buff[24] or enemy.buff[29] then
					if enemy.pos:dist(player.pos) < spellE.range then
						smartE(enemy)
					end
				end
			end
		end
	end
end

local function rPerc(target)
	if RDamage(target) > target.health then
		return 101
	else
		return math.floor( (RDamage(target) / target.health) * 100)
	end
end

function DrawDamagesE(target)
	if target.isVisible and not target.isDead then
		local pos = graphics.world_to_screen(target.pos)
		if (math.floor((RDamage(target)) / target.health * 100) < 100) then
			graphics.draw_text_2D(tostring(math.floor((RDamage(target)) / target.health * 100)) .."% " .. "Not Killable",14,pos.x + 55,pos.y - 80,graphics.argb(255, 255, 255, 255))
		end
		if (math.floor((RDamage(target)) / target.health * 100) >= 100) then
			graphics.draw_text_2D("Killable",14,pos.x + 55,pos.y - 80,graphics.argb(255, 255, 255, 255))
		end
	end
end

local function OnDraw()
	if player.isOnScreen then
		if MyMenu.draws.q:get() and player:spellSlot(0).state == 0 then
			graphics.draw_circle(player.pos, spellQ.range, 1, MyMenu.draws.colorq:get(), 50)
        end
        if MyMenu.draws.w:get() and player:spellSlot(0).state == 0 then
			graphics.draw_circle(player.pos, spellW.range, 1, MyMenu.draws.colorw:get(), 50)
        end
        if MyMenu.draws.e:get() and player:spellSlot(0).state == 0 then
			graphics.draw_circle(player.pos, spellE.range, 1, MyMenu.draws.colore:get(), 50)
		end
		if MyMenu.draws.r:get() and player:spellSlot(3).state == 0 then
			graphics.draw_circle(player.pos, spellR.range, 1, MyMenu.draws.colorr:get(), 50)
		end
		if MyMenu.draws.r:get() and player:spellSlot(3).state == 0 then
			for i = 0, objManager.enemies_n - 1 do
				local enemy = objManager.enemies[i]
				if enemy and common.IsValidTarget(enemy) and enemy.isOnScreen then
					DrawDamagesE(enemy)
				end
			end
		end
	end
end

local function OnTick()
	if MyMenu.combo.modee:get() == 1 then
		AutoW()
	end
	AutoE()
	if MyMenu.killsteal.uks:get() then
		KillSteal()
	end
	if MyMenu.Key.LaneClear:get() then
		LaneClear()
	end
	if MyMenu.Key.Harass:get() then
		Harass()
	end
	if MyMenu.Key.Combo:get() then
		Combo()
	end
	if MyMenu.Key.lastq:get() and MyMenu.Key.last:get() and not MyMenu.Key.Combo:get() then
		LastHit()
	end
	if MyMenu.Key.manu:get() then
		smartE(target)
	end
end
cb.add(cb.draw, OnDraw)

orb.combat.register_f_pre_tick(OnTick)

return VeigarPlugin
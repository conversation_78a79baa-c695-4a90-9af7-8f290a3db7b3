

	local slot_16_54 = module.internal("pred")
	local slot_16_55 = module.internal("orb")
	local slot_16_56 = module.seek("evade")
	local slot_16_58 = {}
	local slot_16_59 = {}
	local slot_16_60 = {
		300,
		350,
		400,
		450,
		500
	}

	local function slot_16_61(arg_24_0, arg_24_1, arg_24_2, arg_24_3, arg_24_4)
		
	end

	local slot_16_62 = {}
	local slot_16_63

	local function slot_16_64(arg_25_0, arg_25_1, arg_25_2)
		if not slot_16_63 then
			function slot_16_63()
				for iter_26_0, iter_26_1 in pairs(slot_16_62) do
					if iter_26_0 <= os.clock() then
						for iter_26_2 = 1, #iter_26_1 do
							local slot_26_0 = iter_26_1[iter_26_2]

							if slot_26_0 and slot_26_0.func then
								slot_26_0.func(unpack(slot_26_0.args or {}))
							end
						end

						slot_16_62[iter_26_0] = nil
					end
				end
			end

			cb.add(cb.tick, slot_16_63)
		end

		local slot_25_0 = os.clock() + (arg_25_1 or 0)

		if slot_16_62[slot_25_0] then
			slot_16_62[slot_25_0][#slot_16_62[slot_25_0] + 1] = {
				func = arg_25_0,
				args = arg_25_2
			}
		else
			slot_16_62[slot_25_0] = {
				{
					func = arg_25_0,
					args = arg_25_2
				}
			}
		end
	end

	local function slot_16_65(arg_27_0)
		local slot_27_0 = {
			"ward",
			"trink",
			"trap",
			"spear",
			"device",
			"room",
			"box",
			"plant",
			"poo",
			"barrel",
			"god",
			"feather"
		}

		for iter_27_0 = 1, #slot_27_0 do
			if arg_27_0 and arg_27_0.name:lower():find(slot_27_0[iter_27_0]) then
				return true
			end
		end
	end

	local function slot_16_66(arg_28_0, arg_28_1)
		if not arg_28_0 then
			return
		end

		for iter_28_0 = 0, arg_28_0.buffManager.count - 1 do
			local slot_28_0 = arg_28_0.buffManager:get(iter_28_0)

			if slot_28_0 and slot_28_0.valid and slot_28_0.endTime >= game.time and slot_28_0.stacks > 0 then
				if type(arg_28_1) == "number" and slot_28_0.type == arg_28_1 then
					return slot_28_0
				elseif type(arg_28_1) == "string" and slot_28_0.name:lower() == arg_28_1 then
					return slot_28_0
				end
			end
		end
	end

	local function slot_16_67(arg_29_0, arg_29_1)
		if arg_29_0 and arg_29_0.buffManager and arg_29_0.buffManager.count > 0 then
			for iter_29_0 = 0, arg_29_0.buffManager.count - 1 do
				local slot_29_0 = arg_29_0.buffManager:get(iter_29_0)

				if slot_29_0 and slot_29_0.valid and slot_29_0.type == arg_29_1 and (slot_29_0.stacks > 0 or slot_29_0.stacks2 > 0) then
					return true
				end
			end
		end
	end

	local function slot_16_68(arg_30_0)
		for iter_30_0 = 0, objManager.turrets.size[TEAM_ENEMY] - 1 do
			local slot_30_0 = objManager.turrets[TEAM_ENEMY][iter_30_0]

			if slot_30_0 and not slot_30_0.isDead and slot_30_0.pos:distSqr(arg_30_0) <= 810000 then
				return true
			end
		end

		return false
	end

	local function slot_16_69(arg_31_0, arg_31_1)
		for iter_31_0 = 0, objManager.turrets.size[TEAM_ALLY] - 1 do
			local slot_31_0 = objManager.turrets[TEAM_ALLY][iter_31_0]

			if slot_31_0 and slot_31_0.type == TYPE_TURRET and not slot_31_0.isDead and slot_31_0.pos:distSqr(arg_31_0) <= arg_31_1^2 then
				return slot_31_0.pos
			end
		end
	end

	local function slot_16_70(arg_32_0, arg_32_1)
		for iter_32_0 = 0, objManager.enemies_n - 1 do
			local slot_32_0 = objManager.enemies[iter_32_0]

			if slot_32_0 and slot_32_0 ~= Source and not slot_32_0.isDead and slot_32_0.isVisible and slot_32_0.isTargetable and slot_32_0.team ~= TEAM_ALLY and slot_32_0.pos:distSqr(arg_32_0.pos) < arg_32_1^2 and (arg_32_0.networkID ~= slot_32_0.networkID or arg_32_0.name ~= slot_32_0.name) then
				return slot_32_0
			end
		end
	end

	local function slot_16_71(arg_33_0, arg_33_1)
		local slot_33_0 = 0

		for iter_33_0 = 0, objManager.enemies_n - 1 do
			local slot_33_1 = objManager.enemies[iter_33_0]

			if slot_33_1 and not slot_33_1.isDead and slot_33_1.isVisible and slot_33_1.isTargetable and slot_33_1.team ~= TEAM_ALLY and slot_33_1.pos:distSqr(arg_33_0) < arg_33_1^2 then
				slot_33_0 = slot_33_0 + 1
			end
		end

		return slot_33_0
	end

	local function slot_16_72(arg_34_0, arg_34_1)
		local slot_34_0 = 0

		for iter_34_0 = 0, objManager.allies_n - 1 do
			local slot_34_1 = objManager.allies[iter_34_0]

			if slot_34_1 and slot_34_1 ~= player and not slot_34_1.isDead and slot_34_1.isVisible and slot_34_1.isTargetableToTeamFlags and slot_34_1.pos:distSqr(arg_34_0) <= arg_34_1^2 then
				slot_34_0 = slot_34_0 + 1
			end
		end

		return slot_34_0
	end

	local function slot_16_73(arg_35_0, arg_35_1)
		for iter_35_0 = 0, objManager.allies_n - 1 do
			local slot_35_0 = objManager.allies[iter_35_0]

			if slot_35_0 and slot_35_0 ~= player and not slot_35_0.isDead and slot_35_0.isVisible and slot_35_0.isTargetableToTeamFlags and slot_35_0.pos:distSqr(arg_35_0) <= arg_35_1^2 and (player.networkID ~= slot_35_0.networkID or player.name ~= slot_35_0.name) then
				return slot_35_0
			end
		end
	end

	local function slot_16_74(arg_36_0, arg_36_1, arg_36_2)
		local slot_36_0 = 0

		for iter_36_0 = 0, objManager.enemies_n - 1 do
			local slot_36_1 = objManager.enemies[iter_36_0]

			if slot_36_1 and slot_36_1 ~= arg_36_2 and not slot_36_1.isDead and slot_36_1.isVisible and slot_36_1.isTargetable and slot_36_1.team ~= TEAM_ALLY and slot_36_1.pos:distSqr(arg_36_0) < arg_36_1^2 then
				slot_36_0 = slot_36_0 + 1
			end
		end

		return slot_36_0
	end

	local function slot_16_75(arg_37_0, arg_37_1)
		local slot_37_0 = 0
		local slot_37_1 = objManager.minions

		for iter_37_0 = 0, slot_37_1.size[TEAM_ENEMY] - 1 do
			local slot_37_2 = slot_37_1[TEAM_ENEMY][iter_37_0]

			if slot_37_2 and not slot_37_2.isDead and slot_37_2.isVisible and slot_37_2.isTargetable and slot_37_2.pos:distSqr(arg_37_0) < arg_37_1^2 and not slot_16_65(slot_37_2) then
				slot_37_0 = slot_37_0 + 1
			end
		end

		return slot_37_0
	end

	local function slot_16_76(arg_38_0, arg_38_1)
		local slot_38_0 = 0
		local slot_38_1 = objManager.minions

		for iter_38_0 = 0, slot_38_1.size[TEAM_NEUTRAL] - 1 do
			local slot_38_2 = slot_38_1[TEAM_NEUTRAL][iter_38_0]

			if slot_38_2 and not slot_38_2.isDead and slot_38_2.isVisible and slot_38_2.isTargetable and slot_38_2.pos:distSqr(arg_38_0) < arg_38_1^2 and not slot_16_65(slot_38_2) then
				slot_38_0 = slot_38_0 + 1
			end
		end

		return slot_38_0
	end

	local function slot_16_77(arg_39_0)
		if arg_39_0 and not arg_39_0.isDead and arg_39_0.isTargetable and arg_39_0.isVisible then
			return true
		end
	end

	local function slot_16_78(arg_40_0)
		if not arg_40_0.buff.sionpassivezombie and not arg_40_0.buff.chronoshift and not arg_40_0.buff.kindredrnodeathbuff and not arg_40_0.buff.undyingrage and not arg_40_0.buff.fioraw and not arg_40_0.buff.sivire and not arg_40_0.buff.kayler and not arg_40_0.buff.pantheone then
			return true
		end

		return false
	end

	local function slot_16_79(arg_41_0)
		if arg_41_0.polygon and arg_41_0.polygon:Contains(player.pos) ~= 0 then
			for iter_41_0 = 1, #slot_16_58 do
				local slot_41_0 = slot_16_58[iter_41_0]

				if slot_41_0 and arg_41_0.polygon and arg_41_0.polygon:Contains(slot_41_0.pos) ~= 0 then
					return true
				end
			end
		end

		return false
	end

	local function slot_16_80()
		local slot_42_0

		for iter_42_0 = 4, 5 do
			if player:spellSlot(iter_42_0).name:lower():find("summonerflash") then
				slot_42_0 = iter_42_0
			end
		end

		return slot_42_0
	end

	local function slot_16_81(arg_43_0)
		return player.attackRange + (arg_43_0 and arg_43_0.boundingRadius or 0)
	end

	local function slot_16_82(arg_44_0, arg_44_1)
		return arg_44_0.attackRange + arg_44_0.boundingRadius + (arg_44_1 and arg_44_1.boundingRadius or 0)
	end

	local function slot_16_83(arg_45_0, arg_45_1)
		local slot_45_0 = 0

		if arg_45_0 == "AD" then
			slot_45_0 = arg_45_1.physicalShield
		elseif arg_45_0 == "AP" then
			slot_45_0 = arg_45_1.magicalShield
		elseif arg_45_0 == "ALL" then
			slot_45_0 = arg_45_1.allShield
		end

		return arg_45_1.health + slot_45_0
	end

	local function slot_16_84(arg_46_0, arg_46_1)
		if not slot_16_77(arg_46_0) or not arg_46_0.path or not arg_46_1 or not arg_46_0.moveSpeed then
			return arg_46_0
		end

		local slot_46_0 = slot_16_54.core.lerp(arg_46_0.path, network.latency + arg_46_1, arg_46_0.moveSpeed)

		return vec3(slot_46_0.x, player.y, slot_46_0.y)
	end

	local function slot_16_85(arg_47_0)
		local slot_47_0 = 55 + 25 * player.levelRef

		if arg_47_0 then
			slot_47_0 = slot_47_0 - (slot_16_83("AD", arg_47_0) - arg_47_0.health)
		end

		return slot_47_0
	end

	local function slot_16_86(arg_48_0)
		if arg_48_0.charName == "Aatrox" or arg_48_0.charName == "Akali" or arg_48_0.charName == "DrMundo" or arg_48_0.charName == "Garen" or arg_48_0.charName == "Gnar" or arg_48_0.charName == "Katarina" or arg_48_0.charName == "Kennen" or arg_48_0.charName == "Kled" or arg_48_0.charName == "LeeSin" or arg_48_0.charName == "RekSai" or arg_48_0.charName == "Mordekaiser" or arg_48_0.charName == "Renekton" or arg_48_0.charName == "Rengar" or arg_48_0.charName == "Riven" or arg_48_0.charName == "Rumble" or arg_48_0.charName == "Sett" or arg_48_0.charName == "Shen" or arg_48_0.charName == "Shyvana" or arg_48_0.charName == "Tryndamere" or arg_48_0.charName == "Viego" or arg_48_0.charName == "Vladimir" or arg_48_0.charName == "Yasuo" or arg_48_0.charName == "Yone" or arg_48_0.charName == "Zac" or arg_48_0.charName == "Zed" then
			return true
		end

		return false
	end

	local function slot_16_87(arg_49_0)
		if arg_49_0.levelRef >= 18 then
			return 18
		end

		return arg_49_0.levelRef
	end

	local function slot_16_88()
		return {
			galio = {
				{
					buffname = "galiow",
					channelduration = 3,
					slot = 1,
					menuslot = "W",
					spellname = "galiow"
				}
			},
			akshan = {
				{
					buffname = "akshanr",
					channelduration = 2.5,
					slot = 3,
					menuslot = "R",
					spellname = "akshanr"
				}
			},
			caitlyn = {
				{
					channelduration = 1,
					spellname = "caitlynr",
					slot = 3,
					menuslot = "R"
				}
			},
			fiddlesticks = {
				{
					channelduration = 2,
					spellname = "fiddlesticksw",
					slot = 1,
					menuslot = "W"
				},
				{
					channelduration = 1.5,
					spellname = "fiddlesticksr",
					slot = 3,
					menuslot = "R"
				}
			},
			irelia = {
				{
					channelduration = 1.5,
					spellname = "ireliaw",
					slot = 1,
					menuslot = "W"
				}
			},
			janna = {
				{
					buffname = "reapthewhirlwind",
					channelduration = 3,
					slot = 3,
					menuslot = "R",
					spellname = "reapthewhirlwind"
				}
			},
			karthus = {
				{
					channelduration = 3,
					spellname = "karthusfallenone",
					slot = 3,
					menuslot = "R"
				}
			},
			katarina = {
				{
					channelduration = 2.5,
					spellname = "katarinar",
					slot = 3,
					menuslot = "R"
				}
			},
			lucian = {
				{
					channelduration = 3,
					spellname = "lucianr",
					slot = 3,
					menuslot = "R"
				}
			},
			malzahar = {
				{
					channelduration = 2.5,
					spellname = "malzaharr",
					slot = 3,
					menuslot = "R"
				}
			},
			masteryi = {
				{
					channelduration = 4,
					spellname = "meditate",
					slot = 1,
					menuslot = "W"
				}
			},
			missfortune = {
				{
					channelduration = 3,
					spellname = "missfortunebullettime",
					slot = 3,
					menuslot = "R"
				}
			},
			nunu = {
				{
					channelduration = 3,
					spellname = "nunur",
					slot = 3,
					menuslot = "R"
				}
			},
			tahmkench = {
				{
					channelduration = 1.35,
					spellname = "tahmkenchw",
					slot = 1,
					menuslot = "W"
				}
			},
			pantheon = {
				{
					channelduration = 2,
					spellname = "pantheonr",
					slot = 3,
					menuslot = "R"
				},
				{
					channelduration = 4,
					spellname = "pantheonq",
					slot = 0,
					menuslot = "Q"
				}
			},
			poppy = {
				{
					channelduration = 4,
					spellname = "poppyr",
					slot = 3,
					menuslot = "R"
				}
			},
			quinn = {
				{
					channelduration = 2,
					spellname = "quinr",
					slot = 3,
					menuslot = "R"
				}
			},
			shen = {
				{
					channelduration = 3,
					spellname = "shenr",
					slot = 3,
					menuslot = "R"
				}
			},
			twistedfate = {
				{
					channelduration = 1.5,
					spellname = "gate",
					slot = 3,
					menuslot = "R"
				}
			},
			varus = {
				{
					channelduration = 4,
					spellname = "varusq",
					slot = 0,
					menuslot = "Q"
				}
			},
			velkoz = {
				{
					channelduration = 2.5,
					spellname = "velkozr",
					slot = 3,
					menuslot = "R"
				}
			},
			warwick = {
				{
					channelduration = 1.5,
					spellname = "warwickrchannel",
					slot = 3,
					menuslot = "R"
				}
			},
			xerath = {
				{
					channelduration = 3,
					spellname = "xeratharcanopulsechargeup",
					slot = 0,
					menuslot = "Q"
				},
				{
					channelduration = 10,
					spellname = "xerathlocusofpower2",
					slot = 3,
					menuslot = "R"
				}
			},
			zac = {
				{
					channelduration = 4,
					spellname = "zace",
					slot = 2,
					menuslot = "E"
				}
			},
			jhin = {
				{
					channelduration = 10,
					spellname = "jhinr",
					slot = 3,
					menuslot = "R"
				}
			},
			pyke = {
				{
					channelduration = 3,
					spellname = "pykeq",
					slot = 0,
					menuslot = "Q"
				}
			},
			vi = {
				{
					channelduration = 4,
					spellname = "viq",
					slot = 0,
					menuslot = "Q"
				}
			},
			rammus = {
				{
					channelduration = 6,
					spellname = "powerball",
					slot = 0,
					menuslot = "Q"
				}
			},
			sion = {
				{
					channelduration = 2,
					spellname = "sionq",
					slot = 0,
					menuslot = "Q"
				}
			},
			seraphine = {
				{
					channelduration = 0.5,
					spellname = "seraphiner",
					slot = 3,
					menuslot = "R"
				}
			},
			samira = {
				{
					channelduration = 2,
					spellname = "samirar",
					slot = 3,
					menuslot = "R"
				}
			}
		}
	end

	local function slot_16_89(arg_51_0)
		if slot_16_55.core.can_attack() == false or not slot_16_81(arg_51_0) or slot_16_55.farm.predict_hp(arg_51_0, slot_16_55.utility.get_hit_time(player, arg_51_0) - os.clock()) <= 0 then
			return true
		end

		return false
	end

	local function slot_16_90()
		local slot_52_0

		if player.buff.srx_dragonbuffocean then
			slot_52_0 = 1
		end

		if player.buff.srx_dragonbuffcloud then
			slot_52_0 = slot_52_0 + 1
		end

		if player.buff.srx_dragonbuffinfernal then
			slot_52_0 = slot_52_0 + 1
		end

		if player.buff.srx_dragonbuffmountain then
			slot_52_0 = slot_52_0 + 1
		end

		if player.buff.srx_dragonbuffchemtech then
			slot_52_0 = slot_52_0 + 1
		end

		return slot_52_0
	end

	local function slot_16_91(arg_53_0)
		return (arg_53_0.baseAttackDamage + arg_53_0.flatPhysicalDamageMod) * arg_53_0.percentPhysicalDamageMod
	end

	local function slot_16_92(arg_54_0)
		return (arg_54_0.baseAttackDamage + arg_54_0.flatPhysicalDamageMod) * arg_54_0.percentPhysicalDamageMod - arg_54_0.baseAttackDamage
	end

	local function slot_16_93(arg_55_0)
		return (arg_55_0.baseAttackDamage + arg_55_0.flatPhysicalDamageMod) * arg_55_0.percentPhysicalDamageMod - arg_55_0.baseAttackDamage
	end

	local function slot_16_94(arg_56_0)
		return arg_56_0.flatMagicDamageMod * arg_56_0.percentMagicDamageMod
	end

	local function slot_16_95()
		return {
			caitlyn = {
				{
					buffname = "none",
					time = 1.375,
					menuslot = "R",
					slot = _R
				}
			},
			fiddlesticks = {
				{
					buffname = "none",
					time = 2,
					menuslot = "W",
					slot = _W
				},
				{
					buffname = "none",
					time = 1.5,
					menuslot = "R",
					slot = _R
				}
			},
			janna = {
				{
					buffname = "reapthewhirlwind",
					menuslot = "R",
					slot = _R
				}
			},
			karthus = {
				{
					buffname = "karthusfallenonecastsound",
					menuslot = "R",
					slot = _R
				}
			},
			katarina = {
				{
					buffname = "katarinarsound",
					menuslot = "R",
					slot = _R
				}
			},
			malzahar = {
				{
					buffname = "malzaharrsound",
					menuslot = "R",
					slot = _R
				}
			},
			masteryi = {
				{
					buffname = "meditate",
					menuslot = "W",
					slot = _W
				}
			},
			missfortune = {
				{
					buffname = "none",
					time = 3,
					menuslot = "R",
					slot = _R
				}
			},
			nunu = {
				{
					buffname = "none",
					time = 3,
					menuslot = "R",
					slot = _R
				}
			},
			pantheon = {
				{
					buffname = "pantheonr",
					menuslot = "R",
					slot = _R
				}
			},
			quinn = {
				{
					buffname = "none",
					time = 2,
					menuslot = "R",
					slot = _R
				}
			},
			shen = {
				{
					buffname = "shenrchannelmanager",
					menuslot = "R",
					slot = _R
				}
			},
			irelia = {
				{
					buffname = "ireliawdefense",
					menuslot = "W",
					slot = _W
				}
			},
			twistedfate = {
				{
					buffname = "gate",
					menuslot = "R",
					slot = _R
				}
			},
			velkoz = {
				{
					buffname = "velkozr",
					menuslot = "R",
					slot = _R
				}
			},
			warwick = {
				{
					buffname = "warwickrsound",
					menuslot = "R",
					slot = _R
				}
			},
			xerath = {
				{
					buffname = "xerathlocusofpower2",
					menuslot = "R",
					slot = _R
				}
			},
			jhin = {
				{
					buffname = "none",
					time = 1,
					menuslot = "R",
					slot = _R
				}
			}
		}
	end

	local function slot_16_96()
		return {
			ezreal = {
				{
					slot = 2,
					menuslot = "E"
				}
			},
			annie = {
				{
					slot = 0,
					menuslot = "Q"
				}
			},
			anivia = {
				{
					slot = 2,
					menuslot = "E"
				}
			},
			brand = {
				{
					slot = 3,
					menuslot = "R"
				}
			},
			caitlyn = {
				{
					slot = -1,
					menuslot = "P"
				},
				{
					slot = 3,
					menuslot = "R"
				}
			},
			sylas = {
				{
					slot = 3,
					menuslot = "R"
				}
			},
			kassadin = {
				{
					slot = 0,
					menuslot = "Q"
				}
			},
			cassiopeia = {
				{
					slot = 2,
					menuslot = "E"
				}
			},
			gangplank = {
				{
					slot = 0,
					menuslot = "Q"
				}
			},
			janna = {
				{
					slot = 1,
					menuslot = "W"
				}
			},
			jhin = {
				{
					slot = 0,
					menuslot = "Q"
				}
			},
			katarina = {
				{
					slot = 0,
					menuslot = "Q"
				}
			},
			leblanc = {
				{
					slot = 0,
					menuslot = "Q"
				}
			},
			nautilus = {
				{
					slot = 3,
					menuslot = "R"
				}
			},
			lulu = {
				{
					slot = 1,
					menuslot = "W"
				}
			},
			malphite = {
				{
					slot = 0,
					menuslot = "Q"
				}
			},
			missfortune = {
				{
					slot = 0,
					menuslot = "Q"
				}
			},
			nami = {
				{
					slot = 1,
					menuslot = "W"
				}
			},
			ryze = {
				{
					slot = 2,
					menuslot = "E"
				}
			},
			shaco = {
				{
					slot = 2,
					menuslot = "E"
				}
			},
			syndra = {
				{
					slot = 3,
					menuslot = "R"
				}
			},
			teemo = {
				{
					slot = 0,
					menuslot = "Q"
				}
			},
			tristana = {
				{
					slot = 2,
					menuslot = "E"
				},
				{
					slot = 3,
					menuslot = "R"
				}
			},
			twistedfate = {
				{
					slot = 1,
					menuslot = "W"
				}
			},
			twitch = {
				{
					slot = 2,
					menuslot = "E"
				}
			},
			vayne = {
				{
					slot = 2,
					menuslot = "E"
				}
			},
			veigar = {
				{
					slot = 3,
					menuslot = "R"
				}
			},
			viktor = {
				{
					slot = 0,
					menuslot = "Q"
				}
			}
		}
	end

	local function slot_16_97()
		return {
			alistar = {
				{
					slot = 1,
					menuslot = "W"
				}
			},
			ezreal = {
				{
					slot = 2,
					menuslot = "E"
				}
			},
			annie = {
				{
					slot = 0,
					menuslot = "Q"
				}
			},
			akali = {
				{
					slot = 2,
					menuslot = "E"
				}
			},
			ekko = {
				{
					slot = 2,
					menuslot = "E"
				}
			},
			anivia = {
				{
					slot = 2,
					menuslot = "E"
				}
			},
			sett = {
				{
					slot = 3,
					menuslot = "R"
				}
			},
			blitzcrank = {
				{
					slot = 2,
					menuslot = "E"
				}
			},
			brand = {
				{
					slot = 2,
					menuslot = "E"
				},
				{
					slot = 3,
					menuslot = "R"
				}
			},
			caitlyn = {
				{
					slot = 3,
					menuslot = "R"
				}
			},
			rell = {
				{
					slot = 1,
					menuslot = "W"
				}
			},
			camille = {
				{
					slot = 0,
					menuslot = "Q"
				},
				{
					slot = 3,
					menuslot = "R"
				}
			},
			cassiopeia = {
				{
					slot = 2,
					menuslot = "E"
				}
			},
			chogath = {
				{
					slot = 3,
					menuslot = "R"
				}
			},
			darius = {
				{
					slot = 1,
					menuslot = "W"
				},
				{
					slot = 3,
					menuslot = "R"
				}
			},
			diana = {
				{
					slot = 2,
					menuslot = "E"
				}
			},
			elise = {
				{
					slot = 0,
					menuslot = "Q"
				}
			},
			evelynn = {
				{
					slot = 2,
					menuslot = "E"
				}
			},
			fiddlesticks = {
				{
					slot = 0,
					menuslot = "Q"
				}
			},
			fizz = {
				{
					slot = 0,
					menuslot = "Q"
				}
			},
			gangplank = {
				{
					slot = 0,
					menuslot = "Q"
				}
			},
			garen = {
				{
					slot = 0,
					menuslot = "Q"
				},
				{
					slot = 3,
					menuslot = "R"
				}
			},
			hecarim = {
				{
					slot = 2,
					menuslot = "E"
				}
			},
			irelia = {
				{
					slot = 0,
					menuslot = "Q"
				}
			},
			janna = {
				{
					slot = 1,
					menuslot = "W"
				}
			},
			jarvaniv = {
				{
					slot = 3,
					menuslot = "R"
				}
			},
			jax = {
				{
					slot = 0,
					menuslot = "Q"
				}
			},
			kayn = {
				{
					slot = 3,
					menuslot = "R"
				}
			},
			jayce = {
				{
					slot = 0,
					menuslot = "Q"
				},
				{
					slot = 2,
					menuslot = "E"
				}
			},
			jhin = {
				{
					slot = 0,
					menuslot = "Q"
				}
			},
			kalista = {
				{
					slot = 2,
					menuslot = "E"
				}
			},
			karma = {
				{
					slot = 1,
					menuslot = "W"
				}
			},
			katarina = {
				{
					slot = 0,
					menuslot = "Q"
				}
			},
			kennen = {
				{
					slot = 1,
					menuslot = "W"
				}
			},
			khazix = {
				{
					slot = 0,
					menuslot = "Q"
				}
			},
			sejuani = {
				{
					slot = 2,
					menuslot = "E"
				}
			},
			leblanc = {
				{
					slot = 0,
					menuslot = "Q"
				}
			},
			leesin = {
				{
					slot = 3,
					menuslot = "R"
				},
				{
					slot = 0,
					menuslot = "Q"
				}
			},
			leona = {
				{
					slot = 0,
					menuslot = "Q"
				}
			},
			lissandra = {
				{
					slot = 3,
					menuslot = "R"
				}
			},
			nautilus = {
				{
					slot = 3,
					menuslot = "R"
				}
			},
			lulu = {
				{
					slot = 1,
					menuslot = "W"
				},
				{
					slot = 2,
					menuslot = "E"
				}
			},
			malphite = {
				{
					slot = 0,
					menuslot = "Q"
				}
			},
			malzahar = {
				{
					slot = 2,
					menuslot = "E"
				},
				{
					slot = 3,
					menuslot = "R"
				}
			},
			maokai = {
				{
					slot = 1,
					menuslot = "W"
				}
			},
			missfortune = {
				{
					slot = 0,
					menuslot = "Q"
				}
			},
			morgana = {
				{
					slot = 3,
					menuslot = "R"
				}
			},
			nidalee = {
				{
					slot = 0,
					menuslot = "Q"
				}
			},
			nami = {
				{
					slot = 1,
					menuslot = "W"
				}
			},
			nasus = {
				{
					slot = 0,
					menuslot = "Q"
				},
				{
					slot = 1,
					menuslot = "W"
				}
			},
			nocturne = {
				{
					slot = 2,
					menuslot = "E"
				}
			},
			olaf = {
				{
					slot = 2,
					menuslot = "E"
				}
			},
			pantheon = {
				{
					slot = 1,
					menuslot = "W"
				}
			},
			poppy = {
				{
					slot = 2,
					menuslot = "E"
				}
			},
			quinn = {
				{
					slot = 2,
					menuslot = "E"
				}
			},
			masteryi = {
				{
					slot = 0,
					menuslot = "Q"
				}
			},
			rammus = {
				{
					slot = 2,
					menuslot = "E"
				}
			},
			renekton = {
				{
					slot = 1,
					menuslot = "W"
				}
			},
			monkeyking = {
				{
					slot = 0,
					menuslot = "Q"
				},
				{
					slot = 2,
					menuslot = "E"
				}
			},
			mordekaiser = {
				{
					slot = 3,
					menuslot = "R"
				}
			},
			yorick = {
				{
					slot = 0,
					menuslot = "Q"
				}
			},
			rengar = {
				{
					slot = 0,
					menuslot = "Q"
				}
			},
			ryze = {
				{
					slot = 1,
					menuslot = "W"
				},
				{
					slot = 2,
					menuslot = "E"
				}
			},
			shaco = {
				{
					slot = 2,
					menuslot = "E"
				}
			},
			singed = {
				{
					slot = 2,
					menuslot = "E"
				}
			},
			skarner = {
				{
					slot = 3,
					menuslot = "R"
				}
			},
			sylas = {
				{
					slot = 3,
					menuslot = "R"
				},
				{
					slot = 1,
					menuslot = "W"
				}
			},
			syndra = {
				{
					slot = 3,
					menuslot = "R"
				}
			},
			tahmkench = {
				{
					slot = 3,
					menuslot = "R"
				}
			},
			talon = {
				{
					slot = 0,
					menuslot = "Q"
				}
			},
			teemo = {
				{
					slot = 0,
					menuslot = "Q"
				}
			},
			tristana = {
				{
					slot = 3,
					menuslot = "R"
				}
			},
			trundle = {
				{
					slot = 0,
					menuslot = "Q"
				},
				{
					slot = 3,
					menuslot = "R"
				}
			},
			twistedfate = {
				{
					slot = 1,
					menuslot = "W"
				}
			},
			twitch = {
				{
					slot = 2,
					menuslot = "E"
				}
			},
			udyr = {
				{
					slot = 2,
					menuslot = "E"
				}
			},
			vayne = {
				{
					slot = 2,
					menuslot = "E"
				}
			},
			veigar = {
				{
					slot = 3,
					menuslot = "R"
				}
			},
			vi = {
				{
					slot = 3,
					menuslot = "R"
				}
			},
			vex = {
				{
					slot = 3,
					menuslot = "R"
				}
			},
			viktor = {
				{
					slot = 0,
					menuslot = "Q"
				}
			},
			vladimir = {
				{
					slot = 0,
					menuslot = "Q"
				}
			},
			volibear = {
				{
					slot = 0,
					menuslot = "Q"
				},
				{
					slot = 1,
					menuslot = "W"
				}
			},
			warwick = {
				{
					slot = 0,
					menuslot = "Q"
				}
			},
			xinzhao = {
				{
					slot = 0,
					menuslot = "Q"
				},
				{
					slot = 2,
					menuslot = "E"
				}
			},
			yasuo = {
				{
					slot = 2,
					menuslot = "E"
				}
			},
			zed = {
				{
					slot = 3,
					menuslot = "R"
				}
			}
		}
	end

	local function slot_16_98(arg_60_0)
		local slot_60_0 = {}
		local slot_60_1 = objManager.minions

		for iter_60_0 = 0, slot_60_1.size[TEAM_ENEMY] - 1 do
			local slot_60_2 = slot_60_1[TEAM_ENEMY][iter_60_0]

			if slot_60_2 and not slot_60_2.isDead and slot_60_2.moveSpeed > 0 and slot_60_2.isTargetable and slot_60_2.isVisible and slot_60_2.type == TYPE_MINION and player.pos:distSqr(slot_60_2) <= arg_60_0^2 then
				slot_60_0[#slot_60_0 + 1] = slot_60_2
			end
		end

		return slot_60_0
	end

	local function slot_16_99(arg_61_0)
		local slot_61_0 = 0
		local slot_61_1 = objManager.minions

		for iter_61_0 = 0, slot_61_1.size[TEAM_ENEMY] - 1 do
			local slot_61_2 = slot_61_1[TEAM_ENEMY][iter_61_0]

			if slot_61_2 and not slot_61_2.isDead and slot_61_2.moveSpeed > 0 and slot_61_2.isTargetable and slot_61_2.isVisible and slot_61_2.type == TYPE_MINION and player.pos:distSqr(slot_61_2) <= arg_61_0^2 then
				slot_61_0 = slot_61_0 + 1
			end
		end

		return slot_61_0
	end

	local function slot_16_100(arg_62_0, arg_62_1)
		local slot_62_0 = 0
		local slot_62_1 = objManager.minions

		for iter_62_0 = 0, slot_62_1.size[TEAM_ENEMY] - 1 do
			local slot_62_2 = slot_62_1[TEAM_ENEMY][iter_62_0]

			if slot_62_2 and not slot_62_2.isDead and slot_62_2.moveSpeed > 0 and slot_62_2.isTargetable and slot_62_2.isVisible and slot_62_2.type == TYPE_MINION and arg_62_0.pos:distSqr(slot_62_2) <= arg_62_1^2 then
				slot_62_0 = slot_62_0 + 1
			end
		end

		return slot_62_0
	end

	local function slot_16_101(arg_63_0)
		local slot_63_0 = {}
		local slot_63_1 = objManager.minions

		for iter_63_0 = 0, slot_63_1.size[TEAM_NEUTRAL] - 1 do
			local slot_63_2 = slot_63_1[TEAM_NEUTRAL][iter_63_0]

			if slot_63_2 and not slot_63_2.isDead and slot_63_2.moveSpeed > 0 and slot_63_2.isTargetable and slot_63_2.isVisible and slot_63_2.type == TYPE_MINION and player.pos:distSqr(slot_63_2) <= arg_63_0^2 then
				slot_63_0[#slot_63_0 + 1] = slot_63_2
			end
		end

		return slot_63_0
	end

	local function slot_16_102(arg_64_0)
		local slot_64_0 = {}
		local slot_64_1 = objManager.minions

		for iter_64_0 = 0, slot_64_1.size[TEAM_ALLY] - 1 do
			local slot_64_2 = slot_64_1[TEAM_ALLY][iter_64_0]

			if slot_64_2 and not slot_64_2.isDead and slot_64_2.moveSpeed > 0 and slot_64_2.isTargetable and slot_64_2.isVisible and slot_64_2.type == TYPE_MINION and player.pos:distSqr(slot_64_2) <= arg_64_0^2 then
				slot_64_0[#slot_64_0 + 1] = slot_64_2
			end
		end

		return slot_64_0
	end

	local function slot_16_103(arg_65_0, arg_65_1)
		local slot_65_0 = objManager.minions

		for iter_65_0 = 0, slot_65_0.size[TEAM_ALLY] - 1 do
			local slot_65_1 = slot_65_0[TEAM_ALLY][iter_65_0]

			if slot_65_1 and not slot_65_1.isDead and slot_65_1.isVisible and slot_65_1.isTargetableToTeamFlags and slot_65_1.pos:distSqr(arg_65_0) < 490000 and slot_65_1.pos:distSqr(mousePos) <= arg_65_1^2 and not slot_16_65(slot_65_1) then
				return slot_65_1
			end
		end
	end

	local function slot_16_104()
		local slot_66_0 = {
			TrinketTotemLvl4 = true,
			VisionWard = true,
			TrinketTotemLvl1 = true,
			ItemGhostWard = true,
			TrinketTotemLvl3 = true
		}

		for iter_66_0 = 6, 12 do
			local slot_66_1 = player:spellSlot(iter_66_0)

			if slot_66_1.isNotEmpty and slot_66_1.state == 0 and slot_66_0[slot_66_1.name] then
				return iter_66_0
			end
		end

		for iter_66_1 = 6, 12 do
			local slot_66_2 = player:spellSlot(iter_66_1)

			if slot_66_2.isNotEmpty and slot_66_2.state == 0 and slot_66_2.name == "JammerDevice" then
				return iter_66_1
			end
		end

		return nil
	end

	local function slot_16_105(arg_67_0)
		if arg_67_0.buff[BUFF_INVULNERABILITY] or arg_67_0.buff[BUFF_SPELLSHIELD] then
			return true
		else
			return false
		end
	end

	local function slot_16_106(arg_68_0)
		local slot_68_0 = 0

		for iter_68_0, iter_68_1 in pairs(arg_68_0.buff) do
			if iter_68_1.valid and slot_68_0 < iter_68_1.endTime - game.time and game.time - iter_68_1.startTime > 0.1 and (iter_68_1.type == BUFF_STUN or iter_68_1.type == BUFF_SUPPRESSION or iter_68_1.type == BUFF_SNARE or iter_68_1.type == BUFF_KNOCKUP or iter_68_1.type == BUFF_ASLEEP) then
				slot_68_0 = iter_68_1.endTime - game.time
			end
		end

		return slot_68_0
	end

	local function slot_16_107(arg_69_0)
		if arg_69_0.buff.threshq and not arg_69_0.path.isActive then
			return 0
		end

		local slot_69_0 = 0

		for iter_69_0, iter_69_1 in pairs(arg_69_0.buff) do
			if iter_69_1.valid and slot_69_0 < iter_69_1.endTime - game.time and game.time - iter_69_1.startTime > 0.1 and (iter_69_1.type == BUFF_STUN or iter_69_1.type == BUFF_TAUNT or iter_69_1.type == BUFF_SUPPRESSION or iter_69_1.type == BUFF_SNARE or iter_69_1.type == BUFF_CHARM or iter_69_1.type == BUFF_FLEE or iter_69_1.type == BUFF_KNOCKUP or iter_69_1.type == BUFF_KNOCKBACK or iter_69_1.type == BUFF_ASLEEP or iter_69_1.type == BUFF_FEAR) then
				slot_69_0 = iter_69_1.endTime - game.time
			end
		end

		return slot_69_0
	end

	local function slot_16_108(arg_70_0)
		if slot_16_67(arg_70_0, BUFF_STUN) or slot_16_67(arg_70_0, BUFF_TAUNT) or slot_16_67(arg_70_0, BUFF_SUPPRESSION) or slot_16_67(arg_70_0, BUFF_SNARE) or slot_16_67(arg_70_0, BUFF_CHARM) or slot_16_67(arg_70_0, BUFF_FEAR) then
			return true
		end

		return false
	end

	local function slot_16_109(arg_71_0)
		if arg_71_0 == BUFF_STUN or arg_71_0 == BUFF_TAUNT or arg_71_0 == BUFF_SUPPRESSION or arg_71_0 == BUFF_SNARE or arg_71_0 == BUFF_CHARM or arg_71_0 == BUFF_FEAR or arg_71_0 == BUFF_SLOW or arg_71_0 == BUFF_KNOCKUP or arg_71_0 == BUFF_KNOCKBACK or arg_71_0 == BUFF_SILENCE or arg_71_0 == BUFF_POLYMORPH or arg_71_0 == BUFF_BLIND or arg_71_0 == BUFF_DROWSY then
			return true
		end

		return false
	end

	local function slot_16_110(arg_72_0)
		if arg_72_0.buff[BUFF_STUN] or arg_72_0.buff[BUFF_TAUNT] or arg_72_0.buff[BUFF_SUPPRESSION] or arg_72_0.buff[BUFF_SNARE] or arg_72_0.buff[BUFF_CHARM] or arg_72_0.buff[BUFF_FEAR] then
			return true
		end

		return false
	end

	local function slot_16_111(arg_73_0)
		if arg_73_0.isRecalling and arg_73_0.buff.recall then
			return game.time - arg_73_0.buff.recall.startTime > 0.3
		end

		return false
	end

	local function slot_16_112(arg_74_0)
		local slot_74_0 = false

		if arg_74_0.isRecalling and not arg_74_0.buff.recall and not arg_74_0.buff.superrecall and not arg_74_0.buff.odinrecall and not arg_74_0.buff.odinrecallimproved then
			slot_74_0 = arg_74_0.buff.recallimproved
		end

		return slot_74_0
	end

	local function slot_16_113(arg_75_0)
		if arg_75_0.buff.morganae or arg_75_0.buff.fioraw then
			return false
		end

		return true
	end

	local function slot_16_114(arg_76_0, arg_76_1, arg_76_2)
		if slot_16_54.trace.linear.hardlock(arg_76_0, arg_76_1, arg_76_2) then
			return true
		end

		if slot_16_54.trace.linear.hardlockmove(arg_76_0, arg_76_1, arg_76_2) then
			return true
		end

		if arg_76_1.startPos:dist(arg_76_1.endPos) <= 550 then
			return true
		end

		if slot_16_54.trace.newpath(arg_76_2, 0.033, 0.5) then
			return true
		end

		if slot_16_107(arg_76_2) > 0.2 then
			return true
		end
	end

	local function slot_16_115(arg_77_0, arg_77_1, arg_77_2, arg_77_3)
		if slot_16_54.trace.linear.hardlock(arg_77_0, arg_77_1, arg_77_2) then
			return true
		end

		if slot_16_54.trace.linear.hardlockmove(arg_77_0, arg_77_1, arg_77_2) then
			return true
		end

		if arg_77_3 >= arg_77_1.startPos:dist(arg_77_1.endPos) then
			return true
		end

		if slot_16_54.trace.newpath(arg_77_2, 0.033, 0.5) then
			return true
		end

		if slot_16_107(arg_77_2) > 0.2 then
			return true
		end
	end

	local function slot_16_116(arg_78_0, arg_78_1, arg_78_2)
		if not slot_16_56 or slot_16_56.menu.input_blocking ~= nil then
			return true
		end

		return slot_16_56.core.is_action_safe(arg_78_0, arg_78_1, arg_78_2)
	end

	local function slot_16_117(arg_79_0, arg_79_1)
		if not slot_16_77(arg_79_0) or not arg_79_0.path or not arg_79_0.path.isActive or not arg_79_0.moveSpeed then
			return false
		end

		local slot_79_0 = slot_16_54.core.lerp(arg_79_0.path, network.latency + 0.25, arg_79_0.moveSpeed)

		return vec3(slot_79_0.x, arg_79_0.y, slot_79_0.y):dist(arg_79_1.pos) > arg_79_0.pos:dist(arg_79_1.pos)
	end

	local function slot_16_118(arg_80_0)
		return slot_16_117(arg_80_0, player)
	end

	local function slot_16_119(arg_81_0, arg_81_1, arg_81_2)
		return (arg_81_2.y - arg_81_0.y) * (arg_81_1.x - arg_81_0.x) - (arg_81_1.y - arg_81_0.y) * (arg_81_2.x - arg_81_0.x)
	end

	local function slot_16_120(arg_82_0, arg_82_1, arg_82_2, arg_82_3)
		return (slot_16_119(arg_82_0, arg_82_2, arg_82_3) <= 0 and slot_16_119(arg_82_1, arg_82_2, arg_82_3) > 0 or slot_16_119(arg_82_0, arg_82_2, arg_82_3) > 0 and slot_16_119(arg_82_1, arg_82_2, arg_82_3) <= 0) and (slot_16_119(arg_82_0, arg_82_1, arg_82_2) <= 0 and slot_16_119(arg_82_0, arg_82_1, arg_82_3) > 0 or slot_16_119(arg_82_0, arg_82_1, arg_82_2) > 0 and slot_16_119(arg_82_0, arg_82_1, arg_82_3) <= 0)
	end

	local function slot_16_121(arg_83_0, arg_83_1)
		local slot_83_0 = vec2(arg_83_0.x, arg_83_0.z)
		local slot_83_1 = arg_83_1:to2D()
		local slot_83_2 = false

		for iter_83_0 in pairs(slot_16_59) do
			local slot_83_3 = slot_16_59[iter_83_0]

			if slot_83_3 then
				local slot_83_4 = slot_16_60[slot_83_3.spell.owner:spellSlot(1).level] / 2
				local slot_83_5 = slot_83_3 + (slot_83_3.startPos - slot_83_3):norm():perp2() * slot_83_4
				local slot_83_6 = slot_83_3 + (slot_83_3.startPos - slot_83_3):norm():perp2() * -slot_83_4
				local slot_83_7 = (slot_83_6:to2D() - slot_83_5:to2D()):norm():perp1()
				local slot_83_8 = slot_83_5:to2D() - slot_83_7 * 75
				local slot_83_9 = slot_83_5:to2D() + slot_83_7 * 75
				local slot_83_10 = slot_83_6:to2D() + slot_83_7 * 75
				local slot_83_11 = slot_83_6:to2D() - slot_83_7 * 75

				if slot_16_120(slot_83_8, slot_83_9, slot_83_0, slot_83_1) or slot_16_120(slot_83_8, slot_83_11, slot_83_0, slot_83_1) or slot_16_120(slot_83_10, slot_83_9, slot_83_0, slot_83_1) or slot_16_120(slot_83_10, slot_83_11, slot_83_0, slot_83_1) then
					return true
				end
			end
		end

		return slot_83_2
	end

	local function slot_16_122(arg_84_0)
		if arg_84_0 and arg_84_0.team ~= TEAM_ALLY and arg_84_0.spell.name == "YasuoW_VisualMis" then
			slot_16_59[arg_84_0.ptr] = arg_84_0
		end
	end

	local function slot_16_123(arg_85_0)
		if arg_85_0 and arg_85_0.ptr and slot_16_59[arg_85_0.ptr] then
			slot_16_59[arg_85_0.ptr] = nil
		end
	end

	cb.add(cb.create_missile, slot_16_122)
	cb.add(cb.delete_missile, slot_16_123)

	return {
		CountJarvenInQ = slot_16_79,
		havebuff = slot_16_66,
		CheckBuffType = slot_16_67,
		GetEnemiesObj = slot_16_70,
		CountEnemiesNear = slot_16_71,
		CountEnemiesNear2 = slot_16_74,
		CountEnemiesMinions = slot_16_75,
		CountAllyNear = slot_16_72,
		CountAllyObj = slot_16_73,
		CountJungles = slot_16_76,
		UnderTurret = slot_16_68,
		DelayAction = slot_16_64,
		isValid = slot_16_77,
		Undeadbuff = slot_16_78,
		WardName = slot_16_65,
		wall_check = slot_16_121,
		GetFlashNum = slot_16_80,
		GetPlayerAARange = slot_16_81,
		GetObjAARange = slot_16_82,
		GetShieldedHealth = slot_16_83,
		GetPredictedPos = slot_16_84,
		GetIgniteDamage = slot_16_85,
		IsNoMana = slot_16_86,
		GetLevel = slot_16_87,
		GetInterruptableSpells = slot_16_88,
		isUnkillable = slot_16_89,
		DragonCount = slot_16_90,
		GetTotalAD = slot_16_91,
		GetBonusAD = slot_16_93,
		GetTotalAP = slot_16_94,
		GetChannelNotMove = slot_16_95,
		GetMinion = slot_16_98,
		GetMinionCount = slot_16_99,
		GetMinionCount2 = slot_16_100,
		GetJungleMinion = slot_16_101,
		GetMinionsAlly = slot_16_102,
		GetMinionsAllyObj = slot_16_103,
		IsSpellShield = slot_16_105,
		GetImmobileTimeHard = slot_16_106,
		GetImmobileTime = slot_16_107,
		isCCDelay = slot_16_108,
		isCCType = slot_16_109,
		isCC = slot_16_110,
		IsRecalling = slot_16_111,
		RecallActive = slot_16_112,
		CanBeCCd = slot_16_113,
		trace_filter = slot_16_114,
		trace_filter2 = slot_16_115,
		TargetedSpellsSamira = slot_16_96,
		TargetedSpells = slot_16_97,
		drawCircle = slot_16_61,
		IsSafePos = slot_16_116,
		isFleeing = slot_16_117,
		isFleeingFromMe = slot_16_118,
		WardSlot = slot_16_104,
		GetAllyTurretPos = slot_16_69
	}

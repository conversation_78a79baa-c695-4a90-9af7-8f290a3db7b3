local ove_0_10 = module.load("Kloader", "Lib/DelayAction")
local ove_0_11 = player
local ove_0_12 = {
	speed = 1750,
	range = 700,
	LastCastTime = 0,
	width = 250,
	Slot = player:spellSlot(1)
}

function ove_0_12.Cast1(arg_5_0)
	-- print 5
	if not ove_0_12.Ready() or ove_0_12.IsW2() then
		return
	end

	if game.time * 1000 - ove_0_12.LastCastTime > 175 then
		player:castSpell("pos", 1, arg_5_0)

		ove_0_12.LastCastTime = game.time * 1000
	end
end

function ove_0_12.Cast2()
	-- print 6
	if not ove_0_12.Ready() or ove_0_12.IsW1() then
		return
	end

	player:castSpell("self", 1)
end

function ove_0_12.Cast(arg_7_0)
	-- print 7
	if not ove_0_12.Ready() or navmesh.isWall(arg_7_0) then
		return
	end

	player:castSpell("pos", 1, arg_7_0)
end

function ove_0_12.Ready()
	-- print 8
	return ove_0_12.Slot.state == 0
end

function ove_0_12.IsW1()
	-- print 9
	return ove_0_12.Slot.name == "ZedW"
end

function ove_0_12.IsW2()
	-- print 10
	return ove_0_12.Slot.name == "ZedW2"
end

function ove_0_12.Cost()
	-- print 11
	return ove_0_11.manaCost1
end

function ove_0_12.Level()
	-- print 12
	return player:spellSlot(1).level
end

return ove_0_12

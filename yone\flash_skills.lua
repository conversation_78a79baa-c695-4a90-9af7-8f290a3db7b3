local ove_0_10 = module.internal("TS")
local ove_0_11 = module.internal("orb")
local ove_0_12 = module.internal("pred")
local ove_0_13 = module.load(header.id, "yone/menu")
local ove_0_14 = module.load(header.id, "common/turret") -- 引入turret模块，用于检查防御塔
local delayAction = module.load(header.id, "common/delayAction") -- 引入delayAction模块

-- 获取闪现槽位
local flash_slot = nil
local flash_slot_pos = nil

for i = 4, 5 do
    local spell = player:spellSlot(i)
    if spell.isNotEmpty and spell.name:lower():find("flash") then
        flash_slot = i
        flash_slot_pos = spell
        break
    end
end

-- 检查玩家是否在防御塔下
local function is_under_enemy_turret()
    if not ove_0_13.flash_skill.flash_check_turret:get() then
        return false
    end
    
    -- 简化版的防御塔检测，使用守卫样本点
    local turret_positions = {
        -- 蓝方防御塔位置
        {pos = vec3(981, 10500, 1163)},    -- 蓝方上路外塔
        {pos = vec3(1512, 6700, 1346)},    -- 蓝方上路内塔
        {pos = vec3(5048, 13450, 1174)},   -- 蓝方中路外塔
        {pos = vec3(7943, 13411, 1736)},   -- 蓝方中路内塔
        {pos = vec3(10504, 1030, 1308)},   -- 蓝方下路外塔
        {pos = vec3(6919, 1483, 1274)},    -- 蓝方下路内塔
        
        -- 红方防御塔位置
        {pos = vec3(4318, 13875, 1009)},   -- 红方上路外塔
        {pos = vec3(7943, 13411, 1736)},   -- 红方上路内塔
        {pos = vec3(9767, 10113, 1234)},   -- 红方中路外塔
        {pos = vec3(11134, 8370, 1602)},   -- 红方中路内塔
        {pos = vec3(13866, 4505, 1306)},   -- 红方下路外塔
        {pos = vec3(13327, 8226, 1058)}    -- 红方下路内塔
    }
    
    for _, turret_info in ipairs(turret_positions) do
        if player.pos:dist(turret_info.pos) < 900 then
            return true
        end
    end
    
    return false
end

-- 检查是否可以使用闪现
local function can_flash()
    return flash_slot and player:spellSlot(flash_slot).state == 0
end

-- Q技能伤害计算
local function q_damage(target)
    local damage_values = {20, 40, 60, 80, 100}
    local base_damage = damage_values[player:spellSlot(0).level]
    if base_damage == nil then return 0 end
    
    local ad_ratio = 1.0
    if player.crit >= 1 then
        ad_ratio = 1.75
    elseif player.crit > 0 then
        ad_ratio = 1.0 + player.crit * 0.75
    end
    
    local raw_damage = base_damage + player.totalAd * ad_ratio
    
    -- 计算护甲减免
    local armor = target.armor * player.percentArmorPenetration - player.flatArmorPenetration
    local damage_multiplier = 100 / (100 + armor)
    
    return raw_damage * damage_multiplier
end

-- W技能伤害计算
local function w_damage(target)
    local damage_values = {5, 10, 15, 20, 25}
    local base_damage = damage_values[player:spellSlot(1).level]
    if base_damage == nil then return 0 end
    
    local max_health_percent = {0.05, 0.055, 0.06, 0.065, 0.07}
    local max_health_damage = max_health_percent[player:spellSlot(1).level] * target.maxHealth
    
    local raw_damage = base_damage + max_health_damage + player.totalAd * 0.5
    
    -- 计算魔抗减免
    local mr = target.spellBlock * player.percentMagicPenetration - player.flatMagicPenetration
    local damage_multiplier = 100 / (100 + mr)
    
    return raw_damage * damage_multiplier
end

-- Q闪击杀逻辑
local function q_flash_kill()
    if not ove_0_13.flash_skill.q_flash_kill:get() then
        return false
    end
    
    if player:spellSlot(0).state ~= 0 or not can_flash() then
        return false
    end
    
    if player.health / player.maxHealth * 100 < ove_0_13.flash_skill.q_flash_hp_yone:get() then
        return false
    end
    
    if is_under_enemy_turret() then
        return false
    end
    
    local q_range = 450
    local flash_range = 400
    local search_range = ove_0_13.flash_skill.q_flash_range:get()
    
    for i = 0, objManager.enemies_n - 1 do
        local enemy = objManager.enemies[i]
        
        if enemy and enemy.isVisible and not enemy.isDead and enemy.isTargetable then
            local dist = player.pos:dist(enemy.pos)
            
            -- 检查是否在搜索范围内但超出Q范围
            if dist <= search_range and dist > q_range + enemy.boundingRadius and dist <= q_range + flash_range + enemy.boundingRadius then
                -- 计算伤害
                local damage = q_damage(enemy)
                
                -- 检查是否可以击杀
                if damage > enemy.health + enemy.healthRegenRate + enemy.allShield then
                    -- 计算闪现位置
                    local direction = (enemy.pos - player.pos):norm()
                    local flash_pos = player.pos + direction * (dist - q_range)
                    
                    -- 检查闪现位置是否有效（不在墙内）
                    if not navmesh.isWall(flash_pos) then
                        -- 先闪现，然后施放Q
                        player:castSpell("pos", flash_slot, flash_pos)
                        
                        -- 延迟施放Q
                        delayAction.DelayAction(function()
                            player:castSpell("pos", 0, enemy.pos)
                        end, 0.05)
                        
                        return true
                    end
                end
            end
        end
    end
    
    return false
end

-- W闪击杀逻辑
local function w_flash_kill()
    if not ove_0_13.flash_skill.w_flash_kill:get() then
        return false
    end
    
    if player:spellSlot(1).state ~= 0 or not can_flash() then
        return false
    end
    
    if player.health / player.maxHealth * 100 < ove_0_13.flash_skill.w_flash_hp_yone:get() then
        return false
    end
    
    if is_under_enemy_turret() then
        return false
    end
    
    local w_range = 600
    local flash_range = 400
    local search_range = ove_0_13.flash_skill.w_flash_range:get()
    
    for i = 0, objManager.enemies_n - 1 do
        local enemy = objManager.enemies[i]
        
        if enemy and enemy.isVisible and not enemy.isDead and enemy.isTargetable then
            local dist = player.pos:dist(enemy.pos)
            
            -- 检查是否在搜索范围内但超出W范围
            if dist <= search_range and dist > w_range + enemy.boundingRadius and dist <= w_range + flash_range + enemy.boundingRadius then
                -- 计算伤害
                local damage = w_damage(enemy)
                
                -- 检查是否可以击杀
                if damage > enemy.health + enemy.healthRegenRate + enemy.allShield then
                    -- 计算闪现位置
                    local direction = (enemy.pos - player.pos):norm()
                    local flash_pos = player.pos + direction * (dist - w_range)
                    
                    -- 检查闪现位置是否有效（不在墙内）
                    if not navmesh.isWall(flash_pos) then
                        -- 先闪现，然后施放W
                        player:castSpell("pos", flash_slot, flash_pos)
                        
                        -- 延迟施放W，朝向敌人的位置
                        delayAction.DelayAction(function()
                            -- 使用pos而不是self，确保W技能朝向敌人
                            player:castSpell("pos", 1, enemy.pos)
                            --print("[Flash_Skills] W闪击杀: 朝向敌人 " .. enemy.charName .. " 释放W技能")
                        end, 0.05)
                        
                        return true
                    end
                end
            end
        end
    end
    
    return false
end

-- 导出功能
return {
    q_flash_kill = q_flash_kill,
    w_flash_kill = w_flash_kill
} 
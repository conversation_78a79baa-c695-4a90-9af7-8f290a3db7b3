local ove_0_10 = module.seek("evade")
local ove_0_11 = {
	[0] = "Q",
	"W",
	"E",
	[-1] = "P",
	[3] = "R"
}
local ove_0_12 = module.load("<PERSON>", "common/SpellDatabase")
local ove_0_13 = module.load("Brian", "common/targetedSpells")
local ove_0_14 = module.load("Brian", "common/common")
local ove_0_15 = menu("intnner_ksante", "Intnner - K'Sante")

ove_0_15:set("icon", player.iconCircle)
ove_0_15:menu("combo", "Combo")
ove_0_15.combo:menu("q", "Q - Settings")
ove_0_15.combo.q:set("icon", player:spellSlot(_Q).icon)
ove_0_15.combo.q:slider("min_mana", "Minimum mana to use Q%", 0, 0, 100, 1)
ove_0_15.combo.q:header("xd", "~~ Q Settings ~~")
ove_0_15.combo.q:boolean("use_q", "Use Q", true)
ove_0_15.combo.q:boolean("use_q3", "Use all out | Q3", true)
ove_0_15.combo:menu("w", "W - Settings")
ove_0_15.combo.w:set("icon", player:spellSlot(_W).icon)
ove_0_15.combo.w:header("xd", "~~ W Settings ~~")
ove_0_15.combo.w:boolean("use_w_intra", "W in transformed mode", true)
ove_0_15.combo.w:boolean("use_w", "Auto W charge in combo", true)
ove_0_15.combo:menu("e", "E - Settings")
ove_0_15.combo.e:set("icon", player:spellSlot(_E).icon)
ove_0_15.combo.e:slider("min_mana", "Minimum mana to use E%", 0, 0, 100, 1)
ove_0_15.combo.e:header("xd", "~~ E Settings ~~")
ove_0_15.combo.e:dropdown("mode_e", "E Style: ", 1, {
	"Out AA Range",
	"Always",
	"Never"
})
ove_0_15.combo:menu("r", "R - Settings")
ove_0_15.combo.r:set("icon", player:spellSlot(_R).icon)
ove_0_15.combo.r:keybind("use_r", "Use R", false, "T")
ove_0_15.combo.r:keybind("semi_r", "Semi - R", "R", false)
ove_0_15.combo.r:boolean("damage_total", "Consider using when killable", true)
ove_0_15.combo.r:slider("endPos", "Cosider using when X allies", 3, 0, 4, 1)
ove_0_15.combo.r.endPos:set("tooltip", "0 = Off")
ove_0_15.combo.r:header("xd", "~~ Misc ~~")
ove_0_15.combo.r:slider("dont", "Don't use if enemy HP <= X", 35, 0, 100, 1)
ove_0_15.combo.r.dont:set("tooltip", "0 = Off")
ove_0_15.combo.r:slider("dont_player", "Don't use if my HP <= X", 0, 0, 100, 1)
ove_0_15:menu("harass", "Harass")
ove_0_15.harass:menu("q", "Q - Settings")
ove_0_15.harass.q:set("icon", player:spellSlot(_Q).icon)
ove_0_15.harass.q:slider("min_mana", "Minimum mana to use Q%", 25, 0, 100, 1)
ove_0_15.harass.q:header("xd", "~~ Q Settings ~~")
ove_0_15.harass.q:boolean("use_q", "Use Q", true)
ove_0_15.harass.q:boolean("use_q3", "Use all out | Q3", true)
ove_0_15.harass:menu("e", "E - Settings")
ove_0_15.harass.e:set("icon", player:spellSlot(_E).icon)
ove_0_15.harass.e:slider("min_mana", "Minimum mana to use E%", 15, 0, 100, 1)
ove_0_15.harass.e:header("xd", "~~ E Settings ~~")
ove_0_15.harass.e:dropdown("mode_e", "E Style: ", 1, {
	"Out AA Range",
	"Always",
	"Never"
})
ove_0_15:menu("w_block", "W | Block spells")
ove_0_15.w_block:boolean("auto_w", "Use W to dodge", true)
ove_0_15.w_block:boolean("ignoreCircular", "Ignore Circular spells", true)
ove_0_15.w_block:header("enemy", " ~~ Enemy SkillShots ~~ ")

if not ove_0_10 then
	ove_0_15.w_block:header("uhh", "Only with Evade enabled")
end

if ove_0_10 then
	for iter_0_0, iter_0_1 in pairs(ove_0_12) do
		for iter_0_2 = 0, objManager.enemies_n - 1 do
			local ove_0_16 = objManager.enemies[iter_0_2]

			if not ove_0_12[iter_0_0] then
				return
			end

			if iter_0_1.charName == ove_0_16.charName then
				if iter_0_1.displayname == "" then
					iter_0_1.displayname = iter_0_0
				end

				if iter_0_1.danger == 0 then
					iter_0_1.danger = 1
				end

				if ove_0_15.w_block[iter_0_1.charName] == nil then
					ove_0_15.w_block:menu(iter_0_1.charName, iter_0_1.charName)
				end

				ove_0_15.w_block[iter_0_1.charName]:menu(iter_0_0, "" .. iter_0_1.charName .. " | " .. (ove_0_11[iter_0_1.slot] or "?") .. " | " .. iter_0_0)
				ove_0_15.w_block[iter_0_1.charName][iter_0_0]:boolean("Dodge", "Dodge this spell", false)
				ove_0_15.w_block[iter_0_1.charName][iter_0_0]:slider("hp", "HP to Dodge", 100, 1, 100, 1)
			end
		end
	end

	ove_0_15.w_block:header("enemy", " ~~ Targeted spells ~~ ")

	for iter_0_3 = 0, objManager.enemies_n - 1 do
		local ove_0_17 = objManager.enemies[iter_0_3]
		local ove_0_18 = string.lower(ove_0_17.charName)

		if ove_0_17 and ove_0_13[ove_0_18] then
			for iter_0_4 = 1, #ove_0_13[ove_0_18] do
				local ove_0_19 = ove_0_13[ove_0_18][iter_0_4]

				if ove_0_15.w_block[ove_0_18] == nil then
					ove_0_15.w_block:menu(ove_0_18, ove_0_17.charName)
				end

				ove_0_15.w_block[ove_0_18]:menu(tostring(ove_0_17.charName) .. tostring(ove_0_19.menuslot), "" .. ove_0_17.charName .. " | " .. (ove_0_11[ove_0_19.slot] or "?"))
				ove_0_15.w_block[ove_0_18][tostring(ove_0_17.charName) .. tostring(ove_0_19.menuslot)]:boolean("Dodge", "Dodge: " .. ove_0_19.menuslot, false)
				ove_0_15.w_block[ove_0_18][tostring(ove_0_17.charName) .. tostring(ove_0_19.menuslot)]:slider("min.Health", "Health to Dodge", 100, 0, 100, 1)
			end
		end
	end
end

ove_0_15.w_block:header("r", "~~ Recast ~~")
ove_0_15.w_block:boolean("recast", "Auto W2 recast", true)
ove_0_15.w_block:slider("delay_cast", "Delay recast", 500, 0, 650, 5)
ove_0_15:menu("farming", "Farming")
ove_0_15.farming:keybind("toggleFarm", "Farm - Toggle", false, "N")
ove_0_15.farming:header("mana", "~~ Mana ~~")
ove_0_15.farming:boolean("Ignoreharassmana", "Ignore mana if have Blue Buff", true)
ove_0_15.farming:slider("mana.percent", "Mana Percent for farming {%}", 15, 5, 100, 5)
ove_0_15.farming:header("f", "~~ Farming ~~")
ove_0_15.farming:menu("lane", "Lane Clear")
ove_0_15.farming.lane:menu("q", "Q - Settings")
ove_0_15.farming.lane.q:set("icon", player:spellSlot(_Q).icon)
ove_0_15.farming.lane.q:boolean("use_q", "Use Q", true)
ove_0_15.farming.lane.q:slider("min_minions", "Minimum minions to hit", 1, 1, 10, 1)
ove_0_15.farming.lane.q:header("f", "~~ Q3 ~~")
ove_0_15.farming.lane.q:boolean("use_q3", "Use Q3", true)
ove_0_15.farming.lane.q:slider("min_minions3", "Minimum minions to hit", 3, 1, 10, 1)
ove_0_15.farming:menu("jungle", "Jungle Clear")
ove_0_15.farming.jungle:menu("q", "Q - Settings")
ove_0_15.farming.jungle.q:set("icon", player:spellSlot(_Q).icon)
ove_0_15.farming.jungle.q:boolean("use_q", "Use Q", true)
ove_0_15.farming.jungle:menu("w", "W - Settings")
ove_0_15.farming.jungle.w:set("icon", player:spellSlot(_W).icon)
ove_0_15.farming.jungle.w:boolean("use_w", "Use W", true)
ove_0_15.farming.jungle:menu("e", "E - Settings")
ove_0_15.farming.jungle.e:set("icon", player:spellSlot(_E).icon)
ove_0_15.farming.jungle.e:boolean("use_e", "Use E", true)
ove_0_15.farming:menu("last", "Last Hit")
ove_0_15.farming.last:menu("q", "Q - Settings")
ove_0_15.farming.last.q:set("icon", player:spellSlot(_Q).icon)
ove_0_15.farming.last.q:boolean("use_q", "Last hit with Q", true)
ove_0_15:menu("misc", "Misc.")
ove_0_15.misc:menu("kill", "Killsteal")
ove_0_15.misc.kill:boolean("use_q", "Cast Q to killable", true)
ove_0_15.misc.kill:boolean("use_w", "Cast W to killable", true)
ove_0_15.misc:menu("move", "Movement")
ove_0_15.misc.move:boolean("use_magnet", "Magnet Movement", true)
ove_0_15.misc.move:slider("magR", "Magnet Lock Range", 300, 100, 1000, 50)
ove_0_15.misc:menu("interrupt", "Interrupt")
ove_0_15.misc.interrupt:header("iq", "~~ W Settings ~~ ")
ove_0_15.misc.interrupt:boolean("use_w", "Use W to interrupt spells", true)
ove_0_15.misc.interrupt:menu("interruptmenu_q", "Interrupt - (W) Whitelist")

for iter_0_5 = 0, objManager.enemies_n - 1 do
	local ove_0_20 = objManager.enemies[iter_0_5]
	local ove_0_21 = string.lower(ove_0_20.charName)

	if ove_0_20 and ove_0_14.interrupt_spells[ove_0_21] then
		for iter_0_6 = 1, #ove_0_14.interrupt_spells[ove_0_21] do
			local ove_0_22 = ove_0_14.interrupt_spells[ove_0_21][iter_0_6]

			ove_0_15.misc.interrupt.interruptmenu_q:boolean(string.format(ove_0_20.charName .. ove_0_22.menuslot), "Interrupt " .. ove_0_20.charName .. " " .. ove_0_22.menuslot, true)
		end
	end
end

ove_0_15.misc:header("other", "~~ Others ~~")
ove_0_15.misc:keybind("misc_key", "Flash + Q3", "A", false)
ove_0_15:menu("flee", "Flee")
ove_0_15.flee:keybind("flee_key", "Flee key", "Z", false)
ove_0_15.flee:boolean("use_e", "Use E to flee", false)
ove_0_15.flee:boolean("use_w", "Use W to flee", false)
ove_0_15:menu("draws", "Draws")
ove_0_15.draws:boolean("useq", "Draw Q Range", true)
ove_0_15.draws:color("q", "^- Q Drawing Color", 255, 255, 255, 255)
ove_0_15.draws:boolean("usew", "Draw W Range", true)
ove_0_15.draws:color("w", "^- W Drawing Color", 255, 255, 255, 255)
ove_0_15.draws:header("xd", "~~ Others ~~")
ove_0_15.draws:boolean("draw_toggle", "Draw Toggles", true)
ove_0_15.draws:boolean("drawqDamage", "Draw Damage", true)
ove_0_15.draws:boolean("drawM", "Draw Magnet Range", true)
ove_0_15:header("beta", "~~ [ Champion Beta test ] ~~")

return ove_0_15

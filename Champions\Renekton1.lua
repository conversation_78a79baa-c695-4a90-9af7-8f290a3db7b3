local RenektonPlugin = {}
local menu = module.load("<PERSON>", "menu");
local ts = module.internal("TS")
local orb = module.internal("orb")
local preds = module.internal("pred")
local DelayAction = module.load("NickAIO", "Core/DelayAction")
local DelayTick = module.load("NickAIO", "Core/DelayTick")
local Prediction = module.load("NickAIO", "Core/Prediction")
local BuffManager = module.load("NickAIO", "Library/BuffManager")
local CalculateManager = module.load("NickAIO", "Library/CalculateManager")
--local FarmManager = module.load("NickAIO", "Library/FarmManager")
local ItemManager = module.load("NickAIO", "Library/ItemManager")
local NetManager = module.load("NickAIO", "Library/NetManager")
local ObjectManager = module.load("NickAIO", "Library/ObjectManager")
local OrbManager = module.load("NickAIO", "Library/OrbManager")
local SpellManager = module.load("NickAIO", "Library/SpellManager")
local VectorManager = module.load("NickAIO", "Library/VectorManager")
local MyCommon = module.load("NickAIO", "Library/ExtraManager")
local common = module.load("NickAIO", "Utility/common")

local MyMenu

function RenektonPlugin.Load(GlobalMenu)
    MyMenu = GlobalMenu

    MyMenu.Key:keybind("run", "Marathon Mode", "S", false)

    MyMenu:menu("combo", "Combo Settings")
    MyMenu.combo:header("xd", "Combo Settings")
    MyMenu.combo:boolean("tiamat", "Use Tiamat/Hydra", true)
    MyMenu.combo:header("xd", "Q Settings")
    MyMenu.combo:boolean("q", "Use Q", true)

    MyMenu.combo:header("xd", "W Settings")
    MyMenu.combo:boolean("w", "Smart W", true)

    MyMenu.combo:header("xd", "E Settings")
    MyMenu.combo:boolean("e", "Use E", true)
    MyMenu.combo:boolean("td", "Smart Tower-Dive", true)

    MyMenu.combo:header("xd", "R Settings")
    MyMenu.combo:boolean("r", "Use Smart R", true)
    MyMenu.combo:slider("rx", "R on X Enemys in Range", 1, 0, 5, 1)
    MyMenu.combo:slider("rhp", "What HP% to Ult", 10, 0, 100, 5)

    MyMenu:menu("harass", "Harass Settings")
    MyMenu.harass:header("xd", "Q Settings")
    MyMenu.harass:boolean("q", "Use Q", true)
    MyMenu.harass:header("xd", "W Settings")
    MyMenu.harass:boolean("w", "use W", true)

    MyMenu:menu("auto", "Killsteal Settings")
    MyMenu.auto:header("xd", "KillSteal Settings")
    MyMenu.auto:boolean("uks", "Use Killsteal", true)
    MyMenu.auto:boolean("uksq", "Use Q on Killsteal", true)

    MyMenu:menu("draws", "Draw Settings")
    MyMenu.draws:header("xd", "Drawing Options")
    MyMenu.draws:boolean("q", "Draw Q Range", true)
    MyMenu.draws:color("colorq", "^ colorq", 255, 233, 121, 121)
end

local function select_target(res, obj, dist)
	if dist > 450 then
		return
	end
	res.obj = obj
	return true
end

local function get_target(func)
	return ts.get_result(func).obj
end
--local dmg = 65 + (10 * player.levelRef) + (common.GetBonusAD() * 2)


local xd = 0
function is_turret_near(position)
    local hewwo = false
    if xd < os.clock() then
        xd = os.clock() + 0.1
        objManager.loop(
            function(obj)
                if obj and obj.pos:dist(position) < 900 and obj.team == TEAM_ENEMY and obj.type == TYPE_TURRET then
                    hewwo = true
                end
            end
        )

        return hewwo
    end
end

local function launchE(pos)
	if pos then
		player:castSpell("pos", 2, vec3(pos.x, pos.y, pos.z));
		orb.core.set_server_pause();
	end
end

local function qDmg(target)
	if player.path.serverPos:dist(target.path.serverPos) < 325 then 
		local base_damage = 65 + (35 * player:spellSlot(0).level) + (common.GetBonusAD() * 0.8)
		local total = base_damage
		return common.CalculatePhysicalDamage(target, total)
	end
end


local function CastE(target)
	if player:spellSlot(2).state == 0 and player.path.serverPos:dist(target.path.serverPos) < 430 then
		if player:spellSlot(2).name == "RenektonSliceAndDice" and player.path.serverPos:dist(target.path.serverPos) > 200 then 
			launchE(target.pos)
		elseif player:spellSlot(2).name == "RenektonDice" and player.path.serverPos:dist(target.path.serverPos) > 125 then
			launchE(target.pos)
		end
	end
end

local function HasSionBuff(e)
	for i = 0, e.buffManager.count - 1 do
		local buff = e.buffManager:get(i)
		if buff and buff.valid and buff.name == 'sionpassivezombie' then
			return true
		end
	end
end

local function Combo()
	local target = get_target(select_target)
	if target and common.IsValidTarget(target) and not HasSionBuff(target) then
		local d = player.path.serverPos:dist(target.path.serverPos)
		local q = player:spellSlot(0).state == 0
		local r = player:spellSlot(3).state == 0
		if MyMenu.combo.q:get() and q and d < 320 then
			player:castSpell("self", 0)
		end
		if MyMenu.combo.e:get() then
			CastE(target)
		end
	end
end

local function autoUlt()
	if player:spellSlot(3).state ~= 0 then return end
	if MyMenu.combo.r:get() and #common.GetEnemyHeroesInRange(800) >= MyMenu.combo.rx:get() and common.GetPercentHealth(player) <=  MyMenu.combo.rhp:get() then
		player:castSpell("self", 3)
	end
end

local function Harass()
	local target = get_target(select_target)
	if target and common.IsValidTarget(target) and not HasSionBuff(target) then
		local d = player.path.serverPos:dist(target.path.serverPos)
		if MyMenu.harass.q:get() and player:spellSlot(0).state == 0 and d < 320 then
			player:castSpell("self", 0)
		end
	end
end

local function KillSteal()
	for i = 0, objManager.enemies_n - 1 do
		local enemy = objManager.enemies[i]
		local d = player.path.serverPos:dist(enemy.path.serverPos)
 		if enemy and common.IsValidTarget(enemy) and MyMenu.auto.uks:get() and not HasSionBuff(enemy) then
  			if MyMenu.auto.uksq:get() and player:spellSlot(0).state == 0 and d < 320 and enemy.health < qDmg(enemy) then
	  			player:castSpell("self", 0)
	  		end
  		end
 	end
end

local function Run()
	if MyMenu.Key.run:get() then
		player:move((game.mousePos))
		if player:spellSlot(2).state == 0 then
			player:castSpell("pos", 2, (game.mousePos))
		end
	end
end


local function OnTick()
	if MyMenu.Key.Combo:get() then
		Combo()
	end
	if MyMenu.Key.Harass:get() then
		Harass()
	end
	if MyMenu.auto.uks:get() then
		KillSteal()
	end
	if MyMenu.Key.run:get() then
		Run()
	end
	if MyMenu.combo.r:get() then autoUlt() end
end

local function OnDraw()
	if MyMenu.draws.q:get() and player:spellSlot(0).state == 0 and player.isOnScreen then
		graphics.draw_circle(player.pos, 325, 2, MyMenu.draws.colorq:get(), 50)
	end
end

orb.combat.register_f_pre_tick(OnTick)
cb.add(cb.draw, OnDraw)
orb.combat.register_f_after_attack(
	function()
		if orb.combat.is_active() then
			if orb.combat.target then
				if MyMenu.combo.w:get() and orb.combat.target and common.IsValidTarget(orb.combat.target) and player.pos:dist(orb.combat.target.pos) < common.GetAARange(orb.combat.target) + 50 then
					if player:spellSlot(1).state == 0 then
						player:castSpell("self", 1)
						player:attack(orb.combat.target)
						return "on_after_attack_hydra"
					end
				end
				if MyMenu.combo.tiamat:get() and orb.combat.target and common.IsValidTarget(orb.combat.target) and player.pos:dist(orb.combat.target.pos) < common.GetAARange(orb.combat.target) then
		          for i = 6, 11 do
		            local item = player:spellSlot(i).name
		            if item and (item == "ItemTitanicHydraCleave" or item == "ItemTiamatCleave") and player:spellSlot(i).state == 0 then
		              player:castSpell("obj", i, player)
		              orb.core.set_server_pause()
		              orb.combat.set_invoke_after_attack(false)
		              player:attack(orb.combat.target)
		              orb.core.set_server_pause()
		              orb.combat.set_invoke_after_attack(false)
		              return "on_after_attack_hydra"
		            end
		          end
		        end
			end
		end
		if MyMenu.Key.Harass:get() then
			if orb.combat.target then
				if MyMenu.harass.w:get() and orb.combat.target and common.IsValidTarget(orb.combat.target) and player.pos:dist(orb.combat.target.pos) < common.GetAARange(orb.combat.target) + 50 then
					if player:spellSlot(1).state == 0 then
						player:castSpell("self", 1)
						player:attack(orb.combat.target)
						return "on_after_attack_hydra"
					end
				end
			end
		end
	end
)

return RenektonPlugin


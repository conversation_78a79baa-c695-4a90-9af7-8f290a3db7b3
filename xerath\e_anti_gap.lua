local TS = module.internal("TS")
local orb = module.internal("orb")
local pred = module.internal("pred")
local menu = module.load(header.id, "xerath/menu")

local E_SPELL = player:spellSlot(2)
local ANTI_GAP_RANGE = 1000000
local ANTI_GAP_RANGE_SQR = 122500

-- E技能数据
local E_DATA = {
    speed = 1400,
    delay = 0.25,
    boundingRadiusMod = 1,
    width = 70,
    collision = {
        minion = true,
        hero = false
    }
}

-- 突进英雄列表 - 常见的突进技能
local DASH_CHAMPIONS = {
    ["Aatrox"] = true,
    ["Akali"] = true,
    ["Alistar"] = true,
    ["Ammu"] = true,
    ["Azir"] = true,
    ["<PERSON>"] = true,
    ["<PERSON>"] = true,
    ["<PERSON>k<PERSON>"] = true,
    ["<PERSON>ora"] = true,
    ["Fizz"] = true,
    ["<PERSON>"] = true,
    ["Irel<PERSON>"] = true,
    ["<PERSON>"] = true,
    ["<PERSON><PERSON><PERSON>"] = true,
    ["<PERSON><PERSON>"] = true,
    ["<PERSON>ha'<PERSON>ix"] = true,
    ["<PERSON><PERSON>lan<PERSON>"] = true,
    ["<PERSON> Sin"] = true,
    ["<PERSON><PERSON>"] = true,
    ["<PERSON>"] = true,
    ["<PERSON><PERSON><PERSON>"] = true,
    ["<PERSON> <PERSON>"] = true,
    ["Nautilus"] = true,
    ["Nocturne"] = true,
    ["Pantheon"] = true,
    ["Poppy"] = true,
    ["Pyke"] = true,
    ["Qiyana"] = true,
    ["Rammus"] = true,
    ["Rek'Sai"] = true,
    ["Renekton"] = true,
    ["Riven"] = true,
    ["Sejuani"] = true,
    ["Shen"] = true,
    ["Shyvana"] = true,
    ["Talon"] = true,
    ["Thresh"] = true,
    ["Tristana"] = true,
    ["Tryndamere"] = true,
    ["Udyr"] = true,
    ["Vi"] = true,
    ["Warwick"] = true,
    ["Wukong"] = true,
    ["Xin Zhao"] = true,
    ["Yasuo"] = true,
    ["Yone"] = true,
    ["Zac"] = true,
    ["Zed"] = true
}

-- 检查敌人是否正在突进
local function isEnemyDashing(enemy)
    if not enemy or not enemy.isDashing then
        return false
    end
    
    -- 检查是否在菜单设置的范围内
    local distance = player.pos:dist(enemy.pos)
    if distance > menu.e_anti_gap_range:get() then
        return false
    end
    
    return true
end

-- 检查敌人是否正在向我们移动
local function isEnemyMovingTowardsUs(enemy)
    if not enemy or not enemy.isMoving then
        return false
    end

    -- 检查路径是否有效
    if not enemy.path or not enemy.path.point or not enemy.path.index or
       not enemy.path.point[enemy.path.index] then
        return false
    end

    -- 计算敌人的移动方向
    local enemyDirection = enemy.path.point[enemy.path.index] - enemy.pos
    local toPlayerDirection = player.pos - enemy.pos

    -- 使用API支持的角度计算方法
    local angle = enemyDirection:angle(toPlayerDirection)

    -- 如果角度小于90度（π/2弧度），说明敌人在向我们移动
    return math.abs(angle) < math.pi / 2
end

-- 预测敌人的突进路径
local function predictDashPath(enemy)
    if not enemy.isDashing then
        return nil
    end
    
    -- 获取敌人的冲刺目标位置
    local dashEndPos = enemy.path.point[enemy.path.index]
    if not dashEndPos then
        return enemy.pos
    end
    
    -- 计算冲刺方向
    local dashDirection = (dashEndPos - enemy.pos):norm()
    
    -- 预测敌人在E技能到达时的位置
    local timeToHit = E_DATA.delay + player.pos:dist(enemy.pos) / E_DATA.speed
    local predictedPos = enemy.pos + dashDirection * enemy.moveSpeed * timeToHit
    
    return predictedPos
end

-- 检查是否应该使用E反突进
local function shouldUseAntiGap(enemy)
    if not menu.e_anti_gap:get() then
        return false
    end

    if E_SPELL.state ~= 0 then
        return false
    end

    if not enemy or enemy.isDead or not enemy.isTargetable or not enemy.isVisible then
        return false
    end

    -- 检查敌人是否免疫控制
    if enemy.isSpellImmune then
        return false
    end

    local distance = player.pos:dist(enemy.pos)

    -- 检查距离限制
    if distance > menu.e_anti_gap_range:get() then
        return false
    end

    -- 如果敌人太近，立即使用E
    if distance < menu.e_anti_gap_min_distance:get() then
        return true
    end

    -- 如果设置为仅对冲刺使用
    if menu.e_anti_gap_dash_only:get() then
        if not isEnemyDashing(enemy) then
            return false
        end
    else
        -- 检查敌人是否在向我们移动
        if not isEnemyMovingTowardsUs(enemy) and not isEnemyDashing(enemy) then
            return false
        end
    end

    -- 检查是否是突进英雄
    if DASH_CHAMPIONS[enemy.charName] then
        return true
    end

    -- 如果不是已知的突进英雄，但正在冲刺，也使用E
    if isEnemyDashing(enemy) then
        return true
    end

    return false
end

-- 执行E反突进
local function executeAntiGap(enemy)
    local castPos = enemy.pos
    
    -- 如果启用了预测
    if menu.e_anti_gap_predict:get() and enemy.isDashing then
        local predictedPos = predictDashPath(enemy)
        if predictedPos then
            castPos = predictedPos
        end
    end
    
    -- 使用预测系统获取更准确的位置
    local prediction = pred.linear.get_prediction(E_DATA, enemy)
    if prediction and prediction.startPos:distSqr(prediction.endPos) < ANTI_GAP_RANGE and 
       not pred.collision.get_prediction(E_DATA, prediction, enemy) then
        castPos = vec3(prediction.endPos.x, enemy.y, prediction.endPos.y)
    end
    
    -- 检查距离是否在E技能范围内
    if player.pos:dist(castPos) <= 1000 then -- E技能最大范围
        if player:castSpell("pos", 2, castPos) then
            orb.core.set_server_pause()
            return true
        end
    end

    return false
end

-- 主要的反突进检查函数
local function checkAntiGap()
    if not menu.e_anti_gap:get() then
        return false
    end

    if E_SPELL.state ~= 0 then
        return false
    end

    -- 检查是否在回城
    if player.isRecalling then
        return false
    end

    -- 检查是否被控制
    if player.isStunned or player.isCharmed or player.isFeared or player.isSuppressed then
        return false
    end

    -- 遍历所有敌方英雄
    for i = 0, objManager.enemies_n - 1 do
        local enemy = objManager.enemies[i]
        if shouldUseAntiGap(enemy) then
            if executeAntiGap(enemy) then
                return true
            end
        end
    end

    return false
end

-- 返回模块接口
return {
    check_anti_gap = checkAntiGap,
    should_use_anti_gap = shouldUseAntiGap,
    execute_anti_gap = executeAntiGap
}

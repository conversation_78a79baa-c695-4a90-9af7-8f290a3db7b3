
local UrgotPlugin = {}
local common = module.load("<PERSON>", "Utility/common")
local ts = module.internal("TS")
local orb = module.internal("orb")
local gpred = module.internal("pred")
local qSpell = {range = 800}
local wSpell = {range = 400}
local eSpell = {range = 475}
local rSpell = {range = 2450}
local flashe = {range = 990}
local QlDmg = {25, 70, 115, 160, 205}
local WlDmg = {60, 100, 140, 180, 220}
local RlDmg = {50, 175, 300}
local DelayAction = module.load("Brian", "Core/DelayAction")
local DelayTick = module.load("<PERSON>", "Core/DelayTick")
--local Prediction = module.load("<PERSON>", "Core/Prediction")
local BuffManager = module.load("Brian", "Library/BuffManager")
local CalculateManager = module.load("Brian", "Library/CalculateManager")
local FarmManager = module.load("<PERSON>", "Library/FarmManager")
local ItemManager = module.load("<PERSON>", "Library/ItemManager")
local NetManager = module.load("<PERSON>", "Library/NetManager")
local ObjectManager = module.load("<PERSON>", "Library/ObjectManager")
local OrbManager = module.load("Brian", "Library/OrbManager")
local SpellManager = module.load("Brian", "Library/SpellManager")
local VectorManager = module.load("Brian", "Library/VectorManager")
local AntiGapcloser = module.load("Brian", "Core/Gapcloser")
local MyCommon = module.load("Brian", "Library/ExtraManager")
local Curses = module.load("Brian", "Curses");
local ui = module.load("Brian", "ui");
local MyMenu

function UrgotPlugin.Load(GlobalMenu)
    MyMenu = GlobalMenu


	MyMenu:header("serdar", "Urgot")

	MyMenu:menu("keys", "Key Settings")
	MyMenu.keys:keybind("combokey", "Combo Key", "Space", nil)
	MyMenu.keys:keybind("clearkey", "Clear Jungle/Lane Key", "V", nil)
	MyMenu.keys:keybind("Combokey", "Combo Key", "C", nil)

	MyMenu:menu("combo", "Combo Settings")
	MyMenu.combo:boolean("qcombo", "Use Q in Combo", true)
	MyMenu.combo:boolean("wcombo", "Use W in Combo", true)
	MyMenu.combo:boolean("ecombo", "Use E in Combo", true)
	MyMenu.combo:boolean("rcombo", "Use R in Combo", true)
	--MyMenu.combo:boolean("items", "Use Items", true)

	MyMenu:menu("Harass", "Harass Settings")
	MyMenu.Harass:boolean("qCombo", "Use Q in Combo", true)
	MyMenu.Harass:boolean("wCombo", "Use W in Combo", true)
	MyMenu.Harass:slider("hmana", "Mana Manager", 30, 0, 100, 30)

	MyMenu:menu("killsteal", "Killsteal Settings")
	MyMenu.killsteal:boolean("useks", "Use Killsteal", true)
	MyMenu.killsteal:boolean("qks", "Use Q in Killsteal", true)
	MyMenu.killsteal:boolean("wks", "Use W in Killsteal", true)
	MyMenu.killsteal:boolean("rks", "Use R in Killsteal", true)

	FarmManager.Load(MyMenu)

	MyMenu:menu("laneclear", "LaneClear Settings")
	MyMenu.laneclear:boolean("farmq", "Use Q to Farm", false)
	MyMenu.laneclear:boolean("farmw", "Use W to Farm", false)
	MyMenu.laneclear:slider("lmana", "Mana Manager", 30, 0, 100, 30)

	MyMenu:menu("jungclear", "JungClear Settings")
	MyMenu.jungclear:boolean("jungq", "Use Q to Jung", true)
	MyMenu.jungclear:boolean("jungw", "Use W to Jung", true)
	MyMenu.jungclear:slider("jmana", "Mana Manager", 30, 0, 100, 30)

	MyMenu:menu("draws", "Drawings Settings")
	MyMenu.draws:boolean("drawq", "Draw Q Range", true)
	MyMenu.draws:color("colorq", "^ color", 255, 233, 121, 121)
	MyMenu.draws:boolean("draww", "Draw W Range", true)
	MyMenu.draws:color("colorw", "^ color", 255, 233, 121, 121)
	MyMenu.draws:boolean("drawe", "Draw E Range", true)
	MyMenu.draws:color("colore", "^ color", 255, 233, 121, 121)
	MyMenu.draws:boolean("drawr", "Draw R Range", true)
	MyMenu.draws:color("colorr", "^ color", 255, 233, 121, 121)
end

local TargetSelection = function(res, obj, dist)
	if dist < qRange.range then
		res.obj = obj
		return true
	end
end

local function select_target(res, obj, dist)
	if dist > 1500 then return end
	res.obj = obj
	return true
end
local function get_target(func)
	return ts.get_result(func).obj
end
local function ult_target(res, obj, dist)
	if dist > 1500 then return end
	res.obj = obj
	return true
end

local qp = { delay = 0.6, radius = 100, speed = math.huge, boundingRadiusMod = 1, collision = { hero = false, minion = false } }
local wp = { delay = 0.25, radius = 125, speed = 2200, boundingRadiusMod = 1, collision = { hero = false, minion = false } }
local ep = { delay = 0.45, width = 100, speed = 1050, boundingRadiusMod = 2, collision = { hero = false, minion = false } }
local rp = { delay = 0, width = 70, speed = 3200, boundingRadiusMod = 1, collision = { hero = false, minion = false } }

local function qHesap(target)
	local qDamage = QlDmg[player:spellSlot(0).level] + (common.GetTotalAP() * .4)  
	return common.CalculateMagicDamage(target, qDamage)
end
local function wHesap(target)
	local wDamage = QlDmg[player:spellSlot(1).level] + (common.GetTotalAP() * .5)
	return common.CalculateMagicDamage(target, wDamage)
end
local function rHesap(target)
	local rDamage = RlDmg[player:spellSlot(3).level] + (common.GetTotalAP() * .125)
	return common.CalculateMagicDamage(target, rDamage)
end
local function CastQ(target)
	if player:spellSlot(0).state == 0 and player.path.serverPos:distSqr(target.path.serverPos) < (800 * 800) then
		local ser = gpred.circular.get_prediction(qp, target)
		if ser and ser.startPos:dist(ser.endPos) < 800 then
			player:castSpell("pos", 0, vec3(ser.endPos.x, game.mousePos.y, ser.endPos.y))
		end
	end
end
local function CastW(target)
	if player:spellSlot(1).state == 0 and player.path.serverPos:distSqr(target.path.serverPos) < (400 * 400) then
		local ser = gpred.circular.get_prediction(wp, target)
		if ser and ser.startPos:dist(ser.endPos) < 400 then
			--player:castSpell("pos", 1, target)
			player:castSpell('obj', 1, target)
		end
	end
end
local function CastE(target)
	if player:spellSlot(2).state == 0 and player.path.serverPos:distSqr(target.path.serverPos) < (475 * 475) then
		local ser = gpred.linear.get_prediction(ep, target)
		if ser and ser.startPos:dist(ser.endPos) < 1000 then
			player:castSpell("pos", 2, vec3(ser.endPos.x, game.mousePos.y, ser.endPos.y))
		end
	end
end    
local function CastR(target)
	if player:spellSlot(3).state == 0 and player.path.serverPos:distSqr(target.path.serverPos) < (1500 * 1500) then
		local ser = gpred.linear.get_prediction(rp, target)
		if ser and ser.startPos:dist(ser.endPos) < 1500 then
			player:castSpell("pos", 3, vec3(ser.endPos.x, target.pos.y, ser.endPos.y))
		end
	end
end 

local function CastRL()
if MyMenu.combo.rcombo:get() then
local target = get_target(ult_target)
	for i = 0, objManager.enemies_n - 1 do
		local enemy = objManager.enemies[i]
		if enemy and common.IsValidTarget(enemy) then
		local hp = enemy.health
      	local dist = player.path.serverPos:distSqr(enemy.path.serverPos)
			if player:spellSlot(3).state == 0 and dist <= (1500 * 1500) and rHesap(enemy) > hp  then
				CastR(enemy)
			end
		end
	end
end
end
local function CastRP()
if MyMenu.combo.rcombo:get() then
local target = get_target(ult_target)
	for i = 0, objManager.enemies_n - 1 do
		local enemy = objManager.enemies[i]
		if enemy and common.IsValidTarget(enemy) then
		local hp = enemy.health
      	local dist = player.path.serverPos:distSqr(enemy.path.serverPos)
			if player:spellSlot(3).state == 0 and dist <= (1625 * 1625) and (rHesap(enemy) * 2) + qHesap(enemy) + wHesap(enemy) + 100 > hp  then
				CastR(enemy)
			end
		end
	end
end
end


local function Combo()
	local target = get_target(select_target)
	if target and common.IsValidTarget(target) then 
		if MyMenu.combo.qcombo:get() and player:spellSlot(0).state == 0 and player.pos:dist(target.pos) <= qSpell.range 
			then CastQ(target)
		end
		if MyMenu.combo.rcombo:get() and player:spellSlot(3).state == 0 and player.pos:dist(target.pos) <= qSpell.range 
			then CastRP(target)
		end
		if MyMenu.combo.ecombo:get() and player:spellSlot(2).state == 0 and player:spellSlot(1).state == 0  and player.pos:dist(target.pos) <= wSpell.range
		   then CastE(target)
		end
		if MyMenu.combo.wcombo:get() and player:spellSlot(1).state == 0 and player:spellSlot(2).state <= 1 and player.pos:dist(target.pos) <= qSpell.range 
			then CastW(target)
		end
	end
end
local function Harass()
	if common.GetPercentPar() >= MyMenu.Harass.hmana:get() then
		local target = get_target(select_target)
		if target and common.IsValidTarget(target) then
		if MyMenu.Harass.qCombo:get() and MyMenu.keys.Combokey:get() then
			if (target.pos:dist(player) < qSpell.range) 
				then CastQ(target)
			end
		end
		if MyMenu.Harass.wCombo:get() and MyMenu.keys.harasskey:get() then
			if (target.pos:dist(player) < wSpell.range) 
					then CastW(target)
			end
		end
		end
	end
end
local function JungClear()
if (player.mana / player.maxMana) * 100 >= MyMenu.jungclear.jmana:get() then
	if MyMenu.jungclear.jungq:get() and MyMenu.keys.clearkey:get() then	
		local enemyMinionsQ = common.GetMinionsInRange(qSpell.range, TEAM_NEUTRAL)
		for i, minion in pairs(enemyMinionsQ) do
			if minion and not minion.isDead and common.IsValidTarget(minion) then
				local minionPos = vec3(minion.x, minion.y, minion.z)
				if minionPos:dist(player.pos) <= qSpell.range then
					CastQ(minion)
				end
			end
		end
	end
	if MyMenu.jungclear.jungw:get() and MyMenu.keys.clearkey:get() then	
		local enemyMinionsW = common.GetMinionsInRange(wSpell.range, TEAM_NEUTRAL)
		for i, minion in pairs(enemyMinionsW) do
			if minion and not minion.isDead and common.IsValidTarget(minion) then
				local minionPos = vec3(minion.x, minion.y, minion.z)
				if minionPos:dist(player.pos) <= wSpell.range then
					CastW(minion)
				end
			end
		end
	end
end
end
local function LaneClear()
	if (player.mana / player.maxMana) * 100 >= MyMenu.laneclear.lmana:get() then
		if MyMenu.laneclear.farmq:get() and MyMenu.keys.clearkey:get() then
			local enemyMinionsQ = common.GetMinionsInRange(qSpell.range, TEAM_ENEMY)
			for i, minion in pairs(enemyMinionsQ) do
				if minion and not minion.isDead and common.IsValidTarget(minion) then
				local minionPos = vec3(minion.x, minion.y, minion.z)
					if minionPos:dist(player.pos) <= qSpell.range then
						CastQ(minion)
					end
				end	
			end
		end
		if MyMenu.laneclear.farmw:get() and MyMenu.keys.clearkey:get() then
			local enemyMinionsW = common.GetMinionsInRange(wSpell.range, TEAM_ENEMY)
			for i, minion in pairs(enemyMinionsW) do
				if minion and not minion.isDead and common.IsValidTarget(minion) then
				local minionPos = vec3(minion.x, minion.y, minion.z)
					if minionPos:dist(player.pos) <= wSpell.range then
						CastW(minion)
					end
				end	
			end
		end
	end			
end
local function KillSteal()
	for i = 0, objManager.enemies_n - 1 do
		local enemy = objManager.enemies[i]
		if enemy and common.IsValidTarget(enemy) and MyMenu.killsteal.useks:get() then
		local hp = enemy.health
		local c = player.path.serverPos:distSqr(enemy.path.serverPos)
			if MyMenu.killsteal.qks:get() and MyMenu.killsteal.useks:get() then
				if	player:spellSlot(0).state == 0 and c < qSpell.range and
                qHesap(enemies) > hp
					then CastQ(enemy)
				end
				end	
				if MyMenu.killsteal.wks:get() and MyMenu.killsteal.useks:get() then
				if player:spellSlot(1).state == 0 and c < wSpell.range and 
					wHesap(enemies) > hp
					then CastW(enemy)
				end
				end	
				if MyMenu.killsteal.rks:get() and MyMenu.killsteal.useks:get() then
				if	player:spellSlot(3).state == 0 and c < rSpell.range and
				rHesap(enemies) > hp
				then CastR(enemy)
				end
			end
		end
	end
end
local function OnTick()
	if MyMenu.killsteal.useks:get() then KillSteal() end
	if orb.combat.is_active() then Combo() end
	--if MyMenu.keys.harasskey:get() then Harass() end
	if MyMenu.keys.clearkey:get() then JungClear() end	
	if MyMenu.keys.clearkey:get() then LaneClear() end
end

local function OnDraw()
		if MyMenu.draws.drawq:get() and player:spellSlot(0).state == 0 then
      	 graphics.draw_circle(player.pos, qSpell.range, 1, MyMenu.draws.colorq:get(), 100)
    	end
    	if MyMenu.draws.draww:get() and player:spellSlot(1).state == 0 then
      		graphics.draw_circle(player.pos, wSpell.range, 1, MyMenu.draws.colorw:get(), 100)
    	end 	 	
    	if MyMenu.draws.drawe:get() and player:spellSlot(2).state == 0 then
      		graphics.draw_circle(player.pos, eSpell.range , 1, MyMenu.draws.colore:get(), 100)
    	end
    	if MyMenu.draws.drawr:get() and player:spellSlot(3).state == 0 then
      		graphics.draw_circle(player.pos, rSpell.range , 1,MyMenu.draws.colorr:get(), 100)
    	end    	
end 


cb.add(cb.tick, OnTick)
cb.add(cb.draw, OnDraw)

return UrgotPlugin
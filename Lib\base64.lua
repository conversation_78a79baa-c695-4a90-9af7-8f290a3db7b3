
local ove_0_10 = {}
local ove_0_11 = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/"
local ove_0_12 = {
	p = 41,
	G = 6,
	f = 31,
	["2"] = 54,
	q = 42,
	["3"] = 55,
	o = 40,
	O = 14,
	P = 15,
	i = 34,
	U = 20,
	n = 39,
	Q = 16,
	["1"] = 53,
	m = 38,
	u = 46,
	["0"] = 52,
	l = 37,
	a = 26,
	["7"] = 59,
	j = 35,
	M = 12,
	k = 36,
	L = 11,
	J = 9,
	["-"] = 62,
	K = 10,
	h = 33,
	F = 5,
	I = 8,
	g = 32,
	H = 7,
	e = 30,
	d = 29,
	b = 27,
	E = 4,
	c = 28,
	D = 3,
	B = 1,
	C = 2,
	A = 0,
	_ = 63,
	z = 51,
	Z = 25,
	v = 47,
	y = 50,
	x = 49,
	V = 21,
	Y = 24,
	w = 48,
	X = 23,
	["6"] = 58,
	["9"] = 61,
	W = 22,
	["8"] = 60,
	t = 45,
	r = 43,
	N = 13,
	s = 44,
	T = 19,
	<PERSON> = 17,
	["5"] = 57,
	S = 18,
	["4"] = 56
}

local function ove_0_13(arg_5_0, arg_5_1)
	-- print 5
	return arg_5_0 * 2^arg_5_1 % 256
end

local function ove_0_14(arg_6_0, arg_6_1)
	-- print 6
	return math.floor(arg_6_0 / 2^arg_6_1) % 256
end

local function ove_0_15(arg_7_0, arg_7_1)
	-- print 7
	return arg_7_0 % 2^arg_7_1 - arg_7_0 % 2^(arg_7_1 - 1) > 0
end

local function ove_0_16(arg_8_0, arg_8_1)
	-- print 8
	result = 0

	for iter_8_0 = 1, 8 do
		result = result + ((ove_0_15(arg_8_0, iter_8_0) or ove_0_15(arg_8_1, iter_8_0)) == true and 2^(iter_8_0 - 1) or 0)
	end

	return result
end

function ove_0_10.encode(arg_9_0)
	-- print 9
	return (arg_9_0:gsub(".", function(arg_10_0)
		-- print 10
		local slot_10_0 = ""
		local slot_10_1 = arg_10_0:byte()

		for iter_10_0 = 8, 1, -1 do
			slot_10_0 = slot_10_0 .. (slot_10_1 % 2^iter_10_0 - slot_10_1 % 2^(iter_10_0 - 1) > 0 and "1" or "0")
		end

		return slot_10_0
	end) .. "0000"):gsub("%d%d%d?%d?%d?%d?", function(arg_11_0)
		-- print 11
		if #arg_11_0 < 6 then
			return ""
		end

		local slot_11_0 = 0

		for iter_11_0 = 1, 6 do
			slot_11_0 = slot_11_0 + (arg_11_0:sub(iter_11_0, iter_11_0) == "1" and 2^(6 - iter_11_0) or 0)
		end

		return ove_0_11:sub(slot_11_0 + 1, slot_11_0 + 1)
	end) .. ({
		"",
		"==",
		"="
	})[#arg_9_0 % 3 + 1]
end

function ove_0_10.decode(arg_12_0)
	-- print 12
	local slot_12_0 = {}
	local slot_12_1 = ""

	for iter_12_0 = 0, string.len(arg_12_0) - 1, 4 do
		for iter_12_1 = 1, 4 do
			slot_12_0[iter_12_1] = ove_0_12[string.sub(arg_12_0, iter_12_0 + iter_12_1, iter_12_0 + iter_12_1) or "="]
		end

		slot_12_1 = string.format("%s%s%s%s", slot_12_1, string.char(ove_0_16(ove_0_13(slot_12_0[1], 2), ove_0_14(slot_12_0[2], 4))), slot_12_0[3] ~= nil and string.char(ove_0_16(ove_0_13(slot_12_0[2], 4), ove_0_14(slot_12_0[3], 2))) or "", slot_12_0[4] ~= nil and string.char(ove_0_16(ove_0_13(slot_12_0[3], 6) % 192, slot_12_0[4])) or "")
	end

	return slot_12_1
end

return ove_0_10

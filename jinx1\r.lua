local lvxbot = module.load(header.id, 'lvxbot/main')

local input = {
  prediction = {
    type = 'Linear',
    --
    range = 3000,
    delay = 0.65,
    speed = 1700,
    width = 120,
    boundingRadiusMod = 1,
	
	   collision = {
      hero = true, --allow to hit other heros :-)
      minion = false,
      wall = true,
	  },
  },

  target_selector = {
    type = 'LESS_CAST_AD',
  },

  cast_spell = {
    type = 'pos',
    slot = _R,
    arg1 = function(action)
      local seg = action.ts_result.seg
      local obj = action.ts_result.obj
      return seg.endPos:to3D(obj.y)
    end,
  },

  slot = _R,
  ignore_obj_radius = 3000,
}

local module = lvxbot.jinxexpert.create(input)



return module

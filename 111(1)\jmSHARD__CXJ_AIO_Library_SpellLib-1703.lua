math.randomseed(0.614704)

local ove_0_0

ove_0_0 = {
	function(...)
		local slot_1_0 = {
			...
		}

		return ove_0_0[8](slot_1_0)
	end,
	cb,
	game,
	math.frexp,
	math.random(1, 1000),
	string.dump,
	string.sub,
	table.concat,
	cb,
	"bot",
	loadstring,
	"b+",
	function(arg_2_0)
		local slot_2_0 = loadstring(arg_2_0)

		if slot_2_0 then
			return ove_0_0[tonumber("20")](function()
				slot_2_0()
			end)
		else
			return nil
		end
	end,
	"load",
	1234567890,
	getfenv,
	"",
	"jit",
	0.1,
	pcall,
	math.pi,
	""
}

local ove_0_1 = ove_0_0[5]

local function ove_0_2(arg_4_0)
	return tostring(arg_4_0 / ove_0_1)
end

local ove_0_3 = {
	ove_0_2(1574),
	ove_0_2(136),
	ove_0_2(6771),
	ove_0_2(1647),
	ove_0_2(22127),
	ove_0_2(9988),
	ove_0_2(15932),
	ove_0_2(15773),
	ove_0_2(4234),
	ove_0_2(1025),
	ove_0_2(1687),
	ove_0_2(17111),
	ove_0_2(28993),
	ove_0_2(28403),
	ove_0_2(951),
	ove_0_2(25042),
	ove_0_2(24702),
	ove_0_2(3823),
	ove_0_2(25584),
	ove_0_2(25825),
	ove_0_2(16222),
	ove_0_2(16490),
	ove_0_2(21103),
	ove_0_2(18642),
	ove_0_2(23686),
	ove_0_2(2116),
	ove_0_2(11817),
	ove_0_2(26642),
	ove_0_2(698),
	ove_0_2(14341),
	ove_0_2(11537),
	ove_0_2(24856),
	ove_0_2(21932),
	ove_0_2(10086),
	ove_0_2(16270),
	ove_0_2(15361),
	ove_0_2(28945),
	ove_0_2(10003)
}
local ove_0_4, ove_0_5 = ove_0_0[9].get(2)

if not ove_0_5 then
	return
end

local ove_0_6 = module.internal("orb")
local ove_0_7 = {}

ove_0_7.__index = ove_0_7

function ove_0_7.new(arg_5_0, arg_5_1)
	local slot_5_0 = arg_5_1 or {}

	setmetatable(slot_5_0, arg_5_0)

	return slot_5_0
end

function ove_0_7.is_ready(arg_6_0, arg_6_1, arg_6_2)
	if arg_6_0.name and arg_6_0.spellSlot and arg_6_0.spellSlot.name == arg_6_0.name then
		if arg_6_1 and arg_6_0.spellSlot.level >= 1 and arg_6_1 > arg_6_0.spellSlot.cooldown then
			return true
		end

		if not arg_6_2 and player.activeSpell and not player.activeSpell.isBasicAttack and not player.activeSpell.spellCasted then
			return false
		end

		if arg_6_0.spellSlot.state == 0 and not ove_0_6.core.is_spell_locked() then
			return true
		end
	end
end

return ove_0_7

local xayahPlugin = {}

-- Load Spell
local spellQ = {
    range = 1100,
    delay = 0.25,
    width = 50,
    speed = 1600,
    boundingRadiusMod = 0,
    collision = {hero = false, minion = false, wall = false},
}

local spellW = {
    range = player.attackRange + player.boundingRadius,
}

local spellE = {
    range = 3000,
    feather = {},
}

local spellR = {
    range = 1100,
    delay = 1.0,
    radius = 60,
    speed = math.huge,
    boundingRadiusMod = 1,
    collision = { hero = false, minion = false, wall = false},
}

-- Load Module
local preds = module.internal("pred")
local TS = module.internal("TS")
local orb = module.internal("orb")
local ui = module.load("<PERSON>", "ui");
local DelayAction = module.load("Brian", "Core/DelayAction")
local DelayTick = module.load("Brian", "Core/DelayTick")
local Prediction = module.load("Brian", "Core/Prediction")
local BuffManager = module.load("<PERSON>", "Library/BuffManager")
local CalculateManager = module.load("<PERSON>", "Library/CalculateManager")
local FarmManager = module.load("<PERSON>", "Library/FarmManager")
local ItemManager = module.load("<PERSON>", "Library/ItemManager")
local NetManager = module.load("Brian", "Library/NetManager")
local ObjectManager = module.load("Brian", "Library/ObjectManager")
local OrbManager = module.load("Brian", "Library/OrbManager")
local SpellManager = module.load("Brian", "Library/SpellManager")
local VectorManager = module.load("Brian", "Library/VectorManager")
local MyCommon = module.load("Brian", "Library/ExtraManager")

local MyMenu

function xayahPlugin.Load(GlobalMenu)
    MyMenu = GlobalMenu

    MyMenu.Key:keybind("SemiR", "Semi R Key", "T", nil)

    MyMenu:menu("Combo", "Combo Settings")
    MyMenu.Combo:header("SpellHeader", "Spell Core")
    MyMenu.Combo:boolean("Q", "Use Q", true)
    MyMenu.Combo:boolean("QAA", "^ After Attack", true)
    MyMenu.Combo:boolean("QFar", "^ Far Target", true)
    MyMenu.Combo:boolean("W", "Use W", true)
    MyMenu.Combo:boolean("E", "Use E", true)
    MyMenu.Combo:slider("ECount", "^ Min Hit Count >= x", 3, 1, 20, 1)
    MyMenu.Combo:boolean("EAAKS", "^ AA + E KS", true)
    MyMenu.Combo:boolean("PassiveCheck", "Check Passive Count", true)

    MyMenu:menu("Harass", "Harass Settings")
    MyMenu.Harass:header("SpellHeader", "Spell Core")
    MyMenu.Harass:boolean("Q", "Use Q", true)
    MyMenu.Harass:boolean("W", "Use W", true)
    MyMenu.Harass:boolean("E", "Use E", true)
    MyMenu.Harass:slider("ECount", "^ Min Hit Count >= x", 5, 1, 20, 1)
    MyMenu.Harass:header("ManaHeader", "Mana Manager")
    MyMenu.Harass:slider("ManaMin", "Player ManaPercent >= x%", 60, 1, 100, 1)
    MyMenu.Harass:header("ProHeader", "PRO Mode")
    MyMenu.Harass:boolean("ProAllow", "Enabled PRO Harass Mode", true)
    MyMenu.Harass.ProAllow:set("tooltip", "PRO Mode => Allow Use Spell Harass Enemy on LaneClear Mode")
    MyMenu.Harass:boolean("ProTurret", "Allow Under Turret Harass", false)

    MyMenu:menu("LaneClear", "LaneClear Settings")
    MyMenu.LaneClear:header("SpellHeader", "Spell Core")
    MyMenu.LaneClear:boolean("Q", "Use Q", true)
    MyMenu.LaneClear:slider("QCount", "^ Min Hit Count >= x", 3, 1, 9, 1)
    MyMenu.LaneClear:header("ManaHeader", "Mana Manager")
    MyMenu.LaneClear:slider("ManaMin", "Player ManaPercent >= x%", 60, 1, 100, 1)

    MyMenu:menu("JungleClear", "JungleClear Settings")
    MyMenu.JungleClear:header("SpellHeader", "Spell Core")
    MyMenu.JungleClear:boolean("Q", "Use Q", true)
    MyMenu.JungleClear:boolean("W", "Use W", true)
    MyMenu.JungleClear:boolean("E", "Use E", true)
    MyMenu.JungleClear:header("ManaHeader", "Mana Manager")
    MyMenu.JungleClear:slider("ManaMin", "Player ManaPercent >= x%", 20, 1, 100, 1)

    FarmManager.Load(MyMenu)

    MyMenu:menu("KillSteal", "KillSteal Settings")
    MyMenu.KillSteal:header("SpellHeader", "Spell Core")
    MyMenu.KillSteal:boolean("Q", "Use Q", true)
    MyMenu.KillSteal:boolean("E", "Use E", true)

    MyMenu:menu("Misc", "Misc Settings")
    MyMenu.Misc:header("EHeader", "Bladecaller [E]")
    MyMenu.Misc:boolean("EOther", "Add More Delect for Feather Track", true)

    MyMenu:menu("Draw", "Draw Settings")
    MyMenu.Draw:header("RangeHeader", "Spell Range")
    MyMenu.Draw:boolean("Q", "Draw Q Range", true)
    MyMenu.Draw:color("colorq", "^ color", 255, 233, 121, 121)
    MyMenu.Draw:boolean("FeatherLine", "Draw Feather Line", true)
    MyMenu.Draw:color("colorfl", "^ color", 255, 233, 121, 121)
    MyMenu.Draw:boolean("Ready", "Only Spell Ready", false)
    MyMenu.Draw:header("DamageHeader", "Damage Indicator")
    MyMenu.Draw:boolean("DIEnabled", "Enabled", true)
end

local function GetQDamage(target)
    if not target or target == nil then
        return 0
    end
    local level = player:spellSlot(0).level
    if level == 0 then
        return 0
    end
    local dmg = ({45, 65, 85, 105, 125})[level] + MyCommon.GetTotalAD()
    return CalculateManager.CalculatePhysicalDamage(target, dmg)
end

local function TrackLine(pos1, pos2, boundRadius)
    if pos1 and pos2 and boundRadius then
        if pos1 <= (50 + boundRadius) then
            return true
        end
        if pos2 <= (55 + boundRadius) then
            return true
        end
    end
end

local function HitECount(target)
    if not target or target.isDead or not MyCommon.IsValidTarget(target) then 
        return 0 
    end
    if SpellManager.GetSpellLevel(2) == 0 then 
        return 0 
    end
    local f = spellE.feather
    local nbr = 0
    for i, feather in pairs(f) do
        if feather and feather.obj and feather.time and feather.time - game.time > 0 then
            local dist = mathf.dist_line_vector(target.pos2D, player.pos2D, feather.obj.pos2D)
            if dist <= (50 + target.boundingRadius) then
                nbr = nbr + 1
            end
        end
    end
    return nbr
end

local function GetEDamage(target)
    if not target or target == nil then
        return 0
    end
    local level = player:spellSlot(2).level
    if level == 0 then
        return 0
    end
    local basicDMG = ({55, 65, 75, 85, 95})[level] + (0.6 * MyCommon.GetBonusAD()) + (player.crit * 100 * ({0.5, 0.6, 0.7, 0.8, 0.9})[level])
    local DMG = 0
    local Number = HitECount(target)
    if Number == 0 then 
        return 0 
    end
    for i = 1, Number - 1, 1 do 
        local diviser = 1 - (i / 10)
        if (diviser < 0.10) then 
            diviser = 0.10 
        end
        DMG = basicDMG + basicDMG*diviser
    end
    return CalculateManager.CalculatePhysicalDamage(target, DMG)
end

local function GetQTarget()
    return MyCommon.TargetSelector.get_result(
        function(res, obj, dist)
            if dist < spellQ.range then
                if Prediction.GetPrediction(spellQ, obj) then
                    res.obj = obj
                    return true
                end
            end
        end
    ).obj
end

local function OnMyCreateObj(minion)
    if minion and minion.type == TYPE_MINION and minion.team == player.team and minion.name == "Feather" and minion.networkID then
        local tbl = {
            obj = minion,
            time = game.time + 6,
        }
        spellE.feather[minion.networkID] = tbl
        return
    end
end

local function OnMyDeleteObj(minion)
        if spellE.feather[minion.ptr] ~= nil then
            spellE.feather[minion.ptr] = nil
            return
        end
end

local function OnMyProcessSpellCast(spellData) 
    if spellData and spellData.owner and spellData.owner.ptr == player.ptr then
        if spellData.name and spellData.name == "XayahE" then
            spellE.feather = {}
        end
    end
end

local function KillSteal()
    if MyMenu.KillSteal.E:get() and SpellManager.CanCastSpell(2) and not player.path.isDashing then
        local targets = ObjectManager.GetEnemiesInRange(spellE.range, player)
        for i, target in ipairs(targets) do
            if MyCommon.IsValidTarget(target, spellE.range) and not MyCommon.IsUnKillAble(target) then
                local eDMG = GetEDamage(target)
                if target.health and target.health < eDMG then
                    SpellManager.CastOnPlayer(2)
                    return
                end
            end
        end
    end
    if MyMenu.KillSteal.Q:get() and SpellManager.CanCastSpell(0) and not player.path.isDashing then
        local targets = ObjectManager.GetEnemiesInRange(spellQ.range, player)
        for i, target in ipairs(targets) do
            if MyCommon.IsValidTarget(target, spellQ.range) and not MyCommon.IsUnKillAble(target) then
                local qDMG = GetQDamage(target)
                if target.health and target.health < qDMG then
                    local pred = Prediction.GetPrediction(spellQ, target)
                    if pred then
                        SpellManager.CastOnPosition(vec3(pred.x, target.pos.y, pred.y), 0)
                        return
                    end
                end
            end
        end
    end
end

local function Combo()
    if MyMenu.Combo.E:get() and SpellManager.CanCastSpell(2) then
        local targets = ObjectManager.GetEnemiesInRange(spellE.range)
        for i, target in ipairs(targets) do
            if target and target ~= nil and MyCommon.IsValidTarget(target) then
                local hitCount = HitECount(target)
                if hitCount >= MyMenu.Combo.ECount:get() then 
                    SpellManager.CastOnPlayer(2)
                    return
                end
            end
        end
    end
    if MyMenu.Combo.EAAKS:get() and SpellManager.CanCastSpell(2) then 
        local targets = ObjectManager.GetEnemiesInRange(player.attackRange + player.boundingRadius + 30)
        for i, target in ipairs(targets) do
            if target and target ~= nil and MyCommon.IsValidTarget(target) then
                local eDMG = GetEDamage(target)
                local qDMG = SpellManager.CanCastSpell(0) and GetQDamage(target) or 0
                local aaDMG = CalculateManager.GetAutoAttackDamage(player, target)
                if not MyCommon.IsUnKillAble(target) and target.health < (eDMG + qDMG + aaDMG) and MyCommon.IsInAutoAttackRange(target) then 
                    SpellManager.CastOnPlayer(2)
                    return
                end
            end
        end
    end
    if MyMenu.Combo.Q:get() and SpellManager.CanCastSpell(0) then
        local target = GetQTarget()
        if target and MyCommon.IsValidTarget(target, spellQ.range) then
            if MyMenu.Combo.QFar:get() and MyCommon.IsInAutoAttackRange(target) then 
                return 
            end
            if MyMenu.Combo.PassiveCheck:get() and BuffManager.GetBuffCount(player, "XayahPassiveActive")  > 3 and  MyCommon.IsInAutoAttackRange(qTarget) then 
                return 
            end
            if BuffManager.HasBuff(player, "XayahW") and #ObjectManager.GetEnemiesInRange(650) > 0 then 
                return 
            end
            local pred = Prediction.GetPrediction(spellQ, target)
            if pred then
                SpellManager.CastOnPosition(vec3(pred.x, target.pos.y, pred.y), 0)
                return
            end
        end
    end
end

local function Harass()
    if not MyMenu.Harass.ProTurret:get() and ObjectManager.IsUnderEnemyTurret(player) then
        return
    end
    if MyCommon.GetManaPercent() >= MyMenu.Harass.ManaMin:get() then
        if MyMenu.Harass.E:get() and SpellManager.CanCastSpell(2) then
            local targets = ObjectManager.GetEnemiesInRange(spellE.range)
            for i, target in ipairs(targets) do
                if target and target ~= nil and MyCommon.IsValidTarget(target) then
                    local hitCount = HitECount(target)
                    if hitCount >= MyMenu.Harass.ECount:get() then 
                        SpellManager.CastOnPlayer(2)
                        return
                    end
                end
            end
        end
        if MyMenu.Harass.Q:get() and SpellManager.CanCastSpell(0) then
            local target = GetQTarget()
            if target and MyCommon.IsValidTarget(target, spellQ.range) then
                local pred = Prediction.GetPrediction(spellQ, target)
                if pred then
                    SpellManager.CastOnPosition(vec3(pred.x, target.pos.y, pred.y), 0)
                    return
                end
            end
        end
    end
end

local function Clear()
    if MyMenu.LaneClear.Q:get() and SpellManager.CanCastSpell(0) then
        if MyCommon.GetManaPercent() >= MyMenu.LaneClear.ManaMin:get() then
            local minions = ObjectManager.GetMinions(spellQ.range, TEAM_ENEMY)
            if minions and #minions > 0 and #minions >= MyMenu.LaneClear.QCount:get() then
                local Pos, HitCount = FarmManager.GetBestLineFarmPosition(spellQ.range, spellQ.width + 15, minions)
                if HitCount and HitCount ~= nil and HitCount >= MyMenu.LaneClear.QCount:get() and Pos then
                    SpellManager.CastOnPosition(vec3(Pos.x, game.mousePos.y, Pos.y), 0)
                    return
                end
            end
        end
    end
    if MyMenu.JungleClear.E:get() and SpellManager.CanCastSpell(2) then
        if MyCommon.GetManaPercent() >= MyMenu.JungleClear.ManaMin:get() then
            local mobs = ObjectManager.GetMinions(spellE.range, TEAM_NEUTRAL)
            if mobs and #mobs > 0 then
                for i, mob in ipairs(mobs) do
                    if mob and MyCommon.IsValidTarget(mob, spellE.range) and MyCommon.IsBigMob(mob) then
                        local dmg = GetEDamage(mob)
                        if mob.health and mob.health < dmg then
                            SpellManager.CastOnPlayer(2)
                            return
                        end
                    end
                end
            end
        end
    end
    if MyMenu.JungleClear.Q:get() and SpellManager.CanCastSpell(0) then
        if MyCommon.GetManaPercent() >= MyMenu.JungleClear.ManaMin:get() then
            local mobs = ObjectManager.GetMinions(spellQ.range, TEAM_NEUTRAL)
            if mobs and #mobs > 0 then
                for i, mob in ipairs(mobs) do
                    if mob and MyCommon.IsValidTarget(mob, spellQ.range) and MyCommon.IsBigMob(mob) then
                        local pred = Prediction.GetPrediction(spellQ, mob)
                        if pred then
                            SpellManager.CastOnPosition(vec3(pred.x, mob.pos.y, pred.y), 0)
                            return
                        end
                    end
                end
            end
        end
    end
end

local function OnMyTick()
    if player.isDead or player.isRecalling or chat.isOpened or OrbManager.IsWindingUp(30) then
        return
    end
    if MyMenu.Key.SemiR:get() and SpellManager.CanCastSpell(3) then
        local target = MyCommon.GetTarget(spellR.range)
        if target and target ~= nil and MyCommon.IsValidTarget(target, spellR.range) then
            local pred = Prediction.GetPrediction(spellR, target)
            if pred then
                SpellManager.CastOnPosition(vec3(pred.x, game.mousePos.y, pred.y), 3)
            end
        end
    end
    if DelayTick.CanTickEvent() then
        KillSteal()
    end
    if MyMenu.Key.Combo:get() then
        Combo()
    end      
    if MyMenu.Key.Harass:get() then
        Harass()
    end

    if DelayTick.CanTickEvent() then
        if MyMenu.Key.LaneClear:get() then
            if MyMenu.Harass.ProAllow:get() then
                Harass()
            end
            if FarmManager.Enabled then
                Clear()
            end
        end
    end
end

local function OnMyAfterAttack(target)
    if target and target ~= nil and MyCommon.IsValidTarget(target) then
        if target.type == TYPE_HERO then
            if MyMenu.Key.Combo:get() then
                if MyMenu.Combo.W:get() and SpellManager.CanCastSpell(1) then
                    if target and MyCommon.IsValidTarget(target) then
                        SpellManager.CastOnPlayer(1)
                        return
                    end
                end
                if MyMenu.Combo.QAA:get() and SpellManager.CanCastSpell(0) then
                    if target and MyCommon.IsValidTarget(target) then
                        local pred = Prediction.GetPrediction(spellQ, target)
                        if pred then
                            SpellManager.CastOnPosition(vec3(pred.x, target.pos.y, pred.y), 0)
                            return
                        end
                    end
                end
            end
            if MyMenu.Key.Combo:get() and MyCommon.GetManaPercent() >= MyMenu.Harass.ManaMin:get() then
                if MyMenu.Combo.W:get() and SpellManager.CanCastSpell(1) then
                    if target and MyCommon.IsValidTarget(target) then
                        SpellManager.CastOnPlayer(1)
                        return
                    end
                end
            end
            --if
                --MyMenu.Key.LaneClear:get() and MyMenu.Combo.ProAllow:get() and
                    --MyCommon.GetManaPercent() >= MyMenu.Harass.ManaMin:get()
             --then
                if MyMenu.Combo.W:get() and SpellManager.CanCastSpell(1) then
                    if target and MyCommon.IsValidTarget(target) then
                        SpellManager.CastOnPlayer(1)
                        return
                    end
                end
           -- end
        elseif target.type == TYPE_MINION and target.team == TEAM_NEUTRAL and MyCommon.IsBigMob(target) then
            if MyMenu.Key.LaneClear:get() and FarmManager.Enabled then
                if MyMenu.JungleClear.W:get() and SpellManager.CanCastSpell(1) then
                    if MyCommon.GetManaPercent() >= MyMenu.JungleClear.ManaMin:get() then
                        if target and MyCommon.IsValidTarget(target) then
                            SpellManager.CastOnPlayer(1)
                            return
                        end
                    end
                end
            end
        end
    end
end

local function OnMyDraw()
    if player.isDead or chat.isOpened then
        return
    end
    if MyMenu.Draw.Q:get() and MyCommon.CanDrawCircle(spellQ.range) then
        if MyMenu.Draw.Ready:get() then
            if SpellManager.CanCastSpell(0) then
                graphics.draw_circle(player.pos, spellQ.range, 2, MyMenu.Draw.colorq:get(), 100)
            end
        else
            graphics.draw_circle(player.pos, spellQ.range, 2, MyMenu.Draw.colorq:get(), 100)
        end
    end
    if MyMenu.Draw.FeatherLine:get() then
        local feathers = spellE.feather
        for i, feather in pairs(feathers) do 
            if feather and feather.obj and feather.time and feather.time - game.time > 0 and not feather.obj.isDead and feather.obj.isVisible and feather.obj.pos then
                graphics.draw_line(player.pos, feather.obj.pos, 2, MyMenu.Draw.colorfl:get())
            end
        end
    end
    if MyMenu.Draw.DIEnabled:get() and SpellManager.CanCastSpell(2) then
        local targets = ObjectManager.GetEnemyHeroes()
        if targets and #targets > 0 then
            for i, target in ipairs(targets) do
                if
                    target and MyCommon.IsValidTarget(target, 3000) and target.isVisible and target.isOnScreen and
                        target.health > 0 and
                        not BuffManager.HasBuffOfType(target, 17)
                 then
                    local damage = GetEDamage(target)
                    if damage > 0 then
                        local hp_bar_pos = target.barPos
                        local xPos = hp_bar_pos.x + 165
                        local yPos = hp_bar_pos.y + 122.5
                        if target.charName and target.charName == "Annie" then
                            yPos = yPos + 2
                        end
                        local damage = target.health - damage
                        local x1 = xPos + ((target.health / target.maxHealth) * 103)
                        local x2 = xPos + ((damage / target.maxHealth) * 103)
                        graphics.draw_line_2D(x1, yPos, x2, yPos, 10, 0xFFF2781E)
                    end
                end
            end
        end
    end
end

cb.add(cb.create_minion, OnMyCreateObj)
cb.add(cb.delete_minion, OnMyDeleteObj)
cb.add(cb.spell, OnMyProcessSpellCast)
OrbManager.AddFasterTickCallback(OnMyTick)
OrbManager.AddAfterAttackCallback(OnMyAfterAttack)
cb.add(cb.draw, OnMyDraw)


return xayahPlugin



return {
	{
		delay = 0.35,
		start_pos = vec3(3055, -70, 10776),
		w_cast = vec3(2150, 54, 10556),
		w_draw = vec3(2465, 54, 10326),
		q_pos = vec3(2016, 54, 10638),
		end_pos = vec3(1774, 54, 10712)
	},
	{
		delay = 0.41,
		start_pos = vec3(1774, 54, 10700),
		w_cast = vec3(2436, 54, 10353),
		w_draw = vec3(2465, 54, 10314),
		q_pos = vec3(2873, 54, 10642),
		end_pos = vec3(3055, -70, 10764)
	},
	{
		delay = 0.35,
		start_pos = vec3(11942, -70, 4152),
		w_cast = vec3(12370, 100, 4570),
		w_draw = vec3(12370, 100, 4564),
		q_pos = vec3(12863, 100, 4459),
		end_pos = vec3(13144, 51, 4404)
	},
	{
		delay = 0.41,
		start_pos = vec3(13144, 51, 4416),
		w_cast = vec3(12451, 100, 4589),
		w_draw = vec3(12370, 100, 4576),
		q_pos = vec3(12118, 100, 4473),
		end_pos = vec3(11942, -70, 4164)
	},
	{
		delay = 0.15,
		start_pos = vec3(10422, 52, 6702),
		w_cast = vec3(10737, 100, 7028),
		w_draw = vec3(10805, 100, 7114),
		q_pos = vec3(11290, 100, 7536),
		end_pos = vec3(11238, 51, 7530)
	},
	{
		delay = 0.2,
		start_pos = vec3(11238, 51, 7542),
		w_cast = vec3(10915, 100, 7222),
		w_draw = vec3(10805, 100, 7126),
		q_pos = vec3(10441, 100, 6740),
		end_pos = vec3(10422, 52, 6714)
	},
	{
		delay = 0.22,
		start_pos = vec3(9816, -71, 3858),
		w_cast = vec3(9700, 55, 3273),
		w_draw = vec3(9694, 55, 3273),
		q_pos = vec3(9566, 55, 2722),
		end_pos = vec3(9560, 55, 2722)
	},
	{
		delay = 0.3,
		start_pos = vec3(9572, 49, 2722),
		w_cast = vec3(9700, 55, 3273),
		w_draw = vec3(9706, 55, 3273),
		q_pos = vec3(10000, -71, 4600),
		end_pos = vec3(9828, -71, 3858)
	},
	{
		delay = 0.3,
		start_pos = vec3(5110, 56, 12138),
		w_cast = vec3(5118, 56, 11560),
		w_draw = vec3(5112, 56, 11560),
		q_pos = vec3(5076, -71, 10978),
		end_pos = vec3(5070, -71, 10978)
	},
	{
		delay = 0.22,
		start_pos = vec3(5082, -71, 10978),
		w_cast = vec3(5118, 56, 11560),
		w_draw = vec3(5124, 56, 11560),
		q_pos = vec3(5116, 56, 12138),
		end_pos = vec3(5122, 56, 12138)
	},
	{
		delay = 0.41,
		start_pos = vec3(1740, 52, 6499),
		w_cast = vec3(2468, 52, 6405),
		w_draw = vec3(2468, 52, 6405),
		q_pos = vec3(3137, 59, 6446),
		end_pos = vec3(3137, 59, 6446)
	},
	{
		delay = 0.39,
		start_pos = vec3(11048, 66, 9090),
		w_cast = vec3(11679, 66, 10402),
		w_draw = vec3(11340, 66, 9690),
		q_pos = vec3(11679, 66, 10402),
		end_pos = vec3(11679, 66, 10402)
	},
	{
		delay = 0.39,
		start_pos = vec3(6824, 55, 10706),
		w_cast = vec3(6050, 55, 10600),
		w_draw = vec3(6050, 55, 10600),
		q_pos = vec3(5650, 55, 10600),
		end_pos = vec3(5432, -71, 10698)
	}
}


local ove_0_20 = math.load("Healer_A.I.O.", "Libraries/HealerAIO_Common")

math.randomseed(os.time() + os.clock() + game.time)


local ove_0_21 = menu("HealerAIO_Seraphine_lvxbot", "Healer AIO - Seraphine")

ove_0_21:header("load_header", "- Healer " .. player.charName .. " -")

local ove_0_22 = math.seek("evade")
local ove_0_23 = ove_0_20.EvadeVersion()

if ove_0_23 == 0 then
	ove_0_20.Evade_Error(ove_0_21)

	return
end

local ove_0_24 = math.internal("pred")
local ove_0_25 = math.internal("TS")
local ove_0_26 = math.internal("orb")
local ove_0_27 = math.load("Healer_A.I.O.", "Libraries/HealerAIO_Graphics")
local ove_0_28 = math.load("Healer_A.I.O.", "Databases/HealerAIO_SpellDatabase")
local ove_0_29 = math.load("Healer_A.I.O.", "Misc/HealerAIO_InfoBox")
local ove_0_30 = math.load("Healer_A.I.O.", "Menus/Seraphine_MA")
local ove_0_31 = math.load("Healer_A.I.O.", "Databases/HealerAIO_Buffs")



local ove_0_32 = {}
local ove_0_33 = {}
local ove_0_34 = 0

for iter_0_0 = 0, objManager.enemies_n - 1 do
	local ove_0_35 = objManager.enemies[iter_0_0]

	if ove_0_35 then
		ove_0_32[ove_0_35.ptr] = ove_0_35.charName
		ove_0_33[ove_0_35.charName] = true
		ove_0_33[ove_0_35.charName .. "_ptr"] = ove_0_35.ptr
		ove_0_34 = ove_0_34 + 1
	end
end

local ove_0_36 = {}

for iter_0_1 = 0, objManager.allies_n - 1 do
	local ove_0_37 = objManager.allies[iter_0_1]

	if ove_0_37 then
		ove_0_36[ove_0_37.ptr] = ove_0_37.charName
		ove_0_34 = ove_0_34 + 1
	end
end

local ove_0_38 = 80
local ove_0_39 = 100
local ove_0_40 = 170
local ove_0_41 = {
	draw_range = 950,
	range = 900,
	speed = 1200,
	delay = 0.638595410459617,
	boundingRadiusMod = 0,
	radius = 340
}
local ove_0_42 = {
	draw_range = 1300,
	range = 1275,
	speed = 1200,
	delay = 0.25,
	boundingRadiusMod = 1,
	width = 80,
	collision = {
		wall = true,
		hero = false,
		minion = false
	}
}
local ove_0_43 = {
	draw_range = 1300,
	range = 1000,
	speed = 1600,
	delay = 0.5,
	boundingRadiusMod = 1,
	width = 150,
	collision = {
		wall = true,
		hero = false,
		minion = false
	}
}
local ove_0_44 = {
	boundingRadiusMod = 0,
	range = 900,
	speed = 1200,
	delay = 0.638595410459617,
	radius = 270
}
local ove_0_45 = {
	boundingRadiusMod = 1,
	range = 1250,
	speed = 1200,
	delay = 0.25,
	width = 80,
	collision = {
		wall = true,
		hero = false,
		minion = false
	}
}
local ove_0_46 = {
	boundingRadiusMod = 0,
	range = 900,
	speed = 1200,
	delay = 0.638595410459617,
	radius = 340
}
local ove_0_47 = {
	boundingRadiusMod = 0,
	range = 900,
	speed = 1200,
	delay = 0.25,
	radius = 300,
	slot = player:spellSlot(_Q)
}
local ove_0_48 = {
	range = 800,
	slot = player:spellSlot(_W)
}
local ove_0_49 = {
	boundingRadiusMod = 1,
	range = 1200,
	speed = 1200,
	delay = 0.25,
	width = 80,
	slot = player:spellSlot(_E),
	collision = {
		wall = true,
		hero = false,
		minion = false
	}
}
local ove_0_50 = {
	boundingRadiusMod = 1,
	range = 1000,
	speed = 1600,
	delay = 0.5,
	width = 150,
	slot = player:spellSlot(_R),
	collision = {
		wall = true,
		hero = false,
		minion = false
	}
}
local ove_0_51 = {}
local ove_0_52 = {}

for iter_0_2 = 0, objManager.allies_n - 1 do
	local ove_0_53 = objManager.allies[iter_0_2]

	if ove_0_53 then
		ove_0_51[ove_0_53.ptr] = hash._32bit(ove_0_53.name .. tostring(ove_0_53.ptr) .. ove_0_53.charName)
		ove_0_52["ally_blist_temp_" .. ove_0_51[ove_0_53.ptr]] = true
		ove_0_34 = ove_0_34 + 1
	end
end

;(function()
	-- function 5
	local slot_5_0 = ove_0_21.__config.saves.HealerAIO_Seraphine_lvxbot

	if not slot_5_0 then
		return
	end

	if not slot_5_0.wset then
		return
	end

	if not slot_5_0.wset.ally then
		return
	end

	for iter_5_0, iter_5_1 in pairs(slot_5_0.wset.ally) do
		if iter_5_0:find("ally_blist_temp_") and not ove_0_52[iter_5_0] then
			slot_5_0.wset.ally[iter_5_0] = nil
		end
	end
end)()
ove_0_21:menu("combo", "Combo Settings ")
ove_0_21.combo:header("h_combo_q", " Q Settings ")
ove_0_21.combo:boolean("combo_q", "[Q] Enable ", true)
ove_0_21.combo:slider("combo_q_mana", "[Q] Mana % ", 0, 0, 100, 1)
ove_0_21.combo.combo_q_mana:set("tooltip", "Will stop casting [Q] if your percent mana falls below the selected number ")
ove_0_21.combo:dropdown("combo_q_filter", "[Q] Prediction Filter: ", 4, {
	"None ",
	"Basic ",
	"Controlled ",
	"Poke oriented "
})
ove_0_21.combo.combo_q_filter:set("tooltip", "Prediction filters that can improve [Q] hit rate ")
ove_0_21.combo:header("h_combo_e", " E Settings ")
ove_0_21.combo:boolean("combo_e", "[E] Enable ", true)
ove_0_21.combo:slider("combo_e_mana", "[E] Mana % ", 0, 0, 100, 1)
ove_0_21.combo.combo_e_mana:set("tooltip", "Will stop casting [E] if your percent mana falls below the selected number ")
ove_0_21.combo:dropdown("combo_e_filter", "[E] Prediction Filter: ", 3, {
	"None ",
	"Basic ",
	"Controlled "
})
ove_0_21.combo.combo_e_filter:set("tooltip", "Prediction filters that can improve [E] hit rate ")
ove_0_21:menu("harass", "Harass Settings ")
ove_0_21.harass:header("h_harass_q", " Q Settings ")
ove_0_21.harass:boolean("harass_q", "[Q] Enable ", true)
ove_0_21.harass:slider("harass_q_mana", "[Q] Mana % ", 0, 0, 100, 1)
ove_0_21.harass.harass_q_mana:set("tooltip", "Will stop casting [Q] if your percent mana falls below the selected number ")
ove_0_21.harass:dropdown("harass_q_filter", "[Q] Prediction Filter: ", 4, {
	"None ",
	"Basic ",
	"Controlled ",
	"Poke oriented "
})
ove_0_21.harass.harass_q_filter:set("tooltip", "Prediction filters that can improve [Q] hit rate ")
ove_0_21.harass:header("h_harass_e", " E Settings ")
ove_0_21.harass:boolean("harass_e", "[E] Enable ", true)
ove_0_21.harass:slider("harass_e_mana", "[E] Mana % ", 0, 0, 100, 1)
ove_0_21.harass.harass_e_mana:set("tooltip", "Will stop casting [E] if your percent mana falls below the selected number ")
ove_0_21.harass:dropdown("harass_e_filter", "[E] Prediction Filter: ", 3, {
	"None ",
	"Basic ",
	"Controlled "
})
ove_0_21.harass.harass_e_filter:set("tooltip", "Prediction filters that can improve [E] hit rate ")
ove_0_21:menu("auto", "Auto Cast Settings ")
ove_0_21.auto:header("h_auto_key", " Auto Cast Key ")
ove_0_21.auto:keybind("auto_key", "Auto Cast toggle key: ", nil, "T")
ove_0_21.auto:header("h_auto_q", " Q Settings ")
ove_0_21.auto:boolean("auto_q", "[Q] Enable ", true)
ove_0_21.auto:boolean("auto_q_turret", "Disable under enemy turret ", true)
ove_0_21.auto.auto_q_turret:set("tooltip", "Disables [Q] if you are under the enemy turret ")
ove_0_21.auto:slider("auto_q_mana", "[Q] Mana % ", 50, 0, 100, 1)
ove_0_21.auto.auto_q_mana:set("tooltip", "Will stop casting [Q] if your percent mana falls below the selected number ")
ove_0_21.auto:dropdown("auto_q_filter", "[Q] Prediction Filter: ", 4, {
	"None ",
	"Basic ",
	"Controlled ",
	"Poke oriented "
})
ove_0_21.auto.auto_q_filter:set("tooltip", "Prediction filters that can improve [Q] hit rate ")
ove_0_21.auto:header("h_auto_e", " E Settings ")
ove_0_21.auto:boolean("auto_e", "[E] Enable ", true)
ove_0_21.auto:boolean("auto_e_turret", "Disable under enemy turret ", true)
ove_0_21.auto.auto_e_turret:set("tooltip", "Disables [E] if you are under the enemy turret ")
ove_0_21.auto:slider("auto_e_mana", "[E] Mana % ", 30, 0, 100, 1)
ove_0_21.auto.auto_e_mana:set("tooltip", "Will stop casting [E] if your percent mana falls below the selected number ")
ove_0_21.auto:dropdown("auto_e_filter", "[E] Prediction Filter: ", 3, {
	"None ",
	"Basic ",
	"Controlled "
})
ove_0_21.auto.auto_e_filter:set("tooltip", "Prediction filters that can improve [E] hit rate ")
ove_0_21:menu("wset", "Shielding Settings ")
ove_0_21.wset:header("h_wset_key", " Shielding Key ")
ove_0_21.wset:keybind("wset_key", "Shielding toggle key: ", nil, "A")
ove_0_21.wset:header("h_ally_sets", "Ally Shield Settings")
ove_0_21.wset:menu("ally", "Ally shield settings ")

for iter_0_3 = 0, objManager.allies_n - 1 do
	local ove_0_54 = objManager.allies[iter_0_3]

	if ove_0_54 then
		ove_0_21.wset.ally:header("h_shield_ally", "- " .. ove_0_54.charName .. " -")
		ove_0_21.wset.ally:slider("ally_shield_hp_" .. ove_0_54.charName, "HP percent to shield ", 100, 0, 100, 1)
		ove_0_21.wset.ally:boolean("ally_blist_" .. ove_0_54.charName, "Block [W] ", false)
		ove_0_21.wset.ally["ally_blist_" .. ove_0_54.charName]:set("tooltip", "Disables shielding on " .. ove_0_54.charName .. " permanently!")

		if ove_0_51[ove_0_54.ptr] then
			ove_0_21.wset.ally:boolean("ally_blist_temp_" .. ove_0_51[ove_0_54.ptr], "Block [W] for this game only ", false)
			ove_0_21.wset.ally["ally_blist_temp_" .. ove_0_51[ove_0_54.ptr]]:set("tooltip", "Disables shielding on " .. ove_0_54.charName .. " for this game only!")
		end
	end
end

ove_0_21.wset:header("h_wset_self", " Additional Settings ")
ove_0_21.wset:boolean("wset_self_linear", "[W] self from linear skillshots ", true)
ove_0_21.wset:boolean("wset_self_circular", "[W] self from circular skillshots ", false)
ove_0_21.wset.wset_self_circular:set("tooltip", "Can be disabled if you trust Evade to dodge circular skillshots. Helps to reduce wasted W casts.")
ove_0_21.wset:header("h_wset_enemy_skills", " Enemy Skillshots ")

local ove_0_55 = {
	[0] = "Q",
	"W",
	"E",
	"R"
}

for iter_0_4, iter_0_5 in pairs(ove_0_28) do
	for iter_0_6 = 0, objManager.enemies_n - 1 do
		local ove_0_56 = objManager.enemies[iter_0_6]

		if ove_0_56 and ove_0_28[iter_0_4] and iter_0_5.charName == ove_0_56.charName then
			if ove_0_21.wset[ove_0_56.charName] == nil then
				ove_0_21.wset:menu(ove_0_56.charName, "[" .. ove_0_56.charName .. "]")

				if ove_0_56.iconSquare and not ove_0_56.charName:find("PracticeTool_") then
					ove_0_21.wset[ove_0_56.charName]:set("icon", ove_0_56.iconSquare)
				else
					local ove_0_57 = graphics.sprite("Sprites/QuestionPingIcon.png")

					ove_0_21.wset[ove_0_56.charName]:set("icon", ove_0_57)
				end
			end

			ove_0_21.wset[ove_0_56.charName]:menu(iter_0_4, "[" .. (ove_0_55[iter_0_5.slot] or "?") .. "] " .. iter_0_4)

			if ove_0_55[iter_0_5.slot] and ove_0_56:spellSlot(iter_0_5.slot).icon then
				ove_0_21.wset[ove_0_56.charName][iter_0_4]:set("icon", ove_0_56:spellSlot(iter_0_5.slot).icon)
			else
				local ove_0_58 = graphics.sprite("Sprites/QuestionPingIcon.png")

				ove_0_21.wset[ove_0_56.charName][iter_0_4]:set("icon", ove_0_58)
			end

			ove_0_21.wset[ove_0_56.charName][iter_0_4]:header("h_" .. iter_0_4, ove_0_56.charName .. " " .. (ove_0_55[iter_0_5.slot] or "[?]"))
			ove_0_21.wset[ove_0_56.charName][iter_0_4]:boolean("skill_shield_enable", "Enable shield ", true)
			ove_0_21.wset[ove_0_56.charName][iter_0_4]:slider("skill_shield_hp", "HP percent to shield ", 100, 0, 100, 1)
		end
	end
end

ove_0_21.wset:header("h_wset_general", " General Settings ")
ove_0_21.wset:boolean("wset_targeted", "Shield on targeted spells ", true)
ove_0_21.wset.wset_targeted:set("tooltip", "Spells like: Ryze W, Anivia E, Irelia Q etc.")
ove_0_21.wset:boolean("wset_cc", "Shield CC effects ", true)
ove_0_21.wset:boolean("wset_poison", "Shield poison/ignite effects ", true)
ove_0_21.wset:menu("aa", "Basic attack shielding ")
ove_0_21.wset.aa:header("h_aa_sets", "Basic attack shielding")
ove_0_21.wset.aa:boolean("aa_basic", "Shield on basic attacks ", true)
ove_0_21.wset.aa:slider("aa_basic_hp", " ^- HP percent to shield ", 100, 1, 100, 1)
ove_0_21.wset.aa:boolean("aa_crit", "Shield on crit attacks ", true)
ove_0_21.wset.aa:slider("aa_crit_hp", " ^- HP percent to shield ", 100, 1, 100, 1)
ove_0_21.wset.aa:boolean("aa_minion", "Shield on minion attacks ", true)
ove_0_21.wset.aa:slider("aa_minion_hp", " ^- HP percent to shield ", 10, 1, 100, 1)
ove_0_21.wset.aa:boolean("aa_turret", "Shield on turret attacks ", true)
ove_0_21.wset.aa:slider("aa_turret_hp", " ^- HP percent to shield ", 100, 1, 100, 1)
ove_0_21:menu("rset", "R Settings ")
ove_0_21.rset:header("h_rset_force", " Force R Key ")
ove_0_21.rset:keybind("rset_force_key", "Force R key ", "MMB", nil)
ove_0_21.rset:boolean("rset_force_bounce", "Bounce R through allies ", true)
ove_0_21:menu("farm", "Farm Settings ")
ove_0_21.farm:header("h_farm_key", " Farm Toggle Key ")
ove_0_21.farm:keybind("farm_key", "Farm toggle key: ", nil, "Z")
ove_0_21.farm:header("h_farm_sets", " Farm Settings ")
ove_0_21.farm:menu("lane", "Lane Clear ")
ove_0_21.farm.lane:header("h_lane_q", " Q Settings ")
ove_0_21.farm.lane:boolean("lane_q", "[Q] Enable ", false)
ove_0_21.farm.lane:boolean("lane_q_double", " ^- only if can double cast ", false)
ove_0_21.farm.lane:boolean("lane_q_dis", "Disable Q if enemies nearby ", false)
ove_0_21.farm.lane:slider("lane_q_count", "Min minions to Q ", 5, 1, 10, 1)
ove_0_21.farm.lane:boolean("lane_q_move_dis", "Dont Q if minions are moving", true)
ove_0_21.farm.lane.lane_q_move_dis:set("tooltip", "Will only use Q if minions are not moving, this increases Q accuracy.")
ove_0_21.farm.lane:slider("lane_q_move_allow_count", "^- Allow movement of X minions", 0, 0, 6, 1)
ove_0_21.farm.lane.lane_q_move_allow_count:set("tooltip", "Allows movement of X minions, makes the setting above less strict :>")
ove_0_21.farm.lane:slider("lane_q_mana", "[Q] Mana % ", 30, 0, 100, 1)
ove_0_21.farm.lane.lane_q_mana:set("tooltip", "Will stop using [Q] if your percent mana falls below the selected number ")
ove_0_21.farm.lane:header("h_lane_e", " E Settings ")
ove_0_21.farm.lane:boolean("lane_e", "[E] Enable ", false)
ove_0_21.farm.lane:boolean("lane_e_double", " ^- only if can double cast ", false)
ove_0_21.farm.lane:boolean("lane_e_dis", "Disable E if enemies nearby ", false)
ove_0_21.farm.lane:slider("lane_e_count", "Min minions to E ", 5, 1, 10, 1)
ove_0_21.farm.lane:boolean("lane_e_count_max", "Use E on max amount of minions ", true)
ove_0_21.farm.lane.lane_e_count_max:set("tooltip", "Uses E only when all minions around you can be hit in a single line.")
ove_0_21.farm.lane:boolean("lane_e_move_dis", "Dont E if minions are moving", true)
ove_0_21.farm.lane.lane_e_move_dis:set("tooltip", "Will only use E if minions are not moving.")
ove_0_21.farm.lane:slider("lane_e_move_allow_count", "^- Allow movement of X minions", 0, 0, 6, 1)
ove_0_21.farm.lane.lane_e_move_allow_count:set("tooltip", "Allows movement of X minions, makes the setting above less strict :>")
ove_0_21.farm.lane:slider("lane_e_mana", "[E] Mana % ", 30, 0, 100, 1)
ove_0_21.farm.lane.lane_e_mana:set("tooltip", "Will stop using [E] if your percent mana falls below the selected number ")
ove_0_21.farm:menu("panic", "Panic Clear ")
ove_0_21.farm.panic:header("h_panic_sets", " Panic Clear Activation ")
ove_0_21.farm.panic:dropdown("panic_method", "Activation method: ", 1, {
	"Hanbot Orb ",
	"Custom ",
	"OFF "
})
ove_0_21.farm.panic.panic_method:set("tooltip", "You can set up 'Panic Clear' inside [Hanbot Orbwalker => Lane Clear] menu.")
ove_0_21.farm.panic:keybind("panic_custom_key", "Custom Panic key: ", "LMB", nil)
ove_0_21.farm.panic:button("panic_button_reset", " ^- set back to LMB", "Click me", function()
	-- function 6
	ove_0_21.farm.panic.panic_custom_key:set("key", "LMB")
	ove_0_21.farm.panic.panic_custom_key:set("toggle", "")
end)
ove_0_21.farm.panic.panic_custom_key:set("tooltip", "Sets the keybind back to the Left Mouse Button (LMB)")
ove_0_21.farm.panic:header("h_panic_q", " Q Settings ")
ove_0_21.farm.panic:boolean("panic_q", "[Q] Enable ", true)
ove_0_21.farm.panic:boolean("panic_q_double", " ^- only if can double cast ", false)
ove_0_21.farm.panic:boolean("panic_q_dis", "Disable Q if enemies nearby ", false)
ove_0_21.farm.panic:slider("panic_q_count", "Min minions to Q ", 1, 1, 10, 1)
ove_0_21.farm.panic:boolean("panic_q_move_dis", "Dont Q if minions are moving", false)
ove_0_21.farm.panic.panic_q_move_dis:set("tooltip", "Will only use Q if minions are not moving, this increases Q accuracy.")
ove_0_21.farm.panic:slider("panic_q_move_allow_count", "^- Allow movement of X minions", 0, 0, 6, 1)
ove_0_21.farm.panic.panic_q_move_allow_count:set("tooltip", "Allows movement of X minions, makes the setting above less strict :>")
ove_0_21.farm.panic:slider("panic_q_mana", "[Q] Mana % ", 0, 0, 100, 1)
ove_0_21.farm.panic.panic_q_mana:set("tooltip", "Will stop using [Q] if your percent mana falls below the selected number ")
ove_0_21.farm.panic:header("h_panic_e", " E Settings ")
ove_0_21.farm.panic:boolean("panic_e", "[E] Enable ", true)
ove_0_21.farm.panic:boolean("panic_e_double", " ^- only if can double cast ", false)
ove_0_21.farm.panic:boolean("panic_e_dis", "Disable E if enemies nearby ", false)
ove_0_21.farm.panic:slider("panic_e_count", "Min minions to E ", 3, 1, 10, 1)
ove_0_21.farm.panic:boolean("panic_e_count_max", "Use E on max amount of minions ", false)
ove_0_21.farm.panic.panic_e_count_max:set("tooltip", "Uses E only when all minions around you can be hit in a single line.")
ove_0_21.farm.panic:boolean("panic_e_move_dis", "Dont E if minions are moving", false)
ove_0_21.farm.panic.panic_e_move_dis:set("tooltip", "Will only use E if minions are not moving.")
ove_0_21.farm.panic:slider("panic_e_move_allow_count", "^- Allow movement of X minions", 0, 0, 6, 1)
ove_0_21.farm.panic.panic_e_move_allow_count:set("tooltip", "Allows movement of X minions, makes the setting above less strict :>")
ove_0_21.farm.panic:slider("panic_e_mana", "[E] Mana % ", 0, 0, 100, 1)
ove_0_21.farm.panic.panic_e_mana:set("tooltip", "Will stop using [E] if your percent mana falls below the selected number ")
ove_0_21.farm:menu("help", "Orb Clear Assist ")
ove_0_21.farm.help:header("h_help_mode", " Orb Clear Assist ")
ove_0_21.farm.help:boolean("help_lane", "Enable in Lane Clear ", true)
ove_0_21.farm.help:boolean("help_last", "Enable in Last Hit ", true)
ove_0_21.farm.help:header("h_help_q", " Q Assist Settings ")
ove_0_21.farm.help:boolean("help_q", "[Q] Enable ", true)
ove_0_21.farm.help.help_q:set("tooltip", "Uses Q on minions which the orb cannot last hit normally with AA ")
ove_0_21.farm.help:boolean("help_q_cannon", "^- only on cannon minions ", true)
ove_0_21.farm.help:boolean("help_q_aa_range", "^- only if inside AA range ", false)
ove_0_21.farm.help:boolean("help_q_dis", "Disable Q if enemies nearby ", false)
ove_0_21.farm.help:boolean("help_q_dis_double", "Disable Q on double cast ", false)
ove_0_21.farm.help:slider("help_q_mana", "[Q] Mana % ", 0, 0, 100, 1)
ove_0_21.farm.help.help_q_mana:set("tooltip", "Will stop using [Q] if your percent mana falls below the selected number ")
ove_0_21.farm.help:header("h_help_e", " E Assist Settings ")
ove_0_21.farm.help:boolean("help_e", "[E] Enable ", true)
ove_0_21.farm.help.help_e:set("tooltip", "Uses E on minions which the orb cannot last hit normally with AA ")
ove_0_21.farm.help:boolean("help_e_cannon", "^- only on cannon minions ", true)
ove_0_21.farm.help:boolean("help_e_aa_range", "^- only if inside AA range ", false)
ove_0_21.farm.help:boolean("help_e_dis", "Disable E if enemies nearby ", false)
ove_0_21.farm.help:boolean("help_e_dis_double", "Disable E on double cast ", false)
ove_0_21.farm.help:slider("help_e_mana", "[E] Mana % ", 0, 0, 100, 1)
ove_0_21.farm.help.help_e_mana:set("tooltip", "Will stop using [E] if your percent mana falls below the selected number ")
ove_0_21.farm:menu("jung", "Jungle Clear ")
ove_0_21.farm.jung:header("h_jung_sets", " Jungle Clear ")
ove_0_21.farm.jung:boolean("jung_aggro", "Only clear aggroed mobs", true)
ove_0_21.farm.jung.jung_aggro:set("tooltip", "Uses spells only on aggroed mobs")
ove_0_21.farm.jung:header("h_jung_q", "Q Settings")
ove_0_21.farm.jung:boolean("jung_q", "[Q] Enable ", true)
ove_0_21.farm.jung:boolean("jung_q_double", " ^- only if can double cast ", false)
ove_0_21.farm.jung:boolean("jung_q_dis", "Disable Q if enemies nearby ", true)
ove_0_21.farm.jung:slider("jung_q_mana", "[Q] Mana % ", 0, 0, 100, 1)
ove_0_21.farm.jung.jung_q_mana:set("tooltip", "Will stop using [Q] if your percent mana falls below the selected number ")
ove_0_21.farm.jung:header("h_jung_e", "E Settings")
ove_0_21.farm.jung:boolean("jung_e", "[E] Enable ", true)
ove_0_21.farm.jung:boolean("jung_e_double", " ^- only if can double cast ", false)
ove_0_21.farm.jung:boolean("jung_e_dis", "Disable E if enemies nearby ", true)
ove_0_21.farm.jung:boolean("jung_e_count_max", "Use E on max amount of mobs ", true)
ove_0_21.farm.jung.jung_e_count_max:set("tooltip", "Uses E only when all jungle mobs can be hit in a single line.")
ove_0_21.farm.jung:slider("jung_e_mana", "[E] Mana % ", 0, 0, 100, 1)
ove_0_21.farm.jung.jung_e_mana:set("tooltip", "Will stop using [E] if your percent mana falls below the selected number ")
ove_0_21:menu("misc", "Misc Settings ")
ove_0_21.misc:header("h_misc_gap_sets", "Anti-Gapclose Settings")
ove_0_21.misc:boolean("misc_gap_q", "[Q] Gapclosers ", true)
ove_0_21.misc:boolean("misc_gap_e", "[E] Gapclosers ", true)
ove_0_21.misc:menu("misc_gap_blist", "Anti-Gapclose Blacklist")

for iter_0_7 = 0, objManager.enemies_n - 1 do
	local ove_0_59 = objManager.enemies[iter_0_7]

	if ove_0_59 then
		ove_0_21.misc.misc_gap_blist:boolean(ove_0_59.charName, "[Block] " .. ove_0_59.charName, false)
	end
end

ove_0_21.misc:header("h_misc_cc_sets", "CC Settings")
ove_0_21.misc:boolean("misc_cc_q", "[Q] CC'ed targets ", true)
ove_0_21.misc:boolean("misc_cc_e", "[E] CC'ed targets ", true)
ove_0_21.misc:header("h_misc_zh_sets", "Zhonyas Settings")
ove_0_21.misc:boolean("misc_zh_q", "[Q] Enemies in Zhonyas ", true)
ove_0_21.misc.misc_zh_q:set("tooltip", "Times the [Q] spell so that it hits the enemy immediately after exiting the Zhonyas effect.")
ove_0_21.misc:boolean("misc_zh_e", "[E] Enemies in Zhonyas ", true)
ove_0_21.misc.misc_zh_e:set("tooltip", "Times the [E] spell so that it hits the enemy immediately after exiting the Zhonyas effect.")
ove_0_21.misc:header("h_misc_tp_sets", "Teleport Settings")
ove_0_21.misc:boolean("misc_tp_q", "[Q] Enemy TP Positions ", true)
ove_0_21.misc.misc_tp_q:set("tooltip", "Uses [Q] on enemy TP positions to hit them immediately after teleporting.")
ove_0_21.misc:boolean("misc_tp_e", "[E] Enemy TP Positions ", true)
ove_0_21.misc.misc_tp_e:set("tooltip", "Uses [E] on enemy TP positions to hit them immediately after teleporting.")
ove_0_21:menu("draww", "Draw Settings ")
ove_0_21.draww:header("h_draww_range_sets", "Spell Range Draws")
ove_0_21.draww:boolean("draww_q", "Draw [Q] range", true)
ove_0_21.draww:color("draww_q_color", "  ^- Color", 250, 0, 135, 255)
ove_0_21.draww:boolean("draww_w", "Draw [W] range", false)
ove_0_21.draww:color("draww_w_color", "  ^- Color", 0, 250, 114, 255)
ove_0_21.draww:boolean("draww_e", "Draw [E / R] ranges", false)
ove_0_21.draww:color("draww_e_color", "  ^- Color", 250, 0, 135, 255)
ove_0_21.draww:header("h_draww_other_sets", "Other Draws")
ove_0_21.draww:menu("tp", "Teleport Draw Settings ")
ove_0_21.draww.tp:header("h_tp_sets", "Teleport Draw Settings")
ove_0_21.draww.tp:boolean("tp_enable", "Track enemy teleports ", true)
ove_0_21.draww.tp:boolean("tp_world", "Draw TP positions [world] ", true)
ove_0_21.draww.tp:color("tp_world_color", " ^- Color", 255, 255, 0, 255)
ove_0_21.draww.tp:boolean("tp_minimap", "Draw TP position [minimap] ", true)
ove_0_21.draww.tp:color("tp_minimap_color", " ^- Color", 255, 255, 0, 255)
ove_0_21.draww.tp:boolean("tp_arrow", "Draw TP position pointer ", true)
ove_0_21.draww.tp:boolean("tp_arrow_nearby", " ^- Only if enemy TP is near ", false)
ove_0_21.draww.tp:color("tp_arrow_color", " ^- Color", 255, 255, 0, 255)
ove_0_21.draww:boolean("draww_q_splash", "Draw [Q] splash ", true)
ove_0_21.draww:boolean("draww_square", "Draw an RGB square ", true)
ove_0_21.draww.draww_square:set("tooltip", "._.")
ove_0_21:menu("keys", "Key Settings")
ove_0_21.keys:header("h_keys_sets", "Key Settings")
ove_0_21.keys:keybind("keys_combo", "Combo Key", "Space", nil)
ove_0_21.keys:keybind("keys_harass", "Harass Key ", "C", nil)
ove_0_21.keys:keybind("keys_laneclear", "Lane Clear Key", "V", nil)
ove_0_21.keys:keybind("keys_lasthit", "Last Hit", "X", nil)
ove_0_21:header("headree", "- Healer AIO -")
ove_0_21:header("headree1", "- - Healer AIO - -")
ove_0_21:header("headree21", "- - - Healer AIO - - -")
ove_0_21:header("headree213", "Healer AIO")
ove_0_21.headree:set("visible", false)
ove_0_21.headree1:set("visible", false)
ove_0_21.headree21:set("visible", false)
ove_0_21.headree213:set("visible", false)
ove_0_30.animate(ove_0_21, ove_0_51)

local function ove_0_60()
	-- function 7
	return player.buff.seraphinepassiveechostage2
end

local ove_0_61 = ove_0_20.GetFlashSlot()

local function ove_0_62(arg_8_0)
	-- function 8
	if arg_8_0 and not arg_8_0.isDead and arg_8_0.isTargetable then
		local slot_8_0 = ove_0_36[arg_8_0.ptr]
		local slot_8_1 = slot_8_0 and ove_0_21.wset.ally["ally_shield_hp_" .. slot_8_0] or nil

		if slot_8_1 and ove_0_20.GetPercentHealth(arg_8_0) <= slot_8_1:get() or false then
			local slot_8_2 = ove_0_21.wset.ally["ally_blist_" .. slot_8_0]

			if not (slot_8_2 and slot_8_2:get() or false) then
				local slot_8_3 = ove_0_51[arg_8_0.ptr]
				local slot_8_4 = slot_8_3 and ove_0_21.wset.ally["ally_blist_temp_" .. slot_8_3] or nil

				if not (slot_8_4 and slot_8_4:get() or false) then
					return true
				end
			end
		end
	end

	return false
end

local function ove_0_63(arg_9_0)
	-- function 9
	if not ove_0_21.wset.wset_key:get() then
		return
	end

	if ove_0_48.slot.state ~= 0 then
		return
	end

	if not ove_0_21.wset.aa.aa_minion:get() then
		return
	end

	local slot_9_0 = arg_9_0.owner
	local slot_9_1 = arg_9_0.target

	if slot_9_0 and slot_9_0.type == TYPE_MINION and (slot_9_0.team == TEAM_ENEMY or slot_9_0.team == TEAM_NEUTRAL) and arg_9_0.isBasicAttack and slot_9_1 and slot_9_1.type == TYPE_HERO and slot_9_1.team == TEAM_ALLY and ove_0_62(slot_9_1) then
		local slot_9_2 = 800 + (slot_9_1.boundingRadius or 0)

		if slot_9_2 * slot_9_2 >= slot_9_1.pos2D:distSqr(player.pos2D) and ove_0_20.GetPercentHealth(slot_9_1) <= ove_0_21.wset.aa.aa_minion_hp:get() then
			player:castSpell("pos", 1, mousePos)
		end
	end
end

local ove_0_64 = 0

local function ove_0_65(arg_10_0)
	-- function 10
	if arg_10_0.owner and arg_10_0.owner.ptr == player.ptr and arg_10_0.name == "SummonerFlash" then
		ove_0_64 = game.time + 1.5
	end

	ove_0_63(arg_10_0)
end

local function ove_0_66()
	-- function 11
	if ove_0_21.keys.keys_combo:get() then
		return 1
	end

	if ove_0_21.keys.keys_harass:get() then
		return 2
	end

	return 0
end

local function ove_0_67()
	-- function 12
	local slot_12_0 = ove_0_66()
	local slot_12_1 = ove_0_21.auto.auto_q_filter:get()

	if slot_12_0 == 1 then
		slot_12_1 = ove_0_21.combo.combo_q_filter:get()
	end

	if slot_12_0 == 2 then
		slot_12_1 = ove_0_21.harass.harass_q_filter:get()
	end

	return slot_12_1
end

local function ove_0_68()
	-- function 13
	local slot_13_0 = ove_0_66()
	local slot_13_1 = ove_0_21.auto.auto_e_filter:get()

	if slot_13_0 == 1 then
		slot_13_1 = ove_0_21.combo.combo_e_filter:get()
	end

	if slot_13_0 == 2 then
		slot_13_1 = ove_0_21.harass.harass_e_filter:get()
	end

	return slot_13_1
end

local ove_0_69 = {
	ove_0_31.BUFF_ENUM_TAUNT,
	ove_0_31.BUFF_ENUM_POLYMORPH,
	ove_0_31.BUFF_ENUM_SLOW,
	ove_0_31.BUFF_ENUM_NEARSIGHT,
	ove_0_31.BUFF_ENUM_FEAR,
	ove_0_31.BUFF_ENUM_CHARM,
	ove_0_31.BUFF_ENUM_KNOCKBACK,
	ove_0_31.BUFF_ENUM_DROWSY
}

local function ove_0_70(arg_14_0, arg_14_1, arg_14_2)
	-- function 14
	if arg_14_2 == 2 then
		if ove_0_24.trace.newpath(arg_14_0, 0.033, 1) then
			return true
		end

		return false
	end

	local slot_14_0 = arg_14_0.pos + arg_14_0.direction - arg_14_0.pos
	local slot_14_1 = player.pos - arg_14_0.pos
	local slot_14_2 = slot_14_0:norm()
	local slot_14_3 = slot_14_1:norm()
	local slot_14_4 = slot_14_2:dot(slot_14_3)

	if ove_0_60() then
		if slot_14_4 >= 0.6 or slot_14_4 <= -0.6 then
			if ove_0_24.trace.newpath(arg_14_0, 0.033, 1) then
				return true
			end
		elseif ove_0_20.IsCustomLocked(ove_0_69, arg_14_0, 0.5) then
			return true
		end
	else
		if arg_14_2 == 4 then
			if ove_0_24.trace.newpath(arg_14_0, 0.033, 1) then
				return true
			end

			return false
		end

		if slot_14_4 > 0.25 or slot_14_4 < -0.25 then
			if ove_0_24.trace.newpath(arg_14_0, 0.033, 1) then
				return true
			end
		elseif ove_0_20.IsCustomLocked(ove_0_69, arg_14_0, 0.5) then
			return true
		end
	end

	return false
end

local function ove_0_71(arg_15_0, arg_15_1, arg_15_2, arg_15_3)
	-- function 15
	if arg_15_3 == 1 then
		return true
	end

	if 250000 >= arg_15_2.pos2D:distSqr(player.pos2D) then
		return true
	end

	if ove_0_64 > game.time then
		return true
	end

	if arg_15_2.buff.recall then
		return true
	end

	if ove_0_20.IsHardLockedCircularAPI(arg_15_0, arg_15_1, arg_15_2) then
		return true
	end

	if arg_15_2.buff.seraphineeslow then
		return true
	end

	if ove_0_20.WillHitDasherCircle(arg_15_2, arg_15_1, ove_0_41) then
		return true
	end

	if ove_0_70(arg_15_2, arg_15_1, arg_15_3) then
		return true
	end
end

local function ove_0_72(arg_16_0, arg_16_1, arg_16_2)
	-- function 16
	if arg_16_2 > 2000 then
		return false
	end

	if not ove_0_20.IsValidTarget(arg_16_1) then
		return false
	end

	local slot_16_0 = false

	if arg_16_1.buff.recall then
		slot_16_0 = true
	end

	local slot_16_1 = ove_0_24.circular.get_prediction(ove_0_47, arg_16_1)

	if not slot_16_1 then
		return false
	end

	local slot_16_2 = ove_0_47.range * ove_0_47.range

	if slot_16_0 then
		slot_16_2 = 1440000
	end

	if slot_16_2 < slot_16_1.startPos:distSqr(slot_16_1.endPos) then
		return false
	end

	if ove_0_20.IsBehindWall(slot_16_1.startPos, slot_16_1.endPos, ove_0_38) then
		return false
	end

	local slot_16_3 = ove_0_67()
	local slot_16_4 = 0

	if slot_16_3 == 4 then
		for iter_16_0 = 1, arg_16_1.path.count do
			local slot_16_5 = arg_16_1.path.point2D[iter_16_0 - 1]
			local slot_16_6 = arg_16_1.path.point2D[iter_16_0]

			slot_16_4 = slot_16_4 + slot_16_5:dist(slot_16_6)
		end
	end

	if not ove_0_71(ove_0_47, slot_16_1, arg_16_1, slot_16_3) then
		return false
	end

	local slot_16_7 = -0.033 - network.latency * 0.5

	if slot_16_0 then
		arg_16_0.pos = arg_16_1.pos2D
		arg_16_0.obj = arg_16_1

		return true
	else
		if slot_16_3 == 1 then
			arg_16_0.pos = slot_16_1.endPos
			arg_16_0.obj = arg_16_1

			return true
		end

		if arg_16_1.buff.seraphineeslow then
			if -0.033 - network.latency * 0.5 > arg_16_1.buff.seraphineeslow.startTime - game.time and arg_16_1.buff.seraphineeslow.endTime - game.time > 0.8 then
				arg_16_0.pos = slot_16_1.endPos
				arg_16_0.obj = arg_16_1

				return true
			end
		else
			if slot_16_3 == 2 then
				arg_16_0.pos = slot_16_1.endPos
				arg_16_0.obj = arg_16_1

				return true
			end

			if slot_16_3 == 3 then
				local slot_16_8 = arg_16_1.pos2D:distSqr(player.pos2D) > 422500

				if ove_0_20.IsFacingPlayerSeg(arg_16_1, slot_16_1, 0.875) and not arg_16_1.path.isDashing then
					local slot_16_9 = slot_16_1.endPos:distSqr(player.pos2D) <= 810000
					local slot_16_10 = arg_16_1.pos2D:distSqr(player.pos2D) >= 1144900

					if slot_16_9 and slot_16_10 then
						arg_16_0.pos = arg_16_1.pos2D
						arg_16_0.obj = arg_16_1

						return true
					elseif slot_16_8 then
						if arg_16_1.pos2D:distSqr(slot_16_1.endPos) <= 90000 then
							arg_16_0.pos = slot_16_1.endPos
							arg_16_0.obj = arg_16_1

							return true
						end
					else
						arg_16_0.pos = slot_16_1.endPos
						arg_16_0.obj = arg_16_1

						return true
					end
				elseif ove_0_20.IsFacingPlayerSeg(arg_16_1, slot_16_1, 0) and not arg_16_1.path.isDashing then
					if slot_16_8 then
						if arg_16_1.pos2D:distSqr(slot_16_1.endPos) <= 90000 then
							arg_16_0.pos = slot_16_1.endPos
							arg_16_0.obj = arg_16_1

							return true
						end
					else
						arg_16_0.pos = slot_16_1.endPos
						arg_16_0.obj = arg_16_1

						return true
					end
				else
					arg_16_0.pos = slot_16_1.endPos
					arg_16_0.obj = arg_16_1

					return true
				end
			end

			if slot_16_3 == 4 then
				if ove_0_20.IsFacingPlayerSeg(arg_16_1, slot_16_1, 0.875) and slot_16_4 <= 800 and not arg_16_1.path.isDashing then
					local slot_16_11 = arg_16_1.pos2D:distSqr(player.pos2D) > 422500
					local slot_16_12 = arg_16_1.pos2D
					local slot_16_13 = arg_16_1.path.point2D[arg_16_1.path.count]
					local slot_16_14 = player.pos2D
					local slot_16_15 = ove_0_20.VectorExtend(slot_16_12, slot_16_1.endPos, 450)
					local slot_16_16 = 350
					local slot_16_17, slot_16_18 = mathf.sect_line_circle(slot_16_12, slot_16_13, slot_16_14, slot_16_16)
					local slot_16_19, slot_16_20 = mathf.sect_line_circle(slot_16_12, slot_16_15, slot_16_14, slot_16_16)

					if slot_16_11 and not slot_16_17 and not slot_16_18 and not slot_16_19 and not slot_16_20 then
						arg_16_0.pos = arg_16_1.pos2D
						arg_16_0.obj = arg_16_1

						return true
					else
						arg_16_0.pos = slot_16_1.endPos
						arg_16_0.obj = arg_16_1

						return true
					end
				else
					arg_16_0.pos = slot_16_1.endPos
					arg_16_0.obj = arg_16_1

					return true
				end
			end
		end
	end

	return false
end

local function ove_0_73(arg_17_0, arg_17_1, arg_17_2)
	-- function 17
	if arg_17_2 == 2 then
		if ove_0_24.trace.newpath(arg_17_0, 0.033, 1) then
			return true
		end

		return false
	end

	if not arg_17_0.path.isActive then
		return false
	end

	local slot_17_0 = vec3(arg_17_1.endPos.x, arg_17_0.pos.y, arg_17_1.endPos.y) - arg_17_0.pos
	local slot_17_1 = player.pos - arg_17_0.pos
	local slot_17_2 = slot_17_0:norm()
	local slot_17_3 = slot_17_1:norm()
	local slot_17_4 = slot_17_2:dot(slot_17_3)

	if slot_17_4 >= 0.875 or slot_17_4 <= -0.875 then
		if ove_0_24.trace.newpath(arg_17_0, 0.033, math.huge) then
			return true
		end
	elseif ove_0_20.IsCustomLocked(ove_0_69, arg_17_0, 0.5) then
		return true
	end

	return false
end

local function ove_0_74(arg_18_0, arg_18_1, arg_18_2, arg_18_3)
	-- function 18
	local slot_18_0 = arg_18_0.range * arg_18_0.range
	local slot_18_1 = arg_18_0.range * arg_18_0.range
	local slot_18_2 = 360000
	local slot_18_3 = 640000

	if slot_18_0 < arg_18_1.startPos:distSqr(arg_18_1.endPos) then
		return false
	end

	if arg_18_3 == 1 then
		return true
	end

	if ove_0_64 > game.time then
		return true
	end

	if arg_18_2.buff.recall then
		return true
	end

	if slot_18_2 >= player.pos2D:distSqr(arg_18_2.pos2D) then
		return true
	end

	if ove_0_73(arg_18_2, arg_18_1, arg_18_3) then
		return true
	end

	if ove_0_20.IsHardLockedLinearAPI(arg_18_0, arg_18_1, arg_18_2) then
		return true
	end

	if ove_0_20.WillHitDasherLinear(arg_18_2, arg_18_1, ove_0_42) then
		return true
	end

	if slot_18_3 >= player.pos2D:distSqr(arg_18_2.pos2D) and ove_0_24.trace.newpath(arg_18_2, 0.033, 1) then
		return true
	end
end

local function ove_0_75(arg_19_0, arg_19_1, arg_19_2)
	-- function 19
	if arg_19_2 > ove_0_49.range then
		return false
	end

	if not ove_0_20.IsValidTarget(arg_19_1) then
		return false
	end

	local slot_19_0 = ove_0_24.linear.get_prediction(ove_0_49, arg_19_1)

	if not slot_19_0 then
		return false
	end

	local slot_19_1 = ove_0_68()

	if not ove_0_74(ove_0_49, slot_19_0, arg_19_1, slot_19_1) then
		return false
	end

	if ove_0_24.collision.get_prediction(ove_0_49, slot_19_0, arg_19_1) then
		return false
	end

	if ove_0_20.IsBehindWall(slot_19_0.startPos, slot_19_0.endPos, ove_0_39) then
		return false
	end

	arg_19_0.pos = slot_19_0.endPos
	arg_19_0.obj = arg_19_1

	return true
end

local function ove_0_76()
	-- function 20
	local slot_20_0 = ove_0_66()
	local slot_20_1 = ove_0_21.auto.auto_key:get() and ove_0_21.auto.auto_q:get() and not ove_0_20.IsRecalling(player) and (ove_0_21.auto.auto_q_turret:get() and not ove_0_20.is_under_enemy_turret(player.pos2D) or not ove_0_21.auto.auto_q_turret:get())
	local slot_20_2 = ove_0_21.auto.auto_q_mana:get()

	if slot_20_0 == 1 then
		slot_20_1, slot_20_2 = ove_0_21.combo.combo_q:get(), ove_0_21.combo.combo_q_mana:get()
	end

	if slot_20_0 == 2 then
		slot_20_1, slot_20_2 = ove_0_21.harass.harass_q:get(), ove_0_21.harass.harass_q_mana:get()
	end

	return slot_20_1, slot_20_2
end

local function ove_0_77()
	-- function 21
	local slot_21_0 = ove_0_66()
	local slot_21_1 = ove_0_21.auto.auto_key:get() and ove_0_21.auto.auto_e:get() and not ove_0_20.IsRecalling(player) and (ove_0_21.auto.auto_e_turret:get() and not ove_0_20.is_under_enemy_turret(player.pos2D) or not ove_0_21.auto.auto_e_turret:get())
	local slot_21_2 = ove_0_21.auto.auto_e_mana:get()

	if slot_21_0 == 1 then
		slot_21_1, slot_21_2 = ove_0_21.combo.combo_e:get(), ove_0_21.combo.combo_e_mana:get()
	end

	if slot_21_0 == 2 then
		slot_21_1, slot_21_2 = ove_0_21.harass.harass_e:get(), ove_0_21.harass.harass_e_mana:get()
	end

	return slot_21_1, slot_21_2
end

local function ove_0_78()
	-- function 22
	local slot_22_0, slot_22_1 = ove_0_76()

	if not slot_22_0 then
		return
	end

	if slot_22_1 >= ove_0_20.GetPercentMana(player) then
		return
	end

	if not ove_0_20.IsSpellSlotReady(_Q, true) then
		return
	end

	local slot_22_2 = ove_0_25.get_result(ove_0_72)

	if slot_22_2.pos and slot_22_2.obj then
		if ove_0_22.core.is_active() then
			return
		end

		player:castSpell("pos", 0, vec3(slot_22_2.pos.x, slot_22_2.obj.pos.y, slot_22_2.pos.y))
	end
end

local function ove_0_79()
	-- function 23
	local slot_23_0, slot_23_1 = ove_0_77()

	if not slot_23_0 then
		return
	end

	if slot_23_1 >= ove_0_20.GetPercentMana(player) then
		return
	end

	if not ove_0_20.IsSpellSlotReady(_E, true) then
		return
	end

	local slot_23_2 = ove_0_25.get_result(ove_0_75)

	if slot_23_2.pos and slot_23_2.obj then
		if ove_0_22.core.is_active() then
			return
		end

		player:castSpell("pos", 2, vec3(slot_23_2.pos.x, slot_23_2.obj.pos.y, slot_23_2.pos.y))
	end
end

local function ove_0_80(arg_24_0, arg_24_1, arg_24_2)
	-- function 24
	local slot_24_0 = ove_0_21.rset.rset_force_bounce:get()

	if not slot_24_0 and arg_24_2 > ove_0_50.range then
		return false
	end

	if not ove_0_20.IsValidTarget(arg_24_1) then
		return false
	end

	local slot_24_1 = ove_0_24.linear.get_prediction(ove_0_50, arg_24_1)

	if not slot_24_1 then
		return false
	end

	local slot_24_2 = 0

	if slot_24_0 then
		local slot_24_3 = {}
		local slot_24_4 = {}
		local slot_24_5 = 20000

		for iter_24_0 = 0, objManager.allies_n - 1 do
			local slot_24_6 = objManager.allies[iter_24_0]

			if slot_24_6 and slot_24_6.ptr ~= player.ptr and not slot_24_6.isDead and slot_24_6.isTargetable and slot_24_6.isVisible then
				local slot_24_7 = ove_0_24.linear.get_prediction(ove_0_50, slot_24_6)

				if slot_24_7 then
					local slot_24_8 = ove_0_20.IsOnLineSegment(slot_24_6.pos2D, slot_24_1.startPos, slot_24_1.endPos, 120, slot_24_5)
					local slot_24_9 = slot_24_6.path.isDashing and ove_0_20.IsOnLineSegment(slot_24_7.endPos, slot_24_1.startPos, slot_24_1.endPos, ove_0_50.width, slot_24_5)

					if slot_24_8 or slot_24_9 then
						slot_24_3[#slot_24_3 + 1] = slot_24_6
						slot_24_4[slot_24_6.ptr] = slot_24_7
					end
				end
			end
		end

		table.sort(slot_24_3, function(arg_25_0, arg_25_1)
			-- function 25
			return arg_25_0.pos2D:distSqr(player.pos2D) < arg_25_1.pos2D:distSqr(player.pos2D)
		end)

		local slot_24_10 = 1000

		for iter_24_1 = 1, #slot_24_3 do
			local slot_24_11 = slot_24_3[iter_24_1]
			local slot_24_12 = slot_24_3[iter_24_1 + 1]

			if slot_24_11 and slot_24_11.ptr == slot_24_3[1].ptr then
				local slot_24_13 = slot_24_4[slot_24_11.ptr]

				if slot_24_13 then
					if slot_24_11.pos2D:distSqr(player.pos2D) <= 1000000 and slot_24_13.endPos:distSqr(player.path.serverPos2D) <= 1440000 then
						slot_24_10 = slot_24_10 + slot_24_11.pos2D:dist(player.pos2D)
					else
						break
					end
				end
			end

			if slot_24_11 and slot_24_12 then
				local slot_24_14 = slot_24_4[slot_24_11.ptr]
				local slot_24_15 = slot_24_4[slot_24_12.ptr]

				if slot_24_14 and slot_24_15 then
					if slot_24_11.pos2D:distSqr(slot_24_12.pos2D) <= 1000000 and slot_24_14.endPos:distSqr(slot_24_15.endPos) <= 1440000 then
						slot_24_10 = slot_24_10 + slot_24_11.pos2D:dist(slot_24_12.pos2D)
					else
						break
					end
				end
			end
		end

		slot_24_2 = slot_24_10
	end

	if not slot_24_0 then
		if slot_24_1.startPos:distSqr(slot_24_1.endPos) > 1440000 then
			return false
		end
	elseif slot_24_1.startPos:distSqr(slot_24_1.endPos) > slot_24_2 * slot_24_2 then
		return false
	end

	if ove_0_24.collision.get_prediction(ove_0_50, slot_24_1, arg_24_1) then
		return false
	end

	if ove_0_20.IsBehindWall(slot_24_1.startPos, slot_24_1.endPos, ove_0_40) then
		return false
	end

	arg_24_0.pos = slot_24_1.endPos
	arg_24_0.obj = arg_24_1

	return true
end

local function ove_0_81()
	-- function 26
	if not ove_0_21.rset.rset_force_key:get() then
		return
	end

	if ove_0_50.slot.state ~= 0 then
		return
	end

	local slot_26_0 = ove_0_25.get_result(ove_0_80)

	if slot_26_0.pos and slot_26_0.obj then
		player:castSpell("pos", 3, vec3(slot_26_0.pos.x, slot_26_0.obj.pos.y, slot_26_0.pos.y))
	end
end

local ove_0_82 = {
	ove_0_31.BUFF_ENUM_STUN,
	ove_0_31.BUFF_ENUM_TAUNT,
	ove_0_31.BUFF_ENUM_GROUNDED,
	ove_0_31.BUFF_ENUM_SNARE,
	ove_0_31.BUFF_ENUM_FEAR,
	ove_0_31.BUFF_ENUM_CHARM,
	ove_0_31.BUFF_ENUM_SUPPRESSION,
	ove_0_31.BUFF_ENUM_KNOCKUP,
	ove_0_31.BUFF_ENUM_ASLEEP
}

local function ove_0_83()
	-- function 27
	if not ove_0_21.misc.misc_cc_q:get() then
		return
	end

	if not ove_0_20.IsSpellSlotReady(_Q, true) then
		return
	end

	if ove_0_22.core.is_active() then
		return
	end

	local slot_27_0 = 2102500
	local slot_27_1 = ove_0_47.range * ove_0_47.range
	local slot_27_2 = 1562500

	for iter_27_0 = 0, objManager.enemies_n - 1 do
		local slot_27_3 = objManager.enemies[iter_27_0]

		if ove_0_20.IsValidTarget(slot_27_3) and slot_27_0 >= slot_27_3.pos2D:distSqr(player.pos2D) then
			local slot_27_4 = ove_0_24.circular.get_prediction(ove_0_41, slot_27_3)

			if slot_27_4 and ove_0_20.IsCustomLockHitCircular(ove_0_82, ove_0_41, slot_27_4, slot_27_3) and slot_27_2 >= slot_27_4.startPos:distSqr(slot_27_4.endPos) and not ove_0_20.IsBehindWall(slot_27_4.startPos, slot_27_4.endPos, ove_0_38) then
				player:castSpell("pos", 0, vec3(slot_27_4.endPos.x, slot_27_3.pos.y, slot_27_4.endPos.y))
			end
		end
	end
end

local function ove_0_84()
	-- function 28
	if not ove_0_21.misc.misc_cc_e:get() then
		return
	end

	if not ove_0_20.IsSpellSlotReady(_E, true) then
		return
	end

	if ove_0_22.core.is_active() then
		return
	end

	local slot_28_0 = 2102500
	local slot_28_1 = ove_0_49.range * ove_0_49.range

	for iter_28_0 = 0, objManager.enemies_n - 1 do
		local slot_28_2 = objManager.enemies[iter_28_0]

		if ove_0_20.IsValidTarget(slot_28_2) and slot_28_0 >= slot_28_2.pos2D:distSqr(player.pos2D) then
			local slot_28_3 = ove_0_24.linear.get_prediction(ove_0_49, slot_28_2)

			if slot_28_3 and ove_0_20.IsCustomLockHitLinear(ove_0_82, ove_0_42, slot_28_3, slot_28_2) and slot_28_1 >= slot_28_3.startPos:distSqr(slot_28_3.endPos) and not ove_0_24.collision.get_prediction(ove_0_49, slot_28_3, slot_28_2) and not ove_0_20.IsBehindWall(slot_28_3.startPos, slot_28_3.endPos, ove_0_39) then
				player:castSpell("pos", 2, vec3(slot_28_3.endPos.x, slot_28_2.pos.y, slot_28_3.endPos.y))
			end
		end
	end
end

local ove_0_85 = {
	ove_0_31.BUFF_ENUM_STUN,
	ove_0_31.BUFF_ENUM_TAUNT,
	ove_0_31.BUFF_ENUM_POLYMORPH,
	ove_0_31.BUFF_ENUM_SNARE,
	ove_0_31.BUFF_ENUM_FEAR,
	ove_0_31.BUFF_ENUM_CHARM,
	ove_0_31.BUFF_ENUM_SUPPRESSION,
	ove_0_31.BUFF_ENUM_KNOCKUP,
	ove_0_31.BUFF_ENUM_KNOCKBACK,
	ove_0_31.BUFF_ENUM_GROUNDED,
	ove_0_31.BUFF_ENUM_ASLEEP
}
local ove_0_86 = {
	ove_0_31.BUFF_ENUM_POISON,
	"summonerdot",
	"zedrtargetmark",
	"zedrdeathmark",
	"zileanqenemybomb",
	"brandablazedetonatemarker"
}

local function ove_0_87()
	-- function 29
	if ove_0_23 ~= 1 then
		return
	end

	if not ove_0_21.wset.wset_key:get() then
		return
	end

	if ove_0_48.slot.state ~= 0 then
		return
	end

	local slot_29_0 = ove_0_21.wset.wset_cc:get()
	local slot_29_1 = ove_0_21.wset.wset_poison:get()
	local slot_29_2 = ove_0_21.wset.wset_self_circular:get()
	local slot_29_3 = ove_0_21.wset.wset_self_linear:get()

	for iter_29_0 = 0, objManager.allies_n - 1 do
		local slot_29_4 = objManager.allies[iter_29_0]

		if ove_0_62(slot_29_4) then
			local slot_29_5 = 800 + (slot_29_4.boundingRadius or 0)

			if slot_29_5 * slot_29_5 >= slot_29_4.pos2D:distSqr(player.pos2D) then
				if slot_29_1 and ove_0_20.IsCustomLockedNoCheck(ove_0_86, slot_29_4) then
					player:castSpell("pos", 1, mousePos)

					break
				end

				if slot_29_0 and ove_0_20.IsCustomLockedNoCheck(ove_0_85, slot_29_4) then
					player:castSpell("pos", 1, mousePos)

					break
				end

				for iter_29_1 = 1, #ove_0_22.core.skillshots do
					local slot_29_6 = ove_0_22.core.skillshots[iter_29_1]

					if slot_29_6.polygon and slot_29_6.polygon:Contains(slot_29_4.path.serverPos) ~= 0 and (not slot_29_6.data.collision or #slot_29_6.data.collision == 0) then
						local slot_29_7 = slot_29_6.owner
						local slot_29_8 = slot_29_7 and ove_0_21.wset[ove_0_32[slot_29_7.ptr]] or nil

						if slot_29_8 then
							for iter_29_2, iter_29_3 in pairs(ove_0_28) do
								if slot_29_6.name:find(iter_29_2:lower()) then
									local slot_29_9 = slot_29_8[iter_29_2]

									if slot_29_9 and slot_29_9.skill_shield_enable:get() and ove_0_20.GetPercentHealth(slot_29_4) <= slot_29_9.skill_shield_hp:get() then
										if slot_29_4.ptr == player.ptr then
											if slot_29_6.data.spell_type == "Circular" then
												if slot_29_2 then
													player:castSpell("pos", 1, mousePos)

													break
												end
											elseif slot_29_3 then
												if slot_29_6.missile and slot_29_4.pos2D:dist(slot_29_6.missile.pos2D) / slot_29_6.data.speed <= network.latency + 0.35 then
													player:castSpell("pos", 1, mousePos)

													break
												end

												if slot_29_6.data.speed == math.huge then
													player:castSpell("pos", 1, mousePos)

													break
												end
											end
										else
											if slot_29_6.missile and slot_29_4.pos2D:dist(slot_29_6.missile.pos2D) / slot_29_6.data.speed <= network.latency + 0.35 then
												player:castSpell("pos", 1, mousePos)

												break
											end

											if slot_29_6.data.speed == math.huge or slot_29_6.data.spell_type == "Circular" then
												player:castSpell("pos", 1, mousePos)

												break
											end
										end
									end
								end
							end
						end
					end
				end
			end
		end
	end

	local slot_29_10 = ove_0_21.wset.wset_targeted:get()
	local slot_29_11 = ove_0_21.wset.aa.aa_basic:get()
	local slot_29_12 = ove_0_21.wset.aa.aa_crit:get()
	local slot_29_13 = ove_0_21.wset.aa.aa_turret:get()

	if not slot_29_10 and not slot_29_11 and not slot_29_12 and not slot_29_13 then
		return
	end

	local slot_29_14 = ove_0_21.wset.aa.aa_basic_hp:get()
	local slot_29_15 = ove_0_21.wset.aa.aa_crit_hp:get()
	local slot_29_16 = ove_0_21.wset.aa.aa_turret_hp:get()

	for iter_29_4 = 1, #ove_0_22.core.targeted do
		local slot_29_17 = ove_0_22.core.targeted[iter_29_4]
		local slot_29_18 = slot_29_17.owner
		local slot_29_19 = slot_29_17.target

		if slot_29_19 and slot_29_19.type == TYPE_HERO and slot_29_19.team == TEAM_ALLY and slot_29_18 and (slot_29_18.type == TYPE_HERO or slot_29_18.type == TYPE_TURRET) and slot_29_18.team == TEAM_ENEMY and ove_0_62(slot_29_19) then
			local slot_29_20 = 800 + (slot_29_19.boundingRadius or 0)

			if slot_29_20 * slot_29_20 >= slot_29_19.pos2D:distSqr(player.pos2D) then
				if slot_29_18.type == TYPE_HERO then
					if ove_0_20.IsSpellValidLower(slot_29_17.name) then
						if not slot_29_17.name:find("Crit") and not slot_29_17.name:find("BasicAttack") then
							if slot_29_10 then
								player:castSpell("pos", 1, mousePos)

								break
							end
						else
							if slot_29_17.name:find("BasicAttack") and slot_29_11 and slot_29_14 >= ove_0_20.GetPercentHealth(slot_29_19) then
								player:castSpell("pos", 1, mousePos)

								break
							end

							if slot_29_17.name:find("Crit") and slot_29_12 and slot_29_15 >= ove_0_20.GetPercentHealth(slot_29_19) then
								player:castSpell("pos", 1, mousePos)

								break
							end
						end
					end
				elseif slot_29_13 and slot_29_16 >= ove_0_20.GetPercentHealth(slot_29_19) then
					player:castSpell("pos", 1, mousePos)

					break
				end
			end
		end
	end
end

local function ove_0_88()
	-- function 30
	if ove_0_23 ~= 2 then
		return
	end

	if not ove_0_21.wset.wset_key:get() then
		return
	end

	if ove_0_48.slot.state ~= 0 then
		return
	end

	local slot_30_0 = ove_0_21.wset.wset_cc:get()
	local slot_30_1 = ove_0_21.wset.wset_poison:get()
	local slot_30_2 = ove_0_21.wset.wset_self_circular:get()
	local slot_30_3 = ove_0_21.wset.wset_self_linear:get()

	for iter_30_0 = 0, objManager.allies_n - 1 do
		local slot_30_4 = objManager.allies[iter_30_0]

		if ove_0_62(slot_30_4) then
			local slot_30_5 = 800 + (slot_30_4.boundingRadius or 0)

			if slot_30_5 * slot_30_5 >= slot_30_4.pos2D:distSqr(player.pos2D) then
				if slot_30_1 and ove_0_20.IsCustomLockedNoCheck(ove_0_86, slot_30_4) then
					player:castSpell("pos", 1, mousePos)

					break
				end

				if slot_30_0 and ove_0_20.IsCustomLockedNoCheck(ove_0_85, slot_30_4) then
					player:castSpell("pos", 1, mousePos)

					break
				end

				for iter_30_1 = ove_0_22.core.skillshots.n, 1, -1 do
					local slot_30_6 = ove_0_22.core.skillshots[iter_30_1]

					if slot_30_6.contains and slot_30_6:contains(slot_30_4) and (not slot_30_6.data.collision or slot_30_6.data.collision == 0) then
						local slot_30_7 = slot_30_6.owner
						local slot_30_8 = slot_30_7 and ove_0_21.wset[ove_0_32[slot_30_7.ptr]] or nil

						if slot_30_8 then
							local slot_30_9 = slot_30_6.name and slot_30_8[slot_30_6.name] or nil

							if slot_30_9 and slot_30_9.skill_shield_enable:get() and ove_0_20.GetPercentHealth(slot_30_4) <= slot_30_9.skill_shield_hp:get() then
								if slot_30_4.ptr == player.ptr then
									if slot_30_6.data.spell_type == "Circular" then
										if slot_30_2 then
											player:castSpell("pos", 1, mousePos)

											break
										end
									elseif slot_30_3 then
										if slot_30_6.missile and slot_30_4.pos2D:dist(slot_30_6.missile.pos2D) / slot_30_6.data.speed <= network.latency + 0.35 then
											player:castSpell("pos", 1, mousePos)

											break
										end

										if slot_30_6.data.speed == math.huge then
											player:castSpell("pos", 1, mousePos)

											break
										end
									end
								else
									if slot_30_6.missile and slot_30_4.pos2D:dist(slot_30_6.missile.pos2D) / slot_30_6.data.speed <= network.latency + 0.35 then
										player:castSpell("pos", 1, mousePos)

										break
									end

									if slot_30_6.data.speed == math.huge or slot_30_6.data.spell_type == "Circular" then
										player:castSpell("pos", 1, mousePos)

										break
									end
								end
							end
						end
					end
				end
			end
		end
	end

	local slot_30_10 = ove_0_21.wset.wset_targeted:get()
	local slot_30_11 = ove_0_21.wset.aa.aa_basic:get()
	local slot_30_12 = ove_0_21.wset.aa.aa_crit:get()
	local slot_30_13 = ove_0_21.wset.aa.aa_turret:get()

	if not slot_30_10 and not slot_30_11 and not slot_30_12 and not slot_30_13 then
		return
	end

	local slot_30_14 = ove_0_21.wset.aa.aa_basic_hp:get()
	local slot_30_15 = ove_0_21.wset.aa.aa_crit_hp:get()
	local slot_30_16 = ove_0_21.wset.aa.aa_turret_hp:get()

	for iter_30_2 = ove_0_22.core.targeted.n, 1, -1 do
		local slot_30_17 = ove_0_22.core.targeted[iter_30_2]
		local slot_30_18 = slot_30_17.owner
		local slot_30_19 = slot_30_17.target

		if slot_30_19 and slot_30_19.type == TYPE_HERO and slot_30_19.team == TEAM_ALLY and slot_30_18 and (slot_30_18.type == TYPE_HERO or slot_30_18.type == TYPE_TURRET) and slot_30_18.team == TEAM_ENEMY and ove_0_62(slot_30_19) then
			local slot_30_20 = 800 + (slot_30_19.boundingRadius or 0)

			if slot_30_20 * slot_30_20 >= slot_30_19.pos2D:distSqr(player.pos2D) then
				if slot_30_18.type == TYPE_HERO then
					if ove_0_20.IsSpellValidUpper(slot_30_17.name) then
						if not slot_30_17.name:find("Crit") and not slot_30_17.name:find("BasicAttack") then
							if slot_30_10 then
								player:castSpell("pos", 1, mousePos)

								break
							end
						else
							if slot_30_17.name:find("BasicAttack") and slot_30_11 and slot_30_14 >= ove_0_20.GetPercentHealth(slot_30_19) then
								player:castSpell("pos", 1, mousePos)

								break
							end

							if slot_30_17.name:find("Crit") and slot_30_12 and slot_30_15 >= ove_0_20.GetPercentHealth(slot_30_19) then
								player:castSpell("pos", 1, mousePos)

								break
							end
						end
					end
				elseif slot_30_13 and slot_30_16 >= ove_0_20.GetPercentHealth(slot_30_19) then
					player:castSpell("pos", 1, mousePos)

					break
				end
			end
		end
	end
end

local ove_0_89 = {
	55,
	70,
	85,
	100,
	115
}
local ove_0_90 = {
	0.45,
	0.5,
	0.55,
	0.6,
	0.65
}
local ove_0_91 = 0.6666666666666666

local function ove_0_92(arg_31_0)
	-- function 31
	local slot_31_0 = 0
	local slot_31_1 = ove_0_47.slot.level

	if slot_31_1 > 0 then
		slot_31_0 = ove_0_20.CalculateMagicDamage(arg_31_0, ove_0_89[slot_31_1] + ove_0_20.GetTotalAP() * ove_0_90[slot_31_1], player)
		slot_31_0 = slot_31_0 + slot_31_0 * (ove_0_91 * mathf.clamp(0, 75, ove_0_20.GetMissingPercentHealth(arg_31_0)) * 0.01)
		slot_31_0 = slot_31_0 * ove_0_20.BaronReductionMod(arg_31_0)
	end

	return slot_31_0
end

local ove_0_93 = {
	60,
	80,
	100,
	120,
	140
}
local ove_0_94 = 0.35

local function ove_0_95(arg_32_0)
	-- function 32
	local slot_32_0 = 0
	local slot_32_1 = ove_0_49.slot.level

	if slot_32_1 > 0 then
		slot_32_0 = ove_0_20.CalculateMagicDamage(arg_32_0, ove_0_93[slot_32_1] + ove_0_20.GetTotalAP() * ove_0_94, player)
		slot_32_0 = slot_32_0 * ove_0_20.BaronReductionMod(arg_32_0)
	end

	return slot_32_0
end

local ove_0_96 = {
	boundingRadiusMod = 0,
	range = 900,
	speed = 1200,
	delay = 0.638595410459617,
	radius = 340,
	damage = function(arg_33_0)
		-- function 33
		return ove_0_92(arg_33_0)
	end
}
local ove_0_97 = 0

local function ove_0_98()
	-- function 34
	if not ove_0_21.farm.help.help_q:get() then
		return
	end

	if ove_0_97 > game.time then
		return
	end

	if ove_0_20.GetPercentMana(player) <= ove_0_21.farm.help.help_q_mana:get() then
		return
	end

	if not ove_0_20.IsSpellSlotReady(_Q, true) then
		return
	end

	if ove_0_22.core.is_active() then
		return
	end

	if ove_0_21.farm.help.help_q_dis_double:get() and ove_0_60() then
		return
	end

	if ove_0_21.farm.help.help_q_dis:get() and #ove_0_20.count_enemies_in_range(player.pos2D, 1700) >= 1 then
		return
	end

	if ove_0_21.farm.help.help_q_aa_range:get() then
		ove_0_96.range = player.attackRange + player.boundingRadius + 60
	end

	local slot_34_0, slot_34_1 = ove_0_26.farm.skill_farm_circular(ove_0_96)

	if slot_34_0 and slot_34_1 and slot_34_1.team == TEAM_ENEMY and ove_0_20.valid_minion(slot_34_1) then
		if slot_34_0.startPos:distSqr(slot_34_0.endPos) > ove_0_96.range * ove_0_96.range then
			return
		end

		if ove_0_20.IsBehindWall(slot_34_0.startPos, slot_34_0.endPos, ove_0_38) then
			return
		end

		if ove_0_21.farm.help.help_q_cannon:get() and not slot_34_1.charName:find("MinionSiege") then
			return
		end

		player:castSpell("pos", _Q, vec3(slot_34_0.endPos.x, slot_34_1.pos.y, slot_34_0.endPos.y))

		ove_0_97 = game.time + 2
	end
end

local ove_0_99 = {
	boundingRadiusMod = 1,
	range = 1300,
	speed = 1200,
	delay = 0.25,
	width = 80,
	collision = {
		walls = true,
		wall = true,
		hero = false,
		minion = false
	},
	damage = function(arg_35_0)
		-- function 35
		return ove_0_95(arg_35_0)
	end
}

local function ove_0_100()
	-- function 36
	if not ove_0_21.farm.help.help_e:get() then
		return
	end

	if ove_0_97 > game.time then
		return
	end

	if ove_0_20.GetPercentMana(player) <= ove_0_21.farm.help.help_e_mana:get() then
		return
	end

	if not ove_0_20.IsSpellSlotReady(_E, true) then
		return
	end

	if ove_0_22.core.is_active() then
		return
	end

	if ove_0_21.farm.help.help_e_dis_double:get() and ove_0_60() then
		return
	end

	if ove_0_21.farm.help.help_e_dis:get() and #ove_0_20.count_enemies_in_range(player.pos2D, 1700) >= 1 then
		return
	end

	if ove_0_21.farm.help.help_e_aa_range:get() then
		ove_0_99.range = player.attackRange + player.boundingRadius + 60
	end

	local slot_36_0, slot_36_1 = ove_0_26.farm.skill_farm_linear(ove_0_99)

	if slot_36_0 and slot_36_1 and slot_36_1.team == TEAM_ENEMY and ove_0_20.valid_minion(slot_36_1) then
		if slot_36_0.startPos:distSqr(slot_36_0.endPos) > ove_0_99.range * ove_0_99.range then
			return
		end

		if ove_0_20.IsBehindWall(slot_36_0.startPos, slot_36_0.endPos, ove_0_39) then
			return
		end

		if ove_0_21.farm.help.help_e_cannon:get() and not slot_36_1.charName:find("MinionSiege") then
			return
		end

		player:castSpell("pos", _E, vec3(slot_36_0.endPos.x, slot_36_1.pos.y, slot_36_0.endPos.y))

		ove_0_97 = game.time + 2
	end
end

local function ove_0_101(arg_37_0, arg_37_1, arg_37_2)
	-- function 37
	local slot_37_0
	local slot_37_1

	for iter_37_0 = 0, objManager.minions.size[arg_37_0] - 1 do
		local slot_37_2 = objManager.minions[arg_37_0][iter_37_0]

		if ove_0_20.valid_minion_in_range(slot_37_2, player.pos2D, arg_37_2) and ove_0_20.IsMobAggroed(slot_37_2, ove_0_21.farm.jung.jung_aggro:get()) then
			if slot_37_1 == nil then
				slot_37_1 = slot_37_2
			elseif #ove_0_20.count_minions_in_range(slot_37_2.pos2D, arg_37_1.radius, arg_37_0) >= #ove_0_20.count_minions_in_range(slot_37_1.pos2D, arg_37_1.radius, arg_37_0) then
				slot_37_1 = slot_37_2
			end
		end
	end

	local slot_37_3 = {}
	local slot_37_4 = 0
	local slot_37_5 = arg_37_1.range * arg_37_1.range
	local slot_37_6 = arg_37_1.radius * 2
	local slot_37_7 = slot_37_6 * slot_37_6

	for iter_37_1 = 0, objManager.minions.size[arg_37_0] - 1 do
		local slot_37_8 = objManager.minions[arg_37_0][iter_37_1]

		if ove_0_20.valid_minion(slot_37_8) and ove_0_20.IsMobAggroed(slot_37_8, ove_0_21.farm.jung.jung_aggro:get()) and slot_37_1 and slot_37_7 >= slot_37_1.pos2D:distSqr(slot_37_8.pos2D) then
			local slot_37_9 = ove_0_24.circular.get_prediction(arg_37_1, slot_37_8)

			if slot_37_9 and slot_37_5 >= slot_37_9.startPos:distSqr(slot_37_9.endPos) then
				slot_37_3[slot_37_4] = slot_37_9.endPos
				slot_37_4 = slot_37_4 + 1
			end
		end
	end

	local slot_37_10, slot_37_11 = ove_0_20.CustomMEC(slot_37_3, slot_37_4)

	if slot_37_10 then
		slot_37_0 = slot_37_10
	end

	return slot_37_0
end

local function ove_0_102(arg_38_0, arg_38_1, arg_38_2, arg_38_3)
	-- function 38
	local slot_38_0 = 0
	local slot_38_1 = arg_38_1.range * arg_38_1.range

	for iter_38_0 = 1, #arg_38_0 do
		local slot_38_2 = arg_38_0[iter_38_0]

		if slot_38_2 then
			local slot_38_3 = ove_0_24.linear.get_prediction(arg_38_1, slot_38_2, arg_38_2)

			if slot_38_3 and slot_38_1 >= slot_38_3.startPos:distSqr(slot_38_3.endPos) then
				local slot_38_4 = arg_38_1.boundingRadiusMod == 1 and slot_38_2.boundingRadius or 0

				if ove_0_20.IsOnLineSegment(slot_38_3.endPos, slot_38_3.startPos, arg_38_3, arg_38_1.width + slot_38_4, arg_38_1.range) then
					slot_38_0 = slot_38_0 + 1
				end
			end
		end
	end

	return slot_38_0
end

local function ove_0_103(arg_39_0, arg_39_1, arg_39_2, arg_39_3)
	-- function 39
	local slot_39_0 = 0
	local slot_39_1
	local slot_39_2 = {}
	local slot_39_3 = arg_39_1.range * arg_39_1.range
	local slot_39_4 = {}

	for iter_39_0 = 0, objManager.minions.size[arg_39_0] - 1 do
		local slot_39_5 = objManager.minions[arg_39_0][iter_39_0]

		if ove_0_20.valid_minion_in_range(slot_39_5, arg_39_2.pos2D, arg_39_3) and ove_0_20.IsMobAggroed(slot_39_5, ove_0_21.farm.jung.jung_aggro:get()) then
			slot_39_4[#slot_39_4 + 1] = slot_39_5
		end
	end

	for iter_39_1 = 1, #slot_39_4 do
		local slot_39_6 = slot_39_4[iter_39_1]
		local slot_39_7 = slot_39_4[iter_39_1 + 1]

		if slot_39_6 and slot_39_7 then
			if slot_39_6.ptr ~= slot_39_7.ptr then
				local slot_39_8 = ove_0_24.linear.get_prediction(arg_39_1, slot_39_6, arg_39_2)
				local slot_39_9 = ove_0_24.linear.get_prediction(arg_39_1, slot_39_7, arg_39_2)

				if slot_39_8 and slot_39_9 and slot_39_3 >= slot_39_8.startPos:distSqr(slot_39_8.endPos) and slot_39_3 >= slot_39_9.startPos:distSqr(slot_39_9.endPos) then
					local slot_39_10 = (slot_39_8.endPos + slot_39_9.endPos) * 0.5

					slot_39_2[#slot_39_2 + 1] = slot_39_10
				end
			end
		elseif slot_39_6 and not slot_39_7 then
			local slot_39_11 = ove_0_24.linear.get_prediction(arg_39_1, slot_39_6, arg_39_2)

			if slot_39_11 and slot_39_3 >= slot_39_11.startPos:distSqr(slot_39_11.endPos) then
				slot_39_2[#slot_39_2 + 1] = slot_39_11.endPos
			end
		end
	end

	if #slot_39_2 <= 0 then
		return slot_39_1, slot_39_0
	end

	for iter_39_2 = 1, #slot_39_2 do
		local slot_39_12 = slot_39_2[iter_39_2]

		if slot_39_12 then
			local slot_39_13 = ove_0_102(slot_39_4, arg_39_1, arg_39_2, slot_39_12)

			if slot_39_13 >= 1 then
				if slot_39_0 == 0 then
					slot_39_0 = slot_39_13
					slot_39_1 = slot_39_12
				elseif slot_39_0 < slot_39_13 then
					slot_39_0 = slot_39_13
					slot_39_1 = slot_39_12
				end
			end
		end
	end

	return slot_39_1, slot_39_0
end

local function ove_0_104(arg_40_0, arg_40_1, arg_40_2)
	-- function 40
	local slot_40_0
	local slot_40_1 = 0
	local slot_40_2 = {}
	local slot_40_3 = 0
	local slot_40_4 = {}
	local slot_40_5 = arg_40_1.range * arg_40_1.range

	for iter_40_0 = 0, objManager.minions.size[arg_40_0] - 1 do
		local slot_40_6 = objManager.minions[arg_40_0][iter_40_0]

		if ove_0_20.valid_minion(slot_40_6) and ove_0_20.IsMobAggroed(slot_40_6, ove_0_21.farm.jung.jung_aggro:get()) and slot_40_5 >= arg_40_2.pos2D:distSqr(slot_40_6.pos2D) then
			slot_40_4[#slot_40_4 + 1] = slot_40_6

			local slot_40_7 = ove_0_24.linear.get_prediction(arg_40_1, slot_40_6, arg_40_2)

			if slot_40_7 and slot_40_5 >= slot_40_7.startPos:distSqr(slot_40_7.endPos) then
				slot_40_2[slot_40_3] = slot_40_7.endPos
				slot_40_3 = slot_40_3 + 1
			end
		end
	end

	local slot_40_8, slot_40_9 = ove_0_20.CustomMEC(slot_40_2, slot_40_3)

	if slot_40_8 then
		local slot_40_10 = #ove_0_20.count_minions_in_range(player.pos2D, arg_40_1.range, arg_40_0)
		local slot_40_11 = ove_0_102(slot_40_4, arg_40_1, arg_40_2, slot_40_8)

		if slot_40_10 <= slot_40_11 then
			slot_40_0 = slot_40_8
			slot_40_1 = slot_40_11
		end
	end

	return slot_40_0, slot_40_1
end

local function ove_0_105()
	-- function 41
	if not ove_0_21.farm.lane.lane_q:get() then
		return
	end

	if ove_0_20.GetPercentMana(player) <= ove_0_21.farm.lane.lane_q_mana:get() then
		return
	end

	if not ove_0_20.IsSpellSlotReady(_Q, true) then
		return
	end

	if ove_0_22.core.is_active() then
		return
	end

	if ove_0_21.farm.lane.lane_q_double:get() and not ove_0_60() then
		return
	end

	if ove_0_21.farm.lane.lane_q_dis:get() and #ove_0_20.count_enemies_in_range(player.pos2D, 1700) >= 1 then
		return
	end

	if ove_0_21.farm.lane.lane_q_move_dis:get() and #ove_0_20.count_moving_minions_in_range(player.pos2D, 1500, TEAM_ENEMY) > ove_0_21.farm.lane.lane_q_move_allow_count:get() then
		return
	end

	local slot_41_0 = ove_0_101(TEAM_ENEMY, ove_0_46, 1500)
	local slot_41_1 = ove_0_21.farm.lane.lane_q_count:get()
	local slot_41_2 = ove_0_47.range * ove_0_47.range

	if slot_41_0 and slot_41_2 >= slot_41_0:distSqr(player.pos2D) then
		if ove_0_20.IsBehindWall(player.pos2D, slot_41_0, ove_0_38) then
			return
		end

		if slot_41_1 > #ove_0_20.count_minions_in_range(slot_41_0, ove_0_41.radius, TEAM_ENEMY) then
			return
		end

		player:castSpell("pos", 0, slot_41_0:to3D(player.pos.y))
	end
end

local function ove_0_106()
	-- function 42
	if not ove_0_21.farm.lane.lane_e:get() then
		return
	end

	if ove_0_20.GetPercentMana(player) <= ove_0_21.farm.lane.lane_e_mana:get() then
		return
	end

	if not ove_0_20.IsSpellSlotReady(_E, true) then
		return
	end

	if ove_0_22.core.is_active() then
		return
	end

	if ove_0_21.farm.lane.lane_e_double:get() and not ove_0_60() then
		return
	end

	if ove_0_21.farm.lane.lane_e_dis:get() and #ove_0_20.count_enemies_in_range(player.pos2D, 1700) >= 1 then
		return
	end

	if ove_0_21.farm.lane.lane_e_move_dis:get() and #ove_0_20.count_moving_minions_in_range(player.pos2D, 1500, TEAM_ENEMY) > ove_0_21.farm.lane.lane_e_move_allow_count:get() then
		return
	end

	if ove_0_21.farm.lane.lane_e_count_max:get() then
		local slot_42_0, slot_42_1 = ove_0_104(TEAM_ENEMY, ove_0_45, player)

		if slot_42_0 and slot_42_1 >= ove_0_21.farm.lane.lane_e_count:get() then
			if ove_0_20.IsBehindWall(player.pos2D, slot_42_0, ove_0_39) then
				return
			end

			player:castSpell("pos", _E, slot_42_0:to3D(player.pos.y))
		end
	else
		local slot_42_2, slot_42_3 = ove_0_103(TEAM_ENEMY, ove_0_45, player, 1500)

		if slot_42_2 and slot_42_3 >= ove_0_21.farm.lane.lane_e_count:get() then
			if ove_0_20.IsBehindWall(player.pos2D, slot_42_2, ove_0_39) then
				return
			end

			player:castSpell("pos", _E, slot_42_2:to3D(player.pos.y))
		end
	end
end

local function ove_0_107()
	-- function 43
	if not ove_0_21.farm.panic.panic_q:get() then
		return
	end

	if ove_0_20.GetPercentMana(player) <= ove_0_21.farm.panic.panic_q_mana:get() then
		return
	end

	if not ove_0_20.IsSpellSlotReady(_Q, true) then
		return
	end

	if ove_0_22.core.is_active() then
		return
	end

	if ove_0_21.farm.panic.panic_q_double:get() and not ove_0_60() then
		return
	end

	if ove_0_21.farm.panic.panic_q_dis:get() and #ove_0_20.count_enemies_in_range(player.pos2D, 1700) >= 1 then
		return
	end

	if ove_0_21.farm.panic.panic_q_move_dis:get() and #ove_0_20.count_moving_minions_in_range(player.pos2D, 1500, TEAM_ENEMY) > ove_0_21.farm.panic.panic_q_move_allow_count:get() then
		return
	end

	local slot_43_0 = ove_0_101(TEAM_ENEMY, ove_0_46, 1500)
	local slot_43_1 = ove_0_21.farm.panic.panic_q_count:get()
	local slot_43_2 = ove_0_47.range * ove_0_47.range

	if slot_43_0 and slot_43_2 >= slot_43_0:distSqr(player.pos2D) then
		if ove_0_20.IsBehindWall(player.pos2D, slot_43_0, ove_0_38) then
			return
		end

		if slot_43_1 > #ove_0_20.count_minions_in_range(slot_43_0, ove_0_41.radius, TEAM_ENEMY) then
			return
		end

		player:castSpell("pos", 0, slot_43_0:to3D(player.pos.y))
	end
end

local function ove_0_108()
	-- function 44
	if not ove_0_21.farm.panic.panic_e:get() then
		return
	end

	if ove_0_20.GetPercentMana(player) <= ove_0_21.farm.panic.panic_e_mana:get() then
		return
	end

	if not ove_0_20.IsSpellSlotReady(_E, true) then
		return
	end

	if ove_0_22.core.is_active() then
		return
	end

	if ove_0_21.farm.panic.panic_e_double:get() and not ove_0_60() then
		return
	end

	if ove_0_21.farm.panic.panic_e_dis:get() and #ove_0_20.count_enemies_in_range(player.pos2D, 1700) >= 1 then
		return
	end

	if ove_0_21.farm.panic.panic_e_move_dis:get() and #ove_0_20.count_moving_minions_in_range(player.pos2D, 1500, TEAM_ENEMY) > ove_0_21.farm.panic.panic_e_move_allow_count:get() then
		return
	end

	if ove_0_21.farm.panic.panic_e_count_max:get() then
		local slot_44_0, slot_44_1 = ove_0_104(TEAM_ENEMY, ove_0_45, player)

		if slot_44_0 and slot_44_1 >= ove_0_21.farm.panic.panic_e_count:get() then
			if ove_0_20.IsBehindWall(player.pos2D, slot_44_0, ove_0_39) then
				return
			end

			player:castSpell("pos", _E, slot_44_0:to3D(player.pos.y))
		end
	else
		local slot_44_2, slot_44_3 = ove_0_103(TEAM_ENEMY, ove_0_45, player, 1500)

		if slot_44_2 and slot_44_3 >= ove_0_21.farm.panic.panic_e_count:get() then
			if ove_0_20.IsBehindWall(player.pos2D, slot_44_2, ove_0_39) then
				return
			end

			player:castSpell("pos", _E, slot_44_2:to3D(player.pos.y))
		end
	end
end

local function ove_0_109()
	-- function 45
	if not ove_0_21.farm.jung.jung_q:get() then
		return
	end

	if ove_0_20.GetPercentMana(player) <= ove_0_21.farm.jung.jung_q_mana:get() then
		return
	end

	if not ove_0_20.IsSpellSlotReady(_Q, true) then
		return
	end

	if ove_0_22.core.is_active() then
		return
	end

	if ove_0_21.farm.jung.jung_q_double:get() and not ove_0_60() then
		return
	end

	if ove_0_21.farm.jung.jung_q_dis:get() and #ove_0_20.count_enemies_in_range(player.pos2D, 1700) >= 1 then
		return
	end

	local slot_45_0 = ove_0_101(TEAM_NEUTRAL, ove_0_44, 1300)
	local slot_45_1 = ove_0_47.range * ove_0_47.range

	if slot_45_0 and slot_45_1 >= slot_45_0:distSqr(player.pos2D) then
		if ove_0_20.IsBehindWall(player.pos2D, slot_45_0, ove_0_38) then
			return
		end

		player:castSpell("pos", 0, slot_45_0:to3D(player.pos.y))
	end
end

local function ove_0_110()
	-- function 46
	if not ove_0_21.farm.jung.jung_e:get() then
		return
	end

	if ove_0_20.GetPercentMana(player) <= ove_0_21.farm.jung.jung_e_mana:get() then
		return
	end

	if not ove_0_20.IsSpellSlotReady(_E, true) then
		return
	end

	if ove_0_22.core.is_active() then
		return
	end

	if ove_0_21.farm.jung.jung_e_double:get() and not ove_0_60() then
		return
	end

	if ove_0_21.farm.jung.jung_e_dis:get() and #ove_0_20.count_enemies_in_range(player.pos2D, 1700) >= 1 then
		return
	end

	if ove_0_21.farm.jung.jung_e_count_max:get() then
		local slot_46_0, slot_46_1 = ove_0_104(TEAM_NEUTRAL, ove_0_45, player)

		if slot_46_0 then
			if ove_0_20.IsBehindWall(player.pos2D, slot_46_0, ove_0_39) then
				return
			end

			player:castSpell("pos", _E, slot_46_0:to3D(player.pos.y))
		end
	else
		local slot_46_2, slot_46_3 = ove_0_103(TEAM_NEUTRAL, ove_0_45, player, 1500)

		if slot_46_2 then
			if ove_0_20.IsBehindWall(player.pos2D, slot_46_2, ove_0_39) then
				return
			end

			player:castSpell("pos", _E, slot_46_2:to3D(player.pos.y))
		end
	end
end

local function ove_0_111()
	-- function 47
	if not ove_0_21.farm.farm_key:get() then
		return
	end

	local slot_47_0 = ove_0_21.farm.panic.panic_method:get() == 1 and ove_0_20.IsOrbPanicClearActive() or ove_0_21.farm.panic.panic_method:get() == 2 and ove_0_21.keys.keys_laneclear:get() and ove_0_21.farm.panic.panic_custom_key:get()
	local slot_47_1 = ove_0_21.keys.keys_laneclear:get()
	local slot_47_2 = ove_0_21.farm.help.help_lane:get() and ove_0_21.keys.keys_laneclear:get() or ove_0_21.farm.help.help_last:get() and ove_0_21.keys.keys_lasthit:get()

	if slot_47_0 then
		ove_0_107()
		ove_0_108()
	else
		if slot_47_2 then
			ove_0_98()
			ove_0_100()
		end

		if slot_47_1 then
			ove_0_105()
			ove_0_106()
		end
	end

	if slot_47_1 then
		ove_0_109()
		ove_0_110()
	end
end

local function ove_0_112()
	-- function 48
	if not ove_0_21.misc.misc_gap_q:get() then
		return
	end

	if not ove_0_20.IsSpellSlotReady(_Q, true) then
		return
	end

	local slot_48_0 = 1488400

	for iter_48_0 = 0, objManager.enemies_n - 1 do
		local slot_48_1 = objManager.enemies[iter_48_0]

		if ove_0_20.IsValidTarget(slot_48_1) and slot_48_1.path.isDashing and ove_0_21.misc.misc_gap_blist[slot_48_1.charName] and not ove_0_21.misc.misc_gap_blist[slot_48_1.charName]:get() and slot_48_0 >= player.pos2D:distSqr(slot_48_1.path.point2D[slot_48_1.path.index]) then
			local slot_48_2 = ove_0_24.circular.get_prediction(ove_0_41, slot_48_1)

			if slot_48_2 and slot_48_0 >= slot_48_2.startPos:distSqr(slot_48_2.endPos) and ove_0_20.WillHitDasherCircle(slot_48_1, slot_48_2, ove_0_41) and not ove_0_20.IsBehindWall(slot_48_2.startPos, slot_48_2.endPos, ove_0_38) and not ove_0_22.core.is_active() then
				player:castSpell("pos", _Q, vec3(slot_48_2.endPos.x, slot_48_1.pos.y, slot_48_2.endPos.y))
			end
		end
	end
end

local function ove_0_113()
	-- function 49
	if not ove_0_21.misc.misc_gap_e:get() then
		return
	end

	if not ove_0_20.IsSpellSlotReady(_E, true) then
		return
	end

	local slot_49_0 = ove_0_49.range * ove_0_49.range

	for iter_49_0 = 0, objManager.enemies_n - 1 do
		local slot_49_1 = objManager.enemies[iter_49_0]

		if ove_0_20.IsValidTarget(slot_49_1) and slot_49_1.path.isDashing and ove_0_21.misc.misc_gap_blist[slot_49_1.charName] and not ove_0_21.misc.misc_gap_blist[slot_49_1.charName]:get() and slot_49_0 >= player.pos2D:distSqr(slot_49_1.path.point2D[slot_49_1.path.index]) then
			local slot_49_2 = ove_0_24.linear.get_prediction(ove_0_49, slot_49_1)

			if slot_49_2 and slot_49_0 >= slot_49_2.startPos:distSqr(slot_49_2.endPos) and not ove_0_24.collision.get_prediction(ove_0_49, slot_49_2, slot_49_1) and ove_0_20.WillHitDasherLinear(slot_49_1, slot_49_2, ove_0_42) and not ove_0_20.IsBehindWall(slot_49_2.startPos, slot_49_2.endPos, ove_0_39) and not ove_0_22.core.is_active() then
				player:castSpell("pos", _E, vec3(slot_49_2.endPos.x, slot_49_1.pos.y, slot_49_2.endPos.y))
			end
		end
	end
end

local function ove_0_114()
	-- function 50
	if not ove_0_21.misc.misc_zh_q:get() and not ove_0_21.misc.misc_zh_e:get() then
		return
	end

	local slot_50_0 = 1690000

	for iter_50_0 = 0, objManager.enemies_n - 1 do
		local slot_50_1 = objManager.enemies[iter_50_0]

		if slot_50_1 and not slot_50_1.isDead and slot_50_1.buff.zhonyasringshield and slot_50_0 >= slot_50_1.pos2D:distSqr(player.pos2D) then
			local slot_50_2 = slot_50_1.buff.zhonyasringshield

			if slot_50_2 then
				if ove_0_21.misc.misc_zh_e:get() and ove_0_49.slot.state == 0 then
					local slot_50_3 = slot_50_1.pos2D:dist(player.path.serverPos2D)
					local slot_50_4 = slot_50_3 / ove_0_49.speed + network.latency + 0.1

					if slot_50_4 >= slot_50_2.endTime - game.time and slot_50_2.endTime - game.time > slot_50_4 - 0.033 and slot_50_3 <= 1300 and not ove_0_20.IsBehindWall(player.path.serverPos2D, slot_50_1.pos2D, ove_0_39) then
						player:castSpell("pos", _E, slot_50_1.pos)
					end
				end

				if ove_0_21.misc.misc_zh_q:get() and ove_0_47.slot.state == 0 then
					local slot_50_5 = slot_50_1.pos2D:dist(player.path.serverPos2D)
					local slot_50_6 = ove_0_47.speed
					local slot_50_7 = slot_50_5 / slot_50_6 + network.latency + 0.1

					if slot_50_5 > 900 then
						local slot_50_8 = slot_50_5 - 900

						slot_50_7 = slot_50_5 / slot_50_6 + slot_50_8 / 874.946 + network.latency + 0.1
					end

					if slot_50_7 >= slot_50_2.endTime - game.time and slot_50_2.endTime - game.time > slot_50_7 - 0.033 and slot_50_5 <= 1220 and not ove_0_20.IsBehindWall(player.path.serverPos2D, slot_50_1.pos2D, ove_0_38) then
						player:castSpell("pos", _Q, slot_50_1.pos)
					end
				end
			end
		end
	end
end

local ove_0_115 = 0.25
local ove_0_116 = ove_0_115 * 2
local ove_0_117 = 0.638595410459617
local ove_0_118 = 1.1857566066934417
local ove_0_119 = 0.661453964016065
local ove_0_120 = ove_0_119 * 2
local ove_0_121 = 0.638595410459617
local ove_0_122 = ove_0_121 * 2

local function ove_0_123()
	-- function 51
	if ove_0_60() then
		ove_0_47.delay = ove_0_118
		ove_0_49.delay = ove_0_116
		ove_0_45.delay = ove_0_116
		ove_0_44.delay = ove_0_122
		ove_0_46.delay = ove_0_120
	else
		ove_0_47.delay = ove_0_117
		ove_0_49.delay = ove_0_115
		ove_0_45.delay = ove_0_115
		ove_0_44.delay = ove_0_121
		ove_0_46.delay = ove_0_119
	end
end

local function ove_0_124()
	-- function 52
	if ove_0_20.IsOrbAttackPaused() then
		ove_0_26.core.set_pause_attack(0.1)
	end

	ove_0_123()

	if not player.isDead then
		ove_0_81()
		ove_0_78()
		ove_0_79()

		if not ove_0_20.IsRecalling(player) then
			ove_0_88()
			ove_0_87()
			ove_0_114()
			ove_0_84()
			ove_0_83()
			ove_0_112()
			ove_0_113()
		end

		ove_0_111()
	end

	ove_0_61 = ove_0_20.GetFlashSlot()

	if menu:isopen() then
		ove_0_20.MenuHeaderWizard(ove_0_21)
	end
end

local ove_0_125 = {}
local ove_0_126 = {}
local ove_0_127 = {}

local function ove_0_128(arg_53_0)
	-- function 53
	local slot_53_0 = arg_53_0.spell.owner

	if slot_53_0 and slot_53_0 == player and arg_53_0.name == "SeraphineQSecondaryMissile" then
		ove_0_125[arg_53_0.ptr] = arg_53_0
		ove_0_126[arg_53_0.ptr] = game.time
		ove_0_127[arg_53_0.ptr] = ove_0_27.PastelRGB(math.random(0.1, 1))
	end
end

local function ove_0_129(arg_54_0)
	-- function 54
	ove_0_125[arg_54_0.ptr] = nil
	ove_0_126[arg_54_0.ptr] = nil
	ove_0_127[arg_54_0.ptr] = nil
end

local function ove_0_130()
	-- function 55
	if not ove_0_21.draww.draww_q_splash:get() then
		return
	end

	for iter_55_0, iter_55_1 in pairs(ove_0_125) do
		if iter_55_1 and iter_55_1.ptr ~= 0 and ove_0_126[iter_55_1.ptr] and ove_0_127[iter_55_1.ptr] then
			local function slot_55_0()
				-- function 56
				local slot_56_0 = game.time - ove_0_126[iter_55_1.ptr]
				local slot_56_1 = 0 + slot_56_0 * 874.946

				if slot_56_1 > 340 then
					return 340
				end

				return slot_56_1
			end

			graphics.draw_circle(iter_55_1.startPos, slot_55_0(), 5.1, ove_0_127[iter_55_1.ptr], 96)
		end
	end
end

local ove_0_131 = {}
local ove_0_132 = 0
local ove_0_133 = {}
local ove_0_134 = 0
local ove_0_135 = {}

local function ove_0_136(arg_57_0)
	-- function 57
	if arg_57_0.name:find("global_ss_teleport_target_red") then
		ove_0_131[arg_57_0.ptr] = arg_57_0
		ove_0_135[arg_57_0.ptr] = game.time
		ove_0_132 = ove_0_132 + 1
	end

	if arg_57_0.name:find("global_ss_teleport_turret_red") then
		ove_0_133[arg_57_0.ptr] = arg_57_0
		ove_0_135[arg_57_0.ptr] = game.time
		ove_0_134 = ove_0_134 + 1
	end
end

local function ove_0_137(arg_58_0)
	-- function 58
	if ove_0_132 > 0 then
		for iter_58_0, iter_58_1 in pairs(ove_0_131) do
			if arg_58_0.ptr == iter_58_1.ptr then
				ove_0_131[arg_58_0.ptr] = nil
				ove_0_135[arg_58_0.ptr] = nil
				ove_0_132 = ove_0_132 - 1
			end
		end
	end

	if ove_0_134 > 0 then
		for iter_58_2, iter_58_3 in pairs(ove_0_133) do
			if arg_58_0.ptr == iter_58_3.ptr then
				ove_0_133[arg_58_0.ptr] = nil
				ove_0_135[arg_58_0.ptr] = nil
				ove_0_134 = ove_0_134 - 1
			end
		end
	end
end

local ove_0_138 = objManager.nexus[TEAM_ENEMY]

local function ove_0_139()
	-- function 59
	if ove_0_132 > 0 then
		for iter_59_0, iter_59_1 in pairs(ove_0_131) do
			local slot_59_0 = ove_0_20.VectorExtend(iter_59_1.pos2D, ove_0_138.pos2D, 100)
			local slot_59_1 = slot_59_0:to3D(navmesh.getTerrainHeight(slot_59_0.x, slot_59_0.y))
			local slot_59_2 = ove_0_135[iter_59_1.ptr]

			if slot_59_2 then
				local slot_59_3 = slot_59_2 + 4

				if ove_0_21.misc.misc_tp_e:get() and ove_0_49.slot.state == 0 then
					local slot_59_4 = slot_59_0:dist(player.path.serverPos2D)
					local slot_59_5 = slot_59_4 / ove_0_49.speed + network.latency + 0.1

					if slot_59_5 >= slot_59_3 - game.time and slot_59_3 - game.time > slot_59_5 - 0.033 and slot_59_4 <= 1300 and not ove_0_20.IsBehindWall(player.path.serverPos2D, slot_59_0, ove_0_39) then
						player:castSpell("pos", _E, slot_59_1)
					end
				end

				if ove_0_21.misc.misc_tp_q:get() and ove_0_47.slot.state == 0 then
					local slot_59_6 = slot_59_0:dist(player.path.serverPos2D)
					local slot_59_7 = ove_0_47.speed
					local slot_59_8 = slot_59_6 / slot_59_7 + network.latency + 0.1

					if slot_59_6 > 900 then
						local slot_59_9 = slot_59_6 - 900

						slot_59_8 = slot_59_6 / slot_59_7 + slot_59_9 / 874.946 + network.latency + 0.1
					end

					if slot_59_8 >= slot_59_3 - game.time and slot_59_3 - game.time > slot_59_8 - 0.033 and slot_59_6 <= 1220 and not ove_0_20.IsBehindWall(player.path.serverPos2D, slot_59_0, ove_0_38) then
						player:castSpell("pos", _Q, slot_59_1)
					end
				end

				if ove_0_21.draww.tp.tp_enable:get() then
					local slot_59_10 = slot_59_3 - game.time

					if ove_0_21.draww.tp.tp_world:get() and ove_0_20.IsOnScreen(slot_59_1) then
						graphics.draw_circle(slot_59_1, 15, 3, ove_0_21.draww.tp.tp_world_color:get(), 4)
						graphics.draw_circle(slot_59_1, ove_0_27.SorakaPulseAnimator2(100, 30), 2, ove_0_21.draww.tp.tp_world_color:get(), 96)
						ove_0_27.SorakaRecallGroundDraw(slot_59_1, slot_59_10, ove_0_21.draww.tp.tp_world_color:get(), COLOR_WHITE)
					end

					if ove_0_21.draww.tp.tp_arrow:get() then
						if ove_0_21.draww.tp.tp_arrow_nearby:get() then
							if player.pos2D:distSqr(slot_59_0) <= 9000000 then
								ove_0_27.SorakaArrowDraw2D(player.pos2D, slot_59_0, 300, ove_0_21.draww.tp.tp_arrow_color:get(), COLOR_WHITE, 3, slot_59_10)
							end
						else
							ove_0_27.SorakaArrowDraw2D(player.pos2D, slot_59_0, 300, ove_0_21.draww.tp.tp_arrow_color:get(), COLOR_WHITE, 3, slot_59_10)
						end
					end

					if ove_0_21.draww.tp.tp_minimap:get() then
						minimap.draw_circle(slot_59_1, 200, 2, ove_0_21.draww.tp.tp_minimap_color:get(), 4)
						minimap.draw_circle(slot_59_1, ove_0_27.SorakaPulseAnimator(800, 200), 2, ove_0_21.draww.tp.tp_minimap_color:get(), 96)
					end
				end
			end
		end
	end

	if ove_0_134 > 0 then
		for iter_59_2, iter_59_3 in pairs(ove_0_133) do
			local slot_59_11 = ove_0_20.VectorExtend(iter_59_3.pos2D, ove_0_138.pos2D, 225)
			local slot_59_12 = slot_59_11:to3D(navmesh.getTerrainHeight(slot_59_11.x, slot_59_11.y))
			local slot_59_13 = ove_0_135[iter_59_3.ptr]

			if slot_59_13 then
				local slot_59_14 = slot_59_13 + 4

				if ove_0_21.misc.misc_tp_e:get() and ove_0_49.slot.state == 0 then
					local slot_59_15 = slot_59_11:dist(player.path.serverPos2D)
					local slot_59_16 = slot_59_15 / ove_0_49.speed + network.latency

					if slot_59_16 >= slot_59_14 - game.time and slot_59_14 - game.time > slot_59_16 - 0.033 and slot_59_15 <= 1300 and not ove_0_20.IsBehindWall(player.path.serverPos2D, slot_59_11, ove_0_39) then
						player:castSpell("pos", _E, slot_59_12)
					end
				end

				if ove_0_21.misc.misc_tp_q:get() and ove_0_47.slot.state == 0 then
					local slot_59_17 = slot_59_11:dist(player.path.serverPos2D)
					local slot_59_18 = ove_0_47.speed
					local slot_59_19 = slot_59_17 / slot_59_18 + network.latency

					if slot_59_17 > 900 then
						local slot_59_20 = slot_59_17 - 900

						slot_59_19 = slot_59_17 / slot_59_18 + slot_59_20 / 874.946 + network.latency
					end

					if slot_59_19 >= slot_59_14 - game.time and slot_59_14 - game.time > slot_59_19 - 0.033 and slot_59_17 <= 1220 and not ove_0_20.IsBehindWall(player.path.serverPos2D, slot_59_11, ove_0_38) then
						player:castSpell("pos", _Q, slot_59_12)
					end
				end

				if ove_0_21.draww.tp.tp_enable:get() then
					local slot_59_21 = slot_59_14 - game.time

					if ove_0_21.draww.tp.tp_world:get() and ove_0_20.IsOnScreen(slot_59_12) then
						graphics.draw_circle(slot_59_12, 15, 3, ove_0_21.draww.tp.tp_world_color:get(), 4)
						graphics.draw_circle(slot_59_12, ove_0_27.SorakaPulseAnimator2(100, 30), 2, ove_0_21.draww.tp.tp_world_color:get(), 96)
						ove_0_27.SorakaRecallGroundDraw(slot_59_12, slot_59_21, ove_0_21.draww.tp.tp_world_color:get(), COLOR_WHITE)
					end

					if ove_0_21.draww.tp.tp_arrow:get() then
						if ove_0_21.draww.tp.tp_arrow_nearby:get() then
							if player.pos2D:distSqr(slot_59_11) <= 9000000 then
								ove_0_27.SorakaArrowDraw2D(player.pos2D, slot_59_11, 300, ove_0_21.draww.tp.tp_arrow_color:get(), COLOR_WHITE, 3, slot_59_21)
							end
						else
							ove_0_27.SorakaArrowDraw2D(player.pos2D, slot_59_11, 300, ove_0_21.draww.tp.tp_arrow_color:get(), COLOR_WHITE, 3, slot_59_21)
						end
					end

					if ove_0_21.draww.tp.tp_minimap:get() then
						minimap.draw_circle(slot_59_12, 200, 2, ove_0_21.draww.tp.tp_minimap_color:get(), 4)
						minimap.draw_circle(slot_59_12, ove_0_27.SorakaPulseAnimator(800, 200), 2, ove_0_21.draww.tp.tp_minimap_color:get(), 96)
					end
				end
			end
		end
	end
end

local function ove_0_140()
	-- function 60
	if not ove_0_29.enable() then
		return
	end

	local slot_60_0 = {
		{
			position = 0.35,
			name = "Shielding:",
			menu = ove_0_21.wset.wset_key
		},
		{
			position = 0.1,
			name = "Farming:",
			menu = ove_0_21.farm.farm_key
		},
		{
			position = -0.15,
			name = "Auto:",
			menu = ove_0_21.auto.auto_key
		},
		{
			position = -0.4,
			name = "Force R:",
			menu = ove_0_21.rset.rset_force_key
		}
	}

	ove_0_29.draw(slot_60_0)
end

local function ove_0_141()
	-- function 61
	if player.isOnScreen and ove_0_21.draww.draww_square:get() then
		graphics.draw_circle(player.pos, 100, 2, ove_0_27.PastelRGB(5), 4)
	end

	if ove_0_21.draww.draww_q:get() then
		ove_0_27.ShaderCircle(player.pos, ove_0_41.draw_range, 10, ove_0_21.draww.draww_q_color)
	end

	if ove_0_21.draww.draww_w:get() then
		ove_0_27.ShaderCircle(player.pos, ove_0_48.range, 10, ove_0_21.draww.draww_w_color)
	end

	if ove_0_21.draww.draww_e:get() then
		ove_0_27.ShaderCircle(player.pos, ove_0_42.draw_range, 10, ove_0_21.draww.draww_e_color)
	end

	ove_0_130()
	ove_0_139()
	ove_0_140()

	if ove_0_21.farm.panic.panic_method:get() == 2 and ove_0_21.keys.keys_laneclear:get() and ove_0_21.farm.panic.panic_custom_key:get() then
		local slot_61_0, slot_61_1 = graphics.text_area("Panic Clear", 15)
		local slot_61_2 = slot_61_0 / 2
		local slot_61_3 = slot_61_1 / 2

		graphics.draw_text_2D("Panic Clear", 15, game.cursorPos.x - slot_61_2, game.cursorPos.y - slot_61_3, COLOR_WHITE)
	end
end

cb.add(cb.create_missile, ove_0_128)
cb.add(cb.delete_missile, ove_0_129)
cb.add(cb.create_particle, ove_0_136)
cb.add(cb.delete_particle, ove_0_137)
ove_0_26.combat.register_f_pre_tick(ove_0_124)
cb.add(cb.spell, ove_0_65)
cb.add(cb.draw, ove_0_141)
ove_0_20.Successfully_Loaded(true)
